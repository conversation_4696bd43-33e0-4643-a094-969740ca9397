const express = require('express');
const { validateRequest } = require('./validation');
const controllers = require('./controllers');

const router = express.Router();

/**
 * @swagger
 * /governance/esg/metrics:
 *   get:
 *     summary: Get a list of ESG metrics
 *     description: Returns a paginated list of ESG metrics with optional filtering
 *     tags: [ESG Metrics]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - $ref: '#/components/parameters/page'
 *       - $ref: '#/components/parameters/limit'
 *       - $ref: '#/components/parameters/sortBy'
 *       - $ref: '#/components/parameters/sortOrder'
 *       - name: category
 *         in: query
 *         description: Filter by metric category
 *         schema:
 *           type: string
 *           enum: [environmental, social, governance]
 *       - name: subcategory
 *         in: query
 *         description: Filter by metric subcategory
 *         schema:
 *           type: string
 *       - name: framework
 *         in: query
 *         description: Filter by framework (matches both legacy framework field and frameworkMappings)
 *         schema:
 *           type: string
 *       - name: standardId
 *         in: query
 *         description: Filter by standard identifier (e.g., GRI 305-1)
 *         schema:
 *           type: string
 *       - name: isStandardized
 *         in: query
 *         description: Filter by whether the metric is standardized
 *         schema:
 *           type: string
 *           enum: [true, false]
 *       - name: verificationRequired
 *         in: query
 *         description: Filter by whether verification is required
 *         schema:
 *           type: string
 *           enum: [true, false]
 *       - name: dataCollectionFrequency
 *         in: query
 *         description: Filter by data collection frequency
 *         schema:
 *           type: string
 *           enum: [daily, weekly, monthly, quarterly, annually, custom]
 *       - name: owner
 *         in: query
 *         description: Filter by metric owner
 *         schema:
 *           type: string
 *       - name: status
 *         in: query
 *         description: Filter by status
 *         schema:
 *           type: string
 *           enum: [active, inactive, archived]
 *       - name: tag
 *         in: query
 *         description: Filter by tag
 *         schema:
 *           type: string
 *       - name: targetDateBefore
 *         in: query
 *         description: Filter by target date before (format YYYY-MM-DD)
 *         schema:
 *           type: string
 *           format: date
 *       - name: targetDateAfter
 *         in: query
 *         description: Filter by target date after (format YYYY-MM-DD)
 *         schema:
 *           type: string
 *           format: date
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/ESGMetric'
 *                 pagination:
 *                   $ref: '#/components/schemas/Pagination'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/', controllers.getMetrics);

/**
 * @swagger
 * /governance/esg/metrics/{id}:
 *   get:
 *     summary: Get a specific ESG metric
 *     description: Returns a specific ESG metric by ID
 *     tags: [ESG Metrics]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: ESG metric ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/ESGMetric'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/:id', controllers.getMetricById);

/**
 * @swagger
 * /governance/esg/metrics:
 *   post:
 *     summary: Create a new ESG metric
 *     description: Creates a new ESG metric
 *     tags: [ESG Metrics]
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               category:
 *                 type: string
 *                 enum: [environmental, social, governance]
 *               subcategory:
 *                 type: string
 *               unit:
 *                 type: string
 *               dataType:
 *                 type: string
 *                 enum: [numeric, percentage, boolean, text, date]
 *               isStandardized:
 *                 type: boolean
 *                 description: Whether this is a standardized industry metric or custom
 *               standardId:
 *                 type: string
 *                 description: Identifier in a standard framework (e.g., GRI 305-1)
 *               frameworkMappings:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     frameworkId:
 *                       type: string
 *                       description: ID of the framework
 *                     elementId:
 *                       type: string
 *                       description: ID of the framework element
 *                     version:
 *                       type: string
 *                       description: Version of the framework
 *               calculationMethod:
 *                 type: string
 *                 description: Method used to calculate the metric
 *               calculationFormula:
 *                 type: string
 *                 description: Formula used to calculate the metric
 *               dataCollectionFrequency:
 *                 type: string
 *                 enum: [daily, weekly, monthly, quarterly, annually, custom]
 *                 description: Frequency of data collection
 *               verificationRequired:
 *                 type: boolean
 *                 description: Whether verification is required for data points
 *               verificationProcess:
 *                 type: string
 *                 description: Description of the verification process
 *               benchmarkValue:
 *                 type: string
 *                 description: Industry benchmark value for comparison
 *               benchmarkSource:
 *                 type: string
 *                 description: Source of the benchmark value
 *               framework:
 *                 type: string
 *                 description: Legacy field for backward compatibility
 *               targetValue:
 *                 type: string
 *               targetDate:
 *                 type: string
 *                 format: date
 *               owner:
 *                 type: string
 *               status:
 *                 type: string
 *                 enum: [active, inactive, archived]
 *               tags:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Tags for categorizing and searching metrics
 *             required:
 *               - name
 *               - category
 *               - dataType
 *               - status
 *     responses:
 *       201:
 *         description: Metric created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/ESGMetric'
 *                 message:
 *                   type: string
 *                   example: ESG metric created successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/', validateRequest('createMetric'), controllers.createMetric);

/**
 * @swagger
 * /governance/esg/metrics/{id}:
 *   put:
 *     summary: Update an ESG metric
 *     description: Updates an existing ESG metric
 *     tags: [ESG Metrics]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: ESG metric ID
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               category:
 *                 type: string
 *                 enum: [environmental, social, governance]
 *               subcategory:
 *                 type: string
 *               unit:
 *                 type: string
 *               dataType:
 *                 type: string
 *                 enum: [numeric, percentage, boolean, text, date]
 *               isStandardized:
 *                 type: boolean
 *                 description: Whether this is a standardized industry metric or custom
 *               standardId:
 *                 type: string
 *                 description: Identifier in a standard framework (e.g., GRI 305-1)
 *               frameworkMappings:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     frameworkId:
 *                       type: string
 *                       description: ID of the framework
 *                     elementId:
 *                       type: string
 *                       description: ID of the framework element
 *                     version:
 *                       type: string
 *                       description: Version of the framework
 *               calculationMethod:
 *                 type: string
 *                 description: Method used to calculate the metric
 *               calculationFormula:
 *                 type: string
 *                 description: Formula used to calculate the metric
 *               dataCollectionFrequency:
 *                 type: string
 *                 enum: [daily, weekly, monthly, quarterly, annually, custom]
 *                 description: Frequency of data collection
 *               verificationRequired:
 *                 type: boolean
 *                 description: Whether verification is required for data points
 *               verificationProcess:
 *                 type: string
 *                 description: Description of the verification process
 *               benchmarkValue:
 *                 type: string
 *                 description: Industry benchmark value for comparison
 *               benchmarkSource:
 *                 type: string
 *                 description: Source of the benchmark value
 *               framework:
 *                 type: string
 *                 description: Legacy field for backward compatibility
 *               targetValue:
 *                 type: string
 *               targetDate:
 *                 type: string
 *                 format: date
 *               owner:
 *                 type: string
 *               status:
 *                 type: string
 *                 enum: [active, inactive, archived]
 *               tags:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Tags for categorizing and searching metrics
 *     responses:
 *       200:
 *         description: Metric updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/ESGMetric'
 *                 message:
 *                   type: string
 *                   example: ESG metric updated successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.put('/:id', validateRequest('updateMetric'), controllers.updateMetric);

/**
 * @swagger
 * /governance/esg/metrics/{id}/benchmark:
 *   get:
 *     summary: Get benchmark data for an ESG metric
 *     description: Returns benchmark data for a specific ESG metric including industry averages, peer comparison, and historical trends
 *     tags: [ESG Metrics]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: ESG metric ID
 *         required: true
 *         schema:
 *           type: string
 *       - name: industry
 *         in: query
 *         description: Filter benchmark data by industry
 *         schema:
 *           type: string
 *       - name: region
 *         in: query
 *         description: Filter benchmark data by region
 *         schema:
 *           type: string
 *       - name: companySize
 *         in: query
 *         description: Filter benchmark data by company size
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: object
 *                   properties:
 *                     metric:
 *                       type: object
 *                       properties:
 *                         id:
 *                           type: string
 *                         name:
 *                           type: string
 *                         category:
 *                           type: string
 *                         unit:
 *                           type: string
 *                     currentValue:
 *                       type: string
 *                     industryAverage:
 *                       type: object
 *                       properties:
 *                         value:
 *                           type: string
 *                         source:
 *                           type: string
 *                         date:
 *                           type: string
 *                     peerComparison:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           segment:
 *                             type: string
 *                           value:
 *                             type: string
 *                           percentile:
 *                             type: number
 *                     historicalTrend:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           year:
 *                             type: number
 *                           value:
 *                             type: string
 *                     filters:
 *                       type: object
 *                       properties:
 *                         industry:
 *                           type: string
 *                         region:
 *                           type: string
 *                         companySize:
 *                           type: string
 *                     recommendations:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           description:
 *                             type: string
 *                           potentialImprovement:
 *                             type: string
 *                           difficulty:
 *                             type: string
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/:id/benchmark', controllers.getMetricBenchmark);

/**
 * @swagger
 * /governance/esg/metrics/{id}:
 *   delete:
 *     summary: Delete an ESG metric
 *     description: Deletes an existing ESG metric
 *     tags: [ESG Metrics]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: ESG metric ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Metric deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: ESG metric deleted successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.delete('/:id', controllers.deleteMetric);

/**
 * @swagger
 * /governance/esg/metrics/{id}/data-points:
 *   get:
 *     summary: Get data points for a metric
 *     description: Returns data points for a specific ESG metric
 *     tags: [ESG Metrics]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: ESG metric ID
 *         required: true
 *         schema:
 *           type: string
 *       - name: startDate
 *         in: query
 *         description: Filter by start date (format YYYY-MM-DD)
 *         schema:
 *           type: string
 *           format: date
 *       - name: endDate
 *         in: query
 *         description: Filter by end date (format YYYY-MM-DD)
 *         schema:
 *           type: string
 *           format: date
 *       - name: period
 *         in: query
 *         description: Filter by period
 *         schema:
 *           type: string
 *           enum: [daily, weekly, monthly, quarterly, annually]
 *       - name: verificationStatus
 *         in: query
 *         description: Filter by verification status
 *         schema:
 *           type: string
 *           enum: [unverified, verified, rejected]
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/ESGDataPoint'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/:id/data-points', controllers.getMetricDataPoints);

/**
 * @swagger
 * /governance/esg/metrics/{id}/data-points:
 *   post:
 *     summary: Add a data point to a metric
 *     description: Adds a new data point to a specific ESG metric
 *     tags: [ESG Metrics]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: ESG metric ID
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               value:
 *                 type: string
 *               date:
 *                 type: string
 *                 format: date
 *               period:
 *                 type: string
 *                 enum: [daily, weekly, monthly, quarterly, annually]
 *               source:
 *                 type: string
 *               notes:
 *                 type: string
 *               verificationStatus:
 *                 type: string
 *                 enum: [unverified, verified, rejected]
 *               verifiedBy:
 *                 type: string
 *               verifiedAt:
 *                 type: string
 *                 format: date-time
 *             required:
 *               - value
 *               - date
 *               - period
 *               - verificationStatus
 *     responses:
 *       201:
 *         description: Data point added successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/ESGDataPoint'
 *                 message:
 *                   type: string
 *                   example: Data point added successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/:id/data-points', validateRequest('createDataPoint'), controllers.addDataPoint);

/**
 * @swagger
 * /governance/esg/initiatives:
 *   get:
 *     summary: Get a list of ESG initiatives
 *     description: Returns a paginated list of ESG initiatives with optional filtering
 *     tags: [ESG Metrics]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - $ref: '#/components/parameters/page'
 *       - $ref: '#/components/parameters/limit'
 *       - $ref: '#/components/parameters/sortBy'
 *       - $ref: '#/components/parameters/sortOrder'
 *       - name: category
 *         in: query
 *         description: Filter by initiative category
 *         schema:
 *           type: string
 *           enum: [environmental, social, governance]
 *       - name: status
 *         in: query
 *         description: Filter by status
 *         schema:
 *           type: string
 *           enum: [planned, in-progress, completed, cancelled]
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/ESGInitiative'
 *                 pagination:
 *                   $ref: '#/components/schemas/Pagination'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/initiatives', controllers.getInitiatives);

/**
 * @swagger
 * /governance/esg/initiatives/{id}:
 *   get:
 *     summary: Get a specific ESG initiative
 *     description: Returns a specific ESG initiative by ID
 *     tags: [ESG Metrics]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: ESG initiative ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/ESGInitiative'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/initiatives/:id', controllers.getInitiativeById);

/**
 * @swagger
 * /governance/esg/initiatives:
 *   post:
 *     summary: Create a new ESG initiative
 *     description: Creates a new ESG initiative
 *     tags: [ESG Metrics]
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               category:
 *                 type: string
 *                 enum: [environmental, social, governance]
 *               startDate:
 *                 type: string
 *                 format: date
 *               endDate:
 *                 type: string
 *                 format: date
 *               status:
 *                 type: string
 *                 enum: [planned, in-progress, completed, cancelled]
 *               owner:
 *                 type: string
 *               budget:
 *                 type: number
 *               metrics:
 *                 type: array
 *                 items:
 *                   type: string
 *               goals:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     description:
 *                       type: string
 *                     targetDate:
 *                       type: string
 *                       format: date
 *                     status:
 *                       type: string
 *                       enum: [not-started, in-progress, completed, cancelled]
 *             required:
 *               - name
 *               - category
 *               - status
 *     responses:
 *       201:
 *         description: Initiative created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/ESGInitiative'
 *                 message:
 *                   type: string
 *                   example: ESG initiative created successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/initiatives', validateRequest('createInitiative'), controllers.createInitiative);

/**
 * @swagger
 * /governance/esg/initiatives/{id}:
 *   put:
 *     summary: Update an ESG initiative
 *     description: Updates an existing ESG initiative
 *     tags: [ESG Metrics]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: ESG initiative ID
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               category:
 *                 type: string
 *                 enum: [environmental, social, governance]
 *               startDate:
 *                 type: string
 *                 format: date
 *               endDate:
 *                 type: string
 *                 format: date
 *               status:
 *                 type: string
 *                 enum: [planned, in-progress, completed, cancelled]
 *               owner:
 *                 type: string
 *               budget:
 *                 type: number
 *               metrics:
 *                 type: array
 *                 items:
 *                   type: string
 *               goals:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     description:
 *                       type: string
 *                     targetDate:
 *                       type: string
 *                       format: date
 *                     status:
 *                       type: string
 *                       enum: [not-started, in-progress, completed, cancelled]
 *     responses:
 *       200:
 *         description: Initiative updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/ESGInitiative'
 *                 message:
 *                   type: string
 *                   example: ESG initiative updated successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.put('/initiatives/:id', validateRequest('updateInitiative'), controllers.updateInitiative);

/**
 * @swagger
 * /governance/esg/initiatives/{id}:
 *   delete:
 *     summary: Delete an ESG initiative
 *     description: Deletes an existing ESG initiative
 *     tags: [ESG Metrics]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: ESG initiative ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Initiative deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: ESG initiative deleted successfully
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.delete('/initiatives/:id', controllers.deleteInitiative);

/**
 * @swagger
 * /governance/esg/categories:
 *   get:
 *     summary: Get ESG categories
 *     description: Returns a list of ESG categories
 *     tags: [ESG Metrics]
 *     security:
 *       - ApiKeyAuth: []
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       name:
 *                         type: string
 *                       description:
 *                         type: string
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/categories', controllers.getCategories);

/**
 * @swagger
 * /governance/esg/frameworks:
 *   get:
 *     summary: Get ESG frameworks
 *     description: Returns a list of ESG frameworks
 *     tags: [ESG Metrics]
 *     security:
 *       - ApiKeyAuth: []
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       name:
 *                         type: string
 *                       description:
 *                         type: string
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/frameworks', controllers.getFrameworks);

module.exports = router;
