const express = require('express');
const crypto = require('crypto');
const BN = require('bn.js');
const promClient = require('express-prometheus-middleware');
const os = require('os');
const { v4: uuidv4 } = require('uuid');
const { getAddress, verify, generatePrivate<PERSON><PERSON>, getPublicKey } = require('./crypto');
const Transaction = require('./transaction');
const TransactionPool = require('./transaction-pool');
const CrownConsensus = require('./crown-consensus');

// Load environment variables and config
require('dotenv').config();
const fs = require('fs');
const path = require('path');

// Load config file if provided
let config = {};
if (process.argv.includes('--config')) {
  const configPath = process.argv[process.argv.indexOf('--config') + 1];
  try {
    config = require(path.resolve(configPath));
    console.log(`✅ Loaded config from ${configPath}`);
  } catch (err) {
    console.error(`❌ Failed to load config from ${configPath}:`, err.message);
    process.exit(1);
  }
}

// Configuration with defaults
const NODE_ID = process.env.NODE_ID || 1;
const PORT = process.env.PORT || 8080;
const RPC_PORT = process.env.RPC_PORT || 8545;
const WS_PORT = process.env.WS_PORT || 8546;
const PRIVATE_KEY = process.env.PRIVATE_KEY || generatePrivateKey();
const PUBLIC_KEY = getPublicKey(PRIVATE_KEY);
const NODE_ADDRESS = getAddress(PUBLIC_KEY);
const VALIDATOR_ADDRESS = process.env.VALIDATOR_ADDRESS || NODE_ADDRESS;
const PEERS = (process.env.PEERS || '').split(',').filter(Boolean);
const HOST = process.env.HOST || '0.0.0.0';
const NODE_ENV = process.env.NODE_ENV || 'development';

// Merge config with environment variables
const nodeConfig = {
  ...config.node,
  id: NODE_ID,
  port: PORT,
  rpcPort: RPC_PORT,
  wsPort: WS_PORT,
  privateKey: PRIVATE_KEY,
  publicKey: PUBLIC_KEY,
  address: VALIDATOR_ADDRESS,
  peers: PEERS.length ? PEERS : (config.node?.peers || [])
};

// Log node configuration
console.log('🔧 Node Configuration:');
console.log(`- ID: ${nodeConfig.id}`);
console.log(`- Address: ${nodeConfig.address}`);
console.log(`- Port: ${nodeConfig.port}`);
console.log(`- RPC Port: ${nodeConfig.rpcPort}`);
console.log(`- WS Port: ${nodeConfig.wsPort}`);
console.log(`- Peers: ${nodeConfig.peers.length ? nodeConfig.peers.join(', ') : 'None'}`);
console.log('');

// Initialize Express app
const app = express();
const bodyParser = require('body-parser');
app.use(bodyParser.json());

// Initialize Crown Consensus
const consensus = new CrownConsensus({
  minStake: new BN('1**********00000000'), // 1 COH
  epochLength: 100,
  blockTime: 2
});

// Initialize with genesis validators (temporary - will be replaced with config)
const GENESIS_VALIDATORS = [
  {
    address: VALIDATOR_ADDRESS,
    stake: '1********************0' // 1000 COH for genesis validator
  }
];

// Track blockchain state
let currentBlockNumber = 0;
let currentBlock = null;
let isMining = false;
const serverStartTime = Date.now();

// Add prometheus metrics middleware
app.use(promClient({
  metricsPath: '/metrics',
  collectDefaultMetrics: true,
  requestDurationBuckets: [0.1, 0.5, 1, 1.5],
  defaultLabels: { service: 'kethernet-core' }
}));

// Initialize transaction pool
const transactionPool = new TransactionPool();

// In-memory stores
const accounts = new Map(); // address -> { balance, nonce, code, storage }
const blocks = []; // Array of mined blocks
let coheriumSupply = new BN('0');
let lastBlockHash = '0x0';

// Initialize genesis account with some balance
const GENESIS_ACCOUNT = {
  balance: new BN('1********************0000'), // 1M AE (18 decimals)
  nonce: '0',
  code: '0x',
  storage: new Map()
};

// API rate limiting
const rateLimit = require('express-rate-limit');
const apiLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.'
});

/**
 * Initialize the blockchain with genesis state
 */
async function initializeBlockchain() {
  try {
    // Initialize consensus engine
    await consensus.initialize(GENESIS_VALIDATORS);
    console.log('👑 Crown Consensus initialized');
    
    // Initialize genesis account
    const genesisAddress = '0x****************************************';
    accounts.set(genesisAddress.toLowerCase(), { ...GENESIS_ACCOUNT });
    
    // Initialize validator account if provided
    if (VALIDATOR_ADDRESS && VALIDATOR_ADDRESS !== '0x' + '0'.repeat(40)) {
      accounts.set(VALIDATOR_ADDRESS.toLowerCase(), {
        balance: new BN('0'),
        nonce: '0',
        code: '0x',
        storage: new Map()
      });
    }
    
    coheriumSupply = new BN('1000000');
    lastBlockHash = crypto.createHash('sha256').update('genesis').digest('hex');
    
    // Create genesis block
    currentBlock = {
      number: 0,
      timestamp: Math.floor(Date.now() / 1000),
      transactions: [],
      parentHash: '0x0',
      hash: lastBlockHash,
      miner: VALIDATOR_ADDRESS || '0x' + '0'.repeat(40),
      difficulty: 1,
      nonce: 0,
      stateRoot: '0x0',
      transactionsRoot: '0x0',
      receiptsRoot: '0x0',
      gasUsed: '0',
      gasLimit: '********',
      extraData: '0x',
      mixHash: '0x0',
      logsBloom: '0x' + '0'.repeat(512)
    };
    
    blocks.push(currentBlock);
    
    console.log('✨ Genesis block initialized');
    console.log(`🔗 Block #${currentBlock.number} hash: ${currentBlock.hash}`);
    
    // Start block production
    startBlockProduction();
  } catch (error) {
    console.error('❌ Failed to initialize blockchain:', error);
    process.exit(1);
  }
}

// Apply API rate limiting to all routes
app.use(apiLimiter);

// Enhanced health check endpoint
app.get('/health', (req, res) => {
  const uptime = process.uptime();
  const memoryUsage = process.memoryUsage();
  const poolSize = transactionPool.getAll().length;
  
  res.status(200).json({
    status: 'ok',
    version: '1.0.0',
    network: 'kethernet-testnet',
    node: os.hostname(),
    platform: `${os.platform()} ${os.arch()}`,
    cpuCores: os.cpus().length,
    totalMemory: Math.round(os.totalmem() / (1024 * 1024)) + ' MB',
    uptime: `${Math.floor(uptime / 3600)}h ${Math.floor((uptime % 3600) / 60)}m ${Math.floor(uptime % 60)}s`,
    memoryUsage: {
      rss: Math.round(memoryUsage.rss / (1024 * 1024)) + ' MB',
      heapTotal: Math.round(memoryUsage.heapTotal / (1024 * 1024)) + ' MB',
      heapUsed: Math.round(memoryUsage.heapUsed / (1024 * 1024)) + ' MB',
      external: Math.round(memoryUsage.external / (1024 * 1024)) + ' MB'
    },
    block: {
      number: currentBlockNumber,
      hash: currentBlock?.hash || '0x0',
      timestamp: currentBlock?.timestamp || 0,
      transactions: currentBlock?.transactions?.length || 0
    },
    pool: {
      pending: poolSize,
      queued: 0 // Not implemented yet
    },
    consensus: {
      status: isMining ? 'mining' : 'idle',
      validators: consensus.validators?.size || 0,
      epoch: consensus.epoch || 0,
      currentProposer: consensus.getCurrentProposer ? consensus.getCurrentProposer() : 'none'
    },
    timestamp: new Date().toISOString()
  });
});

// Aetherium faucet endpoint
app.post('/aetherium/faucet', express.json(), (req, res) => {
  try {
    const { address } = req.body;
    
    if (!address || typeof address !== 'string' || !address.startsWith('0x')) {
      return res.status(400).json({ error: 'Invalid address format' });
    }
    
    // Initialize account if it doesn't exist
    if (!aetheriumAccounts.has(address)) {
      aetheriumAccounts.set(address, {
        balance: new BN(0),
        nonce: 0
      });
    }
    
    // Fund the account (1 AE = 1e18 wei)
    const amount = new BN('1**********00000000'); // 1 AE
    aetheriumAccounts.get(address).balance.iadd(amount);
    
    // Deduct from genesis
    aetheriumAccounts.get('0x****************************************').balance.isub(amount);
    
    res.json({
      success: true,
      address,
      amount: amount.toString(),
      newBalance: aetheriumAccounts.get(address).balance.toString()
    });
  } catch (error) {
    console.error('Faucet error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Send Aetherium transaction
app.post('/aetherium/send', express.json(), (req, res) => {
  try {
    const { from, to, value, maxFeePerGas = '**********' } = req.body;
    
    // Validate required fields
    if (!from || !to || !value) {
      return res.status(400).json({ error: 'Missing required fields' });
    }
    
    // Initialize accounts if they don't exist
    if (!aetheriumAccounts.has(from)) {
      aetheriumAccounts.set(from, { balance: new BN(0), nonce: 0 });
    }
    if (!aetheriumAccounts.has(to)) {
      aetheriumAccounts.set(to, { balance: new BN(0), nonce: 0 });
    }
    
    const fromAccount = aetheriumAccounts.get(from);
    const toAccount = aetheriumAccounts.get(to);
    
    // Convert values to BN for calculations
    const amount = new BN(value);
    const fee = new BN(maxFeePerGas);
    
    // Check balance (amount + fee)
    const totalCost = amount.add(fee);
    if (fromAccount.balance.lt(totalCost)) {
      return res.status(400).json({ 
        error: 'Insufficient balance', 
        required: totalCost.toString(),
        available: fromAccount.balance.toString()
      });
    }
    
    // Process transaction
    fromAccount.balance.isub(totalCost);
    toAccount.balance.iadd(amount);
    
    // Update nonce
    fromAccount.nonce++;
    
    // Generate transaction hash
    const txHash = '0x' + crypto.randomBytes(32).toString('hex');
    
    // Update last block hash
    lastBlockHash = crypto.createHash('sha256')
      .update(lastBlockHash + txHash)
      .digest('hex');
    
    res.json({
      success: true,
      txHash,
      from,
      to,
      value: amount.toString(),
      fee: fee.toString(),
      nonce: fromAccount.nonce - 1,
      blockHash: lastBlockHash
    });
    
    console.log(`[${new Date().toISOString()}] TX ${txHash} | From: ${from} | To: ${to} | Value: ${amount} | Fee: ${fee}`);
  } catch (error) {
    console.error('Send error:', error);
    res.status(500).json({ 
      error: 'Transaction failed',
      details: error.message 
    });
  }
});

// Get account balance
app.get('/aetherium/balance/:address', (req, res) => {
  try {
    const { address } = req.params;
    const account = aetheriumAccounts.get(address) || { balance: new BN(0), nonce: 0 };
    
    res.json({
      address,
      balance: account.balance.toString(),
      nonce: account.nonce,
      blockHash: lastBlockHash
    });
  } catch (error) {
    console.error('Balance error:', error);
    res.status(500).json({ error: 'Failed to get balance' });
  }
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Unhandled error:', err);
  res.status(500).json({
    error: 'Internal Server Error',
    message: process.env.NODE_ENV === 'development' ? err.message : undefined
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({ error: 'Not Found' });
});

/**
 * Start block production process
 */
function startBlockProduction() {
  if (isMining) return;
  isMining = true;
  
  const mineNextBlock = async () => {
    if (!isMining) return;
    
    try {
      const nextBlockNumber = currentBlockNumber + 1;
      const isProposer = consensus.selectProposer(nextBlockNumber) === VALIDATOR_ADDRESS;
      
      if (isProposer) {
        console.log(`⛏️  Mining block #${nextBlockNumber}...`);
        await mineBlock();
      }
      
      // Process epoch if needed
      const epochResult = consensus.processEpoch(nextBlockNumber);
      if (epochResult.newEpoch) {
        console.log(`🔄 New epoch ${epochResult.epoch} started at block ${nextBlockNumber}`);
      }
      
    } catch (error) {
      console.error('Error in block production:', error);
    }
    
    // Schedule next block (2 second block time)
    setTimeout(mineNextBlock, 2000);
  };
  
  mineNextBlock();
}

/**
 * Mine a new block with transactions
 */
async function mineBlock() {
  if (isMining) return;
  isMining = true;
  
  try {
    const blockNumber = currentBlockNumber + 1;
    const timestamp = Math.floor(Date.now() / 1000);
    
    // Mine the block (simple PoW for now)
    const { hash } = await mineBlockWithProofOfWork(newBlock);
    newBlock.hash = hash;
    
    // Add to chain
    currentBlock = newBlock;
    currentBlockNumber = blockNumber;
    pendingTransactions.length = 0; // Clear pending transactions
    
    console.log(`✅ Mined block #${blockNumber} with ${newBlock.transactions.length} txs`);
    console.log(`🔗 Block hash: ${hash}`);
    
    return newBlock;
  } catch (error) {
    console.error('Block validation failed:', error.message);
    throw error;
  }
}

/**
 * Simple Proof of Work mining
 */
async function mineBlockWithProofOfWork(block, difficulty = 2) {
  return new Promise((resolve) => {
    const target = '0'.repeat(difficulty) + 'f'.repeat(64 - difficulty);
    const blockData = JSON.stringify(block);
    
    const mine = () => {
      block.nonce++;
      const hash = crypto.createHash('sha256').update(blockData + block.nonce).digest('hex');
      
      if (hash.startsWith(target)) {
        resolve({ hash, nonce: block.nonce });
      } else {
        setImmediate(mine);
      }
    };
    
    mine();
  });
}

// Initialize server
async function startServer() {
  try {
    // Initialize blockchain and consensus
    await initializeBlockchain();

    const server = app.listen(PORT, HOST, () => {
      const host = HOST === '0.0.0.0' ? 'localhost' : HOST;
      console.log(`🚀 KetherNet Server v1.0.0`);
      console.log(`🌐 Listening on http://${host}:${PORT}`);
      console.log(`🖥️  Hostname: ${os.hostname()}`);
      console.log(`📱 Platform: ${os.platform()} ${os.arch()}`);
      console.log(`⚡ CPU Cores: ${os.cpus().length}`);
      console.log(`💾 Memory: ${(os.totalmem() / 1024 / 1024 / 1024).toFixed(2)} GB`);
      console.log(`🌱 Environment: ${NODE_ENV}`);
      console.log(`🏷️  Validator: ${VALIDATOR_ADDRESS}`);
    });

    // Handle graceful shutdown
    const shutdown = async () => {
      console.log('\n🚦 Shutting down gracefully...');
      isMining = false;

      server.close(() => {
        console.log('✅ Server closed');
        console.log('✅ Server stopped');
        process.exit(0);
      });
      
      // Force shutdown after 5 seconds
      setTimeout(() => {
        console.error('⚠️  Forcing shutdown...');
        process.exit(1);
      }, 5000);
    };
    
    // Handle signals
    process.on('SIGTERM', () => shutdown('SIGTERM'));
    process.on('SIGINT', () => shutdown('SIGINT'));
    
    return server;
  } catch (error) {
    console.error('Failed to start server:', error);
    process.exit(1);
  }
}

// Transaction endpoints
app.post('/tx', async (req, res) => {
  try {
    const txData = req.body;
    const tx = new Transaction(txData);
    
    // Basic validation
    if (!tx.from || !tx.to || !tx.value || !tx.nonce) {
      return res.status(400).json({ error: 'Missing required fields' });
    }
    
    // Verify signature
    if (!tx.verify()) {
      return res.status(400).json({ error: 'Invalid transaction signature' });
    }
    
    // Add to transaction pool
    const added = transactionPool.add(tx);
    if (!added) {
      return res.status(400).json({ error: 'Transaction rejected by pool' });
    }
    
    console.log(`📝 New transaction: ${tx.hash().substring(0, 16)}...`);
    
    res.status(200).json({
      status: 'success',
      txHash: tx.hash(),
      message: 'Transaction added to pool'
    });
  } catch (error) {
    console.error('❌ Transaction error:', error);
    res.status(500).json({ error: 'Failed to process transaction', message: error.message });
  }
});

// Get transaction by hash
app.get('/tx/:txHash', (req, res) => {
  try {
    const { txHash } = req.params;
    const tx = transactionPool.get(txHash);
    
    if (!tx) {
      return res.status(404).json({ error: 'Transaction not found' });
    }
    
    res.status(200).json({
      status: 'success',
      transaction: tx.toJSON()
    });
  } catch (error) {
    console.error('❌ Transaction lookup error:', error);
    res.status(500).json({ error: 'Failed to fetch transaction', message: error.message });
  }
});

// Get account info
app.get('/account/:address', (req, res) => {
  try {
    const { address } = req.params;
    const account = accounts.get(address.toLowerCase());
    
    if (!account) {
      return res.status(404).json({ error: 'Account not found' });
    }
    
    res.status(200).json({
      status: 'success',
      address,
      balance: account.balance.toString(),
      nonce: account.nonce,
      code: account.code,
      storage: Object.fromEntries(account.storage)
    });
  } catch (error) {
    console.error('❌ Account lookup error:', error);
    res.status(500).json({ error: 'Failed to fetch account', message: error.message });
  }
});

// Get block by number or hash
app.get('/block/:identifier', (req, res) => {
  try {
    const { identifier } = req.params;
    let block;
    
    if (/^\d+$/.test(identifier)) {
      // Block number
      const blockNumber = parseInt(identifier, 10);
      block = blocks[blockNumber];
    } else {
      // Block hash
      block = blocks.find(b => b.hash === identifier);
    }
    
    if (!block) {
      return res.status(404).json({ error: 'Block not found' });
    }
    
    res.status(200).json({
      status: 'success',
      block
    });
  } catch (error) {
    console.error('❌ Block lookup error:', error);
    res.status(500).json({ error: 'Failed to fetch block', message: error.message });
  }
});

// Start the server if this file is run directly
if (require.main === module) {
  startServer().catch(console.error);
}

module.exports = { app, startServer };
