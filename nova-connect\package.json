{"name": "novafuse-universal-api-connector", "version": "1.0.0", "description": "NovaFuse Universal API Connector for plug-and-play integration", "main": "index.js", "scripts": {"start": "NODE_ENV=production node index.js", "start:cluster": "NODE_ENV=production node cluster.js", "dev": "nodemon --inspect index.js", "profile": "node --require @google-cloud/profiler index.js", "debug": "node --require @google-cloud/debug-agent index.js", "test": "jest", "test:unit": "jest --testMatch='**/*.unit.test.js'", "test:integration": "jest --testMatch='**/*.integration.test.js'", "test:performance": "jest --testMatch='**/*.performance.test.js'", "test:rbac": "node scripts/test-rbac.js", "test:rbac:manual": "node scripts/test-rbac-manual.js", "test:rbac:unit": "jest tests/unit/rbac-service.test.js", "test:rbac:integration": "jest tests/integration/rbac-api.test.js", "test:rbac:performance": "jest tests/performance/rbac-performance.test.js", "test:rbac:all": "node scripts/test-rbac-all.js", "test:security": "npm audit && snyk test", "test:security:scan": "node scripts/security-scan.js", "test:security:helmet": "node scripts/check-helmet.js", "test:security:rate-limit": "node scripts/check-rate-limit.js", "test:security:jwt": "node scripts/check-jwt-config.js", "test:security:https": "node scripts/check-https-redirect.js", "test:security:features": "jest tests/unit/services/RateLimitService.test.js tests/unit/services/BruteForceProtectionService.test.js tests/unit/services/IpRestrictionService.test.js tests/unit/services/AuthAuditService.test.js tests/integration/routes/securityFeatures.test.js", "test:security:manual": "node scripts/test-security-features.js", "test:security:all": "npm run test:security && npm run test:security:scan && npm run test:security:features", "test:stress": "artillery run tests/stress/stress-test.yml", "test:all": "node tests/run-tests.js", "test:coverage": "jest --coverage", "lint": "eslint .", "docs:build": "cd docs && mkdocs build", "docs:serve": "cd docs && mkdocs serve", "docker:build": "docker build -t novafuse/uac .", "docker:deploy": "docker-compose up -d --build"}, "keywords": ["api", "connector", "integration", "novafuse", "universal"], "author": "NovaGRC", "license": "UNLICENSED", "dependencies": {"@google-cloud/billing": "^3.1.0", "@google-cloud/bigquery": "^6.0.0", "@google-cloud/debug-agent": "^5.2.0", "@google-cloud/logging": "^10.4.0", "@google-cloud/monitoring": "^2.3.5", "@google-cloud/opentelemetry-cloud-trace-exporter": "^1.2.0", "@google-cloud/profiler": "^4.1.0", "@google-cloud/secret-manager": "^4.1.0", "@google-cloud/service-usage": "^2.2.1", "@google-cloud/tasks": "^2.3.0", "@graphql-tools/schema": "^9.0.0", "@opentelemetry/api": "^1.4.1", "@opentelemetry/exporter-zipkin": "^1.13.0", "@opentelemetry/instrumentation": "^0.39.1", "@opentelemetry/instrumentation-express": "^0.32.2", "@opentelemetry/instrumentation-http": "^0.39.1", "@opentelemetry/instrumentation-mongodb": "^0.34.0", "@opentelemetry/resources": "^1.13.0", "@opentelemetry/sdk-trace-base": "^1.13.0", "@opentelemetry/sdk-trace-node": "^1.13.0", "@opentelemetry/semantic-conventions": "^1.13.0", "@sentry/node": "^7.91.1", "ajv": "^8.12.0", "ajv-formats": "^2.1.1", "apollo-server-core": "^3.10.0", "apollo-server-express": "^3.10.0", "axios": "^1.6.2", "body-parser": "^1.20.2", "cluster": "^0.7.7", "compression": "^1.7.4", "cors": "^2.8.5", "csurf": "^1.11.0", "dotenv": "^16.3.1", "express": "^4.18.2", "express-brute-redis": "^0.0.1", "express-rate-limit": "^7.1.5", "graphql": "^16.5.0", "graphql-subscriptions": "^2.0.0", "helmet": "^7.1.0", "jsonpath": "^1.1.1", "jsonwebtoken": "^9.0.1", "mongoose": "^7.4.1", "morgan": "^1.10.0", "node-cache": "^5.1.2", "prom-client": "^14.2.0", "redis": "^4.6.10", "subscriptions-transport-ws": "^0.11.0", "uuid": "^9.0.1", "winston": "^3.10.0", "zapier-platform-core": "^15.5.1"}, "devDependencies": {"@types/jest": "^29.5.4", "artillery": "^2.0.0-31", "axios-mock-adapter": "^1.22.0", "chai": "^4.3.7", "docker-compose": "^0.23.19", "eslint": "^8.46.0", "jest": "^29.7.0", "mongodb-memory-server": "^8.13.0", "nodemon": "^3.0.1", "sinon": "^15.2.0", "snyk": "^1.1264.0", "supertest": "^6.3.3", "webpack-bundle-analyzer": "^4.8.0"}, "config": {"secure-cookie": true, "content-security-policy": "default-src 'self'", "hsts": "max-age=31536000; includeSubDomains"}, "gcp-marketplace": {"deploy": "./scripts/gcp-deploy.sh", "validate": "./scripts/gcp-validation.js", "monitoring": {"service": "novafuse-uac", "metrics": ["request_count", "error_rate"]}}, "performance": {"maxBundleSize": "500KB", "maxAPILatency": "100ms", "maxMemoryUsage": "512MB"}, "audit": {"level": "verbose", "retention": "30d", "export": "gs://novafuse-logs/audit"}}