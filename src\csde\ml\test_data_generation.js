/**
 * Test CSDE-Enhanced ML Data Generation
 * 
 * This script tests the data generation functionality of the CSDE-Enhanced ML module.
 */

const fs = require('fs');
const path = require('path');
const CSDEEnhancedML = require('./csde_enhanced_ml');

// Configuration
const config = {
  dataSize: 10, // Start with a small number for testing
  frameworks: ['NIST', 'GDPR'],
  outputDir: path.join(__dirname, 'data')
};

// Create output directory if it doesn't exist
if (!fs.existsSync(config.outputDir)) {
  fs.mkdirSync(config.outputDir, { recursive: true });
}

// Initialize CSDE-Enhanced ML
const csdeML = new CSDEEnhancedML();

console.log(`Generating ${config.dataSize} enhanced training samples...`);

try {
  // Generate enhanced training data
  const enhancedData = csdeML.generateEnhancedData(config.dataSize, config.frameworks);

  // Save data
  fs.writeFileSync(
    path.join(config.outputDir, 'test_data.json'),
    JSON.stringify(enhancedData, null, 2)
  );

  console.log(`Enhanced training data saved to ${path.join(config.outputDir, 'test_data.json')}`);
  
  // Print sample statistics
  console.log('\nSample Statistics:');
  console.log(`Number of samples: ${enhancedData.length}`);
  
  if (enhancedData.length > 0) {
    const firstSample = enhancedData[0];
    console.log('\nFirst Sample:');
    console.log(`CSDE Value: ${firstSample.csdeFeatures.csdeValue}`);
    console.log(`Performance Factor: ${firstSample.csdeFeatures.performanceFactor}`);
    console.log(`Number of controls: ${firstSample.input.complianceData.controls.length}`);
    console.log(`Number of services: ${firstSample.input.gcpData.services.length}`);
    console.log(`Number of cyber-safety controls: ${firstSample.input.cyberSafetyData.controls.length}`);
  }
} catch (error) {
  console.error('Error generating data:', error);
}
