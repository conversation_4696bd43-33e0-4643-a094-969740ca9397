# ConsciousNovaFold Analysis Report

## Sequence Information

* Sequence ID: seq_20250625_201452
* Sequence: ACDEFGHIKLMNPQRSTVWY

## Consciousness Metrics

* PSI Score: 0.660
* Fibonacci Analysis: {'residue_distances': [{'start': 9, 'end': 13, 'score': 0.9736756758994041, 'ratio': 1.6224085101824333, 'golden_ratio_diff': 0.0027036029298235856}, {'start': 5, 'end': 9, 'score': 0.9646260651191993, 'ratio': 1.6239675034062595, 'golden_ratio_diff': 0.0036671137303789765}, {'start': 6, 'end': 13, 'score': 0.9625962729269749, 'ratio': 1.6243212042121005, 'golden_ratio_diff': 0.0038857128502369186}, {'start': 6, 'end': 10, 'score': 0.9517661769606437, 'ratio': 1.6262338982417672, 'golden_ratio_diff': 0.005067822770649977}, {'start': 10, 'end': 15, 'score': 0.9463248122121147, 'ratio': 1.6088565607597827, 'golden_ratio_diff': 0.005671962427193992}, {'start': 2, 'end': 9, 'score': 0.9097053830115212, 'ratio': 1.6340941068954926, 'golden_ratio_diff': 0.009925698877318319}, {'start': 2, 'end': 6, 'score': 0.860701607276948, 'ratio': 1.6442207103847257, 'golden_ratio_diff': 0.016184284024257662}, {'start': 4, 'end': 13, 'score': 0.839236781552295, 'ratio': 1.5870391184931267, 'golden_ratio_diff': 0.01915588329557591}, {'start': 4, 'end': 12, 'score': 0.8033416402310151, 'ratio': 1.6576435265035359, 'golden_ratio_diff': 0.02448004061042227}, {'start': 3, 'end': 8, 'score': 0.7736564840218466, 'ratio': 1.6653717296658574, 'golden_ratio_diff': 0.029256332836701433}, {'start': 4, 'end': 10, 'score': 0.7560203314786469, 'ratio': 1.5658174834795426, 'golden_ratio_diff': 0.032271575030815715}, {'start': 3, 'end': 9, 'score': 0.7491642532005844, 'ratio': 1.5638588512975944, 'golden_ratio_diff': 0.0334820762907191}, {'start': 2, 'end': 12, 'score': 0.6987847677452858, 'ratio': 1.548287694008826, 'golden_ratio_diff': 0.04310558073934867}, {'start': 6, 'end': 14, 'score': 0.6965538729400185, 'ratio': 1.5475460959376401, 'golden_ratio_diff': 0.04356391355333285}, {'start': 1, 'end': 8, 'score': 0.6729985323890979, 'ratio': 1.539415765454924, 'golden_ratio_diff': 0.04858873413142071}, {'start': 4, 'end': 14, 'score': 0.6514616773845701, 'ratio': 1.5314675993595435, 'golden_ratio_diff': 0.053500970926595476}, {'start': 10, 'end': 17, 'score': 0.6447496822562129, 'ratio': 1.5288820117202686, 'golden_ratio_diff': 0.05509895196855894}, {'start': 2, 'end': 8, 'score': 0.5924241467905665, 'ratio': 1.7293514607096827, 'golden_ratio_diff': 0.06879798121286224}, {'start': 6, 'end': 12, 'score': 0.5896771395409139, 'ratio': 1.730623792570468, 'golden_ratio_diff': 0.06958432554779691}, {'start': 2, 'end': 13, 'score': 0.583921973345524, 'ratio': 1.5027397508499696, 'golden_ratio_diff': 0.07125575772917014}, {'start': 5, 'end': 12, 'score': 0.5788694817355997, 'ratio': 1.5003211858208763, 'golden_ratio_diff': 0.0727505131211517}, {'start': 3, 'end': 12, 'score': 0.5654050683648699, 'ratio': 1.4936648576493063, 'golden_ratio_diff': 0.07686435017145535}, {'start': 10, 'end': 18, 'score': 0.5614368017646203, 'ratio': 1.4916421742608257, 'golden_ratio_diff': 0.0781144372540162}, {'start': 6, 'end': 15, 'score': 0.5595966956514902, 'ratio': 1.4906945100269104, 'golden_ratio_diff': 0.07870012596049844}, {'start': 9, 'end': 14, 'score': 0.5554391731626486, 'ratio': 1.4885302442095445, 'golden_ratio_diff': 0.08003771579632017}, {'start': 4, 'end': 15, 'score': 0.5536600845230121, 'ratio': 1.4875941802887696, 'golden_ratio_diff': 0.08061623511500159}, {'start': 1, 'end': 9, 'score': 0.5486030492068428, 'ratio': 1.4849002757934409, 'golden_ratio_diff': 0.08228115965556085}, {'start': 4, 'end': 7, 'score': 0.5311219631816593, 'ratio': 1.475192861336206, 'golden_ratio_diff': 0.08828067173301411}, {'start': 3, 'end': 6, 'score': 0.5285248527772692, 'ratio': 1.4736958731345968, 'golden_ratio_diff': 0.08920586132236616}, {'start': 4, 'end': 17, 'score': 0.5185672544688347, 'ratio': 1.4678173025141812, 'golden_ratio_diff': 0.09283901777104957}, {'start': 6, 'end': 17, 'score': 0.5161272022277574, 'ratio': 1.4663421907497765, 'golden_ratio_diff': 0.09375068697865653}, {'start': 2, 'end': 14, 'score': 0.5138219867823558, 'ratio': 1.4649357231627795, 'golden_ratio_diff': 0.09461993175149568}], 'torsion_angles': [{'start': 14, 'end': 18, 'score': 0.5325831223851675, 'ratio': 1.476028663551251, 'golden_ratio_diff': 0.08776411755624387}], 'overall_score': 0.687428606439864}
* Fibonacci Alignment: {'closest_fibonacci': 21, 'difference': 1, 'ratio': 0.9523809523809523, 'alignment_score': 0.9545454545454545}
* Trinity Validation: {'ners': np.float64(0.6682285819319591), 'nepi': 0.5389999999999999, 'nefc': 0.694, 'overall': np.float64(0.6371914327727836), 'passed': False}
* Trinity Report: {'scores': {'ners': np.float64(0.6682285819319591), 'nepi': 0.5389999999999999, 'nefc': 0.694, 'overall': np.float64(0.6371914327727836), 'passed': False}, 'thresholds': {'ners': 0.7, 'nepi': 0.5, 'nefc': 0.6}, 'weights': {'ners': 0.4, 'nepi': 0.3, 'nefc': 0.3}, 'validation': {'ners': {'score': np.float64(0.6682285819319591), 'threshold': 0.7, 'passed': np.False_, 'description': 'Neural-Emotional Resonance Score: Measures structural harmony and consciousness resonance.'}, 'nepi': {'score': 0.5389999999999999, 'threshold': 0.5, 'passed': True, 'description': 'Neural-Emotional Potential Index: Evaluates functional potential and adaptability.'}, 'nefc': {'score': 0.694, 'threshold': 0.6, 'passed': True, 'description': 'Neural-Emotional Field Coherence: Assesses field coherence and quantum effects.'}, 'overall': {'score': np.float64(0.6371914327727836), 'passed': False, 'description': 'Overall validation status based on all metrics.'}}}

## Structure Information

* Structure information not available
