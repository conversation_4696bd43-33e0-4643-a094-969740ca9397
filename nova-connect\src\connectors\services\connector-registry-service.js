/**
 * NovaFuse Universal API Connector - Connector Registry Service
 * 
 * This module provides services for managing the connector registry.
 */

const fs = require('fs').promises;
const path = require('path');
const { createLogger } = require('../../utils/logger');
const { Connector, ConnectorStatus } = require('../models/connector-registry');
const { ValidationError, ResourceNotFoundError } = require('../../errors');

const logger = createLogger('connector-registry-service');

/**
 * Connector Registry Service class
 */
class ConnectorRegistryService {
  constructor(options = {}) {
    this.dataDir = options.dataDir || path.join(process.cwd(), 'data', 'connectors');
    this.connectors = new Map();
    this.initialized = false;
    
    logger.info('Connector registry service initialized');
  }

  /**
   * Initialize the registry
   */
  async initialize() {
    if (this.initialized) {
      return;
    }

    try {
      // Create data directory if it doesn't exist
      await fs.mkdir(this.dataDir, { recursive: true });
      
      // Load connectors from disk
      await this._loadConnectors();
      
      this.initialized = true;
      logger.info(`Connector registry initialized with ${this.connectors.size} connectors`);
    } catch (error) {
      logger.error('Failed to initialize connector registry:', { error });
      throw error;
    }
  }

  /**
   * Load connectors from disk
   * 
   * @private
   */
  async _loadConnectors() {
    try {
      // Get connector files
      const files = await fs.readdir(this.dataDir);
      const connectorFiles = files.filter(file => file.endsWith('.json'));
      
      // Load each connector
      for (const file of connectorFiles) {
        try {
          const data = await fs.readFile(path.join(this.dataDir, file), 'utf8');
          const connectorData = JSON.parse(data);
          const connector = new Connector(connectorData);
          
          this.connectors.set(connector.id, connector);
          logger.debug(`Loaded connector: ${connector.name} (${connector.id})`);
        } catch (error) {
          logger.error(`Failed to load connector from file ${file}:`, { error });
        }
      }
    } catch (error) {
      logger.error('Failed to load connectors from disk:', { error });
      throw error;
    }
  }

  /**
   * Save a connector to disk
   * 
   * @param {Connector} connector - The connector to save
   * @private
   */
  async _saveConnector(connector) {
    try {
      const filePath = path.join(this.dataDir, `${connector.id}.json`);
      await fs.writeFile(filePath, JSON.stringify(connector.toJSON(), null, 2));
      logger.debug(`Saved connector to disk: ${connector.name} (${connector.id})`);
    } catch (error) {
      logger.error(`Failed to save connector ${connector.id}:`, { error });
      throw error;
    }
  }

  /**
   * Get all connectors
   * 
   * @param {Object} filters - Filters to apply
   * @returns {Array<Connector>} - The connectors
   */
  async getAllConnectors(filters = {}) {
    await this.initialize();
    
    let connectors = Array.from(this.connectors.values());
    
    // Apply filters
    if (filters.status) {
      connectors = connectors.filter(c => c.status === filters.status);
    }
    
    if (filters.type) {
      connectors = connectors.filter(c => c.type === filters.type);
    }
    
    if (filters.category) {
      connectors = connectors.filter(c => c.category === filters.category);
    }
    
    if (filters.tag) {
      connectors = connectors.filter(c => c.tags.includes(filters.tag));
    }
    
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      connectors = connectors.filter(c => 
        c.name.toLowerCase().includes(searchLower) || 
        c.description.toLowerCase().includes(searchLower)
      );
    }
    
    // Apply sorting
    if (filters.sortBy) {
      const sortField = filters.sortBy;
      const sortOrder = filters.sortOrder === 'desc' ? -1 : 1;
      
      connectors.sort((a, b) => {
        if (a[sortField] < b[sortField]) return -1 * sortOrder;
        if (a[sortField] > b[sortField]) return 1 * sortOrder;
        return 0;
      });
    } else {
      // Default sort by name
      connectors.sort((a, b) => a.name.localeCompare(b.name));
    }
    
    return connectors;
  }

  /**
   * Get a connector by ID
   * 
   * @param {string} id - The connector ID
   * @returns {Connector} - The connector
   */
  async getConnector(id) {
    await this.initialize();
    
    const connector = this.connectors.get(id);
    
    if (!connector) {
      throw new ResourceNotFoundError('Connector', id);
    }
    
    return connector;
  }

  /**
   * Create a new connector
   * 
   * @param {Object} data - The connector data
   * @returns {Connector} - The created connector
   */
  async createConnector(data) {
    await this.initialize();
    
    // Create connector
    const connector = new Connector(data);
    
    // Validate connector
    const validation = connector.validate();
    if (!validation.valid) {
      throw new ValidationError('Invalid connector data', { validationErrors: validation.errors });
    }
    
    // Save connector
    this.connectors.set(connector.id, connector);
    await this._saveConnector(connector);
    
    logger.info(`Created connector: ${connector.name} (${connector.id})`);
    
    return connector;
  }

  /**
   * Update a connector
   * 
   * @param {string} id - The connector ID
   * @param {Object} data - The data to update
   * @returns {Connector} - The updated connector
   */
  async updateConnector(id, data) {
    await this.initialize();
    
    // Get connector
    const connector = await this.getConnector(id);
    
    // Check if connector can be updated
    if (connector.status === ConnectorStatus.RETIRED) {
      throw new ValidationError('Cannot update a retired connector');
    }
    
    // Update connector
    connector.update(data);
    
    // Validate connector
    const validation = connector.validate();
    if (!validation.valid) {
      throw new ValidationError('Invalid connector data', { validationErrors: validation.errors });
    }
    
    // Save connector
    await this._saveConnector(connector);
    
    logger.info(`Updated connector: ${connector.name} (${connector.id})`);
    
    return connector;
  }

  /**
   * Delete a connector
   * 
   * @param {string} id - The connector ID
   * @returns {boolean} - Whether the connector was deleted
   */
  async deleteConnector(id) {
    await this.initialize();
    
    // Get connector
    const connector = await this.getConnector(id);
    
    // Check if connector can be deleted
    if (connector.status === ConnectorStatus.PUBLISHED) {
      throw new ValidationError('Cannot delete a published connector. Deprecate it first.');
    }
    
    // Delete connector
    this.connectors.delete(id);
    
    try {
      // Delete file
      const filePath = path.join(this.dataDir, `${id}.json`);
      await fs.unlink(filePath);
    } catch (error) {
      logger.error(`Failed to delete connector file for ${id}:`, { error });
    }
    
    logger.info(`Deleted connector: ${connector.name} (${connector.id})`);
    
    return true;
  }

  /**
   * Publish a connector
   * 
   * @param {string} id - The connector ID
   * @returns {Connector} - The published connector
   */
  async publishConnector(id) {
    await this.initialize();
    
    // Get connector
    const connector = await this.getConnector(id);
    
    // Publish connector
    connector.publish();
    
    // Save connector
    await this._saveConnector(connector);
    
    return connector;
  }

  /**
   * Deprecate a connector
   * 
   * @param {string} id - The connector ID
   * @param {string} reason - The reason for deprecation
   * @returns {Connector} - The deprecated connector
   */
  async deprecateConnector(id, reason) {
    await this.initialize();
    
    // Get connector
    const connector = await this.getConnector(id);
    
    // Deprecate connector
    connector.deprecate(reason);
    
    // Save connector
    await this._saveConnector(connector);
    
    return connector;
  }

  /**
   * Retire a connector
   * 
   * @param {string} id - The connector ID
   * @param {string} reason - The reason for retirement
   * @returns {Connector} - The retired connector
   */
  async retireConnector(id, reason) {
    await this.initialize();
    
    // Get connector
    const connector = await this.getConnector(id);
    
    // Retire connector
    connector.retire(reason);
    
    // Save connector
    await this._saveConnector(connector);
    
    return connector;
  }

  /**
   * Create a new version of a connector
   * 
   * @param {string} id - The connector ID
   * @param {string} version - The new version
   * @returns {Connector} - The new connector version
   */
  async createConnectorVersion(id, version) {
    await this.initialize();
    
    // Get connector
    const connector = await this.getConnector(id);
    
    // Create new version
    const newConnector = connector.createNewVersion(version);
    
    // Save new connector
    this.connectors.set(newConnector.id, newConnector);
    await this._saveConnector(newConnector);
    
    logger.info(`Created new version ${version} of connector ${connector.name} (${newConnector.id})`);
    
    return newConnector;
  }

  /**
   * Track connector usage
   * 
   * @param {string} id - The connector ID
   * @param {boolean} success - Whether the usage was successful
   * @param {number} responseTime - The response time in milliseconds
   */
  async trackConnectorUsage(id, success, responseTime) {
    await this.initialize();
    
    // Get connector
    const connector = await this.getConnector(id);
    
    // Track usage
    connector.trackUsage(success, responseTime);
    
    // Save connector
    await this._saveConnector(connector);
  }

  /**
   * Get connector metrics
   * 
   * @param {string} id - The connector ID
   * @returns {Object} - The connector metrics
   */
  async getConnectorMetrics(id) {
    await this.initialize();
    
    // Get connector
    const connector = await this.getConnector(id);
    
    return connector.metrics;
  }

  /**
   * Get connector dependencies
   * 
   * @param {string} id - The connector ID
   * @returns {Array<Connector>} - The connector dependencies
   */
  async getConnectorDependencies(id) {
    await this.initialize();
    
    // Get connector
    const connector = await this.getConnector(id);
    
    // Get dependencies
    const dependencies = [];
    
    for (const depId of connector.dependencies) {
      try {
        const depConnector = await this.getConnector(depId);
        dependencies.push(depConnector);
      } catch (error) {
        logger.warn(`Dependency ${depId} for connector ${id} not found`);
      }
    }
    
    return dependencies;
  }

  /**
   * Get connectors that depend on a connector
   * 
   * @param {string} id - The connector ID
   * @returns {Array<Connector>} - The dependent connectors
   */
  async getDependentConnectors(id) {
    await this.initialize();
    
    // Find connectors that depend on this connector
    const dependents = [];
    
    for (const connector of this.connectors.values()) {
      if (connector.dependencies.includes(id)) {
        dependents.push(connector);
      }
    }
    
    return dependents;
  }
}

// Create singleton instance
const connectorRegistryService = new ConnectorRegistryService();

module.exports = connectorRegistryService;
