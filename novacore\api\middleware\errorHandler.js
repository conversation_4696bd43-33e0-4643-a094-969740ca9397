/**
 * NovaCore Error Handler Middleware
 * 
 * This middleware handles errors for the NovaCore API.
 */

const logger = require('../../config/logger');
const { 
  ValidationError, 
  NotFoundError, 
  AuthenticationError, 
  AuthorizationError 
} = require('../utils/errors');

/**
 * Error handler middleware
 * @param {Error} err - Error object
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const errorHandler = (err, req, res, next) => {
  // Log error
  logger.error('API Error:', {
    error: err.message,
    stack: err.stack,
    path: req.path,
    method: req.method,
    ip: req.ip
  });
  
  // Handle specific error types
  if (err instanceof ValidationError) {
    return res.status(400).json({
      success: false,
      error: {
        message: err.message,
        code: 'VALIDATION_ERROR',
        details: err.details
      }
    });
  }
  
  if (err instanceof NotFoundError) {
    return res.status(404).json({
      success: false,
      error: {
        message: err.message,
        code: 'NOT_FOUND_ERROR'
      }
    });
  }
  
  if (err instanceof AuthenticationError) {
    return res.status(401).json({
      success: false,
      error: {
        message: err.message,
        code: 'AUTHENTICATION_ERROR'
      }
    });
  }
  
  if (err instanceof AuthorizationError) {
    return res.status(403).json({
      success: false,
      error: {
        message: err.message,
        code: 'AUTHORIZATION_ERROR'
      }
    });
  }
  
  // Handle Mongoose validation errors
  if (err.name === 'ValidationError') {
    const details = {};
    
    for (const field in err.errors) {
      details[field] = err.errors[field].message;
    }
    
    return res.status(400).json({
      success: false,
      error: {
        message: 'Validation error',
        code: 'VALIDATION_ERROR',
        details
      }
    });
  }
  
  // Handle MongoDB duplicate key errors
  if (err.name === 'MongoError' && err.code === 11000) {
    return res.status(400).json({
      success: false,
      error: {
        message: 'Duplicate key error',
        code: 'DUPLICATE_KEY_ERROR',
        details: err.keyValue
      }
    });
  }
  
  // Handle JWT errors
  if (err.name === 'JsonWebTokenError') {
    return res.status(401).json({
      success: false,
      error: {
        message: 'Invalid token',
        code: 'INVALID_TOKEN_ERROR'
      }
    });
  }
  
  if (err.name === 'TokenExpiredError') {
    return res.status(401).json({
      success: false,
      error: {
        message: 'Token expired',
        code: 'TOKEN_EXPIRED_ERROR'
      }
    });
  }
  
  // Handle other errors
  const statusCode = err.statusCode || 500;
  const message = err.message || 'Internal server error';
  
  return res.status(statusCode).json({
    success: false,
    error: {
      message,
      code: err.code || 'INTERNAL_SERVER_ERROR'
    }
  });
};

module.exports = errorHandler;
