#!/bin/bash
# Deployment script for NovaConnect with CSDE integration

# Exit on error
set -e

# Configuration
NOVA_CONNECT_DIR="$(pwd)"
CSDE_DIR="../src/csde"
ENV_FILE=".env"

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Print header
echo -e "${GREEN}=========================================${NC}"
echo -e "${GREEN}NovaConnect + CSDE Integration Deployment${NC}"
echo -e "${GREEN}=========================================${NC}"

# Check if Docker is running
echo -e "\n${YELLOW}Checking if Docker is running...${NC}"
if ! docker info > /dev/null 2>&1; then
  echo -e "${RED}Docker is not running. Please start Docker and try again.${NC}"
  exit 1
fi
echo -e "${GREEN}Docker is running.${NC}"

# Check if CSDE directory exists
echo -e "\n${YELLOW}Checking if CSDE directory exists...${NC}"
if [ ! -d "$CSDE_DIR" ]; then
  echo -e "${RED}CSDE directory not found at $CSDE_DIR. Please check the path and try again.${NC}"
  exit 1
fi
echo -e "${GREEN}CSDE directory found at $CSDE_DIR.${NC}"

# Create .env file if it doesn't exist
echo -e "\n${YELLOW}Creating .env file...${NC}"
if [ ! -f "$ENV_FILE" ]; then
  echo "NODE_ENV=production" > "$ENV_FILE"
  echo "PORT=3001" >> "$ENV_FILE"
  echo "CSDE_API_URL=http://csde-api:3010" >> "$ENV_FILE"
  echo "LOG_LEVEL=info" >> "$ENV_FILE"
  echo "METRICS_ENABLED=true" >> "$ENV_FILE"
  echo "TRACING_ENABLED=true" >> "$ENV_FILE"
  echo -e "${GREEN}.env file created.${NC}"
else
  # Check if CSDE_API_URL is in .env file
  if ! grep -q "CSDE_API_URL" "$ENV_FILE"; then
    echo "CSDE_API_URL=http://csde-api:3010" >> "$ENV_FILE"
    echo -e "${GREEN}Added CSDE_API_URL to .env file.${NC}"
  else
    echo -e "${GREEN}.env file already exists and contains CSDE_API_URL.${NC}"
  fi
fi

# Create necessary directories
echo -e "\n${YELLOW}Creating necessary directories...${NC}"
mkdir -p logs
mkdir -p monitoring/prometheus
mkdir -p monitoring/grafana/provisioning/dashboards
echo -e "${GREEN}Directories created.${NC}"

# Build and start the containers
echo -e "\n${YELLOW}Building and starting containers...${NC}"
docker-compose build
docker-compose up -d
echo -e "${GREEN}Containers built and started.${NC}"

# Wait for services to start
echo -e "\n${YELLOW}Waiting for services to start...${NC}"
sleep 10

# Check if services are running
echo -e "\n${YELLOW}Checking if services are running...${NC}"
if ! docker-compose ps | grep -q "Up"; then
  echo -e "${RED}Services failed to start. Please check the logs with 'docker-compose logs'.${NC}"
  exit 1
fi
echo -e "${GREEN}Services are running.${NC}"

# Print service URLs
echo -e "\n${GREEN}=========================================${NC}"
echo -e "${GREEN}Services are now available at:${NC}"
echo -e "${GREEN}NovaConnect API: http://localhost:3001${NC}"
echo -e "${GREEN}CSDE API: http://localhost:3010${NC}"
echo -e "${GREEN}Grafana: http://localhost:3002${NC}"
echo -e "${GREEN}Prometheus: http://localhost:9090${NC}"
echo -e "${GREEN}=========================================${NC}"

# Print next steps
echo -e "\n${YELLOW}Next steps:${NC}"
echo -e "1. Access the NovaConnect API at http://localhost:3001"
echo -e "2. Access the CSDE API at http://localhost:3010"
echo -e "3. Access Grafana at http://localhost:3002 (username: admin, password: admin)"
echo -e "4. Access Prometheus at http://localhost:9090"
echo -e "5. View the CSDE Integration Dashboard in Grafana"
echo -e "6. To stop the services, run 'docker-compose down'"
echo -e "7. To view logs, run 'docker-compose logs -f'"

echo -e "\n${GREEN}Deployment complete!${NC}"
