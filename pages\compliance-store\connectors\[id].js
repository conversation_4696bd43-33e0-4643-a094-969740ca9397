import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/router';
import PageWithSidebar from '../../../components/PageWithSidebar';
import ConnectorExecution from '../../../components/ConnectorExecution';
import { useAuth } from '../../../contexts/AuthContext';
import axios from 'axios';

// API URL
const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api';

export default function ConnectorDetail() {
  const router = useRouter();
  const { id } = router.query;
  const { user, isAuthenticated } = useAuth();
  const [isInstalled, setIsInstalled] = useState(false);
  const [isInstalling, setIsInstalling] = useState(false);
  const [installError, setInstallError] = useState('');

  // Sample connector data (in a real app, this would come from an API based on the ID)
  const connectors = {
    'gdpr-shield': {
      id: 'gdpr-shield',
      name: 'GDPR Shield',
      vendor: 'Compliance Partners Inc.',
      description: 'Comprehensive GDPR compliance solution with automated data mapping, consent management, and breach notification.',
      longDescription: 'GDPR Shield provides a complete solution for organizations that need to comply with the European Union\'s General Data Protection Regulation (GDPR). Our connector automates the most challenging aspects of GDPR compliance, including data mapping, consent management, and breach notification processes.',
      category: 'data-privacy',
      framework: 'gdpr',
      rating: 4.5,
      reviewCount: 48,
      price: '$499/mo',
      features: [
        'Automated data mapping and inventory',
        'Consent management system',
        'Data subject request handling',
        'Breach notification workflow',
        'Risk assessment tools',
        'Compliance documentation generator',
        'Regular compliance updates',
        'Cross-border data transfer management'
      ],
      integrations: [
        'CRM systems',
        'Marketing platforms',
        'HR systems',
        'Cloud storage providers',
        'E-commerce platforms'
      ]
    },
    'soc2-automator': {
      id: 'soc2-automator',
      name: 'SOC2 Automator',
      vendor: 'TrustTech Solutions',
      description: 'Automate SOC2 evidence collection, control testing, and audit preparation with continuous compliance monitoring.',
      longDescription: 'SOC2 Automator streamlines the entire SOC2 compliance process, from evidence collection to audit preparation. Our connector provides continuous monitoring of your controls, automated evidence collection, and comprehensive reporting to make your SOC2 audit as painless as possible.',
      category: 'security',
      framework: 'soc2',
      rating: 4.0,
      reviewCount: 36,
      price: '$599/mo',
      features: [
        'Automated evidence collection',
        'Control testing and validation',
        'Continuous compliance monitoring',
        'Audit preparation tools',
        'Gap analysis',
        'Remediation tracking',
        'Auditor collaboration portal',
        'Compliance dashboard'
      ],
      integrations: [
        'Cloud service providers',
        'DevOps tools',
        'Identity management systems',
        'Monitoring tools',
        'Ticketing systems'
      ]
    },
    'hipaa-guardian': {
      id: 'hipaa-guardian',
      name: 'HIPAA Guardian',
      vendor: 'HealthSec Systems',
      description: 'Complete HIPAA compliance solution with PHI tracking, business associate management, and security risk assessment.',
      longDescription: 'HIPAA Guardian provides healthcare organizations with a comprehensive solution for HIPAA compliance. Our connector helps you track protected health information (PHI), manage business associates, conduct security risk assessments, and maintain compliance with all HIPAA requirements.',
      category: 'healthcare',
      framework: 'hipaa',
      rating: 5.0,
      reviewCount: 42,
      price: '$699/mo',
      features: [
        'PHI tracking and inventory',
        'Business associate management',
        'Security risk assessment',
        'Incident response planning',
        'HIPAA training management',
        'Policy and procedure templates',
        'Audit logging and monitoring',
        'Breach notification workflow'
      ],
      integrations: [
        'Electronic health record (EHR) systems',
        'Healthcare management platforms',
        'Medical billing systems',
        'Patient portals',
        'Telehealth platforms'
      ]
    },
    'pci-compliance-suite': {
      id: 'pci-compliance-suite',
      name: 'PCI Compliance Suite',
      vendor: 'SecurePayments Inc.',
      description: 'End-to-end PCI DSS compliance solution with cardholder data scanning, vulnerability management, and audit reporting.',
      longDescription: 'PCI Compliance Suite helps organizations that handle credit card data achieve and maintain compliance with the Payment Card Industry Data Security Standard (PCI DSS). Our connector provides tools for cardholder data scanning, vulnerability management, and comprehensive audit reporting.',
      category: 'financial',
      framework: 'pci-dss',
      rating: 4.2,
      reviewCount: 31,
      price: '$549/mo',
      features: [
        'Cardholder data scanning',
        'Vulnerability management',
        'Security policy templates',
        'Self-assessment questionnaire automation',
        'Network security monitoring',
        'Penetration testing coordination',
        'Compliance reporting',
        'Remediation tracking'
      ],
      integrations: [
        'Payment gateways',
        'E-commerce platforms',
        'Point-of-sale systems',
        'Vulnerability scanners',
        'Firewall management tools'
      ]
    },
    'iso27001-toolkit': {
      id: 'iso27001-toolkit',
      name: 'ISO 27001 Toolkit',
      vendor: 'Global Compliance Solutions',
      description: 'Complete ISO 27001 implementation toolkit with risk assessment templates, policy generators, and audit preparation tools.',
      longDescription: 'ISO 27001 Toolkit provides everything you need to implement and maintain an ISO 27001-compliant information security management system (ISMS). Our connector includes risk assessment templates, policy generators, control implementation guidance, and audit preparation tools.',
      category: 'security',
      framework: 'iso-27001',
      rating: 4.7,
      reviewCount: 39,
      price: '$649/mo',
      features: [
        'ISMS implementation framework',
        'Risk assessment templates',
        'Policy and procedure generators',
        'Control implementation guidance',
        'Gap analysis tools',
        'Internal audit support',
        'Management review automation',
        'Certification preparation'
      ],
      integrations: [
        'Document management systems',
        'Risk management platforms',
        'Asset management tools',
        'HR systems',
        'Incident management systems'
      ]
    },
    'ccpa-compliance-manager': {
      id: 'ccpa-compliance-manager',
      name: 'CCPA Compliance Manager',
      vendor: 'PrivacyTech Solutions',
      description: 'California Consumer Privacy Act compliance solution with consumer request management, data inventory, and privacy policy generator.',
      longDescription: 'CCPA Compliance Manager helps businesses comply with the California Consumer Privacy Act (CCPA) and other state privacy laws. Our connector provides tools for managing consumer requests, maintaining a data inventory, and generating compliant privacy policies.',
      category: 'data-privacy',
      framework: 'ccpa',
      rating: 4.3,
      reviewCount: 28,
      price: '$399/mo',
      features: [
        'Consumer request management',
        'Data inventory and mapping',
        'Privacy policy generator',
        'Opt-out preference management',
        'Vendor management',
        'Compliance reporting',
        'Training materials',
        'Regulatory updates'
      ],
      integrations: [
        'CRM systems',
        'Marketing platforms',
        'E-commerce systems',
        'Data management platforms',
        'Website management tools'
      ]
    },
    'nist-framework-connector': {
      id: 'nist-framework-connector',
      name: 'NIST Framework Connector',
      vendor: 'FedSec Solutions',
      description: 'NIST Cybersecurity Framework implementation tool with control mapping, gap analysis, and continuous monitoring.',
      longDescription: 'NIST Framework Connector helps organizations implement and maintain compliance with the NIST Cybersecurity Framework. Our connector provides control mapping, gap analysis, implementation guidance, and continuous monitoring to ensure your cybersecurity program aligns with NIST standards.',
      category: 'security',
      framework: 'nist',
      rating: 4.6,
      reviewCount: 33,
      price: '$599/mo',
      features: [
        'NIST CSF control mapping',
        'Gap analysis tools',
        'Implementation guidance',
        'Maturity assessment',
        'Continuous monitoring',
        'Risk management integration',
        'Reporting and dashboards',
        'Cross-framework mapping'
      ],
      integrations: [
        'Security information and event management (SIEM) systems',
        'Vulnerability management tools',
        'Asset management platforms',
        'Identity and access management systems',
        'Incident response platforms'
      ]
    },
    'finra-compliance-assistant': {
      id: 'finra-compliance-assistant',
      name: 'FINRA Compliance Assistant',
      vendor: 'FinTech Compliance Inc.',
      description: 'Financial Industry Regulatory Authority compliance solution with trade monitoring, communications review, and regulatory reporting.',
      longDescription: 'FINRA Compliance Assistant helps broker-dealers and financial firms comply with FINRA regulations. Our connector provides tools for trade monitoring, communications review, regulatory reporting, and maintaining compliance with all applicable FINRA rules.',
      category: 'financial',
      framework: 'finra',
      rating: 4.4,
      reviewCount: 26,
      price: '$749/mo',
      features: [
        'Trade monitoring and surveillance',
        'Communications review and archiving',
        'Regulatory reporting automation',
        'Licensing and registration management',
        'Anti-money laundering (AML) tools',
        'Conflict of interest management',
        'Compliance calendar',
        'Regulatory updates'
      ],
      integrations: [
        'Trading platforms',
        'Customer relationship management (CRM) systems',
        'Email and communication platforms',
        'Document management systems',
        'Financial reporting tools'
      ]
    },
    'fedramp-accelerator': {
      id: 'fedramp-accelerator',
      name: 'FedRAMP Accelerator',
      vendor: 'GovCloud Security',
      description: 'Federal Risk and Authorization Management Program compliance accelerator with security control implementation and documentation.',
      longDescription: 'FedRAMP Accelerator helps cloud service providers and federal agencies achieve and maintain FedRAMP compliance. Our connector provides tools for security control implementation, documentation, continuous monitoring, and the entire authorization process.',
      category: 'security',
      framework: 'fedramp',
      rating: 4.8,
      reviewCount: 22,
      price: '$899/mo',
      features: [
        'Security control implementation',
        'Documentation templates',
        'System security plan (SSP) generator',
        'Continuous monitoring tools',
        'Plan of action and milestones (POA&M) tracking',
        '3PAO coordination',
        'Authorization package preparation',
        'Compliance dashboard'
      ],
      integrations: [
        'Cloud service provider platforms',
        'Security information and event management (SIEM) systems',
        'Vulnerability management tools',
        'Configuration management databases (CMDB)',
        'Document management systems'
      ]
    }
  };

  // Check if connector is installed
  useEffect(() => {
    const checkInstallation = async () => {
      if (!id || !isAuthenticated) return;

      try {
        // In a real app, this would be an API call
        // const response = await axios.get(`${API_URL}/dashboard/connectors`);
        // const installed = response.data.some(c => c.connector === id);
        // setIsInstalled(installed);

        // For now, we'll simulate a random installation status
        setIsInstalled(Math.random() > 0.5);
      } catch (error) {
        console.error('Error checking installation:', error);
      }
    };

    checkInstallation();
  }, [id, isAuthenticated]);

  // Install connector
  const installConnector = async () => {
    if (!isAuthenticated) {
      router.push(`/login?redirect=${encodeURIComponent(router.asPath)}`);
      return;
    }

    try {
      setInstallError('');
      setIsInstalling(true);

      // In a real app, this would be an API call
      // await axios.post(`${API_URL}/connectors/${id}/install`);

      // For now, we'll just simulate a successful installation
      await new Promise(resolve => setTimeout(resolve, 1000));

      setIsInstalled(true);
    } catch (error) {
      setInstallError(error.response?.data?.message || 'Failed to install connector. Please try again.');
    } finally {
      setIsInstalling(false);
    }
  };

  // Uninstall connector
  const uninstallConnector = async () => {
    try {
      setInstallError('');
      setIsInstalling(true);

      // In a real app, this would be an API call
      // await axios.post(`${API_URL}/connectors/${id}/uninstall`);

      // For now, we'll just simulate a successful uninstallation
      await new Promise(resolve => setTimeout(resolve, 1000));

      setIsInstalled(false);
    } catch (error) {
      setInstallError(error.response?.data?.message || 'Failed to uninstall connector. Please try again.');
    } finally {
      setIsInstalling(false);
    }
  };

  // Handle execution completion
  const handleExecutionComplete = (result) => {
    console.log('Execution complete:', result);
    // In a real app, you might update the UI or show a notification
  };

  // Get the connector data based on the ID
  const connector = connectors[id];

  // If connector not found, show error
  if (!connector && id) {
    return (
      <PageWithSidebar title="Connector Not Found" sidebarItems={[
        { label: 'Back to Browse', href: '/compliance-store/browse' },
        { label: 'Compliance Store Home', href: '/compliance-store' }
      ]}>
        <div className="bg-secondary rounded-lg p-8 text-center">
          <h1 className="text-3xl font-bold mb-4">Connector Not Found</h1>
          <p className="text-xl mb-6">
            The connector you're looking for doesn't exist or has been removed.
          </p>
          <Link href="/compliance-store/browse" className="inline-block bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded-lg">
            Browse All Connectors
          </Link>
        </div>
      </PageWithSidebar>
    );
  }

  // If connector is still loading, show loading state
  if (!connector) {
    return (
      <PageWithSidebar title="Loading..." sidebarItems={[
        { label: 'Back to Browse', href: '/compliance-store/browse' },
        { label: 'Compliance Store Home', href: '/compliance-store' }
      ]}>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </PageWithSidebar>
    );
  }

  // Define sidebar items for the connector detail page
  const sidebarItems = [
    { type: 'category', label: connector.name, items: [
      { label: 'Overview', href: '#overview' },
      { label: 'Features', href: '#features' },
      { label: 'Pricing', href: '#pricing' },
      { label: 'Integration', href: '#integration' },
      ...(isInstalled ? [{ label: 'Execute', href: '#execution' }] : []),
      { label: 'Support', href: '#support' }
    ]},
    { type: 'category', label: 'Navigation', items: [
      { label: 'Back to Browse', href: '/compliance-store/browse' },
      { label: 'Compliance Store Home', href: '/compliance-store' }
    ]}
  ];

  // SEO metadata
  const pageProps = {
    title: `${connector.name} - NovaFuse Compliance App Store`,
    description: connector.description,
    keywords: `${connector.framework} compliance, ${connector.category.replace('-', ' ')}, ${connector.name}, NovaFuse Compliance App Store`,
    canonical: `https://novafuse.io/compliance-store/connectors/${connector.id}`,
    ogImage: `/images/connectors/${connector.id}-og-image.png`
  };

  return (
    <PageWithSidebar title={pageProps.title} sidebarItems={sidebarItems} {...pageProps}>
      {/* Connector Header */}
      <div id="overview" className="bg-secondary rounded-lg p-8 mb-8">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <div>
            <div className="flex items-center mb-4">
              <div className="bg-blue-600 w-16 h-16 rounded-lg flex items-center justify-center text-white text-2xl font-bold mr-4">
                {connector.framework.toUpperCase().substring(0, 4)}
              </div>
              <div>
                <h1 className="text-3xl font-bold">{connector.name}</h1>
                <p className="text-gray-400">by {connector.vendor}</p>
              </div>
            </div>

            <div className="flex flex-wrap gap-2 mb-4">
              <span className="bg-blue-600 text-xs px-2 py-1 rounded-full">{connector.framework.toUpperCase()}</span>
              <span className="bg-gray-700 text-xs px-2 py-1 rounded-full">{connector.category.replace('-', ' ')}</span>
            </div>
          </div>

          <div className="mt-4 md:mt-0">
            <div className="flex items-center mb-2">
              <div className="flex text-yellow-400 mr-2">
                {'★'.repeat(Math.floor(connector.rating))}
                {connector.rating % 1 !== 0 ? '½' : ''}
                {'☆'.repeat(5 - Math.ceil(connector.rating))}
              </div>
              <span>({connector.reviewCount} reviews)</span>
            </div>
            <div className="text-green-400 font-bold text-xl">{connector.price}</div>
          </div>
        </div>

        <p className="mt-4 text-lg">{connector.longDescription}</p>
      </div>

      {/* Coming Soon Notice */}
      <div className="bg-blue-900 bg-opacity-30 border border-blue-700 rounded-lg p-6 mb-8">
        <h2 className="text-xl font-bold mb-2">Coming Soon</h2>
        <p>
          This connector detail page is a preview. The full functionality, including one-click deployment,
          will be available when the Compliance App Store launches.
        </p>
      </div>

      {/* Features Section */}
      <div id="features" className="mb-8">
        <h2 className="text-2xl font-bold mb-4">Key Features</h2>
        <div className="bg-secondary rounded-lg p-6">
          <ul className="space-y-3">
            {connector.features.map((feature, index) => (
              <li key={index} className="flex items-start">
                <span className="text-green-400 mr-2">✓</span>
                <span>{feature}</span>
              </li>
            ))}
          </ul>
        </div>
      </div>

      {/* Pricing Section */}
      <div id="pricing" className="mb-8">
        <h2 className="text-2xl font-bold mb-4">Pricing</h2>
        <div className="bg-secondary rounded-lg p-6">
          <div className="text-center mb-4">
            <span className="text-3xl font-bold text-green-400">{connector.price}</span>
            <span className="text-gray-400 ml-2">per month</span>
          </div>
          <p className="text-center mb-4">
            Includes all features, unlimited users, and premium support.
          </p>

          {installError && (
            <div className="bg-red-900 bg-opacity-30 border border-red-700 rounded-lg p-4 mb-4 text-red-400">
              {installError}
            </div>
          )}

          {isInstalled ? (
            <div className="space-y-4">
              <div className="bg-green-900 bg-opacity-30 border border-green-700 rounded-lg p-4 mb-4">
                <p className="text-green-400 font-semibold">✓ Connector Installed</p>
              </div>

              <div className="flex flex-col md:flex-row gap-4">
                <button
                  className="border border-red-600 text-red-500 hover:bg-red-900 hover:bg-opacity-20 font-bold py-2 px-4 rounded-lg flex-1"
                  onClick={uninstallConnector}
                  disabled={isInstalling}
                >
                  {isInstalling ? (
                    <span className="flex items-center justify-center">
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Uninstalling...
                    </span>
                  ) : 'Uninstall Connector'}
                </button>
              </div>
            </div>
          ) : (
            <div className="text-center">
              <button
                className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded-lg"
                onClick={installConnector}
                disabled={isInstalling}
              >
                {isInstalling ? (
                  <span className="flex items-center justify-center">
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Installing...
                  </span>
                ) : 'Install Connector'}
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Integration Section */}
      <div id="integration" className="mb-8">
        <h2 className="text-2xl font-bold mb-4">Integrations</h2>
        <div className="bg-secondary rounded-lg p-6">
          <p className="mb-4">{connector.name} integrates seamlessly with:</p>
          <div className="flex flex-wrap gap-2">
            {connector.integrations.map((integration, index) => (
              <span key={index} className="bg-gray-700 text-sm px-3 py-1 rounded-full">
                {integration}
              </span>
            ))}
          </div>
        </div>
      </div>

      {/* Execution Section */}
      {isInstalled && (
        <div id="execution" className="mb-8">
          <h2 className="text-2xl font-bold mb-4">Execute Connector</h2>
          <ConnectorExecution
            connector={connector}
            onExecutionComplete={handleExecutionComplete}
          />
        </div>
      )}

      {/* Support Section */}
      <div id="support" className="mb-8">
        <h2 className="text-2xl font-bold mb-4">Support</h2>
        <div className="bg-secondary rounded-lg p-6">
          <p className="mb-4">
            {connector.name} comes with comprehensive support to ensure your compliance journey is smooth and effective:
          </p>
          <ul className="space-y-3">
            <li className="flex items-start">
              <span className="text-green-400 mr-2">✓</span>
              <span>24/7 technical support</span>
            </li>
            <li className="flex items-start">
              <span className="text-green-400 mr-2">✓</span>
              <span>Implementation assistance</span>
            </li>
            <li className="flex items-start">
              <span className="text-green-400 mr-2">✓</span>
              <span>Regular compliance updates</span>
            </li>
            <li className="flex items-start">
              <span className="text-green-400 mr-2">✓</span>
              <span>Access to compliance experts</span>
            </li>
          </ul>
        </div>
      </div>

      {/* Call to Action */}
      <div className="bg-blue-900 rounded-lg p-8 text-center mb-12">
        <h2 className="text-2xl font-bold mb-4">Ready to simplify {connector.framework.toUpperCase()} compliance?</h2>
        <p className="mb-6">Get early access to {connector.name} and transform your compliance program.</p>
        <Link href="/contact" className="inline-block bg-white text-blue-700 hover:bg-gray-100 font-bold py-2 px-6 rounded-lg">
          Contact Sales
        </Link>
      </div>

      {/* Confidentiality Notice */}
      <div className="border border-blue-800 bg-blue-900 bg-opacity-20 rounded-lg p-4">
        <div className="flex items-start">
          <div className="text-yellow-400 mr-3 mt-1">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
          </div>
          <div>
            <p className="text-sm">
              <strong>CONFIDENTIAL:</strong> The NovaFuse Compliance App Store is currently under IP protection review.
              All content is considered confidential and proprietary. Unauthorized access or sharing is prohibited.
            </p>
          </div>
        </div>
      </div>
    </PageWithSidebar>
  );
}
