<!DOCTYPE html>
<html>
<head>
    <title>Mermaid Diagram Conversion</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        .diagram-container {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            page-break-inside: avoid;
        }
        .diagram-title {
            font-weight: bold;
            margin-bottom: 10px;
        }
        .mermaid {
            background: white;
            padding: 20px;
        }
    </style>
</head>
<body>
    <!-- Diagram 1: Entropy & Coherence Systems -->
    <div class="diagram-container">
        <div class="diagram-title">Entropy & Coherence Systems</div>
        <div class="mermaid">
            graph TD
                %% Title and Description
                classDef titleStyle fill:#f8f8f8,stroke:#333,stroke-width:1px,font-weight:bold,text-align:center;
                classDef component fill:#ffffff,stroke:#666666,stroke-width:2px,color:#000000;
                classDef process fill:#e0e0e0,stroke:#888888,stroke-width:2px;
                classDef system fill:#c0c0c0,stroke:#555555,stroke-width:2px;
                classDef interface fill:#f0f0f0,stroke:#888888,stroke-width:2px;
                
                %% Title
                title[FIG. X: Entropy & Coherence Systems]
                class title titleStyle;
                
                %% Main Components
                subgraph "Entropy Reduction System"
                    style Entropy_Reduction_System fill:#f0f0f0,stroke:#000000,stroke-width:1.5px,color:#000000
                    A[∂Ψ Entropy Reduction Feedback Loop] -->|Monitors| B[System State]
                    B -->|Feeds| C[Real-Time Coherence Scoring]
                    C -->|Adjusts| D[Psi-Field Calibration]
                    D -->|Optimizes| A
                end
                
                subgraph "Quantum Coherence Interface"
                    style Quantum_Coherence_Interface fill:#f0f0f0,stroke:#000000,stroke-width:1.5px,color:#000000
                    E[Quantum State Analyzer] -->|Measures| F[Quantum Decoherence]
                    F -->|Feeds| G[Coherence Optimization Engine]
                    G -->|Adjusts| H[Quantum Control Parameters]
                    H -->|Stabilizes| E
                end
                
                %% Connections between systems
                C -.->|Scores| F
                G -.->|Enhances| D
                
                %% Component Details
                A -->|Processes| A1[Entropy Metrics]
                A -->|Outputs| A2[Reduction Signals]
                
                C -->|Uses| C1[ML-based Scoring Model]
                C -->|Generates| C2[Coherence Index]
                
                D -->|Controls| D1[Field Generators]
                D -->|Monitors| D2[Environmental Factors]
                
                E -->|Tracks| E1[Qubit States]
                E -->|Detects| E2[Decoherence Events]
                
                G -->|Implements| G1[Error Correction]
                G -->|Optimizes| G2[Qubit Coupling]
                
                %% Styling
                class A,B,C,D process;
                class E,F,G,H system;
                class A1,A2,C1,C2,D1,D2,E1,E2,G1,G2 component;
        </div>
    </div>

    <!-- Diagram 2: AI Alignment Case -->
    <div class="diagram-container">
        <div class="diagram-title">AI Alignment Case Study</div>
        <div class="mermaid">
            graph TD
                %% Title and Description
                classDef titleStyle fill:#f8f8f8,stroke:#333,stroke-width:1px,font-weight:bold,text-align:center;
                classDef component fill:#ffffff,stroke:#666666,stroke-width:2px,color:#000000;
                classDef process fill:#e0e0e0,stroke:#888888,stroke-width:2px;
                classDef system fill:#c0c0c0,stroke:#555555,stroke-width:2px;
                classDef interface fill:#f0f0f0,stroke:#888888,stroke-width:2px;
                
                %% Title
                title[FIG. X: AI Alignment Case Study - NovaFuse Alignment Framework]
                class title titleStyle;
                
                %% Main Components
                subgraph "NovaFuse Alignment Stack"
                    style NovaFuse_Alignment_Stack fill:#f0f0f0,stroke:#000000,stroke-width:1.5px,color:#000000
                    A[Ethical Framework Layer] -->|Feeds| B[Alignment Constraints]
                    B -->|Guides| C[Reward Shaping]
                    C -->|Influences| D[Policy Optimization]
                    
                    E[Human Values Interface] -->|Provides| F[Value Proxies]
                    F -->|Trains| G[Value Model]
                    G -->|Updates| H[Alignment Parameters]
                    H -->|Adjusts| B
                end
                
                subgraph "AI System"
                    style AI_System fill:#f0f0f0,stroke:#000000,stroke-width:1.5px,color:#000000
                    I[AI Model] <-->|Interacts| J[Environment]
                    I -->|Receives| K[Alignment Signals]
                    K <-->|From| C
                    I -->|Outputs| L[Actions]
                    L -->|Impacts| J
                end
                
                %% Feedback Loops
                J -->|State| M[Impact Assessment]
                M -->|Feedback| N[Alignment Monitor]
                N -->|Adjusts| H
                
                %% Component Details
                A -->|Includes| A1[Ethical Principles]
                A -->|Implements| A2[Safety Constraints]
                
                E -->|Collects| E1[Human Preferences]
                E -->|Processes| E2[Value Elicitation]
                
                I -->|Uses| I1[Reinforcement Learning]
                I -->|Maintains| I2[Uncertainty Estimates]
                
                M -->|Measures| M1[Value Alignment]
                M -->|Tracks| M2[Behavior Drift]
                
                %% Styling
                class A,B,C,D,E,F,G,H process;
                class I,J,K,L system;
                class A1,A2,E1,E2,I1,I2,M1,M2 component;
                class M,N interface;
        </div>
    </div>

    <!-- Diagram 3: Quantum Decoherence Elimination -->
    <div class="diagram-container">
        <div class="diagram-title">Quantum Decoherence Elimination</div>
        <div class="mermaid">
            graph TD
                %% Title and Description
                classDef titleStyle fill:#f8f8f8,stroke:#333,stroke-width:1px,font-weight:bold,text-align:center;
                classDef component fill:#ffffff,stroke:#666666,stroke-width:2px,color:#000000;
                classDef process fill:#e0e0e0,stroke:#888888,stroke-width:2px;
                classDef system fill:#c0c0c0,stroke:#555555,stroke-width:2px;
                classDef interface fill:#f0f0f0,stroke:#888888,stroke-width:2px;
                
                %% Title
                title[FIG. X: Quantum Decoherence Elimination System]
                class title titleStyle;
                
                %% Main Components
                subgraph "Decoherence Detection"
                    style Decoherence_Detection fill:#f0f0f0,stroke:#000000,stroke-width:1.5px,color:#000000
                    A[Qubit State Monitor] -->|Tracks| B[Quantum State Fidelity]
                    B -->|Feeds| C[Decoherence Predictor]
                    C -->|Alerts| D[Error Correction Trigger]
                end
                
                subgraph "Coherence Maintenance"
                    style Coherence_Maintenance fill:#f0f0f0,stroke:#000000,stroke-width:1.5px,color:#000000
                    E[Dynamic Field Generator] -->|Applies| F[Stabilizing Fields]
                    F -->|Maintains| G[Quantum Coherence]
                    G -->|Enables| H[Extended Qubit Lifetime]
                    
                    I[Error Correction Codec] <-->|Protects| J[Quantum Data]
                    J <-->|Processes| K[Quantum Algorithm]
                end
                
                %% Connections between systems
                D -->|Activates| E
                D -->|Triggers| I
                
                %% Feedback Loops
                H -->|Extends| L[Computation Window]
                L -->|Allows| M[More Complex Algorithms]
                M -->|Generates| N[Better Error Models]
                N -->|Improves| C
                
                %% Component Details
                A -->|Monitors| A1[Qubit States]
                A -->|Detects| A2[Phase Drift]
                
                C -->|Uses| C1[ML Predictions]
                C -->|Analyzes| C2[Environmental Noise]
                
                E -->|Controls| E1[Field Parameters]
                E -->|Adjusts| E2[Field Frequencies]
                
                I -->|Implements| I1[Surface Codes]
                I -->|Corrects| I2[Bit/Phase Flips]
                
                %% Styling
                class A,B,C,D process;
                class E,F,G,H,I,J,K system;
                class A1,A2,C1,C2,E1,E2,I1,I2 component;
                class L,M,N interface;
        </div>
    </div>

    <script>
        // Initialize Mermaid with configuration
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            securityLevel: 'loose',
            fontFamily: 'Arial, sans-serif',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true,
                curve: 'basis'
            },
            // Use black and white for patent compliance
            themeVariables: {
                // USPTO-compliant grayscale theme
                primaryColor: '#ffffff',
                primaryTextColor: '#000000',
                primaryBorderColor: '#000000',
                lineColor: '#000000',
                secondaryColor: '#f0f0f0',
                tertiaryColor: '#e0e0e0',
                // Text colors (all black for maximum contrast)
                textColor: '#000000',
                // Line/border colors (all black for maximum contrast)
                lineColor: '#000000',
                border1: '#000000',
                border2: '#000000',
                // Fill colors (grayscale only)
                fill1: '#ffffff',  // white
                fill2: '#f0f0f0',  // very light gray
                fill3: '#e0e0e0',  // light gray
                // Text (all black for maximum readability)
                text1: '#000000',
                text2: '#000000',
                text3: '#000000'
            }
        });

        // Function to download SVG
        function downloadSVG(svg, filename) {
            const serializer = new XMLSerializer();
            let source = serializer.serializeToString(svg);
            
            // Add namespaces if not present
            if(!source.match(/^<svg[^>]+xmlns="http\:\/\/www\.w3\.org\/2000\/svg"/)){
                source = source.replace(/^<svg/, '<svg xmlns="http://www.w3.org/2000/svg"');
            }
            if(!source.match(/^<svg[^>]+"http\:\/\/www\.w3\.org\/1999\/xlink"/)){
                source = source.replace(/^<svg/, '<svg xmlns:xlink="http://www.w3.org/1999/xlink"');
            }
            
            // Add XML declaration
            source = '<?xml version="1.0" standalone="no"?>\r\n' + source;
            
            // Convert SVG source to URI data scheme
            const url = "data:image/svg+xml;charset=utf-8," + encodeURIComponent(source);
            
            // Create download link
            const downloadLink = document.createElement("a");
            downloadLink.href = url;
            downloadLink.download = filename;
            document.body.appendChild(downloadLink);
            downloadLink.click();
            document.body.removeChild(downloadLink);
        }

        // After Mermaid renders the diagrams, add download buttons
        document.addEventListener('DOMContentLoaded', function() {
            // Wait for Mermaid to finish rendering
            setTimeout(() => {
                const diagrams = document.querySelectorAll('.mermaid');
                diagrams.forEach((diagram, index) => {
                    const svg = diagram.querySelector('svg');
                    if (svg) {
                        const container = diagram.closest('.diagram-container');
                        const title = container.querySelector('.diagram-title').textContent;
                        const filename = `diagram_${index + 1}_${title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.svg`;
                        
                        const btn = document.createElement('button');
                        btn.textContent = 'Download SVG';
                        btn.style.marginTop = '10px';
                        btn.onclick = () => downloadSVG(svg, filename);
                        
                        container.appendChild(btn);
                        container.appendChild(document.createElement('br'));
                        
                        // Add a button to copy the SVG to clipboard
                        const copyBtn = document.createElement('button');
                        copyBtn.textContent = 'Copy SVG to Clipboard';
                        copyBtn.style.marginTop = '5px';
                        copyBtn.onclick = () => {
                            const serializer = new XMLSerializer();
                            let source = serializer.serializeToString(svg);
                            navigator.clipboard.writeText(source).then(() => {
                                alert('SVG copied to clipboard!');
                            });
                        };
                        
                        container.appendChild(copyBtn);
                    }
                });
            }, 1000); // Give Mermaid time to render
        });
    </script>
</body>
</html>
