import React from 'react';
import Link from 'next/link';
import Sidebar from '../components/Sidebar';

export default function UACDemo() {
  const sidebarItems = [
    { type: 'category', label: 'UAC Demo', items: [
      { label: 'Overview', href: '#overview' },
      { label: 'Features', href: '#features' },
      { label: 'Demo Access', href: '#demo-access' },
      { label: 'Use Cases', href: '#use-cases' }
    ]},
    { type: 'category', label: 'Related Pages', items: [
      { label: 'API Docs', href: '/api-docs' },
      { label: 'Partner Ecosystem', href: '/partner-ecosystem' }
    ]}
  ];

  return (
    <div className="flex flex-col md:flex-row gap-8">
      {/* Sidebar - Hidden on mobile, shown on desktop */}
      <div className="hidden md:block md:w-1/4 lg:w-1/5">
        <div className="sticky top-4">
          <Sidebar items={sidebarItems} title="UAC Demo" />
        </div>
      </div>

      {/* Mobile Sidebar Toggle - Shown on mobile only */}
      <div className="md:hidden mb-4">
        <select 
          className="w-full bg-secondary text-white border border-gray-700 rounded p-2"
          onChange={(e) => {
            if (e.target.value) window.location.href = e.target.value;
          }}
          defaultValue=""
        >
          <option value="" disabled>Navigate to...</option>
          {sidebarItems.map((item, index) => (
            item.type === 'category' ? (
              <optgroup key={index} label={item.label}>
                {item.items.map((subItem, subIndex) => (
                  <option key={`${index}-${subIndex}`} value={subItem.href}>
                    {subItem.label}
                  </option>
                ))}
              </optgroup>
            ) : (
              <option key={index} value={item.href}>
                {item.label}
              </option>
            )
          ))}
        </select>
      </div>

      {/* Main Content */}
      <div className="w-full md:w-3/4 lg:w-4/5">
        {/* Hero Section */}
        <div id="overview" className="accent-bg text-white rounded-lg p-8 mb-8">
          <div className="flex justify-center mb-4">
            <div className="bg-blue-800 bg-opacity-70 px-4 py-2 rounded-full text-sm font-bold inline-flex items-center">
              <span className="mr-2">🔌</span>
              <span>4 PATENTS PENDING</span>
            </div>
          </div>
          
          <h2 className="text-3xl md:text-4xl font-bold mb-4 text-center">Universal API Connector (UAC)</h2>
          
          <p className="text-xl mb-6 text-center max-w-3xl mx-auto">
            The world's most versatile API connector with built-in compliance intelligence.
            GRC is just our first use case - the UAC has unlimited potential applications.
          </p>
          
          <div className="flex flex-wrap justify-center gap-4">
            <a href="#demo-access" className="bg-white text-blue-600 px-6 py-3 rounded-lg font-bold hover:bg-blue-50 inline-block">
              Try the Demo
            </a>
            <Link href="/api-docs" className="border border-white text-white px-6 py-3 rounded-lg font-bold hover:bg-blue-900 hover:bg-opacity-20 inline-block">
              View API Docs
            </Link>
          </div>
        </div>

        {/* Features Section */}
        <div id="features" className="mb-12">
          <h2 className="text-2xl font-bold mb-6">Key Features</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-secondary p-6 rounded-lg">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 rounded-full bg-blue-900 flex items-center justify-center mr-4">
                  <span className="text-2xl">🔄</span>
                </div>
                <h3 className="text-xl font-semibold">Universal Connectivity</h3>
              </div>
              <p className="text-gray-300">
                Connect to any API regardless of type (REST, GraphQL, SOAP, gRPC) with a unified interface.
                Transform data between formats seamlessly.
              </p>
            </div>
            
            <div className="bg-secondary p-6 rounded-lg">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 rounded-full bg-blue-900 flex items-center justify-center mr-4">
                  <span className="text-2xl">🛡️</span>
                </div>
                <h3 className="text-xl font-semibold">Compliance Engine</h3>
              </div>
              <p className="text-gray-300">
                Apply compliance rules to API data in real-time. Support for HIPAA, GDPR, PCI DSS, SOC2, ISO27001, and more.
                Automatic data classification and validation.
              </p>
            </div>
            
            <div className="bg-secondary p-6 rounded-lg">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 rounded-full bg-blue-900 flex items-center justify-center mr-4">
                  <span className="text-2xl">🔐</span>
                </div>
                <h3 className="text-xl font-semibold">Security First</h3>
              </div>
              <p className="text-gray-300">
                Enterprise-grade security with role-based access control, audit logging, and encryption.
                Support for various authentication methods including OAuth, JWT, and API keys.
              </p>
            </div>
          </div>
        </div>

        {/* Demo Access Section */}
        <div id="demo-access" className="mb-12">
          <h2 className="text-2xl font-bold mb-6">Try the UAC Demo</h2>
          
          <div className="bg-secondary p-6 rounded-lg">
            <p className="text-gray-300 mb-6">
              Experience the power of the Universal API Connector with our interactive demo.
              Test compliance checks, connect to APIs, and see the UAC in action.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-xl font-semibold mb-4">Demo Features</h3>
                <ul className="space-y-2 text-gray-300">
                  <li className="flex items-start">
                    <span className="text-blue-400 mr-2">✓</span>
                    <span>HIPAA, GDPR, and PCI DSS compliance checks</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-blue-400 mr-2">✓</span>
                    <span>API connection and data transformation</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-blue-400 mr-2">✓</span>
                    <span>Multi-API orchestration</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-blue-400 mr-2">✓</span>
                    <span>Real-time compliance monitoring</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-blue-400 mr-2">✓</span>
                    <span>Interactive API documentation</span>
                  </li>
                </ul>
              </div>
              
              <div>
                <h3 className="text-xl font-semibold mb-4">Access the Demo</h3>
                <p className="text-gray-300 mb-4">
                  The UAC demo is available for partners and qualified prospects.
                  Request access to try it yourself.
                </p>
                <div className="space-y-4">
                  <a href="http://localhost:3030" target="_blank" rel="noopener noreferrer" className="accent-bg text-white px-6 py-3 rounded-lg font-bold hover:bg-blue-700 inline-block w-full text-center">
                    Launch Demo
                  </a>
                  <Link href="/partner-onboarding" className="border border-gray-600 text-white px-6 py-3 rounded-lg font-bold hover:bg-gray-800 inline-block w-full text-center">
                    Request Partner Access
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Use Cases Section */}
        <div id="use-cases" className="mb-12">
          <h2 className="text-2xl font-bold mb-6">Beyond GRC: Unlimited Applications</h2>
          
          <div className="bg-secondary p-6 rounded-lg">
            <p className="text-gray-300 mb-6">
              While the UAC excels at GRC use cases, its potential applications extend far beyond compliance.
              Here are just a few examples of what you can build with the Universal API Connector:
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="bg-gray-800 p-4 rounded-lg">
                <h4 className="text-lg font-semibold mb-2">Healthcare Interoperability</h4>
                <p className="text-gray-300 text-sm">
                  Connect EHRs, HIEs, and healthcare apps with HIPAA-compliant data exchange.
                  Support for FHIR, HL7, and other healthcare standards.
                </p>
              </div>
              
              <div className="bg-gray-800 p-4 rounded-lg">
                <h4 className="text-lg font-semibold mb-2">Financial Data Aggregation</h4>
                <p className="text-gray-300 text-sm">
                  Securely connect to banking, investment, and payment APIs with PCI DSS and SOX compliance.
                  Real-time transaction monitoring and fraud detection.
                </p>
              </div>
              
              <div className="bg-gray-800 p-4 rounded-lg">
                <h4 className="text-lg font-semibold mb-2">IoT Data Management</h4>
                <p className="text-gray-300 text-sm">
                  Connect to IoT devices and platforms with secure data collection and processing.
                  Apply compliance rules to IoT data streams in real-time.
                </p>
              </div>
              
              <div className="bg-gray-800 p-4 rounded-lg">
                <h4 className="text-lg font-semibold mb-2">Supply Chain Integration</h4>
                <p className="text-gray-300 text-sm">
                  Connect to supplier, logistics, and inventory systems with secure data exchange.
                  Ensure compliance with industry regulations and standards.
                </p>
              </div>
              
              <div className="bg-gray-800 p-4 rounded-lg">
                <h4 className="text-lg font-semibold mb-2">Customer Data Platform</h4>
                <p className="text-gray-300 text-sm">
                  Aggregate customer data from multiple sources with GDPR and CCPA compliance.
                  Create a unified customer view while maintaining privacy and security.
                </p>
              </div>
              
              <div className="bg-gray-800 p-4 rounded-lg">
                <h4 className="text-lg font-semibold mb-2">AI/ML Data Pipeline</h4>
                <p className="text-gray-300 text-sm">
                  Connect to data sources and AI/ML platforms with secure data processing.
                  Ensure compliance with AI ethics guidelines and data protection regulations.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="bg-secondary p-6 rounded-lg text-center">
          <h2 className="text-2xl font-bold mb-4">Ready to Experience the UAC?</h2>
          <p className="text-gray-300 mb-6 max-w-3xl mx-auto">
            Join our partner ecosystem and be among the first to leverage the power of the Universal API Connector.
            Transform how you connect, integrate, and comply with regulations.
          </p>
          <div className="flex flex-wrap justify-center gap-4">
            <Link href="/partner-onboarding" className="accent-bg text-white px-6 py-3 rounded-lg font-bold hover:bg-blue-700 inline-block">
              Become a Partner
            </Link>
            <a href="http://localhost:3030/api-docs" target="_blank" rel="noopener noreferrer" className="border border-gray-600 text-white px-6 py-3 rounded-lg font-bold hover:bg-gray-800 inline-block">
              View API Documentation
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
