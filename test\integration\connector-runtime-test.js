/**
 * NovaFuse Universal API Connector - Connector Runtime Integration Tests
 * 
 * This module tests the connector runtime service.
 */

const {
  createTestSuite,
  assertTrue,
  assertFalse,
  assertEqual,
  assertNotEqual,
  assertDefined,
  assertNull,
  assertNotNull,
  assertThrows,
  assertDoesNotThrow
} = require('./test-framework');

const connectorRegistryService = require('../../src/connectors/services/connector-registry');
const connectorConfigService = require('../../src/connectors/services/connector-config');
const connectorRuntimeService = require('../../src/connectors/services/connector-runtime');

// Create test suite
const suite = createTestSuite('Connector Runtime Integration Tests');

// Test data
let testConnector = null;
let testConfig = null;
let testExecution = null;

// Setup and teardown
suite.beforeAll(async () => {
  // Clear any existing connectors, configs, and executions
  connectorRegistryService.connectors.clear();
  connectorConfigService.configs.clear();
  connectorRuntimeService.activeExecutions.clear();
  
  // Create a test connector
  testConnector = await connectorRegistryService.createConnector({
    name: 'Test Connector',
    description: 'A test connector for integration testing',
    version: '1.0.0',
    type: 'source',
    status: 'draft',
    configSchema: {
      type: 'object',
      properties: {
        apiKey: { type: 'string' },
        endpoint: { type: 'string' }
      },
      required: ['apiKey']
    }
  });
  
  // Create a test configuration
  testConfig = await connectorConfigService.createConfiguration({
    name: 'Test Configuration',
    description: 'A test configuration for integration testing',
    connectorId: testConnector.id,
    values: {
      apiKey: 'test-api-key',
      endpoint: 'https://api.example.com'
    }
  });
});

suite.afterAll(async () => {
  // Clean up
  connectorRegistryService.connectors.clear();
  connectorConfigService.configs.clear();
  connectorRuntimeService.activeExecutions.clear();
});

// Tests
suite.test('should execute a connector', async () => {
  // Execute connector
  testExecution = await connectorRuntimeService.executeConnector(testConnector.id, testConfig.id, {
    timeout: 5000
  });
  
  // Assertions
  assertNotNull(testExecution, 'Execution should not be null');
  assertDefined(testExecution.executionId, 'Execution ID should be defined');
  assertEqual(testExecution.connectorId, testConnector.id, 'Execution connector ID should match');
  assertEqual(testExecution.configId, testConfig.id, 'Execution config ID should match');
  assertEqual(testExecution.status, 'success', 'Execution status should be success');
  assertNotNull(testExecution.result, 'Execution result should not be null');
  assertTrue(testExecution.result.success, 'Execution result should indicate success');
});

suite.test('should get execution status', async () => {
  // Get execution status
  const status = connectorRuntimeService.getExecutionStatus(testExecution.executionId);
  
  // Assertions
  assertNotNull(status, 'Status should not be null');
  assertEqual(status.executionId, testExecution.executionId, 'Status execution ID should match');
  assertEqual(status.connectorId, testConnector.id, 'Status connector ID should match');
  assertEqual(status.configId, testConfig.id, 'Status config ID should match');
  assertEqual(status.status, 'completed', 'Status should be completed');
});

suite.test('should get all executions', async () => {
  // Get all executions
  const executions = connectorRuntimeService.getAllExecutions();
  
  // Assertions
  assertNotNull(executions, 'Executions should not be null');
  assertTrue(Array.isArray(executions), 'Executions should be an array');
  assertTrue(executions.length > 0, 'Executions should not be empty');
  
  // Find our test execution
  const execution = executions.find(e => e.executionId === testExecution.executionId);
  assertNotNull(execution, 'Test execution should be in the list');
});

suite.test('should handle execution errors', async () => {
  // Try to execute with an invalid connector ID
  const invalidExecution = await connectorRuntimeService.executeConnector('invalid-id', testConfig.id, {
    timeout: 5000
  });
  
  // Assertions
  assertNotNull(invalidExecution, 'Execution should not be null');
  assertEqual(invalidExecution.status, 'error', 'Execution status should be error');
  assertNotNull(invalidExecution.error, 'Execution error should not be null');
  
  // Get execution status
  const status = connectorRuntimeService.getExecutionStatus(invalidExecution.executionId);
  
  // Assertions
  assertNotNull(status, 'Status should not be null');
  assertEqual(status.status, 'failed', 'Status should be failed');
});

suite.test('should handle unknown execution ID', async () => {
  // Get status for unknown execution ID
  const status = connectorRuntimeService.getExecutionStatus('unknown-id');
  
  // Assertions
  assertNotNull(status, 'Status should not be null');
  assertEqual(status.status, 'unknown', 'Status should be unknown');
  assertEqual(status.message, 'Execution not found', 'Status message should indicate execution not found');
});

// Run the test suite
async function runTests() {
  const results = await suite.run();
  
  if (results.failed > 0) {
    process.exit(1);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTests();
}

module.exports = {
  suite,
  runTests
};
