0722d4e7d87f5d6d5e586013bd43c3c1
/**
 * NovaConnect Encryption Service
 * 
 * FIPS 140-3 compliant encryption service for securing sensitive data
 * in NovaConnect. Supports key rotation, envelope encryption, and
 * integration with Google Cloud KMS.
 */

const crypto = require('crypto');
const {
  promisify
} = require('util');
const {
  KeyManagementServiceClient
} = require('@google-cloud/kms');

// Promisify crypto functions
const randomBytesAsync = promisify(crypto.randomBytes);
const pbkdf2Async = promisify(crypto.pbkdf2);
class EncryptionService {
  constructor(options = {}) {
    this.options = {
      algorithm: 'aes-256-gcm',
      keyLength: 32,
      // 256 bits
      ivLength: 16,
      // 128 bits
      saltLength: 32,
      // 256 bits
      iterations: 100000,
      digest: 'sha256',
      tagLength: 16,
      // 128 bits
      useGcpKms: false,
      ...options
    };

    // Initialize key cache
    this.keyCache = new Map();

    // Initialize GCP KMS client if enabled
    if (this.options.useGcpKms) {
      this._initializeKmsClient();
    }

    // Initialize metrics
    this.metrics = {
      encryptionOperations: 0,
      decryptionOperations: 0,
      keyRotations: 0,
      totalEncryptionTime: 0,
      totalDecryptionTime: 0,
      averageEncryptionTime: 0,
      averageDecryptionTime: 0
    };
  }

  /**
   * Initialize GCP KMS client
   * @private
   */
  _initializeKmsClient() {
    try {
      this.kmsClient = new KeyManagementServiceClient({
        credentials: this.options.gcpCredentials,
        projectId: this.options.gcpProjectId
      });
      this.kmsKeyName = this.options.kmsKeyName;
    } catch (error) {
      console.error('Error initializing KMS client:', error);
      throw error;
    }
  }

  /**
   * Generate a new encryption key
   * @returns {Object} - Key information
   */
  async generateKey() {
    const startTime = Date.now();
    try {
      // Generate a random key
      const key = await randomBytesAsync(this.options.keyLength);

      // Generate a unique key ID
      const keyId = crypto.createHash('sha256').update(key).update(Date.now().toString()).digest('hex');

      // Store key in cache
      this.keyCache.set(keyId, {
        key,
        createdAt: new Date().toISOString(),
        algorithm: this.options.algorithm
      });
      return {
        keyId,
        algorithm: this.options.algorithm,
        createdAt: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error generating key:', error);
      throw error;
    } finally {
      const endTime = Date.now();
      const duration = endTime - startTime;

      // Update metrics
      this.metrics.keyRotations++;
      this.metrics.totalKeyRotationTime = (this.metrics.totalKeyRotationTime || 0) + duration;
      this.metrics.averageKeyRotationTime = this.metrics.totalKeyRotationTime / this.metrics.keyRotations;
    }
  }

  /**
   * Encrypt data using the specified key
   * @param {string|Buffer|Object} data - Data to encrypt
   * @param {string} keyId - Key ID to use for encryption
   * @returns {Object} - Encrypted data
   */
  async encrypt(data, keyId) {
    const startTime = Date.now();
    try {
      // Convert data to buffer if needed
      const dataBuffer = typeof data === 'object' && !(data instanceof Buffer) ? Buffer.from(JSON.stringify(data)) : Buffer.from(data);

      // Get the key
      const keyInfo = this.keyCache.get(keyId);
      if (!keyInfo) {
        throw new Error(`Key with ID ${keyId} not found`);
      }

      // Generate IV
      const iv = await randomBytesAsync(this.options.ivLength);

      // Create cipher
      const cipher = crypto.createCipheriv(this.options.algorithm, keyInfo.key, iv);

      // Encrypt data
      const encrypted = Buffer.concat([cipher.update(dataBuffer), cipher.final()]);

      // Get authentication tag
      const authTag = cipher.getAuthTag();

      // Create encrypted package
      const encryptedPackage = {
        keyId,
        iv: iv.toString('base64'),
        authTag: authTag.toString('base64'),
        data: encrypted.toString('base64'),
        algorithm: this.options.algorithm,
        encryptedAt: new Date().toISOString()
      };
      return encryptedPackage;
    } catch (error) {
      console.error('Error encrypting data:', error);
      throw error;
    } finally {
      const endTime = Date.now();
      const duration = endTime - startTime;

      // Update metrics
      this.metrics.encryptionOperations++;
      this.metrics.totalEncryptionTime += duration;
      this.metrics.averageEncryptionTime = this.metrics.totalEncryptionTime / this.metrics.encryptionOperations;
    }
  }

  /**
   * Decrypt data using the specified key
   * @param {Object} encryptedPackage - Encrypted data package
   * @returns {Buffer} - Decrypted data
   */
  async decrypt(encryptedPackage) {
    const startTime = Date.now();
    try {
      const {
        keyId,
        iv,
        authTag,
        data,
        algorithm
      } = encryptedPackage;

      // Get the key
      const keyInfo = this.keyCache.get(keyId);
      if (!keyInfo) {
        throw new Error(`Key with ID ${keyId} not found`);
      }

      // Convert base64 strings to buffers
      const ivBuffer = Buffer.from(iv, 'base64');
      const authTagBuffer = Buffer.from(authTag, 'base64');
      const encryptedBuffer = Buffer.from(data, 'base64');

      // Create decipher
      const decipher = crypto.createDecipheriv(algorithm || this.options.algorithm, keyInfo.key, ivBuffer);

      // Set auth tag
      decipher.setAuthTag(authTagBuffer);

      // Decrypt data
      const decrypted = Buffer.concat([decipher.update(encryptedBuffer), decipher.final()]);
      return decrypted;
    } catch (error) {
      console.error('Error decrypting data:', error);
      throw error;
    } finally {
      const endTime = Date.now();
      const duration = endTime - startTime;

      // Update metrics
      this.metrics.decryptionOperations++;
      this.metrics.totalDecryptionTime += duration;
      this.metrics.averageDecryptionTime = this.metrics.totalDecryptionTime / this.metrics.decryptionOperations;
    }
  }

  /**
   * Encrypt data with password-based encryption
   * @param {string|Buffer|Object} data - Data to encrypt
   * @param {string} password - Password to use for encryption
   * @returns {Object} - Encrypted data
   */
  async encryptWithPassword(data, password) {
    const startTime = Date.now();
    try {
      // Convert data to buffer if needed
      const dataBuffer = typeof data === 'object' && !(data instanceof Buffer) ? Buffer.from(JSON.stringify(data)) : Buffer.from(data);

      // Generate salt
      const salt = await randomBytesAsync(this.options.saltLength);

      // Derive key from password
      const key = await pbkdf2Async(password, salt, this.options.iterations, this.options.keyLength, this.options.digest);

      // Generate IV
      const iv = await randomBytesAsync(this.options.ivLength);

      // Create cipher
      const cipher = crypto.createCipheriv(this.options.algorithm, key, iv);

      // Encrypt data
      const encrypted = Buffer.concat([cipher.update(dataBuffer), cipher.final()]);

      // Get authentication tag
      const authTag = cipher.getAuthTag();

      // Create encrypted package
      const encryptedPackage = {
        salt: salt.toString('base64'),
        iv: iv.toString('base64'),
        authTag: authTag.toString('base64'),
        data: encrypted.toString('base64'),
        algorithm: this.options.algorithm,
        iterations: this.options.iterations,
        keyLength: this.options.keyLength,
        digest: this.options.digest,
        encryptedAt: new Date().toISOString()
      };
      return encryptedPackage;
    } catch (error) {
      console.error('Error encrypting data with password:', error);
      throw error;
    } finally {
      const endTime = Date.now();
      const duration = endTime - startTime;

      // Update metrics
      this.metrics.encryptionOperations++;
      this.metrics.totalEncryptionTime += duration;
      this.metrics.averageEncryptionTime = this.metrics.totalEncryptionTime / this.metrics.encryptionOperations;
    }
  }

  /**
   * Decrypt data with password-based encryption
   * @param {Object} encryptedPackage - Encrypted data package
   * @param {string} password - Password to use for decryption
   * @returns {Buffer} - Decrypted data
   */
  async decryptWithPassword(encryptedPackage, password) {
    const startTime = Date.now();
    try {
      const {
        salt,
        iv,
        authTag,
        data,
        algorithm,
        iterations,
        keyLength,
        digest
      } = encryptedPackage;

      // Convert base64 strings to buffers
      const saltBuffer = Buffer.from(salt, 'base64');
      const ivBuffer = Buffer.from(iv, 'base64');
      const authTagBuffer = Buffer.from(authTag, 'base64');
      const encryptedBuffer = Buffer.from(data, 'base64');

      // Derive key from password
      const key = await pbkdf2Async(password, saltBuffer, iterations || this.options.iterations, keyLength || this.options.keyLength, digest || this.options.digest);

      // Create decipher
      const decipher = crypto.createDecipheriv(algorithm || this.options.algorithm, key, ivBuffer);

      // Set auth tag
      decipher.setAuthTag(authTagBuffer);

      // Decrypt data
      const decrypted = Buffer.concat([decipher.update(encryptedBuffer), decipher.final()]);
      return decrypted;
    } catch (error) {
      console.error('Error decrypting data with password:', error);
      throw error;
    } finally {
      const endTime = Date.now();
      const duration = endTime - startTime;

      // Update metrics
      this.metrics.decryptionOperations++;
      this.metrics.totalDecryptionTime += duration;
      this.metrics.averageDecryptionTime = this.metrics.totalDecryptionTime / this.metrics.decryptionOperations;
    }
  }

  /**
   * Encrypt data using Google Cloud KMS
   * @param {string|Buffer|Object} data - Data to encrypt
   * @returns {Object} - Encrypted data
   */
  async encryptWithKms(data) {
    if (!this.options.useGcpKms) {
      throw new Error('GCP KMS is not enabled');
    }
    const startTime = Date.now();
    try {
      // Convert data to buffer if needed
      const dataBuffer = typeof data === 'object' && !(data instanceof Buffer) ? Buffer.from(JSON.stringify(data)) : Buffer.from(data);

      // Encrypt with KMS
      const [encryptResponse] = await this.kmsClient.encrypt({
        name: this.kmsKeyName,
        plaintext: dataBuffer
      });

      // Create encrypted package
      const encryptedPackage = {
        data: encryptResponse.ciphertext.toString('base64'),
        kmsKeyName: this.kmsKeyName,
        encryptedAt: new Date().toISOString()
      };
      return encryptedPackage;
    } catch (error) {
      console.error('Error encrypting data with KMS:', error);
      throw error;
    } finally {
      const endTime = Date.now();
      const duration = endTime - startTime;

      // Update metrics
      this.metrics.encryptionOperations++;
      this.metrics.totalEncryptionTime += duration;
      this.metrics.averageEncryptionTime = this.metrics.totalEncryptionTime / this.metrics.encryptionOperations;
    }
  }

  /**
   * Decrypt data using Google Cloud KMS
   * @param {Object} encryptedPackage - Encrypted data package
   * @returns {Buffer} - Decrypted data
   */
  async decryptWithKms(encryptedPackage) {
    if (!this.options.useGcpKms) {
      throw new Error('GCP KMS is not enabled');
    }
    const startTime = Date.now();
    try {
      const {
        data,
        kmsKeyName
      } = encryptedPackage;

      // Convert base64 string to buffer
      const ciphertextBuffer = Buffer.from(data, 'base64');

      // Decrypt with KMS
      const [decryptResponse] = await this.kmsClient.decrypt({
        name: kmsKeyName || this.kmsKeyName,
        ciphertext: ciphertextBuffer
      });
      return decryptResponse.plaintext;
    } catch (error) {
      console.error('Error decrypting data with KMS:', error);
      throw error;
    } finally {
      const endTime = Date.now();
      const duration = endTime - startTime;

      // Update metrics
      this.metrics.decryptionOperations++;
      this.metrics.totalDecryptionTime += duration;
      this.metrics.averageDecryptionTime = this.metrics.totalDecryptionTime / this.metrics.decryptionOperations;
    }
  }

  /**
   * Rotate encryption keys
   * @returns {Object} - New key information
   */
  async rotateKeys() {
    // Generate a new key
    const newKey = await this.generateKey();

    // Mark old keys as rotated
    for (const [keyId, keyInfo] of this.keyCache.entries()) {
      if (keyId !== newKey.keyId) {
        keyInfo.rotatedAt = new Date().toISOString();
        keyInfo.replacedBy = newKey.keyId;
      }
    }
    return newKey;
  }

  /**
   * Get metrics for the encryption service
   * @returns {Object} - Metrics
   */
  getMetrics() {
    return this.metrics;
  }
}
module.exports = EncryptionService;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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