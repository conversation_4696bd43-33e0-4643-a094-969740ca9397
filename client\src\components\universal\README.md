# NovaFuse Universal UI

The NovaFuse Universal UI is a powerful system that automatically generates user interfaces from API schemas. This approach enables rapid development of compliant, consistent UIs across the NovaFuse platform.

## Key Features

- **API-Driven UI Generation**: Create UIs directly from API schemas
- **Compliance Enforcement**: Automatically enforce compliance rules based on frameworks
- **Role-Based Access Control**: Dynamically adapt UI based on user roles
- **Blockchain Verification**: Built-in support for blockchain-verified evidence
- **Audit Trail**: Comprehensive logging for compliance evidence
- **No-Code UI Creation**: Create and modify UIs without coding

## Components

### AutoForm

The `AutoForm` component automatically generates forms based on a schema:

```jsx
import AutoForm from '../components/AutoForm';

// Example schema
const schema = {
  fields: [
    {
      name: "email",
      label: "Email Address",
      type: "email",
      required: true,
      description: "We'll never share your email."
    },
    {
      name: "role",
      label: "Role",
      type: "select",
      options: [
        { label: "Admin", value: "admin" },
        { label: "User", value: "user" }
      ],
      required: true
    }
  ],
  submitLabel: "Create Account"
};

// Usage
<AutoForm 
  schema={schema} 
  onSubmit={handleSubmit} 
  initialValues={initialValues} 
/>
```

### ComplianceAutoForm

The `ComplianceAutoForm` component extends `AutoForm` with compliance enforcement:

```jsx
import ComplianceAutoForm from '../components/ComplianceAutoForm';

// Example schema with compliance mode
const schema = {
  entity: "PatientData",
  compliance_mode: "HIPAA",
  fields: [
    {
      name: "patientName",
      label: "Patient Name",
      type: "text",
      required: true,
      tags: ["phi"],
      description: "Full name of the patient"
    },
    // More fields...
  ],
  submitLabel: "Save Patient Data"
};

// Usage
<ComplianceAutoForm 
  schema={schema} 
  onSubmit={handleSubmit} 
  user={currentUser} 
/>
```

### ComplianceEnforcer

The `ComplianceEnforcer` component enforces compliance rules on schemas:

```jsx
import ComplianceEnforcer from '../components/ComplianceEnforcer';

// Usage
<ComplianceEnforcer 
  schema={schema} 
  user={currentUser} 
  onSchemaChange={handleSchemaChange} 
/>
```

### SchemaBuilder

The `SchemaBuilder` component provides a visual interface for creating schemas:

```jsx
import SchemaBuilder from '../components/SchemaBuilder';

// Usage
<SchemaBuilder 
  initialSchema={existingSchema} 
  onSave={handleSchemaSave} 
/>
```

## Schema Format

The Universal UI uses a simple, flexible schema format:

```json
{
  "entity": "User",
  "entityPlural": "Users",
  "apiEndpoint": "/api/v1/users",
  "compliance_mode": "GDPR",
  "fields": [
    {
      "name": "email",
      "label": "Email Address",
      "type": "email",
      "required": true,
      "description": "We'll never share your email.",
      "tags": ["pii"]
    },
    {
      "name": "password",
      "label": "Password",
      "type": "password",
      "required": true
    },
    {
      "name": "role",
      "label": "Role",
      "type": "select",
      "options": [
        { "label": "Admin", "value": "admin" },
        { "label": "User", "value": "user" }
      ],
      "required": true
    }
  ],
  "submitLabel": "Create Account"
}
```

## Compliance Rules

Compliance rules are defined in `compliance.json` and automatically applied based on the `compliance_mode` in the schema:

```json
{
  "auto_ui_rules": {
    "soc2": {
      "required_components": ["evidence_table", "control_coverage_meter"],
      "forbidden_actions": ["delete_evidence", "modify_timestamp"],
      "field_rules": {
        "evidence": {
          "verification": "blockchain",
          "retention": "7_years"
        }
      }
    },
    "hipaa": {
      "required_components": ["phi_access_log", "authorization_check"],
      "forbidden_actions": ["mass_export", "unencrypted_storage"],
      "field_rules": {
        "phi": {
          "encryption": "required",
          "masking": "partial",
          "access_logging": "detailed"
        }
      }
    }
  }
}
```

## Usage Examples

### Basic Form

```jsx
import AutoForm from '../components/AutoForm';

const userSchema = {
  fields: [
    { name: "name", label: "Full Name", type: "text", required: true },
    { name: "email", label: "Email", type: "email", required: true }
  ],
  submitLabel: "Save"
};

function UserForm() {
  const handleSubmit = (data) => {
    console.log('Form submitted:', data);
  };
  
  return <AutoForm schema={userSchema} onSubmit={handleSubmit} />;
}
```

### Compliance-Enforced Form

```jsx
import ComplianceAutoForm from '../components/ComplianceAutoForm';

const patientSchema = {
  entity: "Patient",
  compliance_mode: "HIPAA",
  fields: [
    { name: "name", label: "Patient Name", type: "text", required: true, tags: ["phi"] },
    { name: "dob", label: "Date of Birth", type: "date", required: true, tags: ["phi"] }
  ],
  submitLabel: "Save Patient"
};

function PatientForm() {
  const currentUser = { id: "user1", role: "Clinician" };
  
  const handleSubmit = (data) => {
    console.log('Patient data submitted:', data);
  };
  
  return (
    <ComplianceAutoForm 
      schema={patientSchema} 
      onSubmit={handleSubmit} 
      user={currentUser} 
    />
  );
}
```

## API Integration

The Universal UI can be integrated with any API that provides schema information:

```jsx
import { useState, useEffect } from 'react';
import ComplianceAutoForm from '../components/ComplianceAutoForm';

function DynamicForm({ entityType }) {
  const [schema, setSchema] = useState(null);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    // Fetch schema from API
    fetch(`/api/v1/schemas/${entityType}`)
      .then(response => response.json())
      .then(data => {
        setSchema(data);
        setLoading(false);
      })
      .catch(error => {
        console.error('Error fetching schema:', error);
        setLoading(false);
      });
  }, [entityType]);
  
  if (loading) return <div>Loading...</div>;
  if (!schema) return <div>No schema found</div>;
  
  return (
    <ComplianceAutoForm 
      schema={schema} 
      onSubmit={handleSubmit} 
      user={currentUser} 
    />
  );
}
```

## Benefits

- **Rapid Development**: Create UIs in seconds instead of days
- **Consistency**: Ensure consistent UI across the platform
- **Compliance**: Automatically enforce compliance rules
- **Maintainability**: Update UIs by changing schemas, not code
- **Flexibility**: Adapt to changing requirements without code changes

## Patent Pending

The NovaFuse Universal UI with Cyber-Safety compliance enforcement is patent pending. This innovative approach combines dynamic UI generation with automated compliance enforcement to create a powerful, flexible system for building compliant applications.
