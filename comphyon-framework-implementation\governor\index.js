/**
 * Governor
 * 
 * This module exports all components of the Governor system.
 * The Governor controls actions, responses, and policy enforcement.
 */

const ControlActionExecution = require('./control-action-execution');
const AutomatedResponseSystem = require('./automated-response-system');
const PolicyEnforcement = require('./policy-enforcement');

/**
 * Create a basic Governor system
 * @param {Object} options - Configuration options
 * @returns {Object} - Governor system components
 */
function createGovernorSystem(options = {}) {
  // Create components
  const controlActionExecution = new ControlActionExecution(options.controlActionExecutionOptions);
  const automatedResponseSystem = new AutomatedResponseSystem(controlActionExecution, options.automatedResponseSystemOptions);
  const policyEnforcement = new PolicyEnforcement(options.policyEnforcementOptions);
  
  return {
    controlActionExecution,
    automatedResponseSystem,
    policyEnforcement
  };
}

/**
 * Create an enhanced Governor system with integrated components
 * @param {Object} options - Configuration options
 * @param {Object} meter - Meter system
 * @returns {Object} - Enhanced Governor system
 */
function createEnhancedGovernorSystem(options = {}, meter = null) {
  // Create basic system
  const governorSystem = createGovernorSystem(options);
  
  // Set up event listeners for integration
  
  // When an action completes, update response action status
  governorSystem.controlActionExecution.on('action-completed', (data) => {
    if (data.context && data.context.response) {
      governorSystem.automatedResponseSystem.updateResponseActionStatus(
        data.context.response,
        data.executionId,
        'completed',
        data.result
      );
    }
  });
  
  governorSystem.controlActionExecution.on('action-failed', (data) => {
    if (data.context && data.context.response) {
      governorSystem.automatedResponseSystem.updateResponseActionStatus(
        data.context.response,
        data.executionId,
        'failed',
        data.error
      );
    }
  });
  
  governorSystem.controlActionExecution.on('action-timeout', (data) => {
    if (data.context && data.context.response) {
      governorSystem.automatedResponseSystem.updateResponseActionStatus(
        data.context.response,
        data.executionId,
        'timeout',
        'Action timed out'
      );
    }
  });
  
  governorSystem.controlActionExecution.on('action-cancelled', (data) => {
    if (data.context && data.context.response) {
      governorSystem.automatedResponseSystem.updateResponseActionStatus(
        data.context.response,
        data.executionId,
        'cancelled',
        data.reason
      );
    }
  });
  
  // When a policy violation occurs, trigger automated response
  governorSystem.policyEnforcement.on('policy-violation', (data) => {
    // Create alert from policy violation
    const alert = {
      id: `alert-policy-${data.violationId}`,
      domain: data.domain,
      level: 'warning', // Default level for policy violations
      entropyValue: 0.7, // Default entropy value for policy violations
      threshold: 0.6, // Default threshold for policy violations
      deviation: 0.1, // Default deviation for policy violations
      timestamp: data.timestamp,
      metadata: {
        source: 'policy-violation',
        policyId: data.policyId,
        policyName: data.policyName,
        ruleId: data.ruleId
      }
    };
    
    // Process alert
    governorSystem.automatedResponseSystem.processAlert(alert);
  });
  
  // Set up meter integration if provided
  if (meter) {
    _integrateMeter(governorSystem, meter, options);
  }
  
  // Add enhanced methods
  const enhancedSystem = {
    ...governorSystem,
    
    /**
     * Start all components
     * @returns {boolean} - Success status
     */
    start() {
      const caeStarted = governorSystem.controlActionExecution.start();
      const arsStarted = governorSystem.automatedResponseSystem.start();
      const peStarted = governorSystem.policyEnforcement.start();
      
      return caeStarted && arsStarted && peStarted;
    },
    
    /**
     * Stop all components
     * @returns {boolean} - Success status
     */
    stop() {
      const caeStopped = governorSystem.controlActionExecution.stop();
      const arsStopped = governorSystem.automatedResponseSystem.stop();
      const peStopped = governorSystem.policyEnforcement.stop();
      
      return caeStopped && arsStopped && peStopped;
    },
    
    /**
     * Get unified state from all components
     * @returns {Object} - Unified state
     */
    getUnifiedState() {
      return {
        actionQueue: governorSystem.controlActionExecution.getActionQueue(),
        runningActions: governorSystem.controlActionExecution.getRunningActions(),
        activeResponses: governorSystem.automatedResponseSystem.getActiveResponses(),
        policyViolations: governorSystem.policyEnforcement.getPolicyViolations(5),
        timestamp: Date.now()
      };
    },
    
    /**
     * Get unified metrics from all components
     * @returns {Object} - Unified metrics
     */
    getUnifiedMetrics() {
      return {
        controlActionExecution: governorSystem.controlActionExecution.getMetrics(),
        automatedResponseSystem: governorSystem.automatedResponseSystem.getMetrics(),
        policyEnforcement: governorSystem.policyEnforcement.getMetrics(),
        timestamp: Date.now()
      };
    },
    
    /**
     * Process alert
     * @param {Object} alert - Alert object
     * @returns {Object} - Processing result
     */
    processAlert(alert) {
      // Process alert with automated response system
      const responses = governorSystem.automatedResponseSystem.processAlert(alert);
      
      // Evaluate data against policies
      const violations = governorSystem.policyEnforcement.evaluateData(
        alert.domain,
        {
          alert,
          level: alert.level,
          entropyValue: alert.entropyValue
        },
        {
          source: 'alert',
          timestamp: alert.timestamp
        }
      );
      
      // Enforce policies for violations
      const actions = [];
      
      for (const violation of violations) {
        const enforcementActions = governorSystem.policyEnforcement.enforcePolicy(
          violation.policyId,
          alert.domain,
          {
            alert,
            level: alert.level,
            entropyValue: alert.entropyValue
          },
          {
            source: 'alert',
            violation,
            timestamp: alert.timestamp
          }
        );
        
        actions.push(...enforcementActions);
      }
      
      return {
        alert,
        responses,
        violations,
        actions,
        timestamp: Date.now()
      };
    },
    
    /**
     * Register control action
     * @param {Object} action - Action definition
     * @returns {Object} - Registered action
     */
    registerControlAction(action) {
      return governorSystem.controlActionExecution.registerAction(action);
    },
    
    /**
     * Register response rule
     * @param {Object} rule - Response rule
     * @returns {Object} - Registered rule
     */
    registerResponseRule(rule) {
      return governorSystem.automatedResponseSystem.registerRule(rule);
    },
    
    /**
     * Register policy
     * @param {Object} policy - Policy definition
     * @returns {Object} - Registered policy
     */
    registerPolicy(policy) {
      return governorSystem.policyEnforcement.registerPolicy(policy);
    },
    
    /**
     * Execute control action
     * @param {string} actionId - Action ID
     * @param {Object} parameters - Action parameters
     * @param {Object} context - Execution context
     * @returns {string} - Execution ID
     */
    executeAction(actionId, parameters, context) {
      return governorSystem.controlActionExecution.executeAction(actionId, parameters, context);
    },
    
    /**
     * Evaluate data against policies
     * @param {string} domain - Domain
     * @param {Object} data - Data to evaluate
     * @param {Object} context - Evaluation context
     * @returns {Array} - Policy violations
     */
    evaluateData(domain, data, context) {
      return governorSystem.policyEnforcement.evaluateData(domain, data, context);
    }
  };
  
  return enhancedSystem;
}

/**
 * Integrate Meter with Governor
 * @param {Object} governorSystem - Governor system
 * @param {Object} meter - Meter system
 * @param {Object} options - Configuration options
 * @private
 */
function _integrateMeter(governorSystem, meter, options = {}) {
  if (!meter) return;
  
  try {
    // Register standard control actions
    
    // Register entropy reduction action
    governorSystem.controlActionExecution.registerAction({
      id: 'action-reduce-entropy',
      name: 'Reduce Entropy',
      domains: ['universal', 'cyber', 'financial', 'biological'],
      description: 'Reduces entropy in the specified domain',
      handler: (parameters, context) => {
        const { domain, amount = 0.1 } = parameters;
        
        if (!domain || !['universal', 'cyber', 'financial', 'biological'].includes(domain)) {
          throw new Error('Valid domain is required');
        }
        
        // In a real implementation, this would take actions to reduce entropy
        // For now, just log the action
        console.log(`[ACTION] Reducing entropy in ${domain} domain by ${amount}`);
        
        return {
          domain,
          amount,
          success: true,
          timestamp: Date.now()
        };
      }
    });
    
    // Register system shutdown action
    governorSystem.controlActionExecution.registerAction({
      id: 'action-system-shutdown',
      name: 'System Shutdown',
      domains: ['universal'],
      description: 'Initiates a controlled system shutdown',
      handler: (parameters, context) => {
        const { reason = 'safety_measure' } = parameters;
        
        // In a real implementation, this would initiate a system shutdown
        // For now, just log the action
        console.log(`[ACTION] Initiating system shutdown: ${reason}`);
        
        return {
          reason,
          success: true,
          timestamp: Date.now()
        };
      }
    });
    
    // Register standard response rules
    
    // Register critical entropy response rule
    governorSystem.automatedResponseSystem.registerRule({
      id: 'rule-critical-entropy',
      name: 'Critical Entropy Response',
      domains: ['universal'],
      alertLevels: ['critical', 'emergency'],
      description: 'Responds to critical entropy levels',
      condition: (alert) => {
        return alert.level === 'critical' || alert.level === 'emergency';
      },
      actions: [
        {
          actionId: 'action-reduce-entropy',
          parameters: {
            domain: 'universal',
            amount: 0.2
          }
        }
      ]
    });
    
    // Register emergency shutdown rule
    governorSystem.automatedResponseSystem.registerRule({
      id: 'rule-emergency-shutdown',
      name: 'Emergency Shutdown',
      domains: ['universal'],
      alertLevels: ['emergency'],
      description: 'Initiates system shutdown in emergency situations',
      condition: (alert) => {
        return alert.level === 'emergency' && alert.entropyValue > 0.95;
      },
      actions: [
        {
          actionId: 'action-system-shutdown',
          parameters: {
            reason: 'emergency_entropy_level'
          }
        }
      ]
    });
    
    // Register standard policies
    
    // Register maximum entropy policy
    governorSystem.policyEnforcement.registerPolicy({
      id: 'policy-max-entropy',
      name: 'Maximum Entropy Policy',
      domains: ['universal', 'cyber', 'financial', 'biological'],
      description: 'Enforces maximum entropy levels',
      rules: [
        {
          id: 'rule-max-universal-entropy',
          condition: (data, context) => {
            return data.entropyValue > 0.9;
          },
          action: (data, context) => {
            // In a real implementation, this would take actions to reduce entropy
            // For now, just return the action
            return {
              type: 'reduce_entropy',
              domain: context.domain,
              amount: 0.2,
              timestamp: Date.now()
            };
          }
        }
      ]
    });
    
    // Listen for alerts from Meter
    if (meter.alertingSystem && typeof meter.alertingSystem.on === 'function') {
      meter.alertingSystem.on('alert-processed', (alert) => {
        // Process alert with Governor
        governorSystem.processAlert(alert);
      });
    }
    
    // Listen for threshold violations from Meter
    if (meter.thresholdManagement && typeof meter.thresholdManagement.on === 'function') {
      meter.thresholdManagement.on('threshold-evaluation', (data) => {
        if (data.thresholdLevel !== 'normal') {
          // Evaluate data against policies
          governorSystem.policyEnforcement.evaluateData(
            data.domain,
            {
              entropyValue: data.entropyValue,
              thresholdLevel: data.thresholdLevel,
              threshold: data.violatedThreshold
            },
            {
              source: 'threshold-violation',
              timestamp: data.timestamp
            }
          );
        }
      });
    }
  } catch (error) {
    if (options.enableLogging) {
      console.error(`Governor: Error integrating with Meter: ${error.message}`);
    }
  }
}

module.exports = {
  ControlActionExecution,
  AutomatedResponseSystem,
  PolicyEnforcement,
  createGovernorSystem,
  createEnhancedGovernorSystem
};
