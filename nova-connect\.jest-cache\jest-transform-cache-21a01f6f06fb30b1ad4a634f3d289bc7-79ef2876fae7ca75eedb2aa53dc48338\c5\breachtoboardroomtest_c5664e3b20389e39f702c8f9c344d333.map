{"version": 3, "names": ["TransformationEngine", "require", "RemediationEngine", "SCCConnector", "EncryptionService", "mockBigQueryService", "getDataset", "jest", "fn", "updateDataset", "encryptDataset", "getAccessLogs", "mockLookerService", "updateDashboard", "getComplianceScore", "describe", "transformationEngine", "remediationEngine", "sccConnector", "encryptionService", "beforeAll", "registerAction", "parameters", "datasetId", "projectId", "success", "accessLevel", "dataset", "access", "dashboardId", "it", "finding", "name", "category", "severity", "resourceName", "state", "eventTime", "Date", "toISOString", "createTime", "sourceProperties", "finding_type", "finding_description", "data_type", "compliance_frameworks", "startTime", "now", "normalizedFinding", "id", "type", "resourceType", "createdAt", "getTime", "description", "dataType", "complianceFrameworks", "remediationScenario", "framework", "control", "resource", "provider", "remediationSteps", "action", "encryptionType", "keyRotationPeriod", "allowedRoles", "findingId", "remediationId", "mockResolvedValue", "encrypted", "timestamp", "user", "overall", "hipaa", "gdpr", "remediationResult", "executeRemediation", "endTime", "duration", "expect", "toBe", "steps", "length", "every", "step", "toHaveBeenCalledWith", "objectContaining", "complianceScore", "toBeGreaterThanOrEqual", "toBeLessThan", "console", "log", "accessLogs", "details", "controls", "total", "compliant", "nonCompliant", "remediated", "frameworks", "score", "controlsCompliant", "controlsTotal", "manualRemediationCostPerIncident", "annualIncidents", "automatedRemediationRate", "annualSavings", "toLocaleString"], "sources": ["breach-to-boardroom.test.js"], "sourcesContent": ["/**\n * NovaConnect \"Breach to Boardroom\" Demo Test\n * \n * This test simulates the demo scenario described in the strategic document:\n * 1. Simulate PHI leak via misconfigured BigQuery dataset\n * 2. Auto-containment in under 8 seconds\n * 3. Boardroom-ready reporting with compliance score maintained at 99.9%\n */\n\nconst { TransformationEngine } = require('../../src/engines/transformation-engine');\nconst { RemediationEngine } = require('../../src/engines/remediation-engine');\nconst { SCCConnector } = require('../../src/connectors/gcp/scc-connector');\nconst { EncryptionService } = require('../../src/security/encryption-service');\n\n// Mock GCP services\nconst mockBigQueryService = {\n  getDataset: jest.fn(),\n  updateDataset: jest.fn(),\n  encryptDataset: jest.fn(),\n  getAccessLogs: jest.fn()\n};\n\n// Mock Looker service\nconst mockLookerService = {\n  updateDashboard: jest.fn(),\n  getComplianceScore: jest.fn()\n};\n\ndescribe('Breach to Boardroom Demo', () => {\n  let transformationEngine;\n  let remediationEngine;\n  let sccConnector;\n  let encryptionService;\n  \n  beforeAll(async () => {\n    // Initialize engines and services\n    transformationEngine = new TransformationEngine();\n    remediationEngine = new RemediationEngine();\n    sccConnector = new SCCConnector();\n    encryptionService = new EncryptionService();\n    \n    // Register remediation actions\n    remediationEngine.registerAction('encrypt-dataset', async ({ parameters }) => {\n      const { datasetId, projectId } = parameters;\n      await mockBigQueryService.encryptDataset(projectId, datasetId);\n      return { success: true, datasetId, projectId };\n    });\n    \n    remediationEngine.registerAction('update-access-controls', async ({ parameters }) => {\n      const { datasetId, projectId, accessLevel } = parameters;\n      const dataset = await mockBigQueryService.getDataset(projectId, datasetId);\n      dataset.access = accessLevel;\n      await mockBigQueryService.updateDataset(projectId, datasetId, dataset);\n      return { success: true, datasetId, projectId };\n    });\n    \n    remediationEngine.registerAction('update-compliance-dashboard', async ({ parameters }) => {\n      const { dashboardId } = parameters;\n      await mockLookerService.updateDashboard(dashboardId);\n      return { success: true, dashboardId };\n    });\n  });\n  \n  it('should detect and remediate PHI leak in under 8 seconds', async () => {\n    // 1. Simulate the finding from SCC\n    const finding = {\n      name: 'organizations/123/sources/456/findings/phi-leak-789',\n      category: 'DATA_LEAK',\n      severity: 'HIGH',\n      resourceName: 'projects/healthcare-demo/datasets/patient_records',\n      state: 'ACTIVE',\n      eventTime: new Date().toISOString(),\n      createTime: new Date().toISOString(),\n      sourceProperties: {\n        finding_type: 'Sensitive Data Exposure',\n        finding_description: 'PHI data exposed in BigQuery dataset',\n        data_type: 'PHI',\n        compliance_frameworks: ['HIPAA', 'GDPR']\n      }\n    };\n    \n    // 2. Normalize the finding\n    const startTime = Date.now();\n    \n    const normalizedFinding = {\n      id: 'phi-leak-789',\n      type: 'data_leak',\n      severity: 'high',\n      resourceName: 'projects/healthcare-demo/datasets/patient_records',\n      resourceType: 'bigquery.dataset',\n      createdAt: new Date().getTime(),\n      description: 'PHI data exposed in BigQuery dataset',\n      dataType: 'PHI',\n      complianceFrameworks: ['HIPAA', 'GDPR']\n    };\n    \n    // 3. Create remediation scenario\n    const remediationScenario = {\n      id: 'phi-leak-remediation-789',\n      type: 'compliance',\n      framework: 'HIPAA',\n      control: '164.312(a)(1)',\n      severity: 'high',\n      resource: {\n        id: 'patient_records',\n        type: 'bigquery.dataset',\n        name: 'patient_records',\n        provider: 'gcp',\n        projectId: 'healthcare-demo'\n      },\n      finding: normalizedFinding,\n      remediationSteps: [\n        {\n          id: 'step-1',\n          action: 'encrypt-dataset',\n          parameters: {\n            projectId: 'healthcare-demo',\n            datasetId: 'patient_records',\n            encryptionType: 'AES-256',\n            keyRotationPeriod: '90d'\n          }\n        },\n        {\n          id: 'step-2',\n          action: 'update-access-controls',\n          parameters: {\n            projectId: 'healthcare-demo',\n            datasetId: 'patient_records',\n            accessLevel: 'restricted',\n            allowedRoles: ['healthcare-admin', 'compliance-officer']\n          }\n        },\n        {\n          id: 'step-3',\n          action: 'update-compliance-dashboard',\n          parameters: {\n            dashboardId: 'hipaa-compliance-dashboard',\n            findingId: 'phi-leak-789',\n            remediationId: 'phi-leak-remediation-789'\n          }\n        }\n      ]\n    };\n    \n    // Mock service responses\n    mockBigQueryService.getDataset.mockResolvedValue({\n      id: 'patient_records',\n      access: 'public'\n    });\n    \n    mockBigQueryService.updateDataset.mockResolvedValue({\n      id: 'patient_records',\n      access: 'restricted'\n    });\n    \n    mockBigQueryService.encryptDataset.mockResolvedValue({\n      id: 'patient_records',\n      encrypted: true\n    });\n    \n    mockBigQueryService.getAccessLogs.mockResolvedValue([\n      { timestamp: new Date().toISOString(), user: 'system', action: 'encrypt' },\n      { timestamp: new Date().toISOString(), user: 'system', action: 'update_access' }\n    ]);\n    \n    mockLookerService.getComplianceScore.mockResolvedValue({\n      overall: 99.9,\n      hipaa: 99.8,\n      gdpr: 100\n    });\n    \n    // 4. Execute remediation\n    const remediationResult = await remediationEngine.executeRemediation(remediationScenario);\n    \n    const endTime = Date.now();\n    const duration = endTime - startTime;\n    \n    // 5. Verify results\n    expect(remediationResult.success).toBe(true);\n    expect(remediationResult.steps.length).toBe(3);\n    expect(remediationResult.steps.every(step => step.success)).toBe(true);\n    \n    // Verify encryption was called\n    expect(mockBigQueryService.encryptDataset).toHaveBeenCalledWith(\n      'healthcare-demo',\n      'patient_records'\n    );\n    \n    // Verify access controls were updated\n    expect(mockBigQueryService.updateDataset).toHaveBeenCalledWith(\n      'healthcare-demo',\n      'patient_records',\n      expect.objectContaining({\n        access: 'restricted'\n      })\n    );\n    \n    // Verify dashboard was updated\n    expect(mockLookerService.updateDashboard).toHaveBeenCalledWith(\n      'hipaa-compliance-dashboard'\n    );\n    \n    // Verify compliance score is maintained\n    const complianceScore = await mockLookerService.getComplianceScore();\n    expect(complianceScore.overall).toBeGreaterThanOrEqual(99.9);\n    \n    // Verify remediation completed in under 8 seconds\n    expect(duration).toBeLessThan(8000);\n    console.log(`Remediation completed in ${duration}ms`);\n    \n    // Verify access logs were updated\n    const accessLogs = await mockBigQueryService.getAccessLogs();\n    expect(accessLogs.length).toBeGreaterThanOrEqual(2);\n  });\n  \n  it('should generate boardroom-ready report', async () => {\n    // Mock compliance score\n    mockLookerService.getComplianceScore.mockResolvedValue({\n      overall: 99.9,\n      hipaa: 99.8,\n      gdpr: 100,\n      details: {\n        controls: {\n          total: 500,\n          compliant: 499,\n          nonCompliant: 1,\n          remediated: 1\n        },\n        frameworks: [\n          { name: 'HIPAA', score: 99.8, controlsCompliant: 249, controlsTotal: 250 },\n          { name: 'GDPR', score: 100, controlsCompliant: 250, controlsTotal: 250 }\n        ]\n      }\n    });\n    \n    // Get compliance score\n    const complianceScore = await mockLookerService.getComplianceScore();\n    \n    // Verify compliance score\n    expect(complianceScore.overall).toBeGreaterThanOrEqual(99.9);\n    expect(complianceScore.details.controls.remediated).toBeGreaterThanOrEqual(1);\n    \n    // Calculate cost savings\n    const manualRemediationCostPerIncident = 4200; // $4,200 per incident\n    const annualIncidents = 1000; // Estimated annual incidents\n    const automatedRemediationRate = 0.92; // 92% of incidents automated\n    \n    const annualSavings = manualRemediationCostPerIncident * annualIncidents * automatedRemediationRate;\n    \n    // Verify cost savings\n    expect(annualSavings).toBeGreaterThanOrEqual(3800000); // $3.8M+\n    \n    console.log(`Annual cost savings: $${annualSavings.toLocaleString()}`);\n    console.log(`Compliance score maintained at ${complianceScore.overall}%`);\n  });\n});\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,MAAM;EAAEA;AAAqB,CAAC,GAAGC,OAAO,CAAC,yCAAyC,CAAC;AACnF,MAAM;EAAEC;AAAkB,CAAC,GAAGD,OAAO,CAAC,sCAAsC,CAAC;AAC7E,MAAM;EAAEE;AAAa,CAAC,GAAGF,OAAO,CAAC,wCAAwC,CAAC;AAC1E,MAAM;EAAEG;AAAkB,CAAC,GAAGH,OAAO,CAAC,uCAAuC,CAAC;;AAE9E;AACA,MAAMI,mBAAmB,GAAG;EAC1BC,UAAU,EAAEC,IAAI,CAACC,EAAE,CAAC,CAAC;EACrBC,aAAa,EAAEF,IAAI,CAACC,EAAE,CAAC,CAAC;EACxBE,cAAc,EAAEH,IAAI,CAACC,EAAE,CAAC,CAAC;EACzBG,aAAa,EAAEJ,IAAI,CAACC,EAAE,CAAC;AACzB,CAAC;;AAED;AACA,MAAMI,iBAAiB,GAAG;EACxBC,eAAe,EAAEN,IAAI,CAACC,EAAE,CAAC,CAAC;EAC1BM,kBAAkB,EAAEP,IAAI,CAACC,EAAE,CAAC;AAC9B,CAAC;AAEDO,QAAQ,CAAC,0BAA0B,EAAE,MAAM;EACzC,IAAIC,oBAAoB;EACxB,IAAIC,iBAAiB;EACrB,IAAIC,YAAY;EAChB,IAAIC,iBAAiB;EAErBC,SAAS,CAAC,YAAY;IACpB;IACAJ,oBAAoB,GAAG,IAAIhB,oBAAoB,CAAC,CAAC;IACjDiB,iBAAiB,GAAG,IAAIf,iBAAiB,CAAC,CAAC;IAC3CgB,YAAY,GAAG,IAAIf,YAAY,CAAC,CAAC;IACjCgB,iBAAiB,GAAG,IAAIf,iBAAiB,CAAC,CAAC;;IAE3C;IACAa,iBAAiB,CAACI,cAAc,CAAC,iBAAiB,EAAE,OAAO;MAAEC;IAAW,CAAC,KAAK;MAC5E,MAAM;QAAEC,SAAS;QAAEC;MAAU,CAAC,GAAGF,UAAU;MAC3C,MAAMjB,mBAAmB,CAACK,cAAc,CAACc,SAAS,EAAED,SAAS,CAAC;MAC9D,OAAO;QAAEE,OAAO,EAAE,IAAI;QAAEF,SAAS;QAAEC;MAAU,CAAC;IAChD,CAAC,CAAC;IAEFP,iBAAiB,CAACI,cAAc,CAAC,wBAAwB,EAAE,OAAO;MAAEC;IAAW,CAAC,KAAK;MACnF,MAAM;QAAEC,SAAS;QAAEC,SAAS;QAAEE;MAAY,CAAC,GAAGJ,UAAU;MACxD,MAAMK,OAAO,GAAG,MAAMtB,mBAAmB,CAACC,UAAU,CAACkB,SAAS,EAAED,SAAS,CAAC;MAC1EI,OAAO,CAACC,MAAM,GAAGF,WAAW;MAC5B,MAAMrB,mBAAmB,CAACI,aAAa,CAACe,SAAS,EAAED,SAAS,EAAEI,OAAO,CAAC;MACtE,OAAO;QAAEF,OAAO,EAAE,IAAI;QAAEF,SAAS;QAAEC;MAAU,CAAC;IAChD,CAAC,CAAC;IAEFP,iBAAiB,CAACI,cAAc,CAAC,6BAA6B,EAAE,OAAO;MAAEC;IAAW,CAAC,KAAK;MACxF,MAAM;QAAEO;MAAY,CAAC,GAAGP,UAAU;MAClC,MAAMV,iBAAiB,CAACC,eAAe,CAACgB,WAAW,CAAC;MACpD,OAAO;QAAEJ,OAAO,EAAE,IAAI;QAAEI;MAAY,CAAC;IACvC,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFC,EAAE,CAAC,yDAAyD,EAAE,YAAY;IACxE;IACA,MAAMC,OAAO,GAAG;MACdC,IAAI,EAAE,qDAAqD;MAC3DC,QAAQ,EAAE,WAAW;MACrBC,QAAQ,EAAE,MAAM;MAChBC,YAAY,EAAE,mDAAmD;MACjEC,KAAK,EAAE,QAAQ;MACfC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MACnCC,UAAU,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MACpCE,gBAAgB,EAAE;QAChBC,YAAY,EAAE,yBAAyB;QACvCC,mBAAmB,EAAE,sCAAsC;QAC3DC,SAAS,EAAE,KAAK;QAChBC,qBAAqB,EAAE,CAAC,OAAO,EAAE,MAAM;MACzC;IACF,CAAC;;IAED;IACA,MAAMC,SAAS,GAAGR,IAAI,CAACS,GAAG,CAAC,CAAC;IAE5B,MAAMC,iBAAiB,GAAG;MACxBC,EAAE,EAAE,cAAc;MAClBC,IAAI,EAAE,WAAW;MACjBhB,QAAQ,EAAE,MAAM;MAChBC,YAAY,EAAE,mDAAmD;MACjEgB,YAAY,EAAE,kBAAkB;MAChCC,SAAS,EAAE,IAAId,IAAI,CAAC,CAAC,CAACe,OAAO,CAAC,CAAC;MAC/BC,WAAW,EAAE,sCAAsC;MACnDC,QAAQ,EAAE,KAAK;MACfC,oBAAoB,EAAE,CAAC,OAAO,EAAE,MAAM;IACxC,CAAC;;IAED;IACA,MAAMC,mBAAmB,GAAG;MAC1BR,EAAE,EAAE,0BAA0B;MAC9BC,IAAI,EAAE,YAAY;MAClBQ,SAAS,EAAE,OAAO;MAClBC,OAAO,EAAE,eAAe;MACxBzB,QAAQ,EAAE,MAAM;MAChB0B,QAAQ,EAAE;QACRX,EAAE,EAAE,iBAAiB;QACrBC,IAAI,EAAE,kBAAkB;QACxBlB,IAAI,EAAE,iBAAiB;QACvB6B,QAAQ,EAAE,KAAK;QACfrC,SAAS,EAAE;MACb,CAAC;MACDO,OAAO,EAAEiB,iBAAiB;MAC1Bc,gBAAgB,EAAE,CAChB;QACEb,EAAE,EAAE,QAAQ;QACZc,MAAM,EAAE,iBAAiB;QACzBzC,UAAU,EAAE;UACVE,SAAS,EAAE,iBAAiB;UAC5BD,SAAS,EAAE,iBAAiB;UAC5ByC,cAAc,EAAE,SAAS;UACzBC,iBAAiB,EAAE;QACrB;MACF,CAAC,EACD;QACEhB,EAAE,EAAE,QAAQ;QACZc,MAAM,EAAE,wBAAwB;QAChCzC,UAAU,EAAE;UACVE,SAAS,EAAE,iBAAiB;UAC5BD,SAAS,EAAE,iBAAiB;UAC5BG,WAAW,EAAE,YAAY;UACzBwC,YAAY,EAAE,CAAC,kBAAkB,EAAE,oBAAoB;QACzD;MACF,CAAC,EACD;QACEjB,EAAE,EAAE,QAAQ;QACZc,MAAM,EAAE,6BAA6B;QACrCzC,UAAU,EAAE;UACVO,WAAW,EAAE,4BAA4B;UACzCsC,SAAS,EAAE,cAAc;UACzBC,aAAa,EAAE;QACjB;MACF,CAAC;IAEL,CAAC;;IAED;IACA/D,mBAAmB,CAACC,UAAU,CAAC+D,iBAAiB,CAAC;MAC/CpB,EAAE,EAAE,iBAAiB;MACrBrB,MAAM,EAAE;IACV,CAAC,CAAC;IAEFvB,mBAAmB,CAACI,aAAa,CAAC4D,iBAAiB,CAAC;MAClDpB,EAAE,EAAE,iBAAiB;MACrBrB,MAAM,EAAE;IACV,CAAC,CAAC;IAEFvB,mBAAmB,CAACK,cAAc,CAAC2D,iBAAiB,CAAC;MACnDpB,EAAE,EAAE,iBAAiB;MACrBqB,SAAS,EAAE;IACb,CAAC,CAAC;IAEFjE,mBAAmB,CAACM,aAAa,CAAC0D,iBAAiB,CAAC,CAClD;MAAEE,SAAS,EAAE,IAAIjC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MAAEiC,IAAI,EAAE,QAAQ;MAAET,MAAM,EAAE;IAAU,CAAC,EAC1E;MAAEQ,SAAS,EAAE,IAAIjC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MAAEiC,IAAI,EAAE,QAAQ;MAAET,MAAM,EAAE;IAAgB,CAAC,CACjF,CAAC;IAEFnD,iBAAiB,CAACE,kBAAkB,CAACuD,iBAAiB,CAAC;MACrDI,OAAO,EAAE,IAAI;MACbC,KAAK,EAAE,IAAI;MACXC,IAAI,EAAE;IACR,CAAC,CAAC;;IAEF;IACA,MAAMC,iBAAiB,GAAG,MAAM3D,iBAAiB,CAAC4D,kBAAkB,CAACpB,mBAAmB,CAAC;IAEzF,MAAMqB,OAAO,GAAGxC,IAAI,CAACS,GAAG,CAAC,CAAC;IAC1B,MAAMgC,QAAQ,GAAGD,OAAO,GAAGhC,SAAS;;IAEpC;IACAkC,MAAM,CAACJ,iBAAiB,CAACnD,OAAO,CAAC,CAACwD,IAAI,CAAC,IAAI,CAAC;IAC5CD,MAAM,CAACJ,iBAAiB,CAACM,KAAK,CAACC,MAAM,CAAC,CAACF,IAAI,CAAC,CAAC,CAAC;IAC9CD,MAAM,CAACJ,iBAAiB,CAACM,KAAK,CAACE,KAAK,CAACC,IAAI,IAAIA,IAAI,CAAC5D,OAAO,CAAC,CAAC,CAACwD,IAAI,CAAC,IAAI,CAAC;;IAEtE;IACAD,MAAM,CAAC3E,mBAAmB,CAACK,cAAc,CAAC,CAAC4E,oBAAoB,CAC7D,iBAAiB,EACjB,iBACF,CAAC;;IAED;IACAN,MAAM,CAAC3E,mBAAmB,CAACI,aAAa,CAAC,CAAC6E,oBAAoB,CAC5D,iBAAiB,EACjB,iBAAiB,EACjBN,MAAM,CAACO,gBAAgB,CAAC;MACtB3D,MAAM,EAAE;IACV,CAAC,CACH,CAAC;;IAED;IACAoD,MAAM,CAACpE,iBAAiB,CAACC,eAAe,CAAC,CAACyE,oBAAoB,CAC5D,4BACF,CAAC;;IAED;IACA,MAAME,eAAe,GAAG,MAAM5E,iBAAiB,CAACE,kBAAkB,CAAC,CAAC;IACpEkE,MAAM,CAACQ,eAAe,CAACf,OAAO,CAAC,CAACgB,sBAAsB,CAAC,IAAI,CAAC;;IAE5D;IACAT,MAAM,CAACD,QAAQ,CAAC,CAACW,YAAY,CAAC,IAAI,CAAC;IACnCC,OAAO,CAACC,GAAG,CAAC,4BAA4Bb,QAAQ,IAAI,CAAC;;IAErD;IACA,MAAMc,UAAU,GAAG,MAAMxF,mBAAmB,CAACM,aAAa,CAAC,CAAC;IAC5DqE,MAAM,CAACa,UAAU,CAACV,MAAM,CAAC,CAACM,sBAAsB,CAAC,CAAC,CAAC;EACrD,CAAC,CAAC;EAEF3D,EAAE,CAAC,wCAAwC,EAAE,YAAY;IACvD;IACAlB,iBAAiB,CAACE,kBAAkB,CAACuD,iBAAiB,CAAC;MACrDI,OAAO,EAAE,IAAI;MACbC,KAAK,EAAE,IAAI;MACXC,IAAI,EAAE,GAAG;MACTmB,OAAO,EAAE;QACPC,QAAQ,EAAE;UACRC,KAAK,EAAE,GAAG;UACVC,SAAS,EAAE,GAAG;UACdC,YAAY,EAAE,CAAC;UACfC,UAAU,EAAE;QACd,CAAC;QACDC,UAAU,EAAE,CACV;UAAEpE,IAAI,EAAE,OAAO;UAAEqE,KAAK,EAAE,IAAI;UAAEC,iBAAiB,EAAE,GAAG;UAAEC,aAAa,EAAE;QAAI,CAAC,EAC1E;UAAEvE,IAAI,EAAE,MAAM;UAAEqE,KAAK,EAAE,GAAG;UAAEC,iBAAiB,EAAE,GAAG;UAAEC,aAAa,EAAE;QAAI,CAAC;MAE5E;IACF,CAAC,CAAC;;IAEF;IACA,MAAMf,eAAe,GAAG,MAAM5E,iBAAiB,CAACE,kBAAkB,CAAC,CAAC;;IAEpE;IACAkE,MAAM,CAACQ,eAAe,CAACf,OAAO,CAAC,CAACgB,sBAAsB,CAAC,IAAI,CAAC;IAC5DT,MAAM,CAACQ,eAAe,CAACM,OAAO,CAACC,QAAQ,CAACI,UAAU,CAAC,CAACV,sBAAsB,CAAC,CAAC,CAAC;;IAE7E;IACA,MAAMe,gCAAgC,GAAG,IAAI,CAAC,CAAC;IAC/C,MAAMC,eAAe,GAAG,IAAI,CAAC,CAAC;IAC9B,MAAMC,wBAAwB,GAAG,IAAI,CAAC,CAAC;;IAEvC,MAAMC,aAAa,GAAGH,gCAAgC,GAAGC,eAAe,GAAGC,wBAAwB;;IAEnG;IACA1B,MAAM,CAAC2B,aAAa,CAAC,CAAClB,sBAAsB,CAAC,OAAO,CAAC,CAAC,CAAC;;IAEvDE,OAAO,CAACC,GAAG,CAAC,yBAAyBe,aAAa,CAACC,cAAc,CAAC,CAAC,EAAE,CAAC;IACtEjB,OAAO,CAACC,GAAG,CAAC,kCAAkCJ,eAAe,CAACf,OAAO,GAAG,CAAC;EAC3E,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}