/**
 * Analytics Controls
 * 
 * This module provides controls for configuring analytics.
 */

const ControlPanel = require('./control-panel');
const { WebSocketClient } = require('../websocket');

/**
 * AnalyticsControls class
 */
class AnalyticsControls {
  /**
   * Create a new AnalyticsControls instance
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    this.options = {
      enableLogging: options.enableLogging !== undefined ? options.enableLogging : true,
      wsUrl: options.wsUrl || 'ws://localhost:3001/ws',
      controlPanel: options.controlPanel || null,
      ...options
    };
    
    // Create control panel if not provided
    if (!this.options.controlPanel) {
      this.controlPanel = new ControlPanel({
        enableLogging: this.options.enableLogging,
        wsUrl: this.options.wsUrl,
        autoConnect: false
      });
    } else {
      this.controlPanel = this.options.controlPanel;
    }
    
    // Create WebSocket client for analytics operations
    this.client = new WebSocketClient({
      url: this.options.wsUrl,
      clientId: `analytics-controls-${Date.now()}`,
      enableLogging: this.options.enableLogging,
      autoReconnect: true
    });
    
    // Initialize state
    this.state = {
      isConnected: false,
      metrics: {},
      dashboards: new Map(), // dashboardId -> dashboard
      selectedDashboardId: null,
      activeQueries: new Set(),
      queryResults: new Map(), // queryId -> result
      lastUpdate: Date.now()
    };
    
    // Set up event handlers
    this.client.on('connected', this._handleConnected.bind(this));
    this.client.on('disconnected', this._handleDisconnected.bind(this));
    this.client.on('error', this._handleError.bind(this));
    this.client.on('channel-message', this._handleChannelMessage.bind(this));
    
    // Set up control panel event handlers
    this.controlPanel.on('control-value-changed', this._handleControlValueChanged.bind(this));
    this.controlPanel.on('action-executed', this._handleActionExecuted.bind(this));
    
    if (this.options.enableLogging) {
      console.log('AnalyticsControls initialized');
    }
  }
  
  /**
   * Connect to the WebSocket server
   * @returns {Promise<void>} - Promise that resolves when connected
   */
  async connect() {
    if (this.state.isConnected) {
      if (this.options.enableLogging) {
        console.log('AnalyticsControls: Already connected');
      }
      return;
    }
    
    // Connect control panel
    if (!this.controlPanel.state.isConnected) {
      await this.controlPanel.connect();
    }
    
    // Connect WebSocket client
    await this.client.connect();
    
    // Subscribe to analytics channels
    await this.client.subscribe('analytics-updates');
    await this.client.subscribe('analytics-events');
    
    // Update state
    this.state.isConnected = true;
    this.state.lastUpdate = Date.now();
    
    // Register controls
    this._registerControls();
    
    // Get metrics
    await this._fetchMetrics();
    
    // Get dashboards
    await this._fetchDashboards();
    
    if (this.options.enableLogging) {
      console.log('AnalyticsControls: Connected');
    }
  }
  
  /**
   * Disconnect from the WebSocket server
   * @returns {Promise<void>} - Promise that resolves when disconnected
   */
  async disconnect() {
    if (!this.state.isConnected) {
      if (this.options.enableLogging) {
        console.log('AnalyticsControls: Not connected');
      }
      return;
    }
    
    // Disconnect WebSocket client
    await this.client.disconnect();
    
    // Update state
    this.state.isConnected = false;
    this.state.lastUpdate = Date.now();
    
    if (this.options.enableLogging) {
      console.log('AnalyticsControls: Disconnected');
    }
  }
  
  /**
   * Handle WebSocket connected event
   * @private
   */
  _handleConnected() {
    // Update state
    this.state.isConnected = true;
    this.state.lastUpdate = Date.now();
    
    if (this.options.enableLogging) {
      console.log('AnalyticsControls: WebSocket connected');
    }
  }
  
  /**
   * Handle WebSocket disconnected event
   * @private
   */
  _handleDisconnected() {
    // Update state
    this.state.isConnected = false;
    this.state.lastUpdate = Date.now();
    
    if (this.options.enableLogging) {
      console.log('AnalyticsControls: WebSocket disconnected');
    }
  }
  
  /**
   * Handle WebSocket error event
   * @param {Error} error - Error object
   * @private
   */
  _handleError(error) {
    if (this.options.enableLogging) {
      console.error('AnalyticsControls: WebSocket error:', error);
    }
  }
  
  /**
   * Handle WebSocket channel message event
   * @param {Object} data - Message data
   * @private
   */
  _handleChannelMessage(data) {
    const { channel, data: messageData } = data;
    
    // Handle message based on channel
    switch (channel) {
      case 'analytics-updates':
        this._handleAnalyticsUpdates(messageData);
        break;
        
      case 'analytics-events':
        this._handleAnalyticsEvents(messageData);
        break;
    }
  }
  
  /**
   * Handle analytics updates
   * @param {Object} data - Update data
   * @private
   */
  _handleAnalyticsUpdates(data) {
    if (!data) {
      return;
    }
    
    // Handle update based on type
    if (data.type === 'metrics') {
      this._handleMetricsUpdate(data);
    } else if (data.type === 'dashboard') {
      this._handleDashboardUpdate(data);
    } else if (data.type === 'query-result') {
      this._handleQueryResultUpdate(data);
    }
  }
  
  /**
   * Handle metrics update
   * @param {Object} data - Update data
   * @private
   */
  _handleMetricsUpdate(data) {
    if (!data || !data.metrics) {
      return;
    }
    
    // Update metrics
    this.state.metrics = { ...this.state.metrics, ...data.metrics };
    this.state.lastUpdate = Date.now();
    
    // Update metrics controls
    this._updateMetricsControls();
    
    if (this.options.enableLogging) {
      console.log('AnalyticsControls: Metrics updated');
    }
  }
  
  /**
   * Handle dashboard update
   * @param {Object} data - Update data
   * @private
   */
  _handleDashboardUpdate(data) {
    if (!data || !data.id || !data.dashboard) {
      return;
    }
    
    // Update dashboard
    this.state.dashboards.set(data.id, data.dashboard);
    this.state.lastUpdate = Date.now();
    
    // Update dashboard list control
    this._updateDashboardListControl();
    
    // Update dashboard details if this is the selected dashboard
    if (this.state.selectedDashboardId === data.id) {
      this._updateDashboardDetailsControls(data.id);
    }
    
    if (this.options.enableLogging) {
      console.log(`AnalyticsControls: Dashboard updated - ${data.id}`);
    }
  }
  
  /**
   * Handle query result update
   * @param {Object} data - Update data
   * @private
   */
  _handleQueryResultUpdate(data) {
    if (!data || !data.query || !data.result) {
      return;
    }
    
    // Update query result
    this.state.queryResults.set(data.result.id, data.result);
    this.state.lastUpdate = Date.now();
    
    // Remove from active queries
    this.state.activeQueries.delete(data.result.id);
    
    // Update query results control
    this._updateQueryResultsControl();
    
    if (this.options.enableLogging) {
      console.log(`AnalyticsControls: Query result updated - ${data.result.id}`);
    }
  }
  
  /**
   * Handle analytics events
   * @param {Object} data - Event data
   * @private
   */
  _handleAnalyticsEvents(data) {
    if (!data || !data.event) {
      return;
    }
    
    // Handle event based on type
    switch (data.event) {
      case 'query-started':
        this._handleQueryStarted(data);
        break;
        
      case 'query-completed':
        this._handleQueryCompleted(data);
        break;
        
      case 'query-error':
        this._handleQueryError(data);
        break;
    }
  }
  
  /**
   * Handle query started event
   * @param {Object} data - Event data
   * @private
   */
  _handleQueryStarted(data) {
    if (!data || !data.query || !data.query.id) {
      return;
    }
    
    // Add to active queries
    this.state.activeQueries.add(data.query.id);
    this.state.lastUpdate = Date.now();
    
    if (this.options.enableLogging) {
      console.log(`AnalyticsControls: Query started - ${data.query.id}`);
    }
  }
  
  /**
   * Handle query completed event
   * @param {Object} data - Event data
   * @private
   */
  _handleQueryCompleted(data) {
    if (!data || !data.query || !data.query.id) {
      return;
    }
    
    // Remove from active queries
    this.state.activeQueries.delete(data.query.id);
    this.state.lastUpdate = Date.now();
    
    if (this.options.enableLogging) {
      console.log(`AnalyticsControls: Query completed - ${data.query.id}`);
    }
  }
  
  /**
   * Handle query error event
   * @param {Object} data - Event data
   * @private
   */
  _handleQueryError(data) {
    if (!data || !data.query || !data.query.id) {
      return;
    }
    
    // Remove from active queries
    this.state.activeQueries.delete(data.query.id);
    this.state.lastUpdate = Date.now();
    
    if (this.options.enableLogging) {
      console.log(`AnalyticsControls: Query error - ${data.query.id}: ${data.error}`);
    }
  }
  
  /**
   * Handle control value changed event
   * @param {Object} data - Event data
   * @private
   */
  _handleControlValueChanged(data) {
    const { controlId, value } = data;
    
    // Handle control value change based on control ID
    switch (controlId) {
      case 'dashboard-selector':
        this._handleDashboardSelected(value);
        break;
        
      case 'refresh-interval':
        this._updateRefreshInterval(value);
        break;
        
      case 'query-input':
        // No action needed, query is executed when execute button is clicked
        break;
    }
  }
  
  /**
   * Handle action executed event
   * @param {Object} data - Event data
   * @private
   */
  _handleActionExecuted(data) {
    const { action, params } = data;
    
    // Handle action based on type
    switch (action) {
      case 'execute-query':
        this._handleExecuteQueryAction(params);
        break;
        
      case 'refresh-metrics':
        this._handleRefreshMetricsAction(params);
        break;
        
      case 'refresh-dashboard':
        this._handleRefreshDashboardAction(params);
        break;
    }
  }
  
  /**
   * Handle dashboard selected
   * @param {string} dashboardId - Dashboard ID
   * @private
   */
  _handleDashboardSelected(dashboardId) {
    // Update selected dashboard
    this.state.selectedDashboardId = dashboardId;
    this.state.lastUpdate = Date.now();
    
    // Update dashboard details controls
    this._updateDashboardDetailsControls(dashboardId);
    
    if (this.options.enableLogging) {
      console.log(`AnalyticsControls: Dashboard selected - ${dashboardId}`);
    }
  }
  
  /**
   * Handle execute query action
   * @param {Object} params - Action parameters
   * @private
   */
  async _handleExecuteQueryAction(params) {
    if (!this.state.isConnected) {
      if (this.options.enableLogging) {
        console.log('AnalyticsControls: Not connected');
      }
      return;
    }
    
    const query = params.query || this.controlPanel.getControlValue('query-input');
    
    if (!query) {
      if (this.options.enableLogging) {
        console.log('AnalyticsControls: No query provided');
      }
      return;
    }
    
    try {
      // Send execute query message
      const response = await this.client.send({
        component: 'analytics',
        type: 'execute-query',
        query,
        params: params.params || {}
      });
      
      if (this.options.enableLogging) {
        console.log(`AnalyticsControls: Query executed - ${response.result.result.id}`);
      }
      
      // Update query result
      if (response && response.result && response.result.result) {
        this.state.queryResults.set(response.result.result.id, response.result.result);
        this.state.lastUpdate = Date.now();
        
        // Update query results control
        this._updateQueryResultsControl();
      }
    } catch (error) {
      if (this.options.enableLogging) {
        console.error('AnalyticsControls: Error executing query:', error);
      }
    }
  }
  
  /**
   * Handle refresh metrics action
   * @param {Object} params - Action parameters
   * @private
   */
  async _handleRefreshMetricsAction(params) {
    await this._fetchMetrics();
  }
  
  /**
   * Handle refresh dashboard action
   * @param {Object} params - Action parameters
   * @private
   */
  async _handleRefreshDashboardAction(params) {
    const dashboardId = params.id || this.state.selectedDashboardId;
    
    if (!dashboardId) {
      if (this.options.enableLogging) {
        console.log('AnalyticsControls: No dashboard selected');
      }
      return;
    }
    
    await this._fetchDashboard(dashboardId);
  }
  
  /**
   * Update refresh interval
   * @param {number} interval - Refresh interval in seconds
   * @private
   */
  _updateRefreshInterval(interval) {
    // Clear existing interval
    if (this._refreshInterval) {
      clearInterval(this._refreshInterval);
      this._refreshInterval = null;
    }
    
    // Set new interval if greater than 0
    if (interval > 0) {
      this._refreshInterval = setInterval(() => {
        // Refresh metrics
        this._fetchMetrics().catch((error) => {
          if (this.options.enableLogging) {
            console.error('AnalyticsControls: Error refreshing metrics:', error);
          }
        });
        
        // Refresh selected dashboard
        if (this.state.selectedDashboardId) {
          this._fetchDashboard(this.state.selectedDashboardId).catch((error) => {
            if (this.options.enableLogging) {
              console.error(`AnalyticsControls: Error refreshing dashboard ${this.state.selectedDashboardId}:`, error);
            }
          });
        }
      }, interval * 1000);
      
      if (this.options.enableLogging) {
        console.log(`AnalyticsControls: Refresh interval set to ${interval} seconds`);
      }
    }
  }
  
  /**
   * Register controls
   * @private
   */
  _registerControls() {
    // Register dashboard selector
    this.controlPanel.registerControl('dashboard-selector', {
      type: 'select',
      label: 'Dashboard',
      options: [],
      defaultValue: '',
      validate: (value) => typeof value === 'string'
    }, 'analytics-controls');
    
    // Register refresh interval control
    this.controlPanel.registerControl('refresh-interval', {
      type: 'select',
      label: 'Refresh Interval',
      options: [
        { value: 0, label: 'Manual' },
        { value: 5, label: '5 seconds' },
        { value: 10, label: '10 seconds' },
        { value: 30, label: '30 seconds' },
        { value: 60, label: '1 minute' },
        { value: 300, label: '5 minutes' }
      ],
      defaultValue: 0,
      validate: (value) => typeof value === 'number' && value >= 0
    }, 'analytics-controls');
    
    // Register query input control
    this.controlPanel.registerControl('query-input', {
      type: 'textarea',
      label: 'Query',
      defaultValue: '',
      validate: (value) => typeof value === 'string'
    }, 'analytics-query');
    
    // Register query parameters control
    this.controlPanel.registerControl('query-params', {
      type: 'json',
      label: 'Parameters',
      defaultValue: '{}',
      validate: (value) => {
        try {
          JSON.parse(value);
          return true;
        } catch (error) {
          return false;
        }
      }
    }, 'analytics-query');
    
    // Register query results control
    this.controlPanel.registerControl('query-results', {
      type: 'json',
      label: 'Results',
      defaultValue: '[]',
      readOnly: true
    }, 'analytics-query');
  }
  
  /**
   * Update dashboard list control
   * @private
   */
  _updateDashboardListControl() {
    // Get dashboard IDs
    const dashboardIds = Array.from(this.state.dashboards.keys());
    
    // Update dashboard selector options
    const dashboardSelector = this.controlPanel.getControl('dashboard-selector');
    
    if (dashboardSelector) {
      dashboardSelector.options = dashboardIds.map((id) => {
        const dashboard = this.state.dashboards.get(id);
        return {
          value: id,
          label: dashboard.name || id
        };
      });
      
      // Update control
      this.controlPanel.registerControl('dashboard-selector', dashboardSelector, 'analytics-controls');
      
      // If no dashboard is selected and there are dashboards available, select the first one
      if (!this.state.selectedDashboardId && dashboardIds.length > 0) {
        this.controlPanel.setControlValue('dashboard-selector', dashboardIds[0]);
      }
    }
  }
  
  /**
   * Update dashboard details controls
   * @param {string} dashboardId - Dashboard ID
   * @private
   */
  _updateDashboardDetailsControls(dashboardId) {
    // Get dashboard
    const dashboard = this.state.dashboards.get(dashboardId);
    
    if (!dashboard) {
      return;
    }
    
    // For now, we don't have specific dashboard details controls
    // This method can be expanded in the future
  }
  
  /**
   * Update metrics controls
   * @private
   */
  _updateMetricsControls() {
    // For now, we don't have specific metrics controls
    // This method can be expanded in the future
  }
  
  /**
   * Update query results control
   * @private
   */
  _updateQueryResultsControl() {
    // Get latest query results
    const results = Array.from(this.state.queryResults.values());
    
    // Sort by timestamp (newest first)
    results.sort((a, b) => (b.timestamp || 0) - (a.timestamp || 0));
    
    // Take the latest 5 results
    const latestResults = results.slice(0, 5);
    
    // Update query results control
    this.controlPanel.setControlValue('query-results', JSON.stringify(latestResults, null, 2));
  }
  
  /**
   * Fetch metrics from the server
   * @private
   */
  async _fetchMetrics() {
    if (!this.state.isConnected) {
      if (this.options.enableLogging) {
        console.log('AnalyticsControls: Not connected');
      }
      return;
    }
    
    try {
      // Send get metrics message
      const response = await this.client.send({
        component: 'analytics',
        type: 'get-metrics'
      });
      
      if (response && response.result && response.result.metrics) {
        // Update metrics
        this.state.metrics = response.result.metrics;
        this.state.lastUpdate = Date.now();
        
        // Update metrics controls
        this._updateMetricsControls();
        
        if (this.options.enableLogging) {
          console.log('AnalyticsControls: Metrics fetched');
        }
      }
    } catch (error) {
      if (this.options.enableLogging) {
        console.error('AnalyticsControls: Error fetching metrics:', error);
      }
    }
  }
  
  /**
   * Fetch dashboards from the server
   * @private
   */
  async _fetchDashboards() {
    if (!this.state.isConnected) {
      if (this.options.enableLogging) {
        console.log('AnalyticsControls: Not connected');
      }
      return;
    }
    
    try {
      // Send get dashboards message
      const response = await this.client.send({
        component: 'analytics',
        type: 'get-dashboards'
      });
      
      if (response && response.result && response.result.dashboards) {
        // Update dashboards
        this.state.dashboards.clear();
        
        for (const dashboard of response.result.dashboards) {
          this.state.dashboards.set(dashboard.id, dashboard);
        }
        
        this.state.lastUpdate = Date.now();
        
        // Update dashboard list control
        this._updateDashboardListControl();
        
        if (this.options.enableLogging) {
          console.log('AnalyticsControls: Dashboards fetched');
        }
      }
    } catch (error) {
      if (this.options.enableLogging) {
        console.error('AnalyticsControls: Error fetching dashboards:', error);
      }
    }
  }
  
  /**
   * Fetch a dashboard from the server
   * @param {string} dashboardId - Dashboard ID
   * @private
   */
  async _fetchDashboard(dashboardId) {
    if (!this.state.isConnected) {
      if (this.options.enableLogging) {
        console.log('AnalyticsControls: Not connected');
      }
      return;
    }
    
    try {
      // Send get dashboard message
      const response = await this.client.send({
        component: 'analytics',
        type: 'get-dashboard',
        id: dashboardId
      });
      
      if (response && response.result && response.result.dashboard) {
        // Update dashboard
        this.state.dashboards.set(dashboardId, response.result.dashboard);
        this.state.lastUpdate = Date.now();
        
        // Update dashboard details controls
        this._updateDashboardDetailsControls(dashboardId);
        
        if (this.options.enableLogging) {
          console.log(`AnalyticsControls: Dashboard fetched - ${dashboardId}`);
        }
      }
    } catch (error) {
      if (this.options.enableLogging) {
        console.error(`AnalyticsControls: Error fetching dashboard ${dashboardId}:`, error);
      }
    }
  }
  
  /**
   * Execute a query
   * @param {string} query - Query to execute
   * @param {Object} [params] - Query parameters
   * @returns {Promise<Object>} - Promise that resolves with the query result
   */
  async executeQuery(query, params = {}) {
    return this.controlPanel.executeAction('execute-query', {
      query,
      params
    });
  }
  
  /**
   * Refresh metrics
   * @returns {Promise<Object>} - Promise that resolves with the metrics
   */
  async refreshMetrics() {
    return this.controlPanel.executeAction('refresh-metrics');
  }
  
  /**
   * Refresh a dashboard
   * @param {string} [id] - Dashboard ID (uses selected dashboard if not provided)
   * @returns {Promise<Object>} - Promise that resolves with the dashboard
   */
  async refreshDashboard(id) {
    return this.controlPanel.executeAction('refresh-dashboard', {
      id: id || this.state.selectedDashboardId
    });
  }
}

module.exports = AnalyticsControls;
