apiVersion: v1
kind: Service
metadata:
  name: {{ .Release.Name }}-novafuse-uac
  labels:
    app: novafuse-uac
    component: api
    release: {{ .Release.Name }}
    app.kubernetes.io/name: novafuse-uac
    app.kubernetes.io/component: api
    app.kubernetes.io/managed-by: helm
  annotations:
    cloud.google.com/neg: '{"ingress": true}'
spec:
  type: ClusterIP
  ports:
    - port: 80
      targetPort: http
      protocol: TCP
      name: http
  selector:
    app: novafuse-uac
    component: api
    release: {{ .Release.Name }}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ .Release.Name }}-novafuse-uac-sa
  labels:
    app: novafuse-uac
    component: api
    release: {{ .Release.Name }}
    app.kubernetes.io/name: novafuse-uac
    app.kubernetes.io/component: api
    app.kubernetes.io/managed-by: helm
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: {{ .Release.Name }}-novafuse-uac-role
  labels:
    app: novafuse-uac
    component: api
    release: {{ .Release.Name }}
    app.kubernetes.io/name: novafuse-uac
    app.kubernetes.io/component: api
    app.kubernetes.io/managed-by: helm
rules:
  - apiGroups: [""]
    resources: ["configmaps", "secrets"]
    verbs: ["get", "list", "watch"]
  - apiGroups: [""]
    resources: ["pods"]
    verbs: ["get", "list", "watch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ .Release.Name }}-novafuse-uac-rolebinding
  labels:
    app: novafuse-uac
    component: api
    release: {{ .Release.Name }}
    app.kubernetes.io/name: novafuse-uac
    app.kubernetes.io/component: api
    app.kubernetes.io/managed-by: helm
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: {{ .Release.Name }}-novafuse-uac-role
subjects:
  - kind: ServiceAccount
    name: {{ .Release.Name }}-novafuse-uac-sa
    namespace: {{ .Release.Namespace }}
