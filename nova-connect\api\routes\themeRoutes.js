/**
 * Theme Routes
 */

const express = require('express');
const router = express.Router();
const ThemeController = require('../controllers/ThemeController');
const { authenticate, hasPermission } = require('../middleware/authMiddleware');

// Public routes for theme CSS
router.get('/:id/css', (req, res, next) => {
  ThemeController.getThemeCss(req, res, next);
});

router.get('/organization/:organizationId/css', (req, res, next) => {
  ThemeController.getOrganizationThemeCss(req, res, next);
});

// All other routes require authentication
router.use(authenticate);

// Get all themes
router.get('/', (req, res, next) => {
  ThemeController.getAllThemes(req, res, next);
});

// Get theme by ID
router.get('/:id', (req, res, next) => {
  ThemeController.getThemeById(req, res, next);
});

// Get organization theme
router.get('/organization/:organizationId', (req, res, next) => {
  ThemeController.getOrganizationTheme(req, res, next);
});

// Routes that require system settings permission
router.use(hasPermission('system:settings'));

// Create a new theme
router.post('/', (req, res, next) => {
  ThemeController.createTheme(req, res, next);
});

// Update a theme
router.put('/:id', (req, res, next) => {
  ThemeController.updateTheme(req, res, next);
});

// Delete a theme
router.delete('/:id', (req, res, next) => {
  ThemeController.deleteTheme(req, res, next);
});

// Clone a theme
router.post('/:id/clone', (req, res, next) => {
  ThemeController.cloneTheme(req, res, next);
});

// Set organization theme
router.post('/organization/:organizationId', (req, res, next) => {
  ThemeController.setOrganizationTheme(req, res, next);
});

// Reset organization theme to default
router.delete('/organization/:organizationId', (req, res, next) => {
  ThemeController.resetOrganizationTheme(req, res, next);
});

module.exports = router;
