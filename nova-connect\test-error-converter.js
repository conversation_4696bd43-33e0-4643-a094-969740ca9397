/**
 * Simple test script for the error converter utility
 */

try {
  console.log('Loading error converter utility...');
  const { 
    convertAxiosError, 
    convertJsonSchemaError, 
    convertError 
  } = require('./src/utils/error-converter');

  const {
    UAConnectorError,
    TimeoutError,
    NetworkError,
    InvalidCredentialsError,
    ResourceNotFoundError,
    RateLimitExceededError,
    BadRequestError,
    ServerError,
    ValidationError
  } = require('./src/errors');

  console.log('Testing error converter utility...');

  // Test convertAxiosError
  console.log('\n--- Testing convertAxiosError ---');
  
  // Test with UAConnectorError
  const uacError = new UAConnectorError('Test error');
  const result1 = convertAxiosError(uacError);
  console.log('UAConnectorError conversion:', result1 === uacError);
  
  // Test with timeout error
  const timeoutError = {
    code: 'ECONNABORTED',
    message: 'timeout of 1000ms exceeded',
    config: {
      url: 'https://api.example.com',
      method: 'get'
    }
  };
  const result2 = convertAxiosError(timeoutError);
  console.log('Timeout error conversion:', result2 instanceof TimeoutError);
  console.log('Timeout error message:', result2.message);
  
  // Test with network error
  const networkError = {
    code: 'ENOTFOUND',
    message: 'getaddrinfo ENOTFOUND api.example.com',
    config: {
      url: 'https://api.example.com',
      method: 'get'
    }
  };
  const result3 = convertAxiosError(networkError);
  console.log('Network error conversion:', result3 instanceof NetworkError);
  console.log('Network error message:', result3.message);
  
  // Test with 401 Unauthorized error
  const unauthorizedError = {
    response: {
      status: 401,
      data: { message: 'Unauthorized' }
    },
    config: {
      url: 'https://api.example.com',
      method: 'get'
    }
  };
  const result4 = convertAxiosError(unauthorizedError);
  console.log('Unauthorized error conversion:', result4 instanceof InvalidCredentialsError);
  console.log('Unauthorized error status code:', result4.statusCode);
  
  // Test with 404 Not Found error
  const notFoundError = {
    response: {
      status: 404,
      data: { message: 'Not Found' }
    },
    config: {
      url: 'https://api.example.com/users/123',
      method: 'get'
    }
  };
  const result5 = convertAxiosError(notFoundError);
  console.log('Not Found error conversion:', result5 instanceof ResourceNotFoundError);
  console.log('Not Found error status code:', result5.statusCode);
  
  // Test with 429 Too Many Requests error
  const rateLimitError = {
    response: {
      status: 429,
      data: { message: 'Too Many Requests' },
      headers: {
        'retry-after': '60'
      }
    },
    config: {
      url: 'https://api.example.com',
      method: 'get'
    }
  };
  const result6 = convertAxiosError(rateLimitError);
  console.log('Rate Limit error conversion:', result6 instanceof RateLimitExceededError);
  console.log('Rate Limit error retry after:', result6.retryAfter);
  
  // Test convertJsonSchemaError
  console.log('\n--- Testing convertJsonSchemaError ---');
  
  // Test with UAConnectorError
  const result7 = convertJsonSchemaError(uacError, 'TestSchema');
  console.log('UAConnectorError schema conversion:', result7 === uacError);
  
  // Test with JSON Schema validation error
  const schemaError = {
    errors: [
      {
        keyword: 'required',
        dataPath: '.user',
        schemaPath: '#/properties/user/required',
        message: 'should have required property \'name\''
      },
      {
        keyword: 'format',
        dataPath: '.user.email',
        schemaPath: '#/properties/user/properties/email/format',
        message: 'should match format "email"'
      }
    ]
  };
  const result8 = convertJsonSchemaError(schemaError, 'UserSchema');
  console.log('Schema error conversion:', result8 instanceof ValidationError);
  console.log('Schema error validation errors count:', result8.validationErrors.length);
  
  // Test convertError
  console.log('\n--- Testing convertError ---');
  
  // Test with UAConnectorError
  const result9 = convertError(uacError);
  console.log('UAConnectorError general conversion:', result9 === uacError);
  
  // Test with Axios error
  const result10 = convertError({
    isAxiosError: true,
    response: {
      status: 404,
      data: { message: 'Not Found' }
    },
    config: {
      url: 'https://api.example.com/users/123',
      method: 'get'
    }
  });
  console.log('Axios error general conversion:', result10 instanceof ResourceNotFoundError);
  
  // Test with JSON Schema validation error
  const result11 = convertError(schemaError, { schemaName: 'UserSchema' });
  console.log('Schema error general conversion:', result11 instanceof ValidationError);
  
  // Test with generic error
  const result12 = convertError(new Error('Generic error'));
  console.log('Generic error conversion:', result12 instanceof UAConnectorError);
  console.log('Generic error message:', result12.message);

  console.log('\nAll error converter tests passed!');
} catch (error) {
  console.error('Test failed:', error);
}
