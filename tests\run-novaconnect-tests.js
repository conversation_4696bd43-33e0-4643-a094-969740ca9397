/**
 * NovaConnect Test Runner
 * 
 * This script runs all tests for NovaConnect, including unit tests,
 * integration tests, performance tests, and security tests.
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  testCategories: [
    { name: 'Unit Tests', dir: 'unit', pattern: '**/*.unit.test.js' },
    { name: 'Integration Tests', dir: 'integration', pattern: '**/*.integration.test.js' },
    { name: 'Performance Tests', dir: 'performance', pattern: '**/*.performance.test.js' },
    { name: 'Security Tests', dir: 'security', pattern: '**/*.security.test.js' }
  ],
  resultsDir: './test-results',
  coverageDir: './coverage',
  jestConfig: './jest.config.test.js'
};

// Ensure results directory exists
if (!fs.existsSync(config.resultsDir)) {
  fs.mkdirSync(config.resultsDir, { recursive: true });
}

// Initialize results
const results = {
  startTime: new Date(),
  endTime: null,
  categories: [],
  summary: {
    total: 0,
    passed: 0,
    failed: 0,
    skipped: 0
  }
};

/**
 * Run tests for a specific category
 */
function runTestCategory(category) {
  console.log(`\n=== Running ${category.name} ===\n`);
  
  const startTime = new Date();
  const categoryResult = {
    name: category.name,
    startTime,
    endTime: null,
    success: false,
    skipped: false,
    testCount: 0,
    passedCount: 0,
    failedCount: 0,
    skippedCount: 0,
    duration: 0
  };
  
  try {
    // Run Jest for the specific test directory
    const command = `npx jest --config=${config.jestConfig} --testMatch="${category.pattern}" --colors --reporters=default --reporters=jest-junit`;
    execSync(command, { stdio: 'inherit' });
    
    categoryResult.success = true;
    
    // Parse the Jest results
    const junitFile = path.join(config.resultsDir, 'junit.xml');
    if (fs.existsSync(junitFile)) {
      const junitContent = fs.readFileSync(junitFile, 'utf8');
      
      // Extract test counts using regex
      const testSuiteMatch = junitContent.match(/tests="(\d+)" failures="(\d+)" skipped="(\d+)"/);
      if (testSuiteMatch) {
        categoryResult.testCount = parseInt(testSuiteMatch[1], 10);
        categoryResult.failedCount = parseInt(testSuiteMatch[2], 10);
        categoryResult.skippedCount = parseInt(testSuiteMatch[3], 10);
        categoryResult.passedCount = categoryResult.testCount - categoryResult.failedCount - categoryResult.skippedCount;
      }
    }
    
    console.log(`✅ ${category.name} passed`);
  } catch (error) {
    categoryResult.success = false;
    console.error(`❌ ${category.name} failed`);
    
    // Try to parse the Jest results even if the tests failed
    const junitFile = path.join(config.resultsDir, 'junit.xml');
    if (fs.existsSync(junitFile)) {
      const junitContent = fs.readFileSync(junitFile, 'utf8');
      
      // Extract test counts using regex
      const testSuiteMatch = junitContent.match(/tests="(\d+)" failures="(\d+)" skipped="(\d+)"/);
      if (testSuiteMatch) {
        categoryResult.testCount = parseInt(testSuiteMatch[1], 10);
        categoryResult.failedCount = parseInt(testSuiteMatch[2], 10);
        categoryResult.skippedCount = parseInt(testSuiteMatch[3], 10);
        categoryResult.passedCount = categoryResult.testCount - categoryResult.failedCount - categoryResult.skippedCount;
      }
    }
  }
  
  categoryResult.endTime = new Date();
  categoryResult.duration = (categoryResult.endTime - categoryResult.startTime) / 1000;
  
  return categoryResult;
}

/**
 * Generate a summary report
 */
function generateSummaryReport(results) {
  const duration = (results.endTime - results.startTime) / 1000;
  
  let report = `# NovaConnect Test Summary\n\n`;
  report += `## Overview\n\n`;
  report += `- **Start Time**: ${results.startTime.toISOString()}\n`;
  report += `- **End Time**: ${results.endTime.toISOString()}\n`;
  report += `- **Duration**: ${duration.toFixed(2)} seconds\n`;
  report += `- **Total Tests**: ${results.summary.total}\n`;
  report += `- **Passed Tests**: ${results.summary.passed}\n`;
  report += `- **Failed Tests**: ${results.summary.failed}\n`;
  report += `- **Skipped Tests**: ${results.summary.skipped}\n`;
  report += `- **Pass Rate**: ${results.summary.total > 0 ? ((results.summary.passed / results.summary.total) * 100).toFixed(2) : 0}%\n\n`;
  
  report += `## Test Categories\n\n`;
  report += `| Category | Status | Tests | Passed | Failed | Skipped | Duration |\n`;
  report += `|----------|--------|-------|--------|--------|---------|----------|\n`;
  
  results.categories.forEach(category => {
    const status = category.success ? '✅ PASS' : '❌ FAIL';
    report += `| ${category.name} | ${status} | ${category.testCount} | ${category.passedCount} | ${category.failedCount} | ${category.skippedCount} | ${category.duration.toFixed(2)}s |\n`;
  });
  
  report += `\n## Coverage\n\n`;
  
  // Try to parse coverage summary
  const coverageSummaryFile = path.join(config.coverageDir, 'coverage-summary.json');
  if (fs.existsSync(coverageSummaryFile)) {
    try {
      const coverageSummary = JSON.parse(fs.readFileSync(coverageSummaryFile, 'utf8'));
      const totalCoverage = coverageSummary.total;
      
      report += `| Type | Covered | Total | Coverage |\n`;
      report += `|------|---------|-------|----------|\n`;
      report += `| Lines | ${totalCoverage.lines.covered} | ${totalCoverage.lines.total} | ${totalCoverage.lines.pct.toFixed(2)}% |\n`;
      report += `| Statements | ${totalCoverage.statements.covered} | ${totalCoverage.statements.total} | ${totalCoverage.statements.pct.toFixed(2)}% |\n`;
      report += `| Functions | ${totalCoverage.functions.covered} | ${totalCoverage.functions.total} | ${totalCoverage.functions.pct.toFixed(2)}% |\n`;
      report += `| Branches | ${totalCoverage.branches.covered} | ${totalCoverage.branches.total} | ${totalCoverage.branches.pct.toFixed(2)}% |\n`;
    } catch (error) {
      report += `Coverage data not available.\n`;
    }
  } else {
    report += `Coverage data not available.\n`;
  }
  
  return report;
}

// Main execution
async function main() {
  console.log('Starting NovaConnect test suite...');
  
  // Run each test category
  for (const category of config.testCategories) {
    const categoryResult = runTestCategory(category);
    results.categories.push(categoryResult);
    
    // Update summary
    results.summary.total += categoryResult.testCount;
    results.summary.passed += categoryResult.passedCount;
    results.summary.failed += categoryResult.failedCount;
    results.summary.skipped += categoryResult.skippedCount;
  }
  
  results.endTime = new Date();
  
  // Generate summary report
  const summaryReport = generateSummaryReport(results);
  fs.writeFileSync(path.join(config.resultsDir, 'novaconnect-summary.md'), summaryReport);
  
  // Print summary
  console.log('\n=== Test Summary ===\n');
  console.log(`Total Tests: ${results.summary.total}`);
  console.log(`Passed: ${results.summary.passed}`);
  console.log(`Failed: ${results.summary.failed}`);
  console.log(`Skipped: ${results.summary.skipped}`);
  console.log(`Pass Rate: ${results.summary.total > 0 ? ((results.summary.passed / results.summary.total) * 100).toFixed(2) : 0}%`);
  
  // Exit with appropriate code
  process.exit(results.summary.failed > 0 ? 1 : 0);
}

// Run the tests
main().catch(error => {
  console.error('Error running tests:', error);
  process.exit(1);
});
