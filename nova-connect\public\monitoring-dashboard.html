<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>NovaFuse UAC Monitoring Dashboard</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 0;
      background-color: #f5f7fa;
      color: #333;
    }
    
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    
    header {
      background-color: #0066cc;
      color: white;
      padding: 20px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    header h1 {
      margin: 0;
      font-size: 24px;
    }
    
    .dashboard-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    }
    
    .dashboard-header h2 {
      margin: 0;
    }
    
    .refresh-button {
      background-color: #0066cc;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      font-weight: bold;
    }
    
    .refresh-button:hover {
      background-color: #0055aa;
    }
    
    .dashboard-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
      gap: 20px;
      margin-bottom: 30px;
    }
    
    .dashboard-card {
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      padding: 20px;
    }
    
    .dashboard-card h3 {
      margin-top: 0;
      margin-bottom: 15px;
      color: #0066cc;
      border-bottom: 1px solid #eee;
      padding-bottom: 10px;
    }
    
    .status-indicator {
      display: inline-block;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      margin-right: 8px;
    }
    
    .status-ok {
      background-color: #4CAF50;
    }
    
    .status-warn {
      background-color: #FF9800;
    }
    
    .status-error {
      background-color: #F44336;
    }
    
    .metric-row {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;
    }
    
    .metric-label {
      font-weight: bold;
    }
    
    .metric-value {
      text-align: right;
    }
    
    .chart-container {
      height: 200px;
      margin-top: 15px;
    }
    
    .table-container {
      overflow-x: auto;
    }
    
    table {
      width: 100%;
      border-collapse: collapse;
    }
    
    th, td {
      padding: 8px 12px;
      text-align: left;
      border-bottom: 1px solid #eee;
    }
    
    th {
      background-color: #f5f7fa;
      font-weight: bold;
    }
    
    tr:hover {
      background-color: #f9f9f9;
    }
    
    .loading {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 200px;
    }
    
    .spinner {
      border: 4px solid rgba(0, 0, 0, 0.1);
      border-left-color: #0066cc;
      border-radius: 50%;
      width: 30px;
      height: 30px;
      animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
      to { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <header>
    <div class="container">
      <h1>NovaFuse Universal API Connector - Monitoring Dashboard</h1>
    </div>
  </header>
  
  <div class="container">
    <div class="dashboard-header">
      <h2>System Overview</h2>
      <div>
        <span id="last-updated">Last updated: Never</span>
        <button id="refresh-button" class="refresh-button">Refresh</button>
      </div>
    </div>
    
    <div class="dashboard-grid">
      <div class="dashboard-card">
        <h3>Health Status</h3>
        <div id="health-status">
          <div class="loading">
            <div class="spinner"></div>
          </div>
        </div>
      </div>
      
      <div class="dashboard-card">
        <h3>System Information</h3>
        <div id="system-info">
          <div class="loading">
            <div class="spinner"></div>
          </div>
        </div>
      </div>
      
      <div class="dashboard-card">
        <h3>Memory Usage</h3>
        <div id="memory-usage">
          <div class="loading">
            <div class="spinner"></div>
          </div>
        </div>
      </div>
      
      <div class="dashboard-card">
        <h3>CPU Usage</h3>
        <div id="cpu-usage">
          <div class="loading">
            <div class="spinner"></div>
          </div>
        </div>
      </div>
    </div>
    
    <h2>Request Metrics</h2>
    
    <div class="dashboard-grid">
      <div class="dashboard-card">
        <h3>Request Volume</h3>
        <div id="request-volume">
          <div class="loading">
            <div class="spinner"></div>
          </div>
        </div>
      </div>
      
      <div class="dashboard-card">
        <h3>Response Time</h3>
        <div id="response-time">
          <div class="loading">
            <div class="spinner"></div>
          </div>
        </div>
      </div>
      
      <div class="dashboard-card">
        <h3>Requests by Method</h3>
        <div id="requests-by-method">
          <div class="loading">
            <div class="spinner"></div>
          </div>
        </div>
      </div>
      
      <div class="dashboard-card">
        <h3>Requests by Status</h3>
        <div id="requests-by-status">
          <div class="loading">
            <div class="spinner"></div>
          </div>
        </div>
      </div>
    </div>
    
    <h2>Error Metrics</h2>
    
    <div class="dashboard-grid">
      <div class="dashboard-card">
        <h3>Error Rate</h3>
        <div id="error-rate">
          <div class="loading">
            <div class="spinner"></div>
          </div>
        </div>
      </div>
      
      <div class="dashboard-card">
        <h3>Errors by Type</h3>
        <div id="errors-by-type">
          <div class="loading">
            <div class="spinner"></div>
          </div>
        </div>
      </div>
      
      <div class="dashboard-card">
        <h3>Errors by Code</h3>
        <div id="errors-by-code">
          <div class="loading">
            <div class="spinner"></div>
          </div>
        </div>
      </div>
      
      <div class="dashboard-card">
        <h3>Top Error Paths</h3>
        <div id="top-error-paths">
          <div class="loading">
            <div class="spinner"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <script>
    // Dashboard data
    let dashboardData = null;
    
    // Format bytes to human-readable format
    function formatBytes(bytes, decimals = 2) {
      if (bytes === 0) return '0 Bytes';
      
      const k = 1024;
      const dm = decimals < 0 ? 0 : decimals;
      const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
      
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      
      return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
    }
    
    // Format number with commas
    function formatNumber(num) {
      return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }
    
    // Format duration in milliseconds
    function formatDuration(ms) {
      if (ms < 1) {
        return (ms * 1000).toFixed(2) + ' μs';
      } else if (ms < 1000) {
        return ms.toFixed(2) + ' ms';
      } else {
        return (ms / 1000).toFixed(2) + ' s';
      }
    }
    
    // Update health status card
    function updateHealthStatus() {
      if (!dashboardData || !dashboardData.health) return;
      
      const health = dashboardData.health;
      const healthStatusEl = document.getElementById('health-status');
      
      let statusClass = 'status-ok';
      if (health.status === 'error') {
        statusClass = 'status-error';
      } else if (health.status === 'warn') {
        statusClass = 'status-warn';
      }
      
      let html = `
        <div class="metric-row">
          <div class="metric-label">
            <span class="status-indicator ${statusClass}"></span>
            Overall Status
          </div>
          <div class="metric-value">${health.status.toUpperCase()}</div>
        </div>
        <div class="metric-row">
          <div class="metric-label">Last Check</div>
          <div class="metric-value">${new Date(health.timestamp).toLocaleTimeString()}</div>
        </div>
        <div class="metric-row">
          <div class="metric-label">Duration</div>
          <div class="metric-value">${formatDuration(parseFloat(health.duration) * 1000)}</div>
        </div>
      `;
      
      // Add individual checks
      if (health.checks) {
        html += '<h4>Individual Checks</h4>';
        
        for (const [name, check] of Object.entries(health.checks)) {
          let checkStatusClass = 'status-ok';
          if (check.status === 'error') {
            checkStatusClass = 'status-error';
          } else if (check.status === 'warn') {
            checkStatusClass = 'status-warn';
          }
          
          html += `
            <div class="metric-row">
              <div class="metric-label">
                <span class="status-indicator ${checkStatusClass}"></span>
                ${name}
              </div>
              <div class="metric-value">${formatDuration(parseFloat(check.duration) * 1000)}</div>
            </div>
          `;
          
          if (check.message) {
            html += `<div class="metric-row">
              <div class="metric-value" style="color: ${check.status === 'error' ? '#F44336' : '#FF9800'}">
                ${check.message}
              </div>
            </div>`;
          }
        }
      }
      
      healthStatusEl.innerHTML = html;
    }
    
    // Update system info card
    function updateSystemInfo() {
      if (!dashboardData || !dashboardData.system) return;
      
      const system = dashboardData.system;
      const systemInfoEl = document.getElementById('system-info');
      
      const html = `
        <div class="metric-row">
          <div class="metric-label">Hostname</div>
          <div class="metric-value">${system.hostname}</div>
        </div>
        <div class="metric-row">
          <div class="metric-label">Platform</div>
          <div class="metric-value">${system.platform} (${system.arch})</div>
        </div>
        <div class="metric-row">
          <div class="metric-label">Node.js Version</div>
          <div class="metric-value">${system.process.version}</div>
        </div>
        <div class="metric-row">
          <div class="metric-label">System Uptime</div>
          <div class="metric-value">${formatDuration(system.uptime * 1000)}</div>
        </div>
        <div class="metric-row">
          <div class="metric-label">Process Uptime</div>
          <div class="metric-value">${formatDuration(system.process.uptime * 1000)}</div>
        </div>
        <div class="metric-row">
          <div class="metric-label">Process ID</div>
          <div class="metric-value">${system.process.pid}</div>
        </div>
      `;
      
      systemInfoEl.innerHTML = html;
    }
    
    // Update memory usage card
    function updateMemoryUsage() {
      if (!dashboardData || !dashboardData.system) return;
      
      const system = dashboardData.system;
      const memoryUsageEl = document.getElementById('memory-usage');
      
      const totalMemory = system.memory.total;
      const usedMemory = system.memory.used;
      const freeMemory = system.memory.free;
      const usedPercentage = (usedMemory / totalMemory * 100).toFixed(1);
      
      const html = `
        <div class="metric-row">
          <div class="metric-label">Total Memory</div>
          <div class="metric-value">${formatBytes(totalMemory)}</div>
        </div>
        <div class="metric-row">
          <div class="metric-label">Used Memory</div>
          <div class="metric-value">${formatBytes(usedMemory)} (${usedPercentage}%)</div>
        </div>
        <div class="metric-row">
          <div class="metric-label">Free Memory</div>
          <div class="metric-value">${formatBytes(freeMemory)}</div>
        </div>
        <div style="margin-top: 15px; background-color: #f5f7fa; border-radius: 4px; height: 20px; overflow: hidden;">
          <div style="background-color: ${usedPercentage > 90 ? '#F44336' : usedPercentage > 70 ? '#FF9800' : '#4CAF50'}; height: 100%; width: ${usedPercentage}%;"></div>
        </div>
        <h4>Process Memory</h4>
        <div class="metric-row">
          <div class="metric-label">RSS</div>
          <div class="metric-value">${formatBytes(system.process.memoryUsage.rss)}</div>
        </div>
        <div class="metric-row">
          <div class="metric-label">Heap Total</div>
          <div class="metric-value">${formatBytes(system.process.memoryUsage.heapTotal)}</div>
        </div>
        <div class="metric-row">
          <div class="metric-label">Heap Used</div>
          <div class="metric-value">${formatBytes(system.process.memoryUsage.heapUsed)}</div>
        </div>
      `;
      
      memoryUsageEl.innerHTML = html;
    }
    
    // Update CPU usage card
    function updateCpuUsage() {
      if (!dashboardData || !dashboardData.system) return;
      
      const system = dashboardData.system;
      const cpuUsageEl = document.getElementById('cpu-usage');
      
      const cpuCount = system.cpu.count;
      const loadAvg = system.cpu.load;
      const loadPercentage = (loadAvg[0] / cpuCount * 100).toFixed(1);
      
      const html = `
        <div class="metric-row">
          <div class="metric-label">CPU Count</div>
          <div class="metric-value">${cpuCount}</div>
        </div>
        <div class="metric-row">
          <div class="metric-label">CPU Model</div>
          <div class="metric-value">${system.cpu.model}</div>
        </div>
        <div class="metric-row">
          <div class="metric-label">Load Average (1m)</div>
          <div class="metric-value">${loadAvg[0].toFixed(2)}</div>
        </div>
        <div class="metric-row">
          <div class="metric-label">Load Average (5m)</div>
          <div class="metric-value">${loadAvg[1].toFixed(2)}</div>
        </div>
        <div class="metric-row">
          <div class="metric-label">Load Average (15m)</div>
          <div class="metric-value">${loadAvg[2].toFixed(2)}</div>
        </div>
        <div class="metric-row">
          <div class="metric-label">CPU Usage</div>
          <div class="metric-value">${loadPercentage}%</div>
        </div>
        <div style="margin-top: 15px; background-color: #f5f7fa; border-radius: 4px; height: 20px; overflow: hidden;">
          <div style="background-color: ${loadPercentage > 90 ? '#F44336' : loadPercentage > 70 ? '#FF9800' : '#4CAF50'}; height: 100%; width: ${Math.min(100, loadPercentage)}%;"></div>
        </div>
      `;
      
      cpuUsageEl.innerHTML = html;
    }
    
    // Update request volume card
    function updateRequestVolume() {
      if (!dashboardData || !dashboardData.requests) return;
      
      const requests = dashboardData.requests;
      const requestVolumeEl = document.getElementById('request-volume');
      
      const html = `
        <div class="metric-row">
          <div class="metric-label">Total Requests</div>
          <div class="metric-value">${formatNumber(requests.total)}</div>
        </div>
        <div class="metric-row">
          <div class="metric-label">Success Rate</div>
          <div class="metric-value">
            ${((1 - (dashboardData.errors.total / Math.max(1, requests.total))) * 100).toFixed(2)}%
          </div>
        </div>
        <div class="metric-row">
          <div class="metric-label">Error Rate</div>
          <div class="metric-value">
            ${((dashboardData.errors.total / Math.max(1, requests.total)) * 100).toFixed(2)}%
          </div>
        </div>
      `;
      
      requestVolumeEl.innerHTML = html;
    }
    
    // Update response time card
    function updateResponseTime() {
      if (!dashboardData || !dashboardData.requests) return;
      
      const requests = dashboardData.requests;
      const responseTimeEl = document.getElementById('response-time');
      
      const html = `
        <div class="metric-row">
          <div class="metric-label">Average Response Time</div>
          <div class="metric-value">${formatDuration(requests.responseTime.avg * 1000)}</div>
        </div>
        <div class="metric-row">
          <div class="metric-label">50th Percentile (p50)</div>
          <div class="metric-value">${formatDuration(requests.responseTime.p50 * 1000)}</div>
        </div>
        <div class="metric-row">
          <div class="metric-label">95th Percentile (p95)</div>
          <div class="metric-value">${formatDuration(requests.responseTime.p95 * 1000)}</div>
        </div>
        <div class="metric-row">
          <div class="metric-label">99th Percentile (p99)</div>
          <div class="metric-value">${formatDuration(requests.responseTime.p99 * 1000)}</div>
        </div>
      `;
      
      responseTimeEl.innerHTML = html;
    }
    
    // Update requests by method card
    function updateRequestsByMethod() {
      if (!dashboardData || !dashboardData.requests) return;
      
      const requests = dashboardData.requests;
      const requestsByMethodEl = document.getElementById('requests-by-method');
      
      let html = '<div class="table-container"><table><thead><tr><th>Method</th><th>Count</th><th>Percentage</th></tr></thead><tbody>';
      
      const methods = Object.entries(requests.byMethod || {})
        .sort((a, b) => b[1] - a[1]);
      
      for (const [method, count] of methods) {
        const percentage = (count / Math.max(1, requests.total) * 100).toFixed(1);
        html += `<tr><td>${method}</td><td>${formatNumber(count)}</td><td>${percentage}%</td></tr>`;
      }
      
      html += '</tbody></table></div>';
      
      requestsByMethodEl.innerHTML = html;
    }
    
    // Update requests by status card
    function updateRequestsByStatus() {
      if (!dashboardData || !dashboardData.requests) return;
      
      const requests = dashboardData.requests;
      const requestsByStatusEl = document.getElementById('requests-by-status');
      
      let html = '<div class="table-container"><table><thead><tr><th>Status</th><th>Count</th><th>Percentage</th></tr></thead><tbody>';
      
      const statuses = Object.entries(requests.byStatus || {})
        .sort((a, b) => b[1] - a[1]);
      
      for (const [status, count] of statuses) {
        const percentage = (count / Math.max(1, requests.total) * 100).toFixed(1);
        const statusClass = status.startsWith('2') ? 'status-ok' : status.startsWith('4') ? 'status-warn' : 'status-error';
        
        html += `<tr>
          <td><span class="status-indicator ${statusClass}"></span>${status}</td>
          <td>${formatNumber(count)}</td>
          <td>${percentage}%</td>
        </tr>`;
      }
      
      html += '</tbody></table></div>';
      
      requestsByStatusEl.innerHTML = html;
    }
    
    // Update error rate card
    function updateErrorRate() {
      if (!dashboardData || !dashboardData.errors) return;
      
      const errors = dashboardData.errors;
      const requests = dashboardData.requests;
      const errorRateEl = document.getElementById('error-rate');
      
      const errorRate = (errors.total / Math.max(1, requests.total) * 100).toFixed(2);
      
      const html = `
        <div class="metric-row">
          <div class="metric-label">Total Errors</div>
          <div class="metric-value">${formatNumber(errors.total)}</div>
        </div>
        <div class="metric-row">
          <div class="metric-label">Error Rate</div>
          <div class="metric-value">${errorRate}%</div>
        </div>
        <div style="margin-top: 15px; background-color: #f5f7fa; border-radius: 4px; height: 20px; overflow: hidden;">
          <div style="background-color: ${errorRate > 5 ? '#F44336' : errorRate > 1 ? '#FF9800' : '#4CAF50'}; height: 100%; width: ${Math.min(100, errorRate * 5)}%;"></div>
        </div>
      `;
      
      errorRateEl.innerHTML = html;
    }
    
    // Update errors by type card
    function updateErrorsByType() {
      if (!dashboardData || !dashboardData.errors) return;
      
      const errors = dashboardData.errors;
      const errorsByTypeEl = document.getElementById('errors-by-type');
      
      let html = '<div class="table-container"><table><thead><tr><th>Error Type</th><th>Count</th><th>Percentage</th></tr></thead><tbody>';
      
      const types = Object.entries(errors.byType || {})
        .sort((a, b) => b[1] - a[1]);
      
      for (const [type, count] of types) {
        const percentage = (count / Math.max(1, errors.total) * 100).toFixed(1);
        html += `<tr><td>${type}</td><td>${formatNumber(count)}</td><td>${percentage}%</td></tr>`;
      }
      
      html += '</tbody></table></div>';
      
      errorsByTypeEl.innerHTML = html;
    }
    
    // Update errors by code card
    function updateErrorsByCode() {
      if (!dashboardData || !dashboardData.errors) return;
      
      const errors = dashboardData.errors;
      const errorsByCodeEl = document.getElementById('errors-by-code');
      
      let html = '<div class="table-container"><table><thead><tr><th>Error Code</th><th>Count</th><th>Percentage</th></tr></thead><tbody>';
      
      const codes = Object.entries(errors.byCode || {})
        .sort((a, b) => b[1] - a[1]);
      
      for (const [code, count] of codes) {
        const percentage = (count / Math.max(1, errors.total) * 100).toFixed(1);
        html += `<tr><td>${code}</td><td>${formatNumber(count)}</td><td>${percentage}%</td></tr>`;
      }
      
      html += '</tbody></table></div>';
      
      errorsByCodeEl.innerHTML = html;
    }
    
    // Update top error paths card
    function updateTopErrorPaths() {
      if (!dashboardData || !dashboardData.errors) return;
      
      const errors = dashboardData.errors;
      const topErrorPathsEl = document.getElementById('top-error-paths');
      
      let html = '<div class="table-container"><table><thead><tr><th>Path</th><th>Errors</th><th>Top Error Type</th></tr></thead><tbody>';
      
      const paths = Object.entries(errors.byPath || {})
        .sort((a, b) => b[1].total - a[1].total)
        .slice(0, 5);
      
      for (const [path, pathErrors] of paths) {
        // Get top error type
        const topErrorType = Object.entries(pathErrors.byType || {})
          .sort((a, b) => b[1] - a[1])
          .map(([type]) => type)[0] || 'Unknown';
        
        html += `<tr><td>${path}</td><td>${formatNumber(pathErrors.total)}</td><td>${topErrorType}</td></tr>`;
      }
      
      html += '</tbody></table></div>';
      
      topErrorPathsEl.innerHTML = html;
    }
    
    // Update all dashboard cards
    function updateDashboard() {
      updateHealthStatus();
      updateSystemInfo();
      updateMemoryUsage();
      updateCpuUsage();
      updateRequestVolume();
      updateResponseTime();
      updateRequestsByMethod();
      updateRequestsByStatus();
      updateErrorRate();
      updateErrorsByType();
      updateErrorsByCode();
      updateTopErrorPaths();
      
      // Update last updated timestamp
      document.getElementById('last-updated').textContent = `Last updated: ${new Date().toLocaleTimeString()}`;
    }
    
    // Fetch dashboard data
    async function fetchDashboardData() {
      try {
        const response = await fetch('/monitoring/dashboard');
        dashboardData = await response.json();
        updateDashboard();
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        alert('Error fetching dashboard data. Please try again.');
      }
    }
    
    // Initialize dashboard
    function initDashboard() {
      // Fetch initial data
      fetchDashboardData();
      
      // Set up refresh button
      document.getElementById('refresh-button').addEventListener('click', fetchDashboardData);
      
      // Set up auto-refresh
      setInterval(fetchDashboardData, 60000); // Refresh every minute
    }
    
    // Initialize when DOM is loaded
    document.addEventListener('DOMContentLoaded', initDashboard);
  </script>
</body>
</html>
