/**
 * Cyber-Safety Medical Engine (CSME)
 * 
 * This module exports all components of the CSME system.
 */

const TelomereErosionPrediction = require('./telomere-erosion-prediction');
const MTORActivationMonitoring = require('./mtor-activation-monitoring');
const InflammationCascadeModeling = require('./inflammation-cascade-modeling');

/**
 * Create a basic CSME system
 * @param {Object} options - Configuration options
 * @returns {Object} - CSME system components
 */
function createCSMESystem(options = {}) {
  // Create components
  const telomereErosionPrediction = new TelomereErosionPrediction(options.telomereErosionPredictionOptions);
  const mtorActivationMonitoring = new MTORActivationMonitoring(options.mtorActivationMonitoringOptions);
  const inflammationCascadeModeling = new InflammationCascadeModeling(options.inflammationCascadeModelingOptions);
  
  return {
    telomereErosionPrediction,
    mtorActivationMonitoring,
    inflammationCascadeModeling
  };
}

/**
 * Create an enhanced CSME system with integrated components
 * @param {Object} options - Configuration options
 * @returns {Object} - Enhanced CSME system
 */
function createEnhancedCSMESystem(options = {}) {
  // Create basic system
  const csmeSystem = createCSMESystem(options);
  
  // Set up event listeners for integration
  
  // When inflammation changes, update telomere erosion
  csmeSystem.inflammationCascadeModeling.on('inflammation-update', (data) => {
    // High inflammation accelerates telomere erosion
    if (data.inflammationLevel > 0.7) {
      csmeSystem.telomereErosionPrediction.addErosionFactor({
        type: 'inflammation',
        impact: data.inflammationLevel * 0.8,
        duration: 24,
        description: 'Erosion from high inflammation'
      });
    }
  });
  
  // When mTOR activation changes, update inflammation
  csmeSystem.mtorActivationMonitoring.on('activation-update', (data) => {
    // High mTOR activation can trigger inflammation
    if (data.activationLevel > 0.7) {
      csmeSystem.inflammationCascadeModeling.addTrigger({
        type: 'metabolic',
        strength: data.activationLevel * 0.6,
        duration: 24,
        description: 'Inflammation from high mTOR activation'
      });
    }
  });
  
  // When telomere length changes, update mTOR activation
  csmeSystem.telomereErosionPrediction.on('telomere-update', (data) => {
    // Short telomeres can affect mTOR signaling
    if (data.telomereLength < 0.3) {
      csmeSystem.mtorActivationMonitoring.addModulator({
        type: 'cellular_senescence',
        effect: 'activation',
        strength: (1 - data.telomereLength) * 0.7,
        duration: 48,
        description: 'mTOR activation from short telomeres'
      });
    }
  });
  
  // Add enhanced methods
  const enhancedSystem = {
    ...csmeSystem,
    
    /**
     * Start all components
     * @returns {boolean} - Success status
     */
    start() {
      const tepStarted = csmeSystem.telomereErosionPrediction.start();
      const mamStarted = csmeSystem.mtorActivationMonitoring.start();
      const icmStarted = csmeSystem.inflammationCascadeModeling.start();
      
      return tepStarted && mamStarted && icmStarted;
    },
    
    /**
     * Stop all components
     * @returns {boolean} - Success status
     */
    stop() {
      const tepStopped = csmeSystem.telomereErosionPrediction.stop();
      const mamStopped = csmeSystem.mtorActivationMonitoring.stop();
      const icmStopped = csmeSystem.inflammationCascadeModeling.stop();
      
      return tepStopped && mamStopped && icmStopped;
    },
    
    /**
     * Get unified state from all components
     * @returns {Object} - Unified state
     */
    getUnifiedState() {
      return {
        telomereErosionPrediction: csmeSystem.telomereErosionPrediction.getState(),
        mtorActivationMonitoring: csmeSystem.mtorActivationMonitoring.getState(),
        inflammationCascadeModeling: csmeSystem.inflammationCascadeModeling.getState(),
        timestamp: Date.now()
      };
    },
    
    /**
     * Get unified metrics from all components
     * @returns {Object} - Unified metrics
     */
    getUnifiedMetrics() {
      return {
        telomereErosionPrediction: csmeSystem.telomereErosionPrediction.getMetrics(),
        mtorActivationMonitoring: csmeSystem.mtorActivationMonitoring.getMetrics(),
        inflammationCascadeModeling: csmeSystem.inflammationCascadeModeling.getMetrics(),
        timestamp: Date.now()
      };
    },
    
    /**
     * Calculate biological entropy (Ψₜ)
     * @returns {number} - Biological entropy value
     */
    calculateBiologicalEntropy() {
      // Get current values
      const telomereLength = csmeSystem.telomereErosionPrediction.getState().telomereLength;
      const mtorActivation = csmeSystem.mtorActivationMonitoring.getState().activationLevel;
      const inflammationLevel = csmeSystem.inflammationCascadeModeling.getState().inflammationLevel;
      
      // Calculate biological entropy using 18/82 principle
      // 18% weight to telomere length (inverted), 82% weight to the combination of mTOR activation and inflammation
      const biologicalEntropy = (
        0.18 * (1 - telomereLength) +
        0.82 * ((mtorActivation + inflammationLevel) / 2)
      );
      
      return this._clamp(biologicalEntropy);
    },
    
    /**
     * Process biological data
     * @param {Object} data - Biological data
     * @returns {Object} - Processing result
     */
    processBiologicalData(data) {
      const result = {
        timestamp: Date.now(),
        updates: {}
      };
      
      // Process telomere data
      if (data.telomere) {
        if (data.telomere.length !== undefined) {
          const telomereLength = csmeSystem.telomereErosionPrediction.setTelomereLength(data.telomere.length);
          result.updates.telomereLength = telomereLength;
        }
        
        if (data.telomere.biologicalAge !== undefined) {
          const ageData = csmeSystem.telomereErosionPrediction.setBiologicalAge(data.telomere.biologicalAge);
          result.updates.biologicalAge = ageData.biologicalAge;
        }
        
        if (data.telomere.chronologicalAge !== undefined) {
          const ageData = csmeSystem.telomereErosionPrediction.setChronologicalAge(data.telomere.chronologicalAge);
          result.updates.chronologicalAge = ageData.chronologicalAge;
        }
        
        if (data.telomere.erosionFactors) {
          for (const factor of data.telomere.erosionFactors) {
            csmeSystem.telomereErosionPrediction.addErosionFactor(factor);
          }
          result.updates.erosionFactorsAdded = data.telomere.erosionFactors.length;
        }
      }
      
      // Process mTOR data
      if (data.mtor) {
        if (data.mtor.activationLevel !== undefined) {
          const activationLevel = csmeSystem.mtorActivationMonitoring.setActivationLevel(data.mtor.activationLevel);
          result.updates.mtorActivationLevel = activationLevel;
        }
        
        if (data.mtor.modulators) {
          for (const modulator of data.mtor.modulators) {
            csmeSystem.mtorActivationMonitoring.addModulator(modulator);
          }
          result.updates.modulatorsAdded = data.mtor.modulators.length;
        }
      }
      
      // Process inflammation data
      if (data.inflammation) {
        if (data.inflammation.level !== undefined) {
          const inflammationLevel = csmeSystem.inflammationCascadeModeling.setInflammationLevel(data.inflammation.level);
          result.updates.inflammationLevel = inflammationLevel;
        }
        
        if (data.inflammation.triggers) {
          for (const trigger of data.inflammation.triggers) {
            csmeSystem.inflammationCascadeModeling.addTrigger(trigger);
          }
          result.updates.triggersAdded = data.inflammation.triggers.length;
        }
        
        if (data.inflammation.mediators) {
          for (const mediator of data.inflammation.mediators) {
            csmeSystem.inflammationCascadeModeling.addMediator(mediator);
          }
          result.updates.mediatorsAdded = data.inflammation.mediators.length;
        }
      }
      
      // Calculate biological entropy
      result.biologicalEntropy = this.calculateBiologicalEntropy();
      
      return result;
    },
    
    /**
     * Get biological health assessment
     * @returns {Object} - Health assessment
     */
    getBiologicalHealthAssessment() {
      const telomereState = csmeSystem.telomereErosionPrediction.getState();
      const mtorState = csmeSystem.mtorActivationMonitoring.getState();
      const inflammationState = csmeSystem.inflammationCascadeModeling.getState();
      
      // Calculate biological entropy
      const biologicalEntropy = this.calculateBiologicalEntropy();
      
      // Determine health status
      let healthStatus = 'good';
      if (biologicalEntropy >= 0.95) {
        healthStatus = 'critical';
      } else if (biologicalEntropy >= 0.8) {
        healthStatus = 'poor';
      } else if (biologicalEntropy >= 0.6) {
        healthStatus = 'fair';
      }
      
      // Calculate biological age acceleration
      const ageAcceleration = telomereState.ageAcceleration;
      
      // Calculate health score (inverse of entropy)
      const healthScore = 1 - biologicalEntropy;
      
      return {
        biologicalEntropy,
        healthStatus,
        healthScore,
        ageAcceleration,
        telomereLength: telomereState.telomereLength,
        telomereStatus: telomereState.telomereStatus,
        mtorActivation: mtorState.activationLevel,
        mtorStatus: mtorState.activationStatus,
        inflammationLevel: inflammationState.inflammationLevel,
        inflammationStatus: inflammationState.inflammationStatus,
        systemicEffects: {
          ...mtorState.downstreamEffects,
          ...inflammationState.systemicEffects
        },
        affectedSystems: inflammationState.affectedSystems,
        timestamp: Date.now()
      };
    },
    
    /**
     * Predict health trajectory
     * @param {number} timeUnits - Number of time units to predict
     * @returns {Object} - Prediction results
     */
    predictHealthTrajectory(timeUnits = 12) {
      // Predict telomere length
      const telomerePredictions = csmeSystem.telomereErosionPrediction.predictTelomereLength(timeUnits);
      
      // Calculate predicted biological entropy for each time point
      const predictions = telomerePredictions.map(prediction => {
        // Estimate mTOR activation and inflammation based on telomere length
        const estimatedMTOR = 0.5 + (1 - prediction.telomereLength) * 0.3;
        const estimatedInflammation = 0.3 + (1 - prediction.telomereLength) * 0.4;
        
        // Calculate biological entropy
        const biologicalEntropy = (
          0.18 * (1 - prediction.telomereLength) +
          0.82 * ((estimatedMTOR + estimatedInflammation) / 2)
        );
        
        // Determine health status
        let healthStatus = 'good';
        if (biologicalEntropy >= 0.95) {
          healthStatus = 'critical';
        } else if (biologicalEntropy >= 0.8) {
          healthStatus = 'poor';
        } else if (biologicalEntropy >= 0.6) {
          healthStatus = 'fair';
        }
        
        return {
          timeUnit: prediction.timeUnit,
          telomereLength: prediction.telomereLength,
          estimatedMTOR,
          estimatedInflammation,
          biologicalEntropy: this._clamp(biologicalEntropy),
          healthStatus,
          timestamp: prediction.timestamp
        };
      });
      
      return {
        predictions,
        currentState: this.getBiologicalHealthAssessment()
      };
    },
    
    /**
     * Clamp value between 0 and 1
     * @param {number} value - Value to clamp
     * @returns {number} - Clamped value
     * @private
     */
    _clamp(value) {
      return Math.max(0, Math.min(1, value));
    }
  };
  
  return enhancedSystem;
}

module.exports = {
  TelomereErosionPrediction,
  MTORActivationMonitoring,
  InflammationCascadeModeling,
  createCSMESystem,
  createEnhancedCSMESystem
};
