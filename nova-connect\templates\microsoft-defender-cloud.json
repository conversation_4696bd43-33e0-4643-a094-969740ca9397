{"metadata": {"name": "Microsoft Defender for Cloud", "version": "1.0.0", "category": "Cloud Security", "description": "Connect to Microsoft Defender for Cloud API", "author": "NovaFuse", "tags": ["microsoft", "azure", "security", "defender"], "created": "2023-06-01T00:00:00Z", "updated": "2023-06-01T00:00:00Z", "icon": "https://storage.googleapis.com/novafuse-icons/microsoft-defender.png", "documentationUrl": "https://docs.microsoft.com/en-us/rest/api/defenderforcloud/"}, "authentication": {"type": "OAUTH2", "fields": {"tenantId": {"type": "string", "description": "Azure AD Tenant ID", "required": true}, "clientId": {"type": "string", "description": "Azure AD Application (client) ID", "required": true}, "clientSecret": {"type": "string", "description": "Azure AD Application Client Secret", "required": true, "sensitive": true}, "subscriptionId": {"type": "string", "description": "Azure Subscription ID", "required": true}}, "testConnection": {"endpoint": "subscriptions/{subscriptionId}/providers/Microsoft.Security/assessments", "method": "GET", "expectedResponse": {"status": 200}}, "oauth2Config": {"authorizationUrl": "https://login.microsoftonline.com/{tenantId}/oauth2/v2.0/authorize", "tokenUrl": "https://login.microsoftonline.com/{tenantId}/oauth2/v2.0/token", "scopes": ["https://management.azure.com/.default"], "grantType": "client_credentials"}}, "configuration": {"baseUrl": "https://management.azure.com", "headers": {"Content-Type": "application/json", "Accept": "application/json"}, "rateLimit": {"requests": 120, "period": "1m"}, "timeout": 30000, "retryPolicy": {"maxRetries": 3, "backoffStrategy": "exponential"}}, "endpoints": [{"id": "list-assessments", "name": "List Security Assessments", "description": "List security assessments from Microsoft Defender for Cloud", "path": "subscriptions/{subscriptionId}/providers/Microsoft.Security/assessments", "method": "GET", "parameters": {"path": {"subscriptionId": {"type": "string", "description": "Subscription ID", "required": true}}, "query": {"$filter": {"type": "string", "description": "OData filter expression"}, "$top": {"type": "integer", "description": "Number of records to return", "default": 100}, "$skipToken": {"type": "string", "description": "Skip token for pagination"}, "api-version": {"type": "string", "description": "API version", "default": "2020-01-01", "required": true}}}, "pagination": {"type": "token", "parameters": {"nextPageToken": "nextLink", "pageToken": "$skipToken"}}, "response": {"successCode": 200, "dataPath": "value", "schema": {"type": "object", "properties": {"value": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "properties": {"type": "object", "properties": {"displayName": {"type": "string"}, "status": {"type": "object", "properties": {"code": {"type": "string"}, "cause": {"type": "string"}, "description": {"type": "string"}}}, "resourceDetails": {"type": "object"}, "additionalData": {"type": "object"}}}}}}, "nextLink": {"type": "string"}}}}}, {"id": "get-assessment", "name": "Get Security Assessment", "description": "Get a security assessment from Microsoft Defender for Cloud", "path": "subscriptions/{subscriptionId}/providers/Microsoft.Security/assessments/{assessmentId}", "method": "GET", "parameters": {"path": {"subscriptionId": {"type": "string", "description": "Subscription ID", "required": true}, "assessmentId": {"type": "string", "description": "Assessment ID", "required": true}}, "query": {"api-version": {"type": "string", "description": "API version", "default": "2020-01-01", "required": true}}}, "response": {"successCode": 200, "schema": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "properties": {"type": "object", "properties": {"displayName": {"type": "string"}, "status": {"type": "object", "properties": {"code": {"type": "string"}, "cause": {"type": "string"}, "description": {"type": "string"}}}, "resourceDetails": {"type": "object"}, "additionalData": {"type": "object"}}}}}}}, {"id": "list-alerts", "name": "List Security Alerts", "description": "List security alerts from Microsoft Defender for Cloud", "path": "subscriptions/{subscriptionId}/providers/Microsoft.Security/alerts", "method": "GET", "parameters": {"path": {"subscriptionId": {"type": "string", "description": "Subscription ID", "required": true}}, "query": {"$filter": {"type": "string", "description": "OData filter expression"}, "$top": {"type": "integer", "description": "Number of records to return", "default": 100}, "$skipToken": {"type": "string", "description": "Skip token for pagination"}, "api-version": {"type": "string", "description": "API version", "default": "2020-01-01", "required": true}}}, "pagination": {"type": "token", "parameters": {"nextPageToken": "nextLink", "pageToken": "$skipToken"}}, "response": {"successCode": 200, "dataPath": "value", "schema": {"type": "object", "properties": {"value": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "properties": {"type": "object", "properties": {"alertDisplayName": {"type": "string"}, "alertType": {"type": "string"}, "severity": {"type": "string"}, "status": {"type": "string"}, "timeGenerated": {"type": "string"}, "resourceIdentifiers": {"type": "array"}, "description": {"type": "string"}}}}}}, "nextLink": {"type": "string"}}}}}], "mappings": [{"sourceEndpoint": "list-assessments", "targetSystem": "NovaGRC", "targetEntity": "SecurityAssessments", "transformations": [{"source": "$.id", "target": "id", "transform": "identity"}, {"source": "$.properties.displayName", "target": "name", "transform": "identity"}, {"source": "$.properties.status.code", "target": "status", "transform": "mapStatus", "parameters": {"mapping": {"Healthy": "compliant", "Unhealthy": "non-compliant", "NotApplicable": "not-applicable"}}}]}, {"sourceEndpoint": "list-alerts", "targetSystem": "NovaGRC", "targetEntity": "SecurityAlerts", "transformations": [{"source": "$.id", "target": "id", "transform": "identity"}, {"source": "$.properties.alertDisplayName", "target": "name", "transform": "identity"}, {"source": "$.properties.severity", "target": "severity", "transform": "mapSeverity", "parameters": {"mapping": {"High": "high", "Medium": "medium", "Low": "low", "Informational": "info"}}}]}], "events": {"polling": [{"endpoint": "list-alerts", "interval": "15m", "condition": "has<PERSON>ew<PERSON><PERSON><PERSON>"}]}}