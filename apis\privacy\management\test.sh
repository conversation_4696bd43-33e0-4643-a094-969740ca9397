#!/bin/bash

# Set environment variables for testing
export NODE_ENV=test

# Create directory for test reports
mkdir -p test-reports

# Run tests with coverage
echo "Running tests with coverage..."
npx jest tests/privacy/management --coverage --coverageDirectory=./test-reports/coverage

# Generate test report
echo "Generating test report..."
npx jest tests/privacy/management --json --outputFile=./test-reports/test-results.json

# Print test summary
echo "Test Summary:"
echo "============="
npx jest tests/privacy/management --coverage --coverageReporters="text-summary"

# Check if all tests passed
if [ $? -eq 0 ]; then
  echo "All tests passed!"
  exit 0
else
  echo "Some tests failed!"
  exit 1
fi
