/**
 * Circular Trust Topology
 *
 * This module implements the circular trust topology derived from the Wilson loop circumference.
 * It provides functionality for calculating trust scores and applying the π10³ factor.
 */

/**
 * CircularTrustTopology class
 */
class CircularTrustTopology {
  /**
   * Create a new CircularTrustTopology
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    this.options = {
      factor: Math.PI * 1000, // π10³
      precision: 6, // Decimal precision
      trustThreshold: 0.82, // Trust threshold based on 18/82 principle
      ...options
    };

    console.log(`CircularTrustTopology initialized with factor: ${this.options.factor}`);
  }

  /**
   * Calculate circular trust score
   * @param {Array} vector - Input vector
   * @returns {number} - Trust score
   */
  calculate(vector) {
    if (!Array.isArray(vector)) {
      throw new Error('Input must be an array');
    }

    if (vector.length === 0) {
      return 0;
    }

    // Calculate average value
    const avg = vector.reduce((sum, val) => sum + val, 0) / vector.length;

    // Apply circular trust factor
    return this._round(avg * this.options.factor);
  }

  /**
   * Calculate circular trust matrix
   * @param {Array} vectors - Array of vectors
   * @returns {Array} - Trust matrix
   */
  calculateMatrix(vectors) {
    if (!Array.isArray(vectors)) {
      throw new Error('Input must be an array of vectors');
    }

    if (vectors.length === 0) {
      return [];
    }

    const matrix = [];

    for (let i = 0; i < vectors.length; i++) {
      const row = [];

      for (let j = 0; j < vectors.length; j++) {
        if (i === j) {
          // Self-trust is always 1
          row.push(1);
        } else {
          // Calculate trust between vectors
          const trust = this._calculateTrustBetweenVectors(vectors[i], vectors[j]);
          row.push(trust);
        }
      }

      matrix.push(row);
    }

    return matrix;
  }

  /**
   * Apply circular trust factor to a value
   * @param {number|Array} value - Input value or array
   * @returns {number|Array} - Value with circular trust factor applied
   */
  applyFactor(value) {
    if (Array.isArray(value)) {
      return value.map(v => this._round(v * this.options.factor));
    }
    return this._round(value * this.options.factor);
  }

  /**
   * Calculate Wilson loop
   * @param {Array} path - Path points
   * @returns {number} - Wilson loop value
   */
  wilsonLoop(path) {
    if (!Array.isArray(path)) {
      throw new Error('Path must be an array');
    }

    if (path.length < 2) {
      return 0;
    }

    let loop = 0;

    for (let i = 0; i < path.length; i++) {
      const next = (i + 1) % path.length;
      loop += path[i] * path[next];
    }

    return this._round(loop * this.options.factor);
  }

  /**
   * Calculate trust network resilience
   * @param {Array} trustMatrix - Trust matrix
   * @returns {number} - Resilience score
   */
  calculateResilience(trustMatrix) {
    if (!Array.isArray(trustMatrix) || !Array.isArray(trustMatrix[0])) {
      throw new Error('Trust matrix must be a 2D array');
    }

    // Check if matrix is square
    const n = trustMatrix.length;
    for (let i = 0; i < n; i++) {
      if (trustMatrix[i].length !== n) {
        throw new Error('Trust matrix must be square');
      }
    }

    // Calculate eigenvalue approximation (using power iteration)
    const eigenvalue = this._approximateMaxEigenvalue(trustMatrix);

    // Resilience is related to the maximum eigenvalue
    return this._round(eigenvalue / n);
  }

  /**
   * Apply 18/82 principle to trust network
   * @param {Array} trustMatrix - Trust matrix
   * @returns {Array} - Filtered trust matrix
   */
  apply1882Principle(trustMatrix) {
    if (!Array.isArray(trustMatrix) || !Array.isArray(trustMatrix[0])) {
      throw new Error('Trust matrix must be a 2D array');
    }

    const n = trustMatrix.length;
    const result = [];

    for (let i = 0; i < n; i++) {
      const row = [];
      for (let j = 0; j < n; j++) {
        // Apply threshold based on 18/82 principle
        row.push(trustMatrix[i][j] >= this.options.trustThreshold ? 1 : 0);
      }
      result.push(row);
    }

    return result;
  }

  /**
   * Calculate trust propagation
   * @param {Array} trustMatrix - Trust matrix
   * @param {Array} initialTrust - Initial trust vector
   * @param {number} steps - Number of propagation steps
   * @returns {Array} - Propagated trust vector
   */
  propagateTrust(trustMatrix, initialTrust, steps = 3) {
    if (!Array.isArray(trustMatrix) || !Array.isArray(initialTrust)) {
      throw new Error('Inputs must be arrays');
    }

    if (trustMatrix.length !== initialTrust.length) {
      throw new Error('Trust matrix dimensions must match initial trust vector length');
    }

    let currentTrust = [...initialTrust];

    // Propagate trust for specified number of steps
    for (let step = 0; step < steps; step++) {
      const newTrust = [];

      for (let i = 0; i < trustMatrix.length; i++) {
        let sum = 0;
        for (let j = 0; j < trustMatrix.length; j++) {
          sum += trustMatrix[i][j] * currentTrust[j];
        }
        newTrust.push(this._round(sum));
      }

      currentTrust = newTrust;
    }

    return currentTrust;
  }

  /**
   * Calculate trust between two vectors
   * @param {Array} a - First vector
   * @param {Array} b - Second vector
   * @returns {number} - Trust value (0-1)
   * @private
   */
  _calculateTrustBetweenVectors(a, b) {
    if (!Array.isArray(a) || !Array.isArray(b)) {
      throw new Error('Inputs must be arrays');
    }

    // Calculate cosine similarity
    let dotProduct = 0;
    let normA = 0;
    let normB = 0;

    // Use the minimum length
    const minLength = Math.min(a.length, b.length);

    for (let i = 0; i < minLength; i++) {
      dotProduct += a[i] * b[i];
      normA += a[i] * a[i];
      normB += b[i] * b[i];
    }

    if (normA === 0 || normB === 0) {
      return 0;
    }

    const similarity = dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));

    // Normalize to 0-1 range
    return this._round(Math.max(0, Math.min(1, (similarity + 1) / 2)));
  }

  /**
   * Approximate maximum eigenvalue of a matrix
   * @param {Array} matrix - Input matrix
   * @param {number} iterations - Number of power iterations
   * @returns {number} - Approximate maximum eigenvalue
   * @private
   */
  _approximateMaxEigenvalue(matrix, iterations = 10) {
    const n = matrix.length;

    // Start with a random vector
    let vector = Array(n).fill(0).map(() => Math.random());

    // Normalize
    const initialNorm = Math.sqrt(vector.reduce((sum, val) => sum + val * val, 0));
    vector = vector.map(val => val / initialNorm);

    // Power iteration
    for (let iter = 0; iter < iterations; iter++) {
      // Multiply matrix by vector
      const newVector = Array(n).fill(0);

      for (let i = 0; i < n; i++) {
        for (let j = 0; j < n; j++) {
          newVector[i] += matrix[i][j] * vector[j];
        }
      }

      // Calculate norm
      const norm = Math.sqrt(newVector.reduce((sum, val) => sum + val * val, 0));

      // Normalize
      vector = newVector.map(val => val / norm);
    }

    // Calculate Rayleigh quotient
    let numerator = 0;
    for (let i = 0; i < n; i++) {
      let sum = 0;
      for (let j = 0; j < n; j++) {
        sum += matrix[i][j] * vector[j];
      }
      numerator += vector[i] * sum;
    }

    const denominator = vector.reduce((sum, val) => sum + val * val, 0);

    return this._round(numerator / denominator);
  }

  /**
   * Round number to specified precision
   * @param {number} value - Value to round
   * @returns {number} - Rounded value
   * @private
   */
  _round(value) {
    const factor = Math.pow(10, this.options.precision);
    return Math.round(value * factor) / factor;
  }
}

module.exports = CircularTrustTopology;
