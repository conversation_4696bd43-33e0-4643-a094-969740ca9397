# Finite Universe Principle API Documentation

## Overview

The Finite Universe Principle API provides a RESTful interface for interacting with the Finite Universe Principle defense system. It allows third-party applications to process data through the defense system, retrieve metrics, and run tests.

## Authentication

The API uses JWT (JSON Web Token) authentication. To access protected endpoints, you need to obtain a JWT token by authenticating with the `/api/v1/login` endpoint.

### Login

```
POST /api/v1/login
```

Request body:

```json
{
  "username": "admin",
  "password": "password"
}
```

Response:

```json
{
  "success": true,
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expiresIn": "1h",
  "user": {
    "username": "admin",
    "role": "admin"
  }
}
```

### Using the Token

Include the token in the `Authorization` header of your requests:

```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## Rate Limiting

The API implements rate limiting to prevent abuse. By default, each IP address is limited to 100 requests per 15-minute window. When the rate limit is exceeded, the API will respond with a 429 Too Many Requests status code.

## Endpoints

### Health Check

Check the health of the API server.

```
GET /api/v1/health
```

Response:

```json
{
  "status": "ok",
  "timestamp": "2023-06-01T12:00:00.000Z"
}
```

Note: This endpoint does not require authentication.

### Process Data

Process data through the defense system.

```
POST /api/v1/process
```

Request body:

```json
{
  "data": {
    "securityScore": 8,
    "threatLevel": 3,
    "encryptionStrength": 256
  },
  "domain": "cyber"
}
```

Response:

```json
{
  "success": true,
  "result": {
    "securityScore": 8,
    "threatLevel": 3,
    "encryptionStrength": 256,
    "boundaryEnforced": true,
    "violations": 0
  },
  "timestamp": "2023-06-01T12:00:00.000Z"
}
```

### Get Metrics

Get current metrics from the defense system.

```
GET /api/v1/metrics
```

Response:

```json
{
  "success": true,
  "metrics": {
    "boundaryViolations": 0,
    "boundaryCorrections": 5,
    "validationFailures": 0,
    "domainMetrics": {
      "cyber": {
        "boundaryViolations": 0,
        "averageSecurityScore": 7.5,
        "averageThreatLevel": 2.3
      },
      "financial": {
        "boundaryViolations": 0,
        "averageInterestRate": 0.05,
        "totalTransactionVolume": 10000
      },
      "medical": {
        "boundaryViolations": 0,
        "averageHeartRate": 75,
        "criticalAlerts": 0
      }
    }
  },
  "timestamp": "2023-06-01T12:00:00.000Z"
}
```

### Get Metrics History

Get historical metrics from the defense system.

```
GET /api/v1/metrics/history
```

Response:

```json
{
  "success": true,
  "history": [
    {
      "timestamp": "2023-06-01T11:59:00.000Z",
      "metrics": {
        "boundaryViolations": 0,
        "boundaryCorrections": 3,
        "validationFailures": 0,
        "domainMetrics": {
          "cyber": {
            "boundaryViolations": 0,
            "averageSecurityScore": 7.2,
            "averageThreatLevel": 2.1
          },
          "financial": {
            "boundaryViolations": 0,
            "averageInterestRate": 0.05,
            "totalTransactionVolume": 9000
          },
          "medical": {
            "boundaryViolations": 0,
            "averageHeartRate": 74,
            "criticalAlerts": 0
          }
        }
      }
    },
    {
      "timestamp": "2023-06-01T12:00:00.000Z",
      "metrics": {
        "boundaryViolations": 0,
        "boundaryCorrections": 5,
        "validationFailures": 0,
        "domainMetrics": {
          "cyber": {
            "boundaryViolations": 0,
            "averageSecurityScore": 7.5,
            "averageThreatLevel": 2.3
          },
          "financial": {
            "boundaryViolations": 0,
            "averageInterestRate": 0.05,
            "totalTransactionVolume": 10000
          },
          "medical": {
            "boundaryViolations": 0,
            "averageHeartRate": 75,
            "criticalAlerts": 0
          }
        }
      }
    }
  ],
  "timestamp": "2023-06-01T12:00:00.000Z"
}
```

### Get Alerts

Get alerts from the defense system.

```
GET /api/v1/alerts
```

Response:

```json
{
  "success": true,
  "alerts": [
    {
      "timestamp": "2023-06-01T11:55:00.000Z",
      "type": "boundary-violation",
      "level": "warning",
      "message": "Attempted to set encryptionStrength to Infinity, bounded to 1000000"
    },
    {
      "timestamp": "2023-06-01T11:58:00.000Z",
      "type": "validation-failure",
      "level": "critical",
      "message": "Cross-domain validation failed for financial data"
    }
  ],
  "timestamp": "2023-06-01T12:00:00.000Z"
}
```

### Run Tests

Run tests on the defense system.

```
POST /api/v1/tests/run
```

Response:

```json
{
  "success": true,
  "results": {
    "totalTests": 10,
    "passedTests": 10,
    "failedTests": 0,
    "testResults": [
      {
        "name": "Boundary Enforcement Test",
        "passed": true,
        "message": "All boundaries enforced correctly"
      },
      {
        "name": "Cross-Domain Validation Test",
        "passed": true,
        "message": "All cross-domain validations passed"
      }
    ]
  },
  "timestamp": "2023-06-01T12:00:00.000Z"
}
```

### Reset Metrics

Reset metrics in the defense system.

```
POST /api/v1/metrics/reset
```

Response:

```json
{
  "success": true,
  "message": "Metrics reset successfully",
  "timestamp": "2023-06-01T12:00:00.000Z"
}
```

## Error Handling

The API uses standard HTTP status codes to indicate the success or failure of a request:

- 200 OK: The request was successful
- 400 Bad Request: The request was invalid
- 401 Unauthorized: Authentication failed
- 403 Forbidden: The authenticated user does not have permission to access the requested resource
- 404 Not Found: The requested resource was not found
- 429 Too Many Requests: Rate limit exceeded
- 500 Internal Server Error: An error occurred on the server

Error responses have the following format:

```json
{
  "error": "Error Type",
  "message": "Detailed error message"
}
```

## Examples

### JavaScript Example

```javascript
// Login to get token
const loginResponse = await fetch('http://localhost:3001/api/v1/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    username: 'admin',
    password: 'password'
  })
});

const loginResult = await loginResponse.json();
const token = loginResult.token;

// Process data with token
const response = await fetch('http://localhost:3001/api/v1/process', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({
    data: {
      securityScore: 8,
      threatLevel: 3,
      encryptionStrength: 256
    },
    domain: 'cyber'
  })
});

const result = await response.json();
console.log('Processed data:', result);
```

### cURL Example

```bash
# Login to get token
curl -X POST http://localhost:3001/api/v1/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"password"}'

# Process data with token
curl -X POST http://localhost:3001/api/v1/process \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"data":{"securityScore":8,"threatLevel":3},"domain":"cyber"}'

# Get metrics with token
curl -X GET http://localhost:3001/api/v1/metrics \
  -H "Authorization: Bearer YOUR_TOKEN"
```
