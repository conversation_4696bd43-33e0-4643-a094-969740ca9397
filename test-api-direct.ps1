# Direct test script for NovaFuse API Superstore

Write-Host "Testing NovaFuse API Superstore API directly..."
Write-Host ""

# Test Marketplace UI
Write-Host "Testing Marketplace UI..."
try {
    $response = Invoke-WebRequest -Uri "http://localhost:3000" -ErrorAction Stop
    Write-Host "? Marketplace UI is working!" -ForegroundColor Green
} catch {
    Write-Host "? Marketplace UI test failed: $_" -ForegroundColor Red
}
Write-Host ""

# Test API Documentation
Write-Host "Testing API Documentation..."
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8080" -ErrorAction Stop
    Write-Host "? API Documentation is working!" -ForegroundColor Green
} catch {
    Write-Host "? API Documentation test failed: $_" -ForegroundColor Red
}
Write-Host ""

# Test Kong Admin API
Write-Host "Testing Kong Admin API..."
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8001" -ErrorAction Stop
    Write-Host "? Kong Admin API is working!" -ForegroundColor Green
} catch {
    Write-Host "? Kong Admin API test failed: $_" -ForegroundColor Red
}
Write-Host ""

Write-Host "Tests completed!"
