const fs = require('fs');
const path = require('path');
const puppeteer = require('puppeteer');

async function generateDiagrams() {
  console.log('Starting diagram generation...');
  
  // Create diagrams directory if it doesn't exist
  const diagramsDir = path.join(__dirname, 'diagrams');
  if (!fs.existsSync(diagramsDir)) {
    fs.mkdirSync(diagramsDir);
  }
  
  // Launch browser
  const browser = await puppeteer.launch({
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    const page = await browser.newPage();
    
    // Set viewport size
    await page.setViewport({
      width: 1200,
      height: 800,
      deviceScaleFactor: 2 // Higher resolution
    });
    
    // List of diagrams to generate
    const diagrams = [
      { id: 'high-level-architecture', name: '1. High-Level System Architecture' },
      { id: 'finite-universe', name: '2. Finite Universe Paradigm' },
      { id: 'three-body-problem', name: '3. Three-Body Problem Reframing' },
      { id: 'uuft-equation-flow', name: '4. UUFT Equation Flow' },
      { id: 'trinity-equation', name: '5. Trinity Equation Visualization' },
      { id: 'meta-field-schema', name: '6. Meta-Field Schema' },
      { id: 'pattern-translation', name: '8. Pattern Translation Process' },
      { id: 'novafuse-components', name: '9. 13 NovaFuse Components' },
      { id: 'alignment-architecture', name: '10. 3-6-9-12-13 Alignment Architecture' },
      { id: 'data-processing-pipeline', name: '11. Cross-Module Data Processing Pipeline' },
      { id: 'incident-response', name: '12. Cyber-Safety Incident Response Workflow' },
      { id: 'healthcare-implementation', name: '13. Healthcare Implementation' },
      { id: 'dashboard-visualization', name: '14. Dashboard and Visualization Examples' },
      { id: 'hardware-architecture', name: '15. Hardware Architecture' }
    ];
    
    // Generate HTML for rendering diagrams
    const generateHtml = (diagramComponent) => {
      return `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8">
          <style>
            body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
            .diagram-container { 
              border: 1px solid #eee; 
              padding: 20px; 
              border-radius: 8px;
              background-color: white;
            }
            .element {
              position: absolute;
              border-radius: 8px;
              padding: 15px;
              box-shadow: 0 2px 5px rgba(0,0,0,0.1);
              z-index: 2;
            }
            .element-number {
              position: absolute;
              top: 5px;
              left: 5px;
              background-color: black;
              color: white;
              border-radius: 50%;
              width: 20px;
              height: 20px;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 12px;
              z-index: 3;
            }
            .connection {
              position: absolute;
              z-index: 1;
            }
            .bold-text {
              font-weight: bold;
            }
          </style>
        </head>
        <body>
          <div id="diagram-container" class="diagram-container">
            <!-- Diagram will be rendered here -->
          </div>
          <script>
            // This would normally be React components, but we'll simulate them with DOM manipulation
            const renderDiagram = () => {
              const container = document.getElementById('diagram-container');
              
              // Set container dimensions
              container.style.width = '800px';
              container.style.height = '600px';
              container.style.position = 'relative';
              
              // Add a title element
              const title = document.createElement('div');
              title.className = 'element';
              title.style.top = '50px';
              title.style.left = '300px';
              title.style.width = '200px';
              title.style.backgroundColor = '#e6f7ff';
              title.style.fontWeight = 'bold';
              title.style.fontSize = '18px';
              title.textContent = '${diagramComponent.name}';
              
              // Add number to title
              const titleNumber = document.createElement('div');
              titleNumber.className = 'element-number';
              titleNumber.textContent = '1';
              title.appendChild(titleNumber);
              
              container.appendChild(title);
              
              // Add a subtitle
              const subtitle = document.createElement('div');
              subtitle.className = 'element';
              subtitle.style.top = '150px';
              subtitle.style.left = '250px';
              subtitle.style.width = '300px';
              subtitle.style.backgroundColor = '#fff0f6';
              subtitle.style.fontSize = '16px';
              subtitle.textContent = 'Comphyology Patent Diagram';
              
              // Add number to subtitle
              const subtitleNumber = document.createElement('div');
              subtitleNumber.className = 'element-number';
              subtitleNumber.textContent = '2';
              subtitle.appendChild(subtitleNumber);
              
              container.appendChild(subtitle);
              
              // Add a connection
              const connection = document.createElement('div');
              connection.className = 'connection';
              connection.style.top = '100px';
              connection.style.left = '400px';
              connection.style.width = '2px';
              connection.style.height = '50px';
              connection.style.backgroundColor = 'black';
              
              container.appendChild(connection);
            };
            
            // Render the diagram
            renderDiagram();
          </script>
        </body>
        </html>
      `;
    };
    
    // Generate each diagram
    for (const diagram of diagrams) {
      console.log(`Generating diagram: ${diagram.name}`);
      
      // Load HTML content
      await page.setContent(generateHtml(diagram));
      
      // Wait for rendering
      await page.waitForSelector('#diagram-container');
      
      // Take screenshot
      const element = await page.$('#diagram-container');
      const outputPath = path.join(diagramsDir, `${diagram.id}.png`);
      await element.screenshot({ path: outputPath });
      
      console.log(`Saved diagram to: ${outputPath}`);
    }
    
    console.log('All diagrams generated successfully!');
  } catch (error) {
    console.error('Error generating diagrams:', error);
  } finally {
    await browser.close();
  }
}

// Run the function
generateDiagrams().catch(console.error);
