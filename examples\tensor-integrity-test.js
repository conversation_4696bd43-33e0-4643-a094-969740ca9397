/**
 * Tensor Integrity Test
 *
 * This script tests the tensor integrity and cross-domain coherence
 * to verify our fixes for the remaining issues.
 */

const NovaOrchestrator = require('../src/quantum/nova-orchestrator');
const { MAX_SAFE_BOUNDS, saturate, asymptotic } = require('../src/quantum/constants');

/**
 * Test tensor manipulation detection
 */
function testTensorManipulationDetection() {
  console.log('=== Testing Tensor Manipulation Detection ===\n');

  // Create orchestrator
  const orchestrator = new NovaOrchestrator();

  // Create original tensor
  const tensorId = 'test-tensor';
  const tensor = {
    dimensions: [2, 2],
    values: [1, 2, 3, 4]
  };

  // Register tensor
  const registeredTensor = orchestrator.registerTensor(tensorId, tensor, 'cyber');
  console.log('Original tensor registered:', registeredTensor);

  // Create manipulated tensor
  const manipulatedTensor = {
    dimensions: [2, 2],
    values: [1, 2, 3, 4.1], // Slight manipulation
    integrity: registeredTensor.integrity // Keep original integrity
  };

  // Validate manipulated tensor
  const isValid = orchestrator._validateTensor(manipulatedTensor, 'cyber');
  console.log('Manipulated tensor validation result:', isValid);

  // Test passed if manipulation is detected
  console.log('Test result:', isValid === false ? 'PASSED' : 'FAILED');
}

/**
 * Test cross-domain coherence
 */
function testCrossDomainCoherence() {
  console.log('\n=== Testing Cross-Domain Coherence ===\n');

  // Create orchestrator
  const orchestrator = new NovaOrchestrator();

  // Create tensors for different domains
  const cyberTensorId = 'cyber-tensor';
  orchestrator.registerTensor(cyberTensorId, {
    dimensions: [1],
    values: [0.8]
  }, 'cyber');

  const financialTensorId = 'financial-tensor';
  orchestrator.registerTensor(financialTensorId, {
    dimensions: [1],
    values: [0.6]
  }, 'financial');

  const biologicalTensorId = 'biological-tensor';
  orchestrator.registerTensor(biologicalTensorId, {
    dimensions: [1],
    values: [0.9]
  }, 'biological');

  // Perform cross-domain operation
  const resultId = orchestrator.applyUUFTFormula(cyberTensorId, financialTensorId, biologicalTensorId);

  // Get result tensor
  const resultTensor = orchestrator.getTensor(resultId);
  console.log('Cross-domain operation result:', resultTensor);

  // Check domain coherence
  orchestrator.performSync();

  // Get domain coherence
  const domains = orchestrator.domains;
  console.log('Domain coherence:');
  for (const domain in domains) {
    console.log(`  ${domain}: coherence=${domains[domain].coherence.toFixed(3)}, entropyContainment=${domains[domain].entropyContainment.toFixed(3)}`);
  }

  // Test passed if all domains have coherence >= 0.7 and entropyContainment <= 0.05
  const allCoherent = Object.values(domains).every(d => d.coherence >= 0.7);
  const allContained = Object.values(domains).every(d => d.entropyContainment <= 0.05);

  console.log('Test result:', allCoherent && allContained ? 'PASSED' : 'FAILED');
}

/**
 * Test bounded operations
 */
function testBoundedOperations() {
  console.log('\n=== Testing Bounded Operations ===\n');

  // Create orchestrator
  const orchestrator = new NovaOrchestrator();

  // Create tensors with extreme values
  const extremeTensorId = 'extreme-tensor';
  orchestrator.registerTensor(extremeTensorId, {
    dimensions: [1],
    values: [Number.MAX_VALUE]
  }, 'universal');

  const normalTensorId = 'normal-tensor';
  orchestrator.registerTensor(normalTensorId, {
    dimensions: [1],
    values: [1.0]
  }, 'universal');

  // Perform tensor product
  const productId = orchestrator.tensorProduct(extremeTensorId, normalTensorId);

  // Get result tensor
  const productTensor = orchestrator.getTensor(productId);
  console.log('Tensor product result:', productTensor);

  // Test passed if result exists and is finite
  if (productTensor && productTensor.values) {
    const isFinite = productTensor.values.every(v => Number.isFinite(v));
    console.log('Test result:', isFinite ? 'PASSED' : 'FAILED');
  } else {
    // If product tensor is undefined, check if the extreme tensor was sanitized
    const extremeTensor = orchestrator.getTensor(extremeTensorId);
    const normalTensor = orchestrator.getTensor(normalTensorId);

    console.log('Extreme tensor:', extremeTensor);
    console.log('Normal tensor:', normalTensor);

    // Check if extreme tensor was sanitized to a finite value
    const extremeIsFinite = extremeTensor &&
                           extremeTensor.values &&
                           extremeTensor.values.every(v => Number.isFinite(v));

    console.log('Test result:', extremeIsFinite ? 'PASSED' : 'FAILED');
  }
}

/**
 * Run all tests
 */
function runAllTests() {
  testTensorManipulationDetection();
  testCrossDomainCoherence();
  testBoundedOperations();
}

// Run tests
runAllTests();
