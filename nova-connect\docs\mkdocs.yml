site_name: NovaConnect UAC Documentation
site_description: Documentation for NovaFuse Universal API Connector
site_author: NovaFuse
copyright: Copyright &copy; 2023 NovaFuse

theme:
  name: material
  palette:
    primary: blue
    accent: blue
  logo: assets/images/logo.png
  favicon: assets/images/favicon.ico
  features:
    - navigation.tabs
    - navigation.sections
    - navigation.expand
    - navigation.top
    - search.highlight
    - search.share
    - content.code.copy

markdown_extensions:
  - admonition
  - codehilite
  - footnotes
  - meta
  - pymdownx.highlight
  - pymdownx.superfences
  - pymdownx.tabbed
  - pymdownx.tasklist:
      custom_checkbox: true
  - toc:
      permalink: true

plugins:
  - search
  - minify:
      minify_html: true
      minify_js: true
      minify_css: true
      htmlmin_opts:
        remove_comments: true
  - git-revision-date-localized:
      type: date

extra:
  social:
    - icon: fontawesome/brands/github
      link: https://github.com/novafuse
    - icon: fontawesome/brands/twitter
      link: https://twitter.com/novafuse
    - icon: fontawesome/brands/linkedin
      link: https://linkedin.com/company/novafuse

nav:
  - Home: index.md
  - Getting Started:
    - Overview: getting-started/overview.md
    - Installation: getting-started/installation.md
    - Quick Start: getting-started/quick-start.md
    - Configuration: getting-started/configuration.md
  - Core Concepts:
    - Architecture: core-concepts/architecture.md
    - Connectors: core-concepts/connectors.md
    - Authentication: core-concepts/authentication.md
    - Data Mapping: core-concepts/data-mapping.md
    - Error Handling: core-concepts/error-handling.md
  - Connectors:
    - Overview: connectors/overview.md
    - Governance & Board Compliance: connectors/governance-board-compliance.md
    - Legal & Regulatory Intelligence: connectors/legal-regulatory-intelligence.md
    - Risk & Audit: connectors/risk-audit.md
    - Cybersecurity/InfoSec/Privacy: connectors/cybersecurity-infosec-privacy.md
    - Contracts & Policy Lifecycle: connectors/contracts-policy-lifecycle.md
    - APIs, iPaaS & Developer Tools: connectors/apis-ipaas-developer-tools.md
    - Business Intelligence & Workflow: connectors/business-intelligence-workflow.md
    - Certifications & Accreditation: connectors/certifications-accreditation.md
  - API Reference:
    - Overview: api-reference/overview.md
    - Authentication: api-reference/authentication.md
    - Connector API: api-reference/connector-api.md
    - Registry API: api-reference/registry-api.md
    - Executor API: api-reference/executor-api.md
    - Error Codes: api-reference/error-codes.md
  - Deployment:
    - Overview: deployment/overview.md
    - Docker: deployment/docker.md
    - Kubernetes: deployment/kubernetes.md
    - GCP: deployment/gcp.md
    - AWS: deployment/aws.md
    - Azure: deployment/azure.md
  - Dashboards:
    - Overview: dashboards/overview.md
    - Performance Dashboard: dashboards/performance-dashboard.md
    - CSDE Dashboard: dashboards/csde-dashboard.md
  - Security:
    - Overview: security/overview.md
    - Authentication: security/authentication.md
    - Authorization: security/authorization.md
    - Data Protection: security/data-protection.md
    - Compliance: security/compliance.md
  - Troubleshooting:
    - Common Issues: troubleshooting/common-issues.md
    - Logging: troubleshooting/logging.md
    - Debugging: troubleshooting/debugging.md
  - Contributing:
    - Guidelines: contributing/guidelines.md
    - Development Setup: contributing/development-setup.md
    - Code Style: contributing/code-style.md
    - Testing: contributing/testing.md
  - FAQ: faq.md
  - Changelog: changelog.md
