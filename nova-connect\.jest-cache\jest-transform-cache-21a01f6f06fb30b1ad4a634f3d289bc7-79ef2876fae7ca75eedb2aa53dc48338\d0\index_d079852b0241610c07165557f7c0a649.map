{"version": 3, "names": ["UAConnectorError", "require", "AuthenticationErrors", "ConnectionErrors", "ValidationErrors", "ApiErrors", "TransformationErrors", "ConnectorErrors", "module", "exports"], "sources": ["index.js"], "sourcesContent": ["/**\n * NovaFuse Universal API Connector - Errors\n * \n * This module exports all error types for the UAC.\n */\n\nconst UAConnectorError = require('./base-error');\nconst AuthenticationErrors = require('./authentication-error');\nconst ConnectionErrors = require('./connection-error');\nconst ValidationErrors = require('./validation-error');\nconst ApiErrors = require('./api-error');\nconst TransformationErrors = require('./transformation-error');\nconst ConnectorErrors = require('./connector-error');\n\nmodule.exports = {\n  // Base error\n  UAConnectorError,\n  \n  // Authentication errors\n  ...AuthenticationErrors,\n  \n  // Connection errors\n  ...ConnectionErrors,\n  \n  // Validation errors\n  ...ValidationErrors,\n  \n  // API errors\n  ...ApiErrors,\n  \n  // Transformation errors\n  ...TransformationErrors,\n  \n  // Connector errors\n  ...ConnectorErrors\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,MAAMA,gBAAgB,GAAGC,OAAO,CAAC,cAAc,CAAC;AAChD,MAAMC,oBAAoB,GAAGD,OAAO,CAAC,wBAAwB,CAAC;AAC9D,MAAME,gBAAgB,GAAGF,OAAO,CAAC,oBAAoB,CAAC;AACtD,MAAMG,gBAAgB,GAAGH,OAAO,CAAC,oBAAoB,CAAC;AACtD,MAAMI,SAAS,GAAGJ,OAAO,CAAC,aAAa,CAAC;AACxC,MAAMK,oBAAoB,GAAGL,OAAO,CAAC,wBAAwB,CAAC;AAC9D,MAAMM,eAAe,GAAGN,OAAO,CAAC,mBAAmB,CAAC;AAEpDO,MAAM,CAACC,OAAO,GAAG;EACf;EACAT,gBAAgB;EAEhB;EACA,GAAGE,oBAAoB;EAEvB;EACA,GAAGC,gBAAgB;EAEnB;EACA,GAAGC,gBAAgB;EAEnB;EACA,GAAGC,SAAS;EAEZ;EACA,GAAGC,oBAAoB;EAEvB;EACA,GAAGC;AACL,CAAC", "ignoreList": []}