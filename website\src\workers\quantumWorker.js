// Quantum simulation worker for offloading heavy calculations

// Quantum state representation
class QuantumState {
  constructor(entropy = 0, coherence = 1) {
    this.entropy = entropy;
    this.coherence = coherence;
    this.amplitude = Math.sqrt(1 - entropy);
    this.phase = 0;
  }

  // Update state based on quantum operations
  update(deltaTime = 0.01) {
    // Simple harmonic oscillator model for quantum state evolution
    this.phase += deltaTime * (1 + this.coherence * 2);
    this.amplitude = Math.sqrt(1 - this.entropy) * (0.9 + 0.1 * Math.sin(this.phase));
    
    // Return current state
    return {
      entropy: this.entropy,
      coherence: this.coherence,
      amplitude: this.amplitude,
      phase: this.phase % (2 * Math.PI)
    };
  }
}

// Initialize quantum state
let quantumState = new QuantumState();
let isRunning = false;
let lastUpdate = performance.now();

// Handle messages from main thread
self.onmessage = function(e) {
  const { type, payload } = e.data;
  
  switch (type) {
    case 'INIT':
      quantumState = new QuantumState(payload.entropy, payload.coherence);
      startLoop();
      break;
      
    case 'UPDATE_PARAMS':
      if (payload.entropy !== undefined) quantumState.entropy = payload.entropy;
      if (payload.coherence !== undefined) quantumState.coherence = payload.coherence;
      break;
      
    case 'PAUSE':
      isRunning = false;
      break;
      
    case 'RESUME':
      if (!isRunning) startLoop();
      break;
      
    case 'RESET':
      quantumState = new QuantumState();
      break;
  }
};

// Main animation loop
function update() {
  if (!isRunning) return;
  
  const now = performance.now();
  const deltaTime = (now - lastUpdate) / 1000; // Convert to seconds
  lastUpdate = now;
  
  // Update quantum state
  const state = quantumState.update(deltaTime);
  
  // Send updated state back to main thread
  self.postMessage({
    type: 'STATE_UPDATE',
    state: {
      ...state,
      timestamp: now
    }
  });
  
  // Continue animation loop
  if (isRunning) {
    self.requestAnimationFrame(update);
  }
}

// Start the update loop
function startLoop() {
  if (isRunning) return;
  isRunning = true;
  lastUpdate = performance.now();
  self.requestAnimationFrame(update);
}

// Initialize with default state
self.postMessage({ type: 'WORKER_READY' });
