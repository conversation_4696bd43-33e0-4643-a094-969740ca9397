/**
 * Simple Adaptive Trinity CSDE Demo
 *
 * This script demonstrates the Adaptive Trinity CSDE with self-optimizing ratios.
 */

const { AdaptiveTrinityCSDE } = require('../src/csde');

// Create a logger
const logger = {
  info: (message) => console.log(`[INFO] ${message}`),
  error: (message) => console.log(`[ERROR] ${message}`),
  warn: (message) => console.log(`[WARN] ${message}`),
  debug: (message) => console.log(`[DEBUG] ${message}`)
};

// Sample data - Governance focused scenario
// In this scenario, governance (policy design) is more important than compliance enforcement
const governanceData = {
  complianceScore: 0.65,  // Lower compliance score
  auditFrequency: 2,      // Lower audit frequency
  timestamp: new Date().toISOString(),
  source: 'official',
  confidence: 0.9,
  policies: [
    { id: 'POL-001', name: 'Access Control Policy', effectiveness: 0.95 },
    { id: 'POL-002', name: 'Data Protection Policy', effectiveness: 0.92 },
    { id: 'POL-003', name: 'Security Policy', effectiveness: 0.90 }
  ]  // More effective policies
};

const detectionData = {
  detectionCapability: 0.75,
  threatSeverity: 0.8,
  threatConfidence: 0.7,
  baselineSignals: 0.85,  // Higher baseline signals
  timestamp: new Date().toISOString(),
  source: 'sensor',
  confidence: 0.85,
  threats: {
    malware: 0.7,         // Lower threat weights
    phishing: 0.6
  }
};

const responseData = {
  baseResponseTime: 30,   // Faster response time
  threatSurface: 175,
  systemRadius: 150,
  reactionTime: 0.9,      // Better reaction time
  mitigationSurface: 0.6, // Lower mitigation surface
  timestamp: new Date().toISOString(),
  source: 'verified',
  confidence: 0.8,
  threats: {
    malware: 0.7,
    phishing: 0.6
  }
};

/**
 * Demo Adaptive Trinity CSDE with multiple optimization cycles
 * @param {number} cycles - Number of optimization cycles
 */
function demoAdaptiveTrinityCSDE(cycles = 5) {
  logger.info(`Demo: Adaptive Trinity CSDE with ${cycles} optimization cycles`);

  try {
    // Initialize Adaptive Trinity CSDE Engine
    const csdeEngine = new AdaptiveTrinityCSDE({
      enableMetrics: true,
      enableCaching: false,  // Disable caching for optimization
      learningRate: 0.15,    // Much higher learning rate for faster convergence and visible changes
      optimizationTarget: 'balanced',
      minRatio: 0.01,        // Allow ratios to go lower
      maxRatio: 0.99         // Allow ratios to go higher
    });

    // Run initial calculation with default 18/82 ratio
    logger.info('Running initial calculation with default 18/82 ratio');
    const initialResult = csdeEngine.calculateTrinityCSDE(
      governanceData,
      detectionData,
      responseData,
      false  // don't optimize ratios yet
    );

    logger.info(`Initial Trinity CSDE Value: ${initialResult.csdeTrinity.toFixed(4)}`);
    logger.info(`Initial Ratios - Father: ${initialResult.adaptiveRatios.father.toFixed(4)}, Son: ${initialResult.adaptiveRatios.son.toFixed(4)}, Spirit: ${initialResult.adaptiveRatios.spirit.toFixed(4)}`);

    // Run optimization cycles
    for (let i = 1; i <= cycles; i++) {
      logger.info(`Running optimization cycle ${i}/${cycles}`);

      // Run optimization cycle
      const result = csdeEngine.calculateTrinityCSDE(
        governanceData,
        detectionData,
        responseData,
        true  // optimize ratios
      );

      logger.info(`Cycle ${i} - Trinity CSDE Value: ${result.csdeTrinity.toFixed(4)}`);
      logger.info(`Cycle ${i} - Ratios - Father: ${result.adaptiveRatios.father.toFixed(4)}, Son: ${result.adaptiveRatios.son.toFixed(4)}, Spirit: ${result.adaptiveRatios.spirit.toFixed(4)}`);
      logger.info(`Cycle ${i} - Performance: ${result.performanceMetrics.currentPerformance.toFixed(4)}`);
    }

    // Get best result
    const bestPerformance = csdeEngine.getPerformanceMetrics().bestPerformance;
    const bestRatios = csdeEngine.getPerformanceMetrics().bestRatios;

    logger.info('\nOptimization Results:');
    logger.info(`Best Performance: ${bestPerformance.toFixed(4)}`);
    logger.info(`Best Ratios - Father: ${bestRatios.father.alpha.toFixed(4)}, Son: ${bestRatios.son.beta.toFixed(4)}, Spirit: ${bestRatios.spirit.gamma.toFixed(4)}`);

  } catch (error) {
    logger.error(`Error in Adaptive Trinity CSDE demo: ${error.message}`);
    throw error;
  }
}

/**
 * Main function
 */
function main() {
  logger.info('Simple Adaptive Trinity CSDE Demo');

  try {
    // Demo Adaptive Trinity CSDE with 5 optimization cycles
    demoAdaptiveTrinityCSDE(5);

    logger.info('Demo completed successfully');
  } catch (error) {
    logger.error(`Error running demo: ${error.message}`);
  }
}

// Run the demo
main();
