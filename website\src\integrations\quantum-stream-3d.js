import { useEffect, useRef, useCallback } from 'react';
import { useNova3DScene } from '@novafuse/nova-vision-3d';
import { useQuantumStream } from '../hooks/useQuantumStream';
import { encodeDelta, decodeDelta } from '../utils/quantumCodec';

// Quality profiles for adaptive streaming
const QUALITY_PROFILES = {
  high: {
    updateRate: 60, // fps
    maxQubits: 1000,
    entanglementDetail: 'full',
    shadowQuality: 'high',
    textureResolution: 2048
  },
  medium: {
    updateRate: 30,
    maxQubits: 500,
    entanglementDetail: 'simplified',
    shadowQuality: 'medium',
    textureResolution: 1024
  },
  low: {
    updateRate: 15,
    maxQubits: 100,
    entanglementDetail: 'none',
    shadowQuality: 'low',
    textureResolution: 512
  }
};

/**
 * Hook for integrating quantum stream with NovaVision 3D
 * @param {Object} config - Configuration object
 * @param {string} config.streamId - ID of the quantum stream to connect to
 * @param {string} [config.initialQuality='high'] - Initial quality setting
 * @param {boolean} [config.autoAdjustQuality=true] - Whether to adjust quality based on performance
 * @returns {Object} - Streaming state and controls
 */
export function useQuantumVisualization({
  streamId,
  initialQuality = 'high',
  autoAdjustQuality = true
} = {}) {
  const sceneRef = useNova3DScene();
  const qualityRef = useRef(initialQuality);
  const frameCount = useRef(0);
  const lastFpsUpdate = useRef(0);
  const currentFps = useRef(0);
  const frameTimes = useRef([]);
  const lastFrameTime = useRef(0);
  
  // Initialize quantum stream with adaptive throttling
  const { data, stats, error, isConnected } = useQuantumStream({
    streamId,
    throttle: 1000 / QUALITY_PROFILES[qualityRef.current].updateRate,
    onMessage: handleQuantumMessage,
    onError: handleStreamError,
    binaryMode: true
  });

  // Handle incoming quantum messages
  const handleQuantumMessage = useCallback((message) => {
    try {
      const decoded = message instanceof ArrayBuffer 
        ? decodeDelta(message) 
        : message;
      
      if (sceneRef.current) {
        sceneRef.current.updateQuantumState({
          qubits: decoded.qubitMatrix,
          entanglement: decoded.entanglementGraph,
          coherence: decoded.coherenceScores,
          timestamp: decoded.timestamp
        });
      }
      
      // Track frame rendering performance
      const now = performance.now();
      if (lastFrameTime.current > 0) {
        frameTimes.current.push(now - lastFrameTime.current);
        if (frameTimes.current.length > 10) frameTimes.current.shift();
      }
      lastFrameTime.current = now;
      
      // Update FPS counter
      frameCount.current++;
      if (now - lastFpsUpdate.current >= 1000) {
        currentFps.current = frameCount.current / ((now - lastFpsUpdate.current) / 1000);
        frameCount.current = 0;
        lastFpsUpdate.current = now;
      }
      
    } catch (err) {
      console.error('Error processing quantum message:', err);
      handleStreamError(err);
    }
  }, [sceneRef]);

  // Handle stream errors
  const handleStreamError = useCallback((error) => {
    console.error('Quantum stream error:', error);
    // TODO: Implement NovaTrack error reporting
  }, []);

  // Adjust quality based on performance
  useEffect(() => {
    if (!autoAdjustQuality || !sceneRef.current) return;
    
    const targetFps = QUALITY_PROFILES[qualityRef.current].updateRate;
    const avgFrameTime = frameTimes.current.reduce((a, b) => a + b, 0) / frameTimes.current.length || 1000/60;
    const currentFps = 1000 / avgFrameTime;
    
    // Adjust quality if FPS is outside acceptable range
    if (currentFps < targetFps * 0.8) {
      // Lower quality
      const qualities = Object.keys(QUALITY_PROFILES);
      const currentIndex = qualities.indexOf(qualityRef.current);
      if (currentIndex < qualities.length - 1) {
        const newQuality = qualities[currentIndex + 1];
        console.log(`Reducing quality to ${newQuality} (FPS: ${currentFps.toFixed(1)} < ${targetFps * 0.8})`);
        qualityRef.current = newQuality;
        sceneRef.current.setQuality(QUALITY_PROFILES[newQuality]);
      }
    } else if (currentFps > targetFps * 1.2 && qualityRef.current !== 'high') {
      // Increase quality if we have headroom
      const qualities = Object.keys(QUALITY_PROFILES);
      const currentIndex = qualities.indexOf(qualityRef.current);
      if (currentIndex > 0) {
        const newQuality = qualities[currentIndex - 1];
        console.log(`Increasing quality to ${newQuality} (FPS: ${currentFps.toFixed(1)} > ${targetFps * 1.2})`);
        qualityRef.current = newQuality;
        sceneRef.current.setQuality(QUALITY_PROFILES[newQuality]);
      }
    }
  }, [data, autoAdjustQuality, sceneRef]);

  // Initialize scene with quality settings
  useEffect(() => {
    if (!sceneRef.current) return;
    
    // Configure initial quality settings
    sceneRef.current.setQuality(QUALITY_PROFILES[qualityRef.current]);
    
    // Set up quantum material properties
    sceneRef.current.setMaterialProperties({
      qubit: {
        baseColor: 0x8b5cf6,
        emissive: 0x6d28d9,
        emissiveIntensity: 0.5,
        metalness: 0.7,
        roughness: 0.3,
        transparent: true,
        opacity: 0.9
      },
      entanglement: {
        color: 0x7c3aed,
        opacity: 0.6,
        lineWidth: 0.05
      }
    });
    
    // Clean up on unmount
    return () => {
      if (sceneRef.current) {
        sceneRef.current.dispose();
      }
    };
  }, [sceneRef]);

  return {
    isConnected,
    currentFps: currentFps.current,
    quality: qualityRef.current,
    stats: {
      ...stats,
      frameTime: frameTimes.current[frameTimes.current.length - 1] || 0,
      avgFrameTime: frameTimes.current.reduce((a, b) => a + b, 0) / frameTimes.current.length || 0
    },
    error,
    setQuality: (quality) => {
      if (QUALITY_PROFILES[quality]) {
        qualityRef.current = quality;
        if (sceneRef.current) {
          sceneRef.current.setQuality(QUALITY_PROFILES[quality]);
        }
      }
    }
  };
}

/**
 * Higher-order component for quantum visualization
 */
export function withQuantumVisualization(Component) {
  return function WrappedComponent(props) {
    const visualization = useQuantumVisualization(props);
    return <Component {...props} quantumVisualization={visualization} />;
  };
}
