{"name": "nova-gateway", "version": "1.0.0", "description": "NovaGateway: Central API Gateway for the NovaFuse platform", "private": true, "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test:unit": "jest --testMatch='**/*.unit.test.js'", "test:integration": "jest --testMatch='**/*.integration.test.js'", "test:coverage": "jest --coverage", "lint": "eslint ."}, "repository": {"type": "git", "url": "git+https://github.com/Dartan1983/nova-gateway.git"}, "keywords": ["api", "gateway", "proxy", "routing", "novafuse"], "author": "NovaGRC", "license": "MIT", "bugs": {"url": "https://github.com/Dartan1983/nova-gateway/issues"}, "homepage": "https://github.com/Dartan1983/nova-gateway#readme", "dependencies": {"cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^6.7.0", "helmet": "^6.1.5", "http-proxy-middleware": "^2.0.6", "jsonwebtoken": "^9.0.1", "morgan": "^1.10.0", "winston": "^3.8.2"}, "devDependencies": {"eslint": "^8.46.0", "jest": "^29.5.0", "nodemon": "^2.0.22", "supertest": "^6.3.3"}, "engines": {"node": ">=14.0.0"}}