/* Dashboard Styles */

body {
    font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    color: #212529;
}

.card {
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    padding: 0.75rem 1.25rem;
}

.card-header h5 {
    margin-bottom: 0;
    font-weight: 600;
    color: #343a40;
}

.card-body {
    padding: 1.25rem;
}

.status-card {
    text-align: center;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.status-card h6 {
    margin-bottom: 10px;
    font-weight: 600;
    color: #6c757d;
}

.status-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #343a40;
}

.chart-container {
    height: 300px;
    width: 100%;
}

.tensor-container {
    height: 200px;
    width: 100%;
    background-color: #f8f9fa;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.range-value {
    display: inline-block;
    width: 40px;
    text-align: right;
    margin-left: 10px;
}

/* D3 Chart Styles */

.axis path,
.axis line {
    stroke: #e9ecef;
}

.axis text {
    fill: #6c757d;
    font-size: 12px;
}

.line {
    fill: none;
    stroke-width: 2px;
}

.line-comphyon {
    stroke: #007bff;
}

.line-resonance {
    stroke: #6f42c1;
}

.bar {
    fill: #007bff;
}

.bar-csde {
    fill: #007bff;
}

.bar-csfe {
    fill: #28a745;
}

.bar-csme {
    fill: #6f42c1;
}

.radar-area {
    fill-opacity: 0.5;
}

.radar-stroke {
    stroke-width: 2px;
    fill: none;
}

.radar-axis {
    stroke: #e9ecef;
    stroke-width: 1px;
}

.radar-circle {
    fill: none;
    stroke: #e9ecef;
    stroke-width: 1px;
}

.tensor-cell {
    stroke: #fff;
    stroke-width: 1px;
}

.tensor-cell-csde {
    fill: #007bff;
}

.tensor-cell-csfe {
    fill: #28a745;
}

.tensor-cell-csme {
    fill: #6f42c1;
}

.tensor-cell-fused {
    fill: #fd7e14;
}

/* Quantum Silence Indicator */

.quantum-silence-yes {
    color: #28a745;
    font-weight: bold;
}

.quantum-silence-no {
    color: #dc3545;
}

/* Dominant Engine Indicator */

.dominant-engine-csde {
    color: #007bff;
    font-weight: bold;
}

.dominant-engine-csfe {
    color: #28a745;
    font-weight: bold;
}

.dominant-engine-csme {
    color: #6f42c1;
    font-weight: bold;
}

.dominant-engine-none {
    color: #6c757d;
}

/* Tooltip Styles */

.tooltip {
    position: absolute;
    padding: 10px;
    background-color: rgba(0, 0, 0, 0.8);
    color: #fff;
    border-radius: 5px;
    pointer-events: none;
    font-size: 12px;
    z-index: 1000;
}

/* Form Styles */

.form-range {
    width: calc(100% - 50px);
    display: inline-block;
    vertical-align: middle;
}

.form-select {
    width: 100%;
}

.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
}

.btn-secondary {
    background-color: #6c757d;
    border-color: #6c757d;
}

/* Responsive Adjustments */

@media (max-width: 768px) {
    .status-value {
        font-size: 1.2rem;
    }
    
    .chart-container {
        height: 200px;
    }
    
    .tensor-container {
        height: 150px;
    }
}
