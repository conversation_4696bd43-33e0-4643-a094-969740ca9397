/**
 * NovaCore Vendor Assessment Controllers Index
 * 
 * This file exports all controllers for the vendor assessment module.
 */

const VendorController = require('./VendorController');
const AssessmentController = require('./AssessmentController');
const AssessmentTemplateController = require('./AssessmentTemplateController');
const DocumentController = require('./DocumentController');

module.exports = {
  VendorController,
  AssessmentController,
  AssessmentTemplateController,
  DocumentController
};
