/**
 * NovaCore Evidence Controller
 * 
 * This controller handles API requests related to evidence management.
 */

const { EvidenceService } = require('../services');
const logger = require('../../config/logger');

class EvidenceController {
  /**
   * Create a new evidence record
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async createEvidence(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const evidence = await EvidenceService.createEvidence(req.body, userId);
      
      res.status(201).json({
        success: true,
        data: evidence
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Get all evidence records
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async getAllEvidence(req, res, next) {
    try {
      // Extract filter parameters from query
      const filter = {};
      
      if (req.query.type) filter.type = req.query.type;
      if (req.query.category) filter.category = req.query.category;
      if (req.query.status) filter.status = req.query.status;
      if (req.query.framework) filter.framework = req.query.framework;
      if (req.query.control) filter.control = req.query.control;
      if (req.query.tags) filter.tags = req.query.tags.split(',');
      if (req.query.verificationStatus) filter.verificationStatus = req.query.verificationStatus;
      if (req.query.createdAfter) filter.createdAfter = req.query.createdAfter;
      if (req.query.createdBefore) filter.createdBefore = req.query.createdBefore;
      
      // Extract pagination and sorting options
      const options = {
        page: parseInt(req.query.page, 10) || 1,
        limit: parseInt(req.query.limit, 10) || 10
      };
      
      if (req.query.sort) {
        const [field, order] = req.query.sort.split(':');
        options.sort = { [field]: order === 'desc' ? -1 : 1 };
      }
      
      const result = await EvidenceService.getAllEvidence(filter, options);
      
      res.status(200).json({
        success: true,
        data: result.data,
        pagination: result.pagination
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Get evidence by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async getEvidenceById(req, res, next) {
    try {
      const evidence = await EvidenceService.getEvidenceById(req.params.id);
      
      res.status(200).json({
        success: true,
        data: evidence
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Update evidence by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async updateEvidence(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const evidence = await EvidenceService.updateEvidence(req.params.id, req.body, userId);
      
      res.status(200).json({
        success: true,
        data: evidence
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Delete evidence by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async deleteEvidence(req, res, next) {
    try {
      await EvidenceService.deleteEvidence(req.params.id);
      
      res.status(200).json({
        success: true,
        message: 'Evidence deleted successfully'
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Update evidence verification status
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async updateVerificationStatus(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const evidence = await EvidenceService.updateVerificationStatus(req.params.id, req.body, userId);
      
      res.status(200).json({
        success: true,
        data: evidence
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Find evidence by framework and control
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async findByFrameworkAndControl(req, res, next) {
    try {
      const { framework, control } = req.params;
      const evidence = await EvidenceService.findByFrameworkAndControl(framework, control);
      
      res.status(200).json({
        success: true,
        data: evidence
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Find evidence by tags
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async findByTags(req, res, next) {
    try {
      const tags = req.query.tags.split(',');
      const evidence = await EvidenceService.findByTags(tags);
      
      res.status(200).json({
        success: true,
        data: evidence
      });
    } catch (error) {
      next(error);
    }
  }
}

module.exports = new EvidenceController();
