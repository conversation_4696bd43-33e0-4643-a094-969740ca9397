/**
 * BottomNavigation Component
 * 
 * A mobile-friendly bottom navigation bar.
 */

import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';

/**
 * BottomNavigation component
 * 
 * @param {Object} props - Component props
 * @param {Array} props.items - Navigation items
 * @param {string} props.items[].id - Item ID
 * @param {string} props.items[].label - Item label
 * @param {React.ReactNode} props.items[].icon - Item icon
 * @param {Function} [props.items[].onClick] - Function to call when item is clicked
 * @param {string} [props.activeItemId] - Active item ID
 * @param {Function} [props.onItemClick] - Function to call when an item is clicked
 * @param {boolean} [props.showLabels=true] - Whether to show item labels
 * @param {boolean} [props.showOnMobileOnly=true] - Whether to show only on mobile devices
 * @param {string} [props.className] - Additional CSS class names
 * @param {Object} [props.style] - Additional inline styles
 * @returns {React.ReactElement} BottomNavigation component
 */
const BottomNavigation = ({
  items,
  activeItemId,
  onItemClick,
  showLabels = true,
  showOnMobileOnly = true,
  className = '',
  style = {}
}) => {
  const [isMobile, setIsMobile] = useState(false);
  
  // Check if device is mobile
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    // Initial check
    handleResize();
    
    // Add event listener
    window.addEventListener('resize', handleResize);
    
    // Cleanup
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);
  
  // Handle item click
  const handleItemClick = (item) => {
    // Call item's onClick handler if provided
    if (item.onClick) {
      item.onClick(item);
    }
    
    // Call onItemClick prop if provided
    if (onItemClick) {
      onItemClick(item);
    }
  };
  
  // Don't render if not mobile and showOnMobileOnly is true
  if (showOnMobileOnly && !isMobile) {
    return null;
  }
  
  return (
    <div
      className={`
        fixed bottom-0 left-0 right-0 z-50
        bg-white border-t border-gray-200 shadow-lg
        ${className}
      `}
      style={style}
      data-testid="bottom-navigation"
    >
      <div className="flex justify-around items-center h-16">
        {items.map(item => {
          const isActive = item.id === activeItemId;
          
          return (
            <button
              key={item.id}
              className={`
                flex flex-col items-center justify-center w-full h-full
                transition-colors duration-200
                ${isActive ? 'text-blue-600' : 'text-gray-600 hover:text-blue-600'}
              `}
              onClick={() => handleItemClick(item)}
              aria-label={item.label}
              data-testid={`nav-item-${item.id}`}
            >
              <div className={`${isActive ? 'text-blue-600' : 'text-gray-600'}`}>
                {item.icon}
              </div>
              
              {showLabels && (
                <span className="text-xs mt-1 font-medium">
                  {item.label}
                </span>
              )}
            </button>
          );
        })}
      </div>
    </div>
  );
};

BottomNavigation.propTypes = {
  items: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string.isRequired,
      label: PropTypes.string.isRequired,
      icon: PropTypes.node.isRequired,
      onClick: PropTypes.func
    })
  ).isRequired,
  activeItemId: PropTypes.string,
  onItemClick: PropTypes.func,
  showLabels: PropTypes.bool,
  showOnMobileOnly: PropTypes.bool,
  className: PropTypes.string,
  style: PropTypes.object
};

export default BottomNavigation;
