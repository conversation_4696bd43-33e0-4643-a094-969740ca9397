const fs = require('fs');
const path = require('path');
const NovaShield = require('../../utils/nova-shield');

// Create NovaShield instance
const novaShield = new NovaShield();

// Credential paths
const credentialsFile = path.join(process.cwd(), 'credentials', 'secure-credentials.json');
const backupFile = path.join(process.cwd(), 'credentials', 'secure-credentials.backup');

// Create credentials directory if it doesn't exist
if (!fs.existsSync(path.dirname(credentialsFile))) {
  fs.mkdirSync(path.dirname(credentialsFile), { recursive: true });
}

export default async function handler(req, res) {
  try {
    switch (req.method) {
      case 'POST':
        // Save credentials
        const { credentials } = req.body;
        
        // Encrypt credentials
        const encryptedData = novaShield.encryptCredentials(credentials);
        
        // Save to file
        fs.writeFileSync(credentialsFile, JSON.stringify(encryptedData, null, 2));
        fs.writeFileSync(backupFile, JSON.stringify(encryptedData, null, 2));
        
        res.status(200).json({
          success: true,
          message: 'Credentials saved successfully'
        });
        break;

      case 'GET':
        // Load credentials
        if (!fs.existsSync(credentialsFile)) {
          throw new Error('Credentials not found');
        }

        // Load encrypted data
        const data = JSON.parse(fs.readFileSync(credentialsFile));
        
        // Decrypt credentials
        const decrypted = novaShield.decryptCredentials(data);
        
        res.status(200).json({
          success: true,
          credentials: decrypted
        });
        break;

      case 'DELETE':
        // Delete credentials
        if (fs.existsSync(credentialsFile)) {
          fs.unlinkSync(credentialsFile);
        }
        if (fs.existsSync(backupFile)) {
          fs.unlinkSync(backupFile);
        }
        
        res.status(200).json({
          success: true,
          message: 'Credentials deleted'
        });
        break;

      default:
        res.setHeader('Allow', ['GET', 'POST', 'DELETE']);
        res.status(405).json({ error: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error('Credentials API error:', error);
    res.status(500).json({
      error: error.message
    });
  }
}
