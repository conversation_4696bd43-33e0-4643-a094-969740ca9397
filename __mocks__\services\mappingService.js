// Mock mapping service
const getAllFrameworks = jest.fn().mockResolvedValue([
  { id: 'gdpr', name: 'GDPR', description: 'General Data Protection Regulation' },
  { id: 'hipaa', name: 'HIPAA', description: 'Health Insurance Portability and Accountability Act' },
  { id: 'nist', name: 'NIST', description: 'National Institute of Standards and Technology' }
]);

const getMappingBetweenFrameworks = jest.fn().mockResolvedValue({
  sourceFramework: 'gdpr',
  targetFramework: 'hipaa',
  mappings: [
    {
      sourceControl: { id: 'gdpr-1', name: 'Data Protection by Design', description: 'Implement appropriate technical and organizational measures' },
      targetControls: [
        { id: 'hipaa-1', name: 'Security Management Process', description: 'Implement policies and procedures' }
      ]
    },
    {
      sourceControl: { id: 'gdpr-2', name: 'Right to Access', description: 'Provide data subjects with access to their personal data' },
      targetControls: [
        { id: 'hipaa-2', name: 'Access Control', description: 'Implement technical policies and procedures' }
      ]
    }
  ]
});

module.exports = {
  getAllFrameworks,
  getMappingBetweenFrameworks
};
