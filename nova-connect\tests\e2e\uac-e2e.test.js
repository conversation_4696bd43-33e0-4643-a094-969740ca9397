/**
 * NovaConnect UAC End-to-End Tests
 * 
 * This test suite validates the end-to-end functionality of the Universal API Connector.
 */

const request = require('supertest');
const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const app = require('../../app');
const { ConnectorRegistry } = require('../../src/registry/connector-registry');
const { FeatureFlagService } = require('../../src/services/feature-flag-service');
const { EncryptionService } = require('../../src/security/encryption-service');

let mongoServer;
let server;
let connectorRegistry;
let featureFlagService;
let encryptionService;
let authToken;

// Test data
const testUser = {
  email: '<EMAIL>',
  password: 'Test@123456',
  name: 'Test User'
};

const testConnector = {
  name: 'Test Connector',
  type: 'REST',
  description: 'Test connector for E2E tests',
  config: {
    baseUrl: 'https://api.example.com',
    authType: 'oauth2',
    headers: {
      'Content-Type': 'application/json'
    }
  }
};

const testTransformation = {
  name: 'Test Transformation',
  description: 'Test transformation for E2E tests',
  rules: [
    {
      source: 'data.id',
      target: 'id',
      transform: 'toString'
    },
    {
      source: 'data.attributes.name',
      target: 'name',
      transform: 'uppercase'
    }
  ]
};

const testWorkflow = {
  name: 'Test Workflow',
  description: 'Test workflow for E2E tests',
  steps: [
    {
      type: 'connector',
      connectorId: null, // Will be set during test
      operation: 'GET',
      path: '/users'
    },
    {
      type: 'transformation',
      transformationId: null, // Will be set during test
    }
  ]
};

// Setup and teardown
beforeAll(async () => {
  // Start MongoDB memory server
  mongoServer = await MongoMemoryServer.create();
  const mongoUri = mongoServer.getUri();
  
  // Connect to in-memory database
  await mongoose.connect(mongoUri, {
    useNewUrlParser: true,
    useUnifiedTopology: true
  });
  
  // Initialize services
  connectorRegistry = new ConnectorRegistry();
  featureFlagService = new FeatureFlagService();
  encryptionService = new EncryptionService();
  
  // Start server
  server = app.listen(0);
});

afterAll(async () => {
  // Stop server and close database connection
  server.close();
  await mongoose.disconnect();
  await mongoServer.stop();
});

// Helper function to authenticate
async function authenticate() {
  // Register user
  await request(app)
    .post('/api/auth/register')
    .send(testUser);
  
  // Login
  const response = await request(app)
    .post('/api/auth/login')
    .send({
      email: testUser.email,
      password: testUser.password
    });
  
  return response.body.token;
}

describe('NovaConnect UAC End-to-End Tests', () => {
  beforeAll(async () => {
    // Authenticate and get token
    authToken = await authenticate();
  });
  
  describe('Connector Management', () => {
    let connectorId;
    
    test('Should create a new connector', async () => {
      const response = await request(app)
        .post('/api/connectors')
        .set('Authorization', `Bearer ${authToken}`)
        .send(testConnector);
      
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('id');
      expect(response.body.name).toBe(testConnector.name);
      
      connectorId = response.body.id;
      testWorkflow.steps[0].connectorId = connectorId;
    });
    
    test('Should get connector by ID', async () => {
      const response = await request(app)
        .get(`/api/connectors/${connectorId}`)
        .set('Authorization', `Bearer ${authToken}`);
      
      expect(response.status).toBe(200);
      expect(response.body.id).toBe(connectorId);
      expect(response.body.name).toBe(testConnector.name);
    });
    
    test('Should update connector', async () => {
      const updatedConnector = {
        ...testConnector,
        name: 'Updated Connector'
      };
      
      const response = await request(app)
        .put(`/api/connectors/${connectorId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updatedConnector);
      
      expect(response.status).toBe(200);
      expect(response.body.name).toBe(updatedConnector.name);
    });
    
    test('Should list all connectors', async () => {
      const response = await request(app)
        .get('/api/connectors')
        .set('Authorization', `Bearer ${authToken}`);
      
      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
    });
  });
  
  describe('Transformation Management', () => {
    let transformationId;
    
    test('Should create a new transformation', async () => {
      const response = await request(app)
        .post('/api/transformations')
        .set('Authorization', `Bearer ${authToken}`)
        .send(testTransformation);
      
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('id');
      expect(response.body.name).toBe(testTransformation.name);
      
      transformationId = response.body.id;
      testWorkflow.steps[1].transformationId = transformationId;
    });
    
    test('Should get transformation by ID', async () => {
      const response = await request(app)
        .get(`/api/transformations/${transformationId}`)
        .set('Authorization', `Bearer ${authToken}`);
      
      expect(response.status).toBe(200);
      expect(response.body.id).toBe(transformationId);
      expect(response.body.name).toBe(testTransformation.name);
    });
    
    test('Should update transformation', async () => {
      const updatedTransformation = {
        ...testTransformation,
        name: 'Updated Transformation'
      };
      
      const response = await request(app)
        .put(`/api/transformations/${transformationId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updatedTransformation);
      
      expect(response.status).toBe(200);
      expect(response.body.name).toBe(updatedTransformation.name);
    });
    
    test('Should list all transformations', async () => {
      const response = await request(app)
        .get('/api/transformations')
        .set('Authorization', `Bearer ${authToken}`);
      
      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
    });
  });
  
  describe('Workflow Management', () => {
    let workflowId;
    
    test('Should create a new workflow', async () => {
      const response = await request(app)
        .post('/api/workflows')
        .set('Authorization', `Bearer ${authToken}`)
        .send(testWorkflow);
      
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('id');
      expect(response.body.name).toBe(testWorkflow.name);
      
      workflowId = response.body.id;
    });
    
    test('Should get workflow by ID', async () => {
      const response = await request(app)
        .get(`/api/workflows/${workflowId}`)
        .set('Authorization', `Bearer ${authToken}`);
      
      expect(response.status).toBe(200);
      expect(response.body.id).toBe(workflowId);
      expect(response.body.name).toBe(testWorkflow.name);
    });
    
    test('Should update workflow', async () => {
      const updatedWorkflow = {
        ...testWorkflow,
        name: 'Updated Workflow'
      };
      
      const response = await request(app)
        .put(`/api/workflows/${workflowId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updatedWorkflow);
      
      expect(response.status).toBe(200);
      expect(response.body.name).toBe(updatedWorkflow.name);
    });
    
    test('Should list all workflows', async () => {
      const response = await request(app)
        .get('/api/workflows')
        .set('Authorization', `Bearer ${authToken}`);
      
      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
    });
    
    test('Should execute workflow', async () => {
      // Mock the connector execution
      jest.spyOn(connectorRegistry, 'executeConnector').mockResolvedValue({
        data: {
          id: 123,
          attributes: {
            name: 'test user'
          }
        }
      });
      
      const response = await request(app)
        .post(`/api/workflows/${workflowId}/execute`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({});
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('result');
      expect(response.body.result).toHaveProperty('id');
      expect(response.body.result).toHaveProperty('name');
      expect(response.body.result.name).toBe('TEST USER'); // Uppercase transformation
    });
  });
  
  describe('Feature Flag Management', () => {
    test('Should get feature flags', async () => {
      const response = await request(app)
        .get('/api/feature-flags')
        .set('Authorization', `Bearer ${authToken}`);
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('flags');
      expect(typeof response.body.flags).toBe('object');
    });
    
    test('Should update feature flags', async () => {
      const flags = {
        'premium.transformations': true,
        'premium.workflows': true
      };
      
      const response = await request(app)
        .put('/api/feature-flags')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ flags });
      
      expect(response.status).toBe(200);
      expect(response.body.flags['premium.transformations']).toBe(true);
      expect(response.body.flags['premium.workflows']).toBe(true);
    });
  });
  
  describe('Security', () => {
    test('Should encrypt and decrypt data', async () => {
      const testData = { secret: 'test-secret' };
      
      // Encrypt
      const encryptResponse = await request(app)
        .post('/api/security/encrypt')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ data: testData });
      
      expect(encryptResponse.status).toBe(200);
      expect(encryptResponse.body).toHaveProperty('encryptedData');
      
      // Decrypt
      const decryptResponse = await request(app)
        .post('/api/security/decrypt')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ encryptedData: encryptResponse.body.encryptedData });
      
      expect(decryptResponse.status).toBe(200);
      expect(decryptResponse.body).toHaveProperty('data');
      expect(decryptResponse.body.data).toEqual(testData);
    });
  });
  
  describe('Audit Logging', () => {
    test('Should retrieve audit logs', async () => {
      const response = await request(app)
        .get('/api/audit-logs')
        .set('Authorization', `Bearer ${authToken}`);
      
      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
    });
  });
});
