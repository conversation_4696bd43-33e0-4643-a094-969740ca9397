/**
 * CSDE Performance Routes
 * 
 * This module defines routes for the CSDE performance monitoring service.
 */

const express = require('express');
const router = express.Router();
const { CSEDPerformanceMonitoringService } = require('../csde/monitoring');

// CSDE Performance Monitoring Service
let performanceMonitor = null;

/**
 * Initialize CSDE Performance routes
 * @param {Object} options - Initialization options
 */
function initialize(options = {}) {
  const logger = options.logger || console;
  
  logger.info('Initializing CSDE Performance routes');
  
  // Create CSDE Performance Monitoring Service if not already created
  if (!performanceMonitor) {
    performanceMonitor = new CSEDPerformanceMonitoringService({
      sampleInterval: parseInt(process.env.PERFORMANCE_SAMPLE_INTERVAL || '60', 10),
      historySize: parseInt(process.env.PERFORMANCE_HISTORY_SIZE || '60', 10),
      enableSystemMetrics: process.env.ENABLE_SYSTEM_METRICS !== 'false',
      enableDetailedMetrics: process.env.ENABLE_DETAILED_METRICS !== 'false',
      enableAlerts: process.env.ENABLE_ALERTS !== 'false',
      alertThresholds: {
        cpuUsage: parseFloat(process.env.ALERT_THRESHOLD_CPU_USAGE || '0.8'),
        memoryUsage: parseFloat(process.env.ALERT_THRESHOLD_MEMORY_USAGE || '0.8'),
        errorRate: parseFloat(process.env.ALERT_THRESHOLD_ERROR_RATE || '0.05'),
        latency: parseFloat(process.env.ALERT_THRESHOLD_LATENCY || '100'),
        performanceFactor: parseFloat(process.env.ALERT_THRESHOLD_PERFORMANCE_FACTOR || '1000')
      },
      logger
    });
    
    logger.info('CSDE Performance Monitoring Service created');
  }
  
  // Set up alert event listener
  performanceMonitor.on('alert', (alert) => {
    logger.warn(`CSDE Performance Alert: ${alert.message}`, { alert });
  });
  
  // Set up metrics updated event listener
  performanceMonitor.on('metrics-updated', () => {
    logger.debug('CSDE Performance metrics updated');
  });
  
  return performanceMonitor;
}

/**
 * Get performance metrics
 */
router.get('/metrics', async (req, res) => {
  try {
    // Initialize if not already initialized
    if (!performanceMonitor) {
      performanceMonitor = initialize({ logger: req.app.locals.logger });
    }
    
    // Get metrics
    const metrics = performanceMonitor.getMetrics();
    
    // Return metrics
    res.json({
      success: true,
      metrics
    });
  } catch (error) {
    req.app.locals.logger.error('Error getting performance metrics', { error: error.message });
    
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * Get visualization data
 */
router.get('/visualization', async (req, res) => {
  try {
    // Initialize if not already initialized
    if (!performanceMonitor) {
      performanceMonitor = initialize({ logger: req.app.locals.logger });
    }
    
    // Get visualization data
    const visualizationData = performanceMonitor.getVisualizationData();
    
    // Return visualization data
    res.json({
      success: true,
      data: visualizationData
    });
  } catch (error) {
    req.app.locals.logger.error('Error getting visualization data', { error: error.message });
    
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * Reset metrics
 */
router.post('/metrics/reset', async (req, res) => {
  try {
    // Initialize if not already initialized
    if (!performanceMonitor) {
      performanceMonitor = initialize({ logger: req.app.locals.logger });
    }
    
    // Reset metrics
    performanceMonitor.resetMetrics();
    
    // Return success
    res.json({
      success: true,
      message: 'Metrics reset successfully'
    });
  } catch (error) {
    req.app.locals.logger.error('Error resetting metrics', { error: error.message });
    
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * Get alerts
 */
router.get('/alerts', async (req, res) => {
  try {
    // Initialize if not already initialized
    if (!performanceMonitor) {
      performanceMonitor = initialize({ logger: req.app.locals.logger });
    }
    
    // Get metrics
    const metrics = performanceMonitor.getMetrics();
    
    // Return alerts
    res.json({
      success: true,
      alerts: metrics.alerts || []
    });
  } catch (error) {
    req.app.locals.logger.error('Error getting alerts', { error: error.message });
    
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * Get health status
 */
router.get('/health', async (req, res) => {
  try {
    // Initialize if not already initialized
    if (!performanceMonitor) {
      performanceMonitor = initialize({ logger: req.app.locals.logger });
    }
    
    // Get metrics
    const metrics = performanceMonitor.getMetrics();
    
    // Calculate error rate
    const errorRate = metrics.engine.totalOperations > 0 
      ? metrics.engine.failedOperations / metrics.engine.totalOperations 
      : 0;
    
    // Determine health status
    let status = 'healthy';
    
    if (errorRate > 0.05 || metrics.engine.averageLatency > 100) {
      status = 'degraded';
    }
    
    if (errorRate > 0.1 || metrics.engine.averageLatency > 200) {
      status = 'unhealthy';
    }
    
    // Return health status
    res.json({
      success: true,
      health: {
        status,
        metrics: {
          totalOperations: metrics.engine.totalOperations,
          successfulOperations: metrics.engine.successfulOperations,
          failedOperations: metrics.engine.failedOperations,
          errorRate,
          averageLatency: metrics.engine.averageLatency,
          performanceFactor: metrics.engine.performanceFactor,
          cacheHitRate: metrics.cache.hitRate
        },
        system: {
          cpuUsage: metrics.system.cpu.length > 0 
            ? metrics.system.cpu[metrics.system.cpu.length - 1].value 
            : 0,
          memoryUsage: metrics.system.memory.length > 0 
            ? metrics.system.memory[metrics.system.memory.length - 1].value 
            : 0,
          eventLoopLag: metrics.system.eventLoop.length > 0 
            ? metrics.system.eventLoop[metrics.system.eventLoop.length - 1].value 
            : 0
        },
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    req.app.locals.logger.error('Error getting health status', { error: error.message });
    
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

module.exports = {
  router,
  initialize,
  getPerformanceMonitor: () => performanceMonitor
};
