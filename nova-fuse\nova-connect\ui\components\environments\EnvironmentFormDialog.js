/**
 * Environment Form Dialog Component
 * 
 * This component allows users to create or edit environments.
 */

import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Button, 
  Checkbox,
  CircularProgress,
  Dialog, 
  DialogActions, 
  DialogContent, 
  DialogTitle, 
  FormControlLabel,
  Grid,
  TextField,
  Typography
} from '@mui/material';
import { SketchPicker } from 'react-color';
import { useEnvironment } from '../../contexts/EnvironmentContext';

const EnvironmentFormDialog = ({ open, onClose, environment }) => {
  const { createEnvironment, updateEnvironment } = useEnvironment();
  
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    color: '#2196f3',
    isDefault: false
  });
  
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);
  const [showColorPicker, setShowColorPicker] = useState(false);
  
  // Initialize form data when environment changes
  useEffect(() => {
    if (environment) {
      setFormData({
        name: environment.name || '',
        description: environment.description || '',
        color: environment.color || '#2196f3',
        isDefault: environment.isDefault || false
      });
    } else {
      setFormData({
        name: '',
        description: '',
        color: '#2196f3',
        isDefault: false
      });
    }
    
    setErrors({});
  }, [environment, open]);
  
  // Handle form input change
  const handleChange = (e) => {
    const { name, value, checked, type } = e.target;
    
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
    
    // Clear error for this field
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: null
      });
    }
  };
  
  // Handle color change
  const handleColorChange = (color) => {
    setFormData({
      ...formData,
      color: color.hex
    });
  };
  
  // Validate form
  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    }
    
    setErrors(newErrors);
    
    return Object.keys(newErrors).length === 0;
  };
  
  // Handle form submission
  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }
    
    try {
      setLoading(true);
      
      if (environment) {
        // Update existing environment
        await updateEnvironment(environment.id, formData);
      } else {
        // Create new environment
        await createEnvironment(formData);
      }
      
      onClose();
    } catch (error) {
      console.error('Error saving environment:', error);
      alert(`Failed to save environment: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <Dialog 
      open={open} 
      onClose={onClose} 
      maxWidth="sm" 
      fullWidth
    >
      <DialogTitle>
        {environment ? 'Edit Environment' : 'Create Environment'}
      </DialogTitle>
      
      <DialogContent>
        <Grid container spacing={2} sx={{ mt: 1 }}>
          <Grid item xs={12}>
            <TextField
              name="name"
              label="Environment Name"
              value={formData.name}
              onChange={handleChange}
              fullWidth
              required
              error={Boolean(errors.name)}
              helperText={errors.name}
            />
          </Grid>
          
          <Grid item xs={12}>
            <TextField
              name="description"
              label="Description"
              value={formData.description}
              onChange={handleChange}
              fullWidth
              multiline
              rows={2}
            />
          </Grid>
          
          <Grid item xs={12}>
            <Typography variant="subtitle2" gutterBottom>
              Environment Color
            </Typography>
            
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Box
                sx={{
                  width: 36,
                  height: 36,
                  borderRadius: 1,
                  bgcolor: formData.color,
                  cursor: 'pointer',
                  border: 1,
                  borderColor: 'divider',
                  mr: 2
                }}
                onClick={() => setShowColorPicker(!showColorPicker)}
              />
              
              <TextField
                name="color"
                value={formData.color}
                onChange={handleChange}
                size="small"
                sx={{ width: 120 }}
              />
            </Box>
            
            {showColorPicker && (
              <Box sx={{ mt: 2, position: 'relative', zIndex: 1 }}>
                <Box
                  sx={{
                    position: 'fixed',
                    top: 0,
                    right: 0,
                    bottom: 0,
                    left: 0
                  }}
                  onClick={() => setShowColorPicker(false)}
                />
                <SketchPicker
                  color={formData.color}
                  onChange={handleColorChange}
                />
              </Box>
            )}
          </Grid>
          
          <Grid item xs={12}>
            <FormControlLabel
              control={
                <Checkbox
                  name="isDefault"
                  checked={formData.isDefault}
                  onChange={handleChange}
                  color="primary"
                />
              }
              label="Set as default environment"
            />
          </Grid>
        </Grid>
      </DialogContent>
      
      <DialogActions>
        <Button onClick={onClose} disabled={loading}>
          Cancel
        </Button>
        <Button 
          onClick={handleSubmit} 
          variant="contained" 
          disabled={loading}
          startIcon={loading ? <CircularProgress size={20} /> : null}
        >
          {loading ? 'Saving...' : 'Save'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default EnvironmentFormDialog;
