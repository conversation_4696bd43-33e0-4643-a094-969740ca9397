<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NovaBrowser - Coherence-First Web Gateway</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=SF+Pro+Display:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --phi: 1.618033988749;
            --phi-inverse: 0.618033988749;

            /* Modern Color Palette */
            --bg-primary: #0a0a0f;
            --bg-secondary: #1a1a2e;
            --bg-tertiary: #16213e;
            --bg-glass: rgba(255, 255, 255, 0.05);
            --bg-glass-hover: rgba(255, 255, 255, 0.08);

            /* Accent Colors */
            --accent-primary: #6366f1;
            --accent-secondary: #8b5cf6;
            --accent-success: #10b981;
            --accent-warning: #f59e0b;
            --accent-danger: #ef4444;

            /* Coherence Colors */
            --coherence-divine: #a855f7;
            --coherence-high: #10b981;
            --coherence-medium: #f59e0b;
            --coherence-low: #ef4444;

            /* Text Colors */
            --text-primary: #ffffff;
            --text-secondary: rgba(255, 255, 255, 0.8);
            --text-tertiary: rgba(255, 255, 255, 0.6);
            --text-muted: rgba(255, 255, 255, 0.4);

            /* Borders */
            --border-primary: rgba(255, 255, 255, 0.1);
            --border-secondary: rgba(255, 255, 255, 0.05);

            /* Shadows */
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            --shadow-glow: 0 0 20px rgba(99, 102, 241, 0.3);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-tertiary) 100%);
            color: var(--text-primary);
            height: 100vh;
            overflow: hidden;
            font-size: 14px;
            line-height: 1.5;
        }

        /* Modern Browser Chrome */
        .browser-chrome {
            background: var(--bg-glass);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--border-primary);
            padding: 12px 16px 8px;
            position: relative;
        }

        .browser-chrome::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, var(--accent-primary), transparent);
            opacity: 0.5;
        }

        .nav-bar {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 12px;
        }

        .nav-buttons {
            display: flex;
            gap: 4px;
        }

        .nav-btn {
            background: var(--bg-glass);
            border: 1px solid var(--border-primary);
            color: var(--text-secondary);
            padding: 8px 12px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(10px);
        }

        .nav-btn:hover {
            background: var(--bg-glass-hover);
            color: var(--text-primary);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .nav-btn:active {
            transform: translateY(0);
        }

        .nav-btn:disabled {
            opacity: 0.4;
            cursor: not-allowed;
            transform: none;
        }

        .address-bar {
            flex: 1;
            display: flex;
            align-items: center;
            background: var(--bg-glass);
            backdrop-filter: blur(20px);
            border: 1px solid var(--border-primary);
            border-radius: 12px;
            padding: 10px 16px;
            margin: 0 12px;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
        }

        .address-bar:focus-within {
            border-color: var(--accent-primary);
            box-shadow: var(--shadow-glow);
            background: rgba(255, 255, 255, 0.08);
        }

        .coherence-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 12px;
            position: relative;
            transition: all 0.3s ease;
        }

        .coherence-indicator::after {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            border-radius: 50%;
            background: inherit;
            opacity: 0.3;
            animation: pulse 2s infinite;
        }

        .coherence-high {
            background: var(--coherence-high);
            box-shadow: 0 0 8px var(--coherence-high);
        }
        .coherence-medium {
            background: var(--coherence-medium);
            box-shadow: 0 0 8px var(--coherence-medium);
        }
        .coherence-low {
            background: var(--coherence-low);
            box-shadow: 0 0 8px var(--coherence-low);
        }

        .url-input {
            flex: 1;
            background: none;
            border: none;
            color: var(--text-primary);
            font-size: 14px;
            font-weight: 400;
            outline: none;
            font-family: 'JetBrains Mono', monospace;
        }

        .url-input::placeholder {
            color: var(--text-muted);
            font-family: 'Inter', sans-serif;
        }

        .go-btn {
            background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
            border: none;
            color: white;
            padding: 6px 16px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            font-size: 13px;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            margin-left: 8px;
        }

        .go-btn:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-lg);
        }

        .go-btn:active {
            transform: translateY(0);
        }

        /* Modern Status Bar */
        .status-bar {
            display: flex;
            align-items: center;
            gap: 20px;
            font-size: 12px;
            padding: 0 4px;
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 4px 8px;
            border-radius: 6px;
            background: var(--bg-glass);
            border: 1px solid var(--border-secondary);
            transition: all 0.2s ease;
        }

        .status-item:hover {
            background: var(--bg-glass-hover);
            border-color: var(--border-primary);
        }

        .status-value {
            font-weight: 600;
            font-family: 'JetBrains Mono', monospace;
        }

        .status-item .emoji {
            font-size: 14px;
        }

        /* Main Content Area */
        .content-area {
            height: calc(100vh - 140px);
            display: flex;
            position: relative;
        }

        /* Modern Sidebar */
        .sidebar {
            width: 320px;
            background: var(--bg-glass);
            backdrop-filter: blur(20px);
            border-right: 1px solid var(--border-primary);
            padding: 24px 20px;
            overflow-y: auto;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: var(--bg-glass);
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: var(--border-primary);
            border-radius: 3px;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: var(--text-muted);
        }

        .sidebar h3 {
            margin-bottom: 16px;
            color: var(--text-primary);
            font-size: 16px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .sidebar h3::before {
            content: '';
            width: 3px;
            height: 16px;
            background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
            border-radius: 2px;
        }

        .metric-card {
            background: var(--bg-glass);
            backdrop-filter: blur(10px);
            border: 1px solid var(--border-primary);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 16px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .metric-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary));
        }

        .metric-card:hover {
            background: var(--bg-glass-hover);
            border-color: var(--accent-primary);
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .metric-title {
            font-size: 12px;
            color: var(--text-tertiary);
            margin-bottom: 8px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .metric-value {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
            color: var(--text-primary);
            font-family: 'JetBrains Mono', monospace;
        }

        .metric-details {
            font-size: 11px;
            color: var(--text-muted);
            font-weight: 400;
        }

        .violations-list {
            max-height: 240px;
            overflow-y: auto;
            padding-right: 4px;
        }

        .violations-list::-webkit-scrollbar {
            width: 4px;
        }

        .violations-list::-webkit-scrollbar-track {
            background: transparent;
        }

        .violations-list::-webkit-scrollbar-thumb {
            background: var(--border-primary);
            border-radius: 2px;
        }

        .violation-item {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid var(--accent-danger);
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 8px;
            font-size: 12px;
            transition: all 0.2s ease;
            position: relative;
        }

        .violation-item:hover {
            background: rgba(239, 68, 68, 0.15);
            transform: translateX(4px);
        }

        .auto-fix-btn {
            background: linear-gradient(135deg, var(--coherence-high), #059669);
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 11px;
            font-weight: 600;
            margin-top: 8px;
            transition: all 0.2s ease;
        }

        .auto-fix-btn:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        /* Modern Website Frame */
        .website-frame {
            flex: 1;
            background: var(--bg-primary);
            position: relative;
            border-radius: 12px;
            margin: 8px;
            overflow: hidden;
            border: 1px solid var(--border-primary);
        }

        .website-iframe {
            width: 100%;
            height: 100%;
            border: none;
            border-radius: 12px;
        }

        /* Modern Coherence Overlay */
        .coherence-overlay {
            position: absolute;
            top: 20px;
            right: 20px;
            background: var(--bg-glass);
            backdrop-filter: blur(20px);
            border: 1px solid var(--border-primary);
            border-radius: 12px;
            padding: 20px;
            min-width: 240px;
            box-shadow: var(--shadow-xl);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .coherence-overlay:hover {
            background: var(--bg-glass-hover);
            transform: translateY(-2px);
            box-shadow: var(--shadow-xl), var(--shadow-glow);
        }

        .overlay-title {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 16px;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .overlay-title::after {
            content: '';
            flex: 1;
            height: 1px;
            background: linear-gradient(90deg, var(--accent-primary), transparent);
        }

        .overlay-metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            font-size: 12px;
            padding: 4px 0;
        }

        .overlay-metric span:first-child {
            color: var(--text-secondary);
            font-weight: 500;
        }

        .overlay-metric span:last-child {
            color: var(--text-primary);
            font-weight: 600;
            font-family: 'JetBrains Mono', monospace;
        }

        .psi-snap {
            text-align: center;
            margin-top: 16px;
            padding: 12px;
            background: linear-gradient(135deg, var(--coherence-high), #059669);
            border-radius: 8px;
            font-weight: 600;
            font-size: 12px;
            color: white;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: var(--shadow-md);
        }

        .psi-snap.inactive {
            background: linear-gradient(135deg, var(--coherence-medium), #d97706);
        }

        /* Modern Loading States */
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            flex-direction: column;
            gap: 24px;
            background: var(--bg-primary);
            color: var(--text-primary);
        }

        .spinner {
            width: 48px;
            height: 48px;
            border: 3px solid var(--border-primary);
            border-left: 3px solid var(--accent-primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        .loading-text {
            font-size: 14px;
            color: var(--text-secondary);
            font-weight: 500;
        }

        /* Animations */
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        @keyframes slideIn {
            0% { transform: translateX(100%); opacity: 0; }
            100% { transform: translateX(0); opacity: 1; }
        }

        @keyframes slideOut {
            0% { transform: translateX(0); opacity: 1; }
            100% { transform: translateX(100%); opacity: 0; }
        }

        @keyframes fadeIn {
            0% { opacity: 0; transform: translateY(10px); }
            100% { opacity: 1; transform: translateY(0); }
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .sidebar {
                width: 280px;
            }
        }

        @media (max-width: 768px) {
            .sidebar {
                position: absolute;
                left: -320px;
                z-index: 1000;
                height: 100%;
                transition: left 0.3s ease;
            }

            .sidebar.open {
                left: 0;
            }

            .coherence-overlay {
                position: fixed;
                top: 80px;
                right: 10px;
                min-width: 200px;
            }
        }
    </style>
</head>
<body>
    <!-- Browser Chrome -->
    <div class="browser-chrome">
        <div class="nav-bar">
            <div class="nav-buttons">
                <button class="nav-btn" id="back-btn" disabled>←</button>
                <button class="nav-btn" id="forward-btn" disabled>→</button>
                <button class="nav-btn" id="refresh-btn">⟳</button>
            </div>
            
            <div class="address-bar">
                <div class="coherence-indicator coherence-high" id="coherence-indicator"></div>
                <input type="text" class="url-input" id="url-input" 
                       placeholder="Enter URL or search with consciousness validation...">
                <button class="go-btn" id="go-btn">Go</button>
            </div>
            
            <div class="nav-buttons">
                <button class="nav-btn">⚙️</button>
                <button class="nav-btn">👁️</button>
                <button class="nav-btn">🛡️</button>
            </div>
        </div>
        
        <div class="status-bar">
            <div class="status-item">
                <span class="emoji">🧬</span>
                <span>Coherence:</span>
                <span class="status-value" id="status-coherence">--</span>
            </div>
            <div class="status-item">
                <span class="emoji">👁️</span>
                <span>Accessibility:</span>
                <span class="status-value" id="status-accessibility">--</span>
            </div>
            <div class="status-item">
                <span class="emoji">🛡️</span>
                <span>Threats:</span>
                <span class="status-value" id="status-threats">--</span>
            </div>
            <div class="status-item">
                <span class="emoji">⚡</span>
                <span>Analysis:</span>
                <span class="status-value" id="status-speed">--</span>
            </div>
        </div>
    </div>
    
    <!-- Main Content -->
    <div class="content-area">
        <!-- Sidebar -->
        <div class="sidebar">
            <h3>🧬 NovaDNA Analysis</h3>
            <div class="metric-card">
                <div class="metric-title">Overall Coherence</div>
                <div class="metric-value" id="coherence-score">--</div>
                <div class="metric-details">Structural • Functional • Relational</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-title">Ψ-Snap Status</div>
                <div class="metric-value" id="psi-snap-status">INACTIVE</div>
                <div class="metric-details">82/18 Comphyological Model</div>
            </div>
            
            <h3>👁️ NovaVision Compliance</h3>
            <div class="metric-card">
                <div class="metric-title">Accessibility Score</div>
                <div class="metric-value" id="accessibility-score">--</div>
                <div class="metric-details">WCAG 2.1 • ADA Compliance</div>
            </div>
            
            <div class="violations-list" id="violations-list">
                <!-- Violations will be populated here -->
            </div>
            
            <h3>🛡️ NovaShield Protection</h3>
            <div class="metric-card">
                <div class="metric-title">Threat Level</div>
                <div class="metric-value" id="threat-level">--</div>
                <div class="metric-details">Real-time monitoring</div>
            </div>
        </div>
        
        <!-- Website Frame -->
        <div class="website-frame">
            <div class="loading" id="loading-screen">
                <div class="spinner"></div>
                <div class="loading-text">Analyzing page coherence...</div>
            </div>

            <iframe class="website-iframe" id="website-iframe" style="display: none;"></iframe>

            <div class="coherence-overlay" id="coherence-overlay" style="display: none;">
                <div class="overlay-title">🧬 Live Analysis</div>
                <div class="overlay-metric">
                    <span>Structural:</span>
                    <span id="overlay-structural">--</span>
                </div>
                <div class="overlay-metric">
                    <span>Functional:</span>
                    <span id="overlay-functional">--</span>
                </div>
                <div class="overlay-metric">
                    <span>Relational:</span>
                    <span id="overlay-relational">--</span>
                </div>
                <div class="psi-snap inactive" id="overlay-psi-snap">
                    Ψ-Snap: INACTIVE
                </div>
            </div>
        </div>
    </div>

    <script>
        // NovaBrowser UI Controller
        class NovaBrowserUI {
            constructor() {
                this.currentUrl = '';
                this.analysisData = {};
                this.initializeEventListeners();
                this.loadDefaultPage();
            }
            
            initializeEventListeners() {
                document.getElementById('go-btn').addEventListener('click', () => this.navigateToUrl());
                document.getElementById('url-input').addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') this.navigateToUrl();
                });
                document.getElementById('refresh-btn').addEventListener('click', () => this.refreshPage());
            }
            
            navigateToUrl() {
                const url = document.getElementById('url-input').value.trim();
                if (!url) return;

                // Handle special URLs
                if (url === 'test' || url === 'novabrowser://test') {
                    this.loadPage('working-test.html');
                    return;
                }

                // Add protocol if missing
                let fullUrl;
                if (url.startsWith('http://') || url.startsWith('https://')) {
                    fullUrl = url;
                } else if (url.includes('.')) {
                    fullUrl = `https://${url}`;
                } else {
                    // Search query
                    fullUrl = `https://www.google.com/search?q=${encodeURIComponent(url)}`;
                }

                this.loadPage(fullUrl);
            }
            
            loadPage(url) {
                this.currentUrl = url;
                this.showLoading();
                
                // Simulate page load and analysis
                setTimeout(() => {
                    this.hideLoading();
                    this.showPage(url);
                    this.runAnalysis();
                }, 2000);
            }
            
            loadDefaultPage() {
                // Load a website that allows iframe embedding
                const defaultUrl = 'https://httpbin.org/html';
                document.getElementById('url-input').value = defaultUrl;
                this.loadPage(defaultUrl);
            }
            
            showLoading() {
                document.getElementById('loading-screen').style.display = 'flex';
                document.getElementById('website-iframe').style.display = 'none';
                document.getElementById('coherence-overlay').style.display = 'none';
            }
            
            hideLoading() {
                document.getElementById('loading-screen').style.display = 'none';
                document.getElementById('website-iframe').style.display = 'block';
                document.getElementById('coherence-overlay').style.display = 'block';
            }
            
            showPage(url) {
                const iframe = document.getElementById('website-iframe');

                // List of sites that block iframe embedding
                const blockedSites = [
                    'google.com', 'facebook.com', 'twitter.com', 'linkedin.com',
                    'instagram.com', 'youtube.com', 'github.com', 'stackoverflow.com'
                ];

                const needsProxy = blockedSites.some(site => url.includes(site));

                if (needsProxy) {
                    // Use proxy server to bypass iframe restrictions
                    const proxyUrl = `http://localhost:3001/proxy?url=${encodeURIComponent(url)}`;
                    iframe.src = proxyUrl;
                    console.log(`Using proxy for: ${url}`);
                } else {
                    // Direct iframe for sites that allow it
                    iframe.src = url;
                }

                // Handle iframe load errors
                iframe.onerror = () => {
                    this.showIframeError(url);
                };
            }

            showIframeError(url) {
                const iframe = document.getElementById('website-iframe');
                iframe.style.display = 'none';

                const errorDiv = document.createElement('div');
                errorDiv.style.cssText = `
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    height: 100%;
                    background: rgba(255, 71, 87, 0.1);
                    color: white;
                    text-align: center;
                    padding: 40px;
                `;

                errorDiv.innerHTML = `
                    <h2>🔒 Site Blocked Iframe Embedding</h2>
                    <p>This website prevents embedding for security reasons.</p>
                    <p><strong>URL:</strong> ${url}</p>
                    <button onclick="window.open('${url}', '_blank')"
                            style="background: #667eea; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin-top: 20px;">
                        🌐 Open in New Tab
                    </button>
                    <p style="margin-top: 20px; font-size: 12px; opacity: 0.7;">
                        NovaBrowser analysis still active - coherence data from URL structure
                    </p>
                `;

                const frame = document.querySelector('.website-frame');
                frame.appendChild(errorDiv);
            }
            
            async runAnalysis() {
                const startTime = performance.now();

                try {
                    // Get backend coherence data
                    const backendResponse = await fetch('http://localhost:8090/status', {
                        method: 'GET',
                        mode: 'cors',
                        headers: { 'Content-Type': 'application/json' }
                    });

                    let backendCoherence = 0.5;
                    if (backendResponse.ok) {
                        const backendData = await backendResponse.json();
                        backendCoherence = backendData.coherence || 0.5;
                    }

                    // Analyze the loaded page via iframe (if accessible)
                    const iframe = document.getElementById('website-iframe');
                    let pageAnalysis = this.analyzeCurrentPage();

                    // Combine local analysis with backend data
                    const analysisResults = {
                        coherence: {
                            overall: Math.round((pageAnalysis.structural + pageAnalysis.functional + backendCoherence) / 3 * 100),
                            structural: Math.round(pageAnalysis.structural * 100),
                            functional: Math.round(pageAnalysis.functional * 100),
                            relational: Math.round(backendCoherence * 100)
                        },
                        accessibility: pageAnalysis.accessibility,
                        security: pageAnalysis.security,
                        performance: {
                            analysisTime: Math.round(performance.now() - startTime)
                        }
                    };

                    this.updateAnalysisResults(analysisResults);

                } catch (error) {
                    console.error('Analysis failed:', error);
                    this.updateAnalysisResults({
                        coherence: { overall: 0, structural: 0, functional: 0, relational: 0 },
                        accessibility: { score: 0, violations: ['Analysis failed: ' + error.message] },
                        security: { level: 'UNKNOWN', threats: 0 },
                        performance: { analysisTime: Math.round(performance.now() - startTime) }
                    });
                }
            }

            analyzeCurrentPage() {
                // Analyze the current page structure
                const url = this.currentUrl;

                // Basic URL-based analysis
                let structural = 0.8; // Default for most sites
                let functional = 0.7; // Default for most sites

                // URL-based heuristics
                if (url.includes('gov') || url.includes('edu')) {
                    structural += 0.1; // Government/education sites tend to be well-structured
                }
                if (url.includes('https://')) {
                    functional += 0.1; // HTTPS is good for functionality
                }
                if (url.includes('example.com')) {
                    structural = 0.9; // Example.com is well-structured
                    functional = 0.8;
                }

                // Accessibility analysis (simulated for cross-origin)
                const violations = [];
                let accessibilityScore = 85; // Default assumption

                // Security analysis
                const isHttps = url.startsWith('https://');
                const securityLevel = isHttps ? 'LOW' : 'MEDIUM';
                const threats = isHttps ? 0 : 1;

                if (!isHttps) {
                    violations.push('Insecure HTTP connection detected');
                    accessibilityScore -= 10;
                }

                // Simulate some violations for demo
                if (url.includes('example.com')) {
                    violations.push('Missing alt text on logo image');
                    violations.push('Poor color contrast in header');
                    accessibilityScore = 60;
                }

                return {
                    structural: Math.min(1, structural),
                    functional: Math.min(1, functional),
                    accessibility: {
                        score: accessibilityScore,
                        violations: violations
                    },
                    security: {
                        level: securityLevel,
                        threats: threats
                    }
                };
            }
            
            updateAnalysisResults(data) {
                // Update status bar
                document.getElementById('status-coherence').textContent = `${data.coherence.overall}%`;
                document.getElementById('status-accessibility').textContent = `${data.accessibility.score}%`;
                document.getElementById('status-threats').textContent = data.security.level;
                document.getElementById('status-speed').textContent = `${data.performance.analysisTime}ms`;
                
                // Update sidebar
                document.getElementById('coherence-score').textContent = `${data.coherence.overall}%`;
                document.getElementById('accessibility-score').textContent = `${data.accessibility.score}%`;
                document.getElementById('threat-level').textContent = data.security.level;
                
                // Update Ψ-Snap status with modern styling
                const psiSnap = data.coherence.overall >= 82;
                const psiSnapElement = document.getElementById('psi-snap-status');
                psiSnapElement.textContent = psiSnap ? 'ACTIVE' : 'INACTIVE';
                psiSnapElement.style.color = psiSnap ? 'var(--coherence-high)' : 'var(--coherence-medium)';

                // Update overlay with enhanced styling
                document.getElementById('overlay-structural').textContent = `${data.coherence.structural}%`;
                document.getElementById('overlay-functional').textContent = `${data.coherence.functional}%`;
                document.getElementById('overlay-relational').textContent = `${data.coherence.relational}%`;

                const overlayPsiSnap = document.getElementById('overlay-psi-snap');
                overlayPsiSnap.textContent = `Ψ-Snap: ${psiSnap ? 'ACTIVE' : 'INACTIVE'}`;
                overlayPsiSnap.className = psiSnap ? 'psi-snap' : 'psi-snap inactive';

                // Update coherence indicator with enhanced visual feedback
                const indicator = document.getElementById('coherence-indicator');
                const overall = data.coherence.overall;
                const relational = data.coherence.relational;

                if (overall >= 82) {
                    indicator.className = 'coherence-indicator coherence-high';
                    indicator.title = `✅ Ψ-Snap ACTIVE (${overall}%) - Divine Coherence Achieved`;
                } else if (overall >= 60) {
                    indicator.className = 'coherence-indicator coherence-medium';
                    indicator.title = `⚠️ Below Ψ-Snap threshold (${overall}% < 82%) - Approaching Coherence`;
                } else {
                    indicator.className = 'coherence-indicator coherence-low';
                    indicator.title = `❌ Low coherence detected (${overall}%) - Coherence Restoration Needed`;
                }

                // Alert for low relational coherence
                if (relational < 85) {
                    this.showCoherenceAlert(`⚠️ Relational coherence below 85% (${relational}%)`);
                }
                
                // Update violations
                this.updateViolationsList(data.accessibility.violations);
            }
            
            updateViolationsList(violations) {
                const list = document.getElementById('violations-list');
                list.innerHTML = '';
                
                if (violations.length === 0) {
                    list.innerHTML = '<div style="color: #00ff96; text-align: center; padding: 20px;">✅ No violations detected</div>';
                    return;
                }
                
                violations.forEach(violation => {
                    const item = document.createElement('div');
                    item.className = 'violation-item';
                    item.innerHTML = `
                        <div>${violation}</div>
                        <button class="auto-fix-btn" onclick="this.parentElement.style.display='none'">Auto-Fix</button>
                    `;
                    list.appendChild(item);
                });
            }
            
            refreshPage() {
                if (this.currentUrl) {
                    this.loadPage(this.currentUrl);
                }
            }

            showCoherenceAlert(message) {
                // Create modern alert notification
                const alert = document.createElement('div');
                alert.style.cssText = `
                    position: fixed;
                    top: 100px;
                    right: 24px;
                    background: var(--bg-glass);
                    backdrop-filter: blur(20px);
                    border: 1px solid var(--coherence-medium);
                    color: var(--text-primary);
                    padding: 16px 20px;
                    border-radius: 12px;
                    font-weight: 600;
                    font-size: 14px;
                    z-index: 10000;
                    box-shadow: var(--shadow-xl);
                    animation: slideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                    max-width: 320px;
                    min-width: 280px;
                `;

                alert.innerHTML = `
                    <div style="display: flex; align-items: center; gap: 12px;">
                        <div style="width: 8px; height: 8px; background: var(--coherence-medium); border-radius: 50%; box-shadow: 0 0 8px var(--coherence-medium);"></div>
                        <div>${message}</div>
                    </div>
                `;

                document.body.appendChild(alert);

                // Auto-remove after 6 seconds with enhanced animation
                setTimeout(() => {
                    if (alert.parentNode) {
                        alert.style.animation = 'slideOut 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
                        setTimeout(() => alert.remove(), 400);
                    }
                }, 6000);
            }
        }
        
        // Initialize NovaBrowser UI
        const novaBrowser = new NovaBrowserUI();
    </script>
</body>
</html>
