const axios = require('axios');
const cluster = require('cluster');
const numCPUs = require('os').cpus().length;

class KetherNetExtremeStress {
  constructor(baseUrl = 'http://localhost:8080') {
    this.baseUrl = baseUrl;
    this.results = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      responseTimes: [],
      errors: [],
      startTime: null,
      endTime: null
    };
  }

  // EXTREME LOAD TEST - THOUSANDS OF CONCURRENT REQUESTS
  async extremeLoadTest(totalRequests = 10000, concurrency = 500) {
    console.log(`🔥 EXTREME LOAD TEST: ${totalRequests} requests, ${concurrency} concurrent`);
    console.log('🚨 WARNING: This will attempt to overwhelm the server!');
    
    this.results.startTime = Date.now();
    
    const chunks = [];
    for (let i = 0; i < totalRequests; i += concurrency) {
      chunks.push(Math.min(concurrency, totalRequests - i));
    }

    for (const chunkSize of chunks) {
      const promises = [];
      
      for (let i = 0; i < chunkSize; i++) {
        const promise = this.makeRandomRequest();
        promises.push(promise);
      }
      
      await Promise.allSettled(promises);
      
      // Show progress
      process.stdout.write(`\r💥 Progress: ${this.results.totalRequests}/${totalRequests} requests`);
    }
    
    this.results.endTime = Date.now();
    console.log('\n');
  }

  async makeRandomRequest() {
    const requestTypes = [
      () => this.makeHealthRequest(),
      () => this.makeStatsRequest(),
      () => this.makeConsciousnessRequest(),
      () => this.makeTransactionRequest(),
      () => this.makeBlockMiningRequest()
    ];
    
    const randomRequest = requestTypes[Math.floor(Math.random() * requestTypes.length)];
    return await randomRequest();
  }

  async makeHealthRequest() {
    return await this.timedRequest('GET', '/health');
  }

  async makeStatsRequest() {
    return await this.timedRequest('GET', '/stats');
  }

  async makeConsciousnessRequest() {
    const data = {
      neural: Math.floor(Math.random() * 20) + 5,
      information: Math.floor(Math.random() * 25) + 10,
      coherence: Math.floor(Math.random() * 30) + 15
    };
    return await this.timedRequest('POST', '/consciousness/validate', data);
  }

  async makeTransactionRequest() {
    const data = {
      from: "0x" + Math.random().toString(16).substr(2, 8),
      to: "0x" + Math.random().toString(16).substr(2, 8),
      value: Math.floor(Math.random() * 1000),
      consciousness_proof: 2847 + Math.floor(Math.random() * 1000)
    };
    return await this.timedRequest('POST', '/transactions', data);
  }

  async makeBlockMiningRequest() {
    const data = {
      miner: "0x" + Math.random().toString(16).substr(2, 8),
      consciousness_proof: 2847 + Math.floor(Math.random() * 1000)
    };
    return await this.timedRequest('POST', '/blocks/mine', data);
  }

  async timedRequest(method, endpoint, data = null) {
    const startTime = Date.now();
    
    try {
      let response;
      if (method === 'POST') {
        response = await axios.post(`${this.baseUrl}${endpoint}`, data, {
          timeout: 5000 // 5 second timeout
        });
      } else {
        response = await axios.get(`${this.baseUrl}${endpoint}`, {
          timeout: 5000
        });
      }
      
      const responseTime = Date.now() - startTime;
      this.results.responseTimes.push(responseTime);
      this.results.successfulRequests++;
      this.results.totalRequests++;
      
      return { success: true, responseTime, status: response.status };
    } catch (error) {
      const responseTime = Date.now() - startTime;
      this.results.failedRequests++;
      this.results.totalRequests++;
      this.results.errors.push({
        endpoint,
        error: error.message,
        responseTime,
        code: error.code
      });
      
      return { success: false, responseTime, error: error.message };
    }
  }

  // MEMORY BOMB ATTACK
  async memoryBombAttack() {
    console.log('💣 MEMORY BOMB ATTACK: Sending massive payloads...');
    
    const sizes = [1, 5, 10, 50, 100]; // MB sizes
    
    for (const sizeMB of sizes) {
      console.log(`💣 Sending ${sizeMB}MB payload...`);
      
      const payload = {
        neural: 15,
        information: 20,
        coherence: 25,
        bomb: "A".repeat(sizeMB * 1024 * 1024) // sizeMB of data
      };
      
      try {
        const startTime = Date.now();
        const response = await axios.post(`${this.baseUrl}/consciousness/validate`, payload, {
          timeout: 30000 // 30 second timeout
        });
        const responseTime = Date.now() - startTime;
        
        console.log(`💥 ${sizeMB}MB payload accepted! Response time: ${responseTime}ms`);
        
        if (responseTime > 10000) {
          console.log('🚨 Server struggling with large payload!');
        }
        
      } catch (error) {
        console.log(`✅ ${sizeMB}MB payload rejected: ${error.message}`);
      }
    }
  }

  // RAPID FIRE ATTACK
  async rapidFireAttack(duration = 30000) {
    console.log(`🔫 RAPID FIRE ATTACK: Maximum RPS for ${duration/1000} seconds`);
    
    const startTime = Date.now();
    let requestCount = 0;
    
    while (Date.now() - startTime < duration) {
      const promises = [];
      
      // Fire 100 requests simultaneously
      for (let i = 0; i < 100; i++) {
        promises.push(
          axios.get(`${this.baseUrl}/health`, { timeout: 1000 })
            .catch(() => null)
        );
      }
      
      await Promise.allSettled(promises);
      requestCount += 100;
      
      // Show RPS
      const elapsed = (Date.now() - startTime) / 1000;
      const rps = requestCount / elapsed;
      process.stdout.write(`\r🔫 RPS: ${rps.toFixed(0)} | Total: ${requestCount}`);
    }
    
    console.log('\n');
    
    // Check if server is still alive
    try {
      await axios.get(`${this.baseUrl}/health`);
      console.log('✅ Server survived rapid fire attack!');
    } catch (error) {
      console.log('💥 Server crashed during rapid fire attack!');
    }
  }

  // CONSCIOUSNESS OVERFLOW ATTACK
  async consciousnessOverflowAttack() {
    console.log('🧠 CONSCIOUSNESS OVERFLOW ATTACK: Testing mathematical limits...');
    
    const extremeValues = [
      { neural: Number.MAX_SAFE_INTEGER, information: Number.MAX_SAFE_INTEGER, coherence: Number.MAX_SAFE_INTEGER },
      { neural: Number.MAX_VALUE, information: Number.MAX_VALUE, coherence: Number.MAX_VALUE },
      { neural: Infinity, information: Infinity, coherence: Infinity },
      { neural: -Infinity, information: -Infinity, coherence: -Infinity },
      { neural: NaN, information: NaN, coherence: NaN }
    ];
    
    for (const values of extremeValues) {
      try {
        const response = await axios.post(`${this.baseUrl}/consciousness/validate`, values);
        console.log(`💥 OVERFLOW ACCEPTED: ${JSON.stringify(values)} -> UUFT: ${response.data.uuftScore}`);
      } catch (error) {
        console.log(`✅ Overflow rejected: ${JSON.stringify(values)}`);
      }
    }
  }

  printResults() {
    const totalTime = (this.results.endTime - this.results.startTime) / 1000;
    const rps = this.results.totalRequests / totalTime;
    const successRate = (this.results.successfulRequests / this.results.totalRequests) * 100;
    
    const responseTimes = this.results.responseTimes;
    const avgResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
    const sortedTimes = responseTimes.sort((a, b) => a - b);
    
    console.log('\n💥 EXTREME STRESS TEST RESULTS');
    console.log('===============================');
    console.log(`Total Requests: ${this.results.totalRequests}`);
    console.log(`Successful: ${this.results.successfulRequests}`);
    console.log(`Failed: ${this.results.failedRequests}`);
    console.log(`Success Rate: ${successRate.toFixed(2)}%`);
    console.log(`Total Time: ${totalTime.toFixed(2)}s`);
    console.log(`Requests/Second: ${rps.toFixed(0)} RPS`);
    
    if (responseTimes.length > 0) {
      console.log('\n⏱️  RESPONSE TIMES:');
      console.log(`   Average: ${avgResponseTime.toFixed(2)}ms`);
      console.log(`   Min: ${Math.min(...responseTimes)}ms`);
      console.log(`   Max: ${Math.max(...responseTimes)}ms`);
      console.log(`   P50: ${sortedTimes[Math.floor(sortedTimes.length * 0.5)]}ms`);
      console.log(`   P95: ${sortedTimes[Math.floor(sortedTimes.length * 0.95)]}ms`);
      console.log(`   P99: ${sortedTimes[Math.floor(sortedTimes.length * 0.99)]}ms`);
    }
    
    if (this.results.errors.length > 0) {
      console.log('\n❌ ERROR BREAKDOWN:');
      const errorCounts = {};
      this.results.errors.forEach(error => {
        const key = error.code || error.error;
        errorCounts[key] = (errorCounts[key] || 0) + 1;
      });
      
      Object.entries(errorCounts).forEach(([error, count]) => {
        console.log(`   ${error}: ${count} occurrences`);
      });
    }
    
    // Verdict
    if (successRate > 95 && rps > 100) {
      console.log('\n🛡️  VERDICT: KETHERNET IS EXTREMELY ROBUST!');
    } else if (successRate > 80) {
      console.log('\n⚠️  VERDICT: KETHERNET SHOWS SOME STRESS UNDER EXTREME LOAD');
    } else {
      console.log('\n💥 VERDICT: KETHERNET VULNERABLE TO EXTREME STRESS');
    }
  }

  async runExtremeStressTest() {
    console.log('💥 STARTING EXTREME KETHERNET STRESS TEST');
    console.log('🚨 WARNING: This will attempt to break the server!');
    console.log('==========================================\n');

    // Run all extreme tests
    await this.extremeLoadTest(5000, 200);
    await this.memoryBombAttack();
    await this.rapidFireAttack(15000);
    await this.consciousnessOverflowAttack();

    this.printResults();
  }
}

// CLI interface
if (require.main === module) {
  const stressTester = new KetherNetExtremeStress();
  
  const testType = process.argv[2] || 'full';
  const requests = process.argv[3] ? parseInt(process.argv[3]) : 5000;
  const concurrency = process.argv[4] ? parseInt(process.argv[4]) : 200;
  
  if (testType === 'load') {
    stressTester.extremeLoadTest(requests, concurrency).then(() => {
      stressTester.printResults();
    });
  } else {
    stressTester.runExtremeStressTest();
  }
}

module.exports = KetherNetExtremeStress;
