/**
 * Trinity CSFE Engine Test
 * 
 * This script tests the Trinity CSFE engine with sample data.
 */

const { TrinityCSFEEngine, TrinityCSFE1882Engine } = require('./index');

// Create Trinity CSFE Engine instances
const trinityCSFEEngine = new TrinityCSFEEngine();
const trinityCSFE1882Engine = new TrinityCSFE1882Engine();

// Sample governance data
const governanceData = {
  policyDesign: {
    clarity: 0.85,
    coverage: 0.9,
    effectiveness: 0.8
  },
  complianceEnforcement: {
    monitoring: 0.9,
    enforcement: 0.85,
    reporting: 0.95
  },
  riskManagement: {
    identification: 0.8,
    assessment: 0.85,
    mitigation: 0.9
  }
};

// Sample detection data
const detectionData = {
  baselineSignals: {
    marketMetrics: 0.8,
    economicIndicators: 0.9,
    sentimentMeasures: 0.75
  },
  threatWeight: {
    marketRisk: 0.7,
    creditRisk: 0.6,
    liquidityRisk: 0.8
  },
  anomalyDetection: {
    sensitivity: 0.85,
    specificity: 0.9,
    accuracy: 0.88
  }
};

// Sample response data
const responseData = {
  reactionTime: {
    detection: 0.9,
    decision: 0.85,
    execution: 0.8
  },
  mitigationSurface: {
    coverage: 0.85,
    effectiveness: 0.8,
    efficiency: 0.9
  },
  adaptiveResponse: {
    learning: 0.8,
    adaptation: 0.85,
    resilience: 0.9
  }
};

// Test Trinity CSFE Engine
console.log('Testing Trinity CSFE Engine...');
const trinityResult = trinityCSFEEngine.calculate(governanceData, detectionData, responseData);

// Display Trinity CSFE result
console.log('\nTrinity CSFE Result:');
console.log(`CSFE Trinity Value: ${trinityResult.csfeTrinity}`);
console.log(`Performance Factor: ${trinityResult.performanceFactor}x`);
console.log(`Calculated At: ${trinityResult.timestamp}`);

// Display Father component
console.log('\nFather Component (Governance):');
console.log(`Policy Design Score: ${trinityResult.fatherComponent.policyDesignScore}`);
console.log(`Compliance Enforcement Score: ${trinityResult.fatherComponent.complianceEnforcementScore}`);
console.log(`Pi-Aligned Score: ${trinityResult.fatherComponent.piAlignedScore}`);
console.log(`Result: ${trinityResult.fatherComponent.result}`);

// Display Son component
console.log('\nSon Component (Detection):');
console.log(`Baseline Signals Score: ${trinityResult.sonComponent.baselineSignalsScore}`);
console.log(`Threat Weight Score: ${trinityResult.sonComponent.threatWeightScore}`);
console.log(`Phi-Weighted Score: ${trinityResult.sonComponent.phiWeightedScore}`);
console.log(`Result: ${trinityResult.sonComponent.result}`);

// Display Spirit component
console.log('\nSpirit Component (Response):');
console.log(`Reaction Time Score: ${trinityResult.spiritComponent.reactionTimeScore}`);
console.log(`Mitigation Surface Score: ${trinityResult.spiritComponent.mitigationSurfaceScore}`);
console.log(`Entropy Restraint: ${trinityResult.spiritComponent.entropyRestraint}`);
console.log(`Speed Limit: ${trinityResult.spiritComponent.speedLimit}`);
console.log(`Spirit Factor: ${trinityResult.spiritComponent.spiritFactor}`);
console.log(`Result: ${trinityResult.spiritComponent.result}`);

// Test Trinity CSFE 18/82 Engine
console.log('\n\nTesting Trinity CSFE 18/82 Engine...');
const trinity1882Result = trinityCSFE1882Engine.calculate(governanceData, detectionData, responseData);

// Display Trinity CSFE 18/82 result
console.log('\nTrinity CSFE 18/82 Result:');
console.log(`CSFE Trinity Value: ${trinity1882Result.csfeTrinity}`);
console.log(`Performance Factor: ${trinity1882Result.performanceFactor}x`);
console.log(`Calculated At: ${trinity1882Result.timestamp}`);

// Display Father component with 18/82 principle
console.log('\nFather Component (Governance) with 18/82 Principle:');
console.log(`Policy Design Score (18%): ${trinity1882Result.fatherComponent.policyDesignScore}`);
console.log(`Compliance Enforcement Score (82%): ${trinity1882Result.fatherComponent.complianceEnforcementScore}`);
console.log(`Principle Ratio: ${trinity1882Result.fatherComponent.principleRatio}`);
console.log(`Principle Score: ${trinity1882Result.fatherComponent.principleScore}`);
console.log(`Pi-Aligned Score: ${trinity1882Result.fatherComponent.piAlignedScore}`);
console.log(`Result: ${trinity1882Result.fatherComponent.result}`);

// Display Son component with 18/82 principle
console.log('\nSon Component (Detection) with 18/82 Principle:');
console.log(`Baseline Signals Score (18%): ${trinity1882Result.sonComponent.baselineSignalsScore}`);
console.log(`Threat Weight Score (82%): ${trinity1882Result.sonComponent.threatWeightScore}`);
console.log(`Principle Ratio: ${trinity1882Result.sonComponent.principleRatio}`);
console.log(`Principle Score: ${trinity1882Result.sonComponent.principleScore}`);
console.log(`Phi-Weighted Score: ${trinity1882Result.sonComponent.phiWeightedScore}`);
console.log(`Result: ${trinity1882Result.sonComponent.result}`);

// Display Spirit component with 18/82 principle
console.log('\nSpirit Component (Response) with 18/82 Principle:');
console.log(`Reaction Time Score (18%): ${trinity1882Result.spiritComponent.reactionTimeScore}`);
console.log(`Mitigation Surface Score (82%): ${trinity1882Result.spiritComponent.mitigationSurfaceScore}`);
console.log(`Principle Ratio: ${trinity1882Result.spiritComponent.principleRatio}`);
console.log(`Principle Score: ${trinity1882Result.spiritComponent.principleScore}`);
console.log(`Entropy Restraint: ${trinity1882Result.spiritComponent.entropyRestraint}`);
console.log(`Speed Limit: ${trinity1882Result.spiritComponent.speedLimit}`);
console.log(`Spirit Factor: ${trinity1882Result.spiritComponent.spiritFactor}`);
console.log(`Result: ${trinity1882Result.spiritComponent.result}`);

// Compare results
console.log('\n\nComparison:');
console.log(`Standard Trinity CSFE Value: ${trinityResult.csfeTrinity}`);
console.log(`18/82 Trinity CSFE Value: ${trinity1882Result.csfeTrinity}`);
console.log(`Difference: ${trinity1882Result.csfeTrinity - trinityResult.csfeTrinity}`);
console.log(`Percentage Difference: ${((trinity1882Result.csfeTrinity / trinityResult.csfeTrinity) - 1) * 100}%`);

console.log('\nTest completed successfully.');
