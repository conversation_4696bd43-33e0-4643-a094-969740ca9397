/**
 * Performance Optimizer for Cyber-Safety Visualizations
 * 
 * This utility provides functions to optimize the performance of visualizations,
 * especially when dealing with large datasets.
 */

/**
 * Optimize tensor data for visualization
 * @param {Object} tensorData - The original tensor data
 * @param {Object} options - Optimization options
 * @returns {Object} - Optimized tensor data
 */
export const optimizeTensorData = (tensorData, options = {}) => {
  const {
    maxPoints = 1000,
    preserveStructure = true,
    samplingStrategy = 'adaptive'
  } = options;
  
  if (!tensorData) return null;
  
  // Clone the data to avoid modifying the original
  const optimized = JSON.parse(JSON.stringify(tensorData));
  
  // Optimize values arrays in each domain
  if (optimized.grc?.values && optimized.grc.values.length > maxPoints) {
    optimized.grc.values = sampleArray(optimized.grc.values, maxPoints, samplingStrategy);
  }
  
  if (optimized.it?.values && optimized.it.values.length > maxPoints) {
    optimized.it.values = sampleArray(optimized.it.values, maxPoints, samplingStrategy);
  }
  
  if (optimized.cybersecurity?.values && optimized.cybersecurity.values.length > maxPoints) {
    optimized.cybersecurity.values = sampleArray(optimized.cybersecurity.values, maxPoints, samplingStrategy);
  }
  
  // Optimize connections if there are too many
  if (optimized.connections && optimized.connections.length > maxPoints / 10) {
    optimized.connections = sampleArray(optimized.connections, maxPoints / 10, samplingStrategy);
  }
  
  return optimized;
};

/**
 * Optimize harmony index data for visualization
 * @param {Object} harmonyData - The original harmony data
 * @param {Object} options - Optimization options
 * @returns {Object} - Optimized harmony data
 */
export const optimizeHarmonyData = (harmonyData, options = {}) => {
  const {
    maxHistoryPoints = 100
  } = options;
  
  if (!harmonyData) return null;
  
  // Clone the data to avoid modifying the original
  const optimized = JSON.parse(JSON.stringify(harmonyData));
  
  // Optimize harmony history if it's too long
  if (optimized.harmonyHistory && optimized.harmonyHistory.length > maxHistoryPoints) {
    optimized.harmonyHistory = sampleArray(optimized.harmonyHistory, maxHistoryPoints, 'preserve-endpoints');
  }
  
  return optimized;
};

/**
 * Optimize compliance data for visualization
 * @param {Object} complianceData - The original compliance data
 * @param {Object} options - Optimization options
 * @returns {Object} - Optimized compliance data
 */
export const optimizeComplianceData = (complianceData, options = {}) => {
  const {
    maxRequirements = 20,
    maxControls = 30,
    maxImplementations = 30,
    maxLinks = 50
  } = options;
  
  if (!complianceData) return null;
  
  // Clone the data to avoid modifying the original
  const optimized = JSON.parse(JSON.stringify(complianceData));
  
  // Optimize requirements if there are too many
  if (optimized.complianceData?.requirements && optimized.complianceData.requirements.length > maxRequirements) {
    optimized.complianceData.requirements = sampleArray(
      optimized.complianceData.requirements, 
      maxRequirements, 
      'importance'
    );
  }
  
  // Optimize controls if there are too many
  if (optimized.complianceData?.controls && optimized.complianceData.controls.length > maxControls) {
    optimized.complianceData.controls = sampleArray(
      optimized.complianceData.controls, 
      maxControls, 
      'importance'
    );
  }
  
  // Optimize implementations if there are too many
  if (optimized.complianceData?.implementations && optimized.complianceData.implementations.length > maxImplementations) {
    optimized.complianceData.implementations = sampleArray(
      optimized.complianceData.implementations, 
      maxImplementations, 
      'importance'
    );
  }
  
  // Optimize links if there are too many
  if (optimized.complianceData?.links && optimized.complianceData.links.length > maxLinks) {
    // First, ensure we keep links for all requirements, controls, and implementations that we kept
    const keptRequirementIds = new Set(optimized.complianceData.requirements.map(r => r.id));
    const keptControlIds = new Set(optimized.complianceData.controls.map(c => c.id));
    const keptImplementationIds = new Set(optimized.complianceData.implementations.map(i => i.id));
    
    // Filter links to only include those that connect kept nodes
    optimized.complianceData.links = optimized.complianceData.links.filter(link => {
      const isRequirementToControl = keptRequirementIds.has(link.source) && keptControlIds.has(link.target);
      const isControlToImplementation = keptControlIds.has(link.source) && keptImplementationIds.has(link.target);
      return isRequirementToControl || isControlToImplementation;
    });
    
    // If we still have too many links, sample them based on strength
    if (optimized.complianceData.links.length > maxLinks) {
      optimized.complianceData.links = sampleArray(
        optimized.complianceData.links, 
        maxLinks, 
        'strength'
      );
    }
  }
  
  return optimized;
};

/**
 * Sample an array to reduce its size
 * @param {Array} array - The original array
 * @param {number} maxSize - The maximum size of the sampled array
 * @param {string} strategy - The sampling strategy
 * @returns {Array} - The sampled array
 */
export const sampleArray = (array, maxSize, strategy = 'uniform') => {
  if (!array || array.length <= maxSize) return array;
  
  switch (strategy) {
    case 'uniform': {
      // Uniform sampling (take every nth element)
      const step = Math.ceil(array.length / maxSize);
      return array.filter((_, index) => index % step === 0).slice(0, maxSize);
    }
    
    case 'random': {
      // Random sampling
      const sampled = [];
      const indices = new Set();
      
      while (indices.size < maxSize) {
        const index = Math.floor(Math.random() * array.length);
        if (!indices.has(index)) {
          indices.add(index);
          sampled.push(array[index]);
        }
      }
      
      return sampled;
    }
    
    case 'preserve-endpoints': {
      // Preserve the first and last elements, sample the rest
      if (maxSize < 2) return array.slice(0, maxSize);
      
      const first = array[0];
      const last = array[array.length - 1];
      const middle = array.slice(1, array.length - 1);
      
      const sampledMiddle = sampleArray(middle, maxSize - 2, 'uniform');
      
      return [first, ...sampledMiddle, last];
    }
    
    case 'adaptive': {
      // Adaptive sampling (preserve areas with high variance)
      if (maxSize < 2) return array.slice(0, maxSize);
      
      // Calculate variance between adjacent points
      const variances = [];
      for (let i = 1; i < array.length; i++) {
        const prev = array[i - 1];
        const curr = array[i];
        
        // Handle different data types
        if (typeof curr === 'number' && typeof prev === 'number') {
          variances.push(Math.abs(curr - prev));
        } else if (typeof curr === 'object' && typeof prev === 'object') {
          // For objects, use a simple hash function
          const currHash = JSON.stringify(curr);
          const prevHash = JSON.stringify(prev);
          variances.push(currHash === prevHash ? 0 : 1);
        } else {
          variances.push(curr === prev ? 0 : 1);
        }
      }
      
      // Create pairs of [index, variance]
      const indexedVariances = variances.map((v, i) => [i + 1, v]);
      
      // Sort by variance (descending)
      indexedVariances.sort((a, b) => b[1] - a[1]);
      
      // Take the indices with highest variance
      const highVarianceIndices = new Set(
        indexedVariances.slice(0, maxSize - 2).map(pair => pair[0])
      );
      
      // Always include first and last points
      highVarianceIndices.add(0);
      highVarianceIndices.add(array.length - 1);
      
      // Create the sampled array
      return array.filter((_, index) => highVarianceIndices.has(index));
    }
    
    case 'importance': {
      // Sample based on an importance field (e.g., completeness, strength)
      // This assumes array elements are objects with an importance-related field
      
      // Try to find an importance field
      const importanceFields = ['importance', 'strength', 'completeness', 'health', 'score', 'weight'];
      let importanceField = null;
      
      for (const field of importanceFields) {
        if (array[0] && field in array[0]) {
          importanceField = field;
          break;
        }
      }
      
      if (importanceField) {
        // Sort by importance (descending)
        const sorted = [...array].sort((a, b) => b[importanceField] - a[importanceField]);
        return sorted.slice(0, maxSize);
      }
      
      // Fall back to uniform sampling if no importance field is found
      return sampleArray(array, maxSize, 'uniform');
    }
    
    case 'strength': {
      // Specifically for links, sample based on strength
      if (array[0] && 'strength' in array[0]) {
        // Sort by strength (descending)
        const sorted = [...array].sort((a, b) => b.strength - a.strength);
        return sorted.slice(0, maxSize);
      }
      
      // Fall back to uniform sampling if no strength field is found
      return sampleArray(array, maxSize, 'uniform');
    }
    
    default:
      return array.slice(0, maxSize);
  }
};

/**
 * Determine the appropriate level of detail based on device capabilities
 * @returns {string} - The appropriate level of detail ('low', 'medium', 'high')
 */
export const determineDetailLevel = () => {
  // Check if we're in a browser environment
  if (typeof window === 'undefined') return 'medium';
  
  // Check for WebGL support
  let webglSupported = false;
  try {
    const canvas = document.createElement('canvas');
    webglSupported = !!(
      window.WebGLRenderingContext &&
      (canvas.getContext('webgl') || canvas.getContext('experimental-webgl'))
    );
  } catch (e) {
    webglSupported = false;
  }
  
  // Check device memory (if available)
  const deviceMemory = navigator.deviceMemory || 4; // Default to 4GB if not available
  
  // Check device pixel ratio (proxy for screen density)
  const devicePixelRatio = window.devicePixelRatio || 1;
  
  // Check if device is mobile
  const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
    navigator.userAgent
  );
  
  // Determine detail level based on device capabilities
  if (!webglSupported || deviceMemory <= 2 || (isMobile && devicePixelRatio >= 2)) {
    return 'low';
  } else if (deviceMemory >= 8 && !isMobile && webglSupported) {
    return 'high';
  } else {
    return 'medium';
  }
};

/**
 * Get optimization options based on detail level
 * @param {string} detailLevel - The detail level ('low', 'medium', 'high')
 * @returns {Object} - Optimization options
 */
export const getOptimizationOptions = (detailLevel = 'medium') => {
  switch (detailLevel) {
    case 'low':
      return {
        maxPoints: 500,
        maxHistoryPoints: 50,
        maxRequirements: 10,
        maxControls: 15,
        maxImplementations: 15,
        maxLinks: 25,
        samplingStrategy: 'importance'
      };
      
    case 'medium':
      return {
        maxPoints: 1000,
        maxHistoryPoints: 100,
        maxRequirements: 20,
        maxControls: 30,
        maxImplementations: 30,
        maxLinks: 50,
        samplingStrategy: 'adaptive'
      };
      
    case 'high':
      return {
        maxPoints: 2000,
        maxHistoryPoints: 200,
        maxRequirements: 30,
        maxControls: 50,
        maxImplementations: 50,
        maxLinks: 100,
        samplingStrategy: 'adaptive'
      };
      
    default:
      return getOptimizationOptions('medium');
  }
};
