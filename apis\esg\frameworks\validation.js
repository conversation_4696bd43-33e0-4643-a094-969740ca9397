const Joi = require('joi');

// Validation schemas
const schemas = {
  // Framework validation schemas
  createFramework: Joi.object({
    code: Joi.string().required().min(1).max(20),
    name: Joi.string().required().min(3).max(100),
    description: Joi.string().required().max(500),
    version: Joi.string().required().max(20),
    organization: Joi.string().required().max(100),
    website: Joi.string().optional().uri().max(200),
    category: Joi.string().required().valid('environmental', 'social', 'governance', 'general', 'industry-specific'),
    industry: Joi.string().optional().allow(null, '').max(100),
    region: Joi.string().optional().allow(null, '').max(100),
    isActive: Joi.boolean().optional().default(true),
    effectiveDate: Joi.string().optional().pattern(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
    expirationDate: Joi.string().optional().pattern(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
    regulatoryStatus: Joi.string().optional().valid('mandatory', 'voluntary', 'recommended'),
    applicableRegulations: Joi.array().items(Joi.string()).optional(),
    maturityLevel: Joi.string().optional().valid('emerging', 'established', 'mature', 'legacy'),
    adoptionRate: Joi.string().optional().max(20),
    implementationCost: Joi.string().optional().valid('low', 'medium', 'high'),
    implementationComplexity: Joi.string().optional().valid('low', 'medium', 'high'),
    updateFrequency: Joi.string().optional().max(50),
    lastUpdatedBy: Joi.string().optional().max(100),
    tags: Joi.array().items(Joi.string().max(30)).optional()
  }),

  updateFramework: Joi.object({
    code: Joi.string().optional().min(1).max(20),
    name: Joi.string().optional().min(3).max(100),
    description: Joi.string().optional().max(500),
    version: Joi.string().optional().max(20),
    organization: Joi.string().optional().max(100),
    website: Joi.string().optional().uri().max(200),
    category: Joi.string().optional().valid('environmental', 'social', 'governance', 'general', 'industry-specific'),
    industry: Joi.string().optional().allow(null, '').max(100),
    region: Joi.string().optional().allow(null, '').max(100),
    isActive: Joi.boolean().optional(),
    effectiveDate: Joi.string().optional().pattern(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
    expirationDate: Joi.string().optional().pattern(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
    regulatoryStatus: Joi.string().optional().valid('mandatory', 'voluntary', 'recommended'),
    applicableRegulations: Joi.array().items(Joi.string()).optional(),
    maturityLevel: Joi.string().optional().valid('emerging', 'established', 'mature', 'legacy'),
    adoptionRate: Joi.string().optional().max(20),
    implementationCost: Joi.string().optional().valid('low', 'medium', 'high'),
    implementationComplexity: Joi.string().optional().valid('low', 'medium', 'high'),
    updateFrequency: Joi.string().optional().max(50),
    lastUpdatedBy: Joi.string().optional().max(100),
    tags: Joi.array().items(Joi.string().max(30)).optional()
  }).min(1), // At least one field must be provided

  // Framework element validation schemas
  createElement: Joi.object({
    code: Joi.string().required().min(1).max(50),
    name: Joi.string().required().min(3).max(100),
    description: Joi.string().required().max(500),
    category: Joi.string().optional().max(100),
    parentId: Joi.string().optional().allow(null, ''),
    level: Joi.number().integer().required().min(1),
    metrics: Joi.array().items(Joi.string()).optional()
  }),

  updateElement: Joi.object({
    code: Joi.string().optional().min(1).max(50),
    name: Joi.string().optional().min(3).max(100),
    description: Joi.string().optional().max(500),
    category: Joi.string().optional().max(100),
    parentId: Joi.string().optional().allow(null, ''),
    level: Joi.number().integer().optional().min(1),
    metrics: Joi.array().items(Joi.string()).optional()
  }).min(1), // At least one field must be provided

  // Framework mapping validation schemas
  createMapping: Joi.object({
    sourceFrameworkId: Joi.string().required(),
    sourceElementId: Joi.string().required(),
    targetFrameworkId: Joi.string().required(),
    targetElementId: Joi.string().required(),
    mappingType: Joi.string().required().valid('exact', 'partial', 'related', 'superset', 'subset'),
    confidenceLevel: Joi.string().optional().valid('low', 'medium', 'high'),
    mappingMethod: Joi.string().optional().valid('manual', 'automated', 'ai-assisted', 'expert-reviewed'),
    mappingVersion: Joi.string().optional().max(20),
    validFrom: Joi.string().optional().pattern(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
    validTo: Joi.string().optional().pattern(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
    verifiedBy: Joi.string().optional().max(100),
    verifiedAt: Joi.string().optional().pattern(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{1,3})?Z$/), // ISO 8601 format
    notes: Joi.string().optional().max(500)
  }),

  updateMapping: Joi.object({
    mappingType: Joi.string().optional().valid('exact', 'partial', 'related', 'superset', 'subset'),
    confidenceLevel: Joi.string().optional().valid('low', 'medium', 'high'),
    mappingMethod: Joi.string().optional().valid('manual', 'automated', 'ai-assisted', 'expert-reviewed'),
    mappingVersion: Joi.string().optional().max(20),
    validFrom: Joi.string().optional().pattern(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
    validTo: Joi.string().optional().pattern(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
    verifiedBy: Joi.string().optional().max(100),
    verifiedAt: Joi.string().optional().pattern(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{1,3})?Z$/), // ISO 8601 format
    notes: Joi.string().optional().max(500)
  }).min(1) // At least one field must be provided
};

/**
 * Middleware to validate request data against a schema
 * @param {string} schemaName - Name of the schema to validate against
 * @returns {Function} Express middleware function
 */
const validateRequest = (schemaName) => {
  return (req, res, next) => {
    const schema = schemas[schemaName];

    if (!schema) {
      return res.status(500).json({
        error: 'Internal Server Error',
        message: `Validation schema '${schemaName}' not found`
      });
    }

    const { error, value } = schema.validate(req.body, {
      abortEarly: false, // Return all errors, not just the first one
      stripUnknown: true // Remove unknown fields
    });

    if (error) {
      const errorMessage = error.details.map(detail => detail.message).join(', ');
      return res.status(400).json({
        error: 'Bad Request',
        message: errorMessage
      });
    }

    // Replace request body with validated value
    req.body = value;
    next();
  };
};

module.exports = {
  validateRequest
};
