import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Typography,
  Divider,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  Switch,
  FormControlLabel,
  LinearProgress,
  Tooltip,
  IconButton
} from '@mui/material';
import {
  Speed as SpeedIcon,
  Memory as MemoryIcon,
  Timeline as TimelineIcon,
  Settings as SettingsIcon,
  Close as CloseIcon
} from '@mui/icons-material';
import visualizationPerformanceService from '../../services/VisualizationPerformanceService';

/**
 * VisualizationPerformance component
 * 
 * Displays performance metrics for visualizations
 */
function VisualizationPerformance({ renderer, scene }) {
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [monitoringControl, setMonitoringControl] = useState(null);
  const [performanceStats, setPerformanceStats] = useState({
    fps: 0,
    renderTime: 0,
    triangleCount: 0,
    drawCalls: 0,
    memoryUsage: 0
  });
  const [showSettings, setShowSettings] = useState(false);
  const [levelOfDetail, setLevelOfDetail] = useState('auto');
  const [frustumCulling, setFrustumCulling] = useState(true);
  const [instancedMeshes, setInstancedMeshes] = useState(true);
  const [mergeGeometries, setMergeGeometries] = useState(true);
  const [simplifyGeometries, setSimplifyGeometries] = useState(true);
  const [maxTriangles, setMaxTriangles] = useState(100000);
  const [showPerformanceOverlay, setShowPerformanceOverlay] = useState(false);
  
  // Start/stop monitoring
  useEffect(() => {
    if (isMonitoring && renderer && scene) {
      const control = visualizationPerformanceService.startMonitoring(renderer, scene);
      setMonitoringControl(control);
      
      // Update stats every 500ms
      const interval = setInterval(() => {
        setPerformanceStats(visualizationPerformanceService.getPerformanceStats());
      }, 500);
      
      return () => {
        clearInterval(interval);
        control.stop();
        setMonitoringControl(null);
      };
    }
  }, [isMonitoring, renderer, scene]);
  
  // Toggle monitoring
  const handleToggleMonitoring = () => {
    setIsMonitoring(!isMonitoring);
  };
  
  // Toggle settings dialog
  const handleToggleSettings = () => {
    setShowSettings(!showSettings);
  };
  
  // Apply optimization settings
  const handleApplySettings = () => {
    if (scene) {
      visualizationPerformanceService.optimizeScene(scene, {
        levelOfDetail,
        frustumCulling,
        instancedMeshes,
        mergeGeometries,
        simplifyGeometries,
        maxTriangles
      });
    }
    
    setShowSettings(false);
  };
  
  // Toggle performance overlay
  const handleTogglePerformanceOverlay = () => {
    setShowPerformanceOverlay(!showPerformanceOverlay);
  };
  
  // Format number with commas
  const formatNumber = (num) => {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  };
  
  // Get performance rating
  const getPerformanceRating = () => {
    const { fps } = performanceStats;
    
    if (fps >= 55) {
      return { label: 'Excellent', color: 'success.main' };
    } else if (fps >= 30) {
      return { label: 'Good', color: 'success.light' };
    } else if (fps >= 20) {
      return { label: 'Fair', color: 'warning.main' };
    } else if (fps >= 10) {
      return { label: 'Poor', color: 'warning.dark' };
    } else {
      return { label: 'Critical', color: 'error.main' };
    }
  };
  
  // Get FPS color
  const getFpsColor = () => {
    const { fps } = performanceStats;
    
    if (fps >= 55) {
      return 'success.main';
    } else if (fps >= 30) {
      return 'success.light';
    } else if (fps >= 20) {
      return 'warning.main';
    } else if (fps >= 10) {
      return 'warning.dark';
    } else {
      return 'error.main';
    }
  };
  
  // Get performance rating
  const rating = getPerformanceRating();

  return (
    <>
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <Tooltip title="Toggle Performance Monitoring">
          <Button
            variant={isMonitoring ? 'contained' : 'outlined'}
            color="primary"
            size="small"
            startIcon={<SpeedIcon />}
            onClick={handleToggleMonitoring}
          >
            {isMonitoring ? 'Monitoring' : 'Monitor'}
          </Button>
        </Tooltip>
        
        <Tooltip title="Optimization Settings">
          <IconButton
            size="small"
            onClick={handleToggleSettings}
          >
            <SettingsIcon fontSize="small" />
          </IconButton>
        </Tooltip>
        
        {isMonitoring && (
          <Tooltip title="Toggle Performance Overlay">
            <IconButton
              size="small"
              onClick={handleTogglePerformanceOverlay}
              color={showPerformanceOverlay ? 'primary' : 'default'}
            >
              <TimelineIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        )}
      </Box>
      
      {/* Performance Overlay */}
      {isMonitoring && showPerformanceOverlay && (
        <Box
          sx={{
            position: 'absolute',
            top: 10,
            right: 10,
            bgcolor: 'rgba(0, 0, 0, 0.7)',
            color: 'white',
            p: 1,
            borderRadius: 1,
            zIndex: 1000,
            fontFamily: 'monospace',
            fontSize: '0.75rem',
            width: 180
          }}
        >
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
            <Typography variant="caption" sx={{ fontWeight: 'bold' }}>
              Performance
            </Typography>
            <IconButton
              size="small"
              onClick={handleTogglePerformanceOverlay}
              sx={{ color: 'white', p: 0 }}
            >
              <CloseIcon fontSize="small" />
            </IconButton>
          </Box>
          
          <Divider sx={{ bgcolor: 'rgba(255, 255, 255, 0.2)', my: 0.5 }} />
          
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="caption">FPS:</Typography>
            <Typography variant="caption" sx={{ color: getFpsColor() }}>
              {performanceStats.fps}
            </Typography>
          </Box>
          
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="caption">Render Time:</Typography>
            <Typography variant="caption">
              {performanceStats.renderTime.toFixed(2)} ms
            </Typography>
          </Box>
          
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="caption">Triangles:</Typography>
            <Typography variant="caption">
              {formatNumber(performanceStats.triangleCount)}
            </Typography>
          </Box>
          
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="caption">Draw Calls:</Typography>
            <Typography variant="caption">
              {performanceStats.drawCalls}
            </Typography>
          </Box>
          
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="caption">Memory:</Typography>
            <Typography variant="caption">
              {performanceStats.memoryUsage}
            </Typography>
          </Box>
          
          <Divider sx={{ bgcolor: 'rgba(255, 255, 255, 0.2)', my: 0.5 }} />
          
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="caption">Rating:</Typography>
            <Typography variant="caption" sx={{ color: rating.color, fontWeight: 'bold' }}>
              {rating.label}
            </Typography>
          </Box>
        </Box>
      )}
      
      {/* Settings Dialog */}
      <Dialog
        open={showSettings}
        onClose={handleToggleSettings}
        maxWidth="xs"
        fullWidth
      >
        <DialogTitle>Optimization Settings</DialogTitle>
        <DialogContent>
          <FormControl fullWidth sx={{ mt: 2, mb: 2 }}>
            <InputLabel id="level-of-detail-label">Level of Detail</InputLabel>
            <Select
              labelId="level-of-detail-label"
              id="level-of-detail"
              value={levelOfDetail}
              label="Level of Detail"
              onChange={(e) => setLevelOfDetail(e.target.value)}
            >
              <MenuItem value="auto">Auto (Based on Device)</MenuItem>
              <MenuItem value="low">Low</MenuItem>
              <MenuItem value="medium">Medium</MenuItem>
              <MenuItem value="high">High</MenuItem>
              <MenuItem value="ultra">Ultra</MenuItem>
            </Select>
          </FormControl>
          
          <FormControlLabel
            control={
              <Switch
                checked={frustumCulling}
                onChange={(e) => setFrustumCulling(e.target.checked)}
              />
            }
            label="Frustum Culling"
            sx={{ display: 'block', mb: 1 }}
          />
          
          <FormControlLabel
            control={
              <Switch
                checked={instancedMeshes}
                onChange={(e) => setInstancedMeshes(e.target.checked)}
              />
            }
            label="Use Instanced Meshes"
            sx={{ display: 'block', mb: 1 }}
          />
          
          <FormControlLabel
            control={
              <Switch
                checked={mergeGeometries}
                onChange={(e) => setMergeGeometries(e.target.checked)}
              />
            }
            label="Merge Geometries"
            sx={{ display: 'block', mb: 1 }}
          />
          
          <FormControlLabel
            control={
              <Switch
                checked={simplifyGeometries}
                onChange={(e) => setSimplifyGeometries(e.target.checked)}
              />
            }
            label="Simplify Geometries"
            sx={{ display: 'block', mb: 1 }}
          />
          
          <Typography gutterBottom>Max Triangles</Typography>
          <Select
            fullWidth
            value={maxTriangles}
            onChange={(e) => setMaxTriangles(e.target.value)}
            sx={{ mb: 2 }}
          >
            <MenuItem value={10000}>10,000 (Low-End Devices)</MenuItem>
            <MenuItem value={50000}>50,000 (Mid-Range Devices)</MenuItem>
            <MenuItem value={100000}>100,000 (High-End Devices)</MenuItem>
            <MenuItem value={500000}>500,000 (Powerful Workstations)</MenuItem>
          </Select>
          
          <Typography variant="caption" color="text.secondary">
            These settings will be applied to the current visualization. Higher quality settings may impact performance on less powerful devices.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleToggleSettings}>Cancel</Button>
          <Button onClick={handleApplySettings} variant="contained" color="primary">
            Apply
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}

export default VisualizationPerformance;
