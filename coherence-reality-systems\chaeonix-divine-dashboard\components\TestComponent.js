/**
 * TEST COMPONENT
 * Simple component to test if basic functionality works
 */

import { useState } from 'react';
import { StarIcon } from '@heroicons/react/24/outline';

export default function TestComponent() {
  const [test, setTest] = useState('Working');

  return (
    <div className="bg-gray-800/50 backdrop-blur-xl rounded-lg border border-gray-600 p-6">
      <div className="flex items-center space-x-2">
        <StarIcon className="w-5 h-5 text-purple-400" />
        <span className="text-white">Test Component: {test}</span>
      </div>
    </div>
  );
}
