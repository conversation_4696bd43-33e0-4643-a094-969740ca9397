/**
 * CSEDAdapter.js
 * 
 * This file defines the standard interface for CSDE adapters that enhance
 * Nova components with Cyber-Safety Domain Engine capabilities.
 * 
 * The CSDE adapter pattern allows each Nova component to receive a "boost"
 * from the CSDE while maintaining a consistent integration approach.
 */

const EventEmitter = require('events');
const { performance } = require('perf_hooks');

/**
 * CSDE operation types
 * @enum {string}
 */
const CSEDOperationType = {
  ANALYZE: 'ANALYZE',
  ENHANCE: 'ENHANCE',
  VALIDATE: 'VALIDATE',
  TRANSFORM: 'TRANSFORM',
  PREDICT: 'PREDICT',
  REMEDIATE: 'REMEDIATE'
};

/**
 * CSDE domain types
 * @enum {string}
 */
const CSEDDomainType = {
  COMPLIANCE: 'COMPLIANCE',
  SECURITY: 'SECURITY',
  GOVERNANCE: 'GOVERNANCE',
  RISK: 'RISK',
  GENERAL: 'GENERAL'
};

/**
 * CSDE adapter status
 * @enum {string}
 */
const CSEDAdapterStatus = {
  INITIALIZING: 'INITIALIZING',
  READY: 'READY',
  PROCESSING: 'PROCESSING',
  DEGRADED: 'DEGRADED',
  ERROR: 'ERROR',
  SHUTDOWN: 'SHUTDOWN'
};

/**
 * Abstract CSDE adapter class that all component-specific adapters must implement
 * @abstract
 * @extends EventEmitter
 */
class CSEDAdapter extends EventEmitter {
  /**
   * Create a new CSDE adapter
   * @param {Object} options - Adapter options
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      componentName: options.componentName || 'unknown',
      enableLogging: options.enableLogging !== undefined ? options.enableLogging : true,
      enableMetrics: options.enableMetrics !== undefined ? options.enableMetrics : true,
      enableCaching: options.enableCaching !== undefined ? options.enableCaching : true,
      cacheSize: options.cacheSize || 1000,
      cacheExpiration: options.cacheExpiration || 300000, // 5 minutes
      csdeEndpoint: options.csdeEndpoint || process.env.CSDE_ENDPOINT || 'http://localhost:3010',
      csdeApiKey: options.csdeApiKey || process.env.CSDE_API_KEY,
      fallbackEnabled: options.fallbackEnabled !== undefined ? options.fallbackEnabled : true,
      retryAttempts: options.retryAttempts || 3,
      retryDelay: options.retryDelay || 1000,
      timeout: options.timeout || 30000,
      ...options
    };
    
    // Initialize adapter state
    this.status = CSEDAdapterStatus.INITIALIZING;
    this.csdeClient = null;
    this.cache = new Map();
    this.metrics = {
      requests: 0,
      cacheHits: 0,
      cacheMisses: 0,
      errors: 0,
      latency: {
        total: 0,
        count: 0
      },
      operations: {
        [CSEDOperationType.ANALYZE]: 0,
        [CSEDOperationType.ENHANCE]: 0,
        [CSEDOperationType.VALIDATE]: 0,
        [CSEDOperationType.TRANSFORM]: 0,
        [CSEDOperationType.PREDICT]: 0,
        [CSEDOperationType.REMEDIATE]: 0
      },
      domains: {
        [CSEDDomainType.COMPLIANCE]: 0,
        [CSEDDomainType.SECURITY]: 0,
        [CSEDDomainType.GOVERNANCE]: 0,
        [CSEDDomainType.RISK]: 0,
        [CSEDDomainType.GENERAL]: 0
      }
    };
    
    // Set up cache cleanup interval
    if (this.options.enableCaching) {
      this.cacheCleanupInterval = setInterval(() => {
        this._cleanupCache();
      }, 60000); // Clean up every minute
    }
    
    this.log(`CSDE adapter for ${this.options.componentName} initialized with options:`, this.options);
  }
  
  /**
   * Log a message if logging is enabled
   * @param {...any} args - Arguments to log
   */
  log(...args) {
    if (this.options.enableLogging) {
      console.log(`[CSEDAdapter:${this.options.componentName} ${new Date().toISOString()}]`, ...args);
    }
  }
  
  /**
   * Initialize the CSDE adapter
   * @returns {Promise<void>} - A promise that resolves when initialization is complete
   */
  async initialize() {
    try {
      this.log(`Initializing CSDE adapter for ${this.options.componentName}...`);
      
      // Initialize CSDE client
      await this._initializeCSEDClient();
      
      // Verify CSDE connection
      await this._verifyCSEDConnection();
      
      // Set status to ready
      this.status = CSEDAdapterStatus.READY;
      
      this.log(`CSDE adapter for ${this.options.componentName} initialized successfully`);
      this.emit('ready');
      
      return Promise.resolve();
    } catch (error) {
      this.status = CSEDAdapterStatus.ERROR;
      this.log(`Error initializing CSDE adapter for ${this.options.componentName}:`, error);
      this.emit('error', error);
      
      if (this.options.fallbackEnabled) {
        this.log(`Enabling fallback mode for ${this.options.componentName}`);
        this.status = CSEDAdapterStatus.DEGRADED;
      }
      
      return Promise.reject(error);
    }
  }
  
  /**
   * Initialize the CSDE client
   * @returns {Promise<void>} - A promise that resolves when the client is initialized
   * @protected
   */
  async _initializeCSEDClient() {
    // This method should be implemented by subclasses
    throw new Error('_initializeCSEDClient must be implemented by subclasses');
  }
  
  /**
   * Verify the CSDE connection
   * @returns {Promise<void>} - A promise that resolves when the connection is verified
   * @protected
   */
  async _verifyCSEDConnection() {
    // This method should be implemented by subclasses
    throw new Error('_verifyCSEDConnection must be implemented by subclasses');
  }
  
  /**
   * Process data with CSDE
   * @param {Object} data - The data to process
   * @param {CSEDOperationType} operation - The operation to perform
   * @param {CSEDDomainType} domain - The domain to process in
   * @param {Object} options - Processing options
   * @returns {Promise<Object>} - A promise that resolves to the processed data
   */
  async process(data, operation = CSEDOperationType.ENHANCE, domain = CSEDDomainType.GENERAL, options = {}) {
    const startTime = performance.now();
    
    try {
      // Update metrics
      if (this.options.enableMetrics) {
        this.metrics.requests++;
        this.metrics.operations[operation]++;
        this.metrics.domains[domain]++;
      }
      
      // Check adapter status
      if (this.status === CSEDAdapterStatus.ERROR) {
        throw new Error(`CSDE adapter for ${this.options.componentName} is in ERROR state`);
      }
      
      // Set status to processing
      const previousStatus = this.status;
      this.status = CSEDAdapterStatus.PROCESSING;
      
      // Check cache if enabled
      if (this.options.enableCaching) {
        const cacheKey = this._generateCacheKey(data, operation, domain);
        if (this.cache.has(cacheKey)) {
          const cachedResult = this.cache.get(cacheKey);
          
          // Check if cache entry is still valid
          if (Date.now() < cachedResult.expiresAt) {
            // Update metrics
            if (this.options.enableMetrics) {
              this.metrics.cacheHits++;
              const latency = performance.now() - startTime;
              this.metrics.latency.total += latency;
              this.metrics.latency.count++;
            }
            
            // Restore previous status
            this.status = previousStatus;
            
            this.log(`Cache hit for ${operation} operation in ${domain} domain`);
            return cachedResult.data;
          } else {
            // Remove expired cache entry
            this.cache.delete(cacheKey);
          }
        }
        
        // Update cache miss metric
        if (this.options.enableMetrics) {
          this.metrics.cacheMisses++;
        }
      }
      
      // Transform component data to CSDE format
      const csdeData = await this._transformToCSEDFormat(data, operation, domain);
      
      // Process data with CSDE
      let result;
      if (this.status === CSEDAdapterStatus.DEGRADED && this.options.fallbackEnabled) {
        // Use fallback processing in degraded mode
        result = await this._fallbackProcessing(csdeData, operation, domain, options);
      } else {
        // Use normal CSDE processing
        result = await this._processWithCSED(csdeData, operation, domain, options);
      }
      
      // Transform CSDE result back to component format
      const componentResult = await this._transformFromCSEDFormat(result, operation, domain);
      
      // Cache result if caching is enabled
      if (this.options.enableCaching) {
        const cacheKey = this._generateCacheKey(data, operation, domain);
        this.cache.set(cacheKey, {
          data: componentResult,
          expiresAt: Date.now() + this.options.cacheExpiration
        });
        
        // Limit cache size
        if (this.cache.size > this.options.cacheSize) {
          // Remove oldest entry
          const oldestKey = this.cache.keys().next().value;
          this.cache.delete(oldestKey);
        }
      }
      
      // Update metrics
      if (this.options.enableMetrics) {
        const latency = performance.now() - startTime;
        this.metrics.latency.total += latency;
        this.metrics.latency.count++;
      }
      
      // Restore previous status
      this.status = previousStatus;
      
      return componentResult;
    } catch (error) {
      // Update metrics
      if (this.options.enableMetrics) {
        this.metrics.errors++;
      }
      
      this.log(`Error processing data with CSDE for ${this.options.componentName}:`, error);
      
      // Try fallback processing if enabled
      if (this.options.fallbackEnabled) {
        try {
          this.log(`Attempting fallback processing for ${operation} operation in ${domain} domain`);
          
          // Set status to degraded
          this.status = CSEDAdapterStatus.DEGRADED;
          
          // Transform component data to CSDE format
          const csdeData = await this._transformToCSEDFormat(data, operation, domain);
          
          // Use fallback processing
          const result = await this._fallbackProcessing(csdeData, operation, domain, options);
          
          // Transform CSDE result back to component format
          const componentResult = await this._transformFromCSEDFormat(result, operation, domain);
          
          this.log(`Fallback processing succeeded for ${operation} operation in ${domain} domain`);
          
          return componentResult;
        } catch (fallbackError) {
          this.log(`Fallback processing failed for ${operation} operation in ${domain} domain:`, fallbackError);
          throw fallbackError;
        }
      }
      
      throw error;
    }
  }
  
  /**
   * Transform component data to CSDE format
   * @param {Object} data - The component data
   * @param {CSEDOperationType} operation - The operation to perform
   * @param {CSEDDomainType} domain - The domain to process in
   * @returns {Promise<Object>} - A promise that resolves to the CSDE-formatted data
   * @protected
   */
  async _transformToCSEDFormat(data, operation, domain) {
    // This method should be implemented by subclasses
    throw new Error('_transformToCSEDFormat must be implemented by subclasses');
  }
  
  /**
   * Process data with CSDE
   * @param {Object} csdeData - The CSDE-formatted data
   * @param {CSEDOperationType} operation - The operation to perform
   * @param {CSEDDomainType} domain - The domain to process in
   * @param {Object} options - Processing options
   * @returns {Promise<Object>} - A promise that resolves to the CSDE result
   * @protected
   */
  async _processWithCSED(csdeData, operation, domain, options) {
    // This method should be implemented by subclasses
    throw new Error('_processWithCSED must be implemented by subclasses');
  }
  
  /**
   * Transform CSDE result back to component format
   * @param {Object} csdeResult - The CSDE result
   * @param {CSEDOperationType} operation - The operation performed
   * @param {CSEDDomainType} domain - The domain processed in
   * @returns {Promise<Object>} - A promise that resolves to the component-formatted result
   * @protected
   */
  async _transformFromCSEDFormat(csdeResult, operation, domain) {
    // This method should be implemented by subclasses
    throw new Error('_transformFromCSEDFormat must be implemented by subclasses');
  }
  
  /**
   * Fallback processing when CSDE is unavailable
   * @param {Object} csdeData - The CSDE-formatted data
   * @param {CSEDOperationType} operation - The operation to perform
   * @param {CSEDDomainType} domain - The domain to process in
   * @param {Object} options - Processing options
   * @returns {Promise<Object>} - A promise that resolves to the fallback result
   * @protected
   */
  async _fallbackProcessing(csdeData, operation, domain, options) {
    // This method should be implemented by subclasses
    throw new Error('_fallbackProcessing must be implemented by subclasses');
  }
  
  /**
   * Generate a cache key for the given data, operation, and domain
   * @param {Object} data - The data to process
   * @param {CSEDOperationType} operation - The operation to perform
   * @param {CSEDDomainType} domain - The domain to process in
   * @returns {string} - The cache key
   * @private
   */
  _generateCacheKey(data, operation, domain) {
    // Create a deterministic string representation of the data
    const dataString = typeof data === 'string' 
      ? data 
      : JSON.stringify(data, Object.keys(data).sort());
    
    // Create a hash of the data
    const crypto = require('crypto');
    const hash = crypto.createHash('sha256').update(dataString).digest('hex');
    
    // Combine operation, domain, and hash to create the cache key
    return `${operation}:${domain}:${hash}`;
  }
  
  /**
   * Clean up expired cache entries
   * @private
   */
  _cleanupCache() {
    if (!this.options.enableCaching) {
      return;
    }
    
    const now = Date.now();
    let expiredCount = 0;
    
    // Remove expired entries
    for (const [key, value] of this.cache.entries()) {
      if (now > value.expiresAt) {
        this.cache.delete(key);
        expiredCount++;
      }
    }
    
    if (expiredCount > 0) {
      this.log(`Cleaned up ${expiredCount} expired cache entries`);
    }
  }
  
  /**
   * Get adapter metrics
   * @returns {Object} - The adapter metrics
   */
  getMetrics() {
    if (!this.options.enableMetrics) {
      return { metricsDisabled: true };
    }
    
    return {
      componentName: this.options.componentName,
      status: this.status,
      requests: this.metrics.requests,
      cacheHits: this.metrics.cacheHits,
      cacheMisses: this.metrics.cacheMisses,
      cacheHitRatio: this.metrics.requests > 0 
        ? this.metrics.cacheHits / this.metrics.requests 
        : 0,
      errors: this.metrics.errors,
      errorRate: this.metrics.requests > 0 
        ? this.metrics.errors / this.metrics.requests 
        : 0,
      averageLatency: this.metrics.latency.count > 0 
        ? this.metrics.latency.total / this.metrics.latency.count 
        : 0,
      operations: this.metrics.operations,
      domains: this.metrics.domains,
      cacheSize: this.cache.size
    };
  }
  
  /**
   * Reset adapter metrics
   */
  resetMetrics() {
    if (!this.options.enableMetrics) {
      return;
    }
    
    this.metrics = {
      requests: 0,
      cacheHits: 0,
      cacheMisses: 0,
      errors: 0,
      latency: {
        total: 0,
        count: 0
      },
      operations: {
        [CSEDOperationType.ANALYZE]: 0,
        [CSEDOperationType.ENHANCE]: 0,
        [CSEDOperationType.VALIDATE]: 0,
        [CSEDOperationType.TRANSFORM]: 0,
        [CSEDOperationType.PREDICT]: 0,
        [CSEDOperationType.REMEDIATE]: 0
      },
      domains: {
        [CSEDDomainType.COMPLIANCE]: 0,
        [CSEDDomainType.SECURITY]: 0,
        [CSEDDomainType.GOVERNANCE]: 0,
        [CSEDDomainType.RISK]: 0,
        [CSEDDomainType.GENERAL]: 0
      }
    };
    
    this.log(`Metrics reset for ${this.options.componentName}`);
  }
  
  /**
   * Clear the adapter cache
   */
  clearCache() {
    if (!this.options.enableCaching) {
      return;
    }
    
    const cacheSize = this.cache.size;
    this.cache.clear();
    
    this.log(`Cleared ${cacheSize} cache entries for ${this.options.componentName}`);
  }
  
  /**
   * Shutdown the adapter
   * @returns {Promise<void>} - A promise that resolves when shutdown is complete
   */
  async shutdown() {
    try {
      this.log(`Shutting down CSDE adapter for ${this.options.componentName}...`);
      
      // Clear cache cleanup interval
      if (this.cacheCleanupInterval) {
        clearInterval(this.cacheCleanupInterval);
        this.cacheCleanupInterval = null;
      }
      
      // Clear cache
      this.clearCache();
      
      // Set status to shutdown
      this.status = CSEDAdapterStatus.SHUTDOWN;
      
      this.log(`CSDE adapter for ${this.options.componentName} shut down successfully`);
      this.emit('shutdown');
      
      return Promise.resolve();
    } catch (error) {
      this.log(`Error shutting down CSDE adapter for ${this.options.componentName}:`, error);
      this.emit('error', error);
      return Promise.reject(error);
    }
  }
}

module.exports = {
  CSEDAdapter,
  CSEDOperationType,
  CSEDDomainType,
  CSEDAdapterStatus
};
