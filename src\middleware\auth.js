/**
 * NovaFuse Universal API Connector - Authentication Middleware
 * 
 * This module provides middleware for authentication and authorization.
 */

const { createLogger } = require('../utils/logger');

const logger = createLogger('auth-middleware');

/**
 * Simple API key authentication middleware
 * 
 * @param {Object} req - The request object
 * @param {Object} res - The response object
 * @param {Function} next - The next middleware function
 */
function apiKeyAuth(req, res, next) {
  try {
    // Get API key from header
    const apiKey = req.headers['x-api-key'];
    
    // Check if API key is provided
    if (!apiKey) {
      return res.status(401).json({
        error: {
          message: 'API key is required',
          code: 'API_KEY_REQUIRED'
        }
      });
    }
    
    // In a real implementation, we would validate the API key against a database
    // For now, we'll use a simple hardcoded key for demonstration
    const validApiKey = process.env.API_KEY || 'novafuse-uac-api-key';
    
    if (apiKey !== validApiKey) {
      return res.status(401).json({
        error: {
          message: 'Invalid API key',
          code: 'INVALID_API_KEY'
        }
      });
    }
    
    // Add user info to request
    req.user = {
      id: 'api-user',
      role: 'api'
    };
    
    next();
  } catch (error) {
    logger.error('Authentication error:', { error });
    
    return res.status(500).json({
      error: {
        message: 'Authentication error',
        code: 'AUTH_ERROR'
      }
    });
  }
}

/**
 * Role-based authorization middleware
 * 
 * @param {string[]} roles - The roles allowed to access the resource
 * @returns {Function} - The authorization middleware
 */
function roleAuth(roles = []) {
  return (req, res, next) => {
    try {
      // Check if user exists
      if (!req.user) {
        return res.status(401).json({
          error: {
            message: 'User not authenticated',
            code: 'USER_NOT_AUTHENTICATED'
          }
        });
      }
      
      // Check if user has required role
      if (roles.length > 0 && !roles.includes(req.user.role)) {
        return res.status(403).json({
          error: {
            message: `User role ${req.user.role} not authorized`,
            code: 'ROLE_NOT_AUTHORIZED'
          }
        });
      }
      
      next();
    } catch (error) {
      logger.error('Authorization error:', { error });
      
      return res.status(500).json({
        error: {
          message: 'Authorization error',
          code: 'AUTH_ERROR'
        }
      });
    }
  };
}

/**
 * Development authentication middleware (no authentication)
 * 
 * @param {Object} req - The request object
 * @param {Object} res - The response object
 * @param {Function} next - The next middleware function
 */
function devAuth(req, res, next) {
  // Add mock user to request
  req.user = {
    id: 'dev-user',
    role: 'admin'
  };
  
  next();
}

module.exports = {
  apiKeyAuth,
  roleAuth,
  devAuth
};
