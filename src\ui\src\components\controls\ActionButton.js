import React, { useState } from 'react';
import { Button, CircularProgress } from '@mui/material';
import { useControl } from '../../contexts/ControlContext';

/**
 * ActionButton component
 * 
 * Renders a button that executes an action
 */
function ActionButton({ action, params, label, icon, color = 'primary', variant = 'contained', sx = {} }) {
  const { executeAction } = useControl();
  const [loading, setLoading] = useState(false);

  const handleClick = async () => {
    setLoading(true);
    try {
      await executeAction(action, params);
    } catch (error) {
      console.error(`Error executing action ${action}:`, error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Button
      variant={variant}
      color={color}
      onClick={handleClick}
      disabled={loading}
      startIcon={loading ? <CircularProgress size={20} color="inherit" /> : icon}
      sx={sx}
    >
      {label}
    </Button>
  );
}

export default ActionButton;
