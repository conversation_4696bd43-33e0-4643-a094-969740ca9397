{"name": "nova-agent-dashboard", "version": "1.0.0", "description": "Nova Agent - NovaFuse Coherence Operating System Dashboard", "private": true, "scripts": {"dev": "next dev -p 3001", "build": "next build", "start": "next start -p 3001", "lint": "next lint"}, "dependencies": {"@heroicons/react": "^2.0.18", "autoprefixer": "^10.4.16", "framer-motion": "^10.16.4", "next": "^14.0.0", "postcss": "^8.4.31", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwindcss": "^3.3.5"}, "devDependencies": {"eslint": "^8.52.0", "eslint-config-next": "14.0.0"}}