/**
 * Rate Limit Controller
 * 
 * This controller handles API requests related to rate limiting.
 */

const RateLimitService = require('../services/RateLimitService');
const { ValidationError } = require('../utils/errors');

class RateLimitController {
  constructor() {
    this.rateLimitService = new RateLimitService();
  }

  /**
   * Get rate limits
   */
  async getRateLimits(req, res, next) {
    try {
      const rateLimits = await this.rateLimitService.getRateLimits();
      
      res.json({
        success: true,
        data: rateLimits
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update rate limits
   */
  async updateRateLimits(req, res, next) {
    try {
      const { rateLimits } = req.body;
      
      if (!rateLimits) {
        throw new ValidationError('Rate limits are required');
      }
      
      // Validate rate limits
      for (const type in rateLimits) {
        const rateLimit = rateLimits[type];
        
        if (!rateLimit.requests || !rateLimit.period) {
          throw new ValidationError(`Invalid rate limit for type '${type}'`);
        }
        
        if (typeof rateLimit.requests !== 'number' || rateLimit.requests <= 0) {
          throw new ValidationError(`Invalid requests value for type '${type}'`);
        }
        
        if (typeof rateLimit.period !== 'number' || rateLimit.period <= 0) {
          throw new ValidationError(`Invalid period value for type '${type}'`);
        }
      }
      
      const updatedRateLimits = await this.rateLimitService.updateRateLimits(rateLimits);
      
      res.json({
        success: true,
        data: updatedRateLimits
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Reset rate limits
   */
  async resetRateLimits(req, res, next) {
    try {
      const defaultRateLimits = this.rateLimitService.defaultRateLimit;
      const updatedRateLimits = await this.rateLimitService.updateRateLimits(defaultRateLimits);
      
      res.json({
        success: true,
        data: updatedRateLimits
      });
    } catch (error) {
      next(error);
    }
  }
}

module.exports = new RateLimitController();
