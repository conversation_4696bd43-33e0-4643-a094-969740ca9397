import React from 'react';
import { Box, Typography, useTheme } from '@mui/material';
import { 
  <PERSON>Chart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  <PERSON>lt<PERSON>, 
  Legend, 
  ResponsiveContainer,
  <PERSON><PERSON>er<PERSON><PERSON>,
  <PERSON>atter,
  ZAxis,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar
} from 'recharts';

/**
 * Cross-Domain Prediction Chart Component
 * 
 * Displays charts for cross-domain prediction metrics.
 * 
 * @param {Object} props - Component props
 * @param {Object} props.data - Cross-domain prediction data
 * @param {Object} props.metrics - Cross-domain prediction metrics
 */
const CrossDomainPredictionChart = ({ data, metrics }) => {
  const theme = useTheme();
  
  // If no data is available, show a message
  if (!data || !data.predictions || data.predictions.length === 0) {
    return (
      <Box sx={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100%' 
      }}>
        <Typography variant="body2" color="text.secondary">
          No cross-domain prediction data available
        </Typography>
      </Box>
    );
  }
  
  // Format timestamp for display
  const formatTimestamp = (timestamp) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString();
  };
  
  // Prepare data for confidence chart
  const confidenceData = data.predictions.map(prediction => ({
    time: formatTimestamp(prediction.timestamp),
    timestamp: prediction.timestamp,
    confidence: prediction.averageConfidence,
    count: prediction.predictionsCount,
    sourceDomain: prediction.sourceDomain,
    targetDomain: prediction.targetDomain
  }));
  
  // Prepare data for domain mapping chart
  const domainMappingData = [];
  
  // Process domain mappings
  data.domainMappings.forEach(mapping => {
    domainMappingData.push({
      source: mapping.sourceDomain,
      target: mapping.targetDomain,
      count: mapping.mappingCount,
      confidence: mapping.averageConfidence
    });
  });
  
  // Prepare data for feature importance radar chart
  const featureImportanceData = [
    { subject: 'Tensor Fusion', A: data.featureImportance.tensorFusion * 100 },
    { subject: 'Adaptive Learning', A: data.featureImportance.adaptiveLearning * 100 },
    { subject: 'Circular Trust', A: data.featureImportance.circularTrust * 100 },
    { subject: 'Pattern Matching', A: data.featureImportance.patternMatching * 100 },
    { subject: 'Semantic Analysis', A: data.featureImportance.semanticAnalysis * 100 }
  ];
  
  // Calculate success rate
  const successRate = metrics.totalRequests > 0 
    ? metrics.successfulRequests / metrics.totalRequests 
    : 1;
  
  // Format success rate as percentage
  const successRateFormatted = `${(successRate * 100).toFixed(1)}%`;
  
  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
      {/* Summary Metrics */}
      <Box sx={{ 
        display: 'flex', 
        justifyContent: 'space-around', 
        mb: 1 
      }}>
        <Box sx={{ textAlign: 'center' }}>
          <Typography variant="caption" color="text.secondary">
            Success Rate
          </Typography>
          <Typography variant="body2" fontWeight="bold">
            {successRateFormatted}
          </Typography>
        </Box>
        <Box sx={{ textAlign: 'center' }}>
          <Typography variant="caption" color="text.secondary">
            Avg. Latency
          </Typography>
          <Typography variant="body2" fontWeight="bold">
            {metrics.averageLatency.toFixed(2)}ms
          </Typography>
        </Box>
        <Box sx={{ textAlign: 'center' }}>
          <Typography variant="caption" color="text.secondary">
            Total Predictions
          </Typography>
          <Typography variant="body2" fontWeight="bold">
            {data.totalPredictions}
          </Typography>
        </Box>
        <Box sx={{ textAlign: 'center' }}>
          <Typography variant="caption" color="text.secondary">
            Avg. Confidence
          </Typography>
          <Typography variant="body2" fontWeight="bold">
            {(data.averageConfidence * 100).toFixed(1)}%
          </Typography>
        </Box>
      </Box>
      
      {/* Charts */}
      <Box sx={{ display: 'flex', flexGrow: 1 }}>
        {/* Confidence Chart */}
        <Box sx={{ width: '50%', height: '100%' }}>
          <ResponsiveContainer width="100%" height="100%">
            <LineChart
              data={confidenceData}
              margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="time" 
                tick={{ fontSize: 10 }}
              />
              <YAxis 
                yAxisId="left"
                domain={[0, 1]}
                tickFormatter={(value) => `${(value * 100).toFixed(0)}%`}
                label={{ 
                  value: 'Confidence', 
                  angle: -90, 
                  position: 'insideLeft',
                  style: { fontSize: 10 }
                }}
                tick={{ fontSize: 10 }}
              />
              <YAxis 
                yAxisId="right" 
                orientation="right" 
                domain={[0, 'dataMax + 5']}
                label={{ 
                  value: 'Count', 
                  angle: 90, 
                  position: 'insideRight',
                  style: { fontSize: 10 }
                }}
                tick={{ fontSize: 10 }}
              />
              <Tooltip 
                formatter={(value, name) => {
                  if (name === 'confidence') return [`${(value * 100).toFixed(1)}%`, 'Confidence'];
                  if (name === 'count') return [value, 'Predictions Count'];
                  return [value, name];
                }}
              />
              <Legend />
              <Line 
                yAxisId="left"
                type="monotone" 
                dataKey="confidence" 
                name="Confidence" 
                stroke={theme.palette.primary.main} 
                activeDot={{ r: 8 }} 
              />
              <Line 
                yAxisId="right"
                type="monotone" 
                dataKey="count" 
                name="Predictions Count" 
                stroke={theme.palette.secondary.main} 
                strokeDasharray="5 5"
              />
            </LineChart>
          </ResponsiveContainer>
        </Box>
        
        {/* Feature Importance Radar Chart */}
        <Box sx={{ width: '50%', height: '100%', display: 'flex', flexDirection: 'column' }}>
          <Typography variant="caption" color="text.secondary" align="center" sx={{ display: 'block' }}>
            Feature Importance
          </Typography>
          <ResponsiveContainer width="100%" height="100%">
            <RadarChart 
              cx="50%" 
              cy="50%" 
              outerRadius="80%" 
              data={featureImportanceData}
            >
              <PolarGrid />
              <PolarAngleAxis dataKey="subject" tick={{ fontSize: 10 }} />
              <PolarRadiusAxis 
                angle={30} 
                domain={[0, 100]} 
                tick={{ fontSize: 10 }}
                tickFormatter={(value) => `${value}%`}
              />
              <Radar 
                name="Feature Importance" 
                dataKey="A" 
                stroke={theme.palette.primary.main} 
                fill={theme.palette.primary.main} 
                fillOpacity={0.6} 
              />
              <Tooltip 
                formatter={(value) => [`${value.toFixed(1)}%`, 'Importance']}
              />
              <Legend />
            </RadarChart>
          </ResponsiveContainer>
        </Box>
      </Box>
      
      {/* Domain Mapping Scatter Chart */}
      <Box sx={{ height: '40%', mt: 2 }}>
        <Typography variant="caption" color="text.secondary" align="center" sx={{ display: 'block' }}>
          Domain Mapping Confidence
        </Typography>
        <ResponsiveContainer width="100%" height="90%">
          <ScatterChart
            margin={{ top: 20, right: 20, bottom: 20, left: 20 }}
          >
            <CartesianGrid />
            <XAxis 
              type="category" 
              dataKey="source" 
              name="Source Domain" 
              tick={{ fontSize: 10 }}
            />
            <YAxis 
              type="category" 
              dataKey="target" 
              name="Target Domain" 
              tick={{ fontSize: 10 }}
            />
            <ZAxis 
              type="number" 
              dataKey="confidence" 
              range={[50, 500]} 
              name="Confidence" 
            />
            <Tooltip 
              cursor={{ strokeDasharray: '3 3' }}
              formatter={(value, name, props) => {
                if (name === 'confidence') return [`${(value * 100).toFixed(1)}%`, 'Confidence'];
                if (name === 'count') return [value, 'Mappings Count'];
                return [value, name];
              }}
            />
            <Legend />
            <Scatter 
              name="Domain Mappings" 
              data={domainMappingData} 
              fill={theme.palette.info.main} 
            />
          </ScatterChart>
        </ResponsiveContainer>
      </Box>
    </Box>
  );
};

export default CrossDomainPredictionChart;
