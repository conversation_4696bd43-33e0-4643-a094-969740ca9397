/* Triadic Color Scheme */
:root {
    --psi-color: #2c3e50;      /* Structural Coherence */
    --phi-color: #e67e22;      /* Functional Alignment */
    --theta-color: #3498db;     /* Relational Integrity */
    --gold-ratio: 1.618;
    --diagram-bg: #f8f9fa;
    --equation-bg: #f5f5f5;
    --equation-border: #e0e0e0;
}

/* Diagram Styles */
.diagram-container {
    background: var(--diagram-bg);
    padding: 1.5rem;
    margin: 2rem 0;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.diagram-caption {
    font-style: italic;
    font-size: 0.9rem;
    color: #666;
    margin-top: 0.5rem;
}

/* Equation Styles */
.equation-container {
    background: var(--equation-bg);
    padding: 1.5rem;
    margin: 2rem 0;
    border-radius: 8px;
    border: 1px solid var(--equation-border);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.equation {
    font-family: 'Cambria Math', serif;
    font-size: 1.2rem;
    line-height: 1.5;
    color: #2c3e50;
}

.equation-number {
    float: right;
    font-size: 0.9rem;
    color: #666;
    margin-left: 1rem;
}

/* Formula Styles */
.formula {
    display: block;
    margin: 1rem 0;
    padding: 0.5rem;
    background: #f8f9fa;
    border-radius: 4px;
}

.formula-inline {
    background: #f8f9fa;
    padding: 0.2rem 0.5rem;
    border-radius: 4px;
}

/* Mathematical Symbols */
.math-symbol {
    font-family: 'Cambria Math', serif;
    font-size: 1.2rem;
    color: var(--theta-color);
}

/* Greek Letters */
.greek-letter {
    font-family: 'Cambria Math', serif;
    font-size: 1.2rem;
    color: var(--phi-color);
}

/* Variables */
.variable {
    font-style: italic;
    color: var(--psi-color);
}

/* Constants */
.constant {
    font-weight: bold;
    color: var(--theta-color);
}

/* Operators */
.operator {
    color: var(--phi-color);
}

/* Print Styles */
@media print {
    .diagram-container {
        box-shadow: none;
        border: 1px solid #ddd;
    }
    
    .equation-container {
        box-shadow: none;
        border: 1px solid #ddd;
    }
    
    .equation-number {
        display: none;
    }
}

/* Coherence-themed Icons */
.coherence-icon {
    font-family: 'Coherence Icons', sans-serif;
    font-size: 1.5rem;
    margin-right: 0.5rem;
}

.psi-icon { color: var(--psi-color); }
.phi-icon { color: var(--phi-color); }
.theta-icon { color: var(--theta-color); }

/* Golden Ratio Spacing */
.coherence-spacing {
    --base: 1rem;
    margin: calc(var(--base) * var(--gold-ratio)) 0;
    padding: calc(var(--base) / var(--gold-ratio));
}

/* Symbol Glossary Styles */
.symbol-glossary {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 8px;
    margin: 2rem 0;
}

.symbol-glossary table {
    border-collapse: separate;
    border-spacing: 0 1rem;
}

.symbol-glossary th {
    background: none;
    padding: 1rem;
    font-size: 1.2rem;
}

.symbol-glossary td {
    padding: 1rem;
    vertical-align: top;
}

/* Real-world Use Cases */
.use-case {
    background: #f5f5f5;
    border-left: 4px solid var(--phi-color);
    padding: 1.5rem;
    margin: 2rem 0;
    border-radius: 0 8px 8px 0;
}

.use-case h3 {
    color: var(--psi-color);
    margin-top: 0;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --background: #1a1a1a;
        --text: #ffffff;
        --psi-color: #87ceeb;
        --phi-color: #ff9800;
        --theta-color: #81d4fa;
    }

    body {
        background: var(--background);
        color: var(--text);
    }

    .term-entry {
        background: #2d2d2d;
    }

    table {
        background: #2d2d2d;
    }

    th {
        background: #3d3d3d;
    }

    tr:nth-child(even) {
        background: #3d3d3d;
    }
}

/* Print Styles */
@media print {
    .coherence-icon {
        display: none;
    }

    .use-case {
        border: none;
        background: white;
    }

    .symbol-glossary {
        background: white;
        box-shadow: none;
    }
}
