{"id": "governance-board-compliance", "name": "Governance & Board Compliance Connector", "description": "Connector for governance and board compliance systems", "version": "1.0.0", "category": "governance", "icon": "governance-icon.svg", "author": "NovaFuse", "website": "https://novafuse.io", "documentation": "https://docs.novafuse.io/connectors/governance", "supportEmail": "<EMAIL>", "authentication": {"type": "oauth2", "oauth2": {"authorizationUrl": "https://auth.example.com/oauth2/authorize", "tokenUrl": "https://auth.example.com/oauth2/token", "scopes": ["read:governance", "write:governance"], "refreshTokenUrl": "https://auth.example.com/oauth2/token"}, "fields": {"clientId": {"type": "string", "label": "Client ID", "required": true, "sensitive": false, "description": "OAuth 2.0 Client ID"}, "clientSecret": {"type": "string", "label": "Client Secret", "required": true, "sensitive": true, "description": "OAuth 2.0 Client Secret"}, "redirectUri": {"type": "string", "label": "Redirect URI", "required": true, "sensitive": false, "description": "OAuth 2.0 Redirect URI"}}}, "endpoints": [{"id": "listPolicies", "name": "List Policies", "description": "List all governance policies", "method": "GET", "url": "https://api.example.com/policies", "headers": {"Content-Type": "application/json", "Accept": "application/json"}, "queryParameters": {"page": {"type": "integer", "label": "Page", "required": false, "default": 1, "description": "Page number"}, "limit": {"type": "integer", "label": "Limit", "required": false, "default": 20, "description": "Number of items per page"}, "status": {"type": "string", "label": "Status", "required": false, "enum": ["active", "inactive", "draft"], "description": "Filter by policy status"}}, "inputSchema": {"type": "object", "properties": {"page": {"type": "integer", "description": "Page number"}, "limit": {"type": "integer", "description": "Number of items per page"}, "status": {"type": "string", "description": "Filter by policy status", "enum": ["active", "inactive", "draft"]}}}, "outputSchema": {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Policy ID"}, "name": {"type": "string", "description": "Policy name"}, "description": {"type": "string", "description": "Policy description"}, "status": {"type": "string", "description": "Policy status", "enum": ["active", "inactive", "draft"]}, "createdAt": {"type": "string", "format": "date-time", "description": "Policy creation date"}, "updatedAt": {"type": "string", "format": "date-time", "description": "Policy last update date"}}}}, "pagination": {"type": "object", "properties": {"page": {"type": "integer", "description": "Current page number"}, "limit": {"type": "integer", "description": "Number of items per page"}, "totalItems": {"type": "integer", "description": "Total number of items"}, "totalPages": {"type": "integer", "description": "Total number of pages"}}}}}}, {"id": "getPolicy", "name": "Get Policy", "description": "Get a specific governance policy", "method": "GET", "url": "https://api.example.com/policies/{policyId}", "headers": {"Content-Type": "application/json", "Accept": "application/json"}, "pathParameters": {"policyId": {"type": "string", "label": "Policy ID", "required": true, "description": "ID of the policy to retrieve"}}, "inputSchema": {"type": "object", "properties": {"policyId": {"type": "string", "description": "ID of the policy to retrieve"}}, "required": ["policyId"]}, "outputSchema": {"type": "object", "properties": {"id": {"type": "string", "description": "Policy ID"}, "name": {"type": "string", "description": "Policy name"}, "description": {"type": "string", "description": "Policy description"}, "content": {"type": "string", "description": "Policy content"}, "status": {"type": "string", "description": "Policy status", "enum": ["active", "inactive", "draft"]}, "version": {"type": "string", "description": "Policy version"}, "approvedBy": {"type": "string", "description": "User who approved the policy"}, "approvedAt": {"type": "string", "format": "date-time", "description": "Policy approval date"}, "createdAt": {"type": "string", "format": "date-time", "description": "Policy creation date"}, "updatedAt": {"type": "string", "format": "date-time", "description": "Policy last update date"}}}}, {"id": "createPolicy", "name": "Create Policy", "description": "Create a new governance policy", "method": "POST", "url": "https://api.example.com/policies", "headers": {"Content-Type": "application/json", "Accept": "application/json"}, "bodyParameters": {"name": {"type": "string", "label": "Name", "required": true, "description": "Policy name"}, "description": {"type": "string", "label": "Description", "required": true, "description": "Policy description"}, "content": {"type": "string", "label": "Content", "required": true, "description": "Policy content"}, "status": {"type": "string", "label": "Status", "required": false, "default": "draft", "enum": ["active", "inactive", "draft"], "description": "Policy status"}}, "inputSchema": {"type": "object", "properties": {"name": {"type": "string", "description": "Policy name"}, "description": {"type": "string", "description": "Policy description"}, "content": {"type": "string", "description": "Policy content"}, "status": {"type": "string", "description": "Policy status", "enum": ["active", "inactive", "draft"], "default": "draft"}}, "required": ["name", "description", "content"]}, "outputSchema": {"type": "object", "properties": {"id": {"type": "string", "description": "Policy ID"}, "name": {"type": "string", "description": "Policy name"}, "description": {"type": "string", "description": "Policy description"}, "content": {"type": "string", "description": "Policy content"}, "status": {"type": "string", "description": "Policy status", "enum": ["active", "inactive", "draft"]}, "version": {"type": "string", "description": "Policy version"}, "createdAt": {"type": "string", "format": "date-time", "description": "Policy creation date"}, "updatedAt": {"type": "string", "format": "date-time", "description": "Policy last update date"}}}}]}