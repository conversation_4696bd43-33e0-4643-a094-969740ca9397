import React, { useState } from 'react';
import { 
  Box, 
  Typography, 
  TextField, 
  Grid, 
  Button,
  Paper,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  IconButton,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Divider
} from '@mui/material';
import { 
  Add as AddIcon, 
  Delete as DeleteIcon,
  Schedule as ScheduleIcon,
  Webhook as WebhookIcon
} from '@mui/icons-material';

export default function EventsForm({ connector, updateConnector }) {
  const [activeTab, setActiveTab] = useState(0);
  const [events, setEvents] = useState(connector.events || { webhooks: [], polling: [] });
  const [newWebhook, setNewWebhook] = useState({
    path: '',
    method: 'POST',
    description: '',
    handler: ''
  });
  const [newPolling, setNewPolling] = useState({
    endpoint: '',
    interval: '5m',
    condition: ''
  });

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const handleAddWebhook = () => {
    if (newWebhook.path && newWebhook.handler) {
      const updatedEvents = {
        ...events,
        webhooks: [...events.webhooks, { ...newWebhook }]
      };
      
      setEvents(updatedEvents);
      updateConnector('events', updatedEvents);
      
      // Reset form
      setNewWebhook({
        path: '',
        method: 'POST',
        description: '',
        handler: ''
      });
    }
  };

  const handleDeleteWebhook = (index) => {
    const updatedEvents = {
      ...events,
      webhooks: events.webhooks.filter((_, i) => i !== index)
    };
    
    setEvents(updatedEvents);
    updateConnector('events', updatedEvents);
  };

  const handleAddPolling = () => {
    if (newPolling.endpoint && newPolling.interval) {
      const updatedEvents = {
        ...events,
        polling: [...events.polling, { ...newPolling }]
      };
      
      setEvents(updatedEvents);
      updateConnector('events', updatedEvents);
      
      // Reset form
      setNewPolling({
        endpoint: '',
        interval: '5m',
        condition: ''
      });
    }
  };

  const handleDeletePolling = (index) => {
    const updatedEvents = {
      ...events,
      polling: events.polling.filter((_, i) => i !== index)
    };
    
    setEvents(updatedEvents);
    updateConnector('events', updatedEvents);
  };

  return (
    <Box>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Typography variant="h6" gutterBottom>
            Event Handling
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            Configure how to handle events from the API, either through webhooks or polling.
          </Typography>
          
          <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
            <Tabs value={activeTab} onChange={handleTabChange} aria-label="event handling tabs">
              <Tab label="Webhooks" icon={<WebhookIcon />} iconPosition="start" />
              <Tab label="Polling" icon={<ScheduleIcon />} iconPosition="start" />
            </Tabs>
          </Box>
          
          {activeTab === 0 && (
            <Box>
              {/* Webhooks */}
              <Paper sx={{ p: 3, mb: 3, backgroundColor: 'background.paper' }}>
                <Typography variant="subtitle1" gutterBottom>
                  Add New Webhook
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      label="Webhook Path"
                      value={newWebhook.path}
                      onChange={(e) => setNewWebhook({ ...newWebhook, path: e.target.value })}
                      fullWidth
                      placeholder="/webhook/example"
                      helperText="Path to receive webhook events"
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth>
                      <InputLabel id="webhook-method-label">HTTP Method</InputLabel>
                      <Select
                        labelId="webhook-method-label"
                        value={newWebhook.method}
                        onChange={(e) => setNewWebhook({ ...newWebhook, method: e.target.value })}
                        label="HTTP Method"
                      >
                        <MenuItem value="GET">GET</MenuItem>
                        <MenuItem value="POST">POST</MenuItem>
                        <MenuItem value="PUT">PUT</MenuItem>
                        <MenuItem value="DELETE">DELETE</MenuItem>
                        <MenuItem value="PATCH">PATCH</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      label="Description"
                      value={newWebhook.description}
                      onChange={(e) => setNewWebhook({ ...newWebhook, description: e.target.value })}
                      fullWidth
                      placeholder="Description of the webhook"
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      label="Handler Function"
                      value={newWebhook.handler}
                      onChange={(e) => setNewWebhook({ ...newWebhook, handler: e.target.value })}
                      fullWidth
                      placeholder="e.g., handleExampleWebhook"
                      helperText="Name of the function to handle this webhook"
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <Button
                      variant="outlined"
                      startIcon={<AddIcon />}
                      onClick={handleAddWebhook}
                      disabled={!newWebhook.path || !newWebhook.handler}
                    >
                      Add Webhook
                    </Button>
                  </Grid>
                </Grid>
              </Paper>
              
              {/* Webhooks List */}
              {events.webhooks.length > 0 ? (
                <Paper variant="outlined" sx={{ backgroundColor: 'background.default' }}>
                  <List>
                    {events.webhooks.map((webhook, index) => (
                      <ListItem
                        key={index}
                        sx={{
                          borderBottom: index < events.webhooks.length - 1 ? 1 : 0,
                          borderColor: 'divider'
                        }}
                      >
                        <ListItemText
                          primary={
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <Typography
                                variant="caption"
                                sx={{
                                  backgroundColor: (() => {
                                    switch (webhook.method) {
                                      case 'GET': return 'rgba(0, 128, 0, 0.1)';
                                      case 'POST': return 'rgba(255, 165, 0, 0.1)';
                                      case 'PUT': return 'rgba(0, 0, 255, 0.1)';
                                      case 'DELETE': return 'rgba(255, 0, 0, 0.1)';
                                      default: return 'rgba(128, 128, 128, 0.1)';
                                    }
                                  })(),
                                  color: (() => {
                                    switch (webhook.method) {
                                      case 'GET': return 'green';
                                      case 'POST': return 'orange';
                                      case 'PUT': return 'blue';
                                      case 'DELETE': return 'red';
                                      default: return 'gray';
                                    }
                                  })(),
                                  px: 1,
                                  py: 0.5,
                                  borderRadius: 1,
                                  fontWeight: 'bold',
                                  mr: 1
                                }}
                              >
                                {webhook.method}
                              </Typography>
                              <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                                {webhook.path}
                              </Typography>
                            </Box>
                          }
                          secondary={
                            <React.Fragment>
                              <Typography component="span" variant="body2" color="text.primary">
                                Handler: {webhook.handler}
                              </Typography>
                              {webhook.description && (
                                <Typography component="p" variant="body2" color="text.secondary">
                                  {webhook.description}
                                </Typography>
                              )}
                            </React.Fragment>
                          }
                        />
                        <ListItemSecondaryAction>
                          <IconButton
                            edge="end"
                            aria-label="delete"
                            onClick={() => handleDeleteWebhook(index)}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </ListItemSecondaryAction>
                      </ListItem>
                    ))}
                  </List>
                </Paper>
              ) : (
                <Paper 
                  variant="outlined" 
                  sx={{ 
                    p: 3, 
                    textAlign: 'center',
                    backgroundColor: 'background.default'
                  }}
                >
                  <Typography color="text.secondary">
                    No webhooks defined yet. Add webhooks above.
                  </Typography>
                </Paper>
              )}
            </Box>
          )}
          
          {activeTab === 1 && (
            <Box>
              {/* Polling */}
              <Paper sx={{ p: 3, mb: 3, backgroundColor: 'background.paper' }}>
                <Typography variant="subtitle1" gutterBottom>
                  Add New Polling Configuration
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth>
                      <InputLabel id="polling-endpoint-label">Endpoint</InputLabel>
                      <Select
                        labelId="polling-endpoint-label"
                        value={newPolling.endpoint}
                        onChange={(e) => setNewPolling({ ...newPolling, endpoint: e.target.value })}
                        label="Endpoint"
                      >
                        {connector.endpoints.map((endpoint) => (
                          <MenuItem key={endpoint.id} value={endpoint.id}>
                            {endpoint.name}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      label="Polling Interval"
                      value={newPolling.interval}
                      onChange={(e) => setNewPolling({ ...newPolling, interval: e.target.value })}
                      fullWidth
                      placeholder="e.g., 5m, 1h"
                      helperText="Time between polls (e.g., 5m, 1h, 1d)"
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      label="Condition Function"
                      value={newPolling.condition}
                      onChange={(e) => setNewPolling({ ...newPolling, condition: e.target.value })}
                      fullWidth
                      placeholder="e.g., hasNewFindings"
                      helperText="Name of the function to determine if an event should be triggered"
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <Button
                      variant="outlined"
                      startIcon={<AddIcon />}
                      onClick={handleAddPolling}
                      disabled={!newPolling.endpoint || !newPolling.interval}
                    >
                      Add Polling Configuration
                    </Button>
                  </Grid>
                </Grid>
              </Paper>
              
              {/* Polling List */}
              {events.polling.length > 0 ? (
                <Paper variant="outlined" sx={{ backgroundColor: 'background.default' }}>
                  <List>
                    {events.polling.map((polling, index) => (
                      <ListItem
                        key={index}
                        sx={{
                          borderBottom: index < events.polling.length - 1 ? 1 : 0,
                          borderColor: 'divider'
                        }}
                      >
                        <ListItemText
                          primary={
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <Typography variant="body2">
                                {connector.endpoints.find(e => e.id === polling.endpoint)?.name || polling.endpoint}
                              </Typography>
                              <Typography
                                variant="caption"
                                sx={{
                                  backgroundColor: 'rgba(37, 99, 235, 0.1)',
                                  color: 'primary.main',
                                  px: 1,
                                  py: 0.5,
                                  borderRadius: 1,
                                  fontWeight: 'bold',
                                  ml: 1
                                }}
                              >
                                Every {polling.interval}
                              </Typography>
                            </Box>
                          }
                          secondary={
                            polling.condition ? `Condition: ${polling.condition}` : 'No condition specified'
                          }
                        />
                        <ListItemSecondaryAction>
                          <IconButton
                            edge="end"
                            aria-label="delete"
                            onClick={() => handleDeletePolling(index)}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </ListItemSecondaryAction>
                      </ListItem>
                    ))}
                  </List>
                </Paper>
              ) : (
                <Paper 
                  variant="outlined" 
                  sx={{ 
                    p: 3, 
                    textAlign: 'center',
                    backgroundColor: 'background.default'
                  }}
                >
                  <Typography color="text.secondary">
                    No polling configurations defined yet. Add polling configurations above.
                  </Typography>
                </Paper>
              )}
            </Box>
          )}
        </Grid>
      </Grid>
    </Box>
  );
}
