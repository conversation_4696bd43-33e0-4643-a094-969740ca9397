# Comphyology (Ψᶜ) Quick Start Guide

This quick start guide will help you get up and running with Comphyology visualizations in just a few minutes.

## Prerequisites

- NovaFuse platform installed
- Node.js 14+ and npm 6+
- Basic knowledge of JavaScript and React (for React integration)

## Installation

The Comphyology framework is included in the NovaFuse package. No additional installation is required.

## Basic Usage

### 1. Generate Visualization Schemas

The simplest way to use Comphyology visualizations is to generate NovaVision schemas and render them:

```javascript
// Import NovaVision
const { novaVision } = require('novavision');

// Import Comphyology NovaVision Integration
const { ComphyologyNovaVisionIntegration } = require('comphyology');

// Initialize integration
const comphyologyIntegration = new ComphyologyNovaVisionIntegration({
  novaVision,
  enableLogging: true
});

// Generate visualization schema
const morphologicalSchema = comphyologyIntegration.generateMorphologicalResonanceSchema();

// Render the visualization
novaVision.render(morphologicalSchema, document.getElementById('visualization-container'));
```

### 2. Run the Demo

The Comphyology package includes a demo that showcases all visualizations:

```bash
npm run demo:comphyology
```

This will generate a demo HTML page in the `comphyology_demo` directory. Open the `index.html` file in a browser to view the demo.

### 3. Generate NovaVision Schemas

You can also generate NovaVision schemas for use with the NovaVision rendering system:

```bash
npm run demo:comphyology:novavision
```

This will generate NovaVision schemas in the `comphyology_schemas` directory, along with an HTML example that showcases the schemas.

## Available Visualizations

### 1. Morphological Resonance Field

The Morphological Resonance Field visualization shows how structural complexity interacts with environmental factors.

```javascript
const morphologicalSchema = comphyologyIntegration.generateMorphologicalResonanceSchema({
  resolution: 50,
  complexityRange: 1,
  adaptabilityRange: 1,
  environmentalPressureRange: 1
});
```

### 2. Quantum Phase Space Map

The Quantum Phase Space Map visualization shows the relationship between entropy and phase.

```javascript
const quantumSchema = comphyologyIntegration.generateQuantumPhaseSpaceSchema({
  resolution: 50,
  entropyRange: 1,
  phaseRange: 2 * Math.PI
});
```

### 3. Ethical Tensor Projection

The Ethical Tensor Projection visualization shows how fairness and transparency interact to produce ethical tensor values.

```javascript
const ethicalSchema = comphyologyIntegration.generateEthicalTensorSchema({
  resolution: 50,
  fairnessRange: 1,
  transparencyRange: 1
});
```

### 4. Trinity Integration Diagram

The Trinity Integration Diagram visualization shows how Comphyology enhances the Trinity CSDE architecture.

```javascript
const trinitySchema = comphyologyIntegration.generateTrinityIntegrationSchema();
```

### 5. Comphyology Dashboard

The Comphyology Dashboard provides a comprehensive view of all Comphyology visualizations.

```javascript
const dashboardSchema = comphyologyIntegration.generateComphyologyDashboardSchema();
```

## React Integration

To use Comphyology visualizations in a React application:

```jsx
import React, { useState, useEffect } from 'react';
import { UUICBridge } from 'novavision/react';

// Import NovaVision
const { novaVision } = require('novavision');

// Import Comphyology NovaVision Integration
const { ComphyologyNovaVisionIntegration } = require('comphyology');

const ComphyologyVisualization = () => {
  const [schema, setSchema] = useState(null);
  const [visualizationType, setVisualizationType] = useState('morphological');

  useEffect(() => {
    // Initialize integration
    const comphyologyIntegration = new ComphyologyNovaVisionIntegration({
      novaVision,
      enableLogging: true
    });

    // Generate schema based on visualization type
    let generatedSchema;

    switch (visualizationType) {
      case 'morphological':
        generatedSchema = comphyologyIntegration.generateMorphologicalResonanceSchema();
        break;
      case 'quantum':
        generatedSchema = comphyologyIntegration.generateQuantumPhaseSpaceSchema();
        break;
      case 'ethical':
        generatedSchema = comphyologyIntegration.generateEthicalTensorSchema();
        break;
      case 'trinity':
        generatedSchema = comphyologyIntegration.generateTrinityIntegrationSchema();
        break;
      case 'dashboard':
        generatedSchema = comphyologyIntegration.generateComphyologyDashboardSchema();
        break;
      default:
        generatedSchema = comphyologyIntegration.generateMorphologicalResonanceSchema();
    }

    // Set schema
    setSchema(generatedSchema);
  }, [visualizationType]);

  // Handle visualization type change
  const handleVisualizationTypeChange = (event) => {
    setVisualizationType(event.target.value);
  };

  // Render the UI
  return (
    <div>
      <div>
        <label htmlFor="visualization-type">Visualization Type:</label>
        <select
          id="visualization-type"
          value={visualizationType}
          onChange={handleVisualizationTypeChange}
        >
          <option value="morphological">Morphological Resonance Field</option>
          <option value="quantum">Quantum Phase Space Map</option>
          <option value="ethical">Ethical Tensor Projection</option>
          <option value="trinity">Trinity Integration Diagram</option>
          <option value="dashboard">Comphyology Dashboard</option>
        </select>
      </div>

      {schema && (
        <UUICBridge
          schema={schema}
          onSubmit={(data) => console.log('Form submitted:', data)}
          onChange={(data) => console.log('Form changed:', data)}
          onAction={(action, data) => console.log('Action triggered:', action, data)}
          options={{
            theme: 'default',
            responsive: true,
            accessibilityLevel: 'AA'
          }}
        />
      )}
    </div>
  );
};

export default ComphyologyVisualization;
```

## Handling Events

NovaVision schemas include interactive elements that trigger events. You can handle these events using the `onSubmit`, `onChange`, and `onAction` props:

```jsx
<UUICBridge
  schema={schema}
  onSubmit={(data) => {
    console.log('Form submitted:', data);
    // Handle form submission
  }}
  onChange={(data) => {
    console.log('Form changed:', data);
    // Handle form changes
  }}
  onAction={(action, data) => {
    console.log('Action triggered:', action, data);
    // Handle actions
    if (action === 'updateVisualization') {
      // Update visualization based on data
    }
  }}
/>
```

## Customizing Visualizations

You can customize visualizations by providing options to the schema generation methods:

```javascript
const customMorphologicalSchema = comphyologyIntegration.generateMorphologicalResonanceSchema({
  resolution: 100, // Higher resolution for more detail
  complexityRange: 0.8, // Limit complexity range
  adaptabilityRange: 0.9, // Limit adaptability range
  environmentalPressureRange: 0.7, // Limit environmental pressure range
  performanceMode: 'balanced', // 'high', 'balanced', or 'low'
  useCache: true, // Enable caching
  adaptiveResolution: true // Enable adaptive resolution
});
```

### Performance Optimization

The Comphyology visualization system includes several performance optimizations:

#### Performance Modes

Choose a performance mode based on your needs:

```javascript
// High Performance Mode - fastest, less detail
const highPerfSchema = comphyologyIntegration.generateMorphologicalResonanceSchema({
  performanceMode: 'high'
});

// Balanced Performance Mode - good balance of performance and detail
const balancedPerfSchema = comphyologyIntegration.generateMorphologicalResonanceSchema({
  performanceMode: 'balanced'
});

// Low Performance Mode - maximum detail, slower
const lowPerfSchema = comphyologyIntegration.generateMorphologicalResonanceSchema({
  performanceMode: 'low'
});
```

#### Caching

Enable caching to significantly improve performance for repeated visualization generation:

```javascript
// First generation - normal speed
const schema1 = comphyologyIntegration.generateMorphologicalResonanceSchema({
  useCache: true
});

// Second generation - much faster (up to 49x speedup)
const schema2 = comphyologyIntegration.generateMorphologicalResonanceSchema({
  useCache: true
});
```

#### Adaptive Resolution

Enable adaptive resolution to automatically adjust the resolution based on the performance mode:

```javascript
const schema = comphyologyIntegration.generateMorphologicalResonanceSchema({
  adaptiveResolution: true,
  performanceMode: 'balanced'
});
```

#### Progressive Loading

Enable progressive loading to provide immediate visual feedback while gradually increasing detail:

```javascript
const schema = comphyologyIntegration.generateMorphologicalResonanceSchema({
  progressiveLoading: true, // Enable progressive loading
  progressiveSteps: [10, 20, 35, 50], // Resolution steps
  useCache: true // Enable caching for faster loading
});
```

Progressive loading provides several benefits:

1. **Faster Initial Rendering**: Initial render with low resolution data is up to 10x faster
2. **Continuous Visual Feedback**: Users see continuous updates as higher resolution data loads
3. **Interactive Throughout**: Visualization remains interactive during the loading process
4. **Graceful Degradation**: Works well on slower connections or devices

For the best user experience, combine progressive loading with caching:

```javascript
const schema = comphyologyIntegration.generateMorphologicalResonanceSchema({
  progressiveLoading: true,
  progressiveSteps: [10, 20, 35, 50],
  useCache: true,
  performanceMode: 'balanced',
  adaptiveResolution: true
});
```

#### Web Workers

Enable Web Workers to offload visualization data generation to background threads, preventing UI blocking:

```javascript
const schema = comphyologyIntegration.generateMorphologicalResonanceSchema({
  useWorkers: true, // Enable Web Workers (default: true if supported)
  maxWorkers: 4, // Maximum number of workers to create
});
```

Web Workers provide several benefits:

1. **Non-Blocking UI**: The main thread remains responsive during complex calculations
2. **Parallel Processing**: Multiple visualizations can be generated simultaneously
3. **Improved Performance**: Up to 2.5x speedup for visualization generation
4. **Efficient Resource Usage**: Better utilization of multi-core processors

For the best user experience, combine Web Workers with progressive loading and caching:

```javascript
const schema = comphyologyIntegration.generateMorphologicalResonanceSchema({
  useWorkers: true,
  maxWorkers: 4,
  progressiveLoading: true,
  progressiveSteps: [10, 20, 35, 50],
  useCache: true,
  performanceMode: 'balanced',
  adaptiveResolution: true
});
```

## Using Real Data

You can use real data with Comphyology visualizations by integrating with other NovaFuse components:

```javascript
// Get threat detection data from NovaShield
const threatData = await novaShield.getThreatData();

// Create context data for Quantum State Inference
const contextData = {
  time: { value: new Date().getHours() / 24 },
  location: { value: 0.5 },
  user: { value: 0.5 },
  resource: { value: 0.5 },
  actualThreatStatus: threatData.threatDetected,
  actualThreats: threatData.threats
};

// Process detection with Advanced Comphyology Quantum Inference
const { AdvancedComphyologyQuantumInference } = require('comphyology');
const quantumInference = new AdvancedComphyologyQuantumInference();
const inferenceResult = quantumInference.processDetection(threatData, contextData);

// Generate Quantum Phase Space schema with real data
const quantumSchema = comphyologyIntegration.generateQuantumPhaseSpaceSchema({
  realData: inferenceResult
});

// Render the visualization
novaVision.render(quantumSchema, document.getElementById('visualization-container'));
```

## Next Steps

- Explore the [Comphyology Documentation](README.md) for more information about the Comphyology framework
- Check out the [Technical Reference](TECHNICAL_REFERENCE.md) for detailed information about the Comphyology implementation
- Try the [Examples](../examples) for more advanced usage scenarios
- Integrate Comphyology visualizations with other NovaFuse components

## Troubleshooting

### Visualization Not Rendering

If a visualization is not rendering:

1. Check that the schema is valid
2. Check that the target element exists
3. Check the browser console for errors
4. Try a different visualization type

### Performance Issues

If you experience performance issues:

1. Reduce the resolution of the visualization
2. Simplify the visualization by limiting ranges
3. Use a smaller dataset
4. Enable caching

### Integration Issues

If you have issues integrating with other NovaFuse components:

1. Check that the component is properly initialized
2. Check that the data format is correct
3. Check the component documentation for integration requirements
4. Use the `enableLogging` option to see more detailed logs

## Getting Help

If you need help with Comphyology visualizations:

1. Check the [Comphyology Documentation](README.md)
2. Check the [Technical Reference](TECHNICAL_REFERENCE.md)
3. Check the [NovaFuse Documentation](../README.md)
4. Contact the NovaFuse team
