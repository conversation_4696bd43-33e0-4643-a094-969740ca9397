#!/usr/bin/env python3
"""
Dynamic Weighting Protocol for Ψ Tensor Core

This module implements the dynamic weighting protocol for determining which engine
dominates per cycle in the Ψ Tensor Core.

The weighting formulas are:
w_CSDE = σ(0.18 * G + 0.82 * D)
w_CSFE = σ(0.18 * R + 0.82 * φ)
w_CSME = σ(0.18 * B + 0.82 * Γ)

Where:
- G = Governance score
- D = Data quality score
- R = Risk score
- φ = Financial impact score
- B = Biological risk score
- Γ = Medical compliance score
- σ = Sigmoid function
"""

import torch
import numpy as np
from typing import Dict, List, Tuple, Union, Optional

class DynamicWeightingProtocol:
    """
    Implements the dynamic weighting protocol for the Ψ Tensor Core.
    """
    
    def __init__(self, device: torch.device = None):
        """
        Initialize the dynamic weighting protocol.
        
        Args:
            device: PyTorch device to use
        """
        self.device = device if device is not None else torch.device("cpu")
        
        # Initialize weights
        self.weights = {
            "CSDE": 0.33,
            "CSFE": 0.33,
            "CSME": 0.34
        }
        
        # Initialize history
        self.weight_history = {
            "CSDE": [],
            "CSFE": [],
            "CSME": []
        }
        
        # 18/82 principle constants
        self.LEADING_WEIGHT = 0.18
        self.LAGGING_WEIGHT = 0.82
    
    def calculate_weights(self, 
                         governance: float, 
                         data: float, 
                         risk: float, 
                         finance: float, 
                         bio: float, 
                         med_compliance: float) -> Dict[str, float]:
        """
        Calculate dynamic weights based on the 18/82 principle.
        
        Args:
            governance: Governance score (0-1)
            data: Data quality score (0-1)
            risk: Risk score (0-1)
            finance: Financial impact score (0-1)
            bio: Biological risk score (0-1)
            med_compliance: Medical compliance score (0-1)
            
        Returns:
            Dictionary of weights for each engine
        """
        # Apply 18/82 principle
        w_CSDE_raw = self.LEADING_WEIGHT * governance + self.LAGGING_WEIGHT * data
        w_CSFE_raw = self.LEADING_WEIGHT * risk + self.LAGGING_WEIGHT * finance
        w_CSME_raw = self.LEADING_WEIGHT * bio + self.LAGGING_WEIGHT * med_compliance
        
        # Apply sigmoid function for normalization
        w_CSDE = torch.sigmoid(torch.tensor(w_CSDE_raw, device=self.device)).item()
        w_CSFE = torch.sigmoid(torch.tensor(w_CSFE_raw, device=self.device)).item()
        w_CSME = torch.sigmoid(torch.tensor(w_CSME_raw, device=self.device)).item()
        
        # Normalize weights to sum to 1
        total = w_CSDE + w_CSFE + w_CSME
        w_CSDE /= total
        w_CSFE /= total
        w_CSME /= total
        
        # Update weights
        self.weights = {
            "CSDE": w_CSDE,
            "CSFE": w_CSFE,
            "CSME": w_CSME
        }
        
        # Update history
        self.weight_history["CSDE"].append(w_CSDE)
        self.weight_history["CSFE"].append(w_CSFE)
        self.weight_history["CSME"].append(w_CSME)
        
        return self.weights
    
    def get_weights(self) -> Dict[str, float]:
        """
        Get the current weights.
        
        Returns:
            Dictionary of weights for each engine
        """
        return self.weights
    
    def get_weight_history(self) -> Dict[str, List[float]]:
        """
        Get the weight history.
        
        Returns:
            Dictionary of weight history for each engine
        """
        return self.weight_history
    
    def apply_weights_to_tensor(self, 
                               csde_tensor: torch.Tensor, 
                               csfe_tensor: torch.Tensor, 
                               csme_tensor: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        Apply weights to tensors.
        
        Args:
            csde_tensor: CSDE tensor
            csfe_tensor: CSFE tensor
            csme_tensor: CSME tensor
            
        Returns:
            Tuple of weighted tensors
        """
        # Extract components for weighting
        governance = csde_tensor[0].item()
        data = csde_tensor[1].item()
        risk = csfe_tensor[0].item()
        finance = csfe_tensor[1].item()
        bio = csme_tensor[0].item()
        med_compliance = csme_tensor[1].item()
        
        # Calculate weights
        weights = self.calculate_weights(governance, data, risk, finance, bio, med_compliance)
        
        # Apply weights to tensors
        weighted_csde = csde_tensor * weights["CSDE"]
        weighted_csfe = csfe_tensor * weights["CSFE"]
        weighted_csme = csme_tensor * weights["CSME"]
        
        return weighted_csde, weighted_csfe, weighted_csme
    
    def get_dominant_engine(self) -> str:
        """
        Get the currently dominant engine.
        
        Returns:
            Name of the dominant engine
        """
        max_weight = max(self.weights.values())
        for engine, weight in self.weights.items():
            if weight == max_weight:
                return engine
        
        return "CSDE"  # Default to CSDE if all weights are equal
