"""
Real-time monitoring example for the ComphyonΨᶜ Meter.
"""

import sys
import os
import time
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.animation import FuncAnimation

# Add the parent directory to the path so we can import the package
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.comphyon_meter import ComphyonMeter
from src.comphyon_meter.utils import generate_random_tensor, calculate_safety_status

class RealTimeMonitor:
    """
    Real-time monitor for ComphyonΨᶜ metrics.
    """
    
    def __init__(self, update_interval=500):
        """
        Initialize the real-time monitor.
        
        Args:
            update_interval: Update interval in milliseconds
        """
        self.meter = ComphyonMeter()
        self.history = []
        self.update_interval = update_interval
        self.max_history_length = 100
        
        # Initialize the plot
        self.fig, (self.ax1, self.ax2) = plt.subplots(2, 1, figsize=(10, 8))
        self.fig.suptitle('ComphyonΨᶜ Real-Time Monitoring', fontsize=16)
        
        # Initialize empty lines
        self.velocity_line, = self.ax1.plot([], [], 'b-', linewidth=2)
        self.acceleration_line, = self.ax2.plot([], [], 'r-', linewidth=2)
        
        # Set up the axes
        self.ax1.set_ylabel('Velocity (Cph-Flux)')
        self.ax1.grid(True, alpha=0.3)
        
        self.ax2.set_ylabel('Acceleration (Cph)')
        self.ax2.set_xlabel('Time (s)')
        self.ax2.grid(True, alpha=0.3)
        
        # Add safety thresholds to acceleration plot
        self.ax2.axhline(y=1.0, color='orange', linestyle='--', alpha=0.7, label='Warning')
        self.ax2.axhline(y=2.5, color='red', linestyle='--', alpha=0.7, label='Critical')
        self.ax2.legend()
        
        # Add status text
        self.status_text = self.fig.text(0.02, 0.02, '', fontsize=12)
        
        # Initialize data
        self.times = []
        self.velocities = []
        self.accelerations = []
        self.start_time = time.time()
    
    def update(self, frame):
        """
        Update function for the animation.
        
        Args:
            frame: Frame number (not used)
            
        Returns:
            tuple: Updated artists
        """
        # Generate random tensor data with some trend
        t = time.time() - self.start_time
        
        # Add some oscillation to make it interesting
        oscillation = 0.1 * np.sin(t / 5)
        trend = 0.05 * np.sin(t / 20)
        
        csde_tensor = [0.75 + trend + np.random.normal(0, 0.03), 
                       0.85 + oscillation + np.random.normal(0, 0.03),
                       0.65 + np.random.normal(0, 0.03),
                       0.90 + np.random.normal(0, 0.03)]
        
        csfe_tensor = [0.65 + trend + np.random.normal(0, 0.03),
                       0.70 + oscillation + np.random.normal(0, 0.03),
                       0.80 + np.random.normal(0, 0.03),
                       0.80 + np.random.normal(0, 0.03)]
        
        csme_tensor = [0.70 + np.random.normal(0, 0.03),
                       0.90 + np.random.normal(0, 0.03),
                       0.60 + trend + np.random.normal(0, 0.03),
                       0.85 + oscillation + np.random.normal(0, 0.03)]
        
        # Clip values to valid range
        csde_tensor = [max(0.1, min(0.99, x)) for x in csde_tensor]
        csfe_tensor = [max(0.1, min(0.99, x)) for x in csfe_tensor]
        csme_tensor = [max(0.1, min(0.99, x)) for x in csme_tensor]
        
        # Calculate metrics
        metrics = self.meter.calculate(csde_tensor, csfe_tensor, csme_tensor)
        self.history.append(metrics)
        
        # Limit history length
        if len(self.history) > self.max_history_length:
            self.history.pop(0)
        
        # Update data
        self.times.append(t)
        self.velocities.append(metrics['velocity'])
        self.accelerations.append(metrics['acceleration'])
        
        # Limit data length
        if len(self.times) > self.max_history_length:
            self.times.pop(0)
            self.velocities.pop(0)
            self.accelerations.pop(0)
        
        # Update plots
        self.velocity_line.set_data(self.times, self.velocities)
        self.acceleration_line.set_data(self.times, self.accelerations)
        
        # Adjust axes limits
        self.ax1.set_xlim(min(self.times), max(self.times))
        self.ax1.set_ylim(min(self.velocities) * 0.9, max(self.velocities) * 1.1)
        
        self.ax2.set_xlim(min(self.times), max(self.times))
        self.ax2.set_ylim(0, max(max(self.accelerations) * 1.1, 3.0))
        
        # Update status text
        status, color = calculate_safety_status(metrics['acceleration'])
        self.status_text.set_text(f"Status: {status} | Velocity: {metrics['velocity']:.4f} | Acceleration: {metrics['acceleration']:.4f}")
        self.status_text.set_color(color)
        
        return self.velocity_line, self.acceleration_line, self.status_text
    
    def run(self):
        """
        Run the real-time monitor.
        """
        # Create the animation
        self.animation = FuncAnimation(
            self.fig, self.update, interval=self.update_interval, blit=True)
        
        plt.tight_layout()
        plt.show()

def main():
    """
    Run the real-time monitoring example.
    """
    print("ComphyonΨᶜ Meter - Real-Time Monitoring Example")
    print("===============================================")
    print("Press Ctrl+C to exit")
    
    monitor = RealTimeMonitor(update_interval=500)
    monitor.run()

if __name__ == "__main__":
    main()
