<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>16. 18/82 Principle</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: white;
        }
        .diagram-container {
            width: 1000px;
            height: 800px;
            position: relative;
            border: 2px solid black;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
        }
        .element {
            position: absolute;
            border-radius: 0; /* Square corners for patent diagrams */
            padding: 20px 20px 20px 50px; /* Extra padding on left for number */
            z-index: 2;
            background-color: white;
            border: 2px solid black;
        }
        .element-number {
            position: absolute;
            top: 50%;
            left: 15px;
            transform: translateY(-50%);
            background-color: black;
            color: white;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: bold;
            z-index: 3;
        }
        .connection {
            position: absolute;
            z-index: 1;
            background-color: black;
        }
        h1 {
            text-align: center;
            margin-bottom: 20px;
            font-size: 24px;
        }
        .bold-formula {
            font-weight: bold;
        }
        .pie-chart {
            position: absolute;
            width: 200px;
            height: 200px;
            border-radius: 50%;
            background: conic-gradient(black 0% 18%, #e0e0e0 18% 100%);
            z-index: 2;
        }
        .pie-label {
            position: absolute;
            font-size: 16px;
            font-weight: bold;
            z-index: 3;
        }
        .container-box {
            position: absolute;
            border: 2px solid black;
            z-index: 1;
        }
        .container-label {
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            font-weight: bold;
            font-size: 18px;
            text-align: center;
            background-color: white;
            padding: 0 10px;
            z-index: 2;
        }
    </style>
</head>
<body>
    <h1>16. 18/82 Principle</h1>

    <div class="diagram-container">
        <!-- Main Title -->
        <div class="element" style="top: 50px; left: 250px; width: 500px; font-weight: bold; font-size: 20px; text-align: center;">
            18/82 PRINCIPLE: RESOURCE OPTIMIZATION IMPLEMENTATION
            <div class="element-number">1600</div>
        </div>

        <!-- Principle Definition -->
        <div class="element" style="top: 150px; left: 150px; width: 700px; font-size: 16px; text-align: center;">
            18% of inputs consistently account for 82% of outputs across all domains
            <div class="element-number">1601</div>
        </div>

        <!-- Pie Chart -->
        <div class="pie-chart" style="top: 220px; left: 400px;"></div>
        <div class="pie-label" style="top: 270px; left: 460px; color: white;">18%</div>
        <div class="pie-label" style="top: 320px; left: 520px; color: black;">82%</div>

        <!-- Critical Inputs -->
        <div class="element" style="top: 250px; left: 100px; width: 200px; font-size: 14px;">
            <span style="font-weight: bold;">Critical Inputs</span><br><br>
            18% of Resources<br>
            • Key Indicators<br>
            • Critical Patterns<br>
            • Essential Data Points
            <div class="element-number">1602</div>
        </div>

        <!-- Resource Allocation -->
        <div class="element" style="top: 250px; left: 700px; width: 200px; font-size: 14px;">
            <span style="font-weight: bold;">Resource Allocation</span><br><br>
            Prioritized Processing<br>
            • Computing Power<br>
            • Memory Allocation<br>
            • Processing Time
            <div class="element-number">1603</div>
        </div>

        <!-- Cross-Domain Applications Container -->
        <div class="container-box" style="top: 450px; left: 100px; width: 800px; height: 280px;"></div>
        <div class="container-label">CROSS-DOMAIN APPLICATIONS</div>

        <!-- Cybersecurity -->
        <div class="element" style="top: 480px; left: 120px; width: 230px; font-size: 14px;">
            <span style="font-weight: bold;">Cybersecurity</span><br><br>
            18% of threat indicators predict 82% of attacks
            <div class="element-number">1604</div>
        </div>

        <!-- Compliance -->
        <div class="element" style="top: 480px; left: 380px; width: 230px; font-size: 14px;">
            <span style="font-weight: bold;">Compliance</span><br><br>
            18% of controls satisfy 82% of requirements
            <div class="element-number">1605</div>
        </div>

        <!-- Risk Management -->
        <div class="element" style="top: 480px; left: 640px; width: 230px; font-size: 14px;">
            <span style="font-weight: bold;">Risk Management</span><br><br>
            18% of risk factors account for 82% of impact
            <div class="element-number">1606</div>
        </div>

        <!-- Medicine -->
        <div class="element" style="top: 600px; left: 120px; width: 230px; font-size: 14px;">
            <span style="font-weight: bold;">Medicine</span><br><br>
            18% of biomarkers provide 82% of diagnoses
            <div class="element-number">1607</div>
        </div>

        <!-- Finance -->
        <div class="element" style="top: 600px; left: 380px; width: 230px; font-size: 14px;">
            <span style="font-weight: bold;">Finance</span><br><br>
            18% of indicators predict 82% of market moves
            <div class="element-number">1608</div>
        </div>

        <!-- System Architecture -->
        <div class="element" style="top: 600px; left: 640px; width: 230px; font-size: 14px;">
            <span style="font-weight: bold;">System Architecture</span><br><br>
            18% of components deliver 82% of functionality
            <div class="element-number">1609</div>
        </div>

        <!-- Mathematical Formula -->
        <div class="element" style="top: 720px; left: 300px; width: 400px; font-size: 16px; text-align: center;">
            <span class="bold-formula">Output = 0.82 × (Top 0.18 Inputs)</span>
            <div class="element-number">1610</div>
        </div>

        <!-- Connections -->
        <!-- Title to Definition -->
        <div class="connection" style="top: 110px; left: 500px; width: 2px; height: 40px;"></div>

        <!-- Definition to Pie Chart -->
        <div class="connection" style="top: 190px; left: 500px; width: 2px; height: 30px;"></div>

        <!-- Pie Chart to Critical Inputs -->
        <div class="connection" style="top: 320px; left: 400px; width: 100px; height: 2px;"></div>

        <!-- Pie Chart to Resource Allocation -->
        <div class="connection" style="top: 320px; left: 600px; width: 100px; height: 2px;"></div>

        <!-- Pie Chart to Cross-Domain Applications -->
        <div class="connection" style="top: 420px; left: 500px; width: 2px; height: 30px;"></div>

        <!-- Cross-Domain to Formula -->
        <div class="connection" style="top: 730px; left: 500px; width: 2px; height: 30px;"></div>
    </div>
</body>
</html>
