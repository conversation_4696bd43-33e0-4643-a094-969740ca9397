/**
 * Integration tests for the Business Intelligence & Workflow Connector
 */

const nock = require('nock');
const BusinessIntelligenceWorkflowConnector = require('../../../../connector/implementations/business-intelligence-workflow');

// Mock logger
jest.mock('../../../../utils/logger', () => ({
  createLogger: jest.fn(() => ({
    info: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn()
  }))
}));

describe('BusinessIntelligenceWorkflowConnector Integration', () => {
  let connector;
  const baseUrl = 'https://api.test.com';
  
  beforeAll(() => {
    // Disable real HTTP requests
    nock.disableNetConnect();
  });
  
  afterAll(() => {
    // Enable real HTTP requests
    nock.enableNetConnect();
  });
  
  beforeEach(() => {
    // Reset nock
    nock.cleanAll();
    
    // Create connector instance
    connector = new BusinessIntelligenceWorkflowConnector({
      baseUrl
    }, {
      clientId: 'test-client-id',
      clientSecret: 'test-client-secret',
      redirectUri: 'https://test-redirect.com'
    });
    
    // Mock authentication
    nock(baseUrl)
      .post('/oauth2/token')
      .reply(200, {
        access_token: 'test-access-token',
        expires_in: 3600
      });
  });
  
  describe('Dashboard Management', () => {
    it('should list dashboards', async () => {
      // Mock dashboards endpoint
      const mockDashboards = {
        data: [
          {
            id: 'dashboard-1',
            name: 'Financial Overview',
            folder: 'Finance',
            owner: '<EMAIL>'
          },
          {
            id: 'dashboard-2',
            name: 'Sales Performance',
            folder: 'Finance',
            owner: '<EMAIL>'
          }
        ],
        pagination: {
          page: 1,
          limit: 20,
          totalItems: 2,
          totalPages: 1
        }
      };
      
      nock(baseUrl)
        .get('/dashboards')
        .query({ folder: 'Finance' })
        .reply(200, mockDashboards);
      
      // Initialize connector
      await connector.initialize();
      
      // List dashboards
      const result = await connector.listDashboards({ folder: 'Finance' });
      
      // Verify result
      expect(result).toEqual(mockDashboards);
    });
    
    it('should get a specific dashboard', async () => {
      // Mock dashboard endpoint
      const mockDashboard = {
        id: 'dashboard-123',
        name: 'Financial Overview',
        description: 'Financial KPIs and metrics',
        folder: 'Finance',
        owner: '<EMAIL>',
        widgets: [
          {
            id: 'widget-1',
            type: 'chart',
            title: 'Revenue by Quarter'
          }
        ]
      };
      
      nock(baseUrl)
        .get('/dashboards/dashboard-123')
        .reply(200, mockDashboard);
      
      // Initialize connector
      await connector.initialize();
      
      // Get dashboard
      const result = await connector.getDashboard('dashboard-123');
      
      // Verify result
      expect(result).toEqual(mockDashboard);
    });
    
    it('should create a new dashboard', async () => {
      // Dashboard data
      const dashboardData = {
        name: 'New Dashboard',
        description: 'New dashboard description',
        folder: 'Finance'
      };
      
      // Mock response
      const mockResponse = {
        id: 'dashboard-new',
        ...dashboardData,
        owner: '<EMAIL>',
        createdAt: '2023-06-15T10:30:00Z',
        updatedAt: '2023-06-15T10:30:00Z'
      };
      
      nock(baseUrl)
        .post('/dashboards', dashboardData)
        .reply(201, mockResponse);
      
      // Initialize connector
      await connector.initialize();
      
      // Create dashboard
      const result = await connector.createDashboard(dashboardData);
      
      // Verify result
      expect(result).toEqual(mockResponse);
    });
    
    it('should update an existing dashboard', async () => {
      // Dashboard update data
      const dashboardId = 'dashboard-123';
      const updateData = {
        name: 'Updated Dashboard',
        description: 'Updated description'
      };
      
      // Mock response
      const mockResponse = {
        id: dashboardId,
        name: 'Updated Dashboard',
        description: 'Updated description',
        folder: 'Finance',
        owner: '<EMAIL>',
        updatedAt: '2023-06-15T11:45:00Z'
      };
      
      nock(baseUrl)
        .put(`/dashboards/${dashboardId}`, updateData)
        .reply(200, mockResponse);
      
      // Initialize connector
      await connector.initialize();
      
      // Update dashboard
      const result = await connector.updateDashboard(dashboardId, updateData);
      
      // Verify result
      expect(result).toEqual(mockResponse);
    });
    
    it('should delete a dashboard', async () => {
      // Dashboard ID
      const dashboardId = 'dashboard-123';
      
      nock(baseUrl)
        .delete(`/dashboards/${dashboardId}`)
        .reply(204);
      
      // Initialize connector
      await connector.initialize();
      
      // Delete dashboard
      await connector.deleteDashboard(dashboardId);
      
      // If no error is thrown, the test passes
      expect(true).toBe(true);
    });
  });
  
  describe('Report Management', () => {
    it('should list reports', async () => {
      // Mock reports endpoint
      const mockReports = {
        data: [
          {
            id: 'report-1',
            name: 'Quarterly Financial Report',
            folder: 'Finance',
            owner: '<EMAIL>'
          },
          {
            id: 'report-2',
            name: 'Sales by Region',
            folder: 'Finance',
            owner: '<EMAIL>'
          }
        ],
        pagination: {
          page: 1,
          limit: 20,
          totalItems: 2,
          totalPages: 1
        }
      };
      
      nock(baseUrl)
        .get('/reports')
        .query({ folder: 'Finance' })
        .reply(200, mockReports);
      
      // Initialize connector
      await connector.initialize();
      
      // List reports
      const result = await connector.listReports({ folder: 'Finance' });
      
      // Verify result
      expect(result).toEqual(mockReports);
    });
    
    it('should get a specific report', async () => {
      // Mock report endpoint
      const mockReport = {
        id: 'report-123',
        name: 'Quarterly Financial Report',
        description: 'Detailed financial analysis by quarter',
        folder: 'Finance',
        owner: '<EMAIL>',
        query: 'SELECT * FROM financial_data WHERE quarter = :quarter AND year = :year',
        parameters: [
          {
            name: 'quarter',
            type: 'string',
            defaultValue: 'Q1'
          },
          {
            name: 'year',
            type: 'integer',
            defaultValue: '2023'
          }
        ]
      };
      
      nock(baseUrl)
        .get('/reports/report-123')
        .reply(200, mockReport);
      
      // Initialize connector
      await connector.initialize();
      
      // Get report
      const result = await connector.getReport('report-123');
      
      // Verify result
      expect(result).toEqual(mockReport);
    });
    
    it('should execute a report', async () => {
      // Report ID
      const reportId = 'report-123';
      
      // Execution options
      const options = {
        parameters: {
          quarter: 'Q1',
          year: 2023
        },
        format: 'json'
      };
      
      // Mock response
      const mockResponse = {
        executionId: 'exec-123',
        status: 'success',
        data: [
          {
            region: 'North America',
            revenue: 1250000,
            expenses: 750000,
            profit: 500000
          },
          {
            region: 'Europe',
            revenue: 980000,
            expenses: 620000,
            profit: 360000
          }
        ],
        executedAt: '2023-06-01T10:15:30Z'
      };
      
      nock(baseUrl)
        .post(`/reports/${reportId}/execute`, options)
        .reply(200, mockResponse);
      
      // Initialize connector
      await connector.initialize();
      
      // Execute report
      const result = await connector.executeReport(reportId, options);
      
      // Verify result
      expect(result).toEqual(mockResponse);
    });
  });
  
  describe('Workflow Management', () => {
    it('should list workflows', async () => {
      // Mock workflows endpoint
      const mockWorkflows = {
        data: [
          {
            id: 'workflow-1',
            name: 'Invoice Approval',
            status: 'active',
            category: 'Finance'
          },
          {
            id: 'workflow-2',
            name: 'Expense Report Processing',
            status: 'active',
            category: 'Finance'
          }
        ],
        pagination: {
          page: 1,
          limit: 20,
          totalItems: 2,
          totalPages: 1
        }
      };
      
      nock(baseUrl)
        .get('/workflows')
        .query({ status: 'active', category: 'Finance' })
        .reply(200, mockWorkflows);
      
      // Initialize connector
      await connector.initialize();
      
      // List workflows
      const result = await connector.listWorkflows({ status: 'active', category: 'Finance' });
      
      // Verify result
      expect(result).toEqual(mockWorkflows);
    });
    
    it('should get a specific workflow', async () => {
      // Mock workflow endpoint
      const mockWorkflow = {
        id: 'workflow-123',
        name: 'Invoice Approval',
        description: 'Workflow for approving invoices',
        status: 'active',
        category: 'Finance',
        trigger: {
          type: 'event',
          config: {
            event: 'invoice.created'
          }
        },
        steps: [
          {
            id: 'step-1',
            name: 'Validate Invoice',
            type: 'script',
            config: {
              script: 'validateInvoice'
            },
            nextSteps: [
              {
                stepId: 'step-2',
                condition: 'invoice.valid === true'
              }
            ]
          },
          {
            id: 'step-2',
            name: 'Approve Invoice',
            type: 'approval',
            config: {
              approvers: ['finance-manager']
            }
          }
        ]
      };
      
      nock(baseUrl)
        .get('/workflows/workflow-123')
        .reply(200, mockWorkflow);
      
      // Initialize connector
      await connector.initialize();
      
      // Get workflow
      const result = await connector.getWorkflow('workflow-123');
      
      // Verify result
      expect(result).toEqual(mockWorkflow);
    });
    
    it('should execute a workflow', async () => {
      // Workflow ID
      const workflowId = 'workflow-123';
      
      // Execution options
      const options = {
        input: {
          invoiceId: 'INV-12345',
          amount: 1500,
          vendor: 'Acme Corp'
        },
        async: true
      };
      
      // Mock response
      const mockResponse = {
        executionId: 'exec-123',
        status: 'queued',
        startTime: '2023-06-01T10:15:30Z',
        statusUrl: `https://api.test.com/workflows/${workflowId}/executions/exec-123`
      };
      
      nock(baseUrl)
        .post(`/workflows/${workflowId}/execute`, options)
        .reply(202, mockResponse);
      
      // Initialize connector
      await connector.initialize();
      
      // Execute workflow
      const result = await connector.executeWorkflow(workflowId, options);
      
      // Verify result
      expect(result).toEqual(mockResponse);
    });
  });
  
  describe('Error Handling', () => {
    it('should handle authentication errors', async () => {
      // Clean previous nock mocks
      nock.cleanAll();
      
      // Mock authentication error
      nock(baseUrl)
        .post('/oauth2/token')
        .reply(401, {
          error: 'invalid_client',
          error_description: 'Invalid client credentials'
        });
      
      // Try to initialize connector
      await expect(connector.initialize()).rejects.toThrow('Authentication failed');
    });
    
    it('should handle not found errors', async () => {
      // Mock not found error
      nock(baseUrl)
        .get('/dashboards/non-existent')
        .reply(404, {
          error: 'not_found',
          error_description: 'Dashboard not found'
        });
      
      // Initialize connector
      await connector.initialize();
      
      // Try to get non-existent dashboard
      await expect(connector.getDashboard('non-existent')).rejects.toThrow('Error getting dashboard');
    });
    
    it('should handle validation errors', async () => {
      // Dashboard data with missing required fields
      const invalidData = {
        description: 'Invalid Dashboard'
        // Missing required field: name
      };
      
      // Mock validation error
      nock(baseUrl)
        .post('/dashboards', invalidData)
        .reply(400, {
          error: 'validation_error',
          error_description: 'Validation failed',
          errors: [
            { field: 'name', message: 'Name is required' }
          ]
        });
      
      // Initialize connector
      await connector.initialize();
      
      // Try to create invalid dashboard
      await expect(connector.createDashboard(invalidData)).rejects.toThrow('name is required');
    });
  });
});
