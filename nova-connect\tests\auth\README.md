# NovaFuse Universal API Connector - Authentication Manager Tests

This directory contains tests for the authentication manager of the NovaFuse Universal API Connector (UAC).

## Structure

- `authentication-manager.test.js` - Tests for the authentication manager

## Running Tests

To run all tests:

```bash
npm test
```

To run only authentication manager tests:

```bash
npx jest tests/auth
```

## Test Coverage

The tests aim to achieve at least 80% code coverage for the authentication manager. This includes:

- Credential storage and retrieval
- Request authentication for different authentication types
- Connection testing
- Error handling

## Mocking

The tests use <PERSON><PERSON>'s mocking capabilities to mock:

- Credential repository
- Axios for HTTP requests
- Encryption/decryption functions

This allows the tests to run without requiring real API endpoints.

## Test Environment

The tests run in a Node.js environment with the following configuration:

- `NODE_ENV` set to `test`
- `CREDENTIALS_ENCRYPTION_KEY` set to a test key

## Authentication Types Tested

The tests cover the following authentication types:

- API Key authentication
- Basic authentication
- OAuth2 authentication
- JWT authentication
- AWS Signature V4 authentication
- No authentication

## Adding New Tests

When adding new functionality to the authentication manager, please add corresponding tests. Follow these guidelines:

1. Create a new test case in the authentication-manager.test.js file
2. Use descriptive test names that explain what is being tested
3. Mock external dependencies
4. Test both success and failure cases
5. Test edge cases and error handling
