/**
 * Role Permission Service
 * 
 * This service handles role-based permissions for users.
 */

const fs = require('fs').promises;
const path = require('path');
const { ValidationError, NotFoundError, AuthorizationError } = require('../utils/errors');

class RolePermissionService {
  constructor(dataDir = path.join(__dirname, '../data')) {
    this.dataDir = dataDir;
    this.rolesFile = path.join(this.dataDir, 'roles.json');
    this.userRolesFile = path.join(this.dataDir, 'user_roles.json');
    this.permissionsFile = path.join(this.dataDir, 'permissions.json');
    this.rolePermissionsFile = path.join(this.dataDir, 'role_permissions.json');
    
    // Define system roles
    this.systemRoles = [
      {
        id: 'admin',
        name: 'Administrator',
        description: 'Full system access',
        isSystem: true,
        created: new Date().toISOString(),
        updated: new Date().toISOString()
      },
      {
        id: 'user',
        name: 'User',
        description: 'Standard user access',
        isSystem: true,
        created: new Date().toISOString(),
        updated: new Date().toISOString()
      }
    ];
    
    // Define system permissions
    this.systemPermissions = [
      // User management permissions
      {
        id: 'user:create',
        name: 'Create User',
        description: 'Create new users',
        category: 'user',
        created: new Date().toISOString(),
        updated: new Date().toISOString()
      },
      {
        id: 'user:read',
        name: 'Read User',
        description: 'View user details',
        category: 'user',
        created: new Date().toISOString(),
        updated: new Date().toISOString()
      },
      {
        id: 'user:update',
        name: 'Update User',
        description: 'Update user details',
        category: 'user',
        created: new Date().toISOString(),
        updated: new Date().toISOString()
      },
      {
        id: 'user:delete',
        name: 'Delete User',
        description: 'Delete users',
        category: 'user',
        created: new Date().toISOString(),
        updated: new Date().toISOString()
      },
      
      // Role management permissions
      {
        id: 'role:create',
        name: 'Create Role',
        description: 'Create new roles',
        category: 'role',
        created: new Date().toISOString(),
        updated: new Date().toISOString()
      },
      {
        id: 'role:read',
        name: 'Read Role',
        description: 'View role details',
        category: 'role',
        created: new Date().toISOString(),
        updated: new Date().toISOString()
      },
      {
        id: 'role:update',
        name: 'Update Role',
        description: 'Update role details',
        category: 'role',
        created: new Date().toISOString(),
        updated: new Date().toISOString()
      },
      {
        id: 'role:delete',
        name: 'Delete Role',
        description: 'Delete roles',
        category: 'role',
        created: new Date().toISOString(),
        updated: new Date().toISOString()
      },
      {
        id: 'role:assign',
        name: 'Assign Role',
        description: 'Assign roles to users',
        category: 'role',
        created: new Date().toISOString(),
        updated: new Date().toISOString()
      },
      
      // Team management permissions
      {
        id: 'team:create',
        name: 'Create Team',
        description: 'Create new teams',
        category: 'team',
        created: new Date().toISOString(),
        updated: new Date().toISOString()
      },
      {
        id: 'team:read',
        name: 'Read Team',
        description: 'View team details',
        category: 'team',
        created: new Date().toISOString(),
        updated: new Date().toISOString()
      },
      {
        id: 'team:update',
        name: 'Update Team',
        description: 'Update team details',
        category: 'team',
        created: new Date().toISOString(),
        updated: new Date().toISOString()
      },
      {
        id: 'team:delete',
        name: 'Delete Team',
        description: 'Delete teams',
        category: 'team',
        created: new Date().toISOString(),
        updated: new Date().toISOString()
      },
      {
        id: 'team:invite',
        name: 'Invite to Team',
        description: 'Invite users to teams',
        category: 'team',
        created: new Date().toISOString(),
        updated: new Date().toISOString()
      },
      
      // Connector permissions
      {
        id: 'connector:create',
        name: 'Create Connector',
        description: 'Create new connectors',
        category: 'connector',
        created: new Date().toISOString(),
        updated: new Date().toISOString()
      },
      {
        id: 'connector:read',
        name: 'Read Connector',
        description: 'View connector details',
        category: 'connector',
        created: new Date().toISOString(),
        updated: new Date().toISOString()
      },
      {
        id: 'connector:update',
        name: 'Update Connector',
        description: 'Update connector details',
        category: 'connector',
        created: new Date().toISOString(),
        updated: new Date().toISOString()
      },
      {
        id: 'connector:delete',
        name: 'Delete Connector',
        description: 'Delete connectors',
        category: 'connector',
        created: new Date().toISOString(),
        updated: new Date().toISOString()
      },
      {
        id: 'connector:execute',
        name: 'Execute Connector',
        description: 'Execute connector operations',
        category: 'connector',
        created: new Date().toISOString(),
        updated: new Date().toISOString()
      },
      
      // Environment permissions
      {
        id: 'environment:create',
        name: 'Create Environment',
        description: 'Create new environments',
        category: 'environment',
        created: new Date().toISOString(),
        updated: new Date().toISOString()
      },
      {
        id: 'environment:read',
        name: 'Read Environment',
        description: 'View environment details',
        category: 'environment',
        created: new Date().toISOString(),
        updated: new Date().toISOString()
      },
      {
        id: 'environment:update',
        name: 'Update Environment',
        description: 'Update environment details',
        category: 'environment',
        created: new Date().toISOString(),
        updated: new Date().toISOString()
      },
      {
        id: 'environment:delete',
        name: 'Delete Environment',
        description: 'Delete environments',
        category: 'environment',
        created: new Date().toISOString(),
        updated: new Date().toISOString()
      },
      {
        id: 'environment:promote',
        name: 'Promote Environment',
        description: 'Promote between environments',
        category: 'environment',
        created: new Date().toISOString(),
        updated: new Date().toISOString()
      },
      
      // Analytics permissions
      {
        id: 'analytics:view',
        name: 'View Analytics',
        description: 'View analytics data',
        category: 'analytics',
        created: new Date().toISOString(),
        updated: new Date().toISOString()
      },
      
      // System permissions
      {
        id: 'system:settings',
        name: 'System Settings',
        description: 'Manage system settings',
        category: 'system',
        created: new Date().toISOString(),
        updated: new Date().toISOString()
      },
      {
        id: 'system:audit',
        name: 'View Audit Logs',
        description: 'View system audit logs',
        category: 'system',
        created: new Date().toISOString(),
        updated: new Date().toISOString()
      }
    ];
    
    // Define default role permissions
    this.defaultRolePermissions = [
      // Admin role permissions (all permissions)
      ...this.systemPermissions.map(permission => ({
        roleId: 'admin',
        permissionId: permission.id,
        created: new Date().toISOString(),
        updated: new Date().toISOString()
      })),
      
      // User role permissions (limited permissions)
      {
        roleId: 'user',
        permissionId: 'user:read',
        created: new Date().toISOString(),
        updated: new Date().toISOString()
      },
      {
        roleId: 'user',
        permissionId: 'team:read',
        created: new Date().toISOString(),
        updated: new Date().toISOString()
      },
      {
        roleId: 'user',
        permissionId: 'connector:read',
        created: new Date().toISOString(),
        updated: new Date().toISOString()
      },
      {
        roleId: 'user',
        permissionId: 'connector:execute',
        created: new Date().toISOString(),
        updated: new Date().toISOString()
      },
      {
        roleId: 'user',
        permissionId: 'environment:read',
        created: new Date().toISOString(),
        updated: new Date().toISOString()
      }
    ];
    
    this.ensureDataDir();
  }

  /**
   * Ensure the data directory exists
   */
  async ensureDataDir() {
    try {
      await fs.mkdir(this.dataDir, { recursive: true });
      
      // Initialize files if they don't exist
      await this.initializeFile(this.rolesFile, this.systemRoles);
      await this.initializeFile(this.userRolesFile, []);
      await this.initializeFile(this.permissionsFile, this.systemPermissions);
      await this.initializeFile(this.rolePermissionsFile, this.defaultRolePermissions);
    } catch (error) {
      console.error('Error creating data directory:', error);
      throw error;
    }
  }

  /**
   * Initialize a file with default data if it doesn't exist
   */
  async initializeFile(filePath, defaultData) {
    try {
      await fs.access(filePath);
    } catch (error) {
      if (error.code === 'ENOENT') {
        // File doesn't exist, create it with default data
        await fs.writeFile(filePath, JSON.stringify(defaultData, null, 2));
      } else {
        throw error;
      }
    }
  }

  /**
   * Load data from file
   */
  async loadData(filePath) {
    try {
      const data = await fs.readFile(filePath, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      if (error.code === 'ENOENT') {
        // File doesn't exist, return empty array
        return [];
      }
      console.error(`Error loading data from ${filePath}:`, error);
      throw error;
    }
  }

  /**
   * Save data to file
   */
  async saveData(filePath, data) {
    try {
      await fs.writeFile(filePath, JSON.stringify(data, null, 2));
    } catch (error) {
      console.error(`Error saving data to ${filePath}:`, error);
      throw error;
    }
  }

  /**
   * Get all roles
   */
  async getAllRoles() {
    return this.loadData(this.rolesFile);
  }

  /**
   * Get role by ID
   */
  async getRoleById(id) {
    const roles = await this.loadData(this.rolesFile);
    const role = roles.find(r => r.id === id);
    
    if (!role) {
      throw new NotFoundError(`Role with ID ${id} not found`);
    }
    
    return role;
  }

  /**
   * Create a new role
   */
  async createRole(data) {
    if (!data.name) {
      throw new ValidationError('Role name is required');
    }
    
    const roles = await this.loadData(this.rolesFile);
    
    // Generate ID from name if not provided
    const id = data.id || data.name.toLowerCase().replace(/[^a-z0-9]/g, '-');
    
    // Check if ID already exists
    if (roles.some(r => r.id === id)) {
      throw new ValidationError(`Role with ID ${id} already exists`);
    }
    
    // Create new role
    const newRole = {
      id,
      name: data.name,
      description: data.description || '',
      isSystem: false,
      created: new Date().toISOString(),
      updated: new Date().toISOString()
    };
    
    roles.push(newRole);
    await this.saveData(this.rolesFile, roles);
    
    return newRole;
  }

  /**
   * Update a role
   */
  async updateRole(id, data) {
    const roles = await this.loadData(this.rolesFile);
    const index = roles.findIndex(r => r.id === id);
    
    if (index === -1) {
      throw new NotFoundError(`Role with ID ${id} not found`);
    }
    
    // Check if trying to update a system role
    if (roles[index].isSystem) {
      throw new ValidationError('Cannot update system roles');
    }
    
    // Update role
    const updatedRole = {
      ...roles[index],
      ...data,
      id, // Don't allow changing the ID
      isSystem: false, // Ensure it's not marked as a system role
      updated: new Date().toISOString()
    };
    
    roles[index] = updatedRole;
    await this.saveData(this.rolesFile, roles);
    
    return updatedRole;
  }

  /**
   * Delete a role
   */
  async deleteRole(id) {
    const roles = await this.loadData(this.rolesFile);
    const index = roles.findIndex(r => r.id === id);
    
    if (index === -1) {
      throw new NotFoundError(`Role with ID ${id} not found`);
    }
    
    // Check if trying to delete a system role
    if (roles[index].isSystem) {
      throw new ValidationError('Cannot delete system roles');
    }
    
    // Remove the role
    roles.splice(index, 1);
    await this.saveData(this.rolesFile, roles);
    
    // Remove all role permissions
    const rolePermissions = await this.loadData(this.rolePermissionsFile);
    const updatedRolePermissions = rolePermissions.filter(rp => rp.roleId !== id);
    await this.saveData(this.rolePermissionsFile, updatedRolePermissions);
    
    // Remove all user roles for this role
    const userRoles = await this.loadData(this.userRolesFile);
    const updatedUserRoles = userRoles.filter(ur => ur.roleId !== id);
    await this.saveData(this.userRolesFile, updatedUserRoles);
    
    return { success: true, message: `Role ${id} deleted` };
  }

  /**
   * Get all permissions
   */
  async getAllPermissions() {
    return this.loadData(this.permissionsFile);
  }

  /**
   * Get permission by ID
   */
  async getPermissionById(id) {
    const permissions = await this.loadData(this.permissionsFile);
    const permission = permissions.find(p => p.id === id);
    
    if (!permission) {
      throw new NotFoundError(`Permission with ID ${id} not found`);
    }
    
    return permission;
  }

  /**
   * Get permissions for a role
   */
  async getPermissionsForRole(roleId) {
    const rolePermissions = await this.loadData(this.rolePermissionsFile);
    const permissionIds = rolePermissions
      .filter(rp => rp.roleId === roleId)
      .map(rp => rp.permissionId);
    
    if (permissionIds.length === 0) {
      return [];
    }
    
    const permissions = await this.loadData(this.permissionsFile);
    return permissions.filter(permission => permissionIds.includes(permission.id));
  }

  /**
   * Add permission to role
   */
  async addPermissionToRole(roleId, permissionId) {
    // Check if role exists
    await this.getRoleById(roleId);
    
    // Check if permission exists
    await this.getPermissionById(permissionId);
    
    const rolePermissions = await this.loadData(this.rolePermissionsFile);
    
    // Check if permission is already assigned to role
    const existingRolePermission = rolePermissions.find(rp => 
      rp.roleId === roleId && rp.permissionId === permissionId
    );
    
    if (existingRolePermission) {
      return existingRolePermission;
    }
    
    // Add permission to role
    const newRolePermission = {
      roleId,
      permissionId,
      created: new Date().toISOString(),
      updated: new Date().toISOString()
    };
    
    rolePermissions.push(newRolePermission);
    await this.saveData(this.rolePermissionsFile, rolePermissions);
    
    return newRolePermission;
  }

  /**
   * Remove permission from role
   */
  async removePermissionFromRole(roleId, permissionId) {
    const rolePermissions = await this.loadData(this.rolePermissionsFile);
    const index = rolePermissions.findIndex(rp => 
      rp.roleId === roleId && rp.permissionId === permissionId
    );
    
    if (index === -1) {
      throw new NotFoundError(`Permission ${permissionId} not found for role ${roleId}`);
    }
    
    // Remove the role permission
    rolePermissions.splice(index, 1);
    await this.saveData(this.rolePermissionsFile, rolePermissions);
    
    return { success: true, message: `Permission ${permissionId} removed from role ${roleId}` };
  }

  /**
   * Get roles for a user
   */
  async getRolesForUser(userId) {
    const userRoles = await this.loadData(this.userRolesFile);
    const roleIds = userRoles
      .filter(ur => ur.userId === userId)
      .map(ur => ur.roleId);
    
    if (roleIds.length === 0) {
      return [];
    }
    
    const roles = await this.loadData(this.rolesFile);
    return roles.filter(role => roleIds.includes(role.id));
  }

  /**
   * Assign role to user
   */
  async assignRoleToUser(userId, roleId) {
    // Check if role exists
    await this.getRoleById(roleId);
    
    const userRoles = await this.loadData(this.userRolesFile);
    
    // Check if user already has this role
    const existingUserRole = userRoles.find(ur => 
      ur.userId === userId && ur.roleId === roleId
    );
    
    if (existingUserRole) {
      return existingUserRole;
    }
    
    // Assign role to user
    const newUserRole = {
      userId,
      roleId,
      created: new Date().toISOString(),
      updated: new Date().toISOString()
    };
    
    userRoles.push(newUserRole);
    await this.saveData(this.userRolesFile, userRoles);
    
    return newUserRole;
  }

  /**
   * Remove role from user
   */
  async removeRoleFromUser(userId, roleId) {
    const userRoles = await this.loadData(this.userRolesFile);
    const index = userRoles.findIndex(ur => 
      ur.userId === userId && ur.roleId === roleId
    );
    
    if (index === -1) {
      throw new NotFoundError(`Role ${roleId} not found for user ${userId}`);
    }
    
    // Remove the user role
    userRoles.splice(index, 1);
    await this.saveData(this.userRolesFile, userRoles);
    
    return { success: true, message: `Role ${roleId} removed from user ${userId}` };
  }

  /**
   * Check if user has a specific role
   */
  async userHasRole(userId, roleId) {
    const userRoles = await this.loadData(this.userRolesFile);
    return userRoles.some(ur => ur.userId === userId && ur.roleId === roleId);
  }

  /**
   * Check if user has a specific permission
   */
  async userHasPermission(userId, permissionId) {
    // Get user roles
    const userRoles = await this.loadData(this.userRolesFile);
    const roleIds = userRoles
      .filter(ur => ur.userId === userId)
      .map(ur => ur.roleId);
    
    if (roleIds.length === 0) {
      return false;
    }
    
    // Get role permissions
    const rolePermissions = await this.loadData(this.rolePermissionsFile);
    
    // Check if any of the user's roles has the permission
    return rolePermissions.some(rp => 
      roleIds.includes(rp.roleId) && rp.permissionId === permissionId
    );
  }

  /**
   * Get all permissions for a user
   */
  async getPermissionsForUser(userId) {
    // Get user roles
    const userRoles = await this.loadData(this.userRolesFile);
    const roleIds = userRoles
      .filter(ur => ur.userId === userId)
      .map(ur => ur.roleId);
    
    if (roleIds.length === 0) {
      return [];
    }
    
    // Get role permissions
    const rolePermissions = await this.loadData(this.rolePermissionsFile);
    const permissionIds = rolePermissions
      .filter(rp => roleIds.includes(rp.roleId))
      .map(rp => rp.permissionId);
    
    if (permissionIds.length === 0) {
      return [];
    }
    
    // Get permissions
    const permissions = await this.loadData(this.permissionsFile);
    return permissions.filter(permission => permissionIds.includes(permission.id));
  }

  /**
   * Ensure user has default role
   */
  async ensureUserHasDefaultRole(userId) {
    // Check if user has any roles
    const userRoles = await this.getRolesForUser(userId);
    
    if (userRoles.length === 0) {
      // Assign default 'user' role
      await this.assignRoleToUser(userId, 'user');
    }
  }
}

module.exports = RolePermissionService;
