/**
 * Node Selector with 18/82 Principle
 * 
 * This module implements the 18/82 Principle for node selection in the
 * Hybrid DAG-based Zero-Knowledge System. The principle states that
 * 18% of nodes handle 82% of the critical operations, optimizing
 * performance and resource allocation.
 * 
 * The 18/82 Principle is aligned with the Comphyology framework and
 * provides efficient node selection for DAG operations.
 */

const crypto = require('crypto');
const { v4: uuidv4 } = require('uuid');
const debug = require('debug')('nova:node-selector');

/**
 * Node Selector implementing the 18/82 Principle
 * @class NodeSelector
 */
class NodeSelector {
  /**
   * Create a new NodeSelector
   * @param {Object} options - Configuration options
   * @param {number} [options.criticalNodeRatio=0.18] - Ratio of critical nodes (18/82 Principle)
   * @param {number} [options.maxNodes=1000] - Maximum number of nodes in the system
   * @param {number} [options.rebalanceThreshold=0.1] - Threshold for rebalancing nodes
   * @param {boolean} [options.enableMetrics=true] - Enable performance metrics
   */
  constructor(options = {}) {
    this.options = {
      criticalNodeRatio: 0.18, // 18% of nodes are critical
      maxNodes: 1000,
      rebalanceThreshold: 0.1,
      enableMetrics: true,
      ...options
    };

    // Node storage
    this.nodes = new Map();
    this.criticalNodes = new Set();
    this.standardNodes = new Set();

    // Performance metrics
    this.metrics = {
      totalOperations: 0,
      criticalNodeOperations: 0,
      standardNodeOperations: 0,
      rebalanceCount: 0,
      averageResponseTime: 0
    };

    // Node performance tracking
    this.nodePerformance = new Map();

    debug('NodeSelector initialized with 18/82 Principle');
  }

  /**
   * Initialize the node selector
   * @returns {Promise<boolean>} - Promise that resolves when initialization is complete
   */
  async initialize() {
    debug('Initializing NodeSelector...');

    // Create initial node set
    await this._createInitialNodes();

    // Apply 18/82 Principle classification
    await this._classifyNodes();

    debug('NodeSelector initialized successfully');
    return true;
  }

  /**
   * Create initial set of nodes
   * @private
   */
  async _createInitialNodes() {
    const initialNodeCount = Math.min(100, this.options.maxNodes);
    
    for (let i = 0; i < initialNodeCount; i++) {
      const node = {
        id: uuidv4(),
        type: 'standard',
        performance: Math.random() * 100, // Random initial performance score
        load: 0,
        operations: 0,
        responseTime: 0,
        reliability: Math.random() * 100,
        created: Date.now()
      };

      this.nodes.set(node.id, node);
      this.nodePerformance.set(node.id, {
        operations: 0,
        totalResponseTime: 0,
        errors: 0,
        lastUpdate: Date.now()
      });
    }

    debug(`Created ${initialNodeCount} initial nodes`);
  }

  /**
   * Classify nodes according to the 18/82 Principle
   * @private
   */
  async _classifyNodes() {
    const nodeArray = Array.from(this.nodes.values());
    
    // Sort nodes by performance score (descending)
    nodeArray.sort((a, b) => b.performance - a.performance);

    // Calculate number of critical nodes (18% of total)
    const criticalNodeCount = Math.ceil(nodeArray.length * this.options.criticalNodeRatio);

    // Clear existing classifications
    this.criticalNodes.clear();
    this.standardNodes.clear();

    // Classify top 18% as critical nodes
    for (let i = 0; i < criticalNodeCount; i++) {
      const node = nodeArray[i];
      node.type = 'critical';
      this.criticalNodes.add(node.id);
      this.nodes.set(node.id, node);
    }

    // Classify remaining 82% as standard nodes
    for (let i = criticalNodeCount; i < nodeArray.length; i++) {
      const node = nodeArray[i];
      node.type = 'standard';
      this.standardNodes.add(node.id);
      this.nodes.set(node.id, node);
    }

    debug(`Classified ${criticalNodeCount} critical nodes and ${nodeArray.length - criticalNodeCount} standard nodes`);
  }

  /**
   * Select optimal nodes for an operation
   * @param {Object} operation - The operation requiring node selection
   * @param {string} [operation.priority='standard'] - Priority level ('critical', 'high', 'standard', 'low')
   * @param {number} [operation.nodeCount=1] - Number of nodes required
   * @returns {Promise<Array>} - Array of selected node IDs
   */
  async selectNodes(operation) {
    const priority = operation.priority || 'standard';
    const nodeCount = operation.nodeCount || 1;

    debug(`Selecting ${nodeCount} nodes for ${priority} priority operation`);

    let selectedNodes = [];

    // Apply 18/82 Principle for node selection
    if (priority === 'critical' || priority === 'high') {
      // Critical operations get priority access to critical nodes (18%)
      selectedNodes = await this._selectFromCriticalNodes(nodeCount);
      
      // If not enough critical nodes available, supplement with best standard nodes
      if (selectedNodes.length < nodeCount) {
        const remainingCount = nodeCount - selectedNodes.length;
        const additionalNodes = await this._selectFromStandardNodes(remainingCount, true);
        selectedNodes = selectedNodes.concat(additionalNodes);
      }
    } else {
      // Standard operations primarily use standard nodes (82%)
      selectedNodes = await this._selectFromStandardNodes(nodeCount, false);
      
      // If not enough standard nodes available, use critical nodes as fallback
      if (selectedNodes.length < nodeCount) {
        const remainingCount = nodeCount - selectedNodes.length;
        const additionalNodes = await this._selectFromCriticalNodes(remainingCount);
        selectedNodes = selectedNodes.concat(additionalNodes);
      }
    }

    // Update metrics
    this._updateSelectionMetrics(selectedNodes, priority);

    debug(`Selected ${selectedNodes.length} nodes: ${selectedNodes.join(', ')}`);
    return selectedNodes;
  }

  /**
   * Select nodes from critical node pool
   * @param {number} count - Number of nodes to select
   * @returns {Promise<Array>} - Array of selected critical node IDs
   * @private
   */
  async _selectFromCriticalNodes(count) {
    const availableCriticalNodes = Array.from(this.criticalNodes)
      .map(nodeId => this.nodes.get(nodeId))
      .filter(node => node && node.load < 80) // Only select nodes with load < 80%
      .sort((a, b) => a.load - b.load); // Sort by load (ascending)

    const selectedCount = Math.min(count, availableCriticalNodes.length);
    return availableCriticalNodes.slice(0, selectedCount).map(node => node.id);
  }

  /**
   * Select nodes from standard node pool
   * @param {number} count - Number of nodes to select
   * @param {boolean} selectBest - Whether to select the best performing standard nodes
   * @returns {Promise<Array>} - Array of selected standard node IDs
   * @private
   */
  async _selectFromStandardNodes(count, selectBest = false) {
    const availableStandardNodes = Array.from(this.standardNodes)
      .map(nodeId => this.nodes.get(nodeId))
      .filter(node => node && node.load < 90); // Only select nodes with load < 90%

    if (selectBest) {
      // Select best performing standard nodes
      availableStandardNodes.sort((a, b) => b.performance - a.performance);
    } else {
      // Select nodes with lowest load for balanced distribution
      availableStandardNodes.sort((a, b) => a.load - b.load);
    }

    const selectedCount = Math.min(count, availableStandardNodes.length);
    return availableStandardNodes.slice(0, selectedCount).map(node => node.id);
  }

  /**
   * Update node performance metrics
   * @param {string} nodeId - ID of the node to update
   * @param {Object} metrics - Performance metrics
   * @param {number} metrics.responseTime - Response time in milliseconds
   * @param {boolean} metrics.success - Whether the operation was successful
   */
  updateNodePerformance(nodeId, metrics) {
    const node = this.nodes.get(nodeId);
    const performance = this.nodePerformance.get(nodeId);

    if (!node || !performance) {
      debug(`Node ${nodeId} not found for performance update`);
      return;
    }

    // Update performance tracking
    performance.operations++;
    performance.totalResponseTime += metrics.responseTime;
    if (!metrics.success) {
      performance.errors++;
    }
    performance.lastUpdate = Date.now();

    // Update node metrics
    node.operations++;
    node.responseTime = performance.totalResponseTime / performance.operations;
    node.reliability = ((performance.operations - performance.errors) / performance.operations) * 100;

    // Update performance score based on response time and reliability
    node.performance = (100 - (node.responseTime / 10)) * (node.reliability / 100);

    // Update load (simplified calculation)
    node.load = Math.min(100, (performance.operations / 100) * 10);

    this.nodePerformance.set(nodeId, performance);
    this.nodes.set(nodeId, node);

    // Check if rebalancing is needed
    this._checkRebalanceNeeded();
  }

  /**
   * Update selection metrics
   * @param {Array} selectedNodes - Array of selected node IDs
   * @param {string} priority - Operation priority
   * @private
   */
  _updateSelectionMetrics(selectedNodes, priority) {
    this.metrics.totalOperations++;

    selectedNodes.forEach(nodeId => {
      if (this.criticalNodes.has(nodeId)) {
        this.metrics.criticalNodeOperations++;
      } else {
        this.metrics.standardNodeOperations++;
      }
    });
  }

  /**
   * Check if node rebalancing is needed
   * @private
   */
  _checkRebalanceNeeded() {
    const totalNodes = this.nodes.size;
    const currentCriticalRatio = this.criticalNodes.size / totalNodes;
    const targetRatio = this.options.criticalNodeRatio;

    if (Math.abs(currentCriticalRatio - targetRatio) > this.options.rebalanceThreshold) {
      debug('Node rebalancing needed');
      this._rebalanceNodes();
    }
  }

  /**
   * Rebalance nodes according to the 18/82 Principle
   * @private
   */
  async _rebalanceNodes() {
    debug('Rebalancing nodes...');
    
    await this._classifyNodes();
    this.metrics.rebalanceCount++;
    
    debug('Node rebalancing completed');
  }

  /**
   * Get current node statistics
   * @returns {Object} - Node statistics
   */
  getNodeStatistics() {
    const totalNodes = this.nodes.size;
    const criticalNodeCount = this.criticalNodes.size;
    const standardNodeCount = this.standardNodes.size;

    return {
      totalNodes,
      criticalNodes: criticalNodeCount,
      standardNodes: standardNodeCount,
      criticalNodeRatio: criticalNodeCount / totalNodes,
      metrics: { ...this.metrics },
      averageLoad: this._calculateAverageLoad(),
      averagePerformance: this._calculateAveragePerformance()
    };
  }

  /**
   * Calculate average node load
   * @returns {number} - Average load percentage
   * @private
   */
  _calculateAverageLoad() {
    const nodes = Array.from(this.nodes.values());
    const totalLoad = nodes.reduce((sum, node) => sum + node.load, 0);
    return nodes.length > 0 ? totalLoad / nodes.length : 0;
  }

  /**
   * Calculate average node performance
   * @returns {number} - Average performance score
   * @private
   */
  _calculateAveragePerformance() {
    const nodes = Array.from(this.nodes.values());
    const totalPerformance = nodes.reduce((sum, node) => sum + node.performance, 0);
    return nodes.length > 0 ? totalPerformance / nodes.length : 0;
  }
}

module.exports = NodeSelector;
