/**
 * Compliance Automation API - Validation Schemas
 * 
 * This file defines the validation schemas for the Compliance Automation API.
 */

const Joi = require('joi');

// Common validation schemas
const idSchema = Joi.string().regex(/^[0-9a-fA-F]{24}$/);
const dateSchema = Joi.date().iso();

// Automation Rule validation schemas
const automationRuleSchema = {
  create: Joi.object({
    name: Joi.string().required(),
    description: Joi.string().required(),
    type: Joi.string().valid('Notification', 'Remediation', 'Documentation', 'Testing', 'Approval', 'Custom').required(),
    trigger: Joi.object({
      event: Joi.string().valid('Schedule', 'DataChange', 'StatusChange', 'ManualTrigger', 'ExternalEvent').required(),
      schedule: Joi.object({
        frequency: Joi.string().valid('Daily', 'Weekly', 'Monthly', 'Quarterly', 'Annually', 'Custom'),
        cronExpression: Joi.string(),
        startDate: dateSchema,
        endDate: dateSchema
      }),
      conditions: Joi.array().items(Joi.object({
        field: Joi.string(),
        operator: Joi.string().valid('Equals', 'NotEquals', 'Contains', 'NotContains', 'GreaterThan', 'LessThan', 'IsNull', 'IsNotNull'),
        value: Joi.any()
      }))
    }).required(),
    actions: Joi.array().items(Joi.object({
      type: Joi.string().valid('SendEmail', 'CreateTask', 'UpdateRecord', 'GenerateDocument', 'ExecuteWorkflow', 'APICall', 'Custom').required(),
      config: Joi.object().required()
    })).required(),
    scope: Joi.object({
      frameworks: Joi.array().items(idSchema),
      controls: Joi.array().items(idSchema),
      entities: Joi.array().items(Joi.string())
    }),
    priority: Joi.string().valid('Low', 'Medium', 'High', 'Critical').default('Medium'),
    status: Joi.string().valid('Draft', 'Active', 'Inactive', 'Archived').default('Draft'),
    tags: Joi.array().items(Joi.string())
  }),
  update: Joi.object({
    name: Joi.string(),
    description: Joi.string(),
    type: Joi.string().valid('Notification', 'Remediation', 'Documentation', 'Testing', 'Approval', 'Custom'),
    trigger: Joi.object({
      event: Joi.string().valid('Schedule', 'DataChange', 'StatusChange', 'ManualTrigger', 'ExternalEvent'),
      schedule: Joi.object({
        frequency: Joi.string().valid('Daily', 'Weekly', 'Monthly', 'Quarterly', 'Annually', 'Custom'),
        cronExpression: Joi.string(),
        startDate: dateSchema,
        endDate: dateSchema
      }),
      conditions: Joi.array().items(Joi.object({
        field: Joi.string(),
        operator: Joi.string().valid('Equals', 'NotEquals', 'Contains', 'NotContains', 'GreaterThan', 'LessThan', 'IsNull', 'IsNotNull'),
        value: Joi.any()
      }))
    }),
    actions: Joi.array().items(Joi.object({
      type: Joi.string().valid('SendEmail', 'CreateTask', 'UpdateRecord', 'GenerateDocument', 'ExecuteWorkflow', 'APICall', 'Custom').required(),
      config: Joi.object().required()
    })),
    scope: Joi.object({
      frameworks: Joi.array().items(idSchema),
      controls: Joi.array().items(idSchema),
      entities: Joi.array().items(Joi.string())
    }),
    priority: Joi.string().valid('Low', 'Medium', 'High', 'Critical'),
    status: Joi.string().valid('Draft', 'Active', 'Inactive', 'Archived'),
    tags: Joi.array().items(Joi.string())
  })
};

// Workflow validation schemas
const workflowSchema = {
  create: Joi.object({
    name: Joi.string().required(),
    description: Joi.string().required(),
    type: Joi.string().valid('Approval', 'Review', 'Remediation', 'Testing', 'Documentation', 'Custom').required(),
    steps: Joi.array().items(Joi.object({
      name: Joi.string().required(),
      description: Joi.string(),
      type: Joi.string().valid('Task', 'Approval', 'Notification', 'Condition', 'Integration', 'Custom').required(),
      config: Joi.object().required(),
      assignee: Joi.string(),
      dueDate: dateSchema,
      dependencies: Joi.array().items(Joi.object({
        step: Joi.number(),
        condition: Joi.string().valid('Completed', 'Approved', 'Rejected', 'Any')
      }))
    })).required(),
    status: Joi.string().valid('Draft', 'Active', 'Inactive', 'Archived').default('Draft'),
    tags: Joi.array().items(Joi.string())
  }),
  update: Joi.object({
    name: Joi.string(),
    description: Joi.string(),
    type: Joi.string().valid('Approval', 'Review', 'Remediation', 'Testing', 'Documentation', 'Custom'),
    steps: Joi.array().items(Joi.object({
      name: Joi.string().required(),
      description: Joi.string(),
      type: Joi.string().valid('Task', 'Approval', 'Notification', 'Condition', 'Integration', 'Custom').required(),
      config: Joi.object().required(),
      assignee: Joi.string(),
      dueDate: dateSchema,
      dependencies: Joi.array().items(Joi.object({
        step: Joi.number(),
        condition: Joi.string().valid('Completed', 'Approved', 'Rejected', 'Any')
      }))
    })),
    status: Joi.string().valid('Draft', 'Active', 'Inactive', 'Archived'),
    tags: Joi.array().items(Joi.string())
  })
};

// Query validation schemas
const querySchema = {
  pagination: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(10),
    sort: Joi.string().default('-createdAt')
  }),
  automationRuleFilters: Joi.object({
    type: Joi.string().valid('Notification', 'Remediation', 'Documentation', 'Testing', 'Approval', 'Custom'),
    status: Joi.string().valid('Draft', 'Active', 'Inactive', 'Archived')
  }),
  workflowFilters: Joi.object({
    type: Joi.string().valid('Approval', 'Review', 'Remediation', 'Testing', 'Documentation', 'Custom'),
    status: Joi.string().valid('Draft', 'Active', 'Inactive', 'Archived')
  })
};

module.exports = {
  automationRule: automationRuleSchema,
  workflow: workflowSchema,
  query: querySchema
};
