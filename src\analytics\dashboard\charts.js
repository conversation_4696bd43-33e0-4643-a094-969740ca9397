/**
 * Comphyological Analytics Dashboard Charts
 * 
 * This module initializes and manages the charts for the analytics dashboard.
 */

// Chart instances
let comphyon<PERSON><PERSON> = null;
let frequencyChart = null;
let quantumSilenceChart = null;
let harmony<PERSON>hart = null;
let comphyonForecast<PERSON><PERSON> = null;
let resonanceForecast<PERSON><PERSON> = null;
let sensitivity<PERSON>hart = null;

/**
 * Initialize all charts
 */
function initializeCharts() {
    initializeComphyonChart();
    initializeFrequencyChart();
    initializeQuantumSilenceChart();
    initializeHarmonyChart();
    initializeComphyonForecastChart();
    initializeResonanceForecastChart();
    initializeSensitivityChart();
}

/**
 * Initialize Comphyon chart
 */
function initializeComphyonChart() {
    const ctx = document.getElementById('comphyon-chart').getContext('2d');
    
    comphyonChart = new Chart(ctx, {
        type: 'line',
        data: {
            datasets: [{
                label: 'Comphyon Value',
                data: [],
                borderColor: '#3f51b5',
                backgroundColor: 'rgba(63, 81, 181, 0.1)',
                borderWidth: 2,
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                x: {
                    type: 'time',
                    time: {
                        unit: 'second',
                        displayFormats: {
                            second: 'HH:mm:ss'
                        }
                    },
                    title: {
                        display: true,
                        text: 'Time'
                    }
                },
                y: {
                    min: 0,
                    max: 0.2,
                    title: {
                        display: true,
                        text: 'Value'
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    mode: 'index',
                    intersect: false
                }
            }
        }
    });
}

/**
 * Initialize Frequency chart
 */
function initializeFrequencyChart() {
    const ctx = document.getElementById('frequency-chart').getContext('2d');
    
    frequencyChart = new Chart(ctx, {
        type: 'line',
        data: {
            datasets: [{
                label: 'Frequency',
                data: [],
                borderColor: '#ff4081',
                backgroundColor: 'rgba(255, 64, 129, 0.1)',
                borderWidth: 2,
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                x: {
                    type: 'time',
                    time: {
                        unit: 'second',
                        displayFormats: {
                            second: 'HH:mm:ss'
                        }
                    },
                    title: {
                        display: true,
                        text: 'Time'
                    }
                },
                y: {
                    min: 390,
                    max: 410,
                    title: {
                        display: true,
                        text: 'Hz'
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    mode: 'index',
                    intersect: false
                },
                annotation: {
                    annotations: {
                        line1: {
                            type: 'line',
                            yMin: 396,
                            yMax: 396,
                            borderColor: 'rgba(255, 0, 0, 0.5)',
                            borderWidth: 1,
                            borderDash: [5, 5],
                            label: {
                                content: '396 Hz',
                                enabled: true,
                                position: 'left'
                            }
                        }
                    }
                }
            }
        }
    });
}

/**
 * Initialize Quantum Silence chart
 */
function initializeQuantumSilenceChart() {
    const ctx = document.getElementById('quantum-silence-chart').getContext('2d');
    
    quantumSilenceChart = new Chart(ctx, {
        type: 'bar',
        data: {
            datasets: [{
                label: 'Quantum Silence',
                data: [],
                backgroundColor: function(context) {
                    const value = context.dataset.data[context.dataIndex]?.y;
                    return value ? 'rgba(76, 175, 80, 0.8)' : 'rgba(244, 67, 54, 0.8)';
                },
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                x: {
                    type: 'time',
                    time: {
                        unit: 'second',
                        displayFormats: {
                            second: 'HH:mm:ss'
                        }
                    },
                    title: {
                        display: true,
                        text: 'Time'
                    }
                },
                y: {
                    min: 0,
                    max: 1,
                    ticks: {
                        callback: function(value) {
                            return value === 0 ? 'No' : value === 1 ? 'Yes' : '';
                        }
                    },
                    title: {
                        display: true,
                        text: 'Status'
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    callbacks: {
                        label: function(context) {
                            const value = context.parsed.y;
                            return value ? 'Quantum Silence: Yes' : 'Quantum Silence: No';
                        }
                    }
                }
            }
        }
    });
}

/**
 * Initialize Harmony chart
 */
function initializeHarmonyChart() {
    const ctx = document.getElementById('harmony-chart').getContext('2d');
    
    harmonyChart = new Chart(ctx, {
        type: 'line',
        data: {
            datasets: [{
                label: 'Harmony',
                data: [],
                borderColor: '#4caf50',
                backgroundColor: 'rgba(76, 175, 80, 0.1)',
                borderWidth: 2,
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                x: {
                    type: 'time',
                    time: {
                        unit: 'second',
                        displayFormats: {
                            second: 'HH:mm:ss'
                        }
                    },
                    title: {
                        display: true,
                        text: 'Time'
                    }
                },
                y: {
                    min: 0,
                    max: 1,
                    title: {
                        display: true,
                        text: 'Harmony'
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    mode: 'index',
                    intersect: false
                }
            }
        }
    });
}

/**
 * Initialize Comphyon Forecast chart
 */
function initializeComphyonForecastChart() {
    const ctx = document.getElementById('comphyon-forecast-chart').getContext('2d');
    
    comphyonForecastChart = new Chart(ctx, {
        type: 'line',
        data: {
            datasets: [
                {
                    label: 'Historical',
                    data: [],
                    borderColor: '#3f51b5',
                    backgroundColor: 'rgba(63, 81, 181, 0.1)',
                    borderWidth: 2,
                    tension: 0.4,
                    fill: false
                },
                {
                    label: 'Forecast',
                    data: [],
                    borderColor: '#ff9800',
                    backgroundColor: 'rgba(255, 152, 0, 0.1)',
                    borderWidth: 2,
                    borderDash: [5, 5],
                    tension: 0.4,
                    fill: false
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                x: {
                    type: 'time',
                    time: {
                        unit: 'second',
                        displayFormats: {
                            second: 'HH:mm:ss'
                        }
                    },
                    title: {
                        display: true,
                        text: 'Time'
                    }
                },
                y: {
                    min: 0,
                    max: 0.2,
                    title: {
                        display: true,
                        text: 'Value'
                    }
                }
            },
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                },
                tooltip: {
                    mode: 'index',
                    intersect: false
                }
            }
        }
    });
}

/**
 * Initialize Resonance Forecast chart
 */
function initializeResonanceForecastChart() {
    const ctx = document.getElementById('resonance-forecast-chart').getContext('2d');
    
    resonanceForecastChart = new Chart(ctx, {
        type: 'line',
        data: {
            datasets: [
                {
                    label: 'Historical',
                    data: [],
                    borderColor: '#ff4081',
                    backgroundColor: 'rgba(255, 64, 129, 0.1)',
                    borderWidth: 2,
                    tension: 0.4,
                    fill: false
                },
                {
                    label: 'Forecast',
                    data: [],
                    borderColor: '#ff9800',
                    backgroundColor: 'rgba(255, 152, 0, 0.1)',
                    borderWidth: 2,
                    borderDash: [5, 5],
                    tension: 0.4,
                    fill: false
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                x: {
                    type: 'time',
                    time: {
                        unit: 'second',
                        displayFormats: {
                            second: 'HH:mm:ss'
                        }
                    },
                    title: {
                        display: true,
                        text: 'Time'
                    }
                },
                y: {
                    min: 390,
                    max: 410,
                    title: {
                        display: true,
                        text: 'Hz'
                    }
                }
            },
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                },
                tooltip: {
                    mode: 'index',
                    intersect: false
                },
                annotation: {
                    annotations: {
                        line1: {
                            type: 'line',
                            yMin: 396,
                            yMax: 396,
                            borderColor: 'rgba(255, 0, 0, 0.5)',
                            borderWidth: 1,
                            borderDash: [5, 5],
                            label: {
                                content: '396 Hz',
                                enabled: true,
                                position: 'left'
                            }
                        }
                    }
                }
            }
        }
    });
}

/**
 * Initialize Sensitivity chart
 */
function initializeSensitivityChart() {
    const ctx = document.getElementById('sensitivity-chart').getContext('2d');
    
    sensitivityChart = new Chart(ctx, {
        type: 'radar',
        data: {
            labels: [
                'CSDE Governance',
                'CSDE Data Quality',
                'CSFE Risk',
                'CSFE Policy Compliance',
                'CSME Trust Factor',
                'CSME Integrity Factor'
            ],
            datasets: [{
                label: 'Sensitivity',
                data: [0, 0, 0, 0, 0, 0],
                backgroundColor: 'rgba(63, 81, 181, 0.2)',
                borderColor: '#3f51b5',
                borderWidth: 2,
                pointBackgroundColor: '#3f51b5',
                pointBorderColor: '#fff',
                pointHoverBackgroundColor: '#fff',
                pointHoverBorderColor: '#3f51b5'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                r: {
                    min: 0,
                    max: 1,
                    ticks: {
                        stepSize: 0.2
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return context.dataset.label + ': ' + context.parsed.r.toFixed(3);
                        }
                    }
                }
            }
        }
    });
}

/**
 * Update charts with new data
 * @param {Array} history - History data
 * @param {Array} predictions - Prediction data
 * @param {string} timeRange - Time range to display
 */
function updateCharts(history, predictions, timeRange) {
    // Calculate time range
    const now = Date.now();
    let startTime = 0;
    
    if (timeRange !== 'all') {
        startTime = now - parseInt(timeRange) * 1000;
    }
    
    // Filter history based on time range
    const filteredHistory = timeRange === 'all'
        ? history
        : history.filter(point => point.timestamp >= startTime);
    
    // Update Comphyon chart
    updateComphyonChart(filteredHistory);
    
    // Update Frequency chart
    updateFrequencyChart(filteredHistory);
    
    // Update Quantum Silence chart
    updateQuantumSilenceChart(filteredHistory);
    
    // Update Harmony chart
    updateHarmonyChart(filteredHistory);
    
    // Update forecast charts if predictions are available
    if (predictions && predictions.length > 0) {
        // Find comphyon prediction
        const comphyonPrediction = predictions.find(p => p.type === 'comphyon');
        if (comphyonPrediction) {
            updateComphyonForecastChart(filteredHistory, comphyonPrediction.values);
        }
        
        // Find resonance prediction
        const resonancePrediction = predictions.find(p => p.type === 'resonance');
        if (resonancePrediction) {
            updateResonanceForecastChart(filteredHistory, resonancePrediction.values);
        }
    }
}

/**
 * Update Comphyon chart
 * @param {Array} history - History data
 */
function updateComphyonChart(history) {
    // Convert history to chart data
    const chartData = history.map(point => ({
        x: point.timestamp,
        y: point.comphyon
    }));
    
    // Update chart data
    comphyonChart.data.datasets[0].data = chartData;
    
    // Update chart
    comphyonChart.update();
}

/**
 * Update Frequency chart
 * @param {Array} history - History data
 */
function updateFrequencyChart(history) {
    // Convert history to chart data
    const chartData = history.map(point => ({
        x: point.timestamp,
        y: point.frequency
    }));
    
    // Update chart data
    frequencyChart.data.datasets[0].data = chartData;
    
    // Update chart
    frequencyChart.update();
}

/**
 * Update Quantum Silence chart
 * @param {Array} history - History data
 */
function updateQuantumSilenceChart(history) {
    // Convert history to chart data
    const chartData = history.map(point => ({
        x: point.timestamp,
        y: point.isQuantumSilence ? 1 : 0
    }));
    
    // Update chart data
    quantumSilenceChart.data.datasets[0].data = chartData;
    
    // Update chart
    quantumSilenceChart.update();
}

/**
 * Update Harmony chart
 * @param {Array} history - History data
 */
function updateHarmonyChart(history) {
    // Convert history to chart data
    const chartData = history.map(point => ({
        x: point.timestamp,
        y: 1 - Math.abs(point.comphyon)
    }));
    
    // Update chart data
    harmonyChart.data.datasets[0].data = chartData;
    
    // Update chart
    harmonyChart.update();
}

/**
 * Update Comphyon Forecast chart
 * @param {Array} history - History data
 * @param {Array} forecast - Forecast data
 */
function updateComphyonForecastChart(history, forecast) {
    // Convert history to chart data
    const historyData = history.map(point => ({
        x: point.timestamp,
        y: point.comphyon
    }));
    
    // Convert forecast to chart data
    const forecastData = forecast.map(point => ({
        x: point.timestamp,
        y: point.value
    }));
    
    // Update chart data
    comphyonForecastChart.data.datasets[0].data = historyData;
    comphyonForecastChart.data.datasets[1].data = forecastData;
    
    // Update chart
    comphyonForecastChart.update();
}

/**
 * Update Resonance Forecast chart
 * @param {Array} history - History data
 * @param {Array} forecast - Forecast data
 */
function updateResonanceForecastChart(history, forecast) {
    // Convert history to chart data
    const historyData = history.map(point => ({
        x: point.timestamp,
        y: point.frequency
    }));
    
    // Convert forecast to chart data
    const forecastData = forecast.map(point => ({
        x: point.timestamp,
        y: point.value
    }));
    
    // Update chart data
    resonanceForecastChart.data.datasets[0].data = historyData;
    resonanceForecastChart.data.datasets[1].data = forecastData;
    
    // Update chart
    resonanceForecastChart.update();
}

/**
 * Update Sensitivity chart
 * @param {Object} sensitivityData - Sensitivity analysis data
 */
function updateSensitivityChart(sensitivityData) {
    // Update chart data
    sensitivityChart.data.datasets[0].data = [
        sensitivityData.csdeGovernance,
        sensitivityData.csdeDataQuality,
        sensitivityData.csfeRisk,
        sensitivityData.csfePolicyCompliance,
        sensitivityData.csmeTrustFactor,
        sensitivityData.csmeIntegrityFactor
    ];
    
    // Update chart
    sensitivityChart.update();
}

// Export functions
if (typeof module !== 'undefined') {
    module.exports = {
        initializeCharts,
        updateCharts,
        updateSensitivityChart
    };
}
