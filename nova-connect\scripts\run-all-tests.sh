#!/bin/bash
# Master script to run all tests for Google Cloud Marketplace integration

# Set variables
PROJECT_ID=${1:-"novafuse-test"}
CLUSTER_NAME=${2:-"novafuse-test-cluster"}
ZONE=${3:-"us-central1-a"}
NAMESPACE=${4:-"novafuse-test"}
IMAGE_NAME=${5:-"novafuse-uac"}
IMAGE_TAG=${6:-"1.0.0"}
EMAIL=${7:-"<EMAIL>"}

# Make all scripts executable
echo "Making all scripts executable..."
chmod +x scripts/*.sh

# Setup test environment
echo "Setting up test environment..."
./scripts/setup-test-environment.sh $PROJECT_ID $CLUSTER_NAME $ZONE

# Build Docker image
echo "Building Docker image..."
./scripts/build-docker-image.sh $PROJECT_ID $IMAGE_NAME $IMAGE_TAG

# Test Docker image
echo "Testing Docker image..."
./scripts/test-docker-image.sh $PROJECT_ID $IMAGE_NAME $IMAGE_TAG

# Deploy to GKE
echo "Deploying to GKE..."
./scripts/deploy-to-gke.sh $PROJECT_ID $CLUSTER_NAME $ZONE $NAMESPACE

# Test GKE deployment
echo "Testing GKE deployment..."
./scripts/test-gke-deployment.sh $PROJECT_ID $CLUSTER_NAME $ZONE $NAMESPACE

# Setup monitoring
echo "Setting up monitoring..."
./scripts/setup-monitoring.sh $PROJECT_ID $CLUSTER_NAME $ZONE "novafuse-monitoring"

# Setup alerting
echo "Setting up alerting..."
./scripts/setup-alerting.sh $PROJECT_ID $CLUSTER_NAME $ZONE "novafuse-monitoring" $EMAIL

# Run security scan
echo "Running security scan..."
./scripts/security-scan.sh $PROJECT_ID $IMAGE_NAME $IMAGE_TAG

# Run security tests
echo "Running security tests..."
./scripts/security-test.sh $PROJECT_ID $CLUSTER_NAME $ZONE $NAMESPACE

# Run performance tests
echo "Running performance tests..."
./scripts/performance-test.sh $PROJECT_ID $CLUSTER_NAME $ZONE $NAMESPACE

# Package for marketplace
echo "Packaging for marketplace..."
./scripts/package-for-marketplace.sh $PROJECT_ID $IMAGE_NAME $IMAGE_TAG

# Test marketplace deployment
echo "Testing marketplace deployment..."
./scripts/test-marketplace-deployment.sh $PROJECT_ID $IMAGE_NAME $IMAGE_TAG "marketplace-test" "core"

echo "All tests completed successfully!"
