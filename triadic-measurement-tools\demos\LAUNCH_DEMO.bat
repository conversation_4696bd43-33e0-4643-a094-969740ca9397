@echo off
echo.
echo 🌌 NovaFuse Cosmic Alignment Simulator (NCAS)
echo    International Demonstration Suite
echo    World's First Physics-Based AI Safety System
echo.
echo 🚀 Launching demonstration...
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

REM Install dependencies if needed
if not exist node_modules (
    echo 📦 Installing dependencies...
    npm install express socket.io
)

REM Launch the demonstration
echo 🌟 Starting NCAS International Demo...
echo.
echo 📍 Demo will be available at: http://localhost:3142
echo 🌐 Browser will open automatically
echo.
echo ⚡ Press Ctrl+C to stop the demonstration
echo.

node launch_international_demo.js

pause
