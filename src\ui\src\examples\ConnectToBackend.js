/**
 * Example of how to connect the UI to the real backend
 * 
 * This example shows how to:
 * 1. Connect to the WebSocket server
 * 2. Register a tensor
 * 3. Create a visualization
 * 4. Execute a query
 * 5. Subscribe to real-time updates
 */

import webSocketService from '../services/WebSocketService';

/**
 * Connect to the backend
 * @returns {Promise<void>} - Promise that resolves when connected
 */
async function connectToBackend() {
  try {
    // Connect to WebSocket server
    await webSocketService.connect();
    console.log('Connected to WebSocket server');

    // Subscribe to channels
    await webSocketService.subscribe('tensor-updates');
    await webSocketService.subscribe('visualization-updates');
    await webSocketService.subscribe('analytics-updates');
    console.log('Subscribed to channels');

    // Set up event listeners
    webSocketService.addEventListener('message:tensor-updates', handleTensorUpdates);
    webSocketService.addEventListener('message:visualization-updates', handleVisualizationUpdates);
    webSocketService.addEventListener('message:analytics-updates', handleAnalyticsUpdates);
    console.log('Set up event listeners');

    // Register a tensor
    const registerResponse = await registerTensor('example-tensor', {
      values: [0.5, 0.6, 0.7, 0.8, 0.9]
    });
    console.log('Registered tensor:', registerResponse);

    // Create a visualization
    const createVisualizationResponse = await createVisualization(
      '3d_tensor_visualization',
      {
        tensor: registerResponse.result.tensor,
        dimensions: [5, 1, 1]
      },
      {
        renderMode: 'high',
        showAxes: true,
        showGrid: true,
        rotationSpeed: 1,
        colorScheme: 'default'
      }
    );
    console.log('Created visualization:', createVisualizationResponse);

    // Execute a query
    const queryResponse = await executeQuery(
      'SELECT * FROM tensor_metrics WHERE tensor_id = "example-tensor"',
      { limit: 10 }
    );
    console.log('Executed query:', queryResponse);

    // Publish tensor update
    await webSocketService.publish('tensor-updates', {
      type: 'tensor-updated',
      id: 'example-tensor',
      tensor: {
        values: [0.6, 0.7, 0.8, 0.9, 1.0],
        health: 0.9,
        entropyContainment: 0.02
      }
    });
    console.log('Published tensor update');

    return {
      tensorId: 'example-tensor',
      visualizationId: createVisualizationResponse.result.id
    };
  } catch (error) {
    console.error('Error connecting to backend:', error);
    throw error;
  }
}

/**
 * Disconnect from the backend
 * @returns {Promise<void>} - Promise that resolves when disconnected
 */
async function disconnectFromBackend() {
  try {
    // Remove event listeners
    webSocketService.removeEventListener('message:tensor-updates', handleTensorUpdates);
    webSocketService.removeEventListener('message:visualization-updates', handleVisualizationUpdates);
    webSocketService.removeEventListener('message:analytics-updates', handleAnalyticsUpdates);

    // Disconnect from WebSocket server
    await webSocketService.disconnect();
    console.log('Disconnected from WebSocket server');
  } catch (error) {
    console.error('Error disconnecting from backend:', error);
    throw error;
  }
}

/**
 * Register a tensor
 * @param {string} id - Tensor ID
 * @param {Object} tensor - Tensor data
 * @param {string} [domain='universal'] - Domain
 * @returns {Promise<Object>} - Promise that resolves with the response
 */
async function registerTensor(id, tensor, domain = 'universal') {
  return webSocketService.send({
    component: 'tensor',
    type: 'register-tensor',
    id,
    tensor,
    domain
  });
}

/**
 * Create a visualization
 * @param {string} visualizationType - Visualization type
 * @param {Object} data - Visualization data
 * @param {Object} [options] - Visualization options
 * @returns {Promise<Object>} - Promise that resolves with the response
 */
async function createVisualization(visualizationType, data, options = {}) {
  return webSocketService.send({
    component: 'visualization',
    type: 'create-visualization',
    visualizationType,
    data,
    options
  });
}

/**
 * Execute a query
 * @param {string} query - Query to execute
 * @param {Object} [params] - Query parameters
 * @returns {Promise<Object>} - Promise that resolves with the response
 */
async function executeQuery(query, params = {}) {
  return webSocketService.send({
    component: 'analytics',
    type: 'execute-query',
    query,
    params
  });
}

/**
 * Handle tensor updates
 * @param {Object} data - Update data
 */
function handleTensorUpdates(data) {
  console.log('Received tensor update:', data);
}

/**
 * Handle visualization updates
 * @param {Object} data - Update data
 */
function handleVisualizationUpdates(data) {
  console.log('Received visualization update:', data);
}

/**
 * Handle analytics updates
 * @param {Object} data - Update data
 */
function handleAnalyticsUpdates(data) {
  console.log('Received analytics update:', data);
}

export {
  connectToBackend,
  disconnectFromBackend,
  registerTensor,
  createVisualization,
  executeQuery
};
