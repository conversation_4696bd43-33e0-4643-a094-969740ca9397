/**
 * Connector Health Details Component
 * 
 * This component displays detailed health information for a specific connector.
 */

import React, { useState } from 'react';
import { 
  Box, 
  Button, 
  Card, 
  CardContent, 
  Chip, 
  Divider, 
  Grid, 
  Paper, 
  Tab, 
  Tabs, 
  Typography 
} from '@mui/material';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import WarningIcon from '@mui/icons-material/Warning';
import HistoryIcon from '@mui/icons-material/History';
import SpeedIcon from '@mui/icons-material/Speed';
import BugReportIcon from '@mui/icons-material/BugReport';
import SettingsIcon from '@mui/icons-material/Settings';
import { 
  LineChart, 
  Line, 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell
} from 'recharts';

const ConnectorHealthDetails = ({ connector, healthData }) => {
  const [activeTab, setActiveTab] = useState('overview');
  
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };
  
  const getStatusIcon = (status) => {
    switch (status) {
      case 'healthy':
        return <CheckCircleIcon color="success" />;
      case 'warning':
        return <WarningIcon color="warning" />;
      case 'error':
        return <ErrorIcon color="error" />;
      default:
        return null;
    }
  };
  
  const getStatusColor = (status) => {
    switch (status) {
      case 'healthy':
        return 'success';
      case 'warning':
        return 'warning';
      case 'error':
        return 'error';
      default:
        return 'default';
    }
  };
  
  const getStatusText = (status) => {
    switch (status) {
      case 'healthy':
        return 'Healthy';
      case 'warning':
        return 'Warning';
      case 'error':
        return 'Error';
      default:
        return 'Unknown';
    }
  };
  
  // Generate hourly data for the last 24 hours
  const generateHourlyData = () => {
    const hours = Array(24).fill(0).map((_, i) => {
      const date = new Date();
      date.setHours(date.getHours() - 23 + i);
      return date.getHours();
    });
    
    return hours.map((hour, index) => {
      const status = healthData.history.status[index];
      const responseTime = healthData.history.responseTimes[index];
      
      // Generate random success rate based on status
      let successRate;
      if (status === 'healthy') {
        successRate = Math.floor(Math.random() * 10) + 90;
      } else if (status === 'warning') {
        successRate = Math.floor(Math.random() * 20) + 70;
      } else {
        successRate = Math.floor(Math.random() * 70);
      }
      
      // Generate random request count
      const requestCount = Math.floor(Math.random() * 100) + 10;
      
      return {
        hour: `${hour}:00`,
        status,
        responseTime,
        successRate,
        requestCount,
        errorCount: Math.floor(requestCount * (100 - successRate) / 100)
      };
    });
  };
  
  // Generate daily data for the last 30 days
  const generateDailyData = () => {
    return Array(30).fill(0).map((_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - 29 + i);
      
      // Generate random status with higher probability of healthy
      const statusRandom = Math.random();
      let status;
      if (statusRandom > 0.9) {
        status = 'error';
      } else if (statusRandom > 0.8) {
        status = 'warning';
      } else {
        status = 'healthy';
      }
      
      // Generate random response time between 100ms and 1000ms
      const responseTime = Math.floor(Math.random() * 900) + 100;
      
      // Generate random success rate based on status
      let successRate;
      if (status === 'healthy') {
        successRate = Math.floor(Math.random() * 10) + 90;
      } else if (status === 'warning') {
        successRate = Math.floor(Math.random() * 20) + 70;
      } else {
        successRate = Math.floor(Math.random() * 70);
      }
      
      // Generate random request count
      const requestCount = Math.floor(Math.random() * 1000) + 100;
      
      return {
        date: `${date.getMonth() + 1}/${date.getDate()}`,
        status,
        responseTime,
        successRate,
        requestCount,
        errorCount: Math.floor(requestCount * (100 - successRate) / 100)
      };
    });
  };
  
  // Generate error data
  const generateErrorData = () => {
    const errorTypes = ['Connection', 'Authentication', 'Timeout', 'Rate Limit', 'Server', 'Validation'];
    
    return errorTypes.map(type => {
      // Generate random count between 0 and 50
      const count = Math.floor(Math.random() * 50);
      
      return {
        type,
        count
      };
    });
  };
  
  const hourlyData = generateHourlyData();
  const dailyData = generateDailyData();
  const errorData = generateErrorData();
  
  // Calculate total requests and errors
  const totalRequests = dailyData.reduce((sum, day) => sum + day.requestCount, 0);
  const totalErrors = dailyData.reduce((sum, day) => sum + day.errorCount, 0);
  const successRate = totalRequests > 0 ? Math.round((totalRequests - totalErrors) / totalRequests * 100) : 0;
  
  // Calculate average response time
  const avgResponseTime = Math.round(
    dailyData.reduce((sum, day) => sum + day.responseTime, 0) / dailyData.length
  );
  
  // Generate pie chart data for error distribution
  const errorDistribution = errorData.filter(error => error.count > 0);
  const COLORS = ['#f44336', '#ff9800', '#2196f3', '#4caf50', '#9c27b0', '#795548'];
  
  return (
    <Box>
      <Card variant="outlined" sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                {getStatusIcon(healthData.status)}
                <Typography variant="h5" sx={{ ml: 1 }}>
                  {connector.name}
                </Typography>
              </Box>
              
              <Typography variant="body1" color="textSecondary" paragraph>
                {connector.description}
              </Typography>
              
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Typography variant="subtitle2" sx={{ mr: 1 }}>
                  Status:
                </Typography>
                <Chip 
                  label={getStatusText(healthData.status)} 
                  color={getStatusColor(healthData.status)} 
                  size="small" 
                />
              </Box>
              
              <Typography variant="body2" sx={{ mb: 1 }}>
                <strong>Last Check:</strong> {healthData.lastCheck.toLocaleString()}
              </Typography>
              
              <Typography variant="body2" sx={{ mb: 1 }}>
                <strong>Base URL:</strong> {connector.baseUrl}
              </Typography>
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Paper sx={{ p: 2, textAlign: 'center', bgcolor: 'primary.light' }}>
                    <Typography variant="h6" gutterBottom>
                      Success Rate
                    </Typography>
                    <Typography variant="h4">
                      {successRate}%
                    </Typography>
                  </Paper>
                </Grid>
                
                <Grid item xs={6}>
                  <Paper sx={{ p: 2, textAlign: 'center', bgcolor: 'secondary.light' }}>
                    <Typography variant="h6" gutterBottom>
                      Avg Response
                    </Typography>
                    <Typography variant="h4">
                      {avgResponseTime} ms
                    </Typography>
                  </Paper>
                </Grid>
                
                <Grid item xs={6}>
                  <Paper sx={{ p: 2, textAlign: 'center', bgcolor: 'success.light' }}>
                    <Typography variant="h6" gutterBottom>
                      Requests
                    </Typography>
                    <Typography variant="h4">
                      {totalRequests}
                    </Typography>
                  </Paper>
                </Grid>
                
                <Grid item xs={6}>
                  <Paper sx={{ p: 2, textAlign: 'center', bgcolor: 'error.light' }}>
                    <Typography variant="h6" gutterBottom>
                      Errors
                    </Typography>
                    <Typography variant="h4">
                      {totalErrors}
                    </Typography>
                  </Paper>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
      
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={activeTab} onChange={handleTabChange} aria-label="health tabs">
          <Tab label="Overview" value="overview" icon={<SpeedIcon />} iconPosition="start" />
          <Tab label="History" value="history" icon={<HistoryIcon />} iconPosition="start" />
          <Tab label="Errors" value="errors" icon={<BugReportIcon />} iconPosition="start" />
          <Tab label="Settings" value="settings" icon={<SettingsIcon />} iconPosition="start" />
        </Tabs>
      </Box>
      
      {activeTab === 'overview' && (
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Card variant="outlined" sx={{ mb: 3 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Response Time (Last 24 Hours)
                </Typography>
                <Box sx={{ height: 300 }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart
                      data={hourlyData}
                      margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="hour" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Line 
                        type="monotone" 
                        dataKey="responseTime" 
                        name="Response Time (ms)" 
                        stroke="#2196f3" 
                        strokeWidth={2} 
                        dot={{ r: 3 }} 
                        activeDot={{ r: 5 }} 
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <Card variant="outlined" sx={{ mb: 3 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Success Rate (Last 24 Hours)
                </Typography>
                <Box sx={{ height: 300 }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart
                      data={hourlyData}
                      margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="hour" />
                      <YAxis domain={[0, 100]} />
                      <Tooltip />
                      <Legend />
                      <Line 
                        type="monotone" 
                        dataKey="successRate" 
                        name="Success Rate (%)" 
                        stroke="#4caf50" 
                        strokeWidth={2} 
                        dot={{ r: 3 }} 
                        activeDot={{ r: 5 }} 
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <Card variant="outlined" sx={{ mb: 3 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Request Volume (Last 24 Hours)
                </Typography>
                <Box sx={{ height: 300 }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={hourlyData}
                      margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="hour" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Bar dataKey="requestCount" name="Requests" fill="#2196f3" />
                      <Bar dataKey="errorCount" name="Errors" fill="#f44336" />
                    </BarChart>
                  </ResponsiveContainer>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12}>
            <Card variant="outlined">
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Recent Issues
                </Typography>
                
                {healthData.issues.length > 0 ? (
                  <Box>
                    {healthData.issues.map((issue, index) => (
                      <Paper 
                        key={index} 
                        variant="outlined" 
                        sx={{ 
                          p: 2, 
                          mb: 2, 
                          bgcolor: issue.type === 'connection' || issue.type === 'authentication' 
                            ? 'error.light' 
                            : 'warning.light' 
                        }}
                      >
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            {issue.type === 'connection' || issue.type === 'authentication' 
                              ? <ErrorIcon color="error" sx={{ mr: 1 }} /> 
                              : <WarningIcon color="warning" sx={{ mr: 1 }} />}
                            <Typography variant="subtitle1">
                              {issue.type.charAt(0).toUpperCase() + issue.type.slice(1)} Issue
                            </Typography>
                          </Box>
                          <Typography variant="body2" color="textSecondary">
                            {issue.timestamp.toLocaleString()}
                          </Typography>
                        </Box>
                        <Typography variant="body1" sx={{ mt: 1 }}>
                          {issue.message}
                        </Typography>
                      </Paper>
                    ))}
                  </Box>
                ) : (
                  <Typography variant="body1" color="textSecondary" sx={{ textAlign: 'center', py: 3 }}>
                    No recent issues detected
                  </Typography>
                )}
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}
      
      {activeTab === 'history' && (
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Card variant="outlined" sx={{ mb: 3 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  30-Day Performance History
                </Typography>
                <Box sx={{ height: 400 }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart
                      data={dailyData}
                      margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis yAxisId="left" />
                      <YAxis yAxisId="right" orientation="right" domain={[0, 100]} />
                      <Tooltip />
                      <Legend />
                      <Line 
                        yAxisId="left"
                        type="monotone" 
                        dataKey="responseTime" 
                        name="Response Time (ms)" 
                        stroke="#2196f3" 
                        strokeWidth={2} 
                        dot={{ r: 2 }} 
                        activeDot={{ r: 4 }} 
                      />
                      <Line 
                        yAxisId="right"
                        type="monotone" 
                        dataKey="successRate" 
                        name="Success Rate (%)" 
                        stroke="#4caf50" 
                        strokeWidth={2} 
                        dot={{ r: 2 }} 
                        activeDot={{ r: 4 }} 
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12}>
            <Card variant="outlined">
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  30-Day Request Volume
                </Typography>
                <Box sx={{ height: 300 }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={dailyData}
                      margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Bar dataKey="requestCount" name="Requests" fill="#2196f3" />
                      <Bar dataKey="errorCount" name="Errors" fill="#f44336" />
                    </BarChart>
                  </ResponsiveContainer>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}
      
      {activeTab === 'errors' && (
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card variant="outlined" sx={{ mb: 3 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Error Distribution
                </Typography>
                <Box sx={{ height: 300, display: 'flex', justifyContent: 'center' }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={errorDistribution}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={100}
                        fill="#8884d8"
                        dataKey="count"
                        nameKey="type"
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      >
                        {errorDistribution.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <Card variant="outlined" sx={{ mb: 3 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Error Counts by Type
                </Typography>
                <Box sx={{ height: 300 }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={errorData}
                      margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                      layout="vertical"
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis type="number" />
                      <YAxis dataKey="type" type="category" />
                      <Tooltip />
                      <Legend />
                      <Bar dataKey="count" name="Error Count" fill="#f44336" />
                    </BarChart>
                  </ResponsiveContainer>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12}>
            <Card variant="outlined">
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Error Trend (Last 30 Days)
                </Typography>
                <Box sx={{ height: 300 }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart
                      data={dailyData}
                      margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Line 
                        type="monotone" 
                        dataKey="errorCount" 
                        name="Errors" 
                        stroke="#f44336" 
                        strokeWidth={2} 
                        dot={{ r: 2 }} 
                        activeDot={{ r: 4 }} 
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}
      
      {activeTab === 'settings' && (
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Card variant="outlined" sx={{ mb: 3 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Health Check Settings
                </Typography>
                
                <Grid container spacing={3}>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" gutterBottom>
                      Health Check Interval
                    </Typography>
                    <Typography variant="body1">
                      5 minutes
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" gutterBottom>
                      Response Time Threshold
                    </Typography>
                    <Typography variant="body1">
                      Warning: &gt; 500ms, Error: &gt; 1000ms
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" gutterBottom>
                      Success Rate Threshold
                    </Typography>
                    <Typography variant="body1">
                      Warning: &lt; 90%, Error: &lt; 70%
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" gutterBottom>
                      Error Rate Threshold
                    </Typography>
                    <Typography variant="body1">
                      Warning: &gt; 10%, Error: &gt; 30%
                    </Typography>
                  </Grid>
                </Grid>
                
                <Box sx={{ mt: 3 }}>
                  <Button variant="contained">
                    Edit Settings
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12}>
            <Card variant="outlined">
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Alert Settings
                </Typography>
                
                <Grid container spacing={3}>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" gutterBottom>
                      Email Alerts
                    </Typography>
                    <Typography variant="body1">
                      Enabled for Error status
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" gutterBottom>
                      Slack Alerts
                    </Typography>
                    <Typography variant="body1">
                      Enabled for Warning and Error status
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" gutterBottom>
                      SMS Alerts
                    </Typography>
                    <Typography variant="body1">
                      Disabled
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" gutterBottom>
                      Alert Recipients
                    </Typography>
                    <Typography variant="body1">
                      <EMAIL>, #api-alerts
                    </Typography>
                  </Grid>
                </Grid>
                
                <Box sx={{ mt: 3 }}>
                  <Button variant="contained">
                    Edit Alert Settings
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}
    </Box>
  );
};

export default ConnectorHealthDetails;
