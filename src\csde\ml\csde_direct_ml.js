/**
 * CSDE Direct ML Integration
 * 
 * This module directly integrates the CSDE engine with ML capabilities.
 */

const { CSDEEngine } = require('../index');
const fs = require('fs');
const path = require('path');

class CSDEDirectML {
  /**
   * Create a new CSDE Direct ML instance
   */
  constructor() {
    // Initialize CSDE Engine
    this.csdeEngine = new CSDEEngine();
    console.log('CSDE Direct ML initialized');
  }
  
  /**
   * Analyze input data and provide ML-enhanced insights
   * @param {Object} input - Input data
   * @returns {Object} - Analysis result
   */
  analyze(input) {
    // Calculate CSDE result
    const csdeResult = this.csdeEngine.calculate(
      input.complianceData,
      input.gcpData,
      input.cyberSafetyData
    );
    
    // Extract key components
    const components = {
      nist: csdeResult.nistComponent.processedValue,
      gcp: csdeResult.gcpComponent.processedValue,
      cyberSafety: csdeResult.cyberSafetyComponent.processedValue,
      tensorProduct: csdeResult.tensorProduct,
      fusionValue: csdeResult.fusionValue,
      circularTrustFactor: csdeResult.circularTrustFactor
    };
    
    // Calculate component contributions
    const totalValue = csdeResult.csdeValue;
    const contributions = {};
    
    for (const component in components) {
      contributions[component] = (components[component] / totalValue) * 100;
    }
    
    // Identify top remediation actions
    const topRemediationActions = csdeResult.remediationActions
      .sort((a, b) => {
        // Sort by priority first
        const priorityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
        const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
        
        if (priorityDiff !== 0) {
          return priorityDiff;
        }
        
        // Then by automation potential
        const automationOrder = { high: 3, medium: 2, low: 1 };
        return automationOrder[b.automationPotential] - automationOrder[a.automationPotential];
      })
      .slice(0, 5);
    
    // Generate ML insights
    const mlInsights = this._generateInsights(input, csdeResult, components, contributions);
    
    return {
      csdeValue: csdeResult.csdeValue,
      performanceFactor: csdeResult.performanceFactor,
      components,
      contributions,
      topRemediationActions,
      mlInsights
    };
  }
  
  /**
   * Generate ML insights
   * @param {Object} input - Input data
   * @param {Object} csdeResult - CSDE result
   * @param {Object} components - CSDE components
   * @param {Object} contributions - Component contributions
   * @returns {Object} - ML insights
   * @private
   */
  _generateInsights(input, csdeResult, components, contributions) {
    // Calculate compliance status
    const complianceStatus = this._analyzeComplianceStatus(input.complianceData);
    
    // Calculate GCP integration status
    const gcpStatus = this._analyzeGcpStatus(input.gcpData);
    
    // Calculate Cyber-Safety status
    const cyberSafetyStatus = this._analyzeCyberSafetyStatus(input.cyberSafetyData);
    
    // Identify key improvement areas
    const improvementAreas = this._identifyImprovementAreas(
      complianceStatus,
      gcpStatus,
      cyberSafetyStatus,
      contributions
    );
    
    // Generate recommendations
    const recommendations = this._generateRecommendations(
      improvementAreas,
      csdeResult.remediationActions
    );
    
    return {
      complianceStatus,
      gcpStatus,
      cyberSafetyStatus,
      improvementAreas,
      recommendations,
      confidenceScore: 0.95 // High confidence since we're using the CSDE engine directly
    };
  }
  
  /**
   * Analyze compliance status
   * @param {Object} complianceData - Compliance data
   * @returns {Object} - Compliance status
   * @private
   */
  _analyzeComplianceStatus(complianceData) {
    // Count controls by status
    const statusCounts = {
      compliant: 0,
      partial: 0,
      'non-compliant': 0
    };
    
    if (complianceData.controls) {
      complianceData.controls.forEach(control => {
        statusCounts[control.status] = (statusCounts[control.status] || 0) + 1;
      });
    }
    
    // Calculate compliance score
    const totalControls = complianceData.controls ? complianceData.controls.length : 0;
    const complianceScore = totalControls > 0 ?
      (statusCounts.compliant + 0.5 * statusCounts.partial) / totalControls : 0;
    
    // Determine compliance level
    let complianceLevel;
    if (complianceScore >= 0.9) {
      complianceLevel = 'excellent';
    } else if (complianceScore >= 0.7) {
      complianceLevel = 'good';
    } else if (complianceScore >= 0.5) {
      complianceLevel = 'moderate';
    } else if (complianceScore >= 0.3) {
      complianceLevel = 'poor';
    } else {
      complianceLevel = 'critical';
    }
    
    return {
      score: complianceScore,
      level: complianceLevel,
      statusCounts,
      totalControls
    };
  }
  
  /**
   * Analyze GCP status
   * @param {Object} gcpData - GCP data
   * @returns {Object} - GCP status
   * @private
   */
  _analyzeGcpStatus(gcpData) {
    // Count services by status
    const statusCounts = {
      optimal: 0,
      partial: 0,
      'non-optimal': 0
    };
    
    if (gcpData.services) {
      gcpData.services.forEach(service => {
        statusCounts[service.status] = (statusCounts[service.status] || 0) + 1;
      });
    }
    
    // Calculate GCP score
    const totalServices = gcpData.services ? gcpData.services.length : 0;
    const gcpScore = totalServices > 0 ?
      (statusCounts.optimal + 0.5 * statusCounts.partial) / totalServices : 0;
    
    // Determine GCP level
    let gcpLevel;
    if (gcpScore >= 0.9) {
      gcpLevel = 'excellent';
    } else if (gcpScore >= 0.7) {
      gcpLevel = 'good';
    } else if (gcpScore >= 0.5) {
      gcpLevel = 'moderate';
    } else if (gcpScore >= 0.3) {
      gcpLevel = 'poor';
    } else {
      gcpLevel = 'critical';
    }
    
    return {
      score: gcpScore,
      level: gcpLevel,
      statusCounts,
      totalServices
    };
  }
  
  /**
   * Analyze Cyber-Safety status
   * @param {Object} cyberSafetyData - Cyber-Safety data
   * @returns {Object} - Cyber-Safety status
   * @private
   */
  _analyzeCyberSafetyStatus(cyberSafetyData) {
    // Count controls by status
    const statusCounts = {
      implemented: 0,
      partial: 0,
      'not-implemented': 0
    };
    
    if (cyberSafetyData.controls) {
      cyberSafetyData.controls.forEach(control => {
        statusCounts[control.status] = (statusCounts[control.status] || 0) + 1;
      });
    }
    
    // Calculate Cyber-Safety score
    const totalControls = cyberSafetyData.controls ? cyberSafetyData.controls.length : 0;
    const cyberSafetyScore = totalControls > 0 ?
      (statusCounts.implemented + 0.5 * statusCounts.partial) / totalControls : 0;
    
    // Determine Cyber-Safety level
    let cyberSafetyLevel;
    if (cyberSafetyScore >= 0.9) {
      cyberSafetyLevel = 'excellent';
    } else if (cyberSafetyScore >= 0.7) {
      cyberSafetyLevel = 'good';
    } else if (cyberSafetyScore >= 0.5) {
      cyberSafetyLevel = 'moderate';
    } else if (cyberSafetyScore >= 0.3) {
      cyberSafetyLevel = 'poor';
    } else {
      cyberSafetyLevel = 'critical';
    }
    
    return {
      score: cyberSafetyScore,
      level: cyberSafetyLevel,
      statusCounts,
      totalControls
    };
  }
  
  /**
   * Identify improvement areas
   * @param {Object} complianceStatus - Compliance status
   * @param {Object} gcpStatus - GCP status
   * @param {Object} cyberSafetyStatus - Cyber-Safety status
   * @param {Object} contributions - Component contributions
   * @returns {Array} - Improvement areas
   * @private
   */
  _identifyImprovementAreas(complianceStatus, gcpStatus, cyberSafetyStatus, contributions) {
    const improvementAreas = [];
    
    // Check compliance status
    if (complianceStatus.level === 'critical' || complianceStatus.level === 'poor') {
      improvementAreas.push({
        area: 'compliance',
        priority: complianceStatus.level === 'critical' ? 'critical' : 'high',
        description: `Compliance level is ${complianceStatus.level} with ${complianceStatus.statusCounts['non-compliant']} non-compliant controls`,
        contribution: contributions.nist
      });
    }
    
    // Check GCP status
    if (gcpStatus.level === 'critical' || gcpStatus.level === 'poor') {
      improvementAreas.push({
        area: 'gcp',
        priority: gcpStatus.level === 'critical' ? 'critical' : 'high',
        description: `GCP integration level is ${gcpStatus.level} with ${gcpStatus.statusCounts['non-optimal']} non-optimal services`,
        contribution: contributions.gcp
      });
    }
    
    // Check Cyber-Safety status
    if (cyberSafetyStatus.level === 'critical' || cyberSafetyStatus.level === 'poor') {
      improvementAreas.push({
        area: 'cyberSafety',
        priority: cyberSafetyStatus.level === 'critical' ? 'critical' : 'high',
        description: `Cyber-Safety level is ${cyberSafetyStatus.level} with ${cyberSafetyStatus.statusCounts['not-implemented']} not implemented controls`,
        contribution: contributions.cyberSafety
      });
    }
    
    // Check tensor integration
    if (contributions.tensorProduct < 10) {
      improvementAreas.push({
        area: 'tensorIntegration',
        priority: 'medium',
        description: 'Tensor integration between NIST and GCP is suboptimal',
        contribution: contributions.tensorProduct
      });
    }
    
    // Check fusion value
    if (contributions.fusionValue < 10) {
      improvementAreas.push({
        area: 'fusion',
        priority: 'medium',
        description: 'Fusion between tensor product and Cyber-Safety is suboptimal',
        contribution: contributions.fusionValue
      });
    }
    
    // Sort by priority and contribution
    improvementAreas.sort((a, b) => {
      const priorityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
      const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
      
      if (priorityDiff !== 0) {
        return priorityDiff;
      }
      
      return b.contribution - a.contribution;
    });
    
    return improvementAreas;
  }
  
  /**
   * Generate recommendations
   * @param {Array} improvementAreas - Improvement areas
   * @param {Array} remediationActions - Remediation actions
   * @returns {Array} - Recommendations
   * @private
   */
  _generateRecommendations(improvementAreas, remediationActions) {
    const recommendations = [];
    
    // Map improvement areas to remediation actions
    improvementAreas.forEach(area => {
      const matchingActions = remediationActions.filter(action => {
        if (area.area === 'compliance' && action.type === 'compliance') {
          return true;
        }
        
        if (area.area === 'gcp' && action.type === 'gcp') {
          return true;
        }
        
        if (area.area === 'cyberSafety' && action.type === 'cyber-safety') {
          return true;
        }
        
        if ((area.area === 'tensorIntegration' || area.area === 'fusion') && 
            (action.description.includes('tensor') || action.description.includes('fusion'))) {
          return true;
        }
        
        return false;
      });
      
      // Add top matching actions to recommendations
      matchingActions.slice(0, 2).forEach(action => {
        recommendations.push({
          area: area.area,
          priority: action.priority,
          action: action.title,
          description: action.description,
          automationPotential: action.automationPotential,
          estimatedEffort: action.estimatedEffort
        });
      });
    });
    
    // Ensure we have at least 3 recommendations
    if (recommendations.length < 3 && remediationActions.length > 0) {
      const additionalActions = remediationActions
        .filter(action => !recommendations.some(rec => rec.action === action.title))
        .slice(0, 3 - recommendations.length);
      
      additionalActions.forEach(action => {
        recommendations.push({
          area: action.type,
          priority: action.priority,
          action: action.title,
          description: action.description,
          automationPotential: action.automationPotential,
          estimatedEffort: action.estimatedEffort
        });
      });
    }
    
    // Sort by priority
    recommendations.sort((a, b) => {
      const priorityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });
    
    return recommendations;
  }
}

module.exports = CSDEDirectML;
