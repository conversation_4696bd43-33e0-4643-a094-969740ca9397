/**
 * Domain Translation Layer
 *
 * This module implements the Domain Translation Layer component of the Bridge.
 * It translates metrics and events between cyber, financial, and biological domains.
 */

const { EventEmitter } = require('events');
const { performance } = require('perf_hooks');

/**
 * DomainTranslationLayer class
 */
class DomainTranslationLayer extends EventEmitter {
  /**
   * Create a new DomainTranslationLayer instance
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();

    this.options = {
      enableLogging: true,
      enableMetrics: true,
      translationMappings: {
        // Default mappings between domains
        cyberToFinancial: new Map([
          ['policy_entropy', 'transaction_entropy'],
          ['audit_entropy', 'attack_surface_coherence'],
          ['regulatory_entropy', 'market_stress']
        ]),
        cyberToBiological: new Map([
          ['policy_entropy', 'telomere_length'],
          ['audit_entropy', 'mtor_activation'],
          ['regulatory_entropy', 'inflammation_level']
        ]),
        financialToCyber: new Map([
          ['transaction_entropy', 'policy_entropy'],
          ['attack_surface_coherence', 'audit_entropy'],
          ['market_stress', 'regulatory_entropy']
        ]),
        financialToBiological: new Map([
          ['transaction_entropy', 'telomere_length'],
          ['attack_surface_coherence', 'mtor_activation'],
          ['market_stress', 'inflammation_level']
        ]),
        biologicalToCyber: new Map([
          ['telomere_length', 'policy_entropy'],
          ['mtor_activation', 'audit_entropy'],
          ['inflammation_level', 'regulatory_entropy']
        ]),
        biologicalToFinancial: new Map([
          ['telomere_length', 'transaction_entropy'],
          ['mtor_activation', 'attack_surface_coherence'],
          ['inflammation_level', 'market_stress']
        ])
      },
      ...options
    };

    // Initialize state
    this.state = {
      translationCache: new Map(), // Cache for recent translations
      translationHistory: [], // History of translations
      domainCorrelations: {
        cyberFinancial: 0.5,
        cyberBiological: 0.3,
        financialBiological: 0.4
      }
    };

    // Initialize metrics
    this.metrics = {
      processingTimeMs: 0,
      totalTranslations: 0,
      cacheHits: 0,
      cacheMisses: 0,
      translationsByDomain: {
        cyberToFinancial: 0,
        cyberToBiological: 0,
        financialToCyber: 0,
        financialToBiological: 0,
        biologicalToCyber: 0,
        biologicalToFinancial: 0
      }
    };

    if (this.options.enableLogging) {
      console.log('DomainTranslationLayer initialized');
    }
  }

  /**
   * Translate a metric from one domain to another
   * @param {string} sourceDomain - Source domain (cyber, financial, biological)
   * @param {string} targetDomain - Target domain (cyber, financial, biological)
   * @param {string} sourceMetric - Source metric name
   * @param {number} sourceValue - Source metric value
   * @returns {Object} - Translation result
   */
  translateMetric(sourceDomain, targetDomain, sourceMetric, sourceValue) {
    const startTime = performance.now();

    // Validate domains
    if (!['cyber', 'financial', 'biological'].includes(sourceDomain)) {
      throw new Error(`Invalid source domain: ${sourceDomain}`);
    }

    if (!['cyber', 'financial', 'biological'].includes(targetDomain)) {
      throw new Error(`Invalid target domain: ${targetDomain}`);
    }

    if (sourceDomain === targetDomain) {
      return {
        sourceDomain,
        targetDomain,
        sourceMetric,
        targetMetric: sourceMetric,
        sourceValue,
        targetValue: sourceValue,
        confidence: 1.0,
        timestamp: Date.now()
      };
    }

    // Check cache
    const cacheKey = `${sourceDomain}:${targetDomain}:${sourceMetric}:${sourceValue}`;
    if (this.state.translationCache.has(cacheKey)) {
      this.metrics.cacheHits++;
      return this.state.translationCache.get(cacheKey);
    }

    this.metrics.cacheMisses++;

    // Get mapping key
    const mappingKey = `${sourceDomain}To${targetDomain.charAt(0).toUpperCase() + targetDomain.slice(1)}`;

    // Get mapping
    const mapping = this.options.translationMappings[mappingKey];
    if (!mapping) {
      throw new Error(`No mapping found for ${sourceDomain} to ${targetDomain}`);
    }

    // Get target metric
    const targetMetric = mapping.get(sourceMetric);
    if (!targetMetric) {
      throw new Error(`No mapping found for metric ${sourceMetric} from ${sourceDomain} to ${targetDomain}`);
    }

    // Calculate target value
    let targetValue;
    let confidence;

    // Apply domain-specific translation logic
    const translationKey = `${sourceDomain}To${targetDomain.charAt(0).toUpperCase() + targetDomain.slice(1)}`;

    switch (translationKey) {
      case 'cyberToFinancial':
        targetValue = this._translateCyberToFinancial(sourceMetric, sourceValue);
        confidence = this.state.domainCorrelations.cyberFinancial;
        this.metrics.translationsByDomain.cyberToFinancial++;
        break;

      case 'cyberToBiological':
        targetValue = this._translateCyberToBiological(sourceMetric, sourceValue);
        confidence = this.state.domainCorrelations.cyberBiological;
        this.metrics.translationsByDomain.cyberToBiological++;
        break;

      case 'financialToCyber':
        targetValue = this._translateFinancialToCyber(sourceMetric, sourceValue);
        confidence = this.state.domainCorrelations.cyberFinancial;
        this.metrics.translationsByDomain.financialToCyber++;
        break;

      case 'financialToBiological':
        targetValue = this._translateFinancialToBiological(sourceMetric, sourceValue);
        confidence = this.state.domainCorrelations.financialBiological;
        this.metrics.translationsByDomain.financialToBiological++;
        break;

      case 'biologicalToCyber':
        targetValue = this._translateBiologicalToCyber(sourceMetric, sourceValue);
        confidence = this.state.domainCorrelations.cyberBiological;
        this.metrics.translationsByDomain.biologicalToCyber++;
        break;

      case 'biologicalToFinancial':
        targetValue = this._translateBiologicalToFinancial(sourceMetric, sourceValue);
        confidence = this.state.domainCorrelations.financialBiological;
        this.metrics.translationsByDomain.biologicalToFinancial++;
        break;

      default:
        throw new Error(`Unsupported translation: ${sourceDomain} to ${targetDomain} (key: ${translationKey})`);
    }

    // Create translation result
    const result = {
      sourceDomain,
      targetDomain,
      sourceMetric,
      targetMetric,
      sourceValue,
      targetValue,
      confidence,
      timestamp: Date.now()
    };

    // Add to cache
    this.state.translationCache.set(cacheKey, result);

    // Limit cache size
    if (this.state.translationCache.size > 1000) {
      // Remove oldest entry
      const oldestKey = this.state.translationCache.keys().next().value;
      this.state.translationCache.delete(oldestKey);
    }

    // Add to history
    this.state.translationHistory.push(result);

    // Limit history size
    if (this.state.translationHistory.length > 100) {
      this.state.translationHistory.shift();
    }

    // Update metrics
    this.metrics.processingTimeMs += performance.now() - startTime;
    this.metrics.totalTranslations++;

    // Emit event
    this.emit('metric-translated', result);

    if (this.options.enableLogging) {
      console.log(`DomainTranslationLayer: Translated ${sourceMetric} (${sourceValue.toFixed(4)}) from ${sourceDomain} to ${targetMetric} (${targetValue.toFixed(4)}) in ${targetDomain}`);
    }

    return result;
  }

  /**
   * Translate an event from one domain to another
   * @param {string} sourceDomain - Source domain (cyber, financial, biological)
   * @param {string} targetDomain - Target domain (cyber, financial, biological)
   * @param {Object} event - Event to translate
   * @returns {Object} - Translated event
   */
  translateEvent(sourceDomain, targetDomain, event) {
    const startTime = performance.now();

    // Validate domains
    if (!['cyber', 'financial', 'biological'].includes(sourceDomain)) {
      throw new Error(`Invalid source domain: ${sourceDomain}`);
    }

    if (!['cyber', 'financial', 'biological'].includes(targetDomain)) {
      throw new Error(`Invalid target domain: ${targetDomain}`);
    }

    if (sourceDomain === targetDomain) {
      return {
        ...event,
        domain: targetDomain,
        confidence: 1.0,
        translatedAt: Date.now()
      };
    }

    // Create translated event
    let translatedEvent;
    let confidence;

    // Apply domain-specific translation logic
    switch (`${sourceDomain}To${targetDomain}`) {
      case 'cyberToFinancial':
        translatedEvent = this._translateCyberEventToFinancial(event);
        confidence = this.state.domainCorrelations.cyberFinancial;
        this.metrics.translationsByDomain.cyberToFinancial++;
        break;

      case 'cyberToBiological':
        translatedEvent = this._translateCyberEventToBiological(event);
        confidence = this.state.domainCorrelations.cyberBiological;
        this.metrics.translationsByDomain.cyberToBiological++;
        break;

      case 'financialToCyber':
        translatedEvent = this._translateFinancialEventToCyber(event);
        confidence = this.state.domainCorrelations.cyberFinancial;
        this.metrics.translationsByDomain.financialToCyber++;
        break;

      case 'financialToBiological':
        translatedEvent = this._translateFinancialEventToBiological(event);
        confidence = this.state.domainCorrelations.financialBiological;
        this.metrics.translationsByDomain.financialToBiological++;
        break;

      case 'biologicalToCyber':
        translatedEvent = this._translateBiologicalEventToCyber(event);
        confidence = this.state.domainCorrelations.cyberBiological;
        this.metrics.translationsByDomain.biologicalToCyber++;
        break;

      case 'biologicalToFinancial':
        translatedEvent = this._translateBiologicalEventToFinancial(event);
        confidence = this.state.domainCorrelations.financialBiological;
        this.metrics.translationsByDomain.biologicalToFinancial++;
        break;

      default:
        throw new Error(`Unsupported translation: ${sourceDomain} to ${targetDomain}`);
    }

    // Add metadata
    translatedEvent = {
      ...translatedEvent,
      originalDomain: sourceDomain,
      domain: targetDomain,
      confidence,
      originalEvent: event,
      translatedAt: Date.now()
    };

    // Update metrics
    this.metrics.processingTimeMs += performance.now() - startTime;
    this.metrics.totalTranslations++;

    // Emit event
    this.emit('event-translated', translatedEvent);

    if (this.options.enableLogging) {
      console.log(`DomainTranslationLayer: Translated event from ${sourceDomain} to ${targetDomain}`);
    }

    return translatedEvent;
  }

  /**
   * Update domain correlations
   * @param {Object} correlations - Domain correlations
   * @returns {Object} - Updated correlations
   */
  updateDomainCorrelations(correlations) {
    if (!correlations || typeof correlations !== 'object') {
      throw new Error('Correlations must be an object');
    }

    const {
      cyberFinancial = this.state.domainCorrelations.cyberFinancial,
      cyberBiological = this.state.domainCorrelations.cyberBiological,
      financialBiological = this.state.domainCorrelations.financialBiological
    } = correlations;

    // Update state
    this.state.domainCorrelations = {
      cyberFinancial: this._clamp(cyberFinancial),
      cyberBiological: this._clamp(cyberBiological),
      financialBiological: this._clamp(financialBiological)
    };

    // Emit update event
    this.emit('correlation-update', {
      domainCorrelations: this.state.domainCorrelations,
      timestamp: Date.now()
    });

    return this.state.domainCorrelations;
  }

  /**
   * Get domain correlations
   * @returns {Object} - Domain correlations
   */
  getDomainCorrelations() {
    return { ...this.state.domainCorrelations };
  }

  /**
   * Get translation history
   * @param {number} limit - Maximum number of history items to return
   * @returns {Array} - Translation history
   */
  getTranslationHistory(limit = 10) {
    return this.state.translationHistory.slice(0, limit);
  }

  /**
   * Get metrics
   * @returns {Object} - Metrics
   */
  getMetrics() {
    return { ...this.metrics };
  }

  /**
   * Translate cyber metric to financial domain
   * @param {string} metric - Metric name
   * @param {number} value - Metric value
   * @returns {number} - Translated value
   * @private
   */
  _translateCyberToFinancial(metric, value) {
    switch (metric) {
      case 'policy_entropy':
        // Higher policy entropy -> higher transaction entropy
        return value;

      case 'audit_entropy':
        // Higher audit entropy -> lower attack surface coherence
        return 1 - value;

      case 'regulatory_entropy':
        // Higher regulatory entropy -> higher market stress
        return value;

      default:
        // Default 1:1 mapping
        return value;
    }
  }

  /**
   * Translate cyber metric to biological domain
   * @param {string} metric - Metric name
   * @param {number} value - Metric value
   * @returns {number} - Translated value
   * @private
   */
  _translateCyberToBiological(metric, value) {
    switch (metric) {
      case 'policy_entropy':
        // Higher policy entropy -> lower telomere length
        return 1 - value;

      case 'audit_entropy':
        // Higher audit entropy -> higher mTOR activation
        return value;

      case 'regulatory_entropy':
        // Higher regulatory entropy -> higher inflammation
        return value;

      default:
        // Default 1:1 mapping
        return value;
    }
  }

  /**
   * Translate financial metric to cyber domain
   * @param {string} metric - Metric name
   * @param {number} value - Metric value
   * @returns {number} - Translated value
   * @private
   */
  _translateFinancialToCyber(metric, value) {
    switch (metric) {
      case 'transaction_entropy':
        // Higher transaction entropy -> higher policy entropy
        return value;

      case 'attack_surface_coherence':
        // Higher attack surface coherence -> lower audit entropy
        return 1 - value;

      case 'market_stress':
        // Higher market stress -> higher regulatory entropy
        return value;

      default:
        // Default 1:1 mapping
        return value;
    }
  }

  /**
   * Translate financial metric to biological domain
   * @param {string} metric - Metric name
   * @param {number} value - Metric value
   * @returns {number} - Translated value
   * @private
   */
  _translateFinancialToBiological(metric, value) {
    switch (metric) {
      case 'transaction_entropy':
        // Higher transaction entropy -> lower telomere length
        return 1 - value;

      case 'attack_surface_coherence':
        // Higher attack surface coherence -> lower mTOR activation
        return 1 - value;

      case 'market_stress':
        // Higher market stress -> higher inflammation
        return value;

      default:
        // Default 1:1 mapping
        return value;
    }
  }

  /**
   * Translate biological metric to cyber domain
   * @param {string} metric - Metric name
   * @param {number} value - Metric value
   * @returns {number} - Translated value
   * @private
   */
  _translateBiologicalToCyber(metric, value) {
    switch (metric) {
      case 'telomere_length':
        // Higher telomere length -> lower policy entropy
        return 1 - value;

      case 'mtor_activation':
        // Higher mTOR activation -> higher audit entropy
        return value;

      case 'inflammation_level':
        // Higher inflammation -> higher regulatory entropy
        return value;

      default:
        // Default 1:1 mapping
        return value;
    }
  }

  /**
   * Translate biological metric to financial domain
   * @param {string} metric - Metric name
   * @param {number} value - Metric value
   * @returns {number} - Translated value
   * @private
   */
  _translateBiologicalToFinancial(metric, value) {
    switch (metric) {
      case 'telomere_length':
        // Higher telomere length -> lower transaction entropy
        return 1 - value;

      case 'mtor_activation':
        // Higher mTOR activation -> lower attack surface coherence
        return 1 - value;

      case 'inflammation_level':
        // Higher inflammation -> higher market stress
        return value;

      default:
        // Default 1:1 mapping
        return value;
    }
  }

  /**
   * Translate cyber event to financial domain
   * @param {Object} event - Cyber event
   * @returns {Object} - Financial event
   * @private
   */
  _translateCyberEventToFinancial(event) {
    // Create base translated event
    const translatedEvent = {
      id: `fin-${event.id || Date.now()}`,
      timestamp: event.timestamp || Date.now(),
      description: event.description || 'Translated cyber event'
    };

    // Apply event-specific translation logic
    switch (event.type) {
      case 'policy_violation':
        return {
          ...translatedEvent,
          type: 'transaction_anomaly',
          severity: event.severity,
          details: {
            source: 'cyber_policy_violation',
            originalType: event.type,
            riskLevel: event.details?.riskLevel || 'medium'
          }
        };

      case 'audit_finding':
        return {
          ...translatedEvent,
          type: 'vulnerability_detected',
          severity: event.severity,
          details: {
            source: 'cyber_audit_finding',
            originalType: event.type,
            impact: event.details?.impact || 'medium'
          }
        };

      case 'regulatory_change':
        return {
          ...translatedEvent,
          type: 'market_event',
          severity: event.severity,
          details: {
            source: 'cyber_regulatory_change',
            originalType: event.type,
            scope: event.details?.scope || 'local'
          }
        };

      default:
        return {
          ...translatedEvent,
          type: 'generic_financial_event',
          severity: event.severity || 'medium',
          details: {
            source: 'cyber_event',
            originalType: event.type
          }
        };
    }
  }

  /**
   * Translate cyber event to biological domain
   * @param {Object} event - Cyber event
   * @returns {Object} - Biological event
   * @private
   */
  _translateCyberEventToBiological(event) {
    // Create base translated event
    const translatedEvent = {
      id: `bio-${event.id || Date.now()}`,
      timestamp: event.timestamp || Date.now(),
      description: event.description || 'Translated cyber event'
    };

    // Apply event-specific translation logic
    switch (event.type) {
      case 'policy_violation':
        return {
          ...translatedEvent,
          type: 'telomere_stress',
          severity: event.severity,
          details: {
            source: 'cyber_policy_violation',
            originalType: event.type,
            duration: 24 // hours
          }
        };

      case 'audit_finding':
        return {
          ...translatedEvent,
          type: 'mtor_modulation',
          severity: event.severity,
          details: {
            source: 'cyber_audit_finding',
            originalType: event.type,
            effect: 'activation'
          }
        };

      case 'regulatory_change':
        return {
          ...translatedEvent,
          type: 'inflammation_trigger',
          severity: event.severity,
          details: {
            source: 'cyber_regulatory_change',
            originalType: event.type,
            duration: 48 // hours
          }
        };

      default:
        return {
          ...translatedEvent,
          type: 'generic_biological_event',
          severity: event.severity || 'medium',
          details: {
            source: 'cyber_event',
            originalType: event.type
          }
        };
    }
  }

  /**
   * Translate financial event to cyber domain
   * @param {Object} event - Financial event
   * @returns {Object} - Cyber event
   * @private
   */
  _translateFinancialEventToCyber(event) {
    // Create base translated event
    const translatedEvent = {
      id: `cyb-${event.id || Date.now()}`,
      timestamp: event.timestamp || Date.now(),
      description: event.description || 'Translated financial event'
    };

    // Apply event-specific translation logic
    switch (event.type) {
      case 'transaction_anomaly':
        return {
          ...translatedEvent,
          type: 'policy_violation',
          severity: event.severity,
          details: {
            source: 'financial_transaction_anomaly',
            originalType: event.type,
            riskLevel: event.details?.riskLevel || 'medium'
          }
        };

      case 'vulnerability_detected':
        return {
          ...translatedEvent,
          type: 'audit_finding',
          severity: event.severity,
          details: {
            source: 'financial_vulnerability',
            originalType: event.type,
            impact: event.details?.impact || 'medium'
          }
        };

      case 'market_event':
        return {
          ...translatedEvent,
          type: 'regulatory_change',
          severity: event.severity,
          details: {
            source: 'financial_market_event',
            originalType: event.type,
            scope: event.details?.scope || 'local'
          }
        };

      default:
        return {
          ...translatedEvent,
          type: 'generic_cyber_event',
          severity: event.severity || 'medium',
          details: {
            source: 'financial_event',
            originalType: event.type
          }
        };
    }
  }

  /**
   * Translate financial event to biological domain
   * @param {Object} event - Financial event
   * @returns {Object} - Biological event
   * @private
   */
  _translateFinancialEventToBiological(event) {
    // Create base translated event
    const translatedEvent = {
      id: `bio-${event.id || Date.now()}`,
      timestamp: event.timestamp || Date.now(),
      description: event.description || 'Translated financial event'
    };

    // Apply event-specific translation logic
    switch (event.type) {
      case 'transaction_anomaly':
        return {
          ...translatedEvent,
          type: 'telomere_stress',
          severity: event.severity,
          details: {
            source: 'financial_transaction_anomaly',
            originalType: event.type,
            duration: 24 // hours
          }
        };

      case 'vulnerability_detected':
        return {
          ...translatedEvent,
          type: 'mtor_modulation',
          severity: event.severity,
          details: {
            source: 'financial_vulnerability',
            originalType: event.type,
            effect: 'activation'
          }
        };

      case 'market_event':
        return {
          ...translatedEvent,
          type: 'inflammation_trigger',
          severity: event.severity,
          details: {
            source: 'financial_market_event',
            originalType: event.type,
            duration: 48 // hours
          }
        };

      default:
        return {
          ...translatedEvent,
          type: 'generic_biological_event',
          severity: event.severity || 'medium',
          details: {
            source: 'financial_event',
            originalType: event.type
          }
        };
    }
  }

  /**
   * Translate biological event to cyber domain
   * @param {Object} event - Biological event
   * @returns {Object} - Cyber event
   * @private
   */
  _translateBiologicalEventToCyber(event) {
    // Create base translated event
    const translatedEvent = {
      id: `cyb-${event.id || Date.now()}`,
      timestamp: event.timestamp || Date.now(),
      description: event.description || 'Translated biological event'
    };

    // Apply event-specific translation logic
    switch (event.type) {
      case 'telomere_stress':
        return {
          ...translatedEvent,
          type: 'policy_violation',
          severity: event.severity,
          details: {
            source: 'biological_telomere_stress',
            originalType: event.type,
            riskLevel: event.details?.riskLevel || 'medium'
          }
        };

      case 'mtor_modulation':
        return {
          ...translatedEvent,
          type: 'audit_finding',
          severity: event.severity,
          details: {
            source: 'biological_mtor_modulation',
            originalType: event.type,
            impact: event.details?.impact || 'medium'
          }
        };

      case 'inflammation_trigger':
        return {
          ...translatedEvent,
          type: 'regulatory_change',
          severity: event.severity,
          details: {
            source: 'biological_inflammation',
            originalType: event.type,
            scope: event.details?.scope || 'local'
          }
        };

      default:
        return {
          ...translatedEvent,
          type: 'generic_cyber_event',
          severity: event.severity || 'medium',
          details: {
            source: 'biological_event',
            originalType: event.type
          }
        };
    }
  }

  /**
   * Translate biological event to financial domain
   * @param {Object} event - Biological event
   * @returns {Object} - Financial event
   * @private
   */
  _translateBiologicalEventToFinancial(event) {
    // Create base translated event
    const translatedEvent = {
      id: `fin-${event.id || Date.now()}`,
      timestamp: event.timestamp || Date.now(),
      description: event.description || 'Translated biological event'
    };

    // Apply event-specific translation logic
    switch (event.type) {
      case 'telomere_stress':
        return {
          ...translatedEvent,
          type: 'transaction_anomaly',
          severity: event.severity,
          details: {
            source: 'biological_telomere_stress',
            originalType: event.type,
            riskLevel: event.details?.riskLevel || 'medium'
          }
        };

      case 'mtor_modulation':
        return {
          ...translatedEvent,
          type: 'vulnerability_detected',
          severity: event.severity,
          details: {
            source: 'biological_mtor_modulation',
            originalType: event.type,
            impact: event.details?.impact || 'medium'
          }
        };

      case 'inflammation_trigger':
        return {
          ...translatedEvent,
          type: 'market_event',
          severity: event.severity,
          details: {
            source: 'biological_inflammation',
            originalType: event.type,
            scope: event.details?.scope || 'local'
          }
        };

      default:
        return {
          ...translatedEvent,
          type: 'generic_financial_event',
          severity: event.severity || 'medium',
          details: {
            source: 'biological_event',
            originalType: event.type
          }
        };
    }
  }

  /**
   * Clamp value between 0 and 1
   * @param {number} value - Value to clamp
   * @returns {number} - Clamped value
   * @private
   */
  _clamp(value) {
    return Math.max(0, Math.min(1, value));
  }
}

module.exports = DomainTranslationLayer;
