/**
 * Authentication Audit Service
 * 
 * This service handles audit logging specifically for authentication events.
 */

const AuditService = require('./AuditService');
const path = require('path');

class AuthAuditService {
  constructor(dataDir = path.join(__dirname, '../data')) {
    this.auditService = new AuditService(dataDir);
    this.resourceType = 'auth';
  }

  /**
   * Log a login attempt
   * 
   * @param {Object} data - Login attempt data
   * @param {string} data.username - Username used for login
   * @param {string} data.ip - IP address of the client
   * @param {string} data.userAgent - User agent of the client
   * @param {boolean} data.success - Whether the login was successful
   * @param {string} data.userId - User ID (if login was successful)
   * @param {string} data.reason - Reason for failure (if login failed)
   * @param {string} data.method - Authentication method (password, oauth2, etc.)
   * @param {Object} data.details - Additional details
   */
  async logLoginAttempt(data) {
    const auditData = {
      userId: data.userId || null,
      action: 'LOGIN',
      resourceType: this.resourceType,
      resourceId: data.username,
      details: {
        method: data.method || 'password',
        success: data.success,
        reason: data.reason || null,
        ...data.details
      },
      ip: data.ip,
      userAgent: data.userAgent,
      status: data.success ? 'success' : 'failure',
      teamId: data.teamId || null,
      environmentId: data.environmentId || null,
      tenantId: data.tenantId || null
    };

    return this.auditService.logEvent(auditData);
  }

  /**
   * Log a logout event
   * 
   * @param {Object} data - Logout data
   * @param {string} data.userId - User ID
   * @param {string} data.username - Username
   * @param {string} data.ip - IP address of the client
   * @param {string} data.userAgent - User agent of the client
   * @param {Object} data.details - Additional details
   */
  async logLogout(data) {
    const auditData = {
      userId: data.userId,
      action: 'LOGOUT',
      resourceType: this.resourceType,
      resourceId: data.username,
      details: data.details || {},
      ip: data.ip,
      userAgent: data.userAgent,
      status: 'success',
      teamId: data.teamId || null,
      environmentId: data.environmentId || null,
      tenantId: data.tenantId || null
    };

    return this.auditService.logEvent(auditData);
  }

  /**
   * Log a registration event
   * 
   * @param {Object} data - Registration data
   * @param {string} data.userId - User ID
   * @param {string} data.username - Username
   * @param {string} data.ip - IP address of the client
   * @param {string} data.userAgent - User agent of the client
   * @param {boolean} data.success - Whether the registration was successful
   * @param {string} data.reason - Reason for failure (if registration failed)
   * @param {Object} data.details - Additional details
   */
  async logRegistration(data) {
    const auditData = {
      userId: data.userId || null,
      action: 'REGISTER',
      resourceType: this.resourceType,
      resourceId: data.username,
      details: {
        success: data.success,
        reason: data.reason || null,
        ...data.details
      },
      ip: data.ip,
      userAgent: data.userAgent,
      status: data.success ? 'success' : 'failure',
      teamId: data.teamId || null,
      environmentId: data.environmentId || null,
      tenantId: data.tenantId || null
    };

    return this.auditService.logEvent(auditData);
  }

  /**
   * Log a password change event
   * 
   * @param {Object} data - Password change data
   * @param {string} data.userId - User ID
   * @param {string} data.username - Username
   * @param {string} data.ip - IP address of the client
   * @param {string} data.userAgent - User agent of the client
   * @param {boolean} data.success - Whether the password change was successful
   * @param {string} data.reason - Reason for failure (if password change failed)
   * @param {Object} data.details - Additional details
   */
  async logPasswordChange(data) {
    const auditData = {
      userId: data.userId,
      action: 'PASSWORD_CHANGE',
      resourceType: this.resourceType,
      resourceId: data.username,
      details: {
        success: data.success,
        reason: data.reason || null,
        ...data.details
      },
      ip: data.ip,
      userAgent: data.userAgent,
      status: data.success ? 'success' : 'failure',
      teamId: data.teamId || null,
      environmentId: data.environmentId || null,
      tenantId: data.tenantId || null
    };

    return this.auditService.logEvent(auditData);
  }

  /**
   * Log a password reset request event
   * 
   * @param {Object} data - Password reset request data
   * @param {string} data.username - Username
   * @param {string} data.ip - IP address of the client
   * @param {string} data.userAgent - User agent of the client
   * @param {boolean} data.success - Whether the password reset request was successful
   * @param {string} data.reason - Reason for failure (if password reset request failed)
   * @param {Object} data.details - Additional details
   */
  async logPasswordResetRequest(data) {
    const auditData = {
      userId: data.userId || null,
      action: 'PASSWORD_RESET_REQUEST',
      resourceType: this.resourceType,
      resourceId: data.username,
      details: {
        success: data.success,
        reason: data.reason || null,
        ...data.details
      },
      ip: data.ip,
      userAgent: data.userAgent,
      status: data.success ? 'success' : 'failure',
      teamId: data.teamId || null,
      environmentId: data.environmentId || null,
      tenantId: data.tenantId || null
    };

    return this.auditService.logEvent(auditData);
  }

  /**
   * Log a password reset event
   * 
   * @param {Object} data - Password reset data
   * @param {string} data.userId - User ID
   * @param {string} data.username - Username
   * @param {string} data.ip - IP address of the client
   * @param {string} data.userAgent - User agent of the client
   * @param {boolean} data.success - Whether the password reset was successful
   * @param {string} data.reason - Reason for failure (if password reset failed)
   * @param {Object} data.details - Additional details
   */
  async logPasswordReset(data) {
    const auditData = {
      userId: data.userId || null,
      action: 'PASSWORD_RESET',
      resourceType: this.resourceType,
      resourceId: data.username,
      details: {
        success: data.success,
        reason: data.reason || null,
        ...data.details
      },
      ip: data.ip,
      userAgent: data.userAgent,
      status: data.success ? 'success' : 'failure',
      teamId: data.teamId || null,
      environmentId: data.environmentId || null,
      tenantId: data.tenantId || null
    };

    return this.auditService.logEvent(auditData);
  }

  /**
   * Log a token refresh event
   * 
   * @param {Object} data - Token refresh data
   * @param {string} data.userId - User ID
   * @param {string} data.username - Username
   * @param {string} data.ip - IP address of the client
   * @param {string} data.userAgent - User agent of the client
   * @param {boolean} data.success - Whether the token refresh was successful
   * @param {string} data.reason - Reason for failure (if token refresh failed)
   * @param {Object} data.details - Additional details
   */
  async logTokenRefresh(data) {
    const auditData = {
      userId: data.userId || null,
      action: 'TOKEN_REFRESH',
      resourceType: this.resourceType,
      resourceId: data.username || data.userId,
      details: {
        success: data.success,
        reason: data.reason || null,
        ...data.details
      },
      ip: data.ip,
      userAgent: data.userAgent,
      status: data.success ? 'success' : 'failure',
      teamId: data.teamId || null,
      environmentId: data.environmentId || null,
      tenantId: data.tenantId || null
    };

    return this.auditService.logEvent(auditData);
  }

  /**
   * Log a two-factor authentication event
   * 
   * @param {Object} data - Two-factor authentication data
   * @param {string} data.userId - User ID
   * @param {string} data.username - Username
   * @param {string} data.ip - IP address of the client
   * @param {string} data.userAgent - User agent of the client
   * @param {string} data.action - Two-factor action (setup, verify, disable)
   * @param {boolean} data.success - Whether the two-factor action was successful
   * @param {string} data.reason - Reason for failure (if two-factor action failed)
   * @param {Object} data.details - Additional details
   */
  async logTwoFactorAuth(data) {
    const auditData = {
      userId: data.userId,
      action: `2FA_${data.action.toUpperCase()}`,
      resourceType: this.resourceType,
      resourceId: data.username,
      details: {
        success: data.success,
        reason: data.reason || null,
        ...data.details
      },
      ip: data.ip,
      userAgent: data.userAgent,
      status: data.success ? 'success' : 'failure',
      teamId: data.teamId || null,
      environmentId: data.environmentId || null,
      tenantId: data.tenantId || null
    };

    return this.auditService.logEvent(auditData);
  }

  /**
   * Get authentication audit logs
   * 
   * @param {Object} filters - Filters to apply
   * @returns {Object} - Filtered audit logs
   */
  async getAuthAuditLogs(filters = {}) {
    // Add resourceType filter for auth events
    const authFilters = {
      ...filters,
      resourceType: this.resourceType
    };

    return this.auditService.getAuditLogs(authFilters);
  }
}

module.exports = AuthAuditService;
