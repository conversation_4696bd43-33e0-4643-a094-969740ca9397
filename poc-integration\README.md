# NovaLift-NovaCortex Proof-of-Concept Integration

This directory contains lightweight adapters that translate NovaLift events into NovaCortex ingest format, enabling bidirectional communication and handshake validation.

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│    NovaLift     │◄──►│   Translation   │◄──►│   NovaCortex    │
│    Events       │    │    Adapters     │    │    Ingest       │
│                 │    │                 │    │                 │
│ • Ψ-Score       │    │ • FastAPI       │    │ • Coherence     │
│ • Performance   │    │ • Go Service    │    │ • CASTL         │
│ • Optimization  │    │ • Event Bridge  │    │ • π-Rhythm      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📦 Components

### 1. Python FastAPI Adapter (`fastapi-adapter/`)
- REST API adapter for event translation
- Prometheus metrics integration  
- Health monitoring endpoints
- JSON schema validation

### 2. Go Microservice (`go-microservice/`)
- High-performance event processor
- gRPC and REST interfaces
- Built-in retry mechanisms
- Concurrent event handling

### 3. Event Bridge (`event-bridge/`)
- Real-time bidirectional messaging
- WebSocket connections
- Event queue management
- Message logging and replay

### 4. Integration Tests (`tests/`)
- End-to-end validation suite
- Performance benchmarking
- Handshake validation
- Message flow verification

## 🚀 Quick Start

1. **Start NovaCortex server:**
   ```bash
   cd ../../src/novacortex
   node server.js
   ```

2. **Start fusion server:**
   ```bash
   cd ../../src/novacortex-novalift-fusion
   node fusion-server.js
   ```

3. **Run Python adapter:**
   ```bash
   cd fastapi-adapter
   pip install -r requirements.txt
   python adapter.py
   ```

4. **Run integration tests:**
   ```bash
   cd tests
   python test_integration.py
   ```

## 📊 Event Translation Schema

### NovaLift → NovaCortex
```json
{
  "source": "novalift",
  "event_type": "optimization_complete",
  "data": {
    "psi_score": 2.45,
    "performance_multiplier": 3.31,
    "coherence_status": "HIGHLY_COHERENT"
  }
}
```

Translates to:
```json
{
  "coherence_context": {
    "performance_factor": 3.31,
    "consciousness_level": 0.82,
    "ethical_compliance_required": true
  }
}
```

### NovaCortex → NovaLift
```json
{
  "source": "novacortex", 
  "event_type": "coherence_update",
  "data": {
    "coherence_level": 0.96,
    "pi_rhythm_synchronized": true,
    "castl_clearance": "approved"
  }
}
```

Translates to:
```json
{
  "optimization_constraints": {
    "coherence_constraint": 0.96,
    "pi_sync": true,
    "consciousness_guided": true
  }
}
```

## 🔧 Configuration

All adapters support environment-based configuration:

```bash
export NOVACORTEX_URL=http://localhost:3010
export NOVALIFT_URL=http://localhost:3014
export FUSION_URL=http://localhost:3015
export ADAPTER_PORT=8080
export LOG_LEVEL=INFO
export METRICS_ENABLED=true
```

## 📈 Monitoring

Access adapter metrics at:
- FastAPI: `http://localhost:8080/metrics`
- Go Service: `http://localhost:8081/metrics`
- Event Bridge: `http://localhost:8082/metrics`

## 🧪 Testing

The integration includes comprehensive testing:
- Unit tests for each adapter
- Integration tests for end-to-end flow
- Performance benchmarks
- Error handling validation
- Failover scenarios

Run all tests:
```bash
./run_tests.sh
```

## 🔄 Event Flow Examples

See `examples/` directory for:
- Basic event translation
- Bidirectional handshake
- Error handling scenarios
- Performance optimization flows
- CASTL decision integration
