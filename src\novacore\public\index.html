<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NovaCore API Documentation</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            padding-top: 2rem;
            padding-bottom: 2rem;
        }
        .endpoint {
            margin-bottom: 1.5rem;
            padding: 1rem;
            border-radius: 0.25rem;
            background-color: #f8f9fa;
            border-left: 4px solid #0d6efd;
        }
        .method {
            font-weight: bold;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            display: inline-block;
            margin-right: 0.5rem;
        }
        .method-get {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        .method-post {
            background-color: #d4edda;
            color: #155724;
        }
        .method-put {
            background-color: #fff3cd;
            color: #856404;
        }
        .method-delete {
            background-color: #f8d7da;
            color: #721c24;
        }
        .path {
            font-family: monospace;
            font-size: 1.1rem;
        }
        .description {
            margin-top: 0.5rem;
            margin-bottom: 0.5rem;
        }
        .header {
            background-color: #0a84ff;
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        .header h1 {
            margin-bottom: 0.5rem;
        }
        .header p {
            opacity: 0.8;
            margin-bottom: 0;
        }
        .section {
            margin-bottom: 3rem;
        }
        .try-it {
            margin-top: 1rem;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1>NovaCore API</h1>
            <p>The Genesis Pairing of NovaSphere and NovaConnect</p>
        </div>
    </div>

    <div class="container">
        <div class="row">
            <div class="col-md-3">
                <div class="sticky-top pt-3">
                    <div class="list-group">
                        <a href="#overview" class="list-group-item list-group-item-action">Overview</a>
                        <a href="#evidence" class="list-group-item list-group-item-action">Evidence API</a>
                        <a href="#connectors" class="list-group-item list-group-item-action">Connectors API</a>
                        <a href="#jobs" class="list-group-item list-group-item-action">Jobs API</a>
                    </div>
                </div>
            </div>
            <div class="col-md-9">
                <section id="overview" class="section">
                    <h2>Overview</h2>
                    <p>
                        NovaCore is the foundational integration of NovaSphere (Evidence Collection System) and NovaConnect (Universal API Connector) 
                        in the NovaFuse ecosystem. It provides a comprehensive API for managing compliance evidence, data source connectors, and 
                        collection jobs.
                    </p>
                    <div class="alert alert-info">
                        <strong>Base URL:</strong> <code>http://localhost:5000/api</code>
                    </div>
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Authentication</h5>
                        </div>
                        <div class="card-body">
                            <p>
                                All API requests require authentication. Use the following header:
                            </p>
                            <pre><code>Authorization: Bearer YOUR_API_TOKEN</code></pre>
                            <p class="mb-0">
                                <small class="text-muted">Note: Authentication is not implemented in the current version.</small>
                            </p>
                        </div>
                    </div>
                </section>

                <section id="evidence" class="section">
                    <h2>Evidence API</h2>
                    <p>
                        The Evidence API allows you to manage compliance evidence, including creation, retrieval, versioning, and blockchain verification.
                    </p>

                    <div class="endpoint">
                        <div>
                            <span class="method method-get">GET</span>
                            <span class="path">/evidence</span>
                        </div>
                        <div class="description">Get all evidence with optional filtering</div>
                        <div class="try-it">
                            <a href="http://localhost:5000/api/evidence" target="_blank" class="btn btn-sm btn-outline-primary">Try it</a>
                        </div>
                    </div>

                    <div class="endpoint">
                        <div>
                            <span class="method method-get">GET</span>
                            <span class="path">/evidence/:id</span>
                        </div>
                        <div class="description">Get evidence by ID</div>
                    </div>

                    <div class="endpoint">
                        <div>
                            <span class="method method-post">POST</span>
                            <span class="path">/evidence</span>
                        </div>
                        <div class="description">Create new evidence</div>
                    </div>

                    <div class="endpoint">
                        <div>
                            <span class="method method-put">PUT</span>
                            <span class="path">/evidence/:id</span>
                        </div>
                        <div class="description">Update evidence</div>
                    </div>

                    <div class="endpoint">
                        <div>
                            <span class="method method-delete">DELETE</span>
                            <span class="path">/evidence/:id</span>
                        </div>
                        <div class="description">Delete evidence</div>
                    </div>

                    <div class="endpoint">
                        <div>
                            <span class="method method-post">POST</span>
                            <span class="path">/evidence/:id/versions</span>
                        </div>
                        <div class="description">Create new version of evidence</div>
                    </div>

                    <div class="endpoint">
                        <div>
                            <span class="method method-get">GET</span>
                            <span class="path">/evidence/:id/versions</span>
                        </div>
                        <div class="description">Get all versions of evidence</div>
                    </div>

                    <div class="endpoint">
                        <div>
                            <span class="method method-post">POST</span>
                            <span class="path">/evidence/:id/verify</span>
                        </div>
                        <div class="description">Verify evidence on blockchain</div>
                    </div>
                </section>

                <section id="connectors" class="section">
                    <h2>Connectors API</h2>
                    <p>
                        The Connectors API allows you to manage data source connectors, including creation, configuration, and activation.
                    </p>

                    <div class="endpoint">
                        <div>
                            <span class="method method-get">GET</span>
                            <span class="path">/connectors</span>
                        </div>
                        <div class="description">Get all connectors with optional filtering</div>
                        <div class="try-it">
                            <a href="http://localhost:5000/api/connectors" target="_blank" class="btn btn-sm btn-outline-primary">Try it</a>
                        </div>
                    </div>

                    <div class="endpoint">
                        <div>
                            <span class="method method-get">GET</span>
                            <span class="path">/connectors/:id</span>
                        </div>
                        <div class="description">Get connector by ID</div>
                    </div>

                    <div class="endpoint">
                        <div>
                            <span class="method method-post">POST</span>
                            <span class="path">/connectors</span>
                        </div>
                        <div class="description">Create new connector</div>
                    </div>

                    <div class="endpoint">
                        <div>
                            <span class="method method-put">PUT</span>
                            <span class="path">/connectors/:id</span>
                        </div>
                        <div class="description">Update connector</div>
                    </div>

                    <div class="endpoint">
                        <div>
                            <span class="method method-post">POST</span>
                            <span class="path">/connectors/:id/activate</span>
                        </div>
                        <div class="description">Activate connector</div>
                    </div>

                    <div class="endpoint">
                        <div>
                            <span class="method method-post">POST</span>
                            <span class="path">/connectors/:id/deactivate</span>
                        </div>
                        <div class="description">Deactivate connector</div>
                    </div>

                    <div class="endpoint">
                        <div>
                            <span class="method method-post">POST</span>
                            <span class="path">/connectors/:id/test</span>
                        </div>
                        <div class="description">Test connector connection</div>
                    </div>
                </section>

                <section id="jobs" class="section">
                    <h2>Jobs API</h2>
                    <p>
                        The Jobs API allows you to manage collection jobs, including creation, execution, and monitoring.
                    </p>

                    <div class="endpoint">
                        <div>
                            <span class="method method-get">GET</span>
                            <span class="path">/jobs</span>
                        </div>
                        <div class="description">Get all jobs with optional filtering</div>
                        <div class="try-it">
                            <a href="http://localhost:5000/api/jobs" target="_blank" class="btn btn-sm btn-outline-primary">Try it</a>
                        </div>
                    </div>

                    <div class="endpoint">
                        <div>
                            <span class="method method-get">GET</span>
                            <span class="path">/jobs/:id</span>
                        </div>
                        <div class="description">Get job by ID</div>
                    </div>

                    <div class="endpoint">
                        <div>
                            <span class="method method-post">POST</span>
                            <span class="path">/jobs/:id/execute</span>
                        </div>
                        <div class="description">Execute job</div>
                    </div>

                    <div class="endpoint">
                        <div>
                            <span class="method method-post">POST</span>
                            <span class="path">/jobs/:id/cancel</span>
                        </div>
                        <div class="description">Cancel job</div>
                    </div>
                </section>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
