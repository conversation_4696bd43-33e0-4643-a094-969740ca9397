'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { DollarSign, TrendingUp, TrendingDown, Zap, Brain, Activity, BarChart3 } from 'lucide-react'

interface MarketData {
  symbol: string
  price: number
  change: number
  psiConfidence: number
  volume: string
  prediction: 'BULLISH' | 'BEARISH' | 'NEUTRAL'
}

export default function FinancialStudio() {
  const [dailyProfit, setDailyProfit] = useState(2847314)
  const [consciousnessField, setConsciousnessField] = useState({
    psi: 0.847,
    phi: 0.764,
    theta: 0.692
  })
  const [marketData, setMarketData] = useState<MarketData[]>([])
  const [terminalOutput, setTerminalOutput] = useState<string[]>([
    'Financial Reality Studio v1.0.0-TRANSCENDENT',
    'Consciousness arbitrage protocols: ACTIVE',
    'SEC-proof algorithms: OPERATIONAL',
    'Multiverse backtesting: READY'
  ])
  const [selectedTimeline, setSelectedTimeline] = useState<string | null>(null)

  const timelines = [
    { id: 'alpha', name: 'Timeline α', returns: '+15.7%', risk: 'LOW' },
    { id: 'beta', name: 'Timeline β', returns: '+23.4%', risk: 'MODERATE' },
    { id: 'gamma', name: 'Timeline γ', returns: '+31.4%', risk: 'HIGH' },
    { id: 'optimal', name: 'Optimal Path', returns: '+47.2%', risk: 'CALCULATED' }
  ]

  const addTerminalLine = (line: string) => {
    setTerminalOutput(prev => [...prev.slice(-8), line])
  }

  useEffect(() => {
    // Initialize market data
    const symbols = ['SPY', 'QQQ', 'NVDA', 'TSLA', 'BTC-USD', 'AAPL', 'MSFT', 'GOOGL']
    const initialData = symbols.map(symbol => ({
      symbol,
      price: 100 + Math.random() * 500,
      change: (Math.random() - 0.5) * 10,
      psiConfidence: 70 + Math.random() * 30,
      volume: (Math.random() * 100 + 10).toFixed(1) + 'M',
      prediction: Math.random() > 0.5 ? 'BULLISH' : Math.random() > 0.25 ? 'BEARISH' : 'NEUTRAL'
    })) as MarketData[]
    
    setMarketData(initialData)

    // Update market data and consciousness field
    const interval = setInterval(() => {
      setMarketData(prev => prev.map(stock => ({
        ...stock,
        price: Math.max(10, stock.price + (Math.random() - 0.5) * 5),
        change: (Math.random() - 0.5) * 8,
        psiConfidence: Math.max(60, Math.min(100, stock.psiConfidence + (Math.random() - 0.5) * 5))
      })))

      setConsciousnessField(prev => ({
        psi: Math.max(0.7, Math.min(0.95, prev.psi + (Math.random() - 0.5) * 0.05)),
        phi: Math.max(0.7, Math.min(0.95, prev.phi + (Math.random() - 0.5) * 0.05)),
        theta: Math.max(0.7, Math.min(0.95, prev.theta + (Math.random() - 0.5) * 0.05))
      }))

      // Update daily profit
      setDailyProfit(prev => prev + Math.floor(Math.random() * 50000 + 10000))
    }, 4000)

    // Add random terminal messages
    const messageInterval = setInterval(() => {
      const messages = [
        '💰 Consciousness arbitrage opportunity detected',
        '⚡ Ψ-field spike in options market',
        '🌌 Multiverse convergence confirmed',
        '🧠 Hedge fund profit share incoming',
        '🔥 Volatility surface mapping: 97.25%',
        '🎯 SEC-proof algorithm: UNDETECTED'
      ]
      const randomMessage = messages[Math.floor(Math.random() * messages.length)]
      addTerminalLine(randomMessage)
    }, 6000)

    return () => {
      clearInterval(interval)
      clearInterval(messageInterval)
    }
  }, [])

  const executeArbitrage = () => {
    addTerminalLine('🚀 Executing consciousness arbitrage sequence...')
    setTimeout(() => {
      const profit = Math.floor(Math.random() * 100000 + 25000)
      addTerminalLine(`💰 Arbitrage complete: +$${profit.toLocaleString()} profit`)
      setDailyProfit(prev => prev + profit)
    }, 2000)
  }

  const deployMultiverseStrategy = () => {
    addTerminalLine('🌠 Deploying multiverse trading strategy...')
    setTimeout(() => {
      addTerminalLine('⚡ Negative-time trades executed: -3.107ms latency')
      addTerminalLine('🎯 Strategy deployed across 314 market nodes')
      const megaProfit = Math.floor(Math.random() * 500000 + 100000)
      setDailyProfit(prev => prev + megaProfit)
    }, 3000)
  }

  const selectTimeline = (timelineId: string) => {
    setSelectedTimeline(timelineId)
    const timeline = timelines.find(t => t.id === timelineId)
    addTerminalLine(`🌌 Selecting ${timeline?.name} for backtesting...`)
    setTimeout(() => {
      addTerminalLine(`✅ ${timeline?.name} analysis complete - ${timeline?.returns} projected`)
    }, 1500)
  }

  return (
    <div className="min-h-screen p-6 bg-black/10">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-8"
        >
          <h1 className="text-5xl font-bold mb-4 text-consciousness">
            💰 Financial Reality Studio
          </h1>
          <p className="text-xl text-white/70 mb-4">
            "Market manipulation through consciousness arbitrage"
          </p>
          <div className="text-lg text-green-400">
            Powered by HOD Patent Technology - Volatility Surface Consciousness Mapping
          </div>
        </motion.div>

        {/* Daily Profit Display */}
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="consciousness-card mb-8 text-center"
        >
          <div className="text-lg text-white/70 mb-2">Today's Consciousness Arbitrage Profits</div>
          <div className="text-5xl font-bold text-consciousness mb-4">
            ${dailyProfit.toLocaleString()}
          </div>
          <div className="text-white/60">
            20% of hedge fund profits + $10M/year central bank licensing
          </div>
        </motion.div>

        <div className="grid lg:grid-cols-3 gap-6">
          {/* Live Ψ-Field Trading Terminal */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="consciousness-card"
          >
            <h2 className="text-xl font-bold text-consciousness mb-6 flex items-center">
              <Brain className="w-6 h-6 mr-2" />
              Live Ψ-Field Trading Terminal
            </h2>
            
            <div className="bg-green-500/10 border border-green-500/30 rounded-lg p-4 mb-6">
              <div className="text-center mb-4">
                <div className="text-sm text-white/70">Global Market Consciousness</div>
              </div>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-cyan-400">Ψ (Spatial):</span>
                  <span className="text-white font-bold">{consciousnessField.psi.toFixed(3)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-purple-400">Φ (Temporal):</span>
                  <span className="text-white font-bold">{consciousnessField.phi.toFixed(3)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-yellow-400">Θ (Recursive):</span>
                  <span className="text-white font-bold">{consciousnessField.theta.toFixed(3)}</span>
                </div>
                <div className="w-full bg-black/30 rounded-full h-3 mt-4">
                  <div 
                    className="h-3 bg-gradient-to-r from-green-500 to-cyan-500 rounded-full transition-all duration-1000"
                    style={{ width: `${consciousnessField.psi * 100}%` }}
                  />
                </div>
              </div>
            </div>
            
            <div className="space-y-3 mb-6">
              {marketData.slice(0, 4).map((stock) => (
                <div key={stock.symbol} className="bg-black/30 rounded-lg p-3 border border-white/10">
                  <div className="flex justify-between items-center">
                    <div>
                      <div className="font-bold text-white">{stock.symbol}</div>
                      <div className="text-sm text-white/60">${stock.price.toFixed(2)}</div>
                    </div>
                    <div className="text-right">
                      <div className={`font-bold ${stock.change >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                        {stock.change >= 0 ? '+' : ''}{stock.change.toFixed(2)}%
                      </div>
                      <div className="text-xs text-cyan-400">
                        Ψ: {stock.psiConfidence.toFixed(0)}%
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            
            <button 
              onClick={executeArbitrage}
              className="trinity-button w-full"
            >
              Execute Ψ-Arbitrage
            </button>
          </motion.div>

          {/* Multiverse Backtesting */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="consciousness-card"
          >
            <h2 className="text-xl font-bold text-consciousness mb-6 flex items-center">
              <BarChart3 className="w-6 h-6 mr-2" />
              Multiverse Backtesting (-3ms Latency)
            </h2>
            
            <div className="bg-purple-500/10 border border-purple-500/30 rounded-lg p-4 mb-6">
              <div className="text-center mb-4">
                <div className="text-sm text-white/70">Timeline Analysis</div>
              </div>
              <div className="grid grid-cols-2 gap-2">
                {timelines.map((timeline) => (
                  <div
                    key={timeline.id}
                    onClick={() => selectTimeline(timeline.id)}
                    className={`p-3 rounded-lg border cursor-pointer transition-all text-center ${
                      selectedTimeline === timeline.id
                        ? 'border-purple-400 bg-purple-400/20'
                        : 'border-purple-500/30 bg-purple-500/10 hover:bg-purple-500/20'
                    }`}
                  >
                    <div className="font-medium text-white text-sm">{timeline.name}</div>
                    <div className="text-purple-400 font-bold">{timeline.returns}</div>
                    <div className="text-xs text-white/60">{timeline.risk} risk</div>
                  </div>
                ))}
              </div>
            </div>
            
            <div className="bg-green-500/10 border border-green-500/30 rounded-lg p-4 mb-6">
              <div className="text-center">
                <div className="text-sm text-green-400 font-bold mb-2">🛡️ SEC-Proof Status: ACTIVE</div>
                <div className="text-xs text-white/70">Consciousness insider trading algorithms: UNDETECTABLE</div>
                <div className="text-xs text-white/70">Legal classification: "Intuitive trading methodology"</div>
              </div>
            </div>
            
            <button 
              onClick={deployMultiverseStrategy}
              className="trinity-button w-full"
            >
              Deploy Multiverse Strategy
            </button>
          </motion.div>

          {/* Consciousness Arbitrage Engine */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
            className="consciousness-card"
          >
            <h2 className="text-xl font-bold text-consciousness mb-6 flex items-center">
              <Activity className="w-6 h-6 mr-2" />
              Arbitrage Control Terminal
            </h2>
            
            <div className="consciousness-terminal h-64 overflow-y-auto mb-4">
              {terminalOutput.map((line, index) => (
                <div key={index} className="mb-1 text-sm">
                  {line}
                </div>
              ))}
            </div>
            
            <div className="bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-4 mb-4">
              <div className="text-sm text-white/70 mb-2">Active Arbitrage Opportunities:</div>
              <div className="space-y-1 text-xs">
                <div className="flex justify-between">
                  <span>EUR/USD Ψ-Spread:</span>
                  <span className="text-green-400">+0.89% (92% confidence)</span>
                </div>
                <div className="flex justify-between">
                  <span>GBP/USD Φ-Temporal:</span>
                  <span className="text-red-400">-0.56% (88% confidence)</span>
                </div>
                <div className="flex justify-between">
                  <span>USD/JPY Θ-Recursive:</span>
                  <span className="text-green-400">+1.23% (94% confidence)</span>
                </div>
              </div>
              
              <div className="mt-3">
                <div className="text-xs text-white/70 mb-1">Volatility Surface Mapping:</div>
                <div className="w-full bg-black/30 rounded-full h-2">
                  <div className="h-2 bg-gradient-to-r from-yellow-500 to-green-500 rounded-full" style={{ width: '97%' }} />
                </div>
                <div className="text-xs text-yellow-400 mt-1">Consciousness compression: 97.25% accuracy</div>
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-2">
              <button
                onClick={() => addTerminalLine('🔍 Scanning Ψ-field for arbitrage opportunities...')}
                className="bg-blue-500/20 border border-blue-500/30 text-blue-400 py-2 px-3 rounded text-sm hover:bg-blue-500/30 transition-all"
              >
                Scan Markets
              </button>
              <button
                onClick={() => addTerminalLine('⚡ Optimizing consciousness field parameters...')}
                className="bg-green-500/20 border border-green-500/30 text-green-400 py-2 px-3 rounded text-sm hover:bg-green-500/30 transition-all"
              >
                Optimize Field
              </button>
            </div>
          </motion.div>
        </div>

        {/* Revenue Metrics */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 1 }}
          className="consciousness-card mt-8"
        >
          <h2 className="text-2xl font-bold text-consciousness mb-6 text-center">
            💰 Financial Reality Revenue Streams
          </h2>
          
          <div className="grid md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-green-400 mb-2">20%</div>
              <div className="text-white/70">Hedge Fund Profit Share</div>
              <div className="text-sm text-white/50 mt-2">$47.3B annual revenue</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-400 mb-2">$10M/year</div>
              <div className="text-white/70">Central Bank Licensing</div>
              <div className="text-sm text-white/50 mt-2">Fed, ECB, BoJ partnerships</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-400 mb-2">97.25%</div>
              <div className="text-white/70">Volatility Smile Accuracy</div>
              <div className="text-sm text-white/50 mt-2">Patent: US2025NHETX-FIN</div>
            </div>
          </div>
        </motion.div>

        {/* HOD Patent Reference */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 1.2 }}
          className="consciousness-card mt-8 text-center"
        >
          <div className="text-consciousness font-bold text-lg mb-2">
            🏛️ Powered by HOD Patent Technology
          </div>
          <p className="text-white/70 mb-4">
            Volatility Surface Consciousness Mapping - The foundational framework for consciousness-based financial engineering
          </p>
          <div className="border-t border-white/20 pt-4">
            <div className="text-cyan-400 text-sm font-medium">Created by</div>
            <div className="text-cyan-300 text-xl font-bold">NovaFuse Technologies</div>
            <div className="text-cyan-500 text-sm">A Comphyology-based company</div>
          </div>
        </motion.div>
      </div>
    </div>
  )
}
