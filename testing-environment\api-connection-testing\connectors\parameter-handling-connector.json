{"metadata": {"name": "Parameter Handling Connector", "version": "1.0.0", "category": "Test", "description": "Test connector for parameter handling", "author": "NovaGRC", "tags": ["test", "parameters"]}, "authentication": {"type": "API_KEY", "fields": {"apiKey": {"type": "string", "description": "API Key", "required": true, "sensitive": true}, "headerName": {"type": "string", "description": "Header name for the API key", "required": true, "default": "X-API-Key"}}, "testConnection": {"endpoint": "/health", "method": "GET", "expectedResponse": {"status": 200}}}, "configuration": {"baseUrl": "http://localhost:3005", "headers": {"Content-Type": "application/json"}, "timeout": 30000, "retryPolicy": {"maxRetries": 3, "backoffStrategy": "exponential"}}, "endpoints": [{"id": "queryParameters", "name": "Query Parameters", "path": "/parameters/query", "method": "GET", "parameters": {"query": {"param1": {"type": "string", "required": true}, "param2": {"type": "string", "required": false}}, "path": {}, "body": {}}, "response": {"successCode": 200}}, {"id": "pathParameters", "name": "Path Parameters", "path": "/parameters/path/{{id}}", "method": "GET", "parameters": {"query": {}, "path": {"id": {"type": "string", "required": true}}, "body": {}}, "response": {"successCode": 200}}, {"id": "bodyParameters", "name": "Body Parameters", "path": "/parameters/body", "method": "POST", "parameters": {"query": {}, "path": {}, "body": {"param1": {"type": "string", "required": true}, "param2": {"type": "string", "required": false}}}, "response": {"successCode": 200}}], "mappings": [{"sourceEndpoint": "queryParameters", "targetSystem": "NovaGRC", "targetEntity": "Parameters", "transformations": [{"source": "$.params.param1", "target": "param1", "transform": "identity"}, {"source": "$.params.param2", "target": "param2", "transform": "identity"}]}, {"sourceEndpoint": "pathParameters", "targetSystem": "NovaGRC", "targetEntity": "Parameters", "transformations": [{"source": "$.params.id", "target": "id", "transform": "identity"}]}, {"sourceEndpoint": "bodyParameters", "targetSystem": "NovaGRC", "targetEntity": "Parameters", "transformations": [{"source": "$.params.param1", "target": "param1", "transform": "identity"}, {"source": "$.params.param2", "target": "param2", "transform": "identity"}]}], "events": {"webhooks": [], "polling": []}}