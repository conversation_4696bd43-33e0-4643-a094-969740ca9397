{"name": "novacore", "version": "1.0.0", "description": "NovaCore API Server - The fusion of NovaConnect and NovaSphere", "main": "api/server.js", "scripts": {"start": "node api/server.js", "dev": "nodemon api/server.js", "test": "jest", "test:mocha": "mocha tests/**/*.test.js --recursive --timeout 10000", "test:unit": "mocha tests/unit/**/*.test.js --recursive --timeout 5000", "test:integration": "mocha tests/integration/**/*.test.js --recursive --timeout 10000", "test:security": "mocha tests/unit/security/**/*.test.js tests/integration/security/**/*.test.js --recursive --timeout 10000", "test:compliance": "mocha tests/unit/compliance/**/*.test.js tests/integration/compliance/**/*.test.js --recursive --timeout 10000", "lint": "eslint .", "lint:fix": "eslint . --fix"}, "dependencies": {"axios": "^1.4.0", "canvas": "^2.11.0", "compression": "^1.7.4", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.0.3", "ethers": "^5.7.2", "express": "^4.18.2", "express-rate-limit": "^6.7.0", "helmet": "^6.1.5", "hpp": "^0.2.3", "jsonwebtoken": "^9.0.0", "marked": "^4.3.0", "mongoose": "^7.1.0", "morgan": "^1.10.0", "uuid": "^9.0.0", "winston": "^3.8.2", "xss-clean": "^0.1.1"}, "devDependencies": {"chai": "^4.3.7", "eslint": "^8.36.0", "jest": "^29.5.0", "mocha": "^10.2.0", "nodemon": "^2.0.22", "sinon": "^15.0.2", "supertest": "^6.3.3"}, "engines": {"node": ">=14.0.0"}, "author": "NovaFuse", "license": "UNLICENSED", "private": true}