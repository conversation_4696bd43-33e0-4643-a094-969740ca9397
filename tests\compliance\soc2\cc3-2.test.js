/**
 * SOC 2 CC3.2 Test Implementation
 * 
 * Tests NovaFuse compliance with SOC 2 CC3.2: COSO Principle 7: Identifies and Analyzes Risk
 */

const { describe, it, expect } = require('@jest/globals');
const fs = require('fs');
const path = require('path');
const os = require('os');

// Import the TrackingManager mock
const { TrackingManager } = require('../../mocks/tracking-manager.mock');

describe('SOC 2 Compliance - CC3.2: Identifies and Analyzes Risk', () => {
  let trackingManager;
  let tempDir;
  
  beforeEach(() => {
    // Create a temporary directory for test data
    tempDir = fs.mkdtempSync(path.join(os.tmpdir(), 'soc2-test-'));
    
    // Initialize the TrackingManager with the temporary directory
    trackingManager = new TrackingManager(tempDir);
  });
  
  afterEach(() => {
    // Clean up the temporary directory
    if (fs.existsSync(tempDir)) {
      fs.rmSync(tempDir, { recursive: true, force: true });
    }
  });
  
  it('should support tracking risk assessment activities', () => {
    // Create a requirement for risk assessment
    const requirement = trackingManager.create_requirement({
      name: 'Implement Risk Assessment Process',
      description: 'Implement a process for identifying and analyzing risks to the achievement of objectives',
      framework: 'SOC 2',
      category: 'Risk Assessment',
      priority: 'high',
      status: 'in_progress',
      due_date: '2023-12-31',
      assigned_to: 'risk_manager',
      tags: ['soc2', 'cc3.2', 'risk_assessment']
    });
    
    // Create activities for implementing the requirement
    const activity1 = trackingManager.create_activity({
      name: 'Identify Risks',
      description: 'Identify risks to the achievement of objectives across the entity',
      requirement_id: requirement.id,
      type: 'assessment',
      status: 'completed',
      start_date: '2023-01-01',
      end_date: '2023-01-15',
      assigned_to: 'risk_manager',
      notes: 'Risks identified and documented'
    });
    
    const activity2 = trackingManager.create_activity({
      name: 'Analyze Risks',
      description: 'Analyze identified risks as a basis for determining how they should be managed',
      requirement_id: requirement.id,
      type: 'assessment',
      status: 'in_progress',
      start_date: '2023-01-16',
      end_date: '2023-01-31',
      assigned_to: 'risk_manager',
      notes: 'Risk analysis in progress'
    });
    
    const activity3 = trackingManager.create_activity({
      name: 'Develop Risk Management Plan',
      description: 'Develop a plan for managing identified risks',
      requirement_id: requirement.id,
      type: 'documentation',
      status: 'pending',
      start_date: '2023-02-01',
      end_date: '2023-02-15',
      assigned_to: 'risk_manager',
      notes: 'Risk management plan development scheduled'
    });
    
    // Verify the requirement was created correctly
    expect(requirement).toBeDefined();
    expect(requirement.framework).toBe('SOC 2');
    expect(requirement.tags).toContain('cc3.2');
    
    // Verify the activities were created correctly
    expect(activity1).toBeDefined();
    expect(activity1.requirement_id).toBe(requirement.id);
    expect(activity1.status).toBe('completed');
    
    expect(activity2).toBeDefined();
    expect(activity2.requirement_id).toBe(requirement.id);
    expect(activity2.status).toBe('in_progress');
    
    expect(activity3).toBeDefined();
    expect(activity3.requirement_id).toBe(requirement.id);
    expect(activity3.status).toBe('pending');
    
    // Verify the activities can be retrieved for the requirement
    const activities = trackingManager.get_requirement_activities(requirement.id);
    expect(activities.length).toBe(3);
    expect(activities).toContainEqual(activity1);
    expect(activities).toContainEqual(activity2);
    expect(activities).toContainEqual(activity3);
    
    // Document compliance status
    console.log('COMPLIANCE STATUS: PARTIAL');
    console.log('COMPLIANCE NOTES: NovaFuse supports tracking risk assessment activities, but does not provide built-in risk assessment functionality.');
    console.log('GAPS: NovaFuse does not provide templates or guidance for risk assessment.');
    console.log('RECOMMENDATIONS: Add templates and guidance for risk assessment, including risk identification, analysis, and management.');
  });
  
  it('should support categorizing risks by entity level', () => {
    // Create requirements for risks at different entity levels
    const entityLevelRisk = trackingManager.create_requirement({
      name: 'Entity-Level Risk',
      description: 'Risk that affects the entire entity',
      framework: 'SOC 2',
      category: 'Risk Assessment',
      priority: 'high',
      status: 'in_progress',
      tags: ['soc2', 'cc3.2', 'risk_assessment', 'entity_level']
    });
    
    const divisionLevelRisk = trackingManager.create_requirement({
      name: 'Division-Level Risk',
      description: 'Risk that affects a specific division',
      framework: 'SOC 2',
      category: 'Risk Assessment',
      priority: 'medium',
      status: 'in_progress',
      tags: ['soc2', 'cc3.2', 'risk_assessment', 'division_level']
    });
    
    const functionalLevelRisk = trackingManager.create_requirement({
      name: 'Functional-Level Risk',
      description: 'Risk that affects a specific function',
      framework: 'SOC 2',
      category: 'Risk Assessment',
      priority: 'low',
      status: 'in_progress',
      tags: ['soc2', 'cc3.2', 'risk_assessment', 'functional_level']
    });
    
    // Verify the requirements were created correctly
    expect(entityLevelRisk).toBeDefined();
    expect(entityLevelRisk.tags).toContain('entity_level');
    
    expect(divisionLevelRisk).toBeDefined();
    expect(divisionLevelRisk.tags).toContain('division_level');
    
    expect(functionalLevelRisk).toBeDefined();
    expect(functionalLevelRisk.tags).toContain('functional_level');
    
    // Filter requirements by entity level
    const entityLevelRisks = Object.values(trackingManager.requirements)
      .filter(req => req.tags.includes('entity_level'));
    
    const divisionLevelRisks = Object.values(trackingManager.requirements)
      .filter(req => req.tags.includes('division_level'));
    
    const functionalLevelRisks = Object.values(trackingManager.requirements)
      .filter(req => req.tags.includes('functional_level'));
    
    // Verify the filtering works correctly
    expect(entityLevelRisks.length).toBe(1);
    expect(entityLevelRisks[0]).toEqual(entityLevelRisk);
    
    expect(divisionLevelRisks.length).toBe(1);
    expect(divisionLevelRisks[0]).toEqual(divisionLevelRisk);
    
    expect(functionalLevelRisks.length).toBe(1);
    expect(functionalLevelRisks[0]).toEqual(functionalLevelRisk);
    
    // Document compliance status
    console.log('COMPLIANCE STATUS: PASSED');
    console.log('COMPLIANCE NOTES: NovaFuse supports categorizing risks by entity level using tags.');
    console.log('GAPS: None');
    console.log('RECOMMENDATIONS: None');
  });
});
