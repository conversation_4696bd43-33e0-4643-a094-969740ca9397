import { useUUICContext } from './UUICProvider';

/**
 * useUUIC - Custom hook for module-agnostic component logic
 * 
 * This hook retrieves a component from the UUIC registry based on the component key.
 * 
 * @param {string} componentKey - Key of the component to retrieve
 * @returns {React.Component|null} Component or null if not found
 */
export const useUUIC = (componentKey) => {
  const config = useUUICContext();
  return config.components?.[componentKey] || null;
};

/**
 * useUUICConfig - Hook to access component configuration
 * 
 * @param {string} componentKey - Key of the component to retrieve configuration for
 * @returns {Object|null} Component configuration or null if not found
 */
export const useUUICConfig = (componentKey) => {
  const config = useUUICContext();
  return config.componentConfigs?.[componentKey] || null;
};

/**
 * useUUICData - Hook to access component data
 * 
 * @param {string} dataKey - Key of the data to retrieve
 * @returns {any} Component data or null if not found
 */
export const useUUICData = (dataKey) => {
  const config = useUUICContext();
  return config.data?.[dataKey] || null;
};

export default useUUIC;
