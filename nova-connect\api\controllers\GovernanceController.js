/**
 * Governance Controller
 * 
 * This controller handles API requests related to governance features.
 */

const GovernanceService = require('../services/GovernanceService');
const { ValidationError } = require('../utils/errors');

class GovernanceController {
  constructor() {
    this.governanceService = new GovernanceService();
  }

  /**
   * Get all approval workflows
   */
  async getAllApprovalWorkflows(req, res, next) {
    try {
      const filters = req.query;
      const approvals = await this.governanceService.getAllApprovalWorkflows(filters);
      res.json(approvals);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get approval workflow by ID
   */
  async getApprovalWorkflowById(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Approval workflow ID is required');
      }
      
      const approval = await this.governanceService.getApprovalWorkflowById(id);
      res.json(approval);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Create a new approval workflow
   */
  async createApprovalWorkflow(req, res, next) {
    try {
      const data = req.body;
      
      if (!data) {
        throw new ValidationError('Approval workflow data is required');
      }
      
      const approval = await this.governanceService.createApprovalWorkflow(data, req.user.id);
      res.status(201).json(approval);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update approval workflow status
   */
  async updateApprovalStatus(req, res, next) {
    try {
      const { id } = req.params;
      const data = req.body;
      
      if (!id) {
        throw new ValidationError('Approval workflow ID is required');
      }
      
      if (!data || !data.status) {
        throw new ValidationError('Approval status is required');
      }
      
      const approval = await this.governanceService.updateApprovalStatus(id, data, req.user.id);
      res.json(approval);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Cancel approval workflow
   */
  async cancelApprovalWorkflow(req, res, next) {
    try {
      const { id } = req.params;
      const data = req.body || {};
      
      if (!id) {
        throw new ValidationError('Approval workflow ID is required');
      }
      
      // Check if user is admin
      data.isAdmin = req.user.role === 'admin';
      
      const result = await this.governanceService.cancelApprovalWorkflow(id, data, req.user.id);
      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get approval workflows for resource
   */
  async getApprovalWorkflowsForResource(req, res, next) {
    try {
      const { resourceType, resourceId } = req.params;
      
      if (!resourceType) {
        throw new ValidationError('Resource type is required');
      }
      
      if (!resourceId) {
        throw new ValidationError('Resource ID is required');
      }
      
      const approvals = await this.governanceService.getApprovalWorkflowsForResource(resourceType, resourceId);
      res.json(approvals);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get approval workflows for user
   */
  async getApprovalWorkflowsForUser(req, res, next) {
    try {
      const { role } = req.query;
      const approvals = await this.governanceService.getApprovalWorkflowsForUser(req.user.id, role);
      res.json(approvals);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get all compliance templates
   */
  async getAllComplianceTemplates(req, res, next) {
    try {
      const filters = req.query;
      const templates = await this.governanceService.getAllComplianceTemplates(filters);
      res.json(templates);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get compliance template by ID
   */
  async getComplianceTemplateById(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Compliance template ID is required');
      }
      
      const template = await this.governanceService.getComplianceTemplateById(id);
      res.json(template);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Create a new compliance template
   */
  async createComplianceTemplate(req, res, next) {
    try {
      const data = req.body;
      
      if (!data) {
        throw new ValidationError('Compliance template data is required');
      }
      
      const template = await this.governanceService.createComplianceTemplate(data, req.user.id);
      res.status(201).json(template);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Delete compliance template
   */
  async deleteComplianceTemplate(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Compliance template ID is required');
      }
      
      const result = await this.governanceService.deleteComplianceTemplate(id, req.user.id);
      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Track data lineage
   */
  async trackDataLineage(req, res, next) {
    try {
      const data = req.body;
      
      if (!data) {
        throw new ValidationError('Data lineage information is required');
      }
      
      const lineage = await this.governanceService.trackDataLineage(data, req.user.id);
      res.status(201).json(lineage);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get data lineage for resource
   */
  async getDataLineageForResource(req, res, next) {
    try {
      const { resourceType, resourceId } = req.params;
      const { direction } = req.query;
      
      if (!resourceType) {
        throw new ValidationError('Resource type is required');
      }
      
      if (!resourceId) {
        throw new ValidationError('Resource ID is required');
      }
      
      const lineages = await this.governanceService.getDataLineageForResource(resourceType, resourceId, direction);
      res.json(lineages);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Build data lineage graph
   */
  async buildDataLineageGraph(req, res, next) {
    try {
      const { resourceType, resourceId } = req.params;
      const { depth } = req.query;
      
      if (!resourceType) {
        throw new ValidationError('Resource type is required');
      }
      
      if (!resourceId) {
        throw new ValidationError('Resource ID is required');
      }
      
      const graph = await this.governanceService.buildDataLineageGraph(
        resourceType, 
        resourceId, 
        depth ? parseInt(depth, 10) : 2
      );
      
      res.json(graph);
    } catch (error) {
      next(error);
    }
  }
}

module.exports = new GovernanceController();
