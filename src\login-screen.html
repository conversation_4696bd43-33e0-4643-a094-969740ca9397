<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NovaFuse™ Login (Patent Pending)</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            background-image: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        }
        
        .login-container {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            width: 400px;
            padding: 40px;
            text-align: center;
        }
        
        .logo {
            margin-bottom: 30px;
        }
        
        .logo img {
            width: 200px;
        }
        
        h1 {
            color: #333;
            margin-bottom: 30px;
            font-weight: 600;
        }
        
        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-weight: 500;
        }
        
        input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        
        button {
            background-color: #4a6cf7;
            color: white;
            border: none;
            border-radius: 5px;
            padding: 12px 20px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
            margin-top: 10px;
            font-weight: 500;
            transition: background-color 0.3s;
        }
        
        button:hover {
            background-color: #3a5ce5;
        }
        
        .forgot-password {
            margin-top: 20px;
            color: #4a6cf7;
            text-decoration: none;
            font-size: 14px;
            display: block;
        }
        
        .forgot-password:hover {
            text-decoration: underline;
        }
        
        .legal-notice {
            margin-top: 30px;
            font-size: 12px;
            color: #777;
            text-align: center;
            font-style: italic;
        }
        
        .tier-selector {
            margin-top: 20px;
            margin-bottom: 30px;
        }
        
        .tier-option {
            display: inline-block;
            margin: 0 10px;
            padding: 10px 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .tier-option:hover {
            border-color: #4a6cf7;
            color: #4a6cf7;
        }
        
        .tier-option.selected {
            background-color: #4a6cf7;
            color: white;
            border-color: #4a6cf7;
        }
        
        .tier-name {
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .tier-description {
            font-size: 12px;
            color: #777;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo">
            <img src="logo.png" alt="NovaFuse Logo">
        </div>
        
        <h1>Welcome to NovaFuse™</h1>
        
        <div class="tier-selector">
            <div class="tier-option" onclick="selectTier(this, 'einstein')">
                <div class="tier-name">Einstein Tier™</div>
                <div class="tier-description">Patent Pending</div>
            </div>
            <div class="tier-option" onclick="selectTier(this, 'newton')">
                <div class="tier-name">Newton Tier™</div>
                <div class="tier-description">Patent Pending</div>
            </div>
            <div class="tier-option" onclick="selectTier(this, 'galileo')">
                <div class="tier-name">Galileo Tier™</div>
                <div class="tier-description">Patent Pending</div>
            </div>
        </div>
        
        <form>
            <div class="form-group">
                <label for="email">Email</label>
                <input type="email" id="email" name="email" required>
            </div>
            
            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" required>
            </div>
            
            <button type="submit">Log In</button>
        </form>
        
        <a href="#" class="forgot-password">Forgot Password?</a>
        
        <div class="legal-notice">
            <p>By accessing NovaFuse CSDE™ (Patent Pending), you agree to non-compete clauses enforceable under π10³ penalty scaling.</p>
            <p>Universal Unified Field Theory (UUFT) and Cyber-Safety Dominance Equation (CSDE) technologies are Patent Pending. Unauthorized replication triggers π10³ legal remedies.</p>
        </div>
    </div>
    
    <script>
        function selectTier(element, tier) {
            // Remove selected class from all tier options
            const tierOptions = document.querySelectorAll('.tier-option');
            tierOptions.forEach(option => {
                option.classList.remove('selected');
            });
            
            // Add selected class to clicked tier option
            element.classList.add('selected');
            
            // Store selected tier
            localStorage.setItem('selectedTier', tier);
        }
    </script>
</body>
</html>
