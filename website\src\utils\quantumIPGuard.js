/**
 * Quantum IP Guard
 * Advanced protection system for quantum computing IP
 * 
 * Protected by US63/XXXXXX, US63/YYYYYY
 */

import { v4 as uuidv4 } from 'uuid';

class QuantumIPGuard {
  constructor(options = {}) {
    this.patents = [
      'US63/XXXXXX', // Quantum streaming core
      'US63/YYYYYY'  // Entanglement visualization
    ];
    
    this.options = {
      protectionLevel: 'enterprise', // 'basic' | 'enterprise' | 'government'
      watermarkIntensity: 0.05,
      obfuscation: true,
      tamperDetection: true,
      telemetry: true,
      ...options
    };
    
    this.sessionId = uuidv4();
    this.entropyBuffer = new Uint8Array(32);
    this.entropyIndex = 0;
    this.initialized = false;
    
    // Initialize crypto if available
    if (typeof crypto !== 'undefined' && crypto.getRandomValues) {
      crypto.getRandomValues(this.entropyBuffer);
    }
    
    // Apply runtime protection
    this.installRuntimeProtection();
  }

  /**
   * Initialize the protection system
   */
  async initialize() {
    if (this.initialized) return true;
    
    try {
      // Generate a unique device fingerprint
      this.deviceFingerprint = await this.generateFingerprint();
      
      // Initialize tamper detection
      if (this.options.tamperDetection) {
        this.installTamperDetection();
      }
      
      // Report initialization (if telemetry is enabled)
      if (this.options.telemetry) {
        this.reportEvent('initialized', {
          protectionLevel: this.options.protectionLevel,
          deviceId: this.deviceFingerprint
        });
      }
      
      this.initialized = true;
      return true;
      
    } catch (error) {
      console.error('Failed to initialize QuantumIPGuard:', error);
      return false;
    }
  }
  
  /**
   * Generate a unique device fingerprint
   */
  async generateFingerprint() {
    try {
      // Collect entropy from various sources
      const entropySources = [
        navigator.userAgent,
        navigator.hardwareConcurrency,
        screen.width + 'x' + screen.height,
        new Date().getTimezoneOffset(),
        navigator.language,
        this.entropyBuffer.join('')
      ];
      
      // Create a hash of the entropy sources
      const encoder = new TextEncoder();
      const data = encoder.encode(entropySources.join('|'));
      
      if (window.crypto && crypto.subtle) {
        const hashBuffer = await crypto.subtle.digest('SHA-256', data);
        const hashArray = Array.from(new Uint8Array(hashBuffer));
        return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
      } else {
        // Fallback for browsers without crypto.subtle
        let hash = 0;
        for (let i = 0; i < data.length; i++) {
          const char = data[i];
          hash = ((hash << 5) - hash) + char;
          hash = hash & hash; // Convert to 32bit integer
        }
        return hash.toString(16);
      }
    } catch (error) {
      console.warn('Fingerprint generation failed, using fallback:', error);
      return 'fp-' + Math.random().toString(36).substring(2, 15);
    }
  }
  
  /**
   * Install runtime protection
   */
  installRuntimeProtection() {
    // Protect constructor
    const originalFunction = Function.prototype.constructor;
    
    // @ts-ignore - We're intentionally modifying built-ins for protection
    Function.prototype.constructor = function() {
      // Check if this is an attempt to access our protected code
      const stack = new Error().stack || '';
      if (stack.includes('eval') || stack.includes('Function.constructor')) {
        console.warn('Suspicious code execution detected and blocked');
        return function() { return null; };
      }
      // @ts-ignore - Call original constructor
      return originalFunction.apply(this, arguments);
    };
    
    // Lock down the prototype
    Object.defineProperty(Function.prototype, 'constructor', {
      writable: false,
      configurable: false
    });
    
    // Protect against prototype pollution
    this.lockPrototype(Object.prototype);
    this.lockPrototype(Array.prototype);
    this.lockPrototype(Function.prototype);
  }
  
  /**
   * Lock down object prototypes
   */
  lockPrototype(proto) {
    if (proto && proto === Object.prototype || 
        proto === Array.prototype || 
        proto === Function.prototype) {
      Object.freeze(proto);
      Object.getOwnPropertyNames(proto).forEach(name => {
        const desc = Object.getOwnPropertyDescriptor(proto, name);
        if (desc && (desc.get || desc.set)) {
          Object.defineProperty(proto, name, {
            get: desc.get,
            set: desc.set,
            enumerable: false,
            configurable: false
          });
        }
      });
    }
  }
  
  /**
   * Install tamper detection
   */
  installTamperDetection() {
    // Check for debugger
    const detectDebugger = () => {
      const startTime = performance.now();
      // eslint-disable-next-line no-debugger
      debugger;
      const endTime = performance.now();
      
      // If the debugger is active, the time difference will be significant
      if (endTime - startTime > 100) {
        this.onTamperDetected('debugger_detected');
      }
    };
    
    // Run detection at random intervals
    setInterval(detectDebugger, Math.random() * 5000 + 1000);
    
    // Detect devtools
    const devtools = {
      isOpen: false,
      orientation: null
    };
    
    const emitEvent = (isOpen, orientation) => {
      window.dispatchEvent(new CustomEvent('devtoolschange', {
        detail: {
          isOpen,
          orientation
        }
      }));
    };
    
    const checkDevTools = () => {
      const widthThreshold = window.outerWidth - window.innerWidth > 160;
      const heightThreshold = window.outerHeight - window.innerHeight > 160;
      const orientation = widthThreshold ? 'vertical' : 'horizontal';
      
      if (!(heightThreshold && widthThreshold) && 
          ((window.Firebug && window.Firebug.chrome && window.Firebug.chrome.isInitialized) || 
           widthThreshold || 
           heightThreshold)) {
        if (!devtools.isOpen || devtools.orientation !== orientation) {
          devtools.isOpen = true;
          devtools.orientation = orientation;
          emitEvent(true, orientation);
          this.onTamperDetected('devtools_opened');
        }
      } else {
        if (devtools.isOpen) {
          devtools.isOpen = false;
          devtools.orientation = null;
          emitEvent(false, null);
        }
      }
    };
    
    // Check every 500ms
    setInterval(checkDevTools, 500);
    
    // Also check on window resize
    window.addEventListener('resize', checkDevTools);
  }
  
  /**
   * Handle tamper detection events
   */
  onTamperDetected(type) {
    console.warn(`[SECURITY] Tamper detected: ${type}`);
    
    // Report the incident
    if (this.options.telemetry) {
      this.reportEvent('tamper_detected', {
        type,
        timestamp: new Date().toISOString(),
        sessionId: this.sessionId,
        deviceId: this.deviceFingerprint
      });
    }
    
    // Take appropriate action based on protection level
    switch (this.options.protectionLevel) {
      case 'enterprise':
      case 'government':
        // In a real implementation, you might want to:
        // 1. Notify the server
        // 2. Degrade functionality
        // 3. Trigger additional security measures
        break;
        
      default:
        // Basic protection - just log the incident
        break;
    }
  }
  
  /**
   * Generate a watermark for the given content
   */
  generateWatermark(content, options = {}) {
    const { intensity = this.options.watermarkIntensity } = options;
    
    // In a real implementation, this would generate a visual watermark
    // For now, we'll just return a text-based watermark
    const watermark = `
      /* 
       * NOVAFUSE QUANTUM STREAM - PROPRIETARY AND CONFIDENTIAL
       * Protected by: ${this.patents.join(', ')}
       * Session: ${this.sessionId}
       * Device: ${this.deviceFingerprint || 'unknown'}
       * DO NOT DISTRIBUTE
       */
    `;
    
    return watermark + '\n\n' + content;
  }
  
  /**
   * Obfuscate code
   */
  obfuscate(code, options = {}) {
    if (!this.options.obfuscation) return code;
    
    const { tech = 'quantum', entropyThreshold = 0.95 } = options;
    
    // In a real implementation, this would use a proper obfuscation tool
    // For now, we'll just add some basic protection
    return `
      // OBFUSCATED WITH ${tech.toUpperCase()} TECHNIQUE
      // ENTROPY THRESHOLD: ${entropyThreshold}
      ${this.generateWatermark(code)}
    `;
  }
  
  /**
   * Protect a component with QuantumIPGuard
   */
  protectComponent(Component) {
    if (typeof window === 'undefined') {
      // Server-side rendering - just return the component as-is
      return Component;
    }
    
    const self = this;
    
    // Create a protected wrapper component
    return class ProtectedComponent extends Component {
      constructor(props) {
        super(props);
        
        // Initialize protection
        this._protectionId = uuidv4();
        this._lastRenderTime = 0;
        this._renderCount = 0;
        
        // Bind methods
        this._handleTamper = this._handleTamper.bind(this);
        
        // Add tamper detection
        window.addEventListener('tamper_detected', this._handleTamper);
      }
      
      componentWillUnmount() {
        // Clean up event listeners
        window.removeEventListener('tamper_detected', this._handleTamper);
        
        // Call original componentWillUnmount if it exists
        if (super.componentWillUnmount) {
          super.componentWillUnmount();
        }
      }
      
      _handleTamper(event) {
        console.warn(`[${this.constructor.name}] Tamper detected:`, event.detail);
        
        // In a real implementation, you might want to:
        // 1. Notify the parent component
        // 2. Trigger a re-render with degraded functionality
        // 3. Report the incident to the server
        
        this.forceUpdate();
      }
      
      render() {
        try {
          // Call the original render method
          const startTime = performance.now();
          const result = super.render();
          const endTime = performance.now();
          
          // Track render performance
          this._lastRenderTime = endTime - startTime;
          this._renderCount++;
          
          // Add protection attributes
          if (result && result.props) {
            return {
              ...result,
              props: {
                ...result.props,
                'data-protected': 'true',
                'data-protection-id': this._protectionId,
                'data-render-count': this._renderCount,
                'data-last-render-time': this._lastRenderTime.toFixed(2) + 'ms'
              }
            };
          }
          
          return result;
          
        } catch (error) {
          console.error(`[${this.constructor.name}] Render error:`, error);
          
          // Return a fallback UI
          return (
            <div className="quantum-error" style={{ 
              padding: '1rem',
              backgroundColor: '#ffebee',
              color: '#c62828',
              border: '1px solid #ef9a9a',
              borderRadius: '4px'
            }}>
              <h3>Quantum Rendering Error</h3>
              <p>An error occurred while rendering this component.</p>
              <p>Reference: {this._protectionId}</p>
            </div>
          );
        }
      }
    };
  }
  
  /**
   * Report an event to the telemetry service
   */
  async reportEvent(eventName, data = {}) {
    if (!this.options.telemetry) return;
    
    try {
      const event = {
        event: eventName,
        timestamp: new Date().toISOString(),
        sessionId: this.sessionId,
        deviceId: this.deviceFingerprint,
        protectionLevel: this.options.protectionLevel,
        url: typeof window !== 'undefined' ? window.location.href : '',
        ...data
      };
      
      // In a real implementation, this would send the event to a telemetry service
      console.debug('[Telemetry]', event);
      
      // Example: Send to an analytics endpoint
      // await fetch('/api/telemetry', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(event)
      // });
      
    } catch (error) {
      console.error('Failed to report event:', error);
    }
  }
}

// Export a singleton instance
export const quantumIPGuard = new QuantumIPGuard();

export default QuantumIPGuard;
