# Deployment Guide

This guide covers the deployment of NovaAlign in various environments, from development to production.

## Table of Contents
- [Prerequisites](#prerequisites)
- [Quick Start with Docker](#quick-start-with-docker)
- [Manual Installation](#manual-installation)
- [Kubernetes Deployment](#kubernetes-deployment)
- [Scaling](#scaling)
- [Backup & Recovery](#backup--recovery)
- [Monitoring](#monitoring)
- [Troubleshooting](#troubleshooting)

## Prerequisites

### Hardware Requirements
- **Development**:
  - CPU: 4 cores
  - RAM: 8GB
  - Storage: 20GB SSD

- **Production**:
  - CPU: 8+ cores
  - RAM: 32GB+
  - Storage: 100GB+ SSD (with backup)

### Software Requirements
- Docker 20.10+
- Docker Compose 2.0+
- Kubernetes 1.20+ (for production)
- PostgreSQL 13+
- Redis 6.0+

## Quick Start with Docker

The fastest way to get started is using Docker Compose:

1. Clone the repository:
   ```bash
   git clone https://github.com/your-org/nova-align.git
   cd nova-align
   ```

2. Copy the example environment file:
   ```bash
   cp .env.example .env
   ```

3. Update the environment variables in `.env` as needed.

4. Start the services:
   ```bash
   docker-compose up -d
   ```

5. Access the dashboard at `http://localhost:3000`

## Manual Installation

### 1. Database Setup

```bash
# Install PostgreSQL
sudo apt update
sudo apt install postgresql postgresql-contrib

# Create database and user
sudo -u postgres createuser nova_align
sudo -u postgres createdb nova_align
sudo -u postgres psql -c "ALTER USER nova_align WITH PASSWORD 'secure_password';"
```

### 2. Backend Setup

```bash
# Clone the repository
git clone https://github.com/your-org/nova-align.git
cd nova-align/backend

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: .\venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Run migrations
alembic upgrade head

# Start the server
uvicorn main:app --host 0.0.0.0 --port 8000
```

### 3. Frontend Setup

```bash
cd ../frontend

# Install dependencies
npm install

# Start the development server
npm run dev
```

## Kubernetes Deployment

For production deployments, we recommend using Kubernetes:

1. Install `kubectl` and `helm`
2. Add the NovaAlign Helm repository:
   ```bash
   helm repo add nova-align https://charts.novaalign.ai
   helm repo update
   ```

3. Create a `values.yaml` file with your configuration

4. Install the chart:
   ```bash
   helm install nova-align nova-align/nova-align -f values.yaml
   ```

## Scaling

### Horizontal Scaling
```yaml
# Example Kubernetes HPA configuration
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: nova-align-api
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: nova-align-api
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
```

## Backup & Recovery

### Database Backup
```bash
# Create backup
pg_dump -U nova_align -d nova_align > backup_$(date +%Y%m%d).sql

# Restore from backup
psql -U nova_align -d nova_align < backup_20230625.sql
```

## Monitoring

We recommend using the following monitoring stack:
- Prometheus for metrics collection
- Grafana for visualization
- Loki for logs
- Alertmanager for alerts

## Troubleshooting

### Common Issues

**Frontend not connecting to backend**
- Verify CORS settings in the backend
- Check if the API URL is correctly configured in the frontend

**Database connection issues**
- Verify database credentials in `.env`
- Check if PostgreSQL is running and accessible

**High CPU/Memory usage**
- Check for slow queries in the database
- Consider scaling your deployment

### Getting Help
- Check the [FAQ](./troubleshooting.md)
- Open an issue on [GitHub](https://github.com/your-org/nova-align/issues)
- Join our [community forum](https://community.novaalign.ai)
