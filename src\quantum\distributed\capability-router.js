/**
 * Capability Router
 *
 * This module provides capability-based task routing for the Finite Universe
 * Principle defense system, enabling routing of tasks to nodes with specific capabilities.
 */

const EventEmitter = require('events');

/**
 * CapabilityRouter class
 * 
 * Provides capability-based task routing for distributed processing.
 */
class CapabilityRouter extends EventEmitter {
  /**
   * Constructor
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();

    this.options = {
      enableLogging: true,
      defaultCapability: options.defaultCapability || 'default',
      loadBalancingStrategy: options.loadBalancingStrategy || 'least-loaded', // 'least-loaded', 'round-robin', 'random'
      capabilityMatchingStrategy: options.capabilityMatchingStrategy || 'exact', // 'exact', 'subset', 'best-match'
      ...options
    };

    // Initialize nodes registry
    this.nodes = new Map();
    
    // Initialize capabilities registry
    this.capabilities = new Map();
    
    // Initialize round-robin indices
    this.roundRobinIndices = new Map();

    if (this.options.enableLogging) {
      console.log('CapabilityRouter initialized with options:', this.options);
    }
  }

  /**
   * Register a node with capabilities
   * @param {Object} nodeInfo - Node information
   * @returns {boolean} - True if node was registered, false otherwise
   */
  registerNode(nodeInfo) {
    // Validate node information
    if (!nodeInfo || !nodeInfo.id) {
      if (this.options.enableLogging) {
        console.log('Invalid node information:', nodeInfo);
      }
      return false;
    }
    
    // Get node capabilities
    const capabilities = nodeInfo.capabilities || [this.options.defaultCapability];
    
    // Register node
    this.nodes.set(nodeInfo.id, {
      ...nodeInfo,
      capabilities,
      registeredAt: Date.now()
    });
    
    // Register capabilities
    for (const capability of capabilities) {
      if (!this.capabilities.has(capability)) {
        this.capabilities.set(capability, new Set());
      }
      
      this.capabilities.get(capability).add(nodeInfo.id);
    }
    
    // Initialize round-robin index
    if (!this.roundRobinIndices.has(nodeInfo.id)) {
      this.roundRobinIndices.set(nodeInfo.id, 0);
    }
    
    if (this.options.enableLogging) {
      console.log(`Node ${nodeInfo.id} registered with capabilities:`, capabilities);
    }
    
    // Emit node-registered event
    this.emit('node-registered', { nodeId: nodeInfo.id, capabilities });
    
    return true;
  }

  /**
   * Unregister a node
   * @param {string} nodeId - Node ID
   * @returns {boolean} - True if node was unregistered, false otherwise
   */
  unregisterNode(nodeId) {
    // Check if node exists
    if (!this.nodes.has(nodeId)) {
      if (this.options.enableLogging) {
        console.log(`Node ${nodeId} not found`);
      }
      return false;
    }
    
    // Get node information
    const nodeInfo = this.nodes.get(nodeId);
    
    // Unregister node
    this.nodes.delete(nodeId);
    
    // Unregister capabilities
    for (const capability of nodeInfo.capabilities) {
      if (this.capabilities.has(capability)) {
        this.capabilities.get(capability).delete(nodeId);
        
        // Remove capability if no nodes have it
        if (this.capabilities.get(capability).size === 0) {
          this.capabilities.delete(capability);
        }
      }
    }
    
    // Remove round-robin index
    this.roundRobinIndices.delete(nodeId);
    
    if (this.options.enableLogging) {
      console.log(`Node ${nodeId} unregistered`);
    }
    
    // Emit node-unregistered event
    this.emit('node-unregistered', { nodeId });
    
    return true;
  }

  /**
   * Update node information
   * @param {Object} nodeInfo - Node information
   * @returns {boolean} - True if node was updated, false otherwise
   */
  updateNodeInfo(nodeInfo) {
    // Check if node exists
    if (!this.nodes.has(nodeInfo.id)) {
      return this.registerNode(nodeInfo);
    }
    
    // Get existing node information
    const existingNodeInfo = this.nodes.get(nodeInfo.id);
    
    // Check if capabilities have changed
    const existingCapabilities = existingNodeInfo.capabilities || [this.options.defaultCapability];
    const newCapabilities = nodeInfo.capabilities || [this.options.defaultCapability];
    
    // Check if capabilities have changed
    const capabilitiesChanged = !this._arraysEqual(existingCapabilities, newCapabilities);
    
    if (capabilitiesChanged) {
      // Unregister old capabilities
      for (const capability of existingCapabilities) {
        if (this.capabilities.has(capability)) {
          this.capabilities.get(capability).delete(nodeInfo.id);
          
          // Remove capability if no nodes have it
          if (this.capabilities.get(capability).size === 0) {
            this.capabilities.delete(capability);
          }
        }
      }
      
      // Register new capabilities
      for (const capability of newCapabilities) {
        if (!this.capabilities.has(capability)) {
          this.capabilities.set(capability, new Set());
        }
        
        this.capabilities.get(capability).add(nodeInfo.id);
      }
    }
    
    // Update node information
    this.nodes.set(nodeInfo.id, {
      ...existingNodeInfo,
      ...nodeInfo,
      capabilities: newCapabilities,
      updatedAt: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log(`Node ${nodeInfo.id} updated`);
      
      if (capabilitiesChanged) {
        console.log(`Node ${nodeInfo.id} capabilities changed:`, newCapabilities);
      }
    }
    
    // Emit node-updated event
    this.emit('node-updated', { nodeId: nodeInfo.id, capabilities: newCapabilities });
    
    return true;
  }

  /**
   * Route a task to a node with matching capabilities
   * @param {Object} task - Task to route
   * @param {Array} requiredCapabilities - Required capabilities
   * @returns {Object} - Node information or null if no matching node found
   */
  routeTask(task, requiredCapabilities = [this.options.defaultCapability]) {
    // Find nodes with matching capabilities
    const matchingNodes = this._findNodesWithCapabilities(requiredCapabilities);
    
    if (matchingNodes.length === 0) {
      if (this.options.enableLogging) {
        console.log(`No nodes found with capabilities:`, requiredCapabilities);
      }
      
      // Emit no-matching-nodes event
      this.emit('no-matching-nodes', { task, requiredCapabilities });
      
      return null;
    }
    
    // Select node based on load balancing strategy
    const selectedNode = this._selectNode(matchingNodes);
    
    if (this.options.enableLogging) {
      console.log(`Task routed to node ${selectedNode.id}`);
    }
    
    // Emit task-routed event
    this.emit('task-routed', { taskId: task.id, nodeId: selectedNode.id });
    
    return selectedNode;
  }

  /**
   * Find nodes with matching capabilities
   * @param {Array} requiredCapabilities - Required capabilities
   * @returns {Array} - Matching nodes
   * @private
   */
  _findNodesWithCapabilities(requiredCapabilities) {
    // Get all nodes
    const allNodes = Array.from(this.nodes.values());
    
    // Filter nodes based on capability matching strategy
    switch (this.options.capabilityMatchingStrategy) {
      case 'exact':
        // Node must have exactly the required capabilities
        return allNodes.filter(node => 
          this._arraysEqual(node.capabilities, requiredCapabilities)
        );
      
      case 'subset':
        // Node must have at least the required capabilities
        return allNodes.filter(node => 
          requiredCapabilities.every(cap => node.capabilities.includes(cap))
        );
      
      case 'best-match':
        // Find nodes with the most matching capabilities
        const matchCounts = allNodes.map(node => {
          const matchCount = requiredCapabilities.filter(cap => 
            node.capabilities.includes(cap)
          ).length;
          
          return { node, matchCount };
        });
        
        // Sort by match count (descending)
        matchCounts.sort((a, b) => b.matchCount - a.matchCount);
        
        // Get nodes with highest match count
        const highestMatchCount = matchCounts.length > 0 ? matchCounts[0].matchCount : 0;
        
        return matchCounts
          .filter(item => item.matchCount === highestMatchCount)
          .map(item => item.node);
      
      default:
        // Default to subset matching
        return allNodes.filter(node => 
          requiredCapabilities.every(cap => node.capabilities.includes(cap))
        );
    }
  }

  /**
   * Select a node based on load balancing strategy
   * @param {Array} nodes - Nodes to select from
   * @returns {Object} - Selected node
   * @private
   */
  _selectNode(nodes) {
    switch (this.options.loadBalancingStrategy) {
      case 'least-loaded':
        // Select node with lowest load
        return nodes.reduce((minNode, node) => 
          (node.load || 0) < (minNode.load || 0) ? node : minNode
        , nodes[0]);
      
      case 'round-robin':
        // Get round-robin index for first node
        const firstNodeId = nodes[0].id;
        let index = this.roundRobinIndices.get(firstNodeId) || 0;
        
        // Update round-robin index
        index = (index + 1) % nodes.length;
        this.roundRobinIndices.set(firstNodeId, index);
        
        return nodes[index];
      
      case 'random':
        // Select random node
        return nodes[Math.floor(Math.random() * nodes.length)];
      
      default:
        // Default to least-loaded
        return nodes.reduce((minNode, node) => 
          (node.load || 0) < (minNode.load || 0) ? node : minNode
        , nodes[0]);
    }
  }

  /**
   * Check if two arrays are equal
   * @param {Array} arr1 - First array
   * @param {Array} arr2 - Second array
   * @returns {boolean} - True if arrays are equal, false otherwise
   * @private
   */
  _arraysEqual(arr1, arr2) {
    if (arr1.length !== arr2.length) {
      return false;
    }
    
    const sorted1 = [...arr1].sort();
    const sorted2 = [...arr2].sort();
    
    return sorted1.every((val, i) => val === sorted2[i]);
  }

  /**
   * Get all registered nodes
   * @returns {Array} - Array of node information
   */
  getNodes() {
    return Array.from(this.nodes.values());
  }

  /**
   * Get node information
   * @param {string} nodeId - Node ID
   * @returns {Object} - Node information
   */
  getNodeInfo(nodeId) {
    return this.nodes.get(nodeId);
  }

  /**
   * Get all registered capabilities
   * @returns {Array} - Array of capabilities
   */
  getCapabilities() {
    return Array.from(this.capabilities.keys());
  }

  /**
   * Get nodes with capability
   * @param {string} capability - Capability
   * @returns {Array} - Array of node IDs
   */
  getNodesWithCapability(capability) {
    if (!this.capabilities.has(capability)) {
      return [];
    }
    
    return Array.from(this.capabilities.get(capability));
  }

  /**
   * Dispose resources
   */
  dispose() {
    // Clear registries
    this.nodes.clear();
    this.capabilities.clear();
    this.roundRobinIndices.clear();
    
    if (this.options.enableLogging) {
      console.log('CapabilityRouter disposed');
    }
  }
}

/**
 * Create a capability router with recommended settings
 * @param {Object} options - Configuration options
 * @returns {CapabilityRouter} - Configured capability router
 */
function createCapabilityRouter(options = {}) {
  return new CapabilityRouter({
    enableLogging: true,
    defaultCapability: 'default',
    loadBalancingStrategy: 'least-loaded',
    capabilityMatchingStrategy: 'subset',
    ...options
  });
}

module.exports = {
  CapabilityRouter,
  createCapabilityRouter
};
