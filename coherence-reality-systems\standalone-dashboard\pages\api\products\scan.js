const TriadicAffiliateEngine = require('../../utils/triadic-affiliate-engine');

export default async function handler(req, res) {
  try {
    const engine = new TriadicAffiliateEngine();
    const result = await engine.scanProducts();

    res.status(200).json({
      products: result.products,
      metrics: result.metrics,
      status: result.status,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error scanning products:', error);
    res.status(500).json({
      error: 'Failed to scan products',
      details: error.message,
      timestamp: new Date().toISOString()
    });
  }
}
      consciousness_score: 0.90
    }];
}

function calculateMetrics(products) {
  return {
    totalRevenue: products.reduce((sum, p) => sum + (p.price * p.commission), 0),
    monthlySales: products.length * 10, // Average 10 sales per product
    conversionRate: 0.081, // 8.1% conversion rate
    roi: 314 // 314% ROI
  };
}
