/**
 * Animation utility functions for the NovaFuse Compliance App Store
 */

/**
 * Fade in animation
 * @param {number} duration - Animation duration in milliseconds
 * @param {number} delay - Animation delay in milliseconds
 * @returns {Object} - Animation properties
 */
export const fadeIn = (duration = 500, delay = 0) => {
  return {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    transition: {
      duration: duration / 1000,
      delay: delay / 1000,
      ease: 'easeInOut'
    }
  };
};

/**
 * Fade in up animation
 * @param {number} duration - Animation duration in milliseconds
 * @param {number} delay - Animation delay in milliseconds
 * @param {number} y - Y offset in pixels
 * @returns {Object} - Animation properties
 */
export const fadeInUp = (duration = 500, delay = 0, y = 20) => {
  return {
    initial: { opacity: 0, y },
    animate: { opacity: 1, y: 0 },
    transition: {
      duration: duration / 1000,
      delay: delay / 1000,
      ease: 'easeOut'
    }
  };
};

/**
 * Fade in down animation
 * @param {number} duration - Animation duration in milliseconds
 * @param {number} delay - Animation delay in milliseconds
 * @param {number} y - Y offset in pixels
 * @returns {Object} - Animation properties
 */
export const fadeInDown = (duration = 500, delay = 0, y = -20) => {
  return {
    initial: { opacity: 0, y },
    animate: { opacity: 1, y: 0 },
    transition: {
      duration: duration / 1000,
      delay: delay / 1000,
      ease: 'easeOut'
    }
  };
};

/**
 * Fade in left animation
 * @param {number} duration - Animation duration in milliseconds
 * @param {number} delay - Animation delay in milliseconds
 * @param {number} x - X offset in pixels
 * @returns {Object} - Animation properties
 */
export const fadeInLeft = (duration = 500, delay = 0, x = 20) => {
  return {
    initial: { opacity: 0, x },
    animate: { opacity: 1, x: 0 },
    transition: {
      duration: duration / 1000,
      delay: delay / 1000,
      ease: 'easeOut'
    }
  };
};

/**
 * Fade in right animation
 * @param {number} duration - Animation duration in milliseconds
 * @param {number} delay - Animation delay in milliseconds
 * @param {number} x - X offset in pixels
 * @returns {Object} - Animation properties
 */
export const fadeInRight = (duration = 500, delay = 0, x = -20) => {
  return {
    initial: { opacity: 0, x },
    animate: { opacity: 1, x: 0 },
    transition: {
      duration: duration / 1000,
      delay: delay / 1000,
      ease: 'easeOut'
    }
  };
};

/**
 * Scale in animation
 * @param {number} duration - Animation duration in milliseconds
 * @param {number} delay - Animation delay in milliseconds
 * @param {number} scale - Initial scale
 * @returns {Object} - Animation properties
 */
export const scaleIn = (duration = 500, delay = 0, scale = 0.9) => {
  return {
    initial: { opacity: 0, scale },
    animate: { opacity: 1, scale: 1 },
    transition: {
      duration: duration / 1000,
      delay: delay / 1000,
      ease: 'easeOut'
    }
  };
};

/**
 * Stagger children animation
 * @param {number} staggerDuration - Stagger duration in milliseconds
 * @param {number} initialDelay - Initial delay in milliseconds
 * @returns {Object} - Animation properties for parent container
 */
export const staggerContainer = (staggerDuration = 100, initialDelay = 0) => {
  return {
    animate: {
      transition: {
        staggerChildren: staggerDuration / 1000,
        delayChildren: initialDelay / 1000
      }
    }
  };
};

/**
 * Pulse animation
 * @param {number} duration - Animation duration in milliseconds
 * @param {number} scale - Scale factor
 * @returns {Object} - Animation properties
 */
export const pulse = (duration = 1500, scale = 1.05) => {
  return {
    animate: {
      scale: [1, scale, 1],
      transition: {
        duration: duration / 1000,
        ease: 'easeInOut',
        times: [0, 0.5, 1],
        repeat: Infinity,
        repeatDelay: 0.5
      }
    }
  };
};

/**
 * Bounce animation
 * @param {number} duration - Animation duration in milliseconds
 * @param {number} y - Y offset in pixels
 * @returns {Object} - Animation properties
 */
export const bounce = (duration = 1500, y = -10) => {
  return {
    animate: {
      y: [0, y, 0],
      transition: {
        duration: duration / 1000,
        ease: 'easeInOut',
        times: [0, 0.5, 1],
        repeat: Infinity,
        repeatDelay: 0.25
      }
    }
  };
};

/**
 * Rotate animation
 * @param {number} duration - Animation duration in milliseconds
 * @param {number} rotate - Rotation angle in degrees
 * @returns {Object} - Animation properties
 */
export const rotate = (duration = 2000, rotate = 360) => {
  return {
    animate: {
      rotate,
      transition: {
        duration: duration / 1000,
        ease: 'linear',
        repeat: Infinity
      }
    }
  };
};
