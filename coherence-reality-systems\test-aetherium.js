const axios = require('axios');
const BN = require('bn.js');
const { randomBytes } = require('crypto');

const SERVER_URL = 'http://localhost:8080';

// Helper to generate random Ethereum-style address
function randomAddress() {
  return '0x' + randomBytes(20).toString('hex');
}

// Test suite for Aetherium Gas System
async function runTests() {
  console.log('🚀 Starting Aetherium Gas System Tests\n');
  
  // Create test accounts
  const accounts = {
    alice: randomAddress(),
    bob: randomAddress(),
    charlie: randomAddress()
  };
  
  // Initialize accounts with some AE (in a real test, we'd fund these from genesis)
  console.log('📝 Test accounts created:');
  Object.entries(accounts).forEach(([name, addr]) => {
    console.log(`  ${name.padEnd(8)}: ${addr}`);
  });
  
  // Test 1: Get gas price
  console.log('\n🔍 Test 1: Get current gas price');
  try {
    const { data: gasPrice } = await axios.get(`${SERVER_URL}/aetherium/gasPrice`);
    console.log('✅ Gas price:', {
      baseFee: gasPrice.baseFee,
      maxPriorityFee: gasPrice.maxPriorityFee,
      maxFee: gasPrice.maxFee
    });
  } catch (error) {
    console.error('❌ Failed to get gas price:', error.message);
  }
  
  // Test 2: Estimate gas for a simple transfer
  console.log('\n🔍 Test 2: Estimate gas for transfer');
  try {
    const { data: estimate } = await axios.post(
      `${SERVER_URL}/aetherium/estimate`,
      {
        from: accounts.alice,
        to: accounts.bob,
        value: '**********000000000' // 1 AE
      }
    );
    console.log('✅ Gas estimate:', {
      gasEstimate: estimate.gasEstimate,
      baseFee: estimate.baseFee
    });
  } catch (error) {
    console.error('❌ Failed to estimate gas:', error.message);
  }
  
  // Test 3: Send a transaction (will fail due to insufficient balance)
  console.log('\n🔍 Test 3: Attempt transfer with insufficient balance');
  try {
    await axios.post(
      `${SERVER_URL}/aetherium/send`,
      {
        from: accounts.alice,
        to: accounts.bob,
        value: '**********000000000', // 1 AE
        maxFeePerGas: '**********', // 2 Gwei
        maxPriorityFeePerGas: '**********' // 1 Gwei
      }
    );
    console.log('❌ Transfer should have failed due to insufficient balance');
  } catch (error) {
    console.log('✅ Expected error:', error.response?.data?.error || error.message);
  }
  
  // Test 4: Get balance of genesis account
  console.log('\n🔍 Test 4: Check genesis account balance');
  try {
    const { data: genesisBalance } = await axios.get(
      `${SERVER_URL}/aetherium/balance/******************************************`
    );
    console.log('✅ Genesis balance:', {
      wei: genesisBalance.balance,
      ae: genesisBalance.formatted
    });
  } catch (error) {
    console.error('❌ Failed to get genesis balance:', error.message);
  }
  
  // Test 5: Submit a Crown Consensus with gas payment
  console.log('\n🔍 Test 5: Submit Crown Consensus with gas payment');
  try {
    const { data: consensus } = await axios.post(
      `${SERVER_URL}/crown-consensus`,
      {
        content_id: 'test-content-123',
        transaction_data: { action: 'validate', data: 'test' },
        gasOptions: {
          from: accounts.alice,
          maxFeePerGas: '**********',
          maxPriorityFeePerGas: '**********'
        }
      },
      {
        headers: {
          'x-node-id': 'test-node-1',
          'x-consciousness-level': '3000',
          'x-validation-status': 'true'
        }
      }
    );
    console.log('✅ Consensus result:', consensus.consensus_reached ? '✅ Consensus reached' : '❌ No consensus');
  } catch (error) {
    console.error('❌ Failed to submit consensus:', error.response?.data?.error || error.message);
  }
  
  console.log('\n🏁 Aetherium Gas System Tests Complete');
}

// Run tests
runTests().catch(console.error);
