# Create a new directory for the organized files
$targetDir = "d:\novafuse-api-superstore\coherence-reality-systems\organized-md"
New-Item -ItemType Directory -Path $targetDir -Force

# Get all MD files from root
$mdFiles = Get-ChildItem -Path "d:\novafuse-api-superstore\coherence-reality-systems" -File -Include *.md

# Move each file to the new directory
foreach ($file in $mdFiles) {
    Move-Item -Path $file.FullName -Destination $targetDir
}

Write-Host "Files have been moved to $targetDir"
