import React from 'react';
import { motion } from 'framer-motion';

/**
 * Page transition component for smooth page transitions
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Page content
 */
const PageTransition = ({ children }) => {
  // Page transition animation
  const pageVariants = {
    initial: {
      opacity: 0,
      y: 20
    },
    animate: {
      opacity: 1,
      y: 0
    },
    exit: {
      opacity: 0,
      y: -20
    }
  };

  // Page transition timing
  const pageTransition = {
    type: 'tween',
    ease: 'easeInOut',
    duration: 0.3
  };

  return (
    <motion.div
      initial="initial"
      animate="animate"
      exit="exit"
      variants={pageVariants}
      transition={pageTransition}
    >
      {children}
    </motion.div>
  );
};

export default PageTransition;
