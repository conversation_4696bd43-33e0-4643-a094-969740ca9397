import React from 'react';
import { motion } from 'framer-motion';

const CBEConsole = ({ logs, onCommand }) => {
    return (
        <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="p-6 rounded-lg shadow-lg bg-white dark:bg-gray-800"
        >
            <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-bold">CBE Console</h2>
            </div>

            <div className="mb-4">
                <textarea
                    className="w-full h-64 p-4 border rounded-lg resize-none"
                    placeholder="Console logs will appear here..."
                    value={logs}
                    readOnly
                />
            </div>

            <div className="flex gap-4">
                <input
                    type="text"
                    placeholder="Enter command..."
                    className="flex-1 p-2 border rounded-lg"
                    onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                            onCommand(e.target.value);
                            e.target.value = '';
                        }
                    }}
                />
                <button
                    onClick={() => onCommand('clear')}
                    className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
                >
                    Clear
                </button>
            </div>
        </motion.div>
    );
};

export default CBEConsole;
