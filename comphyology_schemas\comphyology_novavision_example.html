
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Comphyology NovaVision Integration</title>
  <style>
    body {
      font-family: 'Arial', sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    
    header {
      text-align: center;
      margin-bottom: 40px;
    }
    
    h1 {
      color: #2c3e50;
      margin-bottom: 10px;
    }
    
    .subtitle {
      color: #7f8c8d;
      font-size: 1.2em;
      margin-bottom: 20px;
    }
    
    .intro {
      background-color: #f8f9fa;
      padding: 20px;
      border-radius: 8px;
      margin-bottom: 30px;
      border-left: 5px solid #3498db;
    }
    
    .schema-container {
      margin-bottom: 40px;
    }
    
    .schema-title {
      font-size: 1.5em;
      color: #2c3e50;
      margin-bottom: 10px;
    }
    
    .schema-description {
      color: #7f8c8d;
      margin-bottom: 20px;
    }
    
    pre {
      background-color: #f8f9fa;
      padding: 15px;
      border-radius: 8px;
      overflow-x: auto;
      border: 1px solid #ddd;
    }
    
    code {
      font-family: 'Courier New', monospace;
      font-size: 0.9em;
    }
    
    .note {
      background-color: #fff8e1;
      padding: 15px;
      border-radius: 8px;
      margin-top: 20px;
      border-left: 5px solid #ffc107;
    }
    
    footer {
      margin-top: 50px;
      text-align: center;
      color: #7f8c8d;
      font-size: 0.9em;
      padding-top: 20px;
      border-top: 1px solid #eee;
    }
  </style>
</head>
<body>
  <header>
    <h1>Comphyology (Ψᶜ) NovaVision Integration</h1>
    <div class="subtitle">Visualizing Comphyology concepts using NovaFuse's Universal UI Framework</div>
  </header>
  
  <div class="intro">
    <p>
      This page demonstrates the integration of Comphyology with NovaVision, allowing Comphyology concepts
      to be visualized using NovaFuse's Universal UI Framework.
    </p>
    <p>
      The schemas below can be used with NovaVision's rendering system to create interactive visualizations
      of Comphyology concepts.
    </p>
  </div>
  
  <div class="schema-container">
    <div class="schema-title">Morphological Resonance Field</div>
    <div class="schema-description">
      Visualization of how structural complexity interacts with environmental factors
    </div>
    <pre><code>{}</code></pre>
  </div>
  
  <div class="schema-container">
    <div class="schema-title">Quantum Phase Space Map</div>
    <div class="schema-description">
      Visualization of entropy-phase relationships and pattern detection
    </div>
    <pre><code>{}</code></pre>
  </div>
  
  <div class="schema-container">
    <div class="schema-title">Comphyology Dashboard</div>
    <div class="schema-description">
      Comprehensive visualization of Comphyology concepts
    </div>
    <pre><code>{}</code></pre>
  </div>
  
  <div class="note">
    <p>
      <strong>Note:</strong> To use these schemas with NovaVision, you need to:
    </p>
    <ol>
      <li>Import the NovaVision library</li>
      <li>Load the schema</li>
      <li>Use NovaVision's rendering system to render the UI</li>
    </ol>
    <p>
      See the React example in <code>src/comphyology/examples/react-example.jsx</code> for a complete example.
    </p>
  </div>
  
  <footer>
    <p>NovaFuse Comphyology (Ψᶜ) Framework - Copyright © 2025</p>
  </footer>
</body>
</html>
  