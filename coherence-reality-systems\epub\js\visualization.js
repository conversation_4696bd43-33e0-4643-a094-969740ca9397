// Phase Space Visualization Engine
class PhaseSpaceVisualizer {
    constructor(containerId) {
        this.container = document.getElementById(containerId);
        this.scene = new THREE.Scene();
        this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        this.renderer = new THREE.WebGLRenderer({ antialias: true });
        this.controls = new THREE.OrbitControls(this.camera, this.renderer.domElement);
        
        this.init();
    }

    init() {
        // Set up renderer
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.container.appendChild(this.renderer.domElement);
        
        // Set up camera
        this.camera.position.z = 5;
        
        // Add lighting
        const ambientLight = new THREE.AmbientLight(0x404040);
        this.scene.add(ambientLight);
        
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.5);
        directionalLight.position.set(1, 1, 1);
        this.scene.add(directionalLight);
    }

    createHarmonicField(equation) {
        // Create geometry based on equation parameters
        const geometry = new THREE.SphereGeometry(1, 32, 32);
        const material = new THREE.MeshPhongMaterial({
            color: 0x2c3e50,
            shininess: 100
        });
        
        const sphere = new THREE.Mesh(geometry, material);
        this.scene.add(sphere);
        
        // Add interactive controls
        this.controls.enableDamping = true;
        this.controls.dampingFactor = 0.05;
        
        // Add parameter controls
        this.addParameterControls(equation);
    }

    addParameterControls(equation) {
        const controls = {
            PCI: 1.0,
            TY: 1.0,
            κSR: 1.0
        };
        
        const gui = new dat.GUI();
        
        gui.add(controls, 'PCI', 0.0, 2.0).onChange(() => this.updateField());
        gui.add(controls, 'TY', 0.0, 2.0).onChange(() => this.updateField());
        gui.add(controls, 'κSR', 0.0, 2.0).onChange(() => this.updateField());
    }

    updateField() {
        // Update field visualization based on parameters
        // This would be connected to the equation parameters
    }

    animate() {
        requestAnimationFrame(() => this.animate());
        this.controls.update();
        this.renderer.render(this.scene, this.camera);
    }
}

// Validation Calculator
class ValidationCalculator {
    constructor(containerId) {
        this.container = document.getElementById(containerId);
        this.init();
    }

    init() {
        this.container.innerHTML = `
            <h3>Validation Calculator</h3>
            <div class="calculator">
                <div class="input-group">
                    <label>PCI:</label>
                    <input type="number" id="pci" value="1.0">
                </div>
                <div class="input-group">
                    <label>TY:</label>
                    <input type="number" id="ty" value="1.0">
                </div>
                <div class="input-group">
                    <label>κSR:</label>
                    <input type="number" id="ksr" value="1.0">
                </div>
                <button onclick="calculateII()">Calculate II</button>
                <div id="result"></div>
            </div>
        `;
    }

    calculateII() {
        const PCI = parseFloat(document.getElementById('pci').value);
        const TY = parseFloat(document.getElementById('ty').value);
        const κSR = parseFloat(document.getElementById('ksr').value);
        
        const II = (PCI * TY * κSR).toPrecision(4);
        document.getElementById('result').innerHTML = `II: ${II}`;
    }
}

// Initialize visualizations when page loads
document.addEventListener('DOMContentLoaded', () => {
    const visualizer = new PhaseSpaceVisualizer('visualization-container');
    visualizer.createHarmonicField('eq12.1.1');
    visualizer.animate();
    
    const calculator = new ValidationCalculator('calculator-container');
});
