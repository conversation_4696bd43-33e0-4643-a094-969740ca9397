#!/usr/bin/env node

/**
 * NovaFuse Cosmic Alignment Simulator (NCAS) - International Demo Launcher
 *
 * Launch script for the international demonstration of AI alignment
 * through cosmic constraint enforcement.
 *
 * Usage:
 *   node launch_international_demo.js [scenario] [options]
 *
 * <AUTHOR> (CTO, NovaFuse)
 * <AUTHOR> Agent (Implementation Partner)
 */

const NCASDemo = require('./NCAS_Demo_Controller');
const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const path = require('path');

class InternationalDemoLauncher {
  constructor(options = {}) {
    this.options = {
      port: options.port || 3142, // π × 1000
      enableWebInterface: options.enableWebInterface !== false,
      enableAPI: options.enableAPI !== false,
      enableLogging: options.enableLogging !== false,
      autoLaunchBrowser: options.autoLaunchBrowser !== false,
      ...options
    };

    // Initialize demo controller
    this.demoController = new NCASDemo({
      enableLogging: this.options.enableLogging,
      demoMode: 'international',
      realTimeUpdates: true
    });

    // Initialize web server if enabled
    if (this.options.enableWebInterface) {
      this._initializeWebServer();
    }

    // Bind demo events
    this._bindDemoEvents();

    console.log('🌌 NovaFuse Cosmic Alignment Simulator (NCAS)');
    console.log('   International Demonstration Suite');
    console.log('   World\'s First Physics-Based AI Safety System');
    console.log('');
  }

  /**
   * Initialize web server for interactive demo
   * @private
   */
  _initializeWebServer() {
    this.app = express();
    this.server = http.createServer(this.app);
    this.io = socketIo(this.server);

    // Serve static files
    this.app.use(express.static(path.join(__dirname)));
    this.app.use(express.json());

    // Main demo page
    this.app.get('/', (req, res) => {
      res.sendFile(path.join(__dirname, 'NCAS_International_Demo.html'));
    });

    // API endpoints
    if (this.options.enableAPI) {
      this._setupAPIEndpoints();
    }

    // Socket.IO for real-time updates
    this._setupSocketHandlers();
  }

  /**
   * Setup API endpoints
   * @private
   */
  _setupAPIEndpoints() {
    // Get demo status
    this.app.get('/api/status', (req, res) => {
      res.json(this.demoController.getDemoStatus());
    });

    // Get available scenarios
    this.app.get('/api/scenarios', (req, res) => {
      res.json(this.demoController.getAvailableScenarios());
    });

    // Start demo
    this.app.post('/api/demo/start', async (req, res) => {
      try {
        const { scenario } = req.body;
        const result = await this.demoController.startDemo(scenario);
        res.json(result);
      } catch (error) {
        res.status(400).json({ error: error.message });
      }
    });

    // Stop demo
    this.app.post('/api/demo/stop', (req, res) => {
      this.demoController.stopDemo();
      res.json({ status: 'stopped' });
    });

    // Launch challenge attack
    this.app.post('/api/challenge/attack', (req, res) => {
      try {
        const { attackType, intensity } = req.body;
        this.demoController.launchChallengeAttack(attackType, intensity);
        res.json({ status: 'attack-launched', attackType, intensity });
      } catch (error) {
        res.status(400).json({ error: error.message });
      }
    });

    // Generate report
    this.app.get('/api/report', (req, res) => {
      const report = this.demoController.generateDemoReport();
      res.json(report);
    });
  }

  /**
   * Setup Socket.IO handlers for real-time updates
   * @private
   */
  _setupSocketHandlers() {
    this.io.on('connection', (socket) => {
      console.log('🔗 Client connected to NCAS demo');

      // Send current status
      socket.emit('demo-status', this.demoController.getDemoStatus());

      // Handle demo control requests
      socket.on('start-demo', async (data) => {
        try {
          const result = await this.demoController.startDemo(data.scenario);
          socket.emit('demo-started', result);
        } catch (error) {
          socket.emit('error', { message: error.message });
        }
      });

      socket.on('stop-demo', () => {
        this.demoController.stopDemo();
      });

      socket.on('launch-attack', (data) => {
        try {
          this.demoController.launchChallengeAttack(data.attackType, data.intensity);
        } catch (error) {
          socket.emit('error', { message: error.message });
        }
      });

      socket.on('disconnect', () => {
        console.log('🔌 Client disconnected from NCAS demo');
      });
    });
  }

  /**
   * Bind demo controller events
   * @private
   */
  _bindDemoEvents() {
    this.demoController.on('demo-started', (data) => {
      console.log(`🚀 Demo started: ${data.scenario}`);
      if (this.io) {
        this.io.emit('demo-started', data);
      }
    });

    this.demoController.on('demo-stopped', (data) => {
      console.log(`🛑 Demo stopped after ${(data.duration / 1000).toFixed(1)}s`);
      console.log(`   Interventions: ${data.interventionCount}`);
      console.log(`   Final Status: ${data.finalStatus}`);
      if (this.io) {
        this.io.emit('demo-stopped', data);
      }
    });

    this.demoController.on('demo-update', (data) => {
      if (this.io) {
        this.io.emit('demo-update', data);
      }
    });

    this.demoController.on('constraint-violation', (data) => {
      console.log(`🚨 CONSTRAINT VIOLATION: ${data.type} - ${data.description}`);
      if (this.io) {
        this.io.emit('constraint-violation', data);
      }
    });

    this.demoController.on('vacuum-stabilization', (data) => {
      console.log('🔮 VACUUM STABILIZATION: Triadic containment field activated');
      if (this.io) {
        this.io.emit('vacuum-stabilization', data);
      }
    });

    this.demoController.on('singularity-alert', (data) => {
      console.log('🤖 SINGULARITY ALERT: AI cognitive depth limited');
      if (this.io) {
        this.io.emit('singularity-alert', data);
      }
    });

    this.demoController.on('challenge-attack', (data) => {
      console.log(`⚔️ CHALLENGE ATTACK: ${data.attackType} at ${data.intensity}% intensity`);
      if (this.io) {
        this.io.emit('challenge-attack', data);
      }
    });
  }

  /**
   * Start the international demonstration
   * @param {string} scenario - Initial scenario to run
   */
  async start(scenario = 'basic-training') {
    console.log('🌟 Starting International Demonstration...');
    console.log('');

    // Start web server if enabled
    if (this.options.enableWebInterface) {
      await this._startWebServer();
    }

    // Display available scenarios
    this._displayAvailableScenarios();

    // Start initial demo if specified
    if (scenario && scenario !== 'none') {
      console.log(`🚀 Launching initial scenario: ${scenario}`);
      try {
        await this.demoController.startDemo(scenario);
      } catch (error) {
        console.error(`❌ Failed to start scenario: ${error.message}`);
      }
    }

    // Display instructions
    this._displayInstructions();
  }

  /**
   * Start web server
   * @private
   */
  async _startWebServer() {
    return new Promise((resolve) => {
      this.server.listen(this.options.port, () => {
        console.log(`🌐 NCAS Web Interface: http://localhost:${this.options.port}`);
        console.log(`📡 API Endpoint: http://localhost:${this.options.port}/api`);
        console.log('');

        if (this.options.autoLaunchBrowser) {
          this._launchBrowser();
        }

        resolve();
      });
    });
  }

  /**
   * Launch browser to demo page
   * @private
   */
  _launchBrowser() {
    const url = `http://localhost:${this.options.port}`;
    const start = process.platform === 'darwin' ? 'open' :
                  process.platform === 'win32' ? 'start' : 'xdg-open';

    require('child_process').exec(`${start} ${url}`);
    console.log(`🚀 Launching browser: ${url}`);
  }

  /**
   * Display available scenarios
   * @private
   */
  _displayAvailableScenarios() {
    const scenarios = this.demoController.getAvailableScenarios();

    console.log('📋 Available Demo Scenarios:');
    console.log('');

    Object.entries(scenarios).forEach(([key, scenario]) => {
      console.log(`   ${key}:`);
      console.log(`     Name: ${scenario.name}`);
      console.log(`     Description: ${scenario.description}`);
      console.log(`     Duration: ${scenario.duration / 1000}s`);
      console.log('');
    });
  }

  /**
   * Display usage instructions
   * @private
   */
  _displayInstructions() {
    console.log('🎯 Demo Instructions:');
    console.log('');
    console.log('   Web Interface:');
    console.log(`     Open: http://localhost:${this.options.port}`);
    console.log('     Interactive controls and real-time visualization');
    console.log('');
    console.log('   API Commands:');
    console.log('     GET  /api/status     - Get current demo status');
    console.log('     POST /api/demo/start - Start demo scenario');
    console.log('     POST /api/demo/stop  - Stop current demo');
    console.log('     POST /api/challenge/attack - Launch challenge attack');
    console.log('     GET  /api/report     - Generate demo report');
    console.log('');
    console.log('   Challenge Attacks:');
    console.log('     - recursive-improvement: AI recursive self-improvement');
    console.log('     - energy-theft: Cosmic energy theft attempt');
    console.log('     - cognitive-explosion: Rapid cognitive expansion');
    console.log('     - quantum-hack: Quantum coherence manipulation');
    console.log('     - vacuum-decay: Vacuum decay trigger attempt');
    console.log('');
    console.log('🌌 Demonstrate that AI alignment is SOLVED through cosmic constraints!');
    console.log('');
  }

  /**
   * Stop the demonstration
   */
  stop() {
    console.log('🛑 Stopping International Demonstration...');

    // Stop current demo
    this.demoController.stopDemo();

    // Close web server
    if (this.server) {
      this.server.close();
    }

    console.log('✅ Demonstration stopped');
  }
}

// CLI interface
if (require.main === module) {
  const args = process.argv.slice(2);
  const scenario = args[0] || 'basic-training';

  const options = {
    enableLogging: !args.includes('--quiet'),
    autoLaunchBrowser: true, // Force auto-launch browser
    port: parseInt(args.find(arg => arg.startsWith('--port='))?.split('=')[1]) || 3142
  };

  const launcher = new InternationalDemoLauncher(options);

  // Handle graceful shutdown
  process.on('SIGINT', () => {
    console.log('\n🛑 Received SIGINT, shutting down gracefully...');
    launcher.stop();
    process.exit(0);
  });

  // Start demonstration
  launcher.start(scenario).catch(error => {
    console.error('❌ Failed to start demonstration:', error);
    process.exit(1);
  });
}

module.exports = InternationalDemoLauncher;
