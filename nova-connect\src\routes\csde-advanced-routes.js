/**
 * CSDE Advanced Routes
 *
 * This module provides API routes for advanced CSDE features:
 * - Offline Processing Engine for offline data processing
 * - Cross-Domain Predictor for cross-domain insights
 * - Compliance Mapper for compliance mapping
 */

const express = require('express');
const path = require('path');
const CSEDAdvancedIntegration = require('../integrations/csde-advanced-integration');

// Create router
const router = express.Router();

// Create CSDE advanced integration
let csdeAdvancedIntegration = null;

/**
 * Initialize the CSDE advanced integration
 * @param {Object} options - Initialization options
 */
function initialize(options = {}) {
  const logger = options.logger || console;

  logger.info('Initializing CSDE advanced routes');

  // Create CSDE advanced integration if not already created
  if (!csdeAdvancedIntegration) {
    csdeAdvancedIntegration = new CSEDAdvancedIntegration({
      // Offline processing options
      enableOfflineProcessing: process.env.ENABLE_OFFLINE_PROCESSING !== 'false',
      storageDir: process.env.OFFLINE_STORAGE_DIR || path.join(process.cwd(), 'data', 'offline-processing'),
      maxBatchSize: parseInt(process.env.MAX_BATCH_SIZE || '100', 10),
      compressionLevel: parseInt(process.env.COMPRESSION_LEVEL || '6', 10),
      retentionDays: parseInt(process.env.RETENTION_DAYS || '30', 10),
      syncInterval: parseInt(process.env.SYNC_INTERVAL || '3600000', 10),

      // Cross-domain prediction options
      enableCrossDomainPrediction: process.env.ENABLE_CROSS_DOMAIN_PREDICTION !== 'false',
      enableTensorFusion: options.enableTensorFusion !== false && process.env.ENABLE_TENSOR_FUSION !== 'false',
      enableAdaptiveLearning: options.enableAdaptiveLearning !== false && process.env.ENABLE_ADAPTIVE_LEARNING !== 'false',
      enableCircularTrustTopology: options.enableCircularTrustTopology !== false && process.env.ENABLE_CIRCULAR_TRUST_TOPOLOGY !== 'false',
      confidenceThreshold: options.confidenceThreshold || parseFloat(process.env.CONFIDENCE_THRESHOLD || '0.75'),
      maxPredictions: options.maxPredictions || parseInt(process.env.MAX_PREDICTIONS || '10', 10),
      modelStorageDir: process.env.MODEL_STORAGE_DIR || path.join(process.cwd(), 'data', 'cross-domain-models'),

      // Compliance mapping options
      enableComplianceMapping: process.env.ENABLE_COMPLIANCE_MAPPING !== 'false',

      // General options
      domain: process.env.DOMAIN || 'security',
      targetDomain: process.env.TARGET_DOMAIN || 'compliance',
      primaryFramework: process.env.PRIMARY_FRAMEWORK || 'NIST_CSF',
      targetFrameworks: (process.env.TARGET_FRAMEWORKS || 'PCI_DSS,HIPAA,SOC2').split(','),
      optimizationLevel: parseInt(process.env.OPTIMIZATION_LEVEL || '3', 10),
      logger,

      // Pass through any additional options
      ...options
    });

    logger.info('CSDE advanced integration created', {
      enableOfflineProcessing: csdeAdvancedIntegration.options.enableOfflineProcessing,
      enableCrossDomainPrediction: csdeAdvancedIntegration.options.enableCrossDomainPrediction,
      enableTensorFusion: csdeAdvancedIntegration.options.enableTensorFusion,
      enableAdaptiveLearning: csdeAdvancedIntegration.options.enableAdaptiveLearning,
      enableCircularTrustTopology: csdeAdvancedIntegration.options.enableCircularTrustTopology,
      enableComplianceMapping: csdeAdvancedIntegration.options.enableComplianceMapping
    });
  }
}

/**
 * Process data offline
 */
router.post('/offline', async (req, res) => {
  try {
    // Initialize if not already initialized
    if (!csdeAdvancedIntegration) {
      initialize({ logger: req.app.locals.logger });
    }

    // Get data from request body
    const data = req.body.data;
    const options = req.body.options || {};

    // Process data offline
    const result = await csdeAdvancedIntegration.processOffline(data, options);

    // Return result
    res.json({
      success: true,
      result
    });
  } catch (error) {
    req.app.locals.logger.error('Error processing data offline', { error: error.message });

    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * Get offline processing result
 */
router.get('/offline/:processingId', async (req, res) => {
  try {
    // Initialize if not already initialized
    if (!csdeAdvancedIntegration) {
      initialize({ logger: req.app.locals.logger });
    }

    // Get processing ID from request params
    const { processingId } = req.params;

    if (!processingId) {
      return res.status(400).json({
        success: false,
        error: 'Processing ID is required'
      });
    }

    // Get offline processing result
    const result = await csdeAdvancedIntegration.getOfflineResult(processingId);

    // Return result
    res.json({
      success: true,
      result
    });
  } catch (error) {
    req.app.locals.logger.error('Error getting offline processing result', {
      error: error.message,
      processingId: req.params.processingId
    });

    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * Sync offline results
 */
router.post('/offline/sync', async (req, res) => {
  try {
    // Initialize if not already initialized
    if (!csdeAdvancedIntegration) {
      initialize({ logger: req.app.locals.logger });
    }

    // Sync offline results
    const result = await csdeAdvancedIntegration.syncOfflineResults();

    // Return result
    res.json({
      success: true,
      result
    });
  } catch (error) {
    req.app.locals.logger.error('Error syncing offline results', { error: error.message });

    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * Clean up old offline results
 */
router.post('/offline/cleanup', async (req, res) => {
  try {
    // Initialize if not already initialized
    if (!csdeAdvancedIntegration) {
      initialize({ logger: req.app.locals.logger });
    }

    // Clean up old offline results
    const result = await csdeAdvancedIntegration.cleanupOfflineResults();

    // Return result
    res.json({
      success: true,
      result
    });
  } catch (error) {
    req.app.locals.logger.error('Error cleaning up old offline results', { error: error.message });

    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * Predict cross-domain insights
 */
router.post('/predict', async (req, res) => {
  try {
    // Initialize if not already initialized
    if (!csdeAdvancedIntegration) {
      initialize({ logger: req.app.locals.logger });
    }

    // Get data from request body
    const data = req.body.data;
    const targetDomain = req.body.targetDomain;
    const options = req.body.options || {};

    // Set advanced options
    const advancedOptions = {
      enableTensorFusion: options.enableTensorFusion !== false,
      enableAdaptiveLearning: options.enableAdaptiveLearning !== false,
      enableCircularTrustTopology: options.enableCircularTrustTopology !== false,
      confidenceThreshold: options.confidenceThreshold || 0.75,
      maxPredictions: options.maxPredictions || 10
    };

    // Update initialization options with advanced options
    if (!csdeAdvancedIntegration) {
      initialize({
        logger: req.app.locals.logger,
        ...advancedOptions
      });
    }

    // Predict cross-domain insights
    const result = await csdeAdvancedIntegration.predictCrossDomain(data, targetDomain);

    // Return result
    res.json({
      success: true,
      result
    });
  } catch (error) {
    req.app.locals.logger.error('Error predicting cross-domain insights', { error: error.message });

    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * Map compliance controls
 */
router.post('/compliance', async (req, res) => {
  try {
    // Initialize if not already initialized
    if (!csdeAdvancedIntegration) {
      initialize({ logger: req.app.locals.logger });
    }

    // Get data from request body
    const implementationData = req.body.implementationData;
    const primaryFramework = req.body.primaryFramework;
    const targetFrameworks = req.body.targetFrameworks;
    const options = req.body.options || {};

    // Set enhanced options
    const enhancedOptions = {
      enableEnhancedMapping: options.enableEnhancedMapping !== false,
      enableSemanticAnalysis: options.enableSemanticAnalysis !== false,
      enableMachineLearning: options.enableMachineLearning !== false,
      enableCrossFrameworkHarmonization: options.enableCrossFrameworkHarmonization !== false,
      enableComplianceVisualization: options.enableComplianceVisualization !== false
    };

    // Update initialization options with enhanced options
    if (!csdeAdvancedIntegration) {
      initialize({
        logger: req.app.locals.logger,
        ...enhancedOptions
      });
    } else {
      // Update existing integration options
      csdeAdvancedIntegration.options = {
        ...csdeAdvancedIntegration.options,
        ...enhancedOptions
      };
    }

    // Map compliance controls
    const result = await csdeAdvancedIntegration.mapCompliance(
      implementationData,
      primaryFramework,
      targetFrameworks
    );

    // Return result
    res.json({
      success: true,
      result
    });
  } catch (error) {
    req.app.locals.logger.error('Error mapping compliance controls', { error: error.message });

    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * Process data with all advanced features
 */
router.post('/process', async (req, res) => {
  try {
    // Initialize if not already initialized
    if (!csdeAdvancedIntegration) {
      initialize({ logger: req.app.locals.logger });
    }

    // Get data from request body
    const data = req.body.data;
    const options = req.body.options || {};

    // Process data with advanced features
    const result = await csdeAdvancedIntegration.processAdvanced(data, options);

    // Return result
    res.json({
      success: true,
      result
    });
  } catch (error) {
    req.app.locals.logger.error('Error processing data with advanced features', { error: error.message });

    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * Get advanced processing results
 */
router.get('/process/results', async (req, res) => {
  try {
    // Initialize if not already initialized
    if (!csdeAdvancedIntegration) {
      initialize({ logger: req.app.locals.logger });
    }

    // Get processing IDs from request query
    const processingIds = req.query.processingIds;

    if (!processingIds) {
      return res.status(400).json({
        success: false,
        error: 'Processing IDs are required'
      });
    }

    // Parse processing IDs
    let parsedProcessingIds;
    try {
      parsedProcessingIds = JSON.parse(processingIds);
    } catch (error) {
      return res.status(400).json({
        success: false,
        error: 'Invalid processing IDs format'
      });
    }

    // Get advanced processing results
    const results = await csdeAdvancedIntegration.getAdvancedResults(parsedProcessingIds);

    // Return results
    res.json({
      success: true,
      results
    });
  } catch (error) {
    req.app.locals.logger.error('Error getting advanced processing results', { error: error.message });

    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * Get metrics
 */
router.get('/metrics', async (req, res) => {
  try {
    // Initialize if not already initialized
    if (!csdeAdvancedIntegration) {
      initialize({ logger: req.app.locals.logger });
    }

    // Get metrics
    const metrics = csdeAdvancedIntegration.getMetrics();

    // Return metrics
    res.json({
      success: true,
      metrics
    });
  } catch (error) {
    req.app.locals.logger.error('Error getting metrics', { error: error.message });

    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * Reset metrics
 */
router.post('/metrics/reset', async (req, res) => {
  try {
    // Initialize if not already initialized
    if (!csdeAdvancedIntegration) {
      initialize({ logger: req.app.locals.logger });
    }

    // Reset metrics
    csdeAdvancedIntegration.resetMetrics();

    // Return success
    res.json({
      success: true,
      message: 'Metrics reset successfully'
    });
  } catch (error) {
    req.app.locals.logger.error('Error resetting metrics', { error: error.message });

    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * Get health
 */
router.get('/health', async (req, res) => {
  try {
    // Initialize if not already initialized
    if (!csdeAdvancedIntegration) {
      initialize({ logger: req.app.locals.logger });
    }

    // Get metrics
    const metrics = csdeAdvancedIntegration.getMetrics();

    // Calculate success rates
    const offlineSuccessRate = metrics.offlineProcessing.totalRequests > 0
      ? metrics.offlineProcessing.successfulRequests / metrics.offlineProcessing.totalRequests
      : 1;

    const predictionSuccessRate = metrics.crossDomainPrediction.totalRequests > 0
      ? metrics.crossDomainPrediction.successfulRequests / metrics.crossDomainPrediction.totalRequests
      : 1;

    const complianceSuccessRate = metrics.complianceMapping.totalRequests > 0
      ? metrics.complianceMapping.successfulRequests / metrics.complianceMapping.totalRequests
      : 1;

    // Calculate overall success rate
    const totalRequests = metrics.offlineProcessing.totalRequests +
      metrics.crossDomainPrediction.totalRequests +
      metrics.complianceMapping.totalRequests;

    const successfulRequests = metrics.offlineProcessing.successfulRequests +
      metrics.crossDomainPrediction.successfulRequests +
      metrics.complianceMapping.successfulRequests;

    const overallSuccessRate = totalRequests > 0
      ? successfulRequests / totalRequests
      : 1;

    // Determine health status
    const status = overallSuccessRate >= 0.95 ? 'healthy' : 'degraded';

    // Return health
    res.json({
      success: true,
      health: {
        status,
        metrics: {
          totalRequests,
          successfulRequests,
          overallSuccessRate,
          offlineProcessing: {
            successRate: offlineSuccessRate,
            averageLatency: metrics.offlineProcessing.averageLatency
          },
          crossDomainPrediction: {
            successRate: predictionSuccessRate,
            averageLatency: metrics.crossDomainPrediction.averageLatency
          },
          complianceMapping: {
            successRate: complianceSuccessRate,
            averageLatency: metrics.complianceMapping.averageLatency
          }
        },
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    req.app.locals.logger.error('Error getting health', { error: error.message });

    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

module.exports = {
  router,
  initialize
};
