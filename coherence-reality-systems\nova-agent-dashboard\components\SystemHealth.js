import { motion } from 'framer-motion';
import { HeartIcon, ServerIcon, WifiIcon } from '@heroicons/react/24/outline';

export default function SystemHealth({ agentData, coherenceData }) {
  if (!agentData) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gray-800/50 backdrop-blur-xl rounded-lg border border-gray-600 p-6"
      >
        <div className="animate-pulse">
          <div className="h-6 bg-gray-700 rounded mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-700 rounded"></div>
            <div className="h-4 bg-gray-700 rounded"></div>
            <div className="h-4 bg-gray-700 rounded"></div>
          </div>
        </div>
      </motion.div>
    );
  }

  // Calculate system health metrics
  const coherenceLevel = agentData.coherence;
  const isHealthy = coherenceLevel >= 0.7;
  const uptime = agentData.uptime;
  const apiStatus = 'operational'; // Since we're getting data
  
  const healthMetrics = [
    {
      name: 'API Server',
      status: apiStatus,
      value: '8080',
      icon: <ServerIcon className="w-5 h-5" />,
      color: apiStatus === 'operational' ? 'text-green-400' : 'text-red-400'
    },
    {
      name: 'Coherence Engine',
      status: isHealthy ? 'optimal' : 'degraded',
      value: `${(coherenceLevel * 100).toFixed(1)}%`,
      icon: <HeartIcon className="w-5 h-5" />,
      color: isHealthy ? 'text-green-400' : 'text-yellow-400'
    },
    {
      name: 'Network',
      status: 'connected',
      value: 'localhost',
      icon: <WifiIcon className="w-5 h-5" />,
      color: 'text-green-400'
    }
  ];

  const getStatusBadge = (status) => {
    const colors = {
      operational: 'bg-green-500/20 text-green-400',
      optimal: 'bg-green-500/20 text-green-400',
      degraded: 'bg-yellow-500/20 text-yellow-400',
      connected: 'bg-green-500/20 text-green-400',
      error: 'bg-red-500/20 text-red-400'
    };
    
    return colors[status] || 'bg-gray-500/20 text-gray-400';
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-gray-800/50 backdrop-blur-xl rounded-lg border border-gray-600 p-6"
    >
      {/* Header */}
      <div className="flex items-center space-x-3 mb-6">
        <HeartIcon className="w-6 h-6 text-nova-400" />
        <div>
          <h3 className="text-lg font-semibold text-white">System Health</h3>
          <p className="text-sm text-gray-400">Core Components</p>
        </div>
      </div>

      {/* Health Metrics */}
      <div className="space-y-4">
        {healthMetrics.map((metric, index) => (
          <motion.div
            key={metric.name}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: index * 0.1 }}
            className="flex items-center justify-between p-3 bg-gray-700/30 rounded-lg"
          >
            <div className="flex items-center space-x-3">
              <div className={metric.color}>
                {metric.icon}
              </div>
              <div>
                <p className="text-sm font-medium text-white">{metric.name}</p>
                <p className="text-xs text-gray-400">{metric.value}</p>
              </div>
            </div>
            
            <span className={`px-2 py-1 rounded text-xs font-medium ${getStatusBadge(metric.status)}`}>
              {metric.status.toUpperCase()}
            </span>
          </motion.div>
        ))}
      </div>

      {/* System Summary */}
      <div className="mt-6 pt-4 border-t border-gray-700">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <p className="text-xs text-gray-400">System Uptime</p>
            <p className="text-sm font-medium text-white">{uptime}</p>
          </div>
          <div>
            <p className="text-xs text-gray-400">Overall Health</p>
            <p className={`text-sm font-medium ${isHealthy ? 'text-green-400' : 'text-yellow-400'}`}>
              {isHealthy ? 'Healthy' : 'Degraded'}
            </p>
          </div>
        </div>
      </div>

      {/* Performance Indicators */}
      <div className="mt-4 p-3 bg-gradient-to-r from-nova-900/20 to-coherence-900/20 border border-nova-500/30 rounded-lg">
        <div className="flex items-center justify-between">
          <div className="text-sm">
            <span className="text-nova-400 font-medium">Performance</span>
            <span className="text-gray-400"> Status</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            <span className="text-green-400 text-sm font-medium">Optimal</span>
          </div>
        </div>
      </div>
    </motion.div>
  );
}
