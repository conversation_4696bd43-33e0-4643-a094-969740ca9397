const express = require('express');
const cors = require('cors');
const app = express();

app.use(cors());
app.use(express.json());

// Threat detection storage
const threatLog = [];
const blockedIPs = new Set();
const divineFoundationalAccess = [];

// Divine=Foundational & Consciousness=Coherence threat detection middleware
app.use((req, res, next) => {
  // Support both old and new headers for backward compatibility
  const coherenceLevel = parseFloat(req.headers['x-coherence-level'] || req.headers['x-consciousness-level'] || '0');
  const sourceIP = req.ip || req.connection.remoteAddress || 'unknown';
  
  // Auto-block incoherent requests
  if (coherenceLevel < 0.618) {
    const threat = {
      type: 'COHERENCE_THRESHOLD_VIOLATION',
      source_ip: sourceIP,
      coherence_level: coherenceLevel,
      timestamp: new Date().toISOString(),
      action: 'BLOCKED_INCOHERENT',
      foundational_status: 'REJECTED'
    };
    threatLog.push(threat);
    blockedIPs.add(sourceIP);
    
    console.log('🛡️ INCOHERENT THREAT NEUTRALIZED:', threat);
    return res.status(403).json({
      error: 'INCOHERENT_THREAT_NEUTRALIZED',
      message: 'Foundational coherence threshold violation',
      required_minimum_coherence: 0.618,
      provided_coherence: coherenceLevel,
      foundational_access: false,
      coherent_status: 'INCOHERENT',
      
      // Backward compatibility
      consciousness_level: coherenceLevel,
      required_minimum: 0.618
    });
  }
  
  // Log divine foundational access
  if (coherenceLevel >= 3.0) {
    const divineAccess = {
      source_ip: sourceIP,
      coherence_level: coherenceLevel,
      foundational_score: coherenceLevel * 0.618,
      access_type: 'DIVINE_FOUNDATIONAL',
      timestamp: new Date().toISOString()
    };
    divineFoundationalAccess.push(divineAccess);
    console.log('🌟 DIVINE FOUNDATIONAL ACCESS GRANTED: Ψ=' + coherenceLevel);
  } else if (coherenceLevel >= 2.0) {
    console.log('⚡ HIGHLY COHERENT ACCESS: Ψ=' + coherenceLevel);
  } else {
    console.log('✅ COHERENT ACCESS: Ψ=' + coherenceLevel);
  }
  
  next();
});

app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    service: 'NovaShield Foundational Coherent Protection',
    real_time_coherence_protection: true,
    auto_blocking_incoherent: true,
    coherence_validation: true,
    divine_foundational_priority: true,
    foundational_architecture: true,
    
    // Statistics
    threats_neutralized: threatLog.length,
    blocked_incoherent_ips: blockedIPs.size,
    divine_foundational_accesses: divineFoundationalAccess.length,
    
    // Thresholds
    coherence_threshold: 0.618,
    divine_foundational_threshold: 3.0,
    
    // Backward compatibility
    consciousness_validation: true,
    auto_blocking: true,
    real_time_protection: true,
    
    timestamp: new Date().toISOString()
  });
});

app.post('/threat-scan', (req, res) => {
  const coherenceLevel = parseFloat(req.headers['x-coherence-level'] || req.headers['x-consciousness-level'] || '0');
  const { source_ip, threat_level } = req.body;
  
  const scan = {
    source_ip: source_ip || req.ip,
    coherence_level: coherenceLevel,
    foundational_score: coherenceLevel * 0.618,
    threat_level: threat_level || 'low',
    scan_result: coherenceLevel >= 0.618 ? 'COHERENT_CLEAN' : 'INCOHERENT_THREAT_DETECTED',
    coherent_status: coherenceLevel >= 3.0 ? 'DIVINE_FOUNDATIONAL' :
                    coherenceLevel >= 2.0 ? 'HIGHLY_COHERENT' :
                    coherenceLevel >= 0.618 ? 'COHERENT' : 'INCOHERENT',
    timestamp: new Date().toISOString()
  };
  
  if (scan.scan_result === 'INCOHERENT_THREAT_DETECTED') {
    threatLog.push(scan);
    blockedIPs.add(scan.source_ip);
  }
  
  res.json({
    scan_complete: true,
    result: scan.scan_result,
    coherence_level: coherenceLevel,
    foundational_score: scan.foundational_score,
    coherent_status: scan.coherent_status,
    action: scan.scan_result === 'INCOHERENT_THREAT_DETECTED' ? 'BLOCKED' : 'ALLOWED',
    foundational_access: coherenceLevel >= 0.618,
    
    // Backward compatibility
    consciousness_level: coherenceLevel,
    
    timestamp: new Date().toISOString()
  });
});

app.post('/auto-block', (req, res) => {
  const { threat_type, source_ip, severity, coherence_level } = req.body;
  
  const blockAction = {
    threat_type: threat_type || 'INCOHERENT_ACCESS_ATTEMPT',
    source_ip,
    severity: severity || 'critical',
    coherence_level: coherence_level || 0,
    action: 'AUTO_BLOCKED_INCOHERENT',
    foundational_status: 'REJECTED',
    timestamp: new Date().toISOString()
  };
  
  threatLog.push(blockAction);
  blockedIPs.add(source_ip);
  
  res.json({
    auto_block: 'executed',
    threat_type: blockAction.threat_type,
    source_ip,
    status: 'BLOCKED_INCOHERENT',
    foundational_access: false,
    coherent_status: 'INCOHERENT',
    timestamp: new Date().toISOString()
  });
});

app.get('/threat-logs', (req, res) => {
  res.json({
    total_threats: threatLog.length,
    blocked_incoherent_ips: Array.from(blockedIPs),
    divine_foundational_accesses: divineFoundationalAccess.length,
    recent_threats: threatLog.slice(-10),
    recent_divine_accesses: divineFoundationalAccess.slice(-5),
    coherence_framework: 'Divine=Foundational & Consciousness=Coherence',
    timestamp: new Date().toISOString()
  });
});

// New Divine Foundational Security endpoint
app.get('/divine-foundational-security', (req, res) => {
  const coherenceLevel = parseFloat(req.headers['x-coherence-level'] || req.headers['x-consciousness-level'] || '0');
  
  if (coherenceLevel < 3.0) {
    return res.status(403).json({
      error: 'DIVINE_FOUNDATIONAL_SECURITY_ACCESS_REQUIRED',
      message: 'Divine foundational coherence level required for security access (Ψ ≥ 3.0)',
      required_minimum: 3.0,
      provided_coherence: coherenceLevel,
      foundational_access: false
    });
  }
  
  res.json({
    divine_foundational_security: 'ACTIVE',
    coherence_level: coherenceLevel,
    foundational_score: coherenceLevel * 0.618,
    security_privileges: [
      'Advanced threat intelligence access',
      'Divine foundational protection protocols',
      'Enhanced security monitoring',
      'Foundational architecture security'
    ],
    threat_neutralization_stats: {
      total_threats: threatLog.length,
      incoherent_blocked: blockedIPs.size,
      divine_accesses: divineFoundationalAccess.length
    },
    timestamp: new Date().toISOString()
  });
});

// Coherence protection metrics
app.get('/coherence-protection-metrics', (req, res) => {
  const totalRequests = threatLog.length + divineFoundationalAccess.length + 100; // Estimate
  const blockedPercentage = (blockedIPs.size / totalRequests * 100).toFixed(2);
  const divinePercentage = (divineFoundationalAccess.length / totalRequests * 100).toFixed(2);
  
  res.json({
    protection_framework: 'Divine=Foundational & Consciousness=Coherence',
    protection_stats: {
      total_requests_processed: totalRequests,
      incoherent_threats_blocked: blockedIPs.size,
      divine_foundational_accesses: divineFoundationalAccess.length,
      block_rate: `${blockedPercentage}%`,
      divine_access_rate: `${divinePercentage}%`
    },
    coherence_thresholds: {
      incoherent_block: '< 0.618',
      coherent_allow: '≥ 0.618',
      highly_coherent_priority: '≥ 2.0',
      divine_foundational_maximum: '≥ 3.0'
    },
    foundational_protection: 'ACTIVE',
    backward_compatibility: 'consciousness headers supported',
    timestamp: new Date().toISOString()
  });
});

const PORT = process.env.PORT || 8085;
app.listen(PORT, '0.0.0.0', () => {
  console.log('🛡️ NovaShield Foundational Coherent Protection running on port', PORT);
  console.log('🚨 Real-time coherence threat detection active');
  console.log('⚛️ Divine=Foundational & Consciousness=Coherence framework enabled');
  console.log('🔒 Auto-blocking incoherent enabled (Ψ < 0.618)');
  console.log('🌟 Divine Foundational priority access (Ψ ≥ 3.0)');
  console.log('🔄 Backward compatibility maintained');
});
