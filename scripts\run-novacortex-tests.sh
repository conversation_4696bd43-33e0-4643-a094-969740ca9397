#!/bin/bash

# Exit on error
set -e

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${GREEN}Starting NovaCortex test environment...${NC}"

# Build and start the test environment
docker-compose -f docker-compose.novacortex-test.yml up -d --build

# Wait for services to be ready
echo -e "${GREEN}Waiting for services to be ready...${NC}"
./scripts/wait-for-it.sh novacortex:3010 --timeout=60 -- echo "NovaCortex is up!"

# Run tests
echo -e "${GREEN}Running NovaCortex tests...${NC}"
TEST_RESULT=$(docker-compose -f docker-compose.novacortex-test.yml run --rm test-runner)

# Check test results
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ All tests passed!${NC}
"
    echo "Test Results:"
    echo "------------"
    echo "$TEST_RESULT" | tail -n 20  # Show last 20 lines of test output
    
    # Generate Allure report if tests passed
    echo -e "\n${GREEN}Generating Allure report...${NC}"
    docker-compose -f docker-compose.novacortex-test.yml run --rm allure generate /app/reports/allure-results --clean -o /app/reports/allure-report
    
    echo -e "\n${GREEN}Test Report:${NC}"
    echo "- HTML Report: file://$(pwd)/reports/allure-report/index.html"
    echo "- Monitoring: http://localhost:3000 (Grafana)"
    echo "- Metrics: http://localhost:9090 (Prometheus)"
    echo -e "\n${GREEN}✅ Test execution completed successfully!${NC}"
else
    echo -e "${RED}❌ Some tests failed. Showing test output:${NC}"
    echo "$TEST_RESULT"
    
    # Show logs from failed services
    echo -e "\n${RED}Service logs:${NC}"
    docker-compose -f docker-compose.novacortex-test.yml logs novacortex
    
    # Exit with error code
    exit 1
fi
