/**
 * Branding Routes
 */

const express = require('express');
const router = express.Router();
const multer = require('multer');
const BrandingController = require('../controllers/BrandingController');
const { authenticate, hasPermission, optionalAuth } = require('../middleware/authMiddleware');

// Configure multer for memory storage
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB limit
  }
});

// Public routes for assets
router.get('/assets/:fileName', (req, res, next) => {
  BrandingController.getBrandingAsset(req, res, next);
});

// Routes with optional authentication for branding package
router.use(optionalAuth);

// Get branding package
router.get('/package/:organizationId', (req, res, next) => {
  BrandingController.getBrandingPackage(req, res, next);
});

// All other routes require authentication
router.use(authenticate);

// Get organization branding
router.get('/organization/:organizationId', (req, res, next) => {
  BrandingController.getOrganizationBranding(req, res, next);
});

// Routes that require system settings permission
router.use(hasPermission('system:settings'));

// Update organization branding
router.put('/organization/:organizationId', (req, res, next) => {
  BrandingController.updateOrganizationBranding(req, res, next);
});

// Reset organization branding to default
router.delete('/organization/:organizationId', (req, res, next) => {
  BrandingController.resetOrganizationBranding(req, res, next);
});

// Upload branding asset
router.post('/organization/:organizationId/assets', upload.single('file'), (req, res, next) => {
  BrandingController.uploadBrandingAsset(req, res, next);
});

// Delete branding asset
router.delete('/organization/:organizationId/assets/:fileName', (req, res, next) => {
  BrandingController.deleteBrandingAsset(req, res, next);
});

module.exports = router;
