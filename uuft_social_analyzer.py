#!/usr/bin/env python3
"""
UUFT Social Systems Analyzer

This module analyzes social networks and diffusion processes for 18/82 patterns
and π-related relationships in their structure and dynamics.
"""

import numpy as np
import matplotlib.pyplot as plt
import networkx as nx
import os
import logging
import json
from collections import defaultdict

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('uuft_social.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('UUFT_Social')

# Constants
PATTERN_1882_RATIO = 18 / 82
PI = np.pi
PI_10_CUBED = PI * 10**3
RESULTS_DIR = "uuft_results/social"
os.makedirs(RESULTS_DIR, exist_ok=True)

class UUFTSocialNetwork:
    """Generator for social networks with configurable UUFT properties."""
    
    def __init__(self, num_nodes=1000, network_type="small_world", uuft_bias=0.0):
        """
        Initialize a synthetic social network.
        
        Args:
            num_nodes: Number of nodes (individuals) in the network
            network_type: Type of network ("random", "small_world", "scale_free", "1882_optimized")
            uuft_bias: Bias parameter for introducing 18/82 patterns (0.0 = none, 1.0 = maximum)
        """
        self.num_nodes = num_nodes
        self.network_type = network_type
        self.uuft_bias = uuft_bias
        self.graph = self._generate_network()
        self.node_attributes = self._assign_attributes()
        
        logger.info(f"Created {network_type} network with {num_nodes} nodes and UUFT bias {uuft_bias}")
    
    def _generate_network(self):
        """Generate the network structure based on specified type."""
        if self.network_type == "random":
            # Erdős–Rényi random graph
            p = 4 / self.num_nodes  # Average degree of 4
            graph = nx.erdos_renyi_graph(self.num_nodes, p)
            
        elif self.network_type == "small_world":
            # Watts-Strogatz small-world network
            k = 4  # Each node is connected to k nearest neighbors
            p = 0.1  # Probability of rewiring each edge
            graph = nx.watts_strogatz_graph(self.num_nodes, k, p)
            
        elif self.network_type == "scale_free":
            # Barabási–Albert scale-free network
            m = 2  # Number of edges to attach from a new node to existing nodes
            graph = nx.barabasi_albert_graph(self.num_nodes, m)
            
        elif self.network_type == "1882_optimized":
            # Network with explicit 18/82 structure
            graph = self._generate_1882_network()
        
        else:
            raise ValueError(f"Unknown network type: {self.network_type}")
        
        # Apply UUFT bias if specified
        if self.uuft_bias > 0:
            graph = self._apply_uuft_bias(graph)
        
        return graph
    
    def _generate_1882_network(self):
        """
        Generate network with explicit 18/82 structure.
        
        This creates a network where 18% of nodes have 82% of connections,
        following the UUFT pattern.
        """
        graph = nx.Graph()
        
        # Add all nodes
        graph.add_nodes_from(range(self.num_nodes))
        
        # Determine high-influence nodes (18% of total)
        high_influence_count = int(self.num_nodes * 0.18)
        high_influence_nodes = list(range(high_influence_count))
        
        # Determine total number of edges to maintain average degree of 4
        total_edges = self.num_nodes * 2  # Average degree of 4 means total degree of 4*N
        
        # 82% of edges should connect to high-influence nodes
        high_influence_edges = int(total_edges * 0.82)
        
        # Connect high-influence nodes to each other (fully connected)
        for i in range(high_influence_count):
            for j in range(i+1, high_influence_count):
                graph.add_edge(i, j)
        
        # Count edges used so far
        edges_used = graph.number_of_edges()
        
        # Connect remaining high-influence edges
        remaining_high_edges = high_influence_edges - edges_used
        
        # Connect high-influence nodes to random regular nodes
        regular_nodes = list(range(high_influence_count, self.num_nodes))
        
        edge_count = 0
        while edge_count < remaining_high_edges and regular_nodes:
            # Select a random high-influence node
            high_node = np.random.choice(high_influence_nodes)
            
            # Select a random regular node
            regular_node = np.random.choice(regular_nodes)
            
            # Add edge if it doesn't already exist
            if not graph.has_edge(high_node, regular_node):
                graph.add_edge(high_node, regular_node)
                edge_count += 1
        
        # Connect remaining regular nodes to maintain connectivity
        remaining_edges = total_edges - graph.number_of_edges()
        
        edge_count = 0
        attempts = 0
        while edge_count < remaining_edges and attempts < remaining_edges * 10:
            # Select two random regular nodes
            node1 = np.random.choice(regular_nodes)
            node2 = np.random.choice(regular_nodes)
            
            # Add edge if it doesn't already exist and nodes are different
            if node1 != node2 and not graph.has_edge(node1, node2):
                graph.add_edge(node1, node2)
                edge_count += 1
            
            attempts += 1
        
        return graph
    
    def _apply_uuft_bias(self, graph):
        """
        Apply UUFT bias to the network structure.
        
        This modifies the network to more closely follow 18/82 patterns
        based on the uuft_bias parameter.
        """
        if self.uuft_bias <= 0:
            return graph
        
        # Create a copy of the graph to modify
        modified_graph = graph.copy()
        
        # Calculate current degree distribution
        degrees = dict(modified_graph.degree())
        total_degree = sum(degrees.values())
        
        # Sort nodes by degree
        sorted_nodes = sorted(degrees.keys(), key=lambda x: degrees[x], reverse=True)
        
        # Calculate current top 18% node degree sum
        top_18_percent = int(self.num_nodes * 0.18)
        top_nodes = sorted_nodes[:top_18_percent]
        top_degree_sum = sum(degrees[node] for node in top_nodes)
        
        # Calculate current ratio
        current_ratio = top_degree_sum / total_degree
        
        # Target ratio based on UUFT bias
        target_ratio = 0.5 + (0.82 - 0.5) * self.uuft_bias  # Interpolate between 0.5 (neutral) and 0.82 (full UUFT)
        
        # If current ratio is already close to target, no need to modify
        if abs(current_ratio - target_ratio) < 0.05:
            return modified_graph
        
        # Determine how many edges to rewire
        edges_to_rewire = int(modified_graph.number_of_edges() * self.uuft_bias * 0.2)
        
        # Rewire edges to achieve target ratio
        if current_ratio < target_ratio:
            # Need to increase connections to top nodes
            for _ in range(edges_to_rewire):
                # Find a random edge between non-top nodes
                valid_edges = [(u, v) for u, v in modified_graph.edges() if u not in top_nodes and v not in top_nodes]
                
                if not valid_edges:
                    break
                
                # Select a random edge to remove
                u, v = valid_edges[np.random.randint(len(valid_edges))]
                modified_graph.remove_edge(u, v)
                
                # Add an edge from a top node to a non-top node
                top_node = np.random.choice(top_nodes)
                non_top_nodes = [n for n in modified_graph.nodes() if n not in top_nodes]
                
                # Find a non-top node that's not already connected to this top node
                valid_targets = [n for n in non_top_nodes if not modified_graph.has_edge(top_node, n)]
                
                if valid_targets:
                    target = np.random.choice(valid_targets)
                    modified_graph.add_edge(top_node, target)
        else:
            # Need to decrease connections to top nodes
            for _ in range(edges_to_rewire):
                # Find a random edge between a top node and any other node
                valid_edges = [(u, v) for u, v in modified_graph.edges() 
                              if (u in top_nodes and v not in top_nodes) or 
                                 (v in top_nodes and u not in top_nodes)]
                
                if not valid_edges:
                    break
                
                # Select a random edge to remove
                u, v = valid_edges[np.random.randint(len(valid_edges))]
                modified_graph.remove_edge(u, v)
                
                # Add an edge between non-top nodes
                non_top_nodes = [n for n in modified_graph.nodes() if n not in top_nodes]
                
                # Find two non-top nodes that aren't already connected
                attempts = 0
                while attempts < 100:
                    node1 = np.random.choice(non_top_nodes)
                    node2 = np.random.choice(non_top_nodes)
                    
                    if node1 != node2 and not modified_graph.has_edge(node1, node2):
                        modified_graph.add_edge(node1, node2)
                        break
                    
                    attempts += 1
        
        return modified_graph
    
    def _assign_attributes(self):
        """
        Assign attributes to nodes based on UUFT principles.
        
        This includes:
        - Influence level (following 18/82 distribution)
        - Adoption threshold (with π-related patterns)
        - Social categories (with 18/82 group sizes)
        """
        attributes = {}
        
        # Calculate node degrees
        degrees = dict(self.graph.degree())
        
        # Sort nodes by degree
        sorted_nodes = sorted(degrees.keys(), key=lambda x: degrees[x], reverse=True)
        
        # Assign influence levels based on degree ranking
        for i, node in enumerate(sorted_nodes):
            # High influence for top 18%
            if i < int(self.num_nodes * 0.18):
                influence = np.random.uniform(0.7, 1.0)
                category = "influencer"
            else:
                influence = np.random.uniform(0.1, 0.7)
                category = "regular"
            
            # Assign adoption thresholds with π-related patterns
            if np.random.random() < 0.1:  # 10% of nodes have π-related thresholds
                threshold = PI / 10  # Around 0.314
            else:
                # Regular nodes have higher thresholds (harder to convince)
                if category == "regular":
                    threshold = np.random.uniform(0.3, 0.8)
                else:
                    threshold = np.random.uniform(0.1, 0.5)
            
            attributes[node] = {
                "influence": influence,
                "threshold": threshold,
                "category": category
            }
        
        return attributes
    
    def get_network_stats(self):
        """Get statistics about the network structure."""
        # Calculate basic network statistics
        stats = {
            "num_nodes": self.graph.number_of_nodes(),
            "num_edges": self.graph.number_of_edges(),
            "average_degree": np.mean([d for _, d in self.graph.degree()]),
            "density": nx.density(self.graph),
            "clustering_coefficient": nx.average_clustering(self.graph),
            "connected_components": nx.number_connected_components(self.graph)
        }
        
        # Calculate degree distribution
        degrees = [d for _, d in self.graph.degree()]
        stats["degree_distribution"] = {
            "min": min(degrees),
            "max": max(degrees),
            "mean": np.mean(degrees),
            "median": np.median(degrees),
            "std": np.std(degrees)
        }
        
        # Calculate 18/82 metrics
        sorted_degrees = sorted(degrees, reverse=True)
        top_18_percent = int(len(sorted_degrees) * 0.18)
        top_degrees = sorted_degrees[:top_18_percent]
        bottom_degrees = sorted_degrees[top_18_percent:]
        
        top_sum = sum(top_degrees)
        bottom_sum = sum(bottom_degrees)
        total_sum = top_sum + bottom_sum
        
        if total_sum > 0:
            top_ratio = top_sum / total_sum
            stats["top_18_percent_degree_ratio"] = top_ratio
            stats["proximity_to_1882"] = abs(top_ratio - 0.82) / 0.82 * 100
            stats["is_1882_pattern"] = abs(top_ratio - 0.82) / 0.82 < 0.1  # Within 10% of 0.82
        
        return stats
    
    def visualize_network(self, highlight_1882=True, save_path=None):
        """
        Visualize the network with optional 18/82 pattern highlighting.
        
        Args:
            highlight_1882: Whether to highlight nodes following 18/82 pattern
            save_path: Path to save the visualization
        """
        plt.figure(figsize=(12, 12))
        
        # Calculate node sizes based on degree
        degrees = dict(self.graph.degree())
        node_sizes = [50 + 10 * degrees[node] for node in self.graph.nodes()]
        
        # Determine node colors
        if highlight_1882:
            # Sort nodes by degree
            sorted_nodes = sorted(degrees.keys(), key=lambda x: degrees[x], reverse=True)
            
            # Top 18% nodes are highlighted
            top_18_percent = int(self.num_nodes * 0.18)
            top_nodes = set(sorted_nodes[:top_18_percent])
            
            # Create color map
            node_colors = ['red' if node in top_nodes else 'blue' for node in self.graph.nodes()]
        else:
            node_colors = 'blue'
        
        # Create layout
        if self.num_nodes > 500:
            # For large networks, use faster layout
            pos = nx.spring_layout(self.graph, iterations=50)
        else:
            # For smaller networks, use more accurate layout
            pos = nx.spring_layout(self.graph, iterations=100)
        
        # Draw the network
        nx.draw_networkx_nodes(self.graph, pos, node_size=node_sizes, node_color=node_colors, alpha=0.7)
        nx.draw_networkx_edges(self.graph, pos, alpha=0.2)
        
        # Add title
        stats = self.get_network_stats()
        plt.title(f"{self.network_type.capitalize()} Network (N={self.num_nodes})\n" +
                 f"18/82 Proximity: {stats.get('proximity_to_1882', 'N/A'):.2f}%")
        
        # Remove axis
        plt.axis('off')
        
        # Save if path provided
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Network visualization saved to {save_path}")
        
        plt.close()
        
    def to_dict(self):
        """Convert network to dictionary for serialization."""
        return {
            "num_nodes": self.num_nodes,
            "network_type": self.network_type,
            "uuft_bias": self.uuft_bias,
            "edges": list(self.graph.edges()),
            "node_attributes": self.node_attributes
        }
    
    @classmethod
    def from_dict(cls, data):
        """Create network from dictionary."""
        network = cls(
            num_nodes=data["num_nodes"],
            network_type=data["network_type"],
            uuft_bias=data["uuft_bias"]
        )
        
        # Replace generated graph with saved graph
        network.graph = nx.Graph()
        network.graph.add_nodes_from(range(data["num_nodes"]))
        network.graph.add_edges_from(data["edges"])
        
        # Replace generated attributes with saved attributes
        network.node_attributes = data["node_attributes"]
        
        return network
