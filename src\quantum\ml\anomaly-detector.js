/**
 * Anomaly Detector
 *
 * This module provides machine learning capabilities for anomaly detection
 * in the Finite Universe Principle defense system. It uses statistical
 * methods to detect anomalies in domain-specific data.
 */

const EventEmitter = require('events');

/**
 * AnomalyDetector class
 * 
 * Provides machine learning capabilities for anomaly detection.
 */
class AnomalyDetector extends EventEmitter {
  /**
   * Constructor
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();

    this.options = {
      enableLogging: true,
      historyLength: 100, // Number of data points to keep in history
      anomalyThreshold: 3.0, // Number of standard deviations for anomaly detection
      learningRate: 0.1, // Learning rate for updating statistics
      ...options
    };

    // Initialize data history
    this.history = {
      cyber: [],
      financial: [],
      medical: []
    };

    // Initialize statistics
    this.statistics = {
      cyber: this._createEmptyStatistics(),
      financial: this._createEmptyStatistics(),
      medical: this._createEmptyStatistics()
    };

    // Initialize anomaly counters
    this.anomalies = {
      cyber: 0,
      financial: 0,
      medical: 0,
      total: 0
    };

    if (this.options.enableLogging) {
      console.log('AnomalyDetector initialized with options:', this.options);
    }
  }

  /**
   * Process data for anomaly detection
   * @param {Object} data - Data to process
   * @param {string} domain - Domain of the data
   * @returns {Object} - Anomaly detection results
   */
  processData(data, domain = 'cyber') {
    // Add data to history
    this._addToHistory(data, domain);

    // Update statistics
    this._updateStatistics(data, domain);

    // Detect anomalies
    const anomalies = this._detectAnomalies(data, domain);

    // Emit events for detected anomalies
    if (anomalies.length > 0) {
      this.anomalies[domain]++;
      this.anomalies.total++;

      this.emit('anomalies-detected', {
        domain,
        data,
        anomalies,
        count: anomalies.length
      });

      if (this.options.enableLogging) {
        console.log(`Anomalies detected in ${domain} domain:`, anomalies);
      }
    }

    return {
      domain,
      anomalies,
      isAnomaly: anomalies.length > 0,
      statistics: this.statistics[domain]
    };
  }

  /**
   * Add data to history
   * @param {Object} data - Data to add
   * @param {string} domain - Domain of the data
   * @private
   */
  _addToHistory(data, domain) {
    // Add data to history
    this.history[domain].push({
      timestamp: new Date(),
      data: { ...data }
    });

    // Trim history if needed
    if (this.history[domain].length > this.options.historyLength) {
      this.history[domain].shift();
    }
  }

  /**
   * Update statistics
   * @param {Object} data - Data to update statistics with
   * @param {string} domain - Domain of the data
   * @private
   */
  _updateStatistics(data, domain) {
    const stats = this.statistics[domain];
    const lr = this.options.learningRate;

    // Update statistics for each field
    for (const field in data) {
      const value = data[field];

      // Skip non-numeric values
      if (typeof value !== 'number' || !Number.isFinite(value)) {
        continue;
      }

      // Initialize statistics for new fields
      if (!stats.mean[field]) {
        stats.mean[field] = value;
        stats.variance[field] = 0;
        stats.min[field] = value;
        stats.max[field] = value;
        continue;
      }

      // Update min and max
      stats.min[field] = Math.min(stats.min[field], value);
      stats.max[field] = Math.max(stats.max[field], value);

      // Update mean and variance using online algorithm
      const oldMean = stats.mean[field];
      stats.mean[field] = oldMean + lr * (value - oldMean);
      stats.variance[field] = (1 - lr) * (stats.variance[field] + lr * Math.pow(value - oldMean, 2));
    }
  }

  /**
   * Detect anomalies in data
   * @param {Object} data - Data to detect anomalies in
   * @param {string} domain - Domain of the data
   * @returns {Array} - Detected anomalies
   * @private
   */
  _detectAnomalies(data, domain) {
    const stats = this.statistics[domain];
    const threshold = this.options.anomalyThreshold;
    const anomalies = [];

    // Check each field for anomalies
    for (const field in data) {
      const value = data[field];

      // Skip non-numeric values
      if (typeof value !== 'number' || !Number.isFinite(value)) {
        continue;
      }

      // Skip fields with insufficient history
      if (!stats.mean[field] || stats.variance[field] === 0) {
        continue;
      }

      // Calculate z-score
      const stdDev = Math.sqrt(stats.variance[field]);
      const zScore = Math.abs((value - stats.mean[field]) / stdDev);

      // Check if value is an anomaly
      if (zScore > threshold) {
        anomalies.push({
          field,
          value,
          zScore,
          mean: stats.mean[field],
          stdDev,
          threshold
        });
      }
    }

    return anomalies;
  }

  /**
   * Create empty statistics object
   * @returns {Object} - Empty statistics object
   * @private
   */
  _createEmptyStatistics() {
    return {
      mean: {},
      variance: {},
      min: {},
      max: {}
    };
  }

  /**
   * Get anomaly statistics
   * @returns {Object} - Anomaly statistics
   */
  getAnomalyStats() {
    return { ...this.anomalies };
  }

  /**
   * Get domain statistics
   * @param {string} domain - Domain to get statistics for
   * @returns {Object} - Domain statistics
   */
  getDomainStatistics(domain) {
    return { ...this.statistics[domain] };
  }

  /**
   * Reset statistics and history
   */
  reset() {
    // Reset history
    this.history = {
      cyber: [],
      financial: [],
      medical: []
    };

    // Reset statistics
    this.statistics = {
      cyber: this._createEmptyStatistics(),
      financial: this._createEmptyStatistics(),
      medical: this._createEmptyStatistics()
    };

    // Reset anomaly counters
    this.anomalies = {
      cyber: 0,
      financial: 0,
      medical: 0,
      total: 0
    };

    this.emit('reset');

    if (this.options.enableLogging) {
      console.log('AnomalyDetector reset');
    }
  }
}

/**
 * Create an anomaly detector with recommended settings
 * @param {Object} options - Configuration options
 * @returns {AnomalyDetector} - Configured anomaly detector
 */
function createAnomalyDetector(options = {}) {
  return new AnomalyDetector({
    enableLogging: true,
    historyLength: 100,
    anomalyThreshold: 3.0,
    learningRate: 0.1,
    ...options
  });
}

module.exports = {
  AnomalyDetector,
  createAnomalyDetector
};
