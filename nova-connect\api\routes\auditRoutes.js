/**
 * Audit Routes
 */

const express = require('express');
const router = express.Router();
const AuditController = require('../controllers/AuditController');
const { authenticate, hasPermission } = require('../middleware/authMiddleware');

// All routes require authentication
router.use(authenticate);

// Get audit logs (admin only)
router.get('/', hasPermission('system:audit'), (req, res, next) => {
  AuditController.getAuditLogs(req, res, next);
});

// Get audit log by ID (admin only)
router.get('/:id', hasPermission('system:audit'), (req, res, next) => {
  AuditController.getAuditLogById(req, res, next);
});

// Get audit logs for a resource (admin only)
router.get('/resource/:resourceType/:resourceId', hasPermission('system:audit'), (req, res, next) => {
  AuditController.getAuditLogsForResource(req, res, next);
});

// Get audit logs for a user (admin only)
router.get('/user/:id', hasPermission('system:audit'), (req, res, next) => {
  AuditController.getAuditLogsForUser(req, res, next);
});

// Get my audit logs
router.get('/user/me', (req, res, next) => {
  AuditController.getMyAuditLogs(req, res, next);
});

// Get audit logs for a team (team admins and owners)
router.get('/team/:id', (req, res, next) => {
  AuditController.getAuditLogsForTeam(req, res, next);
});

module.exports = router;
