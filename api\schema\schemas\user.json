{"entity": "User", "entityPlural": "Users", "apiEndpoint": "/api/v1/users", "fields": [{"name": "email", "type": "string", "required": true, "label": "Email Address", "placeholder": "Enter email address"}, {"name": "password", "type": "password", "required": true, "label": "Password", "placeholder": "Enter password"}, {"name": "firstName", "type": "string", "required": true, "label": "First Name", "placeholder": "Enter first name"}, {"name": "lastName", "type": "string", "required": true, "label": "Last Name", "placeholder": "Enter last name"}, {"name": "role", "type": "dropdown", "options": ["admin", "user", "auditor"], "required": true, "label": "Role"}, {"name": "status", "type": "dropdown", "options": ["active", "inactive", "pending"], "required": true, "label": "Status"}, {"name": "phone", "type": "string", "required": false, "label": "Phone Number", "placeholder": "Enter phone number"}, {"name": "department", "type": "string", "required": false, "label": "Department", "placeholder": "Enter department"}]}