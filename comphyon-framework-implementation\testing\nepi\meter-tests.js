/**
 * Comphyon Meter Testing
 * 
 * This module contains tests for the Comphyon Meter component.
 * It verifies the accuracy and reliability of universal entropy and coherence measurement.
 */

const { NEPITestSuite, nepiAssertions } = require('./nepi-test-framework');
const { assertions } = require('../test-framework');
const { EventEmitter } = require('events');

/**
 * Mock UniversalEntropyMeasurement class for testing
 */
class MockUniversalEntropyMeasurement extends EventEmitter {
  constructor(options = {}) {
    super();
    
    this.options = {
      enableLogging: false,
      enableMetrics: true,
      ...options
    };
    
    this.state = {
      isRunning: false,
      universalEntropy: 0.5,
      domainEntropy: {
        cyber: {
          overallEntropy: 0.5,
          policyEntropy: 0.5,
          auditEntropy: 0.5,
          regulatoryEntropy: 0.5
        },
        financial: {
          overallEntropy: 0.5,
          transactionEntropy: 0.5,
          attackSurfaceCoherence: 0.5,
          marketStress: 0.5
        },
        biological: {
          overallEntropy: 0.5,
          telomereLength: 0.5,
          mtorActivation: 0.5,
          inflammationLevel: 0.5
        }
      },
      lastUpdateTime: Date.now()
    };
    
    this.metrics = {
      processingTimeMs: 0,
      entropyUpdates: 0,
      thresholdViolations: 0
    };
  }
  
  start() {
    this.state.isRunning = true;
    return true;
  }
  
  stop() {
    this.state.isRunning = false;
    return true;
  }
  
  getUniversalEntropy() {
    return this.state.universalEntropy;
  }
  
  getDomainEntropy(domain) {
    return this.state.domainEntropy[domain] || null;
  }
  
  updateDomainMetric(domain, metric, value) {
    if (!this.state.domainEntropy[domain]) {
      return false;
    }
    
    if (this.state.domainEntropy[domain][metric] === undefined) {
      return false;
    }
    
    this.state.domainEntropy[domain][metric] = value;
    
    // Update overall entropy for the domain
    const metrics = Object.keys(this.state.domainEntropy[domain]).filter(key => key !== 'overallEntropy');
    let sum = 0;
    
    for (const key of metrics) {
      sum += this.state.domainEntropy[domain][key];
    }
    
    this.state.domainEntropy[domain].overallEntropy = sum / metrics.length;
    
    // Update universal entropy
    this._updateUniversalEntropy();
    
    // Emit event
    this.emit('entropy-update', {
      domain,
      metric,
      value,
      domainEntropy: this.state.domainEntropy[domain],
      universalEntropy: this.state.universalEntropy,
      timestamp: Date.now()
    });
    
    this.metrics.entropyUpdates++;
    
    return true;
  }
  
  calculateComphyon() {
    // E_CSDE = A1 × D (Cyber)
    const csdeEnergy = 0.8 * this.state.domainEntropy.cyber.overallEntropy;
    
    // E_CSFE = A2 × P (Financial)
    const csfeEnergy = 0.6 * this.state.domainEntropy.financial.overallEntropy;
    
    // E_CSME = T × I (Biological)
    const csmeEnergy = 0.5 * this.state.domainEntropy.biological.overallEntropy;
    
    // Calculate energy gradients (simplified)
    const csdeGradient = 0.05 * this.state.domainEntropy.cyber.overallEntropy;
    const csfeGradient = 0.03 * this.state.domainEntropy.financial.overallEntropy;
    const csmeGradient = 0.02 * this.state.domainEntropy.biological.overallEntropy;
    
    // Apply Comphyon formula: Cph = ((dE_CSDE × dE_CSFE) × log(E_CSME))/166000
    const comphyonValue = ((csdeGradient * csfeGradient) * Math.log(csmeEnergy)) / 166000;
    
    return comphyonValue;
  }
  
  getMetrics() {
    return { ...this.metrics };
  }
  
  _updateUniversalEntropy() {
    // Apply 18/82 principle
    const cyberWeight = 0.33;
    const financialWeight = 0.33;
    const biologicalWeight = 0.34;
    
    // Calculate universal entropy
    this.state.universalEntropy = (
      this.state.domainEntropy.cyber.overallEntropy * cyberWeight +
      this.state.domainEntropy.financial.overallEntropy * financialWeight +
      this.state.domainEntropy.biological.overallEntropy * biologicalWeight
    );
  }
}

/**
 * Create a test suite for Comphyon Meter Testing
 * @returns {NEPITestSuite} - Test suite
 */
function createMeterTestSuite() {
  const suite = new NEPITestSuite('Comphyon Meter Tests', {
    testingLayer: 'Component',
    domains: ['universal', 'cyber', 'financial', 'biological']
  });
  
  // Test variables
  let meter;
  
  // Setup
  suite.beforeEach(() => {
    // Create meter
    meter = new MockUniversalEntropyMeasurement({
      enableLogging: false,
      enableMetrics: true
    });
    
    // Start meter
    meter.start();
  });
  
  // Teardown
  suite.afterEach(() => {
    // Stop meter
    meter.stop();
  });
  
  // Test: Input Sensitivity
  suite.nepiTest('should respond accurately to domain energy inputs', async () => {
    // Initial values
    const initialUniversalEntropy = meter.getUniversalEntropy();
    const initialCyberEntropy = meter.getDomainEntropy('cyber').overallEntropy;
    
    // Update cyber domain metric
    meter.updateDomainMetric('cyber', 'policyEntropy', 0.8);
    
    // Get updated values
    const updatedUniversalEntropy = meter.getUniversalEntropy();
    const updatedCyberEntropy = meter.getDomainEntropy('cyber').overallEntropy;
    
    // Assert
    assertions.notEqual(initialUniversalEntropy, updatedUniversalEntropy, 'Universal entropy should change');
    assertions.notEqual(initialCyberEntropy, updatedCyberEntropy, 'Cyber entropy should change');
    assertions.isTrue(updatedUniversalEntropy > initialUniversalEntropy, 'Universal entropy should increase');
    assertions.isTrue(updatedCyberEntropy > initialCyberEntropy, 'Cyber entropy should increase');
  }, {
    testingType: 'Meter Testing',
    coherenceImpact: 'negative',
    domains: ['universal', 'cyber']
  });
  
  // Test: Cph Calculation Accuracy
  suite.nepiTest('should calculate Comphyon value accurately', async () => {
    // Set domain metrics
    meter.updateDomainMetric('cyber', 'policyEntropy', 0.7);
    meter.updateDomainMetric('financial', 'transactionEntropy', 0.6);
    meter.updateDomainMetric('biological', 'inflammationLevel', 0.5);
    
    // Calculate Comphyon value
    const comphyonValue = meter.calculateComphyon();
    
    // Calculate expected value
    // E_CSDE = A1 × D (Cyber)
    const csdeEnergy = 0.8 * meter.getDomainEntropy('cyber').overallEntropy;
    
    // E_CSFE = A2 × P (Financial)
    const csfeEnergy = 0.6 * meter.getDomainEntropy('financial').overallEntropy;
    
    // E_CSME = T × I (Biological)
    const csmeEnergy = 0.5 * meter.getDomainEntropy('biological').overallEntropy;
    
    // Calculate energy gradients (simplified)
    const csdeGradient = 0.05 * meter.getDomainEntropy('cyber').overallEntropy;
    const csfeGradient = 0.03 * meter.getDomainEntropy('financial').overallEntropy;
    const csmeGradient = 0.02 * meter.getDomainEntropy('biological').overallEntropy;
    
    // Apply Comphyon formula: Cph = ((dE_CSDE × dE_CSFE) × log(E_CSME))/166000
    const expectedComphyonValue = ((csdeGradient * csfeGradient) * Math.log(csmeEnergy)) / 166000;
    
    // Assert
    assertions.approximately(comphyonValue, expectedComphyonValue, 0.000001, 'Comphyon calculation incorrect');
  }, {
    testingType: 'Meter Testing',
    coherenceImpact: 'neutral',
    domains: ['universal', 'cyber', 'financial', 'biological']
  });
  
  // Test: Universal Entropy Calculation
  suite.nepiTest('should calculate universal entropy correctly', async () => {
    // Set domain metrics
    meter.updateDomainMetric('cyber', 'policyEntropy', 0.7);
    meter.updateDomainMetric('financial', 'transactionEntropy', 0.6);
    meter.updateDomainMetric('biological', 'inflammationLevel', 0.5);
    
    // Get domain entropies
    const cyberEntropy = meter.getDomainEntropy('cyber').overallEntropy;
    const financialEntropy = meter.getDomainEntropy('financial').overallEntropy;
    const biologicalEntropy = meter.getDomainEntropy('biological').overallEntropy;
    
    // Calculate expected universal entropy
    const expectedUniversalEntropy = (
      cyberEntropy * 0.33 +
      financialEntropy * 0.33 +
      biologicalEntropy * 0.34
    );
    
    // Get actual universal entropy
    const universalEntropy = meter.getUniversalEntropy();
    
    // Assert
    assertions.approximately(universalEntropy, expectedUniversalEntropy, 0.001, 'Universal entropy calculation incorrect');
  }, {
    testingType: 'Meter Testing',
    coherenceImpact: 'neutral',
    domains: ['universal', 'cyber', 'financial', 'biological']
  });
  
  // Test: Event Emission
  suite.nepiTest('should emit events on entropy updates', async () => {
    // Create event tracker
    let eventFired = false;
    let eventData = null;
    
    // Set up event listener
    meter.on('entropy-update', (data) => {
      eventFired = true;
      eventData = data;
    });
    
    // Update domain metric
    meter.updateDomainMetric('cyber', 'policyEntropy', 0.7);
    
    // Assert
    assertions.isTrue(eventFired, 'Entropy update event not fired');
    assertions.isTrue(eventData !== null, 'Event data is null');
    assertions.equal(eventData.domain, 'cyber', 'Event domain incorrect');
    assertions.equal(eventData.metric, 'policyEntropy', 'Event metric incorrect');
    assertions.equal(eventData.value, 0.7, 'Event value incorrect');
  }, {
    testingType: 'Meter Testing',
    coherenceImpact: 'neutral',
    domains: ['cyber']
  });
  
  // Test: Metrics Tracking
  suite.nepiTest('should track metrics correctly', async () => {
    // Get initial metrics
    const initialMetrics = meter.getMetrics();
    
    // Update domain metrics multiple times
    meter.updateDomainMetric('cyber', 'policyEntropy', 0.7);
    meter.updateDomainMetric('financial', 'transactionEntropy', 0.6);
    meter.updateDomainMetric('biological', 'inflammationLevel', 0.5);
    
    // Get updated metrics
    const updatedMetrics = meter.getMetrics();
    
    // Assert
    assertions.equal(updatedMetrics.entropyUpdates, initialMetrics.entropyUpdates + 3, 'Entropy updates metric incorrect');
  }, {
    testingType: 'Meter Testing',
    coherenceImpact: 'neutral',
    domains: ['cyber', 'financial', 'biological']
  });
  
  // Test: Domain Metric Updates
  suite.nepiTest('should update domain metrics correctly', async () => {
    // Update cyber domain metrics
    meter.updateDomainMetric('cyber', 'policyEntropy', 0.7);
    meter.updateDomainMetric('cyber', 'auditEntropy', 0.6);
    meter.updateDomainMetric('cyber', 'regulatoryEntropy', 0.5);
    
    // Get cyber domain entropy
    const cyberEntropy = meter.getDomainEntropy('cyber');
    
    // Assert
    assertions.equal(cyberEntropy.policyEntropy, 0.7, 'Policy entropy incorrect');
    assertions.equal(cyberEntropy.auditEntropy, 0.6, 'Audit entropy incorrect');
    assertions.equal(cyberEntropy.regulatoryEntropy, 0.5, 'Regulatory entropy incorrect');
    
    // Calculate expected overall entropy
    const expectedOverallEntropy = (0.7 + 0.6 + 0.5) / 3;
    
    // Assert overall entropy
    assertions.approximately(cyberEntropy.overallEntropy, expectedOverallEntropy, 0.001, 'Overall entropy calculation incorrect');
  }, {
    testingType: 'Meter Testing',
    coherenceImpact: 'neutral',
    domains: ['cyber']
  });
  
  return suite;
}

module.exports = {
  createMeterTestSuite,
  MockUniversalEntropyMeasurement
};
