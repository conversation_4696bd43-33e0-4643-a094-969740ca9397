const Joi = require('joi');

// Validation schemas
const schemas = {
  createAPI: Joi.object({
    name: Joi.string().required().min(3).max(100),
    description: Joi.string().optional().max(500),
    version: Joi.string().required().pattern(/^\d+\.\d+\.\d+$/), // Semantic versioning (x.y.z)
    status: Joi.string().required().valid('planning', 'development', 'testing', 'staging', 'production', 'deprecated', 'retired'),
    type: Joi.string().required().valid('rest', 'graphql', 'soap', 'grpc', 'webhook'),
    owner: Joi.string().required().max(100),
    team: Joi.string().optional().max(100),
    baseUrl: Joi.string().optional().uri(),
    documentation: Joi.string().optional().uri(),
    repository: Joi.string().optional().uri(),
    tags: Joi.array().items(Joi.string()).optional()
  }),
  
  updateAPI: Joi.object({
    name: Joi.string().optional().min(3).max(100),
    description: Joi.string().optional().max(500),
    status: Joi.string().optional().valid('planning', 'development', 'testing', 'staging', 'production', 'deprecated', 'retired'),
    owner: Joi.string().optional().max(100),
    team: Joi.string().optional().max(100),
    baseUrl: Joi.string().optional().uri(),
    documentation: Joi.string().optional().uri(),
    repository: Joi.string().optional().uri(),
    tags: Joi.array().items(Joi.string()).optional()
  }).min(1), // At least one field must be provided
  
  createAPIVersion: Joi.object({
    version: Joi.string().required().pattern(/^\d+\.\d+\.\d+$/), // Semantic versioning (x.y.z)
    status: Joi.string().required().valid('planning', 'development', 'testing', 'staging', 'production', 'deprecated', 'retired'),
    releaseDate: Joi.string().optional().pattern(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
    endOfLifeDate: Joi.string().optional().allow(null).pattern(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
    changeLog: Joi.string().optional().max(500),
    baseUrl: Joi.string().optional().uri(),
    documentation: Joi.string().optional().uri()
  }),
  
  updateAPIVersion: Joi.object({
    status: Joi.string().optional().valid('planning', 'development', 'testing', 'staging', 'production', 'deprecated', 'retired'),
    releaseDate: Joi.string().optional().pattern(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
    endOfLifeDate: Joi.string().optional().allow(null).pattern(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
    changeLog: Joi.string().optional().max(500),
    baseUrl: Joi.string().optional().uri(),
    documentation: Joi.string().optional().uri()
  }).min(1), // At least one field must be provided
  
  createAPIDependency: Joi.object({
    dependencyType: Joi.string().required().valid('api', 'service', 'database', 'library', 'other'),
    name: Joi.string().required().max(100),
    version: Joi.string().optional().max(50),
    description: Joi.string().optional().max(500),
    criticality: Joi.string().optional().valid('critical', 'high', 'medium', 'low')
  }),
  
  createAPIConsumer: Joi.object({
    name: Joi.string().required().max(100),
    type: Joi.string().required().valid('internal', 'external', 'partner'),
    contact: Joi.string().optional().max(100),
    usageLevel: Joi.string().optional().valid('high', 'medium', 'low'),
    apiVersions: Joi.array().items(Joi.string()).optional()
  })
};

/**
 * Middleware to validate request data against a schema
 * @param {string} schemaName - Name of the schema to validate against
 * @returns {Function} Express middleware function
 */
const validateRequest = (schemaName) => {
  return (req, res, next) => {
    const schema = schemas[schemaName];
    
    if (!schema) {
      return res.status(500).json({
        error: 'Internal Server Error',
        message: `Validation schema '${schemaName}' not found`
      });
    }
    
    const { error, value } = schema.validate(req.body, {
      abortEarly: false, // Return all errors, not just the first one
      stripUnknown: true // Remove unknown fields
    });
    
    if (error) {
      const errorMessage = error.details.map(detail => detail.message).join(', ');
      return res.status(400).json({
        error: 'Bad Request',
        message: errorMessage
      });
    }
    
    // Replace request body with validated value
    req.body = value;
    next();
  };
};

module.exports = {
  validateRequest
};
