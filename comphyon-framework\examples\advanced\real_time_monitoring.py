"""
Real-Time Monitoring and Control Example for the ComphyonΨᶜ Framework.

This example demonstrates a comprehensive real-time monitoring and control system
using the ComphyonΨᶜ Framework.
"""

import sys
import os
import time
import threading
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.animation import FuncAnimation
from collections import deque

# Add the parent directory to the path so we can import the packages
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../comphyon-meter')))
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../comphyon-governor')))

try:
    from src.comphyon_meter import ComphyonMeter
    from src.comphyon_governor import ComphyonGovernor
except ImportError:
    print("Error: Could not import ComphyonMeter or ComphyonGovernor.")
    print("Please make sure you have cloned the repositories and installed the packages.")
    print("See the README.md for instructions.")
    sys.exit(1)

class SystemSimulator:
    """
    Simulates a system that generates tensor data for the ComphyonΨᶜ Framework.
    """
    
    def __init__(self, name="System-1"):
        """
        Initialize the system simulator.
        
        Args:
            name: Name of the simulated system
        """
        self.name = name
        self.start_time = time.time()
        
        # Base values for tensor data
        self.csde_base = [0.75, 0.85, 0.65, 0.90]
        self.csfe_base = [0.65, 0.70, 0.80, 0.80]
        self.csme_base = [0.70, 0.90, 0.60, 0.85]
        
        # System state
        self.state = {
            'trend_factor': 0.02,
            'oscillation_factor': 0.05,
            'resource_allocation': 1.0,
            'coupling_strength': 1.0,
            'ethical_score': 0.82
        }
        
        # Control effect mapping
        self.control_effects = {
            'parameter_adjustment': self._apply_parameter_adjustment,
            'input_filtering': self._apply_input_filtering,
            'resource_allocation': self._apply_resource_allocation,
            'coupling_modulation': self._apply_coupling_modulation,
            'gradient_dampening': self._apply_gradient_dampening,
            'interference_management': self._apply_interference_management,
            'system_thresholds': self._apply_system_thresholds,
            'emergency_protocol': self._apply_emergency_protocol,
            'resource_constraints': self._apply_resource_constraints,
            'circuit_breaker': self._apply_circuit_breaker,
            'ethical_guardrail': self._apply_ethical_guardrail,
            'forced_diversity': self._apply_forced_diversity
        }
    
    def generate_tensor_data(self):
        """
        Generate tensor data based on the current system state.
        
        Returns:
            tuple: (csde_tensor, csfe_tensor, csme_tensor)
        """
        # Calculate elapsed time
        elapsed = time.time() - self.start_time
        
        # Generate tensor data with current state factors
        csde_tensor = self._generate_tensor(
            elapsed, self.csde_base, 
            self.state['oscillation_factor'], 
            self.state['trend_factor']
        )
        
        csfe_tensor = self._generate_tensor(
            elapsed, self.csfe_base, 
            self.state['oscillation_factor'], 
            self.state['trend_factor']
        )
        
        # CSME tensor is influenced by the ethical score
        csme_tensor = self._generate_tensor(
            elapsed, self.csme_base, 
            self.state['oscillation_factor'] * 0.5, 
            0.0
        )
        csme_tensor[0] = self.state['ethical_score']  # Trust component
        
        return csde_tensor, csfe_tensor, csme_tensor
    
    def apply_control_actions(self, control_actions):
        """
        Apply control actions to the system.
        
        Args:
            control_actions: Control actions from the ComphyonΨᶜ Governor
            
        Returns:
            dict: Updated system state
        """
        if control_actions['type'] == 'none':
            return self.state
        
        # Apply each control action
        for action in control_actions['actions']:
            action_type = action['type']
            if action_type in self.control_effects:
                self.control_effects[action_type](action)
        
        return self.state
    
    def _generate_tensor(self, t, base_values, oscillation_factor, trend_factor):
        """
        Generate tensor data with oscillation and trend.
        
        Args:
            t: Time value
            base_values: Base values for the tensor
            oscillation_factor: Factor for oscillation amplitude
            trend_factor: Factor for trend amplitude
            
        Returns:
            list: Generated tensor data
        """
        oscillation = oscillation_factor * np.sin(t / 5)
        trend = trend_factor * np.sin(t / 20)
        
        tensor = [
            base_values[0] + trend + np.random.normal(0, 0.03),
            base_values[1] + oscillation + np.random.normal(0, 0.03),
            base_values[2] + np.random.normal(0, 0.03),
            base_values[3] + np.random.normal(0, 0.03)
        ]
        
        # Apply resource allocation factor
        tensor = [x * self.state['resource_allocation'] for x in tensor]
        
        # Clip values to valid range
        return [max(0.1, min(0.99, x)) for x in tensor]
    
    # Control effect implementations
    def _apply_parameter_adjustment(self, action):
        """Apply parameter adjustment control action."""
        self.state['trend_factor'] *= 0.8
    
    def _apply_input_filtering(self, action):
        """Apply input filtering control action."""
        self.state['oscillation_factor'] *= 0.7
    
    def _apply_resource_allocation(self, action):
        """Apply resource allocation control action."""
        self.state['resource_allocation'] = float(action['value'])
    
    def _apply_coupling_modulation(self, action):
        """Apply coupling modulation control action."""
        self.state['coupling_strength'] = float(action['value'])
    
    def _apply_gradient_dampening(self, action):
        """Apply gradient dampening control action."""
        self.state['trend_factor'] *= float(action['value'])
    
    def _apply_interference_management(self, action):
        """Apply interference management control action."""
        if action['value'] == 'disruptive':
            self.state['oscillation_factor'] *= 0.3
        else:
            self.state['oscillation_factor'] *= 0.7
    
    def _apply_system_thresholds(self, action):
        """Apply system thresholds control action."""
        if action['value'] == 'minimum':
            self.state['resource_allocation'] = 0.3
        else:
            self.state['resource_allocation'] = 0.5
    
    def _apply_emergency_protocol(self, action):
        """Apply emergency protocol control action."""
        self.state['trend_factor'] = 0.001
        self.state['oscillation_factor'] = 0.01
        self.state['resource_allocation'] = 0.2
    
    def _apply_resource_constraints(self, action):
        """Apply resource constraints control action."""
        self.state['resource_allocation'] = float(action['value'])
    
    def _apply_circuit_breaker(self, action):
        """Apply circuit breaker control action."""
        if action['value'] == 'emergency_shutdown':
            self.state['trend_factor'] = 0.0
            self.state['oscillation_factor'] = 0.0
            self.state['resource_allocation'] = 0.1
        elif action['value'] == 'emergency_throttle':
            self.state['trend_factor'] *= 0.1
            self.state['resource_allocation'] = 0.3
    
    def _apply_ethical_guardrail(self, action):
        """Apply ethical guardrail control action."""
        self.state['ethical_score'] = min(0.99, self.state['ethical_score'] * 1.05)
    
    def _apply_forced_diversity(self, action):
        """Apply forced diversity control action."""
        # Add some randomness to the system
        self.state['trend_factor'] += np.random.normal(0, 0.01)
        self.state['oscillation_factor'] += np.random.normal(0, 0.01)


class RealTimeMonitor:
    """
    Real-time monitor for the ComphyonΨᶜ Framework.
    """
    
    def __init__(self, update_interval=500):
        """
        Initialize the real-time monitor.
        
        Args:
            update_interval: Update interval in milliseconds
        """
        self.meter = ComphyonMeter()
        self.governor = ComphyonGovernor(
            thresholds={
                'acceleration': 1.5,  # Trigger control at 1.5 Cph
                'velocity': 60.0      # Trigger control at 60.0 Cph-Flux
            }
        )
        self.system = SystemSimulator()
        
        self.update_interval = update_interval
        self.max_history_length = 100
        self.metrics_history = deque(maxlen=self.max_history_length)
        self.control_history = deque(maxlen=self.max_history_length)
        
        # Initialize the plot
        self.fig, (self.ax1, self.ax2, self.ax3) = plt.subplots(3, 1, figsize=(10, 12))
        self.fig.suptitle('ComphyonΨᶜ Real-Time Monitoring and Control', fontsize=16)
        
        # Initialize empty lines
        self.velocity_line, = self.ax1.plot([], [], 'b-', linewidth=2)
        self.acceleration_line, = self.ax2.plot([], [], 'r-', linewidth=2)
        self.resource_line, = self.ax3.plot([], [], 'g-', linewidth=2)
        self.control_points, = self.ax2.plot([], [], 'ro', markersize=8)
        
        # Set up the axes
        self.ax1.set_ylabel('Velocity (Cph-Flux)')
        self.ax1.grid(True, alpha=0.3)
        
        self.ax2.set_ylabel('Acceleration (Cph)')
        self.ax2.grid(True, alpha=0.3)
        
        self.ax3.set_ylabel('Resource Allocation')
        self.ax3.set_xlabel('Time (s)')
        self.ax3.grid(True, alpha=0.3)
        
        # Add safety thresholds to acceleration plot
        self.ax2.axhline(y=self.governor.thresholds['acceleration'], color='orange', 
                         linestyle='--', alpha=0.7, label='Warning')
        self.ax2.axhline(y=2.5, color='red', linestyle='--', alpha=0.7, label='Critical')
        self.ax2.legend()
        
        # Add status text
        self.status_text = self.fig.text(0.02, 0.02, '', fontsize=12)
        
        # Initialize data
        self.times = []
        self.velocities = []
        self.accelerations = []
        self.resources = []
        self.control_times = []
        self.control_accelerations = []
        self.start_time = time.time()
    
    def update(self, frame):
        """
        Update function for the animation.
        
        Args:
            frame: Frame number (not used)
            
        Returns:
            tuple: Updated artists
        """
        # Generate tensor data
        csde_tensor, csfe_tensor, csme_tensor = self.system.generate_tensor_data()
        
        # Calculate metrics
        metrics = self.meter.calculate(csde_tensor, csfe_tensor, csme_tensor)
        self.metrics_history.append(metrics)
        
        # Apply control actions if needed
        control_actions = self.governor.regulate(metrics)
        
        # Apply control actions to the system
        if control_actions['type'] != 'none':
            self.system.apply_control_actions(control_actions)
            self.control_history.append(control_actions)
            self.control_times.append(time.time() - self.start_time)
            self.control_accelerations.append(metrics['acceleration'])
        
        # Update data
        t = time.time() - self.start_time
        self.times.append(t)
        self.velocities.append(metrics['velocity'])
        self.accelerations.append(metrics['acceleration'])
        self.resources.append(self.system.state['resource_allocation'])
        
        # Limit data length
        if len(self.times) > self.max_history_length:
            self.times.pop(0)
            self.velocities.pop(0)
            self.accelerations.pop(0)
            self.resources.pop(0)
        
        # Update plots
        self.velocity_line.set_data(self.times, self.velocities)
        self.acceleration_line.set_data(self.times, self.accelerations)
        self.resource_line.set_data(self.times, self.resources)
        self.control_points.set_data(self.control_times, self.control_accelerations)
        
        # Adjust axes limits
        if self.times:
            self.ax1.set_xlim(min(self.times), max(self.times))
            self.ax1.set_ylim(min(self.velocities) * 0.9, max(self.velocities) * 1.1)
            
            self.ax2.set_xlim(min(self.times), max(self.times))
            self.ax2.set_ylim(0, max(max(self.accelerations) * 1.1, 3.0))
            
            self.ax3.set_xlim(min(self.times), max(self.times))
            self.ax3.set_ylim(0, 1.1)
        
        # Update status text
        status = "Safe"
        color = "green"
        if metrics['acceleration'] > 2.5:
            status = "Critical"
            color = "red"
        elif metrics['acceleration'] > 1.5:
            status = "Warning"
            color = "orange"
        
        control_info = ""
        if self.control_history:
            last_control = self.control_history[-1]
            control_info = f" | Last Control: {last_control['type']}"
        
        self.status_text.set_text(
            f"Status: {status} | Velocity: {metrics['velocity']:.4f} | "
            f"Acceleration: {metrics['acceleration']:.4f}{control_info}"
        )
        self.status_text.set_color(color)
        
        return (self.velocity_line, self.acceleration_line, self.resource_line, 
                self.control_points, self.status_text)
    
    def run(self):
        """
        Run the real-time monitor.
        """
        # Create the animation
        self.animation = FuncAnimation(
            self.fig, self.update, interval=self.update_interval, blit=True)
        
        plt.tight_layout()
        plt.subplots_adjust(bottom=0.1)  # Make room for status text
        plt.show()


def main():
    """
    Run the real-time monitoring and control example.
    """
    print("ComphyonΨᶜ Framework - Real-Time Monitoring and Control Example")
    print("==============================================================")
    print("This example demonstrates a comprehensive real-time monitoring and")
    print("control system using the ComphyonΨᶜ Framework.")
    print("\nKey concepts demonstrated:")
    print("- Real-time data collection and processing")
    print("- Dynamic threshold adjustment")
    print("- Adaptive control strategies")
    print("- Feedback loop implementation")
    print("- Multi-level control (Micro, Meso, Macro)")
    print("\nPress Ctrl+C to exit")
    
    monitor = RealTimeMonitor(update_interval=500)
    monitor.run()

if __name__ == "__main__":
    main()
