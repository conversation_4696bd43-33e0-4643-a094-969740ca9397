/**
 * NovaFuse Universal API Connector Team Model
 * 
 * This model defines the schema for teams in the NovaConnect UAC.
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

// Define team member schema
const teamMemberSchema = new Schema({
  userId: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'User', 
    required: true 
  },
  role: { 
    type: String, 
    enum: ['owner', 'admin', 'member', 'readonly'], 
    default: 'member' 
  },
  permissions: [{ 
    type: String 
  }],
  addedBy: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'User' 
  },
  addedAt: { 
    type: Date, 
    default: Date.now 
  },
  lastActive: { 
    type: Date 
  }
}, {
  _id: false
});

// Define team invitation schema
const teamInvitationSchema = new Schema({
  email: { 
    type: String, 
    required: true, 
    trim: true, 
    lowercase: true 
  },
  role: { 
    type: String, 
    enum: ['admin', 'member', 'readonly'], 
    default: 'member' 
  },
  token: { 
    type: String, 
    required: true 
  },
  invitedBy: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'User', 
    required: true 
  },
  invitedAt: { 
    type: Date, 
    default: Date.now 
  },
  expiresAt: { 
    type: Date, 
    required: true 
  },
  status: { 
    type: String, 
    enum: ['pending', 'accepted', 'declined', 'expired'], 
    default: 'pending' 
  }
}, {
  _id: true
});

// Define team schema
const teamSchema = new Schema({
  name: { 
    type: String, 
    required: true, 
    trim: true 
  },
  description: { 
    type: String, 
    trim: true 
  },
  slug: { 
    type: String, 
    required: true, 
    unique: true, 
    trim: true, 
    lowercase: true 
  },
  avatar: { 
    type: String 
  },
  members: [teamMemberSchema],
  invitations: [teamInvitationSchema],
  settings: {
    defaultEnvironment: { 
      type: mongoose.Schema.Types.ObjectId, 
      ref: 'Environment' 
    },
    defaultRole: { 
      type: String, 
      enum: ['admin', 'member', 'readonly'], 
      default: 'member' 
    },
    allowPublicConnectors: { 
      type: Boolean, 
      default: false 
    },
    requireApprovalForConnectors: { 
      type: Boolean, 
      default: false 
    },
    requireApprovalForCredentials: { 
      type: Boolean, 
      default: true 
    },
    allowMemberInvitations: { 
      type: Boolean, 
      default: false 
    }
  },
  status: { 
    type: String, 
    enum: ['active', 'inactive', 'archived'], 
    default: 'active' 
  },
  createdBy: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'User', 
    required: true 
  },
  metadata: { 
    type: Object 
  }
}, {
  timestamps: true
});

// Add pre-save hook to generate slug if not provided
teamSchema.pre('save', function(next) {
  if (this.isNew && !this.slug) {
    this.slug = this.name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');
  }
  next();
});

// Add method to check if user is a member
teamSchema.methods.isMember = function(userId) {
  return this.members.some(member => 
    member.userId.toString() === userId.toString()
  );
};

// Add method to get member
teamSchema.methods.getMember = function(userId) {
  return this.members.find(member => 
    member.userId.toString() === userId.toString()
  );
};

// Add method to get member role
teamSchema.methods.getMemberRole = function(userId) {
  const member = this.getMember(userId);
  return member ? member.role : null;
};

// Add method to check if user has a specific role
teamSchema.methods.hasRole = function(userId, role) {
  const memberRole = this.getMemberRole(userId);
  
  if (!memberRole) return false;
  
  if (Array.isArray(role)) {
    return role.includes(memberRole);
  }
  
  return memberRole === role;
};

// Add method to check if user has a specific permission
teamSchema.methods.hasPermission = function(userId, permission) {
  const member = this.getMember(userId);
  
  if (!member) return false;
  
  // Owner and admin roles have all permissions
  if (member.role === 'owner' || member.role === 'admin') {
    return true;
  }
  
  if (Array.isArray(permission)) {
    return permission.some(p => member.permissions.includes(p));
  }
  
  return member.permissions.includes(permission);
};

// Add method to add member
teamSchema.methods.addMember = function(userId, role, addedBy) {
  // Check if user is already a member
  if (this.isMember(userId)) {
    return false;
  }
  
  // Add member
  this.members.push({
    userId,
    role: role || this.settings.defaultRole,
    addedBy,
    addedAt: new Date()
  });
  
  return this.save();
};

// Add method to update member
teamSchema.methods.updateMember = function(userId, updates) {
  // Find member
  const memberIndex = this.members.findIndex(member => 
    member.userId.toString() === userId.toString()
  );
  
  if (memberIndex === -1) {
    return false;
  }
  
  // Update member
  Object.assign(this.members[memberIndex], updates);
  
  return this.save();
};

// Add method to remove member
teamSchema.methods.removeMember = function(userId) {
  // Check if user is a member
  if (!this.isMember(userId)) {
    return false;
  }
  
  // Remove member
  this.members = this.members.filter(member => 
    member.userId.toString() !== userId.toString()
  );
  
  return this.save();
};

// Add method to create invitation
teamSchema.methods.createInvitation = function(email, role, invitedBy, expiresIn = 7 * 24 * 60 * 60 * 1000) {
  // Generate token
  const token = require('crypto').randomBytes(32).toString('hex');
  
  // Create invitation
  const invitation = {
    email,
    role: role || this.settings.defaultRole,
    token,
    invitedBy,
    invitedAt: new Date(),
    expiresAt: new Date(Date.now() + expiresIn),
    status: 'pending'
  };
  
  // Add invitation
  this.invitations.push(invitation);
  
  return this.save().then(() => invitation);
};

// Add method to get invitation by token
teamSchema.methods.getInvitationByToken = function(token) {
  return this.invitations.find(invitation => invitation.token === token);
};

// Add method to get invitation by email
teamSchema.methods.getInvitationByEmail = function(email) {
  return this.invitations.find(invitation => 
    invitation.email.toLowerCase() === email.toLowerCase() && 
    invitation.status === 'pending'
  );
};

// Add method to accept invitation
teamSchema.methods.acceptInvitation = function(token, userId) {
  // Find invitation
  const invitation = this.getInvitationByToken(token);
  
  if (!invitation || invitation.status !== 'pending' || invitation.expiresAt < new Date()) {
    return false;
  }
  
  // Update invitation status
  invitation.status = 'accepted';
  
  // Add member
  this.members.push({
    userId,
    role: invitation.role,
    addedBy: invitation.invitedBy,
    addedAt: new Date()
  });
  
  return this.save();
};

// Add method to decline invitation
teamSchema.methods.declineInvitation = function(token) {
  // Find invitation
  const invitation = this.getInvitationByToken(token);
  
  if (!invitation || invitation.status !== 'pending') {
    return false;
  }
  
  // Update invitation status
  invitation.status = 'declined';
  
  return this.save();
};

// Add method to cancel invitation
teamSchema.methods.cancelInvitation = function(invitationId) {
  // Find invitation
  const invitationIndex = this.invitations.findIndex(invitation => 
    invitation._id.toString() === invitationId.toString() && 
    invitation.status === 'pending'
  );
  
  if (invitationIndex === -1) {
    return false;
  }
  
  // Remove invitation
  this.invitations.splice(invitationIndex, 1);
  
  return this.save();
};

// Add method to check if team is active
teamSchema.methods.isActive = function() {
  return this.status === 'active';
};

// Add method to check if team is archived
teamSchema.methods.isArchived = function() {
  return this.status === 'archived';
};

// Add method to archive team
teamSchema.methods.archive = function() {
  this.status = 'archived';
  return this.save();
};

// Add method to activate team
teamSchema.methods.activate = function() {
  this.status = 'active';
  return this.save();
};

// Add indexes
teamSchema.index({ slug: 1 }, { unique: true });
teamSchema.index({ name: 1 });
teamSchema.index({ status: 1 });
teamSchema.index({ createdBy: 1 });
teamSchema.index({ 'members.userId': 1 });
teamSchema.index({ 'invitations.email': 1, 'invitations.status': 1 });

// Create model
const Team = mongoose.model('Team', teamSchema);

module.exports = Team;
