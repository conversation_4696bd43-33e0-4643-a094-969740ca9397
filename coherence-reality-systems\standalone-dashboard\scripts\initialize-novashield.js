const SecureCredentialManager = require('../utils/credential-encryption');
const credentialManager = new SecureCredentialManager();

async function initializeCredentials() {
  try {
    // Your ClickBank credentials
    const credentials = {
      clickbank: {
        username: process.env.CLICKBANK_USERNAME,
        password: process.env.CLICKBANK_PASSWORD
      }
    };

    console.log('Initializing credentials with NovaShield...');
    
    // Save credentials
    const result = await credentialManager.saveCredentials(credentials);
    console.log('Credentials saved successfully:', result);

    // Verify credentials
    const status = await credentialManager.getStatus();
    console.log('NovaShield Status:', status);

    console.log('✅ NovaShield initialization complete!');
  } catch (error) {
    console.error('❌ Error initializing credentials:', error.message);
    process.exit(1);
  }
}

// Run initialization
initializeCredentials();
