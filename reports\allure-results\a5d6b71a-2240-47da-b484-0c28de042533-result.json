{"name": "test_pi_rhythm_synchronization", "status": "passed", "description": "Test π-Rhythm synchronization.", "start": 1752966148994, "stop": 1752966149001, "uuid": "9d575e4e-9387-4fcc-8a1f-99ba14716d80", "historyId": "23a9864e2eceb95c36373d65094dc1bc", "testCaseId": "23a9864e2eceb95c36373d65094dc1bc", "fullName": "tests.novacortex.test_integration#test_pi_rhythm_synchronization", "labels": [{"name": "tag", "value": "asyncio"}, {"name": "parentSuite", "value": "tests.novacortex"}, {"name": "suite", "value": "test_integration"}, {"name": "host", "value": "d1cae64bda82"}, {"name": "thread", "value": "1-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.novacortex.test_integration"}]}