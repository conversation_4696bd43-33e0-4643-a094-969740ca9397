/**
 * NovaVisionIntegration.js
 * 
 * This module provides integration between NovaDNA and NovaVision.
 * It maps NovaDNA API endpoints to NovaVision UI components.
 */

/**
 * NovaVisionIntegration class for integrating NovaDNA with NovaVision
 */
class NovaVisionIntegration {
  constructor(options = {}) {
    this.apiBaseUrl = options.apiBaseUrl || '/api';
    this.novaVisionComponents = options.novaVisionComponents;
    
    if (!this.novaVisionComponents) {
      throw new Error('NovaVisionComponents is required');
    }
  }

  /**
   * Get the emergency access UI configuration
   * @param {Object} options - Configuration options
   * @returns {Object} - The UI configuration
   */
  getEmergencyAccessUI(options = {}) {
    // Get the emergency access schema from NovaVisionComponents
    const schema = this.novaVisionComponents.getEmergencyAccessSchema();
    
    // Update API endpoints to use our NovaDNA API
    const updatedSchema = {
      ...schema,
      sections: schema.sections.map(section => {
        if (section.id === 'scanner') {
          return {
            ...section,
            components: section.components.map(component => {
              if (component.type === 'qrScanner' || component.type === 'nfcReader') {
                return {
                  ...component,
                  onScan: {
                    ...component.onScan,
                    url: `${this.apiBaseUrl}/access/emergency`
                  }
                };
              }
              return component;
            })
          };
        } else if (section.id === 'manualEntry') {
          return {
            ...section,
            actions: section.actions.map(action => {
              if (action.id === 'submit') {
                return {
                  ...action,
                  url: `${this.apiBaseUrl}/access/emergency`
                };
              }
              return action;
            })
          };
        }
        return section;
      })
    };
    
    // Add context collection to the schema
    updatedSchema.contextCollection = {
      enabled: true,
      fields: [
        {
          id: 'emergencyType',
          label: 'Emergency Type',
          type: 'select',
          required: true,
          options: [
            { value: 'MEDICAL', label: 'Medical Emergency' },
            { value: 'TRAUMA', label: 'Trauma' },
            { value: 'CARDIAC', label: 'Cardiac' },
            { value: 'RESPIRATORY', label: 'Respiratory' },
            { value: 'NEUROLOGICAL', label: 'Neurological' },
            { value: 'ALLERGIC', label: 'Allergic Reaction' },
            { value: 'VEHICULAR', label: 'Vehicle Accident' },
            { value: 'OTHER', label: 'Other Emergency' }
          ]
        },
        {
          id: 'emergencySeverity',
          label: 'Severity',
          type: 'select',
          required: true,
          options: [
            { value: 'LOW', label: 'Low' },
            { value: 'MODERATE', label: 'Moderate' },
            { value: 'HIGH', label: 'High' },
            { value: 'CRITICAL', label: 'Critical' }
          ]
        },
        {
          id: 'location',
          label: 'Location',
          type: 'object',
          fields: [
            {
              id: 'type',
              label: 'Location Type',
              type: 'select',
              options: [
                { value: 'HOSPITAL', label: 'Hospital' },
                { value: 'AMBULANCE', label: 'Ambulance' },
                { value: 'EMERGENCY_SCENE', label: 'Emergency Scene' },
                { value: 'CLINIC', label: 'Clinic' },
                { value: 'OTHER', label: 'Other' }
              ]
            },
            {
              id: 'name',
              label: 'Location Name',
              type: 'text'
            }
          ]
        }
      ]
    };
    
    return updatedSchema;
  }

  /**
   * Get the emergency override UI configuration
   * @param {Object} options - Configuration options
   * @returns {Object} - The UI configuration
   */
  getEmergencyOverrideUI(options = {}) {
    // Create a schema for emergency override
    const schema = {
      type: 'form',
      title: 'Emergency Override Access',
      description: 'Use this form to request emergency override access to a medical profile',
      submitUrl: `${this.apiBaseUrl}/access/override`,
      submitMethod: 'POST',
      sections: [
        {
          id: 'overrideInfo',
          title: 'Override Information',
          fields: [
            {
              id: 'profileId',
              label: 'Profile ID',
              type: 'text',
              required: true,
              placeholder: 'Enter the profile ID'
            },
            {
              id: 'reason',
              label: 'Override Reason',
              type: 'textarea',
              required: true,
              placeholder: 'Explain why emergency override is needed'
            },
            {
              id: 'emergencyType',
              label: 'Emergency Type',
              type: 'select',
              required: true,
              options: [
                { value: 'MEDICAL', label: 'Medical Emergency' },
                { value: 'TRAUMA', label: 'Trauma' },
                { value: 'CARDIAC', label: 'Cardiac' },
                { value: 'RESPIRATORY', label: 'Respiratory' },
                { value: 'NEUROLOGICAL', label: 'Neurological' },
                { value: 'ALLERGIC', label: 'Allergic Reaction' },
                { value: 'VEHICULAR', label: 'Vehicle Accident' },
                { value: 'OTHER', label: 'Other Emergency' }
              ]
            },
            {
              id: 'severityLevel',
              label: 'Severity Level',
              type: 'select',
              required: true,
              options: [
                { value: 'LOW', label: 'Low' },
                { value: 'MEDIUM', label: 'Medium' },
                { value: 'HIGH', label: 'High' },
                { value: 'CRITICAL', label: 'Critical' }
              ]
            }
          ]
        },
        {
          id: 'locationInfo',
          title: 'Location Information',
          fields: [
            {
              id: 'location.type',
              label: 'Location Type',
              type: 'select',
              options: [
                { value: 'HOSPITAL', label: 'Hospital' },
                { value: 'AMBULANCE', label: 'Ambulance' },
                { value: 'EMERGENCY_SCENE', label: 'Emergency Scene' },
                { value: 'CLINIC', label: 'Clinic' },
                { value: 'OTHER', label: 'Other' }
              ]
            },
            {
              id: 'location.name',
              label: 'Location Name',
              type: 'text'
            },
            {
              id: 'location.coordinates',
              label: 'Coordinates',
              type: 'location'
            }
          ]
        }
      ],
      actions: [
        {
          id: 'submit',
          label: 'Request Override',
          primary: true,
          confirmMessage: 'Are you sure you want to request emergency override access? This action will be logged and audited.'
        },
        {
          id: 'cancel',
          label: 'Cancel',
          action: 'navigate',
          url: '/dashboard'
        }
      ],
      resultHandler: 'displayEmergencyProfile'
    };
    
    return schema;
  }

  /**
   * Get the profile view UI configuration
   * @param {String} accessLevel - The access level
   * @returns {Object} - The UI configuration
   */
  getProfileViewUI(accessLevel = 'standard') {
    // Get the profile view schema from NovaVisionComponents
    const schema = this.novaVisionComponents.getProfileViewSchema(accessLevel);
    
    // Update API endpoints to use our NovaDNA API
    const updatedSchema = {
      ...schema,
      dataUrl: `${this.apiBaseUrl}/profiles/{profileId}`
    };
    
    // Add emergency-specific actions based on access level
    if (accessLevel === 'standard' || accessLevel === 'full') {
      updatedSchema.actions = [
        ...(updatedSchema.actions || []),
        {
          id: 'emergency',
          label: 'Emergency Actions',
          icon: 'alert',
          actions: [
            {
              id: 'notify',
              label: 'Notify Emergency Contacts',
              icon: 'phone',
              action: 'fetchData',
              url: `${this.apiBaseUrl}/profiles/{profileId}/notify-contacts`,
              method: 'POST',
              confirmMessage: 'Are you sure you want to notify all emergency contacts?'
            },
            {
              id: 'medicalAlert',
              label: 'Medical Alert',
              icon: 'medical',
              action: 'openModal',
              modalId: 'medicalAlertModal'
            }
          ]
        }
      ];
    }
    
    return updatedSchema;
  }

  /**
   * Get the override review UI configuration
   * @returns {Object} - The UI configuration
   */
  getOverrideReviewUI() {
    // Create a schema for override review
    const schema = {
      type: 'form',
      title: 'Review Emergency Override',
      description: 'Review and approve/reject an emergency override request',
      submitUrl: `${this.apiBaseUrl}/access/override/{overrideId}/review`,
      submitMethod: 'POST',
      dataUrl: `${this.apiBaseUrl}/access/override/{overrideId}`,
      sections: [
        {
          id: 'overrideDetails',
          title: 'Override Details',
          fields: [
            {
              id: 'overrideId',
              label: 'Override ID',
              type: 'text',
              readOnly: true
            },
            {
              id: 'serviceId',
              label: 'Service',
              type: 'text',
              readOnly: true
            },
            {
              id: 'userId',
              label: 'User',
              type: 'text',
              readOnly: true
            },
            {
              id: 'reason',
              label: 'Override Reason',
              type: 'textarea',
              readOnly: true
            },
            {
              id: 'emergencyType',
              label: 'Emergency Type',
              type: 'text',
              readOnly: true
            },
            {
              id: 'severityLevel',
              label: 'Severity Level',
              type: 'text',
              readOnly: true
            },
            {
              id: 'timestamp',
              label: 'Requested At',
              type: 'datetime',
              readOnly: true
            },
            {
              id: 'status',
              label: 'Status',
              type: 'text',
              readOnly: true
            }
          ]
        },
        {
          id: 'reviewDecision',
          title: 'Review Decision',
          fields: [
            {
              id: 'status',
              label: 'Decision',
              type: 'select',
              required: true,
              options: [
                { value: 'APPROVED', label: 'Approve' },
                { value: 'REJECTED', label: 'Reject' },
                { value: 'FLAGGED', label: 'Flag for Investigation' },
                { value: 'REQUIRES_INVESTIGATION', label: 'Requires Further Investigation' }
              ]
            },
            {
              id: 'notes',
              label: 'Review Notes',
              type: 'textarea',
              placeholder: 'Enter notes about your decision'
            }
          ]
        }
      ],
      actions: [
        {
          id: 'submit',
          label: 'Submit Review',
          primary: true
        },
        {
          id: 'cancel',
          label: 'Cancel',
          action: 'navigate',
          url: '/dashboard'
        }
      ]
    };
    
    return schema;
  }

  /**
   * Get the security dashboard UI configuration
   * @returns {Object} - The UI configuration
   */
  getSecurityDashboardUI() {
    // Get the security dashboard schema from NovaVisionComponents
    const schema = this.novaVisionComponents.getSecurityDashboardSchema();
    
    // Update API endpoints to use our NovaDNA API
    const updatedSchema = {
      ...schema,
      dataUrl: `${this.apiBaseUrl}/security/dashboard`
    };
    
    // Add override-specific sections
    updatedSchema.sections.push({
      id: 'overrides',
      title: 'Emergency Overrides',
      components: [
        {
          type: 'table',
          id: 'overridesTable',
          dataPath: 'overrides',
          columns: [
            {
              id: 'timestamp',
              label: 'Time',
              format: 'datetime'
            },
            {
              id: 'serviceId',
              label: 'Service'
            },
            {
              id: 'emergencyType',
              label: 'Emergency Type'
            },
            {
              id: 'severityLevel',
              label: 'Severity',
              format: 'badge'
            },
            {
              id: 'status',
              label: 'Status',
              format: 'badge'
            },
            {
              id: 'reviewStatus',
              label: 'Review Status',
              format: 'badge'
            },
            {
              id: 'actions',
              label: 'Actions',
              format: 'actions',
              actions: [
                {
                  id: 'view',
                  label: 'View Details',
                  icon: 'eye',
                  action: 'navigate',
                  url: '/overrides/{overrideId}'
                },
                {
                  id: 'review',
                  label: 'Review',
                  icon: 'check',
                  action: 'navigate',
                  url: '/overrides/{overrideId}/review',
                  condition: "item.status !== 'ACTIVE' && item.reviewStatus === 'PENDING'"
                }
              ]
            }
          ]
        }
      ]
    });
    
    return updatedSchema;
  }

  /**
   * Get the break-glass protocol UI configuration
   * @returns {Object} - The UI configuration
   */
  getBreakGlassProtocolUI() {
    // Create a schema for break-glass protocol
    const schema = {
      type: 'dashboard',
      title: 'Break-Glass Protocol',
      description: 'Manage emergency overrides and access controls',
      dataUrl: `${this.apiBaseUrl}/access/override-logs`,
      sections: [
        {
          id: 'activeOverrides',
          title: 'Active Overrides',
          components: [
            {
              type: 'table',
              id: 'activeOverridesTable',
              dataPath: 'activeOverrides',
              columns: [
                {
                  id: 'overrideId',
                  label: 'ID'
                },
                {
                  id: 'timestamp',
                  label: 'Initiated',
                  format: 'datetime'
                },
                {
                  id: 'serviceId',
                  label: 'Service'
                },
                {
                  id: 'emergencyType',
                  label: 'Emergency Type'
                },
                {
                  id: 'severityLevel',
                  label: 'Severity',
                  format: 'badge'
                },
                {
                  id: 'expiresAt',
                  label: 'Expires',
                  format: 'datetime'
                },
                {
                  id: 'actions',
                  label: 'Actions',
                  format: 'actions',
                  actions: [
                    {
                      id: 'view',
                      label: 'View Details',
                      icon: 'eye',
                      action: 'navigate',
                      url: '/overrides/{overrideId}'
                    },
                    {
                      id: 'terminate',
                      label: 'Terminate',
                      icon: 'stop',
                      action: 'openModal',
                      modalId: 'terminateOverrideModal',
                      modalParams: {
                        overrideId: '{overrideId}'
                      }
                    }
                  ]
                }
              ]
            }
          ]
        },
        {
          id: 'overrideHistory',
          title: 'Override History',
          components: [
            {
              type: 'filters',
              id: 'overrideFilters',
              filters: [
                {
                  id: 'startDate',
                  label: 'Start Date',
                  type: 'date'
                },
                {
                  id: 'endDate',
                  label: 'End Date',
                  type: 'date'
                },
                {
                  id: 'serviceId',
                  label: 'Service',
                  type: 'select',
                  dataUrl: `${this.apiBaseUrl}/services`,
                  valuePath: 'id',
                  labelPath: 'name'
                },
                {
                  id: 'status',
                  label: 'Status',
                  type: 'select',
                  options: [
                    { value: 'COMPLETED', label: 'Completed' },
                    { value: 'TERMINATED', label: 'Terminated' },
                    { value: 'EXPIRED', label: 'Expired' }
                  ]
                }
              ]
            },
            {
              type: 'table',
              id: 'overrideHistoryTable',
              dataPath: 'overrides',
              columns: [
                {
                  id: 'overrideId',
                  label: 'ID'
                },
                {
                  id: 'timestamp',
                  label: 'Initiated',
                  format: 'datetime'
                },
                {
                  id: 'serviceId',
                  label: 'Service'
                },
                {
                  id: 'emergencyType',
                  label: 'Emergency Type'
                },
                {
                  id: 'severityLevel',
                  label: 'Severity',
                  format: 'badge'
                },
                {
                  id: 'status',
                  label: 'Status',
                  format: 'badge'
                },
                {
                  id: 'reviewStatus',
                  label: 'Review Status',
                  format: 'badge'
                },
                {
                  id: 'actions',
                  label: 'Actions',
                  format: 'actions',
                  actions: [
                    {
                      id: 'view',
                      label: 'View Details',
                      icon: 'eye',
                      action: 'navigate',
                      url: '/overrides/{overrideId}'
                    },
                    {
                      id: 'review',
                      label: 'Review',
                      icon: 'check',
                      action: 'navigate',
                      url: '/overrides/{overrideId}/review',
                      condition: "item.reviewStatus === 'PENDING'"
                    }
                  ]
                }
              ]
            }
          ]
        },
        {
          id: 'notificationSettings',
          title: 'Notification Settings',
          components: [
            {
              type: 'form',
              id: 'notificationSettingsForm',
              submitUrl: `${this.apiBaseUrl}/settings/notifications`,
              submitMethod: 'POST',
              fields: [
                {
                  id: 'emailNotifications',
                  label: 'Email Notifications',
                  type: 'boolean',
                  defaultValue: true
                },
                {
                  id: 'smsNotifications',
                  label: 'SMS Notifications',
                  type: 'boolean',
                  defaultValue: false
                },
                {
                  id: 'pushNotifications',
                  label: 'Push Notifications',
                  type: 'boolean',
                  defaultValue: true
                },
                {
                  id: 'notificationEvents',
                  label: 'Notification Events',
                  type: 'multiselect',
                  options: [
                    { value: 'OVERRIDE_INITIATED', label: 'Override Initiated' },
                    { value: 'OVERRIDE_COMPLETED', label: 'Override Completed' },
                    { value: 'OVERRIDE_TERMINATED', label: 'Override Terminated' },
                    { value: 'OVERRIDE_EXPIRED', label: 'Override Expired' },
                    { value: 'OVERRIDE_REVIEWED', label: 'Override Reviewed' }
                  ],
                  defaultValue: ['OVERRIDE_INITIATED', 'OVERRIDE_REVIEWED']
                }
              ],
              actions: [
                {
                  id: 'save',
                  label: 'Save Settings',
                  primary: true
                }
              ]
            }
          ]
        }
      ]
    };
    
    return schema;
  }
}

module.exports = NovaVisionIntegration;
