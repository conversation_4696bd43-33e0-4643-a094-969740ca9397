/**
 * CSFE (Cyber-Safety Finance Equation) Engine
 *
 * This module exports all components of the CSFE engine.
 *
 * The CSFE formula is: CSFE = (M ⊗ E ⊕ S) × π10³
 *
 * Where:
 * - M = Market Data - representing price, volume, and liquidity information
 * - E = Economic Data - representing macroeconomic indicators and trends
 * - S = Sentiment Data - representing market sentiment and behavioral factors
 * - ⊗ = Tensor product operator - enabling multi-dimensional integration
 * - ⊕ = Fusion operator - creating non-linear synergy between components
 * - π10³ = Circular trust topology factor - derived from the Wilson loop circumference
 *
 * With 18/82 principle applied:
 * - M = (0.18 × Technical Indicators) + (0.82 × Market Structure)
 * - E = (0.18 × Leading Indicators) + (0.82 × Lagging Indicators)
 * - S = (0.18 × Retail Sentiment) + (0.82 × Institutional Positioning)
 *
 * The enhanced CSFE now includes:
 * - Financial Entropy Interpretation (Ψₜᶠ) for cyber-risk contexts
 * - CSFE Meter for measuring and visualizing financial entropy metrics
 * - CSFE Governor for monitoring thresholds and executing control actions
 * - Ψ-Revert Financial (ΨRF) protocols for financial system interventions
 */

// Original CSFE components
const CSFEEngine = require('./core/csfe_engine');
const TensorOperator = require('../csde/tensor/tensor_operator');
const FusionOperator = require('../csde/tensor/fusion_operator');
const CircularTrustTopology = require('../csde/circular_trust/circular_trust_topology');
const FinancialPredictionEngine = require('./financial_prediction/financial_prediction_engine');

// Trinity CSFE components
const TrinityCSFEEngine = require('./trinity/trinity_csfe_engine');
const TrinityCSFE1882Engine = require('./trinity/trinity_csfe_1882_engine');

// Enhanced CSFE components
const CSFEController = require('./core/csfe_controller');
const FinancialEntropyInterpreter = require('./financial_entropy/financial_entropy_interpreter');
const CSFEMeter = require('./meter/csfe_meter');
const CSFEGovernor = require('./governor/csfe_governor');
const PsiRevertFinancial = require('./protocols/psi_revert_financial');

/**
 * Create a fully configured enhanced CSFE system
 * @param {Object} options - Configuration options
 * @returns {Object} - Enhanced CSFE system components
 */
function createEnhancedCSFESystem(options = {}) {
  // Create core components
  const financialEntropyInterpreter = new FinancialEntropyInterpreter(options.financialEntropyInterpreterOptions);

  // Create meter and governor components
  const csfeMeter = new CSFEMeter(options.csfeMeterOptions);
  const csfeGovernor = new CSFEGovernor(options.csfeGovernorOptions);

  // Create protocol components
  const psiRevertFinancial = new PsiRevertFinancial(options.psiRevertFinancialOptions);

  // Create controller
  const csfeController = new CSFEController({
    financialEntropyInterpreter,
    csfeMeter,
    csfeGovernor,
    psiRevertFinancial,
    ...options.csfeControllerOptions
  });

  return {
    csfeController,
    financialEntropyInterpreter,
    csfeMeter,
    csfeGovernor,
    psiRevertFinancial
  };
}

module.exports = {
  // Original CSFE components
  CSFEEngine,
  TensorOperator,
  FusionOperator,
  CircularTrustTopology,
  FinancialPredictionEngine,

  // Trinity CSFE components
  TrinityCSFEEngine,
  TrinityCSFE1882Engine,

  // Enhanced CSFE components
  CSFEController,
  FinancialEntropyInterpreter,
  CSFEMeter,
  CSFEGovernor,
  PsiRevertFinancial,

  // Factory function
  createEnhancedCSFESystem
};
