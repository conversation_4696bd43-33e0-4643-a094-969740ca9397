/**
 * NovaFuse Universal API Connector - Performance Monitor
 *
 * This module provides utilities for monitoring system performance.
 */

const { createLogger } = require('./logger');
const os = require('os');

const logger = createLogger('performance-monitor');

/**
 * Performance monitor for tracking system metrics
 */
class PerformanceMonitor {
  constructor(options = {}) {
    this.options = {
      sampleInterval: options.sampleInterval || 60, // 1 minute
      historySize: options.historySize || 60, // 1 hour of minute samples
      ...options
    };

    this.metrics = {
      cpu: [],
      memory: [],
      eventLoop: [],
      requests: {
        total: 0,
        success: 0,
        error: 0,
        latency: []
      },
      connectors: {
        executions: 0,
        success: 0,
        error: 0,
        latency: []
      }
    };

    this.startTime = Date.now();

    // Start sampling
    this.sampleInterval = setInterval(() => {
      this.sampleMetrics();
    }, this.options.sampleInterval * 1000);

    logger.info('Performance monitor initialized', {
      sampleInterval: this.options.sampleInterval,
      historySize: this.options.historySize
    });
  }

  /**
   * Sample system metrics
   */
  sampleMetrics() {
    // Sample CPU usage
    const cpuUsage = this._getCpuUsage();
    this.metrics.cpu.push({
      timestamp: Date.now(),
      value: cpuUsage
    });

    // Trim history
    if (this.metrics.cpu.length > this.options.historySize) {
      this.metrics.cpu.shift();
    }

    // Sample memory usage
    const memoryUsage = this._getMemoryUsage();
    this.metrics.memory.push({
      timestamp: Date.now(),
      value: memoryUsage
    });

    // Trim history
    if (this.metrics.memory.length > this.options.historySize) {
      this.metrics.memory.shift();
    }

    // Sample event loop lag
    const eventLoopLag = this._getEventLoopLag();
    this.metrics.eventLoop.push({
      timestamp: Date.now(),
      value: eventLoopLag
    });

    // Trim history
    if (this.metrics.eventLoop.length > this.options.historySize) {
      this.metrics.eventLoop.shift();
    }

    // Log metrics
    logger.debug('Performance metrics sampled', {
      cpu: cpuUsage,
      memory: memoryUsage,
      eventLoop: eventLoopLag
    });
  }

  /**
   * Get CPU usage
   *
   * @returns {number} - CPU usage percentage
   * @private
   */
  _getCpuUsage() {
    const cpus = os.cpus();
    let totalIdle = 0;
    let totalTick = 0;

    for (const cpu of cpus) {
      for (const type in cpu.times) {
        totalTick += cpu.times[type];
      }
      totalIdle += cpu.times.idle;
    }

    return 100 - (totalIdle / totalTick * 100);
  }

  /**
   * Get memory usage
   *
   * @returns {number} - Memory usage percentage
   * @private
   */
  _getMemoryUsage() {
    const totalMemory = os.totalmem();
    const freeMemory = os.freemem();
    const usedMemory = totalMemory - freeMemory;

    return (usedMemory / totalMemory) * 100;
  }

  /**
   * Get event loop lag
   *
   * @returns {number} - Event loop lag in milliseconds
   * @private
   */
  _getEventLoopLag() {
    return new Promise(resolve => {
      const start = Date.now();
      setImmediate(() => {
        resolve(Date.now() - start);
      });
    });
  }

  /**
   * Record request metrics
   *
   * @param {Object} data - Request data
   */
  recordRequest(data) {
    this.metrics.requests.total++;

    if (data.success) {
      this.metrics.requests.success++;
    } else {
      this.metrics.requests.error++;
    }

    // Record latency
    this.metrics.requests.latency.push({
      timestamp: Date.now(),
      value: data.latency
    });

    // Trim latency history
    if (this.metrics.requests.latency.length > this.options.historySize * 10) {
      this.metrics.requests.latency.shift();
    }
  }

  /**
   * Record connector execution metrics
   *
   * @param {Object} data - Execution data
   */
  recordConnectorExecution(data) {
    this.metrics.connectors.executions++;

    if (data.success) {
      this.metrics.connectors.success++;
    } else {
      this.metrics.connectors.error++;
    }

    // Record latency
    this.metrics.connectors.latency.push({
      timestamp: Date.now(),
      value: data.latency
    });

    // Trim latency history
    if (this.metrics.connectors.latency.length > this.options.historySize * 10) {
      this.metrics.connectors.latency.shift();
    }
  }

  /**
   * Get performance metrics
   *
   * @returns {Object} - The performance metrics
   */
  getMetrics() {
    // Calculate averages
    const cpuAvg = this._calculateAverage(this.metrics.cpu);
    const memoryAvg = this._calculateAverage(this.metrics.memory);
    const eventLoopAvg = this._calculateAverage(this.metrics.eventLoop);
    const requestLatencyAvg = this._calculateAverage(this.metrics.requests.latency);
    const connectorLatencyAvg = this._calculateAverage(this.metrics.connectors.latency);

    return {
      uptime: Date.now() - this.startTime,
      cpu: {
        current: this.metrics.cpu.length > 0 ? this.metrics.cpu[this.metrics.cpu.length - 1].value : 0,
        average: cpuAvg,
        history: this.metrics.cpu
      },
      memory: {
        current: this.metrics.memory.length > 0 ? this.metrics.memory[this.metrics.memory.length - 1].value : 0,
        average: memoryAvg,
        history: this.metrics.memory
      },
      eventLoop: {
        current: this.metrics.eventLoop.length > 0 ? this.metrics.eventLoop[this.metrics.eventLoop.length - 1].value : 0,
        average: eventLoopAvg,
        history: this.metrics.eventLoop
      },
      requests: {
        total: this.metrics.requests.total,
        success: this.metrics.requests.success,
        error: this.metrics.requests.error,
        successRate: this.metrics.requests.total > 0 ? this.metrics.requests.success / this.metrics.requests.total : 0,
        latency: {
          average: requestLatencyAvg,
          p95: this._calculatePercentile(this.metrics.requests.latency, 95),
          p99: this._calculatePercentile(this.metrics.requests.latency, 99)
        }
      },
      connectors: {
        executions: this.metrics.connectors.executions,
        success: this.metrics.connectors.success,
        error: this.metrics.connectors.error,
        successRate: this.metrics.connectors.executions > 0 ? this.metrics.connectors.success / this.metrics.connectors.executions : 0,
        latency: {
          average: connectorLatencyAvg,
          p95: this._calculatePercentile(this.metrics.connectors.latency, 95),
          p99: this._calculatePercentile(this.metrics.connectors.latency, 99)
        }
      }
    };
  }

  /**
   * Calculate average from metrics array
   *
   * @param {Array} metrics - Array of metrics
   * @returns {number} - Average value
   * @private
   */
  _calculateAverage(metrics) {
    if (metrics.length === 0) {
      return 0;
    }

    const sum = metrics.reduce((acc, metric) => acc + metric.value, 0);
    return sum / metrics.length;
  }

  /**
   * Calculate percentile from metrics array
   *
   * @param {Array} metrics - Array of metrics
   * @param {number} percentile - Percentile to calculate
   * @returns {number} - Percentile value
   * @private
   */
  _calculatePercentile(metrics, percentile) {
    if (metrics.length === 0) {
      return 0;
    }

    // Sort values
    const values = metrics.map(metric => metric.value).sort((a, b) => a - b);

    // Calculate index
    const index = Math.ceil((percentile / 100) * values.length) - 1;

    return values[index];
  }

  /**
   * Reset performance metrics
   *
   * @returns {Object} - Reset confirmation
   */
  resetMetrics() {
    // Reset metrics
    this.metrics = {
      cpu: [],
      memory: [],
      eventLoop: [],
      requests: {
        total: 0,
        success: 0,
        error: 0,
        latency: []
      },
      connectors: {
        executions: 0,
        success: 0,
        error: 0,
        latency: []
      }
    };

    // Reset start time
    this.startTime = Date.now();

    logger.info('Performance metrics reset');

    return {
      success: true,
      timestamp: this.startTime,
      message: 'Performance metrics have been reset'
    };
  }

  /**
   * Stop the performance monitor
   */
  stop() {
    clearInterval(this.sampleInterval);
    logger.info('Performance monitor stopped');
  }
}

// Create singleton instance
const performanceMonitor = new PerformanceMonitor();

module.exports = performanceMonitor;
