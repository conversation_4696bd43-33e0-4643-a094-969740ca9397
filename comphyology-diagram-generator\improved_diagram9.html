<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>9. 13 NovaFuse Components</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: white;
        }
        .diagram-container {
            width: 1100px;
            height: 700px;
            position: relative;
            border: 2px solid black;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
        }
        .element {
            position: absolute;
            border-radius: 0; /* Square corners for patent diagrams */
            padding: 20px 20px 20px 50px; /* Extra padding on left for number */
            z-index: 2;
            background-color: white;
            border: 2px solid black;
        }
        .element-number {
            position: absolute;
            top: 50%;
            left: 15px;
            transform: translateY(-50%);
            background-color: black;
            color: white;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: bold;
            z-index: 3;
        }
        .connection {
            position: absolute;
            z-index: 1;
            background-color: black;
        }
        h1 {
            text-align: center;
            margin-bottom: 20px;
            font-size: 24px;
        }
        .bold-formula {
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>9. 13 NovaFuse Components</h1>
    
    <div class="diagram-container">
        <!-- NovaFuse Platform -->
        <div class="element" style="top: 50px; left: 350px; width: 400px; font-weight: bold; font-size: 20px;">
            13 Universal NovaFuse Components
            <div class="element-number">1</div>
        </div>
        
        <!-- Core components -->
        <div class="element" style="top: 150px; left: 350px; width: 400px; font-weight: bold; font-size: 16px;">
            NovaCore (Universal Compliance Testing Framework)
            <div class="element-number">2</div>
        </div>
        
        <div class="element" style="top: 250px; left: 150px; width: 300px; font-size: 14px;">
            NovaShield (Universal Vendor Risk Management)
            <div class="element-number">3</div>
        </div>
        
        <div class="element" style="top: 250px; left: 650px; width: 300px; font-size: 14px;">
            NovaTrack (Universal Compliance Tracking)
            <div class="element-number">4</div>
        </div>
        
        <div class="element" style="top: 350px; left: 50px; width: 225px; font-size: 14px;">
            NovaLearn (Universal Training)
            <div class="element-number">5</div>
        </div>
        
        <div class="element" style="top: 350px; left: 300px; width: 225px; font-size: 14px;">
            NovaThink (Universal Decision Engine)
            <div class="element-number">6</div>
        </div>
        
        <div class="element" style="top: 350px; left: 550px; width: 225px; font-size: 14px;">
            NovaConnect (Universal API)
            <div class="element-number">7</div>
        </div>
        
        <div class="element" style="top: 350px; left: 800px; width: 225px; font-size: 14px;">
            NovaView (Universal Visualization)
            <div class="element-number">8</div>
        </div>
        
        <div class="element" style="top: 450px; left: 50px; width: 225px; font-size: 14px;">
            NovaVision (Universal UI Framework)
            <div class="element-number">9</div>
        </div>
        
        <div class="element" style="top: 450px; left: 300px; width: 225px; font-size: 14px;">
            NovaFlow (Universal Workflow)
            <div class="element-number">10</div>
        </div>
        
        <div class="element" style="top: 450px; left: 550px; width: 225px; font-size: 14px;">
            NovaPulse+ (Universal Monitoring)
            <div class="element-number">11</div>
        </div>
        
        <div class="element" style="top: 450px; left: 800px; width: 225px; font-size: 14px;">
            NovaStore (Universal Marketplace)
            <div class="element-number">12</div>
        </div>
        
        <div class="element" style="top: 550px; left: 350px; width: 400px; font-weight: bold; font-size: 16px;">
            NovaFuse (Universal Platform)
            <div class="element-number">13</div>
        </div>
        
        <!-- Implementation -->
        <div class="element" style="top: 650px; left: 350px; width: 400px; font-weight: bold; font-size: 16px;">
            3-6-9-12-13 Alignment Architecture
            <div class="element-number">14</div>
        </div>
        
        <!-- Connections -->
        <!-- Connect NovaFuse Platform to NovaCore -->
        <div class="connection" style="top: 100px; left: 550px; width: 2px; height: 50px;"></div>
        
        <!-- Connect NovaCore to second tier -->
        <div class="connection" style="top: 200px; left: 300px; width: 2px; height: 50px;"></div>
        <div class="connection" style="top: 200px; left: 300px; width: 50px; height: 2px;"></div>
        
        <div class="connection" style="top: 200px; left: 800px; width: 2px; height: 50px;"></div>
        <div class="connection" style="top: 200px; left: 750px; width: 50px; height: 2px;"></div>
        
        <!-- Connect second tier to third tier -->
        <div class="connection" style="top: 300px; left: 162px; width: 2px; height: 50px;"></div>
        
        <div class="connection" style="top: 300px; left: 412px; width: 2px; height: 50px;"></div>
        <div class="connection" style="top: 300px; left: 300px; width: 112px; height: 2px;"></div>
        
        <div class="connection" style="top: 300px; left: 662px; width: 2px; height: 50px;"></div>
        <div class="connection" style="top: 300px; left: 662px; width: 112px; height: 2px;"></div>
        
        <div class="connection" style="top: 300px; left: 912px; width: 2px; height: 50px;"></div>
        <div class="connection" style="top: 300px; left: 800px; width: 112px; height: 2px;"></div>
        
        <!-- Connect third tier to fourth tier -->
        <div class="connection" style="top: 400px; left: 162px; width: 2px; height: 50px;"></div>
        
        <div class="connection" style="top: 400px; left: 412px; width: 2px; height: 50px;"></div>
        
        <div class="connection" style="top: 400px; left: 662px; width: 2px; height: 50px;"></div>
        
        <div class="connection" style="top: 400px; left: 912px; width: 2px; height: 50px;"></div>
        
        <!-- Connect fourth tier to NovaFuse -->
        <div class="connection" style="top: 500px; left: 162px; width: 2px; height: 50px;"></div>
        <div class="connection" style="top: 550px; left: 162px; width: 188px; height: 2px;"></div>
        
        <div class="connection" style="top: 500px; left: 412px; width: 2px; height: 50px;"></div>
        
        <div class="connection" style="top: 500px; left: 662px; width: 2px; height: 50px;"></div>
        <div class="connection" style="top: 550px; left: 662px; width: 88px; height: 2px;"></div>
        
        <div class="connection" style="top: 500px; left: 912px; width: 2px; height: 50px;"></div>
        <div class="connection" style="top: 550px; left: 750px; width: 162px; height: 2px;"></div>
        
        <!-- Connect NovaFuse to Implementation -->
        <div class="connection" style="top: 600px; left: 550px; width: 2px; height: 50px;"></div>
    </div>
</body>
</html>
