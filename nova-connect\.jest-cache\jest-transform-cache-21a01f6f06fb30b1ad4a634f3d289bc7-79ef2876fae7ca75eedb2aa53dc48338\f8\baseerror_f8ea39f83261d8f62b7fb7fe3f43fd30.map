{"version": 3, "names": ["UAConnectorError", "Error", "constructor", "message", "options", "name", "code", "severity", "context", "cause", "timestamp", "Date", "toISOString", "errorId", "_generateErrorId", "captureStackTrace", "now", "toString", "randomStr", "Math", "random", "substring", "toJSON", "includeStack", "json", "stack", "getUserMessage", "getDeveloperMessage", "module", "exports"], "sources": ["base-error.js"], "sourcesContent": ["/**\n * NovaFuse Universal API Connector - Base Error\n * \n * This module defines the base error class for the UAC.\n * All UAC-specific errors should extend this class.\n */\n\n/**\n * Base error class for all UAC errors\n * @class UAConnectorError\n * @extends Error\n */\nclass UAConnectorError extends Error {\n  /**\n   * Create a new UAConnectorError\n   * \n   * @param {string} message - Error message\n   * @param {Object} options - Error options\n   * @param {string} options.code - Error code\n   * @param {string} options.severity - Error severity (info, warning, error, critical)\n   * @param {Object} options.context - Additional context for the error\n   * @param {Error} options.cause - The error that caused this error\n   */\n  constructor(message, options = {}) {\n    super(message);\n    \n    // Set the name to the class name\n    this.name = this.constructor.name;\n    \n    // Set error properties\n    this.code = options.code || 'UAC_ERROR';\n    this.severity = options.severity || 'error';\n    this.context = options.context || {};\n    this.cause = options.cause;\n    this.timestamp = new Date().toISOString();\n    \n    // Generate a unique error ID\n    this.errorId = this._generateErrorId();\n    \n    // Capture stack trace\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, this.constructor);\n    }\n  }\n\n  /**\n   * Generate a unique error ID\n   * \n   * @returns {string} - Unique error ID\n   * @private\n   */\n  _generateErrorId() {\n    const timestamp = Date.now().toString(36);\n    const randomStr = Math.random().toString(36).substring(2, 10);\n    return `${timestamp}-${randomStr}`;\n  }\n\n  /**\n   * Convert the error to a JSON object\n   * \n   * @param {boolean} includeStack - Whether to include the stack trace\n   * @returns {Object} - JSON representation of the error\n   */\n  toJSON(includeStack = false) {\n    const json = {\n      errorId: this.errorId,\n      name: this.name,\n      message: this.message,\n      code: this.code,\n      severity: this.severity,\n      timestamp: this.timestamp,\n      context: this.context\n    };\n\n    if (includeStack) {\n      json.stack = this.stack;\n    }\n\n    if (this.cause) {\n      json.cause = this.cause instanceof Error \n        ? {\n            name: this.cause.name,\n            message: this.cause.message,\n            ...(includeStack ? { stack: this.cause.stack } : {})\n          }\n        : this.cause;\n    }\n\n    return json;\n  }\n\n  /**\n   * Get a user-friendly error message\n   * \n   * @returns {string} - User-friendly error message\n   */\n  getUserMessage() {\n    return this.message;\n  }\n\n  /**\n   * Get a developer-friendly error message with more details\n   * \n   * @returns {string} - Developer-friendly error message\n   */\n  getDeveloperMessage() {\n    return `[${this.code}] ${this.message}`;\n  }\n}\n\nmodule.exports = UAConnectorError;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMA,gBAAgB,SAASC,KAAK,CAAC;EACnC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEC,WAAWA,CAACC,OAAO,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IACjC,KAAK,CAACD,OAAO,CAAC;;IAEd;IACA,IAAI,CAACE,IAAI,GAAG,IAAI,CAACH,WAAW,CAACG,IAAI;;IAEjC;IACA,IAAI,CAACC,IAAI,GAAGF,OAAO,CAACE,IAAI,IAAI,WAAW;IACvC,IAAI,CAACC,QAAQ,GAAGH,OAAO,CAACG,QAAQ,IAAI,OAAO;IAC3C,IAAI,CAACC,OAAO,GAAGJ,OAAO,CAACI,OAAO,IAAI,CAAC,CAAC;IACpC,IAAI,CAACC,KAAK,GAAGL,OAAO,CAACK,KAAK;IAC1B,IAAI,CAACC,SAAS,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;;IAEzC;IACA,IAAI,CAACC,OAAO,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;;IAEtC;IACA,IAAIb,KAAK,CAACc,iBAAiB,EAAE;MAC3Bd,KAAK,CAACc,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAACb,WAAW,CAAC;IACjD;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEY,gBAAgBA,CAAA,EAAG;IACjB,MAAMJ,SAAS,GAAGC,IAAI,CAACK,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC;IACzC,MAAMC,SAAS,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACH,QAAQ,CAAC,EAAE,CAAC,CAACI,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;IAC7D,OAAO,GAAGX,SAAS,IAAIQ,SAAS,EAAE;EACpC;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEI,MAAMA,CAACC,YAAY,GAAG,KAAK,EAAE;IAC3B,MAAMC,IAAI,GAAG;MACXX,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBR,IAAI,EAAE,IAAI,CAACA,IAAI;MACfF,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBG,IAAI,EAAE,IAAI,CAACA,IAAI;MACfC,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBG,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBF,OAAO,EAAE,IAAI,CAACA;IAChB,CAAC;IAED,IAAIe,YAAY,EAAE;MAChBC,IAAI,CAACC,KAAK,GAAG,IAAI,CAACA,KAAK;IACzB;IAEA,IAAI,IAAI,CAAChB,KAAK,EAAE;MACde,IAAI,CAACf,KAAK,GAAG,IAAI,CAACA,KAAK,YAAYR,KAAK,GACpC;QACEI,IAAI,EAAE,IAAI,CAACI,KAAK,CAACJ,IAAI;QACrBF,OAAO,EAAE,IAAI,CAACM,KAAK,CAACN,OAAO;QAC3B,IAAIoB,YAAY,GAAG;UAAEE,KAAK,EAAE,IAAI,CAAChB,KAAK,CAACgB;QAAM,CAAC,GAAG,CAAC,CAAC;MACrD,CAAC,GACD,IAAI,CAAChB,KAAK;IAChB;IAEA,OAAOe,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;EACEE,cAAcA,CAAA,EAAG;IACf,OAAO,IAAI,CAACvB,OAAO;EACrB;;EAEA;AACF;AACA;AACA;AACA;EACEwB,mBAAmBA,CAAA,EAAG;IACpB,OAAO,IAAI,IAAI,CAACrB,IAAI,KAAK,IAAI,CAACH,OAAO,EAAE;EACzC;AACF;AAEAyB,MAAM,CAACC,OAAO,GAAG7B,gBAAgB", "ignoreList": []}