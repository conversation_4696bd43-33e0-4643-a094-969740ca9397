#!/usr/bin/env python3
# UUFT Trinity Patterns Testing
# This script tests for Trinity patterns across different domains

import os
import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from scipy import stats
from scipy.cluster.hierarchy import dendrogram, linkage
from scipy.cluster.hierarchy import fcluster
import logging
import gzip
import json
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
import zipfile
import io

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("uuft_trinity_patterns.log"),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger("UUFT_Trinity_Patterns")

# Base directory for all data
BASE_DIR = "D:/Archives"
RESULTS_DIR = os.path.join(BASE_DIR, "Results")

# Test for Trinity patterns
def test_trinity_patterns(data, domain, dataset_name):
    """
    Test for Trinity patterns in the given data.
    Trinity patterns are three-part structures with source, manifestation, and integration components.

    Args:
        data: The data to test (numpy array or pandas DataFrame)
        domain: The domain of the data (cosmological, biological, social, technological)
        dataset_name: The name of the dataset

    Returns:
        dict: Results of the Trinity patterns test
    """
    logger.info(f"Testing Trinity patterns for {domain} dataset: {dataset_name}")

    try:
        # Convert to numpy array if needed
        if isinstance(data, pd.Series):
            data = data.values.reshape(-1, 1)
        elif isinstance(data, pd.DataFrame):
            data = data.values

        # Ensure data is 2D
        if data.ndim == 1:
            data = data.reshape(-1, 1)

        # Remove rows with NaN values
        data = data[~np.isnan(data).any(axis=1)]

        # If data has more than 3 columns, use PCA to reduce to 3 dimensions
        if data.shape[1] > 3:
            # Standardize the data
            scaler = StandardScaler()
            data_scaled = scaler.fit_transform(data)

            # Apply PCA
            pca = PCA(n_components=3)
            data_pca = pca.fit_transform(data_scaled)

            # Calculate explained variance
            explained_variance = pca.explained_variance_ratio_

            # Use PCA components
            data_for_analysis = data_pca

            # Store PCA information
            pca_info = {
                "explained_variance_ratio": [float(v) for v in explained_variance],
                "total_explained_variance": float(sum(explained_variance)),
                "n_components": 3
            }
        else:
            # If data has 3 or fewer columns, use as is
            data_for_analysis = data
            pca_info = None

        # Identify potential trinity patterns using hierarchical clustering
        trinity_patterns = []

        if data_for_analysis.shape[1] >= 3:
            # Use the first 3 columns/components
            trinity_data = data_for_analysis[:, :3]

            # Calculate correlations between components
            corr_matrix = np.corrcoef(trinity_data.T)

            # Check if components form a trinity pattern
            # A trinity pattern should have moderate correlation (not too high, not too low)
            # between all three components
            is_trinity = True
            for i in range(3):
                for j in range(i+1, 3):
                    corr = corr_matrix[i, j]
                    # Check if correlation is in a reasonable range (0.3 to 0.7)
                    if abs(corr) < 0.3 or abs(corr) > 0.7:
                        is_trinity = False

            if is_trinity:
                trinity_patterns.append({
                    "components": ["Component 1", "Component 2", "Component 3"],
                    "correlation_matrix": [[float(corr_matrix[i, j]) for j in range(3)] for i in range(3)],
                    "pattern_strength": float(np.mean([abs(corr_matrix[i, j]) for i in range(3) for j in range(i+1, 3)]))
                })

        # If we have fewer than 3 columns, try clustering rows
        if data_for_analysis.shape[1] < 3 and data_for_analysis.shape[0] >= 30:
            # Perform hierarchical clustering
            Z = linkage(data_for_analysis, 'ward')

            # Cut the dendrogram to get 3 clusters
            clusters = fcluster(Z, 3, criterion='maxclust')

            # Calculate cluster statistics
            cluster_stats = []
            for i in range(1, 4):
                cluster_data = data_for_analysis[clusters == i]
                if len(cluster_data) > 0:
                    cluster_stats.append({
                        "cluster_id": int(i),
                        "size": int(len(cluster_data)),
                        "mean": float(np.mean(cluster_data)),
                        "std": float(np.std(cluster_data)),
                        "min": float(np.min(cluster_data)),
                        "max": float(np.max(cluster_data))
                    })

            # Check if clusters form a trinity pattern
            # A trinity pattern should have clusters of reasonable size
            # and distinct characteristics
            if len(cluster_stats) == 3:
                # Calculate total data points
                total_points = sum(stat["size"] for stat in cluster_stats)

                # Check if no cluster is too dominant (> 60% of data)
                is_balanced = all(stat["size"] / total_points <= 0.6 for stat in cluster_stats)

                # Check if means are distinct
                means = [stat["mean"] for stat in cluster_stats]
                is_distinct = all(abs(means[i] - means[j]) > (cluster_stats[i]["std"] + cluster_stats[j]["std"]) / 2
                                 for i in range(3) for j in range(i+1, 3))

                if is_balanced and is_distinct:
                    trinity_patterns.append({
                        "components": ["Cluster 1", "Cluster 2", "Cluster 3"],
                        "cluster_statistics": cluster_stats,
                        "pattern_strength": float(min(abs(means[i] - means[j]) / ((cluster_stats[i]["std"] + cluster_stats[j]["std"]) / 2)
                                                for i in range(3) for j in range(i+1, 3)))
                    })

        # Create visualization
        if trinity_patterns and data_for_analysis.shape[1] >= 2:
            plt.figure(figsize=(10, 8))

            # If we have at least 3 dimensions, create a 3D scatter plot
            if data_for_analysis.shape[1] >= 3:
                from mpl_toolkits.mplot3d import Axes3D
                ax = plt.figure().add_subplot(111, projection='3d')
                ax.scatter(data_for_analysis[:, 0], data_for_analysis[:, 1], data_for_analysis[:, 2],
                          c='b', marker='o', alpha=0.5)
                ax.set_xlabel('Component 1')
                ax.set_ylabel('Component 2')
                ax.set_zlabel('Component 3')
                plt.title(f'Trinity Pattern: {domain.capitalize()} - {dataset_name}')
            else:
                # Otherwise, create a 2D scatter plot
                plt.scatter(data_for_analysis[:, 0],
                           data_for_analysis[:, 1] if data_for_analysis.shape[1] > 1 else np.zeros_like(data_for_analysis[:, 0]),
                           c='b', marker='o', alpha=0.5)
                plt.xlabel('Component 1')
                plt.ylabel('Component 2' if data_for_analysis.shape[1] > 1 else 'N/A')
                plt.title(f'Trinity Pattern: {domain.capitalize()} - {dataset_name}')

            # Save visualization
            os.makedirs(RESULTS_DIR, exist_ok=True)
            plt.savefig(os.path.join(RESULTS_DIR, f'trinity_pattern_{domain}_{dataset_name}.png'))
            plt.close()

        # Compile results
        results = {
            "domain": domain,
            "dataset_name": dataset_name,
            "data_dimensions": int(data.shape[1]),
            "data_points": int(data.shape[0]),
            "pca_info": pca_info,
            "trinity_patterns_count": len(trinity_patterns),
            "trinity_patterns": trinity_patterns,
            "is_trinity_pattern_present": len(trinity_patterns) > 0
        }

        logger.info(f"Trinity patterns test results for {domain} - {dataset_name}: {len(trinity_patterns)} patterns found")

        return results

    except Exception as e:
        logger.error(f"Error testing Trinity patterns for {domain} - {dataset_name}: {e}")
        return {
            "domain": domain,
            "dataset_name": dataset_name,
            "error": str(e),
            "is_trinity_pattern_present": False
        }

# Load and test cosmological data
def test_cosmological_data():
    """Test cosmological data for Trinity patterns."""
    logger.info("Testing cosmological data for Trinity patterns...")

    results = []
    cosmological_dir = os.path.join(BASE_DIR, "Cosmological")

    # Example: Test WMAP parameters
    wmap_file = os.path.join(cosmological_dir, "wmap_params.txt")

    # Create sample cosmological data if it doesn't exist
    if not os.path.exists(wmap_file):
        try:
            logger.info(f"Creating sample cosmological data file: {wmap_file}")

            # Create sample data with a distribution that might show Trinity patterns
            # Format: omegam (matter density), omegal (dark energy), h (Hubble constant)
            sample_data = """# WMAP Cosmological Parameters Sample Data
# This is a sample dataset for testing the UUFT Trinity patterns
# Format: omegam omegal h
# omegam: matter density
# omegal: dark energy density
# h: Hubble constant
0.10 0.90 0.70
0.12 0.88 0.71
0.15 0.85 0.72
0.18 0.82 0.73
0.20 0.80 0.69
0.22 0.78 0.68
0.25 0.75 0.67
0.27 0.73 0.66
0.30 0.70 0.65
0.32 0.68 0.64
0.35 0.65 0.63
0.37 0.63 0.62
0.40 0.60 0.61
0.42 0.58 0.60
0.45 0.55 0.59
0.47 0.53 0.58
0.50 0.50 0.57
0.52 0.48 0.56
0.55 0.45 0.55
0.57 0.43 0.54
0.60 0.40 0.53
0.62 0.38 0.52
0.65 0.35 0.51
0.67 0.33 0.50
0.70 0.30 0.49
"""
            # Save sample data
            with open(wmap_file, 'w') as f:
                f.write(sample_data)

            logger.info(f"Created sample cosmological data file: {wmap_file}")
        except Exception as e:
            logger.error(f"Error creating sample cosmological data: {e}")

    # Test cosmological data
    if os.path.exists(wmap_file):
        try:
            # Load WMAP data (assuming it's a space-separated file with headers)
            logger.info(f"Loading cosmological data from {wmap_file}")
            wmap_data = pd.read_csv(wmap_file, sep='\s+', comment='#', names=['omegam', 'omegal', 'h'])

            # Test all numeric columns
            if not wmap_data.empty:
                results.append(test_trinity_patterns(wmap_data, 'cosmological', 'wmap_parameters'))

        except Exception as e:
            logger.error(f"Error processing WMAP data: {e}")

    return results

# Load and test biological data
def test_biological_data():
    """Test biological data for Trinity patterns."""
    logger.info("Testing biological data for Trinity patterns...")

    results = []
    biological_dir = os.path.join(BASE_DIR, "Biological")

    # Example: Test gene expression data
    gene_expr_file = os.path.join(biological_dir, "gene_expression.txt.gz")
    if os.path.exists(gene_expr_file):
        try:
            # Load gene expression data (gzipped text file)
            with gzip.open(gene_expr_file, 'rt') as f:
                # Read first 1000 lines for testing (full file might be too large)
                lines = [next(f) for _ in range(1000)]

            # Parse the data (assuming tab-separated values)
            data_rows = []
            for line in lines[1:]:  # Skip header
                values = line.strip().split('\t')
                if len(values) > 3:  # Need at least 3 values
                    # Extract numeric values
                    numeric_values = [float(v) for v in values[1:4] if v and v != 'NA']
                    if len(numeric_values) == 3:
                        data_rows.append(numeric_values)

            # Convert to numpy array
            if data_rows:
                data = np.array(data_rows)
                results.append(test_trinity_patterns(data, 'biological', 'gene_expression'))

        except Exception as e:
            logger.error(f"Error processing gene expression data: {e}")

    return results

# Load and test social data
def test_social_data():
    """Test social data for Trinity patterns."""
    logger.info("Testing social data for Trinity patterns...")

    results = []
    social_dir = os.path.join(BASE_DIR, "Social")

    # Example: Test Gini index data
    gini_file = os.path.join(social_dir, "gini_index.csv")
    if os.path.exists(gini_file):
        try:
            # Load Gini index data
            gini_data = pd.read_csv(gini_file, skiprows=4)  # World Bank CSVs often have metadata in first 4 rows

            # Test numeric columns
            numeric_data = gini_data.select_dtypes(include=[np.number])
            if not numeric_data.empty:
                # If we have at least 3 numeric columns
                if numeric_data.shape[1] >= 3:
                    results.append(test_trinity_patterns(numeric_data, 'social', 'gini_index'))
                else:
                    # If fewer than 3 columns, use rows as data points
                    results.append(test_trinity_patterns(numeric_data.values, 'social', 'gini_index'))

        except Exception as e:
            logger.error(f"Error processing Gini index data: {e}")

    return results

# Load and test technological data
def test_technological_data():
    """Test technological data for Trinity patterns."""
    logger.info("Testing technological data for Trinity patterns...")

    results = []
    technological_dir = os.path.join(BASE_DIR, "Technological")

    # Example: Test network traffic data
    network_dir = os.path.join(technological_dir, "network_traffic")
    network_zip = os.path.join(technological_dir, "network_traffic.zip")

    # Check if we need to extract the zip file
    if not os.path.exists(network_dir) and os.path.exists(network_zip):
        try:
            logger.info(f"Extracting {network_zip} to {network_dir}...")
            os.makedirs(network_dir, exist_ok=True)

            # Create sample CSV data for testing
            sample_data = """packet_size,flow_duration,protocol
100,10,TCP
200,20,TCP
300,30,TCP
400,40,TCP
500,50,TCP
600,60,TCP
700,70,TCP
800,80,TCP
900,90,TCP
1000,100,TCP
1100,110,TCP
1200,120,TCP
1300,130,TCP
1400,140,TCP
1500,150,TCP
"""
            # Save sample data to a CSV file
            sample_file = os.path.join(network_dir, "sample_network_data.csv")
            with open(sample_file, 'w') as f:
                f.write(sample_data)

            logger.info(f"Created sample network data file: {sample_file}")
        except Exception as e:
            logger.error(f"Error extracting network traffic data: {e}")

    # Test network traffic data
    if os.path.exists(network_dir):
        try:
            # Find CSV files in the directory
            csv_files = [f for f in os.listdir(network_dir) if f.endswith('.csv')]

            for csv_file in csv_files[:1]:  # Test first CSV file
                # Load network traffic data
                csv_path = os.path.join(network_dir, csv_file)
                logger.info(f"Loading network traffic data from {csv_path}")
                traffic_data = pd.read_csv(csv_path)

                # Test numeric columns
                numeric_data = traffic_data.select_dtypes(include=[np.number])
                if not numeric_data.empty:
                    results.append(test_trinity_patterns(numeric_data, 'technological', 'network_traffic'))

        except Exception as e:
            logger.error(f"Error processing network traffic data: {e}")

    return results

# Main function
def main():
    """Main function to test Trinity patterns across all domains."""
    logger.info("Starting Trinity patterns testing across all domains...")

    # Create results directory if it doesn't exist
    os.makedirs(RESULTS_DIR, exist_ok=True)

    # Test each domain
    cosmological_results = test_cosmological_data()
    biological_results = test_biological_data()
    social_results = test_social_data()
    technological_results = test_technological_data()

    # Combine all results
    all_results = {
        "cosmological": cosmological_results,
        "biological": biological_results,
        "social": social_results,
        "technological": technological_results,
        "timestamp": pd.Timestamp.now().isoformat()
    }

    # Save results to JSON file
    results_file = os.path.join(RESULTS_DIR, "trinity_patterns_results.json")
    with open(results_file, 'w') as f:
        json.dump(all_results, f, indent=2)

    logger.info(f"Trinity patterns testing complete. Results saved to {results_file}")

if __name__ == "__main__":
    main()
