/**
 * Mock Data Service for Cyber-Safety Visualizations
 * 
 * This service provides mock data for testing the Cyber-Safety fusion visualizations.
 * It includes both predefined mock data and functions to generate random data.
 */

// Mock data for Tri-Domain Tensor Visualization
const mockTriDomainTensorData = {
  grc: { 
    values: Array.from({ length: 10 }, () => Math.random()), 
    health: 0.8, 
    entropyContainment: 0.03 
  },
  it: { 
    values: Array.from({ length: 10 }, () => Math.random()), 
    health: 0.7, 
    entropyContainment: 0.05 
  },
  cybersecurity: { 
    values: Array.from({ length: 10 }, () => Math.random()), 
    health: 0.6, 
    entropyContainment: 0.08 
  },
  connections: [
    { source: 'grc', target: 'it', strength: 0.7 },
    { source: 'it', target: 'cybersecurity', strength: 0.8 },
    { source: 'cybersecurity', target: 'grc', strength: 0.5 },
    { source: 'grc', target: 'cybersecurity', strength: 0.3 }
  ]
};

// Mock data for Cyber-Safety Harmony Index
const mockHarmonyIndexData = {
  domainData: {
    grc: { 
      score: 0.7, 
      metrics: { 
        governance: 0.6, 
        risk: 0.7, 
        compliance: 0.8 
      } 
    },
    it: { 
      score: 0.8, 
      metrics: { 
        infrastructure: 0.8, 
        applications: 0.7, 
        data: 0.9 
      } 
    },
    cybersecurity: { 
      score: 0.6, 
      metrics: { 
        prevention: 0.5, 
        detection: 0.6, 
        response: 0.7 
      } 
    }
  },
  harmonyHistory: [0.65, 0.68, 0.72, 0.69, 0.71, 0.73, 0.72, 0.74, 0.73, 0.75]
};

// Mock data for Risk-Control Fusion Map
const mockRiskControlFusionMapData = {
  riskData: {
    grc: { 
      governance: 0.7, 
      risk: 0.5, 
      compliance: 0.8 
    },
    it: { 
      infrastructure: 0.6, 
      applications: 0.4, 
      data: 0.7 
    },
    cybersecurity: { 
      prevention: 0.8, 
      detection: 0.5, 
      response: 0.3 
    }
  },
  controlData: {
    grc: { 
      governance: 0.8, 
      risk: 0.6, 
      compliance: 0.9 
    },
    it: { 
      infrastructure: 0.7, 
      applications: 0.5, 
      data: 0.6 
    },
    cybersecurity: { 
      prevention: 0.7, 
      detection: 0.4, 
      response: 0.2 
    }
  }
};

// Mock data for Cyber-Safety Resonance Spectrogram
const mockResonanceSpectrogramData = {
  domainData: {
    grc: { 
      values: Array.from({ length: 10 }, () => Math.random()), 
      frequency: 0.3, 
      amplitude: 0.7, 
      phase: 0 
    },
    it: { 
      values: Array.from({ length: 10 }, () => Math.random()), 
      frequency: 0.6, 
      amplitude: 0.8, 
      phase: Math.PI / 3 
    },
    cybersecurity: { 
      values: Array.from({ length: 10 }, () => Math.random()), 
      frequency: 0.9, 
      amplitude: 0.6, 
      phase: Math.PI / 2 
    },
    crossDomainFlows: [
      { source: 'grc', target: 'it', strength: 0.7, frequency: 0.5 },
      { source: 'it', target: 'cybersecurity', strength: 0.8, frequency: 0.7 },
      { source: 'cybersecurity', target: 'grc', strength: 0.5, frequency: 0.4 }
    ]
  },
  predictionData: {
    timeHorizon: 10,
    dissonanceProbability: 0.2,
    criticalPoints: [
      { timeStep: 3, severity: 0.5, description: 'Potential dissonance between GRC and IT' },
      { timeStep: 7, severity: 0.7, description: 'Potential dissonance between IT and Cybersecurity' }
    ]
  }
};

// Mock data for Unified Compliance-Security Visualizer
const mockUnifiedComplianceSecurityVisualizerData = {
  complianceData: {
    requirements: [
      { id: 'req1', name: 'Data Protection', domain: 'grc', completeness: 0.8 },
      { id: 'req2', name: 'Access Control', domain: 'grc', completeness: 0.6 },
      { id: 'req3', name: 'Incident Response', domain: 'grc', completeness: 0.4 }
    ],
    controls: [
      { id: 'ctrl1', name: 'Encryption', domain: 'it', completeness: 0.9 },
      { id: 'ctrl2', name: 'Authentication', domain: 'it', completeness: 0.7 },
      { id: 'ctrl3', name: 'Monitoring', domain: 'cybersecurity', completeness: 0.5 },
      { id: 'ctrl4', name: 'Firewalls', domain: 'cybersecurity', completeness: 0.8 }
    ],
    implementations: [
      { id: 'impl1', name: 'AES-256', domain: 'it', completeness: 0.9 },
      { id: 'impl2', name: 'MFA', domain: 'it', completeness: 0.6 },
      { id: 'impl3', name: 'SIEM', domain: 'cybersecurity', completeness: 0.4 },
      { id: 'impl4', name: 'Next-Gen FW', domain: 'cybersecurity', completeness: 0.7 }
    ],
    links: [
      { source: 'req1', target: 'ctrl1', strength: 0.8, efficiency: 0.9 },
      { source: 'req1', target: 'ctrl3', strength: 0.4, efficiency: 0.5 },
      { source: 'req2', target: 'ctrl2', strength: 0.7, efficiency: 0.8 },
      { source: 'req2', target: 'ctrl4', strength: 0.5, efficiency: 0.6 },
      { source: 'req3', target: 'ctrl3', strength: 0.6, efficiency: 0.7 },
      { source: 'ctrl1', target: 'impl1', strength: 0.9, efficiency: 0.9 },
      { source: 'ctrl2', target: 'impl2', strength: 0.6, efficiency: 0.7 },
      { source: 'ctrl3', target: 'impl3', strength: 0.4, efficiency: 0.5 },
      { source: 'ctrl4', target: 'impl4', strength: 0.7, efficiency: 0.8 }
    ]
  },
  impactAnalysis: {
    proposedChanges: [
      { id: 'change1', target: 'ctrl2', impact: 0.7, description: 'Upgrade to biometric authentication' },
      { id: 'change2', target: 'impl3', impact: 0.5, description: 'Implement AI-based threat detection' }
    ]
  }
};

/**
 * Fetch mock data for a specific visualization type
 * @param {string} visualizationType - The type of visualization
 * @returns {Promise<Object>} - A promise that resolves to the mock data
 */
export const fetchMockData = async (visualizationType) => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));
  
  switch (visualizationType) {
    case 'tri_domain_tensor':
      return mockTriDomainTensorData;
      
    case 'cyber_safety_harmony_index':
      return mockHarmonyIndexData;
      
    case 'risk_control_fusion_map':
      return mockRiskControlFusionMapData;
      
    case 'cyber_safety_resonance_spectrogram':
      return mockResonanceSpectrogramData;
      
    case 'unified_compliance_security_visualizer':
      return mockUnifiedComplianceSecurityVisualizerData;
      
    default:
      throw new Error(`Unknown visualization type: ${visualizationType}`);
  }
};

/**
 * Generate random data for a specific visualization type
 * @param {string} visualizationType - The type of visualization
 * @param {Object} params - Parameters for random data generation
 * @returns {Object} - Random data for the visualization
 */
export const generateRandomData = (visualizationType, params = {}) => {
  const {
    domainCount = 3,
    connectionCount = 5,
    minHealth = 0.3,
    maxHealth = 0.9
  } = params;
  
  // Helper function to generate a random number between min and max
  const random = (min, max) => Math.random() * (max - min) + min;
  
  // Helper function to generate random domain data
  const generateDomains = (count) => {
    const domains = ['grc', 'it', 'cybersecurity'];
    const additionalDomains = ['medical', 'financial', 'legal', 'operational', 'physical'];
    
    // Ensure we have enough domains
    const allDomains = [...domains, ...additionalDomains];
    const selectedDomains = allDomains.slice(0, count);
    
    return selectedDomains;
  };
  
  // Helper function to generate random connections
  const generateConnections = (domains, count) => {
    const connections = [];
    
    for (let i = 0; i < count; i++) {
      const sourceIndex = Math.floor(Math.random() * domains.length);
      let targetIndex;
      
      do {
        targetIndex = Math.floor(Math.random() * domains.length);
      } while (targetIndex === sourceIndex);
      
      connections.push({
        source: domains[sourceIndex],
        target: domains[targetIndex],
        strength: random(0.3, 0.9),
        frequency: random(0.3, 0.9)
      });
    }
    
    return connections;
  };
  
  switch (visualizationType) {
    case 'tri_domain_tensor': {
      const domains = generateDomains(domainCount);
      const result = {};
      
      domains.forEach(domain => {
        result[domain] = {
          values: Array.from({ length: 10 }, () => Math.random()),
          health: random(minHealth, maxHealth),
          entropyContainment: random(0.01, 0.1)
        };
      });
      
      result.connections = generateConnections(domains, connectionCount);
      
      return result;
    }
    
    case 'cyber_safety_harmony_index': {
      const domains = generateDomains(domainCount);
      const domainData = {};
      
      domains.forEach(domain => {
        const metrics = {};
        
        // Generate 3 random metrics for each domain
        for (let i = 0; i < 3; i++) {
          const metricNames = {
            grc: ['governance', 'risk', 'compliance'],
            it: ['infrastructure', 'applications', 'data'],
            cybersecurity: ['prevention', 'detection', 'response'],
            medical: ['patient', 'clinical', 'research'],
            financial: ['accounting', 'investment', 'audit'],
            legal: ['contracts', 'compliance', 'litigation'],
            operational: ['logistics', 'production', 'quality'],
            physical: ['facilities', 'security', 'safety']
          };
          
          const metricName = metricNames[domain]?.[i] || `metric${i + 1}`;
          metrics[metricName] = random(minHealth, maxHealth);
        }
        
        domainData[domain] = {
          score: random(minHealth, maxHealth),
          metrics
        };
      });
      
      // Generate random harmony history
      const historyLength = 10;
      const harmonyHistory = Array.from({ length: historyLength }, () => random(minHealth, maxHealth));
      
      return {
        domainData,
        harmonyHistory
      };
    }
    
    case 'risk_control_fusion_map': {
      const domains = generateDomains(domainCount);
      const riskData = {};
      const controlData = {};
      
      domains.forEach(domain => {
        riskData[domain] = {};
        controlData[domain] = {};
        
        // Generate 3 random categories for each domain
        for (let i = 0; i < 3; i++) {
          const categoryNames = {
            grc: ['governance', 'risk', 'compliance'],
            it: ['infrastructure', 'applications', 'data'],
            cybersecurity: ['prevention', 'detection', 'response'],
            medical: ['patient', 'clinical', 'research'],
            financial: ['accounting', 'investment', 'audit'],
            legal: ['contracts', 'compliance', 'litigation'],
            operational: ['logistics', 'production', 'quality'],
            physical: ['facilities', 'security', 'safety']
          };
          
          const categoryName = categoryNames[domain]?.[i] || `category${i + 1}`;
          riskData[domain][categoryName] = random(minHealth, maxHealth);
          controlData[domain][categoryName] = random(minHealth, maxHealth);
        }
      });
      
      return {
        riskData,
        controlData
      };
    }
    
    case 'cyber_safety_resonance_spectrogram': {
      const domains = generateDomains(domainCount);
      const domainData = {};
      
      domains.forEach(domain => {
        domainData[domain] = {
          values: Array.from({ length: 10 }, () => Math.random()),
          frequency: random(0.3, 0.9),
          amplitude: random(0.5, 0.9),
          phase: random(0, Math.PI)
        };
      });
      
      domainData.crossDomainFlows = generateConnections(domains, connectionCount);
      
      // Generate random prediction data
      const timeHorizon = 10;
      const dissonanceProbability = random(0.1, 0.5);
      const criticalPointCount = Math.floor(random(1, 4));
      
      const criticalPoints = Array.from({ length: criticalPointCount }, () => {
        const timeStep = Math.floor(random(1, timeHorizon));
        const severity = random(0.3, 0.9);
        
        // Generate a random description
        const sourceIndex = Math.floor(Math.random() * domains.length);
        let targetIndex;
        
        do {
          targetIndex = Math.floor(Math.random() * domains.length);
        } while (targetIndex === sourceIndex);
        
        const description = `Potential dissonance between ${domains[sourceIndex].toUpperCase()} and ${domains[targetIndex].toUpperCase()}`;
        
        return {
          timeStep,
          severity,
          description
        };
      });
      
      return {
        domainData,
        predictionData: {
          timeHorizon,
          dissonanceProbability,
          criticalPoints
        }
      };
    }
    
    case 'unified_compliance_security_visualizer': {
      const domains = generateDomains(domainCount);
      
      // Generate requirements
      const requirementCount = Math.floor(random(3, 6));
      const requirements = Array.from({ length: requirementCount }, (_, i) => {
        const domain = domains[Math.floor(Math.random() * domains.length)];
        return {
          id: `req${i + 1}`,
          name: `Requirement ${i + 1}`,
          domain,
          completeness: random(minHealth, maxHealth)
        };
      });
      
      // Generate controls
      const controlCount = Math.floor(random(4, 8));
      const controls = Array.from({ length: controlCount }, (_, i) => {
        const domain = domains[Math.floor(Math.random() * domains.length)];
        return {
          id: `ctrl${i + 1}`,
          name: `Control ${i + 1}`,
          domain,
          completeness: random(minHealth, maxHealth)
        };
      });
      
      // Generate implementations
      const implementationCount = Math.floor(random(4, 8));
      const implementations = Array.from({ length: implementationCount }, (_, i) => {
        const domain = domains[Math.floor(Math.random() * domains.length)];
        return {
          id: `impl${i + 1}`,
          name: `Implementation ${i + 1}`,
          domain,
          completeness: random(minHealth, maxHealth)
        };
      });
      
      // Generate links
      const links = [];
      
      // Links from requirements to controls
      requirements.forEach(req => {
        const linkCount = Math.floor(random(1, 3));
        const availableControls = [...controls];
        
        for (let i = 0; i < linkCount && availableControls.length > 0; i++) {
          const controlIndex = Math.floor(Math.random() * availableControls.length);
          const control = availableControls.splice(controlIndex, 1)[0];
          
          links.push({
            source: req.id,
            target: control.id,
            strength: random(0.3, 0.9),
            efficiency: random(0.3, 0.9)
          });
        }
      });
      
      // Links from controls to implementations
      controls.forEach(ctrl => {
        const linkCount = Math.floor(random(1, 2));
        const availableImplementations = [...implementations];
        
        for (let i = 0; i < linkCount && availableImplementations.length > 0; i++) {
          const implIndex = Math.floor(Math.random() * availableImplementations.length);
          const impl = availableImplementations.splice(implIndex, 1)[0];
          
          links.push({
            source: ctrl.id,
            target: impl.id,
            strength: random(0.3, 0.9),
            efficiency: random(0.3, 0.9)
          });
        }
      });
      
      // Generate proposed changes
      const changeCount = Math.floor(random(1, 3));
      const proposedChanges = Array.from({ length: changeCount }, (_, i) => {
        const targetType = Math.random() > 0.5 ? 'control' : 'implementation';
        const targetArray = targetType === 'control' ? controls : implementations;
        const target = targetArray[Math.floor(Math.random() * targetArray.length)].id;
        
        return {
          id: `change${i + 1}`,
          target,
          impact: random(0.3, 0.9),
          description: `Proposed change ${i + 1} for ${targetType} ${target}`
        };
      });
      
      return {
        complianceData: {
          requirements,
          controls,
          implementations,
          links
        },
        impactAnalysis: {
          proposedChanges
        }
      };
    }
    
    default:
      throw new Error(`Unknown visualization type: ${visualizationType}`);
  }
};
