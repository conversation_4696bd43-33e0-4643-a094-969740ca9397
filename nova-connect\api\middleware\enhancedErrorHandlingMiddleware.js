/**
 * Enhanced Error Handling Middleware
 * 
 * This middleware provides comprehensive error handling for the NovaConnect API:
 * - Consistent error response format
 * - Detailed error information in development mode
 * - Sanitized error information in production mode
 * - Comprehensive error logging
 * - Support for various error types
 * - Request validation errors
 * - Correlation IDs for error tracking
 */

const { v4: uuidv4 } = require('uuid');
const logger = require('../../config/logger');
const { 
  ValidationError, 
  AuthenticationError, 
  AuthorizationError, 
  NotFoundError, 
  ConflictError,
  RateLimitError,
  TimeoutError
} = require('../utils/errors');

/**
 * Error handler middleware
 * 
 * @param {Error} err - Error object
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const errorHandler = (err, req, res, next) => {
  // Generate correlation ID if not already present
  const correlationId = req.correlationId || uuidv4();
  
  // Create error context
  const context = {
    correlationId,
    user: req.user ? { id: req.user.id, email: req.user.email } : null,
    path: req.path,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    resource: req.originalUrl,
    retryCount: req.retryCount || 0,
    tags: {
      route: req.route ? req.route.path : 'unknown',
      controller: req.controller || 'unknown',
      action: req.action || 'unknown'
    }
  };
  
  // Determine error type and status code
  let statusCode = 500;
  let errorType = 'Internal Server Error';
  let errorCode = 'INTERNAL_ERROR';
  let errorDetails = {};
  
  // Handle specific error types
  if (err instanceof ValidationError) {
    statusCode = 400;
    errorType = 'Validation Error';
    errorCode = err.code || 'VALIDATION_ERROR';
    errorDetails = err.details || {};
  } else if (err instanceof AuthenticationError) {
    statusCode = 401;
    errorType = 'Authentication Error';
    errorCode = err.code || 'AUTHENTICATION_ERROR';
  } else if (err instanceof AuthorizationError) {
    statusCode = 403;
    errorType = 'Authorization Error';
    errorCode = err.code || 'AUTHORIZATION_ERROR';
  } else if (err instanceof NotFoundError) {
    statusCode = 404;
    errorType = 'Not Found Error';
    errorCode = err.code || 'NOT_FOUND_ERROR';
  } else if (err instanceof ConflictError) {
    statusCode = 409;
    errorType = 'Conflict Error';
    errorCode = err.code || 'CONFLICT_ERROR';
  } else if (err instanceof RateLimitError) {
    statusCode = 429;
    errorType = 'Rate Limit Error';
    errorCode = err.code || 'RATE_LIMIT_ERROR';
  } else if (err instanceof TimeoutError) {
    statusCode = 504;
    errorType = 'Timeout Error';
    errorCode = err.code || 'TIMEOUT_ERROR';
  } else if (err.name === 'JsonWebTokenError') {
    statusCode = 401;
    errorType = 'Authentication Error';
    errorCode = 'INVALID_TOKEN';
  } else if (err.name === 'TokenExpiredError') {
    statusCode = 401;
    errorType = 'Authentication Error';
    errorCode = 'TOKEN_EXPIRED';
  } else if (err.name === 'SyntaxError' && err.message.includes('JSON')) {
    statusCode = 400;
    errorType = 'Bad Request';
    errorCode = 'INVALID_JSON';
  } else if (err.name === 'MongoError' && err.code === 11000) {
    statusCode = 409;
    errorType = 'Conflict Error';
    errorCode = 'DUPLICATE_KEY';
    errorDetails = { key: Object.keys(err.keyPattern)[0] };
  }
  
  // Create error response
  const errorResponse = {
    error: {
      type: errorType,
      message: err.message,
      code: errorCode,
      status: statusCode,
      correlationId
    }
  };
  
  // Add details in development mode or if explicitly provided
  if (process.env.NODE_ENV !== 'production' || Object.keys(errorDetails).length > 0) {
    errorResponse.error.details = errorDetails;
  }
  
  // Add stack trace in development mode
  if (process.env.NODE_ENV !== 'production') {
    errorResponse.error.stack = err.stack;
  }
  
  // Log error with appropriate level
  if (statusCode >= 500) {
    logger.error(`${errorType}: ${err.message}`, {
      error: {
        message: err.message,
        name: err.name,
        code: errorCode,
        stack: err.stack
      },
      context
    });
  } else if (statusCode >= 400) {
    logger.warn(`${errorType}: ${err.message}`, {
      error: {
        message: err.message,
        name: err.name,
        code: errorCode
      },
      context
    });
  }
  
  // Set correlation ID header
  res.set('X-Correlation-ID', correlationId);
  
  // Send error response
  res.status(statusCode).json(errorResponse);
};

/**
 * Not found handler middleware
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const notFoundHandler = (req, res, next) => {
  const notFoundError = new NotFoundError(`Resource not found: ${req.originalUrl}`);
  notFoundError.code = 'RESOURCE_NOT_FOUND';
  next(notFoundError);
};

/**
 * Async handler middleware
 * 
 * @param {Function} fn - Async function
 * @returns {Function} - Express middleware
 */
const asyncHandler = (fn) => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * Correlation ID middleware
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const correlationIdMiddleware = (req, res, next) => {
  // Get correlation ID from header or generate a new one
  const correlationId = req.get('X-Correlation-ID') || uuidv4();
  
  // Set correlation ID on request
  req.correlationId = correlationId;
  
  // Set correlation ID header
  res.set('X-Correlation-ID', correlationId);
  
  next();
};

/**
 * Request validation middleware
 * 
 * @param {Object} schema - Joi schema
 * @param {string} property - Request property to validate (body, query, params)
 * @returns {Function} - Express middleware
 */
const validateRequest = (schema, property = 'body') => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req[property], { abortEarly: false });
    
    if (error) {
      // Extract validation details
      const details = error.details.map(detail => ({
        path: detail.path.join('.'),
        message: detail.message,
        type: detail.type
      }));
      
      // Create validation error
      const validationError = new ValidationError('Validation failed', details);
      validationError.code = 'VALIDATION_ERROR';
      
      return next(validationError);
    }
    
    // Update request with validated value
    req[property] = value;
    next();
  };
};

/**
 * Retry handler middleware
 * 
 * @param {Object} options - Retry options
 * @param {number} options.maxRetries - Maximum number of retries
 * @param {number} options.retryDelay - Delay between retries in milliseconds
 * @param {Function} options.shouldRetry - Function to determine if request should be retried
 * @returns {Function} - Express middleware
 */
const retryHandler = (options = {}) => {
  const maxRetries = options.maxRetries || 3;
  const retryDelay = options.retryDelay || 1000;
  const shouldRetry = options.shouldRetry || ((err) => err instanceof TimeoutError);
  
  return (err, req, res, next) => {
    // Check if request should be retried
    if (shouldRetry(err) && (!req.retryCount || req.retryCount < maxRetries)) {
      // Increment retry count
      req.retryCount = (req.retryCount || 0) + 1;
      
      // Log retry attempt
      logger.info(`Retrying request (${req.retryCount}/${maxRetries})`, {
        path: req.path,
        method: req.method,
        retryCount: req.retryCount,
        error: err.message
      });
      
      // Retry after delay
      setTimeout(() => {
        // Re-execute route handler
        const route = req.route;
        const method = req.method.toLowerCase();
        const handlers = route.methods[method] ? route.stack.filter(layer => layer.method === method) : [];
        
        if (handlers.length > 0) {
          // Execute handler
          handlers[0].handle(req, res, next);
        } else {
          // No handler found, pass error to next middleware
          next(err);
        }
      }, retryDelay * req.retryCount); // Exponential backoff
    } else {
      // Max retries reached or error should not be retried
      next(err);
    }
  };
};

/**
 * Timeout middleware
 * 
 * @param {number} timeout - Timeout in milliseconds
 * @returns {Function} - Express middleware
 */
const timeoutMiddleware = (timeout = 30000) => {
  return (req, res, next) => {
    // Set timeout
    const timeoutId = setTimeout(() => {
      // Create timeout error
      const timeoutError = new TimeoutError(`Request timeout after ${timeout}ms`);
      timeoutError.code = 'REQUEST_TIMEOUT';
      
      next(timeoutError);
    }, timeout);
    
    // Clear timeout when response is sent
    res.on('finish', () => {
      clearTimeout(timeoutId);
    });
    
    next();
  };
};

module.exports = {
  errorHandler,
  notFoundHandler,
  asyncHandler,
  correlationIdMiddleware,
  validateRequest,
  retryHandler,
  timeoutMiddleware
};
