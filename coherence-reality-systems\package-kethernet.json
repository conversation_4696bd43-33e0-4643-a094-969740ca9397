{"name": "kethernet-server", "version": "1.0.0", "description": "KetherNet Blockchain Server with Coherium and Aetherium support", "main": "kether-server-enhanced.js", "scripts": {"start": "node kether-server-enhanced.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"express": "4.18.2", "cors": "2.8.5", "bn.js": "5.2.1", "express-prometheus-middleware": "1.2.0", "prom-client": "14.2.0"}, "engines": {"node": ">=16.0.0"}, "keywords": ["blockchain", "kethernet", "coherium", "aetherium"], "author": "Coherence Reality Systems", "license": "MIT"}