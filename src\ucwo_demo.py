"""
Demo script for the Universal Compliance Workflow Orchestrator (UCWO).

This script demonstrates how to use the UCWO to create, manage, and execute
compliance workflows.
"""

import os
import sys
import json
import logging
import time
from typing import Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add the src directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Import the UCWO
from ucwo import WorkflowEngine, WorkflowManager, TaskManager, EventHandler
from ucwo.utils.workflow_utils import create_workflow_diagram

def custom_event_handler(event_data: Dict[str, Any]) -> None:
    """
    Custom event handler for workflow events.
    
    Args:
        event_data: Data associated with the event
    """
    logger.info(f"Custom event handler: {event_data}")

def main():
    """Run the UCWO demo."""
    logger.info("Starting UCWO demo")
    
    # Initialize the Workflow Engine
    engine = WorkflowEngine()
    
    # Register a custom event handler
    engine.register_event_handler('workflow_completed', custom_event_handler)
    
    # Get available workflows
    workflow_manager = engine.workflow_manager
    available_workflows = workflow_manager.get_all_workflows()
    
    logger.info(f"Available workflows: {[wf['id'] for wf in available_workflows]}")
    
    # Create output directory if it doesn't exist
    output_dir = os.path.join(os.path.dirname(__file__), 'ucwo_output')
    os.makedirs(output_dir, exist_ok=True)
    
    # Create workflow diagrams
    for workflow in available_workflows:
        workflow_id = workflow['id']
        diagram_path = os.path.join(output_dir, f"{workflow_id}_diagram.txt")
        diagram = create_workflow_diagram(workflow, diagram_path)
        logger.info(f"Created workflow diagram for {workflow_id}")
    
    # Execute a GDPR Data Subject Request workflow
    logger.info("Executing GDPR Data Subject Request workflow")
    
    dsr_input = {
        'request_type': 'access',
        'data_subject_name': 'Jane Doe',
        'data_subject_email': '<EMAIL>',
        'request_details': 'I would like to access all my personal data.'
    }
    
    dsr_instance_id = engine.start_workflow('gdpr_dsr', dsr_input)
    
    logger.info(f"Started GDPR DSR workflow, instance ID: {dsr_instance_id}")
    
    # Wait for the workflow to complete
    max_wait_time = 10  # seconds
    wait_interval = 0.5  # seconds
    waited_time = 0
    
    while waited_time < max_wait_time:
        workflow_status = engine.get_workflow_status(dsr_instance_id)
        if workflow_status['status'] in ['completed', 'failed', 'cancelled']:
            break
        
        time.sleep(wait_interval)
        waited_time += wait_interval
    
    # Get the final workflow status
    workflow_status = engine.get_workflow_status(dsr_instance_id)
    logger.info(f"Final workflow status: {workflow_status['status']}")
    
    if workflow_status['status'] == 'completed':
        logger.info(f"Workflow output: {workflow_status['output_data']}")
    
    # Execute an Incident Response workflow
    logger.info("Executing Incident Response workflow")
    
    incident_input = {
        'incident_type': 'unauthorized_access',
        'incident_description': 'Unauthorized access to customer database detected.',
        'detected_by': 'Security Monitoring System',
        'detection_time': '2023-06-15T08:30:00Z'
    }
    
    incident_instance_id = engine.start_workflow('incident_response', incident_input)
    
    logger.info(f"Started Incident Response workflow, instance ID: {incident_instance_id}")
    
    # Wait for the workflow to complete
    waited_time = 0
    
    while waited_time < max_wait_time:
        workflow_status = engine.get_workflow_status(incident_instance_id)
        if workflow_status['status'] in ['completed', 'failed', 'cancelled']:
            break
        
        time.sleep(wait_interval)
        waited_time += wait_interval
    
    # Get the final workflow status
    workflow_status = engine.get_workflow_status(incident_instance_id)
    logger.info(f"Final workflow status: {workflow_status['status']}")
    
    if workflow_status['status'] == 'completed':
        logger.info(f"Workflow output: {workflow_status['output_data']}")
    
    # Save workflow instances to files
    dsr_status = engine.get_workflow_status(dsr_instance_id)
    incident_status = engine.get_workflow_status(incident_instance_id)
    
    dsr_output_path = os.path.join(output_dir, f"gdpr_dsr_instance_{dsr_instance_id}.json")
    incident_output_path = os.path.join(output_dir, f"incident_response_instance_{incident_instance_id}.json")
    
    with open(dsr_output_path, 'w', encoding='utf-8') as f:
        json.dump(dsr_status, f, indent=2)
    
    with open(incident_output_path, 'w', encoding='utf-8') as f:
        json.dump(incident_status, f, indent=2)
    
    logger.info(f"Saved workflow instances to {output_dir}")
    
    logger.info("UCWO demo completed successfully")

if __name__ == "__main__":
    main()
