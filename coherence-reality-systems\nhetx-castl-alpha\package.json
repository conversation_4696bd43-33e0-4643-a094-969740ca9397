{"name": "alpha-observer-class-calibration", "version": "1.0.0-FULL_CALIBRATION", "description": "ALPHA Observer-Class Coherence Engine - Full Calibration Mode", "main": "alpha-calibration-executor.js", "scripts": {"start": "node alpha-calibration-executor.js", "calibrate": "node alpha-calibration-executor.js", "dashboard": "node alpha-calibration-dashboard.js", "alpha": "node ALPHA-OBSERVER-CLASS-ENGINE.js", "test": "echo \"ALPHA Calibration System Test\" && node alpha-calibration-executor.js", "docker:build": "docker build -t alpha-calibration .", "docker:run": "docker run -p 3000:3000 alpha-calibration", "docker:dev": "docker run -it -p 3000:3000 -v $(pwd):/app/alpha-calibration alpha-calibration sh"}, "keywords": ["alpha", "coherence", "calibration", "observer-class", "nefc", "nhetx", "kappa-field", "aeonix"], "author": "<PERSON> <<EMAIL>>", "license": "PROPRIETARY", "engines": {"node": ">=16.0.0"}, "dependencies": {"@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "axios": "^1.9.0", "cors": "^2.8.5", "express": "^4.18.2", "moment": "^2.30.1", "ws": "^8.14.2"}, "devDependencies": {"nodemon": "^3.0.1"}, "calibration": {"mode": "FULL_CALIBRATION", "priorities": ["NEFC_FINANCIAL_AUTOPILOT", "NHETX_CASTL_CONSCIOUSNESS_TIME", "KAPPA_FIELD_GENERATOR"], "targets": {"nefc_win_rate": ">=95%", "nhetx_c_score": ">=97%", "kappa_field_lift": ">=10mm"}, "update_interval": "72h", "max_cycles": 10}, "repository": {"type": "git", "url": "coherence-reality-systems"}, "bugs": {"url": "mailto:<EMAIL>"}, "homepage": "coherence-reality-systems#readme"}