/**
 * Card Component
 * 
 * A container component for related content that follows the NovaConnect design system.
 */

import React from 'react';
import PropTypes from 'prop-types';
import './Card.css';

/**
 * Card component
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} [props.header] - Card header content
 * @param {React.ReactNode} [props.footer] - Card footer content
 * @param {boolean} [props.elevated=false] - Whether the card has elevation
 * @param {boolean} [props.bordered=true] - Whether the card has a border
 * @param {React.ReactNode} props.children - Card content
 * @param {string} [props.className] - Additional CSS class
 * @returns {React.ReactElement} Card component
 */
const Card = ({
  header,
  footer,
  elevated = false,
  bordered = true,
  children,
  className = '',
  ...rest
}) => {
  const baseClass = 'nova-card';
  const elevatedClass = elevated ? `${baseClass}--elevated` : '';
  const borderedClass = bordered ? `${baseClass}--bordered` : '';
  
  const cardClasses = [
    baseClass,
    elevatedClass,
    borderedClass,
    className
  ].filter(Boolean).join(' ');
  
  return (
    <div className={cardClasses} {...rest}>
      {header && <div className={`${baseClass}__header`}>{header}</div>}
      <div className={`${baseClass}__body`}>{children}</div>
      {footer && <div className={`${baseClass}__footer`}>{footer}</div>}
    </div>
  );
};

Card.propTypes = {
  header: PropTypes.node,
  footer: PropTypes.node,
  elevated: PropTypes.bool,
  bordered: PropTypes.bool,
  children: PropTypes.node.isRequired,
  className: PropTypes.string
};

export default Card;
