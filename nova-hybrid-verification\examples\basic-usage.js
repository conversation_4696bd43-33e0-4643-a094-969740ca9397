/**
 * Basic Usage Example for the Hybrid DAG-based Zero-Knowledge System
 * 
 * This example demonstrates how to use the system for basic transaction
 * processing and proof verification.
 */

// Import the DAGSystem
const { DAGSystem } = require('../src');

// Enable debug logging
process.env.DEBUG = 'nova:*';

// Create a new DAG System
const system = new DAGSystem({
  enableLogging: true,
  enableMetrics: true
});

// Example transaction data
const transactionData = {
  action: 'compliance_check',
  regulation: 'GDPR',
  entity: 'user_123',
  timestamp: Date.now(),
  details: {
    dataAccessed: ['name', 'email'],
    purpose: 'account_verification',
    consentObtained: true
  }
};

// Process a transaction
async function processTransaction() {
  console.log('Creating and processing a transaction...');
  
  // Create a transaction
  const transaction = system.createTransaction({
    data: transactionData,
    type: 'compliance',
    metadata: {
      priority: 'high',
      source: 'api_gateway'
    }
  });
  
  console.log(`Transaction created with ID: ${transaction.id}`);
  
  // Process the transaction
  const result = await system.processTransaction(transaction);
  
  console.log('Transaction processed:');
  console.log(`- Transaction ID: ${result.transactionId}`);
  console.log(`- Status: ${result.status}`);
  console.log(`- Processing Time: ${result.processingTime}ms`);
  
  if (result.proof) {
    console.log(`- Proof ID: ${result.proof.proofId}`);
    console.log(`- Proof Type: ${result.proof.proofType}`);
  }
  
  return result;
}

// Verify a proof
async function verifyProof(proof) {
  console.log(`\nVerifying proof: ${proof.proofId}...`);
  
  // Verify the proof
  const result = await system.verifyProof(proof);
  
  console.log('Proof verification result:');
  console.log(`- Proof ID: ${result.proofId}`);
  console.log(`- Status: ${result.status}`);
  console.log(`- Verified: ${result.verified}`);
  console.log(`- Verification Time: ${result.verificationTime}ms`);
  
  return result;
}

// Process a batch of transactions
async function processBatch(count) {
  console.log(`\nProcessing a batch of ${count} transactions...`);
  
  const transactions = [];
  const results = [];
  
  // Create and process transactions
  for (let i = 0; i < count; i++) {
    const transaction = system.createTransaction({
      data: {
        ...transactionData,
        batchIndex: i,
        entity: `user_${1000 + i}`
      },
      type: 'compliance_batch',
      metadata: {
        batchId: `batch_${Date.now()}`,
        index: i
      }
    });
    
    transactions.push(transaction);
    
    const result = await system.processTransaction(transaction);
    results.push(result);
  }
  
  console.log(`Processed ${results.length} transactions`);
  
  return results;
}

// Display system metrics
function displayMetrics() {
  console.log('\nSystem Metrics:');
  
  const metrics = system.getMetrics();
  
  console.log(`- Uptime: ${Math.round(metrics.uptime / 1000)} seconds`);
  console.log('- Transactions:');
  console.log(`  - Total: ${metrics.transactions.total}`);
  console.log(`  - Processed: ${metrics.transactions.processed}`);
  console.log(`  - Failed: ${metrics.transactions.failed}`);
  console.log('- Proofs:');
  console.log(`  - Total: ${metrics.proofs.total}`);
  console.log(`  - Verified: ${metrics.proofs.verified}`);
  console.log(`  - Failed: ${metrics.proofs.failed}`);
  console.log('- Performance:');
  console.log(`  - Avg Transaction Time: ${Math.round(metrics.performance.averageTransactionTime)}ms`);
  console.log(`  - Avg Verification Time: ${Math.round(metrics.performance.averageVerificationTime)}ms`);
  
  return metrics;
}

// Run the example
async function runExample() {
  try {
    // Process a single transaction
    const result = await processTransaction();
    
    // Verify the proof
    if (result.proof) {
      await verifyProof(result.proof);
    }
    
    // Process a batch of transactions
    await processBatch(5);
    
    // Display metrics
    displayMetrics();
    
    console.log('\nExample completed successfully!');
  } catch (error) {
    console.error('Error running example:', error);
  }
}

// Run the example if this file is executed directly
if (require.main === module) {
  runExample();
}

module.exports = {
  processTransaction,
  verifyProof,
  processBatch,
  displayMetrics,
  runExample
};
