import React from 'react';
import DiagramTemplate from '../DiagramTemplate';

// Define the diagram data
const elements = [
  {
    id: 'uuft-equation',
    top: 50,
    left: 300,
    width: 300,
    text: 'UUFT Equation: (A⊗B⊕C)×π10³',
    number: '1',
    bold: true,
    fontSize: '20px',
    backgroundColor: '#fff0f6'
  },
  // Input tensors
  {
    id: 'tensor-a',
    top: 150,
    left: 100,
    width: 150,
    text: 'Tensor A\nDomain-specific input',
    number: '2',
    fontSize: '14px',
    backgroundColor: '#e6f7ff'
  },
  {
    id: 'tensor-b',
    top: 150,
    left: 300,
    width: 150,
    text: 'Tensor B\nMetadata input',
    number: '3',
    fontSize: '14px',
    backgroundColor: '#e6f7ff'
  },
  {
    id: 'tensor-c',
    top: 150,
    left: 500,
    width: 150,
    text: 'Tensor C\nContext information',
    number: '4',
    fontSize: '14px',
    backgroundColor: '#e6f7ff'
  },
  {
    id: 'pi-factor',
    top: 150,
    left: 700,
    width: 150,
    text: 'π10³ Factor\nCircular trust topology',
    number: '5',
    fontSize: '14px',
    backgroundColor: '#e6f7ff'
  },
  // Operations
  {
    id: 'tensor-product',
    top: 250,
    left: 200,
    width: 150,
    text: 'Tensor Product (⊗)\nMulti-dimensional relationships',
    number: '6',
    fontSize: '14px',
    backgroundColor: '#f6ffed'
  },
  {
    id: 'fusion-operator',
    top: 250,
    left: 400,
    width: 150,
    text: 'Fusion Operator (⊕)\nMerges related data points',
    number: '7',
    fontSize: '14px',
    backgroundColor: '#f6ffed'
  },
  {
    id: 'scaling-operation',
    top: 250,
    left: 600,
    width: 150,
    text: 'Scaling Operation (×)\nApplies trust topology factor',
    number: '8',
    fontSize: '14px',
    backgroundColor: '#f6ffed'
  },
  // Implementation
  {
    id: 'tpu',
    top: 350,
    left: 200,
    width: 150,
    text: 'Tensor Processing Units (TPUs)',
    number: '9',
    fontSize: '12px',
    backgroundColor: '#fff2e8'
  },
  {
    id: 'fpe',
    top: 350,
    left: 400,
    width: 150,
    text: 'Fusion Processing Engines (FPEs)',
    number: '10',
    fontSize: '12px',
    backgroundColor: '#fff2e8'
  },
  {
    id: 'scaling-circuits',
    top: 350,
    left: 600,
    width: 150,
    text: 'Scaling Circuits',
    number: '11',
    fontSize: '12px',
    backgroundColor: '#fff2e8'
  },
  // Result
  {
    id: 'result',
    top: 450,
    left: 400,
    width: 150,
    text: 'Result\n3,142x improvement\n95% accuracy',
    number: '12',
    bold: true,
    fontSize: '14px',
    backgroundColor: '#fffbe6'
  },
  // Implementation
  {
    id: 'implementation',
    top: 550,
    left: 300,
    width: 300,
    text: 'Technical Implementation: Tensor-Fusion Architecture',
    number: '13',
    bold: true,
    fontSize: '16px',
    backgroundColor: '#f9f0ff'
  }
];

const connections = [
  // Connect UUFT Equation to inputs
  {
    start: { x: 300, y: 100 },
    end: { x: 175, y: 150 },
    type: 'arrow'
  },
  {
    start: { x: 400, y: 100 },
    end: { x: 375, y: 150 },
    type: 'arrow'
  },
  {
    start: { x: 500, y: 100 },
    end: { x: 575, y: 150 },
    type: 'arrow'
  },
  {
    start: { x: 600, y: 100 },
    end: { x: 775, y: 150 },
    type: 'arrow'
  },
  // Connect inputs to operations
  {
    start: { x: 175, y: 200 },
    end: { x: 200, y: 250 },
    type: 'arrow'
  },
  {
    start: { x: 375, y: 200 },
    end: { x: 250, y: 250 },
    type: 'arrow'
  },
  {
    start: { x: 575, y: 200 },
    end: { x: 400, y: 250 },
    type: 'arrow'
  },
  {
    start: { x: 775, y: 200 },
    end: { x: 600, y: 250 },
    type: 'arrow'
  },
  // Connect operations to next step
  {
    start: { x: 275, y: 300 },
    end: { x: 400, y: 250 },
    type: 'arrow'
  },
  {
    start: { x: 475, y: 300 },
    end: { x: 600, y: 250 },
    type: 'arrow'
  },
  // Connect operations to implementation
  {
    start: { x: 275, y: 300 },
    end: { x: 275, y: 350 },
    type: 'line'
  },
  {
    start: { x: 475, y: 300 },
    end: { x: 475, y: 350 },
    type: 'line'
  },
  {
    start: { x: 675, y: 300 },
    end: { x: 675, y: 350 },
    type: 'line'
  },
  // Connect to result
  {
    start: { x: 275, y: 400 },
    end: { x: 400, y: 450 },
    type: 'arrow'
  },
  {
    start: { x: 475, y: 400 },
    end: { x: 475, y: 450 },
    type: 'arrow'
  },
  {
    start: { x: 675, y: 400 },
    end: { x: 550, y: 450 },
    type: 'arrow'
  },
  // Connect to implementation
  {
    start: { x: 475, y: 500 },
    end: { x: 450, y: 550 },
    type: 'arrow'
  }
];

const UUFTEquationFlow: React.FC = () => {
  return (
    <DiagramTemplate 
      elements={elements} 
      connections={connections} 
      width="900px" 
      height="650px" 
    />
  );
};

export default UUFTEquationFlow;
