/**
 * NovaCore Framework Model
 * 
 * This model defines the schema for compliance frameworks in the NovaPulse module.
 * NovaPulse is the Universal Regulatory Compliance Monitoring System (URCMS) component
 * of the NovaFuse Cyber-Safety Platform.
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

// Define control schema
const controlSchema = new Schema({
  id: { 
    type: String, 
    required: true, 
    trim: true 
  },
  name: { 
    type: String, 
    required: true, 
    trim: true 
  },
  description: { 
    type: String, 
    required: true 
  },
  objective: { 
    type: String 
  },
  category: { 
    type: String, 
    trim: true 
  },
  domain: { 
    type: String, 
    trim: true 
  },
  section: { 
    type: String, 
    trim: true 
  },
  parentId: { 
    type: String, 
    trim: true 
  },
  level: { 
    type: Number, 
    default: 1 
  },
  type: { 
    type: String, 
    enum: ['preventive', 'detective', 'corrective', 'directive'], 
    default: 'preventive' 
  },
  implementation: { 
    type: String, 
    enum: ['technical', 'administrative', 'physical', 'hybrid'], 
    default: 'technical' 
  },
  automationPotential: { 
    type: String, 
    enum: ['none', 'low', 'medium', 'high', 'full'], 
    default: 'medium' 
  },
  requirements: [{ 
    type: String, 
    trim: true 
  }],
  guidance: { 
    type: String 
  },
  testProcedures: [{ 
    type: String 
  }],
  evidenceRequirements: [{
    type: { 
      type: String, 
      required: true, 
      trim: true 
    },
    description: { 
      type: String, 
      required: true 
    },
    examples: [{ 
      type: String 
    }]
  }],
  mappings: [{
    frameworkId: { 
      type: Schema.Types.ObjectId, 
      ref: 'Framework' 
    },
    frameworkName: { 
      type: String, 
      trim: true 
    },
    controlId: { 
      type: String, 
      trim: true 
    },
    relationship: { 
      type: String, 
      enum: ['equivalent', 'partial', 'related', 'subset', 'superset'], 
      default: 'related' 
    }
  }],
  regulatoryReferences: [{
    regulationId: { 
      type: Schema.Types.ObjectId, 
      ref: 'Regulation' 
    },
    regulationName: { 
      type: String, 
      trim: true 
    },
    requirementId: { 
      type: String, 
      trim: true 
    },
    section: { 
      type: String, 
      trim: true 
    }
  }],
  riskReferences: [{
    riskId: { 
      type: Schema.Types.ObjectId 
    },
    riskName: { 
      type: String, 
      trim: true 
    }
  }],
  tags: [{ 
    type: String, 
    trim: true 
  }],
  metadata: {
    type: Map,
    of: Schema.Types.Mixed
  }
}, { _id: false });

// Define domain schema
const domainSchema = new Schema({
  id: { 
    type: String, 
    required: true, 
    trim: true 
  },
  name: { 
    type: String, 
    required: true, 
    trim: true 
  },
  description: { 
    type: String 
  },
  order: { 
    type: Number 
  }
}, { _id: false });

// Define category schema
const categorySchema = new Schema({
  id: { 
    type: String, 
    required: true, 
    trim: true 
  },
  name: { 
    type: String, 
    required: true, 
    trim: true 
  },
  description: { 
    type: String 
  },
  domainId: { 
    type: String, 
    trim: true 
  },
  order: { 
    type: Number 
  }
}, { _id: false });

// Define version schema
const versionSchema = new Schema({
  versionNumber: { 
    type: String, 
    required: true, 
    trim: true 
  },
  name: { 
    type: String, 
    trim: true 
  },
  releaseDate: { 
    type: Date, 
    required: true 
  },
  effectiveDate: { 
    type: Date 
  },
  endDate: { 
    type: Date 
  },
  status: { 
    type: String, 
    enum: ['draft', 'published', 'effective', 'superseded', 'archived'], 
    default: 'draft' 
  },
  changes: [{
    type: { 
      type: String, 
      enum: ['addition', 'modification', 'removal', 'clarification'], 
      required: true 
    },
    description: { 
      type: String, 
      required: true 
    },
    controlId: { 
      type: String, 
      trim: true 
    }
  }],
  sourceUrl: { 
    type: String, 
    trim: true 
  },
  documentUrl: { 
    type: String, 
    trim: true 
  }
}, { _id: false });

// Define framework schema
const frameworkSchema = new Schema({
  name: { 
    type: String, 
    required: true, 
    trim: true 
  },
  shortName: { 
    type: String, 
    required: true, 
    trim: true 
  },
  description: { 
    type: String, 
    required: true 
  },
  type: { 
    type: String, 
    enum: [
      'regulatory', 
      'industry', 
      'security', 
      'privacy', 
      'governance', 
      'custom'
    ], 
    required: true 
  },
  category: { 
    type: String, 
    enum: [
      'privacy', 
      'security', 
      'financial', 
      'healthcare', 
      'environmental', 
      'industry-specific', 
      'general'
    ], 
    required: true 
  },
  authority: {
    name: { 
      type: String, 
      required: true, 
      trim: true 
    },
    website: { 
      type: String, 
      trim: true 
    },
    contactInfo: { 
      type: String, 
      trim: true 
    }
  },
  jurisdiction: {
    country: { 
      type: String, 
      trim: true 
    },
    region: { 
      type: String, 
      trim: true 
    },
    isGlobal: { 
      type: Boolean, 
      default: false 
    }
  },
  industry: { 
    type: String, 
    trim: true 
  },
  currentVersion: { 
    type: String, 
    trim: true 
  },
  versions: [versionSchema],
  domains: [domainSchema],
  categories: [categorySchema],
  controls: [controlSchema],
  applicability: {
    industries: [{ 
      type: String, 
      trim: true 
    }],
    regions: [{ 
      type: String, 
      trim: true 
    }],
    organizationTypes: [{ 
      type: String, 
      trim: true 
    }],
    dataTypes: [{ 
      type: String, 
      trim: true 
    }],
    thresholds: [{
      type: { 
        type: String, 
        trim: true 
      },
      value: { 
        type: Schema.Types.Mixed 
      },
      description: { 
        type: String, 
        trim: true 
      }
    }]
  },
  relatedFrameworks: [{
    frameworkId: { 
      type: Schema.Types.ObjectId, 
      ref: 'Framework' 
    },
    relationship: { 
      type: String, 
      enum: ['supersedes', 'supplements', 'implements', 'derived_from'], 
      required: true 
    },
    description: { 
      type: String, 
      trim: true 
    }
  }],
  certificationAvailable: { 
    type: Boolean, 
    default: false 
  },
  certificationDetails: {
    name: { 
      type: String, 
      trim: true 
    },
    provider: { 
      type: String, 
      trim: true 
    },
    validity: { 
      type: Number 
    }, // in months
    renewalProcess: { 
      type: String 
    },
    url: { 
      type: String, 
      trim: true 
    }
  },
  assessmentFrequency: { 
    type: String, 
    enum: ['continuous', 'monthly', 'quarterly', 'semi_annual', 'annual', 'biennial', 'custom'], 
    default: 'annual' 
  },
  customAssessmentFrequency: { 
    type: String, 
    trim: true 
  },
  tags: [{ 
    type: String, 
    trim: true 
  }],
  status: { 
    type: String, 
    enum: ['active', 'pending', 'superseded', 'deprecated'], 
    default: 'active' 
  },
  metadata: {
    type: Map,
    of: Schema.Types.Mixed
  },
  isCustom: { 
    type: Boolean, 
    default: false 
  },
  organizationId: { 
    type: Schema.Types.ObjectId, 
    ref: 'Organization' 
  },
  createdBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'User' 
  },
  updatedBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'User' 
  }
}, {
  timestamps: true,
  versionKey: true
});

// Add indexes
frameworkSchema.index({ name: 1 });
frameworkSchema.index({ shortName: 1 });
frameworkSchema.index({ type: 1 });
frameworkSchema.index({ category: 1 });
frameworkSchema.index({ 'jurisdiction.country': 1 });
frameworkSchema.index({ 'jurisdiction.region': 1 });
frameworkSchema.index({ industry: 1 });
frameworkSchema.index({ 'applicability.industries': 1 });
frameworkSchema.index({ 'applicability.regions': 1 });
frameworkSchema.index({ 'applicability.organizationTypes': 1 });
frameworkSchema.index({ 'applicability.dataTypes': 1 });
frameworkSchema.index({ 'controls.id': 1 });
frameworkSchema.index({ 'domains.id': 1 });
frameworkSchema.index({ 'categories.id': 1 });
frameworkSchema.index({ 'versions.versionNumber': 1 });
frameworkSchema.index({ 'versions.status': 1 });
frameworkSchema.index({ status: 1 });
frameworkSchema.index({ isCustom: 1 });
frameworkSchema.index({ organizationId: 1 });
frameworkSchema.index({ tags: 1 });
frameworkSchema.index({ createdAt: 1 });
frameworkSchema.index({ updatedAt: 1 });

// Add methods
frameworkSchema.methods.getCurrentVersion = function() {
  if (!this.currentVersion || !this.versions || this.versions.length === 0) {
    return null;
  }
  
  return this.versions.find(version => version.versionNumber === this.currentVersion);
};

frameworkSchema.methods.getLatestVersion = function() {
  if (!this.versions || this.versions.length === 0) {
    return null;
  }
  
  return this.versions.sort((a, b) => new Date(b.releaseDate) - new Date(a.releaseDate))[0];
};

frameworkSchema.methods.getEffectiveVersion = function(date = new Date()) {
  if (!this.versions || this.versions.length === 0) {
    return null;
  }
  
  return this.versions.find(version => {
    const effectiveDate = version.effectiveDate || version.releaseDate;
    const endDate = version.endDate || new Date('9999-12-31');
    
    return date >= effectiveDate && date <= endDate;
  });
};

frameworkSchema.methods.getControl = function(controlId) {
  if (!this.controls || this.controls.length === 0) {
    return null;
  }
  
  return this.controls.find(control => control.id === controlId);
};

frameworkSchema.methods.getDomain = function(domainId) {
  if (!this.domains || this.domains.length === 0) {
    return null;
  }
  
  return this.domains.find(domain => domain.id === domainId);
};

frameworkSchema.methods.getCategory = function(categoryId) {
  if (!this.categories || this.categories.length === 0) {
    return null;
  }
  
  return this.categories.find(category => category.id === categoryId);
};

frameworkSchema.methods.getControlsByDomain = function(domainId) {
  if (!this.controls || this.controls.length === 0) {
    return [];
  }
  
  return this.controls.filter(control => {
    const category = this.getCategory(control.category);
    return category && category.domainId === domainId;
  });
};

frameworkSchema.methods.getControlsByCategory = function(categoryId) {
  if (!this.controls || this.controls.length === 0) {
    return [];
  }
  
  return this.controls.filter(control => control.category === categoryId);
};

frameworkSchema.methods.isApplicable = function(criteria) {
  if (!criteria) {
    return true;
  }
  
  const { industry, region, organizationType, dataTypes } = criteria;
  
  // Check industry applicability
  if (industry && this.applicability.industries && this.applicability.industries.length > 0) {
    if (!this.applicability.industries.includes(industry)) {
      return false;
    }
  }
  
  // Check region applicability
  if (region && this.applicability.regions && this.applicability.regions.length > 0) {
    if (!this.applicability.regions.includes(region) && !this.jurisdiction.isGlobal) {
      return false;
    }
  }
  
  // Check organization type applicability
  if (organizationType && this.applicability.organizationTypes && this.applicability.organizationTypes.length > 0) {
    if (!this.applicability.organizationTypes.includes(organizationType)) {
      return false;
    }
  }
  
  // Check data type applicability
  if (dataTypes && dataTypes.length > 0 && this.applicability.dataTypes && this.applicability.dataTypes.length > 0) {
    const hasMatchingDataType = dataTypes.some(dataType => 
      this.applicability.dataTypes.includes(dataType)
    );
    
    if (!hasMatchingDataType) {
      return false;
    }
  }
  
  return true;
};

// Add statics
frameworkSchema.statics.findByType = function(type) {
  return this.find({ type, status: 'active' });
};

frameworkSchema.statics.findByCategory = function(category) {
  return this.find({ category, status: 'active' });
};

frameworkSchema.statics.findByJurisdiction = function(country, region) {
  const query = { status: 'active' };
  
  if (country) {
    query['$or'] = [
      { 'jurisdiction.country': country },
      { 'jurisdiction.isGlobal': true }
    ];
  }
  
  if (region) {
    query['$or'] = [
      { 'jurisdiction.region': region },
      { 'jurisdiction.isGlobal': true }
    ];
  }
  
  return this.find(query);
};

frameworkSchema.statics.findByIndustry = function(industry) {
  return this.find({
    $or: [
      { industry },
      { 'applicability.industries': industry }
    ],
    status: 'active'
  });
};

frameworkSchema.statics.findByControlMapping = function(frameworkId, controlId) {
  return this.find({
    'controls.mappings': {
      $elemMatch: {
        frameworkId,
        controlId
      }
    },
    status: 'active'
  });
};

frameworkSchema.statics.findByApplicability = function(criteria) {
  const query = { status: 'active' };
  
  if (criteria.industry) {
    query['$or'] = [
      { industry: criteria.industry },
      { 'applicability.industries': criteria.industry }
    ];
  }
  
  if (criteria.region) {
    query['$or'] = [
      { 'applicability.regions': criteria.region },
      { 'jurisdiction.isGlobal': true }
    ];
  }
  
  if (criteria.organizationType) {
    query['applicability.organizationTypes'] = criteria.organizationType;
  }
  
  if (criteria.dataTypes && criteria.dataTypes.length > 0) {
    query['applicability.dataTypes'] = { $in: criteria.dataTypes };
  }
  
  return this.find(query);
};

frameworkSchema.statics.findCustom = function(organizationId) {
  return this.find({ isCustom: true, organizationId });
};

// Create model
const Framework = mongoose.model('Framework', frameworkSchema);

module.exports = Framework;
