c5b8a3660e8079b7bde30725fcbd8094
// Mock external dependencies
_getJestObj().mock('axios');
_getJestObj().mock('jsonwebtoken');
_getJestObj().mock('mongoose');
_getJestObj().mock('redis');

// Global test utilities
function _getJestObj() {
  const {
    jest
  } = require("@jest/globals");
  _getJestObj = () => jest;
  return jest;
}
/**
 * NovaFuse Universal API Connector Test Setup
 * 
 * This file is run before each test file.
 */

// Set environment variables for testing
process.env.NODE_ENV = 'test';
process.env.PORT = '3010';
process.env.LOG_LEVEL = 'error';
process.env.TEMPLATES_DIR = './templates';
process.env.CREDENTIALS_ENCRYPTION_KEY = 'test-encryption-key';
process.env.JWT_SECRET = 'test-jwt-secret';
process.env.JWT_EXPIRATION = '1h';
process.env.RATE_LIMIT_WINDOW_MS = '60000';
process.env.RATE_LIMIT_MAX_REQUESTS = '100';
process.env.ENABLE_METRICS = 'true';
process.env.ENABLE_TRACING = 'false';
process.env.ENABLE_CACHE = 'true';
process.env.ENABLE_RATE_LIMIT = 'true';
process.env.ENABLE_SSRF_PROTECTION = 'true';
process.env.ENABLE_INPUT_VALIDATION = 'true';
global.testUtils = {
  // Create a mock request object
  createMockRequest: (options = {}) => {
    return {
      ip: '127.0.0.1',
      method: 'GET',
      path: '/',
      headers: {},
      query: {},
      params: {},
      body: {},
      ...options
    };
  },
  // Create a mock response object
  createMockResponse: () => {
    const res = {
      statusCode: 200,
      headers: {},
      body: null,
      status: function (code) {
        this.statusCode = code;
        return this;
      },
      json: function (data) {
        this.body = data;
        return this;
      },
      send: function (data) {
        this.body = data;
        return this;
      },
      setHeader: function (name, value) {
        this.headers[name] = value;
        return this;
      }
    };
    return res;
  },
  // Create a mock connector
  createMockConnector: (options = {}) => {
    return {
      metadata: {
        name: 'Test Connector',
        version: '1.0.0',
        category: 'Test',
        description: 'Test connector',
        author: 'NovaFuse',
        tags: ['test']
      },
      authentication: {
        type: 'API_KEY',
        fields: {
          apiKey: {
            type: 'string',
            description: 'API Key',
            required: true,
            sensitive: true
          }
        }
      },
      configuration: {
        baseUrl: 'https://api.example.com',
        headers: {
          'Content-Type': 'application/json'
        }
      },
      endpoints: [{
        id: 'test-endpoint',
        name: 'Test Endpoint',
        path: '/test',
        method: 'GET'
      }],
      ...options
    };
  }
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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