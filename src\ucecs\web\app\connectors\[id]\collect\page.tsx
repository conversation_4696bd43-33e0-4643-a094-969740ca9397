'use client';

import { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import Link from 'next/link';
import MainLayout from '@/components/MainLayout';
import { FiArrowLeft, FiPlay, FiLoader, FiCheckCircle, FiAlertCircle, FiFileText, FiEye } from 'react-icons/fi';
import { getConnectorById, createCollectionJob, executeJob } from '@/services/novaCoreApi';

// Mock connector data (same as in the details page)
const mockConnector = {
  id: 'conn-001',
  name: 'AWS Security Hub',
  description: 'Collects security findings from AWS Security Hub',
  type: 'aws',
  status: 'active',
  config: {
    baseUrl: 'https://securityhub.us-east-1.amazonaws.com',
    apiVersion: '2018-10-26',
  },
  authentication: {
    type: 'aws_iam',
    credentials: {
      accessKey: 'AKIAXXXXXXXXXXXXXXXX',
      secretKey: '****************************************',
      region: 'us-east-1',
    },
  },
  schedule: {
    frequency: 'daily',
    time: '02:00',
    timezone: 'UTC',
    enabled: true,
    lastRun: '2023-10-15T02:00:00Z',
    nextRun: '2023-10-16T02:00:00Z',
  },
  lastCollection: '2023-10-15T02:00:00Z',
  evidenceCount: 128,
  createdAt: '2023-09-01T10:15:30Z',
  createdBy: 'admin',
  updatedAt: '2023-10-01T14:22:45Z',
  updatedBy: 'admin',
};

export default function CollectPage() {
  const params = useParams();
  const router = useRouter();
  const [connector, setConnector] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [collecting, setCollecting] = useState(false);
  const [collectionStatus, setCollectionStatus] = useState<'idle' | 'running' | 'success' | 'error'>('idle');
  const [collectionResult, setCollectionResult] = useState<any>(null);
  const [formData, setFormData] = useState({
    name: '',
    parameters: {} as Record<string, any>,
  });

  // Load connector data
  useEffect(() => {
    const loadConnector = async () => {
      try {
        // In a real implementation, this would fetch the connector from the API
        // const result = await getConnectorById(params.id as string);
        // setConnector(result);

        // For now, use mock data
        setConnector(mockConnector);

        // Initialize form data
        setFormData({
          name: `Manual collection from ${mockConnector.name}`,
          parameters: {},
        });

        setLoading(false);
      } catch (error) {
        console.error('Error loading connector:', error);
        setLoading(false);
      }
    };

    loadConnector();
  }, [params.id]);

  // Handle form field change
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!connector) {
      return;
    }

    setCollecting(true);
    setCollectionStatus('running');

    try {
      // In a real implementation, this would create and execute a collection job via API
      // const job = await createCollectionJob(connector.id, {
      //   name: formData.name,
      //   priority: 'high',
      //   type: 'manual',
      //   parameters: formData.parameters,
      // });
      // const result = await executeJob(job.id);

      // For now, simulate API call
      await new Promise(resolve => setTimeout(resolve, 3000));

      // Simulate successful collection
      const result = {
        status: 'success',
        evidenceIds: ['ev-001', 'ev-002', 'ev-003'],
        stats: {
          totalItems: 3,
          processedItems: 3,
          successfulItems: 3,
          failedItems: 0,
        },
      };

      setCollectionResult(result);
      setCollectionStatus('success');
    } catch (error) {
      console.error('Error collecting evidence:', error);
      setCollectionStatus('error');
      setCollectionResult({
        status: 'failure',
        errorMessage: 'Failed to collect evidence',
      });
    } finally {
      setCollecting(false);
    }
  };

  if (loading) {
    return (
      <MainLayout>
        <div className="card p-8 text-center">
          <p className="text-gray-500 dark:text-gray-400">Loading connector...</p>
        </div>
      </MainLayout>
    );
  }

  if (!connector) {
    return (
      <MainLayout>
        <div className="card p-8 text-center">
          <p className="text-gray-500 dark:text-gray-400">Connector not found</p>
          <Link href="/connectors" className="btn btn-primary mt-4">
            Back to Connectors
          </Link>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="mb-6 flex items-center">
        <Link href={`/connectors/${connector.id}`} className="mr-4">
          <FiArrowLeft className="h-5 w-5 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200" />
        </Link>
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Collect Evidence</h1>
          <p className="text-gray-500 dark:text-gray-400">{connector.name}</p>
        </div>
      </div>

      <div className="card">
        {collectionStatus === 'idle' && (
          <form onSubmit={handleSubmit}>
            <div className="mb-6">
              <div className="flex items-center mb-4">
                <FiPlay className="h-5 w-5 text-primary mr-2" />
                <h2 className="text-lg font-medium text-gray-900 dark:text-white">Collection Settings</h2>
              </div>

              <div className="mb-4">
                <label htmlFor="name" className="label">Collection Name</label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  className="input"
                  value={formData.name}
                  onChange={handleChange}
                  required
                />
              </div>

              <div className="bg-yellow-50 dark:bg-yellow-900/20 border-l-4 border-yellow-400 p-4 mb-4">
                <div className="flex">
                  <div className="ml-3">
                    <p className="text-sm text-yellow-700 dark:text-yellow-200">
                      This will start a manual collection job for this connector. Depending on the amount of data, this may take some time to complete.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex justify-between">
              <Link href={`/connectors/${connector.id}`} className="btn btn-outline">
                Cancel
              </Link>
              <button
                type="submit"
                className="btn btn-primary flex items-center"
              >
                <FiPlay className="mr-2" />
                Start Collection
              </button>
            </div>
          </form>
        )}

        {collectionStatus === 'running' && (
          <div className="text-center py-12">
            <FiLoader className="h-12 w-12 text-primary mx-auto animate-spin" />
            <h2 className="text-lg font-medium text-gray-900 dark:text-white mt-4">Collecting Evidence</h2>
            <p className="text-gray-500 dark:text-gray-400 mt-2">
              This may take a few moments. Please wait...
            </p>
          </div>
        )}

        {collectionStatus === 'success' && collectionResult && (
          <div className="py-6">
            <div className="text-center mb-6">
              <FiCheckCircle className="h-12 w-12 text-green-500 mx-auto" />
              <h2 className="text-lg font-medium text-gray-900 dark:text-white mt-4">Collection Successful</h2>
              <p className="text-gray-500 dark:text-gray-400 mt-2">
                Evidence has been successfully collected from {connector.name}.
              </p>
            </div>

            <div className="bg-green-50 dark:bg-green-900/20 border rounded-md p-4 mb-6">
              <h3 className="text-md font-medium text-green-800 dark:text-green-200 mb-2">Collection Summary</h3>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-green-700 dark:text-green-300">Total Items</p>
                  <p className="text-lg font-medium text-green-800 dark:text-green-200">{collectionResult.stats.totalItems}</p>
                </div>
                <div>
                  <p className="text-sm text-green-700 dark:text-green-300">Successful Items</p>
                  <p className="text-lg font-medium text-green-800 dark:text-green-200">{collectionResult.stats.successfulItems}</p>
                </div>
                <div>
                  <p className="text-sm text-green-700 dark:text-green-300">Failed Items</p>
                  <p className="text-lg font-medium text-green-800 dark:text-green-200">{collectionResult.stats.failedItems}</p>
                </div>
                <div>
                  <p className="text-sm text-green-700 dark:text-green-300">Evidence IDs</p>
                  <p className="text-sm font-mono text-green-800 dark:text-green-200">{collectionResult.evidenceIds.join(', ')}</p>
                </div>
              </div>
            </div>

            <div className="flex justify-between">
              <Link href={`/connectors/${connector.id}`} className="btn btn-outline">
                Back to Connector
              </Link>
              <Link href={`/evidence?connectorId=${connector.id}`} className="btn btn-primary flex items-center">
                <FiEye className="mr-2" />
                View Evidence
              </Link>
            </div>
          </div>
        )}

        {collectionStatus === 'error' && collectionResult && (
          <div className="py-6">
            <div className="text-center mb-6">
              <FiAlertCircle className="h-12 w-12 text-red-500 mx-auto" />
              <h2 className="text-lg font-medium text-gray-900 dark:text-white mt-4">Collection Failed</h2>
              <p className="text-gray-500 dark:text-gray-400 mt-2">
                There was an error collecting evidence from {connector.name}.
              </p>
            </div>

            <div className="bg-red-50 dark:bg-red-900/20 border rounded-md p-4 mb-6">
              <h3 className="text-md font-medium text-red-800 dark:text-red-200 mb-2">Error Details</h3>
              <p className="text-sm text-red-700 dark:text-red-300">{collectionResult.errorMessage}</p>
            </div>

            <div className="flex justify-between">
              <Link href={`/connectors/${connector.id}`} className="btn btn-outline">
                Back to Connector
              </Link>
              <button
                className="btn btn-primary flex items-center"
                onClick={() => {
                  setCollectionStatus('idle');
                  setCollectionResult(null);
                }}
              >
                <FiPlay className="mr-2" />
                Try Again
              </button>
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  );
}
