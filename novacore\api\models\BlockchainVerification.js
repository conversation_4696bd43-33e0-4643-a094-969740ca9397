/**
 * NovaCore Blockchain Verification Model
 * 
 * This model defines the schema for blockchain verification records in the NovaCore API.
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

// Define transaction schema
const transactionSchema = new Schema({
  txHash: { 
    type: String, 
    required: true, 
    trim: true 
  },
  blockNumber: { 
    type: Number 
  },
  blockHash: { 
    type: String, 
    trim: true 
  },
  timestamp: { 
    type: Date 
  },
  from: { 
    type: String, 
    trim: true 
  },
  to: { 
    type: String, 
    trim: true 
  },
  value: { 
    type: String, 
    trim: true 
  },
  gasUsed: { 
    type: Number 
  },
  status: { 
    type: String, 
    enum: ['pending', 'confirmed', 'failed'], 
    default: 'pending' 
  },
  confirmations: { 
    type: Number, 
    default: 0 
  },
  network: { 
    type: String, 
    trim: true 
  }
}, { _id: false });

// Define merkle proof schema
const merkleProofSchema = new Schema({
  root: { 
    type: String, 
    required: true, 
    trim: true 
  },
  path: [{ 
    type: String, 
    trim: true 
  }],
  indices: [{ 
    type: Number 
  }],
  leaf: { 
    type: String, 
    required: true, 
    trim: true 
  }
}, { _id: false });

// Define blockchain verification schema
const blockchainVerificationSchema = new Schema({
  evidenceId: { 
    type: Schema.Types.ObjectId, 
    ref: 'Evidence', 
    required: true 
  },
  hash: { 
    type: String, 
    required: true, 
    trim: true 
  },
  provider: { 
    type: String, 
    required: true, 
    enum: ['ethereum', 'hyperledger', 'corda', 'bitcoin', 'other'], 
    default: 'ethereum' 
  },
  status: { 
    type: String, 
    enum: ['pending', 'submitted', 'confirmed', 'failed'], 
    default: 'pending' 
  },
  transaction: { 
    type: transactionSchema 
  },
  merkleProof: { 
    type: merkleProofSchema 
  },
  verificationUrl: { 
    type: String, 
    trim: true 
  },
  metadata: { 
    type: Map, 
    of: Schema.Types.Mixed 
  },
  createdBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'User' 
  }
}, {
  timestamps: true,
  versionKey: true
});

// Add indexes
blockchainVerificationSchema.index({ evidenceId: 1 });
blockchainVerificationSchema.index({ hash: 1 });
blockchainVerificationSchema.index({ status: 1 });
blockchainVerificationSchema.index({ 'transaction.txHash': 1 });
blockchainVerificationSchema.index({ createdAt: 1 });

// Add methods
blockchainVerificationSchema.methods.isConfirmed = function() {
  return this.status === 'confirmed';
};

blockchainVerificationSchema.methods.getConfirmationUrl = function() {
  if (!this.transaction || !this.transaction.txHash) {
    return null;
  }
  
  switch (this.provider) {
    case 'ethereum':
      return `https://etherscan.io/tx/${this.transaction.txHash}`;
    case 'bitcoin':
      return `https://www.blockchain.com/btc/tx/${this.transaction.txHash}`;
    default:
      return this.verificationUrl || null;
  }
};

// Add statics
blockchainVerificationSchema.statics.findByEvidenceId = function(evidenceId) {
  return this.findOne({ evidenceId });
};

blockchainVerificationSchema.statics.findByHash = function(hash) {
  return this.findOne({ hash });
};

// Create model
const BlockchainVerification = mongoose.model('BlockchainVerification', blockchainVerificationSchema);

module.exports = BlockchainVerification;
