/**
 * NovaPulse+ - Notification Service
 * 
 * This service provides notification capabilities for regulatory changes.
 */

const { createLogger } = require('../../utils/logger');

const logger = createLogger('notification-service');

// In-memory storage for notifications (would be replaced with a database in production)
const notifications = [];

// In-memory storage for notification subscribers (would be replaced with a database in production)
const subscribers = [
  {
    id: 'subscriber-001',
    name: 'Legal Team',
    email: '<EMAIL>',
    channels: ['email', 'dashboard'],
    subscriptions: {
      regulations: ['GDPR', 'CCPA', 'PCI DSS'],
      categories: ['data-protection', 'consumer-rights'],
      priorities: ['critical', 'high']
    }
  },
  {
    id: 'subscriber-002',
    name: 'Compliance Team',
    email: '<EMAIL>',
    channels: ['email', 'dashboard', 'slack'],
    subscriptions: {
      regulations: ['all'],
      categories: ['all'],
      priorities: ['critical', 'high', 'medium']
    }
  },
  {
    id: 'subscriber-003',
    name: 'IT Security Team',
    email: '<EMAIL>',
    channels: ['email', 'slack'],
    subscriptions: {
      regulations: ['PCI DSS', 'ISO 27001'],
      categories: ['data-security', 'payment-security'],
      priorities: ['critical', 'high']
    }
  }
];

/**
 * Send impact notification
 * 
 * @param {Object} regulatoryChange - Regulatory change object
 * @param {Object} impactAnalysis - Impact analysis object
 * @returns {Promise<Array>} - Sent notifications
 */
async function sendImpactNotification(regulatoryChange, impactAnalysis) {
  logger.info('Sending impact notification', { 
    changeId: regulatoryChange.id,
    regulation: regulatoryChange.regulation
  });
  
  try {
    // Find relevant subscribers
    const relevantSubscribers = findRelevantSubscribers(regulatoryChange, impactAnalysis);
    
    if (relevantSubscribers.length === 0) {
      logger.info('No relevant subscribers found for notification');
      return [];
    }
    
    // Create notification
    const notification = {
      id: `notification-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
      changeId: regulatoryChange.id,
      impactAnalysisId: impactAnalysis.id,
      title: `Impact Analysis: ${regulatoryChange.title}`,
      content: generateNotificationContent(regulatoryChange, impactAnalysis),
      priority: impactAnalysis.priority,
      createdAt: new Date().toISOString(),
      sentNotifications: []
    };
    
    // Send notifications to each subscriber
    for (const subscriber of relevantSubscribers) {
      const sentChannels = await sendToSubscriber(notification, subscriber);
      
      notification.sentNotifications.push({
        subscriberId: subscriber.id,
        subscriberName: subscriber.name,
        channels: sentChannels,
        sentAt: new Date().toISOString()
      });
    }
    
    // Store notification
    notifications.push(notification);
    
    return notification;
  } catch (error) {
    logger.error('Error sending impact notification', {
      changeId: regulatoryChange.id,
      error: error.message
    });
    throw error;
  }
}

/**
 * Find relevant subscribers for a notification
 * 
 * @param {Object} regulatoryChange - Regulatory change object
 * @param {Object} impactAnalysis - Impact analysis object
 * @returns {Array} - Relevant subscribers
 * @private
 */
function findRelevantSubscribers(regulatoryChange, impactAnalysis) {
  return subscribers.filter(subscriber => {
    // Check if subscriber is interested in this regulation
    const interestedInRegulation = 
      subscriber.subscriptions.regulations.includes('all') ||
      subscriber.subscriptions.regulations.includes(regulatoryChange.regulation);
    
    if (!interestedInRegulation) {
      return false;
    }
    
    // Check if subscriber is interested in any of the categories
    const interestedInCategory = 
      subscriber.subscriptions.categories.includes('all') ||
      regulatoryChange.categories.some(category => 
        subscriber.subscriptions.categories.includes(category)
      );
    
    if (!interestedInCategory) {
      return false;
    }
    
    // Check if subscriber is interested in this priority
    const interestedInPriority = 
      subscriber.subscriptions.priorities.includes(impactAnalysis.priority);
    
    return interestedInPriority;
  });
}

/**
 * Generate notification content
 * 
 * @param {Object} regulatoryChange - Regulatory change object
 * @param {Object} impactAnalysis - Impact analysis object
 * @returns {string} - Notification content
 * @private
 */
function generateNotificationContent(regulatoryChange, impactAnalysis) {
  return `
Impact Analysis for ${regulatoryChange.regulation}: ${regulatoryChange.title}

Priority: ${impactAnalysis.priority.toUpperCase()}
Impact Scores:
- Overall: ${impactAnalysis.impactScores.overall}
- Operational: ${impactAnalysis.impactScores.operational}
- Financial: ${impactAnalysis.impactScores.financial}
- Compliance: ${impactAnalysis.impactScores.compliance}
- Reputational: ${impactAnalysis.impactScores.reputational}

Affected Systems:
${impactAnalysis.affectedSystems.map(system => `- ${system}`).join('\n')}

Implementation Timeline:
- Start Date: ${new Date(impactAnalysis.implementationPlan.startDate).toLocaleDateString()}
- Target Completion: ${new Date(impactAnalysis.implementationPlan.targetCompletionDate).toLocaleDateString()}
- Duration: ${impactAnalysis.implementationPlan.timelineInDays} days

Resource Estimates:
- Person Days: ${impactAnalysis.implementationPlan.resourceEstimates.personDays}
- Cost Estimate: $${impactAnalysis.implementationPlan.resourceEstimates.costEstimate.toLocaleString()}

View full analysis: [Link to Impact Analysis]
  `;
}

/**
 * Send notification to a subscriber
 * 
 * @param {Object} notification - Notification object
 * @param {Object} subscriber - Subscriber object
 * @returns {Promise<Array>} - Channels the notification was sent to
 * @private
 */
async function sendToSubscriber(notification, subscriber) {
  logger.debug('Sending notification to subscriber', { 
    notificationId: notification.id,
    subscriberId: subscriber.id
  });
  
  const sentChannels = [];
  
  // Send to each channel
  for (const channel of subscriber.channels) {
    try {
      switch (channel) {
        case 'email':
          await sendEmail(notification, subscriber);
          sentChannels.push('email');
          break;
        case 'dashboard':
          await sendToDashboard(notification, subscriber);
          sentChannels.push('dashboard');
          break;
        case 'slack':
          await sendToSlack(notification, subscriber);
          sentChannels.push('slack');
          break;
        default:
          logger.warn('Unknown notification channel', { channel });
      }
    } catch (error) {
      logger.error('Error sending notification to channel', {
        channel,
        subscriberId: subscriber.id,
        error: error.message
      });
    }
  }
  
  return sentChannels;
}

/**
 * Send notification via email
 * 
 * @param {Object} notification - Notification object
 * @param {Object} subscriber - Subscriber object
 * @returns {Promise<void>}
 * @private
 */
async function sendEmail(notification, subscriber) {
  // In a real implementation, this would send an email
  logger.info('Sending email notification', {
    to: subscriber.email,
    subject: notification.title
  });
  
  // Simulate email sending
  return new Promise(resolve => setTimeout(resolve, 100));
}

/**
 * Send notification to dashboard
 * 
 * @param {Object} notification - Notification object
 * @param {Object} subscriber - Subscriber object
 * @returns {Promise<void>}
 * @private
 */
async function sendToDashboard(notification, subscriber) {
  // In a real implementation, this would send to a dashboard
  logger.info('Sending dashboard notification', {
    subscriberId: subscriber.id
  });
  
  // Simulate dashboard notification
  return new Promise(resolve => setTimeout(resolve, 50));
}

/**
 * Send notification to Slack
 * 
 * @param {Object} notification - Notification object
 * @param {Object} subscriber - Subscriber object
 * @returns {Promise<void>}
 * @private
 */
async function sendToSlack(notification, subscriber) {
  // In a real implementation, this would send to Slack
  logger.info('Sending Slack notification', {
    subscriberId: subscriber.id
  });
  
  // Simulate Slack notification
  return new Promise(resolve => setTimeout(resolve, 75));
}

/**
 * Get all notifications
 * 
 * @param {Object} filters - Optional filters
 * @returns {Promise<Array>} - List of notifications
 */
async function getAllNotifications(filters = {}) {
  logger.debug('Getting all notifications', { filters });
  
  let filteredNotifications = [...notifications];
  
  // Apply filters if provided
  if (filters.changeId) {
    filteredNotifications = filteredNotifications.filter(n => n.changeId === filters.changeId);
  }
  
  if (filters.priority) {
    filteredNotifications = filteredNotifications.filter(n => n.priority === filters.priority);
  }
  
  if (filters.subscriberId) {
    filteredNotifications = filteredNotifications.filter(n => 
      n.sentNotifications.some(sn => sn.subscriberId === filters.subscriberId)
    );
  }
  
  return filteredNotifications;
}

/**
 * Add subscriber
 * 
 * @param {Object} subscriberData - Subscriber data
 * @returns {Promise<Object>} - Created subscriber
 */
async function addSubscriber(subscriberData) {
  logger.info('Adding subscriber', { name: subscriberData.name });
  
  // Validate required fields
  if (!subscriberData.name) {
    throw new Error('Subscriber name is required');
  }
  
  if (!subscriberData.email) {
    throw new Error('Subscriber email is required');
  }
  
  // Create subscriber object
  const subscriber = {
    id: subscriberData.id || `subscriber-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
    name: subscriberData.name,
    email: subscriberData.email,
    channels: subscriberData.channels || ['email'],
    subscriptions: {
      regulations: subscriberData.subscriptions?.regulations || ['all'],
      categories: subscriberData.subscriptions?.categories || ['all'],
      priorities: subscriberData.subscriptions?.priorities || ['critical', 'high']
    }
  };
  
  // Add to subscribers
  subscribers.push(subscriber);
  
  return subscriber;
}

/**
 * Update subscriber
 * 
 * @param {string} subscriberId - Subscriber ID
 * @param {Object} subscriberData - Subscriber data to update
 * @returns {Promise<Object>} - Updated subscriber
 */
async function updateSubscriber(subscriberId, subscriberData) {
  logger.info('Updating subscriber', { subscriberId });
  
  // Find subscriber
  const index = subscribers.findIndex(s => s.id === subscriberId);
  
  if (index === -1) {
    logger.warn('Subscriber not found', { subscriberId });
    throw new Error(`Subscriber not found with ID: ${subscriberId}`);
  }
  
  // Update subscriber
  const updatedSubscriber = {
    ...subscribers[index],
    ...subscriberData,
    id: subscriberId, // Ensure ID doesn't change
    subscriptions: {
      ...subscribers[index].subscriptions,
      ...(subscriberData.subscriptions || {})
    }
  };
  
  subscribers[index] = updatedSubscriber;
  
  return updatedSubscriber;
}

/**
 * Remove subscriber
 * 
 * @param {string} subscriberId - Subscriber ID
 * @returns {Promise<boolean>} - Whether the subscriber was removed
 */
async function removeSubscriber(subscriberId) {
  logger.info('Removing subscriber', { subscriberId });
  
  // Find subscriber
  const index = subscribers.findIndex(s => s.id === subscriberId);
  
  if (index === -1) {
    logger.warn('Subscriber not found', { subscriberId });
    throw new Error(`Subscriber not found with ID: ${subscriberId}`);
  }
  
  // Remove subscriber
  subscribers.splice(index, 1);
  
  return true;
}

module.exports = {
  sendImpactNotification,
  getAllNotifications,
  addSubscriber,
  updateSubscriber,
  removeSubscriber
};
