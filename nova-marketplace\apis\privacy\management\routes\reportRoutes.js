/**
 * Report Routes
 * 
 * This file defines the routes for reports and analytics.
 */

const express = require('express');
const router = express.Router();
const { reportController } = require('../controllers');

// Generate a report based on the report type
router.get('/:reportType', reportController.generateReport);

// Get dashboard metrics
router.get('/dashboard/metrics', reportController.getDashboardMetrics);

module.exports = router;
