<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Patent Diagram Screenshot Guide</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 40px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        h1 {
            text-align: center;
            font-size: 2.5em;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .step-container {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            border-left: 5px solid #ffd700;
        }
        
        .step-number {
            background: #ffd700;
            color: #333;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 18px;
            float: left;
            margin-right: 20px;
        }
        
        .step-content {
            overflow: hidden;
        }
        
        .step-title {
            font-size: 1.4em;
            font-weight: bold;
            margin-bottom: 10px;
            color: #ffd700;
        }
        
        .step-description {
            font-size: 1.1em;
            line-height: 1.6;
            margin-bottom: 15px;
        }
        
        .step-details {
            background: rgba(0, 0, 0, 0.2);
            padding: 15px;
            border-radius: 8px;
            font-size: 0.95em;
            line-height: 1.5;
        }
        
        .keyboard-shortcut {
            background: #333;
            color: #ffd700;
            padding: 4px 8px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-weight: bold;
        }
        
        .file-naming {
            background: rgba(255, 215, 0, 0.2);
            border: 1px solid #ffd700;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .file-naming h3 {
            color: #ffd700;
            margin-top: 0;
        }
        
        .example-filename {
            background: #333;
            color: #ffd700;
            padding: 8px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            margin: 5px 0;
        }
        
        .tips-section {
            background: rgba(39, 174, 96, 0.2);
            border: 1px solid #27ae60;
            border-radius: 8px;
            padding: 20px;
            margin: 25px 0;
        }
        
        .tips-section h3 {
            color: #27ae60;
            margin-top: 0;
        }
        
        .tip-item {
            margin: 10px 0;
            padding-left: 20px;
            position: relative;
        }
        
        .tip-item::before {
            content: "💡";
            position: absolute;
            left: 0;
        }
        
        .quality-settings {
            background: rgba(231, 76, 60, 0.2);
            border: 1px solid #e74c3c;
            border-radius: 8px;
            padding: 20px;
            margin: 25px 0;
        }
        
        .quality-settings h3 {
            color: #e74c3c;
            margin-top: 0;
        }
        
        .action-buttons {
            text-align: center;
            margin-top: 30px;
        }
        
        .btn {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 25px;
            font-weight: bold;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 16px;
            margin: 0 10px;
        }
        
        .btn:hover {
            background: linear-gradient(45deg, #ee5a24, #ff6b6b);
            transform: scale(1.05);
        }
        
        .btn-secondary {
            background: linear-gradient(45deg, #4fc3f7, #29b6f6);
        }
        
        .btn-secondary:hover {
            background: linear-gradient(45deg, #29b6f6, #4fc3f7);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📸 Patent Diagram Screenshot Guide</h1>
        <p style="text-align: center; font-size: 1.2em; margin-bottom: 40px;">
            Professional screenshot capture for USPTO patent submission
        </p>
        
        <div class="step-container">
            <div class="step-number">1</div>
            <div class="step-content">
                <div class="step-title">Select Your Diagrams</div>
                <div class="step-description">
                    Use the Patent Diagram Master Organizer to select which diagrams you want to include in your patent submission.
                </div>
                <div class="step-details">
                    • Click on diagram cards to select them (they'll turn gold)<br>
                    • Mix and match from different sets (A, B, C, D)<br>
                    • Use "Select All" for comprehensive patent coverage<br>
                    • Selected diagrams appear in the bottom-right panel
                </div>
            </div>
        </div>
        
        <div class="step-container">
            <div class="step-number">2</div>
            <div class="step-content">
                <div class="step-title">Open Each Diagram</div>
                <div class="step-description">
                    Click the "🔍 View" button on each selected diagram to open it in a new window.
                </div>
                <div class="step-details">
                    • Each diagram opens in its own browser window<br>
                    • Diagrams are already optimized for screenshot capture<br>
                    • Allow diagrams to fully load before taking screenshots<br>
                    • Keep multiple windows open for batch processing
                </div>
            </div>
        </div>
        
        <div class="step-container">
            <div class="step-number">3</div>
            <div class="step-content">
                <div class="step-title">Use Windows Snipping Tool</div>
                <div class="step-description">
                    Press <span class="keyboard-shortcut">Win + Shift + S</span> to activate the Windows Snipping Tool.
                </div>
                <div class="step-details">
                    • Select "Rectangular Snip" mode<br>
                    • Capture the entire diagram area (including borders)<br>
                    • Avoid capturing browser UI elements<br>
                    • Ensure high contrast and clarity
                </div>
            </div>
        </div>
        
        <div class="step-container">
            <div class="step-number">4</div>
            <div class="step-content">
                <div class="step-title">Save with Proper Naming</div>
                <div class="step-description">
                    Save each screenshot with a descriptive filename that matches the patent structure.
                </div>
                <div class="step-details">
                    • Use PNG format for best quality<br>
                    • Include set letter and figure number<br>
                    • Add descriptive title for easy identification<br>
                    • Maintain consistent naming convention
                </div>
            </div>
        </div>
        
        <div class="file-naming">
            <h3>📁 File Naming Convention</h3>
            <p><strong>Format:</strong> [Set]_FIG[Number]_[Title].png</p>
            
            <div class="example-filename">A_FIG1_UUFT-Mathematical-Framework.png</div>
            <div class="example-filename">B_FIG2_Performance-Visualization-3142x.png</div>
            <div class="example-filename">C_FIG3_Tensor-Fusion-Architecture.png</div>
            <div class="example-filename">D_FIG4_18-82-Principle-Implementation.png</div>
            
            <p><strong>Benefits:</strong></p>
            <ul>
                <li>Easy sorting and organization</li>
                <li>Clear patent section identification</li>
                <li>Professional presentation</li>
                <li>USPTO submission ready</li>
            </ul>
        </div>
        
        <div class="quality-settings">
            <h3>🎯 Quality Requirements for Patent Submission</h3>
            <ul>
                <li><strong>Resolution:</strong> Minimum 300 DPI for print quality</li>
                <li><strong>Format:</strong> PNG for lossless compression</li>
                <li><strong>Size:</strong> Ensure text is readable when printed</li>
                <li><strong>Contrast:</strong> High contrast for black & white printing</li>
                <li><strong>Clarity:</strong> All text and lines must be sharp</li>
            </ul>
        </div>
        
        <div class="tips-section">
            <h3>💡 Pro Tips for Patent-Quality Screenshots</h3>
            
            <div class="tip-item">
                <strong>Zoom Level:</strong> Use 100% browser zoom for crisp screenshots
            </div>
            
            <div class="tip-item">
                <strong>Multiple Monitors:</strong> Use secondary monitor for diagram viewing while capturing
            </div>
            
            <div class="tip-item">
                <strong>Batch Processing:</strong> Open all diagrams first, then screenshot systematically
            </div>
            
            <div class="tip-item">
                <strong>Backup Copies:</strong> Save both high-res and web-optimized versions
            </div>
            
            <div class="tip-item">
                <strong>Organization:</strong> Create folders by Set (A, B, C, D) for easy management
            </div>
            
            <div class="tip-item">
                <strong>Verification:</strong> Review each screenshot for completeness before proceeding
            </div>
        </div>
        
        <div class="step-container">
            <div class="step-number">5</div>
            <div class="step-content">
                <div class="step-title">Organize for Patent Submission</div>
                <div class="step-description">
                    Arrange your screenshots in a logical order that supports your patent claims.
                </div>
                <div class="step-details">
                    • Group by patent sets (A, B, C, D)<br>
                    • Create a master index document<br>
                    • Cross-reference with patent claims<br>
                    • Prepare both digital and print versions
                </div>
            </div>
        </div>
        
        <div class="action-buttons">
            <button class="btn" onclick="window.opener.focus(); window.close();">
                🔙 Back to Organizer
            </button>
            <button class="btn btn-secondary" onclick="window.print();">
                🖨️ Print Guide
            </button>
            <button class="btn btn-secondary" onclick="openOrganizer();">
                📋 Open Organizer
            </button>
        </div>
    </div>
    
    <script>
        function openOrganizer() {
            window.open('./patent-diagram-master-organizer.html', '_blank', 'width=1400,height=900');
        }
    </script>
</body>
</html>
