/**
 * Threshold Management
 * 
 * This module implements the Threshold Management component of the Meter.
 * It manages thresholds for entropy values and triggers alerts when thresholds are exceeded.
 */

const { EventEmitter } = require('events');
const { performance } = require('perf_hooks');

/**
 * ThresholdManagement class
 */
class ThresholdManagement extends EventEmitter {
  /**
   * Create a new ThresholdManagement instance
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      enableLogging: true,
      enableMetrics: true,
      historySize: 100, // Number of historical data points to keep
      defaultThresholds: {
        universal: {
          warning: 0.6,
          critical: 0.8,
          emergency: 0.95
        },
        cyber: {
          warning: 0.6,
          critical: 0.8,
          emergency: 0.95
        },
        financial: {
          warning: 0.6,
          critical: 0.8,
          emergency: 0.95
        },
        biological: {
          warning: 0.6,
          critical: 0.8,
          emergency: 0.95
        }
      },
      ...options
    };
    
    // Initialize state
    this.state = {
      thresholds: JSON.parse(JSON.stringify(this.options.defaultThresholds)),
      thresholdViolations: {
        universal: [],
        cyber: [],
        financial: [],
        biological: []
      },
      activeAlerts: new Map(), // id -> alert
      thresholdHistory: [],
      lastUpdateTime: Date.now()
    };
    
    // Initialize metrics
    this.metrics = {
      processingTimeMs: 0,
      totalEvaluations: 0,
      thresholdViolations: 0,
      alertsGenerated: 0,
      alertsResolved: 0
    };
    
    if (this.options.enableLogging) {
      console.log('ThresholdManagement initialized');
    }
  }
  
  /**
   * Set threshold
   * @param {string} domain - Domain (universal, cyber, financial, biological)
   * @param {string} level - Threshold level (warning, critical, emergency)
   * @param {number} value - Threshold value
   * @returns {Object} - Updated thresholds
   */
  setThreshold(domain, level, value) {
    const startTime = performance.now();
    
    // Validate domain
    if (!['universal', 'cyber', 'financial', 'biological'].includes(domain)) {
      throw new Error(`Invalid domain: ${domain}`);
    }
    
    // Validate level
    if (!['warning', 'critical', 'emergency'].includes(level)) {
      throw new Error(`Invalid threshold level: ${level}`);
    }
    
    // Validate value
    if (typeof value !== 'number' || value < 0 || value > 1) {
      throw new Error('Threshold value must be a number between 0 and 1');
    }
    
    // Validate threshold ordering
    const thresholds = this.state.thresholds[domain];
    
    if (level === 'warning' && value >= thresholds.critical) {
      throw new Error('Warning threshold must be less than critical threshold');
    }
    
    if (level === 'critical' && (value <= thresholds.warning || value >= thresholds.emergency)) {
      throw new Error('Critical threshold must be between warning and emergency thresholds');
    }
    
    if (level === 'emergency' && value <= thresholds.critical) {
      throw new Error('Emergency threshold must be greater than critical threshold');
    }
    
    // Update threshold
    this.state.thresholds[domain][level] = value;
    this.state.lastUpdateTime = Date.now();
    
    // Add to history
    this.state.thresholdHistory.push({
      domain,
      level,
      value,
      timestamp: Date.now()
    });
    
    // Limit history size
    if (this.state.thresholdHistory.length > this.options.historySize) {
      this.state.thresholdHistory.shift();
    }
    
    // Update metrics
    this.metrics.processingTimeMs += performance.now() - startTime;
    
    // Emit update event
    this.emit('threshold-update', {
      domain,
      level,
      value,
      thresholds: { ...this.state.thresholds[domain] },
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log(`ThresholdManagement: Set ${domain} ${level} threshold to ${value.toFixed(4)}`);
    }
    
    return { ...this.state.thresholds[domain] };
  }
  
  /**
   * Reset thresholds to default
   * @param {string} domain - Domain to reset (all domains if not specified)
   * @returns {Object} - Updated thresholds
   */
  resetThresholds(domain) {
    const startTime = performance.now();
    
    if (domain && !['universal', 'cyber', 'financial', 'biological'].includes(domain)) {
      throw new Error(`Invalid domain: ${domain}`);
    }
    
    if (domain) {
      // Reset specific domain
      this.state.thresholds[domain] = JSON.parse(JSON.stringify(this.options.defaultThresholds[domain]));
      
      // Emit update event
      this.emit('threshold-reset', {
        domain,
        thresholds: { ...this.state.thresholds[domain] },
        timestamp: Date.now()
      });
      
      if (this.options.enableLogging) {
        console.log(`ThresholdManagement: Reset ${domain} thresholds to default`);
      }
      
      return { ...this.state.thresholds[domain] };
    } else {
      // Reset all domains
      this.state.thresholds = JSON.parse(JSON.stringify(this.options.defaultThresholds));
      
      // Emit update event
      this.emit('threshold-reset', {
        domain: 'all',
        thresholds: JSON.parse(JSON.stringify(this.state.thresholds)),
        timestamp: Date.now()
      });
      
      if (this.options.enableLogging) {
        console.log('ThresholdManagement: Reset all thresholds to default');
      }
      
      return JSON.parse(JSON.stringify(this.state.thresholds));
    }
  }
  
  /**
   * Evaluate entropy against thresholds
   * @param {string} domain - Domain (universal, cyber, financial, biological)
   * @param {number} entropyValue - Entropy value
   * @param {Object} metadata - Additional metadata
   * @returns {Object} - Evaluation result
   */
  evaluateThreshold(domain, entropyValue, metadata = {}) {
    const startTime = performance.now();
    
    // Validate domain
    if (!['universal', 'cyber', 'financial', 'biological'].includes(domain)) {
      throw new Error(`Invalid domain: ${domain}`);
    }
    
    // Validate entropy value
    if (typeof entropyValue !== 'number' || entropyValue < 0 || entropyValue > 1) {
      throw new Error('Entropy value must be a number between 0 and 1');
    }
    
    // Get thresholds for domain
    const thresholds = this.state.thresholds[domain];
    
    // Determine threshold level
    let thresholdLevel = 'normal';
    let violatedThreshold = null;
    
    if (entropyValue >= thresholds.emergency) {
      thresholdLevel = 'emergency';
      violatedThreshold = thresholds.emergency;
    } else if (entropyValue >= thresholds.critical) {
      thresholdLevel = 'critical';
      violatedThreshold = thresholds.critical;
    } else if (entropyValue >= thresholds.warning) {
      thresholdLevel = 'warning';
      violatedThreshold = thresholds.warning;
    }
    
    // Create evaluation result
    const result = {
      domain,
      entropyValue,
      thresholdLevel,
      violatedThreshold,
      timestamp: Date.now(),
      metadata
    };
    
    // If threshold violated, add to violations and generate alert
    if (thresholdLevel !== 'normal') {
      // Add to violations
      this.state.thresholdViolations[domain].push(result);
      
      // Limit violations history
      if (this.state.thresholdViolations[domain].length > this.options.historySize) {
        this.state.thresholdViolations[domain].shift();
      }
      
      // Generate alert
      this._generateAlert(domain, thresholdLevel, entropyValue, violatedThreshold, metadata);
      
      // Update metrics
      this.metrics.thresholdViolations++;
    } else {
      // Check if we need to resolve any active alerts for this domain
      this._resolveAlerts(domain, entropyValue);
    }
    
    // Update metrics
    this.metrics.processingTimeMs += performance.now() - startTime;
    this.metrics.totalEvaluations++;
    
    // Emit evaluation event
    this.emit('threshold-evaluation', result);
    
    if (this.options.enableLogging && thresholdLevel !== 'normal') {
      console.log(`ThresholdManagement: ${domain} entropy (${entropyValue.toFixed(4)}) exceeded ${thresholdLevel} threshold (${violatedThreshold.toFixed(4)})`);
    }
    
    return result;
  }
  
  /**
   * Get thresholds
   * @param {string} domain - Domain (all domains if not specified)
   * @returns {Object} - Thresholds
   */
  getThresholds(domain) {
    if (domain && !['universal', 'cyber', 'financial', 'biological'].includes(domain)) {
      throw new Error(`Invalid domain: ${domain}`);
    }
    
    if (domain) {
      return { ...this.state.thresholds[domain] };
    }
    
    return JSON.parse(JSON.stringify(this.state.thresholds));
  }
  
  /**
   * Get threshold violations
   * @param {string} domain - Domain (all domains if not specified)
   * @param {number} limit - Maximum number of violations to return
   * @returns {Array} - Threshold violations
   */
  getThresholdViolations(domain, limit = 10) {
    if (domain && !['universal', 'cyber', 'financial', 'biological'].includes(domain)) {
      throw new Error(`Invalid domain: ${domain}`);
    }
    
    if (domain) {
      return this.state.thresholdViolations[domain].slice(0, limit);
    }
    
    const allViolations = [];
    for (const domainKey in this.state.thresholdViolations) {
      allViolations.push(...this.state.thresholdViolations[domainKey]);
    }
    
    // Sort by timestamp (newest first)
    allViolations.sort((a, b) => b.timestamp - a.timestamp);
    
    return allViolations.slice(0, limit);
  }
  
  /**
   * Get active alerts
   * @param {string} domain - Domain (all domains if not specified)
   * @returns {Array} - Active alerts
   */
  getActiveAlerts(domain) {
    const alerts = Array.from(this.state.activeAlerts.values());
    
    if (domain && !['universal', 'cyber', 'financial', 'biological'].includes(domain)) {
      throw new Error(`Invalid domain: ${domain}`);
    }
    
    if (domain) {
      return alerts.filter(alert => alert.domain === domain);
    }
    
    return alerts;
  }
  
  /**
   * Get threshold history
   * @param {number} limit - Maximum number of history items to return
   * @returns {Array} - Threshold history
   */
  getThresholdHistory(limit = 10) {
    return this.state.thresholdHistory.slice(0, limit);
  }
  
  /**
   * Get metrics
   * @returns {Object} - Metrics
   */
  getMetrics() {
    return { ...this.metrics };
  }
  
  /**
   * Generate alert
   * @param {string} domain - Domain
   * @param {string} level - Threshold level
   * @param {number} entropyValue - Entropy value
   * @param {number} threshold - Threshold value
   * @param {Object} metadata - Additional metadata
   * @private
   */
  _generateAlert(domain, level, entropyValue, threshold, metadata) {
    // Create alert ID
    const alertId = `alert-${domain}-${level}-${Date.now()}`;
    
    // Check if there's already an active alert for this domain and level
    const existingAlert = Array.from(this.state.activeAlerts.values()).find(
      alert => alert.domain === domain && alert.level === level && !alert.resolved
    );
    
    if (existingAlert) {
      // Update existing alert
      existingAlert.entropyValue = entropyValue;
      existingAlert.updatedAt = Date.now();
      existingAlert.updateCount++;
      
      // Emit alert update event
      this.emit('alert-update', existingAlert);
      
      if (this.options.enableLogging) {
        console.log(`ThresholdManagement: Updated ${domain} ${level} alert (${existingAlert.id})`);
      }
      
      return existingAlert;
    }
    
    // Create new alert
    const alert = {
      id: alertId,
      domain,
      level,
      entropyValue,
      threshold,
      deviation: entropyValue - threshold,
      createdAt: Date.now(),
      updatedAt: Date.now(),
      resolved: false,
      updateCount: 0,
      metadata
    };
    
    // Add to active alerts
    this.state.activeAlerts.set(alertId, alert);
    
    // Update metrics
    this.metrics.alertsGenerated++;
    
    // Emit alert event
    this.emit('alert-generated', alert);
    
    if (this.options.enableLogging) {
      console.log(`ThresholdManagement: Generated ${domain} ${level} alert (${alertId})`);
    }
    
    return alert;
  }
  
  /**
   * Resolve alerts
   * @param {string} domain - Domain
   * @param {number} entropyValue - Current entropy value
   * @private
   */
  _resolveAlerts(domain, entropyValue) {
    // Get active alerts for domain
    const domainAlerts = Array.from(this.state.activeAlerts.values()).filter(
      alert => alert.domain === domain && !alert.resolved
    );
    
    // Check each alert
    for (const alert of domainAlerts) {
      // If entropy value is below threshold, resolve alert
      if (entropyValue < alert.threshold) {
        alert.resolved = true;
        alert.resolvedAt = Date.now();
        alert.finalEntropyValue = entropyValue;
        
        // Update metrics
        this.metrics.alertsResolved++;
        
        // Emit alert resolved event
        this.emit('alert-resolved', alert);
        
        if (this.options.enableLogging) {
          console.log(`ThresholdManagement: Resolved ${domain} ${alert.level} alert (${alert.id})`);
        }
      }
    }
  }
}

module.exports = ThresholdManagement;
