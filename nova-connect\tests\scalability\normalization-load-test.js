/**
 * NovaConnect UAC Data Normalization Load Test
 * 
 * This script tests the performance and scalability of NovaConnect UAC data normalization.
 * 
 * Usage:
 * k6 run normalization-load-test.js
 */

import http from 'k6/http';
import { check, group, sleep } from 'k6';
import { Rate, Trend } from 'k6/metrics';
import { SharedArray } from 'k6/data';
import { randomItem, randomIntBetween } from 'https://jslib.k6.io/k6-utils/1.2.0/index.js';
import { options } from './k6-config.js';

// Custom metrics
const errorRate = new Rate('error_rate');
const dataNormalizationDuration = new Trend('data_normalization_duration');
const smallDataDuration = new Trend('small_data_duration');
const mediumDataDuration = new Trend('medium_data_duration');
const largeDataDuration = new Trend('large_data_duration');
const complexDataDuration = new Trend('complex_data_duration');

// Load test data
const testData = new SharedArray('test_data', function() {
  return JSON.parse(open('./test-data.json'));
});

// API base URL
const baseUrl = __ENV.API_BASE_URL || 'http://localhost:3001';

// API key for authentication
const apiKey = __ENV.API_KEY || 'test-api-key';

// Default headers
const headers = {
  'Content-Type': 'application/json',
  'X-API-Key': apiKey
};

/**
 * Setup function - called once per VU
 */
export function setup() {
  // Verify API is accessible
  const res = http.get(`${baseUrl}/health`);
  if (res.status !== 200) {
    throw new Error(`API is not accessible: ${res.status} ${res.body}`);
  }
  
  console.log('API is accessible, starting normalization load test');
  
  // Categorize test data by size
  const smallData = testData.normalizationRequests.filter(d => JSON.stringify(d).length < 1000);
  const mediumData = testData.normalizationRequests.filter(d => {
    const size = JSON.stringify(d).length;
    return size >= 1000 && size < 10000;
  });
  const largeData = testData.normalizationRequests.filter(d => JSON.stringify(d).length >= 10000);
  const complexData = testData.normalizationRequests.filter(d => {
    return d.nested && d.nested.depth > 3;
  });
  
  return {
    smallData,
    mediumData,
    largeData,
    complexData
  };
}

/**
 * Default function - called for each VU iteration
 */
export default function(data) {
  group('Data Normalization Performance', () => {
    // Test small data normalization
    testNormalization(data.smallData, smallDataDuration, 'small');
    
    // Test medium data normalization
    testNormalization(data.mediumData, mediumDataDuration, 'medium');
    
    // Test large data normalization
    testNormalization(data.largeData, largeDataDuration, 'large');
    
    // Test complex data normalization
    testNormalization(data.complexData, complexDataDuration, 'complex');
    
    // Small sleep to prevent overwhelming the server
    sleep(randomIntBetween(0.1, 0.3));
  });
  
  group('Batch Normalization Performance', () => {
    // Test small batch
    testBatchNormalization(data.smallData, 10, 'small');
    
    // Test medium batch
    testBatchNormalization(data.mediumData, 5, 'medium');
    
    // Test large batch
    testBatchNormalization(data.largeData, 2, 'large');
    
    // Small sleep to prevent overwhelming the server
    sleep(randomIntBetween(0.1, 0.3));
  });
  
  group('Concurrent Normalization', () => {
    // Test concurrent normalization
    testConcurrentNormalization(data);
    
    // Small sleep to prevent overwhelming the server
    sleep(randomIntBetween(0.1, 0.3));
  });
}

/**
 * Test data normalization
 */
function testNormalization(dataArray, durationMetric, sizeLabel) {
  if (!dataArray || dataArray.length === 0) {
    console.log(`No ${sizeLabel} data available for testing`);
    return;
  }
  
  // Get random test data
  const testItem = randomItem(dataArray);
  
  // Start timer
  const startTime = new Date();
  
  // Normalize data
  const normalizeRes = http.post(
    `${baseUrl}/api/normalize`,
    JSON.stringify(testItem),
    { headers }
  );
  
  // Calculate duration
  const duration = new Date() - startTime;
  
  // Record normalization duration
  durationMetric.add(duration);
  dataNormalizationDuration.add(duration);
  
  check(normalizeRes, {
    [`${sizeLabel} data normalize status is 200`]: (r) => r.status === 200,
    [`${sizeLabel} data normalize response time < threshold`]: (r) => {
      // Different thresholds based on data size
      const thresholds = {
        small: 10,
        medium: 50,
        large: 200,
        complex: 100
      };
      return r.timings.duration < thresholds[sizeLabel];
    },
    [`${sizeLabel} data normalize returns normalized data`]: (r) => JSON.parse(r.body).normalized !== undefined
  });
  
  errorRate.add(normalizeRes.status !== 200);
}

/**
 * Test batch data normalization
 */
function testBatchNormalization(dataArray, batchSize, sizeLabel) {
  if (!dataArray || dataArray.length === 0) {
    console.log(`No ${sizeLabel} data available for batch testing`);
    return;
  }
  
  // Create batch
  const batch = [];
  for (let i = 0; i < batchSize; i++) {
    batch.push(randomItem(dataArray));
  }
  
  // Start timer
  const startTime = new Date();
  
  // Normalize batch
  const batchNormalizeRes = http.post(
    `${baseUrl}/api/normalize/batch`,
    JSON.stringify({ items: batch }),
    { headers }
  );
  
  // Calculate duration
  const duration = new Date() - startTime;
  
  check(batchNormalizeRes, {
    [`${sizeLabel} batch normalize status is 200`]: (r) => r.status === 200,
    [`${sizeLabel} batch normalize response time < threshold`]: (r) => {
      // Different thresholds based on data size
      const thresholds = {
        small: 100,
        medium: 250,
        large: 500
      };
      return r.timings.duration < thresholds[sizeLabel];
    },
    [`${sizeLabel} batch normalize returns normalized data`]: (r) => {
      const body = JSON.parse(r.body);
      return Array.isArray(body.normalized) && body.normalized.length === batchSize;
    }
  });
  
  errorRate.add(batchNormalizeRes.status !== 200);
}

/**
 * Test concurrent data normalization
 */
function testConcurrentNormalization(data) {
  // Create requests
  const requests = [];
  
  // Add small data request
  if (data.smallData && data.smallData.length > 0) {
    requests.push({
      method: 'POST',
      url: `${baseUrl}/api/normalize`,
      body: JSON.stringify(randomItem(data.smallData)),
      params: { headers }
    });
  }
  
  // Add medium data request
  if (data.mediumData && data.mediumData.length > 0) {
    requests.push({
      method: 'POST',
      url: `${baseUrl}/api/normalize`,
      body: JSON.stringify(randomItem(data.mediumData)),
      params: { headers }
    });
  }
  
  // Add large data request
  if (data.largeData && data.largeData.length > 0) {
    requests.push({
      method: 'POST',
      url: `${baseUrl}/api/normalize`,
      body: JSON.stringify(randomItem(data.largeData)),
      params: { headers }
    });
  }
  
  // Add complex data request
  if (data.complexData && data.complexData.length > 0) {
    requests.push({
      method: 'POST',
      url: `${baseUrl}/api/normalize`,
      body: JSON.stringify(randomItem(data.complexData)),
      params: { headers }
    });
  }
  
  // Start timer
  const startTime = new Date();
  
  // Send concurrent requests
  const responses = http.batch(requests);
  
  // Calculate duration
  const duration = new Date() - startTime;
  
  // Check responses
  for (let i = 0; i < responses.length; i++) {
    const res = responses[i];
    
    check(res, {
      'concurrent normalize status is 200': (r) => r.status === 200,
      'concurrent normalize returns normalized data': (r) => JSON.parse(r.body).normalized !== undefined
    });
    
    errorRate.add(res.status !== 200);
  }
  
  // Check total duration
  check(null, {
    'concurrent normalize total time < threshold': () => duration < 500
  });
}
