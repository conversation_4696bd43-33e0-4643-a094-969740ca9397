[{"timestamp": "2025-04-27T00:32:17.157988", "strategy_id": "risk_based_prioritization", "result": {"strategy": "risk_based_prioritization", "risk_threshold": "high", "total_requirements": 6, "high_risk_requirements": 4, "high_risk_activities": 6, "recommendations": [{"type": "prioritize_high_risk", "count": 4, "message": "Prioritize 4 high-risk requirements"}]}}, {"timestamp": "2025-04-27T00:32:17.164901", "strategy_id": "resource_aware_scheduling", "result": {"strategy": "resource_aware_scheduling", "max_utilization": 0.8, "assigned_people": ["trainer", "auditor", "security_officer", "security_engineer", "privacy_officer", "developer"], "activities_per_person": {"trainer": 1, "auditor": 1, "security_officer": 1, "security_engineer": 1, "privacy_officer": 1, "developer": 1}, "utilization_per_person": {"trainer": 0.1, "auditor": 0.1, "security_officer": 0.1, "security_engineer": 0.1, "privacy_officer": 0.1, "developer": 0.1}, "overloaded_people": [], "recommendations": []}}, {"timestamp": "2025-04-27T00:32:17.174336", "strategy_id": "deadline_driven_optimization", "result": {"strategy": "deadline_driven_optimization", "urgency_factor": 1.5, "deadline_threshold": 15, "approaching_deadlines": 3, "recommendations": [{"type": "prioritize_deadline", "requirement_id": "a225861c-5975-4c76-be79-b577f71f7266", "requirement_name": "Incident Response", "days_until_due": 4, "urgency": 1.0999999999999999, "message": "Prioritize 'Incident Response' due in 4 days"}, {"type": "prioritize_deadline", "requirement_id": "ae593207-763f-4934-ad30-e10c385929ff", "requirement_name": "Data Breach Notification", "days_until_due": 9, "urgency": 0.6000000000000001, "message": "Prioritize 'Data Breach Notification' due in 9 days"}, {"type": "prioritize_deadline", "requirement_id": "a5aa1607-03a7-49a5-8fb0-18e40186692e", "requirement_name": "Data Subject Rights", "days_until_due": 14, "urgency": 0.1, "message": "Prioritize 'Data Subject Rights' due in 14 days"}]}}, {"timestamp": "2025-04-27T00:32:17.180863", "strategy_id": "risk_based_prioritization", "result": {"strategy": "risk_based_prioritization", "risk_threshold": "high", "total_requirements": 6, "high_risk_requirements": 4, "high_risk_activities": 6, "recommendations": [{"type": "prioritize_high_risk", "count": 4, "message": "Prioritize 4 high-risk requirements"}]}}, {"timestamp": "2025-04-27T00:32:17.191602", "strategy_id": "deadline_driven_optimization", "result": {"strategy": "deadline_driven_optimization", "urgency_factor": 2.0, "deadline_threshold": 15, "approaching_deadlines": 3, "recommendations": [{"type": "prioritize_deadline", "requirement_id": "a225861c-5975-4c76-be79-b577f71f7266", "requirement_name": "Incident Response", "days_until_due": 4, "urgency": 1.4666666666666666, "message": "Prioritize 'Incident Response' due in 4 days"}, {"type": "prioritize_deadline", "requirement_id": "ae593207-763f-4934-ad30-e10c385929ff", "requirement_name": "Data Breach Notification", "days_until_due": 9, "urgency": 0.8, "message": "Prioritize 'Data Breach Notification' due in 9 days"}, {"type": "prioritize_deadline", "requirement_id": "a5aa1607-03a7-49a5-8fb0-18e40186692e", "requirement_name": "Data Subject Rights", "days_until_due": 14, "urgency": 0.13333333333333333, "message": "Prioritize 'Data Subject Rights' due in 14 days"}]}}, {"timestamp": "2025-04-27T00:32:17.197088", "strategy_id": "resource_aware_scheduling", "result": {"strategy": "resource_aware_scheduling", "max_utilization": 0.8, "assigned_people": ["trainer", "auditor", "security_officer", "security_engineer", "privacy_officer", "developer"], "activities_per_person": {"trainer": 1, "auditor": 1, "security_officer": 1, "security_engineer": 1, "privacy_officer": 1, "developer": 1}, "utilization_per_person": {"trainer": 0.1, "auditor": 0.1, "security_officer": 0.1, "security_engineer": 0.1, "privacy_officer": 0.1, "developer": 0.1}, "overloaded_people": [], "recommendations": []}}, {"timestamp": "2025-04-27T00:32:17.208237", "strategy_id": "compliance_consolidation", "result": {"strategy": "compliance_consolidation", "consolidation_threshold": 0.7, "frameworks": ["GDPR", "SOC 2"], "requirements_by_framework": {"GDPR": 3, "SOC 2": 3}, "consolidation_opportunities": 0, "recommendations": []}}, {"timestamp": "2025-04-27T00:32:17.220637", "strategy_id": "deadline_driven_optimization", "result": {"strategy": "deadline_driven_optimization", "urgency_factor": 1.5, "deadline_threshold": 15, "approaching_deadlines": 3, "recommendations": [{"type": "prioritize_deadline", "requirement_id": "a225861c-5975-4c76-be79-b577f71f7266", "requirement_name": "Incident Response", "days_until_due": 4, "urgency": 1.0999999999999999, "message": "Prioritize 'Incident Response' due in 4 days"}, {"type": "prioritize_deadline", "requirement_id": "ae593207-763f-4934-ad30-e10c385929ff", "requirement_name": "Data Breach Notification", "days_until_due": 9, "urgency": 0.6000000000000001, "message": "Prioritize 'Data Breach Notification' due in 9 days"}, {"type": "prioritize_deadline", "requirement_id": "a5aa1607-03a7-49a5-8fb0-18e40186692e", "requirement_name": "Data Subject Rights", "days_until_due": 14, "urgency": 0.1, "message": "Prioritize 'Data Subject Rights' due in 14 days"}]}}]