import React, { useRef, useEffect, useState } from 'react';
import { Box, Typography, useTheme, CircularProgress } from '@mui/material';
import * as d3 from 'd3';

/**
 * CSED Trinity Visualization Component
 * 
 * Displays a visualization of the Trinity CSDE with 18/82 principle.
 * 
 * @param {Object} props - Component props
 * @param {Object} props.data - Trinity visualization data
 */
const CSEDTrinityVisualization = ({ data }) => {
  const theme = useTheme();
  const svgRef = useRef(null);
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });
  const [isLoading, setIsLoading] = useState(true);
  
  // If no data is available, show a message
  if (!data) {
    return (
      <Box sx={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100%' 
      }}>
        <Typography variant="body2" color="text.secondary">
          No Trinity visualization data available
        </Typography>
      </Box>
    );
  }
  
  // Get container dimensions on mount and window resize
  useEffect(() => {
    const updateDimensions = () => {
      if (svgRef.current) {
        const { width, height } = svgRef.current.parentElement.getBoundingClientRect();
        setDimensions({ width, height });
      }
    };
    
    // Initial update
    updateDimensions();
    
    // Add resize listener
    window.addEventListener('resize', updateDimensions);
    
    // Clean up
    return () => window.removeEventListener('resize', updateDimensions);
  }, []);
  
  // Create visualization when data and dimensions are available
  useEffect(() => {
    if (!data || !dimensions.width || !dimensions.height) return;
    
    setIsLoading(true);
    
    // Clear previous visualization
    d3.select(svgRef.current).selectAll('*').remove();
    
    // Create SVG
    const svg = d3.select(svgRef.current)
      .attr('width', dimensions.width)
      .attr('height', dimensions.height);
    
    // Define colors
    const colors = {
      father: theme.palette.error.main,
      son: theme.palette.success.main,
      spirit: theme.palette.info.main,
      background: theme.palette.background.paper,
      text: theme.palette.text.primary,
      textSecondary: theme.palette.text.secondary,
      highQuality: theme.palette.success.main,
      mediumQuality: theme.palette.warning.main,
      lowQuality: theme.palette.error.main
    };
    
    // Calculate center and radius
    const center = {
      x: dimensions.width / 2,
      y: dimensions.height / 2
    };
    
    const radius = Math.min(dimensions.width, dimensions.height) * 0.4;
    
    // Draw Trinity circle
    const trinityCircle = svg.append('circle')
      .attr('cx', center.x)
      .attr('cy', center.y)
      .attr('r', radius)
      .attr('fill', 'none')
      .attr('stroke', theme.palette.divider)
      .attr('stroke-width', 1)
      .attr('stroke-dasharray', '5,5');
    
    // Draw Trinity components
    const trinityComponents = data.components;
    
    // Calculate angles for each component
    const angleStep = (2 * Math.PI) / trinityComponents.length;
    
    // Draw components
    trinityComponents.forEach((component, i) => {
      const angle = i * angleStep;
      const x = center.x + Math.cos(angle) * radius * 0.8;
      const y = center.y + Math.sin(angle) * radius * 0.8;
      
      // Get component color
      const componentColor = colors[component.type] || theme.palette.primary.main;
      
      // Draw component circle
      const componentGroup = svg.append('g')
        .attr('transform', `translate(${x}, ${y})`);
      
      // Component circle
      componentGroup.append('circle')
        .attr('r', radius * 0.2)
        .attr('fill', componentColor)
        .attr('fill-opacity', 0.2)
        .attr('stroke', componentColor)
        .attr('stroke-width', 2);
      
      // Component name
      componentGroup.append('text')
        .attr('text-anchor', 'middle')
        .attr('dy', -radius * 0.1)
        .attr('fill', colors.text)
        .attr('font-weight', 'bold')
        .attr('font-size', radius * 0.1)
        .text(component.name);
      
      // Component score
      componentGroup.append('text')
        .attr('text-anchor', 'middle')
        .attr('dy', radius * 0.05)
        .attr('fill', colors.text)
        .attr('font-size', radius * 0.15)
        .text(component.score.toFixed(2));
      
      // Component quality
      const qualityColor = component.qualityScore >= 0.8 
        ? colors.highQuality 
        : component.qualityScore >= 0.6 
          ? colors.mediumQuality 
          : colors.lowQuality;
      
      componentGroup.append('text')
        .attr('text-anchor', 'middle')
        .attr('dy', radius * 0.15)
        .attr('fill', qualityColor)
        .attr('font-size', radius * 0.08)
        .text(`Quality: ${(component.qualityScore * 100).toFixed(0)}%`);
      
      // Draw connection to center
      svg.append('line')
        .attr('x1', x)
        .attr('y1', y)
        .attr('x2', center.x)
        .attr('y2', center.y)
        .attr('stroke', componentColor)
        .attr('stroke-width', 2)
        .attr('stroke-opacity', 0.6)
        .attr('stroke-dasharray', '5,5');
    });
    
    // Draw center circle with CSDE value
    const centerGroup = svg.append('g')
      .attr('transform', `translate(${center.x}, ${center.y})`);
    
    // Center circle
    centerGroup.append('circle')
      .attr('r', radius * 0.3)
      .attr('fill', theme.palette.primary.main)
      .attr('fill-opacity', 0.1)
      .attr('stroke', theme.palette.primary.main)
      .attr('stroke-width', 2);
    
    // CSDE label
    centerGroup.append('text')
      .attr('text-anchor', 'middle')
      .attr('dy', -radius * 0.1)
      .attr('fill', colors.text)
      .attr('font-weight', 'bold')
      .attr('font-size', radius * 0.12)
      .text('CSDE');
    
    // CSDE value
    centerGroup.append('text')
      .attr('text-anchor', 'middle')
      .attr('dy', radius * 0.05)
      .attr('fill', colors.text)
      .attr('font-weight', 'bold')
      .attr('font-size', radius * 0.18)
      .text(data.csdeValue.toFixed(2));
    
    // Performance factor
    centerGroup.append('text')
      .attr('text-anchor', 'middle')
      .attr('dy', radius * 0.15)
      .attr('fill', colors.textSecondary)
      .attr('font-size', radius * 0.08)
      .text(`${data.performanceFactor}× Performance`);
    
    // Draw 18/82 principle indicators
    if (data.principle1882) {
      // Draw 18% circle
      const eighteenPercentRadius = radius * 0.18;
      svg.append('circle')
        .attr('cx', center.x)
        .attr('cy', center.y)
        .attr('r', eighteenPercentRadius)
        .attr('fill', 'none')
        .attr('stroke', theme.palette.secondary.main)
        .attr('stroke-width', 1)
        .attr('stroke-dasharray', '3,3');
      
      // Draw 82% circle
      const eightyTwoPercentRadius = radius * 0.82;
      svg.append('circle')
        .attr('cx', center.x)
        .attr('cy', center.y)
        .attr('r', eightyTwoPercentRadius)
        .attr('fill', 'none')
        .attr('stroke', theme.palette.secondary.main)
        .attr('stroke-width', 1)
        .attr('stroke-dasharray', '3,3');
      
      // Add 18/82 labels
      svg.append('text')
        .attr('x', center.x + eighteenPercentRadius * Math.cos(Math.PI / 4))
        .attr('y', center.y + eighteenPercentRadius * Math.sin(Math.PI / 4))
        .attr('text-anchor', 'middle')
        .attr('fill', theme.palette.secondary.main)
        .attr('font-size', radius * 0.06)
        .text('18%');
      
      svg.append('text')
        .attr('x', center.x + eightyTwoPercentRadius * Math.cos(Math.PI / 4))
        .attr('y', center.y + eightyTwoPercentRadius * Math.sin(Math.PI / 4))
        .attr('text-anchor', 'middle')
        .attr('fill', theme.palette.secondary.main)
        .attr('font-size', radius * 0.06)
        .text('82%');
    }
    
    // Add title
    svg.append('text')
      .attr('x', 20)
      .attr('y', 30)
      .attr('fill', colors.text)
      .attr('font-weight', 'bold')
      .attr('font-size', 16)
      .text('Trinity CSDE Visualization');
    
    // Add subtitle
    svg.append('text')
      .attr('x', 20)
      .attr('y', 50)
      .attr('fill', colors.textSecondary)
      .attr('font-size', 12)
      .text(`Calculated at: ${new Date(data.calculatedAt).toLocaleString()}`);
    
    setIsLoading(false);
  }, [data, dimensions, theme]);
  
  return (
    <Box sx={{ 
      position: 'relative',
      width: '100%', 
      height: '100%', 
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center'
    }}>
      {isLoading && (
        <Box sx={{ 
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          zIndex: 1,
          bgcolor: 'rgba(255, 255, 255, 0.7)'
        }}>
          <CircularProgress />
        </Box>
      )}
      <svg ref={svgRef} width="100%" height="100%" />
    </Box>
  );
};

export default CSEDTrinityVisualization;
