/**
 * Zero Trust Middleware
 * 
 * This middleware implements Zero Trust principles for the NovaFuse API.
 * It verifies every request regardless of source and applies the principle
 * of least privilege.
 */

const jwt = require('jsonwebtoken');
const { v4: uuidv4 } = require('uuid');
const logger = require('../utils/logger');
const config = require('../../config');
const encryption = require('../utils/encryption');

/**
 * Context-aware authentication middleware
 * Implements the "never trust, always verify" principle
 */
function contextAwareAuth(req, res, next) {
  try {
    // Skip authentication for public routes
    if (isPublicRoute(req.path)) {
      return next();
    }
    
    // Get authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader) {
      logger.warn('Missing authorization header', { path: req.path, ip: req.ip });
      return res.status(401).json({ error: 'Authentication required' });
    }
    
    // Extract token
    const token = authHeader.startsWith('Bearer ') 
      ? authHeader.substring(7) 
      : authHeader;
    
    // Verify token
    jwt.verify(token, config.jwt.secretKey, (err, decoded) => {
      if (err) {
        logger.warn('Invalid token', { path: req.path, ip: req.ip, error: err.message });
        return res.status(401).json({ error: 'Invalid token' });
      }
      
      // Check token expiration with buffer time
      const now = Math.floor(Date.now() / 1000);
      const bufferTime = 60; // 1 minute buffer
      
      if (decoded.exp && decoded.exp - bufferTime < now) {
        logger.warn('Token about to expire', { 
          path: req.path, 
          ip: req.ip, 
          userId: decoded.id,
          expiresIn: decoded.exp - now
        });
        
        // Set header to indicate token is about to expire
        res.setHeader('X-Token-Expiring', 'true');
      }
      
      // Verify user context
      verifyUserContext(req, decoded)
        .then(contextValid => {
          if (!contextValid) {
            logger.warn('Invalid user context', { 
              path: req.path, 
              ip: req.ip, 
              userId: decoded.id 
            });
            return res.status(401).json({ error: 'Context validation failed' });
          }
          
          // Set user in request
          req.user = decoded;
          
          // Generate session trace ID if not exists
          if (!req.headers['x-session-trace']) {
            const traceId = uuidv4();
            req.sessionTrace = traceId;
            res.setHeader('X-Session-Trace', traceId);
          } else {
            req.sessionTrace = req.headers['x-session-trace'];
          }
          
          // Log successful authentication
          logger.info('User authenticated', { 
            userId: decoded.id, 
            path: req.path,
            sessionTrace: req.sessionTrace
          });
          
          next();
        })
        .catch(error => {
          logger.error('Context verification error', { 
            path: req.path, 
            ip: req.ip, 
            error: error.message 
          });
          return res.status(500).json({ error: 'Authentication error' });
        });
    });
  } catch (error) {
    logger.error('Authentication error', { path: req.path, ip: req.ip, error: error.message });
    return res.status(500).json({ error: 'Authentication error' });
  }
}

/**
 * Verify user context based on various factors
 * @param {Object} req - Express request object
 * @param {Object} user - Decoded user from token
 * @returns {Promise<boolean>} - Whether context is valid
 */
async function verifyUserContext(req, user) {
  try {
    // Check IP address if previous IP is known
    if (user.lastIp && user.lastIp !== req.ip) {
      // IP changed - increase risk score
      // In a real implementation, this would trigger additional verification
      logger.warn('IP address changed', { 
        userId: user.id, 
        previousIp: user.lastIp, 
        currentIp: req.ip 
      });
      
      // For high-risk operations, this might return false
      // For demo purposes, we'll continue but log the change
    }
    
    // Check user agent if previous user agent is known
    const currentUserAgent = req.headers['user-agent'];
    if (user.lastUserAgent && user.lastUserAgent !== currentUserAgent) {
      // User agent changed - increase risk score
      logger.warn('User agent changed', { 
        userId: user.id, 
        previousUserAgent: user.lastUserAgent, 
        currentUserAgent 
      });
      
      // For high-risk operations, this might return false
      // For demo purposes, we'll continue but log the change
    }
    
    // Check if user has required permissions for this endpoint
    const hasPermission = await checkPermission(user, req.method, req.path);
    if (!hasPermission) {
      logger.warn('Permission denied', { 
        userId: user.id, 
        method: req.method, 
        path: req.path 
      });
      return false;
    }
    
    // Additional context checks could be added here:
    // - Time-based access restrictions
    // - Geolocation verification
    // - Device posture assessment
    // - Behavioral analysis
    
    return true;
  } catch (error) {
    logger.error('Context verification error', { error: error.message });
    throw error;
  }
}

/**
 * Check if user has permission for the requested endpoint
 * @param {Object} user - User object
 * @param {string} method - HTTP method
 * @param {string} path - Request path
 * @returns {Promise<boolean>} - Whether user has permission
 */
async function checkPermission(user, method, path) {
  try {
    // In a real implementation, this would check against a permission database
    // For demo purposes, we'll use a simple role-based check
    
    // Admin has access to everything
    if (user.role === 'admin') {
      return true;
    }
    
    // Check if path is restricted to admins
    const adminOnlyPaths = [
      '/api/v1/admin',
      '/api/v1/users'
    ];
    
    if (adminOnlyPaths.some(p => path.startsWith(p))) {
      return user.role === 'admin';
    }
    
    // Organization-specific paths
    if (path.includes('/organizations/')) {
      // Extract organization ID from path
      const pathParts = path.split('/');
      const orgIndex = pathParts.indexOf('organizations');
      
      if (orgIndex !== -1 && pathParts.length > orgIndex + 1) {
        const orgId = pathParts[orgIndex + 1];
        
        // Check if user belongs to this organization
        return user.organizationId === orgId;
      }
    }
    
    // Default to allowing access for authenticated users
    return true;
  } catch (error) {
    logger.error('Permission check error', { error: error.message });
    return false;
  }
}

/**
 * Check if route is public (doesn't require authentication)
 * @param {string} path - Request path
 * @returns {boolean} - Whether route is public
 */
function isPublicRoute(path) {
  const publicRoutes = [
    '/api/v1/auth/login',
    '/api/v1/auth/register',
    '/api/v1/auth/forgot-password',
    '/api/v1/auth/reset-password',
    '/api/v1/health',
    '/api/v1/docs'
  ];
  
  return publicRoutes.some(route => path.startsWith(route));
}

/**
 * Least privilege middleware
 * Ensures users only have access to the minimum resources necessary
 */
function leastPrivilege(req, res, next) {
  try {
    // Skip for public routes
    if (isPublicRoute(req.path)) {
      return next();
    }
    
    // Skip if no user (should not happen due to previous middleware)
    if (!req.user) {
      return res.status(401).json({ error: 'Authentication required' });
    }
    
    // Check specific permissions based on endpoint
    // This would be more sophisticated in a real implementation
    
    // Example: Only allow users to access their own data
    if (req.path.startsWith('/api/v1/users/') && !req.path.includes('me')) {
      const userId = req.path.split('/')[4]; // Extract user ID from path
      
      if (userId && userId !== req.user.id && req.user.role !== 'admin') {
        logger.warn('Attempted to access another user\'s data', { 
          userId: req.user.id, 
          targetUserId: userId, 
          path: req.path 
        });
        return res.status(403).json({ error: 'Access denied' });
      }
    }
    
    // Example: Organization-specific access control
    if (req.path.includes('/organizations/')) {
      const orgId = req.path.split('/organizations/')[1]?.split('/')[0];
      
      if (orgId && orgId !== req.user.organizationId && req.user.role !== 'admin') {
        logger.warn('Attempted to access another organization\'s data', { 
          userId: req.user.id, 
          userOrgId: req.user.organizationId,
          targetOrgId: orgId, 
          path: req.path 
        });
        return res.status(403).json({ error: 'Access denied' });
      }
    }
    
    next();
  } catch (error) {
    logger.error('Least privilege error', { path: req.path, error: error.message });
    return res.status(500).json({ error: 'Authorization error' });
  }
}

/**
 * Request validation middleware
 * Validates all incoming requests to prevent injection attacks
 */
function validateRequests(req, res, next) {
  try {
    // Skip for GET and OPTIONS requests
    if (['GET', 'OPTIONS'].includes(req.method)) {
      return next();
    }
    
    // Validate request body if present
    if (req.body && Object.keys(req.body).length > 0) {
      // Check for suspicious patterns
      const bodyString = JSON.stringify(req.body);
      
      // Check for potential SQL injection
      const sqlInjectionPattern = /('|").*(\s|;)+(SELECT|INSERT|UPDATE|DELETE|DROP|ALTER|UNION)/i;
      if (sqlInjectionPattern.test(bodyString)) {
        logger.warn('Potential SQL injection detected', { 
          path: req.path, 
          ip: req.ip,
          userId: req.user?.id
        });
        return res.status(400).json({ error: 'Invalid request' });
      }
      
      // Check for potential NoSQL injection
      const noSqlInjectionPattern = /(\$where|\$ne|\$gt|\$lt|\$exists|\$regex).*:/i;
      if (noSqlInjectionPattern.test(bodyString)) {
        logger.warn('Potential NoSQL injection detected', { 
          path: req.path, 
          ip: req.ip,
          userId: req.user?.id
        });
        return res.status(400).json({ error: 'Invalid request' });
      }
      
      // Check for potential XSS
      const xssPattern = /<script|javascript:|on\w+\s*=|<iframe|<img[^>]+src[^>]*onerror/i;
      if (xssPattern.test(bodyString)) {
        logger.warn('Potential XSS detected', { 
          path: req.path, 
          ip: req.ip,
          userId: req.user?.id
        });
        return res.status(400).json({ error: 'Invalid request' });
      }
    }
    
    next();
  } catch (error) {
    logger.error('Request validation error', { path: req.path, error: error.message });
    return res.status(500).json({ error: 'Request validation error' });
  }
}

/**
 * Response security middleware
 * Ensures responses don't leak sensitive information
 */
function secureResponses(req, res, next) {
  // Store the original send function
  const originalSend = res.send;
  
  // Override the send function
  res.send = function(body) {
    try {
      // Skip for non-JSON responses
      if (typeof body !== 'string' || !body.startsWith('{')) {
        return originalSend.call(this, body);
      }
      
      // Parse JSON response
      const responseObj = JSON.parse(body);
      
      // Remove sensitive fields
      const sanitizedResponse = sanitizeResponse(responseObj);
      
      // Send sanitized response
      return originalSend.call(this, JSON.stringify(sanitizedResponse));
    } catch (error) {
      // If any error occurs, send the original response
      logger.error('Response sanitization error', { path: req.path, error: error.message });
      return originalSend.call(this, body);
    }
  };
  
  next();
}

/**
 * Sanitize response to remove sensitive information
 * @param {Object} response - Response object
 * @returns {Object} - Sanitized response
 */
function sanitizeResponse(response) {
  // Deep clone the response
  const sanitized = JSON.parse(JSON.stringify(response));
  
  // List of sensitive fields to remove
  const sensitiveFields = [
    'password',
    'passwordHash',
    'secret',
    'token',
    'apiKey',
    'privateKey',
    'ssn',
    'socialSecurityNumber',
    'creditCard',
    'creditCardNumber'
  ];
  
  // Recursively sanitize the response
  function sanitizeObject(obj) {
    if (!obj || typeof obj !== 'object') {
      return;
    }
    
    for (const key of Object.keys(obj)) {
      // Check if key is sensitive
      if (sensitiveFields.some(field => key.toLowerCase().includes(field.toLowerCase()))) {
        // Redact sensitive field
        obj[key] = '[REDACTED]';
      } else if (typeof obj[key] === 'object') {
        // Recursively sanitize nested objects
        sanitizeObject(obj[key]);
      }
    }
  }
  
  sanitizeObject(sanitized);
  return sanitized;
}

// Export middleware functions
module.exports = {
  contextAwareAuth,
  leastPrivilege,
  validateRequests,
  secureResponses
};
