/**
 * NovaCore Regulatory Change Controller
 * 
 * This controller handles API requests related to regulatory changes.
 * NovaPulse is the Universal Regulatory Compliance Monitoring System (URCMS) component
 * of the NovaFuse Cyber-Safety Platform.
 */

const { RegulatoryChangeService } = require('../services');
const logger = require('../../../config/logger');

class RegulatoryChangeController {
  /**
   * Create a new regulatory change
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async createRegulatoryChange(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      
      // Set organization ID from params
      req.body.organizationId = req.params.organizationId;
      
      const regulatoryChange = await RegulatoryChangeService.createRegulatoryChange(req.body, userId);
      
      res.status(201).json({
        success: true,
        data: regulatoryChange
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Get all regulatory changes
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async getAllRegulatoryChanges(req, res, next) {
    try {
      const organizationId = req.params.organizationId;
      
      // Extract filter criteria from query params
      const filter = {};
      
      if (req.query.regulationId) filter.regulationId = req.query.regulationId;
      if (req.query.type) filter.type = req.query.type;
      if (req.query.category) filter.category = req.query.category;
      if (req.query.status) filter.status = req.query.status;
      if (req.query.priority) filter.priority = req.query.priority;
      if (req.query.country) filter.country = req.query.country;
      if (req.query.region) filter.region = req.query.region;
      if (req.query.industry) filter.industry = req.query.industry;
      if (req.query.organizationType) filter.organizationType = req.query.organizationType;
      if (req.query.dataType) filter.dataType = req.query.dataType;
      if (req.query.implementationStatus) filter.implementationStatus = req.query.implementationStatus;
      if (req.query.effectiveDateFrom) filter.effectiveDateFrom = req.query.effectiveDateFrom;
      if (req.query.effectiveDateTo) filter.effectiveDateTo = req.query.effectiveDateTo;
      if (req.query.complianceDeadlineFrom) filter.complianceDeadlineFrom = req.query.complianceDeadlineFrom;
      if (req.query.complianceDeadlineTo) filter.complianceDeadlineTo = req.query.complianceDeadlineTo;
      if (req.query.search) filter.search = req.query.search;
      
      // Extract pagination and sorting options
      const options = {
        page: parseInt(req.query.page, 10) || 1,
        limit: parseInt(req.query.limit, 10) || 10
      };
      
      if (req.query.sort) {
        const [field, order] = req.query.sort.split(':');
        options.sort = { [field]: order === 'desc' ? -1 : 1 };
      }
      
      const result = await RegulatoryChangeService.getAllRegulatoryChanges(organizationId, filter, options);
      
      res.status(200).json({
        success: true,
        data: result.data,
        pagination: result.pagination
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Get regulatory change by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async getRegulatoryChangeById(req, res, next) {
    try {
      const regulatoryChange = await RegulatoryChangeService.getRegulatoryChangeById(req.params.id);
      
      res.status(200).json({
        success: true,
        data: regulatoryChange
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Update regulatory change
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async updateRegulatoryChange(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const regulatoryChange = await RegulatoryChangeService.updateRegulatoryChange(req.params.id, req.body, userId);
      
      res.status(200).json({
        success: true,
        data: regulatoryChange
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Delete regulatory change
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async deleteRegulatoryChange(req, res, next) {
    try {
      await RegulatoryChangeService.deleteRegulatoryChange(req.params.id);
      
      res.status(200).json({
        success: true,
        message: 'Regulatory change deleted successfully'
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Add impact assessment
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async addImpactAssessment(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const regulatoryChange = await RegulatoryChangeService.addImpactAssessment(req.params.id, req.body, userId);
      
      res.status(200).json({
        success: true,
        data: regulatoryChange
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Update implementation status
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async updateImplementationStatus(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const regulatoryChange = await RegulatoryChangeService.updateImplementationStatus(req.params.id, req.body, userId);
      
      res.status(200).json({
        success: true,
        data: regulatoryChange
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Add notification
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async addNotification(req, res, next) {
    try {
      const regulatoryChange = await RegulatoryChangeService.addNotification(req.params.id, req.body);
      
      res.status(200).json({
        success: true,
        data: regulatoryChange
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Update notification status
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async updateNotificationStatus(req, res, next) {
    try {
      const { status, error } = req.body;
      const regulatoryChange = await RegulatoryChangeService.updateNotificationStatus(
        req.params.id, 
        req.params.notificationId, 
        status, 
        error
      );
      
      res.status(200).json({
        success: true,
        data: regulatoryChange
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Add related workflow
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async addRelatedWorkflow(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const regulatoryChange = await RegulatoryChangeService.addRelatedWorkflow(req.params.id, req.body, userId);
      
      res.status(200).json({
        success: true,
        data: regulatoryChange
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Update related workflow status
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async updateRelatedWorkflowStatus(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const { status } = req.body;
      const regulatoryChange = await RegulatoryChangeService.updateRelatedWorkflowStatus(
        req.params.id, 
        req.params.workflowId, 
        status, 
        userId
      );
      
      res.status(200).json({
        success: true,
        data: regulatoryChange
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Find pending regulatory changes
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async findPending(req, res, next) {
    try {
      const organizationId = req.params.organizationId;
      const regulatoryChanges = await RegulatoryChangeService.findPending(organizationId);
      
      res.status(200).json({
        success: true,
        data: regulatoryChanges
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Find overdue regulatory changes
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async findOverdue(req, res, next) {
    try {
      const organizationId = req.params.organizationId;
      const regulatoryChanges = await RegulatoryChangeService.findOverdue(organizationId);
      
      res.status(200).json({
        success: true,
        data: regulatoryChanges
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Find upcoming regulatory changes
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async findUpcoming(req, res, next) {
    try {
      const organizationId = req.params.organizationId;
      const days = parseInt(req.query.days, 10) || 30;
      const regulatoryChanges = await RegulatoryChangeService.findUpcoming(organizationId, days);
      
      res.status(200).json({
        success: true,
        data: regulatoryChanges
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Find regulatory changes by impact level
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async findByImpactLevel(req, res, next) {
    try {
      const organizationId = req.params.organizationId;
      const level = req.params.level;
      const regulatoryChanges = await RegulatoryChangeService.findByImpactLevel(organizationId, level);
      
      res.status(200).json({
        success: true,
        data: regulatoryChanges
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Find regulatory changes by applicability
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async findByApplicability(req, res, next) {
    try {
      const organizationId = req.params.organizationId;
      const criteria = {
        industry: req.query.industry,
        region: req.query.region,
        organizationType: req.query.organizationType,
        dataTypes: req.query.dataTypes ? req.query.dataTypes.split(',') : undefined
      };
      
      const regulatoryChanges = await RegulatoryChangeService.findByApplicability(organizationId, criteria);
      
      res.status(200).json({
        success: true,
        data: regulatoryChanges
      });
    } catch (error) {
      next(error);
    }
  }
}

module.exports = new RegulatoryChangeController();
