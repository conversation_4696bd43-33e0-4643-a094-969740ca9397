const WebSocket = require('ws');
const calculateTriadicMetrics = require('../src/lib/triadicMetrics');

const wss = new WebSocket.Server({ port: 8080 });

// Sample data for consciousness marketing metrics
const sampleData = {
  conversions: [
    { revenue: 150, status: 'completed' },
    { revenue: 200, status: 'completed' },
    { revenue: 180, status: 'pending' },
    { revenue: 120, status: 'completed' }
  ],
  products: [
    { price: 199, status: 'active', conversions: 15 },
    { price: 299, status: 'active', conversions: 10 },
    { price: 99, status: 'inactive', conversions: 5 }
  ]
};

// Consciousness marketing metrics calculation
function calculateConsciousnessMetrics(conversions, products) {
  const triadicMetrics = calculateTriadicMetrics(conversions, products);
  
  // Additional consciousness metrics
  const customerSatisfaction = (conversions.filter(c => c.status === 'completed').length / conversions.length) * 100;
  const productQualityScore = (products.filter(p => p.status === 'active').length / products.length) * 100;
  const socialImpactScore = (triadicMetrics.psi + triadicMetrics.phi + triadicMetrics.kappa) / 3;
  
  return {
    ...triadicMetrics,
    customerSatisfaction: Math.round(customerSatisfaction),
    productQualityScore: Math.round(productQualityScore),
    socialImpactScore: Math.round(socialImpactScore)
  };
}

wss.on('connection', function connection(ws) {
  console.log('Client connected');
  
  // Send initial metrics
  const metrics = calculateConsciousnessMetrics(sampleData.conversions, sampleData.products);
  ws.send(JSON.stringify({ type: 'metrics', data: metrics }));
  
  // Simulate real-time updates every 5 seconds
  setInterval(() => {
    // Update sample data with new conversions
    sampleData.conversions.push({
      revenue: Math.floor(Math.random() * 200) + 100,
      status: Math.random() > 0.2 ? 'completed' : 'pending'
    });
    
    // Update metrics
    const metrics = calculateConsciousnessMetrics(sampleData.conversions, sampleData.products);
    ws.send(JSON.stringify({ type: 'metrics', data: metrics }));
  }, 5000);
});

console.log('WebSocket server is running on ws://localhost:8080');
