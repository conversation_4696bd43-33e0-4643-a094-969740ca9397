/**
 * NovaCore Vendor Assessment Routes
 * 
 * This file defines the routes for the vendor assessment module.
 */

const express = require('express');
const router = express.Router();
const multer = require('multer');
const { 
  VendorController, 
  AssessmentController, 
  AssessmentTemplateController, 
  DocumentController 
} = require('./controllers');
const { authenticate, authorize } = require('../../api/middleware/authMiddleware');

// Configure multer for file uploads
const upload = multer({ 
  dest: 'uploads/',
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB limit
  }
});

// Vendor Routes

// Create a new vendor
router.post(
  '/vendors',
  authenticate,
  authorize('create:vendor'),
  VendorController.createVendor
);

// Get all vendors
router.get(
  '/organizations/:organizationId/vendors',
  authenticate,
  authorize('read:vendor'),
  VendorController.getAllVendors
);

// Get vendor by ID
router.get(
  '/vendors/:id',
  authenticate,
  authorize('read:vendor'),
  VendorController.getVendorById
);

// Update vendor
router.put(
  '/vendors/:id',
  authenticate,
  authorize('update:vendor'),
  VendorController.updateVendor
);

// Delete vendor
router.delete(
  '/vendors/:id',
  authenticate,
  authorize('delete:vendor'),
  VendorController.deleteVendor
);

// Get vendor assessments
router.get(
  '/vendors/:id/assessments',
  authenticate,
  authorize('read:vendor'),
  VendorController.getVendorAssessments
);

// Get vendor documents
router.get(
  '/vendors/:id/documents',
  authenticate,
  authorize('read:vendor'),
  VendorController.getVendorDocuments
);

// Update vendor compliance status
router.put(
  '/vendors/:id/compliance',
  authenticate,
  authorize('update:vendor'),
  VendorController.updateComplianceStatus
);

// Update vendor risk score
router.put(
  '/vendors/:id/risk-score',
  authenticate,
  authorize('update:vendor'),
  VendorController.updateRiskScore
);

// Update vendor Cyber-Safety certification
router.put(
  '/vendors/:id/cyber-safety-certification',
  authenticate,
  authorize('update:vendor'),
  VendorController.updateCyberSafetyCertification
);

// Get vendor dashboard
router.get(
  '/organizations/:organizationId/vendor-dashboard',
  authenticate,
  authorize('read:vendor'),
  VendorController.getVendorDashboard
);

// Assessment Routes

// Create a new assessment
router.post(
  '/assessments',
  authenticate,
  authorize('create:assessment'),
  AssessmentController.createAssessment
);

// Get all assessments
router.get(
  '/organizations/:organizationId/assessments',
  authenticate,
  authorize('read:assessment'),
  AssessmentController.getAllAssessments
);

// Get assessment by ID
router.get(
  '/assessments/:id',
  authenticate,
  authorize('read:assessment'),
  AssessmentController.getAssessmentById
);

// Update assessment
router.put(
  '/assessments/:id',
  authenticate,
  authorize('update:assessment'),
  AssessmentController.updateAssessment
);

// Delete assessment
router.delete(
  '/assessments/:id',
  authenticate,
  authorize('delete:assessment'),
  AssessmentController.deleteAssessment
);

// Submit answer for assessment
router.post(
  '/assessments/:id/answers',
  authenticate,
  authorize('update:assessment'),
  AssessmentController.submitAnswer
);

// Submit assessment for review
router.post(
  '/assessments/:id/submit',
  authenticate,
  authorize('update:assessment'),
  AssessmentController.submitForReview
);

// Complete assessment review
router.post(
  '/assessments/:id/review',
  authenticate,
  authorize('review:assessment'),
  AssessmentController.completeReview
);

// Add finding to assessment
router.post(
  '/assessments/:id/findings',
  authenticate,
  authorize('update:assessment'),
  AssessmentController.addFinding
);

// Update finding in assessment
router.put(
  '/assessments/:id/findings/:findingId',
  authenticate,
  authorize('update:assessment'),
  AssessmentController.updateFinding
);

// Get assessment documents
router.get(
  '/assessments/:id/documents',
  authenticate,
  authorize('read:assessment'),
  AssessmentController.getAssessmentDocuments
);

// Assessment Template Routes

// Create a new assessment template
router.post(
  '/assessment-templates',
  authenticate,
  authorize('create:assessment-template'),
  AssessmentTemplateController.createTemplate
);

// Get all assessment templates
router.get(
  '/organizations/:organizationId/assessment-templates',
  authenticate,
  authorize('read:assessment-template'),
  AssessmentTemplateController.getAllTemplates
);

// Get assessment template by ID
router.get(
  '/assessment-templates/:id',
  authenticate,
  authorize('read:assessment-template'),
  AssessmentTemplateController.getTemplateById
);

// Update assessment template
router.put(
  '/assessment-templates/:id',
  authenticate,
  authorize('update:assessment-template'),
  AssessmentTemplateController.updateTemplate
);

// Delete assessment template
router.delete(
  '/assessment-templates/:id',
  authenticate,
  authorize('delete:assessment-template'),
  AssessmentTemplateController.deleteTemplate
);

// Set template as default
router.post(
  '/assessment-templates/:id/set-default',
  authenticate,
  authorize('update:assessment-template'),
  AssessmentTemplateController.setAsDefault
);

// Create new version of template
router.post(
  '/assessment-templates/:id/versions',
  authenticate,
  authorize('create:assessment-template'),
  AssessmentTemplateController.createNewVersion
);

// Get default template
router.get(
  '/organizations/:organizationId/default-template',
  authenticate,
  authorize('read:assessment-template'),
  AssessmentTemplateController.getDefaultTemplate
);

// Import template from JSON
router.post(
  '/organizations/:organizationId/import-template',
  authenticate,
  authorize('create:assessment-template'),
  AssessmentTemplateController.importTemplate
);

// Document Routes

// Create a new document
router.post(
  '/documents',
  authenticate,
  authorize('create:document'),
  upload.single('file'),
  DocumentController.createDocument
);

// Get all documents
router.get(
  '/organizations/:organizationId/documents',
  authenticate,
  authorize('read:document'),
  DocumentController.getAllDocuments
);

// Get document by ID
router.get(
  '/documents/:id',
  authenticate,
  authorize('read:document'),
  DocumentController.getDocumentById
);

// Get document content
router.get(
  '/documents/:id/content',
  authenticate,
  authorize('read:document'),
  DocumentController.getDocumentContent
);

// Update document
router.put(
  '/documents/:id',
  authenticate,
  authorize('update:document'),
  upload.single('file'),
  DocumentController.updateDocument
);

// Delete document
router.delete(
  '/documents/:id',
  authenticate,
  authorize('delete:document'),
  DocumentController.deleteDocument
);

// Verify document with blockchain
router.post(
  '/documents/:id/verify',
  authenticate,
  authorize('verify:document'),
  DocumentController.verifyDocument
);

// Check verification status for document
router.get(
  '/documents/:id/verification-status',
  authenticate,
  authorize('read:document'),
  DocumentController.checkVerificationStatus
);

module.exports = router;
