/**
 * NovaCore SOC 2 Evidence Model
 * 
 * This model defines the schema for SOC 2 evidence records.
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

// Define SOC 2 control schema
const soc2ControlSchema = new Schema({
  id: { 
    type: String, 
    required: true, 
    trim: true 
  },
  reference: { 
    type: String, 
    required: true, 
    trim: true 
  },
  title: { 
    type: String, 
    required: true, 
    trim: true 
  },
  categories: [{ 
    type: String, 
    trim: true 
  }]
}, { _id: false });

// Define evidence metadata schema
const evidenceMetadataSchema = new Schema({
  source: { 
    type: String, 
    required: true, 
    trim: true 
  },
  sourceType: { 
    type: String, 
    enum: ['aws', 'gcp', 'azure', 'github', 'jira', 'manual', 'other'], 
    default: 'manual' 
  },
  collectionMethod: { 
    type: String, 
    enum: ['automated', 'manual', 'hybrid'], 
    default: 'manual' 
  },
  collectedBy: { 
    type: String, 
    trim: true 
  },
  collectionDate: { 
    type: Date, 
    default: Date.now 
  },
  expiryDate: { 
    type: Date 
  },
  tags: [{ 
    type: String, 
    trim: true 
  }],
  additionalProperties: { 
    type: Map, 
    of: Schema.Types.Mixed 
  }
}, { _id: false });

// Define evidence content schema
const evidenceContentSchema = new Schema({
  format: { 
    type: String, 
    required: true, 
    enum: ['json', 'text', 'binary', 'pdf', 'image', 'html', 'xml'], 
    default: 'json' 
  },
  mimeType: { 
    type: String, 
    trim: true 
  },
  data: { 
    type: Schema.Types.Mixed, 
    required: true 
  },
  size: { 
    type: Number 
  },
  hash: { 
    type: String, 
    trim: true 
  },
  encrypted: { 
    type: Boolean, 
    default: false 
  }
}, { _id: false });

// Define verification schema
const verificationSchema = new Schema({
  status: { 
    type: String, 
    enum: ['pending', 'verified', 'failed'], 
    default: 'pending' 
  },
  verifiedAt: { 
    type: Date 
  },
  verifiedBy: { 
    type: String, 
    trim: true 
  },
  method: { 
    type: String, 
    enum: ['manual', 'automated', 'blockchain'], 
    default: 'manual' 
  },
  blockchainVerificationId: { 
    type: Schema.Types.ObjectId, 
    ref: 'BlockchainVerification' 
  },
  score: { 
    type: Number, 
    min: 0, 
    max: 100 
  },
  comments: { 
    type: String, 
    trim: true 
  }
}, { _id: false });

// Define SOC 2 evidence schema
const soc2EvidenceSchema = new Schema({
  name: { 
    type: String, 
    required: true, 
    trim: true 
  },
  description: { 
    type: String, 
    trim: true 
  },
  organizationId: { 
    type: Schema.Types.ObjectId, 
    ref: 'Organization', 
    required: true 
  },
  control: { 
    type: soc2ControlSchema, 
    required: true 
  },
  status: { 
    type: String, 
    enum: ['draft', 'collected', 'validated', 'invalid', 'stored', 'verified', 'expired'], 
    default: 'draft' 
  },
  metadata: { 
    type: evidenceMetadataSchema, 
    default: () => ({}) 
  },
  content: { 
    type: evidenceContentSchema, 
    required: true 
  },
  verification: { 
    type: verificationSchema, 
    default: () => ({}) 
  },
  createdBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'User' 
  },
  updatedBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'User' 
  }
}, {
  timestamps: true,
  versionKey: true
});

// Add indexes
soc2EvidenceSchema.index({ 'control.id': 1 });
soc2EvidenceSchema.index({ 'control.reference': 1 });
soc2EvidenceSchema.index({ organizationId: 1 });
soc2EvidenceSchema.index({ status: 1 });
soc2EvidenceSchema.index({ 'metadata.tags': 1 });
soc2EvidenceSchema.index({ 'verification.status': 1 });
soc2EvidenceSchema.index({ createdAt: 1 });
soc2EvidenceSchema.index({ 'metadata.expiryDate': 1 });

// Add methods
soc2EvidenceSchema.methods.isExpired = function() {
  if (!this.metadata.expiryDate) {
    return false;
  }
  
  return new Date() > this.metadata.expiryDate;
};

soc2EvidenceSchema.methods.isVerified = function() {
  return this.verification.status === 'verified';
};

// Add statics
soc2EvidenceSchema.statics.findByControl = function(controlId, organizationId) {
  return this.find({
    'control.id': controlId,
    organizationId
  });
};

soc2EvidenceSchema.statics.findByTags = function(tags, organizationId) {
  return this.find({
    'metadata.tags': { $all: tags },
    organizationId
  });
};

soc2EvidenceSchema.statics.findByStatus = function(status, organizationId) {
  return this.find({
    status,
    organizationId
  });
};

// Create model
const SOC2Evidence = mongoose.model('SOC2Evidence', soc2EvidenceSchema);

module.exports = SOC2Evidence;
