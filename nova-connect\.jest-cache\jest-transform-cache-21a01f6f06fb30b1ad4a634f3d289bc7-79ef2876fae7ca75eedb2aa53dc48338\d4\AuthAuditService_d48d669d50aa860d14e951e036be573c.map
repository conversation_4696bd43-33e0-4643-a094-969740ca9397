{"version": 3, "names": ["AuditService", "require", "path", "AuthAuditService", "constructor", "dataDir", "join", "__dirname", "auditService", "resourceType", "logLoginAttempt", "data", "auditData", "userId", "action", "resourceId", "username", "details", "method", "success", "reason", "ip", "userAgent", "status", "teamId", "environmentId", "tenantId", "logEvent", "logLogout", "logRegistration", "logPasswordChange", "logPasswordResetRequest", "logPasswordReset", "logTokenRefresh", "logTwoFactorAuth", "toUpperCase", "getAuthAuditLogs", "filters", "authFilters", "getAuditLogs", "module", "exports"], "sources": ["AuthAuditService.js"], "sourcesContent": ["/**\n * Authentication Audit Service\n * \n * This service handles audit logging specifically for authentication events.\n */\n\nconst AuditService = require('./AuditService');\nconst path = require('path');\n\nclass AuthAuditService {\n  constructor(dataDir = path.join(__dirname, '../data')) {\n    this.auditService = new AuditService(dataDir);\n    this.resourceType = 'auth';\n  }\n\n  /**\n   * Log a login attempt\n   * \n   * @param {Object} data - Login attempt data\n   * @param {string} data.username - Username used for login\n   * @param {string} data.ip - IP address of the client\n   * @param {string} data.userAgent - User agent of the client\n   * @param {boolean} data.success - Whether the login was successful\n   * @param {string} data.userId - User ID (if login was successful)\n   * @param {string} data.reason - Reason for failure (if login failed)\n   * @param {string} data.method - Authentication method (password, oauth2, etc.)\n   * @param {Object} data.details - Additional details\n   */\n  async logLoginAttempt(data) {\n    const auditData = {\n      userId: data.userId || null,\n      action: 'LOGIN',\n      resourceType: this.resourceType,\n      resourceId: data.username,\n      details: {\n        method: data.method || 'password',\n        success: data.success,\n        reason: data.reason || null,\n        ...data.details\n      },\n      ip: data.ip,\n      userAgent: data.userAgent,\n      status: data.success ? 'success' : 'failure',\n      teamId: data.teamId || null,\n      environmentId: data.environmentId || null,\n      tenantId: data.tenantId || null\n    };\n\n    return this.auditService.logEvent(auditData);\n  }\n\n  /**\n   * Log a logout event\n   * \n   * @param {Object} data - Logout data\n   * @param {string} data.userId - User ID\n   * @param {string} data.username - Username\n   * @param {string} data.ip - IP address of the client\n   * @param {string} data.userAgent - User agent of the client\n   * @param {Object} data.details - Additional details\n   */\n  async logLogout(data) {\n    const auditData = {\n      userId: data.userId,\n      action: 'LOGOUT',\n      resourceType: this.resourceType,\n      resourceId: data.username,\n      details: data.details || {},\n      ip: data.ip,\n      userAgent: data.userAgent,\n      status: 'success',\n      teamId: data.teamId || null,\n      environmentId: data.environmentId || null,\n      tenantId: data.tenantId || null\n    };\n\n    return this.auditService.logEvent(auditData);\n  }\n\n  /**\n   * Log a registration event\n   * \n   * @param {Object} data - Registration data\n   * @param {string} data.userId - User ID\n   * @param {string} data.username - Username\n   * @param {string} data.ip - IP address of the client\n   * @param {string} data.userAgent - User agent of the client\n   * @param {boolean} data.success - Whether the registration was successful\n   * @param {string} data.reason - Reason for failure (if registration failed)\n   * @param {Object} data.details - Additional details\n   */\n  async logRegistration(data) {\n    const auditData = {\n      userId: data.userId || null,\n      action: 'REGISTER',\n      resourceType: this.resourceType,\n      resourceId: data.username,\n      details: {\n        success: data.success,\n        reason: data.reason || null,\n        ...data.details\n      },\n      ip: data.ip,\n      userAgent: data.userAgent,\n      status: data.success ? 'success' : 'failure',\n      teamId: data.teamId || null,\n      environmentId: data.environmentId || null,\n      tenantId: data.tenantId || null\n    };\n\n    return this.auditService.logEvent(auditData);\n  }\n\n  /**\n   * Log a password change event\n   * \n   * @param {Object} data - Password change data\n   * @param {string} data.userId - User ID\n   * @param {string} data.username - Username\n   * @param {string} data.ip - IP address of the client\n   * @param {string} data.userAgent - User agent of the client\n   * @param {boolean} data.success - Whether the password change was successful\n   * @param {string} data.reason - Reason for failure (if password change failed)\n   * @param {Object} data.details - Additional details\n   */\n  async logPasswordChange(data) {\n    const auditData = {\n      userId: data.userId,\n      action: 'PASSWORD_CHANGE',\n      resourceType: this.resourceType,\n      resourceId: data.username,\n      details: {\n        success: data.success,\n        reason: data.reason || null,\n        ...data.details\n      },\n      ip: data.ip,\n      userAgent: data.userAgent,\n      status: data.success ? 'success' : 'failure',\n      teamId: data.teamId || null,\n      environmentId: data.environmentId || null,\n      tenantId: data.tenantId || null\n    };\n\n    return this.auditService.logEvent(auditData);\n  }\n\n  /**\n   * Log a password reset request event\n   * \n   * @param {Object} data - Password reset request data\n   * @param {string} data.username - Username\n   * @param {string} data.ip - IP address of the client\n   * @param {string} data.userAgent - User agent of the client\n   * @param {boolean} data.success - Whether the password reset request was successful\n   * @param {string} data.reason - Reason for failure (if password reset request failed)\n   * @param {Object} data.details - Additional details\n   */\n  async logPasswordResetRequest(data) {\n    const auditData = {\n      userId: data.userId || null,\n      action: 'PASSWORD_RESET_REQUEST',\n      resourceType: this.resourceType,\n      resourceId: data.username,\n      details: {\n        success: data.success,\n        reason: data.reason || null,\n        ...data.details\n      },\n      ip: data.ip,\n      userAgent: data.userAgent,\n      status: data.success ? 'success' : 'failure',\n      teamId: data.teamId || null,\n      environmentId: data.environmentId || null,\n      tenantId: data.tenantId || null\n    };\n\n    return this.auditService.logEvent(auditData);\n  }\n\n  /**\n   * Log a password reset event\n   * \n   * @param {Object} data - Password reset data\n   * @param {string} data.userId - User ID\n   * @param {string} data.username - Username\n   * @param {string} data.ip - IP address of the client\n   * @param {string} data.userAgent - User agent of the client\n   * @param {boolean} data.success - Whether the password reset was successful\n   * @param {string} data.reason - Reason for failure (if password reset failed)\n   * @param {Object} data.details - Additional details\n   */\n  async logPasswordReset(data) {\n    const auditData = {\n      userId: data.userId || null,\n      action: 'PASSWORD_RESET',\n      resourceType: this.resourceType,\n      resourceId: data.username,\n      details: {\n        success: data.success,\n        reason: data.reason || null,\n        ...data.details\n      },\n      ip: data.ip,\n      userAgent: data.userAgent,\n      status: data.success ? 'success' : 'failure',\n      teamId: data.teamId || null,\n      environmentId: data.environmentId || null,\n      tenantId: data.tenantId || null\n    };\n\n    return this.auditService.logEvent(auditData);\n  }\n\n  /**\n   * Log a token refresh event\n   * \n   * @param {Object} data - Token refresh data\n   * @param {string} data.userId - User ID\n   * @param {string} data.username - Username\n   * @param {string} data.ip - IP address of the client\n   * @param {string} data.userAgent - User agent of the client\n   * @param {boolean} data.success - Whether the token refresh was successful\n   * @param {string} data.reason - Reason for failure (if token refresh failed)\n   * @param {Object} data.details - Additional details\n   */\n  async logTokenRefresh(data) {\n    const auditData = {\n      userId: data.userId || null,\n      action: 'TOKEN_REFRESH',\n      resourceType: this.resourceType,\n      resourceId: data.username || data.userId,\n      details: {\n        success: data.success,\n        reason: data.reason || null,\n        ...data.details\n      },\n      ip: data.ip,\n      userAgent: data.userAgent,\n      status: data.success ? 'success' : 'failure',\n      teamId: data.teamId || null,\n      environmentId: data.environmentId || null,\n      tenantId: data.tenantId || null\n    };\n\n    return this.auditService.logEvent(auditData);\n  }\n\n  /**\n   * Log a two-factor authentication event\n   * \n   * @param {Object} data - Two-factor authentication data\n   * @param {string} data.userId - User ID\n   * @param {string} data.username - Username\n   * @param {string} data.ip - IP address of the client\n   * @param {string} data.userAgent - User agent of the client\n   * @param {string} data.action - Two-factor action (setup, verify, disable)\n   * @param {boolean} data.success - Whether the two-factor action was successful\n   * @param {string} data.reason - Reason for failure (if two-factor action failed)\n   * @param {Object} data.details - Additional details\n   */\n  async logTwoFactorAuth(data) {\n    const auditData = {\n      userId: data.userId,\n      action: `2FA_${data.action.toUpperCase()}`,\n      resourceType: this.resourceType,\n      resourceId: data.username,\n      details: {\n        success: data.success,\n        reason: data.reason || null,\n        ...data.details\n      },\n      ip: data.ip,\n      userAgent: data.userAgent,\n      status: data.success ? 'success' : 'failure',\n      teamId: data.teamId || null,\n      environmentId: data.environmentId || null,\n      tenantId: data.tenantId || null\n    };\n\n    return this.auditService.logEvent(auditData);\n  }\n\n  /**\n   * Get authentication audit logs\n   * \n   * @param {Object} filters - Filters to apply\n   * @returns {Object} - Filtered audit logs\n   */\n  async getAuthAuditLogs(filters = {}) {\n    // Add resourceType filter for auth events\n    const authFilters = {\n      ...filters,\n      resourceType: this.resourceType\n    };\n\n    return this.auditService.getAuditLogs(authFilters);\n  }\n}\n\nmodule.exports = AuthAuditService;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,MAAMA,YAAY,GAAGC,OAAO,CAAC,gBAAgB,CAAC;AAC9C,MAAMC,IAAI,GAAGD,OAAO,CAAC,MAAM,CAAC;AAE5B,MAAME,gBAAgB,CAAC;EACrBC,WAAWA,CAACC,OAAO,GAAGH,IAAI,CAACI,IAAI,CAACC,SAAS,EAAE,SAAS,CAAC,EAAE;IACrD,IAAI,CAACC,YAAY,GAAG,IAAIR,YAAY,CAACK,OAAO,CAAC;IAC7C,IAAI,CAACI,YAAY,GAAG,MAAM;EAC5B;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,MAAMC,eAAeA,CAACC,IAAI,EAAE;IAC1B,MAAMC,SAAS,GAAG;MAChBC,MAAM,EAAEF,IAAI,CAACE,MAAM,IAAI,IAAI;MAC3BC,MAAM,EAAE,OAAO;MACfL,YAAY,EAAE,IAAI,CAACA,YAAY;MAC/BM,UAAU,EAAEJ,IAAI,CAACK,QAAQ;MACzBC,OAAO,EAAE;QACPC,MAAM,EAAEP,IAAI,CAACO,MAAM,IAAI,UAAU;QACjCC,OAAO,EAAER,IAAI,CAACQ,OAAO;QACrBC,MAAM,EAAET,IAAI,CAACS,MAAM,IAAI,IAAI;QAC3B,GAAGT,IAAI,CAACM;MACV,CAAC;MACDI,EAAE,EAAEV,IAAI,CAACU,EAAE;MACXC,SAAS,EAAEX,IAAI,CAACW,SAAS;MACzBC,MAAM,EAAEZ,IAAI,CAACQ,OAAO,GAAG,SAAS,GAAG,SAAS;MAC5CK,MAAM,EAAEb,IAAI,CAACa,MAAM,IAAI,IAAI;MAC3BC,aAAa,EAAEd,IAAI,CAACc,aAAa,IAAI,IAAI;MACzCC,QAAQ,EAAEf,IAAI,CAACe,QAAQ,IAAI;IAC7B,CAAC;IAED,OAAO,IAAI,CAAClB,YAAY,CAACmB,QAAQ,CAACf,SAAS,CAAC;EAC9C;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,MAAMgB,SAASA,CAACjB,IAAI,EAAE;IACpB,MAAMC,SAAS,GAAG;MAChBC,MAAM,EAAEF,IAAI,CAACE,MAAM;MACnBC,MAAM,EAAE,QAAQ;MAChBL,YAAY,EAAE,IAAI,CAACA,YAAY;MAC/BM,UAAU,EAAEJ,IAAI,CAACK,QAAQ;MACzBC,OAAO,EAAEN,IAAI,CAACM,OAAO,IAAI,CAAC,CAAC;MAC3BI,EAAE,EAAEV,IAAI,CAACU,EAAE;MACXC,SAAS,EAAEX,IAAI,CAACW,SAAS;MACzBC,MAAM,EAAE,SAAS;MACjBC,MAAM,EAAEb,IAAI,CAACa,MAAM,IAAI,IAAI;MAC3BC,aAAa,EAAEd,IAAI,CAACc,aAAa,IAAI,IAAI;MACzCC,QAAQ,EAAEf,IAAI,CAACe,QAAQ,IAAI;IAC7B,CAAC;IAED,OAAO,IAAI,CAAClB,YAAY,CAACmB,QAAQ,CAACf,SAAS,CAAC;EAC9C;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,MAAMiB,eAAeA,CAAClB,IAAI,EAAE;IAC1B,MAAMC,SAAS,GAAG;MAChBC,MAAM,EAAEF,IAAI,CAACE,MAAM,IAAI,IAAI;MAC3BC,MAAM,EAAE,UAAU;MAClBL,YAAY,EAAE,IAAI,CAACA,YAAY;MAC/BM,UAAU,EAAEJ,IAAI,CAACK,QAAQ;MACzBC,OAAO,EAAE;QACPE,OAAO,EAAER,IAAI,CAACQ,OAAO;QACrBC,MAAM,EAAET,IAAI,CAACS,MAAM,IAAI,IAAI;QAC3B,GAAGT,IAAI,CAACM;MACV,CAAC;MACDI,EAAE,EAAEV,IAAI,CAACU,EAAE;MACXC,SAAS,EAAEX,IAAI,CAACW,SAAS;MACzBC,MAAM,EAAEZ,IAAI,CAACQ,OAAO,GAAG,SAAS,GAAG,SAAS;MAC5CK,MAAM,EAAEb,IAAI,CAACa,MAAM,IAAI,IAAI;MAC3BC,aAAa,EAAEd,IAAI,CAACc,aAAa,IAAI,IAAI;MACzCC,QAAQ,EAAEf,IAAI,CAACe,QAAQ,IAAI;IAC7B,CAAC;IAED,OAAO,IAAI,CAAClB,YAAY,CAACmB,QAAQ,CAACf,SAAS,CAAC;EAC9C;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,MAAMkB,iBAAiBA,CAACnB,IAAI,EAAE;IAC5B,MAAMC,SAAS,GAAG;MAChBC,MAAM,EAAEF,IAAI,CAACE,MAAM;MACnBC,MAAM,EAAE,iBAAiB;MACzBL,YAAY,EAAE,IAAI,CAACA,YAAY;MAC/BM,UAAU,EAAEJ,IAAI,CAACK,QAAQ;MACzBC,OAAO,EAAE;QACPE,OAAO,EAAER,IAAI,CAACQ,OAAO;QACrBC,MAAM,EAAET,IAAI,CAACS,MAAM,IAAI,IAAI;QAC3B,GAAGT,IAAI,CAACM;MACV,CAAC;MACDI,EAAE,EAAEV,IAAI,CAACU,EAAE;MACXC,SAAS,EAAEX,IAAI,CAACW,SAAS;MACzBC,MAAM,EAAEZ,IAAI,CAACQ,OAAO,GAAG,SAAS,GAAG,SAAS;MAC5CK,MAAM,EAAEb,IAAI,CAACa,MAAM,IAAI,IAAI;MAC3BC,aAAa,EAAEd,IAAI,CAACc,aAAa,IAAI,IAAI;MACzCC,QAAQ,EAAEf,IAAI,CAACe,QAAQ,IAAI;IAC7B,CAAC;IAED,OAAO,IAAI,CAAClB,YAAY,CAACmB,QAAQ,CAACf,SAAS,CAAC;EAC9C;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,MAAMmB,uBAAuBA,CAACpB,IAAI,EAAE;IAClC,MAAMC,SAAS,GAAG;MAChBC,MAAM,EAAEF,IAAI,CAACE,MAAM,IAAI,IAAI;MAC3BC,MAAM,EAAE,wBAAwB;MAChCL,YAAY,EAAE,IAAI,CAACA,YAAY;MAC/BM,UAAU,EAAEJ,IAAI,CAACK,QAAQ;MACzBC,OAAO,EAAE;QACPE,OAAO,EAAER,IAAI,CAACQ,OAAO;QACrBC,MAAM,EAAET,IAAI,CAACS,MAAM,IAAI,IAAI;QAC3B,GAAGT,IAAI,CAACM;MACV,CAAC;MACDI,EAAE,EAAEV,IAAI,CAACU,EAAE;MACXC,SAAS,EAAEX,IAAI,CAACW,SAAS;MACzBC,MAAM,EAAEZ,IAAI,CAACQ,OAAO,GAAG,SAAS,GAAG,SAAS;MAC5CK,MAAM,EAAEb,IAAI,CAACa,MAAM,IAAI,IAAI;MAC3BC,aAAa,EAAEd,IAAI,CAACc,aAAa,IAAI,IAAI;MACzCC,QAAQ,EAAEf,IAAI,CAACe,QAAQ,IAAI;IAC7B,CAAC;IAED,OAAO,IAAI,CAAClB,YAAY,CAACmB,QAAQ,CAACf,SAAS,CAAC;EAC9C;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,MAAMoB,gBAAgBA,CAACrB,IAAI,EAAE;IAC3B,MAAMC,SAAS,GAAG;MAChBC,MAAM,EAAEF,IAAI,CAACE,MAAM,IAAI,IAAI;MAC3BC,MAAM,EAAE,gBAAgB;MACxBL,YAAY,EAAE,IAAI,CAACA,YAAY;MAC/BM,UAAU,EAAEJ,IAAI,CAACK,QAAQ;MACzBC,OAAO,EAAE;QACPE,OAAO,EAAER,IAAI,CAACQ,OAAO;QACrBC,MAAM,EAAET,IAAI,CAACS,MAAM,IAAI,IAAI;QAC3B,GAAGT,IAAI,CAACM;MACV,CAAC;MACDI,EAAE,EAAEV,IAAI,CAACU,EAAE;MACXC,SAAS,EAAEX,IAAI,CAACW,SAAS;MACzBC,MAAM,EAAEZ,IAAI,CAACQ,OAAO,GAAG,SAAS,GAAG,SAAS;MAC5CK,MAAM,EAAEb,IAAI,CAACa,MAAM,IAAI,IAAI;MAC3BC,aAAa,EAAEd,IAAI,CAACc,aAAa,IAAI,IAAI;MACzCC,QAAQ,EAAEf,IAAI,CAACe,QAAQ,IAAI;IAC7B,CAAC;IAED,OAAO,IAAI,CAAClB,YAAY,CAACmB,QAAQ,CAACf,SAAS,CAAC;EAC9C;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,MAAMqB,eAAeA,CAACtB,IAAI,EAAE;IAC1B,MAAMC,SAAS,GAAG;MAChBC,MAAM,EAAEF,IAAI,CAACE,MAAM,IAAI,IAAI;MAC3BC,MAAM,EAAE,eAAe;MACvBL,YAAY,EAAE,IAAI,CAACA,YAAY;MAC/BM,UAAU,EAAEJ,IAAI,CAACK,QAAQ,IAAIL,IAAI,CAACE,MAAM;MACxCI,OAAO,EAAE;QACPE,OAAO,EAAER,IAAI,CAACQ,OAAO;QACrBC,MAAM,EAAET,IAAI,CAACS,MAAM,IAAI,IAAI;QAC3B,GAAGT,IAAI,CAACM;MACV,CAAC;MACDI,EAAE,EAAEV,IAAI,CAACU,EAAE;MACXC,SAAS,EAAEX,IAAI,CAACW,SAAS;MACzBC,MAAM,EAAEZ,IAAI,CAACQ,OAAO,GAAG,SAAS,GAAG,SAAS;MAC5CK,MAAM,EAAEb,IAAI,CAACa,MAAM,IAAI,IAAI;MAC3BC,aAAa,EAAEd,IAAI,CAACc,aAAa,IAAI,IAAI;MACzCC,QAAQ,EAAEf,IAAI,CAACe,QAAQ,IAAI;IAC7B,CAAC;IAED,OAAO,IAAI,CAAClB,YAAY,CAACmB,QAAQ,CAACf,SAAS,CAAC;EAC9C;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,MAAMsB,gBAAgBA,CAACvB,IAAI,EAAE;IAC3B,MAAMC,SAAS,GAAG;MAChBC,MAAM,EAAEF,IAAI,CAACE,MAAM;MACnBC,MAAM,EAAE,OAAOH,IAAI,CAACG,MAAM,CAACqB,WAAW,CAAC,CAAC,EAAE;MAC1C1B,YAAY,EAAE,IAAI,CAACA,YAAY;MAC/BM,UAAU,EAAEJ,IAAI,CAACK,QAAQ;MACzBC,OAAO,EAAE;QACPE,OAAO,EAAER,IAAI,CAACQ,OAAO;QACrBC,MAAM,EAAET,IAAI,CAACS,MAAM,IAAI,IAAI;QAC3B,GAAGT,IAAI,CAACM;MACV,CAAC;MACDI,EAAE,EAAEV,IAAI,CAACU,EAAE;MACXC,SAAS,EAAEX,IAAI,CAACW,SAAS;MACzBC,MAAM,EAAEZ,IAAI,CAACQ,OAAO,GAAG,SAAS,GAAG,SAAS;MAC5CK,MAAM,EAAEb,IAAI,CAACa,MAAM,IAAI,IAAI;MAC3BC,aAAa,EAAEd,IAAI,CAACc,aAAa,IAAI,IAAI;MACzCC,QAAQ,EAAEf,IAAI,CAACe,QAAQ,IAAI;IAC7B,CAAC;IAED,OAAO,IAAI,CAAClB,YAAY,CAACmB,QAAQ,CAACf,SAAS,CAAC;EAC9C;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,MAAMwB,gBAAgBA,CAACC,OAAO,GAAG,CAAC,CAAC,EAAE;IACnC;IACA,MAAMC,WAAW,GAAG;MAClB,GAAGD,OAAO;MACV5B,YAAY,EAAE,IAAI,CAACA;IACrB,CAAC;IAED,OAAO,IAAI,CAACD,YAAY,CAAC+B,YAAY,CAACD,WAAW,CAAC;EACpD;AACF;AAEAE,MAAM,CAACC,OAAO,GAAGtC,gBAAgB", "ignoreList": []}