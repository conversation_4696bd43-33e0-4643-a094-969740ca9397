/**
 * ProtectedRoute Component
 * 
 * A component for protecting routes that require authentication.
 */

import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { useAuth } from '../auth/AuthContext';
import { Transition } from './Transition';

/**
 * ProtectedRoute component
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components
 * @param {React.ReactNode} [props.fallback] - Fallback component to show when not authenticated
 * @param {boolean} [props.redirectToLogin=true] - Whether to redirect to login page
 * @param {string} [props.requiredRole] - Required role for access
 * @param {Function} [props.onUnauthorized] - Callback when unauthorized
 * @returns {React.ReactElement} ProtectedRoute component
 */
const ProtectedRoute = ({
  children,
  fallback,
  redirectToLogin = true,
  requiredRole,
  onUnauthorized
}) => {
  const { user, isLoading, isAuthenticated, loginPath } = useAuth();
  const [isAuthorized, setIsAuthorized] = useState(false);
  
  // Check authorization
  useEffect(() => {
    // Not authenticated
    if (!isLoading && !isAuthenticated) {
      setIsAuthorized(false);
      
      if (redirectToLogin && typeof window !== 'undefined') {
        // Save current path for redirect after login
        const currentPath = window.location.pathname;
        sessionStorage.setItem('redirectAfterLogin', currentPath);
        
        // Redirect to login
        window.location.href = loginPath;
      }
      
      if (onUnauthorized) {
        onUnauthorized({ reason: 'unauthenticated' });
      }
      
      return;
    }
    
    // Check role if required
    if (requiredRole && user) {
      const hasRequiredRole = user.role === requiredRole || 
        (Array.isArray(user.roles) && user.roles.includes(requiredRole));
      
      setIsAuthorized(hasRequiredRole);
      
      if (!hasRequiredRole && onUnauthorized) {
        onUnauthorized({ reason: 'unauthorized', requiredRole });
      }
      
      return;
    }
    
    // Authenticated and no role required or has required role
    setIsAuthorized(isAuthenticated);
  }, [isLoading, isAuthenticated, user, requiredRole, redirectToLogin, loginPath, onUnauthorized]);
  
  // Show loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }
  
  // Show fallback if not authorized
  if (!isAuthorized && fallback) {
    return fallback;
  }
  
  // Show children if authorized
  return (
    <Transition
      in={isAuthorized}
      enter="fadeIn"
      exit="fadeOut"
      timeout={300}
      unmountOnExit
    >
      <div>{children}</div>
    </Transition>
  );
};

ProtectedRoute.propTypes = {
  children: PropTypes.node.isRequired,
  fallback: PropTypes.node,
  redirectToLogin: PropTypes.bool,
  requiredRole: PropTypes.string,
  onUnauthorized: PropTypes.func
};

export default ProtectedRoute;
