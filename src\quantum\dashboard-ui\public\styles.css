/* Dashboard Styles */

body {
  background-color: #f8f9fa;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.navbar-brand {
  font-weight: bold;
}

.card {
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.card-header {
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  font-weight: bold;
}

.progress {
  height: 10px;
  border-radius: 5px;
}

.table th {
  font-weight: bold;
  background-color: #f8f9fa;
}

/* Alert levels */
.alert-critical {
  background-color: #dc3545;
  color: white;
}

.alert-warning {
  background-color: #ffc107;
  color: black;
}

.alert-info {
  background-color: #0dcaf0;
  color: black;
}

/* Domain-specific colors */
.cyber-color {
  color: #dc3545;
}

.financial-color {
  color: #198754;
}

.medical-color {
  color: #0dcaf0;
}

/* Animations */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.pulse {
  animation: pulse 1s infinite;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .card-title {
    font-size: 1rem;
  }
  
  h2 {
    font-size: 1.5rem;
  }
  
  h3 {
    font-size: 1.2rem;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* Chart containers */
canvas {
  width: 100% !important;
  height: 250px !important;
}

/* Status indicators */
.status-indicator {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 5px;
}

.status-healthy {
  background-color: #198754;
}

.status-warning {
  background-color: #ffc107;
}

.status-critical {
  background-color: #dc3545;
}

/* Tooltip styles */
.tooltip-inner {
  max-width: 300px;
  padding: 10px;
  background-color: rgba(0, 0, 0, 0.8);
  border-radius: 5px;
}

/* Badge styles */
.badge-cyber {
  background-color: #dc3545;
  color: white;
}

.badge-financial {
  background-color: #198754;
  color: white;
}

.badge-medical {
  background-color: #0dcaf0;
  color: white;
}

/* Alert table */
#alerts-table-body tr {
  cursor: pointer;
  transition: background-color 0.2s;
}

#alerts-table-body tr:hover {
  background-color: #f0f0f0;
}

/* New alert animation */
@keyframes newAlert {
  0% {
    background-color: rgba(255, 193, 7, 0.3);
  }
  100% {
    background-color: transparent;
  }
}

.new-alert {
  animation: newAlert 2s;
}

/* Critical alert animation */
@keyframes criticalAlert {
  0% {
    background-color: rgba(220, 53, 69, 0.3);
  }
  100% {
    background-color: transparent;
  }
}

.critical-alert {
  animation: criticalAlert 2s;
}

/* Dashboard header */
.dashboard-header {
  background-color: #343a40;
  color: white;
  padding: 15px;
  margin-bottom: 20px;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.dashboard-header h1 {
  margin: 0;
  font-size: 1.5rem;
}

.dashboard-header p {
  margin: 5px 0 0 0;
  opacity: 0.8;
}

/* Connection status */
.connection-status {
  display: inline-block;
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: bold;
}

.connection-connected {
  background-color: #198754;
  color: white;
}

.connection-disconnected {
  background-color: #dc3545;
  color: white;
}

/* Footer */
.dashboard-footer {
  text-align: center;
  padding: 20px;
  margin-top: 20px;
  color: #6c757d;
  font-size: 0.9rem;
}

.dashboard-footer a {
  color: #0d6efd;
  text-decoration: none;
}

.dashboard-footer a:hover {
  text-decoration: underline;
}
