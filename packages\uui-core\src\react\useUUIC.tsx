import React from 'react';
import { useUUICContext } from './UUICProvider';

/**
 * useUUIC - Custom hook for module-agnostic component logic
 * 
 * This hook retrieves a component from the UUIC registry based on the component key.
 * 
 * @param componentKey - Key of the component to retrieve
 * @returns Component or null if not found
 */
export const useUUIC = (componentKey: string): React.ComponentType<any> | null => {
  const config = useUUICContext();
  return config.components?.[componentKey] || null;
};

/**
 * useUUICConfig - Hook to access component configuration
 * 
 * @param componentKey - Key of the component to retrieve configuration for
 * @returns Component configuration or null if not found
 */
export const useUUICConfig = (componentKey: string): any => {
  const config = useUUICContext();
  return config.componentConfigs?.[componentKey] || null;
};

/**
 * useUUICData - Hook to access component data
 * 
 * @param dataKey - Key of the data to retrieve
 * @returns Component data or null if not found
 */
export const useUUICData = (dataKey: string): any => {
  const config = useUUICContext();
  return config.data?.[dataKey] || null;
};

export default useUUIC;
