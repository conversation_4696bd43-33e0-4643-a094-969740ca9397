/**
 * Simple API Connection Test for NovaConnect Universal API Connector
 */

const axios = require('axios');

// Configuration
const API_URL = 'http://localhost:3005';
const API_KEY = 'valid-api-key';

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

/**
 * Test API Key Authentication
 */
async function testApiKeyAuthentication() {
  console.log(`${colors.bright}${colors.blue}Testing API Key Authentication...${colors.reset}`);
  
  try {
    // Test with valid API key
    console.log(`${colors.yellow}Testing with valid API key...${colors.reset}`);
    const validResponse = await axios.get(`${API_URL}/api-key/resource`, {
      headers: {
        'X-API-Key': API_KEY
      }
    });
    
    console.log(`${colors.green}✓ Success! Status: ${validResponse.status}${colors.reset}`);
    console.log(`${colors.cyan}Response: ${JSON.stringify(validResponse.data, null, 2)}${colors.reset}`);
    
    // Test with invalid API key
    console.log(`\n${colors.yellow}Testing with invalid API key...${colors.reset}`);
    try {
      await axios.get(`${API_URL}/api-key/resource`, {
        headers: {
          'X-API-Key': 'invalid-api-key'
        }
      });
      
      console.log(`${colors.red}✗ Test failed! Expected 403 error but got success${colors.reset}`);
    } catch (error) {
      if (error.response && error.response.status === 403) {
        console.log(`${colors.green}✓ Success! Got expected 403 error${colors.reset}`);
        console.log(`${colors.cyan}Error response: ${JSON.stringify(error.response.data, null, 2)}${colors.reset}`);
      } else {
        console.log(`${colors.red}✗ Test failed! Expected 403 error but got ${error.message}${colors.reset}`);
      }
    }
    
    // Test with missing API key
    console.log(`\n${colors.yellow}Testing with missing API key...${colors.reset}`);
    try {
      await axios.get(`${API_URL}/api-key/resource`);
      
      console.log(`${colors.red}✗ Test failed! Expected 401 error but got success${colors.reset}`);
    } catch (error) {
      if (error.response && error.response.status === 401) {
        console.log(`${colors.green}✓ Success! Got expected 401 error${colors.reset}`);
        console.log(`${colors.cyan}Error response: ${JSON.stringify(error.response.data, null, 2)}${colors.reset}`);
      } else {
        console.log(`${colors.red}✗ Test failed! Expected 401 error but got ${error.message}${colors.reset}`);
      }
    }
    
    return true;
  } catch (error) {
    console.log(`${colors.red}✗ Test failed! Error: ${error.message}${colors.reset}`);
    if (error.response) {
      console.log(`${colors.red}Response: ${JSON.stringify(error.response.data, null, 2)}${colors.reset}`);
    }
    return false;
  }
}

/**
 * Test API Health
 */
async function testApiHealth() {
  console.log(`${colors.bright}${colors.blue}Testing API Health...${colors.reset}`);
  
  try {
    const response = await axios.get(`${API_URL}/health`);
    
    console.log(`${colors.green}✓ Success! Status: ${response.status}${colors.reset}`);
    console.log(`${colors.cyan}Response: ${JSON.stringify(response.data, null, 2)}${colors.reset}`);
    
    return true;
  } catch (error) {
    console.log(`${colors.red}✗ Test failed! Error: ${error.message}${colors.reset}`);
    if (error.response) {
      console.log(`${colors.red}Response: ${JSON.stringify(error.response.data, null, 2)}${colors.reset}`);
    }
    return false;
  }
}

/**
 * Run all tests
 */
async function runTests() {
  console.log(`${colors.bright}${colors.magenta}=== NovaConnect API Connection Tests ===${colors.reset}\n`);
  
  // Test API health
  const healthResult = await testApiHealth();
  
  // Only proceed if health check passes
  if (healthResult) {
    // Test API key authentication
    await testApiKeyAuthentication();
  }
  
  console.log(`\n${colors.bright}${colors.magenta}=== Test Complete ===${colors.reset}`);
}

// Run the tests
runTests();
