/**
 * NovaFuse Test Service Connector
 * Integrates the unified test runner with NovaConnect's Universal API Connector
 * Enables real-time test execution, monitoring, and results streaming
 */

const fs = require('fs').promises;
const path = require('path');
const { spawn } = require('child_process');
const EventEmitter = require('events');
const TestResultsDatabase = require('../database/test-results-db');

class TestServiceConnector extends EventEmitter {
    constructor(novaConnect, options = {}) {
        super();
        
        this.novaConnect = novaConnect;
        this.options = {
            testRootPath: options.testRootPath || process.cwd(),
            maxConcurrentTests: options.maxConcurrentTests || 5,
            testTimeout: options.testTimeout || 300000, // 5 minutes
            enableRealTimeUpdates: options.enableRealTimeUpdates !== false,
            ...options
        };
        
        this.testCategories = {
            uuft: {
                name: 'UUFT Testing Suite',
                pattern: 'UUFT_test_*.py',
                runner: 'python',
                location: '.',
                description: 'Universal Unified Field Theory validation'
            },
            trinity: {
                name: 'Trinity Testing Framework',
                pattern: '*trinity*.{js,py}',
                runner: 'auto',
                location: '.',
                description: 'Trinity coherence validation'
            },
            novaconnect: {
                name: 'NovaConnect Testing',
                pattern: 'tests/**/novaconnect/**/*.test.js',
                runner: 'jest',
                location: 'tests',
                description: 'API and integration testing'
            },
            compliance: {
                name: 'Compliance Testing',
                pattern: 'tests/compliance/**/*.test.js',
                runner: 'jest',
                location: 'tests/compliance',
                description: 'Regulatory validation'
            },
            performance: {
                name: 'Performance Testing',
                pattern: 'tests/performance/**/*.test.js',
                runner: 'jest',
                location: 'tests/performance',
                description: 'Benchmark and load testing'
            },
            security: {
                name: 'Security Testing',
                pattern: 'tests/security/**/*.test.js',
                runner: 'jest',
                location: 'tests/security',
                description: 'Penetration and vulnerability testing'
            },
            coherence: {
                name: 'Coherence Testing',
                pattern: '*coherence*.py',
                runner: 'python',
                location: '.',
                description: 'Coherence validation protocols'
            },
            specialized: {
                name: 'Specialized Testing',
                pattern: '{src,coherence-reality-systems}/**/*test*.{js,py}',
                runner: 'auto',
                location: 'src',
                description: 'Domain-specific testing'
            }
        };
        
        this.activeTests = new Map();
        this.testHistory = [];
        this.testResults = new Map();

        // Initialize database
        this.database = new TestResultsDatabase({
            dbPath: path.join(this.options.testRootPath, 'data', 'test-results.db'),
            enableLogging: this.options.enableLogging !== false
        });

        this.registerWithNovaConnect();
    }
    
    /**
     * Register this connector with NovaConnect
     */
    async registerWithNovaConnect() {
        const connectorConfig = {
            id: 'novafuse-test-service',
            name: 'NovaFuse Test Service',
            description: 'Unified test execution and monitoring service',
            version: '1.0.0',
            type: 'service',
            category: 'testing',
            endpoints: {
                'discover-tests': {
                    method: 'GET',
                    path: '/tests/discover',
                    description: 'Discover all test files in the ecosystem'
                },
                'run-tests': {
                    method: 'POST',
                    path: '/tests/run',
                    description: 'Execute tests by category or specific files'
                },
                'test-status': {
                    method: 'GET',
                    path: '/tests/status',
                    description: 'Get current test execution status'
                },
                'test-results': {
                    method: 'GET',
                    path: '/tests/results',
                    description: 'Get test execution results and history'
                },
                'test-analytics': {
                    method: 'GET',
                    path: '/tests/analytics',
                    description: 'Get test analytics, trends, and performance metrics'
                },
                'stop-tests': {
                    method: 'POST',
                    path: '/tests/stop',
                    description: 'Stop running tests'
                }
            },
            authentication: {
                type: 'none', // Internal service
                required: false
            },
            realTimeEvents: [
                'test.started',
                'test.progress',
                'test.completed',
                'test.failed',
                'test.category.completed'
            ]
        };
        
        try {
            // Initialize database first
            await this.database.initialize();
            console.log('✅ Test Results Database initialized');

            await this.novaConnect.registerConnector(connectorConfig);
            console.log('✅ Test Service Connector registered with NovaConnect');

            // Set up event handlers
            this.setupEventHandlers();

        } catch (error) {
            console.error('❌ Failed to register Test Service Connector:', error);
            throw error;
        }
    }
    
    /**
     * Set up NovaConnect event handlers
     */
    setupEventHandlers() {
        // Handle test discovery requests
        this.novaConnect.on('connector.execute', async (event) => {
            if (event.connectorId === 'novafuse-test-service') {
                await this.handleConnectorRequest(event);
            }
        });
        
        // Set up real-time updates if enabled
        if (this.options.enableRealTimeUpdates) {
            this.on('testStarted', (data) => {
                this.novaConnect.emit('test.started', data);
            });
            
            this.on('testProgress', (data) => {
                this.novaConnect.emit('test.progress', data);
            });
            
            this.on('testCompleted', (data) => {
                this.novaConnect.emit('test.completed', data);
            });
            
            this.on('testFailed', (data) => {
                this.novaConnect.emit('test.failed', data);
            });
        }
    }
    
    /**
     * Handle NovaConnect connector requests
     */
    async handleConnectorRequest(event) {
        const { endpointId, parameters, requestId } = event;
        
        try {
            let result;
            
            switch (endpointId) {
                case 'discover-tests':
                    result = await this.discoverTests(parameters);
                    break;
                    
                case 'run-tests':
                    result = await this.runTests(parameters);
                    break;
                    
                case 'test-status':
                    result = await this.getTestStatus(parameters);
                    break;
                    
                case 'test-results':
                    result = await this.getTestResults(parameters);
                    break;

                case 'test-analytics':
                    result = await this.getTestAnalytics(parameters);
                    break;

                case 'stop-tests':
                    result = await this.stopTests(parameters);
                    break;
                    
                default:
                    throw new Error(`Unknown endpoint: ${endpointId}`);
            }
            
            // Send response back through NovaConnect
            this.novaConnect.emit('connector.response', {
                requestId,
                success: true,
                data: result
            });
            
        } catch (error) {
            console.error(`Test Service error for ${endpointId}:`, error);
            
            this.novaConnect.emit('connector.response', {
                requestId,
                success: false,
                error: {
                    message: error.message,
                    code: error.code || 'TEST_SERVICE_ERROR'
                }
            });
        }
    }
    
    /**
     * Discover all test files in the ecosystem
     */
    async discoverTests(parameters = {}) {
        const { category, includeMetadata = true } = parameters;
        const discoveredTests = {};
        
        const categoriesToScan = category ? [category] : Object.keys(this.testCategories);
        
        for (const categoryKey of categoriesToScan) {
            const categoryConfig = this.testCategories[categoryKey];
            if (!categoryConfig) continue;
            
            try {
                const testFiles = await this.findTestFiles(categoryConfig);
                
                discoveredTests[categoryKey] = {
                    category: categoryConfig.name,
                    description: categoryConfig.description,
                    runner: categoryConfig.runner,
                    location: categoryConfig.location,
                    files: testFiles,
                    count: testFiles.length
                };
                
                if (includeMetadata) {
                    // Add file metadata
                    for (const file of testFiles) {
                        try {
                            const filePath = path.join(this.options.testRootPath, categoryConfig.location, file);
                            const stats = await fs.stat(filePath);
                            file.metadata = {
                                size: stats.size,
                                modified: stats.mtime,
                                created: stats.birthtime
                            };
                        } catch (error) {
                            // File might not exist, skip metadata
                            file.metadata = null;
                        }
                    }
                }
                
            } catch (error) {
                console.error(`Error discovering tests for ${categoryKey}:`, error);
                discoveredTests[categoryKey] = {
                    category: categoryConfig.name,
                    error: error.message,
                    files: [],
                    count: 0
                };
            }
        }
        
        return {
            totalCategories: Object.keys(discoveredTests).length,
            totalTests: Object.values(discoveredTests).reduce((sum, cat) => sum + cat.count, 0),
            categories: discoveredTests,
            discoveredAt: new Date().toISOString()
        };
    }
    
    /**
     * Find test files for a category
     */
    async findTestFiles(categoryConfig) {
        // For now, return mock data similar to the original test runner
        // In a real implementation, this would scan the file system
        const mockFiles = [];
        
        switch (categoryConfig.name) {
            case 'UUFT Testing Suite':
                for (let i = 1; i <= 20; i++) {
                    mockFiles.push({
                        name: `UUFT_test_${i.toString().padStart(2, '0')}.py`,
                        path: `UUFT_test_${i.toString().padStart(2, '0')}.py`,
                        type: 'python'
                    });
                }
                break;
                
            case 'Trinity Testing Framework':
                mockFiles.push(
                    { name: 'test_trinity_csde.py', path: 'test_trinity_csde.py', type: 'python' },
                    { name: 'test_trinitarian_csde.py', path: 'test_trinitarian_csde.py', type: 'python' },
                    { name: 'trinity-day1-test.js', path: 'trinity-day1-test.js', type: 'javascript' },
                    { name: 'trinity-day2-test.js', path: 'trinity-day2-test.js', type: 'javascript' },
                    { name: 'trinity-day3-complete-test.js', path: 'trinity-day3-complete-test.js', type: 'javascript' }
                );
                break;
                
            case 'Coherence Testing':
                mockFiles.push(
                    { name: 'test_coherence.py', path: 'test_coherence.py', type: 'python' },
                    { name: 'test_coherence_resonance_fix.py', path: 'test_coherence_resonance_fix.py', type: 'python' },
                    { name: 'N3C_Coherence_Simulation.py', path: 'N3C_Coherence_Simulation.py', type: 'python' },
                    { name: 'advanced_coherence_demo.py', path: 'advanced_coherence_demo.py', type: 'python' },
                    { name: 'trinity_coherence_final_calibration.py', path: 'trinity_coherence_final_calibration.py', type: 'python' }
                );
                break;
                
            default:
                // Generate mock files for other categories
                for (let i = 1; i <= Math.min(categoryConfig.count || 10, 15); i++) {
                    const fileName = `${categoryConfig.name.toLowerCase().replace(/\s+/g, '_')}_test_${i}`;
                    const extension = categoryConfig.runner === 'python' ? '.py' : '.js';
                    mockFiles.push({
                        name: fileName + extension,
                        path: fileName + extension,
                        type: categoryConfig.runner === 'python' ? 'python' : 'javascript'
                    });
                }
        }
        
        return mockFiles;
    }
    
    /**
     * Run tests
     */
    async runTests(parameters = {}) {
        const { 
            categories = [], 
            files = [], 
            options = {},
            requestId = Date.now().toString()
        } = parameters;
        
        const testExecution = {
            id: requestId,
            startTime: new Date().toISOString(),
            status: 'running',
            categories: categories,
            files: files,
            options: options,
            results: {
                total: 0,
                passed: 0,
                failed: 0,
                skipped: 0,
                categories: {}
            }
        };
        
        this.activeTests.set(requestId, testExecution);
        
        // Emit test started event
        this.emit('testStarted', {
            executionId: requestId,
            categories: categories,
            files: files,
            startTime: testExecution.startTime
        });
        
        try {
            // Execute tests (mock implementation for now)
            await this.executeTestCategories(testExecution, categories);
            
            testExecution.status = 'completed';
            testExecution.endTime = new Date().toISOString();
            testExecution.duration = Date.now() - new Date(testExecution.startTime).getTime();
            
            // Store results in database
            try {
                await this.database.storeTestExecution({
                    id: requestId,
                    startTime: testExecution.startTime,
                    endTime: testExecution.endTime,
                    duration: testExecution.duration,
                    status: testExecution.status,
                    categories: categories,
                    results: testExecution.results,
                    options: options,
                    error: null
                });
                console.log(`✅ Test execution ${requestId} stored in database`);
            } catch (dbError) {
                console.error(`❌ Failed to store test execution in database:`, dbError);
                // Continue without failing the test execution
            }

            // Store results in memory (for backward compatibility)
            this.testResults.set(requestId, testExecution);
            this.testHistory.push({
                id: requestId,
                startTime: testExecution.startTime,
                endTime: testExecution.endTime,
                duration: testExecution.duration,
                status: testExecution.status,
                summary: {
                    total: testExecution.results.total,
                    passed: testExecution.results.passed,
                    failed: testExecution.results.failed,
                    passRate: ((testExecution.results.passed / testExecution.results.total) * 100).toFixed(1)
                }
            });
            
            // Emit completion event
            this.emit('testCompleted', {
                executionId: requestId,
                results: testExecution.results,
                duration: testExecution.duration
            });
            
        } catch (error) {
            testExecution.status = 'failed';
            testExecution.error = error.message;
            testExecution.endTime = new Date().toISOString();
            
            this.emit('testFailed', {
                executionId: requestId,
                error: error.message
            });
        } finally {
            this.activeTests.delete(requestId);
        }
        
        return {
            executionId: requestId,
            status: testExecution.status,
            results: testExecution.results
        };
    }
    
    /**
     * Execute test categories (mock implementation)
     */
    async executeTestCategories(testExecution, categories) {
        const categoriesToRun = categories.length > 0 ? categories : Object.keys(this.testCategories);
        
        for (const categoryKey of categoriesToRun) {
            const categoryConfig = this.testCategories[categoryKey];
            if (!categoryConfig) continue;
            
            const testFiles = await this.findTestFiles(categoryConfig);
            const categoryResults = {
                name: categoryConfig.name,
                total: testFiles.length,
                passed: 0,
                failed: 0,
                skipped: 0,
                files: []
            };
            
            // Simulate test execution
            for (const testFile of testFiles) {
                const isSuccess = Math.random() > 0.1; // 90% success rate
                const duration = Math.floor(Math.random() * 5000) + 100; // 100-5000ms
                
                const fileResult = {
                    file: testFile.name,
                    status: isSuccess ? 'passed' : 'failed',
                    duration: duration,
                    runner: categoryConfig.runner,
                    output: isSuccess ? 'All tests passed' : 'Mock failure for demonstration'
                };
                
                categoryResults.files.push(fileResult);
                
                if (isSuccess) {
                    categoryResults.passed++;
                    testExecution.results.passed++;
                } else {
                    categoryResults.failed++;
                    testExecution.results.failed++;
                }
                
                testExecution.results.total++;
                
                // Emit progress update
                this.emit('testProgress', {
                    executionId: testExecution.id,
                    category: categoryKey,
                    file: testFile.name,
                    status: fileResult.status,
                    progress: {
                        completed: testExecution.results.total,
                        total: categoriesToRun.reduce((sum, cat) => sum + (this.testCategories[cat]?.count || 10), 0)
                    }
                });
                
                // Small delay to simulate test execution
                await new Promise(resolve => setTimeout(resolve, 50));
            }
            
            testExecution.results.categories[categoryKey] = categoryResults;
        }
    }
    
    /**
     * Get test status
     */
    async getTestStatus(parameters = {}) {
        const { executionId } = parameters;
        
        if (executionId) {
            const execution = this.activeTests.get(executionId) || this.testResults.get(executionId);
            return execution || { error: 'Execution not found' };
        }
        
        return {
            activeTests: Array.from(this.activeTests.values()),
            recentHistory: this.testHistory.slice(-10)
        };
    }
    
    /**
     * Get test results
     */
    async getTestResults(parameters = {}) {
        const { executionId, limit = 50, offset = 0, status, category, startDate, endDate } = parameters;

        try {
            if (executionId) {
                // Get specific execution details from database
                const details = await this.database.getExecutionDetails(executionId);
                if (details) {
                    return details;
                }

                // Fallback to memory
                return this.testResults.get(executionId) || { error: 'Results not found' };
            }

            // Get execution history from database
            const history = await this.database.getExecutionHistory({
                limit,
                offset,
                status,
                category,
                startDate,
                endDate
            });

            return {
                history,
                totalExecutions: history.length,
                source: 'database'
            };

        } catch (error) {
            console.error('Database query failed, falling back to memory:', error);

            // Fallback to memory-based results
            return {
                history: this.testHistory.slice(-limit),
                totalExecutions: this.testHistory.length,
                source: 'memory'
            };
        }
    }

    /**
     * Get test analytics and trends
     */
    async getTestAnalytics(parameters = {}) {
        const { days = 30, category } = parameters;

        try {
            const analytics = await this.database.getAnalytics({ days, category });

            // Add additional computed metrics
            const enhancedAnalytics = {
                ...analytics,
                computed: {
                    successRate: analytics.summary.avg_pass_rate || 0,
                    totalTestsRun: analytics.summary.total_tests || 0,
                    avgExecutionTime: analytics.summary.avg_duration || 0,
                    executionFrequency: analytics.summary.total_executions / days,
                    trendDirection: this.calculateTrendDirection(analytics.trends),
                    topPerformingCategories: analytics.categoryBreakdown
                        .sort((a, b) => b.avg_pass_rate - a.avg_pass_rate)
                        .slice(0, 5),
                    slowestCategories: analytics.categoryBreakdown
                        .sort((a, b) => b.avg_duration - a.avg_duration)
                        .slice(0, 5)
                },
                generatedAt: new Date().toISOString()
            };

            return enhancedAnalytics;

        } catch (error) {
            console.error('Failed to get analytics from database:', error);

            // Return basic analytics from memory
            return {
                summary: {
                    total_executions: this.testHistory.length,
                    avg_pass_rate: this.testHistory.length > 0 ?
                        this.testHistory.reduce((sum, exec) => sum + parseFloat(exec.summary.passRate), 0) / this.testHistory.length : 0,
                    total_tests: this.testHistory.reduce((sum, exec) => sum + exec.summary.total, 0)
                },
                trends: [],
                categoryBreakdown: [],
                source: 'memory',
                error: error.message
            };
        }
    }

    /**
     * Calculate trend direction from historical data
     */
    calculateTrendDirection(trends) {
        if (trends.length < 2) return 'stable';

        const recent = trends.slice(0, 7); // Last 7 days
        const older = trends.slice(7, 14); // Previous 7 days

        if (recent.length === 0 || older.length === 0) return 'stable';

        const recentAvg = recent.reduce((sum, t) => sum + t.avg_pass_rate, 0) / recent.length;
        const olderAvg = older.reduce((sum, t) => sum + t.avg_pass_rate, 0) / older.length;

        const difference = recentAvg - olderAvg;

        if (difference > 2) return 'improving';
        if (difference < -2) return 'declining';
        return 'stable';
    }
    
    /**
     * Stop running tests
     */
    async stopTests(parameters = {}) {
        const { executionId } = parameters;
        
        if (executionId) {
            const execution = this.activeTests.get(executionId);
            if (execution) {
                execution.status = 'stopped';
                execution.endTime = new Date().toISOString();
                this.activeTests.delete(executionId);
                return { success: true, message: `Test execution ${executionId} stopped` };
            }
            return { success: false, message: 'Execution not found or already completed' };
        }
        
        // Stop all active tests
        const stoppedCount = this.activeTests.size;
        this.activeTests.clear();
        
        return { 
            success: true, 
            message: `Stopped ${stoppedCount} active test executions` 
        };
    }
}

module.exports = TestServiceConnector;
