/**
 * Environment Service
 * 
 * This service handles environment management for multi-environment support.
 */

const fs = require('fs').promises;
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const { ValidationError, NotFoundError } = require('../utils/errors');

class EnvironmentService {
  constructor(dataDir = path.join(__dirname, '../data')) {
    this.dataDir = dataDir;
    this.environmentsFile = path.join(this.dataDir, 'environments.json');
    this.defaultEnvironments = [
      {
        id: 'dev',
        name: 'Development',
        description: 'Development environment for testing and development',
        color: '#4caf50',
        isDefault: true,
        created: new Date().toISOString(),
        updated: new Date().toISOString()
      },
      {
        id: 'test',
        name: 'Testing',
        description: 'Testing environment for QA and validation',
        color: '#ff9800',
        isDefault: false,
        created: new Date().toISOString(),
        updated: new Date().toISOString()
      },
      {
        id: 'prod',
        name: 'Production',
        description: 'Production environment for live systems',
        color: '#f44336',
        isDefault: false,
        created: new Date().toISOString(),
        updated: new Date().toISOString()
      }
    ];
    this.ensureDataDir();
  }

  /**
   * Ensure the data directory exists
   */
  async ensureDataDir() {
    try {
      await fs.mkdir(this.dataDir, { recursive: true });
      
      // Initialize environments file if it doesn't exist
      try {
        await fs.access(this.environmentsFile);
      } catch (error) {
        if (error.code === 'ENOENT') {
          // File doesn't exist, create it with default environments
          await fs.writeFile(this.environmentsFile, JSON.stringify(this.defaultEnvironments, null, 2));
        } else {
          throw error;
        }
      }
    } catch (error) {
      console.error('Error creating data directory:', error);
      throw error;
    }
  }

  /**
   * Load environments from file
   */
  async loadEnvironments() {
    try {
      const data = await fs.readFile(this.environmentsFile, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      if (error.code === 'ENOENT') {
        // File doesn't exist, return default environments
        return this.defaultEnvironments;
      }
      console.error('Error loading environments:', error);
      throw error;
    }
  }

  /**
   * Save environments to file
   */
  async saveEnvironments(environments) {
    try {
      await fs.writeFile(this.environmentsFile, JSON.stringify(environments, null, 2));
    } catch (error) {
      console.error('Error saving environments:', error);
      throw error;
    }
  }

  /**
   * Get all environments
   */
  async getAllEnvironments() {
    return this.loadEnvironments();
  }

  /**
   * Get environment by ID
   */
  async getEnvironmentById(id) {
    const environments = await this.loadEnvironments();
    const environment = environments.find(env => env.id === id);
    
    if (!environment) {
      throw new NotFoundError(`Environment with ID ${id} not found`);
    }
    
    return environment;
  }

  /**
   * Get default environment
   */
  async getDefaultEnvironment() {
    const environments = await this.loadEnvironments();
    const defaultEnvironment = environments.find(env => env.isDefault);
    
    if (!defaultEnvironment) {
      // If no default environment is set, use the first one
      return environments[0];
    }
    
    return defaultEnvironment;
  }

  /**
   * Create a new environment
   */
  async createEnvironment(data) {
    if (!data.name) {
      throw new ValidationError('Environment name is required');
    }
    
    const environments = await this.loadEnvironments();
    
    // Generate ID from name if not provided
    const id = data.id || data.name.toLowerCase().replace(/[^a-z0-9]/g, '-');
    
    // Check if ID already exists
    if (environments.some(env => env.id === id)) {
      throw new ValidationError(`Environment with ID ${id} already exists`);
    }
    
    // Create new environment
    const newEnvironment = {
      id,
      name: data.name,
      description: data.description || '',
      color: data.color || '#2196f3',
      isDefault: data.isDefault || false,
      created: new Date().toISOString(),
      updated: new Date().toISOString()
    };
    
    // If this is the first environment or it's set as default, update other environments
    if (environments.length === 0 || newEnvironment.isDefault) {
      // Set all other environments to non-default
      environments.forEach(env => {
        env.isDefault = false;
      });
      
      // If this is the first environment, set it as default
      if (environments.length === 0) {
        newEnvironment.isDefault = true;
      }
    }
    
    environments.push(newEnvironment);
    await this.saveEnvironments(environments);
    
    return newEnvironment;
  }

  /**
   * Update an environment
   */
  async updateEnvironment(id, data) {
    const environments = await this.loadEnvironments();
    const index = environments.findIndex(env => env.id === id);
    
    if (index === -1) {
      throw new NotFoundError(`Environment with ID ${id} not found`);
    }
    
    // Update environment
    const updatedEnvironment = {
      ...environments[index],
      ...data,
      id: id, // Don't allow changing the ID
      updated: new Date().toISOString()
    };
    
    // If this environment is set as default, update other environments
    if (updatedEnvironment.isDefault) {
      // Set all other environments to non-default
      environments.forEach(env => {
        if (env.id !== id) {
          env.isDefault = false;
        }
      });
    } else {
      // Make sure at least one environment is default
      const hasDefault = environments.some(env => env.id !== id && env.isDefault);
      
      if (!hasDefault) {
        // If no other environment is default, keep this one as default
        updatedEnvironment.isDefault = true;
      }
    }
    
    environments[index] = updatedEnvironment;
    await this.saveEnvironments(environments);
    
    return updatedEnvironment;
  }

  /**
   * Delete an environment
   */
  async deleteEnvironment(id) {
    const environments = await this.loadEnvironments();
    
    // Don't allow deleting the last environment
    if (environments.length <= 1) {
      throw new ValidationError('Cannot delete the last environment');
    }
    
    const index = environments.findIndex(env => env.id === id);
    
    if (index === -1) {
      throw new NotFoundError(`Environment with ID ${id} not found`);
    }
    
    // Check if this is the default environment
    const isDefault = environments[index].isDefault;
    
    // Remove the environment
    environments.splice(index, 1);
    
    // If this was the default environment, set another one as default
    if (isDefault) {
      environments[0].isDefault = true;
    }
    
    await this.saveEnvironments(environments);
    
    return { success: true, message: `Environment ${id} deleted` };
  }

  /**
   * Set default environment
   */
  async setDefaultEnvironment(id) {
    const environments = await this.loadEnvironments();
    const index = environments.findIndex(env => env.id === id);
    
    if (index === -1) {
      throw new NotFoundError(`Environment with ID ${id} not found`);
    }
    
    // Set all environments to non-default
    environments.forEach(env => {
      env.isDefault = false;
    });
    
    // Set the specified environment as default
    environments[index].isDefault = true;
    environments[index].updated = new Date().toISOString();
    
    await this.saveEnvironments(environments);
    
    return environments[index];
  }

  /**
   * Get environment-specific data directory
   */
  getEnvironmentDataDir(environmentId) {
    return path.join(this.dataDir, 'environments', environmentId);
  }

  /**
   * Ensure environment-specific data directory exists
   */
  async ensureEnvironmentDataDir(environmentId) {
    const envDataDir = this.getEnvironmentDataDir(environmentId);
    
    try {
      await fs.mkdir(envDataDir, { recursive: true });
    } catch (error) {
      console.error(`Error creating environment data directory for ${environmentId}:`, error);
      throw error;
    }
    
    return envDataDir;
  }

  /**
   * Copy data between environments
   */
  async copyEnvironmentData(sourceId, targetId) {
    // Validate environments
    await this.getEnvironmentById(sourceId);
    await this.getEnvironmentById(targetId);
    
    const sourceDir = this.getEnvironmentDataDir(sourceId);
    const targetDir = this.getEnvironmentDataDir(targetId);
    
    try {
      // Ensure both directories exist
      await this.ensureEnvironmentDataDir(sourceId);
      await this.ensureEnvironmentDataDir(targetId);
      
      // Get all files in source directory
      const files = await fs.readdir(sourceDir);
      
      // Copy each file
      for (const file of files) {
        const sourcePath = path.join(sourceDir, file);
        const targetPath = path.join(targetDir, file);
        
        // Check if it's a file
        const stats = await fs.stat(sourcePath);
        
        if (stats.isFile()) {
          // Read source file
          const data = await fs.readFile(sourcePath);
          
          // Write to target file
          await fs.writeFile(targetPath, data);
        }
      }
      
      return { 
        success: true, 
        message: `Data copied from ${sourceId} to ${targetId}`,
        filesCopied: files.length
      };
    } catch (error) {
      console.error(`Error copying environment data from ${sourceId} to ${targetId}:`, error);
      throw error;
    }
  }

  /**
   * Promote environment (copy from one environment to another)
   */
  async promoteEnvironment(sourceId, targetId) {
    // Validate environments
    const sourceEnv = await this.getEnvironmentById(sourceId);
    const targetEnv = await this.getEnvironmentById(targetId);
    
    // Create promotion record
    const promotion = {
      id: uuidv4(),
      sourceId,
      sourceName: sourceEnv.name,
      targetId,
      targetName: targetEnv.name,
      timestamp: new Date().toISOString(),
      status: 'in_progress'
    };
    
    try {
      // Copy data between environments
      await this.copyEnvironmentData(sourceId, targetId);
      
      // Update promotion record
      promotion.status = 'completed';
      
      return promotion;
    } catch (error) {
      // Update promotion record
      promotion.status = 'failed';
      promotion.error = error.message;
      
      throw error;
    }
  }
}

module.exports = EnvironmentService;
