# NovaConnect Environment Configuration

# Server
PORT=3001
NODE_ENV=development
LOG_LEVEL=info

# MongoDB
MONGODB_URI=mongodb://localhost:27017/nova-connect

# CORS
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3002

# Authentication
JWT_SECRET=your-jwt-secret-key
JWT_EXPIRES_IN=1h
REFRESH_TOKEN_EXPIRES_IN=7d

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX=100

# Monitoring
METRICS_ENABLED=true
TRACING_ENABLED=true
TRACING_EXPORTER=console  # Options: console, zipkin, gcp
ZIPKIN_URL=http://localhost:9411/api/v2/spans

# Google Cloud
GOOGLE_CLOUD_PROJECT=your-gcp-project-id
GOOGLE_CLOUD_MONITORING_ENABLED=false
GOOGLE_APPLICATION_CREDENTIALS=path/to/your/credentials.json

# Feature Flags
FEATURE_FLAGS_ENABLED=true
CACHE_ENABLED=true
MAX_CONCURRENT_REMEDIATIONS=20
DEFAULT_TIMEOUT=30000
RETRY_COUNT=3
RETRY_DELAY=1000
