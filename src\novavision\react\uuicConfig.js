/**
 * uuicConfig.js - JSON-style config for mapping components to modules
 * 
 * This file contains the configuration for mapping component keys to
 * actual component implementations.
 */

// Import form field components
import TextInput from './components/form/TextInput';
import PasswordInput from './components/form/PasswordInput';
import EmailInput from './components/form/EmailInput';
import NumberInput from './components/form/NumberInput';
import SelectInput from './components/form/SelectInput';
import CheckboxInput from './components/form/CheckboxInput';
import RadioInput from './components/form/RadioInput';
import TextareaInput from './components/form/TextareaInput';
import DateInput from './components/form/DateInput';
import FileInput from './components/form/FileInput';
import FieldGroup from './components/form/FieldGroup';

// Import form action components
import SubmitButton from './components/form/SubmitButton';
import Button from './components/form/Button';
import LinkButton from './components/form/LinkButton';

// Import dashboard widget components
import ChartWidget from './components/dashboard/ChartWidget';
import TableWidget from './components/dashboard/TableWidget';
import MetricWidget from './components/dashboard/MetricWidget';
import ListWidget from './components/dashboard/ListWidget';
import MapWidget from './components/dashboard/MapWidget';

// Import dashboard filter components
import SelectFilter from './components/dashboard/SelectFilter';
import DateFilter from './components/dashboard/DateFilter';
import DateRangeFilter from './components/dashboard/DateRangeFilter';
import RangeFilter from './components/dashboard/RangeFilter';

// Import report element components
import TextElement from './components/report/TextElement';
import ChartElement from './components/report/ChartElement';
import TableElement from './components/report/TableElement';
import ImageElement from './components/report/ImageElement';
import PageBreakElement from './components/report/PageBreakElement';

// Import report parameter components
import TextParameter from './components/report/TextParameter';
import SelectParameter from './components/report/SelectParameter';
import DateParameter from './components/report/DateParameter';

/**
 * UUIC configuration object
 * 
 * This object maps component keys to their implementations.
 */
export const uuicConfig = {
  components: {
    // Form field components
    'form-field-text': TextInput,
    'form-field-password': PasswordInput,
    'form-field-email': EmailInput,
    'form-field-number': NumberInput,
    'form-field-select': SelectInput,
    'form-field-checkbox': CheckboxInput,
    'form-field-radio': RadioInput,
    'form-field-textarea': TextareaInput,
    'form-field-date': DateInput,
    'form-field-file': FileInput,
    'form-field-group': FieldGroup,
    
    // Form action components
    'form-action-submit': SubmitButton,
    'form-action-button': Button,
    'form-action-link': LinkButton,
    
    // Dashboard widget components
    'dashboard-widget-chart': ChartWidget,
    'dashboard-widget-table': TableWidget,
    'dashboard-widget-metric': MetricWidget,
    'dashboard-widget-list': ListWidget,
    'dashboard-widget-map': MapWidget,
    
    // Dashboard filter components
    'dashboard-filter-select': SelectFilter,
    'dashboard-filter-date': DateFilter,
    'dashboard-filter-daterange': DateRangeFilter,
    'dashboard-filter-range': RangeFilter,
    
    // Report element components
    'report-element-text': TextElement,
    'report-element-chart': ChartElement,
    'report-element-table': TableElement,
    'report-element-image': ImageElement,
    'report-element-page-break': PageBreakElement,
    
    // Report parameter components
    'report-parameter-text': TextParameter,
    'report-parameter-select': SelectParameter,
    'report-parameter-date': DateParameter
  },
  
  // Default component configurations
  componentConfigs: {
    'form-field-text': {
      className: 'uuic-text-input',
      placeholder: 'Enter text...'
    },
    'form-field-password': {
      className: 'uuic-password-input',
      placeholder: 'Enter password...'
    },
    // Additional component configurations...
  }
};

export default uuicConfig;
