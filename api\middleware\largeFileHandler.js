/**
 * Large File Handler Middleware
 * 
 * This middleware provides streaming-based file handling for large files,
 * allowing efficient processing without loading the entire file into memory.
 */

const busboy = require('busboy');
const fs = require('fs');
const path = require('path');
const { pipeline } = require('stream');
const { promisify } = require('util');
const { v4: uuidv4 } = require('uuid');
const { createGzip } = require('zlib');
const { Throttle } = require('stream-throttle');
const logger = require('../utils/logger');

// Promisify pipeline for async/await usage
const pipelineAsync = promisify(pipeline);

/**
 * Configuration for the large file handler
 * @typedef {Object} LargeFileHandlerConfig
 * @property {number} maxFileSize - Maximum file size in bytes (default: 1GB)
 * @property {string} tempDir - Directory for temporary file storage
 * @property {boolean} compressFiles - Whether to compress files during processing
 * @property {number} throttleRate - Rate limit for uploads in bytes per second (optional)
 * @property {string[]} allowedMimeTypes - Array of allowed MIME types (optional)
 * @property {string[]} allowedExtensions - Array of allowed file extensions (optional)
 */

/**
 * Creates a middleware for handling large file uploads via streaming
 * @param {LargeFileHandlerConfig} config - Configuration options
 * @returns {Function} Express middleware
 */
function largeFileHandler(config = {}) {
  // Default configuration
  const options = {
    maxFileSize: 1024 * 1024 * 1024, // 1GB default
    tempDir: path.join(process.cwd(), 'uploads', 'temp'),
    compressFiles: false,
    ...config
  };

  // Ensure temp directory exists
  if (!fs.existsSync(options.tempDir)) {
    fs.mkdirSync(options.tempDir, { recursive: true });
  }

  return (req, res, next) => {
    // Only process multipart requests
    if (!req.is('multipart/form-data')) {
      return next();
    }

    const busboyConfig = {
      headers: req.headers,
      limits: {
        fileSize: options.maxFileSize
      }
    };

    // Initialize file tracking
    req.files = [];
    req.fileErrors = [];
    
    try {
      const bb = busboy(busboyConfig);

      // Handle file upload
      bb.on('file', async (fieldname, fileStream, fileInfo) => {
        const { filename, encoding, mimeType } = fileInfo;
        
        // Check file type if restrictions are set
        if (options.allowedMimeTypes && !options.allowedMimeTypes.includes(mimeType)) {
          req.fileErrors.push({
            field: fieldname,
            filename,
            error: `File type ${mimeType} not allowed`
          });
          fileStream.resume(); // Drain the stream
          return;
        }

        // Check file extension if restrictions are set
        if (options.allowedExtensions) {
          const ext = path.extname(filename).toLowerCase();
          if (!options.allowedExtensions.includes(ext)) {
            req.fileErrors.push({
              field: fieldname,
              filename,
              error: `File extension ${ext} not allowed`
            });
            fileStream.resume(); // Drain the stream
            return;
          }
        }

        // Generate unique filename to prevent collisions
        const uniqueId = uuidv4();
        const fileExtension = path.extname(filename);
        const safeFilename = path.basename(filename, fileExtension)
          .replace(/[^a-zA-Z0-9]/g, '_')
          .replace(/_+/g, '_')
          .substring(0, 50);
        
        const tempFilename = `${uniqueId}-${safeFilename}${fileExtension}`;
        const tempFilePath = path.join(options.tempDir, tempFilename);
        
        // Create write stream
        const writeStream = fs.createWriteStream(tempFilePath);
        
        // Track file size
        let fileSize = 0;
        
        try {
          // Set up the processing pipeline
          const streams = [];
          
          // Add throttling if configured
          if (options.throttleRate) {
            streams.push(new Throttle({ rate: options.throttleRate }));
          }
          
          // Add compression if enabled
          if (options.compressFiles && fileExtension !== '.gz') {
            streams.push(createGzip());
          }
          
          // Add size tracking
          streams.push(
            new (require('stream').Transform)({
              transform(chunk, encoding, callback) {
                fileSize += chunk.length;
                if (options.maxFileSize && fileSize > options.maxFileSize) {
                  this.destroy(new Error(`File size exceeds the limit of ${options.maxFileSize} bytes`));
                  return;
                }
                callback(null, chunk);
              }
            })
          );
          
          // Add write stream
          streams.push(writeStream);
          
          // Connect all streams
          await pipelineAsync(fileStream, ...streams);
          
          // Add file info to request
          req.files.push({
            fieldname,
            originalname: filename,
            encoding,
            mimetype: mimeType,
            size: fileSize,
            path: tempFilePath,
            filename: tempFilename,
            destination: options.tempDir
          });
          
          logger.info(`File uploaded successfully: ${filename} (${fileSize} bytes)`);
        } catch (error) {
          // Clean up the partial file
          writeStream.end();
          if (fs.existsSync(tempFilePath)) {
            fs.unlinkSync(tempFilePath);
          }
          
          req.fileErrors.push({
            field: fieldname,
            filename,
            error: error.message
          });
          
          logger.error(`File upload error: ${error.message}`, { 
            filename, 
            fieldname,
            error 
          });
        }
      });

      // Handle form fields
      bb.on('field', (fieldname, value) => {
        if (!req.body) {
          req.body = {};
        }
        
        // Handle array fields (fieldname[] syntax)
        if (fieldname.endsWith('[]')) {
          const key = fieldname.slice(0, -2);
          if (!req.body[key]) {
            req.body[key] = [];
          }
          req.body[key].push(value);
        } else {
          req.body[fieldname] = value;
        }
      });

      // Handle parsing completion
      bb.on('finish', () => {
        next();
      });

      // Handle errors
      bb.on('error', (error) => {
        logger.error('Busboy error:', error);
        next(error);
      });

      // Pipe the request to busboy
      req.pipe(bb);
    } catch (error) {
      logger.error('Error initializing file upload handler:', error);
      next(error);
    }
  };
}

/**
 * Utility to clean up temporary files after processing
 * @param {Object} file - File object from req.files
 */
function cleanupTempFile(file) {
  if (file && file.path && fs.existsSync(file.path)) {
    fs.unlink(file.path, (err) => {
      if (err) {
        logger.warn(`Failed to clean up temporary file: ${file.path}`, { error: err });
      }
    });
  }
}

module.exports = {
  largeFileHandler,
  cleanupTempFile
};
