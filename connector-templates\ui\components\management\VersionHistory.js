/**
 * Version History Component
 * 
 * This component displays the version history of a connector.
 */

import React, { useState } from 'react';
import { 
  Box, 
  Button, 
  Card, 
  CardContent, 
  Chip, 
  Divider, 
  Grid, 
  Paper, 
  Table, 
  TableBody, 
  TableCell, 
  TableContainer, 
  TableHead, 
  TableRow, 
  Typography 
} from '@mui/material';
import RestoreIcon from '@mui/icons-material/Restore';
import CompareArrowsIcon from '@mui/icons-material/CompareArrows';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';

const VersionHistory = ({ versions }) => {
  const [selectedVersions, setSelectedVersions] = useState([]);
  const [showDiff, setShowDiff] = useState(false);
  
  // If no versions are provided, create a mock version history
  const versionHistory = versions.length > 0 ? versions : [
    {
      version: '1.0.0',
      date: new Date().toISOString(),
      author: '<PERSON>',
      changes: [
        'Initial release'
      ]
    }
  ];
  
  const handleVersionSelect = (version) => {
    if (selectedVersions.includes(version)) {
      setSelectedVersions(selectedVersions.filter(v => v !== version));
    } else {
      if (selectedVersions.length < 2) {
        setSelectedVersions([...selectedVersions, version]);
      }
    }
  };
  
  const handleCompare = () => {
    if (selectedVersions.length === 2) {
      setShowDiff(true);
    }
  };
  
  const handleRestore = (version) => {
    // In a real implementation, this would restore the connector to this version
    alert(`Restore to version ${version}`);
  };
  
  const renderChanges = (changes) => {
    if (!changes || changes.length === 0) {
      return <Typography variant="body2" color="textSecondary">No changes recorded</Typography>;
    }
    
    return (
      <ul style={{ margin: 0, paddingLeft: 16 }}>
        {changes.map((change, index) => (
          <li key={index}>
            <Typography variant="body2">{change}</Typography>
          </li>
        ))}
      </ul>
    );
  };
  
  const renderDiff = () => {
    if (selectedVersions.length !== 2) {
      return null;
    }
    
    // Sort versions chronologically
    const sortedVersions = [...selectedVersions].sort();
    const olderVersion = sortedVersions[0];
    const newerVersion = sortedVersions[1];
    
    // In a real implementation, this would show the actual diff between versions
    // This is just a mock example
    const diffExample = `diff --git a/connector.json b/connector.json
index 1234567..abcdefg 100644
--- a/connector.json (version ${olderVersion})
+++ b/connector.json (version ${newerVersion})
@@ -10,7 +10,7 @@
   "baseUrl": "https://api.example.com",
   "authentication": {
     "type": "API_KEY",
-    "header": "X-API-Key"
+    "header": "Authorization"
   },
   "endpoints": [
     {
@@ -25,6 +25,14 @@
         }
       ]
     },
+    {
+      "name": "new_endpoint",
+      "path": "/new",
+      "method": "GET",
+      "description": "New endpoint added in version ${newerVersion}"
+    },
     {
       "name": "get_user",
       "path": "/users/{id}",`;
    
    return (
      <Box>
        <Typography variant="h6" gutterBottom>
          Comparing versions {olderVersion} and {newerVersion}
        </Typography>
        
        <Paper variant="outlined" sx={{ overflow: 'auto' }}>
          <SyntaxHighlighter language="diff" style={vscDarkPlus} showLineNumbers>
            {diffExample}
          </SyntaxHighlighter>
        </Paper>
      </Box>
    );
  };
  
  return (
    <Box>
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h6">
          Version History
        </Typography>
        
        <Button
          variant="outlined"
          startIcon={<CompareArrowsIcon />}
          onClick={handleCompare}
          disabled={selectedVersions.length !== 2}
        >
          Compare Versions
        </Button>
      </Box>
      
      {showDiff && selectedVersions.length === 2 && (
        <Card variant="outlined" sx={{ mb: 3 }}>
          <CardContent>
            {renderDiff()}
          </CardContent>
        </Card>
      )}
      
      <TableContainer component={Paper} variant="outlined">
        <Table>
          <TableHead>
            <TableRow>
              <TableCell padding="checkbox"></TableCell>
              <TableCell>Version</TableCell>
              <TableCell>Date</TableCell>
              <TableCell>Author</TableCell>
              <TableCell>Changes</TableCell>
              <TableCell align="right">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {versionHistory.map((version, index) => (
              <TableRow 
                key={version.version} 
                hover
                selected={selectedVersions.includes(version.version)}
                onClick={() => handleVersionSelect(version.version)}
                sx={{ cursor: 'pointer' }}
              >
                <TableCell padding="checkbox">
                  <input 
                    type="checkbox" 
                    checked={selectedVersions.includes(version.version)}
                    onChange={() => {}}
                  />
                </TableCell>
                <TableCell>
                  <Typography variant="subtitle2">
                    {version.version}
                  </Typography>
                  {index === 0 && (
                    <Chip label="Latest" size="small" color="primary" sx={{ ml: 1 }} />
                  )}
                </TableCell>
                <TableCell>
                  {new Date(version.date).toLocaleString()}
                </TableCell>
                <TableCell>{version.author}</TableCell>
                <TableCell>{renderChanges(version.changes)}</TableCell>
                <TableCell align="right">
                  <Button
                    variant="outlined"
                    size="small"
                    startIcon={<RestoreIcon />}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleRestore(version.version);
                    }}
                    disabled={index === 0} // Can't restore to current version
                  >
                    Restore
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
};

export default VersionHistory;
