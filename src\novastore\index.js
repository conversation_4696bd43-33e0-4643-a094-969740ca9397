/**
 * NovaStore - The Brain and Nervous System of NovaFuse
 *
 * This module initializes and exports all NovaStore components:
 * 1. Nervous System: The central nervous system of NovaFuse
 * 2. Portfolio Management: Three-tier product portfolio management
 * 3. Cross-Domain Intelligence: Pattern recognition across domains
 * 4. CSDE Integration: Integration with CSDE and Trinity CSDE
 */

const NovaStoreNervousSystem = require('./nervous-system');
const NovaStoreNervousSystemClient = require('./nervous-system/client');
const ProductPortfolio = require('./portfolio');
const CrossDomainIntelligence = require('./cross-domain');
const CSDEIntegration = require('./csde_integration');

/**
 * Initialize NovaStore
 * @param {Object} options - Initialization options
 * @returns {Object} - Initialized NovaStore components
 */
function initialize(options = {}) {
  const logger = options.logger || console;

  logger.info('Initializing NovaStore');

  // Initialize components
  const nervousSystem = new NovaStoreNervousSystem({
    ...options.nervousSystem,
    logger
  });

  const portfolio = new ProductPortfolio({
    ...options.portfolio,
    logger
  });

  const crossDomain = new CrossDomainIntelligence({
    ...options.crossDomain,
    logger
  });

  // Initialize CSDE Integration
  const csdeIntegration = new CSDEIntegration({
    ...options.csdeIntegration,
    logger
  });

  logger.info('NovaStore initialized successfully');

  return {
    nervousSystem,
    portfolio,
    crossDomain,
    csdeIntegration,

    /**
     * Create a client for the NovaStore Nervous System
     * @param {Object} clientOptions - Client options
     * @returns {NovaStoreNervousSystemClient} - Nervous System client
     */
    createClient(clientOptions = {}) {
      return new NovaStoreNervousSystemClient({
        ...clientOptions,
        logger
      });
    },

    /**
     * Start all NovaStore components
     * @returns {Promise<void>}
     */
    async start() {
      logger.info('Starting NovaStore');

      try {
        // Start Nervous System
        await nervousSystem.start();

        logger.info('NovaStore started successfully');
      } catch (error) {
        logger.error('Error starting NovaStore:', error);
        throw error;
      }
    },

    /**
     * Stop all NovaStore components
     * @returns {Promise<void>}
     */
    async stop() {
      logger.info('Stopping NovaStore');

      try {
        // Stop Nervous System
        await nervousSystem.stop();

        logger.info('NovaStore stopped successfully');
      } catch (error) {
        logger.error('Error stopping NovaStore:', error);
        throw error;
      }
    },

    /**
     * Verify a component using Trinity CSDE
     * @param {Object} component - Component to verify
     * @returns {Promise<Object>} - Verification result
     */
    async verifyComponent(component) {
      return csdeIntegration.verifyComponent(component, 'trinity');
    },

    /**
     * Calculate revenue sharing using Trinity CSDE
     * @param {Object} transaction - Transaction details
     * @returns {Promise<Object>} - Revenue sharing result
     */
    async calculateRevenueSharing(transaction) {
      return csdeIntegration.calculateRevenueSharing(transaction, 'trinity');
    }
  };
}

module.exports = {
  initialize,
  NovaStoreNervousSystem,
  NovaStoreNervousSystemClient,
  ProductPortfolio,
  CrossDomainIntelligence,
  CSDEIntegration
};
