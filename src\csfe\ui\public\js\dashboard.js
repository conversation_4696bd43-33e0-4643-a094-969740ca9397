/**
 * CSFE NovaVision Dashboard JavaScript
 * 
 * This script handles the client-side functionality of the CSFE NovaVision Dashboard.
 */

// Global variables
let marketData = {};
let economicData = {};
let sentimentData = {};
let csfeResult = null;
let charts = {};

// DOM elements
const csfeValueElement = document.getElementById('csfe-value');
const performanceFactorElement = document.getElementById('performance-factor');
const calculationTimestampElement = document.getElementById('calculation-timestamp');
const marketDirectionElement = document.getElementById('market-direction');
const marketStrengthBarElement = document.getElementById('market-strength-bar');
const riskLevelElement = document.getElementById('risk-level');
const overallRiskElement = document.getElementById('overall-risk');
const overallRiskBarElement = document.getElementById('overall-risk-bar');
const riskFactorsContainerElement = document.getElementById('risk-factors-container');
const depressionProbabilityBarElement = document.getElementById('depression-probability-bar');
const depressionIndicatorsElement = document.getElementById('depression-indicators');
const marketDataFormElement = document.getElementById('market-data-form');
const economicDataFormElement = document.getElementById('economic-data-form');
const sentimentDataFormElement = document.getElementById('sentiment-data-form');
const engineSelectorElement = document.getElementById('engine-selector');
const calculateBtnElement = document.getElementById('calculate-btn');
const calculateBtnBottomElement = document.getElementById('calculate-btn-bottom');
const loadSampleDataBtnElement = document.getElementById('load-sample-data-btn');

// Initialize the dashboard
document.addEventListener('DOMContentLoaded', () => {
  // Initialize charts
  initializeCharts();
  
  // Add event listeners
  calculateBtnElement.addEventListener('click', calculateCSFE);
  calculateBtnBottomElement.addEventListener('click', calculateCSFE);
  loadSampleDataBtnElement.addEventListener('click', loadSampleData);
  
  // Load sample data on initial load
  loadSampleData();
});

/**
 * Initialize charts
 */
function initializeCharts() {
  // CSFE Components Chart
  const csfeComponentsCtx = document.getElementById('csfe-components-chart').getContext('2d');
  charts.csfeComponents = new Chart(csfeComponentsCtx, {
    type: 'radar',
    data: {
      labels: ['Market', 'Economic', 'Sentiment', 'Tensor', 'Fusion'],
      datasets: [{
        label: 'CSFE Components',
        data: [0, 0, 0, 0, 0],
        backgroundColor: 'rgba(0, 86, 179, 0.2)',
        borderColor: 'rgba(0, 86, 179, 1)',
        borderWidth: 2,
        pointBackgroundColor: 'rgba(0, 86, 179, 1)',
        pointBorderColor: '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: 'rgba(0, 86, 179, 1)'
      }]
    },
    options: {
      scales: {
        r: {
          beginAtZero: true,
          ticks: {
            display: false
          }
        }
      }
    }
  });
  
  // Asset Class Predictions Chart
  const assetClassPredictionsCtx = document.getElementById('asset-class-predictions-chart').getContext('2d');
  charts.assetClassPredictions = new Chart(assetClassPredictionsCtx, {
    type: 'bar',
    data: {
      labels: ['Equity', 'Fixed Income', 'Forex', 'Commodities', 'Crypto'],
      datasets: [{
        label: 'Expected Return (%)',
        data: [0, 0, 0, 0, 0],
        backgroundColor: [
          'rgba(40, 167, 69, 0.7)',
          'rgba(0, 123, 255, 0.7)',
          'rgba(108, 117, 125, 0.7)',
          'rgba(255, 193, 7, 0.7)',
          'rgba(220, 53, 69, 0.7)'
        ],
        borderColor: [
          'rgba(40, 167, 69, 1)',
          'rgba(0, 123, 255, 1)',
          'rgba(108, 117, 125, 1)',
          'rgba(255, 193, 7, 1)',
          'rgba(220, 53, 69, 1)'
        ],
        borderWidth: 1
      }]
    },
    options: {
      scales: {
        y: {
          beginAtZero: true,
          title: {
            display: true,
            text: 'Expected Return (%)'
          }
        }
      }
    }
  });
  
  // Asset Allocation Chart
  const assetAllocationCtx = document.getElementById('asset-allocation-chart').getContext('2d');
  charts.assetAllocation = new Chart(assetAllocationCtx, {
    type: 'doughnut',
    data: {
      labels: ['Equity', 'Fixed Income', 'Forex', 'Commodities', 'Crypto'],
      datasets: [{
        data: [0, 0, 0, 0, 0],
        backgroundColor: [
          'rgba(40, 167, 69, 0.7)',
          'rgba(0, 123, 255, 0.7)',
          'rgba(108, 117, 125, 0.7)',
          'rgba(255, 193, 7, 0.7)',
          'rgba(220, 53, 69, 0.7)'
        ],
        borderColor: [
          'rgba(40, 167, 69, 1)',
          'rgba(0, 123, 255, 1)',
          'rgba(108, 117, 125, 1)',
          'rgba(255, 193, 7, 1)',
          'rgba(220, 53, 69, 1)'
        ],
        borderWidth: 1
      }]
    },
    options: {
      responsive: true,
      plugins: {
        legend: {
          position: 'right'
        }
      }
    }
  });
  
  // Timeline Predictions Chart
  const timelinePredictionsCtx = document.getElementById('timeline-predictions-chart').getContext('2d');
  charts.timelinePredictions = new Chart(timelinePredictionsCtx, {
    type: 'line',
    data: {
      labels: ['Current', 'Short-term', 'Medium-term', 'Long-term'],
      datasets: [{
        label: 'CSFE Projection',
        data: [0, 0, 0, 0],
        backgroundColor: 'rgba(0, 86, 179, 0.2)',
        borderColor: 'rgba(0, 86, 179, 1)',
        borderWidth: 2,
        tension: 0.4
      }]
    },
    options: {
      scales: {
        y: {
          beginAtZero: false,
          title: {
            display: true,
            text: 'CSFE Value'
          }
        }
      }
    }
  });
  
  // Depression Timeline Chart
  const depressionTimelineCtx = document.getElementById('depression-timeline-chart').getContext('2d');
  charts.depressionTimeline = new Chart(depressionTimelineCtx, {
    type: 'bar',
    data: {
      labels: ['2027', '2028', '2029', '2030', '2031'],
      datasets: [{
        label: 'Depression Probability (%)',
        data: [10, 20, 35, 25, 15],
        backgroundColor: 'rgba(255, 193, 7, 0.7)',
        borderColor: 'rgba(255, 193, 7, 1)',
        borderWidth: 1
      }]
    },
    options: {
      scales: {
        y: {
          beginAtZero: true,
          max: 100,
          title: {
            display: true,
            text: 'Probability (%)'
          }
        }
      }
    }
  });
}

/**
 * Load sample data
 */
function loadSampleData() {
  fetch('/api/sample-data')
    .then(response => response.json())
    .then(data => {
      marketData = data.marketData;
      economicData = data.economicData;
      sentimentData = data.sentimentData;
      
      // Populate forms
      populateDataForms();
      
      // Calculate CSFE with sample data
      calculateCSFE();
    })
    .catch(error => {
      console.error('Error loading sample data:', error);
      alert('Failed to load sample data. Please try again.');
    });
}

/**
 * Populate data forms
 */
function populateDataForms() {
  // Populate market data form
  marketDataFormElement.innerHTML = generateFormFields(marketData, 'market');
  
  // Populate economic data form
  economicDataFormElement.innerHTML = generateFormFields(economicData, 'economic');
  
  // Populate sentiment data form
  sentimentDataFormElement.innerHTML = generateFormFields(sentimentData, 'sentiment');
}

/**
 * Generate form fields for data
 * @param {Object} data - Data object
 * @param {String} prefix - Field prefix
 * @returns {String} - HTML for form fields
 */
function generateFormFields(data, prefix) {
  let html = '';
  
  for (const key in data) {
    if (typeof data[key] === 'object' && data[key] !== null) {
      html += `<div class="mb-3"><h5>${formatLabel(key)}</h5>`;
      
      for (const subKey in data[key]) {
        if (subKey !== 'history') {
          const fieldId = `${prefix}-${key}-${subKey}`;
          const fieldValue = data[key][subKey];
          
          html += `
            <div class="mb-2">
              <label for="${fieldId}" class="form-label">${formatLabel(subKey)}</label>
              <input type="number" class="form-control" id="${fieldId}" value="${fieldValue}" 
                     onchange="updateData('${prefix}', '${key}', '${subKey}', this.value)">
            </div>
          `;
        }
      }
      
      html += '</div>';
    }
  }
  
  return html;
}

/**
 * Format label from camelCase or snake_case
 * @param {String} str - Input string
 * @returns {String} - Formatted label
 */
function formatLabel(str) {
  return str
    .replace(/_/g, ' ')
    .replace(/([A-Z])/g, ' $1')
    .replace(/^./, function(str) { return str.toUpperCase(); });
}

/**
 * Update data based on form input
 * @param {String} dataType - Data type (market, economic, sentiment)
 * @param {String} key - Data key
 * @param {String} subKey - Data sub-key
 * @param {String} value - New value
 */
function updateData(dataType, key, subKey, value) {
  const numValue = parseFloat(value);
  
  switch (dataType) {
    case 'market':
      marketData[key][subKey] = numValue;
      break;
    case 'economic':
      economicData[key][subKey] = numValue;
      break;
    case 'sentiment':
      sentimentData[key][subKey] = numValue;
      break;
  }
}

/**
 * Calculate CSFE
 */
function calculateCSFE() {
  const engine = engineSelectorElement.value;
  
  // Show loading state
  csfeValueElement.textContent = 'Calculating...';
  
  // Prepare request data
  const requestData = {
    marketData,
    economicData,
    sentimentData,
    engine
  };
  
  // Send calculation request
  fetch('/api/calculate', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(requestData)
  })
    .then(response => response.json())
    .then(result => {
      csfeResult = result;
      updateDashboard();
    })
    .catch(error => {
      console.error('Error calculating CSFE:', error);
      alert('Failed to calculate CSFE. Please try again.');
      csfeValueElement.textContent = 'Error';
    });
}

/**
 * Update dashboard with CSFE result
 */
function updateDashboard() {
  if (!csfeResult) return;
  
  // Update CSFE value
  csfeValueElement.textContent = csfeResult.csfeValue.toFixed(2);
  performanceFactorElement.textContent = csfeResult.performanceFactor;
  calculationTimestampElement.textContent = formatTimestamp(csfeResult.calculatedAt);
  
  // Update charts based on engine type
  if (csfeResult.engine === 'standard') {
    updateStandardDashboard();
  } else {
    updateTrinityDashboard();
  }
}

/**
 * Update dashboard for standard CSFE engine
 */
function updateStandardDashboard() {
  // Update CSFE components chart
  charts.csfeComponents.data.labels = ['Market', 'Economic', 'Sentiment', 'Tensor', 'Fusion'];
  charts.csfeComponents.data.datasets[0].data = [
    csfeResult.components.market.processedValue,
    csfeResult.components.economic.processedValue,
    csfeResult.components.sentiment.processedValue,
    csfeResult.components.tensor.normalizedValue,
    csfeResult.components.fusion.fusionValue
  ];
  charts.csfeComponents.update();
  
  // Update market predictions
  const marketPredictions = csfeResult.predictions.marketPredictions;
  marketDirectionElement.textContent = marketPredictions.overall.direction.toUpperCase();
  marketDirectionElement.className = `text-${getDirectionClass(marketPredictions.overall.direction)}`;
  
  const strength = marketPredictions.overall.strength;
  marketStrengthBarElement.style.width = `${strength}%`;
  marketStrengthBarElement.textContent = `${strength.toFixed(1)}%`;
  marketStrengthBarElement.className = `progress-bar bg-${getDirectionClass(marketPredictions.overall.direction)}`;
  marketStrengthBarElement.setAttribute('aria-valuenow', strength);
  
  // Update asset class predictions chart
  const assetClasses = Object.keys(marketPredictions.assetClasses);
  charts.assetClassPredictions.data.labels = assetClasses.map(formatLabel);
  charts.assetClassPredictions.data.datasets[0].data = assetClasses.map(ac => 
    marketPredictions.assetClasses[ac].expectedReturn
  );
  charts.assetClassPredictions.update();
  
  // Update asset allocation
  const assetAllocation = csfeResult.predictions.assetAllocation;
  riskLevelElement.textContent = assetAllocation.riskLevel.toUpperCase();
  riskLevelElement.className = `text-${getRiskLevelClass(assetAllocation.riskLevel)}`;
  
  const allocationKeys = Object.keys(assetAllocation.allocation);
  charts.assetAllocation.data.labels = allocationKeys.map(formatLabel);
  charts.assetAllocation.data.datasets[0].data = allocationKeys.map(key => 
    assetAllocation.allocation[key]
  );
  charts.assetAllocation.update();
  
  // Update risk assessment
  const riskAssessment = csfeResult.predictions.riskAssessment;
  const overallRisk = riskAssessment.overallRisk;
  overallRiskElement.textContent = `${overallRisk.toFixed(1)}%`;
  overallRiskBarElement.style.width = `${overallRisk}%`;
  overallRiskBarElement.textContent = `${overallRisk.toFixed(1)}%`;
  overallRiskBarElement.setAttribute('aria-valuenow', overallRisk);
  
  // Update risk factors
  riskFactorsContainerElement.innerHTML = '';
  riskAssessment.riskFactors.forEach(factor => {
    const factorElement = document.createElement('div');
    factorElement.className = 'risk-factor';
    factorElement.innerHTML = `
      <div class="risk-factor-name">
        <span>${factor.name}</span>
        <span class="risk-factor-impact ${factor.impact}">${factor.impact.toUpperCase()} Impact</span>
      </div>
      <div class="progress">
        <div class="progress-bar bg-${getRiskLevelClass(getRiskLevel(factor.level))}" 
             role="progressbar" style="width: ${factor.level}%;" 
             aria-valuenow="${factor.level}" aria-valuemin="0" aria-valuemax="100">
          ${factor.level.toFixed(1)}%
        </div>
      </div>
    `;
    riskFactorsContainerElement.appendChild(factorElement);
  });
  
  // Update timeline predictions
  const timelinePredictions = csfeResult.predictions.timelinePredictions;
  charts.timelinePredictions.data.labels = ['Current', 'Short-term', 'Medium-term', 'Long-term'];
  charts.timelinePredictions.data.datasets[0].data = [
    csfeResult.csfeValue,
    timelinePredictions.short.csfeProjection,
    timelinePredictions.medium.csfeProjection,
    timelinePredictions.long.csfeProjection
  ];
  charts.timelinePredictions.update();
  
  // Update depression prediction (placeholder for now)
  const depressionProbability = calculateDepressionProbability(csfeResult.csfeValue);
  depressionProbabilityBarElement.style.width = `${depressionProbability}%`;
  depressionProbabilityBarElement.textContent = `${depressionProbability.toFixed(1)}%`;
  depressionProbabilityBarElement.setAttribute('aria-valuenow', depressionProbability);
}

/**
 * Update dashboard for Trinity CSFE engine
 */
function updateTrinityDashboard() {
  // Update CSFE components chart
  charts.csfeComponents.data.labels = ['Father (Governance)', 'Son (Detection)', 'Spirit (Response)'];
  charts.csfeComponents.data.datasets[0].data = [
    csfeResult.components.father.result,
    csfeResult.components.son.result,
    csfeResult.components.spirit.result
  ];
  charts.csfeComponents.update();
  
  // Set placeholder values for other charts
  marketDirectionElement.textContent = 'TRINITY MODE';
  marketDirectionElement.className = 'text-primary';
  marketStrengthBarElement.style.width = '100%';
  marketStrengthBarElement.textContent = 'Trinity CSFE Mode';
  marketStrengthBarElement.className = 'progress-bar bg-primary';
  
  riskLevelElement.textContent = 'TRINITY MODE';
  riskLevelElement.className = 'text-primary';
  
  overallRiskElement.textContent = 'N/A';
  overallRiskBarElement.style.width = '0%';
  overallRiskBarElement.textContent = 'Trinity CSFE Mode';
  
  // Update risk factors
  riskFactorsContainerElement.innerHTML = `
    <div class="alert alert-info">
      <strong>Trinity CSFE Mode:</strong> Detailed risk factors are not available in Trinity mode.
      <ul>
        <li>Father Component (Governance): ${csfeResult.components.father.result.toFixed(4)}</li>
        <li>Son Component (Detection): ${csfeResult.components.son.result.toFixed(4)}</li>
        <li>Spirit Component (Response): ${csfeResult.components.spirit.result.toFixed(10)}</li>
      </ul>
    </div>
  `;
  
  // Reset other charts
  charts.assetClassPredictions.data.datasets[0].data = [0, 0, 0, 0, 0];
  charts.assetClassPredictions.update();
  
  charts.assetAllocation.data.datasets[0].data = [0, 0, 0, 0, 0];
  charts.assetAllocation.update();
  
  charts.timelinePredictions.data.datasets[0].data = [
    csfeResult.csfeValue,
    csfeResult.csfeValue * 0.9,
    csfeResult.csfeValue * 0.8,
    csfeResult.csfeValue * 0.7
  ];
  charts.timelinePredictions.update();
}

/**
 * Calculate depression probability (placeholder)
 * @param {Number} csfeValue - CSFE value
 * @returns {Number} - Depression probability
 */
function calculateDepressionProbability(csfeValue) {
  // This is a placeholder calculation
  // In a real implementation, this would use sophisticated analysis
  return Math.max(0, Math.min(100, 100 - (csfeValue / 300)));
}

/**
 * Get CSS class for market direction
 * @param {String} direction - Market direction
 * @returns {String} - CSS class
 */
function getDirectionClass(direction) {
  switch (direction.toLowerCase()) {
    case 'bullish':
      return 'success';
    case 'bearish':
      return 'danger';
    case 'neutral':
    default:
      return 'warning';
  }
}

/**
 * Get CSS class for risk level
 * @param {String} level - Risk level
 * @returns {String} - CSS class
 */
function getRiskLevelClass(level) {
  switch (level.toLowerCase()) {
    case 'aggressive':
    case 'high':
      return 'danger';
    case 'moderate':
    case 'medium':
      return 'warning';
    case 'conservative':
    case 'low':
    default:
      return 'success';
  }
}

/**
 * Get risk level from numeric value
 * @param {Number} value - Risk value
 * @returns {String} - Risk level
 */
function getRiskLevel(value) {
  if (value >= 70) return 'high';
  if (value >= 30) return 'medium';
  return 'low';
}

/**
 * Format timestamp
 * @param {String} timestamp - ISO timestamp
 * @returns {String} - Formatted timestamp
 */
function formatTimestamp(timestamp) {
  const date = new Date(timestamp);
  return date.toLocaleString();
}
