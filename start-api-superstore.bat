@echo off
echo Starting NovaFuse API Superstore...

echo Starting API Gateway...
start cmd /k "cd api-gateway && npm start"

echo Starting NovaConnect...
start cmd /k "cd connector-templates && npm start"

echo Starting Privacy Management API...
start cmd /k "cd nova-marketplace\apis\privacy\management && npm start"

echo Starting Security Assessment API...
start cmd /k "cd nova-marketplace\apis\security\assessment && npm start"

echo Starting Control Testing API...
start cmd /k "cd nova-marketplace\apis\control\testing && npm start"

echo Starting ESG API...
start cmd /k "cd nova-marketplace\apis\esg && npm start"

echo Starting Compliance Automation API...
start cmd /k "cd nova-marketplace\apis\compliance\automation && npm start"

echo NovaFuse API Superstore started successfully!
echo API Gateway: http://localhost:3000
echo API Documentation: http://localhost:3000/api-docs
