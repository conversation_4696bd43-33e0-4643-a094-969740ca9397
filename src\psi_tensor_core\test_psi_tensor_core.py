#!/usr/bin/env python3
"""
Test Script for Ψ Tensor Core

This script demonstrates the functionality of the Ψ Tensor Core by:
1. Creating sample data for CSDE, CSFE, and CSME engines
2. Processing the data through the Unified Ψ Tensor Core
3. Displaying the results
"""

import torch
import numpy as np
import json
import time
from typing import Dict, List, Any
import matplotlib.pyplot as plt

from unified_interface import UnifiedPsiTensorCore

def create_sample_data():
    """
    Create sample data for CSDE, CSFE, and CSME engines.

    Returns:
        Tuple of (csde_data, csfe_data, csme_data)
    """
    # Sample CSDE data
    csde_data = {
        "governance": 0.75,  # High governance score
        "data": 0.85,        # High data quality score
        "action": "Implement enhanced access controls",
        "confidence": 0.9    # High confidence
    }

    # Sample CSFE data
    csfe_data = {
        "risk": 0.65,        # Moderate risk score
        "finance": 0.70,     # Moderate financial impact score
        "action": "Allocate additional budget to security",
        "confidence": 0.8    # High confidence
    }

    # Sample CSME data
    csme_data = {
        "bio": 0.40,         # Low biological risk score
        "med_compliance": 0.90,  # High medical compliance score
        "action": "Update patient data protection protocols",
        "confidence": 0.85   # High confidence
    }

    return csde_data, csfe_data, csme_data

def create_batch_data(batch_size=5):
    """
    Create a batch of sample data for CSDE, CSFE, and CSME engines.

    Args:
        batch_size: Number of samples in the batch

    Returns:
        Tuple of (csde_batch, csfe_batch, csme_batch)
    """
    csde_batch = []
    csfe_batch = []
    csme_batch = []

    actions = [
        "Implement enhanced access controls",
        "Update security policies",
        "Conduct security training",
        "Deploy intrusion detection system",
        "Perform security audit",
        "Update firewall rules",
        "Encrypt sensitive data",
        "Implement multi-factor authentication",
        "Update disaster recovery plan",
        "Conduct penetration testing"
    ]

    for i in range(batch_size):
        # Randomize data for each sample
        csde_batch.append({
            "governance": np.random.uniform(0.3, 0.9),
            "data": np.random.uniform(0.4, 0.95),
            "action": np.random.choice(actions),
            "confidence": np.random.uniform(0.6, 0.95)
        })

        csfe_batch.append({
            "risk": np.random.uniform(0.2, 0.8),
            "finance": np.random.uniform(0.3, 0.9),
            "action": np.random.choice(actions),
            "confidence": np.random.uniform(0.5, 0.9)
        })

        csme_batch.append({
            "bio": np.random.uniform(0.1, 0.7),
            "med_compliance": np.random.uniform(0.5, 0.95),
            "action": np.random.choice(actions),
            "confidence": np.random.uniform(0.7, 0.95)
        })

    return csde_batch, csfe_batch, csme_batch

def plot_weight_history(weight_history):
    """
    Plot the weight history for each engine.

    Args:
        weight_history: Dictionary of weight history for each engine
    """
    plt.figure(figsize=(10, 6))

    for engine, weights in weight_history.items():
        plt.plot(weights, label=engine)

    plt.title('Engine Weight History')
    plt.xlabel('Time Step')
    plt.ylabel('Weight')
    plt.legend()
    plt.grid(True)
    plt.savefig('weight_history.png')
    plt.close()

    print("Weight history plot saved to 'weight_history.png'")

def main():
    """
    Main function to demonstrate the Ψ Tensor Core.
    """
    print("=== Ψ Tensor Core Demonstration ===")

    # Define action space
    action_space = [
        "Implement enhanced access controls",
        "Update security policies",
        "Conduct security training",
        "Deploy intrusion detection system",
        "Perform security audit",
        "Update firewall rules",
        "Encrypt sensitive data",
        "Implement multi-factor authentication",
        "Update disaster recovery plan",
        "Conduct penetration testing",
        "Allocate additional budget to security",
        "Update patient data protection protocols"
    ]

    # Initialize Unified Ψ Tensor Core
    psi_core = UnifiedPsiTensorCore(action_space=action_space, threshold=0.82)

    # Create sample data
    csde_data, csfe_data, csme_data = create_sample_data()

    print("\n1. Processing single sample...")
    start_time = time.time()
    result = psi_core.process(csde_data, csfe_data, csme_data)
    processing_time = time.time() - start_time

    print(f"Processing time: {processing_time:.4f} seconds")
    print("\nResult:")
    print(json.dumps(result, indent=2))

    print("\n2. Processing batch data...")
    batch_size = 10
    csde_batch, csfe_batch, csme_batch = create_batch_data(batch_size)

    start_time = time.time()
    batch_results = psi_core.process_batch(csde_batch, csfe_batch, csme_batch)
    batch_processing_time = time.time() - start_time

    print(f"Batch processing time: {batch_processing_time:.4f} seconds")
    print(f"Average processing time per sample: {batch_processing_time / batch_size:.4f} seconds")

    print("\nConsensus actions from batch:")
    for i, result in enumerate(batch_results):
        print(f"Sample {i+1}: {result['consensus_action']} (Confidence: {result['consensus_confidence']:.2f}, Execute: {result['execute_action']})")

    print("\n3. Analyzing engine weights...")
    weight_history = psi_core.get_weight_history()

    print("Final weights:")
    for engine, weights in weight_history.items():
        print(f"{engine}: {weights[-1]:.4f}")

    # Plot weight history
    plot_weight_history(weight_history)

    print("\n=== Demonstration Complete ===")

if __name__ == "__main__":
    main()
