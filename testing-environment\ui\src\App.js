import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import Dashboard from './pages/Dashboard';
import ConnectorBuilder from './pages/ConnectorBuilder';

// Create a dark theme
const darkTheme = createTheme({
  palette: {
    mode: 'dark',
    primary: {
      main: '#2563eb', // NovaGRC blue
    },
    secondary: {
      main: '#10b981', // Green
    },
    background: {
      default: '#0f172a', // Dark blue
      paper: '#1e293b', // Slightly lighter blue
    },
  },
});

function App() {
  return (
    <ThemeProvider theme={darkTheme}>
      <CssBaseline />
      <Router>
        <Routes>
          <Route path="/" element={<Dashboard />} />
          <Route path="/partner-portal/connectors/new" element={<ConnectorBuilder />} />
        </Routes>
      </Router>
    </ThemeProvider>
  );
}

export default App;
