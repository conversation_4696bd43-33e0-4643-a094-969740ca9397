/**
 * NovaFuse Universal API Connector Validator Tests
 * 
 * This module contains tests for the connector validator.
 */

const { ConnectorValidator } = require('../../src/validation');
const fs = require('fs');
const path = require('path');

describe('ConnectorValidator', () => {
  let googleCloudConnector;
  let microsoftDefenderConnector;
  let awsSecurityHubConnector;
  
  beforeAll(() => {
    // Load test connectors
    googleCloudConnector = JSON.parse(
      fs.readFileSync(path.join(__dirname, '../../templates/google-cloud-security.json'), 'utf8')
    );
    
    microsoftDefenderConnector = JSON.parse(
      fs.readFileSync(path.join(__dirname, '../../templates/microsoft-defender-cloud.json'), 'utf8')
    );
    
    awsSecurityHubConnector = JSON.parse(
      fs.readFileSync(path.join(__dirname, '../../templates/aws-security-hub.json'), 'utf8')
    );
  });
  
  describe('validateConnector', () => {
    test('should validate a valid connector', () => {
      const result = ConnectorValidator.validateConnector(googleCloudConnector);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });
    
    test('should validate another valid connector', () => {
      const result = ConnectorValidator.validateConnector(microsoftDefenderConnector);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });
    
    test('should validate a third valid connector', () => {
      const result = ConnectorValidator.validateConnector(awsSecurityHubConnector);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });
    
    test('should return errors for an invalid connector', () => {
      const invalidConnector = {
        // Missing metadata
        authentication: {
          type: 'API_KEY',
          fields: {
            apiKey: {
              type: 'string',
              required: true
            }
          }
        }
      };
      
      const result = ConnectorValidator.validateConnector(invalidConnector);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Connector metadata is required');
    });
    
    test('should return errors for a connector with invalid metadata', () => {
      const invalidConnector = {
        metadata: {
          // Missing name
          version: '1.0.0',
          category: 'Test',
          description: 'Test connector'
        },
        authentication: {
          type: 'API_KEY',
          fields: {
            apiKey: {
              type: 'string',
              required: true
            }
          }
        }
      };
      
      const result = ConnectorValidator.validateConnector(invalidConnector);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Connector name is required');
    });
    
    test('should return errors for a connector with invalid authentication', () => {
      const invalidConnector = {
        metadata: {
          name: 'Test Connector',
          version: '1.0.0',
          category: 'Test',
          description: 'Test connector'
        },
        authentication: {
          type: 'INVALID_TYPE', // Invalid type
          fields: {
            apiKey: {
              type: 'string',
              required: true
            }
          }
        }
      };
      
      const result = ConnectorValidator.validateConnector(invalidConnector);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Authentication type must be one of: API_KEY, BASIC, OAUTH2, CUSTOM, NONE');
    });
    
    test('should return errors for a connector with missing configuration', () => {
      const invalidConnector = {
        metadata: {
          name: 'Test Connector',
          version: '1.0.0',
          category: 'Test',
          description: 'Test connector'
        },
        authentication: {
          type: 'API_KEY',
          fields: {
            apiKey: {
              type: 'string',
              required: true
            }
          }
        }
        // Missing configuration
      };
      
      const result = ConnectorValidator.validateConnector(invalidConnector);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Connector configuration is required');
    });
    
    test('should return errors for a connector with invalid configuration', () => {
      const invalidConnector = {
        metadata: {
          name: 'Test Connector',
          version: '1.0.0',
          category: 'Test',
          description: 'Test connector'
        },
        authentication: {
          type: 'API_KEY',
          fields: {
            apiKey: {
              type: 'string',
              required: true
            }
          }
        },
        configuration: {
          // Missing baseUrl
          headers: {
            'Content-Type': 'application/json'
          }
        }
      };
      
      const result = ConnectorValidator.validateConnector(invalidConnector);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Configuration base URL is required');
    });
    
    test('should return errors for a connector with missing endpoints', () => {
      const invalidConnector = {
        metadata: {
          name: 'Test Connector',
          version: '1.0.0',
          category: 'Test',
          description: 'Test connector'
        },
        authentication: {
          type: 'API_KEY',
          fields: {
            apiKey: {
              type: 'string',
              required: true
            }
          }
        },
        configuration: {
          baseUrl: 'https://api.example.com'
        }
        // Missing endpoints
      };
      
      const result = ConnectorValidator.validateConnector(invalidConnector);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Connector endpoints are required');
    });
    
    test('should return errors for a connector with invalid endpoints', () => {
      const invalidConnector = {
        metadata: {
          name: 'Test Connector',
          version: '1.0.0',
          category: 'Test',
          description: 'Test connector'
        },
        authentication: {
          type: 'API_KEY',
          fields: {
            apiKey: {
              type: 'string',
              required: true
            }
          }
        },
        configuration: {
          baseUrl: 'https://api.example.com'
        },
        endpoints: [
          {
            // Missing id
            name: 'Test Endpoint',
            path: '/test',
            method: 'GET'
          }
        ]
      };
      
      const result = ConnectorValidator.validateConnector(invalidConnector);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Endpoint at index 0 must have an ID');
    });
    
    test('should return errors for a connector with duplicate endpoint IDs', () => {
      const invalidConnector = {
        metadata: {
          name: 'Test Connector',
          version: '1.0.0',
          category: 'Test',
          description: 'Test connector'
        },
        authentication: {
          type: 'API_KEY',
          fields: {
            apiKey: {
              type: 'string',
              required: true
            }
          }
        },
        configuration: {
          baseUrl: 'https://api.example.com'
        },
        endpoints: [
          {
            id: 'test',
            name: 'Test Endpoint 1',
            path: '/test1',
            method: 'GET'
          },
          {
            id: 'test', // Duplicate ID
            name: 'Test Endpoint 2',
            path: '/test2',
            method: 'GET'
          }
        ]
      };
      
      const result = ConnectorValidator.validateConnector(invalidConnector);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Duplicate endpoint IDs found: test');
    });
  });
  
  describe('validateMetadata', () => {
    test('should validate valid metadata', () => {
      const errors = [];
      ConnectorValidator.validateMetadata(googleCloudConnector, errors);
      expect(errors).toHaveLength(0);
    });
    
    test('should return errors for invalid metadata', () => {
      const errors = [];
      const invalidConnector = {
        metadata: {
          name: 123, // Should be a string
          version: '1.0', // Invalid semver
          category: 'Test',
          description: 'Test connector'
        }
      };
      
      ConnectorValidator.validateMetadata(invalidConnector, errors);
      expect(errors).toContain('Connector name must be a string');
      expect(errors).toContain('Connector version must be in semver format (e.g., 1.0.0)');
    });
  });
  
  describe('validateAuthentication', () => {
    test('should validate valid authentication', () => {
      const errors = [];
      ConnectorValidator.validateAuthentication(googleCloudConnector, errors);
      expect(errors).toHaveLength(0);
    });
    
    test('should return errors for invalid authentication', () => {
      const errors = [];
      const invalidConnector = {
        authentication: {
          type: 'INVALID_TYPE',
          fields: {
            apiKey: {
              type: 'invalid_type',
              required: 'yes' // Should be a boolean
            }
          }
        }
      };
      
      ConnectorValidator.validateAuthentication(invalidConnector, errors);
      expect(errors).toContain('Authentication type must be one of: API_KEY, BASIC, OAUTH2, CUSTOM, NONE');
      expect(errors).toContain('Authentication field \'apiKey\' type must be one of: string, number, boolean, object, array');
      expect(errors).toContain('Authentication field \'apiKey\' required must be a boolean');
    });
  });
  
  describe('validateConfiguration', () => {
    test('should validate valid configuration', () => {
      const errors = [];
      ConnectorValidator.validateConfiguration(googleCloudConnector, errors);
      expect(errors).toHaveLength(0);
    });
    
    test('should return errors for invalid configuration', () => {
      const errors = [];
      const invalidConnector = {
        configuration: {
          baseUrl: 'invalid-url',
          timeout: -1, // Should be positive
          rateLimit: 'invalid' // Should be an object
        }
      };
      
      ConnectorValidator.validateConfiguration(invalidConnector, errors);
      expect(errors).toContain('Configuration base URL must be a valid URL');
      expect(errors).toContain('Configuration timeout must be greater than 0');
      expect(errors).toContain('Configuration rate limit must be an object');
    });
  });
  
  describe('validateEndpoints', () => {
    test('should validate valid endpoints', () => {
      const errors = [];
      ConnectorValidator.validateEndpoints(googleCloudConnector, errors);
      expect(errors).toHaveLength(0);
    });
    
    test('should return errors for invalid endpoints', () => {
      const errors = [];
      const invalidConnector = {
        endpoints: [
          {
            // Missing id
            name: 'Test Endpoint',
            path: '/test',
            method: 'INVALID_METHOD' // Invalid method
          }
        ]
      };
      
      ConnectorValidator.validateEndpoints(invalidConnector, errors);
      expect(errors).toContain('Endpoint at index 0 must have an ID');
      expect(errors).toContain('Endpoint at index 0 method must be one of: GET, POST, PUT, PATCH, DELETE');
    });
  });
  
  describe('utility methods', () => {
    test('isValidUrl should validate URLs correctly', () => {
      expect(ConnectorValidator.isValidUrl('https://example.com')).toBe(true);
      expect(ConnectorValidator.isValidUrl('http://example.com/path')).toBe(true);
      expect(ConnectorValidator.isValidUrl('invalid-url')).toBe(false);
      expect(ConnectorValidator.isValidUrl('example.com')).toBe(false);
    });
    
    test('isValidVersion should validate semver versions correctly', () => {
      expect(ConnectorValidator.isValidVersion('1.0.0')).toBe(true);
      expect(ConnectorValidator.isValidVersion('1.0.0-alpha')).toBe(true);
      expect(ConnectorValidator.isValidVersion('1.0.0-alpha.1')).toBe(true);
      expect(ConnectorValidator.isValidVersion('1.0')).toBe(false);
      expect(ConnectorValidator.isValidVersion('v1.0.0')).toBe(false);
      expect(ConnectorValidator.isValidVersion('1')).toBe(false);
    });
  });
});
