# Script to analyze diagram content and identify duplicates

$diagramDir = "D:\novafuse-api-superstore\Comphyology Diagrams\Mermaid"
$diagramFiles = Get-ChildItem -Path $diagramDir -Filter "*.mmd" -File

# Dictionary to store content hashes
$contentMap = @{}
$duplicates = @()

Write-Host "Analyzing $(@($diagramFiles).Count) diagram files..." -ForegroundColor Cyan

foreach ($file in $diagramFiles) {
    $content = Get-Content -Path $file.FullName -Raw
    $hash = $content.GetHashCode()
    
    if ($contentMap.ContainsKey($hash)) {
        $duplicates += @{
            File = $file.Name
            DuplicateOf = $contentMap[$hash]
            Size = $file.Length
            LastModified = $file.LastWriteTime
        }
    } else {
        $contentMap[$hash] = $file.Name
    }
}

# Report results
Write-Host "`nFound $($contentMap.Count) unique diagrams" -ForegroundColor Green
Write-Host "Found $($duplicates.Count) duplicate files" -ForegroundColor Yellow

# Show duplicate groups
if ($duplicates.Count -gt 0) {
    Write-Host "`nDuplicate Files:" -ForegroundColor Yellow
    $duplicates | Format-Table -AutoSize | Out-String -Width 200
}

# Show potential naming issues (files with similar names but different content)
Write-Host "`nPotential Naming Issues:" -ForegroundColor Yellow
$baseNames = $diagramFiles | ForEach-Object { 
    if ($_.Name -match '^(FIG\d+_)?([^_]+)') { 
        $matches[2] 
    } 
} | Group-Object | Where-Object { $_.Count -gt 1 }

foreach ($group in $baseNames) {
    Write-Host "`nFiles with base name '$($group.Name)':" -ForegroundColor Cyan
    $diagramFiles | Where-Object { $_.Name -match $group.Name } | 
        Select-Object Name, Length, LastWriteTime | 
        Format-Table -AutoSize | 
        Out-String -Width 200
}
