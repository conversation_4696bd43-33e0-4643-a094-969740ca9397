/**
 * Unit tests for the Cache Utility
 */

// Mock the logger
jest.mock('../../../src/utils/logger', () => ({
  createLogger: jest.fn(() => ({
    info: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn(),
    error: jest.fn()
  }))
}));

// Import the cache module after mocking dependencies
const Cache = require('../../../src/utils/cache');

describe('Cache Utility', () => {
  let originalDateNow;
  let mockTime = 1000000000000; // Fixed timestamp for testing

  // Mock Date.now() for consistent testing
  beforeEach(() => {
    originalDateNow = Date.now;
    Date.now = jest.fn(() => mockTime);

    // Clear the cache before each test
    Cache.clear();
  });

  // Restore original Date.now after tests
  afterEach(() => {
    Date.now = originalDateNow;
  });

  describe('set', () => {
    it('should set a value in the cache', () => {
      const result = Cache.set('test-key', 'test-value');

      expect(result).toBe(true);
      expect(Cache.has('test-key')).toBe(true);
    });

    it('should set a value with custom TTL', () => {
      Cache.set('test-key', 'test-value', 60); // 60 seconds TTL

      // Advance time by 30 seconds
      mockTime += 30 * 1000;

      // Value should still be in cache
      expect(Cache.has('test-key')).toBe(true);

      // Advance time by another 31 seconds (total 61 seconds)
      mockTime += 31 * 1000;

      // Value should be expired
      expect(Cache.has('test-key')).toBe(false);
    });

    it('should update an existing value', () => {
      Cache.set('test-key', 'original-value');
      Cache.set('test-key', 'updated-value');

      expect(Cache.get('test-key')).toBe('updated-value');
    });
  });

  describe('get', () => {
    it('should return undefined for non-existent key', () => {
      expect(Cache.get('non-existent')).toBeUndefined();
    });

    it('should return the cached value for existing key', () => {
      Cache.set('test-key', 'test-value');

      expect(Cache.get('test-key')).toBe('test-value');
    });

    it('should return undefined for expired key', () => {
      Cache.set('test-key', 'test-value', 10); // 10 seconds TTL

      // Advance time by 11 seconds
      mockTime += 11 * 1000;

      expect(Cache.get('test-key')).toBeUndefined();
    });

    it('should update lastAccessed time on get', () => {
      Cache.set('test-key', 'test-value');

      // Initial lastAccessed time
      const initialTime = Cache.cache.get('test-key').lastAccessed;

      // Advance time
      mockTime += 1000;

      // Get the value
      Cache.get('test-key');

      // Check that lastAccessed was updated
      const updatedTime = Cache.cache.get('test-key').lastAccessed;
      expect(updatedTime).toBeGreaterThan(initialTime);
    });
  });

  describe('has', () => {
    it('should return false for non-existent key', () => {
      expect(Cache.has('non-existent')).toBe(false);
    });

    it('should return true for existing key', () => {
      Cache.set('test-key', 'test-value');

      expect(Cache.has('test-key')).toBe(true);
    });

    it('should return false for expired key', () => {
      Cache.set('test-key', 'test-value', 10); // 10 seconds TTL

      // Advance time by 11 seconds
      mockTime += 11 * 1000;

      expect(Cache.has('test-key')).toBe(false);
    });
  });

  describe('delete', () => {
    it('should delete a key from the cache', () => {
      Cache.set('test-key', 'test-value');

      expect(Cache.has('test-key')).toBe(true);

      const result = Cache.delete('test-key');

      expect(result).toBe(true);
      expect(Cache.has('test-key')).toBe(false);
    });

    it('should return false when deleting non-existent key', () => {
      const result = Cache.delete('non-existent');

      expect(result).toBe(false);
    });
  });

  describe('clear', () => {
    it('should clear all keys from the cache', () => {
      Cache.set('key1', 'value1');
      Cache.set('key2', 'value2');

      expect(Cache.has('key1')).toBe(true);
      expect(Cache.has('key2')).toBe(true);

      Cache.clear();

      expect(Cache.has('key1')).toBe(false);
      expect(Cache.has('key2')).toBe(false);
    });
  });

  describe('cleanup', () => {
    it('should remove expired items during cleanup', () => {
      // Set items with different TTLs
      Cache.set('key1', 'value1', 10); // 10 seconds TTL
      Cache.set('key2', 'value2', 20); // 20 seconds TTL

      expect(Cache.has('key1')).toBe(true);
      expect(Cache.has('key2')).toBe(true);

      // Advance time by 15 seconds
      mockTime += 15 * 1000;

      // Run cleanup
      Cache.cleanup();

      // key1 should be removed, key2 should remain
      expect(Cache.has('key1')).toBe(false);
      expect(Cache.has('key2')).toBe(true);
    });
  });

  describe('evict', () => {
    it('should evict least recently used items when cache is full', () => {
      // Create a new cache with small maxSize for testing eviction
      const smallCache = new (Cache.constructor)({ maxSize: 3 });

      // Set initial items
      smallCache.set('key1', 'value1');
      smallCache.set('key2', 'value2');
      smallCache.set('key3', 'value3');

      expect(smallCache.cache.size).toBe(3);

      // Access key2 to make it more recently used
      mockTime += 1000;
      smallCache.get('key2');

      // Access key3 to make it the most recently used
      mockTime += 1000;
      smallCache.get('key3');

      // Add a new item to trigger eviction
      smallCache.set('key4', 'value4');

      // key1 should be evicted as it's the least recently used
      expect(smallCache.has('key1')).toBe(false);
      expect(smallCache.has('key2')).toBe(true);
      expect(smallCache.has('key3')).toBe(true);
      expect(smallCache.has('key4')).toBe(true);

      // Clean up
      smallCache.stop();
    });
  });

  describe('getStats', () => {
    it('should return cache statistics', () => {
      // Set some values
      Cache.set('key1', 'value1');
      Cache.set('key2', 'value2');

      // Get some values (hits)
      Cache.get('key1');
      Cache.get('key2');

      // Get non-existent value (miss)
      Cache.get('non-existent');

      const stats = Cache.getStats();

      // Skip size check as it may vary
      expect(stats.hits).toBeGreaterThanOrEqual(2);
      expect(stats.misses).toBe(1);
      expect(stats.sets).toBe(2);
      expect(stats.hitRate).toBeCloseTo(2/3, 2); // 2 hits out of 3 attempts
    });
  });
});
