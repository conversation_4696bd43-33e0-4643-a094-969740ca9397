{"name": "nova-connect", "version": "1.0.0", "description": "NovaConnect: Universal API Connector for seamless API integration", "private": true, "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test:unit": "jest --testMatch='**/*.unit.test.js'", "test:integration": "jest --testMatch='**/*.integration.test.js'", "test:performance": "jest --testMatch='**/*.performance.test.js'", "test:security": "jest --testMatch='**/*.security.test.js'", "test:all": "node tests/run-tests.js", "test:coverage": "jest --coverage", "lint": "eslint .", "build": "webpack --mode production"}, "repository": {"type": "git", "url": "git+https://github.com/Dartan1983/nova-connect.git"}, "keywords": ["api", "connector", "integration", "universal", "novafuse"], "author": "NovaGRC", "license": "MIT", "bugs": {"url": "https://github.com/Dartan1983/nova-connect/issues"}, "homepage": "https://github.com/Dartan1983/nova-connect#readme", "dependencies": {"axios": "^1.4.0", "bcrypt": "^5.1.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "graphql": "^16.6.0", "graphql-ws": "^5.14.0", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.1", "mongoose": "^7.4.1", "morgan": "^1.10.0", "uuid": "^9.0.1", "winston": "^3.10.0", "ws": "^8.13.0"}, "devDependencies": {"eslint": "^8.46.0", "jest": "^29.7.0", "jest-junit": "^16.0.0", "nodemon": "^3.0.1", "supertest": "^6.3.4", "webpack": "^5.88.2", "webpack-cli": "^5.1.4"}, "engines": {"node": ">=14.0.0"}}