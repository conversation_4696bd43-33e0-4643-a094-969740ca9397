import React from 'react';
import Link from 'next/link';
import Sidebar from '../components/Sidebar';

export default function ApiDocs() {
  const sidebarItems = [
    { type: 'category', label: 'API Categories', items: [
      { label: 'Governance & Board Compliance', href: '#governance' },
      { label: 'Security', href: '#security' },
      { label: 'APIs & Developer Tools', href: '#apis' },
      { label: 'Risk & Audit', href: '#risk' },
      { label: 'Contracts & Policy Lifecycle', href: '#contracts' },
      { label: 'Certifications & Accreditation', href: '#certifications' }
    ]},
    { type: 'category', label: 'SDKs', items: [
      { label: 'JavaScript', href: '#javascript' },
      { label: 'Python', href: '#python' },
      { label: 'Java', href: '#java' },
      { label: 'C#', href: '#csharp' }
    ]},
    { type: 'category', label: 'Resources', items: [
      { label: 'Getting Started', href: '#getting-started' },
      { label: 'Authentication', href: '#authentication' },
      { label: 'Rate Limits', href: '#rate-limits' },
      { label: 'Error Handling', href: '#error-handling' }
    ]}
  ];

  return (
    <div className="flex flex-col md:flex-row gap-8">
      {/* Sidebar - Hidden on mobile, shown on desktop */}
      <div className="hidden md:block md:w-1/4 lg:w-1/5">
        <div className="sticky top-4">
          <Sidebar items={sidebarItems} title="API Documentation" />
        </div>
      </div>

      {/* Mobile Sidebar Toggle - Shown on mobile only */}
      <div className="md:hidden mb-4">
        <select
          className="w-full bg-secondary text-white border border-gray-700 rounded p-2"
          onChange={(e) => {
            if (e.target.value) window.location.href = e.target.value;
          }}
          defaultValue=""
        >
          <option value="" disabled>Navigate to...</option>
          {sidebarItems.map((item, index) => (
            item.type === 'category' ? (
              <optgroup key={index} label={item.label}>
                {item.items.map((subItem, subIndex) => (
                  <option key={`${index}-${subIndex}`} value={subItem.href}>
                    {subItem.label}
                  </option>
                ))}
              </optgroup>
            ) : (
              <option key={index} value={item.href}>
                {item.label}
              </option>
            )
          ))}
        </select>
      </div>

      {/* Main Content */}
      <div className="w-full md:w-3/4 lg:w-4/5">



        {/* Introduction */}
        <div className="mb-12">
          <h2 className="text-3xl font-bold mb-4">Introduction</h2>
          <p className="mb-4">
            Welcome to the NovaFuse API Superstore documentation. This API allows you to integrate with NovaFuse's governance, risk, and compliance platform.
          </p>
          <p className="mb-4">
            All API requests require authentication using an API key. You can obtain an API key by registering as a partner.
          </p>
          <div className="bg-secondary p-4 rounded-lg mb-4">
            <h3 className="text-xl font-bold mb-2">Base URL</h3>
            <code className="text-green-400">https://api.novafuse.io</code>
          </div>
          <div className="bg-secondary p-4 rounded-lg">
            <h3 className="text-xl font-bold mb-2">Authentication</h3>
            <p className="mb-2">Add your API key to the request header:</p>
            <code className="text-green-400">apikey: YOUR_API_KEY</code>
          </div>
        </div>

        {/* Governance API */}
        <div id="governance" className="mb-12">
          <h2 className="text-3xl font-bold mb-6">Governance & Board Compliance</h2>

          {/* Board Meetings Endpoint */}
          <div className="bg-secondary p-6 rounded-lg mb-6">
            <div className="flex justify-between items-start mb-4">
              <div>
                <span className="method get">GET</span>
                <code className="text-green-400 ml-2">/governance/board/meetings</code>
              </div>
              <button className="text-sm bg-gray-700 px-3 py-1 rounded hover:bg-gray-600">Try It</button>
            </div>
            <p className="mb-4">Get a list of board meetings with optional filtering.</p>

            <h4 className="font-bold mb-2">Parameters</h4>
            <table className="w-full mb-4">
              <thead className="bg-gray-800">
                <tr>
                  <th className="text-left p-2">Name</th>
                  <th className="text-left p-2">Type</th>
                  <th className="text-left p-2">Description</th>
                </tr>
              </thead>
              <tbody>
                <tr className="border-b border-gray-700">
                  <td className="p-2">status</td>
                  <td className="p-2">string</td>
                  <td className="p-2">Filter by status (scheduled, completed, cancelled)</td>
                </tr>
                <tr className="border-b border-gray-700">
                  <td className="p-2">start_date</td>
                  <td className="p-2">string</td>
                  <td className="p-2">Filter by start date (YYYY-MM-DD)</td>
                </tr>
                <tr>
                  <td className="p-2">end_date</td>
                  <td className="p-2">string</td>
                  <td className="p-2">Filter by end date (YYYY-MM-DD)</td>
                </tr>
              </tbody>
            </table>

            <h4 className="font-bold mb-2">Response</h4>
            <pre className="text-green-400">
{`{
  "data": [
    {
      "id": "bm-001",
      "title": "Q1 Board Meeting",
      "date": "2025-01-15",
      "status": "scheduled",
      "agenda": [
        "Financial review",
        "Strategic initiatives",
        "Compliance updates"
      ],
      "participants": [
        { "id": "p-001", "name": "John Smith", "role": "Chairman" },
        { "id": "p-002", "name": "Jane Doe", "role": "CEO" },
        { "id": "p-003", "name": "Bob Johnson", "role": "CFO" }
      ]
    },
    {
      "id": "bm-002",
      "title": "Q2 Board Meeting",
      "date": "2025-04-15",
      "status": "scheduled"
    }
  ],
  "total": 2,
  "page": 1,
  "limit": 10
}`}
            </pre>
          </div>
        </div>

        {/* Security API */}
        <div id="security" className="mb-12">
          <h2 className="text-3xl font-bold mb-6">Security</h2>

          {/* Vulnerabilities Endpoint */}
          <div className="bg-secondary p-6 rounded-lg mb-6">
            <div className="flex justify-between items-start mb-4">
              <div>
                <span className="method get">GET</span>
                <code className="text-green-400 ml-2">/security/vulnerabilities</code>
              </div>
              <button className="text-sm bg-gray-700 px-3 py-1 rounded hover:bg-gray-600">Try It</button>
            </div>
            <p className="mb-4">Get a list of security vulnerabilities with optional filtering.</p>

            <h4 className="font-bold mb-2">Parameters</h4>
            <table className="w-full mb-4">
              <thead className="bg-gray-800">
                <tr>
                  <th className="text-left p-2">Name</th>
                  <th className="text-left p-2">Type</th>
                  <th className="text-left p-2">Description</th>
                </tr>
              </thead>
              <tbody>
                <tr className="border-b border-gray-700">
                  <td className="p-2">severity</td>
                  <td className="p-2">string</td>
                  <td className="p-2">Filter by severity (critical, high, medium, low)</td>
                </tr>
                <tr className="border-b border-gray-700">
                  <td className="p-2">status</td>
                  <td className="p-2">string</td>
                  <td className="p-2">Filter by status (open, in_progress, resolved)</td>
                </tr>
                <tr>
                  <td className="p-2">asset_id</td>
                  <td className="p-2">string</td>
                  <td className="p-2">Filter by asset ID</td>
                </tr>
              </tbody>
            </table>

            <h4 className="font-bold mb-2">Response</h4>
            <pre className="text-green-400">
{`{
  "data": [
    {
      "id": "vuln-001",
      "title": "SQL Injection Vulnerability",
      "description": "SQL injection vulnerability in login form",
      "severity": "high",
      "status": "open",
      "discovered_date": "2025-01-10",
      "asset_id": "asset-123",
      "remediation_steps": [
        "Implement prepared statements",
        "Add input validation",
        "Update ORM library"
      ]
    },
    {
      "id": "vuln-002",
      "title": "Outdated SSL Certificate",
      "description": "SSL certificate is outdated and needs renewal",
      "severity": "medium",
      "status": "in_progress",
      "discovered_date": "2025-01-15",
      "asset_id": "asset-456"
    }
  ],
  "total": 2,
  "page": 1,
  "limit": 10
}`}
            </pre>
          </div>
        </div>

        {/* More API sections would go here */}
      </div>
    </div>
  );
}
