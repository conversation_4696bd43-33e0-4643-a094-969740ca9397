# NovaConnect Role-Based Access Control (RBAC) System

This document provides an overview of the NovaConnect RBAC system, which provides comprehensive role-based access control for the NovaConnect platform.

## Overview

The RBAC system is designed to provide fine-grained control over who can access what resources and perform what actions within the NovaConnect platform. It is based on the following core concepts:

- **Permissions**: Define what actions can be performed on what resources
- **Roles**: Collections of permissions that can be assigned to users
- **User Roles**: Assignments of roles to users, optionally within specific scopes

## Architecture

The RBAC system consists of the following components:

### Models

- **Permission**: Defines a specific action that can be performed on a specific resource
- **Role**: A collection of permissions that can be assigned to users
- **UserRole**: Associates a user with a role, optionally within a specific scope

### Services

- **RBACService**: Core service that manages roles, permissions, and user-role assignments

### Middleware

- **rbacMiddleware**: Express middleware for role-based and permission-based access control
- **authMiddleware**: Authentication middleware with RBAC integration

## Permissions

Permissions are defined as `resource:action` pairs, where:

- **resource**: The type of resource being accessed (e.g., `connector`, `workflow`, `user`)
- **action**: The action being performed on the resource (e.g., `view`, `create`, `edit`, `delete`)

Special permissions:

- `*`: Wildcard permission that grants access to all resources and actions
- `resource:*`: Wildcard permission that grants access to all actions on a specific resource

## Roles

Roles are collections of permissions. The system includes the following default roles:

- **Administrator**: Full access to all resources (`*` permission)
- **Manager**: Access to most resources, but cannot manage users or system settings
- **User**: Access to connectors, data normalization, and workflows
- **Viewer**: Read-only access to connectors, data normalization, and workflows

Custom roles can be created with specific permissions.

## User Roles

User roles associate users with roles, optionally within specific scopes:

- **global**: Role applies to all resources (default)
- **team**: Role applies only within a specific team
- **project**: Role applies only within a specific project
- **environment**: Role applies only within a specific environment

## Usage

### Checking Permissions

To check if a user has a specific permission:

```javascript
const hasPermission = await rbacService.hasPermission(userId, 'connector:view');
```

### Getting User Permissions

To get all permissions for a user:

```javascript
const permissions = await rbacService.getUserPermissions(userId);
```

### Assigning Roles

To assign a role to a user:

```javascript
await rbacService.assignRoleToUser(userId, roleId);
```

To assign a role to a user within a specific scope:

```javascript
await rbacService.assignRoleToUser(userId, roleId, 'team', teamId);
```

### Using Middleware

To protect a route with permission-based access control:

```javascript
router.get('/connectors', authMiddleware.hasPermission('connector:view'), connectorController.getAllConnectors);
```

To protect a route with role-based access control:

```javascript
router.post('/users', authMiddleware.hasRole('Administrator'), userController.createUser);
```

To require multiple permissions:

```javascript
router.put('/connectors/:id', authMiddleware.hasPermission(['connector:edit', 'connector:use'], { all: true }), connectorController.updateConnector);
```

## API Reference

### RBACService

#### Role Management

- `getAllRoles()`: Get all roles
- `getRoleById(roleId)`: Get role by ID
- `createRole(data)`: Create a new role
- `updateRole(roleId, data)`: Update a role
- `deleteRole(roleId)`: Delete a role

#### Permission Management

- `getAllPermissions()`: Get all permissions
- `getPermissionById(permissionId)`: Get permission by ID
- `getPermissionByResourceAction(resource, action)`: Get permission by resource and action
- `createPermission(data)`: Create a new permission
- `updatePermission(permissionId, data)`: Update a permission
- `deletePermission(permissionId)`: Delete a permission

#### User Role Management

- `getUserRoles(userId)`: Get user roles
- `getUsersByRole(roleId)`: Get users by role
- `assignRoleToUser(userId, roleId, scope, scopeId)`: Assign role to user
- `removeRoleFromUser(userId, roleId, scope, scopeId)`: Remove role from user
- `hasPermission(userId, permissionId)`: Check if user has permission
- `getUserPermissions(userId)`: Get user permissions

### Middleware

#### rbacMiddleware

- `hasPermission(permission, options)`: Check if user has permission
- `hasRole(role, options)`: Check if user has role
- `isAdmin(req, res, next)`: Check if user is an admin

#### authMiddleware

- `hasPermission(permission, options)`: Check if user has permission
- `hasRole(role, options)`: Check if user has role
- `isAdmin(req, res, next)`: Check if user is an admin

## Best Practices

1. **Use the most specific permission**: Always use the most specific permission required for an operation.
2. **Check permissions, not roles**: Check for specific permissions rather than roles when possible.
3. **Use middleware**: Use the provided middleware for access control rather than implementing custom checks.
4. **Scope roles appropriately**: Use scoped roles to limit access to specific teams, projects, or environments.
5. **Don't modify system roles**: Don't modify or delete system roles, as they are required for the system to function properly.

## Security Considerations

1. **Principle of least privilege**: Assign users the minimum permissions required to perform their tasks.
2. **Regular audits**: Regularly audit user roles and permissions to ensure they are appropriate.
3. **Role separation**: Separate administrative roles from regular user roles.
4. **Permission granularity**: Define permissions at an appropriate level of granularity for your application.
5. **Secure defaults**: Default to denying access unless explicitly granted.
