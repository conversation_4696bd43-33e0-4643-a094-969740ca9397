{"numFailedTestSuites": 0, "numFailedTests": 0, "numPassedTestSuites": 2, "numPassedTests": 13, "numPendingTestSuites": 0, "numPendingTests": 0, "numRuntimeErrorTestSuites": 0, "numTodoTests": 0, "numTotalTestSuites": 2, "numTotalTests": 13, "openHandles": [], "snapshot": {"added": 0, "didUpdate": false, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0}, "startTime": 1747982273355, "success": false, "testResults": [{"leaks": false, "numFailingTests": 0, "numPassingTests": 6, "numPendingTests": 0, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 1747982277815, "runtime": 3084, "slow": false, "start": 1747982274731}, "skipped": false, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testFilePath": "D:\\novafuse-api-superstore\\tests\\unit\\novaproof\\blockchain-verification.test.js", "testResults": [{"ancestorTitles": ["NovaProof Blockchain Verification", "Evidence Verification"], "duration": 27, "failureDetails": [], "failureMessages": [], "fullName": "NovaProof Blockchain Verification Evidence Verification should verify evidence correctly", "invocations": 1, "location": null, "numPassingAsserts": 6, "retryReasons": [], "status": "passed", "title": "should verify evidence correctly"}, {"ancestorTitles": ["NovaProof Blockchain Verification", "Evidence Verification"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "NovaProof Blockchain Verification Evidence Verification should handle evidence with attachments", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "should handle evidence with attachments"}, {"ancestorTitles": ["NovaProof Blockchain Verification", "Proof Generation"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "NovaProof Blockchain Verification Proof Generation should generate proof correctly", "invocations": 1, "location": null, "numPassingAsserts": 8, "retryReasons": [], "status": "passed", "title": "should generate proof correctly"}, {"ancestorTitles": ["NovaProof Blockchain Verification", "Proof Verification"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "NovaProof Blockchain Verification Proof Verification should verify proof correctly", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should verify proof correctly"}, {"ancestorTitles": ["NovaProof Blockchain Verification", "Proof Verification"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "NovaProof Blockchain Verification Proof Verification should verify actual <PERSON><PERSON>le proofs correctly", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should verify actual <PERSON><PERSON><PERSON> proofs correctly"}, {"ancestorTitles": ["NovaProof Blockchain Verification", "Performance"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "NovaProof Blockchain Verification Performance should verify evidence efficiently", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should verify evidence efficiently"}], "failureMessage": null}, {"leaks": false, "numFailingTests": 0, "numPassingTests": 7, "numPendingTests": 0, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 1747982278254, "runtime": 3359, "slow": false, "start": 1747982274895}, "skipped": false, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testFilePath": "D:\\novafuse-api-superstore\\tests\\integration\\novaproof\\evidence-verification.test.js", "testResults": [{"ancestorTitles": ["NovaProof API Integration Tests", "Evidence Endpoints"], "duration": 39, "failureDetails": [], "failureMessages": [], "fullName": "NovaProof API Integration Tests Evidence Endpoints GET /api/v1/evidence should return a list of evidence items", "invocations": 1, "location": null, "numPassingAsserts": 5, "retryReasons": [], "status": "passed", "title": "GET /api/v1/evidence should return a list of evidence items"}, {"ancestorTitles": ["NovaProof API Integration Tests", "Evidence Endpoints"], "duration": 22, "failureDetails": [], "failureMessages": [], "fullName": "NovaProof API Integration Tests Evidence Endpoints POST /api/v1/evidence should create a new evidence item", "invocations": 1, "location": null, "numPassingAsserts": 6, "retryReasons": [], "status": "passed", "title": "POST /api/v1/evidence should create a new evidence item"}, {"ancestorTitles": ["NovaProof API Integration Tests", "Evidence Endpoints"], "duration": 6, "failureDetails": [], "failureMessages": [], "fullName": "NovaProof API Integration Tests Evidence Endpoints GET /api/v1/evidence/:id should return a specific evidence item", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "GET /api/v1/evidence/:id should return a specific evidence item"}, {"ancestorTitles": ["NovaProof API Integration Tests", "Verification Endpoints"], "duration": 7, "failureDetails": [], "failureMessages": [], "fullName": "NovaProof API Integration Tests Verification Endpoints POST /api/v1/verification should verify an evidence item", "invocations": 1, "location": null, "numPassingAsserts": 6, "retryReasons": [], "status": "passed", "title": "POST /api/v1/verification should verify an evidence item"}, {"ancestorTitles": ["NovaProof API Integration Tests", "Verification Endpoints"], "duration": 9, "failureDetails": [], "failureMessages": [], "fullName": "NovaProof API Integration Tests Verification Endpoints GET /api/v1/verification should return a list of verifications", "invocations": 1, "location": null, "numPassingAsserts": 5, "retryReasons": [], "status": "passed", "title": "GET /api/v1/verification should return a list of verifications"}, {"ancestorTitles": ["NovaProof API Integration Tests", "Verification Endpoints"], "duration": 6, "failureDetails": [], "failureMessages": [], "fullName": "NovaProof API Integration Tests Verification Endpoints GET /api/v1/verification/:id should return a specific verification", "invocations": 1, "location": null, "numPassingAsserts": 6, "retryReasons": [], "status": "passed", "title": "GET /api/v1/verification/:id should return a specific verification"}, {"ancestorTitles": ["NovaProof API Integration Tests", "Verification Endpoints"], "duration": 5, "failureDetails": [], "failureMessages": [], "fullName": "NovaProof API Integration Tests Verification Endpoints GET /api/v1/verification/:id/proof should return the proof for a verification", "invocations": 1, "location": null, "numPassingAsserts": 6, "retryReasons": [], "status": "passed", "title": "GET /api/v1/verification/:id/proof should return the proof for a verification"}], "failureMessage": null}], "wasInterrupted": false}