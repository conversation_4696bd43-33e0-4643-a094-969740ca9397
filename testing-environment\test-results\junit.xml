<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="jest tests" tests="24" failures="24" errors="0" time="32.287">
  <testsuite name="Authentication Tests" errors="0" failures="24" skipped="0" timestamp="2025-04-07T23:43:02" time="31.997" tests="24">
    <testcase classname="Authentication Tests API Key Authentication should register the API Key connector" name="Authentication Tests API Key Authentication should register the API Key connector" time="0.002">
      <failure>Error: Service at http://localhost:3005/health is not ready after 30 retries
    at waitForService (D:\novafuse-api-superstore\testing-environment\api-connection-testing\setup.js:85:9)
    at async Promise.all (index 0)
    at startAllServices (D:\novafuse-api-superstore\testing-environment\api-connection-testing\setup.js:131:5)
    at Object.&lt;anonymous&gt; (D:\novafuse-api-superstore\testing-environment\api-connection-testing\tests\authentication.test.js:88:5)</failure>
    </testcase>
    <testcase classname="Authentication Tests API Key Authentication should store API Key credentials" name="Authentication Tests API Key Authentication should store API Key credentials" time="0">
      <failure>Error: Service at http://localhost:3005/health is not ready after 30 retries
    at waitForService (D:\novafuse-api-superstore\testing-environment\api-connection-testing\setup.js:85:9)
    at async Promise.all (index 0)
    at startAllServices (D:\novafuse-api-superstore\testing-environment\api-connection-testing\setup.js:131:5)
    at Object.&lt;anonymous&gt; (D:\novafuse-api-superstore\testing-environment\api-connection-testing\tests\authentication.test.js:88:5)</failure>
    </testcase>
    <testcase classname="Authentication Tests API Key Authentication should execute an endpoint with API Key authentication" name="Authentication Tests API Key Authentication should execute an endpoint with API Key authentication" time="0.001">
      <failure>Error: Service at http://localhost:3005/health is not ready after 30 retries
    at waitForService (D:\novafuse-api-superstore\testing-environment\api-connection-testing\setup.js:85:9)
    at async Promise.all (index 0)
    at startAllServices (D:\novafuse-api-superstore\testing-environment\api-connection-testing\setup.js:131:5)
    at Object.&lt;anonymous&gt; (D:\novafuse-api-superstore\testing-environment\api-connection-testing\tests\authentication.test.js:88:5)</failure>
    </testcase>
    <testcase classname="Authentication Tests API Key Authentication should fail with invalid API Key" name="Authentication Tests API Key Authentication should fail with invalid API Key" time="0">
      <failure>Error: Service at http://localhost:3005/health is not ready after 30 retries
    at waitForService (D:\novafuse-api-superstore\testing-environment\api-connection-testing\setup.js:85:9)
    at async Promise.all (index 0)
    at startAllServices (D:\novafuse-api-superstore\testing-environment\api-connection-testing\setup.js:131:5)
    at Object.&lt;anonymous&gt; (D:\novafuse-api-superstore\testing-environment\api-connection-testing\tests\authentication.test.js:88:5)</failure>
    </testcase>
    <testcase classname="Authentication Tests Basic Authentication should register the Basic Auth connector" name="Authentication Tests Basic Authentication should register the Basic Auth connector" time="0.001">
      <failure>Error: Service at http://localhost:3005/health is not ready after 30 retries
    at waitForService (D:\novafuse-api-superstore\testing-environment\api-connection-testing\setup.js:85:9)
    at async Promise.all (index 0)
    at startAllServices (D:\novafuse-api-superstore\testing-environment\api-connection-testing\setup.js:131:5)
    at Object.&lt;anonymous&gt; (D:\novafuse-api-superstore\testing-environment\api-connection-testing\tests\authentication.test.js:88:5)</failure>
    </testcase>
    <testcase classname="Authentication Tests Basic Authentication should store Basic Auth credentials" name="Authentication Tests Basic Authentication should store Basic Auth credentials" time="0">
      <failure>Error: Service at http://localhost:3005/health is not ready after 30 retries
    at waitForService (D:\novafuse-api-superstore\testing-environment\api-connection-testing\setup.js:85:9)
    at async Promise.all (index 0)
    at startAllServices (D:\novafuse-api-superstore\testing-environment\api-connection-testing\setup.js:131:5)
    at Object.&lt;anonymous&gt; (D:\novafuse-api-superstore\testing-environment\api-connection-testing\tests\authentication.test.js:88:5)</failure>
    </testcase>
    <testcase classname="Authentication Tests Basic Authentication should execute an endpoint with Basic authentication" name="Authentication Tests Basic Authentication should execute an endpoint with Basic authentication" time="0">
      <failure>Error: Service at http://localhost:3005/health is not ready after 30 retries
    at waitForService (D:\novafuse-api-superstore\testing-environment\api-connection-testing\setup.js:85:9)
    at async Promise.all (index 0)
    at startAllServices (D:\novafuse-api-superstore\testing-environment\api-connection-testing\setup.js:131:5)
    at Object.&lt;anonymous&gt; (D:\novafuse-api-superstore\testing-environment\api-connection-testing\tests\authentication.test.js:88:5)</failure>
    </testcase>
    <testcase classname="Authentication Tests Basic Authentication should fail with invalid Basic Auth credentials" name="Authentication Tests Basic Authentication should fail with invalid Basic Auth credentials" time="0.001">
      <failure>Error: Service at http://localhost:3005/health is not ready after 30 retries
    at waitForService (D:\novafuse-api-superstore\testing-environment\api-connection-testing\setup.js:85:9)
    at async Promise.all (index 0)
    at startAllServices (D:\novafuse-api-superstore\testing-environment\api-connection-testing\setup.js:131:5)
    at Object.&lt;anonymous&gt; (D:\novafuse-api-superstore\testing-environment\api-connection-testing\tests\authentication.test.js:88:5)</failure>
    </testcase>
    <testcase classname="Authentication Tests OAuth2 Authentication should register the OAuth2 connector" name="Authentication Tests OAuth2 Authentication should register the OAuth2 connector" time="0">
      <failure>Error: Service at http://localhost:3005/health is not ready after 30 retries
    at waitForService (D:\novafuse-api-superstore\testing-environment\api-connection-testing\setup.js:85:9)
    at async Promise.all (index 0)
    at startAllServices (D:\novafuse-api-superstore\testing-environment\api-connection-testing\setup.js:131:5)
    at Object.&lt;anonymous&gt; (D:\novafuse-api-superstore\testing-environment\api-connection-testing\tests\authentication.test.js:88:5)</failure>
    </testcase>
    <testcase classname="Authentication Tests OAuth2 Authentication should store OAuth2 credentials" name="Authentication Tests OAuth2 Authentication should store OAuth2 credentials" time="0">
      <failure>Error: Service at http://localhost:3005/health is not ready after 30 retries
    at waitForService (D:\novafuse-api-superstore\testing-environment\api-connection-testing\setup.js:85:9)
    at async Promise.all (index 0)
    at startAllServices (D:\novafuse-api-superstore\testing-environment\api-connection-testing\setup.js:131:5)
    at Object.&lt;anonymous&gt; (D:\novafuse-api-superstore\testing-environment\api-connection-testing\tests\authentication.test.js:88:5)</failure>
    </testcase>
    <testcase classname="Authentication Tests OAuth2 Authentication should execute an endpoint with OAuth2 authentication" name="Authentication Tests OAuth2 Authentication should execute an endpoint with OAuth2 authentication" time="0">
      <failure>Error: Service at http://localhost:3005/health is not ready after 30 retries
    at waitForService (D:\novafuse-api-superstore\testing-environment\api-connection-testing\setup.js:85:9)
    at async Promise.all (index 0)
    at startAllServices (D:\novafuse-api-superstore\testing-environment\api-connection-testing\setup.js:131:5)
    at Object.&lt;anonymous&gt; (D:\novafuse-api-superstore\testing-environment\api-connection-testing\tests\authentication.test.js:88:5)</failure>
    </testcase>
    <testcase classname="Authentication Tests OAuth2 Authentication should fail with invalid OAuth2 credentials" name="Authentication Tests OAuth2 Authentication should fail with invalid OAuth2 credentials" time="0">
      <failure>Error: Service at http://localhost:3005/health is not ready after 30 retries
    at waitForService (D:\novafuse-api-superstore\testing-environment\api-connection-testing\setup.js:85:9)
    at async Promise.all (index 0)
    at startAllServices (D:\novafuse-api-superstore\testing-environment\api-connection-testing\setup.js:131:5)
    at Object.&lt;anonymous&gt; (D:\novafuse-api-superstore\testing-environment\api-connection-testing\tests\authentication.test.js:88:5)</failure>
    </testcase>
    <testcase classname="Authentication Tests JWT Authentication should register the JWT connector" name="Authentication Tests JWT Authentication should register the JWT connector" time="0">
      <failure>Error: Service at http://localhost:3005/health is not ready after 30 retries
    at waitForService (D:\novafuse-api-superstore\testing-environment\api-connection-testing\setup.js:85:9)
    at async Promise.all (index 0)
    at startAllServices (D:\novafuse-api-superstore\testing-environment\api-connection-testing\setup.js:131:5)
    at Object.&lt;anonymous&gt; (D:\novafuse-api-superstore\testing-environment\api-connection-testing\tests\authentication.test.js:88:5)</failure>
    </testcase>
    <testcase classname="Authentication Tests JWT Authentication should store JWT credentials" name="Authentication Tests JWT Authentication should store JWT credentials" time="0.001">
      <failure>Error: Service at http://localhost:3005/health is not ready after 30 retries
    at waitForService (D:\novafuse-api-superstore\testing-environment\api-connection-testing\setup.js:85:9)
    at async Promise.all (index 0)
    at startAllServices (D:\novafuse-api-superstore\testing-environment\api-connection-testing\setup.js:131:5)
    at Object.&lt;anonymous&gt; (D:\novafuse-api-superstore\testing-environment\api-connection-testing\tests\authentication.test.js:88:5)</failure>
    </testcase>
    <testcase classname="Authentication Tests JWT Authentication should execute an endpoint with JWT authentication" name="Authentication Tests JWT Authentication should execute an endpoint with JWT authentication" time="0">
      <failure>Error: Service at http://localhost:3005/health is not ready after 30 retries
    at waitForService (D:\novafuse-api-superstore\testing-environment\api-connection-testing\setup.js:85:9)
    at async Promise.all (index 0)
    at startAllServices (D:\novafuse-api-superstore\testing-environment\api-connection-testing\setup.js:131:5)
    at Object.&lt;anonymous&gt; (D:\novafuse-api-superstore\testing-environment\api-connection-testing\tests\authentication.test.js:88:5)</failure>
    </testcase>
    <testcase classname="Authentication Tests JWT Authentication should fail with invalid JWT token" name="Authentication Tests JWT Authentication should fail with invalid JWT token" time="0.001">
      <failure>Error: Service at http://localhost:3005/health is not ready after 30 retries
    at waitForService (D:\novafuse-api-superstore\testing-environment\api-connection-testing\setup.js:85:9)
    at async Promise.all (index 0)
    at startAllServices (D:\novafuse-api-superstore\testing-environment\api-connection-testing\setup.js:131:5)
    at Object.&lt;anonymous&gt; (D:\novafuse-api-superstore\testing-environment\api-connection-testing\tests\authentication.test.js:88:5)</failure>
    </testcase>
    <testcase classname="Authentication Tests AWS SigV4 Authentication should register the AWS SigV4 connector" name="Authentication Tests AWS SigV4 Authentication should register the AWS SigV4 connector" time="0">
      <failure>Error: Service at http://localhost:3005/health is not ready after 30 retries
    at waitForService (D:\novafuse-api-superstore\testing-environment\api-connection-testing\setup.js:85:9)
    at async Promise.all (index 0)
    at startAllServices (D:\novafuse-api-superstore\testing-environment\api-connection-testing\setup.js:131:5)
    at Object.&lt;anonymous&gt; (D:\novafuse-api-superstore\testing-environment\api-connection-testing\tests\authentication.test.js:88:5)</failure>
    </testcase>
    <testcase classname="Authentication Tests AWS SigV4 Authentication should store AWS SigV4 credentials" name="Authentication Tests AWS SigV4 Authentication should store AWS SigV4 credentials" time="0">
      <failure>Error: Service at http://localhost:3005/health is not ready after 30 retries
    at waitForService (D:\novafuse-api-superstore\testing-environment\api-connection-testing\setup.js:85:9)
    at async Promise.all (index 0)
    at startAllServices (D:\novafuse-api-superstore\testing-environment\api-connection-testing\setup.js:131:5)
    at Object.&lt;anonymous&gt; (D:\novafuse-api-superstore\testing-environment\api-connection-testing\tests\authentication.test.js:88:5)</failure>
    </testcase>
    <testcase classname="Authentication Tests AWS SigV4 Authentication should execute an endpoint with AWS SigV4 authentication" name="Authentication Tests AWS SigV4 Authentication should execute an endpoint with AWS SigV4 authentication" time="0">
      <failure>Error: Service at http://localhost:3005/health is not ready after 30 retries
    at waitForService (D:\novafuse-api-superstore\testing-environment\api-connection-testing\setup.js:85:9)
    at async Promise.all (index 0)
    at startAllServices (D:\novafuse-api-superstore\testing-environment\api-connection-testing\setup.js:131:5)
    at Object.&lt;anonymous&gt; (D:\novafuse-api-superstore\testing-environment\api-connection-testing\tests\authentication.test.js:88:5)</failure>
    </testcase>
    <testcase classname="Authentication Tests Custom Authentication should register the Custom Auth connector" name="Authentication Tests Custom Authentication should register the Custom Auth connector" time="0">
      <failure>Error: Service at http://localhost:3005/health is not ready after 30 retries
    at waitForService (D:\novafuse-api-superstore\testing-environment\api-connection-testing\setup.js:85:9)
    at async Promise.all (index 0)
    at startAllServices (D:\novafuse-api-superstore\testing-environment\api-connection-testing\setup.js:131:5)
    at Object.&lt;anonymous&gt; (D:\novafuse-api-superstore\testing-environment\api-connection-testing\tests\authentication.test.js:88:5)</failure>
    </testcase>
    <testcase classname="Authentication Tests Custom Authentication should store Custom Auth credentials" name="Authentication Tests Custom Authentication should store Custom Auth credentials" time="0">
      <failure>Error: Service at http://localhost:3005/health is not ready after 30 retries
    at waitForService (D:\novafuse-api-superstore\testing-environment\api-connection-testing\setup.js:85:9)
    at async Promise.all (index 0)
    at startAllServices (D:\novafuse-api-superstore\testing-environment\api-connection-testing\setup.js:131:5)
    at Object.&lt;anonymous&gt; (D:\novafuse-api-superstore\testing-environment\api-connection-testing\tests\authentication.test.js:88:5)</failure>
    </testcase>
    <testcase classname="Authentication Tests Custom Authentication should execute an endpoint with Custom authentication" name="Authentication Tests Custom Authentication should execute an endpoint with Custom authentication" time="0">
      <failure>Error: Service at http://localhost:3005/health is not ready after 30 retries
    at waitForService (D:\novafuse-api-superstore\testing-environment\api-connection-testing\setup.js:85:9)
    at async Promise.all (index 0)
    at startAllServices (D:\novafuse-api-superstore\testing-environment\api-connection-testing\setup.js:131:5)
    at Object.&lt;anonymous&gt; (D:\novafuse-api-superstore\testing-environment\api-connection-testing\tests\authentication.test.js:88:5)</failure>
    </testcase>
    <testcase classname="Authentication Tests Custom Authentication should fail with invalid Custom Auth value" name="Authentication Tests Custom Authentication should fail with invalid Custom Auth value" time="0">
      <failure>Error: Service at http://localhost:3005/health is not ready after 30 retries
    at waitForService (D:\novafuse-api-superstore\testing-environment\api-connection-testing\setup.js:85:9)
    at async Promise.all (index 0)
    at startAllServices (D:\novafuse-api-superstore\testing-environment\api-connection-testing\setup.js:131:5)
    at Object.&lt;anonymous&gt; (D:\novafuse-api-superstore\testing-environment\api-connection-testing\tests\authentication.test.js:88:5)</failure>
    </testcase>
    <testcase classname="Authentication Tests Request Headers Verification should verify that the correct authentication headers were sent" name="Authentication Tests Request Headers Verification should verify that the correct authentication headers were sent" time="0">
      <failure>Error: Service at http://localhost:3005/health is not ready after 30 retries
    at waitForService (D:\novafuse-api-superstore\testing-environment\api-connection-testing\setup.js:85:9)
    at async Promise.all (index 0)
    at startAllServices (D:\novafuse-api-superstore\testing-environment\api-connection-testing\setup.js:131:5)
    at Object.&lt;anonymous&gt; (D:\novafuse-api-superstore\testing-environment\api-connection-testing\tests\authentication.test.js:88:5)</failure>
    </testcase>
  </testsuite>
</testsuites>