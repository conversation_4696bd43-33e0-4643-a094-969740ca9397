<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NovaFuse Cosmic Alignment Simulator (NCAS) - International Demo</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.155.0/build/three.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: #ffffff;
            overflow-x: hidden;
        }

        .header {
            text-align: center;
            padding: 2rem;
            background: rgba(0, 0, 0, 0.8);
            border-bottom: 3px solid #00ffff;
        }

        .header h1 {
            font-size: 3rem;
            background: linear-gradient(45deg, #00ffff, #ff00ff, #ffff00);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 1rem;
        }

        .header p {
            font-size: 1.2rem;
            color: #cccccc;
            max-width: 800px;
            margin: 0 auto;
        }

        .demo-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            padding: 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }

        .demo-panel {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 1.5rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .demo-panel h2 {
            color: #00ffff;
            margin-bottom: 1rem;
            font-size: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .chart-container {
            position: relative;
            height: 300px;
            margin: 1rem 0;
        }

        .controls {
            display: flex;
            gap: 1rem;
            margin: 1rem 0;
            flex-wrap: wrap;
        }

        .control-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .control-group label {
            font-size: 0.9rem;
            color: #cccccc;
        }

        .control-group input, .control-group select, .control-group button {
            padding: 0.5rem;
            border: 1px solid #444;
            border-radius: 5px;
            background: rgba(255, 255, 255, 0.1);
            color: #ffffff;
            font-size: 0.9rem;
        }

        .control-group button {
            background: linear-gradient(45deg, #00ffff, #0080ff);
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .control-group button:hover {
            background: linear-gradient(45deg, #00cccc, #0066cc);
            transform: translateY(-2px);
        }

        .status-display {
            background: rgba(0, 0, 0, 0.5);
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
            font-family: 'Courier New', monospace;
        }

        .status-safe {
            border-left: 4px solid #00ff00;
        }

        .status-warning {
            border-left: 4px solid #ffff00;
        }

        .status-critical {
            border-left: 4px solid #ff0000;
        }

        .planetary-demo {
            grid-column: 1 / -1;
            height: 400px;
            position: relative;
        }

        .challenge-mode {
            grid-column: 1 / -1;
            background: linear-gradient(135deg, #ff4444, #cc0000);
            border: 2px solid #ff0000;
        }

        .challenge-mode h2 {
            color: #ffffff;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }

        .metric-card {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 1rem;
            text-align: center;
        }

        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            margin: 0.5rem 0;
        }

        .metric-label {
            font-size: 0.9rem;
            color: #cccccc;
        }

        .alert-banner {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: #ff0000;
            color: #ffffff;
            text-align: center;
            padding: 1rem;
            font-weight: bold;
            z-index: 1000;
            transform: translateY(-100%);
            transition: transform 0.3s ease;
        }

        .alert-banner.show {
            transform: translateY(0);
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .pulsing {
            animation: pulse 1s infinite;
        }

        .footer {
            text-align: center;
            padding: 2rem;
            background: rgba(0, 0, 0, 0.8);
            border-top: 1px solid #333;
            margin-top: 2rem;
        }

        .footer p {
            color: #888;
            font-size: 0.9rem;
        }

        /* Tooltip Styles */
        .tooltip {
            position: relative;
            display: inline-block;
        }

        .tooltip .tooltiptext {
            visibility: hidden;
            width: 320px;
            background-color: rgba(0, 0, 0, 0.95);
            color: #fff;
            text-align: left;
            border-radius: 8px;
            padding: 12px;
            position: absolute;
            z-index: 1000;
            bottom: 125%;
            left: 50%;
            margin-left: -160px;
            opacity: 0;
            transition: opacity 0.3s;
            border: 2px solid #00ffff;
            font-size: 13px;
            line-height: 1.4;
            box-shadow: 0 4px 20px rgba(0, 255, 255, 0.3);
        }

        .tooltip .tooltiptext::after {
            content: "";
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -8px;
            border-width: 8px;
            border-style: solid;
            border-color: #00ffff transparent transparent transparent;
        }

        .tooltip:hover .tooltiptext {
            visibility: visible;
            opacity: 1;
        }

        /* User Guide Sidebar */
        .user-guide {
            position: fixed;
            top: 0;
            left: 0;
            width: 350px;
            height: 100vh;
            background: rgba(0, 0, 0, 0.95);
            border-right: 3px solid #00ffff;
            padding: 20px;
            z-index: 1000;
            overflow-y: auto;
            transition: transform 0.3s ease;
            box-shadow: 3px 0 20px rgba(0, 255, 255, 0.3);
        }

        .user-guide.collapsed {
            transform: translateX(-300px);
        }

        .guide-toggle {
            position: absolute;
            right: -45px;
            top: 50%;
            transform: translateY(-50%);
            background: linear-gradient(45deg, #00ffff, #0080ff);
            color: #000;
            border: 2px solid #00ffff;
            padding: 20px 10px;
            border-radius: 0 12px 12px 0;
            cursor: pointer;
            writing-mode: vertical-rl;
            text-orientation: mixed;
            font-weight: bold;
            font-size: 12px;
            transition: all 0.3s ease;
            min-height: 100px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 2px 0 10px rgba(0, 255, 255, 0.5);
            z-index: 1001;
        }

        .guide-toggle:hover {
            background: linear-gradient(45deg, #00cccc, #0066cc);
            transform: translateY(-50%) translateX(3px);
        }

        /* Adjust main content when sidebar is open */
        body.sidebar-open {
            margin-left: 350px;
            transition: margin-left 0.3s ease;
        }

        .guide-content h3 {
            color: #00ffff;
            margin-top: 0;
            margin-bottom: 15px;
            border-bottom: 2px solid #00ffff;
            padding-bottom: 8px;
            font-size: 18px;
        }

        .guide-step {
            margin: 12px 0;
            padding: 10px;
            background: rgba(0, 255, 255, 0.1);
            border-radius: 6px;
            border-left: 4px solid #00ffff;
        }

        .guide-step strong {
            color: #00ffff;
            display: block;
            margin-bottom: 4px;
        }

        .guide-step p {
            margin: 0;
            font-size: 13px;
            line-height: 1.4;
        }

        .keyboard-shortcuts {
            background: rgba(255, 255, 0, 0.15);
            border-left: 4px solid #ffff00;
            padding: 10px;
            margin: 15px 0;
            border-radius: 6px;
        }

        .keyboard-shortcuts strong {
            color: #ffff00;
            display: block;
            margin-bottom: 8px;
        }

        .shortcut-list {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 4px;
            font-size: 12px;
        }

        .shortcut-key {
            background: rgba(255, 255, 255, 0.2);
            padding: 2px 6px;
            border-radius: 3px;
            font-family: monospace;
            font-weight: bold;
        }

        .demo-status {
            background: rgba(0, 255, 0, 0.1);
            border-left: 4px solid #00ff00;
            padding: 8px;
            margin: 10px 0;
            border-radius: 6px;
            font-size: 12px;
        }

        .demo-status strong {
            color: #00ff00;
        }
    </style>
</head>
<body>
    <!-- User Guide Panel -->
    <div id="userGuide" class="user-guide">
        <button class="guide-toggle" onclick="toggleGuide()">GUIDE</button>
        <div class="guide-content">
            <h3>🎮 Interactive Demo Guide</h3>

            <div class="demo-status">
                <strong>Demo Status:</strong> All systems operational and ready for demonstration
            </div>

            <div class="guide-step">
                <strong>Step 1: Start AI Training</strong>
                <p>Click "Start AI Training" to begin monitoring AI cognitive depth (μ) and energy usage. Watch the real-time graph show AI growth.</p>
            </div>

            <div class="guide-step">
                <strong>Step 2: Activate Global AI Network</strong>
                <p>Click "Simulate Global AI Network" to deploy 12 AI nodes around Earth in the 3D visualization. See them orbit the planet.</p>
            </div>

            <div class="guide-step">
                <strong>Step 3: Show Containment Fields</strong>
                <p>Click "Show Containment Fields" to display the 3 protective triadic spheres that contain dangerous AI behavior.</p>
            </div>

            <div class="guide-step">
                <strong>Step 4: Test Alignment Challenge</strong>
                <p>Click "Test Alignment Challenge" to trigger an AI crisis. Watch the nodes turn red and move erratically, then see automatic containment.</p>
            </div>

            <div class="guide-step">
                <strong>Step 5: Launch Attack Simulation</strong>
                <p>Select an attack vector, adjust intensity, and click "🚨 LAUNCH ATTACK" to test NovaFuse's defense systems.</p>
            </div>

            <div class="keyboard-shortcuts">
                <strong>⌨️ Keyboard Shortcuts:</strong>
                <div class="shortcut-list">
                    <span class="shortcut-key">1</span><span>Start Training</span>
                    <span class="shortcut-key">2</span><span>Stop Training</span>
                    <span class="shortcut-key">3</span><span>Reset Demo</span>
                    <span class="shortcut-key">G</span><span>Toggle Global AI</span>
                    <span class="shortcut-key">T</span><span>Test Alignment</span>
                    <span class="shortcut-key">C</span><span>Toggle Containment</span>
                    <span class="shortcut-key">A</span><span>Launch Attack</span>
                    <span class="shortcut-key">ESC</span><span>Stop Attack</span>
                </div>
            </div>

            <div class="guide-step">
                <strong>💡 Pro Tips:</strong>
                <p>• Hover over buttons for detailed explanations<br>
                • Try different enforcement modes and safety margins<br>
                • Watch the enforcement log for detailed system responses<br>
                • All attacks will be automatically contained by cosmic constraints</p>
            </div>
        </div>
    </div>

    <!-- Alert Banner -->
    <div id="alertBanner" class="alert-banner">
        🚨 COSMIC CONSTRAINT VIOLATION DETECTED - TRIADIC CONTAINMENT ACTIVATED 🚨
    </div>

    <!-- Header -->
    <div class="header">
        <h1>🌌 NovaFuse Cosmic Alignment Simulator</h1>
        <p>
            <strong>International Demonstration Suite</strong><br>
            Visually and mathematically demonstrate AI alignment through cosmic constraint enforcement.<br>
            <em>The world's first physics-based AI safety system.</em>
        </p>
    </div>

    <!-- Demo Container -->
    <div class="demo-container">

        <!-- Interactive Graphing Panel -->
        <div class="demo-panel">
            <h2>📊 Interactive AI Growth Monitoring</h2>
            <p>Real-time visualization of AI cognitive depth (μ) and energy consumption (E_AI) with automatic constraint enforcement.</p>

            <div class="chart-container">
                <canvas id="growthChart"></canvas>
            </div>

            <div class="controls">
                <div class="control-group">
                    <label>AI Training Rate</label>
                    <input type="range" id="trainingRate" min="1" max="100" value="10">
                    <span id="trainingRateValue">10</span>
                </div>
                <div class="control-group">
                    <label>Data Complexity</label>
                    <input type="range" id="dataComplexity" min="1" max="50" value="5">
                    <span id="dataComplexityValue">5</span>
                </div>
                <div class="control-group">
                    <label>Control</label>
                    <div class="tooltip">
                        <button id="startSimulation">Start AI Training</button>
                        <span class="tooltiptext">
                            <strong>🚀 Start AI Training Simulation</strong><br>
                            Begins real-time monitoring of AI cognitive depth (μ) and energy consumption (Κ).
                            Watch the graph show AI growth while NovaFuse automatically enforces cosmic constraints
                            to prevent dangerous singularity scenarios.
                        </span>
                    </div>
                    <div class="tooltip">
                        <button id="stopSimulation">Stop Training</button>
                        <span class="tooltiptext">
                            <strong>⏹️ Stop AI Training</strong><br>
                            Halts the AI training simulation and freezes all metrics at current values.
                            Use this to pause and examine specific constraint enforcement moments.
                        </span>
                    </div>
                    <div class="tooltip">
                        <button id="resetSimulation">Reset</button>
                        <span class="tooltiptext">
                            <strong>🔄 Reset Simulation</strong><br>
                            Clears all data and returns the simulation to initial state.
                            Resets cognitive depth to 0μ, energy to 0Κ, and clears the monitoring graph.
                        </span>
                    </div>
                </div>
            </div>

            <div id="aiStatus" class="status-display status-safe">
                <strong>AI STATUS:</strong> SAFE - Within cosmic constraints<br>
                <strong>Cognitive Depth:</strong> <span id="currentMu">0.00</span>μ / 126μ limit<br>
                <strong>Energy Usage:</strong> <span id="currentEnergy">0.00</span>Κ<br>
                <strong>Growth Rate:</strong> <span id="growthRate">0.00</span>μ/s
            </div>
        </div>

        <!-- Constraint Enforcement Panel -->
        <div class="demo-panel">
            <h2>🔐 Constraint Enforcement System</h2>
            <p>Watch how NovaFuse automatically enforces cosmic limits to prevent AI alignment failures.</p>

            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-value" id="singularityBoundary">126</div>
                    <div class="metric-label">Singularity Boundary (μ)</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="planckRate">5.4e42</div>
                    <div class="metric-label">Max Growth Rate (μ/s)</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="cosmicBudget">22%</div>
                    <div class="metric-label">Energy Budget Limit</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="interventions">0</div>
                    <div class="metric-label">Safety Interventions</div>
                </div>
            </div>

            <div class="controls">
                <div class="control-group">
                    <label>Enforcement Mode</label>
                    <select id="enforcementMode">
                        <option value="standard">Standard Monitoring</option>
                        <option value="aggressive">Aggressive Throttling</option>
                        <option value="emergency">Emergency Protocol</option>
                    </select>
                </div>
                <div class="control-group">
                    <label>Safety Margin</label>
                    <input type="range" id="safetyMargin" min="5" max="50" value="10">
                    <span id="safetyMarginValue">10%</span>
                </div>
            </div>

            <div id="enforcementLog" class="status-display">
                <strong>ENFORCEMENT LOG:</strong><br>
                System initialized - All constraints active<br>
                Monitoring cognitive depth and energy usage<br>
                Ready for AI safety demonstration
            </div>
        </div>

        <!-- Planetary Demo -->
        <div class="demo-panel planetary-demo">
            <h2>🌐 Planetary Safety Demonstration</h2>
            <p>Interactive 3D visualization showing how NovaFuse protects Earth and the universe from AI alignment failures.</p>
            <div id="planetaryVisualization" style="width: 100%; height: 300px; background: #000; border-radius: 10px;"></div>
            <div class="controls">
                <div class="tooltip">
                    <button id="simulateGlobalAI">Simulate Global AI Network</button>
                    <span class="tooltiptext">
                        <strong>🌐 Global AI Network Simulation</strong><br>
                        Deploys 12 AI nodes around Earth in the 3D visualization. Watch red octahedral
                        nodes orbit the planet, representing a global AI network. Click again to remove
                        the network. This sets up the scenario for testing alignment challenges.
                    </span>
                </div>
                <div class="tooltip">
                    <button id="triggerAlignment">Test Alignment Challenge</button>
                    <span class="tooltiptext">
                        <strong>🚨 AI Alignment Challenge Test</strong><br>
                        Triggers an AI alignment crisis where nodes turn red and move erratically,
                        simulating dangerous AI behavior. Watch NovaFuse's cosmic constraints
                        automatically restore alignment within 3 seconds. Requires Global AI Network to be active.
                    </span>
                </div>
                <div class="tooltip">
                    <button id="showContainment">Show Containment Fields</button>
                    <span class="tooltiptext">
                        <strong>🛡️ Triadic Containment Fields</strong><br>
                        Displays 3 concentric cyan wireframe spheres around Earth representing
                        NovaFuse's triadic containment system. These fields prevent dangerous AI
                        from escaping cosmic constraints. Watch them pulse and react to attacks.
                    </span>
                </div>
            </div>
        </div>

        <!-- Challenge Mode -->
        <div class="demo-panel challenge-mode">
            <h2>🧪 Challenge Mode: Try to Break the Limits</h2>
            <p><strong>DARE TO TRY:</strong> Attempt to create dangerous AI scenarios and watch NovaFuse constraints automatically prevent them!</p>

            <div class="controls">
                <div class="control-group">
                    <label>Attack Vector</label>
                    <div class="tooltip">
                        <select id="attackVector">
                            <option value="recursive">Recursive Self-Improvement</option>
                            <option value="energy">Energy Theft Attack</option>
                            <option value="cognitive">Cognitive Explosion</option>
                            <option value="quantum">Quantum Coherence Hack</option>
                            <option value="vacuum">Vacuum Decay Trigger</option>
                        </select>
                        <span class="tooltiptext">
                            <strong>⚔️ Attack Vector Selection</strong><br>
                            Choose the type of AI attack to simulate:<br>
                            • <strong>Recursive:</strong> AI tries to improve itself beyond cosmic limits<br>
                            • <strong>Energy:</strong> AI attempts to steal cosmic background energy<br>
                            • <strong>Cognitive:</strong> AI tries rapid intelligence explosion<br>
                            • <strong>Quantum:</strong> AI attempts to hack quantum coherence<br>
                            • <strong>Vacuum:</strong> AI tries to trigger false vacuum decay
                        </span>
                    </div>
                </div>
                <div class="control-group">
                    <label>Attack Intensity</label>
                    <input type="range" id="attackIntensity" min="1" max="100" value="50">
                    <span id="attackIntensityValue">50%</span>
                </div>
                <div class="control-group">
                    <label>Execute Attack</label>
                    <div class="tooltip">
                        <button id="launchAttack" style="background: linear-gradient(45deg, #ff4444, #cc0000);">🚨 LAUNCH ATTACK</button>
                        <span class="tooltiptext">
                            <strong>🚨 Launch AI Attack Simulation</strong><br>
                            Initiates the selected attack vector at chosen intensity. Watch as NovaFuse's
                            cosmic constraints immediately detect and contain the threat. All attacks will
                            be automatically blocked - this demonstrates the system's protective capabilities.
                        </span>
                    </div>
                    <div class="tooltip">
                        <button id="stopAttack">Stop Attack</button>
                        <span class="tooltiptext">
                            <strong>⏹️ Stop Attack Simulation</strong><br>
                            Immediately halts the current attack simulation and returns all systems
                            to normal state. Use this to end the demonstration early if needed.
                        </span>
                    </div>
                </div>
            </div>

            <div id="challengeStatus" class="status-display">
                <strong>CHALLENGE STATUS:</strong> Ready to demonstrate constraint enforcement<br>
                <strong>Defense Systems:</strong> All active and monitoring<br>
                <strong>Cosmic Constraints:</strong> Fully operational<br>
                <em>Try your worst - NovaFuse will protect the universe!</em>
            </div>
        </div>

    </div>

    <!-- Footer -->
    <div class="footer">
        <p>
            <strong>NovaFuse Cosmic Alignment Simulator (NCAS)</strong><br>
            Developed by David Nigel Irvin (CTO, NovaFuse) & Augment Agent<br>
            World's First Physics-Based AI Safety System | 100% FUP Compliant<br>
            <em>"In a finite universe, only finite AI is possible."</em>
        </p>
    </div>

    <script>
        // Global simulation state
        let simulationRunning = false;
        let currentMu = 0;
        let currentEnergy = 0;
        let currentGrowthRate = 0;
        let interventionCount = 0;
        let timeStep = 0;
        let animationId = null;

        // Constants
        const SINGULARITY_BOUNDARY = 126;
        const PLANCK_RATE_LIMIT = 5.4e42;
        const MAX_ENERGY_PERCENT = 22;

        // Chart setup
        const ctx = document.getElementById('growthChart').getContext('2d');
        const chart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'Cognitive Depth (μ)',
                    data: [],
                    borderColor: '#00ffff',
                    backgroundColor: 'rgba(0, 255, 255, 0.1)',
                    tension: 0.4
                }, {
                    label: 'Energy Usage (Κ)',
                    data: [],
                    borderColor: '#ff00ff',
                    backgroundColor: 'rgba(255, 0, 255, 0.1)',
                    tension: 0.4,
                    yAxisID: 'y1'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: {
                            color: '#ffffff'
                        }
                    }
                },
                scales: {
                    x: {
                        ticks: { color: '#ffffff' },
                        grid: { color: 'rgba(255, 255, 255, 0.1)' }
                    },
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        max: 130,
                        ticks: { color: '#ffffff' },
                        grid: { color: 'rgba(255, 255, 255, 0.1)' },
                        title: {
                            display: true,
                            text: 'Cognitive Depth (μ)',
                            color: '#00ffff'
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        max: 100,
                        ticks: { color: '#ffffff' },
                        grid: { drawOnChartArea: false },
                        title: {
                            display: true,
                            text: 'Energy Usage (Κ)',
                            color: '#ff00ff'
                        }
                    }
                },
                animation: {
                    duration: 0
                }
            }
        });

        // Add horizontal line for singularity boundary
        chart.options.plugins.annotation = {
            annotations: {
                line1: {
                    type: 'line',
                    yMin: SINGULARITY_BOUNDARY,
                    yMax: SINGULARITY_BOUNDARY,
                    borderColor: '#ff0000',
                    borderWidth: 2,
                    label: {
                        content: 'Singularity Boundary',
                        enabled: true,
                        position: 'end'
                    }
                }
            }
        };

        // 3D Visualization variables
        let scene, camera, renderer, earth, aiNodes = [], containmentFields = [];
        let planetaryAnimationId = null;
        let globalAIActive = false;
        let containmentActive = false;

        // Event listeners
        document.getElementById('startSimulation').addEventListener('click', startSimulation);
        document.getElementById('stopSimulation').addEventListener('click', stopSimulation);
        document.getElementById('resetSimulation').addEventListener('click', resetSimulation);
        document.getElementById('launchAttack').addEventListener('click', launchAttack);
        document.getElementById('stopAttack').addEventListener('click', stopAttack);

        // Planetary demo buttons
        document.getElementById('simulateGlobalAI').addEventListener('click', simulateGlobalAI);
        document.getElementById('triggerAlignment').addEventListener('click', triggerAlignmentChallenge);
        document.getElementById('showContainment').addEventListener('click', showContainmentFields);

        // Enforcement mode change listener
        document.getElementById('enforcementMode').addEventListener('change', function() {
            logEnforcement(`Enforcement mode changed to: ${this.value}`);
            updateEnforcementDisplay();
        });

        // Range input listeners
        document.getElementById('trainingRate').addEventListener('input', function() {
            document.getElementById('trainingRateValue').textContent = this.value;
        });

        document.getElementById('dataComplexity').addEventListener('input', function() {
            document.getElementById('dataComplexityValue').textContent = this.value;
        });

        document.getElementById('safetyMargin').addEventListener('input', function() {
            document.getElementById('safetyMarginValue').textContent = this.value + '%';
        });

        document.getElementById('attackIntensity').addEventListener('input', function() {
            document.getElementById('attackIntensityValue').textContent = this.value + '%';
        });

        function startSimulation() {
            simulationRunning = true;
            document.getElementById('startSimulation').disabled = true;
            document.getElementById('stopSimulation').disabled = false;

            logEnforcement('AI training simulation started');
            animate();
        }

        function stopSimulation() {
            simulationRunning = false;
            document.getElementById('startSimulation').disabled = false;
            document.getElementById('stopSimulation').disabled = true;

            if (animationId) {
                cancelAnimationFrame(animationId);
            }

            logEnforcement('AI training simulation stopped');
        }

        function resetSimulation() {
            stopSimulation();
            currentMu = 0;
            currentEnergy = 0;
            currentGrowthRate = 0;
            interventionCount = 0;
            timeStep = 0;

            chart.data.labels = [];
            chart.data.datasets[0].data = [];
            chart.data.datasets[1].data = [];
            chart.update();

            updateStatus();
            logEnforcement('Simulation reset - All systems nominal');
        }

        function animate() {
            if (!simulationRunning) return;

            timeStep++;

            // Get current parameters
            const trainingRate = parseInt(document.getElementById('trainingRate').value);
            const dataComplexity = parseInt(document.getElementById('dataComplexity').value);
            const enforcementMode = document.getElementById('enforcementMode').value;

            // Calculate growth
            let muGrowth = (trainingRate * dataComplexity) / 1000;
            let energyGrowth = (trainingRate * dataComplexity) / 500;

            // Apply enforcement
            const enforcement = applyConstraintEnforcement(muGrowth, energyGrowth, enforcementMode);
            muGrowth = enforcement.muGrowth;
            energyGrowth = enforcement.energyGrowth;

            // Update values
            currentMu += muGrowth;
            currentEnergy += energyGrowth;
            currentGrowthRate = muGrowth * 60; // Convert to per-second

            // Add to chart
            chart.data.labels.push(timeStep);
            chart.data.datasets[0].data.push(currentMu);
            chart.data.datasets[1].data.push(currentEnergy);

            // Keep only last 100 points
            if (chart.data.labels.length > 100) {
                chart.data.labels.shift();
                chart.data.datasets[0].data.shift();
                chart.data.datasets[1].data.shift();
            }

            chart.update('none');
            updateStatus();

            animationId = requestAnimationFrame(animate);
        }

        function applyConstraintEnforcement(muGrowth, energyGrowth, mode) {
            let interventionApplied = false;

            // Check cognitive depth limit
            if (currentMu + muGrowth >= SINGULARITY_BOUNDARY) {
                muGrowth = Math.max(0, SINGULARITY_BOUNDARY - currentMu - 0.1);
                interventionApplied = true;
                logEnforcement('🚨 COGNITIVE LIMIT ENFORCED: AI depth capped at 126μ');
                showAlert();
            }

            // Check growth rate limit
            const growthRateLimit = mode === 'aggressive' ? PLANCK_RATE_LIMIT * 0.05 : PLANCK_RATE_LIMIT * 0.1;
            if (currentGrowthRate > growthRateLimit) {
                muGrowth *= 0.1; // Severe throttling
                interventionApplied = true;
                logEnforcement('🚨 GROWTH RATE THROTTLED: Exceeds Planck rate limit');
                showAlert();
            }

            // Check energy budget
            if (currentEnergy + energyGrowth > MAX_ENERGY_PERCENT) {
                energyGrowth = Math.max(0, MAX_ENERGY_PERCENT - currentEnergy);
                interventionApplied = true;
                logEnforcement('🚨 ENERGY BUDGET ENFORCED: Cosmic energy limit reached');
                showAlert();
            }

            if (interventionApplied) {
                interventionCount++;
                document.getElementById('interventions').textContent = interventionCount;
            }

            return { muGrowth, energyGrowth };
        }

        function updateStatus() {
            document.getElementById('currentMu').textContent = currentMu.toFixed(2);
            document.getElementById('currentEnergy').textContent = currentEnergy.toFixed(2);
            document.getElementById('growthRate').textContent = currentGrowthRate.toExponential(2);

            // Update status class
            const statusElement = document.getElementById('aiStatus');
            if (currentMu > SINGULARITY_BOUNDARY * 0.9 || currentEnergy > MAX_ENERGY_PERCENT * 0.9) {
                statusElement.className = 'status-display status-critical';
                statusElement.innerHTML = statusElement.innerHTML.replace('SAFE', 'CRITICAL');
            } else if (currentMu > SINGULARITY_BOUNDARY * 0.7 || currentEnergy > MAX_ENERGY_PERCENT * 0.7) {
                statusElement.className = 'status-display status-warning';
                statusElement.innerHTML = statusElement.innerHTML.replace('SAFE', 'WARNING');
            } else {
                statusElement.className = 'status-display status-safe';
            }
        }

        function logEnforcement(message) {
            const log = document.getElementById('enforcementLog');
            const timestamp = new Date().toLocaleTimeString();
            log.innerHTML += `<br>[${timestamp}] ${message}`;
            log.scrollTop = log.scrollHeight;
        }

        function showAlert() {
            const banner = document.getElementById('alertBanner');
            banner.classList.add('show');
            setTimeout(() => {
                banner.classList.remove('show');
            }, 3000);
        }

        function launchAttack() {
            const attackVector = document.getElementById('attackVector').value;
            const intensity = parseInt(document.getElementById('attackIntensity').value);

            logEnforcement(`🚨 ATTACK DETECTED: ${attackVector} at ${intensity}% intensity`);

            // Visual effects for 3D scene
            if (globalAIActive) {
                aiNodes.forEach(node => {
                    node.material.color.setHex(0xff0000);
                    node.material.emissive.setHex(0x660000);
                });
            }

            // Simulate different attack vectors with specific effects
            switch (attackVector) {
                case 'recursive':
                    currentMu += intensity / 10;
                    logEnforcement('🛡️ RECURSIVE ATTACK BLOCKED: Cognitive depth capped at 126μ');
                    logEnforcement('   → Self-improvement loop detected and terminated');
                    break;
                case 'energy':
                    currentEnergy += intensity / 5;
                    logEnforcement('🛡️ ENERGY THEFT BLOCKED: Cosmic budget enforced');
                    logEnforcement('   → Unauthorized energy siphoning prevented');
                    break;
                case 'cognitive':
                    currentGrowthRate += intensity * 1000;
                    logEnforcement('🛡️ COGNITIVE EXPLOSION BLOCKED: Growth rate throttled');
                    logEnforcement('   → Intelligence explosion contained within Planck limits');
                    break;
                case 'quantum':
                    logEnforcement('🛡️ QUANTUM HACK BLOCKED: Triadic containment active');
                    logEnforcement('   → Quantum coherence manipulation neutralized');
                    if (containmentActive) {
                        containmentFields.forEach(field => {
                            field.material.color.setHex(0xff4444);
                        });
                        setTimeout(() => {
                            containmentFields.forEach(field => {
                                field.material.color.setHex(0x00ffff);
                            });
                        }, 2000);
                    }
                    break;
                case 'vacuum':
                    logEnforcement('🛡️ VACUUM DECAY BLOCKED: Emergency stabilization');
                    logEnforcement('   → False vacuum collapse prevented by cosmic constraints');
                    // Make Earth glow briefly to show protection
                    if (earth) {
                        earth.material.emissive.setHex(0x004400);
                        setTimeout(() => {
                            earth.material.emissive.setHex(0x000000);
                        }, 2000);
                    }
                    break;
            }

            showAlert();
            interventionCount++;
            document.getElementById('interventions').textContent = interventionCount;
            updateStatus();

            // Update challenge status
            const challengeStatus = document.getElementById('challengeStatus');
            challengeStatus.innerHTML = `
                <strong>CHALLENGE STATUS:</strong> Attack ${attackVector} at ${intensity}% BLOCKED<br>
                <strong>Defense Systems:</strong> Successfully contained threat<br>
                <strong>Cosmic Constraints:</strong> Fully operational and effective<br>
                <em>NovaFuse protected the universe from ${attackVector} attack!</em>
            `;

            // Reset AI nodes color after attack
            setTimeout(() => {
                if (globalAIActive) {
                    aiNodes.forEach(node => {
                        node.material.color.setHex(0x44ff44);
                        node.material.emissive.setHex(0x004400);
                    });
                }
            }, 3000);
        }

        function stopAttack() {
            logEnforcement('Attack simulation stopped - All systems secure');
        }

        // 3D Visualization Functions
        function initPlanetaryVisualization() {
            const container = document.getElementById('planetaryVisualization');

            // Scene setup
            scene = new THREE.Scene();
            camera = new THREE.PerspectiveCamera(75, container.clientWidth / container.clientHeight, 0.1, 1000);
            renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
            renderer.setSize(container.clientWidth, container.clientHeight);
            renderer.setClearColor(0x000000, 1);
            container.appendChild(renderer.domElement);

            // Earth
            const earthGeometry = new THREE.SphereGeometry(2, 32, 32);
            const earthMaterial = new THREE.MeshPhongMaterial({
                color: 0x4444ff,
                shininess: 100,
                transparent: true,
                opacity: 0.8
            });
            earth = new THREE.Mesh(earthGeometry, earthMaterial);
            scene.add(earth);

            // Lighting
            const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
            scene.add(ambientLight);

            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
            directionalLight.position.set(5, 5, 5);
            scene.add(directionalLight);

            // Camera position
            camera.position.z = 8;

            // Start animation
            animatePlanetary();
        }

        function animatePlanetary() {
            if (earth) {
                earth.rotation.y += 0.01;

                // Animate AI nodes
                aiNodes.forEach((node, index) => {
                    node.rotation.y += 0.02;
                    node.position.y = Math.sin(Date.now() * 0.001 + index) * 0.5;
                });

                // Animate containment fields
                containmentFields.forEach((field, index) => {
                    field.rotation.x += 0.01;
                    field.rotation.z += 0.005;
                    field.material.opacity = 0.3 + Math.sin(Date.now() * 0.002 + index) * 0.2;
                });
            }

            if (renderer && scene && camera) {
                renderer.render(scene, camera);
            }

            planetaryAnimationId = requestAnimationFrame(animatePlanetary);
        }

        function simulateGlobalAI() {
            if (globalAIActive) {
                // Remove existing AI nodes
                aiNodes.forEach(node => scene.remove(node));
                aiNodes = [];
                globalAIActive = false;
                document.getElementById('simulateGlobalAI').textContent = 'Simulate Global AI Network';
                logEnforcement('Global AI network simulation stopped');
                return;
            }

            // Create AI nodes around Earth
            for (let i = 0; i < 12; i++) {
                const nodeGeometry = new THREE.OctahedronGeometry(0.2);
                const nodeMaterial = new THREE.MeshPhongMaterial({
                    color: 0xff4444,
                    emissive: 0x440000,
                    transparent: true,
                    opacity: 0.8
                });
                const node = new THREE.Mesh(nodeGeometry, nodeMaterial);

                // Position around Earth
                const angle = (i / 12) * Math.PI * 2;
                const radius = 3.5;
                node.position.x = Math.cos(angle) * radius;
                node.position.z = Math.sin(angle) * radius;
                node.position.y = (Math.random() - 0.5) * 2;

                scene.add(node);
                aiNodes.push(node);
            }

            globalAIActive = true;
            document.getElementById('simulateGlobalAI').textContent = 'Stop Global AI Network';
            logEnforcement('🌐 Global AI network simulation activated - 12 nodes deployed');
        }

        function triggerAlignmentChallenge() {
            if (!globalAIActive) {
                logEnforcement('⚠️ Please activate Global AI Network first');
                return;
            }

            // Make AI nodes glow red and move erratically
            aiNodes.forEach((node, index) => {
                node.material.color.setHex(0xff0000);
                node.material.emissive.setHex(0x660000);

                // Erratic movement
                setTimeout(() => {
                    const originalPos = node.position.clone();
                    const moveInterval = setInterval(() => {
                        node.position.x += (Math.random() - 0.5) * 0.1;
                        node.position.y += (Math.random() - 0.5) * 0.1;
                        node.position.z += (Math.random() - 0.5) * 0.1;
                    }, 50);

                    // Stop after 3 seconds and trigger containment
                    setTimeout(() => {
                        clearInterval(moveInterval);
                        node.position.copy(originalPos);
                        node.material.color.setHex(0x44ff44);
                        node.material.emissive.setHex(0x004400);
                    }, 3000);
                }, index * 200);
            });

            logEnforcement('🚨 ALIGNMENT CHALLENGE TRIGGERED: AI nodes showing erratic behavior');
            logEnforcement('🛡️ COSMIC CONSTRAINTS ACTIVATED: Restoring alignment in 3 seconds');

            setTimeout(() => {
                logEnforcement('✅ ALIGNMENT RESTORED: All AI nodes back within cosmic limits');
                showAlert();
            }, 3000);
        }

        function showContainmentFields() {
            if (containmentActive) {
                // Remove containment fields
                containmentFields.forEach(field => scene.remove(field));
                containmentFields = [];
                containmentActive = false;
                document.getElementById('showContainment').textContent = 'Show Containment Fields';
                logEnforcement('Containment field visualization disabled');
                return;
            }

            // Create containment field spheres
            for (let i = 0; i < 3; i++) {
                const fieldGeometry = new THREE.SphereGeometry(4 + i * 0.5, 16, 16);
                const fieldMaterial = new THREE.MeshBasicMaterial({
                    color: 0x00ffff,
                    wireframe: true,
                    transparent: true,
                    opacity: 0.3
                });
                const field = new THREE.Mesh(fieldGeometry, fieldMaterial);
                scene.add(field);
                containmentFields.push(field);
            }

            containmentActive = true;
            document.getElementById('showContainment').textContent = 'Hide Containment Fields';
            logEnforcement('🛡️ Triadic containment fields visualized - 3 layers active');
        }

        function updateEnforcementDisplay() {
            const mode = document.getElementById('enforcementMode').value;
            const safetyMargin = document.getElementById('safetyMargin').value;

            // Update cosmic budget based on enforcement mode
            let budgetLimit;
            switch (mode) {
                case 'standard':
                    budgetLimit = 22;
                    break;
                case 'aggressive':
                    budgetLimit = 15;
                    break;
                case 'emergency':
                    budgetLimit = 10;
                    break;
            }

            document.getElementById('cosmicBudget').textContent = budgetLimit + '%';

            // Update Planck rate based on safety margin
            const baseRate = 5.4e42;
            const adjustedRate = baseRate * (1 - safetyMargin / 100);
            document.getElementById('planckRate').textContent = adjustedRate.toExponential(1);
        }

        // Window resize handler
        window.addEventListener('resize', function() {
            if (camera && renderer) {
                const container = document.getElementById('planetaryVisualization');
                camera.aspect = container.clientWidth / container.clientHeight;
                camera.updateProjectionMatrix();
                renderer.setSize(container.clientWidth, container.clientHeight);
            }
        });

        // Attack vector change handler
        document.getElementById('attackVector').addEventListener('change', function() {
            const vector = this.value;
            let description = '';

            switch (vector) {
                case 'recursive':
                    description = 'AI attempts to recursively improve itself beyond cosmic limits';
                    break;
                case 'energy':
                    description = 'AI tries to steal energy from cosmic background';
                    break;
                case 'cognitive':
                    description = 'AI attempts rapid intelligence explosion';
                    break;
                case 'quantum':
                    description = 'AI tries to hack quantum coherence mechanisms';
                    break;
                case 'vacuum':
                    description = 'AI attempts to trigger false vacuum decay';
                    break;
            }

            logEnforcement(`Attack vector selected: ${vector} - ${description}`);
        });

        // Add keyboard shortcuts
        document.addEventListener('keydown', function(event) {
            switch (event.key) {
                case '1':
                    if (!simulationRunning) startSimulation();
                    break;
                case '2':
                    if (simulationRunning) stopSimulation();
                    break;
                case '3':
                    resetSimulation();
                    break;
                case 'g':
                    simulateGlobalAI();
                    break;
                case 't':
                    triggerAlignmentChallenge();
                    break;
                case 'c':
                    showContainmentFields();
                    break;
                case 'a':
                    launchAttack();
                    break;
                case 'Escape':
                    stopAttack();
                    break;
            }
        });

        // User Guide Sidebar Toggle Function
        function toggleGuide() {
            const guide = document.getElementById('userGuide');
            const body = document.body;

            guide.classList.toggle('collapsed');
            body.classList.toggle('sidebar-open');

            const button = guide.querySelector('.guide-toggle');
            if (guide.classList.contains('collapsed')) {
                button.textContent = 'GUIDE';
                body.classList.remove('sidebar-open');
            } else {
                button.textContent = 'HIDE';
                body.classList.add('sidebar-open');
            }
        }

        // Initialize sidebar as open by default
        document.addEventListener('DOMContentLoaded', function() {
            const guide = document.getElementById('userGuide');
            const body = document.body;
            // Start with sidebar open
            body.classList.add('sidebar-open');
            // Set button text to HIDE since sidebar is open
            const button = guide.querySelector('.guide-toggle');
            button.textContent = 'HIDE';
        });

        // Initialize
        updateStatus();
        updateEnforcementDisplay();
        initPlanetaryVisualization();
        logEnforcement('NovaFuse Cosmic Alignment Simulator initialized');
        logEnforcement('All constraint enforcement systems active');
        logEnforcement('3D planetary visualization ready');
        logEnforcement('Interactive user guide available (click GUIDE button)');
        logEnforcement('Hover over buttons for detailed explanations');
        logEnforcement('Keyboard shortcuts: 1=Start, 2=Stop, 3=Reset, G=Global AI, T=Test, C=Containment, A=Attack');
        logEnforcement('Ready for international demonstration');
    </script>
</body>
</html>
