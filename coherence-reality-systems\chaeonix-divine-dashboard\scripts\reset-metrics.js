#!/usr/bin/env node

/**
 * CHAEONIX Metrics Reset Script
 * Resets all profit tracking and performance metrics to $0
 */

const fetch = require('node-fetch');

const RESET_ENDPOINTS = [
  'http://localhost:3000/api/analytics/profit-tracker',
  'http://localhost:3001/api/analytics/profit-tracker', // Alternative port
];

async function resetMetrics() {
  console.log('💰 CHAEONIX METRICS RESET INITIATED');
  console.log('🔄 Resetting all profit tracking data to $0...');
  
  let resetSuccessful = false;
  
  for (const endpoint of RESET_ENDPOINTS) {
    try {
      console.log(`📡 Attempting reset via ${endpoint}`);
      
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'RESET_METRICS'
        })
      });

      if (response.ok) {
        const result = await response.json();
        console.log('✅ Reset successful!');
        console.log(`📊 Total trades: ${result.total_trades}`);
        console.log(`💵 Total profit: $${result.total_profit}`);
        console.log(`⏰ Reset timestamp: ${result.reset_timestamp}`);
        resetSuccessful = true;
        break;
      } else {
        console.log(`❌ Reset failed with status: ${response.status}`);
      }
    } catch (error) {
      console.log(`❌ Connection failed: ${error.message}`);
    }
  }
  
  if (!resetSuccessful) {
    console.log('❌ All reset attempts failed. Please ensure the CHAEONIX dashboard is running.');
    process.exit(1);
  }
  
  console.log('');
  console.log('🌟 CHAEONIX METRICS RESET COMPLETE');
  console.log('💰 All profit metrics set to $0.00');
  console.log('📈 All trade counts reset to 0');
  console.log('🎯 Win rates reset to 0.0%');
  console.log('');
  console.log('🚀 Ready for fresh trading session!');
}

// Run the reset
resetMetrics().catch(error => {
  console.error('💥 Reset script error:', error);
  process.exit(1);
});
