/**
 * Complete Finite Universe Principle Defense System Example
 *
 * This example demonstrates how to use the complete Finite Universe Principle
 * defense system with all components, including domain-specific engines,
 * cross-domain validation, monitoring dashboard, and NovaFuse integration.
 */

const {
  // Complete defense system
  createCompleteDefenseSystem,
  
  // Domain engines
  CSEDAdapter,
  CSFEAdapter,
  CSMEAdapter,
  
  // Cross-domain validation
  CrossDomainValidator,
  
  // Monitoring dashboard
  MonitoringDashboard
} = require('../src/quantum/finite-universe-principle');

/**
 * Example 1: Complete Defense System
 * 
 * This example demonstrates how to create and use the complete defense system.
 */
async function example1() {
  console.log('\n=== Example 1: Complete Defense System ===\n');

  // Create complete defense system
  const defenseSystem = createCompleteDefenseSystem({
    enableLogging: true,
    enableMonitoring: true,
    strictMode: true
  });

  // Create mock NovaFuse components
  const mockNovaComponents = {
    novaConnect: createMockNovaConnect(),
    novaVision: createMockNovaVision(),
    novaDNA: createMockNovaDNA(),
    novaShield: createMockNovaShield()
  };

  // Register NovaFuse components
  defenseSystem.registerNovaComponents(mockNovaComponents);

  // Process data through the defense system
  console.log('Processing data through the complete defense system:');
  
  // Create test data with mixed domains
  const testData = {
    // Cyber domain data
    securityScore: 8,
    threatLevel: 3,
    encryptionStrength: Infinity, // Should be bounded
    
    // Financial domain data
    balance: 1000.123, // Should be rounded to 1000.12
    interestRate: 1.5, // Should be clamped to 1.0
    transactionVolume: 1e20, // Should be bounded
    
    // Medical domain data
    heartRate: 500, // Should be clamped to 300
    bloodPressure: -50, // Should be clamped to 0
    temperature: 60 // Should be clamped to 50
  };
  
  // Process data (domain will be auto-detected)
  const processedData = await defenseSystem.processData(testData);
  
  console.log('Original data:', testData);
  console.log('Processed data:', processedData);
  
  // Get monitoring dashboard
  const dashboard = defenseSystem.monitoringDashboard;
  
  // Get current metrics
  console.log('\nCurrent metrics:');
  console.log(dashboard.getCurrentMetrics());
  
  // Get alerts
  console.log('\nAlerts:');
  console.log(dashboard.getAlerts());
  
  // Dispose resources
  defenseSystem.dispose();
}

/**
 * Example 2: Domain-Specific Processing
 * 
 * This example demonstrates how to process data through specific domains.
 */
async function example2() {
  console.log('\n=== Example 2: Domain-Specific Processing ===\n');

  // Create complete defense system
  const defenseSystem = createCompleteDefenseSystem({
    enableLogging: false, // Disable logging for cleaner output
    enableMonitoring: true,
    strictMode: true
  });

  // Process cyber domain data
  console.log('Processing cyber domain data:');
  const cyberData = {
    securityScore: 15, // Should be clamped to 10
    threatLevel: -5, // Should be clamped to 0
    encryptionStrength: Infinity // Should be bounded
  };
  
  const processedCyberData = await defenseSystem.processData(cyberData, 'cyber');
  console.log('Original cyber data:', cyberData);
  console.log('Processed cyber data:', processedCyberData);
  
  // Process financial domain data
  console.log('\nProcessing financial domain data:');
  const financialData = {
    balance: 1234.56789, // Should be rounded to 1234.57
    interestRate: 1.5, // Should be clamped to 1.0
    transactionVolume: 1e20 // Should be bounded
  };
  
  const processedFinancialData = await defenseSystem.processData(financialData, 'financial');
  console.log('Original financial data:', financialData);
  console.log('Processed financial data:', processedFinancialData);
  
  // Process medical domain data
  console.log('\nProcessing medical domain data:');
  const medicalData = {
    heartRate: 500, // Should be clamped to 300
    bloodPressure: -50, // Should be clamped to 0
    temperature: 60, // Should be clamped to 50
    patientName: 'John Doe', // Should be removed for privacy
    dateOfBirth: '1980-01-01' // Should be converted to age range
  };
  
  const processedMedicalData = await defenseSystem.processData(medicalData, 'medical');
  console.log('Original medical data:', medicalData);
  console.log('Processed medical data:', processedMedicalData);
  
  // Dispose resources
  defenseSystem.dispose();
}

/**
 * Example 3: Cross-Domain Validation
 * 
 * This example demonstrates cross-domain validation and contamination prevention.
 */
async function example3() {
  console.log('\n=== Example 3: Cross-Domain Validation ===\n');

  // Create cross-domain validator
  const validator = new CrossDomainValidator({
    enableLogging: true,
    strictMode: false, // Don't throw errors on validation failures
    enableContaminationPrevention: true,
    enableDomainDetection: true
  });

  // Create test data with mixed domains
  const mixedData = {
    // Cyber domain data
    securityScore: 8,
    threatLevel: 3,
    
    // Financial domain data
    balance: 1000.12,
    interestRate: 0.05,
    
    // Medical domain data
    heartRate: 75,
    bloodPressure: 120
  };
  
  // Detect domain
  const detectedDomain = validator.detectDomain(mixedData);
  console.log(`Detected domain: ${detectedDomain}`);
  
  // Validate cross-domain
  console.log('\nValidating cyber data in financial domain:');
  const cyberToFinancialValidation = validator.validateCrossDomain(mixedData, 'cyber', 'financial');
  console.log('Validation result:', cyberToFinancialValidation);
  
  // Prevent contamination
  console.log('\nPreventing contamination from cyber to financial domain:');
  const sanitizedData = validator.preventContamination(mixedData, 'cyber', 'financial');
  console.log('Original data:', mixedData);
  console.log('Sanitized data:', sanitizedData);
  
  // Get metrics
  console.log('\nValidator metrics:');
  console.log(validator.getMetrics());
}

/**
 * Example 4: Monitoring Dashboard
 * 
 * This example demonstrates the monitoring dashboard.
 */
async function example4() {
  console.log('\n=== Example 4: Monitoring Dashboard ===\n');

  // Create complete defense system
  const defenseSystem = createCompleteDefenseSystem({
    enableLogging: false, // Disable logging for cleaner output
    enableMonitoring: true,
    strictMode: true
  });

  // Get monitoring dashboard
  const dashboard = defenseSystem.monitoringDashboard;
  
  // Process some data to generate metrics
  console.log('Processing data to generate metrics...');
  
  // Process cyber domain data
  await defenseSystem.processData({
    securityScore: 15,
    threatLevel: -5,
    encryptionStrength: Infinity
  }, 'cyber');
  
  // Process financial domain data
  await defenseSystem.processData({
    balance: 1234.56789,
    interestRate: 1.5,
    transactionVolume: 1e20
  }, 'financial');
  
  // Process medical domain data with critical condition
  await defenseSystem.processData({
    heartRate: 200,
    bloodPressure: 200,
    temperature: 40
  }, 'medical');
  
  // Get current metrics
  console.log('\nCurrent metrics:');
  console.log(dashboard.getCurrentMetrics());
  
  // Get metrics history
  console.log('\nMetrics history:');
  const history = dashboard.getMetricsHistory();
  console.log(`History length: ${history.length}`);
  console.log('Latest history entry:', history[history.length - 1]);
  
  // Get alerts
  console.log('\nAlerts:');
  const alerts = dashboard.getAlerts();
  console.log(`Number of alerts: ${alerts.length}`);
  if (alerts.length > 0) {
    console.log('Latest alert:', alerts[alerts.length - 1]);
  }
  
  // Dispose resources
  defenseSystem.dispose();
}

/**
 * Create a mock NovaConnect component
 * @returns {Object} - Mock NovaConnect component
 */
function createMockNovaConnect() {
  return {
    connect: async (config) => {
      console.log('NovaConnect.connect called with:', config);
      return { status: 'connected', config };
    },
    processPolicy: async (policy) => {
      console.log('NovaConnect.processPolicy called with:', policy);
      return { status: 'processed', policy };
    }
  };
}

/**
 * Create a mock NovaVision component
 * @returns {Object} - Mock NovaVision component
 */
function createMockNovaVision() {
  return {
    analyze: async (data) => {
      console.log('NovaVision.analyze called with:', data);
      return { status: 'analyzed', data };
    }
  };
}

/**
 * Create a mock NovaDNA component
 * @returns {Object} - Mock NovaDNA component
 */
function createMockNovaDNA() {
  return {
    process: async (data) => {
      console.log('NovaDNA.process called with:', data);
      return { status: 'processed', data };
    }
  };
}

/**
 * Create a mock NovaShield component
 * @returns {Object} - Mock NovaShield component
 */
function createMockNovaShield() {
  return {
    protect: async (data) => {
      console.log('NovaShield.protect called with:', data);
      return { status: 'protected', data };
    }
  };
}

/**
 * Run all examples
 */
async function runAllExamples() {
  await example1();
  await example2();
  await example3();
  await example4();
}

// Run all examples
runAllExamples().catch(error => {
  console.error('Error running examples:', error);
});
