/**
 * NovaCore SOC 2 Controller
 * 
 * This controller handles API requests related to SOC 2 compliance.
 */

const { 
  SOC2EvidenceService, 
  SOC2ControlService, 
  SOC2AssessmentService 
} = require('../services');
const logger = require('../../../config/logger');

class SOC2Controller {
  /**
   * Initialize SOC 2 controls
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async initializeControls(req, res, next) {
    try {
      const result = await SOC2ControlService.initializeControls();
      
      res.status(200).json({
        success: true,
        data: result
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Get all SOC 2 controls
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async getAllControls(req, res, next) {
    try {
      // Extract filter parameters from query
      const filter = {};
      
      if (req.query.category) filter.category = req.query.category;
      if (req.query.trustServiceCriteria) filter.trustServiceCriteria = req.query.trustServiceCriteria;
      if (req.query.reference) filter.reference = req.query.reference;
      if (req.query.search) filter.search = req.query.search;
      
      // Extract pagination and sorting options
      const options = {
        page: parseInt(req.query.page, 10) || 1,
        limit: parseInt(req.query.limit, 10) || 10
      };
      
      if (req.query.sort) {
        const [field, order] = req.query.sort.split(':');
        options.sort = { [field]: order === 'desc' ? -1 : 1 };
      }
      
      const result = await SOC2ControlService.getAllControls(filter, options);
      
      res.status(200).json({
        success: true,
        data: result.data,
        pagination: result.pagination
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Get SOC 2 control by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async getControlById(req, res, next) {
    try {
      const control = await SOC2ControlService.getControlById(req.params.id);
      
      res.status(200).json({
        success: true,
        data: control
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Get SOC 2 control implementation
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async getControlImplementation(req, res, next) {
    try {
      const { organizationId, controlId } = req.params;
      const result = await SOC2ControlService.getControlImplementation(organizationId, controlId);
      
      res.status(200).json({
        success: true,
        data: result
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Update SOC 2 control implementation
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async updateControlImplementation(req, res, next) {
    try {
      const { organizationId, controlId } = req.params;
      const userId = req.user ? req.user.id : 'system';
      
      const implementation = await SOC2ControlService.updateControlImplementation(
        organizationId,
        controlId,
        req.body,
        userId
      );
      
      res.status(200).json({
        success: true,
        data: implementation
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Get SOC 2 implementation summary
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async getImplementationSummary(req, res, next) {
    try {
      const { organizationId } = req.params;
      const summary = await SOC2ControlService.getImplementationSummary(organizationId);
      
      res.status(200).json({
        success: true,
        data: summary
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Create SOC 2 evidence
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async createEvidence(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const evidence = await SOC2EvidenceService.createEvidence(req.body, userId);
      
      res.status(201).json({
        success: true,
        data: evidence
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Get all SOC 2 evidence
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async getAllEvidence(req, res, next) {
    try {
      const { organizationId } = req.params;
      
      // Extract filter parameters from query
      const filter = {};
      
      if (req.query.controlId) filter.controlId = req.query.controlId;
      if (req.query.status) filter.status = req.query.status;
      if (req.query.tags) filter.tags = req.query.tags.split(',');
      if (req.query.verificationStatus) filter.verificationStatus = req.query.verificationStatus;
      if (req.query.createdAfter) filter.createdAfter = req.query.createdAfter;
      if (req.query.createdBefore) filter.createdBefore = req.query.createdBefore;
      
      // Extract pagination and sorting options
      const options = {
        page: parseInt(req.query.page, 10) || 1,
        limit: parseInt(req.query.limit, 10) || 10
      };
      
      if (req.query.sort) {
        const [field, order] = req.query.sort.split(':');
        options.sort = { [field]: order === 'desc' ? -1 : 1 };
      }
      
      const result = await SOC2EvidenceService.getAllEvidence(organizationId, filter, options);
      
      res.status(200).json({
        success: true,
        data: result.data,
        pagination: result.pagination
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Get SOC 2 evidence by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async getEvidenceById(req, res, next) {
    try {
      const evidence = await SOC2EvidenceService.getEvidenceById(req.params.id);
      
      res.status(200).json({
        success: true,
        data: evidence
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Collect SOC 2 evidence
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async collectEvidence(req, res, next) {
    try {
      const { organizationId, controlId, source } = req.params;
      const userId = req.user ? req.user.id : 'system';
      
      const result = await SOC2EvidenceService.collectEvidence(
        organizationId,
        controlId,
        source,
        req.body,
        userId
      );
      
      res.status(200).json({
        success: true,
        data: result
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Verify SOC 2 evidence
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async verifyEvidence(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const verification = await SOC2EvidenceService.verifyEvidence(req.params.id, userId);
      
      res.status(200).json({
        success: true,
        data: verification
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Create SOC 2 assessment
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async createAssessment(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const assessment = await SOC2AssessmentService.createAssessment(req.body, userId);
      
      res.status(201).json({
        success: true,
        data: assessment
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Get all SOC 2 assessments
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async getAllAssessments(req, res, next) {
    try {
      const { organizationId } = req.params;
      
      // Extract filter parameters from query
      const filter = {};
      
      if (req.query.type) filter.type = req.query.type;
      if (req.query.status) filter.status = req.query.status;
      
      // Extract pagination and sorting options
      const options = {
        page: parseInt(req.query.page, 10) || 1,
        limit: parseInt(req.query.limit, 10) || 10
      };
      
      if (req.query.sort) {
        const [field, order] = req.query.sort.split(':');
        options.sort = { [field]: order === 'desc' ? -1 : 1 };
      }
      
      const result = await SOC2AssessmentService.getAllAssessments(organizationId, filter, options);
      
      res.status(200).json({
        success: true,
        data: result.data,
        pagination: result.pagination
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Get SOC 2 assessment by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async getAssessmentById(req, res, next) {
    try {
      const assessment = await SOC2AssessmentService.getAssessmentById(req.params.id);
      
      res.status(200).json({
        success: true,
        data: assessment
      });
    } catch (error) {
      next(error);
    }
  }
}

module.exports = new SOC2Controller();
