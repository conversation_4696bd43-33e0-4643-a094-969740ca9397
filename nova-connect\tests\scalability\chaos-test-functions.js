/**
 * NovaConnect UAC Chaos Test Functions
 * 
 * This file contains functions used by the chaos test.
 */

// Import functions from the base load test
const baseTestFunctions = require('./gcp-load-test-functions');

// Generate a random string
function randomString(length = 10) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

// Generate a random GRC check payload
function generateGrcCheckPayload(context, events, done) {
  const frameworks = ['SOC2', 'HIPAA', 'PCI-DSS', 'GDPR', 'ISO27001', 'NIST'];
  const providers = ['gcp', 'aws', 'azure', 'on-premise'];
  const services = ['compute', 'storage', 'database', 'networking', 'security'];
  
  const selectedFramework = frameworks[Math.floor(Math.random() * frameworks.length)];
  const selectedProviders = [];
  const numProviders = Math.floor(Math.random() * 3) + 1; // 1-3 providers
  
  for (let i = 0; i < numProviders; i++) {
    const provider = providers[Math.floor(Math.random() * providers.length)];
    if (!selectedProviders.includes(provider)) {
      selectedProviders.push(provider);
    }
  }
  
  const selectedServices = [];
  const numServices = Math.floor(Math.random() * 4) + 1; // 1-4 services
  
  for (let i = 0; i < numServices; i++) {
    const service = services[Math.floor(Math.random() * services.length)];
    if (!selectedServices.includes(service)) {
      selectedServices.push(service);
    }
  }
  
  const grcCheck = {
    requestId: `req-${randomString(16)}`,
    framework: selectedFramework,
    providers: selectedProviders,
    services: selectedServices,
    options: {
      includeEvidence: Math.random() > 0.5,
      detailedReport: Math.random() > 0.7,
      remediationSteps: Math.random() > 0.3
    },
    metadata: {
      source: 'chaos-test',
      environment: 'gcp',
      timestamp: new Date().toISOString()
    }
  };
  
  context.vars.grcCheck = grcCheck;
  return done();
}

// Export all functions
module.exports = {
  ...baseTestFunctions,
  generateGrcCheckPayload
};
