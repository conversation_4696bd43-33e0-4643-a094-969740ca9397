import React from 'react';
import {
  DiagramFrame,
  ComponentBox,
  ComponentLabel,
  ComponentNumber,
  Arrow,
  ContainerBox,
  ContainerLabel,
  DiagramLegend,
  LegendItem,
  LegendColor,
  LegendText,
  InventorLabel
} from '../../components/DiagramComponents';

const EnterpriseBeforeAfter = () => {
  return (
    <DiagramFrame>
      <ContainerBox width="750px" height="600px" left="25px" top="20px">
        <ContainerLabel fontSize="18px">ENTERPRISE CASE STUDY: BEFORE & AFTER</ContainerLabel>
      </ContainerBox>

      {/* Before Section */}
      <ContainerBox width="330px" height="400px" left="50px" top="70px">
        <ContainerLabel fontSize="16px">BEFORE NOVACONNECT</ContainerLabel>
      </ContainerBox>

      <ComponentBox left="80px" top="110px" width="270px" height="50px">
        <ComponentNumber>501</ComponentNumber>
        <ComponentLabel fontSize="14px">Compliance Assessment Cycles</ComponentLabel>
        <span style={{ fontSize: '12px' }}>3 Months</span>
      </ComponentBox>

      <ComponentBox left="80px" top="170px" width="270px" height="50px">
        <ComponentNumber>502</ComponentNumber>
        <ComponentLabel fontSize="14px">Manual Investigation Rate</ComponentLabel>
        <span style={{ fontSize: '12px' }}>42% of Security Events</span>
      </ComponentBox>

      <ComponentBox left="80px" top="230px" width="270px" height="50px">
        <ComponentNumber>503</ComponentNumber>
        <ComponentLabel fontSize="14px">Response Time to Critical Events</ComponentLabel>
        <span style={{ fontSize: '12px' }}>8-12 Seconds</span>
      </ComponentBox>

      <ComponentBox left="80px" top="290px" width="270px" height="50px">
        <ComponentNumber>504</ComponentNumber>
        <ComponentLabel fontSize="14px">Annual Compliance Costs</ComponentLabel>
        <span style={{ fontSize: '12px' }}>$4.2M</span>
      </ComponentBox>

      <ComponentBox left="80px" top="350px" width="270px" height="50px">
        <ComponentNumber>505</ComponentNumber>
        <ComponentLabel fontSize="14px">FTEs Dedicated to Compliance</ComponentLabel>
        <span style={{ fontSize: '12px' }}>22 Staff Members</span>
      </ComponentBox>

      {/* After Section */}
      <ContainerBox width="330px" height="400px" left="420px" top="70px">
        <ContainerLabel fontSize="16px">AFTER NOVACONNECT</ContainerLabel>
      </ContainerBox>

      <ComponentBox left="450px" top="110px" width="270px" height="50px">
        <ComponentNumber>506</ComponentNumber>
        <ComponentLabel fontSize="14px">Compliance Assessment Cycles</ComponentLabel>
        <span style={{ fontSize: '12px', fontWeight: 'bold', color: '#0A84FF' }}>Continuous Real-Time</span>
      </ComponentBox>

      <ComponentBox left="450px" top="170px" width="270px" height="50px">
        <ComponentNumber>507</ComponentNumber>
        <ComponentLabel fontSize="14px">Manual Investigation Rate</ComponentLabel>
        <span style={{ fontSize: '12px', fontWeight: 'bold', color: '#0A84FF' }}>6.3% of Security Events</span>
      </ComponentBox>

      <ComponentBox left="450px" top="230px" width="270px" height="50px">
        <ComponentNumber>508</ComponentNumber>
        <ComponentLabel fontSize="14px">Response Time to Critical Events</ComponentLabel>
        <span style={{ fontSize: '12px', fontWeight: 'bold', color: '#0A84FF' }}>2 Seconds</span>
      </ComponentBox>

      <ComponentBox left="450px" top="290px" width="270px" height="50px">
        <ComponentNumber>509</ComponentNumber>
        <ComponentLabel fontSize="14px">Annual Compliance Costs</ComponentLabel>
        <span style={{ fontSize: '12px', fontWeight: 'bold', color: '#0A84FF' }}>$1.26M</span>
      </ComponentBox>

      <ComponentBox left="450px" top="350px" width="270px" height="50px">
        <ComponentNumber>510</ComponentNumber>
        <ComponentLabel fontSize="14px">FTEs Dedicated to Compliance</ComponentLabel>
        <span style={{ fontSize: '12px', fontWeight: 'bold', color: '#0A84FF' }}>7 Staff Members</span>
      </ComponentBox>

      {/* Arrows connecting before and after */}
      <svg width="800" height="600" style={{ position: 'absolute', top: 0, left: 0, zIndex: 0 }}>
        <line x1="350" y1="135" x2="450" y2="135" stroke="#333" strokeWidth="2" markerEnd="url(#arrowhead)" />
        <line x1="350" y1="195" x2="450" y2="195" stroke="#333" strokeWidth="2" markerEnd="url(#arrowhead)" />
        <line x1="350" y1="255" x2="450" y2="255" stroke="#333" strokeWidth="2" markerEnd="url(#arrowhead)" />
        <line x1="350" y1="315" x2="450" y2="315" stroke="#333" strokeWidth="2" markerEnd="url(#arrowhead)" />
        <line x1="350" y1="375" x2="450" y2="375" stroke="#333" strokeWidth="2" markerEnd="url(#arrowhead)" />

        {/* Arrow definitions */}
        <defs>
          <marker
            id="arrowhead"
            markerWidth="10"
            markerHeight="7"
            refX="0"
            refY="3.5"
            orient="auto"
          >
            <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
          </marker>
        </defs>
      </svg>

      {/* Key Results Section */}
      <ContainerBox width="700px" height="100px" left="50px" top="490px">
        <ContainerLabel fontSize="16px">KEY RESULTS</ContainerLabel>
      </ContainerBox>

      <ComponentBox left="80px" top="520px" width="120px" height="50px">
        <ComponentNumber>511</ComponentNumber>
        <ComponentLabel fontSize="12px">Data Normalization</ComponentLabel>
        <span style={{ fontSize: '12px', fontWeight: 'bold', color: '#0A84FF' }}>3,142x Faster</span>
      </ComponentBox>

      <ComponentBox left="220px" top="520px" width="120px" height="50px">
        <ComponentNumber>512</ComponentNumber>
        <ComponentLabel fontSize="12px">Event Processing</ComponentLabel>
        <span style={{ fontSize: '12px', fontWeight: 'bold', color: '#0A84FF' }}>69,000 Events/Sec</span>
      </ComponentBox>

      <ComponentBox left="360px" top="520px" width="120px" height="50px">
        <ComponentNumber>513</ComponentNumber>
        <ComponentLabel fontSize="12px">Cost Reduction</ComponentLabel>
        <span style={{ fontSize: '12px', fontWeight: 'bold', color: '#0A84FF' }}>82%</span>
      </ComponentBox>

      <ComponentBox left="500px" top="520px" width="120px" height="50px">
        <ComponentNumber>514</ComponentNumber>
        <ComponentLabel fontSize="12px">Staff Efficiency</ComponentLabel>
        <span style={{ fontSize: '12px', fontWeight: 'bold', color: '#0A84FF' }}>68% Improvement</span>
      </ComponentBox>

      <ComponentBox left="640px" top="520px" width="120px" height="50px">
        <ComponentNumber>515</ComponentNumber>
        <ComponentLabel fontSize="12px">Compliance Accuracy</ComponentLabel>
        <span style={{ fontSize: '12px', fontWeight: 'bold', color: '#0A84FF' }}>99.97%</span>
      </ComponentBox>

      {/* Legend */}
      <DiagramLegend>
        <LegendItem>
          <LegendColor color="#fff" />
          <LegendText>Before & After Metrics</LegendText>
        </LegendItem>
        <LegendItem>
          <LegendColor color="#fff" style={{ border: '1px dashed #333' }} />
          <LegendText>Key Performance Indicators</LegendText>
        </LegendItem>
      </DiagramLegend>

      <InventorLabel>Inventor: David Nigel Irvin</InventorLabel>
    </DiagramFrame>
  );
};

export default EnterpriseBeforeAfter;
