/**
 * PROFIT ANALYTICS DASHBOARD
 * Comprehensive profit tracking with accumulative views and category breakdowns
 * Shows day-to-day, week-by-week, month-by-month, YTD performance
 * Breakdown by Stocks, Crypto, and Forex markets
 */

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  ChartBarIcon, 
  TrendingUpIcon, 
  CurrencyDollarIcon,
  CalendarIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  EyeIcon
} from '@heroicons/react/24/outline';

const MARKET_COLORS = {
  STOCKS: '#3B82F6',
  CRYPTO: '#F59E0B', 
  FOREX: '#10B981'
};

const TIMEFRAME_OPTIONS = [
  { key: 'daily', label: 'Daily', icon: '📅' },
  { key: 'weekly', label: 'Weekly', icon: '📊' },
  { key: 'monthly', label: 'Monthly', icon: '📈' },
  { key: 'yearly', label: 'Yearly', icon: '🏆' }
];

export default function ProfitAnalyticsDashboard() {
  const [analyticsData, setAnalyticsData] = useState({
    current_period_stats: {
      today: { total_profit: 0.00, total_trades: 0, win_rate: 0.0 },
      this_week: { total_profit: 0.00, total_trades: 0, win_rate: 0.0 },
      this_month: { total_profit: 0.00, total_trades: 0, win_rate: 0.0 },
      this_year: { total_profit: 0.00, total_trades: 0, win_rate: 0.0 },
      ytd_total: 0.00
    },
    accumulative_profits: {
      daily: [],
      weekly: [],
      monthly: [],
      yearly: []
    },
    category_performance: []
  });

  const [selectedTimeframe, setSelectedTimeframe] = useState('daily');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    updateAnalyticsData();
    
    // Update every 60 seconds
    const interval = setInterval(() => {
      updateAnalyticsData();
    }, 60000);

    return () => clearInterval(interval);
  }, []);

  const updateAnalyticsData = async () => {
    try {
      const response = await fetch('/api/analytics/profit-tracker');
      const data = await response.json();
      
      if (data.success) {
        setAnalyticsData(data);
        setIsLoading(false);
      }
    } catch (error) {
      console.error('Profit analytics error:', error);
      setIsLoading(false);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount || 0);
  };

  const formatPercentage = (value) => {
    return `${(value || 0).toFixed(1)}%`;
  };

  const getProfitColor = (profit) => {
    return profit >= 0 ? 'text-green-400' : 'text-red-400';
  };

  const getProfitIcon = (profit) => {
    return profit >= 0 ? ArrowUpIcon : ArrowDownIcon;
  };

  if (isLoading) {
    return (
      <div className="p-6 rounded-lg border border-gray-600 bg-gray-800/50">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-600 rounded mb-4"></div>
          <div className="grid grid-cols-4 gap-4">
            {[1,2,3,4].map(i => (
              <div key={i} className="h-20 bg-gray-600 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  const currentStats = analyticsData.current_period_stats;
  const accumulativeData = analyticsData.accumulative_profits[selectedTimeframe] || [];
  const categoryData = analyticsData.category_performance || [];

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="p-6 rounded-lg border border-emerald-500/30 bg-gradient-to-br from-emerald-900/20 to-green-900/20 backdrop-blur-sm"
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <TrendingUpIcon className="w-6 h-6 text-emerald-400" />
          <div>
            <h3 className="text-xl font-bold text-white">
              Profit Analytics Dashboard
            </h3>
            <p className="text-sm text-gray-400">
              Accumulative Performance & Category Breakdown
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <EyeIcon className="w-5 h-5 text-emerald-400" />
          <span className="text-emerald-400 font-mono text-sm">
            LIVE TRACKING
          </span>
        </div>
      </div>

      {/* Current Period Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <div className="p-4 rounded-lg bg-gray-800/50 border border-gray-600">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-gray-400">Today</span>
            <CalendarIcon className="w-4 h-4 text-blue-400" />
          </div>
          <div className={`text-lg font-bold ${getProfitColor(currentStats.today.total_profit)}`}>
            {formatCurrency(currentStats.today.total_profit)}
          </div>
          <div className="text-xs text-gray-400">
            {currentStats.today.total_trades} trades • {formatPercentage(currentStats.today.win_rate)} win
          </div>
        </div>

        <div className="p-4 rounded-lg bg-gray-800/50 border border-gray-600">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-gray-400">This Week</span>
            <ChartBarIcon className="w-4 h-4 text-green-400" />
          </div>
          <div className={`text-lg font-bold ${getProfitColor(currentStats.this_week.total_profit)}`}>
            {formatCurrency(currentStats.this_week.total_profit)}
          </div>
          <div className="text-xs text-gray-400">
            {currentStats.this_week.total_trades} trades • {formatPercentage(currentStats.this_week.win_rate)} win
          </div>
        </div>

        <div className="p-4 rounded-lg bg-gray-800/50 border border-gray-600">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-gray-400">This Month</span>
            <TrendingUpIcon className="w-4 h-4 text-yellow-400" />
          </div>
          <div className={`text-lg font-bold ${getProfitColor(currentStats.this_month.total_profit)}`}>
            {formatCurrency(currentStats.this_month.total_profit)}
          </div>
          <div className="text-xs text-gray-400">
            {currentStats.this_month.total_trades} trades • {formatPercentage(currentStats.this_month.win_rate)} win
          </div>
        </div>

        <div className="p-4 rounded-lg bg-gray-800/50 border border-gray-600">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-gray-400">Year to Date</span>
            <CurrencyDollarIcon className="w-4 h-4 text-purple-400" />
          </div>
          <div className={`text-lg font-bold ${getProfitColor(currentStats.ytd_total)}`}>
            {formatCurrency(currentStats.ytd_total)}
          </div>
          <div className="text-xs text-gray-400">
            {currentStats.this_year.total_trades} trades • {formatPercentage(currentStats.this_year.win_rate)} win
          </div>
        </div>
      </div>

      {/* Timeframe Selector */}
      <div className="mb-6">
        <div className="flex items-center space-x-2 mb-4">
          <span className="text-sm text-gray-400">Accumulative View:</span>
          <div className="flex space-x-1">
            {TIMEFRAME_OPTIONS.map(option => (
              <button
                key={option.key}
                onClick={() => setSelectedTimeframe(option.key)}
                className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
                  selectedTimeframe === option.key
                    ? 'bg-emerald-600 text-white'
                    : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                }`}
              >
                {option.icon} {option.label}
              </button>
            ))}
          </div>
        </div>

        {/* Accumulative Data Display */}
        <div className="bg-gray-800/30 rounded-lg p-4 max-h-40 overflow-y-auto">
          <div className="grid grid-cols-1 gap-2">
            {accumulativeData.slice(-10).map((period, index) => {
              const ProfitIcon = getProfitIcon(period.total_profit);
              const periodLabel = selectedTimeframe === 'daily' ? period.date :
                                selectedTimeframe === 'weekly' ? `Week of ${period.week}` :
                                selectedTimeframe === 'monthly' ? period.month :
                                period.year;
              
              return (
                <div key={index} className="flex items-center justify-between p-2 rounded bg-gray-700/50">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-300">{periodLabel}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className={`text-sm font-medium ${getProfitColor(period.total_profit)}`}>
                      {formatCurrency(period.total_profit)}
                    </span>
                    <ProfitIcon className={`w-4 h-4 ${getProfitColor(period.total_profit)}`} />
                    <span className="text-xs text-gray-400">
                      ({formatCurrency(period.cumulative_profit || 0)} total)
                    </span>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Category Breakdown */}
      <div>
        <h4 className="text-lg font-semibold text-white mb-4">📊 Performance by Market Category</h4>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {categoryData.map(category => (
            <div key={category.category} className="p-4 rounded-lg bg-gray-800/50 border border-gray-600">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-2">
                  <div 
                    className="w-4 h-4 rounded"
                    style={{ backgroundColor: category.color }}
                  ></div>
                  <span className="text-white font-medium">{category.category}</span>
                </div>
                <span className={`text-lg font-bold ${getProfitColor(category.total_profit)}`}>
                  {formatCurrency(category.total_profit)}
                </span>
              </div>
              
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-400">Total Trades:</span>
                  <span className="text-white">{category.total_trades}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Win Rate:</span>
                  <span className="text-green-400">{formatPercentage(category.win_rate)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Avg Win:</span>
                  <span className="text-green-400">{formatCurrency(category.avg_win)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Avg Loss:</span>
                  <span className="text-red-400">{formatCurrency(category.avg_loss)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Profit Factor:</span>
                  <span className="text-blue-400">{(category.profit_factor || 0).toFixed(2)}</span>
                </div>
              </div>

              {/* Best/Worst Trade */}
              {category.best_trade && (
                <div className="mt-3 pt-3 border-t border-gray-600">
                  <div className="text-xs text-gray-400 mb-1">Best Trade:</div>
                  <div className="flex justify-between">
                    <span className="text-xs text-gray-300">{category.best_trade.symbol}</span>
                    <span className="text-xs text-green-400">
                      {formatCurrency(category.best_trade.net_profit)}
                    </span>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Summary Stats */}
      <div className="mt-6 p-4 rounded-lg bg-gradient-to-r from-emerald-900/30 to-green-900/30 border border-emerald-500/30">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
          <div>
            <div className="text-2xl font-bold text-emerald-400">
              {analyticsData.total_trades || 0}
            </div>
            <div className="text-sm text-gray-400">Total Trades</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-green-400">
              {formatCurrency(currentStats.ytd_total)}
            </div>
            <div className="text-sm text-gray-400">YTD Profit</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-blue-400">
              {formatPercentage(currentStats.this_year.win_rate)}
            </div>
            <div className="text-sm text-gray-400">Overall Win Rate</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-purple-400">
              {categoryData.length > 0 ? 
                formatCurrency(currentStats.ytd_total / Math.max(1, currentStats.this_year.total_trades)) : 
                '$0.00'
              }
            </div>
            <div className="text-sm text-gray-400">Avg Per Trade</div>
          </div>
        </div>
      </div>
    </motion.div>
  );
}
