/**
 * EmergencyDataPipeline.js
 * 
 * This module provides a secure data pipeline for emergency medical data.
 * It extends the NovaConnectAdapter to implement emergency-specific functionality.
 */

const NovaConnectAdapter = require('../NovaConnectAdapter');
const { v4: uuidv4 } = require('uuid');

/**
 * EmergencyDataPipeline class for secure emergency data access
 */
class EmergencyDataPipeline {
  constructor(options = {}) {
    this.novaConnectAdapter = options.novaConnectAdapter || new NovaConnectAdapter(options);
    this.cacheEnabled = options.cacheEnabled !== false;
    this.cacheTimeout = options.cacheTimeout || 300000; // 5 minutes
    this.dataCache = new Map();
    this.activeSessions = new Map();
  }

  /**
   * Create a secure data transfer session
   * @param {Object} context - The emergency context
   * @returns {Object} - The session information
   */
  createTransferSession(context = {}) {
    const sessionId = uuidv4();
    const timestamp = new Date().toISOString();
    const expiresAt = new Date(Date.now() + this.cacheTimeout).toISOString();
    
    // Create session
    const session = {
      sessionId,
      timestamp,
      expiresAt,
      context: {
        emergencyType: context.emergencyType || 'MEDICAL',
        emergencySeverity: context.emergencySeverity || 'MODERATE',
        responderType: context.responderType || 'PARAMEDIC',
        locationType: context.locationType || 'AMBULANCE'
      },
      status: 'ACTIVE'
    };
    
    // Store session
    this.activeSessions.set(sessionId, session);
    
    return session;
  }

  /**
   * Fetch emergency medical data from connected EHR systems
   * @param {String} patientId - The patient ID
   * @param {String} sessionId - The transfer session ID
   * @returns {Promise<Object>} - The emergency medical data
   */
  async fetchEmergencyData(patientId, sessionId) {
    // Validate session
    const session = this.activeSessions.get(sessionId);
    if (!session || session.status !== 'ACTIVE') {
      throw new Error('Invalid or expired session');
    }
    
    // Check cache if enabled
    const cacheKey = `${patientId}:${session.context.emergencyType}`;
    if (this.cacheEnabled) {
      const cachedData = this.dataCache.get(cacheKey);
      if (cachedData && cachedData.expiresAt > Date.now()) {
        return cachedData.data;
      }
    }
    
    // Determine data types based on emergency context
    const dataTypes = this._getDataTypesByContext(session.context);
    
    try {
      // Get available connectors
      const connectors = await this.novaConnectAdapter.getAvailableConnectors();
      
      // Try to fetch data from each connector
      const results = [];
      
      for (const connector of connectors) {
        try {
          // In a real implementation, we would have connection IDs for each patient
          // For now, we'll simulate a connection ID
          const connectionId = `${connector.id}-connection`;
          
          // Fetch patient data
          const patientData = await this.novaConnectAdapter.fetchPatientData(
            connectionId,
            patientId,
            {
              dataTypes
            }
          );
          
          if (patientData) {
            results.push({
              source: connector.name,
              data: patientData
            });
          }
        } catch (error) {
          console.warn(`Failed to fetch data from ${connector.name}:`, error.message);
          // Continue with other connectors
        }
      }
      
      // Merge results
      const mergedData = this._mergeResults(results);
      
      // Create emergency profile
      const emergencyProfile = this.novaConnectAdapter.createProfileFromEHR(mergedData);
      
      // Cache the result if enabled
      if (this.cacheEnabled) {
        this.dataCache.set(cacheKey, {
          data: emergencyProfile,
          timestamp: Date.now(),
          expiresAt: Date.now() + this.cacheTimeout
        });
      }
      
      // Update session
      session.lastAccessed = new Date().toISOString();
      
      return emergencyProfile;
    } catch (error) {
      throw new Error(`Failed to fetch emergency data: ${error.message}`);
    }
  }

  /**
   * Close a data transfer session
   * @param {String} sessionId - The session ID
   * @returns {Boolean} - Whether the session was closed successfully
   */
  closeSession(sessionId) {
    const session = this.activeSessions.get(sessionId);
    
    if (!session) {
      return false;
    }
    
    // Update session status
    session.status = 'CLOSED';
    session.closedAt = new Date().toISOString();
    
    return true;
  }

  /**
   * Get active sessions
   * @returns {Array} - The active sessions
   */
  getActiveSessions() {
    const sessions = [];
    
    for (const [sessionId, session] of this.activeSessions.entries()) {
      if (session.status === 'ACTIVE') {
        sessions.push({
          sessionId,
          timestamp: session.timestamp,
          expiresAt: session.expiresAt,
          context: session.context
        });
      }
    }
    
    return sessions;
  }

  /**
   * Clear expired sessions and cache
   */
  clearExpired() {
    const now = Date.now();
    
    // Clear expired sessions
    for (const [sessionId, session] of this.activeSessions.entries()) {
      if (new Date(session.expiresAt).getTime() < now) {
        session.status = 'EXPIRED';
        session.expiredAt = new Date().toISOString();
      }
    }
    
    // Clear expired cache
    if (this.cacheEnabled) {
      for (const [cacheKey, cacheEntry] of this.dataCache.entries()) {
        if (cacheEntry.expiresAt < now) {
          this.dataCache.delete(cacheKey);
        }
      }
    }
  }

  /**
   * Get data types based on emergency context
   * @param {Object} context - The emergency context
   * @returns {Array} - The data types to fetch
   * @private
   */
  _getDataTypesByContext(context) {
    // Base data types for all emergencies
    const baseTypes = [
      'demographics',
      'contacts',
      'allergies',
      'medications',
      'conditions',
      'advancedDirectives'
    ];
    
    // Add context-specific data types
    switch (context.emergencyType) {
      case 'CARDIAC':
        return [...baseTypes, 'cardiology', 'vitals', 'procedures', 'implants'];
      
      case 'RESPIRATORY':
        return [...baseTypes, 'pulmonary', 'vitals', 'procedures'];
      
      case 'TRAUMA':
        return [...baseTypes, 'imaging', 'surgeries', 'bloodType'];
      
      case 'ALLERGIC':
        return [...baseTypes, 'allergies', 'immunology', 'vitals'];
      
      case 'NEUROLOGICAL':
        return [...baseTypes, 'neurology', 'imaging', 'procedures'];
      
      default:
        return baseTypes;
    }
  }

  /**
   * Merge results from multiple EHR systems
   * @param {Array} results - The results to merge
   * @returns {Object} - The merged data
   * @private
   */
  _mergeResults(results) {
    if (results.length === 0) {
      return {};
    }
    
    if (results.length === 1) {
      return results[0].data;
    }
    
    // Start with the most complete result
    results.sort((a, b) => {
      const aKeys = Object.keys(a.data).length;
      const bKeys = Object.keys(b.data).length;
      return bKeys - aKeys;
    });
    
    const merged = { ...results[0].data };
    
    // Merge additional data from other results
    for (let i = 1; i < results.length; i++) {
      const result = results[i].data;
      
      // Merge each section
      for (const key of Object.keys(result)) {
        if (!merged[key]) {
          // Section doesn't exist in merged data, add it
          merged[key] = result[key];
        } else if (Array.isArray(merged[key])) {
          // Merge arrays (deduplicate by name)
          const existingNames = new Set(merged[key].map(item => item.name));
          
          for (const item of result[key]) {
            if (!existingNames.has(item.name)) {
              merged[key].push(item);
              existingNames.add(item.name);
            }
          }
        } else if (typeof merged[key] === 'object') {
          // Merge objects
          merged[key] = { ...merged[key], ...result[key] };
        }
      }
    }
    
    return merged;
  }
}

module.exports = EmergencyDataPipeline;
