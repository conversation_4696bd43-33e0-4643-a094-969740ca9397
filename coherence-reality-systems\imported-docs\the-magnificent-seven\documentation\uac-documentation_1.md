# NovaFuse Universal API Connector (UAC) Documentation

## Overview

The NovaFuse Universal API Connector (UAC) is a powerful, secure, and flexible API integration platform that enables seamless connectivity between different systems and services. It provides a standardized way to connect to various APIs, transform data, and execute operations across multiple platforms.

## Key Features

- **Universal Connectivity**: Connect to any API regardless of protocol (REST, GraphQL, SOAP, etc.)
- **Security-First Design**: Built-in protection against common vulnerabilities (SSRF, XSS, SQL Injection)
- **Flexible Authentication**: Support for various authentication methods (API Key, OAuth 2.0, Basic Auth)
- **Data Transformation**: Transform data between different formats and schemas
- **Comprehensive Monitoring**: Built-in metrics and logging for all API operations
- **Rate Limiting**: Protect your systems and third-party APIs from overload
- **Connector Registry**: Centralized repository of API connectors with versioning
- **Credential Management**: Secure storage and management of API credentials

## Architecture

The UAC consists of the following components:

1. **Connector Registry**: Manages API connector templates and their configurations
2. **Authentication Manager**: Handles secure storage and retrieval of API credentials
3. **Connector Executor**: Executes API operations with security protections
4. **Security Utilities**: Provides security features like SSRF protection and input validation
5. **API Layer**: Exposes the UAC functionality through a RESTful API

## Getting Started

### Prerequisites

- Node.js 14.x or higher
- npm 7.x or higher

### Installation

```bash
# Clone the repository
git clone https://github.com/novafuse/nova-connect.git

# Install dependencies
cd nova-connect
npm install

# Start the server
npm start
```

### Configuration

The UAC can be configured through environment variables:

```
PORT=3010                      # Port for the API server
NODE_ENV=production            # Environment (development, production)
LOG_LEVEL=info                 # Logging level (debug, info, warn, error)
TEMPLATES_DIR=./templates      # Directory for connector templates
CREDENTIALS_ENCRYPTION_KEY=    # Encryption key for credentials
```

## API Reference

### Connector Management

#### Get All Connectors

```
GET /connectors
```

Query Parameters:
- `category` (optional): Filter connectors by category
- `query` (optional): Search connectors by name, description, or tags

Response:
```json
[
  {
    "metadata": {
      "name": "Google Cloud Security",
      "version": "1.0.0",
      "category": "Cloud Security",
      "description": "Connect to Google Cloud Security Command Center API",
      "author": "NovaFuse",
      "tags": ["google", "cloud", "security"]
    },
    "authentication": {
      "type": "OAUTH2",
      "fields": {
        "clientId": {
          "type": "string",
          "description": "OAuth 2.0 Client ID",
          "required": true
        },
        "clientSecret": {
          "type": "string",
          "description": "OAuth 2.0 Client Secret",
          "required": true,
          "sensitive": true
        }
      }
    },
    "configuration": {
      "baseUrl": "https://securitycenter.googleapis.com/v1"
    },
    "endpoints": [
      {
        "id": "list-findings",
        "name": "List Security Findings",
        "path": "organizations/{organizationId}/sources/{sourceId}/findings",
        "method": "GET"
      }
    ]
  }
]
```

#### Get Connector by ID

```
GET /connectors/:id
```

Response:
```json
{
  "metadata": {
    "name": "Google Cloud Security",
    "version": "1.0.0",
    "category": "Cloud Security",
    "description": "Connect to Google Cloud Security Command Center API",
    "author": "NovaFuse",
    "tags": ["google", "cloud", "security"]
  },
  "authentication": {
    "type": "OAUTH2",
    "fields": {
      "clientId": {
        "type": "string",
        "description": "OAuth 2.0 Client ID",
        "required": true
      },
      "clientSecret": {
        "type": "string",
        "description": "OAuth 2.0 Client Secret",
        "required": true,
        "sensitive": true
      }
    }
  },
  "configuration": {
    "baseUrl": "https://securitycenter.googleapis.com/v1"
  },
  "endpoints": [
    {
      "id": "list-findings",
      "name": "List Security Findings",
      "path": "organizations/{organizationId}/sources/{sourceId}/findings",
      "method": "GET"
    }
  ]
}
```

#### Register a Connector

```
POST /connectors
```

Request Body: Connector template (see example above)

Response:
```json
{
  "message": "Connector registered successfully"
}
```

#### Update a Connector

```
PUT /connectors/:id
```

Request Body: Updated connector template

Response:
```json
{
  "message": "Connector updated successfully"
}
```

#### Delete a Connector

```
DELETE /connectors/:id
```

Response:
```json
{
  "message": "Connector deleted successfully"
}
```

### Credential Management

#### Store Credentials

```
POST /credentials
```

Request Body:
```json
{
  "connectorId": "google-cloud-security-1.0.0",
  "credentials": {
    "clientId": "your-client-id",
    "clientSecret": "your-client-secret"
  }
}
```

Response:
```json
{
  "credentialId": "cred-123456"
}
```

#### Delete Credentials

```
DELETE /credentials/:id
```

Response:
```json
{
  "message": "Credentials deleted successfully"
}
```

#### Test Connection

```
POST /credentials/:id/test
```

Request Body:
```json
{
  "connectorId": "google-cloud-security-1.0.0"
}
```

Response:
```json
{
  "success": true,
  "message": "Connection successful"
}
```

### Execution

#### Execute an Endpoint

```
POST /execute/:connectorId/:endpointId
```

Request Body:
```json
{
  "credentialId": "cred-123456",
  "parameters": {
    "path": {
      "organizationId": "123",
      "sourceId": "456"
    },
    "query": {
      "pageSize": 10
    }
  }
}
```

Response: The response from the API

### Metrics

#### Get Metrics

```
GET /metrics
```

Response:
```json
{
  "executor": {
    "totalRequests": 100,
    "successfulRequests": 95,
    "failedRequests": 5,
    "blockedRequests": 0,
    "averageRequestTime": 150
  },
  "registry": {
    "totalConnectors": 10,
    "connectorsByCategory": {
      "Cloud Security": 3,
      "Identity & Access Management": 2
    }
  }
}
```

## Connector Template Format

A connector template is a JSON document that describes an API connector. It includes metadata, authentication configuration, endpoint definitions, and more.

### Example Connector Template

```json
{
  "metadata": {
    "name": "Google Cloud Security",
    "version": "1.0.0",
    "category": "Cloud Security",
    "description": "Connect to Google Cloud Security Command Center API",
    "author": "NovaFuse",
    "tags": ["google", "cloud", "security"],
    "created": "2023-06-01T00:00:00Z",
    "updated": "2023-06-01T00:00:00Z",
    "icon": "https://storage.googleapis.com/novafuse-icons/gcp-security.png",
    "documentationUrl": "https://cloud.google.com/security-command-center/docs"
  },
  "authentication": {
    "type": "OAUTH2",
    "fields": {
      "clientId": {
        "type": "string",
        "description": "OAuth 2.0 Client ID",
        "required": true
      },
      "clientSecret": {
        "type": "string",
        "description": "OAuth 2.0 Client Secret",
        "required": true,
        "sensitive": true
      },
      "projectId": {
        "type": "string",
        "description": "Google Cloud Project ID",
        "required": true
      }
    },
    "testConnection": {
      "endpoint": "organizations/{organizationId}/assets",
      "method": "GET",
      "expectedResponse": {
        "status": 200
      }
    },
    "oauth2Config": {
      "authorizationUrl": "https://accounts.google.com/o/oauth2/auth",
      "tokenUrl": "https://oauth2.googleapis.com/token",
      "scopes": [
        "https://www.googleapis.com/auth/cloud-platform"
      ],
      "grantType": "client_credentials"
    }
  },
  "configuration": {
    "baseUrl": "https://securitycenter.googleapis.com/v1",
    "headers": {
      "Content-Type": "application/json",
      "Accept": "application/json"
    },
    "rateLimit": {
      "requests": 100,
      "period": "1m"
    },
    "timeout": 30000,
    "retryPolicy": {
      "maxRetries": 3,
      "backoffStrategy": "exponential"
    }
  },
  "endpoints": [
    {
      "id": "list-findings",
      "name": "List Security Findings",
      "description": "List security findings from Security Command Center",
      "path": "organizations/{organizationId}/sources/{sourceId}/findings",
      "method": "GET",
      "parameters": {
        "path": {
          "organizationId": {
            "type": "string",
            "description": "Organization ID",
            "required": true
          },
          "sourceId": {
            "type": "string",
            "description": "Source ID",
            "required": true
          }
        },
        "query": {
          "filter": {
            "type": "string",
            "description": "Filter expression"
          },
          "orderBy": {
            "type": "string",
            "description": "Order by expression"
          },
          "pageSize": {
            "type": "integer",
            "description": "Page size",
            "default": 100
          },
          "pageToken": {
            "type": "string",
            "description": "Page token"
          }
        }
      },
      "pagination": {
        "type": "token",
        "parameters": {
          "nextPageToken": "nextPageToken",
          "pageToken": "pageToken"
        }
      },
      "response": {
        "successCode": 200,
        "dataPath": "findings",
        "schema": {
          "type": "object",
          "properties": {
            "findings": {
              "type": "array",
              "items": {
                "type": "object",
                "properties": {
                  "name": {
                    "type": "string"
                  },
                  "parent": {
                    "type": "string"
                  },
                  "resourceName": {
                    "type": "string"
                  },
                  "state": {
                    "type": "string"
                  },
                  "category": {
                    "type": "string"
                  },
                  "severity": {
                    "type": "string"
                  },
                  "eventTime": {
                    "type": "string"
                  },
                  "createTime": {
                    "type": "string"
                  }
                }
              }
            },
            "nextPageToken": {
              "type": "string"
            }
          }
        }
      }
    }
  ],
  "mappings": [
    {
      "sourceEndpoint": "list-findings",
      "targetSystem": "NovaGRC",
      "targetEntity": "SecurityFindings",
      "transformations": [
        {
          "source": "$.name",
          "target": "id",
          "transform": "identity"
        },
        {
          "source": "$.category",
          "target": "category",
          "transform": "identity"
        },
        {
          "source": "$.severity",
          "target": "severity",
          "transform": "mapSeverity",
          "parameters": {
            "mapping": {
              "CRITICAL": "critical",
              "HIGH": "high",
              "MEDIUM": "medium",
              "LOW": "low"
            }
          }
        }
      ]
    }
  ],
  "events": {
    "polling": [
      {
        "endpoint": "list-findings",
        "interval": "15m",
        "condition": "hasNewFindings"
      }
    ]
  }
}
```

## Security Considerations

The UAC includes several security features to protect against common vulnerabilities:

### SSRF Protection

The UAC includes protection against Server-Side Request Forgery (SSRF) attacks by:

- Validating URLs against a whitelist of allowed domains
- Blocking requests to private IP addresses
- Enforcing HTTPS for all API requests

### Input Validation

All user input is validated to prevent injection attacks:

- XSS protection
- SQL injection protection
- Command injection protection

### Rate Limiting

The UAC includes rate limiting to protect against abuse:

- Configurable rate limits per client
- Automatic blocking of clients that exceed rate limits
- Rate limit headers in API responses

### Credential Protection

API credentials are protected by:

- Encryption at rest
- Secure credential storage
- Automatic redaction of sensitive information in logs

## Best Practices

### Creating Connector Templates

1. **Use HTTPS**: Always use HTTPS for API endpoints
2. **Minimize Permissions**: Request only the permissions needed for the connector
3. **Document Parameters**: Provide clear descriptions for all parameters
4. **Include Test Connection**: Define a test connection endpoint for validation
5. **Define Response Schemas**: Include response schemas for all endpoints

### Executing API Operations

1. **Handle Rate Limits**: Implement backoff strategies for rate-limited APIs
2. **Validate Responses**: Always validate API responses before processing
3. **Monitor Performance**: Keep track of API performance metrics
4. **Log Errors**: Log all API errors for troubleshooting
5. **Implement Retries**: Retry failed requests with exponential backoff

## Troubleshooting

### Common Issues

#### Connection Errors

- Check that the API credentials are correct
- Verify that the API endpoint is accessible
- Check for rate limiting or IP blocking

#### Authentication Errors

- Verify that the authentication configuration is correct
- Check that the credentials have not expired
- Ensure that the required scopes are included

#### Execution Errors

- Check the API parameters for correctness
- Verify that the endpoint path is correct
- Check for API-specific error messages

### Logging

The UAC logs all API operations to help with troubleshooting:

- Request details (URL, method, parameters)
- Response status and timing
- Error messages and stack traces

## Support

For support with the NovaFuse Universal API Connector, please contact:

- Email: <EMAIL>
- Website: https://novafuse.com/support
- Documentation: https://docs.novafuse.com/uac
