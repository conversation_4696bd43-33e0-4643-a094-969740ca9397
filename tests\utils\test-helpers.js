/**
 * NovaFuse API Superstore - Test Helpers
 * 
 * This module provides helper functions for writing tests.
 */

const { v4: uuidv4 } = require('uuid');
const { Connector, ConnectorStatus, ConnectorType } = require('../../src/connectors/models/connector');

/**
 * Generate a test connector with random data
 * 
 * @param {Object} overrides - Properties to override in the generated connector
 * @returns {Connector} - A test connector
 */
function generateTestConnector(overrides = {}) {
  const id = overrides.id || uuidv4();
  const name = overrides.name || `Test Connector ${id.substring(0, 8)}`;
  
  return new Connector({
    id,
    name,
    description: overrides.description || `A test connector for ${name}`,
    version: overrides.version || '1.0.0',
    type: overrides.type || ConnectorType.SOURCE,
    status: overrides.status || ConnectorStatus.DRAFT,
    configSchema: overrides.configSchema || {
      type: 'object',
      properties: {
        apiKey: {
          type: 'string',
          description: 'API Key for authentication'
        },
        baseUrl: {
          type: 'string',
          description: 'Base URL for the API'
        }
      },
      required: ['apiKey']
    },
    ...overrides
  });
}

/**
 * Generate a test configuration for a connector
 * 
 * @param {string} connectorId - The connector ID
 * @param {Object} overrides - Properties to override in the generated configuration
 * @returns {Object} - A test configuration
 */
function generateTestConfig(connectorId, overrides = {}) {
  const id = overrides.id || uuidv4();
  const name = overrides.name || `Test Config ${id.substring(0, 8)}`;
  
  return {
    id,
    name,
    description: overrides.description || `A test configuration for ${name}`,
    connectorId,
    values: overrides.values || {
      apiKey: 'test-api-key',
      baseUrl: 'https://api.example.com'
    },
    createdAt: overrides.createdAt || new Date().toISOString(),
    updatedAt: overrides.updatedAt || new Date().toISOString(),
    ...overrides
  };
}

/**
 * Generate a test execution result
 * 
 * @param {string} connectorId - The connector ID
 * @param {string} configId - The configuration ID
 * @param {Object} overrides - Properties to override in the generated result
 * @returns {Object} - A test execution result
 */
function generateTestExecutionResult(connectorId, configId, overrides = {}) {
  const id = overrides.id || uuidv4();
  
  return {
    id,
    connectorId,
    configId,
    status: overrides.status || 'success',
    startTime: overrides.startTime || new Date().toISOString(),
    endTime: overrides.endTime || new Date().toISOString(),
    duration: overrides.duration || 123,
    result: overrides.result || { message: 'Execution completed successfully' },
    error: overrides.error || null,
    ...overrides
  };
}

/**
 * Wait for a specified time
 * 
 * @param {number} ms - The time to wait in milliseconds
 * @returns {Promise} - A promise that resolves after the specified time
 */
function wait(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Measure the execution time of a function
 * 
 * @param {Function} fn - The function to measure
 * @param {Array} args - The arguments to pass to the function
 * @returns {Object} - The result and execution time
 */
async function measureExecutionTime(fn, ...args) {
  const start = Date.now();
  const result = await fn(...args);
  const duration = Date.now() - start;
  
  return { result, duration };
}

/**
 * Create a mock request object
 * 
 * @param {Object} overrides - Properties to override in the mock request
 * @returns {Object} - A mock request object
 */
function createMockRequest(overrides = {}) {
  return {
    body: overrides.body || {},
    query: overrides.query || {},
    params: overrides.params || {},
    headers: overrides.headers || {
      'content-type': 'application/json',
      'authorization': 'Bearer test-token'
    },
    ...overrides
  };
}

/**
 * Create a mock response object
 * 
 * @returns {Object} - A mock response object with jest mock functions
 */
function createMockResponse() {
  const res = {
    status: jest.fn().mockReturnThis(),
    json: jest.fn().mockReturnThis(),
    send: jest.fn().mockReturnThis(),
    end: jest.fn().mockReturnThis(),
    setHeader: jest.fn().mockReturnThis(),
    getHeader: jest.fn().mockReturnValue(null),
    headersSent: false
  };
  
  return res;
}

module.exports = {
  generateTestConnector,
  generateTestConfig,
  generateTestExecutionResult,
  wait,
  measureExecutionTime,
  createMockRequest,
  createMockResponse
};
