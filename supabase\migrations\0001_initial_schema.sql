-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Core Tables for C-AIaaS Governance Engine
CREATE TABLE roles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(50) UNIQUE NOT NULL,
  spend_limit NUMERIC,
  q_score_threshold NUMERIC(3,1) DEFAULT 7.0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE entropy_modifiers (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  role_id UUID NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
  task_type VARCHAR(50) NOT NULL,
  modifier NUMERIC(3,2) NOT NULL CHECK (modifier BETWEEN 0.5 AND 2.0),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(role_id, task_type)
);

CREATE TABLE vendors (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(100) NOT NULL,
  current_q_score NUMERIC(3,1) DEFAULT 5.0 CHECK (current_q_score BETWEEN 0.0 AND 10.0),
  nova_dna_signature TEXT UNIQUE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE tasks (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(255) NOT NULL,
  type VARCHAR(50) NOT NULL,
  budget NUMERIC(10,2) NOT NULL,
  deadline_hours INTEGER NOT NULL,
  vendor_id UUID REFERENCES vendors(id),
  role_id UUID NOT NULL REFERENCES roles(id),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  CONSTRAINT positive_budget CHECK (budget > 0),
  CONSTRAINT positive_deadline CHECK (deadline_hours > 0)
);

CREATE TABLE decisions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  task_id UUID NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
  entropy_value NUMERIC(10,4) NOT NULL,
  threshold NUMERIC(10,4) NOT NULL,
  q_factor NUMERIC(3,2) NOT NULL,
  decision VARCHAR(20) NOT NULL,
  entropy_signature TEXT NOT NULL,
  is_recurring BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  CONSTRAINT valid_decision CHECK (decision IN ('Auto-Approved', 'Requires Approval', 'Escalate'))
);

-- Indexes for performance
CREATE INDEX idx_decisions_signature ON decisions(entropy_signature);
CREATE INDEX idx_decisions_recurring ON decisions(is_recurring) WHERE is_recurring = TRUE;
CREATE INDEX idx_tasks_vendor ON tasks(vendor_id);
CREATE INDEX idx_tasks_role ON tasks(role_id);

-- Row Level Security (RLS) policies will be added after initial setup
-- This ensures we can set up the initial schema without RLS blocking operations

-- Function to update updated_at timestamps
CREATE OR REPLACE FUNCTION update_modified_column() 
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW; 
END;
$$ language 'plpgsql';

-- Triggers for updated_at
CREATE TRIGGER update_roles_modtime
BEFORE UPDATE ON roles
FOR EACH ROW EXECUTE FUNCTION update_modified_column();

CREATE TRIGGER update_vendors_modtime
BEFORE UPDATE ON vendors
FOR EACH ROW EXECUTE FUNCTION update_modified_column();

CREATE TRIGGER update_tasks_modtime
BEFORE UPDATE ON tasks
FOR EACH ROW EXECUTE FUNCTION update_modified_column();

-- Initial data
INSERT INTO roles (name, spend_limit, q_score_threshold) VALUES
('CTO', 10000, 7.5),
('Cadence', 5000, 8.0),
('Team Lead', 2000, 7.0);

-- Default entropy modifiers
INSERT INTO entropy_modifiers (role_id, task_type, modifier)
SELECT r.id, t.task_type, t.modifier
FROM roles r
CROSS JOIN (VALUES 
    ('critical_bugfix', 0.8),
    ('feature_dev', 1.2),
    ('documentation', 0.9),
    ('testing', 1.0),
    ('deployment', 1.1)
) AS t(task_type, modifier)
WHERE r.name IN ('CTO', 'Team Lead');

-- Add default modifier for Cadence role
INSERT INTO entropy_modifiers (role_id, task_type, modifier)
SELECT id, 'default', 1.0
FROM roles WHERE name = 'Cadence';
