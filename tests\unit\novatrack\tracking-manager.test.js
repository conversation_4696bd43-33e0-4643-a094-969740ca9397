/**
 * Unit Tests for NovaTrack TrackingManager
 */

const { describe, it, beforeEach, afterEach, expect } = require('@jest/globals');
const fs = require('fs');
const path = require('path');
const os = require('os');

// Import test environment configuration
const testEnv = require('../../setup/test-environment');

// Import the TrackingManager mock
const { TrackingManager } = require('../../mocks/tracking-manager.mock');

// Import test data generator
const {
  generateRequirement,
  generateActivity,
  generateRequirements,
  generateActivities,
  generateTestDataset
} = require('../../data/novatrack-test-data');

describe('NovaTrack TrackingManager', () => {
  let trackingManager;
  let tempDir;

  beforeEach(() => {
    // Create a temporary directory for test data
    if (testEnv.isDocker) {
      // In Docker, use a fixed path
      tempDir = '/app/tmp/novatrack-test';
      // Ensure the directory exists
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
      }
    } else {
      // Locally, use a temporary directory
      tempDir = fs.mkdtempSync(path.join(os.tmpdir(), 'novatrack-test-'));
    }

    // Initialize the TrackingManager with the temporary directory
    trackingManager = new TrackingManager(tempDir);

    console.log(`Running tests in ${testEnv.isDocker ? 'Docker' : 'local'} environment`);
    console.log(`Using test directory: ${tempDir}`);
  });

  afterEach(() => {
    // Clean up the temporary directory
    if (fs.existsSync(tempDir)) {
      fs.rmSync(tempDir, { recursive: true, force: true });
    }
  });

  describe('create_requirement', () => {
    it('should create a new requirement with valid data', () => {
      // Arrange
      const requirementData = generateRequirement();

      // Act
      const requirement = trackingManager.create_requirement(requirementData);

      // Assert
      expect(requirement).toBeDefined();
      expect(requirement.id).toBeDefined();
      expect(requirement.name).toBe(requirementData.name);
      expect(requirement.description).toBe(requirementData.description);
      expect(requirement.framework).toBe(requirementData.framework);
      expect(requirement.category).toBe(requirementData.category);
      expect(requirement.priority).toBe(requirementData.priority);
      expect(requirement.status).toBe(requirementData.status);
      expect(requirement.due_date).toBe(requirementData.due_date);
      expect(requirement.assigned_to).toBe(requirementData.assigned_to);
      expect(requirement.tags).toEqual(requirementData.tags);
      expect(requirement.created_at).toBeDefined();
      expect(requirement.updated_at).toBeDefined();
    });

    it('should throw an error when creating a requirement with invalid data', () => {
      // Arrange
      const invalidRequirementData = {
        // Missing required name field
        description: 'Implement processes for handling data subject rights requests',
        framework: 'GDPR',
        category: 'privacy',
        priority: 'high',
        status: 'in_progress',
        due_date: '2023-12-31',
        assigned_to: 'privacy_officer',
        tags: ['gdpr', 'data_subject_rights', 'privacy']
      };

      // Act & Assert
      expect(() => {
        trackingManager.create_requirement(invalidRequirementData);
      }).toThrow();
    });

    it('should throw an error when creating a requirement with invalid status', () => {
      // Arrange
      const invalidStatusRequirementData = generateRequirement({
        status: 'invalid_status' // Invalid status
      });

      // Act & Assert
      expect(() => {
        trackingManager.create_requirement(invalidStatusRequirementData);
      }).toThrow();
    });
  });

  describe('create_activity', () => {
    it('should create a new activity with valid data', () => {
      // Arrange
      const requirementData = generateRequirement();
      const requirement = trackingManager.create_requirement(requirementData);

      const activityData = generateActivity({
        requirement_id: requirement.id
      });

      // Act
      const activity = trackingManager.create_activity(activityData);

      // Assert
      expect(activity).toBeDefined();
      expect(activity.id).toBeDefined();
      expect(activity.name).toBe(activityData.name);
      expect(activity.description).toBe(activityData.description);
      expect(activity.requirement_id).toBe(activityData.requirement_id);
      expect(activity.type).toBe(activityData.type);
      expect(activity.status).toBe(activityData.status);
      expect(activity.start_date).toBe(activityData.start_date);
      expect(activity.end_date).toBe(activityData.end_date);
      expect(activity.assigned_to).toBe(activityData.assigned_to);
      expect(activity.notes).toBe(activityData.notes);
      expect(activity.created_at).toBeDefined();
      expect(activity.updated_at).toBeDefined();
    });

    it('should throw an error when creating an activity with invalid data', () => {
      // Arrange
      const invalidActivityData = {
        // Missing required name field
        description: 'Create documentation for handling data subject rights requests',
        requirement_id: 'req-123',
        type: 'documentation',
        status: 'completed',
        start_date: '2023-01-01',
        end_date: '2023-01-15',
        assigned_to: 'privacy_officer',
        notes: 'Documentation completed and reviewed'
      };

      // Act & Assert
      expect(() => {
        trackingManager.create_activity(invalidActivityData);
      }).toThrow();
    });

    it('should throw an error when creating an activity with invalid type', () => {
      // Arrange
      const invalidTypeActivityData = generateActivity({
        type: 'invalid_type' // Invalid type
      });

      // Act & Assert
      expect(() => {
        trackingManager.create_activity(invalidTypeActivityData);
      }).toThrow();
    });
  });

  describe('get_requirement_activities', () => {
    it('should return all activities for a requirement', () => {
      // Arrange
      const requirementData = generateRequirement();
      const requirement = trackingManager.create_requirement(requirementData);

      const activityData1 = generateActivity({
        name: 'Activity 1',
        requirement_id: requirement.id
      });

      const activityData2 = generateActivity({
        name: 'Activity 2',
        requirement_id: requirement.id
      });

      trackingManager.create_activity(activityData1);
      trackingManager.create_activity(activityData2);

      // Act
      const activities = trackingManager.get_requirement_activities(requirement.id);

      // Assert
      expect(activities).toBeDefined();
      expect(activities.length).toBe(2);
      expect(activities[0].name).toBe(activityData1.name);
      expect(activities[1].name).toBe(activityData2.name);
    });

    it('should return an empty array for a requirement with no activities', () => {
      // Arrange
      const requirementData = generateRequirement();

      const requirement = trackingManager.create_requirement(requirementData);

      // Act
      const activities = trackingManager.get_requirement_activities(requirement.id);

      // Assert
      expect(activities).toBeDefined();
      expect(activities.length).toBe(0);
    });
  });

  describe('get_requirements_by_framework', () => {
    it('should return all requirements for a framework', () => {
      // Arrange
      const gdprRequirementData1 = generateRequirement({
        name: 'GDPR Requirement 1',
        framework: 'GDPR'
      });

      const gdprRequirementData2 = generateRequirement({
        name: 'GDPR Requirement 2',
        framework: 'GDPR'
      });

      const soc2RequirementData = generateRequirement({
        name: 'SOC 2 Requirement',
        framework: 'SOC 2'
      });

      trackingManager.create_requirement(gdprRequirementData1);
      trackingManager.create_requirement(gdprRequirementData2);
      trackingManager.create_requirement(soc2RequirementData);

      // Act
      const gdprRequirements = trackingManager.get_requirements_by_framework('GDPR');
      const soc2Requirements = trackingManager.get_requirements_by_framework('SOC 2');

      // Assert
      expect(gdprRequirements).toBeDefined();
      expect(gdprRequirements.length).toBe(2);
      expect(gdprRequirements[0].name).toBe(gdprRequirementData1.name);
      expect(gdprRequirements[1].name).toBe(gdprRequirementData2.name);

      expect(soc2Requirements).toBeDefined();
      expect(soc2Requirements.length).toBe(1);
      expect(soc2Requirements[0].name).toBe(soc2RequirementData.name);
    });

    it('should return an empty array for a framework with no requirements', () => {
      // Arrange
      const gdprRequirementData = generateRequirement({
        framework: 'GDPR'
      });

      trackingManager.create_requirement(gdprRequirementData);

      // Act
      const hipaaRequirements = trackingManager.get_requirements_by_framework('HIPAA');

      // Assert
      expect(hipaaRequirements).toBeDefined();
      expect(hipaaRequirements.length).toBe(0);
    });
  });

  describe('Test data generation', () => {
    it('should work with generated test dataset', () => {
      // Arrange
      const testData = generateTestDataset({
        requirementCount: 3,
        activitiesPerRequirement: 2
      });

      // Act
      // Create requirements
      const createdRequirements = testData.requirements.map(req =>
        trackingManager.create_requirement(req)
      );

      // Create activities
      const createdActivities = testData.activities.map(act =>
        trackingManager.create_activity(act)
      );

      // Assert
      // Check requirements were created
      expect(Object.keys(trackingManager.requirements).length).toBe(3);

      // Check activities were created
      expect(Object.keys(trackingManager.activities).length).toBe(6);

      // Check activities are linked to requirements
      for (const req of createdRequirements) {
        const reqActivities = trackingManager.get_requirement_activities(req.id);
        expect(reqActivities.length).toBe(2);
      }
    });
  });
});
