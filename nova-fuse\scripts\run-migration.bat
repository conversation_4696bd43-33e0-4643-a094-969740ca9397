@echo off
echo NovaFuse Migration Script Runner
echo ==============================
echo.

echo Step 1: Running migration script...
powershell -ExecutionPolicy Bypass -File "%~dp0migrate-code-v2.ps1"
if %ERRORLEVEL% neq 0 (
    echo Migration script failed with error code %ERRORLEVEL%
    exit /b %ERRORLEVEL%
)
echo.

echo Step 2: Verifying migration...
powershell -ExecutionPolicy Bypass -File "%~dp0verify-migration.ps1"
if %ERRORLEVEL% neq 0 (
    echo Verification script failed with error code %ERRORLEVEL%
    exit /b %ERRORLEVEL%
)
echo.

echo Migration completed successfully!
echo Check the log files for details:
echo - Migration log: %~dp0migration-log.txt
echo - Verification log: %~dp0verification-log.txt
echo.

pause
