{"version": 3, "names": ["fs", "require", "promises", "path", "BruteForceError", "BruteForceProtectionService", "constructor", "dataDir", "join", "__dirname", "attemptsFile", "config", "maxAttempts", "windowMs", "blockDuration", "ensureDataDir", "setInterval", "cleanupExpiredAttempts", "mkdir", "recursive", "error", "console", "loadAttempts", "data", "readFile", "JSON", "parse", "code", "saveAttempts", "attempts", "writeFile", "stringify", "recordFailedAttempt", "identifier", "now", "Date", "count", "firstAttempt", "lastAttempt", "blocked", "blockedU<PERSON>l", "resetAttempts", "isBlocked", "remainingTime", "Math", "ceil", "attemptsCount", "checkLoginAttempt", "blockStatus", "handleSuccessfulLogin", "handleFailedLogin", "attempt", "remainingAttempts", "modified", "updateConfig", "getConfig", "module", "exports"], "sources": ["BruteForceProtectionService.js"], "sourcesContent": ["/**\n * Brute Force Protection Service\n * \n * This service handles brute force attack protection.\n */\n\nconst fs = require('fs').promises;\nconst path = require('path');\nconst { BruteForceError } = require('../utils/errors');\n\nclass BruteForceProtectionService {\n  constructor(dataDir = path.join(__dirname, '../data')) {\n    this.dataDir = dataDir;\n    this.attemptsFile = path.join(this.dataDir, 'login_attempts.json');\n    this.config = {\n      maxAttempts: 5, // Maximum number of failed attempts\n      windowMs: 15 * 60 * 1000, // 15 minutes\n      blockDuration: 30 * 60 * 1000, // 30 minutes\n    };\n    this.ensureDataDir();\n    \n    // Clean up expired attempts every 5 minutes\n    setInterval(() => this.cleanupExpiredAttempts(), 5 * 60 * 1000);\n  }\n\n  /**\n   * Ensure the data directory exists\n   */\n  async ensureDataDir() {\n    try {\n      await fs.mkdir(this.dataDir, { recursive: true });\n    } catch (error) {\n      console.error('Error creating data directory:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Load login attempts from file\n   */\n  async loadAttempts() {\n    try {\n      const data = await fs.readFile(this.attemptsFile, 'utf8');\n      return JSON.parse(data);\n    } catch (error) {\n      if (error.code === 'ENOENT') {\n        // File doesn't exist, return empty object\n        return {};\n      }\n      console.error('Error loading login attempts:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Save login attempts to file\n   */\n  async saveAttempts(attempts) {\n    try {\n      await fs.writeFile(this.attemptsFile, JSON.stringify(attempts, null, 2));\n    } catch (error) {\n      console.error('Error saving login attempts:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Record a failed login attempt\n   */\n  async recordFailedAttempt(identifier) {\n    const attempts = await this.loadAttempts();\n    const now = Date.now();\n    \n    // Initialize attempts for this identifier if not exists\n    if (!attempts[identifier]) {\n      attempts[identifier] = {\n        count: 0,\n        firstAttempt: now,\n        lastAttempt: now,\n        blocked: false,\n        blockedUntil: null\n      };\n    }\n    \n    // Update attempts\n    attempts[identifier].count++;\n    attempts[identifier].lastAttempt = now;\n    \n    // Check if account should be blocked\n    if (attempts[identifier].count >= this.config.maxAttempts) {\n      attempts[identifier].blocked = true;\n      attempts[identifier].blockedUntil = now + this.config.blockDuration;\n    }\n    \n    // Save attempts\n    await this.saveAttempts(attempts);\n    \n    return attempts[identifier];\n  }\n\n  /**\n   * Reset login attempts for an identifier\n   */\n  async resetAttempts(identifier) {\n    const attempts = await this.loadAttempts();\n    \n    if (attempts[identifier]) {\n      delete attempts[identifier];\n      await this.saveAttempts(attempts);\n    }\n    \n    return true;\n  }\n\n  /**\n   * Check if an identifier is blocked\n   */\n  async isBlocked(identifier) {\n    const attempts = await this.loadAttempts();\n    const now = Date.now();\n    \n    if (!attempts[identifier]) {\n      return false;\n    }\n    \n    // Check if blocked and block duration has not expired\n    if (attempts[identifier].blocked && attempts[identifier].blockedUntil > now) {\n      const remainingTime = Math.ceil((attempts[identifier].blockedUntil - now) / 1000);\n      return {\n        blocked: true,\n        remainingTime,\n        attemptsCount: attempts[identifier].count,\n        maxAttempts: this.config.maxAttempts\n      };\n    }\n    \n    // If block duration has expired, reset the block\n    if (attempts[identifier].blocked && attempts[identifier].blockedUntil <= now) {\n      attempts[identifier].blocked = false;\n      attempts[identifier].blockedUntil = null;\n      attempts[identifier].count = 0;\n      await this.saveAttempts(attempts);\n    }\n    \n    return false;\n  }\n\n  /**\n   * Check login attempt before processing\n   */\n  async checkLoginAttempt(identifier) {\n    // Check if identifier is blocked\n    const blockStatus = await this.isBlocked(identifier);\n    \n    if (blockStatus && blockStatus.blocked) {\n      throw new BruteForceError(\n        `Too many failed login attempts. Account is temporarily blocked. Try again in ${blockStatus.remainingTime} seconds.`,\n        blockStatus.remainingTime\n      );\n    }\n    \n    return true;\n  }\n\n  /**\n   * Handle successful login\n   */\n  async handleSuccessfulLogin(identifier) {\n    return this.resetAttempts(identifier);\n  }\n\n  /**\n   * Handle failed login\n   */\n  async handleFailedLogin(identifier) {\n    const attempt = await this.recordFailedAttempt(identifier);\n    \n    if (attempt.blocked) {\n      const remainingTime = Math.ceil((attempt.blockedUntil - Date.now()) / 1000);\n      throw new BruteForceError(\n        `Too many failed login attempts. Account is temporarily blocked. Try again in ${remainingTime} seconds.`,\n        remainingTime\n      );\n    }\n    \n    return {\n      attemptsCount: attempt.count,\n      maxAttempts: this.config.maxAttempts,\n      remainingAttempts: this.config.maxAttempts - attempt.count\n    };\n  }\n\n  /**\n   * Clean up expired attempts\n   */\n  async cleanupExpiredAttempts() {\n    try {\n      const attempts = await this.loadAttempts();\n      const now = Date.now();\n      let modified = false;\n      \n      for (const identifier in attempts) {\n        // Remove attempts that are older than the window\n        if (now - attempts[identifier].lastAttempt > this.config.windowMs && !attempts[identifier].blocked) {\n          delete attempts[identifier];\n          modified = true;\n        }\n        \n        // Reset blocks that have expired\n        if (attempts[identifier] && attempts[identifier].blocked && attempts[identifier].blockedUntil <= now) {\n          attempts[identifier].blocked = false;\n          attempts[identifier].blockedUntil = null;\n          attempts[identifier].count = 0;\n          modified = true;\n        }\n      }\n      \n      if (modified) {\n        await this.saveAttempts(attempts);\n      }\n    } catch (error) {\n      console.error('Error cleaning up expired attempts:', error);\n    }\n  }\n\n  /**\n   * Update brute force protection configuration\n   */\n  async updateConfig(config) {\n    this.config = {\n      ...this.config,\n      ...config\n    };\n    \n    return this.config;\n  }\n\n  /**\n   * Get brute force protection configuration\n   */\n  getConfig() {\n    return this.config;\n  }\n}\n\nmodule.exports = BruteForceProtectionService;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,MAAMA,EAAE,GAAGC,OAAO,CAAC,IAAI,CAAC,CAACC,QAAQ;AACjC,MAAMC,IAAI,GAAGF,OAAO,CAAC,MAAM,CAAC;AAC5B,MAAM;EAAEG;AAAgB,CAAC,GAAGH,OAAO,CAAC,iBAAiB,CAAC;AAEtD,MAAMI,2BAA2B,CAAC;EAChCC,WAAWA,CAACC,OAAO,GAAGJ,IAAI,CAACK,IAAI,CAACC,SAAS,EAAE,SAAS,CAAC,EAAE;IACrD,IAAI,CAACF,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACG,YAAY,GAAGP,IAAI,CAACK,IAAI,CAAC,IAAI,CAACD,OAAO,EAAE,qBAAqB,CAAC;IAClE,IAAI,CAACI,MAAM,GAAG;MACZC,WAAW,EAAE,CAAC;MAAE;MAChBC,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;MAAE;MAC1BC,aAAa,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,CAAE;IACjC,CAAC;IACD,IAAI,CAACC,aAAa,CAAC,CAAC;;IAEpB;IACAC,WAAW,CAAC,MAAM,IAAI,CAACC,sBAAsB,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;EACjE;;EAEA;AACF;AACA;EACE,MAAMF,aAAaA,CAAA,EAAG;IACpB,IAAI;MACF,MAAMf,EAAE,CAACkB,KAAK,CAAC,IAAI,CAACX,OAAO,EAAE;QAAEY,SAAS,EAAE;MAAK,CAAC,CAAC;IACnD,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAME,YAAYA,CAAA,EAAG;IACnB,IAAI;MACF,MAAMC,IAAI,GAAG,MAAMvB,EAAE,CAACwB,QAAQ,CAAC,IAAI,CAACd,YAAY,EAAE,MAAM,CAAC;MACzD,OAAOe,IAAI,CAACC,KAAK,CAACH,IAAI,CAAC;IACzB,CAAC,CAAC,OAAOH,KAAK,EAAE;MACd,IAAIA,KAAK,CAACO,IAAI,KAAK,QAAQ,EAAE;QAC3B;QACA,OAAO,CAAC,CAAC;MACX;MACAN,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMQ,YAAYA,CAACC,QAAQ,EAAE;IAC3B,IAAI;MACF,MAAM7B,EAAE,CAAC8B,SAAS,CAAC,IAAI,CAACpB,YAAY,EAAEe,IAAI,CAACM,SAAS,CAACF,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IAC1E,CAAC,CAAC,OAAOT,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMY,mBAAmBA,CAACC,UAAU,EAAE;IACpC,MAAMJ,QAAQ,GAAG,MAAM,IAAI,CAACP,YAAY,CAAC,CAAC;IAC1C,MAAMY,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;;IAEtB;IACA,IAAI,CAACL,QAAQ,CAACI,UAAU,CAAC,EAAE;MACzBJ,QAAQ,CAACI,UAAU,CAAC,GAAG;QACrBG,KAAK,EAAE,CAAC;QACRC,YAAY,EAAEH,GAAG;QACjBI,WAAW,EAAEJ,GAAG;QAChBK,OAAO,EAAE,KAAK;QACdC,YAAY,EAAE;MAChB,CAAC;IACH;;IAEA;IACAX,QAAQ,CAACI,UAAU,CAAC,CAACG,KAAK,EAAE;IAC5BP,QAAQ,CAACI,UAAU,CAAC,CAACK,WAAW,GAAGJ,GAAG;;IAEtC;IACA,IAAIL,QAAQ,CAACI,UAAU,CAAC,CAACG,KAAK,IAAI,IAAI,CAACzB,MAAM,CAACC,WAAW,EAAE;MACzDiB,QAAQ,CAACI,UAAU,CAAC,CAACM,OAAO,GAAG,IAAI;MACnCV,QAAQ,CAACI,UAAU,CAAC,CAACO,YAAY,GAAGN,GAAG,GAAG,IAAI,CAACvB,MAAM,CAACG,aAAa;IACrE;;IAEA;IACA,MAAM,IAAI,CAACc,YAAY,CAACC,QAAQ,CAAC;IAEjC,OAAOA,QAAQ,CAACI,UAAU,CAAC;EAC7B;;EAEA;AACF;AACA;EACE,MAAMQ,aAAaA,CAACR,UAAU,EAAE;IAC9B,MAAMJ,QAAQ,GAAG,MAAM,IAAI,CAACP,YAAY,CAAC,CAAC;IAE1C,IAAIO,QAAQ,CAACI,UAAU,CAAC,EAAE;MACxB,OAAOJ,QAAQ,CAACI,UAAU,CAAC;MAC3B,MAAM,IAAI,CAACL,YAAY,CAACC,QAAQ,CAAC;IACnC;IAEA,OAAO,IAAI;EACb;;EAEA;AACF;AACA;EACE,MAAMa,SAASA,CAACT,UAAU,EAAE;IAC1B,MAAMJ,QAAQ,GAAG,MAAM,IAAI,CAACP,YAAY,CAAC,CAAC;IAC1C,MAAMY,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;IAEtB,IAAI,CAACL,QAAQ,CAACI,UAAU,CAAC,EAAE;MACzB,OAAO,KAAK;IACd;;IAEA;IACA,IAAIJ,QAAQ,CAACI,UAAU,CAAC,CAACM,OAAO,IAAIV,QAAQ,CAACI,UAAU,CAAC,CAACO,YAAY,GAAGN,GAAG,EAAE;MAC3E,MAAMS,aAAa,GAAGC,IAAI,CAACC,IAAI,CAAC,CAAChB,QAAQ,CAACI,UAAU,CAAC,CAACO,YAAY,GAAGN,GAAG,IAAI,IAAI,CAAC;MACjF,OAAO;QACLK,OAAO,EAAE,IAAI;QACbI,aAAa;QACbG,aAAa,EAAEjB,QAAQ,CAACI,UAAU,CAAC,CAACG,KAAK;QACzCxB,WAAW,EAAE,IAAI,CAACD,MAAM,CAACC;MAC3B,CAAC;IACH;;IAEA;IACA,IAAIiB,QAAQ,CAACI,UAAU,CAAC,CAACM,OAAO,IAAIV,QAAQ,CAACI,UAAU,CAAC,CAACO,YAAY,IAAIN,GAAG,EAAE;MAC5EL,QAAQ,CAACI,UAAU,CAAC,CAACM,OAAO,GAAG,KAAK;MACpCV,QAAQ,CAACI,UAAU,CAAC,CAACO,YAAY,GAAG,IAAI;MACxCX,QAAQ,CAACI,UAAU,CAAC,CAACG,KAAK,GAAG,CAAC;MAC9B,MAAM,IAAI,CAACR,YAAY,CAACC,QAAQ,CAAC;IACnC;IAEA,OAAO,KAAK;EACd;;EAEA;AACF;AACA;EACE,MAAMkB,iBAAiBA,CAACd,UAAU,EAAE;IAClC;IACA,MAAMe,WAAW,GAAG,MAAM,IAAI,CAACN,SAAS,CAACT,UAAU,CAAC;IAEpD,IAAIe,WAAW,IAAIA,WAAW,CAACT,OAAO,EAAE;MACtC,MAAM,IAAInC,eAAe,CACvB,gFAAgF4C,WAAW,CAACL,aAAa,WAAW,EACpHK,WAAW,CAACL,aACd,CAAC;IACH;IAEA,OAAO,IAAI;EACb;;EAEA;AACF;AACA;EACE,MAAMM,qBAAqBA,CAAChB,UAAU,EAAE;IACtC,OAAO,IAAI,CAACQ,aAAa,CAACR,UAAU,CAAC;EACvC;;EAEA;AACF;AACA;EACE,MAAMiB,iBAAiBA,CAACjB,UAAU,EAAE;IAClC,MAAMkB,OAAO,GAAG,MAAM,IAAI,CAACnB,mBAAmB,CAACC,UAAU,CAAC;IAE1D,IAAIkB,OAAO,CAACZ,OAAO,EAAE;MACnB,MAAMI,aAAa,GAAGC,IAAI,CAACC,IAAI,CAAC,CAACM,OAAO,CAACX,YAAY,GAAGL,IAAI,CAACD,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC;MAC3E,MAAM,IAAI9B,eAAe,CACvB,gFAAgFuC,aAAa,WAAW,EACxGA,aACF,CAAC;IACH;IAEA,OAAO;MACLG,aAAa,EAAEK,OAAO,CAACf,KAAK;MAC5BxB,WAAW,EAAE,IAAI,CAACD,MAAM,CAACC,WAAW;MACpCwC,iBAAiB,EAAE,IAAI,CAACzC,MAAM,CAACC,WAAW,GAAGuC,OAAO,CAACf;IACvD,CAAC;EACH;;EAEA;AACF;AACA;EACE,MAAMnB,sBAAsBA,CAAA,EAAG;IAC7B,IAAI;MACF,MAAMY,QAAQ,GAAG,MAAM,IAAI,CAACP,YAAY,CAAC,CAAC;MAC1C,MAAMY,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;MACtB,IAAImB,QAAQ,GAAG,KAAK;MAEpB,KAAK,MAAMpB,UAAU,IAAIJ,QAAQ,EAAE;QACjC;QACA,IAAIK,GAAG,GAAGL,QAAQ,CAACI,UAAU,CAAC,CAACK,WAAW,GAAG,IAAI,CAAC3B,MAAM,CAACE,QAAQ,IAAI,CAACgB,QAAQ,CAACI,UAAU,CAAC,CAACM,OAAO,EAAE;UAClG,OAAOV,QAAQ,CAACI,UAAU,CAAC;UAC3BoB,QAAQ,GAAG,IAAI;QACjB;;QAEA;QACA,IAAIxB,QAAQ,CAACI,UAAU,CAAC,IAAIJ,QAAQ,CAACI,UAAU,CAAC,CAACM,OAAO,IAAIV,QAAQ,CAACI,UAAU,CAAC,CAACO,YAAY,IAAIN,GAAG,EAAE;UACpGL,QAAQ,CAACI,UAAU,CAAC,CAACM,OAAO,GAAG,KAAK;UACpCV,QAAQ,CAACI,UAAU,CAAC,CAACO,YAAY,GAAG,IAAI;UACxCX,QAAQ,CAACI,UAAU,CAAC,CAACG,KAAK,GAAG,CAAC;UAC9BiB,QAAQ,GAAG,IAAI;QACjB;MACF;MAEA,IAAIA,QAAQ,EAAE;QACZ,MAAM,IAAI,CAACzB,YAAY,CAACC,QAAQ,CAAC;MACnC;IACF,CAAC,CAAC,OAAOT,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;IAC7D;EACF;;EAEA;AACF;AACA;EACE,MAAMkC,YAAYA,CAAC3C,MAAM,EAAE;IACzB,IAAI,CAACA,MAAM,GAAG;MACZ,GAAG,IAAI,CAACA,MAAM;MACd,GAAGA;IACL,CAAC;IAED,OAAO,IAAI,CAACA,MAAM;EACpB;;EAEA;AACF;AACA;EACE4C,SAASA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC5C,MAAM;EACpB;AACF;AAEA6C,MAAM,CAACC,OAAO,GAAGpD,2BAA2B", "ignoreList": []}