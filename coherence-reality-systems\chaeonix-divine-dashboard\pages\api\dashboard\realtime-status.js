/**
 * REAL-TIME DASHBOARD STATUS API
 * Provides comprehensive dashboard data for real-time updates
 * Combines all critical metrics in a single endpoint
 */

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Get current timestamp
    const timestamp = new Date().toISOString();
    const currentHour = new Date().getHours();
    const currentMinute = new Date().getMinutes();

    // Simulate real-time trading activity
    const baseProfit = 117.62;
    const timeVariation = Math.sin(Date.now() / 10000) * 50; // Oscillating profit
    const currentProfit = baseProfit + timeVariation;

    // Simulate market coherence based on time
    const timeOfDay = currentHour + currentMinute / 60;
    const marketCoherence = {
      stocks: 0.68 + 0.15 * Math.sin(timeOfDay / 24 * Math.PI * 2),
      crypto: 0.72 + 0.12 * Math.cos(timeOfDay / 24 * Math.PI * 2 + 1),
      forex: 0.55 + 0.20 * Math.sin(timeOfDay / 24 * Math.PI * 2 + 2)
    };

    // Calculate average coherence
    const avgCoherence = (marketCoherence.stocks + marketCoherence.crypto + marketCoherence.forex) / 3;

    // Engine status based on coherence
    const engineStatus = {
      NEPI: { 
        status: "ONLINE", 
        confidence: Math.min(100, 95 + avgCoherence * 5), 
        color: "green" 
      },
      NEFC: { 
        status: "ONLINE", 
        confidence: Math.min(100, 90 + avgCoherence * 8), 
        color: "green" 
      },
      NERS: { 
        status: "ONLINE", 
        confidence: Math.min(100, 88 + avgCoherence * 10), 
        color: "green" 
      },
      NERE: { 
        status: "ONLINE", 
        confidence: Math.min(100, 92 + avgCoherence * 6), 
        color: "green" 
      },
      NECE: { 
        status: avgCoherence > 0.7 ? "ONLINE" : "WARMING", 
        confidence: Math.min(100, 85 + avgCoherence * 12), 
        color: avgCoherence > 0.7 ? "green" : "yellow" 
      },
      NECO: { 
        status: "ONLINE", 
        confidence: Math.min(100, 90 + avgCoherence * 7), 
        color: "green" 
      },
      NEBE: { 
        status: marketCoherence.crypto < 0.6 ? "CRITICAL" : "WARMING", 
        confidence: Math.max(30, 45 + marketCoherence.crypto * 40), 
        color: marketCoherence.crypto < 0.6 ? "red" : "yellow" 
      },
      NEEE: { 
        status: avgCoherence > 0.75 ? "ONLINE" : "WARMING", 
        confidence: Math.min(100, 78 + avgCoherence * 15), 
        color: avgCoherence > 0.75 ? "green" : "yellow" 
      },
      NEPE: { 
        status: avgCoherence > 0.8 ? "ONLINE" : "WARMING", 
        confidence: Math.min(100, 78 + avgCoherence * 20), 
        color: avgCoherence > 0.8 ? "green" : "yellow" 
      }
    };

    // Calculate hourly target progress
    const minutesIntoHour = currentMinute;
    const expectedProgress = (minutesIntoHour / 60) * 650; // $650 target
    const actualProgress = Math.max(0, currentProfit - 50); // Simulate progress
    const progressPercentage = Math.min(100, (actualProgress / 650) * 100);

    // Trading activity simulation
    const tradesThisHour = Math.floor(minutesIntoHour / 10) + Math.floor(Math.random() * 3);
    const avgPerTrade = tradesThisHour > 0 ? actualProgress / tradesThisHour : 0;

    // Market allocation based on time and coherence
    const forexWeight = 0.82 + (marketCoherence.forex - 0.6) * 0.1;
    const stocksWeight = 0.09 + (marketCoherence.stocks - 0.6) * 0.05;
    const cryptoWeight = 1 - forexWeight - stocksWeight;

    const response = {
      success: true,
      timestamp,
      
      // Real-time metrics
      current_profit: Math.round(currentProfit * 100) / 100,
      hourly_progress: {
        target: 650,
        current: Math.round(actualProgress * 100) / 100,
        percentage: Math.round(progressPercentage * 100) / 100,
        needed: Math.max(0, Math.round((650 - actualProgress) * 100) / 100),
        minutes_left: 60 - minutesIntoHour
      },
      
      // Market coherence
      market_coherence: {
        stocks: Math.round(marketCoherence.stocks * 1000) / 1000,
        crypto: Math.round(marketCoherence.crypto * 1000) / 1000,
        forex: Math.round(marketCoherence.forex * 1000) / 1000,
        average: Math.round(avgCoherence * 1000) / 1000
      },
      
      // Engine status
      engine_status: engineStatus,
      engines_online: Object.values(engineStatus).filter(e => e.status === "ONLINE").length,
      total_engines: Object.keys(engineStatus).length,
      
      // Trading activity
      trading_activity: {
        trades_this_hour: tradesThisHour,
        avg_per_trade: Math.round(avgPerTrade * 100) / 100,
        projected_hourly: Math.round((actualProgress / Math.max(1, minutesIntoHour)) * 60 * 100) / 100
      },
      
      // Market allocation
      market_allocation: {
        forex: Math.round(forexWeight * 1000) / 10, // Convert to percentage
        stocks: Math.round(stocksWeight * 1000) / 10,
        crypto: Math.round(cryptoWeight * 1000) / 10
      },
      
      // Divine metrics
      divine_metrics: {
        coherence_level: Math.round(avgCoherence * 1000) / 10, // Percentage
        phi_alignment: Math.round(1.618 * avgCoherence * 1000) / 1000,
        trinity_score: Math.round(avgCoherence * 1000) / 10,
        engine_health: Math.round((Object.values(engineStatus).filter(e => e.status === "ONLINE").length / Object.keys(engineStatus).length) * 1000) / 10
      },
      
      // System status
      system_status: {
        phase: avgCoherence > 0.8 ? "AMPLIFICATION" : avgCoherence > 0.6 ? "DECISION" : avgCoherence > 0.4 ? "INJECTION" : "DETECTION",
        urgency: progressPercentage < 50 && minutesIntoHour > 30 ? "HIGH" : progressPercentage < 25 && minutesIntoHour > 45 ? "CRITICAL" : "LOW",
        zero_loss_active: true,
        phi_protected: true
      }
    };

    res.status(200).json(response);

  } catch (error) {
    console.error('Real-time status error:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Failed to get real-time status',
      details: error.message 
    });
  }
}
