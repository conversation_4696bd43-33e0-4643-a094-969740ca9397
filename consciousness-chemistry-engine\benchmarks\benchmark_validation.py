"""
Performance benchmarking for the ConsciousNovaFold validation pipeline.

This script measures the performance of various components of the validation
pipeline with different input sizes and characteristics.
"""

import time
import random
import statistics
import tempfile
import os
import argparse
from typing import Dict, List, Tuple

# Import the modules to be benchmarked
from src.conscious_novafold import ConsciousNovaFold
from src.novafold_client import NovaFoldClient

# Amino acid alphabet for random sequence generation
AMINO_ACIDS = 'ACDEFGHIKLMNPQRSTVWY'

def generate_random_sequence(length: int) -> str:
    """Generate a random protein sequence of given length."""
    return ''.join(random.choices(AMINO_ACIDS, k=length))

def benchmark_validation(sequence: str, num_runs: int = 5) -> Dict[str, float]:
    """Benchmark the validation pipeline for a given sequence."""
    times = []
    
    # Set up folder with temporary directory
    with tempfile.TemporaryDirectory() as temp_dir:
        novafold = NovaFoldClient()
        folder = ConsciousNovaFold(
            novafold,
            output_dir=temp_dir,
            enable_caching=False
        )
        
        # Warm-up run
        _ = folder.fold(sequence)
        
        # Benchmark runs
        for _ in range(num_runs):
            start_time = time.perf_counter()
            result = folder.fold(sequence)
            end_time = time.perf_counter()
            times.append(end_time - start_time)
    
    # Calculate statistics
    return {
        'sequence_length': len(sequence),
        'min_time': min(times),
        'max_time': max(times),
        'avg_time': statistics.mean(times),
        'median_time': statistics.median(times),
        'stdev': statistics.stdev(times) if len(times) > 1 else 0.0,
        'num_runs': num_runs
    }

def run_benchmarks(sequence_lengths: List[int], num_runs: int = 5) -> List[Dict]:
    """Run benchmarks for sequences of different lengths."""
    results = []
    
    for length in sequence_lengths:
        print(f"\nBenchmarking sequence length: {length} AA")
        sequence = generate_random_sequence(length)
        
        try:
            metrics = benchmark_validation(sequence, num_runs)
            results.append(metrics)
            
            # Print summary for this length
            print(f"  Average time: {metrics['avg_time']:.4f} ± {metrics['stdev']:.4f} sec")
            print(f"  Min/Max: {metrics['min_time']:.4f}/{metrics['max_time']:.4f} sec")
            
        except Exception as e:
            print(f"  Error benchmarking sequence length {length}: {str(e)}")
    
    return results

def print_benchmark_summary(results: List[Dict]):
    """Print a summary of benchmark results."""
    print("\n" + "="*80)
    print("Benchmark Summary")
    print("="*80)
    print(f"{'Length (AA)':<12} {'Avg Time (s)':<15} {'Stdev':<10} {'Min/Max (s)'}")
    print("-"*80)
    
    for result in results:
        print(f"{result['sequence_length']:<12} {result['avg_time']:<15.4f} "
              f"{result['stdev']:<10.4f} {result['min_time']:.4f}/{result['max_time']:.4f}")

def main():
    parser = argparse.ArgumentParser(description='Benchmark the validation pipeline.')
    parser.add_argument('--min-length', type=int, default=10, help='Minimum sequence length')
    parser.add_argument('--max-length', type=int, default=1000, help='Maximum sequence length')
    parser.add_argument('--step', type=int, default=100, help='Step size for sequence lengths')
    parser.add_argument('--runs', type=int, default=5, help='Number of runs per sequence length')
    
    args = parser.parse_args()
    
    # Generate sequence lengths to test
    sequence_lengths = list(range(
        args.min_length,
        args.min(args.max_length, 5000) + 1,  # Cap at 5000 for safety
        args.step
    ))
    
    print(f"Starting benchmarks for sequence lengths: {sequence_lengths}")
    print(f"Running {args.runs} iterations per length\n")
    
    results = run_benchmarks(sequence_lengths, args.runs)
    
    if results:
        print_benchmark_summary(results)
        
        # Save results to CSV
        import csv
        import datetime
        
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        csv_file = f"validation_benchmark_{timestamp}.csv"
        
        with open(csv_file, 'w', newline='') as f:
            writer = csv.DictWriter(f, fieldnames=results[0].keys())
            writer.writeheader()
            writer.writerows(results)
            
        print(f"\nResults saved to {csv_file}")

if __name__ == "__main__":
    main()
