72cd48ae818dce98486803135de7f5e4
/**
 * NovaFuse Universal API Connector - Connector Error
 * 
 * This module defines connector-related errors for the UAC.
 */

const UAConnectorError = require('./base-error');

/**
 * Error class for connector failures
 * @class ConnectorError
 * @extends UAConnectorError
 */
class ConnectorError extends UAConnectorError {
  /**
   * Create a new ConnectorError
   * 
   * @param {string} message - Error message
   * @param {Object} options - Error options
   * @param {string} options.code - Error code
   * @param {string} options.severity - Error severity
   * @param {Object} options.context - Additional context for the error
   * @param {Error} options.cause - The error that caused this error
   * @param {string} options.connectorId - ID of the connector
   */
  constructor(message, options = {}) {
    super(message, {
      code: options.code || 'CONNECTOR_ERROR',
      severity: options.severity || 'error',
      context: options.context || {},
      cause: options.cause
    });
    this.connectorId = options.connectorId;
  }

  /**
   * Get a user-friendly error message
   * 
   * @returns {string} - User-friendly error message
   */
  getUserMessage() {
    return 'An error occurred with the connector. Please try again later or contact support if the issue persists.';
  }

  /**
   * Convert the error to a JSON object
   * 
   * @param {boolean} includeStack - Whether to include the stack trace
   * @returns {Object} - JSON representation of the error
   */
  toJSON(includeStack = false) {
    const json = super.toJSON(includeStack);
    if (this.connectorId) {
      json.connectorId = this.connectorId;
    }
    return json;
  }
}

/**
 * Error class for connector not found errors
 * @class ConnectorNotFoundError
 * @extends ConnectorError
 */
class ConnectorNotFoundError extends ConnectorError {
  /**
   * Create a new ConnectorNotFoundError
   * 
   * @param {string} connectorId - ID of the connector
   * @param {Object} options - Error options
   */
  constructor(connectorId, options = {}) {
    const message = `Connector not found: ${connectorId}`;
    super(message, {
      code: options.code || 'CONNECTOR_NOT_FOUND',
      severity: options.severity || 'error',
      context: {
        ...options.context,
        connectorId
      },
      cause: options.cause,
      connectorId
    });
  }

  /**
   * Get a user-friendly error message
   * 
   * @returns {string} - User-friendly error message
   */
  getUserMessage() {
    return `The connector "${this.connectorId}" was not found. Please check the connector ID and try again.`;
  }
}

/**
 * Error class for connector configuration errors
 * @class ConnectorConfigurationError
 * @extends ConnectorError
 */
class ConnectorConfigurationError extends ConnectorError {
  /**
   * Create a new ConnectorConfigurationError
   * 
   * @param {string} connectorId - ID of the connector
   * @param {string} message - Error message
   * @param {Object} options - Error options
   */
  constructor(connectorId, message = 'Invalid connector configuration', options = {}) {
    super(message, {
      code: options.code || 'CONNECTOR_CONFIGURATION_ERROR',
      severity: options.severity || 'error',
      context: {
        ...options.context,
        connectorId
      },
      cause: options.cause,
      connectorId
    });
    this.configErrors = options.configErrors || [];
  }

  /**
   * Get a user-friendly error message
   * 
   * @returns {string} - User-friendly error message
   */
  getUserMessage() {
    if (this.configErrors.length > 0) {
      const errorMessages = this.configErrors.map(err => err.message).join('; ');
      return `The connector configuration is invalid: ${errorMessages}`;
    }
    return `The connector configuration is invalid. Please check your configuration and try again.`;
  }

  /**
   * Convert the error to a JSON object
   * 
   * @param {boolean} includeStack - Whether to include the stack trace
   * @returns {Object} - JSON representation of the error
   */
  toJSON(includeStack = false) {
    const json = super.toJSON(includeStack);
    json.configErrors = this.configErrors;
    return json;
  }
}

/**
 * Error class for connector execution errors
 * @class ConnectorExecutionError
 * @extends ConnectorError
 */
class ConnectorExecutionError extends ConnectorError {
  /**
   * Create a new ConnectorExecutionError
   * 
   * @param {string} connectorId - ID of the connector
   * @param {string} operationId - ID of the operation
   * @param {string} message - Error message
   * @param {Object} options - Error options
   */
  constructor(connectorId, operationId, message = 'Error executing connector operation', options = {}) {
    super(message, {
      code: options.code || 'CONNECTOR_EXECUTION_ERROR',
      severity: options.severity || 'error',
      context: {
        ...options.context,
        connectorId,
        operationId
      },
      cause: options.cause,
      connectorId
    });
    this.operationId = operationId;
  }

  /**
   * Get a user-friendly error message
   * 
   * @returns {string} - User-friendly error message
   */
  getUserMessage() {
    return `An error occurred while executing the connector operation. Please try again later or contact support if the issue persists.`;
  }

  /**
   * Convert the error to a JSON object
   * 
   * @param {boolean} includeStack - Whether to include the stack trace
   * @returns {Object} - JSON representation of the error
   */
  toJSON(includeStack = false) {
    const json = super.toJSON(includeStack);
    json.operationId = this.operationId;
    return json;
  }
}

/**
 * Error class for connector version incompatibility errors
 * @class ConnectorVersionError
 * @extends ConnectorError
 */
class ConnectorVersionError extends ConnectorError {
  /**
   * Create a new ConnectorVersionError
   * 
   * @param {string} connectorId - ID of the connector
   * @param {string} currentVersion - Current version
   * @param {string} requiredVersion - Required version
   * @param {Object} options - Error options
   */
  constructor(connectorId, currentVersion, requiredVersion, options = {}) {
    const message = `Connector version incompatible: ${connectorId} (current: ${currentVersion}, required: ${requiredVersion})`;
    super(message, {
      code: options.code || 'CONNECTOR_VERSION_ERROR',
      severity: options.severity || 'error',
      context: {
        ...options.context,
        connectorId,
        currentVersion,
        requiredVersion
      },
      cause: options.cause,
      connectorId
    });
    this.currentVersion = currentVersion;
    this.requiredVersion = requiredVersion;
  }

  /**
   * Get a user-friendly error message
   * 
   * @returns {string} - User-friendly error message
   */
  getUserMessage() {
    return `The connector version is incompatible. Please update the connector to version ${this.requiredVersion} or later.`;
  }

  /**
   * Convert the error to a JSON object
   * 
   * @param {boolean} includeStack - Whether to include the stack trace
   * @returns {Object} - JSON representation of the error
   */
  toJSON(includeStack = false) {
    const json = super.toJSON(includeStack);
    json.currentVersion = this.currentVersion;
    json.requiredVersion = this.requiredVersion;
    return json;
  }
}
module.exports = {
  ConnectorError,
  ConnectorNotFoundError,
  ConnectorConfigurationError,
  ConnectorExecutionError,
  ConnectorVersionError
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJVQUNvbm5lY3RvckVycm9yIiwicmVxdWlyZSIsIkNvbm5lY3RvckVycm9yIiwiY29uc3RydWN0b3IiLCJtZXNzYWdlIiwib3B0aW9ucyIsImNvZGUiLCJzZXZlcml0eSIsImNvbnRleHQiLCJjYXVzZSIsImNvbm5lY3RvcklkIiwiZ2V0VXNlck1lc3NhZ2UiLCJ0b0pTT04iLCJpbmNsdWRlU3RhY2siLCJqc29uIiwiQ29ubmVjdG9yTm90Rm91bmRFcnJvciIsIkNvbm5lY3RvckNvbmZpZ3VyYXRpb25FcnJvciIsImNvbmZpZ0Vycm9ycyIsImxlbmd0aCIsImVycm9yTWVzc2FnZXMiLCJtYXAiLCJlcnIiLCJqb2luIiwiQ29ubmVjdG9yRXhlY3V0aW9uRXJyb3IiLCJvcGVyYXRpb25JZCIsIkNvbm5lY3RvclZlcnNpb25FcnJvciIsImN1cnJlbnRWZXJzaW9uIiwicmVxdWlyZWRWZXJzaW9uIiwibW9kdWxlIiwiZXhwb3J0cyJdLCJzb3VyY2VzIjpbImNvbm5lY3Rvci1lcnJvci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIE5vdmFGdXNlIFVuaXZlcnNhbCBBUEkgQ29ubmVjdG9yIC0gQ29ubmVjdG9yIEVycm9yXG4gKiBcbiAqIFRoaXMgbW9kdWxlIGRlZmluZXMgY29ubmVjdG9yLXJlbGF0ZWQgZXJyb3JzIGZvciB0aGUgVUFDLlxuICovXG5cbmNvbnN0IFVBQ29ubmVjdG9yRXJyb3IgPSByZXF1aXJlKCcuL2Jhc2UtZXJyb3InKTtcblxuLyoqXG4gKiBFcnJvciBjbGFzcyBmb3IgY29ubmVjdG9yIGZhaWx1cmVzXG4gKiBAY2xhc3MgQ29ubmVjdG9yRXJyb3JcbiAqIEBleHRlbmRzIFVBQ29ubmVjdG9yRXJyb3JcbiAqL1xuY2xhc3MgQ29ubmVjdG9yRXJyb3IgZXh0ZW5kcyBVQUNvbm5lY3RvckVycm9yIHtcbiAgLyoqXG4gICAqIENyZWF0ZSBhIG5ldyBDb25uZWN0b3JFcnJvclxuICAgKiBcbiAgICogQHBhcmFtIHtzdHJpbmd9IG1lc3NhZ2UgLSBFcnJvciBtZXNzYWdlXG4gICAqIEBwYXJhbSB7T2JqZWN0fSBvcHRpb25zIC0gRXJyb3Igb3B0aW9uc1xuICAgKiBAcGFyYW0ge3N0cmluZ30gb3B0aW9ucy5jb2RlIC0gRXJyb3IgY29kZVxuICAgKiBAcGFyYW0ge3N0cmluZ30gb3B0aW9ucy5zZXZlcml0eSAtIEVycm9yIHNldmVyaXR5XG4gICAqIEBwYXJhbSB7T2JqZWN0fSBvcHRpb25zLmNvbnRleHQgLSBBZGRpdGlvbmFsIGNvbnRleHQgZm9yIHRoZSBlcnJvclxuICAgKiBAcGFyYW0ge0Vycm9yfSBvcHRpb25zLmNhdXNlIC0gVGhlIGVycm9yIHRoYXQgY2F1c2VkIHRoaXMgZXJyb3JcbiAgICogQHBhcmFtIHtzdHJpbmd9IG9wdGlvbnMuY29ubmVjdG9ySWQgLSBJRCBvZiB0aGUgY29ubmVjdG9yXG4gICAqL1xuICBjb25zdHJ1Y3RvcihtZXNzYWdlLCBvcHRpb25zID0ge30pIHtcbiAgICBzdXBlcihtZXNzYWdlLCB7XG4gICAgICBjb2RlOiBvcHRpb25zLmNvZGUgfHwgJ0NPTk5FQ1RPUl9FUlJPUicsXG4gICAgICBzZXZlcml0eTogb3B0aW9ucy5zZXZlcml0eSB8fCAnZXJyb3InLFxuICAgICAgY29udGV4dDogb3B0aW9ucy5jb250ZXh0IHx8IHt9LFxuICAgICAgY2F1c2U6IG9wdGlvbnMuY2F1c2VcbiAgICB9KTtcbiAgICBcbiAgICB0aGlzLmNvbm5lY3RvcklkID0gb3B0aW9ucy5jb25uZWN0b3JJZDtcbiAgfVxuXG4gIC8qKlxuICAgKiBHZXQgYSB1c2VyLWZyaWVuZGx5IGVycm9yIG1lc3NhZ2VcbiAgICogXG4gICAqIEByZXR1cm5zIHtzdHJpbmd9IC0gVXNlci1mcmllbmRseSBlcnJvciBtZXNzYWdlXG4gICAqL1xuICBnZXRVc2VyTWVzc2FnZSgpIHtcbiAgICByZXR1cm4gJ0FuIGVycm9yIG9jY3VycmVkIHdpdGggdGhlIGNvbm5lY3Rvci4gUGxlYXNlIHRyeSBhZ2FpbiBsYXRlciBvciBjb250YWN0IHN1cHBvcnQgaWYgdGhlIGlzc3VlIHBlcnNpc3RzLic7XG4gIH1cblxuICAvKipcbiAgICogQ29udmVydCB0aGUgZXJyb3IgdG8gYSBKU09OIG9iamVjdFxuICAgKiBcbiAgICogQHBhcmFtIHtib29sZWFufSBpbmNsdWRlU3RhY2sgLSBXaGV0aGVyIHRvIGluY2x1ZGUgdGhlIHN0YWNrIHRyYWNlXG4gICAqIEByZXR1cm5zIHtPYmplY3R9IC0gSlNPTiByZXByZXNlbnRhdGlvbiBvZiB0aGUgZXJyb3JcbiAgICovXG4gIHRvSlNPTihpbmNsdWRlU3RhY2sgPSBmYWxzZSkge1xuICAgIGNvbnN0IGpzb24gPSBzdXBlci50b0pTT04oaW5jbHVkZVN0YWNrKTtcbiAgICBcbiAgICBpZiAodGhpcy5jb25uZWN0b3JJZCkge1xuICAgICAganNvbi5jb25uZWN0b3JJZCA9IHRoaXMuY29ubmVjdG9ySWQ7XG4gICAgfVxuICAgIFxuICAgIHJldHVybiBqc29uO1xuICB9XG59XG5cbi8qKlxuICogRXJyb3IgY2xhc3MgZm9yIGNvbm5lY3RvciBub3QgZm91bmQgZXJyb3JzXG4gKiBAY2xhc3MgQ29ubmVjdG9yTm90Rm91bmRFcnJvclxuICogQGV4dGVuZHMgQ29ubmVjdG9yRXJyb3JcbiAqL1xuY2xhc3MgQ29ubmVjdG9yTm90Rm91bmRFcnJvciBleHRlbmRzIENvbm5lY3RvckVycm9yIHtcbiAgLyoqXG4gICAqIENyZWF0ZSBhIG5ldyBDb25uZWN0b3JOb3RGb3VuZEVycm9yXG4gICAqIFxuICAgKiBAcGFyYW0ge3N0cmluZ30gY29ubmVjdG9ySWQgLSBJRCBvZiB0aGUgY29ubmVjdG9yXG4gICAqIEBwYXJhbSB7T2JqZWN0fSBvcHRpb25zIC0gRXJyb3Igb3B0aW9uc1xuICAgKi9cbiAgY29uc3RydWN0b3IoY29ubmVjdG9ySWQsIG9wdGlvbnMgPSB7fSkge1xuICAgIGNvbnN0IG1lc3NhZ2UgPSBgQ29ubmVjdG9yIG5vdCBmb3VuZDogJHtjb25uZWN0b3JJZH1gO1xuICAgIFxuICAgIHN1cGVyKG1lc3NhZ2UsIHtcbiAgICAgIGNvZGU6IG9wdGlvbnMuY29kZSB8fCAnQ09OTkVDVE9SX05PVF9GT1VORCcsXG4gICAgICBzZXZlcml0eTogb3B0aW9ucy5zZXZlcml0eSB8fCAnZXJyb3InLFxuICAgICAgY29udGV4dDogeyAuLi5vcHRpb25zLmNvbnRleHQsIGNvbm5lY3RvcklkIH0sXG4gICAgICBjYXVzZTogb3B0aW9ucy5jYXVzZSxcbiAgICAgIGNvbm5lY3RvcklkXG4gICAgfSk7XG4gIH1cblxuICAvKipcbiAgICogR2V0IGEgdXNlci1mcmllbmRseSBlcnJvciBtZXNzYWdlXG4gICAqIFxuICAgKiBAcmV0dXJucyB7c3RyaW5nfSAtIFVzZXItZnJpZW5kbHkgZXJyb3IgbWVzc2FnZVxuICAgKi9cbiAgZ2V0VXNlck1lc3NhZ2UoKSB7XG4gICAgcmV0dXJuIGBUaGUgY29ubmVjdG9yIFwiJHt0aGlzLmNvbm5lY3RvcklkfVwiIHdhcyBub3QgZm91bmQuIFBsZWFzZSBjaGVjayB0aGUgY29ubmVjdG9yIElEIGFuZCB0cnkgYWdhaW4uYDtcbiAgfVxufVxuXG4vKipcbiAqIEVycm9yIGNsYXNzIGZvciBjb25uZWN0b3IgY29uZmlndXJhdGlvbiBlcnJvcnNcbiAqIEBjbGFzcyBDb25uZWN0b3JDb25maWd1cmF0aW9uRXJyb3JcbiAqIEBleHRlbmRzIENvbm5lY3RvckVycm9yXG4gKi9cbmNsYXNzIENvbm5lY3RvckNvbmZpZ3VyYXRpb25FcnJvciBleHRlbmRzIENvbm5lY3RvckVycm9yIHtcbiAgLyoqXG4gICAqIENyZWF0ZSBhIG5ldyBDb25uZWN0b3JDb25maWd1cmF0aW9uRXJyb3JcbiAgICogXG4gICAqIEBwYXJhbSB7c3RyaW5nfSBjb25uZWN0b3JJZCAtIElEIG9mIHRoZSBjb25uZWN0b3JcbiAgICogQHBhcmFtIHtzdHJpbmd9IG1lc3NhZ2UgLSBFcnJvciBtZXNzYWdlXG4gICAqIEBwYXJhbSB7T2JqZWN0fSBvcHRpb25zIC0gRXJyb3Igb3B0aW9uc1xuICAgKi9cbiAgY29uc3RydWN0b3IoY29ubmVjdG9ySWQsIG1lc3NhZ2UgPSAnSW52YWxpZCBjb25uZWN0b3IgY29uZmlndXJhdGlvbicsIG9wdGlvbnMgPSB7fSkge1xuICAgIHN1cGVyKG1lc3NhZ2UsIHtcbiAgICAgIGNvZGU6IG9wdGlvbnMuY29kZSB8fCAnQ09OTkVDVE9SX0NPTkZJR1VSQVRJT05fRVJST1InLFxuICAgICAgc2V2ZXJpdHk6IG9wdGlvbnMuc2V2ZXJpdHkgfHwgJ2Vycm9yJyxcbiAgICAgIGNvbnRleHQ6IHsgLi4ub3B0aW9ucy5jb250ZXh0LCBjb25uZWN0b3JJZCB9LFxuICAgICAgY2F1c2U6IG9wdGlvbnMuY2F1c2UsXG4gICAgICBjb25uZWN0b3JJZFxuICAgIH0pO1xuICAgIFxuICAgIHRoaXMuY29uZmlnRXJyb3JzID0gb3B0aW9ucy5jb25maWdFcnJvcnMgfHwgW107XG4gIH1cblxuICAvKipcbiAgICogR2V0IGEgdXNlci1mcmllbmRseSBlcnJvciBtZXNzYWdlXG4gICAqIFxuICAgKiBAcmV0dXJucyB7c3RyaW5nfSAtIFVzZXItZnJpZW5kbHkgZXJyb3IgbWVzc2FnZVxuICAgKi9cbiAgZ2V0VXNlck1lc3NhZ2UoKSB7XG4gICAgaWYgKHRoaXMuY29uZmlnRXJyb3JzLmxlbmd0aCA+IDApIHtcbiAgICAgIGNvbnN0IGVycm9yTWVzc2FnZXMgPSB0aGlzLmNvbmZpZ0Vycm9ycy5tYXAoZXJyID0+IGVyci5tZXNzYWdlKS5qb2luKCc7ICcpO1xuICAgICAgcmV0dXJuIGBUaGUgY29ubmVjdG9yIGNvbmZpZ3VyYXRpb24gaXMgaW52YWxpZDogJHtlcnJvck1lc3NhZ2VzfWA7XG4gICAgfVxuICAgIHJldHVybiBgVGhlIGNvbm5lY3RvciBjb25maWd1cmF0aW9uIGlzIGludmFsaWQuIFBsZWFzZSBjaGVjayB5b3VyIGNvbmZpZ3VyYXRpb24gYW5kIHRyeSBhZ2Fpbi5gO1xuICB9XG5cbiAgLyoqXG4gICAqIENvbnZlcnQgdGhlIGVycm9yIHRvIGEgSlNPTiBvYmplY3RcbiAgICogXG4gICAqIEBwYXJhbSB7Ym9vbGVhbn0gaW5jbHVkZVN0YWNrIC0gV2hldGhlciB0byBpbmNsdWRlIHRoZSBzdGFjayB0cmFjZVxuICAgKiBAcmV0dXJucyB7T2JqZWN0fSAtIEpTT04gcmVwcmVzZW50YXRpb24gb2YgdGhlIGVycm9yXG4gICAqL1xuICB0b0pTT04oaW5jbHVkZVN0YWNrID0gZmFsc2UpIHtcbiAgICBjb25zdCBqc29uID0gc3VwZXIudG9KU09OKGluY2x1ZGVTdGFjayk7XG4gICAganNvbi5jb25maWdFcnJvcnMgPSB0aGlzLmNvbmZpZ0Vycm9ycztcbiAgICByZXR1cm4ganNvbjtcbiAgfVxufVxuXG4vKipcbiAqIEVycm9yIGNsYXNzIGZvciBjb25uZWN0b3IgZXhlY3V0aW9uIGVycm9yc1xuICogQGNsYXNzIENvbm5lY3RvckV4ZWN1dGlvbkVycm9yXG4gKiBAZXh0ZW5kcyBDb25uZWN0b3JFcnJvclxuICovXG5jbGFzcyBDb25uZWN0b3JFeGVjdXRpb25FcnJvciBleHRlbmRzIENvbm5lY3RvckVycm9yIHtcbiAgLyoqXG4gICAqIENyZWF0ZSBhIG5ldyBDb25uZWN0b3JFeGVjdXRpb25FcnJvclxuICAgKiBcbiAgICogQHBhcmFtIHtzdHJpbmd9IGNvbm5lY3RvcklkIC0gSUQgb2YgdGhlIGNvbm5lY3RvclxuICAgKiBAcGFyYW0ge3N0cmluZ30gb3BlcmF0aW9uSWQgLSBJRCBvZiB0aGUgb3BlcmF0aW9uXG4gICAqIEBwYXJhbSB7c3RyaW5nfSBtZXNzYWdlIC0gRXJyb3IgbWVzc2FnZVxuICAgKiBAcGFyYW0ge09iamVjdH0gb3B0aW9ucyAtIEVycm9yIG9wdGlvbnNcbiAgICovXG4gIGNvbnN0cnVjdG9yKGNvbm5lY3RvcklkLCBvcGVyYXRpb25JZCwgbWVzc2FnZSA9ICdFcnJvciBleGVjdXRpbmcgY29ubmVjdG9yIG9wZXJhdGlvbicsIG9wdGlvbnMgPSB7fSkge1xuICAgIHN1cGVyKG1lc3NhZ2UsIHtcbiAgICAgIGNvZGU6IG9wdGlvbnMuY29kZSB8fCAnQ09OTkVDVE9SX0VYRUNVVElPTl9FUlJPUicsXG4gICAgICBzZXZlcml0eTogb3B0aW9ucy5zZXZlcml0eSB8fCAnZXJyb3InLFxuICAgICAgY29udGV4dDogeyAuLi5vcHRpb25zLmNvbnRleHQsIGNvbm5lY3RvcklkLCBvcGVyYXRpb25JZCB9LFxuICAgICAgY2F1c2U6IG9wdGlvbnMuY2F1c2UsXG4gICAgICBjb25uZWN0b3JJZFxuICAgIH0pO1xuICAgIFxuICAgIHRoaXMub3BlcmF0aW9uSWQgPSBvcGVyYXRpb25JZDtcbiAgfVxuXG4gIC8qKlxuICAgKiBHZXQgYSB1c2VyLWZyaWVuZGx5IGVycm9yIG1lc3NhZ2VcbiAgICogXG4gICAqIEByZXR1cm5zIHtzdHJpbmd9IC0gVXNlci1mcmllbmRseSBlcnJvciBtZXNzYWdlXG4gICAqL1xuICBnZXRVc2VyTWVzc2FnZSgpIHtcbiAgICByZXR1cm4gYEFuIGVycm9yIG9jY3VycmVkIHdoaWxlIGV4ZWN1dGluZyB0aGUgY29ubmVjdG9yIG9wZXJhdGlvbi4gUGxlYXNlIHRyeSBhZ2FpbiBsYXRlciBvciBjb250YWN0IHN1cHBvcnQgaWYgdGhlIGlzc3VlIHBlcnNpc3RzLmA7XG4gIH1cblxuICAvKipcbiAgICogQ29udmVydCB0aGUgZXJyb3IgdG8gYSBKU09OIG9iamVjdFxuICAgKiBcbiAgICogQHBhcmFtIHtib29sZWFufSBpbmNsdWRlU3RhY2sgLSBXaGV0aGVyIHRvIGluY2x1ZGUgdGhlIHN0YWNrIHRyYWNlXG4gICAqIEByZXR1cm5zIHtPYmplY3R9IC0gSlNPTiByZXByZXNlbnRhdGlvbiBvZiB0aGUgZXJyb3JcbiAgICovXG4gIHRvSlNPTihpbmNsdWRlU3RhY2sgPSBmYWxzZSkge1xuICAgIGNvbnN0IGpzb24gPSBzdXBlci50b0pTT04oaW5jbHVkZVN0YWNrKTtcbiAgICBqc29uLm9wZXJhdGlvbklkID0gdGhpcy5vcGVyYXRpb25JZDtcbiAgICByZXR1cm4ganNvbjtcbiAgfVxufVxuXG4vKipcbiAqIEVycm9yIGNsYXNzIGZvciBjb25uZWN0b3IgdmVyc2lvbiBpbmNvbXBhdGliaWxpdHkgZXJyb3JzXG4gKiBAY2xhc3MgQ29ubmVjdG9yVmVyc2lvbkVycm9yXG4gKiBAZXh0ZW5kcyBDb25uZWN0b3JFcnJvclxuICovXG5jbGFzcyBDb25uZWN0b3JWZXJzaW9uRXJyb3IgZXh0ZW5kcyBDb25uZWN0b3JFcnJvciB7XG4gIC8qKlxuICAgKiBDcmVhdGUgYSBuZXcgQ29ubmVjdG9yVmVyc2lvbkVycm9yXG4gICAqIFxuICAgKiBAcGFyYW0ge3N0cmluZ30gY29ubmVjdG9ySWQgLSBJRCBvZiB0aGUgY29ubmVjdG9yXG4gICAqIEBwYXJhbSB7c3RyaW5nfSBjdXJyZW50VmVyc2lvbiAtIEN1cnJlbnQgdmVyc2lvblxuICAgKiBAcGFyYW0ge3N0cmluZ30gcmVxdWlyZWRWZXJzaW9uIC0gUmVxdWlyZWQgdmVyc2lvblxuICAgKiBAcGFyYW0ge09iamVjdH0gb3B0aW9ucyAtIEVycm9yIG9wdGlvbnNcbiAgICovXG4gIGNvbnN0cnVjdG9yKGNvbm5lY3RvcklkLCBjdXJyZW50VmVyc2lvbiwgcmVxdWlyZWRWZXJzaW9uLCBvcHRpb25zID0ge30pIHtcbiAgICBjb25zdCBtZXNzYWdlID0gYENvbm5lY3RvciB2ZXJzaW9uIGluY29tcGF0aWJsZTogJHtjb25uZWN0b3JJZH0gKGN1cnJlbnQ6ICR7Y3VycmVudFZlcnNpb259LCByZXF1aXJlZDogJHtyZXF1aXJlZFZlcnNpb259KWA7XG4gICAgXG4gICAgc3VwZXIobWVzc2FnZSwge1xuICAgICAgY29kZTogb3B0aW9ucy5jb2RlIHx8ICdDT05ORUNUT1JfVkVSU0lPTl9FUlJPUicsXG4gICAgICBzZXZlcml0eTogb3B0aW9ucy5zZXZlcml0eSB8fCAnZXJyb3InLFxuICAgICAgY29udGV4dDogeyAuLi5vcHRpb25zLmNvbnRleHQsIGNvbm5lY3RvcklkLCBjdXJyZW50VmVyc2lvbiwgcmVxdWlyZWRWZXJzaW9uIH0sXG4gICAgICBjYXVzZTogb3B0aW9ucy5jYXVzZSxcbiAgICAgIGNvbm5lY3RvcklkXG4gICAgfSk7XG4gICAgXG4gICAgdGhpcy5jdXJyZW50VmVyc2lvbiA9IGN1cnJlbnRWZXJzaW9uO1xuICAgIHRoaXMucmVxdWlyZWRWZXJzaW9uID0gcmVxdWlyZWRWZXJzaW9uO1xuICB9XG5cbiAgLyoqXG4gICAqIEdldCBhIHVzZXItZnJpZW5kbHkgZXJyb3IgbWVzc2FnZVxuICAgKiBcbiAgICogQHJldHVybnMge3N0cmluZ30gLSBVc2VyLWZyaWVuZGx5IGVycm9yIG1lc3NhZ2VcbiAgICovXG4gIGdldFVzZXJNZXNzYWdlKCkge1xuICAgIHJldHVybiBgVGhlIGNvbm5lY3RvciB2ZXJzaW9uIGlzIGluY29tcGF0aWJsZS4gUGxlYXNlIHVwZGF0ZSB0aGUgY29ubmVjdG9yIHRvIHZlcnNpb24gJHt0aGlzLnJlcXVpcmVkVmVyc2lvbn0gb3IgbGF0ZXIuYDtcbiAgfVxuXG4gIC8qKlxuICAgKiBDb252ZXJ0IHRoZSBlcnJvciB0byBhIEpTT04gb2JqZWN0XG4gICAqIFxuICAgKiBAcGFyYW0ge2Jvb2xlYW59IGluY2x1ZGVTdGFjayAtIFdoZXRoZXIgdG8gaW5jbHVkZSB0aGUgc3RhY2sgdHJhY2VcbiAgICogQHJldHVybnMge09iamVjdH0gLSBKU09OIHJlcHJlc2VudGF0aW9uIG9mIHRoZSBlcnJvclxuICAgKi9cbiAgdG9KU09OKGluY2x1ZGVTdGFjayA9IGZhbHNlKSB7XG4gICAgY29uc3QganNvbiA9IHN1cGVyLnRvSlNPTihpbmNsdWRlU3RhY2spO1xuICAgIGpzb24uY3VycmVudFZlcnNpb24gPSB0aGlzLmN1cnJlbnRWZXJzaW9uO1xuICAgIGpzb24ucmVxdWlyZWRWZXJzaW9uID0gdGhpcy5yZXF1aXJlZFZlcnNpb247XG4gICAgcmV0dXJuIGpzb247XG4gIH1cbn1cblxubW9kdWxlLmV4cG9ydHMgPSB7XG4gIENvbm5lY3RvckVycm9yLFxuICBDb25uZWN0b3JOb3RGb3VuZEVycm9yLFxuICBDb25uZWN0b3JDb25maWd1cmF0aW9uRXJyb3IsXG4gIENvbm5lY3RvckV4ZWN1dGlvbkVycm9yLFxuICBDb25uZWN0b3JWZXJzaW9uRXJyb3Jcbn07XG4iXSwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsTUFBTUEsZ0JBQWdCLEdBQUdDLE9BQU8sQ0FBQyxjQUFjLENBQUM7O0FBRWhEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNQyxjQUFjLFNBQVNGLGdCQUFnQixDQUFDO0VBQzVDO0FBQ0Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7RUFDRUcsV0FBV0EsQ0FBQ0MsT0FBTyxFQUFFQyxPQUFPLEdBQUcsQ0FBQyxDQUFDLEVBQUU7SUFDakMsS0FBSyxDQUFDRCxPQUFPLEVBQUU7TUFDYkUsSUFBSSxFQUFFRCxPQUFPLENBQUNDLElBQUksSUFBSSxpQkFBaUI7TUFDdkNDLFFBQVEsRUFBRUYsT0FBTyxDQUFDRSxRQUFRLElBQUksT0FBTztNQUNyQ0MsT0FBTyxFQUFFSCxPQUFPLENBQUNHLE9BQU8sSUFBSSxDQUFDLENBQUM7TUFDOUJDLEtBQUssRUFBRUosT0FBTyxDQUFDSTtJQUNqQixDQUFDLENBQUM7SUFFRixJQUFJLENBQUNDLFdBQVcsR0FBR0wsT0FBTyxDQUFDSyxXQUFXO0VBQ3hDOztFQUVBO0FBQ0Y7QUFDQTtBQUNBO0FBQ0E7RUFDRUMsY0FBY0EsQ0FBQSxFQUFHO0lBQ2YsT0FBTyx3R0FBd0c7RUFDakg7O0VBRUE7QUFDRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0VBQ0VDLE1BQU1BLENBQUNDLFlBQVksR0FBRyxLQUFLLEVBQUU7SUFDM0IsTUFBTUMsSUFBSSxHQUFHLEtBQUssQ0FBQ0YsTUFBTSxDQUFDQyxZQUFZLENBQUM7SUFFdkMsSUFBSSxJQUFJLENBQUNILFdBQVcsRUFBRTtNQUNwQkksSUFBSSxDQUFDSixXQUFXLEdBQUcsSUFBSSxDQUFDQSxXQUFXO0lBQ3JDO0lBRUEsT0FBT0ksSUFBSTtFQUNiO0FBQ0Y7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU1DLHNCQUFzQixTQUFTYixjQUFjLENBQUM7RUFDbEQ7QUFDRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0VBQ0VDLFdBQVdBLENBQUNPLFdBQVcsRUFBRUwsT0FBTyxHQUFHLENBQUMsQ0FBQyxFQUFFO0lBQ3JDLE1BQU1ELE9BQU8sR0FBRyx3QkFBd0JNLFdBQVcsRUFBRTtJQUVyRCxLQUFLLENBQUNOLE9BQU8sRUFBRTtNQUNiRSxJQUFJLEVBQUVELE9BQU8sQ0FBQ0MsSUFBSSxJQUFJLHFCQUFxQjtNQUMzQ0MsUUFBUSxFQUFFRixPQUFPLENBQUNFLFFBQVEsSUFBSSxPQUFPO01BQ3JDQyxPQUFPLEVBQUU7UUFBRSxHQUFHSCxPQUFPLENBQUNHLE9BQU87UUFBRUU7TUFBWSxDQUFDO01BQzVDRCxLQUFLLEVBQUVKLE9BQU8sQ0FBQ0ksS0FBSztNQUNwQkM7SUFDRixDQUFDLENBQUM7RUFDSjs7RUFFQTtBQUNGO0FBQ0E7QUFDQTtBQUNBO0VBQ0VDLGNBQWNBLENBQUEsRUFBRztJQUNmLE9BQU8sa0JBQWtCLElBQUksQ0FBQ0QsV0FBVywrREFBK0Q7RUFDMUc7QUFDRjs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTU0sMkJBQTJCLFNBQVNkLGNBQWMsQ0FBQztFQUN2RDtBQUNGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtFQUNFQyxXQUFXQSxDQUFDTyxXQUFXLEVBQUVOLE9BQU8sR0FBRyxpQ0FBaUMsRUFBRUMsT0FBTyxHQUFHLENBQUMsQ0FBQyxFQUFFO0lBQ2xGLEtBQUssQ0FBQ0QsT0FBTyxFQUFFO01BQ2JFLElBQUksRUFBRUQsT0FBTyxDQUFDQyxJQUFJLElBQUksK0JBQStCO01BQ3JEQyxRQUFRLEVBQUVGLE9BQU8sQ0FBQ0UsUUFBUSxJQUFJLE9BQU87TUFDckNDLE9BQU8sRUFBRTtRQUFFLEdBQUdILE9BQU8sQ0FBQ0csT0FBTztRQUFFRTtNQUFZLENBQUM7TUFDNUNELEtBQUssRUFBRUosT0FBTyxDQUFDSSxLQUFLO01BQ3BCQztJQUNGLENBQUMsQ0FBQztJQUVGLElBQUksQ0FBQ08sWUFBWSxHQUFHWixPQUFPLENBQUNZLFlBQVksSUFBSSxFQUFFO0VBQ2hEOztFQUVBO0FBQ0Y7QUFDQTtBQUNBO0FBQ0E7RUFDRU4sY0FBY0EsQ0FBQSxFQUFHO0lBQ2YsSUFBSSxJQUFJLENBQUNNLFlBQVksQ0FBQ0MsTUFBTSxHQUFHLENBQUMsRUFBRTtNQUNoQyxNQUFNQyxhQUFhLEdBQUcsSUFBSSxDQUFDRixZQUFZLENBQUNHLEdBQUcsQ0FBQ0MsR0FBRyxJQUFJQSxHQUFHLENBQUNqQixPQUFPLENBQUMsQ0FBQ2tCLElBQUksQ0FBQyxJQUFJLENBQUM7TUFDMUUsT0FBTywyQ0FBMkNILGFBQWEsRUFBRTtJQUNuRTtJQUNBLE9BQU8sd0ZBQXdGO0VBQ2pHOztFQUVBO0FBQ0Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtFQUNFUCxNQUFNQSxDQUFDQyxZQUFZLEdBQUcsS0FBSyxFQUFFO0lBQzNCLE1BQU1DLElBQUksR0FBRyxLQUFLLENBQUNGLE1BQU0sQ0FBQ0MsWUFBWSxDQUFDO0lBQ3ZDQyxJQUFJLENBQUNHLFlBQVksR0FBRyxJQUFJLENBQUNBLFlBQVk7SUFDckMsT0FBT0gsSUFBSTtFQUNiO0FBQ0Y7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU1TLHVCQUF1QixTQUFTckIsY0FBYyxDQUFDO0VBQ25EO0FBQ0Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7RUFDRUMsV0FBV0EsQ0FBQ08sV0FBVyxFQUFFYyxXQUFXLEVBQUVwQixPQUFPLEdBQUcscUNBQXFDLEVBQUVDLE9BQU8sR0FBRyxDQUFDLENBQUMsRUFBRTtJQUNuRyxLQUFLLENBQUNELE9BQU8sRUFBRTtNQUNiRSxJQUFJLEVBQUVELE9BQU8sQ0FBQ0MsSUFBSSxJQUFJLDJCQUEyQjtNQUNqREMsUUFBUSxFQUFFRixPQUFPLENBQUNFLFFBQVEsSUFBSSxPQUFPO01BQ3JDQyxPQUFPLEVBQUU7UUFBRSxHQUFHSCxPQUFPLENBQUNHLE9BQU87UUFBRUUsV0FBVztRQUFFYztNQUFZLENBQUM7TUFDekRmLEtBQUssRUFBRUosT0FBTyxDQUFDSSxLQUFLO01BQ3BCQztJQUNGLENBQUMsQ0FBQztJQUVGLElBQUksQ0FBQ2MsV0FBVyxHQUFHQSxXQUFXO0VBQ2hDOztFQUVBO0FBQ0Y7QUFDQTtBQUNBO0FBQ0E7RUFDRWIsY0FBY0EsQ0FBQSxFQUFHO0lBQ2YsT0FBTyw2SEFBNkg7RUFDdEk7O0VBRUE7QUFDRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0VBQ0VDLE1BQU1BLENBQUNDLFlBQVksR0FBRyxLQUFLLEVBQUU7SUFDM0IsTUFBTUMsSUFBSSxHQUFHLEtBQUssQ0FBQ0YsTUFBTSxDQUFDQyxZQUFZLENBQUM7SUFDdkNDLElBQUksQ0FBQ1UsV0FBVyxHQUFHLElBQUksQ0FBQ0EsV0FBVztJQUNuQyxPQUFPVixJQUFJO0VBQ2I7QUFDRjs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTVcscUJBQXFCLFNBQVN2QixjQUFjLENBQUM7RUFDakQ7QUFDRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtFQUNFQyxXQUFXQSxDQUFDTyxXQUFXLEVBQUVnQixjQUFjLEVBQUVDLGVBQWUsRUFBRXRCLE9BQU8sR0FBRyxDQUFDLENBQUMsRUFBRTtJQUN0RSxNQUFNRCxPQUFPLEdBQUcsbUNBQW1DTSxXQUFXLGNBQWNnQixjQUFjLGVBQWVDLGVBQWUsR0FBRztJQUUzSCxLQUFLLENBQUN2QixPQUFPLEVBQUU7TUFDYkUsSUFBSSxFQUFFRCxPQUFPLENBQUNDLElBQUksSUFBSSx5QkFBeUI7TUFDL0NDLFFBQVEsRUFBRUYsT0FBTyxDQUFDRSxRQUFRLElBQUksT0FBTztNQUNyQ0MsT0FBTyxFQUFFO1FBQUUsR0FBR0gsT0FBTyxDQUFDRyxPQUFPO1FBQUVFLFdBQVc7UUFBRWdCLGNBQWM7UUFBRUM7TUFBZ0IsQ0FBQztNQUM3RWxCLEtBQUssRUFBRUosT0FBTyxDQUFDSSxLQUFLO01BQ3BCQztJQUNGLENBQUMsQ0FBQztJQUVGLElBQUksQ0FBQ2dCLGNBQWMsR0FBR0EsY0FBYztJQUNwQyxJQUFJLENBQUNDLGVBQWUsR0FBR0EsZUFBZTtFQUN4Qzs7RUFFQTtBQUNGO0FBQ0E7QUFDQTtBQUNBO0VBQ0VoQixjQUFjQSxDQUFBLEVBQUc7SUFDZixPQUFPLGlGQUFpRixJQUFJLENBQUNnQixlQUFlLFlBQVk7RUFDMUg7O0VBRUE7QUFDRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0VBQ0VmLE1BQU1BLENBQUNDLFlBQVksR0FBRyxLQUFLLEVBQUU7SUFDM0IsTUFBTUMsSUFBSSxHQUFHLEtBQUssQ0FBQ0YsTUFBTSxDQUFDQyxZQUFZLENBQUM7SUFDdkNDLElBQUksQ0FBQ1ksY0FBYyxHQUFHLElBQUksQ0FBQ0EsY0FBYztJQUN6Q1osSUFBSSxDQUFDYSxlQUFlLEdBQUcsSUFBSSxDQUFDQSxlQUFlO0lBQzNDLE9BQU9iLElBQUk7RUFDYjtBQUNGO0FBRUFjLE1BQU0sQ0FBQ0MsT0FBTyxHQUFHO0VBQ2YzQixjQUFjO0VBQ2RhLHNCQUFzQjtFQUN0QkMsMkJBQTJCO0VBQzNCTyx1QkFBdUI7RUFDdkJFO0FBQ0YsQ0FBQyIsImlnbm9yZUxpc3QiOltdfQ==