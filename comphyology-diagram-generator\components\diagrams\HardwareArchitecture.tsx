import React from 'react';
import DiagramTemplate from '../DiagramTemplate';

// Define the diagram data
const elements = [
  {
    id: 'hardware-architecture',
    top: 50,
    left: 350,
    width: 300,
    text: 'Hardware Architecture',
    number: '1',
    bold: true,
    fontSize: '20px',
    backgroundColor: '#e6f7ff'
  },
  // Tensor Processing Units
  {
    id: 'tensor-processing-units',
    top: 120,
    left: 350,
    width: 300,
    text: 'Tensor Processing Units (TPUs)',
    number: '2',
    bold: true,
    fontSize: '16px',
    backgroundColor: '#fff0f6'
  },
  {
    id: 'tensor-core-array',
    top: 180,
    left: 100,
    width: 200,
    text: 'Tensor Core Array\nMassively parallel processing units',
    number: '3',
    fontSize: '14px',
    backgroundColor: '#f6ffed'
  },
  {
    id: 'arithmetic-units',
    top: 180,
    left: 350,
    width: 200,
    text: 'High-Precision Arithmetic Units\n64-bit floating-point units',
    number: '4',
    fontSize: '14px',
    backgroundColor: '#f6ffed'
  },
  {
    id: 'tensor-memory',
    top: 180,
    left: 600,
    width: 200,
    text: 'Tensor Memory Cache\nSpecialized memory architecture',
    number: '5',
    fontSize: '14px',
    backgroundColor: '#f6ffed'
  },
  {
    id: 'tensor-instruction',
    top: 240,
    left: 350,
    width: 200,
    text: 'Tensor Instruction Set\nCustom instructions for tensor operations',
    number: '6',
    fontSize: '14px',
    backgroundColor: '#f6ffed'
  },
  // Fusion Processing Engines
  {
    id: 'fusion-engines',
    top: 300,
    left: 350,
    width: 300,
    text: 'Fusion Processing Engines (FPEs)',
    number: '7',
    bold: true,
    fontSize: '16px',
    backgroundColor: '#fff0f6'
  },
  {
    id: 'fusion-core',
    top: 360,
    left: 100,
    width: 200,
    text: 'Fusion Core\nImplements fusion operator (⊕)',
    number: '8',
    fontSize: '14px',
    backgroundColor: '#fffbe6'
  },
  {
    id: 'pattern-matching',
    top: 360,
    left: 350,
    width: 200,
    text: 'Pattern Matching Units\nIdentifies related data patterns',
    number: '9',
    fontSize: '14px',
    backgroundColor: '#fffbe6'
  },
  {
    id: 'fusion-memory',
    top: 360,
    left: 600,
    width: 200,
    text: 'Fusion Memory\nStores intermediate fusion results',
    number: '10',
    fontSize: '14px',
    backgroundColor: '#fffbe6'
  },
  // Scaling Circuits
  {
    id: 'scaling-circuits',
    top: 420,
    left: 350,
    width: 300,
    text: 'Scaling Circuits',
    number: '11',
    bold: true,
    fontSize: '16px',
    backgroundColor: '#fff0f6'
  },
  {
    id: 'pi-constant',
    top: 480,
    left: 100,
    width: 200,
    text: 'π10³ Constant Storage\nHigh-precision constant memory',
    number: '12',
    fontSize: '14px',
    backgroundColor: '#f9f0ff'
  },
  {
    id: 'multiplication-engine',
    top: 480,
    left: 350,
    width: 200,
    text: 'Multiplication Engine\nHigh-throughput scaling operations',
    number: '13',
    fontSize: '14px',
    backgroundColor: '#f9f0ff'
  },
  {
    id: 'normalization-unit',
    top: 480,
    left: 600,
    width: 200,
    text: 'Normalization Unit\nStandardizes output values',
    number: '14',
    fontSize: '14px',
    backgroundColor: '#f9f0ff'
  },
  // System Interconnect
  {
    id: 'system-interconnect',
    top: 540,
    left: 350,
    width: 300,
    text: 'System Interconnect',
    number: '15',
    bold: true,
    fontSize: '16px',
    backgroundColor: '#fff0f6'
  },
  {
    id: 'high-bandwidth',
    top: 600,
    left: 100,
    width: 200,
    text: 'High-Bandwidth Fabric\n1.2 TB/s inter-component bandwidth',
    number: '16',
    fontSize: '14px',
    backgroundColor: '#fff2e8'
  },
  {
    id: 'low-latency',
    top: 600,
    left: 350,
    width: 200,
    text: 'Low-Latency Switching\n<100ns component-to-component latency',
    number: '17',
    fontSize: '14px',
    backgroundColor: '#fff2e8'
  },
  {
    id: 'coherent-cache',
    top: 600,
    left: 600,
    width: 200,
    text: 'Coherent Cache System\nMaintains data consistency',
    number: '18',
    fontSize: '14px',
    backgroundColor: '#fff2e8'
  },
  // Performance Metrics
  {
    id: 'performance-metrics',
    top: 660,
    left: 350,
    width: 300,
    text: 'Performance Metrics',
    number: '19',
    bold: true,
    fontSize: '16px',
    backgroundColor: '#fff0f6'
  },
  {
    id: 'processing-power',
    top: 720,
    left: 100,
    width: 200,
    text: 'Processing Power\n3,142 TFLOPS tensor operations',
    number: '20',
    fontSize: '14px',
    backgroundColor: '#fffbe6'
  },
  {
    id: 'energy-efficiency',
    top: 720,
    left: 350,
    width: 200,
    text: 'Energy Efficiency\n0.5 TFLOPS/watt',
    number: '21',
    fontSize: '14px',
    backgroundColor: '#fffbe6'
  },
  {
    id: 'accuracy',
    top: 720,
    left: 600,
    width: 200,
    text: 'Accuracy\n95% across all domains',
    number: '22',
    fontSize: '14px',
    backgroundColor: '#fffbe6'
  }
];

const connections = [
  // Connect Hardware Architecture to Tensor Processing Units
  {
    start: { x: 500, y: 100 },
    end: { x: 500, y: 120 },
    type: 'arrow'
  },
  // Connect Tensor Processing Units to components
  {
    start: { x: 350, y: 170 },
    end: { x: 200, y: 180 },
    type: 'arrow'
  },
  {
    start: { x: 500, y: 170 },
    end: { x: 450, y: 180 },
    type: 'arrow'
  },
  {
    start: { x: 650, y: 170 },
    end: { x: 700, y: 180 },
    type: 'arrow'
  },
  {
    start: { x: 500, y: 210 },
    end: { x: 450, y: 240 },
    type: 'arrow'
  },
  // Connect to Fusion Processing Engines
  {
    start: { x: 500, y: 270 },
    end: { x: 500, y: 300 },
    type: 'arrow'
  },
  // Connect Fusion Processing Engines to components
  {
    start: { x: 350, y: 350 },
    end: { x: 200, y: 360 },
    type: 'arrow'
  },
  {
    start: { x: 500, y: 350 },
    end: { x: 450, y: 360 },
    type: 'arrow'
  },
  {
    start: { x: 650, y: 350 },
    end: { x: 700, y: 360 },
    type: 'arrow'
  },
  // Connect to Scaling Circuits
  {
    start: { x: 500, y: 390 },
    end: { x: 500, y: 420 },
    type: 'arrow'
  },
  // Connect Scaling Circuits to components
  {
    start: { x: 350, y: 470 },
    end: { x: 200, y: 480 },
    type: 'arrow'
  },
  {
    start: { x: 500, y: 470 },
    end: { x: 450, y: 480 },
    type: 'arrow'
  },
  {
    start: { x: 650, y: 470 },
    end: { x: 700, y: 480 },
    type: 'arrow'
  },
  // Connect to System Interconnect
  {
    start: { x: 500, y: 510 },
    end: { x: 500, y: 540 },
    type: 'arrow'
  },
  // Connect System Interconnect to components
  {
    start: { x: 350, y: 590 },
    end: { x: 200, y: 600 },
    type: 'arrow'
  },
  {
    start: { x: 500, y: 590 },
    end: { x: 450, y: 600 },
    type: 'arrow'
  },
  {
    start: { x: 650, y: 590 },
    end: { x: 700, y: 600 },
    type: 'arrow'
  },
  // Connect to Performance Metrics
  {
    start: { x: 500, y: 630 },
    end: { x: 500, y: 660 },
    type: 'arrow'
  },
  // Connect Performance Metrics to components
  {
    start: { x: 350, y: 710 },
    end: { x: 200, y: 720 },
    type: 'arrow'
  },
  {
    start: { x: 500, y: 710 },
    end: { x: 450, y: 720 },
    type: 'arrow'
  },
  {
    start: { x: 650, y: 710 },
    end: { x: 700, y: 720 },
    type: 'arrow'
  }
];

const HardwareArchitecture: React.FC = () => {
  return (
    <DiagramTemplate 
      elements={elements} 
      connections={connections} 
      width="900px" 
      height="770px" 
    />
  );
};

export default HardwareArchitecture;
