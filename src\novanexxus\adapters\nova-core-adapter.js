/**
 * NovaCore Adapter for NovaNexxus
 * 
 * This module implements the adapter for integrating NovaCore with NovaNexxus.
 * It leverages our existing event system and CSDE integration to enhance
 * NovaCore's tensor operations with Cyber-Safety capabilities.
 */

const EventEmitter = require('events');
const { performance } = require('perf_hooks');
const { Event, EventPriority } = require('../../novacore/events/event-processor');
const { ComponentCommunicator, MessageType } = require('../../novacore/events/component-communicator');
const { CSEDOperationType, CSEDDomainType } = require('../csde-integration');
const { Tensor } = require('../../novacore/models/Tensor');

/**
 * NovaCore adapter status
 * @enum {string}
 */
const NovaCoreAdapterStatus = {
  INITIALIZING: 'INITIALIZING',
  READY: 'READY',
  PROCESSING: 'PROCESSING',
  ERROR: 'ERROR',
  SHUTDOWN: 'SHUTDOWN'
};

/**
 * NovaCore adapter for NovaNexxus
 * @extends EventEmitter
 */
class NovaCoreAdapter extends EventEmitter {
  /**
   * Create a new NovaCore adapter
   * @param {Object} options - Adapter options
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      enableLogging: options.enableLogging !== undefined ? options.enableLogging : true,
      enableMetrics: options.enableMetrics !== undefined ? options.enableMetrics : true,
      novaCore: options.novaCore,
      csdeIntegration: options.csdeIntegration,
      eventProcessor: options.eventProcessor,
      ...options
    };
    
    // Validate required options
    if (!this.options.novaCore) {
      throw new Error('NovaCore instance is required');
    }
    
    if (!this.options.csdeIntegration) {
      throw new Error('CSDE integration instance is required');
    }
    
    if (!this.options.eventProcessor) {
      throw new Error('Event processor instance is required');
    }
    
    // Initialize adapter state
    this.status = NovaCoreAdapterStatus.INITIALIZING;
    this.metrics = {
      requests: 0,
      enhancedTensors: 0,
      errors: 0,
      latency: {
        total: 0,
        count: 0
      }
    };
    
    this.log('NovaCore adapter initialized');
  }
  
  /**
   * Log a message if logging is enabled
   * @param {...any} args - Arguments to log
   */
  log(...args) {
    if (this.options.enableLogging) {
      console.log(`[NovaCoreAdapter ${new Date().toISOString()}]`, ...args);
    }
  }
  
  /**
   * Initialize the adapter
   * @returns {Promise<void>} - A promise that resolves when initialization is complete
   */
  async initialize() {
    try {
      this.log('Initializing NovaCore adapter...');
      
      // Initialize component communicator
      this.communicator = new ComponentCommunicator({
        componentId: 'novanexxus-novacore-adapter',
        enableLogging: this.options.enableLogging,
        eventProcessor: this.options.eventProcessor
      });
      
      // Connect component communicator
      await this.communicator.connect();
      
      // Register with CSDE integration
      await this._registerWithCSEDIntegration();
      
      // Register event handlers
      this._registerEventHandlers();
      
      // Set status to ready
      this.status = NovaCoreAdapterStatus.READY;
      
      this.log('NovaCore adapter initialized successfully');
      this.emit('ready');
      
      return Promise.resolve();
    } catch (error) {
      this.status = NovaCoreAdapterStatus.ERROR;
      this.log('Error initializing NovaCore adapter:', error);
      this.emit('error', error);
      return Promise.reject(error);
    }
  }
  
  /**
   * Register with CSDE integration
   * @returns {Promise<void>} - A promise that resolves when registration is complete
   * @private
   */
  async _registerWithCSEDIntegration() {
    try {
      this.log('Registering with CSDE integration...');
      
      // Create registration event
      const registrationEvent = new Event('component.register', {
        componentId: 'novanexxus-novacore-adapter',
        componentType: 'adapter',
        capabilities: ['tensor-processing', 'event-processing', 'control-system']
      });
      
      // Process the registration event
      const result = await this.options.eventProcessor.processEvent(registrationEvent);
      
      if (!result.data.result || !result.data.result.success) {
        throw new Error('Registration with CSDE integration failed');
      }
      
      this.log('Registered with CSDE integration successfully');
      return Promise.resolve();
    } catch (error) {
      this.log('Error registering with CSDE integration:', error);
      return Promise.reject(error);
    }
  }
  
  /**
   * Register event handlers
   * @private
   */
  _registerEventHandlers() {
    // Register handler for tensor processing
    this.options.eventProcessor.registerHandler('tensor.process', async (event) => {
      try {
        const { tensor, operation, options } = event.data;
        
        // Process tensor
        const result = await this.processTensor(tensor, operation, options);
        
        // Update event data with result
        event.data.result = result;
        
        return event;
      } catch (error) {
        this.log('Error processing tensor event:', error);
        throw error;
      }
    });
    
    // Register handler for tensor enhancement
    this.options.eventProcessor.registerHandler('tensor.enhance', async (event) => {
      try {
        const { tensor, domain, options } = event.data;
        
        // Enhance tensor with CSDE
        const result = await this.enhanceTensor(tensor, domain, options);
        
        // Update event data with result
        event.data.result = result;
        
        return event;
      } catch (error) {
        this.log('Error enhancing tensor event:', error);
        throw error;
      }
    });
  }
  
  /**
   * Process a tensor using NovaCore
   * @param {Tensor|Object} tensor - The tensor to process
   * @param {string} operation - The operation to perform
   * @param {Object} options - Processing options
   * @returns {Promise<Tensor>} - A promise that resolves to the processed tensor
   */
  async processTensor(tensor, operation, options = {}) {
    const startTime = performance.now();
    
    try {
      // Update metrics
      if (this.options.enableMetrics) {
        this.metrics.requests++;
      }
      
      // Set status to processing
      this.status = NovaCoreAdapterStatus.PROCESSING;
      
      // Convert to Tensor if necessary
      const tensorObj = tensor instanceof Tensor 
        ? tensor 
        : new Tensor(tensor.dimensions, tensor.data, tensor.metadata);
      
      // Process tensor with NovaCore
      const processedTensor = await this.options.novaCore.processTensor(tensorObj, operation, options);
      
      // Update metrics
      if (this.options.enableMetrics) {
        const latency = performance.now() - startTime;
        this.metrics.latency.total += latency;
        this.metrics.latency.count++;
      }
      
      // Set status to ready
      this.status = NovaCoreAdapterStatus.READY;
      
      return processedTensor;
    } catch (error) {
      // Update metrics
      if (this.options.enableMetrics) {
        this.metrics.errors++;
      }
      
      this.log('Error processing tensor:', error);
      throw error;
    }
  }
  
  /**
   * Enhance a tensor with CSDE
   * @param {Tensor|Object} tensor - The tensor to enhance
   * @param {CSEDDomainType} domain - The domain to enhance in
   * @param {Object} options - Enhancement options
   * @returns {Promise<Tensor>} - A promise that resolves to the enhanced tensor
   */
  async enhanceTensor(tensor, domain = CSEDDomainType.GENERAL, options = {}) {
    const startTime = performance.now();
    
    try {
      // Update metrics
      if (this.options.enableMetrics) {
        this.metrics.requests++;
      }
      
      // Set status to processing
      this.status = NovaCoreAdapterStatus.PROCESSING;
      
      // Convert to Tensor if necessary
      const tensorObj = tensor instanceof Tensor 
        ? tensor 
        : new Tensor(tensor.dimensions, tensor.data, tensor.metadata);
      
      // Prepare tensor data for CSDE
      const tensorData = {
        dimensions: tensorObj.dimensions,
        data: tensorObj.data,
        metadata: tensorObj.metadata || {}
      };
      
      // Process with CSDE
      const csdeResult = await this.options.csdeIntegration.process(
        tensorData,
        CSEDOperationType.ENHANCE,
        domain,
        options
      );
      
      // Create enhanced tensor
      const enhancedTensor = new Tensor(
        csdeResult.data.dimensions,
        csdeResult.data.values || csdeResult.data.data,
        {
          ...csdeResult.data.metadata,
          csde: {
            operation: CSEDOperationType.ENHANCE,
            domain,
            timestamp: csdeResult.timestamp,
            nistCompliance: csdeResult.data.metadata.nistCompliance
          }
        }
      );
      
      // Update metrics
      if (this.options.enableMetrics) {
        this.metrics.enhancedTensors++;
        const latency = performance.now() - startTime;
        this.metrics.latency.total += latency;
        this.metrics.latency.count++;
      }
      
      // Set status to ready
      this.status = NovaCoreAdapterStatus.READY;
      
      return enhancedTensor;
    } catch (error) {
      // Update metrics
      if (this.options.enableMetrics) {
        this.metrics.errors++;
      }
      
      this.log('Error enhancing tensor:', error);
      throw error;
    }
  }
  
  /**
   * Get adapter metrics
   * @returns {Object} - The adapter metrics
   */
  getMetrics() {
    if (!this.options.enableMetrics) {
      return { metricsDisabled: true };
    }
    
    return {
      status: this.status,
      requests: this.metrics.requests,
      enhancedTensors: this.metrics.enhancedTensors,
      errors: this.metrics.errors,
      errorRate: this.metrics.requests > 0 
        ? this.metrics.errors / this.metrics.requests 
        : 0,
      averageLatency: this.metrics.latency.count > 0 
        ? this.metrics.latency.total / this.metrics.latency.count 
        : 0
    };
  }
  
  /**
   * Reset adapter metrics
   */
  resetMetrics() {
    if (!this.options.enableMetrics) {
      return;
    }
    
    this.metrics = {
      requests: 0,
      enhancedTensors: 0,
      errors: 0,
      latency: {
        total: 0,
        count: 0
      }
    };
    
    this.log('Metrics reset for NovaCore adapter');
  }
  
  /**
   * Shutdown the adapter
   * @returns {Promise<void>} - A promise that resolves when shutdown is complete
   */
  async shutdown() {
    try {
      this.log('Shutting down NovaCore adapter...');
      
      // Disconnect component communicator
      if (this.communicator) {
        await this.communicator.disconnect();
      }
      
      // Set status to shutdown
      this.status = NovaCoreAdapterStatus.SHUTDOWN;
      
      this.log('NovaCore adapter shut down successfully');
      this.emit('shutdown');
      
      return Promise.resolve();
    } catch (error) {
      this.log('Error shutting down NovaCore adapter:', error);
      this.emit('error', error);
      return Promise.reject(error);
    }
  }
}

module.exports = {
  NovaCoreAdapter,
  NovaCoreAdapterStatus
};
