/**
 * Brute Force Protection Routes
 */

const express = require('express');
const router = express.Router();
const BruteForceController = require('../controllers/BruteForceController');
const { authenticate, hasRole } = require('../middleware/authMiddleware');

// All routes require authentication and admin role
router.use(authenticate);
router.use(hasRole('admin'));

// Get brute force protection configuration
router.get('/config', (req, res, next) => {
  BruteForceController.getConfig(req, res, next);
});

// Update brute force protection configuration
router.put('/config', (req, res, next) => {
  BruteForceController.updateConfig(req, res, next);
});

// Reset brute force protection for a specific identifier
router.post('/reset', (req, res, next) => {
  BruteForceController.resetAttempts(req, res, next);
});

module.exports = router;
