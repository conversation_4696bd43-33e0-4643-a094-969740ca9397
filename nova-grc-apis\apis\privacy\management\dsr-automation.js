/**
 * Data Subject Rights Automation
 * 
 * This module provides functionality for automating data subject rights requests.
 */

const models = require('./models');
const { v4: uuidv4 } = require('uuid');

/**
 * Request status values
 */
const REQUEST_STATUS = {
  PENDING: 'pending',
  IN_PROGRESS: 'in-progress',
  COMPLETED: 'completed',
  REJECTED: 'rejected',
  WITHDRAWN: 'withdrawn'
};

/**
 * Request types
 */
const REQUEST_TYPES = {
  ACCESS: 'access',
  RECTIFICATION: 'rectification',
  ERASURE: 'erasure',
  RESTRICTION: 'restriction',
  PORTABILITY: 'portability',
  OBJECTION: 'objection',
  AUTOMATED_DECISION: 'automated-decision',
  WITHDRAW_CONSENT: 'withdraw-consent',
  OTHER: 'other'
};

/**
 * Data systems
 */
const DATA_SYSTEMS = [
  {
    id: 'crm',
    name: 'Customer Relationship Management',
    description: 'System for managing customer relationships',
    dataCategories: ['Contact information', 'Account details', 'Transaction history'],
    supportsAutomation: true,
    supportedRequestTypes: [
      REQUEST_TYPES.ACCESS,
      REQUEST_TYPES.RECTIFICATION,
      REQUEST_TYPES.ERASURE,
      REQUEST_TYPES.RESTRICTION
    ]
  },
  {
    id: 'marketing',
    name: 'Marketing Database',
    description: 'System for managing marketing campaigns and preferences',
    dataCategories: ['Contact information', 'Marketing preferences', 'Campaign history'],
    supportsAutomation: true,
    supportedRequestTypes: [
      REQUEST_TYPES.ACCESS,
      REQUEST_TYPES.ERASURE,
      REQUEST_TYPES.OBJECTION
    ]
  },
  {
    id: 'analytics',
    name: 'Analytics Platform',
    description: 'System for analyzing user behavior',
    dataCategories: ['Usage data', 'Device information', 'Behavioral data'],
    supportsAutomation: false,
    supportedRequestTypes: [
      REQUEST_TYPES.ACCESS,
      REQUEST_TYPES.ERASURE
    ]
  },
  {
    id: 'support',
    name: 'Customer Support System',
    description: 'System for managing customer support tickets',
    dataCategories: ['Contact information', 'Support history', 'Communication records'],
    supportsAutomation: true,
    supportedRequestTypes: [
      REQUEST_TYPES.ACCESS,
      REQUEST_TYPES.RECTIFICATION,
      REQUEST_TYPES.ERASURE
    ]
  },
  {
    id: 'billing',
    name: 'Billing System',
    description: 'System for managing billing and payments',
    dataCategories: ['Contact information', 'Financial information', 'Transaction history'],
    supportsAutomation: false,
    supportedRequestTypes: [
      REQUEST_TYPES.ACCESS
    ]
  }
];

/**
 * Get all data systems
 * @returns {Array} - Array of data systems
 */
const getDataSystems = () => {
  return DATA_SYSTEMS;
};

/**
 * Get a data system by ID
 * @param {string} id - Data system ID
 * @returns {Object|null} - The data system or null if not found
 */
const getDataSystemById = (id) => {
  return DATA_SYSTEMS.find(system => system.id === id) || null;
};

/**
 * Get data systems that support a specific request type
 * @param {string} requestType - Request type
 * @returns {Array} - Array of data systems that support the request type
 */
const getDataSystemsByRequestType = (requestType) => {
  return DATA_SYSTEMS.filter(system => 
    system.supportedRequestTypes.includes(requestType)
  );
};

/**
 * Get data systems that contain specific data categories
 * @param {Array} dataCategories - Data categories
 * @returns {Array} - Array of data systems that contain the data categories
 */
const getDataSystemsByDataCategories = (dataCategories) => {
  return DATA_SYSTEMS.filter(system => 
    system.dataCategories.some(category => 
      dataCategories.includes(category)
    )
  );
};

/**
 * Create a new data subject request
 * @param {Object} requestData - The request data
 * @returns {Object} - The created request
 */
const createDataSubjectRequest = (requestData) => {
  const {
    requestType,
    dataSubjectName,
    dataSubjectEmail,
    dataSubjectId,
    identityVerified,
    verificationMethod,
    verificationDate,
    requestDetails,
    status,
    assignedTo,
    dueDate,
    affectedSystems,
    notes
  } = requestData;

  // Create a new request with a unique ID
  const newRequest = {
    id: `dsr-${uuidv4().substring(0, 4)}`,
    requestType,
    requestDate: new Date().toISOString(),
    dataSubjectName,
    dataSubjectEmail,
    dataSubjectId: dataSubjectId || null,
    identityVerified: identityVerified || false,
    verificationMethod: verificationMethod || null,
    verificationDate: verificationDate || null,
    requestDetails,
    status: status || REQUEST_STATUS.PENDING,
    assignedTo: assignedTo || null,
    dueDate: dueDate || null,
    completionDate: null,
    responseDetails: null,
    affectedSystems: affectedSystems || [],
    notes: notes || '',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    automationStatus: {
      automated: false,
      systemStatuses: []
    }
  };

  // Add the new request to the collection
  models.dataSubjectRequests.push(newRequest);

  return newRequest;
};

/**
 * Determine affected systems for a request
 * @param {Object} request - The data subject request
 * @returns {Array} - Array of affected systems
 */
const determineAffectedSystems = (request) => {
  // Get systems that support the request type
  const supportedSystems = getDataSystemsByRequestType(request.requestType);
  
  // If the request already has affected systems, filter by those
  if (request.affectedSystems && request.affectedSystems.length > 0) {
    return supportedSystems.filter(system => 
      request.affectedSystems.includes(system.id)
    );
  }
  
  // Otherwise, return all supported systems
  return supportedSystems;
};

/**
 * Execute an automated request in a data system
 * @param {Object} request - The data subject request
 * @param {Object} system - The data system
 * @returns {Object} - The execution result
 */
const executeAutomatedRequest = (request, system) => {
  // In a real implementation, this would call the data system's API
  // For now, we'll simulate the execution
  
  // Check if the system supports automation
  if (!system.supportsAutomation) {
    return {
      success: false,
      automated: false,
      message: `System ${system.name} does not support automation`,
      details: null
    };
  }
  
  // Check if the system supports the request type
  if (!system.supportedRequestTypes.includes(request.requestType)) {
    return {
      success: false,
      automated: false,
      message: `System ${system.name} does not support request type ${request.requestType}`,
      details: null
    };
  }
  
  // Simulate execution based on request type
  switch (request.requestType) {
    case REQUEST_TYPES.ACCESS:
      return {
        success: true,
        automated: true,
        message: `Successfully retrieved data from ${system.name}`,
        details: {
          dataUrl: `https://example.com/data-exports/${request.id}/${system.id}.json`,
          dataFormat: 'JSON',
          dataSize: '1.2 MB',
          dataTimestamp: new Date().toISOString()
        }
      };
      
    case REQUEST_TYPES.RECTIFICATION:
      return {
        success: true,
        automated: true,
        message: `Successfully updated data in ${system.name}`,
        details: {
          updatedFields: ['name', 'email', 'address'],
          updateTimestamp: new Date().toISOString()
        }
      };
      
    case REQUEST_TYPES.ERASURE:
      return {
        success: true,
        automated: true,
        message: `Successfully deleted data from ${system.name}`,
        details: {
          deletedCategories: system.dataCategories,
          deletionTimestamp: new Date().toISOString(),
          retentionExceptions: []
        }
      };
      
    case REQUEST_TYPES.RESTRICTION:
      return {
        success: true,
        automated: true,
        message: `Successfully restricted processing in ${system.name}`,
        details: {
          restrictedOperations: ['marketing', 'profiling', 'automated-decision-making'],
          restrictionTimestamp: new Date().toISOString()
        }
      };
      
    case REQUEST_TYPES.OBJECTION:
      return {
        success: true,
        automated: true,
        message: `Successfully registered objection in ${system.name}`,
        details: {
          objectionScope: 'all-processing',
          objectionTimestamp: new Date().toISOString()
        }
      };
      
    default:
      return {
        success: false,
        automated: false,
        message: `Request type ${request.requestType} not supported for automation in ${system.name}`,
        details: null
      };
  }
};

/**
 * Process a data subject request
 * @param {string} requestId - The request ID
 * @returns {Object} - The processing result
 */
const processDataSubjectRequest = (requestId) => {
  // Find the request
  const requestIndex = models.dataSubjectRequests.findIndex(r => r.id === requestId);
  
  if (requestIndex === -1) {
    return {
      success: false,
      message: `Request with ID ${requestId} not found`
    };
  }
  
  const request = models.dataSubjectRequests[requestIndex];
  
  // Check if the request is in a state that can be processed
  if (request.status !== REQUEST_STATUS.PENDING && request.status !== REQUEST_STATUS.IN_PROGRESS) {
    return {
      success: false,
      message: `Request with ID ${requestId} cannot be processed (status: ${request.status})`
    };
  }
  
  // Determine affected systems
  const affectedSystems = determineAffectedSystems(request);
  
  // Execute the request in each system
  const systemStatuses = affectedSystems.map(system => {
    const executionResult = executeAutomatedRequest(request, system);
    
    return {
      systemId: system.id,
      systemName: system.name,
      ...executionResult
    };
  });
  
  // Update the request with the results
  const automatedSystems = systemStatuses.filter(status => status.automated);
  const allSuccessful = systemStatuses.every(status => status.success);
  const someAutomated = automatedSystems.length > 0;
  const allAutomated = someAutomated && automatedSystems.length === systemStatuses.length;
  
  const updatedRequest = {
    ...request,
    status: allSuccessful ? REQUEST_STATUS.COMPLETED : REQUEST_STATUS.IN_PROGRESS,
    completionDate: allSuccessful ? new Date().toISOString() : null,
    responseDetails: allSuccessful 
      ? `Request processed successfully in all systems` 
      : `Request processed partially. Some systems require manual intervention.`,
    affectedSystems: affectedSystems.map(system => system.id),
    updatedAt: new Date().toISOString(),
    automationStatus: {
      automated: someAutomated,
      fullyAutomated: allAutomated,
      systemStatuses
    }
  };
  
  // Update the request in the collection
  models.dataSubjectRequests[requestIndex] = updatedRequest;
  
  return {
    success: true,
    message: allSuccessful 
      ? `Request processed successfully in all systems` 
      : `Request processed partially. Some systems require manual intervention.`,
    request: updatedRequest
  };
};

/**
 * Generate a data export for a data subject
 * @param {string} requestId - The request ID
 * @returns {Object} - The data export
 */
const generateDataExport = (requestId) => {
  // Find the request
  const request = models.dataSubjectRequests.find(r => r.id === requestId);
  
  if (!request) {
    return {
      success: false,
      message: `Request with ID ${requestId} not found`
    };
  }
  
  // Check if the request is an access request
  if (request.requestType !== REQUEST_TYPES.ACCESS) {
    return {
      success: false,
      message: `Request with ID ${requestId} is not an access request`
    };
  }
  
  // Get the affected systems
  const affectedSystems = request.affectedSystems
    .map(systemId => getDataSystemById(systemId))
    .filter(system => system !== null);
  
  // Generate data export
  const dataExport = {
    requestId: request.id,
    dataSubjectId: request.dataSubjectId,
    dataSubjectName: request.dataSubjectName,
    dataSubjectEmail: request.dataSubjectEmail,
    exportDate: new Date().toISOString(),
    systems: affectedSystems.map(system => {
      // In a real implementation, this would retrieve actual data from the system
      // For now, we'll generate sample data
      return {
        systemId: system.id,
        systemName: system.name,
        data: generateSampleData(system, request)
      };
    })
  };
  
  return {
    success: true,
    message: 'Data export generated successfully',
    dataExport
  };
};

/**
 * Generate sample data for a data export
 * @param {Object} system - The data system
 * @param {Object} request - The data subject request
 * @returns {Object} - The sample data
 */
const generateSampleData = (system, request) => {
  // Generate sample data based on the system's data categories
  const sampleData = {};
  
  system.dataCategories.forEach(category => {
    switch (category) {
      case 'Contact information':
        sampleData[category] = {
          name: request.dataSubjectName,
          email: request.dataSubjectEmail,
          phone: '+**********',
          address: '123 Main St, Anytown, USA'
        };
        break;
        
      case 'Account details':
        sampleData[category] = {
          accountId: request.dataSubjectId || 'ACC12345',
          accountType: 'Standard',
          accountStatus: 'Active',
          creationDate: '2022-01-15T10:30:00Z',
          lastLoginDate: '2023-06-20T15:45:00Z'
        };
        break;
        
      case 'Transaction history':
        sampleData[category] = [
          {
            transactionId: 'TRX001',
            date: '2023-05-10T09:15:00Z',
            amount: 125.50,
            description: 'Product purchase'
          },
          {
            transactionId: 'TRX002',
            date: '2023-05-25T14:30:00Z',
            amount: 75.00,
            description: 'Subscription renewal'
          }
        ];
        break;
        
      case 'Marketing preferences':
        sampleData[category] = {
          emailMarketing: true,
          smsMarketing: false,
          postalMarketing: true,
          lastUpdated: '2023-04-12T11:20:00Z'
        };
        break;
        
      case 'Campaign history':
        sampleData[category] = [
          {
            campaignId: 'CAM001',
            name: 'Spring Sale 2023',
            sentDate: '2023-03-15T08:00:00Z',
            opened: true,
            clicked: true
          },
          {
            campaignId: 'CAM002',
            name: 'Summer Newsletter',
            sentDate: '2023-06-01T08:00:00Z',
            opened: true,
            clicked: false
          }
        ];
        break;
        
      case 'Usage data':
        sampleData[category] = {
          lastVisit: '2023-06-20T15:45:00Z',
          totalVisits: 45,
          averageSessionDuration: '00:12:30',
          mostVisitedPages: ['/products', '/account', '/support']
        };
        break;
        
      case 'Device information':
        sampleData[category] = {
          browser: 'Chrome 114.0.5735.198',
          operatingSystem: 'Windows 10',
          deviceType: 'Desktop',
          screenResolution: '1920x1080'
        };
        break;
        
      case 'Behavioral data':
        sampleData[category] = {
          interests: ['Technology', 'Sports', 'Travel'],
          clickPatterns: 'Primarily product pages and support articles',
          purchaseFrequency: 'Monthly'
        };
        break;
        
      case 'Support history':
        sampleData[category] = [
          {
            ticketId: 'TKT001',
            subject: 'Account access issue',
            creationDate: '2023-04-05T10:15:00Z',
            status: 'Resolved',
            resolution: 'Password reset'
          },
          {
            ticketId: 'TKT002',
            subject: 'Billing question',
            creationDate: '2023-05-20T14:30:00Z',
            status: 'Resolved',
            resolution: 'Explained billing cycle'
          }
        ];
        break;
        
      case 'Communication records':
        sampleData[category] = [
          {
            communicationId: 'COM001',
            type: 'Email',
            date: '2023-04-05T10:20:00Z',
            subject: 'RE: Account access issue',
            direction: 'Outbound'
          },
          {
            communicationId: 'COM002',
            type: 'Phone',
            date: '2023-05-20T14:35:00Z',
            subject: 'Billing question',
            direction: 'Inbound',
            duration: '00:08:45'
          }
        ];
        break;
        
      case 'Financial information':
        sampleData[category] = {
          paymentMethods: [
            {
              type: 'Credit Card',
              lastFour: '1234',
              expiryDate: '12/25',
              billingAddress: '123 Main St, Anytown, USA'
            }
          ],
          billingCycle: 'Monthly',
          currentBalance: 0.00
        };
        break;
        
      default:
        sampleData[category] = {
          note: `Sample data for ${category} not available`
        };
    }
  });
  
  return sampleData;
};

module.exports = {
  REQUEST_STATUS,
  REQUEST_TYPES,
  DATA_SYSTEMS,
  getDataSystems,
  getDataSystemById,
  getDataSystemsByRequestType,
  getDataSystemsByDataCategories,
  createDataSubjectRequest,
  determineAffectedSystems,
  executeAutomatedRequest,
  processDataSubjectRequest,
  generateDataExport
};
