#!/usr/bin/env python3
# UUFT Testing Framework
# This script sets up the directory structure and coordinates the testing of UUFT patterns
# across cosmological, biological, social, and technological domains.

import os
import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime
import requests
import zipfile
import io
import logging

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("uuft_testing.log"),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger("UUFT_Framework")

# Base directory for all data
BASE_DIR = "D:/Archives"

# Subdirectories for each domain
COSMOLOGICAL_DIR = os.path.join(BASE_DIR, "Cosmological")
BIOLOGICAL_DIR = os.path.join(BASE_DIR, "Biological")
SOCIAL_DIR = os.path.join(BASE_DIR, "Social")
TECHNOLOGICAL_DIR = os.path.join(BASE_DIR, "Technological")
RESULTS_DIR = os.path.join(BASE_DIR, "Results")

# Create directory structure
def create_directory_structure():
    """Create the directory structure for the UUFT testing framework."""
    logger.info("Creating directory structure...")
    
    for directory in [BASE_DIR, COSMOLOGICAL_DIR, BIOLOGICAL_DIR, SOCIAL_DIR, 
                     TECHNOLOGICAL_DIR, RESULTS_DIR]:
        try:
            if not os.path.exists(directory):
                os.makedirs(directory)
                logger.info(f"Created directory: {directory}")
            else:
                logger.info(f"Directory already exists: {directory}")
        except Exception as e:
            logger.error(f"Error creating directory {directory}: {e}")
            
    logger.info("Directory structure setup complete.")

# Dataset URLs
DATASET_URLS = {
    "cosmological": {
        "planck_params": "https://pla.esac.esa.int/pla/aio/product-action?COSMOLOGY.FILE_ID=COM_CosmoParams_fullGrid_R3.00.zip",
        "wmap_params": "https://lambda.gsfc.nasa.gov/data/map/dr5/dcp/wmap_mcmc_pars_wmap9_spt_act.txt"
    },
    "biological": {
        "gene_expression": "https://ftp.ncbi.nlm.nih.gov/geo/series/GSE30nnn/GSE30272/suppl/GSE30272_ExprsMtxCleanedN269_31SVN.txt.gz"
    },
    "social": {
        "gini_index": "https://api.worldbank.org/v2/en/indicator/SI.POV.GINI?downloadformat=csv"
    },
    "technological": {
        "network_traffic": "https://www.kaggle.com/datasets/jsrojas/ip-network-traffic-flows-labeled-with-87-apps/download"
    }
}

# Download datasets
def download_dataset(url, destination_dir, filename=None):
    """Download a dataset from a URL to the specified directory."""
    try:
        logger.info(f"Downloading dataset from {url}...")
        
        # Create destination directory if it doesn't exist
        if not os.path.exists(destination_dir):
            os.makedirs(destination_dir)
        
        # Determine filename from URL if not provided
        if filename is None:
            filename = url.split('/')[-1]
            if '?' in filename:
                filename = filename.split('?')[0]
        
        destination_path = os.path.join(destination_dir, filename)
        
        # Check if file already exists
        if os.path.exists(destination_path):
            logger.info(f"File already exists: {destination_path}")
            return destination_path
        
        # Download the file
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        # Save the file
        with open(destination_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        
        logger.info(f"Downloaded dataset to {destination_path}")
        return destination_path
    
    except Exception as e:
        logger.error(f"Error downloading dataset from {url}: {e}")
        return None

# Download all datasets
def download_all_datasets():
    """Download all datasets for UUFT testing."""
    logger.info("Downloading all datasets...")
    
    # Cosmological datasets
    for name, url in DATASET_URLS["cosmological"].items():
        download_dataset(url, COSMOLOGICAL_DIR, f"{name}.txt")
    
    # Biological datasets
    for name, url in DATASET_URLS["biological"].items():
        download_dataset(url, BIOLOGICAL_DIR, f"{name}.txt.gz")
    
    # Social datasets
    for name, url in DATASET_URLS["social"].items():
        download_dataset(url, SOCIAL_DIR, f"{name}.csv")
    
    # Technological datasets
    for name, url in DATASET_URLS["technological"].items():
        download_dataset(url, TECHNOLOGICAL_DIR, f"{name}.zip")
    
    logger.info("Dataset downloads complete.")

# Main function
def main():
    """Main function to set up and run the UUFT testing framework."""
    logger.info("Starting UUFT Testing Framework...")
    
    # Create directory structure
    create_directory_structure()
    
    # Download datasets
    download_all_datasets()
    
    logger.info("UUFT Testing Framework setup complete.")

if __name__ == "__main__":
    main()
