/**
 * Comphyological Tensor Core
 * 
 * This module implements the Comphyological Tensor Core, which integrates
 * the Ψ Tensor Core, Dynamic Weighting Protocol, and Energy Calculator
 * to provide a unified interface for the Comphyological framework.
 */

const { performance } = require('perf_hooks');
const PsiTensorCore = require('./psi_tensor_core');
const DynamicWeightingProtocol = require('./dynamic_weighting');
const EnergyCalculator = require('./energy_calculator');
const TensorOperations = require('./tensor_operations');

/**
 * ComphyologicalTensorCore class
 * 
 * Implements the Comphyological Tensor Core.
 */
class ComphyologicalTensorCore {
  /**
   * Constructor
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    this.options = {
      enableLogging: true,
      strictMode: false,
      useGPU: false,
      useDynamicWeighting: true,
      precision: 6,
      normalizationFactor: 166000,
      ...options
    };

    // Initialize components
    this.psiTensorCore = new PsiTensorCore({
      enableLogging: this.options.enableLogging,
      useGPU: this.options.useGPU,
      useDynamicWeighting: this.options.useDynamicWeighting,
      precision: this.options.precision
    });

    this.dynamicWeighting = new DynamicWeightingProtocol({
      enableLogging: this.options.enableLogging,
      ...options.dynamicWeightingOptions
    });

    this.energyCalculator = new EnergyCalculator({
      enableLogging: this.options.enableLogging,
      precision: this.options.precision,
      normalizationFactor: this.options.normalizationFactor
    });

    this.tensorOps = new TensorOperations({
      enableLogging: this.options.enableLogging,
      precision: this.options.precision
    });

    // Initialize metrics
    this.metrics = {
      processingTime: 0,
      fusionCount: 0,
      comphyonValues: [],
      lastComphyonValue: 0,
      averageComphyonValue: 0,
      maxComphyonValue: 0,
      minComphyonValue: 0,
      startTime: Date.now()
    };

    if (this.options.enableLogging) {
      console.log('ComphyologicalTensorCore initialized with options:', {
        strictMode: this.options.strictMode,
        useGPU: this.options.useGPU,
        useDynamicWeighting: this.options.useDynamicWeighting,
        precision: this.options.precision,
        normalizationFactor: this.options.normalizationFactor
      });
    }
  }

  /**
   * Process data from all three domains
   * @param {Object} csdeData - CSDE data
   * @param {Object} csfeData - CSFE data
   * @param {Object} csmeData - CSME data
   * @returns {Object} - Processing result
   */
  processData(csdeData, csfeData, csmeData) {
    const startTime = performance.now();

    try {
      // Validate input data
      this._validateInputData(csdeData, csfeData, csmeData);

      // Create tensors
      const csdeTensor = this._createCsdeTensor(csdeData);
      const csfeTensor = this._createCsfeTensor(csfeData);
      const csmeTensor = this._createCsmeTensor(csmeData);

      // Prepare metrics for dynamic weighting
      const metrics = {
        csde: this._extractMetrics(csdeData, 'csde'),
        csfe: this._extractMetrics(csfeData, 'csfe'),
        csme: this._extractMetrics(csmeData, 'csme')
      };

      // Fuse engines
      const fusedTensor = this.psiTensorCore.fuseEngines(
        csdeTensor,
        csfeTensor,
        csmeTensor,
        metrics
      );

      // Calculate Comphyon value
      const comphyonResult = this.energyCalculator.calculateComphyon(
        csdeData,
        csfeData,
        csmeData,
        fusedTensor
      );

      // Extract action from fused tensor
      const actionResult = this.psiTensorCore.extractAction(fusedTensor);

      // Update metrics
      this._updateMetrics(comphyonResult.comphyonValue, startTime);

      // Create result
      const result = {
        fusedTensor,
        comphyon: comphyonResult.comphyonValue,
        action: actionResult.action,
        confidence: actionResult.confidence,
        energies: comphyonResult.energies,
        weights: this.dynamicWeighting.getWeights(),
        metrics: { ...this.metrics },
        timestamp: Date.now(),
        processingTime: performance.now() - startTime
      };

      if (this.options.enableLogging) {
        console.log(`Data processed in ${result.processingTime}ms`);
        console.log(`Comphyon value: ${result.comphyon}`);
        console.log(`Action: ${result.action} (Confidence: ${result.confidence})`);
      }

      return result;
    } catch (error) {
      if (this.options.enableLogging) {
        console.error('Error processing data:', error);
      }

      if (this.options.strictMode) {
        throw error;
      }

      return {
        error: error.message,
        timestamp: Date.now(),
        processingTime: performance.now() - startTime
      };
    }
  }

  /**
   * Validate input data
   * @param {Object} csdeData - CSDE data
   * @param {Object} csfeData - CSFE data
   * @param {Object} csmeData - CSME data
   * @private
   */
  _validateInputData(csdeData, csfeData, csmeData) {
    if (!csdeData || typeof csdeData !== 'object') {
      throw new Error('Invalid CSDE data');
    }

    if (!csfeData || typeof csfeData !== 'object') {
      throw new Error('Invalid CSFE data');
    }

    if (!csmeData || typeof csmeData !== 'object') {
      throw new Error('Invalid CSME data');
    }
  }

  /**
   * Create CSDE tensor
   * @param {Object} csdeData - CSDE data
   * @returns {Object} - CSDE tensor
   * @private
   */
  _createCsdeTensor(csdeData) {
    return this.psiTensorCore.createCsdeTensor(
      csdeData.governance || 0.5,
      csdeData.dataQuality || csdeData.data || 0.5,
      csdeData.action || 'allow',
      csdeData.confidence || 0.5
    );
  }

  /**
   * Create CSFE tensor
   * @param {Object} csfeData - CSFE data
   * @returns {Object} - CSFE tensor
   * @private
   */
  _createCsfeTensor(csfeData) {
    return this.psiTensorCore.createCsfeTensor(
      csfeData.risk || 0.5,
      csfeData.policyCompliance || csfeData.finance || 0.5,
      csfeData.action || 'allow',
      csfeData.confidence || 0.5
    );
  }

  /**
   * Create CSME tensor
   * @param {Object} csmeData - CSME data
   * @returns {Object} - CSME tensor
   * @private
   */
  _createCsmeTensor(csmeData) {
    return this.psiTensorCore.createCsmeTensor(
      csmeData.trustFactor || csmeData.bio || 0.5,
      csmeData.integrityFactor || csmeData.medCompliance || 0.5,
      csmeData.action || 'allow',
      csmeData.confidence || 0.5
    );
  }

  /**
   * Extract metrics for dynamic weighting
   * @param {Object} data - Domain data
   * @param {string} domain - Domain name
   * @returns {Object} - Metrics
   * @private
   */
  _extractMetrics(data, domain) {
    switch (domain) {
      case 'csde':
        return {
          governanceScore: data.governance || 0.5,
          dataQualityScore: data.dataQuality || data.data || 0.5,
          actionConfidence: data.confidence || 0.5
        };
      case 'csfe':
        return {
          riskScore: data.risk || 0.5,
          financialScore: data.policyCompliance || data.finance || 0.5,
          actionConfidence: data.confidence || 0.5
        };
      case 'csme':
        return {
          bioScore: data.trustFactor || data.bio || 0.5,
          medComplianceScore: data.integrityFactor || data.medCompliance || 0.5,
          actionConfidence: data.confidence || 0.5
        };
      default:
        return {};
    }
  }

  /**
   * Update metrics
   * @param {number} comphyonValue - Comphyon value
   * @param {number} startTime - Start time
   * @private
   */
  _updateMetrics(comphyonValue, startTime) {
    // Update fusion count
    this.metrics.fusionCount++;

    // Update Comphyon values
    this.metrics.comphyonValues.push(comphyonValue);
    this.metrics.lastComphyonValue = comphyonValue;

    // Limit array size
    if (this.metrics.comphyonValues.length > 100) {
      this.metrics.comphyonValues.shift();
    }

    // Calculate statistics
    this.metrics.averageComphyonValue = this.metrics.comphyonValues.reduce((sum, val) => sum + val, 0) / this.metrics.comphyonValues.length;
    this.metrics.maxComphyonValue = Math.max(...this.metrics.comphyonValues);
    this.metrics.minComphyonValue = Math.min(...this.metrics.comphyonValues);

    // Update processing time
    this.metrics.processingTime = performance.now() - startTime;
  }

  /**
   * Get metrics
   * @returns {Object} - Metrics
   */
  getMetrics() {
    return { ...this.metrics };
  }

  /**
   * Reset metrics
   */
  resetMetrics() {
    this.metrics = {
      processingTime: 0,
      fusionCount: 0,
      comphyonValues: [],
      lastComphyonValue: 0,
      averageComphyonValue: 0,
      maxComphyonValue: 0,
      minComphyonValue: 0,
      startTime: Date.now()
    };
  }
}

/**
 * Create a Comphyological Tensor Core with recommended settings
 * @param {Object} options - Configuration options
 * @returns {ComphyologicalTensorCore} - Configured Comphyological Tensor Core
 */
function createComphyologicalTensorCore(options = {}) {
  return new ComphyologicalTensorCore({
    enableLogging: true,
    strictMode: false,
    useGPU: false,
    useDynamicWeighting: true,
    precision: 6,
    normalizationFactor: 166000,
    ...options
  });
}

module.exports = {
  ComphyologicalTensorCore,
  createComphyologicalTensorCore
};
