"""
Fine-tune a language model for compliance-specific tasks.

This script fine-tunes a pre-trained language model on compliance Q&A data.
"""

import os
import argparse
import logging
from typing import Optional

import torch
from transformers import (
    AutoModelForCausalLM,
    AutoTokenizer,
    Trainer,
    TrainingArguments,
    DataCollatorForLanguageModeling,
    TextDataset
)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_dataset(file_path: str, tokenizer, block_size: int = 128):
    """
    Load a dataset for training or evaluation.
    
    Args:
        file_path: Path to the dataset file
        tokenizer: Tokenizer to use for encoding
        block_size: Maximum sequence length
        
    Returns:
        TextDataset instance
    """
    logger.info(f"Loading dataset from {file_path}")
    return TextDataset(
        tokenizer=tokenizer,
        file_path=file_path,
        block_size=block_size
    )

def fine_tune(
    model_name: str,
    train_file: str,
    val_file: str,
    output_dir: str,
    num_train_epochs: int = 3,
    per_device_train_batch_size: int = 8,
    learning_rate: float = 5e-5,
    weight_decay: float = 0.01,
    warmup_steps: int = 500,
    logging_steps: int = 100,
    save_steps: int = 1000,
    block_size: int = 128,
    fp16: bool = False,
    use_gpu: bool = True
):
    """
    Fine-tune a language model on compliance data.
    
    Args:
        model_name: Name or path of the pre-trained model
        train_file: Path to the training data file
        val_file: Path to the validation data file
        output_dir: Directory to save the fine-tuned model
        num_train_epochs: Number of training epochs
        per_device_train_batch_size: Batch size per device during training
        learning_rate: Learning rate
        weight_decay: Weight decay
        warmup_steps: Number of warmup steps
        logging_steps: Number of steps between logging
        save_steps: Number of steps between saving checkpoints
        block_size: Maximum sequence length
        fp16: Whether to use 16-bit precision
        use_gpu: Whether to use GPU if available
    """
    # Check if GPU is available
    device = torch.device("cuda" if torch.cuda.is_available() and use_gpu else "cpu")
    logger.info(f"Using device: {device}")
    
    # Load tokenizer and model
    logger.info(f"Loading tokenizer and model: {model_name}")
    tokenizer = AutoTokenizer.from_pretrained(model_name)
    model = AutoModelForCausalLM.from_pretrained(model_name).to(device)
    
    # Load datasets
    train_dataset = load_dataset(train_file, tokenizer, block_size)
    val_dataset = load_dataset(val_file, tokenizer, block_size)
    
    # Create data collator
    data_collator = DataCollatorForLanguageModeling(
        tokenizer=tokenizer,
        mlm=False  # We're not using masked language modeling
    )
    
    # Set up training arguments
    training_args = TrainingArguments(
        output_dir=output_dir,
        overwrite_output_dir=True,
        num_train_epochs=num_train_epochs,
        per_device_train_batch_size=per_device_train_batch_size,
        learning_rate=learning_rate,
        weight_decay=weight_decay,
        warmup_steps=warmup_steps,
        logging_steps=logging_steps,
        save_steps=save_steps,
        evaluation_strategy="steps",
        eval_steps=save_steps,
        save_total_limit=2,
        fp16=fp16,
        prediction_loss_only=True,
    )
    
    # Initialize trainer
    trainer = Trainer(
        model=model,
        args=training_args,
        data_collator=data_collator,
        train_dataset=train_dataset,
        eval_dataset=val_dataset
    )
    
    # Train the model
    logger.info("Starting fine-tuning")
    trainer.train()
    
    # Save the final model and tokenizer
    logger.info(f"Saving fine-tuned model to {output_dir}")
    trainer.save_model()
    tokenizer.save_pretrained(output_dir)
    
    logger.info("Fine-tuning complete")

def main():
    parser = argparse.ArgumentParser(description='Fine-tune a language model for compliance-specific tasks')
    parser.add_argument('--model_name', type=str, default='gpt2', help='Name or path of the pre-trained model')
    parser.add_argument('--train_file', type=str, required=True, help='Path to the training data file')
    parser.add_argument('--val_file', type=str, required=True, help='Path to the validation data file')
    parser.add_argument('--output_dir', type=str, default='./fine_tuned_model', help='Directory to save the fine-tuned model')
    parser.add_argument('--num_train_epochs', type=int, default=3, help='Number of training epochs')
    parser.add_argument('--per_device_train_batch_size', type=int, default=8, help='Batch size per device during training')
    parser.add_argument('--learning_rate', type=float, default=5e-5, help='Learning rate')
    parser.add_argument('--weight_decay', type=float, default=0.01, help='Weight decay')
    parser.add_argument('--warmup_steps', type=int, default=500, help='Number of warmup steps')
    parser.add_argument('--logging_steps', type=int, default=100, help='Number of steps between logging')
    parser.add_argument('--save_steps', type=int, default=1000, help='Number of steps between saving checkpoints')
    parser.add_argument('--block_size', type=int, default=128, help='Maximum sequence length')
    parser.add_argument('--fp16', action='store_true', help='Whether to use 16-bit precision')
    parser.add_argument('--no_gpu', action='store_true', help='Disable GPU usage even if available')
    args = parser.parse_args()
    
    # Create output directory if it doesn't exist
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Fine-tune the model
    fine_tune(
        model_name=args.model_name,
        train_file=args.train_file,
        val_file=args.val_file,
        output_dir=args.output_dir,
        num_train_epochs=args.num_train_epochs,
        per_device_train_batch_size=args.per_device_train_batch_size,
        learning_rate=args.learning_rate,
        weight_decay=args.weight_decay,
        warmup_steps=args.warmup_steps,
        logging_steps=args.logging_steps,
        save_steps=args.save_steps,
        block_size=args.block_size,
        fp16=args.fp16,
        use_gpu=not args.no_gpu
    )

if __name__ == "__main__":
    main()
