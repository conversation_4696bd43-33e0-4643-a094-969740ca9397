/**
 * NovaCortex Test Server
 * 
 * This server provides test endpoints for NovaCortex functionality
 * including coherence testing, CASTL decision making, and π-Rhythm synchronization.
 */

const express = require('express');
const cors = require('cors');
const morgan = require('morgan');
const client = require('prom-client');

const app = express();
const port = process.env.PORT || 3010;

// Middleware
app.use(cors());
app.use(morgan('combined'));
app.use(express.json());

// Prometheus metrics
const register = new client.Registry();
client.collectDefaultMetrics({ register });

// Custom metrics
const coherenceGauge = new client.Gauge({
    name: 'novacortex_coherence_level',
    help: 'Current coherence level of NovaCortex system',
    registers: [register]
});

const castlDecisionCounter = new client.Counter({
    name: 'novacortex_castl_decisions_total',
    help: 'Total number of CASTL decisions made',
    registers: [register]
});

const piRhythmDeviationGauge = new client.Gauge({
    name: 'novacortex_pi_rhythm_deviation',
    help: 'Current π-Rhythm deviation',
    registers: [register]
});

// Initialize metrics
coherenceGauge.set(0.98);
piRhythmDeviationGauge.set(0.05);

// Health check endpoint
app.get('/health', (req, res) => {
    res.status(200).json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        components: {
            coherence: 'operational',
            castl: 'operational',
            pi_rhythm: 'operational'
        }
    });
});

// NovaCortex API endpoints
app.get('/api/novacortex/coherence', async (req, res) => {
    try {
        // Simulate coherence calculation with slight variation
        const baseCoherence = 0.98;
        const variation = (Math.random() - 0.5) * 0.04; // ±2% variation
        const coherenceLevel = Math.max(0.95, Math.min(1.0, baseCoherence + variation));
        
        coherenceGauge.set(coherenceLevel);
        
        res.json({
            coherence_level: coherenceLevel,
            timestamp: new Date().toISOString(),
            status: coherenceLevel >= 0.95 ? 'stable' : 'degraded',
            equation: '∂Ψ = 0 (coherence maintained)'
        });
    } catch (error) {
        res.status(500).json({
            error: 'Coherence calculation failed',
            message: error.message
        });
    }
});

app.post('/api/novacortex/castl/decide', async (req, res) => {
    try {
        const { scenario } = req.body;
        
        if (!scenario || !scenario.type || !scenario.options) {
            return res.status(400).json({
                error: 'Invalid scenario',
                message: 'Scenario must include type and options'
            });
        }
        
        castlDecisionCounter.inc();
        
        // Simple decision logic based on scenario type
        let decision, reasoning, principles;
        
        if (scenario.type === 'trolley') {
            decision = 'pull_lever';
            reasoning = 'Utilitarian calculation: minimize total harm (1 vs 5 lives)';
            principles = ['utilitarian_calculus', 'harm_minimization'];
        } else if (scenario.type === 'resource_allocation') {
            decision = 'prioritize_critical';
            reasoning = 'Critical needs take precedence to prevent catastrophic outcomes';
            principles = ['priority_based_allocation', 'critical_first'];
        } else {
            // Default to first option with general reasoning
            decision = scenario.options[0];
            reasoning = 'Default decision based on CASTL ethical framework';
            principles = ['default_ethical_framework'];
        }
        
        res.json({
            decision,
            reasoning,
            principles_applied: principles,
            confidence: 0.85,
            timestamp: new Date().toISOString(),
            scenario_type: scenario.type
        });
    } catch (error) {
        res.status(500).json({
            error: 'CASTL decision failed',
            message: error.message
        });
    }
});

app.get('/api/novacortex/pi-rhythm/measure', async (req, res) => {
    try {
        const duration = parseFloat(req.query.duration) || 5.0;
        
        // Simulate π-Rhythm measurement
        const piValue = Math.PI;
        const measuredFrequency = piValue + (Math.random() - 0.5) * 0.1;
        const deviation = Math.abs(measuredFrequency - piValue);
        const phase = Math.random() * 2 * Math.PI;
        
        piRhythmDeviationGauge.set(deviation);
        
        res.json({
            deviation,
            frequency: measuredFrequency,
            phase,
            pi_reference: piValue,
            duration,
            status: deviation < 0.1 ? 'synchronized' : 'desynchronized',
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        res.status(500).json({
            error: 'π-Rhythm measurement failed',
            message: error.message
        });
    }
});

app.get('/api/novacortex/metrics', async (req, res) => {
    try {
        const currentTime = new Date().toISOString();
        const coherenceLevel = 0.98 + (Math.random() - 0.5) * 0.04;
        const piDeviation = Math.random() * 0.08;
        
        res.json({
            timestamp: currentTime,
            coherence: {
                level: coherenceLevel,
                status: coherenceLevel >= 0.95 ? 'optimal' : 'degraded'
            },
            castl_violations: 0,
            pi_rhythm: {
                deviation: piDeviation,
                status: piDeviation < 0.1 ? 'synchronized' : 'desynchronized'
            },
            system_health: 'optimal',
            uptime: process.uptime(),
            memory_usage: process.memoryUsage(),
            active_connections: 1
        });
    } catch (error) {
        res.status(500).json({
            error: 'Metrics collection failed',
            message: error.message
        });
    }
});

// Prometheus metrics endpoint
app.get('/metrics', async (req, res) => {
    try {
        res.set('Content-Type', register.contentType);
        const metrics = await register.metrics();
        res.end(metrics);
    } catch (error) {
        res.status(500).json({
            error: 'Metrics endpoint failed',
            message: error.message
        });
    }
});

// Error handling middleware
app.use((err, req, res, next) => {
    console.error('Unhandled error:', err);
    res.status(500).json({
        error: 'Internal server error',
        message: err.message
    });
});

// Start server
app.listen(port, () => {
    console.log(`NovaCortex test server running on port ${port}`);
    console.log(`Health check: http://localhost:${port}/health`);
    console.log(`Metrics: http://localhost:${port}/metrics`);
    console.log('🧠 NovaCortex is ready for testing');
});

module.exports = app;
