/**
 * ESG Initiatives API - Controllers
 * 
 * This file defines the controllers for the ESG Initiatives API.
 */

const { Initiative, ImpactAssessment } = require('./models');
const logger = require('../../../utils/logger');

/**
 * Initiative Controllers
 */
const initiativeController = {
  /**
   * Get all initiatives
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getAllInitiatives: async (req, res) => {
    try {
      const { page = 1, limit = 10, sort = '-createdAt', category, status, tag } = req.query;
      
      const options = {
        page: parseInt(page, 10),
        limit: parseInt(limit, 10),
        sort
      };
      
      // Build filter object
      const filter = {};
      if (category) filter.category = category;
      if (status) filter.status = status;
      if (tag) filter.tags = tag;
      
      const initiatives = await Initiative.find(filter)
        .sort(sort)
        .skip((options.page - 1) * options.limit)
        .limit(options.limit)
        .populate('relatedMetrics');
      
      const total = await Initiative.countDocuments(filter);
      
      return res.status(200).json({
        success: true,
        data: initiatives,
        pagination: {
          total,
          page: options.page,
          limit: options.limit,
          pages: Math.ceil(total / options.limit)
        }
      });
    } catch (error) {
      logger.error(`Error getting initiatives: ${error.message}`, { service: 'esg-initiatives-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while getting initiatives'
      });
    }
  },
  
  /**
   * Get initiative by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getInitiativeById: async (req, res) => {
    try {
      const initiative = await Initiative.findById(req.params.id)
        .populate('relatedMetrics')
        .populate('goals.metrics.metric');
      
      if (!initiative) {
        return res.status(404).json({
          success: false,
          error: 'Not Found',
          message: 'Initiative not found'
        });
      }
      
      return res.status(200).json({
        success: true,
        data: initiative
      });
    } catch (error) {
      logger.error(`Error getting initiative: ${error.message}`, { service: 'esg-initiatives-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while getting the initiative'
      });
    }
  },
  
  /**
   * Create a new initiative
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  createInitiative: async (req, res) => {
    try {
      const initiative = new Initiative(req.body);
      await initiative.save();
      
      return res.status(201).json({
        success: true,
        data: initiative,
        message: 'Initiative created successfully'
      });
    } catch (error) {
      logger.error(`Error creating initiative: ${error.message}`, { service: 'esg-initiatives-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while creating the initiative'
      });
    }
  },
  
  /**
   * Update an initiative
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  updateInitiative: async (req, res) => {
    try {
      const initiative = await Initiative.findByIdAndUpdate(
        req.params.id,
        { ...req.body, updatedAt: Date.now() },
        { new: true, runValidators: true }
      );
      
      if (!initiative) {
        return res.status(404).json({
          success: false,
          error: 'Not Found',
          message: 'Initiative not found'
        });
      }
      
      return res.status(200).json({
        success: true,
        data: initiative,
        message: 'Initiative updated successfully'
      });
    } catch (error) {
      logger.error(`Error updating initiative: ${error.message}`, { service: 'esg-initiatives-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while updating the initiative'
      });
    }
  },
  
  /**
   * Delete an initiative
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  deleteInitiative: async (req, res) => {
    try {
      const initiative = await Initiative.findByIdAndDelete(req.params.id);
      
      if (!initiative) {
        return res.status(404).json({
          success: false,
          error: 'Not Found',
          message: 'Initiative not found'
        });
      }
      
      // Delete related impact assessments
      await ImpactAssessment.deleteMany({ initiative: req.params.id });
      
      return res.status(200).json({
        success: true,
        message: 'Initiative and related data deleted successfully'
      });
    } catch (error) {
      logger.error(`Error deleting initiative: ${error.message}`, { service: 'esg-initiatives-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while deleting the initiative'
      });
    }
  },
  
  /**
   * Add a goal to an initiative
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  addGoal: async (req, res) => {
    try {
      const initiative = await Initiative.findById(req.params.id);
      
      if (!initiative) {
        return res.status(404).json({
          success: false,
          error: 'Not Found',
          message: 'Initiative not found'
        });
      }
      
      initiative.goals.push(req.body);
      initiative.updatedAt = Date.now();
      
      await initiative.save();
      
      return res.status(200).json({
        success: true,
        data: initiative,
        message: 'Goal added to initiative successfully'
      });
    } catch (error) {
      logger.error(`Error adding goal to initiative: ${error.message}`, { service: 'esg-initiatives-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while adding the goal to the initiative'
      });
    }
  },
  
  /**
   * Update a goal in an initiative
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  updateGoal: async (req, res) => {
    try {
      const initiative = await Initiative.findById(req.params.id);
      
      if (!initiative) {
        return res.status(404).json({
          success: false,
          error: 'Not Found',
          message: 'Initiative not found'
        });
      }
      
      const goalIndex = initiative.goals.findIndex(goal => goal._id.toString() === req.params.goalId);
      
      if (goalIndex === -1) {
        return res.status(404).json({
          success: false,
          error: 'Not Found',
          message: 'Goal not found in the initiative'
        });
      }
      
      initiative.goals[goalIndex] = { ...initiative.goals[goalIndex].toObject(), ...req.body };
      initiative.updatedAt = Date.now();
      
      await initiative.save();
      
      return res.status(200).json({
        success: true,
        data: initiative,
        message: 'Goal updated successfully'
      });
    } catch (error) {
      logger.error(`Error updating goal in initiative: ${error.message}`, { service: 'esg-initiatives-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while updating the goal in the initiative'
      });
    }
  },
  
  /**
   * Remove a goal from an initiative
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  removeGoal: async (req, res) => {
    try {
      const initiative = await Initiative.findById(req.params.id);
      
      if (!initiative) {
        return res.status(404).json({
          success: false,
          error: 'Not Found',
          message: 'Initiative not found'
        });
      }
      
      const goalIndex = initiative.goals.findIndex(goal => goal._id.toString() === req.params.goalId);
      
      if (goalIndex === -1) {
        return res.status(404).json({
          success: false,
          error: 'Not Found',
          message: 'Goal not found in the initiative'
        });
      }
      
      initiative.goals.splice(goalIndex, 1);
      initiative.updatedAt = Date.now();
      
      await initiative.save();
      
      return res.status(200).json({
        success: true,
        data: initiative,
        message: 'Goal removed from initiative successfully'
      });
    } catch (error) {
      logger.error(`Error removing goal from initiative: ${error.message}`, { service: 'esg-initiatives-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while removing the goal from the initiative'
      });
    }
  },
  
  /**
   * Add an update to an initiative
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  addUpdate: async (req, res) => {
    try {
      const initiative = await Initiative.findById(req.params.id);
      
      if (!initiative) {
        return res.status(404).json({
          success: false,
          error: 'Not Found',
          message: 'Initiative not found'
        });
      }
      
      initiative.updates.push({
        ...req.body,
        author: req.user.id,
        date: Date.now()
      });
      initiative.updatedAt = Date.now();
      
      await initiative.save();
      
      return res.status(200).json({
        success: true,
        data: initiative,
        message: 'Update added to initiative successfully'
      });
    } catch (error) {
      logger.error(`Error adding update to initiative: ${error.message}`, { service: 'esg-initiatives-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while adding the update to the initiative'
      });
    }
  }
};

/**
 * Impact Assessment Controllers
 */
const impactAssessmentController = {
  /**
   * Get all impact assessments
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getAllImpactAssessments: async (req, res) => {
    try {
      const { page = 1, limit = 10, sort = '-assessmentDate', initiative, status } = req.query;
      
      const options = {
        page: parseInt(page, 10),
        limit: parseInt(limit, 10),
        sort
      };
      
      // Build filter object
      const filter = {};
      if (initiative) filter.initiative = initiative;
      if (status) filter.status = status;
      
      const impactAssessments = await ImpactAssessment.find(filter)
        .sort(sort)
        .skip((options.page - 1) * options.limit)
        .limit(options.limit)
        .populate('initiative')
        .populate('environmentalImpact.metrics.metric')
        .populate('socialImpact.metrics.metric')
        .populate('governanceImpact.metrics.metric');
      
      const total = await ImpactAssessment.countDocuments(filter);
      
      return res.status(200).json({
        success: true,
        data: impactAssessments,
        pagination: {
          total,
          page: options.page,
          limit: options.limit,
          pages: Math.ceil(total / options.limit)
        }
      });
    } catch (error) {
      logger.error(`Error getting impact assessments: ${error.message}`, { service: 'esg-initiatives-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while getting impact assessments'
      });
    }
  },
  
  /**
   * Get impact assessments by initiative ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getImpactAssessmentsByInitiativeId: async (req, res) => {
    try {
      const { page = 1, limit = 10, sort = '-assessmentDate', status } = req.query;
      
      const options = {
        page: parseInt(page, 10),
        limit: parseInt(limit, 10),
        sort
      };
      
      // Build filter object
      const filter = { initiative: req.params.initiativeId };
      if (status) filter.status = status;
      
      const impactAssessments = await ImpactAssessment.find(filter)
        .sort(sort)
        .skip((options.page - 1) * options.limit)
        .limit(options.limit)
        .populate('initiative')
        .populate('environmentalImpact.metrics.metric')
        .populate('socialImpact.metrics.metric')
        .populate('governanceImpact.metrics.metric');
      
      const total = await ImpactAssessment.countDocuments(filter);
      
      return res.status(200).json({
        success: true,
        data: impactAssessments,
        pagination: {
          total,
          page: options.page,
          limit: options.limit,
          pages: Math.ceil(total / options.limit)
        }
      });
    } catch (error) {
      logger.error(`Error getting impact assessments: ${error.message}`, { service: 'esg-initiatives-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while getting impact assessments'
      });
    }
  },
  
  /**
   * Get impact assessment by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getImpactAssessmentById: async (req, res) => {
    try {
      const impactAssessment = await ImpactAssessment.findById(req.params.id)
        .populate('initiative')
        .populate('environmentalImpact.metrics.metric')
        .populate('socialImpact.metrics.metric')
        .populate('governanceImpact.metrics.metric');
      
      if (!impactAssessment) {
        return res.status(404).json({
          success: false,
          error: 'Not Found',
          message: 'Impact assessment not found'
        });
      }
      
      return res.status(200).json({
        success: true,
        data: impactAssessment
      });
    } catch (error) {
      logger.error(`Error getting impact assessment: ${error.message}`, { service: 'esg-initiatives-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while getting the impact assessment'
      });
    }
  },
  
  /**
   * Create a new impact assessment
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  createImpactAssessment: async (req, res) => {
    try {
      // Check if initiative exists
      const initiative = await Initiative.findById(req.body.initiative);
      
      if (!initiative) {
        return res.status(404).json({
          success: false,
          error: 'Not Found',
          message: 'Initiative not found'
        });
      }
      
      const impactAssessment = new ImpactAssessment({
        ...req.body,
        assessor: req.user.id
      });
      
      await impactAssessment.save();
      
      return res.status(201).json({
        success: true,
        data: impactAssessment,
        message: 'Impact assessment created successfully'
      });
    } catch (error) {
      logger.error(`Error creating impact assessment: ${error.message}`, { service: 'esg-initiatives-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while creating the impact assessment'
      });
    }
  },
  
  /**
   * Update an impact assessment
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  updateImpactAssessment: async (req, res) => {
    try {
      const impactAssessment = await ImpactAssessment.findByIdAndUpdate(
        req.params.id,
        { ...req.body, updatedAt: Date.now() },
        { new: true, runValidators: true }
      );
      
      if (!impactAssessment) {
        return res.status(404).json({
          success: false,
          error: 'Not Found',
          message: 'Impact assessment not found'
        });
      }
      
      return res.status(200).json({
        success: true,
        data: impactAssessment,
        message: 'Impact assessment updated successfully'
      });
    } catch (error) {
      logger.error(`Error updating impact assessment: ${error.message}`, { service: 'esg-initiatives-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while updating the impact assessment'
      });
    }
  },
  
  /**
   * Delete an impact assessment
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  deleteImpactAssessment: async (req, res) => {
    try {
      const impactAssessment = await ImpactAssessment.findByIdAndDelete(req.params.id);
      
      if (!impactAssessment) {
        return res.status(404).json({
          success: false,
          error: 'Not Found',
          message: 'Impact assessment not found'
        });
      }
      
      return res.status(200).json({
        success: true,
        message: 'Impact assessment deleted successfully'
      });
    } catch (error) {
      logger.error(`Error deleting impact assessment: ${error.message}`, { service: 'esg-initiatives-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while deleting the impact assessment'
      });
    }
  },
  
  /**
   * Review an impact assessment
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  reviewImpactAssessment: async (req, res) => {
    try {
      const { reviewComments } = req.body;
      
      const impactAssessment = await ImpactAssessment.findById(req.params.id);
      
      if (!impactAssessment) {
        return res.status(404).json({
          success: false,
          error: 'Not Found',
          message: 'Impact assessment not found'
        });
      }
      
      if (impactAssessment.status !== 'Completed') {
        return res.status(400).json({
          success: false,
          error: 'Bad Request',
          message: 'Only completed impact assessments can be reviewed'
        });
      }
      
      impactAssessment.status = 'Reviewed';
      impactAssessment.reviewedBy = req.user.id;
      impactAssessment.reviewDate = Date.now();
      impactAssessment.reviewComments = reviewComments || '';
      impactAssessment.updatedAt = Date.now();
      
      await impactAssessment.save();
      
      return res.status(200).json({
        success: true,
        data: impactAssessment,
        message: 'Impact assessment reviewed successfully'
      });
    } catch (error) {
      logger.error(`Error reviewing impact assessment: ${error.message}`, { service: 'esg-initiatives-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while reviewing the impact assessment'
      });
    }
  }
};

module.exports = {
  initiativeController,
  impactAssessmentController
};
