# Package-Based Feature Controls

NovaConnect UAC implements package-based feature controls to support different tiers of functionality for tenants. This document explains how the package-based feature control system works and how to use it.

## Overview

The package-based feature control system allows:

- Defining packages with specific feature sets and limits
- Mapping tenants to packages
- Customizing features and limits for specific tenants
- Checking feature access based on package entitlements
- Enforcing limits based on package definitions

## Key Components

### Package Configuration Registry

The `PackageConfigRegistry` service manages package definitions and tenant-to-package mappings. It provides:

- Package CRUD operations
- Tenant-to-package mapping management
- Feature access checking
- Limit enforcement
- Caching for performance optimization

### Feature Flag Service

The `FeatureFlagService` has been enhanced to integrate with the package-based feature control system. It now:

- Checks feature access based on both user entitlements and tenant package
- Enforces limits based on both user entitlements and tenant package
- Provides tenant-specific feature access information

### Package API

The package API provides endpoints for managing packages and tenant-to-package mappings:

- `/api/packages` - Package management
- `/api/packages/tenant/:tenantId` - Tenant package management

## Package Structure

A package definition includes:

```json
{
  "id": "enterprise",
  "name": "NovaConnect Enterprise",
  "description": "Advanced enterprise features for large-scale deployments",
  "tier": "enterprise",
  "features": [
    "core.basic_connectors",
    "security.encryption",
    "enterprise.advanced_connectors"
  ],
  "limits": {
    "connections": 100,
    "operations_per_day": 25000,
    "workflows": 50,
    "actions_per_workflow": 50,
    "scheduled_workflows": 20,
    "alerts": 50
  }
}
```

## Tenant-to-Package Mapping

A tenant-to-package mapping includes:

```json
{
  "tenantId": "tenant-123",
  "packageId": "enterprise",
  "customFeatures": [
    "beta.new_feature"
  ],
  "customLimits": {
    "connections": 150
  }
}
```

## Default Packages

The system includes four default packages:

1. **Core** - Basic functionality for connecting APIs and automating workflows
2. **Secure** - Enhanced security and compliance features
3. **Enterprise** - Advanced enterprise features for large-scale deployments
4. **AI Boost** - AI-powered features for intelligent automation

## Using Package-Based Feature Controls

### Checking Feature Access

To check if a tenant has access to a feature:

```javascript
const featureFlagService = new FeatureFlagService();

// Check if tenant has access to a feature
const hasAccess = await featureFlagService.hasFeatureAccess(
  userId,
  'enterprise.advanced_connectors',
  tenantId
);

if (hasAccess) {
  // Allow access to the feature
} else {
  // Deny access to the feature
}
```

### Enforcing Limits

To enforce limits based on package entitlements:

```javascript
const featureFlagService = new FeatureFlagService();

// Get tenant's limit for connections
const connectionsLimit = await featureFlagService.getFeatureLimit(
  userId,
  'connections',
  'limit',
  tenantId
);

// Check if tenant has reached the limit
if (currentConnections >= connectionsLimit) {
  // Deny creating a new connection
} else {
  // Allow creating a new connection
}
```

### Getting Available Features

To get all features available to a tenant:

```javascript
const featureFlagService = new FeatureFlagService();

// Get all features available to the tenant
const availableFeatures = await featureFlagService.getUserAvailableFeatures(
  userId,
  tenantId
);

// Use available features to customize UI
```

## Managing Packages

### Creating a New Package

```javascript
const featureFlagService = new FeatureFlagService();

// Create a new package
const newPackage = await featureFlagService.createPackage({
  id: 'custom-package',
  name: 'Custom Package',
  description: 'A custom package for specific needs',
  tier: 'custom',
  features: [
    'core.basic_connectors',
    'security.encryption',
    'custom.feature'
  ],
  limits: {
    connections: 50,
    operations_per_day: 10000
  }
});
```

### Updating a Package

```javascript
const featureFlagService = new FeatureFlagService();

// Update a package
const updatedPackage = await featureFlagService.updatePackage('custom-package', {
  name: 'Updated Custom Package',
  limits: {
    connections: 75
  }
});
```

### Setting Tenant Package

```javascript
const featureFlagService = new FeatureFlagService();

// Set tenant package
const mapping = await featureFlagService.setTenantPackage(
  'tenant-123',
  'enterprise',
  ['beta.new_feature'],
  { connections: 150 }
);
```

## Performance Considerations

The package-based feature control system includes caching to optimize performance:

- Package definitions are cached for 5 minutes
- Tenant-to-package mappings are cached for 5 minutes
- Feature access checks are cached for the duration of the request
- Limit checks are cached for the duration of the request

## Integration with GCP Marketplace

The package-based feature control system is designed to integrate with GCP Marketplace:

- Packages align with GCP Marketplace offerings
- Tenant provisioning automatically sets the appropriate package
- Package upgrades can be triggered by GCP Marketplace plan changes

## Conclusion

The package-based feature control system provides a flexible and scalable way to manage feature access and limits for tenants. It supports the tiered packaging strategy for GCP Marketplace and enables customization for specific tenant needs.
