/**
 * NovaFuse Universal API Connector Secure Executor
 * 
 * This module provides a secure implementation of the connector executor
 * with SSRF protection, input validation, and rate limiting.
 */

const axios = require('axios');
const jsonpath = require('jsonpath');
const { performance } = require('perf_hooks');
const { v4: uuidv4 } = require('uuid');
const { SSRFProtection, InputValidator } = require('../security');

/**
 * Secure Connector Executor
 * 
 * Executes API connectors with security protections.
 */
class SecureConnectorExecutor {
  constructor(connectorRegistry, options = {}) {
    this.connectorRegistry = connectorRegistry;
    this.options = {
      enableMetrics: true,
      enableCaching: true,
      cacheTTL: 3600, // 1 hour
      defaultTimeout: 30000, // 30 seconds
      maxConcurrentRequests: 50,
      ...options
    };
    
    // Initialize SSRF protection
    this.ssrfProtection = new SSRFProtection({
      allowedProtocols: ['https:'],
      allowPrivateIPs: false
    });
    
    // Initialize metrics
    this.metrics = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      blockedRequests: 0,
      totalRequestTime: 0,
      averageRequestTime: 0
    };
    
    // Initialize active requests tracking
    this.activeRequests = new Set();
  }

  /**
   * Execute a connector endpoint
   * @param {string} connectorId - Connector ID
   * @param {string} endpointId - Endpoint ID
   * @param {Object} params - Parameters for the endpoint
   * @returns {Promise<Object>} - Execution result
   */
  async executeConnector(connectorId, endpointId, params = {}) {
    const startTime = this.options.enableMetrics ? performance.now() : 0;
    const requestId = uuidv4();
    
    try {
      // Check if we're at the concurrent request limit
      if (this.activeRequests.size >= this.options.maxConcurrentRequests) {
        throw new Error('Maximum concurrent requests limit reached');
      }
      
      // Add to active requests
      this.activeRequests.add(requestId);
      
      // Get connector
      const connector = this.connectorRegistry.getConnector(connectorId);
      if (!connector) {
        throw new Error(`Connector ${connectorId} not found`);
      }
      
      // Find endpoint
      const endpoint = connector.endpoints.find(e => e.id === endpointId);
      if (!endpoint) {
        throw new Error(`Endpoint ${endpointId} not found in connector ${connectorId}`);
      }
      
      // Validate parameters
      this.validateParameters(params, endpoint);
      
      // Build request URL
      let url = connector.configuration.baseUrl;
      let path = endpoint.path;
      
      // Replace path parameters
      if (params.path) {
        for (const [key, value] of Object.entries(params.path)) {
          // Validate path parameter
          if (!InputValidator.isXssSafe(value) || !InputValidator.isCommandSafe(value)) {
            throw new Error(`Invalid path parameter: ${key}`);
          }
          
          path = path.replace(`{${key}}`, encodeURIComponent(value));
        }
      }
      
      url = url.endsWith('/') ? `${url}${path.startsWith('/') ? path.substring(1) : path}` : `${url}${path.startsWith('/') ? path : `/${path}`}`;
      
      // Check URL for SSRF
      const isSafe = await this.ssrfProtection.isSafeUrl(url);
      if (!isSafe) {
        if (this.options.enableMetrics) {
          this.metrics.totalRequests++;
          this.metrics.blockedRequests++;
        }
        
        throw new Error(`URL blocked by SSRF protection: ${url}`);
      }
      
      // Build request headers
      const headers = {
        ...connector.configuration.headers
      };
      
      // Add authentication headers
      if (connector.authentication.type === 'API_KEY') {
        const apiKeyField = Object.keys(connector.authentication.fields).find(f => 
          connector.authentication.fields[f].type === 'string' && 
          connector.authentication.fields[f].sensitive === true
        );
        
        if (apiKeyField && params.auth && params.auth[apiKeyField]) {
          headers['Authorization'] = `Bearer ${params.auth[apiKeyField]}`;
        }
      } else if (connector.authentication.type === 'BASIC') {
        if (params.auth && params.auth.username && params.auth.password) {
          const auth = Buffer.from(`${params.auth.username}:${params.auth.password}`).toString('base64');
          headers['Authorization'] = `Basic ${auth}`;
        }
      } else if (connector.authentication.type === 'OAUTH2') {
        if (params.auth && params.auth.token) {
          headers['Authorization'] = `Bearer ${params.auth.token}`;
        }
      }
      
      // Add custom headers from parameters
      if (params.headers) {
        // Validate headers
        for (const [key, value] of Object.entries(params.headers)) {
          if (!InputValidator.isXssSafe(value)) {
            throw new Error(`Invalid header value for ${key}`);
          }
          
          headers[key] = value;
        }
      }
      
      // Build request config
      const requestConfig = {
        url,
        method: endpoint.method,
        headers,
        params: params.query,
        data: params.body,
        timeout: params.timeout || connector.configuration.timeout || this.options.defaultTimeout
      };
      
      // Execute request
      const response = await axios(requestConfig);
      
      // Extract data using JSONPath if specified
      let result = response.data;
      
      if (endpoint.response && endpoint.response.dataPath) {
        result = jsonpath.query(response.data, endpoint.response.dataPath);
      }
      
      // Update metrics
      if (this.options.enableMetrics) {
        const endTime = performance.now();
        const duration = endTime - startTime;
        
        this.metrics.totalRequests++;
        this.metrics.successfulRequests++;
        this.metrics.totalRequestTime += duration;
        this.metrics.averageRequestTime = this.metrics.totalRequestTime / 
          (this.metrics.successfulRequests + this.metrics.failedRequests);
      }
      
      // Remove from active requests
      this.activeRequests.delete(requestId);
      
      return {
        success: true,
        data: result,
        statusCode: response.status
      };
    } catch (error) {
      // Update metrics
      if (this.options.enableMetrics) {
        const endTime = performance.now();
        const duration = endTime - startTime;
        
        this.metrics.totalRequests++;
        this.metrics.failedRequests++;
        
        if (this.metrics.successfulRequests > 0) {
          this.metrics.totalRequestTime += duration;
          this.metrics.averageRequestTime = this.metrics.totalRequestTime / 
            (this.metrics.successfulRequests + this.metrics.failedRequests);
        }
      }
      
      // Remove from active requests
      this.activeRequests.delete(requestId);
      
      return {
        success: false,
        error: error.message,
        statusCode: error.response ? error.response.status : 500
      };
    }
  }

  /**
   * Validate parameters against endpoint schema
   * @param {Object} params - Parameters to validate
   * @param {Object} endpoint - Endpoint definition
   * @throws {Error} - If parameters are invalid
   */
  validateParameters(params, endpoint) {
    // Validate path parameters
    if (endpoint.parameters && endpoint.parameters.path) {
      for (const [key, schema] of Object.entries(endpoint.parameters.path)) {
        if (schema.required && (!params.path || params.path[key] === undefined)) {
          throw new Error(`Required path parameter '${key}' is missing`);
        }
      }
    }
    
    // Validate query parameters
    if (endpoint.parameters && endpoint.parameters.query) {
      for (const [key, schema] of Object.entries(endpoint.parameters.query)) {
        if (schema.required && (!params.query || params.query[key] === undefined)) {
          throw new Error(`Required query parameter '${key}' is missing`);
        }
      }
    }
    
    // Validate body parameters
    if (endpoint.parameters && endpoint.parameters.body && endpoint.parameters.body.required) {
      if (!params.body) {
        throw new Error('Request body is required');
      }
      
      // Validate body against schema if available
      if (endpoint.parameters.body.properties) {
        const result = InputValidator.validateObject(params.body, endpoint.parameters.body);
        if (!result.isValid) {
          throw new Error(`Invalid request body: ${result.errors.join(', ')}`);
        }
      }
    }
  }

  /**
   * Get metrics for the connector executor
   * @returns {Object} - Metrics
   */
  getMetrics() {
    return this.metrics;
  }

  /**
   * Get active requests
   * @returns {Array} - Active requests
   */
  getActiveRequests() {
    return Array.from(this.activeRequests);
  }

  /**
   * Add allowed hosts to the SSRF protection whitelist
   * @param {string|string[]} hosts - Host(s) to add
   */
  addAllowedHosts(hosts) {
    this.ssrfProtection.addAllowedHosts(hosts);
  }

  /**
   * Add allowed domains to the SSRF protection whitelist
   * @param {string|string[]} domains - Domain(s) to add
   */
  addAllowedDomains(domains) {
    this.ssrfProtection.addAllowedDomains(domains);
  }
}

module.exports = SecureConnectorExecutor;
