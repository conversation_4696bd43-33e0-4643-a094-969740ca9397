/**
 * Authentication Service
 * 
 * This service handles user authentication and JWT token management.
 */

const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');

// In a real implementation, this would be loaded from environment variables
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '1h';

// Mock user database
// In a real implementation, this would be a MongoDB collection
const users = [
  {
    id: 'user-123',
    username: 'admin',
    password: '$2a$10$X7UrEJPQjQZ5X5hDvNzOZuIm6X5XQGcWGxZ2XXJZTYLTVxJV/VUWW', // hashed 'password'
    role: 'admin'
  },
  {
    id: 'user-456',
    username: 'user',
    password: '$2a$10$X7UrEJPQjQZ5X5hDvNzOZuIm6X5XQGcWGxZ2XXJZTYLTVxJV/VUWW', // hashed 'password'
    role: 'user'
  }
];

/**
 * Authenticate a user
 * @param {string} username - Username
 * @param {string} password - Password
 * @returns {Promise<Object>} Authentication result
 */
const authenticate = async (username, password) => {
  // Find the user
  const user = users.find(u => u.username === username);
  
  if (!user) {
    const error = new Error('Invalid credentials');
    error.name = 'AuthenticationError';
    throw error;
  }
  
  // Verify the password
  const isPasswordValid = await bcrypt.compare(password, user.password);
  
  if (!isPasswordValid) {
    const error = new Error('Invalid credentials');
    error.name = 'AuthenticationError';
    throw error;
  }
  
  // Generate a JWT token
  const token = generateToken(user);
  
  return {
    token,
    expiresIn: JWT_EXPIRES_IN,
    user: {
      id: user.id,
      username: user.username,
      role: user.role
    }
  };
};

/**
 * Generate a JWT token
 * @param {Object} user - User object
 * @returns {string} JWT token
 */
const generateToken = (user) => {
  const payload = {
    sub: user.id,
    username: user.username,
    role: user.role
  };
  
  return jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });
};

/**
 * Verify a JWT token
 * @param {string} token - JWT token
 * @returns {Promise<Object>} Decoded token
 */
const verifyToken = async (token) => {
  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    return decoded;
  } catch (error) {
    const authError = new Error('Invalid token');
    authError.name = 'AuthenticationError';
    throw authError;
  }
};

/**
 * Hash a password
 * @param {string} password - Password to hash
 * @returns {Promise<string>} Hashed password
 */
const hashPassword = async (password) => {
  const salt = await bcrypt.genSalt(10);
  return await bcrypt.hash(password, salt);
};

module.exports = {
  authenticate,
  generateToken,
  verifyToken,
  hashPassword
};
