/**
 * Analytics Dashboard
 *
 * This module provides a dashboard for visualizing analytics data from the
 * Finite Universe Principle defense system.
 */

const EventEmitter = require('events');
const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const path = require('path');
const { createAnalyticsComponents } = require('./index');

/**
 * AnalyticsDashboard class
 * 
 * Provides a dashboard for visualizing analytics data.
 */
class AnalyticsDashboard extends EventEmitter {
  /**
   * Constructor
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();

    this.options = {
      enableLogging: true,
      port: options.port || 3002,
      updateInterval: options.updateInterval || 5000, // 5 seconds
      enableRealTimeUpdates: options.enableRealTimeUpdates !== undefined ? options.enableRealTimeUpdates : true,
      ...options
    };

    // Initialize analytics components
    this.trendAnalyzer = options.trendAnalyzer;
    this.patternDetector = options.patternDetector;
    this.anomalyClassifier = options.anomalyClassifier;
    this.monitoringDashboard = options.monitoringDashboard;
    
    // Create analytics components if not provided
    if (!this.trendAnalyzer || !this.patternDetector || !this.anomalyClassifier) {
      const analyticsComponents = createAnalyticsComponents({
        enableLogging: this.options.enableLogging
      });
      
      this.trendAnalyzer = this.trendAnalyzer || analyticsComponents.trendAnalyzer;
      this.patternDetector = this.patternDetector || analyticsComponents.patternDetector;
      this.anomalyClassifier = this.anomalyClassifier || analyticsComponents.anomalyClassifier;
    }
    
    // Initialize Express app
    this.app = express();
    
    // Initialize HTTP server
    this.server = http.createServer(this.app);
    
    // Initialize Socket.IO
    this.io = socketIo(this.server);
    
    // Initialize update interval
    this.updateInterval = null;
    
    // Configure Express app
    this._configureExpress();
    
    // Configure Socket.IO
    this._configureSocketIO();

    if (this.options.enableLogging) {
      console.log('AnalyticsDashboard initialized with options:', this.options);
    }
  }

  /**
   * Configure Express app
   * @private
   */
  _configureExpress() {
    // Serve static files
    this.app.use(express.static(path.join(__dirname, 'public')));
    
    // Serve analytics dashboard
    this.app.get('/', (req, res) => {
      res.sendFile(path.join(__dirname, 'public', 'analytics.html'));
    });
    
    // API endpoints
    this.app.get('/api/trends', (req, res) => {
      res.json(this.trendAnalyzer.getTrendAnalysis());
    });
    
    this.app.get('/api/correlations', (req, res) => {
      res.json(this.trendAnalyzer.getCorrelationAnalysis());
    });
    
    this.app.get('/api/patterns', (req, res) => {
      res.json(this.patternDetector.getPatternDetection());
    });
    
    this.app.get('/api/anomalies', (req, res) => {
      res.json(this.anomalyClassifier.getClassificationResults());
    });
    
    this.app.get('/api/metrics', (req, res) => {
      if (this.monitoringDashboard) {
        res.json(this.monitoringDashboard.getCurrentMetrics());
      } else {
        res.json({});
      }
    });
    
    this.app.get('/api/metrics/history', (req, res) => {
      if (this.monitoringDashboard) {
        res.json(this.monitoringDashboard.getMetricsHistory());
      } else {
        res.json([]);
      }
    });
  }

  /**
   * Configure Socket.IO
   * @private
   */
  _configureSocketIO() {
    this.io.on('connection', (socket) => {
      if (this.options.enableLogging) {
        console.log('Client connected');
      }

      // Send initial data
      socket.emit('trends', this.trendAnalyzer.getTrendAnalysis());
      socket.emit('correlations', this.trendAnalyzer.getCorrelationAnalysis());
      socket.emit('patterns', this.patternDetector.getPatternDetection());
      socket.emit('anomalies', this.anomalyClassifier.getClassificationResults());
      
      if (this.monitoringDashboard) {
        socket.emit('metrics', this.monitoringDashboard.getCurrentMetrics());
      }

      // Handle disconnect
      socket.on('disconnect', () => {
        if (this.options.enableLogging) {
          console.log('Client disconnected');
        }
      });
    });
    
    // Register event listeners
    this.trendAnalyzer.on('analysis', (data) => {
      this.io.emit('trends', data.trendAnalysis);
      this.io.emit('correlations', data.correlationAnalysis);
    });
    
    this.trendAnalyzer.on('significant-trend', (data) => {
      this.io.emit('significant-trend', data);
    });
    
    this.trendAnalyzer.on('significant-correlation', (data) => {
      this.io.emit('significant-correlation', data);
    });
    
    this.trendAnalyzer.on('seasonality-detected', (data) => {
      this.io.emit('seasonality-detected', data);
    });
    
    this.patternDetector.on('detection', (data) => {
      this.io.emit('patterns', data);
    });
    
    this.patternDetector.on('pattern-detected', (data) => {
      this.io.emit('pattern-detected', data);
    });
    
    this.anomalyClassifier.on('classification', (data) => {
      this.io.emit('classification', data);
    });
    
    this.anomalyClassifier.on('classification-complete', (data) => {
      this.io.emit('anomalies', data);
    });
  }

  /**
   * Start the analytics dashboard
   * @returns {Promise} - Promise that resolves when the dashboard is started
   */
  start() {
    return new Promise((resolve) => {
      // Start the server
      this.server.listen(this.options.port, () => {
        if (this.options.enableLogging) {
          console.log(`Analytics dashboard started on port ${this.options.port}`);
        }
        
        // Start analytics components
        this.trendAnalyzer.start();
        this.patternDetector.start();
        this.anomalyClassifier.start();
        
        // Start real-time updates if enabled
        if (this.options.enableRealTimeUpdates) {
          this._startRealTimeUpdates();
        }
        
        // Emit start event
        this.emit('start');
        
        resolve();
      });
    });
  }

  /**
   * Stop the analytics dashboard
   */
  stop() {
    // Stop real-time updates
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
    
    // Stop analytics components
    this.trendAnalyzer.stop();
    this.patternDetector.stop();
    this.anomalyClassifier.stop();
    
    // Close server
    if (this.server) {
      this.server.close();
    }
    
    if (this.options.enableLogging) {
      console.log('Analytics dashboard stopped');
    }
    
    // Emit stop event
    this.emit('stop');
  }

  /**
   * Start real-time updates
   * @private
   */
  _startRealTimeUpdates() {
    if (this.updateInterval) {
      return;
    }

    this.updateInterval = setInterval(() => {
      // Get current metrics
      if (this.monitoringDashboard) {
        const metrics = this.monitoringDashboard.getCurrentMetrics();
        
        // Process metrics through analytics components
        this.trendAnalyzer.processMetrics(metrics);
        this.patternDetector.processMetrics(metrics);
        
        // Broadcast metrics to clients
        this.io.emit('metrics', metrics);
      }
    }, this.options.updateInterval);

    if (this.options.enableLogging) {
      console.log('Real-time updates started');
    }
  }

  /**
   * Process metrics through analytics components
   * @param {Object} metrics - Metrics to process
   * @returns {Object} - Analytics results
   */
  processMetrics(metrics) {
    // Process metrics through analytics components
    const trendResults = this.trendAnalyzer.processMetrics(metrics);
    const patternResults = this.patternDetector.processMetrics(metrics);
    
    return {
      trends: trendResults.trendAnalysis,
      correlations: trendResults.correlationAnalysis,
      patterns: patternResults
    };
  }

  /**
   * Process anomaly through analytics components
   * @param {Object} anomaly - Anomaly to process
   * @returns {Object} - Classification results
   */
  processAnomaly(anomaly) {
    // Process anomaly through analytics components
    return this.anomalyClassifier.processAnomaly(anomaly);
  }

  /**
   * Get trend analysis results
   * @returns {Object} - Trend analysis results
   */
  getTrendAnalysis() {
    return this.trendAnalyzer.getTrendAnalysis();
  }

  /**
   * Get correlation analysis results
   * @returns {Object} - Correlation analysis results
   */
  getCorrelationAnalysis() {
    return this.trendAnalyzer.getCorrelationAnalysis();
  }

  /**
   * Get pattern detection results
   * @returns {Object} - Pattern detection results
   */
  getPatternDetection() {
    return this.patternDetector.getPatternDetection();
  }

  /**
   * Get anomaly classification results
   * @returns {Object} - Anomaly classification results
   */
  getAnomalyClassification() {
    return this.anomalyClassifier.getClassificationResults();
  }

  /**
   * Get the Express app
   * @returns {Object} - Express app
   */
  getApp() {
    return this.app;
  }

  /**
   * Get the HTTP server
   * @returns {Object} - HTTP server
   */
  getServer() {
    return this.server;
  }

  /**
   * Get the Socket.IO instance
   * @returns {Object} - Socket.IO instance
   */
  getIO() {
    return this.io;
  }

  /**
   * Dispose resources
   */
  dispose() {
    this.stop();
    
    // Dispose analytics components
    this.trendAnalyzer.dispose();
    this.patternDetector.dispose();
    this.anomalyClassifier.dispose();
    
    if (this.options.enableLogging) {
      console.log('AnalyticsDashboard disposed');
    }
  }
}

/**
 * Create an analytics dashboard with recommended settings
 * @param {Object} options - Configuration options
 * @returns {AnalyticsDashboard} - Configured analytics dashboard
 */
function createAnalyticsDashboard(options = {}) {
  return new AnalyticsDashboard({
    enableLogging: true,
    port: 3002,
    updateInterval: 5000,
    enableRealTimeUpdates: true,
    ...options
  });
}

module.exports = {
  AnalyticsDashboard,
  createAnalyticsDashboard
};
