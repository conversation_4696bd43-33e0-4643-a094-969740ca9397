const assert = require('assert');
const { BN } = require('bn.js');
const CrownConsensus = require('./crown-consensus');

describe('CrownConsensus', () => {
  let consensus;
  const genesisValidators = [
    { address: '0x123...', stake: '1000000000000000000' }, // 1 COH
    { address: '0x456...', stake: '2000000000000000000' }, // 2 COH
    { address: '0x789...', stake: '3000000000000000000' }  // 3 COH
  ];

  beforeEach(() => {
    consensus = new CrownConsensus({
      minStake: new BN('1000000000000000000'), // 1 COH
      epochLength: 5,
      blockTime: 2
    });
    consensus.initialize(genesisValidators);
  });

  describe('initialization', () => {
    it('should initialize with genesis validators', () => {
      const validators = consensus.getValidators();
      assert.strictEqual(validators.length, 3);
      assert.strictEqual(validators[0].stake, '1000000000000000000');
      assert.strictEqual(validators[1].stake, '2000000000000000000');
      assert.strictEqual(validators[2].stake, '3000000000000000000');
    });

    it('should reject initialization with invalid stake', () => {
      const invalidConsensus = new CrownConsensus();
      assert.throws(
        () => invalidConsensus.initialize([{ address: '0x123', stake: '100' }]),
        /stake.*below minimum/
      );
    });
  });

  describe('block proposal', () => {
    it('should select validators in round-robin', () => {
      // First block
      let proposer = consensus.selectProposer(0);
      assert.ok(genesisValidators.some(v => v.address === proposer));

      // Second block
      proposer = consensus.selectProposer(1);
      assert.ok(genesisValidators.some(v => v.address === proposer));
    });

    it('should validate block proposals', () => {
      const block = {
        number: 1,
        timestamp: Math.floor(Date.now() / 1000),
        transactions: [],
        parentHash: '0x123...'
      };
      
      const proposer = consensus.selectProposer(block.number);
      assert.doesNotThrow(() => consensus.validateBlockProposal(block, proposer));
    });
  });

  describe('epoch processing', () => {
    it('should detect epoch transitions', () => {
      // Process blocks within the same epoch
      for (let i = 0; i < 4; i++) {
        const result = consensus.processEpoch(i);
        assert.strictEqual(result.newEpoch, false);
      }

      // Process block that triggers epoch transition
      const result = consensus.processEpoch(5);
      assert.strictEqual(result.newEpoch, true);
      assert.strictEqual(result.epoch, 1);
    });
  });
});

// Run tests if this file is executed directly
if (require.main === module) {
  const Mocha = require('mocha');
  const mocha = new Mocha();
  mocha.addFile(__filename);
  mocha.run(failures => {
    process.exitCode = failures ? 1 : 0;
  });
}
