/**
 * NovaFuse Compliance Testing Framework
 * 
 * This module provides a structured approach to testing NovaFuse against various compliance frameworks.
 */

/**
 * Represents a compliance control that needs to be tested
 */
class ComplianceControl {
  /**
   * Create a new compliance control
   * 
   * @param {Object} options - Control options
   * @param {string} options.id - Control identifier (e.g., "NIST-ID.AM-1")
   * @param {string} options.name - Control name
   * @param {string} options.description - Control description
   * @param {string} options.framework - Compliance framework (e.g., "NIST CSF", "GDPR", "SOC 2")
   * @param {string} options.category - Control category within the framework
   * @param {string[]} options.requirements - List of requirements to satisfy this control
   * @param {Function} options.testFunction - Function to test this control
   */
  constructor(options) {
    this.id = options.id;
    this.name = options.name;
    this.description = options.description;
    this.framework = options.framework;
    this.category = options.category;
    this.requirements = options.requirements || [];
    this.testFunction = options.testFunction;
    this.status = 'not_tested'; // Possible values: not_tested, passed, failed, partial
    this.notes = '';
    this.evidence = [];
  }
  
  /**
   * Run the test for this control
   * 
   * @returns {Promise<Object>} - Test result
   */
  async test() {
    try {
      console.log(`Testing control ${this.id}: ${this.name}`);
      
      const result = await this.testFunction(this);
      
      this.status = result.status;
      this.notes = result.notes || '';
      this.evidence = result.evidence || [];
      
      return {
        id: this.id,
        name: this.name,
        framework: this.framework,
        category: this.category,
        status: this.status,
        notes: this.notes,
        evidence: this.evidence
      };
    } catch (error) {
      console.error(`Error testing control ${this.id}: ${error.message}`);
      
      this.status = 'failed';
      this.notes = `Error: ${error.message}`;
      
      return {
        id: this.id,
        name: this.name,
        framework: this.framework,
        category: this.category,
        status: this.status,
        notes: this.notes,
        evidence: []
      };
    }
  }
}

/**
 * Represents a compliance framework with multiple controls
 */
class ComplianceFramework {
  /**
   * Create a new compliance framework
   * 
   * @param {Object} options - Framework options
   * @param {string} options.id - Framework identifier (e.g., "NIST-CSF")
   * @param {string} options.name - Framework name (e.g., "NIST Cybersecurity Framework")
   * @param {string} options.description - Framework description
   * @param {string} options.version - Framework version
   * @param {ComplianceControl[]} options.controls - List of controls in this framework
   */
  constructor(options) {
    this.id = options.id;
    this.name = options.name;
    this.description = options.description;
    this.version = options.version;
    this.controls = options.controls || [];
    this.results = [];
  }
  
  /**
   * Add a control to this framework
   * 
   * @param {ComplianceControl} control - Control to add
   */
  addControl(control) {
    this.controls.push(control);
  }
  
  /**
   * Run all tests for this framework
   * 
   * @returns {Promise<Object>} - Test results
   */
  async runTests() {
    console.log(`Running compliance tests for ${this.name} (${this.version})`);
    console.log(`Total controls: ${this.controls.length}`);
    
    this.results = [];
    
    for (const control of this.controls) {
      const result = await control.test();
      this.results.push(result);
    }
    
    const summary = this.getTestSummary();
    
    console.log(`\nTest Summary for ${this.name}:`);
    console.log(`- Total Controls: ${summary.total}`);
    console.log(`- Passed: ${summary.passed}`);
    console.log(`- Failed: ${summary.failed}`);
    console.log(`- Partial: ${summary.partial}`);
    console.log(`- Not Tested: ${summary.notTested}`);
    console.log(`- Compliance Score: ${summary.complianceScore}%`);
    
    return {
      framework: {
        id: this.id,
        name: this.name,
        version: this.version
      },
      summary: summary,
      results: this.results
    };
  }
  
  /**
   * Get a summary of test results
   * 
   * @returns {Object} - Test summary
   */
  getTestSummary() {
    const total = this.controls.length;
    const passed = this.results.filter(r => r.status === 'passed').length;
    const failed = this.results.filter(r => r.status === 'failed').length;
    const partial = this.results.filter(r => r.status === 'partial').length;
    const notTested = this.results.filter(r => r.status === 'not_tested').length;
    
    // Calculate compliance score (passed + partial*0.5) / total
    const complianceScore = Math.round(((passed + (partial * 0.5)) / total) * 100);
    
    return {
      total,
      passed,
      failed,
      partial,
      notTested,
      complianceScore
    };
  }
  
  /**
   * Generate a compliance report
   * 
   * @returns {Object} - Compliance report
   */
  generateReport() {
    const summary = this.getTestSummary();
    
    return {
      framework: {
        id: this.id,
        name: this.name,
        description: this.description,
        version: this.version
      },
      summary: summary,
      results: this.results.map(result => ({
        id: result.id,
        name: result.name,
        category: result.category,
        status: result.status,
        notes: result.notes
      })),
      timestamp: new Date().toISOString()
    };
  }
  
  /**
   * Save the compliance report to a file
   * 
   * @param {string} filePath - Path to save the report
   * @returns {Promise<void>}
   */
  async saveReport(filePath) {
    const fs = require('fs').promises;
    const path = require('path');
    
    const report = this.generateReport();
    
    // Create the directory if it doesn't exist
    const dir = path.dirname(filePath);
    await fs.mkdir(dir, { recursive: true });
    
    // Write the report to the file
    await fs.writeFile(filePath, JSON.stringify(report, null, 2));
    
    console.log(`Compliance report saved to ${filePath}`);
  }
}

/**
 * Generate an HTML report from compliance test results
 * 
 * @param {Object} results - Compliance test results
 * @param {string} outputPath - Path to save the HTML report
 * @returns {Promise<void>}
 */
async function generateHtmlReport(results, outputPath) {
  const fs = require('fs').promises;
  const path = require('path');
  
  // Create the directory if it doesn't exist
  const dir = path.dirname(outputPath);
  await fs.mkdir(dir, { recursive: true });
  
  // Generate the HTML content
  const html = `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>NovaFuse Compliance Report - ${results.framework.name}</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    header {
      background-color: #0A84FF;
      color: white;
      padding: 20px;
      border-radius: 5px;
      margin-bottom: 20px;
    }
    h1, h2, h3 {
      margin-top: 0;
    }
    .summary {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
    }
    .summary-item {
      background-color: #f5f5f5;
      padding: 15px;
      border-radius: 5px;
      flex: 1;
      margin-right: 10px;
      text-align: center;
    }
    .summary-item:last-child {
      margin-right: 0;
    }
    .summary-item.passed {
      background-color: #d4edda;
      color: #155724;
    }
    .summary-item.failed {
      background-color: #f8d7da;
      color: #721c24;
    }
    .summary-item.partial {
      background-color: #fff3cd;
      color: #856404;
    }
    .summary-item.not-tested {
      background-color: #e2e3e5;
      color: #383d41;
    }
    .summary-item.score {
      background-color: #cce5ff;
      color: #004085;
    }
    .summary-number {
      font-size: 24px;
      font-weight: bold;
      margin: 10px 0;
    }
    .controls {
      margin-bottom: 20px;
    }
    .control {
      margin-bottom: 10px;
      border: 1px solid #ddd;
      border-radius: 5px;
      overflow: hidden;
    }
    .control-header {
      padding: 10px 15px;
      background-color: #f8f9fa;
      border-bottom: 1px solid #ddd;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .control-title {
      margin: 0;
      font-size: 18px;
    }
    .control-status {
      font-weight: bold;
    }
    .control-status.passed {
      color: #28a745;
    }
    .control-status.failed {
      color: #dc3545;
    }
    .control-status.partial {
      color: #ffc107;
    }
    .control-status.not-tested {
      color: #6c757d;
    }
    .control-body {
      padding: 15px;
    }
    .control-notes {
      margin-top: 10px;
      padding: 10px;
      background-color: #f8f9fa;
      border-radius: 3px;
    }
    footer {
      margin-top: 30px;
      text-align: center;
      color: #6c757d;
      font-size: 14px;
    }
  </style>
</head>
<body>
  <header>
    <h1>NovaFuse Compliance Report</h1>
    <h2>${results.framework.name} (${results.framework.version})</h2>
    <p>Generated on ${new Date().toLocaleString()}</p>
  </header>
  
  <div class="summary">
    <div class="summary-item passed">
      <h3>Passed</h3>
      <div class="summary-number">${results.summary.passed}</div>
      <div>${Math.round((results.summary.passed / results.summary.total) * 100)}%</div>
    </div>
    <div class="summary-item failed">
      <h3>Failed</h3>
      <div class="summary-number">${results.summary.failed}</div>
      <div>${Math.round((results.summary.failed / results.summary.total) * 100)}%</div>
    </div>
    <div class="summary-item partial">
      <h3>Partial</h3>
      <div class="summary-number">${results.summary.partial}</div>
      <div>${Math.round((results.summary.partial / results.summary.total) * 100)}%</div>
    </div>
    <div class="summary-item not-tested">
      <h3>Not Tested</h3>
      <div class="summary-number">${results.summary.notTested}</div>
      <div>${Math.round((results.summary.notTested / results.summary.total) * 100)}%</div>
    </div>
    <div class="summary-item score">
      <h3>Compliance Score</h3>
      <div class="summary-number">${results.summary.complianceScore}%</div>
      <div>Overall</div>
    </div>
  </div>
  
  <h2>Controls</h2>
  
  <div class="controls">
    ${results.results.map(control => {
      const statusClass = control.status === 'passed' ? 'passed' : 
                         control.status === 'failed' ? 'failed' : 
                         control.status === 'partial' ? 'partial' : 'not-tested';
      
      const statusText = control.status === 'passed' ? 'Passed' : 
                        control.status === 'failed' ? 'Failed' : 
                        control.status === 'partial' ? 'Partial' : 'Not Tested';
      
      return `
    <div class="control">
      <div class="control-header">
        <h3 class="control-title">${control.id}: ${control.name}</h3>
        <span class="control-status ${statusClass}">${statusText}</span>
      </div>
      <div class="control-body">
        <div><strong>Category:</strong> ${control.category}</div>
        ${control.notes ? `<div class="control-notes">${control.notes}</div>` : ''}
      </div>
    </div>
      `;
    }).join('')}
  </div>
  
  <footer>
    <p>NovaFuse Universal Platform &copy; ${new Date().getFullYear()}</p>
  </footer>
</body>
</html>
  `;
  
  // Write the HTML report to the output file
  await fs.writeFile(outputPath, html);
  
  console.log(`HTML report generated at: ${outputPath}`);
}

module.exports = {
  ComplianceControl,
  ComplianceFramework,
  generateHtmlReport
};
