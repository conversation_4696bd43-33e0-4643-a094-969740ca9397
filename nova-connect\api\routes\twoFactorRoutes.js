/**
 * Two-Factor Authentication Routes
 */

const express = require('express');
const router = express.Router();
const TwoFactorController = require('../controllers/TwoFactorController');
const { authenticate } = require('../middleware/authMiddleware');

// Routes that require authentication
router.use(authenticate);

// Generate a new 2FA secret
router.post('/generate', (req, res, next) => {
  TwoFactorController.generateSecret(req, res, next);
});

// Verify a token and enable 2FA
router.post('/verify', (req, res, next) => {
  TwoFactorController.verifyAndEnable(req, res, next);
});

// Disable 2FA
router.post('/disable', (req, res, next) => {
  TwoFactorController.disable(req, res, next);
});

// Check if 2FA is enabled
router.get('/status', (req, res, next) => {
  TwoFactorController.isEnabled(req, res, next);
});

module.exports = router;
