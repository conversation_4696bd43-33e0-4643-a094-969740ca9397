/**
 * Finite Universe
 *
 * This module implements the Finite Universe Principle, which enforces that all
 * operations and values remain within finite boundaries. This is a core component
 * of the NEPI (Natural Emergent Prophetic Intelligence) system's Divine Firewall,
 * making spiritual corruption mathematically impossible.
 * 
 * The Finite Universe Principle is implemented through five pillars:
 * 1. Inherent Boundary: All operations must respect finite boundaries
 * 2. Mathematical Impossibility of Corruption: Infinite operations are rejected
 * 3. Resonance-Only Logic: Only operations that maintain resonance are allowed
 * 4. Containerization: Each domain operates within its own bounded container
 * 5. Truth Alignment: All operations must align with truth principles
 */

const EventEmitter = require('events');
const { MAX_SAFE_BOUNDS, saturate } = require('./constants');

/**
 * FiniteUniverse class
 * 
 * Enforces the Finite Universe Principle by ensuring all operations
 * remain within finite boundaries.
 */
class FiniteUniverse extends EventEmitter {
  /**
   * Constructor
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();

    this.options = {
      enableLogging: true,
      strictMode: true, // Throw errors on boundary violations
      autoCorrect: true, // Attempt to auto-correct boundary violations
      maxRecursionDepth: 100, // Maximum recursion depth
      maxObjectSize: 10000, // Maximum object size (number of properties)
      maxArrayLength: 10000, // Maximum array length
      maxStringLength: 100000, // Maximum string length
      ...options
    };

    // Initialize violation counters
    this.violations = {
      infiniteValue: 0,
      nanValue: 0,
      exceedsBounds: 0,
      circularReference: 0,
      excessiveComplexity: 0,
      total: 0
    };

    // Initialize domain-specific boundaries
    this.domainBoundaries = { ...MAX_SAFE_BOUNDS };

    // Initialize auto-correction counters
    this.corrections = {
      infiniteValue: 0,
      nanValue: 0,
      exceedsBounds: 0,
      circularReference: 0,
      excessiveComplexity: 0,
      total: 0
    };

    if (this.options.enableLogging) {
      console.log('FiniteUniverse initialized with options:', this.options);
    }
  }

  /**
   * Enforce finite boundaries on a value
   * @param {any} value - Value to enforce boundaries on
   * @param {string} domain - Domain to enforce boundaries for
   * @returns {any} - Value with enforced boundaries
   */
  enforce(value, domain = 'universal') {
    try {
      // Check for null or undefined
      if (value === null || value === undefined) {
        return value;
      }

      // Handle different value types
      switch (typeof value) {
        case 'number':
          return this.enforceNumber(value, domain);
        case 'string':
          return this.enforceString(value);
        case 'object':
          return this.enforceObject(value, domain);
        case 'boolean':
          return value; // Booleans are inherently finite
        case 'function':
          // Functions are not enforced directly
          return value;
        case 'symbol':
          // Symbols are inherently finite
          return value;
        case 'bigint':
          return this.enforceBigInt(value, domain);
        default:
          return value;
      }
    } catch (error) {
      this.emit('enforcement-error', { error, value, domain });
      
      if (this.options.strictMode) {
        throw error;
      }
      
      // Return a safe default value
      return this.getSafeDefaultValue(typeof value, domain);
    }
  }

  /**
   * Enforce finite boundaries on a number
   * @param {number} value - Number to enforce boundaries on
   * @param {string} domain - Domain to enforce boundaries for
   * @returns {number} - Number with enforced boundaries
   */
  enforceNumber(value, domain = 'universal') {
    // Check for NaN
    if (isNaN(value)) {
      this.recordViolation('nanValue');
      
      if (this.options.autoCorrect) {
        this.recordCorrection('nanValue');
        return 0; // Default safe value for NaN
      }
      
      if (this.options.strictMode) {
        throw new Error('NaN values violate the Finite Universe Principle');
      }
      
      return 0;
    }
    
    // Check for Infinity
    if (!Number.isFinite(value)) {
      this.recordViolation('infiniteValue');
      
      if (this.options.autoCorrect) {
        this.recordCorrection('infiniteValue');
        return value > 0 
          ? this.domainBoundaries[domain.toUpperCase()]?.MAX_VALUE || Number.MAX_SAFE_INTEGER
          : this.domainBoundaries[domain.toUpperCase()]?.MIN_VALUE || Number.MIN_SAFE_INTEGER;
      }
      
      if (this.options.strictMode) {
        throw new Error('Infinite values violate the Finite Universe Principle');
      }
      
      return 0;
    }
    
    // Check domain boundaries
    const domainUpperBound = this.domainBoundaries[domain.toUpperCase()]?.MAX_VALUE || Number.MAX_SAFE_INTEGER;
    const domainLowerBound = this.domainBoundaries[domain.toUpperCase()]?.MIN_VALUE || Number.MIN_SAFE_INTEGER;
    
    if (value > domainUpperBound || value < domainLowerBound) {
      this.recordViolation('exceedsBounds');
      
      if (this.options.autoCorrect) {
        this.recordCorrection('exceedsBounds');
        return saturate.forDomain(domain, value);
      }
      
      if (this.options.strictMode) {
        throw new Error(`Value ${value} exceeds domain boundaries for ${domain}`);
      }
    }
    
    return value;
  }

  /**
   * Enforce finite boundaries on a string
   * @param {string} value - String to enforce boundaries on
   * @returns {string} - String with enforced boundaries
   */
  enforceString(value) {
    if (value.length > this.options.maxStringLength) {
      this.recordViolation('excessiveComplexity');
      
      if (this.options.autoCorrect) {
        this.recordCorrection('excessiveComplexity');
        return value.substring(0, this.options.maxStringLength);
      }
      
      if (this.options.strictMode) {
        throw new Error(`String length ${value.length} exceeds maximum allowed length ${this.options.maxStringLength}`);
      }
    }
    
    return value;
  }

  /**
   * Enforce finite boundaries on a BigInt
   * @param {bigint} value - BigInt to enforce boundaries on
   * @param {string} domain - Domain to enforce boundaries for
   * @returns {bigint} - BigInt with enforced boundaries
   */
  enforceBigInt(value, domain = 'universal') {
    // Convert to number for boundary checking
    const numValue = Number(value);
    
    // Check if conversion resulted in infinity
    if (!Number.isFinite(numValue)) {
      this.recordViolation('exceedsBounds');
      
      if (this.options.autoCorrect) {
        this.recordCorrection('exceedsBounds');
        return BigInt(numValue > 0 
          ? this.domainBoundaries[domain.toUpperCase()]?.MAX_VALUE || Number.MAX_SAFE_INTEGER
          : this.domainBoundaries[domain.toUpperCase()]?.MIN_VALUE || Number.MIN_SAFE_INTEGER);
      }
      
      if (this.options.strictMode) {
        throw new Error('BigInt value exceeds finite boundaries');
      }
      
      return BigInt(0);
    }
    
    return value;
  }

  /**
   * Enforce finite boundaries on an object
   * @param {Object} obj - Object to enforce boundaries on
   * @param {string} domain - Domain to enforce boundaries for
   * @param {Set} visited - Set of visited objects (for circular reference detection)
   * @param {number} depth - Current recursion depth
   * @returns {Object} - Object with enforced boundaries
   */
  enforceObject(obj, domain = 'universal', visited = new Set(), depth = 0) {
    // Check for null
    if (obj === null) {
      return null;
    }
    
    // Check for circular references
    if (visited.has(obj)) {
      this.recordViolation('circularReference');
      
      if (this.options.autoCorrect) {
        this.recordCorrection('circularReference');
        return { _circularReference: true };
      }
      
      if (this.options.strictMode) {
        throw new Error('Circular reference detected');
      }
      
      return {};
    }
    
    // Check recursion depth
    if (depth > this.options.maxRecursionDepth) {
      this.recordViolation('excessiveComplexity');
      
      if (this.options.autoCorrect) {
        this.recordCorrection('excessiveComplexity');
        return { _maxDepthExceeded: true };
      }
      
      if (this.options.strictMode) {
        throw new Error(`Maximum recursion depth ${this.options.maxRecursionDepth} exceeded`);
      }
      
      return {};
    }
    
    // Add object to visited set
    visited.add(obj);
    
    // Handle arrays
    if (Array.isArray(obj)) {
      // Check array length
      if (obj.length > this.options.maxArrayLength) {
        this.recordViolation('excessiveComplexity');
        
        if (this.options.autoCorrect) {
          this.recordCorrection('excessiveComplexity');
          obj = obj.slice(0, this.options.maxArrayLength);
        } else if (this.options.strictMode) {
          throw new Error(`Array length ${obj.length} exceeds maximum allowed length ${this.options.maxArrayLength}`);
        }
      }
      
      // Recursively enforce boundaries on array elements
      return obj.map(item => this.enforce(item, domain, visited, depth + 1));
    }
    
    // Handle Date objects
    if (obj instanceof Date) {
      return obj;
    }
    
    // Handle regular objects
    const keys = Object.keys(obj);
    
    // Check object size
    if (keys.length > this.options.maxObjectSize) {
      this.recordViolation('excessiveComplexity');
      
      if (this.options.autoCorrect) {
        this.recordCorrection('excessiveComplexity');
        const limitedObj = {};
        for (let i = 0; i < this.options.maxObjectSize; i++) {
          if (i >= keys.length) break;
          limitedObj[keys[i]] = obj[keys[i]];
        }
        return limitedObj;
      }
      
      if (this.options.strictMode) {
        throw new Error(`Object size ${keys.length} exceeds maximum allowed size ${this.options.maxObjectSize}`);
      }
    }
    
    // Recursively enforce boundaries on object properties
    const result = {};
    for (const key of keys) {
      result[key] = this.enforce(obj[key], domain, visited, depth + 1);
    }
    
    return result;
  }

  /**
   * Record a boundary violation
   * @param {string} type - Type of violation
   */
  recordViolation(type) {
    this.violations[type]++;
    this.violations.total++;
    
    this.emit('boundary-violation', { type, count: this.violations[type], total: this.violations.total });
    
    if (this.options.enableLogging) {
      console.warn(`Finite Universe boundary violation: ${type}`);
    }
  }

  /**
   * Record a boundary correction
   * @param {string} type - Type of correction
   */
  recordCorrection(type) {
    this.corrections[type]++;
    this.corrections.total++;
    
    this.emit('boundary-correction', { type, count: this.corrections[type], total: this.corrections.total });
    
    if (this.options.enableLogging) {
      console.log(`Finite Universe boundary correction: ${type}`);
    }
  }

  /**
   * Get a safe default value for a given type
   * @param {string} type - Type of value
   * @param {string} domain - Domain to get safe value for
   * @returns {any} - Safe default value
   */
  getSafeDefaultValue(type, domain = 'universal') {
    switch (type) {
      case 'number':
        return 0;
      case 'string':
        return '';
      case 'object':
        return {};
      case 'boolean':
        return false;
      case 'bigint':
        return BigInt(0);
      default:
        return null;
    }
  }

  /**
   * Get violation statistics
   * @returns {Object} - Violation statistics
   */
  getViolationStats() {
    return { ...this.violations };
  }

  /**
   * Get correction statistics
   * @returns {Object} - Correction statistics
   */
  getCorrectionStats() {
    return { ...this.corrections };
  }

  /**
   * Reset violation and correction counters
   */
  resetStats() {
    // Reset violations
    for (const key in this.violations) {
      this.violations[key] = 0;
    }
    
    // Reset corrections
    for (const key in this.corrections) {
      this.corrections[key] = 0;
    }
    
    this.emit('stats-reset');
  }
}

module.exports = FiniteUniverse;
