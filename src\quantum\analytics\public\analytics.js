/**
 * Analytics Dashboard JavaScript
 *
 * This file contains the client-side JavaScript for the analytics dashboard.
 */

// Initialize Socket.IO
const socket = io();

// Charts
let trendsChart;
let patternsChart;
let anomaliesCategoryChart;
let anomaliesSeverityChart;
let forecastsChart;

// Data storage
let trendsData = {};
let correlationsData = {};
let patternsData = {};
let anomaliesData = {};
let metricsData = {};

/**
 * Initialize the dashboard
 */
document.addEventListener('DOMContentLoaded', () => {
  // Initialize charts
  initializeCharts();

  // Set up Socket.IO event listeners
  setupSocketListeners();

  // Fetch initial data
  fetchInitialData();
});

/**
 * Initialize charts
 */
function initializeCharts() {
  // Trends chart
  const trendsCtx = document.getElementById('trends-chart').getContext('2d');
  trendsChart = new Chart(trendsCtx, {
    type: 'line',
    data: {
      labels: [],
      datasets: [
        {
          label: 'Boundary Violations',
          data: [],
          borderColor: '#dc3545',
          backgroundColor: 'rgba(220, 53, 69, 0.1)',
          tension: 0.4,
          fill: true
        },
        {
          label: 'Validation Failures',
          data: [],
          borderColor: '#0d6efd',
          backgroundColor: 'rgba(13, 110, 253, 0.1)',
          tension: 0.4,
          fill: true
        },
        {
          label: 'Cyber Violations',
          data: [],
          borderColor: '#198754',
          backgroundColor: 'rgba(25, 135, 84, 0.1)',
          tension: 0.4,
          fill: true
        },
        {
          label: 'Financial Violations',
          data: [],
          borderColor: '#ffc107',
          backgroundColor: 'rgba(255, 193, 7, 0.1)',
          tension: 0.4,
          fill: true
        },
        {
          label: 'Medical Violations',
          data: [],
          borderColor: '#0dcaf0',
          backgroundColor: 'rgba(13, 202, 240, 0.1)',
          tension: 0.4,
          fill: true
        }
      ]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'top',
        },
        tooltip: {
          mode: 'index',
          intersect: false
        }
      },
      scales: {
        y: {
          beginAtZero: true
        }
      }
    }
  });

  // Patterns chart
  const patternsCtx = document.getElementById('patterns-chart').getContext('2d');
  patternsChart = new Chart(patternsCtx, {
    type: 'bar',
    data: {
      labels: ['Boundary Violations', 'Validation Failures', 'Cyber', 'Financial', 'Medical'],
      datasets: [
        {
          label: 'Detected Patterns',
          data: [0, 0, 0, 0, 0],
          backgroundColor: [
            'rgba(220, 53, 69, 0.7)',
            'rgba(13, 110, 253, 0.7)',
            'rgba(25, 135, 84, 0.7)',
            'rgba(255, 193, 7, 0.7)',
            'rgba(13, 202, 240, 0.7)'
          ],
          borderColor: [
            'rgb(220, 53, 69)',
            'rgb(13, 110, 253)',
            'rgb(25, 135, 84)',
            'rgb(255, 193, 7)',
            'rgb(13, 202, 240)'
          ],
          borderWidth: 1
        }
      ]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false
        }
      },
      scales: {
        y: {
          beginAtZero: true,
          title: {
            display: true,
            text: 'Pattern Count'
          }
        }
      }
    }
  });

  // Anomalies category chart
  const anomaliesCategoryCtx = document.getElementById('anomalies-category-chart').getContext('2d');
  anomaliesCategoryChart = new Chart(anomaliesCategoryCtx, {
    type: 'doughnut',
    data: {
      labels: [],
      datasets: [
        {
          data: [],
          backgroundColor: [
            'rgba(220, 53, 69, 0.7)',
            'rgba(13, 110, 253, 0.7)',
            'rgba(25, 135, 84, 0.7)',
            'rgba(255, 193, 7, 0.7)',
            'rgba(13, 202, 240, 0.7)',
            'rgba(111, 66, 193, 0.7)',
            'rgba(253, 126, 20, 0.7)',
            'rgba(32, 201, 151, 0.7)',
            'rgba(108, 117, 125, 0.7)',
            'rgba(248, 249, 250, 0.7)'
          ],
          borderWidth: 1
        }
      ]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'right'
        }
      }
    }
  });

  // Anomalies severity chart
  const anomaliesSeverityCtx = document.getElementById('anomalies-severity-chart').getContext('2d');
  anomaliesSeverityChart = new Chart(anomaliesSeverityCtx, {
    type: 'doughnut',
    data: {
      labels: ['Low', 'Medium', 'High', 'Critical'],
      datasets: [
        {
          data: [0, 0, 0, 0],
          backgroundColor: [
            'rgba(13, 202, 240, 0.7)',
            'rgba(255, 193, 7, 0.7)',
            'rgba(253, 126, 20, 0.7)',
            'rgba(220, 53, 69, 0.7)'
          ],
          borderWidth: 1
        }
      ]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'right'
        }
      }
    }
  });

  // Forecasts chart
  const forecastsCtx = document.getElementById('forecasts-chart').getContext('2d');
  forecastsChart = new Chart(forecastsCtx, {
    type: 'line',
    data: {
      labels: [],
      datasets: [
        {
          label: 'Boundary Violations (Actual)',
          data: [],
          borderColor: '#dc3545',
          backgroundColor: 'rgba(220, 53, 69, 0.1)',
          borderDash: [],
          tension: 0.4,
          fill: false
        },
        {
          label: 'Boundary Violations (Forecast)',
          data: [],
          borderColor: '#dc3545',
          backgroundColor: 'rgba(220, 53, 69, 0.1)',
          borderDash: [5, 5],
          tension: 0.4,
          fill: false
        },
        {
          label: 'Validation Failures (Actual)',
          data: [],
          borderColor: '#0d6efd',
          backgroundColor: 'rgba(13, 110, 253, 0.1)',
          borderDash: [],
          tension: 0.4,
          fill: false
        },
        {
          label: 'Validation Failures (Forecast)',
          data: [],
          borderColor: '#0d6efd',
          backgroundColor: 'rgba(13, 110, 253, 0.1)',
          borderDash: [5, 5],
          tension: 0.4,
          fill: false
        }
      ]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'top',
        },
        tooltip: {
          mode: 'index',
          intersect: false
        }
      },
      scales: {
        y: {
          beginAtZero: true,
          title: {
            display: true,
            text: 'Count'
          }
        }
      }
    }
  });
}

/**
 * Set up Socket.IO event listeners
 */
function setupSocketListeners() {
  // Listen for trends updates
  socket.on('trends', (data) => {
    trendsData = data;
    updateTrendsChart();
    updateTrendMetricsTable();
    updateForecastsChart();
  });

  // Listen for correlations updates
  socket.on('correlations', (data) => {
    correlationsData = data;
    updateCorrelationMatrix();
  });

  // Listen for patterns updates
  socket.on('patterns', (data) => {
    patternsData = data;
    updatePatternsChart();
    updateDetectedPatterns();
  });

  // Listen for anomalies updates
  socket.on('anomalies', (data) => {
    anomaliesData = data;
    updateAnomaliesCharts();
    updateRecentAnomalies();
  });

  // Listen for metrics updates
  socket.on('metrics', (data) => {
    metricsData = data;
    updateOverviewMetrics();
  });

  // Listen for significant events
  socket.on('significant-trend', (data) => {
    console.log('Significant trend detected:', data);
    // Add notification or alert
  });

  socket.on('significant-correlation', (data) => {
    console.log('Significant correlation detected:', data);
    // Add notification or alert
  });

  socket.on('pattern-detected', (data) => {
    console.log('Pattern detected:', data);
    // Add notification or alert
  });

  socket.on('classification', (data) => {
    console.log('Anomaly classified:', data);
    // Add notification or alert
  });

  // Connection status
  socket.on('connect', () => {
    console.log('Connected to server');
  });

  socket.on('disconnect', () => {
    console.log('Disconnected from server');
  });
}

/**
 * Fetch initial data
 */
function fetchInitialData() {
  // Fetch trends data
  fetch('/api/trends')
    .then(response => response.json())
    .then(data => {
      trendsData = data;
      updateTrendsChart();
      updateTrendMetricsTable();
      updateForecastsChart();
    })
    .catch(error => console.error('Error fetching trends data:', error));

  // Fetch correlations data
  fetch('/api/correlations')
    .then(response => response.json())
    .then(data => {
      correlationsData = data;
      updateCorrelationMatrix();
    })
    .catch(error => console.error('Error fetching correlations data:', error));

  // Fetch patterns data
  fetch('/api/patterns')
    .then(response => response.json())
    .then(data => {
      patternsData = data;
      updatePatternsChart();
      updateDetectedPatterns();
    })
    .catch(error => console.error('Error fetching patterns data:', error));

  // Fetch anomalies data
  fetch('/api/anomalies')
    .then(response => response.json())
    .then(data => {
      anomaliesData = data;
      updateAnomaliesCharts();
      updateRecentAnomalies();
    })
    .catch(error => console.error('Error fetching anomalies data:', error));

  // Fetch metrics data
  fetch('/api/metrics')
    .then(response => response.json())
    .then(data => {
      metricsData = data;
      updateOverviewMetrics();
    })
    .catch(error => console.error('Error fetching metrics data:', error));
}

/**
 * Update overview metrics
 */
function updateOverviewMetrics() {
  // Update boundary violations count
  const boundaryViolationsCount = metricsData.boundaryViolations || 0;
  document.getElementById('boundary-violations-count').textContent = boundaryViolationsCount;

  // Update validation failures count
  const validationFailuresCount = metricsData.validationFailures || 0;
  document.getElementById('validation-failures-count').textContent = validationFailuresCount;

  // Update anomalies count
  let anomaliesCount = 0;
  if (anomaliesData.byCategory) {
    anomaliesCount = Object.values(anomaliesData.byCategory).reduce((sum, count) => sum + count, 0);
  }
  document.getElementById('anomalies-count').textContent = anomaliesCount;

  // Update patterns count
  let patternsCount = 0;
  if (patternsData.boundaryViolations && patternsData.boundaryViolations.patterns) {
    patternsCount += patternsData.boundaryViolations.patterns.length;
  }
  if (patternsData.validationFailures && patternsData.validationFailures.patterns) {
    patternsCount += patternsData.validationFailures.patterns.length;
  }
  if (patternsData.domainViolations) {
    for (const domain in patternsData.domainViolations) {
      if (patternsData.domainViolations[domain].patterns) {
        patternsCount += patternsData.domainViolations[domain].patterns.length;
      }
    }
  }
  document.getElementById('patterns-count').textContent = patternsCount;

  // Update trends
  if (trendsData.boundaryViolations) {
    const trend = trendsData.boundaryViolations.trend || 0;
    const trendElement = document.getElementById('boundary-violations-trend');
    trendElement.textContent = `${(trend * 100).toFixed(1)}%`;

    if (trend > 0.05) {
      trendElement.className = 'badge rounded-pill badge-trend-negative';
    } else if (trend < -0.05) {
      trendElement.className = 'badge rounded-pill badge-trend-positive';
    } else {
      trendElement.className = 'badge rounded-pill badge-trend-neutral';
    }
  }

  if (trendsData.validationFailures) {
    const trend = trendsData.validationFailures.trend || 0;
    const trendElement = document.getElementById('validation-failures-trend');
    trendElement.textContent = `${(trend * 100).toFixed(1)}%`;

    if (trend > 0.05) {
      trendElement.className = 'badge rounded-pill badge-trend-negative';
    } else if (trend < -0.05) {
      trendElement.className = 'badge rounded-pill badge-trend-positive';
    } else {
      trendElement.className = 'badge rounded-pill badge-trend-neutral';
    }
  }
}

/**
 * Update trends chart
 */
function updateTrendsChart() {
  // Generate labels (last 10 time points)
  const labels = [];
  for (let i = 0; i < 10; i++) {
    labels.push(`T-${9 - i}`);
  }

  // Update chart data
  trendsChart.data.labels = labels;

  // Generate random data for demonstration
  // In a real implementation, this would use actual historical data
  const boundaryViolations = Array.from({ length: 10 }, () => Math.floor(Math.random() * 10));
  const validationFailures = Array.from({ length: 10 }, () => Math.floor(Math.random() * 5));
  const cyberViolations = Array.from({ length: 10 }, () => Math.floor(Math.random() * 6));
  const financialViolations = Array.from({ length: 10 }, () => Math.floor(Math.random() * 4));
  const medicalViolations = Array.from({ length: 10 }, () => Math.floor(Math.random() * 3));

  trendsChart.data.datasets[0].data = boundaryViolations;
  trendsChart.data.datasets[1].data = validationFailures;
  trendsChart.data.datasets[2].data = cyberViolations;
  trendsChart.data.datasets[3].data = financialViolations;
  trendsChart.data.datasets[4].data = medicalViolations;

  trendsChart.update();
}

/**
 * Update trend metrics table
 */
function updateTrendMetricsTable() {
  const tableBody = document.getElementById('trend-metrics-table');
  tableBody.innerHTML = '';

  // Add boundary violations row
  if (trendsData.boundaryViolations) {
    const row = document.createElement('tr');

    const trend = trendsData.boundaryViolations.trend || 0;
    const trendClass = trend > 0.05 ? 'text-danger' : (trend < -0.05 ? 'text-success' : 'text-muted');
    const trendIcon = trend > 0.05 ? '↑' : (trend < -0.05 ? '↓' : '→');

    row.innerHTML = `
      <td>Boundary Violations</td>
      <td class="${trendClass}">${trendIcon} ${(trend * 100).toFixed(1)}%</td>
      <td>${trendsData.boundaryViolations.seasonality ? 'Yes' : 'No'}</td>
    `;

    tableBody.appendChild(row);
  }

  // Add validation failures row
  if (trendsData.validationFailures) {
    const row = document.createElement('tr');

    const trend = trendsData.validationFailures.trend || 0;
    const trendClass = trend > 0.05 ? 'text-danger' : (trend < -0.05 ? 'text-success' : 'text-muted');
    const trendIcon = trend > 0.05 ? '↑' : (trend < -0.05 ? '↓' : '→');

    row.innerHTML = `
      <td>Validation Failures</td>
      <td class="${trendClass}">${trendIcon} ${(trend * 100).toFixed(1)}%</td>
      <td>${trendsData.validationFailures.seasonality ? 'Yes' : 'No'}</td>
    `;

    tableBody.appendChild(row);
  }

  // Add domain violations rows
  if (trendsData.domainViolations) {
    for (const domain in trendsData.domainViolations) {
      const domainData = trendsData.domainViolations[domain];

      if (domainData) {
        const row = document.createElement('tr');

        const trend = domainData.trend || 0;
        const trendClass = trend > 0.05 ? 'text-danger' : (trend < -0.05 ? 'text-success' : 'text-muted');
        const trendIcon = trend > 0.05 ? '↑' : (trend < -0.05 ? '↓' : '→');

        row.innerHTML = `
          <td>${domain.charAt(0).toUpperCase() + domain.slice(1)} Violations</td>
          <td class="${trendClass}">${trendIcon} ${(trend * 100).toFixed(1)}%</td>
          <td>${domainData.seasonality ? 'Yes' : 'No'}</td>
        `;

        tableBody.appendChild(row);
      }
    }
  }
}

/**
 * Update correlation matrix
 */
function updateCorrelationMatrix() {
  const table = document.getElementById('correlation-matrix-table');
  table.innerHTML = '';

  // Define metrics
  const metrics = [
    'Boundary Violations',
    'Validation Failures',
    'Cyber Violations',
    'Financial Violations',
    'Medical Violations'
  ];

  // Create header row
  const headerRow = document.createElement('tr');
  headerRow.innerHTML = '<th></th>';

  for (const metric of metrics) {
    headerRow.innerHTML += `<th>${metric}</th>`;
  }

  table.appendChild(headerRow);

  // Create correlation rows
  for (let i = 0; i < metrics.length; i++) {
    const row = document.createElement('tr');
    row.innerHTML = `<th>${metrics[i]}</th>`;

    for (let j = 0; j < metrics.length; j++) {
      // Diagonal is always 1
      if (i === j) {
        row.innerHTML += '<td class="correlation-cell correlation-high">1.00</td>';
        continue;
      }

      // Get correlation value
      let correlation = 0;

      if (i === 0 && j === 1) {
        correlation = correlationsData.boundaryViolations?.validationFailures || 0;
      } else if (i === 0 && j === 2) {
        correlation = correlationsData.boundaryViolations?.cyberViolations || 0;
      } else if (i === 0 && j === 3) {
        correlation = correlationsData.boundaryViolations?.financialViolations || 0;
      } else if (i === 0 && j === 4) {
        correlation = correlationsData.boundaryViolations?.medicalViolations || 0;
      } else if (i === 1 && j === 0) {
        correlation = correlationsData.boundaryViolations?.validationFailures || 0;
      } else if (i === 1 && j === 2) {
        correlation = correlationsData.validationFailures?.cyberViolations || 0;
      } else if (i === 1 && j === 3) {
        correlation = correlationsData.validationFailures?.financialViolations || 0;
      } else if (i === 1 && j === 4) {
        correlation = correlationsData.validationFailures?.medicalViolations || 0;
      } else if (i === 2 && j === 0) {
        correlation = correlationsData.boundaryViolations?.cyberViolations || 0;
      } else if (i === 2 && j === 1) {
        correlation = correlationsData.validationFailures?.cyberViolations || 0;
      } else if (i === 2 && j === 3) {
        correlation = correlationsData.domainViolations?.cyber?.financialViolations || 0;
      } else if (i === 2 && j === 4) {
        correlation = correlationsData.domainViolations?.cyber?.medicalViolations || 0;
      } else if (i === 3 && j === 0) {
        correlation = correlationsData.boundaryViolations?.financialViolations || 0;
      } else if (i === 3 && j === 1) {
        correlation = correlationsData.validationFailures?.financialViolations || 0;
      } else if (i === 3 && j === 2) {
        correlation = correlationsData.domainViolations?.cyber?.financialViolations || 0;
      } else if (i === 3 && j === 4) {
        correlation = correlationsData.domainViolations?.financial?.medicalViolations || 0;
      } else if (i === 4 && j === 0) {
        correlation = correlationsData.boundaryViolations?.medicalViolations || 0;
      } else if (i === 4 && j === 1) {
        correlation = correlationsData.validationFailures?.medicalViolations || 0;
      } else if (i === 4 && j === 2) {
        correlation = correlationsData.domainViolations?.cyber?.medicalViolations || 0;
      } else if (i === 4 && j === 3) {
        correlation = correlationsData.domainViolations?.financial?.medicalViolations || 0;
      }

      // Determine cell class based on correlation strength
      let cellClass = 'correlation-low';
      if (Math.abs(correlation) >= 0.7) {
        cellClass = 'correlation-high';
      } else if (Math.abs(correlation) >= 0.4) {
        cellClass = 'correlation-medium';
      }

      row.innerHTML += `<td class="correlation-cell ${cellClass}">${correlation.toFixed(2)}</td>`;
    }

    table.appendChild(row);
  }

  // Update significant correlations
  const significantCorrelations = document.getElementById('significant-correlations');
  significantCorrelations.innerHTML = '';

  let hasSignificantCorrelations = false;

  // Check for significant correlations
  for (const metric1 in correlationsData) {
    for (const metric2 in correlationsData[metric1]) {
      const correlation = correlationsData[metric1][metric2];

      if (Math.abs(correlation) >= 0.7) {
        hasSignificantCorrelations = true;

        const correlationItem = document.createElement('div');
        correlationItem.className = 'mb-3';

        const correlationClass = correlation >= 0 ? 'text-success' : 'text-danger';
        const correlationIcon = correlation >= 0 ? '↑' : '↓';

        correlationItem.innerHTML = `
          <h6>${formatMetricName(metric1)} & ${formatMetricName(metric2)}</h6>
          <p class="${correlationClass}">
            <strong>${correlationIcon} ${Math.abs(correlation).toFixed(2)}</strong>
            ${correlation >= 0 ? 'Positive correlation' : 'Negative correlation'}
          </p>
        `;

        significantCorrelations.appendChild(correlationItem);
      }
    }
  }

  if (!hasSignificantCorrelations) {
    significantCorrelations.innerHTML = '<p class="text-muted">No significant correlations detected</p>';
  }
}

/**
 * Update patterns chart
 */
function updatePatternsChart() {
  // Count patterns for each metric
  const boundaryViolationsPatterns = patternsData.boundaryViolations?.patterns?.length || 0;
  const validationFailuresPatterns = patternsData.validationFailures?.patterns?.length || 0;
  const cyberViolationsPatterns = patternsData.domainViolations?.cyber?.patterns?.length || 0;
  const financialViolationsPatterns = patternsData.domainViolations?.financial?.patterns?.length || 0;
  const medicalViolationsPatterns = patternsData.domainViolations?.medical?.patterns?.length || 0;

  // Update chart data
  patternsChart.data.datasets[0].data = [
    boundaryViolationsPatterns,
    validationFailuresPatterns,
    cyberViolationsPatterns,
    financialViolationsPatterns,
    medicalViolationsPatterns
  ];

  patternsChart.update();
}

/**
 * Update detected patterns
 */
function updateDetectedPatterns() {
  const detectedPatterns = document.getElementById('detected-patterns');
  detectedPatterns.innerHTML = '';

  let hasPatterns = false;

  // Add boundary violations patterns
  if (patternsData.boundaryViolations && patternsData.boundaryViolations.patterns && patternsData.boundaryViolations.patterns.length > 0) {
    hasPatterns = true;

    const patternItem = document.createElement('div');
    patternItem.className = 'pattern-item';

    patternItem.innerHTML = `
      <h6>Boundary Violations</h6>
      <p>Pattern length: ${patternsData.boundaryViolations.patterns[0].length}</p>
      <p>Similarity: ${(patternsData.boundaryViolations.patterns[0].similarity * 100).toFixed(1)}%</p>
    `;

    detectedPatterns.appendChild(patternItem);
  }

  // Add validation failures patterns
  if (patternsData.validationFailures && patternsData.validationFailures.patterns && patternsData.validationFailures.patterns.length > 0) {
    hasPatterns = true;

    const patternItem = document.createElement('div');
    patternItem.className = 'pattern-item';

    patternItem.innerHTML = `
      <h6>Validation Failures</h6>
      <p>Pattern length: ${patternsData.validationFailures.patterns[0].length}</p>
      <p>Similarity: ${(patternsData.validationFailures.patterns[0].similarity * 100).toFixed(1)}%</p>
    `;

    detectedPatterns.appendChild(patternItem);
  }

  // Add domain violations patterns
  if (patternsData.domainViolations) {
    for (const domain in patternsData.domainViolations) {
      const domainData = patternsData.domainViolations[domain];

      if (domainData && domainData.patterns && domainData.patterns.length > 0) {
        hasPatterns = true;

        const patternItem = document.createElement('div');
        patternItem.className = 'pattern-item';

        patternItem.innerHTML = `
          <h6>${domain.charAt(0).toUpperCase() + domain.slice(1)} Violations</h6>
          <p>Pattern length: ${domainData.patterns[0].length}</p>
          <p>Similarity: ${(domainData.patterns[0].similarity * 100).toFixed(1)}%</p>
        `;

        detectedPatterns.appendChild(patternItem);
      }
    }
  }

  if (!hasPatterns) {
    detectedPatterns.innerHTML = '<p class="text-muted">No patterns detected</p>';
  }
}

/**
 * Update anomalies charts
 */
function updateAnomaliesCharts() {
  // Update category chart
  if (anomaliesData.byCategory) {
    const categories = Object.keys(anomaliesData.byCategory);
    const counts = Object.values(anomaliesData.byCategory);

    anomaliesCategoryChart.data.labels = categories.map(formatMetricName);
    anomaliesCategoryChart.data.datasets[0].data = counts;

    anomaliesCategoryChart.update();
  }

  // Update severity chart
  if (anomaliesData.bySeverity) {
    const severities = Object.keys(anomaliesData.bySeverity);
    const counts = Object.values(anomaliesData.bySeverity);

    anomaliesSeverityChart.data.labels = severities.map(s => s.charAt(0).toUpperCase() + s.slice(1));
    anomaliesSeverityChart.data.datasets[0].data = counts;

    anomaliesSeverityChart.update();
  }
}

/**
 * Update recent anomalies
 */
function updateRecentAnomalies() {
  const recentAnomalies = document.getElementById('recent-anomalies');
  recentAnomalies.innerHTML = '';

  if (anomaliesData.recent && anomaliesData.recent.length > 0) {
    for (const anomaly of anomaliesData.recent) {
      const anomalyItem = document.createElement('div');
      anomalyItem.className = `anomaly-item anomaly-${anomaly.severity}`;

      const timestamp = new Date(anomaly.timestamp).toLocaleTimeString();

      anomalyItem.innerHTML = `
        <h6>${formatMetricName(anomaly.category)}</h6>
        <p>Domain: ${anomaly.domain.charAt(0).toUpperCase() + anomaly.domain.slice(1)}</p>
        <p>Severity: ${anomaly.severity.charAt(0).toUpperCase() + anomaly.severity.slice(1)}</p>
        <p class="text-muted">Detected at ${timestamp}</p>
      `;

      recentAnomalies.appendChild(anomalyItem);
    }
  } else {
    recentAnomalies.innerHTML = '<p class="text-muted">No recent anomalies</p>';
  }
}

/**
 * Update forecasts chart
 */
function updateForecastsChart() {
  // Generate labels for time steps
  const labels = [];

  // Past data points
  for (let i = 5; i > 0; i--) {
    labels.push(`T-${i}`);
  }

  // Current data point
  labels.push('Now');

  // Future data points
  for (let i = 1; i <= 5; i++) {
    labels.push(`T+${i}`);
  }

  // Update chart labels
  forecastsChart.data.labels = labels;

  // Generate random data for demonstration
  // In a real implementation, this would use actual historical and forecast data
  const boundaryViolationsActual = Array.from({ length: 6 }, () => Math.floor(Math.random() * 10));
  const boundaryViolationsForecast = Array.from({ length: 5 }, () => Math.floor(Math.random() * 10));

  const validationFailuresActual = Array.from({ length: 6 }, () => Math.floor(Math.random() * 5));
  const validationFailuresForecast = Array.from({ length: 5 }, () => Math.floor(Math.random() * 5));

  // Update chart data
  forecastsChart.data.datasets[0].data = [...boundaryViolationsActual, ...Array(5).fill(null)];
  forecastsChart.data.datasets[1].data = [...Array(5).fill(null), boundaryViolationsActual[5], ...boundaryViolationsForecast];

  forecastsChart.data.datasets[2].data = [...validationFailuresActual, ...Array(5).fill(null)];
  forecastsChart.data.datasets[3].data = [...Array(5).fill(null), validationFailuresActual[5], ...validationFailuresForecast];

  forecastsChart.update();

  // Update forecast metrics table
  updateForecastMetricsTable(boundaryViolationsActual, boundaryViolationsForecast, validationFailuresActual, validationFailuresForecast);
}

/**
 * Update forecast metrics table
 * @param {Array} boundaryViolationsActual - Actual boundary violations
 * @param {Array} boundaryViolationsForecast - Forecast boundary violations
 * @param {Array} validationFailuresActual - Actual validation failures
 * @param {Array} validationFailuresForecast - Forecast validation failures
 */
function updateForecastMetricsTable(boundaryViolationsActual, boundaryViolationsForecast, validationFailuresActual, validationFailuresForecast) {
  const tableBody = document.getElementById('forecast-metrics-table');
  tableBody.innerHTML = '';

  // Add boundary violations row
  const boundaryRow = document.createElement('tr');

  const currentBoundaryViolations = boundaryViolationsActual[5];
  const forecastBoundaryViolations = boundaryViolationsForecast[0];
  const boundaryChange = forecastBoundaryViolations - currentBoundaryViolations;
  const boundaryChangeClass = boundaryChange > 0 ? 'text-danger' : (boundaryChange < 0 ? 'text-success' : 'text-muted');
  const boundaryChangeIcon = boundaryChange > 0 ? '↑' : (boundaryChange < 0 ? '↓' : '→');

  boundaryRow.innerHTML = `
    <td>Boundary Violations</td>
    <td>${currentBoundaryViolations}</td>
    <td>${forecastBoundaryViolations}</td>
    <td class="${boundaryChangeClass}">${boundaryChangeIcon} ${boundaryChange}</td>
  `;

  tableBody.appendChild(boundaryRow);

  // Add validation failures row
  const validationRow = document.createElement('tr');

  const currentValidationFailures = validationFailuresActual[5];
  const forecastValidationFailures = validationFailuresForecast[0];
  const validationChange = forecastValidationFailures - currentValidationFailures;
  const validationChangeClass = validationChange > 0 ? 'text-danger' : (validationChange < 0 ? 'text-success' : 'text-muted');
  const validationChangeIcon = validationChange > 0 ? '↑' : (validationChange < 0 ? '↓' : '→');

  validationRow.innerHTML = `
    <td>Validation Failures</td>
    <td>${currentValidationFailures}</td>
    <td>${forecastValidationFailures}</td>
    <td class="${validationChangeClass}">${validationChangeIcon} ${validationChange}</td>
  `;

  tableBody.appendChild(validationRow);
}

/**
 * Format metric name for display
 * @param {string} name - Metric name
 * @returns {string} - Formatted metric name
 */
function formatMetricName(name) {
  if (!name) return '';

  // Split by camel case or underscore
  const words = name.split(/(?=[A-Z])/).join(' ').split('_').join(' ');

  // Capitalize first letter of each word
  return words.split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}
