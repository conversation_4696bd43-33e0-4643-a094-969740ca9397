/**
 * AI Assistant Routes
 */

const express = require('express');
const router = express.Router();
const AIAssistController = require('../controllers/AIAssistController');
const { authenticate } = require('../middleware/authMiddleware');
const { hasFeatureAccess, trackFeatureUsage } = require('../middleware/featureAccessMiddleware');

// All routes require authentication
router.use(authenticate);

// AI-assisted connector creation
router.post('/connectors/generate', 
  hasFeatureAccess('ai.connector_generation'),
  trackFeatureUsage('ai.connector_generation'),
  (req, res, next) => {
    AIAssistController.generateConnectorConfig(req, res, next);
  }
);

// Natural language connector creation
router.post('/connectors/generate-from-description', 
  hasFeatureAccess('ai.natural_language'),
  trackFeatureUsage('ai.natural_language'),
  (req, res, next) => {
    AIAssistController.generateFromDescription(req, res, next);
  }
);

// Error resolution suggestions
router.post('/errors/suggest-fixes', 
  hasFeatureAccess('ai.error_resolution'),
  trackFeatureUsage('ai.error_resolution'),
  (req, res, next) => {
    AIAssistController.suggestErrorFixes(req, res, next);
  }
);

// Workflow optimization
router.post('/workflows/optimize', 
  hasFeatureAccess('ai.workflow_optimization'),
  trackFeatureUsage('ai.workflow_optimization'),
  (req, res, next) => {
    AIAssistController.optimizeWorkflow(req, res, next);
  }
);

module.exports = router;
