/**
 * Environment Comparison Dialog Component
 * 
 * This component allows users to compare configuration between environments.
 */

import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Button, 
  Chip,
  CircularProgress,
  Dialog, 
  DialogActions, 
  DialogContent, 
  DialogTitle, 
  Divider,
  FormControl,
  Grid,
  MenuItem,
  Paper,
  Select,
  Tab,
  Tabs,
  Typography
} from '@mui/material';
import CompareArrowsIcon from '@mui/icons-material/CompareArrows';
import AddIcon from '@mui/icons-material/Add';
import RemoveIcon from '@mui/icons-material/Remove';
import EditIcon from '@mui/icons-material/Edit';
import { useEnvironment } from '../../contexts/EnvironmentContext';
import { environmentApi } from '../../services/api';

const EnvironmentComparisonDialog = ({ open, onClose, sourceEnvironment }) => {
  const { environments } = useEnvironment();
  
  const [targetEnvironmentId, setTargetEnvironmentId] = useState('');
  const [loading, setLoading] = useState(false);
  const [comparison, setComparison] = useState(null);
  const [activeTab, setActiveTab] = useState('api');
  
  // Reset form when dialog opens or source environment changes
  useEffect(() => {
    if (open) {
      setTargetEnvironmentId('');
      setComparison(null);
      setActiveTab('api');
    }
  }, [open, sourceEnvironment]);
  
  // Handle target environment change
  const handleTargetChange = (event) => {
    setTargetEnvironmentId(event.target.value);
    setComparison(null);
  };
  
  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };
  
  // Handle comparison
  const handleCompare = async () => {
    if (!sourceEnvironment || !targetEnvironmentId) {
      return;
    }
    
    try {
      setLoading(true);
      
      const response = await environmentApi.compareEnvironmentConfig(
        sourceEnvironment.id,
        targetEnvironmentId
      );
      
      setComparison(response.data);
    } catch (error) {
      console.error('Error comparing environments:', error);
      alert(`Failed to compare environments: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };
  
  // Get environment by ID
  const getEnvironmentById = (id) => {
    return environments.find(env => env.id === id) || null;
  };
  
  // Get environment color
  const getEnvironmentColor = (environment) => {
    return environment?.color || '#2196f3';
  };
  
  // Get target environment
  const targetEnvironment = getEnvironmentById(targetEnvironmentId);
  
  // Get current differences based on active tab
  const getCurrentDifferences = () => {
    if (!comparison || !comparison.differences) {
      return null;
    }
    
    return comparison.differences[activeTab] || null;
  };
  
  // Get difference count
  const getDifferenceCount = (differences) => {
    if (!differences) {
      return 0;
    }
    
    return differences.added.length + differences.removed.length + differences.modified.length;
  };
  
  // Current differences
  const currentDifferences = getCurrentDifferences();
  
  return (
    <Dialog 
      open={open} 
      onClose={onClose} 
      maxWidth="md" 
      fullWidth
    >
      <DialogTitle>
        Compare Environments
      </DialogTitle>
      
      <DialogContent>
        <Box>
          <Typography variant="subtitle1" gutterBottom>
            Compare configuration between environments
          </Typography>
          
          <Typography variant="body2" color="textSecondary" paragraph>
            This will show the differences in configuration between the source and target environments.
          </Typography>
          
          <Grid container spacing={3} sx={{ mt: 1 }}>
            <Grid item xs={12} md={5}>
              <Paper variant="outlined" sx={{ p: 2, height: '100%' }}>
                <Typography variant="subtitle2" gutterBottom>
                  Source Environment
                </Typography>
                
                {sourceEnvironment && (
                  <Box sx={{ display: 'flex', alignItems: 'center', mt: 2 }}>
                    <Chip
                      size="small"
                      label=""
                      sx={{
                        bgcolor: getEnvironmentColor(sourceEnvironment),
                        width: 16,
                        height: 16,
                        mr: 1
                      }}
                    />
                    <Typography variant="body1">
                      {sourceEnvironment.name}
                    </Typography>
                  </Box>
                )}
              </Paper>
            </Grid>
            
            <Grid item xs={12} md={2} sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
              <CompareArrowsIcon fontSize="large" color="action" />
            </Grid>
            
            <Grid item xs={12} md={5}>
              <Paper variant="outlined" sx={{ p: 2, height: '100%' }}>
                <Typography variant="subtitle2" gutterBottom>
                  Target Environment
                </Typography>
                
                <FormControl fullWidth sx={{ mt: 2 }}>
                  <Select
                    value={targetEnvironmentId}
                    onChange={handleTargetChange}
                    displayEmpty
                  >
                    <MenuItem value="" disabled>
                      Select target environment
                    </MenuItem>
                    {environments
                      .filter(env => env.id !== sourceEnvironment?.id)
                      .map((env) => (
                        <MenuItem key={env.id} value={env.id}>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <Chip
                              size="small"
                              label=""
                              sx={{
                                bgcolor: getEnvironmentColor(env),
                                width: 16,
                                height: 16,
                                mr: 1
                              }}
                            />
                            {env.name}
                          </Box>
                        </MenuItem>
                      ))}
                  </Select>
                </FormControl>
              </Paper>
            </Grid>
            
            {!comparison && (
              <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
                <Button
                  variant="contained"
                  onClick={handleCompare}
                  disabled={loading || !targetEnvironmentId}
                  startIcon={loading ? <CircularProgress size={20} /> : <CompareArrowsIcon />}
                >
                  {loading ? 'Comparing...' : 'Compare Environments'}
                </Button>
              </Grid>
            )}
          </Grid>
          
          {comparison && (
            <Box sx={{ mt: 3 }}>
              <Divider sx={{ mb: 2 }} />
              
              <Typography variant="h6" gutterBottom>
                Comparison Results
              </Typography>
              
              <Tabs
                value={activeTab}
                onChange={handleTabChange}
                sx={{ mb: 2 }}
              >
                <Tab 
                  label={
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      API
                      {getDifferenceCount(comparison.differences.api) > 0 && (
                        <Chip 
                          size="small" 
                          label={getDifferenceCount(comparison.differences.api)} 
                          color="primary"
                          sx={{ ml: 1 }}
                        />
                      )}
                    </Box>
                  } 
                  value="api" 
                />
                <Tab 
                  label={
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      Connectors
                      {getDifferenceCount(comparison.differences.connectors) > 0 && (
                        <Chip 
                          size="small" 
                          label={getDifferenceCount(comparison.differences.connectors)} 
                          color="primary"
                          sx={{ ml: 1 }}
                        />
                      )}
                    </Box>
                  } 
                  value="connectors" 
                />
                <Tab 
                  label={
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      Security
                      {getDifferenceCount(comparison.differences.security) > 0 && (
                        <Chip 
                          size="small" 
                          label={getDifferenceCount(comparison.differences.security)} 
                          color="primary"
                          sx={{ ml: 1 }}
                        />
                      )}
                    </Box>
                  } 
                  value="security" 
                />
                <Tab 
                  label={
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      Monitoring
                      {getDifferenceCount(comparison.differences.monitoring) > 0 && (
                        <Chip 
                          size="small" 
                          label={getDifferenceCount(comparison.differences.monitoring)} 
                          color="primary"
                          sx={{ ml: 1 }}
                        />
                      )}
                    </Box>
                  } 
                  value="monitoring" 
                />
              </Tabs>
              
              {currentDifferences ? (
                <Grid container spacing={2}>
                  <Grid item xs={12} md={4}>
                    <Paper variant="outlined" sx={{ p: 2 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <AddIcon color="success" sx={{ mr: 1 }} />
                        <Typography variant="subtitle2">
                          Added in Target ({currentDifferences.added.length})
                        </Typography>
                      </Box>
                      
                      <Divider sx={{ mb: 1 }} />
                      
                      {currentDifferences.added.length > 0 ? (
                        <Box sx={{ maxHeight: 300, overflow: 'auto' }}>
                          {currentDifferences.added.map((item, index) => (
                            <Chip
                              key={index}
                              label={item}
                              size="small"
                              color="success"
                              variant="outlined"
                              sx={{ m: 0.5 }}
                            />
                          ))}
                        </Box>
                      ) : (
                        <Typography variant="body2" color="textSecondary">
                          No items added in target
                        </Typography>
                      )}
                    </Paper>
                  </Grid>
                  
                  <Grid item xs={12} md={4}>
                    <Paper variant="outlined" sx={{ p: 2 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <RemoveIcon color="error" sx={{ mr: 1 }} />
                        <Typography variant="subtitle2">
                          Removed in Target ({currentDifferences.removed.length})
                        </Typography>
                      </Box>
                      
                      <Divider sx={{ mb: 1 }} />
                      
                      {currentDifferences.removed.length > 0 ? (
                        <Box sx={{ maxHeight: 300, overflow: 'auto' }}>
                          {currentDifferences.removed.map((item, index) => (
                            <Chip
                              key={index}
                              label={item}
                              size="small"
                              color="error"
                              variant="outlined"
                              sx={{ m: 0.5 }}
                            />
                          ))}
                        </Box>
                      ) : (
                        <Typography variant="body2" color="textSecondary">
                          No items removed in target
                        </Typography>
                      )}
                    </Paper>
                  </Grid>
                  
                  <Grid item xs={12} md={4}>
                    <Paper variant="outlined" sx={{ p: 2 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <EditIcon color="info" sx={{ mr: 1 }} />
                        <Typography variant="subtitle2">
                          Modified in Target ({currentDifferences.modified.length})
                        </Typography>
                      </Box>
                      
                      <Divider sx={{ mb: 1 }} />
                      
                      {currentDifferences.modified.length > 0 ? (
                        <Box sx={{ maxHeight: 300, overflow: 'auto' }}>
                          {currentDifferences.modified.map((item, index) => (
                            <Chip
                              key={index}
                              label={item}
                              size="small"
                              color="info"
                              variant="outlined"
                              sx={{ m: 0.5 }}
                            />
                          ))}
                        </Box>
                      ) : (
                        <Typography variant="body2" color="textSecondary">
                          No items modified in target
                        </Typography>
                      )}
                    </Paper>
                  </Grid>
                </Grid>
              ) : (
                <Typography variant="body2" color="textSecondary" align="center">
                  No differences found in {activeTab} configuration
                </Typography>
              )}
            </Box>
          )}
        </Box>
      </DialogContent>
      
      <DialogActions>
        <Button onClick={onClose}>
          Close
        </Button>
        
        {comparison && (
          <Button 
            variant="contained" 
            color="primary"
            onClick={() => {
              onClose();
              // Open promotion dialog (this would be handled by the parent component)
            }}
          >
            Promote Changes
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default EnvironmentComparisonDialog;
