"""
Cirq backend for quantum computing in protein folding.

This module provides an implementation of the QuantumBackend interface
using Google's Cirq and TensorFlow Quantum.
"""

from typing import Dict, Any, Optional, List, Tuple
import numpy as np

from . import QuantumBackend

class CirqBackend(QuantumBackend):
    """Cirq implementation of the QuantumBackend interface."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize the Cirq backend.
        
        Args:
            config: Configuration dictionary with the following optional keys:
                - use_tensorflow: Whether to use TensorFlow Quantum (default: True)
                - simulator_type: Type of simulator to use ('density_matrix' or 'wavefunction')
                - noise_model: Optional noise model to apply
        """
        super().__init__(config)
        self.use_tensorflow = self.config.get('use_tensorflow', True)
        self.simulator = None
        self._initialize_simulator()
    
    def _initialize_simulator(self) -> None:
        """Initialize the Cirq simulator."""
        simulator_type = self.config.get('simulator_type', 'density_matrix')
        
        if self.use_tensorflow:
            try:
                import tensorflow_quantum as tfq
                self.simulator = tfq.layers.Sampler()
                return
            except ImportError:
                logger.warning("TensorFlow Quantum not available, falling back to Cirq simulator")
                self.use_tensorflow = False
        
        # Fall back to standard Cirq simulator
        import cirq
        if simulator_type == 'density_matrix':
            self.simulator = cirq.DensityMatrixSimulator()
        else:
            self.simulator = cirq.Simulator()
    
    def run_circuit(
        self, 
        circuit: 'cirq.Circuit',
        num_qubits: int,
        depth: int,
        shots: int
    ) -> Dict[str, int]:
        """Run a quantum circuit and return the measurement results."""
        import cirq
        
        try:
            if self.use_tensorflow:
                import tensorflow as tf
                import tensorflow_quantum as tfq
                
                # Convert to TensorFlow Quantum format
                circuit_tf = tfq.convert_to_tensor([circuit])
                
                # Sample from the circuit
                output = self.simulator(
                    circuit_tf,
                    repetitions=shots
                )
                
                # Convert to counts
                measurements = output.to_list()[0].numpy()
                unique, counts = np.unique(measurements, axis=0, return_counts=True)
                
                # Convert to bitstrings
                result = {}
                for bits, count in zip(unique, counts):
                    bitstring = ''.join(['1' if b else '0' for b in bits])
                    result[bitstring] = int(count)
                
                return result
            else:
                # Use standard Cirq simulator
                result = self.simulator.run(circuit, repetitions=shots)
                
                # Get measurements
                measurements = np.column_stack([
                    result.measurements.get(str(i), np.zeros(shots, dtype=int)) 
                    for i in range(num_qubits)
                ])
                
                # Count unique bitstrings
                unique, counts = np.unique(measurements, axis=0, return_counts=True)
                
                # Convert to dictionary
                result = {}
                for bits, count in zip(unique, counts):
                    bitstring = ''.join(map(str, bits.astype(int)))
                    result[bitstring] = int(count)
                
                return result
                
        except Exception as e:
            raise RuntimeError(f"Error running circuit: {str(e)}")
    
    def get_quantum_volume(self) -> int:
        """Get the quantum volume of the backend."""
        # For simulators, return a high value
        return 1024
    
    def get_backend_info(self) -> Dict[str, Any]:
        """Get information about the backend."""
        return {
            'name': 'cirq',
            'simulator_type': 'tensorflow_quantum' if self.use_tensorflow else 'cirq',
            'quantum_volume': self.get_quantum_volume(),
            'is_simulator': True
        }
    
    @classmethod
    def is_available(cls) -> bool:
        """Check if Cirq is available."""
        try:
            import cirq
            return True
        except ImportError:
            return False
