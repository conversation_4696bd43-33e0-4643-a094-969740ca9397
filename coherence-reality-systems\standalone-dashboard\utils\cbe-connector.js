// CBE (Consciousness Browser Engine) Connector
import axios from 'axios';

export default class CBEConnector {
    constructor() {
        // Using Next.js API routes
        this.baseUrl = '/api';
    }

    async getProducts(query) {
        try {
            const response = await axios.get(`${this.baseUrl}/products`, {
                params: { query }
            });
            return response.data;
        } catch (error) {
            console.error('Error fetching products:', error);
            throw error;
        }
    }

    async getProductDetails(productId) {
        try {
            const response = await axios.get(`${this.baseUrl}/products/${productId}`);
            return response.data;
        } catch (error) {
            console.error('Error fetching product details:', error);
            throw error;
        }
    }

    async getAffiliateStats(affiliateId) {
        try {
            const response = await axios.get(`${this.baseUrl}/affiliates/${affiliateId}/stats`);
            return response.data;
        } catch (error) {
            console.error('Error fetching affiliate stats:', error);
            throw error;
        }
    }

    async createAffiliate(affiliateData) {
        try {
            const response = await axios.post(`${this.baseUrl}/affiliates`, affiliateData);
            return response.data;
        } catch (error) {
            console.error('Error creating affiliate:', error);
            throw error;
        }
    }
}
