<!-- Sidebar Toggle Button -->
<button class="sidebar-toggle">
    <i class="fas fa-bars"></i>
</button>

<!-- Header Component -->
<header>
    <div class="container header-content">
        <h1 class="logo">NovaFuse API Superstore</h1>
        <div class="flex space-x-4">
            <a href="partner-portal.html" class="btn btn-primary">Partner Login</a>
            <a href="partner-onboarding.html" class="btn btn-outline">Become a Partner</a>
        </div>
    </div>
</header>

<!-- Navigation Component -->
<nav class="main-nav">
    <div class="container nav-container">
        <button class="menu-toggle">☰</button>
        <ul class="nav-menu">
            <li><a href="index.html" id="nav-home">Home</a></li>
            <li><a href="products.html" id="nav-products">Products</a></li>
            <li><a href="novaconnect-uac.html" id="nav-uac">NovaConnect UAC</a></li>
            <li><a href="novagrc-suite.html" id="nav-grc">NovaGRC Suite</a></li>
            <li><a href="novaconcierge-ai.html" id="nav-ai">NovaConcierge AI</a></li>
            <li><a href="solutions.html" id="nav-solutions">Solutions</a></li>
            <li><a href="partner-empowerment.html" id="nav-partners">Partner Empowerment</a></li>
            <li><a href="resources.html" id="nav-resources">Resources</a></li>
            <li><a href="about.html" id="nav-about">About Us</a></li>
            <li><a href="contact.html" id="nav-contact">Contact</a></li>
        </ul>
    </div>
</nav>

<!-- Sidebar Component -->
<div class="sidebar">
    <div class="sidebar-logo">
        <h2>NovaFuse</h2>
    </div>
    <ul class="sidebar-menu">
        <li><a href="index.html" id="sidebar-home">Home</a></li>
        <li>
            <a href="products.html" id="sidebar-products">Products</a>
            <ul class="sidebar-submenu">
                <li><a href="novaconnect-uac.html">NovaConnect UAC</a></li>
                <li><a href="novagrc-suite.html">NovaGRC Suite</a></li>
                <li><a href="novaconcierge-ai.html">NovaConcierge AI</a></li>
                <li><a href="novaui.html">NovaUI</a></li>
            </ul>
        </li>
        <li>
            <a href="solutions.html" id="sidebar-solutions">Solutions</a>
            <ul class="sidebar-submenu">
                <li><a href="solutions-by-industry.html">By Industry</a></li>
                <li><a href="solutions-by-use-case.html">By Use Case</a></li>
                <li><a href="solutions-by-framework.html">By Compliance Framework</a></li>
            </ul>
        </li>
        <li><a href="partner-empowerment.html" id="sidebar-partners">Partner Empowerment</a></li>
        <li>
            <a href="resources.html" id="sidebar-resources">Resources</a>
            <ul class="sidebar-submenu">
                <li><a href="white-papers.html">White Papers</a></li>
                <li><a href="case-studies.html">Case Studies</a></li>
                <li><a href="blog.html">Blog</a></li>
            </ul>
        </li>
        <li><a href="about.html" id="sidebar-about">About Us</a></li>
        <li><a href="contact.html" id="sidebar-contact">Contact</a></li>
    </ul>
</div>

<script>
    // Set active navigation item based on current page
    document.addEventListener('DOMContentLoaded', function() {
        const currentPage = window.location.pathname.split('/').pop();

        // Set active nav item
        const navItems = {
            'index.html': 'nav-home',
            'products.html': 'nav-products',
            'novaconnect-uac.html': 'nav-uac',
            'novagrc-suite.html': 'nav-grc',
            'novaconcierge-ai.html': 'nav-ai',
            'solutions.html': 'nav-solutions',
            'partner-empowerment.html': 'nav-partners',
            'resources.html': 'nav-resources',
            'about.html': 'nav-about',
            'contact.html': 'nav-contact'
        };

        // Set active sidebar item
        const sidebarItems = {
            'index.html': 'sidebar-home',
            'products.html': 'sidebar-products',
            'solutions.html': 'sidebar-solutions',
            'partner-empowerment.html': 'sidebar-partners',
            'resources.html': 'sidebar-resources',
            'about.html': 'sidebar-about',
            'contact.html': 'sidebar-contact'
        };

        // Set active nav item
        if (navItems[currentPage]) {
            document.getElementById(navItems[currentPage]).classList.add('active');
        }

        // Set active sidebar item
        if (sidebarItems[currentPage]) {
            document.getElementById(sidebarItems[currentPage]).classList.add('active');
        }
    });
</script>
