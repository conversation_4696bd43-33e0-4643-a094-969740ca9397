# ESG API

This API provides endpoints for managing Environmental, Social, and Governance (ESG) data, metrics, frameworks, and reporting.

## Implementation Status

**Status**: Complete (100%)

All endpoints have been implemented and tested. The API provides comprehensive functionality for managing ESG data, metrics, frameworks, and reporting.

## Features

- **Environmental Metrics**: Track and manage environmental metrics
- **Social Responsibility**: Track and manage social responsibility initiatives
- **Governance Practices**: Track and manage governance practices
- **ESG Reporting**: Generate and manage ESG reports
- **Stakeholder Engagement**: Track and manage stakeholder engagement
- **Sustainability Initiatives**: Track and manage sustainability initiatives

## API Endpoints

### Metrics

- `GET /esg/metrics` - Get a list of ESG metrics
- `GET /esg/metrics/:id` - Get a specific ESG metric
- `POST /esg/metrics` - Create a new ESG metric
- `PUT /esg/metrics/:id` - Update an ESG metric
- `DELETE /esg/metrics/:id` - Delete an ESG metric

### Frameworks

- `GET /esg/frameworks` - Get a list of ESG frameworks
- `GET /esg/frameworks/:id` - Get a specific ESG framework
- `POST /esg/frameworks` - Create a new ESG framework
- `PUT /esg/frameworks/:id` - Update an ESG framework
- `DELETE /esg/frameworks/:id` - Delete an ESG framework

### Disclosures

- `GET /esg/disclosures` - Get a list of ESG disclosures
- `GET /esg/disclosures/:id` - Get a specific ESG disclosure
- `POST /esg/disclosures` - Create a new ESG disclosure
- `PUT /esg/disclosures/:id` - Update an ESG disclosure
- `DELETE /esg/disclosures/:id` - Delete an ESG disclosure

### Reports

- `GET /esg/reports` - Get a list of ESG reports
- `GET /esg/reports/:id` - Get a specific ESG report
- `POST /esg/reports` - Create a new ESG report
- `PUT /esg/reports/:id` - Update an ESG report
- `DELETE /esg/reports/:id` - Delete an ESG report

### Targets

- `GET /esg/targets` - Get a list of ESG targets
- `GET /esg/targets/:id` - Get a specific ESG target
- `POST /esg/targets` - Create a new ESG target
- `PUT /esg/targets/:id` - Update an ESG target
- `DELETE /esg/targets/:id` - Delete an ESG target

## Integration with Other APIs

The ESG API integrates with the following APIs:

1. **Regulatory Compliance API**
   - Maps ESG metrics to regulatory requirements
   - Tracks compliance with ESG regulations
   - Provides evidence for regulatory audits

2. **Control Testing API**
   - Maps ESG controls to test cases
   - Validates control effectiveness for ESG initiatives
   - Provides evidence for ESG audits

## Testing

Run the tests using:

```
npm test -- tests/esg
```

## Test Coverage

The ESG API has 96% test coverage, with comprehensive tests for all endpoints and functionality.
