/**
 * OAuth2 Authentication Service
 * 
 * This service handles OAuth2 authentication for user login.
 */

const axios = require('axios');
const crypto = require('crypto');
const fs = require('fs').promises;
const path = require('path');
const jwt = require('jsonwebtoken');
const { ValidationError, AuthenticationError } = require('../utils/errors');
const UserService = require('./UserService');
const AuditService = require('./AuditService');

class OAuth2Service {
  constructor(dataDir = path.join(__dirname, '../data')) {
    this.dataDir = dataDir;
    this.stateFile = path.join(this.dataDir, 'oauth2_states.json');
    this.providersFile = path.join(this.dataDir, 'oauth2_providers.json');
    this.userService = new UserService(dataDir);
    this.auditService = new AuditService(dataDir);
    this.jwtSecret = process.env.JWT_SECRET || 'your-secret-key';
    this.jwtExpiresIn = process.env.JWT_EXPIRES_IN || '24h';
    this.refreshTokenExpiresIn = process.env.REFRESH_TOKEN_EXPIRES_IN || '7d';
    this.ensureDataDir();
  }

  /**
   * Ensure the data directory exists
   */
  async ensureDataDir() {
    try {
      await fs.mkdir(this.dataDir, { recursive: true });
    } catch (error) {
      console.error('Error creating data directory:', error);
      throw error;
    }
  }

  /**
   * Load OAuth2 providers
   */
  async loadProviders() {
    try {
      const data = await fs.readFile(this.providersFile, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      if (error.code === 'ENOENT') {
        // File doesn't exist, return default providers
        return [
          {
            id: 'google',
            name: 'Google',
            enabled: true,
            clientId: process.env.GOOGLE_CLIENT_ID || 'your-google-client-id',
            clientSecret: process.env.GOOGLE_CLIENT_SECRET || 'your-google-client-secret',
            authorizationUrl: 'https://accounts.google.com/o/oauth2/v2/auth',
            tokenUrl: 'https://oauth2.googleapis.com/token',
            userInfoUrl: 'https://www.googleapis.com/oauth2/v3/userinfo',
            scope: 'openid profile email',
            responseType: 'code',
            attributeMapping: {
              id: 'sub',
              email: 'email',
              firstName: 'given_name',
              lastName: 'family_name',
              username: 'email'
            }
          },
          {
            id: 'github',
            name: 'GitHub',
            enabled: true,
            clientId: process.env.GITHUB_CLIENT_ID || 'your-github-client-id',
            clientSecret: process.env.GITHUB_CLIENT_SECRET || 'your-github-client-secret',
            authorizationUrl: 'https://github.com/login/oauth/authorize',
            tokenUrl: 'https://github.com/login/oauth/access_token',
            userInfoUrl: 'https://api.github.com/user',
            scope: 'read:user user:email',
            responseType: 'code',
            attributeMapping: {
              id: 'id',
              email: 'email',
              firstName: 'name',
              lastName: '',
              username: 'login'
            }
          }
        ];
      }
      console.error('Error loading OAuth2 providers:', error);
      throw error;
    }
  }

  /**
   * Save OAuth2 providers
   */
  async saveProviders(providers) {
    try {
      await fs.writeFile(this.providersFile, JSON.stringify(providers, null, 2));
    } catch (error) {
      console.error('Error saving OAuth2 providers:', error);
      throw error;
    }
  }

  /**
   * Get OAuth2 provider by ID
   */
  async getProviderById(providerId) {
    const providers = await this.loadProviders();
    const provider = providers.find(p => p.id === providerId);
    
    if (!provider) {
      throw new ValidationError(`OAuth2 provider with ID ${providerId} not found`);
    }
    
    return provider;
  }

  /**
   * Load OAuth2 states
   */
  async loadStates() {
    try {
      const data = await fs.readFile(this.stateFile, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      if (error.code === 'ENOENT') {
        // File doesn't exist, return empty array
        return [];
      }
      console.error('Error loading OAuth2 states:', error);
      throw error;
    }
  }

  /**
   * Save OAuth2 states
   */
  async saveStates(states) {
    try {
      await fs.writeFile(this.stateFile, JSON.stringify(states, null, 2));
    } catch (error) {
      console.error('Error saving OAuth2 states:', error);
      throw error;
    }
  }

  /**
   * Save OAuth2 state
   */
  async saveState(state, data) {
    const states = await this.loadStates();
    states.push({ state, ...data });
    await this.saveStates(states);
  }

  /**
   * Get OAuth2 state
   */
  async getState(state) {
    const states = await this.loadStates();
    return states.find(s => s.state === state);
  }

  /**
   * Delete OAuth2 state
   */
  async deleteState(state) {
    const states = await this.loadStates();
    const filteredStates = states.filter(s => s.state !== state);
    await this.saveStates(filteredStates);
  }

  /**
   * Clean up expired OAuth2 states
   */
  async cleanupExpiredStates() {
    const states = await this.loadStates();
    const now = new Date();
    const expirationTime = 1000 * 60 * 10; // 10 minutes
    
    const validStates = states.filter(state => {
      const created = new Date(state.created);
      return now.getTime() - created.getTime() < expirationTime;
    });
    
    await this.saveStates(validStates);
    
    return {
      removed: states.length - validStates.length,
      remaining: validStates.length
    };
  }

  /**
   * Generate OAuth2 authorization URL
   */
  async generateAuthUrl(providerId, redirectUri) {
    try {
      // Get provider
      const provider = await this.getProviderById(providerId);
      
      if (!provider.enabled) {
        throw new ValidationError('Provider is disabled');
      }
      
      // Generate state parameter for CSRF protection
      const state = crypto.randomBytes(32).toString('hex');
      
      // Store state for validation
      await this.saveState(state, {
        providerId,
        redirectUri,
        created: new Date().toISOString()
      });
      
      // Build authorization URL
      const params = new URLSearchParams({
        client_id: provider.clientId,
        response_type: provider.responseType || 'code',
        scope: provider.scope || 'openid profile email',
        redirect_uri: redirectUri,
        state
      });
      
      const authUrl = `${provider.authorizationUrl}?${params.toString()}`;
      
      return {
        providerId,
        authUrl,
        state
      };
    } catch (error) {
      console.error('Error generating OAuth2 authorization URL:', error);
      throw error;
    }
  }

  /**
   * Process OAuth2 callback
   */
  async processCallback(code, state, redirectUri) {
    try {
      // Validate state
      const stateData = await this.getState(state);
      
      if (!stateData) {
        throw new ValidationError('Invalid state parameter');
      }
      
      // Get provider
      const provider = await this.getProviderById(stateData.providerId);
      
      if (!provider.enabled) {
        throw new ValidationError('Provider is disabled');
      }
      
      // Exchange authorization code for tokens
      const tokenResponse = await axios.post(provider.tokenUrl, {
        client_id: provider.clientId,
        client_secret: provider.clientSecret,
        code,
        redirect_uri: redirectUri || stateData.redirectUri,
        grant_type: 'authorization_code'
      }, {
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      });
      
      const tokens = tokenResponse.data;
      
      // Get user info
      const userInfoResponse = await axios.get(provider.userInfoUrl, {
        headers: {
          'Authorization': `Bearer ${tokens.access_token}`
        }
      });
      
      const userInfo = userInfoResponse.data;
      
      // Map provider attributes to user properties
      const userData = {
        email: userInfo[provider.attributeMapping.email],
        firstName: userInfo[provider.attributeMapping.firstName],
        lastName: userInfo[provider.attributeMapping.lastName],
        username: userInfo[provider.attributeMapping.username] || userInfo[provider.attributeMapping.email],
        providerId: provider.id,
        providerUserId: userInfo[provider.attributeMapping.id].toString()
      };
      
      // Find or create user
      let user = await this.userService.findUserByProviderInfo(provider.id, userData.providerUserId);
      
      if (!user) {
        // Check if user exists with the same email
        user = await this.userService.findUserByEmail(userData.email);
        
        if (user) {
          // Link provider to existing user
          user = await this.userService.linkProviderToUser(user.id, provider.id, userData.providerUserId);
        } else {
          // Create new user
          user = await this.userService.createUser({
            ...userData,
            role: 'user',
            permissions: ['read']
          });
        }
      }
      
      // Generate JWT tokens
      const accessToken = this.generateAccessToken(user);
      const refreshToken = this.generateRefreshToken(user);
      
      // Clean up state
      await this.deleteState(state);
      
      // Log audit event
      await this.auditService.logEvent({
        userId: user.id,
        action: 'LOGIN',
        resourceType: 'user',
        resourceId: user.id,
        details: {
          method: 'oauth2',
          providerId: provider.id,
          providerName: provider.name
        }
      });
      
      return {
        token: accessToken,
        refreshToken,
        user: {
          id: user.id,
          email: user.email,
          username: user.username,
          firstName: user.firstName,
          lastName: user.lastName,
          role: user.role,
          permissions: user.permissions
        },
        provider: {
          id: provider.id,
          name: provider.name
        }
      };
    } catch (error) {
      console.error('Error processing OAuth2 callback:', error);
      throw error;
    }
  }

  /**
   * Generate access token
   */
  generateAccessToken(user) {
    const payload = {
      sub: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
      permissions: user.permissions,
      type: 'access'
    };
    
    return jwt.sign(payload, this.jwtSecret, { expiresIn: this.jwtExpiresIn });
  }

  /**
   * Generate refresh token
   */
  generateRefreshToken(user) {
    const payload = {
      sub: user.id,
      type: 'refresh'
    };
    
    return jwt.sign(payload, this.jwtSecret, { expiresIn: this.refreshTokenExpiresIn });
  }
}

module.exports = OAuth2Service;
