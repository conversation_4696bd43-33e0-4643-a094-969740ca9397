/**
 * Resonance Exports
 * 
 * This module exports all resonance-related components for easy access.
 */

const ResonanceValidator = require('./resonance_validator');
const ResonantTensorCore = require('./resonant_tensor_core');
const UnifiedResonantTensorCore = require('./unified_resonant_tensor_core');

// Export resonance pattern constants
const RESONANCE_PATTERN = {
  CYCLES: [3, 6, 9, 12],
  THRESHOLDS: [0.3, 0.6, 0.9],
  DECAY_RATES: [0.03, 0.06, 0.09, 0.12, 0.13],
  EFFECTIVENESS_TARGETS: [0.3, 0.6, 0.9]
};

/**
 * Create a resonance validator
 * @param {Object} options - Configuration options
 * @returns {ResonanceValidator} - Resonance validator instance
 */
function createResonanceValidator(options = {}) {
  return new ResonanceValidator(options);
}

/**
 * Create a resonant tensor core
 * @param {Object} tensorCore - Tensor core to wrap
 * @param {Object} options - Configuration options
 * @returns {ResonantTensorCore} - Resonant tensor core instance
 */
function createResonantTensorCore(tensorCore, options = {}) {
  return new ResonantTensorCore(tensorCore, options);
}

/**
 * Create a unified resonant tensor core
 * @param {Object} options - Configuration options
 * @returns {UnifiedResonantTensorCore} - Unified resonant tensor core instance
 */
function createUnifiedResonantTensorCore(options = {}) {
  return new UnifiedResonantTensorCore(options);
}

/**
 * Check if a value is resonant
 * @param {number} value - Value to check
 * @param {string} type - Type of value ('cycle', 'threshold', 'decay', 'factor')
 * @returns {boolean} - Whether the value is resonant
 */
function isResonant(value, type = 'generic') {
  const validator = new ResonanceValidator();
  return validator.isResonant(value, type);
}

/**
 * Harmonize a value to the nearest resonant value
 * @param {number} value - Value to harmonize
 * @param {string} type - Type of value ('cycle', 'threshold', 'decay', 'factor')
 * @returns {number} - Harmonized value
 */
function harmonize(value, type = 'generic') {
  const validator = new ResonanceValidator();
  return validator.harmonize(value, type);
}

/**
 * Validate a value against resonance constraints
 * @param {number} value - Value to validate
 * @param {string} type - Type of value ('cycle', 'threshold', 'decay', 'factor')
 * @returns {Object} - Validation result
 */
function validate(value, type = 'generic') {
  const validator = new ResonanceValidator();
  return validator.validate(value, type);
}

/**
 * Validate a tensor against resonance constraints
 * @param {Object} tensor - Tensor to validate
 * @returns {Object} - Validation result
 */
function validateTensor(tensor) {
  const validator = new ResonanceValidator();
  return validator.validateTensor(tensor);
}

module.exports = {
  // Classes
  ResonanceValidator,
  ResonantTensorCore,
  UnifiedResonantTensorCore,
  
  // Constants
  RESONANCE_PATTERN,
  
  // Factory functions
  createResonanceValidator,
  createResonantTensorCore,
  createUnifiedResonantTensorCore,
  
  // Utility functions
  isResonant,
  harmonize,
  validate,
  validateTensor
};
