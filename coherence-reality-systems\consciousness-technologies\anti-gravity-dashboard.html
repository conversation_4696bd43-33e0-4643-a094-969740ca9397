<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CHAEONIX Anti-Gravity Simulation</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #0a0a0a, #1a1a2e, #16213e);
            color: #ffffff;
            font-family: 'Courier New', monospace;
            overflow: hidden;
        }

        .dashboard {
            display: grid;
            grid-template-columns: 300px 1fr;
            height: 100vh;
        }

        .control-panel {
            background: rgba(0, 0, 0, 0.8);
            padding: 20px;
            border-right: 2px solid #00ff88;
            overflow-y: auto;
        }

        .simulation-view {
            position: relative;
            background: radial-gradient(circle at center, #001122, #000000);
        }

        .header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 1px solid #00ff88;
            padding-bottom: 10px;
        }

        .title {
            font-size: 18px;
            color: #00ff88;
            margin: 0;
        }

        .subtitle {
            font-size: 12px;
            color: #888;
            margin: 5px 0 0 0;
        }

        .control-group {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #333;
            border-radius: 5px;
            background: rgba(0, 255, 136, 0.05);
        }

        .control-label {
            color: #00ff88;
            font-size: 14px;
            margin-bottom: 10px;
            display: block;
        }

        .control-input {
            width: 100%;
            padding: 8px;
            background: rgba(0, 0, 0, 0.7);
            border: 1px solid #00ff88;
            color: #ffffff;
            border-radius: 3px;
            margin-bottom: 10px;
        }

        .btn {
            padding: 10px 15px;
            background: linear-gradient(45deg, #00ff88, #00cc66);
            color: #000;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            width: 100%;
            margin-bottom: 10px;
            transition: all 0.3s;
        }

        .btn:hover {
            background: linear-gradient(45deg, #00cc66, #00aa44);
            transform: translateY(-2px);
        }

        .btn:disabled {
            background: #333;
            color: #666;
            cursor: not-allowed;
            transform: none;
        }

        .metrics {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-top: 15px;
        }

        .metric {
            background: rgba(0, 0, 0, 0.6);
            padding: 10px;
            border-radius: 5px;
            text-align: center;
            border: 1px solid #333;
        }

        .metric-value {
            font-size: 18px;
            color: #00ff88;
            font-weight: bold;
        }

        .metric-label {
            font-size: 10px;
            color: #888;
            margin-top: 5px;
        }

        .simulation-canvas {
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 20% 80%, rgba(0, 255, 136, 0.1), transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 0, 136, 0.1), transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(136, 0, 255, 0.1), transparent 50%);
        }

        .vehicle-indicator {
            position: absolute;
            width: 20px;
            height: 20px;
            background: radial-gradient(circle, #00ff88, #00cc66);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            box-shadow: 0 0 20px #00ff88;
            transition: all 0.1s ease;
        }

        .vehicle-indicator.airborne {
            box-shadow: 0 0 30px #00ff88, 0 0 60px rgba(0, 255, 136, 0.5);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: translate(-50%, -50%) scale(1); }
            50% { transform: translate(-50%, -50%) scale(1.2); }
        }

        .field-visualization {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            opacity: 0.3;
        }

        .status-overlay {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            padding: 15px;
            border-radius: 10px;
            border: 1px solid #00ff88;
            min-width: 200px;
        }

        .status-title {
            color: #00ff88;
            font-size: 14px;
            margin-bottom: 10px;
            text-align: center;
        }

        .phi-signature {
            text-align: center;
            margin-top: 20px;
            padding: 10px;
            background: rgba(255, 215, 0, 0.1);
            border: 1px solid #ffd700;
            border-radius: 5px;
        }

        .phi-value {
            font-size: 16px;
            color: #ffd700;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="dashboard">
        <div class="control-panel">
            <div class="header">
                <h1 class="title">CHAEONIX</h1>
                <p class="subtitle">Anti-Gravity Simulation</p>
                <p class="subtitle">Ψκ-Field Modulation Engine</p>
            </div>

            <div class="control-group">
                <label class="control-label">Vehicle Configuration</label>
                <input type="number" class="control-input" id="vehicleMass" placeholder="Mass (kg)" value="1000">
                <input type="number" class="control-input" id="fieldRadius" placeholder="Field Radius (m)" value="5">
                <input type="number" class="control-input" id="fieldIntensity" placeholder="Ψ-Intensity" value="1.618" step="0.001">
                <button class="btn" id="createVehicle">Create Anti-Gravity Vehicle</button>
            </div>

            <div class="control-group">
                <label class="control-label">Field Control</label>
                <button class="btn" id="activateField">Activate Ψκ-Field</button>
                <button class="btn" id="deactivateField" disabled>Deactivate Field</button>
                <button class="btn" id="emergencyStop">Emergency Stop</button>
            </div>

            <div class="control-group">
                <label class="control-label">Simulation Control</label>
                <button class="btn" id="startSim">Start Simulation</button>
                <button class="btn" id="pauseSim" disabled>Pause Simulation</button>
                <button class="btn" id="resetSim">Reset Simulation</button>
            </div>

            <div class="metrics">
                <div class="metric">
                    <div class="metric-value" id="altitude">0.0</div>
                    <div class="metric-label">Altitude (m)</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="liftForce">0.0</div>
                    <div class="metric-label">Lift Force (N)</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="psiCoherence">1.000</div>
                    <div class="metric-label">Ψ-Coherence</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="fieldStability">95.0</div>
                    <div class="metric-label">CASTL Stability (%)</div>
                </div>
            </div>

            <div class="phi-signature">
                <div class="phi-value">φ = 1.618</div>
                <div style="font-size: 10px; color: #888; margin-top: 5px;">Golden Ratio Alignment</div>
            </div>
        </div>

        <div class="simulation-view">
            <canvas class="simulation-canvas" id="simulationCanvas"></canvas>
            <div class="field-visualization" id="fieldViz"></div>
            
            <div class="status-overlay">
                <div class="status-title">System Status</div>
                <div id="systemStatus">
                    <div>🌌 Ψκ-Field: <span style="color: #00ff88;">STABLE</span></div>
                    <div>⚡ CASTL: <span style="color: #00ff88;">ACTIVE</span></div>
                    <div>🔮 UUFT: <span style="color: #00ff88;">ONLINE</span></div>
                    <div>🛸 Vehicle: <span style="color: #888;">NONE</span></div>
                </div>
            </div>
        </div>
    </div>

    <script src="anti-gravity-simulation-engine.js"></script>
    <script>
        // Initialize simulation
        let simulation = new AntiGravitySimulationEngine();
        let currentVehicle = null;
        let isRunning = false;
        let animationFrame = null;

        // Canvas setup
        const canvas = document.getElementById('simulationCanvas');
        const ctx = canvas.getContext('2d');
        
        function resizeCanvas() {
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;
        }
        
        window.addEventListener('resize', resizeCanvas);
        resizeCanvas();

        // Control handlers
        document.getElementById('createVehicle').addEventListener('click', () => {
            const mass = parseFloat(document.getElementById('vehicleMass').value) || 1000;
            const radius = parseFloat(document.getElementById('fieldRadius').value) || 5;
            const intensity = parseFloat(document.getElementById('fieldIntensity').value) || 1.618;
            
            currentVehicle = simulation.createAntiGravityVehicle({
                mass: mass,
                fieldRadius: radius,
                fieldIntensity: intensity,
                position: { x: 50, y: 5, z: 50 }
            });
            
            updateSystemStatus();
        });

        document.getElementById('activateField').addEventListener('click', () => {
            if (currentVehicle) {
                simulation.activateAntiGravity(currentVehicle.id);
                document.getElementById('activateField').disabled = true;
                document.getElementById('deactivateField').disabled = false;
                updateSystemStatus();
            }
        });

        document.getElementById('startSim').addEventListener('click', () => {
            isRunning = true;
            document.getElementById('startSim').disabled = true;
            document.getElementById('pauseSim').disabled = false;
            runSimulation();
        });

        document.getElementById('pauseSim').addEventListener('click', () => {
            isRunning = false;
            document.getElementById('startSim').disabled = false;
            document.getElementById('pauseSim').disabled = true;
            if (animationFrame) {
                cancelAnimationFrame(animationFrame);
            }
        });

        document.getElementById('resetSim').addEventListener('click', () => {
            isRunning = false;
            simulation = new AntiGravitySimulationEngine();
            currentVehicle = null;
            document.getElementById('startSim').disabled = false;
            document.getElementById('pauseSim').disabled = true;
            document.getElementById('activateField').disabled = false;
            document.getElementById('deactivateField').disabled = true;
            updateSystemStatus();
            clearCanvas();
        });

        function runSimulation() {
            if (!isRunning) return;
            
            const state = simulation.runSimulationStep();
            updateDisplay(state);
            updateMetrics(state);
            
            animationFrame = requestAnimationFrame(runSimulation);
        }

        function updateDisplay(state) {
            clearCanvas();
            
            // Draw field visualization
            drawFieldGrid();
            
            // Draw vehicles
            state.vehicles.forEach(vehicle => {
                drawVehicle(vehicle);
            });
        }

        function clearCanvas() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }

        function drawFieldGrid() {
            // Draw Ψκ-field visualization
            ctx.strokeStyle = 'rgba(0, 255, 136, 0.2)';
            ctx.lineWidth = 1;
            
            for (let i = 0; i < 10; i++) {
                const x = (canvas.width / 10) * i;
                const y = (canvas.height / 10) * i;
                
                ctx.beginPath();
                ctx.moveTo(x, 0);
                ctx.lineTo(x, canvas.height);
                ctx.stroke();
                
                ctx.beginPath();
                ctx.moveTo(0, y);
                ctx.lineTo(canvas.width, y);
                ctx.stroke();
            }
        }

        function drawVehicle(vehicle) {
            const x = (vehicle.position.x / 100) * canvas.width;
            const y = canvas.height - (vehicle.position.y / 50) * canvas.height;
            
            // Draw vehicle
            ctx.fillStyle = vehicle.isActive ? '#00ff88' : '#666';
            ctx.beginPath();
            ctx.arc(x, y, 10, 0, 2 * Math.PI);
            ctx.fill();
            
            // Draw field effect
            if (vehicle.isActive) {
                const gradient = ctx.createRadialGradient(x, y, 0, x, y, 30);
                gradient.addColorStop(0, 'rgba(0, 255, 136, 0.3)');
                gradient.addColorStop(1, 'rgba(0, 255, 136, 0)');
                
                ctx.fillStyle = gradient;
                ctx.beginPath();
                ctx.arc(x, y, 30, 0, 2 * Math.PI);
                ctx.fill();
            }
            
            // Draw status
            ctx.fillStyle = '#ffffff';
            ctx.font = '12px Courier New';
            ctx.fillText(vehicle.status, x + 15, y);
        }

        function updateMetrics(state) {
            if (state.vehicles.length > 0) {
                const vehicle = state.vehicles[0];
                document.getElementById('altitude').textContent = vehicle.altitude.toFixed(1);
                document.getElementById('liftForce').textContent = vehicle.liftForce.toFixed(1);
            }
            
            document.getElementById('psiCoherence').textContent = state.fieldStats.avgPsiCoherence.toFixed(3);
            document.getElementById('fieldStability').textContent = (state.fieldStats.fieldStability * 100).toFixed(1);
        }

        function updateSystemStatus() {
            const statusDiv = document.getElementById('systemStatus');
            const vehicleStatus = currentVehicle ? 
                (currentVehicle.isActive ? '<span style="color: #00ff88;">ACTIVE</span>' : '<span style="color: #ffd700;">READY</span>') : 
                '<span style="color: #888;">NONE</span>';
            
            statusDiv.innerHTML = `
                <div>🌌 Ψκ-Field: <span style="color: #00ff88;">STABLE</span></div>
                <div>⚡ CASTL: <span style="color: #00ff88;">ACTIVE</span></div>
                <div>🔮 UUFT: <span style="color: #00ff88;">ONLINE</span></div>
                <div>🛸 Vehicle: ${vehicleStatus}</div>
            `;
        }

        // Initialize display
        updateSystemStatus();
        console.log('🚀 CHAEONIX Anti-Gravity Dashboard Initialized');
    </script>
</body>
</html>
