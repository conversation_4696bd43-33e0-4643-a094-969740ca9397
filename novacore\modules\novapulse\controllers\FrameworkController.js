/**
 * NovaCore Framework Controller
 * 
 * This controller handles API requests related to compliance frameworks.
 * NovaPulse is the Universal Regulatory Compliance Monitoring System (URCMS) component
 * of the NovaFuse Cyber-Safety Platform.
 */

const { FrameworkService } = require('../services');
const logger = require('../../../config/logger');

class FrameworkController {
  /**
   * Create a new framework
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async createFramework(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      
      // Set organization ID if provided in params
      if (req.params.organizationId) {
        req.body.organizationId = req.params.organizationId;
        req.body.isCustom = true;
      }
      
      const framework = await FrameworkService.createFramework(req.body, userId);
      
      res.status(201).json({
        success: true,
        data: framework
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Get all frameworks
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async getAllFrameworks(req, res, next) {
    try {
      // Extract filter criteria from query params
      const filter = {};
      
      if (req.query.name) filter.name = req.query.name;
      if (req.query.shortName) filter.shortName = req.query.shortName;
      if (req.query.type) filter.type = req.query.type;
      if (req.query.category) filter.category = req.query.category;
      if (req.query.status) filter.status = req.query.status;
      if (req.query.country) filter.country = req.query.country;
      if (req.query.region) filter.region = req.query.region;
      if (req.query.industry) filter.industry = req.query.industry;
      if (req.query.organizationType) filter.organizationType = req.query.organizationType;
      if (req.query.dataType) filter.dataType = req.query.dataType;
      if (req.query.isCustom !== undefined) filter.isCustom = req.query.isCustom;
      if (req.query.search) filter.search = req.query.search;
      
      // Set organization ID if provided in params
      if (req.params.organizationId) {
        filter.organizationId = req.params.organizationId;
      }
      
      // Extract pagination and sorting options
      const options = {
        page: parseInt(req.query.page, 10) || 1,
        limit: parseInt(req.query.limit, 10) || 10
      };
      
      if (req.query.sort) {
        const [field, order] = req.query.sort.split(':');
        options.sort = { [field]: order === 'desc' ? -1 : 1 };
      }
      
      const result = await FrameworkService.getAllFrameworks(filter, options);
      
      res.status(200).json({
        success: true,
        data: result.data,
        pagination: result.pagination
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Get framework by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async getFrameworkById(req, res, next) {
    try {
      const framework = await FrameworkService.getFrameworkById(req.params.id);
      
      res.status(200).json({
        success: true,
        data: framework
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Update framework
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async updateFramework(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const framework = await FrameworkService.updateFramework(req.params.id, req.body, userId);
      
      res.status(200).json({
        success: true,
        data: framework
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Delete framework
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async deleteFramework(req, res, next) {
    try {
      await FrameworkService.deleteFramework(req.params.id);
      
      res.status(200).json({
        success: true,
        message: 'Framework deleted successfully'
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Add control to framework
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async addControl(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const framework = await FrameworkService.addControl(req.params.id, req.body, userId);
      
      res.status(200).json({
        success: true,
        data: framework
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Update control
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async updateControl(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const framework = await FrameworkService.updateControl(
        req.params.id, 
        req.params.controlId, 
        req.body, 
        userId
      );
      
      res.status(200).json({
        success: true,
        data: framework
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Remove control
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async removeControl(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const framework = await FrameworkService.removeControl(
        req.params.id, 
        req.params.controlId, 
        userId
      );
      
      res.status(200).json({
        success: true,
        data: framework
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Add domain to framework
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async addDomain(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const framework = await FrameworkService.addDomain(req.params.id, req.body, userId);
      
      res.status(200).json({
        success: true,
        data: framework
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Add category to framework
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async addCategory(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const framework = await FrameworkService.addCategory(req.params.id, req.body, userId);
      
      res.status(200).json({
        success: true,
        data: framework
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Add version to framework
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async addVersion(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const framework = await FrameworkService.addVersion(req.params.id, req.body, userId);
      
      res.status(200).json({
        success: true,
        data: framework
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Find frameworks by type
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async findByType(req, res, next) {
    try {
      const frameworks = await FrameworkService.findByType(req.params.type);
      
      res.status(200).json({
        success: true,
        data: frameworks
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Find frameworks by category
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async findByCategory(req, res, next) {
    try {
      const frameworks = await FrameworkService.findByCategory(req.params.category);
      
      res.status(200).json({
        success: true,
        data: frameworks
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Find frameworks by jurisdiction
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async findByJurisdiction(req, res, next) {
    try {
      const { country, region } = req.query;
      const frameworks = await FrameworkService.findByJurisdiction(country, region);
      
      res.status(200).json({
        success: true,
        data: frameworks
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Find frameworks by industry
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async findByIndustry(req, res, next) {
    try {
      const frameworks = await FrameworkService.findByIndustry(req.params.industry);
      
      res.status(200).json({
        success: true,
        data: frameworks
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Find frameworks by control mapping
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async findByControlMapping(req, res, next) {
    try {
      const { frameworkId, controlId } = req.params;
      const frameworks = await FrameworkService.findByControlMapping(frameworkId, controlId);
      
      res.status(200).json({
        success: true,
        data: frameworks
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Find frameworks by applicability
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async findByApplicability(req, res, next) {
    try {
      const criteria = {
        industry: req.query.industry,
        region: req.query.region,
        organizationType: req.query.organizationType,
        dataTypes: req.query.dataTypes ? req.query.dataTypes.split(',') : undefined
      };
      
      const frameworks = await FrameworkService.findByApplicability(criteria);
      
      res.status(200).json({
        success: true,
        data: frameworks
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Find custom frameworks
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async findCustom(req, res, next) {
    try {
      const organizationId = req.params.organizationId;
      const frameworks = await FrameworkService.findCustom(organizationId);
      
      res.status(200).json({
        success: true,
        data: frameworks
      });
    } catch (error) {
      next(error);
    }
  }
}

module.exports = new FrameworkController();
