/**
 * Data Processing Activity Controller
 * 
 * This controller handles operations related to data processing activities.
 */

const { DataProcessingActivity } = require('../models');

// Get all data processing activities with pagination and filtering
const getAllDataProcessingActivities = async (req, res, next) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    
    // Build filter object
    const filter = {};
    
    if (req.query.status) {
      filter.status = req.query.status;
    }
    
    if (req.query.legalBasis) {
      filter.legalBasis = req.query.legalBasis;
    }
    
    if (req.query.search) {
      filter.$text = { $search: req.query.search };
    }
    
    // Build sort object
    const sort = {};
    
    if (req.query.sortBy) {
      sort[req.query.sortBy] = req.query.sortOrder === 'desc' ? -1 : 1;
    } else {
      sort.createdAt = -1; // Default sort by creation date descending
    }
    
    // Execute query with pagination
    const activities = await DataProcessingActivity
      .find(filter)
      .sort(sort)
      .skip(skip)
      .limit(limit);
    
    // Get total count for pagination
    const total = await DataProcessingActivity.countDocuments(filter);
    
    res.json({
      data: activities,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    next(error);
  }
};

// Get a specific data processing activity by ID
const getDataProcessingActivityById = async (req, res, next) => {
  try {
    const activity = await DataProcessingActivity.findById(req.params.id);
    
    if (!activity) {
      const error = new Error('Data processing activity not found');
      error.name = 'NotFoundError';
      throw error;
    }
    
    res.json({
      data: activity
    });
  } catch (error) {
    next(error);
  }
};

// Create a new data processing activity
const createDataProcessingActivity = async (req, res, next) => {
  try {
    const activity = new DataProcessingActivity(req.body);
    await activity.save();
    
    res.status(201).json({
      data: activity,
      message: 'Data processing activity created successfully'
    });
  } catch (error) {
    next(error);
  }
};

// Update a data processing activity
const updateDataProcessingActivity = async (req, res, next) => {
  try {
    const activity = await DataProcessingActivity.findById(req.params.id);
    
    if (!activity) {
      const error = new Error('Data processing activity not found');
      error.name = 'NotFoundError';
      throw error;
    }
    
    // Update only the fields that are provided in the request body
    Object.keys(req.body).forEach(key => {
      activity[key] = req.body[key];
    });
    
    await activity.save();
    
    res.json({
      data: activity,
      message: 'Data processing activity updated successfully'
    });
  } catch (error) {
    next(error);
  }
};

// Delete a data processing activity
const deleteDataProcessingActivity = async (req, res, next) => {
  try {
    const activity = await DataProcessingActivity.findById(req.params.id);
    
    if (!activity) {
      const error = new Error('Data processing activity not found');
      error.name = 'NotFoundError';
      throw error;
    }
    
    await activity.remove();
    
    res.json({
      message: 'Data processing activity deleted successfully'
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  getAllDataProcessingActivities,
  getDataProcessingActivityById,
  createDataProcessingActivity,
  updateDataProcessingActivity,
  deleteDataProcessingActivity
};
