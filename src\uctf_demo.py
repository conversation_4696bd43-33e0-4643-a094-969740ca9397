"""
Demo script for the Universal Compliance Testing Framework (UCTF).

This script demonstrates how to use the UCTF to define, execute, and report on
compliance tests.
"""

import os
import sys
import json
import logging
from typing import Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add the src directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Import the UCTF
from uctf import TestEngine
from uctf.utils.test_utils import get_test_result_summary

def custom_event_handler(event_data: Dict[str, Any]) -> None:
    """
    Custom handler for test events.
    
    Args:
        event_data: Data associated with the event
    """
    logger.info(f"Custom event handler: {event_data.get('id')}")
    
    if 'result' in event_data and event_data['result']:
        summary = get_test_result_summary(event_data['result'])
        logger.info(f"Test result summary: {summary}")

def main():
    """Run the UCTF demo."""
    logger.info("Starting UCTF demo")
    
    # Initialize the Test Engine
    engine = TestEngine()
    
    # Register a custom event handler for test completion
    engine.register_event_handler('test_completed', custom_event_handler)
    
    # Create output directory if it doesn't exist
    output_dir = os.path.join(os.path.dirname(__file__), 'uctf_output')
    os.makedirs(output_dir, exist_ok=True)
    
    # Run a GDPR data protection test
    logger.info("Running GDPR data protection test")
    
    gdpr_test_run = engine.run_test('gdpr_data_protection', {
        'strict_mode': True,
        'target_system': 'customer_database'
    })
    
    logger.info(f"GDPR test completed with status: {gdpr_test_run.get('status')}")
    
    # Save the test run to a file
    gdpr_test_run_path = os.path.join(output_dir, f"{gdpr_test_run.get('id')}.json")
    
    with open(gdpr_test_run_path, 'w', encoding='utf-8') as f:
        json.dump(gdpr_test_run, f, indent=2)
    
    logger.info(f"Saved GDPR test run to {gdpr_test_run_path}")
    
    # Generate a summary report
    logger.info("Generating summary report")
    
    summary_report = engine.generate_report('summary', gdpr_test_run.get('id'), {})
    
    # Save the report to a file
    summary_report_path = os.path.join(output_dir, f"{summary_report.get('id')}.json")
    
    with open(summary_report_path, 'w', encoding='utf-8') as f:
        json.dump(summary_report, f, indent=2)
    
    logger.info(f"Saved summary report to {summary_report_path}")
    
    # Run a HIPAA compliance test suite
    logger.info("Running HIPAA compliance test suite")
    
    hipaa_suite_run = engine.run_test_suite('hipaa_compliance', {
        'target_system': 'health_records_system'
    })
    
    logger.info(f"HIPAA test suite completed with status: {hipaa_suite_run.get('status')}")
    
    # Save the test suite run to a file
    hipaa_suite_run_path = os.path.join(output_dir, f"{hipaa_suite_run.get('id')}.json")
    
    with open(hipaa_suite_run_path, 'w', encoding='utf-8') as f:
        json.dump(hipaa_suite_run, f, indent=2)
    
    logger.info(f"Saved HIPAA test suite run to {hipaa_suite_run_path}")
    
    # Generate an executive report
    logger.info("Generating executive report")
    
    executive_report = engine.generate_report('executive', hipaa_suite_run.get('id'), {})
    
    # Save the report to a file
    executive_report_path = os.path.join(output_dir, f"{executive_report.get('id')}.json")
    
    with open(executive_report_path, 'w', encoding='utf-8') as f:
        json.dump(executive_report, f, indent=2)
    
    logger.info(f"Saved executive report to {executive_report_path}")
    
    logger.info("UCTF demo completed successfully")

if __name__ == "__main__":
    main()
