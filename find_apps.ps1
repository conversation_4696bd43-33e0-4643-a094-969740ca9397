# Simple script to find applications by looking for common entry points
$outputFile = "$PWD\APPLICATIONS_LIST_$(Get-Date -Format 'yyyyMMdd_HHmmss').md"
$baseDir = "d:\novafuse-api-superstore"

# Common entry points to look for
$entryPoints = @(
    "app.py", "main.py", "server.py", "index.js", "server.js", "app.js",
    "package.json", "requirements.txt", "pom.xml", "build.gradle"
)

$results = @()

foreach ($entry in $entryPoints) {
    Write-Host "Searching for $entry..."
    $files = Get-ChildItem -Path $baseDir -Filter $entry -Recurse -ErrorAction SilentlyContinue |
             Where-Object { $_.FullName -notmatch '\\node_modules\\' -and 
                          $_.FullName -notmatch '\\.next\\' -and
                          $_.FullName -notmatch '\\dist\\' -and
                          $_.FullName -notmatch '\\build\\' }
    
    foreach ($file in $files) {
        $dir = $file.DirectoryName
        $created = $file.CreationTime
        $modified = $file.LastWriteTime
        $size = "{0:N2} KB" -f ($file.Length / 1KB)
        
        $results += [PSCustomObject]@{
            Name = $file.Name
            Path = $dir.Replace($baseDir, '').TrimStart('\')
            Created = $created
            Modified = $modified
            Size = $size
            Type = $file.Extension
        }
    }
}

# Sort by creation date
$sortedResults = $results | Sort-Object -Property Created

# Generate markdown
$markdown = "# Applications Inventory
*Generated on $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')*

## Summary
- **Total Entry Points Found**: $($sortedResults.Count)

## Applications by Creation Date

| Application | Type | Created | Modified | Size | Path |
|-------------|------|---------|----------|------|------|"

foreach ($item in $sortedResults) {
    $markdown += "
| $($item.Name) | $($item.Type) | $($item.Created) | $($item.Modified) | $($item.Size) | $($item.Path) |"
}

# Write to file
try {
    $markdown | Out-File -FilePath $outputFile -Encoding utf8 -Force
    Write-Host "Applications list generated: $outputFile"
} catch {
    Write-Error "Error writing to file: $_"
    $outputFile = ".\APPLICATIONS_LIST_$(Get-Date -Format 'yyyyMMdd_HHmmss').md"
    $markdown | Out-File -FilePath $outputFile -Encoding utf8 -Force
    Write-Host "Applications list generated in current directory: $((Get-Item $outputFile).FullName)"
}

# Also output the location of the file
Write-Host "`nApplications list created at: $((Get-Item $outputFile).FullName)"
