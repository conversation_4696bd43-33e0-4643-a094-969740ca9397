{"malware_detection": 0.95, "phishing_detection": 0.9, "ransomware_detection": 0.85, "data_exfiltration_detection": 0.8, "lateral_movement_detection": 0.75, "privilege_escalation_detection": 0.95, "command_and_control_detection": 0.9, "brute_force_detection": 0.85, "credential_theft_detection": 0.8, "insider_threat_detection": 0.75, "vulnerability_management": 0.95, "patch_management": 0.9, "configuration_management": 0.85, "asset_inventory": 0.8, "network_segmentation": 0.75, "endpoint_protection": 0.95, "email_security": 0.9, "web_security": 0.85, "cloud_security": 0.8, "mobile_security": 0.75, "identity_management": 0.95, "access_control": 0.9, "privileged_access_management": 0.85, "multi_factor_authentication": 0.8, "single_sign_on": 0.75, "data_classification": 0.95, "data_loss_prevention": 0.9, "encryption_at_rest": 0.85, "encryption_in_transit": 0.8, "key_management": 0.75, "security_monitoring": 0.95, "security_analytics": 0.9, "security_orchestration": 0.85, "security_automation": 0.8, "incident_response": 0.75, "threat_intelligence": 0.95, "threat_hunting": 0.9, "digital_forensics": 0.85, "penetration_testing": 0.8, "red_team_exercises": 0.75, "security_awareness_training": 0.95, "phishing_simulation": 0.9, "security_policy_compliance": 0.85, "security_risk_assessment": 0.8, "third_party_risk_management": 0.75, "network_traffic_analysis": 0.95, "dns_monitoring": 0.9, "ssl_tls_inspection": 0.85, "intrusion_detection": 0.8, "intrusion_prevention": 0.75, "web_application_firewall": 0.95, "api_security": 0.9, "container_security": 0.85, "serverless_security": 0.8, "iot_security": 0.75, "devsecops": 0.95, "secure_coding_practices": 0.9, "code_scanning": 0.85, "dependency_scanning": 0.8, "infrastructure_as_code_security": 0.75, "backup_and_recovery": 0.95, "disaster_recovery": 0.9, "business_continuity": 0.85, "security_architecture": 0.8, "zero_trust_architecture": 0.75, "security_by_design": 0.95, "security_testing": 0.9, "security_benchmarking": 0.85, "security_metrics": 0.8, "security_reporting": 0.75, "security_governance": 0.95, "security_strategy": 0.9, "security_roadmap": 0.85, "security_budget": 0.8, "security_staffing": 0.75, "security_tools_integration": 0.95, "security_automation_integration": 0.9, "security_analytics_integration": 0.85, "security_orchestration_integration": 0.8, "security_reporting_integration": 0.75, "cloud_security_posture_management": 0.95, "cloud_workload_protection": 0.9, "cloud_infrastructure_entitlement_management": 0.85, "cloud_security_monitoring": 0.8, "cloud_security_automation": 0.75, "endpoint_detection_and_response": 0.95, "extended_detection_and_response": 0.9, "managed_detection_and_response": 0.85, "user_behavior_analytics": 0.8, "entity_behavior_analytics": 0.75, "security_information_event_management": 0.95, "security_orchestration_automation_response": 0.9, "threat_intelligence_platform": 0.85, "vulnerability_management_platform": 0.8, "governance_risk_compliance_platform": 0.75}