{"version": 3, "names": ["_getJest<PERSON>bj", "mock", "promises", "mkdir", "jest", "fn", "mockResolvedValue", "undefined", "readFile", "writeFile", "require", "BruteForceProtectionService", "BruteForceError", "fs", "path", "mockDateNow", "spyOn", "Date", "describe", "bruteForceService", "testDataDir", "join", "__dirname", "beforeEach", "clearAllMocks", "mockReset", "mockImplementation", "config", "maxAttempts", "windowMs", "blockDuration", "it", "expect", "dataDir", "toBe", "attemptsFile", "toHaveBeenCalledWith", "recursive", "mockAttempts", "count", "firstAttempt", "lastAttempt", "blocked", "blockedU<PERSON>l", "mockResolvedValueOnce", "JSON", "stringify", "attempts", "loadAttempts", "toEqual", "error", "Error", "code", "mockRejectedValueOnce", "rejects", "toThrow", "saveAttempts", "result", "recordFailedAttempt", "toHaveBeenCalled", "existingAttempts", "isBlocked", "remainingTime", "attemptsCount", "checkLoginAttempt"], "sources": ["BruteForceProtectionService.test.js"], "sourcesContent": ["/**\n * Brute Force Protection Service Tests\n */\n\nconst BruteForceProtectionService = require('../../../api/services/BruteForceProtectionService');\nconst { BruteForceError } = require('../../../api/utils/errors');\nconst fs = require('fs').promises;\nconst path = require('path');\n\n// Mock fs.promises\njest.mock('fs', () => ({\n  promises: {\n    mkdir: jest.fn().mockResolvedValue(undefined),\n    readFile: jest.fn(),\n    writeFile: jest.fn().mockResolvedValue(undefined)\n  }\n}));\n\n// Mock Date.now to control time\nconst mockDateNow = jest.spyOn(Date, 'now');\n\ndescribe('BruteForceProtectionService', () => {\n  let bruteForceService;\n  const testDataDir = path.join(__dirname, 'test-data');\n  \n  beforeEach(() => {\n    // Reset mocks\n    jest.clearAllMocks();\n    \n    // Reset Date.now mock\n    mockDateNow.mockReset();\n    mockDateNow.mockImplementation(() => 1000); // Mock current time\n    \n    // Create a new instance for each test\n    bruteForceService = new BruteForceProtectionService(testDataDir);\n    \n    // Override the config for testing\n    bruteForceService.config = {\n      maxAttempts: 3,\n      windowMs: 15 * 60 * 1000, // 15 minutes\n      blockDuration: 30 * 60 * 1000 // 30 minutes\n    };\n  });\n  \n  describe('constructor', () => {\n    it('should initialize with the correct data directory', () => {\n      expect(bruteForceService.dataDir).toBe(testDataDir);\n      expect(bruteForceService.attemptsFile).toBe(path.join(testDataDir, 'login_attempts.json'));\n    });\n    \n    it('should call ensureDataDir', () => {\n      expect(fs.mkdir).toHaveBeenCalledWith(testDataDir, { recursive: true });\n    });\n  });\n  \n  describe('loadAttempts', () => {\n    it('should load attempts from file', async () => {\n      const mockAttempts = {\n        'test-user': {\n          count: 1,\n          firstAttempt: 500,\n          lastAttempt: 500,\n          blocked: false,\n          blockedUntil: null\n        }\n      };\n      \n      fs.readFile.mockResolvedValueOnce(JSON.stringify(mockAttempts));\n      \n      const attempts = await bruteForceService.loadAttempts();\n      \n      expect(fs.readFile).toHaveBeenCalledWith(bruteForceService.attemptsFile, 'utf8');\n      expect(attempts).toEqual(mockAttempts);\n    });\n    \n    it('should return empty object if file does not exist', async () => {\n      const error = new Error('File not found');\n      error.code = 'ENOENT';\n      fs.readFile.mockRejectedValueOnce(error);\n      \n      const attempts = await bruteForceService.loadAttempts();\n      \n      expect(attempts).toEqual({});\n    });\n    \n    it('should throw error if file read fails for other reasons', async () => {\n      const error = new Error('Permission denied');\n      fs.readFile.mockRejectedValueOnce(error);\n      \n      await expect(bruteForceService.loadAttempts()).rejects.toThrow('Permission denied');\n    });\n  });\n  \n  describe('saveAttempts', () => {\n    it('should save attempts to file', async () => {\n      const attempts = {\n        'test-user': {\n          count: 1,\n          firstAttempt: 500,\n          lastAttempt: 500,\n          blocked: false,\n          blockedUntil: null\n        }\n      };\n      \n      await bruteForceService.saveAttempts(attempts);\n      \n      expect(fs.writeFile).toHaveBeenCalledWith(\n        bruteForceService.attemptsFile,\n        JSON.stringify(attempts, null, 2)\n      );\n    });\n    \n    it('should throw error if file write fails', async () => {\n      const error = new Error('Permission denied');\n      fs.writeFile.mockRejectedValueOnce(error);\n      \n      await expect(bruteForceService.saveAttempts({})).rejects.toThrow('Permission denied');\n    });\n  });\n  \n  describe('recordFailedAttempt', () => {\n    it('should initialize attempts for new identifier', async () => {\n      fs.readFile.mockResolvedValueOnce(JSON.stringify({}));\n      \n      const result = await bruteForceService.recordFailedAttempt('test-user');\n      \n      expect(result).toEqual({\n        count: 1,\n        firstAttempt: 1000,\n        lastAttempt: 1000,\n        blocked: false,\n        blockedUntil: null\n      });\n      \n      expect(fs.writeFile).toHaveBeenCalled();\n    });\n    \n    it('should increment count for existing identifier', async () => {\n      const existingAttempts = {\n        'test-user': {\n          count: 1,\n          firstAttempt: 500,\n          lastAttempt: 500,\n          blocked: false,\n          blockedUntil: null\n        }\n      };\n      \n      fs.readFile.mockResolvedValueOnce(JSON.stringify(existingAttempts));\n      \n      const result = await bruteForceService.recordFailedAttempt('test-user');\n      \n      expect(result).toEqual({\n        count: 2,\n        firstAttempt: 500,\n        lastAttempt: 1000,\n        blocked: false,\n        blockedUntil: null\n      });\n      \n      expect(fs.writeFile).toHaveBeenCalled();\n    });\n    \n    it('should block identifier after max attempts', async () => {\n      const existingAttempts = {\n        'test-user': {\n          count: 2,\n          firstAttempt: 500,\n          lastAttempt: 500,\n          blocked: false,\n          blockedUntil: null\n        }\n      };\n      \n      fs.readFile.mockResolvedValueOnce(JSON.stringify(existingAttempts));\n      \n      const result = await bruteForceService.recordFailedAttempt('test-user');\n      \n      expect(result).toEqual({\n        count: 3,\n        firstAttempt: 500,\n        lastAttempt: 1000,\n        blocked: true,\n        blockedUntil: 1000 + bruteForceService.config.blockDuration\n      });\n      \n      expect(fs.writeFile).toHaveBeenCalled();\n    });\n  });\n  \n  describe('isBlocked', () => {\n    it('should return false for unknown identifier', async () => {\n      fs.readFile.mockResolvedValueOnce(JSON.stringify({}));\n      \n      const result = await bruteForceService.isBlocked('test-user');\n      \n      expect(result).toBe(false);\n    });\n    \n    it('should return false for non-blocked identifier', async () => {\n      const existingAttempts = {\n        'test-user': {\n          count: 1,\n          firstAttempt: 500,\n          lastAttempt: 500,\n          blocked: false,\n          blockedUntil: null\n        }\n      };\n      \n      fs.readFile.mockResolvedValueOnce(JSON.stringify(existingAttempts));\n      \n      const result = await bruteForceService.isBlocked('test-user');\n      \n      expect(result).toBe(false);\n    });\n    \n    it('should return block info for blocked identifier', async () => {\n      const blockedUntil = 1000 + 60000; // 1 minute from now\n      const existingAttempts = {\n        'test-user': {\n          count: 3,\n          firstAttempt: 500,\n          lastAttempt: 500,\n          blocked: true,\n          blockedUntil\n        }\n      };\n      \n      fs.readFile.mockResolvedValueOnce(JSON.stringify(existingAttempts));\n      \n      const result = await bruteForceService.isBlocked('test-user');\n      \n      expect(result).toEqual({\n        blocked: true,\n        remainingTime: 60,\n        attemptsCount: 3,\n        maxAttempts: 3\n      });\n    });\n    \n    it('should reset block if block duration has expired', async () => {\n      const blockedUntil = 500; // Already expired\n      const existingAttempts = {\n        'test-user': {\n          count: 3,\n          firstAttempt: 100,\n          lastAttempt: 100,\n          blocked: true,\n          blockedUntil\n        }\n      };\n      \n      fs.readFile.mockResolvedValueOnce(JSON.stringify(existingAttempts));\n      \n      const result = await bruteForceService.isBlocked('test-user');\n      \n      expect(result).toBe(false);\n      expect(fs.writeFile).toHaveBeenCalled();\n    });\n  });\n  \n  describe('checkLoginAttempt', () => {\n    it('should throw BruteForceError if identifier is blocked', async () => {\n      const blockedUntil = 1000 + 60000; // 1 minute from now\n      const existingAttempts = {\n        'test-user': {\n          count: 3,\n          firstAttempt: 500,\n          lastAttempt: 500,\n          blocked: true,\n          blockedUntil\n        }\n      };\n      \n      fs.readFile.mockResolvedValueOnce(JSON.stringify(existingAttempts));\n      \n      await expect(bruteForceService.checkLoginAttempt('test-user'))\n        .rejects.toThrow(BruteForceError);\n    });\n    \n    it('should return true if identifier is not blocked', async () => {\n      fs.readFile.mockResolvedValueOnce(JSON.stringify({}));\n      \n      const result = await bruteForceService.checkLoginAttempt('test-user');\n      \n      expect(result).toBe(true);\n    });\n  });\n});\n"], "mappings": "AASA;AACAA,WAAA,GAAKC,IAAI,CAAC,IAAI,EAAE,OAAO;EACrBC,QAAQ,EAAE;IACRC,KAAK,EAAEC,IAAI,CAACC,EAAE,CAAC,CAAC,CAACC,iBAAiB,CAACC,SAAS,CAAC;IAC7CC,QAAQ,EAAEJ,IAAI,CAACC,EAAE,CAAC,CAAC;IACnBI,SAAS,EAAEL,IAAI,CAACC,EAAE,CAAC,CAAC,CAACC,iBAAiB,CAACC,SAAS;EAClD;AACF,CAAC,CAAC,CAAC;;AAEH;AAAA,SAAAP,YAAA;EAAA;IAAAI;EAAA,IAAAM,OAAA;EAAAV,WAAA,GAAAA,CAAA,KAAAI,IAAA;EAAA,OAAAA,IAAA;AAAA;AAlBA;AACA;AACA;;AAEA,MAAMO,2BAA2B,GAAGD,OAAO,CAAC,mDAAmD,CAAC;AAChG,MAAM;EAAEE;AAAgB,CAAC,GAAGF,OAAO,CAAC,2BAA2B,CAAC;AAChE,MAAMG,EAAE,GAAGH,OAAO,CAAC,IAAI,CAAC,CAACR,QAAQ;AACjC,MAAMY,IAAI,GAAGJ,OAAO,CAAC,MAAM,CAAC;AAY5B,MAAMK,WAAW,GAAGX,IAAI,CAACY,KAAK,CAACC,IAAI,EAAE,KAAK,CAAC;AAE3CC,QAAQ,CAAC,6BAA6B,EAAE,MAAM;EAC5C,IAAIC,iBAAiB;EACrB,MAAMC,WAAW,GAAGN,IAAI,CAACO,IAAI,CAACC,SAAS,EAAE,WAAW,CAAC;EAErDC,UAAU,CAAC,MAAM;IACf;IACAnB,IAAI,CAACoB,aAAa,CAAC,CAAC;;IAEpB;IACAT,WAAW,CAACU,SAAS,CAAC,CAAC;IACvBV,WAAW,CAACW,kBAAkB,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC;;IAE5C;IACAP,iBAAiB,GAAG,IAAIR,2BAA2B,CAACS,WAAW,CAAC;;IAEhE;IACAD,iBAAiB,CAACQ,MAAM,GAAG;MACzBC,WAAW,EAAE,CAAC;MACdC,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;MAAE;MAC1BC,aAAa,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IAChC,CAAC;EACH,CAAC,CAAC;EAEFZ,QAAQ,CAAC,aAAa,EAAE,MAAM;IAC5Ba,EAAE,CAAC,mDAAmD,EAAE,MAAM;MAC5DC,MAAM,CAACb,iBAAiB,CAACc,OAAO,CAAC,CAACC,IAAI,CAACd,WAAW,CAAC;MACnDY,MAAM,CAACb,iBAAiB,CAACgB,YAAY,CAAC,CAACD,IAAI,CAACpB,IAAI,CAACO,IAAI,CAACD,WAAW,EAAE,qBAAqB,CAAC,CAAC;IAC5F,CAAC,CAAC;IAEFW,EAAE,CAAC,2BAA2B,EAAE,MAAM;MACpCC,MAAM,CAACnB,EAAE,CAACV,KAAK,CAAC,CAACiC,oBAAoB,CAAChB,WAAW,EAAE;QAAEiB,SAAS,EAAE;MAAK,CAAC,CAAC;IACzE,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFnB,QAAQ,CAAC,cAAc,EAAE,MAAM;IAC7Ba,EAAE,CAAC,gCAAgC,EAAE,YAAY;MAC/C,MAAMO,YAAY,GAAG;QACnB,WAAW,EAAE;UACXC,KAAK,EAAE,CAAC;UACRC,YAAY,EAAE,GAAG;UACjBC,WAAW,EAAE,GAAG;UAChBC,OAAO,EAAE,KAAK;UACdC,YAAY,EAAE;QAChB;MACF,CAAC;MAED9B,EAAE,CAACL,QAAQ,CAACoC,qBAAqB,CAACC,IAAI,CAACC,SAAS,CAACR,YAAY,CAAC,CAAC;MAE/D,MAAMS,QAAQ,GAAG,MAAM5B,iBAAiB,CAAC6B,YAAY,CAAC,CAAC;MAEvDhB,MAAM,CAACnB,EAAE,CAACL,QAAQ,CAAC,CAAC4B,oBAAoB,CAACjB,iBAAiB,CAACgB,YAAY,EAAE,MAAM,CAAC;MAChFH,MAAM,CAACe,QAAQ,CAAC,CAACE,OAAO,CAACX,YAAY,CAAC;IACxC,CAAC,CAAC;IAEFP,EAAE,CAAC,mDAAmD,EAAE,YAAY;MAClE,MAAMmB,KAAK,GAAG,IAAIC,KAAK,CAAC,gBAAgB,CAAC;MACzCD,KAAK,CAACE,IAAI,GAAG,QAAQ;MACrBvC,EAAE,CAACL,QAAQ,CAAC6C,qBAAqB,CAACH,KAAK,CAAC;MAExC,MAAMH,QAAQ,GAAG,MAAM5B,iBAAiB,CAAC6B,YAAY,CAAC,CAAC;MAEvDhB,MAAM,CAACe,QAAQ,CAAC,CAACE,OAAO,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC,CAAC;IAEFlB,EAAE,CAAC,yDAAyD,EAAE,YAAY;MACxE,MAAMmB,KAAK,GAAG,IAAIC,KAAK,CAAC,mBAAmB,CAAC;MAC5CtC,EAAE,CAACL,QAAQ,CAAC6C,qBAAqB,CAACH,KAAK,CAAC;MAExC,MAAMlB,MAAM,CAACb,iBAAiB,CAAC6B,YAAY,CAAC,CAAC,CAAC,CAACM,OAAO,CAACC,OAAO,CAAC,mBAAmB,CAAC;IACrF,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFrC,QAAQ,CAAC,cAAc,EAAE,MAAM;IAC7Ba,EAAE,CAAC,8BAA8B,EAAE,YAAY;MAC7C,MAAMgB,QAAQ,GAAG;QACf,WAAW,EAAE;UACXR,KAAK,EAAE,CAAC;UACRC,YAAY,EAAE,GAAG;UACjBC,WAAW,EAAE,GAAG;UAChBC,OAAO,EAAE,KAAK;UACdC,YAAY,EAAE;QAChB;MACF,CAAC;MAED,MAAMxB,iBAAiB,CAACqC,YAAY,CAACT,QAAQ,CAAC;MAE9Cf,MAAM,CAACnB,EAAE,CAACJ,SAAS,CAAC,CAAC2B,oBAAoB,CACvCjB,iBAAiB,CAACgB,YAAY,EAC9BU,IAAI,CAACC,SAAS,CAACC,QAAQ,EAAE,IAAI,EAAE,CAAC,CAClC,CAAC;IACH,CAAC,CAAC;IAEFhB,EAAE,CAAC,wCAAwC,EAAE,YAAY;MACvD,MAAMmB,KAAK,GAAG,IAAIC,KAAK,CAAC,mBAAmB,CAAC;MAC5CtC,EAAE,CAACJ,SAAS,CAAC4C,qBAAqB,CAACH,KAAK,CAAC;MAEzC,MAAMlB,MAAM,CAACb,iBAAiB,CAACqC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAACF,OAAO,CAACC,OAAO,CAAC,mBAAmB,CAAC;IACvF,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFrC,QAAQ,CAAC,qBAAqB,EAAE,MAAM;IACpCa,EAAE,CAAC,+CAA+C,EAAE,YAAY;MAC9DlB,EAAE,CAACL,QAAQ,CAACoC,qBAAqB,CAACC,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAErD,MAAMW,MAAM,GAAG,MAAMtC,iBAAiB,CAACuC,mBAAmB,CAAC,WAAW,CAAC;MAEvE1B,MAAM,CAACyB,MAAM,CAAC,CAACR,OAAO,CAAC;QACrBV,KAAK,EAAE,CAAC;QACRC,YAAY,EAAE,IAAI;QAClBC,WAAW,EAAE,IAAI;QACjBC,OAAO,EAAE,KAAK;QACdC,YAAY,EAAE;MAChB,CAAC,CAAC;MAEFX,MAAM,CAACnB,EAAE,CAACJ,SAAS,CAAC,CAACkD,gBAAgB,CAAC,CAAC;IACzC,CAAC,CAAC;IAEF5B,EAAE,CAAC,gDAAgD,EAAE,YAAY;MAC/D,MAAM6B,gBAAgB,GAAG;QACvB,WAAW,EAAE;UACXrB,KAAK,EAAE,CAAC;UACRC,YAAY,EAAE,GAAG;UACjBC,WAAW,EAAE,GAAG;UAChBC,OAAO,EAAE,KAAK;UACdC,YAAY,EAAE;QAChB;MACF,CAAC;MAED9B,EAAE,CAACL,QAAQ,CAACoC,qBAAqB,CAACC,IAAI,CAACC,SAAS,CAACc,gBAAgB,CAAC,CAAC;MAEnE,MAAMH,MAAM,GAAG,MAAMtC,iBAAiB,CAACuC,mBAAmB,CAAC,WAAW,CAAC;MAEvE1B,MAAM,CAACyB,MAAM,CAAC,CAACR,OAAO,CAAC;QACrBV,KAAK,EAAE,CAAC;QACRC,YAAY,EAAE,GAAG;QACjBC,WAAW,EAAE,IAAI;QACjBC,OAAO,EAAE,KAAK;QACdC,YAAY,EAAE;MAChB,CAAC,CAAC;MAEFX,MAAM,CAACnB,EAAE,CAACJ,SAAS,CAAC,CAACkD,gBAAgB,CAAC,CAAC;IACzC,CAAC,CAAC;IAEF5B,EAAE,CAAC,4CAA4C,EAAE,YAAY;MAC3D,MAAM6B,gBAAgB,GAAG;QACvB,WAAW,EAAE;UACXrB,KAAK,EAAE,CAAC;UACRC,YAAY,EAAE,GAAG;UACjBC,WAAW,EAAE,GAAG;UAChBC,OAAO,EAAE,KAAK;UACdC,YAAY,EAAE;QAChB;MACF,CAAC;MAED9B,EAAE,CAACL,QAAQ,CAACoC,qBAAqB,CAACC,IAAI,CAACC,SAAS,CAACc,gBAAgB,CAAC,CAAC;MAEnE,MAAMH,MAAM,GAAG,MAAMtC,iBAAiB,CAACuC,mBAAmB,CAAC,WAAW,CAAC;MAEvE1B,MAAM,CAACyB,MAAM,CAAC,CAACR,OAAO,CAAC;QACrBV,KAAK,EAAE,CAAC;QACRC,YAAY,EAAE,GAAG;QACjBC,WAAW,EAAE,IAAI;QACjBC,OAAO,EAAE,IAAI;QACbC,YAAY,EAAE,IAAI,GAAGxB,iBAAiB,CAACQ,MAAM,CAACG;MAChD,CAAC,CAAC;MAEFE,MAAM,CAACnB,EAAE,CAACJ,SAAS,CAAC,CAACkD,gBAAgB,CAAC,CAAC;IACzC,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFzC,QAAQ,CAAC,WAAW,EAAE,MAAM;IAC1Ba,EAAE,CAAC,4CAA4C,EAAE,YAAY;MAC3DlB,EAAE,CAACL,QAAQ,CAACoC,qBAAqB,CAACC,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAErD,MAAMW,MAAM,GAAG,MAAMtC,iBAAiB,CAAC0C,SAAS,CAAC,WAAW,CAAC;MAE7D7B,MAAM,CAACyB,MAAM,CAAC,CAACvB,IAAI,CAAC,KAAK,CAAC;IAC5B,CAAC,CAAC;IAEFH,EAAE,CAAC,gDAAgD,EAAE,YAAY;MAC/D,MAAM6B,gBAAgB,GAAG;QACvB,WAAW,EAAE;UACXrB,KAAK,EAAE,CAAC;UACRC,YAAY,EAAE,GAAG;UACjBC,WAAW,EAAE,GAAG;UAChBC,OAAO,EAAE,KAAK;UACdC,YAAY,EAAE;QAChB;MACF,CAAC;MAED9B,EAAE,CAACL,QAAQ,CAACoC,qBAAqB,CAACC,IAAI,CAACC,SAAS,CAACc,gBAAgB,CAAC,CAAC;MAEnE,MAAMH,MAAM,GAAG,MAAMtC,iBAAiB,CAAC0C,SAAS,CAAC,WAAW,CAAC;MAE7D7B,MAAM,CAACyB,MAAM,CAAC,CAACvB,IAAI,CAAC,KAAK,CAAC;IAC5B,CAAC,CAAC;IAEFH,EAAE,CAAC,iDAAiD,EAAE,YAAY;MAChE,MAAMY,YAAY,GAAG,IAAI,GAAG,KAAK,CAAC,CAAC;MACnC,MAAMiB,gBAAgB,GAAG;QACvB,WAAW,EAAE;UACXrB,KAAK,EAAE,CAAC;UACRC,YAAY,EAAE,GAAG;UACjBC,WAAW,EAAE,GAAG;UAChBC,OAAO,EAAE,IAAI;UACbC;QACF;MACF,CAAC;MAED9B,EAAE,CAACL,QAAQ,CAACoC,qBAAqB,CAACC,IAAI,CAACC,SAAS,CAACc,gBAAgB,CAAC,CAAC;MAEnE,MAAMH,MAAM,GAAG,MAAMtC,iBAAiB,CAAC0C,SAAS,CAAC,WAAW,CAAC;MAE7D7B,MAAM,CAACyB,MAAM,CAAC,CAACR,OAAO,CAAC;QACrBP,OAAO,EAAE,IAAI;QACboB,aAAa,EAAE,EAAE;QACjBC,aAAa,EAAE,CAAC;QAChBnC,WAAW,EAAE;MACf,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFG,EAAE,CAAC,kDAAkD,EAAE,YAAY;MACjE,MAAMY,YAAY,GAAG,GAAG,CAAC,CAAC;MAC1B,MAAMiB,gBAAgB,GAAG;QACvB,WAAW,EAAE;UACXrB,KAAK,EAAE,CAAC;UACRC,YAAY,EAAE,GAAG;UACjBC,WAAW,EAAE,GAAG;UAChBC,OAAO,EAAE,IAAI;UACbC;QACF;MACF,CAAC;MAED9B,EAAE,CAACL,QAAQ,CAACoC,qBAAqB,CAACC,IAAI,CAACC,SAAS,CAACc,gBAAgB,CAAC,CAAC;MAEnE,MAAMH,MAAM,GAAG,MAAMtC,iBAAiB,CAAC0C,SAAS,CAAC,WAAW,CAAC;MAE7D7B,MAAM,CAACyB,MAAM,CAAC,CAACvB,IAAI,CAAC,KAAK,CAAC;MAC1BF,MAAM,CAACnB,EAAE,CAACJ,SAAS,CAAC,CAACkD,gBAAgB,CAAC,CAAC;IACzC,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFzC,QAAQ,CAAC,mBAAmB,EAAE,MAAM;IAClCa,EAAE,CAAC,uDAAuD,EAAE,YAAY;MACtE,MAAMY,YAAY,GAAG,IAAI,GAAG,KAAK,CAAC,CAAC;MACnC,MAAMiB,gBAAgB,GAAG;QACvB,WAAW,EAAE;UACXrB,KAAK,EAAE,CAAC;UACRC,YAAY,EAAE,GAAG;UACjBC,WAAW,EAAE,GAAG;UAChBC,OAAO,EAAE,IAAI;UACbC;QACF;MACF,CAAC;MAED9B,EAAE,CAACL,QAAQ,CAACoC,qBAAqB,CAACC,IAAI,CAACC,SAAS,CAACc,gBAAgB,CAAC,CAAC;MAEnE,MAAM5B,MAAM,CAACb,iBAAiB,CAAC6C,iBAAiB,CAAC,WAAW,CAAC,CAAC,CAC3DV,OAAO,CAACC,OAAO,CAAC3C,eAAe,CAAC;IACrC,CAAC,CAAC;IAEFmB,EAAE,CAAC,iDAAiD,EAAE,YAAY;MAChElB,EAAE,CAACL,QAAQ,CAACoC,qBAAqB,CAACC,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAErD,MAAMW,MAAM,GAAG,MAAMtC,iBAAiB,CAAC6C,iBAAiB,CAAC,WAAW,CAAC;MAErEhC,MAAM,CAACyB,MAAM,CAAC,CAACvB,IAAI,CAAC,IAAI,CAAC;IAC3B,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}