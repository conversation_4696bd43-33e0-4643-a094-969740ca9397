const Joi = require('joi');

// Common validation schemas
const idSchema = Joi.string().required();
const emailSchema = Joi.string().email().required();
const dateSchema = Joi.string().isoDate();
const statusSchema = Joi.string().valid('pending', 'in-progress', 'completed', 'rejected', 'withdrawn');
const prioritySchema = Joi.string().valid('low', 'medium', 'high', 'urgent');

// Validation schemas
const schemas = {
  // Data Processing Activity validation schemas
  createDataProcessingActivity: Joi.object({
    name: Joi.string().required().min(3).max(200),
    description: Joi.string().required().max(1000),
    purpose: Joi.string().required().max(500),
    dataCategories: Joi.array().items(Joi.string().max(100)).required(),
    dataSubjects: Joi.array().items(Joi.string().max(100)).required(),
    legalBasis: Joi.string().required().valid('consent', 'contract', 'legal-obligation', 'vital-interests', 'public-interest', 'legitimate-interests'),
    retentionPeriod: Joi.string().required().max(200),
    processingOperations: Joi.array().items(Joi.string().max(100)).required(),
    crossBorderTransfers: Joi.array().items(
      Joi.object({
        country: Joi.string().required().max(100),
        mechanism: Joi.string().required().max(100),
        adequacyDecision: Joi.boolean().required()
      })
    ).optional(),
    securityMeasures: Joi.array().items(Joi.string().max(200)).optional(),
    dataControllers: Joi.array().items(Joi.string().max(200)).optional(),
    dataProcessors: Joi.array().items(Joi.string().max(200)).optional(),
    dpia: Joi.object({
      required: Joi.boolean().required(),
      completed: Joi.boolean().required(),
      completionDate: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}$/).allow(null).optional(),
      reviewDate: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}$/).allow(null).optional()
    }).optional(),
    risks: Joi.array().items(
      Joi.object({
        description: Joi.string().required().max(500),
        likelihood: Joi.string().required().valid('low', 'medium', 'high'),
        impact: Joi.string().required().valid('low', 'medium', 'high'),
        mitigations: Joi.string().required().max(500)
      })
    ).optional(),
    status: Joi.string().required().valid('active', 'inactive', 'archived')
  }),

  updateDataProcessingActivity: Joi.object({
    name: Joi.string().min(3).max(200).optional(),
    description: Joi.string().max(1000).optional(),
    purpose: Joi.string().max(500).optional(),
    dataCategories: Joi.array().items(Joi.string().max(100)).optional(),
    dataSubjects: Joi.array().items(Joi.string().max(100)).optional(),
    legalBasis: Joi.string().valid('consent', 'contract', 'legal-obligation', 'vital-interests', 'public-interest', 'legitimate-interests').optional(),
    retentionPeriod: Joi.string().max(200).optional(),
    processingOperations: Joi.array().items(Joi.string().max(100)).optional(),
    crossBorderTransfers: Joi.array().items(
      Joi.object({
        country: Joi.string().required().max(100),
        mechanism: Joi.string().required().max(100),
        adequacyDecision: Joi.boolean().required()
      })
    ).optional(),
    securityMeasures: Joi.array().items(Joi.string().max(200)).optional(),
    dataControllers: Joi.array().items(Joi.string().max(200)).optional(),
    dataProcessors: Joi.array().items(Joi.string().max(200)).optional(),
    dpia: Joi.object({
      required: Joi.boolean().required(),
      completed: Joi.boolean().required(),
      completionDate: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}$/).allow(null).optional(),
      reviewDate: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}$/).allow(null).optional()
    }).optional(),
    risks: Joi.array().items(
      Joi.object({
        description: Joi.string().required().max(500),
        likelihood: Joi.string().required().valid('low', 'medium', 'high'),
        impact: Joi.string().required().valid('low', 'medium', 'high'),
        mitigations: Joi.string().required().max(500)
      })
    ).optional(),
    status: Joi.string().valid('active', 'inactive', 'archived').optional()
  }).min(1), // At least one field must be provided

  // Data Subject Request validation schemas
  createDataSubjectRequest: Joi.object({
    requestType: Joi.string().required().valid('access', 'rectification', 'erasure', 'restriction', 'portability', 'objection', 'automated-decision', 'withdraw-consent', 'other'),
    dataSubjectName: Joi.string().required().max(200),
    dataSubjectEmail: Joi.string().required().email().max(200),
    dataSubjectId: Joi.string().max(100).allow(null, '').optional(),
    identityVerified: Joi.boolean().optional(),
    verificationMethod: Joi.string().max(100).allow(null, '').optional(),
    verificationDate: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{1,3})?Z$/).allow(null, '').optional(),
    requestDetails: Joi.string().required().max(1000),
    status: Joi.string().valid('pending', 'in-progress', 'completed', 'rejected', 'withdrawn').optional(),
    assignedTo: Joi.string().max(100).allow(null, '').optional(),
    dueDate: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{1,3})?Z$/).allow(null, '').optional(),
    affectedSystems: Joi.array().items(Joi.string().max(100)).optional(),
    notes: Joi.string().max(1000).allow('').optional()
  }),

  updateDataSubjectRequest: Joi.object({
    requestType: Joi.string().valid('access', 'rectification', 'erasure', 'restriction', 'portability', 'objection', 'automated-decision', 'withdraw-consent', 'other').optional(),
    dataSubjectName: Joi.string().max(200).optional(),
    dataSubjectEmail: Joi.string().email().max(200).optional(),
    dataSubjectId: Joi.string().max(100).allow(null, '').optional(),
    identityVerified: Joi.boolean().optional(),
    verificationMethod: Joi.string().max(100).allow(null, '').optional(),
    verificationDate: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{1,3})?Z$/).allow(null, '').optional(),
    requestDetails: Joi.string().max(1000).optional(),
    status: Joi.string().valid('pending', 'in-progress', 'completed', 'rejected', 'withdrawn').optional(),
    assignedTo: Joi.string().max(100).allow(null, '').optional(),
    dueDate: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{1,3})?Z$/).allow(null, '').optional(),
    completionDate: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{1,3})?Z$/).allow(null, '').optional(),
    responseDetails: Joi.string().max(1000).allow(null, '').optional(),
    affectedSystems: Joi.array().items(Joi.string().max(100)).optional(),
    notes: Joi.string().max(1000).allow('').optional()
  }).min(1), // At least one field must be provided

  // Consent Record validation schemas
  createConsentRecord: Joi.object({
    dataSubjectId: Joi.string().max(100).allow(null, '').optional(),
    dataSubjectName: Joi.string().required().max(200),
    dataSubjectEmail: Joi.string().required().email().max(200),
    consentType: Joi.string().required().valid('marketing', 'analytics', 'profiling', 'third-party-sharing', 'cookies', 'research', 'other'),
    consentDescription: Joi.string().required().max(500),
    consentGiven: Joi.boolean().optional(),
    consentExpiryDate: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{1,3})?Z$/).allow(null, '').optional(),
    consentProof: Joi.string().max(1000).allow('').optional(),
    consentVersion: Joi.string().required().max(50),
    consentMethod: Joi.string().required().valid('online-form', 'paper-form', 'email', 'phone', 'in-person', 'api', 'other'),
    privacyNoticeVersion: Joi.string().required().max(50)
  }),

  updateConsentRecord: Joi.object({
    dataSubjectId: Joi.string().max(100).allow(null, '').optional(),
    dataSubjectName: Joi.string().max(200).optional(),
    dataSubjectEmail: Joi.string().email().max(200).optional(),
    consentType: Joi.string().valid('marketing', 'analytics', 'profiling', 'third-party-sharing', 'cookies', 'research', 'other').optional(),
    consentDescription: Joi.string().max(500).optional(),
    consentGiven: Joi.boolean().optional(),
    consentExpiryDate: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{1,3})?Z$/).allow(null, '').optional(),
    consentProof: Joi.string().max(1000).allow('').optional(),
    consentVersion: Joi.string().max(50).optional(),
    consentMethod: Joi.string().valid('online-form', 'paper-form', 'email', 'phone', 'in-person', 'api', 'other').optional(),
    privacyNoticeVersion: Joi.string().max(50).optional(),
    withdrawalDate: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{1,3})?Z$/).allow(null, '').optional(),
    withdrawalMethod: Joi.string().valid('online-form', 'email', 'phone', 'in-person', 'api', 'other').allow(null, '').optional(),
    status: Joi.string().valid('active', 'withdrawn', 'expired', 'declined').optional()
  }).min(1), // At least one field must be provided

  // Privacy Notice validation schemas
  createPrivacyNotice: Joi.object({
    title: Joi.string().required().min(3).max(200),
    version: Joi.string().required().max(50),
    effectiveDate: Joi.string().required().pattern(/^\d{4}-\d{2}-\d{2}$/),
    status: Joi.string().required().valid('draft', 'active', 'archived'),
    audience: Joi.string().required().valid('website-visitors', 'app-users', 'customers', 'employees', 'job-applicants', 'vendors', 'other'),
    language: Joi.string().required().max(10),
    format: Joi.string().required().valid('html', 'text', 'pdf', 'docx', 'other'),
    content: Joi.string().required().max(100000),
    contentUrl: Joi.string().uri().allow('').optional(),
    previousVersions: Joi.array().items(
      Joi.object({
        version: Joi.string().required().max(50),
        effectiveDate: Joi.string().required().pattern(/^\d{4}-\d{2}-\d{2}$/),
        retirementDate: Joi.string().required().pattern(/^\d{4}-\d{2}-\d{2}$/),
        contentUrl: Joi.string().uri().required()
      })
    ).optional(),
    reviewCycle: Joi.string().required().valid('quarterly', 'semi-annual', 'annual', 'biennial', 'as-needed'),
    nextReviewDate: Joi.string().required().pattern(/^\d{4}-\d{2}-\d{2}$/),
    approvedBy: Joi.string().required().max(100),
    approvalDate: Joi.string().required().pattern(/^\d{4}-\d{2}-\d{2}$/)
  }),

  updatePrivacyNotice: Joi.object({
    title: Joi.string().min(3).max(200).optional(),
    version: Joi.string().max(50).optional(),
    effectiveDate: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}$/).optional(),
    status: Joi.string().valid('draft', 'active', 'archived').optional(),
    audience: Joi.string().valid('website-visitors', 'app-users', 'customers', 'employees', 'job-applicants', 'vendors', 'other').optional(),
    language: Joi.string().max(10).optional(),
    format: Joi.string().valid('html', 'text', 'pdf', 'docx', 'other').optional(),
    content: Joi.string().max(100000).optional(),
    contentUrl: Joi.string().uri().allow('').optional(),
    previousVersions: Joi.array().items(
      Joi.object({
        version: Joi.string().required().max(50),
        effectiveDate: Joi.string().required().pattern(/^\d{4}-\d{2}-\d{2}$/),
        retirementDate: Joi.string().required().pattern(/^\d{4}-\d{2}-\d{2}$/),
        contentUrl: Joi.string().uri().required()
      })
    ).optional(),
    reviewCycle: Joi.string().valid('quarterly', 'semi-annual', 'annual', 'biennial', 'as-needed').optional(),
    nextReviewDate: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}$/).optional(),
    approvedBy: Joi.string().max(100).optional(),
    approvalDate: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}$/).optional()
  }).min(1), // At least one field must be provided

  // Data Breach validation schemas
  createDataBreach: Joi.object({
    title: Joi.string().required().min(3).max(200),
    description: Joi.string().required().max(1000),
    breachType: Joi.string().required().valid('unauthorized-access', 'unauthorized-disclosure', 'lost-device', 'stolen-device', 'hacking', 'malware', 'phishing', 'insider-threat', 'physical-breach', 'other'),
    detectionDate: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{1,3})?Z$/).optional(),
    occurrenceDate: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{1,3})?Z$/).required(),
    affectedDataCategories: Joi.array().items(Joi.string().max(100)).optional(),
    affectedDataSubjects: Joi.array().items(Joi.string().max(100)).optional(),
    approximateSubjectsCount: Joi.number().integer().min(0).optional(),
    potentialImpact: Joi.string().required().valid('low', 'medium', 'high', 'critical'),
    containmentStatus: Joi.string().valid('not-contained', 'partially-contained', 'contained').optional(),
    containmentDate: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{1,3})?Z$/).allow(null).optional(),
    containmentMeasures: Joi.string().max(1000).allow('').optional(),
    rootCause: Joi.string().max(1000).allow('').optional(),
    remedialActions: Joi.array().items(Joi.string().max(500)).optional(),
    notificationStatus: Joi.object({
      authorities: Joi.object({
        required: Joi.boolean().required(),
        completed: Joi.boolean().required(),
        date: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{1,3})?Z$/).allow(null).optional(),
        recipient: Joi.string().max(200).allow(null).optional()
      }).required(),
      dataSubjects: Joi.object({
        required: Joi.boolean().required(),
        completed: Joi.boolean().required(),
        date: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{1,3})?Z$/).allow(null).optional(),
        method: Joi.string().max(100).allow(null).optional()
      }).required()
    }).optional(),
    investigationStatus: Joi.string().valid('pending', 'in-progress', 'completed').optional(),
    investigationReport: Joi.string().max(5000).allow('').optional(),
    status: Joi.string().valid('open', 'closed').optional()
  }),

  updateDataBreach: Joi.object({
    title: Joi.string().min(3).max(200).optional(),
    description: Joi.string().max(1000).optional(),
    breachType: Joi.string().valid('unauthorized-access', 'unauthorized-disclosure', 'lost-device', 'stolen-device', 'hacking', 'malware', 'phishing', 'insider-threat', 'physical-breach', 'other').optional(),
    detectionDate: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{1,3})?Z$/).optional(),
    occurrenceDate: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{1,3})?Z$/).optional(),
    affectedDataCategories: Joi.array().items(Joi.string().max(100)).optional(),
    affectedDataSubjects: Joi.array().items(Joi.string().max(100)).optional(),
    approximateSubjectsCount: Joi.number().integer().min(0).optional(),
    potentialImpact: Joi.string().valid('low', 'medium', 'high', 'critical').optional(),
    containmentStatus: Joi.string().valid('not-contained', 'partially-contained', 'contained').optional(),
    containmentDate: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{1,3})?Z$/).allow(null).optional(),
    containmentMeasures: Joi.string().max(1000).allow('').optional(),
    rootCause: Joi.string().max(1000).allow('').optional(),
    remedialActions: Joi.array().items(Joi.string().max(500)).optional(),
    notificationStatus: Joi.object({
      authorities: Joi.object({
        required: Joi.boolean().required(),
        completed: Joi.boolean().required(),
        date: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{1,3})?Z$/).allow(null).optional(),
        recipient: Joi.string().max(200).allow(null).optional()
      }).required(),
      dataSubjects: Joi.object({
        required: Joi.boolean().required(),
        completed: Joi.boolean().required(),
        date: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{1,3})?Z$/).allow(null).optional(),
        method: Joi.string().max(100).allow(null).optional()
      }).required()
    }).optional(),
    investigationStatus: Joi.string().valid('pending', 'in-progress', 'completed').optional(),
    investigationReport: Joi.string().max(5000).allow('').optional(),
    status: Joi.string().valid('open', 'closed').optional()
  }).min(1), // At least one field must be provided

  // Authentication validation schema
  auth: Joi.object({
    username: Joi.string().required(),
    password: Joi.string().required()
  }),

  // Notification validation schema
  createNotification: Joi.object({
    type: Joi.string().valid(
      'dsr-received',
      'dsr-due-soon',
      'dsr-overdue',
      'dsr-completed',
      'consent-withdrawn',
      'consent-expired',
      'data-breach-reported',
      'data-breach-notification-due',
      'dpia-required',
      'regulatory-change',
      'compliance-issue'
    ).required(),
    title: Joi.string().required().max(200),
    message: Joi.string().required().max(1000),
    priority: prioritySchema.default('medium'),
    recipients: Joi.array().items(Joi.string().max(100)).required(),
    channels: Joi.array().items(Joi.string().valid(
      'email',
      'sms',
      'push',
      'in-app',
      'webhook'
    )).default(['in-app']),
    relatedEntityType: Joi.string().max(100).optional(),
    relatedEntityId: Joi.string().max(100).optional(),
    metadata: Joi.object().optional()
  }),

  // Report validation schema
  report: Joi.object({
    reportType: Joi.string().valid(
      'dsr-summary',
      'consent-management',
      'data-breach',
      'processing-activities',
      'compliance-status'
    ).required(),
    period: Joi.string().valid(
      'last-7-days',
      'last-30-days',
      'last-90-days',
      'last-12-months',
      'year-to-date',
      'custom'
    ).required(),
    startDate: Joi.when('period', {
      is: 'custom',
      then: dateSchema.required(),
      otherwise: dateSchema
    }),
    endDate: Joi.when('period', {
      is: 'custom',
      then: dateSchema.required(),
      otherwise: dateSchema
    }),
    groupBy: Joi.string().max(100).optional()
  })
};

/**
 * Middleware to validate request body against a schema
 * @param {string} schemaName - Name of the schema to validate against
 * @returns {function} Express middleware function
 */
const validateRequest = (schemaName) => {
  return (req, res, next) => {
    const schema = schemas[schemaName];

    if (!schema) {
      return res.status(500).json({
        error: 'Internal Server Error',
        message: `Schema '${schemaName}' not found`
      });
    }

    const { error } = schema.validate(req.body, { abortEarly: false });

    if (error) {
      const errorMessage = error.details.map(detail => detail.message).join(', ');
      return res.status(400).json({
        error: 'Bad Request',
        message: errorMessage
      });
    }

    next();
  };
};

/**
 * Middleware to validate request query parameters against a schema
 * @param {Object} schema - Joi schema
 * @returns {function} Express middleware function
 */
const validateQuery = (schema) => {
  return (req, res, next) => {
    const { error } = schema.validate(req.query, { abortEarly: false });

    if (error) {
      const errorMessage = error.details.map(detail => detail.message).join(', ');
      return res.status(400).json({
        error: 'Bad Request',
        message: errorMessage
      });
    }

    next();
  };
};

/**
 * Middleware to validate request parameters against a schema
 * @param {Object} schema - Joi schema
 * @returns {function} Express middleware function
 */
const validateParams = (schema) => {
  return (req, res, next) => {
    const { error } = schema.validate(req.params, { abortEarly: false });

    if (error) {
      const errorMessage = error.details.map(detail => detail.message).join(', ');
      return res.status(400).json({
        error: 'Bad Request',
        message: errorMessage
      });
    }

    next();
  };
};

module.exports = {
  schemas,
  validateRequest,
  validateQuery,
  validateParams,
  idSchema,
  emailSchema,
  dateSchema,
  statusSchema,
  prioritySchema
};
