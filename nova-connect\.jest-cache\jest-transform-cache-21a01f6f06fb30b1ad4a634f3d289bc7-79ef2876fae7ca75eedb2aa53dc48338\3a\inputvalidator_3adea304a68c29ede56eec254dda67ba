0354c7534a6a799d9068a7f734eb311f
/**
 * NovaFuse Universal API Connector Input Validator
 * 
 * This module provides input validation utilities for the UAC.
 */

/**
 * Input Validation Utility
 * 
 * Provides methods for validating user input to prevent injection attacks.
 */
class InputValidator {
  /**
   * Validate a string against SQL injection
   * @param {string} input - Input to validate
   * @returns {boolean} - Whether the input is safe
   */
  static isSqlSafe(input) {
    if (typeof input !== 'string') {
      return true;
    }

    // Check for common SQL injection patterns
    const sqlPatterns = [/(\s|^)(SELECT|INSERT|UPDATE|DELETE|DROP|ALTER|EXEC|UNION|CREATE|WHERE)(\s|$)/i, /(\s|^)(OR|AND)(\s+)(['"]?\d+['"]?\s*=\s*['"]?\d+['"]?)/i, /--/, /;.*/, /\/\*.+\*\//];
    return !sqlPatterns.some(pattern => pattern.test(input));
  }

  /**
   * Validate a string against XSS
   * @param {string} input - Input to validate
   * @returns {boolean} - Whether the input is safe
   */
  static isXssSafe(input) {
    if (typeof input !== 'string') {
      return true;
    }
    return !/<script|javascript:|on\w+\s*=|data:|vbscript:|<iframe/i.test(input);
  }

  /**
   * Validate a string against command injection
   * @param {string} input - Input to validate
   * @returns {boolean} - Whether the input is safe
   */
  static isCommandSafe(input) {
    if (typeof input !== 'string') {
      return true;
    }

    // Check for common command injection patterns
    const commandPatterns = [/(\s|^)(bash|sh|cmd|powershell|exec|eval)(\s|$)/i, /[&|;`]/, /\$\(.+\)/, />\s*[a-zA-Z0-9]/, /<\s*[a-zA-Z0-9]/];
    return !commandPatterns.some(pattern => pattern.test(input));
  }

  /**
   * Sanitize a string for safe use in HTML
   * @param {string} input - Input to sanitize
   * @returns {string} - Sanitized input
   */
  static sanitizeHtml(input) {
    if (typeof input !== 'string') {
      return input;
    }
    return input.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/"/g, '&quot;').replace(/'/g, '&#x27;').replace(/\//g, '&#x2F;');
  }

  /**
   * Sanitize a string for safe use in SQL
   * @param {string} input - Input to sanitize
   * @returns {string} - Sanitized input
   */
  static sanitizeSql(input) {
    if (typeof input !== 'string') {
      return input;
    }
    return input.replace(/'/g, "''").replace(/\\/g, '\\\\').replace(/\0/g, '\\0').replace(/\n/g, '\\n').replace(/\r/g, '\\r').replace(/\t/g, '\\t').replace(/\x1a/g, '\\Z');
  }

  /**
   * Sanitize a string for safe use in shell commands
   * @param {string} input - Input to sanitize
   * @returns {string} - Sanitized input
   */
  static sanitizeCommand(input) {
    if (typeof input !== 'string') {
      return input;
    }
    return input.replace(/[&;|`$><!\\]/g, '').replace(/\r/g, '').replace(/\n/g, '');
  }

  /**
   * Validate an object against a schema
   * @param {Object} input - Input to validate
   * @param {Object} schema - Schema to validate against
   * @returns {Object} - Validation result with isValid and errors properties
   */
  static validateObject(input, schema) {
    const errors = [];
    const result = {
      isValid: true,
      errors
    };
    if (!input || typeof input !== 'object') {
      result.isValid = false;
      errors.push('Input must be an object');
      return result;
    }
    if (!schema || typeof schema !== 'object') {
      result.isValid = false;
      errors.push('Schema must be an object');
      return result;
    }

    // Check required fields
    if (schema.required && Array.isArray(schema.required)) {
      for (const field of schema.required) {
        if (input[field] === undefined) {
          result.isValid = false;
          errors.push(`Required field '${field}' is missing`);
        }
      }
    }

    // Check field types and constraints
    if (schema.properties && typeof schema.properties === 'object') {
      for (const [field, fieldSchema] of Object.entries(schema.properties)) {
        if (input[field] !== undefined) {
          // Check type
          if (fieldSchema.type) {
            const type = Array.isArray(input[field]) ? 'array' : typeof input[field];
            if (type !== fieldSchema.type) {
              result.isValid = false;
              errors.push(`Field '${field}' must be of type '${fieldSchema.type}'`);
            }
          }

          // Check enum
          if (fieldSchema.enum && Array.isArray(fieldSchema.enum)) {
            if (!fieldSchema.enum.includes(input[field])) {
              result.isValid = false;
              errors.push(`Field '${field}' must be one of: ${fieldSchema.enum.join(', ')}`);
            }
          }

          // Check pattern
          if (fieldSchema.pattern && typeof input[field] === 'string') {
            const pattern = new RegExp(fieldSchema.pattern);
            if (!pattern.test(input[field])) {
              result.isValid = false;
              errors.push(`Field '${field}' does not match pattern '${fieldSchema.pattern}'`);
            }
          }

          // Check min/max for numbers
          if (typeof input[field] === 'number') {
            if (fieldSchema.minimum !== undefined && input[field] < fieldSchema.minimum) {
              result.isValid = false;
              errors.push(`Field '${field}' must be greater than or equal to ${fieldSchema.minimum}`);
            }
            if (fieldSchema.maximum !== undefined && input[field] > fieldSchema.maximum) {
              result.isValid = false;
              errors.push(`Field '${field}' must be less than or equal to ${fieldSchema.maximum}`);
            }
          }

          // Check minLength/maxLength for strings
          if (typeof input[field] === 'string') {
            if (fieldSchema.minLength !== undefined && input[field].length < fieldSchema.minLength) {
              result.isValid = false;
              errors.push(`Field '${field}' must be at least ${fieldSchema.minLength} characters long`);
            }
            if (fieldSchema.maxLength !== undefined && input[field].length > fieldSchema.maxLength) {
              result.isValid = false;
              errors.push(`Field '${field}' must be at most ${fieldSchema.maxLength} characters long`);
            }
          }

          // Check minItems/maxItems for arrays
          if (Array.isArray(input[field])) {
            if (fieldSchema.minItems !== undefined && input[field].length < fieldSchema.minItems) {
              result.isValid = false;
              errors.push(`Field '${field}' must have at least ${fieldSchema.minItems} items`);
            }
            if (fieldSchema.maxItems !== undefined && input[field].length > fieldSchema.maxItems) {
              result.isValid = false;
              errors.push(`Field '${field}' must have at most ${fieldSchema.maxItems} items`);
            }
          }
        }
      }
    }
    return result;
  }
}
module.exports = InputValidator;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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