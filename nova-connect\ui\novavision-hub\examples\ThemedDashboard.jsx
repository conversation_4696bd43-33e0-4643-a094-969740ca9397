/**
 * Themed Dashboard Example
 * 
 * This example demonstrates how to use the theme system to create a themed dashboard.
 */

import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import {
  DashboardCard,
  DataTable,
  GraphVisualization,
  MetricsCard,
  ChartCard,
  StatusIndicator,
  TabPanel,
  ResponsiveLayout,
  SkipLink,
  ThemeSelector
} from '../components';
import { ThemeProvider, useTheme } from '../theme';

/**
 * Themed Dashboard Content component
 * 
 * @param {Object} props - Component props
 * @param {Object} props.novaConnect - NovaConnect instance
 * @param {Object} props.novaShield - NovaShield instance
 * @param {Object} props.novaTrack - NovaTrack instance
 * @param {boolean} [props.enableLogging=false] - Whether to enable logging
 * @returns {React.ReactElement} Themed Dashboard Content component
 */
const ThemedDashboardContent = ({
  novaConnect,
  novaShield,
  novaTrack,
  enableLogging = false
}) => {
  const { theme, colorMode } = useTheme();
  const [loading, setLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState(null);
  const [activeTab, setActiveTab] = useState('overview');
  
  // Fetch dashboard data
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        if (enableLogging) {
          console.log('Fetching dashboard data...');
        }
        
        // Simulate API call to fetch dashboard data
        await new Promise(resolve => setTimeout(resolve, 1500));
        
        // Sample dashboard data
        const data = {
          complianceScore: 87,
          securityScore: 92,
          identityScore: 78,
          alerts: [
            { id: 'alert-001', severity: 'critical', type: 'compliance', message: 'GDPR compliance violation detected', timestamp: '2023-06-15T10:30:00Z', status: 'open' },
            { id: 'alert-002', severity: 'high', type: 'security', message: 'Unusual authentication pattern detected', timestamp: '2023-06-15T09:45:00Z', status: 'investigating' },
            { id: 'alert-003', severity: 'medium', type: 'identity', message: 'User access review pending', timestamp: '2023-06-14T16:20:00Z', status: 'open' }
          ],
          complianceChart: {
            labels: ['GDPR', 'HIPAA', 'PCI DSS', 'SOC 2', 'ISO 27001'],
            datasets: [
              {
                label: 'Compliance Score',
                data: [92, 85, 78, 90, 88],
                backgroundColor: 'rgba(54, 162, 235, 0.5)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 1
              }
            ]
          },
          securityChart: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            datasets: [
              {
                label: 'Threats Detected',
                data: [12, 19, 8, 15, 10, 7],
                backgroundColor: 'rgba(255, 99, 132, 0.5)',
                borderColor: 'rgba(255, 99, 132, 1)',
                borderWidth: 1
              },
              {
                label: 'Threats Remediated',
                data: [10, 15, 7, 12, 9, 5],
                backgroundColor: 'rgba(75, 192, 192, 0.5)',
                borderColor: 'rgba(75, 192, 192, 1)',
                borderWidth: 1
              }
            ]
          }
        };
        
        setDashboardData(data);
        setLoading(false);
        
        if (enableLogging) {
          console.log('Dashboard data fetched successfully');
        }
      } catch (error) {
        console.error('Error fetching dashboard data', error);
        setLoading(false);
      }
    };
    
    fetchDashboardData();
  }, [enableLogging]);
  
  // Refresh dashboard data
  const handleRefresh = async () => {
    setLoading(true);
    
    try {
      if (enableLogging) {
        console.log('Refreshing dashboard data...');
      }
      
      // Simulate API call to refresh dashboard data
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Update dashboard data with new values
      setDashboardData(prevData => ({
        ...prevData,
        complianceScore: Math.floor(Math.random() * 20) + 80,
        securityScore: Math.floor(Math.random() * 20) + 80,
        identityScore: Math.floor(Math.random() * 20) + 70
      }));
      
      setLoading(false);
      
      if (enableLogging) {
        console.log('Dashboard data refreshed successfully');
      }
    } catch (error) {
      console.error('Error refreshing dashboard data', error);
      setLoading(false);
    }
  };
  
  // Render status indicator for alert severity
  const renderAlertSeverity = (severity) => {
    const severityMap = {
      critical: { status: 'error', label: 'Critical', pulse: true },
      high: { status: 'error', label: 'High' },
      medium: { status: 'warning', label: 'Medium' },
      low: { status: 'info', label: 'Low' }
    };
    
    const { status, label, pulse } = severityMap[severity] || { status: 'neutral', label: severity };
    
    return (
      <StatusIndicator
        status={status}
        label={label}
        pulse={pulse}
      />
    );
  };
  
  // Define tabs for the dashboard
  const dashboardTabs = [
    {
      id: 'overview',
      label: 'Overview',
      content: (
        <div className="space-y-6">
          {/* Metrics Cards */}
          <ResponsiveLayout
            layouts={{
              xs: 1,
              sm: 1,
              md: 3,
              lg: 3,
              xl: 3
            }}
            gap={4}
          >
            <MetricsCard
              title="Compliance Score"
              metrics={[
                {
                  label: 'Overall Score',
                  value: dashboardData?.complianceScore || 0,
                  suffix: '%',
                  trend: 5,
                  trendDirection: 'up',
                  color: 'text-primary',
                  description: 'Compliance score across all frameworks'
                }
              ]}
              columns={1}
              loading={loading}
            />
            
            <MetricsCard
              title="Security Score"
              metrics={[
                {
                  label: 'Overall Score',
                  value: dashboardData?.securityScore || 0,
                  suffix: '%',
                  trend: 2,
                  trendDirection: 'up',
                  color: 'text-success',
                  description: 'Security score across all systems'
                }
              ]}
              columns={1}
              loading={loading}
            />
            
            <MetricsCard
              title="Identity Score"
              metrics={[
                {
                  label: 'Overall Score',
                  value: dashboardData?.identityScore || 0,
                  suffix: '%',
                  trend: -3,
                  trendDirection: 'down',
                  color: 'text-secondary',
                  description: 'Identity management score'
                }
              ]}
              columns={1}
              loading={loading}
            />
          </ResponsiveLayout>
          
          {/* Charts */}
          <ResponsiveLayout
            layouts={{
              xs: 1,
              sm: 1,
              md: 2,
              lg: 2,
              xl: 2
            }}
            gap={4}
          >
            <ChartCard
              title="Compliance by Framework"
              chartType="radar"
              data={dashboardData?.complianceChart || { labels: [], datasets: [] }}
              loading={loading}
              collapsible={true}
              onRefresh={handleRefresh}
            />
            
            <ChartCard
              title="Security Threats"
              chartType="bar"
              data={dashboardData?.securityChart || { labels: [], datasets: [] }}
              loading={loading}
              collapsible={true}
              onRefresh={handleRefresh}
            />
          </ResponsiveLayout>
        </div>
      )
    },
    {
      id: 'alerts',
      label: 'Alerts',
      content: (
        <DashboardCard
          title="Recent Alerts"
          collapsible={true}
          onRefresh={handleRefresh}
          loading={loading}
        >
          <DataTable
            columns={[
              { field: 'severity', header: 'Severity', render: (value) => renderAlertSeverity(value) },
              { field: 'type', header: 'Type', render: (value) => value.charAt(0).toUpperCase() + value.slice(1) },
              { field: 'message', header: 'Message' },
              { field: 'timestamp', header: 'Timestamp', render: (value) => new Date(value).toLocaleString() },
              { field: 'status', header: 'Status', render: (value) => (
                <StatusIndicator
                  status={value === 'open' ? 'error' : value === 'investigating' ? 'warning' : 'success'}
                  label={value.charAt(0).toUpperCase() + value.slice(1)}
                />
              )}
            ]}
            data={dashboardData?.alerts || []}
            loading={loading}
            emptyMessage="No alerts found"
          />
        </DashboardCard>
      )
    },
    {
      id: 'theme-info',
      label: 'Theme Info',
      content: (
        <DashboardCard
          title="Current Theme Information"
          collapsible={true}
          onRefresh={handleRefresh}
          loading={loading}
        >
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h3 className="text-lg font-medium mb-2">Theme Details</h3>
                <div className="bg-surface p-4 rounded-md">
                  <p><strong>Name:</strong> {theme.name}</p>
                  <p><strong>Color Mode:</strong> {colorMode}</p>
                  <p><strong>Font Family:</strong> {theme.typography?.fontFamily}</p>
                </div>
              </div>
              
              <div>
                <h3 className="text-lg font-medium mb-2">Theme Selector</h3>
                <div className="bg-surface p-4 rounded-md">
                  <p className="mb-2">Select a theme:</p>
                  <ThemeSelector variant="buttons" />
                </div>
              </div>
            </div>
            
            <div>
              <h3 className="text-lg font-medium mb-2">Color Palette</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                <div className="bg-primary text-primaryContrast p-2 rounded-md">Primary</div>
                <div className="bg-secondary text-secondaryContrast p-2 rounded-md">Secondary</div>
                <div className="bg-success text-successContrast p-2 rounded-md">Success</div>
                <div className="bg-warning text-warningContrast p-2 rounded-md">Warning</div>
                <div className="bg-error text-errorContrast p-2 rounded-md">Error</div>
                <div className="bg-info text-infoContrast p-2 rounded-md">Info</div>
                <div className="bg-background text-textPrimary p-2 rounded-md border border-divider">Background</div>
                <div className="bg-surface text-textPrimary p-2 rounded-md border border-divider">Surface</div>
              </div>
            </div>
            
            <div>
              <h3 className="text-lg font-medium mb-2">Typography</h3>
              <div className="bg-surface p-4 rounded-md">
                <p className="text-xs">Text Extra Small</p>
                <p className="text-sm">Text Small</p>
                <p className="text-md">Text Medium</p>
                <p className="text-lg">Text Large</p>
                <p className="text-xl">Text Extra Large</p>
                <p className="text-2xl">Text 2XL</p>
                <p className="text-3xl">Text 3XL</p>
              </div>
            </div>
          </div>
        </DashboardCard>
      )
    }
  ];
  
  return (
    <div className="space-y-6">
      {/* Skip link for keyboard navigation */}
      <SkipLink targetId="main-content" />
      
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-textPrimary" id="dashboard-title">
          Themed Dashboard
        </h1>
        
        <div className="flex items-center space-x-2">
          <ThemeSelector variant="dropdown" />
          
          <button
            className="px-4 py-2 bg-primary text-primaryContrast rounded-md hover:bg-primaryDark transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary"
            onClick={handleRefresh}
            disabled={loading}
          >
            {loading ? 'Refreshing...' : 'Refresh'}
          </button>
        </div>
      </div>
      
      {/* Main content */}
      <main id="main-content" tabIndex="-1">
        <TabPanel
          tabs={dashboardTabs}
          defaultTab="overview"
          variant="pills"
          onTabChange={setActiveTab}
        />
      </main>
    </div>
  );
};

ThemedDashboardContent.propTypes = {
  novaConnect: PropTypes.object,
  novaShield: PropTypes.object,
  novaTrack: PropTypes.object,
  enableLogging: PropTypes.bool
};

/**
 * Themed Dashboard component
 * 
 * @param {Object} props - Component props
 * @param {Object} props.novaConnect - NovaConnect instance
 * @param {Object} props.novaShield - NovaShield instance
 * @param {Object} props.novaTrack - NovaTrack instance
 * @param {Object} [props.initialTheme] - Initial theme
 * @param {boolean} [props.enableSystemPreference=true] - Whether to enable system preference detection
 * @param {boolean} [props.enableLogging=false] - Whether to enable logging
 * @returns {React.ReactElement} Themed Dashboard component
 */
const ThemedDashboard = ({
  novaConnect,
  novaShield,
  novaTrack,
  initialTheme,
  enableSystemPreference = true,
  enableLogging = false
}) => {
  return (
    <ThemeProvider theme={initialTheme} enableSystemPreference={enableSystemPreference}>
      <ThemedDashboardContent
        novaConnect={novaConnect}
        novaShield={novaShield}
        novaTrack={novaTrack}
        enableLogging={enableLogging}
      />
    </ThemeProvider>
  );
};

ThemedDashboard.propTypes = {
  novaConnect: PropTypes.object,
  novaShield: PropTypes.object,
  novaTrack: PropTypes.object,
  initialTheme: PropTypes.object,
  enableSystemPreference: PropTypes.bool,
  enableLogging: PropTypes.bool
};

export default ThemedDashboard;
