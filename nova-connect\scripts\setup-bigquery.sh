#!/bin/bash
# NovaConnect UAC BigQuery Setup Script
# This script sets up the BigQuery datasets and tables for NovaConnect UAC

# Set variables
PROJECT_ID=${PROJECT_ID:-"novafuse-marketplace"}
LOCATION=${LOCATION:-"us-central1"}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# Function to print section header
print_section() {
  echo -e "\n${YELLOW}==== $1 ====${NC}\n"
}

# Check if gcloud is installed
if ! command -v gcloud &> /dev/null; then
  echo -e "${RED}Error: gcloud is required but not installed.${NC}"
  exit 1
fi

# Check if bq is installed
if ! command -v bq &> /dev/null; then
  echo -e "${RED}Error: bq is required but not installed.${NC}"
  exit 1
fi

# Check if jq is installed
if ! command -v jq &> /dev/null; then
  echo -e "${RED}Error: jq is required but not installed.${NC}"
  exit 1
fi

# Check if user is logged in
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" &> /dev/null; then
  echo -e "${RED}Error: You are not logged in to gcloud. Please run 'gcloud auth login' first.${NC}"
  exit 1
fi

# Set the project
print_section "Setting project"
gcloud config set project ${PROJECT_ID}

# Create datasets
print_section "Creating datasets"

# Compliance dataset
echo -e "Creating compliance dataset..."
bq --location=${LOCATION} mk \
  --dataset \
  --description "NovaConnect UAC Compliance Data" \
  ${PROJECT_ID}:compliance

# Billing dataset
echo -e "Creating billing dataset..."
bq --location=${LOCATION} mk \
  --dataset \
  --description "NovaConnect UAC Billing Data" \
  ${PROJECT_ID}:billing

# Analytics dataset
echo -e "Creating analytics dataset..."
bq --location=${LOCATION} mk \
  --dataset \
  --description "NovaConnect UAC Analytics Data" \
  ${PROJECT_ID}:analytics

# Create tables
print_section "Creating tables"

# Compliance dataset tables
echo -e "Creating compliance.soc2_evidence table..."
bq mk \
  --table \
  --description "SOC2 evidence collected from Google Cloud Security Center" \
  ${PROJECT_ID}:compliance.soc2_evidence \
  finding_id:STRING,category:STRING,resource_name:STRING,severity:STRING,event_time:TIMESTAMP,source_properties:STRING,security_marks:STRING,collected_at:TIMESTAMP

# Billing dataset tables
echo -e "Creating billing.entitlements table..."
bq mk \
  --table \
  --description "Customer entitlements from GCP Marketplace" \
  ${PROJECT_ID}:billing.entitlements \
  customer_id:STRING,entitlement_id:STRING,plan:STRING,status:STRING,created_at:TIMESTAMP,updated_at:TIMESTAMP,event_type:STRING

echo -e "Creating billing.usage table..."
bq mk \
  --table \
  --description "Customer usage data" \
  ${PROJECT_ID}:billing.usage \
  customer_id:STRING,metric_name:STRING,quantity:INTEGER,timestamp:TIMESTAMP,date:DATE

# Analytics dataset tables
echo -e "Creating analytics.acquisition_metrics table..."
bq mk \
  --table \
  --description "Acquisition valuation metrics" \
  ${PROJECT_ID}:analytics.acquisition_metrics \
  valuation_date:DATE,total_customers:INTEGER,current_mrr:FLOAT,current_arr:FLOAT,average_daily_revenue:FLOAT,average_retention_rate:FLOAT,monthly_active_customers:INTEGER,weekly_active_customers:INTEGER,daily_active_customers:INTEGER,new_customers_this_month:INTEGER,current_annualized_revenue:FLOAT,conservative_valuation:FLOAT,target_valuation:FLOAT,optimistic_valuation:FLOAT,projected_arr_6_months:FLOAT,projected_arr_12_months:FLOAT,projected_arr_24_months:FLOAT,acquisition_readiness_score:INTEGER

# Create views
print_section "Creating views"

# Create acquisition metrics view
echo -e "Creating analytics.acquisition_metrics_view..."
bq mk \
  --view \
  --description "Acquisition metrics view" \
  --project_id=${PROJECT_ID} \
  --use_legacy_sql=false \
  ${PROJECT_ID}:analytics.acquisition_metrics_view \
  "$(cat ../analytics/acquisition_metrics.sql | sed "s/\${PROJECT_ID}/${PROJECT_ID}/g")"

print_section "BigQuery setup complete"
echo -e "${GREEN}✓ BigQuery datasets and tables created successfully.${NC}"
echo -e "${GREEN}✓ You can now use the acquisition dashboard in Looker Studio.${NC}"

exit 0
