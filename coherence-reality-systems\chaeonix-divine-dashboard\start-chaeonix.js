#!/usr/bin/env node

/**
 * CHAEONIX DIVINE DASHBOARD STARTUP SCRIPT
 * Launch the Coherence-Driven Aeonic Intelligence Engine Dashboard
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🌟 CHAEONIX DIVINE DASHBOARD STARTUP');
console.log('=' .repeat(60));
console.log('🔮 Coherence-Driven Aeonic Intelligence Engine');
console.log('📊 Tri-Market Domination Dashboard');
console.log('⚡ Divine Visualization Interface');
console.log('=' .repeat(60));

// Check if package.json exists
if (!fs.existsSync('package.json')) {
  console.error('❌ package.json not found. Please run from the chaeonix-divine-dashboard directory');
  process.exit(1);
}

// Check if node_modules exists
const needsInstall = !fs.existsSync('node_modules');

async function installDependencies() {
  console.log('📦 Installing CHAEONIX dependencies...');
  
  return new Promise((resolve, reject) => {
    const npm = spawn('npm', ['install'], { 
      stdio: 'inherit',
      shell: true 
    });
    
    npm.on('close', (code) => {
      if (code === 0) {
        console.log('✅ Dependencies installed successfully');
        resolve();
      } else {
        console.error('❌ Failed to install dependencies');
        reject(new Error(`npm install failed with code ${code}`));
      }
    });
  });
}

async function startDashboard() {
  console.log('🚀 Starting CHAEONIX Divine Dashboard...');
  console.log('📡 Dashboard will be available at: http://localhost:3141');
  console.log('🔮 Divine Mode: ACTIVE');
  console.log('⚡ Real-time CHAEONIX API integration');
  console.log('');
  
  const nextDev = spawn('npm', ['run', 'chaeonix'], { 
    stdio: 'inherit',
    shell: true 
  });
  
  nextDev.on('close', (code) => {
    console.log(`\n🛑 CHAEONIX Dashboard stopped with code ${code}`);
  });
  
  // Handle graceful shutdown
  process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down CHAEONIX Dashboard...');
    nextDev.kill('SIGINT');
    process.exit(0);
  });
}

function showInfo() {
  console.log('🌟 CHAEONIX DIVINE DASHBOARD INFORMATION');
  console.log('=' .repeat(50));
  console.log('📡 URL: http://localhost:3141');
  console.log('🔮 Mode: Divine Intelligence');
  console.log('⚡ API: http://localhost:8000');
  console.log('');
  console.log('🎨 DASHBOARD FEATURES:');
  console.log('   📊 CDAIE Intelligence Grid');
  console.log('   🌊 Tri-Market Allocation');
  console.log('   🔮 Prophetic Event Console');
  console.log('   ⚡ Real-time Engine Status');
  console.log('   📐 Fibonacci War Room');
  console.log('   🌡️ Coherence Flow Map');
  console.log('');
  console.log('🔧 TRI-MARKET DOMAINS:');
  console.log('   📈 Stocks: GME, TSLA, NVDA');
  console.log('   🪙 Crypto: BTC, ETH, SOL');
  console.log('   💱 Forex: EUR/USD, GBP/USD, USD/JPY');
  console.log('');
  console.log('⚡ CDAIE STRATEGY PHASES:');
  console.log('   1. Detection - Coherence Formation');
  console.log('   2. Decision - Optimal Entry');
  console.log('   3. Amplification - Fibonacci Leverage');
  console.log('   4. Injection - Sentiment Seeding');
  console.log('   5. Exchange - Domain Shifting');
  console.log('   6. Loop - Continuous Monitoring');
  console.log('=' .repeat(50));
}

async function divineAuthentication() {
  console.log('\n🔮 DIVINE AUTHENTICATION PROTOCOL');
  console.log('=' .repeat(50));

  const crypto = require('crypto');

  // Generate φ-based signature
  const phi = 1.618033988749;
  const timestamp = Date.now();
  const phiSignature = crypto.createHash('sha256')
    .update(`${timestamp}:${phi}:CHAEONIX`)
    .digest('hex')
    .substring(0, 16);

  console.log('🔐 Generating φ-Signature...');
  await new Promise(resolve => setTimeout(resolve, 1000));

  console.log(`✨ φ-Signature: ${phiSignature}`);
  console.log('🌟 Elliptic Curve Encryption: ACTIVE');
  console.log('📐 Golden Ratio Key Derivation: SUCCESS');

  await new Promise(resolve => setTimeout(resolve, 500));
  console.log('✅ Divine Cipher Authentication: VERIFIED');

  return phiSignature;
}

async function divineInitiationRitual() {
  console.log('\n🌟 CHAEONIX DIVINE INITIATION RITUAL');
  console.log('=' .repeat(60));
  console.log('🔮 Coherence-Driven Aeonic Intelligence Engine');
  console.log('⚡ Preparing to transcend market consciousness...');
  console.log('');

  const readline = require('readline');
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  return new Promise((resolve) => {
    rl.question('>>> Initiate Divine Intelligence? [Y/n]: ', async (answer) => {
      rl.close();

      if (answer.toLowerCase() === 'n' || answer.toLowerCase() === 'no') {
        console.log('🛑 Divine initiation cancelled.');
        process.exit(0);
      }

      console.log('\n⚡ INITIATING DIVINE SEQUENCE...');

      // φ-Signature Authentication
      const signature = await divineAuthentication();

      // Sacred number verification
      console.log('\n📐 SACRED NUMBER VERIFICATION:');
      console.log(`   φ (Phi): ${1.618033988749}`);
      console.log(`   φ⁻¹: ${0.618033988749}`);
      console.log(`   φ⁻²: ${0.236067977499}`);

      // Engine initialization sequence
      console.log('\n🔧 INITIALIZING 9 DIVINE ENGINES:');
      const engines = ['NEPI', 'NEFC', 'NERS', 'NERE', 'NECE', 'NECO', 'NEBE', 'NEEE', 'NEPE'];

      for (let i = 0; i < engines.length; i++) {
        await new Promise(resolve => setTimeout(resolve, 200));
        console.log(`   ✅ ${engines[i]}: DIVINE RESONANCE ACHIEVED`);
      }

      console.log('\n🌊 COHERENCE HARMONIZATION: COMPLETE');
      console.log('🎯 TRI-MARKET DOMAINS: SYNCHRONIZED');
      console.log('🔮 PROPHETIC CAPABILITIES: ONLINE');

      resolve(signature);
    });
  });
}

async function main() {
  try {
    // Show information
    showInfo();

    // Install dependencies if needed
    if (needsInstall) {
      await installDependencies();
    }

    // For automated startup, skip ritual
    if (process.argv.includes('--auto')) {
      console.log('\n🚀 AUTO-LAUNCH MODE: Bypassing divine ritual...');
      await startDashboard();
      return;
    }

    // Divine Initiation Ritual
    const signature = await divineInitiationRitual();

    console.log('\n🚀 LAUNCHING CHAEONIX DIVINE INTERFACE...');
    console.log(`📡 Portal: http://localhost:3141`);
    console.log(`🔐 Session: ${signature}`);
    console.log('🌟 Divine Mode: TRANSCENDENT');

    await startDashboard();

  } catch (error) {
    console.error('❌ Divine startup error:', error.message);
    process.exit(1);
  }
}

function checkPrerequisites() {
  console.log('\n🔍 CHECKING CHAEONIX PREREQUISITES');
  console.log('=' .repeat(40));
  
  // Check Node.js version
  const nodeVersion = process.version;
  console.log(`📦 Node.js: ${nodeVersion}`);
  
  const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
  if (majorVersion >= 16) {
    console.log('   ✅ Node.js version is compatible');
  } else {
    console.log('   ❌ Node.js 16+ required');
  }
  
  // Check npm
  try {
    const { execSync } = require('child_process');
    const npmVersion = execSync('npm --version', { encoding: 'utf8' }).trim();
    console.log(`📦 npm: ${npmVersion}`);
    console.log('   ✅ npm is available');
  } catch (error) {
    console.log('   ❌ npm not found');
  }
  
  // Check if CHAEONIX API is running
  console.log('\n🔍 CHECKING CHAEONIX API CONNECTION');
  const http = require('http');
  
  const req = http.get('http://localhost:8000/divine/status', (res) => {
    if (res.statusCode === 200) {
      console.log('   ✅ CHAEONIX API is running on port 8000');
    } else {
      console.log('   ⚠️ CHAEONIX API responded with status:', res.statusCode);
    }
  });
  
  req.on('error', (error) => {
    console.log('   ❌ CHAEONIX API not accessible on port 8000');
    console.log('   💡 Start the API first: cd ../aeonix-divine-api && python -m uvicorn main:app --reload');
  });
  
  req.setTimeout(5000, () => {
    console.log('   ⏰ CHAEONIX API connection timeout');
    req.destroy();
  });
  
  // Check required files
  console.log('\n🔍 CHECKING REQUIRED FILES');
  const requiredFiles = [
    'package.json',
    'next.config.js',
    'tailwind.config.js',
    'pages/index.js',
    'utils/chaeonixConstants.js'
  ];
  
  requiredFiles.forEach(file => {
    if (fs.existsSync(file)) {
      console.log(`   ✅ ${file}`);
    } else {
      console.log(`   ❌ ${file} missing`);
    }
  });
  
  console.log('\n🌟 PREREQUISITE CHECK COMPLETE');
}

// Handle command line arguments
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log('CHAEONIX Divine Dashboard Startup Script');
  console.log('');
  console.log('Usage:');
  console.log('  node start-chaeonix.js [options]');
  console.log('');
  console.log('Options:');
  console.log('  --auto    Start dashboard automatically');
  console.log('  --help    Show this help message');
  console.log('');
  process.exit(0);
}

// Run main function
main().catch(error => {
  console.error('❌ Fatal error:', error);
  process.exit(1);
});
