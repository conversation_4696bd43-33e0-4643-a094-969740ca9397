/**
 * NovaCore Vendor Model
 * 
 * This model defines the schema for vendors in the SaaS vendor assessment module.
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

// Define contact schema
const contactSchema = new Schema({
  name: { 
    type: String, 
    required: true, 
    trim: true 
  },
  title: { 
    type: String, 
    trim: true 
  },
  email: { 
    type: String, 
    required: true, 
    trim: true, 
    lowercase: true 
  },
  phone: { 
    type: String, 
    trim: true 
  },
  isPrimary: { 
    type: Boolean, 
    default: false 
  },
  department: { 
    type: String, 
    trim: true 
  },
  notes: { 
    type: String, 
    trim: true 
  }
}, { _id: false });

// Define compliance status schema
const complianceStatusSchema = new Schema({
  framework: { 
    type: String, 
    required: true, 
    trim: true 
  },
  status: { 
    type: String, 
    enum: ['compliant', 'non_compliant', 'in_progress', 'not_applicable', 'unknown'], 
    default: 'unknown' 
  },
  verificationMethod: { 
    type: String, 
    enum: ['self_attestation', 'third_party_audit', 'certification', 'documentation_review', 'none'], 
    default: 'none' 
  },
  lastVerifiedDate: { 
    type: Date 
  },
  expirationDate: { 
    type: Date 
  },
  documentIds: [{ 
    type: Schema.Types.ObjectId, 
    ref: 'Document' 
  }],
  notes: { 
    type: String, 
    trim: true 
  }
}, { _id: false });

// Define risk score schema
const riskScoreSchema = new Schema({
  overall: { 
    type: Number, 
    min: 0, 
    max: 100, 
    default: 50 
  },
  security: { 
    type: Number, 
    min: 0, 
    max: 100 
  },
  privacy: { 
    type: Number, 
    min: 0, 
    max: 100 
  },
  compliance: { 
    type: Number, 
    min: 0, 
    max: 100 
  },
  businessContinuity: { 
    type: Number, 
    min: 0, 
    max: 100 
  },
  lastUpdated: { 
    type: Date, 
    default: Date.now 
  },
  calculationMethod: { 
    type: String, 
    enum: ['automatic', 'manual', 'hybrid'], 
    default: 'automatic' 
  },
  factors: { 
    type: Map, 
    of: Number 
  },
  notes: { 
    type: String, 
    trim: true 
  }
}, { _id: false });

// Define vendor schema
const vendorSchema = new Schema({
  name: { 
    type: String, 
    required: true, 
    trim: true 
  },
  description: { 
    type: String, 
    trim: true 
  },
  organizationId: { 
    type: Schema.Types.ObjectId, 
    ref: 'Organization', 
    required: true 
  },
  website: { 
    type: String, 
    trim: true 
  },
  industry: { 
    type: String, 
    trim: true 
  },
  status: { 
    type: String, 
    enum: ['active', 'inactive', 'pending', 'rejected'], 
    default: 'pending' 
  },
  tier: { 
    type: String, 
    enum: ['critical', 'high', 'medium', 'low'], 
    default: 'medium' 
  },
  contacts: [contactSchema],
  services: [{
    name: { 
      type: String, 
      required: true, 
      trim: true 
    },
    description: { 
      type: String, 
      trim: true 
    },
    category: { 
      type: String, 
      trim: true 
    },
    dataClassification: { 
      type: String, 
      enum: ['public', 'internal', 'confidential', 'restricted'], 
      default: 'internal' 
    },
    criticality: { 
      type: String, 
      enum: ['critical', 'high', 'medium', 'low'], 
      default: 'medium' 
    }
  }],
  complianceStatus: [complianceStatusSchema],
  riskScore: { 
    type: riskScoreSchema, 
    default: () => ({}) 
  },
  tags: [{ 
    type: String, 
    trim: true 
  }],
  cyberSafetyCertified: { 
    type: Boolean, 
    default: false 
  },
  cyberSafetyScore: { 
    type: Number, 
    min: 0, 
    max: 100 
  },
  cyberSafetyCertificationDate: { 
    type: Date 
  },
  cyberSafetyCertificationExpiration: { 
    type: Date 
  },
  createdBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'User' 
  },
  updatedBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'User' 
  }
}, {
  timestamps: true,
  versionKey: true
});

// Add indexes
vendorSchema.index({ name: 1, organizationId: 1 }, { unique: true });
vendorSchema.index({ status: 1 });
vendorSchema.index({ tier: 1 });
vendorSchema.index({ 'riskScore.overall': 1 });
vendorSchema.index({ cyberSafetyCertified: 1 });
vendorSchema.index({ cyberSafetyScore: 1 });
vendorSchema.index({ tags: 1 });
vendorSchema.index({ createdAt: 1 });

// Add methods
vendorSchema.methods.isActive = function() {
  return this.status === 'active';
};

vendorSchema.methods.isCritical = function() {
  return this.tier === 'critical';
};

vendorSchema.methods.getPrimaryContact = function() {
  return this.contacts.find(contact => contact.isPrimary);
};

vendorSchema.methods.getComplianceStatus = function(framework) {
  return this.complianceStatus.find(status => status.framework === framework);
};

vendorSchema.methods.isCyberSafetyCertified = function() {
  return this.cyberSafetyCertified && 
    this.cyberSafetyCertificationExpiration && 
    new Date() < this.cyberSafetyCertificationExpiration;
};

// Add statics
vendorSchema.statics.findByOrganization = function(organizationId) {
  return this.find({ organizationId });
};

vendorSchema.statics.findActive = function(organizationId) {
  return this.find({ 
    organizationId, 
    status: 'active' 
  });
};

vendorSchema.statics.findCritical = function(organizationId) {
  return this.find({ 
    organizationId, 
    tier: 'critical' 
  });
};

vendorSchema.statics.findCyberSafetyCertified = function(organizationId) {
  return this.find({ 
    organizationId, 
    cyberSafetyCertified: true,
    cyberSafetyCertificationExpiration: { $gt: new Date() }
  });
};

// Create model
const Vendor = mongoose.model('Vendor', vendorSchema);

module.exports = Vendor;
