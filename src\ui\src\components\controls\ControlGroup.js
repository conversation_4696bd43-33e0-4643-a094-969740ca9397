import React from 'react';
import { Box, Card, CardContent, Typography, Divider } from '@mui/material';

/**
 * ControlGroup component
 * 
 * Renders a group of controls with a title
 */
function ControlGroup({ title, children, sx = {} }) {
  return (
    <Card
      sx={{
        mb: 3,
        ...sx
      }}
    >
      <CardContent>
        <Typography variant="h6" gutterBottom>
          {title}
        </Typography>
        <Divider sx={{ mb: 2 }} />
        <Box>
          {children}
        </Box>
      </CardContent>
    </Card>
  );
}

export default ControlGroup;
