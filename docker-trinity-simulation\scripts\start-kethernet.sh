#!/bin/bash

# KetherNet Blockchain Startup Script
# Crown Consensus with Consciousness Validation
#
# Author: <PERSON>, NovaFuse Technologies
# Date: Trinity Docker Simulation

set -e

echo "🔥 STARTING KETHERNET BLOCKCHAIN - CROWN CONSENSUS"
echo "=================================================="
echo "Node Environment: $NODE_ENV"
echo "Consciousness Threshold: $CONSCIOUSNESS_THRESHOLD"
echo "Crown Consensus: $CROWN_CONSENSUS_ENABLED"
echo "Coherium Enabled: $COHERIUM_ENABLED"
echo "Aetherium Enabled: $AETHERIUM_ENABLED"
echo "=================================================="

# Wait for database
echo "⏳ Waiting for PostgreSQL database..."
until pg_isready -h trinity-postgres -p 5432 -U trinity_user; do
    echo "Database not ready, waiting..."
    sleep 2
done
echo "✅ Database connection established"

# Wait for Redis
echo "⏳ Waiting for Redis cache..."
until redis-cli -h trinity-redis ping; do
    echo "Redis not ready, waiting..."
    sleep 2
done
echo "✅ Redis connection established"

# Initialize database if needed
echo "🔧 Initializing KetherNet database..."
node -e "
const { KetherNetBlockchain } = require('./src/index');

async function initializeDatabase() {
    try {
        console.log('Initializing KetherNet blockchain...');
        
        const kethernet = new KetherNetBlockchain({
            enableConsciousnessValidation: true,
            consciousnessThreshold: parseInt(process.env.CONSCIOUSNESS_THRESHOLD),
            enableCoherium: process.env.COHERIUM_ENABLED === 'true',
            enableAetherium: process.env.AETHERIUM_ENABLED === 'true',
            enableLogging: true,
            enableMetrics: true
        });
        
        await kethernet.initialize();
        console.log('✅ KetherNet blockchain initialized successfully');
        
        // Create genesis block if needed
        const genesisTransaction = {
            type: 'GENESIS_BLOCK',
            data: {
                message: 'Trinity of Trust Genesis - Consciousness Validation Begins',
                timestamp: Date.now(),
                version: '1.0.0-TRINITY'
            },
            consciousnessData: {
                neural: 1.0,
                information: 1.0,
                coherence: 1.0
            },
            entityType: 'system',
            requiresConsensus: true
        };
        
        const result = await kethernet.submitTransaction(genesisTransaction);
        console.log('✅ Genesis block created:', result.transactionId);
        
        process.exit(0);
    } catch (error) {
        console.error('❌ Database initialization failed:', error.message);
        process.exit(1);
    }
}

initializeDatabase();
"

echo "✅ KetherNet database initialized"

# Start the KetherNet blockchain service
echo "🚀 Starting KetherNet Crown Consensus service..."

# Create startup script for the main service
cat > /app/kethernet-service.js << 'EOF'
const express = require('express');
const { KetherNetBlockchain } = require('./src/index');

async function startKetherNetService() {
    const app = express();
    app.use(express.json());
    
    // Initialize KetherNet
    const kethernet = new KetherNetBlockchain({
        enableConsciousnessValidation: true,
        consciousnessThreshold: parseInt(process.env.CONSCIOUSNESS_THRESHOLD || '2847'),
        enableCoherium: process.env.COHERIUM_ENABLED === 'true',
        enableAetherium: process.env.AETHERIUM_ENABLED === 'true',
        enableLogging: true,
        enableMetrics: true
    });
    
    await kethernet.initialize();
    
    // Health check endpoint
    app.get('/health', (req, res) => {
        res.json({
            status: 'healthy',
            service: 'kethernet-blockchain',
            version: '1.0.0-TRINITY',
            consciousness: 'validated',
            timestamp: Date.now()
        });
    });
    
    // Ready check endpoint
    app.get('/ready', (req, res) => {
        res.json({
            status: 'ready',
            service: 'kethernet-blockchain',
            crownConsensus: true,
            consciousnessValidation: true,
            timestamp: Date.now()
        });
    });
    
    // Submit transaction endpoint
    app.post('/api/transaction', async (req, res) => {
        try {
            const result = await kethernet.submitTransaction(req.body);
            res.json(result);
        } catch (error) {
            res.status(400).json({ error: error.message });
        }
    });
    
    // Validate consciousness endpoint
    app.post('/api/consciousness/validate', async (req, res) => {
        try {
            const validation = await kethernet.consciousnessValidator.validateConsciousness(
                req.body.consciousnessData,
                req.body.entityType,
                parseInt(process.env.CONSCIOUSNESS_THRESHOLD || '2847')
            );
            res.json(validation);
        } catch (error) {
            res.status(400).json({ error: error.message });
        }
    });
    
    // Get metrics endpoint
    app.get('/api/metrics', (req, res) => {
        const metrics = kethernet.getMetrics();
        res.json(metrics);
    });
    
    // Create identity endpoint (proxy to NovaDNA when available)
    app.post('/api/identity', async (req, res) => {
        try {
            const identity = await kethernet.createIdentity(req.body);
            res.json(identity);
        } catch (error) {
            res.status(400).json({ error: error.message });
        }
    });
    
    // Get identity endpoint
    app.get('/api/identity/:id', (req, res) => {
        try {
            const identity = kethernet.getIdentity(req.params.id);
            if (identity) {
                res.json(identity);
            } else {
                res.status(404).json({ error: 'Identity not found' });
            }
        } catch (error) {
            res.status(400).json({ error: error.message });
        }
    });
    
    // Analyze AI security endpoint (proxy to NovaShield when available)
    app.post('/api/security/analyze', async (req, res) => {
        try {
            const analysis = await kethernet.analyzeAISecurity(req.body);
            res.json(analysis);
        } catch (error) {
            res.status(400).json({ error: error.message });
        }
    });
    
    // Start server
    const port = process.env.PORT || 8080;
    app.listen(port, '0.0.0.0', () => {
        console.log(`🔗 KetherNet Blockchain API listening on port ${port}`);
        console.log(`👑 Crown Consensus operational`);
        console.log(`⚛️ Consciousness validation active (threshold: ${process.env.CONSCIOUSNESS_THRESHOLD})`);
        console.log(`💎 Coherium economics: ${process.env.COHERIUM_ENABLED}`);
        console.log(`⚡ Aetherium gas system: ${process.env.AETHERIUM_ENABLED}`);
    });
    
    // Metrics server
    const metricsApp = express();
    metricsApp.get('/metrics', (req, res) => {
        const metrics = kethernet.getMetrics();
        // Convert to Prometheus format
        let prometheusMetrics = '';
        prometheusMetrics += `# HELP kethernet_transactions_total Total number of transactions\n`;
        prometheusMetrics += `# TYPE kethernet_transactions_total counter\n`;
        prometheusMetrics += `kethernet_transactions_total ${metrics.totalTransactions || 0}\n`;
        
        prometheusMetrics += `# HELP kethernet_consciousness_validations_total Total consciousness validations\n`;
        prometheusMetrics += `# TYPE kethernet_consciousness_validations_total counter\n`;
        prometheusMetrics += `kethernet_consciousness_validations_total ${metrics.trinity?.consciousnessValidations || 0}\n`;
        
        prometheusMetrics += `# HELP kethernet_system_health System health score\n`;
        prometheusMetrics += `# TYPE kethernet_system_health gauge\n`;
        prometheusMetrics += `kethernet_system_health ${metrics.systemHealth || 0}\n`;
        
        res.set('Content-Type', 'text/plain');
        res.send(prometheusMetrics);
    });
    
    metricsApp.listen(8082, '0.0.0.0', () => {
        console.log(`📊 KetherNet metrics server listening on port 8082`);
    });
    
    // Consensus server (for inter-node communication)
    const consensusApp = express();
    consensusApp.use(express.json());
    
    consensusApp.post('/consensus/propose', async (req, res) => {
        try {
            // Handle consensus proposals
            res.json({ status: 'proposal_received', timestamp: Date.now() });
        } catch (error) {
            res.status(400).json({ error: error.message });
        }
    });
    
    consensusApp.listen(8081, '0.0.0.0', () => {
        console.log(`👑 Crown Consensus server listening on port 8081`);
    });
}

// Handle graceful shutdown
process.on('SIGTERM', () => {
    console.log('🛑 Received SIGTERM, shutting down gracefully...');
    process.exit(0);
});

process.on('SIGINT', () => {
    console.log('🛑 Received SIGINT, shutting down gracefully...');
    process.exit(0);
});

// Start the service
startKetherNetService().catch(error => {
    console.error('❌ Failed to start KetherNet service:', error);
    process.exit(1);
});
EOF

# Start the KetherNet service
echo "🚀 Launching KetherNet Crown Consensus..."
node /app/kethernet-service.js
