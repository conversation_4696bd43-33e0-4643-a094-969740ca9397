/**
 * Logger Module
 * 
 * This module provides logging functionality for the UAC.
 */

const winston = require('winston');

// Create a Winston logger instance
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  defaultMeta: { service: 'uac-demo' },
  transports: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    })
  ]
});

/**
 * Get a logger for a specific module
 * @param {String} module Module name
 * @returns {Object} Logger instance
 */
function getLogger(module) {
  return {
    info: (message, meta = {}) => {
      logger.info(message, { module, ...meta });
    },
    error: (message, error = {}) => {
      logger.error(message, { 
        module, 
        error: error.message || error,
        stack: error.stack
      });
    },
    warn: (message, meta = {}) => {
      logger.warn(message, { module, ...meta });
    },
    debug: (message, meta = {}) => {
      logger.debug(message, { module, ...meta });
    }
  };
}

module.exports = {
  getLogger
};
