const express = require('express');
const router = express.Router();
const { authenticateJWT, authorizeAdmin } = require('../middleware/auth');
const { validateUserUpdate } = require('../middleware/validators');

// Import controllers
const usersController = require('../controllers/usersController');

/**
 * @route GET /api/users
 * @desc Get all users (admin only)
 * @access Private (Admin)
 */
router.get('/', authenticateJWT, authorizeAdmin, usersController.getAllUsers);

/**
 * @route GET /api/users/:id
 * @desc Get a user by ID
 * @access Private (Self or Admin)
 */
router.get('/:id', authenticateJWT, usersController.getUserById);

/**
 * @route PUT /api/users/:id
 * @desc Update a user
 * @access Private (Self or Admin)
 */
router.put('/:id', authenticateJWT, validateUserUpdate, usersController.updateUser);

/**
 * @route DELETE /api/users/:id
 * @desc Delete a user
 * @access Private (Self or Admin)
 */
router.delete('/:id', authenticateJWT, usersController.deleteUser);

/**
 * @route PUT /api/users/:id/role
 * @desc Update a user's role (admin only)
 * @access Private (Admin)
 */
router.put('/:id/role', authenticateJWT, authorizeAdmin, usersController.updateUserRole);

/**
 * @route GET /api/users/:id/connectors
 * @desc Get all connectors installed by a user
 * @access Private (Self or Admin)
 */
router.get('/:id/connectors', authenticateJWT, usersController.getUserConnectors);

/**
 * @route POST /api/users/:id/partner
 * @desc Apply for partner status
 * @access Private (Self)
 */
router.post('/:id/partner', authenticateJWT, usersController.applyForPartnerStatus);

/**
 * @route PUT /api/users/:id/partner
 * @desc Approve or reject partner application (admin only)
 * @access Private (Admin)
 */
router.put('/:id/partner', authenticateJWT, authorizeAdmin, usersController.updatePartnerStatus);

module.exports = router;
