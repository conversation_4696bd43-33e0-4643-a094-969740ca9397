"""
Demo script for the Universal Compliance Visualization Framework (UCVF).

This script demonstrates how to use the UCVF to generate visualizations
for different stakeholder roles.
"""

import os
import sys
import json
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add the src directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Import the UCVF
from ucvf import VisualizationEngine
from ucvf.utils.data_utils import load_compliance_data, save_visualization

def main():
    """Run the UCVF demo."""
    logger.info("Starting UCVF demo")

    # Initialize the Visualization Engine
    engine = VisualizationEngine()

    # Load sample compliance data
    data_path = os.path.join(os.path.dirname(__file__), 'ucvf', 'data', 'sample_compliance_data.json')
    compliance_data = load_compliance_data(data_path)

    # Get available roles
    available_roles = engine.get_available_roles()
    logger.info(f"Available roles: {', '.join(available_roles)}")

    # Create output directory if it doesn't exist
    output_dir = os.path.join(os.path.dirname(__file__), 'ucvf_output')
    os.makedirs(output_dir, exist_ok=True)

    # Generate visualizations for each role
    for role in available_roles:
        # Get available visualizations for this role
        available_visualizations = engine.get_available_visualizations(role)
        logger.info(f"Available visualizations for {role}: {', '.join(available_visualizations)}")

        # Generate each type of visualization
        for viz_type in available_visualizations:
            # Generate the visualization
            visualization = engine.generate_visualization(
                data=compliance_data,
                role=role,
                visualization_type=viz_type
            )

            # Save the visualization
            output_path = os.path.join(output_dir, f"{role}_{viz_type}.json")
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(visualization, f, indent=2)

            logger.info(f"Generated {viz_type} for {role} role and saved to {output_path}")

    logger.info("UCVF demo completed successfully")

if __name__ == "__main__":
    main()
