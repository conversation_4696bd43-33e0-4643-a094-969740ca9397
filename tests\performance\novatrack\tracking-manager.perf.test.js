/**
 * Performance Tests for NovaTrack TrackingManager
 * 
 * These tests measure the performance of the TrackingManager
 */

const { describe, it, beforeEach, afterEach, expect } = require('@jest/globals');
const fs = require('fs');
const path = require('path');
const os = require('os');

// Import the TrackingManager mock
const { TrackingManager } = require('../../mocks/tracking-manager.mock');

// Import test data generator
const { 
  generateRequirement, 
  generateActivity,
  generateTestDataset
} = require('../../data/novatrack-test-data');

describe('NovaTrack TrackingManager - Performance', () => {
  let trackingManager;
  let tempDir;
  
  beforeEach(() => {
    // Create a temporary directory for test data
    tempDir = fs.mkdtempSync(path.join(os.tmpdir(), 'novatrack-perf-test-'));
    
    // Initialize the TrackingManager with the temporary directory
    trackingManager = new TrackingManager(tempDir);
  });
  
  afterEach(() => {
    // Clean up the temporary directory
    if (fs.existsSync(tempDir)) {
      fs.rmSync(tempDir, { recursive: true, force: true });
    }
  });
  
  describe('Bulk Operations', () => {
    it('should handle creating 100 requirements efficiently', () => {
      // Measure the time to create 100 requirements
      const requirementCount = 100;
      const requirements = [];
      
      const startTime = Date.now();
      
      for (let i = 0; i < requirementCount; i++) {
        const requirementData = generateRequirement({
          name: `Performance Test Requirement ${i}`
        });
        
        const requirement = trackingManager.create_requirement(requirementData);
        requirements.push(requirement);
      }
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // Log the performance metrics
      console.log(`Created ${requirementCount} requirements in ${duration}ms (${duration / requirementCount}ms per requirement)`);
      
      // Verify all requirements were created
      expect(requirements.length).toBe(requirementCount);
      expect(Object.keys(trackingManager.requirements).length).toBe(requirementCount);
      
      // Performance assertion - should create requirements in a reasonable time
      // This is a flexible assertion that can be adjusted based on the environment
      expect(duration / requirementCount).toBeLessThan(50); // Less than 50ms per requirement
    });
    
    it('should handle creating 100 activities efficiently', () => {
      // Create a requirement for the activities
      const requirementData = generateRequirement();
      const requirement = trackingManager.create_requirement(requirementData);
      
      // Measure the time to create 100 activities
      const activityCount = 100;
      const activities = [];
      
      const startTime = Date.now();
      
      for (let i = 0; i < activityCount; i++) {
        const activityData = generateActivity({
          name: `Performance Test Activity ${i}`,
          requirement_id: requirement.id
        });
        
        const activity = trackingManager.create_activity(activityData);
        activities.push(activity);
      }
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // Log the performance metrics
      console.log(`Created ${activityCount} activities in ${duration}ms (${duration / activityCount}ms per activity)`);
      
      // Verify all activities were created
      expect(activities.length).toBe(activityCount);
      expect(Object.keys(trackingManager.activities).length).toBe(activityCount);
      
      // Performance assertion - should create activities in a reasonable time
      expect(duration / activityCount).toBeLessThan(50); // Less than 50ms per activity
    });
    
    it('should handle retrieving 100 requirements efficiently', () => {
      // Create 100 requirements
      const requirementCount = 100;
      const requirements = [];
      
      for (let i = 0; i < requirementCount; i++) {
        const requirementData = generateRequirement({
          name: `Performance Test Requirement ${i}`
        });
        
        const requirement = trackingManager.create_requirement(requirementData);
        requirements.push(requirement);
      }
      
      // Measure the time to retrieve 100 requirements
      const startTime = Date.now();
      
      for (let i = 0; i < requirementCount; i++) {
        const retrievedRequirement = trackingManager.get_requirement(requirements[i].id);
        expect(retrievedRequirement).toEqual(requirements[i]);
      }
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // Log the performance metrics
      console.log(`Retrieved ${requirementCount} requirements in ${duration}ms (${duration / requirementCount}ms per requirement)`);
      
      // Performance assertion - should retrieve requirements in a reasonable time
      expect(duration / requirementCount).toBeLessThan(10); // Less than 10ms per requirement
    });
    
    it('should handle retrieving 100 activities efficiently', () => {
      // Create a requirement for the activities
      const requirementData = generateRequirement();
      const requirement = trackingManager.create_requirement(requirementData);
      
      // Create 100 activities
      const activityCount = 100;
      const activities = [];
      
      for (let i = 0; i < activityCount; i++) {
        const activityData = generateActivity({
          name: `Performance Test Activity ${i}`,
          requirement_id: requirement.id
        });
        
        const activity = trackingManager.create_activity(activityData);
        activities.push(activity);
      }
      
      // Measure the time to retrieve 100 activities
      const startTime = Date.now();
      
      for (let i = 0; i < activityCount; i++) {
        const retrievedActivity = trackingManager.get_activity(activities[i].id);
        expect(retrievedActivity).toEqual(activities[i]);
      }
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // Log the performance metrics
      console.log(`Retrieved ${activityCount} activities in ${duration}ms (${duration / activityCount}ms per activity)`);
      
      // Performance assertion - should retrieve activities in a reasonable time
      expect(duration / activityCount).toBeLessThan(10); // Less than 10ms per activity
    });
    
    it('should handle updating 100 requirements efficiently', () => {
      // Create 100 requirements
      const requirementCount = 100;
      const requirements = [];
      
      for (let i = 0; i < requirementCount; i++) {
        const requirementData = generateRequirement({
          name: `Performance Test Requirement ${i}`
        });
        
        const requirement = trackingManager.create_requirement(requirementData);
        requirements.push(requirement);
      }
      
      // Measure the time to update 100 requirements
      const startTime = Date.now();
      
      for (let i = 0; i < requirementCount; i++) {
        const updateData = {
          name: `Updated Performance Test Requirement ${i}`,
          status: 'completed'
        };
        
        const updatedRequirement = trackingManager.update_requirement(requirements[i].id, updateData);
        expect(updatedRequirement.name).toBe(updateData.name);
        expect(updatedRequirement.status).toBe(updateData.status);
      }
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // Log the performance metrics
      console.log(`Updated ${requirementCount} requirements in ${duration}ms (${duration / requirementCount}ms per requirement)`);
      
      // Performance assertion - should update requirements in a reasonable time
      expect(duration / requirementCount).toBeLessThan(50); // Less than 50ms per requirement
    });
    
    it('should handle deleting 100 requirements efficiently', () => {
      // Create 100 requirements
      const requirementCount = 100;
      const requirements = [];
      
      for (let i = 0; i < requirementCount; i++) {
        const requirementData = generateRequirement({
          name: `Performance Test Requirement ${i}`
        });
        
        const requirement = trackingManager.create_requirement(requirementData);
        requirements.push(requirement);
      }
      
      // Measure the time to delete 100 requirements
      const startTime = Date.now();
      
      for (let i = 0; i < requirementCount; i++) {
        const result = trackingManager.delete_requirement(requirements[i].id);
        expect(result).toBe(true);
      }
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // Log the performance metrics
      console.log(`Deleted ${requirementCount} requirements in ${duration}ms (${duration / requirementCount}ms per requirement)`);
      
      // Verify all requirements were deleted
      expect(Object.keys(trackingManager.requirements).length).toBe(0);
      
      // Performance assertion - should delete requirements in a reasonable time
      expect(duration / requirementCount).toBeLessThan(50); // Less than 50ms per requirement
    });
  });
  
  describe('Large Dataset Operations', () => {
    it('should handle a large dataset efficiently', () => {
      // Generate a large dataset
      const testData = generateTestDataset({
        requirementCount: 50,
        activitiesPerRequirement: 5
      });
      
      // Measure the time to create the dataset
      const startTime = Date.now();
      
      // Create requirements
      const createdRequirements = testData.requirements.map(req => 
        trackingManager.create_requirement(req)
      );
      
      // Create activities
      const createdActivities = testData.activities.map(act => 
        trackingManager.create_activity(act)
      );
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // Log the performance metrics
      console.log(`Created a dataset with ${testData.requirements.length} requirements and ${testData.activities.length} activities in ${duration}ms`);
      
      // Verify the dataset was created correctly
      expect(createdRequirements.length).toBe(testData.requirements.length);
      expect(createdActivities.length).toBe(testData.activities.length);
      
      // Performance assertion - should create the dataset in a reasonable time
      const totalItems = testData.requirements.length + testData.activities.length;
      expect(duration / totalItems).toBeLessThan(50); // Less than 50ms per item
    });
    
    it('should handle filtering a large dataset efficiently', () => {
      // Generate a large dataset with multiple frameworks
      const frameworks = ['GDPR', 'SOC2', 'HIPAA', 'ISO27001', 'PCI-DSS'];
      const requirementsPerFramework = 20;
      const activitiesPerRequirement = 3;
      
      // Create requirements for each framework
      const allRequirements = [];
      
      for (const framework of frameworks) {
        for (let i = 0; i < requirementsPerFramework; i++) {
          const requirementData = generateRequirement({
            name: `${framework} Requirement ${i}`,
            framework: framework
          });
          
          const requirement = trackingManager.create_requirement(requirementData);
          allRequirements.push(requirement);
          
          // Create activities for this requirement
          for (let j = 0; j < activitiesPerRequirement; j++) {
            const activityData = generateActivity({
              name: `${framework} Activity ${i}-${j}`,
              requirement_id: requirement.id
            });
            
            trackingManager.create_activity(activityData);
          }
        }
      }
      
      // Measure the time to filter requirements by framework
      const startTime = Date.now();
      
      for (const framework of frameworks) {
        const filteredRequirements = trackingManager.get_requirements_by_framework(framework);
        expect(filteredRequirements.length).toBe(requirementsPerFramework);
      }
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // Log the performance metrics
      console.log(`Filtered requirements by framework (${frameworks.length} frameworks) in ${duration}ms (${duration / frameworks.length}ms per framework)`);
      
      // Performance assertion - should filter requirements in a reasonable time
      expect(duration / frameworks.length).toBeLessThan(50); // Less than 50ms per framework
    });
    
    it('should handle retrieving activities for requirements efficiently', () => {
      // Generate a large dataset
      const requirementCount = 50;
      const activitiesPerRequirement = 5;
      
      // Create requirements and activities
      const requirements = [];
      
      for (let i = 0; i < requirementCount; i++) {
        const requirementData = generateRequirement({
          name: `Performance Test Requirement ${i}`
        });
        
        const requirement = trackingManager.create_requirement(requirementData);
        requirements.push(requirement);
        
        // Create activities for this requirement
        for (let j = 0; j < activitiesPerRequirement; j++) {
          const activityData = generateActivity({
            name: `Performance Test Activity ${i}-${j}`,
            requirement_id: requirement.id
          });
          
          trackingManager.create_activity(activityData);
        }
      }
      
      // Measure the time to retrieve activities for each requirement
      const startTime = Date.now();
      
      for (const requirement of requirements) {
        const activities = trackingManager.get_requirement_activities(requirement.id);
        expect(activities.length).toBe(activitiesPerRequirement);
      }
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // Log the performance metrics
      console.log(`Retrieved activities for ${requirementCount} requirements in ${duration}ms (${duration / requirementCount}ms per requirement)`);
      
      // Performance assertion - should retrieve activities in a reasonable time
      expect(duration / requirementCount).toBeLessThan(50); // Less than 50ms per requirement
    });
  });
});
