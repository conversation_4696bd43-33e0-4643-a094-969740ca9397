import React from 'react';
import Sidebar from '../components/Sidebar';

export default function StrategicIndependencePlan() {
  const sidebarItems = [
    { type: 'category', label: 'Strategic Plan', items: [
      { label: 'Executive Summary', href: '#executive-summary' },
      { label: 'Why GCP Is Mission-Critical', href: '#why-gcp' },
      { label: 'Capital Efficiency Strategy', href: '#capital-efficiency' },
      { label: 'Financial Independence Path', href: '#financial-independence' },
      { label: 'Investor-Ready Narrative', href: '#investor-narrative' }
    ]},
    { type: 'category', label: 'Related Pages', items: [
      { label: 'Google Partnership Portal', href: '/google-partnership-portal' },
      { label: 'Technology Roadmap', href: '/technology-roadmap' }
    ]}
  ];

  return (
    <div className="flex flex-col md:flex-row gap-8">
      {/* Sidebar - Hidden on mobile, shown on desktop */}
      <div className="hidden md:block md:w-1/4 lg:w-1/5">
        <div className="sticky top-4">
          <Sidebar items={sidebarItems} title="Strategic Independence" />
        </div>
      </div>

      {/* Mobile Sidebar Toggle - Shown on mobile only */}
      <div className="md:hidden mb-4">
        <select 
          className="w-full bg-secondary text-white border border-gray-700 rounded p-2"
          onChange={(e) => {
            if (e.target.value) window.location.href = e.target.value;
          }}
          defaultValue=""
        >
          <option value="" disabled>Navigate to...</option>
          {sidebarItems.map((item, index) => (
            item.type === 'category' ? (
              <optgroup key={index} label={item.label}>
                {item.items.map((subItem, subIndex) => (
                  <option key={`${index}-${subIndex}`} value={subItem.href}>
                    {subItem.label}
                  </option>
                ))}
              </optgroup>
            ) : (
              <option key={index} value={item.href}>
                {item.label}
              </option>
            )
          ))}
        </select>
      </div>

      {/* Main Content */}
      <div className="w-full md:w-3/4 lg:w-4/5">
        <div className="bg-secondary text-white py-8 px-6 rounded-lg mb-8">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold">NovaGRC Strategic Independence Plan</h1>
              <p className="text-xl mt-2">Plan C: Strategic Independence with GCP Alignment</p>
            </div>
            <div className="text-right">
              <p className="text-lg">CONFIDENTIAL</p>
              <p>June 2024</p>
            </div>
          </div>
        </div>

        {/* Executive Summary */}
        <section id="executive-summary" className="mb-12 bg-secondary rounded-lg shadow-md p-8">
          <h2 className="text-2xl font-bold mb-4 text-blue-400">Executive Summary</h2>
          <div className="border-l-4 border-blue-600 bg-gray-800 p-4 mb-6">
            <p className="text-lg italic text-gray-300">
              "We're not just choosing Google Cloud — we've engineered NovaGRC's entire DNA around its strengths. This is not a preference. It's a non-negotiable prerequisite for our product vision, performance, and go-to-market strategy."
            </p>
          </div>
          <p className="mb-4 text-gray-300">
            This document outlines NovaGRC's strategic approach to building and scaling our AI-driven compliance platform on Google Cloud Platform (GCP), with a focus on capital efficiency and revenue-driven growth. While cloud credits would accelerate our journey, this plan demonstrates our path to success regardless of external support.
          </p>
          <p className="text-gray-300">
            Our "GCP or Bust" strategy isn't about cost savings—it's about leveraging the only platform that can fully enable our vision for AI-driven compliance and Cyber-Safety.
          </p>
        </section>

        {/* Why GCP Is Mission-Critical */}
        <section id="why-gcp" className="mb-12 bg-secondary rounded-lg shadow-md p-8">
          <h2 className="text-2xl font-bold mb-6 text-blue-400">Why GCP Is Mission-Critical for NovaGRC</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="border-l-4 border-blue-600 pl-4">
              <h3 className="text-xl font-bold mb-3"><span className="text-green-500">✓</span> AI-Native Infrastructure</h3>
              <ul className="list-disc list-inside space-y-2 ml-4 text-gray-300">
                <li>Vertex AI powers our predictive risk analytics, real-time compliance scoring, and Explainable AI (XAI) models</li>
                <li>Document AI + Natural Language APIs are the backbone of NovaParse and regulatory text interpretation</li>
                <li>Only GCP delivers this level of vertically integrated, enterprise-grade AI that we can productize on day one</li>
              </ul>
            </div>
            
            <div className="border-l-4 border-blue-600 pl-4">
              <h3 className="text-xl font-bold mb-3"><span className="text-green-500">✓</span> API-First Scalability</h3>
              <ul className="list-disc list-inside space-y-2 ml-4 text-gray-300">
                <li>API Gateway + Cloud Run let us scale NovaFuse, NovaQuest, and NovaPlay with precision</li>
                <li>Event-driven compliance checks demand cold-start performance and autoscaling without overhead — GCP delivers</li>
              </ul>
            </div>
            
            <div className="border-l-4 border-blue-600 pl-4">
              <h3 className="text-xl font-bold mb-3"><span className="text-green-500">✓</span> Security & Compliance Alignment</h3>
              <ul className="list-disc list-inside space-y-2 ml-4 text-gray-300">
                <li>GCP's Confidential VMs, Security Command Center, and default encryption support our best-in-class InfoSec posture</li>
                <li>As a GRC company, our platform must be the example — and GCP's native security stack makes that possible</li>
              </ul>
            </div>
            
            <div className="border-l-4 border-blue-600 pl-4">
              <h3 className="text-xl font-bold mb-3"><span className="text-green-500">✓</span> Strategic Ecosystem Fit</h3>
              <ul className="list-disc list-inside space-y-2 ml-4 text-gray-300">
                <li>We're not just cloud-native — we're Google-native</li>
                <li>NovaGRC is a natural showcase for GCP's RegTech, AI, and Cyber-Safety leadership</li>
                <li>We're ready for Google's marketplace, co-marketing programs, and partner ecosystem from Day 1</li>
              </ul>
            </div>
          </div>
        </section>

        {/* Capital Efficiency Strategy */}
        <section id="capital-efficiency" className="mb-12 bg-secondary rounded-lg shadow-md p-8">
          <h2 className="text-2xl font-bold mb-6 text-blue-400">Capital Efficiency Strategy</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-gray-800 p-6 rounded-lg">
              <h3 className="text-xl font-bold mb-3">🚀 Revenue-First Product Launch</h3>
              <ul className="list-disc list-inside space-y-2 text-gray-300">
                <li>Launch with NovaFuse API Superstore as the monetization core</li>
                <li>Prioritize partner integrations that generate recurring revenue quickly</li>
                <li>Infrastructure scales in direct proportion to usage and income</li>
              </ul>
            </div>
            
            <div className="bg-gray-800 p-6 rounded-lg">
              <h3 className="text-xl font-bold mb-3">🔄 Phased Product Expansion</h3>
              <ul className="list-disc list-inside space-y-2 text-gray-300">
                <li>Start with NovaFuse + NovaPlay (gamification)</li>
                <li>Roll out NovaQuest + NovaXP as revenue permits</li>
                <li>AI compute is sequenced by ROI and usage metrics — not ambition alone</li>
              </ul>
            </div>
            
            <div className="bg-gray-800 p-6 rounded-lg">
              <h3 className="text-xl font-bold mb-3">🤝 Strategic Partner Co-Investment</h3>
              <ul className="list-disc list-inside space-y-2 text-gray-300">
                <li>Identify 3–5 key partners to co-fund specific integrations</li>
                <li>Offer early access, higher revenue share, or co-branding</li>
                <li>Form a Partner Advisory Board to build momentum and trust</li>
              </ul>
            </div>
          </div>
        </section>

        {/* Financial Independence Path */}
        <section id="financial-independence" className="mb-12 bg-secondary rounded-lg shadow-md p-8">
          <h2 className="text-2xl font-bold mb-6 text-blue-400">Financial Independence Path</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-xl font-bold mb-4">Revenue Model</h3>
              <div className="overflow-x-auto">
                <table className="w-full mb-6">
                  <thead>
                    <tr className="border-b border-gray-700">
                      <th className="text-left p-2">Product</th>
                      <th className="text-left p-2">Price</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr className="border-b border-gray-700">
                      <td className="p-2">Base Platform</td>
                      <td className="p-2">$99/user/month (min 3 users)</td>
                    </tr>
                    <tr className="border-b border-gray-700">
                      <td className="p-2">Premium Modules</td>
                      <td className="p-2">$499/month/module</td>
                    </tr>
                    <tr className="border-b border-gray-700">
                      <td className="p-2">Microservices</td>
                      <td className="p-2">$19/month/service</td>
                    </tr>
                    <tr>
                      <td className="p-2">API Usage</td>
                      <td className="p-2">$0.005/call (volume discounts)</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
            
            <div>
              <h3 className="text-xl font-bold mb-4">Year 1 Revenue Milestones</h3>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-700">
                      <th className="text-left p-2">Quarter</th>
                      <th className="text-left p-2">Target Customers</th>
                      <th className="text-left p-2">Projected MRR</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr className="border-b border-gray-700">
                      <td className="p-2">Q1</td>
                      <td className="p-2">10</td>
                      <td className="p-2">$3,000/month</td>
                    </tr>
                    <tr className="border-b border-gray-700">
                      <td className="p-2">Q2</td>
                      <td className="p-2">25</td>
                      <td className="p-2">$9,000/month</td>
                    </tr>
                    <tr className="border-b border-gray-700">
                      <td className="p-2">Q3</td>
                      <td className="p-2">50</td>
                      <td className="p-2">$20,000/month</td>
                    </tr>
                    <tr>
                      <td className="p-2">Q4</td>
                      <td className="p-2">100</td>
                      <td className="p-2">$45,000/month</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
          
          <div className="mt-8">
            <h3 className="text-xl font-bold mb-4">⚙️ Infrastructure Optimization Strategy</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-gray-800 p-4 rounded">
                <p className="text-gray-300"><span className="font-bold">Autoscaling by default</span> — no idle infra waste</p>
              </div>
              <div className="bg-gray-800 p-4 rounded">
                <p className="text-gray-300"><span className="font-bold">Preemptible VMs</span> for non-critical workloads (saves up to 80%)</p>
              </div>
              <div className="bg-gray-800 p-4 rounded">
                <p className="text-gray-300"><span className="font-bold">High-density containerization</span> for cost efficiency</p>
              </div>
              <div className="bg-gray-800 p-4 rounded">
                <p className="text-gray-300"><span className="font-bold">Caching layer</span> to reduce redundant API usage and compute</p>
              </div>
            </div>
          </div>
        </section>

        {/* Investor-Ready Narrative */}
        <section id="investor-narrative" className="bg-secondary rounded-lg shadow-md p-8">
          <h2 className="text-2xl font-bold mb-6 text-blue-400">💼 Investor-Ready Narrative</h2>
          
          <div className="border-l-4 border-blue-600 bg-gray-800 p-6">
            <p className="text-lg text-gray-300">
              "NovaGRC isn't betting on free credits. We're building a self-sustaining, revenue-aligned AI compliance company with or without them. GCP is not about convenience — it's the only platform that can support the AI-native, real-time, explainable future of Cyber-Safety. Our go-to-market is strategically sequenced for growth and capital efficiency, and our business model is already aligned with profitability. We're building more than a product — we're building the future of proactive risk and compliance."
            </p>
          </div>
        </section>
      </div>
    </div>
  );
}
