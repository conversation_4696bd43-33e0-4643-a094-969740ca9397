/**
 * NovaFuse Universal API Connector - Validation Error
 * 
 * This module defines validation-related errors for the UAC.
 */

const UAConnectorError = require('./base-error');

/**
 * Error class for validation failures
 * @class ValidationError
 * @extends UAConnectorError
 */
class ValidationError extends UAConnectorError {
  /**
   * Create a new ValidationError
   * 
   * @param {string} message - Error message
   * @param {Object} options - Error options
   * @param {string} options.code - Error code
   * @param {string} options.severity - Error severity
   * @param {Object} options.context - Additional context for the error
   * @param {Error} options.cause - The error that caused this error
   * @param {Array<Object>} options.validationErrors - List of validation errors
   */
  constructor(message, options = {}) {
    super(message, {
      code: options.code || 'VALIDATION_ERROR',
      severity: options.severity || 'error',
      context: options.context || {},
      cause: options.cause
    });
    
    this.validationErrors = options.validationErrors || [];
  }

  /**
   * Get a user-friendly error message
   * 
   * @returns {string} - User-friendly error message
   */
  getUserMessage() {
    if (this.validationErrors.length > 0) {
      const errorMessages = this.validationErrors.map(err => err.message).join('; ');
      return `Validation failed: ${errorMessages}`;
    }
    return 'The provided data is invalid. Please check your input and try again.';
  }

  /**
   * Convert the error to a JSON object
   * 
   * @param {boolean} includeStack - Whether to include the stack trace
   * @returns {Object} - JSON representation of the error
   */
  toJSON(includeStack = false) {
    const json = super.toJSON(includeStack);
    json.validationErrors = this.validationErrors;
    return json;
  }
}

/**
 * Error class for missing required fields
 * @class MissingRequiredFieldError
 * @extends ValidationError
 */
class MissingRequiredFieldError extends ValidationError {
  /**
   * Create a new MissingRequiredFieldError
   * 
   * @param {string|Array<string>} fields - The missing field(s)
   * @param {Object} options - Error options
   */
  constructor(fields, options = {}) {
    const fieldList = Array.isArray(fields) ? fields : [fields];
    const fieldStr = fieldList.join(', ');
    const message = `Missing required field(s): ${fieldStr}`;
    
    super(message, {
      code: options.code || 'VALIDATION_MISSING_REQUIRED_FIELD',
      severity: options.severity || 'error',
      context: { ...options.context, fields: fieldList },
      cause: options.cause,
      validationErrors: fieldList.map(field => ({
        field,
        message: `Missing required field: ${field}`,
        code: 'MISSING_REQUIRED_FIELD'
      }))
    });
  }
}

/**
 * Error class for invalid field values
 * @class InvalidFieldValueError
 * @extends ValidationError
 */
class InvalidFieldValueError extends ValidationError {
  /**
   * Create a new InvalidFieldValueError
   * 
   * @param {Object|Array<Object>} fieldErrors - The field error(s)
   * @param {Object} options - Error options
   */
  constructor(fieldErrors, options = {}) {
    const errors = Array.isArray(fieldErrors) ? fieldErrors : [fieldErrors];
    const fieldStr = errors.map(e => e.field).join(', ');
    const message = `Invalid value(s) for field(s): ${fieldStr}`;
    
    super(message, {
      code: options.code || 'VALIDATION_INVALID_FIELD_VALUE',
      severity: options.severity || 'error',
      context: { ...options.context, fieldErrors: errors },
      cause: options.cause,
      validationErrors: errors.map(error => ({
        field: error.field,
        value: error.value,
        message: error.message || `Invalid value for field: ${error.field}`,
        code: 'INVALID_FIELD_VALUE',
        constraint: error.constraint
      }))
    });
  }
}

/**
 * Error class for schema validation failures
 * @class SchemaValidationError
 * @extends ValidationError
 */
class SchemaValidationError extends ValidationError {
  /**
   * Create a new SchemaValidationError
   * 
   * @param {string} schemaName - The name of the schema
   * @param {Array<Object>} errors - The validation errors
   * @param {Object} options - Error options
   */
  constructor(schemaName, errors = [], options = {}) {
    const message = `Schema validation failed for ${schemaName}`;
    
    super(message, {
      code: options.code || 'VALIDATION_SCHEMA_ERROR',
      severity: options.severity || 'error',
      context: { ...options.context, schemaName },
      cause: options.cause,
      validationErrors: errors.map(error => ({
        field: error.field || error.path,
        message: error.message,
        code: error.code || 'SCHEMA_VALIDATION_ERROR',
        schemaPath: error.schemaPath
      }))
    });
    
    this.schemaName = schemaName;
  }
}

module.exports = {
  ValidationError,
  MissingRequiredFieldError,
  InvalidFieldValueError,
  SchemaValidationError
};
