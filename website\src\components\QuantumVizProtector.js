/**
 * QuantumVizProtector
 * High-performance wrapper for quantum visualization with IP protection
 * 
 * Protected by US63/XXXXXX, US63/YYYYYY
 */

import { useEffect, useRef, useState, useCallback } from 'react';
import { quantumIPGuard } from '../utils/quantumIPGuard';

export default function QuantumVizProtector({ children, maxQubits = 5000000, securityLevel = 'enterprise' }) {
  const containerRef = useRef(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [error, setError] = useState(null);
  const [performanceMetrics, setPerformanceMetrics] = useState({
    fps: 0,
    qubitCount: 0,
    memoryUsage: 0,
    renderTime: 0
  });
  
  // Initialize the protection system
  useEffect(() => {
    let mounted = true;
    
    const initProtection = async () => {
      try {
        // Initialize with security level from props
        quantumIPGuard.options.protectionLevel = securityLevel;
        await quantumIPGuard.initialize();
        
        if (mounted) {
          setIsInitialized(true);
          
          // Report initialization
          quantumIPGuard.reportEvent('viz_protector_initialized', {
            maxQubits,
            securityLevel,
            containerId: containerRef.current?.id || 'none'
          });
        }
      } catch (err) {
        console.error('Failed to initialize QuantumVizProtector:', err);
        if (mounted) {
          setError('Failed to initialize security system');
        }
      }
    };
    
    initProtection();
    
    return () => {
      mounted = false;
      // Cleanup logic if needed
    };
  }, [securityLevel]);
  
  // Performance monitoring
  const updateMetrics = useCallback((metrics) => {
    setPerformanceMetrics(prev => ({
      ...prev,
      ...metrics,
      timestamp: Date.now()
    }));
  }, []);
  
  // Tamper detection
  useEffect(() => {
    if (!isInitialized) return;
    
    const handleTamper = (event) => {
      console.warn('[QuantumVizProtector] Tamper detected:', event.detail);
      updateMetrics({
        securityAlert: event.detail.type,
        lastAlert: new Date().toISOString()
      });
    };
    
    window.addEventListener('tamper_detected', handleTamper);
    return () => {
      window.removeEventListener('tamper_detected', handleTamper);
    };
  }, [isInitialized, updateMetrics]);
  
  // Render the protected content
  if (error) {
    return (
      <div className="quantum-error" style={errorStyles}>
        <h3>Security Error</h3>
        <p>{error}</p>
        <p>Please refresh the page or contact support if the issue persists.</p>
        <div style={{ marginTop: '1rem', fontSize: '0.8rem', opacity: 0.7 }}>
          Reference: {quantumIPGuard.sessionId}
        </div>
      </div>
    );
  }
  
  if (!isInitialized) {
    return (
      <div className="quantum-loading" style={loadingStyles}>
        <div className="spinner"></div>
        <p>Initializing quantum security...</p>
      </div>
    );
  }
  
  // Clone children with protection props
  const protectedChildren = React.Children.map(children, child => {
    if (React.isValidElement(child)) {
      return React.cloneElement(child, {
        'data-protected': 'true',
        'data-protection-level': securityLevel,
        'data-max-qubits': maxQubits,
        onMetricsUpdate: updateMetrics
      });
    }
    return child;
  });
  
  return (
    <div 
      ref={containerRef} 
      className="quantum-viz-protector"
      style={containerStyles}
    >
      <div className="quantum-viz-content">
        {protectedChildren}
      </div>
      
      {securityLevel !== 'none' && (
        <div className="quantum-watermark" style={watermarkStyles}>
          {quantumIPGuard.patents.map(patent => (
            <div key={patent} className="patent-notice" style={patentStyles}>
              {patent}
            </div>
          ))}
        </div>
      )}
      
      <style jsx global>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
        
        .quantum-watermark {
          pointer-events: none;
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          display: flex;
          flex-direction: column;
          justify-content: space-around;
          align-items: center;
          opacity: 0.03;
          z-index: 9999;
          font-size: 24px;
          color: #0066ff;
          font-weight: bold;
          transform: rotate(-15deg);
          white-space: nowrap;
        }
        
        .patent-notice {
          margin: 20px 0;
          transform: scale(1.5);
        }
      `}</style>
    </div>
  );
}

// Inline styles for better performance
const containerStyles = {
  position: 'relative',
  width: '100%',
  height: '100%',
  overflow: 'hidden'
};

const loadingStyles = {
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'center',
  alignItems: 'center',
  height: '100%',
  color: '#ffffff',
  backgroundColor: '#0a0a1a',
  padding: '2rem'
};

const errorStyles = {
  ...loadingStyles,
  color: '#ff6b6b',
  textAlign: 'center',
  padding: '2rem',
  border: '1px solid #ff6b6b',
  borderRadius: '8px',
  maxWidth: '600px',
  margin: '2rem auto'
};

const watermarkStyles = {
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  pointerEvents: 'none',
  opacity: 0.03,
  zIndex: 9999,
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'space-around',
  alignItems: 'center',
  fontSize: '24px',
  color: '#0066ff',
  fontWeight: 'bold',
  transform: 'rotate(-15deg)',
  whiteSpace: 'nowrap'
};

const patentStyles = {
  margin: '20px 0',
  transform: 'scale(1.5)'
};
