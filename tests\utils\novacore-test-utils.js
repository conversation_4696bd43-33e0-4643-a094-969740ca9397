/**
 * NovaCore Test Utilities
 * 
 * This file contains utility functions for testing NovaCore components.
 */

const crypto = require('crypto');
const { performance } = require('perf_hooks');

/**
 * Generate a random evidence object for testing
 * @param {Object} options - Options for generating the evidence
 * @returns {Object} - A random evidence object
 */
function generateRandomEvidence(options = {}) {
  const defaultOptions = {
    sourceSystem: 'test-system',
    controlId: `C-${Math.floor(Math.random() * 1000)}`,
    framework: 'NIST-CSF',
    timestamp: new Date().toISOString(),
    withAttachment: false,
    status: 'COLLECTED'
  };

  const mergedOptions = { ...defaultOptions, ...options };
  
  const evidence = {
    id: crypto.randomUUID(),
    sourceSystem: mergedOptions.sourceSystem,
    controlId: mergedOptions.controlId,
    framework: mergedOptions.framework,
    timestamp: mergedOptions.timestamp,
    status: mergedOptions.status,
    metadata: {
      collector: 'NovaCore-Test',
      version: '1.0.0',
      environment: 'test'
    },
    data: {
      value: Math.random() > 0.5,
      details: `Test evidence for ${mergedOptions.controlId}`,
      score: Math.floor(Math.random() * 100)
    }
  };

  if (mergedOptions.withAttachment) {
    evidence.attachment = {
      filename: 'test-attachment.pdf',
      contentType: 'application/pdf',
      size: 1024,
      hash: crypto.createHash('sha256').update('test-content').digest('hex')
    };
  }

  return evidence;
}

/**
 * Generate a random blockchain verification record for testing
 * @param {Object} evidence - The evidence object to verify
 * @returns {Object} - A blockchain verification record
 */
function generateVerificationRecord(evidence) {
  const evidenceHash = crypto.createHash('sha256')
    .update(JSON.stringify(evidence))
    .digest('hex');
  
  return {
    id: crypto.randomUUID(),
    evidenceId: evidence.id,
    timestamp: new Date().toISOString(),
    hash: evidenceHash,
    blockchainReference: {
      type: 'ETHEREUM',
      transactionId: `0x${crypto.randomBytes(32).toString('hex')}`,
      blockNumber: Math.floor(Math.random() * 1000000),
      timestamp: new Date().toISOString()
    },
    status: 'VERIFIED',
    verificationMethod: 'MERKLE_TREE'
  };
}

/**
 * Generate a Merkle tree for testing
 * @param {Array} hashes - Array of hashes to include in the tree
 * @returns {Object} - Merkle tree with root and proofs
 */
function generateMerkleTree(hashes) {
  // Simple implementation for testing
  const leaves = hashes.map(hash => ({ hash, parent: null }));
  
  // Build tree bottom-up
  let currentLevel = leaves;
  while (currentLevel.length > 1) {
    const nextLevel = [];
    for (let i = 0; i < currentLevel.length; i += 2) {
      const left = currentLevel[i];
      const right = i + 1 < currentLevel.length ? currentLevel[i + 1] : left;
      
      const parentHash = crypto.createHash('sha256')
        .update(left.hash + right.hash)
        .digest('hex');
      
      const parent = { hash: parentHash, left, right, parent: null };
      left.parent = parent;
      right.parent = parent;
      
      nextLevel.push(parent);
    }
    currentLevel = nextLevel;
  }
  
  const root = currentLevel[0];
  
  // Generate proofs for each leaf
  const proofs = leaves.map(leaf => {
    const proof = [];
    let current = leaf;
    
    while (current.parent) {
      const parent = current.parent;
      const isLeft = parent.left === current;
      
      proof.push({
        position: isLeft ? 'right' : 'left',
        hash: isLeft ? parent.right.hash : parent.left.hash
      });
      
      current = parent;
    }
    
    return {
      leaf: leaf.hash,
      proof
    };
  });
  
  return {
    root: root.hash,
    leaves: leaves.map(leaf => leaf.hash),
    proofs
  };
}

/**
 * Measure performance of a function
 * @param {Function} fn - Function to measure
 * @param {Array} args - Arguments to pass to the function
 * @returns {Object} - Performance metrics
 */
async function measurePerformance(fn, args = []) {
  const start = performance.now();
  const result = await fn(...args);
  const end = performance.now();
  
  return {
    result,
    executionTime: end - start,
    timestamp: new Date().toISOString()
  };
}

module.exports = {
  generateRandomEvidence,
  generateVerificationRecord,
  generateMerkleTree,
  measurePerformance
};
