#!/usr/bin/env node

/**
 * Quantum Streaming Benchmark
 * 
 * Measures performance of quantum visualization at scale
 * Protected by US63/XXXXXX, US63/YYYYYY
 */

const { performance, PerformanceObserver } = require('perf_hooks');
const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const os = require('os');
const { v4: uuidv4 } = require('uuid');

// Configuration
const CONFIG = {
  // Test scenarios
  scenarios: [
    { resolution: [7680, 4320], qubits: 5000000, label: '8K Ultra HD' }, // 8K
    { resolution: [3840, 2160], qubits: 2000000, label: '4K UHD' },     // 4K
    { resolution: [2560, 1440], qubits: 1000000, label: 'QHD' },        // 1440p
    { resolution: [1920, 1080], qubits: 500000, label: 'Full HD' }      // 1080p
  ],
  
  // Test duration per scenario (ms)
  testDuration: 30000,
  
  // Performance thresholds
  thresholds: {
    fps: {
      '8K': 45,     // Minimum FPS for 8K
      '4K': 60,      // Minimum FPS for 4K
      'default': 30  // Minimum FPS for other resolutions
    },
    memory: 4096,   // Max memory in MB
    variance: 0.5   // Max entropy variance %
  },
  
  // Output options
  output: {
    directory: './benchmark-results',
    json: true,
    markdown: true,
    console: true
  }
};

// Global state
const state = {
  startTime: 0,
  frameCount: 0,
  lastFrameTime: 0,
  frameTimes: [],
  memorySamples: [],
  entropySamples: [],
  currentTest: null,
  results: []
};

// Performance metrics collector
const obs = new PerformanceObserver((items) => {
  const entry = items.getEntries()[0];
  const now = performance.now();
  
  // Update frame timing
  state.frameCount++;
  const frameTime = now - state.lastFrameTime;
  state.frameTimes.push(frameTime);
  state.lastFrameTime = now;
  
  // Sample memory usage (approximate)
  if (global.gc) global.gc();
  const used = process.memoryUsage();
  const memoryMB = Math.round(used.heapUsed / 1024 / 1024);
  state.memorySamples.push(memoryMB);
  
  // Log progress
  if (state.frameCount % 10 === 0) {
    const elapsed = (now - state.startTime) / 1000;
    const fps = Math.round((state.frameCount / elapsed) * 10) / 10;
    process.stdout.write(
`\r[${state.currentTest.label}] FPS: ${fps.toFixed(1)} | ` +
                      `Memory: ${memoryMB}MB | ` +
                      `Qubits: ${state.currentTest.qubits.toLocaleString()} | ` +
                      `Elapsed: ${elapsed.toFixed(1)}s`);
  }
});

// Start observing performance marks
obs.observe({ entryTypes: ['measure'] });

/**
 * Run a single benchmark scenario
 */
async function runScenario(scenario) {
  return new Promise((resolve) => {
    // Initialize test state
    state.currentTest = scenario;
    state.frameCount = 0;
    state.frameTimes = [];
    state.memorySamples = [];
    state.entropySamples = [];
    state.startTime = performance.now();
    state.lastFrameTime = state.startTime;
    
    console.log(`\n🚀 Starting benchmark: ${scenario.label} (${scenario.resolution[0]}x${scenario.resolution[1]})`);
    console.log(`   Qubits: ${scenario.qubits.toLocaleString()}`);
    console.log(`   Duration: ${CONFIG.testDuration / 1000}s`);
    
    // Set up test timeout
    const timeout = setTimeout(() => {
      endTest(scenario, resolve);
    }, CONFIG.testDuration);
    
    // Start the test
    startTest(scenario, () => {
      clearTimeout(timeout);
      endTest(scenario, resolve);
    });
  });
}

/**
 * Start a test scenario
 */
function startTest(scenario, onComplete) {
  // In a real implementation, this would start the actual quantum visualization
  // For now, we'll simulate frame rendering
  
  function renderFrame() {
    performance.mark('frame-start');
    
    // Simulate rendering work
    const start = performance.now();
    while (performance.now() - start < 16) {
      // Busy wait to simulate GPU work
      Math.sqrt(Math.random()) * Math.sqrt(Math.random());
    }
    
    performance.mark('frame-end');
    performance.measure('frame', 'frame-start', 'frame-end');
    
    // Continue the render loop if we're still within the test duration
    if (performance.now() - state.startTime < CONFIG.testDuration) {
      // Simulate requestAnimationFrame
      setImmediate(renderFrame);
    } else {
      onComplete();
    }
  }
  
  // Start the render loop
  renderFrame();
}

/**
 * End the current test and collect results
 */
function endTest(scenario) {
  const endTime = performance.now();
  const duration = (endTime - state.startTime) / 1000; // seconds
  
  // Calculate metrics
  const fps = state.frameCount / duration;
  const avgFrameTime = duration * 1000 / state.frameCount; // ms
  const maxMemory = Math.max(...state.memorySamples);
  const avgMemory = state.memorySamples.reduce((a, b) => a + b, 0) / state.memorySamples.length;
  
  // Calculate frame time variance (as a percentage of average)
  const frameTimeVariance = calculateVariance(state.frameTimes) / (avgFrameTime || 1) * 100;
  
  // Determine if test passed
  const minFps = CONFIG.thresholds.fps[scenario.label.split(' ')[0]] || CONFIG.thresholds.fps.default;
  const passed = fps >= minFps && 
                maxMemory <= CONFIG.thresholds.memory && 
                frameTimeVariance <= CONFIG.thresholds.variance;
  
  // Store results
  const result = {
    id: uuidv4(),
    timestamp: new Date().toISOString(),
    scenario: scenario.label,
    resolution: scenario.resolution,
    qubits: scenario.qubits,
    duration,
    metrics: {
      fps: Math.round(fps * 10) / 10,
      frameTime: Math.round(avgFrameTime * 100) / 100,
      frameTimeVariance: Math.round(frameTimeVariance * 100) / 100,
      maxMemory,
      avgMemory: Math.round(avgMemory * 10) / 10,
      frameCount: state.frameCount
    },
    thresholds: {
      minFps: minFps,
      maxMemory: CONFIG.thresholds.memory,
      maxVariance: CONFIG.thresholds.variance
    },
    passed,
    system: {
      platform: os.platform(),
      arch: os.arch(),
      cpus: os.cpus().length,
      totalMemory: Math.round(os.totalmem() / (1024 * 1024)), // MB
      nodeVersion: process.version
    }
  };
  
  state.results.push(result);
  
  // Output results
  console.log('\n\n📊 Benchmark Results:');
  console.log('='.repeat(50));
  console.log(`Scenario:       ${scenario.label}`);
  console.log(`Resolution:     ${scenario.resolution[0]}x${scenario.resolution[1]}`);
  console.log(`Qubits:         ${scenario.qubits.toLocaleString()}`);
  console.log(`Duration:       ${duration.toFixed(1)}s`);
  console.log('-' + '-'.repeat(49));
  console.log(`FPS:            ${result.metrics.fps} (min: ${minFps}) ${fps >= minFps ? '✅' : '❌'}`);
  console.log(`Max Memory:     ${result.metrics.maxMemory}MB (max: ${CONFIG.thresholds.memory}MB) ${maxMemory <= CONFIG.thresholds.memory ? '✅' : '❌'}`);
  console.log(`Frame Variance: ${result.metrics.frameTimeVariance.toFixed(2)}% (max: ${CONFIG.thresholds.variance}%) ${frameTimeVariance <= CONFIG.thresholds.variance ? '✅' : '❌'}`);
  console.log('='.repeat(50));
  console.log(`Result:         ${passed ? 'PASSED ✅' : 'FAILED ❌'}`);
  
  return result;
}

/**
 * Calculate variance of an array of numbers
 */
function calculateVariance(values) {
  if (values.length < 2) return 0;
  
  const mean = values.reduce((a, b) => a + b, 0) / values.length;
  const squaredDiffs = values.map(value => Math.pow(value - mean, 2));
  const variance = squaredDiffs.reduce((a, b) => a + b, 0) / (values.length - 1);
  
  return Math.sqrt(variance); // Return standard deviation
}

/**
 * Save benchmark results to files
 */
async function saveResults() {
  try {
    // Create output directory if it doesn't exist
    if (!fs.existsSync(CONFIG.output.directory)) {
      fs.mkdirSync(CONFIG.output.directory, { recursive: true });
    }
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const baseFilename = `quantum-benchmark-${timestamp}`;
    
    // Save JSON results
    if (CONFIG.output.json) {
      const jsonPath = path.join(CONFIG.output.directory, `${baseFilename}.json`);
      fs.writeFileSync(jsonPath, JSON.stringify({
        metadata: {
          version: '1.0.0',
          timestamp: new Date().toISOString(),
          system: state.results[0]?.system
        },
        results: state.results
      }, null, 2));
      console.log(`\n📄 Results saved to: ${jsonPath}`);
    }
    
    // Save markdown report
    if (CONFIG.output.markdown) {
      const mdPath = path.join(CONFIG.output.directory, `${baseFilename}.md`);
      const mdContent = generateMarkdownReport();
      fs.writeFileSync(mdPath, mdContent);
      console.log(`📝 Markdown report saved to: ${mdPath}`);
    }
    
  } catch (error) {
    console.error('Failed to save results:', error);
  }
}

/**
 * Generate a markdown report from benchmark results
 */
function generateMarkdownReport() {
  let md = `# Quantum Streaming Benchmark Report

**Date:** ${new Date().toLocaleString()}  
**System:** ${os.platform()} ${os.arch()} (${os.cpus().length} CPUs, ${Math.round(os.totalmem() / (1024 * 1024 / 1024))}GB RAM)  
**Node.js:** ${process.version}

## Summary

| Scenario | Resolution | Qubits | FPS (min) | Memory (max) | Variance (max) | Status |
|----------|------------|--------|-----------|--------------|----------------|--------|
`;

  // Add a row for each test scenario
  state.results.forEach(result => {
    const status = result.passed ? '✅ PASS' : '❌ FAIL';
    md += `| ${result.scenario} | ${result.resolution[0]}x${result.resolution[1]} | ${result.qubits.toLocaleString()} | ` +
          `${result.metrics.fps} (${result.thresholds.minFps}) | ${result.metrics.maxMemory}MB (${result.thresholds.maxMemory}MB) | ` +
          `${result.metrics.frameTimeVariance.toFixed(2)}% (${result.thresholds.maxVariance}%) | ${status} |\n`;
  });

  // Add detailed results
  md += '\n## Detailed Results\n\n';
  
  state.results.forEach((result, index) => {
    md += `### ${result.scenario} (${result.resolution[0]}x${result.resolution[1]}, ${result.qubits.toLocaleString()} qubits)\n\n`;
    md += `- **Status:** ${result.passed ? '✅ PASSED' : '❌ FAILED'}\n`;
    md += `- **Duration:** ${result.duration.toFixed(1)}s\n`;
    md += `- **FPS:** ${result.metrics.fps} (min: ${result.thresholds.minFps}) ${result.metrics.fps >= result.thresholds.minFps ? '✅' : '❌'}\n`;
    md += `- **Max Memory:** ${result.metrics.maxMemory}MB (max: ${result.thresholds.maxMemory}MB) ${result.metrics.maxMemory <= result.thresholds.maxMemory ? '✅' : '❌'}\n`;
    md += `- **Frame Time Variance:** ${result.metrics.frameTimeVariance.toFixed(2)}% (max: ${result.thresholds.maxVariance}%) ${result.metrics.frameTimeVariance <= result.thresholds.maxVariance ? '✅' : '❌'}\n`;
    md += `- **Frame Count:** ${result.metrics.frameCount}\n`;
    md += `- **Avg. Frame Time:** ${result.metrics.frameTime.toFixed(2)}ms\n\n`;
  });

  // Add system information
  if (state.results.length > 0) {
    const sys = state.results[0].system;
    md += '## System Information\n\n';
    md += `- **Platform:** ${sys.platform} (${sys.arch})\n`;
    md += `- **CPU Cores:** ${sys.cpus}\n`;
    md += `- **Total Memory:** ${sys.totalMemory}MB\n`;
    md += `- **Node.js Version:** ${sys.nodeVersion}\n`;
  }

  // Add footer
  md += '\n---\n';
  md += `*Generated by NovaFuse Quantum Benchmark v1.0.0*  \n`;
  md += `*Protected by US63/XXXXXX, US63/YYYYYY*\n`;
  
  return md;
}

/**
 * Main function
 */
async function main() {
  console.log('🚀 NovaFuse Quantum Streaming Benchmark');
  console.log('='.repeat(50));
  console.log(`Running ${CONFIG.scenarios.length} test scenarios...\n`);
  
  try {
    // Run each scenario sequentially
    for (const scenario of CONFIG.scenarios) {
      await runScenario(scenario);
    }
    
    // Save results
    await saveResults();
    
    // Check if all tests passed
    const allPassed = state.results.every(r => r.passed);
    console.log(`\n✨ Benchmark ${allPassed ? 'completed successfully!' : 'completed with failures.'}`);
    
    process.exit(allPassed ? 0 : 1);
    
  } catch (error) {
    console.error('\n❌ Benchmark failed:', error);
    process.exit(1);
  }
}

// Run the benchmark
if (require.main === module) {
  main();
}

// Export for programmatic use
module.exports = {
  runBenchmark: main,
  CONFIG
};
