#!/usr/bin/env python3
"""
Create a sample network traffic dataset similar to the Kaggle Computer Network Traffic dataset.
This script generates synthetic network traffic data for UUFT pattern analysis.
"""

import os
import sys
import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import random

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('sample_network_traffic.log')
    ]
)
logger = logging.getLogger('Sample_Network_Traffic')

# Constants
NUM_SAMPLES = 20000
NUM_IPS = 10
START_DATE = datetime(2023, 1, 1)
END_DATE = datetime(2023, 4, 1)
PROTOCOLS = ['TCP', 'UDP', 'ICMP', 'HTTP', 'HTTPS', 'DNS', 'SMTP', 'FTP']
PORTS = [21, 22, 23, 25, 53, 80, 110, 143, 443, 465, 587, 993, 995, 3306, 3389, 5432, 8080, 8443]
SAMPLE_DIR = "sample_network_traffic"
os.makedirs(SAMPLE_DIR, exist_ok=True)

def generate_ip():
    """Generate a random IP address."""
    return f"192.168.1.{random.randint(1, NUM_IPS)}"

def generate_timestamp():
    """Generate a random timestamp within the date range."""
    time_delta = END_DATE - START_DATE
    random_days = random.randint(0, time_delta.days)
    random_seconds = random.randint(0, 24 * 60 * 60)
    return START_DATE + timedelta(days=random_days, seconds=random_seconds)

def generate_traffic_data():
    """Generate synthetic network traffic data."""
    logger.info(f"Generating {NUM_SAMPLES} samples of network traffic data...")
    
    # Create lists to hold the data
    timestamps = []
    source_ips = []
    dest_ips = []
    protocols = []
    source_ports = []
    dest_ports = []
    packet_sizes = []
    packet_counts = []
    byte_counts = []
    
    # Generate the data
    for _ in range(NUM_SAMPLES):
        # Generate timestamp
        timestamp = generate_timestamp()
        timestamps.append(timestamp)
        
        # Generate source and destination IPs
        source_ip = generate_ip()
        dest_ip = generate_ip()
        while dest_ip == source_ip:
            dest_ip = generate_ip()
        source_ips.append(source_ip)
        dest_ips.append(dest_ip)
        
        # Generate protocol
        protocol = random.choice(PROTOCOLS)
        protocols.append(protocol)
        
        # Generate source and destination ports
        source_port = random.choice(PORTS)
        dest_port = random.choice(PORTS)
        source_ports.append(source_port)
        dest_ports.append(dest_port)
        
        # Generate packet size (bytes)
        # Use a distribution that might exhibit 18/82 patterns
        if random.random() < 0.18:
            packet_size = random.randint(64, 500)  # Smaller packets
        else:
            packet_size = random.randint(501, 1500)  # Larger packets
        packet_sizes.append(packet_size)
        
        # Generate packet count
        # Use a distribution that might exhibit Pi relationships
        packet_count = int(np.random.exponential(scale=10)) + 1
        packet_counts.append(packet_count)
        
        # Generate byte count
        byte_count = packet_size * packet_count
        byte_counts.append(byte_count)
    
    # Create a DataFrame
    df = pd.DataFrame({
        'timestamp': timestamps,
        'source_ip': source_ips,
        'dest_ip': dest_ips,
        'protocol': protocols,
        'source_port': source_ports,
        'dest_port': dest_ports,
        'packet_size': packet_sizes,
        'packet_count': packet_counts,
        'byte_count': byte_counts
    })
    
    # Sort by timestamp
    df = df.sort_values('timestamp')
    
    # Convert timestamp to string
    df['timestamp'] = df['timestamp'].dt.strftime('%Y-%m-%d %H:%M:%S')
    
    logger.info(f"Generated {len(df)} samples of network traffic data.")
    
    return df

def save_traffic_data(df):
    """Save the traffic data to a CSV file."""
    file_path = os.path.join(SAMPLE_DIR, "sample_traffic.csv")
    logger.info(f"Saving traffic data to {file_path}...")
    
    df.to_csv(file_path, index=False)
    
    logger.info(f"Traffic data saved to {file_path}.")
    
    return file_path

def main():
    """Main function to generate and save sample network traffic data."""
    logger.info("Starting sample network traffic data generation...")
    
    # Generate the data
    df = generate_traffic_data()
    
    # Save the data
    file_path = save_traffic_data(df)
    
    # Display summary statistics
    logger.info("\n=== Sample Network Traffic Data Summary ===")
    logger.info(f"Total records: {len(df)}")
    logger.info(f"Date range: {START_DATE.strftime('%Y-%m-%d')} to {END_DATE.strftime('%Y-%m-%d')}")
    logger.info(f"Number of unique source IPs: {df['source_ip'].nunique()}")
    logger.info(f"Number of unique destination IPs: {df['dest_ip'].nunique()}")
    logger.info(f"Number of unique protocols: {df['protocol'].nunique()}")
    logger.info(f"Average packet size: {df['packet_size'].mean():.2f} bytes")
    logger.info(f"Average packet count: {df['packet_count'].mean():.2f}")
    logger.info(f"Average byte count: {df['byte_count'].mean():.2f} bytes")
    logger.info(f"Data saved to: {file_path}")
    
    return df

if __name__ == "__main__":
    main()
