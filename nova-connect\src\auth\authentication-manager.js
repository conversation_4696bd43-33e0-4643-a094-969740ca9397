/**
 * NovaFuse Universal API Connector Authentication Manager
 *
 * This module handles authentication for API connectors, supporting various
 * authentication methods including API Key, Basic Auth, OAuth2, JWT, and AWS Signature V4.
 */

const axios = require('axios');
const qs = require('querystring');
const crypto = require('crypto');
// Note: In production, uncomment the following line and install the dependency
// const AWS = require('aws-sdk');
const credentialService = require('../services/credential-service');
const { createLogger } = require('../utils/logger');

const logger = createLogger('authentication-manager');

class AuthenticationManager {
  constructor() {
    this.authHandlers = {
      'API_KEY': this.handleApiKeyAuth.bind(this),
      'BASIC': this.handleBasicAuth.bind(this),
      'OAUTH2': this.handleOAuth2Auth.bind(this),
      'JWT': this.handleJwtAuth.bind(this),
      'AWS_SIG_V4': this.handleAwsSigV4Auth.bind(this),
      'CUSTOM': this.handleCustomAuth.bind(this),
      'NONE': this.handleNoAuth.bind(this)
    };
  }

  /**
   * Initialize the authentication manager
   */
  async initialize() {
    logger.info('Authentication Manager initialized');
    return true;
  }

  /**
   * Store credentials securely
   *
   * @param {string} connectorId - The ID of the connector
   * @param {Object} credentials - The credentials to store
   * @param {string} ownerId - The ID of the owner
   * @param {string} name - The name of the credential
   * @param {string} description - The description of the credential
   * @returns {Promise<string>} - The credential ID
   */
  async storeCredentials(connectorId, credentials, ownerId, name, description = '') {
    try {
      logger.debug(`Storing credentials for connector: ${connectorId}`);
      return await credentialService.storeCredentials(connectorId, credentials, ownerId, name);
    } catch (error) {
      logger.error(`Error storing credentials for connector ${connectorId}:`, { error });
      throw error;
    }
  }

  /**
   * Retrieve credentials
   *
   * @param {string} credentialId - The ID of the credentials to retrieve
   * @returns {Promise<Object>} - The decrypted credentials
   */
  async getCredentials(credentialId) {
    try {
      logger.debug(`Getting credentials: ${credentialId}`);
      return await credentialService.getCredentials(credentialId);
    } catch (error) {
      logger.error(`Error getting credentials ${credentialId}:`, { error });
      throw error;
    }
  }

  /**
   * Delete credentials
   *
   * @param {string} credentialId - The ID of the credentials to delete
   * @returns {Promise<boolean>} - Whether the deletion was successful
   */
  async deleteCredentials(credentialId) {
    try {
      logger.debug(`Deleting credentials: ${credentialId}`);
      return await credentialService.deleteCredentials(credentialId);
    } catch (error) {
      logger.error(`Error deleting credentials ${credentialId}:`, { error });
      throw error;
    }
  }

  /**
   * Authenticate a request based on the connector's authentication configuration
   *
   * @param {Object} connector - The connector configuration
   * @param {Object} credentials - The authentication credentials
   * @param {Object} request - The request to authenticate
   * @returns {Promise<Object>} - The authenticated request
   */
  async authenticateRequest(connector, credentials, request) {
    try {
      const authType = connector.authentication.type;

      if (!this.authHandlers[authType]) {
        throw new Error(`Unsupported authentication type: ${authType}`);
      }

      logger.debug(`Authenticating request with ${authType}`);

      return await this.authHandlers[authType](connector, credentials, request);
    } catch (error) {
      logger.error('Error authenticating request:', error);
      throw error;
    }
  }

  /**
   * Test a connection using the provided credentials
   *
   * @param {Object} connector - The connector configuration
   * @param {Object} credentials - The authentication credentials
   * @returns {Promise<Object>} - The test result
   */
  async testConnection(connector, credentials) {
    try {
      const testConfig = connector.authentication.testConnection;

      if (!testConfig) {
        throw new Error('No test connection configuration found');
      }

      logger.debug(`Testing connection for connector: ${connector.id}`);

      // Build the test request
      const request = {
        method: testConfig.method,
        url: `${connector.configuration.baseUrl}${testConfig.endpoint}`,
        headers: { ...connector.configuration.headers }
      };

      // Authenticate the request
      const authenticatedRequest = await this.authenticateRequest(connector, credentials, request);

      // Send the request
      const response = await axios(authenticatedRequest);

      // Check if the response matches the expected pattern
      if (testConfig.expectedResponse) {
        if (testConfig.expectedResponse.status && response.status !== testConfig.expectedResponse.status) {
          return {
            success: false,
            message: `Expected status ${testConfig.expectedResponse.status}, got ${response.status}`
          };
        }

        if (testConfig.expectedResponse.body) {
          // TODO: Implement response body validation
        }
      }

      logger.info(`Connection test successful for connector: ${connector.id}`);

      return {
        success: true,
        message: 'Connection test successful',
        data: {
          status: response.status,
          statusText: response.statusText
        }
      };
    } catch (error) {
      logger.error(`Connection test failed for connector ${connector.id}:`, error);

      return {
        success: false,
        message: `Connection test failed: ${error.message}`,
        error: error.response ? {
          status: error.response.status,
          statusText: error.response.statusText,
          data: error.response.data
        } : error
      };
    }
  }

  /**
   * Handle API Key authentication
   *
   * @param {Object} connector - The connector configuration
   * @param {Object} credentials - The authentication credentials
   * @param {Object} request - The request to authenticate
   * @returns {Object} - The authenticated request
   */
  handleApiKeyAuth(connector, credentials, request) {
    const authConfig = connector.authentication;
    const fields = authConfig.fields;

    // Determine where to place the API key (header, query, etc.)
    const placement = authConfig.placement || 'header';
    const paramName = authConfig.paramName || 'x-api-key';

    // Find the API key field
    const apiKeyField = Object.keys(fields).find(key =>
      key.includes('key') || key.includes('token') || key.includes('api')
    ) || Object.keys(fields)[0]; // Fallback to first field

    const apiKey = credentials[apiKeyField];

    if (!apiKey) {
      throw new Error(`API key not provided (field: ${apiKeyField})`);
    }

    const authenticatedRequest = { ...request };

    if (placement === 'header') {
      authenticatedRequest.headers = {
        ...authenticatedRequest.headers,
        [paramName]: apiKey
      };
    } else if (placement === 'query') {
      const url = new URL(authenticatedRequest.url);
      url.searchParams.append(paramName, apiKey);
      authenticatedRequest.url = url.toString();
    } else if (placement === 'bearer') {
      authenticatedRequest.headers = {
        ...authenticatedRequest.headers,
        'Authorization': `Bearer ${apiKey}`
      };
    }

    return authenticatedRequest;
  }

  /**
   * Handle Basic authentication
   *
   * @param {Object} connector - The connector configuration
   * @param {Object} credentials - The authentication credentials
   * @param {Object} request - The request to authenticate
   * @returns {Object} - The authenticated request
   */
  handleBasicAuth(connector, credentials, request) {
    const authConfig = connector.authentication;
    const fields = authConfig.fields;

    // Get username and password fields
    const usernameField = Object.keys(fields).find(key =>
      key.includes('username') || key.includes('user') || key.includes('email')
    );

    const passwordField = Object.keys(fields).find(key =>
      key.includes('password') || key.includes('secret') || key.includes('token')
    );

    if (!usernameField || !passwordField) {
      throw new Error('Username or password field not found in authentication configuration');
    }

    const username = credentials[usernameField];
    const password = credentials[passwordField];

    if (!username || !password) {
      throw new Error('Username or password not provided');
    }

    // Create Basic Auth header
    const base64Credentials = Buffer.from(`${username}:${password}`).toString('base64');

    return {
      ...request,
      headers: {
        ...request.headers,
        'Authorization': `Basic ${base64Credentials}`
      }
    };
  }

  /**
   * Handle OAuth2 authentication
   *
   * @param {Object} connector - The connector configuration
   * @param {Object} credentials - The authentication credentials
   * @param {Object} request - The request to authenticate
   * @returns {Promise<Object>} - The authenticated request
   */
  async handleOAuth2Auth(connector, credentials, request) {
    const authConfig = connector.authentication;
    const oauth2Config = authConfig.oauth2Config;

    if (!oauth2Config) {
      throw new Error('OAuth2 configuration not found');
    }

    // Check if we have a valid access token
    if (credentials.access_token && credentials.expires_at && new Date(credentials.expires_at) > new Date()) {
      return {
        ...request,
        headers: {
          ...request.headers,
          'Authorization': `Bearer ${credentials.access_token}`
        }
      };
    }

    // If not, get a new access token
    let tokenResponse;

    if (oauth2Config.grantType === 'client_credentials') {
      // Client credentials flow
      tokenResponse = await this.getClientCredentialsToken(oauth2Config, credentials);
    } else if (oauth2Config.grantType === 'password') {
      // Password flow
      tokenResponse = await this.getPasswordToken(oauth2Config, credentials);
    } else if (oauth2Config.grantType === 'authorization_code') {
      // Authorization code flow (requires user interaction, not implemented here)
      throw new Error('Authorization code flow not implemented');
    } else {
      throw new Error(`Unsupported OAuth2 grant type: ${oauth2Config.grantType}`);
    }

    // Update credentials with new token
    credentials.access_token = tokenResponse.access_token;
    credentials.refresh_token = tokenResponse.refresh_token;
    credentials.expires_at = new Date(Date.now() + tokenResponse.expires_in * 1000).toISOString();

    // Return authenticated request
    return {
      ...request,
      headers: {
        ...request.headers,
        'Authorization': `Bearer ${tokenResponse.access_token}`
      }
    };
  }

  /**
   * Get an OAuth2 token using the client credentials grant type
   *
   * @param {Object} oauth2Config - The OAuth2 configuration
   * @param {Object} credentials - The authentication credentials
   * @returns {Promise<Object>} - The token response
   */
  async getClientCredentialsToken(oauth2Config, credentials) {
    const tokenUrl = oauth2Config.tokenUrl;

    // Find client ID and secret fields
    const clientIdField = Object.keys(credentials).find(key =>
      key.includes('client_id') || key.includes('clientId')
    );

    const clientSecretField = Object.keys(credentials).find(key =>
      key.includes('client_secret') || key.includes('clientSecret')
    );

    if (!clientIdField || !clientSecretField) {
      throw new Error('Client ID or Client Secret field not found in credentials');
    }

    const clientId = credentials[clientIdField];
    const clientSecret = credentials[clientSecretField];

    if (!tokenUrl || !clientId || !clientSecret) {
      throw new Error('Missing required OAuth2 parameters');
    }

    const data = {
      grant_type: 'client_credentials',
      client_id: clientId,
      client_secret: clientSecret
    };

    if (oauth2Config.scopes && oauth2Config.scopes.length > 0) {
      data.scope = oauth2Config.scopes.join(' ');
    }

    const response = await axios.post(tokenUrl, qs.stringify(data), {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    });

    return response.data;
  }

  /**
   * Get an OAuth2 token using the password grant type
   *
   * @param {Object} oauth2Config - The OAuth2 configuration
   * @param {Object} credentials - The authentication credentials
   * @returns {Promise<Object>} - The token response
   */
  async getPasswordToken(oauth2Config, credentials) {
    const tokenUrl = oauth2Config.tokenUrl;

    // Find client ID and secret fields
    const clientIdField = Object.keys(credentials).find(key =>
      key.includes('client_id') || key.includes('clientId')
    );

    const clientSecretField = Object.keys(credentials).find(key =>
      key.includes('client_secret') || key.includes('clientSecret')
    );

    // Find username and password fields
    const usernameField = Object.keys(credentials).find(key =>
      key.includes('username') || key.includes('user') || key.includes('email')
    );

    const passwordField = Object.keys(credentials).find(key =>
      key.includes('password') || key.includes('pass')
    );

    if (!clientIdField || !clientSecretField || !usernameField || !passwordField) {
      throw new Error('Required credential fields not found');
    }

    const clientId = credentials[clientIdField];
    const clientSecret = credentials[clientSecretField];
    const username = credentials[usernameField];
    const password = credentials[passwordField];

    if (!tokenUrl || !clientId || !clientSecret || !username || !password) {
      throw new Error('Missing required OAuth2 parameters');
    }

    const data = {
      grant_type: 'password',
      client_id: clientId,
      client_secret: clientSecret,
      username: username,
      password: password
    };

    if (oauth2Config.scopes && oauth2Config.scopes.length > 0) {
      data.scope = oauth2Config.scopes.join(' ');
    }

    const response = await axios.post(tokenUrl, qs.stringify(data), {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    });

    return response.data;
  }

  /**
   * Handle JWT authentication
   *
   * @param {Object} connector - The connector configuration
   * @param {Object} credentials - The authentication credentials
   * @param {Object} request - The request to authenticate
   * @returns {Object} - The authenticated request
   */
  handleJwtAuth(connector, credentials, request) {
    // Find token field
    const tokenField = Object.keys(credentials).find(key =>
      key.includes('token') || key.includes('jwt')
    );

    if (!tokenField) {
      throw new Error('Token field not found in credentials');
    }

    const token = credentials[tokenField];

    if (!token) {
      throw new Error('JWT token not provided');
    }

    return {
      ...request,
      headers: {
        ...request.headers,
        'Authorization': `Bearer ${token}`
      }
    };
  }

  /**
   * Handle AWS Signature V4 authentication
   *
   * @param {Object} connector - The connector configuration
   * @param {Object} credentials - The authentication credentials
   * @param {Object} request - The request to authenticate
   * @returns {Object} - The authenticated request with AWS Signature V4
   */
  handleAwsSigV4Auth(connector, credentials, request) {
    // Find AWS credential fields
    const accessKeyIdField = Object.keys(credentials).find(key =>
      key.includes('access_key') || key.includes('accessKey') || key.includes('accessKeyId')
    );

    const secretAccessKeyField = Object.keys(credentials).find(key =>
      key.includes('secret_key') || key.includes('secretKey') || key.includes('secretAccessKey')
    );

    const regionField = Object.keys(credentials).find(key =>
      key.includes('region')
    );

    if (!accessKeyIdField || !secretAccessKeyField) {
      throw new Error('AWS credential fields not found');
    }

    const accessKeyId = credentials[accessKeyIdField];
    const secretAccessKey = credentials[secretAccessKeyField];
    const region = credentials[regionField] || connector.authentication.region || 'us-east-1';
    const service = connector.authentication.service || credentials.service || 'execute-api';

    if (!accessKeyId || !secretAccessKey) {
      throw new Error('Missing required AWS credentials');
    }

    // In production, uncomment the following code and install the AWS SDK dependency
    /*
    // Parse the request URL
    const url = new URL(request.url);
    const path = url.pathname;
    const queryParams = {};
    url.searchParams.forEach((value, key) => {
      queryParams[key] = value;
    });

    // Create AWS credentials
    const awsCredentials = new AWS.Credentials({
      accessKeyId,
      secretAccessKey,
      sessionToken: credentials.sessionToken
    });

    // Create a request signer
    const signer = new AWS.Signers.V4({
      credentials: awsCredentials,
      region,
      service,
      method: request.method,
      path,
      headers: request.headers,
      body: request.data,
      queryParams
    });

    // Sign the request
    const signedRequest = signer.sign();

    // Return the authenticated request
    return {
      ...request,
      headers: {
        ...request.headers,
        ...signedRequest.headers
      }
    };
    */

    // For now, implement a simplified version of AWS Signature V4
    // This is not a complete implementation and should be replaced with the AWS SDK in production
    const timestamp = new Date().toISOString().replace(/[:-]|\.\d{3}/g, '');
    const date = timestamp.substring(0, 8);

    // Create canonical request headers
    const headers = {
      ...request.headers,
      'X-Amz-Date': timestamp,
      'Host': new URL(request.url).host
    };

    if (credentials.sessionToken) {
      headers['X-Amz-Security-Token'] = credentials.sessionToken;
    }

    // Return the authenticated request with basic headers
    // Note: This is not a complete AWS Signature V4 implementation
    return {
      ...request,
      headers: {
        ...headers,
        'X-Amz-Content-Sha256': 'UNSIGNED-PAYLOAD',
        'Authorization': `AWS4-HMAC-SHA256 Credential=${accessKeyId}/${date}/${region}/${service}/aws4_request, SignedHeaders=host;x-amz-date, Signature=SIMPLIFIED-SIGNATURE-FOR-DEVELOPMENT`
      }
    };
  }

  /**
   * Handle custom authentication
   *
   * @param {Object} connector - The connector configuration
   * @param {Object} credentials - The authentication credentials
   * @param {Object} request - The request to authenticate
   * @returns {Object} - The authenticated request
   */
  handleCustomAuth(connector, credentials, request) {
    // Custom authentication is implemented by the connector
    if (connector.authentication.handler) {
      return connector.authentication.handler(credentials, request);
    }

    // If no handler is provided, return the request as is
    return request;
  }

  /**
   * Handle no authentication
   *
   * @param {Object} connector - The connector configuration (unused)
   * @param {Object} credentials - The authentication credentials (unused)
   * @param {Object} request - The request to authenticate
   * @returns {Object} - The request without authentication
   */
  handleNoAuth(_connector, _credentials, request) {
    // No authentication needed
    return request;
  }
}

// Create and export a singleton instance
const authenticationManager = new AuthenticationManager();

module.exports = authenticationManager;
