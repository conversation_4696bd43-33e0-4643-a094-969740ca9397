import React from 'react';
import Head from 'next/head';
import Navigation from './Navigation';
import FloatingNovaConcierge from './FloatingNovaConcierge';
import { useNavigation } from './NavigationContext';

const MainLayout = ({ children, title = 'NovaFuse API Superstore' }) => {
  return (
    <div className="min-h-screen bg-primary text-white">
      <Head>
        <title>{title}</title>
        <meta name="description" content="NovaFuse API Superstore - The marketplace for GRC APIs" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <Navigation />

      <main className="container mx-auto px-4 py-8">
        {children}
      </main>

      <footer className="bg-secondary py-8">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="mb-4 md:mb-0">
              <p className="text-gray-400">© 2025 NovaFuse. All rights reserved.</p>
            </div>
            <div className="flex space-x-6">
              <a href="#" className="text-gray-400 hover:text-white">Terms</a>
              <a href="#" className="text-gray-400 hover:text-white">Privacy</a>
              <a href="#" className="text-gray-400 hover:text-white">Contact</a>
            </div>
          </div>
        </div>
      </footer>

      <FloatingNovaConcierge />
    </div>
  );
};

export default MainLayout;
