# NovaConnect UAC API Reference

This document provides a comprehensive reference for the NovaConnect Universal API Connector (UAC) REST API.

## Overview

The NovaConnect UAC API provides a flexible and powerful way to connect to any API, normalize data, and integrate with your existing systems. The API is organized around REST principles and uses standard HTTP methods, status codes, and authentication.

## Base URL

The base URL for all API endpoints is:

```
https://api.novafuse.io/api
```

For staging environment:

```
https://api-staging.novafuse.io/api
```

## Authentication

NovaConnect UAC uses API keys for authentication. You can obtain an API key from the NovaConnect dashboard.

To authenticate, include your API key in the `X-API-Key` header:

```
X-API-Key: your-api-key
```

Alternatively, you can use JWT authentication by including a JWT token in the `Authorization` header:

```
Authorization: Bearer your-jwt-token
```

## Rate Limiting

NovaConnect UAC implements rate limiting to prevent abuse. The default rate limit is 100 requests per minute per API key. If you exceed the rate limit, you will receive a `429 Too Many Requests` response.

Rate limit headers are included in all responses:

```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 99
X-RateLimit-Reset: 1619712000
```

## Error Handling

NovaConnect UAC uses standard HTTP status codes to indicate the success or failure of an API request. In general:

- 2xx: Success
- 4xx: Client error
- 5xx: Server error

Error responses include a JSON object with the following fields:

```json
{
  "error": {
    "id": "550e8400-e29b-41d4-a716-************",
    "type": "validation_error",
    "message": "Invalid connector configuration",
    "status": 400,
    "validation": [
      {
        "path": "config.authentication.type",
        "message": "Authentication type is required"
      }
    ]
  }
}
```

## Pagination

For endpoints that return lists of items, NovaConnect UAC supports pagination using the `page` and `limit` query parameters:

```
GET /api/connectors?page=2&limit=10
```

Pagination information is included in the response:

```json
{
  "data": [...],
  "pagination": {
    "page": 2,
    "limit": 10,
    "totalItems": 42,
    "totalPages": 5
  }
}
```

## Filtering

For endpoints that return lists of items, NovaConnect UAC supports filtering using query parameters:

```
GET /api/connectors?type=http&status=active
```

## Sorting

For endpoints that return lists of items, NovaConnect UAC supports sorting using the `sort` query parameter:

```
GET /api/connectors?sort=name:asc,createdAt:desc
```

## Endpoints

### Health Check

#### GET /health

Check the health of the NovaConnect UAC API.

**Response**

```json
{
  "status": "ok",
  "version": "1.0.0",
  "uptime": 3600,
  "timestamp": "2023-01-01T00:00:00Z"
}
```

### Connectors

#### GET /connectors

Get a list of connectors.

**Query Parameters**

| Parameter | Type | Description |
|-----------|------|-------------|
| page | integer | Page number (default: 1) |
| limit | integer | Items per page (default: 20) |
| type | string | Filter by connector type |
| status | string | Filter by connector status |
| sort | string | Sort by field (format: field:direction) |

**Response**

```json
{
  "data": [
    {
      "id": "550e8400-e29b-41d4-a716-************",
      "name": "AWS Security Hub",
      "type": "aws",
      "description": "AWS Security Hub connector for security findings",
      "status": "active",
      "config": {
        "region": "us-east-1",
        "service": "securityhub",
        "action": "getFindings",
        "authentication": {
          "type": "iam_role",
          "role_arn": "arn:aws:iam::************:role/NovaConnectRole"
        }
      },
      "createdAt": "2023-01-01T00:00:00Z",
      "updatedAt": "2023-01-01T00:00:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "totalItems": 1,
    "totalPages": 1
  }
}
```

#### GET /connectors/:id

Get a connector by ID.

**Path Parameters**

| Parameter | Type | Description |
|-----------|------|-------------|
| id | string | Connector ID |

**Response**

```json
{
  "id": "550e8400-e29b-41d4-a716-************",
  "name": "AWS Security Hub",
  "type": "aws",
  "description": "AWS Security Hub connector for security findings",
  "status": "active",
  "config": {
    "region": "us-east-1",
    "service": "securityhub",
    "action": "getFindings",
    "authentication": {
      "type": "iam_role",
      "role_arn": "arn:aws:iam::************:role/NovaConnectRole"
    }
  },
  "schema": {
    "input": {
      "type": "object",
      "properties": {
        "Filters": {
          "type": "object"
        },
        "MaxResults": {
          "type": "number"
        }
      }
    },
    "output": {
      "type": "object",
      "properties": {
        "Findings": {
          "type": "array"
        }
      }
    }
  },
  "createdAt": "2023-01-01T00:00:00Z",
  "updatedAt": "2023-01-01T00:00:00Z"
}
```

#### POST /connectors

Create a new connector.

**Request Body**

```json
{
  "name": "AWS Security Hub",
  "type": "aws",
  "description": "AWS Security Hub connector for security findings",
  "config": {
    "region": "us-east-1",
    "service": "securityhub",
    "action": "getFindings",
    "authentication": {
      "type": "iam_role",
      "role_arn": "arn:aws:iam::************:role/NovaConnectRole"
    }
  },
  "schema": {
    "input": {
      "type": "object",
      "properties": {
        "Filters": {
          "type": "object"
        },
        "MaxResults": {
          "type": "number"
        }
      }
    },
    "output": {
      "type": "object",
      "properties": {
        "Findings": {
          "type": "array"
        }
      }
    }
  }
}
```

**Response**

```json
{
  "id": "550e8400-e29b-41d4-a716-************",
  "name": "AWS Security Hub",
  "type": "aws",
  "description": "AWS Security Hub connector for security findings",
  "status": "active",
  "config": {
    "region": "us-east-1",
    "service": "securityhub",
    "action": "getFindings",
    "authentication": {
      "type": "iam_role",
      "role_arn": "arn:aws:iam::************:role/NovaConnectRole"
    }
  },
  "schema": {
    "input": {
      "type": "object",
      "properties": {
        "Filters": {
          "type": "object"
        },
        "MaxResults": {
          "type": "number"
        }
      }
    },
    "output": {
      "type": "object",
      "properties": {
        "Findings": {
          "type": "array"
        }
      }
    }
  },
  "createdAt": "2023-01-01T00:00:00Z",
  "updatedAt": "2023-01-01T00:00:00Z"
}
```

#### PUT /connectors/:id

Update a connector.

**Path Parameters**

| Parameter | Type | Description |
|-----------|------|-------------|
| id | string | Connector ID |

**Request Body**

```json
{
  "name": "AWS Security Hub Updated",
  "description": "Updated AWS Security Hub connector for security findings",
  "config": {
    "region": "us-west-2",
    "service": "securityhub",
    "action": "getFindings",
    "authentication": {
      "type": "iam_role",
      "role_arn": "arn:aws:iam::************:role/NovaConnectRole"
    }
  }
}
```

**Response**

```json
{
  "id": "550e8400-e29b-41d4-a716-************",
  "name": "AWS Security Hub Updated",
  "type": "aws",
  "description": "Updated AWS Security Hub connector for security findings",
  "status": "active",
  "config": {
    "region": "us-west-2",
    "service": "securityhub",
    "action": "getFindings",
    "authentication": {
      "type": "iam_role",
      "role_arn": "arn:aws:iam::************:role/NovaConnectRole"
    }
  },
  "schema": {
    "input": {
      "type": "object",
      "properties": {
        "Filters": {
          "type": "object"
        },
        "MaxResults": {
          "type": "number"
        }
      }
    },
    "output": {
      "type": "object",
      "properties": {
        "Findings": {
          "type": "array"
        }
      }
    }
  },
  "createdAt": "2023-01-01T00:00:00Z",
  "updatedAt": "2023-01-01T00:00:00Z"
}
```

#### DELETE /connectors/:id

Delete a connector.

**Path Parameters**

| Parameter | Type | Description |
|-----------|------|-------------|
| id | string | Connector ID |

**Response**

```
204 No Content
```

#### POST /connectors/:id/test

Test a connector.

**Path Parameters**

| Parameter | Type | Description |
|-----------|------|-------------|
| id | string | Connector ID |

**Request Body**

```json
{
  "testData": {
    "Filters": {
      "SeverityLabel": [
        {
          "Value": "HIGH",
          "Comparison": "EQUALS"
        }
      ],
      "CreatedAt": [
        {
          "DateRange": {
            "Value": 30,
            "Unit": "DAYS"
          }
        }
      ]
    },
    "MaxResults": 10
  }
}
```

**Response**

```json
{
  "success": true,
  "result": {
    "Findings": [
      {
        "Id": "arn:aws:securityhub:us-east-1:************:finding/********-1234-1234-1234-************",
        "ProductArn": "arn:aws:securityhub:us-east-1::product/aws/securityhub",
        "ProductName": "Security Hub",
        "CompanyName": "AWS",
        "Region": "us-east-1",
        "GeneratorId": "aws-security-hub",
        "AwsAccountId": "************",
        "Types": [
          "Software and Configuration Checks/Vulnerabilities/CVE"
        ],
        "Severity": {
          "Label": "HIGH",
          "Normalized": 70
        },
        "Title": "CVE-2023-12345 - Critical vulnerability in package",
        "Description": "A critical vulnerability was found in package that could allow remote code execution."
      }
    ]
  },
  "duration": 123
}
```

#### POST /connectors/:id/execute

Execute a connector.

**Path Parameters**

| Parameter | Type | Description |
|-----------|------|-------------|
| id | string | Connector ID |

**Request Body**

```json
{
  "Filters": {
    "SeverityLabel": [
      {
        "Value": "HIGH",
        "Comparison": "EQUALS"
      }
    ],
    "CreatedAt": [
      {
        "DateRange": {
          "Value": 30,
          "Unit": "DAYS"
        }
      }
    ]
  },
  "MaxResults": 10
}
```

**Response**

```json
{
  "result": {
    "Findings": [
      {
        "Id": "arn:aws:securityhub:us-east-1:************:finding/********-1234-1234-1234-************",
        "ProductArn": "arn:aws:securityhub:us-east-1::product/aws/securityhub",
        "ProductName": "Security Hub",
        "CompanyName": "AWS",
        "Region": "us-east-1",
        "GeneratorId": "aws-security-hub",
        "AwsAccountId": "************",
        "Types": [
          "Software and Configuration Checks/Vulnerabilities/CVE"
        ],
        "Severity": {
          "Label": "HIGH",
          "Normalized": 70
        },
        "Title": "CVE-2023-12345 - Critical vulnerability in package",
        "Description": "A critical vulnerability was found in package that could allow remote code execution."
      }
    ]
  },
  "duration": 123
}
```

### Data Normalization

#### POST /normalize

Normalize data.

**Request Body**

```json
{
  "source": "aws",
  "type": "finding",
  "data": {
    "Id": "arn:aws:securityhub:us-east-1:************:finding/********-1234-1234-1234-************",
    "ProductArn": "arn:aws:securityhub:us-east-1::product/aws/securityhub",
    "ProductName": "Security Hub",
    "CompanyName": "AWS",
    "Region": "us-east-1",
    "GeneratorId": "aws-security-hub",
    "AwsAccountId": "************",
    "Types": [
      "Software and Configuration Checks/Vulnerabilities/CVE"
    ],
    "Severity": {
      "Label": "HIGH",
      "Normalized": 70
    },
    "Title": "CVE-2023-12345 - Critical vulnerability in package",
    "Description": "A critical vulnerability was found in package that could allow remote code execution."
  }
}
```

**Response**

```json
{
  "normalized": {
    "id": "arn:aws:securityhub:us-east-1:************:finding/********-1234-1234-1234-************",
    "source": "aws",
    "sourceType": "finding",
    "title": "CVE-2023-12345 - Critical vulnerability in package",
    "description": "A critical vulnerability was found in package that could allow remote code execution.",
    "severity": "high",
    "severityScore": 70,
    "type": "vulnerability",
    "subType": "cve",
    "region": "us-east-1",
    "accountId": "************",
    "createdAt": null,
    "updatedAt": null,
    "rawData": {
      "Id": "arn:aws:securityhub:us-east-1:************:finding/********-1234-1234-1234-************",
      "ProductArn": "arn:aws:securityhub:us-east-1::product/aws/securityhub",
      "ProductName": "Security Hub",
      "CompanyName": "AWS",
      "Region": "us-east-1",
      "GeneratorId": "aws-security-hub",
      "AwsAccountId": "************",
      "Types": [
        "Software and Configuration Checks/Vulnerabilities/CVE"
      ],
      "Severity": {
        "Label": "HIGH",
        "Normalized": 70
      },
      "Title": "CVE-2023-12345 - Critical vulnerability in package",
      "Description": "A critical vulnerability was found in package that could allow remote code execution."
    }
  },
  "duration": 5
}
```

#### POST /normalize/batch

Normalize multiple data items.

**Request Body**

```json
{
  "items": [
    {
      "source": "aws",
      "type": "finding",
      "data": {
        "Id": "arn:aws:securityhub:us-east-1:************:finding/********-1234-1234-1234-************",
        "ProductArn": "arn:aws:securityhub:us-east-1::product/aws/securityhub",
        "ProductName": "Security Hub",
        "CompanyName": "AWS",
        "Region": "us-east-1",
        "GeneratorId": "aws-security-hub",
        "AwsAccountId": "************",
        "Types": [
          "Software and Configuration Checks/Vulnerabilities/CVE"
        ],
        "Severity": {
          "Label": "HIGH",
          "Normalized": 70
        },
        "Title": "CVE-2023-12345 - Critical vulnerability in package",
        "Description": "A critical vulnerability was found in package that could allow remote code execution."
      }
    },
    {
      "source": "azure",
      "type": "alert",
      "data": {
        "id": "/subscriptions/subscription-id/providers/Microsoft.Security/alerts/alert-id",
        "name": "alert-id",
        "type": "Microsoft.Security/alerts",
        "properties": {
          "alertDisplayName": "Suspicious process execution detected",
          "alertType": "Process_Execution",
          "compromisedEntity": "vm-name",
          "description": "A suspicious process execution was detected on the virtual machine.",
          "detectionTime": "2023-01-01T00:00:00Z",
          "reportedTimeUtc": "2023-01-01T00:00:00Z",
          "severity": "High",
          "status": "Active"
        }
      }
    }
  ]
}
```

**Response**

```json
{
  "normalized": [
    {
      "id": "arn:aws:securityhub:us-east-1:************:finding/********-1234-1234-1234-************",
      "source": "aws",
      "sourceType": "finding",
      "title": "CVE-2023-12345 - Critical vulnerability in package",
      "description": "A critical vulnerability was found in package that could allow remote code execution.",
      "severity": "high",
      "severityScore": 70,
      "type": "vulnerability",
      "subType": "cve",
      "region": "us-east-1",
      "accountId": "************",
      "createdAt": null,
      "updatedAt": null,
      "rawData": {
        "Id": "arn:aws:securityhub:us-east-1:************:finding/********-1234-1234-1234-************",
        "ProductArn": "arn:aws:securityhub:us-east-1::product/aws/securityhub",
        "ProductName": "Security Hub",
        "CompanyName": "AWS",
        "Region": "us-east-1",
        "GeneratorId": "aws-security-hub",
        "AwsAccountId": "************",
        "Types": [
          "Software and Configuration Checks/Vulnerabilities/CVE"
        ],
        "Severity": {
          "Label": "HIGH",
          "Normalized": 70
        },
        "Title": "CVE-2023-12345 - Critical vulnerability in package",
        "Description": "A critical vulnerability was found in package that could allow remote code execution."
      }
    },
    {
      "id": "/subscriptions/subscription-id/providers/Microsoft.Security/alerts/alert-id",
      "source": "azure",
      "sourceType": "alert",
      "title": "Suspicious process execution detected",
      "description": "A suspicious process execution was detected on the virtual machine.",
      "severity": "high",
      "severityScore": 70,
      "type": "threat",
      "subType": "process_execution",
      "region": null,
      "accountId": "subscription-id",
      "createdAt": "2023-01-01T00:00:00Z",
      "updatedAt": "2023-01-01T00:00:00Z",
      "rawData": {
        "id": "/subscriptions/subscription-id/providers/Microsoft.Security/alerts/alert-id",
        "name": "alert-id",
        "type": "Microsoft.Security/alerts",
        "properties": {
          "alertDisplayName": "Suspicious process execution detected",
          "alertType": "Process_Execution",
          "compromisedEntity": "vm-name",
          "description": "A suspicious process execution was detected on the virtual machine.",
          "detectionTime": "2023-01-01T00:00:00Z",
          "reportedTimeUtc": "2023-01-01T00:00:00Z",
          "severity": "High",
          "status": "Active"
        }
      }
    }
  ],
  "duration": 10
}
```

### Subscription and Features

#### GET /subscription

Get the current user's subscription details.

**Response**

```json
{
  "userId": "user-id",
  "tierId": "core",
  "tierName": "NovaConnect Core",
  "tierDescription": "Basic functionality for small projects",
  "features": [
    {
      "id": "core.basic_connectors",
      "name": "Basic Connectors",
      "description": "Access to basic connectors",
      "category": "core"
    },
    {
      "id": "core.manual_execution",
      "name": "Manual Execution",
      "description": "Manual execution of connectors",
      "category": "core"
    }
  ],
  "limits": {
    "core.manual_execution": {
      "operations_per_day": 100
    }
  },
  "usage": {
    "core.manual_execution": {
      "operations_per_day": 42
    }
  },
  "created": "2023-01-01T00:00:00Z",
  "updated": "2023-01-01T00:00:00Z"
}
```

#### GET /features

Get the current user's available features.

**Response**

```json
{
  "features": [
    {
      "id": "core.basic_connectors",
      "name": "Basic Connectors",
      "description": "Access to basic connectors",
      "category": "core",
      "enabled": true
    },
    {
      "id": "core.manual_execution",
      "name": "Manual Execution",
      "description": "Manual execution of connectors",
      "category": "core",
      "enabled": true,
      "limits": {
        "operations_per_day": 100
      },
      "usage": {
        "operations_per_day": 42
      }
    },
    {
      "id": "workflow.scheduled",
      "name": "Scheduled Execution",
      "description": "Scheduled execution of connectors",
      "category": "workflow",
      "enabled": false
    }
  ]
}
```

#### GET /features/usage

Get the current user's feature usage.

**Query Parameters**

| Parameter | Type | Description |
|-----------|------|-------------|
| featureId | string | Feature ID |
| startDate | string | Start date (ISO 8601) |
| endDate | string | End date (ISO 8601) |

**Response**

```json
{
  "usage": [
    {
      "featureId": "core.manual_execution",
      "date": "2023-01-01",
      "quantity": 42
    },
    {
      "featureId": "core.manual_execution",
      "date": "2023-01-02",
      "quantity": 37
    }
  ],
  "total": 79,
  "limit": 100,
  "remaining": 21
}
```

### System Information

#### GET /metrics

Get system metrics.

**Response**

```json
{
  "uptime": 3600,
  "memory": {
    "rss": ********9,
    "heapTotal": ********9,
    "heapUsed": ********9,
    "external": ********9
  },
  "cpu": {
    "user": ********9,
    "system": ********9
  },
  "requests": {
    "total": 1000,
    "success": 990,
    "error": 10,
    "avgResponseTime": 42
  },
  "connectors": {
    "total": 10,
    "active": 8,
    "inactive": 2
  },
  "executions": {
    "total": 1000,
    "success": 990,
    "error": 10,
    "avgExecutionTime": 123
  }
}
```

#### GET /cluster/health

Get cluster health information.

**Response**

```json
{
  "status": "ok",
  "cluster": {
    "mode": "cluster",
    "role": "master",
    "workers": [
      {
        "id": "1",
        "uniqueId": "550e8400-e29b-41d4-a716-************",
        "pid": 12345,
        "startTime": 1619712000000,
        "restarts": 0,
        "status": "online"
      },
      {
        "id": "2",
        "uniqueId": "550e8400-e29b-41d4-a716-************",
        "pid": 12346,
        "startTime": 1619712000000,
        "restarts": 0,
        "status": "online"
      }
    ],
    "metrics": {
      "startTime": 1619712000000,
      "totalRequests": 1000,
      "activeRequests": 10,
      "totalErrors": 10,
      "restarts": 0
    },
    "uptime": 3600000,
    "cpus": 4,
    "maxWorkers": 4
  }
}
```

#### GET /cache/metrics

Get cache metrics.

**Response**

```json
{
  "status": "ok",
  "cache": {
    "hits": 1000,
    "misses": 100,
    "sets": 1100,
    "deletes": 50,
    "errors": 0,
    "hitRatio": 0.9090909090909091,
    "provider": "redis",
    "strategy": "simple",
    "enabled": true
  }
}
```

## SDKs and Client Libraries

NovaConnect UAC provides SDKs and client libraries for various programming languages:

- [JavaScript/TypeScript](https://github.com/novafuse/novafuse-js)
- [Python](https://github.com/novafuse/novafuse-python)
- [Java](https://github.com/novafuse/novafuse-java)
- [Go](https://github.com/novafuse/novafuse-go)

## Webhooks

NovaConnect UAC supports webhooks for event notifications. You can configure webhooks in the NovaConnect dashboard.

Webhook events are sent as HTTP POST requests with the following format:

```json
{
  "id": "550e8400-e29b-41d4-a716-************",
  "type": "connector.execution.completed",
  "createdAt": "2023-01-01T00:00:00Z",
  "data": {
    "connectorId": "550e8400-e29b-41d4-a716-************",
    "executionId": "550e8400-e29b-41d4-a716-************",
    "status": "success",
    "duration": 123
  }
}
```

## Conclusion

This API reference provides a comprehensive guide to the NovaConnect UAC REST API. For more information, please refer to the [NovaConnect documentation](https://docs.novafuse.io).
