/**
 * Brute Force Protection Service Tests
 */

const BruteForceProtectionService = require('../../../api/services/BruteForceProtectionService');
const { BruteForceError } = require('../../../api/utils/errors');
const fs = require('fs').promises;
const path = require('path');

// Mock fs.promises
jest.mock('fs', () => ({
  promises: {
    mkdir: jest.fn().mockResolvedValue(undefined),
    readFile: jest.fn(),
    writeFile: jest.fn().mockResolvedValue(undefined)
  }
}));

// Mock Date.now to control time
const mockDateNow = jest.spyOn(Date, 'now');

describe('BruteForceProtectionService', () => {
  let bruteForceService;
  const testDataDir = path.join(__dirname, 'test-data');
  
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Reset Date.now mock
    mockDateNow.mockReset();
    mockDateNow.mockImplementation(() => 1000); // Mock current time
    
    // Create a new instance for each test
    bruteForceService = new BruteForceProtectionService(testDataDir);
    
    // Override the config for testing
    bruteForceService.config = {
      maxAttempts: 3,
      windowMs: 15 * 60 * 1000, // 15 minutes
      blockDuration: 30 * 60 * 1000 // 30 minutes
    };
  });
  
  describe('constructor', () => {
    it('should initialize with the correct data directory', () => {
      expect(bruteForceService.dataDir).toBe(testDataDir);
      expect(bruteForceService.attemptsFile).toBe(path.join(testDataDir, 'login_attempts.json'));
    });
    
    it('should call ensureDataDir', () => {
      expect(fs.mkdir).toHaveBeenCalledWith(testDataDir, { recursive: true });
    });
  });
  
  describe('loadAttempts', () => {
    it('should load attempts from file', async () => {
      const mockAttempts = {
        'test-user': {
          count: 1,
          firstAttempt: 500,
          lastAttempt: 500,
          blocked: false,
          blockedUntil: null
        }
      };
      
      fs.readFile.mockResolvedValueOnce(JSON.stringify(mockAttempts));
      
      const attempts = await bruteForceService.loadAttempts();
      
      expect(fs.readFile).toHaveBeenCalledWith(bruteForceService.attemptsFile, 'utf8');
      expect(attempts).toEqual(mockAttempts);
    });
    
    it('should return empty object if file does not exist', async () => {
      const error = new Error('File not found');
      error.code = 'ENOENT';
      fs.readFile.mockRejectedValueOnce(error);
      
      const attempts = await bruteForceService.loadAttempts();
      
      expect(attempts).toEqual({});
    });
    
    it('should throw error if file read fails for other reasons', async () => {
      const error = new Error('Permission denied');
      fs.readFile.mockRejectedValueOnce(error);
      
      await expect(bruteForceService.loadAttempts()).rejects.toThrow('Permission denied');
    });
  });
  
  describe('saveAttempts', () => {
    it('should save attempts to file', async () => {
      const attempts = {
        'test-user': {
          count: 1,
          firstAttempt: 500,
          lastAttempt: 500,
          blocked: false,
          blockedUntil: null
        }
      };
      
      await bruteForceService.saveAttempts(attempts);
      
      expect(fs.writeFile).toHaveBeenCalledWith(
        bruteForceService.attemptsFile,
        JSON.stringify(attempts, null, 2)
      );
    });
    
    it('should throw error if file write fails', async () => {
      const error = new Error('Permission denied');
      fs.writeFile.mockRejectedValueOnce(error);
      
      await expect(bruteForceService.saveAttempts({})).rejects.toThrow('Permission denied');
    });
  });
  
  describe('recordFailedAttempt', () => {
    it('should initialize attempts for new identifier', async () => {
      fs.readFile.mockResolvedValueOnce(JSON.stringify({}));
      
      const result = await bruteForceService.recordFailedAttempt('test-user');
      
      expect(result).toEqual({
        count: 1,
        firstAttempt: 1000,
        lastAttempt: 1000,
        blocked: false,
        blockedUntil: null
      });
      
      expect(fs.writeFile).toHaveBeenCalled();
    });
    
    it('should increment count for existing identifier', async () => {
      const existingAttempts = {
        'test-user': {
          count: 1,
          firstAttempt: 500,
          lastAttempt: 500,
          blocked: false,
          blockedUntil: null
        }
      };
      
      fs.readFile.mockResolvedValueOnce(JSON.stringify(existingAttempts));
      
      const result = await bruteForceService.recordFailedAttempt('test-user');
      
      expect(result).toEqual({
        count: 2,
        firstAttempt: 500,
        lastAttempt: 1000,
        blocked: false,
        blockedUntil: null
      });
      
      expect(fs.writeFile).toHaveBeenCalled();
    });
    
    it('should block identifier after max attempts', async () => {
      const existingAttempts = {
        'test-user': {
          count: 2,
          firstAttempt: 500,
          lastAttempt: 500,
          blocked: false,
          blockedUntil: null
        }
      };
      
      fs.readFile.mockResolvedValueOnce(JSON.stringify(existingAttempts));
      
      const result = await bruteForceService.recordFailedAttempt('test-user');
      
      expect(result).toEqual({
        count: 3,
        firstAttempt: 500,
        lastAttempt: 1000,
        blocked: true,
        blockedUntil: 1000 + bruteForceService.config.blockDuration
      });
      
      expect(fs.writeFile).toHaveBeenCalled();
    });
  });
  
  describe('isBlocked', () => {
    it('should return false for unknown identifier', async () => {
      fs.readFile.mockResolvedValueOnce(JSON.stringify({}));
      
      const result = await bruteForceService.isBlocked('test-user');
      
      expect(result).toBe(false);
    });
    
    it('should return false for non-blocked identifier', async () => {
      const existingAttempts = {
        'test-user': {
          count: 1,
          firstAttempt: 500,
          lastAttempt: 500,
          blocked: false,
          blockedUntil: null
        }
      };
      
      fs.readFile.mockResolvedValueOnce(JSON.stringify(existingAttempts));
      
      const result = await bruteForceService.isBlocked('test-user');
      
      expect(result).toBe(false);
    });
    
    it('should return block info for blocked identifier', async () => {
      const blockedUntil = 1000 + 60000; // 1 minute from now
      const existingAttempts = {
        'test-user': {
          count: 3,
          firstAttempt: 500,
          lastAttempt: 500,
          blocked: true,
          blockedUntil
        }
      };
      
      fs.readFile.mockResolvedValueOnce(JSON.stringify(existingAttempts));
      
      const result = await bruteForceService.isBlocked('test-user');
      
      expect(result).toEqual({
        blocked: true,
        remainingTime: 60,
        attemptsCount: 3,
        maxAttempts: 3
      });
    });
    
    it('should reset block if block duration has expired', async () => {
      const blockedUntil = 500; // Already expired
      const existingAttempts = {
        'test-user': {
          count: 3,
          firstAttempt: 100,
          lastAttempt: 100,
          blocked: true,
          blockedUntil
        }
      };
      
      fs.readFile.mockResolvedValueOnce(JSON.stringify(existingAttempts));
      
      const result = await bruteForceService.isBlocked('test-user');
      
      expect(result).toBe(false);
      expect(fs.writeFile).toHaveBeenCalled();
    });
  });
  
  describe('checkLoginAttempt', () => {
    it('should throw BruteForceError if identifier is blocked', async () => {
      const blockedUntil = 1000 + 60000; // 1 minute from now
      const existingAttempts = {
        'test-user': {
          count: 3,
          firstAttempt: 500,
          lastAttempt: 500,
          blocked: true,
          blockedUntil
        }
      };
      
      fs.readFile.mockResolvedValueOnce(JSON.stringify(existingAttempts));
      
      await expect(bruteForceService.checkLoginAttempt('test-user'))
        .rejects.toThrow(BruteForceError);
    });
    
    it('should return true if identifier is not blocked', async () => {
      fs.readFile.mockResolvedValueOnce(JSON.stringify({}));
      
      const result = await bruteForceService.checkLoginAttempt('test-user');
      
      expect(result).toBe(true);
    });
  });
});
