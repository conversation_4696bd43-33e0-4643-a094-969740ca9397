/**
 * Transition Component
 * 
 * A component for animating elements entering and leaving the DOM.
 */

import React, { useState, useEffect, useRef } from 'react';
import PropTypes from 'prop-types';
import { createAnimationFromPreset } from '../animation/AnimationUtils';

/**
 * Transition component
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components
 * @param {boolean} props.in - Whether the component is in or out
 * @param {string|Object} [props.enter='fadeIn'] - Enter animation
 * @param {string|Object} [props.exit='fadeOut'] - Exit animation
 * @param {boolean} [props.mountOnEnter=false] - Whether to mount the component on enter
 * @param {boolean} [props.unmountOnExit=true] - Whether to unmount the component on exit
 * @param {number} [props.timeout=300] - Transition timeout in milliseconds
 * @param {Function} [props.onEnter] - Callback when enter begins
 * @param {Function} [props.onEntering] - Callback when enter is in progress
 * @param {Function} [props.onEntered] - Callback when enter is complete
 * @param {Function} [props.onExit] - Callback when exit begins
 * @param {Function} [props.onExiting] - Callback when exit is in progress
 * @param {Function} [props.onExited] - Callback when exit is complete
 * @param {string} [props.className] - Additional CSS class names
 * @param {Object} [props.style] - Additional inline styles
 * @returns {React.ReactElement|null} Transition component
 */
const Transition = ({
  children,
  in: inProp,
  enter = 'fadeIn',
  exit = 'fadeOut',
  mountOnEnter = false,
  unmountOnExit = true,
  timeout = 300,
  onEnter,
  onEntering,
  onEntered,
  onExit,
  onExiting,
  onExited,
  className = '',
  style = {}
}) => {
  // State
  const [status, setStatus] = useState(() => {
    if (inProp) {
      return mountOnEnter ? 'exited' : 'entered';
    } else {
      return 'exited';
    }
  });
  
  // Refs
  const nodeRef = useRef(null);
  const animationRef = useRef(null);
  const timeoutRef = useRef(null);
  
  // Get animation
  const getAnimation = (type) => {
    const animation = type === 'enter' ? enter : exit;
    
    if (typeof animation === 'string') {
      return createAnimationFromPreset(animation, { duration: timeout });
    } else {
      return {
        keyframes: animation.keyframes || [animation.from || {}, animation.to || {}],
        options: {
          duration: animation.duration || timeout,
          easing: animation.easing || 'easeOutCubic',
          fill: 'forwards'
        }
      };
    }
  };
  
  // Play animation
  const playAnimation = (type) => {
    if (!nodeRef.current) return;
    
    // Cancel existing animation
    if (animationRef.current) {
      animationRef.current.cancel();
    }
    
    // Clear timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    // Get animation
    const animation = getAnimation(type);
    
    // Create new animation
    try {
      const webAnimation = nodeRef.current.animate(
        animation.keyframes,
        animation.options
      );
      
      // Store animation
      animationRef.current = webAnimation;
      
      // Add event listeners
      webAnimation.onfinish = () => {
        if (type === 'enter') {
          setStatus('entered');
          if (onEntered) onEntered();
        } else {
          setStatus('exited');
          if (onExited) onExited();
        }
      };
      
      // Fallback timeout in case animation fails
      timeoutRef.current = setTimeout(() => {
        if (type === 'enter') {
          setStatus('entered');
          if (onEntered) onEntered();
        } else {
          setStatus('exited');
          if (onExited) onExited();
        }
      }, animation.options.duration + 50);
    } catch (error) {
      console.error('Error playing animation:', error);
      
      // Fallback if animation fails
      if (type === 'enter') {
        setStatus('entered');
        if (onEntered) onEntered();
      } else {
        setStatus('exited');
        if (onExited) onExited();
      }
    }
  };
  
  // Handle changes to inProp
  useEffect(() => {
    if (inProp) {
      // Enter
      if (status === 'exited') {
        if (onEnter) onEnter();
        setStatus('entering');
        if (onEntering) onEntering();
        playAnimation('enter');
      }
    } else {
      // Exit
      if (status === 'entered') {
        if (onExit) onExit();
        setStatus('exiting');
        if (onExiting) onExiting();
        playAnimation('exit');
      }
    }
    
    // Cleanup
    return () => {
      if (animationRef.current) {
        animationRef.current.cancel();
      }
      
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [inProp, status]);
  
  // Don't render if not mounted
  if (status === 'exited' && unmountOnExit) {
    return null;
  }
  
  // Don't render if not mounted yet
  if (status === 'exited' && mountOnEnter && !inProp) {
    return null;
  }
  
  return React.cloneElement(React.Children.only(children), {
    ref: nodeRef,
    className: `${children.props.className || ''} ${className}`.trim(),
    style: {
      ...children.props.style,
      ...style
    },
    'data-transition-status': status
  });
};

Transition.propTypes = {
  children: PropTypes.element.isRequired,
  in: PropTypes.bool.isRequired,
  enter: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.object
  ]),
  exit: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.object
  ]),
  mountOnEnter: PropTypes.bool,
  unmountOnExit: PropTypes.bool,
  timeout: PropTypes.number,
  onEnter: PropTypes.func,
  onEntering: PropTypes.func,
  onEntered: PropTypes.func,
  onExit: PropTypes.func,
  onExiting: PropTypes.func,
  onExited: PropTypes.func,
  className: PropTypes.string,
  style: PropTypes.object
};

export default Transition;
