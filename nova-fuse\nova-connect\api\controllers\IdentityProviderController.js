/**
 * Identity Provider Controller
 * 
 * This controller handles API requests related to identity providers.
 */

const IdentityProviderService = require('../services/IdentityProviderService');
const { ValidationError } = require('../utils/errors');

class IdentityProviderController {
  constructor() {
    this.identityProviderService = new IdentityProviderService();
  }

  /**
   * Get all identity providers
   */
  async getAllProviders(req, res, next) {
    try {
      const providers = await this.identityProviderService.getAllProviders();
      res.json(providers);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get identity provider by ID
   */
  async getProviderById(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Provider ID is required');
      }
      
      const provider = await this.identityProviderService.getProviderById(id);
      res.json(provider);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get identity provider by domain
   */
  async getProviderByDomain(req, res, next) {
    try {
      const { domain } = req.params;
      
      if (!domain) {
        throw new ValidationError('Domain is required');
      }
      
      const provider = await this.identityProviderService.getProviderByDomain(domain);
      
      if (!provider) {
        return res.status(404).json({ error: 'No provider found for this domain' });
      }
      
      res.json(provider);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Create a new identity provider
   */
  async createProvider(req, res, next) {
    try {
      const data = req.body;
      
      if (!data) {
        throw new ValidationError('Provider data is required');
      }
      
      const provider = await this.identityProviderService.createProvider(data, req.user.id);
      res.status(201).json(provider);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update an identity provider
   */
  async updateProvider(req, res, next) {
    try {
      const { id } = req.params;
      const data = req.body;
      
      if (!id) {
        throw new ValidationError('Provider ID is required');
      }
      
      if (!data) {
        throw new ValidationError('Provider data is required');
      }
      
      const provider = await this.identityProviderService.updateProvider(id, data, req.user.id);
      res.json(provider);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Delete an identity provider
   */
  async deleteProvider(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Provider ID is required');
      }
      
      const result = await this.identityProviderService.deleteProvider(id, req.user.id);
      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Test an identity provider connection
   */
  async testProviderConnection(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Provider ID is required');
      }
      
      const result = await this.identityProviderService.testProviderConnection(id, req.user.id);
      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Generate SAML metadata
   */
  async generateSamlMetadata(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Provider ID is required');
      }
      
      const result = await this.identityProviderService.generateSamlMetadata(id);
      
      // Set content type to XML
      res.set('Content-Type', 'application/xml');
      res.send(result.metadata);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get OIDC configuration
   */
  async getOidcConfiguration(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Provider ID is required');
      }
      
      const result = await this.identityProviderService.getOidcConfiguration(id);
      res.json(result.configuration);
    } catch (error) {
      next(error);
    }
  }
}

module.exports = new IdentityProviderController();
