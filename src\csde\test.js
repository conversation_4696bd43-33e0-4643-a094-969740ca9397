/**
 * CSDE Engine Test Script
 * 
 * This script tests the CSDE engine with sample data.
 */

const { CSDEEngine } = require('./index');

// Create sample data
const complianceData = {
  complianceScore: 0.75,
  controls: [
    {
      id: 'AC-2',
      name: 'Account Management',
      description: 'The organization needs to implement account management procedures',
      severity: 'high',
      status: 'non-compliant',
      framework: 'NIST 800-53'
    },
    {
      id: 'CM-7',
      name: 'Least Functionality',
      description: 'The organization needs to configure systems to provide only essential capabilities',
      severity: 'medium',
      status: 'partial',
      framework: 'NIST 800-53'
    },
    {
      id: 'SC-7',
      name: 'Boundary Protection',
      description: 'The organization needs to implement boundary protection mechanisms',
      severity: 'high',
      status: 'compliant',
      framework: 'NIST 800-53'
    }
  ]
};

const gcpData = {
  integrationScore: 0.85,
  services: [
    {
      id: 'GCP-IAM-1',
      name: 'IAM Role Configuration',
      description: 'IAM roles need to be configured with least privilege',
      severity: 'high',
      status: 'non-optimal',
      service: 'Cloud IAM'
    },
    {
      id: 'GCP-VPC-1',
      name: 'VPC Network Security',
      description: 'VPC network security needs to be enhanced',
      severity: 'medium',
      status: 'partial',
      service: 'VPC Network'
    },
    {
      id: 'GCP-KMS-1',
      name: 'Key Management',
      description: 'Cloud KMS keys need to be properly managed',
      severity: 'high',
      status: 'optimal',
      service: 'Cloud KMS'
    }
  ]
};

const cyberSafetyData = {
  safetyScore: 0.65,
  controls: [
    {
      id: 'CS-P3-1',
      name: 'Self-Destructing Compliance Servers',
      description: 'Implement self-destructing compliance servers with hardware-enforced geo-fencing',
      severity: 'high',
      status: 'not-implemented',
      pillar: 'Pillar 3'
    },
    {
      id: 'CS-P9-1',
      name: 'Post-Quantum Immutable Compliance Journal',
      description: 'Implement post-quantum immutable compliance journal',
      severity: 'medium',
      status: 'partial',
      pillar: 'Pillar 9'
    },
    {
      id: 'CS-P12-1',
      name: 'C-Suite Directive to Code Compiler',
      description: 'Implement C-Suite Directive to Code Compiler',
      severity: 'medium',
      status: 'implemented',
      pillar: 'Pillar 12'
    }
  ]
};

// Initialize CSDE Engine
const csdeEngine = new CSDEEngine();

// Calculate CSDE
console.log('Calculating CSDE...');
const result = csdeEngine.calculate(complianceData, gcpData, cyberSafetyData);

// Display results
console.log('\nCSDE Calculation Results:');
console.log('-------------------------');
console.log(`CSDE Value: ${result.csdeValue.toFixed(2)}`);
console.log(`Performance Factor: ${result.performanceFactor}x`);
console.log('\nComponent Values:');
console.log(`NIST Component: ${result.nistComponent.processedValue.toFixed(2)}`);
console.log(`GCP Component: ${result.gcpComponent.processedValue.toFixed(2)}`);
console.log(`Cyber-Safety Component: ${result.cyberSafetyComponent.processedValue.toFixed(2)}`);
console.log(`Tensor Product: ${result.tensorProduct.normalizedValue.toFixed(2)}`);
console.log(`Fusion Result: ${result.fusionResult.fusionValue.toFixed(2)}`);

// Display remediation actions
console.log('\nRemediation Actions:');
console.log('-------------------');
result.remediationActions.forEach((action, index) => {
  console.log(`\n${index + 1}. ${action.title} (${action.priority.toUpperCase()})`);
  console.log(`   Type: ${action.type}`);
  console.log(`   Description: ${action.description}`);
  console.log(`   Automation Potential: ${action.automationPotential}`);
  console.log(`   Estimated Effort: ${action.estimatedEffort}`);
  console.log('   Steps:');
  action.steps.forEach((step, stepIndex) => {
    console.log(`     ${stepIndex + 1}. ${step}`);
  });
});

// Display performance metrics
console.log('\nPerformance Metrics:');
console.log('-------------------');
const metrics = csdeEngine.getMetrics();
console.log(`Total Operations: ${metrics.totalOperations}`);
console.log(`Average Latency: ${metrics.averageLatency.toFixed(2)} ms`);
console.log(`Operations Per Second: ${metrics.operationsPerSecond.toFixed(2)}`);

console.log('\nTest completed successfully!');
