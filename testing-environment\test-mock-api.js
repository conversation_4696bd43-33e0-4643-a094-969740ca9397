const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');

const app = express();
const port = 3005;

// Middleware
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok' });
});

// Mock AWS Security Hub API
app.get('/aws/securityhub/findings', (req, res) => {
  res.json({
    Findings: [
      {
        Id: 'finding-1',
        Title: 'Unencrypted S3 Bucket',
        Description: 'S3 bucket is not encrypted',
        Severity: { Label: 'HIGH' },
        Resources: [{ Type: 'AwsS3Bucket', Id: 'bucket-123' }],
        Compliance: { Status: 'FAILED' },
        CreatedAt: new Date().toISOString()
      },
      {
        Id: 'finding-2',
        Title: 'IAM User with Admin Privileges',
        Description: 'IAM user has administrator privileges',
        Severity: { Label: 'MEDIUM' },
        Resources: [{ Type: 'AwsIamUser', Id: 'user-456' }],
        Compliance: { Status: 'WARNING' },
        CreatedAt: new Date().toISOString()
      }
    ]
  });
});

// Mock Okta API
app.get('/okta/users', (req, res) => {
  res.json([
    {
      id: 'user-1',
      profile: {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>'
      },
      status: 'ACTIVE'
    },
    {
      id: 'user-2',
      profile: {
        firstName: 'Jane',
        lastName: 'Smith',
        email: '<EMAIL>'
      },
      status: 'ACTIVE'
    }
  ]);
});

// Mock Jira API
app.get('/jira/issues', (req, res) => {
  res.json({
    issues: [
      {
        id: 'ISSUE-1',
        fields: {
          summary: 'Fix security vulnerability',
          status: { name: 'In Progress' },
          priority: { name: 'High' }
        }
      },
      {
        id: 'ISSUE-2',
        fields: {
          summary: 'Update compliance documentation',
          status: { name: 'To Do' },
          priority: { name: 'Medium' }
        }
      }
    ]
  });
});

// Start the server
app.listen(port, () => {
  console.log(`Mock API server running on port ${port}`);
});
