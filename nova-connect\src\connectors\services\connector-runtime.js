/**
 * NovaFuse Universal API Connector - Connector Runtime Service
 * 
 * This module provides services for executing connectors.
 */

const { createLogger } = require('../../utils/logger');
const { ConnectorError, TimeoutError } = require('../../errors');
const connectorRegistryService = require('./connector-registry-service');
const connectorConfigService = require('./connector-config-service');

const logger = createLogger('connector-runtime');

/**
 * Connector Runtime Service class
 */
class ConnectorRuntimeService {
  constructor() {
    this.connectorInstances = new Map();
    this.activeExecutions = new Map();
    
    logger.info('Connector runtime service initialized');
  }

  /**
   * Execute a connector
   * 
   * @param {string} connectorId - The connector ID
   * @param {string} configId - The configuration ID
   * @param {Object} options - Execution options
   * @returns {Promise<Object>} - The execution result
   */
  async executeConnector(connectorId, configId, options = {}) {
    const startTime = Date.now();
    const executionId = `exec-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
    
    try {
      // Get connector
      const connector = await connectorRegistryService.getConnector(connectorId);
      
      // Get configuration
      const config = await connectorConfigService.getConfiguration(configId);
      
      // Check if connector is enabled
      if (connector.status === 'retired') {
        throw new ConnectorError('Connector is retired and cannot be executed', { connectorId });
      }
      
      // Check if configuration is enabled
      if (config.enabled === false) {
        throw new ConnectorError('Configuration is disabled', { configId });
      }
      
      // Load connector module
      const connectorModule = await this._loadConnectorModule(connector);
      
      // Create execution context
      const context = {
        executionId,
        connectorId: connector.id,
        connectorName: connector.name,
        connectorVersion: connector.version,
        configId: config.id,
        configName: config.name,
        startTime,
        logger: createLogger(`connector-execution:${executionId}`),
        options
      };
      
      // Track active execution
      this.activeExecutions.set(executionId, {
        context,
        status: 'running',
        startTime
      });
      
      // Execute connector
      context.logger.info(`Starting connector execution: ${connector.name} (${connector.id})`);
      
      // Set timeout if specified
      let timeoutId;
      const timeoutPromise = new Promise((_, reject) => {
        if (options.timeout) {
          timeoutId = setTimeout(() => {
            reject(new TimeoutError(`Connector execution timed out after ${options.timeout}ms`));
          }, options.timeout);
        }
      });
      
      // Execute connector with timeout
      const executionPromise = connectorModule.execute(config.values, context);
      const result = await Promise.race([executionPromise, timeoutPromise]);
      
      // Clear timeout if set
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
      
      // Track connector usage
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      await connectorRegistryService.trackConnectorUsage(connectorId, true, responseTime);
      
      // Update active execution
      this.activeExecutions.set(executionId, {
        context,
        status: 'completed',
        startTime,
        endTime,
        result
      });
      
      context.logger.info(`Connector execution completed successfully in ${responseTime}ms`);
      
      return {
        executionId,
        connectorId: connector.id,
        connectorName: connector.name,
        configId: config.id,
        configName: config.name,
        startTime: new Date(startTime).toISOString(),
        endTime: new Date(endTime).toISOString(),
        duration: responseTime,
        status: 'success',
        result
      };
    } catch (error) {
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      // Track connector usage (failure)
      try {
        await connectorRegistryService.trackConnectorUsage(connectorId, false, responseTime);
      } catch (trackingError) {
        logger.error('Failed to track connector usage:', { error: trackingError });
      }
      
      // Update active execution
      this.activeExecutions.set(executionId, {
        context: {
          executionId,
          connectorId,
          configId,
          startTime
        },
        status: 'failed',
        startTime,
        endTime,
        error: error.message,
        stack: error.stack
      });
      
      logger.error(`Connector execution failed: ${error.message}`, { error, connectorId, configId });
      
      return {
        executionId,
        connectorId,
        configId,
        startTime: new Date(startTime).toISOString(),
        endTime: new Date(endTime).toISOString(),
        duration: responseTime,
        status: 'error',
        error: {
          message: error.message,
          code: error.code || 'EXECUTION_ERROR',
          details: error.details || {}
        }
      };
    }
  }

  /**
   * Load a connector module
   * 
   * @param {Object} connector - The connector
   * @returns {Object} - The connector module
   * @private
   */
  async _loadConnectorModule(connector) {
    // Check if connector is already loaded
    if (this.connectorInstances.has(connector.id)) {
      return this.connectorInstances.get(connector.id);
    }
    
    try {
      // In a real implementation, this would dynamically load the connector module
      // For now, we'll return a mock connector module
      const connectorModule = {
        execute: async (config, context) => {
          // Simulate connector execution
          await new Promise(resolve => setTimeout(resolve, 500));
          
          return {
            success: true,
            message: 'Connector executed successfully',
            data: {
              connectorId: connector.id,
              connectorName: connector.name,
              timestamp: new Date().toISOString()
            }
          };
        }
      };
      
      // Cache connector module
      this.connectorInstances.set(connector.id, connectorModule);
      
      return connectorModule;
    } catch (error) {
      logger.error(`Failed to load connector module for ${connector.id}:`, { error });
      throw new ConnectorError(`Failed to load connector module: ${error.message}`, { connectorId: connector.id });
    }
  }

  /**
   * Get execution status
   * 
   * @param {string} executionId - The execution ID
   * @returns {Object} - The execution status
   */
  getExecutionStatus(executionId) {
    const execution = this.activeExecutions.get(executionId);
    
    if (!execution) {
      return {
        executionId,
        status: 'unknown',
        message: 'Execution not found'
      };
    }
    
    return {
      executionId,
      connectorId: execution.context.connectorId,
      connectorName: execution.context.connectorName,
      configId: execution.context.configId,
      configName: execution.context.configName,
      startTime: new Date(execution.startTime).toISOString(),
      endTime: execution.endTime ? new Date(execution.endTime).toISOString() : null,
      duration: execution.endTime ? execution.endTime - execution.startTime : Date.now() - execution.startTime,
      status: execution.status,
      result: execution.result,
      error: execution.error
    };
  }

  /**
   * Get all active executions
   * 
   * @returns {Array<Object>} - The active executions
   */
  getAllExecutions() {
    return Array.from(this.activeExecutions.entries()).map(([executionId, execution]) => ({
      executionId,
      connectorId: execution.context.connectorId,
      connectorName: execution.context.connectorName,
      configId: execution.context.configId,
      configName: execution.context.configName,
      startTime: new Date(execution.startTime).toISOString(),
      endTime: execution.endTime ? new Date(execution.endTime).toISOString() : null,
      duration: execution.endTime ? execution.endTime - execution.startTime : Date.now() - execution.startTime,
      status: execution.status
    }));
  }

  /**
   * Cancel an execution
   * 
   * @param {string} executionId - The execution ID
   * @returns {boolean} - Whether the execution was cancelled
   */
  cancelExecution(executionId) {
    const execution = this.activeExecutions.get(executionId);
    
    if (!execution || execution.status !== 'running') {
      return false;
    }
    
    // In a real implementation, this would actually cancel the execution
    // For now, we'll just update the status
    execution.status = 'cancelled';
    execution.endTime = Date.now();
    
    logger.info(`Execution cancelled: ${executionId}`);
    
    return true;
  }
}

// Create singleton instance
const connectorRuntimeService = new ConnectorRuntimeService();

module.exports = connectorRuntimeService;
