/**
 * IP-Based Access Control
 *
 * This module provides IP-based access control capabilities for the Finite Universe
 * Principle defense system, enabling IP whitelisting, blacklisting, and rate limiting.
 */

const EventEmitter = require('events');
const ipaddr = require('ipaddr.js');

/**
 * IPAccessControl class
 * 
 * Provides IP-based access control capabilities.
 */
class IPAccessControl extends EventEmitter {
  /**
   * Constructor
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();

    this.options = {
      enableLogging: true,
      defaultPolicy: options.defaultPolicy || 'deny', // 'allow' or 'deny'
      whitelistEnabled: options.whitelistEnabled !== undefined ? options.whitelistEnabled : true,
      blacklistEnabled: options.blacklistEnabled !== undefined ? options.blacklistEnabled : true,
      rateLimitEnabled: options.rateLimitEnabled !== undefined ? options.rateLimitEnabled : true,
      geoRestrictionEnabled: options.geoRestrictionEnabled !== undefined ? options.geoRestrictionEnabled : false,
      rateLimitWindow: options.rateLimitWindow || 60000, // 1 minute in milliseconds
      rateLimitMax: options.rateLimitMax || 100, // 100 requests per window
      rateLimitBurstMax: options.rateLimitBurstMax || 200, // 200 requests burst
      ...options
    };

    // Initialize IP whitelist
    this.whitelist = new Set(options.whitelist || []);
    
    // Initialize IP blacklist
    this.blacklist = new Set(options.blacklist || []);
    
    // Initialize IP CIDR whitelist
    this.cidrWhitelist = options.cidrWhitelist || [];
    
    // Initialize IP CIDR blacklist
    this.cidrBlacklist = options.cidrBlacklist || [];
    
    // Initialize country whitelist
    this.countryWhitelist = new Set(options.countryWhitelist || []);
    
    // Initialize country blacklist
    this.countryBlacklist = new Set(options.countryBlacklist || []);
    
    // Initialize rate limit registry
    this.rateLimits = new Map();
    
    // Initialize geo provider
    this.geoProvider = options.geoProvider;

    if (this.options.enableLogging) {
      console.log('IPAccessControl initialized with options:', this.options);
    }
  }

  /**
   * Check if IP is allowed
   * @param {string} ip - IP address
   * @param {Object} context - Additional context
   * @returns {Object} - Access control result
   */
  checkAccess(ip, context = {}) {
    // Validate IP
    if (!this._isValidIp(ip)) {
      return {
        allowed: false,
        reason: 'invalid-ip'
      };
    }
    
    // Check whitelist
    if (this.options.whitelistEnabled && this._isWhitelisted(ip)) {
      if (this.options.enableLogging) {
        console.log(`IP ${ip} is whitelisted`);
      }
      
      return {
        allowed: true,
        reason: 'whitelisted'
      };
    }
    
    // Check blacklist
    if (this.options.blacklistEnabled && this._isBlacklisted(ip)) {
      if (this.options.enableLogging) {
        console.log(`IP ${ip} is blacklisted`);
      }
      
      // Emit blacklist-hit event
      this.emit('blacklist-hit', { ip, context });
      
      return {
        allowed: false,
        reason: 'blacklisted'
      };
    }
    
    // Check geo restrictions
    if (this.options.geoRestrictionEnabled && this.geoProvider) {
      const geoResult = this._checkGeoRestrictions(ip);
      
      if (!geoResult.allowed) {
        if (this.options.enableLogging) {
          console.log(`IP ${ip} is geo-restricted: ${geoResult.reason}`);
        }
        
        // Emit geo-restriction-hit event
        this.emit('geo-restriction-hit', { ip, context, country: geoResult.country });
        
        return geoResult;
      }
    }
    
    // Check rate limit
    if (this.options.rateLimitEnabled) {
      const rateResult = this._checkRateLimit(ip);
      
      if (!rateResult.allowed) {
        if (this.options.enableLogging) {
          console.log(`IP ${ip} is rate-limited: ${rateResult.reason}`);
        }
        
        // Emit rate-limit-hit event
        this.emit('rate-limit-hit', { ip, context, ...rateResult });
        
        return rateResult;
      }
    }
    
    // Apply default policy
    const allowed = this.options.defaultPolicy === 'allow';
    
    return {
      allowed,
      reason: allowed ? 'default-allow' : 'default-deny'
    };
  }

  /**
   * Add IP to whitelist
   * @param {string} ip - IP address
   * @returns {boolean} - True if IP was added, false otherwise
   */
  addToWhitelist(ip) {
    // Validate IP
    if (!this._isValidIp(ip)) {
      return false;
    }
    
    // Add to whitelist
    this.whitelist.add(ip);
    
    if (this.options.enableLogging) {
      console.log(`IP ${ip} added to whitelist`);
    }
    
    // Emit whitelist-add event
    this.emit('whitelist-add', { ip });
    
    return true;
  }

  /**
   * Remove IP from whitelist
   * @param {string} ip - IP address
   * @returns {boolean} - True if IP was removed, false otherwise
   */
  removeFromWhitelist(ip) {
    // Check if IP is in whitelist
    if (!this.whitelist.has(ip)) {
      return false;
    }
    
    // Remove from whitelist
    this.whitelist.delete(ip);
    
    if (this.options.enableLogging) {
      console.log(`IP ${ip} removed from whitelist`);
    }
    
    // Emit whitelist-remove event
    this.emit('whitelist-remove', { ip });
    
    return true;
  }

  /**
   * Add IP to blacklist
   * @param {string} ip - IP address
   * @returns {boolean} - True if IP was added, false otherwise
   */
  addToBlacklist(ip) {
    // Validate IP
    if (!this._isValidIp(ip)) {
      return false;
    }
    
    // Add to blacklist
    this.blacklist.add(ip);
    
    if (this.options.enableLogging) {
      console.log(`IP ${ip} added to blacklist`);
    }
    
    // Emit blacklist-add event
    this.emit('blacklist-add', { ip });
    
    return true;
  }

  /**
   * Remove IP from blacklist
   * @param {string} ip - IP address
   * @returns {boolean} - True if IP was removed, false otherwise
   */
  removeFromBlacklist(ip) {
    // Check if IP is in blacklist
    if (!this.blacklist.has(ip)) {
      return false;
    }
    
    // Remove from blacklist
    this.blacklist.delete(ip);
    
    if (this.options.enableLogging) {
      console.log(`IP ${ip} removed from blacklist`);
    }
    
    // Emit blacklist-remove event
    this.emit('blacklist-remove', { ip });
    
    return true;
  }

  /**
   * Add CIDR to whitelist
   * @param {string} cidr - CIDR notation
   * @returns {boolean} - True if CIDR was added, false otherwise
   */
  addCidrToWhitelist(cidr) {
    // Validate CIDR
    if (!this._isValidCidr(cidr)) {
      return false;
    }
    
    // Add to CIDR whitelist
    this.cidrWhitelist.push(cidr);
    
    if (this.options.enableLogging) {
      console.log(`CIDR ${cidr} added to whitelist`);
    }
    
    // Emit cidr-whitelist-add event
    this.emit('cidr-whitelist-add', { cidr });
    
    return true;
  }

  /**
   * Remove CIDR from whitelist
   * @param {string} cidr - CIDR notation
   * @returns {boolean} - True if CIDR was removed, false otherwise
   */
  removeCidrFromWhitelist(cidr) {
    // Check if CIDR is in whitelist
    const index = this.cidrWhitelist.indexOf(cidr);
    
    if (index === -1) {
      return false;
    }
    
    // Remove from CIDR whitelist
    this.cidrWhitelist.splice(index, 1);
    
    if (this.options.enableLogging) {
      console.log(`CIDR ${cidr} removed from whitelist`);
    }
    
    // Emit cidr-whitelist-remove event
    this.emit('cidr-whitelist-remove', { cidr });
    
    return true;
  }

  /**
   * Add CIDR to blacklist
   * @param {string} cidr - CIDR notation
   * @returns {boolean} - True if CIDR was added, false otherwise
   */
  addCidrToBlacklist(cidr) {
    // Validate CIDR
    if (!this._isValidCidr(cidr)) {
      return false;
    }
    
    // Add to CIDR blacklist
    this.cidrBlacklist.push(cidr);
    
    if (this.options.enableLogging) {
      console.log(`CIDR ${cidr} added to blacklist`);
    }
    
    // Emit cidr-blacklist-add event
    this.emit('cidr-blacklist-add', { cidr });
    
    return true;
  }

  /**
   * Remove CIDR from blacklist
   * @param {string} cidr - CIDR notation
   * @returns {boolean} - True if CIDR was removed, false otherwise
   */
  removeCidrFromBlacklist(cidr) {
    // Check if CIDR is in blacklist
    const index = this.cidrBlacklist.indexOf(cidr);
    
    if (index === -1) {
      return false;
    }
    
    // Remove from CIDR blacklist
    this.cidrBlacklist.splice(index, 1);
    
    if (this.options.enableLogging) {
      console.log(`CIDR ${cidr} removed from blacklist`);
    }
    
    // Emit cidr-blacklist-remove event
    this.emit('cidr-blacklist-remove', { cidr });
    
    return true;
  }

  /**
   * Add country to whitelist
   * @param {string} country - Country code
   * @returns {boolean} - True if country was added, false otherwise
   */
  addCountryToWhitelist(country) {
    // Validate country code
    if (!this._isValidCountryCode(country)) {
      return false;
    }
    
    // Add to country whitelist
    this.countryWhitelist.add(country.toUpperCase());
    
    if (this.options.enableLogging) {
      console.log(`Country ${country} added to whitelist`);
    }
    
    // Emit country-whitelist-add event
    this.emit('country-whitelist-add', { country });
    
    return true;
  }

  /**
   * Remove country from whitelist
   * @param {string} country - Country code
   * @returns {boolean} - True if country was removed, false otherwise
   */
  removeCountryFromWhitelist(country) {
    // Check if country is in whitelist
    if (!this.countryWhitelist.has(country.toUpperCase())) {
      return false;
    }
    
    // Remove from country whitelist
    this.countryWhitelist.delete(country.toUpperCase());
    
    if (this.options.enableLogging) {
      console.log(`Country ${country} removed from whitelist`);
    }
    
    // Emit country-whitelist-remove event
    this.emit('country-whitelist-remove', { country });
    
    return true;
  }

  /**
   * Add country to blacklist
   * @param {string} country - Country code
   * @returns {boolean} - True if country was added, false otherwise
   */
  addCountryToBlacklist(country) {
    // Validate country code
    if (!this._isValidCountryCode(country)) {
      return false;
    }
    
    // Add to country blacklist
    this.countryBlacklist.add(country.toUpperCase());
    
    if (this.options.enableLogging) {
      console.log(`Country ${country} added to blacklist`);
    }
    
    // Emit country-blacklist-add event
    this.emit('country-blacklist-add', { country });
    
    return true;
  }

  /**
   * Remove country from blacklist
   * @param {string} country - Country code
   * @returns {boolean} - True if country was removed, false otherwise
   */
  removeCountryFromBlacklist(country) {
    // Check if country is in blacklist
    if (!this.countryBlacklist.has(country.toUpperCase())) {
      return false;
    }
    
    // Remove from country blacklist
    this.countryBlacklist.delete(country.toUpperCase());
    
    if (this.options.enableLogging) {
      console.log(`Country ${country} removed from blacklist`);
    }
    
    // Emit country-blacklist-remove event
    this.emit('country-blacklist-remove', { country });
    
    return true;
  }

  /**
   * Set geo provider
   * @param {Object} provider - Geo provider
   * @returns {boolean} - True if provider was set, false otherwise
   */
  setGeoProvider(provider) {
    // Check if provider has required methods
    if (!provider.getCountryCode) {
      return false;
    }
    
    // Set geo provider
    this.geoProvider = provider;
    
    if (this.options.enableLogging) {
      console.log('Geo provider set');
    }
    
    // Emit geo-provider-set event
    this.emit('geo-provider-set');
    
    return true;
  }

  /**
   * Check if IP is whitelisted
   * @param {string} ip - IP address
   * @returns {boolean} - True if IP is whitelisted, false otherwise
   * @private
   */
  _isWhitelisted(ip) {
    // Check direct whitelist
    if (this.whitelist.has(ip)) {
      return true;
    }
    
    // Check CIDR whitelist
    return this._isInCidrList(ip, this.cidrWhitelist);
  }

  /**
   * Check if IP is blacklisted
   * @param {string} ip - IP address
   * @returns {boolean} - True if IP is blacklisted, false otherwise
   * @private
   */
  _isBlacklisted(ip) {
    // Check direct blacklist
    if (this.blacklist.has(ip)) {
      return true;
    }
    
    // Check CIDR blacklist
    return this._isInCidrList(ip, this.cidrBlacklist);
  }

  /**
   * Check if IP is in CIDR list
   * @param {string} ip - IP address
   * @param {Array} cidrList - CIDR list
   * @returns {boolean} - True if IP is in CIDR list, false otherwise
   * @private
   */
  _isInCidrList(ip, cidrList) {
    try {
      const addr = ipaddr.parse(ip);
      
      for (const cidr of cidrList) {
        const range = ipaddr.parseCIDR(cidr);
        
        if (addr.match(range)) {
          return true;
        }
      }
    } catch (error) {
      if (this.options.enableLogging) {
        console.error(`Error checking IP ${ip} in CIDR list:`, error);
      }
    }
    
    return false;
  }

  /**
   * Check geo restrictions
   * @param {string} ip - IP address
   * @returns {Object} - Geo restriction result
   * @private
   */
  _checkGeoRestrictions(ip) {
    try {
      // Get country code
      const country = this.geoProvider.getCountryCode(ip);
      
      if (!country) {
        return {
          allowed: false,
          reason: 'unknown-country',
          country: null
        };
      }
      
      // Check country whitelist
      if (this.countryWhitelist.size > 0) {
        if (!this.countryWhitelist.has(country)) {
          return {
            allowed: false,
            reason: 'country-not-whitelisted',
            country
          };
        }
      }
      
      // Check country blacklist
      if (this.countryBlacklist.has(country)) {
        return {
          allowed: false,
          reason: 'country-blacklisted',
          country
        };
      }
      
      return {
        allowed: true,
        reason: 'country-allowed',
        country
      };
    } catch (error) {
      if (this.options.enableLogging) {
        console.error(`Error checking geo restrictions for IP ${ip}:`, error);
      }
      
      return {
        allowed: false,
        reason: 'geo-error',
        country: null
      };
    }
  }

  /**
   * Check rate limit
   * @param {string} ip - IP address
   * @returns {Object} - Rate limit result
   * @private
   */
  _checkRateLimit(ip) {
    const now = Date.now();
    
    // Get rate limit data
    let rateData = this.rateLimits.get(ip);
    
    if (!rateData) {
      // Initialize rate limit data
      rateData = {
        count: 0,
        window: now,
        lastRequest: now
      };
      
      this.rateLimits.set(ip, rateData);
    }
    
    // Check if window has expired
    if (now - rateData.window > this.options.rateLimitWindow) {
      // Reset rate limit data
      rateData.count = 0;
      rateData.window = now;
    }
    
    // Increment request count
    rateData.count++;
    rateData.lastRequest = now;
    
    // Check if rate limit exceeded
    if (rateData.count > this.options.rateLimitMax) {
      // Check if burst limit exceeded
      if (rateData.count > this.options.rateLimitBurstMax) {
        return {
          allowed: false,
          reason: 'rate-limit-burst-exceeded',
          count: rateData.count,
          limit: this.options.rateLimitBurstMax,
          window: this.options.rateLimitWindow,
          reset: rateData.window + this.options.rateLimitWindow
        };
      }
      
      // Check request rate
      const timeSinceWindowStart = now - rateData.window;
      const rate = rateData.count / (timeSinceWindowStart / 1000);
      const maxRate = this.options.rateLimitMax / (this.options.rateLimitWindow / 1000);
      
      if (rate > maxRate) {
        return {
          allowed: false,
          reason: 'rate-limit-exceeded',
          count: rateData.count,
          limit: this.options.rateLimitMax,
          window: this.options.rateLimitWindow,
          reset: rateData.window + this.options.rateLimitWindow
        };
      }
    }
    
    return {
      allowed: true,
      reason: 'rate-limit-allowed',
      count: rateData.count,
      limit: this.options.rateLimitMax,
      window: this.options.rateLimitWindow
    };
  }

  /**
   * Check if IP is valid
   * @param {string} ip - IP address
   * @returns {boolean} - True if IP is valid, false otherwise
   * @private
   */
  _isValidIp(ip) {
    try {
      ipaddr.parse(ip);
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Check if CIDR is valid
   * @param {string} cidr - CIDR notation
   * @returns {boolean} - True if CIDR is valid, false otherwise
   * @private
   */
  _isValidCidr(cidr) {
    try {
      ipaddr.parseCIDR(cidr);
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Check if country code is valid
   * @param {string} country - Country code
   * @returns {boolean} - True if country code is valid, false otherwise
   * @private
   */
  _isValidCountryCode(country) {
    return typeof country === 'string' && country.length === 2;
  }

  /**
   * Dispose resources
   */
  dispose() {
    // Clear registries
    this.whitelist.clear();
    this.blacklist.clear();
    this.cidrWhitelist = [];
    this.cidrBlacklist = [];
    this.countryWhitelist.clear();
    this.countryBlacklist.clear();
    this.rateLimits.clear();
    
    if (this.options.enableLogging) {
      console.log('IPAccessControl disposed');
    }
  }
}

/**
 * Create an IP access control with recommended settings
 * @param {Object} options - Configuration options
 * @returns {IPAccessControl} - Configured IP access control
 */
function createIPAccessControl(options = {}) {
  return new IPAccessControl({
    enableLogging: true,
    defaultPolicy: 'deny',
    whitelistEnabled: true,
    blacklistEnabled: true,
    rateLimitEnabled: true,
    geoRestrictionEnabled: false,
    rateLimitWindow: 60000,
    rateLimitMax: 100,
    rateLimitBurstMax: 200,
    ...options
  });
}

module.exports = {
  IPAccessControl,
  createIPAccessControl
};
