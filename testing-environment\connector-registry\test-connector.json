{"metadata": {"name": "Test Connector", "version": "1.0.0", "category": "Test", "description": "Test connector", "author": "NovaGRC", "tags": ["test"]}, "authentication": {"type": "API_KEY", "fields": {"apiKey": {"type": "string", "description": "API Key", "required": true, "sensitive": true}}, "testConnection": {"endpoint": "/health", "method": "GET", "expectedResponse": {"status": 200}}}, "configuration": {"baseUrl": "http://localhost:3005", "headers": {}, "timeout": 30000, "retryPolicy": {"maxRetries": 3, "backoffStrategy": "exponential"}}, "endpoints": [{"id": "getFindings", "name": "Get Findings", "path": "/aws/securityhub/findings", "method": "GET", "parameters": {"query": {}, "path": {}, "body": {}}, "response": {"successCode": 200}}], "mappings": [], "events": {"webhooks": [], "polling": []}}