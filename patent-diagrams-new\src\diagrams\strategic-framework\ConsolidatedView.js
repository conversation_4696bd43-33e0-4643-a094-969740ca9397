import React, { useRef } from 'react';
import styled from 'styled-components';
import TrinityFramework from './TrinityFramework';
import PerformanceVisualization from './PerformanceVisualization';
import PartnerEmpowermentFlywheel from './PartnerEmpowermentFlywheel';
import GoogleOnlyApproach from './GoogleOnlyApproach';
import EnterpriseBeforeAfter from './EnterpriseBeforeAfter';
import PatentShield from './PatentShield';

const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
`;

const Title = styled.h1`
  font-size: 24px;
  color: #333;
  text-align: center;
  margin-bottom: 30px;
`;

const DiagramContainer = styled.div`
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 20px;
  background-color: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  margin-bottom: 30px;
  transform: scale(0.8);
  transform-origin: top center;
  page-break-after: always;

  @media print {
    border: none;
    box-shadow: none;
    transform: none;
    padding: 0;
    margin: 20px 0;
    page-break-after: always;
  }
`;

const DiagramTitle = styled.h2`
  font-size: 18px;
  color: #333;
  text-align: center;
  margin-bottom: 20px;
`;

const ActionBar = styled.div`
  display: flex;
  justify-content: center;
  margin-bottom: 30px;

  @media print {
    display: none;
  }
`;

const Button = styled.button`
  background-color: #555555;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 10px 20px;
  font-size: 16px;
  cursor: pointer;
  margin: 0 10px;

  &:hover {
    background-color: #666666;
  }
`;

const PrintInstructions = styled.div`
  background-color: #f8f9fa;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 30px;

  @media print {
    display: none;
  }
`;

const PageBreak = styled.div`
  page-break-after: always;
  height: 0;

  @media screen {
    display: none;
  }
`;

const CoverPage = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  text-align: center;
  page-break-after: always;

  @media screen {
    height: 600px;
    border: 1px solid #ddd;
    border-radius: 8px;
    margin-bottom: 30px;
    background-color: white;
  }
`;

const CoverTitle = styled.h1`
  font-size: 32px;
  margin-bottom: 20px;
`;

const CoverSubtitle = styled.h2`
  font-size: 24px;
  margin-bottom: 40px;
  color: #666;
`;

const CoverInfo = styled.div`
  margin-top: 60px;
  font-size: 16px;
  color: #666;
`;

const CoverLogo = styled.div`
  font-size: 48px;
  margin-bottom: 40px;
  color: #555555;
  font-weight: bold;
`;

const TableOfContentsPage = styled.div`
  padding: 40px;
  page-break-after: always;

  @media screen {
    border: 1px solid #ddd;
    border-radius: 8px;
    margin-bottom: 30px;
    background-color: white;
  }
`;

const TOCTitle = styled.h2`
  font-size: 24px;
  margin-bottom: 30px;
  text-align: center;
`;

const TOCList = styled.ol`
  margin-left: 20px;

  li {
    margin-bottom: 15px;
    font-size: 16px;
  }
`;

const ConclusionPage = styled.div`
  padding: 40px;

  @media screen {
    border: 1px solid #ddd;
    border-radius: 8px;
    margin-bottom: 30px;
    background-color: white;
  }
`;

const ConclusionTitle = styled.h2`
  font-size: 24px;
  margin-bottom: 30px;
  text-align: center;
`;

const ConclusionText = styled.div`
  font-size: 16px;
  line-height: 1.6;

  p {
    margin-bottom: 20px;
  }
`;

const ContactInfo = styled.div`
  margin-top: 60px;
  padding-top: 20px;
  border-top: 1px solid #ddd;
  text-align: center;
  font-size: 14px;
  color: #666;
`;

const ConsolidatedView = () => {
  const containerRef = useRef(null);

  const handlePrint = () => {
    window.print();
  };

  const handleSaveAsPDF = () => {
    // Trigger the browser's print dialog with PDF option
    window.print();
    // Note: Most browsers allow saving as PDF from the print dialog
  };

  // Get current date in a readable format
  const currentDate = new Date().toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  return (
    <Container ref={containerRef}>
      <Title>The Cyber-Safety Dominance Framework: Complete Presentation</Title>

      <ActionBar>
        <Button onClick={handlePrint}>Print Presentation</Button>
        <Button onClick={handleSaveAsPDF}>Save as PDF</Button>
      </ActionBar>

      <PrintInstructions>
        <h3>PDF Export Instructions:</h3>
        <ol>
          <li>Click "Save as PDF" button above</li>
          <li>In the print dialog that appears, select "Save as PDF" as the destination</li>
          <li>Click "Save" and choose where to save the file</li>
          <li>The PDF will include a cover page and all six diagrams, each on its own page</li>
        </ol>
      </PrintInstructions>

      {/* Cover Page */}
      <CoverPage>
        <CoverLogo>NovaFuse</CoverLogo>
        <CoverTitle>The Cyber-Safety Dominance Framework</CoverTitle>
        <CoverSubtitle>A Strategic Trinity for Google & NIST</CoverSubtitle>
        <CoverInfo>
          <p>Prepared by: David Nigel Irvin</p>
          <p>Date: {currentDate}</p>
          <p>© {new Date().getFullYear()} NovaFuse - All Rights Reserved</p>
        </CoverInfo>
      </CoverPage>
      <PageBreak />

      {/* Table of Contents */}
      <TableOfContentsPage>
        <TOCTitle>Table of Contents</TOCTitle>
        <TOCList>
          <li>FIG. 1: The Cyber-Safety Dominance Framework - Strategic Trinity</li>
          <li>FIG. 2: 3,142x Performance Visualization</li>
          <li>FIG. 3: Partner Empowerment Flywheel</li>
          <li>FIG. 4: Google-Only Approach with Wiz Enhancement</li>
          <li>FIG. 5: Enterprise Case Study - Before & After</li>
          <li>FIG. 6: Patent Shield & Strategic Moat</li>
          <li>Next Steps & Contact Information</li>
        </TOCList>
      </TableOfContentsPage>
      <PageBreak />

      <DiagramContainer>
        <DiagramTitle>FIG. 1: The Cyber-Safety Dominance Framework - Strategic Trinity</DiagramTitle>
        <TrinityFramework />
      </DiagramContainer>
      <PageBreak />

      <DiagramContainer>
        <DiagramTitle>FIG. 2: 3,142x Performance Visualization</DiagramTitle>
        <PerformanceVisualization />
      </DiagramContainer>
      <PageBreak />

      <DiagramContainer>
        <DiagramTitle>FIG. 3: Partner Empowerment Flywheel</DiagramTitle>
        <PartnerEmpowermentFlywheel />
      </DiagramContainer>
      <PageBreak />

      <DiagramContainer>
        <DiagramTitle>FIG. 4: Google-Only Approach with Wiz Enhancement</DiagramTitle>
        <GoogleOnlyApproach />
      </DiagramContainer>
      <PageBreak />

      <DiagramContainer>
        <DiagramTitle>FIG. 5: Enterprise Case Study - Before & After</DiagramTitle>
        <EnterpriseBeforeAfter />
      </DiagramContainer>
      <PageBreak />

      <DiagramContainer>
        <DiagramTitle>FIG. 6: Patent Shield & Strategic Moat</DiagramTitle>
        <PatentShield />
      </DiagramContainer>
      <PageBreak />

      {/* Conclusion Page */}
      <ConclusionPage>
        <ConclusionTitle>Next Steps</ConclusionTitle>
        <ConclusionText>
          <p>
            The Cyber-Safety Dominance Framework represents a trillion-dollar opportunity that transforms the global approach to security and compliance, creating a strategic advantage that will dominate the market for the next 15+ years.
          </p>
          <p>
            We propose the following concrete and measurable next steps:
          </p>
          <ol>
            <li><strong>Week 1:</strong> Finalize the one-pager partnership proposal with performance benchmarks</li>
            <li><strong>Week 2:</strong> Secure Google + NIST executive alignment</li>
            <li><strong>Week 3:</strong> Draft the press release + launch timeline</li>
          </ol>
          <p>
            This framework is accompanied by visual presentations including the Trinity Diagram, 3,142x Value Flywheel, and Patent Shield visualization. Additional materials on the underlying architectural patterns are available upon request.
          </p>
        </ConclusionText>
        <ContactInfo>
          <p>For more information, please contact:</p>
          <p>David Nigel Irvin | <EMAIL></p>
          <p>© {new Date().getFullYear()} NovaFuse - All Rights Reserved</p>
        </ContactInfo>
      </ConclusionPage>
    </Container>
  );
};

export default ConsolidatedView;
