/**
 * Package Routes
 * 
 * This file contains routes for package management.
 */

const express = require('express');
const router = express.Router();
const PackageController = require('../controllers/PackageController');
const { authenticate, hasPermission } = require('../middleware/authMiddleware');

// All routes require authentication
router.use(authenticate);

// Get all packages (admin only)
router.get('/', hasPermission('admin:packages'), PackageController.getAllPackages);

// Get package by ID (admin only)
router.get('/:id', hasPermission('admin:packages'), PackageController.getPackageById);

// Create a new package (admin only)
router.post('/', hasPermission('admin:packages'), PackageController.createPackage);

// Update a package (admin only)
router.put('/:id', hasPermission('admin:packages'), PackageController.updatePackage);

// Delete a package (admin only)
router.delete('/:id', hasPermission('admin:packages'), PackageController.deletePackage);

// Get tenant package (admin or tenant owner)
router.get('/tenant/:tenantId', hasPermission('admin:packages', 'tenant:owner'), PackageController.getTenantPackage);

// Set tenant package (admin only)
router.put('/tenant/:tenantId', hasPermission('admin:packages'), PackageController.setTenantPackage);

// Clear cache (admin only)
router.post('/cache/clear', hasPermission('admin:system'), PackageController.clearCache);

module.exports = router;
