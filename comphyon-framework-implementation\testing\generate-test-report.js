/**
 * Comphyon Test Report Generator
 * 
 * This script runs all tests and generates a comprehensive test report.
 */

const fs = require('fs');
const path = require('path');
const { TestRunner } = require('./test-framework');
const { createIntegrationLayerTestSuite } = require('./integration-layer-tests');
const { createComphyonSystemTestSuite } = require('./comphyon-system-tests');

/**
 * Generate test report
 */
async function generateTestReport() {
  console.log('=== Generating Comphyon Framework Test Report ===\n');
  
  // Create test runner
  const testRunner = new TestRunner({
    enableLogging: true,
    parallelSuites: false
  });
  
  // Add test suites
  testRunner.addSuite(createIntegrationLayerTestSuite());
  testRunner.addSuite(createComphyonSystemTestSuite());
  
  // Create report data
  const reportData = {
    timestamp: new Date().toISOString(),
    summary: {
      total: 0,
      passed: 0,
      failed: 0,
      skipped: 0,
      duration: 0
    },
    suites: []
  };
  
  // Track test results
  testRunner.on('suite-complete', (data) => {
    reportData.suites.push({
      name: data.name,
      passed: data.passed,
      failed: data.failed,
      skipped: data.skipped,
      total: data.total,
      duration: data.duration,
      tests: []
    });
  });
  
  testRunner.on('test-complete', (data) => {
    if (reportData.suites.length > 0) {
      const currentSuite = reportData.suites[reportData.suites.length - 1];
      currentSuite.tests.push({
        name: data.name,
        status: data.status,
        duration: data.duration,
        error: data.error ? data.error.message : null
      });
    }
  });
  
  // Run tests
  try {
    const result = await testRunner.run();
    
    // Update summary
    reportData.summary = {
      total: result.total,
      passed: result.passed,
      failed: result.failed,
      skipped: result.skipped,
      duration: result.duration
    };
    
    // Generate HTML report
    const htmlReport = generateHtmlReport(reportData);
    
    // Create reports directory if it doesn't exist
    const reportsDir = path.join(__dirname, '..', 'reports');
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir);
    }
    
    // Write HTML report
    const reportPath = path.join(reportsDir, `test-report-${new Date().toISOString().replace(/:/g, '-')}.html`);
    fs.writeFileSync(reportPath, htmlReport);
    
    console.log(`\nTest report generated: ${reportPath}`);
    
    // Exit with appropriate code
    process.exit(result.failed > 0 ? 1 : 0);
  } catch (error) {
    console.error('Error generating test report:', error);
    process.exit(1);
  }
}

/**
 * Generate HTML report
 * @param {Object} data - Report data
 * @returns {string} - HTML report
 */
function generateHtmlReport(data) {
  const passRate = data.summary.total > 0 ? Math.round((data.summary.passed / data.summary.total) * 100) : 0;
  
  return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Comphyon Framework Test Report</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    h1, h2, h3 {
      color: #0066cc;
    }
    .summary {
      background-color: #f5f5f5;
      padding: 20px;
      border-radius: 5px;
      margin-bottom: 20px;
    }
    .summary-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
      margin-top: 20px;
    }
    .summary-item {
      text-align: center;
      padding: 15px;
      border-radius: 5px;
    }
    .total {
      background-color: #e6f2ff;
    }
    .passed {
      background-color: #e6ffe6;
    }
    .failed {
      background-color: #ffe6e6;
    }
    .skipped {
      background-color: #fff9e6;
    }
    .duration {
      background-color: #e6e6ff;
    }
    .pass-rate {
      background-color: ${passRate >= 90 ? '#e6ffe6' : passRate >= 70 ? '#fff9e6' : '#ffe6e6'};
    }
    .suite {
      margin-bottom: 30px;
      border: 1px solid #ddd;
      border-radius: 5px;
      overflow: hidden;
    }
    .suite-header {
      background-color: #0066cc;
      color: white;
      padding: 10px 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .suite-stats {
      display: flex;
      gap: 15px;
    }
    .suite-body {
      padding: 0;
    }
    table {
      width: 100%;
      border-collapse: collapse;
    }
    th, td {
      padding: 12px 15px;
      text-align: left;
      border-bottom: 1px solid #ddd;
    }
    th {
      background-color: #f2f2f2;
    }
    tr:hover {
      background-color: #f5f5f5;
    }
    .status {
      font-weight: bold;
      padding: 5px 10px;
      border-radius: 3px;
      display: inline-block;
      min-width: 80px;
      text-align: center;
    }
    .status-passed {
      background-color: #dff0d8;
      color: #3c763d;
    }
    .status-failed {
      background-color: #f2dede;
      color: #a94442;
    }
    .status-skipped {
      background-color: #fcf8e3;
      color: #8a6d3b;
    }
    .error {
      color: #a94442;
      background-color: #f2dede;
      padding: 10px;
      border-radius: 3px;
      margin-top: 5px;
      white-space: pre-wrap;
      font-family: monospace;
    }
    footer {
      margin-top: 30px;
      text-align: center;
      color: #777;
      font-size: 0.9em;
    }
  </style>
</head>
<body>
  <h1>Comphyon Framework Test Report</h1>
  <p>Generated on: ${new Date(data.timestamp).toLocaleString()}</p>
  
  <div class="summary">
    <h2>Summary</h2>
    <div class="summary-grid">
      <div class="summary-item total">
        <h3>Total Tests</h3>
        <p>${data.summary.total}</p>
      </div>
      <div class="summary-item passed">
        <h3>Passed</h3>
        <p>${data.summary.passed}</p>
      </div>
      <div class="summary-item failed">
        <h3>Failed</h3>
        <p>${data.summary.failed}</p>
      </div>
      <div class="summary-item skipped">
        <h3>Skipped</h3>
        <p>${data.summary.skipped}</p>
      </div>
      <div class="summary-item duration">
        <h3>Duration</h3>
        <p>${(data.summary.duration / 1000).toFixed(2)}s</p>
      </div>
      <div class="summary-item pass-rate">
        <h3>Pass Rate</h3>
        <p>${passRate}%</p>
      </div>
    </div>
  </div>
  
  <h2>Test Suites</h2>
  ${data.suites.map(suite => `
    <div class="suite">
      <div class="suite-header">
        <h3>${suite.name}</h3>
        <div class="suite-stats">
          <span>Passed: ${suite.passed}/${suite.total}</span>
          <span>Failed: ${suite.failed}</span>
          <span>Skipped: ${suite.skipped}</span>
          <span>Duration: ${(suite.duration / 1000).toFixed(2)}s</span>
        </div>
      </div>
      <div class="suite-body">
        <table>
          <thead>
            <tr>
              <th>Test</th>
              <th>Status</th>
              <th>Duration</th>
            </tr>
          </thead>
          <tbody>
            ${suite.tests.map(test => `
              <tr>
                <td>${test.name}</td>
                <td><span class="status status-${test.status}">${test.status.toUpperCase()}</span></td>
                <td>${(test.duration / 1000).toFixed(3)}s</td>
              </tr>
              ${test.error ? `
              <tr>
                <td colspan="3">
                  <div class="error">${test.error}</div>
                </td>
              </tr>
              ` : ''}
            `).join('')}
          </tbody>
        </table>
      </div>
    </div>
  `).join('')}
  
  <footer>
    <p>Comphyon Framework - Powered by Comphyology</p>
  </footer>
</body>
</html>`;
}

// Generate test report
generateTestReport();
