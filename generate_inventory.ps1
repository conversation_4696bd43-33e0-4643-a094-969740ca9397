# PowerShell script to generate a comprehensive file inventory
$outputFile = "$PSScriptRoot\CODEBASE_INVENTORY_$(Get-Date -Format 'yyyyMMdd_HHmmss').md"
$baseDir = "d:\novafuse-api-superstore"

# Create output directory if it doesn't exist
$outputDir = [System.IO.Path]::GetDirectoryName($outputFile)
if (-not (Test-Path $outputDir)) {
    New-Item -ItemType Directory -Path $outputDir -Force | Out-Null
}

# Function to get file information
function Get-FileInventory {
    param (
        [string]$directory = $PSScriptRoot,
        [string]$outputFile
    )
    
    # Get all files recursively, excluding node_modules and other common directories
    $files = Get-ChildItem -Path $directory -Recurse -File | 
        Where-Object { $_.FullName -notmatch '\\node_modules\\' -and 
                     $_.FullName -notmatch '\\.git\\' -and
                     $_.FullName -notmatch '\\dist\\' -and
                     $_.FullName -notmatch '\\build\\' }
    
    # Group files by directory
    $filesByDir = $files | Group-Object DirectoryName
    
    # Group files by date (year-month)
    $filesByDate = $files | Group-Object { $_.LastWriteTime.ToString('yyyy-MM') } | Sort-Object Name -Descending
    
    # Generate markdown content
    $markdown = "# Codebase Inventory
*Generated on $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')*

## Summary
- **Total Files**: $($files.Count)
- **Total Size**: $([math]::Round(($files | Measure-Object -Property Length -Sum).Sum / 1MB, 2)) MB
- **Earliest Modified**: $(($files | Sort-Object LastWriteTime | Select-Object -First 1).LastWriteTime)
- **Most Recent**: $(($files | Sort-Object LastWriteTime -Descending | Select-Object -First 1).LastWriteTime)

## Files by Date (Newest First)
"
    
    # Add files grouped by date
    foreach ($dateGroup in $filesByDate) {
        $markdown += "### $($dateGroup.Name)"
        $markdown += "`n| File | Size | Last Modified | Path |"
        $markdown += "`n|------|------|--------------|------|"
        
        foreach ($file in ($dateGroup.Group | Sort-Object LastWriteTime -Descending)) {
            $relativePath = $file.FullName.Replace($baseDir, '').TrimStart('\\')
            $markdown += "`n| $($file.Name) | $($file.Length/1KB | [math]::Round(2)) KB | $($file.LastWriteTime) | $($relativePath) |"
        }
        $markdown += "`n"
    }
    
    # Add directory structure
    $markdown += "`n## Directory Structure`n"
    $markdown += "```"
    function Get-DirectoryTree {
        param (
            [string]$path,
            [string]$indent = ''
        )
        
        $dir = Get-Item $path
        $result = $indent + $dir.Name + "\n"
        
        $subDirs = Get-ChildItem -Path $path -Directory | Sort-Object Name
        $files = Get-ChildItem -Path $path -File | Sort-Object Name
        
        foreach ($subDir in $subDirs) {
            $result += Get-DirectoryTree -path $subDir.FullName -indent ($indent + '    ')
        }
        
        foreach ($file in $files) {
            $result += $indent + '    ' + $file.Name + "\n"
        }
        
        return $result
    }
    
    $markdown += Get-DirectoryTree -path $baseDir
    $markdown += "```"
    
    # Write to file
    try {
        $markdown | Out-File -FilePath $outputFile -Encoding utf8 -Force
        Write-Host "Inventory generated: $outputFile"
    } catch {
        Write-Error "Error writing to file: $_"
        $outputFile = ".\CODEBASE_INVENTORY_$(Get-Date -Format 'yyyyMMdd_HHmmss').md"
        $markdown | Out-File -FilePath $outputFile -Encoding utf8 -Force
        Write-Host "Inventory generated in current directory: $((Get-Item $outputFile).FullName)"
    }
}

# Execute the function
Get-FileInventory -directory $baseDir -outputFile $outputFile
