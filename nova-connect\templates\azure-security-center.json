{"metadata": {"name": "Azure Security Center", "version": "1.0.0", "category": "Cloud Security", "description": "Connect to Microsoft Azure Security Center for continuous security monitoring and compliance", "author": "NovaGRC", "tags": ["azure", "security", "compliance", "cloud"], "created": "2025-01-01T00:00:00Z", "updated": "2025-01-01T00:00:00Z", "icon": "https://azure.microsoft.com/svghandler/security-center?width=600&height=315"}, "authentication": {"type": "OAUTH2", "fields": {"clientId": {"type": "string", "description": "Azure AD Application (client) ID", "required": true}, "clientSecret": {"type": "string", "description": "Azure AD Application client secret", "required": true, "sensitive": true}, "tenantId": {"type": "string", "description": "Azure AD Directory (tenant) ID", "required": true}}, "oauth2Config": {"tokenUrl": "https://login.microsoftonline.com/{tenantId}/oauth2/v2.0/token", "grantType": "client_credentials", "scopes": ["https://management.azure.com/.default"]}, "testConnection": {"endpoint": "/subscriptions/{subscriptionId}/providers/Microsoft.Security/securityStatuses?api-version=2020-01-01", "method": "GET", "expectedResponse": {"status": 200}}}, "configuration": {"baseUrl": "https://management.azure.com", "headers": {"Content-Type": "application/json"}, "rateLimit": {"requests": 100, "period": "1m"}, "timeout": 30000, "retryPolicy": {"maxRetries": 3, "backoffStrategy": "exponential"}}, "endpoints": [{"id": "getSecurityAlerts", "name": "Get Security Alerts", "description": "Get security alerts from Azure Security Center", "path": "/subscriptions/{subscriptionId}/providers/Microsoft.Security/alerts", "method": "GET", "parameters": {"query": {"api-version": "2020-01-01"}, "path": {"subscriptionId": {"type": "string", "description": "Azure Subscription ID", "required": true}}}, "pagination": {"type": "nextLink", "parameters": {"nextLink": "nextLink"}}, "response": {"successCode": 200, "schema": {"value": "array", "nextLink": "string"}}}, {"id": "getSecurityAssessments", "name": "Get Security Assessments", "description": "Get security assessments from Azure Security Center", "path": "/subscriptions/{subscriptionId}/providers/Microsoft.Security/assessments", "method": "GET", "parameters": {"query": {"api-version": "2020-01-01"}, "path": {"subscriptionId": {"type": "string", "description": "Azure Subscription ID", "required": true}}}, "pagination": {"type": "nextLink", "parameters": {"nextLink": "nextLink"}}, "response": {"successCode": 200, "schema": {"value": "array", "nextLink": "string"}}}, {"id": "getComplianceResults", "name": "Get Compliance Results", "description": "Get regulatory compliance results from Azure Security Center", "path": "/subscriptions/{subscriptionId}/providers/Microsoft.Security/regulatoryComplianceStandards", "method": "GET", "parameters": {"query": {"api-version": "2019-01-01-preview"}, "path": {"subscriptionId": {"type": "string", "description": "Azure Subscription ID", "required": true}}}, "pagination": {"type": "nextLink", "parameters": {"nextLink": "nextLink"}}, "response": {"successCode": 200, "schema": {"value": "array", "nextLink": "string"}}}], "mappings": [{"sourceEndpoint": "getSecurityAlerts", "targetSystem": "NovaGRC", "targetEntity": "SecurityAlerts", "transformations": [{"source": "$.value[*].properties.alertDisplayName", "target": "title", "transform": "identity"}, {"source": "$.value[*].properties.description", "target": "description", "transform": "identity"}, {"source": "$.value[*].properties.severity", "target": "severity", "transform": "mapSeverity"}, {"source": "$.value[*].properties.status", "target": "status", "transform": "identity"}, {"source": "$.value[*].properties.resourceDetails", "target": "affectedResources", "transform": "extractResourceDetails"}]}, {"sourceEndpoint": "getComplianceResults", "targetSystem": "NovaGRC", "targetEntity": "ComplianceFindings", "transformations": [{"source": "$.value[*].properties.standardDisplayName", "target": "framework", "transform": "identity"}, {"source": "$.value[*].properties.state", "target": "complianceStatus", "transform": "mapComplianceStatus"}, {"source": "$.value[*].id", "target": "sourceId", "transform": "identity"}]}], "events": {"polling": [{"endpoint": "getSecurityAlerts", "interval": "15m", "condition": "has<PERSON>ew<PERSON><PERSON><PERSON>"}, {"endpoint": "getComplianceResults", "interval": "1h", "condition": "hasComplianceChanges"}]}}