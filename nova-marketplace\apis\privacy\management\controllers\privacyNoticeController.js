/**
 * Privacy Notice Controller
 *
 * This controller handles operations related to privacy notices.
 */

const { privacyNoticeService } = require('../services');

// Get all privacy notices with pagination and filtering
const getAllPrivacyNotices = async (req, res, next) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;

    // Build filter object
    const filter = {};

    if (req.query.status) {
      filter.status = req.query.status;
    }

    if (req.query.audience) {
      filter.audience = req.query.audience;
    }

    if (req.query.language) {
      filter.language = req.query.language;
    }

    if (req.query.version) {
      filter.version = req.query.version;
    }

    if (req.query.search) {
      filter.$text = { $search: req.query.search };
    }

    // Build sort object
    const sort = {};

    if (req.query.sortBy) {
      sort[req.query.sortBy] = req.query.sortOrder === 'desc' ? -1 : 1;
    } else {
      sort.effectiveDate = -1; // Default sort by effective date descending
    }

    // Use the service to get the privacy notices
    const result = await privacyNoticeService.getAllPrivacyNotices({
      page,
      limit,
      filter,
      sort
    });

    res.json(result);
  } catch (error) {
    next(error);
  }
};

// Get a specific privacy notice by ID
const getPrivacyNoticeById = async (req, res, next) => {
  try {
    const privacyNotice = await privacyNoticeService.getPrivacyNoticeById(req.params.id);

    res.json({
      data: privacyNotice
    });
  } catch (error) {
    next(error);
  }
};

// Create a new privacy notice
const createPrivacyNotice = async (req, res, next) => {
  try {
    // Add revision history entry
    const noticeData = { ...req.body };
    noticeData.revisionHistory = [{
      version: noticeData.version || 1,
      date: new Date(),
      changes: 'Initial version',
      changedBy: req.user.id
    }];

    const privacyNotice = await privacyNoticeService.createPrivacyNotice(noticeData);

    res.status(201).json({
      data: privacyNotice,
      message: 'Privacy notice created successfully'
    });
  } catch (error) {
    next(error);
  }
};

// Update a privacy notice
const updatePrivacyNotice = async (req, res, next) => {
  try {
    // Get the current privacy notice to check for version update
    const currentNotice = await privacyNoticeService.getPrivacyNoticeById(req.params.id);

    // Check if version is being updated
    const isVersionUpdate = req.body.version && req.body.version !== currentNotice.version;

    // Prepare update data
    const updateData = { ...req.body };

    // Add revision history entry if version is updated
    if (isVersionUpdate) {
      if (!updateData.revisionHistory) {
        updateData.revisionHistory = [...currentNotice.revisionHistory];
      }

      updateData.revisionHistory.push({
        version: req.body.version,
        date: new Date(),
        changes: req.body.changes || 'Updated version',
        changedBy: req.user.id
      });
    }

    // Update the privacy notice
    const privacyNotice = await privacyNoticeService.updatePrivacyNotice(req.params.id, updateData);

    res.json({
      data: privacyNotice,
      message: 'Privacy notice updated successfully'
    });
  } catch (error) {
    next(error);
  }
};

// Delete a privacy notice
const deletePrivacyNotice = async (req, res, next) => {
  try {
    await privacyNoticeService.deletePrivacyNotice(req.params.id);

    res.json({
      message: 'Privacy notice deleted successfully'
    });
  } catch (error) {
    next(error);
  }
};

// Archive a privacy notice
const archivePrivacyNotice = async (req, res, next) => {
  try {
    const privacyNotice = await privacyNoticeService.archivePrivacyNotice(req.params.id);

    res.json({
      data: privacyNotice,
      message: 'Privacy notice archived successfully'
    });
  } catch (error) {
    next(error);
  }
};

// Get privacy notices by audience
const getPrivacyNoticesByAudience = async (req, res, next) => {
  try {
    const audience = req.params.audience;

    // Use the service to get privacy notices with filter
    const result = await privacyNoticeService.getAllPrivacyNotices({
      filter: {
        audience,
        status: 'active'
      },
      sort: { effectiveDate: -1 }
    });

    res.json({
      data: result.data
    });
  } catch (error) {
    next(error);
  }
};

// Get the latest version of a privacy notice by audience and language
const getLatestPrivacyNotice = async (req, res, next) => {
  try {
    const { type, audience, language } = req.query;

    if (!audience) {
      const error = new Error('Audience is required');
      error.name = 'ValidationError';
      throw error;
    }

    if (!type) {
      const error = new Error('Type is required');
      error.name = 'ValidationError';
      throw error;
    }

    const privacyNotice = await privacyNoticeService.getLatestPrivacyNotice(type, audience, language);

    res.json({
      data: privacyNotice
    });
  } catch (error) {
    next(error);
  }
};

// Compare two versions of a privacy notice
const comparePrivacyNoticeVersions = async (req, res, next) => {
  try {
    const { id1, id2 } = req.query;

    if (!id1 || !id2) {
      const error = new Error('Both privacy notice IDs are required');
      error.name = 'ValidationError';
      throw error;
    }

    const comparison = await privacyNoticeService.comparePrivacyNotices(id1, id2);

    res.json({
      data: comparison,
      message: 'Privacy notice versions compared successfully'
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  getAllPrivacyNotices,
  getPrivacyNoticeById,
  createPrivacyNotice,
  updatePrivacyNotice,
  deletePrivacyNotice,
  archivePrivacyNotice,
  getPrivacyNoticesByAudience,
  getLatestPrivacyNotice,
  comparePrivacyNoticeVersions
};
