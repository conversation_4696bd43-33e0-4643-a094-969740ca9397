/**
 * NovaCore Cyber-Safety Utilities
 * 
 * This file provides utility functions for the Cyber-Safety platform.
 */

const crypto = require('crypto');

/**
 * Generate a hash for an operation
 * @param {Object} req - Express request object or operation context
 * @returns {string} - Operation hash
 */
function generateOperationHash(req) {
  try {
    // Extract relevant data
    const data = {
      path: req.path || req.endpoint,
      method: req.method,
      timestamp: req.timestamp || new Date().toISOString(),
      user: req.user ? (typeof req.user === 'string' ? req.user : req.user.id) : 'system',
      body: req.body || req.requestData || {}
    };
    
    // Create deterministic JSON string
    const jsonString = JSON.stringify(data, (key, value) => {
      // Sort object keys for deterministic output
      if (value && typeof value === 'object' && !Array.isArray(value)) {
        return Object.keys(value).sort().reduce((result, key) => {
          result[key] = value[key];
          return result;
        }, {});
      }
      return value;
    });
    
    // Generate hash
    return crypto.createHash('sha256').update(jsonString).digest('hex');
  } catch (error) {
    console.error('Error generating operation hash:', error);
    
    // Fallback to simple hash
    const fallbackString = `${req.method || 'unknown'}-${req.path || req.endpoint || 'unknown'}-${Date.now()}`;
    return crypto.createHash('sha256').update(fallbackString).digest('hex');
  }
}

/**
 * Generate a safety hash for data
 * @param {Object|string} data - Data to hash
 * @returns {string} - Safety hash
 */
function generateSafetyHash(data) {
  try {
    // Convert data to string if it's an object
    const dataString = typeof data === 'object' ? JSON.stringify(data) : String(data);
    
    // Generate hash
    return crypto.createHash('sha256').update(dataString).digest('hex');
  } catch (error) {
    console.error('Error generating safety hash:', error);
    
    // Fallback to simple hash
    return crypto.createHash('sha256').update(String(Date.now())).digest('hex');
  }
}

/**
 * Sanitize sensitive data
 * @param {Object} data - Data to sanitize
 * @param {Array<string>} [sensitiveFields] - List of sensitive fields
 * @returns {Object} - Sanitized data
 */
function sanitizeSensitiveData(data, sensitiveFields = []) {
  if (!data || typeof data !== 'object') {
    return data;
  }
  
  // Default sensitive fields
  const defaultSensitiveFields = [
    'password', 'token', 'secret', 'key', 'apiKey', 'api_key', 'auth',
    'authorization', 'credential', 'credentials', 'accessToken', 'refreshToken'
  ];
  
  // Combine with custom sensitive fields
  const allSensitiveFields = [...defaultSensitiveFields, ...sensitiveFields];
  
  // Create a deep copy
  const sanitized = JSON.parse(JSON.stringify(data));
  
  // Recursively sanitize object
  function sanitizeObject(obj) {
    if (!obj || typeof obj !== 'object') {
      return;
    }
    
    for (const key in obj) {
      if (allSensitiveFields.some(field => key.toLowerCase().includes(field.toLowerCase()))) {
        obj[key] = '[REDACTED]';
      } else if (typeof obj[key] === 'object') {
        sanitizeObject(obj[key]);
      }
    }
  }
  
  sanitizeObject(sanitized);
  
  return sanitized;
}

/**
 * Calculate safety score
 * @param {Object} components - Score components
 * @returns {number} - Safety score (0-100)
 */
function calculateSafetyScore(components) {
  // Default weights
  const weights = {
    risk: 0.4,
    compliance: 0.3,
    policy: 0.3
  };
  
  // Calculate weighted score
  let score = 0;
  let totalWeight = 0;
  
  for (const [component, weight] of Object.entries(weights)) {
    if (components[component] !== undefined) {
      score += components[component] * weight;
      totalWeight += weight;
    }
  }
  
  // Normalize score if weights don't add up to 1
  if (totalWeight > 0 && totalWeight !== 1) {
    score = score / totalWeight;
  }
  
  // Ensure score is between 0 and 100
  return Math.min(100, Math.max(0, Math.round(score)));
}

/**
 * Determine safety level from score
 * @param {number} score - Safety score
 * @returns {string} - Safety level
 */
function determineSafetyLevel(score) {
  if (score >= 90) {
    return 'excellent';
  } else if (score >= 75) {
    return 'good';
  } else if (score >= 60) {
    return 'moderate';
  } else if (score >= 40) {
    return 'fair';
  } else {
    return 'poor';
  }
}

module.exports = {
  generateOperationHash,
  generateSafetyHash,
  sanitizeSensitiveData,
  calculateSafetyScore,
  determineSafetyLevel
};
