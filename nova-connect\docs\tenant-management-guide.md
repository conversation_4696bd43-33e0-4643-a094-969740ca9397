# NovaConnect UAC Tenant Management Guide

This guide provides instructions for managing tenants in NovaConnect UAC.

## Prerequisites

Before you can manage tenants, you need to have the following:

- Google Cloud SDK installed and configured
- kubectl installed and configured
- Helm installed
- Access to the NovaConnect UAC Google Cloud project

## Provisioning a New Tenant

### Using the Provisioning Script

The easiest way to provision a new tenant is to use the provisioning script:

```bash
# Linux/Mac
./scripts/provision-tenant.sh <tenant_id> [tenant_name] [tier] [replica_count] [cpu_request] [memory_request] [cpu_limit] [memory_limit] [pod_limit]

# Windows
scripts\provision-tenant.bat <tenant_id> [tenant_name] [tier] [replica_count] [cpu_request] [memory_request] [cpu_limit] [memory_limit] [pod_limit]
```

### Parameters

- `tenant_id` (required): Unique identifier for the tenant (e.g., `acme-corp`)
- `tenant_name` (optional): Display name for the tenant (e.g., `Acme Corporation`)
- `tier` (optional): Subscription tier (`core`, `secure`, `enterprise`, or `ai_boost`)
- `replica_count` (optional): Number of replicas for the tenant's deployment
- `cpu_request` (optional): CPU request for the tenant's pods
- `memory_request` (optional): Memory request for the tenant's pods
- `cpu_limit` (optional): CPU limit for the tenant's pods
- `memory_limit` (optional): Memory limit for the tenant's pods
- `pod_limit` (optional): Maximum number of pods for the tenant

### Example

```bash
# Linux/Mac
./scripts/provision-tenant.sh acme-corp "Acme Corporation" enterprise 5 1000m 1Gi 2000m 2Gi 30

# Windows
scripts\provision-tenant.bat acme-corp "Acme Corporation" enterprise 5 1000m 1Gi 2000m 2Gi 30
```

This will provision a new tenant with the following configuration:

- Tenant ID: `acme-corp`
- Tenant Name: `Acme Corporation`
- Tier: `enterprise`
- Replica Count: `5`
- CPU Request: `1000m`
- Memory Request: `1Gi`
- CPU Limit: `2000m`
- Memory Limit: `2Gi`
- Pod Limit: `30`

## Deprovisioning a Tenant

### Using the Deprovisioning Script

To deprovision a tenant, use the deprovisioning script:

```bash
# Linux/Mac
./scripts/deprovision-tenant.sh <tenant_id> [backup]

# Windows
scripts\deprovision-tenant.bat <tenant_id> [backup]
```

### Parameters

- `tenant_id` (required): Unique identifier for the tenant to deprovision
- `backup` (optional): Whether to backup tenant data before deprovisioning (`true` or `false`)

### Example

```bash
# Linux/Mac
./scripts/deprovision-tenant.sh acme-corp true

# Windows
scripts\deprovision-tenant.bat acme-corp true
```

This will deprovision the tenant with ID `acme-corp` and backup the tenant's data before deprovisioning.

## Managing Tenant Resources

### Viewing Tenant Resources

To view the resources for a specific tenant, use the following commands:

```bash
# View all resources in the tenant's namespace
kubectl get all -n tenant-<tenant_id>

# View the tenant's pods
kubectl get pods -n tenant-<tenant_id>

# View the tenant's services
kubectl get services -n tenant-<tenant_id>

# View the tenant's deployments
kubectl get deployments -n tenant-<tenant_id>

# View the tenant's resource quota
kubectl get resourcequota -n tenant-<tenant_id>

# View the tenant's network policy
kubectl get networkpolicy -n tenant-<tenant_id>
```

### Scaling Tenant Resources

To scale the resources for a specific tenant, use the following commands:

```bash
# Scale the tenant's deployment
kubectl scale deployment tenant-<tenant_id>-novafuse-uac -n tenant-<tenant_id> --replicas=<replica_count>

# Update the tenant's resource quota
kubectl apply -f - <<EOF
apiVersion: v1
kind: ResourceQuota
metadata:
  name: tenant-quota
  namespace: tenant-<tenant_id>
spec:
  hard:
    requests.cpu: "<cpu_request>"
    requests.memory: <memory_request>
    limits.cpu: "<cpu_limit>"
    limits.memory: <memory_limit>
    pods: "<pod_limit>"
EOF
```

### Updating Tenant Configuration

To update the configuration for a specific tenant, use the following commands:

```bash
# Update the tenant's Helm release
helm upgrade tenant-<tenant_id> ./marketplace/chart \
  --namespace tenant-<tenant_id> \
  --set tenant.id=<tenant_id> \
  --set tenant.name="<tenant_name>" \
  --set tier=<tier> \
  --set replicaCount=<replica_count> \
  --set resources.requests.cpu=<cpu_request> \
  --set resources.requests.memory=<memory_request> \
  --set resources.limits.cpu=<cpu_limit> \
  --set resources.limits.memory=<memory_limit>
```

## Monitoring Tenant Resources

### Viewing Tenant Logs

To view the logs for a specific tenant, use the following commands:

```bash
# View logs for all pods in the tenant's namespace
kubectl logs -n tenant-<tenant_id> -l app=novafuse-uac

# View logs for a specific pod
kubectl logs -n tenant-<tenant_id> <pod_name>

# Stream logs for a specific pod
kubectl logs -n tenant-<tenant_id> <pod_name> -f
```

### Viewing Tenant Metrics

To view metrics for a specific tenant, use the Google Cloud Console:

1. Go to the Google Cloud Console
2. Navigate to Monitoring > Dashboards
3. Select the tenant-specific dashboard (`tenant-<tenant_id>`)

### Viewing Tenant Audit Logs

To view audit logs for a specific tenant, use the following BigQuery query:

```sql
SELECT *
FROM `<project_id>.tenant_<tenant_id>.audit_logs`
ORDER BY timestamp DESC
LIMIT 100
```

## Troubleshooting Tenant Issues

### Common Issues

#### Tenant Provisioning Fails

If tenant provisioning fails, check the following:

- Ensure that you have the necessary permissions to create resources in the Google Cloud project
- Check the logs for the provisioning script
- Verify that the tenant ID is unique and follows the naming conventions

#### Tenant Deprovisioning Fails

If tenant deprovisioning fails, check the following:

- Ensure that you have the necessary permissions to delete resources in the Google Cloud project
- Check the logs for the deprovisioning script
- Verify that the tenant ID exists

#### Tenant Resources Not Available

If tenant resources are not available, check the following:

- Verify that the tenant's pods are running
- Check the logs for the tenant's pods
- Verify that the tenant's services are properly configured
- Check the tenant's resource quota to ensure it has sufficient resources

### Getting Help

If you encounter issues that you cannot resolve, contact NovaFuse <NAME_EMAIL>.
