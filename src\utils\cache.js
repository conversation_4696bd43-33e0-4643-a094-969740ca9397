/**
 * NovaFuse Universal API Connector - Cache Utility
 *
 * This module provides a caching system for improved performance.
 */

const { createLogger } = require('./logger');

const logger = createLogger('cache');

/**
 * In-memory cache implementation
 */
class Cache {
  constructor(options = {}) {
    this.options = {
      defaultTTL: options.defaultTTL || 300, // 5 minutes
      checkInterval: options.checkInterval || 60, // 1 minute
      maxSize: options.maxSize || 1000, // Maximum number of items
      ...options
    };

    this.cache = new Map();
    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      evictions: 0
    };

    // Start cleanup interval
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, this.options.checkInterval * 1000);

    logger.info('Cache initialized', {
      defaultTTL: this.options.defaultTTL,
      checkInterval: this.options.checkInterval,
      maxSize: this.options.maxSize
    });
  }

  /**
   * Set a value in the cache
   *
   * @param {string} key - The cache key
   * @param {*} value - The value to cache
   * @param {number} ttl - Time to live in seconds
   * @returns {boolean} - Whether the value was set
   */
  set(key, value, ttl = this.options.defaultTTL) {
    // Check if we need to evict items
    if (this.cache.size >= this.options.maxSize && !this.cache.has(key)) {
      this.evict();
    }

    const expires = Date.now() + (ttl * 1000);

    this.cache.set(key, {
      value,
      expires,
      lastAccessed: Date.now()
    });

    this.stats.sets++;

    logger.debug('Cache set', { key, ttl });

    return true;
  }

  /**
   * Get a value from the cache
   *
   * @param {string} key - The cache key
   * @returns {*} - The cached value or undefined if not found
   */
  get(key) {
    const item = this.cache.get(key);

    if (!item) {
      this.stats.misses++;
      logger.debug('Cache miss', { key });
      return undefined;
    }

    // Check if item has expired
    if (item.expires < Date.now()) {
      this.cache.delete(key);
      this.stats.misses++;
      logger.debug('Cache expired', { key });
      return undefined;
    }

    // Update last accessed time
    item.lastAccessed = Date.now();

    this.stats.hits++;
    logger.debug('Cache hit', { key });

    return item.value;
  }

  /**
   * Check if a key exists in the cache
   *
   * @param {string} key - The cache key
   * @returns {boolean} - Whether the key exists
   */
  has(key) {
    const item = this.cache.get(key);

    if (!item) {
      return false;
    }

    // Check if item has expired
    if (item.expires < Date.now()) {
      this.cache.delete(key);
      return false;
    }

    return true;
  }

  /**
   * Delete a value from the cache
   *
   * @param {string} key - The cache key
   * @returns {boolean} - Whether the key was deleted
   */
  delete(key) {
    logger.debug('Cache delete', { key });
    return this.cache.delete(key);
  }

  /**
   * Clear all values from the cache
   */
  clear() {
    logger.info('Cache cleared');
    this.cache.clear();
  }

  /**
   * Clean up expired items
   */
  cleanup() {
    const now = Date.now();
    let expiredCount = 0;

    for (const [key, item] of this.cache.entries()) {
      if (item.expires < now) {
        this.cache.delete(key);
        expiredCount++;
      }
    }

    if (expiredCount > 0) {
      logger.debug('Cache cleanup', { expiredCount });
    }
  }

  /**
   * Evict items from the cache
   *
   * @private
   */
  evict() {
    // Evict least recently used items
    const items = Array.from(this.cache.entries())
      .sort((a, b) => a[1].lastAccessed - b[1].lastAccessed);

    // Evict 10% of items or at least 1
    const evictCount = Math.max(1, Math.floor(this.cache.size * 0.1));

    for (let i = 0; i < evictCount && i < items.length; i++) {
      this.cache.delete(items[i][0]);
      this.stats.evictions++;
    }

    logger.debug('Cache eviction', { evictCount });
  }

  /**
   * Get cache statistics
   *
   * @returns {Object} - The cache statistics
   */
  getStats() {
    return {
      size: this.cache.size,
      maxSize: this.options.maxSize,
      hitRate: this.stats.hits / (this.stats.hits + this.stats.misses) || 0,
      ...this.stats
    };
  }

  /**
   * Reset cache statistics
   *
   * @returns {Object} - The reset statistics
   */
  resetStats() {
    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      evictions: 0
    };

    logger.info('Cache statistics reset');

    return this.getStats();
  }

  /**
   * Stop the cache cleanup interval
   */
  stop() {
    clearInterval(this.cleanupInterval);
    logger.info('Cache stopped');
  }
}

// Create singleton instance
const cache = new Cache();

module.exports = cache;
