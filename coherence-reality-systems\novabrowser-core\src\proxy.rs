#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)]
pub struct ProxyEndpoint {
  /// Proxy server host (e.g. *************, localhost, example.com, etc.)
  pub host: String,
  /// Proxy server port (e.g. 1080, 3128, etc.)
  pub port: String,
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub enum ProxyConfig {
  /// Connect to proxy server via HTTP CONNECT
  Http(ProxyEndpoint),
  /// Connect to proxy server via SOCKSv5
  Socks5(ProxyEndpoint),
}
