/**
 * CSFE Governor Component
 * 
 * This module implements the CSFE Governor component, which monitors financial entropy
 * thresholds and executes control actions when thresholds are exceeded. It mirrors the
 * Comphyon Governor but is tailored for financial systems.
 * 
 * Key features include:
 * - Tiered Alerts: Ψ-Warning, Ψ-Intervention, Ψ-Lockdown
 * - Control Actions: Auto-Isolation, Liquidity Circuit Breakers, Dynamic Key Rotation
 */

const { EventEmitter } = require('events');
const { performance } = require('perf_hooks');

/**
 * CSFEGovernor class
 */
class CSFEGovernor extends EventEmitter {
  /**
   * Create a new CSFEGovernor instance
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      thresholds: {
        warning: 0.6, // Ψ-Warning threshold
        intervention: 0.8, // Ψ-Intervention threshold
        lockdown: 0.95 // Ψ-Lockdown threshold
      },
      controlActions: {
        autoIsolation: true, // Enable auto-isolation of compromised nodes
        liquidityCircuitBreakers: true, // Enable liquidity circuit breakers
        dynamicKeyRotation: true // Enable dynamic key rotation
      },
      humanOversight: {
        required: true, // Require human oversight for critical actions
        timeoutMs: 300000 // 5 minutes timeout for human response
      },
      enableLogging: true, // Enable logging
      enableMetrics: true, // Enable performance metrics
      ...options
    };
    
    // Initialize state
    this.state = {
      currentStatus: 'normal', // normal, warning, intervention, lockdown
      activeControls: [], // Active control actions
      pendingApprovals: [], // Actions pending human approval
      controlHistory: [], // History of control actions
      lastUpdateTime: Date.now(),
      isRunning: false
    };
    
    // Initialize metrics
    this.metrics = {
      processingTimeMs: 0,
      totalUpdates: 0,
      controlsExecuted: 0,
      controlsRejected: 0
    };
    
    console.log('CSFE Governor initialized');
  }
  
  /**
   * Start the governor
   * @returns {boolean} - Success status
   */
  start() {
    if (this.state.isRunning) {
      console.log('CSFE Governor is already running');
      return false;
    }
    
    this.state.isRunning = true;
    console.log('CSFE Governor started');
    this.emit('start');
    
    return true;
  }
  
  /**
   * Stop the governor
   * @returns {boolean} - Success status
   */
  stop() {
    if (!this.state.isRunning) {
      console.log('CSFE Governor is not running');
      return false;
    }
    
    this.state.isRunning = false;
    console.log('CSFE Governor stopped');
    this.emit('stop');
    
    return true;
  }
  
  /**
   * Process financial entropy alert
   * @param {Object} alert - Alert data from CSFE Meter
   * @returns {Object} - Processing result
   */
  processAlert(alert) {
    if (!this.state.isRunning) {
      return { success: false, reason: 'Governor is not running' };
    }
    
    const startTime = performance.now();
    this.metrics.totalUpdates++;
    
    // Extract alert data
    const { level, reason, entropy, velocity, acceleration, timestamp } = alert;
    
    // Determine governor status based on entropy level
    let newStatus = 'normal';
    if (entropy >= this.options.thresholds.lockdown) {
      newStatus = 'lockdown';
    } else if (entropy >= this.options.thresholds.intervention) {
      newStatus = 'intervention';
    } else if (entropy >= this.options.thresholds.warning) {
      newStatus = 'warning';
    }
    
    // Update state
    const previousStatus = this.state.currentStatus;
    this.state.currentStatus = newStatus;
    this.state.lastUpdateTime = Date.now();
    
    // Generate control actions if status changed
    let controlActions = [];
    if (newStatus !== previousStatus) {
      controlActions = this._generateControlActions(newStatus, entropy, velocity, acceleration);
      
      // Execute control actions
      this._executeControlActions(controlActions);
    }
    
    // Update metrics
    this.metrics.processingTimeMs += performance.now() - startTime;
    
    // Emit update event
    this.emit('status-update', {
      previousStatus,
      currentStatus: newStatus,
      entropy,
      timestamp: Date.now()
    });
    
    return {
      success: true,
      previousStatus,
      currentStatus: newStatus,
      controlActions,
      timestamp: Date.now()
    };
  }
  
  /**
   * Get current governor state
   * @returns {Object} - Current state
   */
  getState() {
    return { ...this.state };
  }
  
  /**
   * Get governor metrics
   * @returns {Object} - Metrics
   */
  getMetrics() {
    return { ...this.metrics };
  }
  
  /**
   * Approve pending control action
   * @param {string} actionId - ID of the action to approve
   * @param {Object} approvalData - Approval data
   * @returns {Object} - Approval result
   */
  approveControlAction(actionId, approvalData = {}) {
    // Find pending action
    const pendingIndex = this.state.pendingApprovals.findIndex(a => a.id === actionId);
    if (pendingIndex === -1) {
      return { success: false, reason: 'Action not found or already processed' };
    }
    
    const pendingAction = this.state.pendingApprovals[pendingIndex];
    
    // Remove from pending
    this.state.pendingApprovals.splice(pendingIndex, 1);
    
    // Execute action
    const result = this._executeControlAction(pendingAction.action, true);
    
    // Add approval data
    result.approvedBy = approvalData.approvedBy || 'unknown';
    result.approvalNotes = approvalData.notes || '';
    
    // Add to history
    this._addToHistory(result);
    
    // Emit approval event
    this.emit('action-approved', result);
    
    return result;
  }
  
  /**
   * Reject pending control action
   * @param {string} actionId - ID of the action to reject
   * @param {Object} rejectionData - Rejection data
   * @returns {Object} - Rejection result
   */
  rejectControlAction(actionId, rejectionData = {}) {
    // Find pending action
    const pendingIndex = this.state.pendingApprovals.findIndex(a => a.id === actionId);
    if (pendingIndex === -1) {
      return { success: false, reason: 'Action not found or already processed' };
    }
    
    const pendingAction = this.state.pendingApprovals[pendingIndex];
    
    // Remove from pending
    this.state.pendingApprovals.splice(pendingIndex, 1);
    
    // Create rejection result
    const result = {
      id: pendingAction.id,
      action: pendingAction.action,
      status: 'rejected',
      success: false,
      reason: 'Rejected by human oversight',
      rejectedBy: rejectionData.rejectedBy || 'unknown',
      rejectionNotes: rejectionData.notes || '',
      timestamp: Date.now()
    };
    
    // Update metrics
    this.metrics.controlsRejected++;
    
    // Add to history
    this._addToHistory(result);
    
    // Emit rejection event
    this.emit('action-rejected', result);
    
    return result;
  }
  
  /**
   * Generate control actions based on status
   * @param {string} status - Current status
   * @param {number} entropy - Current entropy value
   * @param {number} velocity - Current velocity value
   * @param {number} acceleration - Current acceleration value
   * @returns {Array} - Control actions
   * @private
   */
  _generateControlActions(status, entropy, velocity, acceleration) {
    const actions = [];
    
    switch (status) {
      case 'warning':
        // Ψ-Warning actions
        if (this.options.controlActions.dynamicKeyRotation) {
          actions.push({
            type: 'dynamic_key_rotation',
            severity: 'low',
            description: 'Rotate cryptographic keys for sensitive systems',
            actions: [
              { type: 'rotate', target: 'api_keys', value: 0.5 },
              { type: 'update', target: 'encryption_params', value: 0.7 }
            ],
            reason: 'Proactive security measure due to elevated financial entropy',
            trinity_levels: ['micro']
          });
        }
        break;
        
      case 'intervention':
        // Ψ-Intervention actions
        if (this.options.controlActions.liquidityCircuitBreakers) {
          actions.push({
            type: 'liquidity_circuit_breakers',
            severity: 'medium',
            description: 'Activate circuit breakers to prevent liquidity cascade',
            actions: [
              { type: 'limit', target: 'transaction_volume', value: 0.7 },
              { type: 'monitor', target: 'payment_flows', value: 0.9 }
            ],
            reason: 'Preventing potential liquidity cascade due to high financial entropy',
            trinity_levels: ['micro', 'meso']
          });
        }
        
        if (this.options.controlActions.dynamicKeyRotation) {
          actions.push({
            type: 'dynamic_key_rotation',
            severity: 'medium',
            description: 'Emergency rotation of all cryptographic keys',
            actions: [
              { type: 'rotate', target: 'all_keys', value: 0.9 },
              { type: 'update', target: 'security_params', value: 0.9 }
            ],
            reason: 'Mitigating potential security breach due to high financial entropy',
            trinity_levels: ['micro', 'meso']
          });
        }
        break;
        
      case 'lockdown':
        // Ψ-Lockdown actions
        if (this.options.controlActions.autoIsolation) {
          actions.push({
            type: 'auto_isolation',
            severity: 'critical',
            description: 'Isolate potentially compromised nodes from the network',
            actions: [
              { type: 'isolate', target: 'suspicious_nodes', value: 1.0 },
              { type: 'restrict', target: 'network_access', value: 0.9 }
            ],
            reason: 'Critical financial entropy levels indicating potential system compromise',
            trinity_levels: ['micro', 'meso', 'macro']
          });
        }
        
        if (this.options.controlActions.liquidityCircuitBreakers) {
          actions.push({
            type: 'liquidity_circuit_breakers',
            severity: 'critical',
            description: 'Full system circuit breaker activation',
            actions: [
              { type: 'halt', target: 'all_transactions', value: 1.0 },
              { type: 'freeze', target: 'suspicious_accounts', value: 1.0 }
            ],
            reason: 'Critical financial entropy levels indicating potential system-wide risk',
            trinity_levels: ['micro', 'meso', 'macro']
          });
        }
        break;
    }
    
    return actions;
  }
  
  /**
   * Execute control actions
   * @param {Array} actions - Control actions to execute
   * @private
   */
  _executeControlActions(actions) {
    for (const action of actions) {
      // Check if human oversight is required
      const requiresHumanOversight = 
        this.options.humanOversight.required && 
        (action.severity === 'critical' || action.type === 'auto_isolation');
      
      if (requiresHumanOversight) {
        // Add to pending approvals
        const pendingAction = {
          id: `action-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          action,
          status: 'pending',
          createdAt: Date.now(),
          expiresAt: Date.now() + this.options.humanOversight.timeoutMs
        };
        
        this.state.pendingApprovals.push(pendingAction);
        
        // Emit pending approval event
        this.emit('action-pending', pendingAction);
        
        if (this.options.enableLogging) {
          console.log(`CSFE Governor: Action ${action.type} (${action.severity}) pending human approval`);
        }
      } else {
        // Execute action immediately
        this._executeControlAction(action);
      }
    }
  }
  
  /**
   * Execute a single control action
   * @param {Object} action - Control action to execute
   * @param {boolean} isApproved - Whether the action was approved by human oversight
   * @returns {Object} - Execution result
   * @private
   */
  _executeControlAction(action, isApproved = false) {
    // In a real implementation, this would execute the actual control action
    // For now, just simulate execution
    
    // Create result object
    const result = {
      id: `action-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      action,
      status: 'executed',
      success: true,
      executedAt: Date.now(),
      isApproved
    };
    
    // Update metrics
    this.metrics.controlsExecuted++;
    
    // Add to active controls if applicable
    if (['liquidity_circuit_breakers', 'auto_isolation'].includes(action.type)) {
      this.state.activeControls.push({
        ...result,
        activatedAt: Date.now()
      });
    }
    
    // Add to history
    this._addToHistory(result);
    
    // Emit control action event
    this.emit('control', result);
    
    if (this.options.enableLogging) {
      console.log(`CSFE Governor: Executed ${action.type} (${action.severity}) control action`);
    }
    
    return result;
  }
  
  /**
   * Add control action to history
   * @param {Object} action - Control action
   * @private
   */
  _addToHistory(action) {
    // Add to history
    this.state.controlHistory.unshift(action);
    
    // Limit history size
    if (this.state.controlHistory.length > 100) {
      this.state.controlHistory.pop();
    }
  }
  
  /**
   * Deactivate a control action
   * @param {string} actionId - ID of the action to deactivate
   * @returns {Object} - Deactivation result
   */
  deactivateControl(actionId) {
    // Find active control
    const activeIndex = this.state.activeControls.findIndex(c => c.id === actionId);
    if (activeIndex === -1) {
      return { success: false, reason: 'Control not found or not active' };
    }
    
    const activeControl = this.state.activeControls[activeIndex];
    
    // Remove from active controls
    this.state.activeControls.splice(activeIndex, 1);
    
    // Create deactivation result
    const result = {
      id: `deactivate-${activeControl.id}`,
      originalAction: activeControl,
      status: 'deactivated',
      success: true,
      deactivatedAt: Date.now()
    };
    
    // Add to history
    this._addToHistory(result);
    
    // Emit deactivation event
    this.emit('control-deactivated', result);
    
    if (this.options.enableLogging) {
      console.log(`CSFE Governor: Deactivated ${activeControl.action.type} control action`);
    }
    
    return result;
  }
}

module.exports = CSFEGovernor;
