/**
 * NovaFuse Universal API Connector - Connector Registry Service
 * 
 * This module provides a service for managing the connector registry.
 */

const { Connector } = require('../models/connector');
const { ValidationError, ResourceNotFoundError } = require('../../errors');

/**
 * Connector Registry Service class
 */
class ConnectorRegistryService {
  constructor() {
    this.connectors = new Map();
  }

  /**
   * Get all connectors
   * 
   * @param {Object} filters - Filters to apply
   * @returns {Array<Connector>} - The connectors
   */
  async getAllConnectors(filters = {}) {
    let connectors = Array.from(this.connectors.values());
    
    // Apply filters
    if (filters.status) {
      connectors = connectors.filter(c => c.status === filters.status);
    }
    
    if (filters.type) {
      connectors = connectors.filter(c => c.type === filters.type);
    }
    
    // Default sort by name
    connectors.sort((a, b) => a.name.localeCompare(b.name));
    
    return connectors;
  }

  /**
   * Get a connector by ID
   * 
   * @param {string} id - The connector ID
   * @returns {Connector} - The connector
   */
  async getConnector(id) {
    const connector = this.connectors.get(id);
    
    if (!connector) {
      throw new ResourceNotFoundError('Connector', id);
    }
    
    return connector;
  }

  /**
   * Create a new connector
   * 
   * @param {Object} data - The connector data
   * @returns {Connector} - The created connector
   */
  async createConnector(data) {
    // Create connector
    const connector = new Connector(data);
    
    // Validate connector
    const validation = connector.validate();
    if (!validation.valid) {
      throw new ValidationError('Invalid connector data', { validationErrors: validation.errors });
    }
    
    // Save connector
    this.connectors.set(connector.id, connector);
    
    return connector;
  }

  /**
   * Update a connector
   * 
   * @param {string} id - The connector ID
   * @param {Object} data - The data to update
   * @returns {Connector} - The updated connector
   */
  async updateConnector(id, data) {
    // Get connector
    const connector = await this.getConnector(id);
    
    // Update fields
    Object.assign(connector, {
      ...data,
      id: connector.id, // Ensure ID doesn't change
      updatedAt: new Date().toISOString()
    });
    
    // Validate connector
    const validation = connector.validate();
    if (!validation.valid) {
      throw new ValidationError('Invalid connector data', { validationErrors: validation.errors });
    }
    
    return connector;
  }

  /**
   * Delete a connector
   * 
   * @param {string} id - The connector ID
   * @returns {boolean} - Whether the connector was deleted
   */
  async deleteConnector(id) {
    // Get connector
    await this.getConnector(id);
    
    // Delete connector
    return this.connectors.delete(id);
  }
}

// Create singleton instance
const connectorRegistryService = new ConnectorRegistryService();

module.exports = connectorRegistryService;
