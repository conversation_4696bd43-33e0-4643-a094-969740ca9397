/**
 * <PERSON><PERSON> Component Styles
 */

@import '../../design-system/variables.css';

.nova-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-family: var(--nova-font-family);
  font-size: var(--nova-font-size-button);
  font-weight: var(--nova-btn-font-weight);
  line-height: var(--nova-line-height-body);
  text-align: center;
  text-decoration: none;
  vertical-align: middle;
  cursor: pointer;
  user-select: none;
  border: var(--nova-btn-border-width) solid transparent;
  border-radius: var(--nova-btn-border-radius);
  padding: var(--nova-btn-padding-y) var(--nova-btn-padding-x);
  transition: color var(--nova-transition-normal) var(--nova-transition-timing-function),
              background-color var(--nova-transition-normal) var(--nova-transition-timing-function),
              border-color var(--nova-transition-normal) var(--nova-transition-timing-function),
              box-shadow var(--nova-transition-normal) var(--nova-transition-timing-function);
}

.nova-button:focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(var(--nova-primary-rgb), 0.25);
}

.nova-button:disabled {
  opacity: 0.65;
  pointer-events: none;
}

/* Sizes */
.nova-button--sm {
  padding: calc(var(--nova-btn-padding-y) * 0.75) calc(var(--nova-btn-padding-x) * 0.75);
  font-size: calc(var(--nova-font-size-button) * 0.875);
  border-radius: calc(var(--nova-btn-border-radius) * 0.875);
}

.nova-button--md {
  /* Default size, no additional styles needed */
}

.nova-button--lg {
  padding: calc(var(--nova-btn-padding-y) * 1.25) calc(var(--nova-btn-padding-x) * 1.25);
  font-size: calc(var(--nova-font-size-button) * 1.125);
  border-radius: calc(var(--nova-btn-border-radius) * 1.125);
}

/* Full width */
.nova-button--full-width {
  display: flex;
  width: 100%;
}

/* Variants */
.nova-button--primary {
  color: var(--nova-white);
  background-color: var(--nova-primary);
  border-color: var(--nova-primary);
}

.nova-button--primary:hover {
  color: var(--nova-white);
  background-color: var(--nova-primary-dark);
  border-color: var(--nova-primary-dark);
}

.nova-button--secondary {
  color: var(--nova-white);
  background-color: var(--nova-secondary);
  border-color: var(--nova-secondary);
}

.nova-button--secondary:hover {
  color: var(--nova-white);
  background-color: var(--nova-secondary-dark);
  border-color: var(--nova-secondary-dark);
}

.nova-button--success {
  color: var(--nova-white);
  background-color: var(--nova-success);
  border-color: var(--nova-success);
}

.nova-button--success:hover {
  color: var(--nova-white);
  background-color: var(--nova-success-dark);
  border-color: var(--nova-success-dark);
}

.nova-button--danger {
  color: var(--nova-white);
  background-color: var(--nova-danger);
  border-color: var(--nova-danger);
}

.nova-button--danger:hover {
  color: var(--nova-white);
  background-color: var(--nova-danger-dark);
  border-color: var(--nova-danger-dark);
}

.nova-button--warning {
  color: var(--nova-gray-900);
  background-color: var(--nova-warning);
  border-color: var(--nova-warning);
}

.nova-button--warning:hover {
  color: var(--nova-gray-900);
  background-color: var(--nova-warning-dark);
  border-color: var(--nova-warning-dark);
}

.nova-button--info {
  color: var(--nova-white);
  background-color: var(--nova-info);
  border-color: var(--nova-info);
}

.nova-button--info:hover {
  color: var(--nova-white);
  background-color: var(--nova-info-dark);
  border-color: var(--nova-info-dark);
}

.nova-button--link {
  color: var(--nova-primary);
  background-color: transparent;
  border-color: transparent;
  text-decoration: none;
}

.nova-button--link:hover {
  color: var(--nova-primary-dark);
  text-decoration: underline;
}

/* Outlined variants */
.nova-button--outlined.nova-button--primary {
  color: var(--nova-primary);
  background-color: transparent;
  border-color: var(--nova-primary);
}

.nova-button--outlined.nova-button--primary:hover {
  color: var(--nova-white);
  background-color: var(--nova-primary);
  border-color: var(--nova-primary);
}

.nova-button--outlined.nova-button--secondary {
  color: var(--nova-secondary);
  background-color: transparent;
  border-color: var(--nova-secondary);
}

.nova-button--outlined.nova-button--secondary:hover {
  color: var(--nova-white);
  background-color: var(--nova-secondary);
  border-color: var(--nova-secondary);
}

.nova-button--outlined.nova-button--success {
  color: var(--nova-success);
  background-color: transparent;
  border-color: var(--nova-success);
}

.nova-button--outlined.nova-button--success:hover {
  color: var(--nova-white);
  background-color: var(--nova-success);
  border-color: var(--nova-success);
}

.nova-button--outlined.nova-button--danger {
  color: var(--nova-danger);
  background-color: transparent;
  border-color: var(--nova-danger);
}

.nova-button--outlined.nova-button--danger:hover {
  color: var(--nova-white);
  background-color: var(--nova-danger);
  border-color: var(--nova-danger);
}

.nova-button--outlined.nova-button--warning {
  color: var(--nova-warning);
  background-color: transparent;
  border-color: var(--nova-warning);
}

.nova-button--outlined.nova-button--warning:hover {
  color: var(--nova-gray-900);
  background-color: var(--nova-warning);
  border-color: var(--nova-warning);
}

.nova-button--outlined.nova-button--info {
  color: var(--nova-info);
  background-color: transparent;
  border-color: var(--nova-info);
}

.nova-button--outlined.nova-button--info:hover {
  color: var(--nova-white);
  background-color: var(--nova-info);
  border-color: var(--nova-info);
}
