/**
 * TwoFactorAuth Component
 * 
 * A component for managing two-factor authentication.
 */

import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { useSecurity } from '../security/SecurityContext';
import { useTheme } from '../theme/ThemeContext';
import { Animated } from './Animated';
import { useI18n } from '../i18n/I18nContext';

/**
 * TwoFactorAuth component
 * 
 * @param {Object} props - Component props
 * @param {string} [props.variant='setup'] - Variant ('setup', 'verify', or 'manage')
 * @param {Function} [props.onVerified] - Callback when verification is successful
 * @param {Function} [props.onCancel] - Callback when canceled
 * @param {Function} [props.onSetupComplete] - Callback when setup is complete
 * @param {string} [props.className] - Additional CSS class names
 * @param {Object} [props.style] - Additional inline styles
 * @returns {React.ReactElement} TwoFactorAuth component
 */
const TwoFactorAuth = ({
  variant = 'setup',
  onVerified,
  onCancel,
  onSetupComplete,
  className = '',
  style = {}
}) => {
  const { theme } = useTheme();
  const { translate } = useI18n();
  const {
    twoFactorAuthStatus,
    enableTwoFactorAuth,
    disableTwoFactorAuth,
    verifyTwoFactorAuth,
    sendTwoFactorAuthCode,
    isLoading,
    error
  } = useSecurity();
  
  // State
  const [step, setStep] = useState('method');
  const [selectedMethod, setSelectedMethod] = useState(null);
  const [verificationCode, setVerificationCode] = useState('');
  const [enrollmentData, setEnrollmentData] = useState(null);
  const [verificationError, setVerificationError] = useState(null);
  
  // Reset state when variant changes
  useEffect(() => {
    setStep('method');
    setSelectedMethod(null);
    setVerificationCode('');
    setEnrollmentData(null);
    setVerificationError(null);
    
    if (variant === 'verify' && twoFactorAuthStatus.verificationMethod) {
      setSelectedMethod(twoFactorAuthStatus.verificationMethod);
      setStep('verify');
    }
  }, [variant, twoFactorAuthStatus.verificationMethod]);
  
  // Handle method selection
  const handleMethodSelect = (method) => {
    setSelectedMethod(method);
    setStep('setup');
    
    // For app method, start setup immediately
    if (method === 'app') {
      handleSetup(method);
    }
  };
  
  // Handle setup
  const handleSetup = async (method) => {
    try {
      setVerificationError(null);
      
      // Enable 2FA
      const data = await enableTwoFactorAuth(method || selectedMethod);
      
      // Store enrollment data
      setEnrollmentData(data);
      
      // Move to verification step
      setStep('verify');
    } catch (err) {
      console.error('Error setting up two-factor authentication:', err);
      setVerificationError(err.message || 'Failed to set up two-factor authentication');
    }
  };
  
  // Handle verification
  const handleVerify = async () => {
    try {
      setVerificationError(null);
      
      // Verify code
      const isValid = await verifyTwoFactorAuth(verificationCode, selectedMethod);
      
      if (isValid) {
        // Call onVerified callback
        if (onVerified) {
          onVerified();
        }
        
        // Call onSetupComplete callback for setup variant
        if (variant === 'setup' && onSetupComplete) {
          onSetupComplete();
        }
        
        // Reset state
        setVerificationCode('');
        setVerificationError(null);
        
        // Move to success step for setup variant
        if (variant === 'setup') {
          setStep('success');
        }
      } else {
        setVerificationError(translate('security.invalidVerificationCode', 'Invalid verification code'));
      }
    } catch (err) {
      console.error('Error verifying two-factor authentication:', err);
      setVerificationError(err.message || 'Failed to verify two-factor authentication');
    }
  };
  
  // Handle resend code
  const handleResendCode = async () => {
    try {
      setVerificationError(null);
      
      // Send code
      await sendTwoFactorAuthCode(selectedMethod);
    } catch (err) {
      console.error('Error sending verification code:', err);
      setVerificationError(err.message || 'Failed to send verification code');
    }
  };
  
  // Handle disable
  const handleDisable = async () => {
    try {
      setVerificationError(null);
      
      // Disable 2FA
      await disableTwoFactorAuth();
      
      // Reset state
      setStep('method');
      setSelectedMethod(null);
      setVerificationCode('');
      setEnrollmentData(null);
      
      // Call onSetupComplete callback
      if (onSetupComplete) {
        onSetupComplete();
      }
    } catch (err) {
      console.error('Error disabling two-factor authentication:', err);
      setVerificationError(err.message || 'Failed to disable two-factor authentication');
    }
  };
  
  // Handle cancel
  const handleCancel = () => {
    // Call onCancel callback
    if (onCancel) {
      onCancel();
    }
  };
  
  // Render method selection step
  const renderMethodSelection = () => {
    return (
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-textPrimary">
          {translate('security.selectVerificationMethod', 'Select Verification Method')}
        </h3>
        
        <p className="text-textSecondary">
          {translate('security.twoFactorAuthDescription', 'Two-factor authentication adds an extra layer of security to your account by requiring a verification code in addition to your password.')}
        </p>
        
        <div className="space-y-2">
          {twoFactorAuthStatus.availableMethods.map(method => (
            <button
              key={method}
              type="button"
              className="flex items-center justify-between w-full p-3 rounded-md border border-divider bg-background hover:bg-surface transition-colors duration-200"
              onClick={() => handleMethodSelect(method)}
              disabled={isLoading}
            >
              <div className="flex items-center">
                {method === 'app' && (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-3 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
                  </svg>
                )}
                
                {method === 'sms' && (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-3 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
                  </svg>
                )}
                
                {method === 'email' && (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-3 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                )}
                
                <div>
                  <div className="font-medium text-textPrimary">
                    {method === 'app' && translate('security.authenticatorApp', 'Authenticator App')}
                    {method === 'sms' && translate('security.sms', 'SMS')}
                    {method === 'email' && translate('security.email', 'Email')}
                  </div>
                  
                  <div className="text-sm text-textSecondary">
                    {method === 'app' && translate('security.authenticatorAppDescription', 'Use an authenticator app like Google Authenticator or Authy')}
                    {method === 'sms' && translate('security.smsDescription', 'Receive a verification code via SMS')}
                    {method === 'email' && translate('security.emailDescription', 'Receive a verification code via email')}
                  </div>
                </div>
              </div>
              
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-textSecondary" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
              </svg>
            </button>
          ))}
        </div>
        
        {onCancel && (
          <div className="flex justify-end mt-4">
            <button
              type="button"
              className="px-4 py-2 text-textSecondary hover:text-textPrimary transition-colors duration-200"
              onClick={handleCancel}
              disabled={isLoading}
            >
              {translate('common.cancel', 'Cancel')}
            </button>
          </div>
        )}
      </div>
    );
  };
  
  // Render setup step
  const renderSetup = () => {
    if (selectedMethod === 'app') {
      return (
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-textPrimary">
            {translate('security.setupAuthenticatorApp', 'Setup Authenticator App')}
          </h3>
          
          <p className="text-textSecondary">
            {translate('security.authenticatorAppSetupDescription', 'Scan the QR code with your authenticator app or enter the secret key manually.')}
          </p>
          
          {isLoading && (
            <div className="flex justify-center py-4">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
            </div>
          )}
          
          {!isLoading && enrollmentData && (
            <div className="space-y-4">
              <div className="flex justify-center">
                <img
                  src={enrollmentData.qrCodeUrl}
                  alt="QR Code"
                  className="w-48 h-48 border border-divider rounded-md"
                />
              </div>
              
              <div className="bg-background p-3 rounded-md border border-divider">
                <div className="text-sm text-textSecondary mb-1">
                  {translate('security.secretKey', 'Secret Key')}
                </div>
                <div className="font-mono text-textPrimary break-all">
                  {enrollmentData.secretKey}
                </div>
              </div>
              
              <div className="flex justify-between">
                <button
                  type="button"
                  className="px-4 py-2 text-textSecondary hover:text-textPrimary transition-colors duration-200"
                  onClick={() => setStep('method')}
                  disabled={isLoading}
                >
                  {translate('common.back', 'Back')}
                </button>
                
                <button
                  type="button"
                  className="px-4 py-2 bg-primary text-primaryContrast rounded-md hover:bg-primaryDark transition-colors duration-200"
                  onClick={() => setStep('verify')}
                  disabled={isLoading}
                >
                  {translate('common.continue', 'Continue')}
                </button>
              </div>
            </div>
          )}
        </div>
      );
    }
    
    return (
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-textPrimary">
          {selectedMethod === 'sms'
            ? translate('security.setupSms', 'Setup SMS Verification')
            : translate('security.setupEmail', 'Setup Email Verification')}
        </h3>
        
        <p className="text-textSecondary">
          {selectedMethod === 'sms'
            ? translate('security.smsSetupDescription', 'We will send a verification code to your phone number.')
            : translate('security.emailSetupDescription', 'We will send a verification code to your email address.')}
        </p>
        
        <div className="flex justify-between">
          <button
            type="button"
            className="px-4 py-2 text-textSecondary hover:text-textPrimary transition-colors duration-200"
            onClick={() => setStep('method')}
            disabled={isLoading}
          >
            {translate('common.back', 'Back')}
          </button>
          
          <button
            type="button"
            className="px-4 py-2 bg-primary text-primaryContrast rounded-md hover:bg-primaryDark transition-colors duration-200"
            onClick={() => handleSetup()}
            disabled={isLoading}
          >
            {isLoading
              ? translate('common.sending', 'Sending...')
              : translate('security.sendVerificationCode', 'Send Verification Code')}
          </button>
        </div>
      </div>
    );
  };
  
  // Render verification step
  const renderVerification = () => {
    return (
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-textPrimary">
          {translate('security.enterVerificationCode', 'Enter Verification Code')}
        </h3>
        
        <p className="text-textSecondary">
          {selectedMethod === 'app'
            ? translate('security.enterCodeFromApp', 'Enter the 6-digit code from your authenticator app.')
            : selectedMethod === 'sms'
              ? translate('security.enterCodeFromSms', 'Enter the 6-digit code sent to your phone.')
              : translate('security.enterCodeFromEmail', 'Enter the 6-digit code sent to your email.')}
        </p>
        
        <div className="space-y-2">
          <label htmlFor="verification-code" className="block text-sm font-medium text-textSecondary">
            {translate('security.verificationCode', 'Verification Code')}
          </label>
          
          <input
            type="text"
            id="verification-code"
            className="w-full px-3 py-2 border border-divider rounded-md bg-background text-textPrimary focus:outline-none focus:ring-2 focus:ring-primary"
            value={verificationCode}
            onChange={(e) => setVerificationCode(e.target.value)}
            placeholder="123456"
            maxLength={6}
            autoComplete="one-time-code"
            inputMode="numeric"
            pattern="[0-9]*"
          />
          
          {verificationError && (
            <div className="text-sm text-error">
              {verificationError}
            </div>
          )}
        </div>
        
        {(selectedMethod === 'sms' || selectedMethod === 'email') && (
          <button
            type="button"
            className="text-sm text-primary hover:text-primaryDark transition-colors duration-200"
            onClick={handleResendCode}
            disabled={isLoading}
          >
            {translate('security.resendCode', 'Resend Code')}
          </button>
        )}
        
        <div className="flex justify-between">
          {variant === 'setup' && (
            <button
              type="button"
              className="px-4 py-2 text-textSecondary hover:text-textPrimary transition-colors duration-200"
              onClick={() => setStep('setup')}
              disabled={isLoading}
            >
              {translate('common.back', 'Back')}
            </button>
          )}
          
          {variant === 'verify' && onCancel && (
            <button
              type="button"
              className="px-4 py-2 text-textSecondary hover:text-textPrimary transition-colors duration-200"
              onClick={handleCancel}
              disabled={isLoading}
            >
              {translate('common.cancel', 'Cancel')}
            </button>
          )}
          
          <button
            type="button"
            className="px-4 py-2 bg-primary text-primaryContrast rounded-md hover:bg-primaryDark transition-colors duration-200"
            onClick={handleVerify}
            disabled={isLoading || verificationCode.length !== 6}
          >
            {isLoading
              ? translate('common.verifying', 'Verifying...')
              : translate('common.verify', 'Verify')}
          </button>
        </div>
      </div>
    );
  };
  
  // Render success step
  const renderSuccess = () => {
    return (
      <div className="space-y-4">
        <div className="flex justify-center">
          <div className="w-16 h-16 rounded-full bg-success bg-opacity-10 flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-success" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
          </div>
        </div>
        
        <h3 className="text-lg font-medium text-textPrimary text-center">
          {translate('security.twoFactorAuthEnabled', 'Two-Factor Authentication Enabled')}
        </h3>
        
        <p className="text-textSecondary text-center">
          {translate('security.twoFactorAuthEnabledDescription', 'Your account is now protected with two-factor authentication.')}
        </p>
        
        <div className="flex justify-center">
          <button
            type="button"
            className="px-4 py-2 bg-primary text-primaryContrast rounded-md hover:bg-primaryDark transition-colors duration-200"
            onClick={handleCancel}
          >
            {translate('common.done', 'Done')}
          </button>
        </div>
      </div>
    );
  };
  
  // Render manage step
  const renderManage = () => {
    return (
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-textPrimary">
          {translate('security.twoFactorAuthentication', 'Two-Factor Authentication')}
        </h3>
        
        <div className="bg-success bg-opacity-10 text-success p-3 rounded-md">
          <div className="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
            <span>{translate('security.twoFactorAuthActive', 'Two-factor authentication is active')}</span>
          </div>
        </div>
        
        <div className="bg-background p-3 rounded-md border border-divider">
          <div className="font-medium text-textPrimary mb-1">
            {translate('security.currentMethod', 'Current Method')}
          </div>
          <div className="text-textSecondary">
            {twoFactorAuthStatus.verificationMethod === 'app' && translate('security.authenticatorApp', 'Authenticator App')}
            {twoFactorAuthStatus.verificationMethod === 'sms' && translate('security.sms', 'SMS')}
            {twoFactorAuthStatus.verificationMethod === 'email' && translate('security.email', 'Email')}
          </div>
        </div>
        
        <div className="flex justify-between">
          <button
            type="button"
            className="px-4 py-2 text-error hover:text-errorDark transition-colors duration-200"
            onClick={handleDisable}
            disabled={isLoading}
          >
            {isLoading
              ? translate('common.disabling', 'Disabling...')
              : translate('security.disableTwoFactorAuth', 'Disable Two-Factor Authentication')}
          </button>
          
          <button
            type="button"
            className="px-4 py-2 bg-primary text-primaryContrast rounded-md hover:bg-primaryDark transition-colors duration-200"
            onClick={() => setStep('method')}
            disabled={isLoading}
          >
            {translate('security.changeMethod', 'Change Method')}
          </button>
        </div>
        
        {verificationError && (
          <div className="text-sm text-error">
            {verificationError}
          </div>
        )}
      </div>
    );
  };
  
  // Render content based on variant and step
  const renderContent = () => {
    if (variant === 'manage') {
      if (twoFactorAuthStatus.isEnrolled) {
        if (step === 'method') {
          return renderManage();
        }
        return renderMethodSelection();
      }
      return renderMethodSelection();
    }
    
    if (variant === 'verify') {
      return renderVerification();
    }
    
    if (variant === 'setup') {
      if (step === 'method') {
        return renderMethodSelection();
      }
      
      if (step === 'setup') {
        return renderSetup();
      }
      
      if (step === 'verify') {
        return renderVerification();
      }
      
      if (step === 'success') {
        return renderSuccess();
      }
    }
    
    return null;
  };
  
  return (
    <div
      className={`${className}`}
      style={style}
      data-testid="two-factor-auth"
    >
      <Animated animation="fadeIn">
        {renderContent()}
      </Animated>
    </div>
  );
};

TwoFactorAuth.propTypes = {
  variant: PropTypes.oneOf(['setup', 'verify', 'manage']),
  onVerified: PropTypes.func,
  onCancel: PropTypes.func,
  onSetupComplete: PropTypes.func,
  className: PropTypes.string,
  style: PropTypes.object
};

export default TwoFactorAuth;
