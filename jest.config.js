module.exports = {
  // The root directory that Je<PERSON> should scan for tests and modules
  rootDir: '.',

  // The test environment that will be used for testing
  testEnvironment: 'jsdom',

  // The glob patterns <PERSON><PERSON> uses to detect test files
  testMatch: [
    '**/__tests__/**/*.js',
    '**/?(*.)+(spec|test).js'
  ],

  // An array of regexp pattern strings that are matched against all test paths
  testPathIgnorePatterns: [
    '/node_modules/',
    '/dist/',
    '/build/',
    '/coverage/'
  ],

  // An array of regexp pattern strings that are matched against all source file paths
  transformIgnorePatterns: [
    '/node_modules/',
    '\\.pnp\\.[^\\/]+$'
  ],

  // Automatically clear mock calls and instances between every test
  clearMocks: true,

  // Indicates whether the coverage information should be collected while executing the test
  collectCoverage: false,

  // The directory where Jest should output its coverage files
  coverageDirectory: 'coverage',

  // An array of regexp pattern strings used to skip coverage collection
  coveragePathIgnorePatterns: [
    '/node_modules/',
    '/tests/setup.js',
    '/tests/setup-react.js',
    '/dist/',
    '/build/',
    '/coverage/'
  ],

  // Include test mocks in coverage
  collectCoverageFrom: [
    'tests/mocks/**/*.js'
  ],

  // Indicates which provider should be used to instrument code for coverage
  coverageProvider: 'v8',

  // A list of reporter names that Jest uses when writing coverage reports
  coverageReporters: [
    'json',
    'text',
    'lcov',
    'clover',
    'html'
  ],

  // The minimum threshold enforcement for coverage results
  coverageThreshold: {
    global: {
      branches: 81,
      functions: 81,
      lines: 81,
      statements: 81
    },
    // For test mocks during development, we can temporarily use a lower threshold
    "./tests/mocks/**/*.js": {
      branches: 50,
      functions: 50,
      lines: 50,
      statements: 50
    }
  },

  // A path to a module which exports an async function that is triggered once before all test suites
  globalSetup: null,

  // A path to a module which exports an async function that is triggered once after all test suites
  globalTeardown: null,

  // A set of global variables that need to be available in all test environments
  globals: {},

  // A path to a custom setup file
  setupFilesAfterEnv: ['./jest.setup.js', './tests/setup-react.js'],

  // The maximum amount of workers used to run your tests
  maxWorkers: '50%',

  // An array of directory names to be searched recursively up from the requiring module's location
  moduleDirectories: [
    'node_modules'
  ],

  // An array of file extensions your modules use
  moduleFileExtensions: [
    'js',
    'json',
    'node'
  ],

  // A map from regular expressions to module names or to arrays of module names
  moduleNameMapper: {},

  // An array of regexp pattern strings, matched against all module paths before considered 'visible' to the module loader
  modulePathIgnorePatterns: [],

  // Activates notifications for test results
  notify: false,

  // An enum that specifies notification mode
  notifyMode: 'failure-change',

  // Run tests with specified reporters
  reporters: [
    'default',
    [
      'jest-junit',
      {
        outputDirectory: 'test-results/junit',
        outputName: 'results.xml'
      }
    ],
    [
      '<rootDir>/tools/test-reporter/jest-html-reporter.js',
      {
        outputDir: 'test-results/html',
        jsonReport: true
      }
    ]
  ],

  // Automatically reset mock state between every test
  resetMocks: false,

  // Reset the module registry before running each individual test
  resetModules: false,

  // A path to a custom resolver
  resolver: null,

  // Automatically restore mock state between every test
  restoreMocks: false,

  // A list of paths to directories that Jest should use to search for files in
  roots: [
    '<rootDir>'
  ],

  // The paths to modules that run some code to configure or set up the testing environment before each test
  setupFiles: [],

  // A list of paths to snapshot serializer modules Jest should use for snapshot testing
  snapshotSerializers: [],

  // The test runner to use
  testRunner: 'jest-circus/runner',

  // Setting this value to "fake" allows the use of fake timers for functions such as "setTimeout"
  timers: 'real',

  // A map from regular expressions to paths to transformers
  transform: {
    '^.+\\.(js|jsx)$': 'babel-jest'
  },

  // Indicates whether each individual test should be reported during the run
  verbose: true,

  // Whether to use watchman for file crawling
  watchman: true,
};
