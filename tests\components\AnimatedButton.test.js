import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import AnimatedButton from '../../components/AnimatedButton';

describe('AnimatedButton', () => {
  it('renders button text correctly', () => {
    render(<AnimatedButton>Click Me</AnimatedButton>);

    // Check if button text is displayed
    expect(screen.getByText('Click Me')).toBeInTheDocument();
  });

  it('calls onClick handler when clicked', () => {
    const handleClick = jest.fn();
    render(<AnimatedButton onClick={handleClick}>Click Me</AnimatedButton>);

    // Click the button
    fireEvent.click(screen.getByText('Click Me'));

    // Check if onClick handler was called
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('renders as a link when href is provided', () => {
    render(<AnimatedButton href="/test-link">Go to Link</AnimatedButton>);

    // Check if it's rendered as a link
    const linkElement = screen.getByText('Go to Link').closest('a');
    expect(linkElement).toBeInTheDocument();
    expect(linkElement).toHaveAttribute('href', '/test-link');
  });

  it('applies custom className', () => {
    const { container } = render(
      <AnimatedButton className="custom-class">Click Me</AnimatedButton>
    );

    // Check if custom class is applied
    expect(container.firstChild).toHaveClass('custom-class');
  });

  it('applies different styles based on variant', () => {
    const { rerender } = render(
      <AnimatedButton variant="primary">Primary</AnimatedButton>
    );

    // Primary variant should have certain classes
    let button = screen.getByText('Primary');
    expect(button).toBeInTheDocument();

    // Rerender with secondary variant
    rerender(<AnimatedButton variant="secondary">Secondary</AnimatedButton>);

    // Secondary variant should be rendered
    button = screen.getByText('Secondary');
    expect(button).toBeInTheDocument();

    // Rerender with outline variant
    rerender(<AnimatedButton variant="outline">Outline</AnimatedButton>);

    // Outline variant should be rendered
    button = screen.getByText('Outline');
    expect(button).toBeInTheDocument();

    // Rerender with ghost variant
    rerender(<AnimatedButton variant="ghost">Ghost</AnimatedButton>);

    // Ghost variant should be rendered
    button = screen.getByText('Ghost');
    expect(button).toBeInTheDocument();
  });

  it('disables the button when disabled prop is true', () => {
    render(<AnimatedButton disabled>Disabled Button</AnimatedButton>);

    // Check if button is disabled
    const button = screen.getByText('Disabled Button');
    expect(button.closest('button')).toHaveAttribute('disabled');
  });
});
