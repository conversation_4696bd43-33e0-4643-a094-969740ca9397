{"extends": "base-connector", "name": "novafuse-risk-connector", "version": "1.0.0", "description": "Risk & Audit connector template for NovaFuse API Superstore", "category": "risk", "base_url": "http://localhost:8000/risk", "endpoints": [{"name": "risk_assessments", "path": "/assessments", "method": "GET", "description": "Get a list of risk assessments", "parameters": [{"name": "status", "in": "query", "required": false, "description": "Filter by status (in_progress, completed, scheduled)"}, {"name": "category", "in": "query", "required": false, "description": "Filter by risk category"}, {"name": "date_range", "in": "query", "required": false, "description": "Filter by date range (YYYY-MM-DD,YYYY-MM-DD)"}]}, {"name": "risk_assessment_details", "path": "/assessments/{id}", "method": "GET", "description": "Get details of a specific risk assessment", "parameters": [{"name": "id", "in": "path", "required": true, "description": "Assessment ID"}]}, {"name": "audit_reports", "path": "/audits/reports", "method": "GET", "description": "Get a list of audit reports", "parameters": [{"name": "audit_type", "in": "query", "required": false, "description": "Filter by audit type (internal, external, compliance)"}, {"name": "status", "in": "query", "required": false, "description": "Filter by status (draft, final, archived)"}]}, {"name": "audit_report_details", "path": "/audits/reports/{id}", "method": "GET", "description": "Get details of a specific audit report", "parameters": [{"name": "id", "in": "path", "required": true, "description": "Report ID"}]}, {"name": "risk_register", "path": "/register", "method": "GET", "description": "Get the risk register", "parameters": [{"name": "severity", "in": "query", "required": false, "description": "Filter by risk severity (low, medium, high, critical)"}, {"name": "status", "in": "query", "required": false, "description": "Filter by status (active, mitigated, accepted, transferred)"}]}, {"name": "risk_analysis", "path": "/analysis", "method": "POST", "description": "Perform a risk analysis", "parameters": []}, {"name": "audit_findings", "path": "/audits/findings", "method": "GET", "description": "Get a list of audit findings", "parameters": [{"name": "audit_id", "in": "query", "required": false, "description": "Filter by audit ID"}, {"name": "severity", "in": "query", "required": false, "description": "Filter by finding severity (low, medium, high, critical)"}, {"name": "status", "in": "query", "required": false, "description": "Filter by status (open, closed, in_progress)"}]}]}