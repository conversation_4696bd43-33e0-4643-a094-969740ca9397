/**
 * Compliance API Routes
 * 
 * This file defines the API routes for compliance-related functionality.
 */

const express = require('express');
const router = express.Router();
const { complianceFrameworks } = require('../../models');

// Feature flag middleware
const checkFeatureAccess = (req, res, next) => {
  // In a real implementation, this would check the user's product tier
  // For simulation, we'll check if the feature flag is enabled in the request
  const productTier = req.headers['x-product-tier'] || 'novaPrime';
  
  // For simulation, we'll allow all features for novaPrime
  if (productTier === 'novaPrime') {
    return next();
  }
  
  // For other tiers, we'll check the feature flag configuration
  // This is a simplified implementation for simulation purposes
  const featureFlags = {
    novaCore: ['privacy-management', 'security-assessment', 'regulatory-compliance'],
    novaShield: ['security-assessment', 'control-testing'],
    novaLearn: ['regulatory-compliance'],
    novaAssistAI: ['privacy-management', 'security-assessment', 'regulatory-compliance', 'control-testing', 'esg']
  };
  
  if (featureFlags[productTier] && featureFlags[productTier].includes('regulatory-compliance')) {
    return next();
  }
  
  return res.status(403).json({
    error: 'Feature not available',
    message: `This feature is not available in your current plan (${productTier})`
  });
};

/**
 * @route GET /compliance/frameworks
 * @description Get all compliance frameworks
 * @access Private
 */
router.get('/frameworks', checkFeatureAccess, (req, res) => {
  // Get query parameters for filtering
  const { category, region, minScore } = req.query;
  
  // Filter frameworks based on query parameters
  let filteredFrameworks = [...complianceFrameworks];
  
  if (category) {
    filteredFrameworks = filteredFrameworks.filter(framework => 
      framework.category.toLowerCase() === category.toLowerCase()
    );
  }
  
  if (region) {
    filteredFrameworks = filteredFrameworks.filter(framework => 
      framework.region.toLowerCase() === region.toLowerCase()
    );
  }
  
  if (minScore) {
    const score = parseInt(minScore);
    filteredFrameworks = filteredFrameworks.filter(framework => 
      framework.overallScore >= score
    );
  }
  
  // Return filtered frameworks
  res.json({
    data: filteredFrameworks.map(framework => ({
      id: framework.id,
      name: framework.name,
      version: framework.version,
      region: framework.region,
      category: framework.category,
      overallScore: framework.overallScore,
      lastAssessment: framework.lastAssessment,
      nextAssessment: framework.nextAssessment,
      controlCount: framework.controls.length
    }))
  });
});

/**
 * @route GET /compliance/frameworks/:id
 * @description Get a specific compliance framework by ID
 * @access Private
 */
router.get('/frameworks/:id', checkFeatureAccess, (req, res) => {
  const { id } = req.params;
  
  // Find the framework by ID
  const framework = complianceFrameworks.find(f => f.id === id);
  
  if (!framework) {
    return res.status(404).json({
      error: 'Framework not found',
      message: `No framework found with ID ${id}`
    });
  }
  
  // Return the framework
  res.json({
    data: framework
  });
});

/**
 * @route GET /compliance/frameworks/:id/controls
 * @description Get controls for a specific compliance framework
 * @access Private
 */
router.get('/frameworks/:id/controls', checkFeatureAccess, (req, res) => {
  const { id } = req.params;
  const { status, category, minScore } = req.query;
  
  // Find the framework by ID
  const framework = complianceFrameworks.find(f => f.id === id);
  
  if (!framework) {
    return res.status(404).json({
      error: 'Framework not found',
      message: `No framework found with ID ${id}`
    });
  }
  
  // Filter controls based on query parameters
  let filteredControls = [...framework.controls];
  
  if (status) {
    filteredControls = filteredControls.filter(control => 
      control.status === status
    );
  }
  
  if (category) {
    filteredControls = filteredControls.filter(control => 
      control.category.toLowerCase() === category.toLowerCase()
    );
  }
  
  if (minScore) {
    const score = parseInt(minScore);
    filteredControls = filteredControls.filter(control => 
      control.score >= score
    );
  }
  
  // Return filtered controls
  res.json({
    data: {
      frameworkId: framework.id,
      frameworkName: framework.name,
      controls: filteredControls
    }
  });
});

/**
 * @route GET /compliance/frameworks/:frameworkId/controls/:controlId
 * @description Get a specific control for a compliance framework
 * @access Private
 */
router.get('/frameworks/:frameworkId/controls/:controlId', checkFeatureAccess, (req, res) => {
  const { frameworkId, controlId } = req.params;
  
  // Find the framework by ID
  const framework = complianceFrameworks.find(f => f.id === frameworkId);
  
  if (!framework) {
    return res.status(404).json({
      error: 'Framework not found',
      message: `No framework found with ID ${frameworkId}`
    });
  }
  
  // Find the control by ID
  const control = framework.controls.find(c => c.id === controlId);
  
  if (!control) {
    return res.status(404).json({
      error: 'Control not found',
      message: `No control found with ID ${controlId} in framework ${frameworkId}`
    });
  }
  
  // Return the control
  res.json({
    data: {
      frameworkId: framework.id,
      frameworkName: framework.name,
      control
    }
  });
});

/**
 * @route GET /compliance/summary
 * @description Get a summary of compliance status across all frameworks
 * @access Private
 */
router.get('/summary', checkFeatureAccess, (req, res) => {
  // Calculate summary statistics
  const summary = {
    overallScore: 0,
    frameworkCount: complianceFrameworks.length,
    controlCount: 0,
    passedControls: 0,
    failedControls: 0,
    partialControls: 0,
    frameworkScores: [],
    categoryScores: {},
    upcomingAssessments: []
  };
  
  // Process each framework
  complianceFrameworks.forEach(framework => {
    // Add to framework scores
    summary.frameworkScores.push({
      id: framework.id,
      name: framework.name,
      score: framework.overallScore
    });
    
    // Add to upcoming assessments if within 30 days
    const nextAssessment = new Date(framework.nextAssessment);
    const now = new Date();
    const daysDifference = Math.ceil((nextAssessment - now) / (1000 * 60 * 60 * 24));
    
    if (daysDifference <= 30 && daysDifference > 0) {
      summary.upcomingAssessments.push({
        id: framework.id,
        name: framework.name,
        date: framework.nextAssessment,
        daysRemaining: daysDifference
      });
    }
    
    // Process controls
    framework.controls.forEach(control => {
      summary.controlCount++;
      
      if (control.status === 'PASS') {
        summary.passedControls++;
      } else if (control.status === 'FAIL') {
        summary.failedControls++;
      } else if (control.status === 'PARTIAL') {
        summary.partialControls++;
      }
      
      // Add to category scores
      if (!summary.categoryScores[control.category]) {
        summary.categoryScores[control.category] = {
          total: 0,
          passed: 0,
          score: 0
        };
      }
      
      summary.categoryScores[control.category].total++;
      
      if (control.status === 'PASS') {
        summary.categoryScores[control.category].passed++;
      } else if (control.status === 'PARTIAL') {
        summary.categoryScores[control.category].passed += 0.5;
      }
    });
  });
  
  // Calculate overall score
  if (summary.controlCount > 0) {
    summary.overallScore = Math.round(
      ((summary.passedControls + (summary.partialControls * 0.5)) / summary.controlCount) * 100
    );
  }
  
  // Calculate category scores
  Object.keys(summary.categoryScores).forEach(category => {
    const categoryData = summary.categoryScores[category];
    if (categoryData.total > 0) {
      categoryData.score = Math.round(
        (categoryData.passed / categoryData.total) * 100
      );
    }
  });
  
  // Sort upcoming assessments by date
  summary.upcomingAssessments.sort((a, b) => 
    new Date(a.date) - new Date(b.date)
  );
  
  // Return the summary
  res.json({
    data: summary
  });
});

module.exports = router;
