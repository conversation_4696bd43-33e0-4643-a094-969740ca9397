
/**
 * CSDE Fixed ML Model API
 *
 * This module provides a simple API for the CSDE Fixed ML Model.
 */

const { CSDEEngine } = require('../index');
const fs = require('fs');
const path = require('path');

class CSDEFixedML {
  /**
   * Create a new CSDE Fixed ML instance
   */
  constructor() {
    // Initialize CSDE Engine
    this.csdeEngine = new CSDEEngine();

    // Load fixed model
    this.model = JSON.parse(fs.readFileSync(
      path.join(__dirname, 'models', 'fixed_model.json'),
      'utf8'
    ));

    console.log('CSDE Fixed ML Model initialized');
  }

  /**
   * Predict CSDE value using fixed model
   * @param {Object} input - Input data
   * @returns {Object} - Prediction result
   */
  predict(input) {
    // Calculate CSDE result to get features
    const csdeResult = this.csdeEngine.calculate(
      input.complianceData,
      input.gcpData,
      input.cyberSafetyData
    );

    // Extract features
    const features = {
      nistComponent: csdeResult.nistComponent.processedValue,
      gcpComponent: csdeResult.gcpComponent.processedValue,
      cyberSafetyComponent: csdeResult.cyberSafetyComponent.processedValue,
      tensorProduct: csdeResult.tensorProduct,
      fusionValue: csdeResult.fusionValue,
      circularTrustFactor: csdeResult.circularTrustFactor
    };

    // Make prediction using fixed model
    let prediction = 0;
    for (const feature in features) {
      prediction += this.model.weights[feature] * features[feature];
    }

    // Calculate confidence (inverse of error percentage)
    const errorPercentage = Math.abs(prediction - csdeResult.csdeValue) / csdeResult.csdeValue;
    const confidence = Math.max(0, 1 - errorPercentage);

    return {
      prediction,
      confidence,
      csdeValue: csdeResult.csdeValue,
      explanation: this._generateExplanation(features, prediction, csdeResult.csdeValue, confidence)
    };
  }

  /**
   * Generate explanation for prediction
   * @param {Object} features - Input features
   * @param {Number} prediction - Predicted value
   * @param {Number} actualValue - Actual CSDE value
   * @param {Number} confidence - Prediction confidence
   * @returns {String} - Explanation
   * @private
   */
  _generateExplanation(features, prediction, actualValue, confidence) {
    // Calculate feature importance
    const featureImportance = {};
    let totalImportance = 0;

    for (const feature in features) {
      const importance = Math.abs(this.model.weights[feature] * features[feature]);
      featureImportance[feature] = importance;
      totalImportance += importance;
    }

    // Normalize feature importance
    for (const feature in featureImportance) {
      featureImportance[feature] /= totalImportance;
    }

    // Sort features by importance
    const sortedFeatures = Object.entries(featureImportance)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 3);

    // Generate explanation
    let explanation = `Prediction: ${prediction.toFixed(2)} (${(confidence * 100).toFixed(2)}% confidence)
`;
    explanation += `CSDE Value: ${actualValue.toFixed(2)}
`;
    explanation += `Error: ${Math.abs(prediction - actualValue).toFixed(2)} (${((1 - confidence) * 100).toFixed(2)}%)

`;
    explanation += 'Top contributing factors:\n';

    sortedFeatures.forEach(([feature, importance]) => {
      explanation += `- ${feature}: ${(importance * 100).toFixed(2)}% contribution
`;
    });

    return explanation;
  }
}

module.exports = CSDEFixedML;
