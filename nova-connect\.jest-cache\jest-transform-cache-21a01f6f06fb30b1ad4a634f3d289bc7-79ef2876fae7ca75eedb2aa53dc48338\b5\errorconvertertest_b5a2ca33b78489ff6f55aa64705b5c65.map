{"version": 3, "names": ["convertAxiosError", "convertJsonSchemaError", "convertError", "require", "UAConnectorError", "TimeoutError", "NetworkError", "InvalidCredentialsError", "AuthenticationError", "ResourceNotFoundError", "RateLimitExceededError", "BadRequestError", "ServerError", "ValidationError", "describe", "it", "error", "result", "expect", "toBe", "code", "message", "config", "url", "method", "toBeInstanceOf", "cause", "context", "request", "response", "status", "data", "statusCode", "toEqual", "resourceId", "headers", "retryAfter", "errors", "keyword", "dataPath", "schemaPath", "validationErrors", "length", "field", "isAxiosError", "schemaName", "Error"], "sources": ["error-converter.test.js"], "sourcesContent": ["/**\n * NovaFuse Universal API Connector - Error Converter Tests\n * \n * This module tests the error converter utilities for the UAC.\n */\n\nconst { \n  convertAxiosError, \n  convertJsonSchemaError, \n  convertError \n} = require('../../src/utils/error-converter');\n\nconst {\n  UAConnectorError,\n  TimeoutError,\n  NetworkError,\n  InvalidCredentialsError,\n  AuthenticationError,\n  ResourceNotFoundError,\n  RateLimitExceededError,\n  BadRequestError,\n  ServerError,\n  ValidationError\n} = require('../../src/errors');\n\ndescribe('Error Converter', () => {\n  describe('convertAxiosError', () => {\n    it('should return the error if it is already a UAConnectorError', () => {\n      const error = new UAConnectorError('Test error');\n      \n      const result = convertAxiosError(error);\n      \n      expect(result).toBe(error);\n    });\n    \n    it('should convert timeout error', () => {\n      const error = {\n        code: 'ECONNABORTED',\n        message: 'timeout of 1000ms exceeded',\n        config: {\n          url: 'https://api.example.com',\n          method: 'get'\n        }\n      };\n      \n      const result = convertAxiosError(error);\n      \n      expect(result).toBeInstanceOf(TimeoutError);\n      expect(result.message).toBe('Request timed out');\n      expect(result.cause).toBe(error);\n      expect(result.context.request.url).toBe('https://api.example.com');\n    });\n    \n    it('should convert network error', () => {\n      const error = {\n        code: 'ENOTFOUND',\n        message: 'getaddrinfo ENOTFOUND api.example.com',\n        config: {\n          url: 'https://api.example.com',\n          method: 'get'\n        }\n      };\n      \n      const result = convertAxiosError(error);\n      \n      expect(result).toBeInstanceOf(NetworkError);\n      expect(result.message).toBe('Network error: ENOTFOUND');\n      expect(result.cause).toBe(error);\n    });\n    \n    it('should convert 401 Unauthorized error', () => {\n      const error = {\n        response: {\n          status: 401,\n          data: { message: 'Unauthorized' }\n        },\n        config: {\n          url: 'https://api.example.com',\n          method: 'get'\n        }\n      };\n      \n      const result = convertAxiosError(error);\n      \n      expect(result).toBeInstanceOf(InvalidCredentialsError);\n      expect(result.statusCode).toBe(401);\n      expect(result.response).toEqual({ message: 'Unauthorized' });\n    });\n    \n    it('should convert 403 Forbidden error', () => {\n      const error = {\n        response: {\n          status: 403,\n          data: { message: 'Forbidden' }\n        },\n        config: {\n          url: 'https://api.example.com',\n          method: 'get'\n        }\n      };\n      \n      const result = convertAxiosError(error);\n      \n      expect(result).toBeInstanceOf(AuthenticationError);\n      expect(result.code).toBe('AUTH_FORBIDDEN');\n      expect(result.statusCode).toBe(403);\n    });\n    \n    it('should convert 404 Not Found error', () => {\n      const error = {\n        response: {\n          status: 404,\n          data: { message: 'Not Found' }\n        },\n        config: {\n          url: 'https://api.example.com/users/123',\n          method: 'get'\n        }\n      };\n      \n      const result = convertAxiosError(error);\n      \n      expect(result).toBeInstanceOf(ResourceNotFoundError);\n      expect(result.statusCode).toBe(404);\n      expect(result.resourceId).toBe('https://api.example.com/users/123');\n    });\n    \n    it('should convert 429 Too Many Requests error', () => {\n      const error = {\n        response: {\n          status: 429,\n          data: { message: 'Too Many Requests' },\n          headers: {\n            'retry-after': '60'\n          }\n        },\n        config: {\n          url: 'https://api.example.com',\n          method: 'get'\n        }\n      };\n      \n      const result = convertAxiosError(error);\n      \n      expect(result).toBeInstanceOf(RateLimitExceededError);\n      expect(result.statusCode).toBe(429);\n      expect(result.retryAfter).toBe(60);\n    });\n    \n    it('should convert 400 Bad Request error', () => {\n      const error = {\n        response: {\n          status: 400,\n          data: { message: 'Bad Request' }\n        },\n        config: {\n          url: 'https://api.example.com',\n          method: 'get'\n        }\n      };\n      \n      const result = convertAxiosError(error);\n      \n      expect(result).toBeInstanceOf(BadRequestError);\n      expect(result.statusCode).toBe(400);\n    });\n    \n    it('should convert 500 Server Error', () => {\n      const error = {\n        response: {\n          status: 500,\n          data: { message: 'Internal Server Error' }\n        },\n        config: {\n          url: 'https://api.example.com',\n          method: 'get'\n        }\n      };\n      \n      const result = convertAxiosError(error);\n      \n      expect(result).toBeInstanceOf(ServerError);\n      expect(result.statusCode).toBe(500);\n    });\n  });\n  \n  describe('convertJsonSchemaError', () => {\n    it('should return the error if it is already a UAConnectorError', () => {\n      const error = new UAConnectorError('Test error');\n      \n      const result = convertJsonSchemaError(error, 'TestSchema');\n      \n      expect(result).toBe(error);\n    });\n    \n    it('should convert JSON Schema validation error', () => {\n      const error = {\n        errors: [\n          {\n            keyword: 'required',\n            dataPath: '.user',\n            schemaPath: '#/properties/user/required',\n            message: 'should have required property \\'name\\''\n          },\n          {\n            keyword: 'format',\n            dataPath: '.user.email',\n            schemaPath: '#/properties/user/properties/email/format',\n            message: 'should match format \"email\"'\n          }\n        ]\n      };\n      \n      const result = convertJsonSchemaError(error, 'UserSchema');\n      \n      expect(result).toBeInstanceOf(ValidationError);\n      expect(result.code).toBe('VALIDATION_SCHEMA_ERROR');\n      expect(result.validationErrors.length).toBe(2);\n      expect(result.validationErrors[0].field).toBe('.user');\n      expect(result.validationErrors[0].code).toBe('required');\n    });\n  });\n  \n  describe('convertError', () => {\n    it('should return the error if it is already a UAConnectorError', () => {\n      const error = new UAConnectorError('Test error');\n      \n      const result = convertError(error);\n      \n      expect(result).toBe(error);\n    });\n    \n    it('should convert Axios error', () => {\n      const error = {\n        isAxiosError: true,\n        response: {\n          status: 404,\n          data: { message: 'Not Found' }\n        },\n        config: {\n          url: 'https://api.example.com/users/123',\n          method: 'get'\n        }\n      };\n      \n      const result = convertError(error);\n      \n      expect(result).toBeInstanceOf(ResourceNotFoundError);\n    });\n    \n    it('should convert JSON Schema validation error', () => {\n      const error = {\n        errors: [\n          {\n            keyword: 'required',\n            dataPath: '.user',\n            message: 'should have required property \\'name\\''\n          }\n        ]\n      };\n      \n      const result = convertError(error, { schemaName: 'UserSchema' });\n      \n      expect(result).toBeInstanceOf(ValidationError);\n    });\n    \n    it('should convert generic error to UAConnectorError', () => {\n      const error = new Error('Generic error');\n      \n      const result = convertError(error);\n      \n      expect(result).toBeInstanceOf(UAConnectorError);\n      expect(result.message).toBe('Generic error');\n      expect(result.cause).toBe(error);\n    });\n  });\n});\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,MAAM;EACJA,iBAAiB;EACjBC,sBAAsB;EACtBC;AACF,CAAC,GAAGC,OAAO,CAAC,iCAAiC,CAAC;AAE9C,MAAM;EACJC,gBAAgB;EAChBC,YAAY;EACZC,YAAY;EACZC,uBAAuB;EACvBC,mBAAmB;EACnBC,qBAAqB;EACrBC,sBAAsB;EACtBC,eAAe;EACfC,WAAW;EACXC;AACF,CAAC,GAAGV,OAAO,CAAC,kBAAkB,CAAC;AAE/BW,QAAQ,CAAC,iBAAiB,EAAE,MAAM;EAChCA,QAAQ,CAAC,mBAAmB,EAAE,MAAM;IAClCC,EAAE,CAAC,6DAA6D,EAAE,MAAM;MACtE,MAAMC,KAAK,GAAG,IAAIZ,gBAAgB,CAAC,YAAY,CAAC;MAEhD,MAAMa,MAAM,GAAGjB,iBAAiB,CAACgB,KAAK,CAAC;MAEvCE,MAAM,CAACD,MAAM,CAAC,CAACE,IAAI,CAACH,KAAK,CAAC;IAC5B,CAAC,CAAC;IAEFD,EAAE,CAAC,8BAA8B,EAAE,MAAM;MACvC,MAAMC,KAAK,GAAG;QACZI,IAAI,EAAE,cAAc;QACpBC,OAAO,EAAE,4BAA4B;QACrCC,MAAM,EAAE;UACNC,GAAG,EAAE,yBAAyB;UAC9BC,MAAM,EAAE;QACV;MACF,CAAC;MAED,MAAMP,MAAM,GAAGjB,iBAAiB,CAACgB,KAAK,CAAC;MAEvCE,MAAM,CAACD,MAAM,CAAC,CAACQ,cAAc,CAACpB,YAAY,CAAC;MAC3Ca,MAAM,CAACD,MAAM,CAACI,OAAO,CAAC,CAACF,IAAI,CAAC,mBAAmB,CAAC;MAChDD,MAAM,CAACD,MAAM,CAACS,KAAK,CAAC,CAACP,IAAI,CAACH,KAAK,CAAC;MAChCE,MAAM,CAACD,MAAM,CAACU,OAAO,CAACC,OAAO,CAACL,GAAG,CAAC,CAACJ,IAAI,CAAC,yBAAyB,CAAC;IACpE,CAAC,CAAC;IAEFJ,EAAE,CAAC,8BAA8B,EAAE,MAAM;MACvC,MAAMC,KAAK,GAAG;QACZI,IAAI,EAAE,WAAW;QACjBC,OAAO,EAAE,uCAAuC;QAChDC,MAAM,EAAE;UACNC,GAAG,EAAE,yBAAyB;UAC9BC,MAAM,EAAE;QACV;MACF,CAAC;MAED,MAAMP,MAAM,GAAGjB,iBAAiB,CAACgB,KAAK,CAAC;MAEvCE,MAAM,CAACD,MAAM,CAAC,CAACQ,cAAc,CAACnB,YAAY,CAAC;MAC3CY,MAAM,CAACD,MAAM,CAACI,OAAO,CAAC,CAACF,IAAI,CAAC,0BAA0B,CAAC;MACvDD,MAAM,CAACD,MAAM,CAACS,KAAK,CAAC,CAACP,IAAI,CAACH,KAAK,CAAC;IAClC,CAAC,CAAC;IAEFD,EAAE,CAAC,uCAAuC,EAAE,MAAM;MAChD,MAAMC,KAAK,GAAG;QACZa,QAAQ,EAAE;UACRC,MAAM,EAAE,GAAG;UACXC,IAAI,EAAE;YAAEV,OAAO,EAAE;UAAe;QAClC,CAAC;QACDC,MAAM,EAAE;UACNC,GAAG,EAAE,yBAAyB;UAC9BC,MAAM,EAAE;QACV;MACF,CAAC;MAED,MAAMP,MAAM,GAAGjB,iBAAiB,CAACgB,KAAK,CAAC;MAEvCE,MAAM,CAACD,MAAM,CAAC,CAACQ,cAAc,CAAClB,uBAAuB,CAAC;MACtDW,MAAM,CAACD,MAAM,CAACe,UAAU,CAAC,CAACb,IAAI,CAAC,GAAG,CAAC;MACnCD,MAAM,CAACD,MAAM,CAACY,QAAQ,CAAC,CAACI,OAAO,CAAC;QAAEZ,OAAO,EAAE;MAAe,CAAC,CAAC;IAC9D,CAAC,CAAC;IAEFN,EAAE,CAAC,oCAAoC,EAAE,MAAM;MAC7C,MAAMC,KAAK,GAAG;QACZa,QAAQ,EAAE;UACRC,MAAM,EAAE,GAAG;UACXC,IAAI,EAAE;YAAEV,OAAO,EAAE;UAAY;QAC/B,CAAC;QACDC,MAAM,EAAE;UACNC,GAAG,EAAE,yBAAyB;UAC9BC,MAAM,EAAE;QACV;MACF,CAAC;MAED,MAAMP,MAAM,GAAGjB,iBAAiB,CAACgB,KAAK,CAAC;MAEvCE,MAAM,CAACD,MAAM,CAAC,CAACQ,cAAc,CAACjB,mBAAmB,CAAC;MAClDU,MAAM,CAACD,MAAM,CAACG,IAAI,CAAC,CAACD,IAAI,CAAC,gBAAgB,CAAC;MAC1CD,MAAM,CAACD,MAAM,CAACe,UAAU,CAAC,CAACb,IAAI,CAAC,GAAG,CAAC;IACrC,CAAC,CAAC;IAEFJ,EAAE,CAAC,oCAAoC,EAAE,MAAM;MAC7C,MAAMC,KAAK,GAAG;QACZa,QAAQ,EAAE;UACRC,MAAM,EAAE,GAAG;UACXC,IAAI,EAAE;YAAEV,OAAO,EAAE;UAAY;QAC/B,CAAC;QACDC,MAAM,EAAE;UACNC,GAAG,EAAE,mCAAmC;UACxCC,MAAM,EAAE;QACV;MACF,CAAC;MAED,MAAMP,MAAM,GAAGjB,iBAAiB,CAACgB,KAAK,CAAC;MAEvCE,MAAM,CAACD,MAAM,CAAC,CAACQ,cAAc,CAAChB,qBAAqB,CAAC;MACpDS,MAAM,CAACD,MAAM,CAACe,UAAU,CAAC,CAACb,IAAI,CAAC,GAAG,CAAC;MACnCD,MAAM,CAACD,MAAM,CAACiB,UAAU,CAAC,CAACf,IAAI,CAAC,mCAAmC,CAAC;IACrE,CAAC,CAAC;IAEFJ,EAAE,CAAC,4CAA4C,EAAE,MAAM;MACrD,MAAMC,KAAK,GAAG;QACZa,QAAQ,EAAE;UACRC,MAAM,EAAE,GAAG;UACXC,IAAI,EAAE;YAAEV,OAAO,EAAE;UAAoB,CAAC;UACtCc,OAAO,EAAE;YACP,aAAa,EAAE;UACjB;QACF,CAAC;QACDb,MAAM,EAAE;UACNC,GAAG,EAAE,yBAAyB;UAC9BC,MAAM,EAAE;QACV;MACF,CAAC;MAED,MAAMP,MAAM,GAAGjB,iBAAiB,CAACgB,KAAK,CAAC;MAEvCE,MAAM,CAACD,MAAM,CAAC,CAACQ,cAAc,CAACf,sBAAsB,CAAC;MACrDQ,MAAM,CAACD,MAAM,CAACe,UAAU,CAAC,CAACb,IAAI,CAAC,GAAG,CAAC;MACnCD,MAAM,CAACD,MAAM,CAACmB,UAAU,CAAC,CAACjB,IAAI,CAAC,EAAE,CAAC;IACpC,CAAC,CAAC;IAEFJ,EAAE,CAAC,sCAAsC,EAAE,MAAM;MAC/C,MAAMC,KAAK,GAAG;QACZa,QAAQ,EAAE;UACRC,MAAM,EAAE,GAAG;UACXC,IAAI,EAAE;YAAEV,OAAO,EAAE;UAAc;QACjC,CAAC;QACDC,MAAM,EAAE;UACNC,GAAG,EAAE,yBAAyB;UAC9BC,MAAM,EAAE;QACV;MACF,CAAC;MAED,MAAMP,MAAM,GAAGjB,iBAAiB,CAACgB,KAAK,CAAC;MAEvCE,MAAM,CAACD,MAAM,CAAC,CAACQ,cAAc,CAACd,eAAe,CAAC;MAC9CO,MAAM,CAACD,MAAM,CAACe,UAAU,CAAC,CAACb,IAAI,CAAC,GAAG,CAAC;IACrC,CAAC,CAAC;IAEFJ,EAAE,CAAC,iCAAiC,EAAE,MAAM;MAC1C,MAAMC,KAAK,GAAG;QACZa,QAAQ,EAAE;UACRC,MAAM,EAAE,GAAG;UACXC,IAAI,EAAE;YAAEV,OAAO,EAAE;UAAwB;QAC3C,CAAC;QACDC,MAAM,EAAE;UACNC,GAAG,EAAE,yBAAyB;UAC9BC,MAAM,EAAE;QACV;MACF,CAAC;MAED,MAAMP,MAAM,GAAGjB,iBAAiB,CAACgB,KAAK,CAAC;MAEvCE,MAAM,CAACD,MAAM,CAAC,CAACQ,cAAc,CAACb,WAAW,CAAC;MAC1CM,MAAM,CAACD,MAAM,CAACe,UAAU,CAAC,CAACb,IAAI,CAAC,GAAG,CAAC;IACrC,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFL,QAAQ,CAAC,wBAAwB,EAAE,MAAM;IACvCC,EAAE,CAAC,6DAA6D,EAAE,MAAM;MACtE,MAAMC,KAAK,GAAG,IAAIZ,gBAAgB,CAAC,YAAY,CAAC;MAEhD,MAAMa,MAAM,GAAGhB,sBAAsB,CAACe,KAAK,EAAE,YAAY,CAAC;MAE1DE,MAAM,CAACD,MAAM,CAAC,CAACE,IAAI,CAACH,KAAK,CAAC;IAC5B,CAAC,CAAC;IAEFD,EAAE,CAAC,6CAA6C,EAAE,MAAM;MACtD,MAAMC,KAAK,GAAG;QACZqB,MAAM,EAAE,CACN;UACEC,OAAO,EAAE,UAAU;UACnBC,QAAQ,EAAE,OAAO;UACjBC,UAAU,EAAE,4BAA4B;UACxCnB,OAAO,EAAE;QACX,CAAC,EACD;UACEiB,OAAO,EAAE,QAAQ;UACjBC,QAAQ,EAAE,aAAa;UACvBC,UAAU,EAAE,2CAA2C;UACvDnB,OAAO,EAAE;QACX,CAAC;MAEL,CAAC;MAED,MAAMJ,MAAM,GAAGhB,sBAAsB,CAACe,KAAK,EAAE,YAAY,CAAC;MAE1DE,MAAM,CAACD,MAAM,CAAC,CAACQ,cAAc,CAACZ,eAAe,CAAC;MAC9CK,MAAM,CAACD,MAAM,CAACG,IAAI,CAAC,CAACD,IAAI,CAAC,yBAAyB,CAAC;MACnDD,MAAM,CAACD,MAAM,CAACwB,gBAAgB,CAACC,MAAM,CAAC,CAACvB,IAAI,CAAC,CAAC,CAAC;MAC9CD,MAAM,CAACD,MAAM,CAACwB,gBAAgB,CAAC,CAAC,CAAC,CAACE,KAAK,CAAC,CAACxB,IAAI,CAAC,OAAO,CAAC;MACtDD,MAAM,CAACD,MAAM,CAACwB,gBAAgB,CAAC,CAAC,CAAC,CAACrB,IAAI,CAAC,CAACD,IAAI,CAAC,UAAU,CAAC;IAC1D,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFL,QAAQ,CAAC,cAAc,EAAE,MAAM;IAC7BC,EAAE,CAAC,6DAA6D,EAAE,MAAM;MACtE,MAAMC,KAAK,GAAG,IAAIZ,gBAAgB,CAAC,YAAY,CAAC;MAEhD,MAAMa,MAAM,GAAGf,YAAY,CAACc,KAAK,CAAC;MAElCE,MAAM,CAACD,MAAM,CAAC,CAACE,IAAI,CAACH,KAAK,CAAC;IAC5B,CAAC,CAAC;IAEFD,EAAE,CAAC,4BAA4B,EAAE,MAAM;MACrC,MAAMC,KAAK,GAAG;QACZ4B,YAAY,EAAE,IAAI;QAClBf,QAAQ,EAAE;UACRC,MAAM,EAAE,GAAG;UACXC,IAAI,EAAE;YAAEV,OAAO,EAAE;UAAY;QAC/B,CAAC;QACDC,MAAM,EAAE;UACNC,GAAG,EAAE,mCAAmC;UACxCC,MAAM,EAAE;QACV;MACF,CAAC;MAED,MAAMP,MAAM,GAAGf,YAAY,CAACc,KAAK,CAAC;MAElCE,MAAM,CAACD,MAAM,CAAC,CAACQ,cAAc,CAAChB,qBAAqB,CAAC;IACtD,CAAC,CAAC;IAEFM,EAAE,CAAC,6CAA6C,EAAE,MAAM;MACtD,MAAMC,KAAK,GAAG;QACZqB,MAAM,EAAE,CACN;UACEC,OAAO,EAAE,UAAU;UACnBC,QAAQ,EAAE,OAAO;UACjBlB,OAAO,EAAE;QACX,CAAC;MAEL,CAAC;MAED,MAAMJ,MAAM,GAAGf,YAAY,CAACc,KAAK,EAAE;QAAE6B,UAAU,EAAE;MAAa,CAAC,CAAC;MAEhE3B,MAAM,CAACD,MAAM,CAAC,CAACQ,cAAc,CAACZ,eAAe,CAAC;IAChD,CAAC,CAAC;IAEFE,EAAE,CAAC,kDAAkD,EAAE,MAAM;MAC3D,MAAMC,KAAK,GAAG,IAAI8B,KAAK,CAAC,eAAe,CAAC;MAExC,MAAM7B,MAAM,GAAGf,YAAY,CAACc,KAAK,CAAC;MAElCE,MAAM,CAACD,MAAM,CAAC,CAACQ,cAAc,CAACrB,gBAAgB,CAAC;MAC/Cc,MAAM,CAACD,MAAM,CAACI,OAAO,CAAC,CAACF,IAAI,CAAC,eAAe,CAAC;MAC5CD,MAAM,CAACD,MAAM,CAACS,KAAK,CAAC,CAACP,IAAI,CAACH,KAAK,CAAC;IAClC,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}