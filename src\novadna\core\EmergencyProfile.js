/**
 * EmergencyProfile.js
 * 
 * This module defines the schema and operations for emergency medical profiles
 * in the NovaDNA system. It ensures that critical medical information is
 * available during emergencies while maintaining privacy and security.
 */

const { v4: uuidv4 } = require('uuid');

/**
 * EmergencyProfile class for managing emergency medical profiles
 */
class EmergencyProfile {
  constructor(options = {}) {
    this.schemaVersion = options.schemaVersion || '1.0.0';
    this.requiredFields = [
      'fullName',
      'dateOfBirth',
      'bloodType',
      'emergencyContacts'
    ];
    this.sensitiveFields = [
      'allergies',
      'medications',
      'medicalConditions',
      'insuranceInfo'
    ];
    this.profileValidators = this._createValidators();
  }

  /**
   * Create validators for profile fields
   * @returns {Object} - Object containing validation functions
   * @private
   */
  _createValidators() {
    return {
      fullName: (value) => typeof value === 'string' && value.length >= 2,
      dateOfBirth: (value) => {
        const date = new Date(value);
        return !isNaN(date.getTime());
      },
      bloodType: (value) => {
        const validBloodTypes = ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-', 'Unknown'];
        return validBloodTypes.includes(value);
      },
      emergencyContacts: (value) => {
        if (!Array.isArray(value) || value.length === 0) return false;
        return value.every(contact => 
          typeof contact.name === 'string' && 
          typeof contact.phone === 'string' && 
          contact.relationship
        );
      },
      allergies: (value) => {
        if (!Array.isArray(value)) return false;
        return value.every(allergy => 
          typeof allergy.name === 'string' && 
          typeof allergy.severity === 'string'
        );
      },
      medications: (value) => {
        if (!Array.isArray(value)) return false;
        return value.every(med => 
          typeof med.name === 'string' && 
          typeof med.dosage === 'string' && 
          typeof med.frequency === 'string'
        );
      },
      medicalConditions: (value) => {
        if (!Array.isArray(value)) return false;
        return value.every(condition => 
          typeof condition.name === 'string' && 
          typeof condition.diagnosisDate === 'string'
        );
      },
      insuranceInfo: (value) => {
        if (!value) return true; // Optional
        return (
          typeof value.provider === 'string' && 
          typeof value.policyNumber === 'string'
        );
      },
      dnr: (value) => {
        if (value === undefined) return true; // Optional
        return typeof value === 'boolean';
      },
      organDonor: (value) => {
        if (value === undefined) return true; // Optional
        return typeof value === 'boolean';
      }
    };
  }

  /**
   * Create a new emergency profile
   * @param {Object} profileData - The profile data
   * @returns {Object} - The created profile
   */
  createProfile(profileData) {
    // Validate required fields
    for (const field of this.requiredFields) {
      if (!profileData[field]) {
        throw new Error(`Missing required field: ${field}`);
      }
      
      if (!this.profileValidators[field](profileData[field])) {
        throw new Error(`Invalid value for field: ${field}`);
      }
    }
    
    // Validate optional fields if present
    for (const field in profileData) {
      if (this.profileValidators[field] && !this.profileValidators[field](profileData[field])) {
        throw new Error(`Invalid value for field: ${field}`);
      }
    }
    
    // Create the profile
    const profile = {
      profileId: uuidv4(),
      schemaVersion: this.schemaVersion,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      ...profileData
    };
    
    return profile;
  }

  /**
   * Update an existing emergency profile
   * @param {Object} existingProfile - The existing profile
   * @param {Object} updates - The updates to apply
   * @returns {Object} - The updated profile
   */
  updateProfile(existingProfile, updates) {
    if (!existingProfile || !existingProfile.profileId) {
      throw new Error('Invalid existing profile');
    }
    
    // Validate updates
    for (const field in updates) {
      if (this.profileValidators[field] && !this.profileValidators[field](updates[field])) {
        throw new Error(`Invalid value for field: ${field}`);
      }
    }
    
    // Create updated profile
    const updatedProfile = {
      ...existingProfile,
      ...updates,
      updatedAt: new Date().toISOString()
    };
    
    return updatedProfile;
  }

  /**
   * Create an emergency access token for a profile
   * @param {Object} profile - The profile to create a token for
   * @param {Object} options - Token creation options
   * @returns {Object} - The created token
   */
  createEmergencyAccessToken(profile, options = {}) {
    if (!profile || !profile.profileId) {
      throw new Error('Invalid profile');
    }
    
    const expiresIn = options.expiresIn || 3600; // 1 hour default
    const accessLevel = options.accessLevel || 'full';
    
    const token = {
      tokenId: uuidv4(),
      profileId: profile.profileId,
      createdAt: new Date().toISOString(),
      expiresAt: new Date(Date.now() + expiresIn * 1000).toISOString(),
      accessLevel,
      emergencyType: options.emergencyType || 'medical',
      issuedTo: options.issuedTo || 'emergency_services',
      issuedBy: options.issuedBy || 'system'
    };
    
    return token;
  }

  /**
   * Get a filtered view of a profile based on access level
   * @param {Object} profile - The full profile
   * @param {String} accessLevel - The access level
   * @returns {Object} - The filtered profile
   */
  getFilteredProfile(profile, accessLevel = 'basic') {
    if (!profile || !profile.profileId) {
      throw new Error('Invalid profile');
    }
    
    // Basic fields always included
    const filteredProfile = {
      profileId: profile.profileId,
      schemaVersion: profile.schemaVersion,
      fullName: profile.fullName,
      dateOfBirth: profile.dateOfBirth,
      bloodType: profile.bloodType,
      emergencyContacts: profile.emergencyContacts
    };
    
    // Add critical medical info for standard access
    if (accessLevel === 'standard' || accessLevel === 'full') {
      if (profile.allergies) filteredProfile.allergies = profile.allergies;
      if (profile.medications) filteredProfile.medications = profile.medications;
      if (profile.medicalConditions) filteredProfile.medicalConditions = profile.medicalConditions;
      if (profile.dnr !== undefined) filteredProfile.dnr = profile.dnr;
      if (profile.organDonor !== undefined) filteredProfile.organDonor = profile.organDonor;
    }
    
    // Add sensitive info for full access
    if (accessLevel === 'full') {
      if (profile.insuranceInfo) filteredProfile.insuranceInfo = profile.insuranceInfo;
      if (profile.primaryCareProvider) filteredProfile.primaryCareProvider = profile.primaryCareProvider;
      if (profile.notes) filteredProfile.notes = profile.notes;
    }
    
    return filteredProfile;
  }

  /**
   * Validate a profile against the schema
   * @param {Object} profile - The profile to validate
   * @returns {Object} - Validation result
   */
  validateProfile(profile) {
    if (!profile || !profile.profileId) {
      return {
        valid: false,
        errors: ['Invalid profile: missing profileId']
      };
    }
    
    const errors = [];
    
    // Check required fields
    for (const field of this.requiredFields) {
      if (!profile[field]) {
        errors.push(`Missing required field: ${field}`);
      } else if (this.profileValidators[field] && !this.profileValidators[field](profile[field])) {
        errors.push(`Invalid value for field: ${field}`);
      }
    }
    
    // Check optional fields if present
    for (const field in profile) {
      if (
        this.profileValidators[field] && 
        !this.requiredFields.includes(field) && 
        !this.profileValidators[field](profile[field])
      ) {
        errors.push(`Invalid value for field: ${field}`);
      }
    }
    
    return {
      valid: errors.length === 0,
      errors: errors.length > 0 ? errors : undefined,
      schemaVersion: this.schemaVersion
    };
  }

  /**
   * Get the schema definition
   * @returns {Object} - The schema definition
   */
  getSchemaDefinition() {
    return {
      schemaVersion: this.schemaVersion,
      requiredFields: this.requiredFields,
      sensitiveFields: this.sensitiveFields,
      fields: {
        profileId: {
          type: 'string',
          format: 'uuid',
          description: 'Unique identifier for the profile'
        },
        schemaVersion: {
          type: 'string',
          description: 'Version of the schema used for this profile'
        },
        fullName: {
          type: 'string',
          description: 'Full name of the individual'
        },
        dateOfBirth: {
          type: 'string',
          format: 'date',
          description: 'Date of birth in ISO format'
        },
        bloodType: {
          type: 'string',
          enum: ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-', 'Unknown'],
          description: 'Blood type'
        },
        emergencyContacts: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              name: { type: 'string' },
              relationship: { type: 'string' },
              phone: { type: 'string' }
            }
          },
          description: 'Emergency contacts'
        },
        allergies: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              name: { type: 'string' },
              severity: { type: 'string', enum: ['Mild', 'Moderate', 'Severe', 'Life-threatening'] },
              reactions: { type: 'array', items: { type: 'string' } }
            }
          },
          description: 'Known allergies'
        },
        medications: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              name: { type: 'string' },
              dosage: { type: 'string' },
              frequency: { type: 'string' },
              purpose: { type: 'string' }
            }
          },
          description: 'Current medications'
        },
        medicalConditions: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              name: { type: 'string' },
              diagnosisDate: { type: 'string', format: 'date' },
              notes: { type: 'string' }
            }
          },
          description: 'Medical conditions'
        },
        insuranceInfo: {
          type: 'object',
          properties: {
            provider: { type: 'string' },
            policyNumber: { type: 'string' },
            groupNumber: { type: 'string' },
            phone: { type: 'string' }
          },
          description: 'Insurance information'
        },
        dnr: {
          type: 'boolean',
          description: 'Do Not Resuscitate status'
        },
        organDonor: {
          type: 'boolean',
          description: 'Organ donor status'
        },
        primaryCareProvider: {
          type: 'object',
          properties: {
            name: { type: 'string' },
            phone: { type: 'string' },
            address: { type: 'string' }
          },
          description: 'Primary care provider information'
        },
        notes: {
          type: 'string',
          description: 'Additional medical notes'
        }
      }
    };
  }
}

module.exports = EmergencyProfile;
