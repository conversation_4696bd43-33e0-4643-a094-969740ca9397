/**
 * Quantum Protein Folding Dashboard Styles
 * Custom styles for the dashboard UI
 */

/* Global Styles */
:root {
    --primary-color: #4e73df;
    --secondary-color: #858796;
    --success-color: #1cc88a;
    --info-color: #36b9cc;
    --warning-color: #f6c23e;
    --danger-color: #e74a3b;
    --light-color: #f8f9fc;
    --dark-color: #5a5c69;
    --purple: #6f42c1;
    --pink: #e83e8c;
    --teal: #20c9a6;
    --cyan: #36b9cc;
    --primary-light: #eaecf4;
    --gray-100: #f8f9fc;
    --gray-200: #e3e6f0;
    --gray-300: #dddfeb;
    --gray-400: #d1d3e2;
    --gray-500: #b7b9cc;
    --gray-600: #858796;
    --gray-700: #6e707e;
    --gray-800: #5a5c69;
    --gray-900: #3a3b45;
    --font-sans-serif: '<PERSON>uni<PERSON>', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
    --font-monospace: SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;
}

body {
    font-family: var(--font-sans-serif);
    font-size: 0.9rem;
    color: var(--gray-800);
    background-color: var(--gray-100);
}

/* Sidebar Styles */
.sidebar {
    min-height: 100vh;
    background: linear-gradient(180deg, var(--primary-color) 0%, #224abe 100%);
    color: white;
    padding: 0;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    z-index: 1000;
}

.sidebar .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 0.75rem 1rem;
    margin: 0.2rem 0.5rem;
    border-radius: 0.35rem;
    transition: all 0.3s;
}

.sidebar .nav-link:hover {
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
}

.sidebar .nav-link.active {
    color: var(--primary-color);
    background-color: white;
    font-weight: 600;
}

.sidebar .nav-link i {
    margin-right: 0.5rem;
    width: 1.2rem;
    text-align: center;
}

/* Main Content */
.main-content {
    padding: 1.5rem 0;
    min-height: 100vh;
    background-color: var(--gray-100);
}

/* Card Styles */
.card {
    border: none;
    border-radius: 0.5rem;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.1);
    margin-bottom: 1.5rem;
    transition: transform 0.3s, box-shadow 0.3s;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 2rem 0 rgba(58, 59, 69, 0.2);
}

.card-header {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
    padding: 1rem 1.25rem;
    font-weight: 600;
    color: var(--gray-800);
}

/* Table Styles */
.table {
    margin-bottom: 0;
}

.table th {
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.7rem;
    letter-spacing: 0.08em;
    color: var(--gray-600);
    border-top: none;
    padding: 0.75rem 1rem;
}

.table td {
    padding: 1rem;
    vertical-align: middle;
    border-color: #e3e6f0;
}

/* Badge Styles */
.badge {
    font-weight: 500;
    padding: 0.4em 0.6em;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Progress Bar */
.progress {
    height: 0.5rem;
    border-radius: 0.25rem;
    background-color: var(--gray-200);
}

.progress-bar {
    transition: width 0.6s ease;
}

/* Buttons */
.btn {
    font-weight: 500;
    padding: 0.375rem 0.75rem;
    font-size: 0.8rem;
    line-height: 1.5;
    border-radius: 0.35rem;
    transition: all 0.15s;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #2e59d9;
    border-color: #2653d4;
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Form Controls */
.form-control, .form-select {
    font-size: 0.85rem;
    padding: 0.375rem 0.75rem;
    border: 1px solid #d1d3e2;
    border-radius: 0.35rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus, .form-select:focus {
    border-color: #bac8f3;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        width: 250px;
        transform: translateX(-100%);
        transition: transform 0.3s ease-in-out;
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
        width: 100%;
    }
    
    .card {
        margin-bottom: 1rem;
    }
}

/* Animation for loading states */
@keyframes pulse {
    0% { opacity: 0.6; }
    50% { opacity: 1; }
    100% { opacity: 0.6; }
}

.loading {
    animation: pulse 1.5s infinite;
}

/* Custom utility classes */
.text-purple {
    color: var(--purple) !important;
}

.bg-purple {
    background-color: var(--purple) !important;
}

/* Experiment status indicators */
.status-indicator {
    display: inline-block;
    width: 0.8rem;
    height: 0.8rem;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.status-indicator.queued {
    background-color: var(--secondary-color);
}

.status-indicator.running {
    background-color: var(--warning-color);
    animation: pulse 1.5s infinite;
}

.status-indicator.completed {
    background-color: var(--success-color);
}

.status-indicator.failed {
    background-color: var(--danger-color);
}

/* Custom tooltip styling */
.tooltip-inner {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
    max-width: 250px;
}

/* Custom checkbox and radio button styling */
.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Custom tab styling */
.nav-tabs .nav-link {
    color: var(--gray-600);
    font-weight: 500;
    border: none;
    padding: 0.75rem 1.25rem;
    border-bottom: 2px solid transparent;
}

.nav-tabs .nav-link.active {
    color: var(--primary-color);
    background: transparent;
    border-bottom: 2px solid var(--primary-color);
}

.nav-tabs .nav-link:hover {
    border-color: transparent;
    border-bottom: 2px solid var(--gray-300);
}

/* Custom modal styling */
.modal-content {
    border: none;
    border-radius: 0.5rem;
    box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.15);
}

.modal-header {
    border-bottom: 1px solid #e3e6f0;
    padding: 1.25rem 1.5rem;
}

.modal-footer {
    border-top: 1px solid #e3e6f0;
    padding: 1rem 1.5rem;
}

/* Custom alert styling */
.alert {
    border: none;
    border-radius: 0.5rem;
    padding: 1rem 1.25rem;
    margin-bottom: 1rem;
}

.alert-dismissible .btn-close {
    padding: 1rem;
}

/* Custom form switch */
.form-switch .form-check-input {
    width: 2.5em;
    margin-left: -2.5em;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='rgba%280, 0, 0, 0.25%29'/%3e%3c/svg%3e");
    background-position: left center;
    border-radius: 2em;
    transition: background-position 0.15s ease-in-out;
}

.form-switch .form-check-input:checked {
    background-position: right center;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23fff'/%3e%3c/svg%3e");
}
