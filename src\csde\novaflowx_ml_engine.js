/**
 * NovaFlowX ML Engine
 * 
 * This module integrates the CSDE ML capabilities with the NovaFlowX engine
 * for automated remediation.
 */

const { CSDEEngine, NovaFlowXEngine } = require('./index');
const CSDEMLIntegration = require('./ml/csde_ml_integration');
const fs = require('fs');
const path = require('path');

class NovaFlowXMLEngine {
  /**
   * Create a new NovaFlowX ML Engine instance
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    this.options = {
      confidenceThreshold: 0.7, // Minimum confidence score for automated remediation
      automationLevels: ['high', 'medium'], // Automation levels to consider
      priorityLevels: ['critical', 'high', 'medium'], // Priority levels to consider
      dryRun: false, // Whether to perform a dry run (no actual changes)
      maxConcurrentRemediations: 5, // Maximum number of concurrent remediations
      feedbackEnabled: true, // Whether to enable feedback loop
      ...options
    };
    
    // Initialize CSDE ML Integration
    this.csdeMLIntegration = new CSDEMLIntegration();
    
    // Initialize NovaFlowX Engine
    this.novaFlowX = new NovaFlowXEngine();
    
    // Initialize remediation history
    this.remediationHistory = [];
    
    console.log('NovaFlowX ML Engine initialized');
    console.log(`Configuration: confidence threshold=${this.options.confidenceThreshold}, automation levels=${this.options.automationLevels.join(',')}, priority levels=${this.options.priorityLevels.join(',')}, dry run=${this.options.dryRun}`);
  }
  
  /**
   * Analyze and remediate compliance, GCP, and Cyber-Safety issues
   * @param {Object} complianceData - Compliance data
   * @param {Object} gcpData - GCP integration data
   * @param {Object} cyberSafetyData - Cyber-Safety data
   * @param {Object} options - Options for this specific run
   * @returns {Object} - Analysis and remediation results
   */
  async analyzeAndRemediate(complianceData, gcpData, cyberSafetyData, options = {}) {
    console.log('Analyzing and remediating issues...');
    
    // Merge options with defaults
    const runOptions = {
      ...this.options,
      ...options
    };
    
    // Get ML-enhanced remediation actions
    const analysisResult = this.csdeMLIntegration.calculate(
      complianceData, 
      gcpData, 
      cyberSafetyData
    );
    
    // Filter actions based on confidence, automation potential, and priority
    const actionsToRemediate = this._filterRemediationActions(
      analysisResult.remediationActions,
      runOptions
    );
    
    console.log(`Selected ${actionsToRemediate.length} actions for remediation`);
    
    // Execute remediation actions
    const remediationResults = await this._executeRemediationActions(
      actionsToRemediate,
      runOptions
    );
    
    // Update remediation history
    this._updateRemediationHistory(remediationResults);
    
    // Enable feedback loop if configured
    if (runOptions.feedbackEnabled) {
      this._processFeedback(remediationResults);
    }
    
    return {
      analysis: analysisResult,
      remediation: {
        actionsSelected: actionsToRemediate.length,
        actionsRemediated: remediationResults.filter(r => r.success).length,
        actionsFailed: remediationResults.filter(r => !r.success).length,
        results: remediationResults
      }
    };
  }
  
  /**
   * Filter remediation actions based on confidence, automation potential, and priority
   * @param {Array} remediationActions - Remediation actions
   * @param {Object} options - Options for filtering
   * @returns {Array} - Filtered remediation actions
   * @private
   */
  _filterRemediationActions(remediationActions, options) {
    return remediationActions.filter(action => {
      // Check confidence threshold
      if (action.mlConfidence < options.confidenceThreshold) {
        return false;
      }
      
      // Check automation potential
      if (!options.automationLevels.includes(action.automationPotential)) {
        return false;
      }
      
      // Check priority
      if (!options.priorityLevels.includes(action.priority)) {
        return false;
      }
      
      return true;
    });
  }
  
  /**
   * Execute remediation actions
   * @param {Array} actions - Actions to remediate
   * @param {Object} options - Options for execution
   * @returns {Array} - Remediation results
   * @private
   */
  async _executeRemediationActions(actions, options) {
    console.log(`Executing ${actions.length} remediation actions${options.dryRun ? ' (DRY RUN)' : ''}...`);
    
    const results = [];
    
    // Process actions in batches to limit concurrency
    for (let i = 0; i < actions.length; i += options.maxConcurrentRemediations) {
      const batch = actions.slice(i, i + options.maxConcurrentRemediations);
      
      // Process batch concurrently
      const batchResults = await Promise.all(batch.map(action => this._executeRemediationAction(action, options)));
      
      results.push(...batchResults);
    }
    
    return results;
  }
  
  /**
   * Execute a single remediation action
   * @param {Object} action - Action to remediate
   * @param {Object} options - Options for execution
   * @returns {Object} - Remediation result
   * @private
   */
  async _executeRemediationAction(action, options) {
    console.log(`Executing remediation action: ${action.title} (${action.priority.toUpperCase()})${options.dryRun ? ' (DRY RUN)' : ''}`);
    
    try {
      // Create remediation plan
      const remediationPlan = this._createRemediationPlan(action);
      
      // If dry run, return success without executing
      if (options.dryRun) {
        return {
          action,
          success: true,
          dryRun: true,
          remediationPlan,
          executionTime: 0,
          message: 'Dry run - no changes made'
        };
      }
      
      // Execute remediation plan
      const startTime = Date.now();
      const result = await this.novaFlowX.executeRemediationPlan(remediationPlan);
      const executionTime = Date.now() - startTime;
      
      return {
        action,
        success: result.success,
        dryRun: false,
        remediationPlan,
        executionTime,
        message: result.message,
        details: result.details
      };
    } catch (error) {
      console.error(`Error executing remediation action: ${error.message}`);
      
      return {
        action,
        success: false,
        dryRun: options.dryRun,
        error: error.message,
        message: `Failed to execute remediation: ${error.message}`
      };
    }
  }
  
  /**
   * Create a remediation plan for an action
   * @param {Object} action - Action to remediate
   * @returns {Object} - Remediation plan
   * @private
   */
  _createRemediationPlan(action) {
    // Create remediation plan based on action type
    switch (action.type) {
      case 'compliance':
        return this._createComplianceRemediationPlan(action);
      case 'gcp':
        return this._createGcpRemediationPlan(action);
      case 'cyber-safety':
        return this._createCyberSafetyRemediationPlan(action);
      default:
        return this._createGenericRemediationPlan(action);
    }
  }
  
  /**
   * Create a compliance remediation plan
   * @param {Object} action - Action to remediate
   * @returns {Object} - Remediation plan
   * @private
   */
  _createComplianceRemediationPlan(action) {
    // Extract control ID from action title or description
    const controlIdMatch = action.title.match(/[A-Z]+-\d+/) || action.description.match(/[A-Z]+-\d+/);
    const controlId = controlIdMatch ? controlIdMatch[0] : '';
    
    return {
      type: 'compliance',
      controlId,
      title: action.title,
      description: action.description,
      priority: action.priority,
      steps: this._generateRemediationSteps(action)
    };
  }
  
  /**
   * Create a GCP remediation plan
   * @param {Object} action - Action to remediate
   * @returns {Object} - Remediation plan
   * @private
   */
  _createGcpRemediationPlan(action) {
    // Extract service from action title or description
    const serviceMatch = action.title.match(/IAM|VPC|KMS|GKE|Cloud SQL|BigQuery|Storage|Compute Engine/);
    const service = serviceMatch ? serviceMatch[0] : '';
    
    return {
      type: 'gcp',
      service,
      title: action.title,
      description: action.description,
      priority: action.priority,
      steps: this._generateRemediationSteps(action)
    };
  }
  
  /**
   * Create a Cyber-Safety remediation plan
   * @param {Object} action - Action to remediate
   * @returns {Object} - Remediation plan
   * @private
   */
  _createCyberSafetyRemediationPlan(action) {
    // Extract pillar from action title or description
    const pillarMatch = action.title.match(/Pillar \d+/) || action.description.match(/Pillar \d+/);
    const pillar = pillarMatch ? pillarMatch[0] : '';
    
    return {
      type: 'cyber-safety',
      pillar,
      title: action.title,
      description: action.description,
      priority: action.priority,
      steps: this._generateRemediationSteps(action)
    };
  }
  
  /**
   * Create a generic remediation plan
   * @param {Object} action - Action to remediate
   * @returns {Object} - Remediation plan
   * @private
   */
  _createGenericRemediationPlan(action) {
    return {
      type: 'generic',
      title: action.title,
      description: action.description,
      priority: action.priority,
      steps: this._generateRemediationSteps(action)
    };
  }
  
  /**
   * Generate remediation steps for an action
   * @param {Object} action - Action to remediate
   * @returns {Array} - Remediation steps
   * @private
   */
  _generateRemediationSteps(action) {
    // If action has steps, use them
    if (action.steps && action.steps.length > 0) {
      return action.steps;
    }
    
    // Otherwise, generate steps based on action type
    const steps = [];
    
    switch (action.type) {
      case 'compliance':
        steps.push(`Identify non-compliant resources for ${action.title}`);
        steps.push(`Apply compliance configuration for ${action.title}`);
        steps.push(`Verify compliance status for ${action.title}`);
        break;
      case 'gcp':
        steps.push(`Identify affected GCP resources for ${action.title}`);
        steps.push(`Apply GCP configuration changes for ${action.title}`);
        steps.push(`Verify GCP configuration for ${action.title}`);
        break;
      case 'cyber-safety':
        steps.push(`Identify affected systems for ${action.title}`);
        steps.push(`Apply Cyber-Safety controls for ${action.title}`);
        steps.push(`Verify Cyber-Safety implementation for ${action.title}`);
        break;
      default:
        steps.push(`Identify affected resources for ${action.title}`);
        steps.push(`Apply configuration changes for ${action.title}`);
        steps.push(`Verify implementation for ${action.title}`);
    }
    
    return steps;
  }
  
  /**
   * Update remediation history
   * @param {Array} results - Remediation results
   * @private
   */
  _updateRemediationHistory(results) {
    // Add timestamp to results
    const timestamp = new Date().toISOString();
    const resultsWithTimestamp = results.map(result => ({
      ...result,
      timestamp
    }));
    
    // Add to history
    this.remediationHistory.push(...resultsWithTimestamp);
    
    // Limit history size
    if (this.remediationHistory.length > 1000) {
      this.remediationHistory = this.remediationHistory.slice(-1000);
    }
    
    console.log(`Updated remediation history (${this.remediationHistory.length} entries)`);
  }
  
  /**
   * Process feedback from remediation results
   * @param {Array} results - Remediation results
   * @private
   */
  _processFeedback(results) {
    console.log('Processing feedback from remediation results...');
    
    // TODO: Implement feedback loop to improve ML recommendations
    // This would involve:
    // 1. Analyzing successful vs. failed remediations
    // 2. Adjusting confidence scores based on success rates
    // 3. Updating ML model based on feedback
    
    // For now, just log the feedback
    const successCount = results.filter(r => r.success).length;
    const failureCount = results.filter(r => !r.success).length;
    const successRate = results.length > 0 ? successCount / results.length : 0;
    
    console.log(`Feedback: ${successCount} successful, ${failureCount} failed (${(successRate * 100).toFixed(2)}% success rate)`);
  }
  
  /**
   * Get remediation history
   * @param {Object} options - Options for filtering history
   * @returns {Array} - Remediation history
   */
  getRemediationHistory(options = {}) {
    let history = [...this.remediationHistory];
    
    // Filter by success
    if (options.success !== undefined) {
      history = history.filter(entry => entry.success === options.success);
    }
    
    // Filter by type
    if (options.type) {
      history = history.filter(entry => entry.action.type === options.type);
    }
    
    // Filter by priority
    if (options.priority) {
      history = history.filter(entry => entry.action.priority === options.priority);
    }
    
    // Filter by date range
    if (options.startDate) {
      const startDate = new Date(options.startDate).getTime();
      history = history.filter(entry => new Date(entry.timestamp).getTime() >= startDate);
    }
    
    if (options.endDate) {
      const endDate = new Date(options.endDate).getTime();
      history = history.filter(entry => new Date(entry.timestamp).getTime() <= endDate);
    }
    
    // Sort by timestamp (newest first)
    history.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
    
    // Limit results
    if (options.limit) {
      history = history.slice(0, options.limit);
    }
    
    return history;
  }
  
  /**
   * Get remediation statistics
   * @returns {Object} - Remediation statistics
   */
  getRemediationStatistics() {
    const totalRemediations = this.remediationHistory.length;
    const successfulRemediations = this.remediationHistory.filter(entry => entry.success).length;
    const failedRemediations = totalRemediations - successfulRemediations;
    const successRate = totalRemediations > 0 ? successfulRemediations / totalRemediations : 0;
    
    // Calculate average execution time for successful remediations
    const successfulExecutionTimes = this.remediationHistory
      .filter(entry => entry.success && !entry.dryRun && entry.executionTime)
      .map(entry => entry.executionTime);
    
    const averageExecutionTime = successfulExecutionTimes.length > 0 ?
      successfulExecutionTimes.reduce((sum, time) => sum + time, 0) / successfulExecutionTimes.length :
      0;
    
    // Calculate statistics by type
    const typeStats = {};
    const types = ['compliance', 'gcp', 'cyber-safety', 'generic'];
    
    types.forEach(type => {
      const typeEntries = this.remediationHistory.filter(entry => entry.action.type === type);
      const typeSuccessful = typeEntries.filter(entry => entry.success).length;
      
      typeStats[type] = {
        total: typeEntries.length,
        successful: typeSuccessful,
        failed: typeEntries.length - typeSuccessful,
        successRate: typeEntries.length > 0 ? typeSuccessful / typeEntries.length : 0
      };
    });
    
    // Calculate statistics by priority
    const priorityStats = {};
    const priorities = ['critical', 'high', 'medium', 'low'];
    
    priorities.forEach(priority => {
      const priorityEntries = this.remediationHistory.filter(entry => entry.action.priority === priority);
      const prioritySuccessful = priorityEntries.filter(entry => entry.success).length;
      
      priorityStats[priority] = {
        total: priorityEntries.length,
        successful: prioritySuccessful,
        failed: priorityEntries.length - prioritySuccessful,
        successRate: priorityEntries.length > 0 ? prioritySuccessful / priorityEntries.length : 0
      };
    });
    
    return {
      total: totalRemediations,
      successful: successfulRemediations,
      failed: failedRemediations,
      successRate,
      averageExecutionTime,
      byType: typeStats,
      byPriority: priorityStats
    };
  }
}

module.exports = NovaFlowXMLEngine;
