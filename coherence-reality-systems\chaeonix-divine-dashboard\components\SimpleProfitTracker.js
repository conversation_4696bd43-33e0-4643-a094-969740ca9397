/**
 * SIMPLE PROFIT TRACKER
 * Streamlined profit analytics with accumulative views and category breakdowns
 * Shows day-to-day, week-by-week, month-by-month, YTD performance
 */

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

export default function SimpleProfitTracker() {
  const [profitData, setProfitData] = useState({
    today: { profit: 0.00, trades: 0, win_rate: 0.0 },
    this_week: { profit: 0.00, trades: 0, win_rate: 0.0 },
    this_month: { profit: 0.00, trades: 0, win_rate: 0.0 },
    ytd: { profit: 0.00, trades: 0, win_rate: 0.0 },
    categories: {
      STOCKS: { profit: 0.00, trades: 0, win_rate: 0.0, color: '#3B82F6' },
      CRYPTO: { profit: 0.00, trades: 0, win_rate: 0.0, color: '#F59E0B' },
      FOREX: { profit: 0.00, trades: 0, win_rate: 0.0, color: '#10B981' }
    },
    accumulative: {
      daily: [],
      weekly: [],
      monthly: []
    }
  });

  const [selectedView, setSelectedView] = useState('overview');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Load real profit data
    updateProfitData();

    // Update every 10 seconds with real data
    const interval = setInterval(() => {
      updateProfitData();
    }, 10000);

    return () => clearInterval(interval);
  }, []);

  const updateProfitData = async () => {
    try {
      const response = await fetch('/api/analytics/profit-tracker');
      const data = await response.json();

      if (data.success) {
        // Map API data to component state
        const stats = data.current_period_stats;
        const categories = data.category_performance || [];

        setProfitData({
          today: {
            profit: stats.today.total_profit || 0,
            trades: stats.today.total_trades || 0,
            win_rate: stats.today.win_rate || 0
          },
          this_week: {
            profit: stats.this_week.total_profit || 0,
            trades: stats.this_week.total_trades || 0,
            win_rate: stats.this_week.win_rate || 0
          },
          this_month: {
            profit: stats.this_month.total_profit || 0,
            trades: stats.this_month.total_trades || 0,
            win_rate: stats.this_month.win_rate || 0
          },
          ytd: {
            profit: stats.ytd_total || 0,
            trades: stats.this_year.total_trades || 0,
            win_rate: stats.this_year.win_rate || 0
          },
          categories: {
            STOCKS: {
              profit: categories.find(c => c.category === 'STOCKS')?.total_profit || 0,
              trades: categories.find(c => c.category === 'STOCKS')?.total_trades || 0,
              win_rate: categories.find(c => c.category === 'STOCKS')?.win_rate || 0,
              color: '#3B82F6'
            },
            CRYPTO: {
              profit: categories.find(c => c.category === 'CRYPTO')?.total_profit || 0,
              trades: categories.find(c => c.category === 'CRYPTO')?.total_trades || 0,
              win_rate: categories.find(c => c.category === 'CRYPTO')?.win_rate || 0,
              color: '#F59E0B'
            },
            FOREX: {
              profit: categories.find(c => c.category === 'FOREX')?.total_profit || 0,
              trades: categories.find(c => c.category === 'FOREX')?.total_trades || 0,
              win_rate: categories.find(c => c.category === 'FOREX')?.win_rate || 0,
              color: '#10B981'
            }
          },
          accumulative: {
            daily: data.accumulative_profits?.daily?.slice(-7) || [],
            weekly: data.accumulative_profits?.weekly?.slice(-4) || [],
            monthly: data.accumulative_profits?.monthly?.slice(-3) || []
          }
        });

        setIsLoading(false);
      }
    } catch (error) {
      console.error('Failed to load profit data:', error);
      setIsLoading(false);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(amount);
  };

  const formatPercentage = (value) => {
    return `${value.toFixed(1)}%`;
  };

  const getProfitColor = (profit) => {
    return profit >= 0 ? 'text-green-400' : 'text-red-400';
  };

  if (isLoading) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="p-6 rounded-lg border border-emerald-500/30 bg-gradient-to-br from-emerald-900/20 to-green-900/20 backdrop-blur-sm"
      >
        <div className="animate-pulse">
          <div className="h-6 bg-gray-600 rounded mb-4 w-1/3"></div>
          <div className="grid grid-cols-4 gap-4 mb-6">
            {[1,2,3,4].map(i => (
              <div key={i} className="h-20 bg-gray-600 rounded"></div>
            ))}
          </div>
          <div className="h-40 bg-gray-600 rounded"></div>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="p-6 rounded-lg border border-emerald-500/30 bg-gradient-to-br from-emerald-900/20 to-green-900/20 backdrop-blur-sm"
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-xl font-bold text-white">
            💰 Profit Analytics
          </h3>
          <p className="text-sm text-gray-400">
            Accumulative Performance & Category Breakdown
          </p>
        </div>
        
        <div className="flex space-x-2">
          <button
            onClick={() => setSelectedView('overview')}
            className={`px-3 py-1 rounded text-sm ${
              selectedView === 'overview' 
                ? 'bg-emerald-600 text-white' 
                : 'bg-gray-700 text-gray-300'
            }`}
          >
            Overview
          </button>
          <button
            onClick={() => setSelectedView('categories')}
            className={`px-3 py-1 rounded text-sm ${
              selectedView === 'categories' 
                ? 'bg-emerald-600 text-white' 
                : 'bg-gray-700 text-gray-300'
            }`}
          >
            Categories
          </button>
        </div>
      </div>

      {selectedView === 'overview' ? (
        <>
          {/* Time Period Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <div className="p-4 rounded-lg bg-gray-800/50 border border-gray-600">
              <div className="text-sm text-gray-400 mb-1">Today</div>
              <div className={`text-lg font-bold ${getProfitColor(profitData.today.profit)}`}>
                {formatCurrency(profitData.today.profit)}
              </div>
              <div className="text-xs text-gray-400">
                {profitData.today.trades} trades • {formatPercentage(profitData.today.win_rate)} win
              </div>
            </div>

            <div className="p-4 rounded-lg bg-gray-800/50 border border-gray-600">
              <div className="text-sm text-gray-400 mb-1">This Week</div>
              <div className={`text-lg font-bold ${getProfitColor(profitData.this_week.profit)}`}>
                {formatCurrency(profitData.this_week.profit)}
              </div>
              <div className="text-xs text-gray-400">
                {profitData.this_week.trades} trades • {formatPercentage(profitData.this_week.win_rate)} win
              </div>
            </div>

            <div className="p-4 rounded-lg bg-gray-800/50 border border-gray-600">
              <div className="text-sm text-gray-400 mb-1">This Month</div>
              <div className={`text-lg font-bold ${getProfitColor(profitData.this_month.profit)}`}>
                {formatCurrency(profitData.this_month.profit)}
              </div>
              <div className="text-xs text-gray-400">
                {profitData.this_month.trades} trades • {formatPercentage(profitData.this_month.win_rate)} win
              </div>
            </div>

            <div className="p-4 rounded-lg bg-gray-800/50 border border-gray-600">
              <div className="text-sm text-gray-400 mb-1">Year to Date</div>
              <div className={`text-lg font-bold ${getProfitColor(profitData.ytd.profit)}`}>
                {formatCurrency(profitData.ytd.profit)}
              </div>
              <div className="text-xs text-gray-400">
                {profitData.ytd.trades} trades • {formatPercentage(profitData.ytd.win_rate)} win
              </div>
            </div>
          </div>

          {/* Recent Performance Trend - REAL DATA */}
          <div className="mb-6">
            <h4 className="text-lg font-semibold text-white mb-3">📈 Recent Daily Performance</h4>
            <div className="bg-gray-800/30 rounded-lg p-4">
              <div className="space-y-2">
                {profitData.accumulative.daily.slice(-7).map((day, index) => (
                  <div key={index} className="flex items-center justify-between p-2 rounded bg-gray-700/50">
                    <span className="text-gray-300">{day.date}</span>
                    <div className="flex items-center space-x-2">
                      <span className={`font-medium ${getProfitColor(day.total_profit)}`}>
                        {formatCurrency(day.total_profit)}
                      </span>
                      <span className="text-blue-400 text-sm">
                        {day.total_trades} trades
                      </span>
                      <span className="text-emerald-400 text-sm">
                        {formatPercentage(day.win_rate)} win
                      </span>
                    </div>
                  </div>
                ))}

                {profitData.accumulative.daily.length === 0 && (
                  <div className="text-center text-gray-400 py-4">
                    No daily data available yet
                  </div>
                )}
              </div>
            </div>
          </div>
        </>
      ) : (
        <>
          {/* Category Breakdown */}
          <div className="mb-6">
            <h4 className="text-lg font-semibold text-white mb-4">📊 Performance by Market Category</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {Object.entries(profitData.categories).map(([category, data]) => (
                <div key={category} className="p-4 rounded-lg bg-gray-800/50 border border-gray-600">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      <div 
                        className="w-4 h-4 rounded"
                        style={{ backgroundColor: data.color }}
                      ></div>
                      <span className="text-white font-medium">{category}</span>
                    </div>
                    <span className={`text-lg font-bold ${getProfitColor(data.profit)}`}>
                      {formatCurrency(data.profit)}
                    </span>
                  </div>
                  
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-400">Total Trades:</span>
                      <span className="text-white">{data.trades}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Win Rate:</span>
                      <span className="text-green-400">{formatPercentage(data.win_rate)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Avg Per Trade:</span>
                      <span className="text-blue-400">
                        {formatCurrency(data.trades > 0 ? data.profit / data.trades : 0)}
                      </span>
                    </div>
                  </div>

                  {/* Performance Bar */}
                  <div className="mt-3">
                    <div className="w-full bg-gray-700 rounded-full h-2">
                      <div 
                        className="h-2 rounded-full"
                        style={{ 
                          backgroundColor: data.color,
                          width: `${Math.min(100, (data.win_rate / 100) * 100)}%`
                        }}
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Category Comparison */}
          <div>
            <h4 className="text-lg font-semibold text-white mb-3">🏆 Category Rankings</h4>
            <div className="bg-gray-800/30 rounded-lg p-4">
              <div className="space-y-2">
                {Object.entries(profitData.categories)
                  .sort(([,a], [,b]) => b.profit - a.profit)
                  .map(([category, data], index) => (
                    <div key={category} className="flex items-center justify-between p-2 rounded bg-gray-700/50">
                      <div className="flex items-center space-x-3">
                        <span className="text-gray-400">#{index + 1}</span>
                        <div 
                          className="w-3 h-3 rounded"
                          style={{ backgroundColor: data.color }}
                        ></div>
                        <span className="text-white font-medium">{category}</span>
                      </div>
                      <div className="text-right">
                        <div className="text-green-400 font-bold">
                          {formatCurrency(data.profit)}
                        </div>
                        <div className="text-xs text-gray-400">
                          {formatPercentage(data.win_rate)} win rate
                        </div>
                      </div>
                    </div>
                  ))}
              </div>
            </div>
          </div>
        </>
      )}

      {/* Summary Footer */}
      <div className="mt-6 p-4 rounded-lg bg-gradient-to-r from-emerald-900/30 to-green-900/30 border border-emerald-500/30">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
          <div>
            <div className="text-xl font-bold text-emerald-400">
              {profitData.ytd.trades}
            </div>
            <div className="text-sm text-gray-400">Total Trades</div>
          </div>
          <div>
            <div className="text-xl font-bold text-green-400">
              {formatCurrency(profitData.ytd.profit)}
            </div>
            <div className="text-sm text-gray-400">YTD Profit</div>
          </div>
          <div>
            <div className="text-xl font-bold text-blue-400">
              {formatPercentage(profitData.ytd.win_rate)}
            </div>
            <div className="text-sm text-gray-400">Win Rate</div>
          </div>
          <div>
            <div className="text-xl font-bold text-purple-400">
              {formatCurrency(profitData.ytd.trades > 0 ? profitData.ytd.profit / profitData.ytd.trades : 0)}
            </div>
            <div className="text-sm text-gray-400">Avg Per Trade</div>
          </div>
        </div>
      </div>
    </motion.div>
  );
}
