@echo off
REM NovaConnect UAC Google Cloud Marketplace Validation Script
REM This script validates the NovaConnect UAC configuration for Google Cloud Marketplace

REM Set variables
set PROJECT_ID=novafuse-marketplace
set REGION=us-central1
set ZONE=us-central1-a
set CLUSTER_NAME=nova-cluster
set NAMESPACE=nova-marketplace
set RELEASE_NAME=novafuse-uac

REM Colors for output
set RED=[91m
set GREEN=[92m
set YELLOW=[93m
set NC=[0m

REM Function to print section header
echo %YELLOW%==== Checking required commands ====%NC%

REM Check required commands
where gcloud >nul 2>&1
if %ERRORLEVEL% neq 0 (
  echo %RED%Error: gcloud is required but not installed.%NC%
  exit /b 1
)

where kubectl >nul 2>&1
if %ERRORLEVEL% neq 0 (
  echo %RED%Error: kubectl is required but not installed.%NC%
  exit /b 1
)

where docker >nul 2>&1
if %ERRORLEVEL% neq 0 (
  echo %RED%Error: docker is required but not installed.%NC%
  exit /b 1
)

where helm >nul 2>&1
if %ERRORLEVEL% neq 0 (
  echo %RED%Error: helm is required but not installed.%NC%
  exit /b 1
)

echo %YELLOW%==== Checking required files ====%NC%

REM Check required files
if not exist marketplace\schema.yaml (
  echo %RED%Error: File marketplace\schema.yaml not found.%NC%
  exit /b 1
) else (
  echo %GREEN%✓ File marketplace\schema.yaml exists.%NC%
)

if not exist k8s\marketplace\deployment.yaml (
  echo %RED%Error: File k8s\marketplace\deployment.yaml not found.%NC%
  exit /b 1
) else (
  echo %GREEN%✓ File k8s\marketplace\deployment.yaml exists.%NC%
)

if not exist k8s\marketplace\service.yaml (
  echo %RED%Error: File k8s\marketplace\service.yaml not found.%NC%
  exit /b 1
) else (
  echo %GREEN%✓ File k8s\marketplace\service.yaml exists.%NC%
)

if not exist k8s\marketplace\configmap.yaml (
  echo %RED%Error: File k8s\marketplace\configmap.yaml not found.%NC%
  exit /b 1
) else (
  echo %GREEN%✓ File k8s\marketplace\configmap.yaml exists.%NC%
)

if not exist k8s\marketplace\secret.yaml (
  echo %RED%Error: File k8s\marketplace\secret.yaml not found.%NC%
  exit /b 1
) else (
  echo %GREEN%✓ File k8s\marketplace\secret.yaml exists.%NC%
)

if not exist k8s\marketplace\autoscaling.yaml (
  echo %RED%Error: File k8s\marketplace\autoscaling.yaml not found.%NC%
  exit /b 1
) else (
  echo %GREEN%✓ File k8s\marketplace\autoscaling.yaml exists.%NC%
)

if not exist Dockerfile (
  echo %RED%Error: File Dockerfile not found.%NC%
  exit /b 1
) else (
  echo %GREEN%✓ File Dockerfile exists.%NC%
)

echo %YELLOW%==== Checking required directories ====%NC%

REM Check required directories
if not exist marketplace\chart (
  echo %RED%Error: Directory marketplace\chart not found.%NC%
  exit /b 1
) else (
  echo %GREEN%✓ Directory marketplace\chart exists.%NC%
)

if not exist monitoring\dashboards (
  echo %RED%Error: Directory monitoring\dashboards not found.%NC%
  exit /b 1
) else (
  echo %GREEN%✓ Directory monitoring\dashboards exists.%NC%
)

echo %YELLOW%==== Validating schema.yaml ====%NC%

REM Validate schema.yaml
findstr /C:"schemaVersion: v2" marketplace\schema.yaml >nul
if %ERRORLEVEL% neq 0 (
  echo %RED%Error: schema.yaml must have schemaVersion: v2%NC%
  exit /b 1
) else (
  echo %GREEN%✓ schema.yaml has correct schemaVersion.%NC%
)

findstr /C:"applicationApiVersion: v1beta1" marketplace\schema.yaml >nul
if %ERRORLEVEL% neq 0 (
  echo %RED%Error: schema.yaml must have applicationApiVersion: v1beta1%NC%
  exit /b 1
) else (
  echo %GREEN%✓ schema.yaml has correct applicationApiVersion.%NC%
)

findstr /C:"publishedVersion:" marketplace\schema.yaml >nul
if %ERRORLEVEL% neq 0 (
  echo %RED%Error: schema.yaml must have publishedVersion%NC%
  exit /b 1
) else (
  echo %GREEN%✓ schema.yaml has publishedVersion.%NC%
)

findstr /C:"billing:" marketplace\schema.yaml >nul
if %ERRORLEVEL% neq 0 (
  echo %YELLOW%Warning: schema.yaml does not have billing configuration.%NC%
) else (
  echo %GREEN%✓ schema.yaml has billing configuration.%NC%
)

echo %YELLOW%==== Validating Dockerfile ====%NC%

REM Validate Dockerfile
findstr /C:"FROM.*distroless" Dockerfile >nul
if %ERRORLEVEL% neq 0 (
  echo %YELLOW%Warning: Dockerfile does not use distroless base image.%NC%
) else (
  echo %GREEN%✓ Dockerfile uses distroless base image.%NC%
)

findstr /C:"HEALTHCHECK" Dockerfile >nul
if %ERRORLEVEL% neq 0 (
  echo %YELLOW%Warning: Dockerfile does not have HEALTHCHECK instruction.%NC%
) else (
  echo %GREEN%✓ Dockerfile has HEALTHCHECK instruction.%NC%
)

echo %YELLOW%==== Validating Kubernetes manifests ====%NC%

REM Validate Kubernetes manifests
findstr /C:"apiVersion: apps/v1" k8s\marketplace\deployment.yaml >nul
if %ERRORLEVEL% neq 0 (
  echo %RED%Error: deployment.yaml must have apiVersion: apps/v1%NC%
  exit /b 1
) else (
  echo %GREEN%✓ deployment.yaml has correct apiVersion.%NC%
)

findstr /C:"kind: Service" k8s\marketplace\service.yaml >nul
if %ERRORLEVEL% neq 0 (
  echo %RED%Error: service.yaml must have kind: Service%NC%
  exit /b 1
) else (
  echo %GREEN%✓ service.yaml has correct kind.%NC%
)

findstr /C:"kind: ConfigMap" k8s\marketplace\configmap.yaml >nul
if %ERRORLEVEL% neq 0 (
  echo %RED%Error: configmap.yaml must have kind: ConfigMap%NC%
  exit /b 1
) else (
  echo %GREEN%✓ configmap.yaml has correct kind.%NC%
)

findstr /C:"kind: Secret" k8s\marketplace\secret.yaml >nul
if %ERRORLEVEL% neq 0 (
  echo %RED%Error: secret.yaml must have kind: Secret%NC%
  exit /b 1
) else (
  echo %GREEN%✓ secret.yaml has correct kind.%NC%
)

findstr /C:"kind: HorizontalPodAutoscaler" k8s\marketplace\autoscaling.yaml >nul
if %ERRORLEVEL% neq 0 (
  echo %RED%Error: autoscaling.yaml must have kind: HorizontalPodAutoscaler%NC%
  exit /b 1
) else (
  echo %GREEN%✓ autoscaling.yaml has correct kind.%NC%
)

echo %YELLOW%==== Validating Helm chart ====%NC%

REM Validate Helm chart
if not exist marketplace\chart\Chart.yaml (
  echo %RED%Error: Chart.yaml not found.%NC%
  exit /b 1
)

findstr /C:"apiVersion:" marketplace\chart\Chart.yaml >nul
if %ERRORLEVEL% neq 0 (
  echo %RED%Error: Chart.yaml must have apiVersion%NC%
  exit /b 1
) else (
  echo %GREEN%✓ Chart.yaml has apiVersion.%NC%
)

echo %YELLOW%==== Validating monitoring dashboards ====%NC%

REM Validate monitoring dashboards
if not exist monitoring\dashboards\marketplace-dashboard.json (
  echo %YELLOW%Warning: marketplace-dashboard.json not found.%NC%
) else (
  echo %GREEN%✓ marketplace-dashboard.json exists.%NC%
)

if not exist monitoring\dashboards\compliance-dashboard.json (
  echo %YELLOW%Warning: compliance-dashboard.json not found.%NC%
) else (
  echo %GREEN%✓ compliance-dashboard.json exists.%NC%
)

echo %YELLOW%==== Checking Docker image build ====%NC%

REM Check Docker image build
echo %YELLOW%Building Docker image to validate Dockerfile...%NC%
docker build -t novafuse-uac:test . >nul 2>&1
if %ERRORLEVEL% neq 0 (
  echo %RED%Error: Docker image build failed.%NC%
  exit /b 1
) else (
  echo %GREEN%✓ Docker image build successful.%NC%
  docker rmi novafuse-uac:test >nul 2>&1
)

echo %YELLOW%==== Validation Summary ====%NC%

REM Summary
echo %GREEN%✓ NovaConnect UAC is ready for Google Cloud Marketplace submission.%NC%
echo %YELLOW%Next steps:%NC%
echo 1. Create a Google Cloud project for the marketplace listing
echo 2. Build and push the Docker image to Google Container Registry
echo 3. Create a deployer image for the marketplace
echo 4. Test the deployment in a GKE cluster
echo 5. Submit the application to Google Cloud Marketplace

exit /b 0
