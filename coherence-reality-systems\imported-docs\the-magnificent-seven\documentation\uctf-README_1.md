# Universal Control Testing Framework (UCTF)

The Universal Control Testing Framework (UCTF) is a powerful framework for defining, executing, and reporting on compliance tests. It provides a flexible and extensible architecture for testing compliance with various regulatory frameworks such as GDPR, HIPAA, and SOC 2.

## Overview

UCTF enables organizations to automate compliance testing, maintain a consistent testing approach across different frameworks, and generate comprehensive reports for stakeholders and auditors.

## Key Features

- **Test Definition**: Define compliance tests with a flexible, JSON-based format
- **Test Execution**: Execute tests individually or as part of test suites
- **Result Management**: Store and analyze test results
- **Report Generation**: Generate various types of reports from test results
- **Extensibility**: Easily add custom tests, report generators, and event handlers
- **Event-Driven Architecture**: Emit events for integration with other systems
- **Framework Agnostic**: Support for multiple compliance frameworks

## Architecture

The UCTF consists of several core components:

- **Test Engine**: The main engine that orchestrates test execution and report generation
- **Test Manager**: Manages test definitions and executes tests
- **Result Manager**: Manages test results and provides analysis capabilities
- **Report Manager**: Generates reports from test results

## Supported Compliance Frameworks

The UCTF includes support for testing compliance with several common frameworks:

- **GDPR**: General Data Protection Regulation
- **HIPAA**: Health Insurance Portability and Accountability Act
- **SOC 2**: Service Organization Control 2

## Installation

```bash
# Clone the repository
git clone https://github.com/novafuse/uctf.git
cd uctf

# Install the package
pip install -e .
```

## Usage

Here's a simple example of how to use the UCTF:

```python
from uctf import TestEngine

# Initialize the Test Engine
engine = TestEngine()

# Run a GDPR data protection test
test_run = engine.run_test('gdpr_data_protection', {
    'strict_mode': True,
    'target_system': 'customer_database'
})

# Print the test result
print(f"Test passed: {test_run['result']['passed']}")
print(f"Test score: {test_run['result']['score']}")

# Generate a summary report
report = engine.generate_report('summary', test_run['id'], {})

# Print the report
print(f"Report ID: {report['id']}")
print(f"Compliance status: {report['compliance_status']}")
```

## Extending the Framework

### Adding a Custom Test

```python
from uctf import TestEngine

# Initialize the Test Engine
engine = TestEngine()

# Define a custom test function
def custom_test(parameters):
    # Custom implementation to test compliance
    # ...
    return {
        'test_id': 'custom_test',
        'passed': True,
        'score': 90,
        'findings': [
            {
                'id': 'CUSTOM-1',
                'description': 'Custom test finding',
                'status': 'passed'
            }
        ]
    }

# Register the custom test
engine.test_manager.register_test('custom_test', custom_test)

# Run the custom test
test_run = engine.run_test('custom_test', {})
```

### Adding a Custom Report Generator

```python
from uctf import TestEngine

# Initialize the Test Engine
engine = TestEngine()

# Define a custom report generator function
def custom_report_generator(test_run, parameters):
    # Custom implementation to generate a report
    # ...
    return {
        'id': 'custom_report',
        'test_run_id': test_run['id'],
        'compliance_status': 'compliant' if test_run['result']['passed'] else 'non_compliant',
        'summary': 'Custom report summary',
        'details': 'Custom report details'
    }

# Register the custom report generator
engine.report_manager.register_report_generator('custom_report', custom_report_generator)

# Generate a custom report
report = engine.generate_report('custom_report', test_run['id'], {})
```

## Integration with Other Systems

UCTF can be integrated with other systems through its event-driven architecture. You can register event handlers for various events, such as test completion, and use these events to trigger actions in other systems.

```python
from uctf import TestEngine

# Initialize the Test Engine
engine = TestEngine()

# Define a custom event handler
def custom_event_handler(event_data):
    # Custom implementation to handle the event
    print(f"Test completed: {event_data['id']}")
    print(f"Test result: {event_data['result']['passed']}")

# Register the custom event handler
engine.register_event_handler('test_completed', custom_event_handler)

# Run a test (the event handler will be called when the test completes)
test_run = engine.run_test('gdpr_data_protection', {})
```

## License

Copyright © 2023-2025 NovaFuse. All rights reserved.

## Contributing

Please read [CONTRIBUTING.md](CONTRIBUTING.md) for details on our code of conduct and the process for submitting pull requests.