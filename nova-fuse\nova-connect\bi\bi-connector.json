{"extends": "base-connector", "name": "novafuse-bi-connector", "version": "1.0.0", "description": "Business Intelligence & Workflow connector template for NovaFuse API Superstore", "category": "bi", "base_url": "http://localhost:8000/bi", "endpoints": [{"name": "dashboards", "path": "/dashboards", "method": "GET", "description": "Get a list of dashboards", "parameters": [{"name": "category", "in": "query", "required": false, "description": "Filter by dashboard category"}, {"name": "status", "in": "query", "required": false, "description": "Filter by status (active, archived, draft)"}]}, {"name": "dashboard_details", "path": "/dashboards/{id}", "method": "GET", "description": "Get details of a specific dashboard", "parameters": [{"name": "id", "in": "path", "required": true, "description": "Dashboard ID"}]}, {"name": "reports", "path": "/reports", "method": "GET", "description": "Get a list of reports", "parameters": [{"name": "category", "in": "query", "required": false, "description": "Filter by report category"}, {"name": "status", "in": "query", "required": false, "description": "Filter by status (active, archived, draft)"}]}, {"name": "report_details", "path": "/reports/{id}", "method": "GET", "description": "Get details of a specific report", "parameters": [{"name": "id", "in": "path", "required": true, "description": "Report ID"}]}, {"name": "workflows", "path": "/workflows", "method": "GET", "description": "Get a list of workflows", "parameters": [{"name": "category", "in": "query", "required": false, "description": "Filter by workflow category"}, {"name": "status", "in": "query", "required": false, "description": "Filter by status (active, inactive, draft)"}]}, {"name": "workflow_details", "path": "/workflows/{id}", "method": "GET", "description": "Get details of a specific workflow", "parameters": [{"name": "id", "in": "path", "required": true, "description": "Workflow ID"}]}, {"name": "data_sources", "path": "/data-sources", "method": "GET", "description": "Get a list of data sources", "parameters": [{"name": "type", "in": "query", "required": false, "description": "Filter by data source type"}, {"name": "status", "in": "query", "required": false, "description": "Filter by status (active, inactive)"}]}, {"name": "data_source_details", "path": "/data-sources/{id}", "method": "GET", "description": "Get details of a specific data source", "parameters": [{"name": "id", "in": "path", "required": true, "description": "Data source ID"}]}, {"name": "workflow_execution", "path": "/workflows/{id}/execute", "method": "POST", "description": "Execute a workflow", "parameters": [{"name": "id", "in": "path", "required": true, "description": "Workflow ID"}]}]}