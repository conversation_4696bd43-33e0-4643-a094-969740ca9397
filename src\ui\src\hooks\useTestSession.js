/**
 * useTestSession Hook
 * 
 * This hook provides functionality for managing test sessions.
 */

import { useState, useEffect, useCallback } from 'react';
import testSessionRecordingService from '../services/TestSessionRecordingService';

/**
 * useTestSession Hook
 * @param {Object} options - Hook options
 * @returns {Object} - Test session methods and state
 */
const useTestSession = (options = {}) => {
  // State for session
  const [session, setSession] = useState(null);
  
  // State for loading
  const [loading, setLoading] = useState(false);
  
  // State for error
  const [error, setError] = useState(null);
  
  // Check for existing session on mount
  useEffect(() => {
    const checkExistingSession = async () => {
      const sessionId = testSessionRecordingService.getCurrentSessionId();
      
      if (sessionId) {
        try {
          setLoading(true);
          const sessionData = await testSessionRecordingService.getSession(sessionId);
          setSession(sessionData);
        } catch (error) {
          console.error('Error retrieving existing session:', error);
          // Clear invalid session ID
          localStorage.removeItem('currentTestSessionId');
        } finally {
          setLoading(false);
        }
      }
    };
    
    checkExistingSession();
  }, []);
  
  /**
   * Start a new test session
   * @param {Object} sessionData - Test session data
   * @returns {Promise} - Promise that resolves to the created session
   */
  const startSession = useCallback(async (sessionData) => {
    try {
      setLoading(true);
      setError(null);
      
      const newSession = await testSessionRecordingService.startSession(sessionData);
      setSession(newSession);
      
      return newSession;
    } catch (error) {
      console.error('Error starting test session:', error);
      setError(error.message || 'Failed to start test session');
      throw error;
    } finally {
      setLoading(false);
    }
  }, []);
  
  /**
   * End the current test session
   * @param {Object} sessionData - Test session data
   * @returns {Promise} - Promise that resolves to the updated session
   */
  const endSession = useCallback(async (sessionData) => {
    if (!session) {
      throw new Error('No active test session');
    }
    
    try {
      setLoading(true);
      setError(null);
      
      const updatedSession = await testSessionRecordingService.endSession(session.id, sessionData);
      setSession(null);
      
      return updatedSession;
    } catch (error) {
      console.error('Error ending test session:', error);
      setError(error.message || 'Failed to end test session');
      throw error;
    } finally {
      setLoading(false);
    }
  }, [session]);
  
  /**
   * Record a user interaction
   * @param {Object} interactionData - Interaction data
   * @returns {Promise} - Promise that resolves to the created interaction
   */
  const recordInteraction = useCallback(async (interactionData) => {
    if (!session) {
      // Store interaction locally if no active session
      const sessionId = testSessionRecordingService.getCurrentSessionId();
      if (sessionId) {
        testSessionRecordingService.storeInteractionLocally(sessionId, interactionData);
        return { stored: true };
      }
      
      throw new Error('No active test session');
    }
    
    try {
      return await testSessionRecordingService.recordInteraction(session.id, interactionData);
    } catch (error) {
      console.error('Error recording interaction:', error);
      // Interaction is already stored locally by the service
      return { stored: true, error: error.message };
    }
  }, [session]);
  
  /**
   * Record task completion
   * @param {string} taskId - Task ID
   * @param {Object} taskData - Task completion data
   * @returns {Promise} - Promise that resolves to the updated task
   */
  const recordTaskCompletion = useCallback(async (taskId, taskData) => {
    if (!session) {
      // Store task completion locally if no active session
      const sessionId = testSessionRecordingService.getCurrentSessionId();
      if (sessionId) {
        testSessionRecordingService.storeTaskCompletionLocally(sessionId, taskId, taskData);
        return { stored: true };
      }
      
      throw new Error('No active test session');
    }
    
    try {
      setLoading(true);
      setError(null);
      
      const result = await testSessionRecordingService.recordTaskCompletion(session.id, taskId, taskData);
      
      // Update session with completed task
      setSession(prevSession => ({
        ...prevSession,
        tasks: prevSession.tasks.map(task => 
          task.id === taskId ? { ...task, ...result } : task
        )
      }));
      
      return result;
    } catch (error) {
      console.error('Error recording task completion:', error);
      setError(error.message || 'Failed to record task completion');
      // Task completion is already stored locally by the service
      return { stored: true, error: error.message };
    } finally {
      setLoading(false);
    }
  }, [session]);
  
  /**
   * Record user feedback
   * @param {Object} feedbackData - Feedback data
   * @returns {Promise} - Promise that resolves to the created feedback
   */
  const recordFeedback = useCallback(async (feedbackData) => {
    if (!session) {
      // Store feedback locally if no active session
      const sessionId = testSessionRecordingService.getCurrentSessionId();
      if (sessionId) {
        testSessionRecordingService.storeFeedbackLocally(sessionId, feedbackData);
        return { stored: true };
      }
      
      throw new Error('No active test session');
    }
    
    try {
      return await testSessionRecordingService.recordFeedback(session.id, feedbackData);
    } catch (error) {
      console.error('Error recording feedback:', error);
      // Feedback is already stored locally by the service
      return { stored: true, error: error.message };
    }
  }, [session]);
  
  /**
   * Record final feedback
   * @param {Object} feedbackData - Final feedback data
   * @returns {Promise} - Promise that resolves to the updated session
   */
  const recordFinalFeedback = useCallback(async (feedbackData) => {
    if (!session) {
      // Store final feedback locally if no active session
      const sessionId = testSessionRecordingService.getCurrentSessionId();
      if (sessionId) {
        testSessionRecordingService.storeFinalFeedbackLocally(sessionId, feedbackData);
        return { stored: true };
      }
      
      throw new Error('No active test session');
    }
    
    try {
      setLoading(true);
      setError(null);
      
      const updatedSession = await testSessionRecordingService.recordFinalFeedback(session.id, feedbackData);
      setSession(updatedSession);
      
      return updatedSession;
    } catch (error) {
      console.error('Error recording final feedback:', error);
      setError(error.message || 'Failed to record final feedback');
      // Final feedback is already stored locally by the service
      return { stored: true, error: error.message };
    } finally {
      setLoading(false);
    }
  }, [session]);
  
  /**
   * Submit all locally stored data
   * @returns {Promise} - Promise that resolves when all data is submitted
   */
  const submitLocalData = useCallback(async () => {
    const sessionId = testSessionRecordingService.getCurrentSessionId();
    
    if (!sessionId) {
      throw new Error('No test session ID found');
    }
    
    try {
      setLoading(true);
      setError(null);
      
      await testSessionRecordingService.submitLocalData(sessionId);
      
      // Refresh session data
      const sessionData = await testSessionRecordingService.getSession(sessionId);
      setSession(sessionData);
      
      return true;
    } catch (error) {
      console.error('Error submitting local data:', error);
      setError(error.message || 'Failed to submit local data');
      throw error;
    } finally {
      setLoading(false);
    }
  }, []);
  
  return {
    session,
    loading,
    error,
    startSession,
    endSession,
    recordInteraction,
    recordTaskCompletion,
    recordFeedback,
    recordFinalFeedback,
    submitLocalData
  };
};

export default useTestSession;
