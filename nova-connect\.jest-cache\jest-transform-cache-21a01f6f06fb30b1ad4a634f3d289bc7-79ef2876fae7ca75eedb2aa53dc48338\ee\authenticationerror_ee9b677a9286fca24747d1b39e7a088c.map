{"version": 3, "names": ["UAConnectorError", "require", "AuthenticationError", "constructor", "message", "options", "code", "severity", "context", "cause", "getUserMessage", "MissingCredentialsError", "InvalidCredentialsError", "ExpiredCredentialsError", "InsufficientPermissionsError", "module", "exports"], "sources": ["authentication-error.js"], "sourcesContent": ["/**\n * NovaFuse Universal API Connector - Authentication Error\n * \n * This module defines authentication-related errors for the UAC.\n */\n\nconst UAConnectorError = require('./base-error');\n\n/**\n * Error class for authentication failures\n * @class AuthenticationError\n * @extends UAConnectorError\n */\nclass AuthenticationError extends UAConnectorError {\n  /**\n   * Create a new AuthenticationError\n   * \n   * @param {string} message - Error message\n   * @param {Object} options - Error options\n   * @param {string} options.code - Error code\n   * @param {string} options.severity - Error severity\n   * @param {Object} options.context - Additional context for the error\n   * @param {Error} options.cause - The error that caused this error\n   */\n  constructor(message, options = {}) {\n    super(message, {\n      code: options.code || 'AUTH_ERROR',\n      severity: options.severity || 'error',\n      context: options.context || {},\n      cause: options.cause\n    });\n  }\n\n  /**\n   * Get a user-friendly error message\n   * \n   * @returns {string} - User-friendly error message\n   */\n  getUserMessage() {\n    return 'Authentication failed. Please check your credentials and try again.';\n  }\n}\n\n/**\n * Error class for missing credentials\n * @class MissingCredentialsError\n * @extends AuthenticationError\n */\nclass MissingCredentialsError extends AuthenticationError {\n  /**\n   * Create a new MissingCredentialsError\n   * \n   * @param {string} message - Error message\n   * @param {Object} options - Error options\n   */\n  constructor(message = 'Required credentials are missing', options = {}) {\n    super(message, {\n      code: options.code || 'AUTH_MISSING_CREDENTIALS',\n      severity: options.severity || 'error',\n      context: options.context || {},\n      cause: options.cause\n    });\n  }\n\n  /**\n   * Get a user-friendly error message\n   * \n   * @returns {string} - User-friendly error message\n   */\n  getUserMessage() {\n    return 'Authentication failed. Required credentials are missing.';\n  }\n}\n\n/**\n * Error class for invalid credentials\n * @class InvalidCredentialsError\n * @extends AuthenticationError\n */\nclass InvalidCredentialsError extends AuthenticationError {\n  /**\n   * Create a new InvalidCredentialsError\n   * \n   * @param {string} message - Error message\n   * @param {Object} options - Error options\n   */\n  constructor(message = 'The provided credentials are invalid', options = {}) {\n    super(message, {\n      code: options.code || 'AUTH_INVALID_CREDENTIALS',\n      severity: options.severity || 'error',\n      context: options.context || {},\n      cause: options.cause\n    });\n  }\n\n  /**\n   * Get a user-friendly error message\n   * \n   * @returns {string} - User-friendly error message\n   */\n  getUserMessage() {\n    return 'Authentication failed. The provided credentials are invalid.';\n  }\n}\n\n/**\n * Error class for expired credentials\n * @class ExpiredCredentialsError\n * @extends AuthenticationError\n */\nclass ExpiredCredentialsError extends AuthenticationError {\n  /**\n   * Create a new ExpiredCredentialsError\n   * \n   * @param {string} message - Error message\n   * @param {Object} options - Error options\n   */\n  constructor(message = 'The credentials have expired', options = {}) {\n    super(message, {\n      code: options.code || 'AUTH_EXPIRED_CREDENTIALS',\n      severity: options.severity || 'error',\n      context: options.context || {},\n      cause: options.cause\n    });\n  }\n\n  /**\n   * Get a user-friendly error message\n   * \n   * @returns {string} - User-friendly error message\n   */\n  getUserMessage() {\n    return 'Authentication failed. Your credentials have expired. Please refresh your credentials and try again.';\n  }\n}\n\n/**\n * Error class for insufficient permissions\n * @class InsufficientPermissionsError\n * @extends AuthenticationError\n */\nclass InsufficientPermissionsError extends AuthenticationError {\n  /**\n   * Create a new InsufficientPermissionsError\n   * \n   * @param {string} message - Error message\n   * @param {Object} options - Error options\n   */\n  constructor(message = 'Insufficient permissions to perform this operation', options = {}) {\n    super(message, {\n      code: options.code || 'AUTH_INSUFFICIENT_PERMISSIONS',\n      severity: options.severity || 'error',\n      context: options.context || {},\n      cause: options.cause\n    });\n  }\n\n  /**\n   * Get a user-friendly error message\n   * \n   * @returns {string} - User-friendly error message\n   */\n  getUserMessage() {\n    return 'Authentication failed. You do not have sufficient permissions to perform this operation.';\n  }\n}\n\nmodule.exports = {\n  AuthenticationError,\n  MissingCredentialsError,\n  InvalidCredentialsError,\n  ExpiredCredentialsError,\n  InsufficientPermissionsError\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,MAAMA,gBAAgB,GAAGC,OAAO,CAAC,cAAc,CAAC;;AAEhD;AACA;AACA;AACA;AACA;AACA,MAAMC,mBAAmB,SAASF,gBAAgB,CAAC;EACjD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEG,WAAWA,CAACC,OAAO,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IACjC,KAAK,CAACD,OAAO,EAAE;MACbE,IAAI,EAAED,OAAO,CAACC,IAAI,IAAI,YAAY;MAClCC,QAAQ,EAAEF,OAAO,CAACE,QAAQ,IAAI,OAAO;MACrCC,OAAO,EAAEH,OAAO,CAACG,OAAO,IAAI,CAAC,CAAC;MAC9BC,KAAK,EAAEJ,OAAO,CAACI;IACjB,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;AACA;AACA;EACEC,cAAcA,CAAA,EAAG;IACf,OAAO,qEAAqE;EAC9E;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMC,uBAAuB,SAAST,mBAAmB,CAAC;EACxD;AACF;AACA;AACA;AACA;AACA;EACEC,WAAWA,CAACC,OAAO,GAAG,kCAAkC,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IACtE,KAAK,CAACD,OAAO,EAAE;MACbE,IAAI,EAAED,OAAO,CAACC,IAAI,IAAI,0BAA0B;MAChDC,QAAQ,EAAEF,OAAO,CAACE,QAAQ,IAAI,OAAO;MACrCC,OAAO,EAAEH,OAAO,CAACG,OAAO,IAAI,CAAC,CAAC;MAC9BC,KAAK,EAAEJ,OAAO,CAACI;IACjB,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;AACA;AACA;EACEC,cAAcA,CAAA,EAAG;IACf,OAAO,0DAA0D;EACnE;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAME,uBAAuB,SAASV,mBAAmB,CAAC;EACxD;AACF;AACA;AACA;AACA;AACA;EACEC,WAAWA,CAACC,OAAO,GAAG,sCAAsC,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IAC1E,KAAK,CAACD,OAAO,EAAE;MACbE,IAAI,EAAED,OAAO,CAACC,IAAI,IAAI,0BAA0B;MAChDC,QAAQ,EAAEF,OAAO,CAACE,QAAQ,IAAI,OAAO;MACrCC,OAAO,EAAEH,OAAO,CAACG,OAAO,IAAI,CAAC,CAAC;MAC9BC,KAAK,EAAEJ,OAAO,CAACI;IACjB,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;AACA;AACA;EACEC,cAAcA,CAAA,EAAG;IACf,OAAO,8DAA8D;EACvE;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMG,uBAAuB,SAASX,mBAAmB,CAAC;EACxD;AACF;AACA;AACA;AACA;AACA;EACEC,WAAWA,CAACC,OAAO,GAAG,8BAA8B,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IAClE,KAAK,CAACD,OAAO,EAAE;MACbE,IAAI,EAAED,OAAO,CAACC,IAAI,IAAI,0BAA0B;MAChDC,QAAQ,EAAEF,OAAO,CAACE,QAAQ,IAAI,OAAO;MACrCC,OAAO,EAAEH,OAAO,CAACG,OAAO,IAAI,CAAC,CAAC;MAC9BC,KAAK,EAAEJ,OAAO,CAACI;IACjB,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;AACA;AACA;EACEC,cAAcA,CAAA,EAAG;IACf,OAAO,sGAAsG;EAC/G;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMI,4BAA4B,SAASZ,mBAAmB,CAAC;EAC7D;AACF;AACA;AACA;AACA;AACA;EACEC,WAAWA,CAACC,OAAO,GAAG,oDAAoD,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IACxF,KAAK,CAACD,OAAO,EAAE;MACbE,IAAI,EAAED,OAAO,CAACC,IAAI,IAAI,+BAA+B;MACrDC,QAAQ,EAAEF,OAAO,CAACE,QAAQ,IAAI,OAAO;MACrCC,OAAO,EAAEH,OAAO,CAACG,OAAO,IAAI,CAAC,CAAC;MAC9BC,KAAK,EAAEJ,OAAO,CAACI;IACjB,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;AACA;AACA;EACEC,cAAcA,CAAA,EAAG;IACf,OAAO,0FAA0F;EACnG;AACF;AAEAK,MAAM,CAACC,OAAO,GAAG;EACfd,mBAAmB;EACnBS,uBAAuB;EACvBC,uBAAuB;EACvBC,uBAAuB;EACvBC;AACF,CAAC", "ignoreList": []}