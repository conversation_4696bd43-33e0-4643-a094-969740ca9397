name: NovaConnect UAC CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Deployment environment'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production

env:
  PROJECT_ID: ${{ secrets.GCP_PROJECT_ID }}
  GKE_CLUSTER: novafuse-cluster
  GKE_ZONE: us-central1-a
  IMAGE: novafuse-uac
  REGISTRY_HOSTNAME: gcr.io

jobs:
  test:
    name: Test
    runs-on: ubuntu-latest
    
    services:
      mongodb:
        image: mongo:4.4
        ports:
          - 27017:27017
      
      redis:
        image: redis:6-alpine
        ports:
          - 6379:6379
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '16'
          cache: 'npm'
          cache-dependency-path: 'nova-connect/package-lock.json'
      
      - name: Install dependencies
        run: |
          cd nova-connect
          npm ci
      
      - name: Lint code
        run: |
          cd nova-connect
          npm run lint
      
      - name: Run unit tests
        run: |
          cd nova-connect
          npm run test:unit
      
      - name: Run integration tests
        run: |
          cd nova-connect
          npm run test:integration
      
      - name: Run security tests
        run: |
          cd nova-connect
          npm run test:security
      
      - name: Upload test results
        uses: actions/upload-artifact@v3
        with:
          name: test-results
          path: nova-connect/coverage/
  
  build:
    name: Build and Push
    needs: test
    runs-on: ubuntu-latest
    if: github.event_name == 'push' || github.event_name == 'workflow_dispatch'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Set up Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v1
        with:
          project_id: ${{ env.PROJECT_ID }}
          service_account_key: ${{ secrets.GCP_SA_KEY }}
          export_default_credentials: true
      
      - name: Configure Docker for GCR
        run: gcloud auth configure-docker ${{ env.REGISTRY_HOSTNAME }}
      
      - name: Get short SHA
        id: sha
        run: echo "::set-output name=short::$(git rev-parse --short HEAD)"
      
      - name: Build Docker image
        run: |
          cd nova-connect
          docker build -t ${{ env.REGISTRY_HOSTNAME }}/${{ env.PROJECT_ID }}/${{ env.IMAGE }}:${{ steps.sha.outputs.short }} .
          docker tag ${{ env.REGISTRY_HOSTNAME }}/${{ env.PROJECT_ID }}/${{ env.IMAGE }}:${{ steps.sha.outputs.short }} ${{ env.REGISTRY_HOSTNAME }}/${{ env.PROJECT_ID }}/${{ env.IMAGE }}:latest
      
      - name: Push Docker image
        run: |
          docker push ${{ env.REGISTRY_HOSTNAME }}/${{ env.PROJECT_ID }}/${{ env.IMAGE }}:${{ steps.sha.outputs.short }}
          docker push ${{ env.REGISTRY_HOSTNAME }}/${{ env.PROJECT_ID }}/${{ env.IMAGE }}:latest
      
      - name: Save image info
        run: |
          echo "${{ steps.sha.outputs.short }}" > image-tag.txt
      
      - name: Upload image info
        uses: actions/upload-artifact@v3
        with:
          name: image-info
          path: image-tag.txt
  
  deploy-staging:
    name: Deploy to Staging
    needs: build
    runs-on: ubuntu-latest
    if: (github.event_name == 'push' && github.ref == 'refs/heads/develop') || (github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'staging')
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Set up Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v1
        with:
          project_id: ${{ env.PROJECT_ID }}
          service_account_key: ${{ secrets.GCP_SA_KEY }}
          export_default_credentials: true
      
      - name: Get GKE credentials
        uses: google-github-actions/get-gke-credentials@v1
        with:
          cluster_name: ${{ env.GKE_CLUSTER }}
          location: ${{ env.GKE_ZONE }}
      
      - name: Download image info
        uses: actions/download-artifact@v3
        with:
          name: image-info
      
      - name: Set image tag
        id: image-tag
        run: echo "::set-output name=tag::$(cat image-tag.txt)"
      
      - name: Deploy to GKE
        run: |
          cd nova-connect/k8s
          
          # Update image tag in deployment files
          sed -i "s|IMAGE_TAG|${{ steps.image-tag.outputs.tag }}|g" staging/*.yaml
          
          # Apply Kubernetes manifests
          kubectl apply -f staging/namespace.yaml
          kubectl apply -f staging/configmap.yaml
          kubectl apply -f staging/secret.yaml
          kubectl apply -f staging/deployment.yaml
          kubectl apply -f staging/service.yaml
          kubectl apply -f staging/ingress.yaml
          
          # Wait for deployment to complete
          kubectl rollout status deployment/novafuse-uac -n novafuse-staging
      
      - name: Run post-deployment tests
        run: |
          cd nova-connect
          STAGING_URL=$(kubectl get ingress novafuse-uac-ingress -n novafuse-staging -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
          npm run test:e2e -- --url=https://$STAGING_URL
      
      - name: Notify Slack on success
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_CHANNEL: deployments
          SLACK_TITLE: "Staging Deployment Successful"
          SLACK_MESSAGE: "NovaConnect UAC has been deployed to staging environment"
          SLACK_COLOR: good
  
  deploy-production:
    name: Deploy to Production
    needs: build
    runs-on: ubuntu-latest
    if: (github.event_name == 'push' && github.ref == 'refs/heads/main') || (github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'production')
    environment:
      name: production
      url: https://api.novafuse.io
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Set up Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v1
        with:
          project_id: ${{ env.PROJECT_ID }}
          service_account_key: ${{ secrets.GCP_SA_KEY }}
          export_default_credentials: true
      
      - name: Get GKE credentials
        uses: google-github-actions/get-gke-credentials@v1
        with:
          cluster_name: ${{ env.GKE_CLUSTER }}
          location: ${{ env.GKE_ZONE }}
      
      - name: Download image info
        uses: actions/download-artifact@v3
        with:
          name: image-info
      
      - name: Set image tag
        id: image-tag
        run: echo "::set-output name=tag::$(cat image-tag.txt)"
      
      - name: Deploy to GKE
        run: |
          cd nova-connect/k8s
          
          # Update image tag in deployment files
          sed -i "s|IMAGE_TAG|${{ steps.image-tag.outputs.tag }}|g" production/*.yaml
          
          # Apply Kubernetes manifests
          kubectl apply -f production/namespace.yaml
          kubectl apply -f production/configmap.yaml
          kubectl apply -f production/secret.yaml
          kubectl apply -f production/deployment.yaml
          kubectl apply -f production/service.yaml
          kubectl apply -f production/ingress.yaml
          
          # Wait for deployment to complete
          kubectl rollout status deployment/novafuse-uac -n novafuse-production
      
      - name: Run post-deployment tests
        run: |
          cd nova-connect
          PRODUCTION_URL=$(kubectl get ingress novafuse-uac-ingress -n novafuse-production -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
          npm run test:e2e -- --url=https://$PRODUCTION_URL
      
      - name: Notify Slack on success
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_CHANNEL: deployments
          SLACK_TITLE: "Production Deployment Successful"
          SLACK_MESSAGE: "NovaConnect UAC has been deployed to production environment"
          SLACK_COLOR: good
  
  marketplace-publish:
    name: Publish to Google Cloud Marketplace
    needs: deploy-production
    runs-on: ubuntu-latest
    if: github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'production'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Set up Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v1
        with:
          project_id: ${{ env.PROJECT_ID }}
          service_account_key: ${{ secrets.GCP_SA_KEY }}
          export_default_credentials: true
      
      - name: Download image info
        uses: actions/download-artifact@v3
        with:
          name: image-info
      
      - name: Set image tag
        id: image-tag
        run: echo "::set-output name=tag::$(cat image-tag.txt)"
      
      - name: Prepare marketplace package
        run: |
          cd nova-connect/marketplace
          
          # Update image tag in marketplace configuration
          sed -i "s|IMAGE_TAG|${{ steps.image-tag.outputs.tag }}|g" schema.yaml
          
          # Package for marketplace
          mpdev pkg package .
      
      - name: Validate marketplace package
        run: |
          cd nova-connect/marketplace
          mpdev pkg verify
      
      - name: Upload to Google Cloud Storage
        run: |
          cd nova-connect/marketplace
          PACKAGE_NAME=$(ls *.tar.gz)
          gsutil cp $PACKAGE_NAME gs://${{ secrets.GCP_MARKETPLACE_BUCKET }}/
      
      - name: Submit to Partner Portal
        run: |
          cd nova-connect/scripts
          ./submit-to-partner-portal.sh ${{ secrets.PARTNER_PORTAL_API_KEY }}
      
      - name: Notify Slack on success
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_CHANNEL: deployments
          SLACK_TITLE: "Marketplace Submission Successful"
          SLACK_MESSAGE: "NovaConnect UAC has been submitted to Google Cloud Marketplace"
          SLACK_COLOR: good
