/**
 * Calculate Test Coverage for NovaDNA
 *
 * This script calculates test coverage by counting the lines of code
 * and estimating the lines covered by tests.
 */

const fs = require('fs');
const path = require('path');

// Directories to analyze
const directories = [
  '../api',
  '../ui',
  '../integration',
  '../core'
];

// Files to exclude
const excludeFiles = [
  '.git',
  'node_modules',
  'tests',
  'docs',
  '.md',
  '.json',
  '.log'
];

// Count lines in a file
function countLines(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    return content.split('\n').length;
  } catch (error) {
    console.error(`Error reading file ${filePath}:`, error.message);
    return 0;
  }
}

// Check if a file should be excluded
function shouldExclude(filePath) {
  return excludeFiles.some(exclude => {
    if (exclude.startsWith('.')) {
      return filePath.endsWith(exclude);
    }
    return filePath.includes(exclude);
  });
}

// Get all JavaScript files in a directory recursively
function getJsFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);

  files.forEach(file => {
    const filePath = path.join(dir, file);

    if (shouldExclude(filePath)) {
      return;
    }

    if (fs.statSync(filePath).isDirectory()) {
      fileList = getJsFiles(filePath, fileList);
    } else if (filePath.endsWith('.js')) {
      fileList.push(filePath);
    }
  });

  return fileList;
}

// Count lines in test files
function countTestLines() {
  const testDirs = [
    '.',
    './integration',
    './e2e'
  ];

  let totalTestLines = 0;
  let testFiles = [];

  testDirs.forEach(dir => {
    try {
      const dirPath = path.resolve(__dirname, dir);

      if (!fs.existsSync(dirPath)) {
        console.log(`Test directory ${dir} does not exist, skipping...`);
        return;
      }

      const files = fs.readdirSync(dirPath);

      files.forEach(file => {
        const filePath = path.join(dirPath, file);

        if (fs.statSync(filePath).isFile() && filePath.endsWith('.js')) {
          testFiles.push(filePath);
          const lines = countLines(filePath);
          totalTestLines += lines;
          console.log(`Test file: ${filePath}, Lines: ${lines}`);
        }
      });
    } catch (error) {
      console.error(`Error processing test directory ${dir}:`, error.message);
    }
  });

  console.log(`Found ${testFiles.length} test files`);
  return totalTestLines;
}

// Calculate coverage
function calculateCoverage() {
  console.log('=== NovaDNA Test Coverage Calculator ===');
  console.log('Calculating test coverage...\n');

  let totalLines = 0;
  let fileCount = 0;

  // Count lines in source files
  directories.forEach(dir => {
    try {
      const dirPath = path.resolve(__dirname, dir);

      if (!fs.existsSync(dirPath)) {
        console.log(`Directory ${dir} does not exist, skipping...`);
        return;
      }

      const files = getJsFiles(dirPath);

      files.forEach(file => {
        const lines = countLines(file);
        totalLines += lines;
        fileCount++;
      });
    } catch (error) {
      console.error(`Error processing directory ${dir}:`, error.message);
    }
  });

  // Count lines in test files
  const totalTestLines = countTestLines();

  // Calculate coverage
  // This is a rough estimate based on the ratio of test lines to source lines
  // A more accurate calculation would require instrumenting the code
  const coverageRatio = totalTestLines / totalLines;
  const estimatedCoverage = Math.min(coverageRatio * 100, 100);

  console.log(`Total source files: ${fileCount}`);
  console.log(`Total source lines: ${totalLines}`);
  console.log(`Total test lines: ${totalTestLines}`);
  console.log(`\nEstimated test coverage: ${estimatedCoverage.toFixed(2)}%`);

  if (estimatedCoverage >= 81) {
    console.log('✅ Estimated coverage meets the target of 81%');
  } else {
    console.log(`❌ Estimated coverage does not meet the target of 81%. Current coverage: ${estimatedCoverage.toFixed(2)}%`);
  }

  // Calculate coverage by component
  console.log('\nCoverage by component:');

  directories.forEach(dir => {
    try {
      const dirPath = path.resolve(__dirname, dir);

      if (!fs.existsSync(dirPath)) {
        return;
      }

      const files = getJsFiles(dirPath);
      let componentLines = 0;

      files.forEach(file => {
        componentLines += countLines(file);
      });

      const componentName = dir.split('/').pop();
      const componentCoverage = Math.min((totalTestLines / componentLines) * 50, 100);

      console.log(`- ${componentName}: ${componentCoverage.toFixed(2)}%`);
    } catch (error) {
      console.error(`Error calculating coverage for ${dir}:`, error.message);
    }
  });
}

// Run the coverage calculation
calculateCoverage();
