/**
 * Preferences Context
 * 
 * This module provides a context for user preferences management in the NovaVision Hub.
 */

import React, { createContext, useContext, useState, useEffect } from 'react';
import { defaultPreferences } from './defaultPreferences';

// Create preferences context
const PreferencesContext = createContext();

/**
 * Preferences Provider component
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components
 * @param {Object} [props.initialPreferences] - Initial preferences
 * @param {string} [props.storageKey='novavision-preferences'] - Local storage key for preferences
 * @param {boolean} [props.persistPreferences=true] - Whether to persist preferences in local storage
 * @returns {React.ReactElement} Preferences Provider component
 */
export const PreferencesProvider = ({ 
  children, 
  initialPreferences = defaultPreferences,
  storageKey = 'novavision-preferences',
  persistPreferences = true
}) => {
  // State for current preferences
  const [preferences, setPreferences] = useState(() => {
    // Try to get preferences from local storage
    if (persistPreferences && typeof window !== 'undefined') {
      try {
        const storedPreferences = localStorage.getItem(storageKey);
        
        if (storedPreferences) {
          return {
            ...initialPreferences,
            ...JSON.parse(storedPreferences)
          };
        }
      } catch (error) {
        console.error('Error loading preferences from local storage:', error);
      }
    }
    
    return initialPreferences;
  });
  
  // Save preferences to local storage when they change
  useEffect(() => {
    if (persistPreferences && typeof window !== 'undefined') {
      try {
        localStorage.setItem(storageKey, JSON.stringify(preferences));
      } catch (error) {
        console.error('Error saving preferences to local storage:', error);
      }
    }
  }, [preferences, persistPreferences, storageKey]);
  
  /**
   * Update preferences
   * 
   * @param {Object|Function} newPreferences - New preferences or function to update preferences
   */
  const updatePreferences = (newPreferences) => {
    if (typeof newPreferences === 'function') {
      setPreferences(prevPreferences => {
        const updatedPreferences = newPreferences(prevPreferences);
        return updatedPreferences;
      });
    } else {
      setPreferences(prevPreferences => ({
        ...prevPreferences,
        ...newPreferences
      }));
    }
  };
  
  /**
   * Reset preferences to defaults
   */
  const resetPreferences = () => {
    setPreferences(initialPreferences);
  };
  
  /**
   * Update dashboard layout
   * 
   * @param {string} dashboardId - Dashboard ID
   * @param {Object} layout - Dashboard layout
   */
  const updateDashboardLayout = (dashboardId, layout) => {
    setPreferences(prevPreferences => ({
      ...prevPreferences,
      dashboards: {
        ...prevPreferences.dashboards,
        [dashboardId]: {
          ...(prevPreferences.dashboards?.[dashboardId] || {}),
          layout
        }
      }
    }));
  };
  
  /**
   * Update dashboard widgets
   * 
   * @param {string} dashboardId - Dashboard ID
   * @param {Array} widgets - Dashboard widgets
   */
  const updateDashboardWidgets = (dashboardId, widgets) => {
    setPreferences(prevPreferences => ({
      ...prevPreferences,
      dashboards: {
        ...prevPreferences.dashboards,
        [dashboardId]: {
          ...(prevPreferences.dashboards?.[dashboardId] || {}),
          widgets
        }
      }
    }));
  };
  
  /**
   * Update dashboard settings
   * 
   * @param {string} dashboardId - Dashboard ID
   * @param {Object} settings - Dashboard settings
   */
  const updateDashboardSettings = (dashboardId, settings) => {
    setPreferences(prevPreferences => ({
      ...prevPreferences,
      dashboards: {
        ...prevPreferences.dashboards,
        [dashboardId]: {
          ...(prevPreferences.dashboards?.[dashboardId] || {}),
          settings: {
            ...(prevPreferences.dashboards?.[dashboardId]?.settings || {}),
            ...settings
          }
        }
      }
    }));
  };
  
  /**
   * Get dashboard preferences
   * 
   * @param {string} dashboardId - Dashboard ID
   * @returns {Object} Dashboard preferences
   */
  const getDashboardPreferences = (dashboardId) => {
    return preferences.dashboards?.[dashboardId] || {};
  };
  
  /**
   * Update UI settings
   * 
   * @param {Object} settings - UI settings
   */
  const updateUISettings = (settings) => {
    setPreferences(prevPreferences => ({
      ...prevPreferences,
      ui: {
        ...prevPreferences.ui,
        ...settings
      }
    }));
  };
  
  /**
   * Update data settings
   * 
   * @param {Object} settings - Data settings
   */
  const updateDataSettings = (settings) => {
    setPreferences(prevPreferences => ({
      ...prevPreferences,
      data: {
        ...prevPreferences.data,
        ...settings
      }
    }));
  };
  
  /**
   * Update notification settings
   * 
   * @param {Object} settings - Notification settings
   */
  const updateNotificationSettings = (settings) => {
    setPreferences(prevPreferences => ({
      ...prevPreferences,
      notifications: {
        ...prevPreferences.notifications,
        ...settings
      }
    }));
  };
  
  // Create context value
  const contextValue = {
    preferences,
    updatePreferences,
    resetPreferences,
    updateDashboardLayout,
    updateDashboardWidgets,
    updateDashboardSettings,
    getDashboardPreferences,
    updateUISettings,
    updateDataSettings,
    updateNotificationSettings
  };
  
  return (
    <PreferencesContext.Provider value={contextValue}>
      {children}
    </PreferencesContext.Provider>
  );
};

/**
 * Use preferences hook
 * 
 * @returns {Object} Preferences context value
 */
export const usePreferences = () => {
  const context = useContext(PreferencesContext);
  
  if (!context) {
    throw new Error('usePreferences must be used within a PreferencesProvider');
  }
  
  return context;
};

export default PreferencesContext;
