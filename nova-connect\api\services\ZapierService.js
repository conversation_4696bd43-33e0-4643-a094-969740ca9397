/**
 * Zapier Service
 * 
 * This service handles Zapier integration for NovaConnect UAC.
 */

const fs = require('fs').promises;
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const logger = require('../../config/logger');

class ZapierService {
  constructor(dataDir = path.join(__dirname, '../../data')) {
    this.dataDir = dataDir;
    this.zapierDir = path.join(this.dataDir, 'zapier');
    this.zapierAppsFile = path.join(this.zapierDir, 'zapier_apps.json');
    this.zapierAuthsFile = path.join(this.zapierDir, 'zapier_auths.json');
    this.zapierTriggersFile = path.join(this.zapierDir, 'zapier_triggers.json');
    this.zapierActionsFile = path.join(this.zapierDir, 'zapier_actions.json');
    
    // JWT settings
    this.jwtSecret = process.env.JWT_SECRET || 'your-secret-key';
    this.jwtExpiresIn = process.env.ZAPIER_JWT_EXPIRES_IN || '30d';
    
    // OAuth settings
    this.clientId = process.env.ZAPIER_CLIENT_ID || 'nova-connect-zapier';
    this.clientSecret = process.env.ZAPIER_CLIENT_SECRET || crypto.randomBytes(32).toString('hex');
    this.redirectUri = process.env.ZAPIER_REDIRECT_URI || 'https://zapier.com/dashboard/auth/oauth/return/App-ID/';
    
    // Initialize data directory
    this.ensureDataDir();
  }
  
  /**
   * Ensure the data directory exists
   */
  async ensureDataDir() {
    try {
      await fs.mkdir(this.zapierDir, { recursive: true });
      
      // Initialize Zapier apps file if it doesn't exist
      try {
        await fs.access(this.zapierAppsFile);
      } catch (error) {
        if (error.code === 'ENOENT') {
          await fs.writeFile(this.zapierAppsFile, JSON.stringify([]));
        } else {
          throw error;
        }
      }
      
      // Initialize Zapier auths file if it doesn't exist
      try {
        await fs.access(this.zapierAuthsFile);
      } catch (error) {
        if (error.code === 'ENOENT') {
          await fs.writeFile(this.zapierAuthsFile, JSON.stringify([]));
        } else {
          throw error;
        }
      }
      
      // Initialize Zapier triggers file if it doesn't exist
      try {
        await fs.access(this.zapierTriggersFile);
      } catch (error) {
        if (error.code === 'ENOENT') {
          await fs.writeFile(this.zapierTriggersFile, JSON.stringify([]));
        } else {
          throw error;
        }
      }
      
      // Initialize Zapier actions file if it doesn't exist
      try {
        await fs.access(this.zapierActionsFile);
      } catch (error) {
        if (error.code === 'ENOENT') {
          await fs.writeFile(this.zapierActionsFile, JSON.stringify([]));
        } else {
          throw error;
        }
      }
    } catch (error) {
      logger.error('Error creating Zapier directory:', error);
      throw error;
    }
  }
  
  /**
   * Load data from file
   */
  async loadData(filePath) {
    try {
      const data = await fs.readFile(filePath, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      logger.error(`Error loading data from ${filePath}:`, error);
      return [];
    }
  }
  
  /**
   * Save data to file
   */
  async saveData(filePath, data) {
    try {
      await fs.writeFile(filePath, JSON.stringify(data, null, 2));
    } catch (error) {
      logger.error(`Error saving data to ${filePath}:`, error);
      throw error;
    }
  }
  
  /**
   * Get Zapier app definition
   */
  getAppDefinition() {
    return {
      title: 'NovaConnect UAC',
      description: 'Connect NovaConnect UAC with 5,000+ apps on Zapier.',
      version: '1.0.0',
      platformVersion: '1.0.0',
      authentication: {
        type: 'oauth2',
        oauth2Config: {
          authorizeUrl: `${process.env.API_BASE_URL || 'https://api.nova-connect.io'}/api/zapier/oauth/authorize`,
          tokenUrl: `${process.env.API_BASE_URL || 'https://api.nova-connect.io'}/api/zapier/oauth/token`,
          refreshUrl: `${process.env.API_BASE_URL || 'https://api.nova-connect.io'}/api/zapier/oauth/refresh`,
          autoRefresh: true,
          scope: 'read write'
        },
        connectionLabel: '{{bundle.authData.username}}'
      },
      beforeApp: {
        url: `${process.env.API_BASE_URL || 'https://api.nova-connect.io'}/api/zapier/before-app`
      },
      afterApp: {
        url: `${process.env.API_BASE_URL || 'https://api.nova-connect.io'}/api/zapier/after-app`
      }
    };
  }
  
  /**
   * Get Zapier triggers
   */
  async getTriggers() {
    const triggers = await this.loadData(this.zapierTriggersFile);
    
    // Add default triggers if none exist
    if (triggers.length === 0) {
      const defaultTriggers = [
        {
          key: 'new_connector',
          noun: 'Connector',
          display: {
            label: 'New Connector',
            description: 'Triggers when a new connector is created.'
          },
          operation: {
            type: 'polling',
            perform: {
              url: `${process.env.API_BASE_URL || 'https://api.nova-connect.io'}/api/zapier/triggers/new-connector`
            },
            sample: {
              id: 'conn-123',
              name: 'Sample Connector',
              type: 'api',
              createdAt: '2023-01-01T00:00:00Z'
            }
          }
        },
        {
          key: 'new_workflow',
          noun: 'Workflow',
          display: {
            label: 'New Workflow',
            description: 'Triggers when a new workflow is created.'
          },
          operation: {
            type: 'polling',
            perform: {
              url: `${process.env.API_BASE_URL || 'https://api.nova-connect.io'}/api/zapier/triggers/new-workflow`
            },
            sample: {
              id: 'wf-123',
              name: 'Sample Workflow',
              status: 'active',
              createdAt: '2023-01-01T00:00:00Z'
            }
          }
        },
        {
          key: 'compliance_event',
          noun: 'Compliance Event',
          display: {
            label: 'New Compliance Event',
            description: 'Triggers when a new compliance event occurs.'
          },
          operation: {
            type: 'polling',
            perform: {
              url: `${process.env.API_BASE_URL || 'https://api.nova-connect.io'}/api/zapier/triggers/compliance-event`
            },
            sample: {
              id: 'evt-123',
              type: 'compliance.violation',
              severity: 'high',
              resource: 'storage-bucket-123',
              details: 'Public access detected',
              timestamp: '2023-01-01T00:00:00Z'
            }
          }
        }
      ];
      
      await this.saveData(this.zapierTriggersFile, defaultTriggers);
      return defaultTriggers;
    }
    
    return triggers;
  }
  
  /**
   * Get Zapier actions
   */
  async getActions() {
    const actions = await this.loadData(this.zapierActionsFile);
    
    // Add default actions if none exist
    if (actions.length === 0) {
      const defaultActions = [
        {
          key: 'create_connector',
          noun: 'Connector',
          display: {
            label: 'Create Connector',
            description: 'Creates a new connector.'
          },
          operation: {
            type: 'perform',
            perform: {
              url: `${process.env.API_BASE_URL || 'https://api.nova-connect.io'}/api/zapier/actions/create-connector`,
              method: 'POST',
              body: {
                name: '{{bundle.inputData.name}}',
                type: '{{bundle.inputData.type}}',
                config: '{{bundle.inputData.config}}'
              }
            },
            inputFields: [
              {
                key: 'name',
                label: 'Name',
                type: 'string',
                required: true,
                helpText: 'The name of the connector.'
              },
              {
                key: 'type',
                label: 'Type',
                type: 'string',
                required: true,
                choices: {
                  api: 'API',
                  database: 'Database',
                  file: 'File'
                },
                helpText: 'The type of the connector.'
              },
              {
                key: 'config',
                label: 'Configuration',
                type: 'text',
                required: true,
                helpText: 'The configuration of the connector in JSON format.'
              }
            ],
            sample: {
              id: 'conn-123',
              name: 'Sample Connector',
              type: 'api',
              createdAt: '2023-01-01T00:00:00Z'
            }
          }
        },
        {
          key: 'execute_workflow',
          noun: 'Workflow',
          display: {
            label: 'Execute Workflow',
            description: 'Executes a workflow.'
          },
          operation: {
            type: 'perform',
            perform: {
              url: `${process.env.API_BASE_URL || 'https://api.nova-connect.io'}/api/zapier/actions/execute-workflow`,
              method: 'POST',
              body: {
                workflowId: '{{bundle.inputData.workflowId}}',
                inputs: '{{bundle.inputData.inputs}}'
              }
            },
            inputFields: [
              {
                key: 'workflowId',
                label: 'Workflow ID',
                type: 'string',
                required: true,
                helpText: 'The ID of the workflow to execute.'
              },
              {
                key: 'inputs',
                label: 'Inputs',
                type: 'text',
                required: false,
                helpText: 'The inputs for the workflow in JSON format.'
              }
            ],
            sample: {
              id: 'exec-123',
              workflowId: 'wf-123',
              status: 'completed',
              result: {
                success: true,
                data: {}
              },
              startedAt: '2023-01-01T00:00:00Z',
              completedAt: '2023-01-01T00:00:01Z'
            }
          }
        },
        {
          key: 'create_compliance_evidence',
          noun: 'Compliance Evidence',
          display: {
            label: 'Create Compliance Evidence',
            description: 'Creates a new compliance evidence record.'
          },
          operation: {
            type: 'perform',
            perform: {
              url: `${process.env.API_BASE_URL || 'https://api.nova-connect.io'}/api/zapier/actions/create-compliance-evidence`,
              method: 'POST',
              body: {
                controlId: '{{bundle.inputData.controlId}}',
                evidenceType: '{{bundle.inputData.evidenceType}}',
                description: '{{bundle.inputData.description}}',
                data: '{{bundle.inputData.data}}'
              }
            },
            inputFields: [
              {
                key: 'controlId',
                label: 'Control ID',
                type: 'string',
                required: true,
                helpText: 'The ID of the compliance control.'
              },
              {
                key: 'evidenceType',
                label: 'Evidence Type',
                type: 'string',
                required: true,
                choices: {
                  document: 'Document',
                  screenshot: 'Screenshot',
                  log: 'Log',
                  test_result: 'Test Result',
                  attestation: 'Attestation'
                },
                helpText: 'The type of evidence.'
              },
              {
                key: 'description',
                label: 'Description',
                type: 'text',
                required: true,
                helpText: 'A description of the evidence.'
              },
              {
                key: 'data',
                label: 'Data',
                type: 'text',
                required: false,
                helpText: 'Additional data for the evidence in JSON format.'
              }
            ],
            sample: {
              id: 'evid-123',
              controlId: 'ctrl-123',
              evidenceType: 'document',
              description: 'Sample evidence',
              createdAt: '2023-01-01T00:00:00Z'
            }
          }
        }
      ];
      
      await this.saveData(this.zapierActionsFile, defaultActions);
      return defaultActions;
    }
    
    return actions;
  }
  
  /**
   * Create OAuth authorization URL
   */
  createAuthorizationUrl(state, redirectUri) {
    const authUrl = new URL(`${process.env.API_BASE_URL || 'https://api.nova-connect.io'}/api/zapier/oauth/authorize`);
    
    authUrl.searchParams.append('client_id', this.clientId);
    authUrl.searchParams.append('redirect_uri', redirectUri || this.redirectUri);
    authUrl.searchParams.append('response_type', 'code');
    authUrl.searchParams.append('state', state);
    
    return authUrl.toString();
  }
  
  /**
   * Generate OAuth access token
   */
  async generateAccessToken(code, redirectUri) {
    try {
      // In a real implementation, this would validate the code
      // For now, we'll generate a token directly
      
      // Generate a random user ID for demo purposes
      const userId = `user-${Math.floor(Math.random() * 1000)}`;
      
      // Generate access token
      const accessToken = jwt.sign(
        {
          sub: userId,
          client_id: this.clientId,
          scope: 'read write'
        },
        this.jwtSecret,
        { expiresIn: this.jwtExpiresIn }
      );
      
      // Generate refresh token
      const refreshToken = crypto.randomBytes(32).toString('hex');
      
      // Save auth data
      const auths = await this.loadData(this.zapierAuthsFile);
      
      auths.push({
        userId,
        clientId: this.clientId,
        accessToken,
        refreshToken,
        scope: 'read write',
        createdAt: new Date().toISOString(),
        expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // 30 days
      });
      
      await this.saveData(this.zapierAuthsFile, auths);
      
      return {
        access_token: accessToken,
        refresh_token: refreshToken,
        token_type: 'Bearer',
        expires_in: 30 * 24 * 60 * 60, // 30 days in seconds
        scope: 'read write'
      };
    } catch (error) {
      logger.error('Error generating access token:', error);
      throw error;
    }
  }
  
  /**
   * Refresh OAuth access token
   */
  async refreshAccessToken(refreshToken) {
    try {
      // Load auths
      const auths = await this.loadData(this.zapierAuthsFile);
      
      // Find auth by refresh token
      const authIndex = auths.findIndex(auth => auth.refreshToken === refreshToken);
      
      if (authIndex === -1) {
        throw new Error('Invalid refresh token');
      }
      
      const auth = auths[authIndex];
      
      // Generate new access token
      const accessToken = jwt.sign(
        {
          sub: auth.userId,
          client_id: auth.clientId,
          scope: auth.scope
        },
        this.jwtSecret,
        { expiresIn: this.jwtExpiresIn }
      );
      
      // Generate new refresh token
      const newRefreshToken = crypto.randomBytes(32).toString('hex');
      
      // Update auth data
      auths[authIndex] = {
        ...auth,
        accessToken,
        refreshToken: newRefreshToken,
        updatedAt: new Date().toISOString(),
        expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // 30 days
      };
      
      await this.saveData(this.zapierAuthsFile, auths);
      
      return {
        access_token: accessToken,
        refresh_token: newRefreshToken,
        token_type: 'Bearer',
        expires_in: 30 * 24 * 60 * 60, // 30 days in seconds
        scope: auth.scope
      };
    } catch (error) {
      logger.error('Error refreshing access token:', error);
      throw error;
    }
  }
  
  /**
   * Verify OAuth access token
   */
  async verifyAccessToken(accessToken) {
    try {
      // Verify JWT
      const decoded = jwt.verify(accessToken, this.jwtSecret);
      
      // Load auths
      const auths = await this.loadData(this.zapierAuthsFile);
      
      // Find auth by access token
      const auth = auths.find(auth => auth.accessToken === accessToken);
      
      if (!auth) {
        throw new Error('Invalid access token');
      }
      
      return {
        userId: decoded.sub,
        clientId: decoded.client_id,
        scope: decoded.scope
      };
    } catch (error) {
      logger.error('Error verifying access token:', error);
      throw error;
    }
  }
  
  /**
   * Register Zapier app
   */
  async registerApp(appData) {
    try {
      // Load apps
      const apps = await this.loadData(this.zapierAppsFile);
      
      // Create app
      const app = {
        id: uuidv4(),
        ...appData,
        createdAt: new Date().toISOString()
      };
      
      // Add app
      apps.push(app);
      
      // Save apps
      await this.saveData(this.zapierAppsFile, apps);
      
      return app;
    } catch (error) {
      logger.error('Error registering Zapier app:', error);
      throw error;
    }
  }
  
  /**
   * Get Zapier app by ID
   */
  async getAppById(appId) {
    try {
      // Load apps
      const apps = await this.loadData(this.zapierAppsFile);
      
      // Find app by ID
      const app = apps.find(app => app.id === appId);
      
      if (!app) {
        throw new Error(`App with ID ${appId} not found`);
      }
      
      return app;
    } catch (error) {
      logger.error('Error getting Zapier app by ID:', error);
      throw error;
    }
  }
  
  /**
   * Get all Zapier apps
   */
  async getAllApps() {
    try {
      // Load apps
      return await this.loadData(this.zapierAppsFile);
    } catch (error) {
      logger.error('Error getting all Zapier apps:', error);
      throw error;
    }
  }
  
  /**
   * Update Zapier app
   */
  async updateApp(appId, appData) {
    try {
      // Load apps
      const apps = await this.loadData(this.zapierAppsFile);
      
      // Find app index
      const appIndex = apps.findIndex(app => app.id === appId);
      
      if (appIndex === -1) {
        throw new Error(`App with ID ${appId} not found`);
      }
      
      // Update app
      apps[appIndex] = {
        ...apps[appIndex],
        ...appData,
        updatedAt: new Date().toISOString()
      };
      
      // Save apps
      await this.saveData(this.zapierAppsFile, apps);
      
      return apps[appIndex];
    } catch (error) {
      logger.error('Error updating Zapier app:', error);
      throw error;
    }
  }
  
  /**
   * Delete Zapier app
   */
  async deleteApp(appId) {
    try {
      // Load apps
      const apps = await this.loadData(this.zapierAppsFile);
      
      // Find app index
      const appIndex = apps.findIndex(app => app.id === appId);
      
      if (appIndex === -1) {
        throw new Error(`App with ID ${appId} not found`);
      }
      
      // Remove app
      apps.splice(appIndex, 1);
      
      // Save apps
      await this.saveData(this.zapierAppsFile, apps);
      
      return true;
    } catch (error) {
      logger.error('Error deleting Zapier app:', error);
      throw error;
    }
  }
  
  /**
   * Register Zapier trigger
   */
  async registerTrigger(triggerData) {
    try {
      // Load triggers
      const triggers = await this.loadData(this.zapierTriggersFile);
      
      // Create trigger
      const trigger = {
        id: uuidv4(),
        ...triggerData,
        createdAt: new Date().toISOString()
      };
      
      // Add trigger
      triggers.push(trigger);
      
      // Save triggers
      await this.saveData(this.zapierTriggersFile, triggers);
      
      return trigger;
    } catch (error) {
      logger.error('Error registering Zapier trigger:', error);
      throw error;
    }
  }
  
  /**
   * Register Zapier action
   */
  async registerAction(actionData) {
    try {
      // Load actions
      const actions = await this.loadData(this.zapierActionsFile);
      
      // Create action
      const action = {
        id: uuidv4(),
        ...actionData,
        createdAt: new Date().toISOString()
      };
      
      // Add action
      actions.push(action);
      
      // Save actions
      await this.saveData(this.zapierActionsFile, actions);
      
      return action;
    } catch (error) {
      logger.error('Error registering Zapier action:', error);
      throw error;
    }
  }
}

module.exports = ZapierService;
