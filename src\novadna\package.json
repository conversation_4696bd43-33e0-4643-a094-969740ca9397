{"name": "novadna", "version": "1.0.0", "description": "novadna - NovaFuse Technologies Component", "main": "index.js", "scripts": {"start": "node index.js", "test": "mocha tests/**/*.test.js", "test:coverage": "nyc mocha tests/**/*.test.js", "health": "curl http://localhost:8080/health"}, "keywords": ["novafuse", "nova", "component", "intelligent-infrastructure"], "author": "NovaFuse Technologies", "license": "PROPRIETARY", "dependencies": {"express": "^4.18.0", "jsonwebtoken": "^9.0.0", "prometheus-client": "^14.0.0"}, "devDependencies": {"mocha": "^10.0.0", "chai": "^4.3.0", "nyc": "^15.1.0"}, "engines": {"node": ">=16.0.0"}}