/**
 * Analytics Middleware
 * 
 * This middleware tracks API usage and performance.
 */

const AnalyticsService = require('../services/AnalyticsService');

const analyticsService = new AnalyticsService();

/**
 * Track API usage and performance
 */
const trackApiUsage = (req, res, next) => {
  // Record start time
  const startTime = process.hrtime();
  
  // Get original end method
  const originalEnd = res.end;
  
  // Override end method to capture response data
  res.end = function(chunk, encoding) {
    // Calculate response time
    const hrTime = process.hrtime(startTime);
    const responseTime = hrTime[0] * 1000 + hrTime[1] / 1000000; // Convert to milliseconds
    
    // Get response size
    const responseSize = res.getHeader('content-length') || 
      (chunk ? Buffer.byteLength(chunk, encoding) : 0);
    
    // Get request size
    const requestSize = req.headers['content-length'] || 0;
    
    // Track usage
    analyticsService.trackUsage({
      endpoint: req.originalUrl || req.url,
      method: req.method,
      userId: req.user ? req.user.id : null,
      apiKeyId: req.apiKey ? req.apiKey.id : null,
      userAgent: req.headers['user-agent'],
      ip: req.ip || req.connection.remoteAddress,
      responseStatus: res.statusCode,
      responseTime,
      requestSize,
      responseSize
    });
    
    // Track performance
    analyticsService.trackPerformance({
      endpoint: req.originalUrl || req.url,
      method: req.method,
      responseTime,
      processingTime: req.processingTime || null,
      dbQueryTime: req.dbQueryTime || null
    });
    
    // Call original end method
    return originalEnd.apply(res, arguments);
  };
  
  next();
};

/**
 * Track API errors
 */
const trackApiErrors = (err, req, res, next) => {
  // Track error
  analyticsService.trackError({
    endpoint: req.originalUrl || req.url,
    method: req.method,
    userId: req.user ? req.user.id : null,
    apiKeyId: req.apiKey ? req.apiKey.id : null,
    userAgent: req.headers['user-agent'],
    ip: req.ip || req.connection.remoteAddress,
    errorCode: err.statusCode || 500,
    errorMessage: err.message,
    errorStack: process.env.NODE_ENV === 'development' ? err.stack : null,
    requestBody: process.env.NODE_ENV === 'development' ? req.body : null
  });
  
  next(err);
};

module.exports = {
  trackApiUsage,
  trackApiErrors
};
