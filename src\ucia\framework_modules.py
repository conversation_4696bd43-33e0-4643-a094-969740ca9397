"""
Framework Modules for the Universal Compliance Intelligence Architecture (UCIA).

This module implements the plug-and-play modules for specific regulatory frameworks,
including the module registry that manages available modules, their versions, and dependencies.
"""

import os
import json
import logging
from typing import Dict, List, Optional, Any, Tuple
from abc import ABC, abstractmethod

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ComplianceFrameworkModule(ABC):
    """
    Abstract base class for compliance framework modules.

    This class defines the interface that all framework modules must implement.
    Framework modules provide framework-specific knowledge and functionality.
    """

    @abstractmethod
    def get_metadata(self) -> Dict[str, Any]:
        """
        Get metadata about the framework module.

        Returns:
            A dictionary containing metadata about the module
        """
        pass

    @abstractmethod
    def process_query(self, query: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a query using this framework's knowledge.

        Args:
            query: The interpreted query
            context: The conversation context

        Returns:
            A dictionary containing the framework-specific response and metadata
        """
        pass

    @abstractmethod
    def validate_artifact(self, artifact: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate a compliance artifact against this framework's requirements.

        Args:
            artifact: The compliance artifact to validate

        Returns:
            A dictionary containing validation results
        """
        pass

    @abstractmethod
    def get_requirements(self, category: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get the requirements defined by this framework.

        Args:
            category: Optional category to filter requirements

        Returns:
            A list of requirement dictionaries
        """
        pass

    @abstractmethod
    def get_mappings(self, other_framework: str) -> Dict[str, List[str]]:
        """
        Get mappings between this framework and another framework.

        Args:
            other_framework: The identifier of the other framework

        Returns:
            A dictionary mapping this framework's requirements to the other framework's requirements
        """
        pass


class GDPRModule(ComplianceFrameworkModule):
    """
    GDPR compliance framework module.

    This module provides GDPR-specific knowledge and functionality.
    """

    def __init__(self, data_path: Optional[str] = None):
        """
        Initialize the GDPR module.

        Args:
            data_path: Path to the module's data files
        """
        self.data_path = data_path or os.path.join(os.path.dirname(__file__), 'data', 'gdpr')
        self.requirements = self._load_requirements()
        self.mappings = self._load_mappings()
        logger.info("GDPR module initialized")

    def get_metadata(self) -> Dict[str, Any]:
        """
        Get metadata about the GDPR module.

        Returns:
            A dictionary containing metadata about the module
        """
        return {
            'id': 'gdpr',
            'name': 'General Data Protection Regulation',
            'version': '1.0.0',
            'description': 'European Union data protection and privacy regulation',
            'effective_date': '2018-05-25',
            'jurisdictions': ['EU', 'EEA'],
            'categories': ['data_protection', 'privacy', 'data_subject_rights', 'data_breach']
        }

    def process_query(self, query: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a query using GDPR knowledge.

        Args:
            query: The interpreted query
            context: The conversation context

        Returns:
            A dictionary containing the GDPR-specific response and metadata
        """
        # TODO: Implement GDPR-specific query processing

        # For now, return a placeholder response
        concepts = query.get('detected_concepts', [])

        if 'DATA_BREACH' in concepts:
            return {
                'response_text': "Under GDPR Article 33, a data breach must be reported to the supervisory authority within 72 hours of becoming aware of it, if it poses a risk to individuals' rights and freedoms.",
                'citations': [{
                    'framework': 'GDPR',
                    'reference': 'Article 33',
                    'text': 'Notification of a personal data breach to the supervisory authority',
                    'url': 'https://gdpr-info.eu/art-33-gdpr/'
                }],
                'confidence': 0.9
            }
        elif 'DATA_SUBJECT_RIGHTS' in concepts:
            return {
                'response_text': "GDPR provides individuals with several rights, including the right to access, rectify, erase, restrict processing, data portability, and object to processing of their personal data.",
                'citations': [{
                    'framework': 'GDPR',
                    'reference': 'Chapter 3',
                    'text': 'Rights of the data subject',
                    'url': 'https://gdpr-info.eu/chapter-3/'
                }],
                'confidence': 0.9
            }
        elif 'CONSENT' in concepts:
            return {
                'response_text': "Under GDPR Article 7, consent must be freely given, specific, informed, and unambiguous. It must be as easy to withdraw consent as it is to give it.",
                'citations': [{
                    'framework': 'GDPR',
                    'reference': 'Article 7',
                    'text': 'Conditions for consent',
                    'url': 'https://gdpr-info.eu/art-7-gdpr/'
                }],
                'confidence': 0.9
            }
        else:
            return {
                'response_text': "GDPR is the European Union's data protection and privacy regulation that applies to the processing of personal data of EU residents.",
                'citations': [{
                    'framework': 'GDPR',
                    'reference': 'Article 1',
                    'text': 'Subject-matter and objectives',
                    'url': 'https://gdpr-info.eu/art-1-gdpr/'
                }],
                'confidence': 0.8
            }

    def validate_artifact(self, artifact: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate a compliance artifact against GDPR requirements.

        Args:
            artifact: The compliance artifact to validate

        Returns:
            A dictionary containing validation results
        """
        # TODO: Implement GDPR-specific artifact validation

        # For now, return a placeholder validation result
        return {
            'valid': True,
            'findings': [],
            'confidence': 0.7
        }

    def get_requirements(self, category: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get the requirements defined by GDPR.

        Args:
            category: Optional category to filter requirements

        Returns:
            A list of requirement dictionaries
        """
        if category:
            return [req for req in self.requirements if category in req.get('categories', [])]
        return self.requirements

    def get_mappings(self, other_framework: str) -> Dict[str, List[str]]:
        """
        Get mappings between GDPR and another framework.

        Args:
            other_framework: The identifier of the other framework

        Returns:
            A dictionary mapping GDPR requirements to the other framework's requirements
        """
        return self.mappings.get(other_framework, {})

    def _load_requirements(self) -> List[Dict[str, Any]]:
        """
        Load GDPR requirements from data files.

        Returns:
            A list of requirement dictionaries
        """
        requirements_file = os.path.join(self.data_path, 'requirements.json')
        try:
            with open(requirements_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return data.get('requirements', [])
        except (FileNotFoundError, json.JSONDecodeError) as e:
            logger.warning(f"Failed to load GDPR requirements: {e}")
            # Return placeholder requirements if file loading fails
            return [
                {
                    'id': 'gdpr-art-5',
                    'reference': 'Article 5',
                    'title': 'Principles relating to processing of personal data',
                    'text': 'Personal data shall be processed lawfully, fairly, and in a transparent manner...',
                    'categories': ['principles', 'data_processing'],
                    'url': 'https://gdpr-info.eu/art-5-gdpr/'
                },
                {
                    'id': 'gdpr-art-7',
                    'reference': 'Article 7',
                    'title': 'Conditions for consent',
                    'text': 'Where processing is based on consent, the controller shall be able to demonstrate that the data subject has consented...',
                    'categories': ['consent', 'data_processing'],
                    'url': 'https://gdpr-info.eu/art-7-gdpr/'
                },
                {
                    'id': 'gdpr-art-33',
                    'reference': 'Article 33',
                    'title': 'Notification of a personal data breach to the supervisory authority',
                    'text': 'In the case of a personal data breach, the controller shall without undue delay and, where feasible, not later than 72 hours after having become aware of it, notify the personal data breach to the supervisory authority...',
                    'categories': ['data_breach', 'notification'],
                    'url': 'https://gdpr-info.eu/art-33-gdpr/'
                }
            ]

    def _load_mappings(self) -> Dict[str, Dict[str, List[str]]]:
        """
        Load mappings between GDPR and other frameworks from data files.

        Returns:
            A dictionary of dictionaries mapping GDPR requirements to other frameworks' requirements
        """
        mappings_file = os.path.join(self.data_path, 'mappings.json')
        try:
            with open(mappings_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return data.get('mappings', {})
        except (FileNotFoundError, json.JSONDecodeError) as e:
            logger.warning(f"Failed to load GDPR mappings: {e}")
            # Return placeholder mappings if file loading fails
            return {
                'hipaa': {
                    'gdpr-art-33': ['hipaa-164-308-a-6-i', 'hipaa-164-308-a-6-ii'],
                    'gdpr-art-5': ['hipaa-164-306-a']
                },
                'soc2': {
                    'gdpr-art-5': ['soc2-cc1-1', 'soc2-cc1-2'],
                    'gdpr-art-33': ['soc2-cc7-3', 'soc2-cc7-4']
                }
            }


class HIPAAModule(ComplianceFrameworkModule):
    """
    HIPAA compliance framework module.

    This module provides HIPAA-specific knowledge and functionality.
    """

    def __init__(self, data_path: Optional[str] = None):
        """
        Initialize the HIPAA module.

        Args:
            data_path: Path to the module's data files
        """
        self.data_path = data_path or os.path.join(os.path.dirname(__file__), 'data', 'hipaa')
        self.requirements = self._load_requirements()
        self.mappings = self._load_mappings()
        logger.info("HIPAA module initialized")

    def get_metadata(self) -> Dict[str, Any]:
        """
        Get metadata about the HIPAA module.

        Returns:
            A dictionary containing metadata about the module
        """
        return {
            'id': 'hipaa',
            'name': 'Health Insurance Portability and Accountability Act',
            'version': '1.0.0',
            'description': 'United States healthcare data privacy and security regulation',
            'effective_date': '2003-04-14',
            'jurisdictions': ['US'],
            'categories': ['healthcare', 'privacy', 'security', 'data_breach']
        }

    def process_query(self, query: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a query using HIPAA knowledge.

        Args:
            query: The interpreted query
            context: The conversation context

        Returns:
            A dictionary containing the HIPAA-specific response and metadata
        """
        # TODO: Implement HIPAA-specific query processing

        # For now, return a placeholder response
        concepts = query.get('detected_concepts', [])

        if 'DATA_BREACH' in concepts:
            return {
                'response_text': "Under HIPAA, a covered entity must notify affected individuals of a breach of unsecured protected health information without unreasonable delay and no later than 60 days after discovery.",
                'citations': [{
                    'framework': 'HIPAA',
                    'reference': '45 CFR § 164.404',
                    'text': 'Notification to individuals',
                    'url': 'https://www.law.cornell.edu/cfr/text/45/164.404'
                }],
                'confidence': 0.9
            }
        else:
            return {
                'response_text': "HIPAA is the United States regulation that protects the privacy and security of certain health information.",
                'citations': [{
                    'framework': 'HIPAA',
                    'reference': '45 CFR Part 160 and Part 164',
                    'text': 'HIPAA Privacy, Security, and Breach Notification Rules',
                    'url': 'https://www.hhs.gov/hipaa/for-professionals/privacy/laws-regulations/index.html'
                }],
                'confidence': 0.8
            }

    def validate_artifact(self, artifact: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate a compliance artifact against HIPAA requirements.

        Args:
            artifact: The compliance artifact to validate

        Returns:
            A dictionary containing validation results
        """
        # TODO: Implement HIPAA-specific artifact validation

        # For now, return a placeholder validation result
        return {
            'valid': True,
            'findings': [],
            'confidence': 0.7
        }

    def get_requirements(self, category: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get the requirements defined by HIPAA.

        Args:
            category: Optional category to filter requirements

        Returns:
            A list of requirement dictionaries
        """
        if category:
            return [req for req in self.requirements if category in req.get('categories', [])]
        return self.requirements

    def get_mappings(self, other_framework: str) -> Dict[str, List[str]]:
        """
        Get mappings between HIPAA and another framework.

        Args:
            other_framework: The identifier of the other framework

        Returns:
            A dictionary mapping HIPAA requirements to the other framework's requirements
        """
        return self.mappings.get(other_framework, {})

    def _load_requirements(self) -> List[Dict[str, Any]]:
        """
        Load HIPAA requirements from data files.

        Returns:
            A list of requirement dictionaries
        """
        requirements_file = os.path.join(self.data_path, 'requirements.json')
        try:
            with open(requirements_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return data.get('requirements', [])
        except (FileNotFoundError, json.JSONDecodeError) as e:
            logger.warning(f"Failed to load HIPAA requirements: {e}")
            # Return placeholder requirements if file loading fails
            return [
                {
                    'id': 'hipaa-164-306-a',
                    'reference': '45 CFR § 164.306(a)',
                    'title': 'Security standards: General rules',
                    'text': 'Covered entities and business associates must ensure the confidentiality, integrity, and availability of all electronic protected health information...',
                    'categories': ['security', 'general'],
                    'url': 'https://www.law.cornell.edu/cfr/text/45/164.306'
                },
                {
                    'id': 'hipaa-164-308-a-6-i',
                    'reference': '45 CFR § 164.308(a)(6)(i)',
                    'title': 'Security incident procedures',
                    'text': 'Implement policies and procedures to address security incidents.',
                    'categories': ['security', 'incident_response'],
                    'url': 'https://www.law.cornell.edu/cfr/text/45/164.308'
                },
                {
                    'id': 'hipaa-164-308-a-6-ii',
                    'reference': '45 CFR § 164.308(a)(6)(ii)',
                    'title': 'Response and reporting',
                    'text': 'Identify and respond to suspected or known security incidents; mitigate, to the extent practicable, harmful effects of security incidents that are known to the covered entity or business associate; and document security incidents and their outcomes.',
                    'categories': ['security', 'incident_response', 'documentation'],
                    'url': 'https://www.law.cornell.edu/cfr/text/45/164.308'
                }
            ]

    def _load_mappings(self) -> Dict[str, Dict[str, List[str]]]:
        """
        Load mappings between HIPAA and other frameworks from data files.

        Returns:
            A dictionary of dictionaries mapping HIPAA requirements to other frameworks' requirements
        """
        mappings_file = os.path.join(self.data_path, 'mappings.json')
        try:
            with open(mappings_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return data.get('mappings', {})
        except (FileNotFoundError, json.JSONDecodeError) as e:
            logger.warning(f"Failed to load HIPAA mappings: {e}")
            # Return placeholder mappings if file loading fails
            return {
                'gdpr': {
                    'hipaa-164-308-a-6-i': ['gdpr-art-33'],
                    'hipaa-164-308-a-6-ii': ['gdpr-art-33'],
                    'hipaa-164-306-a': ['gdpr-art-5', 'gdpr-art-32']
                },
                'soc2': {
                    'hipaa-164-306-a': ['soc2-cc1-1', 'soc2-cc1-2'],
                    'hipaa-164-308-a-6-i': ['soc2-cc7-3'],
                    'hipaa-164-308-a-6-ii': ['soc2-cc7-4', 'soc2-cc7-5']
                }
            }


class SOC2Module(ComplianceFrameworkModule):
    """
    SOC2 compliance framework module.

    This module provides SOC2-specific knowledge and functionality.
    """

    def __init__(self, data_path: Optional[str] = None):
        """
        Initialize the SOC2 module.

        Args:
            data_path: Path to the module's data files
        """
        self.data_path = data_path or os.path.join(os.path.dirname(__file__), 'data', 'soc2')
        self.requirements = self._load_requirements()
        self.mappings = self._load_mappings()
        logger.info("SOC2 module initialized")

    def get_metadata(self) -> Dict[str, Any]:
        """
        Get metadata about the SOC2 module.

        Returns:
            A dictionary containing metadata about the module
        """
        return {
            'id': 'soc2',
            'name': 'Service Organization Control 2',
            'version': '1.0.0',
            'description': 'AICPA Trust Services Criteria for security, availability, processing integrity, confidentiality, and privacy',
            'effective_date': '2017-05-01',
            'jurisdictions': ['US', 'Global'],
            'categories': ['security', 'availability', 'processing_integrity', 'confidentiality', 'privacy']
        }

    def process_query(self, query: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a query using SOC2 knowledge.

        Args:
            query: The interpreted query
            context: The conversation context

        Returns:
            A dictionary containing the SOC2-specific response and metadata
        """
        # TODO: Implement SOC2-specific query processing

        # For now, return a placeholder response
        concepts = query.get('detected_concepts', [])

        if 'DATA_BREACH' in concepts or 'incident_response' in concepts:
            return {
                'response_text': "SOC2 requires organizations to have a defined incident response program to understand, contain, remediate, and communicate security incidents.",
                'citations': [{
                    'framework': 'SOC2',
                    'reference': 'CC7.4',
                    'text': 'The entity responds to identified security incidents by executing a defined incident response program to understand, contain, remediate, and communicate security incidents, as appropriate.',
                    'url': 'https://www.aicpa.org/interestareas/frc/assuranceadvisoryservices/sorhome.html'
                }],
                'confidence': 0.9
            }
        else:
            return {
                'response_text': "SOC2 is a framework that defines criteria for managing customer data based on five trust service criteria: security, availability, processing integrity, confidentiality, and privacy.",
                'citations': [{
                    'framework': 'SOC2',
                    'reference': 'Trust Services Criteria',
                    'text': 'Trust Services Criteria for Security, Availability, Processing Integrity, Confidentiality, and Privacy',
                    'url': 'https://www.aicpa.org/interestareas/frc/assuranceadvisoryservices/sorhome.html'
                }],
                'confidence': 0.8
            }

    def validate_artifact(self, artifact: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate a compliance artifact against SOC2 requirements.

        Args:
            artifact: The compliance artifact to validate

        Returns:
            A dictionary containing validation results
        """
        # TODO: Implement SOC2-specific artifact validation

        # For now, return a placeholder validation result
        return {
            'valid': True,
            'findings': [],
            'confidence': 0.7
        }

    def get_requirements(self, category: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get the requirements defined by SOC2.

        Args:
            category: Optional category to filter requirements

        Returns:
            A list of requirement dictionaries
        """
        if category:
            return [req for req in self.requirements if category in req.get('categories', [])]
        return self.requirements

    def get_mappings(self, other_framework: str) -> Dict[str, List[str]]:
        """
        Get mappings between SOC2 and another framework.

        Args:
            other_framework: The identifier of the other framework

        Returns:
            A dictionary mapping SOC2 requirements to the other framework's requirements
        """
        return self.mappings.get(other_framework, {})

    def _load_requirements(self) -> List[Dict[str, Any]]:
        """
        Load SOC2 requirements from data files.

        Returns:
            A list of requirement dictionaries
        """
        requirements_file = os.path.join(self.data_path, 'requirements.json')
        try:
            with open(requirements_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return data.get('requirements', [])
        except (FileNotFoundError, json.JSONDecodeError) as e:
            logger.warning(f"Failed to load SOC2 requirements: {e}")
            # Return placeholder requirements if file loading fails
            return [
                {
                    'id': 'soc2-cc1-1',
                    'reference': 'CC1.1',
                    'title': 'The entity demonstrates a commitment to integrity and ethical values',
                    'text': 'The entity demonstrates a commitment to integrity and ethical values.',
                    'categories': ['governance', 'integrity', 'ethics'],
                    'url': 'https://www.aicpa.org/interestareas/frc/assuranceadvisoryservices/sorhome.html'
                },
                {
                    'id': 'soc2-cc7-3',
                    'reference': 'CC7.3',
                    'title': 'The entity evaluates security events',
                    'text': 'The entity evaluates security events to determine whether they could or have resulted in a failure of the entity to meet its objectives.',
                    'categories': ['security', 'incident_response', 'evaluation'],
                    'url': 'https://www.aicpa.org/interestareas/frc/assuranceadvisoryservices/sorhome.html'
                },
                {
                    'id': 'soc2-cc7-4',
                    'reference': 'CC7.4',
                    'title': 'The entity responds to identified security incidents',
                    'text': 'The entity responds to identified security incidents by executing a defined incident response program.',
                    'categories': ['security', 'incident_response', 'remediation'],
                    'url': 'https://www.aicpa.org/interestareas/frc/assuranceadvisoryservices/sorhome.html'
                }
            ]

    def _load_mappings(self) -> Dict[str, Dict[str, List[str]]]:
        """
        Load mappings between SOC2 and other frameworks from data files.

        Returns:
            A dictionary of dictionaries mapping SOC2 requirements to other frameworks' requirements
        """
        mappings_file = os.path.join(self.data_path, 'mappings.json')
        try:
            with open(mappings_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return data.get('mappings', {})
        except (FileNotFoundError, json.JSONDecodeError) as e:
            logger.warning(f"Failed to load SOC2 mappings: {e}")
            # Return placeholder mappings if file loading fails
            return {
                'gdpr': {
                    'soc2-cc1-1': ['gdpr-art-5', 'gdpr-art-24'],
                    'soc2-cc7-3': ['gdpr-art-33', 'gdpr-art-34'],
                    'soc2-cc7-4': ['gdpr-art-33', 'gdpr-art-34']
                },
                'hipaa': {
                    'soc2-cc1-1': ['hipaa-164-306-a'],
                    'soc2-cc7-3': ['hipaa-164-308-a-6-i', 'hipaa-164-308-a-6-ii'],
                    'soc2-cc7-4': ['hipaa-164-308-a-6-ii', 'hipaa-164-404']
                }
            }


class ModuleRegistry:
    """
    Registry for compliance framework modules.

    This class manages the available modules, their versions, and dependencies.
    It provides methods for registering, retrieving, and managing modules.
    """

    def __init__(self):
        """Initialize the Module Registry."""
        self.modules = {}
        self.active_modules = set()
        logger.info("Module Registry initialized")

    def register_module(self, module_id: str, module: ComplianceFrameworkModule) -> None:
        """
        Register a module with the registry.

        Args:
            module_id: The identifier for the module
            module: The module instance
        """
        logger.info(f"Registering module: {module_id}")
        self.modules[module_id] = module

    def get_module(self, module_id: str) -> Optional[ComplianceFrameworkModule]:
        """
        Get a module by its identifier.

        Args:
            module_id: The identifier for the module

        Returns:
            The module instance, or None if not found
        """
        return self.modules.get(module_id)

    def activate_module(self, module_id: str) -> bool:
        """
        Activate a module.

        Args:
            module_id: The identifier for the module

        Returns:
            True if the module was activated, False otherwise
        """
        if module_id in self.modules:
            logger.info(f"Activating module: {module_id}")
            self.active_modules.add(module_id)
            return True
        logger.warning(f"Cannot activate module {module_id}: not found")
        return False

    def deactivate_module(self, module_id: str) -> bool:
        """
        Deactivate a module.

        Args:
            module_id: The identifier for the module

        Returns:
            True if the module was deactivated, False otherwise
        """
        if module_id in self.active_modules:
            logger.info(f"Deactivating module: {module_id}")
            self.active_modules.remove(module_id)
            return True
        logger.warning(f"Cannot deactivate module {module_id}: not active")
        return False

    def get_active_modules(self) -> List[ComplianceFrameworkModule]:
        """
        Get all active modules.

        Returns:
            A list of active module instances
        """
        return [self.modules[module_id] for module_id in self.active_modules if module_id in self.modules]

    def get_all_modules(self) -> Dict[str, ComplianceFrameworkModule]:
        """
        Get all registered modules.

        Returns:
            A dictionary mapping module identifiers to module instances
        """
        return self.modules.copy()


# Initialize the default modules
def initialize_default_modules() -> ModuleRegistry:
    """
    Initialize the default compliance framework modules.

    Returns:
        A ModuleRegistry instance with default modules registered
    """
    registry = ModuleRegistry()

    # Register and activate the GDPR module
    gdpr_module = GDPRModule()
    registry.register_module('gdpr', gdpr_module)
    registry.activate_module('gdpr')

    # Register and activate the HIPAA module
    hipaa_module = HIPAAModule()
    registry.register_module('hipaa', hipaa_module)
    registry.activate_module('hipaa')

    # Register and activate the SOC2 module
    soc2_module = SOC2Module()
    registry.register_module('soc2', soc2_module)
    registry.activate_module('soc2')

    return registry


if __name__ == "__main__":
    # Simple test of the framework modules
    registry = initialize_default_modules()

    # Get all modules
    all_modules = registry.get_all_modules()
    print(f"Registered modules: {', '.join(all_modules.keys())}")

    # Get active modules
    active_modules = registry.get_active_modules()
    print(f"Active modules: {len(active_modules)}")

    # Test GDPR module
    gdpr_module = registry.get_module('gdpr')
    if gdpr_module:
        metadata = gdpr_module.get_metadata()
        print(f"GDPR module metadata: {metadata['name']} ({metadata['version']})")

        # Test query processing
        query = {
            'detected_concepts': ['DATA_BREACH'],
            'query_type': 'QUESTION'
        }
        context = {}
        response = gdpr_module.process_query(query, context)
        print(f"GDPR response: {response['response_text']}")
        print(f"GDPR citations: {response['citations']}")

    # Test HIPAA module
    hipaa_module = registry.get_module('hipaa')
    if hipaa_module:
        metadata = hipaa_module.get_metadata()
        print(f"HIPAA module metadata: {metadata['name']} ({metadata['version']})")

        # Test query processing
        query = {
            'detected_concepts': ['DATA_BREACH'],
            'query_type': 'QUESTION'
        }
        context = {}
        response = hipaa_module.process_query(query, context)
        print(f"HIPAA response: {response['response_text']}")
        print(f"HIPAA citations: {response['citations']}")
