/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        // CHAEONIX Divine Color Palette
        divine: {
          50: '#f0f9ff',
          100: '#e0f2fe',
          200: '#bae6fd',
          300: '#7dd3fc',
          400: '#38bdf8',
          500: '#0ea5e9',
          600: '#0284c7',
          700: '#0369a1',
          800: '#075985',
          900: '#0c4a6e',
        },
        coherence: {
          50: '#fdf4ff',
          100: '#fae8ff',
          200: '#f5d0fe',
          300: '#f0abfc',
          400: '#e879f9',
          500: '#d946ef',
          600: '#c026d3',
          700: '#a21caf',
          800: '#86198f',
          900: '#701a75',
        },
        aeonic: {
          50: '#fff7ed',
          100: '#ffedd5',
          200: '#fed7aa',
          300: '#fdba74',
          400: '#fb923c',
          500: '#f97316',
          600: '#ea580c',
          700: '#c2410c',
          800: '#9a3412',
          900: '#7c2d12',
        },
        prophecy: {
          50: '#f7fee7',
          100: '#ecfccb',
          200: '#d9f99d',
          300: '#bef264',
          400: '#a3e635',
          500: '#84cc16',
          600: '#65a30d',
          700: '#4d7c0f',
          800: '#365314',
          900: '#1a2e05',
        },
        // Tri-Market Colors
        stocks: {
          primary: '#ef4444',
          secondary: '#dc2626',
          accent: '#fca5a5',
        },
        crypto: {
          primary: '#f59e0b',
          secondary: '#d97706',
          accent: '#fcd34d',
        },
        forex: {
          primary: '#10b981',
          secondary: '#059669',
          accent: '#6ee7b7',
        },
        // Fibonacci Colors
        fib: {
          236: '#8b5cf6',
          382: '#a78bfa',
          500: '#c4b5fd',
          618: '#ddd6fe',
          786: '#ede9fe',
          1000: '#f3f4f6',
        }
      },
      fontFamily: {
        'divine': ['Inter', 'system-ui', 'sans-serif'],
        'mono': ['JetBrains Mono', 'Fira Code', 'monospace'],
      },
      animation: {
        'pulse-divine': 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'spin-slow': 'spin 3s linear infinite',
        'bounce-gentle': 'bounce 2s infinite',
        'glow': 'glow 2s ease-in-out infinite alternate',
        'coherence-wave': 'coherence-wave 4s ease-in-out infinite',
        'fibonacci-spiral': 'fibonacci-spiral 8s linear infinite',
      },
      keyframes: {
        glow: {
          '0%': { boxShadow: '0 0 5px rgba(139, 92, 246, 0.5)' },
          '100%': { boxShadow: '0 0 20px rgba(139, 92, 246, 0.8)' },
        },
        'coherence-wave': {
          '0%, 100%': { transform: 'scale(1)', opacity: '1' },
          '50%': { transform: 'scale(1.05)', opacity: '0.8' },
        },
        'fibonacci-spiral': {
          '0%': { transform: 'rotate(0deg) scale(1)' },
          '25%': { transform: 'rotate(90deg) scale(1.618)' },
          '50%': { transform: 'rotate(180deg) scale(1)' },
          '75%': { transform: 'rotate(270deg) scale(0.618)' },
          '100%': { transform: 'rotate(360deg) scale(1)' },
        }
      },
      backgroundImage: {
        'divine-gradient': 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        'coherence-gradient': 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
        'aeonic-gradient': 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
        'tri-market-gradient': 'linear-gradient(135deg, #ef4444 0%, #f59e0b 50%, #10b981 100%)',
      },
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
        '128': '32rem',
      },
      borderRadius: {
        'divine': '1.618rem',
      },
      boxShadow: {
        'divine': '0 25px 50px -12px rgba(139, 92, 246, 0.25)',
        'coherence': '0 25px 50px -12px rgba(217, 70, 239, 0.25)',
        'aeonic': '0 25px 50px -12px rgba(249, 115, 22, 0.25)',
      }
    },
  },
  plugins: [],
}
