/**
 * NovaVision - Universal UI Connector Core
 * 
 * This is the main class for the NovaVision UUIC system.
 */

import { FormBuilder } from './services/FormBuilder';
import { DashboardBuilder } from './services/DashboardBuilder';
import { ReportBuilder } from './services/ReportBuilder';
import { UISchemaService } from './services/UISchemaService';
import { RenderingService } from './services/RenderingService';
import { RegulationOrchestrator } from './services/RegulationOrchestrator';
import { UIOptimizer } from './services/UIOptimizer';
import { ConsistencyEnforcer } from './services/ConsistencyEnforcer';
import { Logger } from './utils/Logger';

// Create logger
const logger = new Logger('nova-vision');

/**
 * NovaVision options
 */
export interface NovaVisionOptions {
  theme?: string;
  responsive?: boolean;
  accessibilityLevel?: 'A' | 'AA' | 'AAA';
  regulationAware?: boolean;
  aiOptimization?: boolean;
  consistencyEnforcement?: boolean;
  [key: string]: any;
}

/**
 * NovaVision class for universal UI connector
 */
export class NovaVision {
  private options: NovaVisionOptions;
  public formBuilder: FormBuilder;
  public dashboardBuilder: DashboardBuilder;
  public reportBuilder: ReportBuilder;
  public uiSchemaService: UISchemaService;
  public renderingService: RenderingService;
  public regulationOrchestrator: RegulationOrchestrator;
  public uiOptimizer: UIOptimizer;
  public consistencyEnforcer: ConsistencyEnforcer;

  constructor(options: NovaVisionOptions = {}) {
    this.options = {
      theme: options.theme || 'default',
      responsive: options.responsive !== false,
      accessibilityLevel: options.accessibilityLevel || 'AA',
      regulationAware: options.regulationAware !== false,
      aiOptimization: options.aiOptimization !== false,
      consistencyEnforcement: options.consistencyEnforcement !== false,
      ...options
    };

    this.formBuilder = new FormBuilder(this.options);
    this.dashboardBuilder = new DashboardBuilder(this.options);
    this.reportBuilder = new ReportBuilder(this.options);
    this.uiSchemaService = new UISchemaService();
    this.renderingService = new RenderingService();
    this.regulationOrchestrator = new RegulationOrchestrator();
    this.uiOptimizer = new UIOptimizer();
    this.consistencyEnforcer = new ConsistencyEnforcer();

    logger.info('NovaVision initialized', {
      theme: this.options.theme,
      responsive: this.options.responsive,
      accessibilityLevel: this.options.accessibilityLevel,
      regulationAware: this.options.regulationAware,
      aiOptimization: this.options.aiOptimization,
      consistencyEnforcement: this.options.consistencyEnforcement
    });
  }

  /**
   * Generate form schema
   * 
   * @param formConfig - Form configuration
   * @returns Form schema
   */
  public generateFormSchema(formConfig: any): any {
    logger.info('Generating form schema', { formId: formConfig.id });
    
    try {
      return this.formBuilder.buildFormSchema(formConfig);
    } catch (error: any) {
      logger.error('Error generating form schema', {
        formId: formConfig.id,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Generate dashboard schema
   * 
   * @param dashboardConfig - Dashboard configuration
   * @returns Dashboard schema
   */
  public generateDashboardSchema(dashboardConfig: any): any {
    logger.info('Generating dashboard schema', { dashboardId: dashboardConfig.id });
    
    try {
      return this.dashboardBuilder.buildDashboardSchema(dashboardConfig);
    } catch (error: any) {
      logger.error('Error generating dashboard schema', {
        dashboardId: dashboardConfig.id,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Generate report schema
   * 
   * @param reportConfig - Report configuration
   * @returns Report schema
   */
  public generateReportSchema(reportConfig: any): any {
    logger.info('Generating report schema', { reportId: reportConfig.id });
    
    try {
      return this.reportBuilder.buildReportSchema(reportConfig);
    } catch (error: any) {
      logger.error('Error generating report schema', {
        reportId: reportConfig.id,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Generate schema from API response
   * 
   * @param apiResponse - API response
   * @param schemaType - Schema type (form, dashboard, report)
   * @returns UI schema
   */
  public generateSchemaFromApiResponse(apiResponse: any, schemaType: string = 'form'): any {
    logger.info('Generating schema from API response', { schemaType });
    
    try {
      return this.uiSchemaService.generateSchemaFromApiResponse(apiResponse, schemaType);
    } catch (error: any) {
      logger.error('Error generating schema from API response', {
        schemaType,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Render UI from schema
   * 
   * @param schema - UI schema
   * @param data - Data to render
   * @param options - Rendering options
   * @returns Rendered UI
   */
  public renderUiFromSchema(schema: any, data: any = {}, options: any = {}): any {
    logger.info('Rendering UI from schema', { 
      schemaType: schema.type,
      schemaId: schema.id
    });
    
    try {
      return this.renderingService.renderUiFromSchema(schema, data, {
        ...this.options,
        ...options
      });
    } catch (error: any) {
      logger.error('Error rendering UI from schema', {
        schemaType: schema.type,
        schemaId: schema.id,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Validate UI schema
   * 
   * @param schema - UI schema to validate
   * @returns Validation result
   */
  public validateSchema(schema: any): any {
    logger.debug('Validating schema', { schemaType: schema.type });
    
    try {
      return this.uiSchemaService.validateSchema(schema);
    } catch (error: any) {
      logger.error('Error validating schema', {
        schemaType: schema.type,
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Handle jurisdiction change
   * 
   * @param userId - User ID
   * @param sessionId - Session ID
   * @param newJurisdiction - New jurisdiction
   * @returns Result of the jurisdiction change
   */
  public async handleJurisdictionChange(userId: string, sessionId: string, newJurisdiction: string): Promise<any> {
    logger.info('Handling jurisdiction change', { userId, sessionId, newJurisdiction });
    
    if (!this.options.regulationAware) {
      logger.warn('Regulation awareness is disabled');
      return { status: 'REGULATION_AWARENESS_DISABLED' };
    }
    
    try {
      return await this.regulationOrchestrator.handleJurisdictionChange(userId, sessionId, newJurisdiction);
    } catch (error: any) {
      logger.error('Error handling jurisdiction change', {
        userId,
        sessionId,
        newJurisdiction,
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Optimize UI layout
   * 
   * @param userId - User ID
   * @param userBehaviorStream - Stream of user behavior data
   * @returns Optimization result
   */
  public async optimizeLayout(userId: string, userBehaviorStream: any[]): Promise<any> {
    logger.info('Optimizing UI layout', { userId });
    
    if (!this.options.aiOptimization) {
      logger.warn('AI optimization is disabled');
      return { status: 'AI_OPTIMIZATION_DISABLED' };
    }
    
    try {
      return await this.uiOptimizer.optimizeLayout(userId, userBehaviorStream);
    } catch (error: any) {
      logger.error('Error optimizing layout', {
        userId,
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Validate UI consistency
   * 
   * @param components - UI components
   * @param profileName - Regulatory profile name
   * @returns Validation result
   */
  public async validateConsistency(components: any[], profileName: string): Promise<any> {
    logger.info('Validating UI consistency', { componentCount: components.length, profileName });
    
    if (!this.options.consistencyEnforcement) {
      logger.warn('Consistency enforcement is disabled');
      return { status: 'CONSISTENCY_ENFORCEMENT_DISABLED' };
    }
    
    try {
      return await this.consistencyEnforcer.validateConsistency(components, profileName);
    } catch (error: any) {
      logger.error('Error validating consistency', {
        profileName,
        error: error.message
      });
      throw error;
    }
  }
  
  /**
   * Fix UI consistency issues
   * 
   * @param components - UI components
   * @param validationResult - Validation result
   * @returns Fixed components and results
   */
  public async fixConsistencyIssues(components: any[], validationResult: any): Promise<any> {
    logger.info('Fixing UI consistency issues', { 
      componentCount: components.length,
      profileName: validationResult.profileName
    });
    
    if (!this.options.consistencyEnforcement) {
      logger.warn('Consistency enforcement is disabled');
      return { status: 'CONSISTENCY_ENFORCEMENT_DISABLED' };
    }
    
    try {
      return await this.consistencyEnforcer.fixConsistencyIssues(components, validationResult);
    } catch (error: any) {
      logger.error('Error fixing consistency issues', {
        profileName: validationResult.profileName,
        error: error.message
      });
      throw error;
    }
  }
}

// Create singleton instance
export const novaVision = new NovaVision();
