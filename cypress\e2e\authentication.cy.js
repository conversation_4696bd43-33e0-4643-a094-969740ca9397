describe('Authentication Flow', () => {
  beforeEach(() => {
    // Clear cookies and local storage before each test
    cy.clearCookies();
    cy.clearLocalStorage();
  });

  it('should allow user registration', () => {
    // Visit the sign-up page
    cy.visit('/signup');
    
    // Fill out the registration form
    cy.get('input[name="firstName"]').type('Test');
    cy.get('input[name="lastName"]').type('User');
    cy.get('input[name="email"]').type(`test.user.${Date.now()}@example.com`);
    cy.get('input[name="password"]').type('Password123!');
    cy.get('input[name="confirmPassword"]').type('Password123!');
    cy.get('input[name="company"]').type('Test Company');
    
    // Submit the form
    cy.get('form').submit();
    
    // Check that registration was successful
    cy.url().should('include', '/dashboard');
    cy.contains('Welcome, Test').should('be.visible');
  });

  it('should allow user login', () => {
    // Visit the sign-in page
    cy.visit('/signin');
    
    // Fill out the login form
    cy.get('input[name="email"]').type('<EMAIL>');
    cy.get('input[name="password"]').type('Password123!');
    
    // Submit the form
    cy.get('form').submit();
    
    // Check that login was successful
    cy.url().should('include', '/dashboard');
    cy.contains('Welcome back').should('be.visible');
  });

  it('should show error for invalid credentials', () => {
    // Visit the sign-in page
    cy.visit('/signin');
    
    // Fill out the login form with invalid credentials
    cy.get('input[name="email"]').type('<EMAIL>');
    cy.get('input[name="password"]').type('WrongPassword123!');
    
    // Submit the form
    cy.get('form').submit();
    
    // Check that error message is displayed
    cy.contains('Invalid email or password').should('be.visible');
    cy.url().should('include', '/signin');
  });

  it('should allow user logout', () => {
    // Login first
    cy.visit('/signin');
    cy.get('input[name="email"]').type('<EMAIL>');
    cy.get('input[name="password"]').type('Password123!');
    cy.get('form').submit();
    
    // Wait for login to complete
    cy.url().should('include', '/dashboard');
    
    // Click the logout button
    cy.get('[data-testid="user-menu"]').click();
    cy.contains('Logout').click();
    
    // Check that logout was successful
    cy.url().should('eq', Cypress.config().baseUrl + '/');
    cy.contains('Sign In').should('be.visible');
  });

  it('should redirect unauthenticated users from protected routes', () => {
    // Try to access a protected route
    cy.visit('/dashboard');
    
    // Check that user is redirected to login
    cy.url().should('include', '/signin');
    cy.contains('Please sign in to access this page').should('be.visible');
  });
});
