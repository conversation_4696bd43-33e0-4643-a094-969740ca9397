{"numFailedTestSuites": 1, "numFailedTests": 10, "numPassedTestSuites": 0, "numPassedTests": 1, "numPendingTestSuites": 0, "numPendingTests": 0, "numRuntimeErrorTestSuites": 0, "numTodoTests": 0, "numTotalTestSuites": 1, "numTotalTests": 11, "openHandles": [], "snapshot": {"added": 0, "didUpdate": false, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0}, "startTime": 1747790313545, "success": false, "testResults": [{"leaks": false, "numFailingTests": 10, "numPassingTests": 1, "numPendingTests": 0, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 1747790355486, "runtime": 27500, "slow": true, "start": 1747790327986}, "skipped": false, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testFilePath": "D:\\novafuse-api-superstore\\nova-connect\\tests\\unit\\services\\RateLimitService.test.js", "testResults": [{"ancestorTitles": ["RateLimitService", "constructor"], "duration": 7, "failureDetails": [{"matcherResult": {"expected": "D:\\novafuse-api-superstore\\nova-connect\\tests\\unit\\services\\test-data\\rate_limit_config.json", "message": "\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m\"D:\\\\novafuse-api-superstore\\\\nova-connect\\\\tests\\\\unit\\\\services\\\\test-data\\\\rate_limit_config.json\"\u001b[39m\nReceived: \u001b[31mundefined\u001b[39m", "name": "toBe", "pass": false}}], "failureMessages": ["Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m\"D:\\\\novafuse-api-superstore\\\\nova-connect\\\\tests\\\\unit\\\\services\\\\test-data\\\\rate_limit_config.json\"\u001b[39m\nReceived: \u001b[31mundefined\u001b[39m\n    at Object.toBe (D:\\novafuse-api-superstore\\nova-connect\\tests\\unit\\services\\RateLimitService.test.js:33:43)\n    at Promise.then.completed (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (D:\\novafuse-api-superstore\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (D:\\novafuse-api-superstore\\node_modules\\jest-runner\\build\\runTest.js:444:34)"], "fullName": "RateLimitService constructor should initialize with the correct data directory", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "failed", "title": "should initialize with the correct data directory"}, {"ancestorTitles": ["RateLimitService", "constructor"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "RateLimitService constructor should call ensureDataDir", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should call ensureDataDir"}, {"ancestorTitles": ["RateLimitService", "loadConfig"], "duration": 1, "failureDetails": [{}], "failureMessages": ["TypeError: rateLimitService.loadConfig is not a function\n    at Object.loadConfig (D:\\novafuse-api-superstore\\nova-connect\\tests\\unit\\services\\RateLimitService.test.js:53:45)\n    at Promise.then.completed (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (D:\\novafuse-api-superstore\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (D:\\novafuse-api-superstore\\node_modules\\jest-runner\\build\\runTest.js:444:34)"], "fullName": "RateLimitService loadConfig should load configuration from file", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should load configuration from file"}, {"ancestorTitles": ["RateLimitService", "loadConfig"], "duration": 0, "failureDetails": [{}], "failureMessages": ["TypeError: rateLimitService.loadConfig is not a function\n    at Object.loadConfig (D:\\novafuse-api-superstore\\nova-connect\\tests\\unit\\services\\RateLimitService.test.js:64:45)\n    at Promise.then.completed (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (D:\\novafuse-api-superstore\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (D:\\novafuse-api-superstore\\node_modules\\jest-runner\\build\\runTest.js:444:34)"], "fullName": "RateLimitService loadConfig should return default config if file does not exist", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should return default config if file does not exist"}, {"ancestorTitles": ["RateLimitService", "loadConfig"], "duration": 1, "failureDetails": [{}], "failureMessages": ["TypeError: rateLimitService.loadConfig is not a function\n    at Object.loadConfig (D:\\novafuse-api-superstore\\nova-connect\\tests\\unit\\services\\RateLimitService.test.js:73:37)\n    at Promise.then.completed (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (D:\\novafuse-api-superstore\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (D:\\novafuse-api-superstore\\node_modules\\jest-runner\\build\\runTest.js:444:34)"], "fullName": "RateLimitService loadConfig should throw error if file read fails for other reasons", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should throw error if file read fails for other reasons"}, {"ancestorTitles": ["RateLimitService", "saveConfig"], "duration": 0, "failureDetails": [{}], "failureMessages": ["TypeError: rateLimitService.saveConfig is not a function\n    at Object.saveConfig (D:\\novafuse-api-superstore\\nova-connect\\tests\\unit\\services\\RateLimitService.test.js:87:30)\n    at Promise.then.completed (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (D:\\novafuse-api-superstore\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (D:\\novafuse-api-superstore\\node_modules\\jest-runner\\build\\runTest.js:444:34)"], "fullName": "RateLimitService saveConfig should save configuration to file", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should save configuration to file"}, {"ancestorTitles": ["RateLimitService", "saveConfig"], "duration": 0, "failureDetails": [{}], "failureMessages": ["TypeError: rateLimitService.saveConfig is not a function\n    at Object.saveConfig (D:\\novafuse-api-superstore\\nova-connect\\tests\\unit\\services\\RateLimitService.test.js:99:37)\n    at Promise.then.completed (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (D:\\novafuse-api-superstore\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (D:\\novafuse-api-superstore\\node_modules\\jest-runner\\build\\runTest.js:444:34)"], "fullName": "RateLimitService saveConfig should throw error if file write fails", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should throw error if file write fails"}, {"ancestorTitles": ["RateLimitService", "createLimiter"], "duration": 1, "failureDetails": [{}], "failureMessages": ["TypeError: rateLimitService.createLimiter is not a function\n    at Object.createLimiter (D:\\novafuse-api-superstore\\nova-connect\\tests\\unit\\services\\RateLimitService.test.js:118:46)\n    at Promise.then.completed (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (D:\\novafuse-api-superstore\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (D:\\novafuse-api-superstore\\node_modules\\jest-runner\\build\\runTest.js:444:34)"], "fullName": "RateLimitService createLimiter should create a rate limiter with the correct configuration", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should create a rate limiter with the correct configuration"}, {"ancestorTitles": ["RateLimitService", "createLimiter"], "duration": 0, "failureDetails": [{}], "failureMessages": ["TypeError: rateLimitService.createLimiter is not a function\n    at Object.createLimiter (D:\\novafuse-api-superstore\\nova-connect\\tests\\unit\\services\\RateLimitService.test.js:134:46)\n    at Promise.then.completed (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (D:\\novafuse-api-superstore\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (D:\\novafuse-api-superstore\\node_modules\\jest-runner\\build\\runTest.js:444:34)"], "fullName": "RateLimitService createLimiter should return a pass-through middleware if rate limiting is disabled", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should return a pass-through middleware if rate limiting is disabled"}, {"ancestorTitles": ["RateLimitService", "updateConfig"], "duration": 1, "failureDetails": [{}], "failureMessages": ["TypeError: rateLimitService.updateConfig is not a function\n    at Object.updateConfig (D:\\novafuse-api-superstore\\nova-connect\\tests\\unit\\services\\RateLimitService.test.js:164:45)\n    at Promise.then.completed (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (D:\\novafuse-api-superstore\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (D:\\novafuse-api-superstore\\node_modules\\jest-runner\\build\\runTest.js:444:34)"], "fullName": "RateLimitService updateConfig should update the configuration for a specific type", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should update the configuration for a specific type"}, {"ancestorTitles": ["RateLimitService", "updateConfig"], "duration": 1, "failureDetails": [{}], "failureMessages": ["TypeError: rateLimitService.updateConfig is not a function\n    at Object.updateConfig (D:\\novafuse-api-superstore\\nova-connect\\tests\\unit\\services\\RateLimitService.test.js:179:37)\n    at Promise.then.completed (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTest (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (D:\\novafuse-api-superstore\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (D:\\novafuse-api-superstore\\node_modules\\jest-runner\\build\\runTest.js:444:34)"], "fullName": "RateLimitService updateConfig should throw an error if the type does not exist", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should throw an error if the type does not exist"}], "failureMessage": "\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mRateLimitService › constructor › should initialize with the correct data directory\u001b[39m\u001b[22m\n\n    \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\n    Expected: \u001b[32m\"D:\\\\novafuse-api-superstore\\\\nova-connect\\\\tests\\\\unit\\\\services\\\\test-data\\\\rate_limit_config.json\"\u001b[39m\n    Received: \u001b[31mundefined\u001b[39m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 31 |\u001b[39m     it(\u001b[32m'should initialize with the correct data directory'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\u001b[22m\n\u001b[2m     \u001b[90m 32 |\u001b[39m       expect(rateLimitService\u001b[33m.\u001b[39mdataDir)\u001b[33m.\u001b[39mtoBe(testDataDir)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 33 |\u001b[39m       expect(rateLimitService\u001b[33m.\u001b[39mconfigFile)\u001b[33m.\u001b[39mtoBe(path\u001b[33m.\u001b[39mjoin(testDataDir\u001b[33m,\u001b[39m \u001b[32m'rate_limit_config.json'\u001b[39m))\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m    |\u001b[39m                                           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 34 |\u001b[39m     })\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 35 |\u001b[39m     \u001b[22m\n\u001b[2m     \u001b[90m 36 |\u001b[39m     it(\u001b[32m'should call ensureDataDir'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.toBe (\u001b[22m\u001b[2m\u001b[0m\u001b[36mnova-connect/tests/unit/services/RateLimitService.test.js\u001b[39m\u001b[0m\u001b[2m:33:43)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mRateLimitService › loadConfig › should load configuration from file\u001b[39m\u001b[22m\n\n    TypeError: rateLimitService.loadConfig is not a function\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 51 |\u001b[39m       fs\u001b[33m.\u001b[39mreadFile\u001b[33m.\u001b[39mmockResolvedValueOnce(\u001b[33mJSON\u001b[39m\u001b[33m.\u001b[39mstringify(mockConfig))\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 52 |\u001b[39m       \u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 53 |\u001b[39m       \u001b[36mconst\u001b[39m config \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m rateLimitService\u001b[33m.\u001b[39mloadConfig()\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m    |\u001b[39m                                             \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 54 |\u001b[39m       \u001b[22m\n\u001b[2m     \u001b[90m 55 |\u001b[39m       expect(fs\u001b[33m.\u001b[39mreadFile)\u001b[33m.\u001b[39mtoHaveBeenCalledWith(rateLimitService\u001b[33m.\u001b[39mconfigFile\u001b[33m,\u001b[39m \u001b[32m'utf8'\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 56 |\u001b[39m       expect(config)\u001b[33m.\u001b[39mtoEqual(mockConfig)\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.loadConfig (\u001b[22m\u001b[2m\u001b[0m\u001b[36mnova-connect/tests/unit/services/RateLimitService.test.js\u001b[39m\u001b[0m\u001b[2m:53:45)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mRateLimitService › loadConfig › should return default config if file does not exist\u001b[39m\u001b[22m\n\n    TypeError: rateLimitService.loadConfig is not a function\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 62 |\u001b[39m       fs\u001b[33m.\u001b[39mreadFile\u001b[33m.\u001b[39mmockRejectedValueOnce(error)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 63 |\u001b[39m       \u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 64 |\u001b[39m       \u001b[36mconst\u001b[39m config \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m rateLimitService\u001b[33m.\u001b[39mloadConfig()\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m    |\u001b[39m                                             \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 65 |\u001b[39m       \u001b[22m\n\u001b[2m     \u001b[90m 66 |\u001b[39m       expect(config)\u001b[33m.\u001b[39mtoEqual(rateLimitService\u001b[33m.\u001b[39mdefaultConfig)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 67 |\u001b[39m     })\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.loadConfig (\u001b[22m\u001b[2m\u001b[0m\u001b[36mnova-connect/tests/unit/services/RateLimitService.test.js\u001b[39m\u001b[0m\u001b[2m:64:45)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mRateLimitService › loadConfig › should throw error if file read fails for other reasons\u001b[39m\u001b[22m\n\n    TypeError: rateLimitService.loadConfig is not a function\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 71 |\u001b[39m       fs\u001b[33m.\u001b[39mreadFile\u001b[33m.\u001b[39mmockRejectedValueOnce(error)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 72 |\u001b[39m       \u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 73 |\u001b[39m       \u001b[36mawait\u001b[39m expect(rateLimitService\u001b[33m.\u001b[39mloadConfig())\u001b[33m.\u001b[39mrejects\u001b[33m.\u001b[39mtoThrow(\u001b[32m'Permission denied'\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m    |\u001b[39m                                     \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 74 |\u001b[39m     })\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 75 |\u001b[39m   })\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 76 |\u001b[39m   \u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.loadConfig (\u001b[22m\u001b[2m\u001b[0m\u001b[36mnova-connect/tests/unit/services/RateLimitService.test.js\u001b[39m\u001b[0m\u001b[2m:73:37)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mRateLimitService › saveConfig › should save configuration to file\u001b[39m\u001b[22m\n\n    TypeError: rateLimitService.saveConfig is not a function\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 85 |\u001b[39m       }\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 86 |\u001b[39m       \u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 87 |\u001b[39m       \u001b[36mawait\u001b[39m rateLimitService\u001b[33m.\u001b[39msaveConfig(config)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m    |\u001b[39m                              \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 88 |\u001b[39m       \u001b[22m\n\u001b[2m     \u001b[90m 89 |\u001b[39m       expect(fs\u001b[33m.\u001b[39mwriteFile)\u001b[33m.\u001b[39mtoHaveBeenCalledWith(\u001b[22m\n\u001b[2m     \u001b[90m 90 |\u001b[39m         rateLimitService\u001b[33m.\u001b[39mconfigFile\u001b[33m,\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.saveConfig (\u001b[22m\u001b[2m\u001b[0m\u001b[36mnova-connect/tests/unit/services/RateLimitService.test.js\u001b[39m\u001b[0m\u001b[2m:87:30)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mRateLimitService › saveConfig › should throw error if file write fails\u001b[39m\u001b[22m\n\n    TypeError: rateLimitService.saveConfig is not a function\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m  97 |\u001b[39m       fs\u001b[33m.\u001b[39mwriteFile\u001b[33m.\u001b[39mmockRejectedValueOnce(error)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m  98 |\u001b[39m       \u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m  99 |\u001b[39m       \u001b[36mawait\u001b[39m expect(rateLimitService\u001b[33m.\u001b[39msaveConfig({}))\u001b[33m.\u001b[39mrejects\u001b[33m.\u001b[39mtoThrow(\u001b[32m'Permission denied'\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                                     \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 100 |\u001b[39m     })\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 101 |\u001b[39m   })\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 102 |\u001b[39m   \u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.saveConfig (\u001b[22m\u001b[2m\u001b[0m\u001b[36mnova-connect/tests/unit/services/RateLimitService.test.js\u001b[39m\u001b[0m\u001b[2m:99:37)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mRateLimitService › createLimiter › should create a rate limiter with the correct configuration\u001b[39m\u001b[22m\n\n    TypeError: rateLimitService.createLimiter is not a function\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 116 |\u001b[39m       fs\u001b[33m.\u001b[39mreadFile\u001b[33m.\u001b[39mmockResolvedValueOnce(\u001b[33mJSON\u001b[39m\u001b[33m.\u001b[39mstringify(mockConfig))\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 117 |\u001b[39m       \u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 118 |\u001b[39m       \u001b[36mconst\u001b[39m limiter \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m rateLimitService\u001b[33m.\u001b[39mcreateLimiter(\u001b[32m'test'\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                                              \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 119 |\u001b[39m       \u001b[22m\n\u001b[2m     \u001b[90m 120 |\u001b[39m       \u001b[90m// Since we can't directly test the express-rate-limit instance,\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 121 |\u001b[39m       \u001b[90m// we'll just verify that a function was returned\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.createLimiter (\u001b[22m\u001b[2m\u001b[0m\u001b[36mnova-connect/tests/unit/services/RateLimitService.test.js\u001b[39m\u001b[0m\u001b[2m:118:46)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mRateLimitService › createLimiter › should return a pass-through middleware if rate limiting is disabled\u001b[39m\u001b[22m\n\n    TypeError: rateLimitService.createLimiter is not a function\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 132 |\u001b[39m       fs\u001b[33m.\u001b[39mreadFile\u001b[33m.\u001b[39mmockResolvedValueOnce(\u001b[33mJSON\u001b[39m\u001b[33m.\u001b[39mstringify(mockConfig))\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 133 |\u001b[39m       \u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 134 |\u001b[39m       \u001b[36mconst\u001b[39m limiter \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m rateLimitService\u001b[33m.\u001b[39mcreateLimiter(\u001b[32m'test'\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                                              \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 135 |\u001b[39m       \u001b[22m\n\u001b[2m     \u001b[90m 136 |\u001b[39m       \u001b[90m// Test that it's a pass-through middleware\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 137 |\u001b[39m       \u001b[36mconst\u001b[39m req \u001b[33m=\u001b[39m {}\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.createLimiter (\u001b[22m\u001b[2m\u001b[0m\u001b[36mnova-connect/tests/unit/services/RateLimitService.test.js\u001b[39m\u001b[0m\u001b[2m:134:46)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mRateLimitService › updateConfig › should update the configuration for a specific type\u001b[39m\u001b[22m\n\n    TypeError: rateLimitService.updateConfig is not a function\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 162 |\u001b[39m       fs\u001b[33m.\u001b[39mreadFile\u001b[33m.\u001b[39mmockResolvedValueOnce(\u001b[33mJSON\u001b[39m\u001b[33m.\u001b[39mstringify(initialConfig))\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 163 |\u001b[39m       \u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 164 |\u001b[39m       \u001b[36mconst\u001b[39m result \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m rateLimitService\u001b[33m.\u001b[39mupdateConfig(\u001b[32m'test'\u001b[39m\u001b[33m,\u001b[39m updatedConfig)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                                             \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 165 |\u001b[39m       \u001b[22m\n\u001b[2m     \u001b[90m 166 |\u001b[39m       expect(fs\u001b[33m.\u001b[39mwriteFile)\u001b[33m.\u001b[39mtoHaveBeenCalled()\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 167 |\u001b[39m       expect(result)\u001b[33m.\u001b[39mtoEqual({\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.updateConfig (\u001b[22m\u001b[2m\u001b[0m\u001b[36mnova-connect/tests/unit/services/RateLimitService.test.js\u001b[39m\u001b[0m\u001b[2m:164:45)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mRateLimitService › updateConfig › should throw an error if the type does not exist\u001b[39m\u001b[22m\n\n    TypeError: rateLimitService.updateConfig is not a function\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 177 |\u001b[39m       fs\u001b[33m.\u001b[39mreadFile\u001b[33m.\u001b[39mmockResolvedValueOnce(\u001b[33mJSON\u001b[39m\u001b[33m.\u001b[39mstringify(initialConfig))\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 178 |\u001b[39m       \u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 179 |\u001b[39m       \u001b[36mawait\u001b[39m expect(rateLimitService\u001b[33m.\u001b[39mupdateConfig(\u001b[32m'nonexistent'\u001b[39m\u001b[33m,\u001b[39m {}))\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                                     \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 180 |\u001b[39m         \u001b[33m.\u001b[39mrejects\u001b[33m.\u001b[39mtoThrow(\u001b[32m'Rate limit type'\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 181 |\u001b[39m     })\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 182 |\u001b[39m   })\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.updateConfig (\u001b[22m\u001b[2m\u001b[0m\u001b[36mnova-connect/tests/unit/services/RateLimitService.test.js\u001b[39m\u001b[0m\u001b[2m:179:37)\u001b[22m\u001b[2m\u001b[22m\n"}], "wasInterrupted": false}