/**
 * Request Executor Service Tests
 *
 * This file contains unit tests for the Request Executor service.
 */

// Mock dependencies
jest.mock('axios');
jest.mock('../../services/integrationRegistry');
jest.mock('../../services/authenticationManager');
jest.mock('../../services/dataTransformer');

// Import mocked dependencies
const axios = require('axios');
const { getIntegrationById } = require('../../services/integrationRegistry');
const { generateAuthHeaders } = require('../../services/authenticationManager');
const { transformRequest, transformResponse } = require('../../services/dataTransformer');

// Import the service
const { executeRequest, executeBatchRequests } = require('../../services/requestExecutor');

describe('Request Executor Service', () => {
  // Mock data
  const mockIntegration = {
    _id: 'integration-123',
    name: 'Test Integration',
    type: 'crm',
    status: 'active',
    capabilities: ['getData', 'updateData'],
    handlers: {
      getData: {
        endpoint: 'https://api.example.com/data',
        method: 'GET'
      },
      updateData: {
        endpoint: 'https://api.example.com/data',
        method: 'POST'
      }
    },
    metrics: {
      totalRequests: 100,
      successfulRequests: 90,
      failedRequests: 10,
      averageResponseTime: 200
    },
    save: jest.fn().mockResolvedValue(true)
  };

  const mockAuthHeaders = {
    'Authorization': 'Bearer token123',
    'Content-Type': 'application/json'
  };

  const mockRequestData = {
    id: '123',
    name: 'Test Data'
  };

  const mockTransformedRequestData = {
    id: '123',
    name: 'Test Data',
    format: 'transformed'
  };

  const mockResponseData = {
    id: '123',
    name: 'Test Data',
    status: 'success'
  };

  const mockTransformedResponseData = {
    id: '123',
    name: 'Test Data',
    status: 'success',
    format: 'transformed'
  };

  const mockAxiosResponse = {
    data: mockResponseData,
    status: 200,
    headers: {
      'content-type': 'application/json'
    }
  };

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Setup default mock implementations
    getIntegrationById.mockResolvedValue(mockIntegration);
    generateAuthHeaders.mockResolvedValue(mockAuthHeaders);
    transformRequest.mockResolvedValue(mockTransformedRequestData);
    transformResponse.mockResolvedValue(mockTransformedResponseData);
    axios.mockResolvedValue(mockAxiosResponse);

    // Mock Date.now for consistent response times
    jest.spyOn(Date, 'now')
      .mockReturnValueOnce(1000) // Start time
      .mockReturnValueOnce(1200); // End time (200ms later)
  });

  afterEach(() => {
    // Restore Date.now
    jest.restoreAllMocks();
  });

  describe('executeRequest', () => {
    it('should execute a request successfully', async () => {
      // Call the service method
      const result = await executeRequest('integration-123', 'getData', mockRequestData);

      // Verify dependencies were called correctly
      expect(getIntegrationById).toHaveBeenCalledWith('integration-123');
      expect(generateAuthHeaders).toHaveBeenCalledWith(mockIntegration);
      expect(transformRequest).toHaveBeenCalledWith(mockRequestData, 'crm', 'getData');
      expect(axios).toHaveBeenCalledWith({
        url: 'https://api.example.com/data',
        method: 'GET',
        data: undefined,
        params: mockTransformedRequestData,
        headers: mockAuthHeaders
      });
      expect(transformResponse).toHaveBeenCalledWith({
        data: mockResponseData,
        status: 200,
        headers: { 'content-type': 'application/json' },
        responseTime: 200
      }, 'crm', 'getData');

      // Verify integration metrics were updated
      expect(mockIntegration.metrics.totalRequests).toBe(101);
      expect(mockIntegration.metrics.successfulRequests).toBe(91);
      expect(mockIntegration.save).toHaveBeenCalled();

      // Verify the result
      expect(result).toEqual(mockTransformedResponseData);
    });

    it('should execute a POST request successfully', async () => {
      // Call the service method
      const result = await executeRequest('integration-123', 'updateData', mockRequestData);

      // Verify axios was called with the correct method and data
      expect(axios).toHaveBeenCalledWith({
        url: 'https://api.example.com/data',
        method: 'POST',
        data: mockTransformedRequestData,
        params: undefined,
        headers: mockAuthHeaders
      });

      // Verify the result
      expect(result).toEqual(mockTransformedResponseData);
    });

    it('should throw an error if the integration does not support the action', async () => {
      // Call the service method and expect it to throw
      await expect(executeRequest('integration-123', 'unsupportedAction', mockRequestData))
        .rejects
        .toThrow("Integration does not support action 'unsupportedAction'");

      // Verify dependencies were called correctly
      expect(getIntegrationById).toHaveBeenCalledWith('integration-123');
      expect(generateAuthHeaders).not.toHaveBeenCalled();
      expect(transformRequest).not.toHaveBeenCalled();
      expect(axios).not.toHaveBeenCalled();
    });

    it('should throw an error if the integration is not active', async () => {
      // Setup inactive integration
      const inactiveIntegration = {
        ...mockIntegration,
        status: 'inactive'
      };
      getIntegrationById.mockResolvedValueOnce(inactiveIntegration);

      // Call the service method and expect it to throw
      await expect(executeRequest('integration-123', 'getData', mockRequestData))
        .rejects
        .toThrow('Integration is not active');

      // Verify dependencies were called correctly
      expect(getIntegrationById).toHaveBeenCalledWith('integration-123');
      expect(generateAuthHeaders).not.toHaveBeenCalled();
      expect(transformRequest).not.toHaveBeenCalled();
      expect(axios).not.toHaveBeenCalled();
    });

    it('should throw an error if no handler is configured for the action', async () => {
      // Setup integration with missing handler
      const integrationWithMissingHandler = {
        ...mockIntegration,
        handlers: {
          getData: null
        }
      };
      getIntegrationById.mockResolvedValueOnce(integrationWithMissingHandler);

      // Call the service method and expect it to throw
      await expect(executeRequest('integration-123', 'getData', mockRequestData))
        .rejects
        .toThrow("No handler configured for action 'getData'");

      // Verify dependencies were called correctly
      expect(getIntegrationById).toHaveBeenCalledWith('integration-123');
      expect(generateAuthHeaders).not.toHaveBeenCalled();
      expect(transformRequest).not.toHaveBeenCalled();
      expect(axios).not.toHaveBeenCalled();
    });

    it('should handle HTTP request errors', async () => {
      // Setup axios to throw an error
      const axiosError = new Error('Request failed');
      axiosError.response = {
        status: 400,
        data: { error: 'Bad request' }
      };
      axios.mockRejectedValueOnce(axiosError);

      // Call the service method and expect it to throw
      await expect(executeRequest('integration-123', 'getData', mockRequestData))
        .rejects
        .toMatchObject({
          name: 'IntegrationError',
          status: 400,
          data: { error: 'Bad request' }
        });

      // Verify dependencies were called correctly
      expect(getIntegrationById).toHaveBeenCalledWith('integration-123');
      expect(generateAuthHeaders).toHaveBeenCalledWith(mockIntegration);
      expect(transformRequest).toHaveBeenCalledWith(mockRequestData, 'crm', 'getData');
      expect(axios).toHaveBeenCalled();
      expect(transformResponse).not.toHaveBeenCalled();
    });

    it('should handle errors from dependencies', async () => {
      // Setup transformRequest to throw an error
      const transformError = new Error('Transform error');
      transformRequest.mockRejectedValueOnce(transformError);

      // Call the service method and expect it to throw
      await expect(executeRequest('integration-123', 'getData', mockRequestData))
        .rejects
        .toThrow('Transform error');

      // Verify dependencies were called correctly
      expect(getIntegrationById).toHaveBeenCalledWith('integration-123');
      expect(generateAuthHeaders).toHaveBeenCalledWith(mockIntegration);
      expect(transformRequest).toHaveBeenCalledWith(mockRequestData, 'crm', 'getData');
      expect(axios).not.toHaveBeenCalled();
      expect(transformResponse).not.toHaveBeenCalled();
    });
  });

  describe('executeBatchRequests', () => {
    it('should execute multiple requests successfully', async () => {
      // Setup batch requests
      const batchRequests = [
        {
          integrationId: 'integration-123',
          action: 'getData',
          data: { id: '123' }
        },
        {
          integrationId: 'integration-123',
          action: 'updateData',
          data: { id: '123', name: 'Updated' }
        }
      ];

      // Call the service method
      const results = await executeBatchRequests(batchRequests);

      // Verify executeRequest was called for each request
      expect(getIntegrationById).toHaveBeenCalledTimes(2);
      expect(transformRequest).toHaveBeenCalledTimes(2);
      expect(axios).toHaveBeenCalledTimes(2);
      expect(transformResponse).toHaveBeenCalledTimes(2);

      // Verify the results
      expect(results).toEqual([
        {
          integrationId: 'integration-123',
          action: 'getData',
          success: true,
          data: mockTransformedResponseData
        },
        {
          integrationId: 'integration-123',
          action: 'updateData',
          success: true,
          data: mockTransformedResponseData
        }
      ]);
    });

    it('should handle failed requests in a batch', async () => {
      // Setup batch requests
      const batchRequests = [
        {
          integrationId: 'integration-123',
          action: 'getData',
          data: { id: '123' }
        },
        {
          integrationId: 'integration-123',
          action: 'unsupportedAction',
          data: { id: '123' }
        }
      ];

      // Call the service method
      const results = await executeBatchRequests(batchRequests);

      // Verify the results
      expect(results[0].success).toBe(true);
      expect(results[1].success).toBe(false);
      expect(results[1].error).toEqual({
        message: "Integration does not support action 'unsupportedAction'",
        name: 'ValidationError',
        status: undefined
      });
    });

    it('should return an empty array for an empty batch', async () => {
      // Call the service method with an empty array
      const results = await executeBatchRequests([]);

      // Verify the results
      expect(results).toEqual([]);
      expect(getIntegrationById).not.toHaveBeenCalled();
      expect(transformRequest).not.toHaveBeenCalled();
      expect(axios).not.toHaveBeenCalled();
      expect(transformResponse).not.toHaveBeenCalled();
    });
  });
});
