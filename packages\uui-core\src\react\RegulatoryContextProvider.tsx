import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Logger } from '../utils/Logger';

// Create logger
const logger = new Logger('regulatory-context');

// Define the context type
interface RegulatoryContextType {
  activeRegulations: string[];
  jurisdiction: string;
  userRole: string;
  loading: boolean;
  error: string | null;
}

// Create the context with a default value
const RegulatoryContext = createContext<RegulatoryContextType | undefined>(undefined);

// Define the provider props
interface RegulatoryContextProviderProps {
  children: ReactNode;
  jurisdiction: string;
  userRole: string;
}

/**
 * RegulatoryContextProvider - Provides regulatory context for UI rendering
 * 
 * This component provides regulatory context information to all child components,
 * allowing the UI to adapt based on regulatory requirements.
 */
export const RegulatoryContextProvider: React.FC<RegulatoryContextProviderProps> = ({ 
  children, 
  jurisdiction, 
  userRole 
}) => {
  const [activeRegulations, setActiveRegulations] = useState<string[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  
  // Fetch active regulations based on jurisdiction
  useEffect(() => {
    const fetchRegulations = async () => {
      try {
        setLoading(true);
        
        // In a real implementation, this would fetch from an API
        // For now, we'll simulate the response
        const regulations = await simulateFetchRegulations(jurisdiction);
        
        setActiveRegulations(regulations);
        setLoading(false);
      } catch (err: any) {
        setError(err.message);
        setLoading(false);
      }
    };
    
    fetchRegulations();
  }, [jurisdiction]);
  
  // Create the context value
  const contextValue: RegulatoryContextType = {
    activeRegulations,
    jurisdiction,
    userRole,
    loading,
    error
  };
  
  return (
    <RegulatoryContext.Provider value={contextValue}>
      {children}
    </RegulatoryContext.Provider>
  );
};

/**
 * Simulate fetching regulations
 * 
 * @param jurisdiction - Jurisdiction
 * @returns Simulated regulations
 */
const simulateFetchRegulations = async (jurisdiction: string): Promise<string[]> => {
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 300));
  
  // Return regulations based on jurisdiction
  switch (jurisdiction.toLowerCase()) {
    case 'eu':
      return ['GDPR', 'ISO27001'];
    case 'us-healthcare':
      return ['HIPAA', 'ISO27001'];
    case 'us-finance':
      return ['PCI_DSS', 'SOX'];
    case 'us-general':
      return ['CCPA'];
    case 'global':
      return ['GDPR', 'CCPA', 'ISO27001'];
    default:
      return ['ISO27001']; // Default fallback
  }
};

/**
 * useRegulatoryContext - Hook to access the regulatory context
 * 
 * @returns Regulatory context value
 */
export const useRegulatoryContext = (): RegulatoryContextType => {
  const context = useContext(RegulatoryContext);
  if (context === undefined) {
    throw new Error('useRegulatoryContext must be used within a RegulatoryContextProvider');
  }
  return context;
};

export default RegulatoryContextProvider;
