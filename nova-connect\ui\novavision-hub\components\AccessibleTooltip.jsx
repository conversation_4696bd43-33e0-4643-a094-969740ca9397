/**
 * AccessibleTooltip Component
 * 
 * An accessible tooltip component that follows WCAG 2.1 guidelines.
 */

import React, { useState, useRef, useEffect } from 'react';
import PropTypes from 'prop-types';
import { generateAccessibilityId } from '../utils/accessibility';

/**
 * AccessibleTooltip component
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Trigger element
 * @param {string|React.ReactNode} props.content - Tooltip content
 * @param {string} [props.position='top'] - Tooltip position (top, right, bottom, left)
 * @param {number} [props.delay=300] - Delay before showing tooltip (in ms)
 * @param {boolean} [props.arrow=true] - Whether to show an arrow
 * @param {string} [props.className] - Additional CSS class names for the tooltip
 * @param {string} [props.triggerClassName] - Additional CSS class names for the trigger
 * @param {Object} [props.style] - Additional inline styles for the tooltip
 * @returns {React.ReactElement} AccessibleTooltip component
 */
const AccessibleTooltip = ({
  children,
  content,
  position = 'top',
  delay = 300,
  arrow = true,
  className = '',
  triggerClassName = '',
  style = {}
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const tooltipRef = useRef(null);
  const triggerRef = useRef(null);
  const timeoutRef = useRef(null);
  
  // Generate unique IDs for accessibility
  const tooltipId = generateAccessibilityId('tooltip');
  
  // Show tooltip
  const showTooltip = () => {
    clearTimeout(timeoutRef.current);
    timeoutRef.current = setTimeout(() => {
      setIsVisible(true);
    }, delay);
  };
  
  // Hide tooltip
  const hideTooltip = () => {
    clearTimeout(timeoutRef.current);
    if (!isFocused) {
      setIsVisible(false);
    }
  };
  
  // Handle focus
  const handleFocus = () => {
    setIsFocused(true);
    showTooltip();
  };
  
  // Handle blur
  const handleBlur = () => {
    setIsFocused(false);
    hideTooltip();
  };
  
  // Handle escape key
  const handleKeyDown = (event) => {
    if (event.key === 'Escape' && isVisible) {
      setIsVisible(false);
    }
  };
  
  // Clean up timeout on unmount
  useEffect(() => {
    return () => {
      clearTimeout(timeoutRef.current);
    };
  }, []);
  
  // Position tooltip
  const getTooltipPosition = () => {
    if (!triggerRef.current || !tooltipRef.current) {
      return {};
    }
    
    const triggerRect = triggerRef.current.getBoundingClientRect();
    const tooltipRect = tooltipRef.current.getBoundingClientRect();
    
    const positions = {
      top: {
        top: `${-tooltipRect.height - 10}px`,
        left: `${(triggerRect.width - tooltipRect.width) / 2}px`
      },
      right: {
        top: `${(triggerRect.height - tooltipRect.height) / 2}px`,
        left: `${triggerRect.width + 10}px`
      },
      bottom: {
        top: `${triggerRect.height + 10}px`,
        left: `${(triggerRect.width - tooltipRect.width) / 2}px`
      },
      left: {
        top: `${(triggerRect.height - tooltipRect.height) / 2}px`,
        left: `${-tooltipRect.width - 10}px`
      }
    };
    
    return positions[position] || positions.top;
  };
  
  // Get arrow class
  const getArrowClass = () => {
    const arrowClasses = {
      top: 'bottom-full left-1/2 transform -translate-x-1/2 border-t-gray-800 border-l-transparent border-r-transparent',
      right: 'left-full top-1/2 transform -translate-y-1/2 border-r-gray-800 border-t-transparent border-b-transparent',
      bottom: 'top-full left-1/2 transform -translate-x-1/2 border-b-gray-800 border-l-transparent border-r-transparent',
      left: 'right-full top-1/2 transform -translate-y-1/2 border-l-gray-800 border-t-transparent border-b-transparent'
    };
    
    return arrowClasses[position] || arrowClasses.top;
  };
  
  return (
    <div
      className={`inline-block relative ${triggerClassName}`}
      onMouseEnter={showTooltip}
      onMouseLeave={hideTooltip}
      onFocus={handleFocus}
      onBlur={handleBlur}
      onKeyDown={handleKeyDown}
      ref={triggerRef}
      data-testid="tooltip-container"
    >
      {/* Trigger element */}
      <div
        aria-describedby={isVisible ? tooltipId : undefined}
        className="inline-block"
      >
        {children}
      </div>
      
      {/* Tooltip */}
      {isVisible && (
        <div
          id={tooltipId}
          role="tooltip"
          ref={tooltipRef}
          className={`
            absolute z-50 px-3 py-2 text-sm text-white bg-gray-800 rounded shadow-lg
            max-w-xs whitespace-normal break-words
            ${className}
          `}
          style={{
            ...getTooltipPosition(),
            ...style
          }}
          data-testid="tooltip"
        >
          {content}
          
          {/* Arrow */}
          {arrow && (
            <div
              className={`
                absolute w-0 h-0
                border-solid border-4
                ${getArrowClass()}
              `}
              data-testid="tooltip-arrow"
            />
          )}
        </div>
      )}
    </div>
  );
};

AccessibleTooltip.propTypes = {
  children: PropTypes.node.isRequired,
  content: PropTypes.oneOfType([PropTypes.string, PropTypes.node]).isRequired,
  position: PropTypes.oneOf(['top', 'right', 'bottom', 'left']),
  delay: PropTypes.number,
  arrow: PropTypes.bool,
  className: PropTypes.string,
  triggerClassName: PropTypes.string,
  style: PropTypes.object
};

export default AccessibleTooltip;
