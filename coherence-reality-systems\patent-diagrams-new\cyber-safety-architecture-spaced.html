<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cyber-Safety Protocol Architecture</title>
    <style>
        /* Reset and Base Styles */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            line-height: 1.4;
        }

        /* Main Diagram Container */
        .diagram-container {
            position: relative;
            width: 950px;
            min-height: 900px; /* Increased minimum height */
            margin: 0 auto 40px;
            background-color: white;
            border: 1px solid #ddd;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            box-sizing: border-box;
            overflow: visible; /* Allow content to be fully visible */
        }

        /* Container Box Styles */
        .container-box {
            position: absolute;
            border: 2px solid #666;
            border-radius: 10px;
            padding: 30px 10px 10px;
            overflow: visible; /* Allow content to be fully visible */
            min-height: 100px; /* Minimum height */
            min-width: 200px; /* Minimum width */
        }

        .container-label {
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            font-weight: bold;
            font-size: 16px;
            text-align: center;
            width: 90%; /* Ensures text doesn't overflow container */
            white-space: normal; /* Allows text to wrap */
            overflow: visible; /* Allow content to be fully visible */
            z-index: 10; /* Ensure labels are in front of connecting lines */
            background-color: white; /* Add background to hide lines behind text */
            padding: 0 5px; /* Add padding around text */
        }

        /* Component Box Styles */
        .component-box {
            position: absolute;
            background-color: white;
            border: 2px solid #333;
            border-radius: 6px;
            display: flex;
            flex-direction: column;
            justify-content: center; /* Center content vertically */
            align-items: center;
            text-align: center;
            padding: 5px; /* Reduced padding */
            overflow: visible; /* Allow content to be fully visible */
            border-top-left-radius: 0; /* Flat corner for number */
            min-height: 50px; /* Minimum height */
            min-width: 100px; /* Minimum width */
        }

        /* Component Number Styles - Integrated into corner */
        .component-number-inside {
            position: absolute;
            top: 0;
            left: 0;
            width: 18px;
            height: 18px;
            background-color: #000000; /* Black background for component numbers */
            color: white;
            border-radius: 0 0 6px 0; /* Rounded on bottom-right corner only */
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 10px; /* Smaller font */
            font-weight: bold;
            z-index: 2;
            margin: 0; /* Remove any margin */
            padding: 0; /* Remove any padding */
        }

        .component-label {
            font-weight: bold;
            margin-top: 10px; /* Reduced margin */
            margin-bottom: 2px; /* Reduced margin */
            font-size: 12px; /* Smaller font */
            text-align: center;
            width: 100%;
            white-space: normal; /* Allows text to wrap */
            overflow: visible; /* Allow content to be fully visible */
        }

        .component-content {
            font-size: 10px; /* Smaller font */
            text-align: center;
            width: 100%;
            padding: 0;
            white-space: normal; /* Allows text to wrap */
            overflow: visible; /* Allow content to be fully visible */
            line-height: 1.2; /* Tighter line spacing */
        }

        /* Arrow Styles */
        .arrow {
            position: absolute;
            background-color: #555555; /* Grey color for patent compliance */
            width: 2px;
            z-index: 0;
        }

        /* Legend Styles */
        .legend {
            position: absolute;
            right: 10px;
            bottom: 30px;
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 8px;
            z-index: 10;
            width: 200px;
            font-size: 12px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 6px;
        }

        .legend-color {
            width: 15px;
            height: 15px;
            margin-right: 8px;
            border: 1px solid #333;
            flex-shrink: 0; /* Prevents the color box from shrinking */
        }

        /* Inventor Label */
        .inventor-label {
            position: absolute;
            left: 10px;
            bottom: 30px;
            font-size: 12px;
            font-style: italic;
            color: #333;
            z-index: 10;
        }

        /* Title Styles */
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 20px;
            color: #333;
        }
    </style>
</head>
<body>
    <h1>FIG. 1: Cyber-Safety Protocol Architecture</h1>

    <div class="diagram-container">
        <!-- Main Container -->
        <div class="container-box" style="width: 850px; height: 800px; left: 50px; top: 20px;">
            <div class="container-label">CYBER-SAFETY PROTOCOL ARCHITECTURE</div>
        </div>

        <!-- Core Protocol Layer -->
        <div class="container-box" style="width: 750px; height: 130px; left: 100px; top: 70px;">
            <div class="container-label">CYBER-SAFETY PROTOCOL CORE</div>
        </div>

        <div class="component-box" style="left: 150px; top: 110px; width: 140px; height: 70px;">
            <div class="component-number-inside">101</div>
            <div class="component-label">Native Unification</div>
            <div class="component-content">Engine</div>
        </div>

        <div class="component-box" style="left: 310px; top: 110px; width: 140px; height: 70px;">
            <div class="component-number-inside">102</div>
            <div class="component-label">Dynamic UI</div>
            <div class="component-content">Enforcement</div>
        </div>

        <div class="component-box" style="left: 470px; top: 110px; width: 140px; height: 70px;">
            <div class="component-number-inside">103</div>
            <div class="component-label">Cross-Domain</div>
            <div class="component-content">Intelligence</div>
        </div>

        <div class="component-box" style="left: 630px; top: 110px; width: 140px; height: 70px;">
            <div class="component-number-inside">104</div>
            <div class="component-label">Protocol</div>
            <div class="component-content">Orchestration</div>
        </div>

        <!-- Universal Components Layer -->
        <div class="container-box" style="width: 750px; height: 180px; left: 100px; top: 230px;">
            <div class="container-label">UNIVERSAL COMPONENTS</div>
        </div>

        <div class="component-box" style="left: 130px; top: 270px; width: 110px; height: 50px;">
            <div class="component-number-inside">105</div>
            <div class="component-label">NovaCore</div>
        </div>

        <div class="component-box" style="left: 250px; top: 270px; width: 110px; height: 50px;">
            <div class="component-number-inside">106</div>
            <div class="component-label">NovaShield</div>
        </div>

        <div class="component-box" style="left: 370px; top: 270px; width: 110px; height: 50px;">
            <div class="component-number-inside">107</div>
            <div class="component-label">NovaTrack</div>
        </div>

        <div class="component-box" style="left: 490px; top: 270px; width: 110px; height: 50px;">
            <div class="component-number-inside">108</div>
            <div class="component-label">NovaLearn</div>
        </div>

        <div class="component-box" style="left: 610px; top: 270px; width: 110px; height: 50px;">
            <div class="component-number-inside">109</div>
            <div class="component-label">NovaView</div>
        </div>

        <div class="component-box" style="left: 730px; top: 270px; width: 110px; height: 50px;">
            <div class="component-number-inside">110</div>
            <div class="component-label">NovaFlowX</div>
        </div>

        <div class="component-box" style="left: 130px; top: 340px; width: 110px; height: 50px;">
            <div class="component-number-inside">111</div>
            <div class="component-label">NovaPulse+</div>
        </div>

        <div class="component-box" style="left: 250px; top: 340px; width: 110px; height: 50px;">
            <div class="component-number-inside">112</div>
            <div class="component-label">NovaProof</div>
        </div>

        <div class="component-box" style="left: 370px; top: 340px; width: 110px; height: 50px;">
            <div class="component-number-inside">113</div>
            <div class="component-label">NovaThink</div>
        </div>

        <div class="component-box" style="left: 490px; top: 340px; width: 110px; height: 50px;">
            <div class="component-number-inside">114</div>
            <div class="component-label">NovaConnect</div>
        </div>

        <div class="component-box" style="left: 610px; top: 340px; width: 110px; height: 50px;">
            <div class="component-number-inside">115</div>
            <div class="component-label">NovaVision</div>
        </div>

        <div class="component-box" style="left: 730px; top: 340px; width: 110px; height: 50px;">
            <div class="component-number-inside">116</div>
            <div class="component-label">NovaDNA</div>
        </div>

        <!-- Implementation Layer -->
        <div class="container-box" style="width: 750px; height: 130px; left: 100px; top: 440px;">
            <div class="container-label">INDUSTRY-SPECIFIC IMPLEMENTATIONS</div>
        </div>

        <div class="component-box" style="left: 130px; top: 480px; width: 110px; height: 70px;">
            <div class="component-number-inside">117</div>
            <div class="component-label">Healthcare</div>
        </div>

        <div class="component-box" style="left: 250px; top: 480px; width: 110px; height: 70px;">
            <div class="component-number-inside">118</div>
            <div class="component-label">Financial</div>
        </div>

        <div class="component-box" style="left: 370px; top: 480px; width: 110px; height: 70px;">
            <div class="component-number-inside">119</div>
            <div class="component-label">Education</div>
        </div>

        <div class="component-box" style="left: 490px; top: 480px; width: 110px; height: 70px;">
            <div class="component-number-inside">120</div>
            <div class="component-label">Government</div>
        </div>

        <div class="component-box" style="left: 610px; top: 480px; width: 110px; height: 70px;">
            <div class="component-number-inside">121</div>
            <div class="component-label">Infrastructure</div>
        </div>

        <div class="component-box" style="left: 730px; top: 480px; width: 110px; height: 70px;">
            <div class="component-number-inside">122</div>
            <div class="component-label">Mobile/IoT</div>
        </div>

        <!-- Connecting arrows -->
        <div class="arrow" style="left: 220px; top: 200px; height: 70px;"></div>
        <div class="arrow" style="left: 380px; top: 200px; height: 70px;"></div>
        <div class="arrow" style="left: 540px; top: 200px; height: 70px;"></div>
        <div class="arrow" style="left: 700px; top: 200px; height: 70px;"></div>

        <div class="arrow" style="left: 220px; top: 410px; height: 70px;"></div>
        <div class="arrow" style="left: 380px; top: 410px; height: 70px;"></div>
        <div class="arrow" style="left: 540px; top: 410px; height: 70px;"></div>
        <div class="arrow" style="left: 700px; top: 410px; height: 70px;"></div>

        <!-- Universal Pattern Elements -->
        <div class="container-box" style="width: 750px; height: 80px; left: 100px; top: 600px;">
            <div class="container-label" style="font-size: 14px; top: 5px;">CYBER-SAFETY PRINCIPLES</div>
        </div>

        <div style="position: absolute; left: 125px; top: 635px; display: flex; justify-content: space-between; width: 700px;">
            <div style="font-size: 16px; text-align: center; width: 100px; font-weight: bold; z-index: 10; background-color: white; padding: 0 5px;">TRUTH</div>
            <div style="font-size: 16px; text-align: center; width: 100px; font-weight: bold; z-index: 10; background-color: white; padding: 0 5px;">TRUST</div>
            <div style="font-size: 16px; text-align: center; width: 150px; font-weight: bold; z-index: 10; background-color: white; padding: 0 5px;">TRANSPARENCY</div>
            <div style="font-size: 16px; text-align: center; width: 200px; font-weight: bold; z-index: 10; background-color: white; padding: 0 5px;">TENSORIAL GOVERNANCE</div>
        </div>

        <!-- Legend -->
        <div class="legend">
            <div class="legend-item">
                <div class="legend-color" style="background-color: #555555;"></div>
                <div>Protocol Core</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #555555;"></div>
                <div>Universal Components</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #555555;"></div>
                <div>Industry Implementations</div>
            </div>
        </div>

        <!-- Inventor Label -->
        <div class="inventor-label">Inventor: David Nigel Irvin</div>
    </div>
</body>
</html>
