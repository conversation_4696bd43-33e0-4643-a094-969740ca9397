/**
 * NovaFuse Universal API Connector - Base Error
 * 
 * This module defines the base error class for the UAC.
 * All UAC-specific errors should extend this class.
 */

/**
 * Base error class for all UAC errors
 * @class UAConnectorError
 * @extends Error
 */
class UAConnectorError extends Error {
  /**
   * Create a new UAConnectorError
   * 
   * @param {string} message - Error message
   * @param {Object} options - Error options
   * @param {string} options.code - Error code
   * @param {string} options.severity - Error severity (info, warning, error, critical)
   * @param {Object} options.context - Additional context for the error
   * @param {Error} options.cause - The error that caused this error
   */
  constructor(message, options = {}) {
    super(message);
    
    // Set the name to the class name
    this.name = this.constructor.name;
    
    // Set error properties
    this.code = options.code || 'UAC_ERROR';
    this.severity = options.severity || 'error';
    this.context = options.context || {};
    this.cause = options.cause;
    this.timestamp = new Date().toISOString();
    
    // Generate a unique error ID
    this.errorId = this._generateErrorId();
    
    // Capture stack trace
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor);
    }
  }

  /**
   * Generate a unique error ID
   * 
   * @returns {string} - Unique error ID
   * @private
   */
  _generateErrorId() {
    const timestamp = Date.now().toString(36);
    const randomStr = Math.random().toString(36).substring(2, 10);
    return `${timestamp}-${randomStr}`;
  }

  /**
   * Convert the error to a JSON object
   * 
   * @param {boolean} includeStack - Whether to include the stack trace
   * @returns {Object} - JSON representation of the error
   */
  toJSON(includeStack = false) {
    const json = {
      errorId: this.errorId,
      name: this.name,
      message: this.message,
      code: this.code,
      severity: this.severity,
      timestamp: this.timestamp,
      context: this.context
    };

    if (includeStack) {
      json.stack = this.stack;
    }

    if (this.cause) {
      json.cause = this.cause instanceof Error 
        ? {
            name: this.cause.name,
            message: this.cause.message,
            ...(includeStack ? { stack: this.cause.stack } : {})
          }
        : this.cause;
    }

    return json;
  }

  /**
   * Get a user-friendly error message
   * 
   * @returns {string} - User-friendly error message
   */
  getUserMessage() {
    return this.message;
  }

  /**
   * Get a developer-friendly error message with more details
   * 
   * @returns {string} - Developer-friendly error message
   */
  getDeveloperMessage() {
    return `[${this.code}] ${this.message}`;
  }
}

module.exports = UAConnectorError;
