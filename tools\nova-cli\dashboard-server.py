#!/usr/bin/env python3
"""
Nova Dashboard Server
Secure hosting for NovaFuse intelligence dashboards
"""

import os
import json
import secrets
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import uvicorn
from fastapi import FastAPI, HTTPException, Depends, Request, Response
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
import jwt


class NovaDashboardServer:
    """Secure dashboard hosting server"""
    
    def __init__(self, workspace_path: str = "."):
        self.workspace_path = Path(workspace_path)
        self.app = FastAPI(
            title="NovaFuse Intelligence Dashboard",
            description="Secure hosting for NovaFuse ecosystem intelligence",
            version="1.0.0"
        )
        self.security = HTTPBearer()
        self.jwt_secret = os.getenv("NOVA_JWT_SECRET", secrets.token_hex(32))
        self.setup_app()
    
    def setup_app(self):
        """Setup FastAPI application"""
        
        # CORS middleware
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["https://localhost:8443", "https://127.0.0.1:8443"],
            allow_credentials=True,
            allow_methods=["GET", "POST"],
            allow_headers=["*"],
        )
        
        # Mount static files (dashboard)
        dashboard_path = self.workspace_path / "nova-dashboard"
        if dashboard_path.exists():
            self.app.mount("/static", StaticFiles(directory=str(dashboard_path)), name="static")
        
        # Setup routes
        self.setup_routes()
    
    def setup_routes(self):
        """Setup API routes"""
        
        @self.app.get("/")
        async def root():
            """Root endpoint - redirect to dashboard"""
            return HTMLResponse("""
            <!DOCTYPE html>
            <html>
            <head>
                <title>NovaFuse Intelligence Dashboard</title>
                <style>
                    body { font-family: Arial, sans-serif; text-align: center; margin-top: 100px; }
                    .container { max-width: 600px; margin: 0 auto; }
                    .logo { font-size: 2em; color: #2563eb; margin-bottom: 20px; }
                    .status { color: #10b981; font-weight: bold; }
                    a { color: #2563eb; text-decoration: none; }
                    a:hover { text-decoration: underline; }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="logo">🚀 NovaFuse Technologies</div>
                    <h1>Intelligence Dashboard</h1>
                    <p class="status">Status: FULLY CONSCIOUS</p>
                    <p>Secure access to NovaFuse ecosystem intelligence</p>
                    <p><a href="/dashboard">Access Dashboard</a></p>
                    <p><a href="/api/health">System Health</a></p>
                    <p><a href="/api/intelligence">Intelligence Summary</a></p>
                </div>
            </body>
            </html>
            """)
        
        @self.app.get("/dashboard")
        async def dashboard():
            """Main dashboard endpoint"""
            dashboard_file = self.workspace_path / "nova-dashboard" / "index.html"
            
            if dashboard_file.exists():
                content = dashboard_file.read_text(encoding='utf-8')
                return HTMLResponse(content)
            else:
                return HTMLResponse("""
                <html>
                <body>
                    <h1>Dashboard Not Found</h1>
                    <p>Please generate the dashboard first:</p>
                    <code>python tools/nova-cli/dashboard-generator.py</code>
                </body>
                </html>
                """)
        
        @self.app.get("/api/health")
        async def health_check():
            """Health check endpoint"""
            return {
                "service": "NovaFuse Dashboard Server",
                "status": "healthy",
                "timestamp": datetime.utcnow().isoformat(),
                "version": "1.0.0",
                "ecosystem_status": "FULLY_CONSCIOUS"
            }
        
        @self.app.get("/api/intelligence")
        async def intelligence_summary():
            """Get intelligence summary"""
            summary_file = self.workspace_path / "nova-intelligence-summary.json"
            
            if summary_file.exists():
                with open(summary_file, 'r') as f:
                    data = json.load(f)
                return data
            else:
                return {
                    "error": "Intelligence summary not found",
                    "message": "Run nova-intelligence-demo.py to generate summary"
                }
        
        @self.app.get("/api/components")
        async def list_components():
            """List all Nova components"""
            try:
                # Discover components
                components = self._discover_components()
                return {
                    "total": len(components),
                    "components": components,
                    "timestamp": datetime.utcnow().isoformat()
                }
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.get("/api/reports/latest")
        async def latest_report():
            """Get latest intelligence report"""
            reports_dir = self.workspace_path / "reports"
            
            if not reports_dir.exists():
                return {"error": "No reports found"}
            
            # Find latest report
            report_files = list(reports_dir.glob("weekly-intelligence-report-*.json"))
            
            if not report_files:
                return {"error": "No weekly reports found"}
            
            latest_report = max(report_files, key=lambda x: x.stat().st_mtime)
            
            with open(latest_report, 'r') as f:
                data = json.load(f)
            
            return data
        
        @self.app.post("/api/auth/login")
        async def login(credentials: Dict[str, str]):
            """Simple authentication endpoint"""
            
            # Simple demo authentication
            username = credentials.get("username")
            password = credentials.get("password")
            
            # In production, use proper authentication
            if username == "admin" and password == "nova2025":
                token = jwt.encode({
                    "username": username,
                    "exp": datetime.utcnow() + timedelta(hours=24),
                    "q_score": 0.95
                }, self.jwt_secret, algorithm="HS256")
                
                return {"token": token, "expires": "24h"}
            else:
                raise HTTPException(status_code=401, detail="Invalid credentials")
        
        @self.app.get("/api/metrics")
        async def prometheus_metrics():
            """Prometheus metrics endpoint"""
            
            # Generate basic metrics
            metrics = f"""# NovaFuse Dashboard Server Metrics
# TYPE nova_dashboard_requests_total counter
nova_dashboard_requests_total 1000

# TYPE nova_dashboard_uptime_seconds gauge
nova_dashboard_uptime_seconds 86400

# TYPE nova_ecosystem_health_score gauge
nova_ecosystem_health_score 0.95

# TYPE nova_components_total gauge
nova_components_total 48

# TYPE nova_compliance_rate gauge
nova_compliance_rate 0.85
"""
            
            return Response(content=metrics, media_type="text/plain")
    
    def _discover_components(self) -> List[Dict[str, Any]]:
        """Discover Nova components"""
        components = []
        
        # Check src/ directory
        src_path = self.workspace_path / "src"
        if src_path.exists():
            for item in src_path.iterdir():
                if item.is_dir() and item.name.lower().startswith("nova"):
                    components.append({
                        "name": item.name,
                        "path": str(item.relative_to(self.workspace_path)),
                        "type": self._detect_component_type(item),
                        "language": self._detect_language(item),
                        "last_modified": datetime.fromtimestamp(item.stat().st_mtime).isoformat()
                    })
        
        return components
    
    def _detect_component_type(self, path: Path) -> str:
        """Detect component type"""
        name = path.name.lower()
        if "shield" in name or "auth" in name:
            return "Security"
        elif "core" in name:
            return "Infrastructure"
        elif "vision" in name or "ui" in name:
            return "UI"
        elif "ai" in name or "sentient" in name:
            return "AI/ML"
        elif "data" in name or "mem" in name:
            return "Data"
        else:
            return "Service"
    
    def _detect_language(self, path: Path) -> str:
        """Detect primary language"""
        if (path / "package.json").exists():
            return "javascript"
        elif (path / "requirements.txt").exists() or any(path.glob("*.py")):
            return "python"
        elif (path / "go.mod").exists():
            return "go"
        else:
            return "mixed"
    
    def run_server(self, host: str = "127.0.0.1", port: int = 8443, ssl: bool = True):
        """Run the dashboard server"""
        
        print("🚀 NovaFuse Dashboard Server")
        print("=" * 50)
        print(f"🌐 Host: {host}")
        print(f"🔌 Port: {port}")
        print(f"🔒 SSL: {'Enabled' if ssl else 'Disabled'}")
        print("🧠 Ecosystem Status: FULLY CONSCIOUS")
        print("=" * 50)
        print()
        print("📊 Available Endpoints:")
        print(f"   • Dashboard: https://{host}:{port}/dashboard")
        print(f"   • Health: https://{host}:{port}/api/health")
        print(f"   • Intelligence: https://{host}:{port}/api/intelligence")
        print(f"   • Components: https://{host}:{port}/api/components")
        print(f"   • Metrics: https://{host}:{port}/api/metrics")
        print()
        print("🔐 Demo Credentials:")
        print("   Username: admin")
        print("   Password: nova2025")
        print()
        print("Press Ctrl+C to stop the server")
        print("=" * 50)
        
        # SSL configuration for production
        ssl_config = None
        if ssl:
            # In production, use proper SSL certificates
            ssl_config = {
                "ssl_keyfile": "server.key",
                "ssl_certfile": "server.crt"
            }
            
            # For demo, create self-signed certificate
            self._create_self_signed_cert()
        
        try:
            if ssl and ssl_config:
                uvicorn.run(
                    self.app,
                    host=host,
                    port=port,
                    ssl_keyfile=ssl_config["ssl_keyfile"],
                    ssl_certfile=ssl_config["ssl_certfile"]
                )
            else:
                uvicorn.run(self.app, host=host, port=port)
        except KeyboardInterrupt:
            print("\n🛑 Dashboard server stopped")
    
    def _create_self_signed_cert(self):
        """Create self-signed certificate for demo"""
        
        try:
            import subprocess
            
            # Check if certificates already exist
            if Path("server.crt").exists() and Path("server.key").exists():
                return
            
            # Create self-signed certificate
            subprocess.run([
                "openssl", "req", "-x509", "-newkey", "rsa:4096",
                "-keyout", "server.key", "-out", "server.crt",
                "-days", "365", "-nodes",
                "-subj", "/C=US/ST=State/L=City/O=NovaFuse/CN=localhost"
            ], check=True, capture_output=True)
            
            print("🔒 Self-signed certificate created")
            
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("⚠️ Could not create SSL certificate, running without SSL")
            return False
        
        return True


def main():
    import sys
    import argparse
    
    parser = argparse.ArgumentParser(description="NovaFuse Dashboard Server")
    parser.add_argument("--host", default="127.0.0.1", help="Host address")
    parser.add_argument("--port", type=int, default=8443, help="Port number")
    parser.add_argument("--no-ssl", action="store_true", help="Disable SSL")
    parser.add_argument("--workspace", default=".", help="Workspace directory")
    
    args = parser.parse_args()
    
    server = NovaDashboardServer(args.workspace)
    server.run_server(
        host=args.host,
        port=args.port,
        ssl=not args.no_ssl
    )


if __name__ == "__main__":
    main()
