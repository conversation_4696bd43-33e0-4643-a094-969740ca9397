/**
 * Key Management Service for NovaConnect Universal API Connector
 * 
 * This module provides secure key management capabilities.
 * In a production environment, this would integrate with a cloud key management service
 * like AWS KMS, Azure Key Vault, or HashiCorp Vault.
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const { encrypt, decrypt } = require('./encryption');

// Constants
const KEY_DIRECTORY = process.env.KEY_DIRECTORY || path.join(__dirname, '../.keys');
const MASTER_KEY_FILE = path.join(KEY_DIRECTORY, 'master.key');
const KEYS_FILE = path.join(KEY_DIRECTORY, 'keys.json');
const KEY_ROTATION_DAYS = 90; // Rotate keys every 90 days

// Ensure key directory exists
if (!fs.existsSync(KEY_DIRECTORY)) {
  fs.mkdirSync(KEY_DIRECTORY, { recursive: true });
}

/**
 * Initialize the key management service
 * 
 * @returns {Promise<void>}
 */
async function initialize() {
  // Create master key if it doesn't exist
  if (!fs.existsSync(MASTER_KEY_FILE)) {
    const masterKey = crypto.randomBytes(32).toString('hex');
    fs.writeFileSync(MASTER_KEY_FILE, masterKey, { mode: 0o600 });
    console.log('Master key created');
  }
  
  // Create keys file if it doesn't exist
  if (!fs.existsSync(KEYS_FILE)) {
    const keys = {
      current: {
        id: generateKeyId(),
        key: crypto.randomBytes(32).toString('hex'),
        created: new Date().toISOString(),
        expires: new Date(Date.now() + KEY_ROTATION_DAYS * 24 * 60 * 60 * 1000).toISOString()
      },
      previous: []
    };
    
    // Encrypt keys with master key
    const masterKey = fs.readFileSync(MASTER_KEY_FILE, 'utf8');
    const encryptedKeys = encrypt(keys, masterKey, { deriveKey: true });
    
    fs.writeFileSync(KEYS_FILE, JSON.stringify(encryptedKeys), { mode: 0o600 });
    console.log('Keys file created');
  }
}

/**
 * Generate a unique key ID
 * 
 * @returns {string} - A unique key ID
 */
function generateKeyId() {
  return `key-${Date.now()}-${crypto.randomBytes(4).toString('hex')}`;
}

/**
 * Get the current encryption key
 * 
 * @returns {Object} - The current encryption key
 */
function getCurrentKey() {
  // Check if keys need to be rotated
  checkKeyRotation();
  
  // Read master key
  const masterKey = fs.readFileSync(MASTER_KEY_FILE, 'utf8');
  
  // Read and decrypt keys
  const encryptedKeys = JSON.parse(fs.readFileSync(KEYS_FILE, 'utf8'));
  const keys = decrypt(encryptedKeys, masterKey, { deriveKey: true, parseJson: true });
  
  return {
    id: keys.current.id,
    key: keys.current.key
  };
}

/**
 * Get a specific encryption key by ID
 * 
 * @param {string} keyId - The key ID
 * @returns {Object|null} - The encryption key or null if not found
 */
function getKeyById(keyId) {
  // Read master key
  const masterKey = fs.readFileSync(MASTER_KEY_FILE, 'utf8');
  
  // Read and decrypt keys
  const encryptedKeys = JSON.parse(fs.readFileSync(KEYS_FILE, 'utf8'));
  const keys = decrypt(encryptedKeys, masterKey, { deriveKey: true, parseJson: true });
  
  // Check current key
  if (keys.current.id === keyId) {
    return {
      id: keys.current.id,
      key: keys.current.key
    };
  }
  
  // Check previous keys
  for (const prevKey of keys.previous) {
    if (prevKey.id === keyId) {
      return {
        id: prevKey.id,
        key: prevKey.key
      };
    }
  }
  
  return null;
}

/**
 * Check if keys need to be rotated and rotate if necessary
 */
function checkKeyRotation() {
  // Read master key
  const masterKey = fs.readFileSync(MASTER_KEY_FILE, 'utf8');
  
  // Read and decrypt keys
  const encryptedKeys = JSON.parse(fs.readFileSync(KEYS_FILE, 'utf8'));
  const keys = decrypt(encryptedKeys, masterKey, { deriveKey: true, parseJson: true });
  
  // Check if current key has expired
  const currentExpiry = new Date(keys.current.expires);
  if (currentExpiry <= new Date()) {
    // Rotate keys
    rotateKeys();
  }
}

/**
 * Rotate encryption keys
 */
function rotateKeys() {
  // Read master key
  const masterKey = fs.readFileSync(MASTER_KEY_FILE, 'utf8');
  
  // Read and decrypt keys
  const encryptedKeys = JSON.parse(fs.readFileSync(KEYS_FILE, 'utf8'));
  const keys = decrypt(encryptedKeys, masterKey, { deriveKey: true, parseJson: true });
  
  // Move current key to previous keys
  keys.previous.unshift({
    id: keys.current.id,
    key: keys.current.key,
    created: keys.current.created,
    expires: keys.current.expires,
    rotated: new Date().toISOString()
  });
  
  // Limit the number of previous keys to keep
  if (keys.previous.length > 5) {
    keys.previous = keys.previous.slice(0, 5);
  }
  
  // Create new current key
  keys.current = {
    id: generateKeyId(),
    key: crypto.randomBytes(32).toString('hex'),
    created: new Date().toISOString(),
    expires: new Date(Date.now() + KEY_ROTATION_DAYS * 24 * 60 * 60 * 1000).toISOString()
  };
  
  // Encrypt and save keys
  const newEncryptedKeys = encrypt(keys, masterKey, { deriveKey: true });
  fs.writeFileSync(KEYS_FILE, JSON.stringify(newEncryptedKeys), { mode: 0o600 });
  
  console.log(`Keys rotated. New key ID: ${keys.current.id}`);
  return keys.current.id;
}

/**
 * Force key rotation
 * 
 * @returns {string} - The new key ID
 */
function forceKeyRotation() {
  return rotateKeys();
}

module.exports = {
  initialize,
  getCurrentKey,
  getKeyById,
  forceKeyRotation
};
