/**
 * Feature Flag Routes
 */

const express = require('express');
const router = express.Router();
const FeatureFlagController = require('../controllers/FeatureFlagController');
const { authenticate, hasPermission } = require('../middleware/authMiddleware');

// All routes require authentication
router.use(authenticate);

// Feature flag routes (admin only)
router.get('/flags', hasPermission('system:settings'), (req, res, next) => {
  FeatureFlagController.getAllFeatureFlags(req, res, next);
});

router.get('/flags/:id', hasPermission('system:settings'), (req, res, next) => {
  FeatureFlagController.getFeatureFlagById(req, res, next);
});

router.put('/flags/:id', hasPermission('system:settings'), (req, res, next) => {
  FeatureFlagController.updateFeatureFlag(req, res, next);
});

// Subscription tier routes
router.get('/tiers', (req, res, next) => {
  FeatureFlagController.getAllSubscriptionTiers(req, res, next);
});

router.get('/tiers/:id', (req, res, next) => {
  FeatureFlagController.getSubscriptionTierById(req, res, next);
});

// User entitlement routes (admin only)
router.get('/users/:userId/entitlement', hasPermission('system:settings'), (req, res, next) => {
  FeatureFlagController.getUserEntitlement(req, res, next);
});

router.put('/users/:userId/entitlement', hasPermission('system:settings'), (req, res, next) => {
  FeatureFlagController.updateUserEntitlement(req, res, next);
});

// Feature access routes
router.get('/users/:userId/features/:featureId/access', hasPermission('system:settings'), (req, res, next) => {
  FeatureFlagController.hasFeatureAccess(req, res, next);
});

router.get('/users/:userId/features/:featureId/limits/:limitKey', hasPermission('system:settings'), (req, res, next) => {
  FeatureFlagController.getFeatureLimit(req, res, next);
});

router.post('/users/:userId/features/:featureId/usage', hasPermission('system:settings'), (req, res, next) => {
  FeatureFlagController.trackFeatureUsage(req, res, next);
});

router.get('/users/:userId/usage', hasPermission('system:settings'), (req, res, next) => {
  FeatureFlagController.getFeatureUsageForUser(req, res, next);
});

router.get('/users/:userId/features/:featureId/limits/:limitKey/check', hasPermission('system:settings'), (req, res, next) => {
  FeatureFlagController.hasReachedFeatureLimit(req, res, next);
});

// User subscription routes
router.get('/users/:userId/features', hasPermission('system:settings'), (req, res, next) => {
  FeatureFlagController.getUserAvailableFeatures(req, res, next);
});

router.get('/users/:userId/subscription', hasPermission('system:settings'), (req, res, next) => {
  FeatureFlagController.getUserSubscriptionDetails(req, res, next);
});

// Current user subscription routes
router.get('/my/subscription', (req, res, next) => {
  FeatureFlagController.getCurrentUserSubscriptionDetails(req, res, next);
});

module.exports = router;
