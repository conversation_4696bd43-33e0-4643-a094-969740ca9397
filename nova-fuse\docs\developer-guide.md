# NovaFuse Developer Guide

This guide provides information for developers working on the NovaFuse platform.

## Getting Started

### Prerequisites

- Node.js (v14 or higher)
- <PERSON>er and Docker Compose
- MongoDB
- Git

### Setting Up the Development Environment

1. Clone all repositories:
   ```
   git clone https://github.com/Dartan1983/nova-fuse.git
   git clone https://github.com/Dartan1983/nova-connect.git
   git clone https://github.com/Dartan1983/nova-grc-apis.git
   git clone https://github.com/Dartan1983/nova-ui.git
   git clone https://github.com/Dartan1983/nova-gateway.git
   ```

2. Set up each repository:
   - Install dependencies: `npm install`
   - Configure environment variables: Copy `.env.example` to `.env` and update as needed
   - Start the development server: `npm run dev`

## Development Workflow

### Branching Strategy

- `main` - Production-ready code
- `develop` - Development branch
- `feature/*` - Feature branches
- `bugfix/*` - Bug fix branches
- `release/*` - Release branches

### Pull Request Process

1. Create a feature branch from `develop`
2. Implement your changes
3. Write tests for your changes
4. Submit a pull request to `develop`
5. Ensure all tests pass
6. Get approval from at least one reviewer

### Coding Standards

- Follow the ESLint configuration in each repository
- Write unit tests for all new features
- Maintain test coverage of at least 80%
- Use semantic versioning for releases

## Repository Structure

### nova-fuse

Main repository with documentation and project overview.

### nova-connect

Universal API Connector for seamless API integration.

```
nova-connect/
├── api/                  # API server
│   ├── controllers/      # API controllers
│   ├── middleware/       # Express middleware
│   ├── models/           # Data models
│   ├── routes/           # API routes
│   └── services/         # Business logic
├── auth/                 # Authentication services
│   ├── oauth/            # OAuth implementation
│   ├── apikey/           # API key management
│   └── basic/            # Basic auth
├── connector/            # Connector implementation
│   ├── registry/         # Connector registry
│   ├── executor/         # Request execution
│   └── templates/        # Connector templates
├── transform/            # Data transformation
│   ├── engine/           # Transformation engine
│   ├── functions/        # Custom functions
│   └── schemas/          # Data schemas
├── ui/                   # UI components
│   ├── admin/            # Admin interface
│   ├── designer/         # Endpoint designer
│   └── testing/          # Testing interface
├── docs/                 # Documentation
├── tests/                # Test suite
└── utils/                # Utility functions
```

### nova-grc-apis

Collection of GRC APIs.

```
nova-grc-apis/
├── privacy-management/       # Privacy Management API
├── regulatory-compliance/    # Regulatory Compliance API
├── security-assessment/      # Security Assessment API
├── control-testing/          # Control Testing API
├── esg/                      # ESG API
├── compliance-automation/    # Compliance Automation API
└── shared/                   # Shared utilities and middleware
```

### nova-ui

UI components for all NovaFuse products.

```
nova-ui/
├── packages/                # Shared packages
│   ├── ui-components/       # Shared UI components
│   ├── api-client/          # API client libraries
│   ├── utils/               # Shared utilities
│   └── feature-flags/       # Feature flag system
│
├── products/                # Product-specific UIs
│   ├── prime/               # NovaPrime UI
│   ├── core/                # NovaCore UI
│   ├── shield/              # NovaShield UI
│   ├── assist-ai/           # NovaAssistAI UI
│   ├── learn/               # NovaLearn UI
│   └── marketplace/         # NovaMarketplace UI
│
└── shared/                  # Shared resources
    ├── styles/              # Global styles
    ├── assets/              # Shared assets
    └── layouts/             # Common layouts
```

### nova-gateway

API Gateway for routing and managing API requests.

```
nova-gateway/
├── config/                  # Configuration files
├── middleware/              # Middleware functions
├── routes/                  # API routes
├── services/                # Service connectors
└── utils/                   # Utility functions
```

## Feature Flag System

The feature flag system is a key component of the NovaFuse platform, allowing for a single codebase to power multiple products with different feature sets.

### Feature Flag Configuration

Feature flags are defined in the `nova-ui/packages/feature-flags` package and are used to enable/disable features based on the product tier.

### Using Feature Flags

```jsx
import { useFeatureFlag } from '@nova-ui/feature-flags';

function AnalyticsPanel() {
  const isEnabled = useFeatureFlag('analytics');
  
  if (!isEnabled) {
    return null;
  }
  
  return (
    <div className="analytics-panel">
      {/* Analytics content */}
    </div>
  );
}
```

## API Integration

### API Client

The API client is defined in the `nova-ui/packages/api-client` package and provides a unified interface for interacting with the NovaFuse APIs.

```javascript
import { createApiClient } from '@nova-ui/api-client';

const apiClient = createApiClient({
  baseUrl: process.env.API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  }
});

// Example usage
const response = await apiClient.get('/api/privacy/management/processing-activities');
```

## Testing

### Unit Testing

Unit tests are written using Jest and React Testing Library.

```javascript
import { render, screen } from '@testing-library/react';
import { AnalyticsPanel } from './AnalyticsPanel';

test('renders analytics panel when feature is enabled', () => {
  // Mock the feature flag hook
  jest.mock('@nova-ui/feature-flags', () => ({
    useFeatureFlag: () => true
  }));

  render(<AnalyticsPanel />);
  expect(screen.getByText('Analytics Dashboard')).toBeInTheDocument();
});
```

### Integration Testing

Integration tests are written using Supertest for APIs and Cypress for UI.

## Deployment

### Development Environment

```
npm run dev
```

### Production Build

```
npm run build
npm start
```

## Troubleshooting

### Common Issues

- **API Connection Issues**: Check that the API Gateway is running and properly configured.
- **Feature Flag Issues**: Verify that the feature flag configuration is correct for the current product.
- **Database Connection Issues**: Check that MongoDB is running and the connection string is correct.

## Resources

- [NovaFuse Documentation](https://github.com/Dartan1983/nova-fuse)
- [NovaConnect Documentation](https://github.com/Dartan1983/nova-connect)
- [NovaGRC APIs Documentation](https://github.com/Dartan1983/nova-grc-apis)
- [NovaUI Documentation](https://github.com/Dartan1983/nova-ui)
- [NovaGateway Documentation](https://github.com/Dartan1983/nova-gateway)
