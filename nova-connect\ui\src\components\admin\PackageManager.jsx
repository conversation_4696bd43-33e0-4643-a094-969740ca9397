import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Card,
  CardContent,
  CardHeader,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  Grid,
  IconButton,
  List,
  ListItem,
  ListItemIcon,
  ListItemSecondaryAction,
  ListItemText,
  MenuItem,
  Paper,
  Select,
  Tab,
  Tabs,
  TextField,
  Typography,
  useTheme
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Extension as ExtensionIcon,
  Settings as SettingsIcon,
  Speed as SpeedIcon,
  Storage as StorageIcon
} from '@mui/icons-material';
import { DataGrid } from '@mui/x-data-grid';
import { useSnackbar } from 'notistack';
import { useConfirm } from 'material-ui-confirm';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import api from '../../services/api';
import LoadingIndicator from '../common/LoadingIndicator';
import ErrorMessage from '../common/ErrorMessage';

// Package validation schema
const packageSchema = Yup.object().shape({
  id: Yup.string().required('Package ID is required'),
  name: Yup.string().required('Package name is required'),
  description: Yup.string().required('Description is required'),
  tier: Yup.string().required('Tier is required'),
  features: Yup.array().of(Yup.string()).required('Features are required'),
  limits: Yup.object().required('Limits are required')
});

// Tenant mapping validation schema
const tenantMappingSchema = Yup.object().shape({
  tenantId: Yup.string().required('Tenant ID is required'),
  packageId: Yup.string().required('Package ID is required'),
  customFeatures: Yup.array().of(Yup.string()),
  customLimits: Yup.object()
});

// Package tiers
const PACKAGE_TIERS = [
  { value: 'core', label: 'Core' },
  { value: 'secure', label: 'Secure' },
  { value: 'enterprise', label: 'Enterprise' },
  { value: 'ai_boost', label: 'AI Boost' }
];

// Tab panel component
function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`package-tabpanel-${index}`}
      aria-labelledby={`package-tab-${index}`}
      {...other}
    >
      {value === index && <Box p={3}>{children}</Box>}
    </div>
  );
}

// Package Manager component
const PackageManager = () => {
  const theme = useTheme();
  const { enqueueSnackbar } = useSnackbar();
  const confirm = useConfirm();
  const [tabValue, setTabValue] = useState(0);
  const [packages, setPackages] = useState([]);
  const [tenantMappings, setTenantMappings] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [packageDialogOpen, setPackageDialogOpen] = useState(false);
  const [editingPackage, setEditingPackage] = useState(null);
  const [tenantDialogOpen, setTenantDialogOpen] = useState(false);
  const [editingTenantMapping, setEditingTenantMapping] = useState(null);
  const [featureInput, setFeatureInput] = useState('');
  const [limitKey, setLimitKey] = useState('');
  const [limitValue, setLimitValue] = useState('');

  // Package form
  const packageFormik = useFormik({
    initialValues: {
      id: '',
      name: '',
      description: '',
      tier: 'core',
      features: [],
      limits: {}
    },
    validationSchema: packageSchema,
    onSubmit: async (values) => {
      try {
        setLoading(true);
        if (editingPackage) {
          // Update existing package
          await api.put(`/api/packages/${editingPackage.id}`, values);
          enqueueSnackbar('Package updated successfully', { variant: 'success' });
        } else {
          // Create new package
          await api.post('/api/packages', values);
          enqueueSnackbar('Package created successfully', { variant: 'success' });
        }
        setPackageDialogOpen(false);
        fetchPackages();
      } catch (error) {
        console.error('Error saving package:', error);
        enqueueSnackbar(`Error: ${error.response?.data?.message || error.message}`, { variant: 'error' });
      } finally {
        setLoading(false);
      }
    }
  });

  // Tenant mapping form
  const tenantFormik = useFormik({
    initialValues: {
      tenantId: '',
      packageId: '',
      customFeatures: [],
      customLimits: {}
    },
    validationSchema: tenantMappingSchema,
    onSubmit: async (values) => {
      try {
        setLoading(true);
        await api.put(`/api/packages/tenant/${values.tenantId}`, {
          packageId: values.packageId,
          customFeatures: values.customFeatures,
          customLimits: values.customLimits
        });
        enqueueSnackbar('Tenant package mapping updated successfully', { variant: 'success' });
        setTenantDialogOpen(false);
        fetchTenantMappings();
      } catch (error) {
        console.error('Error saving tenant mapping:', error);
        enqueueSnackbar(`Error: ${error.response?.data?.message || error.message}`, { variant: 'error' });
      } finally {
        setLoading(false);
      }
    }
  });

  // Fetch packages
  const fetchPackages = async () => {
    try {
      setLoading(true);
      const response = await api.get('/api/packages');
      setPackages(response.data);
    } catch (error) {
      console.error('Error fetching packages:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  // Fetch tenant mappings
  const fetchTenantMappings = async () => {
    try {
      setLoading(true);
      // This would be replaced with an actual API call in a real implementation
      // For now, we'll use a mock response
      const mockMappings = [
        {
          tenantId: 'tenant-1',
          packageId: 'enterprise',
          customFeatures: ['custom.feature1'],
          customLimits: { connections: 150 }
        },
        {
          tenantId: 'tenant-2',
          packageId: 'secure',
          customFeatures: [],
          customLimits: {}
        }
      ];
      setTenantMappings(mockMappings);
    } catch (error) {
      console.error('Error fetching tenant mappings:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  // Clear cache
  const clearCache = async () => {
    try {
      setLoading(true);
      await api.post('/api/packages/cache/clear');
      enqueueSnackbar('Cache cleared successfully', { variant: 'success' });
    } catch (error) {
      console.error('Error clearing cache:', error);
      enqueueSnackbar(`Error: ${error.response?.data?.message || error.message}`, { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  // Delete package
  const deletePackage = async (packageId) => {
    try {
      await confirm({
        title: 'Delete Package',
        description: `Are you sure you want to delete the package "${packageId}"? This action cannot be undone.`,
        confirmationText: 'Delete',
        confirmationButtonProps: { color: 'error' }
      });
      
      setLoading(true);
      await api.delete(`/api/packages/${packageId}`);
      enqueueSnackbar('Package deleted successfully', { variant: 'success' });
      fetchPackages();
    } catch (error) {
      if (error !== 'cancel') {
        console.error('Error deleting package:', error);
        enqueueSnackbar(`Error: ${error.response?.data?.message || error.message}`, { variant: 'error' });
      }
    } finally {
      setLoading(false);
    }
  };

  // Open package dialog for editing
  const openEditPackageDialog = (pkg) => {
    setEditingPackage(pkg);
    packageFormik.setValues({
      id: pkg.id,
      name: pkg.name,
      description: pkg.description,
      tier: pkg.tier,
      features: [...pkg.features],
      limits: { ...pkg.limits }
    });
    setPackageDialogOpen(true);
  };

  // Open package dialog for creating
  const openCreatePackageDialog = () => {
    setEditingPackage(null);
    packageFormik.resetForm();
    setPackageDialogOpen(true);
  };

  // Open tenant mapping dialog for editing
  const openEditTenantDialog = (mapping) => {
    setEditingTenantMapping(mapping);
    tenantFormik.setValues({
      tenantId: mapping.tenantId,
      packageId: mapping.packageId,
      customFeatures: [...mapping.customFeatures],
      customLimits: { ...mapping.customLimits }
    });
    setTenantDialogOpen(true);
  };

  // Open tenant mapping dialog for creating
  const openCreateTenantDialog = () => {
    setEditingTenantMapping(null);
    tenantFormik.resetForm();
    setTenantDialogOpen(true);
  };

  // Add feature to package
  const addFeature = () => {
    if (featureInput && !packageFormik.values.features.includes(featureInput)) {
      packageFormik.setFieldValue('features', [...packageFormik.values.features, featureInput]);
      setFeatureInput('');
    }
  };

  // Remove feature from package
  const removeFeature = (feature) => {
    packageFormik.setFieldValue(
      'features',
      packageFormik.values.features.filter(f => f !== feature)
    );
  };

  // Add limit to package
  const addLimit = () => {
    if (limitKey && limitValue) {
      packageFormik.setFieldValue('limits', {
        ...packageFormik.values.limits,
        [limitKey]: parseInt(limitValue, 10)
      });
      setLimitKey('');
      setLimitValue('');
    }
  };

  // Remove limit from package
  const removeLimit = (key) => {
    const newLimits = { ...packageFormik.values.limits };
    delete newLimits[key];
    packageFormik.setFieldValue('limits', newLimits);
  };

  // Add custom feature to tenant
  const addCustomFeature = () => {
    if (featureInput && !tenantFormik.values.customFeatures.includes(featureInput)) {
      tenantFormik.setFieldValue('customFeatures', [...tenantFormik.values.customFeatures, featureInput]);
      setFeatureInput('');
    }
  };

  // Remove custom feature from tenant
  const removeCustomFeature = (feature) => {
    tenantFormik.setFieldValue(
      'customFeatures',
      tenantFormik.values.customFeatures.filter(f => f !== feature)
    );
  };

  // Add custom limit to tenant
  const addCustomLimit = () => {
    if (limitKey && limitValue) {
      tenantFormik.setFieldValue('customLimits', {
        ...tenantFormik.values.customLimits,
        [limitKey]: parseInt(limitValue, 10)
      });
      setLimitKey('');
      setLimitValue('');
    }
  };

  // Remove custom limit from tenant
  const removeCustomLimit = (key) => {
    const newLimits = { ...tenantFormik.values.customLimits };
    delete newLimits[key];
    tenantFormik.setFieldValue('customLimits', newLimits);
  };

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  // Load data on component mount
  useEffect(() => {
    fetchPackages();
    fetchTenantMappings();
  }, []);

  // Package data grid columns
  const packageColumns = [
    { field: 'id', headerName: 'ID', width: 150 },
    { field: 'name', headerName: 'Name', width: 200 },
    { field: 'tier', headerName: 'Tier', width: 150 },
    {
      field: 'features',
      headerName: 'Features',
      width: 200,
      renderCell: (params) => (
        <Typography variant="body2">
          {params.value.length} features
        </Typography>
      )
    },
    {
      field: 'limits',
      headerName: 'Limits',
      width: 200,
      renderCell: (params) => (
        <Typography variant="body2">
          {Object.keys(params.value).length} limits
        </Typography>
      )
    },
    {
      field: 'actions',
      headerName: 'Actions',
      width: 150,
      renderCell: (params) => (
        <>
          <IconButton
            size="small"
            color="primary"
            onClick={() => openEditPackageDialog(params.row)}
          >
            <EditIcon />
          </IconButton>
          <IconButton
            size="small"
            color="error"
            onClick={() => deletePackage(params.row.id)}
          >
            <DeleteIcon />
          </IconButton>
        </>
      )
    }
  ];

  // Tenant mapping data grid columns
  const tenantColumns = [
    { field: 'tenantId', headerName: 'Tenant ID', width: 200 },
    { field: 'packageId', headerName: 'Package', width: 150 },
    {
      field: 'customFeatures',
      headerName: 'Custom Features',
      width: 200,
      renderCell: (params) => (
        <Typography variant="body2">
          {params.value.length} custom features
        </Typography>
      )
    },
    {
      field: 'customLimits',
      headerName: 'Custom Limits',
      width: 200,
      renderCell: (params) => (
        <Typography variant="body2">
          {Object.keys(params.value).length} custom limits
        </Typography>
      )
    },
    {
      field: 'actions',
      headerName: 'Actions',
      width: 150,
      renderCell: (params) => (
        <IconButton
          size="small"
          color="primary"
          onClick={() => openEditTenantDialog(params.row)}
        >
          <EditIcon />
        </IconButton>
      )
    }
  ];

  // If loading, show loading indicator
  if (loading && !packages.length && !tenantMappings.length) {
    return <LoadingIndicator />;
  }

  // If error, show error message
  if (error) {
    return <ErrorMessage message={error} />;
  }

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Package Manager
      </Typography>
      
      <Paper>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
        >
          <Tab label="Packages" icon={<ExtensionIcon />} />
          <Tab label="Tenant Mappings" icon={<StorageIcon />} />
        </Tabs>
        
        <TabPanel value={tabValue} index={0}>
          <Box display="flex" justifyContent="space-between" mb={2}>
            <Typography variant="h6">
              Manage Packages
            </Typography>
            <Box>
              <Button
                variant="outlined"
                color="primary"
                startIcon={<SettingsIcon />}
                onClick={clearCache}
                sx={{ mr: 1 }}
              >
                Clear Cache
              </Button>
              <Button
                variant="contained"
                color="primary"
                startIcon={<AddIcon />}
                onClick={openCreatePackageDialog}
              >
                New Package
              </Button>
            </Box>
          </Box>
          
          <DataGrid
            rows={packages}
            columns={packageColumns}
            pageSize={10}
            rowsPerPageOptions={[10, 25, 50]}
            autoHeight
            disableSelectionOnClick
            getRowId={(row) => row.id}
          />
        </TabPanel>
        
        <TabPanel value={tabValue} index={1}>
          <Box display="flex" justifyContent="space-between" mb={2}>
            <Typography variant="h6">
              Tenant Package Mappings
            </Typography>
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={openCreateTenantDialog}
            >
              New Mapping
            </Button>
          </Box>
          
          <DataGrid
            rows={tenantMappings}
            columns={tenantColumns}
            pageSize={10}
            rowsPerPageOptions={[10, 25, 50]}
            autoHeight
            disableSelectionOnClick
            getRowId={(row) => row.tenantId}
          />
        </TabPanel>
      </Paper>
      
      {/* Package Dialog */}
      <Dialog
        open={packageDialogOpen}
        onClose={() => setPackageDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <form onSubmit={packageFormik.handleSubmit}>
          <DialogTitle>
            {editingPackage ? 'Edit Package' : 'Create Package'}
          </DialogTitle>
          <DialogContent>
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Package ID"
                  name="id"
                  value={packageFormik.values.id}
                  onChange={packageFormik.handleChange}
                  error={packageFormik.touched.id && Boolean(packageFormik.errors.id)}
                  helperText={packageFormik.touched.id && packageFormik.errors.id}
                  disabled={Boolean(editingPackage)}
                  margin="normal"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Package Name"
                  name="name"
                  value={packageFormik.values.name}
                  onChange={packageFormik.handleChange}
                  error={packageFormik.touched.name && Boolean(packageFormik.errors.name)}
                  helperText={packageFormik.touched.name && packageFormik.errors.name}
                  margin="normal"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Description"
                  name="description"
                  value={packageFormik.values.description}
                  onChange={packageFormik.handleChange}
                  error={packageFormik.touched.description && Boolean(packageFormik.errors.description)}
                  helperText={packageFormik.touched.description && packageFormik.errors.description}
                  margin="normal"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <Select
                  fullWidth
                  label="Tier"
                  name="tier"
                  value={packageFormik.values.tier}
                  onChange={packageFormik.handleChange}
                  error={packageFormik.touched.tier && Boolean(packageFormik.errors.tier)}
                  margin="normal"
                >
                  {PACKAGE_TIERS.map((tier) => (
                    <MenuItem key={tier.value} value={tier.value}>
                      {tier.label}
                    </MenuItem>
                  ))}
                </Select>
              </Grid>
              <Grid item xs={12}>
                <Divider sx={{ my: 2 }} />
                <Typography variant="h6">Features</Typography>
                <Box display="flex" alignItems="center" mt={1}>
                  <TextField
                    fullWidth
                    label="Feature ID"
                    value={featureInput}
                    onChange={(e) => setFeatureInput(e.target.value)}
                    margin="normal"
                  />
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={addFeature}
                    sx={{ ml: 1, height: 56 }}
                  >
                    Add
                  </Button>
                </Box>
                <List>
                  {packageFormik.values.features.map((feature) => (
                    <ListItem key={feature}>
                      <ListItemIcon>
                        <ExtensionIcon />
                      </ListItemIcon>
                      <ListItemText primary={feature} />
                      <ListItemSecondaryAction>
                        <IconButton
                          edge="end"
                          color="error"
                          onClick={() => removeFeature(feature)}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </ListItemSecondaryAction>
                    </ListItem>
                  ))}
                </List>
              </Grid>
              <Grid item xs={12}>
                <Divider sx={{ my: 2 }} />
                <Typography variant="h6">Limits</Typography>
                <Box display="flex" alignItems="center" mt={1}>
                  <TextField
                    label="Limit Key"
                    value={limitKey}
                    onChange={(e) => setLimitKey(e.target.value)}
                    margin="normal"
                    sx={{ mr: 1, flex: 1 }}
                  />
                  <TextField
                    label="Limit Value"
                    type="number"
                    value={limitValue}
                    onChange={(e) => setLimitValue(e.target.value)}
                    margin="normal"
                    sx={{ mr: 1, flex: 1 }}
                  />
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={addLimit}
                    sx={{ height: 56 }}
                  >
                    Add
                  </Button>
                </Box>
                <List>
                  {Object.entries(packageFormik.values.limits).map(([key, value]) => (
                    <ListItem key={key}>
                      <ListItemIcon>
                        <SpeedIcon />
                      </ListItemIcon>
                      <ListItemText
                        primary={key}
                        secondary={`Limit: ${value}`}
                      />
                      <ListItemSecondaryAction>
                        <IconButton
                          edge="end"
                          color="error"
                          onClick={() => removeLimit(key)}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </ListItemSecondaryAction>
                    </ListItem>
                  ))}
                </List>
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setPackageDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              type="submit"
              variant="contained"
              color="primary"
              disabled={loading}
            >
              {editingPackage ? 'Update' : 'Create'}
            </Button>
          </DialogActions>
        </form>
      </Dialog>
      
      {/* Tenant Mapping Dialog */}
      <Dialog
        open={tenantDialogOpen}
        onClose={() => setTenantDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <form onSubmit={tenantFormik.handleSubmit}>
          <DialogTitle>
            {editingTenantMapping ? 'Edit Tenant Mapping' : 'Create Tenant Mapping'}
          </DialogTitle>
          <DialogContent>
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Tenant ID"
                  name="tenantId"
                  value={tenantFormik.values.tenantId}
                  onChange={tenantFormik.handleChange}
                  error={tenantFormik.touched.tenantId && Boolean(tenantFormik.errors.tenantId)}
                  helperText={tenantFormik.touched.tenantId && tenantFormik.errors.tenantId}
                  disabled={Boolean(editingTenantMapping)}
                  margin="normal"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <Select
                  fullWidth
                  label="Package"
                  name="packageId"
                  value={tenantFormik.values.packageId}
                  onChange={tenantFormik.handleChange}
                  error={tenantFormik.touched.packageId && Boolean(tenantFormik.errors.packageId)}
                  margin="normal"
                >
                  {packages.map((pkg) => (
                    <MenuItem key={pkg.id} value={pkg.id}>
                      {pkg.name}
                    </MenuItem>
                  ))}
                </Select>
              </Grid>
              <Grid item xs={12}>
                <Divider sx={{ my: 2 }} />
                <Typography variant="h6">Custom Features</Typography>
                <Box display="flex" alignItems="center" mt={1}>
                  <TextField
                    fullWidth
                    label="Feature ID"
                    value={featureInput}
                    onChange={(e) => setFeatureInput(e.target.value)}
                    margin="normal"
                  />
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={addCustomFeature}
                    sx={{ ml: 1, height: 56 }}
                  >
                    Add
                  </Button>
                </Box>
                <List>
                  {tenantFormik.values.customFeatures.map((feature) => (
                    <ListItem key={feature}>
                      <ListItemIcon>
                        <ExtensionIcon />
                      </ListItemIcon>
                      <ListItemText primary={feature} />
                      <ListItemSecondaryAction>
                        <IconButton
                          edge="end"
                          color="error"
                          onClick={() => removeCustomFeature(feature)}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </ListItemSecondaryAction>
                    </ListItem>
                  ))}
                </List>
              </Grid>
              <Grid item xs={12}>
                <Divider sx={{ my: 2 }} />
                <Typography variant="h6">Custom Limits</Typography>
                <Box display="flex" alignItems="center" mt={1}>
                  <TextField
                    label="Limit Key"
                    value={limitKey}
                    onChange={(e) => setLimitKey(e.target.value)}
                    margin="normal"
                    sx={{ mr: 1, flex: 1 }}
                  />
                  <TextField
                    label="Limit Value"
                    type="number"
                    value={limitValue}
                    onChange={(e) => setLimitValue(e.target.value)}
                    margin="normal"
                    sx={{ mr: 1, flex: 1 }}
                  />
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={addCustomLimit}
                    sx={{ height: 56 }}
                  >
                    Add
                  </Button>
                </Box>
                <List>
                  {Object.entries(tenantFormik.values.customLimits).map(([key, value]) => (
                    <ListItem key={key}>
                      <ListItemIcon>
                        <SpeedIcon />
                      </ListItemIcon>
                      <ListItemText
                        primary={key}
                        secondary={`Limit: ${value}`}
                      />
                      <ListItemSecondaryAction>
                        <IconButton
                          edge="end"
                          color="error"
                          onClick={() => removeCustomLimit(key)}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </ListItemSecondaryAction>
                    </ListItem>
                  ))}
                </List>
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setTenantDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              type="submit"
              variant="contained"
              color="primary"
              disabled={loading}
            >
              {editingTenantMapping ? 'Update' : 'Create'}
            </Button>
          </DialogActions>
        </form>
      </Dialog>
    </Box>
  );
};

export default PackageManager;
