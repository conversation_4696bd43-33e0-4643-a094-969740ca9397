# Kali Linux Docker Container for NovaFuse Penetration Testing
FROM kalilinux/kali-rolling

# Set non-interactive mode for apt
ENV DEBIAN_FRONTEND=noninteractive

# Update and install essential tools
RUN apt-get update && apt-get upgrade -y && apt-get install -y \
    kali-tools-top10 \
    metasploit-framework \
    nmap \
    sqlmap \
    dirb \
    nikto \
    hydra \
    john \
    wfuzz \
    burpsuite \
    zaproxy \
    python3-pip \
    git \
    curl \
    wget \
    && apt-get clean

# Install Python tools
RUN pip3 install --upgrade pip && \
    pip3 install \
    requests \
    beautifulsoup4 \
    python-owasp-zap-v2.4 \
    jwt-tool \
    apicheck-package \
    pytest \
    pytest-html

# Install Node.js and npm
RUN curl -sL https://deb.nodesource.com/setup_18.x | bash - && \
    apt-get install -y nodejs && \
    npm install -g npm@latest

# Install API security testing tools
RUN npm install -g \
    @stoplight/prism-cli \
    dredd \
    artillery \
    newman

# Create working directory
WORKDIR /pentest

# Copy scripts
COPY ./tools/security-testing/pentest-scripts/ /pentest/scripts/

# Make scripts executable
RUN chmod +x /pentest/scripts/*.sh

# Set entrypoint
ENTRYPOINT ["/bin/bash"]
