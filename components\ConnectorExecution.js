import React, { useState } from 'react';
import { executeConnector, scheduleConnector } from '../services/connectorExecutionService';

/**
 * Component for executing or scheduling a connector
 * @param {Object} props - Component props
 * @param {Object} props.connector - Connector object
 * @param {Function} props.onExecutionComplete - Callback function called when execution completes
 */
const ConnectorExecution = ({ connector, onExecutionComplete }) => {
  const [isExecuting, setIsExecuting] = useState(false);
  const [isScheduling, setIsScheduling] = useState(false);
  const [showScheduleForm, setShowScheduleForm] = useState(false);
  const [scheduledTime, setScheduledTime] = useState('');
  const [error, setError] = useState('');
  const [result, setResult] = useState(null);

  /**
   * Execute the connector
   */
  const handleExecute = async () => {
    try {
      setError('');
      setIsExecuting(true);
      
      const executionResult = await executeConnector(connector.id);
      
      setResult(executionResult);
      
      if (onExecutionComplete) {
        onExecutionComplete(executionResult);
      }
    } catch (error) {
      setError(error.response?.data?.message || 'Failed to execute connector. Please try again.');
    } finally {
      setIsExecuting(false);
    }
  };

  /**
   * Schedule the connector for execution
   */
  const handleSchedule = async (e) => {
    e.preventDefault();
    
    if (!scheduledTime) {
      setError('Please select a scheduled time');
      return;
    }
    
    try {
      setError('');
      setIsScheduling(true);
      
      const scheduleResult = await scheduleConnector(connector.id, {}, scheduledTime);
      
      setResult({
        scheduled: true,
        jobId: scheduleResult.jobId,
        scheduledTime: scheduleResult.scheduledTime
      });
      
      setShowScheduleForm(false);
      
      if (onExecutionComplete) {
        onExecutionComplete(scheduleResult);
      }
    } catch (error) {
      setError(error.response?.data?.message || 'Failed to schedule connector. Please try again.');
    } finally {
      setIsScheduling(false);
    }
  };

  /**
   * Get minimum date-time for scheduling (now + 5 minutes)
   */
  const getMinScheduleTime = () => {
    const now = new Date();
    now.setMinutes(now.getMinutes() + 5);
    return now.toISOString().slice(0, 16); // Format: YYYY-MM-DDTHH:MM
  };

  return (
    <div className="bg-secondary rounded-lg p-6">
      {error && (
        <div className="bg-red-900 bg-opacity-30 border border-red-700 rounded-lg p-4 mb-4 text-red-400">
          {error}
        </div>
      )}
      
      {result && (
        <div className="bg-green-900 bg-opacity-30 border border-green-700 rounded-lg p-4 mb-4">
          {result.scheduled ? (
            <div>
              <h3 className="text-lg font-semibold mb-2 text-green-400">Connector Scheduled</h3>
              <p className="text-gray-300 mb-2">
                The connector has been scheduled for execution at {new Date(result.scheduledTime).toLocaleString()}.
              </p>
              <p className="text-gray-300">
                Job ID: {result.jobId}
              </p>
            </div>
          ) : (
            <div>
              <h3 className="text-lg font-semibold mb-2 text-green-400">Execution Complete</h3>
              <p className="text-gray-300 mb-2">
                Status: <span className={`font-semibold ${
                  result.status === 'success' ? 'text-green-400' :
                  result.status === 'warning' ? 'text-yellow-400' : 'text-red-400'
                }`}>{result.status}</span>
              </p>
              {result.findings && (
                <p className="text-gray-300 mb-2">
                  Findings: {result.findings.length}
                </p>
              )}
              {result.criticalFindings && result.criticalFindings.length > 0 && (
                <p className="text-red-400 mb-2">
                  Critical Findings: {result.criticalFindings.length}
                </p>
              )}
              <p className="text-gray-300">
                Executed at: {new Date(result.timestamp).toLocaleString()}
              </p>
            </div>
          )}
        </div>
      )}
      
      {showScheduleForm ? (
        <form onSubmit={handleSchedule} className="mb-4">
          <div className="mb-4">
            <label htmlFor="scheduled-time" className="block text-sm font-medium mb-1">
              Schedule Time
            </label>
            <input
              type="datetime-local"
              id="scheduled-time"
              className="w-full bg-gray-800 border border-gray-700 rounded-lg py-2 px-3 focus:outline-none focus:ring-2 focus:ring-blue-600"
              min={getMinScheduleTime()}
              value={scheduledTime}
              onChange={(e) => setScheduledTime(e.target.value)}
              required
            />
          </div>
          
          <div className="flex space-x-2">
            <button
              type="submit"
              className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={isScheduling}
            >
              {isScheduling ? (
                <span className="flex items-center">
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Scheduling...
                </span>
              ) : 'Schedule'}
            </button>
            
            <button
              type="button"
              className="border border-gray-600 text-gray-300 hover:bg-gray-800 font-bold py-2 px-4 rounded-lg"
              onClick={() => setShowScheduleForm(false)}
              disabled={isScheduling}
            >
              Cancel
            </button>
          </div>
        </form>
      ) : (
        <div className="flex space-x-2">
          <button
            className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
            onClick={handleExecute}
            disabled={isExecuting}
          >
            {isExecuting ? (
              <span className="flex items-center">
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Executing...
              </span>
            ) : 'Execute Now'}
          </button>
          
          <button
            className="border border-blue-600 text-blue-500 hover:bg-blue-900 hover:bg-opacity-20 font-bold py-2 px-4 rounded-lg"
            onClick={() => setShowScheduleForm(true)}
            disabled={isExecuting}
          >
            Schedule
          </button>
        </div>
      )}
    </div>
  );
};

export default ConnectorExecution;
