version: '3.8'

services:
  ai-alignment-dashboard:
    build:
      context: ./ai-alignment-demo
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
    restart: unless-stopped
    networks:
      - ai-alignment-network

  sacred-engine:
    build:
      context: ./sacred
      dockerfile: Dockerfile
    volumes:
      - ./sacred:/app
    environment:
      - PYTHONUNBUFFERED=1
    restart: unless-stopped
    networks:
      - ai-alignment-network

networks:
  ai-alignment-network:
    driver: bridge
