const { v4: uuidv4 } = require('uuid');
const models = require('./models');

/**
 * Get a list of ESG reports
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getReports = (req, res) => {
  try {
    const { page = 1, limit = 10, reportType, status, framework, startDate, endDate, sortBy = 'createdAt', sortOrder = 'desc' } = req.query;
    
    // Convert page and limit to numbers
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);
    
    // Filter reports based on query parameters
    let filteredReports = [...models.esgReports];
    
    if (reportType) {
      filteredReports = filteredReports.filter(report => report.reportType === reportType);
    }
    
    if (status) {
      filteredReports = filteredReports.filter(report => report.status === status);
    }
    
    if (framework) {
      filteredReports = filteredReports.filter(report => 
        report.frameworks && report.frameworks.includes(framework)
      );
    }
    
    if (startDate) {
      filteredReports = filteredReports.filter(report => 
        report.reportingPeriod.startDate >= startDate
      );
    }
    
    if (endDate) {
      filteredReports = filteredReports.filter(report => 
        report.reportingPeriod.endDate <= endDate
      );
    }
    
    // Sort reports
    filteredReports.sort((a, b) => {
      let aValue = a[sortBy];
      let bValue = b[sortBy];
      
      // Handle nested properties like reportingPeriod.startDate
      if (sortBy.includes('.')) {
        const parts = sortBy.split('.');
        aValue = parts.reduce((obj, key) => obj && obj[key], a);
        bValue = parts.reduce((obj, key) => obj && obj[key], b);
      }
      
      if (sortOrder.toLowerCase() === 'desc') {
        return aValue > bValue ? -1 : 1;
      }
      return aValue > bValue ? 1 : -1;
    });
    
    // Paginate results
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = pageNum * limitNum;
    const paginatedReports = filteredReports.slice(startIndex, endIndex);
    
    // Calculate pagination info
    const totalReports = filteredReports.length;
    const totalPages = Math.ceil(totalReports / limitNum);
    
    res.json({
      data: paginatedReports,
      pagination: {
        total: totalReports,
        page: pageNum,
        limit: limitNum,
        pages: totalPages
      }
    });
  } catch (error) {
    console.error('Error in getReports:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get a specific ESG report by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getReportById = (req, res) => {
  try {
    const { id } = req.params;
    const report = models.esgReports.find(r => r.id === id);
    
    if (!report) {
      return res.status(404).json({
        error: 'Not Found',
        message: `ESG report with ID ${id} not found`
      });
    }
    
    res.json({ data: report });
  } catch (error) {
    console.error('Error in getReportById:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Create a new ESG report
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const createReport = (req, res) => {
  try {
    const { 
      title, 
      description, 
      reportType, 
      reportingPeriod, 
      frameworks, 
      metrics, 
      sections, 
      attachments 
    } = req.body;
    
    // Create a new report with a unique ID
    const newReport = {
      id: `rep-${uuidv4().substring(0, 8)}`,
      title,
      description: description || '',
      reportType,
      reportingPeriod,
      status: 'draft',
      frameworks: frameworks || [],
      metrics: metrics || [],
      sections: sections || [],
      attachments: attachments || [],
      createdBy: req.user?.id || 'system', // Assuming user info is available in req.user
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      publishedAt: null
    };
    
    // Add the new report to the collection
    models.esgReports.push(newReport);
    
    res.status(201).json({
      data: newReport,
      message: 'ESG report created successfully'
    });
  } catch (error) {
    console.error('Error in createReport:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Update an existing ESG report
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const updateReport = (req, res) => {
  try {
    const { id } = req.params;
    const { 
      title, 
      description, 
      reportType, 
      reportingPeriod, 
      status, 
      frameworks, 
      metrics, 
      sections, 
      attachments 
    } = req.body;
    
    // Find the report to update
    const reportIndex = models.esgReports.findIndex(r => r.id === id);
    
    if (reportIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `ESG report with ID ${id} not found`
      });
    }
    
    // Check if trying to update a published report
    const currentReport = models.esgReports[reportIndex];
    if (currentReport.status === 'published' && status !== 'archived') {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Published reports cannot be modified. You can only archive them.'
      });
    }
    
    // Update the report
    const updatedReport = {
      ...currentReport,
      title: title || currentReport.title,
      description: description !== undefined ? description : currentReport.description,
      reportType: reportType || currentReport.reportType,
      reportingPeriod: reportingPeriod || currentReport.reportingPeriod,
      status: status || currentReport.status,
      frameworks: frameworks || currentReport.frameworks,
      metrics: metrics || currentReport.metrics,
      sections: sections || currentReport.sections,
      attachments: attachments || currentReport.attachments,
      updatedAt: new Date().toISOString()
    };
    
    // Replace the old report with the updated one
    models.esgReports[reportIndex] = updatedReport;
    
    res.json({
      data: updatedReport,
      message: 'ESG report updated successfully'
    });
  } catch (error) {
    console.error('Error in updateReport:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Delete an ESG report
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const deleteReport = (req, res) => {
  try {
    const { id } = req.params;
    
    // Find the report to delete
    const reportIndex = models.esgReports.findIndex(r => r.id === id);
    
    if (reportIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `ESG report with ID ${id} not found`
      });
    }
    
    // Check if trying to delete a published report
    const report = models.esgReports[reportIndex];
    if (report.status === 'published') {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Published reports cannot be deleted. Archive them instead.'
      });
    }
    
    // Remove the report from the collection
    models.esgReports.splice(reportIndex, 1);
    
    res.json({
      message: 'ESG report deleted successfully'
    });
  } catch (error) {
    console.error('Error in deleteReport:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Publish an ESG report
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const publishReport = (req, res) => {
  try {
    const { id } = req.params;
    const { publishedAt } = req.body;
    
    // Find the report to publish
    const reportIndex = models.esgReports.findIndex(r => r.id === id);
    
    if (reportIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `ESG report with ID ${id} not found`
      });
    }
    
    // Check if the report is already published
    const report = models.esgReports[reportIndex];
    if (report.status === 'published') {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Report is already published'
      });
    }
    
    // Update the report status and published date
    const updatedReport = {
      ...report,
      status: 'published',
      publishedAt: publishedAt || new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    // Replace the old report with the updated one
    models.esgReports[reportIndex] = updatedReport;
    
    res.json({
      data: updatedReport,
      message: 'ESG report published successfully'
    });
  } catch (error) {
    console.error('Error in publishReport:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get a list of report templates
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getTemplates = (req, res) => {
  try {
    const { page = 1, limit = 10, reportType, framework, sortBy = 'name', sortOrder = 'asc' } = req.query;
    
    // Convert page and limit to numbers
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);
    
    // Filter templates based on query parameters
    let filteredTemplates = [...models.reportTemplates];
    
    if (reportType) {
      filteredTemplates = filteredTemplates.filter(template => template.reportType === reportType);
    }
    
    if (framework) {
      filteredTemplates = filteredTemplates.filter(template => 
        template.frameworks && template.frameworks.includes(framework)
      );
    }
    
    // Sort templates
    filteredTemplates.sort((a, b) => {
      if (sortOrder.toLowerCase() === 'desc') {
        return a[sortBy] > b[sortBy] ? -1 : 1;
      }
      return a[sortBy] > b[sortBy] ? 1 : -1;
    });
    
    // Paginate results
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = pageNum * limitNum;
    const paginatedTemplates = filteredTemplates.slice(startIndex, endIndex);
    
    // Calculate pagination info
    const totalTemplates = filteredTemplates.length;
    const totalPages = Math.ceil(totalTemplates / limitNum);
    
    res.json({
      data: paginatedTemplates,
      pagination: {
        total: totalTemplates,
        page: pageNum,
        limit: limitNum,
        pages: totalPages
      }
    });
  } catch (error) {
    console.error('Error in getTemplates:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get a specific report template by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getTemplateById = (req, res) => {
  try {
    const { id } = req.params;
    const template = models.reportTemplates.find(t => t.id === id);
    
    if (!template) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Report template with ID ${id} not found`
      });
    }
    
    res.json({ data: template });
  } catch (error) {
    console.error('Error in getTemplateById:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Create a new report template
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const createTemplate = (req, res) => {
  try {
    const { 
      name, 
      description, 
      reportType, 
      frameworks, 
      structure, 
      isDefault 
    } = req.body;
    
    // Create a new template with a unique ID
    const newTemplate = {
      id: `tpl-${uuidv4().substring(0, 8)}`,
      name,
      description: description || '',
      reportType,
      frameworks: frameworks || [],
      structure,
      isDefault: isDefault || false,
      createdBy: req.user?.id || 'system', // Assuming user info is available in req.user
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    // If this is set as a default template, update other templates of the same type
    if (newTemplate.isDefault) {
      models.reportTemplates.forEach((template, index) => {
        if (template.reportType === newTemplate.reportType && template.isDefault) {
          models.reportTemplates[index] = {
            ...template,
            isDefault: false,
            updatedAt: new Date().toISOString()
          };
        }
      });
    }
    
    // Add the new template to the collection
    models.reportTemplates.push(newTemplate);
    
    res.status(201).json({
      data: newTemplate,
      message: 'Report template created successfully'
    });
  } catch (error) {
    console.error('Error in createTemplate:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Update an existing report template
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const updateTemplate = (req, res) => {
  try {
    const { id } = req.params;
    const { 
      name, 
      description, 
      reportType, 
      frameworks, 
      structure, 
      isDefault 
    } = req.body;
    
    // Find the template to update
    const templateIndex = models.reportTemplates.findIndex(t => t.id === id);
    
    if (templateIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Report template with ID ${id} not found`
      });
    }
    
    const currentTemplate = models.reportTemplates[templateIndex];
    
    // Update the template
    const updatedTemplate = {
      ...currentTemplate,
      name: name || currentTemplate.name,
      description: description !== undefined ? description : currentTemplate.description,
      reportType: reportType || currentTemplate.reportType,
      frameworks: frameworks || currentTemplate.frameworks,
      structure: structure || currentTemplate.structure,
      isDefault: isDefault !== undefined ? isDefault : currentTemplate.isDefault,
      updatedAt: new Date().toISOString()
    };
    
    // If this is set as a default template, update other templates of the same type
    if (updatedTemplate.isDefault && !currentTemplate.isDefault) {
      models.reportTemplates.forEach((template, index) => {
        if (template.id !== id && template.reportType === updatedTemplate.reportType && template.isDefault) {
          models.reportTemplates[index] = {
            ...template,
            isDefault: false,
            updatedAt: new Date().toISOString()
          };
        }
      });
    }
    
    // Replace the old template with the updated one
    models.reportTemplates[templateIndex] = updatedTemplate;
    
    res.json({
      data: updatedTemplate,
      message: 'Report template updated successfully'
    });
  } catch (error) {
    console.error('Error in updateTemplate:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Delete a report template
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const deleteTemplate = (req, res) => {
  try {
    const { id } = req.params;
    
    // Find the template to delete
    const templateIndex = models.reportTemplates.findIndex(t => t.id === id);
    
    if (templateIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Report template with ID ${id} not found`
      });
    }
    
    // Check if it's a default template
    const template = models.reportTemplates[templateIndex];
    if (template.isDefault) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Default templates cannot be deleted'
      });
    }
    
    // Remove the template from the collection
    models.reportTemplates.splice(templateIndex, 1);
    
    res.json({
      message: 'Report template deleted successfully'
    });
  } catch (error) {
    console.error('Error in deleteTemplate:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Create a report from a template
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const createReportFromTemplate = (req, res) => {
  try {
    const { templateId } = req.params;
    const { title, reportingPeriod } = req.body;
    
    // Find the template
    const template = models.reportTemplates.find(t => t.id === templateId);
    
    if (!template) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Report template with ID ${templateId} not found`
      });
    }
    
    // Create sections from template structure
    const sections = template.structure.map(item => ({
      title: item.title,
      content: item.description || '',
      order: item.order
    }));
    
    // Create a new report with a unique ID
    const newReport = {
      id: `rep-${uuidv4().substring(0, 8)}`,
      title: title || `${template.name} - ${new Date().toISOString().split('T')[0]}`,
      description: template.description,
      reportType: template.reportType,
      reportingPeriod: reportingPeriod || {
        startDate: new Date().toISOString().split('T')[0],
        endDate: new Date().toISOString().split('T')[0]
      },
      status: 'draft',
      frameworks: template.frameworks,
      metrics: [],
      sections,
      attachments: [],
      createdBy: req.user?.id || 'system', // Assuming user info is available in req.user
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      publishedAt: null
    };
    
    // Add the new report to the collection
    models.esgReports.push(newReport);
    
    res.status(201).json({
      data: newReport,
      message: 'ESG report created from template successfully'
    });
  } catch (error) {
    console.error('Error in createReportFromTemplate:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

module.exports = {
  getReports,
  getReportById,
  createReport,
  updateReport,
  deleteReport,
  publishReport,
  getTemplates,
  getTemplateById,
  createTemplate,
  updateTemplate,
  deleteTemplate,
  createReportFromTemplate
};
