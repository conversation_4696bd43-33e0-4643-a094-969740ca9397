const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Create SVG directory if it doesn't exist
const svgDir = path.join(__dirname, 'svg');
if (!fs.existsSync(svgDir)) {
    fs.mkdirSync(svgDir);
}

// Get all .mmd files
const mermaidDir = path.join(__dirname, 'mermaid_diagrams');
const mermaidFiles = fs.readdirSync(mermaidDir)
    .filter(file => file.endsWith('.mmd'))
    .map(file => ({
        input: path.join(mermaidDir, file),
        output: path.join(svgDir, file.replace(/\.mmd$/, '.svg'))
    }));

// Convert each file
mermaidFiles.forEach(({ input, output }) => {
    try {
        // Use mmdc (Mermaid CLI) to convert .mmd to .svg
        execSync(`npx @mermaid-js/mermaid-cli -i "${input}" -o "${output}"`);
        console.log(`✅ Converted: ${path.basename(input)} -> ${path.basename(output)}`);
    } catch (error) {
        console.error(`❌ Error converting ${path.basename(input)}:`, error.message);
    }
});

console.log('\nConversion complete!');
