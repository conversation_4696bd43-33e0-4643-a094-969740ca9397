/**
 * Helmet CSP Configuration Check
 * 
 * This script checks for proper Content Security Policy (CSP) configuration using Helmet.
 * It analyzes the server.js and other relevant files to ensure that Helm<PERSON> is properly configured.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Files to check
const filesToCheck = [
  path.join(__dirname, '..', 'server.js'),
  path.join(__dirname, '..', 'app.js'),
  path.join(__dirname, '..', 'index.js'),
  path.join(__dirname, '..', 'api', 'connector-api.js')
];

// Required Helmet middlewares
const requiredMiddlewares = [
  'helmet()',
  'helmet.contentSecurityPolicy(',
  'helmet.xssFilter()',
  'helmet.frameguard()',
  'helmet.hsts(',
  'helmet.noSniff()',
  'helmet.dnsPrefetchControl()',
  'helmet.referrerPolicy('
];

// CSP directives that should be set
const requiredCSPDirectives = [
  'default-src',
  'script-src',
  'style-src',
  'img-src',
  'connect-src',
  'font-src',
  'object-src',
  'frame-src'
];

// Results
const results = {
  helmetFound: false,
  helmetImportFound: false,
  helmetConfigured: false,
  cspConfigured: false,
  missingMiddlewares: [],
  missingCSPDirectives: [],
  issues: []
};

// Check if Helmet is installed
try {
  execSync('npm list helmet', { stdio: 'pipe' });
  results.helmetFound = true;
} catch (error) {
  results.issues.push('Helmet is not installed. Run: npm install helmet --save');
}

// Check files for Helmet configuration
for (const file of filesToCheck) {
  if (fs.existsSync(file)) {
    const content = fs.readFileSync(file, 'utf8');
    
    // Check for Helmet import
    if (content.includes('require(\'helmet\')') || content.includes('require("helmet")') || 
        content.includes('from \'helmet\'') || content.includes('from "helmet"')) {
      results.helmetImportFound = true;
    }
    
    // Check for Helmet usage
    if (content.includes('app.use(helmet') || content.includes('app.use(helmet.')) {
      results.helmetConfigured = true;
      
      // Check for specific middlewares
      for (const middleware of requiredMiddlewares) {
        if (!content.includes(middleware)) {
          results.missingMiddlewares.push(middleware);
        }
      }
      
      // Check for CSP configuration
      if (content.includes('contentSecurityPolicy')) {
        results.cspConfigured = true;
        
        // Check for required CSP directives
        for (const directive of requiredCSPDirectives) {
          if (!content.includes(directive)) {
            results.missingCSPDirectives.push(directive);
          }
        }
      }
    }
  }
}

// Remove duplicates from missing middlewares and directives
results.missingMiddlewares = [...new Set(results.missingMiddlewares)];
results.missingCSPDirectives = [...new Set(results.missingCSPDirectives)];

// Generate report
let report = '# Helmet CSP Configuration Check\n\n';

if (!results.helmetFound) {
  report += '❌ Helmet is not installed\n';
} else {
  report += '✅ Helmet is installed\n';
}

if (!results.helmetImportFound) {
  report += '❌ Helmet import not found in any checked files\n';
} else {
  report += '✅ Helmet import found\n';
}

if (!results.helmetConfigured) {
  report += '❌ Helmet is not configured\n';
} else {
  report += '✅ Helmet is configured\n';
}

if (!results.cspConfigured) {
  report += '❌ Content Security Policy is not configured\n';
} else {
  report += '✅ Content Security Policy is configured\n';
}

if (results.missingMiddlewares.length > 0) {
  report += '\n## Missing Helmet Middlewares\n\n';
  for (const middleware of results.missingMiddlewares) {
    report += `- ${middleware}\n`;
  }
}

if (results.missingCSPDirectives.length > 0) {
  report += '\n## Missing CSP Directives\n\n';
  for (const directive of results.missingCSPDirectives) {
    report += `- ${directive}\n`;
  }
}

if (results.issues.length > 0) {
  report += '\n## Issues\n\n';
  for (const issue of results.issues) {
    report += `- ${issue}\n`;
  }
}

// Recommended configuration
report += '\n## Recommended Helmet Configuration\n\n';
report += '```javascript\napp.use(\n  helmet({\n    contentSecurityPolicy: {\n      directives: {\n';
report += '        defaultSrc: ["\'self\'"],\n';
report += '        scriptSrc: ["\'self\'", "\'unsafe-inline\'", "\'unsafe-eval\'", "https://cdn.jsdelivr.net"],\n';
report += '        styleSrc: ["\'self\'", "\'unsafe-inline\'", "https://cdn.jsdelivr.net"],\n';
report += '        imgSrc: ["\'self\'", "data:", "https://cdn.jsdelivr.net"],\n';
report += '        connectSrc: ["\'self\'", "https://api.example.com"],\n';
report += '        fontSrc: ["\'self\'", "https://cdn.jsdelivr.net"],\n';
report += '        objectSrc: ["\'none\'"],\n';
report += '        frameSrc: ["\'self\'"],\n';
report += '        upgradeInsecureRequests: [],\n';
report += '      },\n';
report += '    },\n';
report += '    xssFilter: true,\n';
report += '    frameguard: {\n      action: "deny"\n    },\n';
report += '    hsts: {\n      maxAge: 31536000,\n      includeSubDomains: true,\n      preload: true\n    },\n';
report += '    noSniff: true,\n';
report += '    dnsPrefetchControl: {\n      allow: false\n    },\n';
report += '    referrerPolicy: {\n      policy: "same-origin"\n    }\n';
report += '  })\n);\n```\n';

// Output report
console.log(report);

// Exit with appropriate code
if (!results.helmetFound || !results.helmetConfigured || !results.cspConfigured || 
    results.missingMiddlewares.length > 0 || results.missingCSPDirectives.length > 0) {
  process.exit(1);
} else {
  process.exit(0);
}
