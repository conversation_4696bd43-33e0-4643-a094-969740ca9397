import React from 'react';

const EthicalImpactMap = ({ products }) => {
  if (!products || products.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-600">No products to display</p>
      </div>
    );
  }

  return (
    <div className="bg-white p-6 rounded-lg shadow">
      <h2 className="text-xl font-bold mb-4">Ethical Impact Map</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {products.map((product, index) => (
          <div key={index} className="border rounded-lg p-4">
            <h3 className="font-semibold mb-2">{product.name}</h3>
            <div className="space-y-2">
              <div>
                <span className="font-medium">Trust Score:</span>
                <span className="ml-2">{product.trust_score.toFixed(2)}</span>
              </div>
              <div>
                <span className="font-medium">Engagement Boost:</span>
                <span className="ml-2">{product.engagement_boost.toFixed(2)}</span>
              </div>
              <div>
                <span className="font-medium">Virality Potential:</span>
                <span className="ml-2">{product.virality_potential.toFixed(2)}</span>
              </div>
              <div>
                <span className="font-medium">ROI Impact:</span>
                <span className="ml-2">{product.ethical_roi.toFixed(2)}%</span>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default EthicalImpactMap;
