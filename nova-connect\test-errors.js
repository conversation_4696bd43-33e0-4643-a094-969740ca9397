/**
 * Simple test script for the error handling framework
 */

try {
  console.log('Loading error classes...');
  const { 
    UAConnectorError,
    AuthenticationError,
    ConnectionError,
    ValidationError,
    ApiError,
    TransformationError,
    ConnectorError
  } = require('./src/errors');

  console.log('Testing error classes...');

  // Test UAConnectorError
  console.log('\n--- Testing UAConnectorError ---');
  const baseError = new UAConnectorError('Test error');
  console.log('Base error created:', baseError instanceof Error);
  console.log('Error name:', baseError.name);
  console.log('Error message:', baseError.message);
  console.log('Error code:', baseError.code);
  console.log('Error ID:', baseError.errorId);
  console.log('User message:', baseError.getUserMessage());
  console.log('Developer message:', baseError.getDeveloperMessage());
  console.log('JSON representation:', JSON.stringify(baseError.toJSON(), null, 2));

  // Test AuthenticationError
  console.log('\n--- Testing AuthenticationError ---');
  const authError = new AuthenticationError('Auth failed');
  console.log('Auth error created:', authError instanceof UAConnectorError);
  console.log('Error name:', authError.name);
  console.log('Error code:', authError.code);
  console.log('User message:', authError.getUserMessage());

  // Test ConnectionError
  console.log('\n--- Testing ConnectionError ---');
  const connError = new ConnectionError('Connection failed');
  console.log('Connection error created:', connError instanceof UAConnectorError);
  console.log('Error name:', connError.name);
  console.log('Error code:', connError.code);
  console.log('User message:', connError.getUserMessage());

  // Test ValidationError
  console.log('\n--- Testing ValidationError ---');
  const validationError = new ValidationError('Validation failed', {
    validationErrors: [
      { field: 'name', message: 'Name is required' },
      { field: 'email', message: 'Email is invalid' }
    ]
  });
  console.log('Validation error created:', validationError instanceof UAConnectorError);
  console.log('Error name:', validationError.name);
  console.log('Error code:', validationError.code);
  console.log('User message:', validationError.getUserMessage());
  console.log('Validation errors:', validationError.validationErrors);

  // Test ApiError
  console.log('\n--- Testing ApiError ---');
  const apiError = new ApiError('API error', {
    statusCode: 400,
    response: { message: 'Bad request' }
  });
  console.log('API error created:', apiError instanceof UAConnectorError);
  console.log('Error name:', apiError.name);
  console.log('Error code:', apiError.code);
  console.log('Status code:', apiError.statusCode);
  console.log('User message:', apiError.getUserMessage());

  // Test TransformationError
  console.log('\n--- Testing TransformationError ---');
  const transformError = new TransformationError('Transformation failed', {
    transformationId: 'user-transform'
  });
  console.log('Transformation error created:', transformError instanceof UAConnectorError);
  console.log('Error name:', transformError.name);
  console.log('Error code:', transformError.code);
  console.log('User message:', transformError.getUserMessage());

  // Test ConnectorError
  console.log('\n--- Testing ConnectorError ---');
  const connectorError = new ConnectorError('Connector error', {
    connectorId: 'salesforce'
  });
  console.log('Connector error created:', connectorError instanceof UAConnectorError);
  console.log('Error name:', connectorError.name);
  console.log('Error code:', connectorError.code);
  console.log('User message:', connectorError.getUserMessage());

  console.log('\nAll tests passed!');
} catch (error) {
  console.error('Test failed:', error);
}
