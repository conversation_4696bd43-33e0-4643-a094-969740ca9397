<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Comphyology Mathematical Framework</title>
    <style>
        /* Reset and Base Styles */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            line-height: 1.4;
        }
        
        /* Main Diagram Container */
        .diagram-container {
            position: relative;
            width: 950px;
            min-height: 900px; /* Increased minimum height */
            margin: 0 auto 40px;
            background-color: white;
            border: 1px solid #ddd;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            box-sizing: border-box;
            overflow: visible; /* Allow content to be fully visible */
        }
        
        /* Container Box Styles */
        .container-box {
            position: absolute;
            border: 2px solid #666;
            border-radius: 10px;
            padding: 30px 10px 10px;
            overflow: visible; /* Allow content to be fully visible */
            min-height: 100px; /* Minimum height */
            min-width: 200px; /* Minimum width */
            z-index: 5; /* Bring containers in front of connecting lines */
            background-color: white; /* Ensure background is opaque */
        }
        
        .container-label {
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            font-weight: bold;
            font-size: 16px;
            text-align: center;
            width: 90%; /* Ensures text doesn't overflow container */
            white-space: normal; /* Allows text to wrap */
            overflow: visible; /* Allow content to be fully visible */
            z-index: 10; /* Ensure labels are in front of connecting lines */
            background-color: white; /* Add background to hide lines behind text */
            padding: 0 5px; /* Add padding around text */
        }
        
        /* Component Box Styles */
        .component-box {
            position: absolute;
            background-color: white;
            border: 2px solid #333;
            border-radius: 6px;
            display: flex;
            flex-direction: column;
            justify-content: center; /* Center content vertically */
            align-items: center;
            text-align: center;
            padding: 5px; /* Reduced padding */
            overflow: visible; /* Allow content to be fully visible */
            border-top-left-radius: 0; /* Flat corner for number */
            min-height: 50px; /* Minimum height */
            min-width: 100px; /* Minimum width */
        }
        
        /* Component Number Styles - Integrated into corner */
        .component-number-inside {
            position: absolute;
            top: 0;
            left: 0;
            width: 18px;
            height: 18px;
            background-color: #000000; /* Black background for component numbers */
            color: white;
            border-radius: 0 0 6px 0; /* Rounded on bottom-right corner only */
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 10px; /* Smaller font */
            font-weight: bold;
            z-index: 2;
            margin: 0; /* Remove any margin */
            padding: 0; /* Remove any padding */
        }
        
        .component-label {
            font-weight: bold;
            margin-top: 10px; /* Reduced margin */
            margin-bottom: 2px; /* Reduced margin */
            font-size: 12px; /* Smaller font */
            text-align: center;
            width: 100%;
            white-space: normal; /* Allows text to wrap */
            overflow: visible; /* Allow content to be fully visible */
        }
        
        .component-content {
            font-size: 10px; /* Smaller font */
            text-align: center;
            width: 100%;
            padding: 0;
            white-space: normal; /* Allows text to wrap */
            overflow: visible; /* Allow content to be fully visible */
            line-height: 1.2; /* Tighter line spacing */
        }
        
        /* Arrow Styles */
        .arrow {
            position: absolute;
            background-color: #555555; /* Grey color for patent compliance */
            width: 2px;
            z-index: 1; /* Ensure arrows are behind containers but visible */
        }
        
        /* Legend Styles */
        .legend {
            position: absolute;
            right: 10px;
            bottom: 30px;
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 8px;
            z-index: 10;
            width: 200px;
            font-size: 12px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 6px;
        }
        
        .legend-color {
            width: 15px;
            height: 15px;
            margin-right: 8px;
            border: 1px solid #333;
            flex-shrink: 0; /* Prevents the color box from shrinking */
        }
        
        /* Inventor Label */
        .inventor-label {
            position: absolute;
            left: 10px;
            bottom: 30px;
            font-size: 12px;
            font-style: italic;
            color: #333;
            z-index: 10;
        }
        
        /* Title Styles */
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 20px;
            color: #333;
        }
    </style>
</head>
<body>

    <h1>FIG. 14: Comphyology Mathematical Framework</h1>

    <div class="diagram-container">
        <!-- Main Container -->
        <div class="container-box" style="width: 750px; height: 800px; left: 25px; top: 20px;">
            <div class="container-label" style="font-size: 18px;">COMPHYOLOGY MATHEMATICAL FRAMEWORK: "NEWTON FOR NETWORKS"</div>
        </div>

        <!-- Finite Universe Equation -->
        <div class="component-box" style="left: 275px; top: 80px; width: 250px; height: 110px;">
            <div class="component-number-inside">1</div>
            <div class="component-label">Finite Universe Equation (FUE)</div>
            <div class="component-content">
                U = T[∑(n=1 to 5) Sn⋅(En+In)⋅Φn]
                <br>
                <i>Meta-theoretical framework for complex systems</i>
            </div>
        </div>

        <!-- Foundational Axioms -->
        <div class="component-box" style="left: 75px; top: 200px; width: 200px; height: 130px;">
            <div class="component-number-inside">2</div>
            <div class="component-label">Foundational Axioms</div>
            <div class="component-content">
                • Finite Boundedness: ∂U=0
                <br>
                • Energy-Information Equivalence
                <br>
                • Nested Symmetry: Sn+1=∇×Sn
                <br>
                • Tensorial Governance
            </div>
        </div>

        <!-- Tensor Governance -->
        <div class="component-box" style="left: 300px; top: 200px; width: 200px; height: 130px;">
            <div class="component-number-inside">3</div>
            <div class="component-label">Tensor Governance (T)</div>
            <div class="component-content">
                <div class="equation">T = (A⊗B⊕C) × π10³</div>
                <br>
                Transforms chaotic multi-agent systems into stable, bounded networks
            </div>
        </div>

        <!-- 5 Nested Layers -->
        <div class="component-box" style="left: 525px; top: 200px; width: 200px; height: 130px;">
            <div class="component-number-inside">4</div>
            <div class="component-label">5 Nested Layers (Sn)</div>
            <div class="component-content">
                • Technology
                <br>
                • Government
                <br>
                • Environment
                <br>
                • Society
                <br>
                • Economy
            </div>
        </div>

        <!-- NovaFuse Implementation -->
        <div class="component-box" style="left: 75px; top: 340px; width: 200px; height: 130px;">
            <div class="component-number-inside">5</div>
            <div class="component-label">NovaFuse Implementation</div>
            <div class="component-content">
                • NovaCore: T-tensor processor
                <br>
                • NovaShield: En detection
                <br>
                • NovaTrack: Sn-layer mapping
                <br>
                • NovaProof: Φn calculation
            </div>
        </div>

        <!-- Application Domains -->
        <div class="component-box" style="left: 300px; top: 340px; width: 200px; height: 130px;">
            <div class="component-number-inside">6</div>
            <div class="component-label">Application Domains</div>
            <div class="component-content">
                • Cyber-resilience
                <br>
                • Multi-agent networks
                <br>
                • Market stability
                <br>
                • Policy governance
                <br>
                • Ecosystem management
            </div>
        </div>

        <!-- Performance Metrics -->
        <div class="component-box" style="left: 525px; top: 340px; width: 200px; height: 130px;">
            <div class="component-number-inside">7</div>
            <div class="component-label">Performance Metrics</div>
            <div class="component-content">
                • 3,142x faster threat detection
                <br>
                • 95% accuracy in complex systems
                <br>
                • O(N log N) computational efficiency
                <br>
                • Bounded error propagation
            </div>
        </div>

        <!-- Universal Pattern Elements -->
        <div class="container-box" style="width: 650px; height: 60px; left: 75px; top: 510px;">
            <div class="container-label" style="font-size: 14px; top: 5px;">PHILOSOPHICAL GROUNDING</div>
        </div>

        <div style="position: absolute; left: 100px; top: 530px; display: flex; justify-content: space-between; width: 600px;">
            <div style="font-size: 11px; text-align: center; width: 600px; padding-top: 5px;">
                "All bounded complex systems (networks, markets, ecosystems) exhibit UUFT-governed stability when ∂U=0."
            </div>
        </div>

        <!-- Legend -->
        <div class="legend">
            <div class="legend-item">
                <div class="legend-color" style="background-color: #fff;"></div>
                <div>Mathematical Framework</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #fff;"></div>
                <div>Implementation Components</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #fff;"></div>
                <div>Application Domains</div>
            </div>
        </div>

        <!-- Inventor Label -->
        <div class="inventor-label">Inventor: David Nigel Irvin</div>
    </div>

</body>
</html>
