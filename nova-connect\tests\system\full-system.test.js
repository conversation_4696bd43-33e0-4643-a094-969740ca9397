/**
 * NovaConnect Full System Test
 * 
 * This test validates the end-to-end performance of the NovaConnect system,
 * including data normalization, remediation workflows, and integration with
 * Google Cloud services.
 * 
 * NOTE: This test requires a running NovaConnect API server.
 * Set the SYSTEM_TEST_API_URL environment variable to point to the API server.
 */

const axios = require('axios');
const { performance } = require('perf_hooks');
const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  apiUrl: process.env.SYSTEM_TEST_API_URL || 'http://localhost:3000',
  apiKey: process.env.SYSTEM_TEST_API_KEY || 'test-api-key',
  outputDir: path.join(__dirname, '../../test-results/system'),
  testDataDir: path.join(__dirname, '../data'),
  normalizationBatchSize: 100,
  remediationConcurrency: 10,
  maxConcurrentRequests: 50
};

// Ensure output directory exists
if (!fs.existsSync(config.outputDir)) {
  fs.mkdirSync(config.outputDir, { recursive: true });
}

// Create API client
const apiClient = axios.create({
  baseURL: config.apiUrl,
  headers: {
    'Authorization': `Bearer ${config.apiKey}`,
    'Content-Type': 'application/json'
  }
});

describe('NovaConnect Full System Test', () => {
  // Skip tests if API is not available
  let apiAvailable = false;
  
  beforeAll(async () => {
    try {
      // Check if API is available
      await apiClient.get('/health');
      apiAvailable = true;
    } catch (error) {
      console.warn(`API not available at ${config.apiUrl}. Skipping system tests.`);
    }
  });
  
  // Skip tests if API is not available
  const conditionalTest = apiAvailable ? it : it.skip;
  
  conditionalTest('should normalize data at high throughput', async () => {
    // Load test data
    const testDataPath = path.join(config.testDataDir, 'scc-findings.json');
    let testData;
    
    try {
      testData = JSON.parse(fs.readFileSync(testDataPath, 'utf8'));
    } catch (error) {
      // Generate test data if file doesn't exist
      testData = generateTestData(1000);
      fs.writeFileSync(testDataPath, JSON.stringify(testData, null, 2));
    }
    
    // Split data into batches
    const batches = [];
    for (let i = 0; i < testData.length; i += config.normalizationBatchSize) {
      batches.push(testData.slice(i, i + config.normalizationBatchSize));
    }
    
    console.log(`Normalizing ${testData.length} findings in ${batches.length} batches`);
    
    const startTime = performance.now();
    
    // Process each batch
    const results = [];
    for (const batch of batches) {
      const response = await apiClient.post('/api/transform/normalize', {
        source: 'scc',
        data: batch
      });
      
      results.push(response.data);
    }
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    // Calculate metrics
    const totalFindings = results.reduce((sum, result) => sum + result.data.length, 0);
    const throughput = (totalFindings / duration) * 1000; // findings per second
    
    console.log(`Normalized ${totalFindings} findings in ${duration.toFixed(2)}ms`);
    console.log(`Throughput: ${throughput.toFixed(2)} findings/second`);
    console.log(`Average time per finding: ${(duration / totalFindings).toFixed(2)}ms`);
    
    // Verify performance
    expect(throughput).toBeGreaterThan(1000); // At least 1000 findings per second
    expect(duration / totalFindings).toBeLessThan(1); // Less than 1ms per finding
    
    // Write results to file
    const outputFile = path.join(config.outputDir, 'normalization-performance.json');
    fs.writeFileSync(outputFile, JSON.stringify({
      totalFindings,
      duration,
      throughput,
      averageTimePerFinding: duration / totalFindings
    }, null, 2));
  }, 60000);
  
  conditionalTest('should execute remediation workflows efficiently', async () => {
    // Load test data
    const testDataPath = path.join(config.testDataDir, 'remediation-scenarios.json');
    let testData;
    
    try {
      testData = JSON.parse(fs.readFileSync(testDataPath, 'utf8'));
    } catch (error) {
      // Generate test data if file doesn't exist
      testData = generateRemediationScenarios(100);
      fs.writeFileSync(testDataPath, JSON.stringify(testData, null, 2));
    }
    
    console.log(`Executing ${testData.length} remediation workflows`);
    
    const startTime = performance.now();
    
    // Execute remediation workflows with concurrency control
    const results = [];
    for (let i = 0; i < testData.length; i += config.remediationConcurrency) {
      const batch = testData.slice(i, i + config.remediationConcurrency);
      const promises = batch.map(scenario => 
        apiClient.post('/api/remediate', scenario)
      );
      
      const batchResults = await Promise.all(promises);
      results.push(...batchResults.map(response => response.data));
    }
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    // Calculate metrics
    const totalSteps = results.reduce((sum, result) => sum + result.steps.length, 0);
    const successfulRemediations = results.filter(result => result.status === 'completed').length;
    const throughput = (testData.length / duration) * 60000; // remediations per minute
    
    console.log(`Executed ${testData.length} remediation workflows (${totalSteps} steps) in ${duration.toFixed(2)}ms`);
    console.log(`Success rate: ${(successfulRemediations / testData.length * 100).toFixed(2)}%`);
    console.log(`Throughput: ${throughput.toFixed(2)} remediations/minute`);
    console.log(`Average time per remediation: ${(duration / testData.length).toFixed(2)}ms`);
    
    // Verify performance
    expect(throughput).toBeGreaterThan(30); // At least 30 remediations per minute
    expect(duration / testData.length).toBeLessThan(8000); // Less than 8 seconds per remediation
    expect(successfulRemediations / testData.length).toBeGreaterThanOrEqual(0.95); // At least 95% success rate
    
    // Write results to file
    const outputFile = path.join(config.outputDir, 'remediation-performance.json');
    fs.writeFileSync(outputFile, JSON.stringify({
      totalRemediations: testData.length,
      successfulRemediations,
      totalSteps,
      duration,
      throughput,
      averageTimePerRemediation: duration / testData.length,
      successRate: successfulRemediations / testData.length
    }, null, 2));
  }, 300000);
  
  conditionalTest('should handle concurrent requests at scale', async () => {
    // Generate concurrent requests
    const concurrentRequests = config.maxConcurrentRequests;
    const requestsPerEndpoint = 10;
    
    const endpoints = [
      { path: '/health', method: 'get' },
      { path: '/api/connectors', method: 'get' },
      { path: '/api/transform/capabilities', method: 'get' },
      { path: '/api/remediate/actions', method: 'get' }
    ];
    
    const requests = [];
    for (const endpoint of endpoints) {
      for (let i = 0; i < requestsPerEndpoint; i++) {
        requests.push({
          path: endpoint.path,
          method: endpoint.method
        });
      }
    }
    
    console.log(`Executing ${requests.length} concurrent requests`);
    
    const startTime = performance.now();
    
    // Execute requests in batches to avoid overwhelming the server
    const results = [];
    for (let i = 0; i < requests.length; i += concurrentRequests) {
      const batch = requests.slice(i, i + concurrentRequests);
      const promises = batch.map(request => 
        apiClient[request.method](request.path)
      );
      
      try {
        const batchResults = await Promise.all(promises);
        results.push(...batchResults);
      } catch (error) {
        console.error(`Error executing batch: ${error.message}`);
      }
    }
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    // Calculate metrics
    const successfulRequests = results.length;
    const throughput = (successfulRequests / duration) * 1000; // requests per second
    
    console.log(`Executed ${successfulRequests} requests in ${duration.toFixed(2)}ms`);
    console.log(`Throughput: ${throughput.toFixed(2)} requests/second`);
    console.log(`Average time per request: ${(duration / successfulRequests).toFixed(2)}ms`);
    
    // Verify performance
    expect(throughput).toBeGreaterThan(50); // At least 50 requests per second
    expect(successfulRequests).toBe(requests.length); // All requests should succeed
    
    // Write results to file
    const outputFile = path.join(config.outputDir, 'concurrency-performance.json');
    fs.writeFileSync(outputFile, JSON.stringify({
      totalRequests: requests.length,
      successfulRequests,
      duration,
      throughput,
      averageTimePerRequest: duration / successfulRequests
    }, null, 2));
  }, 60000);
  
  conditionalTest('should simulate peak load of 50K events', async () => {
    // For this test, we'll use a smaller batch and extrapolate
    // to avoid running an actual test with 50K events
    const batchSize = 500; // Use 500 items for the simulation
    
    // Generate batch payload
    const batchPayload = {
      source: 'scc',
      data: generateTestData(batchSize)
    };
    
    console.log(`Simulating peak load with ${batchSize} events`);
    
    const startTime = performance.now();
    
    // Process the batch
    const response = await apiClient.post('/api/transform/normalize', batchPayload);
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    // Calculate metrics
    const throughput = (batchSize / duration) * 1000; // events per second
    const estimatedTimeFor50K = (50000 / throughput) * 1000; // ms
    const estimatedTimeInMinutes = estimatedTimeFor50K / (1000 * 60); // minutes
    
    console.log(`Processed ${batchSize} events in ${duration.toFixed(2)}ms`);
    console.log(`Throughput: ${throughput.toFixed(2)} events/second`);
    console.log(`Estimated time to process 50,000 events: ${estimatedTimeInMinutes.toFixed(2)} minutes`);
    
    // Verify performance
    expect(throughput).toBeGreaterThan(1000); // At least 1000 events per second
    expect(estimatedTimeInMinutes).toBeLessThanOrEqual(15); // Less than 15 minutes for 50K events
    
    // Write results to file
    const outputFile = path.join(config.outputDir, 'peak-load-simulation.json');
    fs.writeFileSync(outputFile, JSON.stringify({
      batchSize,
      duration,
      throughput,
      estimatedTimeFor50K,
      estimatedTimeInMinutes
    }, null, 2));
  }, 60000);
  
  conditionalTest('should execute end-to-end breach remediation workflow', async () => {
    // Create a breach scenario
    const breachScenario = {
      id: `breach-${Date.now()}`,
      type: 'data_leak',
      severity: 'high',
      resource: {
        id: 'patient_records',
        type: 'bigquery.dataset',
        name: 'patient_records',
        provider: 'gcp',
        projectId: 'healthcare-demo'
      },
      finding: {
        id: `finding-${Date.now()}`,
        type: 'data_leak',
        severity: 'high',
        resourceName: 'projects/healthcare-demo/datasets/patient_records',
        resourceType: 'bigquery.dataset',
        createdAt: Date.now(),
        description: 'PHI data exposed in BigQuery dataset',
        dataType: 'PHI',
        complianceFrameworks: ['HIPAA', 'GDPR']
      },
      remediationSteps: [
        {
          id: 'step-1',
          action: 'encrypt-dataset',
          parameters: {
            projectId: 'healthcare-demo',
            datasetId: 'patient_records',
            encryptionType: 'AES-256',
            keyRotationPeriod: '90d'
          }
        },
        {
          id: 'step-2',
          action: 'update-access-controls',
          parameters: {
            projectId: 'healthcare-demo',
            datasetId: 'patient_records',
            accessLevel: 'restricted',
            allowedRoles: ['healthcare-admin', 'compliance-officer']
          }
        },
        {
          id: 'step-3',
          action: 'update-compliance-dashboard',
          parameters: {
            dashboardId: 'hipaa-compliance-dashboard',
            findingId: `finding-${Date.now()}`,
            remediationId: `breach-${Date.now()}`
          }
        }
      ]
    };
    
    console.log('Executing end-to-end breach remediation workflow');
    
    const startTime = performance.now();
    
    // Execute the remediation
    const response = await apiClient.post('/api/remediate', breachScenario);
    const result = response.data;
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    // Verify remediation result
    expect(result).toHaveProperty('id');
    expect(result).toHaveProperty('status');
    expect(result).toHaveProperty('steps');
    expect(result.steps.length).toBe(3);
    expect(result.steps.every(step => step.success)).toBe(true);
    
    console.log(`Executed breach remediation in ${duration.toFixed(2)}ms`);
    console.log(`Status: ${result.status}`);
    
    // Verify performance
    expect(duration).toBeLessThan(8000); // Less than 8 seconds
    
    // Write results to file
    const outputFile = path.join(config.outputDir, 'breach-remediation.json');
    fs.writeFileSync(outputFile, JSON.stringify({
      scenario: breachScenario,
      result,
      duration
    }, null, 2));
  }, 30000);
  
  conditionalTest('should generate system performance report', async () => {
    // Get system metrics
    const response = await apiClient.get('/api/metrics');
    const metrics = response.data;
    
    // Write metrics to file
    const outputFile = path.join(config.outputDir, 'system-metrics.json');
    fs.writeFileSync(outputFile, JSON.stringify(metrics, null, 2));
    
    // Generate summary report
    const summaryFile = path.join(config.outputDir, 'system-performance-summary.md');
    
    let summary = `# NovaConnect System Performance Summary\n\n`;
    summary += `Generated on: ${new Date().toISOString()}\n\n`;
    
    // Add transformation metrics
    if (metrics.transformation) {
      summary += `## Data Normalization Performance\n\n`;
      summary += `- Average normalization time: ${metrics.transformation.averageDuration.toFixed(2)}ms\n`;
      summary += `- Total transformations: ${metrics.transformation.transformations}\n`;
      summary += `- Batch transformations: ${metrics.transformation.batchTransformations}\n`;
      summary += `- Rules applied: ${metrics.transformation.rulesApplied}\n\n`;
    }
    
    // Add remediation metrics
    if (metrics.remediation) {
      summary += `## Remediation Performance\n\n`;
      summary += `- Total remediations: ${metrics.remediation.totalRemediations}\n`;
      summary += `- Successful remediations: ${metrics.remediation.successfulRemediations}\n`;
      summary += `- Failed remediations: ${metrics.remediation.failedRemediations}\n`;
      summary += `- Average remediation time: ${metrics.remediation.averageRemediationTime.toFixed(2)}ms\n`;
      summary += `- Total steps: ${metrics.remediation.totalSteps}\n`;
      summary += `- Successful steps: ${metrics.remediation.successfulSteps}\n`;
      summary += `- Failed steps: ${metrics.remediation.failedSteps}\n`;
      summary += `- Average step time: ${metrics.remediation.averageStepTime.toFixed(2)}ms\n\n`;
    }
    
    // Add system metrics
    if (metrics.system) {
      summary += `## System Performance\n\n`;
      summary += `- CPU usage: ${metrics.system.cpu.toFixed(2)}%\n`;
      summary += `- Memory usage: ${(metrics.system.memory / (1024 * 1024)).toFixed(2)} MB\n`;
      summary += `- Uptime: ${(metrics.system.uptime / (60 * 60)).toFixed(2)} hours\n`;
      summary += `- Active connections: ${metrics.system.connections}\n\n`;
    }
    
    // Add API metrics
    if (metrics.api) {
      summary += `## API Performance\n\n`;
      summary += `- Total requests: ${metrics.api.totalRequests}\n`;
      summary += `- Average response time: ${metrics.api.averageResponseTime.toFixed(2)}ms\n`;
      summary += `- Requests per second: ${metrics.api.requestsPerSecond.toFixed(2)}\n`;
      summary += `- Error rate: ${(metrics.api.errorRate * 100).toFixed(2)}%\n\n`;
    }
    
    // Write summary to file
    fs.writeFileSync(summaryFile, summary);
    
    console.log(`System performance report generated: ${summaryFile}`);
  }, 30000);
});

/**
 * Generate test data for SCC findings
 * @param {number} count - Number of findings to generate
 * @returns {Array} - Array of test findings
 */
function generateTestData(count) {
  const findings = [];
  
  for (let i = 0; i < count; i++) {
    findings.push({
      name: `organizations/123/sources/456/findings/finding-${i}`,
      parent: 'organizations/123/sources/456',
      resourceName: `//compute.googleapis.com/projects/test-project/zones/us-central1-a/instances/instance-${i}`,
      state: 'ACTIVE',
      category: ['VULNERABILITY', 'MISCONFIGURATION', 'THREAT'][i % 3],
      severity: ['HIGH', 'MEDIUM', 'LOW'][i % 3],
      eventTime: new Date().toISOString(),
      createTime: new Date().toISOString(),
      sourceProperties: {
        finding_type: ['Vulnerability', 'Misconfiguration', 'Threat'][i % 3],
        finding_id: `finding-${i}`,
        finding_description: `Mock finding ${i} description`
      }
    });
  }
  
  return findings;
}

/**
 * Generate test data for remediation scenarios
 * @param {number} count - Number of scenarios to generate
 * @returns {Array} - Array of test scenarios
 */
function generateRemediationScenarios(count) {
  const scenarios = [];
  
  for (let i = 0; i < count; i++) {
    scenarios.push({
      id: `scenario-${i}`,
      type: 'compliance',
      framework: ['HIPAA', 'PCI-DSS', 'GDPR'][i % 3],
      control: ['164.312(a)(1)', 'Requirement 3.4', 'Article 32'][i % 3],
      severity: ['high', 'medium', 'low'][i % 3],
      resource: {
        id: `resource-${i}`,
        type: ['compute.instance', 'storage.bucket', 'bigquery.dataset'][i % 3],
        name: `resource-${i}`,
        provider: 'gcp'
      },
      finding: {
        id: `finding-${i}`,
        type: ['vulnerability', 'misconfiguration', 'threat'][i % 3],
        severity: ['high', 'medium', 'low'][i % 3],
        resourceName: `projects/test-project/resources/resource-${i}`,
        resourceType: ['compute.instance', 'storage.bucket', 'bigquery.dataset'][i % 3],
        createdAt: Date.now(),
        description: `Mock finding ${i} description`
      },
      remediationSteps: [
        {
          id: `step-${i}-1`,
          action: ['update-firewall-rule', 'encrypt-bucket', 'update-access-controls'][i % 3],
          parameters: {
            resourceId: `resource-${i}`,
            action: 'restrict'
          }
        },
        {
          id: `step-${i}-2`,
          action: 'generate-evidence',
          parameters: {
            findingId: `finding-${i}`,
            evidenceType: 'remediation'
          }
        }
      ]
    });
  }
  
  return scenarios;
}
