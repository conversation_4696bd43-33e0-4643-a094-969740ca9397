/* 
 * NovaFuse Compliance App Store - Main Styles
 */

:root {
  --primary: #6c5ce7;
  --primary-dark: #5549d6;
  --secondary: #2d3436;
  --success: #00b894;
  --info: #0984e3;
  --warning: #fdcb6e;
  --danger: #e17055;
  --light: #f8f9fa;
  --dark: #2d3436;
  --gray: #636e72;
  --border-radius: 10px;
  --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease;
}

/* General Styles */
body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  color: var(--secondary);
  line-height: 1.6;
}

a {
  color: var(--primary);
  text-decoration: none;
  transition: var(--transition);
}

a:hover {
  color: var(--primary-dark);
}

.btn {
  border-radius: var(--border-radius);
  padding: 0.5rem 1.5rem;
  transition: var(--transition);
}

.btn-primary {
  background-color: var(--primary);
  border-color: var(--primary);
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
}

.btn-outline-primary {
  color: var(--primary);
  border-color: var(--primary);
}

.btn-outline-primary:hover {
  background-color: var(--primary);
  border-color: var(--primary);
}

.section-title {
  font-weight: 700;
  margin-bottom: 2rem;
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 50px;
  height: 3px;
  background-color: var(--primary);
}

.text-center .section-title::after {
  left: 50%;
  transform: translateX(-50%);
}

/* Header & Navigation */
.navbar-brand {
  font-weight: 700;
}

.navbar-dark .navbar-nav .nav-link {
  color: rgba(255, 255, 255, 0.8);
}

.navbar-dark .navbar-nav .nav-link:hover,
.navbar-dark .navbar-nav .nav-link.active {
  color: #fff;
}

/* Connector Cards */
.connector-card {
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.connector-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.card-badge {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: var(--primary);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
}

.connector-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  object-fit: cover;
}

.rating {
  color: var(--warning);
}

/* Category Cards */
.category-card {
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  transition: var(--transition);
  height: 100%;
}

.category-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.category-icon {
  font-size: 2.5rem;
  color: var(--primary);
}

/* How It Works */
.step-icon {
  width: 60px;
  height: 60px;
  background-color: var(--primary);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 auto;
}

/* Partner Benefits */
.partner-benefits {
  list-style: none;
  padding: 0;
}

.partner-benefits li {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
}

.partner-benefits li i {
  color: var(--success);
  font-size: 1.25rem;
  margin-right: 1rem;
}

/* Testimonials */
.testimonial-card {
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
}

.testimonial-rating {
  color: var(--warning);
  font-size: 1.25rem;
}

.testimonial-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  object-fit: cover;
}

/* Footer */
footer a {
  color: rgba(255, 255, 255, 0.8);
  transition: var(--transition);
}

footer a:hover {
  color: #fff;
  text-decoration: none;
}

.social-icons a {
  font-size: 1.25rem;
  color: rgba(255, 255, 255, 0.8);
  transition: var(--transition);
}

.social-icons a:hover {
  color: #fff;
}

/* Browse Page */
.filter-sidebar {
  background-color: var(--light);
  border-radius: var(--border-radius);
  padding: 1.5rem;
}

.filter-group {
  margin-bottom: 1.5rem;
}

.filter-group h5 {
  font-weight: 600;
  margin-bottom: 1rem;
}

.filter-options {
  max-height: 200px;
  overflow-y: auto;
}

.search-bar {
  position: relative;
}

.search-bar .form-control {
  padding-left: 2.5rem;
  border-radius: var(--border-radius);
}

.search-bar i {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--gray);
}

/* Connector Details Page */
.connector-header {
  display: flex;
  align-items: center;
  margin-bottom: 2rem;
}

.connector-logo {
  width: 80px;
  height: 80px;
  border-radius: 12px;
  margin-right: 1.5rem;
}

.connector-meta {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
}

.connector-meta > div {
  margin-right: 2rem;
  display: flex;
  align-items: center;
}

.connector-meta i {
  margin-right: 0.5rem;
  color: var(--primary);
}

.connector-tabs {
  margin-top: 2rem;
}

.connector-tabs .nav-link {
  color: var(--secondary);
  font-weight: 600;
  padding: 1rem 1.5rem;
}

.connector-tabs .nav-link.active {
  color: var(--primary);
  border-bottom: 3px solid var(--primary);
}

.connector-price-card {
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  padding: 1.5rem;
}

.price-amount {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary);
}

.price-period {
  color: var(--gray);
  font-size: 0.875rem;
}

/* Partner Portal */
.dashboard-card {
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.dashboard-card h3 {
  font-weight: 700;
  margin-bottom: 1rem;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary);
}

.stat-label {
  color: var(--gray);
  font-size: 0.875rem;
}

.connector-table {
  width: 100%;
}

.connector-table th {
  font-weight: 600;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
}

.status-badge.published {
  background-color: var(--success);
  color: white;
}

.status-badge.draft {
  background-color: var(--warning);
  color: var(--dark);
}

.status-badge.review {
  background-color: var(--info);
  color: white;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .section-title {
    font-size: 1.75rem;
  }
  
  .connector-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .connector-logo {
    margin-bottom: 1rem;
  }
  
  .connector-meta {
    flex-wrap: wrap;
  }
  
  .connector-meta > div {
    margin-bottom: 0.5rem;
  }
}
