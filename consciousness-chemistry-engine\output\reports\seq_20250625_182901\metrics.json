{"psi_score": 0.66, "fibonacci_analysis": {"residue_distances": [{"start": 1, "end": 6, "score": 0.9077752986025976, "ratio": 1.634472274930739, "golden_ratio_diff": 0.010159419576559345}, {"start": 9, "end": 12, "score": 0.8105762760781643, "ratio": 1.5802221217330084, "golden_ratio_diff": 0.02336901899452698}, {"start": 6, "end": 8, "score": 0.7798994876487099, "ratio": 1.6636975794517348, "golden_ratio_diff": 0.02822165110210072}, {"start": 4, "end": 9, "score": 0.7738972402257278, "ratio": 1.5707613107702942, "golden_ratio_diff": 0.02921612173062192}, {"start": 15, "end": 18, "score": 0.6726517976075393, "ratio": 1.6967761432362214, "golden_ratio_diff": 0.04866532781994479}, {"start": 0, "end": 3, "score": 0.6152574709242099, "ratio": 1.7192154482821285, "golden_ratio_diff": 0.06253358102224238}, {"start": 16, "end": 18, "score": 0.587049555274171, "ratio": 1.5042160241108244, "golden_ratio_diff": 0.0703433706772792}, {"start": 1, "end": 4, "score": 0.5841119690369563, "ratio": 1.502829883153165, "golden_ratio_diff": 0.07120005290231106}, {"start": 0, "end": 6, "score": 0.5808665819362246, "ratio": 1.5012822014483203, "golden_ratio_diff": 0.0721565727996715}, {"start": 4, "end": 8, "score": 0.526127683155644, "ratio": 1.7637669618531184, "golden_ratio_diff": 0.09006793065936637}, {"start": 3, "end": 6, "score": 0.5221417006268911, "ratio": 1.7661146667083127, "golden_ratio_diff": 0.09151889205542961}, {"start": 1, "end": 8, "score": 0.5098080677394268, "ratio": 1.4624563787435372, "golden_ratio_diff": 0.09615225087240481}], "torsion_angles": [{"start": 9, "end": 17, "score": 0.822913571454985, "ratio": 1.583214796276796, "golden_ratio_diff": 0.02151944440919966}, {"start": 9, "end": 16, "score": 0.7720374434035766, "ratio": 1.665810319792756, "golden_ratio_diff": 0.029527396442254907}, {"start": 5, "end": 8, "score": 0.7575392344808293, "ratio": 1.6698213737052368, "golden_ratio_diff": 0.03200636409087622}, {"start": 13, "end": 17, "score": 0.6240698454496951, "ratio": 1.7155018878790518, "golden_ratio_diff": 0.060238474473865225}, {"start": 12, "end": 15, "score": 0.5909853588269259, "ratio": 1.5060515882674173, "golden_ratio_diff": 0.06920892963997381}, {"start": 9, "end": 14, "score": 0.5469326052491488, "ratio": 1.4839994775751044, "golden_ratio_diff": 0.08283788357149811}, {"start": 10, "end": 18, "score": 0.5439310798815663, "ratio": 1.4823669821642882, "golden_ratio_diff": 0.08384682122186077}], "overall_score": 0.6593985404001573}, "fibonacci_alignment": {"closest_fibonacci": 21, "difference": 1, "ratio": 0.9523809523809523, "alignment_score": 0.9545454545454545}, "trinity_validation": {"ners": 0.6598195621200471, "nepi": 0.5389999999999999, "nefc": 0.694, "overall": 0.6338278248480188, "passed": false}, "trinity_report": {"scores": {"ners": 0.6598195621200471, "nepi": 0.5389999999999999, "nefc": 0.694, "overall": 0.6338278248480188, "passed": false}, "thresholds": {"ners": 0.7, "nepi": 0.5, "nefc": 0.6}, "weights": {"ners": 0.4, "nepi": 0.3, "nefc": 0.3}, "validation": {"ners": {"score": 0.6598195621200471, "threshold": 0.7, "passed": false, "description": "Neural-Emotional Resonance Score: Measures structural harmony and consciousness resonance."}, "nepi": {"score": 0.5389999999999999, "threshold": 0.5, "passed": true, "description": "Neural-Emotional Potential Index: Evaluates functional potential and adaptability."}, "nefc": {"score": 0.694, "threshold": 0.6, "passed": true, "description": "Neural-Emotional Field Coherence: Assesses field coherence and quantum effects."}, "overall": {"score": 0.6338278248480188, "passed": false, "description": "Overall validation status based on all metrics."}}}}