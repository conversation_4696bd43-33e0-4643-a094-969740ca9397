/**
 * CSME (Cyber-Safety Medical Engine)
 * 
 * This module exports all components of the CSME system.
 */

// Core components
const CSMEEngine = require('./core/csme_engine');
const BioEntropicTensor = require('./core/bio_entropic_tensor');
const EnvironmentalContextProcessor = require('./core/environmental_context_processor');
const PsiRevertProtocolEngine = require('./core/psi_revert_protocol_engine');
const NeuroEthicalFilterBank = require('./core/neuro_ethical_filter_bank');
const DecayRateCalculator = require('./core/decay_rate_calculator');
const CSMEController = require('./core/csme_controller');

// Integration components
const MeterIntegrationInterface = require('./integration/meter_integration_interface');
const GovernorIntegrationInterface = require('./integration/governor_integration_interface');

/**
 * Create a fully configured CSME system
 * @param {Object} options - Configuration options
 * @returns {Object} - CSME system components
 */
function createCSMESystem(options = {}) {
  // Create core components
  const bioEntropicTensor = new BioEntropicTensor(options.bioEntropicTensorOptions);
  const environmentalContextProcessor = new EnvironmentalContextProcessor(options.environmentalContextProcessorOptions);
  const psiRevertProtocolEngine = new PsiRevertProtocolEngine(options.psiRevertProtocolEngineOptions);
  const neuroEthicalFilterBank = new NeuroEthicalFilterBank(options.neuroEthicalFilterBankOptions);
  const decayRateCalculator = new DecayRateCalculator(options.decayRateCalculatorOptions);
  const csmeEngine = new CSMEEngine(options.csmeEngineOptions);
  
  // Create controller
  const csmeController = new CSMEController({
    bioEntropicTensor,
    environmentalContextProcessor,
    psiRevertProtocolEngine,
    neuroEthicalFilterBank,
    decayRateCalculator,
    csmeEngine,
    ...options.csmeControllerOptions
  });
  
  // Create integration interfaces
  const meterIntegrationInterface = new MeterIntegrationInterface(
    csmeController,
    options.meterIntegrationOptions
  );
  
  const governorIntegrationInterface = new GovernorIntegrationInterface(
    csmeController,
    psiRevertProtocolEngine,
    options.governorIntegrationOptions
  );
  
  // Connect integration interfaces to controller
  csmeController.meterIntegrationInterface = meterIntegrationInterface;
  csmeController.governorIntegrationInterface = governorIntegrationInterface;
  
  return {
    csmeEngine,
    bioEntropicTensor,
    environmentalContextProcessor,
    psiRevertProtocolEngine,
    neuroEthicalFilterBank,
    decayRateCalculator,
    csmeController,
    meterIntegrationInterface,
    governorIntegrationInterface
  };
}

module.exports = {
  // Core components
  CSMEEngine,
  BioEntropicTensor,
  EnvironmentalContextProcessor,
  PsiRevertProtocolEngine,
  NeuroEthicalFilterBank,
  DecayRateCalculator,
  CSMEController,
  
  // Integration components
  MeterIntegrationInterface,
  GovernorIntegrationInterface,
  
  // Factory function
  createCSMESystem
};
