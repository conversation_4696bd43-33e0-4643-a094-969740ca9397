/**
 * SYSTEM INTEGRATION TEST PANEL
 * Comprehensive testing interface for the 15-engine CHAEONIX system
 * Tests engine coordination, profit generation, and system coherence
 */

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

export default function SystemIntegrationTestPanel() {
  const [testResults, setTestResults] = useState(null);
  const [isRunning, setIsRunning] = useState(false);
  const [testProgress, setTestProgress] = useState(0);

  const runSystemTest = async () => {
    setIsRunning(true);
    setTestProgress(0);
    
    try {
      // Simulate test progress
      const progressInterval = setInterval(() => {
        setTestProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 500);

      const response = await fetch('/api/testing/system-integration-test', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'RUN_SYSTEM_TEST' })
      });

      const data = await response.json();
      
      clearInterval(progressInterval);
      setTestProgress(100);
      
      if (data.success) {
        setTestResults(data.test_results);
      } else {
        console.error('Test failed:', data.error);
      }
      
    } catch (error) {
      console.error('Test execution error:', error);
    } finally {
      setIsRunning(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'PASSED': return 'text-green-400';
      case 'WARNING': return 'text-yellow-400';
      case 'FAILED': return 'text-red-400';
      case 'EXCELLENT': return 'text-green-400';
      case 'GOOD': return 'text-blue-400';
      case 'CRITICAL': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'PASSED': return '✅';
      case 'WARNING': return '⚠️';
      case 'FAILED': return '❌';
      case 'EXCELLENT': return '🎯';
      case 'GOOD': return '👍';
      case 'CRITICAL': return '🚨';
      default: return '⏳';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="p-6 rounded-lg border border-purple-500/30 bg-gradient-to-br from-purple-900/20 to-blue-900/20 backdrop-blur-sm"
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-xl font-bold text-white">
            🧪 System Integration Testing
          </h3>
          <p className="text-sm text-gray-400">
            Comprehensive testing of 15-engine Comphyological Money Making Machine
          </p>
        </div>
        
        <button
          onClick={runSystemTest}
          disabled={isRunning}
          className={`px-6 py-3 rounded-lg font-medium transition-all ${
            isRunning 
              ? 'bg-gray-600 text-gray-400 cursor-not-allowed' 
              : 'bg-purple-600 hover:bg-purple-700 text-white hover:scale-105'
          }`}
        >
          {isRunning ? '🧪 Testing...' : '🚀 Run System Test'}
        </button>
      </div>

      {/* Test Progress */}
      {isRunning && (
        <div className="mb-6">
          <div className="flex justify-between text-sm text-gray-400 mb-2">
            <span>Testing Progress</span>
            <span>{testProgress}%</span>
          </div>
          <div className="w-full bg-gray-700 rounded-full h-3">
            <motion.div
              initial={{ width: 0 }}
              animate={{ width: `${testProgress}%` }}
              transition={{ duration: 0.5 }}
              className="h-3 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full"
            />
          </div>
        </div>
      )}

      {/* Test Results */}
      {testResults && (
        <div className="space-y-6">
          {/* Overall Status */}
          <div className="p-4 rounded-lg bg-gray-800/50 border border-gray-600">
            <h4 className="text-lg font-semibold text-white mb-4">📊 Overall System Status</h4>
            
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className={`text-2xl font-bold ${getStatusColor(testResults.overall_status)}`}>
                  {getStatusIcon(testResults.overall_status)} {testResults.overall_status}
                </div>
                <div className="text-sm text-gray-400">System Status</div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-green-400">
                  {testResults.system_health}%
                </div>
                <div className="text-sm text-gray-400">System Health</div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-400">
                  ${testResults.profit_potential?.toLocaleString() || '0'}/hr
                </div>
                <div className="text-sm text-gray-400">Profit Potential</div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-400">
                  {testResults.tests_executed?.length || 0}/7
                </div>
                <div className="text-sm text-gray-400">Tests Completed</div>
              </div>
            </div>
          </div>

          {/* Individual Test Results */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold text-white">🔧 Individual Test Results</h4>
            
            {testResults.tests_executed?.map((test, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className="p-4 rounded-lg bg-gray-800/30 border border-gray-600"
              >
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <span className="text-2xl">{getStatusIcon(test.status)}</span>
                    <div>
                      <h5 className="font-medium text-white">{test.test_name}</h5>
                      <p className="text-sm text-gray-400">
                        Score: {test.score?.toFixed(1) || 0}%
                      </p>
                    </div>
                  </div>
                  <span className={`font-bold ${getStatusColor(test.status)}`}>
                    {test.status}
                  </span>
                </div>
                
                {/* Test Details */}
                {test.details && (
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-2 text-xs">
                    {Object.entries(test.details).slice(0, 6).map(([key, value]) => (
                      <div key={key} className="flex justify-between">
                        <span className="text-gray-500 capitalize">
                          {key.replace(/_/g, ' ')}:
                        </span>
                        <span className="text-gray-300">
                          {typeof value === 'boolean' ? (value ? '✅' : '❌') :
                           typeof value === 'number' ? value.toFixed(1) :
                           typeof value === 'object' ? 'Object' :
                           String(value).slice(0, 20)}
                        </span>
                      </div>
                    ))}
                  </div>
                )}
              </motion.div>
            ))}
          </div>

          {/* Recommendations */}
          {testResults.recommendations && testResults.recommendations.length > 0 && (
            <div className="p-4 rounded-lg bg-blue-900/20 border border-blue-500/30">
              <h4 className="text-lg font-semibold text-white mb-3">💡 Recommendations</h4>
              <div className="space-y-2">
                {testResults.recommendations.map((rec, index) => (
                  <div key={index} className="text-sm text-blue-300">
                    {rec}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Test Summary */}
          <div className="p-4 rounded-lg bg-gradient-to-r from-purple-900/30 to-blue-900/30 border border-purple-500/30">
            <h4 className="text-lg font-semibold text-white mb-3">📋 Test Summary</h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="text-gray-400">Test ID:</span>
                <div className="text-white font-mono text-xs">
                  {testResults.test_id?.slice(-8) || 'N/A'}
                </div>
              </div>
              <div>
                <span className="text-gray-400">Duration:</span>
                <div className="text-white">
                  {testResults.duration ? `${(testResults.duration / 1000).toFixed(1)}s` : 'N/A'}
                </div>
              </div>
              <div>
                <span className="text-gray-400">Start Time:</span>
                <div className="text-white">
                  {testResults.start_time ? new Date(testResults.start_time).toLocaleTimeString() : 'N/A'}
                </div>
              </div>
              <div>
                <span className="text-gray-400">Status:</span>
                <div className={`font-bold ${getStatusColor(testResults.overall_status)}`}>
                  {testResults.overall_status}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Initial State */}
      {!testResults && !isRunning && (
        <div className="text-center py-12">
          <div className="text-6xl mb-4">🧪</div>
          <h4 className="text-xl font-semibold text-white mb-2">
            Ready to Test CHAEONIX System
          </h4>
          <p className="text-gray-400 mb-6">
            Run comprehensive tests to verify all 15 engines are working together as a cohesive money-making machine
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div className="p-3 rounded-lg bg-gray-800/30">
              <div className="font-medium text-white mb-1">🔧 Engine Tests</div>
              <div className="text-gray-400">Coordination, allocation, arbitration</div>
            </div>
            <div className="p-3 rounded-lg bg-gray-800/30">
              <div className="font-medium text-white mb-1">💰 Profit Tests</div>
              <div className="text-gray-400">Generation, trading flow, MT5 integration</div>
            </div>
          </div>
        </div>
      )}
    </motion.div>
  );
}
