/**
 * SSO Authentication Routes
 */

const express = require('express');
const router = express.Router();
const SsoAuthController = require('../controllers/SsoAuthController');
const { authenticate, optionalAuth, hasPermission } = require('../middleware/authMiddleware');

// Public routes for SSO authentication

// Get provider by domain (public)
router.get('/provider/domain/:domain', (req, res, next) => {
  SsoAuthController.getProviderByDomain(req, res, next);
});

// Initiate SSO authentication (public)
router.get('/initiate/:providerId', (req, res, next) => {
  SsoAuthController.initiateAuth(req, res, next);
});

// Process SAML callback (public)
router.post('/callback/saml', (req, res, next) => {
  SsoAuthController.processSamlCallback(req, res, next);
});

// Process OIDC callback (public)
router.get('/callback/oidc', (req, res, next) => {
  SsoAuthController.processOidcCallback(req, res, next);
});

// Process SAML logout callback (public)
router.post('/logout/callback/saml', (req, res, next) => {
  SsoAuthController.processSamlLogoutCallback(req, res, next);
});

// Refresh OIDC tokens (public)
router.post('/refresh', (req, res, next) => {
  SsoAuthController.refreshTokens(req, res, next);
});

// Authenticated routes

// Initiate SSO logout (requires authentication)
router.post('/logout/:providerId', authenticate, (req, res, next) => {
  SsoAuthController.initiateLogout(req, res, next);
});

// Admin routes

// Clean up expired data (admin only)
router.post('/cleanup', authenticate, hasPermission('system:settings'), (req, res, next) => {
  SsoAuthController.cleanup(req, res, next);
});

module.exports = router;
