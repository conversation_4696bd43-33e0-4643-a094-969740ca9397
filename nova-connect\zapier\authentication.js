/**
 * NovaConnect UAC Zapier Authentication
 * 
 * This file defines the authentication for the Zapier integration.
 */

// Define the authentication
module.exports = {
  type: 'oauth2',
  oauth2Config: {
    // Authorization URL
    authorizeUrl: {
      url: '{{process.env.API_BASE_URL}}/api/zapier/oauth/authorize',
      params: {
        client_id: '{{process.env.ZAPIER_CLIENT_ID}}',
        state: '{{bundle.inputData.state}}',
        redirect_uri: '{{bundle.inputData.redirect_uri}}',
        response_type: 'code'
      }
    },
    
    // Token URL
    getAccessToken: {
      url: '{{process.env.API_BASE_URL}}/api/zapier/oauth/token',
      method: 'POST',
      body: {
        code: '{{bundle.inputData.code}}',
        client_id: '{{process.env.ZAPIER_CLIENT_ID}}',
        client_secret: '{{process.env.ZAPIER_CLIENT_SECRET}}',
        redirect_uri: '{{bundle.inputData.redirect_uri}}',
        grant_type: 'authorization_code'
      },
      headers: {
        'Content-Type': 'application/json'
      }
    },
    
    // Refresh URL
    refreshAccessToken: {
      url: '{{process.env.API_BASE_URL}}/api/zapier/oauth/token',
      method: 'POST',
      body: {
        refresh_token: '{{bundle.authData.refresh_token}}',
        client_id: '{{process.env.ZAPIER_CLIENT_ID}}',
        client_secret: '{{process.env.ZAPIER_CLIENT_SECRET}}',
        grant_type: 'refresh_token'
      },
      headers: {
        'Content-Type': 'application/json'
      }
    },
    
    // Scope
    scope: 'read write',
    
    // Auto refresh
    autoRefresh: true
  },
  
  // Test authentication
  test: {
    url: '{{process.env.API_BASE_URL}}/api/zapier/app-definition'
  },
  
  // Connection label
  connectionLabel: '{{bundle.authData.username}}'
};
