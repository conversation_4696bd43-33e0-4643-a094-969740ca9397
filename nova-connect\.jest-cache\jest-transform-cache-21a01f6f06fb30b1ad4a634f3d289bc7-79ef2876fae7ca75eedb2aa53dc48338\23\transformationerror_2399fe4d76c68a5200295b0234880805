585c1c361a130d6e9392627e9f56180f
/**
 * NovaFuse Universal API Connector - Transformation Error
 * 
 * This module defines data transformation-related errors for the UAC.
 */

const UAConnectorError = require('./base-error');

/**
 * Error class for data transformation failures
 * @class TransformationError
 * @extends UAConnectorError
 */
class TransformationError extends UAConnectorError {
  /**
   * Create a new TransformationError
   * 
   * @param {string} message - Error message
   * @param {Object} options - Error options
   * @param {string} options.code - Error code
   * @param {string} options.severity - Error severity
   * @param {Object} options.context - Additional context for the error
   * @param {Error} options.cause - The error that caused this error
   * @param {string} options.transformationId - ID of the transformation
   * @param {string} options.sourceData - Source data that failed transformation
   */
  constructor(message, options = {}) {
    super(message, {
      code: options.code || 'TRANSFORMATION_ERROR',
      severity: options.severity || 'error',
      context: options.context || {},
      cause: options.cause
    });
    this.transformationId = options.transformationId;
    this.sourceData = options.sourceData;
  }

  /**
   * Get a user-friendly error message
   * 
   * @returns {string} - User-friendly error message
   */
  getUserMessage() {
    return 'An error occurred while processing the data. Please contact support if the issue persists.';
  }

  /**
   * Convert the error to a JSON object
   * 
   * @param {boolean} includeStack - Whether to include the stack trace
   * @returns {Object} - JSON representation of the error
   */
  toJSON(includeStack = false) {
    const json = super.toJSON(includeStack);
    if (this.transformationId) {
      json.transformationId = this.transformationId;
    }

    // Only include source data in developer mode to avoid leaking sensitive information
    if (process.env.NODE_ENV === 'development' && this.sourceData) {
      json.sourceData = this.sourceData;
    }
    return json;
  }
}

/**
 * Error class for missing source field errors
 * @class MissingSourceFieldError
 * @extends TransformationError
 */
class MissingSourceFieldError extends TransformationError {
  /**
   * Create a new MissingSourceFieldError
   * 
   * @param {string} fieldPath - The path to the missing field
   * @param {Object} options - Error options
   */
  constructor(fieldPath, options = {}) {
    const message = `Missing source field: ${fieldPath}`;
    super(message, {
      code: options.code || 'TRANSFORMATION_MISSING_SOURCE_FIELD',
      severity: options.severity || 'error',
      context: {
        ...options.context,
        fieldPath
      },
      cause: options.cause,
      transformationId: options.transformationId,
      sourceData: options.sourceData
    });
    this.fieldPath = fieldPath;
  }

  /**
   * Get a user-friendly error message
   * 
   * @returns {string} - User-friendly error message
   */
  getUserMessage() {
    return 'An error occurred while processing the data. A required field is missing.';
  }

  /**
   * Convert the error to a JSON object
   * 
   * @param {boolean} includeStack - Whether to include the stack trace
   * @returns {Object} - JSON representation of the error
   */
  toJSON(includeStack = false) {
    const json = super.toJSON(includeStack);
    json.fieldPath = this.fieldPath;
    return json;
  }
}

/**
 * Error class for invalid transformation function errors
 * @class InvalidTransformationFunctionError
 * @extends TransformationError
 */
class InvalidTransformationFunctionError extends TransformationError {
  /**
   * Create a new InvalidTransformationFunctionError
   * 
   * @param {string} functionName - The name of the invalid function
   * @param {Object} options - Error options
   */
  constructor(functionName, options = {}) {
    const message = `Invalid transformation function: ${functionName}`;
    super(message, {
      code: options.code || 'TRANSFORMATION_INVALID_FUNCTION',
      severity: options.severity || 'error',
      context: {
        ...options.context,
        functionName
      },
      cause: options.cause,
      transformationId: options.transformationId,
      sourceData: options.sourceData
    });
    this.functionName = functionName;
  }

  /**
   * Get a user-friendly error message
   * 
   * @returns {string} - User-friendly error message
   */
  getUserMessage() {
    return 'An error occurred while processing the data. The transformation configuration is invalid.';
  }

  /**
   * Convert the error to a JSON object
   * 
   * @param {boolean} includeStack - Whether to include the stack trace
   * @returns {Object} - JSON representation of the error
   */
  toJSON(includeStack = false) {
    const json = super.toJSON(includeStack);
    json.functionName = this.functionName;
    return json;
  }
}

/**
 * Error class for data type conversion errors
 * @class DataTypeConversionError
 * @extends TransformationError
 */
class DataTypeConversionError extends TransformationError {
  /**
   * Create a new DataTypeConversionError
   * 
   * @param {string} fieldPath - The path to the field
   * @param {string} sourceType - The source data type
   * @param {string} targetType - The target data type
   * @param {Object} options - Error options
   */
  constructor(fieldPath, sourceType, targetType, options = {}) {
    const message = `Cannot convert field ${fieldPath} from ${sourceType} to ${targetType}`;
    super(message, {
      code: options.code || 'TRANSFORMATION_DATA_TYPE_CONVERSION',
      severity: options.severity || 'error',
      context: {
        ...options.context,
        fieldPath,
        sourceType,
        targetType
      },
      cause: options.cause,
      transformationId: options.transformationId,
      sourceData: options.sourceData
    });
    this.fieldPath = fieldPath;
    this.sourceType = sourceType;
    this.targetType = targetType;
  }

  /**
   * Get a user-friendly error message
   * 
   * @returns {string} - User-friendly error message
   */
  getUserMessage() {
    return 'An error occurred while processing the data. A data type conversion failed.';
  }

  /**
   * Convert the error to a JSON object
   * 
   * @param {boolean} includeStack - Whether to include the stack trace
   * @returns {Object} - JSON representation of the error
   */
  toJSON(includeStack = false) {
    const json = super.toJSON(includeStack);
    json.fieldPath = this.fieldPath;
    json.sourceType = this.sourceType;
    json.targetType = this.targetType;
    return json;
  }
}
module.exports = {
  TransformationError,
  MissingSourceFieldError,
  InvalidTransformationFunctionError,
  DataTypeConversionError
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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