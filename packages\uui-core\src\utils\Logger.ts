/**
 * NovaVision - Logger Utility
 * 
 * This utility provides logging capabilities for NovaVision.
 */

/**
 * Log level type
 */
export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

/**
 * Logger options interface
 */
export interface LoggerOptions {
  level?: LogLevel;
  prefix?: string;
  enabled?: boolean;
}

/**
 * Logger class
 */
export class Logger {
  private name: string;
  private options: LoggerOptions;
  
  constructor(name: string, options: LoggerOptions = {}) {
    this.name = name;
    this.options = {
      level: options.level || 'info',
      prefix: options.prefix || '[NovaVision]',
      enabled: options.enabled !== false
    };
  }
  
  /**
   * Log a debug message
   * 
   * @param message - Message to log
   * @param data - Additional data to log
   */
  public debug(message: string, data: any = {}): void {
    this.log('debug', message, data);
  }
  
  /**
   * Log an info message
   * 
   * @param message - Message to log
   * @param data - Additional data to log
   */
  public info(message: string, data: any = {}): void {
    this.log('info', message, data);
  }
  
  /**
   * Log a warning message
   * 
   * @param message - Message to log
   * @param data - Additional data to log
   */
  public warn(message: string, data: any = {}): void {
    this.log('warn', message, data);
  }
  
  /**
   * Log an error message
   * 
   * @param message - Message to log
   * @param data - Additional data to log
   */
  public error(message: string, data: any = {}): void {
    this.log('error', message, data);
  }
  
  /**
   * Log a message
   * 
   * @param level - Log level
   * @param message - Message to log
   * @param data - Additional data to log
   * @private
   */
  private log(level: LogLevel, message: string, data: any = {}): void {
    if (!this.options.enabled) {
      return;
    }
    
    // Check if level is enabled
    if (!this.isLevelEnabled(level)) {
      return;
    }
    
    // Format message
    const formattedMessage = `${this.options.prefix} [${level.toUpperCase()}] [${this.name}] ${message}`;
    
    // Log message
    switch (level) {
      case 'debug':
        console.debug(formattedMessage, data);
        break;
      case 'info':
        console.info(formattedMessage, data);
        break;
      case 'warn':
        console.warn(formattedMessage, data);
        break;
      case 'error':
        console.error(formattedMessage, data);
        break;
    }
  }
  
  /**
   * Check if level is enabled
   * 
   * @param level - Log level
   * @returns Whether level is enabled
   * @private
   */
  private isLevelEnabled(level: LogLevel): boolean {
    const levels: LogLevel[] = ['debug', 'info', 'warn', 'error'];
    const currentLevelIndex = levels.indexOf(this.options.level as LogLevel);
    const levelIndex = levels.indexOf(level);
    
    return levelIndex >= currentLevelIndex;
  }
}
