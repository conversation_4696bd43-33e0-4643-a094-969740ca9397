workspace = {}

[package]
name = "wry"
version = "0.51.2"
authors = ["Tauri Programme within The Commons Conservancy"]
edition = "2021"
license = "Apache-2.0 OR MIT"
description = "Cross-platform WebView rendering library"
readme = "README.md"
repository = "https://github.com/tauri-apps/wry"
documentation = "https://docs.rs/wry"
categories = ["gui"]
rust-version = "1.77"
exclude = ["/.changes", "/.github", "/audits", "/wry-logo.svg"]

[package.metadata.docs.rs]
no-default-features = true
features = ["drag-drop", "protocol", "os-webview"]
targets = [
  "x86_64-unknown-linux-gnu",
  "x86_64-pc-windows-msvc",
  "x86_64-apple-darwin",
]
rustc-args = ["--cfg", "docsrs"]
rustdoc-args = ["--cfg", "docsrs"]

[features]
default = ["drag-drop", "protocol", "os-webview", "x11"]
serde = ["dpi/serde"]
drag-drop = []
protocol = []
devtools = []
transparent = []
fullscreen = []
linux-body = ["webkit2gtk/v2_40", "os-webview"]
mac-proxy = []
os-webview = [
  "javascriptcore-rs",
  "webkit2gtk",
  "webkit2gtk-sys",
  "dep:gtk",
  "soup3",
]
x11 = [
  "x11-dl",
  "gdkx11",
]
tracing = ["dep:tracing"]

[dependencies]
tracing = { version = "0.1", optional = true }
once_cell = "1"
thiserror = "2.0"
http = "1.1"
raw-window-handle = { version = "0.6", features = ["std"] }
dpi = "0.1"
cookie = "0.18"

[target."cfg(any(target_os = \"linux\", target_os = \"dragonfly\", target_os = \"freebsd\", target_os = \"openbsd\", target_os = \"netbsd\"))".dependencies]
javascriptcore-rs = { version = "=1.1.2", features = [
  "v2_28",
], optional = true }
webkit2gtk = { version = "=2.0.1", features = ["v2_38"], optional = true }
webkit2gtk-sys = { version = "=2.0.1", optional = true }
gtk = { version = "0.18", optional = true }
soup3 = { version = "0.5", optional = true }
x11-dl = { version = "2.21", optional = true }
gdkx11 = { version = "0.18", optional = true }
percent-encoding = "2.3"

[target."cfg(target_os = \"windows\")".dependencies]
webview2-com = "0.37"
windows-version = "0.1"
windows-core = "0.61"
dunce = "1"

[target."cfg(target_os = \"windows\")".dependencies.windows]
version = "0.61"
features = [
  "Win32_Foundation",
  "Win32_Graphics_Gdi",
  "Win32_System_Com",
  "Win32_System_Com_StructuredStorage",
  "Win32_System_LibraryLoader",
  "Win32_System_Ole",
  "Win32_System_SystemInformation",
  "Win32_System_SystemServices",
  "Win32_UI_Shell",
  "Win32_UI_WindowsAndMessaging",
  "Win32_Globalization",
  "Win32_UI_HiDpi",
  "Win32_UI_Input",
  "Win32_UI_Input_KeyboardAndMouse",
]

[target.'cfg(target_vendor = "apple")'.dependencies]
url = "2.5"
block2 = "0.6"
objc2 = { version = "0.6", features = [
    "exception",
    # because `NSUUID::from_bytes` needs it,
    # and `WebViewBuilderExtDarwin.with_data_store_identifier` crashes otherwise,
    # see https://github.com/tauri-apps/tauri/issues/12843
    "disable-encoding-assertions",
] }
objc2-web-kit = { version = "0.3.0", default-features = false, features = [
  "std",
  "objc2-core-foundation",
  "objc2-app-kit",
  "block2",
  "WKWebView",
  "WKWebViewConfiguration",
  "WKWebsiteDataStore",
  "WKDownload",
  "WKDownloadDelegate",
  "WKNavigation",
  "WKNavigationDelegate",
  "WKUserContentController",
  "WKURLSchemeHandler",
  "WKPreferences",
  "WKURLSchemeTask",
  "WKScriptMessageHandler",
  "WKUIDelegate",
  "WKOpenPanelParameters",
  "WKFrameInfo",
  "WKSecurityOrigin",
  "WKScriptMessage",
  "WKNavigationAction",
  "WKWebpagePreferences",
  "WKNavigationResponse",
  "WKUserScript",
  "WKHTTPCookieStore",
] }
objc2-core-foundation = { version = "0.3.0", default-features = false, features = [
  "std",
  "CFCGTypes",
] }
objc2-foundation = { version = "0.3.0", default-features = false, features = [
  "std",
  "objc2-core-foundation",
  "NSURLRequest",
  "NSURL",
  "NSString",
  "NSKeyValueCoding",
  "NSStream",
  "NSDictionary",
  "NSObject",
  "NSData",
  "NSEnumerator",
  "NSKeyValueObserving",
  "NSThread",
  "NSJSONSerialization",
  "NSDate",
  "NSBundle",
  "NSProcessInfo",
  "NSValue",
  "NSRange",
  "NSRunLoop",
] }

[target.'cfg(target_os = "ios")'.dependencies]
objc2-ui-kit = { version = "0.3.0", default-features = false, features = [
  "std",
  "objc2-core-foundation",
  "UIResponder",
  "UIScrollView",
  "UIView",
  "UIWindow",
  "UIApplication",
  "UIEvent",
  "UIColor"
] }

[target.'cfg(target_os = "macos")'.dependencies]
objc2-app-kit = { version = "0.3.0", default-features = false, features = [
  "std",
  "objc2-core-foundation",
  "NSApplication",
  "NSButton",
  "NSControl",
  "NSEvent",
  "NSWindow",
  "NSView",
  "NSPasteboard",
  "NSPanel",
  "NSResponder",
  "NSOpenPanel",
  "NSSavePanel",
  "NSMenu",
] }

[target."cfg(target_os = \"android\")".dependencies]
crossbeam-channel = "0.5"
html5ever = "0.26"
kuchiki = { package = "kuchikiki", version = "0.8" }
sha2 = "0.10"
base64 = "0.22"
jni = "0.21"
ndk = "0.9"
tao-macros = "0.1"
libc = "0.2"

[dev-dependencies]
pollster = "0.4.0"
tao = "0.33"
wgpu = "23"
winit = "0.30"
getrandom = "0.3"
http-range = "0.1"
percent-encoding = "2.3"

[lints.rust.unexpected_cfgs]
level = "warn"
check-cfg = ["cfg(linux)", "cfg(gtk)"]
