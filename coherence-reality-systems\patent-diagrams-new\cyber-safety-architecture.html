<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cyber-Safety Protocol Architecture</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .diagram-container {
            position: relative;
            width: 900px;
            height: 600px;
            margin: 0 auto;
            background-color: white;
            border: 1px solid #ddd;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            box-sizing: border-box;
        }
        .container-box {
            position: absolute;
            border: 2px solid #666;
            border-radius: 10px;
            padding: 30px 10px 10px;
            box-sizing: border-box;
        }
        .container-label {
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            font-weight: bold;
            font-size: 16px;
            text-align: center;
            width: 90%;
        }
        .component-box {
            position: absolute;
            background-color: white;
            border: 2px solid #333;
            border-radius: 6px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            padding: 10px;
            box-sizing: border-box;
            font-size: 14px;
            line-height: 1.2;
        }
        .component-label {
            font-weight: bold;
            margin-bottom: 4px;
            text-align: center;
            width: 100%;
        }
        .component-number {
            position: absolute;
            top: -10px;
            left: -10px;
            width: 20px;
            height: 20px;
            background-color: #333;
            color: white;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 12px;
            font-weight: bold;
        }
        .arrow {
            position: absolute;
            background-color: #333;
            width: 2px;
        }
        .legend {
            position: absolute;
            right: 10px;
            bottom: -90px;
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 5px;
            width: 200px;
        }
        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
            font-size: 12px;
        }
        .legend-color {
            width: 15px;
            height: 15px;
            margin-right: 5px;
            border: 1px solid #333;
        }
        .inventor-label {
            position: absolute;
            left: 10px;
            bottom: -90px;
            font-size: 12px;
            font-style: italic;
            color: #333;
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
        }
        .component-content {
            text-align: center;
            width: 100%;
        }
    </style>
</head>
<body>
    <h1>FIG. 1: Cyber-Safety Protocol Architecture</h1>
    
    <div class="diagram-container">
        <!-- Main Container -->
        <div class="container-box" style="width: 800px; height: 500px; left: 50px; top: 20px;">
            <div class="container-label">CYBER-SAFETY PROTOCOL ARCHITECTURE</div>
        </div>
        
        <!-- Core Protocol Layer -->
        <div class="container-box" style="width: 700px; height: 100px; left: 100px; top: 70px;">
            <div class="container-label">CYBER-SAFETY PROTOCOL CORE</div>
        </div>
        
        <div class="component-box" style="left: 150px; top: 110px; width: 120px; height: 40px;">
            <div class="component-number">101</div>
            <div class="component-label">Native Unification</div>
            <div class="component-content">Engine</div>
        </div>
        
        <div class="component-box" style="left: 300px; top: 110px; width: 120px; height: 40px;">
            <div class="component-number">102</div>
            <div class="component-label">Dynamic UI</div>
            <div class="component-content">Enforcement</div>
        </div>
        
        <div class="component-box" style="left: 450px; top: 110px; width: 120px; height: 40px;">
            <div class="component-number">103</div>
            <div class="component-label">Cross-Domain</div>
            <div class="component-content">Intelligence</div>
        </div>
        
        <div class="component-box" style="left: 600px; top: 110px; width: 120px; height: 40px;">
            <div class="component-number">104</div>
            <div class="component-label">Protocol</div>
            <div class="component-content">Orchestration</div>
        </div>
        
        <!-- Universal Components Layer -->
        <div class="container-box" style="width: 700px; height: 150px; left: 100px; top: 200px;">
            <div class="container-label">UNIVERSAL COMPONENTS</div>
        </div>
        
        <div class="component-box" style="left: 130px; top: 240px; width: 100px; height: 40px;">
            <div class="component-number">105</div>
            <div class="component-label">NovaCore</div>
        </div>
        
        <div class="component-box" style="left: 240px; top: 240px; width: 100px; height: 40px;">
            <div class="component-number">106</div>
            <div class="component-label">NovaShield</div>
        </div>
        
        <div class="component-box" style="left: 350px; top: 240px; width: 100px; height: 40px;">
            <div class="component-number">107</div>
            <div class="component-label">NovaTrack</div>
        </div>
        
        <div class="component-box" style="left: 460px; top: 240px; width: 100px; height: 40px;">
            <div class="component-number">108</div>
            <div class="component-label">NovaLearn</div>
        </div>
        
        <div class="component-box" style="left: 570px; top: 240px; width: 100px; height: 40px;">
            <div class="component-number">109</div>
            <div class="component-label">NovaView</div>
        </div>
        
        <div class="component-box" style="left: 680px; top: 240px; width: 100px; height: 40px;">
            <div class="component-number">110</div>
            <div class="component-label">NovaFlowX</div>
        </div>
        
        <div class="component-box" style="left: 130px; top: 290px; width: 100px; height: 40px;">
            <div class="component-number">111</div>
            <div class="component-label">NovaPulse+</div>
        </div>
        
        <div class="component-box" style="left: 240px; top: 290px; width: 100px; height: 40px;">
            <div class="component-number">112</div>
            <div class="component-label">NovaProof</div>
        </div>
        
        <div class="component-box" style="left: 350px; top: 290px; width: 100px; height: 40px;">
            <div class="component-number">113</div>
            <div class="component-label">NovaThink</div>
        </div>
        
        <div class="component-box" style="left: 460px; top: 290px; width: 100px; height: 40px;">
            <div class="component-number">114</div>
            <div class="component-label">NovaConnect</div>
        </div>
        
        <div class="component-box" style="left: 570px; top: 290px; width: 100px; height: 40px;">
            <div class="component-number">115</div>
            <div class="component-label">NovaVision</div>
        </div>
        
        <div class="component-box" style="left: 680px; top: 290px; width: 100px; height: 40px;">
            <div class="component-number">116</div>
            <div class="component-label">NovaDNA</div>
        </div>
        
        <!-- Implementation Layer -->
        <div class="container-box" style="width: 700px; height: 100px; left: 100px; top: 380px;">
            <div class="container-label">INDUSTRY-SPECIFIC IMPLEMENTATIONS</div>
        </div>
        
        <div class="component-box" style="left: 130px; top: 420px; width: 100px; height: 40px;">
            <div class="component-number">117</div>
            <div class="component-label">Healthcare</div>
        </div>
        
        <div class="component-box" style="left: 240px; top: 420px; width: 100px; height: 40px;">
            <div class="component-number">118</div>
            <div class="component-label">Financial</div>
        </div>
        
        <div class="component-box" style="left: 350px; top: 420px; width: 100px; height: 40px;">
            <div class="component-number">119</div>
            <div class="component-label">Education</div>
        </div>
        
        <div class="component-box" style="left: 460px; top: 420px; width: 100px; height: 40px;">
            <div class="component-number">120</div>
            <div class="component-label">Government</div>
        </div>
        
        <div class="component-box" style="left: 570px; top: 420px; width: 100px; height: 40px;">
            <div class="component-number">121</div>
            <div class="component-label">Infrastructure</div>
        </div>
        
        <div class="component-box" style="left: 680px; top: 420px; width: 100px; height: 40px;">
            <div class="component-number">122</div>
            <div class="component-label">Mobile/IoT</div>
        </div>
        
        <!-- Connecting arrows -->
        <div class="arrow" style="left: 210px; top: 170px; height: 30px;"></div>
        <div class="arrow" style="left: 360px; top: 170px; height: 30px;"></div>
        <div class="arrow" style="left: 510px; top: 170px; height: 30px;"></div>
        <div class="arrow" style="left: 660px; top: 170px; height: 30px;"></div>
        
        <div class="arrow" style="left: 210px; top: 350px; height: 30px;"></div>
        <div class="arrow" style="left: 360px; top: 350px; height: 30px;"></div>
        <div class="arrow" style="left: 510px; top: 350px; height: 30px;"></div>
        <div class="arrow" style="left: 660px; top: 350px; height: 30px;"></div>
        
        <!-- Legend -->
        <div class="legend">
            <div class="legend-item">
                <div class="legend-color" style="background-color: #000;"></div>
                <div>Protocol Core</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #000;"></div>
                <div>Universal Components</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #000;"></div>
                <div>Industry Implementations</div>
            </div>
        </div>
        
        <!-- Inventor Label -->
        <div class="inventor-label">Inventor: David Nigel Irvin</div>
    </div>
</body>
</html>
