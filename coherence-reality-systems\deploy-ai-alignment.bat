@echo off
echo ========================================
echo   NovaFuse AI Alignment Deployment
echo ========================================
echo.

echo [INFO] Checking Docker status...
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Docker is not installed or not running
    echo Please install Docker Desktop and ensure it's running
    pause
    exit /b 1
)

echo [INFO] Docker is available
echo.

echo [1/6] Preparing AI Alignment Demo...
cd ai-alignment-demo
if not exist "package.json" (
    echo ERROR: AI Alignment Demo package.json not found
    cd ..
    pause
    exit /b 1
)

echo [INFO] Installing AI Alignment dependencies...
call npm install
if %errorlevel% neq 0 (
    echo ERROR: Failed to install AI Alignment dependencies
    cd ..
    pause
    exit /b 1
)

echo [INFO] Building AI Alignment application...
call npm run build
if %errorlevel% neq 0 (
    echo ERROR: Failed to build AI Alignment application
    cd ..
    pause
    exit /b 1
)

cd ..
echo.

echo [2/6] Stopping existing containers...
docker-compose down
echo.

echo [3/6] Building all Docker images...
docker-compose build
if %errorlevel% neq 0 (
    echo ERROR: Failed to build Docker images
    pause
    exit /b 1
)

echo.
echo [4/6] Starting core services...
docker-compose up -d kong-database kong-migrations kong
echo [INFO] Waiting for Kong to be ready...
timeout /t 10 /nobreak >nul

echo.
echo [5/6] Starting all services including AI Alignment...
docker-compose up -d
if %errorlevel% neq 0 (
    echo ERROR: Failed to start services
    pause
    exit /b 1
)

echo.
echo [6/6] Verifying deployment...
timeout /t 5 /nobreak >nul

echo.
echo ========================================
echo   Deployment Status
echo ========================================
docker-compose ps

echo.
echo ========================================
echo   AI Alignment Demo Ready!
echo ========================================
echo.
echo 🤖 AI Alignment Studio: http://localhost:3004
echo 🌐 Kong Admin:          http://localhost:8001
echo 📚 API Documentation:   http://localhost:8889
echo 🔧 Konga UI:           http://localhost:1337
echo.
echo [INFO] AI Alignment Demo Features:
echo   - Real-time AI consciousness monitoring
echo   - Superintelligence safety controls
echo   - AGI/ASI alignment protocols
echo   - Emergency containment systems
echo.
echo Press any key to open AI Alignment Demo...
pause >nul
start http://localhost:3004
