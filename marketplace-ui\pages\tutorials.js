import { useState } from "react";
import Head from "next/head";
import Link from "next/link";

export default function Tutorials() {
  const [activeCategory, setActiveCategory] = useState("getting-started");
  const [searchQuery, setSearchQuery] = useState("");

  const categories = [
    { id: "getting-started", name: "Getting Started" },
    { id: "privacy-api", name: "Privacy Management API" },
    { id: "security-api", name: "Security Assessment API" },
    { id: "compliance-api", name: "Compliance API" },
    { id: "control-api", name: "Control Testing API" },
    { id: "esg-api", name: "ESG API" },
    { id: "uac", name: "Universal API Connector" },
  ];

  const tutorials = {
    "getting-started": [
      {
        id: "quickstart",
        title: "Quick Start Guide",
        description: "Learn how to get started with NovaFuse APIs in 15 minutes",
        difficulty: "Beginner",
        duration: "15 min",
        language: "JavaScript",
        image: "/images/tutorials/quickstart.jpg"
      },
      {
        id: "authentication",
        title: "Authentication Guide",
        description: "Learn how to authenticate with NovaFuse APIs",
        difficulty: "Beginner",
        duration: "10 min",
        language: "JavaScript",
        image: "/images/tutorials/authentication.jpg"
      },
      {
        id: "error-handling",
        title: "Error Handling",
        description: "Best practices for handling errors in NovaFuse APIs",
        difficulty: "Intermediate",
        duration: "20 min",
        language: "JavaScript",
        image: "/images/tutorials/error-handling.jpg"
      }
    ],
    "privacy-api": [
      {
        id: "dsar-processing",
        title: "DSAR Processing",
        description: "Learn how to process Data Subject Access Requests",
        difficulty: "Intermediate",
        duration: "25 min",
        language: "JavaScript",
        image: "/images/tutorials/dsar.jpg"
      },
      {
        id: "consent-management",
        title: "Consent Management",
        description: "Implement consent management with the Privacy API",
        difficulty: "Intermediate",
        duration: "30 min",
        language: "JavaScript",
        image: "/images/tutorials/consent.jpg"
      }
    ],
    "security-api": [
      {
        id: "vulnerability-scanning",
        title: "Vulnerability Scanning",
        description: "Learn how to scan for vulnerabilities using the Security API",
        difficulty: "Advanced",
        duration: "45 min",
        language: "Python",
        image: "/images/tutorials/vulnerability.jpg"
      }
    ],
    "compliance-api": [
      {
        id: "compliance-reporting",
        title: "Compliance Reporting",
        description: "Generate compliance reports using the Compliance API",
        difficulty: "Intermediate",
        duration: "35 min",
        language: "JavaScript",
        image: "/images/tutorials/compliance.jpg"
      }
    ],
    "control-api": [
      {
        id: "control-testing",
        title: "Control Testing",
        description: "Learn how to test controls using the Control Testing API",
        difficulty: "Intermediate",
        duration: "30 min",
        language: "JavaScript",
        image: "/images/tutorials/control.jpg"
      }
    ],
    "esg-api": [
      {
        id: "esg-reporting",
        title: "ESG Reporting",
        description: "Generate ESG reports using the ESG API",
        difficulty: "Intermediate",
        duration: "40 min",
        language: "JavaScript",
        image: "/images/tutorials/esg.jpg"
      }
    ],
    "uac": [
      {
        id: "connector-creation",
        title: "Creating a Connector",
        description: "Learn how to create a custom connector with the UAC",
        difficulty: "Advanced",
        duration: "60 min",
        language: "JavaScript",
        image: "/images/tutorials/connector.jpg"
      },
      {
        id: "connector-execution",
        title: "Executing a Connector",
        description: "Learn how to execute a connector with the UAC",
        difficulty: "Intermediate",
        duration: "25 min",
        language: "JavaScript",
        image: "/images/tutorials/execution.jpg"
      }
    ]
  };

  // Filter tutorials based on search query
  const filteredTutorials = searchQuery
    ? Object.values(tutorials)
        .flat()
        .filter(
          tutorial =>
            tutorial.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
            tutorial.description.toLowerCase().includes(searchQuery.toLowerCase())
        )
    : tutorials[activeCategory];

  return (
    <div className="min-h-screen bg-gray-50">
      <Head>
        <title>NovaFuse Tutorials</title>
        <meta name="description" content="NovaFuse API tutorials and guides" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold text-gray-900">Tutorials</h1>
          <div className="flex space-x-4">
            <button className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
              Request a Tutorial
            </button>
            <button className="border border-blue-600 text-blue-600 px-4 py-2 rounded hover:bg-blue-50">
              View Documentation
            </button>
          </div>
        </div>

        <div className="mb-6">
          <label htmlFor="search" className="sr-only">
            Search Tutorials
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg
                className="h-5 w-5 text-gray-400"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                fill="currentColor"
                aria-hidden="true"
              >
                <path
                  fillRule="evenodd"
                  d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <input
              id="search"
              name="search"
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              placeholder="Search tutorials"
              type="search"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>

        <div className="flex flex-col md:flex-row">
          {/* Sidebar */}
          {!searchQuery && (
            <div className="w-full md:w-64 flex-shrink-0 mb-6 md:mb-0 md:mr-8">
              <div className="bg-white rounded-lg shadow overflow-hidden">
                <div className="p-4 border-b border-gray-200">
                  <h2 className="font-semibold text-gray-800">Categories</h2>
                </div>
                <nav className="p-4">
                  <ul className="space-y-2">
                    {categories.map((category) => (
                      <li key={category.id}>
                        <button
                          onClick={() => setActiveCategory(category.id)}
                          className={`text-left w-full px-2 py-1 rounded ${
                            activeCategory === category.id
                              ? "bg-blue-100 text-blue-700 font-medium"
                              : "text-gray-700 hover:bg-gray-100"
                          }`}
                        >
                          {category.name}
                        </button>
                      </li>
                    ))}
                  </ul>
                </nav>
              </div>
            </div>
          )}

          {/* Main Content */}
          <div className="flex-1">
            {searchQuery ? (
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                Search Results for "{searchQuery}"
              </h2>
            ) : (
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                {categories.find(c => c.id === activeCategory)?.name} Tutorials
              </h2>
            )}

            {filteredTutorials.length === 0 ? (
              <div className="bg-white rounded-lg shadow p-6 text-center">
                <p className="text-gray-500">No tutorials found matching your search.</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
                {filteredTutorials.map((tutorial) => (
                  <div key={tutorial.id} className="bg-white rounded-lg shadow overflow-hidden">
                    <div className="h-40 bg-gray-200">
                      {/* Placeholder for tutorial image */}
                      <div className="h-full w-full flex items-center justify-center bg-blue-100">
                        <svg className="h-12 w-12 text-blue-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                        </svg>
                      </div>
                    </div>
                    <div className="p-4">
                      <h3 className="text-lg font-semibold text-gray-900 mb-1">
                        {tutorial.title}
                      </h3>
                      <p className="text-gray-600 text-sm mb-3">
                        {tutorial.description}
                      </p>
                      <div className="flex flex-wrap gap-2 mb-4">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          {tutorial.difficulty}
                        </span>
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          {tutorial.duration}
                        </span>
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                          {tutorial.language}
                        </span>
                      </div>
                      <Link
                        href={`/tutorials/${tutorial.id}`}
                        className="block w-full bg-blue-600 text-center text-white px-4 py-2 rounded hover:bg-blue-700"
                      >
                        View Tutorial
                      </Link>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Featured Tutorial */}
        <div className="mt-12 bg-gradient-to-r from-blue-600 to-blue-800 rounded-lg shadow-lg overflow-hidden">
          <div className="md:flex">
            <div className="md:flex-1 p-8">
              <span className="inline-flex items-center px-3 py-0.5 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                Featured Tutorial
              </span>
              <h3 className="mt-2 text-2xl font-bold text-white">
                Building a Complete GRC Solution with NovaFuse
              </h3>
              <p className="mt-3 text-blue-100">
                Learn how to build a complete governance, risk, and compliance solution using NovaFuse APIs and the Universal API Connector.
              </p>
              <div className="mt-4 flex flex-wrap gap-2">
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-900 text-blue-100">
                  Advanced
                </span>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-900 text-blue-100">
                  90 min
                </span>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-900 text-blue-100">
                  JavaScript
                </span>
              </div>
              <div className="mt-6">
                <Link
                  href="/tutorials/complete-grc-solution"
                  className="inline-flex items-center px-4 py-2 border border-transparent text-base font-medium rounded-md shadow-sm text-blue-700 bg-white hover:bg-blue-50"
                >
                  Start Tutorial
                </Link>
              </div>
            </div>
            <div className="md:flex-1 bg-blue-900">
              {/* Placeholder for featured tutorial image */}
              <div className="h-64 md:h-full w-full flex items-center justify-center">
                <svg className="h-24 w-24 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                </svg>
              </div>
            </div>
          </div>
        </div>

        {/* Community Resources */}
        <div className="mt-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Community Resources</h2>
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-3">
            <div className="bg-white rounded-lg shadow overflow-hidden">
              <div className="p-6">
                <div className="flex items-center justify-center h-12 w-12 rounded-md bg-blue-100 text-blue-600 mb-4">
                  <svg className="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Community Forum
                </h3>
                <p className="text-gray-600 mb-4">
                  Join our community forum to ask questions, share knowledge, and connect with other developers.
                </p>
                <a
                  href="https://community.novafuse.com"
                  className="text-blue-600 hover:text-blue-500 font-medium"
                >
                  Visit Forum →
                </a>
              </div>
            </div>
            <div className="bg-white rounded-lg shadow overflow-hidden">
              <div className="p-6">
                <div className="flex items-center justify-center h-12 w-12 rounded-md bg-blue-100 text-blue-600 mb-4">
                  <svg className="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 4a2 2 0 114 0v1a1 1 0 001 1h3a1 1 0 011 1v3a1 1 0 01-1 1h-1a2 2 0 100 4h1a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-1a2 2 0 10-4 0v1a1 1 0 01-1 1H7a1 1 0 01-1-1v-3a1 1 0 00-1-1H4a2 2 0 110-4h1a1 1 0 001-1V7a1 1 0 011-1h3a1 1 0 001-1V4z" />
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Sample Projects
                </h3>
                <p className="text-gray-600 mb-4">
                  Explore sample projects and code snippets to jumpstart your integration with NovaFuse.
                </p>
                <a
                  href="https://github.com/novafuse/samples"
                  className="text-blue-600 hover:text-blue-500 font-medium"
                >
                  View Samples →
                </a>
              </div>
            </div>
            <div className="bg-white rounded-lg shadow overflow-hidden">
              <div className="p-6">
                <div className="flex items-center justify-center h-12 w-12 rounded-md bg-blue-100 text-blue-600 mb-4">
                  <svg className="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Video Tutorials
                </h3>
                <p className="text-gray-600 mb-4">
                  Watch video tutorials and webinars to learn how to use NovaFuse APIs and the UAC.
                </p>
                <a
                  href="https://www.youtube.com/novafuse"
                  className="text-blue-600 hover:text-blue-500 font-medium"
                >
                  Watch Videos →
                </a>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
