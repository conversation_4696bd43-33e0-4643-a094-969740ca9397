/**
 * Resonance Connector
 * 
 * Implements Layer 2 of the Ripple Effect: Adjacent Resonance (The Waves)
 * 
 * This connector propagates φ-harmonics to adjacent systems, enabling
 * 5-15% performance uplifts in connected systems without code changes.
 */

const EventEmitter = require('events');
const { generateUUID } = require('../utils');
const { QuantumStateInferenceEngine } = require('./engine');

/**
 * Resonance Pattern Generator
 * 
 * Generates resonance patterns based on different mathematical sequences.
 */
class ResonancePatternGenerator {
  /**
   * Generate Fibonacci sequence
   * 
   * @param {number} length - Sequence length
   * @returns {Array} - Fibonacci sequence
   */
  static fibonacci(length) {
    const sequence = [1, 1];
    
    while (sequence.length < length) {
      sequence.push(sequence[sequence.length - 1] + sequence[sequence.length - 2]);
    }
    
    // Normalize
    const max = Math.max(...sequence);
    return sequence.map(val => val / max);
  }
  
  /**
   * Generate Golden Ratio sequence
   * 
   * @param {number} length - Sequence length
   * @returns {Array} - Golden Ratio sequence
   */
  static goldenRatio(length) {
    const phi = 0.618033988749895; // 1/φ
    const sequence = [];
    
    for (let i = 0; i < length; i++) {
      sequence.push(Math.pow(phi, i));
    }
    
    // Normalize
    const max = Math.max(...sequence);
    return sequence.map(val => val / max);
  }
  
  /**
   * Generate Pi sequence
   * 
   * @param {number} length - Sequence length
   * @returns {Array} - Pi sequence
   */
  static pi(length) {
    const piDigits = '31415926535897932384626433832795028841971693993751058209749445923';
    const sequence = [];
    
    for (let i = 0; i < length; i++) {
      const digit = parseInt(piDigits[i % piDigits.length]);
      sequence.push(digit / 10);
    }
    
    return sequence;
  }
  
  /**
   * Generate Trinity sequence
   * 
   * @param {number} length - Sequence length
   * @returns {Array} - Trinity sequence
   */
  static trinity(length) {
    const sequence = [];
    
    for (let i = 0; i < length; i++) {
      // Combine pi, phi, and e in a trinity pattern
      const piComponent = Math.sin(i * Math.PI / 3);
      const phiComponent = Math.pow(0.618033988749895, i % 3);
      const eComponent = Math.pow(Math.E, -((i % 3) / 3));
      
      sequence.push((piComponent + phiComponent + eComponent) / 3);
    }
    
    // Normalize
    const max = Math.max(...sequence);
    const min = Math.min(...sequence);
    return sequence.map(val => (val - min) / (max - min));
  }
  
  /**
   * Generate pattern
   * 
   * @param {string} type - Pattern type
   * @param {number} length - Sequence length
   * @returns {Array} - Pattern sequence
   */
  static generate(type, length) {
    switch (type) {
      case 'fibonacci':
        return ResonancePatternGenerator.fibonacci(length);
      case 'goldenRatio':
        return ResonancePatternGenerator.goldenRatio(length);
      case 'pi':
        return ResonancePatternGenerator.pi(length);
      case 'trinity':
        return ResonancePatternGenerator.trinity(length);
      default:
        return ResonancePatternGenerator.fibonacci(length);
    }
  }
}

/**
 * Resonance Connector
 * 
 * Connects to adjacent systems and propagates φ-harmonics.
 */
class ResonanceConnector extends EventEmitter {
  /**
   * Constructor
   * 
   * @param {QuantumStateInferenceEngine} engine - Quantum State Inference Engine
   * @param {Object} options - Connector options
   */
  constructor(engine, options = {}) {
    super();
    
    if (!(engine instanceof QuantumStateInferenceEngine)) {
      throw new Error('Engine must be an instance of QuantumStateInferenceEngine');
    }
    
    this.engine = engine;
    this.id = generateUUID();
    
    this.options = {
      resonanceStrength: 0.18,  // 18% influence
      propagationDistance: 5,   // Network hops
      harmonicPattern: 'fibonacci',
      resonanceInterval: 1618,  // φ-based interval (ms)
      enableLogging: false,
      ...options
    };
    
    // Initialize connected systems registry
    this.connectedSystems = new Map();
    
    // Initialize resonance pattern
    this.resonancePattern = ResonancePatternGenerator.generate(
      this.options.harmonicPattern,
      this.options.propagationDistance + 1
    );
    
    // Initialize resonance timer
    this.resonanceTimer = null;
    
    if (this.options.enableLogging) {
      console.log('Resonance Connector initialized with options:', this.options);
    }
  }
  
  /**
   * Connect to an adjacent system
   * 
   * @param {Object} system - Adjacent system
   * @param {Object} options - Connection options
   * @returns {string} - Connection ID
   */
  connectTo(system, options = {}) {
    if (!system) {
      throw new Error('System is required');
    }
    
    const connectionId = generateUUID();
    
    this.connectedSystems.set(connectionId, {
      system,
      distance: options.distance || 1,
      adapter: options.adapter || this._defaultAdapter,
      lastResonance: null,
      options: {
        resonanceStrength: options.resonanceStrength || this.options.resonanceStrength,
        ...options
      }
    });
    
    if (this.options.enableLogging) {
      console.log(`Connected to system with ID: ${connectionId}`);
    }
    
    // Emit connection event
    this.emit('connected', {
      connectionId,
      system,
      options
    });
    
    return connectionId;
  }
  
  /**
   * Disconnect from an adjacent system
   * 
   * @param {string} connectionId - Connection ID
   */
  disconnect(connectionId) {
    if (!this.connectedSystems.has(connectionId)) {
      throw new Error(`Connection with ID ${connectionId} not found`);
    }
    
    const connection = this.connectedSystems.get(connectionId);
    this.connectedSystems.delete(connectionId);
    
    if (this.options.enableLogging) {
      console.log(`Disconnected from system with ID: ${connectionId}`);
    }
    
    // Emit disconnection event
    this.emit('disconnected', {
      connectionId,
      system: connection.system
    });
  }
  
  /**
   * Start resonance propagation
   */
  startResonance() {
    if (this.resonanceTimer) {
      return;
    }
    
    this.resonanceTimer = setInterval(() => {
      this._propagateResonance();
    }, this.options.resonanceInterval);
    
    if (this.options.enableLogging) {
      console.log('Started resonance propagation');
    }
    
    // Emit start event
    this.emit('resonanceStarted');
  }
  
  /**
   * Stop resonance propagation
   */
  stopResonance() {
    if (!this.resonanceTimer) {
      return;
    }
    
    clearInterval(this.resonanceTimer);
    this.resonanceTimer = null;
    
    if (this.options.enableLogging) {
      console.log('Stopped resonance propagation');
    }
    
    // Emit stop event
    this.emit('resonanceStopped');
  }
  
  /**
   * Propagate resonance to connected systems
   * 
   * @private
   */
  _propagateResonance() {
    if (this.connectedSystems.size === 0) {
      return;
    }
    
    // Get current state from engine
    const currentState = this._getCurrentState();
    
    // Propagate to each connected system
    for (const [connectionId, connection] of this.connectedSystems) {
      try {
        // Calculate resonance strength based on distance
        const distanceIndex = Math.min(connection.distance, this.resonancePattern.length - 1);
        const distanceModifier = this.resonancePattern[distanceIndex];
        const effectiveStrength = connection.options.resonanceStrength * distanceModifier;
        
        // Create resonance data
        const resonanceData = {
          source: this.id,
          timestamp: new Date(),
          strength: effectiveStrength,
          pattern: this.options.harmonicPattern,
          state: currentState,
          harmonics: this._generateHarmonics(currentState, effectiveStrength)
        };
        
        // Apply resonance to connected system
        connection.adapter(connection.system, resonanceData, connection.options);
        
        // Update last resonance timestamp
        connection.lastResonance = new Date();
        
        if (this.options.enableLogging) {
          console.log(`Propagated resonance to system ${connectionId} with strength ${effectiveStrength.toFixed(4)}`);
        }
        
        // Emit resonance event
        this.emit('resonance', {
          connectionId,
          system: connection.system,
          resonanceData
        });
      } catch (error) {
        if (this.options.enableLogging) {
          console.error(`Error propagating resonance to system ${connectionId}:`, error);
        }
        
        // Emit error event
        this.emit('error', {
          connectionId,
          system: connection.system,
          error
        });
      }
    }
  }
  
  /**
   * Get current state from engine
   * 
   * @returns {Object} - Current state
   * @private
   */
  _getCurrentState() {
    // Extract relevant state information from engine
    return {
      stateCount: this.engine.stateRegistry.size,
      averageCertainty: this._calculateAverageCertainty(),
      recentPredictions: this._getRecentPredictions()
    };
  }
  
  /**
   * Calculate average certainty
   * 
   * @returns {number} - Average certainty
   * @private
   */
  _calculateAverageCertainty() {
    if (this.engine.stateRegistry.size === 0) {
      return 0;
    }
    
    let totalCertainty = 0;
    
    for (const [id, vector] of this.engine.stateRegistry) {
      totalCertainty += vector.certainty;
    }
    
    return totalCertainty / this.engine.stateRegistry.size;
  }
  
  /**
   * Get recent predictions
   * 
   * @returns {Array} - Recent predictions
   * @private
   */
  _getRecentPredictions() {
    // In a real implementation, this would return recent predictions
    // For now, return an empty array
    return [];
  }
  
  /**
   * Generate harmonics
   * 
   * @param {Object} state - Current state
   * @param {number} strength - Resonance strength
   * @returns {Object} - Harmonics
   * @private
   */
  _generateHarmonics(state, strength) {
    // Generate φ-harmonics based on current state
    return {
      phi: {
        primary: 0.618033988749895 * strength,
        secondary: 0.381966011250105 * strength
      },
      pi: {
        primary: (Math.PI / 10) * strength,
        secondary: (1 / Math.PI) * strength
      },
      trinity: {
        governance: Math.PI * strength,
        detection: 0.618033988749895 * strength,
        response: (1 / Math.E) * strength
      }
    };
  }
  
  /**
   * Default adapter for connected systems
   * 
   * @param {Object} system - Connected system
   * @param {Object} resonanceData - Resonance data
   * @param {Object} options - Connection options
   * @private
   */
  _defaultAdapter(system, resonanceData, options) {
    // In a real implementation, this would adapt the resonance data
    // to the specific system type and apply it
    
    // For now, just check if the system has an applyResonance method
    if (system && typeof system.applyResonance === 'function') {
      system.applyResonance(resonanceData);
    }
  }
}

module.exports = {
  ResonancePatternGenerator,
  ResonanceConnector
};
