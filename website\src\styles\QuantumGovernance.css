.governance-field {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  color: #333;
}

.governance-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e0e0e0;
}

.governance-header h2 {
  margin: 0;
  color: #2c3e50;
  font-weight: 600;
}

.governance-content {
  display: grid;
  grid-template-columns: 1fr 1.5fr;
  gap: 2rem;
  margin-top: 1.5rem;
}

.task-form-container {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
}

.visualization-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.error-message {
  background-color: #ffebee;
  color: #c62828;
  padding: 1rem;
  border-radius: 4px;
  margin-top: 1rem;
  border-left: 4px solid #ef5350;
}

.quantum-indicator {
  display: inline-flex;
  align-items: center;
  background: rgba(138, 43, 226, 0.1);
  color: #8a2be2;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
  margin-left: 1rem;
}

.quantum-indicator::before {
  content: '';
  display: inline-block;
  width: 8px;
  height: 8px;
  background: #8a2be2;
  border-radius: 50%;
  margin-right: 6px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 0.6; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.2); }
  100% { opacity: 0.6; transform: scale(1); }
}

/* Responsive adjustments */
@media (max-width: 992px) {
  .governance-content {
    grid-template-columns: 1fr;
  }
  
  .visualization-container {
    margin-top: 1.5rem;
  }
}

/* Loading state */
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  margin: 1rem 0;
}

/* Animation for loading dots */
@keyframes blink {
  0%, 100% { opacity: 0.2; }
  50% { opacity: 1; }
}

.loading-dots span {
  animation: blink 1.4s infinite both;
}

.loading-dots span:nth-child(2) {
  animation-delay: 0.2s;
}

.loading-dots span:nth-child(3) {
  animation-delay: 0.4s;
}
