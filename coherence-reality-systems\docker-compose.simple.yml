version: '3.8'

services:
  kethernet:
    image: node:18-alpine
    container_name: kethernet-demo
    working_dir: /app
    ports:
      - "8080:8080"
    environment:
      - NODE_ENV=production
      - PORT=8080
      - HOST=0.0.0.0
    volumes:
      - .:/app
    command: >
      sh -c "
        echo '🔥 Starting KetherNet Consciousness Blockchain Demo!' &&
        echo '⚛️ Crown Consensus with UUFT ≥2847 threshold' &&
        echo '💰 Coherium (κ) and Aetherium (⍶) tokens active' &&
        echo '🌐 Consciousness-validated network operational' &&
        echo '' &&
        npm install express cors bn.js crypto &&
        echo '' &&
        echo '🚀 KetherNet blockchain starting...' &&
        node kethernet-demo.js
      "
    networks:
      - kethernet-net
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

networks:
  kethernet-net:
    driver: bridge
