/**
 * NovaFuse Universal API Connector - Database Connection
 * 
 * This module handles the MongoDB connection for the UAC.
 */

const mongoose = require('mongoose');
const { createLogger } = require('../utils/logger');

const logger = createLogger('database');

// Get MongoDB URI from environment
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/nova-connect';

// Connection options
const options = {
  useNewUrlParser: true,
  useUnifiedTopology: true,
  serverSelectionTimeoutMS: 5000,
  socketTimeoutMS: 45000,
  family: 4 // Use IPv4, skip trying IPv6
};

// Connection state
let isConnected = false;
let connectionPromise = null;

/**
 * Connect to MongoDB
 * @returns {Promise<mongoose.Connection>} Mongoose connection
 */
const connect = async () => {
  if (isConnected) {
    logger.debug('Using existing database connection');
    return mongoose.connection;
  }
  
  if (connectionPromise) {
    logger.debug('Connection in progress, waiting for it to complete');
    return connectionPromise;
  }
  
  logger.info(`Connecting to MongoDB at ${MONGODB_URI.replace(/\/\/([^:]+):([^@]+)@/, '//***:***@')}`);
  
  connectionPromise = mongoose.connect(MONGODB_URI, options)
    .then(() => {
      isConnected = true;
      logger.info('Successfully connected to MongoDB');
      
      // Set up connection event handlers
      mongoose.connection.on('error', (err) => {
        logger.error('MongoDB connection error:', err);
        isConnected = false;
      });
      
      mongoose.connection.on('disconnected', () => {
        logger.warn('MongoDB disconnected');
        isConnected = false;
      });
      
      mongoose.connection.on('reconnected', () => {
        logger.info('MongoDB reconnected');
        isConnected = true;
      });
      
      return mongoose.connection;
    })
    .catch((err) => {
      logger.error('Error connecting to MongoDB:', err);
      isConnected = false;
      connectionPromise = null;
      throw err;
    });
  
  return connectionPromise;
};

/**
 * Disconnect from MongoDB
 * @returns {Promise<void>}
 */
const disconnect = async () => {
  if (!isConnected) {
    logger.debug('No active connection to disconnect');
    return;
  }
  
  logger.info('Disconnecting from MongoDB');
  
  try {
    await mongoose.disconnect();
    isConnected = false;
    connectionPromise = null;
    logger.info('Successfully disconnected from MongoDB');
  } catch (err) {
    logger.error('Error disconnecting from MongoDB:', err);
    throw err;
  }
};

/**
 * Check if connected to MongoDB
 * @returns {boolean} Whether connected to MongoDB
 */
const isConnectedToDatabase = () => {
  return isConnected && mongoose.connection.readyState === 1;
};

/**
 * Get the MongoDB connection
 * @returns {mongoose.Connection} Mongoose connection
 */
const getConnection = () => {
  if (!isConnected) {
    throw new Error('Not connected to MongoDB');
  }
  
  return mongoose.connection;
};

module.exports = {
  connect,
  disconnect,
  isConnected: isConnectedToDatabase,
  getConnection
};
