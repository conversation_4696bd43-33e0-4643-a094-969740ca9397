/**
 * NovaConnect API Connection Testing Setup
 * 
 * This module sets up the test environment for API connection testing.
 */

const path = require('path');
const { spawn } = require('child_process');
const axios = require('axios');

// Configuration
const config = {
  mockApiPort: 3005,
  registryPort: 3006,
  authPort: 3007,
  executorPort: 3008,
  usageMeteringPort: 3009,
  connectorApiPort: 3010
};

// Service URLs
const mockApiUrl = `http://localhost:${config.mockApiPort}`;
const registryUrl = `http://localhost:${config.registryPort}`;
const authUrl = `http://localhost:${config.authPort}`;
const executorUrl = `http://localhost:${config.executorPort}`;
const usageMeteringUrl = `http://localhost:${config.usageMeteringPort}`;
const connectorApiUrl = `http://localhost:${config.connectorApiPort}`;

// Track running services
const services = new Map();

/**
 * Start a service
 * 
 * @param {string} script - Path to the script to run
 * @param {number} port - Port to run the service on
 * @param {string} name - Name of the service
 * @returns {ChildProcess} - The child process
 */
function startService(script, port, name) {
  console.log(`Starting ${name} on port ${port}...`);
  
  const service = spawn('node', [script], {
    env: { ...process.env, PORT: port },
    stdio: 'pipe'
  });
  
  service.stdout.on('data', (data) => {
    console.log(`[${name}] ${data.toString().trim()}`);
  });
  
  service.stderr.on('data', (data) => {
    console.error(`[${name}] ERROR: ${data.toString().trim()}`);
  });
  
  service.on('close', (code) => {
    console.log(`${name} exited with code ${code}`);
    services.delete(name);
  });
  
  services.set(name, service);
  return service;
}

/**
 * Wait for a service to be ready
 * 
 * @param {string} url - URL to check
 * @param {number} maxRetries - Maximum number of retries
 * @param {number} retryDelay - Delay between retries in milliseconds
 * @returns {Promise<boolean>} - True if the service is ready
 */
async function waitForService(url, maxRetries = 30, retryDelay = 1000) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      await axios.get(url);
      console.log(`Service at ${url} is ready`);
      return true;
    } catch (error) {
      console.log(`Waiting for service at ${url}... (${i + 1}/${maxRetries})`);
      await new Promise(resolve => setTimeout(resolve, retryDelay));
    }
  }
  
  throw new Error(`Service at ${url} is not ready after ${maxRetries} retries`);
}

/**
 * Start all services
 * 
 * @returns {Promise<void>}
 */
async function startAllServices() {
  try {
    // Start mock API
    startService(
      path.resolve(__dirname, '../mock-api/server.js'),
      config.mockApiPort,
      'Mock API'
    );
    
    // Start connector registry
    startService(
      path.resolve(__dirname, '../connector-registry/server.js'),
      config.registryPort,
      'Connector Registry'
    );
    
    // Start authentication service
    startService(
      path.resolve(__dirname, '../auth-service/server.js'),
      config.authPort,
      'Authentication Service'
    );
    
    // Start connector executor
    startService(
      path.resolve(__dirname, '../connector-executor/server.js'),
      config.executorPort,
      'Connector Executor'
    );
    
    // Start usage metering
    startService(
      path.resolve(__dirname, '../usage-metering/server.js'),
      config.usageMeteringPort,
      'Usage Metering'
    );
    
    // Wait for all services to be ready
    await Promise.all([
      waitForService(`${mockApiUrl}/health`),
      waitForService(`${registryUrl}/health`),
      waitForService(`${authUrl}/health`),
      waitForService(`${executorUrl}/health`),
      waitForService(`${usageMeteringUrl}/health`)
    ]);
    
    console.log('All services are ready');
    return true;
  } catch (error) {
    console.error('Error starting services:', error);
    stopAllServices();
    throw error;
  }
}

/**
 * Stop all services
 */
function stopAllServices() {
  console.log('Stopping all services...');
  
  for (const [name, service] of services.entries()) {
    console.log(`Stopping ${name}...`);
    service.kill();
  }
  
  services.clear();
  console.log('All services stopped');
}

module.exports = {
  config,
  mockApiUrl,
  registryUrl,
  authUrl,
  executorUrl,
  usageMeteringUrl,
  connectorApiUrl,
  startService,
  waitForService,
  startAllServices,
  stopAllServices
};
