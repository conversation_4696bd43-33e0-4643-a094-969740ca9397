global:
  scrape_interval: 15s
  evaluation_interval: 15s
  scrape_timeout: 10s

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets: []
      scheme: http
      timeout: 10s
      api_version: v1

# Rule files
rule_files:
  - "novafuse_rules.yml"

# Scrape configurations
scrape_configs:
  # Prometheus self-monitoring
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # NovaFuse API
  - job_name: 'novafuse-api'
    metrics_path: '/metrics'
    scrape_interval: 5s
    static_configs:
      - targets: ['novafuse-api:3000']
    
  # Quantum Inference metrics
  - job_name: 'quantum-inference'
    metrics_path: '/metrics/quantum'
    scrape_interval: 5s
    static_configs:
      - targets: ['novafuse-api:3000']

  # Trinity CSDE metrics
  - job_name: 'trinity-csde'
    metrics_path: '/metrics/trinity'
    scrape_interval: 5s
    static_configs:
      - targets: ['novafuse-api:3000']
      
  # NovaStore metrics
  - job_name: 'novastore'
    metrics_path: '/metrics/store'
    scrape_interval: 5s
    static_configs:
      - targets: ['novafuse-api:3000']
