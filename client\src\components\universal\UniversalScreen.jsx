import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { fetchSchema, fetchData, saveData, deleteData } from '../../services/apiService';
import DynamicTable from './DynamicTable';
import DynamicForm from './DynamicForm';
import DynamicDetail from './DynamicDetail';
import { Box, CircularProgress, Typography, Alert, Paper } from '@mui/material';

/**
 * Universal Screen Component
 * 
 * This component renders a dynamic screen based on the entity schema.
 * It supports list, detail, create, and edit views.
 */
const UniversalScreen = () => {
  const { entityType, id, mode = 'list' } = useParams();
  const navigate = useNavigate();
  
  const [schema, setSchema] = useState(null);
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  
  // Fetch schema and data
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Fetch schema
        const schemaResponse = await fetchSchema(entityType);
        const schemaData = schemaResponse.data;
        setSchema(schemaData);
        
        // Fetch entity data if in edit or view mode
        if (id && (mode === 'edit' || mode === 'view')) {
          const dataResponse = await fetchData(`${schemaData.apiEndpoint}/${id}`);
          setData(dataResponse.data);
        }
        
        setLoading(false);
      } catch (err) {
        console.error('Error loading data:', err);
        setError(err.message || 'Failed to load data');
        setLoading(false);
      }
    };
    
    loadData();
  }, [entityType, id, mode]);
  
  // Handle form submission
  const handleSubmit = async (formData) => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(null);
      
      if (mode === 'create') {
        const response = await saveData(schema.apiEndpoint, formData);
        setSuccess(`${schema.entityName} created successfully`);
        
        // Navigate to the detail view of the created entity
        setTimeout(() => {
          navigate(`/universal/${entityType}/view/${response.data._id}`);
        }, 1500);
      } else if (mode === 'edit') {
        await saveData(`${schema.apiEndpoint}/${id}`, formData, 'PUT');
        setSuccess(`${schema.entityName} updated successfully`);
        
        // Navigate to the detail view
        setTimeout(() => {
          navigate(`/universal/${entityType}/view/${id}`);
        }, 1500);
      }
      
      setLoading(false);
    } catch (err) {
      console.error('Error saving data:', err);
      setError(err.message || 'Failed to save data');
      setLoading(false);
    }
  };
  
  // Handle entity deletion
  const handleDelete = async () => {
    if (!window.confirm(`Are you sure you want to delete this ${schema.entityName}?`)) {
      return;
    }
    
    try {
      setLoading(true);
      setError(null);
      
      await deleteData(`${schema.apiEndpoint}/${id}`);
      setSuccess(`${schema.entityName} deleted successfully`);
      
      // Navigate to the list view
      setTimeout(() => {
        navigate(`/universal/${entityType}`);
      }, 1500);
      
      setLoading(false);
    } catch (err) {
      console.error('Error deleting data:', err);
      setError(err.message || 'Failed to delete data');
      setLoading(false);
    }
  };
  
  // Handle navigation
  const handleNavigate = (path) => {
    navigate(path);
  };
  
  // Render loading state
  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }
  
  // Render error state
  if (error) {
    return (
      <Box m={2}>
        <Alert severity="error">{error}</Alert>
      </Box>
    );
  }
  
  // Render if schema not found
  if (!schema) {
    return (
      <Box m={2}>
        <Alert severity="warning">No schema found for {entityType}</Alert>
      </Box>
    );
  }
  
  // Render success message
  const successMessage = success && (
    <Box mb={2}>
      <Alert severity="success">{success}</Alert>
    </Box>
  );
  
  // Render appropriate view based on mode
  return (
    <Paper elevation={2} sx={{ p: 2, m: 2 }}>
      {successMessage}
      
      {mode === 'list' && (
        <>
          <Typography variant="h4" gutterBottom>
            {schema.entityNamePlural}
          </Typography>
          <DynamicTable 
            schema={schema} 
            onNavigate={handleNavigate} 
          />
        </>
      )}
      
      {mode === 'view' && data && (
        <DynamicDetail 
          schema={schema} 
          data={data} 
          onEdit={() => navigate(`/universal/${entityType}/edit/${id}`)}
          onDelete={handleDelete}
          onBack={() => navigate(`/universal/${entityType}`)}
        />
      )}
      
      {(mode === 'create' || mode === 'edit') && (
        <DynamicForm 
          schema={schema} 
          data={data} 
          mode={mode}
          onSubmit={handleSubmit}
          onCancel={() => mode === 'edit' 
            ? navigate(`/universal/${entityType}/view/${id}`)
            : navigate(`/universal/${entityType}`)
          }
        />
      )}
    </Paper>
  );
};

export default UniversalScreen;
