const axios = require('axios');

class KetherNetLoadTester {
  constructor(baseUrl = 'http://localhost:8080') {
    this.baseUrl = baseUrl;
    this.results = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      responseTimes: [],
      errors: []
    };
  }

  async makeRequest(endpoint, method = 'GET', data = null) {
    const startTime = Date.now();
    
    try {
      let response;
      if (method === 'POST') {
        response = await axios.post(`${this.baseUrl}${endpoint}`, data);
      } else {
        response = await axios.get(`${this.baseUrl}${endpoint}`);
      }
      
      const responseTime = Date.now() - startTime;
      this.results.responseTimes.push(responseTime);
      this.results.successfulRequests++;
      
      return { success: true, responseTime, data: response.data };
    } catch (error) {
      const responseTime = Date.now() - startTime;
      this.results.failedRequests++;
      this.results.errors.push({
        endpoint,
        error: error.message,
        responseTime
      });
      
      return { success: false, responseTime, error: error.message };
    } finally {
      this.results.totalRequests++;
    }
  }

  async runConcurrentRequests(requests, concurrency = 10) {
    console.log(`🚀 Running ${requests.length} requests with concurrency ${concurrency}`);
    
    const chunks = [];
    for (let i = 0; i < requests.length; i += concurrency) {
      chunks.push(requests.slice(i, i + concurrency));
    }

    for (const chunk of chunks) {
      const promises = chunk.map(req => this.makeRequest(req.endpoint, req.method, req.data));
      await Promise.all(promises);
      
      // Show progress
      process.stdout.write(`\r📊 Progress: ${this.results.totalRequests}/${requests.length} requests`);
    }
    
    console.log('\n✅ Load test complete!');
  }

  generateLoadTestRequests(count = 100) {
    const requests = [];
    
    for (let i = 0; i < count; i++) {
      const rand = Math.random();
      
      if (rand < 0.2) {
        // Consciousness validation (20%)
        requests.push({
          endpoint: '/consciousness/validate',
          method: 'POST',
          data: {
            neural: Math.floor(Math.random() * 20) + 5,
            information: Math.floor(Math.random() * 25) + 10,
            coherence: Math.floor(Math.random() * 30) + 15
          }
        });
      } else if (rand < 0.4) {
        // Stats requests (20%)
        requests.push({ endpoint: '/stats' });
      } else if (rand < 0.6) {
        // Health checks (20%)
        requests.push({ endpoint: '/health' });
      } else if (rand < 0.8) {
        // Coherium balance (20%)
        requests.push({ endpoint: '/coherium/balance' });
      } else {
        // Block requests (20%)
        requests.push({ endpoint: '/blocks' });
      }
    }
    
    return requests;
  }

  calculateStats() {
    const responseTimes = this.results.responseTimes;
    const sorted = responseTimes.sort((a, b) => a - b);
    
    return {
      totalRequests: this.results.totalRequests,
      successfulRequests: this.results.successfulRequests,
      failedRequests: this.results.failedRequests,
      successRate: ((this.results.successfulRequests / this.results.totalRequests) * 100).toFixed(2),
      avgResponseTime: (responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length).toFixed(2),
      minResponseTime: Math.min(...responseTimes),
      maxResponseTime: Math.max(...responseTimes),
      p50: sorted[Math.floor(sorted.length * 0.5)],
      p95: sorted[Math.floor(sorted.length * 0.95)],
      p99: sorted[Math.floor(sorted.length * 0.99)]
    };
  }

  printResults() {
    const stats = this.calculateStats();
    
    console.log('\n🎯 KETHERNET LOAD TEST RESULTS');
    console.log('================================');
    console.log(`📊 Total Requests: ${stats.totalRequests}`);
    console.log(`✅ Successful: ${stats.successfulRequests}`);
    console.log(`❌ Failed: ${stats.failedRequests}`);
    console.log(`📈 Success Rate: ${stats.successRate}%`);
    console.log('\n⏱️  RESPONSE TIMES:');
    console.log(`   Average: ${stats.avgResponseTime}ms`);
    console.log(`   Min: ${stats.minResponseTime}ms`);
    console.log(`   Max: ${stats.maxResponseTime}ms`);
    console.log(`   P50: ${stats.p50}ms`);
    console.log(`   P95: ${stats.p95}ms`);
    console.log(`   P99: ${stats.p99}ms`);
    
    if (this.results.errors.length > 0) {
      console.log('\n❌ ERRORS:');
      this.results.errors.slice(0, 5).forEach(error => {
        console.log(`   ${error.endpoint}: ${error.error}`);
      });
      if (this.results.errors.length > 5) {
        console.log(`   ... and ${this.results.errors.length - 5} more errors`);
      }
    }
    
    console.log('\n🔥 KetherNet Load Test Complete!');
  }

  async runLoadTest(requestCount = 100, concurrency = 10) {
    console.log('🚀 Starting KetherNet Load Test...');
    console.log(`📊 Requests: ${requestCount} | Concurrency: ${concurrency}`);
    console.log('🌐 Testing consciousness blockchain performance...\n');

    const requests = this.generateLoadTestRequests(requestCount);
    await this.runConcurrentRequests(requests, concurrency);
    this.printResults();
  }
}

// CLI interface
if (require.main === module) {
  const tester = new KetherNetLoadTester();
  
  const requestCount = process.argv[2] ? parseInt(process.argv[2]) : 100;
  const concurrency = process.argv[3] ? parseInt(process.argv[3]) : 10;
  
  tester.runLoadTest(requestCount, concurrency);
}

module.exports = KetherNetLoadTester;
