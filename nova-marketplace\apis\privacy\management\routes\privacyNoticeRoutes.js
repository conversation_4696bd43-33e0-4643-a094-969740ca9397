/**
 * Privacy Notice Routes
 * 
 * This file defines the routes for privacy notices.
 */

const express = require('express');
const router = express.Router();
const { privacyNoticeController } = require('../controllers');

// Get all privacy notices
router.get('/', privacyNoticeController.getAllPrivacyNotices);

// Get a specific privacy notice by ID
router.get('/:id', privacyNoticeController.getPrivacyNoticeById);

// Create a new privacy notice
router.post('/', privacyNoticeController.createPrivacyNotice);

// Update a privacy notice
router.put('/:id', privacyNoticeController.updatePrivacyNotice);

// Delete a privacy notice
router.delete('/:id', privacyNoticeController.deletePrivacyNotice);

// Archive a privacy notice
router.post('/:id/archive', privacyNoticeController.archivePrivacyNotice);

// Get privacy notices by audience
router.get('/audiences/:audience', privacyNoticeController.getPrivacyNoticesByAudience);

// Get the latest version of a privacy notice
router.get('/latest', privacyNoticeController.getLatestPrivacyNotice);

// Compare two versions of a privacy notice
router.get('/compare', privacyNoticeController.comparePrivacyNoticeVersions);

module.exports = router;
