/**
 * Quantum Visualization Demo
 * Next.js Example Implementation
 * 
 * Protected by US63/XXXXXX, US63/YYYYYY
 */

import { useEffect, useRef, useState } from 'react';
import Head from 'next/head';
import dynamic from 'next/dynamic';
import { getIPProtection } from '../../../src/utils/ipProtection';

// Dynamically import the QuantumVisualizer component with SSR disabled
const QuantumVisualizer = dynamic(
  () => import('../../../src/components/QuantumVisualizer'),
  { ssr: false }
);

// Initialize IP protection
const ipProtection = getIPProtection({
  protectionLevel: 'high',
  watermarkText: 'NOVAFUSE QUANTUM STREAM',
  watermarkOpacity: 0.03,
  patentNumbers: ['US63/XXXXXX', 'US63/YYYYYY']
});

export default function QuantumVizPage() {
  const canvasRef = useRef(null);
  const [isReady, setIsReady] = useState(false);
  const [performanceMetrics, setPerformanceMetrics] = useState({
    fps: 0,
    qubits: 0,
    memory: 0
  });

  // Initialize the visualization when component mounts
  useEffect(() => {
    if (typeof window === 'undefined') return; // Skip during SSR
    
    let vizInstance = null;
    let frameId = null;
    let lastFrameTime = performance.now();
    let frameTimes = [];
    
    const initViz = async () => {
      try {
        // Initialize IP protection
        await ipProtection.initialize();
        
        // Initialize the visualizer
        const { default: QuantumViz } = await import('../../../src/utils/quantumViz');
        vizInstance = new QuantumViz({
          canvas: canvasRef.current,
          resolution: [window.innerWidth, window.innerHeight],
          maxQubits: 1000000,
          quality: 'high'
        });
        
        // Start the render loop
        const render = (timestamp) => {
          // Calculate FPS
          const now = performance.now();
          const delta = now - lastFrameTime;
          lastFrameTime = now;
          
          // Update FPS calculation (moving average)
          frameTimes.push(delta);
          if (frameTimes.length > 60) frameTimes.shift();
          const avgDelta = frameTimes.reduce((a, b) => a + b, 0) / frameTimes.length;
          const fps = 1000 / avgDelta;
          
          // Update performance metrics
          setPerformanceMetrics({
            fps: Math.round(fps * 10) / 10,
            qubits: vizInstance.getQubitCount(),
            memory: Math.round(performance.memory?.usedJSHeapSize / (1024 * 1024) || 0)
          });
          
          // Render the frame
          vizInstance.render();
          
          // Apply watermark if needed
          if (ipProtection.options.protectionLevel !== 'none') {
            ipProtection.applyWatermark(canvasRef.current);
          }
          
          // Request next frame
          frameId = requestAnimationFrame(render);
        };
        
        // Start rendering
        frameId = requestAnimationFrame(render);
        setIsReady(true);
        
        // Handle window resize
        const handleResize = () => {
          if (vizInstance) {
            vizInstance.setSize(window.innerWidth, window.innerHeight);
          }
        };
        
        window.addEventListener('resize', handleResize);
        
        // Cleanup function
        return () => {
          window.removeEventListener('resize', handleResize);
          if (frameId) cancelAnimationFrame(frameId);
          if (vizInstance) vizInstance.dispose();
        };
        
      } catch (error) {
        console.error('Failed to initialize quantum visualization:', error);
      }
    };
    
    initViz();
    
    // Cleanup function
    return () => {
      if (frameId) cancelAnimationFrame(frameId);
      if (vizInstance) vizInstance.dispose();
    };
  }, []);

  return (
    <div className="container">
      <Head>
        <title>NovaFuse Quantum Stream</title>
        <meta name="description" content="High-performance quantum visualization powered by NovaFuse" />
        <meta name="patent" content={ipProtection.options.patentNumbers.join(', ')} />
      </Head>

      <header className="header">
        <h1>NovaFuse Quantum Stream</h1>
        <div className="metrics">
          <span>FPS: {performanceMetrics.fps}</span>
          <span>Qubits: {performanceMetrics.qubits.toLocaleString()}</span>
          <span>Memory: {performanceMetrics.memory} MB</span>
        </div>
      </header>

      <main className="main">
        <div className={`visualization-container ${isReady ? 'ready' : 'loading'}`}>
          <canvas 
            ref={canvasRef} 
            className="quantum-canvas"
            width="1920"
            height="1080"
          />
          {!isReady && (
            <div className="loading-overlay">
              <div className="spinner"></div>
              <p>Initializing quantum visualization...</p>
            </div>
          )}
        </div>
        
        <div className="controls">
          <button 
            className="btn"
            onClick={() => {
              // Example interaction
              if (window.quantumViz) {
                window.quantumViz.addRandomQubits(1000);
              }
            }}
          >
            Add Qubits
          </button>
          
          <div className="patent-notice">
            Protected by: {ipProtection.options.patentNumbers.join(', ')}
          </div>
        </div>
      </main>

      <style jsx global>{`
        :root {
          --primary-color: #0066ff;
          --background: #0a0a1a;
          --text: #ffffff;
          --text-secondary: #a0a0c0;
          --border: rgba(255, 255, 255, 0.1);
        }
        
        * {
          box-sizing: border-box;
          margin: 0;
          padding: 0;
        }
        
        html, body {
          height: 100%;
          width: 100%;
          overflow: hidden;
          background-color: var(--background);
          color: var(--text);
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        }
        
        .container {
          display: flex;
          flex-direction: column;
          height: 100vh;
          width: 100vw;
        }
        
        .header {
          padding: 1rem;
          display: flex;
          justify-content: space-between;
          align-items: center;
          border-bottom: 1px solid var(--border);
          background-color: rgba(0, 0, 0, 0.3);
          backdrop-filter: blur(10px);
          z-index: 10;
        }
        
        .header h1 {
          font-size: 1.2rem;
          font-weight: 600;
          color: var(--primary-color);
        }
        
        .metrics {
          display: flex;
          gap: 1.5rem;
          font-family: 'Fira Code', monospace;
          font-size: 0.9rem;
          color: var(--text-secondary);
        }
        
        .main {
          flex: 1;
          display: flex;
          flex-direction: column;
          position: relative;
          overflow: hidden;
        }
        
        .visualization-container {
          position: relative;
          flex: 1;
          width: 100%;
          height: 100%;
          background-color: #000;
        }
        
        .quantum-canvas {
          display: block;
          width: 100%;
          height: 100%;
          outline: none;
        }
        
        .loading-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          background-color: rgba(0, 0, 0, 0.8);
          color: white;
          z-index: 20;
        }
        
        .spinner {
          width: 40px;
          height: 40px;
          border: 4px solid rgba(255, 255, 255, 0.1);
          border-radius: 50%;
          border-top-color: var(--primary-color);
          animation: spin 1s ease-in-out infinite;
          margin-bottom: 1rem;
        }
        
        @keyframes spin {
          to { transform: rotate(360deg); }
        }
        
        .controls {
          padding: 1rem;
          display: flex;
          justify-content: space-between;
          align-items: center;
          border-top: 1px solid var(--border);
          background-color: rgba(0, 0, 0, 0.3);
          backdrop-filter: blur(10px);
        }
        
        .btn {
          padding: 0.5rem 1rem;
          background-color: var(--primary-color);
          color: white;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-weight: 500;
          transition: background-color 0.2s;
        }
        
        .btn:hover {
          background-color: #0052cc;
        }
        
        .patent-notice {
          font-size: 0.8rem;
          color: var(--text-secondary);
          opacity: 0.7;
        }
        
        /* Responsive adjustments */
        @media (max-width: 768px) {
          .header {
            flex-direction: column;
            gap: 0.5rem;
            text-align: center;
          }
          
          .metrics {
            width: 100%;
            justify-content: space-around;
          }
          
          .controls {
            flex-direction: column;
            gap: 1rem;
          }
        }
      `}</style>
    </div>
  );
}

// Add server-side rendering protection
export async function getServerSideProps(context) {
  // In a real implementation, you might want to:
  // 1. Verify the request comes from an authorized domain
  // 2. Check for valid authentication
  // 3. Apply rate limiting
  
  return {
    props: {}, // Will be passed to the page component as props
  };
}
