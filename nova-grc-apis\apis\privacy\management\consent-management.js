/**
 * Consent Management System
 * 
 * This module provides functionality for managing consent collection, storage, and verification.
 */

const models = require('./models');
const { v4: uuidv4 } = require('uuid');

/**
 * Consent status values
 */
const CONSENT_STATUS = {
  ACTIVE: 'active',
  WITHDRAWN: 'withdrawn',
  EXPIRED: 'expired',
  DECLINED: 'declined'
};

/**
 * Consent types
 */
const CONSENT_TYPES = {
  MARKETING: 'marketing',
  ANALYTICS: 'analytics',
  PROFILING: 'profiling',
  THIRD_PARTY_SHARING: 'third-party-sharing',
  COOKIES: 'cookies',
  RESEARCH: 'research',
  OTHER: 'other'
};

/**
 * Consent collection methods
 */
const CONSENT_METHODS = {
  ONLINE_FORM: 'online-form',
  PAPER_FORM: 'paper-form',
  EMAIL: 'email',
  PHONE: 'phone',
  IN_PERSON: 'in-person',
  API: 'api',
  OTHER: 'other'
};

/**
 * Create a new consent record
 * @param {Object} consentData - The consent data
 * @returns {Object} - The created consent record
 */
const createConsentRecord = (consentData) => {
  const {
    dataSubjectId,
    dataSubjectName,
    dataSubjectEmail,
    consentType,
    consentDescription,
    consentGiven,
    consentExpiryDate,
    consentProof,
    consentVersion,
    consentMethod,
    privacyNoticeVersion
  } = consentData;

  // Create a new consent record with a unique ID
  const newRecord = {
    id: `con-${uuidv4().substring(0, 4)}`,
    dataSubjectId,
    dataSubjectName,
    dataSubjectEmail,
    consentType,
    consentDescription,
    consentGiven: consentGiven !== undefined ? consentGiven : true,
    consentDate: new Date().toISOString(),
    consentExpiryDate: consentExpiryDate || null,
    consentProof: consentProof || '',
    consentVersion,
    consentMethod,
    privacyNoticeVersion,
    withdrawalDate: null,
    withdrawalMethod: null,
    status: consentGiven !== undefined && consentGiven === false ? CONSENT_STATUS.DECLINED : CONSENT_STATUS.ACTIVE,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  // Add the new record to the collection
  models.consentRecords.push(newRecord);

  return newRecord;
};

/**
 * Withdraw consent
 * @param {string} consentId - The consent record ID
 * @param {string} withdrawalMethod - The method used to withdraw consent
 * @returns {Object|null} - The updated consent record or null if not found
 */
const withdrawConsent = (consentId, withdrawalMethod) => {
  const recordIndex = models.consentRecords.findIndex(r => r.id === consentId);

  if (recordIndex === -1) {
    return null;
  }

  // Update the consent record
  const updatedRecord = {
    ...models.consentRecords[recordIndex],
    withdrawalDate: new Date().toISOString(),
    withdrawalMethod,
    status: CONSENT_STATUS.WITHDRAWN,
    updatedAt: new Date().toISOString()
  };

  // Replace the old record with the updated one
  models.consentRecords[recordIndex] = updatedRecord;

  return updatedRecord;
};

/**
 * Check if consent is valid
 * @param {string} consentId - The consent record ID
 * @returns {Object} - The consent validity status
 */
const checkConsentValidity = (consentId) => {
  const record = models.consentRecords.find(r => r.id === consentId);

  if (!record) {
    return {
      valid: false,
      reason: 'Consent record not found'
    };
  }

  // Check if consent was given
  if (!record.consentGiven) {
    return {
      valid: false,
      reason: 'Consent was declined',
      record
    };
  }

  // Check if consent was withdrawn
  if (record.withdrawalDate) {
    return {
      valid: false,
      reason: 'Consent was withdrawn',
      withdrawalDate: record.withdrawalDate,
      withdrawalMethod: record.withdrawalMethod,
      record
    };
  }

  // Check if consent has expired
  if (record.consentExpiryDate && new Date(record.consentExpiryDate) < new Date()) {
    return {
      valid: false,
      reason: 'Consent has expired',
      expiryDate: record.consentExpiryDate,
      record
    };
  }

  // Consent is valid
  return {
    valid: true,
    record
  };
};

/**
 * Get consent records for a data subject
 * @param {string} dataSubjectId - The data subject ID
 * @returns {Array} - The consent records for the data subject
 */
const getConsentRecordsByDataSubject = (dataSubjectId) => {
  return models.consentRecords.filter(r => r.dataSubjectId === dataSubjectId);
};

/**
 * Get consent records by email
 * @param {string} email - The data subject email
 * @returns {Array} - The consent records for the email
 */
const getConsentRecordsByEmail = (email) => {
  return models.consentRecords.filter(r => r.dataSubjectEmail === email);
};

/**
 * Get active consent records for a data subject and consent type
 * @param {string} dataSubjectId - The data subject ID
 * @param {string} consentType - The consent type
 * @returns {Array} - The active consent records
 */
const getActiveConsentRecords = (dataSubjectId, consentType) => {
  return models.consentRecords.filter(r => 
    r.dataSubjectId === dataSubjectId && 
    r.consentType === consentType && 
    r.status === CONSENT_STATUS.ACTIVE &&
    (!r.consentExpiryDate || new Date(r.consentExpiryDate) >= new Date())
  );
};

/**
 * Generate a consent form for a specific purpose
 * @param {string} consentType - The consent type
 * @param {string} language - The language code (default: 'en')
 * @returns {Object} - The consent form
 */
const generateConsentForm = (consentType, language = 'en') => {
  // Get the latest privacy notice for the consent type
  const privacyNotices = models.privacyNotices.filter(n => 
    n.status === 'active' && n.language === language
  );
  
  const latestPrivacyNotice = privacyNotices.length > 0 
    ? privacyNotices.sort((a, b) => new Date(b.effectiveDate) - new Date(a.effectiveDate))[0]
    : null;

  // Generate consent description based on consent type
  let consentDescription = '';
  let consentTitle = '';
  let consentOptions = [];
  
  switch (consentType) {
    case CONSENT_TYPES.MARKETING:
      consentTitle = language === 'en' ? 'Marketing Communications Consent' : 'Consentement aux communications marketing';
      consentDescription = language === 'en' 
        ? 'I consent to receive marketing communications about products, services, and promotions.' 
        : 'Je consens à recevoir des communications marketing concernant les produits, services et promotions.';
      consentOptions = [
        { id: 'email', label: language === 'en' ? 'Email' : 'Courriel', default: true },
        { id: 'sms', label: language === 'en' ? 'SMS/Text Messages' : 'SMS/Messages texte', default: false },
        { id: 'phone', label: language === 'en' ? 'Phone Calls' : 'Appels téléphoniques', default: false },
        { id: 'postal', label: language === 'en' ? 'Postal Mail' : 'Courrier postal', default: false }
      ];
      break;
      
    case CONSENT_TYPES.ANALYTICS:
      consentTitle = language === 'en' ? 'Analytics Cookies Consent' : 'Consentement aux cookies d\'analyse';
      consentDescription = language === 'en'
        ? 'I consent to the use of analytics cookies to help understand how I use the website and to improve the user experience.'
        : 'Je consens à l\'utilisation de cookies d\'analyse pour aider à comprendre comment j\'utilise le site Web et pour améliorer l\'expérience utilisateur.';
      consentOptions = [
        { id: 'performance', label: language === 'en' ? 'Performance Cookies' : 'Cookies de performance', default: true },
        { id: 'functionality', label: language === 'en' ? 'Functionality Cookies' : 'Cookies de fonctionnalité', default: true },
        { id: 'targeting', label: language === 'en' ? 'Targeting Cookies' : 'Cookies de ciblage', default: false }
      ];
      break;
      
    case CONSENT_TYPES.PROFILING:
      consentTitle = language === 'en' ? 'Profiling Consent' : 'Consentement au profilage';
      consentDescription = language === 'en'
        ? 'I consent to the processing of my personal data to create a profile about me, my preferences, and interests to provide personalized content and offers.'
        : 'Je consens au traitement de mes données personnelles pour créer un profil sur moi, mes préférences et mes intérêts afin de fournir du contenu et des offres personnalisés.';
      consentOptions = [
        { id: 'interests', label: language === 'en' ? 'Interest-based Profiling' : 'Profilage basé sur les intérêts', default: false },
        { id: 'behavior', label: language === 'en' ? 'Behavioral Profiling' : 'Profilage comportemental', default: false }
      ];
      break;
      
    case CONSENT_TYPES.THIRD_PARTY_SHARING:
      consentTitle = language === 'en' ? 'Third-Party Sharing Consent' : 'Consentement au partage avec des tiers';
      consentDescription = language === 'en'
        ? 'I consent to the sharing of my personal data with third parties for the purposes described in the privacy notice.'
        : 'Je consens au partage de mes données personnelles avec des tiers aux fins décrites dans l\'avis de confidentialité.';
      consentOptions = [
        { id: 'partners', label: language === 'en' ? 'Business Partners' : 'Partenaires commerciaux', default: false },
        { id: 'advertisers', label: language === 'en' ? 'Advertisers' : 'Annonceurs', default: false },
        { id: 'analytics', label: language === 'en' ? 'Analytics Providers' : 'Fournisseurs d\'analyse', default: false }
      ];
      break;
      
    default:
      consentTitle = language === 'en' ? 'Consent Form' : 'Formulaire de consentement';
      consentDescription = language === 'en'
        ? 'I consent to the processing of my personal data as described in the privacy notice.'
        : 'Je consens au traitement de mes données personnelles comme décrit dans l\'avis de confidentialité.';
      consentOptions = [];
  }

  return {
    consentType,
    consentTitle,
    consentDescription,
    consentOptions,
    privacyNoticeVersion: latestPrivacyNotice ? latestPrivacyNotice.version : null,
    privacyNoticeUrl: latestPrivacyNotice ? latestPrivacyNotice.contentUrl : null,
    language,
    consentVersion: '1.0',
    generatedAt: new Date().toISOString()
  };
};

/**
 * Verify consent proof
 * @param {string} consentProof - The consent proof
 * @returns {Object} - The verification result
 */
const verifyConsentProof = (consentProof) => {
  // In a real implementation, this would verify the cryptographic proof
  // For now, we'll just check if the proof contains the expected information
  
  if (!consentProof) {
    return {
      verified: false,
      reason: 'No consent proof provided'
    };
  }
  
  // Check if the proof contains IP address
  const hasIpAddress = consentProof.includes('IP:');
  
  // Check if the proof contains user agent
  const hasUserAgent = consentProof.includes('User-Agent:');
  
  // Check if the proof contains timestamp
  const hasTimestamp = consentProof.includes('Timestamp:');
  
  if (hasIpAddress && hasUserAgent && hasTimestamp) {
    return {
      verified: true,
      details: {
        hasIpAddress,
        hasUserAgent,
        hasTimestamp
      }
    };
  }
  
  return {
    verified: false,
    reason: 'Incomplete consent proof',
    details: {
      hasIpAddress,
      hasUserAgent,
      hasTimestamp
    }
  };
};

/**
 * Generate consent proof
 * @param {Object} data - The data to include in the proof
 * @returns {string} - The consent proof
 */
const generateConsentProof = (data) => {
  const { ip, userAgent, timestamp = new Date().toISOString() } = data;
  
  // In a real implementation, this would generate a cryptographic proof
  // For now, we'll just create a string with the information
  
  return `IP: ${ip}, User-Agent: ${userAgent}, Timestamp: ${timestamp}`;
};

module.exports = {
  CONSENT_STATUS,
  CONSENT_TYPES,
  CONSENT_METHODS,
  createConsentRecord,
  withdrawConsent,
  checkConsentValidity,
  getConsentRecordsByDataSubject,
  getConsentRecordsByEmail,
  getActiveConsentRecords,
  generateConsentForm,
  verifyConsentProof,
  generateConsentProof
};
