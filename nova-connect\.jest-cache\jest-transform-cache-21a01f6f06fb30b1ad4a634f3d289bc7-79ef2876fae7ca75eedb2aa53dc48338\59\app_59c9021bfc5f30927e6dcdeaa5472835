4ba17516e173f13d055fecb6fe4bb301
/**
 * NovaConnect API Server
 *
 * This is the main entry point for the NovaConnect API server.
 */

const express = require('express');
const cors = require('cors');
const morgan = require('morgan');
const helmet = require('helmet');
const errorHandler = require('./middleware/errorHandler');
const databaseManager = require('./config/database');
const logger = require('./config/logger');
const initializeDatabase = require('./config/init-db');
const UserAuthenticationService = require('../auth/user-authentication-service');

// Import routes
const connectorRoutes = require('./routes/connectorRoutes');
const testingRoutes = require('./routes/testingRoutes');
const monitoringRoutes = require('./routes/monitoringRoutes');
const credentialRoutes = require('./routes/credentialRoutes');
const graphqlRoutes = require('./routes/graphqlRoutes');
const graphqlSubscriptionRoutes = require('./routes/graphqlSubscriptionRoutes');
const authRoutes = require('./routes/authRoutes');
const oauth2Routes = require('./routes/oauth2Routes');
const twoFactorRoutes = require('./routes/twoFactorRoutes');
const authTwoFactorRoutes = require('./routes/authTwoFactorRoutes');
const authAuditRoutes = require('./routes/authAuditRoutes');
const apiKeyRoutes = require('./routes/apiKeyRoutes');
const rateLimitRoutes = require('./routes/rateLimitRoutes');
const bruteForceRoutes = require('./routes/bruteForceRoutes');
const ipRestrictionRoutes = require('./routes/ipRestrictionRoutes');
const analyticsRoutes = require('./routes/analyticsRoutes');
const environmentRoutes = require('./routes/environmentRoutes');
const teamRoutes = require('./routes/teamRoutes');
const rolePermissionRoutes = require('./routes/rolePermissionRoutes');
const auditRoutes = require('./routes/auditRoutes');
const enhancedAuditRoutes = require('./routes/enhancedAuditRoutes');
const reportRoutes = require('./routes/reportRoutes');
const enhancedReportRoutes = require('./routes/enhancedReportRoutes');
const enhancedAnalyticsRoutes = require('./routes/enhancedAnalyticsRoutes');
const changeRequestRoutes = require('./routes/changeRequestRoutes');
const policyRoutes = require('./routes/policyRoutes');
const resourceLockRoutes = require('./routes/resourceLockRoutes');
const identityProviderRoutes = require('./routes/identityProviderRoutes');
const ssoAuthRoutes = require('./routes/ssoAuthRoutes');
const themeRoutes = require('./routes/themeRoutes');
const brandingRoutes = require('./routes/brandingRoutes');
const whiteLabelRoutes = require('./routes/whiteLabelRoutes');
const exportImportRoutes = require('./routes/exportImportRoutes');
const workflowRoutes = require('./routes/workflowRoutes');
const featureFlagRoutes = require('./routes/featureFlagRoutes');
const packageRoutes = require('./routes/packageRoutes');
const billingRoutes = require('./routes/billingRoutes');
const zapierRoutes = require('./routes/zapierRoutes');
const aiAssistRoutes = require('./routes/aiAssistRoutes');
const governanceRoutes = require('./routes/governanceRoutes');
const securityRoutes = require('./routes/securityRoutes');

// Import middleware
const {
  optionalAuth
} = require('./middleware/authMiddleware');
const {
  trackApiUsage,
  trackApiErrors
} = require('./middleware/analyticsMiddleware');
const {
  globalLimiter,
  authLimiter,
  apiLimiter
} = require('./middleware/rateLimitMiddleware');
const createIpRestrictionMiddleware = require('./middleware/ipRestrictionMiddleware');
const ipRestrictionMiddleware = createIpRestrictionMiddleware();
const AuditService = require('./services/AuditService');
const auditService = new AuditService();
const auditMiddleware = auditService.createAuditMiddleware();

// Import enhanced error handling middleware
const {
  errorHandler: enhancedErrorHandler,
  notFoundHandler: enhancedNotFoundHandler,
  correlationIdMiddleware,
  timeoutMiddleware
} = require('./middleware/enhancedErrorHandlingMiddleware');

// Initialize authentication service
const userAuthService = new UserAuthenticationService();

// Create Express app
const app = express();

// Middleware
app.use(helmet()); // Security headers
app.use(cors()); // Enable CORS
app.use(morgan('dev')); // Logging
app.use(express.json()); // Parse JSON bodies
app.use(express.urlencoded({
  extended: true
})); // Parse URL-encoded bodies

// Apply global rate limiter to all requests
app.use(globalLimiter);

// Apply IP restriction middleware to all requests
app.use(ipRestrictionMiddleware);

// Apply analytics middleware to track API usage
app.use(trackApiUsage);

// Apply audit middleware to log user actions
app.use(auditMiddleware);

// Apply optional authentication to all routes
app.use(optionalAuth);

// API routes
// Apply stricter rate limiting to authentication routes
app.use('/api/auth', authLimiter, authRoutes);
app.use('/api/auth/2fa', authLimiter, authTwoFactorRoutes);
app.use('/api/auth/audit', apiLimiter, authAuditRoutes);
app.use('/api/oauth2', authLimiter, oauth2Routes);

// Apply standard API rate limiting to other routes
app.use('/api/2fa', apiLimiter, twoFactorRoutes);
app.use('/api/api-keys', apiLimiter, apiKeyRoutes);
app.use('/api/analytics', apiLimiter, analyticsRoutes);
app.use('/api/enhanced-analytics', apiLimiter, enhancedAnalyticsRoutes);
app.use('/api/environments', apiLimiter, environmentRoutes);
app.use('/api/teams', apiLimiter, teamRoutes);
app.use('/api/roles', apiLimiter, rolePermissionRoutes);
app.use('/api/audit', apiLimiter, auditRoutes);
app.use('/api/enhanced-audit', apiLimiter, enhancedAuditRoutes);
app.use('/api/change-requests', apiLimiter, changeRequestRoutes);
app.use('/api/policies', apiLimiter, policyRoutes);
app.use('/api/resource-locks', apiLimiter, resourceLockRoutes);
app.use('/api/identity-providers', apiLimiter, identityProviderRoutes);
app.use('/api/sso', apiLimiter, ssoAuthRoutes);
app.use('/api/rate-limits', apiLimiter, rateLimitRoutes);
app.use('/api/brute-force', apiLimiter, bruteForceRoutes);
app.use('/api/ip-restrictions', apiLimiter, ipRestrictionRoutes);
app.use('/api/themes', apiLimiter, themeRoutes);
app.use('/api/branding', apiLimiter, brandingRoutes);
app.use('/api/white-label', apiLimiter, whiteLabelRoutes);
app.use('/api/reports', apiLimiter, reportRoutes);
app.use('/api/enhanced-reports', apiLimiter, enhancedReportRoutes);
app.use('/api/export-import', apiLimiter, exportImportRoutes);
app.use('/api/workflows', apiLimiter, workflowRoutes);
app.use('/api/subscription', apiLimiter, featureFlagRoutes);
app.use('/api/packages', apiLimiter, packageRoutes);
app.use('/api/billing', apiLimiter, billingRoutes);
app.use('/api/zapier', apiLimiter, zapierRoutes);
app.use('/api/ai-assist', apiLimiter, aiAssistRoutes);
app.use('/api/governance', apiLimiter, governanceRoutes);
app.use('/api/security', apiLimiter, securityRoutes);
app.use('/api/connectors', apiLimiter, connectorRoutes);
app.use('/api/testing', apiLimiter, testingRoutes);
app.use('/api/monitoring', apiLimiter, monitoringRoutes);
app.use('/api/credentials', apiLimiter, credentialRoutes);
app.use('/api/graphql', apiLimiter, graphqlRoutes);
app.use('/api/graphql/subscriptions', apiLimiter, graphqlSubscriptionRoutes);

// Health check endpoint
app.get('/health', (_req, res) => {
  res.status(200).json({
    status: 'ok'
  });
});

// Apply correlation ID middleware
app.use(correlationIdMiddleware);

// Apply timeout middleware (30 seconds)
app.use(timeoutMiddleware(30000));

// Apply error tracking middleware
app.use(trackApiErrors);

// Apply 404 handler for undefined routes
app.use(enhancedNotFoundHandler);

// Apply enhanced error handling
app.use(enhancedErrorHandler);

// Start server
const PORT = process.env.PORT || 3001;

// Connect to database and initialize server
async function startServer() {
  try {
    logger.info('Starting NovaConnect API server...');

    // Connect to database
    logger.info('Connecting to database...');
    await databaseManager.connect();

    // Initialize database with default data
    await initializeDatabase();

    // Initialize authentication service
    await userAuthService.initialize(databaseManager.connection);

    // Start server
    app.listen(PORT, () => {
      logger.info(`NovaConnect API server running on port ${PORT}`);
      logger.info('Available routes:');
      logger.info('- /api/auth/login');
      logger.info('- /api/auth/register');
      logger.info('- /api/connectors');
      logger.info('- /api/testing');
      logger.info('- /api/graphql/execute');
      logger.info('- /api/enhanced-audit/logs');
      logger.info('- /api/enhanced-audit/logs/export');
      logger.info('- /api/enhanced-audit/logs/stats');
      logger.info('- /api/enhanced-reports/compliance');
      logger.info('- /api/enhanced-reports/performance');
      logger.info('- /api/enhanced-reports/security');
      logger.info('- /api/enhanced-reports/custom');
      logger.info('- /api/enhanced-analytics/user');
      logger.info('- /api/enhanced-analytics/connector');
      logger.info('- /api/enhanced-analytics/compliance');
      logger.info('- /api/enhanced-analytics/predictive');
    });
  } catch (error) {
    logger.error('Failed to start server:', {
      error: error.message
    });
    process.exit(1);
  }
}

// Start the server
startServer();
module.exports = app;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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