"""
Result Manager for the Universal Compliance Testing Framework.

This module provides functionality for managing test results.
"""

import os
import json
import logging
from typing import Dict, List, Any, Optional, Union

# Import shared components
from shared.models.test_result import TestResult
from shared.events.event_bus import event_bus

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ResultManager:
    """
    Manager for test results.

    This class is responsible for storing, retrieving, and analyzing test results.
    """

    def __init__(self, results_dir: Optional[str] = None):
        """
        Initialize the Result Manager.

        Args:
            results_dir: Path to a directory for storing test results
        """
        logger.info("Initializing Result Manager")

        # Set the results directory
        self.results_dir = results_dir or os.path.join(os.getcwd(), 'test_results')

        # Create the results directory if it doesn't exist
        os.makedirs(self.results_dir, exist_ok=True)

        # Dictionary to store test results in memory
        self.results: Dict[str, Dict[str, Any]] = {}

        logger.info("Result Manager initialized")

    def store_result(self, run_id: str, result: Union[Dict[str, Any], TestResult]) -> None:
        """
        Store a test result.

        Args:
            run_id: The ID of the test run
            result: The test result (either a dictionary or a TestResult instance)
        """
        logger.info(f"Storing test result for run: {run_id}")

        # Convert to TestResult if it's a dictionary
        if isinstance(result, dict):
            # Extract required fields from the dictionary
            test_result = TestResult(
                test_id=result.get('test_id', ''),
                name=result.get('name', ''),
                framework=result.get('framework', ''),
                status='passed' if result.get('passed', False) else 'failed',
                score=result.get('score', 0.0),
                findings=result.get('findings', []),
                metadata=result.get('metadata', {}),
                result_id=run_id
            )
        else:
            test_result = result

        # Convert to dictionary for storage
        result_dict = test_result.to_dict()

        # Store the result in memory
        self.results[run_id] = result_dict

        # Store the result on disk
        self._save_result_to_disk(run_id, result_dict)

        # Emit an event that a new test result has been stored
        event_bus.emit('uctf.result_stored', {
            'run_id': run_id,
            'test_result': test_result.to_dict()
        })

    def get_result(self, run_id: str) -> Dict[str, Any]:
        """
        Get a test result as a dictionary.

        Args:
            run_id: The ID of the test run

        Returns:
            The test result as a dictionary

        Raises:
            ValueError: If the test result does not exist
        """
        logger.info(f"Getting test result for run: {run_id}")

        # Check if the result is in memory
        if run_id in self.results:
            return self.results[run_id]

        # Try to load the result from disk
        result = self._load_result_from_disk(run_id)

        if result:
            # Cache the result in memory
            self.results[run_id] = result
            return result

        raise ValueError(f"Test result not found for run: {run_id}")

    def get_test_result(self, run_id: str) -> TestResult:
        """
        Get a test result as a TestResult object.

        Args:
            run_id: The ID of the test run

        Returns:
            The test result as a TestResult object

        Raises:
            ValueError: If the test result does not exist
        """
        logger.info(f"Getting TestResult object for run: {run_id}")

        # Get the result as a dictionary
        result_dict = self.get_result(run_id)

        # Convert to TestResult
        return TestResult.from_dict(result_dict)

    def get_all_results(self) -> List[Dict[str, Any]]:
        """
        Get all test results as dictionaries.

        Returns:
            List of test results as dictionaries
        """
        logger.info("Getting all test results as dictionaries")

        # Load all results from disk
        self._load_all_results_from_disk()

        return list(self.results.values())

    def get_all_test_results(self) -> List[TestResult]:
        """
        Get all test results as TestResult objects.

        Returns:
            List of test results as TestResult objects
        """
        logger.info("Getting all test results as TestResult objects")

        # Get all results as dictionaries
        result_dicts = self.get_all_results()

        # Convert to TestResult objects
        return [TestResult.from_dict(result_dict) for result_dict in result_dicts]

    def analyze_result(self, run_id: str) -> Dict[str, Any]:
        """
        Analyze a test result.

        Args:
            run_id: The ID of the test run

        Returns:
            The analysis result

        Raises:
            ValueError: If the test result does not exist
        """
        logger.info(f"Analyzing test result for run: {run_id}")

        # Get the test result
        result = self.get_result(run_id)

        # Analyze the result
        analysis = {
            'run_id': run_id,
            'test_id': result.get('test_id'),
            'passed': result.get('passed', False),
            'score': result.get('score', 0),
            'findings_count': len(result.get('findings', [])),
            'passed_findings': len([f for f in result.get('findings', []) if f.get('status') == 'passed']),
            'warning_findings': len([f for f in result.get('findings', []) if f.get('status') == 'warning']),
            'failed_findings': len([f for f in result.get('findings', []) if f.get('status') == 'failed'])
        }

        return analysis

    def compare_results(self, run_id_1: str, run_id_2: str) -> Dict[str, Any]:
        """
        Compare two test results.

        Args:
            run_id_1: The ID of the first test run
            run_id_2: The ID of the second test run

        Returns:
            The comparison result

        Raises:
            ValueError: If either test result does not exist
        """
        logger.info(f"Comparing test results for runs: {run_id_1} and {run_id_2}")

        # Get the test results
        result_1 = self.get_result(run_id_1)
        result_2 = self.get_result(run_id_2)

        # Analyze both results
        analysis_1 = self.analyze_result(run_id_1)
        analysis_2 = self.analyze_result(run_id_2)

        # Compare the results
        comparison = {
            'run_id_1': run_id_1,
            'run_id_2': run_id_2,
            'test_id_1': result_1.get('test_id'),
            'test_id_2': result_2.get('test_id'),
            'passed_1': result_1.get('passed', False),
            'passed_2': result_2.get('passed', False),
            'score_1': result_1.get('score', 0),
            'score_2': result_2.get('score', 0),
            'score_diff': result_2.get('score', 0) - result_1.get('score', 0),
            'findings_count_1': analysis_1.get('findings_count', 0),
            'findings_count_2': analysis_2.get('findings_count', 0),
            'passed_findings_1': analysis_1.get('passed_findings', 0),
            'passed_findings_2': analysis_2.get('passed_findings', 0),
            'warning_findings_1': analysis_1.get('warning_findings', 0),
            'warning_findings_2': analysis_2.get('warning_findings', 0),
            'failed_findings_1': analysis_1.get('failed_findings', 0),
            'failed_findings_2': analysis_2.get('failed_findings', 0)
        }

        return comparison

    def _save_result_to_disk(self, run_id: str, result: Dict[str, Any]) -> None:
        """
        Save a test result to disk.

        Args:
            run_id: The ID of the test run
            result: The test result
        """
        try:
            # Create the file path
            file_path = os.path.join(self.results_dir, f"{run_id}.json")

            # Save the result to a JSON file
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2)

            logger.info(f"Saved test result to disk: {file_path}")
        except Exception as e:
            logger.error(f"Failed to save test result to disk: {e}")

    def _load_result_from_disk(self, run_id: str) -> Optional[Dict[str, Any]]:
        """
        Load a test result from disk.

        Args:
            run_id: The ID of the test run

        Returns:
            The test result, or None if not found
        """
        try:
            # Create the file path
            file_path = os.path.join(self.results_dir, f"{run_id}.json")

            # Check if the file exists
            if not os.path.exists(file_path):
                return None

            # Load the result from the JSON file
            with open(file_path, 'r', encoding='utf-8') as f:
                result = json.load(f)

            logger.info(f"Loaded test result from disk: {file_path}")

            return result
        except Exception as e:
            logger.error(f"Failed to load test result from disk: {e}")
            return None

    def _load_all_results_from_disk(self) -> None:
        """Load all test results from disk."""
        try:
            # Get all JSON files in the results directory
            result_files = [f for f in os.listdir(self.results_dir) if f.endswith('.json')]

            for result_file in result_files:
                try:
                    # Get the run ID from the file name
                    run_id = os.path.splitext(result_file)[0]

                    # Skip if the result is already in memory
                    if run_id in self.results:
                        continue

                    # Load the result from disk
                    result = self._load_result_from_disk(run_id)

                    if result:
                        # Cache the result in memory
                        self.results[run_id] = result

                except Exception as e:
                    logger.error(f"Failed to load test result from {result_file}: {e}")

            logger.info(f"Loaded {len(self.results)} test results from disk")
        except Exception as e:
            logger.error(f"Failed to load test results from disk: {e}")

