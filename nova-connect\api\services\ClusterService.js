/**
 * Cluster Service
 * 
 * This service provides cluster mode functionality for NovaConnect UAC.
 * It enables the application to utilize all available CPU cores for improved performance.
 */

const cluster = require('cluster');
const os = require('os');
const logger = require('../utils/logger');
const { v4: uuidv4 } = require('uuid');

class ClusterService {
  constructor() {
    this.numCPUs = os.cpus().length;
    this.workers = new Map();
    this.isEnabled = process.env.CLUSTER_ENABLED === 'true';
    this.maxWorkers = parseInt(process.env.MAX_WORKERS || this.numCPUs, 10);
    this.restartDelay = parseInt(process.env.WORKER_RESTART_DELAY || 1000, 10);
    this.gracefulShutdownTimeout = parseInt(process.env.GRACEFUL_SHUTDOWN_TIMEOUT || 30000, 10);
    this.workerId = cluster.isMaster ? 'master' : (cluster.worker ? cluster.worker.id : 'unknown');
    
    // Initialize cluster metrics
    this.metrics = {
      startTime: Date.now(),
      totalRequests: 0,
      activeRequests: 0,
      totalErrors: 0,
      restarts: 0,
      lastRestartTime: null
    };
  }
  
  /**
   * Initialize cluster mode
   * @param {Function} workerFunction - Function to run in worker processes
   * @returns {Promise<boolean>} - Whether cluster mode was initialized
   */
  async initialize(workerFunction) {
    if (!this.isEnabled) {
      logger.info('Cluster mode is disabled. Running in single process mode.');
      return false;
    }
    
    if (cluster.isMaster) {
      logger.info(`Initializing cluster mode with ${this.maxWorkers} workers (${this.numCPUs} CPUs available)`);
      
      // Fork workers
      for (let i = 0; i < this.maxWorkers; i++) {
        this._forkWorker();
      }
      
      // Listen for worker events
      this._setupWorkerEvents();
      
      // Set up process events
      this._setupProcessEvents();
      
      return true;
    } else {
      // Worker process
      logger.info(`Worker ${this.workerId} started`);
      
      // Set up worker process events
      this._setupWorkerProcessEvents();
      
      // Run worker function
      if (typeof workerFunction === 'function') {
        await workerFunction();
      }
      
      return true;
    }
  }
  
  /**
   * Fork a new worker
   * @private
   */
  _forkWorker() {
    // Create a unique worker ID
    const workerId = uuidv4();
    
    // Fork a new worker
    const worker = cluster.fork({ WORKER_ID: workerId });
    
    // Store worker information
    this.workers.set(worker.id, {
      id: worker.id,
      uniqueId: workerId,
      pid: worker.process.pid,
      startTime: Date.now(),
      restarts: 0,
      lastRestartTime: null,
      status: 'starting'
    });
    
    logger.info(`Worker ${worker.id} (PID: ${worker.process.pid}) forked`);
  }
  
  /**
   * Set up worker events
   * @private
   */
  _setupWorkerEvents() {
    // Listen for online event
    cluster.on('online', (worker) => {
      const workerInfo = this.workers.get(worker.id);
      
      if (workerInfo) {
        workerInfo.status = 'online';
        logger.info(`Worker ${worker.id} (PID: ${worker.process.pid}) is online`);
      }
    });
    
    // Listen for listening event
    cluster.on('listening', (worker, address) => {
      const workerInfo = this.workers.get(worker.id);
      
      if (workerInfo) {
        workerInfo.status = 'listening';
        workerInfo.address = address;
        logger.info(`Worker ${worker.id} (PID: ${worker.process.pid}) is listening on ${address.address}:${address.port}`);
      }
    });
    
    // Listen for disconnect event
    cluster.on('disconnect', (worker) => {
      const workerInfo = this.workers.get(worker.id);
      
      if (workerInfo) {
        workerInfo.status = 'disconnected';
        logger.warn(`Worker ${worker.id} (PID: ${worker.process.pid}) disconnected`);
      }
    });
    
    // Listen for exit event
    cluster.on('exit', (worker, code, signal) => {
      const workerInfo = this.workers.get(worker.id);
      
      if (workerInfo) {
        workerInfo.status = 'exited';
        workerInfo.exitCode = code;
        workerInfo.exitSignal = signal;
        
        logger.warn(`Worker ${worker.id} (PID: ${worker.process.pid}) died with code: ${code} and signal: ${signal}`);
        
        // Update metrics
        this.metrics.restarts++;
        this.metrics.lastRestartTime = Date.now();
        
        // Remove worker from map
        this.workers.delete(worker.id);
        
        // Fork a new worker after a delay
        setTimeout(() => {
          logger.info(`Restarting worker after ${this.restartDelay}ms delay`);
          this._forkWorker();
        }, this.restartDelay);
      }
    });
    
    // Listen for message event
    cluster.on('message', (worker, message) => {
      if (message.type === 'metrics') {
        // Update metrics from worker
        this.metrics.totalRequests += message.data.requests || 0;
        this.metrics.activeRequests += message.data.activeRequests || 0;
        this.metrics.totalErrors += message.data.errors || 0;
      }
    });
  }
  
  /**
   * Set up process events
   * @private
   */
  _setupProcessEvents() {
    // Handle SIGTERM signal
    process.on('SIGTERM', () => {
      logger.info('SIGTERM received, shutting down gracefully');
      this._gracefulShutdown();
    });
    
    // Handle SIGINT signal
    process.on('SIGINT', () => {
      logger.info('SIGINT received, shutting down gracefully');
      this._gracefulShutdown();
    });
    
    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      logger.error('Uncaught exception in master process', { error });
      this._gracefulShutdown();
    });
    
    // Handle unhandled rejections
    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled rejection in master process', { reason });
      this._gracefulShutdown();
    });
  }
  
  /**
   * Set up worker process events
   * @private
   */
  _setupWorkerProcessEvents() {
    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      logger.error(`Uncaught exception in worker ${this.workerId}`, { error });
      
      // Send error to master
      if (cluster.worker) {
        cluster.worker.send({
          type: 'error',
          data: {
            message: error.message,
            stack: error.stack
          }
        });
      }
      
      // Exit after a short delay to allow logging
      setTimeout(() => {
        process.exit(1);
      }, 1000);
    });
    
    // Handle unhandled rejections
    process.on('unhandledRejection', (reason, promise) => {
      logger.error(`Unhandled rejection in worker ${this.workerId}`, { reason });
      
      // Send error to master
      if (cluster.worker) {
        cluster.worker.send({
          type: 'error',
          data: {
            message: reason.message || 'Unknown error',
            stack: reason.stack || 'No stack trace'
          }
        });
      }
    });
    
    // Handle SIGTERM signal
    process.on('SIGTERM', () => {
      logger.info(`SIGTERM received in worker ${this.workerId}, shutting down gracefully`);
      
      // Close server and exit
      this._shutdownWorker();
    });
  }
  
  /**
   * Gracefully shut down the cluster
   * @private
   */
  _gracefulShutdown() {
    logger.info('Starting graceful shutdown of cluster');
    
    // Set shutdown flag
    this.isShuttingDown = true;
    
    // Disconnect all workers
    for (const [id, worker] of Object.entries(cluster.workers)) {
      logger.info(`Disconnecting worker ${id}`);
      worker.disconnect();
    }
    
    // Set a timeout to force exit if workers don't exit gracefully
    const forceExitTimeout = setTimeout(() => {
      logger.warn(`Forcing exit after ${this.gracefulShutdownTimeout}ms timeout`);
      process.exit(1);
    }, this.gracefulShutdownTimeout);
    
    // Check if all workers have exited
    const checkWorkersInterval = setInterval(() => {
      const workerCount = Object.keys(cluster.workers).length;
      
      if (workerCount === 0) {
        logger.info('All workers have exited, shutting down master');
        clearInterval(checkWorkersInterval);
        clearTimeout(forceExitTimeout);
        process.exit(0);
      } else {
        logger.info(`Waiting for ${workerCount} workers to exit`);
      }
    }, 1000);
  }
  
  /**
   * Shut down a worker process
   * @private
   */
  _shutdownWorker() {
    // Set a timeout to force exit if shutdown takes too long
    const forceExitTimeout = setTimeout(() => {
      logger.warn(`Forcing worker ${this.workerId} exit after ${this.gracefulShutdownTimeout}ms timeout`);
      process.exit(1);
    }, this.gracefulShutdownTimeout);
    
    // Exit gracefully
    process.exit(0);
  }
  
  /**
   * Get cluster information
   * @returns {Object} - Cluster information
   */
  getInfo() {
    if (cluster.isMaster) {
      // Master process
      return {
        mode: this.isEnabled ? 'cluster' : 'single',
        role: 'master',
        workers: Array.from(this.workers.values()),
        metrics: this.metrics,
        uptime: Date.now() - this.metrics.startTime,
        cpus: this.numCPUs,
        maxWorkers: this.maxWorkers
      };
    } else {
      // Worker process
      return {
        mode: this.isEnabled ? 'cluster' : 'single',
        role: 'worker',
        id: this.workerId,
        pid: process.pid,
        uptime: process.uptime() * 1000
      };
    }
  }
  
  /**
   * Record a request in metrics
   */
  recordRequest() {
    if (cluster.isWorker) {
      // Increment active requests
      this.metrics.activeRequests++;
      
      // Send metrics to master periodically
      this._sendMetricsToMaster();
    }
  }
  
  /**
   * Record a completed request in metrics
   * @param {boolean} isError - Whether the request resulted in an error
   */
  recordRequestComplete(isError = false) {
    if (cluster.isWorker) {
      // Increment total requests
      this.metrics.totalRequests++;
      
      // Decrement active requests
      this.metrics.activeRequests = Math.max(0, this.metrics.activeRequests - 1);
      
      // Increment errors if applicable
      if (isError) {
        this.metrics.totalErrors++;
      }
      
      // Send metrics to master periodically
      this._sendMetricsToMaster();
    }
  }
  
  /**
   * Send metrics to master process
   * @private
   */
  _sendMetricsToMaster() {
    if (cluster.isWorker && cluster.worker) {
      // Only send metrics every 5 seconds to reduce IPC overhead
      const now = Date.now();
      if (!this._lastMetricsSent || now - this._lastMetricsSent > 5000) {
        cluster.worker.send({
          type: 'metrics',
          data: {
            requests: this.metrics.totalRequests,
            activeRequests: this.metrics.activeRequests,
            errors: this.metrics.totalErrors
          }
        });
        
        this._lastMetricsSent = now;
      }
    }
  }
}

module.exports = new ClusterService();
