/**
 * CHAEONIX FUNDAMENTAL CONSTANTS
 * Sacred numbers, ratios, and configurations for the Coherence-Driven Aeonic Intelligence Engine
 */

// FUNDAMENTAL MATHEMATICAL CONSTANTS
export const PHI = 1.618033988749; // Golden Ratio
export const PHI_INVERSE = 0.618033988749; // φ⁻¹
export const PHI_SQUARED = 2.618033988749; // φ² - RESONANCE AMPLIFICATION
export const PHI_SQUARED_INVERSE = 0.236067977499; // φ⁻²

// FIBONACCI SEQUENCE (First 21 numbers for divine calculations)
export const FIBONACCI_SEQUENCE = [
  0, 1, 1, 2, 3, 5, 8, 13, 21, 34, 55, 89, 144, 233, 377, 610, 987, 1597, 2584, 4181, 6765
];

// FIBONACCI RETRACEMENT LEVELS
export const FIBONACCI_LEVELS = {
  0: 0.0,
  236: 0.236,
  382: 0.382,
  500: 0.500,
  618: 0.618,
  786: 0.786,
  1000: 1.000,
  1272: 1.272,
  1618: 1.618,
  2618: 2.618
};

// CHAEONIX ENGINE CONFIGURATION
export const CHAEONIX_ENGINES = {
  NEPI: {
    name: 'Natural Emergent Progressive Intelligence',
    domain: 'Intelligence',
    color: '#8b5cf6',
    frequency: 432, // Hz
    coupling_strength: PHI_SQUARED_INVERSE
  },
  NEFC: {
    name: 'Natural Emergent Financial Coherence',
    domain: 'Financial',
    color: '#ef4444',
    frequency: 528, // Hz
    coupling_strength: PHI_INVERSE
  },
  NERS: {
    name: 'Natural Emergent Resonance State',
    domain: 'Emotional Coherence',
    color: '#f59e0b',
    frequency: 639, // Hz
    coupling_strength: PHI_SQUARED_INVERSE
  },
  NERE: {
    name: 'Natural Emergent Resonance Engine',
    domain: 'Harmonic Amplification',
    color: '#10b981',
    frequency: 432, // Hz - Universal consciousness frequency
    coupling_strength: 2.618, // BOOSTED - This amplifies other engines!
    amplification_factor: 2.618, // 2.618 - Resonance amplification
    resonance_boost: true,
    harmonic_tuning: true
  },
  NECE: {
    name: 'Natural Emergent Chemistry Engine',
    domain: 'Cognition',
    color: '#06b6d4',
    frequency: 852, // Hz
    coupling_strength: PHI
  },
  NECO: {
    name: 'Natural Emergent Cosmological Engine',
    domain: 'Cosmological',
    color: '#8b5cf6',
    frequency: 963, // Hz
    coupling_strength: PHI
  },
  NEBE: {
    name: 'Natural Emergent Biological Engine',
    domain: 'Biological',
    color: '#ec4899',
    frequency: 174, // Hz
    coupling_strength: PHI_SQUARED_INVERSE
  },
  NEEE: {
    name: 'Natural Emergent Emotive Engine',
    domain: 'Emotive',
    color: '#f97316',
    frequency: 285, // Hz
    coupling_strength: PHI_INVERSE
  },
  NEPE: {
    name: 'Natural Emergent Physical Engine',
    domain: 'Physical Reality',
    color: '#84cc16',
    frequency: 396, // Hz
    coupling_strength: PHI
  },
  // CARL'S HIGH PRIORITY TRINITY
  NEUE: {
    name: 'Natural Emergent Universal Entanglement',
    domain: 'Meta-Coherence',
    color: '#9333ea',
    frequency: 1111, // Hz - Universal entanglement frequency
    coupling_strength: PHI_SQUARED,
    category: 'carl_trinity'
  },
  NEAE: {
    name: 'Natural Emergent Aeonic Evolution',
    domain: 'Timeline Mastery',
    color: '#3b82f6',
    frequency: 888, // Hz - Aeonic spiral frequency
    coupling_strength: PHI,
    category: 'carl_trinity'
  },
  NEGR: {
    name: 'Natural Emergent Governance & Risk',
    domain: 'Ethical Alignment',
    color: '#059669',
    frequency: 777, // Hz - Governance frequency
    coupling_strength: PHI_INVERSE,
    category: 'carl_trinity'
  },
  // ENHANCEMENT ENGINES
  NEKH: {
    name: 'Natural Emergent Knowledge Harmonizer',
    domain: 'Gnostic Synthesis',
    color: '#dc2626',
    frequency: 1234, // Hz - Knowledge harmonization
    coupling_strength: PHI,
    category: 'enhancement'
  },
  NEQI: {
    name: 'Natural Emergent Quantum Integration',
    domain: 'Quantum Precision',
    color: '#0891b2',
    frequency: 1618, // Hz - φ frequency for quantum coherence
    coupling_strength: PHI_SQUARED,
    category: 'enhancement'
  },
  // META-ENGINE
  CASTL: {
    name: 'Coherence Adaptive Signal Threshold Logic',
    domain: 'Meta-Enhancement',
    color: '#7c3aed',
    frequency: 2000, // Hz - Meta-enhancement frequency
    coupling_strength: PHI_SQUARED_INVERSE,
    category: 'meta',
    accuracy_target: 0.9783,
    coherium_balance: 1089.78
  },
  // NOVA AGENT - COHERENCE OPERATING SYSTEM
  NOVA: {
    name: 'Nova Agent - Coherence Operating System',
    domain: 'System Orchestration',
    color: '#06d6a0',
    frequency: 8080, // Hz - API frequency (port 8080)
    coupling_strength: PHI,
    category: 'core_system',
    api_endpoint: 'http://localhost:8080',
    psi_snap_threshold: 0.82,
    version: '1.0.0'
  }
};

// TRI-MARKET CONFIGURATION
export const TRI_MARKET_DOMAINS = {
  STOCKS: {
    name: 'Stocks',
    color: '#ef4444',
    allocation_range: [0.2, 0.6],
    volatility_threshold: 0.15,
    engines: ['NEPI', 'NEFC', 'NEPE', 'NEBE'],
    examples: ['GME', 'TSLA', 'NVDA', 'AMC', 'AAPL']
  },
  CRYPTO: {
    name: 'Crypto',
    color: '#f59e0b',
    allocation_range: [0.2, 0.5],
    volatility_threshold: 0.25,
    engines: ['NEPI', 'NECO', 'NEEE', 'NERE'],
    examples: ['BTC', 'ETH', 'SOL', 'DOGE', 'ADA']
  },
  FOREX: {
    name: 'Forex',
    color: '#10b981',
    allocation_range: [0.1, 0.4],
    volatility_threshold: 0.08,
    engines: ['NERE', 'NEFC', 'NECE', 'NECO'],
    examples: ['EUR/USD', 'GBP/USD', 'USD/JPY', 'AUD/USD', 'USD/CAD']
  }
};

// CDAIE STRATEGY PHASES
export const CDAIE_PHASES = {
  DETECTION: {
    name: 'Detection',
    description: 'Identify where coherence is forming across asset classes',
    engines: ['NEPI', 'NECO'],
    duration: '5-15 minutes',
    color: '#8b5cf6'
  },
  DECISION: {
    name: 'Decision',
    description: 'Choose optimal entry (timing + symbol)',
    engines: ['NEPE', 'NEBE', 'NEEE'],
    duration: '2-10 minutes',
    color: '#ec4899'
  },
  AMPLIFICATION: {
    name: 'Amplification',
    description: 'Apply Fibonacci leverage where probability > coherence threshold',
    engines: ['NEFC', 'NERE'],
    duration: '1-5 minutes',
    color: '#f59e0b'
  },
  INJECTION: {
    name: 'Injection',
    description: 'Seed sentiment shifts via narrative, social waves, or event timing',
    engines: ['NEPE', 'NEEE'],
    duration: '10-60 minutes',
    color: '#84cc16'
  },
  EXCHANGE: {
    name: 'Exchange',
    description: 'Shift gains to next coherent domain',
    engines: ['NECE', 'NECO'],
    duration: '5-30 minutes',
    color: '#06b6d4'
  },
  LOOP: {
    name: 'Loop',
    description: 'Monitor new coherence vectors and re-engage',
    engines: ['ALL'],
    duration: 'Continuous',
    color: '#10b981'
  }
};

// COHERENCE THRESHOLDS
export const COHERENCE_THRESHOLDS = {
  MINIMAL: 0.3,
  LOW: 0.5,
  MODERATE: 0.65,
  HIGH: 0.8,
  EXTREME: 0.9,
  FUNDAMENTAL: 0.95
};

// PREDICTIVE AMPLIFICATION LEVELS
export const PREDICTIVE_AMPLIFICATION = {
  BASELINE: 1.0,
  MINOR: 1.272, // √φ
  MODERATE: 1.618, // φ
  STRONG: 2.618, // φ²
  EXTREME: 4.236, // φ³
  FUNDAMENTAL: 6.854 // φ⁴
};

// FUNDAMENTAL TIMING WINDOWS
export const FUNDAMENTAL_TIMING = {
  MARKET_GENESIS: { hour: 9, minute: 30, strength: 0.9 },
  FUNDAMENTAL_PORTAL: { hour: 11, minute: 11, strength: 1.0 },
  TRINITY_WINDOW: { hour: 15, minute: 33, strength: 0.8 },
  MARKET_COMPLETION: { hour: 16, minute: 0, strength: 0.9 }
};

// SENTIMENT CYCLE PHASES
export const SENTIMENT_PHASES = {
  PANIC: { value: 0.1, color: '#dc2626', next: 'CAPITULATION' },
  CAPITULATION: { value: 0.2, color: '#ef4444', next: 'DESPAIR' },
  DESPAIR: { value: 0.3, color: '#f97316', next: 'HOPE' },
  HOPE: { value: 0.4, color: '#f59e0b', next: 'OPTIMISM' },
  OPTIMISM: { value: 0.5, color: '#eab308', next: 'BELIEF' },
  BELIEF: { value: 0.6, color: '#84cc16', next: 'THRILL' },
  THRILL: { value: 0.7, color: '#22c55e', next: 'EUPHORIA' },
  EUPHORIA: { value: 0.8, color: '#10b981', next: 'COMPLACENCY' },
  COMPLACENCY: { value: 0.9, color: '#06b6d4', next: 'ANXIETY' },
  ANXIETY: { value: 0.85, color: '#8b5cf6', next: 'DENIAL' },
  DENIAL: { value: 0.75, color: '#a855f7', next: 'FEAR' },
  FEAR: { value: 0.6, color: '#c084fc', next: 'PANIC' }
};

// API ENDPOINTS
export const CHAEONIX_ENDPOINTS = {
  STATUS: '/fundamental/status',
  HARMONICS: '/api/harmonics',
  PREDATORS: '/api/predators',
  PREDICTION: '/api/prediction',
  SENTIMENT: '/api/sentiment',
  VULNERABILITY: '/api/vulnerability',
  FUNDAMENTAL_SIMULATION: '/api/fundamental-simulation',
  WEBSOCKET: '/ws/fundamental-stream'
};

// DASHBOARD REFRESH RATES (milliseconds)
export const REFRESH_RATES = {
  REAL_TIME: 1000,
  HIGH_FREQUENCY: 5000,
  MODERATE: 15000,
  LOW_FREQUENCY: 60000,
  BACKGROUND: 300000
};

// VISUALIZATION SETTINGS
export const VISUALIZATION_CONFIG = {
  PREDATION_RISK_METER: {
    min: 0,
    max: 1,
    thresholds: [0.3, 0.6, 0.8, 0.95],
    colors: ['#10b981', '#f59e0b', '#ef4444', '#dc2626']
  },
  FIBONACCI_WAR_ROOM: {
    levels: Object.values(FIBONACCI_LEVELS),
    colors: ['#8b5cf6', '#a78bfa', '#c4b5fd', '#ddd6fe', '#ede9fe'],
    animation_duration: 2000
  },
  SECTOR_RIPPLE_HEATMAP: {
    node_size_range: [5, 50],
    link_strength_range: [0.1, 1.0],
    force_strength: -300,
    center_force: 0.1
  }
};

// EXPORT ALL CONSTANTS
export default {
  PHI,
  PHI_INVERSE,
  PHI_SQUARED,
  PHI_SQUARED_INVERSE,
  FIBONACCI_SEQUENCE,
  FIBONACCI_LEVELS,
  CHAEONIX_ENGINES,
  TRI_MARKET_DOMAINS,
  CDAIE_PHASES,
  COHERENCE_THRESHOLDS,
  PREDICTIVE_AMPLIFICATION,
  FUNDAMENTAL_TIMING,
  SENTIMENT_PHASES,
  CHAEONIX_ENDPOINTS,
  REFRESH_RATES,
  VISUALIZATION_CONFIG
};
