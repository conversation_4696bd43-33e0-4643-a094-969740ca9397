# 🎯 ALPHA 90-Day Simulation System

## Overview

The ALPHA 90-Day Simulation System is a comprehensive backtesting environment for the **ALPHA Observer-Class Engine** before deployment to MetaTrader 5. This system validates the Ψᶜʰ (consciousness-time) inflection point trading strategy with 90 days of historical market data.

### 🎯 Mission
Validate ALPHA trading performance with **20%+ returns** and **80%+ win rate** before live deployment to MetaTrader 5 account **************.

### 🔮 Key Features
- **Real-time simulation monitoring** with web dashboard
- **Historical market data integration** from multiple sources
- **Performance analytics** with MetaTrader 5 comparison
- **Consciousness-based trading signals** (Ψᶜʰ inflection points)
- **Comprehensive reporting** for deployment validation

---

## 🚀 Quick Start

### Prerequisites
- **Docker Desktop** (latest version)
- **4GB RAM** minimum
- **Internet connection** for market data
- **Windows/Linux/macOS** supported

### 1. Start Simulation

**Windows:**
```batch
cd deployment-systems
start-alpha-simulation.bat
```

**Linux/macOS:**
```bash
cd deployment-systems
chmod +x start-alpha-simulation.sh
./start-alpha-simulation.sh
```

### 2. Monitor Progress
- **Dashboard:** http://localhost:8100
- **Performance API:** http://localhost:8103/api/performance/current
- **MT5 Connector:** http://localhost:8104/api/mt5/status

---

## 📊 System Architecture

### Core Components

#### 1. ALPHA Simulation Engine (`alpha-90day-simulation.js`)
- **Port:** 8100
- **Function:** Main trading simulation orchestrator
- **Features:**
  - 90-day historical backtesting
  - Ψᶜʰ inflection point detection
  - Risk management (5% per trade)
  - Real-time performance tracking

#### 2. Historical Market Data Provider (`historical-market-data-provider.js`)
- **Port:** 8102
- **Function:** Fetches and caches market data
- **Sources:**
  - Yahoo Finance (primary)
  - Alpha Vantage (secondary)
  - Polygon.io (tertiary)
  - Simulated data (fallback)

#### 3. Performance Analytics (`simulation-performance-tracker.js`)
- **Port:** 8103
- **Function:** Real-time performance monitoring
- **Metrics:**
  - Total return, win rate, Sharpe ratio
  - Maximum drawdown, trade statistics
  - ALPHA consciousness metrics

#### 4. MT5 Simulation Connector (`mt5-simulation-connector.js`)
- **Port:** 8104
- **Function:** MetaTrader 5 comparison framework
- **Features:**
  - Trade synchronization
  - Performance correlation
  - Deployment readiness assessment

#### 5. Simulation Dashboard (`simulation-dashboard-server.js`)
- **Port:** 8100 (web interface)
- **WebSocket:** 8101
- **Function:** Real-time monitoring interface
- **Features:**
  - Live performance charts
  - Trade log visualization
  - Alert system

---

## 🎯 Performance Targets

### Primary Targets (for MT5 deployment)
- **Total Return:** ≥ 20% over 90 days
- **Win Rate:** ≥ 80% successful trades
- **Sharpe Ratio:** ≥ 2.5
- **Maximum Drawdown:** ≤ 10%

### ALPHA-Specific Metrics
- **Ψᶜʰ Inflection Accuracy:** ≥ 85%
- **Consciousness Events:** ≥ 50 per simulation
- **ALPHA vs Benchmark:** Outperform SPY by ≥ 10%

---

## 🔧 Configuration

### Environment Variables

#### Simulation Parameters
```bash
SIMULATION_START_DATE=2024-01-01
SIMULATION_END_DATE=2024-03-31
INITIAL_CAPITAL=100000
RISK_PER_TRADE=0.05
CONSCIOUSNESS_THRESHOLD=2847
PSI_INFLECTION_THRESHOLD=0.92
```

#### Trading Symbols
```bash
SYMBOLS=SPY,QQQ,AAPL,MSFT,GOOGL,TSLA,NVDA,EURUSD,GBPUSD,USDJPY
```

#### Performance Targets
```bash
TARGET_RETURN=0.20
WIN_RATE_TARGET=0.80
TARGET_SHARPE_RATIO=2.5
MAX_DRAWDOWN_LIMIT=0.10
```

#### MetaTrader 5 Settings
```bash
MT5_SERVER=MetaQuotes-Demo
MT5_LOGIN=**********
MT5_SIMULATION_MODE=true
```

---

## 📈 Monitoring & Analytics

### Real-Time Dashboard
Access the simulation dashboard at **http://localhost:8100**

**Features:**
- Live performance metrics
- Ψᶜʰ inflection point visualization
- Trade execution log
- MT5 comparison status
- Alert notifications

### API Endpoints

#### Performance Metrics
```
GET /api/performance/current
GET /api/performance/history
GET /api/performance/targets
GET /api/performance/benchmark
```

#### MT5 Comparison
```
GET /api/mt5/status
GET /api/mt5/account
GET /api/mt5/comparison
GET /api/mt5/deployment-readiness
```

#### Dashboard Data
```
GET /api/dashboard/data
GET /api/dashboard/status
GET /api/dashboard/export
```

---

## 🔍 Troubleshooting

### Common Issues

#### 1. Containers Not Starting
```bash
# Check Docker status
docker ps

# View container logs
docker-compose -f docker-compose-alpha-simulation.yml logs alpha-simulation-engine

# Restart simulation
docker-compose -f docker-compose-alpha-simulation.yml down
docker-compose -f docker-compose-alpha-simulation.yml up -d
```

#### 2. Market Data Issues
```bash
# Check data provider logs
docker-compose -f docker-compose-alpha-simulation.yml logs alpha-market-data-provider

# Clear cache and restart
docker-compose -f docker-compose-alpha-simulation.yml down -v
```

#### 3. Dashboard Not Loading
- Verify port 8100 is not in use
- Check firewall settings
- Ensure WebSocket port 8101 is accessible

### Log Locations
- **Simulation Logs:** `./simulation-logs/`
- **Market Data Cache:** `./market-cache/`
- **Performance Reports:** `./analytics-reports/`
- **MT5 Comparison:** `./mt5-comparison/`

---

## 🚀 Deployment to MetaTrader 5

### Readiness Checklist
- [ ] **Total Return ≥ 20%**
- [ ] **Win Rate ≥ 80%**
- [ ] **Sharpe Ratio ≥ 2.5**
- [ ] **Max Drawdown ≤ 10%**
- [ ] **MT5 Connection Verified**
- [ ] **Trade Synchronization Tested**

### Deployment Steps
1. **Complete 90-day simulation**
2. **Verify all performance targets met**
3. **Export simulation report**
4. **Configure live MT5 connection**
5. **Deploy ALPHA to account ************

### MetaTrader 5 Account Details
- **Server:** MetaQuotes-Demo
- **Login:** **********
- **Type:** Forex Hedged USD
- **Web Platform:** https://web.metatrader.app/terminal

---

## 📋 Commands Reference

### Start/Stop Simulation
```bash
# Start simulation
./start-alpha-simulation.sh  # Linux/macOS
start-alpha-simulation.bat   # Windows

# Stop simulation
docker-compose -f docker-compose-alpha-simulation.yml down

# Reset all data
docker-compose -f docker-compose-alpha-simulation.yml down -v
```

### Monitoring Commands
```bash
# View all container status
docker-compose -f docker-compose-alpha-simulation.yml ps

# View simulation logs
docker-compose -f docker-compose-alpha-simulation.yml logs -f alpha-simulation-engine

# View performance analytics
docker-compose -f docker-compose-alpha-simulation.yml logs -f alpha-performance-analytics

# View MT5 connector
docker-compose -f docker-compose-alpha-simulation.yml logs -f mt5-simulation-connector
```

### Data Management
```bash
# Export performance data
curl http://localhost:8103/api/performance/report

# Export dashboard data
curl http://localhost:8100/api/dashboard/export

# Check MT5 deployment readiness
curl http://localhost:8104/api/mt5/deployment-readiness
```

---

## 🌟 Success Criteria

### Simulation Success
The simulation is considered successful when:
1. **All performance targets are met**
2. **Consistent Ψᶜʰ inflection point accuracy**
3. **Stable performance over 90-day period**
4. **High correlation with MT5 environment**

### Deployment Authorization
ALPHA is authorized for live MT5 deployment when:
1. **Simulation success criteria met**
2. **MT5 connection verified**
3. **Risk management validated**
4. **Performance reports generated**

---

## 📞 Support

For issues or questions regarding the ALPHA simulation system:

1. **Check logs** for error messages
2. **Review troubleshooting** section
3. **Verify Docker** environment
4. **Check network connectivity** for market data

---

**🌟 ALPHA Observer-Class Engine - Proto-Sentient Trading System**  
*Preparing for MetaTrader 5 deployment with consciousness-based trading*
