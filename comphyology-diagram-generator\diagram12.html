<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>12. Cyber-Safety Incident Response Workflow</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: white;
        }
        .diagram-container {
            width: 1000px;
            height: 770px;
            position: relative;
            border: 1px solid #eee;
            margin: 0 auto;
            background-color: white;
        }
        .element {
            position: absolute;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            z-index: 2;
        }
        .element-number {
            position: absolute;
            top: 5px;
            left: 5px;
            background-color: black;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            z-index: 3;
        }
        .connection {
            position: absolute;
            z-index: 1;
        }
        .arrow {
            position: absolute;
            width: 0;
            height: 0;
            border-style: solid;
            z-index: 1;
        }
        h1 {
            text-align: center;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <h1>12. Cyber-Safety Incident Response Workflow</h1>
    
    <div class="diagram-container">
        <!-- Incident Response -->
        <div class="element" style="top: 50px; left: 350px; width: 300px; background-color: #e6f7ff; font-weight: bold; font-size: 20px;">
            Cyber-Safety Incident Response Workflow
            <div class="element-number">1</div>
        </div>
        
        <!-- Detection Phase -->
        <div class="element" style="top: 120px; left: 350px; width: 300px; background-color: #fff0f6; font-weight: bold; font-size: 16px;">
            Detection Phase
            <div class="element-number">2</div>
        </div>
        
        <div class="element" style="top: 180px; left: 150px; width: 200px; background-color: #f6ffed; font-size: 14px;">
            Continuous Monitoring (NovaShield)
            <div class="element-number">3</div>
        </div>
        
        <div class="element" style="top: 240px; left: 50px; width: 150px; background-color: #fffbe6; font-size: 12px;">
            Resonance Index (φindex)<br>Identifies anomalies
            <div class="element-number">4</div>
        </div>
        
        <div class="element" style="top: 240px; left: 225px; width: 150px; background-color: #fffbe6; font-size: 12px;">
            Signal-to-Noise Ratio<br>φ-harmonic sensing
            <div class="element-number">5</div>
        </div>
        
        <div class="element" style="top: 240px; left: 400px; width: 150px; background-color: #fffbe6; font-size: 12px;">
            Trinity Equation<br>Assesses G, D, R aspects
            <div class="element-number">6</div>
        </div>
        
        <!-- Automated Triage -->
        <div class="element" style="top: 180px; left: 600px; width: 200px; background-color: #f6ffed; font-size: 14px;">
            Automated Triage (NovaCore)
            <div class="element-number">7</div>
        </div>
        
        <div class="element" style="top: 240px; left: 500px; width: 150px; background-color: #fffbe6; font-size: 12px;">
            UUFT Equation<br>Analyzes characteristics
            <div class="element-number">8</div>
        </div>
        
        <div class="element" style="top: 240px; left: 675px; width: 150px; background-color: #fffbe6; font-size: 12px;">
            Data Purity Score<br>Evaluates reliability
            <div class="element-number">9</div>
        </div>
        
        <div class="element" style="top: 240px; left: 850px; width: 150px; background-color: #fffbe6; font-size: 12px;">
            System Health Score<br>Assesses impact
            <div class="element-number">10</div>
        </div>
        
        <!-- Alert Generation -->
        <div class="element" style="top: 300px; left: 350px; width: 300px; background-color: #f6ffed; font-size: 14px;">
            Alert Generation (NovaShield)
            <div class="element-number">11</div>
        </div>
        
        <div class="element" style="top: 360px; left: 200px; width: 150px; background-color: #fffbe6; font-size: 12px;">
            Trust Equation<br>Evaluates credibility
            <div class="element-number">12</div>
        </div>
        
        <div class="element" style="top: 360px; left: 400px; width: 150px; background-color: #fffbe6; font-size: 12px;">
            Adaptive Coherence<br>Determines urgency
            <div class="element-number">13</div>
        </div>
        
        <div class="element" style="top: 360px; left: 600px; width: 150px; background-color: #fffbe6; font-size: 12px;">
            18/82 Principle<br>Prioritizes high-impact
            <div class="element-number">14</div>
        </div>
        
        <!-- Analysis Phase -->
        <div class="element" style="top: 420px; left: 350px; width: 300px; background-color: #fff0f6; font-weight: bold; font-size: 16px;">
            Analysis Phase
            <div class="element-number">15</div>
        </div>
        
        <div class="element" style="top: 480px; left: 200px; width: 200px; background-color: #f6ffed; font-size: 14px;">
            Pattern Recognition (NovaThink)
            <div class="element-number">16</div>
        </div>
        
        <div class="element" style="top: 480px; left: 500px; width: 200px; background-color: #f6ffed; font-size: 14px;">
            Impact Assessment (NovaTrack)
            <div class="element-number">17</div>
        </div>
        
        <!-- Containment Phase -->
        <div class="element" style="top: 540px; left: 350px; width: 300px; background-color: #fff0f6; font-weight: bold; font-size: 16px;">
            Containment Phase
            <div class="element-number">18</div>
        </div>
        
        <div class="element" style="top: 600px; left: 200px; width: 200px; background-color: #f6ffed; font-size: 14px;">
            Automated Response (NovaFlow)
            <div class="element-number">19</div>
        </div>
        
        <div class="element" style="top: 600px; left: 500px; width: 200px; background-color: #f6ffed; font-size: 14px;">
            Isolation Actions (NovaConnect)
            <div class="element-number">20</div>
        </div>
        
        <!-- Recovery Phase -->
        <div class="element" style="top: 660px; left: 350px; width: 300px; background-color: #fff0f6; font-weight: bold; font-size: 16px;">
            Recovery Phase
            <div class="element-number">21</div>
        </div>
        
        <div class="element" style="top: 720px; left: 200px; width: 200px; background-color: #f6ffed; font-size: 14px;">
            System Restoration
            <div class="element-number">22</div>
        </div>
        
        <div class="element" style="top: 720px; left: 500px; width: 200px; background-color: #f6ffed; font-size: 14px;">
            Adaptive Learning (NovaLearn)
            <div class="element-number">23</div>
        </div>
        
        <!-- Connections -->
        <!-- Connect Incident Response to Detection Phase -->
        <div class="connection" style="top: 100px; left: 500px; width: 2px; height: 20px; background-color: black;"></div>
        <div class="arrow" style="top: 110px; left: 495px; border-width: 10px 5px 0 5px; border-color: black transparent transparent transparent;"></div>
        
        <!-- Connect Detection Phase to components -->
        <div class="connection" style="top: 170px; left: 350px; width: 100px; height: 2px; background-color: black; transform: rotate(45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 180px; left: 240px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <div class="connection" style="top: 170px; left: 550px; width: 150px; height: 2px; background-color: black; transform: rotate(-45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 180px; left: 690px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <!-- Connect Continuous Monitoring to subcomponents -->
        <div class="connection" style="top: 210px; left: 175px; width: 50px; height: 2px; background-color: black; transform: rotate(45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 240px; left: 115px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <div class="connection" style="top: 210px; left: 250px; width: 50px; height: 2px; background-color: black; transform: rotate(-45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 240px; left: 290px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <div class="connection" style="top: 210px; left: 350px; width: 125px; height: 2px; background-color: black; transform: rotate(-45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 240px; left: 465px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <!-- Connect Automated Triage to subcomponents -->
        <div class="connection" style="top: 210px; left: 550px; width: 50px; height: 2px; background-color: black; transform: rotate(45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 240px; left: 565px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <div class="connection" style="top: 210px; left: 650px; width: 100px; height: 2px; background-color: black; transform: rotate(-45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 240px; left: 740px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <div class="connection" style="top: 210px; left: 750px; width: 175px; height: 2px; background-color: black; transform: rotate(-45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 240px; left: 915px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <!-- Connect to Alert Generation -->
        <div class="connection" style="top: 270px; left: 250px; width: 100px; height: 2px; background-color: black; transform: rotate(-45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 300px; left: 340px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <div class="connection" style="top: 270px; left: 700px; width: 200px; height: 2px; background-color: black; transform: rotate(45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 300px; left: 490px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <!-- Connect Alert Generation to subcomponents -->
        <div class="connection" style="top: 330px; left: 300px; width: 100px; height: 2px; background-color: black; transform: rotate(45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 360px; left: 265px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <div class="connection" style="top: 330px; left: 450px; width: 50px; height: 2px; background-color: black; transform: rotate(-45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 360px; left: 465px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <div class="connection" style="top: 330px; left: 600px; width: 75px; height: 2px; background-color: black; transform: rotate(-45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 360px; left: 665px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <!-- Connect to Analysis Phase -->
        <div class="connection" style="top: 390px; left: 500px; width: 2px; height: 30px; background-color: black;"></div>
        <div class="arrow" style="top: 410px; left: 495px; border-width: 10px 5px 0 5px; border-color: black transparent transparent transparent;"></div>
        
        <!-- Connect Analysis Phase to components -->
        <div class="connection" style="top: 470px; left: 350px; width: 50px; height: 2px; background-color: black; transform: rotate(45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 480px; left: 290px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <div class="connection" style="top: 470px; left: 550px; width: 50px; height: 2px; background-color: black; transform: rotate(-45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 480px; left: 590px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <!-- Connect to Containment Phase -->
        <div class="connection" style="top: 510px; left: 500px; width: 2px; height: 30px; background-color: black;"></div>
        <div class="arrow" style="top: 530px; left: 495px; border-width: 10px 5px 0 5px; border-color: black transparent transparent transparent;"></div>
        
        <!-- Connect Containment Phase to components -->
        <div class="connection" style="top: 590px; left: 350px; width: 50px; height: 2px; background-color: black; transform: rotate(45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 600px; left: 290px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <div class="connection" style="top: 590px; left: 550px; width: 50px; height: 2px; background-color: black; transform: rotate(-45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 600px; left: 590px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <!-- Connect to Recovery Phase -->
        <div class="connection" style="top: 630px; left: 500px; width: 2px; height: 30px; background-color: black;"></div>
        <div class="arrow" style="top: 650px; left: 495px; border-width: 10px 5px 0 5px; border-color: black transparent transparent transparent;"></div>
        
        <!-- Connect Recovery Phase to components -->
        <div class="connection" style="top: 710px; left: 350px; width: 50px; height: 2px; background-color: black; transform: rotate(45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 720px; left: 290px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <div class="connection" style="top: 710px; left: 550px; width: 50px; height: 2px; background-color: black; transform: rotate(-45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 720px; left: 590px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
    </div>
</body>
</html>
