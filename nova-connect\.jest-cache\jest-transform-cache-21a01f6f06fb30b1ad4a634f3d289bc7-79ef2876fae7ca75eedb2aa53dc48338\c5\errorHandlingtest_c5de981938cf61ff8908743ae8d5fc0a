63f5a731129a56e17d02f996a3ebd52a
/**
 * Error Handling Tests
 * 
 * This file contains tests for the error handling infrastructure.
 */

const request = require('supertest');
const app = require('../../server');
const errorHandlingService = require('../../api/services/ErrorHandlingService');
describe('Error Handling Infrastructure', () => {
  describe('Error Handler Middleware', () => {
    it('should handle 404 errors', async () => {
      const response = await request(app).get('/non-existent-route').expect('Content-Type', /json/).expect(404);
      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toHaveProperty('type', 'not_found_error');
      expect(response.body.error).toHaveProperty('status', 404);
    });
    it('should handle validation errors', async () => {
      const response = await request(app).post('/api/connectors').send({}) // Missing required fields
      .expect('Content-Type', /json/).expect(400);
      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toHaveProperty('type', 'validation_error');
      expect(response.body.error).toHaveProperty('status', 400);
    });
  });
  describe('Error Handling Service', () => {
    it('should create error responses with appropriate status codes', () => {
      const error = new Error('Test error');
      error.name = 'ValidationError';
      const context = {
        user: {
          id: 'test-user'
        },
        path: '/test',
        method: 'GET'
      };
      const errorResponse = errorHandlingService.handleError(error, context);
      expect(errorResponse).toHaveProperty('error');
      expect(errorResponse.error).toHaveProperty('type', 'validation_error');
      expect(errorResponse.error).toHaveProperty('status', 400);
      expect(errorResponse.error).toHaveProperty('message', 'Test error');
    });
    it('should apply retry strategy for network errors', () => {
      const error = new Error('Connection refused');
      error.name = 'NetworkError';
      const context = {
        resource: 'test-api',
        retryCount: 1
      };
      const errorResponse = errorHandlingService.handleError(error, context);
      expect(errorResponse).toHaveProperty('error');
      expect(errorResponse.error).toHaveProperty('type', 'network_error');
      expect(errorResponse.error).toHaveProperty('status', 503);
      expect(errorResponse).toHaveProperty('recovery');
      expect(errorResponse.recovery).toHaveProperty('strategy', 'retry');
      expect(errorResponse.recovery).toHaveProperty('retryCount', 1);
    });
    it('should apply circuit breaker strategy for rate limit errors', () => {
      const error = new Error('Rate limit exceeded');
      error.name = 'RateLimitError';
      const context = {
        resource: 'test-api'
      };

      // Register circuit breaker for test
      errorHandlingService.registerCircuitBreaker('test-api', {
        failureThreshold: 5,
        resetTimeout: 30000
      });
      const errorResponse = errorHandlingService.handleError(error, context);
      expect(errorResponse).toHaveProperty('error');
      expect(errorResponse.error).toHaveProperty('type', 'rate_limit_error');
      expect(errorResponse.error).toHaveProperty('status', 429);
      expect(errorResponse).toHaveProperty('recovery');
      expect(errorResponse.recovery).toHaveProperty('strategy', 'circuit_breaker');
      expect(errorResponse.recovery).toHaveProperty('state', 'closed');
    });
  });
  describe('Retry Mechanism', () => {
    it('should retry failed operations', async () => {
      let attempts = 0;
      const testFunction = errorHandlingService.withRetry(async () => {
        attempts++;
        if (attempts < 3) {
          const error = new Error('Temporary failure');
          error.name = 'NetworkError';
          throw error;
        }
        return 'success';
      }, {
        maxRetries: 3,
        initialDelay: 10,
        maxDelay: 100,
        retryableErrors: ['network_error']
      });
      const result = await testFunction();
      expect(result).toBe('success');
      expect(attempts).toBe(3);
    });
    it('should not retry non-retryable errors', async () => {
      let attempts = 0;
      const testFunction = errorHandlingService.withRetry(async () => {
        attempts++;
        const error = new Error('Validation error');
        error.name = 'ValidationError';
        throw error;
      }, {
        maxRetries: 3,
        initialDelay: 10,
        maxDelay: 100,
        retryableErrors: ['network_error', 'timeout_error']
      });
      await expect(testFunction()).rejects.toThrow('Validation error');
      expect(attempts).toBe(1);
    });
  });
  describe('Circuit Breaker', () => {
    it('should trip circuit breaker after threshold failures', async () => {
      const resource = 'test-circuit-breaker';

      // Register circuit breaker
      errorHandlingService.registerCircuitBreaker(resource, {
        failureThreshold: 2,
        resetTimeout: 100
      });
      const circuitBreaker = errorHandlingService.circuitBreakers.get(resource);
      expect(circuitBreaker.state).toBe('closed');

      // Create test function
      const testFunction = errorHandlingService.withCircuitBreaker(async () => {
        throw new Error('Service unavailable');
      }, {
        resource
      });

      // First failure
      await expect(testFunction()).rejects.toThrow('Service unavailable');
      expect(circuitBreaker.state).toBe('closed');
      expect(circuitBreaker.failureCount).toBe(1);

      // Second failure - should trip circuit breaker
      await expect(testFunction()).rejects.toThrow('Service unavailable');
      expect(circuitBreaker.state).toBe('open');
      expect(circuitBreaker.failureCount).toBe(2);

      // Third attempt - circuit breaker is open
      await expect(testFunction()).rejects.toThrow('Circuit breaker for test-circuit-breaker is open');

      // Wait for circuit breaker to reset
      await new Promise(resolve => setTimeout(resolve, 150));

      // Circuit breaker should be half-open
      expect(circuitBreaker.state).toBe('half-open');

      // Successful call should close circuit breaker
      const successFunction = errorHandlingService.withCircuitBreaker(async () => {
        return 'success';
      }, {
        resource
      });
      await successFunction();
      expect(circuitBreaker.state).toBe('closed');
      expect(circuitBreaker.failureCount).toBe(0);
    });
  });
  describe('Timeout Mechanism', () => {
    it('should timeout long-running operations', async () => {
      const testFunction = errorHandlingService.withTimeout(async () => {
        await new Promise(resolve => setTimeout(resolve, 100));
        return 'success';
      }, {
        timeoutMs: 50
      });
      await expect(testFunction()).rejects.toThrow('Operation timed out after 50ms');
    });
    it('should not timeout quick operations', async () => {
      const testFunction = errorHandlingService.withTimeout(async () => {
        await new Promise(resolve => setTimeout(resolve, 10));
        return 'success';
      }, {
        timeoutMs: 50
      });
      const result = await testFunction();
      expect(result).toBe('success');
    });
  });
  describe('Fallback Mechanism', () => {
    it('should use fallback when primary function fails', async () => {
      const primaryFunction = async () => {
        throw new Error('Primary function failed');
      };
      const fallbackFunction = async () => {
        return 'fallback result';
      };
      const testFunction = errorHandlingService.withFallback(primaryFunction, fallbackFunction);
      const result = await testFunction();
      expect(result).toBe('fallback result');
    });
    it('should use primary function when it succeeds', async () => {
      const primaryFunction = async () => {
        return 'primary result';
      };
      const fallbackFunction = async () => {
        return 'fallback result';
      };
      const testFunction = errorHandlingService.withFallback(primaryFunction, fallbackFunction);
      const result = await testFunction();
      expect(result).toBe('primary result');
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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