/**
 * User Personas Data Model
 * 
 * This file defines the data structure for user personas in the NovaFuse GCP simulation.
 * It includes detailed persona information, permissions, dashboards, and preferences.
 */

// Sample user personas data
const userPersonas = [
  {
    id: "persona-ciso",
    role: "<PERSON><PERSON><PERSON>",
    name: "<PERSON>",
    email: "<EMAIL>",
    department: "Information Security",
    description: "Chief Information Security Officer responsible for overall security strategy and compliance.",
    permissions: [
      "view_all",
      "manage_policies",
      "approve_exceptions",
      "view_executive_reports",
      "manage_security_program",
      "assign_responsibilities"
    ],
    dashboards: [
      {
        id: "dashboard-ciso-1",
        name: "Compliance Drift Monitor",
        description: "Real-time view of compliance posture changes",
        default: true,
        widgets: [
          { 
            id: "widget-ciso-1-1",
            type: "ComplianceTrendChart", 
            title: "Compliance Trend",
            description: "Trend of compliance scores over time",
            size: "large",
            position: { row: 0, col: 0 },
            config: {
              timeRange: "90d", 
              refreshRate: "5m",
              frameworks: ["GDPR", "HIPAA", "PCI-DSS", "SOC2"],
              showTarget: true,
              targetScore: 85
            }
          },
          { 
            id: "widget-ciso-1-2",
            type: "RiskHeatmap", 
            title: "Risk Heatmap",
            description: "Heatmap of risk intensity by category",
            size: "medium",
            position: { row: 1, col: 0 },
            config: {
              categories: ["all"], 
              timeRange: "30d",
              showLegend: true,
              colorScale: "red"
            }
          },
          { 
            id: "widget-ciso-1-3",
            type: "ComplianceByFramework", 
            title: "Compliance by Framework",
            description: "Current compliance scores by framework",
            size: "medium",
            position: { row: 1, col: 1 },
            config: {
              frameworks: ["GDPR", "SOC2", "PCI-DSS", "HIPAA"],
              showTarget: true,
              targetScore: 85,
              sortBy: "score"
            }
          },
          { 
            id: "widget-ciso-1-4",
            type: "CriticalFindingsAlert", 
            title: "Critical Findings",
            description: "High and critical severity findings requiring attention",
            size: "medium",
            position: { row: 2, col: 0 },
            config: {
              severity: ["HIGH", "CRITICAL"], 
              age: ">7d",
              limit: 10,
              groupBy: "category"
            }
          },
          { 
            id: "widget-ciso-1-5",
            type: "ComplianceScorecard", 
            title: "Executive Scorecard",
            description: "Executive summary of compliance and security posture",
            size: "medium",
            position: { row: 2, col: 1 },
            config: {
              metrics: ["ComplianceScore", "SecurityPosture", "RiskLevel", "FindingsCount"],
              showTrend: true,
              trendPeriod: "30d"
            }
          }
        ]
      },
      {
        id: "dashboard-ciso-2",
        name: "Security Program Overview",
        description: "High-level view of security program effectiveness",
        default: false,
        widgets: [
          { 
            id: "widget-ciso-2-1",
            type: "SecurityPostureChart", 
            title: "Security Posture",
            description: "Overall security posture score and trend",
            size: "large",
            position: { row: 0, col: 0 },
            config: {
              timeRange: "90d", 
              refreshRate: "5m",
              categories: ["all"],
              showTarget: true,
              targetScore: 85
            }
          },
          { 
            id: "widget-ciso-2-2",
            type: "SecurityControlCoverage", 
            title: "Control Coverage",
            description: "Coverage of security controls by category",
            size: "medium",
            position: { row: 1, col: 0 },
            config: {
              categories: ["all"],
              showGaps: true,
              sortBy: "coverage"
            }
          },
          { 
            id: "widget-ciso-2-3",
            type: "SecurityIncidentTimeline", 
            title: "Security Incidents",
            description: "Timeline of security incidents",
            size: "medium",
            position: { row: 1, col: 1 },
            config: {
              timeRange: "90d",
              severity: ["all"],
              showResolution: true,
              groupBy: "category"
            }
          },
          { 
            id: "widget-ciso-2-4",
            type: "SecurityInvestmentROI", 
            title: "Security ROI",
            description: "Return on investment for security initiatives",
            size: "medium",
            position: { row: 2, col: 0 },
            config: {
              timeRange: "365d",
              initiatives: ["all"],
              metrics: ["CostAvoidance", "EfficiencyGains", "RiskReduction"]
            }
          },
          { 
            id: "widget-ciso-2-5",
            type: "SecurityRoadmap", 
            title: "Security Roadmap",
            description: "Roadmap of security initiatives and milestones",
            size: "medium",
            position: { row: 2, col: 1 },
            config: {
              timeRange: "365d",
              showMilestones: true,
              categories: ["all"]
            }
          }
        ]
      }
    ],
    preferredMetrics: ["ComplianceDrift", "RiskTrend", "RemediationVelocity", "SecurityPosture"],
    notificationPreferences: {
      email: true,
      inApp: true,
      frequency: "daily",
      categories: ["Critical Findings", "Compliance Changes", "Security Incidents"]
    },
    lastLogin: "2023-06-20T09:15:00Z"
  },
  {
    id: "persona-auditor",
    role: "Auditor",
    name: "Michael Washington",
    email: "<EMAIL>",
    department: "Compliance",
    description: "Internal auditor responsible for assessing compliance with regulatory requirements and internal policies.",
    permissions: [
      "view_evidence",
      "view_controls",
      "view_audit_logs",
      "export_reports",
      "view_compliance_status",
      "view_findings"
    ],
    dashboards: [
      {
        id: "dashboard-auditor-1",
        name: "Evidence Chain Analysis",
        description: "Comprehensive view of control evidence and audit trails",
        default: true,
        widgets: [
          { 
            id: "widget-auditor-1-1",
            type: "EvidenceTimeline", 
            title: "Evidence Timeline",
            description: "Timeline of control evidence collection",
            size: "large",
            position: { row: 0, col: 0 },
            config: {
              controls: ["all"], 
              timeRange: "180d",
              groupBy: "framework",
              showGaps: true
            }
          },
          { 
            id: "widget-auditor-1-2",
            type: "ControlTestResults", 
            title: "Control Test Results",
            description: "Results of control tests",
            size: "medium",
            position: { row: 1, col: 0 },
            config: {
              status: ["all"], 
              sortBy: "lastTested",
              frameworks: ["all"],
              showEvidence: true
            }
          },
          { 
            id: "widget-auditor-1-3",
            type: "AuditLogAnalyzer", 
            title: "Audit Log Analysis",
            description: "Analysis of audit logs for compliance-relevant events",
            size: "medium",
            position: { row: 1, col: 1 },
            config: {
              services: ["all"], 
              anomalyDetection: true,
              timeRange: "30d",
              eventTypes: ["all"]
            }
          },
          { 
            id: "widget-auditor-1-4",
            type: "ComplianceDocumentLibrary", 
            title: "Compliance Documents",
            description: "Library of compliance documents and evidence",
            size: "medium",
            position: { row: 2, col: 0 },
            config: {
              frameworks: ["all"], 
              status: ["all"],
              documentTypes: ["all"],
              sortBy: "lastUpdated"
            }
          },
          { 
            id: "widget-auditor-1-5",
            type: "ComplianceGapAnalysis", 
            title: "Compliance Gaps",
            description: "Analysis of compliance gaps and remediation status",
            size: "medium",
            position: { row: 2, col: 1 },
            config: {
              frameworks: ["all"],
              severity: ["all"],
              showRemediation: true,
              groupBy: "framework"
            }
          }
        ]
      },
      {
        id: "dashboard-auditor-2",
        name: "Audit Planning",
        description: "Tools for planning and managing audits",
        default: false,
        widgets: [
          { 
            id: "widget-auditor-2-1",
            type: "AuditCalendar", 
            title: "Audit Calendar",
            description: "Calendar of planned and completed audits",
            size: "large",
            position: { row: 0, col: 0 },
            config: {
              timeRange: "365d", 
              auditTypes: ["all"],
              showStatus: true
            }
          },
          { 
            id: "widget-auditor-2-2",
            type: "AuditScope", 
            title: "Audit Scope",
            description: "Scope of current and planned audits",
            size: "medium",
            position: { row: 1, col: 0 },
            config: {
              auditId: "current",
              showControls: true,
              showSystems: true
            }
          },
          { 
            id: "widget-auditor-2-3",
            type: "AuditFindings", 
            title: "Audit Findings",
            description: "Findings from previous audits",
            size: "medium",
            position: { row: 1, col: 1 },
            config: {
              timeRange: "365d",
              severity: ["all"],
              status: ["all"],
              groupBy: "category"
            }
          },
          { 
            id: "widget-auditor-2-4",
            type: "AuditResources", 
            title: "Audit Resources",
            description: "Resources allocated to audits",
            size: "medium",
            position: { row: 2, col: 0 },
            config: {
              timeRange: "90d",
              resourceTypes: ["all"],
              showUtilization: true
            }
          },
          { 
            id: "widget-auditor-2-5",
            type: "AuditMetrics", 
            title: "Audit Metrics",
            description: "Metrics on audit effectiveness and efficiency",
            size: "medium",
            position: { row: 2, col: 1 },
            config: {
              timeRange: "365d",
              metrics: ["FindingsPerAudit", "TimeToRemediate", "AuditCoverage", "AuditEfficiency"],
              showTrend: true
            }
          }
        ]
      }
    ],
    preferredMetrics: ["EvidenceCompleteness", "ControlEffectiveness", "AuditTrailIntegrity", "ComplianceScore"],
    notificationPreferences: {
      email: true,
      inApp: true,
      frequency: "weekly",
      categories: ["Evidence Collection", "Control Testing", "Compliance Changes"]
    },
    lastLogin: "2023-06-19T14:30:00Z"
  },
  {
    id: "persona-security-engineer",
    role: "Security Engineer",
    name: "Jamal Rodriguez",
    email: "<EMAIL>",
    department: "Information Security",
    description: "Security engineer responsible for implementing and maintaining security controls.",
    permissions: [
      "view_findings",
      "manage_remediation",
      "run_assessments",
      "configure_integrations",
      "view_security_controls",
      "manage_security_tools"
    ],
    dashboards: [
      {
        id: "dashboard-security-engineer-1",
        name: "Technical Control Dashboard",
        description: "Detailed view of security controls and technical findings",
        default: true,
        widgets: [
          { 
            id: "widget-security-engineer-1-1",
            type: "RawSecurityLogs", 
            title: "Security Logs",
            description: "Raw security logs from various services",
            size: "large",
            position: { row: 0, col: 0 },
            config: {
              services: ["all"], 
              severity: ["CRITICAL", "HIGH"],
              limit: 100,
              refreshRate: "1m"
            }
          },
          { 
            id: "widget-security-engineer-1-2",
            type: "RemediationTaskboard", 
            title: "Remediation Tasks",
            description: "Tasks for remediating security findings",
            size: "medium",
            position: { row: 1, col: 0 },
            config: {
              status: ["OPEN", "IN_PROGRESS"], 
              assignee: "me",
              sortBy: "priority",
              groupBy: "category"
            }
          },
          { 
            id: "widget-security-engineer-1-3",
            type: "VulnerabilityScanner", 
            title: "Vulnerability Scanner",
            description: "Real-time vulnerability scanning results",
            size: "medium",
            position: { row: 1, col: 1 },
            config: {
              resources: ["all"], 
              refreshRate: "60m",
              severity: ["CRITICAL", "HIGH", "MEDIUM"],
              groupBy: "resource"
            }
          },
          { 
            id: "widget-security-engineer-1-4",
            type: "APIIntegrationStatus", 
            title: "API Integration Status",
            description: "Status of API integrations with security tools",
            size: "medium",
            position: { row: 2, col: 0 },
            config: {
              connectors: ["all"], 
              healthCheck: true,
              showErrors: true,
              refreshRate: "5m"
            }
          },
          { 
            id: "widget-security-engineer-1-5",
            type: "SecurityControlImplementation", 
            title: "Control Implementation",
            description: "Implementation status of security controls",
            size: "medium",
            position: { row: 2, col: 1 },
            config: {
              categories: ["all"],
              status: ["all"],
              assignee: "all",
              groupBy: "category"
            }
          }
        ]
      },
      {
        id: "dashboard-security-engineer-2",
        name: "Security Monitoring",
        description: "Real-time security monitoring and alerting",
        default: false,
        widgets: [
          { 
            id: "widget-security-engineer-2-1",
            type: "SecurityAlertDashboard", 
            title: "Security Alerts",
            description: "Real-time security alerts from all sources",
            size: "large",
            position: { row: 0, col: 0 },
            config: {
              sources: ["all"], 
              severity: ["all"],
              timeRange: "24h",
              refreshRate: "1m"
            }
          },
          { 
            id: "widget-security-engineer-2-2",
            type: "NetworkTrafficAnalysis", 
            title: "Network Traffic",
            description: "Analysis of network traffic patterns",
            size: "medium",
            position: { row: 1, col: 0 },
            config: {
              networks: ["all"],
              metrics: ["volume", "connections", "protocols"],
              timeRange: "6h",
              refreshRate: "5m"
            }
          },
          { 
            id: "widget-security-engineer-2-3",
            type: "UserActivityMonitor", 
            title: "User Activity",
            description: "Monitoring of user activity and authentication",
            size: "medium",
            position: { row: 1, col: 1 },
            config: {
              userTypes: ["all"],
              activityTypes: ["authentication", "authorization", "data_access"],
              timeRange: "24h",
              anomalyDetection: true
            }
          },
          { 
            id: "widget-security-engineer-2-4",
            type: "CloudResourceMonitor", 
            title: "Cloud Resources",
            description: "Monitoring of cloud resource security configuration",
            size: "medium",
            position: { row: 2, col: 0 },
            config: {
              resourceTypes: ["all"],
              complianceStatus: ["all"],
              refreshRate: "15m",
              groupBy: "type"
            }
          },
          { 
            id: "widget-security-engineer-2-5",
            type: "SecurityIncidentTracker", 
            title: "Security Incidents",
            description: "Tracking of active security incidents",
            size: "medium",
            position: { row: 2, col: 1 },
            config: {
              status: ["OPEN", "INVESTIGATING"],
              severity: ["all"],
              assignee: "all",
              sortBy: "severity"
            }
          }
        ]
      }
    ],
    preferredMetrics: ["MeanTimeToRemediate", "VulnerabilityDensity", "PatchCompliance", "SecurityControlCoverage"],
    notificationPreferences: {
      email: true,
      inApp: true,
      frequency: "realtime",
      categories: ["Critical Vulnerabilities", "Security Incidents", "Failed Controls"]
    },
    lastLogin: "2023-06-20T11:45:00Z"
  },
  {
    id: "persona-compliance-officer",
    role: "Compliance Officer",
    name: "Elena Patel",
    email: "<EMAIL>",
    department: "Compliance",
    description: "Compliance officer responsible for ensuring adherence to regulatory requirements and internal policies.",
    permissions: [
      "view_compliance_status",
      "manage_compliance_tasks",
      "generate_reports",
      "view_audit_logs",
      "manage_policies",
      "view_evidence"
    ],
    dashboards: [
      {
        id: "dashboard-compliance-officer-1",
        name: "Compliance Overview",
        description: "High-level view of compliance status across all frameworks",
        default: true,
        widgets: [
          { 
            id: "widget-compliance-officer-1-1",
            type: "ComplianceScorecard", 
            title: "Compliance Scorecard",
            description: "Overall compliance scores by framework",
            size: "large",
            position: { row: 0, col: 0 },
            config: {
              frameworks: ["all"], 
              showTrend: true,
              trendPeriod: "90d",
              showTarget: true
            }
          },
          { 
            id: "widget-compliance-officer-1-2",
            type: "ComplianceTaskboard", 
            title: "Compliance Tasks",
            description: "Tasks for maintaining compliance",
            size: "medium",
            position: { row: 1, col: 0 },
            config: {
              status: ["OPEN", "IN_PROGRESS", "OVERDUE"], 
              assignee: "all",
              sortBy: "dueDate",
              groupBy: "framework"
            }
          },
          { 
            id: "widget-compliance-officer-1-3",
            type: "ControlGapAnalysis", 
            title: "Control Gaps",
            description: "Analysis of control gaps across frameworks",
            size: "medium",
            position: { row: 1, col: 1 },
            config: {
              frameworks: ["all"], 
              status: ["FAIL", "PARTIAL"],
              sortBy: "impact",
              showRemediation: true
            }
          },
          { 
            id: "widget-compliance-officer-1-4",
            type: "ComplianceCalendar", 
            title: "Compliance Calendar",
            description: "Calendar of compliance deadlines and activities",
            size: "medium",
            position: { row: 2, col: 0 },
            config: {
              timeRange: "90d", 
              eventTypes: ["all"],
              frameworks: ["all"],
              showStatus: true
            }
          },
          { 
            id: "widget-compliance-officer-1-5",
            type: "RegulatoryUpdates", 
            title: "Regulatory Updates",
            description: "Recent and upcoming regulatory changes",
            size: "medium",
            position: { row: 2, col: 1 },
            config: {
              frameworks: ["all"],
              timeRange: "90d",
              impactLevels: ["all"],
              sortBy: "date"
            }
          }
        ]
      },
      {
        id: "dashboard-compliance-officer-2",
        name: "Framework Management",
        description: "Detailed management of specific compliance frameworks",
        default: false,
        widgets: [
          { 
            id: "widget-compliance-officer-2-1",
            type: "FrameworkControlMatrix", 
            title: "Control Matrix",
            description: "Matrix of controls across frameworks",
            size: "large",
            position: { row: 0, col: 0 },
            config: {
              frameworks: ["GDPR", "HIPAA", "PCI-DSS", "SOC2"], 
              showMappings: true,
              showStatus: true,
              groupBy: "category"
            }
          },
          { 
            id: "widget-compliance-officer-2-2",
            type: "ControlImplementation", 
            title: "Control Implementation",
            description: "Implementation status of controls by framework",
            size: "medium",
            position: { row: 1, col: 0 },
            config: {
              frameworks: ["all"],
              status: ["all"],
              showEvidence: true,
              groupBy: "status"
            }
          },
          { 
            id: "widget-compliance-officer-2-3",
            type: "EvidenceRepository", 
            title: "Evidence Repository",
            description: "Repository of compliance evidence",
            size: "medium",
            position: { row: 1, col: 1 },
            config: {
              frameworks: ["all"],
              evidenceTypes: ["all"],
              timeRange: "365d",
              sortBy: "date"
            }
          },
          { 
            id: "widget-compliance-officer-2-4",
            type: "PolicyManagement", 
            title: "Policy Management",
            description: "Management of compliance policies",
            size: "medium",
            position: { row: 2, col: 0 },
            config: {
              policyTypes: ["all"],
              status: ["all"],
              showReviewDates: true,
              sortBy: "reviewDate"
            }
          },
          { 
            id: "widget-compliance-officer-2-5",
            type: "ComplianceReporting", 
            title: "Compliance Reporting",
            description: "Generation and management of compliance reports",
            size: "medium",
            position: { row: 2, col: 1 },
            config: {
              reportTypes: ["all"],
              frameworks: ["all"],
              timeRange: "365d",
              sortBy: "date"
            }
          }
        ]
      }
    ],
    preferredMetrics: ["ComplianceScore", "ControlEffectiveness", "TaskCompletion", "EvidenceCompleteness"],
    notificationPreferences: {
      email: true,
      inApp: true,
      frequency: "daily",
      categories: ["Compliance Changes", "Task Deadlines", "Control Failures", "Regulatory Updates"]
    },
    lastLogin: "2023-06-20T10:30:00Z"
  },
  {
    id: "persona-gcp-admin",
    role: "GCP Administrator",
    name: "David Kim",
    email: "<EMAIL>",
    department: "Cloud Operations",
    description: "Google Cloud Platform administrator responsible for managing cloud resources and configurations.",
    permissions: [
      "view_gcp_resources",
      "manage_gcp_configurations",
      "view_security_recommendations",
      "implement_controls",
      "view_compliance_requirements",
      "manage_cloud_resources"
    ],
    dashboards: [
      {
        id: "dashboard-gcp-admin-1",
        name: "GCP Resource Management",
        description: "Management of Google Cloud resources and configurations",
        default: true,
        widgets: [
          { 
            id: "widget-gcp-admin-1-1",
            type: "GCPResourceInventory", 
            title: "Resource Inventory",
            description: "Inventory of all Google Cloud resources",
            size: "large",
            position: { row: 0, col: 0 },
            config: {
              resourceTypes: ["all"], 
              projects: ["all"],
              groupBy: "type",
              showDetails: true
            }
          },
          { 
            id: "widget-gcp-admin-1-2",
            type: "ConfigurationStatus", 
            title: "Configuration Status",
            description: "Status of resource configurations",
            size: "medium",
            position: { row: 1, col: 0 },
            config: {
              resourceTypes: ["all"], 
              complianceStatus: ["all"],
              showRecommendations: true,
              groupBy: "status"
            }
          },
          { 
            id: "widget-gcp-admin-1-3",
            type: "SecurityRecommendations", 
            title: "Security Recommendations",
            description: "Security recommendations for Google Cloud resources",
            size: "medium",
            position: { row: 1, col: 1 },
            config: {
              severity: ["all"], 
              resourceTypes: ["all"],
              status: ["ACTIVE"],
              sortBy: "severity"
            }
          },
          { 
            id: "widget-gcp-admin-1-4",
            type: "ServiceHealth", 
            title: "Service Health",
            description: "Health status of Google Cloud services",
            size: "medium",
            position: { row: 2, col: 0 },
            config: {
              services: ["all"], 
              regions: ["all"],
              showIncidents: true,
              refreshRate: "5m"
            }
          },
          { 
            id: "widget-gcp-admin-1-5",
            type: "ComplianceByService", 
            title: "Compliance by Service",
            description: "Compliance status by Google Cloud service",
            size: "medium",
            position: { row: 2, col: 1 },
            config: {
              services: ["all"],
              frameworks: ["all"],
              showDetails: true,
              groupBy: "service"
            }
          }
        ]
      },
      {
        id: "dashboard-gcp-admin-2",
        name: "GCP Security Management",
        description: "Management of Google Cloud security configurations",
        default: false,
        widgets: [
          { 
            id: "widget-gcp-admin-2-1",
            type: "SecurityCommandCenter", 
            title: "Security Command Center",
            description: "Security findings from Security Command Center",
            size: "large",
            position: { row: 0, col: 0 },
            config: {
              findingTypes: ["all"], 
              severity: ["all"],
              status: ["ACTIVE"],
              groupBy: "category"
            }
          },
          { 
            id: "widget-gcp-admin-2-2",
            type: "IAMAnalysis", 
            title: "IAM Analysis",
            description: "Analysis of IAM policies and permissions",
            size: "medium",
            position: { row: 1, col: 0 },
            config: {
              projects: ["all"],
              showExcessivePermissions: true,
              showUnusedPermissions: true,
              groupBy: "role"
            }
          },
          { 
            id: "widget-gcp-admin-2-3",
            type: "NetworkSecurityAnalysis", 
            title: "Network Security",
            description: "Analysis of network security configurations",
            size: "medium",
            position: { row: 1, col: 1 },
            config: {
              networks: ["all"],
              resourceTypes: ["firewall", "vpc", "load_balancer"],
              showViolations: true,
              groupBy: "network"
            }
          },
          { 
            id: "widget-gcp-admin-2-4",
            type: "EncryptionStatus", 
            title: "Encryption Status",
            description: "Status of data encryption across services",
            size: "medium",
            position: { row: 2, col: 0 },
            config: {
              services: ["all"],
              encryptionTypes: ["all"],
              showViolations: true,
              groupBy: "service"
            }
          },
          { 
            id: "widget-gcp-admin-2-5",
            type: "SecurityTaskboard", 
            title: "Security Tasks",
            description: "Tasks for implementing security controls",
            size: "medium",
            position: { row: 2, col: 1 },
            config: {
              status: ["OPEN", "IN_PROGRESS"],
              priority: ["all"],
              assignee: "me",
              sortBy: "priority"
            }
          }
        ]
      }
    ],
    preferredMetrics: ["MisconfiguredResources", "ComplianceByService", "ResourceUtilization", "SecurityFindingsByService"],
    notificationPreferences: {
      email: true,
      inApp: true,
      frequency: "realtime",
      categories: ["Security Findings", "Configuration Changes", "Service Health", "Compliance Violations"]
    },
    lastLogin: "2023-06-20T08:45:00Z"
  }
];

module.exports = userPersonas;
