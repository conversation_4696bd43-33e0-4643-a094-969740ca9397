/**
 * NovaFuse Universal API Connector - Database Connection Pool
 *
 * This module provides a connection pool for database operations.
 */

const { createLogger } = require('./logger');

const logger = createLogger('db-pool');

/**
 * Database connection pool
 */
class DatabasePool {
  constructor(options = {}) {
    this.options = {
      min: options.min || 5,
      max: options.max || 20,
      idleTimeoutMillis: options.idleTimeoutMillis || 30000,
      connectionTimeoutMillis: options.connectionTimeoutMillis || 2000,
      ...options
    };

    this.connections = [];
    this.activeConnections = 0;
    this.waitingClients = [];

    logger.info('Database connection pool initialized', {
      min: this.options.min,
      max: this.options.max
    });

    // Initialize minimum connections
    this._initializeMinConnections();
  }

  /**
   * Initialize minimum connections
   *
   * @private
   */
  _initializeMinConnections() {
    for (let i = 0; i < this.options.min; i++) {
      this._createConnection();
    }
  }

  /**
   * Create a new connection
   *
   * @returns {Object} - The connection
   * @private
   */
  _createConnection() {
    // In a real implementation, this would create a database connection
    // For now, we'll simulate a connection
    const connection = {
      id: `conn-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
      createdAt: Date.now(),
      lastUsedAt: Date.now(),
      isActive: false
    };

    this.connections.push(connection);

    logger.debug('Created new connection', { connectionId: connection.id });

    return connection;
  }

  /**
   * Get a connection from the pool
   *
   * @returns {Promise<Object>} - The connection
   */
  async getConnection() {
    // Check for available connection
    const availableConnection = this.connections.find(conn => !conn.isActive);

    if (availableConnection) {
      availableConnection.isActive = true;
      availableConnection.lastUsedAt = Date.now();
      this.activeConnections++;

      logger.debug('Reusing existing connection', { connectionId: availableConnection.id });

      return availableConnection;
    }

    // If we can create a new connection, do so
    if (this.connections.length < this.options.max) {
      const newConnection = this._createConnection();
      newConnection.isActive = true;
      this.activeConnections++;

      logger.debug('Created new connection for immediate use', { connectionId: newConnection.id });

      return newConnection;
    }

    // Otherwise, wait for a connection to become available
    logger.debug('Waiting for connection to become available', {
      activeConnections: this.activeConnections,
      totalConnections: this.connections.length
    });

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        // Remove from waiting clients
        this.waitingClients = this.waitingClients.filter(client => client.resolve !== resolve);

        reject(new Error('Connection timeout'));
      }, this.options.connectionTimeoutMillis);

      this.waitingClients.push({
        resolve,
        reject,
        timeout
      });
    });
  }

  /**
   * Release a connection back to the pool
   *
   * @param {Object} connection - The connection to release
   */
  releaseConnection(connection) {
    const conn = this.connections.find(c => c.id === connection.id);

    if (!conn) {
      logger.warn('Attempted to release unknown connection', { connectionId: connection.id });
      return;
    }

    conn.isActive = false;
    conn.lastUsedAt = Date.now();
    this.activeConnections--;

    logger.debug('Released connection back to pool', { connectionId: connection.id });

    // Check if there are waiting clients
    if (this.waitingClients.length > 0) {
      const client = this.waitingClients.shift();
      clearTimeout(client.timeout);

      conn.isActive = true;
      this.activeConnections++;

      logger.debug('Providing connection to waiting client', { connectionId: conn.id });

      client.resolve(conn);
    }
  }

  /**
   * Clean up idle connections
   */
  cleanUpIdleConnections() {
    const now = Date.now();

    // Only clean up connections if we have more than the minimum
    if (this.connections.length > this.options.min) {
      const idleConnections = this.connections.filter(
        conn => !conn.isActive && (now - conn.lastUsedAt) > this.options.idleTimeoutMillis
      );

      if (idleConnections.length > 0) {
        // Remove idle connections, but ensure we keep at least the minimum
        const connectionsToRemove = Math.min(
          idleConnections.length,
          this.connections.length - this.options.min
        );

        if (connectionsToRemove > 0) {
          const connectionsToRemoveIds = idleConnections
            .slice(0, connectionsToRemove)
            .map(conn => conn.id);

          this.connections = this.connections.filter(
            conn => !connectionsToRemoveIds.includes(conn.id)
          );

          logger.debug('Removed idle connections', {
            count: connectionsToRemove,
            remainingConnections: this.connections.length
          });
        }
      }
    }
  }

  /**
   * Get pool statistics
   *
   * @returns {Object} - The pool statistics
   */
  getStats() {
    return {
      totalConnections: this.connections.length,
      activeConnections: this.activeConnections,
      idleConnections: this.connections.length - this.activeConnections,
      waitingClients: this.waitingClients.length
    };
  }

  /**
   * Reset pool statistics
   *
   * @returns {Object} - The reset statistics
   */
  resetStats() {
    // For the database pool, we don't have cumulative statistics to reset
    // But we can log the reset request and return current stats
    logger.info('Database pool statistics reset requested');

    return this.getStats();
  }

  /**
   * Close all connections and shut down the pool
   */
  async close() {
    logger.info('Closing connection pool');

    // Reject any waiting clients
    for (const client of this.waitingClients) {
      clearTimeout(client.timeout);
      client.reject(new Error('Connection pool closing'));
    }

    this.waitingClients = [];

    // In a real implementation, we would close each connection
    // For now, we'll just clear the array
    this.connections = [];
    this.activeConnections = 0;

    logger.info('Connection pool closed');
  }
}

// Create singleton instance
const dbPool = new DatabasePool();

// Set up periodic cleanup
setInterval(() => {
  dbPool.cleanUpIdleConnections();
}, 60000); // Run every minute

module.exports = dbPool;
