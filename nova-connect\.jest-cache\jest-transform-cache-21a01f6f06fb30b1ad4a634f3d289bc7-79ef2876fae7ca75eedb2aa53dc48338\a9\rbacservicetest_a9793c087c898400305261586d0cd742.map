{"version": 3, "names": ["mongoose", "require", "RBACService", "Role", "Permission", "UserRole", "User", "ValidationError", "NotFoundError", "setupTestEnvironment", "clearDatabase", "disconnectFromDatabase", "getTestData", "rbacService", "adminUser", "regularUser", "viewerUser", "testRole", "testPermission", "beforeAll", "testData", "error", "console", "afterAll", "after<PERSON>ach", "describe", "test", "roles", "getAllRoles", "expect", "toBeDefined", "Array", "isArray", "toBe", "length", "toBeGreaterThan", "role", "toHaveProperty", "getRoleById", "_id", "toString", "name", "nonExistentId", "Types", "ObjectId", "rejects", "toThrow", "newRole", "description", "permissions", "createdRole", "createRole", "deleteOne", "duplicateRole", "roleToUpdate", "create", "updateData", "updatedRole", "updateRole", "roleToDelete", "result", "deleteRole", "success", "deletedRole", "findById", "toBeNull", "getAllPermissions", "permission", "getPermissionById", "getPermissionByResourceAction", "resource", "action", "newPermission", "createdPermission", "createPermission", "getUserRoles", "adminRole", "find", "assignRoleToUser", "userRoles", "hasTestRole", "some", "user", "removeRoleFromUser", "hasPermission", "getUserPermissions", "hasViewPermission", "p"], "sources": ["rbac-service.test.js"], "sourcesContent": ["/**\n * RBAC Service Unit Tests\n * \n * This file contains unit tests for the RBAC service.\n */\n\nconst mongoose = require('mongoose');\nconst RBACService = require('../../api/services/RBACService');\nconst Role = require('../../api/models/Role');\nconst Permission = require('../../api/models/Permission');\nconst UserRole = require('../../api/models/UserRole');\nconst User = require('../../api/models/User');\nconst { ValidationError, NotFoundError } = require('../../api/utils/errors');\nconst { setupTestEnvironment, clearDatabase, disconnectFromDatabase, getTestData } = require('../setup/rbac-test-setup');\n\n// Initialize RBAC service\nconst rbacService = new RBACService();\n\n// Test data\nlet adminUser;\nlet regularUser;\nlet viewerUser;\nlet testRole;\nlet testPermission;\n\n// Setup and teardown\nbeforeAll(async () => {\n  try {\n    const testData = await setupTestEnvironment();\n    adminUser = testData.adminUser;\n    regularUser = testData.regularUser;\n    viewerUser = testData.viewerUser;\n    testRole = testData.testRole;\n    testPermission = testData.testPermission;\n  } catch (error) {\n    console.error('Error in beforeAll:', error);\n    throw error;\n  }\n});\n\nafterAll(async () => {\n  await disconnectFromDatabase();\n});\n\n// Reset database between tests\nafterEach(async () => {\n  // No need to clear database between tests as we're using transactions\n});\n\n// Test suites\ndescribe('RBACService', () => {\n  // Role tests\n  describe('Role Management', () => {\n    test('getAllRoles should return all roles', async () => {\n      const roles = await rbacService.getAllRoles();\n      expect(roles).toBeDefined();\n      expect(Array.isArray(roles)).toBe(true);\n      expect(roles.length).toBeGreaterThan(0);\n      \n      // Check if roles have expected properties\n      const role = roles[0];\n      expect(role).toHaveProperty('_id');\n      expect(role).toHaveProperty('name');\n      expect(role).toHaveProperty('permissions');\n    });\n    \n    test('getRoleById should return a role by ID', async () => {\n      const role = await rbacService.getRoleById(testRole._id);\n      expect(role).toBeDefined();\n      expect(role._id.toString()).toBe(testRole._id.toString());\n      expect(role.name).toBe(testRole.name);\n    });\n    \n    test('getRoleById should throw NotFoundError for non-existent role', async () => {\n      const nonExistentId = new mongoose.Types.ObjectId();\n      await expect(rbacService.getRoleById(nonExistentId)).rejects.toThrow(NotFoundError);\n    });\n    \n    test('createRole should create a new role', async () => {\n      const newRole = {\n        name: 'New Test Role',\n        description: 'A new role for testing',\n        permissions: [testPermission._id]\n      };\n      \n      const createdRole = await rbacService.createRole(newRole);\n      expect(createdRole).toBeDefined();\n      expect(createdRole.name).toBe(newRole.name);\n      expect(createdRole.description).toBe(newRole.description);\n      expect(Array.isArray(createdRole.permissions)).toBe(true);\n      expect(createdRole.permissions.length).toBe(1);\n      \n      // Clean up\n      await Role.deleteOne({ _id: createdRole._id });\n    });\n    \n    test('createRole should handle string permission format', async () => {\n      const newRole = {\n        name: 'String Permission Role',\n        description: 'A role with string permission format',\n        permissions: ['resource:view']\n      };\n      \n      const createdRole = await rbacService.createRole(newRole);\n      expect(createdRole).toBeDefined();\n      expect(createdRole.name).toBe(newRole.name);\n      expect(Array.isArray(createdRole.permissions)).toBe(true);\n      expect(createdRole.permissions.length).toBe(1);\n      \n      // Clean up\n      await Role.deleteOne({ _id: createdRole._id });\n    });\n    \n    test('createRole should throw ValidationError for duplicate role name', async () => {\n      const duplicateRole = {\n        name: testRole.name,\n        description: 'This should fail',\n        permissions: [testPermission._id]\n      };\n      \n      await expect(rbacService.createRole(duplicateRole)).rejects.toThrow(ValidationError);\n    });\n    \n    test('updateRole should update a role', async () => {\n      // Create a role to update\n      const roleToUpdate = await Role.create({\n        name: 'Role To Update',\n        description: 'This role will be updated',\n        permissions: [testPermission._id]\n      });\n      \n      const updateData = {\n        name: 'Updated Role Name',\n        description: 'This role has been updated'\n      };\n      \n      const updatedRole = await rbacService.updateRole(roleToUpdate._id, updateData);\n      expect(updatedRole).toBeDefined();\n      expect(updatedRole.name).toBe(updateData.name);\n      expect(updatedRole.description).toBe(updateData.description);\n      \n      // Clean up\n      await Role.deleteOne({ _id: roleToUpdate._id });\n    });\n    \n    test('deleteRole should delete a role', async () => {\n      // Create a role to delete\n      const roleToDelete = await Role.create({\n        name: 'Role To Delete',\n        description: 'This role will be deleted',\n        permissions: [testPermission._id]\n      });\n      \n      const result = await rbacService.deleteRole(roleToDelete._id);\n      expect(result).toBeDefined();\n      expect(result.success).toBe(true);\n      \n      // Verify role is deleted\n      const deletedRole = await Role.findById(roleToDelete._id);\n      expect(deletedRole).toBeNull();\n    });\n  });\n  \n  // Permission tests\n  describe('Permission Management', () => {\n    test('getAllPermissions should return all permissions', async () => {\n      const permissions = await rbacService.getAllPermissions();\n      expect(permissions).toBeDefined();\n      expect(Array.isArray(permissions)).toBe(true);\n      expect(permissions.length).toBeGreaterThan(0);\n      \n      // Check if permissions have expected properties\n      const permission = permissions[0];\n      expect(permission).toHaveProperty('_id');\n      expect(permission).toHaveProperty('name');\n      expect(permission).toHaveProperty('resource');\n      expect(permission).toHaveProperty('action');\n    });\n    \n    test('getPermissionById should return a permission by ID', async () => {\n      const permission = await rbacService.getPermissionById(testPermission._id);\n      expect(permission).toBeDefined();\n      expect(permission._id.toString()).toBe(testPermission._id.toString());\n      expect(permission.name).toBe(testPermission.name);\n    });\n    \n    test('getPermissionByResourceAction should return a permission by resource and action', async () => {\n      const permission = await rbacService.getPermissionByResourceAction('resource', 'view');\n      expect(permission).toBeDefined();\n      expect(permission.resource).toBe('resource');\n      expect(permission.action).toBe('view');\n    });\n    \n    test('createPermission should create a new permission', async () => {\n      const newPermission = {\n        name: 'New Test Permission',\n        description: 'A new permission for testing',\n        resource: 'test',\n        action: 'test'\n      };\n      \n      const createdPermission = await rbacService.createPermission(newPermission);\n      expect(createdPermission).toBeDefined();\n      expect(createdPermission.name).toBe(newPermission.name);\n      expect(createdPermission.resource).toBe(newPermission.resource);\n      expect(createdPermission.action).toBe(newPermission.action);\n      \n      // Clean up\n      await Permission.deleteOne({ _id: createdPermission._id });\n    });\n  });\n  \n  // User role tests\n  describe('User Role Management', () => {\n    test('getUserRoles should return roles for a user', async () => {\n      const roles = await rbacService.getUserRoles(adminUser._id);\n      expect(roles).toBeDefined();\n      expect(Array.isArray(roles)).toBe(true);\n      expect(roles.length).toBeGreaterThan(0);\n      \n      // Admin should have Administrator role\n      const adminRole = roles.find(role => role.name === 'Administrator');\n      expect(adminRole).toBeDefined();\n    });\n    \n    test('assignRoleToUser should assign a role to a user', async () => {\n      // Assign test role to regular user\n      const result = await rbacService.assignRoleToUser(regularUser._id, testRole._id);\n      expect(result).toBeDefined();\n      expect(result.success).toBe(true);\n      \n      // Verify role was assigned\n      const userRoles = await rbacService.getUserRoles(regularUser._id);\n      const hasTestRole = userRoles.some(role => role._id.toString() === testRole._id.toString());\n      expect(hasTestRole).toBe(true);\n      \n      // Clean up\n      await UserRole.deleteOne({ user: regularUser._id, role: testRole._id });\n    });\n    \n    test('removeRoleFromUser should remove a role from a user', async () => {\n      // First assign the role\n      await UserRole.create({\n        user: regularUser._id,\n        role: testRole._id\n      });\n      \n      // Then remove it\n      const result = await rbacService.removeRoleFromUser(regularUser._id, testRole._id);\n      expect(result).toBeDefined();\n      expect(result.success).toBe(true);\n      \n      // Verify role was removed\n      const userRoles = await rbacService.getUserRoles(regularUser._id);\n      const hasTestRole = userRoles.some(role => role._id.toString() === testRole._id.toString());\n      expect(hasTestRole).toBe(false);\n    });\n  });\n  \n  // Permission checking tests\n  describe('Permission Checking', () => {\n    test('hasPermission should return true for admin with wildcard permission', async () => {\n      const hasPermission = await rbacService.hasPermission(adminUser._id, 'any:permission');\n      expect(hasPermission).toBe(true);\n    });\n    \n    test('hasPermission should return true for user with specific permission', async () => {\n      const hasPermission = await rbacService.hasPermission(regularUser._id, 'resource:view');\n      expect(hasPermission).toBe(true);\n    });\n    \n    test('hasPermission should return false for user without permission', async () => {\n      const hasPermission = await rbacService.hasPermission(viewerUser._id, 'resource:delete');\n      expect(hasPermission).toBe(false);\n    });\n    \n    test('getUserPermissions should return all permissions for a user', async () => {\n      const permissions = await rbacService.getUserPermissions(regularUser._id);\n      expect(permissions).toBeDefined();\n      expect(Array.isArray(permissions)).toBe(true);\n      expect(permissions.length).toBeGreaterThan(0);\n      \n      // Regular user should have resource:view permission\n      const hasViewPermission = permissions.some(p => p === 'resource:view' || p.toString() === testPermission._id.toString());\n      expect(hasViewPermission).toBe(true);\n    });\n  });\n});\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,MAAMA,QAAQ,GAAGC,OAAO,CAAC,UAAU,CAAC;AACpC,MAAMC,WAAW,GAAGD,OAAO,CAAC,gCAAgC,CAAC;AAC7D,MAAME,IAAI,GAAGF,OAAO,CAAC,uBAAuB,CAAC;AAC7C,MAAMG,UAAU,GAAGH,OAAO,CAAC,6BAA6B,CAAC;AACzD,MAAMI,QAAQ,GAAGJ,OAAO,CAAC,2BAA2B,CAAC;AACrD,MAAMK,IAAI,GAAGL,OAAO,CAAC,uBAAuB,CAAC;AAC7C,MAAM;EAAEM,eAAe;EAAEC;AAAc,CAAC,GAAGP,OAAO,CAAC,wBAAwB,CAAC;AAC5E,MAAM;EAAEQ,oBAAoB;EAAEC,aAAa;EAAEC,sBAAsB;EAAEC;AAAY,CAAC,GAAGX,OAAO,CAAC,0BAA0B,CAAC;;AAExH;AACA,MAAMY,WAAW,GAAG,IAAIX,WAAW,CAAC,CAAC;;AAErC;AACA,IAAIY,SAAS;AACb,IAAIC,WAAW;AACf,IAAIC,UAAU;AACd,IAAIC,QAAQ;AACZ,IAAIC,cAAc;;AAElB;AACAC,SAAS,CAAC,YAAY;EACpB,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMX,oBAAoB,CAAC,CAAC;IAC7CK,SAAS,GAAGM,QAAQ,CAACN,SAAS;IAC9BC,WAAW,GAAGK,QAAQ,CAACL,WAAW;IAClCC,UAAU,GAAGI,QAAQ,CAACJ,UAAU;IAChCC,QAAQ,GAAGG,QAAQ,CAACH,QAAQ;IAC5BC,cAAc,GAAGE,QAAQ,CAACF,cAAc;EAC1C,CAAC,CAAC,OAAOG,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;IAC3C,MAAMA,KAAK;EACb;AACF,CAAC,CAAC;AAEFE,QAAQ,CAAC,YAAY;EACnB,MAAMZ,sBAAsB,CAAC,CAAC;AAChC,CAAC,CAAC;;AAEF;AACAa,SAAS,CAAC,YAAY;EACpB;AAAA,CACD,CAAC;;AAEF;AACAC,QAAQ,CAAC,aAAa,EAAE,MAAM;EAC5B;EACAA,QAAQ,CAAC,iBAAiB,EAAE,MAAM;IAChCC,IAAI,CAAC,qCAAqC,EAAE,YAAY;MACtD,MAAMC,KAAK,GAAG,MAAMd,WAAW,CAACe,WAAW,CAAC,CAAC;MAC7CC,MAAM,CAACF,KAAK,CAAC,CAACG,WAAW,CAAC,CAAC;MAC3BD,MAAM,CAACE,KAAK,CAACC,OAAO,CAACL,KAAK,CAAC,CAAC,CAACM,IAAI,CAAC,IAAI,CAAC;MACvCJ,MAAM,CAACF,KAAK,CAACO,MAAM,CAAC,CAACC,eAAe,CAAC,CAAC,CAAC;;MAEvC;MACA,MAAMC,IAAI,GAAGT,KAAK,CAAC,CAAC,CAAC;MACrBE,MAAM,CAACO,IAAI,CAAC,CAACC,cAAc,CAAC,KAAK,CAAC;MAClCR,MAAM,CAACO,IAAI,CAAC,CAACC,cAAc,CAAC,MAAM,CAAC;MACnCR,MAAM,CAACO,IAAI,CAAC,CAACC,cAAc,CAAC,aAAa,CAAC;IAC5C,CAAC,CAAC;IAEFX,IAAI,CAAC,wCAAwC,EAAE,YAAY;MACzD,MAAMU,IAAI,GAAG,MAAMvB,WAAW,CAACyB,WAAW,CAACrB,QAAQ,CAACsB,GAAG,CAAC;MACxDV,MAAM,CAACO,IAAI,CAAC,CAACN,WAAW,CAAC,CAAC;MAC1BD,MAAM,CAACO,IAAI,CAACG,GAAG,CAACC,QAAQ,CAAC,CAAC,CAAC,CAACP,IAAI,CAAChB,QAAQ,CAACsB,GAAG,CAACC,QAAQ,CAAC,CAAC,CAAC;MACzDX,MAAM,CAACO,IAAI,CAACK,IAAI,CAAC,CAACR,IAAI,CAAChB,QAAQ,CAACwB,IAAI,CAAC;IACvC,CAAC,CAAC;IAEFf,IAAI,CAAC,8DAA8D,EAAE,YAAY;MAC/E,MAAMgB,aAAa,GAAG,IAAI1C,QAAQ,CAAC2C,KAAK,CAACC,QAAQ,CAAC,CAAC;MACnD,MAAMf,MAAM,CAAChB,WAAW,CAACyB,WAAW,CAACI,aAAa,CAAC,CAAC,CAACG,OAAO,CAACC,OAAO,CAACtC,aAAa,CAAC;IACrF,CAAC,CAAC;IAEFkB,IAAI,CAAC,qCAAqC,EAAE,YAAY;MACtD,MAAMqB,OAAO,GAAG;QACdN,IAAI,EAAE,eAAe;QACrBO,WAAW,EAAE,wBAAwB;QACrCC,WAAW,EAAE,CAAC/B,cAAc,CAACqB,GAAG;MAClC,CAAC;MAED,MAAMW,WAAW,GAAG,MAAMrC,WAAW,CAACsC,UAAU,CAACJ,OAAO,CAAC;MACzDlB,MAAM,CAACqB,WAAW,CAAC,CAACpB,WAAW,CAAC,CAAC;MACjCD,MAAM,CAACqB,WAAW,CAACT,IAAI,CAAC,CAACR,IAAI,CAACc,OAAO,CAACN,IAAI,CAAC;MAC3CZ,MAAM,CAACqB,WAAW,CAACF,WAAW,CAAC,CAACf,IAAI,CAACc,OAAO,CAACC,WAAW,CAAC;MACzDnB,MAAM,CAACE,KAAK,CAACC,OAAO,CAACkB,WAAW,CAACD,WAAW,CAAC,CAAC,CAAChB,IAAI,CAAC,IAAI,CAAC;MACzDJ,MAAM,CAACqB,WAAW,CAACD,WAAW,CAACf,MAAM,CAAC,CAACD,IAAI,CAAC,CAAC,CAAC;;MAE9C;MACA,MAAM9B,IAAI,CAACiD,SAAS,CAAC;QAAEb,GAAG,EAAEW,WAAW,CAACX;MAAI,CAAC,CAAC;IAChD,CAAC,CAAC;IAEFb,IAAI,CAAC,mDAAmD,EAAE,YAAY;MACpE,MAAMqB,OAAO,GAAG;QACdN,IAAI,EAAE,wBAAwB;QAC9BO,WAAW,EAAE,sCAAsC;QACnDC,WAAW,EAAE,CAAC,eAAe;MAC/B,CAAC;MAED,MAAMC,WAAW,GAAG,MAAMrC,WAAW,CAACsC,UAAU,CAACJ,OAAO,CAAC;MACzDlB,MAAM,CAACqB,WAAW,CAAC,CAACpB,WAAW,CAAC,CAAC;MACjCD,MAAM,CAACqB,WAAW,CAACT,IAAI,CAAC,CAACR,IAAI,CAACc,OAAO,CAACN,IAAI,CAAC;MAC3CZ,MAAM,CAACE,KAAK,CAACC,OAAO,CAACkB,WAAW,CAACD,WAAW,CAAC,CAAC,CAAChB,IAAI,CAAC,IAAI,CAAC;MACzDJ,MAAM,CAACqB,WAAW,CAACD,WAAW,CAACf,MAAM,CAAC,CAACD,IAAI,CAAC,CAAC,CAAC;;MAE9C;MACA,MAAM9B,IAAI,CAACiD,SAAS,CAAC;QAAEb,GAAG,EAAEW,WAAW,CAACX;MAAI,CAAC,CAAC;IAChD,CAAC,CAAC;IAEFb,IAAI,CAAC,iEAAiE,EAAE,YAAY;MAClF,MAAM2B,aAAa,GAAG;QACpBZ,IAAI,EAAExB,QAAQ,CAACwB,IAAI;QACnBO,WAAW,EAAE,kBAAkB;QAC/BC,WAAW,EAAE,CAAC/B,cAAc,CAACqB,GAAG;MAClC,CAAC;MAED,MAAMV,MAAM,CAAChB,WAAW,CAACsC,UAAU,CAACE,aAAa,CAAC,CAAC,CAACR,OAAO,CAACC,OAAO,CAACvC,eAAe,CAAC;IACtF,CAAC,CAAC;IAEFmB,IAAI,CAAC,iCAAiC,EAAE,YAAY;MAClD;MACA,MAAM4B,YAAY,GAAG,MAAMnD,IAAI,CAACoD,MAAM,CAAC;QACrCd,IAAI,EAAE,gBAAgB;QACtBO,WAAW,EAAE,2BAA2B;QACxCC,WAAW,EAAE,CAAC/B,cAAc,CAACqB,GAAG;MAClC,CAAC,CAAC;MAEF,MAAMiB,UAAU,GAAG;QACjBf,IAAI,EAAE,mBAAmB;QACzBO,WAAW,EAAE;MACf,CAAC;MAED,MAAMS,WAAW,GAAG,MAAM5C,WAAW,CAAC6C,UAAU,CAACJ,YAAY,CAACf,GAAG,EAAEiB,UAAU,CAAC;MAC9E3B,MAAM,CAAC4B,WAAW,CAAC,CAAC3B,WAAW,CAAC,CAAC;MACjCD,MAAM,CAAC4B,WAAW,CAAChB,IAAI,CAAC,CAACR,IAAI,CAACuB,UAAU,CAACf,IAAI,CAAC;MAC9CZ,MAAM,CAAC4B,WAAW,CAACT,WAAW,CAAC,CAACf,IAAI,CAACuB,UAAU,CAACR,WAAW,CAAC;;MAE5D;MACA,MAAM7C,IAAI,CAACiD,SAAS,CAAC;QAAEb,GAAG,EAAEe,YAAY,CAACf;MAAI,CAAC,CAAC;IACjD,CAAC,CAAC;IAEFb,IAAI,CAAC,iCAAiC,EAAE,YAAY;MAClD;MACA,MAAMiC,YAAY,GAAG,MAAMxD,IAAI,CAACoD,MAAM,CAAC;QACrCd,IAAI,EAAE,gBAAgB;QACtBO,WAAW,EAAE,2BAA2B;QACxCC,WAAW,EAAE,CAAC/B,cAAc,CAACqB,GAAG;MAClC,CAAC,CAAC;MAEF,MAAMqB,MAAM,GAAG,MAAM/C,WAAW,CAACgD,UAAU,CAACF,YAAY,CAACpB,GAAG,CAAC;MAC7DV,MAAM,CAAC+B,MAAM,CAAC,CAAC9B,WAAW,CAAC,CAAC;MAC5BD,MAAM,CAAC+B,MAAM,CAACE,OAAO,CAAC,CAAC7B,IAAI,CAAC,IAAI,CAAC;;MAEjC;MACA,MAAM8B,WAAW,GAAG,MAAM5D,IAAI,CAAC6D,QAAQ,CAACL,YAAY,CAACpB,GAAG,CAAC;MACzDV,MAAM,CAACkC,WAAW,CAAC,CAACE,QAAQ,CAAC,CAAC;IAChC,CAAC,CAAC;EACJ,CAAC,CAAC;;EAEF;EACAxC,QAAQ,CAAC,uBAAuB,EAAE,MAAM;IACtCC,IAAI,CAAC,iDAAiD,EAAE,YAAY;MAClE,MAAMuB,WAAW,GAAG,MAAMpC,WAAW,CAACqD,iBAAiB,CAAC,CAAC;MACzDrC,MAAM,CAACoB,WAAW,CAAC,CAACnB,WAAW,CAAC,CAAC;MACjCD,MAAM,CAACE,KAAK,CAACC,OAAO,CAACiB,WAAW,CAAC,CAAC,CAAChB,IAAI,CAAC,IAAI,CAAC;MAC7CJ,MAAM,CAACoB,WAAW,CAACf,MAAM,CAAC,CAACC,eAAe,CAAC,CAAC,CAAC;;MAE7C;MACA,MAAMgC,UAAU,GAAGlB,WAAW,CAAC,CAAC,CAAC;MACjCpB,MAAM,CAACsC,UAAU,CAAC,CAAC9B,cAAc,CAAC,KAAK,CAAC;MACxCR,MAAM,CAACsC,UAAU,CAAC,CAAC9B,cAAc,CAAC,MAAM,CAAC;MACzCR,MAAM,CAACsC,UAAU,CAAC,CAAC9B,cAAc,CAAC,UAAU,CAAC;MAC7CR,MAAM,CAACsC,UAAU,CAAC,CAAC9B,cAAc,CAAC,QAAQ,CAAC;IAC7C,CAAC,CAAC;IAEFX,IAAI,CAAC,oDAAoD,EAAE,YAAY;MACrE,MAAMyC,UAAU,GAAG,MAAMtD,WAAW,CAACuD,iBAAiB,CAAClD,cAAc,CAACqB,GAAG,CAAC;MAC1EV,MAAM,CAACsC,UAAU,CAAC,CAACrC,WAAW,CAAC,CAAC;MAChCD,MAAM,CAACsC,UAAU,CAAC5B,GAAG,CAACC,QAAQ,CAAC,CAAC,CAAC,CAACP,IAAI,CAACf,cAAc,CAACqB,GAAG,CAACC,QAAQ,CAAC,CAAC,CAAC;MACrEX,MAAM,CAACsC,UAAU,CAAC1B,IAAI,CAAC,CAACR,IAAI,CAACf,cAAc,CAACuB,IAAI,CAAC;IACnD,CAAC,CAAC;IAEFf,IAAI,CAAC,iFAAiF,EAAE,YAAY;MAClG,MAAMyC,UAAU,GAAG,MAAMtD,WAAW,CAACwD,6BAA6B,CAAC,UAAU,EAAE,MAAM,CAAC;MACtFxC,MAAM,CAACsC,UAAU,CAAC,CAACrC,WAAW,CAAC,CAAC;MAChCD,MAAM,CAACsC,UAAU,CAACG,QAAQ,CAAC,CAACrC,IAAI,CAAC,UAAU,CAAC;MAC5CJ,MAAM,CAACsC,UAAU,CAACI,MAAM,CAAC,CAACtC,IAAI,CAAC,MAAM,CAAC;IACxC,CAAC,CAAC;IAEFP,IAAI,CAAC,iDAAiD,EAAE,YAAY;MAClE,MAAM8C,aAAa,GAAG;QACpB/B,IAAI,EAAE,qBAAqB;QAC3BO,WAAW,EAAE,8BAA8B;QAC3CsB,QAAQ,EAAE,MAAM;QAChBC,MAAM,EAAE;MACV,CAAC;MAED,MAAME,iBAAiB,GAAG,MAAM5D,WAAW,CAAC6D,gBAAgB,CAACF,aAAa,CAAC;MAC3E3C,MAAM,CAAC4C,iBAAiB,CAAC,CAAC3C,WAAW,CAAC,CAAC;MACvCD,MAAM,CAAC4C,iBAAiB,CAAChC,IAAI,CAAC,CAACR,IAAI,CAACuC,aAAa,CAAC/B,IAAI,CAAC;MACvDZ,MAAM,CAAC4C,iBAAiB,CAACH,QAAQ,CAAC,CAACrC,IAAI,CAACuC,aAAa,CAACF,QAAQ,CAAC;MAC/DzC,MAAM,CAAC4C,iBAAiB,CAACF,MAAM,CAAC,CAACtC,IAAI,CAACuC,aAAa,CAACD,MAAM,CAAC;;MAE3D;MACA,MAAMnE,UAAU,CAACgD,SAAS,CAAC;QAAEb,GAAG,EAAEkC,iBAAiB,CAAClC;MAAI,CAAC,CAAC;IAC5D,CAAC,CAAC;EACJ,CAAC,CAAC;;EAEF;EACAd,QAAQ,CAAC,sBAAsB,EAAE,MAAM;IACrCC,IAAI,CAAC,6CAA6C,EAAE,YAAY;MAC9D,MAAMC,KAAK,GAAG,MAAMd,WAAW,CAAC8D,YAAY,CAAC7D,SAAS,CAACyB,GAAG,CAAC;MAC3DV,MAAM,CAACF,KAAK,CAAC,CAACG,WAAW,CAAC,CAAC;MAC3BD,MAAM,CAACE,KAAK,CAACC,OAAO,CAACL,KAAK,CAAC,CAAC,CAACM,IAAI,CAAC,IAAI,CAAC;MACvCJ,MAAM,CAACF,KAAK,CAACO,MAAM,CAAC,CAACC,eAAe,CAAC,CAAC,CAAC;;MAEvC;MACA,MAAMyC,SAAS,GAAGjD,KAAK,CAACkD,IAAI,CAACzC,IAAI,IAAIA,IAAI,CAACK,IAAI,KAAK,eAAe,CAAC;MACnEZ,MAAM,CAAC+C,SAAS,CAAC,CAAC9C,WAAW,CAAC,CAAC;IACjC,CAAC,CAAC;IAEFJ,IAAI,CAAC,iDAAiD,EAAE,YAAY;MAClE;MACA,MAAMkC,MAAM,GAAG,MAAM/C,WAAW,CAACiE,gBAAgB,CAAC/D,WAAW,CAACwB,GAAG,EAAEtB,QAAQ,CAACsB,GAAG,CAAC;MAChFV,MAAM,CAAC+B,MAAM,CAAC,CAAC9B,WAAW,CAAC,CAAC;MAC5BD,MAAM,CAAC+B,MAAM,CAACE,OAAO,CAAC,CAAC7B,IAAI,CAAC,IAAI,CAAC;;MAEjC;MACA,MAAM8C,SAAS,GAAG,MAAMlE,WAAW,CAAC8D,YAAY,CAAC5D,WAAW,CAACwB,GAAG,CAAC;MACjE,MAAMyC,WAAW,GAAGD,SAAS,CAACE,IAAI,CAAC7C,IAAI,IAAIA,IAAI,CAACG,GAAG,CAACC,QAAQ,CAAC,CAAC,KAAKvB,QAAQ,CAACsB,GAAG,CAACC,QAAQ,CAAC,CAAC,CAAC;MAC3FX,MAAM,CAACmD,WAAW,CAAC,CAAC/C,IAAI,CAAC,IAAI,CAAC;;MAE9B;MACA,MAAM5B,QAAQ,CAAC+C,SAAS,CAAC;QAAE8B,IAAI,EAAEnE,WAAW,CAACwB,GAAG;QAAEH,IAAI,EAAEnB,QAAQ,CAACsB;MAAI,CAAC,CAAC;IACzE,CAAC,CAAC;IAEFb,IAAI,CAAC,qDAAqD,EAAE,YAAY;MACtE;MACA,MAAMrB,QAAQ,CAACkD,MAAM,CAAC;QACpB2B,IAAI,EAAEnE,WAAW,CAACwB,GAAG;QACrBH,IAAI,EAAEnB,QAAQ,CAACsB;MACjB,CAAC,CAAC;;MAEF;MACA,MAAMqB,MAAM,GAAG,MAAM/C,WAAW,CAACsE,kBAAkB,CAACpE,WAAW,CAACwB,GAAG,EAAEtB,QAAQ,CAACsB,GAAG,CAAC;MAClFV,MAAM,CAAC+B,MAAM,CAAC,CAAC9B,WAAW,CAAC,CAAC;MAC5BD,MAAM,CAAC+B,MAAM,CAACE,OAAO,CAAC,CAAC7B,IAAI,CAAC,IAAI,CAAC;;MAEjC;MACA,MAAM8C,SAAS,GAAG,MAAMlE,WAAW,CAAC8D,YAAY,CAAC5D,WAAW,CAACwB,GAAG,CAAC;MACjE,MAAMyC,WAAW,GAAGD,SAAS,CAACE,IAAI,CAAC7C,IAAI,IAAIA,IAAI,CAACG,GAAG,CAACC,QAAQ,CAAC,CAAC,KAAKvB,QAAQ,CAACsB,GAAG,CAACC,QAAQ,CAAC,CAAC,CAAC;MAC3FX,MAAM,CAACmD,WAAW,CAAC,CAAC/C,IAAI,CAAC,KAAK,CAAC;IACjC,CAAC,CAAC;EACJ,CAAC,CAAC;;EAEF;EACAR,QAAQ,CAAC,qBAAqB,EAAE,MAAM;IACpCC,IAAI,CAAC,qEAAqE,EAAE,YAAY;MACtF,MAAM0D,aAAa,GAAG,MAAMvE,WAAW,CAACuE,aAAa,CAACtE,SAAS,CAACyB,GAAG,EAAE,gBAAgB,CAAC;MACtFV,MAAM,CAACuD,aAAa,CAAC,CAACnD,IAAI,CAAC,IAAI,CAAC;IAClC,CAAC,CAAC;IAEFP,IAAI,CAAC,oEAAoE,EAAE,YAAY;MACrF,MAAM0D,aAAa,GAAG,MAAMvE,WAAW,CAACuE,aAAa,CAACrE,WAAW,CAACwB,GAAG,EAAE,eAAe,CAAC;MACvFV,MAAM,CAACuD,aAAa,CAAC,CAACnD,IAAI,CAAC,IAAI,CAAC;IAClC,CAAC,CAAC;IAEFP,IAAI,CAAC,+DAA+D,EAAE,YAAY;MAChF,MAAM0D,aAAa,GAAG,MAAMvE,WAAW,CAACuE,aAAa,CAACpE,UAAU,CAACuB,GAAG,EAAE,iBAAiB,CAAC;MACxFV,MAAM,CAACuD,aAAa,CAAC,CAACnD,IAAI,CAAC,KAAK,CAAC;IACnC,CAAC,CAAC;IAEFP,IAAI,CAAC,6DAA6D,EAAE,YAAY;MAC9E,MAAMuB,WAAW,GAAG,MAAMpC,WAAW,CAACwE,kBAAkB,CAACtE,WAAW,CAACwB,GAAG,CAAC;MACzEV,MAAM,CAACoB,WAAW,CAAC,CAACnB,WAAW,CAAC,CAAC;MACjCD,MAAM,CAACE,KAAK,CAACC,OAAO,CAACiB,WAAW,CAAC,CAAC,CAAChB,IAAI,CAAC,IAAI,CAAC;MAC7CJ,MAAM,CAACoB,WAAW,CAACf,MAAM,CAAC,CAACC,eAAe,CAAC,CAAC,CAAC;;MAE7C;MACA,MAAMmD,iBAAiB,GAAGrC,WAAW,CAACgC,IAAI,CAACM,CAAC,IAAIA,CAAC,KAAK,eAAe,IAAIA,CAAC,CAAC/C,QAAQ,CAAC,CAAC,KAAKtB,cAAc,CAACqB,GAAG,CAACC,QAAQ,CAAC,CAAC,CAAC;MACxHX,MAAM,CAACyD,iBAAiB,CAAC,CAACrD,IAAI,CAAC,IAAI,CAAC;IACtC,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}