/**
 * OpenID Connect Authentication Service
 * 
 * This service handles OIDC-based authentication for SSO.
 */

const fs = require('fs').promises;
const path = require('path');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const { ValidationError, AuthenticationError } = require('../utils/errors');
const IdentityProviderService = require('./IdentityProviderService');
const UserService = require('./UserService');
const AuditService = require('./AuditService');

class OidcAuthService {
  constructor(dataDir = path.join(__dirname, '../data')) {
    this.dataDir = dataDir;
    this.stateFile = path.join(this.dataDir, 'oidc_states.json');
    this.identityProviderService = new IdentityProviderService(dataDir);
    this.userService = new UserService(dataDir);
    this.auditService = new AuditService(dataDir);
    this.jwtSecret = process.env.JWT_SECRET || 'your-secret-key';
    this.jwtExpiresIn = process.env.JWT_EXPIRES_IN || '1d';
    this.ensureDataDir();
  }

  /**
   * Ensure the data directory exists
   */
  async ensureDataDir() {
    try {
      await fs.mkdir(this.dataDir, { recursive: true });
      
      // Initialize files if they don't exist
      try {
        await fs.access(this.stateFile);
      } catch (error) {
        if (error.code === 'ENOENT') {
          // File doesn't exist, create it with empty object
          await fs.writeFile(this.stateFile, JSON.stringify({}));
        } else {
          throw error;
        }
      }
    } catch (error) {
      console.error('Error creating data directory:', error);
      throw error;
    }
  }

  /**
   * Generate OIDC authorization URL
   */
  async generateAuthUrl(providerId, redirectUri, nonce = null) {
    try {
      // Get provider
      const provider = await this.identityProviderService.getProviderById(providerId);
      
      if (provider.type !== 'oidc') {
        throw new ValidationError('Provider is not an OIDC provider');
      }
      
      if (!provider.enabled) {
        throw new ValidationError('Provider is disabled');
      }
      
      // In a real implementation, this would fetch the OIDC configuration
      // from the discovery URL and generate an authorization URL
      
      // Generate state parameter for CSRF protection
      const state = crypto.randomBytes(32).toString('hex');
      
      // Generate nonce parameter for replay protection if not provided
      if (!nonce) {
        nonce = crypto.randomBytes(16).toString('hex');
      }
      
      // Store state and nonce for validation
      await this.saveState(state, {
        providerId,
        redirectUri,
        nonce,
        created: new Date().toISOString()
      });
      
      // Build authorization URL
      const params = new URLSearchParams({
        client_id: provider.clientId,
        response_type: provider.responseType || 'code',
        scope: provider.scope || 'openid profile email',
        redirect_uri: redirectUri,
        state,
        nonce
      });
      
      // In a real implementation, this would use the authorization_endpoint from the discovery document
      const authUrl = `https://example.com/oauth2/authorize?${params.toString()}`;
      
      return {
        providerId,
        authUrl,
        state,
        nonce
      };
    } catch (error) {
      console.error('Error generating OIDC authorization URL:', error);
      throw error;
    }
  }

  /**
   * Process OIDC callback
   */
  async processCallback(code, state, redirectUri) {
    try {
      // Validate state parameter
      const stateData = await this.getState(state);
      
      if (!stateData) {
        throw new ValidationError('Invalid state parameter');
      }
      
      // Get provider
      const provider = await this.identityProviderService.getProviderById(stateData.providerId);
      
      if (provider.type !== 'oidc') {
        throw new ValidationError('Provider is not an OIDC provider');
      }
      
      if (!provider.enabled) {
        throw new ValidationError('Provider is disabled');
      }
      
      // In a real implementation, this would exchange the authorization code for tokens
      // and validate the ID token
      
      // For now, just simulate a successful token exchange
      const tokens = {
        access_token: 'dummy-access-token',
        id_token: 'dummy-id-token',
        refresh_token: 'dummy-refresh-token',
        token_type: 'Bearer',
        expires_in: 3600
      };
      
      // In a real implementation, this would decode and validate the ID token
      // and fetch user information from the userinfo endpoint if necessary
      
      // For now, just simulate user information
      const userInfo = {
        sub: 'user123',
        email: '<EMAIL>',
        email_verified: true,
        name: 'John Doe',
        given_name: 'John',
        family_name: 'Doe',
        preferred_username: 'johndoe',
        groups: ['users', 'developers']
      };
      
      // Map OIDC claims to user properties using provider's attribute mapping
      const userData = {
        email: userInfo[provider.attributeMapping.email] || userInfo.email,
        firstName: userInfo[provider.attributeMapping.firstName] || userInfo.given_name,
        lastName: userInfo[provider.attributeMapping.lastName] || userInfo.family_name,
        username: userInfo[provider.attributeMapping.username] || userInfo.preferred_username
      };
      
      // Find or create user
      let user = await this.userService.getUserByEmail(userData.email);
      
      if (!user) {
        // Create new user
        user = await this.userService.createUser({
          email: userData.email,
          firstName: userData.firstName,
          lastName: userData.lastName,
          username: userData.username,
          authProvider: 'oidc',
          authProviderId: provider.id
        });
      } else {
        // Update existing user
        user = await this.userService.updateUser(user.id, {
          firstName: userData.firstName,
          lastName: userData.lastName,
          username: userData.username,
          authProvider: 'oidc',
          authProviderId: provider.id
        });
      }
      
      // Generate JWT token
      const token = jwt.sign(
        {
          id: user.id,
          email: user.email,
          username: user.username,
          firstName: user.firstName,
          lastName: user.lastName,
          authProvider: 'oidc',
          authProviderId: provider.id
        },
        this.jwtSecret,
        { expiresIn: this.jwtExpiresIn }
      );
      
      // Clean up state
      await this.deleteState(state);
      
      // Log audit event
      await this.auditService.logEvent({
        userId: user.id,
        action: 'LOGIN',
        resourceType: 'user',
        resourceId: user.id,
        details: {
          method: 'oidc',
          providerId: provider.id,
          providerName: provider.name
        }
      });
      
      return {
        token,
        user: {
          id: user.id,
          email: user.email,
          username: user.username,
          firstName: user.firstName,
          lastName: user.lastName
        },
        tokens
      };
    } catch (error) {
      console.error('Error processing OIDC callback:', error);
      throw error;
    }
  }

  /**
   * Refresh OIDC tokens
   */
  async refreshTokens(refreshToken, providerId) {
    try {
      // Get provider
      const provider = await this.identityProviderService.getProviderById(providerId);
      
      if (provider.type !== 'oidc') {
        throw new ValidationError('Provider is not an OIDC provider');
      }
      
      if (!provider.enabled) {
        throw new ValidationError('Provider is disabled');
      }
      
      // In a real implementation, this would exchange the refresh token for new tokens
      
      // For now, just simulate a successful token refresh
      const tokens = {
        access_token: 'new-dummy-access-token',
        id_token: 'new-dummy-id-token',
        refresh_token: 'new-dummy-refresh-token',
        token_type: 'Bearer',
        expires_in: 3600
      };
      
      return tokens;
    } catch (error) {
      console.error('Error refreshing OIDC tokens:', error);
      throw error;
    }
  }

  /**
   * Logout from OIDC provider
   */
  async logout(userId, providerId, idToken, postLogoutRedirectUri) {
    try {
      // Get provider
      const provider = await this.identityProviderService.getProviderById(providerId);
      
      if (provider.type !== 'oidc') {
        throw new ValidationError('Provider is not an OIDC provider');
      }
      
      if (!provider.enabled) {
        throw new ValidationError('Provider is disabled');
      }
      
      // In a real implementation, this would fetch the OIDC configuration
      // from the discovery URL and generate a logout URL
      
      // For now, just simulate a logout URL
      const params = new URLSearchParams({
        id_token_hint: idToken,
        post_logout_redirect_uri: postLogoutRedirectUri
      });
      
      // In a real implementation, this would use the end_session_endpoint from the discovery document
      const logoutUrl = `https://example.com/oauth2/logout?${params.toString()}`;
      
      // Log audit event
      await this.auditService.logEvent({
        userId,
        action: 'LOGOUT',
        resourceType: 'user',
        resourceId: userId,
        details: {
          method: 'oidc',
          providerId: provider.id,
          providerName: provider.name
        }
      });
      
      return {
        providerId,
        logoutUrl
      };
    } catch (error) {
      console.error('Error logging out from OIDC provider:', error);
      throw error;
    }
  }

  /**
   * Save state data
   */
  async saveState(state, data) {
    try {
      const states = await this.loadStates();
      states[state] = data;
      await this.saveStates(states);
    } catch (error) {
      console.error('Error saving state:', error);
      throw error;
    }
  }

  /**
   * Get state data
   */
  async getState(state) {
    try {
      const states = await this.loadStates();
      return states[state] || null;
    } catch (error) {
      console.error('Error getting state:', error);
      throw error;
    }
  }

  /**
   * Delete state data
   */
  async deleteState(state) {
    try {
      const states = await this.loadStates();
      delete states[state];
      await this.saveStates(states);
    } catch (error) {
      console.error('Error deleting state:', error);
      throw error;
    }
  }

  /**
   * Load states from file
   */
  async loadStates() {
    try {
      const data = await fs.readFile(this.stateFile, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      if (error.code === 'ENOENT') {
        // File doesn't exist, return empty object
        return {};
      }
      console.error('Error loading states:', error);
      throw error;
    }
  }

  /**
   * Save states to file
   */
  async saveStates(states) {
    try {
      await fs.writeFile(this.stateFile, JSON.stringify(states, null, 2));
    } catch (error) {
      console.error('Error saving states:', error);
      throw error;
    }
  }

  /**
   * Clean up expired states
   */
  async cleanupExpiredStates() {
    try {
      const states = await this.loadStates();
      const now = new Date();
      let count = 0;
      
      // Remove states older than 1 hour
      for (const [state, data] of Object.entries(states)) {
        const created = new Date(data.created);
        if (now - created > 3600000) { // 1 hour in milliseconds
          delete states[state];
          count++;
        }
      }
      
      if (count > 0) {
        await this.saveStates(states);
      }
      
      return { success: true, message: `Cleaned up ${count} expired states` };
    } catch (error) {
      console.error('Error cleaning up expired states:', error);
      throw error;
    }
  }
}

module.exports = OidcAuthService;
