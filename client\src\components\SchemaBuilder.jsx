import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { saveSchema } from '../services/schemaService';

/**
 * Schema Builder Component
 * 
 * A component for building form schemas visually.
 * 
 * @param {Object} props - Component props
 * @param {Object} props.initialSchema - Initial schema
 * @param {Function} props.onSave - Save callback
 * @param {Function} props.onCancel - Cancel callback
 */
const SchemaBuilder = ({ initialSchema = null, onSave, onCancel }) => {
  const [schema, setSchema] = useState(initialSchema || {
    entity: '',
    entityPlural: '',
    apiEndpoint: '',
    fields: [],
    submitLabel: 'Submit'
  });
  
  const [currentField, setCurrentField] = useState({
    name: '',
    label: '',
    type: 'text',
    required: false,
    description: '',
    placeholder: '',
    options: []
  });
  
  const [currentOption, setCurrentOption] = useState({
    label: '',
    value: ''
  });
  
  const [editingFieldIndex, setEditingFieldIndex] = useState(-1);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  
  // Handle schema property change
  const handleSchemaChange = (e) => {
    const { name, value } = e.target;
    setSchema({
      ...schema,
      [name]: value
    });
  };
  
  // Handle field property change
  const handleFieldChange = (e) => {
    const { name, value, type, checked } = e.target;
    setCurrentField({
      ...currentField,
      [name]: type === 'checkbox' ? checked : value
    });
  };
  
  // Handle option property change
  const handleOptionChange = (e) => {
    const { name, value } = e.target;
    setCurrentOption({
      ...currentOption,
      [name]: value
    });
  };
  
  // Add option to current field
  const handleAddOption = () => {
    if (!currentOption.label || !currentOption.value) {
      setError('Option label and value are required');
      return;
    }
    
    setCurrentField({
      ...currentField,
      options: [...(currentField.options || []), { ...currentOption }]
    });
    
    setCurrentOption({
      label: '',
      value: ''
    });
    
    setError('');
  };
  
  // Remove option from current field
  const handleRemoveOption = (index) => {
    setCurrentField({
      ...currentField,
      options: currentField.options.filter((_, i) => i !== index)
    });
  };
  
  // Add field to schema
  const handleAddField = () => {
    if (!currentField.name || !currentField.type) {
      setError('Field name and type are required');
      return;
    }
    
    // Check if field name already exists
    if (schema.fields.some(field => field.name === currentField.name) && editingFieldIndex === -1) {
      setError(`Field with name "${currentField.name}" already exists`);
      return;
    }
    
    let updatedFields;
    
    if (editingFieldIndex >= 0) {
      // Update existing field
      updatedFields = [...schema.fields];
      updatedFields[editingFieldIndex] = { ...currentField };
    } else {
      // Add new field
      updatedFields = [...schema.fields, { ...currentField }];
    }
    
    setSchema({
      ...schema,
      fields: updatedFields
    });
    
    // Reset current field
    setCurrentField({
      name: '',
      label: '',
      type: 'text',
      required: false,
      description: '',
      placeholder: '',
      options: []
    });
    
    setEditingFieldIndex(-1);
    setError('');
    setSuccess('Field added successfully');
    
    setTimeout(() => {
      setSuccess('');
    }, 3000);
  };
  
  // Edit field
  const handleEditField = (index) => {
    setCurrentField({ ...schema.fields[index] });
    setEditingFieldIndex(index);
  };
  
  // Remove field from schema
  const handleRemoveField = (index) => {
    setSchema({
      ...schema,
      fields: schema.fields.filter((_, i) => i !== index)
    });
  };
  
  // Move field up
  const handleMoveFieldUp = (index) => {
    if (index === 0) return;
    
    const updatedFields = [...schema.fields];
    const temp = updatedFields[index];
    updatedFields[index] = updatedFields[index - 1];
    updatedFields[index - 1] = temp;
    
    setSchema({
      ...schema,
      fields: updatedFields
    });
  };
  
  // Move field down
  const handleMoveFieldDown = (index) => {
    if (index === schema.fields.length - 1) return;
    
    const updatedFields = [...schema.fields];
    const temp = updatedFields[index];
    updatedFields[index] = updatedFields[index + 1];
    updatedFields[index + 1] = temp;
    
    setSchema({
      ...schema,
      fields: updatedFields
    });
  };
  
  // Save schema
  const handleSaveSchema = async () => {
    if (!schema.entity) {
      setError('Entity name is required');
      return;
    }
    
    if (schema.fields.length === 0) {
      setError('At least one field is required');
      return;
    }
    
    try {
      const response = await saveSchema(schema);
      setSuccess('Schema saved successfully');
      
      if (onSave) {
        onSave(response.data);
      }
    } catch (err) {
      setError(err.response?.data?.error || 'Failed to save schema');
    }
  };
  
  return (
    <div className="schema-builder">
      <h2>Schema Builder</h2>
      
      {error && (
        <div className="alert alert-danger">{error}</div>
      )}
      
      {success && (
        <div className="alert alert-success">{success}</div>
      )}
      
      <div className="card mb-4">
        <div className="card-header">
          <h3 className="card-title">Schema Properties</h3>
        </div>
        <div className="card-body">
          <div className="form-group">
            <label htmlFor="entity">Entity Name</label>
            <input
              type="text"
              id="entity"
              name="entity"
              value={schema.entity}
              onChange={handleSchemaChange}
              className="form-control"
              placeholder="e.g., User, Control, etc."
              required
            />
          </div>
          
          <div className="form-group">
            <label htmlFor="entityPlural">Entity Plural Name</label>
            <input
              type="text"
              id="entityPlural"
              name="entityPlural"
              value={schema.entityPlural}
              onChange={handleSchemaChange}
              className="form-control"
              placeholder="e.g., Users, Controls, etc."
            />
          </div>
          
          <div className="form-group">
            <label htmlFor="apiEndpoint">API Endpoint</label>
            <input
              type="text"
              id="apiEndpoint"
              name="apiEndpoint"
              value={schema.apiEndpoint}
              onChange={handleSchemaChange}
              className="form-control"
              placeholder="e.g., /api/v1/users"
            />
          </div>
          
          <div className="form-group">
            <label htmlFor="submitLabel">Submit Button Label</label>
            <input
              type="text"
              id="submitLabel"
              name="submitLabel"
              value={schema.submitLabel}
              onChange={handleSchemaChange}
              className="form-control"
              placeholder="e.g., Submit, Save, Create, etc."
            />
          </div>
        </div>
      </div>
      
      <div className="card mb-4">
        <div className="card-header">
          <h3 className="card-title">
            {editingFieldIndex >= 0 ? 'Edit Field' : 'Add Field'}
          </h3>
        </div>
        <div className="card-body">
          <div className="row">
            <div className="col-md-6">
              <div className="form-group">
                <label htmlFor="fieldName">Field Name</label>
                <input
                  type="text"
                  id="fieldName"
                  name="name"
                  value={currentField.name}
                  onChange={handleFieldChange}
                  className="form-control"
                  placeholder="e.g., email, firstName, etc."
                  required
                />
              </div>
            </div>
            
            <div className="col-md-6">
              <div className="form-group">
                <label htmlFor="fieldLabel">Field Label</label>
                <input
                  type="text"
                  id="fieldLabel"
                  name="label"
                  value={currentField.label}
                  onChange={handleFieldChange}
                  className="form-control"
                  placeholder="e.g., Email Address, First Name, etc."
                />
              </div>
            </div>
          </div>
          
          <div className="row">
            <div className="col-md-6">
              <div className="form-group">
                <label htmlFor="fieldType">Field Type</label>
                <select
                  id="fieldType"
                  name="type"
                  value={currentField.type}
                  onChange={handleFieldChange}
                  className="form-control"
                  required
                >
                  <option value="text">Text</option>
                  <option value="email">Email</option>
                  <option value="password">Password</option>
                  <option value="number">Number</option>
                  <option value="tel">Telephone</option>
                  <option value="url">URL</option>
                  <option value="textarea">Textarea</option>
                  <option value="select">Select</option>
                  <option value="checkbox">Checkbox</option>
                  <option value="radio">Radio</option>
                  <option value="date">Date</option>
                </select>
              </div>
            </div>
            
            <div className="col-md-6">
              <div className="form-group">
                <label htmlFor="fieldPlaceholder">Placeholder</label>
                <input
                  type="text"
                  id="fieldPlaceholder"
                  name="placeholder"
                  value={currentField.placeholder}
                  onChange={handleFieldChange}
                  className="form-control"
                  placeholder="e.g., Enter your email, etc."
                />
              </div>
            </div>
          </div>
          
          <div className="form-group">
            <label htmlFor="fieldDescription">Description</label>
            <input
              type="text"
              id="fieldDescription"
              name="description"
              value={currentField.description}
              onChange={handleFieldChange}
              className="form-control"
              placeholder="e.g., We'll never share your email, etc."
            />
          </div>
          
          <div className="form-check mb-3">
            <input
              type="checkbox"
              id="fieldRequired"
              name="required"
              checked={currentField.required}
              onChange={handleFieldChange}
              className="form-check-input"
            />
            <label className="form-check-label" htmlFor="fieldRequired">
              Required
            </label>
          </div>
          
          {(currentField.type === 'select' || currentField.type === 'radio') && (
            <div className="card mb-3">
              <div className="card-header">
                <h4 className="card-title">Options</h4>
              </div>
              <div className="card-body">
                <div className="row">
                  <div className="col-md-5">
                    <div className="form-group">
                      <label htmlFor="optionLabel">Option Label</label>
                      <input
                        type="text"
                        id="optionLabel"
                        name="label"
                        value={currentOption.label}
                        onChange={handleOptionChange}
                        className="form-control"
                        placeholder="e.g., Admin, User, etc."
                      />
                    </div>
                  </div>
                  
                  <div className="col-md-5">
                    <div className="form-group">
                      <label htmlFor="optionValue">Option Value</label>
                      <input
                        type="text"
                        id="optionValue"
                        name="value"
                        value={currentOption.value}
                        onChange={handleOptionChange}
                        className="form-control"
                        placeholder="e.g., admin, user, etc."
                      />
                    </div>
                  </div>
                  
                  <div className="col-md-2">
                    <div className="form-group">
                      <label>&nbsp;</label>
                      <button
                        type="button"
                        className="btn btn-primary btn-block"
                        onClick={handleAddOption}
                      >
                        Add
                      </button>
                    </div>
                  </div>
                </div>
                
                {currentField.options && currentField.options.length > 0 && (
                  <table className="table table-sm">
                    <thead>
                      <tr>
                        <th>Label</th>
                        <th>Value</th>
                        <th>Action</th>
                      </tr>
                    </thead>
                    <tbody>
                      {currentField.options.map((option, index) => (
                        <tr key={index}>
                          <td>{option.label}</td>
                          <td>{option.value}</td>
                          <td>
                            <button
                              type="button"
                              className="btn btn-sm btn-danger"
                              onClick={() => handleRemoveOption(index)}
                            >
                              Remove
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                )}
              </div>
            </div>
          )}
          
          <div className="form-group">
            <button
              type="button"
              className="btn btn-primary"
              onClick={handleAddField}
            >
              {editingFieldIndex >= 0 ? 'Update Field' : 'Add Field'}
            </button>
            
            {editingFieldIndex >= 0 && (
              <button
                type="button"
                className="btn btn-secondary ml-2"
                onClick={() => {
                  setCurrentField({
                    name: '',
                    label: '',
                    type: 'text',
                    required: false,
                    description: '',
                    placeholder: '',
                    options: []
                  });
                  setEditingFieldIndex(-1);
                }}
              >
                Cancel Edit
              </button>
            )}
          </div>
        </div>
      </div>
      
      <div className="card mb-4">
        <div className="card-header">
          <h3 className="card-title">Fields</h3>
        </div>
        <div className="card-body">
          {schema.fields.length === 0 ? (
            <div className="alert alert-info">No fields added yet</div>
          ) : (
            <table className="table">
              <thead>
                <tr>
                  <th>Name</th>
                  <th>Label</th>
                  <th>Type</th>
                  <th>Required</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {schema.fields.map((field, index) => (
                  <tr key={index}>
                    <td>{field.name}</td>
                    <td>{field.label}</td>
                    <td>{field.type}</td>
                    <td>{field.required ? 'Yes' : 'No'}</td>
                    <td>
                      <div className="btn-group">
                        <button
                          type="button"
                          className="btn btn-sm btn-primary"
                          onClick={() => handleEditField(index)}
                        >
                          Edit
                        </button>
                        <button
                          type="button"
                          className="btn btn-sm btn-danger"
                          onClick={() => handleRemoveField(index)}
                        >
                          Remove
                        </button>
                        <button
                          type="button"
                          className="btn btn-sm btn-secondary"
                          onClick={() => handleMoveFieldUp(index)}
                          disabled={index === 0}
                        >
                          ↑
                        </button>
                        <button
                          type="button"
                          className="btn btn-sm btn-secondary"
                          onClick={() => handleMoveFieldDown(index)}
                          disabled={index === schema.fields.length - 1}
                        >
                          ↓
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          )}
        </div>
      </div>
      
      <div className="form-group">
        <button
          type="button"
          className="btn btn-success"
          onClick={handleSaveSchema}
        >
          Save Schema
        </button>
        
        {onCancel && (
          <button
            type="button"
            className="btn btn-secondary ml-2"
            onClick={onCancel}
          >
            Cancel
          </button>
        )}
      </div>
    </div>
  );
};

SchemaBuilder.propTypes = {
  initialSchema: PropTypes.object,
  onSave: PropTypes.func,
  onCancel: PropTypes.func
};

export default SchemaBuilder;
