/**
 * HubSpot Integration Module
 * 
 * This module provides integration with HubSpot CRM for the Privacy Management API.
 * It allows exporting, deleting, and updating data in HubSpot.
 */

const axios = require('axios');
const logger = require('../logging');
const { recordIntegrationRequest } = require('../monitoring');

// HubSpot API base URL
const HUBSPOT_API_BASE_URL = 'https://api.hubapi.com';

/**
 * Get HubSpot client
 * @param {Object} config - HubSpot configuration
 * @returns {Object} - HubSpot client
 */
function getClient(config) {
  const { apiKey } = config;
  
  if (!apiKey) {
    throw new Error('HubSpot API key is required');
  }
  
  return axios.create({
    baseURL: HUBSPOT_API_BASE_URL,
    headers: {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json'
    }
  });
}

/**
 * Search for contacts by email
 * @param {Object} client - HubSpot client
 * @param {string} email - Email address
 * @returns {Promise<Array>} - List of contacts
 */
async function searchContactsByEmail(client, email) {
  try {
    const response = await client.get('/crm/v3/objects/contacts/search', {
      params: {
        filterGroups: JSON.stringify([
          {
            filters: [
              {
                propertyName: 'email',
                operator: 'EQ',
                value: email
              }
            ]
          }
        ])
      }
    });
    
    return response.data.results || [];
  } catch (error) {
    logger.error('Error searching HubSpot contacts', { email, error: error.message });
    throw error;
  }
}

/**
 * Get contact by ID
 * @param {Object} client - HubSpot client
 * @param {string} contactId - Contact ID
 * @param {Array} properties - Properties to include
 * @returns {Promise<Object>} - Contact data
 */
async function getContact(client, contactId, properties = []) {
  try {
    const response = await client.get(`/crm/v3/objects/contacts/${contactId}`, {
      params: {
        properties: properties.join(',')
      }
    });
    
    return response.data;
  } catch (error) {
    logger.error('Error getting HubSpot contact', { contactId, error: error.message });
    throw error;
  }
}

/**
 * Get deals associated with a contact
 * @param {Object} client - HubSpot client
 * @param {string} contactId - Contact ID
 * @returns {Promise<Array>} - List of deals
 */
async function getContactDeals(client, contactId) {
  try {
    const response = await client.get(`/crm/v3/objects/contacts/${contactId}/associations/deals`);
    
    const deals = [];
    for (const association of response.data.results) {
      const dealResponse = await client.get(`/crm/v3/objects/deals/${association.id}`);
      deals.push(dealResponse.data);
    }
    
    return deals;
  } catch (error) {
    logger.error('Error getting HubSpot contact deals', { contactId, error: error.message });
    throw error;
  }
}

/**
 * Get activities associated with a contact
 * @param {Object} client - HubSpot client
 * @param {string} contactId - Contact ID
 * @returns {Promise<Array>} - List of activities
 */
async function getContactActivities(client, contactId) {
  try {
    const response = await client.get(`/crm/v3/objects/contacts/${contactId}/associations/engagements`);
    
    const activities = [];
    for (const association of response.data.results) {
      const activityResponse = await client.get(`/crm/v3/objects/engagements/${association.id}`);
      activities.push(activityResponse.data);
    }
    
    return activities;
  } catch (error) {
    logger.error('Error getting HubSpot contact activities', { contactId, error: error.message });
    throw error;
  }
}

/**
 * Update contact properties
 * @param {Object} client - HubSpot client
 * @param {string} contactId - Contact ID
 * @param {Object} properties - Properties to update
 * @returns {Promise<Object>} - Updated contact
 */
async function updateContact(client, contactId, properties) {
  try {
    const response = await client.patch(`/crm/v3/objects/contacts/${contactId}`, {
      properties
    });
    
    return response.data;
  } catch (error) {
    logger.error('Error updating HubSpot contact', { contactId, error: error.message });
    throw error;
  }
}

/**
 * Delete contact
 * @param {Object} client - HubSpot client
 * @param {string} contactId - Contact ID
 * @returns {Promise<boolean>} - Whether the deletion was successful
 */
async function deleteContact(client, contactId) {
  try {
    await client.delete(`/crm/v3/objects/contacts/${contactId}`);
    return true;
  } catch (error) {
    logger.error('Error deleting HubSpot contact', { contactId, error: error.message });
    throw error;
  }
}

/**
 * Export data from HubSpot
 * @param {Object} config - HubSpot configuration
 * @param {Object} parameters - Export parameters
 * @returns {Promise<Object>} - Exported data
 */
async function exportData(config, parameters) {
  const startTime = Date.now();
  
  try {
    const { email, dataCategories = ['contacts', 'deals', 'activities'] } = parameters;
    
    if (!email) {
      throw new Error('Email is required for HubSpot data export');
    }
    
    const client = getClient(config);
    
    // Search for contacts by email
    const contacts = await searchContactsByEmail(client, email);
    
    if (contacts.length === 0) {
      return {
        contacts: [],
        deals: [],
        activities: []
      };
    }
    
    const result = {
      contacts: []
    };
    
    // Get contact details
    for (const contact of contacts) {
      const contactData = await getContact(client, contact.id, [
        'email',
        'firstname',
        'lastname',
        'phone',
        'address',
        'company',
        'website',
        'jobtitle',
        'lifecyclestage',
        'hs_lead_status',
        'createdate',
        'lastmodifieddate'
      ]);
      
      result.contacts.push(contactData);
      
      // Get deals if requested
      if (dataCategories.includes('deals')) {
        const deals = await getContactDeals(client, contact.id);
        if (!result.deals) {
          result.deals = [];
        }
        result.deals.push(...deals);
      }
      
      // Get activities if requested
      if (dataCategories.includes('activities')) {
        const activities = await getContactActivities(client, contact.id);
        if (!result.activities) {
          result.activities = [];
        }
        result.activities.push(...activities);
      }
    }
    
    const duration = (Date.now() - startTime) / 1000;
    recordIntegrationRequest('hubspot', 'data-export', 'success', duration);
    
    return result;
  } catch (error) {
    const duration = (Date.now() - startTime) / 1000;
    recordIntegrationRequest('hubspot', 'data-export', 'error', duration);
    
    logger.error('Error exporting data from HubSpot', { error: error.message });
    throw error;
  }
}

/**
 * Delete data from HubSpot
 * @param {Object} config - HubSpot configuration
 * @param {Object} parameters - Deletion parameters
 * @returns {Promise<Object>} - Deletion result
 */
async function deleteData(config, parameters) {
  const startTime = Date.now();
  
  try {
    const { email } = parameters;
    
    if (!email) {
      throw new Error('Email is required for HubSpot data deletion');
    }
    
    const client = getClient(config);
    
    // Search for contacts by email
    const contacts = await searchContactsByEmail(client, email);
    
    if (contacts.length === 0) {
      return {
        deletedContacts: 0
      };
    }
    
    // Delete contacts
    let deletedContacts = 0;
    for (const contact of contacts) {
      await deleteContact(client, contact.id);
      deletedContacts++;
    }
    
    const duration = (Date.now() - startTime) / 1000;
    recordIntegrationRequest('hubspot', 'data-deletion', 'success', duration);
    
    return {
      deletedContacts
    };
  } catch (error) {
    const duration = (Date.now() - startTime) / 1000;
    recordIntegrationRequest('hubspot', 'data-deletion', 'error', duration);
    
    logger.error('Error deleting data from HubSpot', { error: error.message });
    throw error;
  }
}

/**
 * Update data in HubSpot
 * @param {Object} config - HubSpot configuration
 * @param {Object} parameters - Update parameters
 * @returns {Promise<Object>} - Update result
 */
async function updateData(config, parameters) {
  const startTime = Date.now();
  
  try {
    const { email, updates } = parameters;
    
    if (!email) {
      throw new Error('Email is required for HubSpot data update');
    }
    
    if (!updates || Object.keys(updates).length === 0) {
      throw new Error('Updates are required for HubSpot data update');
    }
    
    const client = getClient(config);
    
    // Search for contacts by email
    const contacts = await searchContactsByEmail(client, email);
    
    if (contacts.length === 0) {
      return {
        updatedContacts: 0
      };
    }
    
    // Update contacts
    let updatedContacts = 0;
    for (const contact of contacts) {
      await updateContact(client, contact.id, updates);
      updatedContacts++;
    }
    
    const duration = (Date.now() - startTime) / 1000;
    recordIntegrationRequest('hubspot', 'data-update', 'success', duration);
    
    return {
      updatedContacts
    };
  } catch (error) {
    const duration = (Date.now() - startTime) / 1000;
    recordIntegrationRequest('hubspot', 'data-update', 'error', duration);
    
    logger.error('Error updating data in HubSpot', { error: error.message });
    throw error;
  }
}

/**
 * Check HubSpot integration health
 * @param {Object} config - HubSpot configuration
 * @returns {Promise<Object>} - Health check result
 */
async function healthCheck(config) {
  try {
    const client = getClient(config);
    
    // Call a simple API to check if the integration is working
    await client.get('/crm/v3/properties/contacts');
    
    return {
      status: 'healthy',
      message: 'HubSpot integration is working properly'
    };
  } catch (error) {
    logger.error('HubSpot integration health check failed', { error: error.message });
    
    return {
      status: 'unhealthy',
      message: `HubSpot integration error: ${error.message}`
    };
  }
}

module.exports = {
  exportData,
  deleteData,
  updateData,
  healthCheck,
  
  // Export internal functions for testing
  getClient,
  searchContactsByEmail,
  getContact,
  getContactDeals,
  getContactActivities,
  updateContact,
  deleteContact
};
