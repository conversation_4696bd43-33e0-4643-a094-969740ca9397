# CSDE Advanced API Documentation

This document provides detailed information about the CSDE Advanced API endpoints, which enable advanced CSDE features including offline processing, cross-domain prediction, and compliance mapping.

## Base URL

```
/api/csde-advanced
```

## Authentication

All API endpoints require authentication. Include an authentication token in the `Authorization` header:

```
Authorization: Bearer <token>
```

## Offline Processing

### Process Data Offline

Process data using the Offline Processing Engine, which supports batch processing, persistent storage, and prioritization.

```
POST /offline
```

**Request Body:**

```json
{
  "data": {
    "complianceData": { ... },
    "gcpData": { ... },
    "cyberSafetyData": { ... }
  },
  "options": {
    "priority": "high",
    "priorityScore": 0.9,
    "processingId": "custom-id-123"
  }
}
```

| Parameter | Type | Description |
|-----------|------|-------------|
| data | Object | Data to process |
| options | Object | Processing options |
| options.priority | String | Priority level: "high", "normal", "low" |
| options.priorityScore | Number | Priority score (0-1) |
| options.processingId | String | Custom processing ID (optional) |

**Response:**

```json
{
  "success": true,
  "result": {
    "processingId": "abc123",
    "status": "queued",
    "position": 3,
    "estimatedCompletionTime": "2023-01-01T12:30:00Z",
    "queuedAt": "2023-01-01T12:00:00Z"
  }
}
```

For high-priority requests that are processed immediately:

```json
{
  "success": true,
  "result": {
    "processingId": "abc123",
    "status": "completed",
    "result": {
      "csdeValue": 3142.0,
      "performanceFactor": 3142,
      "nistComponent": { ... },
      "gcpComponent": { ... },
      "cyberSafetyComponent": { ... },
      "tensorProduct": { ... },
      "fusionResult": { ... },
      "remediationActions": [ ... ],
      "calculationTime": 123.45,
      "calculatedAt": "2023-01-01T12:00:00Z"
    },
    "processingTime": 123.45
  }
}
```

### Get Offline Processing Result

Retrieve the result of an offline processing request.

```
GET /offline/:processingId
```

**Parameters:**

| Parameter | Type | In | Description |
|-----------|------|-------|------------|
| processingId | String | path | Processing ID |

**Response:**

For completed processing:

```json
{
  "success": true,
  "result": {
    "processingId": "abc123",
    "status": "completed",
    "result": {
      "csdeValue": 3142.0,
      "performanceFactor": 3142,
      "nistComponent": { ... },
      "gcpComponent": { ... },
      "cyberSafetyComponent": { ... },
      "tensorProduct": { ... },
      "fusionResult": { ... },
      "remediationActions": [ ... ],
      "calculationTime": 123.45,
      "calculatedAt": "2023-01-01T12:00:00Z"
    },
    "retrievedAt": "2023-01-01T12:30:00Z"
  }
}
```

For queued processing:

```json
{
  "success": true,
  "result": {
    "processingId": "abc123",
    "status": "queued",
    "position": 3,
    "estimatedCompletionTime": "2023-01-01T12:30:00Z",
    "queuedAt": "2023-01-01T12:00:00Z"
  }
}
```

### Sync Offline Results

Synchronize offline results with the online system.

```
POST /offline/sync
```

**Response:**

```json
{
  "success": true,
  "result": {
    "synced": 10,
    "failed": 0,
    "results": [
      {
        "file": "abc123.json.gz",
        "status": "synced"
      },
      ...
    ],
    "duration": 123.45,
    "timestamp": "2023-01-01T12:00:00Z"
  }
}
```

### Clean Up Old Offline Results

Clean up old offline results that have been synced and are beyond the retention period.

```
POST /offline/cleanup
```

**Response:**

```json
{
  "success": true,
  "result": {
    "deletedBatches": 5,
    "deletedResults": 50,
    "timestamp": "2023-01-01T12:00:00Z"
  }
}
```

## Advanced Processing

### Process Data with All Advanced Features

Process data using all available advanced features.

```
POST /process
```

**Request Body:**

```json
{
  "data": {
    "complianceData": { ... },
    "gcpData": { ... },
    "cyberSafetyData": { ... }
  },
  "options": {
    "processOffline": true,
    "predictCrossDomain": true,
    "mapCompliance": true,
    "priority": "high",
    "priorityScore": 0.9,
    "targetDomain": "finance",
    "primaryFramework": "NIST_CSF",
    "targetFrameworks": ["PCI_DSS", "HIPAA", "SOC2"]
  }
}
```

**Response:**

```json
{
  "success": true,
  "result": {
    "timestamp": "2023-01-01T12:00:00Z",
    "processingTime": 123.45,
    "processingIds": {
      "offlineProcessing": "abc123"
    },
    "data": {
      "offlineProcessing": {
        "status": "queued",
        "processingId": "abc123"
      },
      "crossDomainPrediction": { ... },
      "complianceMapping": { ... }
    }
  }
}
```

### Get Advanced Processing Results

Retrieve the results of an advanced processing request.

```
GET /process/results
```

**Query Parameters:**

| Parameter | Type | Description |
|-----------|------|-------------|
| processingIds | String | JSON string of processing IDs for each feature |

Example: `?processingIds={"offlineProcessing":"abc123"}`

**Response:**

```json
{
  "success": true,
  "results": {
    "timestamp": "2023-01-01T12:30:00Z",
    "data": {
      "offlineProcessing": {
        "processingId": "abc123",
        "status": "completed",
        "result": { ... },
        "retrievedAt": "2023-01-01T12:30:00Z"
      }
    }
  }
}
```

## Cross-Domain Prediction

### Predict Cross-Domain Insights

Predict insights across different domains using advanced features like tensor fusion, adaptive learning, and circular trust topology.

```
POST /predict
```

**Request Body:**

```json
{
  "data": {
    "accessControl": {
      "policies": 85,
      "enforcement": 92,
      "reviews": 78
    },
    "dataEncryption": {
      "atRest": 95,
      "inTransit": 90,
      "keyManagement": 88
    },
    "vulnerabilityManagement": {
      "scanning": 82,
      "patching": 75,
      "riskAssessment": 80
    }
  },
  "targetDomain": "compliance",
  "options": {
    "enableTensorFusion": true,
    "enableAdaptiveLearning": true,
    "enableCircularTrustTopology": true,
    "confidenceThreshold": 0.8,
    "maxPredictions": 5
  }
}
```

| Parameter | Type | Description |
|-----------|------|-------------|
| data | Object | Source domain data |
| targetDomain | String | Target domain for predictions (compliance, finance, healthcare) |
| options | Object | Advanced prediction options |
| options.enableTensorFusion | Boolean | Enable tensor fusion for enhanced prediction accuracy |
| options.enableAdaptiveLearning | Boolean | Enable adaptive learning for improved pattern mapping |
| options.enableCircularTrustTopology | Boolean | Enable circular trust topology for confidence enhancement |
| options.confidenceThreshold | Number | Minimum confidence threshold for predictions (0-1) |
| options.maxPredictions | Number | Maximum number of predictions to return |

**Response:**

```json
{
  "success": true,
  "result": {
    "sourceDomain": "security",
    "targetDomain": "compliance",
    "sourcePatterns": [
      {
        "type": "access_control",
        "strength": 0.85,
        "data": { ... }
      },
      {
        "type": "data_encryption",
        "strength": 0.91,
        "data": { ... }
      }
    ],
    "targetPatterns": [
      {
        "type": "access_management_controls",
        "strength": 0.85,
        "sourcePattern": "access_control",
        "sourceDomain": "security"
      },
      {
        "type": "data_protection_controls",
        "strength": 0.91,
        "sourcePattern": "data_encryption",
        "sourceDomain": "security"
      }
    ],
    "predictions": [
      {
        "id": "comp-acc-1623456789",
        "type": "compliance_control",
        "title": "Access Management Control Gap",
        "description": "Potential gap in access management controls based on security patterns",
        "domain": "compliance",
        "patternType": "access_management_controls",
        "patternStrength": 0.85,
        "sourcePattern": "access_control",
        "sourceDomain": "security",
        "confidence": 0.92,
        "fusionFactor": 0.95,
        "fusionApplied": true,
        "circularTrustApplied": true
      },
      {
        "id": "comp-dat-1623456790",
        "type": "compliance_control",
        "title": "Data Protection Control Gap",
        "description": "Potential gap in data protection controls based on security patterns",
        "domain": "compliance",
        "patternType": "data_protection_controls",
        "patternStrength": 0.91,
        "sourcePattern": "data_encryption",
        "sourceDomain": "security",
        "confidence": 0.94,
        "fusionFactor": 0.97,
        "fusionApplied": true,
        "circularTrustApplied": true
      }
    ],
    "metadata": {
      "totalPredictions": 8,
      "filteredPredictions": 5,
      "confidenceThreshold": 0.8,
      "predictionTime": 123.45,
      "predictedAt": "2023-01-01T12:00:00Z",
      "tensorFusionApplied": true,
      "adaptiveLearningApplied": true,
      "circularTrustTopologyApplied": true
    }
  }
}
```

## Compliance Mapping

### Map Compliance Controls

Map compliance controls across different frameworks with enhanced features like semantic analysis, machine learning, cross-framework harmonization, and compliance visualization.

```
POST /compliance
```

**Request Body:**

```json
{
  "implementationData": {
    "ID.AM-1": {
      "status": "IMPLEMENTED",
      "implementation": ["Asset inventory system is in place"],
      "evidence": ["Asset inventory report"]
    },
    "PR.AC-1": {
      "status": "PARTIALLY_IMPLEMENTED",
      "implementation": ["Identity management system is in place"],
      "evidence": ["IAM configuration"]
    },
    "PR.DS-1": {
      "status": "NOT_IMPLEMENTED",
      "implementation": [],
      "evidence": []
    }
  },
  "primaryFramework": "NIST_CSF",
  "targetFrameworks": ["PCI_DSS", "HIPAA", "SOC2"],
  "options": {
    "enableEnhancedMapping": true,
    "enableSemanticAnalysis": true,
    "enableMachineLearning": true,
    "enableCrossFrameworkHarmonization": true,
    "enableComplianceVisualization": true
  }
}
```

| Parameter | Type | Description |
|-----------|------|-------------|
| implementationData | Object | Implementation data for primary framework |
| primaryFramework | String | Primary framework (NIST_CSF, ISO27001, etc.) |
| targetFrameworks | Array | Target frameworks to map to (PCI_DSS, HIPAA, SOC2, etc.) |
| options | Object | Enhanced mapping options |
| options.enableEnhancedMapping | Boolean | Enable enhanced mapping features |
| options.enableSemanticAnalysis | Boolean | Enable semantic analysis for finding additional mappings |
| options.enableMachineLearning | Boolean | Enable machine learning for confidence scoring |
| options.enableCrossFrameworkHarmonization | Boolean | Enable cross-framework harmonization |
| options.enableComplianceVisualization | Boolean | Enable compliance visualization data generation |

**Response:**

```json
{
  "success": true,
  "result": {
    "primaryFramework": "NIST_CSF",
    "targetFrameworks": ["PCI_DSS", "HIPAA", "SOC2"],
    "mappingResults": {
      "PCI_DSS": {
        "mappedControls": {
          "9.1": {
            "control": {
              "id": "9.1",
              "category": "Physical Access",
              "description": "Use appropriate facility entry controls to limit and monitor physical access to systems"
            },
            "mappedFrom": [
              {
                "controlId": "ID.AM-1",
                "status": "IMPLEMENTED",
                "mappingConfidence": 0.85
              }
            ],
            "status": "IMPLEMENTED",
            "implementation": ["Asset inventory system is in place"],
            "evidence": ["Asset inventory report"]
          }
        },
        "unmappedControls": ["1.1", "2.1", "3.1"],
        "enhancedMappedControls": {
          "9.1": {
            "control": {
              "id": "9.1",
              "category": "Physical Access",
              "description": "Use appropriate facility entry controls to limit and monitor physical access to systems"
            },
            "mappedFrom": [
              {
                "controlId": "ID.AM-1",
                "status": "IMPLEMENTED",
                "mappingConfidence": 0.85
              }
            ],
            "status": "IMPLEMENTED",
            "implementation": ["Asset inventory system is in place"],
            "evidence": ["Asset inventory report"],
            "enhancedConfidence": 0.92,
            "mappingSource": "predefined"
          },
          "8.1": {
            "control": {
              "id": "8.1",
              "category": "Access Control",
              "description": "Define and implement policies and procedures to ensure proper user identification management"
            },
            "mappedFrom": [
              {
                "controlId": "PR.AC-1",
                "status": "PARTIALLY_IMPLEMENTED",
                "mappingConfidence": 0.78
              }
            ],
            "status": "PARTIALLY_IMPLEMENTED",
            "implementation": ["Identity management system is in place"],
            "evidence": ["IAM configuration"],
            "enhancedConfidence": 0.85,
            "mappingSource": "semantic"
          }
        },
        "semanticMappings": [
          {
            "sourceControlId": "PR.AC-1",
            "targetControlId": "8.1",
            "similarity": 0.78,
            "sourceDescription": "Identity management and access control",
            "targetDescription": "Define and implement policies and procedures to ensure proper user identification management"
          }
        ],
        "mlConfidenceScores": {
          "9.1": 0.92,
          "8.1": 0.85
        },
        "mappingMetadata": {
          "semanticAnalysisApplied": true,
          "machineLearningApplied": true,
          "mappedAt": "2023-01-01T12:00:00Z"
        }
      }
    },
    "gapAnalysis": {
      "PCI_DSS": {
        "unmappedControls": ["1.1", "2.1", "3.1"],
        "notImplementedControls": ["3.1"],
        "partiallyImplementedControls": ["8.1"],
        "gapSeverity": {
          "high": ["3.1"],
          "medium": ["1.1"],
          "low": ["2.1"]
        },
        "gapCategories": {
          "Network Security": ["1.1"],
          "Encryption": ["3.1"],
          "Access Control": ["2.1"]
        },
        "remediationDifficulty": {
          "easy": ["1.1"],
          "moderate": ["2.1"],
          "complex": ["3.1"]
        }
      }
    },
    "complianceScores": {
      "PCI_DSS": {
        "totalControls": 12,
        "implementedControls": 1,
        "partiallyImplementedControls": 1,
        "notImplementedControls": 1,
        "unmappedControls": 9,
        "complianceScore": 0.25,
        "weightedComplianceScore": 0.32,
        "complianceByCategory": {
          "Physical Access": {
            "totalControls": 2,
            "implementedControls": 1,
            "partiallyImplementedControls": 0,
            "complianceScore": 0.5
          },
          "Access Control": {
            "totalControls": 3,
            "implementedControls": 0,
            "partiallyImplementedControls": 1,
            "complianceScore": 0.17
          }
        },
        "averageConfidence": 0.88
      }
    },
    "recommendations": {
      "PCI_DSS": {
        "priorityControls": [
          {
            "controlId": "3.1",
            "description": "Keep cardholder data storage to a minimum",
            "priority": "HIGH"
          }
        ],
        "quickWins": [
          {
            "controlId": "1.1",
            "description": "Establish and implement firewall configuration standards",
            "reason": "High severity gap with easy remediation",
            "impact": "HIGH"
          }
        ],
        "strategicRecommendations": [
          {
            "category": "Encryption",
            "description": "Focus on improving compliance in the Encryption category",
            "currentScore": 0.0,
            "controlsNeeded": 2,
            "priority": "HIGH"
          },
          {
            "description": "Implement a comprehensive cardholder data environment (CDE) segmentation strategy",
            "benefit": "Reduces scope of PCI DSS compliance and improves security posture",
            "priority": "HIGH"
          }
        ],
        "categoryRecommendations": {
          "Encryption": {
            "gapCount": 2,
            "description": "Address 2 control gaps in the Encryption category",
            "sampleGaps": ["3.1", "3.4"],
            "priority": "HIGH"
          }
        }
      }
    },
    "visualizationData": {
      "complianceScores": {
        "PCI_DSS": {
          "overall": 0.25,
          "weighted": 0.32,
          "implemented": 0.08,
          "partiallyImplemented": 0.08,
          "notImplemented": 0.08,
          "unmapped": 0.75
        }
      },
      "categoryBreakdown": {
        "PCI_DSS": {
          "Physical Access": {
            "score": 0.5,
            "implemented": 0.5,
            "partiallyImplemented": 0.0,
            "notImplemented": 0.5
          },
          "Access Control": {
            "score": 0.17,
            "implemented": 0.0,
            "partiallyImplemented": 0.33,
            "notImplemented": 0.67
          }
        }
      },
      "mappingRelationships": {
        "PCI_DSS": [
          {
            "source": "ID.AM-1",
            "target": "9.1",
            "confidence": 0.85,
            "status": "IMPLEMENTED"
          },
          {
            "source": "PR.AC-1",
            "target": "8.1",
            "confidence": 0.78,
            "status": "PARTIALLY_IMPLEMENTED"
          }
        ]
      }
    },
    "metadata": {
      "mappingTime": 123.45,
      "mappedAt": "2023-01-01T12:00:00Z",
      "enhancedFeatures": {
        "semanticAnalysis": true,
        "machineLearning": true,
        "crossFrameworkHarmonization": true,
        "complianceVisualization": true
      }
    }
  }
}
```

## Metrics and Health

### Get Metrics

Get performance metrics for the CSDE advanced features.

```
GET /metrics
```

**Response:**

```json
{
  "success": true,
  "metrics": {
    "offlineProcessing": {
      "totalRequests": 100,
      "successfulRequests": 95,
      "failedRequests": 5,
      "totalLatency": 12345,
      "averageLatency": 123.45
    },
    "crossDomainPrediction": { ... },
    "complianceMapping": { ... }
  }
}
```

### Reset Metrics

Reset performance metrics.

```
POST /metrics/reset
```

**Response:**

```json
{
  "success": true,
  "message": "Metrics reset successfully"
}
```

### Get Health

Get health status of the CSDE advanced features.

```
GET /health
```

**Response:**

```json
{
  "success": true,
  "health": {
    "status": "healthy",
    "metrics": {
      "totalRequests": 100,
      "successfulRequests": 95,
      "overallSuccessRate": 0.95,
      "offlineProcessing": {
        "successRate": 0.95,
        "averageLatency": 123.45
      },
      "crossDomainPrediction": { ... },
      "complianceMapping": { ... }
    },
    "timestamp": "2023-01-01T12:00:00Z"
  }
}
```
