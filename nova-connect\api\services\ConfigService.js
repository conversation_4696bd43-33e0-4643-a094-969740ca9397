/**
 * Configuration Service
 * 
 * This service handles environment-specific configuration management.
 */

const fs = require('fs').promises;
const path = require('path');
const { ValidationError, NotFoundError } = require('../utils/errors');
const EnvironmentService = require('./EnvironmentService');

class ConfigService {
  constructor(dataDir = path.join(__dirname, '../data')) {
    this.dataDir = dataDir;
    this.environmentService = new EnvironmentService(dataDir);
  }

  /**
   * Get configuration file path for a specific environment
   */
  getConfigFilePath(environmentId, configType) {
    const envDataDir = this.environmentService.getEnvironmentDataDir(environmentId);
    return path.join(envDataDir, `${configType}.json`);
  }

  /**
   * Load configuration from file
   */
  async loadConfig(environmentId, configType) {
    try {
      // Ensure environment exists
      await this.environmentService.getEnvironmentById(environmentId);
      
      // Ensure environment data directory exists
      await this.environmentService.ensureEnvironmentDataDir(environmentId);
      
      const configFile = this.getConfigFilePath(environmentId, configType);
      
      try {
        const data = await fs.readFile(configFile, 'utf8');
        return JSON.parse(data);
      } catch (error) {
        if (error.code === 'ENOENT') {
          // File doesn't exist, return empty object
          return {};
        }
        throw error;
      }
    } catch (error) {
      console.error(`Error loading ${configType} config for environment ${environmentId}:`, error);
      throw error;
    }
  }

  /**
   * Save configuration to file
   */
  async saveConfig(environmentId, configType, config) {
    try {
      // Ensure environment exists
      await this.environmentService.getEnvironmentById(environmentId);
      
      // Ensure environment data directory exists
      await this.environmentService.ensureEnvironmentDataDir(environmentId);
      
      const configFile = this.getConfigFilePath(environmentId, configType);
      
      await fs.writeFile(configFile, JSON.stringify(config, null, 2));
      
      return config;
    } catch (error) {
      console.error(`Error saving ${configType} config for environment ${environmentId}:`, error);
      throw error;
    }
  }

  /**
   * Get API configuration for an environment
   */
  async getApiConfig(environmentId) {
    return this.loadConfig(environmentId, 'api');
  }

  /**
   * Update API configuration for an environment
   */
  async updateApiConfig(environmentId, config) {
    return this.saveConfig(environmentId, 'api', config);
  }

  /**
   * Get connector configuration for an environment
   */
  async getConnectorConfig(environmentId) {
    return this.loadConfig(environmentId, 'connectors');
  }

  /**
   * Update connector configuration for an environment
   */
  async updateConnectorConfig(environmentId, config) {
    return this.saveConfig(environmentId, 'connectors', config);
  }

  /**
   * Get security configuration for an environment
   */
  async getSecurityConfig(environmentId) {
    return this.loadConfig(environmentId, 'security');
  }

  /**
   * Update security configuration for an environment
   */
  async updateSecurityConfig(environmentId, config) {
    return this.saveConfig(environmentId, 'security', config);
  }

  /**
   * Get monitoring configuration for an environment
   */
  async getMonitoringConfig(environmentId) {
    return this.loadConfig(environmentId, 'monitoring');
  }

  /**
   * Update monitoring configuration for an environment
   */
  async updateMonitoringConfig(environmentId, config) {
    return this.saveConfig(environmentId, 'monitoring', config);
  }

  /**
   * Get all configuration for an environment
   */
  async getAllConfig(environmentId) {
    try {
      // Ensure environment exists
      await this.environmentService.getEnvironmentById(environmentId);
      
      // Load all configuration types
      const [api, connectors, security, monitoring] = await Promise.all([
        this.getApiConfig(environmentId),
        this.getConnectorConfig(environmentId),
        this.getSecurityConfig(environmentId),
        this.getMonitoringConfig(environmentId)
      ]);
      
      return {
        api,
        connectors,
        security,
        monitoring
      };
    } catch (error) {
      console.error(`Error loading all config for environment ${environmentId}:`, error);
      throw error;
    }
  }

  /**
   * Compare configuration between environments
   */
  async compareConfig(sourceId, targetId) {
    try {
      // Ensure environments exist
      await this.environmentService.getEnvironmentById(sourceId);
      await this.environmentService.getEnvironmentById(targetId);
      
      // Load all configuration for both environments
      const sourceConfig = await this.getAllConfig(sourceId);
      const targetConfig = await this.getAllConfig(targetId);
      
      // Compare configurations
      const differences = {};
      
      for (const configType in sourceConfig) {
        differences[configType] = this.compareObjects(
          sourceConfig[configType],
          targetConfig[configType]
        );
      }
      
      return {
        sourceId,
        targetId,
        differences
      };
    } catch (error) {
      console.error(`Error comparing config between ${sourceId} and ${targetId}:`, error);
      throw error;
    }
  }

  /**
   * Compare two objects and return differences
   */
  compareObjects(source, target) {
    const differences = {
      added: [],
      removed: [],
      modified: []
    };
    
    // Check for added or modified properties
    for (const key in source) {
      if (!(key in target)) {
        differences.removed.push(key);
      } else if (JSON.stringify(source[key]) !== JSON.stringify(target[key])) {
        differences.modified.push(key);
      }
    }
    
    // Check for removed properties
    for (const key in target) {
      if (!(key in source)) {
        differences.added.push(key);
      }
    }
    
    return differences;
  }
}

module.exports = ConfigService;
