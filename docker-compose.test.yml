version: '3.8'

services:
  # MongoDB for data storage
  mongodb:
    image: mongo:latest
    volumes:
      - mongodb_data:/data/db
    ports:
      - "27017:27017"
    healthcheck:
      test: ["C<PERSON>", "mongo", "--eval", "db.adminCommand('ping')"]
      interval: 10s
      timeout: 5s
      retries: 3

  # Redis for caching and pub/sub
  redis:
    image: redis:alpine
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  # NovaConnect API
  nova-connect:
    build:
      context: ./nova-connect
      dockerfile: Dockerfile
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=test
      - PORT=3001
      - MONGODB_URI=mongodb://mongodb:27017/nova-connect-test
      - REDIS_URI=redis://redis:6379
    depends_on:
      mongodb:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./nova-connect:/app
      - /app/node_modules
    healthcheck:
      test: ["CMD", "wget", "--spider", "http://localhost:3001/health"]
      interval: 10s
      timeout: 5s
      retries: 3

  # GCP Service Simulators
  gcp-simulators:
    build:
      context: ./gcp-simulation/gcp-simulators
      dockerfile: Dockerfile
    ports:
      - "8081-8083:8081-8083"
    environment:
      - SCC_PORT=8081
      - IAM_PORT=8082
      - BIGQUERY_PORT=8083
    healthcheck:
      test: ["CMD", "wget", "--spider", "http://localhost:8081/health"]
      interval: 10s
      timeout: 5s
      retries: 3

  # GCP Services
  gcp-services:
    build:
      context: ./gcp-simulation/gcp-services
      dockerfile: Dockerfile
    ports:
      - "8084-8086:8084-8086"
    environment:
      - CLOUD_STORAGE_PORT=8084
      - CLOUD_FUNCTIONS_PORT=8085
      - CLOUD_MONITORING_PORT=8086
    healthcheck:
      test: ["CMD", "wget", "--spider", "http://localhost:8084/health"]
      interval: 10s
      timeout: 5s
      retries: 3

  # Test Runner
  test-runner:
    build:
      context: ./
      dockerfile: Dockerfile.test
    volumes:
      - ./:/app
      - /app/node_modules
      - ./test-results:/app/test-results
    environment:
      - NODE_ENV=test
      - NOVA_CONNECT_URL=http://nova-connect:3001
      - GCP_SIMULATORS_URL=http://gcp-simulators
      - GCP_SERVICES_URL=http://gcp-services
    depends_on:
      nova-connect:
        condition: service_healthy
      gcp-simulators:
        condition: service_healthy
      gcp-services:
        condition: service_healthy
    command: ["npm", "run", "test:all"]

volumes:
  mongodb_data:
