/**
 * Enhanced Report Routes
 * 
 * This file defines enhanced routes for reporting functionality including:
 * - Compliance reporting
 * - Performance reporting
 * - Security reporting
 * - Custom reporting with advanced filtering
 * - Report scheduling and distribution
 */

const express = require('express');
const router = express.Router();
const EnhancedReportController = require('../controllers/EnhancedReportController');
const { authenticate, hasPermission } = require('../middleware/authMiddleware');

// All routes require authentication
router.use(authenticate);

/**
 * @route POST /api/enhanced-reports/compliance
 * @description Generate a compliance report
 * @access Authenticated
 */
router.post('/compliance', (req, res, next) => {
  EnhancedReportController.generateComplianceReport(req, res, next);
});

/**
 * @route POST /api/enhanced-reports/performance
 * @description Generate a performance report
 * @access Authenticated
 */
router.post('/performance', (req, res, next) => {
  EnhancedReportController.generatePerformanceReport(req, res, next);
});

/**
 * @route POST /api/enhanced-reports/security
 * @description Generate a security report
 * @access Authenticated
 */
router.post('/security', (req, res, next) => {
  EnhancedReportController.generateSecurityReport(req, res, next);
});

/**
 * @route POST /api/enhanced-reports/custom
 * @description Generate a custom report
 * @access Authenticated
 */
router.post('/custom', (req, res, next) => {
  EnhancedReportController.generateCustomReport(req, res, next);
});

/**
 * @route GET /api/enhanced-reports/:reportId/status
 * @description Get report status
 * @access Authenticated
 */
router.get('/:reportId/status', (req, res, next) => {
  EnhancedReportController.getReportStatus(req, res, next);
});

/**
 * @route GET /api/enhanced-reports/:reportId/download
 * @description Download a report
 * @access Authenticated
 */
router.get('/:reportId/download', (req, res, next) => {
  EnhancedReportController.downloadReport(req, res, next);
});

/**
 * @route POST /api/enhanced-reports/:reportId/schedule
 * @description Schedule a report
 * @access Authenticated
 */
router.post('/:reportId/schedule', (req, res, next) => {
  EnhancedReportController.scheduleReport(req, res, next);
});

/**
 * @route GET /api/enhanced-reports/scheduled
 * @description Get all scheduled reports
 * @access Admin
 */
router.get('/scheduled', hasPermission('system:audit'), (req, res, next) => {
  EnhancedReportController.getAllScheduledReports(req, res, next);
});

/**
 * @route GET /api/enhanced-reports/scheduled/my
 * @description Get my scheduled reports
 * @access Authenticated
 */
router.get('/scheduled/my', (req, res, next) => {
  EnhancedReportController.getMyScheduledReports(req, res, next);
});

/**
 * @route GET /api/enhanced-reports/scheduled/:id
 * @description Get scheduled report by ID
 * @access Authenticated
 */
router.get('/scheduled/:id', (req, res, next) => {
  EnhancedReportController.getScheduledReportById(req, res, next);
});

/**
 * @route PUT /api/enhanced-reports/scheduled/:id
 * @description Update scheduled report
 * @access Authenticated
 */
router.put('/scheduled/:id', (req, res, next) => {
  EnhancedReportController.updateScheduledReport(req, res, next);
});

/**
 * @route DELETE /api/enhanced-reports/scheduled/:id
 * @description Delete scheduled report
 * @access Authenticated
 */
router.delete('/scheduled/:id', (req, res, next) => {
  EnhancedReportController.deleteScheduledReport(req, res, next);
});

/**
 * @route POST /api/enhanced-reports/scheduled/:id/run
 * @description Run scheduled report now
 * @access Authenticated
 */
router.post('/scheduled/:id/run', (req, res, next) => {
  EnhancedReportController.runScheduledReport(req, res, next);
});

/**
 * @route GET /api/enhanced-reports/templates
 * @description Get all report templates
 * @access Authenticated
 */
router.get('/templates', (req, res, next) => {
  EnhancedReportController.getAllReportTemplates(req, res, next);
});

/**
 * @route GET /api/enhanced-reports/templates/:id
 * @description Get report template by ID
 * @access Authenticated
 */
router.get('/templates/:id', (req, res, next) => {
  EnhancedReportController.getReportTemplateById(req, res, next);
});

/**
 * @route POST /api/enhanced-reports/templates
 * @description Create report template
 * @access Admin
 */
router.post('/templates', hasPermission('system:settings'), (req, res, next) => {
  EnhancedReportController.createReportTemplate(req, res, next);
});

/**
 * @route PUT /api/enhanced-reports/templates/:id
 * @description Update report template
 * @access Admin
 */
router.put('/templates/:id', hasPermission('system:settings'), (req, res, next) => {
  EnhancedReportController.updateReportTemplate(req, res, next);
});

/**
 * @route DELETE /api/enhanced-reports/templates/:id
 * @description Delete report template
 * @access Admin
 */
router.delete('/templates/:id', hasPermission('system:settings'), (req, res, next) => {
  EnhancedReportController.deleteReportTemplate(req, res, next);
});

/**
 * @route POST /api/enhanced-reports/templates/:id/clone
 * @description Clone report template
 * @access Authenticated
 */
router.post('/templates/:id/clone', (req, res, next) => {
  EnhancedReportController.cloneReportTemplate(req, res, next);
});

module.exports = router;
