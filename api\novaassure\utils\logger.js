/**
 * Logger Utility
 * 
 * This utility provides logging functionality for the NovaAssure API.
 */

const winston = require('winston');
const path = require('path');

// Define log format
const logFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.errors({ stack: true }),
  winston.format.splat(),
  winston.format.json()
);

// Create logger
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: logFormat,
  defaultMeta: { service: 'novaassure' },
  transports: [
    // Write to all logs with level 'info' and below to 'combined.log'
    new winston.transports.File({ 
      filename: path.join(process.env.LOG_DIR || 'logs', 'novaassure-combined.log') 
    }),
    // Write all logs with level 'error' and below to 'error.log'
    new winston.transports.File({ 
      filename: path.join(process.env.LOG_DIR || 'logs', 'novaassure-error.log'), 
      level: 'error' 
    })
  ]
});

// If we're not in production, log to the console as well
if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.combine(
      winston.format.colorize(),
      winston.format.simple()
    )
  }));
}

/**
 * Log an info message
 * @param {string} message - Log message
 * @param {Object} [meta] - Additional metadata
 */
function info(message, meta = {}) {
  logger.info(message, meta);
}

/**
 * Log a warning message
 * @param {string} message - Log message
 * @param {Object} [meta] - Additional metadata
 */
function warn(message, meta = {}) {
  logger.warn(message, meta);
}

/**
 * Log an error message
 * @param {string} message - Log message
 * @param {Object|Error} [error] - Error object or metadata
 */
function error(message, error = {}) {
  if (error instanceof Error) {
    logger.error(message, { error: error.message, stack: error.stack });
  } else {
    logger.error(message, error);
  }
}

/**
 * Log a debug message
 * @param {string} message - Log message
 * @param {Object} [meta] - Additional metadata
 */
function debug(message, meta = {}) {
  logger.debug(message, meta);
}

module.exports = {
  info,
  warn,
  error,
  debug,
  logger
};
