#!/bin/bash
# NovaConnect UAC Tenant Provisioning Script

# Set variables
TENANT_ID=$1
TENANT_NAME=${2:-"Tenant ${TENANT_ID}"}
TIER=${3:-"core"}
REPLICA_COUNT=${4:-"3"}
CPU_REQUEST=${5:-"500m"}
MEMORY_REQUEST=${6:-"512Mi"}
CPU_LIMIT=${7:-"1000m"}
MEMORY_LIMIT=${8:-"1Gi"}
POD_LIMIT=${9:-"20"}
PROJECT_ID=${PROJECT_ID:-"novafuse-marketplace"}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# Check if tenant ID is provided
if [ -z "$TENANT_ID" ]; then
  echo -e "${RED}Error: Tenant ID is required.${NC}"
  echo "Usage: $0 <tenant_id> [tenant_name] [tier] [replica_count] [cpu_request] [memory_request] [cpu_limit] [memory_limit] [pod_limit]"
  exit 1
fi

# Generate random secrets
API_KEY=$(openssl rand -hex 16)
JWT_SECRET=$(openssl rand -hex 32)
CSRF_SECRET=$(openssl rand -hex 16)

# Create namespace from template
echo -e "${YELLOW}Creating namespace for tenant ${TENANT_ID}...${NC}"
envsubst < k8s/templates/namespace.yaml | kubectl apply -f -

# Create resource quota from template
echo -e "${YELLOW}Creating resource quota for tenant ${TENANT_ID}...${NC}"
envsubst < k8s/templates/resource-quota.yaml | kubectl apply -f -

# Create network policy from template
echo -e "${YELLOW}Creating network policy for tenant ${TENANT_ID}...${NC}"
envsubst < k8s/templates/network-policy.yaml | kubectl apply -f -

# Create tenant-specific values file
echo -e "${YELLOW}Creating tenant-specific values file...${NC}"
VALUES_FILE="marketplace/chart/values-tenant-${TENANT_ID}.yaml"
envsubst < marketplace/chart/values-tenant-template.yaml > ${VALUES_FILE}

# Create tenant-specific service account
echo -e "${YELLOW}Creating service account for tenant ${TENANT_ID}...${NC}"
gcloud iam service-accounts create tenant-${TENANT_ID} \
  --display-name="NovaConnect UAC Tenant ${TENANT_ID} Service Account"

# Grant permissions to service account
echo -e "${YELLOW}Granting permissions to service account...${NC}"
gcloud projects add-iam-policy-binding ${PROJECT_ID} \
  --member="serviceAccount:tenant-${TENANT_ID}@${PROJECT_ID}.iam.gserviceaccount.com" \
  --role="roles/monitoring.metricWriter"

gcloud projects add-iam-policy-binding ${PROJECT_ID} \
  --member="serviceAccount:tenant-${TENANT_ID}@${PROJECT_ID}.iam.gserviceaccount.com" \
  --role="roles/logging.logWriter"

# Create tenant-specific encryption key
echo -e "${YELLOW}Creating encryption key for tenant ${TENANT_ID}...${NC}"
gcloud kms keyrings create tenant-${TENANT_ID}-keyring \
  --location=global

gcloud kms keys create tenant-${TENANT_ID}-key \
  --location=global \
  --keyring=tenant-${TENANT_ID}-keyring \
  --purpose=encryption

# Grant key access to service account
gcloud kms keys add-iam-policy-binding tenant-${TENANT_ID}-key \
  --location=global \
  --keyring=tenant-${TENANT_ID}-keyring \
  --member="serviceAccount:tenant-${TENANT_ID}@${PROJECT_ID}.iam.gserviceaccount.com" \
  --role="roles/cloudkms.cryptoKeyEncrypterDecrypter"

# Create tenant-specific BigQuery dataset
echo -e "${YELLOW}Creating BigQuery dataset for tenant ${TENANT_ID}...${NC}"
bq mk --dataset \
  --description "NovaConnect UAC Data for Tenant ${TENANT_ID}" \
  ${PROJECT_ID}:tenant_${TENANT_ID}

# Create tenant-specific monitoring dashboard
echo -e "${YELLOW}Creating monitoring dashboard for tenant ${TENANT_ID}...${NC}"
mkdir -p monitoring/dashboards
sed "s/\${TENANT_ID}/${TENANT_ID}/g" monitoring/templates/tenant-dashboard.json > monitoring/dashboards/tenant-${TENANT_ID}-dashboard.json

# Deploy tenant-specific instance
echo -e "${YELLOW}Deploying NovaConnect UAC for tenant ${TENANT_ID}...${NC}"
helm upgrade --install tenant-${TENANT_ID} ./marketplace/chart \
  --namespace tenant-${TENANT_ID} \
  --values ${VALUES_FILE}

echo -e "${GREEN}Tenant ${TENANT_ID} provisioned successfully!${NC}"
echo -e "${GREEN}Dashboard URL: https://console.cloud.google.com/monitoring/dashboards/custom/tenant-${TENANT_ID}?project=${PROJECT_ID}${NC}"
echo -e "${GREEN}API URL: https://tenant-${TENANT_ID}.novafuse.io${NC}"

exit 0
