# Request Validation Schemas

This directory contains validation schemas for all API endpoints in NovaConnect.

## Directory Structure

- `auth/` - Authentication-related schemas
- `connectors/` - Connector-related schemas
- `users/` - User-related schemas
- `teams/` - Team-related schemas
- `audit/` - Audit-related schemas
- `reports/` - Report-related schemas
- `analytics/` - Analytics-related schemas
- `common/` - Common validation schemas used across multiple endpoints

## Usage

Validation schemas are used with the `requestValidator` utility to validate API requests:

```javascript
const { validate } = require('../utils/requestValidator');
const { createUserSchema } = require('../validation/users/userSchemas');

router.post('/users', validate(createUserSchema), userController.createUser);
```

## Schema Format

Each schema file exports one or more validation schemas in the following format:

```javascript
const Joi = require('joi');
const { commonSchemas } = require('../utils/requestValidator');

const createUserSchema = {
  body: Joi.object({
    name: Joi.string().required(),
    email: Joi.string().email().required(),
    password: Joi.string().min(8).required()
  })
};

module.exports = {
  createUserSchema
};
```

## Common Schemas

Common validation schemas are available in the `requestValidator` utility:

```javascript
const { commonSchemas } = require('../utils/requestValidator');

const schema = {
  body: Joi.object({
    id: commonSchemas.id,
    email: commonSchemas.email,
    password: commonSchemas.password
  })
};
```
