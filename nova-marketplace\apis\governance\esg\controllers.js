const { v4: uuidv4 } = require('uuid');
const models = require('./models');

/**
 * Get a list of ESG reports
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getReports = (req, res) => {
  try {
    const { page = 1, limit = 10, framework, status, year, sortBy = 'title', sortOrder = 'asc' } = req.query;
    
    // Convert page and limit to numbers
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);
    
    // Filter reports based on query parameters
    let filteredReports = [...models.esgReports];
    
    if (framework) {
      filteredReports = filteredReports.filter(report => report.framework === framework);
    }
    
    if (status) {
      filteredReports = filteredReports.filter(report => report.status === status);
    }
    
    if (year) {
      const yearNum = parseInt(year, 10);
      filteredReports = filteredReports.filter(report => report.reportingYear === yearNum);
    }
    
    // Sort reports
    filteredReports.sort((a, b) => {
      if (sortOrder.toLowerCase() === 'desc') {
        return a[sortBy] > b[sortBy] ? -1 : 1;
      }
      return a[sortBy] > b[sortBy] ? 1 : -1;
    });
    
    // Paginate results
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = pageNum * limitNum;
    const paginatedReports = filteredReports.slice(startIndex, endIndex);
    
    // Calculate pagination info
    const totalReports = filteredReports.length;
    const totalPages = Math.ceil(totalReports / limitNum);
    
    res.json({
      data: paginatedReports,
      pagination: {
        total: totalReports,
        page: pageNum,
        limit: limitNum,
        pages: totalPages
      }
    });
  } catch (error) {
    console.error('Error in getReports:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get a specific ESG report by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getReportById = (req, res) => {
  try {
    const { id } = req.params;
    const report = models.esgReports.find(r => r.id === id);
    
    if (!report) {
      return res.status(404).json({
        error: 'Not Found',
        message: `ESG report with ID ${id} not found`
      });
    }
    
    res.json({ data: report });
  } catch (error) {
    console.error('Error in getReportById:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Create a new ESG report
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const createReport = (req, res) => {
  try {
    const { title, description, framework, reportingYear, status, metrics } = req.body;
    
    // Create a new report with a unique ID
    const newReport = {
      id: uuidv4(),
      title,
      description,
      framework,
      reportingYear,
      status: status || 'draft',
      metrics: metrics || [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    // Add the new report to the collection
    models.esgReports.push(newReport);
    
    res.status(201).json({
      data: newReport,
      message: 'ESG report created successfully'
    });
  } catch (error) {
    console.error('Error in createReport:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Update an existing ESG report
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const updateReport = (req, res) => {
  try {
    const { id } = req.params;
    const { title, description, framework, reportingYear, status, metrics } = req.body;
    
    // Find the report to update
    const reportIndex = models.esgReports.findIndex(r => r.id === id);
    
    if (reportIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `ESG report with ID ${id} not found`
      });
    }
    
    // Update the report
    const updatedReport = {
      ...models.esgReports[reportIndex],
      title: title || models.esgReports[reportIndex].title,
      description: description || models.esgReports[reportIndex].description,
      framework: framework || models.esgReports[reportIndex].framework,
      reportingYear: reportingYear || models.esgReports[reportIndex].reportingYear,
      status: status || models.esgReports[reportIndex].status,
      metrics: metrics || models.esgReports[reportIndex].metrics,
      updatedAt: new Date().toISOString()
    };
    
    // Replace the old report with the updated one
    models.esgReports[reportIndex] = updatedReport;
    
    res.json({
      data: updatedReport,
      message: 'ESG report updated successfully'
    });
  } catch (error) {
    console.error('Error in updateReport:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Delete an ESG report
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const deleteReport = (req, res) => {
  try {
    const { id } = req.params;
    
    // Find the report to delete
    const reportIndex = models.esgReports.findIndex(r => r.id === id);
    
    if (reportIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `ESG report with ID ${id} not found`
      });
    }
    
    // Remove the report from the collection
    models.esgReports.splice(reportIndex, 1);
    
    res.json({
      message: 'ESG report deleted successfully'
    });
  } catch (error) {
    console.error('Error in deleteReport:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get ESG metrics
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getMetrics = (req, res) => {
  try {
    const { category, year } = req.query;
    
    // Filter metrics based on query parameters
    let filteredMetrics = { ...models.esgMetrics };
    
    if (category) {
      filteredMetrics = {
        [category]: models.esgMetrics[category] || []
      };
    }
    
    if (year) {
      const yearNum = parseInt(year, 10);
      Object.keys(filteredMetrics).forEach(cat => {
        filteredMetrics[cat] = filteredMetrics[cat].filter(metric => metric.year === yearNum);
      });
    }
    
    res.json({ data: filteredMetrics });
  } catch (error) {
    console.error('Error in getMetrics:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get a list of ESG frameworks
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getFrameworks = (req, res) => {
  try {
    res.json({ data: models.esgFrameworks });
  } catch (error) {
    console.error('Error in getFrameworks:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get a specific ESG framework by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getFrameworkById = (req, res) => {
  try {
    const { id } = req.params;
    const framework = models.esgFrameworks.find(f => f.id === id);
    
    if (!framework) {
      return res.status(404).json({
        error: 'Not Found',
        message: `ESG framework with ID ${id} not found`
      });
    }
    
    res.json({ data: framework });
  } catch (error) {
    console.error('Error in getFrameworkById:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

module.exports = {
  getReports,
  getReportById,
  createReport,
  updateReport,
  deleteReport,
  getMetrics,
  getFrameworks,
  getFrameworkById
};
