// nova-agent.go
// NovaAgent Unified Runtime - Cross-Platform Executable
// NovaFuse Coherence Operating System

package main

import (
	"encoding/json"
	"flag"
	"fmt"
	"log"
	"net"
	"os"
	"os/exec"
	"os/signal"
	"path/filepath"
	"runtime"
	"sync"
	"syscall"
	"time"
)

// NovaAgent represents the unified runtime
type NovaAgent struct {
	Version     string            `json:"version"`
	Platform    string            `json:"platform"`
	Modules     map[string]Module `json:"modules"`
	Status      string            `json:"status"`
	StartTime   time.Time         `json:"start_time"`
	ConfigPath  string            `json:"config_path"`
	LogLevel    string            `json:"log_level"`
	mutex       sync.RWMutex
}

// Module interface for NovaFuse modules
type Module interface {
	Initialize() error
	Start() error
	Stop() error
	Status() string
	Health() bool
}

// NovaCore represents the core engine module
type NovaCore struct {
	Name        string `json:"name"`
	Path        string `json:"path"`
	Port        int    `json:"port"`
	Status      string `json:"status"`
	ProcessID   int    `json:"process_id"`
	HealthCheck string `json:"health_check"`
}

// Initialize NovaAgent
func NewNovaAgent() *NovaAgent {
	return &NovaAgent{
		Version:   "1.0.0",
		Platform:  runtime.GOOS + "/" + runtime.GOARCH,
		Modules:   make(map[string]Module),
		Status:    "INITIALIZING",
		StartTime: time.Now(),
		LogLevel:  "INFO",
	}
}

// Bootstrap the entire NovaFuse suite
func (na *NovaAgent) Bootstrap() error {
	na.logInfo("🚀 NovaAgent v%s Starting on %s...", na.Version, na.Platform)
	
	// Initialize configuration
	if err := na.loadConfiguration(); err != nil {
		return fmt.Errorf("failed to load configuration: %w", err)
	}
	
	// Start NovaCore engines
	if err := na.startNovaCore(); err != nil {
		return fmt.Errorf("failed to start NovaCore: %w", err)
	}
	
	na.Status = "RUNNING"
	na.logInfo("✅ NovaAgent bootstrap completed successfully")
	
	return nil
}

// Start NovaCore engines
func (na *NovaAgent) startNovaCore() error {
	na.logInfo("Starting NovaCore engines...")
	
	// Create NovaCore module
	novaCore := &NovaCore{
		Name:        "NovaCore",
		Path:        "nhetx-castl-alpha",
		Port:        8000,
		Status:      "RUNNING",
		ProcessID:   0,
		HealthCheck: "http://localhost:8000/health",
	}
	
	na.Modules["novacore"] = novaCore
	na.logInfo("✅ NovaCore engines started")
	
	return nil
}

// Load configuration
func (na *NovaAgent) loadConfiguration() error {
	na.ConfigPath = "novafuse-config.json"
	
	if _, err := os.Stat(na.ConfigPath); os.IsNotExist(err) {
		na.logInfo("Configuration file not found, using defaults")
		return nil
	}
	
	na.logInfo("✅ Configuration loaded from %s", na.ConfigPath)
	return nil
}

// Get system status
func (na *NovaAgent) GetStatus() map[string]interface{} {
	na.mutex.RLock()
	defer na.mutex.RUnlock()
	
	status := map[string]interface{}{
		"agent": map[string]interface{}{
			"version":    na.Version,
			"platform":   na.Platform,
			"status":     na.Status,
			"start_time": na.StartTime,
			"uptime":     time.Since(na.StartTime).String(),
		},
		"modules": make(map[string]interface{}),
	}
	
	for name, module := range na.Modules {
		status["modules"].(map[string]interface{})[name] = map[string]interface{}{
			"status": module.Status(),
			"health": module.Health(),
		}
	}
	
	return status
}

// Health check for all modules
func (na *NovaAgent) HealthCheck() bool {
	na.mutex.RLock()
	defer na.mutex.RUnlock()
	
	for _, module := range na.Modules {
		if !module.Health() {
			return false
		}
	}
	
	return true
}

// Shutdown NovaAgent gracefully
func (na *NovaAgent) Shutdown() error {
	na.logInfo("🛑 Shutting down NovaAgent...")
	
	na.mutex.Lock()
	defer na.mutex.Unlock()
	
	// Stop all modules
	for name, module := range na.Modules {
		na.logInfo("Stopping module: %s", name)
		if err := module.Stop(); err != nil {
			na.logError("Failed to stop module %s: %v", name, err)
		}
	}
	
	na.Status = "STOPPED"
	na.logInfo("✅ NovaAgent shutdown completed")
	
	return nil
}

// Logging functions
func (na *NovaAgent) logInfo(format string, args ...interface{}) {
	log.Printf("[INFO] [NovaAgent] "+format, args...)
}

func (na *NovaAgent) logError(format string, args ...interface{}) {
	log.Printf("[ERROR] [NovaAgent] "+format, args...)
}

func (na *NovaAgent) logDebug(format string, args ...interface{}) {
	if na.LogLevel == "DEBUG" {
		log.Printf("[DEBUG] [NovaAgent] "+format, args...)
	}
}

// Implement Module interface for NovaCore
func (nc *NovaCore) Initialize() error { return nil }
func (nc *NovaCore) Start() error { nc.Status = "RUNNING"; return nil }
func (nc *NovaCore) Stop() error { nc.Status = "STOPPED"; return nil }
func (nc *NovaCore) Status() string { return nc.Status }
func (nc *NovaCore) Health() bool { return nc.Status == "RUNNING" }

// Command line flag parsing
var (
	configPath = flag.String("config", "./novafuse-config.json", "Configuration file path")
	logLevel   = flag.String("log-level", "INFO", "Log level (DEBUG, INFO, WARN, ERROR)")
	mode       = flag.String("mode", "Enterprise", "Operating mode")
	healthCheck = flag.Bool("health-check", false, "Perform health check and exit")
	version    = flag.Bool("version", false, "Show version information")
	help       = flag.Bool("help", false, "Show help information")
)

// Show help information
func showHelp() {
	fmt.Println("NovaAgent - NovaFuse Coherence Operating System")
	fmt.Println("Usage: nova-agent [options]")
	fmt.Println("")
	fmt.Println("Options:")
	flag.PrintDefaults()
	fmt.Println("")
	fmt.Println("Examples:")
	fmt.Println("  nova-agent                                    # Start with defaults")
	fmt.Println("  nova-agent --config=/path/to/config.json     # Custom config")
	fmt.Println("  nova-agent --log-level=DEBUG                 # Debug mode")
	fmt.Println("  nova-agent --health-check                    # Health check only")
}

// Show version information
func showVersion() {
	fmt.Println("NovaAgent v1.0.0")
	fmt.Printf("Platform: %s/%s\n", runtime.GOOS, runtime.GOARCH)
	fmt.Println("NovaFuse Coherence Operating System")
	fmt.Println("Built with Go", runtime.Version())
}

// Perform health check
func performHealthCheck() int {
	fmt.Println("🔍 NovaAgent Health Check")

	// Check configuration file
	if _, err := os.Stat(*configPath); os.IsNotExist(err) {
		fmt.Printf("❌ Configuration file not found: %s\n", *configPath)
		return 1
	}
	fmt.Printf("✅ Configuration file: %s\n", *configPath)

	// Check required directories
	requiredDirs := []string{"nhetx-castl-alpha", "aeonix-divine-api", "chaeonix-divine-dashboard"}
	for _, dir := range requiredDirs {
		if _, err := os.Stat(dir); os.IsNotExist(err) {
			fmt.Printf("⚠️ Directory not found: %s\n", dir)
		} else {
			fmt.Printf("✅ Directory found: %s\n", dir)
		}
	}

	// Check ports availability
	ports := []int{8000, 8001, 3000}
	for _, port := range ports {
		if isPortAvailable(port) {
			fmt.Printf("✅ Port %d: Available\n", port)
		} else {
			fmt.Printf("⚠️ Port %d: In use\n", port)
		}
	}

	fmt.Println("🎯 Health check completed")
	return 0
}

// Check if port is available
func isPortAvailable(port int) bool {
	ln, err := net.Listen("tcp", fmt.Sprintf(":%d", port))
	if err != nil {
		return false
	}
	ln.Close()
	return true
}

// Signal handling for graceful shutdown
func setupSignalHandling(agent *NovaAgent) {
	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt, syscall.SIGTERM)

	go func() {
		<-c
		fmt.Println("\n🛑 Received shutdown signal...")
		agent.Shutdown()
		os.Exit(0)
	}()
}

// Main entry point
func main() {
	flag.Parse()

	// Handle command line flags
	if *help {
		showHelp()
		return
	}

	if *version {
		showVersion()
		return
	}

	if *healthCheck {
		os.Exit(performHealthCheck())
	}

	fmt.Println("🚀 NovaAgent - NovaFuse Coherence Operating System")
	fmt.Println("Version: 1.0.0")
	fmt.Printf("Platform: %s/%s\n", runtime.GOOS, runtime.GOARCH)
	fmt.Printf("Mode: %s\n", *mode)
	fmt.Printf("Config: %s\n", *configPath)
	fmt.Println("==========================================")

	// Create and bootstrap NovaAgent
	agent := NewNovaAgent()
	agent.ConfigPath = *configPath
	agent.LogLevel = *logLevel

	// Setup signal handling
	setupSignalHandling(agent)

	if err := agent.Bootstrap(); err != nil {
		log.Fatalf("❌ NovaAgent bootstrap failed: %v", err)
	}

	// Keep running and handle signals
	fmt.Println("✅ NovaAgent is running. Press Ctrl+C to stop.")

	// Status monitoring loop
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			if agent.HealthCheck() {
				agent.logInfo("💚 All modules healthy")
			} else {
				agent.logError("💔 Some modules unhealthy")
			}

			// Print status in debug mode
			if *logLevel == "DEBUG" {
				status := agent.GetStatus()
				statusJSON, _ := json.MarshalIndent(status, "", "  ")
				agent.logDebug("Status: %s", string(statusJSON))
			}
		}
	}
}
