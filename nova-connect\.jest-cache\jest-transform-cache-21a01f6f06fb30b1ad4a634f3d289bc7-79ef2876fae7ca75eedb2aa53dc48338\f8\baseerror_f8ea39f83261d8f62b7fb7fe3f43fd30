40471381a5220c5443cdb2e39f6ff858
/**
 * NovaFuse Universal API Connector - Base Error
 * 
 * This module defines the base error class for the UAC.
 * All UAC-specific errors should extend this class.
 */

/**
 * Base error class for all UAC errors
 * @class UAConnectorError
 * @extends Error
 */
class UAConnectorError extends Error {
  /**
   * Create a new UAConnectorError
   * 
   * @param {string} message - Error message
   * @param {Object} options - Error options
   * @param {string} options.code - Error code
   * @param {string} options.severity - Error severity (info, warning, error, critical)
   * @param {Object} options.context - Additional context for the error
   * @param {Error} options.cause - The error that caused this error
   */
  constructor(message, options = {}) {
    super(message);

    // Set the name to the class name
    this.name = this.constructor.name;

    // Set error properties
    this.code = options.code || 'UAC_ERROR';
    this.severity = options.severity || 'error';
    this.context = options.context || {};
    this.cause = options.cause;
    this.timestamp = new Date().toISOString();

    // Generate a unique error ID
    this.errorId = this._generateErrorId();

    // Capture stack trace
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor);
    }
  }

  /**
   * Generate a unique error ID
   * 
   * @returns {string} - Unique error ID
   * @private
   */
  _generateErrorId() {
    const timestamp = Date.now().toString(36);
    const randomStr = Math.random().toString(36).substring(2, 10);
    return `${timestamp}-${randomStr}`;
  }

  /**
   * Convert the error to a JSON object
   * 
   * @param {boolean} includeStack - Whether to include the stack trace
   * @returns {Object} - JSON representation of the error
   */
  toJSON(includeStack = false) {
    const json = {
      errorId: this.errorId,
      name: this.name,
      message: this.message,
      code: this.code,
      severity: this.severity,
      timestamp: this.timestamp,
      context: this.context
    };
    if (includeStack) {
      json.stack = this.stack;
    }
    if (this.cause) {
      json.cause = this.cause instanceof Error ? {
        name: this.cause.name,
        message: this.cause.message,
        ...(includeStack ? {
          stack: this.cause.stack
        } : {})
      } : this.cause;
    }
    return json;
  }

  /**
   * Get a user-friendly error message
   * 
   * @returns {string} - User-friendly error message
   */
  getUserMessage() {
    return this.message;
  }

  /**
   * Get a developer-friendly error message with more details
   * 
   * @returns {string} - Developer-friendly error message
   */
  getDeveloperMessage() {
    return `[${this.code}] ${this.message}`;
  }
}
module.exports = UAConnectorError;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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