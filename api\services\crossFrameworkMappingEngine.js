/**
 * Cross-Framework Mapping Engine
 * 
 * This service is responsible for mapping controls and requirements between different compliance frameworks.
 * It provides a standardized interface for cross-framework mapping, control mapping, and requirement mapping.
 */

const logger = require('../utils/logger');

/**
 * Framework definitions with their control categories
 * This is a simplified version for demonstration purposes
 */
const frameworks = {
  'gdpr': {
    name: 'General Data Protection Regulation',
    version: '2016/679',
    categories: [
      'Data Subject Rights',
      'Consent',
      'Data Protection',
      'Data Processing',
      'Data Transfer',
      'Breach Notification',
      'Accountability'
    ]
  },
  'hipaa': {
    name: 'Health Insurance Portability and Accountability Act',
    version: '1996',
    categories: [
      'Privacy Rule',
      'Security Rule',
      'Breach Notification Rule',
      'Enforcement Rule'
    ]
  },
  'soc2': {
    name: 'Service Organization Control 2',
    version: '2017',
    categories: [
      'Security',
      'Availability',
      'Processing Integrity',
      'Confidentiality',
      'Privacy'
    ]
  },
  'pci-dss': {
    name: 'Payment Card Industry Data Security Standard',
    version: '4.0',
    categories: [
      'Build and Maintain a Secure Network',
      'Protect Cardholder Data',
      'Maintain a Vulnerability Management Program',
      'Implement Strong Access Control Measures',
      'Regularly Monitor and Test Networks',
      'Maintain an Information Security Policy'
    ]
  },
  'iso-27001': {
    name: 'ISO/IEC 27001',
    version: '2013',
    categories: [
      'Information Security Policies',
      'Organization of Information Security',
      'Human Resource Security',
      'Asset Management',
      'Access Control',
      'Cryptography',
      'Physical and Environmental Security',
      'Operations Security',
      'Communications Security',
      'System Acquisition, Development and Maintenance',
      'Supplier Relationships',
      'Information Security Incident Management',
      'Information Security Aspects of Business Continuity Management',
      'Compliance'
    ]
  },
  'ccpa': {
    name: 'California Consumer Privacy Act',
    version: '2018',
    categories: [
      'Notice',
      'Right to Access',
      'Right to Delete',
      'Right to Opt-Out',
      'Non-Discrimination',
      'Data Security'
    ]
  },
  'nist': {
    name: 'NIST Cybersecurity Framework',
    version: '1.1',
    categories: [
      'Identify',
      'Protect',
      'Detect',
      'Respond',
      'Recover'
    ]
  },
  'finra': {
    name: 'Financial Industry Regulatory Authority',
    version: '2023',
    categories: [
      'Supervision',
      'Communications with the Public',
      'Trade Reporting',
      'Customer Protection',
      'Financial and Operational Rules',
      'Recordkeeping Requirements'
    ]
  },
  'fedramp': {
    name: 'Federal Risk and Authorization Management Program',
    version: '2.0',
    categories: [
      'Access Control',
      'Awareness and Training',
      'Audit and Accountability',
      'Security Assessment and Authorization',
      'Configuration Management',
      'Contingency Planning',
      'Identification and Authentication',
      'Incident Response',
      'Maintenance',
      'Media Protection',
      'Physical and Environmental Protection',
      'Planning',
      'Personnel Security',
      'Risk Assessment',
      'System and Services Acquisition',
      'System and Communications Protection',
      'System and Information Integrity'
    ]
  }
};

/**
 * Cross-framework mapping database
 * This is a simplified version for demonstration purposes
 * In a real implementation, this would be stored in a database
 * 
 * The mapping is structured as:
 * {
 *   sourceFramework: {
 *     sourceControl: {
 *       targetFramework: {
 *         targetControls: [control1, control2, ...],
 *         mappingStrength: 'full' | 'partial' | 'related'
 *       }
 *     }
 *   }
 * }
 */
const frameworkMappings = {
  'gdpr': {
    'Article 32 - Security of processing': {
      'soc2': {
        targetControls: ['CC6.1', 'CC6.2', 'CC6.3'],
        mappingStrength: 'partial'
      },
      'hipaa': {
        targetControls: ['164.308(a)(1)(ii)(A)', '164.308(a)(1)(ii)(B)'],
        mappingStrength: 'partial'
      },
      'iso-27001': {
        targetControls: ['A.14.2.1', 'A.14.2.5', 'A.14.2.6'],
        mappingStrength: 'full'
      }
    },
    'Article 33 - Notification of a personal data breach': {
      'hipaa': {
        targetControls: ['164.308(a)(6)(ii)', '164.404'],
        mappingStrength: 'full'
      },
      'soc2': {
        targetControls: ['CC7.3', 'CC7.4', 'CC7.5'],
        mappingStrength: 'partial'
      }
    }
  },
  'hipaa': {
    '164.308(a)(1)(ii)(A) - Risk Analysis': {
      'soc2': {
        targetControls: ['CC3.1', 'CC3.2', 'CC3.3'],
        mappingStrength: 'partial'
      },
      'iso-27001': {
        targetControls: ['A.8.2.1', 'A.8.2.2', 'A.8.2.3'],
        mappingStrength: 'full'
      },
      'nist': {
        targetControls: ['ID.RA-1', 'ID.RA-2', 'ID.RA-3'],
        mappingStrength: 'full'
      }
    }
  },
  'soc2': {
    'CC6.1 - Logical Access Security': {
      'hipaa': {
        targetControls: ['164.308(a)(4)', '164.312(a)(1)'],
        mappingStrength: 'partial'
      },
      'pci-dss': {
        targetControls: ['Requirement 7', 'Requirement 8'],
        mappingStrength: 'full'
      },
      'iso-27001': {
        targetControls: ['A.9.1.1', 'A.9.1.2', 'A.9.2.1'],
        mappingStrength: 'full'
      }
    }
  }
};

/**
 * Get all available frameworks
 * @returns {Object} - All available frameworks
 */
const getAllFrameworks = () => {
  return frameworks;
};

/**
 * Get a specific framework by ID
 * @param {string} frameworkId - ID of the framework to retrieve
 * @returns {Object} - Framework details
 */
const getFramework = (frameworkId) => {
  if (!frameworks[frameworkId]) {
    throw new Error(`Framework ${frameworkId} not found`);
  }
  
  return frameworks[frameworkId];
};

/**
 * Map a control from one framework to another
 * @param {string} sourceFramework - Source framework ID
 * @param {string} sourceControl - Source control ID
 * @param {string} targetFramework - Target framework ID
 * @returns {Object} - Mapping result
 */
const mapControl = (sourceFramework, sourceControl, targetFramework) => {
  logger.info(`Mapping control ${sourceControl} from ${sourceFramework} to ${targetFramework}`);
  
  try {
    // Check if frameworks exist
    if (!frameworks[sourceFramework]) {
      throw new Error(`Source framework ${sourceFramework} not found`);
    }
    
    if (!frameworks[targetFramework]) {
      throw new Error(`Target framework ${targetFramework} not found`);
    }
    
    // Check if mapping exists
    if (!frameworkMappings[sourceFramework] || 
        !frameworkMappings[sourceFramework][sourceControl] || 
        !frameworkMappings[sourceFramework][sourceControl][targetFramework]) {
      
      // Return empty mapping if not found
      return {
        sourceFramework,
        sourceControl,
        targetFramework,
        targetControls: [],
        mappingStrength: 'none',
        confidence: 0
      };
    }
    
    // Get mapping
    const mapping = frameworkMappings[sourceFramework][sourceControl][targetFramework];
    
    // Calculate confidence based on mapping strength
    let confidence = 0;
    switch (mapping.mappingStrength) {
      case 'full':
        confidence = 1.0;
        break;
      case 'partial':
        confidence = 0.7;
        break;
      case 'related':
        confidence = 0.4;
        break;
      default:
        confidence = 0;
    }
    
    return {
      sourceFramework,
      sourceControl,
      targetFramework,
      targetControls: mapping.targetControls,
      mappingStrength: mapping.mappingStrength,
      confidence
    };
  } catch (error) {
    logger.error(`Error mapping control: ${error.message}`);
    throw error;
  }
};

/**
 * Map multiple controls from one framework to another
 * @param {string} sourceFramework - Source framework ID
 * @param {Array<string>} sourceControls - Array of source control IDs
 * @param {string} targetFramework - Target framework ID
 * @returns {Array<Object>} - Array of mapping results
 */
const mapControls = (sourceFramework, sourceControls, targetFramework) => {
  logger.info(`Mapping ${sourceControls.length} controls from ${sourceFramework} to ${targetFramework}`);
  
  try {
    return sourceControls.map(sourceControl => 
      mapControl(sourceFramework, sourceControl, targetFramework)
    );
  } catch (error) {
    logger.error(`Error mapping controls: ${error.message}`);
    throw error;
  }
};

/**
 * Get all possible mappings for a control
 * @param {string} sourceFramework - Source framework ID
 * @param {string} sourceControl - Source control ID
 * @returns {Object} - All possible mappings for the control
 */
const getAllMappingsForControl = (sourceFramework, sourceControl) => {
  logger.info(`Getting all mappings for control ${sourceControl} from ${sourceFramework}`);
  
  try {
    // Check if framework exists
    if (!frameworks[sourceFramework]) {
      throw new Error(`Source framework ${sourceFramework} not found`);
    }
    
    // Check if control has any mappings
    if (!frameworkMappings[sourceFramework] || 
        !frameworkMappings[sourceFramework][sourceControl]) {
      
      // Return empty mappings if not found
      return {
        sourceFramework,
        sourceControl,
        mappings: {}
      };
    }
    
    // Get all mappings for the control
    const controlMappings = frameworkMappings[sourceFramework][sourceControl];
    
    // Format the response
    const result = {
      sourceFramework,
      sourceControl,
      mappings: {}
    };
    
    // Add mappings for each target framework
    Object.keys(controlMappings).forEach(targetFramework => {
      const mapping = controlMappings[targetFramework];
      
      // Calculate confidence based on mapping strength
      let confidence = 0;
      switch (mapping.mappingStrength) {
        case 'full':
          confidence = 1.0;
          break;
        case 'partial':
          confidence = 0.7;
          break;
        case 'related':
          confidence = 0.4;
          break;
        default:
          confidence = 0;
      }
      
      result.mappings[targetFramework] = {
        targetControls: mapping.targetControls,
        mappingStrength: mapping.mappingStrength,
        confidence
      };
    });
    
    return result;
  } catch (error) {
    logger.error(`Error getting all mappings for control: ${error.message}`);
    throw error;
  }
};

/**
 * Get all mappings between two frameworks
 * @param {string} sourceFramework - Source framework ID
 * @param {string} targetFramework - Target framework ID
 * @returns {Object} - All mappings between the two frameworks
 */
const getFrameworkToFrameworkMappings = (sourceFramework, targetFramework) => {
  logger.info(`Getting all mappings from ${sourceFramework} to ${targetFramework}`);
  
  try {
    // Check if frameworks exist
    if (!frameworks[sourceFramework]) {
      throw new Error(`Source framework ${sourceFramework} not found`);
    }
    
    if (!frameworks[targetFramework]) {
      throw new Error(`Target framework ${targetFramework} not found`);
    }
    
    // Initialize result
    const result = {
      sourceFramework,
      targetFramework,
      mappings: {}
    };
    
    // Check if source framework has any mappings
    if (!frameworkMappings[sourceFramework]) {
      return result;
    }
    
    // Get all controls in the source framework
    const sourceControls = Object.keys(frameworkMappings[sourceFramework]);
    
    // For each control, check if it has a mapping to the target framework
    sourceControls.forEach(sourceControl => {
      if (frameworkMappings[sourceFramework][sourceControl][targetFramework]) {
        const mapping = frameworkMappings[sourceFramework][sourceControl][targetFramework];
        
        // Calculate confidence based on mapping strength
        let confidence = 0;
        switch (mapping.mappingStrength) {
          case 'full':
            confidence = 1.0;
            break;
          case 'partial':
            confidence = 0.7;
            break;
          case 'related':
            confidence = 0.4;
            break;
          default:
            confidence = 0;
        }
        
        result.mappings[sourceControl] = {
          targetControls: mapping.targetControls,
          mappingStrength: mapping.mappingStrength,
          confidence
        };
      }
    });
    
    return result;
  } catch (error) {
    logger.error(`Error getting framework-to-framework mappings: ${error.message}`);
    throw error;
  }
};

/**
 * Calculate compliance coverage across frameworks
 * @param {string} sourceFramework - Source framework ID
 * @param {Array<string>} implementedControls - Array of implemented control IDs
 * @param {string} targetFramework - Target framework ID
 * @returns {Object} - Compliance coverage calculation
 */
const calculateComplianceCoverage = (sourceFramework, implementedControls, targetFramework) => {
  logger.info(`Calculating compliance coverage from ${sourceFramework} to ${targetFramework}`);
  
  try {
    // Map each implemented control to the target framework
    const mappings = mapControls(sourceFramework, implementedControls, targetFramework);
    
    // Get all unique target controls that are covered
    const coveredTargetControls = new Set();
    mappings.forEach(mapping => {
      mapping.targetControls.forEach(control => {
        coveredTargetControls.add(control);
      });
    });
    
    // Calculate coverage metrics
    // In a real implementation, we would have a complete list of all controls in each framework
    // For this example, we'll use a simplified approach
    const totalTargetControls = 100; // Placeholder value
    const coverage = coveredTargetControls.size / totalTargetControls;
    
    // Calculate weighted coverage based on mapping strength
    let weightedCoverage = 0;
    mappings.forEach(mapping => {
      const weight = mapping.confidence;
      weightedCoverage += (mapping.targetControls.length * weight) / totalTargetControls;
    });
    
    return {
      sourceFramework,
      targetFramework,
      implementedControls: implementedControls.length,
      coveredTargetControls: coveredTargetControls.size,
      totalTargetControls,
      coverage,
      weightedCoverage,
      mappings
    };
  } catch (error) {
    logger.error(`Error calculating compliance coverage: ${error.message}`);
    throw error;
  }
};

module.exports = {
  getAllFrameworks,
  getFramework,
  mapControl,
  mapControls,
  getAllMappingsForControl,
  getFrameworkToFrameworkMappings,
  calculateComplianceCoverage
};
