c975a5dfb969d8664676049501700c61
/**
 * NovaFuse Universal API Connector - Errors
 * 
 * This module exports all error types for the UAC.
 */

const UAConnectorError = require('./base-error');
const AuthenticationErrors = require('./authentication-error');
const ConnectionErrors = require('./connection-error');
const ValidationErrors = require('./validation-error');
const ApiErrors = require('./api-error');
const TransformationErrors = require('./transformation-error');
const ConnectorErrors = require('./connector-error');
module.exports = {
  // Base error
  UAConnectorError,
  // Authentication errors
  ...AuthenticationErrors,
  // Connection errors
  ...ConnectionErrors,
  // Validation errors
  ...ValidationErrors,
  // API errors
  ...ApiErrors,
  // Transformation errors
  ...TransformationErrors,
  // Connector errors
  ...ConnectorErrors
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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