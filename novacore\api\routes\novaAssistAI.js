/**
 * NovaAssistAI Routes
 *
 * This file defines the API routes for the NovaAssistAI chatbot.
 */

const express = require('express');
const router = express.Router();
const novaAssistAIController = require('../controllers/novaAssistAIController');
const fileUploadController = require('../controllers/fileUploadController');
const auth = require('../middleware/auth');

/**
 * @route POST /api/v1/nova-assist
 * @desc Process a message from the user and generate a response
 * @access Private
 */
router.post('/', auth, novaAssistAIController.processMessage);

/**
 * @route POST /api/v1/nova-assist/actions
 * @desc Execute an action suggested by NovaAssistAI
 * @access Private
 */
router.post('/actions', auth, novaAssistAIController.executeAction);

/**
 * @route POST /api/v1/nova-assist/end
 * @desc End the current conversation
 * @access Private
 */
router.post('/end', auth, novaAssistAIController.endConversation);

/**
 * @route GET /api/v1/nova-assist/history
 * @desc Get conversation history
 * @access Private
 */
router.get('/history', auth, novaAssistAIController.getConversationHistory);

/**
 * @route POST /api/v1/nova-assist/upload
 * @desc Upload a file for NovaAssistAI
 * @access Private
 */
router.post('/upload', auth, fileUploadController.uploadFile);

module.exports = router;
