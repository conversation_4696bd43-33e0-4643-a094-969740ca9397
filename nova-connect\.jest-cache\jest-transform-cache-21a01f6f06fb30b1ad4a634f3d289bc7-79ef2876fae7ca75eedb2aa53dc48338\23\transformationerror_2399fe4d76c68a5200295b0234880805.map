{"version": 3, "names": ["UAConnectorError", "require", "TransformationError", "constructor", "message", "options", "code", "severity", "context", "cause", "transformationId", "sourceData", "getUserMessage", "toJSON", "includeStack", "json", "process", "env", "NODE_ENV", "MissingSourceFieldError", "fieldPath", "InvalidTransformationFunctionError", "functionName", "DataTypeConversionError", "sourceType", "targetType", "module", "exports"], "sources": ["transformation-error.js"], "sourcesContent": ["/**\n * NovaFuse Universal API Connector - Transformation Error\n * \n * This module defines data transformation-related errors for the UAC.\n */\n\nconst UAConnectorError = require('./base-error');\n\n/**\n * Error class for data transformation failures\n * @class TransformationError\n * @extends UAConnectorError\n */\nclass TransformationError extends UAConnectorError {\n  /**\n   * Create a new TransformationError\n   * \n   * @param {string} message - Error message\n   * @param {Object} options - Error options\n   * @param {string} options.code - Error code\n   * @param {string} options.severity - Error severity\n   * @param {Object} options.context - Additional context for the error\n   * @param {Error} options.cause - The error that caused this error\n   * @param {string} options.transformationId - ID of the transformation\n   * @param {string} options.sourceData - Source data that failed transformation\n   */\n  constructor(message, options = {}) {\n    super(message, {\n      code: options.code || 'TRANSFORMATION_ERROR',\n      severity: options.severity || 'error',\n      context: options.context || {},\n      cause: options.cause\n    });\n    \n    this.transformationId = options.transformationId;\n    this.sourceData = options.sourceData;\n  }\n\n  /**\n   * Get a user-friendly error message\n   * \n   * @returns {string} - User-friendly error message\n   */\n  getUserMessage() {\n    return 'An error occurred while processing the data. Please contact support if the issue persists.';\n  }\n\n  /**\n   * Convert the error to a JSON object\n   * \n   * @param {boolean} includeStack - Whether to include the stack trace\n   * @returns {Object} - JSON representation of the error\n   */\n  toJSON(includeStack = false) {\n    const json = super.toJSON(includeStack);\n    \n    if (this.transformationId) {\n      json.transformationId = this.transformationId;\n    }\n    \n    // Only include source data in developer mode to avoid leaking sensitive information\n    if (process.env.NODE_ENV === 'development' && this.sourceData) {\n      json.sourceData = this.sourceData;\n    }\n    \n    return json;\n  }\n}\n\n/**\n * Error class for missing source field errors\n * @class MissingSourceFieldError\n * @extends TransformationError\n */\nclass MissingSourceFieldError extends TransformationError {\n  /**\n   * Create a new MissingSourceFieldError\n   * \n   * @param {string} fieldPath - The path to the missing field\n   * @param {Object} options - Error options\n   */\n  constructor(fieldPath, options = {}) {\n    const message = `Missing source field: ${fieldPath}`;\n    \n    super(message, {\n      code: options.code || 'TRANSFORMATION_MISSING_SOURCE_FIELD',\n      severity: options.severity || 'error',\n      context: { ...options.context, fieldPath },\n      cause: options.cause,\n      transformationId: options.transformationId,\n      sourceData: options.sourceData\n    });\n    \n    this.fieldPath = fieldPath;\n  }\n\n  /**\n   * Get a user-friendly error message\n   * \n   * @returns {string} - User-friendly error message\n   */\n  getUserMessage() {\n    return 'An error occurred while processing the data. A required field is missing.';\n  }\n\n  /**\n   * Convert the error to a JSON object\n   * \n   * @param {boolean} includeStack - Whether to include the stack trace\n   * @returns {Object} - JSON representation of the error\n   */\n  toJSON(includeStack = false) {\n    const json = super.toJSON(includeStack);\n    json.fieldPath = this.fieldPath;\n    return json;\n  }\n}\n\n/**\n * Error class for invalid transformation function errors\n * @class InvalidTransformationFunctionError\n * @extends TransformationError\n */\nclass InvalidTransformationFunctionError extends TransformationError {\n  /**\n   * Create a new InvalidTransformationFunctionError\n   * \n   * @param {string} functionName - The name of the invalid function\n   * @param {Object} options - Error options\n   */\n  constructor(functionName, options = {}) {\n    const message = `Invalid transformation function: ${functionName}`;\n    \n    super(message, {\n      code: options.code || 'TRANSFORMATION_INVALID_FUNCTION',\n      severity: options.severity || 'error',\n      context: { ...options.context, functionName },\n      cause: options.cause,\n      transformationId: options.transformationId,\n      sourceData: options.sourceData\n    });\n    \n    this.functionName = functionName;\n  }\n\n  /**\n   * Get a user-friendly error message\n   * \n   * @returns {string} - User-friendly error message\n   */\n  getUserMessage() {\n    return 'An error occurred while processing the data. The transformation configuration is invalid.';\n  }\n\n  /**\n   * Convert the error to a JSON object\n   * \n   * @param {boolean} includeStack - Whether to include the stack trace\n   * @returns {Object} - JSON representation of the error\n   */\n  toJSON(includeStack = false) {\n    const json = super.toJSON(includeStack);\n    json.functionName = this.functionName;\n    return json;\n  }\n}\n\n/**\n * Error class for data type conversion errors\n * @class DataTypeConversionError\n * @extends TransformationError\n */\nclass DataTypeConversionError extends TransformationError {\n  /**\n   * Create a new DataTypeConversionError\n   * \n   * @param {string} fieldPath - The path to the field\n   * @param {string} sourceType - The source data type\n   * @param {string} targetType - The target data type\n   * @param {Object} options - Error options\n   */\n  constructor(fieldPath, sourceType, targetType, options = {}) {\n    const message = `Cannot convert field ${fieldPath} from ${sourceType} to ${targetType}`;\n    \n    super(message, {\n      code: options.code || 'TRANSFORMATION_DATA_TYPE_CONVERSION',\n      severity: options.severity || 'error',\n      context: { ...options.context, fieldPath, sourceType, targetType },\n      cause: options.cause,\n      transformationId: options.transformationId,\n      sourceData: options.sourceData\n    });\n    \n    this.fieldPath = fieldPath;\n    this.sourceType = sourceType;\n    this.targetType = targetType;\n  }\n\n  /**\n   * Get a user-friendly error message\n   * \n   * @returns {string} - User-friendly error message\n   */\n  getUserMessage() {\n    return 'An error occurred while processing the data. A data type conversion failed.';\n  }\n\n  /**\n   * Convert the error to a JSON object\n   * \n   * @param {boolean} includeStack - Whether to include the stack trace\n   * @returns {Object} - JSON representation of the error\n   */\n  toJSON(includeStack = false) {\n    const json = super.toJSON(includeStack);\n    json.fieldPath = this.fieldPath;\n    json.sourceType = this.sourceType;\n    json.targetType = this.targetType;\n    return json;\n  }\n}\n\nmodule.exports = {\n  TransformationError,\n  MissingSourceFieldError,\n  InvalidTransformationFunctionError,\n  DataTypeConversionError\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,MAAMA,gBAAgB,GAAGC,OAAO,CAAC,cAAc,CAAC;;AAEhD;AACA;AACA;AACA;AACA;AACA,MAAMC,mBAAmB,SAASF,gBAAgB,CAAC;EACjD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEG,WAAWA,CAACC,OAAO,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IACjC,KAAK,CAACD,OAAO,EAAE;MACbE,IAAI,EAAED,OAAO,CAACC,IAAI,IAAI,sBAAsB;MAC5CC,QAAQ,EAAEF,OAAO,CAACE,QAAQ,IAAI,OAAO;MACrCC,OAAO,EAAEH,OAAO,CAACG,OAAO,IAAI,CAAC,CAAC;MAC9BC,KAAK,EAAEJ,OAAO,CAACI;IACjB,CAAC,CAAC;IAEF,IAAI,CAACC,gBAAgB,GAAGL,OAAO,CAACK,gBAAgB;IAChD,IAAI,CAACC,UAAU,GAAGN,OAAO,CAACM,UAAU;EACtC;;EAEA;AACF;AACA;AACA;AACA;EACEC,cAAcA,CAAA,EAAG;IACf,OAAO,4FAA4F;EACrG;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEC,MAAMA,CAACC,YAAY,GAAG,KAAK,EAAE;IAC3B,MAAMC,IAAI,GAAG,KAAK,CAACF,MAAM,CAACC,YAAY,CAAC;IAEvC,IAAI,IAAI,CAACJ,gBAAgB,EAAE;MACzBK,IAAI,CAACL,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;IAC/C;;IAEA;IACA,IAAIM,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,IAAI,IAAI,CAACP,UAAU,EAAE;MAC7DI,IAAI,CAACJ,UAAU,GAAG,IAAI,CAACA,UAAU;IACnC;IAEA,OAAOI,IAAI;EACb;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMI,uBAAuB,SAASjB,mBAAmB,CAAC;EACxD;AACF;AACA;AACA;AACA;AACA;EACEC,WAAWA,CAACiB,SAAS,EAAEf,OAAO,GAAG,CAAC,CAAC,EAAE;IACnC,MAAMD,OAAO,GAAG,yBAAyBgB,SAAS,EAAE;IAEpD,KAAK,CAAChB,OAAO,EAAE;MACbE,IAAI,EAAED,OAAO,CAACC,IAAI,IAAI,qCAAqC;MAC3DC,QAAQ,EAAEF,OAAO,CAACE,QAAQ,IAAI,OAAO;MACrCC,OAAO,EAAE;QAAE,GAAGH,OAAO,CAACG,OAAO;QAAEY;MAAU,CAAC;MAC1CX,KAAK,EAAEJ,OAAO,CAACI,KAAK;MACpBC,gBAAgB,EAAEL,OAAO,CAACK,gBAAgB;MAC1CC,UAAU,EAAEN,OAAO,CAACM;IACtB,CAAC,CAAC;IAEF,IAAI,CAACS,SAAS,GAAGA,SAAS;EAC5B;;EAEA;AACF;AACA;AACA;AACA;EACER,cAAcA,CAAA,EAAG;IACf,OAAO,2EAA2E;EACpF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEC,MAAMA,CAACC,YAAY,GAAG,KAAK,EAAE;IAC3B,MAAMC,IAAI,GAAG,KAAK,CAACF,MAAM,CAACC,YAAY,CAAC;IACvCC,IAAI,CAACK,SAAS,GAAG,IAAI,CAACA,SAAS;IAC/B,OAAOL,IAAI;EACb;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMM,kCAAkC,SAASnB,mBAAmB,CAAC;EACnE;AACF;AACA;AACA;AACA;AACA;EACEC,WAAWA,CAACmB,YAAY,EAAEjB,OAAO,GAAG,CAAC,CAAC,EAAE;IACtC,MAAMD,OAAO,GAAG,oCAAoCkB,YAAY,EAAE;IAElE,KAAK,CAAClB,OAAO,EAAE;MACbE,IAAI,EAAED,OAAO,CAACC,IAAI,IAAI,iCAAiC;MACvDC,QAAQ,EAAEF,OAAO,CAACE,QAAQ,IAAI,OAAO;MACrCC,OAAO,EAAE;QAAE,GAAGH,OAAO,CAACG,OAAO;QAAEc;MAAa,CAAC;MAC7Cb,KAAK,EAAEJ,OAAO,CAACI,KAAK;MACpBC,gBAAgB,EAAEL,OAAO,CAACK,gBAAgB;MAC1CC,UAAU,EAAEN,OAAO,CAACM;IACtB,CAAC,CAAC;IAEF,IAAI,CAACW,YAAY,GAAGA,YAAY;EAClC;;EAEA;AACF;AACA;AACA;AACA;EACEV,cAAcA,CAAA,EAAG;IACf,OAAO,2FAA2F;EACpG;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEC,MAAMA,CAACC,YAAY,GAAG,KAAK,EAAE;IAC3B,MAAMC,IAAI,GAAG,KAAK,CAACF,MAAM,CAACC,YAAY,CAAC;IACvCC,IAAI,CAACO,YAAY,GAAG,IAAI,CAACA,YAAY;IACrC,OAAOP,IAAI;EACb;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMQ,uBAAuB,SAASrB,mBAAmB,CAAC;EACxD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEC,WAAWA,CAACiB,SAAS,EAAEI,UAAU,EAAEC,UAAU,EAAEpB,OAAO,GAAG,CAAC,CAAC,EAAE;IAC3D,MAAMD,OAAO,GAAG,wBAAwBgB,SAAS,SAASI,UAAU,OAAOC,UAAU,EAAE;IAEvF,KAAK,CAACrB,OAAO,EAAE;MACbE,IAAI,EAAED,OAAO,CAACC,IAAI,IAAI,qCAAqC;MAC3DC,QAAQ,EAAEF,OAAO,CAACE,QAAQ,IAAI,OAAO;MACrCC,OAAO,EAAE;QAAE,GAAGH,OAAO,CAACG,OAAO;QAAEY,SAAS;QAAEI,UAAU;QAAEC;MAAW,CAAC;MAClEhB,KAAK,EAAEJ,OAAO,CAACI,KAAK;MACpBC,gBAAgB,EAAEL,OAAO,CAACK,gBAAgB;MAC1CC,UAAU,EAAEN,OAAO,CAACM;IACtB,CAAC,CAAC;IAEF,IAAI,CAACS,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACI,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,UAAU,GAAGA,UAAU;EAC9B;;EAEA;AACF;AACA;AACA;AACA;EACEb,cAAcA,CAAA,EAAG;IACf,OAAO,6EAA6E;EACtF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEC,MAAMA,CAACC,YAAY,GAAG,KAAK,EAAE;IAC3B,MAAMC,IAAI,GAAG,KAAK,CAACF,MAAM,CAACC,YAAY,CAAC;IACvCC,IAAI,CAACK,SAAS,GAAG,IAAI,CAACA,SAAS;IAC/BL,IAAI,CAACS,UAAU,GAAG,IAAI,CAACA,UAAU;IACjCT,IAAI,CAACU,UAAU,GAAG,IAAI,CAACA,UAAU;IACjC,OAAOV,IAAI;EACb;AACF;AAEAW,MAAM,CAACC,OAAO,GAAG;EACfzB,mBAAmB;EACnBiB,uBAAuB;EACvBE,kCAAkC;EAClCE;AACF,CAAC", "ignoreList": []}