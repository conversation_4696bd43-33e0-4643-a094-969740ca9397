const Joi = require('joi');

// Validation schemas
const schemas = {
  createMetric: Joi.object({
    name: Joi.string().required().min(3).max(100),
    description: Joi.string().optional().max(500),
    category: Joi.string().required().valid('environmental', 'social', 'governance'),
    subcategory: Joi.string().optional().max(50),
    unit: Joi.string().optional().max(20),
    dataType: Joi.string().required().valid('numeric', 'percentage', 'boolean', 'text', 'date'),
    isStandardized: Joi.boolean().optional(),
    standardId: Joi.string().optional().max(50),
    frameworkMappings: Joi.array().optional().items(
      Joi.object({
        frameworkId: Joi.string().required(),
        elementId: Joi.string().required(),
        version: Joi.string().optional()
      })
    ),
    calculationMethod: Joi.string().optional().max(200),
    calculationFormula: Joi.string().optional().max(200),
    dataCollectionFrequency: Joi.string().optional().valid('daily', 'weekly', 'monthly', 'quarterly', 'annually', 'custom'),
    verificationRequired: Joi.boolean().optional(),
    verificationProcess: Joi.string().optional().max(200),
    benchmarkValue: Joi.string().optional().max(50),
    benchmarkSource: Joi.string().optional().max(100),
    framework: Joi.string().optional().max(50), // Legacy field, kept for backward compatibility
    targetValue: Joi.string().optional().max(50),
    targetDate: Joi.string().optional().pattern(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
    owner: Joi.string().optional().max(100),
    status: Joi.string().required().valid('active', 'inactive', 'archived'),
    tags: Joi.array().optional().items(Joi.string().max(30))
  }),

  updateMetric: Joi.object({
    name: Joi.string().optional().min(3).max(100),
    description: Joi.string().optional().max(500),
    category: Joi.string().optional().valid('environmental', 'social', 'governance'),
    subcategory: Joi.string().optional().max(50),
    unit: Joi.string().optional().max(20),
    dataType: Joi.string().optional().valid('numeric', 'percentage', 'boolean', 'text', 'date'),
    isStandardized: Joi.boolean().optional(),
    standardId: Joi.string().optional().max(50),
    frameworkMappings: Joi.array().optional().items(
      Joi.object({
        frameworkId: Joi.string().required(),
        elementId: Joi.string().required(),
        version: Joi.string().optional()
      })
    ),
    calculationMethod: Joi.string().optional().max(200),
    calculationFormula: Joi.string().optional().max(200),
    dataCollectionFrequency: Joi.string().optional().valid('daily', 'weekly', 'monthly', 'quarterly', 'annually', 'custom'),
    verificationRequired: Joi.boolean().optional(),
    verificationProcess: Joi.string().optional().max(200),
    benchmarkValue: Joi.string().optional().max(50),
    benchmarkSource: Joi.string().optional().max(100),
    framework: Joi.string().optional().max(50), // Legacy field, kept for backward compatibility
    targetValue: Joi.string().optional().max(50),
    targetDate: Joi.string().optional().pattern(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
    owner: Joi.string().optional().max(100),
    status: Joi.string().optional().valid('active', 'inactive', 'archived'),
    tags: Joi.array().optional().items(Joi.string().max(30))
  }).min(1), // At least one field must be provided

  createDataPoint: Joi.object({
    value: Joi.string().required().max(50),
    date: Joi.string().required().pattern(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
    period: Joi.string().required().valid('daily', 'weekly', 'monthly', 'quarterly', 'annually'),
    source: Joi.string().optional().max(100),
    notes: Joi.string().optional().max(500),
    verificationStatus: Joi.string().required().valid('unverified', 'verified', 'rejected'),
    verifiedBy: Joi.string().optional().max(100),
    verifiedAt: Joi.string().optional().allow(null).pattern(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}Z$/) // ISO 8601 format
  }),

  createInitiative: Joi.object({
    name: Joi.string().required().min(3).max(100),
    description: Joi.string().optional().max(500),
    category: Joi.string().required().valid('environmental', 'social', 'governance'),
    startDate: Joi.string().optional().pattern(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
    endDate: Joi.string().optional().pattern(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
    status: Joi.string().required().valid('planned', 'in-progress', 'completed', 'cancelled'),
    owner: Joi.string().optional().max(100),
    budget: Joi.number().optional().min(0),
    metrics: Joi.array().items(Joi.string()).optional(),
    goals: Joi.array().items(
      Joi.object({
        description: Joi.string().required(),
        targetDate: Joi.string().optional().pattern(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
        status: Joi.string().required().valid('not-started', 'in-progress', 'completed', 'cancelled')
      })
    ).optional()
  }),

  updateInitiative: Joi.object({
    name: Joi.string().optional().min(3).max(100),
    description: Joi.string().optional().max(500),
    category: Joi.string().optional().valid('environmental', 'social', 'governance'),
    startDate: Joi.string().optional().pattern(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
    endDate: Joi.string().optional().pattern(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
    status: Joi.string().optional().valid('planned', 'in-progress', 'completed', 'cancelled'),
    owner: Joi.string().optional().max(100),
    budget: Joi.number().optional().min(0),
    metrics: Joi.array().items(Joi.string()).optional(),
    goals: Joi.array().items(
      Joi.object({
        description: Joi.string().required(),
        targetDate: Joi.string().optional().pattern(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
        status: Joi.string().required().valid('not-started', 'in-progress', 'completed', 'cancelled')
      })
    ).optional()
  }).min(1) // At least one field must be provided
};

/**
 * Middleware to validate request data against a schema
 * @param {string} schemaName - Name of the schema to validate against
 * @returns {Function} Express middleware function
 */
const validateRequest = (schemaName) => {
  return (req, res, next) => {
    const schema = schemas[schemaName];

    if (!schema) {
      return res.status(500).json({
        error: 'Internal Server Error',
        message: `Validation schema '${schemaName}' not found`
      });
    }

    const { error, value } = schema.validate(req.body, {
      abortEarly: false, // Return all errors, not just the first one
      stripUnknown: true // Remove unknown fields
    });

    if (error) {
      const errorMessage = error.details.map(detail => detail.message).join(', ');
      return res.status(400).json({
        error: 'Bad Request',
        message: errorMessage
      });
    }

    // Replace request body with validated value
    req.body = value;
    next();
  };
};

module.exports = {
  validateRequest
};
