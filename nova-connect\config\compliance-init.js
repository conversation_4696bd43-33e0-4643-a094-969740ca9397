/**
 * NovaConnect UAC Compliance Initialization
 * 
 * This module initializes compliance-related functionality.
 */

const complianceUtils = require('../utils/compliance');
const logger = require('./logger');

/**
 * Initialize compliance functionality
 */
function initializeCompliance() {
  try {
    // Schedule compliance tasks
    complianceUtils.scheduleComplianceTasks();
    
    // Run initial evidence collection if enabled
    if (process.env.COMPLIANCE_INIT_COLLECTION === 'true') {
      logger.info('Running initial compliance evidence collection');
      complianceUtils.collectSOC2Evidence()
        .then(() => logger.info('Initial compliance evidence collection completed'))
        .catch(error => logger.error('Initial compliance evidence collection failed:', error));
    }
    
    logger.info('Compliance functionality initialized');
  } catch (error) {
    logger.error('Error initializing compliance functionality:', error);
  }
}

module.exports = {
  initializeCompliance
};
