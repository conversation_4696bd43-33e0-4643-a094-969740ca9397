"""
Report Manager for the Universal Compliance Evidence Collection System.

This module provides functionality for generating reports based on collected evidence.
"""

import os
import json
import logging
import datetime
from typing import Dict, List, Any, Optional, Set, Union

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class ReportManager:
    """
    Manager for generating reports based on collected evidence.
    
    This class is responsible for generating various types of reports based on
    the evidence collected, validated, and stored in the system.
    """
    
    def __init__(self, reports_dir: Optional[str] = None):
        """
        Initialize the Report Manager.
        
        Args:
            reports_dir: Path to a directory for storing generated reports
        """
        logger.info("Initializing Report Manager")
        
        # Set the reports directory
        self.reports_dir = reports_dir or os.path.join(os.getcwd(), 'reports')
        
        # Create the reports directory if it doesn't exist
        os.makedirs(self.reports_dir, exist_ok=True)
        
        logger.info("Report Manager initialized")
    
    def generate_evidence_report(self, 
                               evidence_metadata: Dict[str, Dict[str, Any]],
                               evidence_tags: Dict[str, Set[str]],
                               evidence_by_category: Dict[str, Set[str]],
                               format: str = 'json',
                               filters: Optional[Dict[str, Any]] = None,
                               output_file: Optional[str] = None) -> str:
        """
        Generate a report of evidence items.
        
        Args:
            evidence_metadata: Dictionary of evidence metadata
            evidence_tags: Dictionary of evidence tags
            evidence_by_category: Dictionary of evidence by category
            format: Output format ('json' or 'html')
            filters: Filters to apply to the evidence
            output_file: Path to the output file
            
        Returns:
            Path to the generated report
            
        Raises:
            ValueError: If the format is not supported
        """
        logger.info(f"Generating evidence report in {format} format")
        
        # Apply filters to the evidence
        filtered_evidence = self._filter_evidence(
            evidence_metadata, 
            evidence_tags, 
            evidence_by_category, 
            filters
        )
        
        # Generate the report
        if format.lower() == 'json':
            report_content = self._generate_json_report(filtered_evidence)
        elif format.lower() == 'html':
            report_content = self._generate_html_report(filtered_evidence)
        else:
            raise ValueError(f"Unsupported format: {format}")
        
        # Generate a default output file name if not provided
        if not output_file:
            timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = os.path.join(self.reports_dir, f"evidence_report_{timestamp}.{format.lower()}")
        
        # Write the report to the output file
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        logger.info(f"Evidence report generated: {output_file}")
        
        return output_file
    
    def _filter_evidence(self, 
                       evidence_metadata: Dict[str, Dict[str, Any]],
                       evidence_tags: Dict[str, Set[str]],
                       evidence_by_category: Dict[str, Set[str]],
                       filters: Optional[Dict[str, Any]] = None) -> Dict[str, Dict[str, Any]]:
        """
        Filter evidence based on the provided filters.
        
        Args:
            evidence_metadata: Dictionary of evidence metadata
            evidence_tags: Dictionary of evidence tags
            evidence_by_category: Dictionary of evidence by category
            filters: Filters to apply to the evidence
            
        Returns:
            Filtered evidence metadata
        """
        if not filters:
            return evidence_metadata
        
        filtered_ids = set(evidence_metadata.keys())
        
        # Filter by type
        if 'type' in filters:
            type_ids = {
                evidence_id for evidence_id, metadata in evidence_metadata.items()
                if metadata.get('type') == filters['type']
            }
            filtered_ids &= type_ids
        
        # Filter by status
        if 'status' in filters:
            status_ids = {
                evidence_id for evidence_id, metadata in evidence_metadata.items()
                if metadata.get('status') == filters['status']
            }
            filtered_ids &= status_ids
        
        # Filter by tags
        if 'tags' in filters:
            tags = filters['tags']
            if not isinstance(tags, list):
                tags = [tags]
            
            tag_ids = set()
            for tag in tags:
                for evidence_id, evidence_tags_set in evidence_tags.items():
                    if tag in evidence_tags_set:
                        tag_ids.add(evidence_id)
            
            filtered_ids &= tag_ids
        
        # Filter by category
        if 'category' in filters:
            category = filters['category']
            category_ids = evidence_by_category.get(category, set())
            filtered_ids &= category_ids
        
        # Filter by date range
        if 'date_range' in filters:
            date_range = filters['date_range']
            start_date = date_range.get('start')
            end_date = date_range.get('end')
            
            date_ids = set()
            for evidence_id, metadata in evidence_metadata.items():
                created_at = metadata.get('created_at')
                if not created_at:
                    continue
                
                try:
                    created_date = datetime.datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                    
                    if start_date and end_date:
                        start = datetime.datetime.fromisoformat(start_date.replace('Z', '+00:00'))
                        end = datetime.datetime.fromisoformat(end_date.replace('Z', '+00:00'))
                        if start <= created_date <= end:
                            date_ids.add(evidence_id)
                    elif start_date:
                        start = datetime.datetime.fromisoformat(start_date.replace('Z', '+00:00'))
                        if start <= created_date:
                            date_ids.add(evidence_id)
                    elif end_date:
                        end = datetime.datetime.fromisoformat(end_date.replace('Z', '+00:00'))
                        if created_date <= end:
                            date_ids.add(evidence_id)
                except (ValueError, TypeError):
                    continue
            
            if date_ids:
                filtered_ids &= date_ids
        
        # Filter by validation status
        if 'is_valid' in filters:
            is_valid = filters['is_valid']
            valid_ids = {
                evidence_id for evidence_id, metadata in evidence_metadata.items()
                if metadata.get('validation_results', {}).get('is_valid') == is_valid
            }
            filtered_ids &= valid_ids
        
        # Return the filtered evidence
        return {
            evidence_id: evidence_metadata[evidence_id]
            for evidence_id in filtered_ids
        }
    
    def _generate_json_report(self, evidence_metadata: Dict[str, Dict[str, Any]]) -> str:
        """
        Generate a JSON report of evidence items.
        
        Args:
            evidence_metadata: Dictionary of evidence metadata
            
        Returns:
            JSON report as a string
        """
        report = {
            'generated_at': datetime.datetime.now(datetime.timezone.utc).isoformat(),
            'evidence_count': len(evidence_metadata),
            'evidence': evidence_metadata
        }
        
        return json.dumps(report, indent=2)
    
    def _generate_html_report(self, evidence_metadata: Dict[str, Dict[str, Any]]) -> str:
        """
        Generate an HTML report of evidence items.
        
        Args:
            evidence_metadata: Dictionary of evidence metadata
            
        Returns:
            HTML report as a string
        """
        # Generate the HTML report
        html = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Evidence Report</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    margin: 20px;
                }
                h1 {
                    color: #333;
                }
                table {
                    border-collapse: collapse;
                    width: 100%;
                }
                th, td {
                    border: 1px solid #ddd;
                    padding: 8px;
                    text-align: left;
                }
                th {
                    background-color: #f2f2f2;
                }
                tr:nth-child(even) {
                    background-color: #f9f9f9;
                }
                .valid {
                    color: green;
                }
                .invalid {
                    color: red;
                }
                .metadata {
                    margin-bottom: 20px;
                }
            </style>
        </head>
        <body>
            <h1>Evidence Report</h1>
            <div class="metadata">
                <p><strong>Generated at:</strong> {generated_at}</p>
                <p><strong>Evidence count:</strong> {evidence_count}</p>
            </div>
            <table>
                <tr>
                    <th>ID</th>
                    <th>Type</th>
                    <th>Status</th>
                    <th>Created At</th>
                    <th>Updated At</th>
                    <th>Validation</th>
                </tr>
        """.format(
            generated_at=datetime.datetime.now(datetime.timezone.utc).isoformat(),
            evidence_count=len(evidence_metadata)
        )
        
        # Add rows for each evidence item
        for evidence_id, metadata in evidence_metadata.items():
            is_valid = metadata.get('validation_results', {}).get('is_valid', False)
            validation_class = 'valid' if is_valid else 'invalid'
            validation_text = 'Valid' if is_valid else 'Invalid'
            
            html += """
                <tr>
                    <td>{id}</td>
                    <td>{type}</td>
                    <td>{status}</td>
                    <td>{created_at}</td>
                    <td>{updated_at}</td>
                    <td class="{validation_class}">{validation_text}</td>
                </tr>
            """.format(
                id=evidence_id,
                type=metadata.get('type', ''),
                status=metadata.get('status', ''),
                created_at=metadata.get('created_at', ''),
                updated_at=metadata.get('updated_at', ''),
                validation_class=validation_class,
                validation_text=validation_text
            )
        
        # Close the HTML
        html += """
            </table>
        </body>
        </html>
        """
        
        return html
    
    def generate_compliance_summary_report(self,
                                         evidence_metadata: Dict[str, Dict[str, Any]],
                                         requirements: Dict[str, Dict[str, Any]],
                                         evidence_by_requirement: Dict[str, Set[str]],
                                         format: str = 'json',
                                         output_file: Optional[str] = None) -> str:
        """
        Generate a compliance summary report.
        
        Args:
            evidence_metadata: Dictionary of evidence metadata
            requirements: Dictionary of compliance requirements
            evidence_by_requirement: Dictionary mapping requirement IDs to evidence IDs
            format: Output format ('json' or 'html')
            output_file: Path to the output file
            
        Returns:
            Path to the generated report
            
        Raises:
            ValueError: If the format is not supported
        """
        logger.info(f"Generating compliance summary report in {format} format")
        
        # Calculate compliance status for each requirement
        compliance_status = {}
        for requirement_id, requirement in requirements.items():
            # Get the evidence for this requirement
            evidence_ids = evidence_by_requirement.get(requirement_id, set())
            
            # Check if there is any valid evidence for this requirement
            valid_evidence = []
            invalid_evidence = []
            for evidence_id in evidence_ids:
                if evidence_id in evidence_metadata:
                    metadata = evidence_metadata[evidence_id]
                    is_valid = metadata.get('validation_results', {}).get('is_valid', False)
                    if is_valid:
                        valid_evidence.append(evidence_id)
                    else:
                        invalid_evidence.append(evidence_id)
            
            # Determine compliance status
            if valid_evidence:
                status = 'compliant'
            elif invalid_evidence:
                status = 'non_compliant'
            else:
                status = 'no_evidence'
            
            # Add to compliance status
            compliance_status[requirement_id] = {
                'requirement': requirement,
                'status': status,
                'valid_evidence': valid_evidence,
                'invalid_evidence': invalid_evidence,
                'evidence_count': len(valid_evidence) + len(invalid_evidence)
            }
        
        # Generate the report
        if format.lower() == 'json':
            report_content = self._generate_json_compliance_report(compliance_status)
        elif format.lower() == 'html':
            report_content = self._generate_html_compliance_report(compliance_status)
        else:
            raise ValueError(f"Unsupported format: {format}")
        
        # Generate a default output file name if not provided
        if not output_file:
            timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = os.path.join(self.reports_dir, f"compliance_report_{timestamp}.{format.lower()}")
        
        # Write the report to the output file
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        logger.info(f"Compliance summary report generated: {output_file}")
        
        return output_file
    
    def _generate_json_compliance_report(self, compliance_status: Dict[str, Dict[str, Any]]) -> str:
        """
        Generate a JSON compliance summary report.
        
        Args:
            compliance_status: Dictionary of compliance status by requirement
            
        Returns:
            JSON report as a string
        """
        # Calculate overall compliance
        total_requirements = len(compliance_status)
        compliant_requirements = sum(1 for status in compliance_status.values() if status['status'] == 'compliant')
        compliance_percentage = (compliant_requirements / total_requirements * 100) if total_requirements > 0 else 0
        
        report = {
            'generated_at': datetime.datetime.now(datetime.timezone.utc).isoformat(),
            'total_requirements': total_requirements,
            'compliant_requirements': compliant_requirements,
            'compliance_percentage': compliance_percentage,
            'requirements': compliance_status
        }
        
        return json.dumps(report, indent=2)
    
    def _generate_html_compliance_report(self, compliance_status: Dict[str, Dict[str, Any]]) -> str:
        """
        Generate an HTML compliance summary report.
        
        Args:
            compliance_status: Dictionary of compliance status by requirement
            
        Returns:
            HTML report as a string
        """
        # Calculate overall compliance
        total_requirements = len(compliance_status)
        compliant_requirements = sum(1 for status in compliance_status.values() if status['status'] == 'compliant')
        compliance_percentage = (compliant_requirements / total_requirements * 100) if total_requirements > 0 else 0
        
        # Generate the HTML report
        html = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Compliance Summary Report</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    margin: 20px;
                }
                h1, h2 {
                    color: #333;
                }
                .summary {
                    margin-bottom: 20px;
                    padding: 10px;
                    background-color: #f5f5f5;
                    border-radius: 5px;
                }
                .progress-bar {
                    width: 100%;
                    background-color: #e0e0e0;
                    border-radius: 5px;
                    margin-bottom: 10px;
                }
                .progress {
                    height: 20px;
                    background-color: #4CAF50;
                    border-radius: 5px;
                    text-align: center;
                    color: white;
                    line-height: 20px;
                }
                table {
                    border-collapse: collapse;
                    width: 100%;
                }
                th, td {
                    border: 1px solid #ddd;
                    padding: 8px;
                    text-align: left;
                }
                th {
                    background-color: #f2f2f2;
                }
                tr:nth-child(even) {
                    background-color: #f9f9f9;
                }
                .compliant {
                    color: green;
                }
                .non_compliant {
                    color: red;
                }
                .no_evidence {
                    color: orange;
                }
            </style>
        </head>
        <body>
            <h1>Compliance Summary Report</h1>
            <div class="summary">
                <p><strong>Generated at:</strong> {generated_at}</p>
                <p><strong>Total Requirements:</strong> {total_requirements}</p>
                <p><strong>Compliant Requirements:</strong> {compliant_requirements}</p>
                <div class="progress-bar">
                    <div class="progress" style="width: {compliance_percentage}%">
                        {compliance_percentage:.1f}%
                    </div>
                </div>
            </div>
            <h2>Requirements</h2>
            <table>
                <tr>
                    <th>ID</th>
                    <th>Name</th>
                    <th>Status</th>
                    <th>Evidence Count</th>
                </tr>
        """.format(
            generated_at=datetime.datetime.now(datetime.timezone.utc).isoformat(),
            total_requirements=total_requirements,
            compliant_requirements=compliant_requirements,
            compliance_percentage=compliance_percentage
        )
        
        # Add rows for each requirement
        for requirement_id, status in compliance_status.items():
            requirement = status['requirement']
            status_class = status['status']
            status_text = status['status'].replace('_', ' ').title()
            
            html += """
                <tr>
                    <td>{id}</td>
                    <td>{name}</td>
                    <td class="{status_class}">{status_text}</td>
                    <td>{evidence_count}</td>
                </tr>
            """.format(
                id=requirement_id,
                name=requirement.get('name', ''),
                status_class=status_class,
                status_text=status_text,
                evidence_count=status['evidence_count']
            )
        
        # Close the HTML
        html += """
            </table>
        </body>
        </html>
        """
        
        return html
