/**
 * Authentication Module
 */
class Auth {
  constructor(apiClient) {
    this.api = apiClient;
    this.loginForm = document.getElementById('login-form');
    this.loginContainer = document.getElementById('login-container');
    this.logoutBtn = document.getElementById('logout-btn');
    
    this.init();
  }
  
  /**
   * Initialize the authentication module
   */
  init() {
    // Check if the user is authenticated
    if (!this.api.isAuthenticated()) {
      this.showLoginForm();
    }
    
    // Add event listeners
    this.loginForm.addEventListener('submit', this.handleLogin.bind(this));
    this.logoutBtn.addEventListener('click', this.handleLogout.bind(this));
  }
  
  /**
   * Show the login form
   */
  showLoginForm() {
    // Hide the main content
    document.getElementById('sidebar').classList.add('d-none');
    document.getElementById('dashboard-container').classList.add('d-none');
    
    // Show the login form
    this.loginContainer.classList.remove('d-none');
  }
  
  /**
   * Hide the login form
   */
  hideLoginForm() {
    // Show the main content
    document.getElementById('sidebar').classList.remove('d-none');
    document.getElementById('dashboard-container').classList.remove('d-none');
    
    // Hide the login form
    this.loginContainer.classList.add('d-none');
  }
  
  /**
   * Handle login form submission
   * @param {Event} event - Form submission event
   */
  async handleLogin(event) {
    event.preventDefault();
    
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;
    
    try {
      await this.api.login(username, password);
      
      // If login is successful, hide the login form
      this.hideLoginForm();
      
      // Load the dashboard
      if (typeof dashboard !== 'undefined') {
        dashboard.loadDashboard();
      }
    } catch (error) {
      alert('Login failed: ' + error.message);
    }
  }
  
  /**
   * Handle logout button click
   * @param {Event} event - Click event
   */
  handleLogout(event) {
    event.preventDefault();
    
    this.api.logout();
    this.showLoginForm();
  }
}

// Initialize the authentication module when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.auth = new Auth(api);
});
