"""
Regulation utilities for the Universal Regulatory Change Management System.

This module provides utility functions for working with regulations.
"""

import json
import logging
from typing import Dict, List, Any, Optional

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_regulation_definition(file_path: str) -> Dict[str, Any]:
    """
    Load a regulation definition from a JSON file.
    
    Args:
        file_path: Path to the JSON file
        
    Returns:
        The regulation definition
        
    Raises:
        FileNotFoundError: If the file does not exist
        json.JSONDecodeError: If the file is not valid JSON
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            regulation_def = json.load(f)
        
        logger.info(f"Loaded regulation definition from {file_path}")
        return regulation_def
    except FileNotFoundError:
        logger.error(f"File not found: {file_path}")
        raise
    except json.JSONDecodeError:
        logger.error(f"Invalid JSON in file: {file_path}")
        raise

def save_regulation_definition(regulation_def: Dict[str, Any], file_path: str) -> None:
    """
    Save a regulation definition to a JSON file.
    
    Args:
        regulation_def: The regulation definition
        file_path: Path to the output JSON file
        
    Raises:
        IOError: If the file cannot be written
    """
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(regulation_def, f, indent=2)
        
        logger.info(f"Saved regulation definition to {file_path}")
    except IOError:
        logger.error(f"Failed to save regulation definition to {file_path}")
        raise

def validate_regulation_definition(regulation_def: Dict[str, Any]) -> List[str]:
    """
    Validate a regulation definition.
    
    Args:
        regulation_def: The regulation definition to validate
        
    Returns:
        List of validation errors, empty if valid
    """
    errors = []
    
    # Check required fields
    required_fields = ['id', 'name', 'description', 'version', 'effective_date', 'authority', 'url', 'requirements']
    for field in required_fields:
        if field not in regulation_def:
            errors.append(f"Missing required field: {field}")
    
    # If any required fields are missing, return early
    if errors:
        return errors
    
    # Check that requirements is a dictionary
    if not isinstance(regulation_def['requirements'], dict):
        errors.append(f"Requirements must be a dictionary in regulation definition: {regulation_def['id']}")
    
    return errors

def compare_regulation_versions(old_version: Dict[str, Any], new_version: Dict[str, Any]) -> Dict[str, Any]:
    """
    Compare two versions of a regulation and identify changes.
    
    Args:
        old_version: The old version of the regulation
        new_version: The new version of the regulation
        
    Returns:
        Dictionary of changes
    """
    changes = {
        'regulation_id': new_version.get('id'),
        'regulation_type': new_version.get('id'),
        'title': f"Changes in {new_version.get('name')}",
        'description': f"Changes between version {old_version.get('version')} and {new_version.get('version')}",
        'change_date': new_version.get('effective_date'),
        'effective_date': new_version.get('effective_date'),
        'url': new_version.get('url'),
        'affected_requirements': [],
        'change_details': {}
    }
    
    # Compare requirements
    old_requirements = old_version.get('requirements', {})
    new_requirements = new_version.get('requirements', {})
    
    # Find added requirements
    for req_id, req_data in new_requirements.items():
        if req_id not in old_requirements:
            changes['affected_requirements'].append(req_id)
            changes['change_details'][req_id] = {
                'change_type': 'added',
                'old_value': None,
                'new_value': req_data.get('description')
            }
    
    # Find removed requirements
    for req_id, req_data in old_requirements.items():
        if req_id not in new_requirements:
            changes['affected_requirements'].append(req_id)
            changes['change_details'][req_id] = {
                'change_type': 'removed',
                'old_value': req_data.get('description'),
                'new_value': None
            }
    
    # Find modified requirements
    for req_id, req_data in new_requirements.items():
        if req_id in old_requirements:
            old_req_data = old_requirements[req_id]
            
            # Compare descriptions
            if req_data.get('description') != old_req_data.get('description'):
                changes['affected_requirements'].append(req_id)
                changes['change_details'][req_id] = {
                    'change_type': 'modified',
                    'old_value': old_req_data.get('description'),
                    'new_value': req_data.get('description')
                }
    
    return changes

def get_requirement_details(regulation_def: Dict[str, Any], requirement_id: str) -> Optional[Dict[str, Any]]:
    """
    Get details of a specific requirement in a regulation.
    
    Args:
        regulation_def: The regulation definition
        requirement_id: The ID of the requirement
        
    Returns:
        The requirement details, or None if not found
    """
    requirements = regulation_def.get('requirements', {})
    return requirements.get(requirement_id)

def get_affected_controls(requirement_id: str) -> List[str]:
    """
    Get the controls affected by a specific requirement.
    
    Args:
        requirement_id: The ID of the requirement
        
    Returns:
        List of affected controls
    """
    # Map requirements to controls (simplified mapping)
    requirement_to_controls = {
        'data_protection': ['data_protection_officer', 'data_protection_impact_assessment'],
        'data_subject_rights': ['data_subject_rights_process', 'consent_management'],
        'controller_processor': ['data_protection_officer', 'vendor_management'],
        'data_transfers': ['cross_border_data_transfer_mechanism'],
        'remedies_liability': ['data_breach_notification_process'],
        
        'privacy_rule': ['security_risk_assessment', 'access_controls'],
        'security_rule': ['access_controls', 'audit_controls', 'integrity_controls', 'transmission_security'],
        'breach_notification': ['data_breach_notification_process'],
        'enforcement_rule': ['business_associate_agreements'],
        
        'security': ['security_policies', 'risk_management', 'system_operations', 'logical_access', 'physical_access', 'system_monitoring'],
        'availability': ['system_operations', 'incident_management'],
        'processing_integrity': ['change_management', 'system_monitoring'],
        'confidentiality': ['logical_access', 'physical_access'],
        'privacy': ['data_subject_rights_process', 'consent_management']
    }
    
    return requirement_to_controls.get(requirement_id, [])
