/**
 * GraphVisualization Component
 * 
 * A reusable graph visualization component for displaying network graphs, risk maps, etc.
 */

import React, { useEffect, useRef, useState } from 'react';
import PropTypes from 'prop-types';
import * as d3 from 'd3';

/**
 * GraphVisualization component
 * 
 * @param {Object} props - Component props
 * @param {Object} props.data - Graph data
 * @param {Array} props.data.nodes - Graph nodes
 * @param {Array} props.data.edges - Graph edges
 * @param {Object} [props.options] - Graph options
 * @param {string} [props.options.layout='force'] - Graph layout (force, radial, hierarchical)
 * @param {string} [props.options.nodeSize='fixed'] - Node size strategy (fixed, value, degree)
 * @param {string} [props.options.nodeColor='category'] - Node color strategy (category, value, fixed)
 * @param {string} [props.options.edgeWidth='fixed'] - Edge width strategy (fixed, value)
 * @param {boolean} [props.options.interactive=true] - Whether the graph is interactive
 * @param {boolean} [props.options.zoomable=true] - Whether the graph is zoomable
 * @param {boolean} [props.options.draggable=true] - Whether nodes are draggable
 * @param {boolean} [props.options.highlightNeighbors=true] - Whether to highlight neighbors on hover
 * @param {boolean} [props.options.showLegend=true] - Whether to show the legend
 * @param {Function} [props.onNodeClick] - Function to call when a node is clicked
 * @param {Function} [props.onEdgeClick] - Function to call when an edge is clicked
 * @param {string} [props.className] - Additional CSS class names
 * @param {Object} [props.style] - Additional inline styles
 * @returns {React.ReactElement} GraphVisualization component
 */
const GraphVisualization = ({
  data,
  options = {},
  onNodeClick,
  onEdgeClick,
  className = '',
  style = {}
}) => {
  const svgRef = useRef(null);
  const containerRef = useRef(null);
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });
  const [selectedNode, setSelectedNode] = useState(null);
  
  // Default options
  const defaultOptions = {
    layout: 'force',
    nodeSize: 'fixed',
    nodeColor: 'category',
    edgeWidth: 'fixed',
    interactive: true,
    zoomable: true,
    draggable: true,
    highlightNeighbors: true,
    showLegend: true
  };
  
  // Merge default options with provided options
  const graphOptions = { ...defaultOptions, ...options };
  
  // Update dimensions when container size changes
  useEffect(() => {
    if (!containerRef.current) return;
    
    const resizeObserver = new ResizeObserver(entries => {
      for (const entry of entries) {
        const { width, height } = entry.contentRect;
        setDimensions({ width, height });
      }
    });
    
    resizeObserver.observe(containerRef.current);
    
    return () => {
      resizeObserver.disconnect();
    };
  }, []);
  
  // Create and update graph visualization
  useEffect(() => {
    if (!svgRef.current || !data || !data.nodes || !data.edges || dimensions.width === 0 || dimensions.height === 0) return;
    
    // Clear previous visualization
    d3.select(svgRef.current).selectAll('*').remove();
    
    // Create SVG element
    const svg = d3.select(svgRef.current)
      .attr('width', dimensions.width)
      .attr('height', dimensions.height)
      .attr('viewBox', [0, 0, dimensions.width, dimensions.height]);
    
    // Create zoom behavior
    if (graphOptions.zoomable) {
      const zoom = d3.zoom()
        .scaleExtent([0.1, 10])
        .on('zoom', (event) => {
          g.attr('transform', event.transform);
        });
      
      svg.call(zoom);
    }
    
    // Create container for graph elements
    const g = svg.append('g');
    
    // Create color scale for nodes
    const nodeCategories = [...new Set(data.nodes.map(node => node.category || 'default'))];
    const colorScale = d3.scaleOrdinal()
      .domain(nodeCategories)
      .range(d3.schemeCategory10);
    
    // Create links (edges)
    const links = g.append('g')
      .attr('class', 'links')
      .selectAll('line')
      .data(data.edges)
      .enter()
      .append('line')
      .attr('stroke', '#999')
      .attr('stroke-opacity', 0.6)
      .attr('stroke-width', d => {
        if (graphOptions.edgeWidth === 'value') {
          return (d.weight || 1) * 2;
        }
        return 1;
      })
      .on('click', (event, d) => {
        if (onEdgeClick) {
          onEdgeClick(d);
        }
      });
    
    // Create nodes
    const nodes = g.append('g')
      .attr('class', 'nodes')
      .selectAll('circle')
      .data(data.nodes)
      .enter()
      .append('circle')
      .attr('r', d => {
        if (graphOptions.nodeSize === 'value') {
          return (d.value || 5) * 2;
        } else if (graphOptions.nodeSize === 'degree') {
          // Count number of connected edges
          const degree = data.edges.filter(edge => 
            edge.source === d.id || edge.target === d.id
          ).length;
          return Math.max(5, Math.min(20, 5 + degree));
        }
        return 8;
      })
      .attr('fill', d => {
        if (graphOptions.nodeColor === 'category') {
          return colorScale(d.category || 'default');
        } else if (graphOptions.nodeColor === 'value') {
          // Create a color scale based on node value
          const valueScale = d3.scaleLinear()
            .domain([0, d3.max(data.nodes, n => n.value || 0)])
            .range(['#f7fbff', '#08519c']);
          return valueScale(d.value || 0);
        }
        return '#1f77b4';
      })
      .attr('stroke', '#fff')
      .attr('stroke-width', 1.5)
      .on('click', (event, d) => {
        setSelectedNode(selectedNode === d.id ? null : d.id);
        if (onNodeClick) {
          onNodeClick(d);
        }
      });
    
    // Add node labels
    const labels = g.append('g')
      .attr('class', 'labels')
      .selectAll('text')
      .data(data.nodes)
      .enter()
      .append('text')
      .attr('dx', 12)
      .attr('dy', '.35em')
      .text(d => d.label || d.id)
      .style('font-size', '10px')
      .style('fill', '#333');
    
    // Add hover effects
    if (graphOptions.interactive) {
      nodes
        .on('mouseover', (event, d) => {
          // Highlight the node
          d3.select(event.currentTarget)
            .attr('stroke', '#000')
            .attr('stroke-width', 2);
          
          // Highlight connected nodes and links if highlightNeighbors is enabled
          if (graphOptions.highlightNeighbors) {
            const connectedNodeIds = data.edges
              .filter(edge => edge.source === d.id || edge.target === d.id)
              .flatMap(edge => [edge.source, edge.target]);
            
            nodes.filter(node => connectedNodeIds.includes(node.id))
              .attr('stroke', '#000')
              .attr('stroke-width', 2);
            
            links.filter(edge => edge.source === d.id || edge.target === d.id)
              .attr('stroke', '#000')
              .attr('stroke-width', d => {
                if (graphOptions.edgeWidth === 'value') {
                  return (d.weight || 1) * 3;
                }
                return 2;
              })
              .attr('stroke-opacity', 1);
          }
        })
        .on('mouseout', (event, d) => {
          // Reset node style
          nodes.attr('stroke', '#fff')
            .attr('stroke-width', 1.5);
          
          // Reset links style
          links.attr('stroke', '#999')
            .attr('stroke-opacity', 0.6)
            .attr('stroke-width', d => {
              if (graphOptions.edgeWidth === 'value') {
                return (d.weight || 1) * 2;
              }
              return 1;
            });
        });
    }
    
    // Create force simulation
    const simulation = d3.forceSimulation(data.nodes)
      .force('link', d3.forceLink(data.edges)
        .id(d => d.id)
        .distance(100))
      .force('charge', d3.forceManyBody().strength(-300))
      .force('center', d3.forceCenter(dimensions.width / 2, dimensions.height / 2))
      .force('collision', d3.forceCollide().radius(30));
    
    // Apply different layouts
    if (graphOptions.layout === 'radial') {
      simulation
        .force('link', d3.forceLink(data.edges).id(d => d.id).distance(100))
        .force('charge', d3.forceManyBody().strength(-50))
        .force('r', d3.forceRadial(dimensions.width / 3, dimensions.width / 2, dimensions.height / 2))
        .force('center', d3.forceCenter(dimensions.width / 2, dimensions.height / 2));
    } else if (graphOptions.layout === 'hierarchical') {
      // Simple hierarchical layout (top to bottom)
      const levels = {};
      
      // Assign levels to nodes (breadth-first traversal)
      const rootNode = data.nodes[0];
      levels[rootNode.id] = 0;
      
      const queue = [rootNode.id];
      while (queue.length > 0) {
        const currentId = queue.shift();
        const currentLevel = levels[currentId];
        
        const neighbors = data.edges
          .filter(edge => edge.source === currentId)
          .map(edge => edge.target);
        
        for (const neighbor of neighbors) {
          if (levels[neighbor] === undefined) {
            levels[neighbor] = currentLevel + 1;
            queue.push(neighbor);
          }
        }
      }
      
      // Position nodes based on levels
      data.nodes.forEach(node => {
        const level = levels[node.id] || 0;
        const nodesAtLevel = data.nodes.filter(n => (levels[n.id] || 0) === level).length;
        const index = data.nodes.filter(n => (levels[n.id] || 0) === level).findIndex(n => n.id === node.id);
        
        node.fx = (dimensions.width / (nodesAtLevel + 1)) * (index + 1);
        node.fy = (dimensions.height / (Object.keys(levels).length + 1)) * (level + 1);
      });
    }
    
    // Enable node dragging
    if (graphOptions.draggable) {
      nodes.call(d3.drag()
        .on('start', (event, d) => {
          if (!event.active) simulation.alphaTarget(0.3).restart();
          d.fx = d.x;
          d.fy = d.y;
        })
        .on('drag', (event, d) => {
          d.fx = event.x;
          d.fy = event.y;
        })
        .on('end', (event, d) => {
          if (!event.active) simulation.alphaTarget(0);
          if (graphOptions.layout !== 'hierarchical') {
            d.fx = null;
            d.fy = null;
          }
        }));
    }
    
    // Update node and link positions on simulation tick
    simulation.on('tick', () => {
      links
        .attr('x1', d => d.source.x)
        .attr('y1', d => d.source.y)
        .attr('x2', d => d.target.x)
        .attr('y2', d => d.target.y);
      
      nodes
        .attr('cx', d => d.x)
        .attr('cy', d => d.y);
      
      labels
        .attr('x', d => d.x)
        .attr('y', d => d.y);
    });
    
    // Add legend if enabled
    if (graphOptions.showLegend && graphOptions.nodeColor === 'category') {
      const legend = svg.append('g')
        .attr('class', 'legend')
        .attr('transform', `translate(20, 20)`);
      
      nodeCategories.forEach((category, i) => {
        const legendItem = legend.append('g')
          .attr('transform', `translate(0, ${i * 20})`);
        
        legendItem.append('circle')
          .attr('r', 6)
          .attr('fill', colorScale(category));
        
        legendItem.append('text')
          .attr('x', 12)
          .attr('y', 4)
          .text(category)
          .style('font-size', '12px');
      });
    }
    
    // Cleanup
    return () => {
      simulation.stop();
    };
  }, [data, dimensions, graphOptions, onNodeClick, onEdgeClick, selectedNode]);
  
  return (
    <div
      ref={containerRef}
      className={`relative w-full h-full min-h-[300px] ${className}`}
      style={{ ...style }}
      data-testid="graph-visualization"
    >
      <svg
        ref={svgRef}
        className="w-full h-full"
      />
    </div>
  );
};

GraphVisualization.propTypes = {
  data: PropTypes.shape({
    nodes: PropTypes.arrayOf(
      PropTypes.shape({
        id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
        label: PropTypes.string,
        category: PropTypes.string,
        value: PropTypes.number
      })
    ).isRequired,
    edges: PropTypes.arrayOf(
      PropTypes.shape({
        source: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
        target: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
        label: PropTypes.string,
        weight: PropTypes.number
      })
    ).isRequired
  }).isRequired,
  options: PropTypes.shape({
    layout: PropTypes.oneOf(['force', 'radial', 'hierarchical']),
    nodeSize: PropTypes.oneOf(['fixed', 'value', 'degree']),
    nodeColor: PropTypes.oneOf(['category', 'value', 'fixed']),
    edgeWidth: PropTypes.oneOf(['fixed', 'value']),
    interactive: PropTypes.bool,
    zoomable: PropTypes.bool,
    draggable: PropTypes.bool,
    highlightNeighbors: PropTypes.bool,
    showLegend: PropTypes.bool
  }),
  onNodeClick: PropTypes.func,
  onEdgeClick: PropTypes.func,
  className: PropTypes.string,
  style: PropTypes.object
};

export default GraphVisualization;
