/**
 * TextField Component
 * 
 * A text input component that follows the NovaConnect design system.
 */

import React, { forwardRef } from 'react';
import PropTypes from 'prop-types';
import './TextField.css';

/**
 * TextField component
 * 
 * @param {Object} props - Component props
 * @param {string} [props.id] - Input ID
 * @param {string} [props.name] - Input name
 * @param {string} [props.type='text'] - Input type
 * @param {string} [props.label] - Input label
 * @param {string} [props.placeholder] - Input placeholder
 * @param {string} [props.value] - Input value
 * @param {string} [props.defaultValue] - Input default value
 * @param {boolean} [props.disabled=false] - Whether the input is disabled
 * @param {boolean} [props.readOnly=false] - Whether the input is read-only
 * @param {boolean} [props.required=false] - Whether the input is required
 * @param {boolean} [props.fullWidth=false] - Whether the input takes full width
 * @param {string} [props.helperText] - Helper text
 * @param {string} [props.errorText] - Error text
 * @param {Function} [props.onChange] - Change handler
 * @param {Function} [props.onFocus] - Focus handler
 * @param {Function} [props.onBlur] - Blur handler
 * @param {string} [props.className] - Additional CSS class
 * @returns {React.ReactElement} TextField component
 */
const TextField = forwardRef(({
  id,
  name,
  type = 'text',
  label,
  placeholder,
  value,
  defaultValue,
  disabled = false,
  readOnly = false,
  required = false,
  fullWidth = false,
  helperText,
  errorText,
  onChange,
  onFocus,
  onBlur,
  className = '',
  ...rest
}, ref) => {
  const baseClass = 'nova-text-field';
  const fullWidthClass = fullWidth ? `${baseClass}--full-width` : '';
  const errorClass = errorText ? `${baseClass}--error` : '';
  const disabledClass = disabled ? `${baseClass}--disabled` : '';
  
  const textFieldClasses = [
    baseClass,
    fullWidthClass,
    errorClass,
    disabledClass,
    className
  ].filter(Boolean).join(' ');
  
  const inputId = id || `${baseClass}-${Math.random().toString(36).substring(2, 9)}`;
  
  return (
    <div className={textFieldClasses}>
      {label && (
        <label 
          className={`${baseClass}__label`} 
          htmlFor={inputId}
        >
          {label}
          {required && <span className={`${baseClass}__required`}>*</span>}
        </label>
      )}
      
      <input
        ref={ref}
        id={inputId}
        name={name}
        type={type}
        className={`${baseClass}__input`}
        placeholder={placeholder}
        value={value}
        defaultValue={defaultValue}
        disabled={disabled}
        readOnly={readOnly}
        required={required}
        onChange={onChange}
        onFocus={onFocus}
        onBlur={onBlur}
        aria-invalid={!!errorText}
        aria-describedby={
          (helperText || errorText) ? `${inputId}-helper` : undefined
        }
        {...rest}
      />
      
      {(helperText || errorText) && (
        <div 
          id={`${inputId}-helper`}
          className={`${baseClass}__helper-text ${errorText ? `${baseClass}__helper-text--error` : ''}`}
        >
          {errorText || helperText}
        </div>
      )}
    </div>
  );
});

TextField.displayName = 'TextField';

TextField.propTypes = {
  id: PropTypes.string,
  name: PropTypes.string,
  type: PropTypes.oneOf([
    'text', 'password', 'email', 'number', 'tel', 'url', 'search',
    'date', 'time', 'datetime-local', 'month', 'week'
  ]),
  label: PropTypes.string,
  placeholder: PropTypes.string,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  defaultValue: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  disabled: PropTypes.bool,
  readOnly: PropTypes.bool,
  required: PropTypes.bool,
  fullWidth: PropTypes.bool,
  helperText: PropTypes.string,
  errorText: PropTypes.string,
  onChange: PropTypes.func,
  onFocus: PropTypes.func,
  onBlur: PropTypes.func,
  className: PropTypes.string
};

export default TextField;
