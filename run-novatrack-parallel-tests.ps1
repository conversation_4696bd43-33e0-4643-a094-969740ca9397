# NovaFuse Universal Platform - NovaTrack Parallel Tests

# Function to display colored output
function Write-ColorOutput {
    param(
        [string]$Text,
        [string]$ForegroundColor = "White"
    )
    
    $originalColor = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    Write-Output $Text
    $host.UI.RawUI.ForegroundColor = $originalColor
}

# Create directories for test results
Write-ColorOutput "Creating directories for test results..." -ForegroundColor Yellow
New-Item -ItemType Directory -Force -Path "./test-results" | Out-Null
New-Item -ItemType Directory -Force -Path "./test-results/local" | Out-Null
New-Item -ItemType Directory -Force -Path "./test-results/docker" | Out-Null
New-Item -ItemType Directory -Force -Path "./coverage" | Out-Null
New-Item -ItemType Directory -Force -Path "./coverage/local" | Out-Null
New-Item -ItemType Directory -Force -Path "./coverage/docker" | Out-Null

# Display welcome message
Write-ColorOutput "NovaFuse Universal Platform - NovaTrack Parallel Tests" -ForegroundColor Cyan
Write-ColorOutput "=====================================================" -ForegroundColor Cyan
Write-ColorOutput "This script will run NovaTrack tests in both local and Docker environments." -ForegroundColor Cyan
Write-ColorOutput "Coverage threshold is set to 81% for branches, functions, lines, and statements." -ForegroundColor Cyan
Write-ColorOutput "" -ForegroundColor Cyan

# Run local tests
Write-ColorOutput "Running local NovaTrack tests..." -ForegroundColor Green
Write-ColorOutput "--------------------------------" -ForegroundColor Green
npm run test:novatrack

# Run local tests with coverage
Write-ColorOutput "`nRunning local NovaTrack tests with coverage..." -ForegroundColor Green
Write-ColorOutput "--------------------------------------------" -ForegroundColor Green
npm run test:coverage:novatrack

# Run Docker tests
Write-ColorOutput "`nRunning Docker NovaTrack tests..." -ForegroundColor Green
Write-ColorOutput "----------------------------------" -ForegroundColor Green
npm run test:novatrack:docker

# Run Docker tests with coverage
Write-ColorOutput "`nRunning Docker NovaTrack tests with coverage..." -ForegroundColor Green
Write-ColorOutput "--------------------------------------------" -ForegroundColor Green
npm run test:novatrack:coverage:docker

# Display summary
Write-ColorOutput "`nTesting completed!" -ForegroundColor Green
Write-ColorOutput "Local test results are available in ./test-results/local" -ForegroundColor Green
Write-ColorOutput "Docker test results are available in ./test-results/docker" -ForegroundColor Green
Write-ColorOutput "Local coverage report is available in ./coverage/local/lcov-report/index.html" -ForegroundColor Green
Write-ColorOutput "Docker coverage report is available in ./coverage/docker/lcov-report/index.html" -ForegroundColor Green

# Compare results
Write-ColorOutput "`nComparing test results..." -ForegroundColor Yellow
Write-ColorOutput "If there are differences between local and Docker test results, they will be displayed below:" -ForegroundColor Yellow
Write-ColorOutput "" -ForegroundColor Yellow

# Open the local coverage report
Write-ColorOutput "`nOpening local coverage report..." -ForegroundColor Green
Start-Process "./coverage/local/lcov-report/index.html"
