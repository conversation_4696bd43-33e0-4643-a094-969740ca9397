@echo off
echo Building Nova Agent with WebSocket Console...
echo.

REM Remove old executables
if exist nova-agent.exe del nova-agent.exe
if exist nova-agent-api.exe del nova-agent-api.exe

REM Initialize Go modules
echo Initializing Go modules...
"C:\Program Files\Go\bin\go.exe" mod tidy

REM Build with Go
echo Building nova-agent-api.exe...
"C:\Program Files\Go\bin\go.exe" build -o nova-agent-api.exe nova-agent.go

REM Check if build succeeded
if exist nova-agent-api.exe (
    echo ✅ Build successful!
    echo.
    echo 🚀 Nova Agent Platform Console Integration Ready!
    echo 📡 API Server: http://localhost:8080
    echo 🔌 WebSocket: ws://localhost:8080/ws
    echo 🎛️ Dashboard: http://localhost:3001 (run separately)
    echo.
    echo Press Ctrl+C to stop the server
    echo.
    nova-agent-api.exe
) else (
    echo ❌ Build failed!
    echo Checking for errors...
    "C:\Program Files\Go\bin\go.exe" build -v nova-agent.go
)

echo.
pause
