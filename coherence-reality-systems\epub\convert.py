import markdown
import os
from bs4 import BeautifulSoup
import json

def convert_markdown_to_html(md_file, output_file):
    """Convert Markdown file to HTML with proper EPUB formatting"""
    with open(md_file, 'r', encoding='utf-8') as f:
        md_content = f.read()
    
    # Convert markdown to HTML
    html_content = markdown.markdown(
        md_content,
        extensions=['tables', 'fenced_code', 'codehilite', 'toc'],
        extension_configs={'codehilite': {'use_pygments': True}}
    )
    
    # Parse HTML and add proper EPUB structure
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # Add proper EPUB classes and structure
    for table in soup.find_all('table'):
        table['class'] = 'epub-table'
    
    for code in soup.find_all('code'):
        code['class'] = 'epub-code'
    
    # Create proper EPUB HTML structure
    epub_html = f"""
    <?xml version="1.0" encoding="UTF-8"?>
    <!DOCTYPE html>
    <html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>{os.path.basename(md_file).replace('.md', '')}</title>
        <link href="../css/style.css" type="text/css" rel="stylesheet"/>
    </head>
    <body>
        {str(soup)}
    </body>
    </html>
    """
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(epub_html)

def create_epub_structure():
    """Create the EPUB directory structure"""
    os.makedirs('epub/text', exist_ok=True)
    os.makedirs('epub/css', exist_ok=True)
    os.makedirs('epub/images', exist_ok=True)

def main():
    create_epub_structure()
    
    # Convert main dictionary
    convert_markdown_to_html('Dictionary of Comphyology First Edition.md', 'epub/text/dictionary.xhtml')
    
    # Convert treatise
    convert_markdown_to_html('Treatise on Comphyology.md', 'epub/text/treatise.xhtml')
    
    print("Conversion complete!")

if __name__ == "__main__":
    main()
