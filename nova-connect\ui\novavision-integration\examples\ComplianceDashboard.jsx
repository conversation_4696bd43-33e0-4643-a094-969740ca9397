/**
 * Compliance Dashboard Example
 * 
 * This example demonstrates how to use the NovaVision integration to create a compliance dashboard.
 */

import React, { useState, useEffect } from 'react';
import NovaVision from '@novafuse/uui-core';
import { NovaVisionIntegration, NovaVisionBridge } from '../';

/**
 * Compliance Dashboard component
 * 
 * @returns {React.ReactElement} Compliance Dashboard component
 */
const ComplianceDashboard = () => {
  const [novaVision, setNovaVision] = useState(null);
  const [integration, setIntegration] = useState(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [error, setError] = useState(null);
  const [complianceData, setComplianceData] = useState({
    complianceScore: 85,
    frameworks: [
      { name: 'GDPR', score: 85 },
      { name: 'HIPAA', score: 92 },
      { name: 'PCI DSS', score: 78 },
      { name: 'SOC 2', score: 90 }
    ],
    recentEvents: [
      { date: '2023-06-01', event: 'GDPR Audit', status: 'Completed' },
      { date: '2023-05-15', event: 'PCI DSS Assessment', status: 'In Progress' },
      { date: '2023-05-01', event: 'HIPAA Compliance Check', status: 'Completed' }
    ]
  });
  
  // Initialize NovaVision
  useEffect(() => {
    try {
      // Create NovaVision instance
      const novaVisionInstance = new NovaVision({
        theme: 'default',
        responsive: true,
        accessibilityLevel: 'AA',
        regulationAware: true,
        aiOptimization: true,
        consistencyEnforcement: true
      });
      
      setNovaVision(novaVisionInstance);
    } catch (err) {
      console.error('Error initializing NovaVision', err);
      setError(err);
    }
  }, []);
  
  // Initialize NovaVision integration
  useEffect(() => {
    if (!novaVision) {
      return;
    }
    
    const initializeIntegration = async () => {
      try {
        // Create integration instance
        const novaVisionIntegration = new NovaVisionIntegration({
          novaVision,
          enableLogging: true
        });
        
        // Initialize integration
        await novaVisionIntegration.initialize();
        
        setIntegration(novaVisionIntegration);
        setIsInitialized(true);
      } catch (err) {
        console.error('Error initializing NovaVision integration', err);
        setError(err);
      }
    };
    
    initializeIntegration();
  }, [novaVision]);
  
  // Create UI schema
  const createDashboardSchema = () => {
    return {
      type: 'dashboard',
      title: 'Compliance Dashboard',
      layout: {
        type: 'grid',
        columns: 2,
        rows: 2,
        areas: [
          ['header', 'header'],
          ['sidebar', 'content'],
          ['footer', 'footer']
        ]
      },
      components: [
        {
          type: 'card',
          area: 'header',
          header: 'Compliance Status',
          content: {
            type: 'chart',
            chartType: 'pie',
            data: {
              labels: ['Compliant', 'Non-Compliant', 'In Progress'],
              datasets: [
                {
                  data: [
                    complianceData.complianceScore,
                    100 - complianceData.complianceScore,
                    0
                  ],
                  backgroundColor: ['#28a745', '#dc3545', '#ffc107']
                }
              ]
            }
          }
        },
        {
          type: 'card',
          area: 'sidebar',
          header: 'Compliance Frameworks',
          content: {
            type: 'list',
            items: complianceData.frameworks.map(framework => ({
              text: framework.name,
              badge: `${framework.score}%`
            }))
          }
        },
        {
          type: 'card',
          area: 'content',
          header: 'Recent Compliance Events',
          content: {
            type: 'table',
            columns: [
              { field: 'date', header: 'Date' },
              { field: 'event', header: 'Event' },
              { field: 'status', header: 'Status' }
            ],
            data: complianceData.recentEvents
          }
        },
        {
          type: 'card',
          area: 'footer',
          content: {
            type: 'button',
            text: 'Generate Compliance Report',
            variant: 'primary',
            onClick: 'generateReport'
          }
        }
      ]
    };
  };
  
  // Handle actions
  const handleAction = (action, data) => {
    if (action === 'generateReport') {
      console.log('Generating compliance report...');
      alert('Generating compliance report...');
      // Generate report logic
    }
  };
  
  if (error) {
    return (
      <div className="error">
        <h3>Error</h3>
        <p>{error.message}</p>
      </div>
    );
  }
  
  if (!isInitialized) {
    return (
      <div className="loading">
        <p>Initializing NovaVision integration...</p>
      </div>
    );
  }
  
  return (
    <div className="compliance-dashboard">
      <h1>Compliance Dashboard</h1>
      <p>This dashboard demonstrates how to use the NovaVision integration to create a compliance dashboard.</p>
      
      <div className="dashboard-container">
        <NovaVisionBridge
          novaVision={novaVision}
          schema={createDashboardSchema()}
          onAction={handleAction}
          enableLogging={true}
        />
      </div>
    </div>
  );
};

export default ComplianceDashboard;
