/**
 * NovaCore Document Controller
 * 
 * This controller handles API requests related to vendor documents.
 */

const { DocumentService } = require('../services');
const logger = require('../../../config/logger');

class DocumentController {
  /**
   * Create a new document
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async createDocument(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      
      // Check if file is provided
      if (!req.file && !req.body.content) {
        return res.status(400).json({
          success: false,
          message: 'File or content is required'
        });
      }
      
      let document;
      
      if (req.file) {
        // Import document from file
        document = await DocumentService.importDocumentFromFile(req.file, req.body, userId);
      } else {
        // Create document from content
        document = await DocumentService.createDocument(req.body, req.body.content, userId);
      }
      
      res.status(201).json({
        success: true,
        data: document
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Get all documents
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async getAllDocuments(req, res, next) {
    try {
      const { organizationId } = req.params;
      
      // Extract filter parameters from query
      const filter = {};
      
      if (req.query.vendorId) filter.vendorId = req.query.vendorId;
      if (req.query.assessmentId) filter.assessmentId = req.query.assessmentId;
      if (req.query.type) filter.type = req.query.type;
      if (req.query.status) filter.status = req.query.status;
      if (req.query.tags) filter.tags = req.query.tags.split(',');
      if (req.query.frameworks) filter.frameworks = req.query.frameworks.split(',');
      if (req.query.blockchainVerified) filter.blockchainVerified = req.query.blockchainVerified;
      if (req.query.search) filter.search = req.query.search;
      
      // Extract pagination and sorting options
      const options = {
        page: parseInt(req.query.page, 10) || 1,
        limit: parseInt(req.query.limit, 10) || 10
      };
      
      if (req.query.sort) {
        const [field, order] = req.query.sort.split(':');
        options.sort = { [field]: order === 'desc' ? -1 : 1 };
      }
      
      const result = await DocumentService.getAllDocuments(organizationId, filter, options);
      
      res.status(200).json({
        success: true,
        data: result.data,
        pagination: result.pagination
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Get document by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async getDocumentById(req, res, next) {
    try {
      const document = await DocumentService.getDocumentById(req.params.id);
      
      res.status(200).json({
        success: true,
        data: document
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Get document content
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async getDocumentContent(req, res, next) {
    try {
      const document = await DocumentService.getDocumentById(req.params.id);
      const content = await DocumentService.getDocumentContent(req.params.id);
      
      // Set content type based on document format
      let contentType = 'application/octet-stream';
      
      if (document.mimeType) {
        contentType = document.mimeType;
      } else {
        switch (document.format) {
          case 'pdf':
            contentType = 'application/pdf';
            break;
          case 'doc':
          case 'docx':
            contentType = 'application/msword';
            break;
          case 'xls':
          case 'xlsx':
            contentType = 'application/vnd.ms-excel';
            break;
          case 'ppt':
          case 'pptx':
            contentType = 'application/vnd.ms-powerpoint';
            break;
          case 'txt':
            contentType = 'text/plain';
            break;
          case 'csv':
            contentType = 'text/csv';
            break;
          case 'json':
            contentType = 'application/json';
            break;
          case 'xml':
            contentType = 'application/xml';
            break;
          case 'image':
            contentType = 'image/jpeg';
            break;
        }
      }
      
      // Set headers
      res.setHeader('Content-Type', contentType);
      res.setHeader('Content-Disposition', `attachment; filename="${document.name}"`);
      
      // Send content
      res.send(content);
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Update document
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async updateDocument(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      
      let fileContent;
      
      if (req.file) {
        // Read file content
        const fs = require('fs').promises;
        fileContent = await fs.readFile(req.file.path);
        
        // Clean up temporary file
        try {
          await fs.unlink(req.file.path);
        } catch (unlinkError) {
          logger.warn('Error deleting temporary file', { path: req.file.path, error: unlinkError });
        }
      } else if (req.body.content) {
        fileContent = req.body.content;
      }
      
      const document = await DocumentService.updateDocument(req.params.id, req.body, fileContent, userId);
      
      res.status(200).json({
        success: true,
        data: document
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Delete document
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async deleteDocument(req, res, next) {
    try {
      await DocumentService.deleteDocument(req.params.id);
      
      res.status(200).json({
        success: true,
        message: 'Document deleted successfully'
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Verify document with blockchain
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async verifyDocument(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const verification = await DocumentService.verifyDocument(req.params.id, userId);
      
      res.status(200).json({
        success: true,
        data: verification
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Check verification status for document
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async checkVerificationStatus(req, res, next) {
    try {
      const status = await DocumentService.checkVerificationStatus(req.params.id);
      
      res.status(200).json({
        success: true,
        data: status
      });
    } catch (error) {
      next(error);
    }
  }
}

module.exports = new DocumentController();
