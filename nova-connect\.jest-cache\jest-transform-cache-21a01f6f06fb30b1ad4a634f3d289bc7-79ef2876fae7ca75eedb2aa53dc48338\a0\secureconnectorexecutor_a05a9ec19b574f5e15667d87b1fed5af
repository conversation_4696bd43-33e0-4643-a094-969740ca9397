d6dbe9655ff96223ade118f85ebe7547
/**
 * NovaFuse Universal API Connector Secure Executor
 * 
 * This module provides a secure implementation of the connector executor
 * with SSRF protection, input validation, and rate limiting.
 */

const axios = require('axios');
const jsonpath = require('jsonpath');
const {
  performance
} = require('perf_hooks');
const {
  v4: uuidv4
} = require('uuid');
const {
  SSRFProtection,
  InputValidator
} = require('../security');

/**
 * Secure Connector Executor
 * 
 * Executes API connectors with security protections.
 */
class SecureConnectorExecutor {
  constructor(connectorRegistry, options = {}) {
    this.connectorRegistry = connectorRegistry;
    this.options = {
      enableMetrics: true,
      enableCaching: true,
      cacheTTL: 3600,
      // 1 hour
      defaultTimeout: 30000,
      // 30 seconds
      maxConcurrentRequests: 50,
      ...options
    };

    // Initialize SSRF protection
    this.ssrfProtection = new SSRFProtection({
      allowedProtocols: ['https:'],
      allowPrivateIPs: false
    });

    // Initialize metrics
    this.metrics = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      blockedRequests: 0,
      totalRequestTime: 0,
      averageRequestTime: 0
    };

    // Initialize active requests tracking
    this.activeRequests = new Set();
  }

  /**
   * Execute a connector endpoint
   * @param {string} connectorId - Connector ID
   * @param {string} endpointId - Endpoint ID
   * @param {Object} params - Parameters for the endpoint
   * @returns {Promise<Object>} - Execution result
   */
  async executeConnector(connectorId, endpointId, params = {}) {
    const startTime = this.options.enableMetrics ? performance.now() : 0;
    const requestId = uuidv4();
    try {
      // Check if we're at the concurrent request limit
      if (this.activeRequests.size >= this.options.maxConcurrentRequests) {
        throw new Error('Maximum concurrent requests limit reached');
      }

      // Add to active requests
      this.activeRequests.add(requestId);

      // Get connector
      const connector = this.connectorRegistry.getConnector(connectorId);
      if (!connector) {
        throw new Error(`Connector ${connectorId} not found`);
      }

      // Find endpoint
      const endpoint = connector.endpoints.find(e => e.id === endpointId);
      if (!endpoint) {
        throw new Error(`Endpoint ${endpointId} not found in connector ${connectorId}`);
      }

      // Validate parameters
      this.validateParameters(params, endpoint);

      // Build request URL
      let url = connector.configuration.baseUrl;
      let path = endpoint.path;

      // Replace path parameters
      if (params.path) {
        for (const [key, value] of Object.entries(params.path)) {
          // Validate path parameter
          if (!InputValidator.isXssSafe(value) || !InputValidator.isCommandSafe(value)) {
            throw new Error(`Invalid path parameter: ${key}`);
          }
          path = path.replace(`{${key}}`, encodeURIComponent(value));
        }
      }
      url = url.endsWith('/') ? `${url}${path.startsWith('/') ? path.substring(1) : path}` : `${url}${path.startsWith('/') ? path : `/${path}`}`;

      // Check URL for SSRF
      const isSafe = await this.ssrfProtection.isSafeUrl(url);
      if (!isSafe) {
        if (this.options.enableMetrics) {
          this.metrics.totalRequests++;
          this.metrics.blockedRequests++;
        }
        throw new Error(`URL blocked by SSRF protection: ${url}`);
      }

      // Build request headers
      const headers = {
        ...connector.configuration.headers
      };

      // Add authentication headers
      if (connector.authentication.type === 'API_KEY') {
        const apiKeyField = Object.keys(connector.authentication.fields).find(f => connector.authentication.fields[f].type === 'string' && connector.authentication.fields[f].sensitive === true);
        if (apiKeyField && params.auth && params.auth[apiKeyField]) {
          headers['Authorization'] = `Bearer ${params.auth[apiKeyField]}`;
        }
      } else if (connector.authentication.type === 'BASIC') {
        if (params.auth && params.auth.username && params.auth.password) {
          const auth = Buffer.from(`${params.auth.username}:${params.auth.password}`).toString('base64');
          headers['Authorization'] = `Basic ${auth}`;
        }
      } else if (connector.authentication.type === 'OAUTH2') {
        if (params.auth && params.auth.token) {
          headers['Authorization'] = `Bearer ${params.auth.token}`;
        }
      }

      // Add custom headers from parameters
      if (params.headers) {
        // Validate headers
        for (const [key, value] of Object.entries(params.headers)) {
          if (!InputValidator.isXssSafe(value)) {
            throw new Error(`Invalid header value for ${key}`);
          }
          headers[key] = value;
        }
      }

      // Build request config
      const requestConfig = {
        url,
        method: endpoint.method,
        headers,
        params: params.query,
        data: params.body,
        timeout: params.timeout || connector.configuration.timeout || this.options.defaultTimeout
      };

      // Execute request
      const response = await axios(requestConfig);

      // Extract data using JSONPath if specified
      let result = response.data;
      if (endpoint.response && endpoint.response.dataPath) {
        result = jsonpath.query(response.data, endpoint.response.dataPath);
      }

      // Update metrics
      if (this.options.enableMetrics) {
        const endTime = performance.now();
        const duration = endTime - startTime;
        this.metrics.totalRequests++;
        this.metrics.successfulRequests++;
        this.metrics.totalRequestTime += duration;
        this.metrics.averageRequestTime = this.metrics.totalRequestTime / (this.metrics.successfulRequests + this.metrics.failedRequests);
      }

      // Remove from active requests
      this.activeRequests.delete(requestId);
      return {
        success: true,
        data: result,
        statusCode: response.status
      };
    } catch (error) {
      // Update metrics
      if (this.options.enableMetrics) {
        const endTime = performance.now();
        const duration = endTime - startTime;
        this.metrics.totalRequests++;
        this.metrics.failedRequests++;
        if (this.metrics.successfulRequests > 0) {
          this.metrics.totalRequestTime += duration;
          this.metrics.averageRequestTime = this.metrics.totalRequestTime / (this.metrics.successfulRequests + this.metrics.failedRequests);
        }
      }

      // Remove from active requests
      this.activeRequests.delete(requestId);
      return {
        success: false,
        error: error.message,
        statusCode: error.response ? error.response.status : 500
      };
    }
  }

  /**
   * Validate parameters against endpoint schema
   * @param {Object} params - Parameters to validate
   * @param {Object} endpoint - Endpoint definition
   * @throws {Error} - If parameters are invalid
   */
  validateParameters(params, endpoint) {
    // Validate path parameters
    if (endpoint.parameters && endpoint.parameters.path) {
      for (const [key, schema] of Object.entries(endpoint.parameters.path)) {
        if (schema.required && (!params.path || params.path[key] === undefined)) {
          throw new Error(`Required path parameter '${key}' is missing`);
        }
      }
    }

    // Validate query parameters
    if (endpoint.parameters && endpoint.parameters.query) {
      for (const [key, schema] of Object.entries(endpoint.parameters.query)) {
        if (schema.required && (!params.query || params.query[key] === undefined)) {
          throw new Error(`Required query parameter '${key}' is missing`);
        }
      }
    }

    // Validate body parameters
    if (endpoint.parameters && endpoint.parameters.body && endpoint.parameters.body.required) {
      if (!params.body) {
        throw new Error('Request body is required');
      }

      // Validate body against schema if available
      if (endpoint.parameters.body.properties) {
        const result = InputValidator.validateObject(params.body, endpoint.parameters.body);
        if (!result.isValid) {
          throw new Error(`Invalid request body: ${result.errors.join(', ')}`);
        }
      }
    }
  }

  /**
   * Get metrics for the connector executor
   * @returns {Object} - Metrics
   */
  getMetrics() {
    return this.metrics;
  }

  /**
   * Get active requests
   * @returns {Array} - Active requests
   */
  getActiveRequests() {
    return Array.from(this.activeRequests);
  }

  /**
   * Add allowed hosts to the SSRF protection whitelist
   * @param {string|string[]} hosts - Host(s) to add
   */
  addAllowedHosts(hosts) {
    this.ssrfProtection.addAllowedHosts(hosts);
  }

  /**
   * Add allowed domains to the SSRF protection whitelist
   * @param {string|string[]} domains - Domain(s) to add
   */
  addAllowedDomains(domains) {
    this.ssrfProtection.addAllowedDomains(domains);
  }
}
module.exports = SecureConnectorExecutor;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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