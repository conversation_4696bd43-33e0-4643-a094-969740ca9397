/**
 * Credential Routes
 */

const express = require('express');
const router = express.Router();
const CredentialController = require('../controllers/CredentialController');

// Get all credentials
router.get('/', (req, res, next) => {
  CredentialController.getAllCredentials(req, res, next);
});

// Create a new credential
router.post('/', (req, res, next) => {
  CredentialController.createCredential(req, res, next);
});

// Get credential by ID
router.get('/:id', (req, res, next) => {
  CredentialController.getCredentialById(req, res, next);
});

// Update credential
router.put('/:id', (req, res, next) => {
  CredentialController.updateCredential(req, res, next);
});

// Delete credential
router.delete('/:id', (req, res, next) => {
  CredentialController.deleteCredential(req, res, next);
});

module.exports = router;
