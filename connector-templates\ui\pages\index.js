import React from 'react';
import { 
  Box, 
  Typography, 
  Grid, 
  Card, 
  CardContent, 
  CardActions, 
  Button,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider
} from '@mui/material';
import {
  Add as AddIcon,
  Storage as StorageIcon,
  Build as BuildIcon,
  Security as SecurityIcon,
  Code as CodeIcon,
  Transform as TransformIcon,
  Notifications as NotificationsIcon
} from '@mui/icons-material';
import Link from 'next/link';

export default function Dashboard() {
  // Mock data for recent connectors
  const recentConnectors = [
    { id: 1, name: 'AWS Security Hub', category: 'Cloud Security', lastUpdated: '2 hours ago' },
    { id: 2, name: 'Okta', category: 'Identity & Access Management', lastUpdated: '1 day ago' },
    { id: 3, name: '<PERSON><PERSON>', category: 'Workflow & Ticketing', lastUpdated: '3 days ago' }
  ];

  // Mock data for recent activities
  const recentActivities = [
    { id: 1, action: 'Created connector', target: 'AWS Security Hub', time: '2 hours ago' },
    { id: 2, action: 'Updated authentication', target: 'Okta', time: '1 day ago' },
    { id: 3, action: 'Added transformation', target: '<PERSON>ra', time: '3 days ago' },
    { id: 4, action: 'Tested endpoint', target: 'AWS Security Hub', time: '4 days ago' }
  ];

  return (
    <Box>
      <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Dashboard
        </Typography>
        <Link href="/builder" passHref>
          <Button 
            variant="contained" 
            startIcon={<AddIcon />}
            sx={{ 
              backgroundColor: 'primary.main',
              '&:hover': {
                backgroundColor: 'primary.dark',
              }
            }}
          >
            Create New Connector
          </Button>
        </Link>
      </Box>

      <Grid container spacing={4}>
        {/* Quick Actions */}
        <Grid item xs={12}>
          <Typography variant="h6" gutterBottom>
            Quick Actions
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column', backgroundColor: 'background.paper' }}>
                <CardContent sx={{ flexGrow: 1 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'center', mb: 2 }}>
                    <BuildIcon sx={{ fontSize: 48, color: 'primary.main' }} />
                  </Box>
                  <Typography variant="h6" component="h2" align="center">
                    Build Connector
                  </Typography>
                  <Typography variant="body2" color="text.secondary" align="center">
                    Create a new API connector from scratch
                  </Typography>
                </CardContent>
                <CardActions>
                  <Link href="/builder" passHref style={{ width: '100%' }}>
                    <Button size="small" fullWidth>Start Building</Button>
                  </Link>
                </CardActions>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column', backgroundColor: 'background.paper' }}>
                <CardContent sx={{ flexGrow: 1 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'center', mb: 2 }}>
                    <StorageIcon sx={{ fontSize: 48, color: 'primary.main' }} />
                  </Box>
                  <Typography variant="h6" component="h2" align="center">
                    My Connectors
                  </Typography>
                  <Typography variant="body2" color="text.secondary" align="center">
                    View and manage your existing connectors
                  </Typography>
                </CardContent>
                <CardActions>
                  <Link href="/connectors" passHref style={{ width: '100%' }}>
                    <Button size="small" fullWidth>View Connectors</Button>
                  </Link>
                </CardActions>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column', backgroundColor: 'background.paper' }}>
                <CardContent sx={{ flexGrow: 1 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'center', mb: 2 }}>
                    <SecurityIcon sx={{ fontSize: 48, color: 'primary.main' }} />
                  </Box>
                  <Typography variant="h6" component="h2" align="center">
                    Authentication
                  </Typography>
                  <Typography variant="body2" color="text.secondary" align="center">
                    Manage authentication methods
                  </Typography>
                </CardContent>
                <CardActions>
                  <Link href="/authentication" passHref style={{ width: '100%' }}>
                    <Button size="small" fullWidth>Manage Auth</Button>
                  </Link>
                </CardActions>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column', backgroundColor: 'background.paper' }}>
                <CardContent sx={{ flexGrow: 1 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'center', mb: 2 }}>
                    <TransformIcon sx={{ fontSize: 48, color: 'primary.main' }} />
                  </Box>
                  <Typography variant="h6" component="h2" align="center">
                    Transformations
                  </Typography>
                  <Typography variant="body2" color="text.secondary" align="center">
                    Create and manage data transformations
                  </Typography>
                </CardContent>
                <CardActions>
                  <Link href="/transformations" passHref style={{ width: '100%' }}>
                    <Button size="small" fullWidth>View Transformations</Button>
                  </Link>
                </CardActions>
              </Card>
            </Grid>
          </Grid>
        </Grid>

        {/* Recent Connectors */}
        <Grid item xs={12} md={6}>
          <Typography variant="h6" gutterBottom>
            Recent Connectors
          </Typography>
          <Paper sx={{ backgroundColor: 'background.paper' }}>
            <List>
              {recentConnectors.map((connector, index) => (
                <React.Fragment key={connector.id}>
                  <ListItem button component={Link} href={`/connectors/${connector.id}`}>
                    <ListItemIcon>
                      <CodeIcon sx={{ color: 'primary.main' }} />
                    </ListItemIcon>
                    <ListItemText 
                      primary={connector.name} 
                      secondary={
                        <React.Fragment>
                          <Typography component="span" variant="body2" color="text.secondary">
                            {connector.category}
                          </Typography>
                          <br />
                          <Typography component="span" variant="body2" color="text.secondary">
                            Last updated: {connector.lastUpdated}
                          </Typography>
                        </React.Fragment>
                      } 
                    />
                  </ListItem>
                  {index < recentConnectors.length - 1 && <Divider />}
                </React.Fragment>
              ))}
            </List>
            <Box sx={{ p: 2, display: 'flex', justifyContent: 'center' }}>
              <Link href="/connectors" passHref>
                <Button size="small">View All Connectors</Button>
              </Link>
            </Box>
          </Paper>
        </Grid>

        {/* Recent Activity */}
        <Grid item xs={12} md={6}>
          <Typography variant="h6" gutterBottom>
            Recent Activity
          </Typography>
          <Paper sx={{ backgroundColor: 'background.paper' }}>
            <List>
              {recentActivities.map((activity, index) => (
                <React.Fragment key={activity.id}>
                  <ListItem>
                    <ListItemIcon>
                      <NotificationsIcon sx={{ color: 'primary.main' }} />
                    </ListItemIcon>
                    <ListItemText 
                      primary={`${activity.action}: ${activity.target}`} 
                      secondary={activity.time} 
                    />
                  </ListItem>
                  {index < recentActivities.length - 1 && <Divider />}
                </React.Fragment>
              ))}
            </List>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
}
