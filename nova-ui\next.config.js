/**
 * Next.js Configuration
 * 
 * This file contains the configuration for the Next.js application.
 */

/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  swcMinify: true,
  
  // Configure environment variables
  env: {
    NEXT_PUBLIC_API_BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3000',
    NEXT_PUBLIC_DEFAULT_PRODUCT: process.env.NEXT_PUBLIC_DEFAULT_PRODUCT || 'novaPrime'
  },
  
  // Configure image domains
  images: {
    domains: ['localhost', 'novafuse.io'],
  },
  
  // Configure redirects
  async redirects() {
    return [
      {
        source: '/',
        destination: '/dashboard',
        permanent: true,
      },
    ];
  },
  
  // Configure rewrites
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/:path*`,
      },
    ];
  },
  
  // Configure webpack
  webpack(config) {
    // Add SVG support
    config.module.rules.push({
      test: /\.svg$/,
      use: ['@svgr/webpack'],
    });
    
    return config;
  },
  
  // Configure TypeScript
  typescript: {
    // !! WARN !!
    // Dangerously allow production builds to successfully complete even if
    // your project has type errors.
    // !! WARN !!
    ignoreBuildErrors: false,
  },
  
  // Configure ESLint
  eslint: {
    // Warning: This allows production builds to successfully complete even if
    // your project has ESLint errors.
    ignoreDuringBuilds: false,
  },
  
  // Configure output directory
  distDir: 'build',
  
  // Configure trailing slash
  trailingSlash: false,
  
  // Configure compression
  compress: true,
  
  // Configure powered by header
  poweredByHeader: false,
  
  // Configure base path
  basePath: '',
  
  // Configure asset prefix
  assetPrefix: '',
  
  // Configure page extensions
  pageExtensions: ['js', 'jsx', 'ts', 'tsx'],
  
  // Configure internationalization
  i18n: {
    locales: ['en', 'fr', 'de', 'es'],
    defaultLocale: 'en',
  },
};

module.exports = nextConfig;
