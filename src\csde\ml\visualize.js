/**
 * CSDE ML Visualization Script
 * 
 * This script visualizes the ML test results.
 */

const fs = require('fs');
const path = require('path');

// Load metrics
const metricsPath = path.join(__dirname, 'output', 'ml_metrics.json');
const metrics = JSON.parse(fs.readFileSync(metricsPath, 'utf8'));

// Load training data
const trainingDataPath = path.join(__dirname, 'output', 'training_data.json');
const trainingData = JSON.parse(fs.readFileSync(trainingDataPath, 'utf8'));

// Create HTML visualization
const html = `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>CSDE ML Test Results</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: #f8f9fa;
      padding: 20px;
    }
    .card {
      margin-bottom: 20px;
      border-radius: 10px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    .card-header {
      background-color: #0a84ff;
      color: white;
      font-weight: bold;
      border-radius: 10px 10px 0 0 !important;
    }
    .metric-value {
      font-size: 2.5rem;
      font-weight: bold;
      color: #0a84ff;
    }
    .metric-label {
      font-size: 1rem;
      color: #6c757d;
    }
    .chart-container {
      height: 400px;
      margin-bottom: 20px;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="row mb-4">
      <div class="col-12">
        <h1 class="text-center">CSDE ML Test Results</h1>
        <p class="text-center text-muted">Performance metrics for the CSDE ML components</p>
      </div>
    </div>

    <div class="row mb-4">
      <div class="col-md-3">
        <div class="card">
          <div class="card-header">Accuracy</div>
          <div class="card-body text-center">
            <div class="metric-value">${(metrics.accuracy * 100).toFixed(2)}%</div>
            <div class="metric-label">Correct Predictions</div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card">
          <div class="card-header">Average Error</div>
          <div class="card-body text-center">
            <div class="metric-value">${(metrics.averageError * 100).toFixed(2)}%</div>
            <div class="metric-label">Mean Absolute Error</div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card">
          <div class="card-header">Processing Time</div>
          <div class="card-body text-center">
            <div class="metric-value">${metrics.processingTime} ms</div>
            <div class="metric-label">Total Time</div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card">
          <div class="card-header">Throughput</div>
          <div class="card-body text-center">
            <div class="metric-value">${metrics.samplesPerSecond.toFixed(2)}</div>
            <div class="metric-label">Samples Per Second</div>
          </div>
        </div>
      </div>
    </div>

    <div class="row mb-4">
      <div class="col-md-6">
        <div class="card">
          <div class="card-header">Error Distribution</div>
          <div class="card-body">
            <div class="chart-container">
              <canvas id="errorChart"></canvas>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="card">
          <div class="card-header">Component Correlation</div>
          <div class="card-body">
            <div class="chart-container">
              <canvas id="correlationChart"></canvas>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="row mb-4">
      <div class="col-12">
        <div class="card">
          <div class="card-header">Detailed Metrics</div>
          <div class="card-body">
            <table class="table table-striped">
              <thead>
                <tr>
                  <th>Metric</th>
                  <th>Value</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>Total Samples</td>
                  <td>${metrics.totalSamples}</td>
                </tr>
                <tr>
                  <td>Correct Predictions</td>
                  <td>${metrics.correctPredictions}</td>
                </tr>
                <tr>
                  <td>Accuracy</td>
                  <td>${(metrics.accuracy * 100).toFixed(2)}%</td>
                </tr>
                <tr>
                  <td>Average Error</td>
                  <td>${(metrics.averageError * 100).toFixed(2)}%</td>
                </tr>
                <tr>
                  <td>Max Error</td>
                  <td>${(metrics.maxError * 100).toFixed(2)}%</td>
                </tr>
                <tr>
                  <td>Min Error</td>
                  <td>${(metrics.minError * 100).toFixed(2)}%</td>
                </tr>
                <tr>
                  <td>Processing Time</td>
                  <td>${metrics.processingTime} ms</td>
                </tr>
                <tr>
                  <td>Samples Per Second</td>
                  <td>${metrics.samplesPerSecond.toFixed(2)}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    // Prepare data for error distribution chart
    const errorBins = [0, 0.1, 0.5, 1, 5, 10, 50, 100, 500, 1000];
    const errorCounts = Array(errorBins.length).fill(0);
    
    // Calculate expected vs actual values for each sample
    const expectedValues = [];
    const actualValues = [];
    const errors = [];
    
    // Process training data to calculate errors
    ${JSON.stringify(trainingData)}.forEach(sample => {
      const expectedValue = sample.output.csdeValue;
      
      // Calculate CSDE value using the formula
      const complianceScore = sample.input.complianceData.complianceScore;
      const gcpScore = sample.input.gcpData.integrationScore;
      const cyberSafetyScore = sample.input.cyberSafetyData.safetyScore;
      
      // Use the tensor formula
      const nistValue = complianceScore * 10;
      const gcpValue = gcpScore * 10;
      const cyberSafetyValue = cyberSafetyScore * 31.42;
      const tensorValue = nistValue * gcpValue * 3;
      const fusionValue = tensorValue + cyberSafetyValue * 1.618;
      const actualValue = fusionValue * 3141.59;
      
      expectedValues.push(expectedValue);
      actualValues.push(actualValue);
      
      // Calculate error
      const error = Math.abs(expectedValue - actualValue) / expectedValue * 100;
      errors.push(error);
      
      // Increment error bin count
      for (let i = 0; i < errorBins.length; i++) {
        if (error <= errorBins[i] || i === errorBins.length - 1) {
          errorCounts[i]++;
          break;
        }
      }
    });
    
    // Create error distribution chart
    const errorCtx = document.getElementById('errorChart').getContext('2d');
    new Chart(errorCtx, {
      type: 'bar',
      data: {
        labels: errorBins.map((bin, index) => {
          if (index === 0) return '0%';
          const prevBin = errorBins[index - 1];
          return \`\${prevBin}% - \${bin}%\`;
        }),
        datasets: [{
          label: 'Number of Samples',
          data: errorCounts,
          backgroundColor: 'rgba(10, 132, 255, 0.6)',
          borderColor: 'rgba(10, 132, 255, 1)',
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true,
            title: {
              display: true,
              text: 'Number of Samples'
            }
          },
          x: {
            title: {
              display: true,
              text: 'Error Range'
            }
          }
        },
        plugins: {
          title: {
            display: true,
            text: 'Distribution of Prediction Errors'
          }
        }
      }
    });
    
    // Create correlation chart
    const correlationCtx = document.getElementById('correlationChart').getContext('2d');
    new Chart(correlationCtx, {
      type: 'scatter',
      data: {
        datasets: [{
          label: 'Expected vs Actual',
          data: expectedValues.map((expected, index) => ({
            x: expected,
            y: actualValues[index]
          })),
          backgroundColor: 'rgba(10, 132, 255, 0.6)',
          borderColor: 'rgba(10, 132, 255, 1)',
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            title: {
              display: true,
              text: 'Actual CSDE Value'
            }
          },
          x: {
            title: {
              display: true,
              text: 'Expected CSDE Value'
            }
          }
        },
        plugins: {
          title: {
            display: true,
            text: 'Correlation between Expected and Actual Values'
          }
        }
      }
    });
  </script>
</body>
</html>
`;

// Save HTML visualization
const outputPath = path.join(__dirname, 'output', 'visualization.html');
fs.writeFileSync(outputPath, html);

console.log(`Visualization saved to ${outputPath}`);
console.log('Open this file in a browser to view the visualization');
