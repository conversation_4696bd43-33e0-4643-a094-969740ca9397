version: '3.8'

services:
  # Simulated GKE environment using Kubernetes-in-Docker
  kubernetes:
    image: rancher/k3s:latest
    privileged: true
    environment:
      - K3S_KUBECONFIG_OUTPUT=/output/kubeconfig.yaml
      - K3S_KUBECONFIG_MODE=666
    volumes:
      - k3s-server:/var/lib/rancher/k3s
      - ./output:/output
    ports:
      - "6443:6443"  # Kubernetes API
      - "8080:80"    # Ingress controller
    command: server --disable=traefik

  # NovaFuse API Gateway
  novafuse-gateway:
    image: nginx:alpine
    volumes:
      - ./config/nginx:/etc/nginx/conf.d
    ports:
      - "3000:80"
    depends_on:
      - novafuse-api
      - novafuse-uac

  # NovaFuse API Services
  novafuse-api:
    build:
      context: ./novafuse-api
      dockerfile: Dockerfile
    environment:
      - NODE_ENV=production
      - PORT=3001
      - MONGODB_URI=mongodb://mongodb:27017/novafuse
      - FEATURE_FLAGS_ENABLED=true
      - GCP_INTEGRATION_ENABLED=true
      - SCC_URL=http://security-command-center:8081
      - IAM_URL=http://cloud-iam:8082
      - BIGQUERY_URL=http://bigquery:8083
      - STORAGE_URL=http://cloud-storage:8084
      - FUNCTIONS_URL=http://cloud-functions:8085
      - MONITORING_URL=http://cloud-monitoring:8086
    ports:
      - "3001:3001"
    depends_on:
      - mongodb
      - redis

  # NovaConnect (UAC)
  novafuse-uac:
    build:
      context: ./novafuse-uac
      dockerfile: Dockerfile
    environment:
      - NODE_ENV=production
      - PORT=3002
      - MONGODB_URI=mongodb://mongodb:27017/novafuse-uac
      - REDIS_URI=redis://redis:6379
      - SECRET_KEY=simulation-secret-key
    ports:
      - "3002:3002"
    depends_on:
      - mongodb
      - redis

  # Simulated Security Command Center
  security-command-center:
    build:
      context: ./gcp-simulators/scc
      dockerfile: Dockerfile
    ports:
      - "8081:8081"
    environment:
      - PORT=8081
      - FINDINGS_DB=mongodb://mongodb:27017/scc-findings

  # Simulated Cloud IAM
  cloud-iam:
    build:
      context: ./gcp-simulators/iam
      dockerfile: Dockerfile
    ports:
      - "8082:8082"
    environment:
      - PORT=8082
      - IAM_DB=mongodb://mongodb:27017/cloud-iam

  # Simulated BigQuery
  bigquery:
    build:
      context: ./gcp-simulators/bigquery
      dockerfile: Dockerfile
    ports:
      - "8083:8083"
    environment:
      - PORT=8083
      - BQ_STORAGE=./data

  # Simulated Cloud Storage
  cloud-storage:
    build:
      context: ./gcp-services
      dockerfile: Dockerfile.cloud-storage
    ports:
      - "8084:8084"
    environment:
      - PORT=8084

  # Simulated Cloud Functions
  cloud-functions:
    build:
      context: ./gcp-services
      dockerfile: Dockerfile.cloud-functions
    ports:
      - "8085:8085"
    environment:
      - PORT=8085

  # Simulated Cloud Monitoring
  cloud-monitoring:
    build:
      context: ./gcp-services
      dockerfile: Dockerfile.cloud-monitoring
    ports:
      - "8086:8086"
    environment:
      - PORT=8086

  # MongoDB for data storage
  mongodb:
    image: mongo:latest
    volumes:
      - mongodb_data:/data/db
    ports:
      - "27017:27017"

  # Redis for caching and pub/sub
  redis:
    image: redis:alpine
    ports:
      - "6379:6379"

  # UI for testing and demonstration
  novafuse-ui:
    build:
      context: ./novafuse-ui
      dockerfile: Dockerfile
    ports:
      - "3003:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:3000
      - REACT_APP_FEATURE_FLAGS_ENABLED=true
    depends_on:
      - novafuse-gateway

volumes:
  k3s-server:
  mongodb_data:
