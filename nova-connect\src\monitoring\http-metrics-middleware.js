/**
 * NovaFuse Universal API Connector - HTTP Metrics Middleware
 * 
 * This module provides middleware for collecting HTTP metrics.
 */

const metricsService = require('./metrics-service');
const { createLogger } = require('../utils/logger');

const logger = createLogger('http-metrics');

// Initialize metrics
const httpRequestsTotal = metricsService.registerCounter(
  'http_requests_total',
  'Total number of HTTP requests',
  ['method', 'path', 'status']
);

const httpRequestDuration = metricsService.registerHistogram(
  'http_request_duration_seconds',
  'HTTP request duration in seconds',
  ['method', 'path', 'status'],
  [0.01, 0.05, 0.1, 0.5, 1, 2.5, 5, 10]
);

const httpRequestSize = metricsService.registerHistogram(
  'http_request_size_bytes',
  'HTTP request size in bytes',
  ['method', 'path']
);

const httpResponseSize = metricsService.registerHistogram(
  'http_response_size_bytes',
  'HTTP response size in bytes',
  ['method', 'path', 'status']
);

const activeRequests = metricsService.registerGauge(
  'http_active_requests',
  'Number of active HTTP requests',
  ['method', 'path']
);

/**
 * Normalize the path by replacing path parameters with placeholders
 * 
 * @param {string} path - The request path
 * @returns {string} - The normalized path
 */
function normalizePath(path) {
  // Replace numeric IDs with :id
  return path
    .replace(/\/\d+/g, '/:id')
    .replace(/\/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/gi, '/:uuid');
}

/**
 * Calculate the size of the request
 * 
 * @param {Object} req - The request object
 * @returns {number} - The request size in bytes
 */
function calculateRequestSize(req) {
  let size = 0;
  
  // Add request line size
  size += Buffer.byteLength(`${req.method} ${req.originalUrl} HTTP/${req.httpVersion}\r\n`);
  
  // Add headers size
  for (const [name, value] of Object.entries(req.headers)) {
    size += Buffer.byteLength(`${name}: ${value}\r\n`);
  }
  
  // Add body size
  if (req.body) {
    size += Buffer.byteLength(JSON.stringify(req.body));
  }
  
  return size;
}

/**
 * HTTP metrics middleware
 * 
 * @param {Object} req - The request object
 * @param {Object} res - The response object
 * @param {Function} next - The next middleware function
 */
function httpMetricsMiddleware(req, res, next) {
  // Skip metrics endpoint to avoid circular metrics
  if (req.path === '/metrics') {
    return next();
  }
  
  // Normalize path
  const path = normalizePath(req.path);
  
  // Record start time
  const startTime = process.hrtime();
  
  // Increment active requests
  activeRequests.inc(1, { method: req.method, path });
  
  // Record request size
  const requestSize = calculateRequestSize(req);
  httpRequestSize.observe(requestSize, { method: req.method, path });
  
  // Track response size
  let responseSize = 0;
  const originalWrite = res.write;
  const originalEnd = res.end;
  
  res.write = function(chunk, ...args) {
    if (chunk) {
      responseSize += chunk.length;
    }
    return originalWrite.apply(res, [chunk, ...args]);
  };
  
  res.end = function(chunk, ...args) {
    if (chunk) {
      responseSize += chunk.length;
    }
    
    // Calculate duration
    const [seconds, nanoseconds] = process.hrtime(startTime);
    const duration = seconds + nanoseconds / 1e9;
    
    // Record metrics
    const status = res.statusCode.toString();
    
    httpRequestsTotal.inc(1, { method: req.method, path, status });
    httpRequestDuration.observe(duration, { method: req.method, path, status });
    httpResponseSize.observe(responseSize, { method: req.method, path, status });
    
    // Decrement active requests
    activeRequests.dec(1, { method: req.method, path });
    
    // Log request
    logger.debug(`${req.method} ${req.originalUrl} ${status} ${duration.toFixed(3)}s ${responseSize} bytes`);
    
    return originalEnd.apply(res, [chunk, ...args]);
  };
  
  next();
}

module.exports = httpMetricsMiddleware;
