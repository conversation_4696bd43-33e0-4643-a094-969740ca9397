/**
 * Optimization Routes
 * 
 * This file contains routes for optimization functionality.
 */

const express = require('express');
const router = express.Router();
const { asyncHandler } = require('../middleware/errorHandlingMiddleware');
const DatabaseOptimizationService = require('../services/DatabaseOptimizationService');
const logger = require('../utils/logger');

/**
 * Get database optimization stats
 * GET /optimization/database/stats
 */
router.get('/database/stats', asyncHandler(async (req, res) => {
  const stats = DatabaseOptimizationService.getStats();
  res.json({ status: 'ok', stats });
}));

/**
 * Run database optimization
 * POST /optimization/database/optimize
 */
router.post('/database/optimize', asyncHandler(async (req, res) => {
  logger.info('Manual database optimization requested');
  
  const results = await DatabaseOptimizationService.optimizeDatabase();
  
  res.json({
    status: 'ok',
    message: 'Database optimization completed',
    results
  });
}));

/**
 * Run performance analysis
 * POST /optimization/performance/analyze
 */
router.post('/performance/analyze', asyncHandler(async (req, res) => {
  logger.info('Performance analysis requested');
  
  // Import the performance optimization script
  const performanceOptimizer = require('../../scripts/optimize-performance');
  
  // Analyze system resources
  const systemInfo = performanceOptimizer.analyzeSystemResources();
  
  // Analyze Node.js configuration
  const nodeInfo = performanceOptimizer.analyzeNodeConfiguration();
  
  // Analyze MongoDB
  const mongoInfo = await performanceOptimizer.analyzeMongoDb();
  
  // Analyze Redis
  const redisInfo = await performanceOptimizer.analyzeRedis();
  
  // Analyze code
  const codeInfo = performanceOptimizer.analyzeCode();
  
  // Generate recommendations
  const recommendations = performanceOptimizer.generateRecommendations(
    systemInfo,
    nodeInfo,
    mongoInfo,
    redisInfo,
    codeInfo
  );
  
  res.json({
    status: 'ok',
    message: 'Performance analysis completed',
    systemInfo,
    nodeInfo,
    mongoInfo,
    redisInfo,
    codeInfo,
    recommendations
  });
}));

module.exports = router;
