<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NovaStore Partner Empowerment Model</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .diagram-container {
            position: relative;
            width: 800px;
            height: 600px;
            margin: 0 auto;
            background-color: white;
            border: 1px solid #ddd;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            box-sizing: border-box;
        }
        .container-box {
            position: absolute;
            border: 2px solid #666;
            border-radius: 10px;
            padding: 30px 10px 10px;
            box-sizing: border-box;
        }
        .container-label {
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            font-weight: bold;
            font-size: 16px;
            text-align: center;
        }
        .component-box {
            position: absolute;
            background-color: white;
            border: 2px solid #333;
            border-radius: 6px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            padding: 10px;
            box-sizing: border-box;
            font-size: 14px;
            line-height: 1.2;
        }
        .component-label {
            font-weight: bold;
            margin-bottom: 4px;
        }
        .component-number {
            position: absolute;
            top: -10px;
            left: -10px;
            width: 20px;
            height: 20px;
            background-color: #333;
            color: white;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 12px;
            font-weight: bold;
        }
        .arrow {
            position: absolute;
            background-color: #333;
            width: 2px;
        }
        .legend {
            position: absolute;
            right: 10px;
            bottom: -90px;
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 5px;
            width: 200px;
        }
        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
            font-size: 12px;
        }
        .legend-color {
            width: 15px;
            height: 15px;
            margin-right: 5px;
            border: 1px solid #333;
        }
        .inventor-label {
            position: absolute;
            left: 10px;
            bottom: -90px;
            font-size: 12px;
            font-style: italic;
            color: #333;
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <h1>FIG. 6: NovaStore Partner Empowerment Model</h1>
    
    <div class="diagram-container">
        <!-- Main Container -->
        <div class="container-box" style="width: 750px; height: 600px; left: 25px; top: 20px;">
            <div class="container-label">NOVASTORE: PARTNER EMPOWERMENT MODEL</div>
        </div>
        
        <!-- NovaStore Core -->
        <div class="container-box" style="width: 700px; height: 120px; left: 50px; top: 70px;">
            <div class="container-label">NOVASTORE MARKETPLACE</div>
        </div>
        
        <div class="component-box" style="left: 100px; top: 110px; width: 150px; height: 60px;">
            <div class="component-number">601</div>
            <div class="component-label">Component Registry</div>
            Verified Modules
        </div>
        
        <div class="component-box" style="left: 270px; top: 110px; width: 150px; height: 60px;">
            <div class="component-number">602</div>
            <div class="component-label">Smart Contract Licensing</div>
            Automated Enforcement
        </div>
        
        <div class="component-box" style="left: 440px; top: 110px; width: 150px; height: 60px;">
            <div class="component-number">603</div>
            <div class="component-label">Verification Pipeline</div>
            Compliance Testing
        </div>
        
        <div class="component-box" style="left: 610px; top: 110px; width: 150px; height: 60px;">
            <div class="component-number">604</div>
            <div class="component-label">API Gateway</div>
            Secure Access
        </div>
        
        <!-- Revenue Model -->
        <div class="container-box" style="width: 700px; height: 120px; left: 50px; top: 210px;">
            <div class="container-label">PARTNER EMPOWERMENT REVENUE MODEL</div>
        </div>
        
        <div class="component-box" style="left: 175px; top: 250px; width: 200px; height: 60px;">
            <div class="component-number">605</div>
            <div class="component-label">Partner Revenue Share</div>
            82%
        </div>
        
        <div class="component-box" style="left: 425px; top: 250px; width: 200px; height: 60px;">
            <div class="component-number">606</div>
            <div class="component-label">Platform Revenue Share</div>
            18%
        </div>
        
        <!-- Integration Components -->
        <div class="container-box" style="width: 700px; height: 120px; left: 50px; top: 350px;">
            <div class="container-label">INTEGRATION COMPONENTS</div>
        </div>
        
        <div class="component-box" style="left: 100px; top: 390px; width: 150px; height: 60px;">
            <div class="component-number">607</div>
            <div class="component-label">Royalty Distribution</div>
            Automated Payments
        </div>
        
        <div class="component-box" style="left: 270px; top: 390px; width: 150px; height: 60px;">
            <div class="component-number">608</div>
            <div class="component-label">Certification Process</div>
            Quality Assurance
        </div>
        
        <div class="component-box" style="left: 440px; top: 390px; width: 150px; height: 60px;">
            <div class="component-number">609</div>
            <div class="component-label">Usage Analytics</div>
            Performance Metrics
        </div>
        
        <div class="component-box" style="left: 610px; top: 390px; width: 150px; height: 60px;">
            <div class="component-number">610</div>
            <div class="component-label">Governance Model</div>
            Ecosystem Integrity
        </div>
        
        <!-- Ecosystem Impact -->
        <div class="container-box" style="width: 700px; height: 100px; left: 50px; top: 490px;">
            <div class="container-label">ECOSYSTEM IMPACT</div>
        </div>
        
        <div class="component-box" style="left: 175px; top: 530px; width: 200px; height: 40px;">
            <div class="component-number">611</div>
            <div class="component-label">Economic Incentives</div>
            Security-First Development
        </div>
        
        <div class="component-box" style="left: 425px; top: 530px; width: 200px; height: 40px;">
            <div class="component-number">612</div>
            <div class="component-label">Cross-Industry Application</div>
            Regulatory Compliance
        </div>
        
        <!-- Connecting arrows -->
        <div class="arrow" style="left: 175px; top: 170px; height: 40px;"></div>
        <div class="arrow" style="left: 345px; top: 170px; height: 40px;"></div>
        <div class="arrow" style="left: 515px; top: 170px; height: 40px;"></div>
        <div class="arrow" style="left: 685px; top: 170px; height: 40px;"></div>
        
        <div class="arrow" style="left: 275px; top: 310px; height: 40px;"></div>
        <div class="arrow" style="left: 525px; top: 310px; height: 40px;"></div>
        
        <div class="arrow" style="left: 175px; top: 450px; height: 40px;"></div>
        <div class="arrow" style="left: 345px; top: 450px; height: 40px;"></div>
        <div class="arrow" style="left: 515px; top: 450px; height: 40px;"></div>
        <div class="arrow" style="left: 685px; top: 450px; height: 40px;"></div>
        
        <!-- Legend -->
        <div class="legend">
            <div class="legend-item">
                <div class="legend-color" style="background-color: #000;"></div>
                <div>NovaStore Components</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #000;"></div>
                <div>Revenue Model (82/18 Split)</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #000;"></div>
                <div>Integration Components</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #000;"></div>
                <div>Ecosystem Impact</div>
            </div>
        </div>
        
        <!-- Inventor Label -->
        <div class="inventor-label">Inventor: David Nigel Irvin</div>
    </div>
</body>
</html>
