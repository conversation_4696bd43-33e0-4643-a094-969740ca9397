/**
 * <PERSON><PERSON><PERSON> to update the AuditService middleware to include tenant information
 * 
 * This script modifies the AuditService.js file to ensure that tenant information
 * is properly captured in audit logs and sent to tenant-specific BigQuery tables.
 */

const fs = require('fs');
const path = require('path');

// Path to the AuditService.js file
const auditServicePath = path.join(__dirname, '../api/services/AuditService.js');

// Read the current file
let content = fs.readFileSync(auditServicePath, 'utf8');

// Check if the file already has tenant-specific logging
if (content.includes('logTenantEvent') && content.includes('x-tenant-id')) {
  console.log('AuditService.js already has tenant-specific logging. No changes needed.');
  process.exit(0);
}

// Update the middleware to include tenant information
const middlewarePattern = /createAuditMiddleware\(\) {[\s\S]+?this\.logEvent\(auditData\);[\s\S]+?next\(\);[\s\S]+?}/;
const updatedMiddleware = `createAuditMiddleware() {
    return (req, res, next) => {
      // Store original end method
      const originalEnd = res.end;

      // Override end method to capture response
      res.end = function(chunk, encoding) {
        // Restore original end method
        res.end = originalEnd;

        // Call original end method
        res.end(chunk, encoding);

        // Skip audit logging for certain paths
        if (req.path.startsWith('/health') || req.path.startsWith('/api/monitoring/health')) {
          return;
        }

        // Log audit event
        const auditData = {
          userId: req.user ? req.user.id : null,
          action: req.method,
          resourceType: req.path.split('/')[2] || 'unknown',
          resourceId: req.params.id || null,
          details: {
            path: req.path,
            query: req.query,
            body: req.method !== 'GET' ? req.body : null
          },
          ip: req.ip || req.connection.remoteAddress,
          userAgent: req.headers['user-agent'],
          status: res.statusCode >= 400 ? 'failure' : 'success',
          teamId: req.headers['x-team-id'] || null,
          environmentId: req.headers['x-environment-id'] || null,
          tenantId: req.headers['x-tenant-id'] || null
        };

        // If tenant ID is present, use tenant-specific logging
        if (req.headers['x-tenant-id']) {
          this.logTenantEvent(req.headers['x-tenant-id'], auditData);
        } else {
          this.logEvent(auditData);
        }
      };

      next();
    };
  }`;

// Replace the middleware in the content
content = content.replace(middlewarePattern, updatedMiddleware);

// Add logTenantEvent method if it doesn't exist
if (!content.includes('logTenantEvent')) {
  const logEventPattern = /async logEvent\(data\) {[\s\S]+?}\n  }/;
  const logTenantEventMethod = `async logEvent(data) {
    try {
      // Create audit log entry
      const logEntry = {
        id: uuidv4(),
        timestamp: new Date().toISOString(),
        userId: data.userId || null,
        action: data.action,
        resourceType: data.resourceType,
        resourceId: data.resourceId,
        details: data.details || null,
        ip: data.ip || null,
        userAgent: data.userAgent || null,
        status: data.status || 'success',
        teamId: data.teamId || null,
        environmentId: data.environmentId || null,
        tenantId: data.tenantId || null
      };

      // Log to BigQuery if enabled
      if (this.bigQueryEnabled && this.bigquery) {
        try {
          await this.logToBigQuery(logEntry);
        } catch (bigQueryError) {
          console.error('Error logging to BigQuery:', bigQueryError);
          // Continue with local logging even if BigQuery fails
        }
      }

      // Log to local file
      const auditLogs = await this.loadAuditLogs();
      auditLogs.push(logEntry);

      // Limit the size of the audit logs
      if (auditLogs.length > 10000) {
        auditLogs.splice(0, auditLogs.length - 10000);
      }

      await this.saveAuditLogs(auditLogs);

      return logEntry;
    } catch (error) {
      console.error('Error logging audit event:', error);
      // Don't throw error to prevent affecting the main request flow
    }
  }

  /**
   * Log a tenant-specific audit event
   */
  async logTenantEvent(tenantId, data) {
    try {
      // Add tenant ID to data
      const tenantData = {
        ...data,
        tenantId
      };

      // Log the event
      const logEntry = await this.logEvent(tenantData);

      // If BigQuery is enabled, also log to tenant-specific table
      if (this.bigQueryEnabled && this.bigquery && tenantId) {
        try {
          // Get tenant-specific dataset
          const dataset = this.bigquery.dataset(\`tenant_\${tenantId}\`);

          // Get audit table (create if it doesn't exist)
          let table;
          try {
            table = dataset.table('audit_logs');
            await table.get();
          } catch (tableError) {
            // Table doesn't exist, create it
            const schema = [
              { name: 'id', type: 'STRING' },
              { name: 'timestamp', type: 'TIMESTAMP' },
              { name: 'userId', type: 'STRING' },
              { name: 'action', type: 'STRING' },
              { name: 'resourceType', type: 'STRING' },
              { name: 'resourceId', type: 'STRING' },
              { name: 'details', type: 'STRING' },
              { name: 'ip', type: 'STRING' },
              { name: 'userAgent', type: 'STRING' },
              { name: 'status', type: 'STRING' },
              { name: 'teamId', type: 'STRING' },
              { name: 'environmentId', type: 'STRING' },
              { name: 'tenantId', type: 'STRING' }
            ];

            const options = {
              schema: schema,
              timePartitioning: {
                type: 'DAY',
                field: 'timestamp'
              }
            };

            await dataset.createTable('audit_logs', options);
            table = dataset.table('audit_logs');
          }

          // Insert into tenant-specific table
          await table.insert([logEntry]);
        } catch (bigQueryError) {
          console.error(\`Error logging to tenant-specific BigQuery table for tenant \${tenantId}:\`, bigQueryError);
        }
      }

      return logEntry;
    } catch (error) {
      console.error('Error logging tenant audit event:', error);
      // Don't throw error to prevent affecting the main request flow
    }
  }`;

  content = content.replace(logEventPattern, logTenantEventMethod);
}

// Add logToBigQuery method if it doesn't exist
if (!content.includes('logToBigQuery')) {
  const getAuditLogsPattern = /async getAuditLogs\(/;
  const logToBigQueryMethod = `/**
   * Log an audit event to BigQuery
   */
  async logToBigQuery(event) {
    try {
      // Get dataset reference
      const dataset = this.bigquery.dataset(this.datasetId);

      // Get table reference
      const table = dataset.table(this.tableId);

      // Insert row
      await table.insert([event]);
    } catch (error) {
      console.error('Error logging to BigQuery:', error);
      throw error;
    }
  }

  /**
   * Get audit logs
   */
  async getAuditLogs`;

  content = content.replace(getAuditLogsPattern, logToBigQueryMethod);
}

// Write the updated content back to the file
fs.writeFileSync(auditServicePath, content);

console.log('AuditService.js updated successfully with tenant-specific logging.');
