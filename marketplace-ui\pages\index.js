import { useState, useEffect } from "react";
import Head from "next/head";
import Link from "next/link";

export default function Home() {
  const [partners, setPartners] = useState([]);
  const [categories, setCategories] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // In a real implementation, this would fetch from an API
    const partnerData = [
      {
        id: "zapier",
        name: "Zapier",
        description: "Connect NovaFuse to 3,000+ apps without code. Automate workflows and integrate with your favorite tools.",
        category: "integration",
        logo: "/logos/zapier.png",
        featured: true,
        tags: ["automation", "workflow", "no-code"]
      },
      {
        id: "okta",
        name: "Ok<PERSON>",
        description: "Enterprise identity management and single sign-on. Secure access to all your applications.",
        category: "security",
        logo: "/logos/okta.png",
        featured: true,
        tags: ["identity", "authentication", "sso"]
      },
      {
        id: "airtable",
        name: "Airtable",
        description: "Low-code database integration. Build custom apps and workflows without coding expertise.",
        category: "productivity",
        logo: "/logos/airtable.png",
        featured: true,
        tags: ["database", "low-code", "collaboration"]
      },
      {
        id: "slack",
        name: "Slack",
        description: "Team communication and notifications. Get alerts and updates directly in your Slack channels.",
        category: "communication",
        logo: "/logos/slack.png",
        featured: false,
        tags: ["messaging", "alerts", "collaboration"]
      },
      {
        id: "salesforce",
        name: "Salesforce",
        description: "CRM integration for customer data. Connect compliance data with your customer records.",
        category: "crm",
        logo: "/logos/salesforce.png",
        featured: false,
        tags: ["crm", "sales", "customer-data"]
      },
      {
        id: "github",
        name: "GitHub",
        description: "Code repository integration. Track compliance issues alongside your code.",
        category: "development",
        logo: "/logos/github.png",
        featured: false,
        tags: ["code", "version-control", "development"]
      },
      {
        id: "jira",
        name: "Jira",
        description: "Issue tracking and project management. Create and track compliance tasks in your workflow.",
        category: "productivity",
        logo: "/logos/jira.png",
        featured: false,
        tags: ["project-management", "issues", "tasks"]
      },
      {
        id: "aws",
        name: "AWS",
        description: "Cloud infrastructure integration. Monitor compliance across your AWS resources.",
        category: "cloud",
        logo: "/logos/aws.png",
        featured: false,
        tags: ["cloud", "infrastructure", "monitoring"]
      }
    ];

    setPartners(partnerData);

    // Extract unique categories
    const uniqueCategories = [...new Set(partnerData.map(partner => partner.category))];
    setCategories(uniqueCategories);

    setLoading(false);
  }, []);

  const filteredPartners = selectedCategory === "all"
    ? partners
    : partners.filter(partner => partner.category === selectedCategory);

  const featuredPartners = partners.filter(partner => partner.featured);

  return (
    <div className="min-h-screen bg-gray-50">
      <Head>
        <title>NovaFuse API Superstore</title>
        <meta name="description" content="NovaFuse API Superstore - Connect with premium partners" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 py-6 sm:px-6 lg:px-8 flex justify-between items-center">
          <h1 className="text-3xl font-bold text-gray-900">NovaFuse API Superstore</h1>
          <div className="flex space-x-4">
            <Link href="/partner-portal" className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
              Partner Login
            </Link>
            <Link href="/partner-registration" className="border border-blue-600 text-blue-600 px-4 py-2 rounded hover:bg-blue-50">
              Become a Partner
            </Link>
          </div>
        </div>
      </header>

      <main>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Hero Section */}
          <div className="bg-blue-600 text-white rounded-lg p-8 mb-8">
            <h2 className="text-3xl font-bold mb-4">Integrate with NovaFuse</h2>
            <p className="text-xl mb-6">
              Connect your GRC workflows with premium partners and services.
              Streamline compliance, automate risk management, and enhance governance.
            </p>
            <Link href="#partners" className="inline-block bg-transparent text-white px-6 py-3 rounded-lg font-bold border-2 border-white hover:bg-blue-700">
              Explore Integrations
            </Link>
          </div>

          {/* Featured Partners */}
          <div id="partners" className="mb-12 pt-8">
            <h2 className="text-2xl font-bold mb-6">Featured Partners</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {featuredPartners.map(partner => (
                <div key={partner.id} className="bg-white border rounded-lg p-6 shadow-md hover:shadow-lg transition-shadow">
                  <div className="flex items-center mb-4">
                    <div className="w-16 h-16 mr-4 bg-gray-100 rounded-md flex items-center justify-center">
                      {partner.logo ? (
                        <img src={partner.logo} alt={partner.name} className="max-w-full max-h-full" />
                      ) : (
                        <span className="text-2xl">{partner.name.charAt(0)}</span>
                      )}
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold">{partner.name}</h3>
                      <p className="text-sm text-gray-500 capitalize">{partner.category}</p>
                    </div>
                  </div>
                  <p className="text-gray-700 mb-4">{partner.description}</p>
                  <div className="flex flex-wrap gap-2 mb-4">
                    {partner.tags.map(tag => (
                      <span key={tag} className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">
                        {tag}
                      </span>
                    ))}
                  </div>
                  <button className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 w-full">
                    Connect
                  </button>
                </div>
              ))}
            </div>
          </div>

          {/* All Partners */}
          <div>
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold">All Partners</h2>
              <div className="flex items-center">
                <label htmlFor="category" className="mr-2 text-gray-700">Filter by:</label>
                <select
                  id="category"
                  className="border rounded p-2"
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                >
                  <option value="all">All Categories</option>
                  {categories.map(category => (
                    <option key={category} value={category}>
                      {category.charAt(0).toUpperCase() + category.slice(1)}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {loading ? (
              <p>Loading partners...</p>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredPartners.map(partner => (
                  <div key={partner.id} className="bg-white border rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow">
                    <div className="flex items-center mb-4">
                      <div className="w-12 h-12 mr-4 bg-gray-100 rounded-md flex items-center justify-center">
                        {partner.logo ? (
                          <img src={partner.logo} alt={partner.name} className="max-w-full max-h-full" />
                        ) : (
                          <span className="text-xl">{partner.name.charAt(0)}</span>
                        )}
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold">{partner.name}</h3>
                        <p className="text-xs text-gray-500 capitalize">{partner.category}</p>
                      </div>
                    </div>
                    <p className="text-sm text-gray-700 mb-4">{partner.description}</p>
                    <button className="bg-blue-600 text-white px-3 py-1.5 text-sm rounded hover:bg-blue-700">
                      Connect
                    </button>
                  </div>
                ))}
              </div>
            )}
            <div className="text-center mt-8">
              <Link href="/partners" className="inline-block bg-blue-600 text-white px-6 py-3 rounded-lg font-bold hover:bg-blue-700">
                View All Partners
              </Link>
            </div>
          </div>
        </div>
      </main>

      <footer className="bg-gray-800 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <h3 className="text-lg font-semibold mb-4">NovaFuse API Superstore</h3>
              <p className="text-gray-400">
                Connect your GRC workflows with premium partners and services.
              </p>
            </div>
            <div>
              <h4 className="text-md font-semibold mb-4">For Partners</h4>
              <ul className="space-y-2">
                <li><Link href="/partner-registration" className="text-gray-400 hover:text-white">Become a Partner</Link></li>
                <li><Link href="/partner-portal" className="text-gray-400 hover:text-white">Partner Portal</Link></li>
                <li><Link href="/api-docs" className="text-gray-400 hover:text-white">API Documentation</Link></li>
                <li><Link href="/support" className="text-gray-400 hover:text-white">Support</Link></li>
              </ul>
            </div>
            <div>
              <h4 className="text-md font-semibold mb-4">For Customers</h4>
              <ul className="space-y-2">
                <li><Link href="/tutorials" className="text-gray-400 hover:text-white">Integration Guides</Link></li>
                <li><Link href="/api-docs" className="text-gray-400 hover:text-white">API Reference</Link></li>
                <li><Link href="/support" className="text-gray-400 hover:text-white">Support Center</Link></li>
                <li><Link href="/contact-sales" className="text-gray-400 hover:text-white">Contact Us</Link></li>
              </ul>
            </div>
            <div>
              <h4 className="text-md font-semibold mb-4">Legal</h4>
              <ul className="space-y-2">
                <li><Link href="/terms" className="text-gray-400 hover:text-white">Terms of Service</Link></li>
                <li><Link href="/privacy" className="text-gray-400 hover:text-white">Privacy Policy</Link></li>
                <li><Link href="/cookies" className="text-gray-400 hover:text-white">Cookie Policy</Link></li>
                <li><Link href="/gdpr" className="text-gray-400 hover:text-white">GDPR Compliance</Link></li>
              </ul>
            </div>
          </div>
          <div className="mt-8 pt-8 border-t border-gray-700 text-center text-gray-400">
            <p>&copy; {new Date().getFullYear()} NovaFuse. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
