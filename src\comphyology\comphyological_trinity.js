/**
 * Comphyological Trinity
 *
 * This module implements the complete Comphyological Trinity - the three laws
 * of Comphyological Governance:
 *
 * 1. First Law (Boundary Condition):
 *    "A system shall neither externalize non-resonant states nor propagate unmeasured energy transitions."
 *
 * 2. Second Law (Internal Coherence):
 *    "A system shall sustain resonance through self-similar, energy-minimizing transitions."
 *
 * 3. Third Law (Cross-Domain Harmony):
 *    "Systems shall interact through translational resonance, preserving integrity across domains."
 *
 * Together, these laws form a complete governance framework for complex systems.
 */

const EventEmitter = require('events');
const ResonanceValidator = require('./resonance_validator');
const CoherenceEnforcer = require('./coherence_enforcer');
const DomainTranslator = require('./domain_translator');

/**
 * Comphyological Trinity class
 */
class ComphyologicalTrinity extends EventEmitter {
  /**
   * Constructor
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();

    this.options = {
      // Whether to enforce each law
      enforceFirstLaw: true,
      enforceSecondLaw: true,
      enforceThirdLaw: true,

      // First Law options
      strictMode: false,
      resonanceLock: true,
      logValidation: false,

      // Second Law options
      energyMinimization: true,
      selfSimilarityPreservation: true,
      logOptimization: false,

      // Third Law options
      domains: ['cyber', 'financial', 'medical'],
      preserveResonance: true,
      logTranslations: false,

      // General options
      logGovernance: false,

      ...options
    };

    // Initialize the three laws
    this._initializeLaws();

    // Initialize metrics
    this.metrics = {
      operations: 0,
      firstLawEnforcements: 0,
      firstLawViolations: 0,
      secondLawOptimizations: 0,
      thirdLawTranslations: 0,
      fullTrinityApplications: 0
    };

    if (this.options.logGovernance) {
      console.log('Comphyological Trinity initialized with options:', this.options);
    }
  }

  /**
   * Initialize the three laws
   * @private
   */
  _initializeLaws() {
    // First Law - Boundary Condition
    this.firstLaw = new ResonanceValidator({
      strictMode: this.options.strictMode,
      logValidation: this.options.logValidation,
      resonanceLock: this.options.resonanceLock
    });

    // Second Law - Internal Coherence
    this.secondLaw = new CoherenceEnforcer({
      energyMinimization: this.options.energyMinimization,
      selfSimilarityPreservation: this.options.selfSimilarityPreservation,
      logOptimization: this.options.logOptimization
    });

    // Third Law - Cross-Domain Harmony
    this.thirdLaw = new DomainTranslator({
      domains: this.options.domains,
      preserveResonance: this.options.preserveResonance,
      logTranslations: this.options.logTranslations,
      strictMode: this.options.strictMode
    });

    // Forward events from components that support events
    if (typeof this.firstLaw.on === 'function') {
      this.firstLaw.on('validation', (data) => {
        this.emit('first-law-validation', data);
      });
    }

    if (typeof this.secondLaw.on === 'function') {
      this.secondLaw.on('state-optimized', (data) => {
        this.emit('second-law-optimization', data);
      });
    }

    if (typeof this.thirdLaw.on === 'function') {
      this.thirdLaw.on('value-translated', (data) => {
        this.emit('third-law-translation', data);
      });
    }
  }

  /**
   * Apply the Comphyological Trinity to govern a state
   * @param {number|Object} state - State to govern
   * @param {Object} options - Governance options
   * @returns {number|Object} - Governed state
   */
  govern(state, options = {}) {
    this.metrics.operations++;
    let governedState = state;

    // Create governance context
    const context = {
      timestamp: Date.now(),
      ...options
    };

    // Track which laws were applied
    const lawsApplied = {
      firstLaw: false,
      secondLaw: false,
      thirdLaw: false
    };

    // Apply First Law - Boundary Condition
    if (this.options.enforceFirstLaw) {
      try {
        const validationResult = this._applyFirstLaw(governedState, context);
        governedState = validationResult.value;
        lawsApplied.firstLaw = true;
      } catch (error) {
        this.metrics.firstLawViolations++;

        if (this.options.logGovernance) {
          console.error('First Law violation:', error.message);
        }

        throw new Error(`First Law Violation: ${error.message}`);
      }
    }

    // Apply Second Law - Internal Coherence
    if (this.options.enforceSecondLaw) {
      governedState = this._applySecondLaw(governedState, context);
      lawsApplied.secondLaw = true;
    }

    // Apply Third Law - Cross-Domain Harmony
    if (this.options.enforceThirdLaw && options.sourceDomain && options.targetDomain) {
      const translationResult = this._applyThirdLaw(
        governedState,
        options.sourceDomain,
        options.targetDomain,
        context
      );

      governedState = translationResult.value;
      lawsApplied.thirdLaw = true;
    }

    // Check if all three laws were applied
    if (lawsApplied.firstLaw && lawsApplied.secondLaw && lawsApplied.thirdLaw) {
      this.metrics.fullTrinityApplications++;
    }

    // Create governance result
    const governanceResult = {
      originalState: state,
      governedState,
      lawsApplied,
      context
    };

    // Emit governance event
    this.emit('state-governed', governanceResult);

    // Log governance if enabled
    if (this.options.logGovernance) {
      console.log(`State governed: ${JSON.stringify(state)} -> ${JSON.stringify(governedState)}`);
      console.log(`Laws applied: ${Object.entries(lawsApplied).filter(([_, applied]) => applied).map(([law]) => law).join(', ')}`);
    }

    return governedState;
  }

  /**
   * Apply the First Law - Boundary Condition
   * @param {number|Object} state - State to validate
   * @param {Object} context - Governance context
   * @returns {Object} - Validation result
   * @private
   */
  _applyFirstLaw(state, context) {
    this.metrics.firstLawEnforcements++;

    // Handle different types of state
    if (typeof state === 'object' && state !== null) {
      // Create a copy of the state to avoid modifying the original
      const validatedState = { ...state };

      // Validate each numeric property
      for (const key in validatedState) {
        if (typeof validatedState[key] === 'number') {
          const validationResult = this.firstLaw.validate(validatedState[key]);

          if (!validationResult.isValid) {
            this.metrics.firstLawViolations++;
            throw new Error(`Property ${key} with value ${validatedState[key]} violates the First Law`);
          }

          if (validationResult.wasHarmonized) {
            validatedState[key] = validationResult.harmonizedValue;
          }
        } else if (typeof validatedState[key] === 'object' && validatedState[key] !== null) {
          validatedState[key] = this._applyFirstLaw(validatedState[key], {
            ...context,
            property: key
          }).value;
        }
      }

      return { value: validatedState, isValid: true };
    } else if (typeof state === 'number') {
      const validationResult = this.firstLaw.validate(state);

      if (!validationResult.isValid) {
        this.metrics.firstLawViolations++;
        throw new Error(`Value ${state} violates the First Law`);
      }

      return {
        value: validationResult.wasHarmonized ? validationResult.harmonizedValue : state,
        isValid: true
      };
    } else {
      throw new Error(`Unsupported state type: ${typeof state}`);
    }
  }

  /**
   * Apply the Second Law - Internal Coherence
   * @param {number|Object} state - State to optimize
   * @param {Object} context - Governance context
   * @returns {number|Object} - Optimized state
   * @private
   */
  _applySecondLaw(state, context) {
    this.metrics.secondLawOptimizations++;
    return this.secondLaw.optimize(state, context);
  }

  /**
   * Apply the Third Law - Cross-Domain Harmony
   * @param {number|Object} state - State to translate
   * @param {string} sourceDomain - Source domain
   * @param {string} targetDomain - Target domain
   * @param {Object} context - Governance context
   * @returns {Object} - Translation result
   * @private
   */
  _applyThirdLaw(state, sourceDomain, targetDomain, context) {
    this.metrics.thirdLawTranslations++;
    return this.thirdLaw.map(state, sourceDomain, targetDomain, context);
  }

  /**
   * Get metrics from all three laws
   * @returns {Object} - Combined metrics
   */
  getMetrics() {
    return {
      trinity: { ...this.metrics },
      firstLaw: this.firstLaw ? {
        isResonant: this.firstLaw.isResonant.bind(this.firstLaw),
        validate: this.firstLaw.validate.bind(this.firstLaw)
      } : null,
      secondLaw: this.secondLaw ? this.secondLaw.getMetrics() : null,
      thirdLaw: this.thirdLaw ? this.thirdLaw.getMetrics() : null
    };
  }

  /**
   * Reset metrics for all three laws
   */
  resetMetrics() {
    this.metrics = {
      operations: 0,
      firstLawEnforcements: 0,
      firstLawViolations: 0,
      secondLawOptimizations: 0,
      thirdLawTranslations: 0,
      fullTrinityApplications: 0
    };

    if (this.secondLaw) {
      this.secondLaw.resetMetrics();
    }

    if (this.thirdLaw) {
      this.thirdLaw.resetMetrics();
    }
  }
}

module.exports = ComphyologicalTrinity;
