{"metadata": {"name": "AWS SigV4 Connector", "version": "1.0.0", "category": "Test", "description": "Test connector for AWS SigV4 authentication", "author": "NovaGRC", "tags": ["test", "aws", "sigv4"]}, "authentication": {"type": "AWS_SIG_V4", "fields": {"accessKeyId": {"type": "string", "description": "AWS Access Key ID", "required": true}, "secretAccessKey": {"type": "string", "description": "AWS Secret Access Key", "required": true, "sensitive": true}, "region": {"type": "string", "description": "AWS Region", "required": true, "default": "us-east-1"}, "sessionToken": {"type": "string", "description": "AWS Session Token", "required": false, "sensitive": true}}, "testConnection": {"endpoint": "/health", "method": "GET", "expectedResponse": {"status": 200}}}, "configuration": {"baseUrl": "http://localhost:3005", "headers": {"Content-Type": "application/json"}, "timeout": 30000, "retryPolicy": {"maxRetries": 3, "backoffStrategy": "exponential"}}, "endpoints": [{"id": "getResource", "name": "Get Resource", "path": "/aws/resource", "method": "GET", "parameters": {"query": {}, "path": {}, "body": {}}, "response": {"successCode": 200}}], "mappings": [{"sourceEndpoint": "getResource", "targetSystem": "NovaGRC", "targetEntity": "Resource", "transformations": [{"source": "$.data.id", "target": "resourceId", "transform": "identity"}, {"source": "$.data.name", "target": "resourceName", "transform": "identity"}]}], "events": {"webhooks": [], "polling": []}}