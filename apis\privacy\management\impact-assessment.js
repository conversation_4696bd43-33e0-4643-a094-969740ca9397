/**
 * Automated Privacy Impact Assessment functionality
 */

/**
 * Risk levels
 */
const RISK_LEVELS = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high'
};

/**
 * Risk categories
 */
const RISK_CATEGORIES = {
  IDENTIFICATION: 'identification',
  ACCESS_CONTROL: 'access_control',
  DATA_QUALITY: 'data_quality',
  DATA_MINIMIZATION: 'data_minimization',
  PURPOSE_LIMITATION: 'purpose_limitation',
  TRANSPARENCY: 'transparency',
  DATA_SUBJECT_RIGHTS: 'data_subject_rights',
  SECURITY: 'security',
  INTERNATIONAL_TRANSFERS: 'international_transfers',
  SPECIAL_CATEGORIES: 'special_categories'
};

/**
 * Risk assessment criteria
 */
const ASSESSMENT_CRITERIA = [
  {
    id: 'data_volume',
    category: RISK_CATEGORIES.DATA_MINIMIZATION,
    question: 'What is the volume of personal data being processed?',
    options: [
      { value: 'small', label: 'Small (fewer than 1,000 data subjects)', score: 1 },
      { value: 'medium', label: 'Medium (1,000 to 10,000 data subjects)', score: 2 },
      { value: 'large', label: 'Large (10,000 to 100,000 data subjects)', score: 3 },
      { value: 'very_large', label: 'Very large (more than 100,000 data subjects)', score: 4 }
    ]
  },
  {
    id: 'data_sensitivity',
    category: RISK_CATEGORIES.SPECIAL_CATEGORIES,
    question: 'Does the processing involve special category or sensitive data?',
    options: [
      { value: 'none', label: 'No special category data', score: 1 },
      { value: 'limited', label: 'Limited special category data', score: 2 },
      { value: 'significant', label: 'Significant special category data', score: 3 },
      { value: 'extensive', label: 'Extensive special category data', score: 4 }
    ]
  },
  {
    id: 'data_subjects',
    category: RISK_CATEGORIES.IDENTIFICATION,
    question: 'Who are the data subjects?',
    options: [
      { value: 'employees', label: 'Employees only', score: 1 },
      { value: 'adult_customers', label: 'Adult customers/users', score: 2 },
      { value: 'vulnerable_adults', label: 'Vulnerable adults', score: 3 },
      { value: 'children', label: 'Children or other vulnerable groups', score: 4 }
    ]
  },
  {
    id: 'processing_purpose',
    category: RISK_CATEGORIES.PURPOSE_LIMITATION,
    question: 'What is the purpose of the processing?',
    options: [
      { value: 'contractual', label: 'Contractual necessity', score: 1 },
      { value: 'legal_obligation', label: 'Legal obligation', score: 1 },
      { value: 'legitimate_interest', label: 'Legitimate interest', score: 2 },
      { value: 'consent', label: 'Consent-based processing', score: 3 }
    ]
  },
  {
    id: 'automated_decision_making',
    category: RISK_CATEGORIES.DATA_SUBJECT_RIGHTS,
    question: 'Does the processing involve automated decision-making with legal or similar significant effects?',
    options: [
      { value: 'none', label: 'No automated decision-making', score: 1 },
      { value: 'limited', label: 'Limited automated decision-making with human oversight', score: 2 },
      { value: 'significant', label: 'Significant automated decision-making with human oversight', score: 3 },
      { value: 'extensive', label: 'Extensive automated decision-making with little or no human oversight', score: 4 }
    ]
  },
  {
    id: 'data_retention',
    category: RISK_CATEGORIES.DATA_MINIMIZATION,
    question: 'What is the data retention period?',
    options: [
      { value: 'short', label: 'Short-term (less than 1 year)', score: 1 },
      { value: 'medium', label: 'Medium-term (1-3 years)', score: 2 },
      { value: 'long', label: 'Long-term (3-7 years)', score: 3 },
      { value: 'indefinite', label: 'Indefinite or very long-term (more than 7 years)', score: 4 }
    ]
  },
  {
    id: 'security_measures',
    category: RISK_CATEGORIES.SECURITY,
    question: 'What security measures are in place?',
    options: [
      { value: 'comprehensive', label: 'Comprehensive security measures', score: 1 },
      { value: 'standard', label: 'Standard security measures', score: 2 },
      { value: 'basic', label: 'Basic security measures', score: 3 },
      { value: 'minimal', label: 'Minimal or unknown security measures', score: 4 }
    ]
  },
  {
    id: 'cross_border_transfers',
    category: RISK_CATEGORIES.INTERNATIONAL_TRANSFERS,
    question: 'Are there any cross-border transfers of data?',
    options: [
      { value: 'none', label: 'No cross-border transfers', score: 1 },
      { value: 'adequate', label: 'Transfers to countries with adequacy decisions', score: 2 },
      { value: 'safeguards', label: 'Transfers with appropriate safeguards', score: 3 },
      { value: 'no_safeguards', label: 'Transfers without adequate safeguards', score: 4 }
    ]
  },
  {
    id: 'transparency',
    category: RISK_CATEGORIES.TRANSPARENCY,
    question: 'How transparent is the processing to data subjects?',
    options: [
      { value: 'fully_transparent', label: 'Fully transparent with clear privacy notices', score: 1 },
      { value: 'mostly_transparent', label: 'Mostly transparent with some limitations', score: 2 },
      { value: 'limited_transparency', label: 'Limited transparency', score: 3 },
      { value: 'not_transparent', label: 'Not transparent or hidden processing', score: 4 }
    ]
  },
  {
    id: 'data_subject_rights',
    category: RISK_CATEGORIES.DATA_SUBJECT_RIGHTS,
    question: 'How are data subject rights facilitated?',
    options: [
      { value: 'comprehensive', label: 'Comprehensive processes for all rights', score: 1 },
      { value: 'standard', label: 'Standard processes for most rights', score: 2 },
      { value: 'limited', label: 'Limited processes for some rights', score: 3 },
      { value: 'minimal', label: 'Minimal or no processes for data subject rights', score: 4 }
    ]
  }
];

/**
 * Generate a privacy impact assessment for a data processing activity
 * @param {Object} activity - The data processing activity
 * @returns {Object} - The privacy impact assessment
 */
const generateImpactAssessment = (activity) => {
  // Initialize assessment
  const assessment = {
    activityId: activity.id,
    activityName: activity.name,
    assessmentDate: new Date().toISOString(),
    criteria: [],
    overallRiskScore: 0,
    maxPossibleScore: ASSESSMENT_CRITERIA.length * 4, // Maximum possible score
    riskLevel: RISK_LEVELS.LOW,
    risksByCategory: {},
    recommendations: []
  };

  // Initialize risk categories
  Object.values(RISK_CATEGORIES).forEach(category => {
    assessment.risksByCategory[category] = {
      score: 0,
      count: 0,
      averageScore: 0,
      level: RISK_LEVELS.LOW
    };
  });

  // Assess each criterion
  ASSESSMENT_CRITERIA.forEach(criterion => {
    // Default to highest risk if we can't determine
    let selectedOption = criterion.options[criterion.options.length - 1];
    
    // Try to determine the appropriate option based on the activity
    switch (criterion.id) {
      case 'data_volume':
        // Estimate based on approximate subjects count if available
        if (activity.approximateSubjectsCount !== undefined) {
          if (activity.approximateSubjectsCount < 1000) {
            selectedOption = criterion.options[0]; // small
          } else if (activity.approximateSubjectsCount < 10000) {
            selectedOption = criterion.options[1]; // medium
          } else if (activity.approximateSubjectsCount < 100000) {
            selectedOption = criterion.options[2]; // large
          } else {
            selectedOption = criterion.options[3]; // very large
          }
        }
        break;
        
      case 'data_sensitivity':
        // Check for special category data
        if (activity.dataCategories) {
          const sensitiveDataTypes = [
            'health', 'biometric', 'genetic', 'racial', 'ethnic', 
            'political', 'religious', 'philosophical', 'trade union', 
            'sexual', 'criminal'
          ];
          
          const sensitiveCategories = activity.dataCategories.filter(category => 
            sensitiveDataTypes.some(type => category.toLowerCase().includes(type))
          );
          
          if (sensitiveCategories.length === 0) {
            selectedOption = criterion.options[0]; // none
          } else if (sensitiveCategories.length === 1) {
            selectedOption = criterion.options[1]; // limited
          } else if (sensitiveCategories.length < 3) {
            selectedOption = criterion.options[2]; // significant
          } else {
            selectedOption = criterion.options[3]; // extensive
          }
        }
        break;
        
      case 'data_subjects':
        // Determine data subject types
        if (activity.dataSubjects) {
          if (activity.dataSubjects.some(subject => 
            subject.toLowerCase().includes('child') || 
            subject.toLowerCase().includes('minor')
          )) {
            selectedOption = criterion.options[3]; // children
          } else if (activity.dataSubjects.some(subject => 
            subject.toLowerCase().includes('vulnerable')
          )) {
            selectedOption = criterion.options[2]; // vulnerable adults
          } else if (activity.dataSubjects.some(subject => 
            subject.toLowerCase().includes('customer') || 
            subject.toLowerCase().includes('user') || 
            subject.toLowerCase().includes('client')
          )) {
            selectedOption = criterion.options[1]; // adult customers
          } else if (activity.dataSubjects.some(subject => 
            subject.toLowerCase().includes('employee') || 
            subject.toLowerCase().includes('staff')
          )) {
            selectedOption = criterion.options[0]; // employees
          }
        }
        break;
        
      case 'processing_purpose':
        // Determine based on legal basis
        if (activity.legalBasis) {
          if (activity.legalBasis === 'consent') {
            selectedOption = criterion.options[3]; // consent
          } else if (activity.legalBasis === 'legitimate-interests') {
            selectedOption = criterion.options[2]; // legitimate interest
          } else if (activity.legalBasis === 'legal-obligation') {
            selectedOption = criterion.options[1]; // legal obligation
          } else if (activity.legalBasis === 'contract') {
            selectedOption = criterion.options[0]; // contractual
          }
        }
        break;
        
      case 'automated_decision_making':
        // Default to none unless specified
        selectedOption = criterion.options[0]; // none
        break;
        
      case 'data_retention':
        // Determine based on retention period
        if (activity.retentionPeriod) {
          const retentionPeriod = activity.retentionPeriod.toLowerCase();
          
          if (retentionPeriod.includes('indefinite') || 
              retentionPeriod.includes('permanent') ||
              retentionPeriod.includes('7 year') ||
              retentionPeriod.includes('10 year')) {
            selectedOption = criterion.options[3]; // indefinite
          } else if (retentionPeriod.includes('5 year') ||
                     retentionPeriod.includes('6 year')) {
            selectedOption = criterion.options[2]; // long
          } else if (retentionPeriod.includes('1 year') ||
                     retentionPeriod.includes('2 year') ||
                     retentionPeriod.includes('3 year')) {
            selectedOption = criterion.options[1]; // medium
          } else if (retentionPeriod.includes('month') ||
                     retentionPeriod.includes('day') ||
                     retentionPeriod.includes('week')) {
            selectedOption = criterion.options[0]; // short
          }
        }
        break;
        
      case 'security_measures':
        // Determine based on security measures
        if (activity.securityMeasures && activity.securityMeasures.length > 0) {
          const strongMeasures = [
            'encryption', 'access control', 'authentication', 'audit', 
            'monitoring', 'backup', 'disaster recovery'
          ];
          
          const implementedStrongMeasures = activity.securityMeasures.filter(measure => 
            strongMeasures.some(strong => measure.toLowerCase().includes(strong))
          );
          
          if (implementedStrongMeasures.length >= 5) {
            selectedOption = criterion.options[0]; // comprehensive
          } else if (implementedStrongMeasures.length >= 3) {
            selectedOption = criterion.options[1]; // standard
          } else if (implementedStrongMeasures.length >= 1) {
            selectedOption = criterion.options[2]; // basic
          } else {
            selectedOption = criterion.options[3]; // minimal
          }
        }
        break;
        
      case 'cross_border_transfers':
        // Determine based on cross-border transfers
        if (activity.crossBorderTransfers) {
          if (activity.crossBorderTransfers.length === 0) {
            selectedOption = criterion.options[0]; // none
          } else {
            const adequacyTransfers = activity.crossBorderTransfers.filter(transfer => 
              transfer.adequacyDecision === true
            );
            
            const safeguardTransfers = activity.crossBorderTransfers.filter(transfer => 
              transfer.adequacyDecision === false && transfer.mechanism
            );
            
            const noSafeguardTransfers = activity.crossBorderTransfers.filter(transfer => 
              transfer.adequacyDecision === false && !transfer.mechanism
            );
            
            if (noSafeguardTransfers.length > 0) {
              selectedOption = criterion.options[3]; // no safeguards
            } else if (safeguardTransfers.length > 0) {
              selectedOption = criterion.options[2]; // safeguards
            } else if (adequacyTransfers.length > 0) {
              selectedOption = criterion.options[1]; // adequate
            }
          }
        }
        break;
        
      case 'transparency':
        // Default to mostly transparent unless we have more info
        selectedOption = criterion.options[1]; // mostly transparent
        break;
        
      case 'data_subject_rights':
        // Default to standard unless we have more info
        selectedOption = criterion.options[1]; // standard
        break;
    }
    
    // Add the criterion assessment
    assessment.criteria.push({
      id: criterion.id,
      category: criterion.category,
      question: criterion.question,
      selectedOption: selectedOption.value,
      score: selectedOption.score
    });
    
    // Update overall score
    assessment.overallRiskScore += selectedOption.score;
    
    // Update category score
    assessment.risksByCategory[criterion.category].score += selectedOption.score;
    assessment.risksByCategory[criterion.category].count += 1;
  });
  
  // Calculate average scores and risk levels for each category
  Object.keys(assessment.risksByCategory).forEach(category => {
    const categoryData = assessment.risksByCategory[category];
    
    if (categoryData.count > 0) {
      categoryData.averageScore = categoryData.score / categoryData.count;
      
      // Determine risk level
      if (categoryData.averageScore <= 1.5) {
        categoryData.level = RISK_LEVELS.LOW;
      } else if (categoryData.averageScore <= 2.5) {
        categoryData.level = RISK_LEVELS.MEDIUM;
      } else {
        categoryData.level = RISK_LEVELS.HIGH;
      }
    }
  });
  
  // Determine overall risk level
  const normalizedScore = assessment.overallRiskScore / assessment.maxPossibleScore;
  
  if (normalizedScore <= 0.4) {
    assessment.riskLevel = RISK_LEVELS.LOW;
  } else if (normalizedScore <= 0.7) {
    assessment.riskLevel = RISK_LEVELS.MEDIUM;
  } else {
    assessment.riskLevel = RISK_LEVELS.HIGH;
  }
  
  // Generate recommendations based on high-risk areas
  Object.keys(assessment.risksByCategory).forEach(category => {
    const categoryData = assessment.risksByCategory[category];
    
    if (categoryData.level === RISK_LEVELS.HIGH) {
      switch (category) {
        case RISK_CATEGORIES.IDENTIFICATION:
          assessment.recommendations.push({
            category,
            recommendation: 'Implement additional safeguards for data subject identification, such as pseudonymization or anonymization where possible.'
          });
          break;
          
        case RISK_CATEGORIES.ACCESS_CONTROL:
          assessment.recommendations.push({
            category,
            recommendation: 'Strengthen access controls with role-based access, multi-factor authentication, and regular access reviews.'
          });
          break;
          
        case RISK_CATEGORIES.DATA_QUALITY:
          assessment.recommendations.push({
            category,
            recommendation: 'Implement data quality checks and validation procedures to ensure accuracy and completeness of personal data.'
          });
          break;
          
        case RISK_CATEGORIES.DATA_MINIMIZATION:
          assessment.recommendations.push({
            category,
            recommendation: 'Review data collection practices to ensure only necessary data is collected and implement shorter retention periods.'
          });
          break;
          
        case RISK_CATEGORIES.PURPOSE_LIMITATION:
          assessment.recommendations.push({
            category,
            recommendation: 'Clearly define and document processing purposes and implement controls to prevent processing for incompatible purposes.'
          });
          break;
          
        case RISK_CATEGORIES.TRANSPARENCY:
          assessment.recommendations.push({
            category,
            recommendation: 'Enhance privacy notices and communications to data subjects to provide clear, concise information about processing activities.'
          });
          break;
          
        case RISK_CATEGORIES.DATA_SUBJECT_RIGHTS:
          assessment.recommendations.push({
            category,
            recommendation: 'Implement robust processes for handling data subject rights requests, including access, rectification, erasure, and objection.'
          });
          break;
          
        case RISK_CATEGORIES.SECURITY:
          assessment.recommendations.push({
            category,
            recommendation: 'Strengthen security measures with encryption, access controls, regular security assessments, and incident response procedures.'
          });
          break;
          
        case RISK_CATEGORIES.INTERNATIONAL_TRANSFERS:
          assessment.recommendations.push({
            category,
            recommendation: 'Implement appropriate safeguards for international data transfers, such as standard contractual clauses or binding corporate rules.'
          });
          break;
          
        case RISK_CATEGORIES.SPECIAL_CATEGORIES:
          assessment.recommendations.push({
            category,
            recommendation: 'Implement additional safeguards for special category data, including enhanced security measures and explicit consent mechanisms.'
          });
          break;
      }
    } else if (categoryData.level === RISK_LEVELS.MEDIUM) {
      // Add recommendations for medium-risk areas
      switch (category) {
        case RISK_CATEGORIES.DATA_MINIMIZATION:
          assessment.recommendations.push({
            category,
            recommendation: 'Review data retention periods and implement automated deletion processes for data that is no longer needed.'
          });
          break;
          
        case RISK_CATEGORIES.SECURITY:
          assessment.recommendations.push({
            category,
            recommendation: 'Conduct regular security assessments and implement additional security controls based on the findings.'
          });
          break;
          
        case RISK_CATEGORIES.TRANSPARENCY:
          assessment.recommendations.push({
            category,
            recommendation: 'Review and update privacy notices to ensure they are clear, concise, and easily accessible to data subjects.'
          });
          break;
      }
    }
  });
  
  return assessment;
};

module.exports = {
  RISK_LEVELS,
  RISK_CATEGORIES,
  ASSESSMENT_CRITERIA,
  generateImpactAssessment
};
