/**
 * Credential Service
 * 
 * This service handles operations related to credentials.
 * In a production environment, credentials should be stored securely,
 * possibly using a vault service or encrypted storage.
 */

const fs = require('fs').promises;
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const crypto = require('crypto');
const { ValidationError } = require('../utils/errors');

class CredentialService {
  constructor(dataDir = path.join(__dirname, '../data')) {
    this.dataDir = dataDir;
    this.credentialsFile = path.join(this.dataDir, 'credentials.json');
    this.encryptionKey = process.env.ENCRYPTION_KEY || 'default-encryption-key-for-development-only';
    this.ensureDataDir();
  }

  /**
   * Ensure the data directory exists
   */
  async ensureDataDir() {
    try {
      await fs.mkdir(this.dataDir, { recursive: true });
    } catch (error) {
      console.error('Error creating data directory:', error);
      throw error;
    }
  }

  /**
   * Load credentials from file
   */
  async loadCredentials() {
    try {
      const data = await fs.readFile(this.credentialsFile, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      if (error.code === 'ENOENT') {
        // File doesn't exist, return empty array
        return [];
      }
      console.error('Error loading credentials:', error);
      throw error;
    }
  }

  /**
   * Save credentials to file
   */
  async saveCredentials(credentials) {
    try {
      await fs.writeFile(this.credentialsFile, JSON.stringify(credentials, null, 2));
    } catch (error) {
      console.error('Error saving credentials:', error);
      throw error;
    }
  }

  /**
   * Encrypt sensitive data
   */
  encrypt(text) {
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipheriv('aes-256-gcm', Buffer.from(this.encryptionKey), iv);
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    const authTag = cipher.getAuthTag().toString('hex');
    return {
      iv: iv.toString('hex'),
      encrypted,
      authTag
    };
  }

  /**
   * Decrypt sensitive data
   */
  decrypt(encryptedData) {
    const decipher = crypto.createDecipheriv(
      'aes-256-gcm',
      Buffer.from(this.encryptionKey),
      Buffer.from(encryptedData.iv, 'hex')
    );
    decipher.setAuthTag(Buffer.from(encryptedData.authTag, 'hex'));
    let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    return decrypted;
  }

  /**
   * Get all credentials
   */
  async getAllCredentials() {
    const credentials = await this.loadCredentials();
    // Return credentials without sensitive data
    return credentials.map(cred => ({
      id: cred.id,
      name: cred.name,
      type: cred.type,
      connectorId: cred.connectorId,
      created: cred.created,
      lastUsed: cred.lastUsed
    }));
  }

  /**
   * Get credential by ID
   */
  async getCredentialById(id) {
    const credentials = await this.loadCredentials();
    const credential = credentials.find(c => c.id === id);
    
    if (!credential) {
      throw new Error(`Credential with ID ${id} not found`);
    }
    
    // Decrypt sensitive data
    const decryptedCredential = { ...credential };
    
    if (credential.apiKey && credential.apiKey.encrypted) {
      decryptedCredential.apiKey = this.decrypt(credential.apiKey);
    }
    
    if (credential.username && credential.username.encrypted) {
      decryptedCredential.username = this.decrypt(credential.username);
    }
    
    if (credential.password && credential.password.encrypted) {
      decryptedCredential.password = this.decrypt(credential.password);
    }
    
    if (credential.token && credential.token.encrypted) {
      decryptedCredential.token = this.decrypt(credential.token);
    }
    
    if (credential.clientId && credential.clientId.encrypted) {
      decryptedCredential.clientId = this.decrypt(credential.clientId);
    }
    
    if (credential.clientSecret && credential.clientSecret.encrypted) {
      decryptedCredential.clientSecret = this.decrypt(credential.clientSecret);
    }
    
    return decryptedCredential;
  }

  /**
   * Create a new credential
   */
  async createCredential(credentialData) {
    if (!credentialData.name) {
      throw new ValidationError('Credential name is required');
    }
    
    if (!credentialData.type) {
      throw new ValidationError('Credential type is required');
    }
    
    if (!credentialData.connectorId) {
      throw new ValidationError('Connector ID is required');
    }
    
    const credentials = await this.loadCredentials();
    
    // Encrypt sensitive data
    const encryptedCredential = { ...credentialData };
    
    if (credentialData.apiKey) {
      encryptedCredential.apiKey = this.encrypt(credentialData.apiKey);
    }
    
    if (credentialData.username) {
      encryptedCredential.username = this.encrypt(credentialData.username);
    }
    
    if (credentialData.password) {
      encryptedCredential.password = this.encrypt(credentialData.password);
    }
    
    if (credentialData.token) {
      encryptedCredential.token = this.encrypt(credentialData.token);
    }
    
    if (credentialData.clientId) {
      encryptedCredential.clientId = this.encrypt(credentialData.clientId);
    }
    
    if (credentialData.clientSecret) {
      encryptedCredential.clientSecret = this.encrypt(credentialData.clientSecret);
    }
    
    const newCredential = {
      id: uuidv4(),
      ...encryptedCredential,
      created: new Date().toISOString(),
      lastUsed: null
    };
    
    credentials.push(newCredential);
    await this.saveCredentials(credentials);
    
    // Return credential without sensitive data
    return {
      id: newCredential.id,
      name: newCredential.name,
      type: newCredential.type,
      connectorId: newCredential.connectorId,
      created: newCredential.created,
      lastUsed: newCredential.lastUsed
    };
  }

  /**
   * Update an existing credential
   */
  async updateCredential(id, credentialData) {
    const credentials = await this.loadCredentials();
    const index = credentials.findIndex(c => c.id === id);
    
    if (index === -1) {
      throw new Error(`Credential with ID ${id} not found`);
    }
    
    // Encrypt sensitive data
    const encryptedCredential = { ...credentialData };
    
    if (credentialData.apiKey) {
      encryptedCredential.apiKey = this.encrypt(credentialData.apiKey);
    }
    
    if (credentialData.username) {
      encryptedCredential.username = this.encrypt(credentialData.username);
    }
    
    if (credentialData.password) {
      encryptedCredential.password = this.encrypt(credentialData.password);
    }
    
    if (credentialData.token) {
      encryptedCredential.token = this.encrypt(credentialData.token);
    }
    
    if (credentialData.clientId) {
      encryptedCredential.clientId = this.encrypt(credentialData.clientId);
    }
    
    if (credentialData.clientSecret) {
      encryptedCredential.clientSecret = this.encrypt(credentialData.clientSecret);
    }
    
    const updatedCredential = {
      ...credentials[index],
      ...encryptedCredential,
      updated: new Date().toISOString()
    };
    
    credentials[index] = updatedCredential;
    await this.saveCredentials(credentials);
    
    // Return credential without sensitive data
    return {
      id: updatedCredential.id,
      name: updatedCredential.name,
      type: updatedCredential.type,
      connectorId: updatedCredential.connectorId,
      created: updatedCredential.created,
      updated: updatedCredential.updated,
      lastUsed: updatedCredential.lastUsed
    };
  }

  /**
   * Delete a credential
   */
  async deleteCredential(id) {
    const credentials = await this.loadCredentials();
    const index = credentials.findIndex(c => c.id === id);
    
    if (index === -1) {
      throw new Error(`Credential with ID ${id} not found`);
    }
    
    credentials.splice(index, 1);
    await this.saveCredentials(credentials);
    
    return { success: true, message: `Credential with ID ${id} deleted` };
  }

  /**
   * Update last used timestamp
   */
  async updateLastUsed(id) {
    const credentials = await this.loadCredentials();
    const index = credentials.findIndex(c => c.id === id);
    
    if (index === -1) {
      throw new Error(`Credential with ID ${id} not found`);
    }
    
    credentials[index].lastUsed = new Date().toISOString();
    await this.saveCredentials(credentials);
  }
}

module.exports = CredentialService;
