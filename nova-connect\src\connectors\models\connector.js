/**
 * NovaFuse Universal API Connector - Connector Model
 * 
 * This module defines the basic connector model.
 */

const { v4: uuidv4 } = require('uuid');

/**
 * Connector status enum
 */
const ConnectorStatus = {
  DRAFT: 'draft',
  TESTING: 'testing',
  PUBLISHED: 'published',
  DEPRECATED: 'deprecated',
  RETIRED: 'retired'
};

/**
 * Connector type enum
 */
const ConnectorType = {
  SOURCE: 'source',
  DESTINATION: 'destination',
  TRANSFORMATION: 'transformation'
};

/**
 * Connector model class
 */
class Connector {
  /**
   * Create a new connector
   * 
   * @param {Object} data - The connector data
   */
  constructor(data = {}) {
    this.id = data.id || uuidv4();
    this.name = data.name || '';
    this.description = data.description || '';
    this.version = data.version || '1.0.0';
    this.type = data.type || ConnectorType.SOURCE;
    this.status = data.status || ConnectorStatus.DRAFT;
    this.createdAt = data.createdAt || new Date().toISOString();
    this.updatedAt = data.updatedAt || new Date().toISOString();
    this.configSchema = data.configSchema || { type: 'object', properties: {} };
  }

  /**
   * Validate the connector
   * 
   * @returns {Object} - Validation result
   */
  validate() {
    const errors = [];

    // Required fields
    if (!this.name) {
      errors.push('Name is required');
    }

    if (!this.description) {
      errors.push('Description is required');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Convert to JSON
   * 
   * @returns {Object} - The JSON representation
   */
  toJSON() {
    return {
      id: this.id,
      name: this.name,
      description: this.description,
      version: this.version,
      type: this.type,
      status: this.status,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
      configSchema: this.configSchema
    };
  }
}

module.exports = {
  Connector,
  ConnectorStatus,
  ConnectorType
};
