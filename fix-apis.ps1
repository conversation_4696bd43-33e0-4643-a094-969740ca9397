# Fix NovaFuse API Superstore APIs

Write-Host "Fixing NovaFuse API Superstore APIs..." -ForegroundColor Cyan
Write-Host "============================================================" -ForegroundColor Cyan
Write-Host ""

# Stop the containers
Write-Host "Stopping containers..." -ForegroundColor Yellow
& D:\novafuse-api-superstore\stop.bat
Start-Sleep -Seconds 5

# Fix the Governance API
Write-Host "Fixing Governance API..." -ForegroundColor Yellow
$governanceApiJs = @'
const express = require('express');
const app = express();
const port = 3000;

// Sample data
const boardMeetings = [
  {
    id: 'bm-001',
    title: 'Q1 Board Meeting',
    date: '2025-01-15',
    status: 'scheduled'
  },
  {
    id: 'bm-002',
    title: 'Q2 Board Meeting',
    date: '2025-04-15',
    status: 'scheduled'
  },
  {
    id: 'bm-003',
    title: 'Emergency Board Meeting',
    date: '2024-12-10',
    status: 'completed'
  }
];

// Routes
app.get('/health', (req, res) => {
  res.json({ status: 'healthy' });
});

// Governance routes
app.get('/governance/board/meetings', (req, res) => {
  res.json({
    data: boardMeetings,
    total: boardMeetings.length
  });
});

// Original routes
app.get('/board/meetings', (req, res) => {
  res.json({
    data: boardMeetings,
    total: boardMeetings.length
  });
});

app.listen(port, () => {
  console.log(`Governance API running on port ${port}`);
});
'@

# Fix the Security API
Write-Host "Fixing Security API..." -ForegroundColor Yellow
$securityApiJs = @'
const express = require('express');
const app = express();
const port = 3000;

// Sample data
const vulnerabilities = [
  {
    id: 'vuln-001',
    title: 'SQL Injection in Login Form',
    severity: 'critical',
    status: 'open'
  },
  {
    id: 'vuln-002',
    title: 'Outdated SSL Certificate',
    severity: 'medium',
    status: 'in_progress'
  },
  {
    id: 'vuln-003',
    title: 'Weak Password Policy',
    severity: 'high',
    status: 'open'
  }
];

// Routes
app.get('/health', (req, res) => {
  res.json({ status: 'healthy' });
});

// Security routes
app.get('/security/vulnerabilities', (req, res) => {
  res.json({
    data: vulnerabilities,
    total: vulnerabilities.length
  });
});

// Original routes
app.get('/vulnerabilities', (req, res) => {
  res.json({
    data: vulnerabilities,
    total: vulnerabilities.length
  });
});

app.listen(port, () => {
  console.log(`Security API running on port ${port}`);
});
'@

# Fix the APIs API
Write-Host "Fixing APIs API..." -ForegroundColor Yellow
$apisApiJs = @'
const express = require('express');
const app = express();
const port = 3000;

// Sample data
const apiCatalog = [
  {
    id: 'api-001',
    name: 'User Management API',
    category: 'identity',
    status: 'active'
  },
  {
    id: 'api-002',
    name: 'Compliance Reporting API',
    category: 'compliance',
    status: 'active'
  },
  {
    id: 'api-003',
    name: 'Risk Assessment API',
    category: 'risk',
    status: 'beta'
  }
];

// Routes
app.get('/health', (req, res) => {
  res.json({ status: 'healthy' });
});

// APIs routes
app.get('/apis/catalog', (req, res) => {
  res.json({
    data: apiCatalog,
    total: apiCatalog.length
  });
});

// Original routes
app.get('/catalog', (req, res) => {
  res.json({
    data: apiCatalog,
    total: apiCatalog.length
  });
});

app.listen(port, () => {
  console.log(`APIs API running on port ${port}`);
});
'@

# Start the containers
Write-Host "Starting containers..." -ForegroundColor Yellow
& D:\novafuse-api-superstore\start.bat
Start-Sleep -Seconds 30

# Update the API server.js files
Write-Host "Updating API server.js files..." -ForegroundColor Yellow
docker exec -it novafuse-governance-api sh -c "echo '$governanceApiJs' > /app/server.js"
docker exec -it novafuse-security-api sh -c "echo '$securityApiJs' > /app/server.js"
docker exec -it novafuse-apis-api sh -c "echo '$apisApiJs' > /app/server.js"

# Restart the API containers
Write-Host "Restarting API containers..." -ForegroundColor Yellow
docker restart novafuse-governance-api
docker restart novafuse-security-api
docker restart novafuse-apis-api
Start-Sleep -Seconds 10

# Fix Kong API Gateway DNS resolution issues
Write-Host "Fixing Kong API Gateway DNS resolution issues..." -ForegroundColor Yellow
$governanceApiIp = docker inspect -f '{{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}' novafuse-governance-api
$securityApiIp = docker inspect -f '{{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}' novafuse-security-api
$apisApiIp = docker inspect -f '{{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}' novafuse-apis-api

Write-Host "Governance API IP: $governanceApiIp" -ForegroundColor Green
Write-Host "Security API IP: $securityApiIp" -ForegroundColor Green
Write-Host "APIs API IP: $apisApiIp" -ForegroundColor Green

# Update Kong service configuration
Write-Host "Updating Kong service configuration..." -ForegroundColor Yellow
Invoke-RestMethod -Uri "http://localhost:8001/services/governance-service" -Method Patch -Body @{
    host = $governanceApiIp
    port = 3000
}

Invoke-RestMethod -Uri "http://localhost:8001/services/security-service" -Method Patch -Body @{
    host = $securityApiIp
    port = 3000
}

Invoke-RestMethod -Uri "http://localhost:8001/services/apis-service" -Method Patch -Body @{
    host = $apisApiIp
    port = 3000
}

# Test the APIs
Write-Host "Testing the APIs..." -ForegroundColor Yellow
& D:\novafuse-api-superstore\test-api-comprehensive.ps1

Write-Host "API fixes completed!" -ForegroundColor Cyan
Write-Host "============================================================" -ForegroundColor Cyan
