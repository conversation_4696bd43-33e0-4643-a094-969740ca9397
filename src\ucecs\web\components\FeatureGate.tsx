'use client';

import React from 'react';
import { FeatureFlag } from '@/utils/features/featureFlags';
import { useFeatureFlags } from '@/contexts/FeatureFlagContext';

interface FeatureGateProps {
  feature: FeatureFlag;
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

/**
 * FeatureGate component
 * 
 * Conditionally renders content based on whether a feature flag is enabled.
 * 
 * @example
 * <FeatureGate feature={FeatureFlag.ADVANCED_DASHBOARD}>
 *   <AdvancedDashboard />
 * </FeatureGate>
 * 
 * @example
 * <FeatureGate 
 *   feature={FeatureFlag.EXPERIMENTAL_AI_ASSISTANT}
 *   fallback={<p>AI Assistant coming soon!</p>}
 * >
 *   <AIAssistant />
 * </FeatureGate>
 */
export default function FeatureGate({ feature, children, fallback = null }: FeatureGateProps) {
  const { isEnabled } = useFeatureFlags();
  
  if (isEnabled(feature)) {
    return <>{children}</>;
  }
  
  return <>{fallback}</>;
}
