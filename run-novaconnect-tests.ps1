# NovaConnect Test Runner
# This script runs the NovaConnect tests in a Docker environment

# Set error action preference
$ErrorActionPreference = "Stop"

# Configuration
$dockerComposeFile = "docker-compose.novaconnect-test.yml"

# Function to display colored output
function Write-ColorOutput {
    param (
        [Parameter(Mandatory=$true)]
        [string]$Message,
        
        [Parameter(Mandatory=$false)]
        [string]$ForegroundColor = "White"
    )
    
    Write-Host $Message -ForegroundColor $ForegroundColor
}

# Function to check if Docker is running
function Test-DockerRunning {
    try {
        $dockerInfo = docker info 2>&1
        return $true
    } catch {
        return $false
    }
}

# Main execution
Write-ColorOutput "NovaConnect Test Runner" -ForegroundColor Cyan
Write-ColorOutput "======================" -ForegroundColor Cyan

# Check if Docker is running
if (-not (Test-DockerRunning)) {
    Write-ColorOutput "Error: Docker is not running. Please start Docker and try again." -ForegroundColor Red
    exit 1
}

# Check if docker-compose file exists
if (-not (Test-Path $dockerComposeFile)) {
    Write-ColorOutput "Error: Docker Compose file '$dockerComposeFile' not found." -ForegroundColor Red
    exit 1
}

# Start the Docker environment
Write-ColorOutput "`nStarting Docker test environment..." -ForegroundColor Yellow
try {
    docker-compose -f $dockerComposeFile up -d
    Write-ColorOutput "Docker environment started successfully." -ForegroundColor Green
} catch {
    Write-ColorOutput "Error starting Docker environment: $_" -ForegroundColor Red
    exit 1
}

# Wait for services to be ready
Write-ColorOutput "`nWaiting for services to be ready..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# Run the tests
Write-ColorOutput "`nRunning NovaConnect tests..." -ForegroundColor Yellow
try {
    docker-compose -f $dockerComposeFile run --rm test-runner
    $testResult = $LASTEXITCODE
    
    if ($testResult -eq 0) {
        Write-ColorOutput "`nAll tests passed successfully!" -ForegroundColor Green
    } else {
        Write-ColorOutput "`nSome tests failed. Check the test report for details." -ForegroundColor Red
    }
} catch {
    Write-ColorOutput "Error running tests: $_" -ForegroundColor Red
    $testResult = 1
}

# Copy test results from the container
Write-ColorOutput "`nCopying test results..." -ForegroundColor Yellow
try {
    $containerId = docker-compose -f $dockerComposeFile ps -q test-runner
    if ($containerId) {
        docker cp ${containerId}:/app/test-results ./test-results
        Write-ColorOutput "Test results copied successfully." -ForegroundColor Green
    } else {
        Write-ColorOutput "Warning: Could not find test-runner container to copy results." -ForegroundColor Yellow
    }
} catch {
    Write-ColorOutput "Error copying test results: $_" -ForegroundColor Yellow
}

# Stop the Docker environment
Write-ColorOutput "`nStopping Docker test environment..." -ForegroundColor Yellow
try {
    docker-compose -f $dockerComposeFile down
    Write-ColorOutput "Docker environment stopped successfully." -ForegroundColor Green
} catch {
    Write-ColorOutput "Error stopping Docker environment: $_" -ForegroundColor Red
}

# Display test report location
Write-ColorOutput "`nTest Summary:" -ForegroundColor Cyan
Write-ColorOutput "- Test report: ./test-results/summary.md" -ForegroundColor White

# Exit with the test result
exit $testResult
