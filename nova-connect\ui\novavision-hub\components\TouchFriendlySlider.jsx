/**
 * TouchFriendlySlider Component
 * 
 * A touch-friendly slider component for mobile devices.
 */

import React, { useState, useRef, useEffect } from 'react';
import PropTypes from 'prop-types';

/**
 * TouchFriendlySlider component
 * 
 * @param {Object} props - Component props
 * @param {Array} props.items - Slider items
 * @param {number} [props.initialIndex=0] - Initial active item index
 * @param {boolean} [props.showDots=true] - Whether to show navigation dots
 * @param {boolean} [props.showArrows=true] - Whether to show navigation arrows
 * @param {boolean} [props.autoPlay=false] - Whether to automatically cycle through items
 * @param {number} [props.autoPlayInterval=5000] - Interval between auto-play transitions (in ms)
 * @param {boolean} [props.loop=false] - Whether to loop back to the first item after the last
 * @param {Function} [props.onChange] - Function to call when active item changes
 * @param {string} [props.className] - Additional CSS class names
 * @param {Object} [props.style] - Additional inline styles
 * @returns {React.ReactElement} TouchFriendlySlider component
 */
const TouchFriendlySlider = ({
  items,
  initialIndex = 0,
  showDots = true,
  showArrows = true,
  autoPlay = false,
  autoPlayInterval = 5000,
  loop = false,
  onChange,
  className = '',
  style = {}
}) => {
  const [activeIndex, setActiveIndex] = useState(initialIndex);
  const [touchStartX, setTouchStartX] = useState(null);
  const [touchEndX, setTouchEndX] = useState(null);
  const [isTouching, setIsTouching] = useState(false);
  const [touchOffset, setTouchOffset] = useState(0);
  const sliderRef = useRef(null);
  const autoPlayTimerRef = useRef(null);
  
  // Reset auto-play timer when active index changes
  useEffect(() => {
    if (onChange) {
      onChange(activeIndex);
    }
    
    if (autoPlay) {
      clearTimeout(autoPlayTimerRef.current);
      autoPlayTimerRef.current = setTimeout(() => {
        goToNext();
      }, autoPlayInterval);
    }
    
    return () => {
      clearTimeout(autoPlayTimerRef.current);
    };
  }, [activeIndex, autoPlay, autoPlayInterval, onChange]);
  
  // Go to previous item
  const goToPrev = () => {
    setActiveIndex(prevIndex => {
      if (prevIndex === 0) {
        return loop ? items.length - 1 : 0;
      }
      return prevIndex - 1;
    });
  };
  
  // Go to next item
  const goToNext = () => {
    setActiveIndex(prevIndex => {
      if (prevIndex === items.length - 1) {
        return loop ? 0 : items.length - 1;
      }
      return prevIndex + 1;
    });
  };
  
  // Go to specific item
  const goToIndex = (index) => {
    setActiveIndex(index);
  };
  
  // Handle touch start
  const handleTouchStart = (e) => {
    setTouchStartX(e.touches[0].clientX);
    setTouchEndX(e.touches[0].clientX);
    setIsTouching(true);
    setTouchOffset(0);
  };
  
  // Handle touch move
  const handleTouchMove = (e) => {
    if (!isTouching) return;
    
    setTouchEndX(e.touches[0].clientX);
    
    // Calculate touch offset
    const newOffset = touchEndX - touchStartX;
    setTouchOffset(newOffset);
  };
  
  // Handle touch end
  const handleTouchEnd = () => {
    if (!isTouching) return;
    
    setIsTouching(false);
    setTouchOffset(0);
    
    // Calculate swipe distance
    const swipeDistance = touchEndX - touchStartX;
    
    // Minimum swipe distance (in pixels)
    const minSwipeDistance = 50;
    
    // Check if swipe was long enough
    if (Math.abs(swipeDistance) >= minSwipeDistance) {
      if (swipeDistance > 0) {
        // Swiped right, go to previous
        goToPrev();
      } else {
        // Swiped left, go to next
        goToNext();
      }
    }
  };
  
  // Calculate transform style based on active index and touch offset
  const getTransformStyle = () => {
    const baseTransform = `translateX(-${activeIndex * 100}%)`;
    
    if (isTouching) {
      // Calculate percentage of slide width
      const slideWidth = sliderRef.current ? sliderRef.current.clientWidth : 0;
      const offsetPercentage = (touchOffset / slideWidth) * 100;
      
      return `translateX(calc(-${activeIndex * 100}% + ${offsetPercentage}%))`;
    }
    
    return baseTransform;
  };
  
  return (
    <div
      className={`relative overflow-hidden ${className}`}
      style={style}
      data-testid="touch-friendly-slider"
    >
      {/* Slider track */}
      <div
        ref={sliderRef}
        className="flex transition-transform duration-300 ease-out"
        style={{
          transform: getTransformStyle()
        }}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
        data-testid="slider-track"
      >
        {items.map((item, index) => (
          <div
            key={index}
            className="w-full flex-shrink-0"
            data-testid={`slider-item-${index}`}
          >
            {item}
          </div>
        ))}
      </div>
      
      {/* Navigation arrows */}
      {showArrows && items.length > 1 && (
        <>
          <button
            className={`
              absolute top-1/2 left-2 transform -translate-y-1/2 z-10
              w-10 h-10 rounded-full bg-white/80 shadow-md flex items-center justify-center
              focus:outline-none focus:ring-2 focus:ring-blue-500
              ${activeIndex === 0 && !loop ? 'opacity-50 cursor-not-allowed' : 'opacity-100 cursor-pointer'}
            `}
            onClick={goToPrev}
            disabled={activeIndex === 0 && !loop}
            aria-label="Previous"
            data-testid="prev-button"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          
          <button
            className={`
              absolute top-1/2 right-2 transform -translate-y-1/2 z-10
              w-10 h-10 rounded-full bg-white/80 shadow-md flex items-center justify-center
              focus:outline-none focus:ring-2 focus:ring-blue-500
              ${activeIndex === items.length - 1 && !loop ? 'opacity-50 cursor-not-allowed' : 'opacity-100 cursor-pointer'}
            `}
            onClick={goToNext}
            disabled={activeIndex === items.length - 1 && !loop}
            aria-label="Next"
            data-testid="next-button"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </>
      )}
      
      {/* Navigation dots */}
      {showDots && items.length > 1 && (
        <div className="absolute bottom-4 left-0 right-0 flex justify-center space-x-2" data-testid="navigation-dots">
          {items.map((_, index) => (
            <button
              key={index}
              className={`
                w-3 h-3 rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500
                ${activeIndex === index ? 'bg-blue-600' : 'bg-gray-300'}
              `}
              onClick={() => goToIndex(index)}
              aria-label={`Go to slide ${index + 1}`}
              data-testid={`dot-${index}`}
            />
          ))}
        </div>
      )}
    </div>
  );
};

TouchFriendlySlider.propTypes = {
  items: PropTypes.arrayOf(PropTypes.node).isRequired,
  initialIndex: PropTypes.number,
  showDots: PropTypes.bool,
  showArrows: PropTypes.bool,
  autoPlay: PropTypes.bool,
  autoPlayInterval: PropTypes.number,
  loop: PropTypes.bool,
  onChange: PropTypes.func,
  className: PropTypes.string,
  style: PropTypes.object
};

export default TouchFriendlySlider;
