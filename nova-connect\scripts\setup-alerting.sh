#!/bin/bash
# <PERSON><PERSON>t to set up alerting for NovaConnect UAC

# Set variables
PROJECT_ID=${1:-"novafuse-test"}
CLUSTER_NAME=${2:-"novafuse-test-cluster"}
ZONE=${3:-"us-central1-a"}
NAMESPACE=${4:-"novafuse-monitoring"}
EMAIL=${5:-"<EMAIL>"}

# Get credentials for the cluster
echo "Getting credentials for the cluster..."
gcloud container clusters get-credentials $CLUSTER_NAME --zone $ZONE --project $PROJECT_ID

# Create alert policy
echo "Creating alert policy..."
gcloud alpha monitoring policies create \
  --project=$PROJECT_ID \
  --policy-from-file=monitoring/alerts/marketplace-alerts.json

# Create notification channel
echo "Creating notification channel..."
CHANNEL_ID=$(gcloud alpha monitoring channels create \
  --project=$PROJECT_ID \
  --display-name="NovaConnect UAC Alerts" \
  --type=email \
  --channel-labels=email_address=$EMAIL \
  --format="value(name)")

# Update alert policy with notification channel
echo "Updating alert policy with notification channel..."
POLICY_ID=$(gcloud alpha monitoring policies list \
  --project=$PROJECT_ID \
  --filter="displayName='NovaConnect UAC Marketplace Alerts'" \
  --format="value(name)")

gcloud alpha monitoring policies update $POLICY_ID \
  --project=$PROJECT_ID \
  --add-notification-channels=$CHANNEL_ID

echo "Alerting setup complete!"
