/**
 * Authentication Validation Schema
 * 
 * This file defines the validation schema for authentication.
 */

const Joi = require('joi');

// Login schema
const loginSchema = Joi.object({
  username: Joi.string()
    .required()
    .messages({
      'any.required': 'Username is required',
      'string.empty': 'Username cannot be empty'
    }),
  
  password: Joi.string()
    .required()
    .messages({
      'any.required': 'Password is required',
      'string.empty': 'Password cannot be empty'
    })
});

module.exports = {
  loginSchema
};
