"""
Workflow Engine for the Universal Compliance Workflow Orchestrator.

This module provides the main engine for executing and managing compliance workflows.
"""

import logging
import uuid
from typing import Dict, List, Any, Optional, Callable

from .workflow_manager import WorkflowManager
from .task_manager import TaskManager
from .event_handler import EventHandler

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class WorkflowEngine:
    """
    Main engine for executing and managing compliance workflows.
    
    This class orchestrates the execution of workflows, manages their state,
    and handles events and tasks within the workflows.
    """
    
    def __init__(self):
        """Initialize the Workflow Engine."""
        logger.info("Initializing Workflow Engine")
        
        # Initialize the workflow manager
        self.workflow_manager = WorkflowManager()
        
        # Initialize the task manager
        self.task_manager = TaskManager()
        
        # Initialize the event handler
        self.event_handler = EventHandler()
        
        # Dictionary to store running workflow instances
        self.running_workflows: Dict[str, Dict[str, Any]] = {}
        
        logger.info("Workflow Engine initialized")
    
    def start_workflow(self, 
                      workflow_id: str, 
                      input_data: Optional[Dict[str, Any]] = None,
                      context: Optional[Dict[str, Any]] = None) -> str:
        """
        Start a workflow instance.
        
        Args:
            workflow_id: The ID of the workflow definition
            input_data: Input data for the workflow
            context: Additional context for the workflow
            
        Returns:
            The instance ID of the started workflow
            
        Raises:
            ValueError: If the workflow does not exist
        """
        logger.info(f"Starting workflow: {workflow_id}")
        
        # Get the workflow definition
        workflow_def = self.workflow_manager.get_workflow(workflow_id)
        
        # Generate a unique instance ID
        instance_id = str(uuid.uuid4())
        
        # Initialize workflow state
        workflow_state = {
            'instance_id': instance_id,
            'workflow_id': workflow_id,
            'status': 'running',
            'current_step': workflow_def['start_step'],
            'input_data': input_data or {},
            'context': context or {},
            'output_data': {},
            'history': [],
            'created_at': self._get_current_timestamp(),
            'updated_at': self._get_current_timestamp()
        }
        
        # Store the workflow instance
        self.running_workflows[instance_id] = workflow_state
        
        # Execute the first step
        self._execute_step(instance_id, workflow_state['current_step'])
        
        logger.info(f"Workflow started: {workflow_id}, Instance ID: {instance_id}")
        
        return instance_id
    
    def pause_workflow(self, instance_id: str) -> None:
        """
        Pause a running workflow instance.
        
        Args:
            instance_id: The instance ID of the workflow
            
        Raises:
            ValueError: If the workflow instance does not exist
        """
        logger.info(f"Pausing workflow instance: {instance_id}")
        
        if instance_id not in self.running_workflows:
            raise ValueError(f"Workflow instance not found: {instance_id}")
        
        workflow_state = self.running_workflows[instance_id]
        
        if workflow_state['status'] != 'running':
            logger.warning(f"Cannot pause workflow that is not running: {instance_id}, Status: {workflow_state['status']}")
            return
        
        # Update workflow state
        workflow_state['status'] = 'paused'
        workflow_state['updated_at'] = self._get_current_timestamp()
        
        # Add to history
        workflow_state['history'].append({
            'action': 'paused',
            'timestamp': self._get_current_timestamp()
        })
        
        logger.info(f"Workflow paused: {instance_id}")
    
    def resume_workflow(self, instance_id: str) -> None:
        """
        Resume a paused workflow instance.
        
        Args:
            instance_id: The instance ID of the workflow
            
        Raises:
            ValueError: If the workflow instance does not exist
        """
        logger.info(f"Resuming workflow instance: {instance_id}")
        
        if instance_id not in self.running_workflows:
            raise ValueError(f"Workflow instance not found: {instance_id}")
        
        workflow_state = self.running_workflows[instance_id]
        
        if workflow_state['status'] != 'paused':
            logger.warning(f"Cannot resume workflow that is not paused: {instance_id}, Status: {workflow_state['status']}")
            return
        
        # Update workflow state
        workflow_state['status'] = 'running'
        workflow_state['updated_at'] = self._get_current_timestamp()
        
        # Add to history
        workflow_state['history'].append({
            'action': 'resumed',
            'timestamp': self._get_current_timestamp()
        })
        
        # Continue execution from the current step
        self._execute_step(instance_id, workflow_state['current_step'])
        
        logger.info(f"Workflow resumed: {instance_id}")
    
    def cancel_workflow(self, instance_id: str) -> None:
        """
        Cancel a workflow instance.
        
        Args:
            instance_id: The instance ID of the workflow
            
        Raises:
            ValueError: If the workflow instance does not exist
        """
        logger.info(f"Cancelling workflow instance: {instance_id}")
        
        if instance_id not in self.running_workflows:
            raise ValueError(f"Workflow instance not found: {instance_id}")
        
        workflow_state = self.running_workflows[instance_id]
        
        if workflow_state['status'] in ['completed', 'failed', 'cancelled']:
            logger.warning(f"Workflow already in terminal state: {instance_id}, Status: {workflow_state['status']}")
            return
        
        # Update workflow state
        workflow_state['status'] = 'cancelled'
        workflow_state['updated_at'] = self._get_current_timestamp()
        
        # Add to history
        workflow_state['history'].append({
            'action': 'cancelled',
            'timestamp': self._get_current_timestamp()
        })
        
        # Trigger cancellation event
        self.event_handler.trigger_event('workflow_cancelled', {
            'instance_id': instance_id,
            'workflow_id': workflow_state['workflow_id']
        })
        
        logger.info(f"Workflow cancelled: {instance_id}")
    
    def get_workflow_status(self, instance_id: str) -> Dict[str, Any]:
        """
        Get the status of a workflow instance.
        
        Args:
            instance_id: The instance ID of the workflow
            
        Returns:
            The workflow state
            
        Raises:
            ValueError: If the workflow instance does not exist
        """
        if instance_id not in self.running_workflows:
            raise ValueError(f"Workflow instance not found: {instance_id}")
        
        return self.running_workflows[instance_id]
    
    def get_all_workflows(self, status: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get all workflow instances, optionally filtered by status.
        
        Args:
            status: Optional status filter
            
        Returns:
            List of workflow states
        """
        if status:
            return [
                workflow for workflow in self.running_workflows.values()
                if workflow['status'] == status
            ]
        else:
            return list(self.running_workflows.values())
    
    def register_event_handler(self, event_type: str, handler: Callable) -> None:
        """
        Register a handler for a specific event type.
        
        Args:
            event_type: The type of event
            handler: The handler function
        """
        self.event_handler.register_handler(event_type, handler)
    
    def _execute_step(self, instance_id: str, step_id: str) -> None:
        """
        Execute a workflow step.
        
        Args:
            instance_id: The instance ID of the workflow
            step_id: The ID of the step to execute
        """
        logger.info(f"Executing step: {step_id} for workflow instance: {instance_id}")
        
        workflow_state = self.running_workflows[instance_id]
        workflow_def = self.workflow_manager.get_workflow(workflow_state['workflow_id'])
        
        # Get the step definition
        if step_id not in workflow_def['steps']:
            self._handle_workflow_error(instance_id, f"Step not found: {step_id}")
            return
        
        step_def = workflow_def['steps'][step_id]
        
        # Add to history
        workflow_state['history'].append({
            'action': 'step_started',
            'step_id': step_id,
            'timestamp': self._get_current_timestamp()
        })
        
        # Update current step
        workflow_state['current_step'] = step_id
        workflow_state['updated_at'] = self._get_current_timestamp()
        
        try:
            # Execute the step
            if step_def['type'] == 'task':
                # Execute a task
                task_id = step_def['task_id']
                task_input = self._prepare_task_input(workflow_state, step_def)
                
                task_result = self.task_manager.execute_task(task_id, task_input)
                
                # Process the task result
                self._process_task_result(instance_id, step_id, task_result)
                
            elif step_def['type'] == 'decision':
                # Evaluate a decision
                condition = step_def['condition']
                condition_result = self._evaluate_condition(workflow_state, condition)
                
                # Determine the next step based on the condition result
                next_step = step_def['true_step'] if condition_result else step_def['false_step']
                
                # Add to history
                workflow_state['history'].append({
                    'action': 'decision',
                    'step_id': step_id,
                    'condition_result': condition_result,
                    'next_step': next_step,
                    'timestamp': self._get_current_timestamp()
                })
                
                # Execute the next step
                self._execute_step(instance_id, next_step)
                
            elif step_def['type'] == 'parallel':
                # Execute parallel steps
                parallel_steps = step_def['steps']
                
                # TODO: Implement parallel execution
                # For now, execute sequentially
                for parallel_step in parallel_steps:
                    self._execute_step(instance_id, parallel_step)
                
                # Execute the next step
                self._execute_step(instance_id, step_def['next_step'])
                
            elif step_def['type'] == 'end':
                # End the workflow
                workflow_state['status'] = 'completed'
                workflow_state['updated_at'] = self._get_current_timestamp()
                
                # Add to history
                workflow_state['history'].append({
                    'action': 'completed',
                    'timestamp': self._get_current_timestamp()
                })
                
                # Trigger completion event
                self.event_handler.trigger_event('workflow_completed', {
                    'instance_id': instance_id,
                    'workflow_id': workflow_state['workflow_id'],
                    'output_data': workflow_state['output_data']
                })
                
                logger.info(f"Workflow completed: {instance_id}")
                
            else:
                self._handle_workflow_error(instance_id, f"Unknown step type: {step_def['type']}")
                
        except Exception as e:
            self._handle_workflow_error(instance_id, f"Error executing step: {step_id}, Error: {str(e)}")
    
    def _process_task_result(self, instance_id: str, step_id: str, task_result: Dict[str, Any]) -> None:
        """
        Process the result of a task execution.
        
        Args:
            instance_id: The instance ID of the workflow
            step_id: The ID of the step
            task_result: The result of the task execution
        """
        workflow_state = self.running_workflows[instance_id]
        workflow_def = self.workflow_manager.get_workflow(workflow_state['workflow_id'])
        step_def = workflow_def['steps'][step_id]
        
        # Update output data
        if 'output_mapping' in step_def:
            for output_key, workflow_key in step_def['output_mapping'].items():
                if output_key in task_result:
                    workflow_state['output_data'][workflow_key] = task_result[output_key]
        
        # Add to history
        workflow_state['history'].append({
            'action': 'step_completed',
            'step_id': step_id,
            'timestamp': self._get_current_timestamp()
        })
        
        # Execute the next step
        if 'next_step' in step_def:
            self._execute_step(instance_id, step_def['next_step'])
        else:
            # No next step defined, end the workflow
            workflow_state['status'] = 'completed'
            workflow_state['updated_at'] = self._get_current_timestamp()
            
            # Add to history
            workflow_state['history'].append({
                'action': 'completed',
                'timestamp': self._get_current_timestamp()
            })
            
            # Trigger completion event
            self.event_handler.trigger_event('workflow_completed', {
                'instance_id': instance_id,
                'workflow_id': workflow_state['workflow_id'],
                'output_data': workflow_state['output_data']
            })
            
            logger.info(f"Workflow completed: {instance_id}")
    
    def _prepare_task_input(self, workflow_state: Dict[str, Any], step_def: Dict[str, Any]) -> Dict[str, Any]:
        """
        Prepare the input for a task execution.
        
        Args:
            workflow_state: The workflow state
            step_def: The step definition
            
        Returns:
            The task input
        """
        task_input = {}
        
        # Add workflow input data
        task_input.update(workflow_state['input_data'])
        
        # Add workflow output data
        task_input.update(workflow_state['output_data'])
        
        # Add workflow context
        task_input['context'] = workflow_state['context']
        
        # Add workflow metadata
        task_input['workflow_instance_id'] = workflow_state['instance_id']
        task_input['workflow_id'] = workflow_state['workflow_id']
        
        # Add step-specific input mapping
        if 'input_mapping' in step_def:
            for workflow_key, task_key in step_def['input_mapping'].items():
                if workflow_key in workflow_state['output_data']:
                    task_input[task_key] = workflow_state['output_data'][workflow_key]
                elif workflow_key in workflow_state['input_data']:
                    task_input[task_key] = workflow_state['input_data'][workflow_key]
        
        return task_input
    
    def _evaluate_condition(self, workflow_state: Dict[str, Any], condition: Dict[str, Any]) -> bool:
        """
        Evaluate a condition.
        
        Args:
            workflow_state: The workflow state
            condition: The condition to evaluate
            
        Returns:
            The result of the condition evaluation
        """
        # TODO: Implement a more sophisticated condition evaluator
        
        # Simple implementation for now
        if condition['type'] == 'equals':
            left_value = self._get_value_from_workflow(workflow_state, condition['left'])
            right_value = self._get_value_from_workflow(workflow_state, condition['right'])
            return left_value == right_value
        
        elif condition['type'] == 'not_equals':
            left_value = self._get_value_from_workflow(workflow_state, condition['left'])
            right_value = self._get_value_from_workflow(workflow_state, condition['right'])
            return left_value != right_value
        
        elif condition['type'] == 'greater_than':
            left_value = self._get_value_from_workflow(workflow_state, condition['left'])
            right_value = self._get_value_from_workflow(workflow_state, condition['right'])
            return left_value > right_value
        
        elif condition['type'] == 'less_than':
            left_value = self._get_value_from_workflow(workflow_state, condition['left'])
            right_value = self._get_value_from_workflow(workflow_state, condition['right'])
            return left_value < right_value
        
        elif condition['type'] == 'contains':
            left_value = self._get_value_from_workflow(workflow_state, condition['left'])
            right_value = self._get_value_from_workflow(workflow_state, condition['right'])
            return right_value in left_value
        
        elif condition['type'] == 'and':
            return all(self._evaluate_condition(workflow_state, subcondition) for subcondition in condition['conditions'])
        
        elif condition['type'] == 'or':
            return any(self._evaluate_condition(workflow_state, subcondition) for subcondition in condition['conditions'])
        
        elif condition['type'] == 'not':
            return not self._evaluate_condition(workflow_state, condition['condition'])
        
        else:
            logger.warning(f"Unknown condition type: {condition['type']}")
            return False
    
    def _get_value_from_workflow(self, workflow_state: Dict[str, Any], value_spec: Dict[str, Any]) -> Any:
        """
        Get a value from the workflow state.
        
        Args:
            workflow_state: The workflow state
            value_spec: The value specification
            
        Returns:
            The value
        """
        if value_spec['type'] == 'literal':
            return value_spec['value']
        
        elif value_spec['type'] == 'input':
            key = value_spec['key']
            return workflow_state['input_data'].get(key)
        
        elif value_spec['type'] == 'output':
            key = value_spec['key']
            return workflow_state['output_data'].get(key)
        
        elif value_spec['type'] == 'context':
            key = value_spec['key']
            return workflow_state['context'].get(key)
        
        else:
            logger.warning(f"Unknown value specification type: {value_spec['type']}")
            return None
    
    def _handle_workflow_error(self, instance_id: str, error_message: str) -> None:
        """
        Handle a workflow error.
        
        Args:
            instance_id: The instance ID of the workflow
            error_message: The error message
        """
        logger.error(f"Workflow error: {error_message}, Instance ID: {instance_id}")
        
        workflow_state = self.running_workflows[instance_id]
        
        # Update workflow state
        workflow_state['status'] = 'failed'
        workflow_state['error'] = error_message
        workflow_state['updated_at'] = self._get_current_timestamp()
        
        # Add to history
        workflow_state['history'].append({
            'action': 'failed',
            'error': error_message,
            'timestamp': self._get_current_timestamp()
        })
        
        # Trigger error event
        self.event_handler.trigger_event('workflow_failed', {
            'instance_id': instance_id,
            'workflow_id': workflow_state['workflow_id'],
            'error': error_message
        })
    
    def _get_current_timestamp(self) -> str:
        """
        Get the current timestamp.
        
        Returns:
            The current timestamp as a string
        """
        import datetime
        return datetime.datetime.now().isoformat()
