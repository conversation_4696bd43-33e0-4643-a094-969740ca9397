/**
 * NovaFuse Universal API Connector - Connector Configuration Service
 * 
 * This module provides services for managing connector configurations.
 */

const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');
const { createLogger } = require('../../utils/logger');
const { ValidationError, ResourceNotFoundError } = require('../../errors');
const connectorRegistryService = require('./connector-registry-service');

const logger = createLogger('connector-config-service');

/**
 * Connector Configuration Service class
 */
class ConnectorConfigService {
  constructor(options = {}) {
    this.dataDir = options.dataDir || path.join(process.cwd(), 'data', 'connector-configs');
    this.configs = new Map();
    this.initialized = false;
    this.encryptionKey = options.encryptionKey || process.env.CONNECTOR_CONFIG_ENCRYPTION_KEY || 'novafuse-uac-default-key';
    
    logger.info('Connector configuration service initialized');
  }

  /**
   * Initialize the service
   */
  async initialize() {
    if (this.initialized) {
      return;
    }

    try {
      // Create data directory if it doesn't exist
      await fs.mkdir(this.dataDir, { recursive: true });
      
      // Load configurations from disk
      await this._loadConfigurations();
      
      this.initialized = true;
      logger.info(`Connector configuration service initialized with ${this.configs.size} configurations`);
    } catch (error) {
      logger.error('Failed to initialize connector configuration service:', { error });
      throw error;
    }
  }

  /**
   * Load configurations from disk
   * 
   * @private
   */
  async _loadConfigurations() {
    try {
      // Get configuration files
      const files = await fs.readdir(this.dataDir);
      const configFiles = files.filter(file => file.endsWith('.json'));
      
      // Load each configuration
      for (const file of configFiles) {
        try {
          const data = await fs.readFile(path.join(this.dataDir, file), 'utf8');
          const config = JSON.parse(data);
          
          // Decrypt sensitive fields
          if (config.encryptedFields && config.encryptedFields.length > 0) {
            for (const field of config.encryptedFields) {
              if (config.values[field]) {
                config.values[field] = this._decrypt(config.values[field]);
              }
            }
          }
          
          this.configs.set(config.id, config);
          logger.debug(`Loaded configuration: ${config.name} (${config.id})`);
        } catch (error) {
          logger.error(`Failed to load configuration from file ${file}:`, { error });
        }
      }
    } catch (error) {
      logger.error('Failed to load configurations from disk:', { error });
      throw error;
    }
  }

  /**
   * Save a configuration to disk
   * 
   * @param {Object} config - The configuration to save
   * @private
   */
  async _saveConfiguration(config) {
    try {
      // Create a copy of the configuration
      const configToSave = { ...config };
      
      // Encrypt sensitive fields
      if (configToSave.encryptedFields && configToSave.encryptedFields.length > 0) {
        configToSave.values = { ...configToSave.values };
        
        for (const field of configToSave.encryptedFields) {
          if (configToSave.values[field]) {
            configToSave.values[field] = this._encrypt(configToSave.values[field]);
          }
        }
      }
      
      const filePath = path.join(this.dataDir, `${config.id}.json`);
      await fs.writeFile(filePath, JSON.stringify(configToSave, null, 2));
      logger.debug(`Saved configuration to disk: ${config.name} (${config.id})`);
    } catch (error) {
      logger.error(`Failed to save configuration ${config.id}:`, { error });
      throw error;
    }
  }

  /**
   * Encrypt a value
   * 
   * @param {string} value - The value to encrypt
   * @returns {string} - The encrypted value
   * @private
   */
  _encrypt(value) {
    if (!value) return value;
    
    try {
      const iv = crypto.randomBytes(16);
      const cipher = crypto.createCipheriv('aes-256-cbc', Buffer.from(this.encryptionKey.padEnd(32).slice(0, 32)), iv);
      let encrypted = cipher.update(value, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      return `${iv.toString('hex')}:${encrypted}`;
    } catch (error) {
      logger.error('Failed to encrypt value:', { error });
      return value;
    }
  }

  /**
   * Decrypt a value
   * 
   * @param {string} value - The value to decrypt
   * @returns {string} - The decrypted value
   * @private
   */
  _decrypt(value) {
    if (!value || !value.includes(':')) return value;
    
    try {
      const [ivHex, encryptedHex] = value.split(':');
      const iv = Buffer.from(ivHex, 'hex');
      const decipher = crypto.createDecipheriv('aes-256-cbc', Buffer.from(this.encryptionKey.padEnd(32).slice(0, 32)), iv);
      let decrypted = decipher.update(encryptedHex, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      return decrypted;
    } catch (error) {
      logger.error('Failed to decrypt value:', { error });
      return value;
    }
  }

  /**
   * Validate configuration against schema
   * 
   * @param {Object} values - The configuration values
   * @param {Object} schema - The configuration schema
   * @returns {Object} - Validation result
   * @private
   */
  _validateConfig(values, schema) {
    const errors = [];
    
    // Check required properties
    if (schema.required) {
      for (const prop of schema.required) {
        if (values[prop] === undefined || values[prop] === null || values[prop] === '') {
          errors.push(`${prop} is required`);
        }
      }
    }
    
    // Check property types and constraints
    if (schema.properties) {
      for (const [prop, propSchema] of Object.entries(schema.properties)) {
        if (values[prop] !== undefined && values[prop] !== null) {
          // Check type
          if (propSchema.type) {
            const type = typeof values[prop];
            
            if (propSchema.type === 'string' && type !== 'string') {
              errors.push(`${prop} must be a string`);
            } else if (propSchema.type === 'number' && type !== 'number') {
              errors.push(`${prop} must be a number`);
            } else if (propSchema.type === 'boolean' && type !== 'boolean') {
              errors.push(`${prop} must be a boolean`);
            } else if (propSchema.type === 'object' && (type !== 'object' || Array.isArray(values[prop]))) {
              errors.push(`${prop} must be an object`);
            } else if (propSchema.type === 'array' && !Array.isArray(values[prop])) {
              errors.push(`${prop} must be an array`);
            }
          }
          
          // Check enum
          if (propSchema.enum && !propSchema.enum.includes(values[prop])) {
            errors.push(`${prop} must be one of: ${propSchema.enum.join(', ')}`);
          }
          
          // Check minimum/maximum for numbers
          if (propSchema.type === 'number' || typeof values[prop] === 'number') {
            if (propSchema.minimum !== undefined && values[prop] < propSchema.minimum) {
              errors.push(`${prop} must be at least ${propSchema.minimum}`);
            }
            
            if (propSchema.maximum !== undefined && values[prop] > propSchema.maximum) {
              errors.push(`${prop} must be at most ${propSchema.maximum}`);
            }
          }
          
          // Check minLength/maxLength for strings
          if (propSchema.type === 'string' || typeof values[prop] === 'string') {
            if (propSchema.minLength !== undefined && values[prop].length < propSchema.minLength) {
              errors.push(`${prop} must be at least ${propSchema.minLength} characters`);
            }
            
            if (propSchema.maxLength !== undefined && values[prop].length > propSchema.maxLength) {
              errors.push(`${prop} must be at most ${propSchema.maxLength} characters`);
            }
            
            // Check pattern
            if (propSchema.pattern && !new RegExp(propSchema.pattern).test(values[prop])) {
              errors.push(`${prop} must match pattern: ${propSchema.pattern}`);
            }
          }
          
          // Check minItems/maxItems for arrays
          if (propSchema.type === 'array' || Array.isArray(values[prop])) {
            if (propSchema.minItems !== undefined && values[prop].length < propSchema.minItems) {
              errors.push(`${prop} must have at least ${propSchema.minItems} items`);
            }
            
            if (propSchema.maxItems !== undefined && values[prop].length > propSchema.maxItems) {
              errors.push(`${prop} must have at most ${propSchema.maxItems} items`);
            }
          }
        }
      }
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Get all configurations
   * 
   * @param {Object} filters - Filters to apply
   * @returns {Array<Object>} - The configurations
   */
  async getAllConfigurations(filters = {}) {
    await this.initialize();
    
    let configs = Array.from(this.configs.values());
    
    // Apply filters
    if (filters.connectorId) {
      configs = configs.filter(c => c.connectorId === filters.connectorId);
    }
    
    if (filters.environment) {
      configs = configs.filter(c => c.environment === filters.environment);
    }
    
    if (filters.partnerId) {
      configs = configs.filter(c => c.partnerId === filters.partnerId);
    }
    
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      configs = configs.filter(c => 
        c.name.toLowerCase().includes(searchLower) || 
        c.description.toLowerCase().includes(searchLower)
      );
    }
    
    // Apply sorting
    if (filters.sortBy) {
      const sortField = filters.sortBy;
      const sortOrder = filters.sortOrder === 'desc' ? -1 : 1;
      
      configs.sort((a, b) => {
        if (a[sortField] < b[sortField]) return -1 * sortOrder;
        if (a[sortField] > b[sortField]) return 1 * sortOrder;
        return 0;
      });
    } else {
      // Default sort by name
      configs.sort((a, b) => a.name.localeCompare(b.name));
    }
    
    return configs;
  }

  /**
   * Get a configuration by ID
   * 
   * @param {string} id - The configuration ID
   * @returns {Object} - The configuration
   */
  async getConfiguration(id) {
    await this.initialize();
    
    const config = this.configs.get(id);
    
    if (!config) {
      throw new ResourceNotFoundError('Configuration', id);
    }
    
    return config;
  }

  /**
   * Create a new configuration
   * 
   * @param {Object} data - The configuration data
   * @returns {Object} - The created configuration
   */
  async createConfiguration(data) {
    await this.initialize();
    
    // Validate required fields
    if (!data.name) {
      throw new ValidationError('Name is required');
    }
    
    if (!data.connectorId) {
      throw new ValidationError('Connector ID is required');
    }
    
    // Get connector to validate configuration
    const connector = await connectorRegistryService.getConnector(data.connectorId);
    
    // Create configuration ID if not provided
    const id = data.id || `${data.connectorId}-${Date.now()}`;
    
    // Create configuration
    const config = {
      id,
      name: data.name,
      description: data.description || '',
      connectorId: data.connectorId,
      connectorVersion: connector.version,
      environment: data.environment || 'development',
      partnerId: data.partnerId || null,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      values: data.values || {},
      encryptedFields: data.encryptedFields || [],
      enabled: data.enabled !== undefined ? data.enabled : true,
      tags: data.tags || []
    };
    
    // Validate configuration against schema
    const validation = this._validateConfig(config.values, connector.configSchema);
    if (!validation.valid) {
      throw new ValidationError('Invalid configuration', { validationErrors: validation.errors });
    }
    
    // Save configuration
    this.configs.set(id, config);
    await this._saveConfiguration(config);
    
    logger.info(`Created configuration: ${config.name} (${config.id})`);
    
    return config;
  }

  /**
   * Update a configuration
   * 
   * @param {string} id - The configuration ID
   * @param {Object} data - The data to update
   * @returns {Object} - The updated configuration
   */
  async updateConfiguration(id, data) {
    await this.initialize();
    
    // Get configuration
    const config = await this.getConfiguration(id);
    
    // Update fields
    const updatedConfig = {
      ...config,
      ...data,
      id: config.id, // Ensure ID doesn't change
      connectorId: config.connectorId, // Ensure connector ID doesn't change
      updatedAt: new Date().toISOString(),
      values: data.values !== undefined ? data.values : config.values,
      encryptedFields: data.encryptedFields !== undefined ? data.encryptedFields : config.encryptedFields
    };
    
    // Get connector to validate configuration
    const connector = await connectorRegistryService.getConnector(config.connectorId);
    
    // Validate configuration against schema
    const validation = this._validateConfig(updatedConfig.values, connector.configSchema);
    if (!validation.valid) {
      throw new ValidationError('Invalid configuration', { validationErrors: validation.errors });
    }
    
    // Save configuration
    this.configs.set(id, updatedConfig);
    await this._saveConfiguration(updatedConfig);
    
    logger.info(`Updated configuration: ${updatedConfig.name} (${updatedConfig.id})`);
    
    return updatedConfig;
  }

  /**
   * Delete a configuration
   * 
   * @param {string} id - The configuration ID
   * @returns {boolean} - Whether the configuration was deleted
   */
  async deleteConfiguration(id) {
    await this.initialize();
    
    // Get configuration
    const config = await this.getConfiguration(id);
    
    // Delete configuration
    this.configs.delete(id);
    
    try {
      // Delete file
      const filePath = path.join(this.dataDir, `${id}.json`);
      await fs.unlink(filePath);
    } catch (error) {
      logger.error(`Failed to delete configuration file for ${id}:`, { error });
    }
    
    logger.info(`Deleted configuration: ${config.name} (${config.id})`);
    
    return true;
  }

  /**
   * Test a configuration
   * 
   * @param {string} id - The configuration ID
   * @returns {Object} - The test result
   */
  async testConfiguration(id) {
    await this.initialize();
    
    // Get configuration
    const config = await this.getConfiguration(id);
    
    // Get connector
    const connector = await connectorRegistryService.getConnector(config.connectorId);
    
    // TODO: Implement actual connector testing logic
    // This would involve loading the connector module and calling its test method
    
    // For now, just return a mock result
    const testResult = {
      success: true,
      timestamp: new Date().toISOString(),
      message: 'Configuration test successful',
      details: {
        connectorName: connector.name,
        connectorVersion: connector.version,
        configurationName: config.name,
        environment: config.environment
      }
    };
    
    logger.info(`Tested configuration: ${config.name} (${config.id})`);
    
    return testResult;
  }

  /**
   * Clone a configuration
   * 
   * @param {string} id - The configuration ID
   * @param {Object} options - Clone options
   * @returns {Object} - The cloned configuration
   */
  async cloneConfiguration(id, options = {}) {
    await this.initialize();
    
    // Get configuration
    const config = await this.getConfiguration(id);
    
    // Create new configuration
    const clonedConfig = {
      ...config,
      id: `${config.id}-clone-${Date.now()}`,
      name: options.name || `${config.name} (Clone)`,
      description: options.description || `Clone of ${config.name}`,
      environment: options.environment || config.environment,
      partnerId: options.partnerId || config.partnerId,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      values: { ...config.values },
      tags: [...config.tags]
    };
    
    // Override values if provided
    if (options.values) {
      Object.assign(clonedConfig.values, options.values);
    }
    
    // Save configuration
    this.configs.set(clonedConfig.id, clonedConfig);
    await this._saveConfiguration(clonedConfig);
    
    logger.info(`Cloned configuration ${config.id} to ${clonedConfig.id}`);
    
    return clonedConfig;
  }

  /**
   * Get configurations for a connector
   * 
   * @param {string} connectorId - The connector ID
   * @returns {Array<Object>} - The configurations
   */
  async getConfigurationsForConnector(connectorId) {
    await this.initialize();
    
    return this.getAllConfigurations({ connectorId });
  }

  /**
   * Get configurations for a partner
   * 
   * @param {string} partnerId - The partner ID
   * @returns {Array<Object>} - The configurations
   */
  async getConfigurationsForPartner(partnerId) {
    await this.initialize();
    
    return this.getAllConfigurations({ partnerId });
  }
}

// Create singleton instance
const connectorConfigService = new ConnectorConfigService();

module.exports = connectorConfigService;
