/**
 * Monitoring Controller
 * 
 * This controller provides endpoints for monitoring metrics and alerts.
 */

const logger = require('../../config/logger');
const { getAllMetrics } = require('../../monitoring/metrics');
const { getAlertHistory, STATUS } = require('../../monitoring/alerts');
const { getRedisClient } = require('../../config/redis');

// Redis client for metrics
const redisClient = getRedisClient();

/**
 * Get monitoring metrics
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getMetrics = async (req, res, next) => {
  try {
    logger.debug('Getting monitoring metrics');
    
    // Get query parameters
    const { timeRange = '1h', visualizationType } = req.query;
    
    // Calculate time range
    const endTime = Date.now();
    let startTime;
    
    switch (timeRange) {
      case '1h':
        startTime = endTime - 60 * 60 * 1000; // 1 hour
        break;
      case '6h':
        startTime = endTime - 6 * 60 * 60 * 1000; // 6 hours
        break;
      case '24h':
        startTime = endTime - 24 * 60 * 60 * 1000; // 24 hours
        break;
      case '7d':
        startTime = endTime - 7 * 24 * 60 * 60 * 1000; // 7 days
        break;
      case '30d':
        startTime = endTime - 30 * 24 * 60 * 60 * 1000; // 30 days
        break;
      default:
        startTime = endTime - 60 * 60 * 1000; // Default to 1 hour
    }
    
    // Get metrics from Redis
    const metrics = await getMetricsFromRedis(startTime, endTime, visualizationType);
    
    // Process metrics for visualization
    const processedMetrics = processMetricsForVisualization(metrics);
    
    // Return metrics
    res.status(200).json(processedMetrics);
  } catch (error) {
    logger.error('Error getting monitoring metrics:', error);
    next(error);
  }
};

/**
 * Get monitoring alerts
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getAlerts = async (req, res, next) => {
  try {
    logger.debug('Getting monitoring alerts');
    
    // Get query parameters
    const { timeRange = '24h', visualizationType, status } = req.query;
    
    // Calculate time range
    const endTime = Date.now();
    let startTime;
    
    switch (timeRange) {
      case '1h':
        startTime = endTime - 60 * 60 * 1000; // 1 hour
        break;
      case '6h':
        startTime = endTime - 6 * 60 * 60 * 1000; // 6 hours
        break;
      case '24h':
        startTime = endTime - 24 * 60 * 60 * 1000; // 24 hours
        break;
      case '7d':
        startTime = endTime - 7 * 24 * 60 * 60 * 1000; // 7 days
        break;
      case '30d':
        startTime = endTime - 30 * 24 * 60 * 60 * 1000; // 30 days
        break;
      default:
        startTime = endTime - 24 * 60 * 60 * 1000; // Default to 24 hours
    }
    
    // Get all alerts
    const allAlerts = [];
    
    // Get alerts for each visualization type
    const visualizationTypes = visualizationType 
      ? [visualizationType] 
      : ['triDomainTensor', 'harmonyIndex', 'riskControlFusion', 'resonanceSpectrogram', 'unifiedComplianceSecurity'];
    
    for (const type of visualizationTypes) {
      // Get alerts for high error rate
      const highErrorRateAlerts = await getAlertHistory(`cyber_safety_visualization_high_error_rate_${type}`, 100);
      
      // Get alerts for slow response time
      const slowResponseTimeAlerts = await getAlertHistory(`cyber_safety_visualization_slow_response_time_${type}`, 100);
      
      // Add alerts to list
      allAlerts.push(...highErrorRateAlerts, ...slowResponseTimeAlerts);
    }
    
    // Filter alerts by time range
    const filteredAlerts = allAlerts.filter(alert => {
      const alertTime = new Date(alert.timestamp).getTime();
      return alertTime >= startTime && alertTime <= endTime;
    });
    
    // Filter alerts by status if specified
    const statusFilteredAlerts = status
      ? filteredAlerts.filter(alert => alert.status === status)
      : filteredAlerts;
    
    // Sort alerts by timestamp (newest first)
    const sortedAlerts = statusFilteredAlerts.sort((a, b) => {
      return new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime();
    });
    
    // Return alerts
    res.status(200).json(sortedAlerts);
  } catch (error) {
    logger.error('Error getting monitoring alerts:', error);
    next(error);
  }
};

/**
 * Get metrics from Redis
 * @param {number} startTime - Start time in milliseconds
 * @param {number} endTime - End time in milliseconds
 * @param {string} visualizationType - Visualization type filter
 * @returns {Array} - Array of metrics
 */
const getMetricsFromRedis = async (startTime, endTime, visualizationType) => {
  try {
    // Get all keys for metrics
    const pattern = visualizationType
      ? `cyber_safety:metrics:${visualizationType}:*`
      : 'cyber_safety:metrics:*';
    
    const keys = await redisClient.keys(pattern);
    
    // Filter keys by timestamp
    const filteredKeys = keys.filter(key => {
      const timestamp = parseInt(key.split(':').pop(), 10);
      return timestamp >= startTime && timestamp <= endTime;
    });
    
    // Get metrics for filtered keys
    const metrics = [];
    
    for (const key of filteredKeys) {
      const data = await redisClient.get(key);
      if (data) {
        metrics.push(JSON.parse(data));
      }
    }
    
    return metrics;
  } catch (error) {
    logger.error('Error getting metrics from Redis:', error);
    return [];
  }
};

/**
 * Process metrics for visualization
 * @param {Array} metrics - Raw metrics from Redis
 * @returns {Object} - Processed metrics for visualization
 */
const processMetricsForVisualization = (metrics) => {
  try {
    // Group metrics by visualization type
    const groupedMetrics = {};
    
    metrics.forEach(metric => {
      const visualizationType = metric.visualizationType || 'unknown';
      
      if (!groupedMetrics[visualizationType]) {
        groupedMetrics[visualizationType] = [];
      }
      
      groupedMetrics[visualizationType].push(metric);
    });
    
    // Process request count
    const requestCount = processRequestCount(metrics);
    
    // Process response time
    const responseTime = processResponseTime(metrics);
    
    // Process error count
    const errorCount = processErrorCount(metrics);
    
    // Process data size
    const dataSize = processDataSize(metrics);
    
    // Process cache hit rate
    const cacheHitRate = processCacheHitRate(metrics);
    
    return {
      requestCount,
      responseTime,
      errorCount,
      dataSize,
      cacheHitRate
    };
  } catch (error) {
    logger.error('Error processing metrics for visualization:', error);
    return {
      requestCount: [],
      responseTime: [],
      errorCount: [],
      dataSize: [],
      cacheHitRate: []
    };
  }
};

/**
 * Process request count metrics
 * @param {Array} metrics - Raw metrics
 * @returns {Array} - Processed request count metrics
 */
const processRequestCount = (metrics) => {
  try {
    // Group metrics by timestamp (rounded to minute)
    const groupedByTime = {};
    
    metrics.forEach(metric => {
      // Round timestamp to minute
      const timestamp = new Date(metric.timestamp);
      timestamp.setSeconds(0, 0);
      const timeKey = timestamp.getTime();
      
      if (!groupedByTime[timeKey]) {
        groupedByTime[timeKey] = {
          timestamp: timeKey,
          triDomainTensor: 0,
          harmonyIndex: 0,
          riskControlFusion: 0,
          resonanceSpectrogram: 0,
          unifiedComplianceSecurity: 0
        };
      }
      
      // Increment count for visualization type
      const visualizationType = metric.visualizationType || 'unknown';
      if (groupedByTime[timeKey][visualizationType] !== undefined) {
        groupedByTime[timeKey][visualizationType]++;
      }
    });
    
    // Convert to array and sort by timestamp
    return Object.values(groupedByTime).sort((a, b) => a.timestamp - b.timestamp);
  } catch (error) {
    logger.error('Error processing request count metrics:', error);
    return [];
  }
};

/**
 * Process response time metrics
 * @param {Array} metrics - Raw metrics
 * @returns {Array} - Processed response time metrics
 */
const processResponseTime = (metrics) => {
  try {
    // Group metrics by timestamp (rounded to minute)
    const groupedByTime = {};
    
    metrics.forEach(metric => {
      // Skip if no response time
      if (metric.responseTime === undefined) return;
      
      // Round timestamp to minute
      const timestamp = new Date(metric.timestamp);
      timestamp.setSeconds(0, 0);
      const timeKey = timestamp.getTime();
      
      if (!groupedByTime[timeKey]) {
        groupedByTime[timeKey] = {
          timestamp: timeKey,
          triDomainTensor: { sum: 0, count: 0 },
          harmonyIndex: { sum: 0, count: 0 },
          riskControlFusion: { sum: 0, count: 0 },
          resonanceSpectrogram: { sum: 0, count: 0 },
          unifiedComplianceSecurity: { sum: 0, count: 0 }
        };
      }
      
      // Add response time for visualization type
      const visualizationType = metric.visualizationType || 'unknown';
      if (groupedByTime[timeKey][visualizationType]) {
        groupedByTime[timeKey][visualizationType].sum += metric.responseTime;
        groupedByTime[timeKey][visualizationType].count++;
      }
    });
    
    // Calculate averages
    const result = Object.entries(groupedByTime).map(([timeKey, data]) => {
      const result = { timestamp: data.timestamp };
      
      // Calculate average for each visualization type
      Object.entries(data).forEach(([key, value]) => {
        if (key !== 'timestamp') {
          result[key] = value.count > 0 ? value.sum / value.count : 0;
        }
      });
      
      return result;
    });
    
    // Sort by timestamp
    return result.sort((a, b) => a.timestamp - b.timestamp);
  } catch (error) {
    logger.error('Error processing response time metrics:', error);
    return [];
  }
};

/**
 * Process error count metrics
 * @param {Array} metrics - Raw metrics
 * @returns {Array} - Processed error count metrics
 */
const processErrorCount = (metrics) => {
  try {
    // Group metrics by timestamp (rounded to minute)
    const groupedByTime = {};
    
    metrics.forEach(metric => {
      // Skip if not an error
      if (metric.statusCode < 400) return;
      
      // Round timestamp to minute
      const timestamp = new Date(metric.timestamp);
      timestamp.setSeconds(0, 0);
      const timeKey = timestamp.getTime();
      
      if (!groupedByTime[timeKey]) {
        groupedByTime[timeKey] = {
          timestamp: timeKey,
          triDomainTensor: 0,
          harmonyIndex: 0,
          riskControlFusion: 0,
          resonanceSpectrogram: 0,
          unifiedComplianceSecurity: 0
        };
      }
      
      // Increment count for visualization type
      const visualizationType = metric.visualizationType || 'unknown';
      if (groupedByTime[timeKey][visualizationType] !== undefined) {
        groupedByTime[timeKey][visualizationType]++;
      }
    });
    
    // Convert to array and sort by timestamp
    return Object.values(groupedByTime).sort((a, b) => a.timestamp - b.timestamp);
  } catch (error) {
    logger.error('Error processing error count metrics:', error);
    return [];
  }
};

/**
 * Process data size metrics
 * @param {Array} metrics - Raw metrics
 * @returns {Array} - Processed data size metrics
 */
const processDataSize = (metrics) => {
  try {
    // Group metrics by timestamp (rounded to minute)
    const groupedByTime = {};
    
    metrics.forEach(metric => {
      // Skip if no data size
      if (metric.dataSize === undefined) return;
      
      // Round timestamp to minute
      const timestamp = new Date(metric.timestamp);
      timestamp.setSeconds(0, 0);
      const timeKey = timestamp.getTime();
      
      if (!groupedByTime[timeKey]) {
        groupedByTime[timeKey] = {
          timestamp: timeKey,
          triDomainTensor: { sum: 0, count: 0 },
          harmonyIndex: { sum: 0, count: 0 },
          riskControlFusion: { sum: 0, count: 0 },
          resonanceSpectrogram: { sum: 0, count: 0 },
          unifiedComplianceSecurity: { sum: 0, count: 0 }
        };
      }
      
      // Add data size for visualization type
      const visualizationType = metric.visualizationType || 'unknown';
      if (groupedByTime[timeKey][visualizationType]) {
        groupedByTime[timeKey][visualizationType].sum += metric.dataSize;
        groupedByTime[timeKey][visualizationType].count++;
      }
    });
    
    // Calculate averages
    const result = Object.entries(groupedByTime).map(([timeKey, data]) => {
      const result = { timestamp: data.timestamp };
      
      // Calculate average for each visualization type
      Object.entries(data).forEach(([key, value]) => {
        if (key !== 'timestamp') {
          result[key] = value.count > 0 ? value.sum / value.count : 0;
        }
      });
      
      return result;
    });
    
    // Sort by timestamp
    return result.sort((a, b) => a.timestamp - b.timestamp);
  } catch (error) {
    logger.error('Error processing data size metrics:', error);
    return [];
  }
};

/**
 * Process cache hit rate metrics
 * @param {Array} metrics - Raw metrics
 * @returns {Array} - Processed cache hit rate metrics
 */
const processCacheHitRate = (metrics) => {
  try {
    // Count cache hits and misses
    let hits = 0;
    let misses = 0;
    
    metrics.forEach(metric => {
      if (metric.cacheHit !== undefined) {
        if (metric.cacheHit) {
          hits++;
        } else {
          misses++;
        }
      }
    });
    
    // Calculate hit rate
    const total = hits + misses;
    const hitRate = total > 0 ? hits / total : 0;
    const missRate = total > 0 ? misses / total : 0;
    
    // Return data for pie chart
    return [
      { name: 'Hit', value: hitRate },
      { name: 'Miss', value: missRate }
    ];
  } catch (error) {
    logger.error('Error processing cache hit rate metrics:', error);
    return [
      { name: 'Hit', value: 0 },
      { name: 'Miss', value: 0 }
    ];
  }
};

module.exports = {
  getMetrics,
  getAlerts
};
