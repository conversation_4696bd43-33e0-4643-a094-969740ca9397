# NovaConnect Monitoring Infrastructure

This document describes the monitoring infrastructure for NovaConnect UAC, which provides comprehensive observability for the system.

## Overview

NovaConnect's monitoring infrastructure consists of the following components:

1. **Prometheus Metrics** - Collects and exposes metrics for monitoring and alerting
2. **OpenTelemetry Tracing** - Provides distributed tracing for request flows
3. **Google Cloud Operations Integration** - Integrates with Google Cloud Monitoring and Logging
4. **Health Checks** - Provides endpoints for system health monitoring

## Metrics

NovaConnect exposes the following metrics:

### HTTP Metrics

- `novaconnect_http_requests_total` - Total number of HTTP requests
- `novaconnect_http_request_duration_seconds` - HTTP request duration in seconds
- `novaconnect_api_errors_total` - Total number of API errors

### Connector Metrics

- `novaconnect_connector_requests_total` - Total number of connector requests
- `novaconnect_connector_request_duration_seconds` - Connector request duration in seconds
- `novaconnect_connector_health` - Connector health status (1 = healthy, 0 = unhealthy)
- `novaconnect_connector_response_time` - Connector response time in milliseconds

### Data Processing Metrics

- `novaconnect_data_normalization_duration_seconds` - Data normalization duration in seconds
- `novaconnect_remediation_workflows_total` - Total number of remediation workflows
- `novaconnect_remediation_workflow_duration_seconds` - Remediation workflow duration in seconds

### System Metrics

- `novaconnect_active_connections` - Number of active connections
- `novaconnect_rate_limit_exceeded_total` - Total number of rate limit exceeded events
- `novaconnect_authentication_success_total` - Total number of successful authentications
- `novaconnect_authentication_failure_total` - Total number of failed authentications
- `novaconnect_cache_hits_total` - Total number of cache hits
- `novaconnect_cache_misses_total` - Total number of cache misses

## Tracing

NovaConnect uses OpenTelemetry for distributed tracing, which provides:

- End-to-end request tracing
- Detailed performance insights
- Error tracking and correlation
- Service dependency mapping

Traces can be exported to:

- Google Cloud Trace
- Zipkin
- Console (for development)

## Google Cloud Operations Integration

NovaConnect integrates with Google Cloud Operations for:

- Metrics visualization and alerting
- Log aggregation and analysis
- Distributed tracing
- Custom dashboards

### Custom Dashboards

The following dashboards are available:

- **NovaConnect Overview** - High-level system metrics
- **Connector Performance** - Detailed connector metrics
- **Error Analysis** - Error rates and patterns
- **Data Processing** - Normalization and remediation metrics

### Alert Policies

The following alert policies are configured:

- **High Error Rate** - Alerts when error rate exceeds 5%
- **High Response Time** - Alerts when average response time exceeds 1 second
- **Connector Health** - Alerts when connectors become unhealthy
- **System Resource Usage** - Alerts on high CPU, memory, or disk usage

## Health Check Endpoints

NovaConnect provides the following health check endpoints:

- `/health` - Basic health check
- `/health/detailed` - Detailed health information
- `/health/ready` - Readiness check for load balancers
- `/metrics` - Prometheus metrics endpoint

## Configuration

Monitoring can be configured using the following environment variables:

```
# Monitoring
METRICS_ENABLED=true
TRACING_ENABLED=true
TRACING_EXPORTER=console  # Options: console, zipkin, gcp
ZIPKIN_URL=http://localhost:9411/api/v2/spans

# Google Cloud
GOOGLE_CLOUD_PROJECT=your-gcp-project-id
GOOGLE_CLOUD_MONITORING_ENABLED=false
GOOGLE_APPLICATION_CREDENTIALS=path/to/your/credentials.json
```

## Integration with Google Cloud Marketplace

For Google Cloud Marketplace deployment, the monitoring infrastructure automatically integrates with Google Cloud Operations, providing:

1. **Seamless Monitoring** - Automatic integration with Google Cloud Monitoring
2. **Log Analysis** - Structured logs in Google Cloud Logging
3. **Trace Visualization** - Distributed traces in Google Cloud Trace
4. **Custom Dashboards** - Pre-configured dashboards for NovaConnect
5. **Alert Notifications** - Configurable alerts based on metrics and logs

This integration ensures that NovaConnect meets the observability requirements for Google Cloud Marketplace.
