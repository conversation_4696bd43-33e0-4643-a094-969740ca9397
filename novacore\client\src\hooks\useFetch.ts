/**
 * useFetch Hook
 * 
 * A custom hook for data fetching with built-in loading and error handling.
 * It provides a consistent way to handle API requests across the application.
 */

import { useState, useEffect, useCallback } from 'react';

interface UseFetchState<T> {
  data: T | null;
  loading: boolean;
  error: Error | null;
}

interface UseFetchOptions {
  immediate?: boolean;
  dependencies?: any[];
  onSuccess?: (data: any) => void;
  onError?: (error: Error) => void;
}

/**
 * Custom hook for data fetching with loading and error states
 * @param fetchFn The async function to fetch data
 * @param options Configuration options
 * @returns Object containing data, loading state, error, and refetch function
 */
function useFetch<T>(
  fetchFn: () => Promise<T>,
  options: UseFetchOptions = {}
) {
  const { immediate = true, dependencies = [], onSuccess, onError } = options;
  
  const [state, setState] = useState<UseFetchState<T>>({
    data: null,
    loading: immediate,
    error: null,
  });

  const execute = useCallback(async () => {
    setState(prev => ({ ...prev, loading: true, error: null }));
    
    try {
      const data = await fetchFn();
      setState({ data, loading: false, error: null });
      
      if (onSuccess) {
        onSuccess(data);
      }
      
      return data;
    } catch (error) {
      const errorObj = error instanceof Error ? error : new Error('An unknown error occurred');
      setState({ data: null, loading: false, error: errorObj });
      
      if (onError) {
        onError(errorObj);
      }
      
      return null;
    }
  }, [fetchFn, onSuccess, onError]);

  useEffect(() => {
    if (immediate) {
      execute();
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [...dependencies]);

  return {
    ...state,
    refetch: execute,
  };
}

export default useFetch;
