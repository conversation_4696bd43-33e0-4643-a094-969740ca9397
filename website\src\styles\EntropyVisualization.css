/* Main container */
.entropy-visualization {
  background: #0f172a; /* Dark theme background */
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  padding: 1.5rem;
  height: 100%;
  display: flex;
  flex-direction: column;
  color: #e2e8f0;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

/* Fullscreen mode */
.entropy-visualization.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  border-radius: 0;
  padding: 1.5rem;
  overflow-y: auto;
}

/* Container for 3D visualization */
.quantum-3d-container {
  flex: 1;
  min-height: 400px;
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  background: #0a0f1f;
  box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
}

/* Quantum canvas */
.quantum-canvas {
  width: 100%;
  height: 100%;
  outline: none;
  background: transparent;
}

/* Quantum controls */
.quantum-controls {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  padding: 1rem;
  background: rgba(15, 23, 42, 0.7);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.control-group label {
  font-size: 0.875rem;
  color: #94a3b8;
  display: flex;
  justify-content: space-between;
}

.control-group input[type="range"] {
  -webkit-appearance: none;
  width: 100%;
  height: 4px;
  border-radius: 2px;
  background: #1e293b;
  outline: none;
  opacity: 0.7;
  transition: opacity 0.2s;
}

.control-group input[type="range"]:hover {
  opacity: 1;
}

.control-group input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #8b5cf6;
  cursor: pointer;
  transition: all 0.2s;
}

.control-group input[type="range"]::-webkit-slider-thumb:hover {
  transform: scale(1.2);
  box-shadow: 0 0 0 4px rgba(139, 92, 246, 0.2);
}

/* Visualization header */
.visualization-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  flex-wrap: wrap;
  gap: 1rem;
}

.visualization-header h3 {
  margin: 0;
  color: #e2e8f0;
  font-weight: 600;
  font-size: 1.25rem;
  letter-spacing: 0.5px;
}

.visualization-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

/* Quantum stats */
.quantum-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin: 1.5rem 0;
}

.stat-card {
  background: rgba(15, 23, 42, 0.7);
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  border-color: rgba(139, 92, 246, 0.3);
}

.stat-label {
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  color: #94a3b8;
  margin-bottom: 0.5rem;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 600;
  color: #e2e8f0;
  margin-bottom: 0.25rem;
  font-family: 'Fira Code', monospace;
}

.stat-value.quantum {
  color: #8b5cf6;
  background: linear-gradient(90deg, #8b5cf6, #c4b5fd);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.stat-description {
  font-size: 0.75rem;
  color: #94a3b8;
  line-height: 1.4;
}

/* Quantum actions */
.quantum-actions {
  display: flex;
  gap: 0.75rem;
  margin-top: 1rem;
  justify-content: flex-end;
}

/* Mode indicator */
.mode-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #94a3b8;
  background: rgba(255, 255, 255, 0.05);
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
}

.status-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  display: inline-block;
}

.status-dot.quantum {
  background: #8b5cf6;
  box-shadow: 0 0 10px #8b5cf6;
}

.status-dot.classical {
  background: #4b5563;
  box-shadow: 0 0 10px #4b5563;
}

/* No data state */
.no-data {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 2rem;
  color: #94a3b8;
  background: rgba(15, 23, 42, 0.5);
  border: 2px dashed rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  margin: 1rem 0;
}

.no-data p {
  margin: 0.5rem 0;
  max-width: 400px;
}

.no-data p:first-child {
  font-size: 1.125rem;
  color: #e2e8f0;
  font-weight: 500;
}

/* Animations */
@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.spin {
  animation: spin 1s linear infinite;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .quantum-stats {
    grid-template-columns: 1fr 1fr;
  }
  
  .quantum-controls {
    grid-template-columns: 1fr;
  }
  
  .visualization-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .visualization-controls {
    width: 100%;
    justify-content: space-between;
  }
  
  .quantum-actions {
    justify-content: center;
  }
}

/* Dark theme overrides */
.entropy-visualization :global(.MuiToggleButton-root) {
  color: #94a3b8;
  border-color: #334155;
  background: #1e293b;
}

.entropy-visualization :global(.MuiToggleButton-root.Mui-selected) {
  color: #e2e8f0;
  background: #8b5cf6;
  border-color: #8b5cf6;
}

.entropy-visualization :global(.MuiToggleButton-root.Mui-selected:hover) {
  background: #7c3aed;
}

.entropy-visualization :global(.MuiToggleButton-root:hover) {
  background: #1e293b;
  color: #e2e8f0;
}

/* Button styles */
.entropy-visualization :global(.MuiButton-outlined) {
  color: #e2e8f0;
  border-color: #4b5563;
  text-transform: none;
  letter-spacing: 0.5px;
  font-weight: 500;
  transition: all 0.2s;
}

.entropy-visualization :global(.MuiButton-outlined:hover) {
  border-color: #8b5cf6;
  color: #8b5cf6;
  background: rgba(139, 92, 246, 0.1);
}

/* Tooltip styles */
.entropy-visualization :global(.MuiTooltip-tooltip) {
  background: #1e293b;
  color: #e2e8f0;
  font-size: 0.75rem;
  padding: 0.5rem 0.75rem;
  border: 1px solid #334155;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.entropy-visualization :global(.MuiTooltip-arrow) {
  color: #1e293b;
}

/* Custom scrollbar */
.entropy-visualization::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.entropy-visualization::-webkit-scrollbar-track {
  background: #0f172a;
}

.entropy-visualization::-webkit-scrollbar-thumb {
  background: #334155;
  border-radius: 4px;
}

.entropy-visualization::-webkit-scrollbar-thumb:hover {
  background: #475569;
}

/* Header section */
.visualization-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.entropy-visualization h3 {
  margin: 0;
  color: #e2e8f0;
  font-weight: 600;
  font-size: 1.25rem;
  letter-spacing: 0.5px;
}

/* Coherence indicator */
.coherence-indicator {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: rgba(255, 255, 255, 0.05);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.coherence-indicator .label {
  font-size: 0.875rem;
  color: #94a3b8;
}

.coherence-indicator .value {
  font-family: 'Fira Code', monospace;
  font-size: 1rem;
  font-weight: 500;
  color: #60a5fa;
  min-width: 60px;
  text-align: right;
}

.coherence-bar {
  height: 6px;
  width: 100px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  overflow: hidden;
  position: relative;
}

.coherence-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: var(--coherence-level, 0%);
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
  transition: width 0.5s ease-out;
}

/* 3D Canvas Container */
.quantum-canvas-container {
  flex: 1;
  min-height: 400px;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  background: #0a0f1f;
  box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.5);
}

.quantum-canvas {
  width: 100%;
  height: 100%;
  outline: none;
}

/* Stats section */
.visualization-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.stat {
  background: rgba(255, 255, 255, 0.03);
  padding: 1rem;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  transition: transform 0.2s ease, background 0.2s ease;
}

.stat:hover {
  transform: translateY(-2px);
  background: rgba(255, 255, 255, 0.05);
}

.stat .label {
  font-size: 0.8125rem;
  color: #94a3b8;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat .value {
  font-size: 1.25rem;
  font-weight: 600;
  color: #e2e8f0;
  font-family: 'Fira Code', monospace;
}

/* No data state */
.no-data {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #94a3b8;
  z-index: 1;
}

.no-data p {
  margin: 0.5rem 0;
  font-size: 1rem;
}

.no-data p:first-child {
  font-size: 1.125rem;
  color: #e2e8f0;
  font-weight: 500;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .visualization-stats {
    grid-template-columns: 1fr 1fr;
  }
  
  .visualization-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .coherence-indicator {
    width: 100%;
    justify-content: space-between;
  }
}

/* Animation for quantum state changes */
@keyframes pulse {
  0% { opacity: 0.6; }
  50% { opacity: 1; }
  100% { opacity: 0.6; }
}

.quantum-active {
  animation: pulse 2s infinite ease-in-out;
}

.entropy-value .label {
  font-size: 0.9rem;
  color: #4a5568;
  font-weight: 500;
}

.entropy-value .value {
  font-size: 1.5rem;
  font-weight: 600;
  font-family: 'Courier New', monospace;
  color: #2c3e50;
}

.entropy-value .value.low {
  color: #38a169;
}

.entropy-value .value.medium {
  color: #d69e2e;
}

.entropy-value .value.high {
  color: #e53e3e;
}

.risk-level {
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-top: 0.25rem;
  display: inline-block;
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  background: #f0f0f0;
  align-self: flex-start;
}

.risk-level.low {
  background: #c6f6d5;
  color: #2f855a;
}

.risk-level.medium {
  background: #feebc8;
  color: #b7791f;
}

.risk-level.high {
  background: #fed7d7;
  color: #c53030;
}

.legend {
  display: flex;
  gap: 1.5rem;
  margin-top: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.85rem;
  color: #4a5568;
}

.legend-item .dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.legend-item .dot.quantum {
  background: #8a2be2;
  box-shadow: 0 0 0 2px rgba(138, 43, 226, 0.3);
}

.legend-item .dot.classical {
  background: #4a5568;
}

.no-data {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #a0aec0;
  font-style: italic;
}

.loading-indicator {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #4a5568;
  gap: 1rem;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(138, 43, 226, 0.1);
  border-radius: 50%;
  border-top-color: #8a2be2;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Tooltip customization */
.recharts-tooltip-wrapper {
  background: #fff !important;
  border: 1px solid #e2e8f0 !important;
  border-radius: 6px !important;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
  padding: 0.75rem !important;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

.recharts-tooltip-label {
  color: #8a2be2 !important;
  font-weight: 600 !important;
  margin-bottom: 0.5rem !important;
  font-size: 0.9rem !important;
}

.recharts-tooltip-item {
  font-size: 0.85rem !important;
  color: #4a5568 !important;
}

.recharts-tooltip-item:before {
  background-color: #8a2be2 !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .entropy-visualization {
    padding: 1rem;
  }
  
  .entropy-values {
    flex-direction: column;
  }
  
  .entropy-value {
    width: 100%;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .entropy-visualization {
    background: #2d3748;
    color: #e2e8f0;
  }
  
  .entropy-visualization h3 {
    color: #e2e8f0;
    border-bottom-color: #4a5568;
  }
  
  .entropy-value {
    background: #2d3748;
    border-left-color: #9f7aea;
    color: #e2e8f0;
  }
  
  .entropy-value .value {
    color: #e2e8f0;
  }
  
  .risk-level {
    background: #4a5568;
  }
  
  .legend-item {
    color: #a0aec0;
  }
  
  .no-data {
    color: #a0aec0;
  }
  
  .recharts-tooltip-wrapper {
    background: #2d3748 !important;
    border-color: #4a5568 !important;
  }
  
  .recharts-tooltip-label {
    color: #9f7aea !important;
  }
  
  .recharts-tooltip-item {
    color: #e2e8f0 !important;
  }
}
