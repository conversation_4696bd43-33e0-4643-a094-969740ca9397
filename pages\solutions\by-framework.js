import React from 'react';
import Link from 'next/link';
import PageWithSidebar from '../../components/PageWithSidebar';

const SolutionsByFramework = () => {
  const sidebarItems = [
    { label: 'SOC 2', href: '#soc2' },
    { label: 'HIPAA', href: '#hipaa' },
    { label: 'GDPR', href: '#gdpr' },
    { label: 'ISO 27001', href: '#iso27001' },
    { label: 'PCI DSS', href: '#pcidss' },
    { label: 'Back to Solutions', href: '/solutions' },
  ];

  return (
    <PageWithSidebar title="Solutions by Compliance Framework" sidebarItems={sidebarItems}>
      <div className="space-y-12">
        {/* Hero Section */}
        <div className="bg-blue-900 p-8 rounded-lg shadow-lg">
          <h1 className="text-3xl font-bold mb-4">Compliance Framework Solutions</h1>
          <p className="text-xl mb-6">
            NovaFuse provides specialized solutions for major compliance frameworks, helping you achieve and maintain compliance efficiently.
          </p>
        </div>

        {/* SOC 2 Section */}
        <div id="soc2" className="scroll-mt-20">
          <h2 className="text-2xl font-bold mb-6">SOC 2</h2>
          <div className="bg-secondary p-6 rounded-lg shadow-md">
            <p className="mb-4">
              Our SOC 2 compliance solution helps technology and SaaS companies achieve and maintain SOC 2 compliance with minimal effort.
            </p>
            
            <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-blue-900 p-4 rounded-lg">
                <h3 className="text-xl font-bold mb-3">Key Features</h3>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Pre-mapped SOC 2 controls</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Automated evidence collection</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Control implementation guidance</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Readiness assessments</span>
                  </li>
                </ul>
              </div>
              
              <div className="bg-blue-900 p-4 rounded-lg">
                <h3 className="text-xl font-bold mb-3">Trust Service Criteria</h3>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Security</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Availability</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Processing Integrity</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Confidentiality</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Privacy</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* HIPAA Section */}
        <div id="hipaa" className="scroll-mt-20">
          <h2 className="text-2xl font-bold mb-6">HIPAA</h2>
          <div className="bg-secondary p-6 rounded-lg shadow-md">
            <p className="mb-4">
              Our HIPAA compliance solution helps healthcare organizations and their business associates meet HIPAA Security, Privacy, and Breach Notification Rules.
            </p>
            
            <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-blue-900 p-4 rounded-lg">
                <h3 className="text-xl font-bold mb-3">Key Features</h3>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>PHI data mapping</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Security risk assessments</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Business associate management</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Breach notification workflows</span>
                  </li>
                </ul>
              </div>
              
              <div className="bg-blue-900 p-4 rounded-lg">
                <h3 className="text-xl font-bold mb-3">HIPAA Rules Covered</h3>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Security Rule</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Privacy Rule</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Breach Notification Rule</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>HITECH Act Requirements</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* GDPR Section */}
        <div id="gdpr" className="scroll-mt-20">
          <h2 className="text-2xl font-bold mb-6">GDPR</h2>
          <div className="bg-secondary p-6 rounded-lg shadow-md">
            <p className="mb-4">
              Our GDPR compliance solution helps organizations meet the requirements of the EU's General Data Protection Regulation.
            </p>
            
            <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-blue-900 p-4 rounded-lg">
                <h3 className="text-xl font-bold mb-3">Key Features</h3>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Data mapping and inventory</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>DPIA automation</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>DSR management</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Breach notification workflows</span>
                  </li>
                </ul>
              </div>
              
              <div className="bg-blue-900 p-4 rounded-lg">
                <h3 className="text-xl font-bold mb-3">GDPR Principles Covered</h3>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Lawfulness, fairness, and transparency</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Purpose limitation</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Data minimization</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Accuracy</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Storage limitation</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Integrity and confidentiality</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* More frameworks can be added here */}

        {/* CTA Section */}
        <div className="bg-gradient-to-r from-blue-900 to-purple-900 p-8 rounded-lg shadow-lg border border-blue-400">
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-4">Need Support for a Specific Framework?</h2>
            <p className="mb-6">
              Contact us to discuss how NovaFuse can help you achieve and maintain compliance with your required frameworks.
            </p>
            <div className="flex flex-wrap justify-center gap-4">
              <Link href="/contact" className="bg-gradient-to-r from-blue-700 to-purple-700 text-white px-6 py-3 rounded-lg font-bold hover:from-blue-800 hover:to-purple-800 inline-block shadow-md hover:shadow-lg transform hover:-translate-y-1 transition-all duration-200 border border-blue-400">
                Contact Us
              </Link>
              <Link href="/api-docs" className="bg-white text-blue-700 px-6 py-3 rounded-lg font-bold hover:bg-gray-100 inline-block shadow-md hover:shadow-lg transform hover:-translate-y-1 transition-all duration-200 border border-blue-400">
                View API Documentation
              </Link>
            </div>
          </div>
        </div>
      </div>
    </PageWithSidebar>
  );
};

export default SolutionsByFramework;
