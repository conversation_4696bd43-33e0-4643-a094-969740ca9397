/**
 * useNovaAssureApi Hook
 * 
 * A custom hook that provides access to the NovaAssure API with built-in
 * error handling, loading states, and authentication integration.
 */

import { useState, useEffect, useCallback } from 'react';
import { NovaAssureAPI } from '../api/novaAssureApi';
import { useAuth } from '../contexts/AuthContext';

// Create a singleton instance of the API
const api = new NovaAssureAPI();

/**
 * Hook for accessing the NovaAssure API
 * @returns The NovaAssure API instance and authentication state
 */
function useNovaAssureApi() {
  const auth = useAuth();
  const [isReady, setIsReady] = useState(false);

  // Initialize the API when auth state is available
  useEffect(() => {
    if (!auth.loading) {
      setIsReady(true);
    }
  }, [auth.loading]);

  /**
   * Wrapper for API calls that handles authentication and errors
   * @param apiCall Function that makes the API call
   * @returns Promise with the API response
   */
  const callApi = useCallback(async <T>(
    apiCall: () => Promise<T>
  ): Promise<T> => {
    if (!auth.isAuthenticated) {
      throw new Error('User is not authenticated');
    }

    try {
      return await apiCall();
    } catch (error) {
      // If the error is related to authentication, redirect to login
      if (error instanceof Error && 
          (error.message.includes('authentication') || 
           error.message.includes('unauthorized'))) {
        auth.logout();
      }
      throw error;
    }
  }, [auth]);

  return {
    api,
    isReady,
    isAuthenticated: auth.isAuthenticated,
    callApi
  };
}

export default useNovaAssureApi;
