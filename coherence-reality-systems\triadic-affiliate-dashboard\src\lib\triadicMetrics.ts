export interface TriadicMetrics {
  psi: number
  phi: number
  kappa: number
}

export function calculateTriadicMetrics(
  conversions: {
    revenue: number
    status: 'pending' | 'completed' | 'failed'
  }[],
  products: {
    price: number
    status: 'active' | 'inactive'
    conversions: number
  }[]
): TriadicMetrics {
  // Calculate base metrics
  const totalRevenue = conversions.reduce((sum, c) => sum + c.revenue, 0)
  const completedConversions = conversions.filter(c => c.status === 'completed').length
  const totalConversions = conversions.length
  const activeProducts = products.filter(p => p.status === 'active').length
  const totalProducts = products.length

  // Calculate ratios
  const conversionRate = totalConversions > 0 ? completedConversions / totalConversions : 0
  const avgRevenuePerConversion = totalConversions > 0 ? totalRevenue / completedConversions : 0
  const activeProductRatio = totalProducts > 0 ? activeProducts / totalProducts : 0

  // Calculate indices
  const psi = ((conversionRate * 0.4) + (avgRevenuePerConversion * 0.4) + (activeProductRatio * 0.2)) * 100
  const phi = ((conversionRate * 0.5) + (avgRevenuePerConversion * 0.3) + (activeProductRatio * 0.2)) * 100
  const kappa = ((avgRevenuePerConversion * 0.6) + (activeProductRatio * 0.4)) * 100

  return {
    psi: Math.round(psi * 100) / 100,
    phi: Math.round(phi * 100) / 100,
    kappa: Math.round(kappa * 100) / 100
  }
}

export function getTriadicColor(metric: number): string {
  if (metric >= 80) return 'bg-green-500'
  if (metric >= 60) return 'bg-yellow-500'
  return 'bg-red-500'
}

export function formatTriadicMetric(metric: number): string {
  return metric.toFixed(1) + '%'
}
