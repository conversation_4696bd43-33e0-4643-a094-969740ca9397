c2ab090efc635a0fec7b76a7355a7098
/**
 * NovaFuse Universal API Connector - Async Handler Tests
 * 
 * This module tests the async handler utilities for the UAC.
 */

const {
  asyncHandler,
  retryWithBackoff,
  circuitBreaker
} = require('../../src/utils/async-handler');
describe('Async Handler', () => {
  describe('asyncHandler', () => {
    it('should pass the result to the next middleware on success', async () => {
      const req = {};
      const res = {};
      const next = jest.fn();
      const handler = asyncHandler(async (req, res) => {
        return 'success';
      });
      await handler(req, res, next);
      expect(next).not.toHaveBeenCalled();
    });
    it('should pass the error to the next middleware on failure', async () => {
      const req = {};
      const res = {};
      const next = jest.fn();
      const error = new Error('Test error');
      const handler = asyncHandler(async (req, res) => {
        throw error;
      });
      await handler(req, res, next);
      expect(next).toHaveBeenCalledWith(error);
    });
  });
  describe('retryWithBackoff', () => {
    beforeEach(() => {
      jest.useFakeTimers();
    });
    afterEach(() => {
      jest.useRealTimers();
    });
    it('should return the result if the function succeeds on first try', async () => {
      const fn = jest.fn().mockResolvedValue('success');
      const promise = retryWithBackoff(fn);
      await expect(promise).resolves.toBe('success');
      expect(fn).toHaveBeenCalledTimes(1);
    });
    it('should retry the function if it fails', async () => {
      const error = new Error('Test error');
      const fn = jest.fn().mockRejectedValueOnce(error).mockResolvedValueOnce('success');
      const promise = retryWithBackoff(fn, {
        initialDelay: 100
      });

      // Fast-forward time to trigger retry
      jest.advanceTimersByTime(200);
      await expect(promise).resolves.toBe('success');
      expect(fn).toHaveBeenCalledTimes(2);
    });
    it('should throw the error if max retries is reached', async () => {
      const error = new Error('Test error');
      const fn = jest.fn().mockRejectedValue(error);
      const promise = retryWithBackoff(fn, {
        maxRetries: 2,
        initialDelay: 100
      });

      // Fast-forward time to trigger retries
      jest.advanceTimersByTime(300);
      await expect(promise).rejects.toThrow(error);
      expect(fn).toHaveBeenCalledTimes(3); // Initial + 2 retries
    });
    it('should respect shouldRetry function', async () => {
      const retryError = new Error('Retry error');
      const noRetryError = new Error('No retry error');
      const fn = jest.fn().mockRejectedValueOnce(retryError).mockRejectedValueOnce(noRetryError);
      const shouldRetry = jest.fn(error => error === retryError);
      const promise = retryWithBackoff(fn, {
        maxRetries: 2,
        initialDelay: 100,
        shouldRetry
      });

      // Fast-forward time to trigger retry
      jest.advanceTimersByTime(200);
      await expect(promise).rejects.toThrow(noRetryError);
      expect(fn).toHaveBeenCalledTimes(2);
      expect(shouldRetry).toHaveBeenCalledTimes(2);
    });
  });
  describe('circuitBreaker', () => {
    it('should pass through the result if the function succeeds', async () => {
      const fn = jest.fn().mockResolvedValue('success');
      const protectedFn = circuitBreaker(fn);
      await expect(protectedFn()).resolves.toBe('success');
      expect(fn).toHaveBeenCalledTimes(1);
    });
    it('should pass through the error if the function fails but threshold not reached', async () => {
      const error = new Error('Test error');
      const fn = jest.fn().mockRejectedValue(error);
      const protectedFn = circuitBreaker(fn, {
        failureThreshold: 2
      });
      await expect(protectedFn()).rejects.toThrow(error);
      expect(fn).toHaveBeenCalledTimes(1);
    });
    it('should open the circuit after threshold failures', async () => {
      const error = new Error('Test error');
      const fn = jest.fn().mockRejectedValue(error);
      const protectedFn = circuitBreaker(fn, {
        failureThreshold: 2
      });

      // First failure
      await expect(protectedFn()).rejects.toThrow(error);

      // Second failure - should open the circuit
      await expect(protectedFn()).rejects.toThrow(error);

      // Third call - should fail with circuit open error
      await expect(protectedFn()).rejects.toThrow('Circuit is open');
      expect(fn).toHaveBeenCalledTimes(2);
    });
    it('should reset failures after a successful call', async () => {
      const error = new Error('Test error');
      const fn = jest.fn().mockRejectedValueOnce(error).mockResolvedValueOnce('success').mockRejectedValueOnce(error);
      const protectedFn = circuitBreaker(fn, {
        failureThreshold: 2
      });

      // First failure
      await expect(protectedFn()).rejects.toThrow(error);

      // Success - should reset failures
      await expect(protectedFn()).resolves.toBe('success');

      // Another failure - should not open the circuit yet
      await expect(protectedFn()).rejects.toThrow(error);
      expect(fn).toHaveBeenCalledTimes(3);
    });
    it('should respect isFailure function', async () => {
      const retryError = new Error('Retry error');
      const noRetryError = new Error('No retry error');
      const fn = jest.fn().mockRejectedValueOnce(retryError).mockRejectedValueOnce(retryError).mockRejectedValueOnce(noRetryError);
      const isFailure = jest.fn(error => error === retryError);
      const protectedFn = circuitBreaker(fn, {
        failureThreshold: 2,
        isFailure
      });

      // First failure (counts)
      await expect(protectedFn()).rejects.toThrow(retryError);

      // Second failure (counts) - should open the circuit
      await expect(protectedFn()).rejects.toThrow(retryError);

      // Third call - should fail with circuit open error
      await expect(protectedFn()).rejects.toThrow('Circuit is open');
      expect(fn).toHaveBeenCalledTimes(2);
      expect(isFailure).toHaveBeenCalledTimes(2);
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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