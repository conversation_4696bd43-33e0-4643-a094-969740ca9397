const request = require('supertest');
const express = require('express');
const router = require('../../../../apis/security/assessment/routes');
const models = require('../../../../apis/security/assessment/models');

// Create a test Express app
const app = express();
app.use(express.json());
app.use('/security/assessment', router);

// Generate a large dataset for performance testing
const generateLargeAssessmentDataset = (count) => {
  const assessments = [];
  for (let i = 0; i < count; i++) {
    assessments.push({
      id: `sa-${i.toString().padStart(8, '0')}`,
      name: `Test Assessment ${i}`,
      description: `Description for test assessment ${i}`,
      type: i % 3 === 0 ? 'internal' : i % 3 === 1 ? 'vendor' : 'application',
      status: i % 4 === 0 ? 'planned' : i % 4 === 1 ? 'in-progress' : i % 4 === 2 ? 'completed' : 'cancelled',
      scope: {
        systems: [`System ${i % 5}`, `System ${(i + 1) % 5}`],
        applications: [`App ${i % 3}`],
        networks: [`Network ${i % 2}`]
      },
      methodology: i % 3 === 0 ? 'NIST CSF' : i % 3 === 1 ? 'ISO 27001' : 'OWASP MASVS',
      startDate: '2023-01-01',
      endDate: i % 4 === 2 ? '2023-02-28' : null,
      findings: generateFindings(i, 1 + (i % 5)), // Each assessment has 1-5 findings
      assessors: [`Assessor ${i % 3}`, `Assessor ${(i + 1) % 3}`],
      stakeholders: [`Stakeholder ${i % 4}`],
      attachments: generateAttachments(i, i % 3), // Each assessment has 0-2 attachments
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    });
  }
  return assessments;
};

// Generate findings for an assessment
const generateFindings = (assessmentIndex, count) => {
  const findings = [];
  for (let i = 0; i < count; i++) {
    findings.push({
      id: `finding-${assessmentIndex}-${i}`,
      title: `Finding ${i} of Assessment ${assessmentIndex}`,
      description: `Description for finding ${i} of assessment ${assessmentIndex}`,
      severity: i % 3 === 0 ? 'high' : i % 3 === 1 ? 'medium' : 'low',
      category: i % 4 === 0 ? 'authentication' : i % 4 === 1 ? 'encryption' : i % 4 === 2 ? 'access-control' : 'configuration',
      status: i % 3 === 0 ? 'open' : i % 3 === 1 ? 'in-progress' : 'remediated',
      affectedSystems: [`System ${i % 5}`],
      remediationPlan: `Remediation plan for finding ${i}`,
      remediationDate: i % 3 === 2 ? '2023-03-15' : null,
      assignedTo: `Team ${i % 3}`
    });
  }
  return findings;
};

// Generate attachments for an assessment
const generateAttachments = (assessmentIndex, count) => {
  const attachments = [];
  for (let i = 0; i < count; i++) {
    attachments.push({
      id: `att-${assessmentIndex}-${i}`,
      name: `Attachment ${i} of Assessment ${assessmentIndex}`,
      type: i % 3 === 0 ? 'pdf' : i % 3 === 1 ? 'xlsx' : 'docx',
      url: `https://example.com/reports/assessment-${assessmentIndex}-attachment-${i}.${i % 3 === 0 ? 'pdf' : i % 3 === 1 ? 'xlsx' : 'docx'}`,
      uploadedBy: `User ${i % 3}`,
      uploadedAt: new Date().toISOString()
    });
  }
  return attachments;
};

// Generate assessment templates
const generateTemplates = (count) => {
  const templates = [];
  for (let i = 0; i < count; i++) {
    templates.push({
      id: `template-${i.toString().padStart(5, '0')}`,
      name: `Assessment Template ${i}`,
      description: `Description for assessment template ${i}`,
      type: i % 3 === 0 ? 'internal' : i % 3 === 1 ? 'vendor' : 'application',
      methodology: i % 3 === 0 ? 'NIST CSF' : i % 3 === 1 ? 'ISO 27001' : 'OWASP MASVS',
      sections: generateSections(i, 2 + (i % 3)), // Each template has 2-4 sections
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    });
  }
  return templates;
};

// Generate sections for a template
const generateSections = (templateIndex, count) => {
  const sections = [];
  for (let i = 0; i < count; i++) {
    sections.push({
      title: `Section ${i} of Template ${templateIndex}`,
      description: `Description for section ${i} of template ${templateIndex}`,
      questions: generateQuestions(templateIndex, i, 3 + (i % 5)) // Each section has 3-7 questions
    });
  }
  return sections;
};

// Generate questions for a section
const generateQuestions = (templateIndex, sectionIndex, count) => {
  const questions = [];
  for (let i = 0; i < count; i++) {
    questions.push({
      id: `q-${templateIndex}-${sectionIndex}-${i}`,
      text: `Question ${i} of Section ${sectionIndex} of Template ${templateIndex}`,
      type: i % 3 === 0 ? 'yes-no' : i % 3 === 1 ? 'text' : 'multiple-choice',
      required: i < 2 // First two questions are required
    });
  }
  return questions;
};

// Mock the models with a large dataset
const ASSESSMENT_COUNT = 500;
const TEMPLATE_COUNT = 50;

jest.mock('../../../../apis/security/assessment/models', () => {
  const assessments = generateLargeAssessmentDataset(ASSESSMENT_COUNT);
  const templates = generateTemplates(TEMPLATE_COUNT);
  return {
    securityAssessments: assessments,
    assessmentTemplates: templates
  };
});

describe('Security Assessment API Performance Tests', () => {
  // Set a longer timeout for performance tests
  jest.setTimeout(30000);

  describe('GET /security/assessment', () => {
    it('should handle pagination efficiently', async () => {
      const startTime = Date.now();
      
      const response = await request(app).get('/security/assessment?page=1&limit=20');
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveLength(20);
      
      // Response time should be under 200ms
      expect(responseTime).toBeLessThan(200);
      console.log(`Pagination response time: ${responseTime}ms`);
    });

    it('should handle filtering efficiently', async () => {
      const startTime = Date.now();
      
      const response = await request(app).get('/security/assessment?type=internal&status=completed');
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      
      // Response time should be under 200ms
      expect(responseTime).toBeLessThan(200);
      console.log(`Filtering response time: ${responseTime}ms`);
    });
  });

  describe('GET /security/assessment/:id', () => {
    it('should retrieve a specific assessment efficiently', async () => {
      const startTime = Date.now();
      
      const response = await request(app).get('/security/assessment/sa-00000050');
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data.id).toBe('sa-00000050');
      
      // Response time should be under 100ms
      expect(responseTime).toBeLessThan(100);
      console.log(`Get assessment response time: ${responseTime}ms`);
    });
  });

  describe('GET /security/assessment/:id/findings', () => {
    it('should retrieve findings for an assessment efficiently', async () => {
      // Find an assessment with multiple findings
      const assessmentWithFindings = models.securityAssessments.find(a => a.findings.length > 2);
      
      const startTime = Date.now();
      
      const response = await request(app).get(`/security/assessment/${assessmentWithFindings.id}/findings`);
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data.length).toBeGreaterThan(2);
      
      // Response time should be under 100ms
      expect(responseTime).toBeLessThan(100);
      console.log(`Get findings response time: ${responseTime}ms`);
    });
  });

  describe('GET /security/assessment/:id/attachments', () => {
    it('should retrieve attachments for an assessment efficiently', async () => {
      // Find an assessment with attachments
      const assessmentWithAttachments = models.securityAssessments.find(a => a.attachments.length > 0);
      
      const startTime = Date.now();
      
      const response = await request(app).get(`/security/assessment/${assessmentWithAttachments.id}/attachments`);
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data.length).toBeGreaterThan(0);
      
      // Response time should be under 100ms
      expect(responseTime).toBeLessThan(100);
      console.log(`Get attachments response time: ${responseTime}ms`);
    });
  });

  describe('GET /security/assessment/templates', () => {
    it('should retrieve assessment templates efficiently', async () => {
      const startTime = Date.now();
      
      const response = await request(app).get('/security/assessment/templates');
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data.length).toBe(TEMPLATE_COUNT);
      
      // Response time should be under 150ms
      expect(responseTime).toBeLessThan(150);
      console.log(`Get templates response time: ${responseTime}ms`);
    });
  });

  describe('POST /security/assessment', () => {
    it('should create a new assessment efficiently', async () => {
      const newAssessment = {
        name: 'Performance Test Assessment',
        description: 'Assessment for performance testing',
        type: 'internal',
        status: 'planned',
        scope: {
          systems: ['Test System'],
          applications: ['Test App'],
          networks: ['Test Network']
        },
        methodology: 'NIST CSF',
        startDate: '2023-06-01',
        assessors: ['Test Assessor'],
        stakeholders: ['Test Stakeholder']
      };

      const startTime = Date.now();
      
      const response = await request(app)
        .post('/security/assessment')
        .send(newAssessment);
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('data');
      
      // Response time should be under 150ms
      expect(responseTime).toBeLessThan(150);
      console.log(`Create assessment response time: ${responseTime}ms`);
    });
  });

  describe('POST /security/assessment/:id/findings', () => {
    it('should add a new finding efficiently', async () => {
      const newFinding = {
        title: 'Performance Test Finding',
        description: 'Finding for performance testing',
        severity: 'high',
        category: 'authentication',
        status: 'open',
        affectedSystems: ['Test System'],
        remediationPlan: 'Test remediation plan',
        assignedTo: 'Test Team'
      };

      const startTime = Date.now();
      
      const response = await request(app)
        .post('/security/assessment/sa-00000025/findings')
        .send(newFinding);
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('data');
      
      // Response time should be under 150ms
      expect(responseTime).toBeLessThan(150);
      console.log(`Add finding response time: ${responseTime}ms`);
    });
  });

  describe('POST /security/assessment/:id/attachments', () => {
    it('should add a new attachment efficiently', async () => {
      const newAttachment = {
        name: 'Performance Test Attachment',
        type: 'pdf',
        url: 'https://example.com/reports/performance-test.pdf'
      };

      const startTime = Date.now();
      
      const response = await request(app)
        .post('/security/assessment/sa-00000025/attachments')
        .send(newAttachment);
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('data');
      
      // Response time should be under 150ms
      expect(responseTime).toBeLessThan(150);
      console.log(`Add attachment response time: ${responseTime}ms`);
    });
  });

  describe('POST /security/assessment/templates', () => {
    it('should create a new template efficiently', async () => {
      const newTemplate = {
        name: 'Performance Test Template',
        description: 'Template for performance testing',
        type: 'internal',
        methodology: 'NIST CSF',
        sections: [
          {
            title: 'Test Section',
            description: 'Description for test section',
            questions: [
              {
                text: 'Test question 1',
                type: 'yes-no',
                required: true
              },
              {
                text: 'Test question 2',
                type: 'text',
                required: false
              }
            ]
          }
        ]
      };

      const startTime = Date.now();
      
      const response = await request(app)
        .post('/security/assessment/templates')
        .send(newTemplate);
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('data');
      
      // Response time should be under 150ms
      expect(responseTime).toBeLessThan(150);
      console.log(`Create template response time: ${responseTime}ms`);
    });
  });

  describe('Concurrent requests', () => {
    it('should handle multiple concurrent requests efficiently', async () => {
      const startTime = Date.now();
      
      // Make 10 concurrent requests
      const requests = [];
      for (let i = 0; i < 10; i++) {
        requests.push(request(app).get(`/security/assessment?page=${i+1}&limit=10`));
      }
      
      const responses = await Promise.all(requests);
      
      const endTime = Date.now();
      const totalResponseTime = endTime - startTime;
      
      // All responses should be successful
      responses.forEach(response => {
        expect(response.status).toBe(200);
        expect(response.body).toHaveProperty('data');
      });
      
      // Total response time for 10 concurrent requests should be under 1000ms
      expect(totalResponseTime).toBeLessThan(1000);
      console.log(`Concurrent requests response time: ${totalResponseTime}ms`);
    });

    it('should handle concurrent read and write operations efficiently', async () => {
      const startTime = Date.now();
      
      // Create a mix of read and write operations
      const requests = [];
      
      // Read operations
      requests.push(request(app).get('/security/assessment?page=1&limit=10'));
      requests.push(request(app).get('/security/assessment/sa-00000005'));
      requests.push(request(app).get('/security/assessment/sa-00000005/findings'));
      requests.push(request(app).get('/security/assessment/templates'));
      
      // Write operations
      const newAssessment = {
        name: 'Concurrent Test Assessment',
        description: 'Assessment for concurrent testing',
        type: 'internal',
        status: 'planned',
        scope: {
          systems: ['Test System'],
          applications: ['Test App'],
          networks: ['Test Network']
        },
        methodology: 'NIST CSF',
        startDate: '2023-06-01',
        assessors: ['Test Assessor'],
        stakeholders: ['Test Stakeholder']
      };
      
      const newFinding = {
        title: 'Concurrent Test Finding',
        description: 'Finding for concurrent testing',
        severity: 'high',
        category: 'authentication',
        status: 'open',
        affectedSystems: ['Test System'],
        remediationPlan: 'Test remediation plan',
        assignedTo: 'Test Team'
      };
      
      requests.push(request(app).post('/security/assessment').send(newAssessment));
      requests.push(request(app).post('/security/assessment/sa-00000010/findings').send(newFinding));
      
      const responses = await Promise.all(requests);
      
      const endTime = Date.now();
      const totalResponseTime = endTime - startTime;
      
      // All responses should be successful
      responses.forEach(response => {
        expect(response.status).toBe(200) || expect(response.status).toBe(201);
      });
      
      // Total response time for mixed operations should be under 1000ms
      expect(totalResponseTime).toBeLessThan(1000);
      console.log(`Mixed operations response time: ${totalResponseTime}ms`);
    });
  });

  describe('Load testing', () => {
    it('should handle a large number of sequential requests', async () => {
      const requestCount = 50;
      const startTime = Date.now();
      
      // Make sequential requests
      for (let i = 0; i < requestCount; i++) {
        const response = await request(app).get(`/security/assessment?page=1&limit=5`);
        expect(response.status).toBe(200);
      }
      
      const endTime = Date.now();
      const totalResponseTime = endTime - startTime;
      const averageResponseTime = totalResponseTime / requestCount;
      
      // Average response time should be under 20ms
      expect(averageResponseTime).toBeLessThan(20);
      console.log(`Sequential requests average response time: ${averageResponseTime}ms`);
    });
  });

  describe('Performance with large datasets', () => {
    it('should handle retrieving an assessment with many findings efficiently', async () => {
      // Find an assessment with many findings
      const assessmentWithManyFindings = models.securityAssessments.find(a => a.findings.length > 4);
      
      const startTime = Date.now();
      
      const response = await request(app).get(`/security/assessment/${assessmentWithManyFindings.id}`);
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data.findings.length).toBeGreaterThan(4);
      
      // Response time should be under 150ms even with many findings
      expect(responseTime).toBeLessThan(150);
      console.log(`Assessment with many findings response time: ${responseTime}ms`);
    });

    it('should handle retrieving a template with many sections efficiently', async () => {
      // Find a template with many sections
      const templateWithManySections = models.assessmentTemplates.find(t => t.sections.length > 3);
      
      const startTime = Date.now();
      
      const response = await request(app).get(`/security/assessment/templates/${templateWithManySections.id}`);
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data.sections.length).toBeGreaterThan(3);
      
      // Response time should be under 150ms even with many sections
      expect(responseTime).toBeLessThan(150);
      console.log(`Template with many sections response time: ${responseTime}ms`);
    });
  });
});
