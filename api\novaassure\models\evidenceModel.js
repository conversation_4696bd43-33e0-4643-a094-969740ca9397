/**
 * Evidence Model
 * 
 * This model defines the schema for evidence.
 */

const mongoose = require('mongoose');
const crypto = require('crypto');
const fs = require('fs');
const path = require('path');
const blockchainEvidence = require('../services/blockchainEvidence');
const logger = require('../utils/logger');

const evidenceSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  type: {
    type: String,
    enum: ['document', 'screenshot', 'log', 'api-response', 'other'],
    required: true,
    index: true
  },
  control: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Control',
    index: true
  },
  testExecution: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'TestExecution',
    index: true
  },
  filePath: {
    type: String
  },
  fileType: {
    type: String
  },
  fileSize: {
    type: Number
  },
  fileHash: {
    type: String
  },
  content: {
    type: String
  },
  blockchain: {
    merkleRoot: {
      type: String
    },
    proof: [{
      type: String
    }],
    submissionId: {
      type: String
    },
    blockNumber: {
      type: Number
    },
    blockHash: {
      type: String
    },
    timestamp: {
      type: Number
    },
    transactionHash: {
      type: String
    }
  },
  metadata: {
    type: mongoose.Schema.Types.Mixed
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Create indexes
evidenceSchema.index({ name: 'text', description: 'text' });
evidenceSchema.index({ type: 1, control: 1 });
evidenceSchema.index({ type: 1, testExecution: 1 });
evidenceSchema.index({ fileHash: 1 });

/**
 * Get evidence by ID
 * @param {string} id - Evidence ID
 * @returns {Promise<Object>} - Evidence object
 */
evidenceSchema.statics.getById = async function(id) {
  return this.findById(id)
    .populate('control')
    .populate('testExecution')
    .populate('createdBy');
};

/**
 * Get evidence by control
 * @param {string} controlId - Control ID
 * @returns {Promise<Array>} - Array of evidence objects
 */
evidenceSchema.statics.getByControl = async function(controlId) {
  return this.find({ control: controlId })
    .populate('control')
    .populate('testExecution')
    .populate('createdBy')
    .sort({ createdAt: -1 });
};

/**
 * Get evidence by test execution
 * @param {string} testExecutionId - Test execution ID
 * @returns {Promise<Array>} - Array of evidence objects
 */
evidenceSchema.statics.getByTestExecution = async function(testExecutionId) {
  return this.find({ testExecution: testExecutionId })
    .populate('control')
    .populate('testExecution')
    .populate('createdBy')
    .sort({ createdAt: -1 });
};

/**
 * Get evidence by type
 * @param {string} type - Evidence type
 * @returns {Promise<Array>} - Array of evidence objects
 */
evidenceSchema.statics.getByType = async function(type) {
  return this.find({ type })
    .populate('control')
    .populate('testExecution')
    .populate('createdBy')
    .sort({ createdAt: -1 });
};

/**
 * Create evidence from file
 * @param {Object} evidenceData - Evidence data
 * @param {Object} file - File object
 * @param {string} userId - User ID
 * @returns {Promise<Object>} - Evidence object
 */
evidenceSchema.statics.createFromFile = async function(evidenceData, file, userId) {
  try {
    // Calculate file hash
    const fileBuffer = fs.readFileSync(file.path);
    const fileHash = crypto.createHash('sha256').update(fileBuffer).digest('hex');
    
    // Create evidence
    const evidence = new this({
      name: evidenceData.name,
      description: evidenceData.description,
      type: evidenceData.type,
      control: evidenceData.controlId,
      testExecution: evidenceData.testExecutionId,
      filePath: file.path,
      fileType: file.mimetype,
      fileSize: file.size,
      fileHash,
      metadata: evidenceData.metadata,
      createdBy: userId,
      updatedBy: userId
    });
    
    // Save evidence
    await evidence.save();
    
    // Submit to blockchain
    try {
      const blockchainRecord = await blockchainEvidence.createEvidenceRecord({
        type: 'file',
        content: fileHash,
        metadata: {
          evidenceId: evidence._id.toString(),
          name: evidence.name,
          type: evidence.type,
          fileType: evidence.fileType,
          fileSize: evidence.fileSize
        }
      });
      
      // Update evidence with blockchain information
      evidence.blockchain = blockchainRecord.blockchain;
      await evidence.save();
    } catch (error) {
      logger.error('Failed to submit evidence to blockchain', error);
    }
    
    return evidence;
  } catch (error) {
    // Delete file if evidence creation fails
    if (file && file.path) {
      try {
        fs.unlinkSync(file.path);
      } catch (unlinkError) {
        logger.error('Failed to delete file', unlinkError);
      }
    }
    
    throw error;
  }
};

/**
 * Create evidence from content
 * @param {Object} evidenceData - Evidence data
 * @param {string} userId - User ID
 * @returns {Promise<Object>} - Evidence object
 */
evidenceSchema.statics.createFromContent = async function(evidenceData, userId) {
  try {
    // Calculate content hash
    const contentHash = crypto.createHash('sha256').update(evidenceData.content).digest('hex');
    
    // Create evidence
    const evidence = new this({
      name: evidenceData.name,
      description: evidenceData.description,
      type: evidenceData.type,
      control: evidenceData.controlId,
      testExecution: evidenceData.testExecutionId,
      content: evidenceData.content,
      fileHash: contentHash,
      metadata: evidenceData.metadata,
      createdBy: userId,
      updatedBy: userId
    });
    
    // Save evidence
    await evidence.save();
    
    // Submit to blockchain
    try {
      const blockchainRecord = await blockchainEvidence.createEvidenceRecord({
        type: 'content',
        content: contentHash,
        metadata: {
          evidenceId: evidence._id.toString(),
          name: evidence.name,
          type: evidence.type
        }
      });
      
      // Update evidence with blockchain information
      evidence.blockchain = blockchainRecord.blockchain;
      await evidence.save();
    } catch (error) {
      logger.error('Failed to submit evidence to blockchain', error);
    }
    
    return evidence;
  } catch (error) {
    throw error;
  }
};

/**
 * Verify evidence
 * @param {string} id - Evidence ID
 * @returns {Promise<Object>} - Verification result
 */
evidenceSchema.statics.verify = async function(id) {
  // Get evidence
  const evidence = await this.findById(id);
  
  if (!evidence) {
    throw new Error('Evidence not found');
  }
  
  // Check if evidence has blockchain information
  if (!evidence.blockchain || !evidence.blockchain.merkleRoot || !evidence.blockchain.proof) {
    return {
      verified: false,
      reason: 'Evidence not anchored to blockchain'
    };
  }
  
  // Verify file hash if evidence has a file
  if (evidence.filePath) {
    try {
      const fileBuffer = fs.readFileSync(evidence.filePath);
      const fileHash = crypto.createHash('sha256').update(fileBuffer).digest('hex');
      
      if (fileHash !== evidence.fileHash) {
        return {
          verified: false,
          reason: 'File hash mismatch'
        };
      }
    } catch (error) {
      logger.error('Failed to verify file hash', error);
      
      return {
        verified: false,
        reason: 'Failed to verify file hash'
      };
    }
  }
  
  // Verify content hash if evidence has content
  if (evidence.content) {
    const contentHash = crypto.createHash('sha256').update(evidence.content).digest('hex');
    
    if (contentHash !== evidence.fileHash) {
      return {
        verified: false,
        reason: 'Content hash mismatch'
      };
    }
  }
  
  // Verify blockchain evidence
  try {
    const verification = await blockchainEvidence.verifyEvidence(
      evidence.fileHash,
      evidence.blockchain.proof,
      evidence.blockchain.merkleRoot,
      evidence.blockchain.submissionId
    );
    
    return verification;
  } catch (error) {
    logger.error('Failed to verify blockchain evidence', error);
    
    return {
      verified: false,
      reason: 'Failed to verify blockchain evidence'
    };
  }
};

const Evidence = mongoose.model('Evidence', evidenceSchema);

module.exports = Evidence;
