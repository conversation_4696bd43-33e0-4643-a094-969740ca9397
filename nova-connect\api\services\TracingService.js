/**
 * Tracing Service
 * 
 * This service provides distributed tracing capabilities using OpenTelemetry.
 * It integrates with Google Cloud Trace for visualization and analysis.
 */

const opentelemetry = require('@opentelemetry/api');
const { NodeTracerProvider } = require('@opentelemetry/sdk-trace-node');
const { SimpleSpanProcessor, BatchSpanProcessor } = require('@opentelemetry/sdk-trace-base');
const { ZipkinExporter } = require('@opentelemetry/exporter-zipkin');
const { TraceExporter } = require('@google-cloud/opentelemetry-cloud-trace-exporter');
const { Resource } = require('@opentelemetry/resources');
const { SemanticResourceAttributes } = require('@opentelemetry/semantic-conventions');
const { ExpressInstrumentation } = require('@opentelemetry/instrumentation-express');
const { HttpInstrumentation } = require('@opentelemetry/instrumentation-http');
const { MongoDBInstrumentation } = require('@opentelemetry/instrumentation-mongodb');
const { registerInstrumentations } = require('@opentelemetry/instrumentation');
const logger = require('../utils/logger');

class TracingService {
  constructor() {
    this.enabled = process.env.TRACING_ENABLED === 'true';
    this.exporterType = process.env.TRACING_EXPORTER || 'console';
    this.serviceName = process.env.SERVICE_NAME || 'novaconnect';
    this.serviceVersion = process.env.SERVICE_VERSION || '1.0.0';
    
    if (this.enabled) {
      this._initializeTracing();
      logger.info(`Tracing service initialized with ${this.exporterType} exporter`);
    } else {
      logger.info('Tracing service disabled');
    }
  }
  
  /**
   * Initialize OpenTelemetry tracing
   */
  _initializeTracing() {
    try {
      // Create a tracer provider
      this.tracerProvider = new NodeTracerProvider({
        resource: new Resource({
          [SemanticResourceAttributes.SERVICE_NAME]: this.serviceName,
          [SemanticResourceAttributes.SERVICE_VERSION]: this.serviceVersion,
          [SemanticResourceAttributes.DEPLOYMENT_ENVIRONMENT]: process.env.NODE_ENV || 'development'
        })
      });
      
      // Create and add span processor with appropriate exporter
      const spanProcessor = this._createSpanProcessor();
      this.tracerProvider.addSpanProcessor(spanProcessor);
      
      // Register the provider
      this.tracerProvider.register();
      
      // Register instrumentations
      this._registerInstrumentations();
      
      // Create a tracer
      this.tracer = opentelemetry.trace.getTracer(this.serviceName);
      
      logger.info('OpenTelemetry tracing initialized');
    } catch (error) {
      logger.error('Error initializing OpenTelemetry tracing', { error });
      this.enabled = false;
    }
  }
  
  /**
   * Create a span processor with the appropriate exporter
   * @returns {SpanProcessor} - OpenTelemetry span processor
   */
  _createSpanProcessor() {
    let exporter;
    
    switch (this.exporterType) {
      case 'gcp':
        exporter = new TraceExporter({
          projectId: process.env.GOOGLE_CLOUD_PROJECT
        });
        return new BatchSpanProcessor(exporter);
        
      case 'zipkin':
        exporter = new ZipkinExporter({
          url: process.env.ZIPKIN_URL || 'http://localhost:9411/api/v2/spans',
          serviceName: this.serviceName
        });
        return new BatchSpanProcessor(exporter);
        
      case 'console':
      default:
        // Console exporter for development
        const { ConsoleSpanExporter } = require('@opentelemetry/sdk-trace-base');
        exporter = new ConsoleSpanExporter();
        return new SimpleSpanProcessor(exporter);
    }
  }
  
  /**
   * Register automatic instrumentations
   */
  _registerInstrumentations() {
    registerInstrumentations({
      tracerProvider: this.tracerProvider,
      instrumentations: [
        // HTTP instrumentation
        new HttpInstrumentation({
          ignoreIncomingPaths: ['/health', '/metrics'],
        }),
        
        // Express instrumentation
        new ExpressInstrumentation(),
        
        // MongoDB instrumentation
        new MongoDBInstrumentation({
          enhancedDatabaseReporting: true,
        }),
      ],
    });
  }
  
  /**
   * Start a new span
   * @param {string} name - Span name
   * @param {Object} options - Span options
   * @returns {Span} - OpenTelemetry span
   */
  startSpan(name, options = {}) {
    if (!this.enabled) {
      return {
        end: () => {},
        setAttribute: () => {},
        addEvent: () => {},
        setStatus: () => {},
        recordException: () => {},
        updateName: () => {}
      };
    }
    
    return this.tracer.startSpan(name, options);
  }
  
  /**
   * Execute a function within a new span
   * @param {string} name - Span name
   * @param {Function} fn - Function to execute
   * @param {Object} options - Span options
   * @returns {any} - Function result
   */
  withSpan(name, fn, options = {}) {
    if (!this.enabled) {
      return fn();
    }
    
    const span = this.startSpan(name, options);
    
    try {
      const result = fn(span);
      
      // Handle promises
      if (result instanceof Promise) {
        return result
          .then((value) => {
            span.end();
            return value;
          })
          .catch((error) => {
            span.recordException(error);
            span.setStatus({ code: opentelemetry.SpanStatusCode.ERROR });
            span.end();
            throw error;
          });
      }
      
      span.end();
      return result;
    } catch (error) {
      span.recordException(error);
      span.setStatus({ code: opentelemetry.SpanStatusCode.ERROR });
      span.end();
      throw error;
    }
  }
  
  /**
   * Get the current span
   * @returns {Span} - Current span or null
   */
  getCurrentSpan() {
    if (!this.enabled) {
      return null;
    }
    
    return opentelemetry.trace.getSpan(opentelemetry.context.active());
  }
  
  /**
   * Add an attribute to the current span
   * @param {string} key - Attribute key
   * @param {string|number|boolean} value - Attribute value
   */
  addAttribute(key, value) {
    if (!this.enabled) {
      return;
    }
    
    const currentSpan = this.getCurrentSpan();
    if (currentSpan) {
      currentSpan.setAttribute(key, value);
    }
  }
  
  /**
   * Add an event to the current span
   * @param {string} name - Event name
   * @param {Object} attributes - Event attributes
   */
  addEvent(name, attributes = {}) {
    if (!this.enabled) {
      return;
    }
    
    const currentSpan = this.getCurrentSpan();
    if (currentSpan) {
      currentSpan.addEvent(name, attributes);
    }
  }
  
  /**
   * Record an exception in the current span
   * @param {Error} error - Error to record
   */
  recordException(error) {
    if (!this.enabled) {
      return;
    }
    
    const currentSpan = this.getCurrentSpan();
    if (currentSpan) {
      currentSpan.recordException(error);
      currentSpan.setStatus({ code: opentelemetry.SpanStatusCode.ERROR });
    }
  }
}

module.exports = new TracingService();
