#!/usr/bin/env python3
"""
Real Vision Benchmark with π-Coherence Optimization
Tests actual ResNet18 model with π-coherence timing applied to inference loops

This is REAL AI benchmarking - no mocks, no simulations.
"""

import torch
import time
import psutil
import os
from typing import Dict, Any

def benchmark_vision_with_pi_coherence() -> Dict[str, Any]:
    """
    Benchmark ResNet18 with and without π-coherence optimization
    
    Returns:
        Dictionary containing real benchmark results
    """
    print("👁️  Real Vision Benchmark: ResNet18 with π-Coherence")
    print("=" * 60)
    
    try:
        from torchvision import models
        from benchmarks.pi_scheduler import get_next_interval, get_pi_stats, reset_interval_cycle
        from metrics.logger import log_result
        
        # Load real ResNet18 model
        print("📥 Loading ResNet18 model...")
        model = models.resnet18(weights=None)  # No pretrained weights for faster loading
        model.eval()
        print("✅ Model loaded successfully")
        
        # Prepare test input (batch of images)
        batch_size = 4
        image_size = 224
        dummy_input = torch.randn(batch_size, 3, image_size, image_size)
        
        print(f"🖼️  Test input: {batch_size} images of {image_size}x{image_size}")
        print(f"📊 Input tensor shape: {dummy_input.shape}")
        
        # Warm up the model
        print("🔥 Warming up model...")
        with torch.no_grad():
            for _ in range(5):
                _ = model(dummy_input)
        
        # Benchmark parameters
        num_iterations = 25
        print(f"🔄 Running {num_iterations} iterations for each mode...")
        
        # ---- CONTROL MODE (No π-Coherence) ----
        print("\n📊 Control Mode (No π-Coherence):")
        
        # Memory before control
        process = psutil.Process()
        memory_before_control = process.memory_info().rss / 1024 / 1024  # MB
        
        control_times = []
        start_time = time.time()
        
        with torch.no_grad():
            for i in range(num_iterations):
                iter_start = time.time()
                outputs = model(dummy_input)
                iter_end = time.time()
                control_times.append((iter_end - iter_start) * 1000)  # Convert to ms
                
                if (i + 1) % 10 == 0:
                    print(f"  Completed {i + 1}/{num_iterations} iterations")
        
        control_total_time = time.time() - start_time
        control_avg_latency = sum(control_times) / len(control_times)
        control_images_per_sec = (batch_size * num_iterations) / control_total_time
        
        # Memory after control
        memory_after_control = process.memory_info().rss / 1024 / 1024  # MB
        control_memory_usage = memory_after_control - memory_before_control
        
        print(f"  Average latency: {control_avg_latency:.2f}ms")
        print(f"  Images/sec: {control_images_per_sec:.2f}")
        print(f"  Memory usage: {control_memory_usage:.2f}MB")
        
        # ---- π-COHERENCE MODE ----
        print("\n🧭 π-Coherence Mode:")
        
        # Reset π-coherence cycle
        reset_interval_cycle()
        
        # Memory before π-coherence
        memory_before_pi = process.memory_info().rss / 1024 / 1024  # MB
        
        pi_times = []
        start_time = time.time()
        
        with torch.no_grad():
            for i in range(num_iterations):
                iter_start = time.time()
                outputs = model(dummy_input)
                iter_end = time.time()
                
                # Apply π-coherence timing
                pi_interval = get_next_interval()
                time.sleep(pi_interval)
                
                pi_times.append((iter_end - iter_start) * 1000)  # Convert to ms
                
                if (i + 1) % 10 == 0:
                    print(f"  Completed {i + 1}/{num_iterations} iterations (π-coherence applied)")
        
        pi_total_time = time.time() - start_time
        pi_avg_latency = sum(pi_times) / len(pi_times)  # Pure inference time
        pi_images_per_sec = (batch_size * num_iterations) / pi_total_time
        
        # Memory after π-coherence
        memory_after_pi = process.memory_info().rss / 1024 / 1024  # MB
        pi_memory_usage = memory_after_pi - memory_before_pi
        
        print(f"  Average inference latency: {pi_avg_latency:.2f}ms")
        print(f"  Total images/sec (with π-timing): {pi_images_per_sec:.2f}")
        print(f"  Memory usage: {pi_memory_usage:.2f}MB")
        
        # Get π-coherence statistics
        pi_stats = get_pi_stats()
        
        # ---- CALCULATE IMPROVEMENTS ----
        latency_improvement = ((control_avg_latency - pi_avg_latency) / control_avg_latency) * 100
        throughput_improvement = ((pi_images_per_sec - control_images_per_sec) / control_images_per_sec) * 100
        memory_improvement = ((control_memory_usage - pi_memory_usage) / control_memory_usage) * 100 if control_memory_usage > 0 else 0
        
        # Prepare results
        results = {
            "model": "resnet18",
            "iterations": num_iterations,
            "batch_size": batch_size,
            "image_size": f"{image_size}x{image_size}",
            "control_metrics": {
                "avg_latency_ms": round(control_avg_latency, 2),
                "images_per_sec": round(control_images_per_sec, 2),
                "memory_usage_mb": round(control_memory_usage, 2),
                "total_time_sec": round(control_total_time, 2)
            },
            "pi_coherence_metrics": {
                "avg_inference_latency_ms": round(pi_avg_latency, 2),
                "images_per_sec": round(pi_images_per_sec, 2),
                "memory_usage_mb": round(pi_memory_usage, 2),
                "total_time_sec": round(pi_total_time, 2),
                "pi_intervals_used": pi_stats['total_delays'],
                "avg_pi_delay": round(pi_stats['average_delay'] * 1000, 2)  # Convert to ms
            },
            "improvements": {
                "latency_improvement_percent": round(latency_improvement, 2),
                "throughput_improvement_percent": round(throughput_improvement, 2),
                "memory_improvement_percent": round(memory_improvement, 2)
            },
            "gain_percent": round(latency_improvement, 2),  # For logger compatibility
            "pi_coherence_applied": True,
            "real_model": True
        }
        
        # Log results
        log_result("benchmark_vision", results)
        
        # Print summary
        print(f"\n📈 RESULTS SUMMARY:")
        print(f"  Latency Improvement: {latency_improvement:+.2f}%")
        print(f"  Throughput Change: {throughput_improvement:+.2f}%")
        print(f"  Memory Change: {memory_improvement:+.2f}%")
        print(f"  π-Intervals Applied: {pi_stats['total_delays']}")
        
        if latency_improvement > 0:
            print("  ✅ π-Coherence shows positive impact on inference latency!")
        else:
            print("  ⚠️  π-Coherence impact on latency is minimal or negative")
            
        return results
        
    except ImportError as e:
        print(f"❌ Required libraries not available: {e}")
        print("   Install with: pip install torch torchvision")
        return {"error": "missing_dependencies", "message": str(e)}
    
    except Exception as e:
        print(f"❌ Benchmark failed: {e}")
        import traceback
        traceback.print_exc()
        return {"error": "benchmark_failed", "message": str(e)}

if __name__ == "__main__":
    # Run the benchmark
    result = benchmark_vision_with_pi_coherence()
    
    if "error" not in result:
        print("\n✅ Real vision benchmark completed successfully!")
        print("📊 Results saved to output/benchmark_results.json")
    else:
        print(f"\n❌ Benchmark failed: {result.get('message', 'Unknown error')}")
        exit(1)
