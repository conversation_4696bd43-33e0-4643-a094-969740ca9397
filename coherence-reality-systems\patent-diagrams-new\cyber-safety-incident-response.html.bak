<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cyber-Safety Incident Response Flow Diagram</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .diagram-container {
            position: relative;
            width: 800px;
            height: 650px;
            margin: 0 auto;
            background-color: white;
            border: 1px solid #ddd;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            box-sizing: border-box;
        }
        .container-box {
            position: absolute;
            border: 2px solid #666;
            border-radius: 10px;
            padding: 30px 10px 10px;
            box-sizing: border-box;
        }
        .container-label {
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            font-weight: bold;
            font-size: 16px;
            text-align: center;
        }
        .component-box {
            position: absolute;
            background-color: white;
            border: 2px solid #333;
            border-radius: 6px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            padding: 10px;
            box-sizing: border-box;
            font-size: 14px;
            line-height: 1.2;
            z-index: 1;
        }
        .component-number {
            position: absolute;
            top: -10px;
            left: -10px;
            width: 20px;
            height: 20px;
            background-color: #555555; /* Grey color for patent compliance */
            color: white;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 12px;
            font-weight: bold;
        }
        .component-label {
            font-weight: bold;
            margin-bottom: 4px;
        }
        .arrow {
            position: absolute;
            background-color: transparent;
            z-index: 0;
        }
        .arrow-line {
            position: absolute;
            background-color: #333;
            z-index: 0;
        }
        .arrow-head {
            position: absolute;
            width: 0;
            height: 0;
            border-style: solid;
        }
        .formula-box {
            position: absolute;
            font-size: 12px;
            font-style: italic;
            color: #555555;
            background-color: #f9f9f9;
            border: 1px dashed #ccc;
            border-radius: 4px;
            padding: 2px 5px;
            z-index: 1;
        }
        .legend {
            position: absolute;
            right: 10px;
            bottom: 10px;
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 5px;
            z-index: 10;
            width: 200px;
        }
        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
            font-size: 12px;
        }
        .legend-color {
            width: 15px;
            height: 15px;
            margin-right: 5px;
            border: 1px solid #333;
        }
        .inventor-label {
            position: absolute;
            left: 10px;
            bottom: 10px;
            font-size: 12px;
            font-style: italic;
            color: #333;
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <h1>FIG. 8: Cyber-Safety Incident Response Flow Diagram</h1>
    
    <div class="diagram-container">
        <!-- Main Container -->
        <div class="container-box" style="width: 750px; height: 600px; left: 25px; top: 20px;">
            <div class="container-label" style="font-size: 18px;">CYBER-SAFETY INCIDENT RESPONSE FLOW</div>
        </div>

        <!-- Threat Detection -->
        <div class="component-box" style="left: 300px; top: 80px; width: 200px; height: 80px;">
            <div class="component-number">1001</div>
            <div class="component-label">Threat Detection</div>
            <div style="font-size: 12px; text-align: center;">
                NovaShield<br>
                Multi-source Anomaly Detection
            </div>
        </div>
        <div class="formula-box" style="left: 510px; top: 100px;">Trinity Equation</div>

        <!-- Initial Analysis -->
        <div class="component-box" style="left: 300px; top: 180px; width: 200px; height: 80px;">
            <div class="component-number">1002</div>
            <div class="component-label">Initial Analysis</div>
            <div style="font-size: 12px; text-align: center;">
                NovaCore<br>
                UUFT-based Pattern Recognition
            </div>
        </div>
        <div class="formula-box" style="left: 510px; top: 200px;">(A⊗B⊕C)×π10³</div>

        <!-- Risk Assessment -->
        <div class="component-box" style="left: 300px; top: 280px; width: 200px; height: 80px;">
            <div class="component-number">1003</div>
            <div class="component-label">Risk Assessment</div>
            <div style="font-size: 12px; text-align: center;">
                NovaTrack<br>
                Impact & Probability Calculation
            </div>
        </div>
        <div class="formula-box" style="left: 510px; top: 300px;">Data Purity Score</div>

        <!-- Response Selection -->
        <div class="component-box" style="left: 150px; top: 380px; width: 200px; height: 80px;">
            <div class="component-number">1004</div>
            <div class="component-label">Automated Response</div>
            <div style="font-size: 12px; text-align: center;">
                NovaFlowX<br>
                Predefined Response Workflows
            </div>
        </div>
        <div class="formula-box" style="left: 150px; top: 470px;">18/82 Principle</div>

        <!-- Response Selection -->
        <div class="component-box" style="left: 450px; top: 380px; width: 200px; height: 80px;">
            <div class="component-number">1005</div>
            <div class="component-label">Adaptive Response</div>
            <div style="font-size: 12px; text-align: center;">
                NovaLearn<br>
                AI-driven Custom Response
            </div>
        </div>
        <div class="formula-box" style="left: 450px; top: 470px;">Adaptive Coherence</div>

        <!-- Verification & Documentation -->
        <div class="component-box" style="left: 300px; top: 500px; width: 200px; height: 80px;">
            <div class="component-number">1006</div>
            <div class="component-label">Verification & Documentation</div>
            <div style="font-size: 12px; text-align: center;">
                NovaProof<br>
                Compliance & Audit Trail
            </div>
        </div>

        <!-- Decision Points -->
        <div class="component-box" style="left: 50px; top: 280px; width: 150px; height: 80px; border: 2px dashed #333;">
            <div class="component-number">1007</div>
            <div class="component-label">Decision Point</div>
            <div style="font-size: 12px; text-align: center;">
                Severity Assessment<br>
                High/Medium/Low
            </div>
        </div>

        <div class="component-box" style="left: 600px; top: 280px; width: 150px; height: 80px; border: 2px dashed #333;">
            <div class="component-number">1008</div>
            <div class="component-label">Decision Point</div>
            <div style="font-size: 12px; text-align: center;">
                Response Type<br>
                Auto/Adaptive/Manual
            </div>
        </div>

        <!-- Arrows -->
        <div class="arrow-line" style="left: 400px; top: 160px; width: 2px; height: 20px;"></div>
        <div class="arrow-head" style="left: 396px; top: 180px; border-width: 8px 4px 0 4px; border-color: #333 transparent transparent transparent;"></div>

        <div class="arrow-line" style="left: 400px; top: 260px; width: 2px; height: 20px;"></div>
        <div class="arrow-head" style="left: 396px; top: 280px; border-width: 8px 4px 0 4px; border-color: #333 transparent transparent transparent;"></div>

        <div class="arrow-line" style="left: 300px; top: 320px; width: 150px; height: 2px;"></div>
        <div class="arrow-head" style="left: 150px; top: 316px; border-width: 4px 8px 4px 0; border-color: transparent #333 transparent transparent;"></div>

        <div class="arrow-line" style="left: 500px; top: 320px; width: 100px; height: 2px;"></div>
        <div class="arrow-head" style="left: 600px; top: 316px; border-width: 4px 0 4px 8px; border-color: transparent transparent transparent #333;"></div>

        <div class="arrow-line" style="left: 125px; top: 360px; width: 2px; height: 20px;"></div>
        <div class="arrow-head" style="left: 121px; top: 380px; border-width: 8px 4px 0 4px; border-color: #333 transparent transparent transparent;"></div>

        <div class="arrow-line" style="left: 675px; top: 360px; width: 2px; height: 20px;"></div>
        <div class="arrow-head" style="left: 671px; top: 380px; border-width: 8px 4px 0 4px; border-color: #333 transparent transparent transparent;"></div>

        <div class="arrow-line" style="left: 250px; top: 420px; width: 50px; height: 2px; transform: translate(0, 0) rotate(45deg); transform-origin: top left;"></div>
        <div class="arrow-head" style="left: 300px; top: 500px; border-width: 4px 0 4px 8px; border-color: transparent transparent transparent #333;"></div>

        <div class="arrow-line" style="left: 550px; top: 420px; width: 50px; height: 2px; transform: translate(0, 0) rotate(-45deg); transform-origin: top left;"></div>
        <div class="arrow-head" style="left: 500px; top: 500px; border-width: 4px 8px 4px 0; border-color: transparent #333 transparent transparent;"></div>

        <!-- Legend -->
        <div class="legend">
            <div class="legend-item">
                <div class="legend-color" style="background-color: #fff;"></div>
                <div>Process Steps</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #fff; border: 2px dashed #333;"></div>
                <div>Decision Points</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #f9f9f9;"></div>
                <div>Mathematical Formulas</div>
            </div>
        </div>

        <div class="inventor-label">Inventor: David Nigel Irvin</div>
    </div>
</body>
</html>
