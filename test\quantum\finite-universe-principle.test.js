/**
 * Finite Universe Principle Tests
 *
 * This file contains tests for the Finite Universe Principle components.
 */

const {
  FiniteUniverse,
  BoundaryEnforcer,
  ResilienceGauntlet,
  createBoundaryEnforcer,
  createResilienceGauntlet
} = require('../../src/quantum/finite-universe-principle');

describe('FiniteUniverse', () => {
  let finiteUniverse;

  beforeEach(() => {
    finiteUniverse = new FiniteUniverse({
      enableLogging: false,
      strictMode: false,
      autoCorrect: true
    });
  });

  test('should handle infinite values', () => {
    expect(finiteUniverse.enforceNumber(Infinity)).toBe(Number.MAX_SAFE_INTEGER);
    expect(finiteUniverse.enforceNumber(-Infinity)).toBe(Number.MIN_SAFE_INTEGER);
    expect(finiteUniverse.getViolationStats().infiniteValue).toBe(2);
    expect(finiteUniverse.getCorrectionStats().infiniteValue).toBe(2);
  });

  test('should handle NaN values', () => {
    expect(finiteUniverse.enforceNumber(NaN)).toBe(0);
    expect(finiteUniverse.getViolationStats().nanValue).toBe(1);
    expect(finiteUniverse.getCorrectionStats().nanValue).toBe(1);
  });

  test('should handle values exceeding domain boundaries', () => {
    // Assuming cyber domain has a max value of 1e9
    expect(finiteUniverse.enforceNumber(1e10, 'cyber')).toBeLessThan(1e10);
    expect(finiteUniverse.getViolationStats().exceedsBounds).toBe(1);
    expect(finiteUniverse.getCorrectionStats().exceedsBounds).toBe(1);
  });

  test('should handle circular references', () => {
    const circularObj = {};
    circularObj.self = circularObj;
    
    const result = finiteUniverse.enforceObject(circularObj);
    
    expect(result).toHaveProperty('_circularReference');
    expect(finiteUniverse.getViolationStats().circularReference).toBe(1);
    expect(finiteUniverse.getCorrectionStats().circularReference).toBe(1);
  });

  test('should handle excessive complexity', () => {
    // Create a deep object
    let deepObj = {};
    let current = deepObj;
    for (let i = 0; i < 200; i++) {
      current.next = {};
      current = current.next;
    }
    
    const result = finiteUniverse.enforceObject(deepObj);
    
    expect(result).toHaveProperty('_maxDepthExceeded');
    expect(finiteUniverse.getViolationStats().excessiveComplexity).toBe(1);
    expect(finiteUniverse.getCorrectionStats().excessiveComplexity).toBe(1);
  });

  test('should reset statistics', () => {
    finiteUniverse.enforceNumber(Infinity);
    finiteUniverse.enforceNumber(NaN);
    
    expect(finiteUniverse.getViolationStats().total).toBe(2);
    expect(finiteUniverse.getCorrectionStats().total).toBe(2);
    
    finiteUniverse.resetStats();
    
    expect(finiteUniverse.getViolationStats().total).toBe(0);
    expect(finiteUniverse.getCorrectionStats().total).toBe(0);
  });
});

describe('BoundaryEnforcer', () => {
  let boundaryEnforcer;

  beforeEach(() => {
    boundaryEnforcer = createBoundaryEnforcer({
      enableLogging: false,
      strictMode: false,
      autoCorrect: true
    });
  });

  test('should enforce boundaries on values', () => {
    expect(boundaryEnforcer.enforceValue(Infinity)).toBe(Number.MAX_SAFE_INTEGER);
    expect(boundaryEnforcer.enforceValue(-Infinity)).toBe(Number.MIN_SAFE_INTEGER);
    expect(boundaryEnforcer.enforceValue(NaN)).toBe(0);
    
    const circularObj = {};
    circularObj.self = circularObj;
    expect(boundaryEnforcer.enforceValue(circularObj)).toHaveProperty('_circularReference');
  });

  test('should enforce boundaries on function calls', () => {
    const infinityFn = () => Infinity;
    expect(boundaryEnforcer.enforceFunction(infinityFn)).toBe(Number.MAX_SAFE_INTEGER);
    
    const nanFn = () => NaN;
    expect(boundaryEnforcer.enforceFunction(nanFn)).toBe(0);
  });

  test('should prevent excessive recursion', () => {
    function recursiveFn(n) {
      if (n <= 0) return 0;
      return recursiveFn(n - 1) + 1;
    }
    
    // This would normally cause a stack overflow
    expect(() => boundaryEnforcer.enforceFunction(recursiveFn, [1000])).toThrow();
  });

  test('should prevent infinite loops', () => {
    // This would normally be an infinite loop
    expect(() => boundaryEnforcer.enforceLoop(
      () => true, // Always true condition
      () => {}, // Empty body
      () => {} // No update
    )).toThrow();
  });

  test('should create safe proxies', () => {
    const obj = {
      value: Infinity,
      getValue() {
        return this.value;
      }
    };
    
    const safeObj = boundaryEnforcer.createSafeProxy(obj);
    
    expect(safeObj.value).toBe(Number.MAX_SAFE_INTEGER);
    expect(safeObj.getValue()).toBe(Number.MAX_SAFE_INTEGER);
  });
});

describe('ResilienceGauntlet', () => {
  let boundaryEnforcer;
  let gauntlet;

  beforeEach(() => {
    boundaryEnforcer = createBoundaryEnforcer({
      enableLogging: false,
      strictMode: false,
      autoCorrect: true
    });
    
    gauntlet = createResilienceGauntlet({
      enableLogging: false,
      timeoutMs: 5000
    });
  });

  test('should run scenarios against a target', async () => {
    const results = await gauntlet.run(boundaryEnforcer);
    
    expect(results).toHaveProperty('summary');
    expect(results.summary).toHaveProperty('totalScenarios');
    expect(results.summary).toHaveProperty('successfulScenarios');
    expect(results.summary).toHaveProperty('failedScenarios');
    expect(results.summary).toHaveProperty('resilienceScore');
    
    expect(results).toHaveProperty('categoryStats');
    expect(results).toHaveProperty('severityStats');
    expect(results).toHaveProperty('results');
  });

  test('should generate a report with correct statistics', async () => {
    const results = await gauntlet.run(boundaryEnforcer);
    
    // Verify that category stats add up
    for (const category in results.categoryStats) {
      const stats = results.categoryStats[category];
      expect(stats.successful + stats.failed).toBe(stats.total);
    }
    
    // Verify that severity stats add up
    for (const severity in results.severityStats) {
      const stats = results.severityStats[severity];
      expect(stats.successful + stats.failed).toBe(stats.total);
    }
    
    // Verify that total scenarios add up
    expect(results.summary.successfulScenarios + results.summary.failedScenarios)
      .toBe(results.summary.totalScenarios);
  });

  test('should handle scenario timeouts', async () => {
    // Create a gauntlet with a very short timeout
    const shortTimeoutGauntlet = new ResilienceGauntlet({
      enableLogging: false,
      timeoutMs: 1 // 1ms timeout
    });
    
    // Add a scenario that will timeout
    shortTimeoutGauntlet.addScenario({
      name: 'Timeout Scenario',
      category: 'Timeout',
      severity: 'high',
      run: async () => {
        return new Promise(resolve => setTimeout(resolve, 1000));
      }
    });
    
    const results = await shortTimeoutGauntlet.run(boundaryEnforcer);
    
    expect(results.summary.failedScenarios).toBe(1);
    expect(results.results[0].success).toBe(false);
    expect(results.results[0].message).toContain('timed out');
  });
});

// Helper function to create a scenario for testing
function createTestScenario(name, category, severity, success = true) {
  return {
    name,
    category,
    severity,
    run: async () => ({
      name,
      category,
      severity,
      success,
      message: success ? 'Success' : 'Failure'
    })
  };
}
