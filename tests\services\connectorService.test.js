import {
  getConnectors,
  getConnectorById,
  installConnector,
  uninstallConnector,
  executeConnector,
  submitConnector
} from '../../services/connectorService';

// Mock fetch
global.fetch = jest.fn();

// Mock localStorage
const localStorageMock = (() => {
  let store = {};
  return {
    getItem: jest.fn(key => store[key] || null),
    setItem: jest.fn((key, value) => {
      store[key] = value.toString();
    }),
    removeItem: jest.fn(key => {
      delete store[key];
    }),
    clear: jest.fn(() => {
      store = {};
    })
  };
})();

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
});

// Skip these tests for now as they require a running API server
describe.skip('connectorService', () => {
  beforeEach(() => {
    // Clear all mocks
    jest.clearAllMocks();
    localStorageMock.clear();

    // Set token in localStorage
    localStorageMock.setItem('token', 'test-token');

    // Mock successful fetch response
    global.fetch.mockResolvedValue({
      ok: true,
      json: jest.fn().mockResolvedValue({})
    });
  });

  describe('getConnectors', () => {
    it('fetches connectors correctly', async () => {
      // Mock successful fetch response for getConnectors
      global.fetch.mockResolvedValueOnce({
        ok: true,
        json: jest.fn().mockResolvedValue([
          { id: 'connector-1', name: 'Connector 1' },
          { id: 'connector-2', name: 'Connector 2' }
        ])
      });

      const result = await getConnectors();

      // Check if fetch was called with correct URL
      expect(global.fetch).toHaveBeenCalledWith('/api/connectors');

      // Check if result has correct structure
      expect(Array.isArray(result)).toBe(true);
      expect(result).toHaveLength(2);
      expect(result[0]).toHaveProperty('id', 'connector-1');
      expect(result[1]).toHaveProperty('id', 'connector-2');
    });

    it('handles fetch errors correctly', async () => {
      // Mock fetch to reject
      global.fetch.mockRejectedValueOnce(new Error('Network error'));

      // Call should throw an error
      await expect(getConnectors()).rejects.toThrow('Network error');
    });

    it('handles API errors correctly', async () => {
      // Mock fetch to return error response
      global.fetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error'
      });

      // Call should throw an error
      await expect(getConnectors()).rejects.toThrow('Failed to fetch connectors: 500 Internal Server Error');
    });
  });

  describe('getConnectorById', () => {
    it('fetches connector by ID correctly', async () => {
      // Mock successful fetch response for getConnectorById
      global.fetch.mockResolvedValueOnce({
        ok: true,
        json: jest.fn().mockResolvedValue({
          id: 'connector-1',
          name: 'Connector 1',
          description: 'Test connector'
        })
      });

      const result = await getConnectorById('connector-1');

      // Check if fetch was called with correct URL
      expect(global.fetch).toHaveBeenCalledWith('/api/connectors/connector-1');

      // Check if result has correct structure
      expect(result).toHaveProperty('id', 'connector-1');
      expect(result).toHaveProperty('name', 'Connector 1');
      expect(result).toHaveProperty('description', 'Test connector');
    });

    it('handles fetch errors correctly', async () => {
      // Mock fetch to reject
      global.fetch.mockRejectedValueOnce(new Error('Network error'));

      // Call should throw an error
      await expect(getConnectorById('connector-1')).rejects.toThrow('Network error');
    });

    it('handles API errors correctly', async () => {
      // Mock fetch to return error response
      global.fetch.mockResolvedValueOnce({
        ok: false,
        status: 404,
        statusText: 'Not Found'
      });

      // Call should throw an error
      await expect(getConnectorById('nonexistent')).rejects.toThrow('Failed to fetch connector: 404 Not Found');
    });
  });

  describe('installConnector', () => {
    it('installs connector correctly', async () => {
      // Mock successful fetch response for installConnector
      global.fetch.mockResolvedValueOnce({
        ok: true,
        json: jest.fn().mockResolvedValue({
          success: true,
          message: 'Connector installed successfully'
        })
      });

      const result = await installConnector('connector-1');

      // Check if fetch was called with correct URL, method, and headers
      expect(global.fetch).toHaveBeenCalledWith('/api/connectors/connector-1/install', {
        method: 'POST',
        headers: {
          'Authorization': 'Bearer test-token',
          'Content-Type': 'application/json'
        }
      });

      // Check if result has correct structure
      expect(result).toHaveProperty('success', true);
      expect(result).toHaveProperty('message', 'Connector installed successfully');
    });

    it('handles fetch errors correctly', async () => {
      // Mock fetch to reject
      global.fetch.mockRejectedValueOnce(new Error('Network error'));

      // Call should throw an error
      await expect(installConnector('connector-1')).rejects.toThrow('Network error');
    });

    it('handles API errors correctly', async () => {
      // Mock fetch to return error response
      global.fetch.mockResolvedValueOnce({
        ok: false,
        status: 400,
        statusText: 'Bad Request',
        json: jest.fn().mockResolvedValue({
          message: 'Connector already installed'
        })
      });

      // Call should throw an error
      await expect(installConnector('connector-1')).rejects.toThrow('Connector already installed');
    });
  });

  describe('uninstallConnector', () => {
    it('uninstalls connector correctly', async () => {
      // Mock successful fetch response for uninstallConnector
      global.fetch.mockResolvedValueOnce({
        ok: true,
        json: jest.fn().mockResolvedValue({
          success: true,
          message: 'Connector uninstalled successfully'
        })
      });

      const result = await uninstallConnector('connector-1');

      // Check if fetch was called with correct URL, method, and headers
      expect(global.fetch).toHaveBeenCalledWith('/api/connectors/connector-1/uninstall', {
        method: 'POST',
        headers: {
          'Authorization': 'Bearer test-token',
          'Content-Type': 'application/json'
        }
      });

      // Check if result has correct structure
      expect(result).toHaveProperty('success', true);
      expect(result).toHaveProperty('message', 'Connector uninstalled successfully');
    });

    it('handles fetch errors correctly', async () => {
      // Mock fetch to reject
      global.fetch.mockRejectedValueOnce(new Error('Network error'));

      // Call should throw an error
      await expect(uninstallConnector('connector-1')).rejects.toThrow('Network error');
    });

    it('handles API errors correctly', async () => {
      // Mock fetch to return error response
      global.fetch.mockResolvedValueOnce({
        ok: false,
        status: 400,
        statusText: 'Bad Request',
        json: jest.fn().mockResolvedValue({
          message: 'Connector not installed'
        })
      });

      // Call should throw an error
      await expect(uninstallConnector('connector-1')).rejects.toThrow('Connector not installed');
    });
  });

  describe('executeConnector', () => {
    it('executes connector correctly', async () => {
      // Mock successful fetch response for executeConnector
      global.fetch.mockResolvedValueOnce({
        ok: true,
        json: jest.fn().mockResolvedValue({
          executionId: 'exec-123',
          status: 'success',
          results: { data: 'test data' }
        })
      });

      const params = { param1: 'value1', param2: 'value2' };
      const result = await executeConnector('connector-1', params);

      // Check if fetch was called with correct URL, method, headers, and body
      expect(global.fetch).toHaveBeenCalledWith('/api/connectors/connector-1/execute', {
        method: 'POST',
        headers: {
          'Authorization': 'Bearer test-token',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(params)
      });

      // Check if result has correct structure
      expect(result).toHaveProperty('executionId', 'exec-123');
      expect(result).toHaveProperty('status', 'success');
      expect(result).toHaveProperty('results');
      expect(result.results).toHaveProperty('data', 'test data');
    });

    it('handles fetch errors correctly', async () => {
      // Mock fetch to reject
      global.fetch.mockRejectedValueOnce(new Error('Network error'));

      // Call should throw an error
      await expect(executeConnector('connector-1', {})).rejects.toThrow('Network error');
    });

    it('handles API errors correctly', async () => {
      // Mock fetch to return error response
      global.fetch.mockResolvedValueOnce({
        ok: false,
        status: 400,
        statusText: 'Bad Request',
        json: jest.fn().mockResolvedValue({
          message: 'Invalid parameters'
        })
      });

      // Call should throw an error
      await expect(executeConnector('connector-1', {})).rejects.toThrow('Invalid parameters');
    });
  });

  describe('submitConnector', () => {
    it('submits connector correctly', async () => {
      // Mock successful fetch response for submitConnector
      global.fetch.mockResolvedValueOnce({
        ok: true,
        json: jest.fn().mockResolvedValue({
          success: true,
          message: 'Connector submitted successfully',
          submissionId: 'sub-123'
        })
      });

      const connectorData = {
        name: 'New Connector',
        description: 'A new connector for testing',
        category: 'security',
        framework: 'gdpr'
      };

      const result = await submitConnector(connectorData);

      // Check if fetch was called with correct URL, method, headers, and body
      expect(global.fetch).toHaveBeenCalledWith('/api/connectors/submit', {
        method: 'POST',
        headers: {
          'Authorization': 'Bearer test-token',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(connectorData)
      });

      // Check if result has correct structure
      expect(result).toHaveProperty('success', true);
      expect(result).toHaveProperty('message', 'Connector submitted successfully');
      expect(result).toHaveProperty('submissionId', 'sub-123');
    });

    it('handles fetch errors correctly', async () => {
      // Mock fetch to reject
      global.fetch.mockRejectedValueOnce(new Error('Network error'));

      // Call should throw an error
      await expect(submitConnector({})).rejects.toThrow('Network error');
    });

    it('handles API errors correctly', async () => {
      // Mock fetch to return error response
      global.fetch.mockResolvedValueOnce({
        ok: false,
        status: 400,
        statusText: 'Bad Request',
        json: jest.fn().mockResolvedValue({
          message: 'Invalid connector data'
        })
      });

      // Call should throw an error
      await expect(submitConnector({})).rejects.toThrow('Invalid connector data');
    });
  });
});
