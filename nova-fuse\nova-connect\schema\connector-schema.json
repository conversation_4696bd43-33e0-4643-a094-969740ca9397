{"$schema": "http://json-schema.org/draft-07/schema#", "title": "NovaFuse Universal API Connector Schema", "description": "Schema definition for NovaFuse Universal API Connector templates", "type": "object", "required": ["metadata", "authentication", "configuration", "endpoints"], "properties": {"metadata": {"type": "object", "required": ["name", "version", "category", "description"], "properties": {"name": {"type": "string", "description": "Display name of the connector"}, "version": {"type": "string", "description": "Semantic version of the connector", "pattern": "^\\d+\\.\\d+\\.\\d+$"}, "category": {"type": "string", "description": "API category", "enum": ["Cloud Security", "Identity & Access Management", "Workflow & Ticketing", "Compliance Frameworks", "Security Information", "Document & Signature", "ERP & Finance", "Threat Intelligence", "Other"]}, "description": {"type": "string", "description": "Detailed description of the connector"}, "author": {"type": "string", "description": "Creator of the connector"}, "tags": {"type": "array", "items": {"type": "string"}, "description": "Searchable tags for the connector"}, "created": {"type": "string", "format": "date-time", "description": "Creation timestamp"}, "updated": {"type": "string", "format": "date-time", "description": "Last update timestamp"}, "icon": {"type": "string", "description": "URL to the connector icon"}, "documentationUrl": {"type": "string", "format": "uri", "description": "URL to the connector documentation"}}}, "authentication": {"type": "object", "required": ["type", "fields"], "properties": {"type": {"type": "string", "description": "Authentication type", "enum": ["API_KEY", "BASIC", "OAUTH2", "JWT", "AWS_SIG_V4", "CUSTOM"]}, "fields": {"type": "object", "description": "Authentication fields specific to the auth type", "additionalProperties": {"type": "object", "required": ["type", "description"], "properties": {"type": {"type": "string", "enum": ["string", "number", "boolean", "object", "array"]}, "description": {"type": "string"}, "required": {"type": "boolean", "default": false}, "sensitive": {"type": "boolean", "default": false}, "default": {"description": "Default value for the field"}, "enum": {"type": "array", "description": "Enumeration of possible values"}}}}, "testConnection": {"type": "object", "description": "Configuration for testing the connection", "required": ["endpoint", "method"], "properties": {"endpoint": {"type": "string", "description": "Endpoint to test the connection"}, "method": {"type": "string", "enum": ["GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS"]}, "expectedResponse": {"type": "object", "description": "Expected response pattern", "properties": {"status": {"type": "integer", "description": "Expected HTTP status code"}, "body": {"type": "object", "description": "Expected response body pattern"}}}}}, "oauth2Config": {"type": "object", "description": "OAuth2 specific configuration", "properties": {"authorizationUrl": {"type": "string", "format": "uri"}, "tokenUrl": {"type": "string", "format": "uri"}, "scopes": {"type": "array", "items": {"type": "string"}}, "grantType": {"type": "string", "enum": ["authorization_code", "client_credentials", "password", "implicit"]}}}}}, "configuration": {"type": "object", "required": ["baseUrl"], "properties": {"baseUrl": {"type": "string", "description": "API base URL"}, "headers": {"type": "object", "description": "Default headers to include in all requests", "additionalProperties": {"type": "string"}}, "rateLimit": {"type": "object", "description": "Rate limiting configuration", "properties": {"requests": {"type": "integer", "description": "Maximum number of requests"}, "period": {"type": "string", "description": "Time period (e.g., '1m', '1h')"}}}, "timeout": {"type": "integer", "description": "Request timeout in milliseconds", "default": 30000}, "retryPolicy": {"type": "object", "description": "Retry policy configuration", "properties": {"maxRetries": {"type": "integer", "description": "Maximum number of retry attempts", "default": 3}, "backoffStrategy": {"type": "string", "description": "Backoff strategy for retries", "enum": ["linear", "exponential", "fixed"], "default": "exponential"}}}}}, "endpoints": {"type": "array", "description": "API endpoints definition", "minItems": 1, "items": {"type": "object", "required": ["id", "name", "path", "method"], "properties": {"id": {"type": "string", "description": "Unique endpoint identifier"}, "name": {"type": "string", "description": "Display name for the endpoint"}, "description": {"type": "string", "description": "Detailed description of the endpoint"}, "path": {"type": "string", "description": "Endpoint path (appended to baseUrl)"}, "method": {"type": "string", "description": "HTTP method", "enum": ["GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS"]}, "parameters": {"type": "object", "description": "Endpoint parameters", "properties": {"query": {"type": "object", "description": "Query parameters", "additionalProperties": true}, "path": {"type": "object", "description": "Path parameters", "additionalProperties": true}, "body": {"description": "Request body schema", "additionalProperties": true}, "headers": {"type": "object", "description": "Request headers", "additionalProperties": {"type": "string"}}}}, "pagination": {"type": "object", "description": "Pagination configuration", "properties": {"type": {"type": "string", "description": "Pagination type", "enum": ["page", "offset", "cursor", "token", "none"]}, "parameters": {"type": "object", "description": "Pagination parameters", "additionalProperties": true}}}, "response": {"type": "object", "description": "Response configuration", "properties": {"successCode": {"type": "integer", "description": "Expected success code", "default": 200}, "schema": {"type": "object", "description": "Response schema", "additionalProperties": true}, "dataPath": {"type": "string", "description": "JSONPath to the data in the response"}}}}}}, "mappings": {"type": "array", "description": "Data mapping configuration", "items": {"type": "object", "required": ["sourceEndpoint", "targetSystem", "targetEntity", "transformations"], "properties": {"sourceEndpoint": {"type": "string", "description": "Source endpoint ID"}, "targetSystem": {"type": "string", "description": "Target system (e.g., 'NovaGRC')"}, "targetEntity": {"type": "string", "description": "Target entity (e.g., 'ComplianceFindings')"}, "transformations": {"type": "array", "description": "Field transformations", "items": {"type": "object", "required": ["source", "target"], "properties": {"source": {"type": "string", "description": "JSONPath to source field"}, "target": {"type": "string", "description": "Target field name"}, "transform": {"type": "string", "description": "Transformation function name", "default": "identity"}, "parameters": {"type": "object", "description": "Additional parameters for the transformation function", "additionalProperties": true}}}}}}}, "events": {"type": "object", "description": "Event handling configuration", "properties": {"webhooks": {"type": "array", "description": "Webhook configurations", "items": {"type": "object", "required": ["path", "method", "handler"], "properties": {"path": {"type": "string", "description": "Webhook path"}, "method": {"type": "string", "description": "HTTP method", "enum": ["GET", "POST", "PUT", "DELETE", "PATCH"]}, "description": {"type": "string", "description": "Webhook description"}, "parameters": {"type": "object", "description": "Expected parameters", "additionalProperties": true}, "handler": {"type": "string", "description": "Event handler function name"}}}}, "polling": {"type": "array", "description": "Polling configurations", "items": {"type": "object", "required": ["endpoint", "interval"], "properties": {"endpoint": {"type": "string", "description": "Endpoint ID to poll"}, "interval": {"type": "string", "description": "Polling interval (e.g., '5m', '1h')"}, "condition": {"type": "string", "description": "Condition function name to trigger event"}}}}}}}}