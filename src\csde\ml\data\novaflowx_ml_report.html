
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>NovaFlowX ML Engine Results</title>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
      background-color: #f8f9fa;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
    }
    .chart-container {
      width: 100%;
      margin: 20px auto;
      height: 400px;
    }
    h1, h2, h3 {
      color: #0a84ff;
    }
    .card {
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      padding: 20px;
      margin-bottom: 20px;
    }
    .metrics {
      width: 100%;
      border-collapse: collapse;
    }
    .metrics th, .metrics td {
      border: 1px solid #ddd;
      padding: 12px;
      text-align: left;
    }
    .metrics th {
      background-color: #f2f2f2;
    }
    .metrics tr:nth-child(even) {
      background-color: #f9f9f9;
    }
    .status-badge {
      display: inline-block;
      padding: 4px 8px;
      border-radius: 4px;
      font-weight: bold;
    }
    .status-success {
      background-color: #d4edda;
      color: #155724;
    }
    .status-failure {
      background-color: #f8d7da;
      color: #721c24;
    }
    .status-dryrun {
      background-color: #fff3cd;
      color: #856404;
    }
    .priority-critical {
      color: #dc3545;
    }
    .priority-high {
      color: #fd7e14;
    }
    .priority-medium {
      color: #ffc107;
    }
    .priority-low {
      color: #28a745;
    }
    .stats-card {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
    }
    .stat-item {
      flex: 1;
      min-width: 200px;
      text-align: center;
      padding: 20px;
      margin: 10px;
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    .stat-value {
      font-size: 36px;
      font-weight: bold;
      margin: 10px 0;
      color: #0a84ff;
    }
    .stat-label {
      font-size: 14px;
      color: #6c757d;
    }
    .remediation-item {
      margin-bottom: 15px;
      padding: 15px;
      border-left: 4px solid #0a84ff;
      background-color: white;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    .remediation-title {
      font-weight: bold;
      margin-bottom: 10px;
    }
    .remediation-details {
      margin-left: 20px;
      font-size: 14px;
    }
    .step-item {
      margin: 5px 0;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>NovaFlowX ML Engine Results</h1>
    
    <div class="card">
      <h2>Analysis Summary</h2>
      <div class="stats-card">
        <div class="stat-item">
          <div class="stat-label">CSDE Value</div>
          <div class="stat-value">1094991.59</div>
        </div>
        <div class="stat-item">
          <div class="stat-label">Performance Factor</div>
          <div class="stat-value">3142.00x</div>
        </div>
        <div class="stat-item">
          <div class="stat-label">ML Enhanced</div>
          <div class="stat-value">Yes</div>
        </div>
      </div>
    </div>
    
    <div class="card">
      <h2>Remediation Summary</h2>
      <div class="stats-card">
        <div class="stat-item">
          <div class="stat-label">Actions Selected</div>
          <div class="stat-value">3</div>
        </div>
        <div class="stat-item">
          <div class="stat-label">Actions Remediated</div>
          <div class="stat-value">3</div>
        </div>
        <div class="stat-item">
          <div class="stat-label">Success Rate</div>
          <div class="stat-value">100.00%</div>
        </div>
      </div>
    </div>
    
    <div class="card">
      <h2>Remediation Statistics</h2>
      <div class="chart-container">
        <canvas id="remediationChart"></canvas>
      </div>
    </div>
    
    <div class="card">
      <h2>Remediation Details</h2>
      
        <div class="remediation-item">
          <div class="remediation-title priority-critical">
            1. Remediate Account Management (CRITICAL)
            <span class="status-badge status-success status-dryrun">
              DRY RUN
            </span>
          </div>
          <div class="remediation-details">
            <p>Implement controls to address: The organization needs to implement account management procedures</p>
            <p><strong>Type:</strong> compliance</p>
            <p><strong>Automation Potential:</strong> high</p>
            <p><strong>Message:</strong> Dry run - no changes made</p>
            
              <p><strong>Remediation Plan:</strong></p>
              <ul>
                
                  <li class="step-item">Review NIST 800-53 requirements for NIST-AC-2</li>
                
                  <li class="step-item">Develop implementation plan for Account Management</li>
                
                  <li class="step-item">Implement required controls</li>
                
                  <li class="step-item">Document evidence of implementation</li>
                
                  <li class="step-item">Verify effectiveness of controls</li>
                
              </ul>
            
          </div>
        </div>
      
        <div class="remediation-item">
          <div class="remediation-title priority-high">
            2. Remediate Least Functionality (HIGH)
            <span class="status-badge status-success status-dryrun">
              DRY RUN
            </span>
          </div>
          <div class="remediation-details">
            <p>Implement controls to address: The organization needs to configure systems to provide only essential capabilities</p>
            <p><strong>Type:</strong> compliance</p>
            <p><strong>Automation Potential:</strong> high</p>
            <p><strong>Message:</strong> Dry run - no changes made</p>
            
              <p><strong>Remediation Plan:</strong></p>
              <ul>
                
                  <li class="step-item">Review NIST 800-53 requirements for NIST-CM-7</li>
                
                  <li class="step-item">Develop implementation plan for Least Functionality</li>
                
                  <li class="step-item">Implement required controls</li>
                
                  <li class="step-item">Document evidence of implementation</li>
                
                  <li class="step-item">Verify effectiveness of controls</li>
                
              </ul>
            
          </div>
        </div>
      
        <div class="remediation-item">
          <div class="remediation-title priority-medium">
            3. Optimize IAM Role Configuration (MEDIUM)
            <span class="status-badge status-success status-dryrun">
              DRY RUN
            </span>
          </div>
          <div class="remediation-details">
            <p>Enhance GCP configuration: IAM roles need to be configured with least privilege</p>
            <p><strong>Type:</strong> gcp</p>
            <p><strong>Automation Potential:</strong> high</p>
            <p><strong>Message:</strong> Dry run - no changes made</p>
            
              <p><strong>Remediation Plan:</strong></p>
              <ul>
                
                  <li class="step-item">Review current configuration of Cloud IAM</li>
                
                  <li class="step-item">Identify optimization opportunities for IAM Role Configuration</li>
                
                  <li class="step-item">Implement recommended configurations</li>
                
                  <li class="step-item">Test and validate changes</li>
                
                  <li class="step-item">Document updated configuration</li>
                
              </ul>
            
          </div>
        </div>
      
    </div>
    
    <div class="card">
      <h2>ML Insights</h2>
      <h3>Status Analysis</h3>
      <table class="metrics">
        <tr>
          <th>Domain</th>
          <th>Level</th>
          <th>Score</th>
          <th>Details</th>
        </tr>
        <tr>
          <td>Compliance</td>
          <td>moderate</td>
          <td>50.00%</td>
          <td>
            Compliant: 1<br>
            Partial: 1<br>
            Non-Compliant: 1
          </td>
        </tr>
        <tr>
          <td>GCP</td>
          <td>moderate</td>
          <td>50.00%</td>
          <td>
            Optimal: 1<br>
            Partial: 1<br>
            Non-Optimal: 1
          </td>
        </tr>
        <tr>
          <td>Cyber-Safety</td>
          <td>moderate</td>
          <td>50.00%</td>
          <td>
            Implemented: 1<br>
            Partial: 1<br>
            Not Implemented: 1
          </td>
        </tr>
      </table>
      
      <h3>Improvement Areas</h3>
      <table class="metrics">
        <tr>
          <th>Area</th>
          <th>Priority</th>
          <th>Description</th>
          <th>Contribution</th>
        </tr>
        
      </table>
    </div>
  </div>
  
  <script>
    // Remediation chart
    const remediationCtx = document.getElementById('remediationChart').getContext('2d');
    new Chart(remediationCtx, {
      type: 'bar',
      data: {
        labels: ['Total', 'Selected', 'Remediated', 'Failed'],
        datasets: [{
          label: 'Remediation Actions',
          data: [
            5,
            3,
            3,
            0
          ],
          backgroundColor: [
            'rgba(75, 192, 192, 0.6)',
            'rgba(54, 162, 235, 0.6)',
            'rgba(75, 192, 192, 0.6)',
            'rgba(255, 99, 132, 0.6)'
          ],
          borderColor: [
            'rgba(75, 192, 192, 1)',
            'rgba(54, 162, 235, 1)',
            'rgba(75, 192, 192, 1)',
            'rgba(255, 99, 132, 1)'
          ],
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true,
            title: {
              display: true,
              text: 'Number of Actions'
            }
          },
          x: {
            title: {
              display: true,
              text: 'Remediation Status'
            }
          }
        },
        plugins: {
          title: {
            display: true,
            text: 'Remediation Actions Summary'
          }
        }
      }
    });
  </script>
</body>
</html>
