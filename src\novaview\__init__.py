"""
NovaView (NUCV) - Universal Compliance Visualization.

A system for visualizing compliance status across multiple frameworks with
dynamic UI rendering based on API schemas.

Key Differentiation: Unified regulatory "command center"
"""

# Import from legacy module for backward compatibility
try:
    from ucto.dashboard import DashboardManager, DashboardAPI, DashboardIntegration
    has_dashboard = True
except ImportError:
    has_dashboard = False
    DashboardManager = None
    DashboardAPI = None
    DashboardIntegration = None

__version__ = '0.1.0'
__all__ = []

# Add dashboard components to __all__ if available
if has_dashboard:
    __all__.extend(['DashboardManager', 'DashboardAPI', 'DashboardIntegration'])
