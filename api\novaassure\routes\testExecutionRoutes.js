/**
 * Test Execution Routes
 * 
 * This file defines the routes for test execution.
 */

const express = require('express');
const router = express.Router();
const testExecutionController = require('../controllers/testExecutionController');
const authMiddleware = require('../middleware/authMiddleware');

/**
 * @swagger
 * /api/v1/novaassure/test-execution:
 *   get:
 *     summary: Get all test executions
 *     description: Retrieve a list of all test executions
 *     tags: [Test Execution]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: testPlanId
 *         schema:
 *           type: string
 *         description: Filter by test plan ID
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [pending, in-progress, completed, failed]
 *         description: Filter by status
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Items per page
 *     responses:
 *       200:
 *         description: List of test executions
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/', authMiddleware.authenticate, testExecutionController.getAllTestExecutions);

/**
 * @swagger
 * /api/v1/novaassure/test-execution/{id}:
 *   get:
 *     summary: Get test execution by ID
 *     description: Retrieve a test execution by its ID
 *     tags: [Test Execution]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Test execution ID
 *     responses:
 *       200:
 *         description: Test execution details
 *       404:
 *         description: Test execution not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/:id', authMiddleware.authenticate, testExecutionController.getTestExecutionById);

/**
 * @swagger
 * /api/v1/novaassure/test-execution/start:
 *   post:
 *     summary: Start test execution
 *     description: Start a new test execution
 *     tags: [Test Execution]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - testPlanId
 *             properties:
 *               testPlanId:
 *                 type: string
 *                 description: Test plan ID
 *               executedBy:
 *                 type: string
 *                 description: User ID executing the test
 *               notes:
 *                 type: string
 *                 description: Execution notes
 *     responses:
 *       201:
 *         description: Test execution started
 *       400:
 *         description: Invalid request
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post('/start', authMiddleware.authenticate, testExecutionController.startTestExecution);

/**
 * @swagger
 * /api/v1/novaassure/test-execution/{id}/complete:
 *   post:
 *     summary: Complete test execution
 *     description: Complete a test execution
 *     tags: [Test Execution]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Test execution ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - results
 *             properties:
 *               results:
 *                 type: array
 *                 items:
 *                   type: object
 *                   required:
 *                     - controlId
 *                     - status
 *                   properties:
 *                     controlId:
 *                       type: string
 *                       description: Control ID
 *                     status:
 *                       type: string
 *                       enum: [pass, fail, not-applicable]
 *                       description: Test result status
 *                     notes:
 *                       type: string
 *                       description: Test result notes
 *                     evidenceIds:
 *                       type: array
 *                       items:
 *                         type: string
 *                       description: Evidence IDs
 *               notes:
 *                 type: string
 *                 description: Execution notes
 *     responses:
 *       200:
 *         description: Test execution completed
 *       400:
 *         description: Invalid request
 *       404:
 *         description: Test execution not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post('/:id/complete', authMiddleware.authenticate, testExecutionController.completeTestExecution);

/**
 * @swagger
 * /api/v1/novaassure/test-execution/{id}/cancel:
 *   post:
 *     summary: Cancel test execution
 *     description: Cancel a test execution
 *     tags: [Test Execution]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Test execution ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - reason
 *             properties:
 *               reason:
 *                 type: string
 *                 description: Cancellation reason
 *     responses:
 *       200:
 *         description: Test execution cancelled
 *       400:
 *         description: Invalid request
 *       404:
 *         description: Test execution not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post('/:id/cancel', authMiddleware.authenticate, testExecutionController.cancelTestExecution);

/**
 * @swagger
 * /api/v1/novaassure/test-execution/{id}/result:
 *   post:
 *     summary: Add test result
 *     description: Add a test result to a test execution
 *     tags: [Test Execution]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Test execution ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - controlId
 *               - status
 *             properties:
 *               controlId:
 *                 type: string
 *                 description: Control ID
 *               status:
 *                 type: string
 *                 enum: [pass, fail, not-applicable]
 *                 description: Test result status
 *               notes:
 *                 type: string
 *                 description: Test result notes
 *               evidenceIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Evidence IDs
 *     responses:
 *       200:
 *         description: Test result added
 *       400:
 *         description: Invalid request
 *       404:
 *         description: Test execution not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post('/:id/result', authMiddleware.authenticate, testExecutionController.addTestResult);

/**
 * @swagger
 * /api/v1/novaassure/test-execution/schedule:
 *   post:
 *     summary: Schedule automated test execution
 *     description: Schedule an automated test execution
 *     tags: [Test Execution]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - testPlanId
 *               - schedule
 *             properties:
 *               testPlanId:
 *                 type: string
 *                 description: Test plan ID
 *               schedule:
 *                 type: object
 *                 properties:
 *                   frequency:
 *                     type: string
 *                     enum: [daily, weekly, monthly, quarterly, annually]
 *                     description: Test frequency
 *                   startDate:
 *                     type: string
 *                     format: date
 *                     description: Start date
 *                   endDate:
 *                     type: string
 *                     format: date
 *                     description: End date
 *               notifyUsers:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: User IDs to notify
 *     responses:
 *       201:
 *         description: Test execution scheduled
 *       400:
 *         description: Invalid request
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post('/schedule', authMiddleware.authenticate, testExecutionController.scheduleAutomatedTestExecution);

/**
 * @swagger
 * /api/v1/novaassure/test-execution/automated:
 *   post:
 *     summary: Run automated test
 *     description: Run an automated test
 *     tags: [Test Execution]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - testPlanId
 *             properties:
 *               testPlanId:
 *                 type: string
 *                 description: Test plan ID
 *               parameters:
 *                 type: object
 *                 description: Test parameters
 *     responses:
 *       202:
 *         description: Automated test started
 *       400:
 *         description: Invalid request
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post('/automated', authMiddleware.authenticate, testExecutionController.runAutomatedTest);

module.exports = router;
