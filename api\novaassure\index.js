/**
 * NovaAssure (UCTF) - Universal Control Testing Framework
 *
 * This module provides functionality for automated compliance testing and evidence collection.
 */

const path = require('path');
const fs = require('fs');
const logger = require('./utils/logger');

// Import routes
const routes = require('./routes');

// Import models
const models = require('./models');

// Import services
const services = require('./services');

// Create uploads directory if it doesn't exist
const uploadsDir = path.join(__dirname, '../../uploads/evidence');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// Create reports directory if it doesn't exist
const reportsDir = path.join(__dirname, '../../reports');
if (!fs.existsSync(reportsDir)) {
  fs.mkdirSync(reportsDir, { recursive: true });
}

// Create templates directory if it doesn't exist
const templatesDir = path.join(__dirname, './templates');
if (!fs.existsSync(templatesDir)) {
  fs.mkdirSync(templatesDir, { recursive: true });
}

/**
 * Initialize NovaAssure
 * @param {Object} app - Express app
 * @param {Object} config - Configuration object
 */
function initialize(app, config) {
  // Initialize routes
  app.use('/api/v1/novaassure', routes);

  // Log initialization
  logger.info('NovaAssure (UCTF) initialized');
}

module.exports = {
  initialize,
  routes,
  services,
  models
};
