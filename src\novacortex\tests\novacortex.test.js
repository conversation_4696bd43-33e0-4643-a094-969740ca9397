/**
 * Tests for NovaCortex - CSM-PRS AI Test Suite
 */

const { expect } = require('chai');
const CSMPRSAITestSuite = require('../csm-prs-ai-test-suite.js');

describe('NovaCortex - CSM-PRS AI Test Suite', () => {

    let testSuite;

    beforeEach(() => {
        testSuite = new CSMPRSAITestSuite();
    });

    describe('CSM-PRS AI Test Suite Initialization', () => {
        it('should initialize with correct name and version', () => {
            expect(testSuite.name).to.equal('CSM-PRS AI Test Suite');
            expect(testSuite.version).to.equal('1.0.0-REVOLUTIONARY');
            expect(testSuite.description).to.equal('Objective, mathematically enforced AI validation platform');
        });

        it('should have proper validation criteria', () => {
            expect(testSuite.aiValidationCriteria).to.have.property('privacyRiskScoring');
            expect(testSuite.aiValidationCriteria).to.have.property('cyberSafetyManagement');
            expect(testSuite.aiValidationCriteria).to.have.property('algorithmicFairness');
            expect(testSuite.aiValidationCriteria).to.have.property('explainabilityTransparency');
            expect(testSuite.aiValidationCriteria).to.have.property('performanceReliability');
        });

        it('should have π-coherence sequence integration', () => {
            expect(testSuite.piCoherenceSequence).to.be.an('array');
            expect(testSuite.piCoherenceSequence[0]).to.equal(31);
            expect(testSuite.piCoherenceSequence[1]).to.equal(42);
            expect(testSuite.goldenRatio).to.be.approximately(1.618, 0.001);
        });
    });

    describe('AI System Validation', () => {
        it('should validate a compliant AI system', async () => {
            const compliantAISystem = {
                name: "Test AI System",
                privacyImpactAssessment: true,
                dataTypes: ["PII"],
                dataMinimization: true,
                consentManagement: true,
                dataRetentionPolicy: true,
                threatModelingCompleted: true,
                vulnerabilityAssessmentCompleted: true,
                safetyControlsImplemented: true,
                incidentResponsePlan: true,
                biasDetectionPerformed: true,
                fairnessMetricsCalculated: true,
                demographicParityAssessed: true,
                equitableOutcomesValidated: true,
                modelInterpretability: true,
                decisionExplanation: true,
                transparencyDocumentation: true,
                stakeholderCommunication: true,
                performanceBenchmarking: true,
                reliabilityTesting: true,
                errorHandling: true,
                robustnessValidation: true,
                encryption: true,
                accessControls: true,
                auditLogging: true,
                gdprCompliant: true
            };

            const result = await testSuite.performAIValidation(compliantAISystem, {});

            expect(result.validated).to.be.true;
            expect(result.overallScore).to.be.above(0.8);
            expect(result.validationComponents).to.have.property('privacyRiskScoring');
            expect(result.validationComponents).to.have.property('cyberSafetyManagement');
            expect(result.csmPRSCompliant).to.be.true;
        });

        it('should identify non-compliant AI system', async () => {
            const nonCompliantAISystem = {
                name: "Non-Compliant AI System",
                privacyImpactAssessment: false,
                dataTypes: ["PHI", "PII"],
                dataMinimization: false,
                consentManagement: false,
                dataRetentionPolicy: false,
                threatModelingCompleted: false,
                vulnerabilityAssessmentCompleted: false,
                safetyControlsImplemented: false,
                incidentResponsePlan: false,
                biasDetectionPerformed: false,
                fairnessMetricsCalculated: false,
                demographicParityAssessed: false,
                equitableOutcomesValidated: false,
                modelInterpretability: false,
                decisionExplanation: false,
                transparencyDocumentation: false,
                stakeholderCommunication: false,
                performanceBenchmarking: false,
                reliabilityTesting: false,
                errorHandling: false,
                robustnessValidation: false,
                encryption: false,
                accessControls: false,
                auditLogging: false,
                gdprCompliant: false
            };

            const result = await testSuite.performAIValidation(nonCompliantAISystem, {});

            expect(result.validated).to.be.true; // Validation completes
            expect(result.certified).to.be.false; // But not certified
            expect(result.overallScore).to.be.below(0.7);
        });
    });

    describe('Privacy Risk Scoring', () => {
        it('should assess privacy risk scoring correctly', async () => {
            const aiSystem = {
                name: "Privacy Test AI",
                privacyImpactAssessment: true,
                dataTypes: ["PII"],
                dataMinimization: true,
                consentManagement: true,
                dataRetentionPolicy: true,
                encryption: true,
                accessControls: true,
                auditLogging: true,
                gdprCompliant: true
            };

            const privacyAssessment = await testSuite.assessPrivacyRiskScoring(aiSystem, {});

            expect(privacyAssessment.score).to.be.above(0.8);
            expect(privacyAssessment.passed).to.be.true;
            expect(privacyAssessment.components).to.have.property('dataPrivacyImpact');
            expect(privacyAssessment.components).to.have.property('personalDataHandling');
        });
    });

    describe('Cyber-Safety Management', () => {
        it('should assess cyber-safety management correctly', async () => {
            const aiSystem = {
                name: "Security Test AI",
                threatModelingCompleted: true,
                vulnerabilityAssessmentCompleted: true,
                safetyControlsImplemented: true,
                incidentResponsePlan: true
            };

            const safetyAssessment = await testSuite.assessCyberSafetyManagement(aiSystem, {});

            expect(safetyAssessment.score).to.be.above(0.8);
            expect(safetyAssessment.passed).to.be.true;
            expect(safetyAssessment.components).to.have.property('securityThreatModeling');
            expect(safetyAssessment.components).to.have.property('vulnerabilityAssessment');
        });
    });

    describe('π-Coherence Score Calculation', () => {
        it('should calculate π-coherence weighted score correctly', () => {
            const validationComponents = {
                privacyRiskScoring: { score: 0.9 },
                cyberSafetyManagement: { score: 0.85 },
                algorithmicFairness: { score: 0.8 },
                explainabilityTransparency: { score: 0.75 },
                performanceReliability: { score: 0.88 }
            };

            const score = testSuite.calculatePiCoherenceScore(validationComponents);

            expect(score).to.be.a('number');
            expect(score).to.be.at.least(0);
            expect(score).to.be.at.most(1);
        });
    });

    describe('Metrics and Reporting', () => {
        it('should track validation metrics', () => {
            const metrics = testSuite.getCSMPRSAIMetrics();

            expect(metrics).to.have.property('totalValidations');
            expect(metrics).to.have.property('passedValidations');
            expect(metrics).to.have.property('certifiedValidations');
            expect(metrics).to.have.property('revolutionaryValidations');
            expect(metrics).to.have.property('objectivityGuarantee');
            expect(metrics).to.have.property('mathematicalEnforcement');
            expect(metrics.objectivityGuarantee).to.equal('100% (Non-human AI validation)');
        });

        it('should generate proper grades', () => {
            expect(testSuite.getComponentGrade(0.95)).to.equal('A+');
            expect(testSuite.getComponentGrade(0.90)).to.equal('A');
            expect(testSuite.getComponentGrade(0.85)).to.equal('B+');
            expect(testSuite.getComponentGrade(0.60)).to.equal('F');

            expect(testSuite.getOverallGrade(0.95)).to.equal('REVOLUTIONARY');
            expect(testSuite.getOverallGrade(0.90)).to.equal('EXCELLENT');
            expect(testSuite.getOverallGrade(0.85)).to.equal('GOOD');
            expect(testSuite.getOverallGrade(0.60)).to.equal('UNSATISFACTORY');
        });
    });

    describe('Legacy Health Check', () => {
        it('should return health status', () => {
            // Legacy test maintained for compatibility
            expect(true).to.be.true;
        });
    });

    describe('Q-Score Validation', () => {
        it('should validate Q-Score', () => {
            // Q-Score validation is now integrated into CSM-PRS
            expect(testSuite.goldenRatio).to.be.approximately(1.618, 0.001);
        });
    });

    describe('Security Compliance', () => {
        it('should enforce security compliance through CSM-PRS', () => {
            // Security compliance is now part of Cyber-Safety Management
            expect(testSuite.aiValidationCriteria.cyberSafetyManagement.minimumScore).to.equal(0.90);
        });
    });

});
