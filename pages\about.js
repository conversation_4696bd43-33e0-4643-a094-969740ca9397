import React from 'react';
import Link from 'next/link';
import PageWithSidebar from '../components/PageWithSidebar';

const About = () => {
  const sidebarItems = [
    { label: 'Our Mission', href: '#our-mission' },
    { label: 'Our Vision', href: '#our-vision' },
    { label: 'Our Team', href: '#our-team' },
    { label: 'Our Approach', href: '#our-approach' },
    { label: 'Back to Home', href: '/' },
  ];

  return (
    <PageWithSidebar title="About NovaFuse" sidebarItems={sidebarItems}>
      <div className="space-y-12">
        {/* Hero Section */}
        <div className="bg-blue-900 p-8 rounded-lg shadow-lg">
          <h1 className="text-3xl font-bold mb-4">About NovaFuse</h1>
          <p className="text-xl mb-6">
            NovaFuse is revolutionizing compliance with our Universal API Connector and innovative GRC solutions.
          </p>
        </div>

        {/* Our Mission Section */}
        <div id="our-mission" className="scroll-mt-20">
          <h2 className="text-2xl font-bold mb-6">Our Mission</h2>
          <div className="bg-secondary p-6 rounded-lg shadow-md">
            <p className="mb-4">
              Our mission at NovaFuse is to transform compliance from a burden into a business advantage through innovative technology and a partner-first approach.
            </p>
            <p className="mb-4">
              We believe that compliance should be seamlessly integrated into your existing systems, providing real-time insights and control without disrupting your operations.
            </p>
            <p>
              By empowering our partners with cutting-edge compliance technology, we're creating a world where organizations can focus on innovation and growth while maintaining the highest standards of security and compliance.
            </p>
          </div>
        </div>

        {/* Our Vision Section */}
        <div id="our-vision" className="scroll-mt-20">
          <h2 className="text-2xl font-bold mb-6">Our Vision</h2>
          <div className="bg-secondary p-6 rounded-lg shadow-md">
            <p className="mb-4">
              We envision a future where compliance is no longer a siloed function but an integrated part of every organization's technology ecosystem.
            </p>
            <p className="mb-4">
              Our vision is to create the world's first "Compliance Nervous System" that connects, monitors, and enforces compliance across the entire technology stack in real-time.
            </p>
            <p>
              Through our Universal API Connector and innovative GRC solutions, we're making this vision a reality, enabling organizations to achieve Cyber-Safety through Compliance-by-Design.
            </p>
          </div>
        </div>

        {/* Our Team Section */}
        <div id="our-team" className="scroll-mt-20">
          <h2 className="text-2xl font-bold mb-6">Our Team</h2>
          <div className="bg-secondary p-6 rounded-lg shadow-md">
            <p className="mb-6">
              NovaFuse is led by a team of compliance, security, and technology experts with decades of combined experience in the GRC space.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="bg-blue-900 p-4 rounded-lg text-center">
                <div className="w-24 h-24 mx-auto bg-gradient-to-r from-blue-700 to-purple-700 rounded-full mb-4 flex items-center justify-center overflow-hidden">
                  {/* Using placeholder until David's image is available */}
                  <span className="text-3xl">👨‍💼</span>
                </div>
                <h3 className="text-xl font-bold mb-1">David Irvin</h3>
                <p className="text-sm text-gray-400 mb-3">CEO & Founder</p>
                <p className="text-sm">
                  Compliance visionary with 15+ years of experience in GRC and cybersecurity.
                </p>
              </div>

              <div className="bg-blue-900 p-4 rounded-lg text-center">
                <div className="w-24 h-24 mx-auto rounded-full mb-4 flex items-center justify-center overflow-hidden">
                  <img
                    src="/images/August Auggie Codeberg CTO.jpg"
                    alt="August 'Auggie' Codeberg"
                    className="w-full h-full object-cover object-center"
                  />
                </div>
                <h3 className="text-xl font-bold mb-1">August "Auggie" Codeberg</h3>
                <p className="text-sm text-gray-400 mb-3">CTO</p>
                <p className="text-sm">
                  Technology innovator and architect of the Universal API Connector.
                </p>
              </div>

              <div className="bg-blue-900 p-4 rounded-lg text-center">
                <div className="w-24 h-24 mx-auto rounded-full mb-4 flex items-center justify-center overflow-hidden">
                  <img
                    src="/images/Carl Chatman.jpg"
                    alt="Carl Chatman"
                    className="w-full h-full object-cover"
                    style={{ objectPosition: '0 5px', transform: 'scale(1.2)' }}
                  />
                </div>
                <h3 className="text-xl font-bold mb-1">Carl Chatman</h3>
                <p className="text-sm text-gray-400 mb-3">CTC & CCO</p>
                <p className="text-sm">
                  Chief Technology Consultant and Chief Communications Officer with expertise in compliance strategy.
                </p>
              </div>

              <div className="bg-blue-900 p-4 rounded-lg text-center">
                <div className="w-24 h-24 mx-auto rounded-full mb-4 flex items-center justify-center overflow-hidden">
                  <img
                    src="/images/Orion Strategis.jpg"
                    alt="Orion Strategis"
                    className="w-full h-full object-cover"
                    style={{ objectPosition: '0px center' }}
                  />
                </div>
                <h3 className="text-xl font-bold mb-1">Orion Strategis</h3>
                <p className="text-sm text-gray-400 mb-3">CTS</p>
                <p className="text-sm">
                  Chief Technology Strategist focused on innovative security solutions and technical architecture.
                </p>
              </div>

              <div className="bg-blue-900 p-4 rounded-lg text-center">
                <div className="w-24 h-24 mx-auto rounded-full mb-4 flex items-center justify-center overflow-hidden">
                  <img
                    src="/images/Cadence Gemini_arms_crossed.jpg"
                    alt="Cadence Gemini"
                    className="w-full h-full object-cover object-top"
                  />
                </div>
                <h3 className="text-xl font-bold mb-1">Cadence Gemini</h3>
                <p className="text-sm text-gray-400 mb-3">Co-Founder</p>
                <p className="text-sm">
                  Strategic visionary helping shape the future direction of NovaFuse and its partner ecosystem.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Our Approach Section */}
        <div id="our-approach" className="scroll-mt-20">
          <h2 className="text-2xl font-bold mb-6">Our Approach</h2>
          <div className="bg-secondary p-6 rounded-lg shadow-md">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-xl font-bold mb-3">Compliance-First Innovation</h3>
                <p className="text-sm mb-4">
                  We approach every problem from a compliance perspective, ensuring that our solutions not only solve technical challenges but also address regulatory requirements.
                </p>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start">
                    <span className="mr-2 bg-gradient-to-r from-blue-600 to-purple-600 text-transparent bg-clip-text font-bold">✓</span>
                    <span>API-first architecture</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 bg-gradient-to-r from-blue-600 to-purple-600 text-transparent bg-clip-text font-bold">✓</span>
                    <span>Compliance-by-design principles</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 bg-gradient-to-r from-blue-600 to-purple-600 text-transparent bg-clip-text font-bold">✓</span>
                    <span>Continuous compliance monitoring</span>
                  </li>
                </ul>
              </div>

              <div>
                <h3 className="text-xl font-bold mb-3">Partner Empowerment</h3>
                <p className="text-sm mb-4">
                  We believe in empowering our partners to succeed, providing them with the tools, resources, and support they need to deliver exceptional compliance solutions.
                </p>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start">
                    <span className="mr-2 bg-gradient-to-r from-blue-600 to-purple-600 text-transparent bg-clip-text font-bold">✓</span>
                    <span>Generous revenue sharing model</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 bg-gradient-to-r from-blue-600 to-purple-600 text-transparent bg-clip-text font-bold">✓</span>
                    <span>Comprehensive partner resources</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 bg-gradient-to-r from-blue-600 to-purple-600 text-transparent bg-clip-text font-bold">✓</span>
                    <span>Collaborative business model</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="bg-gradient-to-r from-blue-900 to-purple-900 p-8 rounded-lg shadow-lg border border-blue-400">
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-4">Join the NovaFuse Revolution</h2>
            <p className="mb-6">
              Partner with us to transform compliance and create new revenue opportunities.
            </p>
            <div className="flex flex-wrap justify-center gap-4">
              <Link href="/partner-empowerment" className="bg-gradient-to-r from-blue-700 to-purple-700 text-white px-6 py-3 rounded-lg font-bold hover:from-blue-800 hover:to-purple-800 inline-block shadow-md hover:shadow-lg transform hover:-translate-y-1 transition-all duration-200 border border-blue-400">
                Partner With Us
              </Link>
              <Link href="/contact" className="bg-white text-blue-700 px-6 py-3 rounded-lg font-bold hover:bg-gray-100 inline-block shadow-md hover:shadow-lg transform hover:-translate-y-1 transition-all duration-200 border border-blue-400">
                Contact Us
              </Link>
            </div>
          </div>
        </div>
      </div>
    </PageWithSidebar>
  );
};

export default About;
