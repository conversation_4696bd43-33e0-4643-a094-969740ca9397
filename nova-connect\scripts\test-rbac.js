/**
 * Test RBAC System
 *
 * This script runs the RBAC tests.
 */

const { spawn } = require('child_process');
const path = require('path');

// Run Jest with the RBAC test file
const jest = spawn('node', ['./node_modules/jest/bin/jest.js', 'tests/rbac/rbac.test.js', '--verbose'], {
  stdio: 'inherit',
  cwd: path.resolve(__dirname, '..')
});

jest.on('close', (code) => {
  console.log(`Jest process exited with code ${code}`);
  process.exit(code);
});
