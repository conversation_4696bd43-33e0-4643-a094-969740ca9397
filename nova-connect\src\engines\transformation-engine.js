/**
 * NovaConnect Transformation Engine
 * 
 * High-performance data normalization engine capable of transforming data
 * from various sources into a standardized format at sub-millisecond speeds.
 */

const { performance } = require('perf_hooks');

class TransformationEngine {
  constructor(options = {}) {
    this.options = {
      enableMetrics: true,
      enableCaching: true,
      ...options
    };
    
    // Initialize transformers registry
    this.transformers = {
      // String transformations
      lowercase: (value) => typeof value === 'string' ? value.toLowerCase() : value,
      uppercase: (value) => typeof value === 'string' ? value.toUpperCase() : value,
      trim: (value) => typeof value === 'string' ? value.trim() : value,
      
      // Date transformations
      isoToUnix: (value) => typeof value === 'string' ? new Date(value).getTime() : value,
      unixToIso: (value) => typeof value === 'number' ? new Date(value).toISOString() : value,
      
      // Number transformations
      toNumber: (value) => !isNaN(parseFloat(value)) ? parseFloat(value) : value,
      
      // Object transformations
      toJson: (value) => typeof value === 'object' ? JSON.stringify(value) : value,
      fromJson: (value) => {
        if (typeof value !== 'string') return value;
        try {
          return JSON.parse(value);
        } catch (e) {
          return value;
        }
      },
      
      // Array transformations
      join: (value, separator = ',') => Array.isArray(value) ? value.join(separator) : value,
      split: (value, separator = ',') => typeof value === 'string' ? value.split(separator) : value,
      
      // Security transformations
      mask: (value, pattern = 'xxxx') => {
        if (typeof value !== 'string') return value;
        return value.substring(0, 4) + pattern;
      }
    };
    
    // Initialize transformation cache
    this.cache = new Map();
  }
  
  /**
   * Register a custom transformer
   * @param {string} name - Name of the transformer
   * @param {Function} transformer - Transformer function
   */
  registerTransformer(name, transformer) {
    if (typeof transformer !== 'function') {
      throw new Error('Transformer must be a function');
    }
    
    this.transformers[name] = transformer;
  }
  
  /**
   * Transform data according to the provided rules
   * @param {Object} data - Source data to transform
   * @param {Array} rules - Transformation rules
   * @returns {Object} - Transformed data
   */
  transform(data, rules) {
    const startTime = this.options.enableMetrics ? performance.now() : 0;
    
    // Check cache if enabled
    if (this.options.enableCaching) {
      const cacheKey = this._generateCacheKey(data, rules);
      const cachedResult = this.cache.get(cacheKey);
      
      if (cachedResult) {
        return cachedResult;
      }
    }
    
    const result = {};
    
    // Apply each transformation rule
    for (const rule of rules) {
      try {
        // Get value from source path
        const value = this._getValueByPath(data, rule.source);
        
        // Apply transformation if specified
        let transformedValue = value;
        if (rule.transform) {
          if (typeof rule.transform === 'string' && this.transformers[rule.transform]) {
            // Single transformation
            transformedValue = this.transformers[rule.transform](value, rule.transformParams);
          } else if (Array.isArray(rule.transform)) {
            // Chain of transformations
            transformedValue = rule.transform.reduce((currentValue, transformName) => {
              if (this.transformers[transformName]) {
                return this.transformers[transformName](currentValue, rule.transformParams);
              }
              return currentValue;
            }, value);
          }
        }
        
        // Set value in target path
        this._setValueByPath(result, rule.target, transformedValue);
      } catch (error) {
        // Log error but continue with other rules
        console.error(`Error applying transformation rule: ${error.message}`, rule);
      }
    }
    
    // Cache result if caching is enabled
    if (this.options.enableCaching) {
      const cacheKey = this._generateCacheKey(data, rules);
      this.cache.set(cacheKey, result);
    }
    
    // Record metrics if enabled
    if (this.options.enableMetrics) {
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      // Store metrics for this transformation
      this._recordMetrics(duration, rules.length);
    }
    
    return result;
  }
  
  /**
   * Transform batch data according to the provided rules
   * @param {Object} data - Source data containing arrays
   * @param {Array} rules - Transformation rules with array paths
   * @returns {Object} - Transformed data
   */
  batchTransform(data, rules) {
    const startTime = this.options.enableMetrics ? performance.now() : 0;
    
    // Find array paths in rules
    const arrayPaths = rules.filter(rule => rule.source.includes('[*]'));
    
    // If no array paths, use regular transform
    if (arrayPaths.length === 0) {
      return this.transform(data, rules);
    }
    
    // Parse the source array path from the first array rule
    const sourceArrayPath = arrayPaths[0].source.split('[*]')[0];
    const sourceArray = this._getValueByPath(data, sourceArrayPath);
    
    if (!Array.isArray(sourceArray)) {
      throw new Error(`Path ${sourceArrayPath} does not point to an array`);
    }
    
    // Create result object with transformed items
    const result = {};
    const items = [];
    
    // Transform each item in the array
    for (const item of sourceArray) {
      // Create item-specific rules by replacing array notation
      const itemRules = rules.map(rule => {
        if (!rule.source.includes('[*]')) {
          return rule;
        }
        
        return {
          source: rule.source.replace(`${sourceArrayPath}[*]`, '').replace(/^\./, ''),
          target: rule.target.replace(/\[.*\]/, ''),
          transform: rule.transform,
          transformParams: rule.transformParams
        };
      });
      
      // Transform the item
      const transformedItem = this.transform({ item }, itemRules);
      items.push(transformedItem);
    }
    
    // Set the items in the result
    const targetArrayPath = arrayPaths[0].target.split('[*]')[0];
    this._setValueByPath(result, targetArrayPath, items);
    
    // Record metrics if enabled
    if (this.options.enableMetrics) {
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      // Store metrics for this batch transformation
      this._recordMetrics(duration, rules.length * sourceArray.length, true);
    }
    
    return result;
  }
  
  /**
   * Get metrics for the transformation engine
   * @returns {Object} - Metrics object
   */
  getMetrics() {
    return this.metrics;
  }
  
  /**
   * Clear the transformation cache
   */
  clearCache() {
    this.cache.clear();
  }
  
  /**
   * Generate a cache key for the data and rules
   * @private
   */
  _generateCacheKey(data, rules) {
    // Simple implementation - in production would use a more efficient approach
    return JSON.stringify({ data, rules });
  }
  
  /**
   * Record metrics for a transformation
   * @private
   */
  _recordMetrics(duration, rulesCount, isBatch = false) {
    if (!this.metrics) {
      this.metrics = {
        transformations: 0,
        totalDuration: 0,
        averageDuration: 0,
        batchTransformations: 0,
        batchTotalDuration: 0,
        batchAverageDuration: 0,
        rulesApplied: 0
      };
    }
    
    if (isBatch) {
      this.metrics.batchTransformations++;
      this.metrics.batchTotalDuration += duration;
      this.metrics.batchAverageDuration = this.metrics.batchTotalDuration / this.metrics.batchTransformations;
    } else {
      this.metrics.transformations++;
      this.metrics.totalDuration += duration;
      this.metrics.averageDuration = this.metrics.totalDuration / this.metrics.transformations;
    }
    
    this.metrics.rulesApplied += rulesCount;
  }
  
  /**
   * Get a value from an object by path
   * @private
   */
  _getValueByPath(obj, path) {
    if (!path) return obj;
    
    // Handle array notation
    const normalizedPath = path.replace(/\[(\w+)\]/g, '.$1');
    const parts = normalizedPath.split('.');
    
    let current = obj;
    for (const part of parts) {
      if (part === '') continue;
      if (current === null || current === undefined) return undefined;
      current = current[part];
    }
    
    return current;
  }
  
  /**
   * Set a value in an object by path
   * @private
   */
  _setValueByPath(obj, path, value) {
    if (!path) return;
    
    // Handle array notation
    const normalizedPath = path.replace(/\[(\w+)\]/g, '.$1');
    const parts = normalizedPath.split('.');
    
    let current = obj;
    for (let i = 0; i < parts.length - 1; i++) {
      const part = parts[i];
      if (part === '') continue;
      
      if (!(part in current)) {
        // Create object or array based on next part
        current[part] = parts[i + 1].match(/^\d+$/) ? [] : {};
      }
      current = current[part];
    }
    
    const lastPart = parts[parts.length - 1];
    if (lastPart !== '') {
      current[lastPart] = value;
    }
  }
}

module.exports = TransformationEngine;
