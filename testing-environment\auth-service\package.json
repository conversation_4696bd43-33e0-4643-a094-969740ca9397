{"name": "novafuse-auth-service", "version": "1.0.0", "description": "Authentication service for NovaConnect", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "dependencies": {"express": "^4.17.3", "mongoose": "^6.2.4", "cors": "^2.8.5", "body-parser": "^1.19.2", "crypto-js": "^4.1.1", "joi": "^17.6.0", "winston": "^3.6.0"}, "devDependencies": {"nodemon": "^2.0.15", "jest": "^27.5.1", "supertest": "^6.2.2"}}