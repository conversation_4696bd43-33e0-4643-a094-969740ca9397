/**
 * Tracing Middleware
 * 
 * This middleware integrates OpenTelemetry tracing with Express.
 */

const tracingService = require('../services/TracingService');
const logger = require('../utils/logger');

/**
 * Middleware to add tracing to Express routes
 */
const tracingMiddleware = (req, res, next) => {
  // The actual tracing is handled by the OpenTelemetry Express instrumentation
  // This middleware adds additional context and custom attributes
  
  const currentSpan = tracingService.getCurrentSpan();
  
  if (currentSpan) {
    // Add custom attributes to the current span
    currentSpan.setAttribute('http.request.headers.user_agent', req.headers['user-agent'] || '');
    currentSpan.setAttribute('http.request.headers.content_length', req.headers['content-length'] || 0);
    
    // Add user context if available
    if (req.user) {
      currentSpan.setAttribute('user.id', req.user.id);
      currentSpan.setAttribute('user.role', req.user.role || 'unknown');
    }
    
    // Add API key context if available
    if (req.apiKey) {
      currentSpan.setAttribute('api_key.id', req.apiKey.id);
    }
    
    // Add response listener to capture response attributes
    res.on('finish', () => {
      currentSpan.setAttribute('http.response.headers.content_length', res.getHeader('content-length') || 0);
      currentSpan.setAttribute('http.response.headers.content_type', res.getHeader('content-type') || '');
      
      // Add response status
      if (res.statusCode >= 400) {
        currentSpan.setStatus({
          code: opentelemetry.SpanStatusCode.ERROR,
          message: `HTTP ${res.statusCode}`
        });
      }
    });
  }
  
  next();
};

/**
 * Create a custom span for a route handler
 * @param {string} name - Span name
 */
const createRouteSpan = (name) => {
  return (req, res, next) => {
    return tracingService.withSpan(name, () => {
      return next();
    });
  };
};

/**
 * Error handler middleware for tracing
 */
const tracingErrorMiddleware = (err, req, res, next) => {
  // Record the error in the current span
  tracingService.recordException(err);
  
  // Continue to the next error handler
  next(err);
};

module.exports = {
  tracingMiddleware,
  createRouteSpan,
  tracingErrorMiddleware
};
