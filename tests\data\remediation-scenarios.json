[{"id": "scenario-0", "type": "compliance", "framework": "HIPAA", "control": "164.312(a)(1)", "severity": "high", "resource": {"id": "resource-0", "type": "compute.instance", "name": "resource-0", "provider": "gcp"}, "finding": {"id": "finding-0", "type": "vulnerability", "severity": "high", "resourceName": "projects/test-project/resources/resource-0", "resourceType": "compute.instance", "createdAt": 1689000000000, "description": "<PERSON><PERSON> finding 0 description"}, "remediationSteps": [{"id": "step-0-1", "action": "update-firewall-rule", "parameters": {"resourceId": "resource-0", "action": "restrict"}}, {"id": "step-0-2", "action": "generate-evidence", "parameters": {"findingId": "finding-0", "evidenceType": "remediation"}}]}, {"id": "scenario-1", "type": "compliance", "framework": "PCI-DSS", "control": "Requirement 3.4", "severity": "medium", "resource": {"id": "resource-1", "type": "storage.bucket", "name": "resource-1", "provider": "gcp"}, "finding": {"id": "finding-1", "type": "misconfiguration", "severity": "medium", "resourceName": "projects/test-project/resources/resource-1", "resourceType": "storage.bucket", "createdAt": 1689000060000, "description": "<PERSON><PERSON> finding 1 description"}, "remediationSteps": [{"id": "step-1-1", "action": "encrypt-bucket", "parameters": {"resourceId": "resource-1", "action": "restrict"}}, {"id": "step-1-2", "action": "generate-evidence", "parameters": {"findingId": "finding-1", "evidenceType": "remediation"}}]}, {"id": "scenario-2", "type": "compliance", "framework": "GDPR", "control": "Article 32", "severity": "low", "resource": {"id": "resource-2", "type": "bigquery.dataset", "name": "resource-2", "provider": "gcp"}, "finding": {"id": "finding-2", "type": "threat", "severity": "low", "resourceName": "projects/test-project/resources/resource-2", "resourceType": "bigquery.dataset", "createdAt": 1689000120000, "description": "<PERSON><PERSON> finding 2 description"}, "remediationSteps": [{"id": "step-2-1", "action": "update-access-controls", "parameters": {"resourceId": "resource-2", "action": "restrict"}}, {"id": "step-2-2", "action": "generate-evidence", "parameters": {"findingId": "finding-2", "evidenceType": "remediation"}}]}]