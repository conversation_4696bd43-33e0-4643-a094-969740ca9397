FROM node:18

WORKDIR /app

# Copy package.json and package-lock.json
COPY package*.json ./

# Install dependencies
RUN npm install

# Copy application code
COPY src/ ./src/
COPY . .

# Create directory for test results
RUN mkdir -p test-results

# Set environment variables
ENV NODE_ENV=test
ENV PORT=3010
ENV MONGODB_URI=mongodb://mongodb:27017/novacortex-test
ENV REDIS_URI=redis://redis:6379/0

# Expose port
EXPOSE 3010

# Health check
HEALTHCHECK --interval=10s --timeout=5s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:3010/health || exit 1

# Command to run NovaCortex server
CMD ["node", "src/novacortex/server.js"]
