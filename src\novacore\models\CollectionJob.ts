/**
 * CollectionJob.ts
 * 
 * Model for evidence collection jobs in the NovaCore system.
 * These jobs manage the automated collection of evidence from various data sources.
 */

import { v4 as uuidv4 } from 'uuid';
import { ConnectorType } from './Connector';

/**
 * Job status enum
 */
export enum JobStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
  SCHEDULED = 'scheduled',
}

/**
 * Job priority enum
 */
export enum JobPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

/**
 * Job type enum
 */
export enum JobType {
  SCHEDULED = 'scheduled',
  MANUAL = 'manual',
  TRIGGERED = 'triggered',
  RECURRING = 'recurring',
}

/**
 * Job result interface
 */
export interface JobResult {
  status: 'success' | 'partial' | 'failure';
  evidenceIds?: string[];
  errorMessage?: string;
  warningMessages?: string[];
  stats?: {
    totalItems: number;
    processedItems: number;
    successfulItems: number;
    failedItems: number;
  };
  metadata?: Record<string, any>;
}

/**
 * Collection job interface
 */
export interface CollectionJob {
  id: string;
  name: string;
  description?: string;
  connectorId: string;
  connectorType: ConnectorType;
  status: JobStatus;
  priority: JobPriority;
  type: JobType;
  organization: string;
  createdAt: Date;
  updatedAt: Date;
  scheduledAt?: Date;
  startedAt?: Date;
  completedAt?: Date;
  createdBy: string;
  parameters?: Record<string, any>;
  result?: JobResult;
  parentJobId?: string;
  childJobIds?: string[];
  retryCount?: number;
  maxRetries?: number;
  nextRetryAt?: Date;
  tags?: string[];
  metadata?: Record<string, any>;
}

/**
 * Create a new collection job
 */
export function createCollectionJob(
  name: string,
  connectorId: string,
  connectorType: ConnectorType,
  priority: JobPriority,
  type: JobType,
  createdBy: string,
  organization: string,
  description?: string,
  scheduledAt?: Date,
  parameters?: Record<string, any>,
  parentJobId?: string,
  maxRetries?: number,
  tags?: string[],
  metadata?: Record<string, any>,
): CollectionJob {
  const now = new Date();
  
  return {
    id: uuidv4(),
    name,
    description,
    connectorId,
    connectorType,
    status: scheduledAt && scheduledAt > now ? JobStatus.SCHEDULED : JobStatus.PENDING,
    priority,
    type,
    organization,
    createdAt: now,
    updatedAt: now,
    scheduledAt,
    createdBy,
    parameters,
    parentJobId,
    retryCount: 0,
    maxRetries,
    tags,
    metadata,
  };
}

/**
 * Update job status
 */
export function updateJobStatus(
  job: CollectionJob,
  status: JobStatus,
  details?: Record<string, any>,
): CollectionJob {
  const now = new Date();
  let updatedJob = { ...job, status, updatedAt: now };
  
  switch (status) {
    case JobStatus.RUNNING:
      updatedJob.startedAt = now;
      break;
    case JobStatus.COMPLETED:
    case JobStatus.FAILED:
    case JobStatus.CANCELLED:
      updatedJob.completedAt = now;
      break;
  }
  
  if (details) {
    updatedJob.metadata = { ...updatedJob.metadata, statusDetails: details };
  }
  
  return updatedJob;
}

/**
 * Set job result
 */
export function setJobResult(
  job: CollectionJob,
  result: JobResult,
): CollectionJob {
  const now = new Date();
  
  return {
    ...job,
    result,
    status: result.status === 'success' ? JobStatus.COMPLETED : JobStatus.FAILED,
    completedAt: now,
    updatedAt: now,
  };
}

/**
 * Schedule job retry
 */
export function scheduleJobRetry(
  job: CollectionJob,
  retryDelay: number = 300000, // 5 minutes in milliseconds
): CollectionJob {
  const now = new Date();
  const nextRetryAt = new Date(now.getTime() + retryDelay);
  
  // Only retry if we haven't exceeded max retries
  if (job.maxRetries !== undefined && job.retryCount !== undefined && job.retryCount >= job.maxRetries) {
    return {
      ...job,
      status: JobStatus.FAILED,
      updatedAt: now,
      metadata: {
        ...job.metadata,
        retryExhausted: true,
        retryExhaustedAt: now,
      },
    };
  }
  
  return {
    ...job,
    status: JobStatus.SCHEDULED,
    updatedAt: now,
    nextRetryAt,
    retryCount: (job.retryCount || 0) + 1,
    metadata: {
      ...job.metadata,
      lastRetryAt: now,
      retryReason: job.result?.errorMessage || 'Unknown error',
    },
  };
}

/**
 * Create child job
 */
export function createChildJob(
  parentJob: CollectionJob,
  name: string,
  parameters?: Record<string, any>,
  scheduledAt?: Date,
): CollectionJob {
  const childJob = createCollectionJob(
    name,
    parentJob.connectorId,
    parentJob.connectorType,
    parentJob.priority,
    JobType.TRIGGERED,
    parentJob.createdBy,
    parentJob.organization,
    `Child job of ${parentJob.name}`,
    scheduledAt,
    parameters,
    parentJob.id,
    parentJob.maxRetries,
    parentJob.tags,
    parentJob.metadata,
  );
  
  // Update parent job with child job ID
  const updatedParentJob = {
    ...parentJob,
    childJobIds: [...(parentJob.childJobIds || []), childJob.id],
    updatedAt: new Date(),
  };
  
  return childJob;
}
