/**
 * NovaCore Framework Service
 * 
 * This service provides functionality for managing compliance frameworks.
 * NovaPulse is the Universal Regulatory Compliance Monitoring System (URCMS) component
 * of the NovaFuse Cyber-Safety Platform.
 */

const { Framework } = require('../models');
const logger = require('../../../config/logger');
const { ValidationError, NotFoundError } = require('../../../api/utils/errors');

class FrameworkService {
  /**
   * Create a new framework
   * @param {Object} data - Framework data
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Created framework
   */
  async createFramework(data, userId) {
    try {
      logger.info('Creating new framework', { name: data.name });
      
      // Set created by and updated by
      data.createdBy = userId;
      data.updatedBy = userId;
      
      // Create framework
      const framework = new Framework(data);
      await framework.save();
      
      logger.info('Framework created successfully', { id: framework._id });
      
      return framework;
    } catch (error) {
      logger.error('Error creating framework', { error });
      throw error;
    }
  }
  
  /**
   * Get all frameworks
   * @param {Object} filter - Filter criteria
   * @param {Object} options - Query options (pagination, sorting)
   * @returns {Promise<Object>} - Frameworks with pagination info
   */
  async getAllFrameworks(filter = {}, options = {}) {
    try {
      const { page = 1, limit = 10, sort = { name: 1 } } = options;
      
      // Build query
      const query = {};
      
      // Apply filters
      if (filter.name) {
        query.name = { $regex: filter.name, $options: 'i' };
      }
      
      if (filter.shortName) {
        query.shortName = { $regex: filter.shortName, $options: 'i' };
      }
      
      if (filter.type) {
        query.type = filter.type;
      }
      
      if (filter.category) {
        query.category = filter.category;
      }
      
      if (filter.status) {
        query.status = filter.status;
      }
      
      if (filter.country) {
        query['$or'] = [
          { 'jurisdiction.country': filter.country },
          { 'jurisdiction.isGlobal': true }
        ];
      }
      
      if (filter.region) {
        query['$or'] = [
          { 'jurisdiction.region': filter.region },
          { 'jurisdiction.isGlobal': true }
        ];
      }
      
      if (filter.industry) {
        query['$or'] = [
          { industry: filter.industry },
          { 'applicability.industries': filter.industry }
        ];
      }
      
      if (filter.organizationType) {
        query['applicability.organizationTypes'] = filter.organizationType;
      }
      
      if (filter.dataType) {
        query['applicability.dataTypes'] = filter.dataType;
      }
      
      if (filter.isCustom !== undefined) {
        query.isCustom = filter.isCustom === 'true' || filter.isCustom === true;
      }
      
      if (filter.organizationId) {
        query['$or'] = [
          { organizationId: filter.organizationId },
          { isCustom: false }
        ];
      }
      
      if (filter.search) {
        query['$or'] = [
          { name: { $regex: filter.search, $options: 'i' } },
          { shortName: { $regex: filter.search, $options: 'i' } },
          { description: { $regex: filter.search, $options: 'i' } }
        ];
      }
      
      // Execute query with pagination
      const skip = (page - 1) * limit;
      
      const [frameworks, total] = await Promise.all([
        Framework.find(query)
          .sort(sort)
          .skip(skip)
          .limit(limit),
        Framework.countDocuments(query)
      ]);
      
      // Calculate pagination info
      const totalPages = Math.ceil(total / limit);
      const hasNext = page < totalPages;
      const hasPrev = page > 1;
      
      return {
        data: frameworks,
        pagination: {
          total,
          page,
          limit,
          totalPages,
          hasNext,
          hasPrev
        }
      };
    } catch (error) {
      logger.error('Error getting frameworks', { error });
      throw error;
    }
  }
  
  /**
   * Get framework by ID
   * @param {string} id - Framework ID
   * @returns {Promise<Object>} - Framework
   */
  async getFrameworkById(id) {
    try {
      const framework = await Framework.findById(id);
      
      if (!framework) {
        throw new NotFoundError(`Framework with ID ${id} not found`);
      }
      
      return framework;
    } catch (error) {
      logger.error('Error getting framework by ID', { id, error });
      throw error;
    }
  }
  
  /**
   * Update framework
   * @param {string} id - Framework ID
   * @param {Object} data - Updated framework data
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Updated framework
   */
  async updateFramework(id, data, userId) {
    try {
      // Get existing framework
      const framework = await this.getFrameworkById(id);
      
      // Set updated by
      data.updatedBy = userId;
      
      // Update framework
      Object.assign(framework, data);
      await framework.save();
      
      logger.info('Framework updated successfully', { id });
      
      return framework;
    } catch (error) {
      logger.error('Error updating framework', { id, error });
      throw error;
    }
  }
  
  /**
   * Delete framework
   * @param {string} id - Framework ID
   * @returns {Promise<boolean>} - Deletion success
   */
  async deleteFramework(id) {
    try {
      const result = await Framework.findByIdAndDelete(id);
      
      if (!result) {
        throw new NotFoundError(`Framework with ID ${id} not found`);
      }
      
      logger.info('Framework deleted successfully', { id });
      
      return true;
    } catch (error) {
      logger.error('Error deleting framework', { id, error });
      throw error;
    }
  }
  
  /**
   * Add control to framework
   * @param {string} id - Framework ID
   * @param {Object} control - Control data
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Updated framework
   */
  async addControl(id, control, userId) {
    try {
      // Get existing framework
      const framework = await this.getFrameworkById(id);
      
      // Check if control ID already exists
      if (framework.controls.some(c => c.id === control.id)) {
        throw new ValidationError(`Control with ID ${control.id} already exists`);
      }
      
      // Add control
      framework.controls.push(control);
      
      // Set updated by
      framework.updatedBy = userId;
      
      // Save framework
      await framework.save();
      
      logger.info('Control added successfully', { 
        frameworkId: id, 
        controlId: control.id 
      });
      
      return framework;
    } catch (error) {
      logger.error('Error adding control', { id, error });
      throw error;
    }
  }
  
  /**
   * Update control
   * @param {string} id - Framework ID
   * @param {string} controlId - Control ID
   * @param {Object} data - Updated control data
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Updated framework
   */
  async updateControl(id, controlId, data, userId) {
    try {
      // Get existing framework
      const framework = await this.getFrameworkById(id);
      
      // Find control
      const controlIndex = framework.controls.findIndex(c => c.id === controlId);
      
      if (controlIndex === -1) {
        throw new NotFoundError(`Control with ID ${controlId} not found`);
      }
      
      // Update control
      framework.controls[controlIndex] = {
        ...framework.controls[controlIndex].toObject(),
        ...data,
        id: controlId // Ensure ID doesn't change
      };
      
      // Set updated by
      framework.updatedBy = userId;
      
      // Save framework
      await framework.save();
      
      logger.info('Control updated successfully', { 
        frameworkId: id, 
        controlId 
      });
      
      return framework;
    } catch (error) {
      logger.error('Error updating control', { id, controlId, error });
      throw error;
    }
  }
  
  /**
   * Remove control
   * @param {string} id - Framework ID
   * @param {string} controlId - Control ID
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Updated framework
   */
  async removeControl(id, controlId, userId) {
    try {
      // Get existing framework
      const framework = await this.getFrameworkById(id);
      
      // Find control
      const controlIndex = framework.controls.findIndex(c => c.id === controlId);
      
      if (controlIndex === -1) {
        throw new NotFoundError(`Control with ID ${controlId} not found`);
      }
      
      // Remove control
      framework.controls.splice(controlIndex, 1);
      
      // Set updated by
      framework.updatedBy = userId;
      
      // Save framework
      await framework.save();
      
      logger.info('Control removed successfully', { 
        frameworkId: id, 
        controlId 
      });
      
      return framework;
    } catch (error) {
      logger.error('Error removing control', { id, controlId, error });
      throw error;
    }
  }
  
  /**
   * Add domain to framework
   * @param {string} id - Framework ID
   * @param {Object} domain - Domain data
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Updated framework
   */
  async addDomain(id, domain, userId) {
    try {
      // Get existing framework
      const framework = await this.getFrameworkById(id);
      
      // Check if domain ID already exists
      if (framework.domains.some(d => d.id === domain.id)) {
        throw new ValidationError(`Domain with ID ${domain.id} already exists`);
      }
      
      // Add domain
      framework.domains.push(domain);
      
      // Set updated by
      framework.updatedBy = userId;
      
      // Save framework
      await framework.save();
      
      logger.info('Domain added successfully', { 
        frameworkId: id, 
        domainId: domain.id 
      });
      
      return framework;
    } catch (error) {
      logger.error('Error adding domain', { id, error });
      throw error;
    }
  }
  
  /**
   * Add category to framework
   * @param {string} id - Framework ID
   * @param {Object} category - Category data
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Updated framework
   */
  async addCategory(id, category, userId) {
    try {
      // Get existing framework
      const framework = await this.getFrameworkById(id);
      
      // Check if category ID already exists
      if (framework.categories.some(c => c.id === category.id)) {
        throw new ValidationError(`Category with ID ${category.id} already exists`);
      }
      
      // Check if domain exists if domainId is provided
      if (category.domainId && !framework.domains.some(d => d.id === category.domainId)) {
        throw new ValidationError(`Domain with ID ${category.domainId} not found`);
      }
      
      // Add category
      framework.categories.push(category);
      
      // Set updated by
      framework.updatedBy = userId;
      
      // Save framework
      await framework.save();
      
      logger.info('Category added successfully', { 
        frameworkId: id, 
        categoryId: category.id 
      });
      
      return framework;
    } catch (error) {
      logger.error('Error adding category', { id, error });
      throw error;
    }
  }
  
  /**
   * Add version to framework
   * @param {string} id - Framework ID
   * @param {Object} version - Version data
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Updated framework
   */
  async addVersion(id, version, userId) {
    try {
      // Get existing framework
      const framework = await this.getFrameworkById(id);
      
      // Check if version number already exists
      if (framework.versions.some(v => v.versionNumber === version.versionNumber)) {
        throw new ValidationError(`Version ${version.versionNumber} already exists`);
      }
      
      // Add version
      framework.versions.push(version);
      
      // Update current version if specified
      if (version.status === 'effective') {
        framework.currentVersion = version.versionNumber;
      }
      
      // Set updated by
      framework.updatedBy = userId;
      
      // Save framework
      await framework.save();
      
      logger.info('Version added successfully', { 
        frameworkId: id, 
        versionNumber: version.versionNumber 
      });
      
      return framework;
    } catch (error) {
      logger.error('Error adding version', { id, error });
      throw error;
    }
  }
  
  /**
   * Find frameworks by type
   * @param {string} type - Framework type
   * @returns {Promise<Array>} - Frameworks
   */
  async findByType(type) {
    try {
      return await Framework.findByType(type);
    } catch (error) {
      logger.error('Error finding frameworks by type', { type, error });
      throw error;
    }
  }
  
  /**
   * Find frameworks by category
   * @param {string} category - Framework category
   * @returns {Promise<Array>} - Frameworks
   */
  async findByCategory(category) {
    try {
      return await Framework.findByCategory(category);
    } catch (error) {
      logger.error('Error finding frameworks by category', { category, error });
      throw error;
    }
  }
  
  /**
   * Find frameworks by jurisdiction
   * @param {string} country - Country
   * @param {string} region - Region
   * @returns {Promise<Array>} - Frameworks
   */
  async findByJurisdiction(country, region) {
    try {
      return await Framework.findByJurisdiction(country, region);
    } catch (error) {
      logger.error('Error finding frameworks by jurisdiction', { country, region, error });
      throw error;
    }
  }
  
  /**
   * Find frameworks by industry
   * @param {string} industry - Industry
   * @returns {Promise<Array>} - Frameworks
   */
  async findByIndustry(industry) {
    try {
      return await Framework.findByIndustry(industry);
    } catch (error) {
      logger.error('Error finding frameworks by industry', { industry, error });
      throw error;
    }
  }
  
  /**
   * Find frameworks by control mapping
   * @param {string} frameworkId - Framework ID
   * @param {string} controlId - Control ID
   * @returns {Promise<Array>} - Frameworks
   */
  async findByControlMapping(frameworkId, controlId) {
    try {
      return await Framework.findByControlMapping(frameworkId, controlId);
    } catch (error) {
      logger.error('Error finding frameworks by control mapping', { frameworkId, controlId, error });
      throw error;
    }
  }
  
  /**
   * Find frameworks by applicability
   * @param {Object} criteria - Applicability criteria
   * @returns {Promise<Array>} - Frameworks
   */
  async findByApplicability(criteria) {
    try {
      return await Framework.findByApplicability(criteria);
    } catch (error) {
      logger.error('Error finding frameworks by applicability', { criteria, error });
      throw error;
    }
  }
  
  /**
   * Find custom frameworks
   * @param {string} organizationId - Organization ID
   * @returns {Promise<Array>} - Frameworks
   */
  async findCustom(organizationId) {
    try {
      return await Framework.findCustom(organizationId);
    } catch (error) {
      logger.error('Error finding custom frameworks', { organizationId, error });
      throw error;
    }
  }
}

module.exports = new FrameworkService();
