{"name": "nova-ui", "version": "1.0.0", "description": "NovaUI: A comprehensive UI component library for the NovaFuse platform", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "repository": {"type": "git", "url": "git+https://github.com/Dartan1983/nova-ui.git"}, "keywords": ["ui", "components", "react", "nextjs", "novafuse"], "author": "NovaGRC", "license": "MIT", "bugs": {"url": "https://github.com/Dartan1983/nova-ui/issues"}, "homepage": "https://github.com/Dartan1983/nova-ui#readme", "dependencies": {"@headlessui/react": "^1.7.15", "@heroicons/react": "^2.0.18", "@reduxjs/toolkit": "^1.9.5", "axios": "^1.4.0", "chart.js": "^4.3.0", "next": "^13.4.12", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.45.2", "react-redux": "^8.1.1", "react-table": "^7.8.0", "redux-persist": "^6.0.0", "swr": "^2.2.0"}, "devDependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.4.3", "@types/node": "^20.4.5", "@types/react": "^18.2.17", "@types/react-dom": "^18.2.7", "autoprefixer": "^10.4.14", "eslint": "^8.46.0", "eslint-config-next": "^13.4.12", "jest": "^29.6.2", "jest-environment-jsdom": "^29.6.2", "postcss": "^8.4.27", "tailwindcss": "^3.3.3", "typescript": "^5.1.6"}}