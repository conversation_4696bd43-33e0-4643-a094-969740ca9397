# NovaConnect Testing Implementation - Phase 1

This document outlines the implementation plan for Phase 1 of the NovaConnect testing strategy, focusing on core technical validation.

## Phase 1 Objectives

1. Set up Docker-based testing environment
2. Implement test case for 50K events processing in 15 minutes
3. Validate sub-100ms normalization claim
4. Establish baseline performance metrics

## Implementation Steps

### 1. Docker Testing Environment Setup

#### 1.1 Create Docker Compose Configuration

Create a `docker-compose.test.yml` file with the following services:

```yaml
version: '3.8'

services:
  # MongoDB for data storage
  mongodb:
    image: mongo:latest
    volumes:
      - mongodb_data:/data/db
    ports:
      - "27017:27017"
    healthcheck:
      test: ["CMD", "mongo", "--eval", "db.adminCommand('ping')"]
      interval: 10s
      timeout: 5s
      retries: 3

  # Redis for caching and pub/sub
  redis:
    image: redis:alpine
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  # NovaConnect API
  nova-connect:
    build:
      context: ./nova-connect
      dockerfile: Dockerfile
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=test
      - PORT=3001
      - MONGODB_URI=mongodb://mongodb:27017/nova-connect-test
      - REDIS_URI=redis://redis:6379
    depends_on:
      mongodb:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./nova-connect:/app
      - /app/node_modules
    healthcheck:
      test: ["CMD", "wget", "--spider", "http://localhost:3001/health"]
      interval: 10s
      timeout: 5s
      retries: 3

  # Test Runner
  test-runner:
    build:
      context: ./
      dockerfile: Dockerfile.test
    volumes:
      - ./:/app
      - /app/node_modules
      - ./test-results:/app/test-results
    environment:
      - NODE_ENV=test
      - NOVA_CONNECT_URL=http://nova-connect:3001
    depends_on:
      nova-connect:
        condition: service_healthy
    command: ["npm", "run", "test:performance"]
```

#### 1.2 Create Test Runner Dockerfile

Create a `Dockerfile.test` file:

```dockerfile
FROM node:16-alpine

WORKDIR /app

# Install necessary tools for testing
RUN apk add --no-cache curl wget

# Copy package.json and package-lock.json
COPY package*.json ./

# Install dependencies
RUN npm install

# Copy test files
COPY tests/ ./tests/
COPY jest.config.js ./

# Set environment to test
ENV NODE_ENV=test

# Command will be provided by docker-compose
```

#### 1.3 Create Test Scripts

Add the following scripts to `package.json`:

```json
"scripts": {
  "test:performance": "jest --config=jest.config.test.js --testMatch='**/performance/**/*.test.js'",
  "test:all": "node tests/run-novaconnect-tests.js"
}
```

### 2. Core Performance Test Implementation

#### 2.1 Create Data Normalization Test

Create a test file at `tests/performance/data-normalization.test.js`:

```javascript
/**
 * NovaConnect Performance Tests - Data Normalization
 * 
 * These tests validate the performance of the data normalization engine,
 * ensuring it meets the <100ms requirement.
 */

const axios = require('axios');
const { performance } = require('perf_hooks');

// Helper function to measure execution time
const measureExecutionTime = async (fn) => {
  const start = performance.now();
  const result = await fn();
  const end = performance.now();
  const duration = end - start;
  return { result, duration };
};

// Test configuration
const config = {
  baseUrl: process.env.NOVA_CONNECT_URL || 'http://localhost:3001',
  transformUrl: '/api/transform'
};

// Test data
const samplePayload = {
  source: {
    id: 'finding-123',
    name: 'Unpatched vulnerability',
    severity: 'HIGH',
    category: 'VULNERABILITY',
    createTime: '2023-01-01T00:00:00Z',
    resource: {
      name: 'projects/test-project/instances/test-instance',
      type: 'google.compute.Instance'
    }
  },
  transformationRules: [
    { source: 'source.id', target: 'id' },
    { source: 'source.name', target: 'title' },
    { source: 'source.severity', target: 'severity', transform: 'lowercase' },
    { source: 'source.category', target: 'type', transform: 'lowercase' },
    { source: 'source.createTime', target: 'createdAt', transform: 'isoToUnix' },
    { source: 'source.resource.name', target: 'resourceName' },
    { source: 'source.resource.type', target: 'resourceType' }
  ]
};

describe('Data Normalization Performance Tests', () => {
  // Set a longer timeout for performance tests
  jest.setTimeout(30000);
  
  // Test single transformation performance
  it('should normalize data within 100ms', async () => {
    const { result, duration } = await measureExecutionTime(async () => {
      return await axios.post(`${config.baseUrl}${config.transformUrl}`, samplePayload);
    });
    
    expect(result.status).toBe(200);
    expect(result.data).toHaveProperty('id', 'finding-123');
    expect(result.data).toHaveProperty('severity', 'high');
    expect(duration).toBeLessThan(100); // Should normalize in less than 100ms
    
    console.log(`Data normalization completed in ${duration.toFixed(2)} ms`);
  });
  
  // Test batch transformation performance
  it('should normalize 100 items within acceptable time', async () => {
    // Generate batch payload with 100 items
    const batchPayload = {
      source: {
        findings: Array(100).fill(0).map((_, i) => ({
          id: `finding-${i}`,
          name: `Finding ${i}`,
          severity: ['HIGH', 'MEDIUM', 'LOW'][i % 3],
          category: ['VULNERABILITY', 'MISCONFIGURATION', 'THREAT'][i % 3],
          createTime: new Date().toISOString(),
          resource: {
            name: `projects/test-project/instances/instance-${i}`,
            type: 'google.compute.Instance'
          }
        }))
      },
      transformationRules: [
        { source: 'findings[*].id', target: 'items[*].id' },
        { source: 'findings[*].name', target: 'items[*].title' },
        { source: 'findings[*].severity', target: 'items[*].severity', transform: 'lowercase' },
        { source: 'findings[*].category', target: 'items[*].type', transform: 'lowercase' },
        { source: 'findings[*].createTime', target: 'items[*].createdAt', transform: 'isoToUnix' },
        { source: 'findings[*].resource.name', target: 'items[*].resourceName' },
        { source: 'findings[*].resource.type', target: 'items[*].resourceType' }
      ]
    };
    
    const { result, duration } = await measureExecutionTime(async () => {
      return await axios.post(`${config.baseUrl}${config.transformUrl}`, batchPayload);
    });
    
    expect(result.status).toBe(200);
    expect(result.data).toHaveProperty('items');
    expect(result.data.items).toHaveLength(100);
    
    // Average time per item should be less than 100ms
    const averageTimePerItem = duration / 100;
    expect(averageTimePerItem).toBeLessThan(100);
    
    console.log(`Batch normalization (100 items) completed in ${duration.toFixed(2)} ms`);
    console.log(`Average time per item: ${averageTimePerItem.toFixed(2)} ms`);
  });
});
```

#### 2.2 Create Event Processing Test

Create a test file at `tests/performance/event-processing.test.js`:

```javascript
/**
 * NovaConnect Performance Tests - Event Processing
 * 
 * These tests validate the event processing capabilities of NovaConnect,
 * ensuring it can handle 50,000 events in 15 minutes.
 */

const axios = require('axios');
const { performance } = require('perf_hooks');

// Helper function to measure execution time
const measureExecutionTime = async (fn) => {
  const start = performance.now();
  const result = await fn();
  const end = performance.now();
  const duration = end - start;
  return { result, duration };
};

// Test configuration
const config = {
  baseUrl: process.env.NOVA_CONNECT_URL || 'http://localhost:3001',
  eventsUrl: '/api/events/process'
};

// Generate a test event
const generateEvent = (index) => ({
  id: `event-${index}`,
  type: ['compliance', 'security', 'audit'][index % 3],
  severity: ['high', 'medium', 'low'][index % 3],
  source: ['gcp', 'aws', 'azure'][index % 3],
  resource: {
    id: `resource-${index}`,
    type: ['vm', 'storage', 'network'][index % 3],
    name: `Resource ${index}`
  },
  timestamp: new Date().toISOString(),
  data: {
    finding: `Finding ${index}`,
    details: `Details for finding ${index}`
  }
});

describe('Event Processing Performance Tests', () => {
  // Set a longer timeout for performance tests
  jest.setTimeout(60000);
  
  // Test single event processing
  it('should process a single event quickly', async () => {
    const event = generateEvent(1);
    
    const { result, duration } = await measureExecutionTime(async () => {
      return await axios.post(`${config.baseUrl}${config.eventsUrl}`, event);
    });
    
    expect(result.status).toBe(200);
    expect(result.data).toHaveProperty('success', true);
    expect(duration).toBeLessThan(200); // Should process in less than 200ms
    
    console.log(`Single event processing completed in ${duration.toFixed(2)} ms`);
  });
  
  // Test batch event processing
  it('should process a batch of events efficiently', async () => {
    const batchSize = 100; // Use a smaller batch for the test
    const events = Array(batchSize).fill(0).map((_, i) => generateEvent(i));
    
    const { result, duration } = await measureExecutionTime(async () => {
      return await axios.post(`${config.baseUrl}${config.eventsUrl}/batch`, { events });
    });
    
    expect(result.status).toBe(200);
    expect(result.data).toHaveProperty('success', true);
    expect(result.data).toHaveProperty('processed', batchSize);
    
    // Calculate throughput (events per second)
    const throughput = (batchSize / duration) * 1000;
    
    console.log(`Batch processing (${batchSize} events) completed in ${duration.toFixed(2)} ms`);
    console.log(`Throughput: ${throughput.toFixed(2)} events/second`);
    
    // Calculate if we can meet the 50K in 15 minutes requirement
    const eventsPerSecondNeeded = 50000 / (15 * 60); // 55.56 events/second
    expect(throughput).toBeGreaterThan(eventsPerSecondNeeded);
    
    // Extrapolate to full load
    const timeFor50K = (50000 / throughput) * 1000; // ms
    const timeInMinutes = timeFor50K / (1000 * 60); // minutes
    
    console.log(`Estimated time to process 50,000 events: ${timeInMinutes.toFixed(2)} minutes`);
    expect(timeInMinutes).toBeLessThanOrEqual(15);
  });
});
```

### 3. Test Runner Script

Create a script at `tests/run-novaconnect-tests.js` to run all tests:

```javascript
/**
 * NovaConnect Test Runner
 * 
 * This script runs all tests for NovaConnect, including unit tests,
 * integration tests, performance tests, and security tests.
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  testCategories: [
    { name: 'Unit Tests', pattern: '**/unit/**/*.test.js' },
    { name: 'Integration Tests', pattern: '**/integration/**/*.test.js' },
    { name: 'Performance Tests', pattern: '**/performance/**/*.test.js' },
    { name: 'Security Tests', pattern: '**/security/**/*.test.js' }
  ],
  resultsDir: './test-results',
  jestConfig: './jest.config.test.js'
};

// Ensure results directory exists
if (!fs.existsSync(config.resultsDir)) {
  fs.mkdirSync(config.resultsDir, { recursive: true });
}

// Initialize results
const results = {
  startTime: new Date(),
  endTime: null,
  categories: [],
  summary: {
    total: 0,
    passed: 0,
    failed: 0,
    skipped: 0
  }
};

/**
 * Run tests for a specific category
 */
function runTestCategory(category) {
  console.log(`\n=== Running ${category.name} ===\n`);
  
  try {
    // Run Jest for the specific test pattern
    const command = `npx jest --config=${config.jestConfig} --testMatch="${category.pattern}" --colors`;
    execSync(command, { stdio: 'inherit' });
    
    console.log(`✅ ${category.name} passed`);
    return { success: true, skipped: false };
  } catch (error) {
    console.error(`❌ ${category.name} failed`);
    return { success: false, skipped: false };
  }
}

// Main execution
console.log('Starting NovaConnect test suite...');

// Run each test category
for (const category of config.testCategories) {
  const result = runTestCategory(category);
  results.categories.push({
    name: category.name,
    success: result.success,
    skipped: result.skipped
  });
}

results.endTime = new Date();

// Print summary
console.log('\n=== Test Summary ===\n');
for (const category of results.categories) {
  const status = category.success ? '✅ PASS' : category.skipped ? '⏭️ SKIP' : '❌ FAIL';
  console.log(`${category.name}: ${status}`);
}

// Exit with appropriate code
const hasFailures = results.categories.some(category => !category.success && !category.skipped);
process.exit(hasFailures ? 1 : 0);
```

### 4. Docker Test Runner Script

Create a script at `run-docker-tests.sh` to run tests in Docker:

```bash
#!/bin/bash

# NovaConnect Docker Test Runner
# This script runs the NovaConnect tests in a Docker environment

# Set error handling
set -e

# Configuration
DOCKER_COMPOSE_FILE="docker-compose.test.yml"

# Function to display colored output
function echo_color() {
    local color=$1
    local message=$2
    
    case $color in
        "red")
            echo -e "\033[0;31m$message\033[0m"
            ;;
        "green")
            echo -e "\033[0;32m$message\033[0m"
            ;;
        "yellow")
            echo -e "\033[0;33m$message\033[0m"
            ;;
        "cyan")
            echo -e "\033[0;36m$message\033[0m"
            ;;
        *)
            echo "$message"
            ;;
    esac
}

# Main execution
echo_color "cyan" "NovaConnect Docker Test Runner"
echo_color "cyan" "=============================="

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo_color "red" "Error: Docker is not running. Please start Docker and try again."
    exit 1
fi

# Start the Docker environment
echo_color "yellow" "Starting Docker test environment..."
docker-compose -f $DOCKER_COMPOSE_FILE up -d

# Wait for services to be ready
echo_color "yellow" "Waiting for services to be ready..."
sleep 10

# Run the tests
echo_color "yellow" "Running NovaConnect tests..."
docker-compose -f $DOCKER_COMPOSE_FILE run test-runner

# Get the exit code
TEST_RESULT=$?

# Stop the Docker environment
echo_color "yellow" "Stopping Docker test environment..."
docker-compose -f $DOCKER_COMPOSE_FILE down

# Exit with the test result
exit $TEST_RESULT
```

Make the script executable:

```bash
chmod +x run-docker-tests.sh
```

## Expected Outcomes

After implementing Phase 1, we expect to have:

1. A working Docker-based testing environment
2. Performance tests that validate:
   - Data normalization in <100ms
   - Ability to process 50,000 events in 15 minutes
3. Baseline performance metrics for future comparison
4. A test runner script that can execute all test categories

## Next Steps

After completing Phase 1, we will proceed to Phase 2, which will focus on:

1. Implementing remediation workflow tests
2. Validating the 8-second remediation claim
3. Testing the framework mapping engine
4. Expanding the test coverage to include more complex scenarios
