/**
 * Collect Evidence Action for the UCTO Compliance Automation Framework.
 *
 * This module provides an action that collects evidence for a compliance requirement or activity.
 */

/**
 * Collect Evidence action handler.
 */
class CollectEvidenceAction {
  /**
   * Initialize the Collect Evidence Action.
   * @param {Object} options - Options for the action
   */
  constructor(options = {}) {
    console.log("Initializing Collect Evidence Action");
    
    // Store options
    this.options = options;
    
    // Store the tracking manager
    this.trackingManager = options.trackingManager;
    
    console.log("Collect Evidence Action initialized");
  }
  
  /**
   * Execute the action.
   * @param {Object} parameters - Action parameters
   * @param {Object} context - Execution context
   * @returns {Promise<Object>} Execution result
   */
  async execute(parameters, context) {
    console.log("Executing Collect Evidence Action");
    
    // Validate parameters
    if (!parameters.name) {
      throw new Error("Name parameter is required for Collect Evidence Action");
    }
    
    if (!parameters.description) {
      throw new Error("Description parameter is required for Collect Evidence Action");
    }
    
    if (!parameters.requirement_id && !parameters.activity_id) {
      throw new Error("Either requirement_id or activity_id parameter is required for Collect Evidence Action");
    }
    
    // Create the evidence data
    const evidenceData = {
      name: parameters.name,
      description: parameters.description,
      requirement_id: parameters.requirement_id,
      activity_id: parameters.activity_id,
      type: parameters.type || 'document',
      source: parameters.source,
      collection_method: parameters.collection_method || 'automated',
      content: parameters.content,
      url: parameters.url,
      metadata: parameters.metadata || {}
    };
    
    // Add collection timestamp
    evidenceData.metadata.collected_at = new Date().toISOString();
    evidenceData.metadata.collected_by = 'automation';
    
    // If tracking manager is available, create the evidence
    if (this.trackingManager && this.trackingManager.create_evidence) {
      try {
        const evidence = await this.trackingManager.create_evidence(evidenceData);
        
        return {
          success: true,
          evidence
        };
      } catch (error) {
        console.error("Error collecting evidence:", error);
        
        throw new Error(`Failed to collect evidence: ${error.message}`);
      }
    } else {
      // Return mock data if tracking manager is not available
      return {
        success: true,
        evidence: {
          id: `evidence-${Date.now()}`,
          ...evidenceData,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
      };
    }
  }
}

module.exports = CollectEvidenceAction;
