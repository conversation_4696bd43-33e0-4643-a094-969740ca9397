@echo off
title Nova Agent Test Suite
color 0A

echo ================================================
echo           NOVA AGENT TEST SUITE
echo ================================================
echo.

echo [1] Checking current directory...
cd
echo.

echo [2] Listing files in directory...
dir nova-agent.*
echo.

echo [3] Testing Go installation...
"C:\Program Files\Go\bin\go.exe" version
echo.

echo [4] Rebuilding Nova Agent...
"C:\Program Files\Go\bin\go.exe" build -o nova-agent-new.exe nova-agent.go
echo.

echo [5] Testing new executable...
if exist nova-agent-new.exe (
    echo ✅ New build successful! Running test:
    echo.
    nova-agent-new.exe
    echo.
    echo ✅ Nova Agent is working!
) else (
    echo ❌ Build failed - checking existing executable...
    if exist nova-agent.exe (
        echo Found existing nova-agent.exe, testing:
        nova-agent.exe
    ) else (
        echo ❌ No executable found!
    )
)

echo.
echo ================================================
echo Test completed. Press any key to exit...
pause >nul
