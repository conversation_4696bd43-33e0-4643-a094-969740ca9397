import React from 'react';
import DiagramTemplate from '../DiagramTemplate';

// Define the diagram data
const elements = [
  {
    id: 'data-processing',
    top: 50,
    left: 350,
    width: 300,
    text: 'Cross-Module Data Processing Pipeline',
    number: '1',
    bold: true,
    fontSize: '20px',
    backgroundColor: '#e6f7ff'
  },
  // Data Sources
  {
    id: 'data-sources',
    top: 120,
    left: 350,
    width: 300,
    text: 'External Data Sources',
    number: '2',
    bold: true,
    fontSize: '16px',
    backgroundColor: '#fff0f6'
  },
  {
    id: 'structured-data',
    top: 180,
    left: 100,
    width: 150,
    text: 'Structured Data\n(databases, APIs, CSV)',
    number: '3',
    fontSize: '12px',
    backgroundColor: '#f6ffed'
  },
  {
    id: 'unstructured-data',
    top: 180,
    left: 300,
    width: 150,
    text: 'Unstructured Data\n(documents, emails, logs)',
    number: '4',
    fontSize: '12px',
    backgroundColor: '#f6ffed'
  },
  {
    id: 'semi-structured-data',
    top: 180,
    left: 500,
    width: 150,
    text: 'Semi-structured Data\n(JSON, XML, YAML)',
    number: '5',
    fontSize: '12px',
    backgroundColor: '#f6ffed'
  },
  {
    id: 'real-time-streams',
    top: 180,
    left: 700,
    width: 150,
    text: 'Real-time Streams\n(events, telemetry)',
    number: '6',
    fontSize: '12px',
    backgroundColor: '#f6ffed'
  },
  // Data Ingestion
  {
    id: 'data-ingestion',
    top: 250,
    left: 350,
    width: 300,
    text: 'Data Ingestion (NovaConnect)',
    number: '7',
    bold: true,
    fontSize: '16px',
    backgroundColor: '#fff0f6'
  },
  {
    id: 'api-connectors',
    top: 310,
    left: 250,
    width: 150,
    text: 'Universal API Connectors',
    number: '8',
    fontSize: '12px',
    backgroundColor: '#fffbe6'
  },
  {
    id: 'data-adapters',
    top: 310,
    left: 450,
    width: 150,
    text: 'Data Format Adapters',
    number: '9',
    fontSize: '12px',
    backgroundColor: '#fffbe6'
  },
  // Data Normalization
  {
    id: 'data-normalization',
    top: 380,
    left: 350,
    width: 300,
    text: 'Data Normalization (NovaCore)',
    number: '10',
    bold: true,
    fontSize: '16px',
    backgroundColor: '#fff0f6'
  },
  {
    id: 'tensor-product',
    top: 440,
    left: 150,
    width: 150,
    text: 'Tensor Product (⊗)\nMulti-dimensional relationships',
    number: '11',
    fontSize: '12px',
    backgroundColor: '#f9f0ff'
  },
  {
    id: 'fusion-operator',
    top: 440,
    left: 350,
    width: 150,
    text: 'Fusion Operator (⊕)\nMerges related data points',
    number: '12',
    fontSize: '12px',
    backgroundColor: '#f9f0ff'
  },
  {
    id: 'pi-factor',
    top: 440,
    left: 550,
    width: 150,
    text: 'π10³ Factor\nConsistent processing',
    number: '13',
    fontSize: '12px',
    backgroundColor: '#f9f0ff'
  },
  // Data Quality Assessment
  {
    id: 'data-quality',
    top: 510,
    left: 350,
    width: 300,
    text: 'Data Quality Assessment (NovaTrack)',
    number: '14',
    bold: true,
    fontSize: '16px',
    backgroundColor: '#fff0f6'
  },
  {
    id: 'governance-vectors',
    top: 570,
    left: 150,
    width: 150,
    text: 'Governance Vector Extraction',
    number: '15',
    fontSize: '12px',
    backgroundColor: '#e6f7ff'
  },
  {
    id: 'deviation-calc',
    top: 570,
    left: 350,
    width: 150,
    text: 'Deviation Calculation',
    number: '16',
    fontSize: '12px',
    backgroundColor: '#e6f7ff'
  },
  {
    id: 'data-triage',
    top: 570,
    left: 550,
    width: 150,
    text: 'Data Triage\nπscore < 0.618 flagged',
    number: '17',
    fontSize: '12px',
    backgroundColor: '#e6f7ff'
  },
  // Pattern Detection
  {
    id: 'pattern-detection',
    top: 640,
    left: 350,
    width: 300,
    text: 'Pattern Detection (NovaShield)',
    number: '18',
    bold: true,
    fontSize: '16px',
    backgroundColor: '#fff0f6'
  },
  {
    id: 'resonance-index',
    top: 700,
    left: 250,
    width: 150,
    text: 'Resonance Index\nφ-weighted detection',
    number: '19',
    fontSize: '12px',
    backgroundColor: '#f6ffed'
  },
  {
    id: 'pattern-translation',
    top: 700,
    left: 450,
    width: 150,
    text: 'Universal Pattern Language\nCross-domain translation',
    number: '20',
    fontSize: '12px',
    backgroundColor: '#f6ffed'
  }
];

const connections = [
  // Connect Data Processing to Data Sources
  {
    start: { x: 500, y: 100 },
    end: { x: 500, y: 120 },
    type: 'arrow'
  },
  // Connect Data Sources to specific sources
  {
    start: { x: 350, y: 170 },
    end: { x: 175, y: 180 },
    type: 'arrow'
  },
  {
    start: { x: 400, y: 170 },
    end: { x: 375, y: 180 },
    type: 'arrow'
  },
  {
    start: { x: 500, y: 170 },
    end: { x: 575, y: 180 },
    type: 'arrow'
  },
  {
    start: { x: 550, y: 170 },
    end: { x: 775, y: 180 },
    type: 'arrow'
  },
  // Connect sources to Data Ingestion
  {
    start: { x: 175, y: 230 },
    end: { x: 350, y: 250 },
    type: 'arrow'
  },
  {
    start: { x: 375, y: 230 },
    end: { x: 400, y: 250 },
    type: 'arrow'
  },
  {
    start: { x: 575, y: 230 },
    end: { x: 500, y: 250 },
    type: 'arrow'
  },
  {
    start: { x: 775, y: 230 },
    end: { x: 550, y: 250 },
    type: 'arrow'
  },
  // Connect Data Ingestion to components
  {
    start: { x: 400, y: 300 },
    end: { x: 325, y: 310 },
    type: 'arrow'
  },
  {
    start: { x: 500, y: 300 },
    end: { x: 525, y: 310 },
    type: 'arrow'
  },
  // Connect Data Ingestion to Data Normalization
  {
    start: { x: 500, y: 350 },
    end: { x: 500, y: 380 },
    type: 'arrow'
  },
  // Connect Data Normalization to components
  {
    start: { x: 350, y: 430 },
    end: { x: 225, y: 440 },
    type: 'arrow'
  },
  {
    start: { x: 450, y: 430 },
    end: { x: 425, y: 440 },
    type: 'arrow'
  },
  {
    start: { x: 550, y: 430 },
    end: { x: 625, y: 440 },
    type: 'arrow'
  },
  // Connect Data Normalization to Data Quality Assessment
  {
    start: { x: 500, y: 480 },
    end: { x: 500, y: 510 },
    type: 'arrow'
  },
  // Connect Data Quality Assessment to components
  {
    start: { x: 350, y: 560 },
    end: { x: 225, y: 570 },
    type: 'arrow'
  },
  {
    start: { x: 450, y: 560 },
    end: { x: 425, y: 570 },
    type: 'arrow'
  },
  {
    start: { x: 550, y: 560 },
    end: { x: 625, y: 570 },
    type: 'arrow'
  },
  // Connect Data Quality Assessment to Pattern Detection
  {
    start: { x: 500, y: 610 },
    end: { x: 500, y: 640 },
    type: 'arrow'
  },
  // Connect Pattern Detection to components
  {
    start: { x: 400, y: 690 },
    end: { x: 325, y: 700 },
    type: 'arrow'
  },
  {
    start: { x: 500, y: 690 },
    end: { x: 525, y: 700 },
    type: 'arrow'
  }
];

const DataProcessingPipeline: React.FC = () => {
  return (
    <DiagramTemplate 
      elements={elements} 
      connections={connections} 
      width="900px" 
      height="750px" 
    />
  );
};

export default DataProcessingPipeline;
