import React from 'react';
import {
  DiagramFrame,
  ComponentBox,
  ComponentLabel,
  ComponentNumber,
  ContainerBox,
  ContainerLabel,
  DiagramLegend,
  LegendItem,
  LegendColor,
  LegendText,
  InventorLabel
} from '../../components/DiagramComponents';

const PerformanceEquation = () => {
  return (
    <DiagramFrame>
      <ContainerBox width="750px" height="600px" left="25px" top="20px">
        <ContainerLabel fontSize="18px">NOVAFUSE PERFORMANCE EQUATION (THE 3,142x FORMULA)</ContainerLabel>
      </ContainerBox>
      
      {/* Core Equation */}
      <ContainerBox width="700px" height="120px" left="50px" top="70px">
        <ContainerLabel fontSize="16px">CORE PERFORMANCE EQUATION</ContainerLabel>
      </ContainerBox>
      
      <ComponentBox left="100px" top="110px" width="600px" height="60px">
        <ComponentNumber>801</ComponentNumber>
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', width: '100%' }}>
          <div style={{ fontFamily: 'serif', fontSize: '18px', fontStyle: 'italic', fontWeight: 'bold' }}>
            E = N × G × C = 10 × 10 × 31.42 = <span style={{ color: '#0A84FF' }}>3,142</span>
          </div>
        </div>
      </ComponentBox>
      
      {/* Variable Definitions */}
      <ContainerBox width="700px" height="180px" left="50px" top="210px">
        <ContainerLabel fontSize="16px">VARIABLE DEFINITIONS</ContainerLabel>
      </ContainerBox>
      
      <ComponentBox left="80px" top="250px" width="150px" height="120px">
        <ComponentNumber>802</ComponentNumber>
        <ComponentLabel fontSize="16px">N = 10</ComponentLabel>
        <div style={{ fontSize: '12px', textAlign: 'center', marginTop: '5px' }}>
          <div style={{ fontWeight: 'bold', marginBottom: '5px' }}>NIST Multiplier</div>
          <div style={{ fontStyle: 'italic' }}>
            ~90% reduction in compliance gaps
          </div>
          <div style={{ marginTop: '5px' }}>
            NIST-aligned controls
          </div>
        </div>
      </ComponentBox>
      
      <ComponentBox left="300px" top="250px" width="150px" height="120px">
        <ComponentNumber>803</ComponentNumber>
        <ComponentLabel fontSize="16px">G = 10</ComponentLabel>
        <div style={{ fontSize: '12px', textAlign: 'center', marginTop: '5px' }}>
          <div style={{ fontWeight: 'bold', marginBottom: '5px' }}>GCP Multiplier</div>
          <div style={{ fontStyle: 'italic' }}>
            ~90% reduction in processing latency
          </div>
          <div style={{ marginTop: '5px' }}>
            ML-driven infrastructure
          </div>
        </div>
      </ComponentBox>
      
      <ComponentBox left="520px" top="250px" width="150px" height="120px">
        <ComponentNumber>804</ComponentNumber>
        <ComponentLabel fontSize="16px" color="#0A84FF">C = 31.42</ComponentLabel>
        <div style={{ fontSize: '12px', textAlign: 'center', marginTop: '5px' }}>
          <div style={{ fontWeight: 'bold', marginBottom: '5px' }}>Cyber-Safety Multiplier</div>
          <div style={{ fontStyle: 'italic' }}>
            ~97% faster threat response
          </div>
          <div style={{ marginTop: '5px' }}>
            Real-time fusion
          </div>
        </div>
      </ComponentBox>
      
      {/* Expanded Formulation */}
      <ContainerBox width="700px" height="120px" left="50px" top="400px">
        <ContainerLabel fontSize="16px">EXPANDED FORMULATION WITH VARIABLES</ContainerLabel>
      </ContainerBox>
      
      <ComponentBox left="100px" top="440px" width="600px" height="60px">
        <ComponentNumber>805</ComponentNumber>
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', width: '100%' }}>
          <div style={{ fontFamily: 'serif', fontSize: '16px', fontStyle: 'italic' }}>
            E = (1+R<sub>n</sub>) × (1+R<sub>g</sub>) × (1+R<sub>c</sub>) = (1+0.90) × (1+0.90) × (1+30.42) ≈ <span style={{ color: '#0A84FF', fontWeight: 'bold' }}>3,142</span>
          </div>
        </div>
      </ComponentBox>
      
      {/* Master Functional */}
      <ContainerBox width="700px" height="100px" left="50px" top="520px">
        <ContainerLabel fontSize="16px">MASTER FUNCTIONAL (UNIFIED EQUATION)</ContainerLabel>
      </ContainerBox>
      
      <ComponentBox left="100px" top="560px" width="600px" height="40px">
        <ComponentNumber>806</ComponentNumber>
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', width: '100%' }}>
          <div style={{ fontFamily: 'serif', fontSize: '14px', fontStyle: 'italic', color: '#0A84FF' }}>
            F<sub>NovaFuse</sub>(t,n) = (∫[N⊗G⊕C]dτ) · (π10<sup>3</sup>e<sup>iπ/2</sup>) · (0.82·2)<sup>n</sup> · φ<sup>-2</sup>√5
          </div>
        </div>
      </ComponentBox>
      
      {/* Legend */}
      <DiagramLegend>
        <LegendItem>
          <LegendColor color="#fff" />
          <LegendText>Performance Variables</LegendText>
        </LegendItem>
        <LegendItem>
          <LegendColor color="#0A84FF" />
          <LegendText>Key Results</LegendText>
        </LegendItem>
      </DiagramLegend>
      
      <InventorLabel>Inventor: David Nigel Irvin</InventorLabel>
    </DiagramFrame>
  );
};

export default PerformanceEquation;
