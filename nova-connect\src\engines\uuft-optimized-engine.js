/**
 * UUFT-Optimized Transformation Engine for NovaConnect
 * 
 * This module provides an optimized implementation of the UUFT-enhanced transformation engine
 * with improved performance through caching, selective application, and memory optimizations.
 */

const { performance } = require('perf_hooks');

class UUFTOptimizedEngine {
  /**
   * Create a new UUFT-Optimized Transformation Engine
   * @param {Object} options - Configuration options
   * @param {boolean} options.enableCaching - Enable result caching
   * @param {boolean} options.enableMetrics - Enable performance metrics
   * @param {number} options.cacheSize - Maximum cache size
   * @param {boolean} options.enableUUFT - Enable UUFT optimization
   * @param {number} options.uuftThreshold - Threshold for applying UUFT (0-1)
   * @param {string} options.domain - Domain-specific optimization ('security', 'compliance', etc.)
   */
  constructor(options = {}) {
    this.options = {
      enableCaching: true,
      enableMetrics: true,
      cacheSize: 1000,
      enableUUFT: true,
      uuftThreshold: 0.7,
      domain: 'general',
      ...options
    };
    
    // UUFT constants - domain-specific adjustments
    this.PI = Math.PI;
    this.GOLDEN_RATIO = (1 + Math.sqrt(5)) / 2; // φ ≈ 1.618
    
    // Domain-specific optimizations
    if (this.options.domain === 'security') {
      this.PI_POWER = 1; // Use π10¹ for security domain (based on test results)
      this.GOLDEN_RATIO_FACTOR = 1.2;
      this.RATIO_18_82 = [0.15, 0.85]; // Adjusted for security domain
    } else if (this.options.domain === 'compliance') {
      this.PI_POWER = 2; // Use π10² for compliance domain
      this.GOLDEN_RATIO_FACTOR = 1.0;
      this.RATIO_18_82 = [0.18, 0.82]; // Standard ratio
    } else {
      this.PI_POWER = 3; // Default π10³
      this.GOLDEN_RATIO_FACTOR = 1.0;
      this.RATIO_18_82 = [0.18, 0.82]; // Standard ratio
    }
    
    // Calculate derived constants
    this.PI_FACTOR = this.PI * Math.pow(10, this.PI_POWER);
    this.ADJUSTED_GOLDEN_RATIO = this.GOLDEN_RATIO * this.GOLDEN_RATIO_FACTOR;
    
    // Initialize transformers registry
    this.transformers = {
      // String transformations
      lowercase: (value) => typeof value === 'string' ? value.toLowerCase() : value,
      uppercase: (value) => typeof value === 'string' ? value.toUpperCase() : value,
      trim: (value) => typeof value === 'string' ? value.trim() : value,
      
      // Date transformations
      isoToUnix: (value) => typeof value === 'string' ? new Date(value).getTime() : value,
      unixToIso: (value) => typeof value === 'number' ? new Date(value).toISOString() : value,
      
      // Number transformations
      toNumber: (value) => !isNaN(parseFloat(value)) ? parseFloat(value) : value,
      
      // Object transformations
      toJson: (value) => typeof value === 'object' ? JSON.stringify(value) : value,
      fromJson: (value) => {
        if (typeof value !== 'string') return value;
        try {
          return JSON.parse(value);
        } catch (e) {
          return value;
        }
      },
      
      // Array transformations
      join: (value, separator = ',') => Array.isArray(value) ? value.join(separator) : value,
      split: (value, separator = ',') => typeof value === 'string' ? value.split(separator) : value,
      
      // Security transformations
      mask: (value, pattern = 'xxxx') => {
        if (typeof value !== 'string') return value;
        return value.substring(0, 4) + pattern;
      },
      
      // UUFT-specific transformations
      uuftOptimize: (value, params = {}) => {
        return this.applyUUFTEquation(
          params.A || 1.0,
          params.B || 0.5,
          params.C || value
        );
      }
    };
    
    // Initialize caches
    this.resultCache = new Map();
    this.pathCache = new Map();
    this.importantPathsCache = new Map();
    
    // Metrics
    this.metrics = {
      transformations: 0,
      totalDuration: 0,
      averageDuration: 0,
      uuftOptimizations: 0,
      cacheHits: 0,
      cacheMisses: 0
    };
    
    // Domain-specific importance keywords
    this.importantKeywords = this._getDomainKeywords();
  }
  
  /**
   * Get domain-specific keywords for importance scoring
   * @private
   * @returns {Array} - Array of important keywords
   */
  _getDomainKeywords() {
    switch (this.options.domain) {
      case 'security':
        return [
          'vulnerability', 'severity', 'cve', 'exploit', 'attack',
          'threat', 'malware', 'breach', 'risk', 'security', 'cvss'
        ];
      case 'compliance':
        return [
          'compliance', 'regulation', 'requirement', 'control', 'policy',
          'standard', 'framework', 'audit', 'evidence', 'certification'
        ];
      default:
        return [
          'id', 'key', 'name', 'status', 'type', 'priority', 'risk',
          'security', 'compliance', 'critical', 'important'
        ];
    }
  }
  
  /**
   * Apply the UUFT equation: (A ⊗ B ⊕ C) × π10^n
   * Optimized implementation with type-specific handling
   * 
   * @param {number} A - Source component (data source quality/reliability)
   * @param {number} B - Validation component (data validation score)
   * @param {*} C - Context component (the actual data)
   * @returns {*} - Result of applying the UUFT equation
   */
  applyUUFTEquation(A, B, C) {
    // Fast path for null/undefined values
    if (C === null || C === undefined) {
      return C;
    }
    
    // Type-specific processing for C
    let processedC;
    const typeOfC = typeof C;
    
    // Fast paths for common types
    if (typeOfC === 'number') {
      processedC = C;
    } else if (typeOfC === 'boolean') {
      return C; // Preserve boolean values as-is
    } else if (typeOfC === 'string') {
      // Preserve short strings as-is
      if (C.length <= 10) {
        return C;
      }
      processedC = C.length / 100; // Normalize string length
    } else if (Array.isArray(C)) {
      // For arrays, apply UUFT to each element if it's a small array
      if (C.length <= 5) {
        return C.map(item => this.applyUUFTEquation(A, B, item));
      }
      processedC = C.length / 100; // Normalize array length
    } else if (typeOfC === 'object') {
      // For objects, preserve as-is if small
      const keys = Object.keys(C);
      if (keys.length <= 5) {
        return C;
      }
      processedC = keys.length / 100; // Normalize object size
    } else {
      // For other types, return as-is
      return C;
    }
    
    // Apply UUFT equation with optimized calculations
    // Tensor product (⊗) with adjusted golden ratio
    const tensorProduct = A * B * this.ADJUSTED_GOLDEN_RATIO;
    
    // Fusion operator (⊕) with inverse golden ratio
    const fusionResult = tensorProduct + (processedC * (1 / this.ADJUSTED_GOLDEN_RATIO));
    
    // Apply pi factor
    const result = fusionResult * this.PI_FACTOR;
    
    // For non-numeric original values, convert back to the original type
    if (typeOfC === 'string') {
      return result.toString();
    } else if (Array.isArray(C)) {
      // For large arrays, we've already normalized, so return original
      return C;
    } else if (typeOfC === 'object') {
      // For large objects, we've already normalized, so return original
      return C;
    }
    
    return result;
  }
  
  /**
   * Apply the 18/82 principle to identify the most important fields
   * Optimized implementation with caching
   * 
   * @param {Object} data - Data object to analyze
   * @returns {Array} - Array of field paths sorted by importance
   */
  apply1882Principle(data) {
    // Generate cache key
    const cacheKey = this._generateCacheKey(data);
    
    // Check cache
    if (this.importantPathsCache.has(cacheKey)) {
      return this.importantPathsCache.get(cacheKey);
    }
    
    // Extract all paths from the data
    const paths = this._extractPaths(data);
    
    // Calculate importance score for each path
    const scoredPaths = paths.map(path => {
      const value = this._getValueByPath(data, path);
      const score = this._calculateImportanceScore(path, value);
      return { path, score };
    });
    
    // Sort paths by score (descending)
    scoredPaths.sort((a, b) => b.score - a.score);
    
    // Apply 18/82 principle
    const totalPaths = scoredPaths.length;
    const importantPathsCount = Math.ceil(totalPaths * this.RATIO_18_82[0]);
    
    // Get the top paths
    const importantPaths = scoredPaths.slice(0, importantPathsCount).map(item => item.path);
    
    // Cache the result
    this.importantPathsCache.set(cacheKey, importantPaths);
    
    return importantPaths;
  }
  
  /**
   * Calculate importance score for a path and value
   * @private
   * @param {string} path - Path to the value
   * @param {*} value - Value at the path
   * @returns {number} - Importance score
   */
  _calculateImportanceScore(path, value) {
    let score = 0;
    
    // Score based on data type
    if (typeof value === 'number') {
      score += 5; // Numbers are often important metrics
    } else if (typeof value === 'string') {
      score += value.length > 0 ? 3 : 1; // Non-empty strings are more important
    } else if (typeof value === 'boolean') {
      score += 4; // Booleans often represent important flags
    } else if (Array.isArray(value)) {
      score += value.length > 0 ? 4 : 2; // Non-empty arrays are more important
    } else if (value === null || value === undefined) {
      score += 0; // Null/undefined values are least important
    } else if (typeof value === 'object') {
      score += Object.keys(value).length > 0 ? 3 : 1; // Non-empty objects are more important
    }
    
    // Score based on path depth (deeper paths often contain more specific data)
    score += path.split('.').length;
    
    // Score based on path name (certain keywords indicate importance)
    for (const keyword of this.importantKeywords) {
      if (path.toLowerCase().includes(keyword)) {
        score += 2;
        break;
      }
    }
    
    return score;
  }
  
  /**
   * Transform data according to the provided rules with optimized UUFT
   * @param {Object} data - Source data to transform
   * @param {Array} rules - Transformation rules
   * @returns {Object} - Transformed data
   */
  transform(data, rules) {
    const startTime = this.options.enableMetrics ? performance.now() : 0;
    
    // Check cache if enabled
    if (this.options.enableCaching) {
      const cacheKey = this._generateCacheKey(data, rules);
      const cachedResult = this.resultCache.get(cacheKey);
      
      if (cachedResult) {
        if (this.options.enableMetrics) {
          this.metrics.cacheHits++;
        }
        return cachedResult;
      }
      
      if (this.options.enableMetrics) {
        this.metrics.cacheMisses++;
      }
    }
    
    let result = {};
    
    // Apply UUFT optimization if enabled
    if (this.options.enableUUFT) {
      // Identify important fields using 18/82 principle
      const importantPaths = this.apply1882Principle(data);
      
      // Split rules into high and low impact
      const highImpactRules = [];
      const lowImpactRules = [];
      
      for (const rule of rules) {
        // Check if rule is high impact
        const isHighImpact = importantPaths.includes(rule.source) || 
                            (rule.sourceReliability && rule.validationScore && 
                             rule.sourceReliability * rule.validationScore >= this.options.uuftThreshold);
        
        if (isHighImpact) {
          highImpactRules.push(rule);
        } else {
          lowImpactRules.push(rule);
        }
      }
      
      // Process high impact rules with UUFT
      for (const rule of highImpactRules) {
        try {
          // Get value from source path
          const value = this._getValueByPath(data, rule.source);
          
          // Apply transformation if specified
          let transformedValue = value;
          if (rule.transform) {
            if (typeof rule.transform === 'string' && this.transformers[rule.transform]) {
              // Single transformation
              transformedValue = this.transformers[rule.transform](value, rule.transformParams);
            } else if (Array.isArray(rule.transform)) {
              // Chain of transformations
              transformedValue = rule.transform.reduce((currentValue, transformName) => {
                if (this.transformers[transformName]) {
                  return this.transformers[transformName](currentValue, rule.transformParams);
                }
                return currentValue;
              }, value);
            }
            
            // Apply UUFT equation to enhance the transformation
            const sourceReliability = rule.sourceReliability || 1.0;
            const validationScore = rule.validationScore || 0.5;
            
            transformedValue = this.applyUUFTEquation(
              sourceReliability,
              validationScore,
              transformedValue
            );
            
            if (this.options.enableMetrics) {
              this.metrics.uuftOptimizations++;
            }
          }
          
          // Set value in target path
          this._setValueByPath(result, rule.target, transformedValue);
        } catch (error) {
          // Log error but continue with other rules
          console.error(`Error applying transformation rule: ${error.message}`, rule);
        }
      }
      
      // Process low impact rules with standard transformation
      for (const rule of lowImpactRules) {
        try {
          // Get value from source path
          const value = this._getValueByPath(data, rule.source);
          
          // Apply transformation if specified
          let transformedValue = value;
          if (rule.transform) {
            if (typeof rule.transform === 'string' && this.transformers[rule.transform]) {
              // Single transformation
              transformedValue = this.transformers[rule.transform](value, rule.transformParams);
            } else if (Array.isArray(rule.transform)) {
              // Chain of transformations
              transformedValue = rule.transform.reduce((currentValue, transformName) => {
                if (this.transformers[transformName]) {
                  return this.transformers[transformName](currentValue, rule.transformParams);
                }
                return currentValue;
              }, value);
            }
          }
          
          // Set value in target path
          this._setValueByPath(result, rule.target, transformedValue);
        } catch (error) {
          // Log error but continue with other rules
          console.error(`Error applying transformation rule: ${error.message}`, rule);
        }
      }
    } else {
      // Standard transformation without UUFT optimization
      for (const rule of rules) {
        try {
          // Get value from source path
          const value = this._getValueByPath(data, rule.source);
          
          // Apply transformation if specified
          let transformedValue = value;
          if (rule.transform) {
            if (typeof rule.transform === 'string' && this.transformers[rule.transform]) {
              // Single transformation
              transformedValue = this.transformers[rule.transform](value, rule.transformParams);
            } else if (Array.isArray(rule.transform)) {
              // Chain of transformations
              transformedValue = rule.transform.reduce((currentValue, transformName) => {
                if (this.transformers[transformName]) {
                  return this.transformers[transformName](currentValue, rule.transformParams);
                }
                return currentValue;
              }, value);
            }
          }
          
          // Set value in target path
          this._setValueByPath(result, rule.target, transformedValue);
        } catch (error) {
          // Log error but continue with other rules
          console.error(`Error applying transformation rule: ${error.message}`, rule);
        }
      }
    }
    
    // Cache result if caching is enabled
    if (this.options.enableCaching) {
      const cacheKey = this._generateCacheKey(data, rules);
      this.resultCache.set(cacheKey, result);
      
      // Limit cache size
      if (this.resultCache.size > this.options.cacheSize) {
        const firstKey = this.resultCache.keys().next().value;
        this.resultCache.delete(firstKey);
      }
    }
    
    // Record metrics if enabled
    if (this.options.enableMetrics) {
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      // Update metrics
      this.metrics.transformations++;
      this.metrics.totalDuration += duration;
      this.metrics.averageDuration = this.metrics.totalDuration / this.metrics.transformations;
    }
    
    return result;
  }
  
  /**
   * Get value from an object by dot-notation path
   * @private
   * @param {Object} obj - Source object
   * @param {string} path - Dot-notation path
   * @returns {*} - Value at the path
   */
  _getValueByPath(obj, path) {
    if (!obj || !path) return undefined;
    
    // Check path cache
    const cacheKey = `${this._getObjectId(obj)}:${path}`;
    if (this.pathCache.has(cacheKey)) {
      return this.pathCache.get(cacheKey);
    }
    
    const parts = path.split('.');
    let current = obj;
    
    for (const part of parts) {
      if (current === null || current === undefined) return undefined;
      current = current[part];
    }
    
    // Cache the result
    if (this.options.enableCaching) {
      this.pathCache.set(cacheKey, current);
      
      // Limit cache size
      if (this.pathCache.size > this.options.cacheSize * 10) {
        const firstKey = this.pathCache.keys().next().value;
        this.pathCache.delete(firstKey);
      }
    }
    
    return current;
  }
  
  /**
   * Set value in an object by dot-notation path
   * @private
   * @param {Object} obj - Target object
   * @param {string} path - Dot-notation path
   * @param {*} value - Value to set
   */
  _setValueByPath(obj, path, value) {
    if (!obj || !path) return;
    
    const parts = path.split('.');
    let current = obj;
    
    for (let i = 0; i < parts.length - 1; i++) {
      const part = parts[i];
      if (!(part in current)) {
        current[part] = {};
      }
      current = current[part];
    }
    
    current[parts[parts.length - 1]] = value;
  }
  
  /**
   * Generate a cache key for data and rules
   * @private
   * @param {Object} data - Source data
   * @param {Array} rules - Transformation rules (optional)
   * @returns {string} - Cache key
   */
  _generateCacheKey(data, rules) {
    const dataHash = this._getObjectId(data);
    
    if (!rules) {
      return dataHash;
    }
    
    const rulesHash = this._getObjectId(rules);
    return `${dataHash}:${rulesHash}`;
  }
  
  /**
   * Get a unique identifier for an object
   * @private
   * @param {Object} obj - Object to identify
   * @returns {string} - Object identifier
   */
  _getObjectId(obj) {
    // Simple hash function for objects
    const str = JSON.stringify(obj);
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    return hash.toString(36);
  }
  
  /**
   * Extract all paths from an object
   * @private
   * @param {Object} obj - Source object
   * @param {string} prefix - Path prefix
   * @returns {Array} - Array of paths
   */
  _extractPaths(obj, prefix = '') {
    let paths = [];
    
    for (const key in obj) {
      const value = obj[key];
      const newPath = prefix ? `${prefix}.${key}` : key;
      
      paths.push(newPath);
      
      if (value !== null && typeof value === 'object' && !Array.isArray(value)) {
        paths = paths.concat(this._extractPaths(value, newPath));
      }
    }
    
    return paths;
  }
  
  /**
   * Get current metrics
   * @returns {Object} - Metrics object
   */
  getMetrics() {
    return { ...this.metrics };
  }
  
  /**
   * Reset metrics
   */
  resetMetrics() {
    this.metrics = {
      transformations: 0,
      totalDuration: 0,
      averageDuration: 0,
      uuftOptimizations: 0,
      cacheHits: 0,
      cacheMisses: 0
    };
  }
  
  /**
   * Clear all caches
   */
  clearCaches() {
    this.resultCache.clear();
    this.pathCache.clear();
    this.importantPathsCache.clear();
  }
}

module.exports = UUFTOptimizedEngine;
