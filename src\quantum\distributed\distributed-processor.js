/**
 * Distributed Processor
 *
 * This module provides distributed processing capabilities for the Finite Universe
 * Principle defense system, coordinating task execution across multiple nodes.
 */

const EventEmitter = require('events');
const { v4: uuidv4 } = require('uuid');
const { ClusterManager, createClusterManager } = require('./cluster-manager');
const { <PERSON>adBalancer, createLoadBalancer } = require('./load-balancer');
const { NodeDiscovery, createNodeDiscovery } = require('./node-discovery');
const { PriorityQueue, createPriorityQueue } = require('./priority-queue');
const { CapabilityRouter, createCapabilityRouter } = require('./capability-router');

/**
 * DistributedProcessor class
 *
 * Coordinates distributed processing across multiple nodes.
 */
class DistributedProcessor extends EventEmitter {
  /**
   * Constructor
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();

    this.options = {
      enableLogging: true,
      isMaster: options.isMaster !== undefined ? options.isMaster : true,
      taskTimeout: options.taskTimeout || 30000, // 30 seconds
      maxRetries: options.maxRetries || 3,
      retryDelay: options.retryDelay || 1000, // 1 second
      ...options
    };

    // Initialize cluster manager
    this.clusterManager = options.clusterManager || createClusterManager({
      enableLogging: this.options.enableLogging,
      isMaster: this.options.isMaster,
      ...options.clusterManagerOptions
    });

    // Initialize load balancer if master
    this.loadBalancer = this.options.isMaster
      ? (options.loadBalancer || createLoadBalancer({
          enableLogging: this.options.enableLogging,
          ...options.loadBalancerOptions
        }))
      : null;

    // Initialize node discovery
    this.nodeDiscovery = options.nodeDiscovery || createNodeDiscovery({
      enableLogging: this.options.enableLogging,
      isMaster: this.options.isMaster,
      nodeId: this.clusterManager.nodeInfo.id,
      capabilities: options.capabilities || ['default'],
      ...options.nodeDiscoveryOptions
    });

    // Initialize priority queue
    this.priorityQueue = options.priorityQueue || createPriorityQueue({
      enableLogging: this.options.enableLogging,
      ...options.priorityQueueOptions
    });

    // Initialize capability router
    this.capabilityRouter = options.capabilityRouter || createCapabilityRouter({
      enableLogging: this.options.enableLogging,
      ...options.capabilityRouterOptions
    });

    // Initialize task registry
    this.tasks = new Map();
    this.taskResults = new Map();
    this.taskRetries = new Map();
    this.taskTimeouts = new Map();

    // Initialize processor function registry
    this.processors = new Map();

    // Register event listeners
    this._registerEventListeners();

    if (this.options.enableLogging) {
      console.log('DistributedProcessor initialized with options:', this.options);
    }
  }

  /**
   * Start the distributed processor
   */
  start() {
    // Start cluster manager
    this.clusterManager.start();

    // Start load balancer if master
    if (this.options.isMaster && this.loadBalancer) {
      this.loadBalancer.start();

      // Register self as a node in the load balancer
      this.loadBalancer.registerNode(this.clusterManager.nodeInfo);
    }

    // Start node discovery
    this.nodeDiscovery.start();

    // Register event listeners for node discovery
    this._registerNodeDiscoveryListeners();

    if (this.options.enableLogging) {
      console.log(`DistributedProcessor started. Role: ${this.options.isMaster ? 'Master' : 'Worker'}`);
    }

    // Emit start event
    this.emit('start', { isMaster: this.options.isMaster });
  }

  /**
   * Stop the distributed processor
   */
  stop() {
    // Clear all task timeouts
    for (const timeoutId of this.taskTimeouts.values()) {
      clearTimeout(timeoutId);
    }

    // Stop node discovery
    this.nodeDiscovery.stop();

    // Stop load balancer if master
    if (this.options.isMaster && this.loadBalancer) {
      this.loadBalancer.stop();
    }

    // Stop cluster manager
    this.clusterManager.stop();

    if (this.options.enableLogging) {
      console.log('DistributedProcessor stopped');
    }

    // Emit stop event
    this.emit('stop');
  }

  /**
   * Register event listeners for node discovery
   * @private
   */
  _registerNodeDiscoveryListeners() {
    // Node discovered event
    this.nodeDiscovery.on('node-discovered', (data) => {
      // Register node in cluster manager
      this.clusterManager.registerNode(data.node);

      // Register node in load balancer if master
      if (this.options.isMaster && this.loadBalancer) {
        this.loadBalancer.registerNode(data.node);
      }

      // Register node in capability router
      this.capabilityRouter.registerNode(data.node);

      // Emit node-discovered event
      this.emit('node-discovered', data);
    });

    // Node updated event
    this.nodeDiscovery.on('node-updated', (data) => {
      // Update node in cluster manager
      this.clusterManager.updateNodeInfo(data.node);

      // Update node in load balancer if master
      if (this.options.isMaster && this.loadBalancer) {
        this.loadBalancer.updateNodeInfo(data.node);
      }

      // Update node in capability router
      this.capabilityRouter.updateNodeInfo(data.node);

      // Emit node-updated event
      this.emit('node-updated', data);
    });

    // Node timeout event
    this.nodeDiscovery.on('node-timeout', (data) => {
      // Unregister node from cluster manager
      this.clusterManager.unregisterNode(data.nodeId);

      // Unregister node from load balancer if master
      if (this.options.isMaster && this.loadBalancer) {
        this.loadBalancer.unregisterNode(data.nodeId);
      }

      // Unregister node from capability router
      this.capabilityRouter.unregisterNode(data.nodeId);

      // Emit node-timeout event
      this.emit('node-timeout', data);
    });
  }

  /**
   * Register a processor function
   * @param {string} type - Processor type
   * @param {Function} processorFn - Processor function
   */
  registerProcessor(type, processorFn) {
    if (typeof processorFn !== 'function') {
      throw new Error(`Processor for type ${type} must be a function`);
    }

    this.processors.set(type, processorFn);

    if (this.options.enableLogging) {
      console.log(`Processor registered for type ${type}`);
    }

    // Emit processor-registered event
    this.emit('processor-registered', { type });
  }

  /**
   * Process data in a distributed manner
   * @param {*} data - Data to process
   * @param {string} type - Processor type
   * @param {string} domain - Domain of the data
   * @param {number} priority - Priority level (0-9, 0 is highest)
   * @param {Array} requiredCapabilities - Required capabilities for processing
   * @returns {Promise<*>} - Processed data
   */
  async process(data, type, domain = '', priority = this.options.defaultPriority, requiredCapabilities = []) {
    // Generate task ID
    const taskId = uuidv4();

    // Create task object
    const taskObj = {
      id: taskId,
      data,
      type,
      domain,
      priority,
      requiredCapabilities: requiredCapabilities.length > 0 ? requiredCapabilities : [this.options.defaultCapability],
      status: 'pending',
      submittedAt: Date.now(),
      submittedBy: this.clusterManager.nodeInfo.id
    };

    // Register task
    this.tasks.set(taskId, taskObj);
    this.taskRetries.set(taskId, 0);

    if (this.options.enableLogging) {
      console.log(`Task ${taskId} submitted for processing with type ${type} in domain ${domain} with priority ${priority}`);
    }

    // Emit task-submitted event
    this.emit('task-submitted', { taskId, data, type, domain, priority, requiredCapabilities });

    // Enqueue task in priority queue
    this.priorityQueue.enqueue(taskObj, priority);

    // Process task
    return this._processTask(taskObj);
  }

  /**
   * Process a task
   * @param {Object} taskObj - Task object
   * @returns {Promise<*>} - Task result
   * @private
   */
  async _processTask(taskObj) {
    // Update task status
    taskObj.status = 'processing';
    taskObj.startedAt = Date.now();

    // Set task timeout
    const timeoutId = setTimeout(() => {
      this._handleTaskTimeout(taskObj);
    }, this.options.taskTimeout);

    this.taskTimeouts.set(taskObj.id, timeoutId);

    try {
      let result;

      // If master, distribute task
      if (this.options.isMaster && this.loadBalancer) {
        result = await this._distributeTask(taskObj);
      } else {
        // Process task locally
        result = await this._processTaskLocally(taskObj);
      }

      // Clear task timeout
      clearTimeout(this.taskTimeouts.get(taskObj.id));
      this.taskTimeouts.delete(taskObj.id);

      // Update task status
      taskObj.status = 'completed';
      taskObj.completedAt = Date.now();
      taskObj.result = result;

      // Store task result
      this.taskResults.set(taskObj.id, result);

      if (this.options.enableLogging) {
        console.log(`Task ${taskObj.id} completed`);
      }

      // Emit task-completed event
      this.emit('task-completed', { taskId: taskObj.id, result });

      return result;
    } catch (error) {
      // Clear task timeout
      clearTimeout(this.taskTimeouts.get(taskObj.id));
      this.taskTimeouts.delete(taskObj.id);

      // Get retry count
      const retryCount = this.taskRetries.get(taskObj.id) || 0;

      // Check if task can be retried
      if (retryCount < this.options.maxRetries) {
        // Increment retry count
        this.taskRetries.set(taskObj.id, retryCount + 1);

        if (this.options.enableLogging) {
          console.log(`Retrying task ${taskObj.id} (${retryCount + 1}/${this.options.maxRetries})`);
        }

        // Emit task-retry event
        this.emit('task-retry', { taskId: taskObj.id, error, retryCount: retryCount + 1 });

        // Wait for retry delay
        await new Promise(resolve => setTimeout(resolve, this.options.retryDelay));

        // Retry task
        return this._processTask(taskObj);
      }

      // Update task status
      taskObj.status = 'failed';
      taskObj.failedAt = Date.now();
      taskObj.error = error.message;

      if (this.options.enableLogging) {
        console.error(`Task ${taskObj.id} failed:`, error);
      }

      // Emit task-failed event
      this.emit('task-failed', { taskId: taskObj.id, error });

      throw error;
    }
  }

  /**
   * Distribute a task to a node
   * @param {Object} taskObj - Task object
   * @returns {Promise<*>} - Task result
   * @private
   */
  async _distributeTask(taskObj) {
    // Route task to a node with matching capabilities
    const node = this.capabilityRouter.routeTask(taskObj, taskObj.requiredCapabilities);

    // If no node with matching capabilities, fall back to load balancer
    if (!node) {
      // Get next node from load balancer
      const fallbackNode = this.loadBalancer.getNextNode(taskObj, taskObj.domain);

      if (!fallbackNode) {
        throw new Error('No nodes available for processing');
      }

      if (this.options.enableLogging) {
        console.log(`No nodes with required capabilities found for task ${taskObj.id}, falling back to load balancer`);
      }

      // Update task with node information
      taskObj.assignedTo = fallbackNode.id;
      taskObj.routedBy = 'load-balancer';

      try {
        // In a real implementation, this would send the task to the selected node
        // For now, we'll just process it locally
        const result = await this._processTaskLocally(taskObj);

        // Release node
        this.loadBalancer.releaseNode(fallbackNode.id, true);

        return result;
      } catch (error) {
        // Release node with failure
        this.loadBalancer.releaseNode(fallbackNode.id, false);

        throw error;
      }
    }

    // Update task with node information
    taskObj.assignedTo = node.id;
    taskObj.routedBy = 'capability-router';

    if (this.options.enableLogging) {
      console.log(`Task ${taskObj.id} routed to node ${node.id} with matching capabilities`);
    }

    try {
      // In a real implementation, this would send the task to the selected node
      // For now, we'll just process it locally
      const result = await this._processTaskLocally(taskObj);

      // Update node load in capability router
      const updatedNode = { ...node, load: (node.load || 0) - 1 };
      this.capabilityRouter.updateNodeInfo(updatedNode);

      return result;
    } catch (error) {
      // Update node status in capability router
      const updatedNode = { ...node, status: 'error' };
      this.capabilityRouter.updateNodeInfo(updatedNode);

      throw error;
    }
  }

  /**
   * Process a task locally
   * @param {Object} taskObj - Task object
   * @returns {Promise<*>} - Task result
   * @private
   */
  async _processTaskLocally(taskObj) {
    // Get processor function
    const processorFn = this.processors.get(taskObj.type);

    if (!processorFn) {
      throw new Error(`No processor registered for type ${taskObj.type}`);
    }

    try {
      // Process data
      const result = await processorFn(taskObj.data, taskObj.domain);

      return result;
    } catch (error) {
      if (this.options.enableLogging) {
        console.error(`Error processing task ${taskObj.id}:`, error);
      }

      throw error;
    }
  }

  /**
   * Handle task timeout
   * @param {Object} taskObj - Task object
   * @private
   */
  _handleTaskTimeout(taskObj) {
    // Update task status
    taskObj.status = 'timeout';
    taskObj.timedOutAt = Date.now();

    if (this.options.enableLogging) {
      console.error(`Task ${taskObj.id} timed out after ${this.options.taskTimeout}ms`);
    }

    // Emit task-timeout event
    this.emit('task-timeout', { taskId: taskObj.id });

    // Get retry count
    const retryCount = this.taskRetries.get(taskObj.id) || 0;

    // Check if task can be retried
    if (retryCount < this.options.maxRetries) {
      // Increment retry count
      this.taskRetries.set(taskObj.id, retryCount + 1);

      if (this.options.enableLogging) {
        console.log(`Retrying task ${taskObj.id} after timeout (${retryCount + 1}/${this.options.maxRetries})`);
      }

      // Emit task-retry event
      this.emit('task-retry', { taskId: taskObj.id, error: 'Task timed out', retryCount: retryCount + 1 });

      // Retry task after delay
      setTimeout(() => {
        this._processTask(taskObj).catch(error => {
          console.error(`Error retrying task ${taskObj.id}:`, error);
        });
      }, this.options.retryDelay);
    }
  }

  /**
   * Register event listeners
   * @private
   */
  _registerEventListeners() {
    // Cluster manager events
    this.clusterManager.on('node-registered', (data) => {
      // Register node in load balancer if master
      if (this.options.isMaster && this.loadBalancer) {
        this.loadBalancer.registerNode(data.nodeInfo);
      }

      // Forward event
      this.emit('node-registered', data);
    });

    this.clusterManager.on('node-unregistered', (data) => {
      // Unregister node from load balancer if master
      if (this.options.isMaster && this.loadBalancer) {
        this.loadBalancer.unregisterNode(data.nodeId);
      }

      // Forward event
      this.emit('node-unregistered', data);
    });

    // Forward other events
    ['start', 'stop', 'heartbeat', 'discovery', 'shutdown'].forEach(event => {
      this.clusterManager.on(event, (data) => {
        this.emit(`cluster-${event}`, data);
      });
    });

    // Load balancer events
    if (this.options.isMaster && this.loadBalancer) {
      ['start', 'stop', 'node-healthy', 'node-unhealthy', 'health-check'].forEach(event => {
        this.loadBalancer.on(event, (data) => {
          this.emit(`loadbalancer-${event}`, data);
        });
      });
    }
  }

  /**
   * Get all tasks
   * @returns {Array} - Array of task objects
   */
  getTasks() {
    return Array.from(this.tasks.values());
  }

  /**
   * Get task by ID
   * @param {string} taskId - Task ID
   * @returns {Object} - Task object
   */
  getTask(taskId) {
    return this.tasks.get(taskId);
  }

  /**
   * Get task result by ID
   * @param {string} taskId - Task ID
   * @returns {*} - Task result
   */
  getTaskResult(taskId) {
    return this.taskResults.get(taskId);
  }

  /**
   * Get cluster manager
   * @returns {ClusterManager} - Cluster manager
   */
  getClusterManager() {
    return this.clusterManager;
  }

  /**
   * Get load balancer
   * @returns {LoadBalancer} - Load balancer
   */
  getLoadBalancer() {
    return this.loadBalancer;
  }

  /**
   * Dispose resources
   */
  dispose() {
    this.stop();

    // Dispose load balancer if master
    if (this.options.isMaster && this.loadBalancer) {
      this.loadBalancer.dispose();
    }

    // Dispose cluster manager
    this.clusterManager.dispose();

    // Dispose node discovery
    this.nodeDiscovery.dispose();

    // Dispose priority queue
    this.priorityQueue.dispose();

    // Dispose capability router
    this.capabilityRouter.dispose();

    // Clear registries
    this.tasks.clear();
    this.taskResults.clear();
    this.taskRetries.clear();
    this.taskTimeouts.clear();
    this.processors.clear();

    if (this.options.enableLogging) {
      console.log('DistributedProcessor disposed');
    }
  }
}

/**
 * Create a distributed processor with recommended settings
 * @param {Object} options - Configuration options
 * @returns {DistributedProcessor} - Configured distributed processor
 */
function createDistributedProcessor(options = {}) {
  return new DistributedProcessor({
    enableLogging: true,
    isMaster: true,
    ...options
  });
}

module.exports = {
  DistributedProcessor,
  createDistributedProcessor
};
