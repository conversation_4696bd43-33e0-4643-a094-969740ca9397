/**
 * Mapper Worker
 * 
 * This module provides a worker thread implementation for parallel data mapping.
 */

const { parentPort } = require('worker_threads');
const jsonpath = require('jsonpath');

// Initialize transformation functions
const transformationFunctions = {
  // String transformations
  lowercase: (value) => typeof value === 'string' ? value.toLowerCase() : value,
  uppercase: (value) => typeof value === 'string' ? value.toUpperCase() : value,
  trim: (value) => typeof value === 'string' ? value.trim() : value,
  substring: (value, params) => {
    if (typeof value !== 'string') return value;
    const start = params?.start || 0;
    const end = params?.end !== undefined ? params.end : value.length;
    return value.substring(start, end);
  },
  replace: (value, params) => {
    if (typeof value !== 'string') return value;
    const pattern = params?.pattern ? new RegExp(params.pattern, params.flags || 'g') : /./g;
    const replacement = params?.replacement || '';
    return value.replace(pattern, replacement);
  },
  
  // Number transformations
  toNumber: (value) => !isNaN(parseFloat(value)) ? parseFloat(value) : value,
  round: (value) => typeof value === 'number' ? Math.round(value) : value,
  floor: (value) => typeof value === 'number' ? Math.floor(value) : value,
  ceil: (value) => typeof value === 'number' ? Math.ceil(value) : value,
  multiply: (value, params) => typeof value === 'number' ? value * (params?.factor || 1) : value,
  divide: (value, params) => typeof value === 'number' ? value / (params?.divisor || 1) : value,
  
  // Boolean transformations
  toBoolean: (value) => Boolean(value),
  negate: (value) => typeof value === 'boolean' ? !value : value,
  
  // Array transformations
  split: (value, params) => typeof value === 'string' ? value.split(params?.separator || ',') : value,
  join: (value, params) => Array.isArray(value) ? value.join(params?.separator || ',') : value,
  filter: (value, params) => {
    if (!Array.isArray(value)) return value;
    if (!params?.property) return value;
    return value.filter(item => item[params.property]);
  },
  map: (value, params) => {
    if (!Array.isArray(value)) return value;
    if (!params?.property) return value;
    return value.map(item => item[params.property]);
  },
  
  // Object transformations
  pick: (value, params) => {
    if (typeof value !== 'object' || value === null) return value;
    if (!params?.properties || !Array.isArray(params.properties)) return value;
    
    const result = {};
    for (const prop of params.properties) {
      if (value[prop] !== undefined) {
        result[prop] = value[prop];
      }
    }
    return result;
  },
  omit: (value, params) => {
    if (typeof value !== 'object' || value === null) return value;
    if (!params?.properties || !Array.isArray(params.properties)) return value;
    
    const result = { ...value };
    for (const prop of params.properties) {
      delete result[prop];
    }
    return result;
  },
  
  // Date transformations
  toDate: (value) => {
    if (value instanceof Date) return value;
    if (typeof value === 'number' || typeof value === 'string') {
      const date = new Date(value);
      return isNaN(date.getTime()) ? value : date;
    }
    return value;
  },
  formatDate: (value, params) => {
    if (!(value instanceof Date)) {
      value = new Date(value);
      if (isNaN(value.getTime())) return value;
    }
    
    const format = params?.format || 'ISO';
    if (format === 'ISO') return value.toISOString();
    if (format === 'UTC') return value.toUTCString();
    if (format === 'locale') return value.toLocaleString();
    return value.toString();
  }
};

/**
 * Map data according to the provided rules
 * @param {Object} data - Source data to map
 * @param {Array} rules - Mapping rules
 * @param {Object} options - Mapping options
 * @returns {Object} - Mapped data
 */
function mapData(data, rules, options = {}) {
  const result = {};
  
  // Apply each rule
  for (const rule of rules) {
    try {
      // Get source value
      let sourceValue;
      
      if (options.enableDirectPropertyAccess && rule.source.indexOf('.') === -1 && rule.source.indexOf('[') === -1) {
        // Direct property access for simple paths
        sourceValue = data[rule.source];
      } else {
        // Use JSONPath for complex paths
        const sourceValues = jsonpath.query(data, rule.source);
        sourceValue = sourceValues.length === 1 ? sourceValues[0] : sourceValues;
      }
      
      // Apply transformation if specified
      let transformedValue = sourceValue;
      
      if (rule.transform) {
        if (typeof rule.transform === 'string') {
          // Single transformation
          const transformFn = transformationFunctions[rule.transform];
          if (transformFn) {
            transformedValue = transformFn(sourceValue, rule.transformParams);
          }
        } else if (Array.isArray(rule.transform)) {
          // Chain of transformations
          transformedValue = rule.transform.reduce((value, transformName) => {
            const transformFn = transformationFunctions[transformName];
            return transformFn ? transformFn(value, rule.transformParams) : value;
          }, sourceValue);
        }
      }
      
      // Set target value
      if (options.enableDirectPropertyAccess && rule.target.indexOf('.') === -1 && rule.target.indexOf('[') === -1) {
        // Direct property access for simple paths
        result[rule.target] = transformedValue;
      } else {
        // Use JSONPath for complex paths
        const targetPath = rule.target;
        const targetParts = targetPath.split('.');
        
        let current = result;
        for (let i = 0; i < targetParts.length - 1; i++) {
          const part = targetParts[i];
          if (!current[part]) {
            current[part] = {};
          }
          current = current[part];
        }
        
        current[targetParts[targetParts.length - 1]] = transformedValue;
      }
    } catch (error) {
      // Continue with next rule
      continue;
    }
  }
  
  return result;
}

// Listen for messages from the parent thread
parentPort.on('message', (message) => {
  try {
    const { messageId, action, data, rules, options } = message;
    
    if (action === 'map') {
      // Map data
      const result = mapData(data, rules, options);
      
      // Send result back to parent thread
      parentPort.postMessage({
        messageId,
        result
      });
    } else {
      // Unknown action
      parentPort.postMessage({
        messageId,
        error: `Unknown action: ${action}`
      });
    }
  } catch (error) {
    // Send error back to parent thread
    parentPort.postMessage({
      messageId: message.messageId,
      error: error.message
    });
  }
});
