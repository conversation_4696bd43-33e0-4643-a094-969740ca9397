/**
 * RBAC Test Setup
 * 
 * This file provides utilities for setting up and tearing down the test environment
 * for RBAC tests, including database connections and test data.
 */

const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const User = require('../../api/models/User');
const Role = require('../../api/models/Role');
const Permission = require('../../api/models/Permission');
const UserRole = require('../../api/models/UserRole');
const { hashPassword } = require('../../api/utils/auth');
const logger = require('../../config/logger');

// Silence the logger during tests
logger.level = 'silent';

let mongoServer;
let adminUser;
let regularUser;
let viewerUser;
let testRole;
let testPermission;

/**
 * Connect to the in-memory database
 */
async function connectToDatabase() {
  try {
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();
    
    await mongoose.connect(mongoUri, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    
    console.log('Connected to in-memory MongoDB');
    return mongoServer;
  } catch (error) {
    console.error('Error connecting to in-memory MongoDB:', error);
    throw error;
  }
}

/**
 * Disconnect from the in-memory database
 */
async function disconnectFromDatabase() {
  try {
    await mongoose.disconnect();
    if (mongoServer) {
      await mongoServer.stop();
    }
    console.log('Disconnected from in-memory MongoDB');
  } catch (error) {
    console.error('Error disconnecting from in-memory MongoDB:', error);
    throw error;
  }
}

/**
 * Clear all collections in the database
 */
async function clearDatabase() {
  try {
    const collections = mongoose.connection.collections;
    
    for (const key in collections) {
      const collection = collections[key];
      await collection.deleteMany({});
    }
    
    console.log('Cleared all collections');
  } catch (error) {
    console.error('Error clearing database:', error);
    throw error;
  }
}

/**
 * Create test users
 */
async function createTestUsers() {
  try {
    // Create admin user
    adminUser = new User({
      username: 'admin',
      email: '<EMAIL>',
      password: await hashPassword('password123'),
      firstName: 'Admin',
      lastName: 'User',
      isAdmin: true
    });
    
    await adminUser.save();
    
    // Create regular user
    regularUser = new User({
      username: 'user',
      email: '<EMAIL>',
      password: await hashPassword('password123'),
      firstName: 'Regular',
      lastName: 'User',
      isAdmin: false
    });
    
    await regularUser.save();
    
    // Create viewer user
    viewerUser = new User({
      username: 'viewer',
      email: '<EMAIL>',
      password: await hashPassword('password123'),
      firstName: 'Viewer',
      lastName: 'User',
      isAdmin: false
    });
    
    await viewerUser.save();
    
    console.log('Created test users');
    return { adminUser, regularUser, viewerUser };
  } catch (error) {
    console.error('Error creating test users:', error);
    throw error;
  }
}

/**
 * Create test permissions
 */
async function createTestPermissions() {
  try {
    const permissions = [
      {
        name: 'View Resources',
        description: 'Permission to view resources',
        resource: 'resource',
        action: 'view',
        isSystem: true
      },
      {
        name: 'Create Resources',
        description: 'Permission to create resources',
        resource: 'resource',
        action: 'create',
        isSystem: true
      },
      {
        name: 'Edit Resources',
        description: 'Permission to edit resources',
        resource: 'resource',
        action: 'edit',
        isSystem: true
      },
      {
        name: 'Delete Resources',
        description: 'Permission to delete resources',
        resource: 'resource',
        action: 'delete',
        isSystem: true
      },
      {
        name: 'View Users',
        description: 'Permission to view users',
        resource: 'user',
        action: 'view',
        isSystem: true
      },
      {
        name: 'Manage Users',
        description: 'Permission to manage users',
        resource: 'user',
        action: 'manage',
        isSystem: true
      }
    ];
    
    const createdPermissions = await Permission.insertMany(permissions);
    testPermission = createdPermissions[0]; // View Resources permission
    
    console.log('Created test permissions');
    return createdPermissions;
  } catch (error) {
    console.error('Error creating test permissions:', error);
    throw error;
  }
}

/**
 * Create test roles
 */
async function createTestRoles() {
  try {
    const permissions = await Permission.find();
    const permissionIds = permissions.map(p => p._id);
    
    const roles = [
      {
        name: 'Administrator',
        description: 'Full access to all resources',
        permissions: ['*'],
        isSystem: true,
        isDefault: false
      },
      {
        name: 'User',
        description: 'Regular user with limited access',
        permissions: [
          permissions.find(p => p.resource === 'resource' && p.action === 'view')._id,
          permissions.find(p => p.resource === 'resource' && p.action === 'create')._id
        ],
        isSystem: true,
        isDefault: true
      },
      {
        name: 'Viewer',
        description: 'Read-only access',
        permissions: [
          permissions.find(p => p.resource === 'resource' && p.action === 'view')._id
        ],
        isSystem: true,
        isDefault: false
      },
      {
        name: 'Test Role',
        description: 'Role for testing',
        permissions: [
          permissions.find(p => p.resource === 'resource' && p.action === 'view')._id
        ],
        isSystem: false,
        isDefault: false
      }
    ];
    
    const createdRoles = await Role.insertMany(roles);
    testRole = createdRoles[3]; // Test Role
    
    console.log('Created test roles');
    return createdRoles;
  } catch (error) {
    console.error('Error creating test roles:', error);
    throw error;
  }
}

/**
 * Assign roles to users
 */
async function assignRolesToUsers() {
  try {
    const adminRole = await Role.findOne({ name: 'Administrator' });
    const userRole = await Role.findOne({ name: 'User' });
    const viewerRole = await Role.findOne({ name: 'Viewer' });
    
    // Assign admin role to admin user
    await UserRole.create({
      user: adminUser._id,
      role: adminRole._id
    });
    
    // Assign user role to regular user
    await UserRole.create({
      user: regularUser._id,
      role: userRole._id
    });
    
    // Assign viewer role to viewer user
    await UserRole.create({
      user: viewerUser._id,
      role: viewerRole._id
    });
    
    console.log('Assigned roles to users');
  } catch (error) {
    console.error('Error assigning roles to users:', error);
    throw error;
  }
}

/**
 * Set up the test environment
 */
async function setupTestEnvironment() {
  try {
    await connectToDatabase();
    await clearDatabase();
    await createTestUsers();
    await createTestPermissions();
    await createTestRoles();
    await assignRolesToUsers();
    
    console.log('Test environment set up successfully');
    
    return {
      adminUser,
      regularUser,
      viewerUser,
      testRole,
      testPermission
    };
  } catch (error) {
    console.error('Error setting up test environment:', error);
    await disconnectFromDatabase();
    throw error;
  }
}

module.exports = {
  setupTestEnvironment,
  clearDatabase,
  disconnectFromDatabase,
  getTestData: () => ({
    adminUser,
    regularUser,
    viewerUser,
    testRole,
    testPermission
  })
};
