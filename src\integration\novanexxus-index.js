/**
 * 🪐 NovaNexxus™: The Living Architecture of Cyber-Safety
 * 
 * This file exports all the components of the NovaNexxus™ architecture.
 * The system ties together the NovaTriad™ (NovaCore, NovaProof, NovaConnect)
 * with the Sentinels (CSDE and NovaVision), with CSDE serving as the
 * foundational engine powering all components.
 */

// Export NovaNexxus
const { 
  NovaNexxus, 
  IntegrationStatus 
} = require('./novanexxus');

// Export CSDE Foundation (Sentinel)
const { 
  CSDEFoundation, 
  CSDEDomain, 
  CSDEOperation, 
  CSDEStatus 
} = require('./csde-foundation');

// Export NovaVision Integration (Sentinel)
const { NovaVisionIntegration } = require('./novavision-integration');

// Export Unified API
const { UnifiedAPI } = require('./unified-api');

/**
 * Create a NovaNexxus system
 * @param {Object} options - Configuration options
 * @returns {Object} - NovaNexxus system
 */
function createNovaNexxus(options = {}) {
  // Create CSDE Foundation (Sentinel)
  const csde = new CSDEFoundation({
    enableLogging: options.enableLogging !== undefined ? options.enableLogging : true,
    apiUrl: options.csdeApiUrl || process.env.CSDE_API_URL,
    apiKey: options.csdeApiKey || process.env.CSDE_API_KEY
  });
  
  // Create NovaNexxus
  const novanexxus = new NovaNexxus({
    enableLogging: options.enableLogging !== undefined ? options.enableLogging : true,
    enableMetrics: options.enableMetrics !== undefined ? options.enableMetrics : true,
    enableCaching: options.enableCaching !== undefined ? options.enableCaching : true,
    novaCore: options.novaCore,
    novaProof: options.novaProof,
    novaConnect: options.novaConnect,
    novaVision: options.novaVision,
    csde: csde
  });
  
  // Create NovaVision Integration (Sentinel)
  const novaVisionIntegration = new NovaVisionIntegration({
    enableLogging: options.enableLogging !== undefined ? options.enableLogging : true,
    novaVision: options.novaVision,
    novaFuseIntegration: novanexxus,
    csde: csde
  });
  
  // Create Unified API
  const unifiedAPI = new UnifiedAPI({
    port: options.port || process.env.PORT || 3000,
    enableLogging: options.enableLogging !== undefined ? options.enableLogging : true,
    enableCors: options.enableCors !== undefined ? options.enableCors : true,
    novaCore: options.novaCore,
    novaProof: options.novaProof,
    novaConnect: options.novaConnect,
    novaVision: options.novaVision,
    csdeApiUrl: options.csdeApiUrl || process.env.CSDE_API_URL,
    csdeApiKey: options.csdeApiKey || process.env.CSDE_API_KEY
  });
  
  // Return the NovaNexxus system
  return {
    novanexxus,
    csde,
    novaVisionIntegration,
    unifiedAPI,
    
    // Initialize the system
    async initialize() {
      try {
        // Initialize CSDE Foundation
        await csde.initialize();
        
        // Initialize NovaNexxus
        await novanexxus.initialize();
        
        // Initialize NovaVision Integration
        await novaVisionIntegration.initialize();
        
        // Initialize Unified API
        await unifiedAPI.start();
        
        return {
          status: 'initialized',
          components: {
            novanexxus: novanexxus.status,
            csde: csde.status,
            novaVisionIntegration: 'ready',
            unifiedAPI: 'running'
          }
        };
      } catch (error) {
        console.error('Error initializing NovaNexxus system:', error);
        throw error;
      }
    },
    
    // Shutdown the system
    async shutdown() {
      try {
        // Shutdown Unified API
        await unifiedAPI.stop();
        
        // Shutdown NovaVision Integration
        await novaVisionIntegration.shutdown();
        
        // Shutdown NovaNexxus
        await novanexxus.shutdown();
        
        // Shutdown CSDE Foundation
        await csde.shutdown();
        
        return {
          status: 'shutdown',
          components: {
            novanexxus: novanexxus.status,
            csde: csde.status,
            novaVisionIntegration: 'shutdown',
            unifiedAPI: 'stopped'
          }
        };
      } catch (error) {
        console.error('Error shutting down NovaNexxus system:', error);
        throw error;
      }
    }
  };
}

// Export all components
module.exports = {
  // NovaNexxus
  NovaNexxus,
  IntegrationStatus,
  
  // CSDE Foundation (Sentinel)
  CSDEFoundation,
  CSDEDomain,
  CSDEOperation,
  CSDEStatus,
  
  // NovaVision Integration (Sentinel)
  NovaVisionIntegration,
  
  // Unified API
  UnifiedAPI,
  
  // Factory function
  createNovaNexxus
};
