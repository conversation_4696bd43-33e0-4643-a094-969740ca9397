/**
 * PsiRevertProtocolEngine
 *
 * This module implements the PsiRevertProtocolEngine class, which manages intervention
 * protocols for restoring biological coherence.
 *
 * The PsiRevertProtocolEngine is responsible for:
 * 1. Managing a library of intervention protocols
 * 2. Selecting appropriate protocols based on subject state
 * 3. Calculating protocol priorities
 * 4. Generating detailed protocol instructions
 * 5. Supporting multiple protocol types (environmental, dietary, genetic, emergency)
 */

const { performance } = require('perf_hooks');

/**
 * PsiRevertProtocolEngine class
 */
class PsiRevertProtocolEngine {
  /**
   * Create a new PsiRevertProtocolEngine instance
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    this.options = {
      protocolTypes: ['environmental', 'dietary', 'genetic', 'emergency', 'behavioral', 'cognitive'],
      enableCaching: true,
      enableMetrics: true,
      emergencyThreshold: 0.3, // Coherence threshold for emergency protocols
      priorityThreshold: 0.6, // Coherence threshold for priority protocols
      standardThreshold: 0.82, // Coherence threshold for standard protocols (based on 18/82 principle)
      ...options
    };

    // Initialize protocol library
    this.protocols = {};
    this._initializeProtocolLibrary();

    // Initialize cache
    this.cache = new Map();

    // Initialize metrics
    this.metrics = {
      processingTimeMs: 0,
      cacheHits: 0,
      cacheMisses: 0,
      totalProcessed: 0,
      protocolsGenerated: 0
    };

    console.log('PsiRevertProtocolEngine initialized with protocol types:', this.options.protocolTypes);
  }

  /**
   * Select appropriate protocol based on subject state
   * @param {Object} subjectState - Current state of the subject
   * @returns {Object} - Selected protocol
   */
  selectProtocol(subjectState) {
    const startTime = performance.now();
    this.metrics.totalProcessed++;

    // Generate cache key
    const cacheKey = this._generateCacheKey(subjectState);

    // Check cache if enabled
    if (this.options.enableCaching && this.cache.has(cacheKey)) {
      this.metrics.cacheHits++;
      const cachedResult = this.cache.get(cacheKey);

      // Update processing time
      this.metrics.processingTimeMs += performance.now() - startTime;

      return cachedResult;
    }

    this.metrics.cacheMisses++;

    // Extract key subject metrics
    const {
      coherence = 0.82,
      entropyGradient = 0,
      components = {},
      environmentalContext = {},
      medicalHistory = {},
      geneticProfile = {},
      currentInterventions = []
    } = subjectState;

    // Determine protocol type based on coherence level
    let protocolType;
    if (coherence < this.options.emergencyThreshold) {
      protocolType = 'emergency';
    } else if (coherence < this.options.priorityThreshold) {
      // For priority protocols, determine the most affected component
      const componentValues = {
        environmental: components.environmentalEntropy || 0.5,
        dietary: components.proteomicEntropy || 0.5,
        genetic: components.genomicEntropy || 0.5,
        behavioral: components.behavioralEntropy || 0.5,
        cognitive: components.cognitiveEntropy || 0.5
      };

      // Find the component with highest entropy (most affected)
      protocolType = Object.entries(componentValues)
        .sort((a, b) => b[1] - a[1])[0][0];
    } else {
      // For standard protocols, use a balanced approach
      protocolType = 'standard';
    }

    // Get candidate protocols of the selected type
    const candidateProtocols = this._getCandidateProtocols(protocolType, subjectState);

    // Calculate protocol priorities
    const prioritizedProtocols = this.calculateProtocolPriorities(candidateProtocols, subjectState);

    // Select the highest priority protocol
    const selectedProtocol = prioritizedProtocols.length > 0 ? prioritizedProtocols[0] : null;

    // Generate detailed instructions if a protocol was selected
    let result;
    if (selectedProtocol) {
      const instructions = this.generateProtocolInstructions(selectedProtocol, subjectState);

      result = {
        protocol: selectedProtocol,
        instructions,
        alternativeProtocols: prioritizedProtocols.slice(1, 4), // Include up to 3 alternatives
        protocolType,
        coherenceTarget: Math.min(1, coherence + 0.2), // Target 20% improvement in coherence
        selectedAt: new Date().toISOString()
      };

      this.metrics.protocolsGenerated++;
    } else {
      // No suitable protocol found
      result = {
        protocol: null,
        instructions: null,
        alternativeProtocols: [],
        protocolType: 'none',
        coherenceTarget: coherence,
        selectedAt: new Date().toISOString()
      };
    }

    // Cache result if caching is enabled
    if (this.options.enableCaching) {
      this.cache.set(cacheKey, result);

      // Limit cache size
      if (this.cache.size > 1000) {
        const oldestKey = this.cache.keys().next().value;
        this.cache.delete(oldestKey);
      }
    }

    // Update processing time
    this.metrics.processingTimeMs += performance.now() - startTime;

    return result;
  }

  /**
   * Calculate priorities for multiple protocols
   * @param {Array} protocols - Array of protocols
   * @param {Object} subjectState - Current state of the subject
   * @returns {Array} - Prioritized protocols
   */
  calculateProtocolPriorities(protocols, subjectState) {
    if (!protocols || protocols.length === 0) {
      return [];
    }

    // Extract key subject metrics
    const {
      coherence = 0.82,
      entropyGradient = 0,
      components = {},
      environmentalContext = {},
      medicalHistory = {},
      geneticProfile = {},
      currentInterventions = []
    } = subjectState;

    // Calculate priority for each protocol
    const prioritizedProtocols = protocols.map(protocol => {
      // Base priority from protocol definition
      let priority = protocol.basePriority || 0.5;

      // Adjust priority based on coherence level (lower coherence = higher priority)
      priority += (1 - coherence) * 0.3;

      // Adjust priority based on entropy gradient (negative gradient = higher priority)
      priority += entropyGradient < 0 ? Math.abs(entropyGradient) * 0.2 : 0;

      // Adjust priority based on protocol effectiveness for this subject
      priority += this._calculateProtocolEffectiveness(protocol, subjectState) * 0.3;

      // Adjust priority based on protocol complexity (simpler protocols get higher priority)
      priority += (1 - (protocol.complexity || 0.5)) * 0.1;

      // Adjust priority based on protocol side effects (fewer side effects = higher priority)
      priority += (1 - (protocol.sideEffects || 0.5)) * 0.1;

      // Check if protocol is already in use (avoid duplicates)
      const isAlreadyInUse = currentInterventions.some(
        intervention => intervention.protocolId === protocol.id
      );

      if (isAlreadyInUse) {
        priority = 0; // Set priority to 0 for protocols already in use
      }

      return {
        ...protocol,
        calculatedPriority: priority
      };
    });

    // Sort protocols by priority (descending)
    return prioritizedProtocols
      .filter(protocol => protocol.calculatedPriority > 0)
      .sort((a, b) => b.calculatedPriority - a.calculatedPriority);
  }

  /**
   * Generate detailed instructions for implementing the protocol
   * @param {Object} protocol - Selected protocol
   * @param {Object} subjectState - Current state of the subject
   * @returns {Object} - Detailed protocol instructions
   */
  generateProtocolInstructions(protocol, subjectState) {
    if (!protocol) {
      return null;
    }

    // Extract key subject metrics
    const {
      coherence = 0.82,
      components = {},
      environmentalContext = {},
      medicalHistory = {},
      geneticProfile = {}
    } = subjectState;

    // Generate base instructions from protocol template
    let instructions = {
      title: protocol.name,
      description: protocol.description,
      steps: [...(protocol.steps || [])],
      duration: protocol.duration,
      frequency: protocol.frequency,
      monitoringRequirements: protocol.monitoringRequirements,
      expectedOutcomes: protocol.expectedOutcomes,
      potentialSideEffects: protocol.potentialSideEffects,
      contraindications: protocol.contraindications,
      emergencyProcedures: protocol.emergencyProcedures,
      generatedAt: new Date().toISOString()
    };

    // Personalize instructions based on subject state
    instructions = this._personalizeInstructions(instructions, subjectState, protocol);

    return instructions;
  }

  /**
   * Add a new protocol to the library
   * @param {Object} protocol - Protocol definition
   * @returns {boolean} - Success status
   */
  addProtocol(protocol) {
    if (!protocol || !protocol.id || !protocol.type) {
      console.error('Invalid protocol definition');
      return false;
    }

    // Ensure protocol type exists
    if (!this.protocols[protocol.type]) {
      this.protocols[protocol.type] = [];
    }

    // Check if protocol with same ID already exists
    const existingIndex = this.protocols[protocol.type].findIndex(p => p.id === protocol.id);
    if (existingIndex >= 0) {
      // Update existing protocol
      this.protocols[protocol.type][existingIndex] = protocol;
    } else {
      // Add new protocol
      this.protocols[protocol.type].push(protocol);
    }

    return true;
  }

  /**
   * Remove a protocol from the library
   * @param {string} protocolId - Protocol ID
   * @param {string} protocolType - Protocol type
   * @returns {boolean} - Success status
   */
  removeProtocol(protocolId, protocolType) {
    if (!protocolId || !protocolType || !this.protocols[protocolType]) {
      return false;
    }

    const initialLength = this.protocols[protocolType].length;
    this.protocols[protocolType] = this.protocols[protocolType].filter(p => p.id !== protocolId);

    return this.protocols[protocolType].length < initialLength;
  }

  /**
   * Get all protocols of a specific type
   * @param {string} protocolType - Protocol type
   * @returns {Array} - Array of protocols
   */
  getProtocolsByType(protocolType) {
    return this.protocols[protocolType] || [];
  }

  /**
   * Initialize protocol library with default protocols
   * @private
   */
  _initializeProtocolLibrary() {
    // Initialize empty protocol collections for each type
    this.options.protocolTypes.forEach(type => {
      this.protocols[type] = [];
    });

    // Add standard protocol type
    this.protocols['standard'] = [];

    // Add sample protocols (in a real implementation, these would be loaded from a database)
    this._addSampleProtocols();
  }

  /**
   * Add sample protocols to the library
   * @private
   */
  _addSampleProtocols() {
    // Environmental protocols
    this.addProtocol({
      id: 'env-001',
      type: 'environmental',
      name: 'Optimal Oxygen Environment',
      description: 'Adjusts environmental oxygen levels to optimize cellular respiration',
      basePriority: 0.7,
      complexity: 0.3,
      sideEffects: 0.1,
      duration: '4 hours per day',
      frequency: 'Daily',
      steps: [
        'Ensure room has proper ventilation',
        'Maintain oxygen levels between 21-23%',
        'Keep humidity between 40-50%',
        'Maintain temperature between 20-22°C'
      ],
      monitoringRequirements: ['Oxygen level', 'Humidity', 'Temperature'],
      expectedOutcomes: ['Improved cellular respiration', 'Reduced oxidative stress'],
      potentialSideEffects: ['Mild headache during adaptation'],
      contraindications: ['Certain respiratory conditions'],
      emergencyProcedures: ['Return to normal atmospheric conditions if discomfort occurs']
    });

    // Dietary protocols
    this.addProtocol({
      id: 'diet-001',
      type: 'dietary',
      name: 'Cellular Regeneration Nutrition Protocol',
      description: 'Optimizes nutrient intake to support cellular regeneration',
      basePriority: 0.6,
      complexity: 0.5,
      sideEffects: 0.2,
      duration: 'Ongoing',
      frequency: 'Daily',
      steps: [
        'Consume 3-5 servings of antioxidant-rich foods daily',
        'Include omega-3 fatty acids in diet',
        'Maintain adequate protein intake (0.8-1.2g/kg body weight)',
        'Ensure sufficient micronutrient intake',
        'Maintain hydration (2-3 liters of water daily)'
      ],
      monitoringRequirements: ['Nutrient levels', 'Hydration status'],
      expectedOutcomes: ['Improved cellular function', 'Reduced inflammation'],
      potentialSideEffects: ['Digestive adjustment period'],
      contraindications: ['Certain metabolic disorders'],
      emergencyProcedures: ['Discontinue if allergic reaction occurs']
    });

    // Emergency protocols
    this.addProtocol({
      id: 'emerg-001',
      type: 'emergency',
      name: 'Rapid Coherence Restoration Protocol',
      description: 'Emergency intervention to rapidly restore coherence in critical situations',
      basePriority: 0.9,
      complexity: 0.2,
      sideEffects: 0.4,
      duration: '1-4 hours',
      frequency: 'As needed in emergencies',
      steps: [
        'Remove subject from harmful environmental factors',
        'Administer oxygen therapy if available',
        'Ensure proper hydration',
        'Administer antioxidant supplements',
        'Monitor vital signs continuously'
      ],
      monitoringRequirements: ['Vital signs', 'Coherence levels', 'Oxygen saturation'],
      expectedOutcomes: ['Rapid coherence improvement', 'Stabilization of vital functions'],
      potentialSideEffects: ['Temporary discomfort', 'Mild anxiety'],
      contraindications: ['None in true emergencies'],
      emergencyProcedures: ['Seek medical attention if no improvement within 1 hour']
    });
  }

  /**
   * Get candidate protocols based on type and subject state
   * @param {string} protocolType - Protocol type
   * @param {Object} subjectState - Current state of the subject
   * @returns {Array} - Array of candidate protocols
   * @private
   */
  _getCandidateProtocols(protocolType, subjectState) {
    // For standard protocol type, consider protocols from all types
    if (protocolType === 'standard') {
      return Object.values(this.protocols).flat();
    }

    // Otherwise, return protocols of the specified type
    return this.protocols[protocolType] || [];
  }

  /**
   * Calculate effectiveness of a protocol for a specific subject
   * @param {Object} protocol - Protocol definition
   * @param {Object} subjectState - Current state of the subject
   * @returns {number} - Effectiveness score (0-1)
   * @private
   */
  _calculateProtocolEffectiveness(protocol, subjectState) {
    // In a real implementation, this would use a more sophisticated model
    // based on historical data, genetic factors, and other subject-specific metrics

    // For now, use a simple heuristic based on protocol type and subject components
    const { components = {} } = subjectState;

    switch (protocol.type) {
      case 'environmental':
        // Environmental protocols are more effective when environmental entropy is high
        return 1 - (components.environmentalEntropy || 0.5);
      case 'dietary':
        // Dietary protocols are more effective when proteomic entropy is high
        return 1 - (components.proteomicEntropy || 0.5);
      case 'genetic':
        // Genetic protocols are more effective when genomic entropy is high
        return 1 - (components.genomicEntropy || 0.5);
      case 'behavioral':
        // Behavioral protocols are more effective when behavioral entropy is high
        return 1 - (components.behavioralEntropy || 0.5);
      case 'cognitive':
        // Cognitive protocols are more effective when cognitive entropy is high
        return 1 - (components.cognitiveEntropy || 0.5);
      case 'emergency':
        // Emergency protocols have fixed high effectiveness in emergency situations
        return 0.9;
      default:
        return 0.5; // Default effectiveness
    }
  }

  /**
   * Personalize protocol instructions based on subject state
   * @param {Object} instructions - Base instructions
   * @param {Object} subjectState - Current state of the subject
   * @param {Object} protocol - Protocol definition
   * @returns {Object} - Personalized instructions
   * @private
   */
  _personalizeInstructions(instructions, subjectState, protocol) {
    // In a real implementation, this would use more sophisticated personalization
    // based on subject-specific factors

    // For now, implement basic personalization
    const personalizedInstructions = { ...instructions };

    // Add personalized note
    const coherence = subjectState.coherence || 0.82; // Default to 0.82 if undefined
    personalizedInstructions.personalizedNote =
      `This protocol has been specifically adapted for your current coherence level of ${
        coherence.toFixed(2)
      }. The goal is to improve your coherence to at least ${
        Math.min(1, coherence + 0.2).toFixed(2)
      }.`;

    // Adjust duration based on coherence level
    if (coherence < 0.5) {
      // For low coherence, intensify the protocol
      personalizedInstructions.duration =
        personalizedInstructions.duration.replace(/(\d+)/, match => Math.min(24, parseInt(match) * 1.5));
      personalizedInstructions.frequency = 'Every 4-6 hours';
    }

    return personalizedInstructions;
  }

  /**
   * Generate cache key for subject state
   * @param {Object} subjectState - Subject state
   * @returns {string} - Cache key
   * @private
   */
  _generateCacheKey(subjectState) {
    try {
      // Use only essential properties for the key
      const keyData = {
        c: Math.round(subjectState.coherence * 100) / 100,
        eg: Math.round(subjectState.entropyGradient * 100) / 100,
        comp: {
          ge: Math.round((subjectState.components?.genomicEntropy || 0.5) * 100) / 100,
          pe: Math.round((subjectState.components?.proteomicEntropy || 0.5) * 100) / 100,
          ce: Math.round((subjectState.components?.clinicalEntropy || 0.5) * 100) / 100,
          ee: Math.round((subjectState.components?.environmentalEntropy || 0.5) * 100) / 100
        },
        ci: subjectState.currentInterventions?.map(i => i.protocolId) || []
      };

      return JSON.stringify(keyData);
    } catch (error) {
      console.error('Error generating cache key:', error);
      return Date.now().toString(); // Fallback to timestamp
    }
  }
}

module.exports = PsiRevertProtocolEngine;
