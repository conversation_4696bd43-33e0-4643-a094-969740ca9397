/**
 * Security Components
 *
 * This module exports security components for the Finite Universe
 * Principle defense system, including RBAC, audit logging, and secure storage.
 */

const { RBAC, createRBAC } = require('./rbac');
const { AuditLogger, createAuditLogger } = require('./audit-logger');
const { SecureStorage, createSecureStorage } = require('./secure-storage');
const { MFAService, createMFAService } = require('./authentication/mfa-service');
const { IPAccessControl, createIPAccessControl } = require('./access-control/ip-access-control');
const { ThreatDetector, createThreatDetector } = require('./threat-detection/threat-detector');

/**
 * Create all security components
 * @param {Object} options - Configuration options
 * @returns {Object} - Object containing all security components
 */
function createSecurityComponents(options = {}) {
  // Create RBAC
  const rbac = createRBAC({
    enableLogging: options.enableLogging !== undefined ? options.enableLogging : true,
    ...options.rbacOptions
  });

  // Create audit logger
  const auditLogger = createAuditLogger({
    enableLogging: options.enableLogging !== undefined ? options.enableLogging : true,
    ...options.auditLoggerOptions
  });

  // Create secure storage
  const secureStorage = createSecureStorage({
    enableLogging: options.enableLogging !== undefined ? options.enableLogging : true,
    ...options.secureStorageOptions
  });

  // Create multi-factor authentication service
  const mfaService = createMFAService({
    enableLogging: options.enableLogging !== undefined ? options.enableLogging : true,
    ...options.mfaServiceOptions
  });

  // Create IP access control
  const ipAccessControl = createIPAccessControl({
    enableLogging: options.enableLogging !== undefined ? options.enableLogging : true,
    ...options.ipAccessControlOptions
  });

  // Create threat detector
  const threatDetector = createThreatDetector({
    enableLogging: options.enableLogging !== undefined ? options.enableLogging : true,
    ...options.threatDetectorOptions
  });

  return {
    rbac,
    auditLogger,
    secureStorage,
    mfaService,
    ipAccessControl,
    threatDetector
  };
}

module.exports = {
  // RBAC
  RBAC,
  createRBAC,

  // Audit logger
  AuditLogger,
  createAuditLogger,

  // Secure storage
  SecureStorage,
  createSecureStorage,

  // Multi-factor authentication
  MFAService,
  createMFAService,

  // IP access control
  IPAccessControl,
  createIPAccessControl,

  // Threat detection
  ThreatDetector,
  createThreatDetector,

  // Factory function
  createSecurityComponents
};
