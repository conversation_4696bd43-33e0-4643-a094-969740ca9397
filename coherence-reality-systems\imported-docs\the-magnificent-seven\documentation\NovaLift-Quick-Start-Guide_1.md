# NovaLift Quick Start Guide
## Get NovaFuse Coherence Operating System Running in Minutes

### **🚀 OVERVIEW**

NovaLift is the enterprise system booster that deploys the complete NovaFuse Coherence Operating System. This guide gets you from zero to running in under 10 minutes.

---

## **⚡ QUICK INSTALLATION**

### **Windows (PowerShell)**
```powershell
# Download and run installer
.\install-novalift.ps1 -InstallPath "C:\NovaFuse" -Mode "Enterprise"

# Start NovaAgent
C:\NovaFuse\NovaAgent\nova-agent.exe

# Open NovaFuse Console
# http://localhost:3000
```

### **Linux/macOS (Bash)**
```bash
# Make installer executable
chmod +x install-novalift.sh

# Run installer
./install-novalift.sh /opt/novafuse Enterprise

# Start service
sudo systemctl start novafuse

# Open NovaFuse Console
# http://localhost:3000
```

---

## **🎯 WHAT GETS INSTALLED**

### **Core Components**
- **NovaCore**: NHET-X CASTL™ engines (NEPI, NEFC, NERS, NERE, NECE)
- **NovaAgent**: Unified runtime orchestrator
- **NovaBridge**: Enterprise API integration layer
- **NovaConsole**: React dashboard (port 3000)

### **Directory Structure**
```
/opt/novafuse (Linux) or C:\NovaFuse (Windows)
├── NovaCore/          # CASTL™ engines
├── NovaAgent/         # Runtime executable
├── NovaBridge/        # API layer
├── NovaConsole/       # Web dashboard
├── Config/            # Configuration files
├── Logs/              # System logs
└── Data/              # Runtime data
```

---

## **🔧 CONFIGURATION**

### **Main Config: novafuse-config.json**
```json
{
  "novafuse": {
    "version": "1.0.0",
    "mode": "Enterprise",
    "components": {
      "novacore": { "enabled": true, "port": 8000 },
      "novabridge": { "enabled": true, "port": 8001 },
      "novaconsole": { "enabled": true, "port": 3000 }
    }
  }
}
```

### **NovaLift Config: novalift-config.json**
```json
{
  "novalift": {
    "auto_start": true,
    "monitoring": { "enabled": true, "interval": 30 },
    "logging": { "level": "INFO", "max_size": "100MB" }
  }
}
```

---

## **🚀 STARTING NOVAFUSE**

### **Option 1: NovaAgent Direct**
```bash
# Windows
C:\NovaFuse\NovaAgent\nova-agent.exe

# Linux/macOS
/opt/novafuse/NovaAgent/nova-agent
```

### **Option 2: System Service (Linux)**
```bash
# Start service
sudo systemctl start novafuse

# Enable auto-start
sudo systemctl enable novafuse

# Check status
sudo systemctl status novafuse
```

### **Option 3: Manual Components**
```bash
# Start NovaCore (Terminal 1)
cd /opt/novafuse/NovaCore
node aeonix-kernel-orchestrator.js

# Start NovaBridge (Terminal 2)
cd /opt/novafuse/NovaBridge
python -m uvicorn main:app --host 0.0.0.0 --port 8001

# Start NovaConsole (Terminal 3)
cd /opt/novafuse/NovaConsole
npm run dev
```

---

## **🌐 ACCESSING NOVAFUSE**

### **NovaFuse Platform Console**
- **URL**: http://localhost:3000
- **Features**: Real-time dashboard, module status, coherence metrics
- **Login**: No authentication required (demo mode)

### **API Endpoints**
- **NovaCore**: http://localhost:8000
- **NovaBridge**: http://localhost:8001
- **Health Check**: http://localhost:8001/health
- **WebSocket**: ws://localhost:8000/ws

---

## **🔍 VERIFICATION**

### **Quick Health Check**
```bash
# NovaAgent health check
nova-agent --health-check

# Manual verification
curl http://localhost:8000/health    # NovaCore
curl http://localhost:8001/health    # NovaBridge
curl http://localhost:3000          # NovaConsole
```

### **Expected Responses**
- **NovaCore**: Engine status and coherence metrics
- **NovaBridge**: API health and connector status
- **NovaConsole**: React application loads successfully

---

## **🚨 TROUBLESHOOTING**

### **Common Issues**

#### **Port Already in Use**
```bash
# Check what's using the port
netstat -tulpn | grep :8000
netstat -tulpn | grep :8001
netstat -tulpn | grep :3000

# Kill process if needed
sudo kill -9 <PID>
```

#### **Permission Denied**
```bash
# Fix permissions (Linux)
sudo chown -R $USER:$USER /opt/novafuse
chmod +x /opt/novafuse/NovaAgent/nova-agent

# Run as administrator (Windows)
# Right-click PowerShell -> "Run as Administrator"
```

#### **Dependencies Missing**
```bash
# Install Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install Python
sudo apt-get install python3 python3-pip

# Install Python packages
pip3 install fastapi uvicorn aiohttp requests
```

#### **NovaAgent Won't Start**
```bash
# Check configuration
nova-agent --config=/path/to/config.json --log-level=DEBUG

# Check logs
tail -f /opt/novafuse/Logs/nova-agent.log

# Verify components exist
ls -la /opt/novafuse/NovaCore
ls -la /opt/novafuse/NovaBridge
```

---

## **📊 MONITORING**

### **Log Files**
- **NovaAgent**: `/opt/novafuse/Logs/nova-agent.log`
- **NovaCore**: `/opt/novafuse/Logs/novacore.log`
- **NovaBridge**: `/opt/novafuse/Logs/novabridge.log`

### **System Status**
```bash
# Service status (Linux)
sudo systemctl status novafuse

# Process monitoring
ps aux | grep nova

# Resource usage
top -p $(pgrep nova-agent)
```

### **Performance Metrics**
- **CPU Usage**: Monitor via system tools
- **Memory Usage**: Typically 100-500MB total
- **Network**: Ports 8000, 8001, 3000
- **Disk**: Logs rotate at 100MB

---

## **🔧 ADVANCED CONFIGURATION**

### **Environment Variables**
```bash
export NOVA_INSTALL_PATH=/opt/novafuse
export NOVA_MODE=Enterprise
export NOVA_LOG_LEVEL=INFO
export NOVA_CORE_PORT=8000
export NOVA_BRIDGE_PORT=8001
export NOVA_CONSOLE_PORT=3000
```

### **Custom Ports**
Edit `novafuse-config.json`:
```json
{
  "components": {
    "novacore": { "port": 9000 },
    "novabridge": { "port": 9001 },
    "novaconsole": { "port": 9002 }
  }
}
```

### **Enterprise Integration**
Configure enterprise connectors in `novabridge_enterprise_connectors.py`:
- Microsoft Compliance Center
- ServiceNow
- Splunk
- Snowflake

---

## **🚀 NEXT STEPS**

### **Development**
1. **Explore NovaCore Engines**: NEPI, NEFC, NERS capabilities
2. **Customize Dashboard**: Modify React components
3. **Add Connectors**: Integrate with your enterprise systems
4. **Extend APIs**: Add custom endpoints to NovaBridge

### **Production Deployment**
1. **Security**: Configure authentication and HTTPS
2. **Monitoring**: Set up Prometheus/Grafana
3. **Backup**: Implement data backup procedures
4. **Scaling**: Configure load balancing

### **Enterprise Features**
1. **CPMF™ Integration**: Use Comphyological Project Management
2. **82/18 Model**: Apply coherence emergence principles
3. **NovaLearn**: Implement gamified compliance
4. **Compliance**: FedRAMP, ISO 27001 preparation

---

## **📞 SUPPORT**

### **Documentation**
- **Full Documentation**: See comprehensive guides in repository
- **API Reference**: Available at http://localhost:8001/docs
- **Component Docs**: Individual README files in each directory

### **Testing**
```bash
# Run integration tests
./test-novalift-integration.ps1    # Windows
./test-novalift-integration.sh     # Linux (if created)
```

### **Community**
- **Issues**: Report via GitHub issues
- **Discussions**: Community forums
- **Updates**: Follow release notes

---

## **🎯 SUCCESS CRITERIA**

**✅ NovaFuse is working correctly when:**
- NovaAgent starts without errors
- All three ports (8000, 8001, 3000) respond
- NovaFuse Console loads in browser
- Health checks return positive status
- Logs show no critical errors

**🚀 You're now running the world's first Coherence Operating System!**

---

**Status**: QUICK START GUIDE COMPLETE
**Time to Deploy**: Under 10 minutes
**Complexity**: Minimal - just run the installer
**Support**: Comprehensive troubleshooting included
