/**
 * CSDE Remediation Integration
 * 
 * This module provides automated remediation capabilities based on CSDE insights.
 */

const axios = require('axios');
const { performance } = require('perf_hooks');

class CSEDRemediationIntegration {
  /**
   * Create a new CSDE Remediation Integration
   * @param {Object} options - Integration options
   * @param {Object} options.csdeConnector - CSDE connector instance
   * @param {Object} options.logger - Logger instance
   */
  constructor(options = {}) {
    this.options = {
      autoRemediate: options.autoRemediate !== false,
      remediationThreshold: options.remediationThreshold || 'HIGH',
      maxConcurrentRemediations: options.maxConcurrentRemediations || 5,
      ...options
    };
    
    this.csdeConnector = options.csdeConnector;
    this.logger = options.logger || console;
    
    this.remediationQueue = [];
    this.activeRemediations = new Map();
    this.remediationHistory = [];
    
    this.severityLevels = {
      'LOW': 1,
      'MEDIUM': 2,
      'HIGH': 3,
      'CRITICAL': 4
    };
    
    this.logger.info('CSDE Remediation Integration initialized', {
      autoRemediate: this.options.autoRemediate,
      remediationThreshold: this.options.remediationThreshold,
      maxConcurrentRemediations: this.options.maxConcurrentRemediations
    });
  }
  
  /**
   * Process remediation actions from CSDE result
   * @param {Object} csdeResult - CSDE result
   * @param {Object} originalData - Original data
   * @returns {Array} - Remediation actions
   */
  processRemediationActions(csdeResult, originalData) {
    this.logger.debug('Processing remediation actions');
    
    try {
      // Extract remediation actions from CSDE result
      const remediationActions = csdeResult._remediation || [];
      
      if (remediationActions.length === 0) {
        this.logger.debug('No remediation actions found');
        return [];
      }
      
      this.logger.info(`Found ${remediationActions.length} remediation actions`);
      
      // Process each remediation action
      const processedActions = remediationActions.map(action => {
        // Add original data reference
        const processedAction = {
          ...action,
          originalData,
          status: 'PENDING',
          createdAt: new Date().toISOString()
        };
        
        // Add to remediation queue if auto-remediation is enabled
        if (this.options.autoRemediate && 
            this.shouldAutoRemediate(processedAction)) {
          this.queueRemediation(processedAction);
        }
        
        return processedAction;
      });
      
      return processedActions;
    } catch (error) {
      this.logger.error('Error processing remediation actions', { error: error.message });
      throw error;
    }
  }
  
  /**
   * Check if a remediation action should be auto-remediated
   * @param {Object} action - Remediation action
   * @returns {boolean} - Whether the action should be auto-remediated
   */
  shouldAutoRemediate(action) {
    // Check if action has automation script
    if (!action.automationPossible || !action.automationScript) {
      return false;
    }
    
    // Check severity threshold
    const actionSeverity = this.severityLevels[action.severity] || 0;
    const thresholdSeverity = this.severityLevels[this.options.remediationThreshold] || 0;
    
    return actionSeverity >= thresholdSeverity;
  }
  
  /**
   * Queue a remediation action
   * @param {Object} action - Remediation action
   */
  queueRemediation(action) {
    this.logger.debug('Queuing remediation action', { 
      actionId: action.id,
      title: action.title,
      severity: action.severity
    });
    
    // Add to queue
    this.remediationQueue.push({
      ...action,
      queuedAt: new Date().toISOString()
    });
    
    // Process queue
    this.processRemediationQueue();
  }
  
  /**
   * Process remediation queue
   */
  async processRemediationQueue() {
    this.logger.debug('Processing remediation queue', { 
      queueLength: this.remediationQueue.length,
      activeRemediations: this.activeRemediations.size
    });
    
    // Check if we can process more remediations
    if (this.activeRemediations.size >= this.options.maxConcurrentRemediations) {
      this.logger.debug('Maximum concurrent remediations reached, waiting');
      return;
    }
    
    // Get next action from queue
    const action = this.remediationQueue.shift();
    
    if (!action) {
      this.logger.debug('No more actions in queue');
      return;
    }
    
    // Start remediation
    this.startRemediation(action);
    
    // Process more actions if available
    if (this.remediationQueue.length > 0 && 
        this.activeRemediations.size < this.options.maxConcurrentRemediations) {
      this.processRemediationQueue();
    }
  }
  
  /**
   * Start a remediation action
   * @param {Object} action - Remediation action
   */
  async startRemediation(action) {
    this.logger.info('Starting remediation', { 
      actionId: action.id,
      title: action.title,
      severity: action.severity
    });
    
    // Update action status
    action.status = 'IN_PROGRESS';
    action.startedAt = new Date().toISOString();
    
    // Add to active remediations
    this.activeRemediations.set(action.id, action);
    
    try {
      // Execute remediation
      const result = await this.executeRemediation(action);
      
      // Update action with result
      action.status = 'COMPLETED';
      action.completedAt = new Date().toISOString();
      action.result = result;
      
      this.logger.info('Remediation completed successfully', { 
        actionId: action.id,
        title: action.title
      });
    } catch (error) {
      // Update action with error
      action.status = 'FAILED';
      action.completedAt = new Date().toISOString();
      action.error = error.message;
      
      this.logger.error('Remediation failed', { 
        actionId: action.id,
        title: action.title,
        error: error.message
      });
    } finally {
      // Remove from active remediations
      this.activeRemediations.delete(action.id);
      
      // Add to history
      this.remediationHistory.push(action);
      
      // Process more actions if available
      if (this.remediationQueue.length > 0) {
        this.processRemediationQueue();
      }
    }
    
    return action;
  }
  
  /**
   * Execute a remediation action
   * @param {Object} action - Remediation action
   * @returns {Object} - Remediation result
   */
  async executeRemediation(action) {
    this.logger.debug('Executing remediation', { 
      actionId: action.id,
      title: action.title,
      automationScript: action.automationScript
    });
    
    const startTime = performance.now();
    
    try {
      // Check if we have a remediation service URL
      if (!this.options.remediationServiceUrl) {
        throw new Error('Remediation service URL not configured');
      }
      
      // Call remediation service
      const response = await axios.post(`${this.options.remediationServiceUrl}/execute`, {
        action,
        options: {
          dryRun: this.options.dryRun || false
        }
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.options.remediationServiceToken || ''}`
        },
        timeout: 30000 // 30 seconds
      });
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      return {
        success: true,
        data: response.data,
        duration
      };
    } catch (error) {
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      return {
        success: false,
        error: error.message,
        duration
      };
    }
  }
  
  /**
   * Get remediation queue
   * @returns {Array} - Remediation queue
   */
  getRemediationQueue() {
    return [...this.remediationQueue];
  }
  
  /**
   * Get active remediations
   * @returns {Array} - Active remediations
   */
  getActiveRemediations() {
    return Array.from(this.activeRemediations.values());
  }
  
  /**
   * Get remediation history
   * @returns {Array} - Remediation history
   */
  getRemediationHistory() {
    return [...this.remediationHistory];
  }
  
  /**
   * Clear remediation history
   */
  clearRemediationHistory() {
    this.logger.info('Clearing remediation history');
    this.remediationHistory = [];
  }
  
  /**
   * Get remediation statistics
   * @returns {Object} - Remediation statistics
   */
  getRemediationStatistics() {
    const totalRemediations = this.remediationHistory.length;
    const successfulRemediations = this.remediationHistory.filter(action => action.status === 'COMPLETED').length;
    const failedRemediations = this.remediationHistory.filter(action => action.status === 'FAILED').length;
    
    const severityCounts = this.remediationHistory.reduce((counts, action) => {
      const severity = action.severity || 'UNKNOWN';
      counts[severity] = (counts[severity] || 0) + 1;
      return counts;
    }, {});
    
    const averageDuration = this.remediationHistory.reduce((sum, action) => {
      if (action.result && action.result.duration) {
        return sum + action.result.duration;
      }
      return sum;
    }, 0) / (totalRemediations || 1);
    
    return {
      totalRemediations,
      successfulRemediations,
      failedRemediations,
      successRate: totalRemediations > 0 ? successfulRemediations / totalRemediations : 0,
      severityCounts,
      averageDuration,
      queueLength: this.remediationQueue.length,
      activeRemediations: this.activeRemediations.size
    };
  }
}

module.exports = CSEDRemediationIntegration;
