"""
FastAPI integration for the C-AIaaS Governance Engine.

Exposes quantum risk assessment and governance endpoints with OpenAPI documentation.
"""

from fastapi import FastAP<PERSON>, HTTPException, Depends, status, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.routing import APIRouter
from pydantic import BaseModel, Field, validator
from typing import Dict, Any, Optional, List, Union
from datetime import datetime, timezone
import logging
import uvicorn
import json
import grpc
from google.protobuf import json_format

from ..governance.novashield import (
    NovaShieldClient, 
    RiskLevel, 
    ComplianceLevel,
    QuantumRiskProfile
)

# Import generated gRPC code
# from ..generated import novaconnect_pb2, novaconnect_pb2_grpc

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app and router
app = FastAPI(
    title="C-AIaaS Governance API",
    description="Quantum-enhanced AI Governance API for C-AIaaS platform",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Create API router for version 1
api_router = APIRouter(prefix="/api/v1", tags=["v1"])
app.include_router(api_router)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize NovaShield client
novashield = NovaShieldClient()

# Request/Response Models
class TaskMetadata(BaseModel):
    """Metadata for quantum assessment tasks"""
    priority: str = "normal"
    environment: str = "production"
    tags: List[str] = []
    custom_fields: Dict[str, Any] = {}

class HybridAssessmentRequest(BaseModel):
    """Request model for hybrid quantum-classical assessment"""
    task_type: str = Field(..., description="Type of task being assessed")
    vendor_qscore: float = Field(..., ge=0.0, le=10.0, description="Vendor's current Q-Score (0.0-10.0)")
    compliance_level: ComplianceLevel = Field(
        default=ComplianceLevel.STANDARD,
        description="Required compliance level for the assessment"
    )
    metadata: TaskMetadata = Field(
        default_factory=TaskMetadata,
        description="Additional metadata for the assessment"
    )
    quantum_required: bool = Field(
        default=True,
        description="Whether quantum assessment is required"
    )

    @validator('task_type')
    def validate_task_type(cls, v):
        valid_types = ["security_update", "data_processing", "model_training", "api_call"]
        if v not in valid_types:
            raise ValueError(f"task_type must be one of {valid_types}")
        return v

class HybridAssessmentResponse(BaseModel):
    """Response model for hybrid assessment"""
    task_id: str = Field(..., description="Unique identifier for this assessment")
    risk_level: RiskLevel = Field(..., description="Determined risk level")
    quantum_risk_score: float = Field(..., description="Quantum risk score (0.0-1.0)")
    classical_risk_score: Optional[float] = Field(
        None, 
        description="Classical risk score if quantum assessment failed"
    )
    confidence_interval: Tuple[float, float] = Field(
        ..., 
        description="Confidence interval for the risk assessment"
    )
    recommended_action: str = Field(..., description="Recommended action based on risk")
    assessment_timestamp: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        description="When the assessment was performed"
    )
    quantum_used: bool = Field(
        ...,
        description="Whether quantum assessment was successfully used"
    )
    metadata: Dict[str, Any] = Field(
        default_factory=dict,
        description="Additional metadata about the assessment"
    )

class RiskAssessmentRequest(BaseModel):
    """Request model for quantum risk assessment"""
    task_type: str = Field(..., description="Type of task being assessed")
    vendor_qscore: float = Field(..., ge=0.0, le=10.0, description="Vendor's current Q-Score (0.0-10.0)")
    compliance_level: ComplianceLevel = Field(
        default=ComplianceLevel.STANDARD,
        description="Required compliance level for the assessment"
    )
    metadata: Optional[Dict[str, Any]] = Field(
        default_factory=dict,
        description="Additional metadata for the assessment"
    )

class RiskAssessmentResponse(QuantumRiskProfile):
    """Response model for risk assessment"""
    timestamp: datetime
    request_id: str

class ThresholdRequest(BaseModel):
    """Request model for getting dynamic thresholds"""
    risk_level: RiskLevel
    compliance_level: ComplianceLevel = ComplianceLevel.STANDARD

class ThresholdResponse(BaseModel):
    """Response model for threshold information"""
    threshold: float
    risk_level: RiskLevel
    compliance_level: ComplianceLevel
    last_updated: datetime

# NovaConnect Client
class NovaConnectClient:
    """Client for interacting with NovaConnect gRPC service"""
    
    def __init__(self, host: str = "localhost", port: int = 50051):
        self.channel = grpc.insecure_channel(f"{host}:{port}")
        # self.stub = novaconnect_pb2_grpc.QuantumAssessmentStub(self.channel)
    
    async def assess_quantum(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Send assessment request to NovaConnect"""
        try:
            # Convert task to protocol buffer
            # request = novaconnect_pb2.QuantumAssessmentRequest(
            #     task_type=task['task_type'],
            #     vendor_qscore=task['vendor_qscore'],
            #     compliance_level=task['compliance_level'].value
            # )
            # # Add metadata
            # for key, value in task['metadata'].items():
            #     request.metadata[key] = str(value)
            # 
            # # Make gRPC call
            # response = await asyncio.get_event_loop().run_in_executor(
            #     None,
            #     lambda: self.stub.Assess(request)
            # )
            # 
            # # Convert response to dict
            # return json_format.MessageToDict(
            #     response,
            #     preserving_proto_field_name=True
            # )
            
            # Mock response until gRPC service is implemented
            return {
                "risk_level": "high",
                "quantum_risk_score": 0.78,
                "confidence_interval": [0.72, 0.84],
                "recommended_action": "Review required before proceeding",
                "quantum_used": True
            }
            
        except Exception as e:
            logger.error(f"NovaConnect assessment failed: {str(e)}")
            raise

# Initialize NovaConnect client
novaconnect = NovaConnectClient()

# API Endpoints
@api_router.post("/hybrid-assess", response_model=HybridAssessmentResponse)
async def hybrid_assessment(
    request: Request,
    task: HybridAssessmentRequest,
    request_id: str = "system-generated"
) -> HybridAssessmentResponse:
    """
    Perform hybrid quantum-classical risk assessment.
    
    This endpoint routes the assessment through NovaConnect for quantum processing
    with a fallback to classical assessment if needed.
    """
    logger.info(f"Received hybrid assessment request: {request_id}")
    
    try:
        # Convert task to dict for NovaConnect
        task_dict = task.dict()
        task_dict['compliance_level'] = task.compliance_level.value
        
        # Add request metadata
        task_dict['metadata']['client_ip'] = request.client.host if request.client else "unknown"
        task_dict['metadata']['request_id'] = request_id
        
        # Try quantum assessment if required
        quantum_result = None
        if task.quantum_required:
            try:
                quantum_result = await novaconnect.assess_quantum(task_dict)
                logger.info(f"Quantum assessment successful: {request_id}")
            except Exception as e:
                logger.warning(f"Quantum assessment failed, falling back to classical: {str(e)}")
        
        # Fallback to classical if quantum failed or not required
        if not quantum_result:
            classical_result = await novashield._classical_risk_assessment(
                task_type=task.task_type,
                vendor_qscore=task.vendor_qscore
            )
            quantum_used = False
        else:
            classical_result = None
            quantum_used = True
        
        # Format response
        response_data = {
            'task_id': request_id,
            'quantum_used': quantum_used,
            'metadata': {
                'endpoint': 'hybrid-assess',
                'phase': 1
            }
        }
        
        if quantum_result:
            response_data.update({
                'risk_level': quantum_result['risk_level'],
                'quantum_risk_score': quantum_result['quantum_risk_score'],
                'confidence_interval': quantum_result['confidence_interval'],
                'recommended_action': quantum_result['recommended_action']
            })
        else:
            response_data.update({
                'risk_level': classical_result['risk_level'].value,
                'classical_risk_score': classical_result['quantum_risk_score'],
                'confidence_interval': classical_result['confidence_interval'],
                'recommended_action': classical_result['recommended_action']
            })
        
        return HybridAssessmentResponse(**response_data)
        
    except Exception as e:
        logger.error(f"Hybrid assessment failed: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Assessment failed: {str(e)}"
        )

@api_router.post("/risk/assess", response_model=RiskAssessmentResponse)
async def assess_risk(
    request: RiskAssessmentRequest,
    request_id: str = "system-generated"
) -> RiskAssessmentResponse:
    """
    Perform quantum-enhanced risk assessment for a task.
    
    This endpoint evaluates the risk level of a given task using quantum computing
    enhanced with classical risk factors. It returns a detailed risk profile
    with recommended actions.
    """
    try:
        # Perform quantum risk assessment
        risk_profile = await novashield.assess_quantum_risk(
            task_type=request.task_type,
            vendor_qscore=request.vendor_qscore,
            historical_data=request.metadata.get("history", {})
        )
        
        # Add request metadata to response
        response = RiskAssessmentResponse(
            **risk_profile,
            request_id=request_id,
            timestamp=datetime.utcnow()
        )
        
        logger.info(
            f"Risk assessment completed - Task: {request.task_type}, "
            f"Risk: {risk_profile['risk_level']}, "
            f"Score: {risk_profile['quantum_risk_score']:.2f}"
        )
        
        return response
        
    except Exception as e:
        logger.error(f"Risk assessment failed: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Risk assessment failed: {str(e)}"
        )

@app.get("/api/v1/thresholds/{risk_level}", response_model=ThresholdResponse)
async def get_threshold(
    risk_level: RiskLevel,
    compliance_level: ComplianceLevel = ComplianceLevel.STANDARD
) -> ThresholdResponse:
    """
    Get the current threshold for a specific risk and compliance level.
    
    Returns the dynamic threshold value that is used to determine if a task
    requires additional scrutiny based on its risk profile.
    """
    try:
        # Get the threshold profile
        profile = novashield.default_profiles.get(risk_level)
        if not profile:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"No threshold profile found for risk level: {risk_level}"
            )
        
        # Get the effective threshold
        threshold = profile.get_effective_threshold(compliance_level)
        
        return ThresholdResponse(
            threshold=threshold,
            risk_level=risk_level,
            compliance_level=compliance_level,
            last_updated=profile.last_updated
        )
        
    except Exception as e:
        logger.error(f"Failed to get threshold: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get threshold: {str(e)}"
        )

@app.get("/health")
async def health_check() -> Dict[str, str]:
    """Health check endpoint"""
    return {"status": "healthy", "timestamp": datetime.utcnow().isoformat()}

def start_api(host: str = "0.0.0.0", port: int = 8000):
    """Start the FastAPI server"""
    logger.info(f"Starting C-AIaaS Governance API on {host}:{port}")
    uvicorn.run(
        "src.api.governance_api:app",
        host=host,
        port=port,
        reload=True,
        log_level="info"
    )

if __name__ == "__main__":
    start_api()
