/**
 * Mock Cache Service
 * 
 * This is a mock implementation of the CacheService that uses an in-memory cache
 * instead of Redis for testing purposes.
 */

const NodeCache = require('node-cache');

class MockCacheService {
  constructor() {
    this.cache = new NodeCache({
      stdTTL: 300, // 5 minutes default TTL
      checkperiod: 60 // Check for expired keys every 60 seconds
    });
  }

  /**
   * Get a value from the cache
   * @param {string} key - The cache key
   * @returns {Promise<any>} - The cached value or null if not found
   */
  async get(key) {
    return this.cache.get(key) || null;
  }

  /**
   * Set a value in the cache
   * @param {string} key - The cache key
   * @param {any} value - The value to cache
   * @param {number} ttl - Time to live in seconds
   * @returns {Promise<boolean>} - True if successful
   */
  async set(key, value, ttl = 300) {
    return this.cache.set(key, value, ttl);
  }

  /**
   * Delete a value from the cache
   * @param {string} key - The cache key
   * @returns {Promise<boolean>} - True if successful
   */
  async delete(key) {
    return this.cache.del(key) > 0;
  }

  /**
   * Get all keys matching a pattern
   * @param {string} pattern - The key pattern to match
   * @returns {Promise<string[]>} - Array of matching keys
   */
  async keys(pattern) {
    const allKeys = this.cache.keys();
    const regex = new RegExp(pattern.replace('*', '.*'));
    return allKeys.filter(key => regex.test(key));
  }

  /**
   * Flush all keys with a specific prefix
   * @param {string} prefix - The key prefix to flush
   * @returns {Promise<boolean>} - True if successful
   */
  async flush(prefix) {
    const keys = this.cache.keys();
    const keysToDelete = keys.filter(key => key.startsWith(prefix));
    this.cache.del(keysToDelete);
    return true;
  }

  /**
   * Flush all keys
   * @returns {Promise<boolean>} - True if successful
   */
  async flushAll() {
    this.cache.flushAll();
    return true;
  }
}

// Export a singleton instance
module.exports = new MockCacheService();
