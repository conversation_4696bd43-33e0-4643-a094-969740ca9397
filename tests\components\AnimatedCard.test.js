import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import AnimatedCard from '../../components/AnimatedCard';

// Mock framer-motion
jest.mock('framer-motion', () => {
  return {
    motion: {
      div: ({ children, className, onClick, ...props }) => (
        <div
          className={className}
          onClick={onClick}
          data-testid="motion-div"
          data-animation-props={JSON.stringify(props)}
        >
          {children}
        </div>
      )
    }
  };
});

// Mock animations
jest.mock('../../utils/animations', () => ({
  fadeInUp: jest.fn().mockImplementation((duration, delay) => ({
    initial: { opacity: 0, y: 50 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: duration / 1000, delay: delay / 1000 }
  })),
  scaleIn: jest.fn().mockImplementation((duration, delay) => ({
    initial: { opacity: 0, scale: 0.8 },
    animate: { opacity: 1, scale: 1 },
    transition: { duration: duration / 1000, delay: delay / 1000 }
  }))
}));

describe('AnimatedCard', () => {
  it('renders children correctly', () => {
    render(
      <AnimatedCard>
        <div data-testid="card-content">Card Content</div>
      </AnimatedCard>
    );

    // Check if children are rendered
    expect(screen.getByTestId('card-content')).toBeInTheDocument();
    expect(screen.getByText('Card Content')).toBeInTheDocument();
  });

  it('applies custom className', () => {
    render(
      <AnimatedCard className="custom-class">
        <div>Card Content</div>
      </AnimatedCard>
    );

    // Check if the custom class is applied
    const card = screen.getByTestId('motion-div');
    expect(card).toHaveClass('custom-class');
    expect(card).toHaveClass('bg-secondary');
    expect(card).toHaveClass('rounded-lg');
    expect(card).toHaveClass('overflow-hidden');
  });

  it('uses fadeInUp animation by default', () => {
    render(
      <AnimatedCard>
        <div>Card Content</div>
      </AnimatedCard>
    );

    // Check if fadeInUp animation is applied
    const card = screen.getByTestId('motion-div');
    const animationProps = JSON.parse(card.dataset.animationProps);

    expect(animationProps.initial).toEqual({ opacity: 0, y: 50 });
    expect(animationProps.animate).toEqual({ opacity: 1, y: 0 });
    expect(animationProps.transition).toEqual({ duration: 0.5, delay: 0 });
  });

  it('uses scaleIn animation when specified', () => {
    render(
      <AnimatedCard animation="scaleIn">
        <div>Card Content</div>
      </AnimatedCard>
    );

    // Check if scaleIn animation is applied
    const card = screen.getByTestId('motion-div');
    const animationProps = JSON.parse(card.dataset.animationProps);

    expect(animationProps.initial).toEqual({ opacity: 0, scale: 0.8 });
    expect(animationProps.animate).toEqual({ opacity: 1, scale: 1 });
    expect(animationProps.transition).toEqual({ duration: 0.5, delay: 0 });
  });

  it('applies custom delay', () => {
    render(
      <AnimatedCard delay={200}>
        <div>Card Content</div>
      </AnimatedCard>
    );

    // Check if delay is applied
    const card = screen.getByTestId('motion-div');
    const animationProps = JSON.parse(card.dataset.animationProps);

    expect(animationProps.transition.delay).toBe(0.2);
  });

  it('calls onClick handler when clicked', () => {
    const handleClick = jest.fn();
    render(
      <AnimatedCard onClick={handleClick}>
        <div>Card Content</div>
      </AnimatedCard>
    );

    // Click the card
    fireEvent.click(screen.getByTestId('motion-div'));

    // Check if onClick handler was called
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('applies hover and tap animations', () => {
    render(
      <AnimatedCard>
        <div>Card Content</div>
      </AnimatedCard>
    );

    // Check if whileHover and whileTap props are passed
    const card = screen.getByTestId('motion-div');
    const animationProps = JSON.parse(card.dataset.animationProps);

    expect(animationProps.whileHover).toEqual({
      scale: 1.02,
      boxShadow: '0 10px 30px -15px rgba(0, 0, 0, 0.5)',
      transition: { duration: 0.2 }
    });

    expect(animationProps.whileTap).toEqual({
      scale: 0.98,
      transition: { duration: 0.1 }
    });
  });
});
