"""
Domain analysis module for identifying and analyzing protein domains.
"""
import numpy as np
from typing import List, <PERSON><PERSON>, <PERSON><PERSON>, Optional
from dataclasses import dataclass
import requests
import os

# Fibonacci sequence up to 1000
FIBONACCI_SEQ = [1, 1, 2, 3, 5, 8, 13, 21, 34, 55, 89, 144, 233, 377, 610, 987]

@dataclass
class ProteinDomain:
    """Represents a protein domain."""
    start: int  # 1-based start position
    end: int    # 1-based end position
    name: str   # Domain name/identifier
    score: float = 0.0  # Confidence score (0-1)
    
    @property
    def length(self) -> int:
        """Get domain length."""
        return self.end - self.start + 1
    
    def fibonacci_alignment(self) -> Tuple[int, float]:
        """
        Calculate Fibonacci alignment for this domain.
        
        Returns:
            Tuple of (closest Fibonacci number, alignment score 0-1)
        """
        length = self.length
        if length <= 0:
            return 0, 0.0
            
        # Find closest Fibonacci number
        closest = min(FIBONACCI_SEQ, key=lambda x: abs(x - length))
        
        # Calculate alignment score (1.0 = perfect match)
        diff = abs(length - closest)
        score = 1.0 / (1.0 + diff)
        
        return closest, score


class DomainAnalyzer:
    """Handles domain prediction and analysis."""
    
    def __init__(self, cache_dir: str = "data/domains"):
        """Initialize with cache directory for domain predictions."""
        self.cache_dir = cache_dir
        os.makedirs(cache_dir, exist_ok=True)
    
    def predict_domains(self, sequence: str, method: str = "pfam") -> List[ProteinDomain]:
        """
        Predict protein domains for a given sequence.
        
        Args:
            sequence: Protein sequence
            method: Prediction method ('pfam', 'deepdomain', etc.)
            
        Returns:
            List of predicted domains
        """
        # In a real implementation, this would call a domain prediction service
        # For now, we'll return mock domains
        length = len(sequence)
        if length < 50:
            # Small protein, single domain
            return [ProteinDomain(1, length, "Single_Domain")]
        
        # Split into 2-3 domains for larger proteins
        num_domains = 2 if length < 200 else 3
        domain_length = length // num_domains
        
        domains = []
        for i in range(num_domains):
            start = i * domain_length + 1
            end = (i + 1) * domain_length if i < num_domains - 1 else length
            domains.append(ProteinDomain(start, end, f"Domain_{i+1}"))
        
        return domains
    
    def analyze_domain_fibonacci(self, sequence: str) -> Dict:
        """
        Analyze Fibonacci alignment of domains in a protein.
        
        Args:
            sequence: Protein sequence
            
        Returns:
            Dictionary with domain analysis results
        """
        domains = self.predict_domains(sequence)
        
        results = {
            'domains': [],
            'average_alignment': 0.0,
            'best_aligned_domain': None,
            'worst_aligned_domain': None
        }
        
        if not domains:
            return results
        
        # Analyze each domain
        domain_scores = []
        best_score = -1
        worst_score = 2
        
        for domain in domains:
            closest_fib, score = domain.fibonacci_alignment()
            domain_data = {
                'name': domain.name,
                'start': domain.start,
                'end': domain.end,
                'length': domain.length,
                'closest_fibonacci': closest_fib,
                'alignment_score': score,
                'fibonacci_ratio': domain.length / closest_fib if closest_fib > 0 else 0
            }
            
            results['domains'].append(domain_data)
            domain_scores.append(score)
            
            # Track best/worst aligned domains
            if score > best_score:
                best_score = score
                results['best_aligned_domain'] = domain_data
            if score < worst_score:
                worst_score = score
                results['worst_aligned_domain'] = domain_data
        
        # Calculate average alignment score
        results['average_alignment'] = sum(domain_scores) / len(domain_scores)
        
        # Add overall protein Fibonacci analysis
        protein_length = len(sequence)
        closest_fib = min(FIBONACCI_SEQ, key=lambda x: abs(x - protein_length))
        results['protein_fibonacci'] = {
            'length': protein_length,
            'closest_fibonacci': closest_fib,
            'alignment_score': 1.0 / (1.0 + abs(protein_length - closest_fib)),
            'fibonacci_ratio': protein_length / closest_fib if closest_fib > 0 else 0
        }
        
        return results


def get_fibonacci_alignment_score(length: int) -> float:
    """
    Calculate Fibonacci alignment score for a given length.
    
    Args:
        length: Length to analyze
        
    Returns:
        Alignment score (0-1)
    """
    if length <= 0:
        return 0.0
        
    closest = min(FIBONACCI_SEQ, key=lambda x: abs(x - length))
    diff = abs(length - closest)
    return 1.0 / (1.0 + diff)


def find_fibonacci_domains(sequence: str, min_domain_length: int = 20) -> List[Tuple[int, int]]:
    """
    Find potential Fibonacci-length domains in a sequence.
    
    Args:
        sequence: Protein sequence
        min_domain_length: Minimum domain length to consider
        
    Returns:
        List of (start, end) tuples for potential domains
    """
    length = len(sequence)
    if length < min_domain_length:
        return [(1, length)] if length > 0 else []
    
    # Find all Fibonacci numbers >= min_domain_length and <= sequence length
    valid_fibs = [f for f in FIBONACCI_SEQ if min_domain_length <= f <= length]
    if not valid_fibs:
        return [(1, length)]
    
    # Try to split sequence into Fibonacci-length segments
    domains = []
    pos = 0
    
    while pos < length:
        remaining = length - pos
        
        # Find largest Fibonacci number that fits
        fib = max(f for f in valid_fibs if f <= remaining)
        if fib == 0:  # Shouldn't happen due to checks above
            break
            
        end = pos + fib
        domains.append((pos + 1, end))  # Convert to 1-based
        pos = end
    
    # If we have a small remaining segment, merge it with the last domain
    if pos < length and domains:
        last_start, last_end = domains[-1]
        domains[-1] = (last_start, length)
    
    return domains
