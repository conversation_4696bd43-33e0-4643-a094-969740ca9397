version: '3.8'

# <PERSON>P<PERSON> 90-DAY SIMULATION ENVIRONMENT
# Clean, simplified version for reliable startup
# Author: <PERSON>, NovaFuse Technologies

services:
  # ALPHA Simulation Engine - Main container
  alpha-simulation:
    image: node:18-alpine
    container_name: alpha-90day-simulator
    ports:
      - "8100:8100"  # Main dashboard
      - "8101:8101"  # WebSocket updates
    environment:
      - NODE_ENV=simulation
      - SIMULATION_MODE=90_DAY_BACKTEST_AGGRESSIVE
      - INITIAL_CAPITAL=100000
      - TARGET_RETURN=0.20
      - WIN_RATE_TARGET=0.70
      - CONSCIOUSNESS_THRESHOLD=1800
      - PSI_INFLECTION_THRESHOLD=0.84
      - MIN_TRADE_CONFIDENCE=0.78
      - NEFC_COHERENCE_MIN=0.78
      - MAX_DRAWDOWN_LIMIT=0.05
    volumes:
      - ../nhetx-castl-alpha:/app/alpha:ro
      - ../comphyological-finance-dominance:/app/trading:ro
      - ./simulation-data:/app/data
    working_dir: /app
    command: >
      sh -c "
        echo '🎯 ALPHA 90-DAY SIMULATION STARTING' &&
        echo '==================================================================' &&
        echo '🔮 ALPHA Observer-Class Engine: Trading Simulation Mode' &&
        echo '📊 Target: 20%+ returns, 80%+ win rate over 90 days' &&
        echo '🏢 Preparing for MetaTrader 5 deployment (Account: **********)' &&
        echo '==================================================================' &&
        echo '' &&
        echo '📦 Installing dependencies...' &&
        npm install --no-package-lock express cors ws axios moment lodash &&
        echo '' &&
        echo '🚀 Starting ALPHA simulation...' &&
        node alpha/alpha-90day-simulation.js
      "
    restart: unless-stopped
    networks:
      - alpha-network
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8100"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Performance Dashboard (simplified)
  alpha-dashboard:
    image: node:18-alpine
    container_name: alpha-dashboard
    ports:
      - "8102:8102"
    environment:
      - NODE_ENV=simulation
      - DASHBOARD_MODE=performance_tracking
    volumes:
      - ../nhetx-castl-alpha:/app/alpha:ro
      - ./simulation-data:/app/data
    working_dir: /app
    command: >
      sh -c "
        echo '📊 ALPHA Performance Dashboard Starting...' &&
        npm install --no-package-lock express cors &&
        node alpha/simulation-dashboard-server.js
      "
    depends_on:
      - alpha-simulation
    restart: unless-stopped
    networks:
      - alpha-network

  # Market Data Provider (simplified)
  market-data:
    image: node:18-alpine
    container_name: alpha-market-data
    ports:
      - "8103:8103"
    environment:
      - NODE_ENV=simulation
      - DATA_PROVIDER_MODE=historical
      - SYMBOLS=SPY,QQQ,AAPL,MSFT,GOOGL,TSLA,NVDA
    volumes:
      - ../nhetx-castl-alpha:/app/alpha:ro
      - ./market-cache:/app/cache
    working_dir: /app
    command: >
      sh -c "
        echo '📈 Market Data Provider Starting...' &&
        npm install --no-package-lock axios moment &&
        node alpha/historical-market-data-provider.js
      "
    restart: unless-stopped
    networks:
      - alpha-network

networks:
  alpha-network:
    driver: bridge

volumes:
  alpha-simulation-data:
    driver: local
  alpha-market-cache:
    driver: local

# Quick Start Commands:
#
# Start simulation:
#   docker-compose -f alpha-simulation-clean.yml up -d
#
# View logs:
#   docker-compose -f alpha-simulation-clean.yml logs -f alpha-simulation
#
# Stop simulation:
#   docker-compose -f alpha-simulation-clean.yml down
#
# Access dashboard:
#   http://localhost:8100
