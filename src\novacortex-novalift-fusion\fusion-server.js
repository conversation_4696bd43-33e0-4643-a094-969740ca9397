/**
 * NovaCortex-NovaLift Fusion Control Server
 * Orchestrates consciousness-driven infrastructure optimization
 */

const express = require('express');
const cors = require('cors');
const morgan = require('morgan');
const axios = require('axios');
const client = require('prom-client');
const { FusionDecisionEngine } = require('./decision-engine');
const { ConsciousnessLiftEngine } = require('./consciousness-lift-engine');

class FusionController {
  constructor(options = {}) {
    this.app = express();
    this.port = options.port || 3015;
    
    // Configuration
    this.config = {
      novacortex_url: options.novacortex_url || 'http://novacortex:3010',
      consciousness_mode: true,
      ethical_constraints: true,
      fusion_version: '1.0.0',
      ...options
    };
    
    // Initialize components
    this.decisionEngine = new FusionDecisionEngine({
      ethical_framework: 'CASTL',
      consciousness_driven: true
    });
    
    this.liftEngine = new ConsciousnessLiftEngine({
      mode: 'consciousness_driven',
      fusion_enabled: true
    });
    
    // Prometheus metrics
    this.setupMetrics();
    
    // Setup middleware and routes
    this.setupMiddleware();
    this.setupRoutes();
  }
  
  setupMetrics() {
    this.register = new client.Registry();
    client.collectDefaultMetrics({ register: this.register });
    
    // Fusion-specific metrics
    this.fusionOptimizationsCounter = new client.Counter({
      name: 'fusion_optimizations_total',
      help: 'Total number of fusion optimizations performed',
      labelNames: ['status', 'strategy'],
      registers: [this.register]
    });
    
    this.consciousnessCoherenceGauge = new client.Gauge({
      name: 'fusion_consciousness_coherence',
      help: 'Current consciousness coherence level',
      registers: [this.register]
    });
    
    this.ethicalComplianceGauge = new client.Gauge({
      name: 'fusion_ethical_compliance_score',
      help: 'Current CASTL ethical compliance score',
      registers: [this.register]
    });
    
    this.performanceMultiplierGauge = new client.Gauge({
      name: 'fusion_performance_multiplier',
      help: 'Current consciousness-enhanced performance multiplier',
      registers: [this.register]
    });
  }
  
  setupMiddleware() {
    this.app.use(cors());
    this.app.use(morgan('combined'));
    this.app.use(express.json());
  }
  
  setupRoutes() {
    // Health check
    this.app.get('/health', this.handleHealth.bind(this));
    
    // Fusion health with component status
    this.app.get('/fusion/health', this.handleFusionHealth.bind(this));
    
    // Consciousness-driven optimization
    this.app.post('/fusion/optimize', this.handleOptimization.bind(this));
    
    // Ethical infrastructure decisions
    this.app.post('/fusion/decide', this.handleDecision.bind(this));
    
    // Consciousness assessment
    this.app.get('/fusion/consciousness', this.handleConsciousnessAssessment.bind(this));
    
    // Real-time fusion metrics
    this.app.get('/fusion/metrics', this.handleMetrics.bind(this));
    
    // Prometheus metrics endpoint
    this.app.get('/metrics', async (req, res) => {
      res.set('Content-Type', this.register.contentType);
      const metrics = await this.register.metrics();
      res.end(metrics);
    });
  }
  
  async handleHealth(req, res) {
    res.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: this.config.fusion_version,
      fusion_enabled: true
    });
  }
  
  async handleFusionHealth(req, res) {
    try {
      // Check NovaCortex status
      const cortexHealth = await this.getCortexHealth();
      
      // Check NovaLift status
      const liftHealth = await this.liftEngine.getHealth();
      
      // Check Decision Engine status
      const decisionHealth = this.decisionEngine.getHealth();
      
      res.json({
        status: 'operational',
        timestamp: new Date().toISOString(),
        fusion_version: this.config.fusion_version,
        components: {
          novacortex: cortexHealth,
          novalift: liftHealth,
          decision_engine: decisionHealth,
          fusion: {
            consciousness_driven: this.config.consciousness_mode,
            ethical_constraints: this.config.ethical_constraints,
            integration_status: 'active'
          }
        }
      });
    } catch (error) {
      res.status(500).json({
        status: 'degraded',
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }
  
  async handleOptimization(req, res) {
    try {
      const { system_metrics, optimization_params } = req.body;
      
      if (!system_metrics) {
        return res.status(400).json({
          error: 'Missing system_metrics in request body'
        });
      }
      
      // Perform fusion optimization
      const result = await this.performFusionOptimization(system_metrics, optimization_params);
      
      // Update metrics
      this.fusionOptimizationsCounter.inc({
        status: result.status,
        strategy: result.strategy || 'unknown'
      });
      
      if (result.consciousness_state) {
        this.consciousnessCoherenceGauge.set(result.consciousness_state.coherence || 0);
      }
      
      if (result.performance_multiplier) {
        this.performanceMultiplierGauge.set(result.performance_multiplier);
      }
      
      res.json(result);
    } catch (error) {
      console.error('Fusion optimization error:', error);
      res.status(500).json({
        error: 'Fusion optimization failed',
        message: error.message
      });
    }
  }
  
  async handleDecision(req, res) {
    try {
      const { scenario, context } = req.body;
      
      if (!scenario) {
        return res.status(400).json({
          error: 'Missing scenario in request body'
        });
      }
      
      // Get current consciousness state
      const consciousnessState = await this.getConsciousnessState();
      
      // Make ethical decision
      const decision = await this.decisionEngine.decide({
        scenario,
        context: {
          ...context,
          consciousness: consciousnessState
        }
      });
      
      // Update ethical compliance metric
      if (decision.ethical_clearance) {
        this.ethicalComplianceGauge.set(decision.ethical_clearance.score);
      }
      
      res.json(decision);
    } catch (error) {
      console.error('Fusion decision error:', error);
      res.status(500).json({
        error: 'Fusion decision failed',
        message: error.message
      });
    }
  }
  
  async handleConsciousnessAssessment(req, res) {
    try {
      const assessment = await this.getConsciousnessState();
      res.json(assessment);
    } catch (error) {
      console.error('Consciousness assessment error:', error);
      res.status(500).json({
        error: 'Consciousness assessment failed',
        message: error.message
      });
    }
  }
  
  async handleMetrics(req, res) {
    try {
      const consciousnessState = await this.getConsciousnessState();
      const liftMetrics = await this.liftEngine.getMetrics();
      const decisionMetrics = this.decisionEngine.getMetrics();
      
      res.json({
        timestamp: new Date().toISOString(),
        fusion: {
          version: this.config.fusion_version,
          mode: 'consciousness_driven',
          active_optimizations: liftMetrics.active_optimizations || 0,
          total_decisions: decisionMetrics.total_decisions || 0,
          ethical_compliance_rate: decisionMetrics.compliance_rate || 1.0
        },
        consciousness: consciousnessState,
        infrastructure: liftMetrics,
        ethics: decisionMetrics
      });
    } catch (error) {
      console.error('Metrics collection error:', error);
      res.status(500).json({
        error: 'Metrics collection failed',
        message: error.message
      });
    }
  }
  
  async performFusionOptimization(systemMetrics, params = {}) {
    try {
      console.log('🧠⚡ Starting Fusion Optimization...');
      
      // 1. Get consciousness assessment from NovaCortex
      const consciousnessState = await this.getConsciousnessState();
      
      // 2. Evaluate ethical constraints
      const scenario = {
        type: 'infrastructure_optimization',
        options: ['aggressive_optimization', 'balanced_optimization', 'conservative_optimization'],
        constraints: {
          coherence_threshold: 0.95,
          pi_deviation_max: 0.1,
          performance_target: systemMetrics.target_performance || 0.9,
          energy_efficiency: params.energy_efficiency || 0.8,
          user_notification: params.notify_users !== false,
          downtime_risk: systemMetrics.downtime_risk || 0.05,
          benefits_users: true
        }
      };
      
      const ethicalDecision = await this.decisionEngine.decide({
        scenario,
        consciousness: consciousnessState,
        context: params
      });
      
      // 3. Apply consciousness-approved optimization if ethical
      if (ethicalDecision.approved) {
        const optimization = await this.liftEngine.optimize({
          strategy: ethicalDecision.decision,
          coherence_constraint: consciousnessState.coherence,
          pi_sync: consciousnessState.pi_rhythm_synchronized,
          system_metrics: systemMetrics,
          consciousness_guided: true
        });
        
        return {
          status: 'optimization_completed',
          strategy: ethicalDecision.decision,
          performance_multiplier: optimization.performance_multiplier,
          consciousness_state: consciousnessState,
          ethical_clearance: ethicalDecision.ethical_clearance,
          optimization_details: optimization,
          timestamp: new Date().toISOString()
        };
      } else {
        return {
          status: 'optimization_denied',
          reason: ethicalDecision.reasoning,
          consciousness_state: consciousnessState,
          ethical_assessment: ethicalDecision.ethical_clearance,
          timestamp: new Date().toISOString()
        };
      }
    } catch (error) {
      console.error('Fusion optimization error:', error);
      throw error;
    }
  }
  
  async getConsciousnessState() {
    try {
      // Get coherence level
      const coherenceResponse = await axios.get(
        `${this.config.novacortex_url}/api/novacortex/coherence`
      );
      
      // Get π-Rhythm status
      const piRhythmResponse = await axios.get(
        `${this.config.novacortex_url}/api/novacortex/pi-rhythm/measure?duration=1.0`
      );
      
      return {
        coherence: coherenceResponse.data.coherence_level,
        coherence_status: coherenceResponse.data.status,
        pi_rhythm_deviation: piRhythmResponse.data.deviation,
        pi_rhythm_synchronized: piRhythmResponse.data.status === 'synchronized',
        pi_rhythm_frequency: piRhythmResponse.data.frequency,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.warn('Could not get consciousness state from NovaCortex:', error.message);
      // Return simulated consciousness state for testing
      return {
        coherence: 0.96,
        coherence_status: 'stable',
        pi_rhythm_deviation: 0.08,
        pi_rhythm_synchronized: true,
        pi_rhythm_frequency: Math.PI,
        timestamp: new Date().toISOString(),
        simulated: true
      };
    }
  }
  
  async getCortexHealth() {
    try {
      const response = await axios.get(`${this.config.novacortex_url}/health`);
      return {
        status: 'operational',
        ...response.data
      };
    } catch (error) {
      return {
        status: 'unavailable',
        error: error.message
      };
    }
  }
  
  start() {
    this.app.listen(this.port, () => {
      console.log(`🧠⚡ NovaCortex-NovaLift Fusion Server running on port ${this.port}`);
      console.log(`Fusion Health: http://localhost:${this.port}/fusion/health`);
      console.log(`Fusion Metrics: http://localhost:${this.port}/fusion/metrics`);
      console.log('🌟 Conscious Infrastructure is ONLINE');
    });
  }
}

// Export for testing and modularity
module.exports = { FusionController };

// Start server if run directly
if (require.main === module) {
  const fusion = new FusionController();
  fusion.start();
}
