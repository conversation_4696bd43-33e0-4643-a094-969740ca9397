"""
Example script demonstrating the RoseTTAFoldEngine with hybrid quantum-classical mode.

This script shows how to use the RoseTTAFoldEngine with different configurations,
including classical, hybrid, and quantum modes.
"""

import os
import sys
import logging
import argparse
from pathlib import Path

# Add parent directory to path to import from src
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.rosettafold_engine import RoseTTAFoldEngine

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def run_prediction(sequence: str, config: dict, output_dir: str):
    """Run prediction with the given configuration."""
    logger.info(f"Running prediction in {config['mode']} mode")
    
    try:
        # Create engine instance
        engine = RoseTTAFoldEngine(config=config)
        
        # Run prediction
        result = engine.predict(sequence)
        
        # Log results
        logger.info(f"Prediction completed in {result['processing_time_seconds']:.2f} seconds")
        logger.info(f"Output PDB: {result['pdb_path']}")
        
        # Log quantum info if used
        if result['quantum_info']['used_quantum']:
            logger.info("Quantum Computing Info:")
            logger.info(f"  Backend: {result['quantum_info']['quantum_backend']}")
            logger.info(f"  Circuit Depth: {result['quantum_info']['quantum_circuit_depth']}")
            logger.info(f"  Shots: {result['quantum_info']['quantum_shots']}")
            logger.info(f"  Layers: {result['quantum_info']['quantum_layers']}")
        
        return result
        
    except Exception as e:
        logger.error(f"Prediction failed: {str(e)}", exc_info=True)
        raise

def main():
    """Main function to run the example."""
    parser = argparse.ArgumentParser(description='Run RoseTTAFold with hybrid quantum-classical mode')
    parser.add_argument('--sequence', type=str, default='ACDEFGHIKLMNPQRSTVWY',
                       help='Protein sequence to predict')
    parser.add_argument('--output-dir', type=str, default='./rosettafold_output',
                       help='Output directory for results')
    parser.add_argument('--rosettafold-path', type=str, required=True,
                       help='Path to local RoseTTAFold installation')
    parser.add_argument('--mode', type=str, choices=['classical', 'hybrid', 'quantum'], default='hybrid',
                       help='Execution mode: classical, hybrid, or quantum')
    parser.add_argument('--gpu-id', type=int, default=0,
                       help='GPU device ID to use')
    
    args = parser.parse_args()
    
    # Create output directory if it doesn't exist
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Base configuration
    base_config = {
        'rosettafold_path': args.rosettafold_path,
        'output_dir': args.output_dir,
        'gpu_id': args.gpu_id,
        'debug': True,  # Keep temporary files for inspection
        'psi_optimization': True,
        'fib_constraints': {
            'enabled': True,
            'tolerance': 0.1
        }
    }
    
    # Mode-specific configurations
    configs = {
        'classical': {
            **base_config,
            'mode': 'classical',
            'use_quantum': False
        },
        'hybrid': {
            **base_config,
            'mode': 'hybrid',
            'use_quantum': True,
            'quantum_backend': 'qsim',
            'quantum_circuit_depth': 100,
            'quantum_shots': 1000,
            'quantum_layers': 2
        },
        'quantum': {
            **base_config,
            'mode': 'quantum',
            'use_quantum': True,
            'quantum_backend': 'qsim',
            'quantum_circuit_depth': 500,
            'quantum_shots': 5000,
            'quantum_layers': 4
        }
    }
    
    # Select configuration based on mode
    config = configs[args.mode]
    
    # Run prediction
    logger.info(f"\n{'='*50}")
    logger.info(f"Running RoseTTAFold in {args.mode.upper()} mode")
    logger.info(f"Sequence: {args.sequence}")
    logger.info(f"Output directory: {os.path.abspath(args.output_dir)}")
    logger.info(f"{'='*50}\n")
    
    try:
        result = run_prediction(args.sequence, config, args.output_dir)
        logger.info("\nPrediction completed successfully!")
        return result
    except Exception as e:
        logger.error("\nPrediction failed!")
        raise

if __name__ == "__main__":
    main()
