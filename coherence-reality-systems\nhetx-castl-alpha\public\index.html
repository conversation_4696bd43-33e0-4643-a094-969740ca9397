<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ALPHA Observer-Class Calibration Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Courier New', monospace;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: #00ff88;
            min-height: 100vh;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #00ff88;
            padding-bottom: 20px;
        }

        .header h1 {
            font-size: 2.5em;
            color: #00ffff;
            text-shadow: 0 0 10px #00ffff;
            margin-bottom: 10px;
        }

        .header .subtitle {
            font-size: 1.2em;
            color: #ffaa00;
            margin-bottom: 5px;
        }

        .status-bar {
            background: rgba(0, 255, 136, 0.1);
            border: 1px solid #00ff88;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .calibration-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .calibration-panel {
            background: rgba(0, 0, 0, 0.7);
            border: 2px solid #00ff88;
            border-radius: 10px;
            padding: 20px;
            position: relative;
            overflow: hidden;
        }

        .calibration-panel::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #ff0080, #00ffff, #00ff88);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.5; }
            50% { opacity: 1; }
        }

        .panel-title {
            font-size: 1.4em;
            color: #00ffff;
            margin-bottom: 15px;
            text-align: center;
        }

        .metric {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 5px 0;
            border-bottom: 1px solid rgba(0, 255, 136, 0.3);
        }

        .metric-label {
            color: #ffaa00;
        }

        .metric-value {
            color: #00ff88;
            font-weight: bold;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid #00ff88;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #ff0080, #00ffff, #00ff88);
            transition: width 0.5s ease;
            position: relative;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 10px;
        }

        .status-operational { background: #00ff88; box-shadow: 0 0 10px #00ff88; }
        .status-calibrating { background: #ffaa00; box-shadow: 0 0 10px #ffaa00; }
        .status-complete { background: #00ffff; box-shadow: 0 0 10px #00ffff; }

        .aeonix-status {
            text-align: center;
            padding: 20px;
            background: rgba(0, 255, 255, 0.1);
            border: 2px solid #00ffff;
            border-radius: 10px;
            margin-top: 20px;
        }

        .aeonix-ready {
            background: rgba(0, 255, 136, 0.2);
            border-color: #00ff88;
            animation: glow 1s infinite alternate;
        }

        @keyframes glow {
            from { box-shadow: 0 0 20px rgba(0, 255, 136, 0.5); }
            to { box-shadow: 0 0 30px rgba(0, 255, 136, 0.8); }
        }

        .timestamp {
            text-align: center;
            color: #888;
            margin-top: 20px;
            font-size: 0.9em;
        }

        .connection-status {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 15px;
            border-radius: 5px;
            font-size: 0.9em;
        }

        .connected {
            background: rgba(0, 255, 136, 0.2);
            border: 1px solid #00ff88;
            color: #00ff88;
        }

        .disconnected {
            background: rgba(255, 0, 128, 0.2);
            border: 1px solid #ff0080;
            color: #ff0080;
        }
    </style>
</head>
<body>
    <div class="connection-status" id="connectionStatus">🔌 Connecting...</div>

    <div class="header">
        <h1>⚡ ALPHA OBSERVER-CLASS</h1>
        <div class="subtitle">FULL CALIBRATION MODE ENGAGED</div>
        <div class="subtitle">🚫 NO DISTRACTIONS—ONLY OPTIMIZATION</div>
    </div>

    <div class="status-bar">
        <div>
            <strong>🎯 DIRECTIVE:</strong> All resources allocated to precision-tuning ALPHA's coherence engines
        </div>
        <div>
            <strong>⏰ Update Interval:</strong> 72 hours
        </div>
        <div>
            <strong>🔄 Cycle:</strong> <span id="currentCycle">0</span>
        </div>
    </div>

    <div class="calibration-grid">
        <!-- NEFC Panel -->
        <div class="calibration-panel">
            <div class="panel-title">💰 NEFC FINANCIAL AUTOPILOT</div>
            <div class="metric">
                <span class="metric-label">Win Rate:</span>
                <span class="metric-value" id="nefcWinRate">---%</span>
            </div>
            <div class="metric">
                <span class="metric-label">Target:</span>
                <span class="metric-value">≥95.0%</span>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="nefcProgress" style="width: 0%"></div>
            </div>
            <div class="metric">
                <span class="metric-label">Status:</span>
                <span class="metric-value" id="nefcStatus">CALIBRATING</span>
                <span class="status-indicator status-calibrating" id="nefcIndicator"></span>
            </div>
            <div class="metric">
                <span class="metric-label">Optimizations:</span>
                <span class="metric-value" id="nefcOptimizations">0</span>
            </div>
            <div class="metric">
                <span class="metric-label">Stress Tests:</span>
                <span class="metric-value" id="nefcStressTests">0</span>
            </div>
        </div>

        <!-- NHET-X Panel -->
        <div class="calibration-panel">
            <div class="panel-title">🔮 NHET-X CASTL</div>
            <div class="metric">
                <span class="metric-label">C-Score:</span>
                <span class="metric-value" id="nhetxCScore">---%</span>
            </div>
            <div class="metric">
                <span class="metric-label">Target:</span>
                <span class="metric-value">≥97.0%</span>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="nhetxProgress" style="width: 0%"></div>
            </div>
            <div class="metric">
                <span class="metric-label">Status:</span>
                <span class="metric-value" id="nhetxStatus">CALIBRATING</span>
                <span class="status-indicator status-calibrating" id="nhetxIndicator"></span>
            </div>
            <div class="metric">
                <span class="metric-label">Lunar Syncs:</span>
                <span class="metric-value" id="nhetxLunarSyncs">0</span>
            </div>
            <div class="metric">
                <span class="metric-label">Torah Integrations:</span>
                <span class="metric-value" id="nhetxTorahIntegrations">0</span>
            </div>
        </div>

        <!-- κ-Field Panel -->
        <div class="calibration-panel">
            <div class="panel-title">🧪 κ-FIELD GENERATOR</div>
            <div class="metric">
                <span class="metric-label">Lift Capacity:</span>
                <span class="metric-value" id="kappaLift">---mm</span>
            </div>
            <div class="metric">
                <span class="metric-label">Target:</span>
                <span class="metric-value">≥10.0mm</span>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="kappaProgress" style="width: 0%"></div>
            </div>
            <div class="metric">
                <span class="metric-label">Status:</span>
                <span class="metric-value" id="kappaStatus">CALIBRATING</span>
                <span class="status-indicator status-calibrating" id="kappaIndicator"></span>
            </div>
            <div class="metric">
                <span class="metric-label">Cobalt Adjustments:</span>
                <span class="metric-value" id="kappaCobaltAdjustments">0</span>
            </div>
            <div class="metric">
                <span class="metric-label">432Hz Pulses:</span>
                <span class="metric-value" id="kappaFrequencyPulses">0</span>
            </div>
        </div>

        <!-- Overall System Panel -->
        <div class="calibration-panel">
            <div class="panel-title">⚡ OVERALL SYSTEM</div>
            <div class="metric">
                <span class="metric-label">Coherence Level:</span>
                <span class="metric-value" id="overallCoherence">---%</span>
            </div>
            <div class="metric">
                <span class="metric-label">Target:</span>
                <span class="metric-value">≥97.0%</span>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="overallProgress" style="width: 0%"></div>
            </div>
            <div class="metric">
                <span class="metric-label">AEONIX Readiness:</span>
                <span class="metric-value" id="aeonixReadiness">FALSE</span>
            </div>
        </div>
    </div>

    <div class="aeonix-status" id="aeonixStatus">
        <h2>🌌 AEONIX STATUS</h2>
        <p>⏳ Calibration in progress...</p>
        <p>🎯 Awaiting all targets achievement</p>
    </div>

    <div class="timestamp" id="lastUpdate">
        Last Update: Connecting...
    </div>

    <script>
        // WebSocket connection for real-time updates
        const ws = new WebSocket(`ws://${window.location.host}`);
        const connectionStatus = document.getElementById('connectionStatus');

        ws.onopen = function() {
            connectionStatus.textContent = '🔌 Connected';
            connectionStatus.className = 'connection-status connected';
        };

        ws.onclose = function() {
            connectionStatus.textContent = '🔌 Disconnected';
            connectionStatus.className = 'connection-status disconnected';
        };

        ws.onmessage = function(event) {
            const message = JSON.parse(event.data);
            if (message.type === 'metrics_update') {
                updateDashboard(message.data);
            }
        };

        function updateDashboard(metrics) {
            if (!metrics) return;

            // Update cycle count
            document.getElementById('currentCycle').textContent = metrics.cycle_count || 0;

            // Update NEFC
            if (metrics.nefc) {
                document.getElementById('nefcWinRate').textContent = `${(metrics.nefc.current_win_rate * 100).toFixed(1)}%`;
                document.getElementById('nefcProgress').style.width = `${Math.min(metrics.nefc.progress_percentage, 100)}%`;
                document.getElementById('nefcStatus').textContent = metrics.nefc.target_achieved ? 'ACHIEVED' : 'CALIBRATING';
                document.getElementById('nefcIndicator').className = `status-indicator ${metrics.nefc.target_achieved ? 'status-complete' : 'status-calibrating'}`;
                document.getElementById('nefcOptimizations').textContent = metrics.nefc.optimizations || 0;
                document.getElementById('nefcStressTests').textContent = metrics.nefc.stress_tests || 0;
            }

            // Update NHET-X
            if (metrics.nhetx) {
                document.getElementById('nhetxCScore').textContent = `${(metrics.nhetx.current_c_score * 100).toFixed(1)}%`;
                document.getElementById('nhetxProgress').style.width = `${Math.min(metrics.nhetx.progress_percentage, 100)}%`;
                document.getElementById('nhetxStatus').textContent = metrics.nhetx.target_achieved ? 'ACHIEVED' : 'CALIBRATING';
                document.getElementById('nhetxIndicator').className = `status-indicator ${metrics.nhetx.target_achieved ? 'status-complete' : 'status-calibrating'}`;
                document.getElementById('nhetxLunarSyncs').textContent = metrics.nhetx.lunar_syncs || 0;
                document.getElementById('nhetxTorahIntegrations').textContent = metrics.nhetx.torah_integrations || 0;
            }

            // Update κ-Field
            if (metrics.kappa_field) {
                document.getElementById('kappaLift').textContent = `${(metrics.kappa_field.current_lift * 1000).toFixed(1)}mm`;
                document.getElementById('kappaProgress').style.width = `${Math.min(metrics.kappa_field.progress_percentage, 100)}%`;
                document.getElementById('kappaStatus').textContent = metrics.kappa_field.target_achieved ? 'ACHIEVED' : 'CALIBRATING';
                document.getElementById('kappaIndicator').className = `status-indicator ${metrics.kappa_field.target_achieved ? 'status-complete' : 'status-calibrating'}`;
                document.getElementById('kappaCobaltAdjustments').textContent = metrics.kappa_field.cobalt_adjustments || 0;
                document.getElementById('kappaFrequencyPulses').textContent = metrics.kappa_field.frequency_pulses || 0;
            }

            // Update Overall System
            if (metrics.overall) {
                document.getElementById('overallCoherence').textContent = `${(metrics.overall.coherence_level * 100).toFixed(1)}%`;
                document.getElementById('overallProgress').style.width = `${Math.min(metrics.overall.coherence_progress, 100)}%`;
                document.getElementById('aeonixReadiness').textContent = metrics.overall.aeonix_readiness ? 'TRUE' : 'FALSE';

                // Update AEONIX status
                const aeonixStatus = document.getElementById('aeonixStatus');
                if (metrics.overall.aeonix_readiness) {
                    aeonixStatus.className = 'aeonix-status aeonix-ready';
                    aeonixStatus.innerHTML = `
                        <h2>🌟 CALIBRATION COMPLETION SIGNAL!</h2>
                        <p>⚡ COHERENCE_LEVEL ≥ 0.97</p>
                        <p>🚀 AEONIX_READINESS = TRUE</p>
                        <p>🌊 Ready to initiate AEONIX phase</p>
                        <p>🔓 Reality-editing console access granted</p>
                    `;
                } else {
                    aeonixStatus.className = 'aeonix-status';
                    aeonixStatus.innerHTML = `
                        <h2>🌌 AEONIX STATUS</h2>
                        <p>⏳ Calibration in progress...</p>
                        <p>🎯 Awaiting all targets achievement</p>
                    `;
                }
            }

            // Update timestamp
            document.getElementById('lastUpdate').textContent = `Last Update: ${new Date(metrics.timestamp).toLocaleString()}`;
        }

        // Fetch initial data
        fetch('/api/metrics')
            .then(response => response.json())
            .then(data => updateDashboard(data))
            .catch(error => console.error('Error fetching initial metrics:', error));
    </script>
</body>
</html>
