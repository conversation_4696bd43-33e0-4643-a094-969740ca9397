/**
 * NovaShield - Vendor Management Service
 * 
 * This service provides vendor management capabilities.
 */

const { createLogger } = require('../../utils/logger');
const { v4: uuidv4 } = require('uuid');

const logger = createLogger('vendor-management-service');

// In-memory storage for vendors (would be replaced with a database in production)
const vendors = [
  {
    id: 'vendor-001',
    name: 'Acme Corporation',
    category: 'software',
    criticality: 'high',
    dataAccess: 'sensitive',
    complianceStatus: 'compliant',
    financialStability: 'stable',
    contractStatus: 'active',
    lastReviewDate: '2023-01-15T00:00:00.000Z',
    contacts: [
      {
        name: '<PERSON>',
        email: '<EMAIL>',
        phone: '******-123-4567',
        role: 'Account Manager'
      }
    ],
    services: [
      {
        name: 'Cloud Hosting',
        description: 'Provides cloud hosting services',
        criticality: 'high'
      }
    ],
    createdAt: '2022-06-01T00:00:00.000Z',
    updatedAt: '2023-01-15T00:00:00.000Z'
  },
  {
    id: 'vendor-002',
    name: 'TechSolutions Inc.',
    category: 'hardware',
    criticality: 'medium',
    dataAccess: 'limited',
    complianceStatus: 'partial',
    financialStability: 'stable',
    contractStatus: 'active',
    lastReviewDate: '2023-02-20T00:00:00.000Z',
    contacts: [
      {
        name: 'Jane Smith',
        email: '<EMAIL>',
        phone: '******-987-6543',
        role: 'Technical Support'
      }
    ],
    services: [
      {
        name: 'Hardware Maintenance',
        description: 'Provides hardware maintenance services',
        criticality: 'medium'
      }
    ],
    createdAt: '2022-08-15T00:00:00.000Z',
    updatedAt: '2023-02-20T00:00:00.000Z'
  },
  {
    id: 'vendor-003',
    name: 'SecureData Partners',
    category: 'security',
    criticality: 'high',
    dataAccess: 'extensive',
    complianceStatus: 'compliant',
    financialStability: 'very-stable',
    contractStatus: 'active',
    lastReviewDate: '2023-03-10T00:00:00.000Z',
    contacts: [
      {
        name: 'Robert Johnson',
        email: '<EMAIL>',
        phone: '******-456-7890',
        role: 'Security Consultant'
      }
    ],
    services: [
      {
        name: 'Security Monitoring',
        description: 'Provides 24/7 security monitoring services',
        criticality: 'high'
      }
    ],
    createdAt: '2022-05-05T00:00:00.000Z',
    updatedAt: '2023-03-10T00:00:00.000Z'
  }
];

/**
 * Get all vendors
 * 
 * @param {Object} filters - Optional filters
 * @returns {Promise<Array>} - List of vendors
 */
async function getAllVendors(filters = {}) {
  logger.debug('Getting all vendors', { filters });
  
  let filteredVendors = [...vendors];
  
  // Apply filters if provided
  if (filters.category) {
    filteredVendors = filteredVendors.filter(v => v.category === filters.category);
  }
  
  if (filters.criticality) {
    filteredVendors = filteredVendors.filter(v => v.criticality === filters.criticality);
  }
  
  if (filters.complianceStatus) {
    filteredVendors = filteredVendors.filter(v => v.complianceStatus === filters.complianceStatus);
  }
  
  if (filters.contractStatus) {
    filteredVendors = filteredVendors.filter(v => v.contractStatus === filters.contractStatus);
  }
  
  return filteredVendors;
}

/**
 * Get a vendor by ID
 * 
 * @param {string} vendorId - Vendor ID
 * @returns {Promise<Object>} - Vendor object
 */
async function getVendor(vendorId) {
  logger.debug('Getting vendor', { vendorId });
  
  const vendor = vendors.find(v => v.id === vendorId);
  
  if (!vendor) {
    logger.warn('Vendor not found', { vendorId });
    throw new Error(`Vendor not found with ID: ${vendorId}`);
  }
  
  return vendor;
}

/**
 * Create a new vendor
 * 
 * @param {Object} vendorData - Vendor data
 * @returns {Promise<Object>} - Created vendor
 */
async function createVendor(vendorData) {
  logger.info('Creating vendor', { vendorName: vendorData.name });
  
  // Validate required fields
  if (!vendorData.name) {
    throw new Error('Vendor name is required');
  }
  
  // Create vendor object
  const vendor = {
    id: vendorData.id || `vendor-${uuidv4()}`,
    name: vendorData.name,
    category: vendorData.category || 'other',
    criticality: vendorData.criticality || 'medium',
    dataAccess: vendorData.dataAccess || 'limited',
    complianceStatus: vendorData.complianceStatus || 'unknown',
    financialStability: vendorData.financialStability || 'unknown',
    contractStatus: vendorData.contractStatus || 'pending',
    lastReviewDate: vendorData.lastReviewDate || null,
    contacts: vendorData.contacts || [],
    services: vendorData.services || [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };
  
  // Add to vendors
  vendors.push(vendor);
  
  return vendor;
}

/**
 * Update a vendor
 * 
 * @param {string} vendorId - Vendor ID
 * @param {Object} vendorData - Vendor data to update
 * @returns {Promise<Object>} - Updated vendor
 */
async function updateVendor(vendorId, vendorData) {
  logger.info('Updating vendor', { vendorId });
  
  // Find vendor
  const index = vendors.findIndex(v => v.id === vendorId);
  
  if (index === -1) {
    logger.warn('Vendor not found', { vendorId });
    throw new Error(`Vendor not found with ID: ${vendorId}`);
  }
  
  // Update vendor
  const updatedVendor = {
    ...vendors[index],
    ...vendorData,
    id: vendorId, // Ensure ID doesn't change
    updatedAt: new Date().toISOString()
  };
  
  vendors[index] = updatedVendor;
  
  return updatedVendor;
}

/**
 * Delete a vendor
 * 
 * @param {string} vendorId - Vendor ID
 * @returns {Promise<boolean>} - Whether the vendor was deleted
 */
async function deleteVendor(vendorId) {
  logger.info('Deleting vendor', { vendorId });
  
  // Find vendor
  const index = vendors.findIndex(v => v.id === vendorId);
  
  if (index === -1) {
    logger.warn('Vendor not found', { vendorId });
    throw new Error(`Vendor not found with ID: ${vendorId}`);
  }
  
  // Remove vendor
  vendors.splice(index, 1);
  
  return true;
}

module.exports = {
  getAllVendors,
  getVendor,
  createVendor,
  updateVendor,
  deleteVendor
};
