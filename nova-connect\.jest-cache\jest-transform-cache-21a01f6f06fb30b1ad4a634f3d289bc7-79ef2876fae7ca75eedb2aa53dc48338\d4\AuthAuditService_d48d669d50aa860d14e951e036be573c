8eff25b0135ca0688d6cc39e622ed0a3
/**
 * Authentication Audit Service
 * 
 * This service handles audit logging specifically for authentication events.
 */

const AuditService = require('./AuditService');
const path = require('path');
class AuthAuditService {
  constructor(dataDir = path.join(__dirname, '../data')) {
    this.auditService = new AuditService(dataDir);
    this.resourceType = 'auth';
  }

  /**
   * Log a login attempt
   * 
   * @param {Object} data - Login attempt data
   * @param {string} data.username - Username used for login
   * @param {string} data.ip - IP address of the client
   * @param {string} data.userAgent - User agent of the client
   * @param {boolean} data.success - Whether the login was successful
   * @param {string} data.userId - User ID (if login was successful)
   * @param {string} data.reason - Reason for failure (if login failed)
   * @param {string} data.method - Authentication method (password, oauth2, etc.)
   * @param {Object} data.details - Additional details
   */
  async logLoginAttempt(data) {
    const auditData = {
      userId: data.userId || null,
      action: 'LOGIN',
      resourceType: this.resourceType,
      resourceId: data.username,
      details: {
        method: data.method || 'password',
        success: data.success,
        reason: data.reason || null,
        ...data.details
      },
      ip: data.ip,
      userAgent: data.userAgent,
      status: data.success ? 'success' : 'failure',
      teamId: data.teamId || null,
      environmentId: data.environmentId || null,
      tenantId: data.tenantId || null
    };
    return this.auditService.logEvent(auditData);
  }

  /**
   * Log a logout event
   * 
   * @param {Object} data - Logout data
   * @param {string} data.userId - User ID
   * @param {string} data.username - Username
   * @param {string} data.ip - IP address of the client
   * @param {string} data.userAgent - User agent of the client
   * @param {Object} data.details - Additional details
   */
  async logLogout(data) {
    const auditData = {
      userId: data.userId,
      action: 'LOGOUT',
      resourceType: this.resourceType,
      resourceId: data.username,
      details: data.details || {},
      ip: data.ip,
      userAgent: data.userAgent,
      status: 'success',
      teamId: data.teamId || null,
      environmentId: data.environmentId || null,
      tenantId: data.tenantId || null
    };
    return this.auditService.logEvent(auditData);
  }

  /**
   * Log a registration event
   * 
   * @param {Object} data - Registration data
   * @param {string} data.userId - User ID
   * @param {string} data.username - Username
   * @param {string} data.ip - IP address of the client
   * @param {string} data.userAgent - User agent of the client
   * @param {boolean} data.success - Whether the registration was successful
   * @param {string} data.reason - Reason for failure (if registration failed)
   * @param {Object} data.details - Additional details
   */
  async logRegistration(data) {
    const auditData = {
      userId: data.userId || null,
      action: 'REGISTER',
      resourceType: this.resourceType,
      resourceId: data.username,
      details: {
        success: data.success,
        reason: data.reason || null,
        ...data.details
      },
      ip: data.ip,
      userAgent: data.userAgent,
      status: data.success ? 'success' : 'failure',
      teamId: data.teamId || null,
      environmentId: data.environmentId || null,
      tenantId: data.tenantId || null
    };
    return this.auditService.logEvent(auditData);
  }

  /**
   * Log a password change event
   * 
   * @param {Object} data - Password change data
   * @param {string} data.userId - User ID
   * @param {string} data.username - Username
   * @param {string} data.ip - IP address of the client
   * @param {string} data.userAgent - User agent of the client
   * @param {boolean} data.success - Whether the password change was successful
   * @param {string} data.reason - Reason for failure (if password change failed)
   * @param {Object} data.details - Additional details
   */
  async logPasswordChange(data) {
    const auditData = {
      userId: data.userId,
      action: 'PASSWORD_CHANGE',
      resourceType: this.resourceType,
      resourceId: data.username,
      details: {
        success: data.success,
        reason: data.reason || null,
        ...data.details
      },
      ip: data.ip,
      userAgent: data.userAgent,
      status: data.success ? 'success' : 'failure',
      teamId: data.teamId || null,
      environmentId: data.environmentId || null,
      tenantId: data.tenantId || null
    };
    return this.auditService.logEvent(auditData);
  }

  /**
   * Log a password reset request event
   * 
   * @param {Object} data - Password reset request data
   * @param {string} data.username - Username
   * @param {string} data.ip - IP address of the client
   * @param {string} data.userAgent - User agent of the client
   * @param {boolean} data.success - Whether the password reset request was successful
   * @param {string} data.reason - Reason for failure (if password reset request failed)
   * @param {Object} data.details - Additional details
   */
  async logPasswordResetRequest(data) {
    const auditData = {
      userId: data.userId || null,
      action: 'PASSWORD_RESET_REQUEST',
      resourceType: this.resourceType,
      resourceId: data.username,
      details: {
        success: data.success,
        reason: data.reason || null,
        ...data.details
      },
      ip: data.ip,
      userAgent: data.userAgent,
      status: data.success ? 'success' : 'failure',
      teamId: data.teamId || null,
      environmentId: data.environmentId || null,
      tenantId: data.tenantId || null
    };
    return this.auditService.logEvent(auditData);
  }

  /**
   * Log a password reset event
   * 
   * @param {Object} data - Password reset data
   * @param {string} data.userId - User ID
   * @param {string} data.username - Username
   * @param {string} data.ip - IP address of the client
   * @param {string} data.userAgent - User agent of the client
   * @param {boolean} data.success - Whether the password reset was successful
   * @param {string} data.reason - Reason for failure (if password reset failed)
   * @param {Object} data.details - Additional details
   */
  async logPasswordReset(data) {
    const auditData = {
      userId: data.userId || null,
      action: 'PASSWORD_RESET',
      resourceType: this.resourceType,
      resourceId: data.username,
      details: {
        success: data.success,
        reason: data.reason || null,
        ...data.details
      },
      ip: data.ip,
      userAgent: data.userAgent,
      status: data.success ? 'success' : 'failure',
      teamId: data.teamId || null,
      environmentId: data.environmentId || null,
      tenantId: data.tenantId || null
    };
    return this.auditService.logEvent(auditData);
  }

  /**
   * Log a token refresh event
   * 
   * @param {Object} data - Token refresh data
   * @param {string} data.userId - User ID
   * @param {string} data.username - Username
   * @param {string} data.ip - IP address of the client
   * @param {string} data.userAgent - User agent of the client
   * @param {boolean} data.success - Whether the token refresh was successful
   * @param {string} data.reason - Reason for failure (if token refresh failed)
   * @param {Object} data.details - Additional details
   */
  async logTokenRefresh(data) {
    const auditData = {
      userId: data.userId || null,
      action: 'TOKEN_REFRESH',
      resourceType: this.resourceType,
      resourceId: data.username || data.userId,
      details: {
        success: data.success,
        reason: data.reason || null,
        ...data.details
      },
      ip: data.ip,
      userAgent: data.userAgent,
      status: data.success ? 'success' : 'failure',
      teamId: data.teamId || null,
      environmentId: data.environmentId || null,
      tenantId: data.tenantId || null
    };
    return this.auditService.logEvent(auditData);
  }

  /**
   * Log a two-factor authentication event
   * 
   * @param {Object} data - Two-factor authentication data
   * @param {string} data.userId - User ID
   * @param {string} data.username - Username
   * @param {string} data.ip - IP address of the client
   * @param {string} data.userAgent - User agent of the client
   * @param {string} data.action - Two-factor action (setup, verify, disable)
   * @param {boolean} data.success - Whether the two-factor action was successful
   * @param {string} data.reason - Reason for failure (if two-factor action failed)
   * @param {Object} data.details - Additional details
   */
  async logTwoFactorAuth(data) {
    const auditData = {
      userId: data.userId,
      action: `2FA_${data.action.toUpperCase()}`,
      resourceType: this.resourceType,
      resourceId: data.username,
      details: {
        success: data.success,
        reason: data.reason || null,
        ...data.details
      },
      ip: data.ip,
      userAgent: data.userAgent,
      status: data.success ? 'success' : 'failure',
      teamId: data.teamId || null,
      environmentId: data.environmentId || null,
      tenantId: data.tenantId || null
    };
    return this.auditService.logEvent(auditData);
  }

  /**
   * Get authentication audit logs
   * 
   * @param {Object} filters - Filters to apply
   * @returns {Object} - Filtered audit logs
   */
  async getAuthAuditLogs(filters = {}) {
    // Add resourceType filter for auth events
    const authFilters = {
      ...filters,
      resourceType: this.resourceType
    };
    return this.auditService.getAuditLogs(authFilters);
  }
}
module.exports = AuthAuditService;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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