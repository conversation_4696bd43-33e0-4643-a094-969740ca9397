# Quantum Resilience Testing Suite Runner
# This script runs the Quantum Resilience Testing Suite and generates a comprehensive report

# Function to display colored output
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$ForegroundColor = "White"
    )
    
    $originalColor = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    Write-Output $Message
    $host.UI.RawUI.ForegroundColor = $originalColor
}

# Create necessary directories
Write-ColorOutput "Creating necessary directories..." "Yellow"
$reportsDir = ".\reports"
if (-not (Test-Path $reportsDir)) {
    New-Item -ItemType Directory -Path $reportsDir | Out-Null
}

$testingDir = ".\testing"
if (-not (Test-Path $testingDir)) {
    New-Item -ItemType Directory -Path $testingDir | Out-Null
}

$nepiDir = ".\testing\nepi"
if (-not (Test-Path $nepiDir)) {
    New-Item -ItemType Directory -Path $nepiDir | Out-Null
}

# Display banner
Write-ColorOutput "`n====================================================" "Cyan"
Write-ColorOutput "       QUANTUM RESILIENCE TESTING SUITE" "Cyan"
Write-ColorOutput "====================================================" "Cyan"
Write-ColorOutput "A comprehensive testing suite for quantum resilience," "Cyan"
Write-ColorOutput "focusing on π10³ constant protection, tensor integrity," "Cyan"
Write-ColorOutput "and quantum entanglement tests." "Cyan"
Write-ColorOutput "====================================================`n" "Cyan"

# Run the NEPI tests
Write-ColorOutput "Running NEPI tests with Quantum Resilience Test Suites..." "Green"
try {
    node testing/nepi/run-nepi-tests.js
    $testResult = $LASTEXITCODE
    
    if ($testResult -eq 0) {
        Write-ColorOutput "`nQuantum Resilience tests passed successfully!" "Green"
    } else {
        Write-ColorOutput "`nQuantum Resilience tests failed. Check the test report for details." "Red"
    }
} catch {
    Write-ColorOutput "Error running tests: $_" "Red"
    $testResult = 1
}

# Find the latest test report
Write-ColorOutput "`nLocating test report..." "Yellow"
$latestReport = Get-ChildItem -Path $reportsDir -Filter "nepi-test-report-*.html" | Sort-Object LastWriteTime -Descending | Select-Object -First 1

if ($latestReport) {
    Write-ColorOutput "Latest test report found: $($latestReport.FullName)" "Green"
    
    # Open the report in the default browser
    Write-ColorOutput "`nOpening test report in browser..." "Yellow"
    Start-Process $latestReport.FullName
} else {
    Write-ColorOutput "No test report found in $reportsDir" "Red"
}

# Run the dashboard example
Write-ColorOutput "`nRunning Quantum Resilience Dashboard example..." "Green"
try {
    node examples/quantum_resilience_dashboard_example.js
    $dashboardResult = $LASTEXITCODE
    
    if ($dashboardResult -eq 0) {
        Write-ColorOutput "`nQuantum Resilience Dashboard example completed successfully!" "Green"
    } else {
        Write-ColorOutput "`nQuantum Resilience Dashboard example failed." "Red"
    }
} catch {
    Write-ColorOutput "Error running dashboard example: $_" "Red"
    $dashboardResult = 1
}

# Find the dashboard preview
Write-ColorOutput "`nLocating dashboard preview..." "Yellow"
$dashboardPreview = Get-ChildItem -Path ".\dashboard_results" -Filter "dashboards_preview.html" -ErrorAction SilentlyContinue

if ($dashboardPreview) {
    Write-ColorOutput "Dashboard preview found: $($dashboardPreview.FullName)" "Green"
    
    # Open the dashboard preview in the default browser
    Write-ColorOutput "`nOpening dashboard preview in browser..." "Yellow"
    Start-Process $dashboardPreview.FullName
} else {
    Write-ColorOutput "No dashboard preview found in .\dashboard_results" "Red"
}

# Display summary
Write-ColorOutput "`n====================================================" "Cyan"
Write-ColorOutput "                    SUMMARY" "Cyan"
Write-ColorOutput "====================================================" "Cyan"
Write-ColorOutput "Quantum Resilience Tests: $(if ($testResult -eq 0) { "PASSED" } else { "FAILED" })" "$(if ($testResult -eq 0) { "Green" } else { "Red" })"
Write-ColorOutput "Dashboard Example: $(if ($dashboardResult -eq 0) { "COMPLETED" } else { "FAILED" })" "$(if ($dashboardResult -eq 0) { "Green" } else { "Red" })"
Write-ColorOutput "====================================================`n" "Cyan"

# Exit with appropriate code
exit $testResult
