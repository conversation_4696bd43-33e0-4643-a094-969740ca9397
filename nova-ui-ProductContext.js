/**
 * ProductContext
 * 
 * A React context for managing the current product.
 */

import { createContext, useState, useEffect } from 'react';

// Define the available products
export const PRODUCTS = {
  NOVA_PRIME: 'novaPrime',
  NOVA_CORE: 'novaCore',
  NOVA_SHIELD: 'novaShield',
  NOVA_LEARN: 'novaLearn',
  NOVA_ASSIST_AI: 'novaAssistAI'
};

// Create the context
export const ProductContext = createContext({
  product: null,
  setProduct: () => {},
  isProductActive: () => false
});

/**
 * ProductProvider component
 * @param {object} props - Component props
 * @param {React.ReactNode} props.children - Child components
 * @returns {React.ReactNode} - Provider component
 */
export function ProductProvider({ children }) {
  // Get the initial product from localStorage or URL parameters
  const getInitialProduct = () => {
    // Check URL parameters first
    const urlParams = new URLSearchParams(window.location.search);
    const productParam = urlParams.get('product');
    
    if (productParam && Object.values(PRODUCTS).includes(productParam)) {
      return productParam;
    }
    
    // Check localStorage
    const storedProduct = localStorage.getItem('novaProduct');
    
    if (storedProduct && Object.values(PRODUCTS).includes(storedProduct)) {
      return storedProduct;
    }
    
    // Default to NovaPrime
    return PRODUCTS.NOVA_PRIME;
  };
  
  const [product, setProduct] = useState(null);
  
  // Initialize the product on mount
  useEffect(() => {
    const initialProduct = getInitialProduct();
    setProduct(initialProduct);
    localStorage.setItem('novaProduct', initialProduct);
  }, []);
  
  // Update localStorage when product changes
  useEffect(() => {
    if (product) {
      localStorage.setItem('novaProduct', product);
    }
  }, [product]);
  
  /**
   * Check if a product is active
   * @param {string} productToCheck - The product to check
   * @returns {boolean} - Whether the product is active
   */
  const isProductActive = (productToCheck) => {
    return product === productToCheck;
  };
  
  return (
    <ProductContext.Provider value={{ product, setProduct, isProductActive }}>
      {children}
    </ProductContext.Provider>
  );
}

/**
 * Example usage:
 * 
 * import { ProductProvider, PRODUCTS } from '../contexts/ProductContext';
 * 
 * function App() {
 *   return (
 *     <ProductProvider>
 *       <YourApp />
 *     </ProductProvider>
 *   );
 * }
 * 
 * // In a component:
 * import { useContext } from 'react';
 * import { ProductContext, PRODUCTS } from '../contexts/ProductContext';
 * 
 * function ProductSwitcher() {
 *   const { product, setProduct, isProductActive } = useContext(ProductContext);
 *   
 *   return (
 *     <div>
 *       <button 
 *         onClick={() => setProduct(PRODUCTS.NOVA_PRIME)}
 *         className={isProductActive(PRODUCTS.NOVA_PRIME) ? 'active' : ''}
 *       >
 *         NovaPrime
 *       </button>
 *       <button 
 *         onClick={() => setProduct(PRODUCTS.NOVA_CORE)}
 *         className={isProductActive(PRODUCTS.NOVA_CORE) ? 'active' : ''}
 *       >
 *         NovaCore
 *       </button>
 *       {/* Other product buttons */}
 *     </div>
 *   );
 * }
 */
