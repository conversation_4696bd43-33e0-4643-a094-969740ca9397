.quantum-toggle {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem 1.25rem;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: 1px solid #e2e8f0;
}

.quantum-toggle.quantum-active {
  background: linear-gradient(135deg, #f8f5ff 0%, #f0e6ff 100%);
  border-color: #d9c2ff;
}

.quantum-toggle.disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.toggle-label {
  font-weight: 500;
  color: #4a5568;
  font-size: 0.95rem;
  min-width: 100px;
}

/* Switch styling */
.switch {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 30px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #e2e8f0;
  transition: .4s;
  border-radius: 34px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 22px;
  width: 22px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

input:checked + .slider {
  background-color: #8a2be2;
}

input:focus + .slider {
  box-shadow: 0 0 0 3px rgba(138, 43, 226, 0.3);
}

input:checked + .slider:before {
  transform: translateX(30px);
}

/* Status indicator */
.status-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-left: auto;
  font-size: 0.85rem;
  color: #718096;
}

.status-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #a0aec0;
  transition: all 0.3s ease;
}

.status-dot.quantum {
  background-color: #8a2be2;
  box-shadow: 0 0 0 2px rgba(138, 43, 226, 0.3);
  animation: pulse 2s infinite;
}

.status-dot.classical {
  background-color: #4a5568;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Tooltip for disabled state */
.quantum-toggle.disabled .tooltip {
  visibility: hidden;
  width: 120px;
  background-color: #2d3748;
  color: #fff;
  text-align: center;
  border-radius: 6px;
  padding: 5px;
  position: absolute;
  z-index: 1;
  bottom: 125%;
  left: 50%;
  transform: translateX(-50%);
  opacity: 0;
  transition: opacity 0.3s;
}

.quantum-toggle.disabled:hover .tooltip {
  visibility: visible;
  opacity: 1;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .quantum-toggle {
    flex-direction: column;
    align-items: flex-start;
    padding: 1rem;
  }
  
  .status-indicator {
    margin-left: 0;
    margin-top: 0.5rem;
    width: 100%;
    justify-content: space-between;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .quantum-toggle {
    background: #2d3748;
    border-color: #4a5568;
  }
  
  .quantum-toggle.quantum-active {
    background: linear-gradient(135deg, #2d1b69 0%, #3c1b69 100%);
    border-color: #553c9a;
  }
  
  .toggle-label {
    color: #e2e8f0;
  }
  
  .status-indicator {
    color: #a0aec0;
  }
  
  .slider {
    background-color: #4a5568;
  }
  
  .slider:before {
    background-color: #e2e8f0;
  }
}
