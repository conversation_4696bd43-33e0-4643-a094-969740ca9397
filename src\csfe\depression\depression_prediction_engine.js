/**
 * Depression Prediction Engine for CSFE
 *
 * This module implements the Depression Prediction engine, which focuses specifically
 * on depression prediction for the 2027-2031 timeframe. It applies the same mathematical
 * architecture as CSDE to financial depression prediction.
 */

const { CSFEEngine } = require('../index');
const HistoricalPatterns = require('./historical_patterns');
const TimelineProjector = require('./timeline_projector');
const MitigationStrategies = require('./mitigation_strategies');

class DepressionPredictionEngine {
  /**
   * Create a new Depression Prediction Engine instance
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    this.options = {
      targetTimeframe: { start: 2027, end: 2031 },
      confidenceThreshold: 0.7,
      enableHistoricalComparison: true,
      enableTimelineProjection: true,
      enableMitigationStrategies: true,
      ...options
    };

    // Initialize CSFE Engine
    this.csfeEngine = new CSFEEngine();

    // Initialize components
    this.historicalPatterns = new HistoricalPatterns();
    this.timelineProjector = new TimelineProjector(this.options.targetTimeframe);
    this.mitigationStrategies = new MitigationStrategies();

    console.log('Depression Prediction Engine initialized');
  }

  /**
   * Calculate depression probability
   * @param {Object} marketData - Market data
   * @param {Object} economicData - Economic data
   * @param {Object} sentimentData - Sentiment data
   * @returns {Object} - Depression prediction result
   */
  calculateDepressionProbability(marketData, economicData, sentimentData) {
    console.log('Calculating depression probability');

    try {
      // Calculate CSFE value
      const csfeResult = this.csfeEngine.calculate(marketData, economicData, sentimentData);

      // Extract key components
      const { csfeValue, tensorProduct, fusionResult } = csfeResult;

      // Calculate base depression probability
      const baseProb = this._calculateBaseDepressionProbability(csfeValue, economicData);

      // Calculate timeline probability distribution
      const timelineProb = this._calculateTimelineProbability(csfeValue, economicData);

      // Calculate severity distribution
      const severityDist = this._calculateSeverityDistribution(csfeValue, economicData);

      // Identify key indicators
      const keyIndicators = this._identifyKeyIndicators(marketData, economicData, sentimentData);

      // Compare with historical patterns
      const historicalComparison = this.options.enableHistoricalComparison ?
        this.historicalPatterns.compareWithHistorical(marketData, economicData, sentimentData) : null;

      // Generate timeline projection
      const timelineProjection = this.options.enableTimelineProjection ?
        this.timelineProjector.projectTimeline(csfeValue, baseProb, timelineProb, economicData) : null;

      // Generate mitigation strategies
      const mitigationStrategies = this.options.enableMitigationStrategies ?
        this.mitigationStrategies.generateStrategies(baseProb, keyIndicators, timelineProb) : null;

      return {
        csfeValue,
        depressionProbability: baseProb,
        timelineProbability: timelineProb,
        severityDistribution: severityDist,
        keyIndicators,
        historicalComparison,
        timelineProjection,
        mitigationStrategies,
        targetTimeframe: this.options.targetTimeframe,
        confidence: this._calculateConfidence(csfeValue),
        calculatedAt: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error calculating depression probability:', error);
      throw new Error(`Depression probability calculation failed: ${error.message}`);
    }
  }

  /**
   * Calculate base depression probability
   * @param {Number} csfeValue - CSFE value
   * @param {Object} economicData - Economic data (optional)
   * @returns {Number} - Base depression probability (0-1)
   * @private
   */
  _calculateBaseDepressionProbability(csfeValue, economicData = null) {
    // In a real implementation, this would use sophisticated analysis
    // For now, use a simplified approach

    // Sigmoid function to normalize to 0-1 range
    const sigmoid = x => 1 / (1 + Math.exp(-x));

    // Invert CSFE value (lower CSFE = higher depression probability)
    const invertedValue = 5000 - csfeValue;

    // Normalize to reasonable range for sigmoid
    const normalizedValue = invertedValue / 1000;

    // Base probability from CSFE value
    let baseProb = sigmoid(normalizedValue);

    // Check for pandemic-depression cycle data
    if (economicData && economicData.pandemicDepressionCycle) {
      const pandemicData = economicData.pandemicDepressionCycle;

      // Calculate years since last pandemic
      const currentYear = new Date().getFullYear();
      const yearsSincePandemic = currentYear - pandemicData.lastPandemic;

      // Calculate years until expected depression based on historical cycle
      const yearsUntilDepression = pandemicData.historicalCycleYears - yearsSincePandemic;

      // Calculate pandemic-depression cycle factor
      // Highest probability when approaching the historical cycle year
      const cycleFactor = Math.max(0, 1 - Math.abs(yearsUntilDepression) / 5) * pandemicData.cycleStrength;

      // Adjust base probability with pandemic-depression cycle factor
      // This significantly increases probability when approaching the historical cycle year
      baseProb = Math.max(baseProb, cycleFactor * 0.8);
    }

    return baseProb;
  }

  /**
   * Calculate timeline probability distribution
   * @param {Number} csfeValue - CSFE value
   * @param {Object} economicData - Economic data (optional)
   * @returns {Object} - Timeline probability distribution
   * @private
   */
  _calculateTimelineProbability(csfeValue, economicData = null) {
    // In a real implementation, this would use sophisticated analysis
    // For now, use a simplified approach

    const baseProb = this._calculateBaseDepressionProbability(csfeValue, economicData);
    const { start, end } = this.options.targetTimeframe;
    const years = end - start + 1;

    // Create a distribution centered around the middle of the timeframe
    const distribution = {};
    let peakYear = Math.floor((start + end) / 2);

    // Check for pandemic-depression cycle data
    if (economicData && economicData.pandemicDepressionCycle) {
      const pandemicData = economicData.pandemicDepressionCycle;

      // Calculate expected depression year based on pandemic cycle
      const expectedDepressionYear = pandemicData.lastPandemic + pandemicData.historicalCycleYears;

      // If expected depression year is within our timeframe, adjust peak year
      if (expectedDepressionYear >= start && expectedDepressionYear <= end) {
        peakYear = expectedDepressionYear;
        console.log(`Pandemic-Depression Cycle: Adjusted peak year to ${peakYear} based on ${pandemicData.lastPandemic} pandemic + ${pandemicData.historicalCycleYears} year cycle`);
      }
    }

    for (let year = start; year <= end; year++) {
      // Calculate distance from peak (normalized to 0-1)
      const distanceFromPeak = Math.abs(year - peakYear) / (years / 2);

      // Calculate probability (higher at peak, lower at edges)
      let yearProb = baseProb * (1 - 0.5 * distanceFromPeak);

      // Enhance probability for pandemic cycle if applicable
      if (economicData && economicData.pandemicDepressionCycle) {
        const pandemicData = economicData.pandemicDepressionCycle;
        const expectedDepressionYear = pandemicData.lastPandemic + pandemicData.historicalCycleYears;

        // Significantly boost probability for the expected depression year
        if (year === expectedDepressionYear) {
          yearProb = Math.max(yearProb, 0.7 * pandemicData.cycleStrength);
        }
        // Boost probability for years adjacent to expected depression year
        else if (Math.abs(year - expectedDepressionYear) === 1) {
          yearProb = Math.max(yearProb, 0.5 * pandemicData.cycleStrength);
        }
      }

      distribution[year] = yearProb;
    }

    return distribution;
  }

  /**
   * Calculate severity distribution
   * @param {Number} csfeValue - CSFE value
   * @returns {Object} - Severity distribution
   * @private
   */
  _calculateSeverityDistribution(csfeValue, economicData = null) {
    // In a real implementation, this would use sophisticated analysis
    // For now, use a simplified approach

    const baseProb = this._calculateBaseDepressionProbability(csfeValue, economicData);

    // Define severity levels
    const severityLevels = ['mild', 'moderate', 'severe', 'extreme'];

    // Create a distribution based on base probability
    const distribution = {};

    // Check for pandemic-depression cycle data
    let pandemicCycleFactor = 0;
    if (economicData && economicData.pandemicDepressionCycle) {
      const pandemicData = economicData.pandemicDepressionCycle;

      // Calculate years since last pandemic
      const currentYear = new Date().getFullYear();
      const yearsSincePandemic = currentYear - pandemicData.lastPandemic;

      // Calculate years until expected depression based on historical cycle
      const yearsUntilDepression = pandemicData.historicalCycleYears - yearsSincePandemic;

      // Calculate pandemic-depression cycle factor
      // Higher factor when approaching the historical cycle year
      if (yearsUntilDepression >= 0 && yearsUntilDepression <= 3) {
        pandemicCycleFactor = (1 - yearsUntilDepression / 5) * pandemicData.cycleStrength;
        console.log(`Pandemic-Depression Cycle: ${yearsUntilDepression} years until expected depression, cycle factor: ${pandemicCycleFactor}`);
      }
    }

    // Adjust base probability with pandemic cycle factor
    const adjustedProb = Math.max(baseProb, pandemicCycleFactor * 0.7);

    if (adjustedProb < 0.3) {
      // Low probability - mostly mild
      distribution.mild = 0.7;
      distribution.moderate = 0.2;
      distribution.severe = 0.08;
      distribution.extreme = 0.02;
    } else if (adjustedProb < 0.6) {
      // Medium probability - mostly moderate
      distribution.mild = 0.3;
      distribution.moderate = 0.5;
      distribution.severe = 0.15;
      distribution.extreme = 0.05;
    } else {
      // High probability - more severe
      distribution.mild = 0.1;
      distribution.moderate = 0.3;
      distribution.severe = 0.4;
      distribution.extreme = 0.2;
    }

    // If pandemic cycle is strong, shift distribution toward more severe outcomes
    if (pandemicCycleFactor > 0.5) {
      // Shift distribution toward more severe outcomes
      distribution.mild = Math.max(0.05, distribution.mild * 0.5);
      distribution.moderate = Math.max(0.15, distribution.moderate * 0.7);
      distribution.severe = Math.min(0.5, distribution.severe * 1.5);
      distribution.extreme = Math.min(0.3, distribution.extreme * 2);

      // Normalize to ensure sum is 1
      const sum = distribution.mild + distribution.moderate + distribution.severe + distribution.extreme;
      distribution.mild /= sum;
      distribution.moderate /= sum;
      distribution.severe /= sum;
      distribution.extreme /= sum;
    }

    return distribution;
  }

  /**
   * Identify key indicators for depression
   * @param {Object} marketData - Market data
   * @param {Object} economicData - Economic data
   * @param {Object} sentimentData - Sentiment data
   * @returns {Array} - Key indicators
   * @private
   */
  _identifyKeyIndicators(marketData, economicData, sentimentData) {
    // In a real implementation, this would use sophisticated analysis
    // For now, use a simplified approach

    const indicators = [];

    // Check market indicators
    if (marketData.volatility && marketData.volatility.value > 20) {
      indicators.push({
        name: 'High Market Volatility',
        value: marketData.volatility.value,
        threshold: 20,
        category: 'market',
        impact: 'high'
      });
    }

    if (marketData.liquidity && marketData.liquidity.value < 0.5) {
      indicators.push({
        name: 'Low Market Liquidity',
        value: marketData.liquidity.value,
        threshold: 0.5,
        category: 'market',
        impact: 'high'
      });
    }

    // Check economic indicators
    if (economicData.gdp && economicData.gdp.growth < 1.5) {
      indicators.push({
        name: 'Low GDP Growth',
        value: economicData.gdp.growth,
        threshold: 1.5,
        category: 'economic',
        impact: 'high'
      });
    }

    if (economicData.unemployment && economicData.unemployment.rate > 5) {
      indicators.push({
        name: 'Rising Unemployment',
        value: economicData.unemployment.rate,
        threshold: 5,
        category: 'economic',
        impact: 'high'
      });
    }

    if (economicData.interestRates && economicData.interestRates.fed_funds > 3) {
      indicators.push({
        name: 'High Interest Rates',
        value: economicData.interestRates.fed_funds,
        threshold: 3,
        category: 'economic',
        impact: 'medium'
      });
    }

    // Check for pandemic-depression cycle indicator
    if (economicData.pandemicDepressionCycle) {
      const pandemicData = economicData.pandemicDepressionCycle;
      const currentYear = new Date().getFullYear();
      const yearsSincePandemic = currentYear - pandemicData.lastPandemic;
      const yearsUntilDepression = pandemicData.historicalCycleYears - yearsSincePandemic;

      if (yearsUntilDepression >= 0 && yearsUntilDepression <= 3) {
        indicators.push({
          name: 'Pandemic-Depression Cycle',
          value: pandemicData.cycleStrength,
          threshold: 0.5,
          category: 'historical',
          impact: 'high',
          description: `${yearsSincePandemic} years since ${pandemicData.lastPandemic} pandemic, approaching historical ${pandemicData.historicalCycleYears}-year cycle`
        });
      }
    }

    // Check sentiment indicators
    if (sentimentData.retail && sentimentData.retail.bullishPercentage < 40) {
      indicators.push({
        name: 'Low Retail Sentiment',
        value: sentimentData.retail.bullishPercentage,
        threshold: 40,
        category: 'sentiment',
        impact: 'medium'
      });
    }

    if (sentimentData.institutional && sentimentData.institutional.netPositioning < 0) {
      indicators.push({
        name: 'Negative Institutional Positioning',
        value: sentimentData.institutional.netPositioning,
        threshold: 0,
        category: 'sentiment',
        impact: 'high'
      });
    }

    // Sort by impact
    indicators.sort((a, b) => {
      const impactOrder = { high: 3, medium: 2, low: 1 };
      return impactOrder[b.impact] - impactOrder[a.impact];
    });

    return indicators;
  }

  /**
   * Calculate confidence score
   * @param {Number} csfeValue - CSFE value
   * @returns {Number} - Confidence score (0-1)
   * @private
   */
  _calculateConfidence(csfeValue) {
    // In a real implementation, this would use sophisticated analysis
    // For now, use a simplified approach

    // Higher CSFE values generally mean more data points and higher confidence
    const baseConfidence = Math.min(1, csfeValue / 5000);

    // Adjust based on target timeframe distance
    const currentYear = new Date().getFullYear();
    const yearsToStart = this.options.targetTimeframe.start - currentYear;

    // Confidence decreases with distance to target timeframe
    const timeframeAdjustment = Math.max(0.5, 1 - (yearsToStart / 10));

    return baseConfidence * timeframeAdjustment;
  }
}

module.exports = DepressionPredictionEngine;
