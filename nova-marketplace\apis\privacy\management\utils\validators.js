/**
 * Validators
 *
 * This file contains validation functions for the Privacy Management API.
 */

/**
 * Validate an email address
 * @param {string} email - Email address to validate
 * @returns {boolean} Whether the email is valid
 */
const isValidEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Validate a phone number
 * @param {string} phone - Phone number to validate
 * @returns {boolean} Whether the phone number is valid
 */
const isValidPhone = (phone) => {
  const phoneRegex = /^\+?[0-9]{10,15}$/;
  return phoneRegex.test(phone);
};

/**
 * Validate a date
 * @param {string|Date} date - Date to validate
 * @returns {boolean} Whether the date is valid
 */
const isValidDate = (date) => {
  if (!date) return false;

  const dateObj = new Date(date);
  return !isNaN(dateObj.getTime());
};

/**
 * Validate a date range
 * @param {Object} dateRange - Date range to validate
 * @param {string|Date} dateRange.start - Start date
 * @param {string|Date} dateRange.end - End date
 * @returns {boolean} Whether the date range is valid
 */
const isValidDateRange = (dateRange) => {
  if (!dateRange || !dateRange.start || !dateRange.end) return false;

  const startDate = new Date(dateRange.start);
  const endDate = new Date(dateRange.end);

  return !isNaN(startDate.getTime()) && !isNaN(endDate.getTime()) && startDate <= endDate;
};

/**
 * Validate a URL
 * @param {string} url - URL to validate
 * @returns {boolean} Whether the URL is valid
 */
const isValidUrl = (url) => {
  if (!url || typeof url !== 'string') return false;

  // Check for common invalid URL patterns
  if (url.includes(' ') || url.includes(':/') && !url.includes('://')) {
    return false;
  }

  try {
    const parsedUrl = new URL(url);
    // Ensure the URL has a protocol (http or https)
    // Note: ftp and other protocols are not considered valid for our purposes
    return parsedUrl.protocol === 'http:' || parsedUrl.protocol === 'https:';
  } catch (error) {
    return false;
  }
};

/**
 * Validate an IP address
 * @param {string} ip - IP address to validate
 * @returns {boolean} Whether the IP address is valid
 */
const isValidIp = (ip) => {
  if (!ip || typeof ip !== 'string') return false;

  // IPv4 validation
  if (ip.includes('.')) {
    const ipv4Regex = /^(\d{1,3}\.){3}\d{1,3}$/;
    if (!ipv4Regex.test(ip)) return false;

    // Check each octet is between 0 and 255
    const octets = ip.split('.');
    return octets.every(octet => {
      const num = parseInt(octet, 10);
      return num >= 0 && num <= 255;
    });
  }

  // IPv6 validation (simplified)
  if (ip.includes(':')) {
    const ipv6Regex = /^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;
    return ipv6Regex.test(ip);
  }

  return false;
};

/**
 * Validate a data subject request type
 * @param {string} requestType - Request type to validate
 * @returns {boolean} Whether the request type is valid
 */
const isValidDsrType = (requestType) => {
  const validTypes = ['access', 'rectification', 'erasure', 'restriction', 'portability', 'objection', 'automated_decision'];
  return validTypes.includes(requestType);
};

/**
 * Validate a consent type
 * @param {string} consentType - Consent type to validate
 * @returns {boolean} Whether the consent type is valid
 */
const isValidConsentType = (consentType) => {
  const validTypes = ['marketing', 'analytics', 'profiling', 'third_party', 'research', 'other'];
  return validTypes.includes(consentType);
};

/**
 * Validate a legal basis
 * @param {string} legalBasis - Legal basis to validate
 * @returns {boolean} Whether the legal basis is valid
 */
const isValidLegalBasis = (legalBasis) => {
  const validBases = ['consent', 'contract', 'legal_obligation', 'vital_interests', 'public_interest', 'legitimate_interests'];
  return validBases.includes(legalBasis);
};

/**
 * Validate a data breach severity
 * @param {string} severity - Severity to validate
 * @returns {boolean} Whether the severity is valid
 */
const isValidBreachSeverity = (severity) => {
  const validSeverities = ['low', 'medium', 'high', 'critical'];
  return validSeverities.includes(severity);
};

/**
 * Validate a notification priority
 * @param {string} priority - Priority to validate
 * @returns {boolean} Whether the priority is valid
 */
const isValidNotificationPriority = (priority) => {
  const validPriorities = ['low', 'medium', 'high'];
  return validPriorities.includes(priority);
};

module.exports = {
  isValidEmail,
  isValidPhone,
  isValidDate,
  isValidDateRange,
  isValidUrl,
  isValidIp,
  isValidDsrType,
  isValidConsentType,
  isValidLegalBasis,
  isValidBreachSeverity,
  isValidNotificationPriority
};
