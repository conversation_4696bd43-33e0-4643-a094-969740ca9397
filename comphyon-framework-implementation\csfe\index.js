/**
 * Cyber-Safety Financial Engine (CSFE)
 *
 * This module exports all components of the CSFE system.
 */

const TransactionEntropy = require('./transaction-entropy');
const AttackSurfaceCoherence = require('./attack-surface-coherence');
const MarketStressInfusion = require('./market-stress-infusion');

/**
 * Create a basic CSFE system
 * @param {Object} options - Configuration options
 * @returns {Object} - CSFE system components
 */
function createCSFESystem(options = {}) {
  // Create components
  const transactionEntropy = new TransactionEntropy(options.transactionEntropyOptions);
  const attackSurfaceCoherence = new AttackSurfaceCoherence(options.attackSurfaceCoherenceOptions);
  const marketStressInfusion = new MarketStressInfusion(options.marketStressInfusionOptions);

  return {
    transactionEntropy,
    attackSurfaceCoherence,
    marketStressInfusion
  };
}

/**
 * Create an enhanced CSFE system with integrated components
 * @param {Object} options - Configuration options
 * @returns {Object} - Enhanced CSFE system
 */
function createEnhancedCSFESystem(options = {}) {
  // Create basic system
  const csfeSystem = createCSFESystem(options);

  // Set up event listeners for integration

  // When transaction entropy changes, update market stress
  csfeSystem.transactionEntropy.on('entropy-update', (data) => {
    // High transaction entropy can trigger market stress
    if (data.transactionEntropy > 0.7) {
      csfeSystem.marketStressInfusion.injectStress(
        // In a real implementation, we would target specific nodes
        // For now, just use the first node if available
        Array.from(csfeSystem.marketStressInfusion.state?.marketGraph?.nodes?.keys() || [])[0] || 'node-0',
        data.transactionEntropy * 0.3, // Scale down the stress amount
        {
          type: 'transaction_entropy',
          description: 'Stress from high transaction entropy',
          sourceEntropy: data.transactionEntropy
        }
      );
    }
  });

  // When attack surface coherence changes, update transaction entropy
  csfeSystem.attackSurfaceCoherence.on('coherence-update', (data) => {
    // Low coherence (high vulnerability) can affect transaction patterns
    if (data.coherenceScore < 0.5) {
      // In a real implementation, this would generate specific transaction patterns
      // For now, just log the event
      if (options.enableLogging) {
        console.log(`Enhanced CSFE: Low attack surface coherence (${data.coherenceScore.toFixed(2)}) affecting transaction patterns`);
      }
    }
  });

  // When market stress changes, update attack surface coherence
  csfeSystem.marketStressInfusion.on('stress-update', (data) => {
    // High market stress can create new vulnerabilities
    if (data.marketStress > 0.7) {
      csfeSystem.attackSurfaceCoherence.addVulnerability({
        severity: data.marketStress > 0.9 ? 'critical' : 'high',
        impact: data.marketStress,
        exploitability: data.marketStress * 0.8,
        description: 'Vulnerability from high market stress',
        source: 'market_stress'
      });
    }
  });

  // Add enhanced methods
  const enhancedSystem = {
    ...csfeSystem,

    /**
     * Start all components
     * @returns {boolean} - Success status
     */
    start() {
      const teStarted = csfeSystem.transactionEntropy.start();
      const ascStarted = csfeSystem.attackSurfaceCoherence.start();
      const msiStarted = csfeSystem.marketStressInfusion.start();

      return teStarted && ascStarted && msiStarted;
    },

    /**
     * Stop all components
     * @returns {boolean} - Success status
     */
    stop() {
      const teStopped = csfeSystem.transactionEntropy.stop();
      const ascStopped = csfeSystem.attackSurfaceCoherence.stop();
      const msiStopped = csfeSystem.marketStressInfusion.stop();

      return teStopped && ascStopped && msiStopped;
    },

    /**
     * Get unified state from all components
     * @returns {Object} - Unified state
     */
    getUnifiedState() {
      return {
        transactionEntropy: csfeSystem.transactionEntropy.getState(),
        attackSurfaceCoherence: csfeSystem.attackSurfaceCoherence.getState(),
        marketStressInfusion: csfeSystem.marketStressInfusion.getState(),
        timestamp: Date.now()
      };
    },

    /**
     * Get unified metrics from all components
     * @returns {Object} - Unified metrics
     */
    getUnifiedMetrics() {
      return {
        transactionEntropy: csfeSystem.transactionEntropy.getMetrics(),
        attackSurfaceCoherence: csfeSystem.attackSurfaceCoherence.getMetrics(),
        marketStressInfusion: csfeSystem.marketStressInfusion.getMetrics(),
        timestamp: Date.now()
      };
    },

    /**
     * Calculate financial entropy (Ψₜᶠ)
     * @returns {number} - Financial entropy value
     */
    calculateFinancialEntropy() {
      // Get current values
      const transactionEntropy = csfeSystem.transactionEntropy.getState().transactionEntropy;
      const attackSurfaceCoherence = csfeSystem.attackSurfaceCoherence.getState().coherenceScore;
      const marketStress = csfeSystem.marketStressInfusion.getState().marketStress;

      // Calculate financial entropy using 18/82 principle
      // 18% weight to market stress, 82% weight to the combination of transaction entropy and attack surface coherence
      const financialEntropy = (
        0.18 * marketStress +
        0.82 * ((transactionEntropy + (1 - attackSurfaceCoherence)) / 2)
      );

      return this._clamp(financialEntropy);
    },

    /**
     * Process financial data
     * @param {Object} data - Financial data
     * @returns {Object} - Processing result
     */
    processFinancialData(data) {
      const result = {
        timestamp: Date.now(),
        updates: {}
      };

      // Process transactions
      if (data.transactions) {
        const transactionEntropy = csfeSystem.transactionEntropy.processTransactions(data.transactions);
        result.updates.transactionEntropy = transactionEntropy;
      }

      // Process vulnerabilities
      if (data.vulnerabilities) {
        for (const vuln of data.vulnerabilities) {
          csfeSystem.attackSurfaceCoherence.addVulnerability(vuln);
        }
        result.updates.vulnerabilitiesAdded = data.vulnerabilities.length;
      }

      // Process market events
      if (data.marketEvents) {
        for (const event of data.marketEvents) {
          if (event.nodeId && event.stressAmount) {
            csfeSystem.marketStressInfusion.injectStress(event.nodeId, event.stressAmount, event);
          }
        }
        result.updates.marketEventsProcessed = data.marketEvents.length;
      }

      // Calculate financial entropy
      result.financialEntropy = this.calculateFinancialEntropy();

      return result;
    },

    /**
     * Get financial risk assessment
     * @returns {Object} - Risk assessment
     */
    getFinancialRiskAssessment() {
      const transactionEntropyState = csfeSystem.transactionEntropy.getState();
      const attackSurfaceCoherenceState = csfeSystem.attackSurfaceCoherence.getState();
      const marketStressInfusionState = csfeSystem.marketStressInfusion.getState();

      // Calculate financial entropy
      const financialEntropy = this.calculateFinancialEntropy();

      // Determine risk status
      let riskStatus = 'low';
      if (financialEntropy >= 0.95) {
        riskStatus = 'critical';
      } else if (financialEntropy >= 0.8) {
        riskStatus = 'high';
      } else if (financialEntropy >= 0.6) {
        riskStatus = 'medium';
      }

      return {
        financialEntropy,
        riskStatus,
        transactionEntropy: transactionEntropyState.transactionEntropy,
        attackSurfaceCoherence: attackSurfaceCoherenceState.coherenceScore,
        marketStress: marketStressInfusionState.marketStress,
        anomalies: transactionEntropyState.anomalies ? transactionEntropyState.anomalies.slice(0, 5) : [],
        vulnerabilities: csfeSystem.attackSurfaceCoherence.getVulnerabilities('open').slice(0, 5),
        stressEvents: marketStressInfusionState.stressEvents ? marketStressInfusionState.stressEvents.slice(0, 5) : [],
        timestamp: Date.now()
      };
    },

    /**
     * Clamp value between 0 and 1
     * @param {number} value - Value to clamp
     * @returns {number} - Clamped value
     * @private
     */
    _clamp(value) {
      return Math.max(0, Math.min(1, value));
    }
  };

  return enhancedSystem;
}

module.exports = {
  TransactionEntropy,
  AttackSurfaceCoherence,
  MarketStressInfusion,
  createCSFESystem,
  createEnhancedCSFESystem
};
