<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>NovaVision NovaStore Demo</title>
  
  <!-- NovaVision CSS -->
  <style>
    :root {
      --nova-primary: #6C5CE7;
      --nova-secondary: #00B894;
      --nova-accent: #FF6B6B;
      --nova-background: #F8F9FA;
      --nova-card-bg: #FFFFFF;
      --nova-text: #333333;
      --nova-text-light: #666666;
      --nova-border: #E0E0E0;
      --nova-success: #4CAF50;
      --nova-warning: #FF9800;
      --nova-error: #F44336;
      --nova-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      
      --nova-father-color: #FF6B6B;
      --nova-son-color: #4ECDC4;
      --nova-spirit-color: #45B7D1;
    }
    
    body {
      font-family: '<PERSON><PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 0;
      background-color: var(--nova-background);
      color: var(--nova-text);
    }
    
    .nova-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    
    .nova-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 20px;
      padding-bottom: 20px;
      border-bottom: 1px solid var(--nova-border);
    }
    
    .nova-logo {
      display: flex;
      align-items: center;
    }
    
    .nova-logo-icon {
      width: 40px;
      height: 40px;
      margin-right: 10px;
      background-color: var(--nova-primary);
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: bold;
      font-size: 20px;
    }
    
    .nova-logo-text {
      font-size: 24px;
      font-weight: bold;
      color: var(--nova-primary);
    }
    
    .nova-nav {
      display: flex;
      gap: 20px;
    }
    
    .nova-nav-item {
      text-decoration: none;
      color: var(--nova-text);
      font-weight: 500;
      padding: 8px 12px;
      border-radius: 4px;
      transition: background-color 0.2s;
    }
    
    .nova-nav-item:hover {
      background-color: rgba(108, 92, 231, 0.1);
    }
    
    .nova-nav-item.active {
      color: var(--nova-primary);
      background-color: rgba(108, 92, 231, 0.1);
    }
    
    .nova-card {
      background-color: var(--nova-card-bg);
      border-radius: 8px;
      box-shadow: var(--nova-shadow);
      padding: 20px;
      margin-bottom: 20px;
    }
    
    .nova-card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 20px;
    }
    
    .nova-card-title {
      font-size: 18px;
      font-weight: bold;
      margin: 0;
    }
    
    .nova-button {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      padding: 8px 16px;
      border-radius: 4px;
      font-weight: 500;
      cursor: pointer;
      transition: background-color 0.2s;
      border: none;
    }
    
    .nova-button-primary {
      background-color: var(--nova-primary);
      color: white;
    }
    
    .nova-button-primary:hover {
      background-color: #5D4ED6;
    }
    
    .nova-button-secondary {
      background-color: rgba(108, 92, 231, 0.1);
      color: var(--nova-primary);
    }
    
    .nova-button-secondary:hover {
      background-color: rgba(108, 92, 231, 0.2);
    }
    
    .nova-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 20px;
    }
    
    .nova-component-card {
      background-color: var(--nova-card-bg);
      border-radius: 8px;
      box-shadow: var(--nova-shadow);
      padding: 20px;
      transition: transform 0.2s, box-shadow 0.2s;
    }
    
    .nova-component-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    
    .nova-component-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 10px;
    }
    
    .nova-component-name {
      font-size: 18px;
      font-weight: bold;
      margin: 0;
    }
    
    .nova-badge {
      display: inline-block;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: bold;
      text-transform: uppercase;
    }
    
    .nova-badge-gold {
      background-color: #FFD700;
      color: #333;
    }
    
    .nova-badge-silver {
      background-color: #C0C0C0;
      color: #333;
    }
    
    .nova-badge-bronze {
      background-color: #CD7F32;
      color: white;
    }
    
    .nova-badge-none {
      background-color: #999;
      color: white;
    }
    
    .nova-component-info {
      margin-bottom: 15px;
    }
    
    .nova-component-meta {
      display: flex;
      gap: 10px;
      font-size: 14px;
      color: var(--nova-text-light);
      margin-bottom: 10px;
    }
    
    .nova-component-description {
      font-size: 14px;
      margin-bottom: 10px;
    }
    
    .nova-component-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 5px;
    }
    
    .nova-tag {
      background-color: #F0F0F0;
      color: var(--nova-text-light);
      padding: 2px 6px;
      border-radius: 4px;
      font-size: 12px;
    }
    
    .nova-verification-status {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 15px;
    }
    
    .nova-status-indicator {
      display: flex;
      align-items: center;
      gap: 5px;
      font-weight: 500;
    }
    
    .nova-verification-score {
      width: 60%;
    }
    
    .nova-progress-bar {
      height: 8px;
      background-color: #F0F0F0;
      border-radius: 4px;
      overflow: hidden;
    }
    
    .nova-progress-fill {
      height: 100%;
      border-radius: 4px;
    }
    
    .nova-progress-label {
      font-size: 12px;
      text-align: right;
      margin-top: 2px;
    }
    
    .nova-quality-metrics {
      margin-bottom: 15px;
    }
    
    .nova-section-title {
      font-size: 16px;
      font-weight: bold;
      margin: 0 0 10px 0;
      display: flex;
      align-items: center;
      gap: 5px;
    }
    
    .nova-metrics-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 10px;
    }
    
    .nova-metric {
      margin-bottom: 5px;
    }
    
    .nova-metric-label {
      font-size: 12px;
      margin-bottom: 2px;
    }
    
    .nova-component-actions {
      display: flex;
      gap: 10px;
    }
    
    .nova-footer {
      margin-top: 40px;
      padding-top: 20px;
      border-top: 1px solid var(--nova-border);
      text-align: center;
      color: var(--nova-text-light);
      font-size: 14px;
    }
  </style>
</head>
<body>
  <div class="nova-container">
    <header class="nova-header">
      <div class="nova-logo">
        <div class="nova-logo-icon">N</div>
        <div class="nova-logo-text">NovaStore</div>
      </div>
      
      <nav class="nova-nav">
        <a href="#" class="nova-nav-item active">Home</a>
        <a href="#" class="nova-nav-item">Components</a>
        <a href="#" class="nova-nav-item">Verification</a>
        <a href="#" class="nova-nav-item">Documentation</a>
        <a href="#" class="nova-nav-item">Support</a>
      </nav>
    </header>
    
    <main>
      <div class="nova-card">
        <div class="nova-card-header">
          <h2 class="nova-card-title">NovaStore Marketplace</h2>
          <button class="nova-button nova-button-primary">View Dashboard</button>
        </div>
        
        <p>
          Welcome to the NovaStore Marketplace, powered by the Trinity CSDE with 18/82 principle 
          and Data Quality Framework. Browse verified components, explore quality metrics, and 
          discover the power of adaptive optimization.
        </p>
      </div>
      
      <div class="nova-card">
        <div class="nova-card-header">
          <h2 class="nova-card-title">Featured Components</h2>
          <button class="nova-button nova-button-secondary">View All</button>
        </div>
        
        <div class="nova-grid">
          <!-- Component Card 1 -->
          <div class="nova-component-card">
            <div class="nova-component-header">
              <h3 class="nova-component-name">NovaShield Firewall</h3>
              <span class="nova-badge nova-badge-gold">GOLD</span>
            </div>
            
            <div class="nova-component-info">
              <div class="nova-component-meta">
                <div class="nova-component-version">v1.2.0</div>
                <div class="nova-component-author">NovaFuse</div>
                <div class="nova-component-category">security</div>
              </div>
              
              <div class="nova-component-description">
                Advanced firewall component with adaptive threat detection
              </div>
              
              <div class="nova-component-tags">
                <span class="nova-tag">firewall</span>
                <span class="nova-tag">network</span>
                <span class="nova-tag">protection</span>
              </div>
            </div>
            
            <div class="nova-verification-status">
              <div class="nova-status-indicator" style="color: #4CAF50;">
                <span class="nova-status-icon">✓</span>
                <span class="nova-status-text">VERIFIED</span>
              </div>
              
              <div class="nova-verification-score">
                <div class="nova-progress-bar">
                  <div class="nova-progress-fill" style="width: 95%; background-color: #FFD700;"></div>
                </div>
                <div class="nova-progress-label">95.00%</div>
              </div>
            </div>
            
            <div class="nova-quality-metrics">
              <h4 class="nova-section-title">
                <span>Trinity CSDE Quality Metrics</span>
              </h4>
              
              <div class="nova-metrics-grid">
                <div class="nova-metric">
                  <div class="nova-metric-label">Governance</div>
                  <div class="nova-progress-bar">
                    <div class="nova-progress-fill" style="width: 92%; background-color: #FF6B6B;"></div>
                  </div>
                  <div class="nova-progress-label">92.00%</div>
                </div>
                
                <div class="nova-metric">
                  <div class="nova-metric-label">Detection</div>
                  <div class="nova-progress-bar">
                    <div class="nova-progress-fill" style="width: 90%; background-color: #4ECDC4;"></div>
                  </div>
                  <div class="nova-progress-label">90.00%</div>
                </div>
                
                <div class="nova-metric">
                  <div class="nova-metric-label">Response</div>
                  <div class="nova-progress-bar">
                    <div class="nova-progress-fill" style="width: 85%; background-color: #45B7D1;"></div>
                  </div>
                  <div class="nova-progress-label">85.00%</div>
                </div>
                
                <div class="nova-metric">
                  <div class="nova-metric-label">Overall</div>
                  <div class="nova-progress-bar">
                    <div class="nova-progress-fill" style="width: 90%; background-color: #6C5CE7;"></div>
                  </div>
                  <div class="nova-progress-label">90.00%</div>
                </div>
              </div>
            </div>
            
            <div class="nova-component-actions">
              <button class="nova-button nova-button-primary">
                <span>Download</span>
              </button>
              
              <button class="nova-button nova-button-secondary">
                <span>Details</span>
              </button>
            </div>
          </div>
          
          <!-- Component Card 2 -->
          <div class="nova-component-card">
            <div class="nova-component-header">
              <h3 class="nova-component-name">NovaTrack Asset Manager</h3>
              <span class="nova-badge nova-badge-silver">SILVER</span>
            </div>
            
            <div class="nova-component-info">
              <div class="nova-component-meta">
                <div class="nova-component-version">v2.0.1</div>
                <div class="nova-component-author">NovaFuse</div>
                <div class="nova-component-category">asset-management</div>
              </div>
              
              <div class="nova-component-description">
                Asset tracking and management component
              </div>
              
              <div class="nova-component-tags">
                <span class="nova-tag">asset</span>
                <span class="nova-tag">inventory</span>
                <span class="nova-tag">management</span>
              </div>
            </div>
            
            <div class="nova-verification-status">
              <div class="nova-status-indicator" style="color: #4CAF50;">
                <span class="nova-status-icon">✓</span>
                <span class="nova-status-text">VERIFIED</span>
              </div>
              
              <div class="nova-verification-score">
                <div class="nova-progress-bar">
                  <div class="nova-progress-fill" style="width: 85%; background-color: #C0C0C0;"></div>
                </div>
                <div class="nova-progress-label">85.00%</div>
              </div>
            </div>
            
            <div class="nova-quality-metrics">
              <h4 class="nova-section-title">
                <span>Trinity CSDE Quality Metrics</span>
              </h4>
              
              <div class="nova-metrics-grid">
                <div class="nova-metric">
                  <div class="nova-metric-label">Governance</div>
                  <div class="nova-progress-bar">
                    <div class="nova-progress-fill" style="width: 85%; background-color: #FF6B6B;"></div>
                  </div>
                  <div class="nova-progress-label">85.00%</div>
                </div>
                
                <div class="nova-metric">
                  <div class="nova-metric-label">Detection</div>
                  <div class="nova-progress-bar">
                    <div class="nova-progress-fill" style="width: 80%; background-color: #4ECDC4;"></div>
                  </div>
                  <div class="nova-progress-label">80.00%</div>
                </div>
                
                <div class="nova-metric">
                  <div class="nova-metric-label">Response</div>
                  <div class="nova-progress-bar">
                    <div class="nova-progress-fill" style="width: 75%; background-color: #45B7D1;"></div>
                  </div>
                  <div class="nova-progress-label">75.00%</div>
                </div>
                
                <div class="nova-metric">
                  <div class="nova-metric-label">Overall</div>
                  <div class="nova-progress-bar">
                    <div class="nova-progress-fill" style="width: 80%; background-color: #6C5CE7;"></div>
                  </div>
                  <div class="nova-progress-label">80.00%</div>
                </div>
              </div>
            </div>
            
            <div class="nova-component-actions">
              <button class="nova-button nova-button-primary">
                <span>Download</span>
              </button>
              
              <button class="nova-button nova-button-secondary">
                <span>Details</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </main>
    
    <footer class="nova-footer">
      <p>© 2023 NovaFuse. All rights reserved.</p>
      <p>Powered by Trinity CSDE with 18/82 principle and Data Quality Framework.</p>
    </footer>
  </div>
</body>
</html>
