/**
 * CHAEONIX API HOOK
 * Interface for all CHAEONIX divine intelligence API calls
 * Handles engine analysis, divine simulation, and prophetic seeding
 */

import { useState, useCallback } from 'react';
import axios from 'axios';
import { CHAEONIX_ENDPOINTS } from '../utils/chaeonixConstants';

const API_BASE_URL = process.env.NEXT_PUBLIC_CHAEONIX_API_URL || 'http://localhost:8000';

// Create axios instance with default config
const chaeonixAPI = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000, // 30 second timeout
  headers: {
    'Content-Type': 'application/json',
    'X-Client': 'chaeonix-dashboard',
    'X-Version': '1.0.0'
  }
});

// Request interceptor for logging
chaeonixAPI.interceptors.request.use(
  (config) => {
    console.log(`🚀 CHAEONIX API Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('❌ CHAEONIX API Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
chaeonixAPI.interceptors.response.use(
  (response) => {
    console.log(`✅ CHAEONIX API Response: ${response.status} ${response.config.url}`);
    return response;
  },
  (error) => {
    console.error('❌ CHAEONIX API Response Error:', error.response?.status, error.message);
    return Promise.reject(error);
  }
);

export function useCHAEONIXAPI() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [lastResponse, setLastResponse] = useState(null);

  // Generic API call wrapper
  const apiCall = useCallback(async (endpoint, data = null, method = 'GET') => {
    setIsLoading(true);
    setError(null);
    
    try {
      const config = {
        method,
        url: endpoint,
        ...(data && { data })
      };
      
      const response = await chaeonixAPI(config);
      setLastResponse(response.data);
      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.detail || err.message || 'Unknown API error';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Get CHAEONIX system status
  const getSystemStatus = useCallback(async () => {
    return apiCall(CHAEONIX_ENDPOINTS.STATUS);
  }, [apiCall]);

  // NEPI - Harmonic Analysis
  const analyzeHarmonics = useCallback(async (inputData) => {
    return apiCall(CHAEONIX_ENDPOINTS.HARMONICS, inputData, 'POST');
  }, [apiCall]);

  // NEFC - Predator Analysis
  const analyzePredators = useCallback(async (inputData) => {
    return apiCall(CHAEONIX_ENDPOINTS.PREDATORS, inputData, 'POST');
  }, [apiCall]);

  // NEPE - Prophetic Analysis
  const analyzeProphecy = useCallback(async (inputData) => {
    return apiCall(CHAEONIX_ENDPOINTS.PROPHECY, inputData, 'POST');
  }, [apiCall]);

  // NEEE - Sentiment Analysis
  const analyzeSentiment = useCallback(async (inputData) => {
    return apiCall(CHAEONIX_ENDPOINTS.SENTIMENT, inputData, 'POST');
  }, [apiCall]);

  // NERS - Vulnerability Analysis
  const analyzeVulnerability = useCallback(async (inputData) => {
    return apiCall(CHAEONIX_ENDPOINTS.VULNERABILITY, inputData, 'POST');
  }, [apiCall]);

  // Divine Simulation - Complete CHAEONIX Analysis
  const runDivineSimulation = useCallback(async (simulationParams) => {
    const defaultParams = {
      ticker: 'GME',
      amplification: 1.63,
      include_engines: ['NEPI', 'NEFC', 'NEPE', 'NEEE', 'NERS']
    };
    
    const params = { ...defaultParams, ...simulationParams };
    return apiCall(CHAEONIX_ENDPOINTS.DIVINE_SIMULATION, params, 'POST');
  }, [apiCall]);

  // Batch engine analysis
  const runBatchAnalysis = useCallback(async (analysisRequests) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const promises = analysisRequests.map(async (request) => {
        const { engine, data } = request;
        
        switch (engine) {
          case 'NEPI':
            return { engine, result: await analyzeHarmonics(data) };
          case 'NEFC':
            return { engine, result: await analyzePredators(data) };
          case 'NEPE':
            return { engine, result: await analyzeProphecy(data) };
          case 'NEEE':
            return { engine, result: await analyzeSentiment(data) };
          case 'NERS':
            return { engine, result: await analyzeVulnerability(data) };
          default:
            throw new Error(`Unknown engine: ${engine}`);
        }
      });
      
      const results = await Promise.allSettled(promises);
      
      const successful = results
        .filter(result => result.status === 'fulfilled')
        .map(result => result.value);
      
      const failed = results
        .filter(result => result.status === 'rejected')
        .map(result => result.reason);
      
      if (failed.length > 0) {
        console.warn('⚠️ Some batch analyses failed:', failed);
      }
      
      return {
        successful,
        failed,
        total: analysisRequests.length,
        success_rate: successful.length / analysisRequests.length
      };
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [analyzeHarmonics, analyzePredators, analyzeProphecy, analyzeSentiment, analyzeVulnerability]);

  // CDAIE Strategy Analysis
  const runCDAIEAnalysis = useCallback(async (marketDomain, symbols) => {
    const analysisPromises = symbols.map(async (symbol) => {
      try {
        // Run divine simulation for each symbol
        const simulation = await runDivineSimulation({
          ticker: symbol,
          prophecy_event: `Market analysis for ${symbol}`,
          amplification: 1.618
        });
        
        return {
          symbol,
          domain: marketDomain,
          simulation,
          success: true
        };
      } catch (error) {
        return {
          symbol,
          domain: marketDomain,
          error: error.message,
          success: false
        };
      }
    });
    
    const results = await Promise.allSettled(analysisPromises);
    
    return results.map(result => 
      result.status === 'fulfilled' ? result.value : result.reason
    );
  }, [runDivineSimulation]);

  // Prophetic Event Seeding
  const seedPropheticEvent = useCallback(async (eventData) => {
    const defaultEvent = {
      event: 'Market volatility expected',
      amplification: 1.63,
      impact_scope: 'company'
    };
    
    const event = { ...defaultEvent, ...eventData };
    return apiCall(CHAEONIX_ENDPOINTS.PROPHECY, event, 'POST');
  }, [apiCall]);

  // Tri-Market Coherence Analysis
  const analyzeTriMarketCoherence = useCallback(async () => {
    const stockSymbols = ['GME', 'TSLA', 'NVDA'];
    const cryptoSymbols = ['BTC', 'ETH', 'SOL'];
    const forexSymbols = ['EUR/USD', 'GBP/USD', 'USD/JPY'];
    
    try {
      const [stocksAnalysis, cryptoAnalysis, forexAnalysis] = await Promise.all([
        runCDAIEAnalysis('STOCKS', stockSymbols),
        runCDAIEAnalysis('CRYPTO', cryptoSymbols),
        runCDAIEAnalysis('FOREX', forexSymbols)
      ]);
      
      return {
        STOCKS: stocksAnalysis,
        CRYPTO: cryptoAnalysis,
        FOREX: forexAnalysis,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      throw new Error(`Tri-market analysis failed: ${error.message}`);
    }
  }, [runCDAIEAnalysis]);

  // Get engine status
  const getEngineStatus = useCallback(async () => {
    try {
      const status = await getSystemStatus();
      return {
        kernel_status: status.kernel || 'unknown',
        engine_count: status.engines || 0,
        phi_coupling: status.phi_coupling || 0,
        timestamp: status.timestamp || new Date().toISOString()
      };
    } catch (error) {
      throw new Error(`Engine status check failed: ${error.message}`);
    }
  }, [getSystemStatus]);

  // Fibonacci Analysis Helper
  const analyzeFibonacciLevels = useCallback(async (priceData) => {
    const harmonicsInput = {
      series: priceData.prices || [],
      timeframe: priceData.timeframe || '4h',
      current_price: priceData.current_price
    };
    
    return analyzeHarmonics(harmonicsInput);
  }, [analyzeHarmonics]);

  // Sentiment Cycle Analysis Helper
  const analyzeSentimentCycle = useCallback(async (socialData) => {
    const sentimentInput = {
      reddit_posts: socialData.reddit_posts || 1000,
      sentiment: socialData.sentiment || 'neutral',
      meme_intensity: socialData.meme_intensity || 0.5,
      social_metrics: socialData.social_metrics || {}
    };
    
    return analyzeSentiment(sentimentInput);
  }, [analyzeSentiment]);

  // Predator Detection Helper
  const detectPredators = useCallback(async (marketData) => {
    const predatorInput = {
      ticker: marketData.ticker,
      short_interest: marketData.short_interest || true,
      options_flow: marketData.options_flow || {},
      volume_data: marketData.volume_data || []
    };
    
    return analyzePredators(predatorInput);
  }, [analyzePredators]);

  return {
    // Loading state
    isLoading,
    error,
    lastResponse,
    
    // System status
    getSystemStatus,
    getEngineStatus,
    
    // Individual engine analysis
    analyzeHarmonics,
    analyzePredators,
    analyzeProphecy,
    analyzeSentiment,
    analyzeVulnerability,
    
    // Complex analysis
    runDivineSimulation,
    runBatchAnalysis,
    runCDAIEAnalysis,
    analyzeTriMarketCoherence,
    
    // Prophetic capabilities
    seedPropheticEvent,
    
    // Helper functions
    analyzeFibonacciLevels,
    analyzeSentimentCycle,
    detectPredators,
    
    // Raw API call
    apiCall
  };
}

// Hook for specific engine analysis
export function useCHAEONIXEngine(engineCode) {
  const { 
    analyzeHarmonics,
    analyzePredators,
    analyzeProphecy,
    analyzeSentiment,
    analyzeVulnerability,
    isLoading,
    error
  } = useCHAEONIXAPI();

  const analyzeEngine = useCallback(async (inputData) => {
    switch (engineCode) {
      case 'NEPI':
        return analyzeHarmonics(inputData);
      case 'NEFC':
        return analyzePredators(inputData);
      case 'NEPE':
        return analyzeProphecy(inputData);
      case 'NEEE':
        return analyzeSentiment(inputData);
      case 'NERS':
        return analyzeVulnerability(inputData);
      default:
        throw new Error(`Unknown engine code: ${engineCode}`);
    }
  }, [engineCode, analyzeHarmonics, analyzePredators, analyzeProphecy, analyzeSentiment, analyzeVulnerability]);

  return {
    analyzeEngine,
    isLoading,
    error
  };
}

export default useCHAEONIXAPI;
