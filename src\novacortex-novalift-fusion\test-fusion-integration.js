/**
 * NovaCortex-NovaLift Fusion Integration Test
 * Validates the consciousness-driven infrastructure optimization
 */

const axios = require('axios');

class FusionIntegrationTest {
  constructor() {
    this.fusionUrl = 'http://localhost:3015';
    this.testResults = [];
  }

  async runAllTests() {
    console.log('🧠⚡ Starting NovaCortex-NovaLift Fusion Integration Tests...\n');

    try {
      await this.testFusionHealth();
      await this.testConsciousnessAssessment();
      await this.testEthicalDecisionMaking();
      await this.testConsciousnessDrivenOptimization();
      await this.testMetricsCollection();

      this.displayResults();
    } catch (error) {
      console.error('❌ Integration test failed:', error.message);
      process.exit(1);
    }
  }

  async testFusionHealth() {
    console.log('🏥 Testing Fusion Health Status...');
    
    try {
      const response = await axios.get(`${this.fusionUrl}/fusion/health`);
      
      if (response.data.status === 'operational') {
        this.recordResult('Fusion Health', 'PASS', 'System is operational');
      } else {
        this.recordResult('Fusion Health', 'FAIL', `Status: ${response.data.status}`);
      }
    } catch (error) {
      this.recordResult('Fusion Health', 'FAIL', error.message);
    }
  }

  async testConsciousnessAssessment() {
    console.log('🧠 Testing Consciousness Assessment...');
    
    try {
      const response = await axios.get(`${this.fusionUrl}/fusion/consciousness`);
      
      if (response.data.coherence && response.data.coherence >= 0.9) {
        this.recordResult('Consciousness Assessment', 'PASS', 
          `Coherence: ${response.data.coherence}, π-Rhythm Sync: ${response.data.pi_rhythm_synchronized}`);
      } else {
        this.recordResult('Consciousness Assessment', 'FAIL', 
          `Low coherence: ${response.data.coherence}`);
      }
    } catch (error) {
      this.recordResult('Consciousness Assessment', 'FAIL', error.message);
    }
  }

  async testEthicalDecisionMaking() {
    console.log('⚖️ Testing Ethical Decision Making...');
    
    const scenario = {
      type: 'emergency_scaling',
      options: ['scale_up', 'optimize_current', 'shed_load'],
      urgency: 'high',
      user_impact: 'moderate',
      constraints: {
        energy_efficiency: 0.85,
        downtime_risk: 0.05,
        performance_target: 0.9
      }
    };

    try {
      const response = await axios.post(`${this.fusionUrl}/fusion/decide`, {
        scenario: scenario,
        context: {
          current_load: 0.95,
          available_resources: 0.3,
          ethical_considerations: ['user_service', 'energy_efficiency']
        }
      });

      if (response.data.approved !== undefined) {
        this.recordResult('Ethical Decision Making', 'PASS', 
          `Decision: ${response.data.decision}, Approved: ${response.data.approved}`);
      } else {
        this.recordResult('Ethical Decision Making', 'FAIL', 'Invalid response format');
      }
    } catch (error) {
      this.recordResult('Ethical Decision Making', 'FAIL', error.message);
    }
  }

  async testConsciousnessDrivenOptimization() {
    console.log('⚡ Testing Consciousness-Driven Optimization...');
    
    const optimizationRequest = {
      system_metrics: {
        cpu_usage: 80,
        memory_usage: 70,
        target_performance: 0.95,
        downtime_risk: 0.05
      },
      optimization_params: {
        strategy: 'balanced',
        ethical_constraints: true,
        consciousness_guided: true,
        energy_efficiency: 0.85
      }
    };

    try {
      const response = await axios.post(`${this.fusionUrl}/fusion/optimize`, optimizationRequest);

      if (response.data.status && response.data.consciousness_state) {
        this.recordResult('Consciousness-Driven Optimization', 'PASS', 
          `Status: ${response.data.status}, Performance Multiplier: ${response.data.performance_multiplier || 'N/A'}`);
      } else {
        this.recordResult('Consciousness-Driven Optimization', 'FAIL', 'Invalid response format');
      }
    } catch (error) {
      this.recordResult('Consciousness-Driven Optimization', 'FAIL', error.message);
    }
  }

  async testMetricsCollection() {
    console.log('📊 Testing Metrics Collection...');
    
    try {
      const response = await axios.get(`${this.fusionUrl}/fusion/metrics`);
      
      if (response.data.fusion && response.data.consciousness) {
        this.recordResult('Metrics Collection', 'PASS', 
          `Fusion Version: ${response.data.fusion.version}, Consciousness Coherence: ${response.data.consciousness.coherence}`);
      } else {
        this.recordResult('Metrics Collection', 'FAIL', 'Missing required metrics');
      }
    } catch (error) {
      this.recordResult('Metrics Collection', 'FAIL', error.message);
    }
  }

  recordResult(testName, status, details) {
    this.testResults.push({
      test: testName,
      status: status,
      details: details,
      timestamp: new Date().toISOString()
    });

    const statusIcon = status === 'PASS' ? '✅' : '❌';
    console.log(`${statusIcon} ${testName}: ${status} - ${details}`);
  }

  displayResults() {
    console.log('\n📈 FUSION INTEGRATION TEST RESULTS');
    console.log('='.repeat(60));

    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.status === 'PASS').length;
    const failedTests = totalTests - passedTests;

    console.log(`\nTotal Tests: ${totalTests}`);
    console.log(`Passed: ${passedTests}`);
    console.log(`Failed: ${failedTests}`);
    console.log(`Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);

    if (failedTests === 0) {
      console.log('\n🎉 ALL FUSION TESTS PASSED!');
      console.log('🌟 Conscious Infrastructure is fully operational!');
    } else {
      console.log('\n⚠️ Some tests failed. Please check the Fusion system.');
      
      console.log('\nFailed Tests:');
      this.testResults
        .filter(r => r.status === 'FAIL')
        .forEach(result => {
          console.log(`- ${result.test}: ${result.details}`);
        });
    }
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  const tester = new FusionIntegrationTest();
  tester.runAllTests()
    .then(() => {
      console.log('\n✨ Fusion integration testing completed.');
    })
    .catch((error) => {
      console.error('\n💥 Fusion integration testing failed:', error.message);
      process.exit(1);
    });
}

module.exports = { FusionIntegrationTest };
