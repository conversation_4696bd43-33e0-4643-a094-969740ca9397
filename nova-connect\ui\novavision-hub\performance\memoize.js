/**
 * Memoization Utilities
 * 
 * This module provides utilities for memoizing expensive calculations.
 */

/**
 * Create a key from arguments
 * 
 * @param {Array} args - Arguments
 * @returns {string} Key
 */
const createKey = (args) => {
  try {
    return JSON.stringify(args);
  } catch (error) {
    // If arguments can't be stringified, use a fallback
    return args.map(arg => {
      if (arg === null) return 'null';
      if (arg === undefined) return 'undefined';
      if (typeof arg === 'function') return 'function';
      if (typeof arg === 'object') return `object:${Object.keys(arg).join(',')}`;
      return String(arg);
    }).join('|');
  }
};

/**
 * Memoize a function
 * 
 * @param {Function} fn - Function to memoize
 * @param {Object} [options] - Options
 * @param {number} [options.maxSize=100] - Maximum cache size
 * @param {number} [options.maxAge=60000] - Maximum cache age in milliseconds (default: 1 minute)
 * @returns {Function} Memoized function
 */
export const memoize = (fn, { maxSize = 100, maxAge = 60000 } = {}) => {
  const cache = new Map();
  const timestamps = new Map();
  
  // Return memoized function
  return function(...args) {
    const key = createKey(args);
    const now = Date.now();
    
    // Check if result is in cache and not expired
    if (cache.has(key)) {
      const timestamp = timestamps.get(key);
      
      // If not expired, return cached result
      if (now - timestamp < maxAge) {
        // Update timestamp to extend TTL
        timestamps.set(key, now);
        return cache.get(key);
      }
      
      // If expired, remove from cache
      cache.delete(key);
      timestamps.delete(key);
    }
    
    // Calculate result
    const result = fn.apply(this, args);
    
    // If result is a promise, handle it
    if (result instanceof Promise) {
      return result.then(value => {
        // Store in cache
        cache.set(key, value);
        timestamps.set(key, now);
        
        // Prune cache if it exceeds max size
        if (cache.size > maxSize) {
          pruneCache(cache, timestamps);
        }
        
        return value;
      });
    }
    
    // Store in cache
    cache.set(key, result);
    timestamps.set(key, now);
    
    // Prune cache if it exceeds max size
    if (cache.size > maxSize) {
      pruneCache(cache, timestamps);
    }
    
    return result;
  };
};

/**
 * Prune cache
 * 
 * @param {Map} cache - Cache
 * @param {Map} timestamps - Timestamps
 */
const pruneCache = (cache, timestamps) => {
  // Get oldest entries
  const entries = Array.from(timestamps.entries());
  entries.sort((a, b) => a[1] - b[1]);
  
  // Remove oldest entries (25% of max size)
  const removeCount = Math.ceil(cache.size * 0.25);
  for (let i = 0; i < removeCount; i++) {
    if (entries[i]) {
      const [key] = entries[i];
      cache.delete(key);
      timestamps.delete(key);
    }
  }
};

/**
 * Memoize a function with a TTL (Time To Live)
 * 
 * @param {Function} fn - Function to memoize
 * @param {number} ttl - Time to live in milliseconds
 * @returns {Function} Memoized function
 */
export const memoizeWithTTL = (fn, ttl) => {
  return memoize(fn, { maxAge: ttl });
};

/**
 * Memoize a function with a LRU (Least Recently Used) cache
 * 
 * @param {Function} fn - Function to memoize
 * @param {number} maxSize - Maximum cache size
 * @returns {Function} Memoized function
 */
export const memoizeWithLRU = (fn, maxSize) => {
  return memoize(fn, { maxSize });
};

/**
 * Create a memoized selector
 * 
 * @param {Array<Function>} inputSelectors - Input selectors
 * @param {Function} resultFn - Result function
 * @param {Object} [options] - Options
 * @returns {Function} Memoized selector
 */
export const createSelector = (inputSelectors, resultFn, options) => {
  // Memoize result function
  const memoizedResultFn = memoize(resultFn, options);
  
  // Return selector
  return function(...args) {
    // Get inputs
    const inputs = inputSelectors.map(selector => selector(...args));
    
    // Return result
    return memoizedResultFn(...inputs);
  };
};

/**
 * Memoize a component
 * 
 * @param {Function} component - Component to memoize
 * @param {Function} [propsAreEqual] - Function to check if props are equal
 * @returns {Function} Memoized component
 */
export const memoizeComponent = (component, propsAreEqual) => {
  // Cache for last props and result
  let lastProps = null;
  let lastResult = null;
  
  // Return memoized component
  return function(props) {
    // If first render or props changed, render component
    if (
      lastProps === null ||
      (propsAreEqual ? !propsAreEqual(lastProps, props) : !shallowEqual(lastProps, props))
    ) {
      lastProps = { ...props };
      lastResult = component(props);
    }
    
    return lastResult;
  };
};

/**
 * Shallow equal
 * 
 * @param {Object} objA - Object A
 * @param {Object} objB - Object B
 * @returns {boolean} Whether objects are shallow equal
 */
const shallowEqual = (objA, objB) => {
  if (objA === objB) {
    return true;
  }
  
  if (
    typeof objA !== 'object' ||
    objA === null ||
    typeof objB !== 'object' ||
    objB === null
  ) {
    return false;
  }
  
  const keysA = Object.keys(objA);
  const keysB = Object.keys(objB);
  
  if (keysA.length !== keysB.length) {
    return false;
  }
  
  for (let i = 0; i < keysA.length; i++) {
    const key = keysA[i];
    
    if (
      !Object.prototype.hasOwnProperty.call(objB, key) ||
      objA[key] !== objB[key]
    ) {
      return false;
    }
  }
  
  return true;
};
