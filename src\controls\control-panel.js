/**
 * Control Panel
 * 
 * This module provides a control panel for interacting with the system components.
 */

const EventEmitter = require('events');
const { WebSocketClient } = require('../websocket');

/**
 * ControlPanel class
 */
class ControlPanel extends EventEmitter {
  /**
   * Create a new ControlPanel instance
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      enableLogging: options.enableLogging !== undefined ? options.enableLogging : true,
      wsUrl: options.wsUrl || 'ws://localhost:3001/ws',
      autoConnect: options.autoConnect !== undefined ? options.autoConnect : true,
      ...options
    };
    
    // Initialize state
    this.state = {
      isConnected: false,
      activeControls: new Map(), // controlId -> control
      controlValues: new Map(), // controlId -> value
      controlGroups: new Map(), // groupId -> Set of controlIds
      lastUpdate: Date.now()
    };
    
    // Create WebSocket client
    this.client = new WebSocketClient({
      url: this.options.wsUrl,
      clientId: `control-panel-${Date.now()}`,
      enableLogging: this.options.enableLogging,
      autoReconnect: true
    });
    
    // Set up event handlers
    this.client.on('connected', this._handleConnected.bind(this));
    this.client.on('disconnected', this._handleDisconnected.bind(this));
    this.client.on('error', this._handleError.bind(this));
    this.client.on('channel-message', this._handleChannelMessage.bind(this));
    
    // Connect if autoConnect is true
    if (this.options.autoConnect) {
      this.connect().catch((error) => {
        if (this.options.enableLogging) {
          console.error('ControlPanel: Error connecting:', error);
        }
      });
    }
    
    if (this.options.enableLogging) {
      console.log('ControlPanel initialized');
    }
  }
  
  /**
   * Connect to the WebSocket server
   * @returns {Promise<void>} - Promise that resolves when connected
   */
  async connect() {
    if (this.state.isConnected) {
      if (this.options.enableLogging) {
        console.log('ControlPanel: Already connected');
      }
      return;
    }
    
    // Connect WebSocket client
    await this.client.connect();
    
    // Subscribe to control channels
    await this.client.subscribe('control-updates');
    await this.client.subscribe('control-actions');
    await this.client.subscribe('control-events');
    
    // Update state
    this.state.isConnected = true;
    this.state.lastUpdate = Date.now();
    
    if (this.options.enableLogging) {
      console.log('ControlPanel: Connected');
    }
    
    // Emit event
    this.emit('connected');
  }
  
  /**
   * Disconnect from the WebSocket server
   * @returns {Promise<void>} - Promise that resolves when disconnected
   */
  async disconnect() {
    if (!this.state.isConnected) {
      if (this.options.enableLogging) {
        console.log('ControlPanel: Not connected');
      }
      return;
    }
    
    // Disconnect WebSocket client
    await this.client.disconnect();
    
    // Update state
    this.state.isConnected = false;
    this.state.lastUpdate = Date.now();
    
    if (this.options.enableLogging) {
      console.log('ControlPanel: Disconnected');
    }
    
    // Emit event
    this.emit('disconnected');
  }
  
  /**
   * Handle WebSocket connected event
   * @private
   */
  _handleConnected() {
    // Update state
    this.state.isConnected = true;
    this.state.lastUpdate = Date.now();
    
    // Emit event
    this.emit('connected');
    
    if (this.options.enableLogging) {
      console.log('ControlPanel: WebSocket connected');
    }
  }
  
  /**
   * Handle WebSocket disconnected event
   * @private
   */
  _handleDisconnected() {
    // Update state
    this.state.isConnected = false;
    this.state.lastUpdate = Date.now();
    
    // Emit event
    this.emit('disconnected');
    
    if (this.options.enableLogging) {
      console.log('ControlPanel: WebSocket disconnected');
    }
  }
  
  /**
   * Handle WebSocket error event
   * @param {Error} error - Error object
   * @private
   */
  _handleError(error) {
    // Emit event
    this.emit('error', error);
    
    if (this.options.enableLogging) {
      console.error('ControlPanel: WebSocket error:', error);
    }
  }
  
  /**
   * Handle WebSocket channel message event
   * @param {Object} data - Message data
   * @private
   */
  _handleChannelMessage(data) {
    const { channel, data: messageData } = data;
    
    // Handle message based on channel
    switch (channel) {
      case 'control-updates':
        this._handleControlUpdates(messageData);
        break;
        
      case 'control-actions':
        this._handleControlActions(messageData);
        break;
        
      case 'control-events':
        this._handleControlEvents(messageData);
        break;
    }
  }
  
  /**
   * Handle control updates
   * @param {Object} data - Update data
   * @private
   */
  _handleControlUpdates(data) {
    if (!data || !data.controlId) {
      return;
    }
    
    // Update control value
    this.state.controlValues.set(data.controlId, data.value);
    this.state.lastUpdate = Date.now();
    
    // Emit event
    this.emit('control-updated', {
      controlId: data.controlId,
      value: data.value,
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log(`ControlPanel: Control updated - ${data.controlId}: ${JSON.stringify(data.value)}`);
    }
  }
  
  /**
   * Handle control actions
   * @param {Object} data - Action data
   * @private
   */
  _handleControlActions(data) {
    if (!data || !data.action) {
      return;
    }
    
    // Emit event
    this.emit('action', {
      action: data.action,
      params: data.params,
      timestamp: Date.now()
    });
    
    // Emit action-specific event
    this.emit(`action:${data.action}`, {
      params: data.params,
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log(`ControlPanel: Action received - ${data.action}`);
    }
  }
  
  /**
   * Handle control events
   * @param {Object} data - Event data
   * @private
   */
  _handleControlEvents(data) {
    if (!data || !data.event) {
      return;
    }
    
    // Emit event
    this.emit('control-event', {
      event: data.event,
      data: data.data,
      timestamp: Date.now()
    });
    
    // Emit event-specific event
    this.emit(`event:${data.event}`, {
      data: data.data,
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log(`ControlPanel: Event received - ${data.event}`);
    }
  }
  
  /**
   * Register a control
   * @param {string} controlId - Control ID
   * @param {Object} control - Control configuration
   * @param {string} [groupId] - Group ID
   * @returns {boolean} - Success status
   */
  registerControl(controlId, control, groupId) {
    // Validate parameters
    if (!controlId || !control) {
      throw new Error('Control ID and control configuration are required');
    }
    
    // Check if control already exists
    if (this.state.activeControls.has(controlId)) {
      if (this.options.enableLogging) {
        console.log(`ControlPanel: Control already exists - ${controlId}`);
      }
      return false;
    }
    
    // Register control
    this.state.activeControls.set(controlId, control);
    
    // Set initial value
    if (control.defaultValue !== undefined) {
      this.state.controlValues.set(controlId, control.defaultValue);
    }
    
    // Add to group if provided
    if (groupId) {
      if (!this.state.controlGroups.has(groupId)) {
        this.state.controlGroups.set(groupId, new Set());
      }
      
      this.state.controlGroups.get(groupId).add(controlId);
    }
    
    // Update state
    this.state.lastUpdate = Date.now();
    
    // Emit event
    this.emit('control-registered', {
      controlId,
      control,
      groupId,
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log(`ControlPanel: Control registered - ${controlId}`);
    }
    
    return true;
  }
  
  /**
   * Unregister a control
   * @param {string} controlId - Control ID
   * @returns {boolean} - Success status
   */
  unregisterControl(controlId) {
    // Check if control exists
    if (!this.state.activeControls.has(controlId)) {
      if (this.options.enableLogging) {
        console.log(`ControlPanel: Control does not exist - ${controlId}`);
      }
      return false;
    }
    
    // Remove control
    this.state.activeControls.delete(controlId);
    this.state.controlValues.delete(controlId);
    
    // Remove from groups
    for (const [groupId, controls] of this.state.controlGroups.entries()) {
      if (controls.has(controlId)) {
        controls.delete(controlId);
        
        // Remove group if empty
        if (controls.size === 0) {
          this.state.controlGroups.delete(groupId);
        }
      }
    }
    
    // Update state
    this.state.lastUpdate = Date.now();
    
    // Emit event
    this.emit('control-unregistered', {
      controlId,
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log(`ControlPanel: Control unregistered - ${controlId}`);
    }
    
    return true;
  }
  
  /**
   * Get a control
   * @param {string} controlId - Control ID
   * @returns {Object|null} - Control configuration or null if not found
   */
  getControl(controlId) {
    return this.state.activeControls.get(controlId) || null;
  }
  
  /**
   * Get all controls
   * @param {string} [groupId] - Group ID to filter by
   * @returns {Object} - Map of control IDs to control configurations
   */
  getControls(groupId) {
    const controls = {};
    
    if (groupId) {
      // Get controls in group
      const groupControls = this.state.controlGroups.get(groupId);
      
      if (groupControls) {
        for (const controlId of groupControls) {
          const control = this.state.activeControls.get(controlId);
          
          if (control) {
            controls[controlId] = control;
          }
        }
      }
    } else {
      // Get all controls
      for (const [controlId, control] of this.state.activeControls.entries()) {
        controls[controlId] = control;
      }
    }
    
    return controls;
  }
  
  /**
   * Get a control value
   * @param {string} controlId - Control ID
   * @returns {*} - Control value or undefined if not found
   */
  getControlValue(controlId) {
    return this.state.controlValues.get(controlId);
  }
  
  /**
   * Get all control values
   * @param {string} [groupId] - Group ID to filter by
   * @returns {Object} - Map of control IDs to control values
   */
  getControlValues(groupId) {
    const values = {};
    
    if (groupId) {
      // Get values for controls in group
      const groupControls = this.state.controlGroups.get(groupId);
      
      if (groupControls) {
        for (const controlId of groupControls) {
          const value = this.state.controlValues.get(controlId);
          
          if (value !== undefined) {
            values[controlId] = value;
          }
        }
      }
    } else {
      // Get all values
      for (const [controlId, value] of this.state.controlValues.entries()) {
        values[controlId] = value;
      }
    }
    
    return values;
  }
  
  /**
   * Set a control value
   * @param {string} controlId - Control ID
   * @param {*} value - Control value
   * @returns {boolean} - Success status
   */
  setControlValue(controlId, value) {
    // Check if control exists
    if (!this.state.activeControls.has(controlId)) {
      if (this.options.enableLogging) {
        console.log(`ControlPanel: Control does not exist - ${controlId}`);
      }
      return false;
    }
    
    // Get control
    const control = this.state.activeControls.get(controlId);
    
    // Validate value
    if (control.validate && !control.validate(value)) {
      if (this.options.enableLogging) {
        console.log(`ControlPanel: Invalid value for control ${controlId}`);
      }
      return false;
    }
    
    // Set value
    this.state.controlValues.set(controlId, value);
    this.state.lastUpdate = Date.now();
    
    // Emit event
    this.emit('control-value-changed', {
      controlId,
      value,
      timestamp: Date.now()
    });
    
    // Publish update
    if (this.state.isConnected) {
      this.client.publish('control-updates', {
        controlId,
        value,
        timestamp: Date.now()
      }).catch((error) => {
        if (this.options.enableLogging) {
          console.error(`ControlPanel: Error publishing control update for ${controlId}:`, error);
        }
      });
    }
    
    if (this.options.enableLogging) {
      console.log(`ControlPanel: Control value set - ${controlId}: ${JSON.stringify(value)}`);
    }
    
    return true;
  }
  
  /**
   * Execute an action
   * @param {string} action - Action name
   * @param {Object} [params] - Action parameters
   * @returns {Promise<boolean>} - Promise that resolves with success status
   */
  async executeAction(action, params = {}) {
    if (!this.state.isConnected) {
      if (this.options.enableLogging) {
        console.log('ControlPanel: Not connected');
      }
      return false;
    }
    
    // Publish action
    try {
      await this.client.publish('control-actions', {
        action,
        params,
        timestamp: Date.now()
      });
      
      // Emit event
      this.emit('action-executed', {
        action,
        params,
        timestamp: Date.now()
      });
      
      if (this.options.enableLogging) {
        console.log(`ControlPanel: Action executed - ${action}`);
      }
      
      return true;
    } catch (error) {
      if (this.options.enableLogging) {
        console.error(`ControlPanel: Error executing action ${action}:`, error);
      }
      
      return false;
    }
  }
}

module.exports = ControlPanel;
