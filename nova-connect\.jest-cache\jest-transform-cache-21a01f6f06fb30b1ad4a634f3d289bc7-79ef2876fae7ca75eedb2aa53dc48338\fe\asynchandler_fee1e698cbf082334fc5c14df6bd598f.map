{"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "fn", "req", "res", "next", "Promise", "resolve", "catch", "retry<PERSON><PERSON><PERSON><PERSON><PERSON>", "options", "maxRetries", "initialDelay", "max<PERSON><PERSON><PERSON>", "shouldRetry", "retries", "delay", "error", "Math", "min", "jitter", "random", "actualDelay", "setTimeout", "circuitBreaker", "failureT<PERSON><PERSON>old", "resetTimeout", "isFailure", "failures", "circuitOpen", "nextAttempt", "Date", "now", "circuitBreakerWrapper", "args", "Error", "result", "module", "exports"], "sources": ["async-handler.js"], "sourcesContent": ["/**\n * NovaFuse Universal API Connector - Async Handler\n * \n * This module provides utilities for handling asynchronous operations.\n */\n\n/**\n * Wrap an async route handler to catch errors and pass them to the error middleware\n * \n * @param {Function} fn - The async route handler function\n * @returns {Function} - The wrapped route handler\n */\nfunction asyncHandler(fn) {\n  return (req, res, next) => {\n    Promise.resolve(fn(req, res, next)).catch(next);\n  };\n}\n\n/**\n * Retry an async function with exponential backoff\n * \n * @param {Function} fn - The async function to retry\n * @param {Object} options - Retry options\n * @param {number} options.maxRetries - Maximum number of retries\n * @param {number} options.initialDelay - Initial delay in milliseconds\n * @param {number} options.maxDelay - Maximum delay in milliseconds\n * @param {Function} options.shouldRetry - Function to determine if retry should be attempted\n * @returns {Promise<any>} - The result of the function\n */\nasync function retryWithBackoff(fn, options = {}) {\n  const maxRetries = options.maxRetries || 3;\n  const initialDelay = options.initialDelay || 1000;\n  const maxDelay = options.maxDelay || 30000;\n  const shouldRetry = options.shouldRetry || (() => true);\n  \n  let retries = 0;\n  let delay = initialDelay;\n  \n  while (true) {\n    try {\n      return await fn();\n    } catch (error) {\n      // If we've reached the maximum retries or shouldn't retry, throw the error\n      if (retries >= maxRetries || !shouldRetry(error)) {\n        throw error;\n      }\n      \n      // Increment retry count\n      retries++;\n      \n      // Calculate delay with exponential backoff and jitter\n      delay = Math.min(delay * 2, maxDelay);\n      const jitter = delay * 0.2 * Math.random();\n      const actualDelay = delay + jitter;\n      \n      // Wait for the delay\n      await new Promise(resolve => setTimeout(resolve, actualDelay));\n    }\n  }\n}\n\n/**\n * Create a circuit breaker for an async function\n * \n * @param {Function} fn - The async function to protect\n * @param {Object} options - Circuit breaker options\n * @param {number} options.failureThreshold - Number of failures before opening the circuit\n * @param {number} options.resetTimeout - Time in milliseconds before attempting to close the circuit\n * @param {Function} options.isFailure - Function to determine if a result is a failure\n * @returns {Function} - The protected function\n */\nfunction circuitBreaker(fn, options = {}) {\n  const failureThreshold = options.failureThreshold || 5;\n  const resetTimeout = options.resetTimeout || 30000;\n  const isFailure = options.isFailure || (error => true);\n  \n  let failures = 0;\n  let circuitOpen = false;\n  let nextAttempt = Date.now();\n  \n  return async function circuitBreakerWrapper(...args) {\n    // If the circuit is open, check if we should try again\n    if (circuitOpen) {\n      if (Date.now() < nextAttempt) {\n        throw new Error('Circuit is open');\n      }\n      \n      // Circuit is half-open, allow one request\n      circuitOpen = false;\n    }\n    \n    try {\n      const result = await fn(...args);\n      \n      // Reset failures on success\n      failures = 0;\n      \n      return result;\n    } catch (error) {\n      // Check if this error should count as a failure\n      if (isFailure(error)) {\n        failures++;\n        \n        // If we've reached the threshold, open the circuit\n        if (failures >= failureThreshold) {\n          circuitOpen = true;\n          nextAttempt = Date.now() + resetTimeout;\n        }\n      }\n      \n      throw error;\n    }\n  };\n}\n\nmodule.exports = {\n  asyncHandler,\n  retryWithBackoff,\n  circuitBreaker\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,YAAYA,CAACC,EAAE,EAAE;EACxB,OAAO,CAACC,GAAG,EAAEC,GAAG,EAAEC,IAAI,KAAK;IACzBC,OAAO,CAACC,OAAO,CAACL,EAAE,CAACC,GAAG,EAAEC,GAAG,EAAEC,IAAI,CAAC,CAAC,CAACG,KAAK,CAACH,IAAI,CAAC;EACjD,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAeI,gBAAgBA,CAACP,EAAE,EAAEQ,OAAO,GAAG,CAAC,CAAC,EAAE;EAChD,MAAMC,UAAU,GAAGD,OAAO,CAACC,UAAU,IAAI,CAAC;EAC1C,MAAMC,YAAY,GAAGF,OAAO,CAACE,YAAY,IAAI,IAAI;EACjD,MAAMC,QAAQ,GAAGH,OAAO,CAACG,QAAQ,IAAI,KAAK;EAC1C,MAAMC,WAAW,GAAGJ,OAAO,CAACI,WAAW,KAAK,MAAM,IAAI,CAAC;EAEvD,IAAIC,OAAO,GAAG,CAAC;EACf,IAAIC,KAAK,GAAGJ,YAAY;EAExB,OAAO,IAAI,EAAE;IACX,IAAI;MACF,OAAO,MAAMV,EAAE,CAAC,CAAC;IACnB,CAAC,CAAC,OAAOe,KAAK,EAAE;MACd;MACA,IAAIF,OAAO,IAAIJ,UAAU,IAAI,CAACG,WAAW,CAACG,KAAK,CAAC,EAAE;QAChD,MAAMA,KAAK;MACb;;MAEA;MACAF,OAAO,EAAE;;MAET;MACAC,KAAK,GAAGE,IAAI,CAACC,GAAG,CAACH,KAAK,GAAG,CAAC,EAAEH,QAAQ,CAAC;MACrC,MAAMO,MAAM,GAAGJ,KAAK,GAAG,GAAG,GAAGE,IAAI,CAACG,MAAM,CAAC,CAAC;MAC1C,MAAMC,WAAW,GAAGN,KAAK,GAAGI,MAAM;;MAElC;MACA,MAAM,IAAId,OAAO,CAACC,OAAO,IAAIgB,UAAU,CAAChB,OAAO,EAAEe,WAAW,CAAC,CAAC;IAChE;EACF;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,cAAcA,CAACtB,EAAE,EAAEQ,OAAO,GAAG,CAAC,CAAC,EAAE;EACxC,MAAMe,gBAAgB,GAAGf,OAAO,CAACe,gBAAgB,IAAI,CAAC;EACtD,MAAMC,YAAY,GAAGhB,OAAO,CAACgB,YAAY,IAAI,KAAK;EAClD,MAAMC,SAAS,GAAGjB,OAAO,CAACiB,SAAS,KAAKV,KAAK,IAAI,IAAI,CAAC;EAEtD,IAAIW,QAAQ,GAAG,CAAC;EAChB,IAAIC,WAAW,GAAG,KAAK;EACvB,IAAIC,WAAW,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;EAE5B,OAAO,eAAeC,qBAAqBA,CAAC,GAAGC,IAAI,EAAE;IACnD;IACA,IAAIL,WAAW,EAAE;MACf,IAAIE,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGF,WAAW,EAAE;QAC5B,MAAM,IAAIK,KAAK,CAAC,iBAAiB,CAAC;MACpC;;MAEA;MACAN,WAAW,GAAG,KAAK;IACrB;IAEA,IAAI;MACF,MAAMO,MAAM,GAAG,MAAMlC,EAAE,CAAC,GAAGgC,IAAI,CAAC;;MAEhC;MACAN,QAAQ,GAAG,CAAC;MAEZ,OAAOQ,MAAM;IACf,CAAC,CAAC,OAAOnB,KAAK,EAAE;MACd;MACA,IAAIU,SAAS,CAACV,KAAK,CAAC,EAAE;QACpBW,QAAQ,EAAE;;QAEV;QACA,IAAIA,QAAQ,IAAIH,gBAAgB,EAAE;UAChCI,WAAW,GAAG,IAAI;UAClBC,WAAW,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGN,YAAY;QACzC;MACF;MAEA,MAAMT,KAAK;IACb;EACF,CAAC;AACH;AAEAoB,MAAM,CAACC,OAAO,GAAG;EACfrC,YAAY;EACZQ,gBAAgB;EAChBe;AACF,CAAC", "ignoreList": []}