/**
 * Zapier Controller
 * 
 * This controller handles Zapier integration for NovaConnect UAC.
 */

const ZapierService = require('../services/ZapierService');
const logger = require('../../config/logger');

// Initialize services
const zapierService = new ZapierService();

/**
 * Get Zapier app definition
 */
const getAppDefinition = async (req, res, next) => {
  try {
    const appDefinition = zapierService.getAppDefinition();
    
    res.status(200).json(appDefinition);
  } catch (error) {
    logger.error('Error getting Zapier app definition:', error);
    next(error);
  }
};

/**
 * Get Zapier triggers
 */
const getTriggers = async (req, res, next) => {
  try {
    const triggers = await zapierService.getTriggers();
    
    res.status(200).json(triggers);
  } catch (error) {
    logger.error('Error getting Zapier triggers:', error);
    next(error);
  }
};

/**
 * Get Zapier actions
 */
const getActions = async (req, res, next) => {
  try {
    const actions = await zapierService.getActions();
    
    res.status(200).json(actions);
  } catch (error) {
    logger.error('Error getting Zapier actions:', error);
    next(error);
  }
};

/**
 * OAuth authorization endpoint
 */
const authorizeOAuth = async (req, res, next) => {
  try {
    const { client_id, redirect_uri, state, response_type } = req.query;
    
    // Validate client ID
    if (client_id !== zapierService.clientId) {
      return res.status(400).json({
        error: 'invalid_client',
        error_description: 'Invalid client ID'
      });
    }
    
    // Validate response type
    if (response_type !== 'code') {
      return res.status(400).json({
        error: 'unsupported_response_type',
        error_description: 'Only code response type is supported'
      });
    }
    
    // In a real implementation, this would redirect to a login page
    // For now, we'll generate a code directly
    const code = Math.random().toString(36).substring(2, 15);
    
    // Redirect to redirect URI with code and state
    const redirectUrl = new URL(redirect_uri);
    redirectUrl.searchParams.append('code', code);
    redirectUrl.searchParams.append('state', state);
    
    res.redirect(redirectUrl.toString());
  } catch (error) {
    logger.error('Error authorizing OAuth:', error);
    next(error);
  }
};

/**
 * OAuth token endpoint
 */
const getOAuthToken = async (req, res, next) => {
  try {
    const { grant_type, code, refresh_token, redirect_uri, client_id, client_secret } = req.body;
    
    // Validate client credentials
    if (client_id !== zapierService.clientId || client_secret !== zapierService.clientSecret) {
      return res.status(401).json({
        error: 'invalid_client',
        error_description: 'Invalid client credentials'
      });
    }
    
    // Handle different grant types
    if (grant_type === 'authorization_code') {
      // Validate code
      if (!code) {
        return res.status(400).json({
          error: 'invalid_request',
          error_description: 'Code is required'
        });
      }
      
      // Generate access token
      const tokenResponse = await zapierService.generateAccessToken(code, redirect_uri);
      
      res.status(200).json(tokenResponse);
    } else if (grant_type === 'refresh_token') {
      // Validate refresh token
      if (!refresh_token) {
        return res.status(400).json({
          error: 'invalid_request',
          error_description: 'Refresh token is required'
        });
      }
      
      // Refresh access token
      const tokenResponse = await zapierService.refreshAccessToken(refresh_token);
      
      res.status(200).json(tokenResponse);
    } else {
      return res.status(400).json({
        error: 'unsupported_grant_type',
        error_description: 'Unsupported grant type'
      });
    }
  } catch (error) {
    logger.error('Error getting OAuth token:', error);
    
    res.status(400).json({
      error: 'invalid_grant',
      error_description: error.message
    });
  }
};

/**
 * Before app hook
 */
const beforeApp = async (req, res, next) => {
  try {
    // This hook is called before the Zapier app is loaded
    // It can be used to perform any necessary setup
    
    res.status(200).json({
      status: 'success',
      message: 'Before app hook executed successfully'
    });
  } catch (error) {
    logger.error('Error executing before app hook:', error);
    next(error);
  }
};

/**
 * After app hook
 */
const afterApp = async (req, res, next) => {
  try {
    // This hook is called after the Zapier app is loaded
    // It can be used to perform any necessary cleanup
    
    res.status(200).json({
      status: 'success',
      message: 'After app hook executed successfully'
    });
  } catch (error) {
    logger.error('Error executing after app hook:', error);
    next(error);
  }
};

/**
 * New connector trigger
 */
const newConnectorTrigger = async (req, res, next) => {
  try {
    // In a real implementation, this would fetch new connectors since the last poll
    // For now, we'll return sample data
    
    const connectors = [
      {
        id: 'conn-123',
        name: 'Sample Connector',
        type: 'api',
        createdAt: new Date().toISOString()
      }
    ];
    
    res.status(200).json(connectors);
  } catch (error) {
    logger.error('Error executing new connector trigger:', error);
    next(error);
  }
};

/**
 * New workflow trigger
 */
const newWorkflowTrigger = async (req, res, next) => {
  try {
    // In a real implementation, this would fetch new workflows since the last poll
    // For now, we'll return sample data
    
    const workflows = [
      {
        id: 'wf-123',
        name: 'Sample Workflow',
        status: 'active',
        createdAt: new Date().toISOString()
      }
    ];
    
    res.status(200).json(workflows);
  } catch (error) {
    logger.error('Error executing new workflow trigger:', error);
    next(error);
  }
};

/**
 * Compliance event trigger
 */
const complianceEventTrigger = async (req, res, next) => {
  try {
    // In a real implementation, this would fetch new compliance events since the last poll
    // For now, we'll return sample data
    
    const events = [
      {
        id: 'evt-123',
        type: 'compliance.violation',
        severity: 'high',
        resource: 'storage-bucket-123',
        details: 'Public access detected',
        timestamp: new Date().toISOString()
      }
    ];
    
    res.status(200).json(events);
  } catch (error) {
    logger.error('Error executing compliance event trigger:', error);
    next(error);
  }
};

/**
 * Create connector action
 */
const createConnectorAction = async (req, res, next) => {
  try {
    const { name, type, config } = req.body;
    
    // Validate required fields
    if (!name || !type || !config) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Name, type, and config are required'
      });
    }
    
    // In a real implementation, this would create a connector
    // For now, we'll return sample data
    
    const connector = {
      id: `conn-${Math.floor(Math.random() * 1000)}`,
      name,
      type,
      config: typeof config === 'string' ? JSON.parse(config) : config,
      createdAt: new Date().toISOString()
    };
    
    res.status(201).json(connector);
  } catch (error) {
    logger.error('Error executing create connector action:', error);
    next(error);
  }
};

/**
 * Execute workflow action
 */
const executeWorkflowAction = async (req, res, next) => {
  try {
    const { workflowId, inputs } = req.body;
    
    // Validate required fields
    if (!workflowId) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Workflow ID is required'
      });
    }
    
    // In a real implementation, this would execute a workflow
    // For now, we'll return sample data
    
    const execution = {
      id: `exec-${Math.floor(Math.random() * 1000)}`,
      workflowId,
      status: 'completed',
      result: {
        success: true,
        data: inputs ? (typeof inputs === 'string' ? JSON.parse(inputs) : inputs) : {}
      },
      startedAt: new Date().toISOString(),
      completedAt: new Date().toISOString()
    };
    
    res.status(200).json(execution);
  } catch (error) {
    logger.error('Error executing workflow action:', error);
    next(error);
  }
};

/**
 * Create compliance evidence action
 */
const createComplianceEvidenceAction = async (req, res, next) => {
  try {
    const { controlId, evidenceType, description, data } = req.body;
    
    // Validate required fields
    if (!controlId || !evidenceType || !description) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Control ID, evidence type, and description are required'
      });
    }
    
    // In a real implementation, this would create compliance evidence
    // For now, we'll return sample data
    
    const evidence = {
      id: `evid-${Math.floor(Math.random() * 1000)}`,
      controlId,
      evidenceType,
      description,
      data: data ? (typeof data === 'string' ? JSON.parse(data) : data) : null,
      createdAt: new Date().toISOString()
    };
    
    res.status(201).json(evidence);
  } catch (error) {
    logger.error('Error executing create compliance evidence action:', error);
    next(error);
  }
};

/**
 * Register Zapier app
 */
const registerApp = async (req, res, next) => {
  try {
    const { name, description, webhookUrl } = req.body;
    
    // Validate required fields
    if (!name || !webhookUrl) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Name and webhook URL are required'
      });
    }
    
    // Register app
    const app = await zapierService.registerApp({
      name,
      description,
      webhookUrl
    });
    
    res.status(201).json(app);
  } catch (error) {
    logger.error('Error registering Zapier app:', error);
    next(error);
  }
};

/**
 * Get all Zapier apps
 */
const getAllApps = async (req, res, next) => {
  try {
    const apps = await zapierService.getAllApps();
    
    res.status(200).json(apps);
  } catch (error) {
    logger.error('Error getting all Zapier apps:', error);
    next(error);
  }
};

/**
 * Get Zapier app by ID
 */
const getAppById = async (req, res, next) => {
  try {
    const { id } = req.params;
    
    const app = await zapierService.getAppById(id);
    
    res.status(200).json(app);
  } catch (error) {
    logger.error('Error getting Zapier app by ID:', error);
    
    if (error.message.includes('not found')) {
      return res.status(404).json({
        error: 'Not Found',
        message: error.message
      });
    }
    
    next(error);
  }
};

/**
 * Update Zapier app
 */
const updateApp = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { name, description, webhookUrl } = req.body;
    
    // Update app
    const app = await zapierService.updateApp(id, {
      name,
      description,
      webhookUrl
    });
    
    res.status(200).json(app);
  } catch (error) {
    logger.error('Error updating Zapier app:', error);
    
    if (error.message.includes('not found')) {
      return res.status(404).json({
        error: 'Not Found',
        message: error.message
      });
    }
    
    next(error);
  }
};

/**
 * Delete Zapier app
 */
const deleteApp = async (req, res, next) => {
  try {
    const { id } = req.params;
    
    await zapierService.deleteApp(id);
    
    res.status(204).end();
  } catch (error) {
    logger.error('Error deleting Zapier app:', error);
    
    if (error.message.includes('not found')) {
      return res.status(404).json({
        error: 'Not Found',
        message: error.message
      });
    }
    
    next(error);
  }
};

/**
 * Register Zapier trigger
 */
const registerTrigger = async (req, res, next) => {
  try {
    const { key, noun, display, operation } = req.body;
    
    // Validate required fields
    if (!key || !noun || !display || !operation) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Key, noun, display, and operation are required'
      });
    }
    
    // Register trigger
    const trigger = await zapierService.registerTrigger({
      key,
      noun,
      display,
      operation
    });
    
    res.status(201).json(trigger);
  } catch (error) {
    logger.error('Error registering Zapier trigger:', error);
    next(error);
  }
};

/**
 * Register Zapier action
 */
const registerAction = async (req, res, next) => {
  try {
    const { key, noun, display, operation } = req.body;
    
    // Validate required fields
    if (!key || !noun || !display || !operation) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Key, noun, display, and operation are required'
      });
    }
    
    // Register action
    const action = await zapierService.registerAction({
      key,
      noun,
      display,
      operation
    });
    
    res.status(201).json(action);
  } catch (error) {
    logger.error('Error registering Zapier action:', error);
    next(error);
  }
};

module.exports = {
  getAppDefinition,
  getTriggers,
  getActions,
  authorizeOAuth,
  getOAuthToken,
  beforeApp,
  afterApp,
  newConnectorTrigger,
  newWorkflowTrigger,
  complianceEventTrigger,
  createConnectorAction,
  executeWorkflowAction,
  createComplianceEvidenceAction,
  registerApp,
  getAllApps,
  getAppById,
  updateApp,
  deleteApp,
  registerTrigger,
  registerAction
};
