/**
 * DecayRateCalculator
 * 
 * This module implements the DecayRateCalculator class, which calculates personalized
 * decay rates for biological coherence.
 * 
 * The DecayRateCalculator is responsible for:
 * 1. Calculating personalized decay rates based on subject data
 * 2. Projecting coherence decay over time
 * 3. Simulating the effect of interventions on coherence decay
 * 4. Ensuring decay rates stay within biologically plausible bounds
 * 5. Supporting multi-factor decay rate adjustments
 */

const { performance } = require('perf_hooks');

/**
 * DecayRateCalculator class
 */
class DecayRateCalculator {
  /**
   * Create a new DecayRateCalculator instance
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    this.options = {
      defaultDecayRate: 0.05, // Default decay rate (5% per time unit)
      minDecayRate: 0.01, // Minimum decay rate (1% per time unit)
      maxDecayRate: 0.2, // Maximum decay rate (20% per time unit)
      baselineDecayRate: 0.05, // Baseline decay rate for healthy individuals
      ageImpactFactor: 0.001, // Impact of age on decay rate (0.1% per year)
      geneticImpactFactor: 0.5, // Impact of genetic factors (0-1)
      environmentalImpactFactor: 0.3, // Impact of environmental factors (0-1)
      lifestyleImpactFactor: 0.4, // Impact of lifestyle factors (0-1)
      stressImpactFactor: 0.3, // Impact of stress factors (0-1)
      enableCaching: true,
      enableMetrics: true,
      ...options
    };
    
    // Initialize cache
    this.cache = new Map();
    
    // Initialize metrics
    this.metrics = {
      processingTimeMs: 0,
      cacheHits: 0,
      cacheMisses: 0,
      totalProcessed: 0
    };
    
    console.log('DecayRateCalculator initialized');
  }
  
  /**
   * Calculate personalized decay rate
   * @param {Object} subjectData - Subject data
   * @param {Object} environmentalData - Environmental data
   * @returns {Object} - Decay rate calculation result
   */
  calculateDecayRate(subjectData, environmentalData) {
    const startTime = performance.now();
    this.metrics.totalProcessed++;
    
    // Generate cache key
    const cacheKey = this._generateCacheKey(subjectData, environmentalData);
    
    // Check cache if enabled
    if (this.options.enableCaching && this.cache.has(cacheKey)) {
      this.metrics.cacheHits++;
      const cachedResult = this.cache.get(cacheKey);
      
      // Update processing time
      this.metrics.processingTimeMs += performance.now() - startTime;
      
      return cachedResult;
    }
    
    this.metrics.cacheMisses++;
    
    // Extract key subject features
    const {
      age = 35,
      geneticProfile = {},
      lifestyleFactors = {},
      stressFactors = {},
      medicalHistory = {},
      currentInterventions = []
    } = subjectData || {};
    
    // Start with baseline decay rate
    let decayRate = this.options.baselineDecayRate;
    
    // Adjust for age (decay rate increases with age)
    const ageAdjustment = Math.max(0, age - 25) * this.options.ageImpactFactor;
    decayRate += ageAdjustment;
    
    // Adjust for genetic factors
    const geneticAdjustment = this._calculateGeneticAdjustment(geneticProfile);
    decayRate += geneticAdjustment;
    
    // Adjust for environmental factors
    const environmentalAdjustment = this._calculateEnvironmentalAdjustment(environmentalData);
    decayRate += environmentalAdjustment;
    
    // Adjust for lifestyle factors
    const lifestyleAdjustment = this._calculateLifestyleAdjustment(lifestyleFactors);
    decayRate += lifestyleAdjustment;
    
    // Adjust for stress factors
    const stressAdjustment = this._calculateStressAdjustment(stressFactors);
    decayRate += stressAdjustment;
    
    // Adjust for medical history
    const medicalAdjustment = this._calculateMedicalAdjustment(medicalHistory);
    decayRate += medicalAdjustment;
    
    // Adjust for current interventions
    const interventionAdjustment = this._calculateInterventionAdjustment(currentInterventions);
    decayRate += interventionAdjustment;
    
    // Ensure decay rate is within bounds
    decayRate = Math.max(this.options.minDecayRate, Math.min(this.options.maxDecayRate, decayRate));
    
    // Prepare result
    const result = {
      decayRate,
      adjustments: {
        ageAdjustment,
        geneticAdjustment,
        environmentalAdjustment,
        lifestyleAdjustment,
        stressAdjustment,
        medicalAdjustment,
        interventionAdjustment
      },
      calculatedAt: new Date().toISOString()
    };
    
    // Cache result if caching is enabled
    if (this.options.enableCaching) {
      this.cache.set(cacheKey, result);
      
      // Limit cache size
      if (this.cache.size > 1000) {
        const oldestKey = this.cache.keys().next().value;
        this.cache.delete(oldestKey);
      }
    }
    
    // Update processing time
    this.metrics.processingTimeMs += performance.now() - startTime;
    
    return result;
  }
  
  /**
   * Project coherence decay over time
   * @param {number} initialCoherence - Initial coherence value
   * @param {number} decayRate - Decay rate
   * @param {number} timeSteps - Number of time steps to project
   * @returns {Array} - Projected coherence values
   */
  projectCoherenceDecay(initialCoherence, decayRate, timeSteps) {
    if (timeSteps <= 0) {
      return [initialCoherence];
    }
    
    // Ensure decay rate is within bounds
    const boundedDecayRate = Math.max(this.options.minDecayRate, Math.min(this.options.maxDecayRate, decayRate));
    
    // Initialize projection
    const projection = [initialCoherence];
    let currentCoherence = initialCoherence;
    
    // Project decay over time steps
    for (let i = 0; i < timeSteps; i++) {
      // Apply exponential decay formula: C(t+1) = C(t) * (1 - r)
      currentCoherence = currentCoherence * (1 - boundedDecayRate);
      
      // Ensure coherence doesn't go below 0
      currentCoherence = Math.max(0, currentCoherence);
      
      projection.push(currentCoherence);
    }
    
    return projection;
  }
  
  /**
   * Simulate the effect of an intervention on coherence decay
   * @param {number} initialCoherence - Initial coherence value
   * @param {number} decayRate - Decay rate
   * @param {Object} intervention - Intervention details
   * @param {number} timeSteps - Number of time steps to project
   * @returns {Object} - Simulation result
   */
  simulateInterventionEffect(initialCoherence, decayRate, intervention, timeSteps) {
    if (!intervention || timeSteps <= 0) {
      return {
        baselineProjection: this.projectCoherenceDecay(initialCoherence, decayRate, timeSteps),
        interventionProjection: this.projectCoherenceDecay(initialCoherence, decayRate, timeSteps),
        netEffect: 0
      };
    }
    
    // Calculate baseline projection (without intervention)
    const baselineProjection = this.projectCoherenceDecay(initialCoherence, decayRate, timeSteps);
    
    // Extract intervention parameters
    const {
      effectOnDecayRate = 0,
      effectOnCoherence = 0,
      effectDuration = timeSteps,
      effectDelay = 0,
      effectTaper = 0.2 // How quickly the effect tapers off (0-1)
    } = intervention;
    
    // Initialize intervention projection
    const interventionProjection = [initialCoherence];
    let currentCoherence = initialCoherence;
    
    // Simulate intervention effect over time steps
    for (let i = 0; i < timeSteps; i++) {
      // Calculate intervention effect at this time step
      let currentEffectOnDecayRate = 0;
      let currentEffectOnCoherence = 0;
      
      // Apply effect after delay and before effect duration ends
      if (i >= effectDelay && i < effectDelay + effectDuration) {
        // Calculate effect strength based on time since intervention started
        const timeInEffect = i - effectDelay;
        const effectStrength = Math.exp(-effectTaper * timeInEffect / effectDuration);
        
        // Apply effect strength to intervention effects
        currentEffectOnDecayRate = effectOnDecayRate * effectStrength;
        currentEffectOnCoherence = effectOnCoherence * effectStrength;
      }
      
      // Apply modified decay rate
      const modifiedDecayRate = Math.max(
        this.options.minDecayRate,
        Math.min(this.options.maxDecayRate, decayRate + currentEffectOnDecayRate)
      );
      
      // Apply decay and coherence boost
      currentCoherence = currentCoherence * (1 - modifiedDecayRate) + currentEffectOnCoherence;
      
      // Ensure coherence is within bounds
      currentCoherence = Math.max(0, Math.min(1, currentCoherence));
      
      interventionProjection.push(currentCoherence);
    }
    
    // Calculate net effect (area between curves)
    let netEffect = 0;
    for (let i = 0; i < interventionProjection.length; i++) {
      netEffect += interventionProjection[i] - baselineProjection[i];
    }
    
    return {
      baselineProjection,
      interventionProjection,
      netEffect
    };
  }
  
  /**
   * Calculate genetic adjustment to decay rate
   * @param {Object} geneticProfile - Genetic profile data
   * @returns {number} - Genetic adjustment
   * @private
   */
  _calculateGeneticAdjustment(geneticProfile) {
    if (!geneticProfile) {
      return 0;
    }
    
    // Extract genetic factors
    const {
      longevityGenes = 0.5, // 0-1, higher is better
      inflammationGenes = 0.5, // 0-1, higher is worse
      oxidativeStressGenes = 0.5, // 0-1, higher is worse
      telomereLengthGenes = 0.5, // 0-1, higher is better
      mitochondrialGenes = 0.5 // 0-1, higher is better
    } = geneticProfile;
    
    // Calculate positive genetic factors (reduce decay rate)
    const positiveFactors = (longevityGenes + telomereLengthGenes + mitochondrialGenes) / 3;
    
    // Calculate negative genetic factors (increase decay rate)
    const negativeFactors = (inflammationGenes + oxidativeStressGenes) / 2;
    
    // Apply 18/82 principle: 18% weight to positive factors, 82% to negative factors
    const geneticImpact = (0.18 * (1 - positiveFactors)) + (0.82 * negativeFactors);
    
    // Scale by genetic impact factor
    return geneticImpact * this.options.geneticImpactFactor;
  }
  
  /**
   * Calculate environmental adjustment to decay rate
   * @param {Object} environmentalData - Environmental data
   * @returns {number} - Environmental adjustment
   * @private
   */
  _calculateEnvironmentalAdjustment(environmentalData) {
    if (!environmentalData) {
      return 0;
    }
    
    // Extract environmental factors
    const {
      toxinLevel = 0, // 0-1, higher is worse
      radiationLevel = 0, // 0-1, higher is worse
      airQuality = 0.7, // 0-1, higher is better
      waterQuality = 0.7, // 0-1, higher is better
      temperature = 22, // Celsius, optimal around 22
      humidity = 45 // Percent, optimal around 45
    } = environmentalData;
    
    // Calculate temperature deviation (optimal is 22°C)
    const tempDeviation = Math.abs(temperature - 22) / 20; // Normalize to 0-1
    
    // Calculate humidity deviation (optimal is 45%)
    const humidityDeviation = Math.abs(humidity - 45) / 50; // Normalize to 0-1
    
    // Calculate environmental quality (higher is better)
    const environmentalQuality = (airQuality + waterQuality) / 2;
    
    // Calculate environmental stress (higher is worse)
    const environmentalStress = (toxinLevel + radiationLevel + tempDeviation + humidityDeviation) / 4;
    
    // Apply 18/82 principle: 18% weight to environmental quality, 82% to environmental stress
    const environmentalImpact = (0.18 * (1 - environmentalQuality)) + (0.82 * environmentalStress);
    
    // Scale by environmental impact factor
    return environmentalImpact * this.options.environmentalImpactFactor;
  }
  
  /**
   * Calculate lifestyle adjustment to decay rate
   * @param {Object} lifestyleFactors - Lifestyle factors
   * @returns {number} - Lifestyle adjustment
   * @private
   */
  _calculateLifestyleAdjustment(lifestyleFactors) {
    if (!lifestyleFactors) {
      return 0;
    }
    
    // Extract lifestyle factors
    const {
      diet = 0.5, // 0-1, higher is better
      exercise = 0.5, // 0-1, higher is better
      sleep = 0.5, // 0-1, higher is better
      smoking = 0, // 0-1, higher is worse
      alcohol = 0, // 0-1, higher is worse
      socialConnections = 0.5 // 0-1, higher is better
    } = lifestyleFactors;
    
    // Calculate positive lifestyle factors (reduce decay rate)
    const positiveFactors = (diet + exercise + sleep + socialConnections) / 4;
    
    // Calculate negative lifestyle factors (increase decay rate)
    const negativeFactors = (smoking + alcohol) / 2;
    
    // Apply 18/82 principle: 18% weight to positive factors, 82% to negative factors
    const lifestyleImpact = (0.18 * (1 - positiveFactors)) + (0.82 * negativeFactors);
    
    // Scale by lifestyle impact factor
    return lifestyleImpact * this.options.lifestyleImpactFactor;
  }
  
  /**
   * Calculate stress adjustment to decay rate
   * @param {Object} stressFactors - Stress factors
   * @returns {number} - Stress adjustment
   * @private
   */
  _calculateStressAdjustment(stressFactors) {
    if (!stressFactors) {
      return 0;
    }
    
    // Extract stress factors
    const {
      chronicStress = 0.3, // 0-1, higher is worse
      acuteStress = 0.2, // 0-1, higher is worse
      workStress = 0.3, // 0-1, higher is worse
      financialStress = 0.3, // 0-1, higher is worse
      relationshipStress = 0.2 // 0-1, higher is worse
    } = stressFactors;
    
    // Calculate overall stress level
    const stressLevel = (
      chronicStress * 0.3 + 
      acuteStress * 0.1 + 
      workStress * 0.2 + 
      financialStress * 0.2 + 
      relationshipStress * 0.2
    );
    
    // Scale by stress impact factor
    return stressLevel * this.options.stressImpactFactor;
  }
  
  /**
   * Calculate medical adjustment to decay rate
   * @param {Object} medicalHistory - Medical history
   * @returns {number} - Medical adjustment
   * @private
   */
  _calculateMedicalAdjustment(medicalHistory) {
    if (!medicalHistory) {
      return 0;
    }
    
    // Extract medical factors
    const {
      chronicConditions = [], // List of chronic conditions
      medications = [], // List of medications
      surgeries = [], // List of surgeries
      allergies = [] // List of allergies
    } = medicalHistory;
    
    // Calculate impact of chronic conditions
    const chronicConditionImpact = chronicConditions.length * 0.02;
    
    // Calculate impact of medications (some may reduce decay rate)
    const medicationImpact = medications.length * 0.01;
    
    // Calculate impact of surgeries
    const surgeryImpact = surgeries.length * 0.01;
    
    // Calculate total medical adjustment
    return chronicConditionImpact + medicationImpact + surgeryImpact;
  }
  
  /**
   * Calculate intervention adjustment to decay rate
   * @param {Array} currentInterventions - Current interventions
   * @returns {number} - Intervention adjustment
   * @private
   */
  _calculateInterventionAdjustment(currentInterventions) {
    if (!currentInterventions || currentInterventions.length === 0) {
      return 0;
    }
    
    // Calculate total intervention effect
    let totalEffect = 0;
    
    for (const intervention of currentInterventions) {
      // Extract intervention effect
      const effectOnDecayRate = intervention.effectOnDecayRate || 0;
      
      // Add to total effect
      totalEffect += effectOnDecayRate;
    }
    
    return totalEffect;
  }
  
  /**
   * Generate cache key for subject and environmental data
   * @param {Object} subjectData - Subject data
   * @param {Object} environmentalData - Environmental data
   * @returns {string} - Cache key
   * @private
   */
  _generateCacheKey(subjectData, environmentalData) {
    try {
      // Use only essential properties for the key
      const keyData = {
        age: subjectData?.age || 35,
        genetic: this._hashObject(subjectData?.geneticProfile),
        lifestyle: this._hashObject(subjectData?.lifestyleFactors),
        stress: this._hashObject(subjectData?.stressFactors),
        env: this._hashObject(environmentalData)
      };
      
      return JSON.stringify(keyData);
    } catch (error) {
      console.error('Error generating cache key:', error);
      return Date.now().toString(); // Fallback to timestamp
    }
  }
  
  /**
   * Create a simple hash of an object
   * @param {Object} obj - Input object
   * @returns {number} - Hash value
   * @private
   */
  _hashObject(obj) {
    if (!obj) return 0;
    
    const str = JSON.stringify(obj);
    let hash = 0;
    
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    
    return hash;
  }
}

module.exports = DecayRateCalculator;
