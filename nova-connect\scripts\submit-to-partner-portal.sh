#!/bin/bash
# <PERSON>ript to submit NovaConnect UAC to Google Cloud Marketplace Partner Portal

# Check if API key is provided
if [ -z "$1" ]; then
  echo "Error: Partner Portal API key is required"
  echo "Usage: $0 <api_key>"
  exit 1
fi

API_KEY=$1
PARTNER_ID="novafuse"
SOLUTION_ID="novafuse-uac"
VERSION=$(cat ../package.json | grep version | head -1 | awk -F: '{ print $2 }' | sed 's/[",]//g' | tr -d '[:space:]')
PACKAGE_PATH=$(ls ../marketplace/*.tar.gz)
PACKAGE_NAME=$(basename $PACKAGE_PATH)

echo "Submitting NovaConnect UAC v$VERSION to Google Cloud Marketplace Partner Portal..."

# Upload package to Partner Portal
echo "Uploading package $PACKAGE_NAME..."
UPLOAD_RESPONSE=$(curl -s -X POST \
  "https://cloudpartnerportal.googleapis.com/v1/partners/$PARTNER_ID/solutions/$SOLUTION_ID/versions/$VERSION/packages" \
  -H "Authorization: Bearer $API_KEY" \
  -H "Content-Type: multipart/form-data" \
  -F "package=@$PACKAGE_PATH")

# Check if upload was successful
if [[ $UPLOAD_RESPONSE == *"error"* ]]; then
  echo "Error uploading package: $UPLOAD_RESPONSE"
  exit 1
fi

echo "Package uploaded successfully"

# Create new version in Partner Portal
echo "Creating version $VERSION in Partner Portal..."
VERSION_RESPONSE=$(curl -s -X POST \
  "https://cloudpartnerportal.googleapis.com/v1/partners/$PARTNER_ID/solutions/$SOLUTION_ID/versions" \
  -H "Authorization: Bearer $API_KEY" \
  -H "Content-Type: application/json" \
  -d "{
    \"version\": \"$VERSION\",
    \"releaseNotes\": \"See release notes at https://docs.novafuse.io/releases/$VERSION\",
    \"packageName\": \"$PACKAGE_NAME\"
  }")

# Check if version creation was successful
if [[ $VERSION_RESPONSE == *"error"* ]]; then
  echo "Error creating version: $VERSION_RESPONSE"
  exit 1
fi

echo "Version $VERSION created successfully"

# Submit version for review
echo "Submitting version $VERSION for review..."
SUBMIT_RESPONSE=$(curl -s -X POST \
  "https://cloudpartnerportal.googleapis.com/v1/partners/$PARTNER_ID/solutions/$SOLUTION_ID/versions/$VERSION/submit" \
  -H "Authorization: Bearer $API_KEY")

# Check if submission was successful
if [[ $SUBMIT_RESPONSE == *"error"* ]]; then
  echo "Error submitting version for review: $SUBMIT_RESPONSE"
  exit 1
fi

echo "Version $VERSION submitted for review successfully"
echo "Submission complete!"
