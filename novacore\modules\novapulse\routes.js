/**
 * NovaCore NovaPulse Routes
 * 
 * This file defines the routes for the NovaPulse module.
 * NovaPulse is the Universal Regulatory Compliance Monitoring System (URCMS) component
 * of the NovaFuse Cyber-Safety Platform.
 */

const express = require('express');
const router = express.Router();
const {
  RegulationController,
  RegulatoryChangeController,
  FrameworkController,
  ComplianceProfileController
} = require('./controllers');
const { authenticate, authorize } = require('../../api/middleware/authMiddleware');

// Regulation Routes
router.post(
  '/regulations',
  authenticate,
  authorize('create:regulation'),
  RegulationController.createRegulation
);

router.get(
  '/regulations',
  authenticate,
  authorize('read:regulation'),
  RegulationController.getAllRegulations
);

router.get(
  '/regulations/:id',
  authenticate,
  authorize('read:regulation'),
  RegulationController.getRegulationById
);

router.put(
  '/regulations/:id',
  authenticate,
  authorize('update:regulation'),
  RegulationController.updateRegulation
);

router.delete(
  '/regulations/:id',
  authenticate,
  authorize('delete:regulation'),
  RegulationController.deleteRegulation
);

router.post(
  '/regulations/:id/requirements',
  authenticate,
  authorize('update:regulation'),
  RegulationController.addRequirement
);

router.put(
  '/regulations/:id/requirements/:requirementId',
  authenticate,
  authorize('update:regulation'),
  RegulationController.updateRequirement
);

router.delete(
  '/regulations/:id/requirements/:requirementId',
  authenticate,
  authorize('update:regulation'),
  RegulationController.removeRequirement
);

router.post(
  '/regulations/:id/versions',
  authenticate,
  authorize('update:regulation'),
  RegulationController.addVersion
);

router.put(
  '/regulations/:id/versions/:versionNumber',
  authenticate,
  authorize('update:regulation'),
  RegulationController.updateVersion
);

router.get(
  '/regulations/jurisdiction',
  authenticate,
  authorize('read:regulation'),
  RegulationController.findByJurisdiction
);

router.get(
  '/regulations/category/:category',
  authenticate,
  authorize('read:regulation'),
  RegulationController.findByCategory
);

router.get(
  '/regulations/applicability',
  authenticate,
  authorize('read:regulation'),
  RegulationController.findByApplicability
);

router.get(
  '/regulations/requirement/:requirementId',
  authenticate,
  authorize('read:regulation'),
  RegulationController.findByRequirementId
);

router.get(
  '/regulations/control-mapping/:frameworkId/:controlId',
  authenticate,
  authorize('read:regulation'),
  RegulationController.findByControlMapping
);

// Regulatory Change Routes
router.post(
  '/organizations/:organizationId/regulatory-changes',
  authenticate,
  authorize('create:regulatory-change'),
  RegulatoryChangeController.createRegulatoryChange
);

router.get(
  '/organizations/:organizationId/regulatory-changes',
  authenticate,
  authorize('read:regulatory-change'),
  RegulatoryChangeController.getAllRegulatoryChanges
);

router.get(
  '/regulatory-changes/:id',
  authenticate,
  authorize('read:regulatory-change'),
  RegulatoryChangeController.getRegulatoryChangeById
);

router.put(
  '/regulatory-changes/:id',
  authenticate,
  authorize('update:regulatory-change'),
  RegulatoryChangeController.updateRegulatoryChange
);

router.delete(
  '/regulatory-changes/:id',
  authenticate,
  authorize('delete:regulatory-change'),
  RegulatoryChangeController.deleteRegulatoryChange
);

router.post(
  '/regulatory-changes/:id/impact-assessment',
  authenticate,
  authorize('update:regulatory-change'),
  RegulatoryChangeController.addImpactAssessment
);

router.put(
  '/regulatory-changes/:id/implementation-status',
  authenticate,
  authorize('update:regulatory-change'),
  RegulatoryChangeController.updateImplementationStatus
);

router.post(
  '/regulatory-changes/:id/notifications',
  authenticate,
  authorize('update:regulatory-change'),
  RegulatoryChangeController.addNotification
);

router.put(
  '/regulatory-changes/:id/notifications/:notificationId',
  authenticate,
  authorize('update:regulatory-change'),
  RegulatoryChangeController.updateNotificationStatus
);

router.post(
  '/regulatory-changes/:id/workflows',
  authenticate,
  authorize('update:regulatory-change'),
  RegulatoryChangeController.addRelatedWorkflow
);

router.put(
  '/regulatory-changes/:id/workflows/:workflowId',
  authenticate,
  authorize('update:regulatory-change'),
  RegulatoryChangeController.updateRelatedWorkflowStatus
);

router.get(
  '/organizations/:organizationId/regulatory-changes/pending',
  authenticate,
  authorize('read:regulatory-change'),
  RegulatoryChangeController.findPending
);

router.get(
  '/organizations/:organizationId/regulatory-changes/overdue',
  authenticate,
  authorize('read:regulatory-change'),
  RegulatoryChangeController.findOverdue
);

router.get(
  '/organizations/:organizationId/regulatory-changes/upcoming',
  authenticate,
  authorize('read:regulatory-change'),
  RegulatoryChangeController.findUpcoming
);

router.get(
  '/organizations/:organizationId/regulatory-changes/impact/:level',
  authenticate,
  authorize('read:regulatory-change'),
  RegulatoryChangeController.findByImpactLevel
);

router.get(
  '/organizations/:organizationId/regulatory-changes/applicability',
  authenticate,
  authorize('read:regulatory-change'),
  RegulatoryChangeController.findByApplicability
);

// Framework Routes
router.post(
  '/frameworks',
  authenticate,
  authorize('create:framework'),
  FrameworkController.createFramework
);

router.post(
  '/organizations/:organizationId/frameworks',
  authenticate,
  authorize('create:framework'),
  FrameworkController.createFramework
);

router.get(
  '/frameworks',
  authenticate,
  authorize('read:framework'),
  FrameworkController.getAllFrameworks
);

router.get(
  '/organizations/:organizationId/frameworks',
  authenticate,
  authorize('read:framework'),
  FrameworkController.getAllFrameworks
);

router.get(
  '/frameworks/:id',
  authenticate,
  authorize('read:framework'),
  FrameworkController.getFrameworkById
);

router.put(
  '/frameworks/:id',
  authenticate,
  authorize('update:framework'),
  FrameworkController.updateFramework
);

router.delete(
  '/frameworks/:id',
  authenticate,
  authorize('delete:framework'),
  FrameworkController.deleteFramework
);

router.post(
  '/frameworks/:id/controls',
  authenticate,
  authorize('update:framework'),
  FrameworkController.addControl
);

router.put(
  '/frameworks/:id/controls/:controlId',
  authenticate,
  authorize('update:framework'),
  FrameworkController.updateControl
);

router.delete(
  '/frameworks/:id/controls/:controlId',
  authenticate,
  authorize('update:framework'),
  FrameworkController.removeControl
);

router.post(
  '/frameworks/:id/domains',
  authenticate,
  authorize('update:framework'),
  FrameworkController.addDomain
);

router.post(
  '/frameworks/:id/categories',
  authenticate,
  authorize('update:framework'),
  FrameworkController.addCategory
);

router.post(
  '/frameworks/:id/versions',
  authenticate,
  authorize('update:framework'),
  FrameworkController.addVersion
);

router.get(
  '/frameworks/type/:type',
  authenticate,
  authorize('read:framework'),
  FrameworkController.findByType
);

router.get(
  '/frameworks/category/:category',
  authenticate,
  authorize('read:framework'),
  FrameworkController.findByCategory
);

router.get(
  '/frameworks/jurisdiction',
  authenticate,
  authorize('read:framework'),
  FrameworkController.findByJurisdiction
);

router.get(
  '/frameworks/industry/:industry',
  authenticate,
  authorize('read:framework'),
  FrameworkController.findByIndustry
);

router.get(
  '/frameworks/control-mapping/:frameworkId/:controlId',
  authenticate,
  authorize('read:framework'),
  FrameworkController.findByControlMapping
);

router.get(
  '/frameworks/applicability',
  authenticate,
  authorize('read:framework'),
  FrameworkController.findByApplicability
);

router.get(
  '/organizations/:organizationId/frameworks/custom',
  authenticate,
  authorize('read:framework'),
  FrameworkController.findCustom
);

// Compliance Profile Routes
router.post(
  '/organizations/:organizationId/compliance-profiles',
  authenticate,
  authorize('create:compliance-profile'),
  ComplianceProfileController.createComplianceProfile
);

router.get(
  '/organizations/:organizationId/compliance-profiles',
  authenticate,
  authorize('read:compliance-profile'),
  ComplianceProfileController.getAllComplianceProfiles
);

router.get(
  '/compliance-profiles/:id',
  authenticate,
  authorize('read:compliance-profile'),
  ComplianceProfileController.getComplianceProfileById
);

router.put(
  '/compliance-profiles/:id',
  authenticate,
  authorize('update:compliance-profile'),
  ComplianceProfileController.updateComplianceProfile
);

router.delete(
  '/compliance-profiles/:id',
  authenticate,
  authorize('delete:compliance-profile'),
  ComplianceProfileController.deleteComplianceProfile
);

router.post(
  '/compliance-profiles/:id/frameworks',
  authenticate,
  authorize('update:compliance-profile'),
  ComplianceProfileController.addApplicableFramework
);

router.put(
  '/compliance-profiles/:id/frameworks/:frameworkId',
  authenticate,
  authorize('update:compliance-profile'),
  ComplianceProfileController.updateApplicableFramework
);

router.delete(
  '/compliance-profiles/:id/frameworks/:frameworkId',
  authenticate,
  authorize('update:compliance-profile'),
  ComplianceProfileController.removeApplicableFramework
);

router.post(
  '/compliance-profiles/:id/regulations',
  authenticate,
  authorize('update:compliance-profile'),
  ComplianceProfileController.addApplicableRegulation
);

router.post(
  '/compliance-profiles/:id/data-inventory',
  authenticate,
  authorize('update:compliance-profile'),
  ComplianceProfileController.addDataInventoryItem
);

router.post(
  '/compliance-profiles/:id/business-activities',
  authenticate,
  authorize('update:compliance-profile'),
  ComplianceProfileController.addBusinessActivity
);

router.post(
  '/compliance-profiles/:id/update-compliance-status',
  authenticate,
  authorize('update:compliance-profile'),
  ComplianceProfileController.updateOverallComplianceStatus
);

router.get(
  '/organizations/:organizationId/compliance-profiles/active',
  authenticate,
  authorize('read:compliance-profile'),
  ComplianceProfileController.findByOrganization
);

router.get(
  '/frameworks/:frameworkId/compliance-profiles',
  authenticate,
  authorize('read:compliance-profile'),
  ComplianceProfileController.findByFramework
);

router.get(
  '/regulations/:regulationId/compliance-profiles',
  authenticate,
  authorize('read:compliance-profile'),
  ComplianceProfileController.findByRegulation
);

router.get(
  '/compliance-profiles/status/:status',
  authenticate,
  authorize('read:compliance-profile'),
  ComplianceProfileController.findByComplianceStatus
);

module.exports = router;
