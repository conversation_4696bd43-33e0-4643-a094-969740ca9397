/**
 * Package Controller Tests
 */

const PackageController = require('../../../api/controllers/PackageController');
const FeatureFlagService = require('../../../api/services/FeatureFlagService');

// Mock the FeatureFlagService
jest.mock('../../../api/services/FeatureFlagService');

describe('PackageController', () => {
  let req, res, next;
  
  beforeEach(() => {
    // Mock request, response, and next
    req = {
      params: {},
      body: {}
    };
    
    res = {
      json: jest.fn(),
      status: jest.fn().mockReturnThis(),
      end: jest.fn()
    };
    
    next = jest.fn();
    
    // Mock FeatureFlagService methods
    FeatureFlagService.mockImplementation(() => ({
      getAllPackages: jest.fn().mockResolvedValue([
        { id: 'core', name: 'Core Package' },
        { id: 'enterprise', name: 'Enterprise Package' }
      ]),
      getPackageById: jest.fn().mockResolvedValue({
        id: 'enterprise',
        name: 'Enterprise Package',
        features: ['feature1', 'feature2', 'feature3'],
        limits: { connections: 100 }
      }),
      createPackage: jest.fn().mockResolvedValue({
        id: 'test-package',
        name: 'Test Package',
        features: ['test.feature1', 'test.feature2'],
        limits: { connections: 50 }
      }),
      updatePackage: jest.fn().mockResolvedValue({
        id: 'enterprise',
        name: 'Updated Enterprise Package',
        features: ['feature1', 'feature2', 'feature3'],
        limits: { connections: 100 }
      }),
      deletePackage: jest.fn().mockResolvedValue(true),
      getTenantPackage: jest.fn().mockResolvedValue({
        id: 'enterprise',
        name: 'Enterprise Package',
        features: ['feature1', 'feature2', 'feature3'],
        limits: { connections: 100 }
      }),
      setTenantPackage: jest.fn().mockResolvedValue({
        tenantId: 'test-tenant',
        packageId: 'enterprise',
        customFeatures: ['custom.feature1'],
        customLimits: { connections: 200 }
      }),
      clearCache: jest.fn()
    }));
  });
  
  afterEach(() => {
    jest.clearAllMocks();
  });
  
  test('getAllPackages should return all packages', async () => {
    await PackageController.getAllPackages(req, res, next);
    
    expect(res.json).toHaveBeenCalledWith([
      { id: 'core', name: 'Core Package' },
      { id: 'enterprise', name: 'Enterprise Package' }
    ]);
  });
  
  test('getPackageById should return package by ID', async () => {
    req.params.id = 'enterprise';
    
    await PackageController.getPackageById(req, res, next);
    
    expect(res.json).toHaveBeenCalledWith({
      id: 'enterprise',
      name: 'Enterprise Package',
      features: ['feature1', 'feature2', 'feature3'],
      limits: { connections: 100 }
    });
  });
  
  test('getPackageById should handle not found error', async () => {
    req.params.id = 'non-existent';
    
    // Mock getPackageById to throw not found error
    FeatureFlagService.mockImplementation(() => ({
      getPackageById: jest.fn().mockRejectedValue(new Error('Package with ID non-existent not found'))
    }));
    
    await PackageController.getPackageById(req, res, next);
    
    expect(res.status).toHaveBeenCalledWith(404);
    expect(res.json).toHaveBeenCalledWith({
      error: 'Not Found',
      message: 'Package with ID non-existent not found'
    });
  });
  
  test('createPackage should create a new package', async () => {
    req.body = {
      id: 'test-package',
      name: 'Test Package',
      tier: 'test',
      features: ['test.feature1', 'test.feature2'],
      limits: { connections: 50 }
    };
    
    await PackageController.createPackage(req, res, next);
    
    expect(res.status).toHaveBeenCalledWith(201);
    expect(res.json).toHaveBeenCalledWith({
      id: 'test-package',
      name: 'Test Package',
      features: ['test.feature1', 'test.feature2'],
      limits: { connections: 50 }
    });
  });
  
  test('createPackage should validate required fields', async () => {
    req.body = {
      name: 'Test Package'
      // Missing id, tier, features, limits
    };
    
    await PackageController.createPackage(req, res, next);
    
    expect(res.status).toHaveBeenCalledWith(400);
    expect(res.json).toHaveBeenCalledWith({
      error: 'Validation Error',
      message: 'Package ID is required'
    });
  });
  
  test('updatePackage should update a package', async () => {
    req.params.id = 'enterprise';
    req.body = {
      name: 'Updated Enterprise Package'
    };
    
    await PackageController.updatePackage(req, res, next);
    
    expect(res.json).toHaveBeenCalledWith({
      id: 'enterprise',
      name: 'Updated Enterprise Package',
      features: ['feature1', 'feature2', 'feature3'],
      limits: { connections: 100 }
    });
  });
  
  test('updatePackage should handle not found error', async () => {
    req.params.id = 'non-existent';
    req.body = {
      name: 'Updated Package'
    };
    
    // Mock updatePackage to throw not found error
    FeatureFlagService.mockImplementation(() => ({
      updatePackage: jest.fn().mockRejectedValue(new Error('Package with ID non-existent not found'))
    }));
    
    await PackageController.updatePackage(req, res, next);
    
    expect(res.status).toHaveBeenCalledWith(404);
    expect(res.json).toHaveBeenCalledWith({
      error: 'Not Found',
      message: 'Package with ID non-existent not found'
    });
  });
  
  test('deletePackage should delete a package', async () => {
    req.params.id = 'enterprise';
    
    await PackageController.deletePackage(req, res, next);
    
    expect(res.status).toHaveBeenCalledWith(204);
    expect(res.end).toHaveBeenCalled();
  });
  
  test('deletePackage should handle not found error', async () => {
    req.params.id = 'non-existent';
    
    // Mock deletePackage to throw not found error
    FeatureFlagService.mockImplementation(() => ({
      deletePackage: jest.fn().mockRejectedValue(new Error('Package with ID non-existent not found'))
    }));
    
    await PackageController.deletePackage(req, res, next);
    
    expect(res.status).toHaveBeenCalledWith(404);
    expect(res.json).toHaveBeenCalledWith({
      error: 'Not Found',
      message: 'Package with ID non-existent not found'
    });
  });
  
  test('getTenantPackage should return tenant package', async () => {
    req.params.tenantId = 'test-tenant';
    
    await PackageController.getTenantPackage(req, res, next);
    
    expect(res.json).toHaveBeenCalledWith({
      id: 'enterprise',
      name: 'Enterprise Package',
      features: ['feature1', 'feature2', 'feature3'],
      limits: { connections: 100 }
    });
  });
  
  test('setTenantPackage should set tenant package', async () => {
    req.params.tenantId = 'test-tenant';
    req.body = {
      packageId: 'enterprise',
      customFeatures: ['custom.feature1'],
      customLimits: { connections: 200 }
    };
    
    await PackageController.setTenantPackage(req, res, next);
    
    expect(res.json).toHaveBeenCalledWith({
      tenantId: 'test-tenant',
      packageId: 'enterprise',
      customFeatures: ['custom.feature1'],
      customLimits: { connections: 200 }
    });
  });
  
  test('setTenantPackage should validate required fields', async () => {
    req.params.tenantId = 'test-tenant';
    req.body = {
      // Missing packageId
      customFeatures: ['custom.feature1'],
      customLimits: { connections: 200 }
    };
    
    await PackageController.setTenantPackage(req, res, next);
    
    expect(res.status).toHaveBeenCalledWith(400);
    expect(res.json).toHaveBeenCalledWith({
      error: 'Validation Error',
      message: 'Package ID is required'
    });
  });
  
  test('clearCache should clear cache', async () => {
    await PackageController.clearCache(req, res, next);
    
    expect(res.json).toHaveBeenCalledWith({ message: 'Cache cleared successfully' });
  });
});
