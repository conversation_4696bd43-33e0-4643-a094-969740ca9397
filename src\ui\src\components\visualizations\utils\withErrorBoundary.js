import React, { Component } from 'react';
import { Box, Typography, Button, Paper } from '@mui/material';
import { ErrorOutline as ErrorIcon, Refresh as RefreshIcon } from '@mui/icons-material';
import analyticsService from '../../../services/visualizationAnalyticsService';

/**
 * Error Boundary component for visualizations
 */
class VisualizationErrorBoundary extends Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    // Log the error to the console
    console.error('Visualization Error:', error, errorInfo);
    
    // Update state with error info
    this.setState({ errorInfo });
    
    // Track error in analytics
    if (this.props.visualizationType) {
      analyticsService.trackError(
        this.props.visualizationType,
        error.message,
        {
          errorType: error.name,
          errorStack: error.stack,
          componentStack: errorInfo.componentStack
        }
      );
    }
  }

  handleRetry = () => {
    // Reset error state
    this.setState({ hasError: false, error: null, errorInfo: null });
  }

  render() {
    if (this.state.hasError) {
      // Render fallback UI
      return (
        <Paper
          sx={{
            p: 3,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            height: this.props.height || 400,
            width: this.props.width || '100%',
            backgroundColor: '#f8f9fa',
            borderRadius: 2
          }}
        >
          <ErrorIcon color="error" sx={{ fontSize: 48, mb: 2 }} />
          <Typography variant="h6" color="error" gutterBottom>
            Visualization Error
          </Typography>
          <Typography variant="body2" color="textSecondary" align="center" sx={{ mb: 2 }}>
            {this.state.error ? this.state.error.message : 'An error occurred while rendering the visualization.'}
          </Typography>
          <Button
            variant="contained"
            color="primary"
            startIcon={<RefreshIcon />}
            onClick={this.handleRetry}
          >
            Retry
          </Button>
          {this.props.onError && (
            <Button
              variant="text"
              color="primary"
              onClick={() => this.props.onError(this.state.error, this.state.errorInfo)}
              sx={{ mt: 1 }}
            >
              Report Issue
            </Button>
          )}
          {process.env.NODE_ENV === 'development' && this.state.errorInfo && (
            <Box sx={{ mt: 2, maxWidth: '100%', overflow: 'auto' }}>
              <Typography variant="caption" component="pre" sx={{ whiteSpace: 'pre-wrap' }}>
                {this.state.errorInfo.componentStack}
              </Typography>
            </Box>
          )}
        </Paper>
      );
    }

    // If no error, render children normally
    return this.props.children;
  }
}

/**
 * Higher-Order Component (HOC) that adds error boundary to visualizations
 * @param {React.Component} WrappedComponent - The component to wrap
 * @param {Object} options - Additional options for the HOC
 * @returns {React.Component} - The wrapped component with error boundary
 */
const withErrorBoundary = (WrappedComponent, options = {}) => {
  // Return a new component
  return function WithErrorBoundary(props) {
    // Get visualization type from options or props
    const visualizationType = options.visualizationType || props.visualizationType;
    
    return (
      <VisualizationErrorBoundary
        visualizationType={visualizationType}
        height={props.height}
        width={props.width}
        onError={props.onError}
      >
        <WrappedComponent {...props} />
      </VisualizationErrorBoundary>
    );
  };
};

export default withErrorBoundary;
