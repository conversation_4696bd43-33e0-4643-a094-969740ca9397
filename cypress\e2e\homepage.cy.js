describe('Homepage', () => {
  beforeEach(() => {
    cy.visit('/');
  });

  it('should display the main heading', () => {
    cy.get('h1').contains('NovaFuse API Superstore').should('be.visible');
  });

  it('should have navigation links', () => {
    cy.get('nav').should('be.visible');
    cy.get('nav').contains('Home').should('be.visible');
    cy.get('nav').contains('Products').should('be.visible');
    cy.get('nav').contains('Solutions').should('be.visible');
    cy.get('nav').contains('Partner Empowerment').should('be.visible');
    cy.get('nav').contains('Resources').should('be.visible');
    cy.get('nav').contains('About Us').should('be.visible');
    cy.get('nav').contains('Contact').should('be.visible');
  });

  it('should have sign in and sign up buttons', () => {
    cy.contains('Sign In').should('be.visible');
    cy.contains('Sign Up').should('be.visible');
  });

  it('should have a hero section with call-to-action', () => {
    cy.get('[data-testid="hero-section"]').should('be.visible');
    cy.get('[data-testid="hero-section"]').find('button').should('be.visible');
  });

  it('should have a features section', () => {
    cy.get('[data-testid="features-section"]').should('be.visible');
  });

  it('should have a testimonials section', () => {
    cy.get('[data-testid="testimonials-section"]').should('be.visible');
  });

  it('should have a footer with links', () => {
    cy.get('footer').should('be.visible');
    cy.get('footer').contains('Privacy Policy').should('be.visible');
    cy.get('footer').contains('Terms of Service').should('be.visible');
  });
});
