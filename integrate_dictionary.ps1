# Script to integrate the dictionary into the Master Archive

# Define paths
$sourceFile = "d:\\novafuse-api-superstore\\coherence-reality-systems\\imported-docs\\the-magnificent-seven\\documentation\\The Comphyological Dictionary 1st Edition.md"
$targetDir = "d:\\novafuse-api-superstore\\coherence-reality-systems\\Comphyology Master Archive\\3. APPENDIX A - MATH FOUNDATION"
$readmePath = "$targetDir\\README.md"

# 1. Copy the dictionary to the Math Foundation directory
$targetFile = "$targetDir\\The Comphyological Dictionary 1st Edition.md"
Copy-Item -Path $sourceFile -Destination $targetFile -Force

# 2. Update the README to include the dictionary
$readmeContent = Get-Content -Path $readmePath -Raw

# Check if the dictionary is already in the README
if ($readmeContent -notmatch "Comphyological Dictionary") {
    # Insert the dictionary entry after the Symbols Chart section
    $updatedReadme = $readmeContent -replace '(### 2\. Symbols Chart[\s\S]+?)(### 3\.)', "`$1
### 2.1 Comphyological Dictionary
- [The Comphyological Dictionary 1st Edition.md](./The%20Comphyological%20Dictionary%201st%20Edition.md)
  - Comprehensive reference of all Comphyology terms, concepts, and their interrelationships.

### 3."
    
    # Write the updated README back to disk
    [System.IO.File]::WriteAllText($readmePath, $updatedReadme.Trim())
}

Write-Host "Dictionary has been successfully integrated into the Master Archive!"
Write-Host "Location: $targetFile"
