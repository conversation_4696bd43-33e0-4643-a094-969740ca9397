#!/usr/bin/env python3
"""
UUFT Diffusion Model

This module simulates diffusion processes in social networks according to UUFT principles,
analyzing how 18/82 patterns and π-related relationships affect adoption dynamics.
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import networkx as nx
import os
import logging
import json
from collections import defaultdict
from uuft_social_analyzer import UUFTSocialNetwork

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('uuft_diffusion.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('UUFT_Diffusion')

# Constants
PATTERN_1882_RATIO = 18 / 82
PI = np.pi
PI_10_CUBED = PI * 10**3
RESULTS_DIR = "uuft_results/social"
os.makedirs(RESULTS_DIR, exist_ok=True)

class UUFTDiffusionModel:
    """
    Simulates diffusion processes in social networks according to UUFT principles.
    """

    def __init__(self, social_network, model_type="threshold", uuft_parameters=None):
        """
        Initialize the diffusion model.

        Args:
            social_network: UUFTSocialNetwork instance
            model_type: Type of diffusion model ("threshold", "independent_cascade", "uuft_optimized")
            uuft_parameters: Dictionary of UUFT-specific parameters
        """
        self.network = social_network
        self.model_type = model_type
        self.uuft_parameters = uuft_parameters or {
            "pi_influence": 0.5,  # Influence of π-related patterns
            "pattern_1882_strength": 0.7,  # Strength of 18/82 pattern enforcement
            "temporal_stability": 0.8  # Stability of patterns over time
        }

        # Initialize adoption state (0 = not adopted, 1 = adopted)
        self.adoption_state = np.zeros(social_network.num_nodes)

        # Initialize adoption time (when each node adopted, inf if not yet adopted)
        self.adoption_time = np.full(social_network.num_nodes, np.inf)

        # Initialize current time step
        self.current_time = 0

        # Initialize history of adoptions
        self.adoption_history = []

        logger.info(f"Initialized {model_type} diffusion model with UUFT parameters: {uuft_parameters}")

    def initialize_adopters(self, method="1882_optimized", initial_fraction=0.05):
        """
        Initialize the set of initial adopters.

        Args:
            method: Method for selecting initial adopters
                   ("random", "central", "influential", "1882_optimized")
            initial_fraction: Fraction of nodes to initialize as adopters
        """
        # Calculate number of initial adopters
        initial_count = int(self.network.num_nodes * initial_fraction)

        # Select initial adopters based on method
        if method == "random":
            # Random selection
            initial_adopters = np.random.choice(
                self.network.num_nodes,
                size=initial_count,
                replace=False
            )

        elif method == "central":
            # Select most central nodes
            centrality = nx.eigenvector_centrality(self.network.graph)
            sorted_nodes = sorted(centrality.keys(), key=lambda x: centrality[x], reverse=True)
            initial_adopters = sorted_nodes[:initial_count]

        elif method == "influential":
            # Select nodes with highest influence attribute
            influence = {node: attrs["influence"] for node, attrs in self.network.node_attributes.items()}
            sorted_nodes = sorted(influence.keys(), key=lambda x: influence[x], reverse=True)
            initial_adopters = sorted_nodes[:initial_count]

        elif method == "1882_optimized":
            # Select 18% of initial adopters from influencers, 82% from regular nodes
            influencers = [node for node, attrs in self.network.node_attributes.items()
                          if attrs["category"] == "influencer"]
            regular = [node for node, attrs in self.network.node_attributes.items()
                      if attrs["category"] == "regular"]

            # Calculate counts
            influencer_count = int(initial_count * 0.18)
            regular_count = initial_count - influencer_count

            # Select nodes
            if len(influencers) >= influencer_count:
                selected_influencers = np.random.choice(influencers, size=influencer_count, replace=False)
            else:
                selected_influencers = influencers

            if len(regular) >= regular_count:
                selected_regular = np.random.choice(regular, size=regular_count, replace=False)
            else:
                selected_regular = regular

            initial_adopters = np.concatenate([selected_influencers, selected_regular])

        else:
            raise ValueError(f"Unknown initialization method: {method}")

        # Set adoption state and time for initial adopters
        for node in initial_adopters:
            self.adoption_state[node] = 1
            self.adoption_time[node] = 0

        # Record initial state in history
        self.adoption_history.append({
            "time": 0,
            "new_adopters": list(initial_adopters),
            "total_adopters": len(initial_adopters),
            "adoption_rate": len(initial_adopters) / self.network.num_nodes
        })

        logger.info(f"Initialized {len(initial_adopters)} adopters using {method} method")

        return initial_adopters

    def step(self):
        """
        Perform one step of the diffusion process.

        Returns:
            Number of new adoptions in this step
        """
        self.current_time += 1

        if self.model_type == "threshold":
            new_adopters = self._threshold_step()
        elif self.model_type == "independent_cascade":
            new_adopters = self._cascade_step()
        elif self.model_type == "uuft_optimized":
            new_adopters = self._uuft_step()
        else:
            raise ValueError(f"Unknown model type: {self.model_type}")

        # Update adoption time for new adopters
        for node in new_adopters:
            self.adoption_time[node] = self.current_time

        # Record state in history
        self.adoption_history.append({
            "time": self.current_time,
            "new_adopters": list(new_adopters),
            "total_adopters": int(np.sum(self.adoption_state)),
            "adoption_rate": float(np.sum(self.adoption_state) / self.network.num_nodes)
        })

        return len(new_adopters)

    def _threshold_step(self):
        """
        Perform one step of threshold-based diffusion.

        In the threshold model, a node adopts if the fraction of its neighbors
        who have adopted exceeds its threshold.

        Returns:
            List of newly adopted nodes
        """
        new_adopters = []

        # Check each non-adopter
        for node in range(self.network.num_nodes):
            if self.adoption_state[node] == 0:
                # Get neighbors
                neighbors = list(self.network.graph.neighbors(node))

                if not neighbors:
                    continue

                # Calculate fraction of adopted neighbors
                adopted_neighbors = sum(self.adoption_state[neighbor] for neighbor in neighbors)
                fraction_adopted = adopted_neighbors / len(neighbors)

                # Check if threshold is exceeded
                threshold = self.network.node_attributes[node]["threshold"]

                if fraction_adopted >= threshold:
                    self.adoption_state[node] = 1
                    new_adopters.append(node)

        return new_adopters

    def _cascade_step(self):
        """
        Perform one step of independent cascade diffusion.

        In the cascade model, each newly activated node gets one chance to
        activate each of its inactive neighbors with a certain probability.

        Returns:
            List of newly adopted nodes
        """
        new_adopters = []

        # Get nodes that adopted in the previous time step
        if self.current_time == 1:
            # For the first step, use initial adopters
            previous_adopters = [node for node in range(self.network.num_nodes)
                               if self.adoption_time[node] == 0]
        else:
            previous_adopters = [node for node in range(self.network.num_nodes)
                               if self.adoption_time[node] == self.current_time - 1]

        # Each previous adopter attempts to influence neighbors
        for adopter in previous_adopters:
            # Get non-adopted neighbors
            neighbors = [node for node in self.network.graph.neighbors(adopter)
                        if self.adoption_state[node] == 0]

            # Attempt to influence each neighbor
            for neighbor in neighbors:
                # Calculate influence probability
                influence = self.network.node_attributes[adopter]["influence"]

                # Check if influence is successful
                if np.random.random() < influence:
                    self.adoption_state[neighbor] = 1
                    new_adopters.append(neighbor)

        return new_adopters

    def _uuft_step(self):
        """
        Perform one step of UUFT-optimized diffusion.

        This incorporates:
        - 18/82 influence patterns
        - π-related adoption thresholds
        - Temporal stability factors

        Returns:
            List of newly adopted nodes
        """
        new_adopters = []

        # Extract UUFT parameters
        pi_influence = self.uuft_parameters["pi_influence"]
        pattern_1882_strength = self.uuft_parameters["pattern_1882_strength"]
        temporal_stability = self.uuft_parameters["temporal_stability"]

        # Calculate current adoption pattern
        current_adoption_rate = np.sum(self.adoption_state) / self.network.num_nodes

        # Apply temporal stability - adjust influence based on pattern stability
        if self.current_time > 1 and len(self.adoption_history) > 1:
            # Calculate adoption rate change
            prev_rate = self.adoption_history[-1]["adoption_rate"]
            rate_change = abs(current_adoption_rate - prev_rate)

            # If change is too rapid, apply stabilizing factor
            if rate_change > 0.1:  # More than 10% change
                stability_factor = 1.0 - (rate_change - 0.1) * temporal_stability * 5
                stability_factor = max(0.5, min(1.0, stability_factor))  # Clamp to [0.5, 1.0]
            else:
                stability_factor = 1.0
        else:
            stability_factor = 1.0

        # Check each non-adopter
        for node in range(self.network.num_nodes):
            if self.adoption_state[node] == 0:
                # Get neighbors
                neighbors = list(self.network.graph.neighbors(node))

                if not neighbors:
                    continue

                # Calculate basic influence (combination of threshold and cascade models)
                # 1. Threshold component
                adopted_neighbors = sum(self.adoption_state[neighbor] for neighbor in neighbors)
                fraction_adopted = adopted_neighbors / len(neighbors)
                threshold = self.network.node_attributes[node]["threshold"]
                threshold_exceeded = fraction_adopted >= threshold

                # 2. Cascade component
                # Get neighbors that adopted in the previous time step
                recent_adopters = [n for n in neighbors if self.adoption_time[n] == self.current_time - 1]

                # Calculate combined influence from recent adopters
                cascade_influence = 0
                for adopter in recent_adopters:
                    influence = self.network.node_attributes[adopter]["influence"]

                    # Apply π-influence factor for nodes with π-related thresholds
                    if abs(threshold - PI/10) < 0.01:  # Check if threshold is π-related
                        influence *= (1.0 + pi_influence)

                    cascade_influence += influence

                # Cap cascade influence at 1.0
                cascade_influence = min(1.0, cascade_influence)

                # 3. Apply 18/82 pattern enforcement
                # Check if node is in the 18% influencer category
                is_influencer = self.network.node_attributes[node]["category"] == "influencer"

                # Calculate current adoption rates by category
                influencer_nodes = [n for n in range(self.network.num_nodes)
                                  if self.network.node_attributes[n]["category"] == "influencer"]
                regular_nodes = [n for n in range(self.network.num_nodes)
                               if self.network.node_attributes[n]["category"] == "regular"]

                influencer_adoption = sum(self.adoption_state[n] for n in influencer_nodes) / len(influencer_nodes)
                regular_adoption = sum(self.adoption_state[n] for n in regular_nodes) / len(regular_nodes)

                # Calculate target ratio based on 18/82 pattern
                if current_adoption_rate < 0.5:  # Early diffusion phase
                    # Influencers should adopt first (higher rate)
                    target_ratio = PATTERN_1882_RATIO  # influencer_rate / regular_rate should be high
                else:  # Later diffusion phase
                    # Regular nodes catch up
                    target_ratio = 1.0  # Equal adoption rates

                # Calculate current ratio
                if regular_adoption > 0:
                    current_ratio = influencer_adoption / regular_adoption
                else:
                    current_ratio = float('inf')

                # Apply pattern adjustment
                pattern_adjustment = 0
                if is_influencer and current_ratio < target_ratio:
                    # Boost influencer adoption to maintain pattern
                    pattern_adjustment = pattern_1882_strength * 0.2
                elif not is_influencer and current_ratio > target_ratio:
                    # Boost regular adoption to maintain pattern
                    pattern_adjustment = pattern_1882_strength * 0.2

                # 4. Combine all factors
                # Base probability from threshold model
                base_prob = 0.5 if threshold_exceeded else 0.1

                # Final adoption probability
                adoption_prob = (base_prob + cascade_influence + pattern_adjustment) * stability_factor

                # Check if adoption occurs
                if np.random.random() < adoption_prob:
                    self.adoption_state[node] = 1
                    new_adopters.append(node)

        return new_adopters

    def run_simulation(self, max_steps=100):
        """
        Run the complete diffusion simulation.

        Args:
            max_steps: Maximum number of steps to simulate

        Returns:
            DataFrame with adoption data over time
        """
        logger.info(f"Running diffusion simulation for up to {max_steps} steps")

        step_count = 0
        total_adoptions = int(np.sum(self.adoption_state))

        while step_count < max_steps and total_adoptions < self.network.num_nodes:
            new_adoptions = self.step()
            step_count += 1
            total_adoptions += new_adoptions

            # Log progress periodically
            if step_count % 10 == 0 or new_adoptions == 0:
                logger.info(f"Step {step_count}: {new_adoptions} new adoptions, {total_adoptions} total ({total_adoptions/self.network.num_nodes*100:.1f}%)")

            # Stop if no new adoptions
            if new_adoptions == 0:
                logger.info(f"Diffusion process reached equilibrium after {step_count} steps")
                break

        # Convert history to DataFrame
        df = pd.DataFrame(self.adoption_history)

        return df

    def get_adoption_curve(self):
        """Get the cumulative adoption curve over time."""
        if not self.adoption_history:
            return None

        times = [entry["time"] for entry in self.adoption_history]
        adoption_rates = [entry["adoption_rate"] for entry in self.adoption_history]

        return times, adoption_rates

    def visualize_diffusion(self, time_points=None, save_path=None):
        """
        Visualize the diffusion process at specified time points.

        Args:
            time_points: List of time points to visualize
            save_path: Path to save the visualization
        """
        if not self.adoption_history:
            logger.warning("No diffusion data to visualize")
            return

        # If time points not specified, use evenly spaced points
        if time_points is None:
            max_time = self.adoption_history[-1]["time"]
            time_points = [0, max(1, int(max_time * 0.25)), int(max_time * 0.5), int(max_time * 0.75), max_time]

        # Create figure with subplots
        fig, axes = plt.subplots(1, len(time_points), figsize=(5*len(time_points), 5))

        # If only one time point, wrap axes in list
        if len(time_points) == 1:
            axes = [axes]

        # Get network layout (consistent across subplots)
        pos = nx.spring_layout(self.network.graph)

        # Plot each time point
        for i, time in enumerate(time_points):
            ax = axes[i]

            # Find closest time point in history
            closest_entry = min(self.adoption_history, key=lambda x: abs(x["time"] - time))
            actual_time = closest_entry["time"]

            # Get adoption state at this time
            adoption_state = np.zeros(self.network.num_nodes)
            for entry in self.adoption_history:
                if entry["time"] <= actual_time:
                    for node in entry["new_adopters"]:
                        adoption_state[node] = 1

            # Create node colors based on adoption state
            node_colors = ['red' if adoption_state[node] == 1 else 'blue' for node in self.network.graph.nodes()]

            # Draw network
            nx.draw_networkx_nodes(self.network.graph, pos, node_size=50, node_color=node_colors, alpha=0.7, ax=ax)
            nx.draw_networkx_edges(self.network.graph, pos, alpha=0.2, ax=ax)

            # Add title
            adoption_rate = np.sum(adoption_state) / self.network.num_nodes
            ax.set_title(f"Time {actual_time}\nAdoption: {adoption_rate*100:.1f}%")

            # Remove axis
            ax.axis('off')

        # Add overall title
        fig.suptitle(f"{self.model_type.capitalize()} Diffusion Process", fontsize=16)
        plt.tight_layout()

        # Save if path provided
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Diffusion visualization saved to {save_path}")

        plt.close()

    def visualize_adoption_curve(self, save_path=None):
        """
        Visualize the adoption curve over time.

        Args:
            save_path: Path to save the visualization
        """
        if not self.adoption_history:
            logger.warning("No diffusion data to visualize")
            return

        plt.figure(figsize=(10, 6))

        # Get adoption curve
        times, adoption_rates = self.get_adoption_curve()

        # Plot curve
        plt.plot(times, [rate * 100 for rate in adoption_rates], 'b-', linewidth=2)

        # Add reference lines
        plt.axhline(y=18, color='r', linestyle='--', alpha=0.5, label='18%')
        plt.axhline(y=82, color='g', linestyle='--', alpha=0.5, label='82%')

        # Add labels and title
        plt.xlabel('Time Step')
        plt.ylabel('Adoption Rate (%)')
        plt.title(f"{self.model_type.capitalize()} Diffusion Process - Adoption Curve")
        plt.grid(True, alpha=0.3)
        plt.legend()

        # Save if path provided
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Adoption curve visualization saved to {save_path}")

        plt.close()

    def to_dict(self):
        """Convert diffusion model to dictionary for serialization."""
        return {
            "model_type": self.model_type,
            "uuft_parameters": self.uuft_parameters,
            "current_time": self.current_time,
            "adoption_state": self.adoption_state.tolist(),
            "adoption_time": [float(t) if not np.isinf(t) else "inf" for t in self.adoption_time],
            "adoption_history": self.adoption_history
        }

    @classmethod
    def from_dict(cls, data, social_network):
        """Create diffusion model from dictionary."""
        model = cls(
            social_network=social_network,
            model_type=data["model_type"],
            uuft_parameters=data["uuft_parameters"]
        )

        model.current_time = data["current_time"]
        model.adoption_state = np.array(data["adoption_state"])
        model.adoption_time = np.array([float(t) if t != "inf" else np.inf for t in data["adoption_time"]])
        model.adoption_history = data["adoption_history"]

        return model
