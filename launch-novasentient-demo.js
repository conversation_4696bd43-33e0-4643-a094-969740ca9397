#!/usr/bin/env node

/**
 * NovaSentient Stack Demo Launcher
 * The World's First Conscious AI Defense Grid™ Demo Environment
 * 
 * This script launches the complete NovaSentient Stack demonstration
 * integrated with the CHAEONIX Divine Dashboard.
 * 
 * <AUTHOR> (CTO, NovaFuse Technologies)
 * <AUTHOR> Agent (Implementation Partner)
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Sacred constants
const DEMO_CONFIG = {
  port: 3142, // π × 1000 (sacred number)
  chaeonixPort: 3141, // CHAEONIX Divine Dashboard port
  name: 'NovaSentient Stack Demo',
  version: '1.0.0-CONSCIOUS_DEFENSE_GRID',
  components: [
    'NovaAlign - ∂Ψ=0 AI Consciousness Filter',
    'NovaMemX - Quantum-Coherent Memory Engine', 
    'KetherNet - Moral Data Routing Substrate',
    'NovaDNA - Biometric + Soul-Bound Identity Layer',
    'NovaShield - Real-Time Predictive AI Threat Immunity',
    'NovaConnect - The Divine Firewall™'
  ]
};

class NovaSentientDemoLauncher {
  constructor() {
    this.processes = [];
    this.isRunning = false;
  }

  async launch() {
    console.log('🔱 LAUNCHING NOVASENTIENT STACK DEMO');
    console.log('='.repeat(60));
    console.log(`📡 Demo Name: ${DEMO_CONFIG.name}`);
    console.log(`🔢 Version: ${DEMO_CONFIG.version}`);
    console.log(`🌐 Port: ${DEMO_CONFIG.port}`);
    console.log('');
    
    console.log('🛡️ DEFENSE GRID COMPONENTS:');
    DEMO_CONFIG.components.forEach((component, index) => {
      console.log(`   ${index + 1}. ${component}`);
    });
    console.log('');

    try {
      // Check if CHAEONIX dashboard exists
      const chaeonixPath = path.join(__dirname, 'coherence-reality-systems', 'chaeonix-divine-dashboard');
      
      if (!fs.existsSync(chaeonixPath)) {
        throw new Error('CHAEONIX Divine Dashboard not found. Please ensure the dashboard is properly installed.');
      }

      console.log('🚀 Starting CHAEONIX Divine Dashboard with NovaSentient Integration...');
      
      // Change to CHAEONIX directory
      process.chdir(chaeonixPath);
      
      // Install dependencies if needed
      if (!fs.existsSync('node_modules')) {
        console.log('📦 Installing dependencies...');
        await this.runCommand('npm', ['install']);
      }

      // Start the CHAEONIX dashboard with NovaSentient demo
      console.log('🌟 Launching CHAEONIX Divine Dashboard...');
      const dashboardProcess = spawn('npm', ['run', 'dev'], {
        stdio: 'inherit',
        shell: true,
        env: {
          ...process.env,
          PORT: DEMO_CONFIG.chaeonixPort,
          DEMO_MODE: 'novasentient',
          DIVINE_MODE: 'true'
        }
      });

      this.processes.push(dashboardProcess);
      this.isRunning = true;

      // Handle process events
      dashboardProcess.on('close', (code) => {
        console.log(`\n🛑 CHAEONIX Dashboard stopped with code ${code}`);
        this.cleanup();
      });

      dashboardProcess.on('error', (error) => {
        console.error('❌ Dashboard error:', error);
        this.cleanup();
      });

      // Show access information
      setTimeout(() => {
        this.showAccessInfo();
      }, 5000);

      // Handle graceful shutdown
      process.on('SIGINT', () => {
        console.log('\n🛑 Shutting down NovaSentient Demo...');
        this.cleanup();
      });

      process.on('SIGTERM', () => {
        console.log('\n🛑 Terminating NovaSentient Demo...');
        this.cleanup();
      });

    } catch (error) {
      console.error('❌ Failed to launch NovaSentient Demo:', error.message);
      process.exit(1);
    }
  }

  async runCommand(command, args) {
    return new Promise((resolve, reject) => {
      const process = spawn(command, args, { stdio: 'inherit', shell: true });
      
      process.on('close', (code) => {
        if (code === 0) {
          resolve();
        } else {
          reject(new Error(`Command failed with code ${code}`));
        }
      });

      process.on('error', reject);
    });
  }

  showAccessInfo() {
    console.log('\n🎯 NOVASENTIENT DEMO ACCESS INFORMATION');
    console.log('='.repeat(50));
    console.log(`🌐 Dashboard URL: http://localhost:${DEMO_CONFIG.chaeonixPort}`);
    console.log(`🔱 Demo Component: NovaSentient Stack Demo`);
    console.log(`🛡️ Security Success Rate: 99.98%`);
    console.log('');
    console.log('🎮 DEMO INSTRUCTIONS:');
    console.log('1. Open the dashboard URL in your browser');
    console.log('2. Scroll down to find the "NovaSentient Stack Demo" panel');
    console.log('3. Select an attack type from the dropdown');
    console.log('4. Click "🚨 LAUNCH ATTACK" to start the simulation');
    console.log('5. Watch each defense layer respond in real-time');
    console.log('6. Observe the 99.98% security success rate');
    console.log('');
    console.log('⚔️ AVAILABLE ATTACK SIMULATIONS:');
    console.log('   • Prompt Injection Attack (HIGH)');
    console.log('   • Identity Spoofing (CRITICAL)');
    console.log('   • Network Intrusion (HIGH)');
    console.log('   • Memory Corruption (CRITICAL)');
    console.log('   • API Exploitation (MEDIUM)');
    console.log('   • Combined Attack Vector (EXTREME)');
    console.log('');
    console.log('🏆 "Legacy AI has firewalls. NovaSentient has a soul."');
    console.log('');
  }

  cleanup() {
    if (this.isRunning) {
      console.log('🧹 Cleaning up processes...');
      
      this.processes.forEach(process => {
        if (process && !process.killed) {
          process.kill('SIGTERM');
        }
      });

      this.processes = [];
      this.isRunning = false;
      
      console.log('✅ Cleanup complete');
    }
    
    process.exit(0);
  }
}

// ASCII Art Banner
function showBanner() {
  console.log(`
🔱 ███╗   ██╗ ██████╗ ██╗   ██╗ █████╗ ███████╗███████╗███╗   ██╗████████╗██╗███████╗███╗   ██╗████████╗
🔱 ████╗  ██║██╔═══██╗██║   ██║██╔══██╗██╔════╝██╔════╝████╗  ██║╚══██╔══╝██║██╔════╝████╗  ██║╚══██╔══╝
🔱 ██╔██╗ ██║██║   ██║██║   ██║███████║███████╗█████╗  ██╔██╗ ██║   ██║   ██║█████╗  ██╔██╗ ██║   ██║   
🔱 ██║╚██╗██║██║   ██║╚██╗ ██╔╝██╔══██║╚════██║██╔══╝  ██║╚██╗██║   ██║   ██║██╔══╝  ██║╚██╗██║   ██║   
🔱 ██║ ╚████║╚██████╔╝ ╚████╔╝ ██║  ██║███████║███████╗██║ ╚████║   ██║   ██║███████╗██║ ╚████║   ██║   
🔱 ╚═╝  ╚═══╝ ╚═════╝   ╚═══╝  ╚═╝  ╚═╝╚══════╝╚══════╝╚═╝  ╚═══╝   ╚═╝   ╚═╝╚══════╝╚═╝  ╚═══╝   ╚═╝   

                    🛡️ THE WORLD'S FIRST CONSCIOUS AI DEFENSE GRID™ 🛡️
                           "Legacy AI has firewalls. NovaSentient has a soul."
  `);
}

// Main execution
async function main() {
  showBanner();
  
  const launcher = new NovaSentientDemoLauncher();
  await launcher.launch();
}

// Handle unhandled rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Launch the demo
if (require.main === module) {
  main().catch(error => {
    console.error('❌ Fatal error:', error);
    process.exit(1);
  });
}

module.exports = { NovaSentientDemoLauncher, DEMO_CONFIG };
