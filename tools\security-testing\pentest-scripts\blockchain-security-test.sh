#!/bin/bash
# Blockchain Security Testing Script for NovaFuse
# This script tests blockchain-related security aspects of NovaFuse

# Configuration
TARGET_URL=${1:-"http://novafuse-api:3000"}
OUTPUT_DIR=${2:-"/pentest/reports"}
TIMESTAMP=$(date +%Y%m%d-%H%M%S)

# Create output directory
mkdir -p $OUTPUT_DIR

echo "==== NovaFuse Blockchain Security Scan ===="
echo "Target URL: $TARGET_URL"
echo "Output Directory: $OUTPUT_DIR"
echo "Timestamp: $TIMESTAMP"
echo "==========================================="

# Function to run a command and log its output
run_command() {
  local cmd="$1"
  local description="$2"
  local output_file="$3"
  
  echo "Running: $description"
  echo "Command: $cmd"
  
  eval "$cmd" | tee "$output_file"
  
  if [ ${PIPESTATUS[0]} -eq 0 ]; then
    echo "✅ $description completed successfully"
  else
    echo "❌ $description failed"
  fi
  
  echo "Output saved to: $output_file"
  echo "-----------------------------------"
}

# 1. Test blockchain evidence endpoints
echo "Testing blockchain evidence endpoints..."
EVIDENCE_ENDPOINTS=(
  "/api/v1/blockchain/evidence"
  "/api/v1/evidence"
  "/api/v1/blockchain/verify"
  "/api/v1/blockchain/transactions"
)

for endpoint in "${EVIDENCE_ENDPOINTS[@]}"; do
  # Test GET request
  curl -s -o "$OUTPUT_DIR/blockchain-get-$TIMESTAMP.json" -w "%{http_code}" "$TARGET_URL$endpoint"
  echo " - GET $endpoint"
  
  # Test POST request with empty payload
  curl -s -X POST -H "Content-Type: application/json" -d '{}' \
    -o "$OUTPUT_DIR/blockchain-post-empty-$TIMESTAMP.json" -w "%{http_code}" "$TARGET_URL$endpoint"
  echo " - POST $endpoint (empty payload)"
  
  # Test POST request with malformed payload
  curl -s -X POST -H "Content-Type: application/json" -d '{"malformed": true, "data": [1,2,3]}' \
    -o "$OUTPUT_DIR/blockchain-post-malformed-$TIMESTAMP.json" -w "%{http_code}" "$TARGET_URL$endpoint"
  echo " - POST $endpoint (malformed payload)"
done

# 2. Test for common blockchain vulnerabilities

# 2.1 Test for replay attacks
echo "Testing for replay vulnerability..."
# Get a valid transaction if possible
VALID_TX=$(curl -s "$TARGET_URL/api/v1/blockchain/transactions?limit=1" | grep -o '"hash":"[^"]*"' | head -1 | cut -d'"' -f4)

if [ ! -z "$VALID_TX" ]; then
  # Try to replay the transaction
  curl -s -X POST -H "Content-Type: application/json" \
    -d "{\"transaction\": \"$VALID_TX\"}" \
    -o "$OUTPUT_DIR/blockchain-replay-$TIMESTAMP.json" \
    "$TARGET_URL/api/v1/blockchain/submit"
  echo " - Attempted transaction replay with hash: $VALID_TX"
fi

# 2.2 Test for transaction malleability
echo "Testing for transaction malleability..."
curl -s -X POST -H "Content-Type: application/json" \
  -d '{"evidence": {"id": "test-id", "data": "test-data", "timestamp": "2023-01-01T00:00:00Z"}}' \
  -o "$OUTPUT_DIR/blockchain-tx1-$TIMESTAMP.json" \
  "$TARGET_URL/api/v1/blockchain/evidence"

curl -s -X POST -H "Content-Type: application/json" \
  -d '{"evidence": {"id": "test-id", "data": "test-data", "timestamp": "2023-01-01T00:00:00Z", "extra": "field"}}' \
  -o "$OUTPUT_DIR/blockchain-tx2-$TIMESTAMP.json" \
  "$TARGET_URL/api/v1/blockchain/evidence"

# 2.3 Test for front-running vulnerability
echo "Testing for front-running vulnerability..."
for i in {1..5}; do
  curl -s -X POST -H "Content-Type: application/json" \
    -d "{\"evidence\": {\"id\": \"concurrent-test-$i\", \"data\": \"test-data-$i\", \"timestamp\": \"$(date -Iseconds)\"}}" \
    -o "$OUTPUT_DIR/blockchain-concurrent-$i-$TIMESTAMP.json" \
    "$TARGET_URL/api/v1/blockchain/evidence" &
done
wait

# 3. Test for blockchain identity verification
echo "Testing blockchain identity verification..."
curl -s -X POST -H "Content-Type: application/json" \
  -d '{"id": "test-identity", "publicKey": "invalid-key"}' \
  -o "$OUTPUT_DIR/blockchain-identity-$TIMESTAMP.json" \
  "$TARGET_URL/api/v1/blockchain/verify-identity"

# Generate summary report
echo "Generating blockchain security report..."
cat > "$OUTPUT_DIR/blockchain-security-$TIMESTAMP.html" << EOF
<!DOCTYPE html>
<html>
<head>
  <title>NovaFuse Blockchain Security Test Report</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    h1 { color: #0A84FF; }
    .section { margin-bottom: 20px; }
    .result { margin-left: 20px; }
    .timestamp { color: #666; font-size: 0.8em; }
    .success { color: green; }
    .failure { color: red; }
  </style>
</head>
<body>
  <h1>NovaFuse Blockchain Security Test Report</h1>
  <div class="timestamp">Generated on: $(date)</div>
  
  <div class="section">
    <h2>Test Configuration</h2>
    <div class="result">Target URL: $TARGET_URL</div>
    <div class="result">Timestamp: $TIMESTAMP</div>
  </div>
  
  <div class="section">
    <h2>Blockchain Endpoint Tests</h2>
    <div class="result">GET Responses: <a href="blockchain-get-$TIMESTAMP.json">View Results</a></div>
    <div class="result">POST (Empty) Responses: <a href="blockchain-post-empty-$TIMESTAMP.json">View Results</a></div>
    <div class="result">POST (Malformed) Responses: <a href="blockchain-post-malformed-$TIMESTAMP.json">View Results</a></div>
  </div>
  
  <div class="section">
    <h2>Blockchain Vulnerability Tests</h2>
    <div class="result">Replay Attack Test: <a href="blockchain-replay-$TIMESTAMP.json">View Results</a></div>
    <div class="result">Transaction Malleability Test: 
      <a href="blockchain-tx1-$TIMESTAMP.json">Transaction 1</a> | 
      <a href="blockchain-tx2-$TIMESTAMP.json">Transaction 2</a>
    </div>
    <div class="result">Front-Running Test: 
      <a href="blockchain-concurrent-1-$TIMESTAMP.json">Request 1</a> | 
      <a href="blockchain-concurrent-2-$TIMESTAMP.json">Request 2</a> | 
      <a href="blockchain-concurrent-3-$TIMESTAMP.json">Request 3</a> | 
      <a href="blockchain-concurrent-4-$TIMESTAMP.json">Request 4</a> | 
      <a href="blockchain-concurrent-5-$TIMESTAMP.json">Request 5</a>
    </div>
    <div class="result">Identity Verification Test: <a href="blockchain-identity-$TIMESTAMP.json">View Results</a></div>
  </div>
</body>
</html>
EOF

echo "✅ Blockchain security testing completed"
echo "Report saved to: $OUTPUT_DIR/blockchain-security-$TIMESTAMP.html"
