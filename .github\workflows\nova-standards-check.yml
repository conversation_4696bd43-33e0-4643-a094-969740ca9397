name: Nova Standards Validation

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    # Run daily at 2 AM UTC
    - cron: '0 2 * * *'

jobs:
  nova-validation:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
        
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install networkx requests pytest prometheus-client pyjwt pydantic click jinja2 pyyaml
        
    - name: Run Nova Standards Validation
      run: |
        python tools/nova-cli/validate-standards.py . --validate
        
    - name: Run Component Health Assessment
      run: |
        python tools/nova-cli/component-health-monitor.py .
        
    - name: Run Dependency Mapping
      run: |
        python tools/nova-cli/dependency-mapper.py .
        
    - name: Update Manifest (on main branch)
      if: github.ref == 'refs/heads/main'
      run: |
        python tools/nova-cli/validate-standards.py . --update-manifest
        
    - name: Commit updated manifest
      if: github.ref == 'refs/heads/main'
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"
        git add NOVA_MANIFEST.md
        git diff --staged --quiet || git commit -m "🤖 Auto-update NOVA_MANIFEST.md [skip ci]"
        git push
        
    - name: Upload validation reports
      uses: actions/upload-artifact@v3
      with:
        name: nova-validation-reports
        path: |
          nova-dependencies.json
        retention-days: 30
        
    - name: Comment PR with validation results
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v6
      with:
        script: |
          const fs = require('fs');
          
          // Read validation results (if available)
          let comment = '## 🚀 Nova Standards Validation Results\n\n';
          comment += '✅ Standards validation completed successfully!\n\n';
          comment += '### Quick Summary:\n';
          comment += '- Component standards validation: ✅ Passed\n';
          comment += '- Health assessment: ✅ Completed\n';
          comment += '- Dependency mapping: ✅ Generated\n\n';
          comment += '📊 Detailed reports are available in the workflow artifacts.\n';
          
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: comment
          });

  nova-security-scan:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Run security scan on Nova components
      run: |
        echo "🔒 Running security scan on Nova components..."
        
        # Check for common security issues
        find src/ -name "*.py" -o -name "*.js" -o -name "*.ts" | xargs grep -l "password\|secret\|key" | head -10
        
        # Check for hardcoded credentials (basic check)
        if grep -r "password.*=" src/ --include="*.py" --include="*.js" --include="*.ts" | grep -v "password_field\|password_hash"; then
          echo "⚠️ Potential hardcoded passwords found"
          exit 1
        fi
        
        echo "✅ Basic security scan completed"

  nova-performance-check:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
        
    - name: Install performance testing dependencies
      run: |
        pip install pytest pytest-benchmark memory-profiler
        
    - name: Run performance tests
      run: |
        echo "⚡ Running Nova component performance checks..."
        
        # Check for large files that might impact performance
        find src/ -type f -size +1M -name "*.py" -o -name "*.js" -o -name "*.ts" | head -5
        
        # Count lines of code per component
        for dir in src/nova*/; do
          if [ -d "$dir" ]; then
            component=$(basename "$dir")
            lines=$(find "$dir" -name "*.py" -o -name "*.js" -o -name "*.ts" | xargs wc -l 2>/dev/null | tail -1 | awk '{print $1}')
            echo "📊 $component: $lines lines of code"
          fi
        done
        
        echo "✅ Performance check completed"

  nova-documentation-check:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Check documentation coverage
      run: |
        echo "📚 Checking Nova component documentation..."
        
        missing_readme=0
        total_components=0
        
        for dir in src/nova*/; do
          if [ -d "$dir" ]; then
            total_components=$((total_components + 1))
            component=$(basename "$dir")
            
            if [ ! -f "$dir/README.md" ]; then
              echo "❌ Missing README.md in $component"
              missing_readme=$((missing_readme + 1))
            else
              echo "✅ $component has README.md"
            fi
          fi
        done
        
        echo "📊 Documentation Summary:"
        echo "   Total components: $total_components"
        echo "   Missing README: $missing_readme"
        echo "   Documentation coverage: $(( (total_components - missing_readme) * 100 / total_components ))%"
        
        if [ $missing_readme -gt 0 ]; then
          echo "⚠️ Some components are missing documentation"
          # Don't fail the build, just warn
        fi

  nova-integration-test:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
        
    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        
    - name: Install dependencies
      run: |
        # Python dependencies
        pip install fastapi uvicorn requests pytest
        
        # Node.js dependencies (if package.json exists)
        if [ -f package.json ]; then
          npm install
        fi
        
    - name: Run Nova integration tests
      run: |
        echo "🔗 Running Nova component integration tests..."
        
        # Start a simple test server for each Python component
        for dir in src/nova*/; do
          if [ -d "$dir" ] && [ -f "$dir/main.py" ]; then
            component=$(basename "$dir")
            echo "🚀 Testing $component integration..."
            
            # Basic import test
            cd "$dir"
            python -c "import main; print('✅ $component imports successfully')" || echo "❌ $component import failed"
            cd - > /dev/null
          fi
        done
        
        echo "✅ Integration tests completed"

  nova-compliance-report:
    runs-on: ubuntu-latest
    needs: [nova-validation, nova-security-scan, nova-performance-check, nova-documentation-check]
    if: always()
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Generate compliance report
      run: |
        echo "📋 Generating Nova Compliance Report..."
        
        cat > nova-compliance-report.md << 'EOF'
        # Nova Compliance Report
        
        **Generated**: $(date)
        **Commit**: ${{ github.sha }}
        **Branch**: ${{ github.ref_name }}
        
        ## Validation Results
        
        | Check | Status | Details |
        |-------|--------|---------|
        | Standards Validation | ${{ needs.nova-validation.result == 'success' && '✅ Passed' || '❌ Failed' }} | Component standards compliance |
        | Security Scan | ${{ needs.nova-security-scan.result == 'success' && '✅ Passed' || '❌ Failed' }} | Security vulnerability check |
        | Performance Check | ${{ needs.nova-performance-check.result == 'success' && '✅ Passed' || '❌ Failed' }} | Performance metrics analysis |
        | Documentation Check | ${{ needs.nova-documentation-check.result == 'success' && '✅ Passed' || '❌ Failed' }} | Documentation coverage |
        
        ## Recommendations
        
        - Maintain regular validation runs
        - Address any failed checks promptly
        - Keep documentation up to date
        - Monitor component health metrics
        
        ---
        *This report was generated automatically by Nova CI/CD pipeline*
        EOF
        
        echo "✅ Compliance report generated"
        
    - name: Upload compliance report
      uses: actions/upload-artifact@v3
      with:
        name: nova-compliance-report
        path: nova-compliance-report.md
        retention-days: 90
