/**
 * Integration Registry Tests
 *
 * This file contains unit tests for the integration registry service.
 */

const { Integration, mockSave, mockRemove, mockFind, mockFindOne } = require('../../models');
const integrationRegistry = require('../../services/integrationRegistry');

// Mock the Integration model
jest.mock('../../models', () => {
  const mockSave = jest.fn().mockResolvedValue();
  const mockRemove = jest.fn().mockResolvedValue();
  const mockFind = jest.fn();
  const mockFindOne = jest.fn();

  class MockIntegration {
    constructor(data) {
      Object.assign(this, data);
    }

    save() {
      return mockSave();
    }

    remove() {
      return mockRemove();
    }
  }

  MockIntegration.find = mockFind;
  MockIntegration.findOne = mockFindOne;

  return {
    Integration: MockIntegration,
    mockSave,
    mockRemove,
    mockFind,
    mockFindOne
  };
});

describe('Integration Registry', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  describe('getAllIntegrations', () => {
    it('should return all integrations', async () => {
      const mockIntegrations = [
        { id: 'integration-1', name: 'Integration 1' },
        { id: 'integration-2', name: 'Integration 2' }
      ];

      Integration.find.mockResolvedValue(mockIntegrations);

      const result = await integrationRegistry.getAllIntegrations();

      expect(result).toEqual(mockIntegrations);
      expect(Integration.find).toHaveBeenCalled();
    });

    it('should handle errors', async () => {
      const error = new Error('Database error');
      Integration.find.mockRejectedValue(error);

      await expect(integrationRegistry.getAllIntegrations())
        .rejects
        .toThrow('Database error');
    });
  });

  describe('getIntegrationById', () => {
    it('should return an integration by ID', async () => {
      const mockIntegration = {
        id: 'integration-1',
        name: 'Integration 1'
      };

      Integration.findOne.mockResolvedValue(mockIntegration);

      const result = await integrationRegistry.getIntegrationById('integration-1');

      expect(result).toEqual(mockIntegration);
      expect(Integration.findOne).toHaveBeenCalledWith({ id: 'integration-1' });
    });

    it('should throw NotFoundError if integration is not found', async () => {
      Integration.findOne.mockResolvedValue(null);

      await expect(integrationRegistry.getIntegrationById('non-existent'))
        .rejects
        .toThrow("Integration with ID 'non-existent' not found");

      expect(Integration.findOne).toHaveBeenCalledWith({ id: 'non-existent' });
    });

    it('should handle other errors', async () => {
      const error = new Error('Database error');
      Integration.findOne.mockRejectedValue(error);

      await expect(integrationRegistry.getIntegrationById('integration-1'))
        .rejects
        .toThrow('Database error');
    });
  });

  describe('registerIntegration', () => {
    it('should register a new integration', async () => {
      const integrationData = {
        id: 'new-integration',
        name: 'New Integration',
        type: 'crm',
        capabilities: ['data-export', 'data-deletion']
      };

      // Mock that the integration doesn't already exist
      Integration.findOne.mockResolvedValue(null);

      // Mock the save method
      mockSave.mockResolvedValue(integrationData);

      const result = await integrationRegistry.registerIntegration(integrationData);

      expect(result).toHaveProperty('id', 'new-integration');
      expect(result).toHaveProperty('name', 'New Integration');
      expect(Integration.findOne).toHaveBeenCalledWith({ id: 'new-integration' });
      expect(mockSave).toHaveBeenCalled();
    });

    it('should throw ConflictError if integration already exists', async () => {
      const integrationData = {
        id: 'existing-integration',
        name: 'Existing Integration'
      };

      // Mock that the integration already exists
      Integration.findOne.mockResolvedValue(integrationData);

      await expect(integrationRegistry.registerIntegration(integrationData))
        .rejects
        .toThrow("Integration with ID 'existing-integration' already exists");

      expect(Integration.findOne).toHaveBeenCalledWith({ id: 'existing-integration' });
    });

    it('should handle validation errors', async () => {
      const integrationData = {
        id: 'invalid-integration'
      };

      // Mock that the integration doesn't already exist
      Integration.findOne.mockResolvedValue(null);

      // Mock the save method to throw a validation error
      const validationError = new Error('Validation error');
      validationError.name = 'ValidationError';

      mockSave.mockRejectedValue(validationError);

      await expect(integrationRegistry.registerIntegration(integrationData))
        .rejects
        .toThrow('Validation error');

      expect(mockSave).toHaveBeenCalled();
    });

    it('should handle other errors', async () => {
      const integrationData = {
        id: 'new-integration'
      };

      // Mock that the integration doesn't already exist
      Integration.findOne.mockResolvedValue(null);

      // Mock the save method to throw an error
      const error = new Error('Database error');

      mockSave.mockRejectedValue(error);

      await expect(integrationRegistry.registerIntegration(integrationData))
        .rejects
        .toThrow('Database error');

      expect(mockSave).toHaveBeenCalled();
    });
  });

  describe('updateIntegration', () => {
    it('should update an integration', async () => {
      const id = 'integration-1';
      const integrationData = {
        name: 'Updated Integration',
        type: 'marketing'
      };

      // Mock the existing integration
      const mockIntegration = {
        id,
        name: 'Integration 1',
        type: 'crm',
        save: mockSave,
        remove: mockRemove
      };

      // Mock getIntegrationById to return the existing integration
      Integration.findOne.mockResolvedValue(mockIntegration);
      mockSave.mockResolvedValue();

      const result = await integrationRegistry.updateIntegration(id, integrationData);

      // Check that the integration was updated
      expect(result.name).toBe('Updated Integration');
      expect(result.type).toBe('marketing');
      expect(result.id).toBe(id); // ID should not change
      expect(mockSave).toHaveBeenCalled();
    });

    it('should not update the ID field', async () => {
      const id = 'integration-1';
      const integrationData = {
        id: 'new-id',
        name: 'Updated Integration'
      };

      // Mock the existing integration
      const mockIntegration = {
        id,
        name: 'Integration 1',
        save: mockSave,
        remove: mockRemove
      };

      // Mock getIntegrationById to return the existing integration
      Integration.findOne.mockResolvedValue(mockIntegration);
      mockSave.mockResolvedValue();

      const result = await integrationRegistry.updateIntegration(id, integrationData);

      // Check that the ID was not updated
      expect(result.id).toBe(id);
      expect(result.name).toBe('Updated Integration');
      expect(mockSave).toHaveBeenCalled();
    });

    it('should handle NotFoundError', async () => {
      const id = 'non-existent';
      const integrationData = {
        name: 'Updated Integration'
      };

      // Mock getIntegrationById to throw NotFoundError
      Integration.findOne.mockResolvedValue(null);

      await expect(integrationRegistry.updateIntegration(id, integrationData))
        .rejects
        .toThrow("Integration with ID 'non-existent' not found");
    });

    it('should handle validation errors', async () => {
      const id = 'integration-1';
      const integrationData = {
        name: 'Updated Integration'
      };

      // Mock the existing integration
      const mockIntegration = {
        id,
        name: 'Integration 1',
        save: mockSave,
        remove: mockRemove
      };

      // Mock getIntegrationById to return the existing integration
      Integration.findOne.mockResolvedValue(mockIntegration);
      mockSave.mockRejectedValue(new Error('Validation error'));

      await expect(integrationRegistry.updateIntegration(id, integrationData))
        .rejects
        .toThrow('Validation error');
    });
  });

  describe('deleteIntegration', () => {
    it('should delete an integration', async () => {
      const id = 'integration-1';

      // Mock the existing integration
      const mockIntegration = {
        id,
        name: 'Integration 1',
        save: mockSave,
        remove: mockRemove
      };

      // Mock getIntegrationById to return the existing integration
      Integration.findOne.mockResolvedValue(mockIntegration);
      mockRemove.mockResolvedValue();

      await integrationRegistry.deleteIntegration(id);

      expect(mockRemove).toHaveBeenCalled();
    });

    it('should handle NotFoundError', async () => {
      const id = 'non-existent';

      // Mock getIntegrationById to throw NotFoundError
      Integration.findOne.mockResolvedValue(null);

      await expect(integrationRegistry.deleteIntegration(id))
        .rejects
        .toThrow("Integration with ID 'non-existent' not found");
    });

    it('should handle other errors', async () => {
      const id = 'integration-1';

      // Mock the existing integration
      const mockIntegration = {
        id,
        name: 'Integration 1',
        save: mockSave,
        remove: mockRemove
      };

      // Mock getIntegrationById to return the existing integration
      Integration.findOne.mockResolvedValue(mockIntegration);
      mockRemove.mockRejectedValue(new Error('Database error'));

      await expect(integrationRegistry.deleteIntegration(id))
        .rejects
        .toThrow('Database error');
    });
  });

  describe('configureIntegration', () => {
    it('should configure an integration', async () => {
      const id = 'integration-1';
      const config = {
        apiKey: 'test-api-key',
        endpoint: 'https://api.example.com'
      };

      // Mock the existing integration
      const mockIntegration = {
        id,
        name: 'Integration 1',
        config: {},
        status: 'inactive',
        save: mockSave,
        remove: mockRemove
      };

      // Mock getIntegrationById to return the existing integration
      Integration.findOne.mockResolvedValue(mockIntegration);
      mockSave.mockResolvedValue();

      const result = await integrationRegistry.configureIntegration(id, config);

      // Check that the integration was configured
      expect(result.config).toEqual(config);
      expect(result.status).toBe('active');
      expect(mockSave).toHaveBeenCalled();
    });

    it('should handle NotFoundError', async () => {
      const id = 'non-existent';
      const config = {
        apiKey: 'test-api-key'
      };

      // Mock getIntegrationById to throw NotFoundError
      Integration.findOne.mockResolvedValue(null);

      await expect(integrationRegistry.configureIntegration(id, config))
        .rejects
        .toThrow("Integration with ID 'non-existent' not found");
    });

    it('should handle validation errors', async () => {
      const id = 'integration-1';
      const config = {
        apiKey: 'test-api-key'
      };

      // Mock the existing integration
      const mockIntegration = {
        id,
        name: 'Integration 1',
        config: {},
        status: 'inactive',
        save: mockSave,
        remove: mockRemove
      };

      // Mock getIntegrationById to return the existing integration
      Integration.findOne.mockResolvedValue(mockIntegration);
      mockSave.mockRejectedValue(new Error('Validation error'));

      await expect(integrationRegistry.configureIntegration(id, config))
        .rejects
        .toThrow('Validation error');
    });
  });

  describe('supportsAction', () => {
    it('should return true if integration supports the action', async () => {
      const id = 'integration-1';
      const action = 'data-export';

      // Mock the existing integration
      const mockIntegration = {
        id,
        name: 'Integration 1',
        capabilities: ['data-export', 'data-deletion']
      };

      // Mock getIntegrationById to return the existing integration
      Integration.findOne.mockResolvedValue(mockIntegration);

      const result = await integrationRegistry.supportsAction(id, action);

      expect(result).toBe(true);
    });

    it('should return false if integration does not support the action', async () => {
      const id = 'integration-1';
      const action = 'data-processing';

      // Mock the existing integration
      const mockIntegration = {
        id,
        name: 'Integration 1',
        capabilities: ['data-export', 'data-deletion']
      };

      // Mock getIntegrationById to return the existing integration
      Integration.findOne.mockResolvedValue(mockIntegration);

      const result = await integrationRegistry.supportsAction(id, action);

      expect(result).toBe(false);
    });

    it('should handle NotFoundError', async () => {
      const id = 'non-existent';
      const action = 'data-export';

      // Mock getIntegrationById to throw NotFoundError
      Integration.findOne.mockResolvedValue(null);

      await expect(integrationRegistry.supportsAction(id, action))
        .rejects
        .toThrow("Integration with ID 'non-existent' not found");
    });
  });

  describe('getActionHandler', () => {
    it('should return the handler for a supported action', async () => {
      const id = 'integration-1';
      const action = 'data-export';
      const handler = {
        endpoint: '/export',
        method: 'POST'
      };

      // Mock the existing integration
      const mockIntegration = {
        id,
        name: 'Integration 1',
        capabilities: ['data-export', 'data-deletion'],
        handlers: {
          'data-export': handler
        }
      };

      // Mock getIntegrationById to return the existing integration
      Integration.findOne.mockResolvedValue(mockIntegration);

      const result = await integrationRegistry.getActionHandler(id, action);

      expect(result).toEqual(handler);
    });

    it('should throw ValidationError if action is not supported', async () => {
      const id = 'integration-1';
      const action = 'data-processing';

      // Mock the existing integration
      const mockIntegration = {
        id,
        name: 'Integration 1',
        capabilities: ['data-export', 'data-deletion'],
        handlers: {}
      };

      // Mock getIntegrationById to return the existing integration
      Integration.findOne.mockResolvedValue(mockIntegration);

      await expect(integrationRegistry.getActionHandler(id, action))
        .rejects
        .toThrow("Integration does not support action 'data-processing'");
    });

    it('should handle NotFoundError', async () => {
      const id = 'non-existent';
      const action = 'data-export';

      // Mock getIntegrationById to throw NotFoundError
      Integration.findOne.mockResolvedValue(null);

      await expect(integrationRegistry.getActionHandler(id, action))
        .rejects
        .toThrow("Integration with ID 'non-existent' not found");
    });
  });
});
