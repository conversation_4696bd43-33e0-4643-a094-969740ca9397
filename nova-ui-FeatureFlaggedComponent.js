/**
 * FeatureFlaggedComponent
 * 
 * A sample component that uses feature flags to conditionally render content.
 */

import { useFeatureFlag } from '../hooks/useFeatureFlag';

/**
 * AnalyticsPanel component
 * @returns {React.ReactNode} - Component JSX
 */
export function AnalyticsPanel() {
  const isAnalyticsEnabled = useFeatureFlag('dashboard', 'analytics');
  
  if (!isAnalyticsEnabled) {
    return (
      <div className="bg-gray-100 p-4 rounded-lg text-center">
        <h3 className="text-lg font-semibold text-gray-500">Analytics</h3>
        <p className="text-gray-500">This feature is not available in your current plan.</p>
        <button className="mt-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
          Upgrade to access Analytics
        </button>
      </div>
    );
  }
  
  return (
    <div className="bg-white p-4 rounded-lg shadow">
      <h3 className="text-lg font-semibold mb-4">Analytics Dashboard</h3>
      
      <div className="grid grid-cols-2 gap-4 mb-4">
        <div className="bg-blue-50 p-3 rounded">
          <h4 className="font-medium text-blue-700">Compliance Score</h4>
          <div className="flex items-center mt-2">
            <div className="text-2xl font-bold">87%</div>
            <div className="ml-2 text-green-500 text-sm">↑ 3%</div>
          </div>
        </div>
        
        <div className="bg-green-50 p-3 rounded">
          <h4 className="font-medium text-green-700">Controls Tested</h4>
          <div className="flex items-center mt-2">
            <div className="text-2xl font-bold">142/156</div>
            <div className="ml-2 text-green-500 text-sm">91%</div>
          </div>
        </div>
      </div>
      
      <div className="bg-gray-50 p-3 rounded mb-4">
        <h4 className="font-medium text-gray-700">Risk Trend</h4>
        <div className="h-40 mt-2 bg-gray-200 rounded flex items-center justify-center">
          {/* Placeholder for chart */}
          <span className="text-gray-500">Risk Trend Chart</span>
        </div>
      </div>
      
      <div className="flex justify-end">
        <button className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
          View Full Analytics
        </button>
      </div>
    </div>
  );
}

/**
 * AIAssistantPanel component
 * @returns {React.ReactNode} - Component JSX
 */
export function AIAssistantPanel() {
  const isAIAssistantEnabled = useFeatureFlag('advanced', 'aiAssistant');
  
  if (!isAIAssistantEnabled) {
    return (
      <div className="bg-gray-100 p-4 rounded-lg text-center">
        <h3 className="text-lg font-semibold text-gray-500">AI Assistant</h3>
        <p className="text-gray-500">This feature is not available in your current plan.</p>
        <button className="mt-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
          Upgrade to access AI Assistant
        </button>
      </div>
    );
  }
  
  return (
    <div className="bg-white p-4 rounded-lg shadow">
      <h3 className="text-lg font-semibold mb-4">NovaAssistAI</h3>
      
      <div className="bg-blue-50 p-3 rounded mb-4">
        <div className="flex items-start">
          <div className="bg-blue-500 text-white p-2 rounded-full">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <p className="text-blue-700">How can I help you with your GRC tasks today?</p>
          </div>
        </div>
      </div>
      
      <div className="mb-4">
        <h4 className="font-medium text-gray-700 mb-2">Quick Actions</h4>
        <div className="grid grid-cols-2 gap-2">
          <button className="p-2 bg-gray-100 rounded text-left hover:bg-gray-200">
            Generate compliance report
          </button>
          <button className="p-2 bg-gray-100 rounded text-left hover:bg-gray-200">
            Analyze security vulnerabilities
          </button>
          <button className="p-2 bg-gray-100 rounded text-left hover:bg-gray-200">
            Update privacy policy
          </button>
          <button className="p-2 bg-gray-100 rounded text-left hover:bg-gray-200">
            Schedule control testing
          </button>
        </div>
      </div>
      
      <div className="relative">
        <input
          type="text"
          placeholder="Ask NovaAssistAI a question..."
          className="w-full p-2 pr-10 border border-gray-300 rounded"
        />
        <button className="absolute right-2 top-2 text-blue-500">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.707l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13a1 1 0 102 0V9.414l1.293 1.293a1 1 0 001.414-1.414z" clipRule="evenodd" />
          </svg>
        </button>
      </div>
    </div>
  );
}

/**
 * GamificationPanel component
 * @returns {React.ReactNode} - Component JSX
 */
export function GamificationPanel() {
  const isGamificationEnabled = useFeatureFlag('learning', 'gamification');
  
  if (!isGamificationEnabled) {
    return (
      <div className="bg-gray-100 p-4 rounded-lg text-center">
        <h3 className="text-lg font-semibold text-gray-500">GRC Learning Games</h3>
        <p className="text-gray-500">This feature is not available in your current plan.</p>
        <button className="mt-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
          Upgrade to access Learning Games
        </button>
      </div>
    );
  }
  
  return (
    <div className="bg-white p-4 rounded-lg shadow">
      <h3 className="text-lg font-semibold mb-4">GRC Learning Games</h3>
      
      <div className="bg-purple-50 p-3 rounded mb-4">
        <div className="flex justify-between items-center">
          <h4 className="font-medium text-purple-700">Your Progress</h4>
          <div className="text-sm text-purple-700">Level 7</div>
        </div>
        <div className="mt-2 h-2 bg-purple-200 rounded-full">
          <div className="h-2 bg-purple-500 rounded-full w-3/4"></div>
        </div>
        <div className="mt-1 text-xs text-purple-700 text-right">750/1000 XP</div>
      </div>
      
      <div className="mb-4">
        <h4 className="font-medium text-gray-700 mb-2">Available Games</h4>
        <div className="space-y-2">
          <div className="p-3 border border-green-200 rounded bg-green-50">
            <div className="flex justify-between items-center">
              <h5 className="font-medium text-green-700">Privacy Protector</h5>
              <span className="text-xs bg-green-200 text-green-800 px-2 py-1 rounded">Completed</span>
            </div>
            <p className="text-sm text-green-600 mt-1">Protect personal data from threats in this interactive game.</p>
          </div>
          
          <div className="p-3 border border-blue-200 rounded bg-blue-50">
            <div className="flex justify-between items-center">
              <h5 className="font-medium text-blue-700">Compliance Quest</h5>
              <span className="text-xs bg-yellow-200 text-yellow-800 px-2 py-1 rounded">In Progress</span>
            </div>
            <p className="text-sm text-blue-600 mt-1">Navigate the complex world of regulatory compliance.</p>
          </div>
          
          <div className="p-3 border border-gray-200 rounded bg-gray-50">
            <div className="flex justify-between items-center">
              <h5 className="font-medium text-gray-700">Security Defender</h5>
              <span className="text-xs bg-gray-200 text-gray-800 px-2 py-1 rounded">Locked</span>
            </div>
            <p className="text-sm text-gray-600 mt-1">Defend your organization against cyber threats.</p>
          </div>
        </div>
      </div>
      
      <div className="flex justify-between">
        <button className="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300">
          View Leaderboard
        </button>
        <button className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600">
          Continue Playing
        </button>
      </div>
    </div>
  );
}

/**
 * Dashboard component that combines all feature-flagged components
 * @returns {React.ReactNode} - Component JSX
 */
export default function Dashboard() {
  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-6">NovaFuse Dashboard</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <AnalyticsPanel />
        <AIAssistantPanel />
        <GamificationPanel />
      </div>
    </div>
  );
}
