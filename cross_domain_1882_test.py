#!/usr/bin/env python3
"""
Cross-Domain 18/82 Pattern Test

This script tests whether the 18/82 pattern appears consistently across different domains
by analyzing sample datasets and measuring the alignment with the 18/82 ratio.
"""

import numpy as np
import matplotlib.pyplot as plt
import os
import json
from datetime import datetime
from scipy import stats

# Create results directory
RESULTS_DIR = "uuft_test_results"
os.makedirs(RESULTS_DIR, exist_ok=True)

# Constants
RATIO_18_82 = (0.18, 0.82)

def generate_sample_data():
    """
    Generate sample datasets for different domains.
    
    In a real test, this would use actual domain data.
    For this simple test, we'll generate synthetic data with varying degrees
    of alignment to the 18/82 pattern.
    
    Returns:
        dict: Sample datasets for different domains
    """
    print("Generating sample datasets for different domains")
    
    np.random.seed(42)  # For reproducibility
    
    # Cybersecurity data: network traffic distribution
    # (e.g., 18% of traffic accounts for 82% of security events)
    cybersecurity_data = {
        "traffic_volume": np.random.pareto(1.5, 1000),  # Pareto distribution often shows 18/82 pattern
        "security_events": np.random.pareto(1.2, 1000),
        "compliance_gaps": np.random.pareto(1.8, 1000)
    }
    
    # Medical data: disease prevalence and treatment efficacy
    medical_data = {
        "disease_prevalence": np.random.pareto(1.6, 1000),
        "treatment_efficacy": np.random.pareto(1.4, 1000),
        "patient_recovery_time": np.random.pareto(1.7, 1000)
    }
    
    # Financial data: market returns and investment distribution
    financial_data = {
        "market_returns": np.random.pareto(1.3, 1000),
        "investment_distribution": np.random.pareto(1.5, 1000),
        "trading_volume": np.random.pareto(1.4, 1000)
    }
    
    # Physics data: particle energy distribution and interaction frequency
    physics_data = {
        "particle_energy": np.random.pareto(1.6, 1000),
        "interaction_frequency": np.random.pareto(1.5, 1000),
        "field_strength": np.random.pareto(1.7, 1000)
    }
    
    # Random control data: should not show 18/82 pattern
    control_data = {
        "uniform_distribution": np.random.uniform(0, 1, 1000),
        "normal_distribution": np.random.normal(0, 1, 1000),
        "exponential_distribution": np.random.exponential(1, 1000)
    }
    
    return {
        "cybersecurity": cybersecurity_data,
        "medical": medical_data,
        "financial": financial_data,
        "physics": physics_data,
        "control": control_data
    }

def analyze_1882_pattern(data, domain_name):
    """
    Analyze a dataset for the presence of the 18/82 pattern.
    
    Args:
        data: Dictionary of datasets for a domain
        domain_name: Name of the domain
        
    Returns:
        dict: Analysis results
    """
    print(f"Analyzing {domain_name} data for 18/82 pattern")
    
    results = {
        "domain": domain_name,
        "datasets": {},
        "overall_alignment": 0.0
    }
    
    dataset_alignments = []
    
    for dataset_name, dataset in data.items():
        # Sort the data
        sorted_data = np.sort(dataset)
        total_sum = np.sum(sorted_data)
        
        # Find the cutoff point where the sum is closest to 82% of the total
        cumulative_sum = np.cumsum(sorted_data)
        target_sum = 0.82 * total_sum
        cutoff_idx = np.argmin(np.abs(cumulative_sum - target_sum))
        
        # Calculate the actual percentages
        top_count = len(sorted_data) - cutoff_idx
        bottom_count = cutoff_idx
        
        top_sum = np.sum(sorted_data[cutoff_idx:])
        bottom_sum = np.sum(sorted_data[:cutoff_idx])
        
        top_percent = top_sum / total_sum
        bottom_percent = bottom_sum / total_sum
        
        # Calculate alignment with 18/82 pattern
        alignment = 1.0 - (abs(bottom_percent - 0.18) + abs(top_percent - 0.82)) / 2
        
        # Store results
        results["datasets"][dataset_name] = {
            "top_percent": float(top_percent),
            "bottom_percent": float(bottom_percent),
            "top_count": int(top_count),
            "bottom_count": int(bottom_count),
            "total_count": len(sorted_data),
            "alignment": float(alignment),
            "is_1882_pattern": alignment > 0.9  # Consider it a match if alignment > 90%
        }
        
        dataset_alignments.append(alignment)
    
    # Calculate overall alignment for the domain
    results["overall_alignment"] = float(np.mean(dataset_alignments))
    results["is_1882_domain"] = results["overall_alignment"] > 0.9
    
    return results

def plot_results(all_results):
    """
    Plot the results of the 18/82 pattern analysis.
    
    Args:
        all_results: Results from analyze_1882_pattern() for all domains
    """
    # Plot overall domain alignments
    domains = list(all_results.keys())
    alignments = [all_results[d]["overall_alignment"] for d in domains]
    
    plt.figure(figsize=(10, 6))
    bars = plt.bar(domains, alignments, color=['blue' if a > 0.9 else 'gray' for a in alignments])
    
    # Add a horizontal line at 0.9 (threshold for 18/82 pattern)
    plt.axhline(y=0.9, color='r', linestyle='--', label='Threshold (90%)')
    
    plt.xlabel('Domain')
    plt.ylabel('Alignment with 18/82 Pattern')
    plt.title('Cross-Domain 18/82 Pattern Test: Overall Alignment by Domain')
    plt.ylim(0, 1)
    plt.legend()
    
    # Add alignment values on top of bars
    for bar, alignment in zip(bars, alignments):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                f'{alignment:.2f}', ha='center', va='bottom')
    
    plt.savefig(os.path.join(RESULTS_DIR, "cross_domain_1882_overall.png"))
    plt.close()
    
    # Plot dataset-specific alignments for each domain
    plt.figure(figsize=(12, 8))
    
    bar_positions = []
    bar_heights = []
    bar_colors = []
    bar_labels = []
    
    for i, domain in enumerate(domains):
        datasets = list(all_results[domain]["datasets"].keys())
        dataset_alignments = [all_results[domain]["datasets"][d]["alignment"] for d in datasets]
        
        for j, (dataset, alignment) in enumerate(zip(datasets, dataset_alignments)):
            bar_positions.append(i + j * 0.25)
            bar_heights.append(alignment)
            bar_colors.append('blue' if alignment > 0.9 else 'gray')
            bar_labels.append(f"{domain}_{dataset}")
    
    bars = plt.bar(bar_positions, bar_heights, width=0.2, color=bar_colors)
    
    # Add a horizontal line at 0.9 (threshold for 18/82 pattern)
    plt.axhline(y=0.9, color='r', linestyle='--', label='Threshold (90%)')
    
    plt.xlabel('Dataset')
    plt.ylabel('Alignment with 18/82 Pattern')
    plt.title('Cross-Domain 18/82 Pattern Test: Dataset-Specific Alignment')
    plt.xticks([i + 0.25 for i in range(len(domains))], domains)
    plt.ylim(0, 1)
    plt.legend()
    
    plt.savefig(os.path.join(RESULTS_DIR, "cross_domain_1882_datasets.png"))
    plt.close()

def save_results(all_results):
    """
    Save the results to a text file.
    
    Args:
        all_results: Results from analyze_1882_pattern() for all domains
    """
    with open(os.path.join(RESULTS_DIR, "cross_domain_1882_results.txt"), "w") as f:
        f.write("Cross-Domain 18/82 Pattern Test Results\n")
        f.write("=====================================\n")
        f.write(f"Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        # Write summary
        f.write("Summary:\n")
        for domain, results in all_results.items():
            f.write(f"  {domain.capitalize()}: Alignment = {results['overall_alignment']:.4f}, ")
            f.write(f"Is 18/82 Domain? {'Yes' if results['is_1882_domain'] else 'No'}\n")
        
        f.write("\n")
        
        # Write detailed results
        f.write("Detailed Results by Domain:\n")
        for domain, results in all_results.items():
            f.write(f"\n{domain.capitalize()}:\n")
            f.write(f"  Overall Alignment: {results['overall_alignment']:.4f}\n")
            f.write(f"  Is 18/82 Domain? {'Yes' if results['is_1882_domain'] else 'No'}\n")
            
            f.write("  Datasets:\n")
            for dataset, dataset_results in results["datasets"].items():
                f.write(f"    {dataset}:\n")
                f.write(f"      Top {dataset_results['top_count']}/{dataset_results['total_count']} items: {dataset_results['top_percent']*100:.2f}%\n")
                f.write(f"      Bottom {dataset_results['bottom_count']}/{dataset_results['total_count']} items: {dataset_results['bottom_percent']*100:.2f}%\n")
                f.write(f"      Alignment: {dataset_results['alignment']:.4f}\n")
                f.write(f"      Is 18/82 Pattern? {'Yes' if dataset_results['is_1882_pattern'] else 'No'}\n")

def main():
    """Run the cross-domain 18/82 pattern test."""
    print("Running Cross-Domain 18/82 Pattern Test")
    print("======================================")
    
    # Generate sample data
    sample_data = generate_sample_data()
    
    # Analyze each domain
    all_results = {}
    for domain, data in sample_data.items():
        all_results[domain] = analyze_1882_pattern(data, domain)
    
    # Plot the results
    plot_results(all_results)
    
    # Save the results
    save_results(all_results)
    
    print("\nTest complete. Results saved to the uuft_test_results directory.")
    
    # Count domains with 18/82 pattern
    domains_with_1882 = sum(1 for results in all_results.values() if results["is_1882_domain"])
    total_domains = len(all_results)
    
    print(f"\nDomains with 18/82 pattern: {domains_with_1882}/{total_domains}")
    
    # Check if control domain shows 18/82 pattern (it shouldn't)
    control_has_1882 = all_results["control"]["is_1882_domain"]
    print(f"Control domain has 18/82 pattern: {'Yes' if control_has_1882 else 'No'}")
    
    return all_results

if __name__ == "__main__":
    main()
