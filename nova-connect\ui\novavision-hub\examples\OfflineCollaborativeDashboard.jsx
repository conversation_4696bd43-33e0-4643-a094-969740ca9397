/**
 * Offline Collaborative Dashboard Example
 * 
 * This example demonstrates offline collaboration features.
 */

import React, { useState } from 'react';
import PropTypes from 'prop-types';
import {
  DashboardCard,
  TabPanel,
  SkipLink,
  ThemeSelector,
  OfflineIndicator,
  OfflineCollaborationLobby,
  OfflineCollaborationRoom
} from '../components';
import { ThemeProvider } from '../theme';
import { PreferencesProvider } from '../preferences';
import { OfflineProvider } from '../offline';
import { AnimationProvider } from '../animation';
import { I18nProvider } from '../i18n';
import { AccessibilityProvider } from '../accessibility';
import { SecurityProvider } from '../security';
import { MockSecurityService } from '../security';
import { AuthProvider } from '../auth';
import { OfflineCollaborationProvider } from '../collaboration/OfflineCollaborationContext';
import { Translation } from '../components';

/**
 * Offline Collaborative Dashboard Content component
 * 
 * @param {Object} props - Component props
 * @param {Object} props.novaConnect - NovaConnect instance
 * @param {Object} props.novaShield - NovaShield instance
 * @param {Object} props.novaTrack - NovaTrack instance
 * @param {boolean} [props.enableLogging=false] - Whether to enable logging
 * @returns {React.ReactElement} Offline Collaborative Dashboard Content component
 */
const OfflineCollaborativeDashboardContent = ({
  novaConnect,
  novaShield,
  novaTrack,
  enableLogging = false
}) => {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [currentRoomId, setCurrentRoomId] = useState(null);
  
  // Handle join room
  const handleJoinRoom = (roomId) => {
    setCurrentRoomId(roomId);
    setActiveTab('collaboration');
  };
  
  // Handle leave room
  const handleLeaveRoom = () => {
    setCurrentRoomId(null);
    setActiveTab('dashboard');
  };
  
  // Tabs
  const tabs = [
    {
      id: 'dashboard',
      label: <Translation id="dashboard.dashboard" defaultValue="Dashboard" />,
      content: (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <DashboardCard
            title={<Translation id="collaboration.offlineCollaboration" defaultValue="Offline Collaboration" />}
            subtitle={<Translation id="collaboration.workOffline" defaultValue="Work offline, sync when online" />}
          >
            <div className="p-4">
              <h3 className="text-lg font-medium text-textPrimary mb-4">
                <Translation id="collaboration.offlineFeatures" defaultValue="Offline Collaboration Features" />
              </h3>
              <p className="text-textSecondary mb-4">
                <Translation
                  id="collaboration.offlineFeaturesDescription"
                  defaultValue="This dashboard demonstrates offline collaboration features that allow users to continue working even when they lose internet connectivity."
                />
              </p>
              <ul className="list-disc list-inside space-y-2 text-textSecondary">
                <li>
                  <Translation id="collaboration.offlineMessaging" defaultValue="Offline Messaging" />
                </li>
                <li>
                  <Translation id="collaboration.automaticSync" defaultValue="Automatic Synchronization" />
                </li>
                <li>
                  <Translation id="collaboration.conflictResolution" defaultValue="Conflict Resolution" />
                </li>
                <li>
                  <Translation id="collaboration.pendingChanges" defaultValue="Pending Changes Tracking" />
                </li>
                <li>
                  <Translation id="collaboration.offlineRooms" defaultValue="Offline Room Creation" />
                </li>
                <li>
                  <Translation id="collaboration.cursorSharing" defaultValue="Cursor Sharing" />
                </li>
              </ul>
            </div>
          </DashboardCard>
          
          <DashboardCard
            title={<Translation id="collaboration.howItWorks" defaultValue="How It Works" />}
            subtitle={<Translation id="collaboration.technicalDetails" defaultValue="Technical Details" />}
          >
            <div className="p-4">
              <h3 className="text-lg font-medium text-textPrimary mb-4">
                <Translation id="collaboration.offlineSyncProcess" defaultValue="Offline Synchronization Process" />
              </h3>
              <p className="text-textSecondary mb-4">
                <Translation
                  id="collaboration.offlineSyncDescription"
                  defaultValue="The offline collaboration system uses a combination of local storage and change tracking to enable seamless offline-to-online transitions."
                />
              </p>
              
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium text-textPrimary mb-1">
                    <Translation id="collaboration.localStorage" defaultValue="1. Local Storage" />
                  </h4>
                  <p className="text-textSecondary">
                    <Translation
                      id="collaboration.localStorageDescription"
                      defaultValue="All collaboration data is stored locally using IndexedDB, allowing for persistence across browser sessions."
                    />
                  </p>
                </div>
                
                <div>
                  <h4 className="font-medium text-textPrimary mb-1">
                    <Translation id="collaboration.changeTracking" defaultValue="2. Change Tracking" />
                  </h4>
                  <p className="text-textSecondary">
                    <Translation
                      id="collaboration.changeTrackingDescription"
                      defaultValue="Changes made while offline are tracked in a queue and automatically synchronized when connectivity is restored."
                    />
                  </p>
                </div>
                
                <div>
                  <h4 className="font-medium text-textPrimary mb-1">
                    <Translation id="collaboration.conflictResolution" defaultValue="3. Conflict Resolution" />
                  </h4>
                  <p className="text-textSecondary">
                    <Translation
                      id="collaboration.conflictResolutionDescription"
                      defaultValue="When conflicts occur during synchronization, they are resolved using a last-writer-wins strategy with timestamp-based versioning."
                    />
                  </p>
                </div>
                
                <div>
                  <h4 className="font-medium text-textPrimary mb-1">
                    <Translation id="collaboration.networkDetection" defaultValue="4. Network Detection" />
                  </h4>
                  <p className="text-textSecondary">
                    <Translation
                      id="collaboration.networkDetectionDescription"
                      defaultValue="The system automatically detects network status changes and switches between online and offline modes accordingly."
                    />
                  </p>
                </div>
              </div>
            </div>
          </DashboardCard>
          
          <DashboardCard
            title={<Translation id="collaboration.joinCollaboration" defaultValue="Join Collaboration" />}
            subtitle={<Translation id="collaboration.startCollaborating" defaultValue="Start collaborating with others" />}
            className="md:col-span-2"
          >
            <div className="p-4">
              <p className="text-textSecondary mb-4">
                <Translation
                  id="collaboration.joinDescription"
                  defaultValue="Join an existing collaboration room or create a new one. You can continue collaborating even when offline."
                />
              </p>
              
              <div className="flex justify-center">
                <button
                  type="button"
                  className="px-4 py-2 bg-primary text-primaryContrast rounded-md hover:bg-primaryDark transition-colors duration-200"
                  onClick={() => setActiveTab('collaboration')}
                >
                  <Translation id="collaboration.goToCollaboration" defaultValue="Go to Collaboration" />
                </button>
              </div>
            </div>
          </DashboardCard>
        </div>
      )
    },
    {
      id: 'collaboration',
      label: <Translation id="collaboration.collaboration" defaultValue="Collaboration" />,
      content: (
        <div className="h-full">
          {currentRoomId ? (
            <OfflineCollaborationRoom
              roomId={currentRoomId}
              onLeave={handleLeaveRoom}
              className="h-full"
            />
          ) : (
            <OfflineCollaborationLobby
              onJoinRoom={handleJoinRoom}
            />
          )}
        </div>
      )
    },
    {
      id: 'help',
      label: <Translation id="common.help" defaultValue="Help" />,
      content: (
        <DashboardCard
          title={<Translation id="collaboration.offlineCollaborationHelp" defaultValue="Offline Collaboration Help" />}
          subtitle={<Translation id="collaboration.learnMore" defaultValue="Learn more about offline collaboration features" />}
        >
          <div className="p-4">
            <h3 className="text-lg font-medium text-textPrimary mb-4">
              <Translation id="collaboration.offlineMode" defaultValue="Offline Mode" />
            </h3>
            <p className="text-textSecondary mb-4">
              <Translation
                id="collaboration.offlineModeHelp"
                defaultValue="When you lose internet connectivity, the system automatically switches to offline mode. You can continue working as normal, and your changes will be synchronized when you reconnect."
              />
            </p>
            
            <h3 className="text-lg font-medium text-textPrimary mb-4">
              <Translation id="collaboration.pendingChanges" defaultValue="Pending Changes" />
            </h3>
            <p className="text-textSecondary mb-4">
              <Translation
                id="collaboration.pendingChangesHelp"
                defaultValue="Changes made while offline are tracked as pending changes. You can see the number of pending changes in the interface, and they will be automatically synchronized when you reconnect."
              />
            </p>
            
            <h3 className="text-lg font-medium text-textPrimary mb-4">
              <Translation id="collaboration.manualSync" defaultValue="Manual Synchronization" />
            </h3>
            <p className="text-textSecondary mb-4">
              <Translation
                id="collaboration.manualSyncHelp"
                defaultValue="You can manually trigger synchronization by clicking the sync button when you have pending changes and are online."
              />
            </p>
            
            <h3 className="text-lg font-medium text-textPrimary mb-4">
              <Translation id="collaboration.offlineIndicator" defaultValue="Offline Indicator" />
            </h3>
            <p className="text-textSecondary mb-4">
              <Translation
                id="collaboration.offlineIndicatorHelp"
                defaultValue="When you are offline, an offline indicator is displayed in the interface to let you know that you are working in offline mode."
              />
            </p>
            
            <h3 className="text-lg font-medium text-textPrimary mb-4">
              <Translation id="collaboration.troubleshooting" defaultValue="Troubleshooting" />
            </h3>
            <p className="text-textSecondary mb-4">
              <Translation
                id="collaboration.troubleshootingHelp"
                defaultValue="If you encounter issues with offline collaboration, try refreshing the page or clearing your browser cache. If problems persist, contact support."
              />
            </p>
          </div>
        </DashboardCard>
      )
    }
  ];
  
  return (
    <div className="space-y-6 h-full flex flex-col">
      {/* Skip link for keyboard navigation */}
      <SkipLink targetId="main-content" />
      
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-textPrimary" id="dashboard-title">
          <Translation id="collaboration.offlineCollaborativeDashboard" defaultValue="Offline Collaborative Dashboard" />
        </h1>
        
        <div className="flex items-center space-x-2">
          <OfflineIndicator />
          <ThemeSelector variant="dropdown" size="sm" />
        </div>
      </div>
      
      {/* Main content */}
      <main id="main-content" tabIndex="-1" className="flex-1">
        <TabPanel
          tabs={tabs}
          defaultTab="dashboard"
          variant="pills"
          onTabChange={setActiveTab}
          className="h-full"
        />
      </main>
    </div>
  );
};

OfflineCollaborativeDashboardContent.propTypes = {
  novaConnect: PropTypes.object,
  novaShield: PropTypes.object,
  novaTrack: PropTypes.object,
  enableLogging: PropTypes.bool
};

/**
 * Offline Collaborative Dashboard component
 * 
 * @param {Object} props - Component props
 * @param {Object} props.novaConnect - NovaConnect instance
 * @param {Object} props.novaShield - NovaShield instance
 * @param {Object} props.novaTrack - NovaTrack instance
 * @param {Object} [props.initialTheme] - Initial theme
 * @param {boolean} [props.enableSystemPreference=true] - Whether to enable system preference detection
 * @param {boolean} [props.enableLogging=false] - Whether to enable logging
 * @returns {React.ReactElement} Offline Collaborative Dashboard component
 */
const OfflineCollaborativeDashboard = ({
  novaConnect,
  novaShield,
  novaTrack,
  initialTheme,
  enableSystemPreference = true,
  enableLogging = false
}) => {
  // Create mock security service
  const mockSecurityService = new MockSecurityService();
  
  return (
    <ThemeProvider theme={initialTheme} enableSystemPreference={enableSystemPreference}>
      <I18nProvider detectBrowserLocale>
        <AccessibilityProvider>
          <AuthProvider>
            <SecurityProvider securityService={mockSecurityService}>
              <OfflineProvider>
                <OfflineCollaborationProvider enableLogging={enableLogging}>
                  <PreferencesProvider>
                    <AnimationProvider>
                      <OfflineCollaborativeDashboardContent
                        novaConnect={novaConnect}
                        novaShield={novaShield}
                        novaTrack={novaTrack}
                        enableLogging={enableLogging}
                      />
                    </AnimationProvider>
                  </PreferencesProvider>
                </OfflineCollaborationProvider>
              </OfflineProvider>
            </SecurityProvider>
          </AuthProvider>
        </AccessibilityProvider>
      </I18nProvider>
    </ThemeProvider>
  );
};

OfflineCollaborativeDashboard.propTypes = {
  novaConnect: PropTypes.object,
  novaShield: PropTypes.object,
  novaTrack: PropTypes.object,
  initialTheme: PropTypes.object,
  enableSystemPreference: PropTypes.bool,
  enableLogging: PropTypes.bool
};

export default OfflineCollaborativeDashboard;
