fe9f640b80b205d52a68d00dfbd42457
/**
 * NovaConnect Full System Test
 * 
 * This test validates the end-to-end performance of the NovaConnect system,
 * including data normalization, remediation workflows, and integration with
 * Google Cloud services.
 * 
 * NOTE: This test requires a running NovaConnect API server.
 * Set the SYSTEM_TEST_API_URL environment variable to point to the API server.
 */

const axios = require('axios');
const {
  performance
} = require('perf_hooks');
const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  apiUrl: process.env.SYSTEM_TEST_API_URL || 'http://localhost:3000',
  apiKey: process.env.SYSTEM_TEST_API_KEY || 'test-api-key',
  outputDir: path.join(__dirname, '../../test-results/system'),
  testDataDir: path.join(__dirname, '../data'),
  normalizationBatchSize: 100,
  remediationConcurrency: 10,
  maxConcurrentRequests: 50
};

// Ensure output directory exists
if (!fs.existsSync(config.outputDir)) {
  fs.mkdirSync(config.outputDir, {
    recursive: true
  });
}

// Create API client
const apiClient = axios.create({
  baseURL: config.apiUrl,
  headers: {
    'Authorization': `Bearer ${config.apiKey}`,
    'Content-Type': 'application/json'
  }
});
describe('NovaConnect Full System Test', () => {
  // Skip tests if API is not available
  let apiAvailable = false;
  beforeAll(async () => {
    try {
      // Check if API is available
      await apiClient.get('/health');
      apiAvailable = true;
    } catch (error) {
      console.warn(`API not available at ${config.apiUrl}. Skipping system tests.`);
    }
  });

  // Skip tests if API is not available
  const conditionalTest = apiAvailable ? it : it.skip;
  conditionalTest('should normalize data at high throughput', async () => {
    // Load test data
    const testDataPath = path.join(config.testDataDir, 'scc-findings.json');
    let testData;
    try {
      testData = JSON.parse(fs.readFileSync(testDataPath, 'utf8'));
    } catch (error) {
      // Generate test data if file doesn't exist
      testData = generateTestData(1000);
      fs.writeFileSync(testDataPath, JSON.stringify(testData, null, 2));
    }

    // Split data into batches
    const batches = [];
    for (let i = 0; i < testData.length; i += config.normalizationBatchSize) {
      batches.push(testData.slice(i, i + config.normalizationBatchSize));
    }
    console.log(`Normalizing ${testData.length} findings in ${batches.length} batches`);
    const startTime = performance.now();

    // Process each batch
    const results = [];
    for (const batch of batches) {
      const response = await apiClient.post('/api/transform/normalize', {
        source: 'scc',
        data: batch
      });
      results.push(response.data);
    }
    const endTime = performance.now();
    const duration = endTime - startTime;

    // Calculate metrics
    const totalFindings = results.reduce((sum, result) => sum + result.data.length, 0);
    const throughput = totalFindings / duration * 1000; // findings per second

    console.log(`Normalized ${totalFindings} findings in ${duration.toFixed(2)}ms`);
    console.log(`Throughput: ${throughput.toFixed(2)} findings/second`);
    console.log(`Average time per finding: ${(duration / totalFindings).toFixed(2)}ms`);

    // Verify performance
    expect(throughput).toBeGreaterThan(1000); // At least 1000 findings per second
    expect(duration / totalFindings).toBeLessThan(1); // Less than 1ms per finding

    // Write results to file
    const outputFile = path.join(config.outputDir, 'normalization-performance.json');
    fs.writeFileSync(outputFile, JSON.stringify({
      totalFindings,
      duration,
      throughput,
      averageTimePerFinding: duration / totalFindings
    }, null, 2));
  }, 60000);
  conditionalTest('should execute remediation workflows efficiently', async () => {
    // Load test data
    const testDataPath = path.join(config.testDataDir, 'remediation-scenarios.json');
    let testData;
    try {
      testData = JSON.parse(fs.readFileSync(testDataPath, 'utf8'));
    } catch (error) {
      // Generate test data if file doesn't exist
      testData = generateRemediationScenarios(100);
      fs.writeFileSync(testDataPath, JSON.stringify(testData, null, 2));
    }
    console.log(`Executing ${testData.length} remediation workflows`);
    const startTime = performance.now();

    // Execute remediation workflows with concurrency control
    const results = [];
    for (let i = 0; i < testData.length; i += config.remediationConcurrency) {
      const batch = testData.slice(i, i + config.remediationConcurrency);
      const promises = batch.map(scenario => apiClient.post('/api/remediate', scenario));
      const batchResults = await Promise.all(promises);
      results.push(...batchResults.map(response => response.data));
    }
    const endTime = performance.now();
    const duration = endTime - startTime;

    // Calculate metrics
    const totalSteps = results.reduce((sum, result) => sum + result.steps.length, 0);
    const successfulRemediations = results.filter(result => result.status === 'completed').length;
    const throughput = testData.length / duration * 60000; // remediations per minute

    console.log(`Executed ${testData.length} remediation workflows (${totalSteps} steps) in ${duration.toFixed(2)}ms`);
    console.log(`Success rate: ${(successfulRemediations / testData.length * 100).toFixed(2)}%`);
    console.log(`Throughput: ${throughput.toFixed(2)} remediations/minute`);
    console.log(`Average time per remediation: ${(duration / testData.length).toFixed(2)}ms`);

    // Verify performance
    expect(throughput).toBeGreaterThan(30); // At least 30 remediations per minute
    expect(duration / testData.length).toBeLessThan(8000); // Less than 8 seconds per remediation
    expect(successfulRemediations / testData.length).toBeGreaterThanOrEqual(0.95); // At least 95% success rate

    // Write results to file
    const outputFile = path.join(config.outputDir, 'remediation-performance.json');
    fs.writeFileSync(outputFile, JSON.stringify({
      totalRemediations: testData.length,
      successfulRemediations,
      totalSteps,
      duration,
      throughput,
      averageTimePerRemediation: duration / testData.length,
      successRate: successfulRemediations / testData.length
    }, null, 2));
  }, 300000);
  conditionalTest('should handle concurrent requests at scale', async () => {
    // Generate concurrent requests
    const concurrentRequests = config.maxConcurrentRequests;
    const requestsPerEndpoint = 10;
    const endpoints = [{
      path: '/health',
      method: 'get'
    }, {
      path: '/api/connectors',
      method: 'get'
    }, {
      path: '/api/transform/capabilities',
      method: 'get'
    }, {
      path: '/api/remediate/actions',
      method: 'get'
    }];
    const requests = [];
    for (const endpoint of endpoints) {
      for (let i = 0; i < requestsPerEndpoint; i++) {
        requests.push({
          path: endpoint.path,
          method: endpoint.method
        });
      }
    }
    console.log(`Executing ${requests.length} concurrent requests`);
    const startTime = performance.now();

    // Execute requests in batches to avoid overwhelming the server
    const results = [];
    for (let i = 0; i < requests.length; i += concurrentRequests) {
      const batch = requests.slice(i, i + concurrentRequests);
      const promises = batch.map(request => apiClient[request.method](request.path));
      try {
        const batchResults = await Promise.all(promises);
        results.push(...batchResults);
      } catch (error) {
        console.error(`Error executing batch: ${error.message}`);
      }
    }
    const endTime = performance.now();
    const duration = endTime - startTime;

    // Calculate metrics
    const successfulRequests = results.length;
    const throughput = successfulRequests / duration * 1000; // requests per second

    console.log(`Executed ${successfulRequests} requests in ${duration.toFixed(2)}ms`);
    console.log(`Throughput: ${throughput.toFixed(2)} requests/second`);
    console.log(`Average time per request: ${(duration / successfulRequests).toFixed(2)}ms`);

    // Verify performance
    expect(throughput).toBeGreaterThan(50); // At least 50 requests per second
    expect(successfulRequests).toBe(requests.length); // All requests should succeed

    // Write results to file
    const outputFile = path.join(config.outputDir, 'concurrency-performance.json');
    fs.writeFileSync(outputFile, JSON.stringify({
      totalRequests: requests.length,
      successfulRequests,
      duration,
      throughput,
      averageTimePerRequest: duration / successfulRequests
    }, null, 2));
  }, 60000);
  conditionalTest('should simulate peak load of 50K events', async () => {
    // For this test, we'll use a smaller batch and extrapolate
    // to avoid running an actual test with 50K events
    const batchSize = 500; // Use 500 items for the simulation

    // Generate batch payload
    const batchPayload = {
      source: 'scc',
      data: generateTestData(batchSize)
    };
    console.log(`Simulating peak load with ${batchSize} events`);
    const startTime = performance.now();

    // Process the batch
    const response = await apiClient.post('/api/transform/normalize', batchPayload);
    const endTime = performance.now();
    const duration = endTime - startTime;

    // Calculate metrics
    const throughput = batchSize / duration * 1000; // events per second
    const estimatedTimeFor50K = 50000 / throughput * 1000; // ms
    const estimatedTimeInMinutes = estimatedTimeFor50K / (1000 * 60); // minutes

    console.log(`Processed ${batchSize} events in ${duration.toFixed(2)}ms`);
    console.log(`Throughput: ${throughput.toFixed(2)} events/second`);
    console.log(`Estimated time to process 50,000 events: ${estimatedTimeInMinutes.toFixed(2)} minutes`);

    // Verify performance
    expect(throughput).toBeGreaterThan(1000); // At least 1000 events per second
    expect(estimatedTimeInMinutes).toBeLessThanOrEqual(15); // Less than 15 minutes for 50K events

    // Write results to file
    const outputFile = path.join(config.outputDir, 'peak-load-simulation.json');
    fs.writeFileSync(outputFile, JSON.stringify({
      batchSize,
      duration,
      throughput,
      estimatedTimeFor50K,
      estimatedTimeInMinutes
    }, null, 2));
  }, 60000);
  conditionalTest('should execute end-to-end breach remediation workflow', async () => {
    // Create a breach scenario
    const breachScenario = {
      id: `breach-${Date.now()}`,
      type: 'data_leak',
      severity: 'high',
      resource: {
        id: 'patient_records',
        type: 'bigquery.dataset',
        name: 'patient_records',
        provider: 'gcp',
        projectId: 'healthcare-demo'
      },
      finding: {
        id: `finding-${Date.now()}`,
        type: 'data_leak',
        severity: 'high',
        resourceName: 'projects/healthcare-demo/datasets/patient_records',
        resourceType: 'bigquery.dataset',
        createdAt: Date.now(),
        description: 'PHI data exposed in BigQuery dataset',
        dataType: 'PHI',
        complianceFrameworks: ['HIPAA', 'GDPR']
      },
      remediationSteps: [{
        id: 'step-1',
        action: 'encrypt-dataset',
        parameters: {
          projectId: 'healthcare-demo',
          datasetId: 'patient_records',
          encryptionType: 'AES-256',
          keyRotationPeriod: '90d'
        }
      }, {
        id: 'step-2',
        action: 'update-access-controls',
        parameters: {
          projectId: 'healthcare-demo',
          datasetId: 'patient_records',
          accessLevel: 'restricted',
          allowedRoles: ['healthcare-admin', 'compliance-officer']
        }
      }, {
        id: 'step-3',
        action: 'update-compliance-dashboard',
        parameters: {
          dashboardId: 'hipaa-compliance-dashboard',
          findingId: `finding-${Date.now()}`,
          remediationId: `breach-${Date.now()}`
        }
      }]
    };
    console.log('Executing end-to-end breach remediation workflow');
    const startTime = performance.now();

    // Execute the remediation
    const response = await apiClient.post('/api/remediate', breachScenario);
    const result = response.data;
    const endTime = performance.now();
    const duration = endTime - startTime;

    // Verify remediation result
    expect(result).toHaveProperty('id');
    expect(result).toHaveProperty('status');
    expect(result).toHaveProperty('steps');
    expect(result.steps.length).toBe(3);
    expect(result.steps.every(step => step.success)).toBe(true);
    console.log(`Executed breach remediation in ${duration.toFixed(2)}ms`);
    console.log(`Status: ${result.status}`);

    // Verify performance
    expect(duration).toBeLessThan(8000); // Less than 8 seconds

    // Write results to file
    const outputFile = path.join(config.outputDir, 'breach-remediation.json');
    fs.writeFileSync(outputFile, JSON.stringify({
      scenario: breachScenario,
      result,
      duration
    }, null, 2));
  }, 30000);
  conditionalTest('should generate system performance report', async () => {
    // Get system metrics
    const response = await apiClient.get('/api/metrics');
    const metrics = response.data;

    // Write metrics to file
    const outputFile = path.join(config.outputDir, 'system-metrics.json');
    fs.writeFileSync(outputFile, JSON.stringify(metrics, null, 2));

    // Generate summary report
    const summaryFile = path.join(config.outputDir, 'system-performance-summary.md');
    let summary = `# NovaConnect System Performance Summary\n\n`;
    summary += `Generated on: ${new Date().toISOString()}\n\n`;

    // Add transformation metrics
    if (metrics.transformation) {
      summary += `## Data Normalization Performance\n\n`;
      summary += `- Average normalization time: ${metrics.transformation.averageDuration.toFixed(2)}ms\n`;
      summary += `- Total transformations: ${metrics.transformation.transformations}\n`;
      summary += `- Batch transformations: ${metrics.transformation.batchTransformations}\n`;
      summary += `- Rules applied: ${metrics.transformation.rulesApplied}\n\n`;
    }

    // Add remediation metrics
    if (metrics.remediation) {
      summary += `## Remediation Performance\n\n`;
      summary += `- Total remediations: ${metrics.remediation.totalRemediations}\n`;
      summary += `- Successful remediations: ${metrics.remediation.successfulRemediations}\n`;
      summary += `- Failed remediations: ${metrics.remediation.failedRemediations}\n`;
      summary += `- Average remediation time: ${metrics.remediation.averageRemediationTime.toFixed(2)}ms\n`;
      summary += `- Total steps: ${metrics.remediation.totalSteps}\n`;
      summary += `- Successful steps: ${metrics.remediation.successfulSteps}\n`;
      summary += `- Failed steps: ${metrics.remediation.failedSteps}\n`;
      summary += `- Average step time: ${metrics.remediation.averageStepTime.toFixed(2)}ms\n\n`;
    }

    // Add system metrics
    if (metrics.system) {
      summary += `## System Performance\n\n`;
      summary += `- CPU usage: ${metrics.system.cpu.toFixed(2)}%\n`;
      summary += `- Memory usage: ${(metrics.system.memory / (1024 * 1024)).toFixed(2)} MB\n`;
      summary += `- Uptime: ${(metrics.system.uptime / (60 * 60)).toFixed(2)} hours\n`;
      summary += `- Active connections: ${metrics.system.connections}\n\n`;
    }

    // Add API metrics
    if (metrics.api) {
      summary += `## API Performance\n\n`;
      summary += `- Total requests: ${metrics.api.totalRequests}\n`;
      summary += `- Average response time: ${metrics.api.averageResponseTime.toFixed(2)}ms\n`;
      summary += `- Requests per second: ${metrics.api.requestsPerSecond.toFixed(2)}\n`;
      summary += `- Error rate: ${(metrics.api.errorRate * 100).toFixed(2)}%\n\n`;
    }

    // Write summary to file
    fs.writeFileSync(summaryFile, summary);
    console.log(`System performance report generated: ${summaryFile}`);
  }, 30000);
});

/**
 * Generate test data for SCC findings
 * @param {number} count - Number of findings to generate
 * @returns {Array} - Array of test findings
 */
function generateTestData(count) {
  const findings = [];
  for (let i = 0; i < count; i++) {
    findings.push({
      name: `organizations/123/sources/456/findings/finding-${i}`,
      parent: 'organizations/123/sources/456',
      resourceName: `//compute.googleapis.com/projects/test-project/zones/us-central1-a/instances/instance-${i}`,
      state: 'ACTIVE',
      category: ['VULNERABILITY', 'MISCONFIGURATION', 'THREAT'][i % 3],
      severity: ['HIGH', 'MEDIUM', 'LOW'][i % 3],
      eventTime: new Date().toISOString(),
      createTime: new Date().toISOString(),
      sourceProperties: {
        finding_type: ['Vulnerability', 'Misconfiguration', 'Threat'][i % 3],
        finding_id: `finding-${i}`,
        finding_description: `Mock finding ${i} description`
      }
    });
  }
  return findings;
}

/**
 * Generate test data for remediation scenarios
 * @param {number} count - Number of scenarios to generate
 * @returns {Array} - Array of test scenarios
 */
function generateRemediationScenarios(count) {
  const scenarios = [];
  for (let i = 0; i < count; i++) {
    scenarios.push({
      id: `scenario-${i}`,
      type: 'compliance',
      framework: ['HIPAA', 'PCI-DSS', 'GDPR'][i % 3],
      control: ['164.312(a)(1)', 'Requirement 3.4', 'Article 32'][i % 3],
      severity: ['high', 'medium', 'low'][i % 3],
      resource: {
        id: `resource-${i}`,
        type: ['compute.instance', 'storage.bucket', 'bigquery.dataset'][i % 3],
        name: `resource-${i}`,
        provider: 'gcp'
      },
      finding: {
        id: `finding-${i}`,
        type: ['vulnerability', 'misconfiguration', 'threat'][i % 3],
        severity: ['high', 'medium', 'low'][i % 3],
        resourceName: `projects/test-project/resources/resource-${i}`,
        resourceType: ['compute.instance', 'storage.bucket', 'bigquery.dataset'][i % 3],
        createdAt: Date.now(),
        description: `Mock finding ${i} description`
      },
      remediationSteps: [{
        id: `step-${i}-1`,
        action: ['update-firewall-rule', 'encrypt-bucket', 'update-access-controls'][i % 3],
        parameters: {
          resourceId: `resource-${i}`,
          action: 'restrict'
        }
      }, {
        id: `step-${i}-2`,
        action: 'generate-evidence',
        parameters: {
          findingId: `finding-${i}`,
          evidenceType: 'remediation'
        }
      }]
    });
  }
  return scenarios;
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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