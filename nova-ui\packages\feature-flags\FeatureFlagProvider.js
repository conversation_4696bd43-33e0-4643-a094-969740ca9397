/**
 * FeatureFlagProvider
 * 
 * A provider component that combines the ProductContext and FeatureFlags.
 */

import React from 'react';
import { ProductProvider } from './ProductContext';
import { featureFlags } from './index';

/**
 * FeatureFlagProvider component
 * @param {object} props - Component props
 * @param {React.ReactNode} props.children - Child components
 * @param {string} [props.initialProduct] - Initial product
 * @param {object} [props.customFlags] - Custom feature flags to override the defaults
 * @returns {React.ReactNode} - Provider component
 */
export function FeatureFlagProvider({ children, initialProduct, customFlags = {} }) {
  // Merge custom flags with default flags
  const mergedFlags = React.useMemo(() => {
    if (!customFlags || Object.keys(customFlags).length === 0) {
      return featureFlags;
    }
    
    const merged = { ...featureFlags };
    
    // Merge custom flags with default flags
    Object.keys(customFlags).forEach(product => {
      if (!merged[product]) {
        merged[product] = customFlags[product];
        return;
      }
      
      Object.keys(customFlags[product]).forEach(category => {
        if (!merged[product][category]) {
          merged[product][category] = customFlags[product][category];
          return;
        }
        
        merged[product][category] = {
          ...merged[product][category],
          ...customFlags[product][category]
        };
      });
    });
    
    return merged;
  }, [customFlags]);
  
  // Create a context for the merged flags
  const FeatureFlagContext = React.createContext(mergedFlags);
  
  return (
    <FeatureFlagContext.Provider value={mergedFlags}>
      <ProductProvider initialProduct={initialProduct}>
        {children}
      </ProductProvider>
    </FeatureFlagContext.Provider>
  );
}

export default FeatureFlagProvider;
