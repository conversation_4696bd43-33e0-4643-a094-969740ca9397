const express = require('express');
const axios = require('axios');
const cors = require('cors');
const bodyParser = require('body-parser');
const jp = require('jsonpath');
const winston = require('winston');

const app = express();
const port = process.env.PORT || 3000;

// Configure logger
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' })
  ]
});

// Middleware
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Configuration
const registryApiUrl = process.env.REGISTRY_API_URL || 'http://localhost:3001';
const authServiceUrl = process.env.AUTH_SERVICE_URL || 'http://localhost:3002';
const usageMeteringUrl = process.env.USAGE_METERING_URL || 'http://localhost:3004';

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok' });
});

// Execute connector endpoint
app.post('/execute/:connectorId/:endpointId', async (req, res) => {
  try {
    const { connectorId, endpointId } = req.params;
    const { credentialId, parameters = {} } = req.body;
    
    // Get connector from registry
    const connectorResponse = await axios.get(`${registryApiUrl}/connectors/${connectorId}`);
    const connector = connectorResponse.data;
    
    // Find the endpoint
    const endpoint = connector.endpoints.find(e => e.id === endpointId);
    if (!endpoint) {
      return res.status(404).json({ error: `Endpoint ${endpointId} not found` });
    }
    
    // Get credentials from auth service
    const credentialResponse = await axios.get(`${authServiceUrl}/credentials/${credentialId}/decrypt`);
    const { authType, credentials } = credentialResponse.data;
    
    // Execute the API call
    const apiResponse = await executeApiCall(connector, endpoint, authType, credentials, parameters);
    
    // Track usage
    try {
      await axios.post(`${usageMeteringUrl}/track`, {
        connectorId,
        endpointId,
        userId: req.body.userId || 'anonymous',
        timestamp: new Date().toISOString()
      });
    } catch (err) {
      logger.error('Error tracking usage', { error: err.message });
      // Don't fail the request if usage tracking fails
    }
    
    // Find mapping for this endpoint
    const mapping = connector.mappings.find(m => m.sourceEndpoint === endpointId);
    
    // Transform the response if mapping exists
    let result;
    if (mapping) {
      result = transformResponse(apiResponse.data, mapping);
    } else {
      result = apiResponse.data;
    }
    
    res.json({
      success: true,
      statusCode: apiResponse.status,
      data: result
    });
  } catch (err) {
    logger.error('Error executing connector', { error: err.message });
    
    if (err.response) {
      return res.status(err.response.status).json({
        error: 'API request failed',
        statusCode: err.response.status,
        message: err.response.data
      });
    }
    
    res.status(500).json({ error: 'Internal server error', message: err.message });
  }
});

// Execute API call
async function executeApiCall(connector, endpoint, authType, credentials, parameters) {
  // Prepare URL
  let url = connector.configuration.baseUrl + endpoint.path;
  
  // Replace path parameters
  if (endpoint.parameters.path && Object.keys(endpoint.parameters.path).length > 0) {
    for (const [key, value] of Object.entries(endpoint.parameters.path)) {
      const paramValue = parameters[key] || value.default;
      if (paramValue) {
        url = url.replace(`{${key}}`, encodeURIComponent(paramValue));
      }
    }
  }
  
  // Prepare query parameters
  const queryParams = {};
  if (endpoint.parameters.query && Object.keys(endpoint.parameters.query).length > 0) {
    for (const [key, value] of Object.entries(endpoint.parameters.query)) {
      const paramValue = parameters[key] || value.default;
      if (paramValue !== undefined) {
        queryParams[key] = paramValue;
      }
    }
  }
  
  // Prepare headers
  const headers = { ...connector.configuration.headers };
  
  // Add authentication headers
  switch (authType) {
    case 'API_KEY':
      if (credentials.header) {
        headers[credentials.header] = credentials.apiKey;
      } else if (credentials.query) {
        queryParams[credentials.query] = credentials.apiKey;
      }
      break;
    case 'BASIC':
      headers['Authorization'] = `Basic ${Buffer.from(`${credentials.username}:${credentials.password}`).toString('base64')}`;
      break;
    case 'OAUTH2':
      headers['Authorization'] = `Bearer ${credentials.accessToken}`;
      break;
    case 'JWT':
      headers['Authorization'] = `Bearer ${credentials.token}`;
      break;
    case 'AWS_SIG_V4':
      // In a real implementation, we would use AWS SDK to sign the request
      headers['X-Amz-Security-Token'] = 'mock-aws-signature';
      break;
    default:
      break;
  }
  
  // Prepare request body
  let data = undefined;
  if (endpoint.method !== 'GET' && endpoint.parameters.body && Object.keys(endpoint.parameters.body).length > 0) {
    data = {};
    for (const [key, value] of Object.entries(endpoint.parameters.body)) {
      const paramValue = parameters[key] || value.default;
      if (paramValue !== undefined) {
        data[key] = paramValue;
      }
    }
  }
  
  // Execute the request
  const config = {
    method: endpoint.method,
    url,
    headers,
    params: queryParams,
    data,
    timeout: connector.configuration.timeout || 30000
  };
  
  logger.info('Executing API call', { config: { ...config, headers: '***' } });
  
  return await axios(config);
}

// Transform response based on mapping
function transformResponse(data, mapping) {
  const result = {
    targetSystem: mapping.targetSystem,
    targetEntity: mapping.targetEntity,
    data: {}
  };
  
  for (const transformation of mapping.transformations) {
    try {
      let value;
      
      // Extract value from source using JSONPath
      if (transformation.source.startsWith('$')) {
        value = jp.query(data, transformation.source);
        if (Array.isArray(value) && value.length === 1) {
          value = value[0];
        }
      } else {
        value = transformation.source;
      }
      
      // Apply transformation function
      switch (transformation.transform) {
        case 'identity':
          // No transformation needed
          break;
        case 'formatDate':
          if (value) {
            value = new Date(value).toISOString();
          }
          break;
        case 'mapComplianceStatus':
          value = mapComplianceStatus(value);
          break;
        case 'mapSeverityToRisk':
          value = mapSeverityToRisk(value);
          break;
        default:
          // No transformation
          break;
      }
      
      // Set the value in the result
      result.data[transformation.target] = value;
    } catch (err) {
      logger.error('Error applying transformation', {
        transformation,
        error: err.message
      });
    }
  }
  
  return result;
}

// Transformation functions
function mapComplianceStatus(status) {
  const statusMap = {
    'PASSED': 'Compliant',
    'FAILED': 'Non-Compliant',
    'WARNING': 'Warning',
    'NOT_AVAILABLE': 'Not Applicable'
  };
  
  return statusMap[status] || status;
}

function mapSeverityToRisk(severity) {
  const severityMap = {
    'CRITICAL': 'Critical',
    'HIGH': 'High',
    'MEDIUM': 'Medium',
    'LOW': 'Low',
    'INFORMATIONAL': 'Info'
  };
  
  return severityMap[severity] || severity;
}

// Start the server
app.listen(port, () => {
  logger.info(`Connector Executor service running on port ${port}`);
});
