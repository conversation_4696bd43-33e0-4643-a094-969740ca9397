/**
 * Basic RBAC Test Script
 * 
 * This script tests the basic functionality of the RBAC system.
 */

console.log('Starting basic RBAC test...');

// Mock the CacheService
const mockCacheService = {
  async get(key) {
    return null;
  },
  async set(key, value, ttl) {
    return true;
  },
  async delete(key) {
    return true;
  },
  async keys(pattern) {
    return [];
  },
  async flush(prefix) {
    return true;
  }
};

// Mock the logger
const mockLogger = {
  info: console.log,
  error: console.error,
  warn: console.warn,
  debug: console.log
};

// Mock mongoose
const mockMongoose = {
  Schema: class Schema {
    constructor(definition) {
      this.definition = definition;
    }
  },
  model: (name, schema) => {
    return class MockModel {
      static async find() {
        return [];
      }
      static async findById() {
        return null;
      }
      static async findOne() {
        return null;
      }
      static async exists() {
        return false;
      }
      static async deleteOne() {
        return { deletedCount: 1 };
      }
      constructor(data) {
        Object.assign(this, data);
        this._id = Math.random().toString(36).substring(7);
      }
      async save() {
        return this;
      }
    };
  },
  Types: {
    ObjectId: {
      isValid: () => true
    }
  }
};

// Mock the models
const mockRole = mockMongoose.model('Role');
const mockPermission = mockMongoose.model('Permission');
const mockUserRole = mockMongoose.model('UserRole');
const mockUser = mockMongoose.model('User');

// Create a simple RBAC service
class RBACService {
  constructor() {
    this.roles = [];
    this.permissions = [];
    this.userRoles = [];
    this.users = [];
    
    // Cache keys
    this.CACHE_KEYS = {
      USER_PERMISSIONS: 'rbac:user-permissions:',
      USER_PERMISSION: 'rbac:permission:',
      ALL_ROLES: 'rbac:all-roles',
      ALL_PERMISSIONS: 'rbac:all-permissions'
    };
  }
  
  async getAllRoles() {
    console.log('Getting all roles...');
    return this.roles;
  }
  
  async getRoleById(roleId) {
    console.log(`Getting role by ID: ${roleId}`);
    const role = this.roles.find(r => r._id === roleId);
    if (!role) {
      throw new Error(`Role with ID ${roleId} not found`);
    }
    return role;
  }
  
  async createRole(data) {
    console.log(`Creating role: ${data.name}`);
    const role = {
      _id: Math.random().toString(36).substring(7),
      name: data.name,
      description: data.description || '',
      permissions: data.permissions || [],
      isSystem: data.isSystem || false,
      isDefault: data.isDefault || false,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    this.roles.push(role);
    return role;
  }
  
  async getAllPermissions() {
    console.log('Getting all permissions...');
    return this.permissions;
  }
  
  async createPermission(data) {
    console.log(`Creating permission: ${data.resource}:${data.action}`);
    const permission = {
      _id: Math.random().toString(36).substring(7),
      name: data.name,
      description: data.description || '',
      resource: data.resource,
      action: data.action,
      isSystem: data.isSystem || false,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    this.permissions.push(permission);
    return permission;
  }
  
  async assignRoleToUser(userId, roleId) {
    console.log(`Assigning role ${roleId} to user ${userId}`);
    const userRole = {
      _id: Math.random().toString(36).substring(7),
      user: userId,
      role: roleId,
      createdAt: new Date()
    };
    this.userRoles.push(userRole);
    return { success: true, message: `Role assigned to user ${userId}` };
  }
  
  async removeRoleFromUser(userId, roleId) {
    console.log(`Removing role ${roleId} from user ${userId}`);
    const index = this.userRoles.findIndex(ur => ur.user === userId && ur.role === roleId);
    if (index !== -1) {
      this.userRoles.splice(index, 1);
    }
    return { success: true, message: `Role removed from user ${userId}` };
  }
  
  async getUserRoles(userId) {
    console.log(`Getting roles for user ${userId}`);
    const userRoleIds = this.userRoles
      .filter(ur => ur.user === userId)
      .map(ur => ur.role);
    
    return this.roles.filter(role => userRoleIds.includes(role._id));
  }
  
  async hasPermission(userId, permissionId) {
    console.log(`Checking if user ${userId} has permission ${permissionId}`);
    
    // Get user roles
    const userRoles = await this.getUserRoles(userId);
    
    if (userRoles.length === 0) {
      return false;
    }
    
    // Check if any role has wildcard permission
    for (const role of userRoles) {
      if (role.permissions.includes('*')) {
        return true;
      }
      
      if (role.permissions.includes(permissionId)) {
        return true;
      }
      
      // Check resource wildcard
      if (typeof permissionId === 'string' && permissionId.includes(':')) {
        const [resource, action] = permissionId.split(':');
        const resourceWildcard = `${resource}:*`;
        
        if (role.permissions.includes(resourceWildcard)) {
          return true;
        }
      }
    }
    
    return false;
  }
  
  async getUserPermissions(userId) {
    console.log(`Getting permissions for user ${userId}`);
    
    // Get user roles
    const userRoles = await this.getUserRoles(userId);
    
    if (userRoles.length === 0) {
      return [];
    }
    
    // Get all permissions
    const allPermissions = await this.getAllPermissions();
    
    // Collect all permissions from user roles
    const userPermissions = new Set();
    
    for (const role of userRoles) {
      // If role has wildcard permission, add all permissions
      if (role.permissions.includes('*')) {
        allPermissions.forEach(permission => {
          userPermissions.add(`${permission.resource}:${permission.action}`);
        });
        break;
      }
      
      // Add specific permissions
      for (const permissionId of role.permissions) {
        // Handle resource wildcard permissions
        if (typeof permissionId === 'string' && permissionId.endsWith(':*')) {
          const resourcePrefix = permissionId.split(':')[0];
          allPermissions
            .filter(p => p.resource === resourcePrefix)
            .forEach(p => userPermissions.add(`${p.resource}:${p.action}`));
        } else {
          userPermissions.add(permissionId);
        }
      }
    }
    
    return Array.from(userPermissions);
  }
}

// Run tests
async function runTests() {
  try {
    console.log('Running RBAC tests...');
    
    // Create RBAC service
    const rbacService = new RBACService();
    
    // Test 1: Create permissions
    console.log('\nTest 1: Creating permissions...');
    
    const viewPermission = await rbacService.createPermission({
      name: 'View Test Resource',
      description: 'Permission to view test resources',
      resource: 'test-resource',
      action: 'view'
    });
    
    const editPermission = await rbacService.createPermission({
      name: 'Edit Test Resource',
      description: 'Permission to edit test resources',
      resource: 'test-resource',
      action: 'edit'
    });
    
    const deletePermission = await rbacService.createPermission({
      name: 'Delete Test Resource',
      description: 'Permission to delete test resources',
      resource: 'test-resource',
      action: 'delete'
    });
    
    console.log('Test 1: Permissions created successfully');
    
    // Test 2: Create roles
    console.log('\nTest 2: Creating roles...');
    
    const adminRole = await rbacService.createRole({
      name: 'Test Admin',
      description: 'Administrator role for testing',
      permissions: ['*']
    });
    
    const editorRole = await rbacService.createRole({
      name: 'Test Editor',
      description: 'Editor role for testing',
      permissions: [
        `${viewPermission.resource}:${viewPermission.action}`,
        `${editPermission.resource}:${editPermission.action}`
      ]
    });
    
    const viewerRole = await rbacService.createRole({
      name: 'Test Viewer',
      description: 'Viewer role for testing',
      permissions: [`${viewPermission.resource}:${viewPermission.action}`]
    });
    
    console.log('Test 2: Roles created successfully');
    
    // Test 3: Assign roles to users
    console.log('\nTest 3: Assigning roles to users...');
    
    const adminUserId = 'admin123';
    const editorUserId = 'editor123';
    const viewerUserId = 'viewer123';
    
    await rbacService.assignRoleToUser(adminUserId, adminRole._id);
    await rbacService.assignRoleToUser(editorUserId, editorRole._id);
    await rbacService.assignRoleToUser(viewerUserId, viewerRole._id);
    
    console.log('Test 3: Roles assigned successfully');
    
    // Test 4: Check permissions
    console.log('\nTest 4: Checking permissions...');
    
    const adminViewPermission = await rbacService.hasPermission(adminUserId, 'test-resource:view');
    const adminEditPermission = await rbacService.hasPermission(adminUserId, 'test-resource:edit');
    const adminDeletePermission = await rbacService.hasPermission(adminUserId, 'test-resource:delete');
    
    const editorViewPermission = await rbacService.hasPermission(editorUserId, 'test-resource:view');
    const editorEditPermission = await rbacService.hasPermission(editorUserId, 'test-resource:edit');
    const editorDeletePermission = await rbacService.hasPermission(editorUserId, 'test-resource:delete');
    
    const viewerViewPermission = await rbacService.hasPermission(viewerUserId, 'test-resource:view');
    const viewerEditPermission = await rbacService.hasPermission(viewerUserId, 'test-resource:edit');
    const viewerDeletePermission = await rbacService.hasPermission(viewerUserId, 'test-resource:delete');
    
    console.log(`Admin permissions:
      - test-resource:view: ${adminViewPermission}
      - test-resource:edit: ${adminEditPermission}
      - test-resource:delete: ${adminDeletePermission}`);
    
    console.log(`Editor permissions:
      - test-resource:view: ${editorViewPermission}
      - test-resource:edit: ${editorEditPermission}
      - test-resource:delete: ${editorDeletePermission}`);
    
    console.log(`Viewer permissions:
      - test-resource:view: ${viewerViewPermission}
      - test-resource:edit: ${viewerEditPermission}
      - test-resource:delete: ${viewerDeletePermission}`);
    
    console.log('Test 4: Permission checks completed successfully');
    
    console.log('\nAll RBAC tests completed successfully');
  } catch (error) {
    console.error('Test failed:', error);
  }
}

// Run the tests
runTests();
