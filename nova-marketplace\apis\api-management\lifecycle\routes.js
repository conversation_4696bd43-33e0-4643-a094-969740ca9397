const express = require('express');
const { validateRequest } = require('./validation');
const controllers = require('./controllers');

const router = express.Router();

/**
 * @swagger
 * /api-management/lifecycle/apis:
 *   get:
 *     summary: Get a list of APIs
 *     description: Returns a paginated list of APIs with optional filtering
 *     tags: [API Lifecycle]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - $ref: '#/components/parameters/page'
 *       - $ref: '#/components/parameters/limit'
 *       - $ref: '#/components/parameters/sortBy'
 *       - $ref: '#/components/parameters/sortOrder'
 *       - name: status
 *         in: query
 *         description: Filter by API status
 *         schema:
 *           type: string
 *           enum: [planning, development, testing, staging, production, deprecated, retired]
 *       - name: type
 *         in: query
 *         description: Filter by API type
 *         schema:
 *           type: string
 *           enum: [rest, graphql, soap, grpc, webhook]
 *       - name: tag
 *         in: query
 *         description: Filter by API tag
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/API'
 *                 pagination:
 *                   $ref: '#/components/schemas/Pagination'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/apis', controllers.getAPIs);

/**
 * @swagger
 * /api-management/lifecycle/apis/{id}:
 *   get:
 *     summary: Get a specific API
 *     description: Returns a specific API by ID
 *     tags: [API Lifecycle]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: API ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/API'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/apis/:id', controllers.getAPIById);

/**
 * @swagger
 * /api-management/lifecycle/apis:
 *   post:
 *     summary: Create a new API
 *     description: Creates a new API
 *     tags: [API Lifecycle]
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/APIInput'
 *     responses:
 *       201:
 *         description: API created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/API'
 *                 message:
 *                   type: string
 *                   example: API created successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/apis', validateRequest('createAPI'), controllers.createAPI);

/**
 * @swagger
 * /api-management/lifecycle/apis/{id}:
 *   put:
 *     summary: Update an API
 *     description: Updates an existing API
 *     tags: [API Lifecycle]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: API ID
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/APIInput'
 *     responses:
 *       200:
 *         description: API updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/API'
 *                 message:
 *                   type: string
 *                   example: API updated successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.put('/apis/:id', validateRequest('updateAPI'), controllers.updateAPI);

/**
 * @swagger
 * /api-management/lifecycle/apis/{id}:
 *   delete:
 *     summary: Delete an API
 *     description: Deletes an existing API
 *     tags: [API Lifecycle]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: API ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: API deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: API deleted successfully
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.delete('/apis/:id', controllers.deleteAPI);

/**
 * @swagger
 * /api-management/lifecycle/apis/{id}/versions:
 *   get:
 *     summary: Get versions for an API
 *     description: Returns all versions associated with a specific API
 *     tags: [API Lifecycle]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: API ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/APIVersion'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/apis/:id/versions', controllers.getAPIVersions);

/**
 * @swagger
 * /api-management/lifecycle/apis/{id}/versions:
 *   post:
 *     summary: Add a version to an API
 *     description: Adds a new version to an existing API
 *     tags: [API Lifecycle]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: API ID
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/APIVersionInput'
 *     responses:
 *       201:
 *         description: API version added successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/APIVersion'
 *                 message:
 *                   type: string
 *                   example: API version added successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/apis/:id/versions', validateRequest('createAPIVersion'), controllers.addAPIVersion);

/**
 * @swagger
 * /api-management/lifecycle/apis/{id}/versions/{versionId}:
 *   get:
 *     summary: Get a specific API version
 *     description: Returns a specific version of an API
 *     tags: [API Lifecycle]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: API ID
 *         required: true
 *         schema:
 *           type: string
 *       - name: versionId
 *         in: path
 *         description: Version ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/APIVersion'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/apis/:id/versions/:versionId', controllers.getAPIVersionById);

/**
 * @swagger
 * /api-management/lifecycle/apis/{id}/versions/{versionId}:
 *   put:
 *     summary: Update an API version
 *     description: Updates an existing version of an API
 *     tags: [API Lifecycle]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: API ID
 *         required: true
 *         schema:
 *           type: string
 *       - name: versionId
 *         in: path
 *         description: Version ID
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/APIVersionInput'
 *     responses:
 *       200:
 *         description: API version updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/APIVersion'
 *                 message:
 *                   type: string
 *                   example: API version updated successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.put('/apis/:id/versions/:versionId', validateRequest('updateAPIVersion'), controllers.updateAPIVersion);

/**
 * @swagger
 * /api-management/lifecycle/apis/{id}/dependencies:
 *   get:
 *     summary: Get dependencies for an API
 *     description: Returns all dependencies associated with a specific API
 *     tags: [API Lifecycle]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: API ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/APIDependency'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/apis/:id/dependencies', controllers.getAPIDependencies);

/**
 * @swagger
 * /api-management/lifecycle/apis/{id}/dependencies:
 *   post:
 *     summary: Add a dependency to an API
 *     description: Adds a new dependency to an existing API
 *     tags: [API Lifecycle]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: API ID
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/APIDependencyInput'
 *     responses:
 *       201:
 *         description: API dependency added successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/APIDependency'
 *                 message:
 *                   type: string
 *                   example: API dependency added successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/apis/:id/dependencies', validateRequest('createAPIDependency'), controllers.addAPIDependency);

/**
 * @swagger
 * /api-management/lifecycle/apis/{id}/consumers:
 *   get:
 *     summary: Get consumers for an API
 *     description: Returns all consumers associated with a specific API
 *     tags: [API Lifecycle]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: API ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/APIConsumer'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/apis/:id/consumers', controllers.getAPIConsumers);

/**
 * @swagger
 * /api-management/lifecycle/apis/{id}/consumers:
 *   post:
 *     summary: Add a consumer to an API
 *     description: Adds a new consumer to an existing API
 *     tags: [API Lifecycle]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: API ID
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/APIConsumerInput'
 *     responses:
 *       201:
 *         description: API consumer added successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/APIConsumer'
 *                 message:
 *                   type: string
 *                   example: API consumer added successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/apis/:id/consumers', validateRequest('createAPIConsumer'), controllers.addAPIConsumer);

/**
 * @swagger
 * /api-management/lifecycle/types:
 *   get:
 *     summary: Get API types
 *     description: Returns a list of API types
 *     tags: [API Lifecycle]
 *     security:
 *       - ApiKeyAuth: []
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       name:
 *                         type: string
 *                       description:
 *                         type: string
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/types', controllers.getAPITypes);

/**
 * @swagger
 * /api-management/lifecycle/statuses:
 *   get:
 *     summary: Get API statuses
 *     description: Returns a list of API statuses
 *     tags: [API Lifecycle]
 *     security:
 *       - ApiKeyAuth: []
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       name:
 *                         type: string
 *                       description:
 *                         type: string
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/statuses', controllers.getAPIStatuses);

module.exports = router;
