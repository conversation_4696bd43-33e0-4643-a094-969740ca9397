/**
 * NovaFuse Universal API Connector - Error Classes
 * 
 * This module defines custom error classes for the application.
 */

/**
 * Base error class for the application
 */
class AppError extends Error {
  constructor(message, statusCode = 500, details = {}) {
    super(message);
    this.name = this.constructor.name;
    this.statusCode = statusCode;
    this.details = details;
    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * Validation error
 */
class ValidationError extends AppError {
  constructor(message, details = {}) {
    super(message, 400, details);
    this.code = 'VALIDATION_ERROR';
  }
}

/**
 * Resource not found error
 */
class ResourceNotFoundError extends AppError {
  constructor(resource, id) {
    super(`${resource} not found with ID: ${id}`, 404, { resource, id });
    this.code = 'RESOURCE_NOT_FOUND';
  }
}

/**
 * Unauthorized error
 */
class UnauthorizedError extends AppError {
  constructor(message = 'Unauthorized', details = {}) {
    super(message, 401, details);
    this.code = 'UNAUTHORIZED';
  }
}

/**
 * Forbidden error
 */
class ForbiddenError extends AppError {
  constructor(message = 'Forbidden', details = {}) {
    super(message, 403, details);
    this.code = 'FORBIDDEN';
  }
}

/**
 * Conflict error
 */
class ConflictError extends AppError {
  constructor(message, details = {}) {
    super(message, 409, details);
    this.code = 'CONFLICT';
  }
}

/**
 * Connector error
 */
class ConnectorError extends AppError {
  constructor(message, details = {}) {
    super(message, 500, details);
    this.code = 'CONNECTOR_ERROR';
  }
}

/**
 * Timeout error
 */
class TimeoutError extends AppError {
  constructor(message, details = {}) {
    super(message, 504, details);
    this.code = 'TIMEOUT';
  }
}

module.exports = {
  AppError,
  ValidationError,
  ResourceNotFoundError,
  UnauthorizedError,
  ForbiddenError,
  ConflictError,
  ConnectorError,
  TimeoutError
};
