/**
 * NovaCore API Server Entry Point
 *
 * This file starts the NovaCore API server.
 */

const app = require('./app');
const config = require('../config');
const logger = require('../config/logger');
const databaseManager = require('../config/database');

// Get port from config
const PORT = config.server.port;

// Connect to database and start server
async function startServer() {
  try {
    logger.info('Starting NovaCore API server...');

    // Connect to database
    logger.info('Connecting to database...');
    await databaseManager.connect();

    // Start server
    app.listen(PORT, () => {
      logger.info(`NovaCore API server running on port ${PORT}`);
      logger.info(`Server URL: ${config.server.baseUrl}`);
      logger.info('Available routes:');
      logger.info(`- ${config.api.prefix}/evidence`);
      logger.info(`- ${config.api.prefix}/blockchain`);
      logger.info(`- ${config.api.prefix}/connectors`);
      logger.info('- /health');
    });
  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', error);
  process.exit(1);
});

// Handle SIGTERM signal
process.on('SIGTERM', async () => {
  logger.info('SIGTERM received, shutting down gracefully');
  await databaseManager.disconnect();
  process.exit(0);
});

// Handle SIGINT signal
process.on('SIGINT', async () => {
  logger.info('SIGINT received, shutting down gracefully');
  await databaseManager.disconnect();
  process.exit(0);
});

// Start server
startServer();
