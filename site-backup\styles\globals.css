@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --primary-bg: #172030;
  --secondary-bg: #1e293b;
  --primary-text: #f8fafc;
  --secondary-text: #cbd5e1;
  --accent-color: #2563eb;
}

body {
  background-color: var(--primary-bg);
  color: var(--primary-text);
}

.bg-primary {
  background-color: var(--primary-bg);
}

.bg-secondary {
  background-color: var(--secondary-bg);
}

.text-primary {
  color: var(--primary-text);
}

.text-secondary {
  color: var(--secondary-text);
}

.accent-color {
  color: var(--accent-color);
}

.accent-bg {
  background-image: linear-gradient(to right, #1d4ed8, #7e22ce);
  border: 1px solid #60a5fa;
}

/* API Documentation Styles */
pre {
  background-color: #0f172a;
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
}

.method {
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-weight: bold;
  display: inline-block;
  width: 60px;
  text-align: center;
}

.get {
  background-color: #0891b2;
  color: white;
}

.post {
  background-color: #65a30d;
  color: white;
}

.put {
  background-color: #d97706;
  color: white;
}

.delete {
  background-color: #dc2626;
  color: white;
}
