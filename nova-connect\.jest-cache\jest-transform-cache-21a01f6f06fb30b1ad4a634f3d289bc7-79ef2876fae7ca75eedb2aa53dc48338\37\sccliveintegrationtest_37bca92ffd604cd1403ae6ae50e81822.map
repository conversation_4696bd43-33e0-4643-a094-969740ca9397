{"version": 3, "names": ["SCCConnector", "require", "TransformationEngine", "RemediationEngine", "performance", "fs", "path", "hasCredentials", "process", "env", "GOOGLE_APPLICATION_CREDENTIALS", "existsSync", "config", "organizationId", "GCP_ORGANIZATION_ID", "projectId", "GCP_PROJECT_ID", "finding<PERSON><PERSON>er", "max<PERSON><PERSON><PERSON>", "normalizationBatchSize", "describe", "sccConnector", "transformationEngine", "remediationEngine", "beforeAll", "registerAction", "parameters", "console", "log", "success", "credentials<PERSON>ath", "credentials", "JSON", "parse", "readFileSync", "initialize", "error", "conditionalTest", "it", "skip", "startTime", "now", "findings", "nextPageToken", "getFindings", "filter", "pageSize", "endTime", "duration", "length", "toFixed", "Math", "max", "expect", "Array", "isArray", "toBe", "firstFinding", "toHaveProperty", "normalizedFindings", "normalizeFindings", "firstNormalized", "createdAt", "toBeLessThan", "allFindings", "token", "pageToken", "push", "batchSize", "batches", "i", "slice", "batch", "normalizedBatch", "findingsPerSecond", "toBeGreaterThan", "finding", "remediationScenario", "id", "Date", "type", "severity", "resource", "resourceName", "provider", "remediationSteps", "action", "ruleName", "priority", "remediationResult", "executeRemediation", "steps"], "sources": ["scc-live-integration.test.js"], "sourcesContent": ["/**\n * NovaConnect - Security Command Center Live Integration Test\n * \n * This test connects to the actual Google Security Command Center API\n * to validate real-world performance and functionality.\n * \n * NOTE: This test requires valid GCP credentials with SCC access.\n * Set the GOOGLE_APPLICATION_CREDENTIALS environment variable to point\n * to a service account key file with appropriate permissions.\n */\n\nconst { SCCConnector } = require('../../../src/connectors/gcp/scc-connector');\nconst { TransformationEngine } = require('../../../src/engines/transformation-engine');\nconst { RemediationEngine } = require('../../../src/engines/remediation-engine');\nconst { performance } = require('perf_hooks');\nconst fs = require('fs');\nconst path = require('path');\n\n// Skip tests if credentials are not available\nconst hasCredentials = process.env.GOOGLE_APPLICATION_CREDENTIALS && \n  fs.existsSync(process.env.GOOGLE_APPLICATION_CREDENTIALS);\n\n// Test configuration\nconst config = {\n  organizationId: process.env.GCP_ORGANIZATION_ID || '************',\n  projectId: process.env.GCP_PROJECT_ID || 'test-project',\n  findingFilter: 'severity=\"HIGH\" OR severity=\"CRITICAL\"',\n  maxFindings: 1000,\n  normalizationBatchSize: 100\n};\n\ndescribe('Security Command Center Live Integration', () => {\n  let sccConnector;\n  let transformationEngine;\n  let remediationEngine;\n  \n  beforeAll(async () => {\n    // Initialize the connectors and engines\n    sccConnector = new SCCConnector();\n    transformationEngine = new TransformationEngine();\n    remediationEngine = new RemediationEngine();\n    \n    // Register a test remediation action\n    remediationEngine.registerAction('update-firewall-rule', async ({ parameters }) => {\n      console.log('Mock updating firewall rule:', parameters);\n      return { success: true };\n    });\n    \n    // Initialize the SCC connector with credentials\n    if (hasCredentials) {\n      try {\n        const credentialsPath = process.env.GOOGLE_APPLICATION_CREDENTIALS;\n        const credentials = JSON.parse(fs.readFileSync(credentialsPath, 'utf8'));\n        await sccConnector.initialize(credentials);\n      } catch (error) {\n        console.error('Error initializing SCC connector:', error);\n      }\n    }\n  });\n  \n  // Skip tests if credentials are not available\n  const conditionalTest = hasCredentials ? it : it.skip;\n  \n  conditionalTest('should retrieve findings from SCC', async () => {\n    // Skip if no credentials\n    if (!hasCredentials) {\n      return;\n    }\n    \n    const startTime = performance.now();\n    \n    // Get findings from SCC\n    const { findings, nextPageToken } = await sccConnector.getFindings({\n      organizationId: config.organizationId,\n      filter: config.findingFilter,\n      pageSize: 10\n    });\n    \n    const endTime = performance.now();\n    const duration = endTime - startTime;\n    \n    // Log performance metrics\n    console.log(`Retrieved ${findings.length} findings in ${duration.toFixed(2)}ms`);\n    console.log(`Average time per finding: ${(duration / Math.max(1, findings.length)).toFixed(2)}ms`);\n    \n    // Verify findings\n    expect(Array.isArray(findings)).toBe(true);\n    if (findings.length > 0) {\n      const firstFinding = findings[0];\n      expect(firstFinding).toHaveProperty('name');\n      expect(firstFinding).toHaveProperty('category');\n      expect(firstFinding).toHaveProperty('severity');\n    }\n  }, 30000);\n  \n  conditionalTest('should normalize findings efficiently', async () => {\n    // Skip if no credentials\n    if (!hasCredentials) {\n      return;\n    }\n    \n    // Get findings from SCC\n    const { findings } = await sccConnector.getFindings({\n      organizationId: config.organizationId,\n      filter: config.findingFilter,\n      pageSize: 10\n    });\n    \n    if (findings.length === 0) {\n      console.log('No findings to normalize');\n      return;\n    }\n    \n    const startTime = performance.now();\n    \n    // Normalize findings\n    const normalizedFindings = sccConnector.normalizeFindings(findings);\n    \n    const endTime = performance.now();\n    const duration = endTime - startTime;\n    \n    // Log performance metrics\n    console.log(`Normalized ${findings.length} findings in ${duration.toFixed(2)}ms`);\n    console.log(`Average time per finding: ${(duration / findings.length).toFixed(2)}ms`);\n    \n    // Verify normalized findings\n    expect(Array.isArray(normalizedFindings)).toBe(true);\n    expect(normalizedFindings.length).toBe(findings.length);\n    \n    if (normalizedFindings.length > 0) {\n      const firstNormalized = normalizedFindings[0];\n      expect(firstNormalized).toHaveProperty('id');\n      expect(firstNormalized).toHaveProperty('severity');\n      expect(firstNormalized).toHaveProperty('category');\n      expect(firstNormalized).toHaveProperty('createdAt');\n      expect(typeof firstNormalized.createdAt).toBe('number');\n    }\n    \n    // Verify normalization speed\n    expect(duration / findings.length).toBeLessThan(1); // Less than 1ms per finding\n  }, 30000);\n  \n  conditionalTest('should handle large batch of findings', async () => {\n    // Skip if no credentials\n    if (!hasCredentials) {\n      return;\n    }\n    \n    // Get a larger batch of findings\n    const allFindings = [];\n    let nextPageToken = null;\n    \n    do {\n      const { findings, nextPageToken: token } = await sccConnector.getFindings({\n        organizationId: config.organizationId,\n        filter: config.findingFilter,\n        pageSize: 100,\n        pageToken: nextPageToken\n      });\n      \n      allFindings.push(...findings);\n      nextPageToken = token;\n      \n      // Limit the number of findings to avoid long test times\n      if (allFindings.length >= config.maxFindings) {\n        break;\n      }\n    } while (nextPageToken);\n    \n    console.log(`Retrieved ${allFindings.length} findings for batch test`);\n    \n    if (allFindings.length === 0) {\n      console.log('No findings to process');\n      return;\n    }\n    \n    // Process findings in batches\n    const batchSize = config.normalizationBatchSize;\n    const batches = [];\n    \n    for (let i = 0; i < allFindings.length; i += batchSize) {\n      batches.push(allFindings.slice(i, i + batchSize));\n    }\n    \n    console.log(`Processing ${batches.length} batches of ${batchSize} findings each`);\n    \n    const startTime = performance.now();\n    \n    // Process each batch\n    for (const batch of batches) {\n      const normalizedBatch = sccConnector.normalizeFindings(batch);\n      expect(normalizedBatch.length).toBe(batch.length);\n    }\n    \n    const endTime = performance.now();\n    const duration = endTime - startTime;\n    \n    // Log performance metrics\n    console.log(`Processed ${allFindings.length} findings in ${duration.toFixed(2)}ms`);\n    console.log(`Average time per finding: ${(duration / allFindings.length).toFixed(2)}ms`);\n    console.log(`Throughput: ${((allFindings.length / duration) * 1000).toFixed(2)} findings/second`);\n    \n    // Verify processing speed\n    const findingsPerSecond = (allFindings.length / duration) * 1000;\n    expect(findingsPerSecond).toBeGreaterThan(1000); // At least 1000 findings per second\n  }, 120000);\n  \n  conditionalTest('should create and execute remediation workflow', async () => {\n    // Skip if no credentials\n    if (!hasCredentials) {\n      return;\n    }\n    \n    // Get a finding to remediate\n    const { findings } = await sccConnector.getFindings({\n      organizationId: config.organizationId,\n      filter: config.findingFilter,\n      pageSize: 1\n    });\n    \n    if (findings.length === 0) {\n      console.log('No findings to remediate');\n      return;\n    }\n    \n    // Normalize the finding\n    const normalizedFindings = sccConnector.normalizeFindings(findings);\n    const finding = normalizedFindings[0];\n    \n    // Create a remediation scenario\n    const remediationScenario = {\n      id: `remediation-${Date.now()}`,\n      type: 'security',\n      severity: finding.severity,\n      resource: {\n        id: finding.resourceName,\n        type: 'firewall',\n        provider: 'gcp'\n      },\n      finding,\n      remediationSteps: [\n        {\n          id: 'step-1',\n          action: 'update-firewall-rule',\n          parameters: {\n            ruleName: 'default-allow-all',\n            action: 'deny',\n            priority: 1000\n          }\n        }\n      ]\n    };\n    \n    const startTime = performance.now();\n    \n    // Execute the remediation\n    const remediationResult = await remediationEngine.executeRemediation(remediationScenario);\n    \n    const endTime = performance.now();\n    const duration = endTime - startTime;\n    \n    // Log performance metrics\n    console.log(`Executed remediation in ${duration.toFixed(2)}ms`);\n    \n    // Verify remediation result\n    expect(remediationResult).toHaveProperty('id');\n    expect(remediationResult).toHaveProperty('status');\n    expect(remediationResult).toHaveProperty('steps');\n    expect(remediationResult.steps.length).toBe(1);\n    expect(remediationResult.steps[0].success).toBe(true);\n    \n    // Verify remediation speed\n    expect(duration).toBeLessThan(8000); // Less than 8 seconds\n  }, 30000);\n});\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,MAAM;EAAEA;AAAa,CAAC,GAAGC,OAAO,CAAC,2CAA2C,CAAC;AAC7E,MAAM;EAAEC;AAAqB,CAAC,GAAGD,OAAO,CAAC,4CAA4C,CAAC;AACtF,MAAM;EAAEE;AAAkB,CAAC,GAAGF,OAAO,CAAC,yCAAyC,CAAC;AAChF,MAAM;EAAEG;AAAY,CAAC,GAAGH,OAAO,CAAC,YAAY,CAAC;AAC7C,MAAMI,EAAE,GAAGJ,OAAO,CAAC,IAAI,CAAC;AACxB,MAAMK,IAAI,GAAGL,OAAO,CAAC,MAAM,CAAC;;AAE5B;AACA,MAAMM,cAAc,GAAGC,OAAO,CAACC,GAAG,CAACC,8BAA8B,IAC/DL,EAAE,CAACM,UAAU,CAACH,OAAO,CAACC,GAAG,CAACC,8BAA8B,CAAC;;AAE3D;AACA,MAAME,MAAM,GAAG;EACbC,cAAc,EAAEL,OAAO,CAACC,GAAG,CAACK,mBAAmB,IAAI,cAAc;EACjEC,SAAS,EAAEP,OAAO,CAACC,GAAG,CAACO,cAAc,IAAI,cAAc;EACvDC,aAAa,EAAE,wCAAwC;EACvDC,WAAW,EAAE,IAAI;EACjBC,sBAAsB,EAAE;AAC1B,CAAC;AAEDC,QAAQ,CAAC,0CAA0C,EAAE,MAAM;EACzD,IAAIC,YAAY;EAChB,IAAIC,oBAAoB;EACxB,IAAIC,iBAAiB;EAErBC,SAAS,CAAC,YAAY;IACpB;IACAH,YAAY,GAAG,IAAIrB,YAAY,CAAC,CAAC;IACjCsB,oBAAoB,GAAG,IAAIpB,oBAAoB,CAAC,CAAC;IACjDqB,iBAAiB,GAAG,IAAIpB,iBAAiB,CAAC,CAAC;;IAE3C;IACAoB,iBAAiB,CAACE,cAAc,CAAC,sBAAsB,EAAE,OAAO;MAAEC;IAAW,CAAC,KAAK;MACjFC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEF,UAAU,CAAC;MACvD,OAAO;QAAEG,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC;;IAEF;IACA,IAAItB,cAAc,EAAE;MAClB,IAAI;QACF,MAAMuB,eAAe,GAAGtB,OAAO,CAACC,GAAG,CAACC,8BAA8B;QAClE,MAAMqB,WAAW,GAAGC,IAAI,CAACC,KAAK,CAAC5B,EAAE,CAAC6B,YAAY,CAACJ,eAAe,EAAE,MAAM,CAAC,CAAC;QACxE,MAAMT,YAAY,CAACc,UAAU,CAACJ,WAAW,CAAC;MAC5C,CAAC,CAAC,OAAOK,KAAK,EAAE;QACdT,OAAO,CAACS,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MAC3D;IACF;EACF,CAAC,CAAC;;EAEF;EACA,MAAMC,eAAe,GAAG9B,cAAc,GAAG+B,EAAE,GAAGA,EAAE,CAACC,IAAI;EAErDF,eAAe,CAAC,mCAAmC,EAAE,YAAY;IAC/D;IACA,IAAI,CAAC9B,cAAc,EAAE;MACnB;IACF;IAEA,MAAMiC,SAAS,GAAGpC,WAAW,CAACqC,GAAG,CAAC,CAAC;;IAEnC;IACA,MAAM;MAAEC,QAAQ;MAAEC;IAAc,CAAC,GAAG,MAAMtB,YAAY,CAACuB,WAAW,CAAC;MACjE/B,cAAc,EAAED,MAAM,CAACC,cAAc;MACrCgC,MAAM,EAAEjC,MAAM,CAACK,aAAa;MAC5B6B,QAAQ,EAAE;IACZ,CAAC,CAAC;IAEF,MAAMC,OAAO,GAAG3C,WAAW,CAACqC,GAAG,CAAC,CAAC;IACjC,MAAMO,QAAQ,GAAGD,OAAO,GAAGP,SAAS;;IAEpC;IACAb,OAAO,CAACC,GAAG,CAAC,aAAac,QAAQ,CAACO,MAAM,gBAAgBD,QAAQ,CAACE,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;IAChFvB,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAACoB,QAAQ,GAAGG,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEV,QAAQ,CAACO,MAAM,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;;IAElG;IACAG,MAAM,CAACC,KAAK,CAACC,OAAO,CAACb,QAAQ,CAAC,CAAC,CAACc,IAAI,CAAC,IAAI,CAAC;IAC1C,IAAId,QAAQ,CAACO,MAAM,GAAG,CAAC,EAAE;MACvB,MAAMQ,YAAY,GAAGf,QAAQ,CAAC,CAAC,CAAC;MAChCW,MAAM,CAACI,YAAY,CAAC,CAACC,cAAc,CAAC,MAAM,CAAC;MAC3CL,MAAM,CAACI,YAAY,CAAC,CAACC,cAAc,CAAC,UAAU,CAAC;MAC/CL,MAAM,CAACI,YAAY,CAAC,CAACC,cAAc,CAAC,UAAU,CAAC;IACjD;EACF,CAAC,EAAE,KAAK,CAAC;EAETrB,eAAe,CAAC,uCAAuC,EAAE,YAAY;IACnE;IACA,IAAI,CAAC9B,cAAc,EAAE;MACnB;IACF;;IAEA;IACA,MAAM;MAAEmC;IAAS,CAAC,GAAG,MAAMrB,YAAY,CAACuB,WAAW,CAAC;MAClD/B,cAAc,EAAED,MAAM,CAACC,cAAc;MACrCgC,MAAM,EAAEjC,MAAM,CAACK,aAAa;MAC5B6B,QAAQ,EAAE;IACZ,CAAC,CAAC;IAEF,IAAIJ,QAAQ,CAACO,MAAM,KAAK,CAAC,EAAE;MACzBtB,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;MACvC;IACF;IAEA,MAAMY,SAAS,GAAGpC,WAAW,CAACqC,GAAG,CAAC,CAAC;;IAEnC;IACA,MAAMkB,kBAAkB,GAAGtC,YAAY,CAACuC,iBAAiB,CAAClB,QAAQ,CAAC;IAEnE,MAAMK,OAAO,GAAG3C,WAAW,CAACqC,GAAG,CAAC,CAAC;IACjC,MAAMO,QAAQ,GAAGD,OAAO,GAAGP,SAAS;;IAEpC;IACAb,OAAO,CAACC,GAAG,CAAC,cAAcc,QAAQ,CAACO,MAAM,gBAAgBD,QAAQ,CAACE,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;IACjFvB,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAACoB,QAAQ,GAAGN,QAAQ,CAACO,MAAM,EAAEC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;;IAErF;IACAG,MAAM,CAACC,KAAK,CAACC,OAAO,CAACI,kBAAkB,CAAC,CAAC,CAACH,IAAI,CAAC,IAAI,CAAC;IACpDH,MAAM,CAACM,kBAAkB,CAACV,MAAM,CAAC,CAACO,IAAI,CAACd,QAAQ,CAACO,MAAM,CAAC;IAEvD,IAAIU,kBAAkB,CAACV,MAAM,GAAG,CAAC,EAAE;MACjC,MAAMY,eAAe,GAAGF,kBAAkB,CAAC,CAAC,CAAC;MAC7CN,MAAM,CAACQ,eAAe,CAAC,CAACH,cAAc,CAAC,IAAI,CAAC;MAC5CL,MAAM,CAACQ,eAAe,CAAC,CAACH,cAAc,CAAC,UAAU,CAAC;MAClDL,MAAM,CAACQ,eAAe,CAAC,CAACH,cAAc,CAAC,UAAU,CAAC;MAClDL,MAAM,CAACQ,eAAe,CAAC,CAACH,cAAc,CAAC,WAAW,CAAC;MACnDL,MAAM,CAAC,OAAOQ,eAAe,CAACC,SAAS,CAAC,CAACN,IAAI,CAAC,QAAQ,CAAC;IACzD;;IAEA;IACAH,MAAM,CAACL,QAAQ,GAAGN,QAAQ,CAACO,MAAM,CAAC,CAACc,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;EACtD,CAAC,EAAE,KAAK,CAAC;EAET1B,eAAe,CAAC,uCAAuC,EAAE,YAAY;IACnE;IACA,IAAI,CAAC9B,cAAc,EAAE;MACnB;IACF;;IAEA;IACA,MAAMyD,WAAW,GAAG,EAAE;IACtB,IAAIrB,aAAa,GAAG,IAAI;IAExB,GAAG;MACD,MAAM;QAAED,QAAQ;QAAEC,aAAa,EAAEsB;MAAM,CAAC,GAAG,MAAM5C,YAAY,CAACuB,WAAW,CAAC;QACxE/B,cAAc,EAAED,MAAM,CAACC,cAAc;QACrCgC,MAAM,EAAEjC,MAAM,CAACK,aAAa;QAC5B6B,QAAQ,EAAE,GAAG;QACboB,SAAS,EAAEvB;MACb,CAAC,CAAC;MAEFqB,WAAW,CAACG,IAAI,CAAC,GAAGzB,QAAQ,CAAC;MAC7BC,aAAa,GAAGsB,KAAK;;MAErB;MACA,IAAID,WAAW,CAACf,MAAM,IAAIrC,MAAM,CAACM,WAAW,EAAE;QAC5C;MACF;IACF,CAAC,QAAQyB,aAAa;IAEtBhB,OAAO,CAACC,GAAG,CAAC,aAAaoC,WAAW,CAACf,MAAM,0BAA0B,CAAC;IAEtE,IAAIe,WAAW,CAACf,MAAM,KAAK,CAAC,EAAE;MAC5BtB,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;MACrC;IACF;;IAEA;IACA,MAAMwC,SAAS,GAAGxD,MAAM,CAACO,sBAAsB;IAC/C,MAAMkD,OAAO,GAAG,EAAE;IAElB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,WAAW,CAACf,MAAM,EAAEqB,CAAC,IAAIF,SAAS,EAAE;MACtDC,OAAO,CAACF,IAAI,CAACH,WAAW,CAACO,KAAK,CAACD,CAAC,EAAEA,CAAC,GAAGF,SAAS,CAAC,CAAC;IACnD;IAEAzC,OAAO,CAACC,GAAG,CAAC,cAAcyC,OAAO,CAACpB,MAAM,eAAemB,SAAS,gBAAgB,CAAC;IAEjF,MAAM5B,SAAS,GAAGpC,WAAW,CAACqC,GAAG,CAAC,CAAC;;IAEnC;IACA,KAAK,MAAM+B,KAAK,IAAIH,OAAO,EAAE;MAC3B,MAAMI,eAAe,GAAGpD,YAAY,CAACuC,iBAAiB,CAACY,KAAK,CAAC;MAC7DnB,MAAM,CAACoB,eAAe,CAACxB,MAAM,CAAC,CAACO,IAAI,CAACgB,KAAK,CAACvB,MAAM,CAAC;IACnD;IAEA,MAAMF,OAAO,GAAG3C,WAAW,CAACqC,GAAG,CAAC,CAAC;IACjC,MAAMO,QAAQ,GAAGD,OAAO,GAAGP,SAAS;;IAEpC;IACAb,OAAO,CAACC,GAAG,CAAC,aAAaoC,WAAW,CAACf,MAAM,gBAAgBD,QAAQ,CAACE,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;IACnFvB,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAACoB,QAAQ,GAAGgB,WAAW,CAACf,MAAM,EAAEC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;IACxFvB,OAAO,CAACC,GAAG,CAAC,eAAe,CAAEoC,WAAW,CAACf,MAAM,GAAGD,QAAQ,GAAI,IAAI,EAAEE,OAAO,CAAC,CAAC,CAAC,kBAAkB,CAAC;;IAEjG;IACA,MAAMwB,iBAAiB,GAAIV,WAAW,CAACf,MAAM,GAAGD,QAAQ,GAAI,IAAI;IAChEK,MAAM,CAACqB,iBAAiB,CAAC,CAACC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC;EACnD,CAAC,EAAE,MAAM,CAAC;EAEVtC,eAAe,CAAC,gDAAgD,EAAE,YAAY;IAC5E;IACA,IAAI,CAAC9B,cAAc,EAAE;MACnB;IACF;;IAEA;IACA,MAAM;MAAEmC;IAAS,CAAC,GAAG,MAAMrB,YAAY,CAACuB,WAAW,CAAC;MAClD/B,cAAc,EAAED,MAAM,CAACC,cAAc;MACrCgC,MAAM,EAAEjC,MAAM,CAACK,aAAa;MAC5B6B,QAAQ,EAAE;IACZ,CAAC,CAAC;IAEF,IAAIJ,QAAQ,CAACO,MAAM,KAAK,CAAC,EAAE;MACzBtB,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;MACvC;IACF;;IAEA;IACA,MAAM+B,kBAAkB,GAAGtC,YAAY,CAACuC,iBAAiB,CAAClB,QAAQ,CAAC;IACnE,MAAMkC,OAAO,GAAGjB,kBAAkB,CAAC,CAAC,CAAC;;IAErC;IACA,MAAMkB,mBAAmB,GAAG;MAC1BC,EAAE,EAAE,eAAeC,IAAI,CAACtC,GAAG,CAAC,CAAC,EAAE;MAC/BuC,IAAI,EAAE,UAAU;MAChBC,QAAQ,EAAEL,OAAO,CAACK,QAAQ;MAC1BC,QAAQ,EAAE;QACRJ,EAAE,EAAEF,OAAO,CAACO,YAAY;QACxBH,IAAI,EAAE,UAAU;QAChBI,QAAQ,EAAE;MACZ,CAAC;MACDR,OAAO;MACPS,gBAAgB,EAAE,CAChB;QACEP,EAAE,EAAE,QAAQ;QACZQ,MAAM,EAAE,sBAAsB;QAC9B5D,UAAU,EAAE;UACV6D,QAAQ,EAAE,mBAAmB;UAC7BD,MAAM,EAAE,MAAM;UACdE,QAAQ,EAAE;QACZ;MACF,CAAC;IAEL,CAAC;IAED,MAAMhD,SAAS,GAAGpC,WAAW,CAACqC,GAAG,CAAC,CAAC;;IAEnC;IACA,MAAMgD,iBAAiB,GAAG,MAAMlE,iBAAiB,CAACmE,kBAAkB,CAACb,mBAAmB,CAAC;IAEzF,MAAM9B,OAAO,GAAG3C,WAAW,CAACqC,GAAG,CAAC,CAAC;IACjC,MAAMO,QAAQ,GAAGD,OAAO,GAAGP,SAAS;;IAEpC;IACAb,OAAO,CAACC,GAAG,CAAC,2BAA2BoB,QAAQ,CAACE,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;;IAE/D;IACAG,MAAM,CAACoC,iBAAiB,CAAC,CAAC/B,cAAc,CAAC,IAAI,CAAC;IAC9CL,MAAM,CAACoC,iBAAiB,CAAC,CAAC/B,cAAc,CAAC,QAAQ,CAAC;IAClDL,MAAM,CAACoC,iBAAiB,CAAC,CAAC/B,cAAc,CAAC,OAAO,CAAC;IACjDL,MAAM,CAACoC,iBAAiB,CAACE,KAAK,CAAC1C,MAAM,CAAC,CAACO,IAAI,CAAC,CAAC,CAAC;IAC9CH,MAAM,CAACoC,iBAAiB,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC9D,OAAO,CAAC,CAAC2B,IAAI,CAAC,IAAI,CAAC;;IAErD;IACAH,MAAM,CAACL,QAAQ,CAAC,CAACe,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;EACvC,CAAC,EAAE,KAAK,CAAC;AACX,CAAC,CAAC", "ignoreList": []}