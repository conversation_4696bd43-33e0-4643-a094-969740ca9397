"""
Quantum Protein Folding Dashboard

A web-based dashboard for monitoring and visualizing quantum protein folding experiments,
including QAOA, VQE, and quantum machine learning approaches.
"""

import os
import json
import time
import psutil
import socket
import platform
import numpy as np
from pathlib import Path
from typing import Dict, List, Optional, Any, <PERSON><PERSON>
from datetime import datetime, timed<PERSON>ta
from concurrent.futures import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>xecutor
from threading import Lock

from flask import Flask, render_template, request, jsonify, send_from_directory, abort
from flask_socketio import Socket<PERSON>, emit, join_room, leave_room
import plotly
import plotly.graph_objs as go
from plotly.subplots import make_subplots

# Import quantum folding modules with error handling
try:
    from ..algorithms import qaoa_folding, vqe_folding
except ImportError as e:
    print(f"Warning: Could not import quantum folding modules: {e}")
    qaoa_folding = vqe_folding = None

try:
    from ..ml.quantum_models import QuantumProteinDataset, HybridQuantumProteinModel, train_quantum_model
except ImportError as e:
    print(f"Warning: Could not import quantum ML modules: {e}")
    QuantumProteinDataset = HybridQuantumProteinModel = train_quantum_model = None

try:
    from ..quantum_backend_factory import QuantumBackendFactory
except ImportError as e:
    print(f"Warning: Could not import QuantumBackendFactory: {e}")
    QuantumBackendFactory = None

# Initialize Flask app
app = Flask(__name__, static_folder='static')
app.config.update(
    SECRET_KEY=os.environ.get('FLASK_SECRET_KEY', 'dev-secret-key-123'),
    MAX_CONTENT_LENGTH=16 * 1024 * 1024,  # 16MB max upload
    UPLOAD_FOLDER=os.path.join(os.path.dirname(__file__), 'uploads')
)

# Initialize SocketIO with message queue for production
socketio = SocketIO(
    app,
    async_mode='threading',
    cors_allowed_origins="*",
    message_queue=os.environ.get('REDIS_URL'),
    logger=True,
    engineio_logger=os.environ.get('FLASK_ENV') == 'development'
)

# Thread pool for background tasks
executor = ThreadPoolExecutor(max_workers=4)

# Global state for storing experiment results
experiments: Dict[str, Dict] = {}
active_experiments: Dict[str, bool] = {}
connected_clients = set()

# Thread-safe locks
experiment_lock = Lock()
client_lock = Lock()

# System monitoring
system_metrics = {
    'cpu': [],
    'memory': [],
    'gpu': None,
    'network': {},
    'last_updated': None
}

# Create necessary directories
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
os.makedirs('static/plots', exist_ok=True)
os.makedirs('static/results', exist_ok=True)

# Utility functions
def get_system_metrics() -> Dict[str, Any]:
    """Get current system metrics."""
    # CPU
    cpu_percent = psutil.cpu_percent(interval=1, percpu=False)
    cpu_freq = psutil.cpu_freq()
    
    # Memory
    memory = psutil.virtual_memory()
    swap = psutil.swap_memory()
    
    # Disk
    disk = psutil.disk_usage('/')
    
    # Network
    net_io = psutil.net_io_counters()
    
    # System info
    boot_time = datetime.fromtimestamp(psutil.boot_time())
    uptime = datetime.now() - boot_time
    
    # GPU info (if available)
    gpu_info = None
    try:
        import GPUtil
        gpus = GPUtil.getGPUs()
        if gpus:
            gpu = gpus[0]  # Get first GPU
            gpu_info = {
                'name': gpu.name,
                'load': gpu.load * 100,  # Convert to percentage
                'temperature': gpu.temperature,
                'memory_total': gpu.memoryTotal,
                'memory_used': gpu.memoryUsed,
                'memory_free': gpu.memoryFree
            }
    except ImportError:
        pass
    
    return {
        'cpu': {
            'percent': cpu_percent,
            'cores': psutil.cpu_count(logical=False),
            'threads': psutil.cpu_count(logical=True),
            'frequency': cpu_freq.current if cpu_freq else None,
            'max_frequency': cpu_freq.max if cpu_freq else None
        },
        'memory': {
            'total': memory.total,
            'available': memory.available,
            'used': memory.used,
            'percent': memory.percent,
            'swap_total': swap.total,
            'swap_used': swap.used,
            'swap_percent': swap.percent
        },
        'disk': {
            'total': disk.total,
            'used': disk.used,
            'free': disk.free,
            'percent': disk.percent
        },
        'network': {
            'bytes_sent': net_io.bytes_sent,
            'bytes_recv': net_io.bytes_recv,
            'packets_sent': net_io.packets_sent,
            'packets_recv': net_io.packets_recv,
            'hostname': socket.gethostname(),
            'ip': socket.gethostbyname(socket.gethostname())
        },
        'system': {
            'os': platform.system(),
            'os_version': platform.version(),
            'hostname': platform.node(),
            'boot_time': boot_time.isoformat(),
            'uptime': str(uptime)
        },
        'gpu': gpu_info,
        'timestamp': datetime.now().isoformat()
    }

def update_system_metrics():
    """Update system metrics and broadcast to clients."""
    metrics = get_system_metrics()
    system_metrics.update({
        'cpu': metrics['cpu']['percent'],
        'memory': metrics['memory']['percent'],
        'gpu': metrics['gpu'],
        'network': metrics['network'],
        'last_updated': datetime.now().isoformat()
    })
    
    # Broadcast to all connected clients
    socketio.emit('system_status', metrics, namespace='/')
    
    # Schedule next update
    socketio.start_background_task(schedule_system_update)

def schedule_system_update():
    """Schedule the next system metrics update."""
    time.sleep(5)  # Update every 5 seconds
    update_system_metrics()

# Routes
@app.route('/')
def index():
    """Render the main dashboard page."""
    return render_template('index.html')

@app.route('/api/system/status', methods=['GET'])
def get_system_status():
    """Get current system status and metrics."""
    return jsonify({
        'status': 'success',
        'data': get_system_metrics()
    })

@app.route('/api/system/backends', methods=['GET'])
def get_quantum_backends():
    """Get available quantum backends with status."""
    if not QuantumBackendFactory:
        return jsonify({
            'status': 'error',
            'message': 'Quantum backends not available',
            'backends': []
        })
    
    backends = []
    for name in QuantumBackendFactory.get_available_backends():
        try:
            backend = QuantumBackendFactory.create_backend(name)
            backends.append({
                'name': name,
                'status': 'online',
                'qubits': getattr(backend, 'num_qubits', 'unknown'),
                'provider': getattr(backend, 'provider', 'unknown'),
                'type': getattr(backend, 'backend_type', 'simulator')
            })
        except Exception as e:
            backends.append({
                'name': name,
                'status': 'error',
                'error': str(e)
            })
    
    return jsonify({
        'status': 'success',
        'backends': backends
    })

@app.route('/api/backends', methods=['GET'])
def get_available_backends():
    """Get list of available quantum backends."""
    backends = QuantumBackendFactory.get_available_backends()
    backend_info = {}
    
    for backend_name in backends:
        try:
            backend_info[backend_name] = QuantumBackendFactory.get_backend_info(backend_name)
        except Exception as e:
            print(f"Error getting info for backend {backend_name}: {e}")
            backend_info[backend_name] = {
                'name': backend_name,
                'available': True,
                'error': str(e)
            }
    
    return jsonify({
        'status': 'success',
        'backends': backend_info
    })

@app.route('/api/experiments', methods=['GET', 'POST'])
def manage_experiments():
    """List all experiments or create a new one."""
    if request.method == 'POST':
        data = request.get_json()
        
        # Validate request
        if not data or 'type' not in data:
            return jsonify({
                'status': 'error',
                'message': 'Missing required fields: type'
            }), 400
            
        # Generate experiment ID
        experiment_id = f"exp_{int(time.time() * 1000)}"
        created_at = datetime.now().isoformat()
        
        # Create experiment entry
        experiment = {
            'id': experiment_id,
            'name': data.get('name', f'Experiment {experiment_id}'),
            'type': data['type'],
            'status': 'queued',
            'created_at': created_at,
            'updated_at': created_at,
            'config': data,
            'results': None,
            'metrics': {},
            'plots': {},
            'logs': []
        }
        
        # Thread-safe experiment creation
        with experiment_lock:
            experiments[experiment_id] = experiment
            active_experiments[experiment_id] = True
        
        # Log experiment creation
        log_experiment_event(experiment_id, 'experiment_created', 
                           {'message': 'Experiment created and queued for execution'})
        
        # Start experiment in background based on type
        experiment_type = data['type']
        try:
            if experiment_type == 'qaoa':
                executor.submit(run_qaoa_experiment, experiment_id, data)
            elif experiment_type == 'vqe':
                executor.submit(run_vqe_experiment, experiment_id, data)
            elif experiment_type == 'qml':
                executor.submit(run_qml_experiment, experiment_id, data)
            else:
                raise ValueError(f"Unsupported experiment type: {experiment_type}")
                
        except Exception as e:
            with experiment_lock:
                experiments[experiment_id]['status'] = 'failed'
                experiments[experiment_id]['error'] = str(e)
                experiments[experiment_id]['updated_at'] = datetime.now().isoformat()
            
            log_experiment_event(experiment_id, 'error', {
                'message': f'Failed to start experiment: {str(e)}',
                'error': str(e)
            })
            
            return jsonify({
                'status': 'error',
                'message': f'Failed to start experiment: {str(e)}',
                'experiment_id': experiment_id
            }), 500
        
        # Emit update to all connected clients
        emit_experiment_update(experiment_id)
        
        return jsonify({
            'status': 'success',
            'experiment_id': experiment_id,
            'message': 'Experiment started successfully'
        })
    
    else:  # GET request
        # Get query parameters
        limit = min(int(request.args.get('limit', 50)), 100)
        offset = max(int(request.args.get('offset', 0)), 0)
        status = request.args.get('status')
        experiment_type = request.args.get('type')
        
        # Filter experiments
        filtered_experiments = list(experiments.values())
        
        if status:
            filtered_experiments = [e for e in filtered_experiments 
                                  if e['status'].lower() == status.lower()]
        
        if experiment_type:
            filtered_experiments = [e for e in filtered_experiments 
                                  if e['type'].lower() == experiment_type.lower()]
        
        # Sort by creation date (newest first)
        filtered_experiments.sort(key=lambda x: x['created_at'], reverse=True)
        
        # Apply pagination
        paginated = filtered_experiments[offset:offset + limit]
        
        return jsonify({
            'status': 'success',
            'data': {
                'experiments': paginated,
                'pagination': {
                    'total': len(filtered_experiments),
                    'limit': limit,
                    'offset': offset,
                    'has_more': (offset + limit) < len(filtered_experiments)
                }
            }
        })

@app.route('/api/experiments/<experiment_id>', methods=['GET'])
def get_experiment(experiment_id: str):
    """Get details of a specific experiment."""
    if experiment_id not in experiments:
        return jsonify({
            'status': 'error', 
            'message': 'Experiment not found'
        }), 404
    
    return jsonify({
        'status': 'success',
        'data': experiments[experiment_id]
    })

@app.route('/api/experiments/<experiment_id>/results', methods=['GET'])
def get_experiment_results(experiment_id: str):
    """Get results for a specific experiment."""
    if experiment_id not in experiments:
        return jsonify({
            'status': 'error', 
            'message': 'Experiment not found'
        }), 404
    
    experiment = experiments[experiment_id]
    
    if 'results' not in experiment or experiment['results'] is None:
        return jsonify({
            'status': 'error',
            'message': 'No results available for this experiment yet'
        }), 404
    
    return jsonify({
        'status': 'success',
        'data': {
            'results': experiment['results'],
            'plots': experiment.get('plots', {})
        }
    })

@app.route('/api/experiments/<experiment_id>/logs', methods=['GET'])
def get_experiment_logs(experiment_id: str):
    """Get logs for a specific experiment."""
    if experiment_id not in experiments:
        return jsonify({
            'status': 'error', 
            'message': 'Experiment not found'
        }), 404
    
    return jsonify({
        'status': 'success',
        'data': {
            'logs': experiments[experiment_id].get('logs', [])
        }
    })

@app.route('/api/experiments/<experiment_id>/cancel', methods=['POST'])
def cancel_experiment(experiment_id: str):
    """Cancel a running experiment."""
    if experiment_id not in experiments:
        return jsonify({
            'status': 'error', 
            'message': 'Experiment not found'
        }), 404
    
    if experiment_id not in active_experiments:
        return jsonify({
            'status': 'error',
            'message': 'Experiment is not running'
        }), 400
    
    # Mark as cancelled
    with experiment_lock:
        experiments[experiment_id]['status'] = 'cancelled'
        experiments[experiment_id]['updated_at'] = datetime.now().isoformat()
        active_experiments.pop(experiment_id, None)
    
    log_experiment_event(experiment_id, 'experiment_cancelled', 
                        {'message': 'Experiment was cancelled by user'})
    
    # Notify clients
    emit_experiment_update(experiment_id)
    
    return jsonify({
        'status': 'success',
        'message': 'Experiment cancelled successfully'
    })

@app.route('/api/experiments/<experiment_id>/stop', methods=['POST'])
def stop_experiment(experiment_id: str):
    """Stop a running experiment."""
    if experiment_id in active_experiments:
        active_experiments[experiment_id] = False
        return jsonify({'status': 'success'})
    return jsonify({'status': 'error', 'message': 'Experiment not running'}), 400

# WebSocket event handlers
@socketio.on('connect')
def handle_connect():
    """Handle WebSocket connection."""
    client_id = request.sid
    with client_lock:
        connected_clients.add(client_id)
    
    print(f'Client connected: {client_id}')
    emit('connection_established', {'message': 'Successfully connected to WebSocket'})
    
    # Send current system status on connect
    emit('system_status', get_system_metrics())
    
    # If this is the first client, start system monitoring
    if len(connected_clients) == 1:
        socketio.start_background_task(update_system_metrics)

@socketio.on('disconnect')
def handle_disconnect():
    """Handle WebSocket disconnection."""
    client_id = request.sid
    with client_lock:
        if client_id in connected_clients:
            connected_clients.remove(client_id)
    
    print(f'Client disconnected: {client_id}')

@socketio.on('subscribe_experiment')
def handle_subscribe_experiment(data):
    """Subscribe to updates for a specific experiment."""
    experiment_id = data.get('experiment_id')
    if not experiment_id:
        emit('error', {'message': 'Missing experiment_id'})
        return
    
    # Join the experiment room
    join_room(f'experiment_{experiment_id}')
    emit('subscription_success', {
        'experiment_id': experiment_id,
        'message': f'Subscribed to updates for experiment {experiment_id}'
    })

@socketio.on('unsubscribe_experiment')
def handle_unsubscribe_experiment(data):
    """Unsubscribe from updates for a specific experiment."""
    experiment_id = data.get('experiment_id')
    if not experiment_id:
        emit('error', {'message': 'Missing experiment_id'})
        return
    
    # Leave the experiment room
    leave_room(f'experiment_{experiment_id}')
    emit('unsubscription_success', {
        'experiment_id': experiment_id,
        'message': f'Unsubscribed from updates for experiment {experiment_id}'
    })

# Helper functions
def log_experiment_event(experiment_id: str, event_type: str, data: dict):
    """Log an event for an experiment."""
    if experiment_id not in experiments:
        return
    
    log_entry = {
        'timestamp': datetime.now().isoformat(),
        'type': event_type,
        'data': data
    }
    
    with experiment_lock:
        if 'logs' not in experiments[experiment_id]:
            experiments[experiment_id]['logs'] = []
        experiments[experiment_id]['logs'].append(log_entry)
        experiments[experiment_id]['updated_at'] = datetime.now().isoformat()
    
    # Emit log event to all clients subscribed to this experiment
    socketio.emit('experiment_log', {
        'experiment_id': experiment_id,
        'log': log_entry
    }, room=f'experiment_{experiment_id}')

def emit_experiment_update(experiment_id: str):
    """Emit an update for a specific experiment to all connected clients."""
    if experiment_id not in experiments:
        return
    
    # Get the latest experiment data
    experiment_data = experiments[experiment_id]
    
    # Emit the update to all clients in the experiment room
    socketio.emit('experiment_update', {
        'experiment_id': experiment_id,
        'status': experiment_data['status'],
        'progress': experiment_data.get('progress', 0),
        'updated_at': experiment_data['updated_at'],
        'results_available': experiment_data.get('results') is not None
    }, room=f'experiment_{experiment_id}')

def run_qaoa_experiment(experiment_id: str, config: Dict[str, Any]):
    """Run a QAOA experiment in the background."""
    try:
        experiments[experiment_id]['status'] = 'running'
        emit_experiment_update(experiment_id)
        
        # Run QAOA
        results = qaoa_folding.run_qaoa_folding(
            sequence=config['sequence'],
            num_qubits=min(config.get('num_qubits', 4), len(config['sequence'])),
            p=config.get('p', 1),
            optimizer=config.get('optimizer', 'COBYLA'),
            maxiter=config.get('maxiter', 50),
            quantum_backend=config.get('quantum_backend'),
            shots=config.get('shots', 1000)
        )
        
        # Store results
        experiments[experiment_id]['results'] = results
        experiments[experiment_id]['status'] = 'completed'
        
        # Generate plots
        generate_optimization_plot(experiment_id, results)
        
    except Exception as e:
        experiments[experiment_id]['status'] = 'failed'
        experiments[experiment_id]['error'] = str(e)
        print(f"Error in QAOA experiment {experiment_id}: {e}")
    finally:
        active_experiments.pop(experiment_id, None)
        emit_experiment_update(experiment_id)

def run_vqe_experiment(experiment_id: str, config: Dict[str, Any]):
    """Run a VQE experiment in the background."""
    try:
        experiments[experiment_id]['status'] = 'running'
        emit_experiment_update(experiment_id)
        
        # Run VQE
        results = vqe_folding.run_vqe_folding(
            sequence=config['sequence'],
            num_qubits=min(config.get('num_qubits', 4), len(config['sequence'])),
            ansatz=config.get('ansatz', 'EfficientSU2'),
            optimizer=config.get('optimizer', 'COBYLA'),
            maxiter=config.get('maxiter', 50),
            quantum_backend=config.get('quantum_backend'),
            shots=config.get('shots', 1000)
        )
        
        # Store results
        experiments[experiment_id]['results'] = results
        experiments[experiment_id]['status'] = 'completed'
        
        # Generate plots
        generate_optimization_plot(experiment_id, results)
        
    except Exception as e:
        experiments[experiment_id]['status'] = 'failed'
        experiments[experiment_id]['error'] = str(e)
        print(f"Error in VQE experiment {experiment_id}: {e}")
    finally:
        active_experiments.pop(experiment_id, None)
        emit_experiment_update(experiment_id)

def run_qml_experiment(experiment_id: str, config: Dict[str, Any]):
    """Run a quantum machine learning experiment in the background."""
    try:
        experiments[experiment_id]['status'] = 'running'
        emit_experiment_update(experiment_id)
        
        # Generate some synthetic data for demonstration
        sequences = [
            'HHPPHPPH',
            'HPPHPPHH',
            'PHHPPHPH',
            'PPHHPHPH',
            'HPPHHPPH',
            'HPHPHPHP',
            'HHPHPHPH',
            'PPHHPPHH'
        ]
        
        # Random target energies for demonstration
        np.random.seed(42)
        targets = np.random.rand(len(sequences)) * 10
        
        # Create and train model
        train_dataset = QuantumProteinDataset(sequences, targets)
        train_loader = DataLoader(train_dataset, batch_size=2, shuffle=True)
        
        model = HybridQuantumProteinModel(
            input_dim=len(sequences[0]) * 20,  # 20 amino acids
            output_dim=1,
            hidden_dims=[16, 8],
            num_qubits=2,
            quantum_layers=1,
            use_sampler=False
        )
        
        metrics = train_quantum_model(
            model=model,
            train_loader=train_loader,
            num_epochs=10,
            learning_rate=0.01,
            device='cpu'
        )
        
        # Store results
        experiments[experiment_id]['results'] = {
            'metrics': metrics,
            'model_summary': str(model)
        }
        experiments[experiment_id]['status'] = 'completed'
        
        # Generate training plot
        generate_training_plot(experiment_id, metrics)
        
    except Exception as e:
        experiments[experiment_id]['status'] = 'failed'
        experiments[experiment_id]['error'] = str(e)
        print(f"Error in QML experiment {experiment_id}: {e}")
    finally:
        active_experiments.pop(experiment_id, None)
        emit_experiment_update(experiment_id)

def generate_optimization_plot(experiment_id: str, results: Dict[str, Any]):
    """Generate optimization progress plot."""
    if 'optimizer_history' not in results or not results['optimizer_history']:
        return
    
    history = results['optimizer_history']
    
    fig = go.Figure()
    
    # Add energy trace
    fig.add_trace(go.Scatter(
        y=history['energy'],
        mode='lines+markers',
        name='Energy',
        line=dict(color='blue')
    ))
    
    # Update layout
    fig.update_layout(
        title='Optimization Progress',
        xaxis_title='Iteration',
        yaxis_title='Energy',
        showlegend=True,
        template='plotly_white'
    )
    
    # Save plot
    plot_path = f'static/plots/{experiment_id}_optimization.html'
    fig.write_html(plot_path)
    
    # Store plot path in experiment
    experiments[experiment_id]['plots']['optimization'] = f'/plots/{experiment_id}_optimization.html'

def generate_training_plot(experiment_id: str, metrics: Dict[str, List[float]]):
    """Generate training progress plot."""
    fig = go.Figure()
    
    # Add training loss trace
    fig.add_trace(go.Scatter(
        y=metrics['train_loss'],
        mode='lines+markers',
        name='Training Loss',
        line=dict(color='blue')
    ))
    
    # Add validation loss trace if available
    if 'val_loss' in metrics:
        fig.add_trace(go.Scatter(
            y=metrics['val_loss'],
            mode='lines+markers',
            name='Validation Loss',
            line=dict(color='red')
        ))
    
    # Update layout
    fig.update_layout(
        title='Training Progress',
        xaxis_title='Epoch',
        yaxis_title='Loss',
        showlegend=True,
        template='plotly_white'
    )
    
    # Save plot
    plot_path = f'static/plots/{experiment_id}_training.html'
    fig.write_html(plot_path)
    
    # Store plot path in experiment
    experiments[experiment_id]['plots']['training'] = f'/plots/{experiment_id}_training.html'

def emit_experiment_update(experiment_id: str):
    """Emit experiment update to all connected clients."""
    socketio.emit('experiment_update', {
        'experiment_id': experiment_id,
        'experiment': experiments[experiment_id]
    }, namespace='/')

if __name__ == '__main__':
    # Parse command line arguments
    import argparse
    parser = argparse.ArgumentParser(description='Quantum Protein Folding Dashboard')
    parser.add_argument('--host', type=str, default='0.0.0.0', help='Host to run the server on')
    parser.add_argument('--port', type=int, default=5000, help='Port to run the server on')
    parser.add_argument('--debug', action='store_true', help='Run in debug mode')
    args = parser.parse_args()
    
    # Print startup message
    print("\n" + "="*50)
    print("Quantum Protein Folding Dashboard")
    print("="*50)
    print(f"Version: 1.0.0")
    print(f"Environment: {'development' if args.debug else 'production'}")
    print(f"Server: http://{args.host}:{args.port}")
    print("="*50 + "\n")
    
    # Create necessary directories
    os.makedirs('static/plots', exist_ok=True)
    
    # Start the WebSocket server
    socketio.run(
        app, 
        host=args.host, 
        port=args.port, 
        debug=args.debug,
        use_reloader=args.debug,
        log_output=args.debug
    )
