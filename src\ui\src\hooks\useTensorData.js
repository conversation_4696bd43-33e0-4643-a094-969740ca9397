import { useState, useEffect } from 'react';
import tensorDataService from '../services/TensorDataService';

/**
 * Hook for fetching and subscribing to tensor data
 * @param {string} tensorId - Tensor ID
 * @param {Object} options - Options
 * @returns {Object} - Tensor data and loading state
 */
function useTensorData(tensorId, options = {}) {
  const {
    useRealtime = false,
    realtimeInterval = 1000,
    useFallback = true,
    fallbackOptions = {}
  } = options;
  
  const [tensor, setTensor] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [streamControl, setStreamControl] = useState(null);
  
  // Fetch tensor data
  useEffect(() => {
    let isMounted = true;
    let unsubscribe = null;
    
    const fetchTensor = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        // Fetch tensor data
        const data = await tensorDataService.getTensor(tensorId);
        
        if (isMounted) {
          if (data) {
            setTensor(data);
          } else if (useFallback) {
            // Use fallback data if tensor not found
            setTensor(tensorDataService.generateSampleTensor(fallbackOptions));
          } else {
            setError('Tensor not found');
          }
          
          setIsLoading(false);
        }
      } catch (err) {
        if (isMounted) {
          console.error(`Error fetching tensor ${tensorId}:`, err);
          
          if (useFallback) {
            // Use fallback data on error
            setTensor(tensorDataService.generateSampleTensor(fallbackOptions));
          } else {
            setError(err.message || 'Error fetching tensor data');
          }
          
          setIsLoading(false);
        }
      }
    };
    
    // Set up real-time updates
    const setupRealtime = async () => {
      try {
        // Subscribe to tensor updates
        unsubscribe = tensorDataService.subscribeTensorUpdates(tensorId, (data) => {
          if (isMounted) {
            setTensor(data.tensor);
          }
        });
        
        // Start real-time stream if requested
        if (useRealtime) {
          const control = await tensorDataService.getRealtimeTensorStream(
            tensorId,
            realtimeInterval
          );
          
          if (isMounted) {
            setStreamControl(control);
          } else {
            // Stop stream if component unmounted
            control.stop().catch(err => console.error('Error stopping stream:', err));
          }
        }
      } catch (err) {
        if (isMounted) {
          console.error(`Error setting up real-time updates for tensor ${tensorId}:`, err);
          setError(err.message || 'Error setting up real-time updates');
        }
      }
    };
    
    // Fetch initial data
    fetchTensor();
    
    // Set up real-time updates
    setupRealtime();
    
    // Clean up
    return () => {
      isMounted = false;
      
      // Unsubscribe from updates
      if (unsubscribe) {
        unsubscribe();
      }
      
      // Stop real-time stream
      if (streamControl && streamControl.isActive) {
        streamControl.stop().catch(err => console.error('Error stopping stream:', err));
      }
    };
  }, [tensorId, useRealtime, realtimeInterval, useFallback, fallbackOptions]);
  
  // Update real-time interval
  useEffect(() => {
    if (streamControl && streamControl.isActive && useRealtime) {
      streamControl.setInterval(realtimeInterval)
        .catch(err => console.error('Error updating stream interval:', err));
    }
  }, [realtimeInterval, streamControl, useRealtime]);
  
  return {
    tensor,
    isLoading,
    error,
    refresh: async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        const data = await tensorDataService.getTensor(tensorId);
        
        if (data) {
          setTensor(data);
        } else if (useFallback) {
          setTensor(tensorDataService.generateSampleTensor(fallbackOptions));
        } else {
          setError('Tensor not found');
        }
      } catch (err) {
        console.error(`Error refreshing tensor ${tensorId}:`, err);
        
        if (useFallback) {
          setTensor(tensorDataService.generateSampleTensor(fallbackOptions));
        } else {
          setError(err.message || 'Error refreshing tensor data');
        }
      } finally {
        setIsLoading(false);
      }
    }
  };
}

export default useTensorData;
