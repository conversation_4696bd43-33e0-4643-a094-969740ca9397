Set WshShell = CreateObject("WScript.Shell")

' Change to CHAEONIX directory
WshShell.CurrentDirectory = CreateObject("Scripting.FileSystemObject").GetParentFolderName(WScript.ScriptFullName) & "\chaeonix-divine-dashboard"

' Start CHAEONIX in hidden window
WshShell.Run "cmd /c npm run dev", 0, False

' Wait 10 seconds then open browser
WScript.Sleep 10000
WshShell.Run "http://localhost:3141", 1, False
