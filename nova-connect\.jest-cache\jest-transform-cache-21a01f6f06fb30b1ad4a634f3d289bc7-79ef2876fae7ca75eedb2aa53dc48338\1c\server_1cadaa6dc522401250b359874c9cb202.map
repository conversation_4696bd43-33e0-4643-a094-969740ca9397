{"version": 3, "names": ["require", "config", "express", "cors", "helmet", "morgan", "winston", "rateLimit", "mongoose", "path", "apiRoutes", "metricsRoutes", "featureRoutes", "optimizationRoutes", "errorDemoRoutes", "monitoringRoutes", "connectorRoutes", "performanceMetricsRoutes", "csdeDashboardRoutes", "csdeBatchRoutes", "csdeAdvancedRoutes", "csdePerformanceRoutes", "metricsMiddleware", "errorMetricsMiddleware", "tracingMiddleware", "tracingErrorMiddleware", "auth", "security", "rateLimiter", "sanitizer", "httpMetricsMiddleware", "errorMetricsMiddleware2", "enhancedTracingMiddleware", "enhancedTracingErrorMiddleware", "requestLoggingMiddleware", "errorLoggingMiddleware", "<PERSON><PERSON><PERSON><PERSON>", "legacyError<PERSON><PERSON>ler", "notFoundHandler", "legacyNotFoundHandler", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "circuitBreakerHandler", "timeout<PERSON><PERSON><PERSON>", "enhancedAsyncHandler", "clusterMiddleware", "clusterHealth", "cacheMiddleware", "cacheClearMiddleware", "cacheMetrics", "cacheClear", "requireFeature", "checkFeatureLimit", "getUserSubscription", "getAvailableFeatures", "getFeatureUsage", "updateUserSubscription", "configureSecurityMiddleware", "requestMonitoringMiddleware", "metricsEndpoint", "healthCheckEndpoint", "prometheusMetrics", "googleCloudMonitoring", "tracingService", "errorHandlingService", "clusterService", "cacheService", "app", "PORT", "process", "env", "logger", "createLogger", "level", "LOG_LEVEL", "format", "combine", "timestamp", "json", "defaultMeta", "service", "transports", "<PERSON><PERSON><PERSON>", "File", "filename", "use", "limit", "u<PERSON><PERSON><PERSON>", "extended", "static", "securityHeaders", "sanitize", "windowMs", "max", "connectToMongoDB", "NODE_ENV", "info", "mongoOptions", "useNewUrlParser", "useUnifiedTopology", "serverSelectionTimeoutMS", "socketTimeoutMS", "family", "mongoURI", "MONGODB_URI", "connect", "connection", "on", "err", "error", "message", "warn", "close", "exit", "get", "req", "res", "name", "version", "description", "cluster", "mode", "links", "url", "post", "put", "ttl", "methods", "shouldCache", "noCacheParams", "some", "param", "query", "get<PERSON><PERSON><PERSON>", "split", "resource", "shouldClear", "includes", "method", "api<PERSON><PERSON><PERSON><PERSON>", "router", "sendFile", "join", "__dirname", "failureT<PERSON><PERSON>old", "resetTimeout", "timeoutMs", "retryAfter", "server", "listen", "console", "log", "enabled", "writeLog", "port", "environment", "npm_package_version", "createDashboard", "createAlertPolicy", "updateActiveConnections", "count", "connections", "reason", "module", "exports"], "sources": ["server.js"], "sourcesContent": ["/**\n * NovaConnect - Universal API Connector\n * Main Server Entry Point\n */\n\nrequire('dotenv').config();\nconst express = require('express');\nconst cors = require('cors');\nconst helmet = require('helmet');\nconst morgan = require('morgan');\nconst winston = require('winston');\nconst rateLimit = require('express-rate-limit');\nconst mongoose = require('mongoose');\nconst path = require('path');\n\n// Import routes\nconst apiRoutes = require('./api/routes');\nconst metricsRoutes = require('./api/routes/metricsRoutes');\nconst featureRoutes = require('./api/routes/featureRoutes');\nconst optimizationRoutes = require('./api/routes/optimizationRoutes');\nconst errorDemoRoutes = require('./src/routes/error-demo');\nconst monitoringRoutes = require('./src/routes/monitoring-routes');\nconst connectorRoutes = require('./src/routes/connector-routes');\nconst performanceMetricsRoutes = require('./src/routes/metrics-routes');\nconst csdeDashboardRoutes = require('./src/routes/csde-dashboard-routes');\nconst csdeBatchRoutes = require('./src/routes/csde-batch-routes');\nconst csdeAdvancedRoutes = require('./src/routes/csde-advanced-routes');\nconst csdePerformanceRoutes = require('./src/routes/csde-performance-routes');\n\n// Import middleware\nconst { metricsMiddleware, errorMetricsMiddleware } = require('./api/middleware/metricsMiddleware');\nconst { tracingMiddleware, tracingErrorMiddleware } = require('./api/middleware/tracingMiddleware');\n\n// Import security middleware\nconst { auth, security, rateLimiter, sanitizer } = require('./src/middleware');\n\n// Import enhanced monitoring middleware\nconst httpMetricsMiddleware = require('./src/monitoring/http-metrics-middleware');\nconst errorMetricsMiddleware2 = require('./src/monitoring/error-metrics-middleware');\nconst { tracingMiddleware: enhancedTracingMiddleware, tracingErrorMiddleware: enhancedTracingErrorMiddleware } = require('./src/monitoring/tracing-middleware');\nconst { requestLoggingMiddleware, errorLoggingMiddleware } = require('./src/monitoring/logging-middleware');\nconst { errorHandler: legacyErrorHandler, notFoundHandler: legacyNotFoundHandler, asyncHandler, retryHandler, circuitBreakerHandler, timeoutHandler } = require('./api/middleware/errorHandlingMiddleware');\nconst { errorHandler, notFoundHandler } = require('./src/middleware/error-handler');\nconst { asyncHandler: enhancedAsyncHandler } = require('./src/utils/async-handler');\nconst { clusterMiddleware, clusterHealth } = require('./api/middleware/clusterMiddleware');\nconst { cacheMiddleware, cacheClearMiddleware, cacheMetrics, cacheClear } = require('./api/middleware/cacheMiddleware');\nconst { requireFeature, checkFeatureLimit, getUserSubscription, getAvailableFeatures, getFeatureUsage, updateUserSubscription } = require('./api/middleware/featureFlagMiddleware');\nconst { configureSecurityMiddleware } = require('./api/middleware/securityMiddleware');\nconst { requestMonitoringMiddleware, metricsEndpoint, healthCheckEndpoint } = require('./api/middleware/monitoringMiddleware');\n\n// Import services\nconst prometheusMetrics = require('./api/services/PrometheusMetricsService');\nconst googleCloudMonitoring = require('./api/services/GoogleCloudMonitoringService');\nconst tracingService = require('./api/services/TracingService');\nconst errorHandlingService = require('./api/services/ErrorHandlingService');\nconst clusterService = require('./api/services/ClusterService');\nconst cacheService = require('./api/services/CacheService');\n\n// Create Express app\nconst app = express();\nconst PORT = process.env.PORT || 3001;\n\n// Configure logger\nconst logger = winston.createLogger({\n  level: process.env.LOG_LEVEL || 'info',\n  format: winston.format.combine(\n    winston.format.timestamp(),\n    winston.format.json()\n  ),\n  defaultMeta: { service: 'nova-connect' },\n  transports: [\n    new winston.transports.Console(),\n    new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),\n    new winston.transports.File({ filename: 'logs/combined.log' })\n  ]\n});\n\n// Middleware\napp.use(express.json({ limit: '10mb' }));\napp.use(express.urlencoded({ extended: true, limit: '10mb' }));\napp.use(morgan('dev'));\napp.use(express.static('public'));\n\n// Configure security middleware\nconfigureSecurityMiddleware(app);\n\n// Add our enhanced security middleware\napp.use(security.securityHeaders);\napp.use(security.cors());\napp.use(sanitizer.sanitize);\napp.use(rateLimiter.rateLimiter({\n  windowMs: 60 * 1000, // 1 minute\n  max: 100 // 100 requests per minute\n}));\n\n// Add monitoring middleware\napp.use(metricsMiddleware);\napp.use(tracingMiddleware);\napp.use(clusterMiddleware);\napp.use(requestMonitoringMiddleware);\n\n// Add enhanced monitoring middleware\napp.use(httpMetricsMiddleware);\napp.use(enhancedTracingMiddleware);\napp.use(requestLoggingMiddleware);\n\n// Rate limiting is now handled by the security middleware\n\n// MongoDB connection\nconst connectToMongoDB = async () => {\n  if (process.env.NODE_ENV === 'test') {\n    logger.info('Skipping MongoDB connection in test environment');\n    return;\n  }\n\n  try {\n    const mongoOptions = {\n      useNewUrlParser: true,\n      useUnifiedTopology: true,\n      serverSelectionTimeoutMS: 5000,\n      socketTimeoutMS: 45000,\n      family: 4\n    };\n\n    const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/nova-connect';\n\n    await mongoose.connect(mongoURI, mongoOptions);\n    logger.info('Connected to MongoDB successfully');\n\n    mongoose.connection.on('error', (err) => {\n      logger.error('MongoDB connection error:', { error: err.message });\n    });\n\n    mongoose.connection.on('disconnected', () => {\n      logger.warn('MongoDB disconnected. Attempting to reconnect...');\n    });\n\n    mongoose.connection.on('reconnected', () => {\n      logger.info('Reconnected to MongoDB');\n    });\n\n    process.on('SIGINT', async () => {\n      try {\n        await mongoose.connection.close();\n        logger.info('MongoDB connection closed through app termination');\n        process.exit(0);\n      } catch (err) {\n        logger.error('Error closing MongoDB connection:', { error: err.message });\n        process.exit(1);\n      }\n    });\n\n  } catch (err) {\n    logger.error('Failed to connect to MongoDB:', { error: err.message });\n  }\n};\n\n// Initialize MongoDB connection\nconnectToMongoDB();\n\n// Routes\napp.get('/', (req, res) => {\n  res.json({\n    name: 'NovaConnect',\n    version: '1.0.0',\n    description: 'Universal API Connector for seamless API integration',\n    cluster: req.cluster ? req.cluster.info : { mode: 'single' },\n    links: [\n      { name: 'Error Handling Demo', url: '/error-demo.html' },\n      { name: 'Monitoring Dashboard', url: '/monitoring-dashboard.html' },\n      { name: 'Connector Management', url: '/connector-management' },\n      { name: 'Health Check', url: '/health' },\n      { name: 'Metrics', url: '/metrics' },\n      { name: 'API Dashboard', url: '/monitoring/dashboard' },\n      { name: 'CSDE Dashboard', url: '/csde/dashboard' },\n      { name: 'CSDE Batch Processing', url: '/csde/batch' },\n      { name: 'CSDE Advanced Features', url: '/csde/advanced' },\n      { name: 'CSDE Performance', url: '/csde/performance' }\n    ]\n  });\n});\n\n// Health and monitoring endpoints\napp.get('/health', healthCheckEndpoint);\napp.get('/metrics', metricsEndpoint);\n\n// Cluster health endpoint\napp.get('/cluster/health', clusterHealth);\n\n// Cache endpoints\napp.get('/cache/metrics', cacheMetrics);\napp.post('/cache/clear', cacheClear);\n\n// Feature flag endpoints\napp.get('/subscription', getUserSubscription);\napp.get('/features', getAvailableFeatures);\napp.get('/features/usage', getFeatureUsage);\napp.put('/subscription/:userId', updateUserSubscription);\n\n// API Routes\napp.use('/api', cacheMiddleware({\n  ttl: 300, // 5 minutes\n  methods: ['GET'],\n  shouldCache: (req) => {\n    // Don't cache requests with query parameters that affect data\n    const noCacheParams = ['refresh', 'nocache', 'timestamp'];\n    return !noCacheParams.some(param => req.query[param]);\n  }\n}), apiRoutes);\n\n// Feature Routes\napp.use('/features', featureRoutes);\n\n// Optimization Routes\napp.use('/optimization', optimizationRoutes);\n\n// Clear cache for POST, PUT, DELETE requests\napp.use('/api', cacheClearMiddleware({\n  getKeys: (req) => {\n    // Clear all cache for the resource\n    const path = req.path.split('/');\n    const resource = path[1]; // First part after /api\n    return [`GET:/api/${resource}`];\n  },\n  shouldClear: (req) => ['POST', 'PUT', 'DELETE'].includes(req.method)\n}));\n\n// Metrics Routes\napp.use('/', metricsRoutes);\n\n// Error Demo Routes\napp.use('/error-demo', errorDemoRoutes);\n\n// Monitoring Routes\napp.use('/monitoring', monitoringRoutes);\n\n// Connector Routes\napp.use('/connectors', auth.apiKeyAuth, connectorRoutes);\n\n// Performance Metrics Routes\napp.use('/api/metrics', auth.apiKeyAuth, performanceMetricsRoutes);\n\n// CSDE Dashboard Routes\napp.use('/csde/dashboard', csdeDashboardRoutes.router);\n\n// CSDE Batch Processing Routes\napp.use('/csde/batch', csdeBatchRoutes.router);\n\n// CSDE Advanced Features Routes\napp.use('/csde/advanced', csdeAdvancedRoutes.router);\n\n// CSDE Performance Routes\napp.use('/csde/performance', csdePerformanceRoutes.router);\n\n// Serve connector management page\napp.get('/connector-management', (req, res) => {\n  res.sendFile(path.join(__dirname, 'public', 'connector-management.html'));\n});\n\n// Serve performance dashboard\napp.get('/performance-dashboard', (req, res) => {\n  res.sendFile(path.join(__dirname, 'public', 'performance-dashboard.html'));\n});\n\n// Serve CSDE dashboard page\napp.get('/csde/dashboard', (req, res) => {\n  res.sendFile(path.join(__dirname, 'public', 'csde-dashboard.html'));\n});\n\n// Serve CSDE batch processing page\napp.get('/csde/batch', (req, res) => {\n  res.sendFile(path.join(__dirname, 'public', 'csde-batch.html'));\n});\n\n// Serve CSDE advanced features page\napp.get('/csde/advanced', (req, res) => {\n  res.sendFile(path.join(__dirname, 'public', 'csde-advanced.html'));\n});\n\n// Serve CSDE performance page\napp.get('/csde/performance', (req, res) => {\n  res.sendFile(path.join(__dirname, 'public', 'csde-performance.html'));\n});\n\n// Serve Compliance App Store static files\napp.use('/compliance-store/static', express.static(path.join(__dirname, 'public', 'compliance-store')));\n\n// Serve Compliance App Store\napp.get('/compliance-store', (req, res) => {\n  res.sendFile(path.join(__dirname, 'public', 'compliance-store', 'index.html'));\n});\n\n// Test route for Compliance App Store\napp.get('/compliance-store-test', (req, res) => {\n  res.sendFile(path.join(__dirname, 'public', 'compliance-store', 'test.html'));\n});\n\n// Apply circuit breaker to API routes\napp.use('/api', circuitBreakerHandler({\n  resource: 'api',\n  failureThreshold: 5,\n  resetTimeout: 30000\n}));\n\n// Apply timeout to API routes\napp.use('/api', timeoutHandler({\n  timeoutMs: 30000\n}));\n\n// Apply retry handler to API routes\napp.use('/api', retryHandler({\n  retryAfter: 1\n}));\n\n// 404 handler\napp.use(notFoundHandler);\n\n// Error tracking middleware\napp.use(errorMetricsMiddleware);\napp.use(tracingErrorMiddleware);\n\n// Enhanced error tracking middleware\napp.use(errorMetricsMiddleware2);\napp.use(enhancedTracingErrorMiddleware);\napp.use(errorLoggingMiddleware);\n\n// Enhanced error handler\napp.use(errorHandler);\n\n// Start the server\nconst server = app.listen(PORT, () => {\n  logger.info(`NovaConnect server running on port ${PORT}`);\n  console.log(`NovaConnect server running on http://localhost:${PORT}`);\n\n  // Log startup to Google Cloud if enabled\n  if (googleCloudMonitoring.enabled) {\n    googleCloudMonitoring.writeLog('INFO', 'NovaConnect server started', {\n      port: PORT,\n      environment: process.env.NODE_ENV || 'development',\n      version: process.env.npm_package_version || '1.0.0'\n    });\n\n    // Create custom dashboard for NovaConnect\n    googleCloudMonitoring.createDashboard('NovaConnect Dashboard', [\n      'HTTP Request Rate',\n      'Error Rate',\n      'Response Time',\n      'Connector Health'\n    ]);\n\n    // Create alert policies\n    googleCloudMonitoring.createAlertPolicy(\n      'High Error Rate',\n      'novafuse_api_errors_total',\n      'rate(5m) > 0.05',\n      ['email']\n    );\n\n    googleCloudMonitoring.createAlertPolicy(\n      'High Response Time',\n      'novafuse_http_request_duration_seconds',\n      'avg(5m) > 1.0',\n      ['email']\n    );\n  }\n\n  // Update active connections metric\n  prometheusMetrics.updateActiveConnections(0);\n});\n\n// Track connections\nserver.on('connection', () => {\n  const count = server.connections || 1;\n  prometheusMetrics.updateActiveConnections(count);\n});\n\n// Handle graceful shutdown\nprocess.on('SIGTERM', () => {\n  logger.info('SIGTERM received, shutting down gracefully');\n\n  // Log shutdown to Google Cloud if enabled\n  if (googleCloudMonitoring.enabled) {\n    googleCloudMonitoring.writeLog('INFO', 'NovaConnect server shutting down', {\n      reason: 'SIGTERM'\n    });\n  }\n\n  server.close(() => {\n    logger.info('Server closed');\n    process.exit(0);\n  });\n});\n\nmodule.exports = app;\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEAA,OAAO,CAAC,QAAQ,CAAC,CAACC,MAAM,CAAC,CAAC;AAC1B,MAAMC,OAAO,GAAGF,OAAO,CAAC,SAAS,CAAC;AAClC,MAAMG,IAAI,GAAGH,OAAO,CAAC,MAAM,CAAC;AAC5B,MAAMI,MAAM,GAAGJ,OAAO,CAAC,QAAQ,CAAC;AAChC,MAAMK,MAAM,GAAGL,OAAO,CAAC,QAAQ,CAAC;AAChC,MAAMM,OAAO,GAAGN,OAAO,CAAC,SAAS,CAAC;AAClC,MAAMO,SAAS,GAAGP,OAAO,CAAC,oBAAoB,CAAC;AAC/C,MAAMQ,QAAQ,GAAGR,OAAO,CAAC,UAAU,CAAC;AACpC,MAAMS,IAAI,GAAGT,OAAO,CAAC,MAAM,CAAC;;AAE5B;AACA,MAAMU,SAAS,GAAGV,OAAO,CAAC,cAAc,CAAC;AACzC,MAAMW,aAAa,GAAGX,OAAO,CAAC,4BAA4B,CAAC;AAC3D,MAAMY,aAAa,GAAGZ,OAAO,CAAC,4BAA4B,CAAC;AAC3D,MAAMa,kBAAkB,GAAGb,OAAO,CAAC,iCAAiC,CAAC;AACrE,MAAMc,eAAe,GAAGd,OAAO,CAAC,yBAAyB,CAAC;AAC1D,MAAMe,gBAAgB,GAAGf,OAAO,CAAC,gCAAgC,CAAC;AAClE,MAAMgB,eAAe,GAAGhB,OAAO,CAAC,+BAA+B,CAAC;AAChE,MAAMiB,wBAAwB,GAAGjB,OAAO,CAAC,6BAA6B,CAAC;AACvE,MAAMkB,mBAAmB,GAAGlB,OAAO,CAAC,oCAAoC,CAAC;AACzE,MAAMmB,eAAe,GAAGnB,OAAO,CAAC,gCAAgC,CAAC;AACjE,MAAMoB,kBAAkB,GAAGpB,OAAO,CAAC,mCAAmC,CAAC;AACvE,MAAMqB,qBAAqB,GAAGrB,OAAO,CAAC,sCAAsC,CAAC;;AAE7E;AACA,MAAM;EAAEsB,iBAAiB;EAAEC;AAAuB,CAAC,GAAGvB,OAAO,CAAC,oCAAoC,CAAC;AACnG,MAAM;EAAEwB,iBAAiB;EAAEC;AAAuB,CAAC,GAAGzB,OAAO,CAAC,oCAAoC,CAAC;;AAEnG;AACA,MAAM;EAAE0B,IAAI;EAAEC,QAAQ;EAAEC,WAAW;EAAEC;AAAU,CAAC,GAAG7B,OAAO,CAAC,kBAAkB,CAAC;;AAE9E;AACA,MAAM8B,qBAAqB,GAAG9B,OAAO,CAAC,0CAA0C,CAAC;AACjF,MAAM+B,uBAAuB,GAAG/B,OAAO,CAAC,2CAA2C,CAAC;AACpF,MAAM;EAAEwB,iBAAiB,EAAEQ,yBAAyB;EAAEP,sBAAsB,EAAEQ;AAA+B,CAAC,GAAGjC,OAAO,CAAC,qCAAqC,CAAC;AAC/J,MAAM;EAAEkC,wBAAwB;EAAEC;AAAuB,CAAC,GAAGnC,OAAO,CAAC,qCAAqC,CAAC;AAC3G,MAAM;EAAEoC,YAAY,EAAEC,kBAAkB;EAAEC,eAAe,EAAEC,qBAAqB;EAAEC,YAAY;EAAEC,YAAY;EAAEC,qBAAqB;EAAEC;AAAe,CAAC,GAAG3C,OAAO,CAAC,0CAA0C,CAAC;AAC3M,MAAM;EAAEoC,YAAY;EAAEE;AAAgB,CAAC,GAAGtC,OAAO,CAAC,gCAAgC,CAAC;AACnF,MAAM;EAAEwC,YAAY,EAAEI;AAAqB,CAAC,GAAG5C,OAAO,CAAC,2BAA2B,CAAC;AACnF,MAAM;EAAE6C,iBAAiB;EAAEC;AAAc,CAAC,GAAG9C,OAAO,CAAC,oCAAoC,CAAC;AAC1F,MAAM;EAAE+C,eAAe;EAAEC,oBAAoB;EAAEC,YAAY;EAAEC;AAAW,CAAC,GAAGlD,OAAO,CAAC,kCAAkC,CAAC;AACvH,MAAM;EAAEmD,cAAc;EAAEC,iBAAiB;EAAEC,mBAAmB;EAAEC,oBAAoB;EAAEC,eAAe;EAAEC;AAAuB,CAAC,GAAGxD,OAAO,CAAC,wCAAwC,CAAC;AACnL,MAAM;EAAEyD;AAA4B,CAAC,GAAGzD,OAAO,CAAC,qCAAqC,CAAC;AACtF,MAAM;EAAE0D,2BAA2B;EAAEC,eAAe;EAAEC;AAAoB,CAAC,GAAG5D,OAAO,CAAC,uCAAuC,CAAC;;AAE9H;AACA,MAAM6D,iBAAiB,GAAG7D,OAAO,CAAC,yCAAyC,CAAC;AAC5E,MAAM8D,qBAAqB,GAAG9D,OAAO,CAAC,6CAA6C,CAAC;AACpF,MAAM+D,cAAc,GAAG/D,OAAO,CAAC,+BAA+B,CAAC;AAC/D,MAAMgE,oBAAoB,GAAGhE,OAAO,CAAC,qCAAqC,CAAC;AAC3E,MAAMiE,cAAc,GAAGjE,OAAO,CAAC,+BAA+B,CAAC;AAC/D,MAAMkE,YAAY,GAAGlE,OAAO,CAAC,6BAA6B,CAAC;;AAE3D;AACA,MAAMmE,GAAG,GAAGjE,OAAO,CAAC,CAAC;AACrB,MAAMkE,IAAI,GAAGC,OAAO,CAACC,GAAG,CAACF,IAAI,IAAI,IAAI;;AAErC;AACA,MAAMG,MAAM,GAAGjE,OAAO,CAACkE,YAAY,CAAC;EAClCC,KAAK,EAAEJ,OAAO,CAACC,GAAG,CAACI,SAAS,IAAI,MAAM;EACtCC,MAAM,EAAErE,OAAO,CAACqE,MAAM,CAACC,OAAO,CAC5BtE,OAAO,CAACqE,MAAM,CAACE,SAAS,CAAC,CAAC,EAC1BvE,OAAO,CAACqE,MAAM,CAACG,IAAI,CAAC,CACtB,CAAC;EACDC,WAAW,EAAE;IAAEC,OAAO,EAAE;EAAe,CAAC;EACxCC,UAAU,EAAE,CACV,IAAI3E,OAAO,CAAC2E,UAAU,CAACC,OAAO,CAAC,CAAC,EAChC,IAAI5E,OAAO,CAAC2E,UAAU,CAACE,IAAI,CAAC;IAAEC,QAAQ,EAAE,gBAAgB;IAAEX,KAAK,EAAE;EAAQ,CAAC,CAAC,EAC3E,IAAInE,OAAO,CAAC2E,UAAU,CAACE,IAAI,CAAC;IAAEC,QAAQ,EAAE;EAAoB,CAAC,CAAC;AAElE,CAAC,CAAC;;AAEF;AACAjB,GAAG,CAACkB,GAAG,CAACnF,OAAO,CAAC4E,IAAI,CAAC;EAAEQ,KAAK,EAAE;AAAO,CAAC,CAAC,CAAC;AACxCnB,GAAG,CAACkB,GAAG,CAACnF,OAAO,CAACqF,UAAU,CAAC;EAAEC,QAAQ,EAAE,IAAI;EAAEF,KAAK,EAAE;AAAO,CAAC,CAAC,CAAC;AAC9DnB,GAAG,CAACkB,GAAG,CAAChF,MAAM,CAAC,KAAK,CAAC,CAAC;AACtB8D,GAAG,CAACkB,GAAG,CAACnF,OAAO,CAACuF,MAAM,CAAC,QAAQ,CAAC,CAAC;;AAEjC;AACAhC,2BAA2B,CAACU,GAAG,CAAC;;AAEhC;AACAA,GAAG,CAACkB,GAAG,CAAC1D,QAAQ,CAAC+D,eAAe,CAAC;AACjCvB,GAAG,CAACkB,GAAG,CAAC1D,QAAQ,CAACxB,IAAI,CAAC,CAAC,CAAC;AACxBgE,GAAG,CAACkB,GAAG,CAACxD,SAAS,CAAC8D,QAAQ,CAAC;AAC3BxB,GAAG,CAACkB,GAAG,CAACzD,WAAW,CAACA,WAAW,CAAC;EAC9BgE,QAAQ,EAAE,EAAE,GAAG,IAAI;EAAE;EACrBC,GAAG,EAAE,GAAG,CAAC;AACX,CAAC,CAAC,CAAC;;AAEH;AACA1B,GAAG,CAACkB,GAAG,CAAC/D,iBAAiB,CAAC;AAC1B6C,GAAG,CAACkB,GAAG,CAAC7D,iBAAiB,CAAC;AAC1B2C,GAAG,CAACkB,GAAG,CAACxC,iBAAiB,CAAC;AAC1BsB,GAAG,CAACkB,GAAG,CAAC3B,2BAA2B,CAAC;;AAEpC;AACAS,GAAG,CAACkB,GAAG,CAACvD,qBAAqB,CAAC;AAC9BqC,GAAG,CAACkB,GAAG,CAACrD,yBAAyB,CAAC;AAClCmC,GAAG,CAACkB,GAAG,CAACnD,wBAAwB,CAAC;;AAEjC;;AAEA;AACA,MAAM4D,gBAAgB,GAAG,MAAAA,CAAA,KAAY;EACnC,IAAIzB,OAAO,CAACC,GAAG,CAACyB,QAAQ,KAAK,MAAM,EAAE;IACnCxB,MAAM,CAACyB,IAAI,CAAC,iDAAiD,CAAC;IAC9D;EACF;EAEA,IAAI;IACF,MAAMC,YAAY,GAAG;MACnBC,eAAe,EAAE,IAAI;MACrBC,kBAAkB,EAAE,IAAI;MACxBC,wBAAwB,EAAE,IAAI;MAC9BC,eAAe,EAAE,KAAK;MACtBC,MAAM,EAAE;IACV,CAAC;IAED,MAAMC,QAAQ,GAAGlC,OAAO,CAACC,GAAG,CAACkC,WAAW,IAAI,wCAAwC;IAEpF,MAAMhG,QAAQ,CAACiG,OAAO,CAACF,QAAQ,EAAEN,YAAY,CAAC;IAC9C1B,MAAM,CAACyB,IAAI,CAAC,mCAAmC,CAAC;IAEhDxF,QAAQ,CAACkG,UAAU,CAACC,EAAE,CAAC,OAAO,EAAGC,GAAG,IAAK;MACvCrC,MAAM,CAACsC,KAAK,CAAC,2BAA2B,EAAE;QAAEA,KAAK,EAAED,GAAG,CAACE;MAAQ,CAAC,CAAC;IACnE,CAAC,CAAC;IAEFtG,QAAQ,CAACkG,UAAU,CAACC,EAAE,CAAC,cAAc,EAAE,MAAM;MAC3CpC,MAAM,CAACwC,IAAI,CAAC,kDAAkD,CAAC;IACjE,CAAC,CAAC;IAEFvG,QAAQ,CAACkG,UAAU,CAACC,EAAE,CAAC,aAAa,EAAE,MAAM;MAC1CpC,MAAM,CAACyB,IAAI,CAAC,wBAAwB,CAAC;IACvC,CAAC,CAAC;IAEF3B,OAAO,CAACsC,EAAE,CAAC,QAAQ,EAAE,YAAY;MAC/B,IAAI;QACF,MAAMnG,QAAQ,CAACkG,UAAU,CAACM,KAAK,CAAC,CAAC;QACjCzC,MAAM,CAACyB,IAAI,CAAC,mDAAmD,CAAC;QAChE3B,OAAO,CAAC4C,IAAI,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,OAAOL,GAAG,EAAE;QACZrC,MAAM,CAACsC,KAAK,CAAC,mCAAmC,EAAE;UAAEA,KAAK,EAAED,GAAG,CAACE;QAAQ,CAAC,CAAC;QACzEzC,OAAO,CAAC4C,IAAI,CAAC,CAAC,CAAC;MACjB;IACF,CAAC,CAAC;EAEJ,CAAC,CAAC,OAAOL,GAAG,EAAE;IACZrC,MAAM,CAACsC,KAAK,CAAC,+BAA+B,EAAE;MAAEA,KAAK,EAAED,GAAG,CAACE;IAAQ,CAAC,CAAC;EACvE;AACF,CAAC;;AAED;AACAhB,gBAAgB,CAAC,CAAC;;AAElB;AACA3B,GAAG,CAAC+C,GAAG,CAAC,GAAG,EAAE,CAACC,GAAG,EAAEC,GAAG,KAAK;EACzBA,GAAG,CAACtC,IAAI,CAAC;IACPuC,IAAI,EAAE,aAAa;IACnBC,OAAO,EAAE,OAAO;IAChBC,WAAW,EAAE,sDAAsD;IACnEC,OAAO,EAAEL,GAAG,CAACK,OAAO,GAAGL,GAAG,CAACK,OAAO,CAACxB,IAAI,GAAG;MAAEyB,IAAI,EAAE;IAAS,CAAC;IAC5DC,KAAK,EAAE,CACL;MAAEL,IAAI,EAAE,qBAAqB;MAAEM,GAAG,EAAE;IAAmB,CAAC,EACxD;MAAEN,IAAI,EAAE,sBAAsB;MAAEM,GAAG,EAAE;IAA6B,CAAC,EACnE;MAAEN,IAAI,EAAE,sBAAsB;MAAEM,GAAG,EAAE;IAAwB,CAAC,EAC9D;MAAEN,IAAI,EAAE,cAAc;MAAEM,GAAG,EAAE;IAAU,CAAC,EACxC;MAAEN,IAAI,EAAE,SAAS;MAAEM,GAAG,EAAE;IAAW,CAAC,EACpC;MAAEN,IAAI,EAAE,eAAe;MAAEM,GAAG,EAAE;IAAwB,CAAC,EACvD;MAAEN,IAAI,EAAE,gBAAgB;MAAEM,GAAG,EAAE;IAAkB,CAAC,EAClD;MAAEN,IAAI,EAAE,uBAAuB;MAAEM,GAAG,EAAE;IAAc,CAAC,EACrD;MAAEN,IAAI,EAAE,wBAAwB;MAAEM,GAAG,EAAE;IAAiB,CAAC,EACzD;MAAEN,IAAI,EAAE,kBAAkB;MAAEM,GAAG,EAAE;IAAoB,CAAC;EAE1D,CAAC,CAAC;AACJ,CAAC,CAAC;;AAEF;AACAxD,GAAG,CAAC+C,GAAG,CAAC,SAAS,EAAEtD,mBAAmB,CAAC;AACvCO,GAAG,CAAC+C,GAAG,CAAC,UAAU,EAAEvD,eAAe,CAAC;;AAEpC;AACAQ,GAAG,CAAC+C,GAAG,CAAC,iBAAiB,EAAEpE,aAAa,CAAC;;AAEzC;AACAqB,GAAG,CAAC+C,GAAG,CAAC,gBAAgB,EAAEjE,YAAY,CAAC;AACvCkB,GAAG,CAACyD,IAAI,CAAC,cAAc,EAAE1E,UAAU,CAAC;;AAEpC;AACAiB,GAAG,CAAC+C,GAAG,CAAC,eAAe,EAAE7D,mBAAmB,CAAC;AAC7Cc,GAAG,CAAC+C,GAAG,CAAC,WAAW,EAAE5D,oBAAoB,CAAC;AAC1Ca,GAAG,CAAC+C,GAAG,CAAC,iBAAiB,EAAE3D,eAAe,CAAC;AAC3CY,GAAG,CAAC0D,GAAG,CAAC,uBAAuB,EAAErE,sBAAsB,CAAC;;AAExD;AACAW,GAAG,CAACkB,GAAG,CAAC,MAAM,EAAEtC,eAAe,CAAC;EAC9B+E,GAAG,EAAE,GAAG;EAAE;EACVC,OAAO,EAAE,CAAC,KAAK,CAAC;EAChBC,WAAW,EAAGb,GAAG,IAAK;IACpB;IACA,MAAMc,aAAa,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,WAAW,CAAC;IACzD,OAAO,CAACA,aAAa,CAACC,IAAI,CAACC,KAAK,IAAIhB,GAAG,CAACiB,KAAK,CAACD,KAAK,CAAC,CAAC;EACvD;AACF,CAAC,CAAC,EAAEzH,SAAS,CAAC;;AAEd;AACAyD,GAAG,CAACkB,GAAG,CAAC,WAAW,EAAEzE,aAAa,CAAC;;AAEnC;AACAuD,GAAG,CAACkB,GAAG,CAAC,eAAe,EAAExE,kBAAkB,CAAC;;AAE5C;AACAsD,GAAG,CAACkB,GAAG,CAAC,MAAM,EAAErC,oBAAoB,CAAC;EACnCqF,OAAO,EAAGlB,GAAG,IAAK;IAChB;IACA,MAAM1G,IAAI,GAAG0G,GAAG,CAAC1G,IAAI,CAAC6H,KAAK,CAAC,GAAG,CAAC;IAChC,MAAMC,QAAQ,GAAG9H,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1B,OAAO,CAAC,YAAY8H,QAAQ,EAAE,CAAC;EACjC,CAAC;EACDC,WAAW,EAAGrB,GAAG,IAAK,CAAC,MAAM,EAAE,KAAK,EAAE,QAAQ,CAAC,CAACsB,QAAQ,CAACtB,GAAG,CAACuB,MAAM;AACrE,CAAC,CAAC,CAAC;;AAEH;AACAvE,GAAG,CAACkB,GAAG,CAAC,GAAG,EAAE1E,aAAa,CAAC;;AAE3B;AACAwD,GAAG,CAACkB,GAAG,CAAC,aAAa,EAAEvE,eAAe,CAAC;;AAEvC;AACAqD,GAAG,CAACkB,GAAG,CAAC,aAAa,EAAEtE,gBAAgB,CAAC;;AAExC;AACAoD,GAAG,CAACkB,GAAG,CAAC,aAAa,EAAE3D,IAAI,CAACiH,UAAU,EAAE3H,eAAe,CAAC;;AAExD;AACAmD,GAAG,CAACkB,GAAG,CAAC,cAAc,EAAE3D,IAAI,CAACiH,UAAU,EAAE1H,wBAAwB,CAAC;;AAElE;AACAkD,GAAG,CAACkB,GAAG,CAAC,iBAAiB,EAAEnE,mBAAmB,CAAC0H,MAAM,CAAC;;AAEtD;AACAzE,GAAG,CAACkB,GAAG,CAAC,aAAa,EAAElE,eAAe,CAACyH,MAAM,CAAC;;AAE9C;AACAzE,GAAG,CAACkB,GAAG,CAAC,gBAAgB,EAAEjE,kBAAkB,CAACwH,MAAM,CAAC;;AAEpD;AACAzE,GAAG,CAACkB,GAAG,CAAC,mBAAmB,EAAEhE,qBAAqB,CAACuH,MAAM,CAAC;;AAE1D;AACAzE,GAAG,CAAC+C,GAAG,CAAC,uBAAuB,EAAE,CAACC,GAAG,EAAEC,GAAG,KAAK;EAC7CA,GAAG,CAACyB,QAAQ,CAACpI,IAAI,CAACqI,IAAI,CAACC,SAAS,EAAE,QAAQ,EAAE,2BAA2B,CAAC,CAAC;AAC3E,CAAC,CAAC;;AAEF;AACA5E,GAAG,CAAC+C,GAAG,CAAC,wBAAwB,EAAE,CAACC,GAAG,EAAEC,GAAG,KAAK;EAC9CA,GAAG,CAACyB,QAAQ,CAACpI,IAAI,CAACqI,IAAI,CAACC,SAAS,EAAE,QAAQ,EAAE,4BAA4B,CAAC,CAAC;AAC5E,CAAC,CAAC;;AAEF;AACA5E,GAAG,CAAC+C,GAAG,CAAC,iBAAiB,EAAE,CAACC,GAAG,EAAEC,GAAG,KAAK;EACvCA,GAAG,CAACyB,QAAQ,CAACpI,IAAI,CAACqI,IAAI,CAACC,SAAS,EAAE,QAAQ,EAAE,qBAAqB,CAAC,CAAC;AACrE,CAAC,CAAC;;AAEF;AACA5E,GAAG,CAAC+C,GAAG,CAAC,aAAa,EAAE,CAACC,GAAG,EAAEC,GAAG,KAAK;EACnCA,GAAG,CAACyB,QAAQ,CAACpI,IAAI,CAACqI,IAAI,CAACC,SAAS,EAAE,QAAQ,EAAE,iBAAiB,CAAC,CAAC;AACjE,CAAC,CAAC;;AAEF;AACA5E,GAAG,CAAC+C,GAAG,CAAC,gBAAgB,EAAE,CAACC,GAAG,EAAEC,GAAG,KAAK;EACtCA,GAAG,CAACyB,QAAQ,CAACpI,IAAI,CAACqI,IAAI,CAACC,SAAS,EAAE,QAAQ,EAAE,oBAAoB,CAAC,CAAC;AACpE,CAAC,CAAC;;AAEF;AACA5E,GAAG,CAAC+C,GAAG,CAAC,mBAAmB,EAAE,CAACC,GAAG,EAAEC,GAAG,KAAK;EACzCA,GAAG,CAACyB,QAAQ,CAACpI,IAAI,CAACqI,IAAI,CAACC,SAAS,EAAE,QAAQ,EAAE,uBAAuB,CAAC,CAAC;AACvE,CAAC,CAAC;;AAEF;AACA5E,GAAG,CAACkB,GAAG,CAAC,0BAA0B,EAAEnF,OAAO,CAACuF,MAAM,CAAChF,IAAI,CAACqI,IAAI,CAACC,SAAS,EAAE,QAAQ,EAAE,kBAAkB,CAAC,CAAC,CAAC;;AAEvG;AACA5E,GAAG,CAAC+C,GAAG,CAAC,mBAAmB,EAAE,CAACC,GAAG,EAAEC,GAAG,KAAK;EACzCA,GAAG,CAACyB,QAAQ,CAACpI,IAAI,CAACqI,IAAI,CAACC,SAAS,EAAE,QAAQ,EAAE,kBAAkB,EAAE,YAAY,CAAC,CAAC;AAChF,CAAC,CAAC;;AAEF;AACA5E,GAAG,CAAC+C,GAAG,CAAC,wBAAwB,EAAE,CAACC,GAAG,EAAEC,GAAG,KAAK;EAC9CA,GAAG,CAACyB,QAAQ,CAACpI,IAAI,CAACqI,IAAI,CAACC,SAAS,EAAE,QAAQ,EAAE,kBAAkB,EAAE,WAAW,CAAC,CAAC;AAC/E,CAAC,CAAC;;AAEF;AACA5E,GAAG,CAACkB,GAAG,CAAC,MAAM,EAAE3C,qBAAqB,CAAC;EACpC6F,QAAQ,EAAE,KAAK;EACfS,gBAAgB,EAAE,CAAC;EACnBC,YAAY,EAAE;AAChB,CAAC,CAAC,CAAC;;AAEH;AACA9E,GAAG,CAACkB,GAAG,CAAC,MAAM,EAAE1C,cAAc,CAAC;EAC7BuG,SAAS,EAAE;AACb,CAAC,CAAC,CAAC;;AAEH;AACA/E,GAAG,CAACkB,GAAG,CAAC,MAAM,EAAE5C,YAAY,CAAC;EAC3B0G,UAAU,EAAE;AACd,CAAC,CAAC,CAAC;;AAEH;AACAhF,GAAG,CAACkB,GAAG,CAAC/C,eAAe,CAAC;;AAExB;AACA6B,GAAG,CAACkB,GAAG,CAAC9D,sBAAsB,CAAC;AAC/B4C,GAAG,CAACkB,GAAG,CAAC5D,sBAAsB,CAAC;;AAE/B;AACA0C,GAAG,CAACkB,GAAG,CAACtD,uBAAuB,CAAC;AAChCoC,GAAG,CAACkB,GAAG,CAACpD,8BAA8B,CAAC;AACvCkC,GAAG,CAACkB,GAAG,CAAClD,sBAAsB,CAAC;;AAE/B;AACAgC,GAAG,CAACkB,GAAG,CAACjD,YAAY,CAAC;;AAErB;AACA,MAAMgH,MAAM,GAAGjF,GAAG,CAACkF,MAAM,CAACjF,IAAI,EAAE,MAAM;EACpCG,MAAM,CAACyB,IAAI,CAAC,sCAAsC5B,IAAI,EAAE,CAAC;EACzDkF,OAAO,CAACC,GAAG,CAAC,kDAAkDnF,IAAI,EAAE,CAAC;;EAErE;EACA,IAAIN,qBAAqB,CAAC0F,OAAO,EAAE;IACjC1F,qBAAqB,CAAC2F,QAAQ,CAAC,MAAM,EAAE,4BAA4B,EAAE;MACnEC,IAAI,EAAEtF,IAAI;MACVuF,WAAW,EAAEtF,OAAO,CAACC,GAAG,CAACyB,QAAQ,IAAI,aAAa;MAClDuB,OAAO,EAAEjD,OAAO,CAACC,GAAG,CAACsF,mBAAmB,IAAI;IAC9C,CAAC,CAAC;;IAEF;IACA9F,qBAAqB,CAAC+F,eAAe,CAAC,uBAAuB,EAAE,CAC7D,mBAAmB,EACnB,YAAY,EACZ,eAAe,EACf,kBAAkB,CACnB,CAAC;;IAEF;IACA/F,qBAAqB,CAACgG,iBAAiB,CACrC,iBAAiB,EACjB,2BAA2B,EAC3B,iBAAiB,EACjB,CAAC,OAAO,CACV,CAAC;IAEDhG,qBAAqB,CAACgG,iBAAiB,CACrC,oBAAoB,EACpB,wCAAwC,EACxC,eAAe,EACf,CAAC,OAAO,CACV,CAAC;EACH;;EAEA;EACAjG,iBAAiB,CAACkG,uBAAuB,CAAC,CAAC,CAAC;AAC9C,CAAC,CAAC;;AAEF;AACAX,MAAM,CAACzC,EAAE,CAAC,YAAY,EAAE,MAAM;EAC5B,MAAMqD,KAAK,GAAGZ,MAAM,CAACa,WAAW,IAAI,CAAC;EACrCpG,iBAAiB,CAACkG,uBAAuB,CAACC,KAAK,CAAC;AAClD,CAAC,CAAC;;AAEF;AACA3F,OAAO,CAACsC,EAAE,CAAC,SAAS,EAAE,MAAM;EAC1BpC,MAAM,CAACyB,IAAI,CAAC,4CAA4C,CAAC;;EAEzD;EACA,IAAIlC,qBAAqB,CAAC0F,OAAO,EAAE;IACjC1F,qBAAqB,CAAC2F,QAAQ,CAAC,MAAM,EAAE,kCAAkC,EAAE;MACzES,MAAM,EAAE;IACV,CAAC,CAAC;EACJ;EAEAd,MAAM,CAACpC,KAAK,CAAC,MAAM;IACjBzC,MAAM,CAACyB,IAAI,CAAC,eAAe,CAAC;IAC5B3B,OAAO,CAAC4C,IAAI,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC;AACJ,CAAC,CAAC;AAEFkD,MAAM,CAACC,OAAO,GAAGjG,GAAG", "ignoreList": []}