/**
 * Run Advanced NEPI Tests
 *
 * This script runs the advanced NEPI tests for multi-dimensional tensor entanglement,
 * domain-transition mapping, time-drift resistance, and self-healing tensor operations.
 */

const fs = require('fs');
const path = require('path');
const { runAdvancedTests } = require('./advanced-tensor-tests');

// Create reports directory if it doesn't exist
const REPORTS_DIR = path.join(__dirname, '../../reports');
try {
  if (!fs.existsSync(REPORTS_DIR)) {
    fs.mkdirSync(REPORTS_DIR, { recursive: true });
  }
} catch (error) {
  console.error(`Error creating reports directory: ${error.message}`);
}

/**
 * Run tests
 */
async function runTests() {
  console.log('Running Advanced NEPI Tests...');

  // Run the advanced tests
  const results = await runAdvancedTests();

  // Generate report
  const reportPath = generateReport(results);

  // Print summary
  printSummary(results);

  console.log(`Report saved to: ${reportPath}`);

  // Return success if all tests passed
  return results.failedTests.length === 0;
}

/**
 * Generate HTML report
 * @param {Object} results - Test results
 * @returns {string} - Report path
 */
function generateReport(results) {
  const timestamp = new Date().toISOString();
  const reportPath = path.join(REPORTS_DIR, `advanced-nepi-test-report-${timestamp}.html`);

  // Ensure reports directory exists
  try {
    if (!fs.existsSync(REPORTS_DIR)) {
      fs.mkdirSync(REPORTS_DIR, { recursive: true });
    }
  } catch (error) {
    console.error(`Error creating reports directory: ${error.message}`);
    return 'Error creating report';
  }

  // Generate HTML
  const html = `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Advanced NEPI Test Report</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
      background-color: #f5f5f5;
    }
    h1, h2, h3 {
      color: #0066cc;
    }
    .summary {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
    }
    .summary-box {
      background-color: #fff;
      border-radius: 5px;
      padding: 20px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      flex: 1;
      margin: 0 10px;
      text-align: center;
    }
    .summary-box.passed {
      border-left: 5px solid #4CAF50;
    }
    .summary-box.failed {
      border-left: 5px solid #F44336;
    }
    .summary-box.total {
      border-left: 5px solid #2196F3;
    }
    .summary-box h3 {
      margin-top: 0;
    }
    .summary-box .count {
      font-size: 36px;
      font-weight: bold;
      margin: 10px 0;
    }
    .test-results {
      margin-top: 30px;
    }
    .test {
      background-color: #fff;
      border-radius: 5px;
      padding: 20px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      margin-bottom: 20px;
    }
    .test.passed {
      border-left: 5px solid #4CAF50;
    }
    .test.failed {
      border-left: 5px solid #F44336;
    }
    .test-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
    }
    .test-name {
      font-weight: bold;
      font-size: 18px;
    }
    .test-status {
      padding: 5px 10px;
      border-radius: 3px;
      font-weight: bold;
    }
    .test-status.passed {
      background-color: #E8F5E9;
      color: #2E7D32;
    }
    .test-status.failed {
      background-color: #FFEBEE;
      color: #C62828;
    }
    .test-details {
      margin-top: 10px;
      padding: 10px;
      background-color: #f9f9f9;
      border-radius: 3px;
    }
    .test-error {
      margin-top: 10px;
      padding: 10px;
      background-color: #FFEBEE;
      border-radius: 3px;
      color: #C62828;
      font-family: monospace;
      white-space: pre-wrap;
    }
    .test-time {
      color: #666;
      font-size: 14px;
    }
    .domains {
      display: flex;
      margin-top: 5px;
    }
    .domain {
      background-color: #E3F2FD;
      color: #0D47A1;
      padding: 2px 8px;
      border-radius: 3px;
      margin-right: 5px;
      font-size: 12px;
    }
  </style>
</head>
<body>
  <h1>Advanced NEPI Test Report</h1>
  <p>Generated: ${new Date().toLocaleString()}</p>

  <div class="summary">
    <div class="summary-box passed">
      <h3>Passed</h3>
      <div class="count">${results.passedTests.length}</div>
    </div>
    <div class="summary-box failed">
      <h3>Failed</h3>
      <div class="count">${results.failedTests.length}</div>
    </div>
    <div class="summary-box total">
      <h3>Total</h3>
      <div class="count">${results.totalTests}</div>
    </div>
  </div>

  <div class="test-results">
    <h2>Test Results</h2>

    ${results.tests.map(test => `
      <div class="test ${test.passed ? 'passed' : 'failed'}">
        <div class="test-header">
          <div class="test-name">${test.name}</div>
          <div class="test-status ${test.passed ? 'passed' : 'failed'}">${test.passed ? 'PASSED' : 'FAILED'}</div>
        </div>
        <div class="test-time">Duration: ${test.duration}ms</div>
        <div class="domains">
          ${test.metadata.domains.map(domain => `<div class="domain">${domain}</div>`).join('')}
        </div>
        <div class="test-details">
          <strong>Testing Type:</strong> ${test.metadata.testingType}<br>
          <strong>Coherence Impact:</strong> ${test.metadata.coherenceImpact}
        </div>
        ${test.passed ? '' : `<div class="test-error">${test.error}</div>`}
      </div>
    `).join('')}
  </div>

  <footer>
    <p>NovaFuse Advanced NEPI Tests - Copyright © ${new Date().getFullYear()}</p>
  </footer>
</body>
</html>
  `;

  // Write report to file
  try {
    fs.writeFileSync(reportPath, html);
    return reportPath;
  } catch (error) {
    console.error(`Error writing report: ${error.message}`);
    return 'Error writing report';
  }
}

/**
 * Print test summary
 * @param {Object} results - Test results
 */
function printSummary(results) {
  console.log('\n=== Advanced NEPI Test Summary ===');
  console.log(`Total Tests: ${results.totalTests}`);
  console.log(`Passed: ${results.passedTests.length}`);
  console.log(`Failed: ${results.failedTests.length}`);

  if (results.failedTests.length > 0) {
    console.log('\nFailed Tests:');
    results.failedTests.forEach(test => {
      console.log(`- ${test.name}`);
      console.log(`  Error: ${test.error}`);
    });
  }
}

// Run tests
runTests()
  .then(success => {
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('Error running tests:', error);
    process.exit(1);
  });
