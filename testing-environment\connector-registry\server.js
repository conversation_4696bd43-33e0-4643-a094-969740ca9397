const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const bodyParser = require('body-parser');
const winston = require('winston');

const app = express();
const port = process.env.PORT || 3006;

// Configure logger
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' })
  ]
});

// Middleware
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/connector-registry', {
  useNewUrlParser: true,
  useUnifiedTopology: true
})
.then(() => {
  logger.info('Connected to MongoDB');
})
.catch((err) => {
  logger.error('Error connecting to MongoDB', { error: err.message });
});

// Define connector schema
const connectorSchema = new mongoose.Schema({
  metadata: {
    name: { type: String, required: true },
    version: { type: String, required: true },
    category: { type: String, required: true },
    description: { type: String, required: true },
    author: { type: String, required: true },
    tags: [String],
    created: { type: Date, default: Date.now },
    updated: { type: Date, default: Date.now }
  },
  authentication: {
    type: { type: String, required: true },
    fields: { type: Object, required: true },
    testConnection: {
      endpoint: { type: String, required: true },
      method: { type: String, required: true },
      expectedResponse: { type: Object }
    }
  },
  configuration: {
    baseUrl: { type: String, required: true },
    headers: { type: Object, default: {} },
    timeout: { type: Number, default: 30000 },
    retryPolicy: {
      maxRetries: { type: Number, default: 3 },
      backoffStrategy: { type: String, default: 'exponential' }
    }
  },
  endpoints: [{
    id: { type: String, required: true },
    name: { type: String, required: true },
    path: { type: String, required: true },
    method: { type: String, required: true },
    parameters: {
      query: { type: Object, default: {} },
      path: { type: Object, default: {} },
      body: { type: Object, default: {} }
    },
    response: {
      successCode: { type: Number, default: 200 },
      dataPath: { type: String },
      schema: { type: Object }
    }
  }],
  mappings: [{
    sourceEndpoint: { type: String, required: true },
    targetSystem: { type: String, required: true },
    targetEntity: { type: String, required: true },
    transformations: [{
      source: { type: String, required: true },
      target: { type: String, required: true },
      transform: { type: String, default: 'identity' }
    }]
  }],
  events: {
    webhooks: [{
      path: { type: String, required: true },
      method: { type: String, required: true },
      description: { type: String },
      handler: { type: String, required: true }
    }],
    polling: [{
      endpoint: { type: String, required: true },
      interval: { type: String, required: true },
      condition: { type: String }
    }]
  }
});

const Connector = mongoose.model('Connector', connectorSchema);

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok' });
});

// API endpoints
app.get('/connectors', async (req, res) => {
  try {
    const connectors = await Connector.find({}, 'metadata');
    res.json(connectors);
  } catch (err) {
    logger.error('Error fetching connectors', { error: err.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.get('/connectors/:id', async (req, res) => {
  try {
    const connector = await Connector.findById(req.params.id);
    if (!connector) {
      return res.status(404).json({ error: 'Connector not found' });
    }
    res.json(connector);
  } catch (err) {
    logger.error('Error fetching connector', { error: err.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.post('/connectors', async (req, res) => {
  try {
    const connector = new Connector(req.body);
    await connector.validate();
    await connector.save();
    res.status(201).json(connector);
  } catch (err) {
    logger.error('Error creating connector', { error: err.message });
    if (err.name === 'ValidationError') {
      return res.status(400).json({ error: err.message });
    }
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.put('/connectors/:id', async (req, res) => {
  try {
    const connector = await Connector.findByIdAndUpdate(
      req.params.id,
      { ...req.body, 'metadata.updated': Date.now() },
      { new: true, runValidators: true }
    );
    if (!connector) {
      return res.status(404).json({ error: 'Connector not found' });
    }
    res.json(connector);
  } catch (err) {
    logger.error('Error updating connector', { error: err.message });
    if (err.name === 'ValidationError') {
      return res.status(400).json({ error: err.message });
    }
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.delete('/connectors/:id', async (req, res) => {
  try {
    const connector = await Connector.findByIdAndDelete(req.params.id);
    if (!connector) {
      return res.status(404).json({ error: 'Connector not found' });
    }
    res.status(204).send();
  } catch (err) {
    logger.error('Error deleting connector', { error: err.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Start the server
app.listen(port, () => {
  logger.info(`Connector Registry service running on port ${port}`);
});
