"""
Example usage of ConsciousNovaFold for protein structure prediction with consciousness enhancement.
"""
import sys
import os

# Add the parent directory to the path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Now import from the src directory
from src.ConsciousNovaFold import ConsciousNovaFold, NovaFoldClient

def main():
    """Demonstrate consciousness-enhanced protein folding."""
    # Initialize the NovaFold client and conscious folder
    print("Initializing ConsciousNovaFold...")
    novafold = NovaFoldClient()
    conscious_folder = ConsciousNovaFold(novafold)
    
    # Example protein sequences (replace with your sequences of interest)
    sequences = {
        'lysozyme': 'MKALIVLGLVLLSVTVQGKVFERCELARTLKRLGMDGYRGISLANWMCLAKWESGYNTRATNYNAGDRSTDYGIFQINSRYWCNDGKTPGAVNACHLSCSALLQDNIADAVACAKRVVRDPQGIRAWVAWRNRCQNRDVRQYVQGCGV',
        'myoglobin': 'MGLSDGEWQLVLNVWGKVEADIPGHGQEVLIRLFKGHPETLEKFDKFKHLKSEDEMKASEDLKKHGATVLTALGGILKKKGHHEAEIKPLAQSHATKHKIPVKYLEFISECIIQVLQSKHPGDFGADAQGAMNKALELFRKDIAAKYKELGYQG',
        'short_peptide': 'ACDEFGHIKLMNPQRSTVWY'  # Example short sequence
    }
    
    # Process each sequence
    for name, seq in sequences.items():
        print(f"\n{'='*50}")
        print(f"Processing {name} (length: {len(seq)} amino acids)")
        
        # Fold with consciousness enhancement
        print("\nRunning consciousness-enhanced folding...")
        result = conscious_folder.fold(seq)
        
        # Display results
        print("\nConsciousness Metrics:")
        metrics = result['consciousness_metrics']
        print(f"Average Ψ-score: {metrics['average_psi']:.3f}")
        
        fib = metrics['fibonacci_alignment']
        print(f"Fibonacci Alignment: Length={len(seq)}, "
              f"Closest Fibonacci={fib['closest_fibonacci']}, "
              f"Score={fib['alignment_score']:.3f}")
        
        print("\nTrinity Validation:")
        validation = metrics['trinity_validation']
        for key, val in validation.items():
            if key != 'passed':
                status = "✓" if val['passed'] else "✗"
                print(f"  {key}: {status} (Score: {val['score']:.2f})")
        
        print(f"\nOverall Validation: {'PASSED' if validation['passed'] else 'FAILED'}")

if __name__ == "__main__":
    main()
