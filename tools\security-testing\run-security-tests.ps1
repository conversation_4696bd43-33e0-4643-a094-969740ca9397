# NovaFuse Universal Platform - Automated Security Testing
# This script runs automated security tests using OWASP ZAP

# Function to display colored output
function Write-ColorOutput {
    param(
        [string]$Text,
        [string]$ForegroundColor = "White"
    )
    
    $originalColor = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    Write-Output $Text
    $host.UI.RawUI.ForegroundColor = $originalColor
}

# Create directories for security test results
Write-ColorOutput "Creating directories for security test results..." -ForegroundColor Yellow
New-Item -ItemType Directory -Force -Path "./security-test-results" | Out-Null
New-Item -ItemType Directory -Force -Path "./security-test-results/zap" | Out-Null
New-Item -ItemType Directory -Force -Path "./security-test-results/reports" | Out-Null

# Display welcome message
Write-ColorOutput "NovaFuse Universal Platform - Automated Security Testing" -ForegroundColor Cyan
Write-ColorOutput "=======================================================" -ForegroundColor Cyan
Write-ColorOutput "This script will run automated security tests using OWASP ZAP." -ForegroundColor Cyan
Write-ColorOutput "" -ForegroundColor Cyan

# Check if Docker is running
Write-ColorOutput "Checking if Docker is running..." -ForegroundColor Yellow
$dockerRunning = $false
try {
    $dockerStatus = docker info
    $dockerRunning = $true
    Write-ColorOutput "Docker is running." -ForegroundColor Green
} catch {
    Write-ColorOutput "Docker is not running. Please start Docker and try again." -ForegroundColor Red
    exit 1
}

# Check if OWASP ZAP Docker image is available
Write-ColorOutput "Checking if OWASP ZAP Docker image is available..." -ForegroundColor Yellow
$zapImageExists = $false
try {
    $zapImage = docker images owasp/zap2docker-stable -q
    if ($zapImage) {
        $zapImageExists = $true
        Write-ColorOutput "OWASP ZAP Docker image is available." -ForegroundColor Green
    } else {
        Write-ColorOutput "OWASP ZAP Docker image is not available. Pulling image..." -ForegroundColor Yellow
        docker pull owasp/zap2docker-stable
        $zapImageExists = $true
        Write-ColorOutput "OWASP ZAP Docker image pulled successfully." -ForegroundColor Green
    }
} catch {
    Write-ColorOutput "Failed to check or pull OWASP ZAP Docker image. Error: $_" -ForegroundColor Red
    exit 1
}

# Define the target application
$targetApp = "http://localhost:3000"
$targetName = "NovaFuse"

# Ask for confirmation
Write-ColorOutput "Target application: $targetApp" -ForegroundColor Yellow
Write-ColorOutput "WARNING: Security testing can potentially impact the target application." -ForegroundColor Red
Write-ColorOutput "Do you want to continue? (Y/N)" -ForegroundColor Yellow
$confirmation = Read-Host
if ($confirmation -ne "Y" -and $confirmation -ne "y") {
    Write-ColorOutput "Security testing cancelled." -ForegroundColor Red
    exit 0
}

# Start the NovaFuse application if it's not already running
Write-ColorOutput "Checking if NovaFuse application is running..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri $targetApp -Method Head -TimeoutSec 5 -ErrorAction SilentlyContinue
    Write-ColorOutput "NovaFuse application is running." -ForegroundColor Green
} catch {
    Write-ColorOutput "NovaFuse application is not running. Starting application..." -ForegroundColor Yellow
    Start-Process -FilePath "npm" -ArgumentList "run start" -NoNewWindow
    
    # Wait for the application to start
    $maxRetries = 10
    $retryCount = 0
    $appStarted = $false
    
    while (-not $appStarted -and $retryCount -lt $maxRetries) {
        try {
            Start-Sleep -Seconds 5
            $response = Invoke-WebRequest -Uri $targetApp -Method Head -TimeoutSec 5 -ErrorAction SilentlyContinue
            $appStarted = $true
            Write-ColorOutput "NovaFuse application started successfully." -ForegroundColor Green
        } catch {
            $retryCount++
            Write-ColorOutput "Waiting for NovaFuse application to start ($retryCount/$maxRetries)..." -ForegroundColor Yellow
        }
    }
    
    if (-not $appStarted) {
        Write-ColorOutput "Failed to start NovaFuse application. Please start it manually and try again." -ForegroundColor Red
        exit 1
    }
}

# Run OWASP ZAP baseline scan
Write-ColorOutput "`nRunning OWASP ZAP baseline scan..." -ForegroundColor Green
$timestamp = Get-Date -Format "yyyyMMdd-HHmmss"
$baselineReportPath = "./security-test-results/zap/baseline-$timestamp"

try {
    docker run --rm -v ${PWD}/security-test-results/zap:/zap/wrk:rw owasp/zap2docker-stable zap-baseline.py -t $targetApp -g gen.conf -r baseline-$timestamp.html -w baseline-$timestamp.md -x baseline-$timestamp.xml
    
    if (Test-Path "$baselineReportPath.html") {
        Write-ColorOutput "Baseline scan completed successfully." -ForegroundColor Green
        Write-ColorOutput "Report saved to $baselineReportPath.html" -ForegroundColor Green
    } else {
        Write-ColorOutput "Baseline scan failed or did not generate a report." -ForegroundColor Red
    }
} catch {
    Write-ColorOutput "Failed to run OWASP ZAP baseline scan. Error: $_" -ForegroundColor Red
}

# Run OWASP ZAP full scan
Write-ColorOutput "`nRunning OWASP ZAP full scan..." -ForegroundColor Green
Write-ColorOutput "This may take a while..." -ForegroundColor Yellow
$fullScanReportPath = "./security-test-results/zap/full-scan-$timestamp"

try {
    docker run --rm -v ${PWD}/security-test-results/zap:/zap/wrk:rw owasp/zap2docker-stable zap-full-scan.py -t $targetApp -g gen.conf -r full-scan-$timestamp.html -w full-scan-$timestamp.md -x full-scan-$timestamp.xml -z "-config api.disablekey=true"
    
    if (Test-Path "$fullScanReportPath.html") {
        Write-ColorOutput "Full scan completed successfully." -ForegroundColor Green
        Write-ColorOutput "Report saved to $fullScanReportPath.html" -ForegroundColor Green
    } else {
        Write-ColorOutput "Full scan failed or did not generate a report." -ForegroundColor Red
    }
} catch {
    Write-ColorOutput "Failed to run OWASP ZAP full scan. Error: $_" -ForegroundColor Red
}

# Run OWASP ZAP API scan
Write-ColorOutput "`nRunning OWASP ZAP API scan..." -ForegroundColor Green
$apiScanReportPath = "./security-test-results/zap/api-scan-$timestamp"

try {
    docker run --rm -v ${PWD}/security-test-results/zap:/zap/wrk:rw owasp/zap2docker-stable zap-api-scan.py -t $targetApp/api -f openapi -g gen.conf -r api-scan-$timestamp.html -w api-scan-$timestamp.md -x api-scan-$timestamp.xml
    
    if (Test-Path "$apiScanReportPath.html") {
        Write-ColorOutput "API scan completed successfully." -ForegroundColor Green
        Write-ColorOutput "Report saved to $apiScanReportPath.html" -ForegroundColor Green
    } else {
        Write-ColorOutput "API scan failed or did not generate a report." -ForegroundColor Red
    }
} catch {
    Write-ColorOutput "Failed to run OWASP ZAP API scan. Error: $_" -ForegroundColor Red
}

# Generate a consolidated security report
Write-ColorOutput "`nGenerating consolidated security report..." -ForegroundColor Green
$consolidatedReportPath = "./security-test-results/reports/security-report-$timestamp.html"

$reportContent = @"
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NovaFuse Security Test Report</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            padding: 20px;
        }
        .header {
            background-color: #0A84FF;
            color: white;
            padding: 20px;
            margin-bottom: 30px;
            border-radius: 5px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border-radius: 5px;
            background-color: #f8f9fa;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .alert-high {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .alert-medium {
            background-color: #fff3cd;
            border-color: #ffeeba;
            color: #856404;
        }
        .alert-low {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        .alert-info {
            background-color: #d1e7dd;
            border-color: #badbcc;
            color: #0f5132;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>NovaFuse Security Test Report</h1>
            <p>Generated on: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")</p>
            <p>Target: $targetApp</p>
        </div>
        
        <div class="section">
            <h2>Executive Summary</h2>
            <p>This report contains the results of automated security testing performed on the NovaFuse Universal Platform using OWASP ZAP.</p>
            <p>The testing included baseline scanning, full scanning, and API scanning to identify potential security vulnerabilities.</p>
        </div>
        
        <div class="section">
            <h2>Test Results</h2>
            <p>The following security tests were performed:</p>
            <ul>
                <li><a href="../zap/baseline-$timestamp.html">Baseline Scan</a></li>
                <li><a href="../zap/full-scan-$timestamp.html">Full Scan</a></li>
                <li><a href="../zap/api-scan-$timestamp.html">API Scan</a></li>
            </ul>
        </div>
        
        <div class="section">
            <h2>Recommendations</h2>
            <p>Based on the security test results, the following recommendations are provided:</p>
            <ul>
                <li>Review and address all high and medium risk findings</li>
                <li>Implement security headers (Content-Security-Policy, X-Content-Type-Options, X-Frame-Options)</li>
                <li>Ensure proper input validation for all user inputs</li>
                <li>Implement proper authentication and authorization mechanisms</li>
                <li>Regularly update dependencies to address known vulnerabilities</li>
            </ul>
        </div>
        
        <div class="section">
            <h2>Next Steps</h2>
            <p>The following steps are recommended to improve the security posture of the NovaFuse Universal Platform:</p>
            <ol>
                <li>Review the detailed findings in the individual scan reports</li>
                <li>Prioritize and address the identified vulnerabilities</li>
                <li>Implement security best practices in the development process</li>
                <li>Conduct regular security testing as part of the CI/CD pipeline</li>
                <li>Perform manual penetration testing to identify vulnerabilities that automated tools may miss</li>
            </ol>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
"@

try {
    New-Item -ItemType Directory -Force -Path "./security-test-results/reports" | Out-Null
    Set-Content -Path $consolidatedReportPath -Value $reportContent
    Write-ColorOutput "Consolidated security report generated successfully." -ForegroundColor Green
    Write-ColorOutput "Report saved to $consolidatedReportPath" -ForegroundColor Green
} catch {
    Write-ColorOutput "Failed to generate consolidated security report. Error: $_" -ForegroundColor Red
}

# Open the consolidated report
Write-ColorOutput "`nOpening consolidated security report..." -ForegroundColor Green
try {
    Start-Process $consolidatedReportPath
} catch {
    Write-ColorOutput "Failed to open consolidated security report. Error: $_" -ForegroundColor Red
}

# Display summary
Write-ColorOutput "`nSecurity testing completed!" -ForegroundColor Green
Write-ColorOutput "Baseline scan report: $baselineReportPath.html" -ForegroundColor Green
Write-ColorOutput "Full scan report: $fullScanReportPath.html" -ForegroundColor Green
Write-ColorOutput "API scan report: $apiScanReportPath.html" -ForegroundColor Green
Write-ColorOutput "Consolidated report: $consolidatedReportPath" -ForegroundColor Green
