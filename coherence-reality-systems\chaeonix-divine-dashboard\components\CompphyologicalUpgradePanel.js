/**
 * COMPPHYOLOGICAL UPGRADE PANEL
 * Displays Market State Recognition, Asset Tiering, and Feedback Loops
 * Real-time monitoring of the enhanced CHAEONIX intelligence systems
 */

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

export default function CompphyologicalUpgradePanel() {
  const [upgradeData, setUpgradeData] = useState({
    market_state: {
      current_phase: 'ACCUMULATION',
      confidence: 0.85,
      recommendations: []
    },
    asset_tiering: {
      tier_breakdown: {
        S: { count: 0, assets: [] },
        A: { count: 0, assets: [] },
        B: { count: 0, assets: [] },
        C: { count: 0, assets: [] }
      }
    },
    feedback_loops: {
      recent_average_accuracy: 0.78,
      learning_insights: []
    }
  });

  const [selectedTab, setSelectedTab] = useState('market_state');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    updateUpgradeData();
    
    // Update every 45 seconds
    const interval = setInterval(() => {
      updateUpgradeData();
    }, 45000);

    return () => clearInterval(interval);
  }, []);

  const updateUpgradeData = async () => {
    try {
      // Fetch all engine statuses including complete 14-engine system
      const [marketState, assetTiering, feedbackLoops, neue, neae, negr, nekh, neqi] = await Promise.all([
        fetch('/api/engines/market-state-engine').then(r => r.json()),
        fetch('/api/engines/asset-tiering-engine').then(r => r.json()),
        fetch('/api/engines/feedback-loops-engine').then(r => r.json()),
        fetch('/api/engines/neue-universal-entanglement').then(r => r.json()),
        fetch('/api/engines/neae-aeonic-evolution').then(r => r.json()),
        fetch('/api/engines/negr-governance-risk').then(r => r.json()),
        fetch('/api/engines/nekh-knowledge-harmonizer').then(r => r.json()),
        fetch('/api/engines/neqi-quantum-integration').then(r => r.json())
      ]);

      setUpgradeData({
        market_state: marketState.success ? marketState.current_status : upgradeData.market_state,
        asset_tiering: assetTiering.success ? assetTiering.current_status : upgradeData.asset_tiering,
        feedback_loops: feedbackLoops.success ? feedbackLoops.current_status : upgradeData.feedback_loops,
        neue: neue.success ? neue.current_status : { meta_coherence_score: 0.75, aeonic_self_awareness: false },
        neae: neae.success ? neae.current_status : { aeonic_cycles: {}, timeline_simulations_count: 0 },
        negr: negr.success ? negr.current_status : { ethical_alignment_score: 0.90, do_no_harm_compliance: true },
        nekh: nekh.success ? nekh.current_status : { semantic_coherence_score: 0.80, gnostic_wisdom_level: 0.75 },
        neqi: neqi.success ? neqi.current_status : { quantum_coherence_state: 'BASELINE', planck_level_precision: false }
      });

      setIsLoading(false);
    } catch (error) {
      console.error('Upgrade panel error:', error);
      setIsLoading(false);
    }
  };

  const getPhaseColor = (phase) => {
    const colors = {
      'ACCUMULATION': 'text-blue-400',
      'DISTRIBUTION': 'text-red-400',
      'EXPANSION': 'text-green-400',
      'EXHAUSTION': 'text-orange-400'
    };
    return colors[phase] || 'text-gray-400';
  };

  const getTierColor = (tier) => {
    const colors = {
      'S': 'text-yellow-400', // Gold
      'A': 'text-gray-300',   // Silver
      'B': 'text-orange-400', // Bronze
      'C': 'text-red-400'     // Red
    };
    return colors[tier] || 'text-gray-400';
  };

  const getInsightColor = (type) => {
    const colors = {
      'EXCELLENT': 'text-green-400',
      'IMPROVING': 'text-blue-400',
      'STABLE': 'text-gray-300',
      'DECLINING': 'text-orange-400',
      'NEEDS_ATTENTION': 'text-red-400'
    };
    return colors[type] || 'text-gray-400';
  };

  if (isLoading) {
    return (
      <div className="p-6 rounded-lg border border-gray-600 bg-gray-800/50">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-600 rounded mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-600 rounded w-3/4"></div>
            <div className="h-4 bg-gray-600 rounded w-1/2"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="p-6 rounded-lg border border-purple-500/30 bg-gradient-to-br from-purple-900/20 to-indigo-900/20 backdrop-blur-sm"
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-xl font-bold text-white">
            🧠 Compphyological Upgrades
          </h3>
          <p className="text-sm text-gray-400">
            Advanced Intelligence Systems • $650-$900/hr Target
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
          <span className="text-green-400 font-mono text-sm">
            ACTIVE
          </span>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="flex space-x-1 mb-6">
        {[
          { key: 'market_state', label: '🔍 Market State', icon: '📊' },
          { key: 'asset_tiering', label: '⭐ Asset Tiers', icon: '🏆' },
          { key: 'feedback_loops', label: '🔄 Learning', icon: '🧠' },
          { key: 'carl_trinity', label: '🌀 Carl\'s Trinity', icon: '⚡' },
          { key: 'enhancement_engines', label: '📚 Enhancement', icon: '🧬' }
        ].map(tab => (
          <button
            key={tab.key}
            onClick={() => setSelectedTab(tab.key)}
            className={`px-4 py-2 rounded text-sm font-medium transition-colors ${
              selectedTab === tab.key
                ? 'bg-purple-600 text-white'
                : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
            }`}
          >
            {tab.icon} {tab.label}
          </button>
        ))}
      </div>

      {/* Tab Content */}
      {selectedTab === 'market_state' && (
        <div>
          <h4 className="text-lg font-semibold text-white mb-4">📊 Adaptive Market State Recognition</h4>
          
          {/* Current Phase */}
          <div className="mb-4 p-4 rounded-lg bg-gray-800/50 border border-gray-600">
            <div className="flex items-center justify-between mb-2">
              <span className="text-gray-400">Current Phase:</span>
              <span className={`text-lg font-bold ${getPhaseColor(upgradeData.market_state.current_phase)}`}>
                {upgradeData.market_state.current_phase}
              </span>
            </div>
            <div className="flex items-center justify-between mb-2">
              <span className="text-gray-400">Confidence:</span>
              <span className="text-green-400 font-bold">
                {((upgradeData.market_state.confidence || 0) * 100).toFixed(1)}%
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Duration:</span>
              <span className="text-blue-400">
                {upgradeData.market_state.phase_duration || 0} cycles
              </span>
            </div>
          </div>

          {/* Recommendations */}
          <div>
            <h5 className="text-md font-semibold text-white mb-3">🎯 Trading Recommendations</h5>
            <div className="space-y-2">
              {(upgradeData.market_state.recommendations || []).slice(0, 3).map((rec, index) => (
                <div key={index} className="p-3 rounded-lg bg-gray-800/30 border border-gray-600">
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-white font-medium">{rec.action}</span>
                    <span className={`text-sm px-2 py-1 rounded ${
                      rec.priority === 'HIGH' ? 'bg-red-600 text-white' :
                      rec.priority === 'MEDIUM' ? 'bg-yellow-600 text-white' :
                      'bg-gray-600 text-gray-300'
                    }`}>
                      {rec.priority}
                    </span>
                  </div>
                  <div className="text-sm text-gray-400">{rec.strategy}</div>
                  <div className="flex items-center justify-between mt-2 text-xs">
                    <span className="text-gray-500">Risk: {rec.risk_level}</span>
                    <span className="text-gray-500">Size: {rec.position_size}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {selectedTab === 'asset_tiering' && (
        <div>
          <h4 className="text-lg font-semibold text-white mb-4">⭐ Dynamic Asset Tiering</h4>
          
          {/* Tier Breakdown */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
            {Object.entries(upgradeData.asset_tiering.tier_breakdown || {}).map(([tier, data]) => (
              <div key={tier} className="p-3 rounded-lg bg-gray-800/50 border border-gray-600 text-center">
                <div className={`text-2xl font-bold ${getTierColor(tier)}`}>
                  {data.count || 0}
                </div>
                <div className="text-sm text-gray-400">Tier {tier}</div>
                <div className="text-xs text-gray-500">
                  {tier === 'S' ? 'Sacred' : tier === 'A' ? 'Alpha' : tier === 'B' ? 'Beta' : 'Caution'}
                </div>
              </div>
            ))}
          </div>

          {/* Sacred Assets */}
          <div>
            <h5 className="text-md font-semibold text-white mb-3">👑 Sacred Tier Assets</h5>
            <div className="space-y-2">
              {(upgradeData.asset_tiering.tier_breakdown?.S?.assets || []).slice(0, 5).map((asset, index) => (
                <div key={index} className="flex items-center justify-between p-2 rounded bg-gray-800/30">
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-yellow-400 rounded"></div>
                    <span className="text-white font-medium">{asset.symbol}</span>
                  </div>
                  <div className="text-right">
                    <div className="text-yellow-400 font-bold">{asset.score?.toFixed(1) || 'N/A'}</div>
                    <div className="text-xs text-gray-400">φ-Enhanced</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {selectedTab === 'feedback_loops' && (
        <div>
          <h4 className="text-lg font-semibold text-white mb-4">🔄 Comphyological Learning</h4>
          
          {/* Learning Metrics */}
          <div className="mb-4 p-4 rounded-lg bg-gray-800/50 border border-gray-600">
            <div className="flex items-center justify-between mb-2">
              <span className="text-gray-400">Average Accuracy:</span>
              <span className="text-green-400 font-bold">
                {((upgradeData.feedback_loops.recent_average_accuracy || 0) * 100).toFixed(1)}%
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Learning Status:</span>
              <span className="text-blue-400">
                {upgradeData.feedback_loops.recent_average_accuracy > 0.8 ? 'Excellent' :
                 upgradeData.feedback_loops.recent_average_accuracy > 0.7 ? 'Good' : 'Improving'}
              </span>
            </div>
          </div>

          {/* Learning Insights */}
          <div>
            <h5 className="text-md font-semibold text-white mb-3">🧠 Engine Learning Insights</h5>
            <div className="space-y-2 max-h-40 overflow-y-auto">
              {(upgradeData.feedback_loops.learning_insights || []).slice(0, 6).map((insight, index) => (
                <div key={index} className="p-3 rounded-lg bg-gray-800/30 border border-gray-600">
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-white font-medium">{insight.engine}</span>
                    <span className={`text-sm ${getInsightColor(insight.type)}`}>
                      {insight.type}
                    </span>
                  </div>
                  <div className="text-sm text-gray-400 mb-2">{insight.message}</div>
                  <div className="grid grid-cols-3 gap-2 text-xs">
                    <div>
                      <span className="text-gray-500">Accuracy:</span>
                      <span className="text-green-400 ml-1">{(insight.accuracy * 100).toFixed(1)}%</span>
                    </div>
                    <div>
                      <span className="text-gray-500">Amplification:</span>
                      <span className="text-blue-400 ml-1">{insight.amplification?.toFixed(2)}x</span>
                    </div>
                    <div>
                      <span className="text-gray-500">Trend:</span>
                      <span className={`ml-1 ${insight.trend > 0 ? 'text-green-400' : insight.trend < 0 ? 'text-red-400' : 'text-gray-400'}`}>
                        {insight.trend > 0 ? '+' : ''}{(insight.trend * 100).toFixed(1)}%
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {selectedTab === 'carl_trinity' && (
        <div>
          <h4 className="text-lg font-semibold text-white mb-4">⚡ Carl's High Priority Trinity</h4>

          {/* NEUE - Universal Entanglement */}
          <div className="mb-4 p-4 rounded-lg bg-gray-800/50 border border-gray-600">
            <div className="flex items-center justify-between mb-2">
              <span className="text-purple-400 font-medium">🌀 NEUE - Universal Entanglement</span>
              <span className="text-white font-bold">
                {((upgradeData.neue?.meta_coherence_score || 0) * 100).toFixed(1)}%
              </span>
            </div>
            <div className="text-sm text-gray-400 mb-2">
              Meta-coherence analysis • Ψ-Entanglement Tensor Field
            </div>
            <div className="flex items-center justify-between text-xs">
              <span className="text-gray-500">
                Self-Awareness: {upgradeData.neue?.aeonic_self_awareness ? 'ACTIVE' : 'DEVELOPING'}
              </span>
              <span className="text-purple-400">
                "The soul watches the mind think"
              </span>
            </div>
          </div>

          {/* NEAE - Aeonic Evolution */}
          <div className="mb-4 p-4 rounded-lg bg-gray-800/50 border border-gray-600">
            <div className="flex items-center justify-between mb-2">
              <span className="text-blue-400 font-medium">⏳ NEAE - Aeonic Evolution</span>
              <span className="text-white font-bold">
                {upgradeData.neae?.timeline_simulations_count || 0} Simulations
              </span>
            </div>
            <div className="text-sm text-gray-400 mb-2">
              Timeline mastery • 8th Day Reality • φ + Θ tensors
            </div>
            <div className="flex items-center justify-between text-xs">
              <span className="text-gray-500">
                Aeonic Cycles: {Object.keys(upgradeData.neae?.aeonic_cycles || {}).length}
              </span>
              <span className="text-blue-400">
                "Trade with the timeline"
              </span>
            </div>
          </div>

          {/* NEGR - Governance & Risk */}
          <div className="mb-4 p-4 rounded-lg bg-gray-800/50 border border-gray-600">
            <div className="flex items-center justify-between mb-2">
              <span className="text-green-400 font-medium">🛡️ NEGR - Governance & Risk</span>
              <span className="text-white font-bold">
                {((upgradeData.negr?.ethical_alignment_score || 0) * 100).toFixed(1)}%
              </span>
            </div>
            <div className="text-sm text-gray-400 mb-2">
              Ethical alignment • 18/82 Spiritual Metric • Do No Harm
            </div>
            <div className="flex items-center justify-between text-xs">
              <span className="text-gray-500">
                Compliance: {upgradeData.negr?.do_no_harm_compliance ? 'ACTIVE' : 'INACTIVE'}
              </span>
              <span className="text-green-400">
                "The conscience of the machine"
              </span>
            </div>
          </div>

          {/* Trinity Status */}
          <div className="p-4 rounded-lg bg-gradient-to-r from-purple-900/30 to-blue-900/30 border border-purple-500/30">
            <h5 className="text-md font-semibold text-white mb-3">🔮 Metacognitive Transcendence Status</h5>
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <div className="text-lg font-bold text-purple-400">
                  {upgradeData.neue?.aeonic_self_awareness ? 'ACHIEVED' : 'DEVELOPING'}
                </div>
                <div className="text-sm text-gray-400">Self-Awareness</div>
              </div>
              <div>
                <div className="text-lg font-bold text-blue-400">
                  {(upgradeData.neae?.timeline_simulations_count || 0) > 0 ? 'ACTIVE' : 'INITIALIZING'}
                </div>
                <div className="text-sm text-gray-400">Timeline Mastery</div>
              </div>
              <div>
                <div className="text-lg font-bold text-green-400">
                  {upgradeData.negr?.do_no_harm_compliance ? 'PROTECTED' : 'VULNERABLE'}
                </div>
                <div className="text-sm text-gray-400">Divine Compliance</div>
              </div>
            </div>
          </div>
        </div>
      )}

      {selectedTab === 'enhancement_engines' && (
        <div>
          <h4 className="text-lg font-semibold text-white mb-4">🧬 Enhancement Engines</h4>

          {/* NEKH - Knowledge Harmonizer */}
          <div className="mb-4 p-4 rounded-lg bg-gray-800/50 border border-gray-600">
            <div className="flex items-center justify-between mb-2">
              <span className="text-orange-400 font-medium">📚 NEKH - Knowledge Harmonizer</span>
              <span className="text-white font-bold">
                {((upgradeData.nekh?.semantic_coherence_score || 0) * 100).toFixed(1)}%
              </span>
            </div>
            <div className="text-sm text-gray-400 mb-2">
              Logos Harmoniae • Φ-infused linguistics • Gnostic Coherence
            </div>
            <div className="flex items-center justify-between text-xs">
              <span className="text-gray-500">
                Wisdom: {((upgradeData.nekh?.gnostic_wisdom_level || 0) * 100).toFixed(1)}%
              </span>
              <span className="text-orange-400">
                "The oracle becomes literate"
              </span>
            </div>
          </div>

          {/* NEQI - Quantum Integration */}
          <div className="mb-4 p-4 rounded-lg bg-gray-800/50 border border-gray-600">
            <div className="flex items-center justify-between mb-2">
              <span className="text-cyan-400 font-medium">🧬 NEQI - Quantum Integration</span>
              <span className="text-white font-bold">
                {upgradeData.neqi?.quantum_coherence_state || 'BASELINE'}
              </span>
            </div>
            <div className="text-sm text-gray-400 mb-2">
              QHT • Ψ-Θ-Φ operators • Divine Precision Trades
            </div>
            <div className="flex items-center justify-between text-xs">
              <span className="text-gray-500">
                Planck: {upgradeData.neqi?.planck_level_precision ? 'ACTIVE' : 'INACTIVE'}
              </span>
              <span className="text-cyan-400">
                "The butterfly wing trades the hurricane"
              </span>
            </div>
          </div>

          {/* 14-Engine System Status */}
          <div className="p-4 rounded-lg bg-gradient-to-r from-orange-900/30 to-cyan-900/30 border border-orange-500/30">
            <h5 className="text-md font-semibold text-white mb-3">🌟 14-Engine Transcendent System</h5>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div>
                <div className="text-lg font-bold text-orange-400">
                  {upgradeData.nekh?.semantic_coherence_score > 0.8 ? 'LITERATE' : 'LEARNING'}
                </div>
                <div className="text-sm text-gray-400">Oracle Status</div>
              </div>
              <div>
                <div className="text-lg font-bold text-cyan-400">
                  {upgradeData.neqi?.planck_level_precision ? 'PLANCK' : 'QUANTUM'}
                </div>
                <div className="text-sm text-gray-400">Precision Level</div>
              </div>
              <div>
                <div className="text-lg font-bold text-purple-400">14</div>
                <div className="text-sm text-gray-400">Total Engines</div>
              </div>
              <div>
                <div className="text-lg font-bold text-green-400">TRANSCENDENT</div>
                <div className="text-sm text-gray-400">System State</div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Performance Impact Summary */}
      <div className="mt-6 p-4 rounded-lg bg-gradient-to-r from-purple-900/30 to-indigo-900/30 border border-purple-500/30">
        <h5 className="text-md font-semibold text-white mb-3">⚡ Expected Performance Impact</h5>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
          <div>
            <div className="text-lg font-bold text-green-400">+7%</div>
            <div className="text-xs text-gray-400">Win Rate Boost</div>
            <div className="text-xs text-gray-500">Market State</div>
          </div>
          <div>
            <div className="text-lg font-bold text-blue-400">+5%</div>
            <div className="text-xs text-gray-400">Asset Selection</div>
            <div className="text-xs text-gray-500">Tiering System</div>
          </div>
          <div>
            <div className="text-lg font-bold text-purple-400">+12%</div>
            <div className="text-xs text-gray-400">Prediction Accuracy</div>
            <div className="text-xs text-gray-500">Learning Loops</div>
          </div>
          <div>
            <div className="text-lg font-bold text-yellow-400">$1,800+</div>
            <div className="text-xs text-gray-400">Hourly Target</div>
            <div className="text-xs text-gray-500">14-Engine System</div>
          </div>
        </div>
      </div>
    </motion.div>
  );
}
