/**
 * Preferences Module
 * 
 * This module exports preferences-related components and utilities.
 */

// Export components
export { default as PreferencesManager } from './PreferencesManager';
export { default as DashboardPreferences } from './DashboardPreferences';

// Export context and hooks
export { PreferencesProvider, usePreferences } from './PreferencesContext';

// Export default preferences
export { defaultPreferences } from './defaultPreferences';
