# PowerShell script to generate a comprehensive file inventory
$outputFile = "$PSScriptRoot\CODEBASE_INVENTORY_$(Get-Date -Format 'yyyyMMdd_HHmmss').md"
$baseDir = "d:\novafuse-api-superstore"

# Create output directory if it doesn't exist
$outputDir = [System.IO.Path]::GetDirectoryName($outputFile)
if (-not (Test-Path $outputDir)) {
    New-Item -ItemType Directory -Path $outputDir -Force | Out-Null
}

# Function to get file information
function Get-FileInventory {
    param (
        [string]$directory = $PSScriptRoot,
        [string]$outputFile
    )
    
    # Get all files recursively, excluding node_modules and other common directories
    $files = Get-ChildItem -Path $directory -Recurse -File | 
        Where-Object { $_.FullName -notmatch '\\node_modules\\' -and 
                     $_.FullName -notmatch '\\.git\\' -and
                     $_.FullName -notmatch '\\dist\\' -and
                     $_.FullName -notmatch '\\build\\' }
    
    # Group files by directory
    $filesByDir = $files | Group-Object DirectoryName
    
    # Group files by date (year-month)
    $filesByDate = $files | Group-Object { $_.LastWriteTime.ToString('yyyy-MM') } | Sort-Object Name -Descending
    
    # Generate markdown content
    $markdown = "# Codebase Inventory
*Generated on $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')*

## Summary
- **Total Files**: $($files.Count)
- **Total Size**: $([math]::Round(($files | Measure-Object -Property Length -Sum).Sum / 1MB, 2)) MB
- **Earliest Modified**: $(($files | Sort-Object LastWriteTime | Select-Object -First 1).LastWriteTime)
- **Most Recent**: $(($files | Sort-Object LastWriteTime -Descending | Select-Object -First 1).LastWriteTime)

## Files by Date (Newest First)
"
    
    # Add files grouped by date
    foreach ($dateGroup in $filesByDate) {
        $markdown += "### $($dateGroup.Name)"
        $markdown += "`n| File | Size | Last Modified | Path |"
        $markdown += "`n|------|------|--------------|------|"
        
        foreach ($file in ($dateGroup.Group | Sort-Object LastWriteTime -Descending)) {
            $relativePath = $file.FullName.Replace($baseDir, '').TrimStart('\\')
            $markdown += "`n| $($file.Name) | $($file.Length/1KB | [math]::Round(2)) KB | $($file.LastWriteTime) | $($relativePath) |"
        }
        $markdown += "`n"
    }
    
    # Add directory structure
    $markdown += "`n## Directory Structure`n"
    $markdown += "```javascript
"
    function Get-DirectoryTree {
        param (
            [string]$path,
            [string]$indent = ''
        )
        
        $dir = Get-Item $path
        $result = $indent + $dir.Name + "\n"
        
        $subDirs = Get-ChildItem -Path $path -Directory | Sort-Object Name
        $files = Get-ChildItem -Path $path -File | Sort-Object Name
        
        foreach ($subDir in $subDirs) {
            $result += Get-DirectoryTree -path $subDir.FullName -indent ($indent + '    ')
        }
        
        foreach ($file in $files) {
            $result += $indent + '    ' + $file.Name + "\n"
        }
        
        return $result
    }
    
    $markdown += Get-DirectoryTree -path $baseDir
    $markdown += "
```"
    
    # Write to file
    try {
        $markdown | Out-File -FilePath $outputFile -Encoding utf8 -Force
        Write-Host "Inventory generated: $outputFile"
    } catch {
        Write-Error "Error writing to file: $_"
        $outputFile = ".\CODEBASE_INVENTORY_$(Get-Date -Format 'yyyyMMdd_HHmmss').md"
        $markdown | Out-File -FilePath $outputFile -Encoding utf8 -Force
        Write-Host "Inventory generated in current directory: $((Get-Item $outputFile).FullName)"
    }

    
    # Write to file
    $markdown | Out-File -FilePath $outputFile -Encoding utf8
    
    Write-Host "Inventory generated: $outputFile"
}

# Execute the function
Get-FileInventory -directory $baseDir -outputFile $outputFile
/**
 * Quantum Stream Utilities
 * Implements optimized data streaming with quantum-delta compression
 */

class QuantumStream {
  constructor(options = {}) {
    this.options = {
      chunkSize: 65536, // 64KB chunks
      compression: 'quantum-delta',
      precision: 'auto',
      ...options
    };
    
    this.decoder = new TextDecoder();
    this.encoder = new TextEncoder();
    this.chunkQueue = [];
    this.isProcessing = false;
  }

  /**
   * Compress data using quantum-delta algorithm
   * @param {ArrayBuffer} data - Input data to compress
   * @returns {Promise<ArrayBuffer>} Compressed data
   */
  async compress(data) {
    if (!(data instanceof ArrayBuffer)) {
      data = this.encoder.encode(JSON.stringify(data)).buffer;
    }

    // Use WebAssembly compression if available
    if (this.wasmModule) {
      return this.compressWasm(data);
    }

    // Fallback to JavaScript implementation
    return this.compressJS(data);
  }

  /**
   * Decompress data using quantum-delta algorithm
   * @param {ArrayBuffer} data - Compressed data
   * @returns {Promise<Object>} Decompressed data
   */
  async decompress(data) {
    if (!(data instanceof ArrayBuffer)) {
      throw new Error('Input must be an ArrayBuffer');
    }

    // Use WebAssembly decompression if available
    if (this.wasmModule) {
      const result = await this.decompressWasm(data);
      try {
        return JSON.parse(this.decoder.decode(result));
      } catch (e) {
        return result; // Return as ArrayBuffer if not JSON
      }
    }

    // Fallback to JavaScript implementation
    return this.decompressJS(data);
  }

  /**
   * Stream data in chunks with compression
   * @param {ReadableStream} source - Source stream
   * @param {Function} onChunk - Callback for each processed chunk
   * @returns {Promise<void>}
   */
  async stream(source, onChunk) {
    const reader = source.getReader();
    let buffer = new Uint8Array();
    
    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        
        // Append new data to buffer
        const newBuffer = new Uint8Array(buffer.length + value.length);
        newBuffer.set(buffer);
        newBuffer.set(value, buffer.length);
        buffer = newBuffer;
        
        // Process complete chunks
        while (buffer.length >= this.options.chunkSize) {
          const chunk = buffer.slice(0, this.options.chunkSize);
          buffer = buffer.slice(this.options.chunkSize);
          
          // Process chunk in background
          this.processChunk(chunk, onChunk);
        }
      }
      
      // Process remaining data
      if (buffer.length > 0) {
        await this.processChunk(buffer, onChunk);
      }
      
      // Wait for all chunks to be processed
      await this.waitForQueue();
      
    } catch (error) {
      console.error('Stream error:', error);
      throw error;
    } finally {
      reader.releaseLock();
    }
  }

  /**
   * Process a single chunk of data
   * @private
   */
  async processChunk(chunk, onChunk) {
    this.chunkQueue.push(async () => {
      try {
        const compressed = await this.compress(chunk);
        await onChunk(compressed);
      } catch (error) {
        console.error('Error processing chunk:', error);
      }
    });
    
    if (!this.isProcessing) {
      this.processQueue();
    }
  }

  /**
   * Process the chunk queue
   * @private
   */
  async processQueue() {
    if (this.chunkQueue.length === 0) {
      this.isProcessing = false;
      return;
    }
    
    this.isProcessing = true;
    const processNext = this.chunkQueue.shift();
    
    try {
      await processNext();
    } finally {
      // Process next chunk in queue
      setImmediate(() => this.processQueue());
    }
  }

  /**
   * Wait for all queued chunks to be processed
   * @returns {Promise<void>}
   */
  async waitForQueue() {
    while (this.isProcessing || this.chunkQueue.length > 0) {
      await new Promise(resolve => setTimeout(resolve, 10));
    }
  }

  // WebAssembly compression/decompression methods would be implemented here
  // These are placeholders for the actual implementation
  
  async compressWasm(data) {
    // Implementation would use WebAssembly module
    throw new Error('WebAssembly compression not implemented');
  }
  
  async decompressWasm(data) {
    // Implementation would use WebAssembly module
    throw new Error('WebAssembly decompression not implemented');
  }
  
  async compressJS(data) {
    // Simple JavaScript implementation as fallback
    return data;
  }
  
  async decompressJS(data) {
    // Simple JavaScript implementation as fallback
    return data;
  }
}

// Helper function for creating optimized streams
export function createOptimizedStream(options = {}) {
  return new QuantumStream({
    compression: 'quantum-delta',
    precision: 'auto',
    ...options
  });
}

// Example usage:
/*
async function exampleStreamUsage() {
  const stream = createOptimizedStream();
  
  // Example with fetch
  const response = await fetch('https://api.example.com/quantum-data');
  const reader = response.body.getReader();
  
  await stream.stream(
    new ReadableStream({
      async start(controller) {
        while (true) {
          const { done, value } = await reader.read();
          if (done) {
            controller.close();
            break;
          }
          controller.enqueue(value);
        }
      }
    }),
    async (compressedChunk) => {
      // Process each compressed chunk
      const data = await stream.decompress(compressedChunk);
      console.log('Processed chunk:', data);
    }
  );
}
*/

export default QuantumStream;
