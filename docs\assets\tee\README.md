# TEE Framework Assets

This directory contains all assets related to the Time-Energy-Efficiency (TEE) framework, a core component of the Comphyology system.

## Core Components

### 1. TEE Calculator
- **File**: `tee-calculator.html`
- **Purpose**: Interactive tool for calculating and optimizing TEE scores
- **Features**:
  - Real-time TEE score calculation
  - Visual metrics and progress indicators
  - Radar chart for multi-dimensional analysis
  - Responsive design for all devices

### 2. TEE Energy Flow Diagram
- **File**: `tee-energy-flow.md`
- **Preview**: `preview.html`
- **Purpose**: Visualizes energy transformation through systems
- **Components**:
  - Input energy sources
  - Friction points (F)
  - Efficiency conversion (η)
  - Output optimization paths

### 3. TEE Optimization Matrix
- **File**: `tee-optimization-matrix.md`
- **Purpose**: Categorizes activities by efficiency and friction
- **Components**:
  - 2x2 matrix of optimization strategies
  - Color-coded efficiency zones
  - Actionable insights for each quadrant

### 4. TEE Integration Map
- **File**: `tee-integration-map.md`
- **Preview**: `preview-integration.html`
- **Purpose**: Shows TEE's role in the Comphyology ecosystem
- **Components**:
  - Core TEE equation and principles
  - Connections to UUFT and 18/82
  - Implementation touchpoints

## Usage Guidelines

### For Users:
1. Open `tee-calculator.html` in a web browser
2. Enter activity parameters
3. View and analyze TEE metrics
4. Use visualizations to identify optimization opportunities

### For Developers:
1. **Styling**:
   - Primary: `#2E7D32` (Dark Green)
   - Secondary: `#FFC107` (Amber)
   - Font: System UI, sans-serif

2. **File Structure**:
   - `.md` files contain Mermaid.js diagrams
   - `.html` files are previews/renderers
   - Keep source files in this directory

3. **Dependencies**:
   - Mermaid.js for diagrams
   - Chart.js for interactive charts
   - No build step required

## Integration

All TEE components are designed to work both as standalone tools and as integrated parts of the Comphyology documentation system. The calculator and visualizations can be embedded in other pages using iframes or by including the relevant JavaScript.

## License

All assets are part of the Comphyology project and are subject to the project's licensing terms.

## Getting Help

For questions or support with the TEE Framework, please refer to the main documentation at `../../TEE_FRAMEWORK.md` or contact the project maintainers.
