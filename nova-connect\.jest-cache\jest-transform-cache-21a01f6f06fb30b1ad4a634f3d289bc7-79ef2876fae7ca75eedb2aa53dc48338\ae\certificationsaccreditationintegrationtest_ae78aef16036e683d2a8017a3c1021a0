b4aa5e0812e108205ae7b1cf0ebc86d3
// Mock logger
_getJestObj().mock('../../../../utils/logger', () => ({
  createLogger: jest.fn(() => ({
    info: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn()
  }))
}));
function _getJestObj() {
  const {
    jest
  } = require("@jest/globals");
  _getJestObj = () => jest;
  return jest;
}
/**
 * Integration tests for the Certifications & Accreditation Connector
 */

const nock = require('nock');
const CertificationsAccreditationConnector = require('../../../../connector/implementations/certifications-accreditation');
describe('CertificationsAccreditationConnector Integration', () => {
  let connector;
  const baseUrl = 'https://api.test.com';
  beforeAll(() => {
    // Disable real HTTP requests
    nock.disableNetConnect();
  });
  afterAll(() => {
    // Enable real HTTP requests
    nock.enableNetConnect();
  });
  beforeEach(() => {
    // Reset nock
    nock.cleanAll();

    // Create connector instance
    connector = new CertificationsAccreditationConnector({
      baseUrl
    }, {
      clientId: 'test-client-id',
      clientSecret: 'test-client-secret',
      redirectUri: 'https://test-redirect.com'
    });

    // Mock authentication
    nock(baseUrl).post('/oauth2/token').reply(200, {
      access_token: 'test-access-token',
      expires_in: 3600
    });
  });
  describe('Certification Management', () => {
    it('should list certifications', async () => {
      // Mock certifications endpoint
      const mockCertifications = {
        data: [{
          id: 'cert-1',
          name: 'ISO 27001',
          description: 'Information Security Management System certification',
          type: 'security',
          status: 'active',
          issuer: 'International Organization for Standardization'
        }, {
          id: 'cert-2',
          name: 'SOC 2 Type II',
          description: 'Service Organization Control 2 Type II',
          type: 'security',
          status: 'active',
          issuer: 'American Institute of CPAs'
        }],
        pagination: {
          page: 1,
          limit: 20,
          totalItems: 2,
          totalPages: 1
        }
      };
      nock(baseUrl).get('/certifications').query({
        status: 'active'
      }).reply(200, mockCertifications);

      // Initialize connector
      await connector.initialize();

      // List certifications
      const result = await connector.listCertifications({
        status: 'active'
      });

      // Verify result
      expect(result).toEqual(mockCertifications);
    });
    it('should get a specific certification', async () => {
      // Mock certification endpoint
      const mockCertification = {
        id: 'cert-123',
        name: 'ISO 27001',
        description: 'Information Security Management System certification',
        type: 'security',
        status: 'active',
        issuer: 'International Organization for Standardization',
        issuedDate: '2023-01-15',
        expirationDate: '2026-01-14',
        requirements: [{
          id: 'req-1',
          name: 'Information Security Policy',
          description: 'Establish and maintain an information security policy',
          status: 'met'
        }, {
          id: 'req-2',
          name: 'Risk Assessment',
          description: 'Conduct regular risk assessments',
          status: 'met'
        }],
        documents: [{
          id: 'doc-1',
          name: 'ISO 27001 Certificate',
          type: 'pdf',
          url: 'https://example.com/documents/iso27001-cert.pdf'
        }]
      };
      nock(baseUrl).get('/certifications/cert-123').reply(200, mockCertification);

      // Initialize connector
      await connector.initialize();

      // Get certification
      const result = await connector.getCertification('cert-123');

      // Verify result
      expect(result).toEqual(mockCertification);
    });
    it('should create a new certification', async () => {
      // Certification data
      const certificationData = {
        name: 'HIPAA Compliance',
        description: 'Health Insurance Portability and Accountability Act compliance',
        type: 'healthcare',
        issuer: 'Department of Health and Human Services'
      };

      // Mock response
      const mockResponse = {
        id: 'cert-new',
        ...certificationData,
        status: 'pending',
        createdAt: '2023-06-15T10:30:00Z',
        updatedAt: '2023-06-15T10:30:00Z'
      };
      nock(baseUrl).post('/certifications', certificationData).reply(201, mockResponse);

      // Initialize connector
      await connector.initialize();

      // Create certification
      const result = await connector.createCertification(certificationData);

      // Verify result
      expect(result).toEqual(mockResponse);
    });
    it('should update an existing certification', async () => {
      // Certification update data
      const certificationId = 'cert-123';
      const updateData = {
        name: 'ISO 27001:2022',
        description: 'Updated Information Security Management System certification'
      };

      // Mock response
      const mockResponse = {
        id: certificationId,
        name: 'ISO 27001:2022',
        description: 'Updated Information Security Management System certification',
        type: 'security',
        status: 'active',
        issuer: 'International Organization for Standardization',
        updatedAt: '2023-06-15T11:45:00Z'
      };
      nock(baseUrl).put(`/certifications/${certificationId}`, updateData).reply(200, mockResponse);

      // Initialize connector
      await connector.initialize();

      // Update certification
      const result = await connector.updateCertification(certificationId, updateData);

      // Verify result
      expect(result).toEqual(mockResponse);
    });
    it('should delete a certification', async () => {
      // Certification ID
      const certificationId = 'cert-123';
      nock(baseUrl).delete(`/certifications/${certificationId}`).reply(204);

      // Initialize connector
      await connector.initialize();

      // Delete certification
      await connector.deleteCertification(certificationId);

      // If no error is thrown, the test passes
      expect(true).toBe(true);
    });
  });
  describe('Assessment Management', () => {
    it('should list assessments', async () => {
      // Mock assessments endpoint
      const mockAssessments = {
        data: [{
          id: 'assess-1',
          name: 'ISO 27001 Annual Assessment',
          description: 'Annual surveillance audit for ISO 27001',
          status: 'in_progress',
          certificationId: 'cert-123',
          certificationName: 'ISO 27001'
        }, {
          id: 'assess-2',
          name: 'ISO 27001 Gap Analysis',
          description: 'Pre-certification gap analysis',
          status: 'completed',
          certificationId: 'cert-123',
          certificationName: 'ISO 27001'
        }],
        pagination: {
          page: 1,
          limit: 20,
          totalItems: 2,
          totalPages: 1
        }
      };
      nock(baseUrl).get('/assessments').query({
        certificationId: 'cert-123'
      }).reply(200, mockAssessments);

      // Initialize connector
      await connector.initialize();

      // List assessments
      const result = await connector.listAssessments({
        certificationId: 'cert-123'
      });

      // Verify result
      expect(result).toEqual(mockAssessments);
    });
    it('should get a specific assessment', async () => {
      // Mock assessment endpoint
      const mockAssessment = {
        id: 'assess-123',
        name: 'ISO 27001 Annual Assessment',
        description: 'Annual surveillance audit for ISO 27001',
        status: 'in_progress',
        certificationId: 'cert-123',
        certificationName: 'ISO 27001',
        startDate: '2023-06-01',
        endDate: '2023-06-15',
        assessor: {
          id: 'assessor-1',
          name: 'John Smith',
          organization: 'Certification Body Inc.'
        },
        controls: [{
          id: 'control-1',
          name: 'A.5.1.1 Information Security Policies',
          description: 'A set of policies for information security shall be defined, approved by management, published and communicated to employees and relevant external parties.',
          status: 'compliant'
        }]
      };
      nock(baseUrl).get('/assessments/assess-123').reply(200, mockAssessment);

      // Initialize connector
      await connector.initialize();

      // Get assessment
      const result = await connector.getAssessment('assess-123');

      // Verify result
      expect(result).toEqual(mockAssessment);
    });
    it('should create an assessment', async () => {
      // Assessment data
      const assessmentData = {
        name: 'ISO 27001 Recertification',
        description: 'Full recertification audit for ISO 27001',
        certificationId: 'cert-123',
        startDate: '2023-09-01',
        endDate: '2023-09-15'
      };

      // Mock response
      const mockResponse = {
        id: 'assess-new',
        ...assessmentData,
        status: 'planned',
        certificationName: 'ISO 27001',
        createdAt: '2023-06-15T10:30:00Z',
        updatedAt: '2023-06-15T10:30:00Z'
      };
      nock(baseUrl).post('/assessments', assessmentData).reply(201, mockResponse);

      // Initialize connector
      await connector.initialize();

      // Create assessment
      const result = await connector.createAssessment(assessmentData);

      // Verify result
      expect(result).toEqual(mockResponse);
    });
  });
  describe('Evidence Management', () => {
    it('should list evidence', async () => {
      // Mock evidence endpoint
      const mockEvidence = {
        data: [{
          id: 'evidence-1',
          name: 'Information Security Policy Document',
          description: 'Current version of the Information Security Policy',
          type: 'document',
          assessmentId: 'assess-123',
          controlId: 'control-1'
        }, {
          id: 'evidence-2',
          name: 'Risk Assessment Report',
          description: 'Latest risk assessment report',
          type: 'document',
          assessmentId: 'assess-123',
          controlId: 'control-2'
        }],
        pagination: {
          page: 1,
          limit: 20,
          totalItems: 2,
          totalPages: 1
        }
      };
      nock(baseUrl).get('/evidence').query({
        assessmentId: 'assess-123'
      }).reply(200, mockEvidence);

      // Initialize connector
      await connector.initialize();

      // List evidence
      const result = await connector.listEvidence({
        assessmentId: 'assess-123'
      });

      // Verify result
      expect(result).toEqual(mockEvidence);
    });
    it('should get specific evidence', async () => {
      // Mock evidence endpoint
      const mockEvidence = {
        id: 'evidence-123',
        name: 'Information Security Policy Document',
        description: 'Current version of the Information Security Policy',
        type: 'document',
        assessmentId: 'assess-123',
        controlId: 'control-1',
        url: 'https://example.com/documents/security-policy.pdf',
        metadata: {
          version: '2.3',
          lastReviewed: '2023-05-15',
          approvedBy: 'Security Committee'
        }
      };
      nock(baseUrl).get('/evidence/evidence-123').reply(200, mockEvidence);

      // Initialize connector
      await connector.initialize();

      // Get evidence
      const result = await connector.getEvidence('evidence-123');

      // Verify result
      expect(result).toEqual(mockEvidence);
    });
    it('should create evidence', async () => {
      // Evidence data
      const evidenceData = {
        name: 'Access Control Policy',
        description: 'Access control policy document',
        type: 'document',
        assessmentId: 'assess-123',
        controlId: 'control-3',
        url: 'https://example.com/documents/access-control-policy.pdf'
      };

      // Mock response
      const mockResponse = {
        id: 'evidence-new',
        ...evidenceData,
        createdAt: '2023-06-15T10:30:00Z',
        updatedAt: '2023-06-15T10:30:00Z'
      };
      nock(baseUrl).post('/evidence', evidenceData).reply(201, mockResponse);

      // Initialize connector
      await connector.initialize();

      // Create evidence
      const result = await connector.createEvidence(evidenceData);

      // Verify result
      expect(result).toEqual(mockResponse);
    });
  });
  describe('Error Handling', () => {
    it('should handle authentication errors', async () => {
      // Clean previous nock mocks
      nock.cleanAll();

      // Mock authentication error
      nock(baseUrl).post('/oauth2/token').reply(401, {
        error: 'invalid_client',
        error_description: 'Invalid client credentials'
      });

      // Try to initialize connector
      await expect(connector.initialize()).rejects.toThrow('Authentication failed');
    });
    it('should handle not found errors', async () => {
      // Mock not found error
      nock(baseUrl).get('/certifications/non-existent').reply(404, {
        error: 'not_found',
        error_description: 'Certification not found'
      });

      // Initialize connector
      await connector.initialize();

      // Try to get non-existent certification
      await expect(connector.getCertification('non-existent')).rejects.toThrow('Error getting certification');
    });
    it('should handle validation errors', async () => {
      // Certification data with missing required fields
      const invalidData = {
        description: 'Invalid Certification'
        // Missing required field: name
      };

      // Mock validation error
      nock(baseUrl).post('/certifications', invalidData).reply(400, {
        error: 'validation_error',
        error_description: 'Validation failed',
        errors: [{
          field: 'name',
          message: 'Name is required'
        }]
      });

      // Initialize connector
      await connector.initialize();

      // Try to create invalid certification
      await expect(connector.createCertification(invalidData)).rejects.toThrow('name is required');
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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