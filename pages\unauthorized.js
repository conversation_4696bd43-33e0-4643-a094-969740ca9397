import React from 'react';
import Link from 'next/link';
import Head from 'next/head';

export default function Unauthorized() {
  return (
    <>
      <Head>
        <title>Unauthorized - NovaFuse Compliance App Store</title>
        <meta name="description" content="You do not have permission to access this page" />
      </Head>
      
      <div className="flex flex-col items-center justify-center min-h-[60vh] text-center px-4">
        <div className="bg-red-900 bg-opacity-20 rounded-full p-6 mb-6">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m0 0v2m0-2h2m-2 0H9m3-3V8m0 0V6m0 2h2m-2 0H9M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z" />
          </svg>
        </div>
        
        <h1 className="text-4xl font-bold mb-4">Access Denied</h1>
        
        <p className="text-xl text-gray-300 mb-8 max-w-2xl">
          You do not have permission to access this page. If you believe this is an error, please contact support.
        </p>
        
        <div className="flex flex-wrap gap-4 justify-center">
          <Link href="/" className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded-lg">
            Go to Home
          </Link>
          
          <Link href="/contact" className="border border-blue-600 text-blue-500 hover:bg-blue-900 hover:bg-opacity-20 font-bold py-2 px-6 rounded-lg">
            Contact Support
          </Link>
        </div>
      </div>
    </>
  );
}
