import React, { memo } from 'react';
import PropTypes from 'prop-types';
import { QUANTUM_STATES } from '../contexts/QuantumField';
import '../styles/QuantumDebugPanel.css';

const QuantumDebugPanel = memo(({ metrics, entropyHistory, quantumState, coherence }) => {
  // Format timestamp
  const formatTime = (timestamp) => {
    if (!timestamp) return 'N/A';
    return new Date(timestamp).toLocaleTimeString();
  };

  // Calculate statistics
  const stats = React.useMemo(() => {
    if (entropyHistory.length === 0) return {};
    
    const quantumMeasurements = entropyHistory.filter(m => m.mode === 'quantum');
    const classicalMeasurements = entropyHistory.filter(m => m.mode === 'classical');
    
    return {
      totalMeasurements: entropyHistory.length,
      quantumMeasurements: quantumMeasurements.length,
      classicalMeasurements: classicalMeasurements.length,
      avgQuantumEntropy: quantumMeasurements.length > 0
        ? (quantumMeasurements.reduce((sum, m) => sum + (m.entropy || 0), 0) / quantumMeasurements.length).toFixed(4)
        : 0,
      lastMeasurement: entropyHistory[entropyHistory.length - 1]?.timestamp,
      anomalies: entropyHistory.filter(m => m.anomaly?.isAnomaly).length
    };
  }, [entropyHistory]);

  // Determine status color based on quantum state
  const getStatusColor = () => {
    switch(quantumState) {
      case QUANTUM_STATES.ENTANGLED:
        return '#4ade80'; // Green
      case QUANTUM_STATES.MEASURING:
        return '#fbbf24'; // Yellow
      case QUANTUM_STATES.ERROR:
        return '#f87171'; // Red
      case QUANTUM_STATES.COLLAPSED:
        return '#60a5fa'; // Blue
      default:
        return '#9ca3af'; // Gray
    }
  };

  return (
    <div className="quantum-debug-panel">
      <div className="debug-header">
        <h3>Quantum Debug Console</h3>
        <div className="status-indicator">
          <span className="status-dot" style={{ backgroundColor: getStatusColor() }} />
          <span className="status-text">{quantumState}</span>
        </div>
      </div>
      
      <div className="debug-metrics">
        <div className="metric-group">
          <h4>System Metrics</h4>
          <div className="metric">
            <span className="metric-label">Coherence:</span>
            <span className="metric-value">{coherence.toFixed(4)}</span>
          </div>
          <div className="metric">
            <span className="metric-label">Quantum Success Rate:</span>
            <span className="metric-value">
              {(metrics.quantumSuccessRate * 100).toFixed(1)}%
            </span>
          </div>
          <div className="metric">
            <span className="metric-label">Fallback Frequency:</span>
            <span className="metric-value">
              {(metrics.fallbackFrequency * 100).toFixed(1)}%
            </span>
          </div>
        </div>
        
        <div className="metric-group">
          <h4>Measurements</h4>
          <div className="metric">
            <span className="metric-label">Total:</span>
            <span className="metric-value">{stats.totalMeasurements}</span>
          </div>
          <div className="metric">
            <span className="metric-label">Quantum:</span>
            <span className="metric-value">
              {stats.quantumMeasurements} 
              <span className="metric-subtext">
                ({(stats.quantumMeasurements / stats.totalMeasurements * 100 || 0).toFixed(1)}%)
              </span>
            </span>
          </div>
          <div className="metric">
            <span className="metric-label">Classical:</span>
            <span className="metric-value">
              {stats.classicalMeasurements}
              <span className="metric-subtext">
                ({(stats.classicalMeasurements / stats.totalMeasurements * 100 || 0).toFixed(1)}%)
              </span>
            </span>
          </div>
          <div className="metric">
            <span className="metric-label">Anomalies:</span>
            <span className="metric-value">{stats.anomalies}</span>
          </div>
        </div>
        
        <div className="metric-group">
          <h4>Performance</h4>
          <div className="metric">
            <span className="metric-label">Avg Quantum Entropy:</span>
            <span className="metric-value">{stats.avgQuantumEntropy}</span>
          </div>
          <div className="metric">
            <span className="metric-label">Last Measurement:</span>
            <span className="metric-value">{formatTime(stats.lastMeasurement)}</span>
          </div>
          <div className="metric">
            <span className="metric-label">Readiness Score:</span>
            <span className="metric-value">
              {(metrics.readinessScore * 100).toFixed(1)}%
            </span>
          </div>
        </div>
      </div>
      
      <div className="debug-log">
        <h4>Event Log</h4>
        <div className="log-entries">
          {entropyHistory.slice(-5).reverse().map((entry, idx) => (
            <div key={`log-${entry.timestamp}-${idx}`} className="log-entry">
              <span className="log-time">{formatTime(entry.timestamp)}</span>
              <span className={`log-mode log-mode-${entry.mode}`}>
                {entry.mode.toUpperCase()}
              </span>
              <span className="log-message">
                Measurement: {entry.entropy?.toFixed(4)}
                {entry.anomaly?.isAnomaly && (
                  <span className="log-warning"> (Anomaly: +{entry.anomaly.deviation.toFixed(2)})</span>
                )}
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
});

QuantumDebugPanel.propTypes = {
  metrics: PropTypes.shape({
    quantumSuccessRate: PropTypes.number,
    fallbackFrequency: PropTypes.number,
    readinessScore: PropTypes.number,
    totalRequests: PropTypes.number,
    quantumRequests: PropTypes.number,
    classicalRequests: PropTypes.number,
    lastUpdated: PropTypes.string
  }).isRequired,
  entropyHistory: PropTypes.arrayOf(PropTypes.shape({
    entropy: PropTypes.number,
    timestamp: PropTypes.number,
    mode: PropTypes.oneOf(['quantum', 'classical']),
    anomaly: PropTypes.shape({
      isAnomaly: PropTypes.bool,
      deviation: PropTypes.number
    })
  })).isRequired,
  quantumState: PropTypes.oneOf(Object.values(QUANTUM_STATES)).isRequired,
  coherence: PropTypes.number.isRequired
};

export default QuantumDebugPanel;
