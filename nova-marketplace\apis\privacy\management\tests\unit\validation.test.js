/**
 * Validation Middleware Tests
 * 
 * This file contains unit tests for the validation middleware.
 */

const Joi = require('joi');
const { validate } = require('../../middleware/validation');

describe('Validation Middleware', () => {
  let req, res, next;
  
  beforeEach(() => {
    req = {
      body: {},
      query: {},
      params: {}
    };
    
    res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    };
    
    next = jest.fn();
  });
  
  describe('validate', () => {
    it('should call next() when validation passes', () => {
      // Create a schema that requires a name field
      const schema = Joi.object({
        name: Joi.string().required()
      });
      
      // Set up the request with valid data
      req.body = { name: 'Test Name' };
      
      // Create the middleware
      const middleware = validate(schema);
      
      // Call the middleware
      middleware(req, res, next);
      
      // Check that next() was called
      expect(next).toHaveBeenCalled();
      
      // Check that res.status() and res.json() were not called
      expect(res.status).not.toHaveBeenCalled();
      expect(res.json).not.toHaveBeenCalled();
    });
    
    it('should return a 400 error when validation fails', () => {
      // Create a schema that requires a name field
      const schema = Joi.object({
        name: Joi.string().required()
      });
      
      // Set up the request with invalid data
      req.body = { };
      
      // Create the middleware
      const middleware = validate(schema);
      
      // Call the middleware
      middleware(req, res, next);
      
      // Check that next() was not called
      expect(next).not.toHaveBeenCalled();
      
      // Check that res.status() was called with 400
      expect(res.status).toHaveBeenCalledWith(400);
      
      // Check that res.json() was called with the expected error
      expect(res.json).toHaveBeenCalledWith({
        error: 'ValidationError',
        message: 'Validation failed',
        errors: expect.arrayContaining([
          expect.objectContaining({
            field: 'name',
            message: expect.stringContaining('required')
          })
        ])
      });
    });
    
    it('should validate the specified request property', () => {
      // Create a schema that requires an id field
      const schema = Joi.object({
        id: Joi.string().required()
      });
      
      // Set up the request with valid data in params
      req.params = { id: '123' };
      
      // Create the middleware to validate params
      const middleware = validate(schema, 'params');
      
      // Call the middleware
      middleware(req, res, next);
      
      // Check that next() was called
      expect(next).toHaveBeenCalled();
      
      // Check that res.status() and res.json() were not called
      expect(res.status).not.toHaveBeenCalled();
      expect(res.json).not.toHaveBeenCalled();
    });
    
    it('should return multiple validation errors', () => {
      // Create a schema that requires name and email fields
      const schema = Joi.object({
        name: Joi.string().required(),
        email: Joi.string().email().required()
      });
      
      // Set up the request with invalid data
      req.body = { };
      
      // Create the middleware
      const middleware = validate(schema);
      
      // Call the middleware
      middleware(req, res, next);
      
      // Check that next() was not called
      expect(next).not.toHaveBeenCalled();
      
      // Check that res.status() was called with 400
      expect(res.status).toHaveBeenCalledWith(400);
      
      // Check that res.json() was called with the expected errors
      expect(res.json).toHaveBeenCalledWith({
        error: 'ValidationError',
        message: 'Validation failed',
        errors: expect.arrayContaining([
          expect.objectContaining({
            field: 'name',
            message: expect.stringContaining('required')
          }),
          expect.objectContaining({
            field: 'email',
            message: expect.stringContaining('required')
          })
        ])
      });
    });
  });
});
