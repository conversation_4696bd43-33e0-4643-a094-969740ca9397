"""
Tests for the LocalAlphaFoldEngine implementation.
"""
import os
import unittest
import tempfile
from unittest.mock import patch, MagicMock

from src.folding_engines import LocalAlphaFoldEngine, FoldingEngineError

class TestLocalAlphaFoldEngine(unittest.TestCase):
    """Test cases for LocalAlphaFoldEngine."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.test_sequence = "ACDEFGHIKLMNPQRSTVWY"  # Valid protein sequence
        self.temp_dir = tempfile.mkdtemp()
        
        # Mock configuration
        self.config = {
            'alphafold_path': '/fake/alphafold',
            'output_dir': self.temp_dir,
            'model_preset': 'monomer',
            'db_preset': 'reduced_dbs',
            'gpu_id': 0,
            'debug': True  # Keep temp files for inspection
        }
        
        # Mock GPU info
        self.gpu_info = {
            'total_memory': 1024 * 1024 * 1024 * 16,  # 16GB
            'used_memory': 1024 * 1024 * 1024 * 4,    # 4GB used
            'free_memory': 1024 * 1024 * 1024 * 12,   # 12GB free
            'utilization': 25  # 25% utilization
        }
    
    def tearDown(self):
        """Clean up after tests."""
        # Clean up temporary directory
        for root, dirs, files in os.walk(self.temp_dir, topdown=False):
            for name in files:
                os.remove(os.path.join(root, name))
            for name in dirs:
                os.rmdir(os.path.join(root, name))
        os.rmdir(self.temp_dir)
    
    @patch('subprocess.Popen')
    @patch('os.path.exists', return_value=True)
    @patch('os.makedirs')
    def test_predict_success(self, mock_makedirs, mock_exists, mock_popen):
        """Test successful prediction."""
        # Setup mock process
        mock_process = MagicMock()
        mock_process.stdout = ["AlphaFold: Starting prediction", "AlphaFold: Done"]
        mock_process.wait.return_value = 0
        mock_popen.return_value = mock_process
        
        # Create engine and run prediction
        engine = LocalAlphaFoldEngine(config=self.config)
        
        # Mock GPU info methods
        engine._get_gpu_memory_usage = MagicMock(return_value=(self.gpu_info['used_memory'] // (1024*1024), 
                                                             self.gpu_info['total_memory'] // (1024*1024)))
        
        # Mock file operations
        with patch('builtins.open', unittest.mock.mock_open()):
            result = engine.predict(self.test_sequence)
        
        # Verify results
        self.assertEqual(result['status'], 'COMPLETED')
        self.assertIn('pdb_path', result)
        self.assertIn('output_dir', result)
        self.assertIn('processing_time_seconds', result)
        self.assertIn('metrics', result)
        self.assertIn('gpu_info', result)
        self.assertIn('config_used', result)
        self.assertIn('metadata', result)
    
    @patch('subprocess.Popen')
    def test_predict_failure(self, mock_popen):
        """Test prediction failure."""
        # Setup mock process that fails
        mock_process = MagicMock()
        mock_process.stdout = ["AlphaFold: Error occurred"]
        mock_process.wait.return_value = 1
        mock_popen.return_value = mock_process
        
        # Create engine and expect failure
        engine = LocalAlphaFoldEngine(config=self.config)
        
        with self.assertRaises(FoldingEngineError):
            with patch('builtins.open', unittest.mock.mock_open()):
                engine.predict(self.test_sequence)
    
    def test_gpu_memory_optimization(self):
        """Test GPU memory optimization logic."""
        engine = LocalAlphaFoldEngine(config=self.config)
        
        # Test with different sequence lengths
        test_cases = [
            (100, 1),    # Short sequence
            (500, 2),    # Medium sequence
            (1500, 4),   # Long sequence
            (3000, 8),   # Very long sequence
        ]
        
        for seq_len, expected_batch_size in test_cases:
            with self.subTest(sequence_length=seq_len):
                batch_size = engine._optimize_batch_size(seq_len)
                self.assertEqual(batch_size, expected_batch_size)
    
    def test_consciousness_optimization(self):
        """Test consciousness optimization configuration."""
        # Create engine with consciousness optimization
        config = self.config.copy()
        config.update({
            'psi_optimization': True,
            'fib_constraints': {'enabled': True, 'tolerance': 0.1}
        })
        
        engine = LocalAlphaFoldEngine(config=config)
        
        # Verify configuration
        self.assertTrue(engine.config.get('psi_optimization', False))
        self.assertTrue(engine.config.get('fib_constraints', {}).get('enabled', False))
        self.assertEqual(engine.config.get('fib_constraints', {}).get('tolerance', 0), 0.1)

if __name__ == '__main__':
    unittest.main()
