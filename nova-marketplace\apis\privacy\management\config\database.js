/**
 * Database Configuration
 * 
 * This file contains the configuration for the MongoDB database.
 */

const mongoose = require('mongoose');

// MongoDB connection options
const options = {
  useNewUrlParser: true,
  useUnifiedTopology: true,
  serverSelectionTimeoutMS: 5000,
  socketTimeoutMS: 45000,
};

// MongoDB connection URI
// In a production environment, this would be loaded from environment variables
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/novafuse-privacy';

/**
 * Connect to MongoDB
 * @returns {Promise<void>}
 */
const connect = async () => {
  try {
    await mongoose.connect(MONGODB_URI, options);
    console.log('Connected to MongoDB');
  } catch (error) {
    console.error('Failed to connect to MongoDB', error);
    throw error;
  }
};

/**
 * Disconnect from MongoDB
 * @returns {Promise<void>}
 */
const disconnect = async () => {
  try {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  } catch (error) {
    console.error('Failed to disconnect from MongoDB', error);
    throw error;
  }
};

/**
 * Get the MongoDB connection
 * @returns {mongoose.Connection} MongoDB connection
 */
const getConnection = () => {
  return mongoose.connection;
};

module.exports = {
  connect,
  disconnect,
  getConnection,
  MONGODB_URI
};
