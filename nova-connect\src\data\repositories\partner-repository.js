/**
 * NovaFuse Universal API Connector - Partner Repository
 * 
 * This module provides data access methods for partner information.
 */

const Partner = require('../models/partner');
const { createLogger } = require('../../utils/logger');

const logger = createLogger('partner-repository');

/**
 * Partner Repository
 * 
 * Provides methods for accessing and manipulating partner information.
 */
class PartnerRepository {
  /**
   * Get a partner by ID
   * @param {string} id - Partner ID
   * @returns {Promise<Object>} Partner
   */
  async getPartnerById(id) {
    try {
      logger.debug(`Getting partner by ID: ${id}`);
      return await Partner.findOne({ id, status: { $ne: 'suspended' } });
    } catch (error) {
      logger.error(`Error getting partner by ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Get a partner by API key
   * @param {string} apiKey - API key
   * @returns {Promise<Object>} Partner
   */
  async getPartnerByApiKey(apiKey) {
    try {
      logger.debug(`Getting partner by API key: ${apiKey}`);
      return await Partner.findOne({ apiKey, status: 'active' });
    } catch (error) {
      logger.error(`<PERSON>rror getting partner by API key:`, error);
      throw error;
    }
  }

  /**
   * Validate API credentials
   * @param {string} apiKey - API key
   * @param {string} apiSecret - API secret
   * @returns {Promise<Object>} Partner if credentials are valid, null otherwise
   */
  async validateApiCredentials(apiKey, apiSecret) {
    try {
      logger.debug(`Validating API credentials`);
      return await Partner.validateApiCredentials(apiKey, apiSecret);
    } catch (error) {
      logger.error(`Error validating API credentials:`, error);
      throw error;
    }
  }

  /**
   * Get all partners
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Partners
   */
  async getAllPartners(options = {}) {
    try {
      const { limit = 100, skip = 0, status } = options;
      
      logger.debug('Getting all partners');
      
      const query = {};
      
      // Add status filter if provided
      if (status) {
        query.status = status;
      } else {
        // Exclude suspended partners by default
        query.status = { $ne: 'suspended' };
      }
      
      return await Partner.find(query)
        .sort({ name: 1 })
        .skip(skip)
        .limit(limit);
    } catch (error) {
      logger.error('Error getting all partners:', error);
      throw error;
    }
  }

  /**
   * Create a partner
   * @param {Object} partnerData - Partner data
   * @returns {Promise<Object>} Created partner
   */
  async createPartner(partnerData) {
    try {
      logger.debug('Creating partner');
      
      const partner = new Partner(partnerData);
      return await partner.save();
    } catch (error) {
      logger.error('Error creating partner:', error);
      throw error;
    }
  }

  /**
   * Update a partner
   * @param {string} id - Partner ID
   * @param {Object} partnerData - Updated partner data
   * @returns {Promise<Object>} Updated partner
   */
  async updatePartner(id, partnerData) {
    try {
      logger.debug(`Updating partner: ${id}`);
      
      // Ensure ID is not changed
      delete partnerData.id;
      
      // Don't allow updating API credentials through this method
      delete partnerData.apiKey;
      delete partnerData.apiSecret;
      
      // Update timestamp
      partnerData.updatedAt = new Date();
      
      return await Partner.findOneAndUpdate(
        { id },
        { $set: partnerData },
        { new: true, runValidators: true }
      );
    } catch (error) {
      logger.error(`Error updating partner ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete a partner
   * @param {string} id - Partner ID
   * @returns {Promise<boolean>} Whether the partner was deleted
   */
  async deletePartner(id) {
    try {
      logger.debug(`Deleting partner: ${id}`);
      
      // Soft delete by setting status to suspended
      const result = await Partner.findOneAndUpdate(
        { id },
        { $set: { status: 'suspended', updatedAt: new Date() } }
      );
      
      return !!result;
    } catch (error) {
      logger.error(`Error deleting partner ${id}:`, error);
      throw error;
    }
  }

  /**
   * Regenerate API credentials
   * @param {string} id - Partner ID
   * @returns {Promise<Object>} Updated partner with new API credentials
   */
  async regenerateApiCredentials(id) {
    try {
      logger.debug(`Regenerating API credentials for partner: ${id}`);
      
      const partner = await Partner.findOne({ id });
      
      if (!partner) {
        throw new Error(`Partner ${id} not found`);
      }
      
      return await partner.regenerateApiCredentials();
    } catch (error) {
      logger.error(`Error regenerating API credentials for partner ${id}:`, error);
      throw error;
    }
  }

  /**
   * Check partner limits
   * @param {string} id - Partner ID
   * @param {string} limitType - Limit type (requests, concurrentRequests, connectors, credentials)
   * @param {number} currentCount - Current count
   * @returns {Promise<boolean>} Whether the partner is within limits
   */
  async checkPartnerLimits(id, limitType, currentCount) {
    try {
      logger.debug(`Checking ${limitType} limit for partner: ${id}`);
      
      const partner = await Partner.findOne({ id, status: 'active' });
      
      if (!partner) {
        throw new Error(`Partner ${id} not found or not active`);
      }
      
      return await partner.checkLimits(limitType, currentCount);
    } catch (error) {
      logger.error(`Error checking ${limitType} limit for partner ${id}:`, error);
      throw error;
    }
  }

  /**
   * Update partner limits
   * @param {string} id - Partner ID
   * @param {Object} limits - Updated limits
   * @returns {Promise<Object>} Updated partner
   */
  async updatePartnerLimits(id, limits) {
    try {
      logger.debug(`Updating limits for partner: ${id}`);
      
      return await Partner.findOneAndUpdate(
        { id },
        { $set: { limits, updatedAt: new Date() } },
        { new: true, runValidators: true }
      );
    } catch (error) {
      logger.error(`Error updating limits for partner ${id}:`, error);
      throw error;
    }
  }

  /**
   * Count partners
   * @param {Object} filter - Filter criteria
   * @returns {Promise<number>} Partner count
   */
  async countPartners(filter = {}) {
    try {
      logger.debug('Counting partners');
      
      return await Partner.countDocuments(filter);
    } catch (error) {
      logger.error('Error counting partners:', error);
      throw error;
    }
  }

  /**
   * Search partners
   * @param {string} searchTerm - Search term
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Partners
   */
  async searchPartners(searchTerm, options = {}) {
    try {
      const { limit = 100, skip = 0, status } = options;
      
      logger.debug(`Searching partners with term: ${searchTerm}`);
      
      const query = {
        $or: [
          { name: { $regex: searchTerm, $options: 'i' } },
          { description: { $regex: searchTerm, $options: 'i' } },
          { 'contactInfo.primaryEmail': { $regex: searchTerm, $options: 'i' } }
        ]
      };
      
      // Add status filter if provided
      if (status) {
        query.status = status;
      } else {
        // Exclude suspended partners by default
        query.status = { $ne: 'suspended' };
      }
      
      return await Partner.find(query)
        .sort({ name: 1 })
        .skip(skip)
        .limit(limit);
    } catch (error) {
      logger.error(`Error searching partners with term ${searchTerm}:`, error);
      throw error;
    }
  }
}

// Export singleton instance
module.exports = new PartnerRepository();
