/**
 * Cloud Functions Simulator
 * 
 * This file simulates the Google Cloud Functions service for the NovaFuse GCP simulation.
 * It provides endpoints for managing and executing serverless functions for compliance enforcement.
 */

const express = require('express');
const cors = require('cors');
const morgan = require('morgan');
const axios = require('axios');

// Create Express app
const app = express();

// Middleware
app.use(cors());
app.use(express.json());
app.use(morgan('dev'));

// In-memory storage for functions
const functions = {
  'PublicBucketRemediation': {
    name: 'PublicBucketRemediation',
    description: 'Automatically remediates public access to Cloud Storage buckets',
    status: 'ACTIVE',
    entryPoint: 'handlePublicBucket',
    runtime: 'nodejs16',
    serviceAccountEmail: '<EMAIL>',
    trigger: {
      type: 'SCC_FINDING',
      condition: "finding.category = 'PUBLIC_BUCKET_ACL'"
    },
    actions: [
      { type: 'UPDATE_BUCKET_ACL', parameters: { allUsers: 'REMOVE', allAuthenticatedUsers: 'REMOVE' } },
      { type: 'CREATE_FINDING_NOTE', parameters: { category: 'REMEDIATION', severity: 'INFO' } },
      { type: 'UPDATE_COMPLIANCE_STATUS', parameters: { control: 'pci-dss-req3.4', status: 'COMPLIANT' } },
      { type: 'STORE_EVIDENCE', parameters: { type: 'AUTO_REMEDIATION', retention: '5y' } }
    ],
    notificationChannels: ['<EMAIL>', 'slack-security-alerts'],
    executionCount: 12,
    lastExecution: '2023-06-18T14:22:31Z',
    averageExecutionTime: 1.2
  },
  'EncryptionKeyRotation': {
    name: 'EncryptionKeyRotation',
    description: 'Automatically rotates encryption keys for compliance',
    status: 'ACTIVE',
    entryPoint: 'rotateKeys',
    runtime: 'nodejs16',
    serviceAccountEmail: '<EMAIL>',
    trigger: {
      type: 'SCHEDULE',
      condition: 'rate(30 days)'
    },
    actions: [
      { type: 'ROTATE_KMS_KEYS', parameters: { keyRing: 'projects/novafuse/locations/global/keyRings/compliance' } },
      { type: 'UPDATE_COMPLIANCE_STATUS', parameters: { control: 'gdpr-art32-1a', status: 'COMPLIANT' } },
      { type: 'STORE_EVIDENCE', parameters: { type: 'KEY_ROTATION', retention: '7y' } }
    ],
    notificationChannels: ['<EMAIL>'],
    executionCount: 6,
    lastExecution: '2023-06-15T00:00:00Z',
    averageExecutionTime: 3.5
  },
  'ComplianceViolationRemediation': {
    name: 'ComplianceViolationRemediation',
    description: 'Automatically remediates common compliance violations',
    status: 'ACTIVE',
    entryPoint: 'handleComplianceViolation',
    runtime: 'nodejs16',
    serviceAccountEmail: '<EMAIL>',
    trigger: {
      type: 'COMPLIANCE_FINDING',
      condition: "finding.status = 'FAILED'"
    },
    actions: [
      { type: 'APPLY_POLICY', parameters: { policyType: 'COMPLIANCE', policyId: 'default-compliance-policy' } },
      { type: 'CREATE_FINDING_NOTE', parameters: { category: 'REMEDIATION', severity: 'INFO' } },
      { type: 'STORE_EVIDENCE', parameters: { type: 'AUTO_REMEDIATION', retention: '5y' } }
    ],
    notificationChannels: ['<EMAIL>', 'slack-compliance-alerts'],
    executionCount: 28,
    lastExecution: '2023-06-19T10:15:22Z',
    averageExecutionTime: 2.3
  },
  'LogRetentionEnforcement': {
    name: 'LogRetentionEnforcement',
    description: 'Enforces log retention policies for compliance',
    status: 'ACTIVE',
    entryPoint: 'enforceLogRetention',
    runtime: 'nodejs16',
    serviceAccountEmail: '<EMAIL>',
    trigger: {
      type: 'SCHEDULE',
      condition: 'rate(7 days)'
    },
    actions: [
      { type: 'UPDATE_LOG_RETENTION', parameters: { logType: 'DATA_ACCESS', retentionDays: 365 } },
      { type: 'UPDATE_LOG_RETENTION', parameters: { logType: 'ADMIN_ACTIVITY', retentionDays: 365 * 3 } },
      { type: 'UPDATE_COMPLIANCE_STATUS', parameters: { control: 'hipaa-164-312b', status: 'COMPLIANT' } },
      { type: 'STORE_EVIDENCE', parameters: { type: 'LOG_RETENTION', retention: '3y' } }
    ],
    notificationChannels: ['<EMAIL>'],
    executionCount: 8,
    lastExecution: '2023-06-14T00:00:00Z',
    averageExecutionTime: 1.8
  }
};

// In-memory storage for function executions
const executions = [];

/**
 * @route GET /functions
 * @description List all functions
 * @access Private
 */
app.get('/functions', (req, res) => {
  const functionsList = Object.values(functions).map(fn => ({
    name: fn.name,
    description: fn.description,
    status: fn.status,
    trigger: fn.trigger,
    executionCount: fn.executionCount,
    lastExecution: fn.lastExecution
  }));
  
  res.json({
    functions: functionsList
  });
});

/**
 * @route GET /functions/:name
 * @description Get a specific function
 * @access Private
 */
app.get('/functions/:name', (req, res) => {
  const { name } = req.params;
  
  if (!functions[name]) {
    return res.status(404).json({
      error: {
        code: 404,
        message: `Function ${name} not found`
      }
    });
  }
  
  res.json({
    function: functions[name]
  });
});

/**
 * @route POST /functions
 * @description Create a new function
 * @access Private
 */
app.post('/functions', (req, res) => {
  const { name, description, entryPoint, runtime, trigger, actions, notificationChannels } = req.body;
  
  if (!name || !entryPoint || !runtime || !trigger) {
    return res.status(400).json({
      error: {
        code: 400,
        message: 'Name, entryPoint, runtime, and trigger are required'
      }
    });
  }
  
  if (functions[name]) {
    return res.status(409).json({
      error: {
        code: 409,
        message: `Function ${name} already exists`
      }
    });
  }
  
  functions[name] = {
    name,
    description: description || '',
    status: 'ACTIVE',
    entryPoint,
    runtime,
    serviceAccountEmail: '<EMAIL>',
    trigger,
    actions: actions || [],
    notificationChannels: notificationChannels || [],
    executionCount: 0,
    lastExecution: null,
    averageExecutionTime: 0
  };
  
  res.status(201).json({
    function: functions[name]
  });
});

/**
 * @route DELETE /functions/:name
 * @description Delete a function
 * @access Private
 */
app.delete('/functions/:name', (req, res) => {
  const { name } = req.params;
  
  if (!functions[name]) {
    return res.status(404).json({
      error: {
        code: 404,
        message: `Function ${name} not found`
      }
    });
  }
  
  delete functions[name];
  
  res.status(204).end();
});

/**
 * @route POST /functions/:name/execute
 * @description Execute a function
 * @access Private
 */
app.post('/functions/:name/execute', async (req, res) => {
  const { name } = req.params;
  const { data } = req.body;
  
  if (!functions[name]) {
    return res.status(404).json({
      error: {
        code: 404,
        message: `Function ${name} not found`
      }
    });
  }
  
  const fn = functions[name];
  
  // Create an execution record
  const executionId = `execution-${Date.now()}`;
  const startTime = new Date().toISOString();
  
  const execution = {
    id: executionId,
    functionName: name,
    startTime,
    status: 'RUNNING',
    input: data,
    output: null,
    error: null,
    endTime: null,
    duration: null
  };
  
  executions.push(execution);
  
  try {
    // Simulate function execution
    const result = await executeFunction(fn, data);
    
    // Update execution record
    const endTime = new Date().toISOString();
    const duration = (new Date(endTime) - new Date(startTime)) / 1000;
    
    execution.status = 'SUCCESS';
    execution.output = result;
    execution.endTime = endTime;
    execution.duration = duration;
    
    // Update function statistics
    fn.executionCount += 1;
    fn.lastExecution = startTime;
    fn.averageExecutionTime = ((fn.averageExecutionTime * (fn.executionCount - 1)) + duration) / fn.executionCount;
    
    res.json({
      execution: {
        id: executionId,
        functionName: name,
        status: 'SUCCESS',
        startTime,
        endTime,
        duration,
        result
      }
    });
  } catch (error) {
    // Update execution record
    const endTime = new Date().toISOString();
    const duration = (new Date(endTime) - new Date(startTime)) / 1000;
    
    execution.status = 'FAILURE';
    execution.error = error.message;
    execution.endTime = endTime;
    execution.duration = duration;
    
    res.status(500).json({
      execution: {
        id: executionId,
        functionName: name,
        status: 'FAILURE',
        startTime,
        endTime,
        duration,
        error: error.message
      }
    });
  }
});

/**
 * @route GET /executions
 * @description List all function executions
 * @access Private
 */
app.get('/executions', (req, res) => {
  const { functionName, status, limit } = req.query;
  
  let filteredExecutions = [...executions];
  
  if (functionName) {
    filteredExecutions = filteredExecutions.filter(exec => exec.functionName === functionName);
  }
  
  if (status) {
    filteredExecutions = filteredExecutions.filter(exec => exec.status === status);
  }
  
  // Sort by start time (newest first)
  filteredExecutions.sort((a, b) => new Date(b.startTime) - new Date(a.startTime));
  
  // Apply limit if specified
  if (limit) {
    filteredExecutions = filteredExecutions.slice(0, parseInt(limit));
  }
  
  res.json({
    executions: filteredExecutions
  });
});

/**
 * @route GET /executions/:id
 * @description Get a specific function execution
 * @access Private
 */
app.get('/executions/:id', (req, res) => {
  const { id } = req.params;
  
  const execution = executions.find(exec => exec.id === id);
  
  if (!execution) {
    return res.status(404).json({
      error: {
        code: 404,
        message: `Execution ${id} not found`
      }
    });
  }
  
  res.json({
    execution
  });
});

/**
 * Execute a function with the given data
 * @param {Object} fn The function to execute
 * @param {Object} data The input data for the function
 * @returns {Promise<Object>} The result of the function execution
 */
async function executeFunction(fn, data) {
  // Simulate function execution based on the function type
  switch (fn.name) {
    case 'PublicBucketRemediation':
      return await executePublicBucketRemediation(fn, data);
    case 'EncryptionKeyRotation':
      return await executeEncryptionKeyRotation(fn, data);
    case 'ComplianceViolationRemediation':
      return await executeComplianceViolationRemediation(fn, data);
    case 'LogRetentionEnforcement':
      return await executeLogRetentionEnforcement(fn, data);
    default:
      // Generic execution for custom functions
      return {
        message: `Executed function ${fn.name}`,
        input: data,
        timestamp: new Date().toISOString()
      };
  }
}

/**
 * Execute the PublicBucketRemediation function
 * @param {Object} fn The function configuration
 * @param {Object} data The input data
 * @returns {Promise<Object>} The result of the function execution
 */
async function executePublicBucketRemediation(fn, data) {
  // Simulate updating bucket ACL
  console.log(`Simulating removal of public access from bucket ${data.resource}`);
  
  // Simulate creating a finding note
  console.log(`Simulating creation of remediation finding for ${data.resource}`);
  
  // Simulate updating compliance status
  console.log(`Simulating update of compliance status for control pci-dss-req3.4`);
  
  // Simulate storing evidence
  try {
    // In a real implementation, this would call the Cloud Storage API
    const evidenceData = {
      type: 'AUTO_REMEDIATION',
      content: {
        finding: data,
        remediation: {
          action: 'REMOVE_PUBLIC_ACCESS',
          timestamp: new Date().toISOString(),
          resource: data.resource,
          status: 'SUCCESS'
        }
      },
      metadata: {
        control: 'pci-dss-req3.4',
        finding: data.id
      }
    };
    
    // Simulate API call to store evidence
    // In a real implementation, this would be an actual API call
    console.log(`Simulating storing evidence for remediation of ${data.resource}`);
    
    // Simulate sending notifications
    console.log(`Simulating sending notifications to ${fn.notificationChannels.join(', ')}`);
    
    // Return the result
    return {
      message: 'Public bucket access remediated successfully',
      resource: data.resource,
      complianceStatus: 'COMPLIANT',
      evidenceStored: true,
      notificationsSent: fn.notificationChannels.length
    };
  } catch (error) {
    console.error(`Error executing PublicBucketRemediation: ${error.message}`);
    throw error;
  }
}

/**
 * Execute the EncryptionKeyRotation function
 * @param {Object} fn The function configuration
 * @param {Object} data The input data
 * @returns {Promise<Object>} The result of the function execution
 */
async function executeEncryptionKeyRotation(fn, data) {
  // Simulate rotating KMS keys
  console.log(`Simulating rotation of encryption keys in ${fn.actions[0].parameters.keyRing}`);
  
  // Simulate updating compliance status
  console.log(`Simulating update of compliance status for control gdpr-art32-1a`);
  
  // Simulate storing evidence
  try {
    // In a real implementation, this would call the Cloud Storage API
    const evidenceData = {
      type: 'KEY_ROTATION',
      content: {
        keyRing: fn.actions[0].parameters.keyRing,
        rotationTimestamp: new Date().toISOString(),
        status: 'SUCCESS'
      },
      metadata: {
        control: 'gdpr-art32-1a'
      }
    };
    
    // Simulate API call to store evidence
    console.log(`Simulating storing evidence for key rotation`);
    
    // Simulate sending notifications
    console.log(`Simulating sending notifications to ${fn.notificationChannels.join(', ')}`);
    
    // Return the result
    return {
      message: 'Encryption keys rotated successfully',
      keyRing: fn.actions[0].parameters.keyRing,
      complianceStatus: 'COMPLIANT',
      evidenceStored: true,
      notificationsSent: fn.notificationChannels.length
    };
  } catch (error) {
    console.error(`Error executing EncryptionKeyRotation: ${error.message}`);
    throw error;
  }
}

/**
 * Execute the ComplianceViolationRemediation function
 * @param {Object} fn The function configuration
 * @param {Object} data The input data
 * @returns {Promise<Object>} The result of the function execution
 */
async function executeComplianceViolationRemediation(fn, data) {
  // Simulate applying compliance policy
  console.log(`Simulating application of compliance policy ${fn.actions[0].parameters.policyId} to ${data.resource}`);
  
  // Simulate creating a finding note
  console.log(`Simulating creation of remediation finding for ${data.resource}`);
  
  // Simulate storing evidence
  try {
    // In a real implementation, this would call the Cloud Storage API
    const evidenceData = {
      type: 'AUTO_REMEDIATION',
      content: {
        finding: data,
        remediation: {
          action: 'APPLY_COMPLIANCE_POLICY',
          policyId: fn.actions[0].parameters.policyId,
          timestamp: new Date().toISOString(),
          resource: data.resource,
          status: 'SUCCESS'
        }
      },
      metadata: {
        control: data.control,
        finding: data.id
      }
    };
    
    // Simulate API call to store evidence
    console.log(`Simulating storing evidence for compliance violation remediation of ${data.resource}`);
    
    // Simulate sending notifications
    console.log(`Simulating sending notifications to ${fn.notificationChannels.join(', ')}`);
    
    // Return the result
    return {
      message: 'Compliance violation remediated successfully',
      resource: data.resource,
      policyApplied: fn.actions[0].parameters.policyId,
      evidenceStored: true,
      notificationsSent: fn.notificationChannels.length
    };
  } catch (error) {
    console.error(`Error executing ComplianceViolationRemediation: ${error.message}`);
    throw error;
  }
}

/**
 * Execute the LogRetentionEnforcement function
 * @param {Object} fn The function configuration
 * @param {Object} data The input data
 * @returns {Promise<Object>} The result of the function execution
 */
async function executeLogRetentionEnforcement(fn, data) {
  // Simulate updating log retention policies
  const logTypes = fn.actions
    .filter(action => action.type === 'UPDATE_LOG_RETENTION')
    .map(action => ({
      logType: action.parameters.logType,
      retentionDays: action.parameters.retentionDays
    }));
  
  console.log(`Simulating update of log retention policies for ${logTypes.map(lt => lt.logType).join(', ')}`);
  
  // Simulate updating compliance status
  console.log(`Simulating update of compliance status for control hipaa-164-312b`);
  
  // Simulate storing evidence
  try {
    // In a real implementation, this would call the Cloud Storage API
    const evidenceData = {
      type: 'LOG_RETENTION',
      content: {
        logTypes,
        timestamp: new Date().toISOString(),
        status: 'SUCCESS'
      },
      metadata: {
        control: 'hipaa-164-312b'
      }
    };
    
    // Simulate API call to store evidence
    console.log(`Simulating storing evidence for log retention enforcement`);
    
    // Simulate sending notifications
    console.log(`Simulating sending notifications to ${fn.notificationChannels.join(', ')}`);
    
    // Return the result
    return {
      message: 'Log retention policies enforced successfully',
      logTypes,
      complianceStatus: 'COMPLIANT',
      evidenceStored: true,
      notificationsSent: fn.notificationChannels.length
    };
  } catch (error) {
    console.error(`Error executing LogRetentionEnforcement: ${error.message}`);
    throw error;
  }
}

// Start server
const PORT = process.env.CLOUD_FUNCTIONS_PORT || 8085;
app.listen(PORT, () => {
  console.log(`Cloud Functions simulator running on port ${PORT}`);
});

module.exports = app;
