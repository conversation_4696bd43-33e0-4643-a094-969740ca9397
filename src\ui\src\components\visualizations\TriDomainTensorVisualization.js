import React, { useRef, useEffect, useState } from 'react';
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';
import { Box, CircularProgress, Typography } from '@mui/material';

/**
 * TriDomainTensorVisualization component
 * 
 * Renders a 3D visualization showing the fusion of three domains:
 * - GRC (Governance, Risk, Compliance)
 * - IT (Information Technology)
 * - Cybersecurity
 * 
 * This visualization explicitly shows how data flows between the three domains
 * and highlights fusion points where domains interact.
 */
function TriDomainTensorVisualization({
  domainData = {
    grc: { values: [], health: 0.8, entropyContainment: 0.03 },
    it: { values: [], health: 0.8, entropyContainment: 0.03 },
    cybersecurity: { values: [], health: 0.8, entropyContainment: 0.03 },
    connections: [] // Array of { source: 'domain1', target: 'domain2', strength: 0.5 }
  },
  options = {
    renderMode: 'medium',
    showAxes: true,
    showGrid: true,
    rotationSpeed: 1,
    colorScheme: 'default',
    showLabels: true,
    highlightFusionPoints: true,
    connectionScale: 1.0
  },
  width = '100%',
  height = '100%'
}) {
  // Refs for Three.js objects
  const containerRef = useRef(null);
  const sceneRef = useRef(null);
  const cameraRef = useRef(null);
  const rendererRef = useRef(null);
  const controlsRef = useRef(null);
  const animationFrameRef = useRef(null);
  const domainsGroupRef = useRef(null);
  const connectionsGroupRef = useRef(null);
  const fusionPointsGroupRef = useRef(null);
  const labelsGroupRef = useRef(null);

  // State for loading and error handling
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  // Domain colors
  const domainColors = {
    grc: new THREE.Color(0x3366cc),      // Blue
    it: new THREE.Color(0x33cc33),       // Green
    cybersecurity: new THREE.Color(0xcc3333)  // Red
  };

  // Initialize Three.js scene
  useEffect(() => {
    if (!containerRef.current) return;

    try {
      // Create scene
      const scene = new THREE.Scene();
      scene.background = new THREE.Color(0x111111);
      sceneRef.current = scene;

      // Create camera
      const camera = new THREE.PerspectiveCamera(75, containerRef.current.clientWidth / containerRef.current.clientHeight, 0.1, 1000);
      camera.position.set(5, 5, 5);
      cameraRef.current = camera;

      // Create renderer
      const renderer = new THREE.WebGLRenderer({ antialias: options.renderMode !== 'low' });
      renderer.setSize(containerRef.current.clientWidth, containerRef.current.clientHeight);
      renderer.setPixelRatio(window.devicePixelRatio);
      containerRef.current.appendChild(renderer.domElement);
      rendererRef.current = renderer;

      // Create controls
      const controls = new OrbitControls(camera, renderer.domElement);
      controls.enableDamping = true;
      controls.dampingFactor = 0.05;
      controlsRef.current = controls;

      // Create groups for organization
      const domainsGroup = new THREE.Group();
      domainsGroup.name = 'domains';
      scene.add(domainsGroup);
      domainsGroupRef.current = domainsGroup;

      const connectionsGroup = new THREE.Group();
      connectionsGroup.name = 'connections';
      scene.add(connectionsGroup);
      connectionsGroupRef.current = connectionsGroup;

      const fusionPointsGroup = new THREE.Group();
      fusionPointsGroup.name = 'fusion-points';
      scene.add(fusionPointsGroup);
      fusionPointsGroupRef.current = fusionPointsGroup;

      const labelsGroup = new THREE.Group();
      labelsGroup.name = 'labels';
      scene.add(labelsGroup);
      labelsGroupRef.current = labelsGroup;

      // Add axes and grid if enabled
      if (options.showAxes) {
        const axesHelper = new THREE.AxesHelper(5);
        scene.add(axesHelper);
      }

      if (options.showGrid) {
        const gridHelper = new THREE.GridHelper(10, 10);
        scene.add(gridHelper);
      }

      // Set loading to false
      setIsLoading(false);

      // Handle window resize
      const handleResize = () => {
        if (containerRef.current && cameraRef.current && rendererRef.current) {
          const width = containerRef.current.clientWidth;
          const height = containerRef.current.clientHeight;
          
          cameraRef.current.aspect = width / height;
          cameraRef.current.updateProjectionMatrix();
          
          rendererRef.current.setSize(width, height);
        }
      };

      window.addEventListener('resize', handleResize);

      // Clean up
      return () => {
        window.removeEventListener('resize', handleResize);
        
        if (rendererRef.current && containerRef.current) {
          containerRef.current.removeChild(rendererRef.current.domElement);
        }
        
        if (animationFrameRef.current) {
          cancelAnimationFrame(animationFrameRef.current);
        }
      };
    } catch (err) {
      console.error('Error initializing 3D visualization:', err);
      setError(err.message || 'Error initializing visualization');
      setIsLoading(false);
    }
  }, [options.renderMode, options.showAxes, options.showGrid]);

  // Update visualization when domain data changes
  useEffect(() => {
    if (isLoading || !sceneRef.current || !domainsGroupRef.current || !connectionsGroupRef.current || !fusionPointsGroupRef.current) return;

    try {
      // Clear previous visualization
      while (domainsGroupRef.current.children.length > 0) {
        domainsGroupRef.current.remove(domainsGroupRef.current.children[0]);
      }
      
      while (connectionsGroupRef.current.children.length > 0) {
        connectionsGroupRef.current.remove(connectionsGroupRef.current.children[0]);
      }
      
      while (fusionPointsGroupRef.current.children.length > 0) {
        fusionPointsGroupRef.current.remove(fusionPointsGroupRef.current.children[0]);
      }
      
      while (labelsGroupRef.current.children.length > 0) {
        labelsGroupRef.current.remove(labelsGroupRef.current.children[0]);
      }

      // Domain positions
      const domainPositions = {
        grc: new THREE.Vector3(-3, 0, 0),
        it: new THREE.Vector3(0, 0, 3),
        cybersecurity: new THREE.Vector3(3, 0, 0)
      };

      // Create domain spheres
      Object.entries(domainData).forEach(([domain, data]) => {
        if (domain === 'connections') return; // Skip connections array
        
        if (!domainPositions[domain]) return; // Skip unknown domains
        
        // Create domain sphere
        const geometry = new THREE.SphereGeometry(1, 32, 32);
        const material = new THREE.MeshPhongMaterial({
          color: domainColors[domain] || 0xffffff,
          transparent: true,
          opacity: 0.7,
          emissive: domainColors[domain] || 0xffffff,
          emissiveIntensity: 0.2
        });
        
        const sphere = new THREE.Mesh(geometry, material);
        sphere.position.copy(domainPositions[domain]);
        sphere.userData = { domain, health: data.health, entropyContainment: data.entropyContainment };
        
        // Scale sphere based on health
        const scale = 0.5 + (data.health * 0.5);
        sphere.scale.set(scale, scale, scale);
        
        domainsGroupRef.current.add(sphere);
        
        // Add domain label if enabled
        if (options.showLabels) {
          const domainLabel = document.createElement('div');
          domainLabel.className = 'domain-label';
          domainLabel.textContent = domain.toUpperCase();
          domainLabel.style.color = '#ffffff';
          
          const label = new CSS2DObject(domainLabel);
          label.position.copy(domainPositions[domain]);
          label.position.y += 1.5;
          
          labelsGroupRef.current.add(label);
        }
      });

      // Create connections between domains
      if (domainData.connections && domainData.connections.length > 0) {
        domainData.connections.forEach(connection => {
          if (!domainPositions[connection.source] || !domainPositions[connection.target]) return;
          
          const sourcePos = domainPositions[connection.source];
          const targetPos = domainPositions[connection.target];
          
          // Create connection line
          const points = [];
          points.push(sourcePos);
          points.push(targetPos);
          
          const geometry = new THREE.BufferGeometry().setFromPoints(points);
          
          // Blend colors of source and target domains
          const sourceColor = domainColors[connection.source] || new THREE.Color(0xffffff);
          const targetColor = domainColors[connection.target] || new THREE.Color(0xffffff);
          const blendedColor = new THREE.Color().lerpColors(sourceColor, targetColor, 0.5);
          
          const material = new THREE.LineBasicMaterial({
            color: blendedColor,
            linewidth: 1 + (connection.strength * 5 * options.connectionScale),
            transparent: true,
            opacity: connection.strength
          });
          
          const line = new THREE.Line(geometry, material);
          line.userData = { 
            source: connection.source, 
            target: connection.target, 
            strength: connection.strength 
          };
          
          connectionsGroupRef.current.add(line);
          
          // Create fusion point at midpoint if enabled
          if (options.highlightFusionPoints && connection.strength > 0.3) {
            const midpoint = new THREE.Vector3().lerpVectors(sourcePos, targetPos, 0.5);
            
            const fusionGeometry = new THREE.SphereGeometry(0.2 + (connection.strength * 0.3), 16, 16);
            const fusionMaterial = new THREE.MeshPhongMaterial({
              color: blendedColor,
              transparent: true,
              opacity: 0.8,
              emissive: blendedColor,
              emissiveIntensity: 0.5
            });
            
            const fusionPoint = new THREE.Mesh(fusionGeometry, fusionMaterial);
            fusionPoint.position.copy(midpoint);
            fusionPoint.userData = { 
              source: connection.source, 
              target: connection.target, 
              strength: connection.strength 
            };
            
            fusionPointsGroupRef.current.add(fusionPoint);
          }
        });
      }

      // Add lights
      if (sceneRef.current.children.length < 5) { // Only add lights once
        const ambientLight = new THREE.AmbientLight(0x404040);
        sceneRef.current.add(ambientLight);
        
        const directionalLight1 = new THREE.DirectionalLight(0xffffff, 0.5);
        directionalLight1.position.set(1, 1, 1);
        sceneRef.current.add(directionalLight1);
        
        const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.3);
        directionalLight2.position.set(-1, -1, -1);
        sceneRef.current.add(directionalLight2);
      }
    } catch (err) {
      console.error('Error updating tri-domain visualization:', err);
      setError(err.message || 'Error updating visualization');
    }
  }, [domainData, options.showLabels, options.highlightFusionPoints, options.connectionScale, isLoading]);

  // Animation loop
  useEffect(() => {
    if (isLoading) return;

    // Animation loop with rotation
    const animate = () => {
      animationFrameRef.current = requestAnimationFrame(animate);
      
      // Rotate domains group based on rotation speed
      if (domainsGroupRef.current && options.rotationSpeed > 0) {
        domainsGroupRef.current.rotation.y += 0.005 * options.rotationSpeed;
      }
      
      // Pulse fusion points
      if (fusionPointsGroupRef.current && options.highlightFusionPoints) {
        const time = Date.now() * 0.001;
        fusionPointsGroupRef.current.children.forEach((fusionPoint, index) => {
          const offset = index * 0.2;
          const scale = 1 + 0.2 * Math.sin(time + offset);
          fusionPoint.scale.set(scale, scale, scale);
        });
      }
      
      // Update controls
      if (controlsRef.current) {
        controlsRef.current.update();
      }
      
      // Render scene
      if (rendererRef.current && sceneRef.current && cameraRef.current) {
        rendererRef.current.render(sceneRef.current, cameraRef.current);
      }
    };

    // Start animation loop
    animate();

    // Clean up
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [options.rotationSpeed, options.highlightFusionPoints, isLoading]);

  // CSS2D Renderer for labels
  class CSS2DObject extends THREE.Object3D {
    constructor(element) {
      super();
      this.element = element;
      this.element.style.position = 'absolute';
      this.element.style.fontSize = '14px';
      this.element.style.fontWeight = 'bold';
      this.element.style.textAlign = 'center';
      this.element.style.pointerEvents = 'none';
      
      const vector = new THREE.Vector3();
      const setPosition = (x, y, z) => {
        vector.set(x, y, z);
        vector.project(cameraRef.current);
        
        const widthHalf = containerRef.current.clientWidth / 2;
        const heightHalf = containerRef.current.clientHeight / 2;
        
        this.element.style.left = (vector.x * widthHalf + widthHalf) + 'px';
        this.element.style.top = (- vector.y * heightHalf + heightHalf) + 'px';
      };
      
      this.setPosition = setPosition;
    }
  }

  // Update label positions in animation loop
  useEffect(() => {
    if (isLoading || !labelsGroupRef.current || !options.showLabels) return;
    
    const updateLabels = () => {
      if (labelsGroupRef.current && labelsGroupRef.current.children) {
        labelsGroupRef.current.children.forEach(label => {
          if (label instanceof CSS2DObject) {
            label.setPosition(label.position.x, label.position.y, label.position.z);
          }
        });
      }
      
      requestAnimationFrame(updateLabels);
    };
    
    updateLabels();
  }, [isLoading, options.showLabels]);

  return (
    <Box
      ref={containerRef}
      sx={{
        width,
        height,
        position: 'relative',
        overflow: 'hidden'
      }}
    >
      {isLoading && (
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: 'rgba(0, 0, 0, 0.5)'
          }}
        >
          <CircularProgress />
        </Box>
      )}
      
      {error && (
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: 'rgba(0, 0, 0, 0.7)',
            color: 'error.main',
            p: 2
          }}
        >
          <Typography variant="body1" color="error">
            Error: {error}
          </Typography>
        </Box>
      )}
    </Box>
  );
}

export default TriDomainTensorVisualization;
