const request = require('supertest');
const app = require('./mockApp');

describe('Privacy Management API - Integration Tests', () => {
  describe('Data Subject Request Lifecycle', () => {
    it('should handle the complete lifecycle of a data subject request', async () => {
      // Step 1: Create a data subject request
      const newRequest = {
        requestType: 'access',
        dataSubjectName: '<PERSON>',
        dataSubjectEmail: '<EMAIL>',
        requestDetails: 'I would like to access all my personal data',
        status: 'pending'
      };

      const createResponse = await request(app)
        .post('/privacy/management/subject-requests')
        .send(newRequest);

      expect(createResponse.status).toBe(201);
      const requestId = createResponse.body.data.id;

      // Step 2: Determine affected systems
      const affectedSystemsResponse = await request(app)
        .get(`/privacy/management/subject-requests/${requestId}/affected-systems`);

      expect(affectedSystemsResponse.status).toBe(200);
      expect(affectedSystemsResponse.body.data).toBeDefined();
      expect(Array.isArray(affectedSystemsResponse.body.data)).toBe(true);
      expect(affectedSystemsResponse.body.data.length).toBeGreaterThan(0);

      // Step 3: Process the request
      const processResponse = await request(app)
        .post(`/privacy/management/subject-requests/${requestId}/process`);

      expect(processResponse.status).toBe(200);
      expect(processResponse.body.data).toBeDefined();
      expect(processResponse.body.data.automationStatus).toBeDefined();

      // Step 4: Generate a data export
      const exportResponse = await request(app)
        .get(`/privacy/management/subject-requests/${requestId}/export`);

      expect(exportResponse.status).toBe(200);
      expect(exportResponse.body.data).toBeDefined();
      expect(exportResponse.body.data.requestId).toBe(requestId);
      expect(exportResponse.body.data.systems).toBeDefined();

      // Step 5: Update the request status to completed
      const updateResponse = await request(app)
        .put(`/privacy/management/subject-requests/${requestId}`)
        .send({
          status: 'completed',
          completionDate: new Date().toISOString(),
          responseDetails: 'Request completed successfully. Data export provided to data subject.'
        });

      expect(updateResponse.status).toBe(200);
      expect(updateResponse.body.data).toBeDefined();
      expect(updateResponse.body.data.status).toBe('completed');
      expect(updateResponse.body.data.completionDate).toBeDefined();

      // Step 6: Generate a report that includes this request
      const reportResponse = await request(app)
        .get('/privacy/management/reports/dsr-summary')
        .query({ period: 'last-30-days' });

      expect(reportResponse.status).toBe(200);
      expect(reportResponse.body.data).toBeDefined();
      expect(reportResponse.body.data.metrics).toBeDefined();
      expect(reportResponse.body.data.metrics.completedRequests).toBeGreaterThan(0);
    });
  });

  describe('Consent Management Lifecycle', () => {
    it('should handle the complete lifecycle of consent management', async () => {
      // Step 1: Generate a consent form
      const formResponse = await request(app)
        .get('/privacy/management/consent/forms')
        .query({ consentType: 'marketing', language: 'en' });

      expect(formResponse.status).toBe(200);
      expect(formResponse.body.data).toBeDefined();
      expect(formResponse.body.data.consentType).toBe('marketing');
      expect(formResponse.body.data.consentTitle).toBeDefined();
      expect(formResponse.body.data.consentDescription).toBeDefined();

      // Step 2: Generate consent proof
      const proofData = {
        ip: '***********',
        userAgent: 'Mozilla/5.0',
        timestamp: new Date().toISOString()
      };

      const proofResponse = await request(app)
        .post('/privacy/management/consent/proof/generate')
        .send(proofData);

      expect(proofResponse.status).toBe(200);
      expect(proofResponse.body.data).toBeDefined();
      expect(proofResponse.body.data.consentProof).toBeDefined();
      const consentProof = proofResponse.body.data.consentProof;

      // Step 3: Create a consent record
      const newConsent = {
        dataSubjectId: 'ds-0001',
        dataSubjectName: 'John Doe',
        dataSubjectEmail: '<EMAIL>',
        consentType: 'marketing',
        consentDescription: formResponse.body.data.consentDescription,
        consentGiven: true,
        consentVersion: formResponse.body.data.consentVersion,
        consentMethod: 'online-form',
        privacyNoticeVersion: formResponse.body.data.privacyNoticeVersion,
        consentProof: consentProof
      };

      const createResponse = await request(app)
        .post('/privacy/management/consent-records')
        .send(newConsent);

      expect(createResponse.status).toBe(201);
      const consentId = createResponse.body.data.id;

      // Step 4: Verify consent validity
      const validityResponse = await request(app)
        .get(`/privacy/management/consent/${consentId}/validity`);

      expect(validityResponse.status).toBe(200);
      expect(validityResponse.body.data).toBeDefined();
      expect(validityResponse.body.data.valid).toBe(true);
      expect(validityResponse.body.data.record).toBeDefined();

      // Step 5: Get consent records by data subject
      const recordsBySubjectResponse = await request(app)
        .get(`/privacy/management/consent/data-subjects/${newConsent.dataSubjectId}`);

      expect(recordsBySubjectResponse.status).toBe(200);
      expect(recordsBySubjectResponse.body.data).toBeDefined();
      expect(Array.isArray(recordsBySubjectResponse.body.data)).toBe(true);
      expect(recordsBySubjectResponse.body.data.length).toBeGreaterThan(0);
      expect(recordsBySubjectResponse.body.data.some(record => record.id === consentId)).toBe(true);

      // Step 6: Get consent records by email
      const recordsByEmailResponse = await request(app)
        .get(`/privacy/management/consent/emails/${newConsent.dataSubjectEmail}`);

      expect(recordsByEmailResponse.status).toBe(200);
      expect(recordsByEmailResponse.body.data).toBeDefined();
      expect(Array.isArray(recordsByEmailResponse.body.data)).toBe(true);
      expect(recordsByEmailResponse.body.data.length).toBeGreaterThan(0);
      expect(recordsByEmailResponse.body.data.some(record => record.id === consentId)).toBe(true);

      // Step 7: Withdraw consent
      const withdrawResponse = await request(app)
        .post(`/privacy/management/consent/${consentId}/withdraw`)
        .send({ withdrawalMethod: 'online-form' });

      expect(withdrawResponse.status).toBe(200);
      expect(withdrawResponse.body.data).toBeDefined();
      expect(withdrawResponse.body.data.status).toBe('withdrawn');
      expect(withdrawResponse.body.data.withdrawalDate).toBeDefined();
      expect(withdrawResponse.body.data.withdrawalMethod).toBe('online-form');

      // Step 8: Verify consent is now invalid
      const invalidityResponse = await request(app)
        .get(`/privacy/management/consent/${consentId}/validity`);

      expect(invalidityResponse.status).toBe(200);
      expect(invalidityResponse.body.data).toBeDefined();
      expect(invalidityResponse.body.data.valid).toBe(false);
      expect(invalidityResponse.body.data.reason).toBe('Consent was withdrawn');

      // Step 9: Generate a consent management report
      const reportResponse = await request(app)
        .get('/privacy/management/reports/consent-management')
        .query({ period: 'last-30-days' });

      expect(reportResponse.status).toBe(200);
      expect(reportResponse.body.data).toBeDefined();
      expect(reportResponse.body.data.metrics).toBeDefined();
      expect(reportResponse.body.data.metrics.withdrawnConsents).toBeGreaterThan(0);
    });
  });

  describe('Notification System Integration', () => {
    it('should handle the complete lifecycle of notifications', async () => {
      // Step 1: Create a notification
      const newNotification = {
        type: 'dsr-received',
        title: 'New DSR Received',
        message: 'A new data subject request has been received',
        recipients: ['privacy-team'],
        priority: 'medium',
        channels: ['email', 'in-app'],
        relatedEntityType: 'data-subject-request',
        relatedEntityId: 'dsr-0001',
        metadata: {
          requestType: 'access',
          dataSubjectName: 'John Doe'
        }
      };

      const createResponse = await request(app)
        .post('/privacy/management/notifications')
        .send(newNotification);

      expect(createResponse.status).toBe(201);
      const notificationId = createResponse.body.data.id;

      // Step 2: Get the notification
      const getResponse = await request(app)
        .get(`/privacy/management/notifications/${notificationId}`);

      expect(getResponse.status).toBe(200);
      expect(getResponse.body.data).toBeDefined();
      expect(getResponse.body.data.id).toBe(notificationId);
      expect(getResponse.body.data.status).toBe('pending');

      // Step 3: Send the notification
      const sendResponse = await request(app)
        .post(`/privacy/management/notifications/${notificationId}/send`);

      expect(sendResponse.status).toBe(200);
      expect(sendResponse.body.data).toBeDefined();
      expect(sendResponse.body.data.status).toBe('sent');
      expect(sendResponse.body.data.sentAt).toBeDefined();

      // Step 4: Mark the notification as read
      const readResponse = await request(app)
        .post(`/privacy/management/notifications/${notificationId}/read`);

      expect(readResponse.status).toBe(200);
      expect(readResponse.body.data).toBeDefined();
      expect(readResponse.body.data.status).toBe('read');
      expect(readResponse.body.data.readAt).toBeDefined();

      // Step 5: Filter notifications by status
      const filterResponse = await request(app)
        .get('/privacy/management/notifications')
        .query({ status: 'read' });

      expect(filterResponse.status).toBe(200);
      expect(filterResponse.body.data).toBeDefined();
      expect(Array.isArray(filterResponse.body.data)).toBe(true);
      expect(filterResponse.body.data.some(notification => notification.id === notificationId)).toBe(true);

      // All returned notifications should have the specified status
      filterResponse.body.data.forEach(notification => {
        expect(notification.status).toBe('read');
      });

      // Step 6: Generate notifications
      const generateResponse = await request(app)
        .post('/privacy/management/notifications/generate');

      expect(generateResponse.status).toBe(200);
      expect(generateResponse.body.data).toBeDefined();
      expect(Array.isArray(generateResponse.body.data)).toBe(true);
      expect(generateResponse.body.message).toBeDefined();

      // Step 7: Send all pending notifications
      const sendAllResponse = await request(app)
        .post('/privacy/management/notifications/send-all');

      expect(sendAllResponse.status).toBe(200);
      expect(sendAllResponse.body.data).toBeDefined();
      expect(sendAllResponse.body.data.total).toBeDefined();
      expect(sendAllResponse.body.data.sent).toBeDefined();
      expect(sendAllResponse.body.data.details).toBeDefined();
    });
  });
});
