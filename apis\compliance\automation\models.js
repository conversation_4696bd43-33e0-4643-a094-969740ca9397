/**
 * @swagger
 * components:
 *   schemas:
 *     ComplianceFramework:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: Unique identifier for the compliance framework
 *         name:
 *           type: string
 *           description: Name of the compliance framework
 *         description:
 *           type: string
 *           description: Description of the compliance framework
 *         version:
 *           type: string
 *           description: Version of the compliance framework
 *         category:
 *           type: string
 *           description: Category of the compliance framework (e.g., privacy, security, industry-specific)
 *         authority:
 *           type: string
 *           description: Issuing authority or organization
 *         website:
 *           type: string
 *           description: Official website for the framework
 *         applicability:
 *           type: array
 *           items:
 *             type: string
 *           description: Industries or regions where the framework applies
 *         status:
 *           type: string
 *           enum: [active, deprecated, draft]
 *           description: Current status of the framework
 *         effectiveDate:
 *           type: string
 *           format: date
 *           description: Date when the framework became effective
 *         expirationDate:
 *           type: string
 *           format: date
 *           description: Date when the framework expires (if applicable)
 *         riskLevel:
 *           type: string
 *           enum: [critical, high, medium, low]
 *           description: Risk level associated with non-compliance
 *         complianceScore:
 *           type: number
 *           minimum: 0
 *           maximum: 100
 *           description: Current compliance score as a percentage
 *         lastAssessmentDate:
 *           type: string
 *           format: date
 *           description: Date when the framework was last assessed
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the framework was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the framework was last updated
 *       required:
 *         - id
 *         - name
 *         - description
 *         - version
 *         - category
 *         - createdAt
 *         - updatedAt
 *
 *     ComplianceRequirement:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: Unique identifier for the compliance requirement
 *         frameworkId:
 *           type: string
 *           description: ID of the associated compliance framework
 *         code:
 *           type: string
 *           description: Code or reference number for the requirement
 *         title:
 *           type: string
 *           description: Title of the requirement
 *         description:
 *           type: string
 *           description: Description of the requirement
 *         category:
 *           type: string
 *           description: Category of the requirement
 *         priority:
 *           type: string
 *           enum: [critical, high, medium, low]
 *           description: Priority level of the requirement
 *         status:
 *           type: string
 *           enum: [applicable, not-applicable, under-review]
 *           description: Status of the requirement
 *         evidenceRequired:
 *           type: boolean
 *           description: Whether evidence is required for this requirement
 *         automationStatus:
 *           type: string
 *           enum: [manual, semi-automated, fully-automated]
 *           description: Level of automation for this requirement
 *         implementationGuidelines:
 *           type: string
 *           description: Guidelines for implementing this requirement
 *         verificationMethod:
 *           type: string
 *           enum: [documentation-review, interview, observation, testing]
 *           description: Method used to verify compliance with this requirement
 *         riskLevel:
 *           type: string
 *           enum: [critical, high, medium, low]
 *           description: Risk level associated with non-compliance
 *         dueDate:
 *           type: string
 *           format: date
 *           description: Date by which the requirement must be met
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the requirement was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the requirement was last updated
 *       required:
 *         - id
 *         - frameworkId
 *         - code
 *         - title
 *         - description
 *         - priority
 *         - status
 *         - createdAt
 *         - updatedAt
 *
 *     ComplianceControl:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: Unique identifier for the compliance control
 *         name:
 *           type: string
 *           description: Name of the control
 *         description:
 *           type: string
 *           description: Description of the control
 *         type:
 *           type: string
 *           enum: [preventive, detective, corrective, administrative, technical, physical]
 *           description: Type of control
 *         status:
 *           type: string
 *           enum: [implemented, partially-implemented, not-implemented, planned]
 *           description: Implementation status of the control
 *         owner:
 *           type: string
 *           description: Person or team responsible for the control
 *         implementationDetails:
 *           type: string
 *           description: Details about how the control is implemented
 *         automationDetails:
 *           type: string
 *           description: Details about how the control is automated
 *         automationStatus:
 *           type: string
 *           enum: [manual, semi-automated, fully-automated]
 *           description: Level of automation for this control
 *         effectiveness:
 *           type: string
 *           enum: [effective, partially-effective, ineffective, not-assessed]
 *           description: Effectiveness rating of the control
 *         testProcedure:
 *           type: string
 *           description: Procedure for testing the control
 *         lastTestedDate:
 *           type: string
 *           format: date
 *           description: Date when the control was last tested
 *         nextTestDate:
 *           type: string
 *           format: date
 *           description: Date when the control should be tested next
 *         lastReviewDate:
 *           type: string
 *           format: date
 *           description: Date when the control was last reviewed
 *         reviewFrequency:
 *           type: string
 *           enum: [daily, weekly, monthly, quarterly, annually, as-needed]
 *           description: How frequently the control should be reviewed
 *         relatedRequirements:
 *           type: array
 *           items:
 *             type: string
 *           description: IDs of requirements addressed by this control
 *         evidenceIds:
 *           type: array
 *           items:
 *             type: string
 *           description: IDs of evidence supporting this control
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the control was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the control was last updated
 *       required:
 *         - id
 *         - name
 *         - description
 *         - type
 *         - status
 *         - owner
 *         - createdAt
 *         - updatedAt
 *
 *     ComplianceAssessment:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: Unique identifier for the compliance assessment
 *         frameworkId:
 *           type: string
 *           description: ID of the associated compliance framework
 *         name:
 *           type: string
 *           description: Name of the assessment
 *         description:
 *           type: string
 *           description: Description of the assessment
 *         assessor:
 *           type: string
 *           description: Person or organization conducting the assessment
 *         startDate:
 *           type: string
 *           format: date
 *           description: Start date of the assessment
 *         endDate:
 *           type: string
 *           format: date
 *           description: End date of the assessment
 *         status:
 *           type: string
 *           enum: [planned, in-progress, completed, cancelled]
 *           description: Status of the assessment
 *         scope:
 *           type: string
 *           description: Scope of the assessment
 *         findings:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               id:
 *                 type: string
 *               requirementId:
 *                 type: string
 *               status:
 *                 type: string
 *                 enum: [compliant, non-compliant, partially-compliant, not-applicable]
 *               notes:
 *                 type: string
 *               remediationPlan:
 *                 type: string
 *               remediationDueDate:
 *                 type: string
 *                 format: date
 *           description: Assessment findings
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the assessment was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the assessment was last updated
 *       required:
 *         - id
 *         - frameworkId
 *         - name
 *         - assessor
 *         - startDate
 *         - status
 *         - createdAt
 *         - updatedAt
 *
 *     ComplianceEvidence:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: Unique identifier for the compliance evidence
 *         controlId:
 *           type: string
 *           description: ID of the associated control
 *         name:
 *           type: string
 *           description: Name of the evidence
 *         description:
 *           type: string
 *           description: Description of the evidence
 *         type:
 *           type: string
 *           enum: [document, screenshot, log, report, certification, attestation, other]
 *           description: Type of evidence
 *         location:
 *           type: string
 *           description: Location or URL where the evidence is stored
 *         collectedBy:
 *           type: string
 *           description: Person who collected the evidence
 *         collectedAt:
 *           type: string
 *           format: date
 *           description: Date when the evidence was collected
 *         expiresAt:
 *           type: string
 *           format: date
 *           description: Date when the evidence expires (if applicable)
 *         status:
 *           type: string
 *           enum: [valid, expired, pending-review, rejected]
 *           description: Status of the evidence
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the evidence was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the evidence was last updated
 *       required:
 *         - id
 *         - controlId
 *         - name
 *         - type
 *         - collectedBy
 *         - collectedAt
 *         - status
 *         - createdAt
 *         - updatedAt
 */

// Sample compliance frameworks
const complianceFrameworks = [
  {
    id: 'cf-001',
    name: 'SOC 2',
    description: 'Service Organization Control 2 - A framework for managing customer data based on five trust service criteria',
    version: '2017',
    category: 'security',
    authority: 'American Institute of CPAs (AICPA)',
    website: 'https://www.aicpa.org/soc2',
    applicability: ['Technology Service Providers', 'SaaS Companies', 'Cloud Service Providers'],
    createdAt: '2023-01-15T10:00:00Z',
    updatedAt: '2023-01-15T10:00:00Z'
  },
  {
    id: 'cf-002',
    name: 'GDPR',
    description: 'General Data Protection Regulation - A regulation on data protection and privacy in the European Union',
    version: '2018',
    category: 'privacy',
    authority: 'European Union',
    website: 'https://gdpr.eu',
    applicability: ['Organizations processing EU citizens data', 'Global companies with EU customers'],
    createdAt: '2023-01-20T14:30:00Z',
    updatedAt: '2023-01-20T14:30:00Z'
  },
  {
    id: 'cf-003',
    name: 'HIPAA',
    description: 'Health Insurance Portability and Accountability Act - U.S. legislation that provides data privacy and security provisions for safeguarding medical information',
    version: '1996 (with 2013 updates)',
    category: 'healthcare',
    authority: 'U.S. Department of Health and Human Services',
    website: 'https://www.hhs.gov/hipaa',
    applicability: ['Healthcare Providers', 'Health Plans', 'Healthcare Clearinghouses', 'Business Associates'],
    createdAt: '2023-02-05T09:15:00Z',
    updatedAt: '2023-02-05T09:15:00Z'
  }
];

// Sample compliance requirements
const complianceRequirements = [
  {
    id: 'cr-001',
    frameworkId: 'cf-001',
    code: 'CC1.1',
    title: 'Management establishes responsibility and accountability',
    description: 'Management establishes responsibility and accountability for developing and maintaining the entity's system of internal control.',
    category: 'Control Environment',
    priority: 'high',
    status: 'applicable',
    createdAt: '2023-01-16T11:30:00Z',
    updatedAt: '2023-01-16T11:30:00Z'
  },
  {
    id: 'cr-002',
    frameworkId: 'cf-001',
    code: 'CC5.1',
    title: 'Logical access security software',
    description: 'The entity uses logical access security software, infrastructure, and architectures for authentication, authorization, and encryption.',
    category: 'Logical and Physical Access Controls',
    priority: 'critical',
    status: 'applicable',
    createdAt: '2023-01-16T13:45:00Z',
    updatedAt: '2023-01-16T13:45:00Z'
  },
  {
    id: 'cr-003',
    frameworkId: 'cf-002',
    code: 'Art.5',
    title: 'Principles relating to processing of personal data',
    description: 'Personal data shall be processed lawfully, fairly and in a transparent manner in relation to the data subject.',
    category: 'Data Processing Principles',
    priority: 'critical',
    status: 'applicable',
    createdAt: '2023-01-21T09:20:00Z',
    updatedAt: '2023-01-21T09:20:00Z'
  },
  {
    id: 'cr-004',
    frameworkId: 'cf-002',
    code: 'Art.17',
    title: 'Right to erasure (right to be forgotten)',
    description: 'The data subject shall have the right to obtain from the controller the erasure of personal data concerning him or her without undue delay.',
    category: 'Data Subject Rights',
    priority: 'high',
    status: 'applicable',
    createdAt: '2023-01-21T10:15:00Z',
    updatedAt: '2023-01-21T10:15:00Z'
  },
  {
    id: 'cr-005',
    frameworkId: 'cf-003',
    code: '164.308(a)(1)(i)',
    title: 'Security Management Process',
    description: 'Implement policies and procedures to prevent, detect, contain, and correct security violations.',
    category: 'Administrative Safeguards',
    priority: 'high',
    status: 'applicable',
    createdAt: '2023-02-06T14:10:00Z',
    updatedAt: '2023-02-06T14:10:00Z'
  }
];

// Sample compliance assessments
const complianceAssessments = [
  {
    id: 'ca-001',
    name: 'Annual SOC 2 Readiness Assessment',
    description: 'Annual assessment to evaluate readiness for SOC 2 audit',
    frameworkId: 'cf-001',
    status: 'completed',
    startDate: '2023-01-10',
    endDate: '2023-01-25',
    assessor: 'Internal Audit Team',
    assessorType: 'internal',
    overallScore: 85,
    findings: [
      {
        id: 'f-001',
        requirementId: 'cr-002',
        description: 'Access review process not consistently documented',
        severity: 'medium',
        status: 'in-remediation'
      },
      {
        id: 'f-002',
        requirementId: 'cr-005',
        description: 'Incident response plan not tested in last 12 months',
        severity: 'high',
        status: 'open'
      }
    ],
    recommendations: [
      {
        id: 'r-001',
        description: 'Implement quarterly access reviews with documentation',
        priority: 'high',
        status: 'in-progress'
      },
      {
        id: 'r-002',
        description: 'Conduct incident response tabletop exercise',
        priority: 'high',
        status: 'planned'
      }
    ],
    createdAt: '2023-01-05T09:00:00Z',
    updatedAt: '2023-01-26T14:30:00Z'
  },
  {
    id: 'ca-002',
    name: 'GDPR Compliance Review',
    description: 'Review of GDPR compliance controls and processes',
    frameworkId: 'cf-003',
    status: 'in-progress',
    startDate: '2023-03-15',
    endDate: null,
    assessor: 'Privacy Consultants Inc.',
    assessorType: 'external',
    overallScore: null,
    findings: [],
    recommendations: [],
    createdAt: '2023-03-10T11:15:00Z',
    updatedAt: '2023-03-15T09:30:00Z'
  }
];

// Sample automation rules
const complianceAutomationRules = [
  {
    id: 'ar-001',
    name: 'Quarterly Access Review',
    description: 'Automatically trigger quarterly access reviews',
    requirementId: 'cr-002',
    triggerType: 'scheduled',
    triggerCondition: 'First day of each quarter',
    actions: [
      {
        type: 'create-task',
        parameters: {
          assignee: 'Security Team',
          dueDate: '+14 days',
          title: 'Quarterly Access Review',
          description: 'Conduct quarterly access review for all systems'
        }
      },
      {
        type: 'notify',
        parameters: {
          recipients: ['<EMAIL>', '<EMAIL>'],
          subject: 'Quarterly Access Review Due',
          message: 'The quarterly access review is now due. Please complete within 14 days.'
        }
      }
    ],
    schedule: '0 0 1 1,4,7,10 *',
    frequency: 'quarterly',
    status: 'active',
    lastExecutionDate: '2023-01-01T00:00:00Z',
    nextExecutionDate: '2023-04-01T00:00:00Z',
    createdAt: '2022-12-15T10:00:00Z',
    updatedAt: '2023-01-01T00:05:00Z'
  },
  {
    id: 'ar-002',
    name: 'Security Patch Compliance Check',
    description: 'Verify systems are patched within required timeframe',
    requirementId: 'cr-007',
    triggerType: 'scheduled',
    triggerCondition: 'Every Monday at 8:00 AM',
    actions: [
      {
        type: 'collect-evidence',
        parameters: {
          evidenceType: 'report',
          source: 'Patch Management System',
          query: 'SELECT * FROM patch_status WHERE status = "pending" AND age > 30'
        }
      },
      {
        type: 'update-status',
        parameters: {
          condition: 'COUNT(*) > 0',
          status: 'non-compliant',
          requirementId: 'cr-007'
        }
      }
    ],
    schedule: '0 8 * * 1',
    frequency: 'weekly',
    status: 'active',
    lastExecutionDate: '2023-03-27T08:00:00Z',
    nextExecutionDate: '2023-04-03T08:00:00Z',
    createdAt: '2022-11-20T15:30:00Z',
    updatedAt: '2023-03-27T08:05:00Z'
  }
];

// Sample compliance controls
const complianceControls = [
  {
    id: 'cc-001',
    name: 'Access Control Policy',
    description: 'Formal policy that defines access control requirements, including role-based access, least privilege, and access review procedures',
    type: 'administrative',
    status: 'implemented',
    owner: 'Security Team',
    implementationDetails: 'Documented in the Information Security Policy, Section 4.2',
    testProcedure: 'Review the policy document and verify it includes all required elements',
    lastTestedDate: '2023-03-10',
    nextTestDate: '2023-09-10',
    relatedRequirements: ['cr-002', 'cr-005'],
    createdAt: '2023-01-25T15:30:00Z',
    updatedAt: '2023-03-11T09:45:00Z'
  },
  {
    id: 'cc-002',
    name: 'Multi-Factor Authentication',
    description: 'Implementation of MFA for all administrative access and remote access to systems containing sensitive data',
    type: 'technical',
    status: 'implemented',
    owner: 'IT Infrastructure Team',
    implementationDetails: 'Using Microsoft Azure AD for MFA with mobile app verification',
    testProcedure: 'Attempt to access admin interfaces without completing MFA challenge',
    lastTestedDate: '2023-03-15',
    nextTestDate: '2023-06-15',
    relatedRequirements: ['cr-002'],
    createdAt: '2023-01-30T11:20:00Z',
    updatedAt: '2023-03-16T14:30:00Z'
  },
  {
    id: 'cc-003',
    name: 'Data Retention and Deletion Procedures',
    description: 'Procedures for retaining data only as long as necessary and securely deleting data upon request or when no longer needed',
    type: 'administrative',
    status: 'partially-implemented',
    owner: 'Data Governance Team',
    implementationDetails: 'Procedures documented but automated deletion workflows still in development',
    testProcedure: 'Review documentation and sample a data deletion request to verify proper handling',
    lastTestedDate: '2023-02-20',
    nextTestDate: '2023-05-20',
    relatedRequirements: ['cr-003', 'cr-004'],
    createdAt: '2023-02-01T13:45:00Z',
    updatedAt: '2023-02-21T10:15:00Z'
  },
  {
    id: 'cc-004',
    name: 'Security Incident Response Plan',
    description: 'Documented plan for detecting, responding to, and recovering from security incidents',
    type: 'administrative',
    status: 'implemented',
    owner: 'Security Incident Response Team',
    implementationDetails: 'Documented in the Incident Response Playbook with specific procedures for different incident types',
    testProcedure: 'Conduct tabletop exercise simulating a security breach',
    lastTestedDate: '2023-04-05',
    nextTestDate: '2023-10-05',
    relatedRequirements: ['cr-001', 'cr-005'],
    createdAt: '2023-02-10T09:30:00Z',
    updatedAt: '2023-04-06T16:20:00Z'
  }
];

// Sample compliance assessments
const complianceAssessments = [
  {
    id: 'ca-001',
    frameworkId: 'cf-001',
    name: 'Annual SOC 2 Type II Assessment',
    description: 'Comprehensive assessment of SOC 2 controls for Type II report',
    assessor: 'External Audit Firm XYZ',
    startDate: '2023-04-01',
    endDate: '2023-05-15',
    status: 'completed',
    scope: 'All SOC 2 Trust Services Criteria',
    findings: [
      {
        id: 'f-001',
        requirementId: 'cr-001',
        status: 'compliant',
        notes: 'All required documentation and evidence provided',
        remediationPlan: '',
        remediationDueDate: ''
      },
      {
        id: 'f-002',
        requirementId: 'cr-002',
        status: 'partially-compliant',
        notes: 'MFA implemented but not enforced for all administrative accounts',
        remediationPlan: 'Enable MFA for remaining admin accounts',
        remediationDueDate: '2023-06-30'
      }
    ],
    createdAt: '2023-03-15T10:00:00Z',
    updatedAt: '2023-05-16T14:30:00Z'
  },
  {
    id: 'ca-002',
    frameworkId: 'cf-002',
    name: 'GDPR Readiness Assessment',
    description: 'Internal assessment of GDPR compliance status',
    assessor: 'Privacy Team with Legal Counsel',
    startDate: '2023-02-15',
    endDate: '2023-03-10',
    status: 'completed',
    scope: 'All GDPR requirements applicable to our operations',
    findings: [
      {
        id: 'f-003',
        requirementId: 'cr-003',
        status: 'compliant',
        notes: 'Privacy policy and data processing documentation in place',
        remediationPlan: '',
        remediationDueDate: ''
      },
      {
        id: 'f-004',
        requirementId: 'cr-004',
        status: 'non-compliant',
        notes: 'No automated process for handling right to erasure requests',
        remediationPlan: 'Implement data deletion workflow in customer portal',
        remediationDueDate: '2023-07-31'
      }
    ],
    createdAt: '2023-02-10T09:15:00Z',
    updatedAt: '2023-03-11T16:45:00Z'
  },
  {
    id: 'ca-003',
    frameworkId: 'cf-003',
    name: 'HIPAA Security Rule Assessment',
    description: 'Assessment of compliance with HIPAA Security Rule requirements',
    assessor: 'Healthcare Compliance Consultants Inc.',
    startDate: '2023-05-01',
    endDate: '',
    status: 'in-progress',
    scope: 'All HIPAA Security Rule safeguards',
    findings: [],
    createdAt: '2023-04-20T11:30:00Z',
    updatedAt: '2023-04-20T11:30:00Z'
  }
];

// Sample compliance evidence
const complianceEvidence = [
  {
    id: 'ce-001',
    controlId: 'cc-001',
    name: 'Access Control Policy Document',
    description: 'Current version of the Access Control Policy',
    type: 'document',
    location: '/policies/access-control-policy-v2.1.pdf',
    collectedBy: 'Compliance Manager',
    collectedAt: '2023-03-10',
    expiresAt: '2024-03-10',
    status: 'valid',
    createdAt: '2023-03-10T14:20:00Z',
    updatedAt: '2023-03-10T14:20:00Z'
  },
  {
    id: 'ce-002',
    controlId: 'cc-002',
    name: 'MFA Configuration Screenshots',
    description: 'Screenshots showing MFA settings in Azure AD',
    type: 'screenshot',
    location: '/evidence/mfa-config-screenshots.zip',
    collectedBy: 'IT Administrator',
    collectedAt: '2023-03-15',
    expiresAt: '2023-09-15',
    status: 'valid',
    createdAt: '2023-03-15T10:45:00Z',
    updatedAt: '2023-03-15T10:45:00Z'
  },
  {
    id: 'ce-003',
    controlId: 'cc-003',
    name: 'Data Retention Policy',
    description: 'Current version of the Data Retention and Deletion Policy',
    type: 'document',
    location: '/policies/data-retention-policy-v1.5.pdf',
    collectedBy: 'Data Protection Officer',
    collectedAt: '2023-02-20',
    expiresAt: '2024-02-20',
    status: 'valid',
    createdAt: '2023-02-20T09:30:00Z',
    updatedAt: '2023-02-20T09:30:00Z'
  },
  {
    id: 'ce-004',
    controlId: 'cc-004',
    name: 'Incident Response Tabletop Exercise Report',
    description: 'Report documenting the most recent incident response tabletop exercise',
    type: 'report',
    location: '/evidence/ir-tabletop-exercise-2023-04.pdf',
    collectedBy: 'Security Team Lead',
    collectedAt: '2023-04-05',
    expiresAt: '2023-10-05',
    status: 'valid',
    createdAt: '2023-04-06T15:10:00Z',
    updatedAt: '2023-04-06T15:10:00Z'
  }
];

/**
 * @swagger
 * components:
 *   schemas:
 *     ComplianceAssessment:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: Unique identifier for the compliance assessment
 *         name:
 *           type: string
 *           description: Name of the assessment
 *         description:
 *           type: string
 *           description: Description of the assessment
 *         frameworkId:
 *           type: string
 *           description: ID of the associated compliance framework
 *         status:
 *           type: string
 *           enum: [planned, in-progress, completed, cancelled]
 *           description: Status of the assessment
 *         startDate:
 *           type: string
 *           format: date
 *           description: Date when the assessment started
 *         endDate:
 *           type: string
 *           format: date
 *           description: Date when the assessment ended
 *         assessor:
 *           type: string
 *           description: Person or team conducting the assessment
 *         assessorType:
 *           type: string
 *           enum: [internal, external, third-party]
 *           description: Type of assessor
 *         overallScore:
 *           type: number
 *           minimum: 0
 *           maximum: 100
 *           description: Overall compliance score as a percentage
 *         findings:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               id:
 *                 type: string
 *               requirementId:
 *                 type: string
 *               description:
 *                 type: string
 *               severity:
 *                 type: string
 *                 enum: [critical, high, medium, low]
 *               status:
 *                 type: string
 *                 enum: [open, in-remediation, closed]
 *           description: List of findings from the assessment
 *         recommendations:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               id:
 *                 type: string
 *               description:
 *                 type: string
 *               priority:
 *                 type: string
 *                 enum: [critical, high, medium, low]
 *               status:
 *                 type: string
 *                 enum: [planned, in-progress, completed, rejected]
 *           description: List of recommendations from the assessment
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the assessment was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the assessment was last updated
 *       required:
 *         - id
 *         - name
 *         - frameworkId
 *         - status
 *         - createdAt
 *         - updatedAt
 *
 *     ComplianceEvidence:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: Unique identifier for the compliance evidence
 *         controlId:
 *           type: string
 *           description: ID of the associated compliance control
 *         name:
 *           type: string
 *           description: Name of the evidence
 *         description:
 *           type: string
 *           description: Description of the evidence
 *         type:
 *           type: string
 *           enum: [document, screenshot, log, report, certification, attestation, other]
 *           description: Type of evidence
 *         location:
 *           type: string
 *           description: Storage location or URL of the evidence
 *         collectionMethod:
 *           type: string
 *           enum: [manual, automated, hybrid]
 *           description: Method used to collect the evidence
 *         collectedBy:
 *           type: string
 *           description: Person or system that collected the evidence
 *         collectedAt:
 *           type: string
 *           format: date
 *           description: Date when the evidence was collected
 *         expiresAt:
 *           type: string
 *           format: date
 *           description: Date when the evidence expires
 *         status:
 *           type: string
 *           enum: [pending, valid, expired, rejected]
 *           description: Status of the evidence
 *         approvedBy:
 *           type: string
 *           description: Person who approved the evidence
 *         approvedAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the evidence was approved
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the evidence was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the evidence was last updated
 *       required:
 *         - id
 *         - controlId
 *         - name
 *         - type
 *         - collectedAt
 *         - status
 *         - createdAt
 *         - updatedAt
 *
 *     ComplianceAutomationRule:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: Unique identifier for the automation rule
 *         name:
 *           type: string
 *           description: Name of the rule
 *         description:
 *           type: string
 *           description: Description of the rule
 *         requirementId:
 *           type: string
 *           description: ID of the associated compliance requirement
 *         triggerType:
 *           type: string
 *           enum: [scheduled, event-based, manual]
 *           description: Type of trigger for the rule
 *         triggerCondition:
 *           type: string
 *           description: Condition that triggers the rule
 *         actions:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               type:
 *                 type: string
 *                 enum: [collect-evidence, notify, update-status, create-task]
 *               parameters:
 *                 type: object
 *           description: Actions to perform when the rule is triggered
 *         schedule:
 *           type: string
 *           description: Schedule for the rule (cron expression or description)
 *         frequency:
 *           type: string
 *           enum: [daily, weekly, monthly, quarterly, annually, custom]
 *           description: Frequency of rule execution
 *         status:
 *           type: string
 *           enum: [active, inactive, draft]
 *           description: Status of the rule
 *         lastExecutionDate:
 *           type: string
 *           format: date-time
 *           description: Date and time when the rule was last executed
 *         nextExecutionDate:
 *           type: string
 *           format: date-time
 *           description: Date and time when the rule will be executed next
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the rule was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the rule was last updated
 *       required:
 *         - id
 *         - name
 *         - requirementId
 *         - triggerType
 *         - actions
 *         - status
 *         - createdAt
 *         - updatedAt
 */

// Framework categories for reference
const frameworkCategories = [
  {
    id: 'security',
    name: 'Security',
    description: 'Frameworks focused on information security controls and practices'
  },
  {
    id: 'privacy',
    name: 'Privacy',
    description: 'Frameworks focused on data privacy and protection'
  },
  {
    id: 'healthcare',
    name: 'Healthcare',
    description: 'Frameworks specific to the healthcare industry'
  },
  {
    id: 'financial',
    name: 'Financial',
    description: 'Frameworks specific to the financial services industry'
  },
  {
    id: 'general',
    name: 'General Compliance',
    description: 'General-purpose compliance frameworks'
  }
];

// Control types for reference
const controlTypes = [
  {
    id: 'preventive',
    name: 'Preventive',
    description: 'Controls designed to prevent security incidents or compliance violations'
  },
  {
    id: 'detective',
    name: 'Detective',
    description: 'Controls designed to detect security incidents or compliance violations'
  },
  {
    id: 'corrective',
    name: 'Corrective',
    description: 'Controls designed to correct or mitigate the impact of security incidents or compliance violations'
  },
  {
    id: 'administrative',
    name: 'Administrative',
    description: 'Controls involving policies, procedures, and guidelines'
  },
  {
    id: 'technical',
    name: 'Technical',
    description: 'Controls implemented through technology solutions'
  },
  {
    id: 'physical',
    name: 'Physical',
    description: 'Controls involving physical security measures'
  }
];

module.exports = {
  complianceFrameworks,
  complianceRequirements,
  complianceControls,
  complianceAssessments,
  complianceEvidence,
  complianceAutomationRules,
  frameworkCategories,
  controlTypes
};
