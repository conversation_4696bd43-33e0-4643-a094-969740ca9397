@echo off
REM NovaConnect UAC Tenant Deprovisioning Script for Windows

REM Set variables
set TENANT_ID=%1
set BACKUP=%2
if "%BACKUP%"=="" set BACKUP=true
if "%PROJECT_ID%"=="" set PROJECT_ID=novafuse-marketplace

REM Colors for output
set RED=[91m
set GREEN=[92m
set YELLOW=[93m
set NC=[0m

REM Check if tenant ID is provided
if "%TENANT_ID%"=="" (
  echo %RED%Error: Tenant ID is required.%NC%
  echo Usage: %0 ^<tenant_id^> [backup=true^|false]
  exit /b 1
)

REM Confirm deprovisioning
set /p CONFIRM=Are you sure you want to deprovision tenant %TENANT_ID%? This action cannot be undone. (y/n): 
if /i not "%CONFIRM%"=="y" (
  echo %YELLOW%Deprovisioning cancelled.%NC%
  exit /b 0
)

REM Backup tenant data if requested
if /i "%BACKUP%"=="true" (
  echo %YELLOW%Backing up tenant data...%NC%
  
  REM Create backup directory
  set BACKUP_DIR=backups\tenant-%TENANT_ID%-%date:~10,4%%date:~4,2%%date:~7,2%%time:~0,2%%time:~3,2%%time:~6,2%
  set BACKUP_DIR=%BACKUP_DIR: =%
  mkdir %BACKUP_DIR%
  
  REM Export Kubernetes resources
  kubectl get all -n tenant-%TENANT_ID% -o yaml > %BACKUP_DIR%\kubernetes-resources.yaml
  
  REM Export BigQuery data
  bq extract --destination_format=NEWLINE_DELIMITED_JSON %PROJECT_ID%:tenant_%TENANT_ID%.* gs://%PROJECT_ID%-backups/tenant-%TENANT_ID%/%date:~10,4%%date:~4,2%%date:~7,2%/
  
  echo %GREEN%Tenant data backed up to %BACKUP_DIR% and GCS.%NC%
)

REM Delete Helm release
echo %YELLOW%Deleting Helm release for tenant %TENANT_ID%...%NC%
helm uninstall tenant-%TENANT_ID% --namespace tenant-%TENANT_ID%

REM Delete namespace
echo %YELLOW%Deleting namespace for tenant %TENANT_ID%...%NC%
kubectl delete namespace tenant-%TENANT_ID%

REM Delete service account
echo %YELLOW%Deleting service account for tenant %TENANT_ID%...%NC%
gcloud iam service-accounts delete tenant-%TENANT_ID%@%PROJECT_ID%.iam.gserviceaccount.com --quiet

REM Delete encryption key
echo %YELLOW%Deleting encryption key for tenant %TENANT_ID%...%NC%
for /f "tokens=*" %%a in ('gcloud kms keys versions list --location=global --keyring=tenant-%TENANT_ID%-keyring --key=tenant-%TENANT_ID%-key --format="value(name)"') do (
  gcloud kms keys versions destroy %%a --location=global --keyring=tenant-%TENANT_ID%-keyring --key=tenant-%TENANT_ID%-key
)

gcloud kms keys delete tenant-%TENANT_ID%-key --location=global --keyring=tenant-%TENANT_ID%-keyring --quiet
gcloud kms keyrings delete tenant-%TENANT_ID%-keyring --location=global --quiet

REM Delete BigQuery dataset (with caution)
echo %YELLOW%Deleting BigQuery dataset for tenant %TENANT_ID%...%NC%
bq rm -f -r %PROJECT_ID%:tenant_%TENANT_ID%

REM Delete monitoring dashboard
echo %YELLOW%Deleting monitoring dashboard for tenant %TENANT_ID%...%NC%
del /q monitoring\dashboards\tenant-%TENANT_ID%-dashboard.json

REM Delete tenant-specific values file
echo %YELLOW%Deleting tenant-specific values file...%NC%
del /q marketplace\chart\values-tenant-%TENANT_ID%.yaml

echo %GREEN%Tenant %TENANT_ID% deprovisioned successfully!%NC%

exit /b 0
