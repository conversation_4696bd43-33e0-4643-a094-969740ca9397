/**
 * Distributed Processing Example
 *
 * This example demonstrates how to use the distributed processing components
 * for the Finite Universe Principle defense system, enabling distributed
 * processing across multiple nodes.
 */

const {
  // Complete defense system
  createCompleteDefenseSystem,

  // Distributed processing components
  createClusterManager,
  createLoadBalancer,
  createNodeDiscovery,
  createPriorityQueue,
  createCapabilityRouter,
  createDistributedProcessor,
  createDistributedComponents
} = require('../src/quantum/finite-universe-principle');

/**
 * Example 1: Basic Cluster Management
 *
 * This example demonstrates how to use the cluster manager.
 */
async function example1() {
  console.log('\n=== Example 1: Basic Cluster Management ===\n');

  // Create cluster manager
  const clusterManager = createClusterManager({
    enableLogging: true,
    isMaster: true,
    discoveryEnabled: true
  });

  // Register event listeners
  clusterManager.on('start', (data) => {
    console.log('Cluster manager started:', data);
  });

  clusterManager.on('node-registered', (data) => {
    console.log('Node registered:', data.nodeId);
  });

  // Start cluster manager
  clusterManager.start();

  // Register some mock nodes
  const mockNodes = [
    {
      id: 'node1',
      hostname: 'worker1',
      address: '*************',
      isMaster: false,
      cpus: 4,
      memory: **********, // 8GB
      freeMemory: 4294967296, // 4GB
      platform: 'win32',
      arch: 'x64',
      status: 'active',
      lastHeartbeat: Date.now(),
      capabilities: ['cyber', 'financial'],
      load: 0.2,
      tasks: 2
    },
    {
      id: 'node2',
      hostname: 'worker2',
      address: '*************',
      isMaster: false,
      cpus: 8,
      memory: 17179869184, // 16GB
      freeMemory: **********, // 8GB
      platform: 'win32',
      arch: 'x64',
      status: 'active',
      lastHeartbeat: Date.now(),
      capabilities: ['cyber', 'medical'],
      load: 0.1,
      tasks: 1
    }
  ];

  // Register mock nodes
  mockNodes.forEach(node => {
    clusterManager.registerNode(node);
  });

  // Get all nodes
  const nodes = clusterManager.getNodes();
  console.log('Registered nodes:', nodes.length);

  // Submit a task
  const taskResult = await clusterManager.submitTask({ data: 'test' }, 'cyber');
  console.log('Task result:', taskResult);

  // Dispose cluster manager
  clusterManager.dispose();
}

/**
 * Example 2: Load Balancing
 *
 * This example demonstrates how to use the load balancer.
 */
async function example2() {
  console.log('\n=== Example 2: Load Balancing ===\n');

  // Create load balancer
  const loadBalancer = createLoadBalancer({
    enableLogging: true,
    strategy: 'round-robin'
  });

  // Register event listeners
  loadBalancer.on('start', () => {
    console.log('Load balancer started');
  });

  loadBalancer.on('node-registered', (data) => {
    console.log('Node registered in load balancer:', data.nodeId);
  });

  loadBalancer.on('node-selected', (data) => {
    console.log('Node selected for task:', data.nodeId);
  });

  // Start load balancer
  loadBalancer.start();

  // Register some mock nodes
  const mockNodes = [
    {
      id: 'node1',
      hostname: 'worker1',
      address: '*************',
      isMaster: false,
      cpus: 4,
      status: 'active'
    },
    {
      id: 'node2',
      hostname: 'worker2',
      address: '*************',
      isMaster: false,
      cpus: 8,
      status: 'active'
    },
    {
      id: 'node3',
      hostname: 'worker3',
      address: '*************',
      isMaster: false,
      cpus: 2,
      status: 'active'
    }
  ];

  // Register mock nodes with different weights
  loadBalancer.registerNode(mockNodes[0], 1);
  loadBalancer.registerNode(mockNodes[1], 2);
  loadBalancer.registerNode(mockNodes[2], 0.5);

  // Get next node for processing (round-robin)
  console.log('\nRound-robin strategy:');
  for (let i = 0; i < 6; i++) {
    const node = loadBalancer.getNextNode({ id: `task${i}` }, 'cyber');
    console.log(`Task ${i} assigned to node ${node.id}`);
    loadBalancer.releaseNode(node.id, true);
  }

  // Change strategy to weighted
  loadBalancer.options.strategy = 'weighted';
  console.log('\nWeighted strategy:');

  // Count node selections
  const nodeCounts = { node1: 0, node2: 0, node3: 0 };

  // Run 100 selections to see distribution
  for (let i = 0; i < 100; i++) {
    const node = loadBalancer.getNextNode({ id: `task${i}` }, 'cyber');
    nodeCounts[node.id]++;
    loadBalancer.releaseNode(node.id, true);
  }

  console.log('Node selection counts (weighted):', nodeCounts);

  // Change strategy to least-connections
  loadBalancer.options.strategy = 'least-connections';
  console.log('\nLeast-connections strategy:');

  // Simulate some connections
  loadBalancer.nodeConnections.set('node1', 5);
  loadBalancer.nodeConnections.set('node2', 2);
  loadBalancer.nodeConnections.set('node3', 10);

  // Get next node for processing (least-connections)
  for (let i = 0; i < 3; i++) {
    const node = loadBalancer.getNextNode({ id: `task${i}` }, 'cyber');
    console.log(`Task ${i} assigned to node ${node.id} (connections: ${loadBalancer.nodeConnections.get(node.id)})`);
    loadBalancer.releaseNode(node.id, true);
  }

  // Dispose load balancer
  loadBalancer.dispose();
}

/**
 * Example 3: Distributed Processing
 *
 * This example demonstrates how to use the distributed processor.
 */
async function example3() {
  console.log('\n=== Example 3: Distributed Processing ===\n');

  // Create distributed components
  const {
    clusterManager,
    loadBalancer,
    distributedProcessor
  } = createDistributedComponents({
    enableLogging: true,
    isMaster: true
  });

  // Register event listeners
  distributedProcessor.on('start', (data) => {
    console.log('Distributed processor started:', data);
  });

  distributedProcessor.on('task-submitted', (data) => {
    console.log('Task submitted:', data.taskId);
  });

  distributedProcessor.on('task-completed', (data) => {
    console.log('Task completed:', data.taskId);
  });

  // Start distributed processor
  distributedProcessor.start();

  // Register processor functions
  distributedProcessor.registerProcessor('cyber', async (data, domain) => {
    console.log(`Processing cyber data: ${JSON.stringify(data)}`);
    return {
      processed: true,
      data,
      domain,
      securityScore: data.securityScore * 1.1,
      threatLevel: data.threatLevel * 0.9
    };
  });

  distributedProcessor.registerProcessor('financial', async (data, domain) => {
    console.log(`Processing financial data: ${JSON.stringify(data)}`);
    return {
      processed: true,
      data,
      domain,
      balance: data.balance * 1.05,
      interestRate: data.interestRate * 0.95
    };
  });

  // Process data
  console.log('\nProcessing cyber data:');
  const cyberResult = await distributedProcessor.process(
    { securityScore: 8, threatLevel: 3 },
    'cyber',
    'cyber'
  );
  console.log('Cyber result:', cyberResult);

  console.log('\nProcessing financial data:');
  const financialResult = await distributedProcessor.process(
    { balance: 1000, interestRate: 0.05 },
    'financial',
    'financial'
  );
  console.log('Financial result:', financialResult);

  // Process multiple items
  console.log('\nProcessing multiple items:');
  const promises = [];
  for (let i = 0; i < 5; i++) {
    promises.push(distributedProcessor.process(
      { securityScore: 7 + i, threatLevel: 2 + (i * 0.5) },
      'cyber',
      'cyber'
    ));
  }

  const results = await Promise.all(promises);
  console.log('Multiple results count:', results.length);

  // Dispose distributed processor
  distributedProcessor.dispose();
}

/**
 * Example 4: Integration with Defense System
 *
 * This example demonstrates how to integrate distributed processing with the defense system.
 */
async function example4() {
  console.log('\n=== Example 4: Integration with Defense System ===\n');

  // Create complete defense system
  const defenseSystem = createCompleteDefenseSystem({
    enableLogging: false, // Disable logging for cleaner output
    enableMonitoring: true,
    strictMode: true
  });

  // Create distributed components
  const {
    distributedProcessor
  } = createDistributedComponents({
    enableLogging: true,
    isMaster: true
  });

  // Start distributed processor
  distributedProcessor.start();

  // Register processor function that uses the defense system
  distributedProcessor.registerProcessor('defense', async (data, domain) => {
    return await defenseSystem.processData(data, domain);
  });

  // Process data through the defense system
  console.log('\nProcessing data through the defense system:');
  const result = await distributedProcessor.process(
    {
      securityScore: 8,
      threatLevel: 3,
      encryptionStrength: 256
    },
    'defense',
    'cyber'
  );
  console.log('Result:', result);

  // Process data with boundary violations
  console.log('\nProcessing data with boundary violations:');
  try {
    const violationResult = await distributedProcessor.process(
      {
        securityScore: 8,
        threatLevel: 3,
        encryptionStrength: Infinity // Should be bounded
      },
      'defense',
      'cyber'
    );
    console.log('Violation result:', violationResult);
  } catch (error) {
    console.error('Error processing data with boundary violations:', error.message);
  }

  // Dispose resources
  distributedProcessor.dispose();
  defenseSystem.dispose();
}

/**
 * Example 5: Dynamic Node Discovery
 *
 * This example demonstrates how to use the node discovery component.
 */
async function example5() {
  console.log('\n=== Example 5: Dynamic Node Discovery ===\n');

  // Create node discovery for master node
  const masterNodeDiscovery = createNodeDiscovery({
    enableLogging: true,
    isMaster: true,
    nodeId: 'master-node',
    capabilities: ['default', 'master', 'routing']
  });

  // Create node discovery for worker node
  const workerNodeDiscovery = createNodeDiscovery({
    enableLogging: true,
    isMaster: false,
    nodeId: 'worker-node',
    capabilities: ['default', 'worker', 'processing']
  });

  // Register event listeners
  masterNodeDiscovery.on('node-discovered', (data) => {
    console.log('Master discovered node:', data.nodeId);
  });

  workerNodeDiscovery.on('node-discovered', (data) => {
    console.log('Worker discovered node:', data.nodeId);
  });

  // Start node discovery
  masterNodeDiscovery.start();
  workerNodeDiscovery.start();

  // Wait for nodes to discover each other
  await new Promise(resolve => setTimeout(resolve, 2000));

  // Get discovered nodes
  console.log('\nMaster discovered nodes:');
  console.log(masterNodeDiscovery.getNodes().map(node => ({
    id: node.id,
    isMaster: node.isMaster,
    capabilities: node.capabilities
  })));

  console.log('\nWorker discovered nodes:');
  console.log(workerNodeDiscovery.getNodes().map(node => ({
    id: node.id,
    isMaster: node.isMaster,
    capabilities: node.capabilities
  })));

  // Stop node discovery
  masterNodeDiscovery.stop();
  workerNodeDiscovery.stop();

  // Dispose resources
  masterNodeDiscovery.dispose();
  workerNodeDiscovery.dispose();
}

/**
 * Example 6: Task Prioritization
 *
 * This example demonstrates how to use the priority queue component.
 */
async function example6() {
  console.log('\n=== Example 6: Task Prioritization ===\n');

  // Create priority queue
  const priorityQueue = createPriorityQueue({
    enableLogging: true,
    priorityLevels: 10,
    defaultPriority: 5,
    preemptionEnabled: true,
    fairnessEnabled: true
  });

  // Register event listeners
  priorityQueue.on('enqueue', (data) => {
    console.log(`Task ${data.taskId} enqueued with priority ${data.priority}`);
  });

  priorityQueue.on('dequeue', (data) => {
    console.log(`Task ${data.taskId} dequeued with priority ${data.priority}`);
  });

  priorityQueue.on('complete', (data) => {
    console.log(`Task ${data.taskId} completed with result:`, data.result);
  });

  priorityQueue.on('preempt', (data) => {
    console.log(`Task ${data.taskId} preempted`);
  });

  // Enqueue tasks with different priorities
  console.log('\nEnqueuing tasks:');

  const task1 = { data: 'Task 1 data' };
  const task2 = { data: 'Task 2 data' };
  const task3 = { data: 'Task 3 data' };
  const task4 = { data: 'Task 4 data' };
  const task5 = { data: 'Task 5 data' };

  const taskId1 = priorityQueue.enqueue(task1, 5); // Medium priority
  const taskId2 = priorityQueue.enqueue(task2, 8); // Low priority
  const taskId3 = priorityQueue.enqueue(task3, 2); // High priority
  const taskId4 = priorityQueue.enqueue(task4, 0); // Highest priority
  const taskId5 = priorityQueue.enqueue(task5, 9); // Lowest priority

  // Dequeue tasks
  console.log('\nDequeuing tasks:');

  const dequeuedTask1 = priorityQueue.dequeue();
  console.log('Dequeued task 1:', dequeuedTask1);
  priorityQueue.complete(dequeuedTask1.id, 'Result 1');

  const dequeuedTask2 = priorityQueue.dequeue();
  console.log('Dequeued task 2:', dequeuedTask2);
  priorityQueue.complete(dequeuedTask2.id, 'Result 2');

  const dequeuedTask3 = priorityQueue.dequeue();
  console.log('Dequeued task 3:', dequeuedTask3);
  priorityQueue.complete(dequeuedTask3.id, 'Result 3');

  const dequeuedTask4 = priorityQueue.dequeue();
  console.log('Dequeued task 4:', dequeuedTask4);
  priorityQueue.complete(dequeuedTask4.id, 'Result 4');

  const dequeuedTask5 = priorityQueue.dequeue();
  console.log('Dequeued task 5:', dequeuedTask5);
  priorityQueue.complete(dequeuedTask5.id, 'Result 5');

  // Get queue statistics
  console.log('\nQueue statistics:', priorityQueue.getStats());

  // Dispose resources
  priorityQueue.dispose();
}

/**
 * Example 7: Capability-Based Routing
 *
 * This example demonstrates how to use the capability router component.
 */
async function example7() {
  console.log('\n=== Example 7: Capability-Based Routing ===\n');

  // Create capability router
  const capabilityRouter = createCapabilityRouter({
    enableLogging: true,
    defaultCapability: 'default',
    loadBalancingStrategy: 'least-loaded',
    capabilityMatchingStrategy: 'subset'
  });

  // Register nodes with different capabilities
  console.log('\nRegistering nodes:');

  const node1 = {
    id: 'node1',
    capabilities: ['default', 'compute', 'storage'],
    load: 2
  };

  const node2 = {
    id: 'node2',
    capabilities: ['default', 'compute', 'network'],
    load: 1
  };

  const node3 = {
    id: 'node3',
    capabilities: ['default', 'storage', 'network'],
    load: 0
  };

  const node4 = {
    id: 'node4',
    capabilities: ['default', 'compute', 'storage', 'network'],
    load: 3
  };

  capabilityRouter.registerNode(node1);
  capabilityRouter.registerNode(node2);
  capabilityRouter.registerNode(node3);
  capabilityRouter.registerNode(node4);

  // Route tasks with different capability requirements
  console.log('\nRouting tasks:');

  const task1 = { id: 'task1', data: 'Task 1 data' };
  const task2 = { id: 'task2', data: 'Task 2 data' };
  const task3 = { id: 'task3', data: 'Task 3 data' };
  const task4 = { id: 'task4', data: 'Task 4 data' };

  const routedNode1 = capabilityRouter.routeTask(task1, ['compute']);
  console.log('Task 1 routed to node:', routedNode1?.id);

  const routedNode2 = capabilityRouter.routeTask(task2, ['storage']);
  console.log('Task 2 routed to node:', routedNode2?.id);

  const routedNode3 = capabilityRouter.routeTask(task3, ['compute', 'network']);
  console.log('Task 3 routed to node:', routedNode3?.id);

  const routedNode4 = capabilityRouter.routeTask(task4, ['compute', 'storage', 'network']);
  console.log('Task 4 routed to node:', routedNode4?.id);

  // Get all nodes
  console.log('\nAll nodes:', capabilityRouter.getNodes());

  // Get all capabilities
  console.log('\nAll capabilities:', capabilityRouter.getCapabilities());

  // Get nodes with specific capability
  console.log('\nNodes with compute capability:', capabilityRouter.getNodesWithCapability('compute'));
  console.log('Nodes with storage capability:', capabilityRouter.getNodesWithCapability('storage'));
  console.log('Nodes with network capability:', capabilityRouter.getNodesWithCapability('network'));

  // Dispose resources
  capabilityRouter.dispose();
}

/**
 * Example 8: Enhanced Distributed Processing
 *
 * This example demonstrates how to use the enhanced distributed processing components.
 */
async function example8() {
  console.log('\n=== Example 8: Enhanced Distributed Processing ===\n');

  // Create distributed components with enhanced capabilities
  const components = createDistributedComponents({
    enableLogging: true,
    isMaster: true,
    nodeId: 'master-node',
    capabilities: ['default', 'master', 'routing', 'compute', 'storage']
  });

  // Start distributed processor
  components.distributedProcessor.start();

  // Register processor functions
  components.distributedProcessor.registerProcessor('compute', async (data) => {
    console.log('Computing:', data);
    return { result: data * 2 };
  });

  components.distributedProcessor.registerProcessor('storage', async (data) => {
    console.log('Storing:', data);
    return { stored: true, data };
  });

  // Process data with different priorities and capabilities
  console.log('\nProcessing data with priorities and capabilities:');

  const result1 = await components.distributedProcessor.process(
    5, 'compute', 'math', 2, ['compute']
  );
  console.log('Result 1 (high priority, compute capability):', result1);

  const result2 = await components.distributedProcessor.process(
    { key: 'value' }, 'storage', 'data', 5, ['storage']
  );
  console.log('Result 2 (medium priority, storage capability):', result2);

  const result3 = await components.distributedProcessor.process(
    { complex: 'data' }, 'compute', 'math', 8, ['compute', 'storage']
  );
  console.log('Result 3 (low priority, compute+storage capabilities):', result3);

  // Get all tasks
  console.log('\nAll tasks:', components.distributedProcessor.getTasks());

  // Stop distributed processor
  components.distributedProcessor.stop();

  // Dispose resources
  components.distributedProcessor.dispose();
}

/**
 * Run all examples
 */
async function runAllExamples() {
  await example1();
  await example2();
  await example3();
  await example4();
  await example5();
  await example6();
  await example7();
  await example8();
}

// Run all examples
runAllExamples().catch(error => {
  console.error('Error running examples:', error);
});
