/**
 * Cloud Storage Simulator
 * 
 * This file simulates the Google Cloud Storage service for the NovaFuse GCP simulation.
 * It provides endpoints for managing buckets, objects, and access controls.
 */

const express = require('express');
const cors = require('cors');
const morgan = require('morgan');
const { evidenceBinder } = require('../models');

// Create Express app
const app = express();

// Middleware
app.use(cors());
app.use(express.json());
app.use(morgan('dev'));

// In-memory storage for buckets and objects
const storage = {
  buckets: {
    'novafuse-evidence-store': {
      name: 'novafuse-evidence-store',
      location: 'us-central1',
      storageClass: 'STANDARD',
      created: new Date().toISOString(),
      updated: new Date().toISOString(),
      lifecycle: {
        rule: [
          {
            action: { type: 'Delete' },
            condition: { age: 365 * 7 } // 7 years
          }
        ]
      },
      encryption: {
        defaultKmsKeyName: 'projects/novafuse/locations/global/keyRings/compliance/cryptoKeys/evidence'
      },
      iamConfiguration: {
        uniformBucketLevelAccess: { enabled: true }
      },
      acl: {
        owner: 'project-owners-123456789',
        entries: [
          { entity: 'project-owners-123456789', role: 'OWNER' },
          { entity: 'group:<EMAIL>', role: 'READER' },
          { entity: 'group:<EMAIL>', role: 'READER' }
        ]
      },
      labels: {
        purpose: 'compliance-evidence',
        environment: 'production',
        dataClassification: 'confidential'
      }
    }
  },
  objects: {
    'novafuse-evidence-store': {
      'evidence/iam-policy/2023-06-15/evidence-001.json': {
        name: 'evidence/iam-policy/2023-06-15/evidence-001.json',
        contentType: 'application/json',
        size: 1024,
        created: '2023-06-15T14:22:31Z',
        updated: '2023-06-15T14:22:31Z',
        md5Hash: 'a1b2c3d4e5f6g7h8i9j0',
        metadata: {
          evidenceType: 'IAM_POLICY',
          evidenceId: 'evidence-001',
          framework: 'GDPR',
          control: 'gdpr-art5-1f',
          retention: '7y'
        },
        content: JSON.stringify({
          bindings: [
            { role: 'roles/viewer', members: ['group:<EMAIL>'] },
            { role: 'roles/editor', members: ['group:<EMAIL>'] },
            { role: 'roles/owner', members: ['user:<EMAIL>'] }
          ],
          etag: 'BwWKmjvelug=',
          version: 1
        })
      },
      'evidence/audit-log/2023-06-14/evidence-002.json': {
        name: 'evidence/audit-log/2023-06-14/evidence-002.json',
        contentType: 'application/json',
        size: 2048,
        created: '2023-06-14T09:17:45Z',
        updated: '2023-06-14T09:17:45Z',
        md5Hash: 'b2c3d4e5f6g7h8i9j0k1',
        metadata: {
          evidenceType: 'AUDIT_LOG',
          evidenceId: 'evidence-002',
          framework: 'HIPAA',
          control: 'hipaa-164-312b',
          retention: '3y'
        },
        content: JSON.stringify({
          logEntries: [
            {
              timestamp: '2023-06-14T09:15:22Z',
              principalEmail: '<EMAIL>',
              methodName: 'google.cloud.bigquery.v2.JobService.Query',
              resourceName: 'projects/project-123/datasets/customer_data/tables/transactions',
              status: 'SUCCESS'
            },
            {
              timestamp: '2023-06-14T09:16:45Z',
              principalEmail: '<EMAIL>',
              methodName: 'google.cloud.bigquery.v2.JobService.GetQueryResults',
              resourceName: 'projects/project-123/datasets/customer_data/tables/transactions',
              status: 'SUCCESS'
            }
          ]
        })
      }
    }
  }
};

/**
 * @route GET /storage/buckets
 * @description List all buckets
 * @access Private
 */
app.get('/storage/buckets', (req, res) => {
  const buckets = Object.values(storage.buckets).map(bucket => ({
    name: bucket.name,
    location: bucket.location,
    storageClass: bucket.storageClass,
    created: bucket.created,
    updated: bucket.updated
  }));
  
  res.json({
    kind: 'storage#buckets',
    items: buckets
  });
});

/**
 * @route GET /storage/buckets/:bucketName
 * @description Get a specific bucket
 * @access Private
 */
app.get('/storage/buckets/:bucketName', (req, res) => {
  const { bucketName } = req.params;
  
  if (!storage.buckets[bucketName]) {
    return res.status(404).json({
      error: {
        code: 404,
        message: `Bucket ${bucketName} not found`
      }
    });
  }
  
  res.json({
    kind: 'storage#bucket',
    ...storage.buckets[bucketName]
  });
});

/**
 * @route POST /storage/buckets
 * @description Create a new bucket
 * @access Private
 */
app.post('/storage/buckets', (req, res) => {
  const { name, location, storageClass, lifecycle, encryption, labels } = req.body;
  
  if (!name) {
    return res.status(400).json({
      error: {
        code: 400,
        message: 'Bucket name is required'
      }
    });
  }
  
  if (storage.buckets[name]) {
    return res.status(409).json({
      error: {
        code: 409,
        message: `Bucket ${name} already exists`
      }
    });
  }
  
  const now = new Date().toISOString();
  
  storage.buckets[name] = {
    name,
    location: location || 'us-central1',
    storageClass: storageClass || 'STANDARD',
    created: now,
    updated: now,
    lifecycle: lifecycle || { rule: [] },
    encryption: encryption || {},
    iamConfiguration: {
      uniformBucketLevelAccess: { enabled: true }
    },
    acl: {
      owner: 'project-owners-123456789',
      entries: [
        { entity: 'project-owners-123456789', role: 'OWNER' }
      ]
    },
    labels: labels || {}
  };
  
  storage.objects[name] = {};
  
  res.status(201).json({
    kind: 'storage#bucket',
    ...storage.buckets[name]
  });
});

/**
 * @route GET /storage/buckets/:bucketName/objects
 * @description List objects in a bucket
 * @access Private
 */
app.get('/storage/buckets/:bucketName/objects', (req, res) => {
  const { bucketName } = req.params;
  const { prefix } = req.query;
  
  if (!storage.buckets[bucketName]) {
    return res.status(404).json({
      error: {
        code: 404,
        message: `Bucket ${bucketName} not found`
      }
    });
  }
  
  let objects = Object.values(storage.objects[bucketName] || {});
  
  if (prefix) {
    objects = objects.filter(obj => obj.name.startsWith(prefix));
  }
  
  const items = objects.map(obj => ({
    name: obj.name,
    contentType: obj.contentType,
    size: obj.size,
    created: obj.created,
    updated: obj.updated,
    md5Hash: obj.md5Hash,
    metadata: obj.metadata
  }));
  
  res.json({
    kind: 'storage#objects',
    items
  });
});

/**
 * @route GET /storage/buckets/:bucketName/objects/:objectName
 * @description Get a specific object
 * @access Private
 */
app.get('/storage/buckets/:bucketName/objects/:objectName(*)', (req, res) => {
  const { bucketName, objectName } = req.params;
  
  if (!storage.buckets[bucketName]) {
    return res.status(404).json({
      error: {
        code: 404,
        message: `Bucket ${bucketName} not found`
      }
    });
  }
  
  if (!storage.objects[bucketName] || !storage.objects[bucketName][objectName]) {
    return res.status(404).json({
      error: {
        code: 404,
        message: `Object ${objectName} not found in bucket ${bucketName}`
      }
    });
  }
  
  const object = storage.objects[bucketName][objectName];
  
  // Check if the client wants the content or just the metadata
  const { alt } = req.query;
  
  if (alt === 'media') {
    // Return the object content
    if (object.contentType === 'application/json') {
      return res.json(JSON.parse(object.content));
    }
    
    return res.send(object.content);
  }
  
  // Return the object metadata
  res.json({
    kind: 'storage#object',
    bucket: bucketName,
    ...object,
    // Don't include the content in the metadata response
    content: undefined
  });
});

/**
 * @route POST /storage/buckets/:bucketName/objects
 * @description Upload a new object
 * @access Private
 */
app.post('/storage/buckets/:bucketName/objects', (req, res) => {
  const { bucketName } = req.params;
  const { name, contentType, content, metadata } = req.body;
  
  if (!storage.buckets[bucketName]) {
    return res.status(404).json({
      error: {
        code: 404,
        message: `Bucket ${bucketName} not found`
      }
    });
  }
  
  if (!name || !content) {
    return res.status(400).json({
      error: {
        code: 400,
        message: 'Object name and content are required'
      }
    });
  }
  
  if (!storage.objects[bucketName]) {
    storage.objects[bucketName] = {};
  }
  
  const now = new Date().toISOString();
  const md5Hash = Math.random().toString(36).substring(2, 15);
  
  storage.objects[bucketName][name] = {
    name,
    contentType: contentType || 'application/octet-stream',
    size: content.length,
    created: now,
    updated: now,
    md5Hash,
    metadata: metadata || {},
    content
  };
  
  res.status(201).json({
    kind: 'storage#object',
    bucket: bucketName,
    ...storage.objects[bucketName][name],
    // Don't include the content in the response
    content: undefined
  });
});

/**
 * @route DELETE /storage/buckets/:bucketName/objects/:objectName
 * @description Delete an object
 * @access Private
 */
app.delete('/storage/buckets/:bucketName/objects/:objectName(*)', (req, res) => {
  const { bucketName, objectName } = req.params;
  
  if (!storage.buckets[bucketName]) {
    return res.status(404).json({
      error: {
        code: 404,
        message: `Bucket ${bucketName} not found`
      }
    });
  }
  
  if (!storage.objects[bucketName] || !storage.objects[bucketName][objectName]) {
    return res.status(404).json({
      error: {
        code: 404,
        message: `Object ${objectName} not found in bucket ${bucketName}`
      }
    });
  }
  
  delete storage.objects[bucketName][objectName];
  
  res.status(204).end();
});

/**
 * @route GET /storage/evidence-binder
 * @description Get evidence binder configuration
 * @access Private
 */
app.get('/storage/evidence-binder', (req, res) => {
  res.json({
    kind: 'storage#evidenceBinder',
    ...evidenceBinder
  });
});

/**
 * @route POST /storage/evidence-binder/evidence
 * @description Store new evidence in the evidence binder
 * @access Private
 */
app.post('/storage/evidence-binder/evidence', (req, res) => {
  const { type, content, metadata } = req.body;
  
  if (!type || !content) {
    return res.status(400).json({
      error: {
        code: 400,
        message: 'Evidence type and content are required'
      }
    });
  }
  
  // Find the evidence type configuration
  const evidenceType = evidenceBinder.evidenceTypes.find(et => et.type === type);
  
  if (!evidenceType) {
    return res.status(400).json({
      error: {
        code: 400,
        message: `Evidence type ${type} is not supported`
      }
    });
  }
  
  // Generate a unique ID for the evidence
  const evidenceId = `evidence-${Date.now()}`;
  
  // Generate the object name
  const now = new Date();
  const dateStr = now.toISOString().split('T')[0];
  const objectName = `evidence/${type.toLowerCase()}/${dateStr}/${evidenceId}.json`;
  
  // Create the evidence object
  const evidenceObject = {
    name: objectName,
    contentType: 'application/json',
    size: JSON.stringify(content).length,
    created: now.toISOString(),
    updated: now.toISOString(),
    md5Hash: Math.random().toString(36).substring(2, 15),
    metadata: {
      evidenceType: type,
      evidenceId,
      ...metadata,
      retention: evidenceType.retention
    },
    content: JSON.stringify(content)
  };
  
  // Store the evidence object
  if (!storage.objects[evidenceBinder.bucketName]) {
    storage.objects[evidenceBinder.bucketName] = {};
  }
  
  storage.objects[evidenceBinder.bucketName][objectName] = evidenceObject;
  
  // Return the evidence metadata
  res.status(201).json({
    kind: 'storage#evidenceObject',
    bucket: evidenceBinder.bucketName,
    ...evidenceObject,
    // Don't include the content in the response
    content: undefined
  });
});

// Start server
const PORT = process.env.CLOUD_STORAGE_PORT || 8084;
app.listen(PORT, () => {
  console.log(`Cloud Storage simulator running on port ${PORT}`);
});

module.exports = app;
