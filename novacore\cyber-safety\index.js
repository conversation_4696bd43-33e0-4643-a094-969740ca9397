/**
 * NovaCore Cyber-Safety Platform
 *
 * This file exports the Cyber-Safety platform components.
 */

const safetyMiddleware = require('./middleware/safetyMiddleware');
const { safetyService, visualizationService } = require('./services');
const { SafetyScore, SafetyPolicy } = require('./models');
const safetyUtils = require('./utils/safetyUtils');

module.exports = {
  middleware: {
    safetyMiddleware
  },
  services: {
    safetyService,
    visualizationService
  },
  models: {
    SafetyScore,
    SafetyPolicy
  },
  utils: safetyUtils
};
