apiVersion: v1
kind: Namespace
metadata:
  name: novaconnect
  labels:
    name: novaconnect
    part-of: novafuse
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: novaconnect-config
  namespace: novaconnect
data:
  NODE_ENV: "production"
  LOG_LEVEL: "info"
  PORT: "3000"
  METRICS_ENABLED: "true"
  CACHE_ENABLED: "true"
  MAX_CONCURRENT_REMEDIATIONS: "20"
  DEFAULT_TIMEOUT: "30000"
  RETRY_COUNT: "3"
  RETRY_DELAY: "1000"
  ENCRYPTION_ALGORITHM: "aes-256-gcm"
  ENCRYPTION_KEY_ROTATION_DAYS: "30"
---
apiVersion: v1
kind: Secret
metadata:
  name: novaconnect-secrets
  namespace: novaconnect
type: Opaque
data:
  # These values should be base64 encoded in a real deployment
  JWT_SECRET: "c2VjcmV0LWtleS1mb3Itbm92YWNvbm5lY3Q="
  ENCRYPTION_MASTER_KEY: "bWFzdGVyLWtleS1mb3ItZW5jcnlwdGlvbi1zZXJ2aWNl"
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: novaconnect-api
  namespace: novaconnect
  labels:
    app: novaconnect-api
    part-of: novafuse
spec:
  replicas: 3
  selector:
    matchLabels:
      app: novaconnect-api
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        app: novaconnect-api
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "3000"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: novaconnect-sa
      containers:
      - name: novaconnect-api
        image: gcr.io/novafuse/novaconnect-api:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 3000
          name: http
        resources:
          requests:
            cpu: "500m"
            memory: "512Mi"
          limits:
            cpu: "2"
            memory: "2Gi"
        envFrom:
        - configMapRef:
            name: novaconnect-config
        - secretRef:
            name: novaconnect-secrets
        env:
        - name: POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health/ready
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        volumeMounts:
        - name: tmp-volume
          mountPath: /tmp
        - name: logs-volume
          mountPath: /app/logs
      volumes:
      - name: tmp-volume
        emptyDir: {}
      - name: logs-volume
        emptyDir: {}
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        fsGroup: 1000
---
apiVersion: v1
kind: Service
metadata:
  name: novaconnect-api
  namespace: novaconnect
  labels:
    app: novaconnect-api
    part-of: novafuse
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: 3000
    protocol: TCP
    name: http
  selector:
    app: novaconnect-api
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: novaconnect-ingress
  namespace: novaconnect
  annotations:
    kubernetes.io/ingress.class: "gce"
    networking.gke.io/managed-certificates: "novaconnect-cert"
    networking.gke.io/v1beta1.FrontendConfig: "novaconnect-frontend-config"
spec:
  rules:
  - host: api.novaconnect.novafuse.com
    http:
      paths:
      - path: /*
        pathType: ImplementationSpecific
        backend:
          service:
            name: novaconnect-api
            port:
              number: 80
---
apiVersion: networking.gke.io/v1beta1
kind: FrontendConfig
metadata:
  name: novaconnect-frontend-config
  namespace: novaconnect
spec:
  redirectToHttps:
    enabled: true
    responseCodeName: MOVED_PERMANENTLY_DEFAULT
---
apiVersion: networking.gke.io/v1
kind: ManagedCertificate
metadata:
  name: novaconnect-cert
  namespace: novaconnect
spec:
  domains:
  - api.novaconnect.novafuse.com
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: novaconnect-api-hpa
  namespace: novaconnect
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: novaconnect-api
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 0
      policies:
      - type: Percent
        value: 100
        periodSeconds: 60
      - type: Pods
        value: 4
        periodSeconds: 60
      selectPolicy: Max
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: novaconnect-api-pdb
  namespace: novaconnect
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: novaconnect-api
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: novaconnect-sa
  namespace: novaconnect
  annotations:
    iam.gke.io/gcp-service-account: <EMAIL>
---
apiVersion: monitoring.googleapis.com/v1
kind: PodMonitoring
metadata:
  name: novaconnect-monitoring
  namespace: novaconnect
spec:
  selector:
    matchLabels:
      app: novaconnect-api
  endpoints:
  - port: http
    path: /metrics
    interval: 30s
---
apiVersion: cloud.google.com/v1
kind: BackendConfig
metadata:
  name: novaconnect-backend-config
  namespace: novaconnect
spec:
  healthCheck:
    checkIntervalSec: 15
    timeoutSec: 5
    healthyThreshold: 2
    unhealthyThreshold: 3
    type: HTTP
    requestPath: /health
    port: 3000
  securityPolicy:
    name: "novaconnect-security-policy"
  logging:
    enable: true
  cdn:
    enabled: false
