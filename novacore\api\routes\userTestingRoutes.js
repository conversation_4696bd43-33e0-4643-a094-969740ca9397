/**
 * User Testing Routes
 * 
 * This file defines the routes for the user testing API.
 */

const express = require('express');
const router = express.Router();
const { asyncHandler } = require('../middleware/asyncHandler');
const { authenticate, authorize } = require('../middleware/authMiddleware');
const userTestingController = require('../controllers/UserTestingController');

/**
 * @route   GET /api/user-testing/sessions
 * @desc    Get all test sessions
 * @access  Private
 */
router.get(
  '/sessions',
  authenticate,
  authorize('read:user_testing'),
  async<PERSON>and<PERSON>(userTestingController.getTestSessions)
);

/**
 * @route   GET /api/user-testing/sessions/:id
 * @desc    Get a test session by ID
 * @access  Private
 */
router.get(
  '/sessions/:id',
  authenticate,
  authorize('read:user_testing'),
  asyncHandler(userTestingController.getTestSessionById)
);

/**
 * @route   POST /api/user-testing/sessions
 * @desc    Create a new test session
 * @access  Private
 */
router.post(
  '/sessions',
  authenticate,
  authorize('write:user_testing'),
  as<PERSON><PERSON><PERSON><PERSON>(userTestingController.createTestSession)
);

/**
 * @route   PUT /api/user-testing/sessions/:id
 * @desc    Update a test session
 * @access  Private
 */
router.put(
  '/sessions/:id',
  authenticate,
  authorize('write:user_testing'),
  asyncHandler(userTestingController.updateTestSession)
);

/**
 * @route   DELETE /api/user-testing/sessions/:id
 * @desc    Delete a test session
 * @access  Private
 */
router.delete(
  '/sessions/:id',
  authenticate,
  authorize('write:user_testing'),
  asyncHandler(userTestingController.deleteTestSession)
);

/**
 * @route   GET /api/user-testing/results
 * @desc    Get all test results
 * @access  Private
 */
router.get(
  '/results',
  authenticate,
  authorize('read:user_testing'),
  asyncHandler(userTestingController.getTestResults)
);

/**
 * @route   GET /api/user-testing/results/:id
 * @desc    Get a test result by ID
 * @access  Private
 */
router.get(
  '/results/:id',
  authenticate,
  authorize('read:user_testing'),
  asyncHandler(userTestingController.getTestResultById)
);

/**
 * @route   POST /api/user-testing/results
 * @desc    Submit test results
 * @access  Public
 */
router.post(
  '/results',
  asyncHandler(userTestingController.submitTestResults)
);

/**
 * @route   DELETE /api/user-testing/results/:id
 * @desc    Delete a test result
 * @access  Private
 */
router.delete(
  '/results/:id',
  authenticate,
  authorize('write:user_testing'),
  asyncHandler(userTestingController.deleteTestResult)
);

/**
 * @route   GET /api/user-testing/results/:id/export
 * @desc    Export a test result
 * @access  Private
 */
router.get(
  '/results/:id/export',
  authenticate,
  authorize('read:user_testing'),
  asyncHandler(userTestingController.exportTestResult)
);

module.exports = router;
