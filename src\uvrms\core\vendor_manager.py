"""
Vendor Manager for the Universal Vendor Risk Management System.

This module provides functionality for managing vendor information.
"""

import os
import json
import logging
import uuid
from typing import Dict, List, Any, Optional

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class VendorManager:
    """
    Manager for vendor information.
    
    This class is responsible for creating, retrieving, updating, and deleting
    vendor information.
    """
    
    def __init__(self, vendors_dir: Optional[str] = None):
        """
        Initialize the Vendor Manager.
        
        Args:
            vendors_dir: Path to a directory for storing vendor information
        """
        logger.info("Initializing Vendor Manager")
        
        # Set the vendors directory
        self.vendors_dir = vendors_dir or os.path.join(os.getcwd(), 'vendor_data')
        
        # Create the vendors directory if it doesn't exist
        os.makedirs(self.vendors_dir, exist_ok=True)
        
        # Dictionary to store vendors in memory
        self.vendors: Dict[str, Dict[str, Any]] = {}
        
        # Load vendors from disk
        self._load_vendors_from_disk()
        
        logger.info(f"Vendor Manager initialized with {len(self.vendors)} vendors")
    
    def create_vendor(self, vendor_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a new vendor.
        
        Args:
            vendor_data: The vendor data
            
        Returns:
            The created vendor
            
        Raises:
            ValueError: If the vendor data is invalid
        """
        logger.info("Creating new vendor")
        
        # Validate the vendor data
        self._validate_vendor_data(vendor_data)
        
        # Generate a unique vendor ID
        vendor_id = str(uuid.uuid4())
        
        # Create the vendor object
        vendor = {
            'id': vendor_id,
            'name': vendor_data.get('name'),
            'description': vendor_data.get('description', ''),
            'contact_info': vendor_data.get('contact_info', {}),
            'services': vendor_data.get('services', []),
            'categories': vendor_data.get('categories', []),
            'status': vendor_data.get('status', 'active'),
            'created_at': self._get_current_timestamp(),
            'updated_at': self._get_current_timestamp()
        }
        
        # Store the vendor in memory
        self.vendors[vendor_id] = vendor
        
        # Store the vendor on disk
        self._save_vendor_to_disk(vendor)
        
        logger.info(f"Vendor created: {vendor_id}")
        
        return vendor
    
    def get_vendor(self, vendor_id: str) -> Dict[str, Any]:
        """
        Get a vendor.
        
        Args:
            vendor_id: The ID of the vendor
            
        Returns:
            The vendor
            
        Raises:
            ValueError: If the vendor does not exist
        """
        logger.info(f"Getting vendor: {vendor_id}")
        
        if vendor_id not in self.vendors:
            raise ValueError(f"Vendor not found: {vendor_id}")
        
        return self.vendors[vendor_id]
    
    def update_vendor(self, vendor_id: str, vendor_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update a vendor.
        
        Args:
            vendor_id: The ID of the vendor
            vendor_data: The updated vendor data
            
        Returns:
            The updated vendor
            
        Raises:
            ValueError: If the vendor does not exist
            ValueError: If the vendor data is invalid
        """
        logger.info(f"Updating vendor: {vendor_id}")
        
        # Check if the vendor exists
        if vendor_id not in self.vendors:
            raise ValueError(f"Vendor not found: {vendor_id}")
        
        # Get the existing vendor
        vendor = self.vendors[vendor_id]
        
        # Update the vendor data
        if 'name' in vendor_data:
            vendor['name'] = vendor_data['name']
        
        if 'description' in vendor_data:
            vendor['description'] = vendor_data['description']
        
        if 'contact_info' in vendor_data:
            vendor['contact_info'] = vendor_data['contact_info']
        
        if 'services' in vendor_data:
            vendor['services'] = vendor_data['services']
        
        if 'categories' in vendor_data:
            vendor['categories'] = vendor_data['categories']
        
        if 'status' in vendor_data:
            vendor['status'] = vendor_data['status']
        
        # Update the updated_at timestamp
        vendor['updated_at'] = self._get_current_timestamp()
        
        # Store the updated vendor on disk
        self._save_vendor_to_disk(vendor)
        
        logger.info(f"Vendor updated: {vendor_id}")
        
        return vendor
    
    def delete_vendor(self, vendor_id: str) -> None:
        """
        Delete a vendor.
        
        Args:
            vendor_id: The ID of the vendor
            
        Raises:
            ValueError: If the vendor does not exist
        """
        logger.info(f"Deleting vendor: {vendor_id}")
        
        # Check if the vendor exists
        if vendor_id not in self.vendors:
            raise ValueError(f"Vendor not found: {vendor_id}")
        
        # Remove the vendor from memory
        del self.vendors[vendor_id]
        
        # Remove the vendor from disk
        self._delete_vendor_from_disk(vendor_id)
        
        logger.info(f"Vendor deleted: {vendor_id}")
    
    def get_all_vendors(self) -> List[Dict[str, Any]]:
        """
        Get all vendors.
        
        Returns:
            List of vendors
        """
        logger.info("Getting all vendors")
        
        return list(self.vendors.values())
    
    def search_vendors(self, 
                      query: Optional[str] = None, 
                      categories: Optional[List[str]] = None,
                      status: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Search for vendors.
        
        Args:
            query: Optional search query
            categories: Optional list of categories
            status: Optional vendor status
            
        Returns:
            List of matching vendors
        """
        logger.info(f"Searching vendors: query={query}, categories={categories}, status={status}")
        
        # Start with all vendors
        results = self.get_all_vendors()
        
        # Filter by query
        if query:
            query = query.lower()
            results = [
                v for v in results
                if query in v.get('name', '').lower() or
                   query in v.get('description', '').lower() or
                   any(query in s.lower() for s in v.get('services', []))
            ]
        
        # Filter by categories
        if categories:
            results = [
                v for v in results
                if any(c in v.get('categories', []) for c in categories)
            ]
        
        # Filter by status
        if status:
            results = [v for v in results if v.get('status') == status]
        
        logger.info(f"Found {len(results)} matching vendors")
        
        return results
    
    def _validate_vendor_data(self, vendor_data: Dict[str, Any]) -> None:
        """
        Validate vendor data.
        
        Args:
            vendor_data: The vendor data to validate
            
        Raises:
            ValueError: If the vendor data is invalid
        """
        # Check required fields
        if 'name' not in vendor_data:
            raise ValueError("Vendor name is required")
        
        # Validate contact_info if provided
        if 'contact_info' in vendor_data:
            contact_info = vendor_data['contact_info']
            
            if not isinstance(contact_info, dict):
                raise ValueError("Contact info must be a dictionary")
        
        # Validate services if provided
        if 'services' in vendor_data:
            services = vendor_data['services']
            
            if not isinstance(services, list):
                raise ValueError("Services must be a list")
        
        # Validate categories if provided
        if 'categories' in vendor_data:
            categories = vendor_data['categories']
            
            if not isinstance(categories, list):
                raise ValueError("Categories must be a list")
        
        # Validate status if provided
        if 'status' in vendor_data:
            status = vendor_data['status']
            
            if status not in ['active', 'inactive', 'pending', 'suspended']:
                raise ValueError("Invalid vendor status")
    
    def _load_vendors_from_disk(self) -> None:
        """Load vendors from disk."""
        try:
            # Get all JSON files in the vendors directory
            vendor_files = [f for f in os.listdir(self.vendors_dir) if f.endswith('.json')]
            
            for vendor_file in vendor_files:
                try:
                    # Load the vendor from disk
                    file_path = os.path.join(self.vendors_dir, vendor_file)
                    
                    with open(file_path, 'r', encoding='utf-8') as f:
                        vendor = json.load(f)
                    
                    # Store the vendor in memory
                    vendor_id = vendor.get('id')
                    
                    if vendor_id:
                        self.vendors[vendor_id] = vendor
                        logger.info(f"Loaded vendor from disk: {vendor_id}")
                
                except Exception as e:
                    logger.error(f"Failed to load vendor from {vendor_file}: {e}")
            
            logger.info(f"Loaded {len(self.vendors)} vendors from disk")
        
        except Exception as e:
            logger.error(f"Failed to load vendors from disk: {e}")
    
    def _save_vendor_to_disk(self, vendor: Dict[str, Any]) -> None:
        """
        Save a vendor to disk.
        
        Args:
            vendor: The vendor to save
        """
        try:
            # Get the vendor ID
            vendor_id = vendor.get('id')
            
            if not vendor_id:
                raise ValueError("Vendor ID is missing")
            
            # Create the file path
            file_path = os.path.join(self.vendors_dir, f"{vendor_id}.json")
            
            # Save the vendor to a JSON file
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(vendor, f, indent=2)
            
            logger.info(f"Saved vendor to disk: {vendor_id}")
        
        except Exception as e:
            logger.error(f"Failed to save vendor to disk: {e}")
    
    def _delete_vendor_from_disk(self, vendor_id: str) -> None:
        """
        Delete a vendor from disk.
        
        Args:
            vendor_id: The ID of the vendor
        """
        try:
            # Create the file path
            file_path = os.path.join(self.vendors_dir, f"{vendor_id}.json")
            
            # Check if the file exists
            if os.path.exists(file_path):
                # Delete the file
                os.remove(file_path)
                logger.info(f"Deleted vendor from disk: {vendor_id}")
            else:
                logger.warning(f"Vendor file not found on disk: {vendor_id}")
        
        except Exception as e:
            logger.error(f"Failed to delete vendor from disk: {e}")
    
    def _get_current_timestamp(self) -> str:
        """
        Get the current timestamp.
        
        Returns:
            The current timestamp as a string
        """
        import datetime
        return datetime.datetime.now().isoformat()
