<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Visualization Output Examples</title>
    <style>
        /* Reset and Base Styles */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            line-height: 1.4;
        }
        
        /* Main Diagram Container */
        .diagram-container {
            position: relative;
            width: 950px;
            min-height: 700px; /* Minimum height, will expand with content */
            margin: 0 auto 40px;
            background-color: white;
            border: 1px solid #ddd;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            box-sizing: border-box;
            overflow: visible; /* Allow content to be fully visible */
        }
        
        /* Container Box Styles */
        .container-box {
            position: absolute;
            border: 2px solid #666;
            border-radius: 10px;
            padding: 30px 10px 10px;
            overflow: visible; /* Allow content to be fully visible */
            min-height: 100px; /* Minimum height */
            min-width: 200px; /* Minimum width */
        }
        
        .container-label {
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            font-weight: bold;
            font-size: 16px;
            text-align: center;
            width: 90%; /* Ensures text doesn't overflow container */
            white-space: normal; /* Allows text to wrap */
            overflow: visible; /* Allow content to be fully visible */
        }
        
        /* Component Box Styles */
        .component-box {
            position: absolute;
            background-color: white;
            border: 2px solid #333;
            border-radius: 6px;
            display: flex;
            flex-direction: column;
            justify-content: center; /* Center content vertically */
            align-items: center;
            text-align: center;
            padding: 5px; /* Reduced padding */
            overflow: visible; /* Allow content to be fully visible */
            border-top-left-radius: 0; /* Flat corner for number */
            min-height: 50px; /* Minimum height */
            min-width: 100px; /* Minimum width */
        }
        
        /* Component Number Styles - Integrated into corner */
        .component-number-inside {
            position: absolute;
            top: 0;
            left: 0;
            width: 18px;
            height: 18px;
            background-color: #000000; /* Black background for component numbers */
            color: white;
            border-radius: 0 0 6px 0; /* Rounded on bottom-right corner only */
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 10px; /* Smaller font */
            font-weight: bold;
            z-index: 2;
            margin: 0; /* Remove any margin */
            padding: 0; /* Remove any padding */
        }
        
        .component-label {
            font-weight: bold;
            margin-top: 10px; /* Reduced margin */
            margin-bottom: 2px; /* Reduced margin */
            font-size: 12px; /* Smaller font */
            text-align: center;
            width: 100%;
            white-space: normal; /* Allows text to wrap */
            overflow: visible; /* Allow content to be fully visible */
        }
        
        .component-content {
            font-size: 10px; /* Smaller font */
            text-align: center;
            width: 100%;
            padding: 0;
            white-space: normal; /* Allows text to wrap */
            overflow: visible; /* Allow content to be fully visible */
            line-height: 1.2; /* Tighter line spacing */
        }
        
        /* Arrow Styles */
        .arrow {
            position: absolute;
            background-color: #555555; /* Grey color for patent compliance */
            width: 2px;
            z-index: 0;
        }
        
        /* Legend Styles */
        .legend {
            position: absolute;
            right: 10px;
            bottom: 30px;
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 8px;
            z-index: 10;
            width: 200px;
            font-size: 12px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 6px;
        }
        
        .legend-color {
            width: 15px;
            height: 15px;
            margin-right: 8px;
            border: 1px solid #333;
            flex-shrink: 0; /* Prevents the color box from shrinking */
        }
        
        /* Inventor Label */
        .inventor-label {
            position: absolute;
            left: 10px;
            bottom: 30px;
            font-size: 12px;
            font-style: italic;
            color: #333;
            z-index: 10;
        }
        
        /* Title Styles */
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 20px;
            color: #333;
        }
    </style>
</head>
<body>

    <h1>FIG. 12: Visualization Output Examples</h1>
    
    <div class="diagram-container">
        <!-- Main Container -->
        <div class="container-box" style="overflow: visible; min-height: 100px; min-width: 200px;" style="width: 750px; height: 580px; left: 25px; top: 20px;">
            <div class="container-label" style="font-size: 18px;">VISUALIZATION OUTPUT EXAMPLES: CYBER-SAFETY STATE</div>
        </div>

        <!-- Cyber-Safety Dashboard -->
        <div class="visualization-box" style="left: 50px; top: 80px; width: 340px; height: 580px;">
            <div class="visualization-title">Cyber-Safety Dashboard</div>
            <div class="visualization-content">
                <div class="dashboard-item">
                    <div class="metric">
                        <div class="metric-label">System Health Score</div>
                        <div class="metric-value">94.7%</div>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 94.7%;"></div>
                    </div>
                </div>
                <div class="dashboard-item">
                    <div class="metric">
                        <div class="metric-label">Data Purity Score</div>
                        <div class="metric-value">89.2%</div>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 89.2%;"></div>
                    </div>
                </div>
                <div class="dashboard-item">
                    <div class="metric">
                        <div class="metric-label">Compliance Coverage</div>
                        <div class="metric-value">97.3%</div>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 97.3%;"></div>
                    </div>
                </div>
                <div class="dashboard-item">
                    <div class="metric">
                        <div class="metric-label">Threat Detection Rate</div>
                        <div class="metric-value">99.1%</div>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 99.1%;"></div>
                    </div>
                </div>
                <div class="dashboard-item">
                    <div class="metric">
                        <div class="metric-label">Adaptive Response Time</div>
                        <div class="metric-value">3.2ms</div>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 95%;"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Field Coherence Map -->
        <div class="visualization-box" style="left: 410px; top: 80px; width: 340px; height: 580px;">
            <div class="visualization-title">Field Coherence Map</div>
            <div class="visualization-content">
                <svg width="320" height="180">
                    <!-- Grid lines -->
                    <line x1="0" y1="45" x2="320" y2="45" stroke="#ddd" stroke-width="1" />
                    <line x1="0" y1="90" x2="320" y2="90" stroke="#ddd" stroke-width="1" />
                    <line x1="0" y1="135" x2="320" y2="135" stroke="#ddd" stroke-width="1" />
                    <line x1="80" y1="0" x2="80" y2="180" stroke="#ddd" stroke-width="1" />
                    <line x1="160" y1="0" x2="160" y2="180" stroke="#ddd" stroke-width="1" />
                    <line x1="240" y1="0" x2="240" y2="180" stroke="#ddd" stroke-width="1" />
                    
                    <!-- Field nodes -->
                    <circle cx="60" cy="50" r="15" fill="#f5f5f5" stroke="#555555" stroke-width="2" />
                    <circle cx="120" cy="80" r="12" fill="#f5f5f5" stroke="#555555" stroke-width="2" />
                    <circle cx="180" cy="40" r="18" fill="#f5f5f5" stroke="#555555" stroke-width="2" />
                    <circle cx="240" cy="100" r="14" fill="#f5f5f5" stroke="#555555" stroke-width="2" />
                    <circle cx="90" cy="140" r="16" fill="#f5f5f5" stroke="#555555" stroke-width="2" />
                    <circle cx="280" cy="60" r="10" fill="#f5f5f5" stroke="#555555" stroke-width="2" />
                    <circle cx="200" cy="130" r="13" fill="#f5f5f5" stroke="#555555" stroke-width="2" />
                    
                    <!-- Connections -->
                    <line x1="60" y1="50" x2="120" y2="80" stroke="#555555" stroke-width="1.5" />
                    <line x1="120" y1="80" x2="180" y2="40" stroke="#555555" stroke-width="1.5" />
                    <line x1="180" y1="40" x2="240" y2="100" stroke="#555555" stroke-width="1.5" />
                    <line x1="240" y1="100" x2="200" y2="130" stroke="#555555" stroke-width="1.5" />
                    <line x1="200" y1="130" x2="90" y2="140" stroke="#555555" stroke-width="1.5" />
                    <line x1="90" y1="140" x2="60" y2="50" stroke="#555555" stroke-width="1.5" />
                    <line x1="180" y1="40" x2="280" y2="60" stroke="#555555" stroke-width="1.5" />
                    <line x1="280" y1="60" x2="240" y2="100" stroke="#555555" stroke-width="1.5" />
                    
                    <!-- Labels -->
                    <text x="60" y="50" text-anchor="middle" dominant-baseline="middle" font-size="8" fill="#555555">A</text>
                    <text x="120" y="80" text-anchor="middle" dominant-baseline="middle" font-size="8" fill="#555555">B</text>
                    <text x="180" y="40" text-anchor="middle" dominant-baseline="middle" font-size="8" fill="#555555">C</text>
                    <text x="240" y="100" text-anchor="middle" dominant-baseline="middle" font-size="8" fill="#555555">D</text>
                    <text x="90" y="140" text-anchor="middle" dominant-baseline="middle" font-size="8" fill="#555555">E</text>
                    <text x="280" y="60" text-anchor="middle" dominant-baseline="middle" font-size="8" fill="#555555">F</text>
                    <text x="200" y="130" text-anchor="middle" dominant-baseline="middle" font-size="8" fill="#555555">G</text>
                </svg>
            </div>
        </div>

        <!-- Trinity Visualization -->
        <div class="visualization-box" style="left: 50px; top: 320px; width: 340px; height: 580px;">
            <div class="visualization-title">Trinity Visualization</div>
            <div class="visualization-content">
                <svg width="320" height="180">
                    <!-- Triangle -->
                    <polygon points="160,20 40,160 280,160" fill="none" stroke="#555555" stroke-width="2" />
                    
                    <!-- Inner circles -->
                    <circle cx="160" cy="90" r="30" fill="none" stroke="#555555" stroke-width="1.5" />
                    
                    <!-- Vertices -->
                    <circle cx="160" cy="20" r="15" fill="#f5f5f5" stroke="#555555" stroke-width="2" />
                    <circle cx="40" cy="160" r="15" fill="#f5f5f5" stroke="#555555" stroke-width="2" />
                    <circle cx="280" cy="160" r="15" fill="#f5f5f5" stroke="#555555" stroke-width="2" />
                    
                    <!-- Center -->
                    <circle cx="160" cy="90" r="20" fill="#f5f5f5" stroke="#555555" stroke-width="2" />
                    
                    <!-- Labels -->
                    <text x="160" y="25" text-anchor="middle" dominant-baseline="middle" font-size="10" fill="#555555">Truth</text>
                    <text x="40" y="160" text-anchor="middle" dominant-baseline="middle" font-size="10" fill="#555555">Trust</text>
                    <text x="280" y="160" text-anchor="middle" dominant-baseline="middle" font-size="10" fill="#555555">Transparency</text>
                    <text x="160" y="90" text-anchor="middle" dominant-baseline="middle" font-size="10" fill="#555555">UUFT</text>
                </svg>
            </div>
        </div>

        <!-- Adaptive Compliance Visualization -->
        <div class="visualization-box" style="left: 410px; top: 320px; width: 340px; height: 580px;">
            <div class="visualization-title">Adaptive Compliance Visualization</div>
            <div class="visualization-content">
                <div class="graph">
                    <svg width="100%" height="100%" viewBox="0 0 320 100" preserveAspectRatio="none">
                        <!-- Grid lines -->
                        <line x1="0" y1="25" x2="320" y2="25" stroke="#ddd" stroke-width="1" />
                        <line x1="0" y1="50" x2="320" y2="50" stroke="#ddd" stroke-width="1" />
                        <line x1="0" y1="75" x2="320" y2="75" stroke="#ddd" stroke-width="1" />
                        
                        <!-- Compliance threshold line -->
                        <line x1="0" y1="30" x2="320" y2="30" stroke="#555555" stroke-width="1" stroke-dasharray="5,5" />
                        
                        <!-- Compliance level line -->
                        <polyline 
                            points="0,70 40,65 80,40 120,35 160,25 200,20 240,15 280,20 320,25" 
                            fill="none" 
                            stroke="#555555" 
                            stroke-width="2" 
                        />
                        
                        <!-- Adaptive response markers -->
                        <circle cx="80" cy="40" r="4" fill="#555555" />
                        <circle cx="160" cy="25" r="4" fill="#555555" />
                        <circle cx="240" cy="15" r="4" fill="#555555" />
                        
                        <!-- Labels -->
                        <text x="10" y="95" font-size="8" fill="#555555">T-24h</text>
                        <text x="310" y="95" font-size="8" fill="#555555">Now</text>
                        <text x="325" y="30" font-size="8" fill="#555555">Threshold</text>
                    </svg>
                </div>
                <div style="margin-top: 10px; font-size: 11px;">
                    <div style="display: flex; justify-content: space-between;">
                        <div>Current Compliance Level: 95.8%</div>
                        <div>Threshold: 90.0%</div>
                    </div>
                    <div style="margin-top: 5px;">
                        Adaptive Actions: 3 in last 24 hours
                    </div>
                </div>
            </div>
        </div>

        <div class="inventor-label" style="position: absolute; left: 10px; bottom: 10px; font-size: 12px; font-style: italic; color: #333;">Inventor: David Nigel Irvin</div>
        <div class="inventor-label">Inventor: David Nigel Irvin</div>
</div>

</body>
</html>
