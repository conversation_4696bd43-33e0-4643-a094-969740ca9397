# PowerShell script to view diagrams one by one

# Create a simple HTML template for each diagram
$htmlTemplate = @"
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Comphyology Diagram Viewer</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            margin-bottom: 20px;
        }
        .diagram-container {
            margin-top: 20px;
            border: 1px solid #eee;
            padding: 20px;
            border-radius: 8px;
        }
        .navigation {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
        }
        button {
            padding: 10px 20px;
            background-color: #0070f3;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #0060df;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>DIAGRAM_TITLE</h1>
        
        <div class="diagram-container" id="diagram-container">
            DIAGRAM_CONTENT
        </div>
        
        <div class="navigation">
            <button id="prev-button" onclick="prevDiagram()">Previous</button>
            <button id="next-button" onclick="nextDiagram()">Next</button>
        </div>
    </div>

    <script>
        function prevDiagram() {
            window.location.href = 'PREV_DIAGRAM_URL';
        }
        
        function nextDiagram() {
            window.location.href = 'NEXT_DIAGRAM_URL';
        }
        
        // Disable buttons if needed
        document.addEventListener('DOMContentLoaded', function() {
            if ('PREV_DIAGRAM_URL' === '#') {
                document.getElementById('prev-button').disabled = true;
            }
            if ('NEXT_DIAGRAM_URL' === '#') {
                document.getElementById('next-button').disabled = true;
            }
        });
    </script>
</body>
</html>
"@

# Define the diagrams
$diagrams = @(
    @{
        id = "high-level-architecture"
        title = "1. High-Level System Architecture"
        content = "<div style='text-align: center; padding: 50px;'><h2>High-Level System Architecture</h2><p>This diagram shows the NovaFuse Platform and its relationship with the Comphyology Framework.</p></div>"
    },
    @{
        id = "finite-universe"
        title = "2. Finite Universe Paradigm"
        content = "<div style='text-align: center; padding: 50px;'><h2>Finite Universe Paradigm</h2><p>This diagram illustrates the Finite Universe Paradigm concept.</p></div>"
    },
    @{
        id = "three-body-problem"
        title = "3. Three-Body Problem Reframing"
        content = "<div style='text-align: center; padding: 50px;'><h2>Three-Body Problem Reframing</h2><p>This diagram compares classical and Comphyological approaches to the three-body problem.</p></div>"
    },
    @{
        id = "uuft-equation-flow"
        title = "4. UUFT Equation Flow"
        content = "<div style='text-align: center; padding: 50px;'><h2>UUFT Equation Flow</h2><p>This diagram visualizes the UUFT equation (A⊗B⊕C)×π10³.</p></div>"
    },
    @{
        id = "trinity-equation"
        title = "5. Trinity Equation Visualization"
        content = "<div style='text-align: center; padding: 50px;'><h2>Trinity Equation Visualization</h2><p>This diagram visualizes the Trinity Equation concept.</p></div>"
    },
    @{
        id = "meta-field-schema"
        title = "6. Meta-Field Schema"
        content = "<div style='text-align: center; padding: 50px;'><h2>Meta-Field Schema</h2><p>This diagram represents the Meta-Field Schema.</p></div>"
    },
    @{
        id = "pattern-translation"
        title = "8. Pattern Translation Process"
        content = "<div style='text-align: center; padding: 50px;'><h2>Pattern Translation Process</h2><p>This diagram illustrates the Universal Pattern Language.</p></div>"
    },
    @{
        id = "novafuse-components"
        title = "9. 13 NovaFuse Components"
        content = "<div style='text-align: center; padding: 50px;'><h2>13 NovaFuse Components</h2><p>This diagram provides an overview of the 13 Universal NovaFuse Components.</p></div>"
    },
    @{
        id = "alignment-architecture"
        title = "10. 3-6-9-12-13 Alignment Architecture"
        content = "<div style='text-align: center; padding: 50px;'><h2>3-6-9-12-13 Alignment Architecture</h2><p>This diagram visualizes the Alignment Architecture.</p></div>"
    },
    @{
        id = "data-processing-pipeline"
        title = "11. Cross-Module Data Processing Pipeline"
        content = "<div style='text-align: center; padding: 50px;'><h2>Cross-Module Data Processing Pipeline</h2><p>This diagram shows the data processing pipeline.</p></div>"
    },
    @{
        id = "incident-response"
        title = "12. Cyber-Safety Incident Response Workflow"
        content = "<div style='text-align: center; padding: 50px;'><h2>Cyber-Safety Incident Response Workflow</h2><p>This diagram illustrates the workflow for incident response.</p></div>"
    },
    @{
        id = "healthcare-implementation"
        title = "13. Healthcare Implementation"
        content = "<div style='text-align: center; padding: 50px;'><h2>Healthcare Implementation</h2><p>This diagram shows the implementation for the healthcare industry.</p></div>"
    },
    @{
        id = "dashboard-visualization"
        title = "14. Dashboard and Visualization Examples"
        content = "<div style='text-align: center; padding: 50px;'><h2>Dashboard and Visualization Examples</h2><p>This diagram provides examples of dashboards and visualizations.</p></div>"
    },
    @{
        id = "hardware-architecture"
        title = "15. Hardware Architecture"
        content = "<div style='text-align: center; padding: 50px;'><h2>Hardware Architecture</h2><p>This diagram illustrates the hardware architecture.</p></div>"
    }
)

# Create diagrams directory if it doesn't exist
$diagramsDir = Join-Path $PSScriptRoot "diagram-viewer"
if (-not (Test-Path $diagramsDir)) {
    New-Item -ItemType Directory -Path $diagramsDir | Out-Null
}

# Generate HTML files for each diagram
for ($i = 0; $i -lt $diagrams.Count; $i++) {
    $diagram = $diagrams[$i]
    
    # Determine previous and next diagram URLs
    $prevUrl = if ($i -gt 0) { "$($diagrams[$i-1].id).html" } else { "#" }
    $nextUrl = if ($i -lt $diagrams.Count - 1) { "$($diagrams[$i+1].id).html" } else { "#" }
    
    # Create HTML content
    $htmlContent = $htmlTemplate -replace "DIAGRAM_TITLE", $diagram.title
    $htmlContent = $htmlContent -replace "DIAGRAM_CONTENT", $diagram.content
    $htmlContent = $htmlContent -replace "PREV_DIAGRAM_URL", $prevUrl
    $htmlContent = $htmlContent -replace "NEXT_DIAGRAM_URL", $nextUrl
    
    # Save HTML file
    $htmlPath = Join-Path $diagramsDir "$($diagram.id).html"
    $htmlContent | Out-File -FilePath $htmlPath -Encoding utf8
    
    Write-Host "Generated diagram: $($diagram.title)"
}

# Open the first diagram in the default browser
$firstDiagramPath = Join-Path $diagramsDir "$($diagrams[0].id).html"
$firstDiagramUri = [System.Uri]::new($firstDiagramPath)
Start-Process $firstDiagramUri.AbsoluteUri

Write-Host "Diagram viewer is now open in your browser."
