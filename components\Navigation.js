/* eslint-disable */
import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/router';

const Navigation = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [activeDropdown, setActiveDropdown] = useState(null);
  const router = useRouter();

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      // Check if the click is outside any dropdown button or dropdown menu
      if (!event.target.closest('.dropdown-button') && !event.target.closest('.dropdown-menu')) {
        setActiveDropdown(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Toggle dropdown visibility
  const toggleDropdown = (dropdown) => {
    setActiveDropdown(activeDropdown === dropdown ? null : dropdown);
  };

  // Close dropdown after navigation
  const handleLinkClick = () => {
    setActiveDropdown(null);
  };

  const isActive = (path) => {
    return router.pathname === path ? 'text-blue-400' : 'text-gray-300 hover:text-white';
  };

  return (
    <nav className="bg-secondary py-4">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center">
          <Link href="/" className="text-2xl font-bold">
            NovaFuse API Superstore
          </Link>

          {/* Mobile menu button */}
          <button
            className="md:hidden text-gray-300 hover:text-white"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              {isMenuOpen ? (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              ) : (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              )}
            </svg>
          </button>

          {/* Desktop menu */}
          <div className="hidden md:flex space-x-6">
            <Link href="/" className={isActive('/')}>
              Home
            </Link>

            {/* Products Dropdown */}
            <div className="relative">
              <button
                className={`flex items-center dropdown-button ${isActive('/novaconnect-uac') || isActive('/novagrc-suite') || isActive('/novaconcierge') || isActive('/nova-ui-components') ? 'text-blue-400' : 'text-gray-300 hover:text-white'}`}
                onClick={() => toggleDropdown('products')}
              >
                Products
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              {activeDropdown === 'products' && (
                <div className="absolute left-0 mt-2 w-48 bg-gray-800 rounded-md shadow-lg z-10 dropdown-menu">
                  <div className="py-1">
                    <Link href="/compliance-store" className="block px-4 py-2 text-sm font-bold text-blue-400 hover:bg-gray-700 hover:text-white border-b border-gray-700" onClick={handleLinkClick}>
                      Compliance App Store
                    </Link>
                    <Link href="/novaconnect-uac" className="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white" onClick={handleLinkClick}>
                      NovaConnect UAC
                    </Link>
                    <Link href="/uac-faq" className="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white" onClick={handleLinkClick}>
                      UAC FAQ
                    </Link>
                    <Link href="/novagrc-suite" className="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white" onClick={handleLinkClick}>
                      NovaGRC Suite
                    </Link>
                    <Link href="/novaconcierge" className="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white" onClick={handleLinkClick}>
                      NovaConcierge AI
                    </Link>
                    <Link href="/nova-ui-components" className="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white" onClick={handleLinkClick}>
                      NovaUI
                    </Link>
                    <Link href="/compliance-store" className="block px-4 py-2 text-sm font-bold text-blue-400 hover:bg-gray-700 hover:text-white border-t border-gray-700" onClick={handleLinkClick}>
                      Compliance App Store
                    </Link>
                  </div>
                </div>
              )}
            </div>

            {/* Solutions Dropdown */}
            <div className="relative">
              <button
                className={`flex items-center dropdown-button ${isActive('/solutions/by-industry') || isActive('/solutions/by-use-case') || isActive('/solutions/by-framework') ? 'text-blue-400' : 'text-gray-300 hover:text-white'}`}
                onClick={() => toggleDropdown('solutions')}
              >
                Solutions
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              {activeDropdown === 'solutions' && (
                <div className="absolute left-0 mt-2 w-48 bg-gray-800 rounded-md shadow-lg z-10 dropdown-menu">
                  <div className="py-1">
                    <Link href="/solutions/by-industry" className="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white" onClick={handleLinkClick}>
                      By Industry
                    </Link>
                    <Link href="/solutions/by-use-case" className="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white" onClick={handleLinkClick}>
                      By Use Case
                    </Link>
                    <Link href="/solutions/by-framework" className="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white" onClick={handleLinkClick}>
                      By Compliance Framework
                    </Link>
                  </div>
                </div>
              )}
            </div>

            {/* Partner Empowerment */}
            <Link href="/partner-empowerment" className={isActive('/partner-empowerment')}>
              Partner Empowerment
            </Link>

            {/* Resources Dropdown */}
            <div className="relative">
              <button
                className={`flex items-center dropdown-button ${isActive('/api-docs') || isActive('/uac-demo') || isActive('/partner-knowledge-base') || isActive('/resources/white-papers') ? 'text-blue-400' : 'text-gray-300 hover:text-white'}`}
                onClick={() => toggleDropdown('resources')}
              >
                Resources
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              {activeDropdown === 'resources' && (
                <div className="absolute left-0 mt-2 w-48 bg-gray-800 rounded-md shadow-lg z-10 dropdown-menu">
                  <div className="py-1">
                    <Link href="/resources/documentation" className="block px-4 py-2 text-sm font-bold text-blue-400 hover:bg-gray-700 hover:text-white border-b border-gray-700" onClick={handleLinkClick}>
                      Documentation Repository
                    </Link>
                    <Link href="/developers" className="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white" onClick={handleLinkClick}>
                      Developer Portal
                    </Link>
                    <Link href="/api-docs" className="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white" onClick={handleLinkClick}>
                      API Docs
                    </Link>
                    <Link href="/uac-demo" className="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white" onClick={handleLinkClick}>
                      UAC Demo
                    </Link>
                    <Link href="/partner-knowledge-base" className="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white" onClick={handleLinkClick}>
                      Partner Knowledge Base
                    </Link>
                    <Link href="/resources/white-papers" className="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white" onClick={handleLinkClick}>
                      White Papers
                    </Link>
                  </div>
                </div>
              )}
            </div>

            {/* About Us */}
            <Link href="/about" className={isActive('/about')}>
              About Us
            </Link>

            {/* Contact */}
            <Link href="/contact" className={isActive('/contact')}>
              Contact
            </Link>
          </div>

          {/* Desktop CTA buttons */}
          <div className="hidden md:flex space-x-4">
            <Link href="/sign-in" className="accent-bg text-white px-4 py-2 rounded hover:bg-blue-700">
              Sign In
            </Link>
            <Link href="/sign-up" className="bg-white text-blue-700 px-4 py-2 rounded hover:bg-gray-100 border border-blue-400">
              Sign Up
            </Link>
          </div>
        </div>

        {/* Mobile menu */}
        {isMenuOpen && (
          <div className="md:hidden mt-4 space-y-4">
            <div className="flex flex-col space-y-3">
              <Link href="/" className={`${isActive('/')} py-2`}>
                Home
              </Link>

              {/* Products Section */}
              <div className="py-2 border-t border-gray-700">
                <h3 className="text-gray-400 font-semibold mb-2">Products</h3>
                <div className="pl-4 flex flex-col space-y-2">
                  <Link href="/compliance-store" className={`${isActive('/compliance-store')} py-1 font-bold text-blue-400`}>
                    Compliance App Store
                  </Link>
                  <Link href="/novaconnect-uac" className={`${isActive('/novaconnect-uac')} py-1`}>
                    NovaConnect UAC
                  </Link>
                  <Link href="/uac-faq" className={`${isActive('/uac-faq')} py-1`}>
                    UAC FAQ
                  </Link>
                  <Link href="/novagrc-suite" className={`${isActive('/novagrc-suite')} py-1`}>
                    NovaGRC Suite
                  </Link>
                  <Link href="/novaconcierge" className={`${isActive('/novaconcierge')} py-1`}>
                    NovaConcierge AI
                  </Link>
                  <Link href="/nova-ui-components" className={`${isActive('/nova-ui-components')} py-1`}>
                    NovaUI
                  </Link>
                  <Link href="/compliance-store" className={`${isActive('/compliance-store')} py-1 font-bold text-blue-400 mt-2 border-t border-gray-700 pt-2`}>
                    Compliance App Store
                  </Link>
                </div>
              </div>

              {/* Solutions Section */}
              <div className="py-2 border-t border-gray-700">
                <h3 className="text-gray-400 font-semibold mb-2">Solutions</h3>
                <div className="pl-4 flex flex-col space-y-2">
                  <Link href="/solutions/by-industry" className={`${isActive('/solutions/by-industry')} py-1`}>
                    By Industry
                  </Link>
                  <Link href="/solutions/by-use-case" className={`${isActive('/solutions/by-use-case')} py-1`}>
                    By Use Case
                  </Link>
                  <Link href="/solutions/by-framework" className={`${isActive('/solutions/by-framework')} py-1`}>
                    By Compliance Framework
                  </Link>
                </div>
              </div>

              {/* Partner Empowerment */}
              <Link href="/partner-empowerment" className={`${isActive('/partner-empowerment')} py-2`}>
                Partner Empowerment
              </Link>

              {/* Resources Section */}
              <div className="py-2 border-t border-gray-700">
                <h3 className="text-gray-400 font-semibold mb-2">Resources</h3>
                <div className="pl-4 flex flex-col space-y-2">
                  <Link href="/resources/documentation" className={`${isActive('/resources/documentation')} py-1 font-bold text-blue-400`}>
                    Documentation Repository
                  </Link>
                  <Link href="/developers" className={`${isActive('/developers')} py-1`}>
                    Developer Portal
                  </Link>
                  <Link href="/api-docs" className={`${isActive('/api-docs')} py-1`}>
                    API Docs
                  </Link>
                  <Link href="/uac-demo" className={`${isActive('/uac-demo')} py-1`}>
                    UAC Demo
                  </Link>
                  <Link href="/partner-knowledge-base" className={`${isActive('/partner-knowledge-base')} py-1`}>
                    Partner Knowledge Base
                  </Link>
                  <Link href="/resources/white-papers" className={`${isActive('/resources/white-papers')} py-1`}>
                    White Papers
                  </Link>
                </div>
              </div>

              {/* About Us */}
              <Link href="/about" className={`${isActive('/about')} py-2`}>
                About Us
              </Link>

              {/* Contact */}
              <Link href="/contact" className={`${isActive('/contact')} py-2`}>
                Contact
              </Link>
            </div>

            <div className="flex flex-col space-y-3 pt-3 border-t border-gray-700">
              <Link href="/sign-in" className="accent-bg text-white px-4 py-2 rounded hover:bg-blue-700 text-center">
                Sign In
              </Link>
              <Link href="/sign-up" className="bg-white text-blue-700 px-4 py-2 rounded hover:bg-gray-100 border border-blue-400 text-center">
                Sign Up
              </Link>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
};

export default Navigation;
