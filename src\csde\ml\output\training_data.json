[{"input": {"complianceData": {"complianceScore": 0.5835014621413772, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "medium", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "medium", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "high", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "low", "status": "partial", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.8168148011658662, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "medium", "status": "partial", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "medium", "status": "non-optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "high", "status": "non-optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "low", "status": "optimal", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "low", "status": "non-optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.2990671945821446, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "low", "status": "implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "medium", "status": "partial", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "high", "status": "not-implemented", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 1406986.760566949}}, {"input": {"complianceData": {"complianceScore": 0.31518395069657745, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "low", "status": "partial", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "medium", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "low", "status": "partial", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.39873612271469594, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "medium", "status": "non-optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "high", "status": "non-optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "medium", "status": "non-optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "low", "status": "optimal", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "low", "status": "partial", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.38809232092898527, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "high", "status": "implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "medium", "status": "partial", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "high", "status": "implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "medium", "status": "partial", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 481438.0513282326}}, {"input": {"complianceData": {"complianceScore": 0.8711629167811292, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "medium", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "high", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.8687211725316875, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "low", "status": "optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "high", "status": "optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "high", "status": "partial", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "high", "status": "optimal", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "high", "status": "optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.9784436080072882, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "high", "status": "implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "medium", "status": "partial", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "low", "status": "not-implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "medium", "status": "implemented", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "high", "status": "partial", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 7309224.034183593}}, {"input": {"complianceData": {"complianceScore": 0.8730416925023388, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "medium", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "high", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "low", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "high", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "high", "status": "partial", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.7118798491508322, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "low", "status": "non-optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "low", "status": "partial", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "medium", "status": "optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "high", "status": "partial", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "high", "status": "optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.9704664710194442, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "low", "status": "not-implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "low", "status": "partial", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "low", "status": "not-implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "high", "status": "not-implemented", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "high", "status": "implemented", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 5953576.053099261}}, {"input": {"complianceData": {"complianceScore": 0.5849607689010985, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "medium", "status": "compliant", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.48565857296661674, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "medium", "status": "non-optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "low", "status": "non-optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "high", "status": "non-optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "medium", "status": "optimal", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "low", "status": "non-optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.04207663227592828, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "low", "status": "partial", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "medium", "status": "partial", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "high", "status": "not-implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "medium", "status": "partial", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "low", "status": "partial", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 117992.51524719853}}, {"input": {"complianceData": {"complianceScore": 0.9223872666595971, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "high", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "medium", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "high", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "low", "status": "partial", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.9619091345945725, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "high", "status": "non-optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "low", "status": "partial", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "medium", "status": "non-optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "high", "status": "non-optimal", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "low", "status": "non-optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.8537580612291786, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "high", "status": "not-implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "low", "status": "partial", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "high", "status": "partial", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "low", "status": "implemented", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "high", "status": "implemented", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 7477180.27894387}}, {"input": {"complianceData": {"complianceScore": 0.906822418332067, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "high", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "low", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "high", "status": "partial", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.9561753241154198, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "high", "status": "optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "medium", "status": "optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "low", "status": "non-optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "high", "status": "partial", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "low", "status": "partial", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.19188517315336084, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "medium", "status": "implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "low", "status": "implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "medium", "status": "partial", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "high", "status": "partial", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "high", "status": "partial", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 1642316.608327682}}, {"input": {"complianceData": {"complianceScore": 0.32731020615057416, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "low", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "high", "status": "partial", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "medium", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "medium", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "medium", "status": "compliant", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.07363112903516944, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "medium", "status": "partial", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "low", "status": "optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "low", "status": "non-optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "medium", "status": "partial", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "high", "status": "optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.9331303704568903, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "high", "status": "not-implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "high", "status": "not-implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "low", "status": "implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "low", "status": "not-implemented", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "low", "status": "implemented", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 221982.64335359793}}, {"input": {"complianceData": {"complianceScore": 0.5149051398328026, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "medium", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "low", "status": "partial", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "high", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "medium", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "medium", "status": "compliant", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.2534470879109323, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "medium", "status": "optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "high", "status": "optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "low", "status": "non-optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "high", "status": "optimal", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "low", "status": "partial", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.7907496590921503, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "medium", "status": "implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "high", "status": "implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "medium", "status": "partial", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "high", "status": "not-implemented", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 1018613.0421600485}}, {"input": {"complianceData": {"complianceScore": 0.7660398707154947, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "high", "status": "partial", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "low", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "high", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "medium", "status": "non-compliant", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.10555519131272795, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "low", "status": "non-optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "low", "status": "partial", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "high", "status": "partial", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "high", "status": "partial", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "medium", "status": "non-optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.04640793887198069, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "high", "status": "not-implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "medium", "status": "implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "low", "status": "not-implemented", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "medium", "status": "implemented", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 37040.6789387349}}, {"input": {"complianceData": {"complianceScore": 0.05781338549129056, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "low", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "low", "status": "partial", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "low", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "high", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "medium", "status": "non-compliant", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.9089894275976593, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "medium", "status": "non-optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "low", "status": "non-optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "high", "status": "optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "high", "status": "non-optimal", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "medium", "status": "optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.9466935890113772, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "low", "status": "not-implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "medium", "status": "implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "medium", "status": "partial", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 491080.1237456033}}, {"input": {"complianceData": {"complianceScore": 0.9498112369799276, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "medium", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "high", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.42737121536633493, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "low", "status": "non-optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "high", "status": "partial", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "high", "status": "partial", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "high", "status": "non-optimal", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "medium", "status": "non-optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.6396928321536486, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "medium", "status": "partial", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "medium", "status": "partial", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "medium", "status": "implemented", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 2563124.7375652287}}, {"input": {"complianceData": {"complianceScore": 0.23673590125916122, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "medium", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "high", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "low", "status": "partial", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.07936102137454148, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "medium", "status": "non-optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "low", "status": "non-optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "low", "status": "optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "high", "status": "partial", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "medium", "status": "non-optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.5274485820424022, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "low", "status": "not-implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "low", "status": "not-implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "high", "status": "implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "low", "status": "partial", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 97815.38945052872}}, {"input": {"complianceData": {"complianceScore": 0.8237918964542355, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "high", "status": "partial", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "high", "status": "non-compliant", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.23225063496867882, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "low", "status": "partial", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "medium", "status": "non-optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "high", "status": "non-optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "medium", "status": "non-optimal", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "low", "status": "partial", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.21531041189345235, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "low", "status": "not-implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "low", "status": "partial", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "low", "status": "not-implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "high", "status": "partial", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "high", "status": "implemented", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 406625.9995823184}}, {"input": {"complianceData": {"complianceScore": 0.8651554463111442, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "low", "status": "partial", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "high", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "high", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "high", "status": "partial", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.7566543399741383, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "medium", "status": "optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "low", "status": "non-optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "low", "status": "non-optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "high", "status": "partial", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "low", "status": "partial", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.527075397095444, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "high", "status": "not-implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "low", "status": "partial", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "medium", "status": "implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "high", "status": "implemented", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "high", "status": "partial", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 3405807.5563151175}}, {"input": {"complianceData": {"complianceScore": 0.3958228947796836, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "high", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "low", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "high", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "low", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "high", "status": "compliant", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.0662856329422643, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "low", "status": "non-optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "low", "status": "optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "high", "status": "optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "medium", "status": "partial", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "high", "status": "non-optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.6468958965066856, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "medium", "status": "implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "high", "status": "not-implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "high", "status": "implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "high", "status": "partial", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "low", "status": "partial", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 167536.87136299783}}, {"input": {"complianceData": {"complianceScore": 0.9171798066509134, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "medium", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "low", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "low", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "high", "status": "partial", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "high", "status": "partial", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.37724268649173087, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "medium", "status": "partial", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "high", "status": "partial", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "low", "status": "optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "high", "status": "non-optimal", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "medium", "status": "optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.41347909279417294, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "medium", "status": "implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "high", "status": "not-implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "high", "status": "implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "high", "status": "not-implemented", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 1412162.109950904}}, {"input": {"complianceData": {"complianceScore": 0.5635008065109426, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "high", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "low", "status": "partial", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "high", "status": "non-compliant", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.1330761651218153, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "medium", "status": "optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "medium", "status": "partial", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "high", "status": "optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "low", "status": "partial", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "medium", "status": "non-optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.6337895833046743, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "low", "status": "not-implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "medium", "status": "partial", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "high", "status": "implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "low", "status": "implemented", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 469132.588883851}}, {"input": {"complianceData": {"complianceScore": 0.5836883342483026, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "high", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "high", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "high", "status": "partial", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "high", "status": "partial", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "high", "status": "compliant", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.5984464867981516, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "medium", "status": "partial", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "high", "status": "optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "low", "status": "optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "low", "status": "partial", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "high", "status": "partial", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.3844334151117219, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "high", "status": "implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "low", "status": "not-implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "high", "status": "partial", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "low", "status": "not-implemented", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "low", "status": "implemented", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 1325510.4364380725}}, {"input": {"complianceData": {"complianceScore": 0.025669698512339023, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "low", "status": "partial", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "high", "status": "partial", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "high", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "high", "status": "partial", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.11928050073654828, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "low", "status": "non-optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "high", "status": "optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "medium", "status": "optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "low", "status": "non-optimal", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "low", "status": "optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.598841142846686, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "medium", "status": "partial", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "high", "status": "partial", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "medium", "status": "partial", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "medium", "status": "implemented", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 18099.123298927032}}, {"input": {"complianceData": {"complianceScore": 0.5316788139146478, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "medium", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "low", "status": "partial", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "low", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.5968962736163974, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "high", "status": "non-optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "low", "status": "non-optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "medium", "status": "partial", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "low", "status": "non-optimal", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "low", "status": "optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.004679453060804972, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "low", "status": "implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "high", "status": "implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "low", "status": "implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "low", "status": "not-implemented", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 14658.819747244459}}, {"input": {"complianceData": {"complianceScore": 0.8879011826855885, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "high", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "high", "status": "partial", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "medium", "status": "non-compliant", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.4714882236539566, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "high", "status": "non-optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "low", "status": "partial", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "medium", "status": "non-optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "high", "status": "partial", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "low", "status": "non-optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.46071763513929476, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "low", "status": "not-implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "low", "status": "implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "low", "status": "partial", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "high", "status": "implemented", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "medium", "status": "implemented", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 1903820.5362366922}}, {"input": {"complianceData": {"complianceScore": 0.28465675874615615, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "medium", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "high", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "high", "status": "partial", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "low", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "medium", "status": "non-compliant", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.04239831579042064, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "low", "status": "optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "high", "status": "non-optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "medium", "status": "non-optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "high", "status": "non-optimal", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "medium", "status": "optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.9906586763659591, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "high", "status": "implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "high", "status": "partial", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "low", "status": "partial", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "high", "status": "partial", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "high", "status": "not-implemented", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 118018.43172303247}}, {"input": {"complianceData": {"complianceScore": 0.11386749232843663, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "high", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "low", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "low", "status": "compliant", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.4124085563529156, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "low", "status": "partial", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "low", "status": "non-optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "medium", "status": "optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "high", "status": "partial", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "low", "status": "optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.589229888744542, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "high", "status": "not-implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "low", "status": "implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "high", "status": "partial", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "medium", "status": "implemented", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "high", "status": "partial", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 273129.04013795726}}, {"input": {"complianceData": {"complianceScore": 0.2412056292021183, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "medium", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "medium", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "low", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "medium", "status": "non-compliant", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.3504836050737743, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "medium", "status": "optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "medium", "status": "optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "medium", "status": "non-optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "high", "status": "non-optimal", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "high", "status": "partial", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.2018517554653918, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "low", "status": "implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "high", "status": "not-implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "high", "status": "implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "low", "status": "partial", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "high", "status": "partial", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 168439.2750959927}}, {"input": {"complianceData": {"complianceScore": 0.7980234190054152, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "high", "status": "partial", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "low", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "medium", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "low", "status": "partial", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.6187400532819674, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "high", "status": "optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "high", "status": "non-optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "high", "status": "partial", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "high", "status": "optimal", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "high", "status": "optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.9172123349722701, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "high", "status": "implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "low", "status": "partial", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "low", "status": "not-implemented", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "low", "status": "not-implemented", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 4470431.452894533}}, {"input": {"complianceData": {"complianceScore": 0.1737595967411163, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "medium", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "low", "status": "partial", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "medium", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "low", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "high", "status": "partial", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.7983441029238156, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "high", "status": "optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "medium", "status": "optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "low", "status": "optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "medium", "status": "partial", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "medium", "status": "non-optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.4116402748004997, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "low", "status": "not-implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "low", "status": "not-implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "low", "status": "partial", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "low", "status": "implemented", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "high", "status": "not-implemented", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 563653.8369176451}}, {"input": {"complianceData": {"complianceScore": 0.5404642843389524, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "medium", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "high", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.1583355985754633, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "low", "status": "partial", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "high", "status": "partial", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "medium", "status": "non-optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "low", "status": "optimal", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "low", "status": "partial", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.297670754546532, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "low", "status": "not-implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "high", "status": "implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "high", "status": "implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "high", "status": "partial", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 251441.76858032952}}, {"input": {"complianceData": {"complianceScore": 0.6499191952284595, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "low", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "high", "status": "partial", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "medium", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "high", "status": "partial", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.3225516380579543, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "low", "status": "optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "high", "status": "partial", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "low", "status": "non-optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "medium", "status": "partial", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "high", "status": "non-optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.36957069714589297, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "high", "status": "not-implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "high", "status": "not-implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "high", "status": "not-implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "medium", "status": "partial", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "low", "status": "partial", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 764736.5218513386}}, {"input": {"complianceData": {"complianceScore": 0.39429111727990773, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "high", "status": "partial", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "medium", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "low", "status": "partial", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.340103218086933, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "medium", "status": "partial", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "low", "status": "partial", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "low", "status": "non-optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "high", "status": "partial", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "medium", "status": "partial", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.7291144663042814, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "high", "status": "implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "high", "status": "partial", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "low", "status": "not-implemented", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 965115.1570424734}}, {"input": {"complianceData": {"complianceScore": 0.5245671756923611, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "high", "status": "partial", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "high", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "low", "status": "partial", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "high", "status": "non-compliant", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.46454842286392317, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "low", "status": "non-optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "low", "status": "non-optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "low", "status": "non-optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "medium", "status": "partial", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "high", "status": "optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.1196373054877995, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "high", "status": "implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "medium", "status": "implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "low", "status": "implemented", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "high", "status": "not-implemented", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 287775.89364205615}}, {"input": {"complianceData": {"complianceScore": 0.6111230125721867, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "low", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "low", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "medium", "status": "compliant", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.8587329405281914, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "medium", "status": "partial", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "medium", "status": "optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "low", "status": "optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "high", "status": "non-optimal", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "low", "status": "optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.2831378297084679, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "high", "status": "not-implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "low", "status": "implemented", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 1466696.8045607358}}, {"input": {"complianceData": {"complianceScore": 0.39760794983216785, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "medium", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "medium", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "high", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "low", "status": "compliant", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.23345646851759683, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "medium", "status": "partial", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "high", "status": "partial", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "high", "status": "partial", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "medium", "status": "partial", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "high", "status": "non-optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.2006757872087941, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "low", "status": "partial", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "medium", "status": "implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "high", "status": "not-implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "low", "status": "implemented", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "high", "status": "partial", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 183870.32034418976}}, {"input": {"complianceData": {"complianceScore": 0.7667568667997278, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "medium", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "high", "status": "partial", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "medium", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "medium", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "low", "status": "compliant", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.3105296309103096, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "low", "status": "non-optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "high", "status": "partial", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "medium", "status": "optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "high", "status": "partial", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "low", "status": "partial", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.09248738429912806, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "low", "status": "not-implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "medium", "status": "implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "low", "status": "implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "low", "status": "partial", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "low", "status": "partial", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 217369.64933693607}}, {"input": {"complianceData": {"complianceScore": 0.5514548610872352, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "high", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "high", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "medium", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "low", "status": "partial", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.4523899900661561, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "low", "status": "non-optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "medium", "status": "non-optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "low", "status": "non-optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "medium", "status": "partial", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "medium", "status": "partial", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.75860164135458, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "medium", "status": "implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "low", "status": "implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "low", "status": "implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "medium", "status": "partial", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "medium", "status": "partial", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 1868066.8806433212}}, {"input": {"complianceData": {"complianceScore": 0.13789579454210976, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "low", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "medium", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "high", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "high", "status": "compliant", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.8385338380057015, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "low", "status": "optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "medium", "status": "optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "high", "status": "optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "low", "status": "optimal", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "high", "status": "partial", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.31188608373648363, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "medium", "status": "partial", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "high", "status": "not-implemented", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "medium", "status": "partial", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 355978.11410127365}}, {"input": {"complianceData": {"complianceScore": 0.20215173615688697, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "low", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "high", "status": "partial", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "high", "status": "partial", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "low", "status": "partial", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.9435476619922802, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "high", "status": "non-optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "high", "status": "non-optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "low", "status": "partial", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "medium", "status": "optimal", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "high", "status": "optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.7255877174241163, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "low", "status": "not-implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "low", "status": "not-implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "medium", "status": "partial", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "low", "status": "implemented", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "medium", "status": "implemented", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 1366113.9541539073}}, {"input": {"complianceData": {"complianceScore": 0.8241709789237324, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "low", "status": "partial", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "high", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "high", "status": "compliant", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.46301977660688043, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "high", "status": "optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "low", "status": "optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "medium", "status": "optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "high", "status": "optimal", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "medium", "status": "non-optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.48838467268502495, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "high", "status": "partial", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "low", "status": "not-implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "medium", "status": "implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "low", "status": "implemented", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 1839647.3164635415}}, {"input": {"complianceData": {"complianceScore": 0.1701762983099695, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "low", "status": "partial", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "high", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "low", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "high", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.8996102314978334, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "low", "status": "non-optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "medium", "status": "partial", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "medium", "status": "partial", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "low", "status": "non-optimal", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "low", "status": "non-optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.7177927037642682, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "high", "status": "partial", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "low", "status": "not-implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "medium", "status": "partial", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 1084696.365095448}}, {"input": {"complianceData": {"complianceScore": 0.3275581816197777, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "low", "status": "partial", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "medium", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "low", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "high", "status": "partial", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "high", "status": "partial", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.8838239840414421, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "medium", "status": "partial", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "high", "status": "partial", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "high", "status": "optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "low", "status": "non-optimal", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "medium", "status": "non-optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.6636438043970894, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "medium", "status": "implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "medium", "status": "partial", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "high", "status": "implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "high", "status": "not-implemented", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 1896465.580999606}}, {"input": {"complianceData": {"complianceScore": 0.5229564106675326, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "high", "status": "partial", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "high", "status": "partial", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.6582432615170999, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "high", "status": "optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "low", "status": "partial", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "low", "status": "non-optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "low", "status": "partial", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "low", "status": "optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.9437914810477912, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "low", "status": "not-implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "low", "status": "partial", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "high", "status": "implemented", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "low", "status": "implemented", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 3206886.966569747}}, {"input": {"complianceData": {"complianceScore": 0.08356286060612117, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "high", "status": "partial", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "low", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "medium", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "low", "status": "compliant", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.13940438342961947, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "medium", "status": "partial", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "medium", "status": "optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "high", "status": "non-optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "high", "status": "non-optimal", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "high", "status": "optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.4400967916744525, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "high", "status": "implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "low", "status": "partial", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "low", "status": "partial", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "low", "status": "implemented", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 50605.02197666905}}, {"input": {"complianceData": {"complianceScore": 0.9548086989387721, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "low", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "low", "status": "partial", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.44934733134844596, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "low", "status": "optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "low", "status": "optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "medium", "status": "optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "low", "status": "partial", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "low", "status": "optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.4797266353802636, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "medium", "status": "partial", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "low", "status": "not-implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "low", "status": "not-implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "high", "status": "partial", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 2031646.07012341}}, {"input": {"complianceData": {"complianceScore": 0.8413480408892497, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "medium", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "high", "status": "partial", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.8274608821078744, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "medium", "status": "non-optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "low", "status": "optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "medium", "status": "non-optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "low", "status": "optimal", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "medium", "status": "optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.32891323337533374, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "low", "status": "partial", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "medium", "status": "implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "low", "status": "not-implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "low", "status": "partial", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "high", "status": "implemented", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 2260269.336343631}}, {"input": {"complianceData": {"complianceScore": 0.2628213733749376, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "low", "status": "partial", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "low", "status": "partial", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "high", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "high", "status": "non-compliant", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.31373855204242473, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "low", "status": "non-optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "high", "status": "partial", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "low", "status": "non-optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "medium", "status": "non-optimal", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "high", "status": "non-optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.6618772988955239, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "medium", "status": "implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "high", "status": "partial", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "low", "status": "not-implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "medium", "status": "implemented", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "high", "status": "partial", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 538718.3150487404}}, {"input": {"complianceData": {"complianceScore": 0.0020559187135913426, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "high", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "medium", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "medium", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "high", "status": "non-compliant", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.969487232885383, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "medium", "status": "partial", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "medium", "status": "non-optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "low", "status": "partial", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "medium", "status": "partial", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "low", "status": "optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.00878328835985931, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "low", "status": "not-implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "medium", "status": "implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "high", "status": "partial", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "high", "status": "implemented", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "low", "status": "partial", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 172.80681331130188}}, {"input": {"complianceData": {"complianceScore": 0.18491015979414804, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "medium", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "high", "status": "partial", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "medium", "status": "compliant", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.29557418473507613, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "low", "status": "partial", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "high", "status": "partial", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "high", "status": "optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "low", "status": "non-optimal", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "low", "status": "partial", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.541813442338146, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "high", "status": "not-implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "low", "status": "not-implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "high", "status": "not-implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "low", "status": "not-implemented", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 292302.6391009761}}, {"input": {"complianceData": {"complianceScore": 0.5518942036578183, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "high", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "low", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "medium", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "high", "status": "partial", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.21937173310759994, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "high", "status": "non-optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "medium", "status": "partial", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "medium", "status": "partial", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "low", "status": "non-optimal", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "medium", "status": "non-optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.22001899995599694, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "medium", "status": "partial", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "high", "status": "not-implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "high", "status": "partial", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "high", "status": "implemented", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 262937.4047964}}, {"input": {"complianceData": {"complianceScore": 0.3565352368011421, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "medium", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "high", "status": "partial", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "low", "status": "partial", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "medium", "status": "compliant", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.1755155077134123, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "low", "status": "optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "medium", "status": "partial", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "medium", "status": "optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "high", "status": "non-optimal", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "low", "status": "optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.3252113337276279, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "low", "status": "not-implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "high", "status": "not-implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "low", "status": "implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "low", "status": "partial", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "low", "status": "implemented", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 200881.20825679397}}, {"input": {"complianceData": {"complianceScore": 0.9285222763628633, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "high", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "high", "status": "partial", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "low", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "high", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.28702531839488876, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "low", "status": "partial", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "low", "status": "non-optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "low", "status": "optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "low", "status": "non-optimal", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "high", "status": "partial", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.8979600314215215, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "low", "status": "implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "low", "status": "partial", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "low", "status": "not-implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "high", "status": "implemented", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "high", "status": "partial", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 2362246.574305783}}, {"input": {"complianceData": {"complianceScore": 0.5953656421648454, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "low", "status": "partial", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "medium", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "low", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.9833131483967428, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "high", "status": "partial", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "high", "status": "non-optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "low", "status": "non-optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "high", "status": "non-optimal", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "low", "status": "non-optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.17456392340798543, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "medium", "status": "partial", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "low", "status": "partial", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "medium", "status": "partial", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "medium", "status": "partial", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "high", "status": "implemented", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 1008755.2214427188}}, {"input": {"complianceData": {"complianceScore": 0.957026910019876, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "high", "status": "partial", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "high", "status": "partial", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "high", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "high", "status": "compliant", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.5386076630288303, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "medium", "status": "non-optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "low", "status": "optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "medium", "status": "non-optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "low", "status": "optimal", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "medium", "status": "partial", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.15977248024268653, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "high", "status": "partial", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "medium", "status": "implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "low", "status": "partial", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "medium", "status": "implemented", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "high", "status": "partial", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 812932.2282306493}}, {"input": {"complianceData": {"complianceScore": 0.49244384603973823, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "medium", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "high", "status": "partial", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "high", "status": "partial", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.14817921665656297, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "medium", "status": "optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "high", "status": "non-optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "low", "status": "partial", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "medium", "status": "non-optimal", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "medium", "status": "non-optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.8535764444224225, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "low", "status": "implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "high", "status": "implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "low", "status": "partial", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 614811.6910796439}}, {"input": {"complianceData": {"complianceScore": 0.023862815880494104, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "low", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "medium", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "low", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "medium", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.7154039133353276, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "high", "status": "partial", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "low", "status": "optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "low", "status": "optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "medium", "status": "partial", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "medium", "status": "optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.429893543408727, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "low", "status": "implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "high", "status": "not-implemented", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "low", "status": "implemented", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 72441.86303912134}}, {"input": {"complianceData": {"complianceScore": 0.7051718883380993, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "high", "status": "partial", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "high", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "low", "status": "partial", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.8375588297699035, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "medium", "status": "optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "medium", "status": "non-optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "low", "status": "optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "high", "status": "non-optimal", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "medium", "status": "non-optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.4960051137705217, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "medium", "status": "implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "high", "status": "implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "medium", "status": "partial", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "low", "status": "partial", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "low", "status": "implemented", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 2891692.794942391}}, {"input": {"complianceData": {"complianceScore": 0.48021729147071324, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "low", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "medium", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "medium", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "low", "status": "compliant", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.4356128447734915, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "medium", "status": "partial", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "high", "status": "non-optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "low", "status": "non-optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "high", "status": "partial", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "low", "status": "partial", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.31074472234161377, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "high", "status": "implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "low", "status": "partial", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "high", "status": "partial", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "high", "status": "partial", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "high", "status": "partial", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 641649.5869022373}}, {"input": {"complianceData": {"complianceScore": 0.7863895760054922, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "high", "status": "partial", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "low", "status": "compliant", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.0031661458741727433, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "medium", "status": "partial", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "low", "status": "optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "low", "status": "partial", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "low", "status": "partial", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "low", "status": "non-optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.33970384065145476, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "medium", "status": "partial", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "medium", "status": "implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "low", "status": "not-implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "medium", "status": "partial", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "high", "status": "not-implemented", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 8348.814503908923}}, {"input": {"complianceData": {"complianceScore": 0.5174805217319802, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "medium", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "high", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "medium", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "medium", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "high", "status": "partial", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.7337839310978178, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "high", "status": "partial", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "low", "status": "non-optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "high", "status": "non-optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "low", "status": "non-optimal", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "low", "status": "optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.7981777545299804, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "medium", "status": "implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "low", "status": "implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "low", "status": "partial", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "low", "status": "not-implemented", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "high", "status": "partial", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 2991696.343523214}}, {"input": {"complianceData": {"complianceScore": 0.3211480826173041, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "medium", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "low", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.12892169537390807, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "medium", "status": "partial", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "low", "status": "optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "low", "status": "partial", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "high", "status": "optimal", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "medium", "status": "optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.07689813713992, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "high", "status": "not-implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "high", "status": "partial", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "medium", "status": "implemented", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "low", "status": "partial", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 31426.994328927838}}, {"input": {"complianceData": {"complianceScore": 0.08589242283866971, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "low", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "low", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "high", "status": "partial", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.4739765020580087, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "medium", "status": "non-optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "low", "status": "optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "low", "status": "partial", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "high", "status": "partial", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "high", "status": "partial", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.6166303120661436, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "low", "status": "not-implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "low", "status": "not-implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "high", "status": "not-implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "medium", "status": "implemented", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "high", "status": "implemented", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 247794.8187722995}}, {"input": {"complianceData": {"complianceScore": 0.4829082811259313, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "medium", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "low", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "high", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "high", "status": "partial", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "medium", "status": "non-compliant", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.9662101462859072, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "low", "status": "optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "high", "status": "partial", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "low", "status": "non-optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "high", "status": "optimal", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "high", "status": "optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.2957048996354037, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "high", "status": "not-implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "low", "status": "implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "low", "status": "partial", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "low", "status": "implemented", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "low", "status": "implemented", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 1361916.4131459298}}, {"input": {"complianceData": {"complianceScore": 0.9503754057597897, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "high", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "medium", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.7999876451321748, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "medium", "status": "partial", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "medium", "status": "optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "low", "status": "partial", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "medium", "status": "partial", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "high", "status": "partial", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.6550553581611154, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "low", "status": "not-implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "low", "status": "partial", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "high", "status": "partial", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "medium", "status": "partial", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "high", "status": "partial", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 4916003.220808473}}, {"input": {"complianceData": {"complianceScore": 0.5462213306139989, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "medium", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "medium", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "high", "status": "non-compliant", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.2972456947117508, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "medium", "status": "non-optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "high", "status": "partial", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "medium", "status": "non-optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "low", "status": "partial", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "medium", "status": "optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.7608470120948292, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "high", "status": "partial", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "low", "status": "partial", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "high", "status": "not-implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "high", "status": "partial", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "low", "status": "not-implemented", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 1219374.910670544}}, {"input": {"complianceData": {"complianceScore": 0.03838345629935591, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "low", "status": "partial", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "medium", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "low", "status": "partial", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.09853053508809029, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "high", "status": "partial", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "medium", "status": "partial", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "high", "status": "optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "high", "status": "non-optimal", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "medium", "status": "partial", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.018241031016002163, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "low", "status": "not-implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "high", "status": "implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "medium", "status": "partial", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "low", "status": "partial", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 680.9574702847591}}, {"input": {"complianceData": {"complianceScore": 0.48147942899223817, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "low", "status": "partial", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "medium", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "low", "status": "partial", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "low", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "low", "status": "partial", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.536887319375186, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "medium", "status": "partial", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "medium", "status": "partial", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "medium", "status": "partial", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "low", "status": "partial", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "medium", "status": "partial", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.03309284755396358, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "high", "status": "implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "low", "status": "not-implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "high", "status": "partial", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "medium", "status": "partial", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 84440.48296586282}}, {"input": {"complianceData": {"complianceScore": 0.04146720714102625, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "low", "status": "partial", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "low", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "high", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "low", "status": "compliant", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.34282067273413497, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "medium", "status": "non-optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "medium", "status": "partial", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "low", "status": "optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "low", "status": "optimal", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "medium", "status": "optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.34192140073199995, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "low", "status": "not-implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "high", "status": "implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "low", "status": "partial", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "medium", "status": "implemented", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 47979.2836544248}}, {"input": {"complianceData": {"complianceScore": 0.5176632920016178, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "medium", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "high", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "high", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "medium", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "low", "status": "partial", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.48278284687788053, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "low", "status": "non-optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "medium", "status": "non-optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "medium", "status": "non-optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "medium", "status": "partial", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "medium", "status": "optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.423824938281125, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "low", "status": "implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "high", "status": "not-implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "low", "status": "implemented", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "medium", "status": "partial", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 1045541.7877799314}}, {"input": {"complianceData": {"complianceScore": 0.30180060213353377, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "medium", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "high", "status": "partial", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "high", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "high", "status": "compliant", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.801544034543862, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "high", "status": "non-optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "low", "status": "non-optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "high", "status": "partial", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "medium", "status": "non-optimal", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "medium", "status": "optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.5618354256963123, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "medium", "status": "implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "medium", "status": "partial", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "low", "status": "partial", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "low", "status": "implemented", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "medium", "status": "partial", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 1341566.7755462716}}, {"input": {"complianceData": {"complianceScore": 0.4040486372854126, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "low", "status": "partial", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "low", "status": "partial", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "high", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "low", "status": "partial", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "high", "status": "partial", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.358911048271932, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "low", "status": "partial", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "high", "status": "non-optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "high", "status": "partial", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "high", "status": "optimal", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "high", "status": "non-optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.18872052546972262, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "low", "status": "not-implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "medium", "status": "implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "low", "status": "partial", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "medium", "status": "implemented", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 270143.98211607395}}, {"input": {"complianceData": {"complianceScore": 0.8682040723814288, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "high", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "high", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "low", "status": "partial", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.38041830179923775, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "medium", "status": "non-optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "high", "status": "non-optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "medium", "status": "partial", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "high", "status": "partial", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "medium", "status": "non-optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.5019993713556126, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "high", "status": "not-implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "high", "status": "not-implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "low", "status": "implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "low", "status": "partial", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "high", "status": "implemented", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 1636598.2444675702}}, {"input": {"complianceData": {"complianceScore": 0.476760802052546, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "high", "status": "partial", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "high", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "low", "status": "partial", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.2378629232976568, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "medium", "status": "non-optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "medium", "status": "optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "high", "status": "partial", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "high", "status": "partial", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "low", "status": "partial", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.6347022901324655, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "medium", "status": "partial", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "low", "status": "partial", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "low", "status": "implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "low", "status": "not-implemented", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "low", "status": "partial", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 710481.944408905}}, {"input": {"complianceData": {"complianceScore": 0.9750052907862417, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "medium", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "high", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "medium", "status": "non-compliant", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.5439951861912358, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "high", "status": "non-optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "medium", "status": "partial", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "low", "status": "partial", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "high", "status": "partial", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "low", "status": "partial", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.94706649601355, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "medium", "status": "implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "high", "status": "partial", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "medium", "status": "implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "medium", "status": "implemented", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 4958361.521077703}}, {"input": {"complianceData": {"complianceScore": 0.6562048419122233, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "high", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "high", "status": "partial", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "medium", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "medium", "status": "compliant", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.09267125486348382, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "low", "status": "non-optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "medium", "status": "optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "medium", "status": "non-optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "medium", "status": "optimal", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "medium", "status": "partial", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.03150851885692152, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "medium", "status": "implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "low", "status": "not-implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "high", "status": "partial", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "high", "status": "not-implemented", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "low", "status": "partial", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 18913.336500169644}}, {"input": {"complianceData": {"complianceScore": 0.2775612894003392, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "high", "status": "partial", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "high", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "high", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.17250758806571365, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "high", "status": "optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "low", "status": "non-optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "low", "status": "partial", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "high", "status": "partial", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "low", "status": "optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.7356744632903689, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "medium", "status": "partial", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "high", "status": "not-implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "low", "status": "implemented", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "high", "status": "partial", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 347703.0234058346}}, {"input": {"complianceData": {"complianceScore": 0.9289214229666083, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "low", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "high", "status": "partial", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "medium", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "low", "status": "partial", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.5823892064822844, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "medium", "status": "optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "medium", "status": "non-optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "low", "status": "optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "medium", "status": "non-optimal", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "high", "status": "optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.5822394756702989, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "low", "status": "partial", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "low", "status": "not-implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "high", "status": "not-implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "low", "status": "not-implemented", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "low", "status": "implemented", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 3109206.951438726}}, {"input": {"complianceData": {"complianceScore": 0.6522763804665928, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "high", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "high", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "high", "status": "partial", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "medium", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.3633208209034964, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "medium", "status": "optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "high", "status": "non-optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "high", "status": "partial", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "high", "status": "partial", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "medium", "status": "optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.8444152098696507, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "high", "status": "implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "low", "status": "not-implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "high", "status": "partial", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "low", "status": "implemented", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "high", "status": "not-implemented", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 1975302.7724915342}}, {"input": {"complianceData": {"complianceScore": 0.27731237019039634, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "low", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "medium", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "medium", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.5735869212348232, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "high", "status": "optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "medium", "status": "non-optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "medium", "status": "optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "high", "status": "partial", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "medium", "status": "non-optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.7592330103149141, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "low", "status": "partial", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "medium", "status": "partial", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "high", "status": "implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "low", "status": "not-implemented", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 1192063.1193162105}}, {"input": {"complianceData": {"complianceScore": 0.6934078027486326, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "medium", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "medium", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "low", "status": "partial", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "high", "status": "non-compliant", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.14174237887104568, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "low", "status": "optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "medium", "status": "non-optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "low", "status": "partial", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "low", "status": "non-optimal", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "medium", "status": "non-optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.8582322798829858, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "medium", "status": "implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "medium", "status": "implemented", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "high", "status": "implemented", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 832624.0926881059}}, {"input": {"complianceData": {"complianceScore": 0.5613464825537477, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "medium", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "low", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "low", "status": "partial", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "medium", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "high", "status": "compliant", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.40516751890850133, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "high", "status": "optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "medium", "status": "optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "low", "status": "non-optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "high", "status": "optimal", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "low", "status": "non-optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.9227000800771166, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "high", "status": "partial", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "low", "status": "not-implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "medium", "status": "partial", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "low", "status": "partial", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "medium", "status": "implemented", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 2071485.3799533101}}, {"input": {"complianceData": {"complianceScore": 0.8388660969294237, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "medium", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "high", "status": "partial", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.6034844069715168, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "low", "status": "partial", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "medium", "status": "optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "medium", "status": "non-optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "low", "status": "optimal", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "low", "status": "optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.6116662101959938, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "low", "status": "partial", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "low", "status": "implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "high", "status": "not-implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "high", "status": "not-implemented", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 3056531.472910404}}, {"input": {"complianceData": {"complianceScore": 0.9562433049120367, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "medium", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "high", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.6048453363401813, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "low", "status": "non-optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "low", "status": "partial", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "high", "status": "non-optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "medium", "status": "partial", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "low", "status": "optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.6849384920911978, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "low", "status": "implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "low", "status": "partial", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "low", "status": "partial", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "high", "status": "implemented", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "low", "status": "not-implemented", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 3910389.370894342}}, {"input": {"complianceData": {"complianceScore": 0.8243031077776284, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "medium", "status": "non-compliant", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.6085253985500239, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "low", "status": "partial", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "low", "status": "non-optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "low", "status": "non-optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "low", "status": "optimal", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "low", "status": "partial", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.34222559342050496, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "high", "status": "partial", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "medium", "status": "implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "medium", "status": "partial", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "medium", "status": "partial", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 1694469.7435671024}}, {"input": {"complianceData": {"complianceScore": 0.30110863879814165, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "low", "status": "partial", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "medium", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "medium", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "medium", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "low", "status": "partial", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.739112209223739, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "low", "status": "optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "low", "status": "optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "high", "status": "non-optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "low", "status": "non-optimal", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "high", "status": "non-optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.4874647749548324, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "high", "status": "not-implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "high", "status": "not-implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "low", "status": "not-implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "high", "status": "partial", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "medium", "status": "implemented", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 1070859.5566599162}}, {"input": {"complianceData": {"complianceScore": 0.26926326122387634, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "high", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "high", "status": "partial", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "medium", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "high", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "medium", "status": "non-compliant", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.4748500195252523, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "high", "status": "non-optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "high", "status": "partial", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "low", "status": "partial", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "high", "status": "partial", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "high", "status": "partial", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.15175367593040123, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "low", "status": "partial", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "medium", "status": "partial", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "medium", "status": "partial", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "low", "status": "not-implemented", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "low", "status": "partial", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 191526.32171465704}}, {"input": {"complianceData": {"complianceScore": 0.6963670038173309, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "low", "status": "partial", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "medium", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "medium", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "high", "status": "non-compliant", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.14746853759278133, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "medium", "status": "optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "medium", "status": "optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "low", "status": "partial", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "medium", "status": "non-optimal", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "low", "status": "partial", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.09490276232075301, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "low", "status": "not-implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "low", "status": "not-implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "low", "status": "not-implemented", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "medium", "status": "implemented", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 96199.34127663096}}, {"input": {"complianceData": {"complianceScore": 0.5805761239330722, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "low", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "low", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "medium", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "low", "status": "partial", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.3767468374536058, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "low", "status": "non-optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "high", "status": "partial", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "low", "status": "optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "low", "status": "non-optimal", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "low", "status": "non-optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.10076621336417779, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "low", "status": "partial", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "medium", "status": "partial", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "high", "status": "not-implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "high", "status": "not-implemented", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "high", "status": "implemented", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 217560.18142591353}}, {"input": {"complianceData": {"complianceScore": 0.29697900453679016, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "low", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "high", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "high", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "low", "status": "partial", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "low", "status": "partial", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.681492062960948, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "low", "status": "optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "low", "status": "optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "low", "status": "non-optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "high", "status": "optimal", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "high", "status": "non-optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.16371497826633163, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "low", "status": "not-implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "high", "status": "implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "low", "status": "implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "low", "status": "implemented", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "medium", "status": "implemented", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 327062.42364144226}}, {"input": {"complianceData": {"complianceScore": 0.6766224916443948, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "high", "status": "partial", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "low", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.44363226651862453, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "low", "status": "optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "high", "status": "non-optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "low", "status": "optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "high", "status": "optimal", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "high", "status": "partial", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.015362292132244937, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "low", "status": "not-implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "medium", "status": "implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "high", "status": "not-implemented", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "high", "status": "implemented", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 45517.79988195797}}, {"input": {"complianceData": {"complianceScore": 0.5044441125164798, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "high", "status": "partial", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "high", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "medium", "status": "compliant", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.04243260888464384, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "medium", "status": "partial", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "low", "status": "optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "high", "status": "partial", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "high", "status": "non-optimal", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "high", "status": "optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.23612561151497613, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "medium", "status": "implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "low", "status": "implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "low", "status": "not-implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "low", "status": "partial", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "high", "status": "implemented", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 49889.77831939238}}, {"input": {"complianceData": {"complianceScore": 0.27987877277166495, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "medium", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "low", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "low", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.23681039657075398, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "medium", "status": "optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "high", "status": "optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "low", "status": "partial", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "low", "status": "optimal", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "high", "status": "optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.3108965315595271, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "high", "status": "partial", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "high", "status": "not-implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "low", "status": "not-implemented", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "medium", "status": "partial", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 203395.94461611583}}, {"input": {"complianceData": {"complianceScore": 0.36564166663517095, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "low", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "high", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "high", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "high", "status": "partial", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.23015108862788125, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "low", "status": "non-optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "medium", "status": "non-optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "low", "status": "non-optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "low", "status": "non-optimal", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "medium", "status": "non-optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.6033457481782136, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "medium", "status": "implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "high", "status": "partial", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "low", "status": "implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "medium", "status": "implemented", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "high", "status": "implemented", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 501176.4510407407}}, {"input": {"complianceData": {"complianceScore": 0.8376069024154171, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "high", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "medium", "status": "non-compliant", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.45452969829915824, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "high", "status": "partial", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "medium", "status": "partial", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "low", "status": "partial", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "low", "status": "non-optimal", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "medium", "status": "optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.8630032969052852, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "low", "status": "implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "high", "status": "implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "low", "status": "not-implemented", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "high", "status": "implemented", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 3243177.0162385693}}, {"input": {"complianceData": {"complianceScore": 0.5712587712869237, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "high", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "medium", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "high", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "medium", "status": "non-compliant", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.8103283331352584, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "medium", "status": "non-optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "high", "status": "optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "low", "status": "partial", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "high", "status": "optimal", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "high", "status": "optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.5446492080002703, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "high", "status": "partial", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "low", "status": "partial", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "high", "status": "not-implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "high", "status": "partial", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 2488665.1643987754}}, {"input": {"complianceData": {"complianceScore": 0.522304056876151, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "high", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "high", "status": "partial", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "medium", "status": "compliant", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.38338964125722375, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "low", "status": "non-optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "medium", "status": "partial", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "low", "status": "partial", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "medium", "status": "optimal", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "low", "status": "partial", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.07846057624176317, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "high", "status": "not-implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "medium", "status": "partial", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "medium", "status": "partial", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 155085.41398187453}}, {"input": {"complianceData": {"complianceScore": 0.35979302056036877, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "low", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "high", "status": "partial", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "low", "status": "partial", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.9707006266588432, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "low", "status": "optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "high", "status": "partial", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "medium", "status": "optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "low", "status": "partial", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "medium", "status": "non-optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.6032272429656467, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "medium", "status": "partial", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "low", "status": "not-implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "high", "status": "partial", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "low", "status": "partial", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 2079575.4313301507}}, {"input": {"complianceData": {"complianceScore": 0.015985030648070886, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "high", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "high", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "high", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.1771288427735107, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "high", "status": "non-optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "medium", "status": "optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "low", "status": "optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "high", "status": "non-optimal", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "low", "status": "partial", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.43880610343753657, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "medium", "status": "implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "high", "status": "partial", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "medium", "status": "partial", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "low", "status": "partial", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 12263.970713890641}}, {"input": {"complianceData": {"complianceScore": 0.07688355518611356, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "medium", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "low", "status": "partial", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.34112533602923145, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "medium", "status": "optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "high", "status": "non-optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "high", "status": "non-optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "medium", "status": "partial", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "medium", "status": "optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.305495609963651, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "high", "status": "implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "high", "status": "partial", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "medium", "status": "implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "low", "status": "partial", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "medium", "status": "implemented", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 79087.5449283393}}, {"input": {"complianceData": {"complianceScore": 0.1994845169521806, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "medium", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "low", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "medium", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "low", "status": "compliant", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.15180426370022815, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "high", "status": "partial", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "low", "status": "non-optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "low", "status": "non-optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "high", "status": "partial", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "medium", "status": "non-optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.9866175669753472, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "high", "status": "partial", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "high", "status": "not-implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "low", "status": "partial", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "high", "status": "partial", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 294915.56454973423}}, {"input": {"complianceData": {"complianceScore": 0.021790277638083166, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "low", "status": "partial", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "medium", "status": "partial", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "low", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "medium", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "high", "status": "compliant", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.7735634560247355, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "low", "status": "non-optimal", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "low", "status": "non-optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "low", "status": "non-optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "medium", "status": "partial", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "low", "status": "optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.012324332123039072, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "medium", "status": "implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "low", "status": "not-implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "high", "status": "not-implemented", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "low", "status": "partial", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 2050.585059475113}}, {"input": {"complianceData": {"complianceScore": 0.8356380926964062, "controls": [{"id": "C1", "name": "Control C1", "description": "Description for control C1", "severity": "medium", "status": "non-compliant", "framework": "NIST 800-53"}, {"id": "C2", "name": "Control C2", "description": "Description for control C2", "severity": "medium", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C3", "name": "Control C3", "description": "Description for control C3", "severity": "low", "status": "partial", "framework": "NIST 800-53"}, {"id": "C4", "name": "Control C4", "description": "Description for control C4", "severity": "high", "status": "compliant", "framework": "NIST 800-53"}, {"id": "C5", "name": "Control C5", "description": "Description for control C5", "severity": "low", "status": "compliant", "framework": "NIST 800-53"}]}, "gcpData": {"integrationScore": 0.861274387805248, "services": [{"id": "S1", "name": "Service S1", "description": "Description for service S1", "severity": "low", "status": "partial", "service": "GCP Service 1"}, {"id": "S2", "name": "Service S2", "description": "Description for service S2", "severity": "high", "status": "optimal", "service": "GCP Service 2"}, {"id": "S3", "name": "Service S3", "description": "Description for service S3", "severity": "medium", "status": "optimal", "service": "GCP Service 3"}, {"id": "S4", "name": "Service S4", "description": "Description for service S4", "severity": "high", "status": "partial", "service": "GCP Service 4"}, {"id": "S5", "name": "Service S5", "description": "Description for service S5", "severity": "low", "status": "non-optimal", "service": "GCP Service 5"}]}, "cyberSafetyData": {"safetyScore": 0.6948906013108509, "controls": [{"id": "CS1", "name": "Cyber-Safety Control CS1", "description": "Description for Cyber-Safety control CS1", "severity": "low", "status": "not-implemented", "pillar": "Pillar 1"}, {"id": "CS2", "name": "Cyber-Safety Control CS2", "description": "Description for Cyber-Safety control CS2", "severity": "low", "status": "not-implemented", "pillar": "Pillar 2"}, {"id": "CS3", "name": "Cyber-Safety Control CS3", "description": "Description for Cyber-Safety control CS3", "severity": "medium", "status": "partial", "pillar": "Pillar 3"}, {"id": "CS4", "name": "Cyber-Safety Control CS4", "description": "Description for Cyber-Safety control CS4", "severity": "low", "status": "partial", "pillar": "Pillar 4"}, {"id": "CS5", "name": "Cyber-Safety Control CS5", "description": "Description for Cyber-Safety control CS5", "severity": "high", "status": "not-implemented", "pillar": "Pillar 5"}]}}, "output": {"csdeValue": 4936644.866460465}}]