{"sourceRoot": "D:\\novafuse-api-superstore", "destinationRoot": "D:\\novafuse-api-superstore", "logFile": "D:\\novafuse-api-superstore\\nova-fuse\\scripts\\logs\\migration-log.txt", "migrationMappings": [{"name": "NovaConnect Migration", "sourcePath": "connector-templates", "destinationPath": "nova-connect", "filter": "*.*", "importPathReplacements": {"from '\\.\\.\\/connector-templates": "from '@nova-connect", "require\\('\\.\\.\\/connector-templates": "require('@nova-connect"}}, {"name": "Privacy Management API Migration", "sourcePath": "nova-marketplace\\apis\\privacy\\management", "destinationPath": "nova-grc-apis\\privacy-management", "filter": "*.*", "importPathReplacements": {"from '\\.\\.\\/\\.\\.\\/\\.\\.\\/\\.\\.\\/common": "from '@nova-grc-apis/shared", "require\\('\\.\\.\\/\\.\\.\\/\\.\\.\\/\\.\\.\\/common": "require('@nova-grc-apis/shared"}}, {"name": "Regulatory Compliance API Migration", "sourcePath": "nova-marketplace\\apis\\compliance", "destinationPath": "nova-grc-apis\\regulatory-compliance", "filter": "*.*", "importPathReplacements": {"from '\\.\\.\\/\\.\\.\\/\\.\\.\\/common": "from '@nova-grc-apis/shared", "require\\('\\.\\.\\/\\.\\.\\/\\.\\.\\/common": "require('@nova-grc-apis/shared"}}, {"name": "Security Assessment API Migration", "sourcePath": "nova-marketplace\\apis\\security\\assessment", "destinationPath": "nova-grc-apis\\security-assessment", "filter": "*.*", "importPathReplacements": {"from '\\.\\.\\/\\.\\.\\/\\.\\.\\/\\.\\.\\/common": "from '@nova-grc-apis/shared", "require\\('\\.\\.\\/\\.\\.\\/\\.\\.\\/\\.\\.\\/common": "require('@nova-grc-apis/shared"}}, {"name": "Control Testing API Migration", "sourcePath": "nova-marketplace\\apis\\control\\testing", "destinationPath": "nova-grc-apis\\control-testing", "filter": "*.*", "importPathReplacements": {"from '\\.\\.\\/\\.\\.\\/\\.\\.\\/\\.\\.\\/common": "from '@nova-grc-apis/shared", "require\\('\\.\\.\\/\\.\\.\\/\\.\\.\\/\\.\\.\\/common": "require('@nova-grc-apis/shared"}}, {"name": "ESG API Migration", "sourcePath": "nova-marketplace\\apis\\esg", "destinationPath": "nova-grc-apis\\esg", "filter": "*.*", "importPathReplacements": {"from '\\.\\.\\/\\.\\.\\/\\.\\.\\/common": "from '@nova-grc-apis/shared", "require\\('\\.\\.\\/\\.\\.\\/\\.\\.\\/common": "require('@nova-grc-apis/shared"}}, {"name": "Compliance Automation API Migration", "sourcePath": "nova-marketplace\\apis\\compliance\\automation", "destinationPath": "nova-grc-apis\\compliance-automation", "filter": "*.*", "importPathReplacements": {"from '\\.\\.\\/\\.\\.\\/\\.\\.\\/\\.\\.\\/common": "from '@nova-grc-apis/shared", "require\\('\\.\\.\\/\\.\\.\\/\\.\\.\\/\\.\\.\\/common": "require('@nova-grc-apis/shared"}}, {"name": "Common Utilities Migration", "sourcePath": "nova-marketplace\\common", "destinationPath": "nova-grc-apis\\shared", "filter": "*.*", "importPathReplacements": {"from '\\.\\.\\/common": "from '@nova-grc-apis/shared", "require\\('\\.\\.\\/common": "require('@nova-grc-apis/shared"}}, {"name": "API Gateway Migration", "sourcePath": "api-gateway", "destinationPath": "nova-gateway", "filter": "*.*", "importPathReplacements": {"from '\\.\\.\\/api-gateway": "from '@nova-gateway", "require\\('\\.\\.\\/api-gateway": "require('@nova-gateway"}}, {"name": "UI Components Migration", "sourcePath": "marketplace-ui\\components", "destinationPath": "nova-ui\\packages\\ui-components", "filter": "*.*", "importPathReplacements": {"from '\\.\\.\\/components": "from '@nova-ui/ui-components", "import \\{ (.*) \\} from '\\.\\.\\/utils": "import { $1 } from '@nova-ui/utils"}}, {"name": "UI Utils Migration", "sourcePath": "marketplace-ui\\utils", "destinationPath": "nova-ui\\packages\\utils", "filter": "*.*", "importPathReplacements": {"from '\\.\\.\\/utils": "from '@nova-ui/utils"}}, {"name": "UI Pages Migration", "sourcePath": "marketplace-ui\\pages", "destinationPath": "nova-ui\\pages", "filter": "*.*", "importPathReplacements": {"from '\\.\\.\\/components": "from '@nova-ui/ui-components", "from '\\.\\.\\/utils": "from '@nova-ui/utils"}}]}