/**
 * Evidence Controller
 * 
 * This controller handles API requests for evidence management.
 */

const evidenceService = require('../services/evidenceService');
const logger = require('../utils/logger');
const multer = require('multer');
const path = require('path');

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, path.join(__dirname, '../../uploads/evidence'));
  },
  filename: function (req, file, cb) {
    cb(null, `${Date.now()}-${file.originalname}`);
  }
});

const upload = multer({ storage });

/**
 * Get all evidence
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
async function getAllEvidence(req, res, next) {
  try {
    const { controlId, testExecutionId, type, search, page = 1, limit = 10 } = req.query;
    
    const filters = {};
    
    if (controlId) {
      filters.controlId = controlId;
    }
    
    if (testExecutionId) {
      filters.testExecutionId = testExecutionId;
    }
    
    if (type) {
      filters.type = type;
    }
    
    if (search) {
      filters.search = search;
    }
    
    const result = await evidenceService.getAllEvidence(
      filters,
      parseInt(page, 10),
      parseInt(limit, 10)
    );
    
    res.json(result);
  } catch (error) {
    logger.error('Error getting evidence', error);
    next(error);
  }
}

/**
 * Get evidence by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
async function getEvidenceById(req, res, next) {
  try {
    const { id } = req.params;
    
    const evidence = await evidenceService.getEvidenceById(id);
    
    res.json(evidence);
  } catch (error) {
    logger.error(`Error getting evidence ${req.params.id}`, error);
    next(error);
  }
}

/**
 * Create evidence
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
async function createEvidence(req, res, next) {
  try {
    const evidenceData = req.body;
    const userId = req.user.id;
    
    let evidence;
    
    if (req.file) {
      // Create evidence from file
      evidence = await evidenceService.createEvidenceFromFile(evidenceData, req.file, userId);
    } else if (evidenceData.content) {
      // Create evidence from content
      evidence = await evidenceService.createEvidenceFromContent(evidenceData, userId);
    } else {
      return res.status(400).json({
        success: false,
        error: 'Either file or content is required'
      });
    }
    
    res.status(201).json(evidence);
  } catch (error) {
    logger.error('Error creating evidence', error);
    next(error);
  }
}

/**
 * Update evidence
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
async function updateEvidence(req, res, next) {
  try {
    const { id } = req.params;
    const evidenceData = req.body;
    const userId = req.user.id;
    
    const evidence = await evidenceService.updateEvidence(id, evidenceData, userId);
    
    res.json(evidence);
  } catch (error) {
    logger.error(`Error updating evidence ${req.params.id}`, error);
    next(error);
  }
}

/**
 * Delete evidence
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
async function deleteEvidence(req, res, next) {
  try {
    const { id } = req.params;
    
    const evidence = await evidenceService.deleteEvidence(id);
    
    res.json({
      success: true,
      message: `Evidence ${id} deleted successfully`,
      evidence
    });
  } catch (error) {
    logger.error(`Error deleting evidence ${req.params.id}`, error);
    next(error);
  }
}

/**
 * Get evidence file
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
async function getEvidenceFile(req, res, next) {
  try {
    const { id } = req.params;
    
    const fileInfo = await evidenceService.getEvidenceFile(id);
    
    res.download(fileInfo.filePath, fileInfo.fileName, {
      headers: {
        'Content-Type': fileInfo.fileType
      }
    });
  } catch (error) {
    logger.error(`Error getting evidence file ${req.params.id}`, error);
    next(error);
  }
}

/**
 * Verify evidence
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
async function verifyEvidence(req, res, next) {
  try {
    const { id } = req.params;
    
    const verification = await evidenceService.verifyEvidence(id);
    
    res.json(verification);
  } catch (error) {
    logger.error(`Error verifying evidence ${req.params.id}`, error);
    next(error);
  }
}

/**
 * Collect API evidence
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
async function collectApiEvidence(req, res, next) {
  try {
    const evidenceData = req.body;
    const userId = req.user.id;
    
    const evidence = await evidenceService.collectApiEvidence(evidenceData, userId);
    
    res.status(201).json(evidence);
  } catch (error) {
    logger.error('Error collecting API evidence', error);
    next(error);
  }
}

/**
 * Collect system evidence
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
async function collectSystemEvidence(req, res, next) {
  try {
    const evidenceData = req.body;
    const userId = req.user.id;
    
    const evidence = await evidenceService.collectSystemEvidence(evidenceData, userId);
    
    res.status(201).json(evidence);
  } catch (error) {
    logger.error('Error collecting system evidence', error);
    next(error);
  }
}

// Middleware for file upload
const uploadMiddleware = upload.single('file');

module.exports = {
  getAllEvidence,
  getEvidenceById,
  createEvidence: [uploadMiddleware, createEvidence],
  updateEvidence,
  deleteEvidence,
  getEvidenceFile,
  verifyEvidence,
  collectApiEvidence,
  collectSystemEvidence
};
