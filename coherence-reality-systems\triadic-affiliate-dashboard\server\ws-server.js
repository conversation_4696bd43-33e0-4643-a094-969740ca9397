const WebSocket = require('ws');
const { performance } = require('perf_hooks');

const wss = new WebSocket.Server({ port: 8080 });

const products = [
  { id: 1, name: 'Product A', price: 99.99, status: 'active', conversions: 0 },
  { id: 2, name: 'Product B', price: 149.99, status: 'active', conversions: 0 },
  { id: 3, name: 'Product C', price: 199.99, status: 'inactive', conversions: 0 }
];

let totalRevenue = 0;
let completedConversions = 0;
let totalConversions = 0;

wss.on('connection', (ws) => {
  console.log('Client connected');

  // Send initial data
  ws.send(JSON.stringify({
    type: 'initial',
    data: {
      products,
      metrics: {
        totalRevenue,
        completedConversions,
        totalConversions
      }
    }
  }));

  // Simulate data updates every 5 seconds
  const interval = setInterval(() => {
    // Simulate a conversion
    const product = products[Math.floor(Math.random() * products.length)];
    if (product.status === 'active') {
      product.conversions++;
      totalRevenue += product.price;
      completedConversions++;
      totalConversions++;
    }

    // Send updated data
    ws.send(JSON.stringify({
      type: 'update',
      data: {
        products,
        metrics: {
          totalRevenue,
          completedConversions,
          totalConversions
        }
      }
    }));
  }, 5000);

  ws.on('close', () => {
    console.log('Client disconnected');
    clearInterval(interval);
  });
});

console.log('WebSocket server is running on ws://localhost:8080');
