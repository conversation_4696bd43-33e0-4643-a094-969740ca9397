/**
 * Simple Schema Definition
 * 
 * This file defines a simplified schema format for the Universal UI Generator.
 */

/**
 * @typedef {Object} Field
 * @property {string} name - Field name
 * @property {string} type - Field type (string, number, boolean, date, password, dropdown, etc.)
 * @property {boolean} [required] - Whether the field is required
 * @property {string[]} [options] - Options for dropdown fields
 * @property {string} [label] - Display label (defaults to formatted field name)
 * @property {string} [placeholder] - Placeholder text
 * @property {string} [description] - Field description
 * @property {boolean} [readOnly] - Whether the field is read-only
 * @property {boolean} [hidden] - Whether the field is hidden
 */

/**
 * @typedef {Object} SimpleSchema
 * @property {string} entity - Entity name
 * @property {string} [entityPlural] - Plural entity name (defaults to entity + 's')
 * @property {string} [apiEndpoint] - API endpoint (defaults to /api/v1/{entity.toLowerCase()})
 * @property {Field[]} fields - Entity fields
 */

// Example schema
const exampleSchema = {
  "entity": "User",
  "entityPlural": "Users",
  "apiEndpoint": "/api/v1/users",
  "fields": [
    {"name": "email", "type": "string", "required": true},
    {"name": "password", "type": "password", "required": true},
    {"name": "role", "type": "dropdown", "options": ["admin", "user"], "required": true}
  ]
};

/**
 * Format field label from field name
 * @param {string} fieldName - Field name
 * @returns {string} - Formatted field label
 */
const formatFieldLabel = (fieldName) => {
  return fieldName
    .replace(/([A-Z])/g, ' $1') // Insert space before capital letters
    .replace(/^./, (str) => str.toUpperCase()); // Capitalize first letter
};

/**
 * Normalize schema by adding default values
 * @param {SimpleSchema} schema - Schema to normalize
 * @returns {SimpleSchema} - Normalized schema
 */
const normalizeSchema = (schema) => {
  const normalized = { ...schema };
  
  // Set default entity plural
  if (!normalized.entityPlural) {
    normalized.entityPlural = `${normalized.entity}s`;
  }
  
  // Set default API endpoint
  if (!normalized.apiEndpoint) {
    normalized.apiEndpoint = `/api/v1/${normalized.entity.toLowerCase()}s`;
  }
  
  // Normalize fields
  normalized.fields = normalized.fields.map(field => {
    const normalizedField = { ...field };
    
    // Set default label
    if (!normalizedField.label) {
      normalizedField.label = formatFieldLabel(normalizedField.name);
    }
    
    return normalizedField;
  });
  
  return normalized;
};

module.exports = {
  exampleSchema,
  normalizeSchema,
  formatFieldLabel
};
