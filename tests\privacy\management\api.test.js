const request = require('supertest');
const express = require('express');
const router = require('../../../apis/privacy/management/routes');

// Mock data
jest.mock('../../../apis/privacy/management/models', () => ({
  dataProcessingActivities: [
    {
      id: 'dpa-0001',
      name: 'Customer Data Processing',
      description: 'Processing of customer personal data for account management and service delivery',
      purpose: 'Account management and service delivery',
      dataCategories: ['Contact information', 'Account details', 'Transaction history'],
      dataSubjects: ['Customers'],
      legalBasis: 'contract',
      retentionPeriod: '7 years after account closure',
      processingOperations: ['Collection', 'Storage', 'Use', 'Disclosure to service providers'],
      crossBorderTransfers: [
        {
          country: 'United States',
          mechanism: 'Standard Contractual Clauses',
          adequacyDecision: false
        }
      ],
      securityMeasures: ['Encryption', 'Access controls', 'Regular security assessments'],
      dataControllers: ['Marketing Department', 'Customer Service'],
      dataProcessors: ['Cloud Storage Provider', 'Email Service Provider'],
      dpia: {
        required: true,
        completed: true,
        completionDate: '2023-01-15',
        reviewDate: '2024-01-15'
      },
      risks: [
        {
          description: 'Unauthorized access to customer data',
          likelihood: 'low',
          impact: 'high',
          mitigations: 'Strong access controls and encryption'
        }
      ],
      status: 'active',
      createdAt: '2023-01-10T09:00:00Z',
      updatedAt: '2023-06-15T11:00:00Z'
    }
  ],
  dataSubjectRequests: [
    {
      id: 'dsr-0001',
      requestType: 'access',
      requestDate: '2023-05-10T14:30:00Z',
      dataSubjectName: 'John Doe',
      dataSubjectEmail: '<EMAIL>',
      dataSubjectId: 'user-123',
      identityVerified: true,
      verificationMethod: 'email verification',
      verificationDate: '2023-05-10T15:00:00Z',
      requestDetails: 'Request for all personal data held about me',
      status: 'completed',
      assignedTo: 'Privacy Team',
      dueDate: '2023-06-09T14:30:00Z',
      completionDate: '2023-05-25T11:45:00Z',
      responseDetails: 'Provided data export via secure link',
      affectedSystems: ['CRM', 'Marketing Database', 'Customer Support System'],
      notes: 'Verified identity through account email and security questions',
      createdAt: '2023-05-10T14:30:00Z',
      updatedAt: '2023-05-25T11:45:00Z'
    }
  ],
  consentRecords: [
    {
      id: 'con-0001',
      dataSubjectId: 'user-123',
      dataSubjectName: 'John Doe',
      dataSubjectEmail: '<EMAIL>',
      consentType: 'marketing',
      consentDescription: 'Consent to receive marketing communications',
      consentGiven: true,
      consentDate: '2023-03-15T10:30:00Z',
      consentExpiryDate: '2024-03-15T10:30:00Z',
      consentProof: 'IP: ***********, User-Agent: Mozilla/5.0...',
      consentVersion: '1.2',
      consentMethod: 'online-form',
      privacyNoticeVersion: '2.0',
      withdrawalDate: null,
      withdrawalMethod: null,
      status: 'active',
      createdAt: '2023-03-15T10:30:00Z',
      updatedAt: '2023-03-15T10:30:00Z'
    }
  ],
  privacyNotices: [
    {
      id: 'pn-0001',
      title: 'Website Privacy Notice',
      version: '2.0',
      effectiveDate: '2023-01-01',
      lastUpdated: '2022-12-15',
      status: 'active',
      audience: 'website-visitors',
      language: 'en',
      format: 'html',
      content: '<h1>Privacy Notice</h1><p>This privacy notice explains how we collect and use your personal data...</p>',
      contentUrl: 'https://example.com/privacy-notice',
      previousVersions: [
        {
          version: '1.0',
          effectiveDate: '2022-01-01',
          retirementDate: '2022-12-31',
          contentUrl: 'https://example.com/privacy-notice-v1'
        }
      ],
      reviewCycle: 'annual',
      nextReviewDate: '2023-12-15',
      approvedBy: 'Legal Department',
      approvalDate: '2022-12-10',
      createdAt: '2022-12-10T09:00:00Z',
      updatedAt: '2022-12-15T14:30:00Z'
    }
  ],
  dataBreaches: [
    {
      id: 'db-0001',
      title: 'Unauthorized Access to Customer Database',
      description: 'Unauthorized access to customer database detected through security monitoring',
      breachType: 'unauthorized-access',
      detectionDate: '2023-07-10T08:30:00Z',
      occurrenceDate: '2023-07-09T23:15:00Z',
      affectedDataCategories: ['Contact information', 'Account details'],
      affectedDataSubjects: ['Customers'],
      approximateSubjectsCount: 1500,
      potentialImpact: 'medium',
      containmentStatus: 'contained',
      containmentDate: '2023-07-10T12:45:00Z',
      containmentMeasures: 'Blocked unauthorized access, reset affected credentials',
      rootCause: 'Compromised employee credentials',
      remedialActions: [
        'Reset all employee credentials',
        'Implemented additional authentication factors',
        'Enhanced monitoring'
      ],
      notificationStatus: {
        authorities: {
          required: true,
          completed: true,
          date: '2023-07-11T10:00:00Z',
          recipient: 'Data Protection Authority'
        },
        dataSubjects: {
          required: true,
          completed: true,
          date: '2023-07-12T15:30:00Z',
          method: 'Email notification'
        }
      },
      investigationStatus: 'completed',
      investigationReport: 'Investigation completed on 2023-07-20. Root cause identified as phishing attack.',
      status: 'closed',
      createdAt: '2023-07-10T08:45:00Z',
      updatedAt: '2023-07-25T14:30:00Z'
    }
  ]
}));

// Create a test Express app
const app = express();
app.use(express.json());
app.use('/privacy/management', router);

describe('Privacy Management API', () => {
  describe('GET /privacy/management/processing-activities', () => {
    it('should return a list of data processing activities', async () => {
      const response = await request(app).get('/privacy/management/processing-activities');
      expect(response.status).toBe(200);
      expect(response.body.data.length).toBeGreaterThan(0);
      expect(response.body.data.some(activity => activity.name === 'Customer Data Processing')).toBe(true);
    });
  });

  describe('GET /privacy/management/processing-activities/:id', () => {
    it('should return a specific data processing activity', async () => {
      const response = await request(app).get('/privacy/management/processing-activities/dpa-0001');
      expect(response.status).toBe(200);
      expect(response.body.data.id).toBe('dpa-0001');
      expect(response.body.data.name).toBe('Customer Data Processing');
    });

    it('should return 404 for non-existent data processing activity', async () => {
      const response = await request(app).get('/privacy/management/processing-activities/non-existent');
      expect(response.status).toBe(404);
    });
  });

  describe('POST /privacy/management/processing-activities', () => {
    it('should create a new data processing activity', async () => {
      const newActivity = {
        name: 'Marketing Data Processing',
        description: 'Processing of customer data for marketing purposes',
        purpose: 'Marketing and promotional communications',
        dataCategories: ['Contact information', 'Marketing preferences'],
        dataSubjects: ['Customers', 'Prospects'],
        legalBasis: 'consent',
        retentionPeriod: '3 years after last interaction',
        processingOperations: ['Collection', 'Storage', 'Use', 'Profiling'],
        status: 'active'
      };

      const response = await request(app)
        .post('/privacy/management/processing-activities')
        .send(newActivity);

      expect(response.status).toBe(201);
      expect(response.body.data.name).toBe(newActivity.name);
      expect(response.body.data.purpose).toBe(newActivity.purpose);
      expect(response.body.data.legalBasis).toBe(newActivity.legalBasis);
      expect(response.body.message).toBe('Data processing activity created successfully');
    });
  });

  describe('PUT /privacy/management/processing-activities/:id', () => {
    it('should update an existing data processing activity', async () => {
      const updatedActivity = {
        name: 'Updated Customer Data Processing',
        status: 'inactive'
      };

      const response = await request(app)
        .put('/privacy/management/processing-activities/dpa-0001')
        .send(updatedActivity);

      expect(response.status).toBe(200);
      expect(response.body.data.name).toBe(updatedActivity.name);
      expect(response.body.data.status).toBe(updatedActivity.status);
      expect(response.body.message).toBe('Data processing activity updated successfully');
    });

    it('should return 404 for non-existent data processing activity', async () => {
      const response = await request(app)
        .put('/privacy/management/processing-activities/non-existent')
        .send({ name: 'Updated Activity' });

      expect(response.status).toBe(404);
    });
  });

  describe('DELETE /privacy/management/processing-activities/:id', () => {
    it('should delete a data processing activity', async () => {
      const response = await request(app).delete('/privacy/management/processing-activities/dpa-0001');
      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Data processing activity deleted successfully');
    });

    it('should return 404 for non-existent data processing activity', async () => {
      const response = await request(app).delete('/privacy/management/processing-activities/non-existent');
      expect(response.status).toBe(404);
    });
  });

  // Data Subject Rights Requests Tests
  describe('GET /privacy/management/subject-requests', () => {
    it('should return a list of data subject requests', async () => {
      const response = await request(app).get('/privacy/management/subject-requests');
      expect(response.status).toBe(200);
      expect(response.body.data.length).toBeGreaterThan(0);
      expect(response.body.data.some(request => request.dataSubjectName === 'John Doe')).toBe(true);
    });
  });

  describe('GET /privacy/management/subject-requests/:id', () => {
    it('should return a specific data subject request', async () => {
      const response = await request(app).get('/privacy/management/subject-requests/dsr-0001');
      expect(response.status).toBe(200);
      expect(response.body.data.id).toBe('dsr-0001');
      expect(response.body.data.requestType).toBe('access');
    });

    it('should return 404 for non-existent data subject request', async () => {
      const response = await request(app).get('/privacy/management/subject-requests/non-existent');
      expect(response.status).toBe(404);
    });
  });

  describe('POST /privacy/management/subject-requests', () => {
    it('should create a new data subject request', async () => {
      const newRequest = {
        requestType: 'erasure',
        dataSubjectName: 'Jane Smith',
        dataSubjectEmail: '<EMAIL>',
        requestDetails: 'Request for deletion of all my personal data'
      };

      const response = await request(app)
        .post('/privacy/management/subject-requests')
        .send(newRequest);

      expect(response.status).toBe(201);
      expect(response.body.data.dataSubjectName).toBe(newRequest.dataSubjectName);
      expect(response.body.data.requestType).toBe(newRequest.requestType);
      expect(response.body.message).toBe('Data subject request created successfully');
    });
  });

  // Consent Records Tests
  describe('GET /privacy/management/consent-records', () => {
    it('should return a list of consent records', async () => {
      const response = await request(app).get('/privacy/management/consent-records');
      expect(response.status).toBe(200);
      expect(response.body.data.length).toBeGreaterThan(0);
      expect(response.body.data.some(record => record.dataSubjectName === 'John Doe')).toBe(true);
    });
  });

  describe('GET /privacy/management/consent-records/:id', () => {
    it('should return a specific consent record', async () => {
      const response = await request(app).get('/privacy/management/consent-records/con-0001');
      expect(response.status).toBe(200);
      expect(response.body.data.id).toBe('con-0001');
      expect(response.body.data.consentType).toBe('marketing');
    });

    it('should return 404 for non-existent consent record', async () => {
      const response = await request(app).get('/privacy/management/consent-records/non-existent');
      expect(response.status).toBe(404);
    });
  });

  describe('POST /privacy/management/consent-records', () => {
    it('should create a new consent record', async () => {
      const newRecord = {
        dataSubjectName: 'Jane Smith',
        dataSubjectEmail: '<EMAIL>',
        consentType: 'analytics',
        consentDescription: 'Consent to use analytics cookies',
        consentGiven: true,
        consentVersion: '1.0',
        consentMethod: 'online-form',
        privacyNoticeVersion: '2.0'
      };

      const response = await request(app)
        .post('/privacy/management/consent-records')
        .send(newRecord);

      expect(response.status).toBe(201);
      expect(response.body.data.dataSubjectName).toBe(newRecord.dataSubjectName);
      expect(response.body.data.consentType).toBe(newRecord.consentType);
      expect(response.body.message).toBe('Consent record created successfully');
    });
  });

  // Privacy Notices Tests
  describe('GET /privacy/management/privacy-notices', () => {
    it('should return a list of privacy notices', async () => {
      const response = await request(app).get('/privacy/management/privacy-notices');
      expect(response.status).toBe(200);
      expect(response.body.data.length).toBeGreaterThan(0);
      expect(response.body.data.some(notice => notice.title === 'Website Privacy Notice')).toBe(true);
    });
  });

  describe('GET /privacy/management/privacy-notices/:id', () => {
    it('should return a specific privacy notice', async () => {
      const response = await request(app).get('/privacy/management/privacy-notices/pn-0001');
      expect(response.status).toBe(200);
      expect(response.body.data.id).toBe('pn-0001');
      expect(response.body.data.title).toBe('Website Privacy Notice');
    });

    it('should return 404 for non-existent privacy notice', async () => {
      const response = await request(app).get('/privacy/management/privacy-notices/non-existent');
      expect(response.status).toBe(404);
    });
  });

  describe('POST /privacy/management/privacy-notices', () => {
    it('should create a new privacy notice', async () => {
      const newNotice = {
        title: 'Mobile App Privacy Notice',
        version: '1.0',
        effectiveDate: '2023-06-01',
        status: 'active',
        audience: 'app-users',
        language: 'en',
        format: 'html',
        content: '<h1>Mobile App Privacy Notice</h1><p>This privacy notice explains how we collect and use your personal data in our mobile app...</p>',
        reviewCycle: 'annual',
        nextReviewDate: '2024-06-01',
        approvedBy: 'Legal Department',
        approvalDate: '2023-05-15'
      };

      const response = await request(app)
        .post('/privacy/management/privacy-notices')
        .send(newNotice);

      expect(response.status).toBe(201);
      expect(response.body.data.title).toBe(newNotice.title);
      expect(response.body.data.version).toBe(newNotice.version);
      expect(response.body.message).toBe('Privacy notice created successfully');
    });
  });

  // Data Breaches Tests
  describe('GET /privacy/management/data-breaches', () => {
    it('should return a list of data breaches', async () => {
      const response = await request(app).get('/privacy/management/data-breaches');
      expect(response.status).toBe(200);
      expect(response.body.data.length).toBeGreaterThan(0);
      expect(response.body.data.some(breach => breach.title === 'Unauthorized Access to Customer Database')).toBe(true);
    });
  });

  describe('GET /privacy/management/data-breaches/:id', () => {
    it('should return a specific data breach', async () => {
      const response = await request(app).get('/privacy/management/data-breaches/db-0001');
      expect(response.status).toBe(200);
      expect(response.body.data.id).toBe('db-0001');
      expect(response.body.data.breachType).toBe('unauthorized-access');
    });

    it('should return 404 for non-existent data breach', async () => {
      const response = await request(app).get('/privacy/management/data-breaches/non-existent');
      expect(response.status).toBe(404);
    });
  });

  describe('POST /privacy/management/data-breaches', () => {
    it('should create a new data breach', async () => {
      const newBreach = {
        title: 'Lost Company Laptop',
        description: 'Employee reported lost company laptop containing customer data',
        breachType: 'lost-device',
        occurrenceDate: '2023-08-04T18:30:00Z',
        affectedDataCategories: ['Contact information', 'Account details'],
        affectedDataSubjects: ['Customers'],
        approximateSubjectsCount: 200,
        potentialImpact: 'low'
      };

      const response = await request(app)
        .post('/privacy/management/data-breaches')
        .send(newBreach);

      expect(response.status).toBe(201);
      expect(response.body.data.title).toBe(newBreach.title);
      expect(response.body.data.breachType).toBe(newBreach.breachType);
      expect(response.body.message).toBe('Data breach created successfully');
    });
  });
});
