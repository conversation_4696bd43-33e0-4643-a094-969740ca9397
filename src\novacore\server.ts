/**
 * NovaCore Server
 *
 * This file creates an Express server to run the NovaCore API.
 */

import express from 'express';
import cors from 'cors';
import bodyParser from 'body-parser';
import path from 'path';
import api from './api';

// Create Express app
const app = express();
const port = process.env.PORT || 5000;

// Middleware
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Serve static files from the public directory
app.use(express.static(path.join(__dirname, 'public')));

// Mount API
app.use('/api', api);

// API info endpoint
app.get('/api/info', (req, res) => {
  res.json({
    name: 'NovaCore API',
    description: 'NovaCore API for NovaFuse NovaSphere and NovaConnect integration',
    version: '1.0.0',
    endpoints: [
      '/api/evidence',
      '/api/connectors',
      '/api/jobs',
      '/api/health',
    ],
  });
});

// Root endpoint - serve the API documentation
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Start server
app.listen(port, () => {
  console.log(`NovaCore API server running on port ${port}`);
});

export default app;
