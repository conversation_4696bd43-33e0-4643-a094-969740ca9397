# NovaConnect Performance Test Runner
# This script runs the NovaConnect performance tests in a Docker environment

# Set error action preference
$ErrorActionPreference = "Stop"

# Configuration
$dockerComposeFile = "docker-compose.test.yml"

# Function to display colored output
function Write-ColorOutput {
    param (
        [Parameter(Mandatory=$true)]
        [string]$Message,
        
        [Parameter(Mandatory=$false)]
        [string]$ForegroundColor = "White"
    )
    
    Write-Host $Message -ForegroundColor $ForegroundColor
}

# Function to check if Docker is running
function Test-DockerRunning {
    try {
        $dockerInfo = docker info 2>&1
        return $true
    } catch {
        return $false
    }
}

# Main execution
Write-ColorOutput "NovaConnect Performance Test Runner" -ForegroundColor Cyan
Write-ColorOutput "===================================" -ForegroundColor Cyan

# Check if Docker is running
if (-not (Test-DockerRunning)) {
    Write-ColorOutput "Error: Docker is not running. Please start Docker and try again." -ForegroundColor Red
    exit 1
}

# Start the Docker environment
Write-ColorOutput "`nStarting Docker test environment..." -ForegroundColor Yellow
try {
    docker-compose -f $dockerComposeFile up -d
    Write-ColorOutput "Docker environment started successfully." -ForegroundColor Green
} catch {
    Write-ColorOutput "Error starting Docker environment: $_" -ForegroundColor Red
    exit 1
}

# Wait for services to be ready
Write-ColorOutput "`nWaiting for services to be ready..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# Run the performance tests
Write-ColorOutput "`nRunning NovaConnect performance tests..." -ForegroundColor Yellow
try {
    docker-compose -f $dockerComposeFile run --rm test-runner npm run test:performance
    $testResult = $LASTEXITCODE
    
    if ($testResult -eq 0) {
        Write-ColorOutput "`nPerformance tests passed successfully!" -ForegroundColor Green
    } else {
        Write-ColorOutput "`nPerformance tests failed. Check the test report for details." -ForegroundColor Red
    }
} catch {
    Write-ColorOutput "Error running tests: $_" -ForegroundColor Red
    $testResult = 1
}

# Stop the Docker environment
Write-ColorOutput "`nStopping Docker test environment..." -ForegroundColor Yellow
try {
    docker-compose -f $dockerComposeFile down
    Write-ColorOutput "Docker environment stopped successfully." -ForegroundColor Green
} catch {
    Write-ColorOutput "Error stopping Docker environment: $_" -ForegroundColor Red
}

# Exit with the test result
exit $testResult
