
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>NovaVision Trinity Visualization</title>
  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: '<PERSON><PERSON>', 'Segoe UI', Arial, sans-serif;
      background-color: #1a1a2e;
      color: white;
    }

    .container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 100vh;
      padding: 20px;
    }

    .message {
      text-align: center;
      max-width: 600px;
      background-color: rgba(255, 255, 255, 0.1);
      padding: 30px;
      border-radius: 8px;
    }

    .message h1 {
      margin-top: 0;
      font-size: 28px;
      background: linear-gradient(45deg, #4CAF50, #2196F3, #9C27B0);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      text-fill-color: transparent;
      background-size: 200% 200%;
      animation: gradientShift 5s ease infinite;
    }

    .message p {
      margin-bottom: 20px;
      line-height: 1.6;
    }

    .button {
      display: inline-block;
      background-color: #2196F3;
      color: white;
      padding: 10px 20px;
      border-radius: 4px;
      text-decoration: none;
      font-weight: 500;
      transition: background-color 0.2s ease;
    }

    .button:hover {
      background-color: #1976D2;
    }

    @keyframes gradientShift {
      0% {
        background-position: 0% 50%;
      }
      50% {
        background-position: 100% 50%;
      }
      100% {
        background-position: 0% 50%;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="message">
      <h1>NovaVision Trinity Visualization</h1>
      <p>
        The NovaVision-integrated Trinity Visualization requires a React environment
        to run. This integration provides real-time data flow from the Universal Ripple
        Stack and interactive 3D visualization of the nested Trinity architecture.
      </p>
      <p>
        To experience the full NovaVision integration, please run the React application
        using the NovaVisionTrinityDemo component.
      </p>
      <a href="/static" class="button">View Static Visualization</a>
    </div>
  </div>
</body>
</html>
  