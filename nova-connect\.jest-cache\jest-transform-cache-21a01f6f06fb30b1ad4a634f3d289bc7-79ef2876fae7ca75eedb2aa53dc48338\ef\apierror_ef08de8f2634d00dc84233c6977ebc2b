269ec639e72c0750c2001bf78a568623
/**
 * NovaFuse Universal API Connector - API Error
 * 
 * This module defines API-related errors for the UAC.
 */

const UAConnectorError = require('./base-error');

/**
 * Error class for API failures
 * @class ApiError
 * @extends UAConnectorError
 */
class ApiError extends UAConnectorError {
  /**
   * Create a new ApiError
   * 
   * @param {string} message - Error message
   * @param {Object} options - Error options
   * @param {string} options.code - Error code
   * @param {string} options.severity - Error severity
   * @param {Object} options.context - Additional context for the error
   * @param {Error} options.cause - The error that caused this error
   * @param {number} options.statusCode - HTTP status code
   * @param {Object} options.response - API response data
   */
  constructor(message, options = {}) {
    super(message, {
      code: options.code || 'API_ERROR',
      severity: options.severity || 'error',
      context: options.context || {},
      cause: options.cause
    });
    this.statusCode = options.statusCode;
    this.response = options.response;
  }

  /**
   * Get a user-friendly error message
   * 
   * @returns {string} - User-friendly error message
   */
  getUserMessage() {
    return 'An error occurred while communicating with the external service. Please try again later.';
  }

  /**
   * Convert the error to a JSON object
   * 
   * @param {boolean} includeStack - Whether to include the stack trace
   * @returns {Object} - JSON representation of the error
   */
  toJSON(includeStack = false) {
    const json = super.toJSON(includeStack);
    if (this.statusCode) {
      json.statusCode = this.statusCode;
    }
    if (this.response) {
      json.response = this.response;
    }
    return json;
  }
}

/**
 * Error class for rate limit exceeded errors
 * @class RateLimitExceededError
 * @extends ApiError
 */
class RateLimitExceededError extends ApiError {
  /**
   * Create a new RateLimitExceededError
   * 
   * @param {string} message - Error message
   * @param {Object} options - Error options
   * @param {number} options.retryAfter - Seconds to wait before retrying
   */
  constructor(message = 'Rate limit exceeded', options = {}) {
    super(message, {
      code: options.code || 'API_RATE_LIMIT_EXCEEDED',
      severity: options.severity || 'warning',
      context: options.context || {},
      cause: options.cause,
      statusCode: options.statusCode || 429,
      response: options.response
    });
    this.retryAfter = options.retryAfter;
  }

  /**
   * Get a user-friendly error message
   * 
   * @returns {string} - User-friendly error message
   */
  getUserMessage() {
    if (this.retryAfter) {
      return `Rate limit exceeded. Please try again after ${this.retryAfter} seconds.`;
    }
    return 'Rate limit exceeded. Please try again later.';
  }

  /**
   * Convert the error to a JSON object
   * 
   * @param {boolean} includeStack - Whether to include the stack trace
   * @returns {Object} - JSON representation of the error
   */
  toJSON(includeStack = false) {
    const json = super.toJSON(includeStack);
    if (this.retryAfter) {
      json.retryAfter = this.retryAfter;
    }
    return json;
  }
}

/**
 * Error class for resource not found errors
 * @class ResourceNotFoundError
 * @extends ApiError
 */
class ResourceNotFoundError extends ApiError {
  /**
   * Create a new ResourceNotFoundError
   * 
   * @param {string} resourceType - The type of resource
   * @param {string} resourceId - The ID of the resource
   * @param {Object} options - Error options
   */
  constructor(resourceType, resourceId, options = {}) {
    const message = `${resourceType} not found with ID: ${resourceId}`;
    super(message, {
      code: options.code || 'API_RESOURCE_NOT_FOUND',
      severity: options.severity || 'error',
      context: {
        ...options.context,
        resourceType,
        resourceId
      },
      cause: options.cause,
      statusCode: options.statusCode || 404,
      response: options.response
    });
    this.resourceType = resourceType;
    this.resourceId = resourceId;
  }

  /**
   * Get a user-friendly error message
   * 
   * @returns {string} - User-friendly error message
   */
  getUserMessage() {
    return `The requested ${this.resourceType.toLowerCase()} could not be found.`;
  }

  /**
   * Convert the error to a JSON object
   * 
   * @param {boolean} includeStack - Whether to include the stack trace
   * @returns {Object} - JSON representation of the error
   */
  toJSON(includeStack = false) {
    const json = super.toJSON(includeStack);
    json.resourceType = this.resourceType;
    json.resourceId = this.resourceId;
    return json;
  }
}

/**
 * Error class for bad request errors
 * @class BadRequestError
 * @extends ApiError
 */
class BadRequestError extends ApiError {
  /**
   * Create a new BadRequestError
   * 
   * @param {string} message - Error message
   * @param {Object} options - Error options
   */
  constructor(message = 'Bad request', options = {}) {
    super(message, {
      code: options.code || 'API_BAD_REQUEST',
      severity: options.severity || 'error',
      context: options.context || {},
      cause: options.cause,
      statusCode: options.statusCode || 400,
      response: options.response
    });
  }

  /**
   * Get a user-friendly error message
   * 
   * @returns {string} - User-friendly error message
   */
  getUserMessage() {
    return 'The request was invalid. Please check your input and try again.';
  }
}

/**
 * Error class for server errors
 * @class ServerError
 * @extends ApiError
 */
class ServerError extends ApiError {
  /**
   * Create a new ServerError
   * 
   * @param {string} message - Error message
   * @param {Object} options - Error options
   */
  constructor(message = 'Server error', options = {}) {
    super(message, {
      code: options.code || 'API_SERVER_ERROR',
      severity: options.severity || 'error',
      context: options.context || {},
      cause: options.cause,
      statusCode: options.statusCode || 500,
      response: options.response
    });
  }

  /**
   * Get a user-friendly error message
   * 
   * @returns {string} - User-friendly error message
   */
  getUserMessage() {
    return 'An error occurred on the server. Please try again later or contact support if the issue persists.';
  }
}
module.exports = {
  ApiError,
  RateLimitExceededError,
  ResourceNotFoundError,
  BadRequestError,
  ServerError
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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