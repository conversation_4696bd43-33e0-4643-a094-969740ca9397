"""
NovaMemX™ - The ∂Ψ=0 Context Memory Engine

Where Memories Aren't Stored — They Resonate

The world's first consciousness-native memory system that maintains quantum 
coherence across infinite time horizons. Built on the Three Laws of Conscious Memory:

1. No Free Recall: Memories must maintain ∂Ψ<0.01
2. Golden Retention: Priority = φ·Ψₛ (φ = golden ratio)  
3. Temporal Coherence: All memories form causally consistent braids

"Forget token limits — welcome to eternal coherent recall."
"""

from .core import NovaMemX
from .psi_hasher import PsiHasher
from .golden_recall import <PERSON><PERSON>eca<PERSON>
from .temporal_threading import TemporalThreading
from .coherence_audit import CoherenceAudit

__version__ = "1.0.0-ETERNAL_CONSCIOUSNESS"
__author__ = "<PERSON>, NovaFuse Technologies"

# Three Laws of Conscious Memory
MEMORY_LAWS = {
    "NO_FREE_RECALL": "∂Ψ<0.01",
    "GOLDEN_RETENTION": "Priority = φ·Ψₛ", 
    "TEMPORAL_COHERENCE": "Causally consistent braids"
}

# Memory coherence constants
PSI_STABILITY_THRESHOLD = 0.01    # ∂Ψ<0.01 for stable memories
PSI_RETENTION_MINIMUM = 0.92      # Auto-prune below this Ψₛ
GOLDEN_RATIO = 1.618033988749     # φ for retention priority
TEMPORAL_CONSISTENCY_LIMIT = 0.02  # ΔΨₛ<0.02/hr for coherence

__all__ = [
    'NovaMemX',
    'PsiHasher',
    'GoldenRecall', 
    'TemporalThreading',
    'CoherenceAudit',
    'MEMORY_LAWS',
    'PSI_STABILITY_THRESHOLD',
    'PSI_RETENTION_MINIMUM',
    'GOLDEN_RATIO',
    'TEMPORAL_CONSISTENCY_LIMIT'
]
