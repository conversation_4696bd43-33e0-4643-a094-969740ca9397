/**
 * NovaCore Configuration
 * 
 * This file exports the configuration for the NovaCore API server.
 */

require('dotenv').config();

const config = {
  // Server configuration
  server: {
    port: process.env.NOVACORE_PORT || 3001,
    host: process.env.NOVACORE_HOST || 'localhost',
    baseUrl: process.env.NOVACORE_BASE_URL || 'http://localhost:3001'
  },
  
  // Database configuration
  database: {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 27017,
    name: process.env.DB_NAME || 'novacore',
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    url: process.env.DB_URL || 'mongodb://localhost:27017/novacore'
  },
  
  // API configuration
  api: {
    version: process.env.API_VERSION || 'v1',
    prefix: process.env.API_PREFIX || '/api',
    rateLimitWindow: process.env.RATE_LIMIT_WINDOW || 15 * 60 * 1000, // 15 minutes
    rateLimitMax: process.env.RATE_LIMIT_MAX || 100, // 100 requests per window
    timeout: process.env.API_TIMEOUT || 30000 // 30 seconds
  },
  
  // Authentication configuration
  auth: {
    jwtSecret: process.env.JWT_SECRET || 'novacore-jwt-secret',
    jwtExpiresIn: process.env.JWT_EXPIRES_IN || '1d',
    apiKey: process.env.API_KEY || 'novacore-api-key'
  },
  
  // Blockchain configuration
  blockchain: {
    provider: process.env.BLOCKCHAIN_PROVIDER || 'ethereum',
    networkId: process.env.BLOCKCHAIN_NETWORK_ID || '1',
    apiKey: process.env.BLOCKCHAIN_API_KEY,
    apiUrl: process.env.BLOCKCHAIN_API_URL,
    contractAddress: process.env.BLOCKCHAIN_CONTRACT_ADDRESS
  },
  
  // Encryption configuration
  encryption: {
    algorithm: process.env.ENCRYPTION_ALGORITHM || 'aes-256-gcm',
    secretKey: process.env.ENCRYPTION_SECRET_KEY || 'novacore-encryption-key'
  },
  
  // Logging configuration
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    format: process.env.LOG_FORMAT || 'json'
  }
};

module.exports = config;
