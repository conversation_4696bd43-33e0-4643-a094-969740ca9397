"""
Example of using the Notification Manager.

This example demonstrates how to use the Notification Manager to send
and retrieve notifications.
"""

import os
import sys
import json
import logging
import datetime

# Add the parent directory to the path so we can import the UCECS modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.ucecs.core.notification_manager import NotificationManager, NotificationType

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """Run the Notification Manager example."""
    # Create a temporary directory for the example
    temp_dir = os.path.join(os.getcwd(), 'temp_notification_example')
    os.makedirs(temp_dir, exist_ok=True)
    
    # Create a notifications directory
    notifications_dir = os.path.join(temp_dir, 'notifications')
    
    # Create a Notification Manager
    notification_manager = NotificationManager(notifications_dir=notifications_dir)
    
    try:
        # Send an info notification
        logger.info("Sending an info notification...")
        info_notification = notification_manager.send_notification(
            message="This is an informational notification",
            notification_type=NotificationType.INFO,
            subject="Info Notification",
            details={
                'source': 'notification_manager_example.py',
                'timestamp': datetime.datetime.now(datetime.timezone.utc).isoformat()
            }
        )
        logger.info(f"Info notification sent: {json.dumps(info_notification, indent=2)}")
        
        # Send a warning notification
        logger.info("Sending a warning notification...")
        warning_notification = notification_manager.send_notification(
            message="This is a warning notification",
            notification_type=NotificationType.WARNING,
            subject="Warning Notification",
            details={
                'source': 'notification_manager_example.py',
                'timestamp': datetime.datetime.now(datetime.timezone.utc).isoformat(),
                'warning_code': 'WARN001'
            }
        )
        logger.info(f"Warning notification sent: {json.dumps(warning_notification, indent=2)}")
        
        # Send an error notification
        logger.info("Sending an error notification...")
        error_notification = notification_manager.send_notification(
            message="This is an error notification",
            notification_type=NotificationType.ERROR,
            subject="Error Notification",
            details={
                'source': 'notification_manager_example.py',
                'timestamp': datetime.datetime.now(datetime.timezone.utc).isoformat(),
                'error_code': 'ERR001',
                'stack_trace': 'Simulated stack trace...'
            }
        )
        logger.info(f"Error notification sent: {json.dumps(error_notification, indent=2)}")
        
        # Get all notifications
        logger.info("Getting all notifications...")
        all_notifications = notification_manager.get_notifications()
        logger.info(f"All notifications: {json.dumps(all_notifications, indent=2)}")
        
        # Get only warning notifications
        logger.info("Getting warning notifications...")
        warning_notifications = notification_manager.get_notifications(
            notification_type=NotificationType.WARNING
        )
        logger.info(f"Warning notifications: {json.dumps(warning_notifications, indent=2)}")
        
        # Check the notification log file
        notification_log_path = os.path.join(notifications_dir, 'notification_log.json')
        logger.info(f"Notification log saved to: {notification_log_path}")
        
        # Clear notifications
        logger.info("Clearing notifications...")
        notification_manager.clear_notifications()
        
        # Verify notifications were cleared
        logger.info("Getting notifications after clearing...")
        cleared_notifications = notification_manager.get_notifications()
        logger.info(f"Notifications after clearing: {json.dumps(cleared_notifications, indent=2)}")
        
    except Exception as e:
        logger.error(f"Error: {e}")
    
    finally:
        # Clean up the temporary directory
        # Uncomment the following line to delete the temporary directory
        # import shutil; shutil.rmtree(temp_dir)
        pass

if __name__ == "__main__":
    main()
