/**
 * Connector API Tests
 * 
 * Tests for the connector API endpoints.
 */

const { request, app } = require('./setup');

// Mock connector data
const mockConnector = {
  name: 'Test Connector',
  description: 'A test connector for API testing',
  version: '1.0.0',
  category: 'Security',
  tags: ['test', 'api'],
  baseUrl: 'https://api.example.com',
  authentication: {
    type: 'API_KEY',
    location: 'header',
    headerName: 'X-API-Key'
  },
  endpoints: [
    {
      id: 'getItems',
      name: 'Get Items',
      description: 'Retrieve a list of items',
      path: '/items',
      method: 'GET',
      parameters: {
        query: {
          limit: {
            type: 'number',
            description: 'Maximum number of items to return',
            default: 10
          },
          offset: {
            type: 'number',
            description: 'Number of items to skip',
            default: 0
          }
        }
      }
    }
  ]
};

describe('Connector API', () => {
  let createdConnectorId;

  describe('GET /api/connect/connectors', () => {
    it('should return a list of connectors', async () => {
      const response = await request(app)
        .get('/api/connect/connectors')
        .expect('Content-Type', /json/)
        .expect(200);
      
      // Verify response is an array
      expect(Array.isArray(response.body)).toBe(true);
    });
  });

  describe('POST /api/connect/connectors', () => {
    it('should create a new connector', async () => {
      const response = await request(app)
        .post('/api/connect/connectors')
        .send(mockConnector)
        .expect('Content-Type', /json/)
        .expect(201);
      
      // Save the created connector ID for later tests
      createdConnectorId = response.body.id || response.body._id;
      
      // Verify response contains the connector data
      expect(response.body).toHaveProperty('name', mockConnector.name);
      expect(response.body).toHaveProperty('description', mockConnector.description);
      expect(response.body).toHaveProperty('version', mockConnector.version);
    });

    it('should return 400 when required fields are missing', async () => {
      const invalidConnector = {
        description: 'Missing required fields'
      };

      await request(app)
        .post('/api/connect/connectors')
        .send(invalidConnector)
        .expect('Content-Type', /json/)
        .expect(400);
    });
  });

  describe('GET /api/connect/connectors/:id', () => {
    it('should return a specific connector by ID', async () => {
      // Skip if no connector was created
      if (!createdConnectorId) {
        return;
      }

      const response = await request(app)
        .get(`/api/connect/connectors/${createdConnectorId}`)
        .expect('Content-Type', /json/)
        .expect(200);
      
      // Verify response contains the connector data
      expect(response.body).toHaveProperty('name', mockConnector.name);
      expect(response.body).toHaveProperty('description', mockConnector.description);
    });

    it('should return 404 for non-existent connector ID', async () => {
      await request(app)
        .get('/api/connect/connectors/nonexistent-id')
        .expect('Content-Type', /json/)
        .expect(404);
    });
  });

  describe('PUT /api/connect/connectors/:id', () => {
    it('should update an existing connector', async () => {
      // Skip if no connector was created
      if (!createdConnectorId) {
        return;
      }

      const updatedData = {
        ...mockConnector,
        name: 'Updated Connector Name',
        description: 'Updated connector description'
      };

      const response = await request(app)
        .put(`/api/connect/connectors/${createdConnectorId}`)
        .send(updatedData)
        .expect('Content-Type', /json/)
        .expect(200);
      
      // Verify response contains the updated data
      expect(response.body).toHaveProperty('name', updatedData.name);
      expect(response.body).toHaveProperty('description', updatedData.description);
    });

    it('should return 404 for updating non-existent connector', async () => {
      await request(app)
        .put('/api/connect/connectors/nonexistent-id')
        .send(mockConnector)
        .expect('Content-Type', /json/)
        .expect(404);
    });
  });

  describe('DELETE /api/connect/connectors/:id', () => {
    it('should delete an existing connector', async () => {
      // Skip if no connector was created
      if (!createdConnectorId) {
        return;
      }

      await request(app)
        .delete(`/api/connect/connectors/${createdConnectorId}`)
        .expect(200);
      
      // Verify the connector was deleted
      await request(app)
        .get(`/api/connect/connectors/${createdConnectorId}`)
        .expect(404);
    });

    it('should return 404 for deleting non-existent connector', async () => {
      await request(app)
        .delete('/api/connect/connectors/nonexistent-id')
        .expect(404);
    });
  });
});
