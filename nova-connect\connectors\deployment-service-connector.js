/**
 * NovaFuse Deployment Service Connector
 * Integrates Docker deployment and orchestration with NovaConnect
 * Enables real-time deployment management, monitoring, and health tracking
 */

const { spawn, exec } = require('child_process');
const fs = require('fs').promises;
const path = require('path');
const EventEmitter = require('events');

class DeploymentServiceConnector extends EventEmitter {
    constructor(novaConnect, options = {}) {
        super();
        
        this.novaConnect = novaConnect;
        this.options = {
            dockerComposePath: options.dockerComposePath || './docker-compose.unified.yml',
            deploymentTimeout: options.deploymentTimeout || 300000, // 5 minutes
            healthCheckInterval: options.healthCheckInterval || 30000, // 30 seconds
            enableRealTimeUpdates: options.enableRealTimeUpdates !== false,
            maxConcurrentDeployments: options.maxConcurrentDeployments || 3,
            ...options
        };
        
        // Deployment categories from the unified compose file
        this.deploymentCategories = {
            core: {
                name: 'Core Platform',
                services: ['novafuse-api', 'nova-database', 'nova-redis'],
                priority: 1,
                dependencies: [],
                healthChecks: true
            },
            coherence: {
                name: 'Coherence-Native Engines',
                services: ['neri-engine', 'nece-engine', 'csme-engine', 'novasentient'],
                priority: 2,
                dependencies: ['coherence-validator'],
                healthChecks: true
            },
            blockchain: {
                name: 'Blockchain & Financial',
                services: ['kethernet', 'novafinx', 'novastr-x'],
                priority: 3,
                dependencies: ['core'],
                healthChecks: true
            },
            security: {
                name: 'Security & Identity',
                services: ['novashield', 'novacaia', 'quantum-coherence-firewall'],
                priority: 2,
                dependencies: ['core'],
                healthChecks: true
            },
            infrastructure: {
                name: 'Supporting Infrastructure',
                services: ['nova-database', 'nova-redis', 'coherence-validator'],
                priority: 0,
                dependencies: [],
                healthChecks: true
            },
            monitoring: {
                name: 'Monitoring & Analytics',
                services: ['prometheus', 'grafana', 'nova-analytics'],
                priority: 4,
                dependencies: ['core'],
                healthChecks: false
            }
        };
        
        this.activeDeployments = new Map();
        this.serviceStatus = new Map();
        this.healthCheckInterval = null;
        
        this.registerWithNovaConnect();
    }
    
    /**
     * Register this connector with NovaConnect
     */
    async registerWithNovaConnect() {
        const connectorConfig = {
            id: 'novafuse-deployment-service',
            name: 'NovaFuse Deployment Service',
            description: 'Docker deployment and orchestration management',
            version: '1.0.0',
            type: 'service',
            category: 'deployment',
            endpoints: {
                'deploy-services': {
                    method: 'POST',
                    path: '/deployment/deploy',
                    description: 'Deploy services by category or specific services'
                },
                'stop-services': {
                    method: 'POST',
                    path: '/deployment/stop',
                    description: 'Stop running services'
                },
                'service-status': {
                    method: 'GET',
                    path: '/deployment/status',
                    description: 'Get current service status and health'
                },
                'deployment-logs': {
                    method: 'GET',
                    path: '/deployment/logs',
                    description: 'Get deployment and service logs'
                },
                'health-check': {
                    method: 'GET',
                    path: '/deployment/health',
                    description: 'Perform health check on all services'
                },
                'scale-services': {
                    method: 'POST',
                    path: '/deployment/scale',
                    description: 'Scale services up or down'
                }
            },
            authentication: {
                type: 'none',
                required: false
            },
            realTimeEvents: [
                'deployment.started',
                'deployment.progress',
                'deployment.completed',
                'deployment.failed',
                'service.health.changed',
                'container.status.changed'
            ]
        };
        
        try {
            await this.novaConnect.registerConnector(connectorConfig);
            console.log('✅ Deployment Service Connector registered with NovaConnect');
            
            this.setupEventHandlers();
            this.startHealthMonitoring();
            
        } catch (error) {
            console.error('❌ Failed to register Deployment Service Connector:', error);
            throw error;
        }
    }
    
    /**
     * Set up NovaConnect event handlers
     */
    setupEventHandlers() {
        this.novaConnect.on('connector.execute', async (event) => {
            if (event.connectorId === 'novafuse-deployment-service') {
                await this.handleConnectorRequest(event);
            }
        });
        
        if (this.options.enableRealTimeUpdates) {
            this.on('deploymentStarted', (data) => {
                this.novaConnect.emit('deployment.started', data);
            });
            
            this.on('deploymentProgress', (data) => {
                this.novaConnect.emit('deployment.progress', data);
            });
            
            this.on('deploymentCompleted', (data) => {
                this.novaConnect.emit('deployment.completed', data);
            });
            
            this.on('deploymentFailed', (data) => {
                this.novaConnect.emit('deployment.failed', data);
            });
            
            this.on('serviceHealthChanged', (data) => {
                this.novaConnect.emit('service.health.changed', data);
            });
        }
    }
    
    /**
     * Handle NovaConnect connector requests
     */
    async handleConnectorRequest(event) {
        const { endpointId, parameters, requestId } = event;
        
        try {
            let result;
            
            switch (endpointId) {
                case 'deploy-services':
                    result = await this.deployServices(parameters);
                    break;
                    
                case 'stop-services':
                    result = await this.stopServices(parameters);
                    break;
                    
                case 'service-status':
                    result = await this.getServiceStatus(parameters);
                    break;
                    
                case 'deployment-logs':
                    result = await this.getDeploymentLogs(parameters);
                    break;
                    
                case 'health-check':
                    result = await this.performHealthCheck(parameters);
                    break;
                    
                case 'scale-services':
                    result = await this.scaleServices(parameters);
                    break;
                    
                default:
                    throw new Error(`Unknown endpoint: ${endpointId}`);
            }
            
            this.novaConnect.emit('connector.response', {
                requestId,
                success: true,
                data: result
            });
            
        } catch (error) {
            console.error(`Deployment Service error for ${endpointId}:`, error);
            
            this.novaConnect.emit('connector.response', {
                requestId,
                success: false,
                error: {
                    message: error.message,
                    code: error.code || 'DEPLOYMENT_SERVICE_ERROR'
                }
            });
        }
    }
    
    /**
     * Deploy services by category or specific services
     */
    async deployServices(parameters = {}) {
        const { 
            categories = [], 
            services = [], 
            options = {},
            requestId = Date.now().toString()
        } = parameters;
        
        const deployment = {
            id: requestId,
            startTime: new Date().toISOString(),
            status: 'running',
            categories: categories,
            services: services,
            options: options,
            progress: {
                total: 0,
                completed: 0,
                failed: 0,
                current: null
            },
            results: {}
        };
        
        this.activeDeployments.set(requestId, deployment);
        
        this.emit('deploymentStarted', {
            deploymentId: requestId,
            categories: categories,
            services: services,
            startTime: deployment.startTime
        });
        
        try {
            // Determine services to deploy
            const servicesToDeploy = this.resolveServicesToDeploy(categories, services);
            deployment.progress.total = servicesToDeploy.length;
            
            // Deploy services in priority order
            const sortedServices = this.sortServicesByPriority(servicesToDeploy);
            
            for (const service of sortedServices) {
                deployment.progress.current = service;
                
                this.emit('deploymentProgress', {
                    deploymentId: requestId,
                    service: service,
                    progress: deployment.progress
                });
                
                try {
                    const result = await this.deployService(service, options);
                    deployment.results[service] = result;
                    deployment.progress.completed++;
                    
                } catch (error) {
                    deployment.results[service] = {
                        success: false,
                        error: error.message
                    };
                    deployment.progress.failed++;
                    
                    if (options.stopOnFailure) {
                        throw error;
                    }
                }
            }
            
            deployment.status = 'completed';
            deployment.endTime = new Date().toISOString();
            deployment.duration = Date.now() - new Date(deployment.startTime).getTime();
            
            this.emit('deploymentCompleted', {
                deploymentId: requestId,
                results: deployment.results,
                duration: deployment.duration
            });
            
        } catch (error) {
            deployment.status = 'failed';
            deployment.error = error.message;
            deployment.endTime = new Date().toISOString();
            
            this.emit('deploymentFailed', {
                deploymentId: requestId,
                error: error.message
            });
        } finally {
            this.activeDeployments.delete(requestId);
        }
        
        return {
            deploymentId: requestId,
            status: deployment.status,
            results: deployment.results,
            progress: deployment.progress
        };
    }
    
    /**
     * Deploy a single service
     */
    async deployService(serviceName, options = {}) {
        return new Promise((resolve, reject) => {
            const command = `docker-compose -f ${this.options.dockerComposePath} up -d ${serviceName}`;
            
            exec(command, { timeout: this.options.deploymentTimeout }, (error, stdout, stderr) => {
                if (error) {
                    reject(new Error(`Failed to deploy ${serviceName}: ${error.message}`));
                    return;
                }
                
                // Update service status
                this.serviceStatus.set(serviceName, {
                    status: 'running',
                    lastUpdated: new Date().toISOString(),
                    deployedAt: new Date().toISOString()
                });
                
                resolve({
                    success: true,
                    service: serviceName,
                    output: stdout,
                    deployedAt: new Date().toISOString()
                });
            });
        });
    }
    
    /**
     * Resolve services to deploy based on categories and specific services
     */
    resolveServicesToDeploy(categories, services) {
        const servicesToDeploy = new Set();
        
        // Add services from categories
        for (const categoryKey of categories) {
            const category = this.deploymentCategories[categoryKey];
            if (category) {
                category.services.forEach(service => servicesToDeploy.add(service));
            }
        }
        
        // Add specific services
        services.forEach(service => servicesToDeploy.add(service));
        
        return Array.from(servicesToDeploy);
    }
    
    /**
     * Sort services by deployment priority
     */
    sortServicesByPriority(services) {
        const serviceWithPriority = services.map(service => {
            // Find which category this service belongs to
            for (const [categoryKey, category] of Object.entries(this.deploymentCategories)) {
                if (category.services.includes(service)) {
                    return { service, priority: category.priority };
                }
            }
            return { service, priority: 999 }; // Unknown services last
        });
        
        return serviceWithPriority
            .sort((a, b) => a.priority - b.priority)
            .map(item => item.service);
    }
    
    /**
     * Stop services
     */
    async stopServices(parameters = {}) {
        const { services = [], categories = [] } = parameters;
        
        const servicesToStop = this.resolveServicesToDeploy(categories, services);
        const results = {};
        
        for (const service of servicesToStop) {
            try {
                const result = await this.stopService(service);
                results[service] = result;
            } catch (error) {
                results[service] = {
                    success: false,
                    error: error.message
                };
            }
        }
        
        return {
            success: true,
            results: results,
            stoppedAt: new Date().toISOString()
        };
    }
    
    /**
     * Stop a single service
     */
    async stopService(serviceName) {
        return new Promise((resolve, reject) => {
            const command = `docker-compose -f ${this.options.dockerComposePath} stop ${serviceName}`;
            
            exec(command, (error, stdout, stderr) => {
                if (error) {
                    reject(new Error(`Failed to stop ${serviceName}: ${error.message}`));
                    return;
                }
                
                // Update service status
                this.serviceStatus.set(serviceName, {
                    status: 'stopped',
                    lastUpdated: new Date().toISOString(),
                    stoppedAt: new Date().toISOString()
                });
                
                resolve({
                    success: true,
                    service: serviceName,
                    output: stdout,
                    stoppedAt: new Date().toISOString()
                });
            });
        });
    }
    
    /**
     * Get current service status
     */
    async getServiceStatus(parameters = {}) {
        const { services = [], detailed = false } = parameters;
        
        try {
            // Get Docker Compose status
            const composeStatus = await this.getDockerComposeStatus();
            
            // If specific services requested, filter results
            if (services.length > 0) {
                const filteredStatus = {};
                services.forEach(service => {
                    if (composeStatus[service]) {
                        filteredStatus[service] = composeStatus[service];
                    }
                });
                return filteredStatus;
            }
            
            return {
                services: composeStatus,
                categories: this.getCategoryStatus(composeStatus),
                lastUpdated: new Date().toISOString()
            };
            
        } catch (error) {
            console.error('Failed to get service status:', error);
            return {
                error: error.message,
                fallbackStatus: Object.fromEntries(this.serviceStatus)
            };
        }
    }
    
    /**
     * Get Docker Compose status
     */
    async getDockerComposeStatus() {
        return new Promise((resolve, reject) => {
            const command = `docker-compose -f ${this.options.dockerComposePath} ps --format json`;
            
            exec(command, (error, stdout, stderr) => {
                if (error) {
                    reject(new Error(`Failed to get compose status: ${error.message}`));
                    return;
                }
                
                try {
                    const services = {};
                    const lines = stdout.trim().split('\n').filter(line => line.trim());
                    
                    lines.forEach(line => {
                        try {
                            const serviceInfo = JSON.parse(line);
                            services[serviceInfo.Service] = {
                                name: serviceInfo.Service,
                                status: serviceInfo.State,
                                health: serviceInfo.Health || 'unknown',
                                ports: serviceInfo.Publishers || [],
                                lastUpdated: new Date().toISOString()
                            };
                        } catch (parseError) {
                            // Skip invalid JSON lines
                        }
                    });
                    
                    resolve(services);
                    
                } catch (parseError) {
                    reject(new Error(`Failed to parse compose status: ${parseError.message}`));
                }
            });
        });
    }
    
    /**
     * Get category status summary
     */
    getCategoryStatus(serviceStatus) {
        const categoryStatus = {};
        
        for (const [categoryKey, category] of Object.entries(this.deploymentCategories)) {
            const categoryServices = category.services;
            const runningServices = categoryServices.filter(service => 
                serviceStatus[service] && serviceStatus[service].status === 'running'
            ).length;
            
            categoryStatus[categoryKey] = {
                name: category.name,
                total: categoryServices.length,
                running: runningServices,
                status: runningServices === categoryServices.length ? 'healthy' : 
                        runningServices > 0 ? 'partial' : 'stopped',
                services: categoryServices.map(service => ({
                    name: service,
                    status: serviceStatus[service]?.status || 'unknown',
                    health: serviceStatus[service]?.health || 'unknown'
                }))
            };
        }
        
        return categoryStatus;
    }
    
    /**
     * Get deployment logs
     */
    async getDeploymentLogs(parameters = {}) {
        const { services = [], lines = 100 } = parameters;
        
        const logs = {};
        
        for (const service of services) {
            try {
                const serviceLogs = await this.getServiceLogs(service, lines);
                logs[service] = serviceLogs;
            } catch (error) {
                logs[service] = {
                    error: error.message
                };
            }
        }
        
        return {
            logs: logs,
            retrievedAt: new Date().toISOString()
        };
    }
    
    /**
     * Get logs for a specific service
     */
    async getServiceLogs(serviceName, lines = 100) {
        return new Promise((resolve, reject) => {
            const command = `docker-compose -f ${this.options.dockerComposePath} logs --tail=${lines} ${serviceName}`;
            
            exec(command, (error, stdout, stderr) => {
                if (error) {
                    reject(new Error(`Failed to get logs for ${serviceName}: ${error.message}`));
                    return;
                }
                
                resolve({
                    service: serviceName,
                    logs: stdout,
                    lines: lines,
                    retrievedAt: new Date().toISOString()
                });
            });
        });
    }
    
    /**
     * Perform health check on services
     */
    async performHealthCheck(parameters = {}) {
        const { services = [] } = parameters;
        
        try {
            const status = await this.getServiceStatus({ services, detailed: true });
            
            const healthCheck = {
                overall: 'healthy',
                services: {},
                summary: {
                    total: 0,
                    healthy: 0,
                    unhealthy: 0,
                    unknown: 0
                },
                checkedAt: new Date().toISOString()
            };
            
            const servicesToCheck = services.length > 0 ? services : Object.keys(status.services || {});
            
            for (const service of servicesToCheck) {
                const serviceStatus = status.services?.[service];
                if (serviceStatus) {
                    const isHealthy = serviceStatus.status === 'running' && 
                                    (serviceStatus.health === 'healthy' || serviceStatus.health === 'unknown');
                    
                    healthCheck.services[service] = {
                        status: serviceStatus.status,
                        health: serviceStatus.health,
                        healthy: isHealthy
                    };
                    
                    healthCheck.summary.total++;
                    if (isHealthy) {
                        healthCheck.summary.healthy++;
                    } else if (serviceStatus.health === 'unhealthy') {
                        healthCheck.summary.unhealthy++;
                    } else {
                        healthCheck.summary.unknown++;
                    }
                }
            }
            
            // Determine overall health
            if (healthCheck.summary.unhealthy > 0) {
                healthCheck.overall = 'unhealthy';
            } else if (healthCheck.summary.unknown > healthCheck.summary.healthy) {
                healthCheck.overall = 'unknown';
            }
            
            return healthCheck;
            
        } catch (error) {
            return {
                overall: 'error',
                error: error.message,
                checkedAt: new Date().toISOString()
            };
        }
    }
    
    /**
     * Scale services
     */
    async scaleServices(parameters = {}) {
        const { services = {}, options = {} } = parameters;
        
        const results = {};
        
        for (const [serviceName, replicas] of Object.entries(services)) {
            try {
                const result = await this.scaleService(serviceName, replicas);
                results[serviceName] = result;
            } catch (error) {
                results[serviceName] = {
                    success: false,
                    error: error.message
                };
            }
        }
        
        return {
            success: true,
            results: results,
            scaledAt: new Date().toISOString()
        };
    }
    
    /**
     * Scale a single service
     */
    async scaleService(serviceName, replicas) {
        return new Promise((resolve, reject) => {
            const command = `docker-compose -f ${this.options.dockerComposePath} up -d --scale ${serviceName}=${replicas} ${serviceName}`;
            
            exec(command, (error, stdout, stderr) => {
                if (error) {
                    reject(new Error(`Failed to scale ${serviceName}: ${error.message}`));
                    return;
                }
                
                resolve({
                    success: true,
                    service: serviceName,
                    replicas: replicas,
                    output: stdout,
                    scaledAt: new Date().toISOString()
                });
            });
        });
    }
    
    /**
     * Start health monitoring
     */
    startHealthMonitoring() {
        if (this.healthCheckInterval) {
            clearInterval(this.healthCheckInterval);
        }
        
        this.healthCheckInterval = setInterval(async () => {
            try {
                const healthCheck = await this.performHealthCheck({});
                
                // Emit health changes
                for (const [service, health] of Object.entries(healthCheck.services)) {
                    const previousHealth = this.serviceStatus.get(service)?.health;
                    if (previousHealth && previousHealth !== health.health) {
                        this.emit('serviceHealthChanged', {
                            service: service,
                            previousHealth: previousHealth,
                            currentHealth: health.health,
                            timestamp: new Date().toISOString()
                        });
                    }
                    
                    // Update stored status
                    this.serviceStatus.set(service, {
                        ...this.serviceStatus.get(service),
                        health: health.health,
                        status: health.status,
                        lastUpdated: new Date().toISOString()
                    });
                }
                
            } catch (error) {
                console.error('Health monitoring error:', error);
            }
        }, this.options.healthCheckInterval);
        
        console.log(`✅ Health monitoring started (interval: ${this.options.healthCheckInterval}ms)`);
    }
    
    /**
     * Stop health monitoring
     */
    stopHealthMonitoring() {
        if (this.healthCheckInterval) {
            clearInterval(this.healthCheckInterval);
            this.healthCheckInterval = null;
            console.log('Health monitoring stopped');
        }
    }
}

module.exports = DeploymentServiceConnector;
