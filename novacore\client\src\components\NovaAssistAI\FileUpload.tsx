/**
 * File Upload Component
 * 
 * This component handles file uploads in the NovaAssistAI chat.
 * It supports drag and drop, file selection, and displays upload progress.
 */

import React, { useState, useRef } from 'react';
import { Upload, X, File, Image, FileText, Archive, Film, Music, Database } from 'lucide-react';
import axios from 'axios';

interface FileUploadProps {
  onUploadComplete: (fileUrls: string[], fileNames: string[]) => void;
  onUploadError: (error: string) => void;
  maxFiles?: number;
  maxSizeMB?: number;
  allowedTypes?: string[];
}

const FileUpload: React.FC<FileUploadProps> = ({
  onUploadComplete,
  onUploadError,
  maxFiles = 5,
  maxSizeMB = 10,
  allowedTypes = ['image/*', 'application/pdf', '.doc', '.docx', '.xls', '.xlsx', '.csv', '.txt', '.json', '.zip', '.rar']
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const [files, setFiles] = useState<File[]>([]);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<Record<string, number>>({});
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const handleDragEnter = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };
  
  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  };
  
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (!isDragging) {
      setIsDragging(true);
    }
  };
  
  const validateFiles = (fileList: FileList | File[]): File[] => {
    const validFiles: File[] = [];
    const maxSizeBytes = maxSizeMB * 1024 * 1024;
    
    // Convert FileList to array
    const fileArray = Array.from(fileList);
    
    // Check if adding these files would exceed the max files limit
    if (files.length + fileArray.length > maxFiles) {
      onUploadError(`You can only upload a maximum of ${maxFiles} files`);
      return validFiles;
    }
    
    for (const file of fileArray) {
      // Check file size
      if (file.size > maxSizeBytes) {
        onUploadError(`File "${file.name}" exceeds the maximum size of ${maxSizeMB}MB`);
        continue;
      }
      
      // Check file type
      const fileType = file.type;
      const fileExtension = `.${file.name.split('.').pop()}`;
      
      const isAllowedType = allowedTypes.some(type => {
        if (type.startsWith('.')) {
          // Check by extension
          return fileExtension.toLowerCase() === type.toLowerCase();
        } else if (type.includes('*')) {
          // Check by MIME type pattern (e.g., image/*)
          const [category] = type.split('/');
          return fileType.startsWith(`${category}/`);
        } else {
          // Check exact MIME type
          return fileType === type;
        }
      });
      
      if (!isAllowedType) {
        onUploadError(`File type "${fileType || fileExtension}" is not allowed`);
        continue;
      }
      
      validFiles.push(file);
    }
    
    return validFiles;
  };
  
  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const validFiles = validateFiles(e.dataTransfer.files);
      if (validFiles.length > 0) {
        setFiles(prevFiles => [...prevFiles, ...validFiles]);
      }
    }
  };
  
  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const validFiles = validateFiles(e.target.files);
      if (validFiles.length > 0) {
        setFiles(prevFiles => [...prevFiles, ...validFiles]);
      }
    }
  };
  
  const handleRemoveFile = (index: number) => {
    setFiles(prevFiles => prevFiles.filter((_, i) => i !== index));
  };
  
  const handleUpload = async () => {
    if (files.length === 0) return;
    
    setUploading(true);
    const uploadedUrls: string[] = [];
    const uploadedNames: string[] = [];
    let hasError = false;
    
    try {
      // Create a new FormData instance for each file to track individual progress
      const uploadPromises = files.map(async (file, index) => {
        const formData = new FormData();
        formData.append('file', file);
        
        try {
          const response = await axios.post('/api/v1/nova-assist/upload', formData, {
            headers: {
              'Content-Type': 'multipart/form-data'
            },
            onUploadProgress: (progressEvent) => {
              if (progressEvent.total) {
                const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
                setUploadProgress(prev => ({
                  ...prev,
                  [file.name]: percentCompleted
                }));
              }
            }
          });
          
          uploadedUrls.push(response.data.url);
          uploadedNames.push(file.name);
          return response;
        } catch (error) {
          hasError = true;
          onUploadError(`Failed to upload "${file.name}"`);
          throw error;
        }
      });
      
      await Promise.all(uploadPromises);
      
      if (!hasError && uploadedUrls.length > 0) {
        onUploadComplete(uploadedUrls, uploadedNames);
        setFiles([]);
        setUploadProgress({});
      }
    } catch (error) {
      console.error('Upload error:', error);
    } finally {
      setUploading(false);
    }
  };
  
  const getFileIcon = (file: File) => {
    const fileType = file.type;
    const fileExtension = `.${file.name.split('.').pop()}`.toLowerCase();
    
    if (fileType.startsWith('image/')) {
      return <Image className="h-5 w-5 text-blue-500" />;
    } else if (fileType === 'application/pdf' || fileExtension === '.pdf') {
      return <FileText className="h-5 w-5 text-red-500" />;
    } else if (fileType.includes('word') || ['.doc', '.docx'].includes(fileExtension)) {
      return <FileText className="h-5 w-5 text-blue-700" />;
    } else if (fileType.includes('excel') || fileType.includes('spreadsheet') || ['.xls', '.xlsx', '.csv'].includes(fileExtension)) {
      return <Database className="h-5 w-5 text-green-600" />;
    } else if (fileType.includes('zip') || fileType.includes('rar') || fileType.includes('tar') || ['.zip', '.rar', '.tar', '.gz'].includes(fileExtension)) {
      return <Archive className="h-5 w-5 text-yellow-600" />;
    } else if (fileType.startsWith('video/')) {
      return <Film className="h-5 w-5 text-purple-500" />;
    } else if (fileType.startsWith('audio/')) {
      return <Music className="h-5 w-5 text-pink-500" />;
    } else {
      return <File className="h-5 w-5 text-gray-500" />;
    }
  };
  
  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return bytes + ' B';
    else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + ' KB';
    else return (bytes / 1048576).toFixed(1) + ' MB';
  };
  
  return (
    <div className="w-full">
      {/* Drag and drop area */}
      <div
        className={`border-2 border-dashed rounded-lg p-4 text-center cursor-pointer transition-colors ${
          isDragging ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-blue-400'
        }`}
        onDragEnter={handleDragEnter}
        onDragLeave={handleDragLeave}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
        onClick={() => fileInputRef.current?.click()}
      >
        <input
          type="file"
          ref={fileInputRef}
          className="hidden"
          multiple
          onChange={handleFileInputChange}
          accept={allowedTypes.join(',')}
        />
        
        <Upload className="h-8 w-8 mx-auto text-blue-500" />
        <p className="mt-2 text-sm text-gray-600">
          Drag and drop files here, or click to select files
        </p>
        <p className="text-xs text-gray-500 mt-1">
          Max {maxFiles} files, up to {maxSizeMB}MB each
        </p>
      </div>
      
      {/* File list */}
      {files.length > 0 && (
        <div className="mt-4">
          <div className="text-sm font-medium mb-2">Selected Files ({files.length})</div>
          <ul className="space-y-2">
            {files.map((file, index) => (
              <li key={`${file.name}-${index}`} className="flex items-center justify-between bg-gray-50 p-2 rounded">
                <div className="flex items-center">
                  {getFileIcon(file)}
                  <span className="ml-2 text-sm truncate max-w-[150px]">{file.name}</span>
                  <span className="ml-2 text-xs text-gray-500">{formatFileSize(file.size)}</span>
                </div>
                
                {uploading ? (
                  <div className="w-20 bg-gray-200 rounded-full h-2.5">
                    <div 
                      className="bg-blue-600 h-2.5 rounded-full" 
                      style={{ width: `${uploadProgress[file.name] || 0}%` }}
                    ></div>
                  </div>
                ) : (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleRemoveFile(index);
                    }}
                    className="text-gray-500 hover:text-red-500"
                  >
                    <X className="h-4 w-4" />
                  </button>
                )}
              </li>
            ))}
          </ul>
          
          {!uploading && (
            <button
              onClick={handleUpload}
              className="mt-3 w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 transition-colors"
              disabled={files.length === 0}
            >
              Upload {files.length} {files.length === 1 ? 'file' : 'files'}
            </button>
          )}
        </div>
      )}
    </div>
  );
};

export default FileUpload;
