"""
Regulation Monitor for the Universal Regulatory Change Management System.

This module provides functionality for monitoring regulatory changes from various sources.
"""

import os
import json
import logging
import threading
import time
import uuid
import requests
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable, Union, Tuple

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RegulationMonitor:
    """
    Monitor for regulatory changes.

    This class is responsible for monitoring regulatory changes from various
    sources (including external APIs) and notifying registered handlers when changes occur.
    """

    def __init__(self, regulations_dir: Optional[str] = None, api_config_path: Optional[str] = None):
        """
        Initialize the Regulation Monitor.

        Args:
            regulations_dir: Path to a directory containing regulation definitions
            api_config_path: Path to a JSON file containing API configurations
        """
        logger.info("Initializing Regulation Monitor")

        # Initialize the regulations dictionary
        self.regulations: Dict[str, Dict[str, Any]] = {}

        # Initialize the changes list
        self.changes: List[Dict[str, Any]] = []

        # Initialize the change handlers list
        self.change_handlers: List[Callable] = []

        # Initialize the monitoring thread
        self.monitoring_thread: Optional[threading.Thread] = None

        # Initialize the monitoring flag
        self.is_monitoring = False

        # Initialize the monitoring interval (in seconds)
        self.monitoring_interval = 60

        # Initialize the API sources dictionary
        self.api_sources: Dict[str, Dict[str, Any]] = {}

        # Initialize the last check times dictionary
        self.last_check_times: Dict[str, datetime] = {}

        # Load default regulations
        self._load_default_regulations()

        # Load custom regulations if provided
        if regulations_dir and os.path.exists(regulations_dir):
            self._load_regulations_from_directory(regulations_dir)

        # Load API configurations if provided
        if api_config_path and os.path.exists(api_config_path):
            self._load_api_configurations(api_config_path)
        else:
            # Load default API configurations
            self._load_default_api_configurations()

        logger.info(f"Regulation Monitor initialized with {len(self.regulations)} regulations and {len(self.api_sources)} API sources")

    def _load_default_api_configurations(self) -> None:
        """Load the default API configurations."""
        # EU Data Protection Board API
        self.api_sources['edpb'] = {
            'id': 'edpb',
            'name': 'European Data Protection Board',
            'description': 'Official source for GDPR guidance and decisions',
            'url': 'https://edpb.europa.eu/news/news_en',
            'type': 'rss',
            'regulation_type': 'gdpr',
            'check_interval': 86400,  # 24 hours in seconds
            'parser': 'edpb_parser'
        }

        # US HHS API
        self.api_sources['hhs'] = {
            'id': 'hhs',
            'name': 'US Department of Health & Human Services',
            'description': 'Official source for HIPAA guidance and decisions',
            'url': 'https://www.hhs.gov/hipaa/newsroom/index.html',
            'type': 'web',
            'regulation_type': 'hipaa',
            'check_interval': 86400,  # 24 hours in seconds
            'parser': 'hhs_parser'
        }

        # NIST API
        self.api_sources['nist'] = {
            'id': 'nist',
            'name': 'National Institute of Standards and Technology',
            'description': 'Official source for NIST guidance and standards',
            'url': 'https://csrc.nist.gov/news',
            'type': 'web',
            'regulation_type': 'nist',
            'check_interval': 86400,  # 24 hours in seconds
            'parser': 'nist_parser'
        }

        # Initialize last check times
        for source_id in self.api_sources:
            self.last_check_times[source_id] = datetime.now() - timedelta(days=7)  # Start by checking last 7 days

    def _load_api_configurations(self, config_path: str) -> None:
        """
        Load API configurations from a JSON file.

        Args:
            config_path: Path to the JSON file containing API configurations
        """
        try:
            # Load the API configurations
            with open(config_path, 'r', encoding='utf-8') as f:
                api_configs = json.load(f)

            # Add each API configuration
            for api_config in api_configs:
                # Validate the API configuration
                if self._validate_api_configuration(api_config):
                    # Add the API configuration
                    self.api_sources[api_config['id']] = api_config
                    # Initialize last check time
                    self.last_check_times[api_config['id']] = datetime.now() - timedelta(days=7)
                    logger.info(f"Loaded API configuration: {api_config['id']} from {config_path}")
                else:
                    logger.warning(f"Invalid API configuration in {config_path}: {api_config.get('id', 'unknown')}")

        except Exception as e:
            logger.error(f"Failed to load API configurations from {config_path}: {e}")

    def _validate_api_configuration(self, api_config: Dict[str, Any]) -> bool:
        """
        Validate an API configuration.

        Args:
            api_config: The API configuration to validate

        Returns:
            True if the API configuration is valid, False otherwise
        """
        # Check required fields
        required_fields = ['id', 'name', 'description', 'url', 'type', 'regulation_type', 'check_interval', 'parser']
        for field in required_fields:
            if field not in api_config:
                logger.warning(f"Missing required field in API configuration: {field}")
                return False

        # Check that check_interval is a positive integer
        if not isinstance(api_config['check_interval'], int) or api_config['check_interval'] <= 0:
            logger.warning(f"Check interval must be a positive integer in API configuration: {api_config['id']}")
            return False

        return True

    def _load_default_regulations(self) -> None:
        """Load the default regulation definitions."""
        # GDPR regulation
        self.regulations['gdpr'] = {
            'id': 'gdpr',
            'name': 'General Data Protection Regulation',
            'description': 'Regulation on data protection and privacy in the European Union',
            'version': '2016/679',
            'effective_date': '2018-05-25',
            'authority': 'European Union',
            'url': 'https://gdpr.eu/tag/gdpr/',
            'requirements': {
                'data_protection': {
                    'id': 'data_protection',
                    'name': 'Data Protection Principles',
                    'description': 'Principles relating to processing of personal data',
                    'articles': ['5', '6', '7', '8']
                },
                'data_subject_rights': {
                    'id': 'data_subject_rights',
                    'name': 'Rights of the Data Subject',
                    'description': 'Rights of individuals whose personal data is processed',
                    'articles': ['12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22']
                },
                'controller_processor': {
                    'id': 'controller_processor',
                    'name': 'Controller and Processor',
                    'description': 'Obligations of data controllers and processors',
                    'articles': ['24', '25', '26', '27', '28', '29', '30', '31']
                },
                'data_transfers': {
                    'id': 'data_transfers',
                    'name': 'Transfers of Personal Data',
                    'description': 'Rules for transferring personal data outside the EU',
                    'articles': ['44', '45', '46', '47', '48', '49', '50']
                },
                'remedies_liability': {
                    'id': 'remedies_liability',
                    'name': 'Remedies, Liability and Penalties',
                    'description': 'Enforcement and penalties for non-compliance',
                    'articles': ['77', '78', '79', '80', '81', '82', '83', '84']
                }
            }
        }

        # HIPAA regulation
        self.regulations['hipaa'] = {
            'id': 'hipaa',
            'name': 'Health Insurance Portability and Accountability Act',
            'description': 'Regulation on data privacy and security for medical information in the United States',
            'version': '1996',
            'effective_date': '1996-08-21',
            'authority': 'United States Department of Health and Human Services',
            'url': 'https://www.hhs.gov/hipaa/index.html',
            'requirements': {
                'privacy_rule': {
                    'id': 'privacy_rule',
                    'name': 'Privacy Rule',
                    'description': 'Standards for privacy of individually identifiable health information',
                    'sections': ['164.500', '164.502', '164.504', '164.506', '164.508', '164.510', '164.512', '164.514', '164.520', '164.522', '164.524', '164.526', '164.528', '164.530']
                },
                'security_rule': {
                    'id': 'security_rule',
                    'name': 'Security Rule',
                    'description': 'Standards for security of electronic protected health information',
                    'sections': ['164.302', '164.304', '164.306', '164.308', '164.310', '164.312', '164.314', '164.316', '164.318']
                },
                'breach_notification': {
                    'id': 'breach_notification',
                    'name': 'Breach Notification Rule',
                    'description': 'Requirements for notification following a breach of unsecured protected health information',
                    'sections': ['164.400', '164.402', '164.404', '164.406', '164.408', '164.410', '164.412', '164.414']
                },
                'enforcement_rule': {
                    'id': 'enforcement_rule',
                    'name': 'Enforcement Rule',
                    'description': 'Compliance and investigations, penalties for violations, and procedures for hearings',
                    'sections': ['160.300', '160.302', '160.304', '160.306', '160.308', '160.310', '160.312', '160.314', '160.316', '160.401', '160.402', '160.404', '160.406', '160.408', '160.410', '160.412', '160.414', '160.416', '160.418', '160.420', '160.422', '160.424', '160.426']
                }
            }
        }

        # SOC 2 regulation
        self.regulations['soc2'] = {
            'id': 'soc2',
            'name': 'Service Organization Control 2',
            'description': 'Framework for managing customer data based on five trust service criteria',
            'version': '2017',
            'effective_date': '2017-01-01',
            'authority': 'American Institute of Certified Public Accountants (AICPA)',
            'url': 'https://www.aicpa.org/interestareas/frc/assuranceadvisoryservices/serviceorganization-smanagement.html',
            'requirements': {
                'security': {
                    'id': 'security',
                    'name': 'Security',
                    'description': 'Information and systems are protected against unauthorized access, unauthorized disclosure of information, and damage to systems',
                    'criteria': ['CC1', 'CC2', 'CC3', 'CC4', 'CC5', 'CC6', 'CC7', 'CC8', 'CC9']
                },
                'availability': {
                    'id': 'availability',
                    'name': 'Availability',
                    'description': 'Information and systems are available for operation and use to meet the entity\'s objectives',
                    'criteria': ['A1']
                },
                'processing_integrity': {
                    'id': 'processing_integrity',
                    'name': 'Processing Integrity',
                    'description': 'System processing is complete, valid, accurate, timely, and authorized',
                    'criteria': ['PI1']
                },
                'confidentiality': {
                    'id': 'confidentiality',
                    'name': 'Confidentiality',
                    'description': 'Information designated as confidential is protected as committed or agreed',
                    'criteria': ['C1']
                },
                'privacy': {
                    'id': 'privacy',
                    'name': 'Privacy',
                    'description': 'Personal information is collected, used, retained, disclosed, and disposed of in conformity with the commitments in the entity\'s privacy notice',
                    'criteria': ['P1', 'P2', 'P3', 'P4', 'P5', 'P6', 'P7', 'P8']
                }
            }
        }

        # NIST Cybersecurity Framework
        self.regulations['nist'] = {
            'id': 'nist',
            'name': 'NIST Cybersecurity Framework',
            'description': 'Framework for improving critical infrastructure cybersecurity',
            'version': '1.1',
            'effective_date': '2018-04-16',
            'authority': 'National Institute of Standards and Technology',
            'url': 'https://www.nist.gov/cyberframework',
            'requirements': {
                'identify': {
                    'id': 'identify',
                    'name': 'Identify',
                    'description': 'Develop organizational understanding to manage cybersecurity risk to systems, people, assets, data, and capabilities',
                    'categories': ['ID.AM', 'ID.BE', 'ID.GV', 'ID.RA', 'ID.RM', 'ID.SC']
                },
                'protect': {
                    'id': 'protect',
                    'name': 'Protect',
                    'description': 'Develop and implement appropriate safeguards to ensure delivery of critical services',
                    'categories': ['PR.AC', 'PR.AT', 'PR.DS', 'PR.IP', 'PR.MA', 'PR.PT']
                },
                'detect': {
                    'id': 'detect',
                    'name': 'Detect',
                    'description': 'Develop and implement appropriate activities to identify the occurrence of a cybersecurity event',
                    'categories': ['DE.AE', 'DE.CM', 'DE.DP']
                },
                'respond': {
                    'id': 'respond',
                    'name': 'Respond',
                    'description': 'Develop and implement appropriate activities to take action regarding a detected cybersecurity incident',
                    'categories': ['RS.RP', 'RS.CO', 'RS.AN', 'RS.MI', 'RS.IM']
                },
                'recover': {
                    'id': 'recover',
                    'name': 'Recover',
                    'description': 'Develop and implement appropriate activities to maintain plans for resilience and to restore any capabilities or services that were impaired due to a cybersecurity incident',
                    'categories': ['RC.RP', 'RC.IM', 'RC.CO']
                }
            }
        }

        # CCPA regulation
        self.regulations['ccpa'] = {
            'id': 'ccpa',
            'name': 'California Consumer Privacy Act',
            'description': 'Regulation on data privacy and consumer protection for residents of California',
            'version': '2018',
            'effective_date': '2020-01-01',
            'authority': 'State of California',
            'url': 'https://oag.ca.gov/privacy/ccpa',
            'requirements': {
                'notice': {
                    'id': 'notice',
                    'name': 'Notice Requirements',
                    'description': 'Requirements for providing notice to consumers about data collection and processing',
                    'sections': ['1798.100', '1798.130']
                },
                'consumer_rights': {
                    'id': 'consumer_rights',
                    'name': 'Consumer Rights',
                    'description': 'Rights of California residents regarding their personal information',
                    'sections': ['1798.100', '1798.105', '1798.110', '1798.115', '1798.120', '1798.125']
                },
                'business_obligations': {
                    'id': 'business_obligations',
                    'name': 'Business Obligations',
                    'description': 'Obligations of businesses that collect personal information',
                    'sections': ['1798.100', '1798.105', '1798.110', '1798.115', '1798.120', '1798.130', '1798.135']
                },
                'exemptions': {
                    'id': 'exemptions',
                    'name': 'Exemptions',
                    'description': 'Exemptions from CCPA requirements',
                    'sections': ['1798.145']
                }
            }
        }

    def _load_regulations_from_directory(self, directory: str) -> None:
        """
        Load regulation definitions from a directory.

        Args:
            directory: Path to the directory containing regulation definition files
        """
        try:
            # Get all JSON files in the directory
            regulation_files = [f for f in os.listdir(directory) if f.endswith('.json')]

            for regulation_file in regulation_files:
                try:
                    # Load the regulation definition
                    file_path = os.path.join(directory, regulation_file)
                    with open(file_path, 'r', encoding='utf-8') as f:
                        regulation_def = json.load(f)

                    # Validate the regulation definition
                    if self._validate_regulation(regulation_def):
                        # Add the regulation definition
                        self.regulations[regulation_def['id']] = regulation_def
                        logger.info(f"Loaded regulation: {regulation_def['id']} from {file_path}")
                    else:
                        logger.warning(f"Invalid regulation definition in {file_path}")

                except Exception as e:
                    logger.error(f"Failed to load regulation from {regulation_file}: {e}")

        except Exception as e:
            logger.error(f"Failed to load regulations from directory {directory}: {e}")

    def _validate_regulation(self, regulation_def: Dict[str, Any]) -> bool:
        """
        Validate a regulation definition.

        Args:
            regulation_def: The regulation definition to validate

        Returns:
            True if the regulation definition is valid, False otherwise
        """
        # Check required fields
        required_fields = ['id', 'name', 'description', 'version', 'effective_date', 'authority', 'url', 'requirements']
        for field in required_fields:
            if field not in regulation_def:
                logger.warning(f"Missing required field in regulation definition: {field}")
                return False

        # Check that requirements is a dictionary
        if not isinstance(regulation_def['requirements'], dict):
            logger.warning(f"Requirements must be a dictionary in regulation definition: {regulation_def['id']}")
            return False

        return True

    def register_change_handler(self, handler: Callable) -> None:
        """
        Register a handler for regulatory changes.

        Args:
            handler: The handler function
        """
        self.change_handlers.append(handler)
        logger.info("Registered regulatory change handler")

    def unregister_change_handler(self, handler: Callable) -> None:
        """
        Unregister a handler for regulatory changes.

        Args:
            handler: The handler function
        """
        if handler in self.change_handlers:
            self.change_handlers.remove(handler)
            logger.info("Unregistered regulatory change handler")

    def start(self) -> None:
        """Start monitoring for regulatory changes."""
        if self.is_monitoring:
            logger.warning("Regulation monitoring is already running")
            return

        logger.info("Starting regulation monitoring")

        # Set the monitoring flag
        self.is_monitoring = True

        # Create and start the monitoring thread
        self.monitoring_thread = threading.Thread(target=self._monitoring_loop)
        self.monitoring_thread.daemon = True
        self.monitoring_thread.start()

    def stop(self) -> None:
        """Stop monitoring for regulatory changes."""
        if not self.is_monitoring:
            logger.warning("Regulation monitoring is not running")
            return

        logger.info("Stopping regulation monitoring")

        # Clear the monitoring flag
        self.is_monitoring = False

        # Wait for the monitoring thread to terminate
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5.0)
            self.monitoring_thread = None

    def _monitoring_loop(self) -> None:
        """Monitoring loop for regulatory changes."""
        logger.info("Regulation monitoring loop started")

        while self.is_monitoring:
            try:
                # Check for regulatory changes
                self._check_for_changes()

                # Sleep for the monitoring interval
                time.sleep(self.monitoring_interval)

            except Exception as e:
                logger.error(f"Error in regulation monitoring loop: {e}")

                # Sleep for a short time before retrying
                time.sleep(5.0)

        logger.info("Regulation monitoring loop stopped")

    def _check_for_changes(self) -> None:
        """Check for regulatory changes from various sources."""
        logger.debug("Checking for regulatory changes")

        # Check for changes from API sources
        for source_id, source_config in self.api_sources.items():
            try:
                # Check if it's time to check this source
                now = datetime.now()
                last_check_time = self.last_check_times.get(source_id, datetime.min)
                check_interval = source_config.get('check_interval', 86400)  # Default to 24 hours

                if (now - last_check_time).total_seconds() >= check_interval:
                    # Update the last check time
                    self.last_check_times[source_id] = now

                    # Check for changes from this source
                    logger.info(f"Checking for changes from source: {source_id}")
                    changes = self._check_source_for_changes(source_id, source_config)

                    # Process any changes found
                    for change in changes:
                        # Add the change to the changes list
                        self.changes.append(change)

                        # Notify change handlers
                        self._notify_change_handlers(change)

            except Exception as e:
                logger.error(f"Error checking source {source_id} for changes: {e}")

        # For demonstration purposes, also include simulated changes
        # In a production environment, you would remove this code
        if len(self.changes) == 0:
            self._simulate_gdpr_change()

        if len(self.changes) == 1:
            self._simulate_hipaa_change()

        # Simulate a NIST change
        if len(self.changes) == 2:
            self._simulate_nist_change()

        # Simulate a CCPA change
        if len(self.changes) == 3:
            self._simulate_ccpa_change()

    def _check_source_for_changes(self, source_id: str, source_config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Check a specific source for regulatory changes.

        Args:
            source_id: The ID of the source
            source_config: The configuration of the source

        Returns:
            List of regulatory changes
        """
        # Get the source type
        source_type = source_config.get('type', 'web')

        # Check for changes based on the source type
        if source_type == 'rss':
            return self._check_rss_source_for_changes(source_id, source_config)
        elif source_type == 'web':
            return self._check_web_source_for_changes(source_id, source_config)
        elif source_type == 'api':
            return self._check_api_source_for_changes(source_id, source_config)
        else:
            logger.warning(f"Unsupported source type: {source_type}")
            return []

    def _check_rss_source_for_changes(self, source_id: str, source_config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Check an RSS source for regulatory changes.

        Args:
            source_id: The ID of the source
            source_config: The configuration of the source

        Returns:
            List of regulatory changes
        """
        # In a real implementation, this would fetch and parse an RSS feed
        # For now, return an empty list
        logger.info(f"Checking RSS source for changes: {source_id}")
        return []

    def _check_web_source_for_changes(self, source_id: str, source_config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Check a web source for regulatory changes.

        Args:
            source_id: The ID of the source
            source_config: The configuration of the source

        Returns:
            List of regulatory changes
        """
        # In a real implementation, this would fetch and parse a web page
        # For now, return an empty list
        logger.info(f"Checking web source for changes: {source_id}")
        return []

    def _check_api_source_for_changes(self, source_id: str, source_config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Check an API source for regulatory changes.

        Args:
            source_id: The ID of the source
            source_config: The configuration of the source

        Returns:
            List of regulatory changes
        """
        # In a real implementation, this would call an API
        # For now, return an empty list
        logger.info(f"Checking API source for changes: {source_id}")
        return []

    def _simulate_nist_change(self) -> None:
        """Simulate a NIST regulatory change."""
        logger.info("Simulating NIST regulatory change")

        # Create a change object
        change = {
            'id': 'nist_change_1',
            'regulation_id': 'nist',
            'regulation_type': 'nist',
            'title': 'Updated Cybersecurity Framework',
            'description': 'Updated framework for improving critical infrastructure cybersecurity',
            'change_date': '2023-07-01',
            'effective_date': '2023-11-01',
            'url': 'https://www.nist.gov/cyberframework',
            'affected_requirements': ['identify', 'protect'],
            'change_details': {
                'identify': {
                    'old_value': 'Develop organizational understanding to manage cybersecurity risk to systems, people, assets, data, and capabilities',
                    'new_value': 'Enhanced requirements for developing organizational understanding to manage cybersecurity risk to systems, people, assets, data, and capabilities'
                },
                'protect': {
                    'old_value': 'Develop and implement appropriate safeguards to ensure delivery of critical services',
                    'new_value': 'Enhanced requirements for developing and implementing appropriate safeguards to ensure delivery of critical services'
                }
            }
        }

        # Add the change to the changes list
        self.changes.append(change)

        # Notify change handlers
        self._notify_change_handlers(change)

    def _simulate_ccpa_change(self) -> None:
        """Simulate a CCPA regulatory change."""
        logger.info("Simulating CCPA regulatory change")

        # Create a change object
        change = {
            'id': 'ccpa_change_1',
            'regulation_id': 'ccpa',
            'regulation_type': 'ccpa',
            'title': 'Updated Consumer Rights Requirements',
            'description': 'Updated requirements for handling consumer rights requests',
            'change_date': '2023-08-01',
            'effective_date': '2023-12-01',
            'url': 'https://oag.ca.gov/privacy/ccpa',
            'affected_requirements': ['consumer_rights'],
            'change_details': {
                'consumer_rights': {
                    'old_value': 'Rights of California residents regarding their personal information',
                    'new_value': 'Enhanced rights of California residents regarding their personal information, including new requirements for responding to requests'
                }
            }
        }

        # Add the change to the changes list
        self.changes.append(change)

        # Notify change handlers
        self._notify_change_handlers(change)

    def _simulate_gdpr_change(self) -> None:
        """Simulate a GDPR regulatory change."""
        logger.info("Simulating GDPR regulatory change")

        # Create a change object
        change = {
            'id': 'gdpr_change_1',
            'regulation_id': 'gdpr',
            'regulation_type': 'gdpr',
            'title': 'Updated Data Subject Rights',
            'description': 'Updated requirements for handling data subject rights requests',
            'change_date': '2023-06-01',
            'effective_date': '2023-09-01',
            'url': 'https://gdpr.eu/tag/gdpr/',
            'affected_requirements': ['data_subject_rights'],
            'change_details': {
                'data_subject_rights': {
                    'old_value': 'Rights of individuals whose personal data is processed',
                    'new_value': 'Enhanced rights of individuals whose personal data is processed, including stricter time limits for responses'
                }
            }
        }

        # Add the change to the changes list
        self.changes.append(change)

        # Notify change handlers
        self._notify_change_handlers(change)

    def _simulate_hipaa_change(self) -> None:
        """Simulate a HIPAA regulatory change."""
        logger.info("Simulating HIPAA regulatory change")

        # Create a change object
        change = {
            'id': 'hipaa_change_1',
            'regulation_id': 'hipaa',
            'regulation_type': 'hipaa',
            'title': 'Updated Breach Notification Requirements',
            'description': 'Updated requirements for notifying individuals of data breaches',
            'change_date': '2023-06-15',
            'effective_date': '2023-10-01',
            'url': 'https://www.hhs.gov/hipaa/index.html',
            'affected_requirements': ['breach_notification'],
            'change_details': {
                'breach_notification': {
                    'old_value': 'Requirements for notification following a breach of unsecured protected health information',
                    'new_value': 'Enhanced requirements for notification following a breach of unsecured protected health information, including shorter notification timeframes'
                }
            }
        }

        # Add the change to the changes list
        self.changes.append(change)

        # Notify change handlers
        self._notify_change_handlers(change)

    def _notify_change_handlers(self, change: Dict[str, Any]) -> None:
        """
        Notify registered change handlers of a regulatory change.

        Args:
            change: The regulatory change
        """
        logger.info(f"Notifying change handlers of regulatory change: {change['id']}")

        # Call all registered change handlers
        for handler in self.change_handlers:
            try:
                handler(change)
            except Exception as e:
                logger.error(f"Error in change handler: {e}")

    def get_regulation_types(self) -> List[str]:
        """
        Get all supported regulation types.

        Returns:
            List of regulation types
        """
        return list(self.regulations.keys())

    def get_regulations(self, regulation_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get all regulations, optionally filtered by type.

        Args:
            regulation_type: Optional regulation type filter

        Returns:
            List of regulations
        """
        if regulation_type:
            return [reg for reg_id, reg in self.regulations.items() if reg_id == regulation_type]
        else:
            return list(self.regulations.values())

    def get_regulation(self, regulation_id: str) -> Dict[str, Any]:
        """
        Get a specific regulation.

        Args:
            regulation_id: The ID of the regulation

        Returns:
            The regulation

        Raises:
            ValueError: If the regulation does not exist
        """
        if regulation_id not in self.regulations:
            raise ValueError(f"Regulation not found: {regulation_id}")

        return self.regulations[regulation_id]

    def get_changes(self, regulation_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get all regulatory changes, optionally filtered by type.

        Args:
            regulation_type: Optional regulation type filter

        Returns:
            List of regulatory changes
        """
        if regulation_type:
            return [change for change in self.changes if change['regulation_type'] == regulation_type]
        else:
            return self.changes

    def get_change(self, change_id: str) -> Dict[str, Any]:
        """
        Get a specific regulatory change.

        Args:
            change_id: The ID of the regulatory change

        Returns:
            The regulatory change

        Raises:
            ValueError: If the change does not exist
        """
        for change in self.changes:
            if change['id'] == change_id:
                return change

        raise ValueError(f"Regulatory change not found: {change_id}")
