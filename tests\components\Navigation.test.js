import React from 'react';
import { render, screen, fireEvent, cleanup } from '@testing-library/react';
import Navigation from '../../components/Navigation';

// Mock Next.js router
jest.mock('next/router', () => ({
  useRouter: jest.fn().mockReturnValue({
    pathname: '/',
    push: jest.fn(),
  }),
}));

// Mock Next.js Link component
jest.mock('next/link', () => {
  return ({ children, href, className, onClick }) => {
    return (
      <a href={href} className={className} onClick={onClick} data-testid={`link-${href.replace(/\//g, '-')}`}>
        {children}
      </a>
    );
  };
});

describe('Navigation', () => {
  beforeEach(() => {
    // Reset mocks between tests
    jest.clearAllMocks();

    // Mock document.addEventListener and removeEventListener
    document.addEventListener = jest.fn();
    document.removeEventListener = jest.fn();
  });

  afterEach(() => {
    cleanup();
  });

  it('renders the logo and navigation links', () => {
    render(<Navigation />);

    // Check if the logo is rendered
    expect(screen.getByText('NovaFuse API Superstore')).toBeInTheDocument();

    // Check if main navigation links are rendered
    expect(screen.getByText('Home')).toBeInTheDocument();
    expect(screen.getByText('Products')).toBeInTheDocument();
    expect(screen.getByText('Solutions')).toBeInTheDocument();
    expect(screen.getByText('Partner Empowerment')).toBeInTheDocument();
    expect(screen.getByText('Resources')).toBeInTheDocument();
    expect(screen.getByText('About Us')).toBeInTheDocument();
    expect(screen.getByText('Contact')).toBeInTheDocument();

    // Check if sign in/up buttons are rendered
    expect(screen.getByText('Sign In')).toBeInTheDocument();
    expect(screen.getByText('Sign Up')).toBeInTheDocument();
  });

  it('toggles mobile menu when menu button is clicked', () => {
    render(<Navigation />);

    // Mobile menu should be hidden initially
    expect(screen.queryByText('Products', { selector: 'h3' })).not.toBeInTheDocument();

    // Find and click the mobile menu button (the first button in the nav)
    const menuButton = screen.getByRole('button', { name: '' });
    fireEvent.click(menuButton);

    // Mobile menu should now be visible
    expect(screen.getByText('Products', { selector: 'h3' })).toBeInTheDocument();
    expect(screen.getByText('Solutions', { selector: 'h3' })).toBeInTheDocument();
    expect(screen.getByText('Resources', { selector: 'h3' })).toBeInTheDocument();

    // Click the menu button again to close
    fireEvent.click(menuButton);

    // Mobile menu should be hidden again
    expect(screen.queryByText('Products', { selector: 'h3' })).not.toBeInTheDocument();
  });

  it('toggles dropdowns when dropdown buttons are clicked', () => {
    render(<Navigation />);

    // Products dropdown should be hidden initially
    expect(screen.queryByText('NovaConnect UAC', { selector: 'a' })).not.toBeInTheDocument();

    // Find and click the Products dropdown button
    const productsButton = screen.getByRole('button', { name: /products/i });
    fireEvent.click(productsButton);

    // Products dropdown should now be visible
    expect(screen.getByText('NovaConnect UAC')).toBeInTheDocument();
    expect(screen.getByText('UAC FAQ')).toBeInTheDocument();
    expect(screen.getByText('NovaGRC Suite')).toBeInTheDocument();

    // Click the Solutions dropdown button
    const solutionsButton = screen.getByRole('button', { name: /solutions/i });
    fireEvent.click(solutionsButton);

    // Products dropdown should be hidden and Solutions dropdown should be visible
    expect(screen.queryByText('NovaConnect UAC')).not.toBeInTheDocument();
    expect(screen.getByText('By Industry')).toBeInTheDocument();
    expect(screen.getByText('By Use Case')).toBeInTheDocument();

    // Click the Solutions button again to close it
    fireEvent.click(solutionsButton);

    // Solutions dropdown should be hidden
    expect(screen.queryByText('By Industry')).not.toBeInTheDocument();

    // Click the Resources dropdown button
    const resourcesButton = screen.getByRole('button', { name: /resources/i });
    fireEvent.click(resourcesButton);

    // Resources dropdown should be visible
    expect(screen.getByText('Developer Portal')).toBeInTheDocument();
    expect(screen.getByText('API Docs')).toBeInTheDocument();
    expect(screen.getByText('UAC Demo')).toBeInTheDocument();

    // Click the Products button again to toggle back
    fireEvent.click(productsButton);

    // Resources dropdown should be hidden and Products dropdown should be visible
    expect(screen.queryByText('Developer Portal')).not.toBeInTheDocument();
    expect(screen.getByText('NovaConnect UAC')).toBeInTheDocument();
  });

  it('applies active styles to current route', () => {
    // Test multiple routes to ensure isActive function works correctly
    const routesToTest = [
      { path: '/', linkText: 'Home' },
      { path: '/about', linkText: 'About Us' },
      { path: '/contact', linkText: 'Contact' },
      { path: '/partner-empowerment', linkText: 'Partner Empowerment' }
    ];

    for (const route of routesToTest) {
      // Mock the router to return the specific path
      const useRouter = jest.requireMock('next/router').useRouter;
      useRouter.mockReturnValue({
        pathname: route.path,
        push: jest.fn(),
      });

      // Re-render with new router value
      cleanup();
      render(<Navigation />);

      // The current route link should have the active class (text-blue-400)
      const activeLink = screen.getByText(route.linkText);
      expect(activeLink).toHaveClass('text-blue-400');

      // Other links should not have the active class
      const inactiveLinks = Array.from(screen.getAllByRole('link'))
        .filter(link => link.textContent !== route.linkText && !link.textContent.includes('NovaFuse'));

      // Check at least one inactive link to verify it doesn't have the active class
      if (inactiveLinks.length > 0) {
        expect(inactiveLinks[0]).not.toHaveClass('text-blue-400');
      }
    }
  });

  it('applies active styles to dropdown buttons when child routes are active', () => {
    // Test all product routes
    const productRoutes = [
      '/novaconnect-uac',
      '/novagrc-suite',
      '/novaconcierge',
      '/nova-ui-components'
    ];

    for (const route of productRoutes) {
      // Mock the router to return a product page path
      const useRouter = jest.requireMock('next/router').useRouter;
      useRouter.mockReturnValue({
        pathname: route,
        push: jest.fn(),
      });

      // Re-render with new router value
      cleanup();
      render(<Navigation />);

      // The Products dropdown button should have the active class
      const productsButton = screen.getByRole('button', { name: /products/i });
      expect(productsButton).toHaveClass('text-blue-400');
    }

    // Test all solution routes
    const solutionRoutes = [
      '/solutions/by-industry',
      '/solutions/by-use-case',
      '/solutions/by-framework'
    ];

    for (const route of solutionRoutes) {
      // Mock the router to return a solutions page path
      const useRouter = jest.requireMock('next/router').useRouter;
      useRouter.mockReturnValue({
        pathname: route,
        push: jest.fn(),
      });

      // Re-render with new router value
      cleanup();
      render(<Navigation />);

      // The Solutions dropdown button should have the active class
      const solutionsButton = screen.getByRole('button', { name: /solutions/i });
      expect(solutionsButton).toHaveClass('text-blue-400');
    }

    // Test all resource routes
    const resourceRoutes = [
      '/api-docs',
      '/uac-demo',
      '/partner-knowledge-base',
      '/resources/white-papers'
    ];

    for (const route of resourceRoutes) {
      // Mock the router to return a resources page path
      const useRouter = jest.requireMock('next/router').useRouter;
      useRouter.mockReturnValue({
        pathname: route,
        push: jest.fn(),
      });

      // Re-render with new router value
      cleanup();
      render(<Navigation />);

      // The Resources dropdown button should have the active class
      const resourcesButton = screen.getByRole('button', { name: /resources/i });
      expect(resourcesButton).toHaveClass('text-blue-400');
    }
  });

  it('handles link clicks and closes dropdowns', () => {
    render(<Navigation />);

    // Open the Products dropdown
    const productsButton = screen.getByRole('button', { name: /products/i });
    fireEvent.click(productsButton);

    // Products dropdown should be visible
    expect(screen.getByText('NovaConnect UAC')).toBeInTheDocument();

    // Click a link in the dropdown
    const uacLink = screen.getByText('NovaConnect UAC');
    fireEvent.click(uacLink);

    // Dropdown should be closed
    expect(screen.queryByText('NovaConnect UAC', { selector: 'a' })).not.toBeInTheDocument();
  });

  it('closes dropdown when clicking outside', () => {
    render(<Navigation />);

    // Open the Products dropdown
    const productsButton = screen.getByRole('button', { name: /products/i });
    fireEvent.click(productsButton);

    // Products dropdown should be visible
    expect(screen.getByText('NovaConnect UAC')).toBeInTheDocument();

    // Get the mousedown handler
    const handleClickOutside = document.addEventListener.mock.calls[0][1];

    // Simulate a click outside the dropdown
    handleClickOutside({ target: document.body });

    // Re-render to reflect state changes
    cleanup();
    render(<Navigation />);

    // Dropdown should be closed
    expect(screen.queryByText('NovaConnect UAC', { selector: 'a' })).not.toBeInTheDocument();
  });

  it('does not close dropdown when clicking inside dropdown button', () => {
    render(<Navigation />);

    // Open the Products dropdown
    const productsButton = screen.getByRole('button', { name: /products/i });
    fireEvent.click(productsButton);

    // Products dropdown should be visible
    expect(screen.getByText('NovaConnect UAC')).toBeInTheDocument();

    // Get the mousedown handler
    const handleClickOutside = document.addEventListener.mock.calls[0][1];

    // Mock closest to return true for dropdown-button
    const mockTarget = {
      closest: (selector) => selector === '.dropdown-button'
    };

    // Simulate a click on the dropdown button
    handleClickOutside({ target: mockTarget });

    // Dropdown should still be visible
    expect(screen.getByText('NovaConnect UAC')).toBeInTheDocument();
  });

  it('does not close dropdown when clicking inside dropdown menu', () => {
    render(<Navigation />);

    // Open the Products dropdown
    const productsButton = screen.getByRole('button', { name: /products/i });
    fireEvent.click(productsButton);

    // Products dropdown should be visible
    expect(screen.getByText('NovaConnect UAC')).toBeInTheDocument();

    // Get the mousedown handler
    const handleClickOutside = document.addEventListener.mock.calls[0][1];

    // Mock closest to return true for dropdown-menu
    const mockTarget = {
      closest: (selector) => selector === '.dropdown-menu'
    };

    // Simulate a click inside the dropdown menu
    handleClickOutside({ target: mockTarget });

    // Dropdown should still be visible
    expect(screen.getByText('NovaConnect UAC')).toBeInTheDocument();
  });

  it('sets up and cleans up event listeners', () => {
    const { unmount } = render(<Navigation />);

    // Check if addEventListener was called
    expect(document.addEventListener).toHaveBeenCalledWith('mousedown', expect.any(Function));

    // Unmount the component
    unmount();

    // Check if removeEventListener was called during cleanup
    expect(document.removeEventListener).toHaveBeenCalledWith('mousedown', expect.any(Function));
  });

  it('renders mobile menu with correct active styles', () => {
    // Mock the router to return a specific path
    const useRouter = jest.requireMock('next/router').useRouter;
    useRouter.mockReturnValue({
      pathname: '/about',
      push: jest.fn(),
    });

    render(<Navigation />);

    // Open the mobile menu
    const menuButton = screen.getByRole('button', { name: '' });
    fireEvent.click(menuButton);

    // The About Us link in mobile menu should have the active class
    const mobileLinks = screen.getAllByText('About Us');
    const mobileAboutLink = mobileLinks[mobileLinks.length - 1]; // Get the one in the mobile menu
    expect(mobileAboutLink).toHaveClass('text-blue-400');

    // Other links should not have the active class
    const mobileHomeLinks = screen.getAllByText('Home');
    const mobileHomeLink = mobileHomeLinks[mobileHomeLinks.length - 1]; // Get the one in the mobile menu
    expect(mobileHomeLink).not.toHaveClass('text-blue-400');
  });

  it('applies active styles to mobile menu product links', () => {
    // Test product route
    const useRouter = jest.requireMock('next/router').useRouter;
    useRouter.mockReturnValue({
      pathname: '/novaconnect-uac',
      push: jest.fn(),
    });

    render(<Navigation />);

    // Open the mobile menu
    const menuButton = screen.getByRole('button', { name: '' });
    fireEvent.click(menuButton);

    // The NovaConnect UAC link in mobile menu should have the active class
    const uacLinks = screen.getAllByText('NovaConnect UAC');
    expect(uacLinks[0]).toHaveClass('text-blue-400');

    // Other product links should not have the active class
    const grcLinks = screen.getAllByText('NovaGRC Suite');
    expect(grcLinks[0]).not.toHaveClass('text-blue-400');

    // Test solution route
    cleanup();
    useRouter.mockReturnValue({
      pathname: '/solutions/by-industry',
      push: jest.fn(),
    });

    render(<Navigation />);

    // Open the mobile menu
    const menuButton2 = screen.getByRole('button', { name: '' });
    fireEvent.click(menuButton2);

    // The By Industry link in mobile menu should have the active class
    const industryLinks = screen.getAllByText('By Industry');
    expect(industryLinks[0]).toHaveClass('text-blue-400');

    // Other solution links should not have the active class
    const useCaseLinks = screen.getAllByText('By Use Case');
    expect(useCaseLinks[0]).not.toHaveClass('text-blue-400');

    // Test resource route
    cleanup();
    useRouter.mockReturnValue({
      pathname: '/api-docs',
      push: jest.fn(),
    });

    render(<Navigation />);

    // Open the mobile menu
    const menuButton3 = screen.getByRole('button', { name: '' });
    fireEvent.click(menuButton3);

    // The API Docs link in mobile menu should have the active class
    const apiDocsLinks = screen.getAllByText('API Docs');
    expect(apiDocsLinks[0]).toHaveClass('text-blue-400');

    // Other resource links should not have the active class
    const demoLinks = screen.getAllByText('UAC Demo');
    expect(demoLinks[0]).not.toHaveClass('text-blue-400');
  });
});
