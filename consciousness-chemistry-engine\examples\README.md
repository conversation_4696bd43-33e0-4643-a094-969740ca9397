# ConsciousNovaFold Examples

This directory contains example scripts and reports demonstrating the capabilities of the ConsciousNovaFold pipeline.

## Available Examples

### 1. Generate Reports

Generate comprehensive reports showing the results of the ConsciousNovaFold pipeline, including:

- Consciousness metrics (NERS, NEPI, NEFC)
- Evolutionary conservation analysis
- Domain architecture with Fibonacci alignment
- Visualization of results

#### How to Run

1. Install the required dependencies:
   ```bash
   pip install -r requirements.txt
   ```

2. Run the report generator:
   ```bash
   python generate_reports.py
   ```

3. View the generated report in the `reports/` directory.

## Report Contents

Each generated report includes:

1. **Pipeline Overview**
   - Input sequence information
   - Processing summary

2. **Consciousness Metrics**
   - Trinity Validation scores (NERS, NEPI, NEFC)
   - Fibonacci alignment analysis
   - Pass/fail status for each validation component

3. **Visualizations**
   - Evolutionary conservation plots
   - Domain architecture diagrams
   - Structural annotations

## Customizing the Report

To analyze your own protein sequence, modify the `EXAMPLE_SEQUENCE` variable in `generate_reports.py` with your desired sequence.

## Output Files

- `report_<timestamp>/report.md`: Main report in Markdown format
- `report_<timestamp>/plots/`: Directory containing generated visualizations
- `report_<timestamp>/data/`: Directory containing raw data files (if any)

## Requirements

- Python 3.8+
- Dependencies listed in `requirements.txt`
- Optional: PyMOL for 3D structure visualization
