"""
Impact Analyzer for the Universal Regulatory Change Management System.

This module provides functionality for analyzing the impact of regulatory changes,
including cross-framework impact analysis and detailed risk scoring.
"""

import logging
import math
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple, Set, Union

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ImpactAnalyzer:
    """
    Analyzer for the impact of regulatory changes.

    This class is responsible for analyzing the impact of regulatory changes
    on an organization's compliance posture, including cross-framework impact analysis
    and detailed risk scoring.
    """

    # Risk score weights
    RISK_WEIGHTS = {
        'compliance_level': 0.3,
        'affected_requirements': 0.2,
        'affected_controls': 0.2,
        'affected_gaps': 0.15,
        'implementation_time': 0.15
    }

    # Risk levels
    RISK_LEVELS = {
        'low': {'min': 0, 'max': 3.0},
        'medium': {'min': 3.0, 'max': 7.0},
        'high': {'min': 7.0, 'max': 10.0}
    }

    # Cross-framework mappings
    CROSS_FRAMEWORK_MAPPINGS = {
        'gdpr': {
            'data_protection': ['hipaa:security_rule', 'nist:protect', 'ccpa:business_obligations'],
            'data_subject_rights': ['hipaa:privacy_rule', 'ccpa:consumer_rights'],
            'controller_processor': ['hipaa:business_associate', 'nist:identify'],
            'data_transfers': ['hipaa:security_rule', 'nist:protect'],
            'remedies_liability': ['hipaa:enforcement_rule']
        },
        'hipaa': {
            'privacy_rule': ['gdpr:data_subject_rights', 'ccpa:consumer_rights'],
            'security_rule': ['gdpr:data_protection', 'nist:protect', 'nist:detect'],
            'breach_notification': ['gdpr:data_protection', 'nist:respond'],
            'enforcement_rule': ['gdpr:remedies_liability']
        },
        'nist': {
            'identify': ['gdpr:controller_processor', 'hipaa:security_rule'],
            'protect': ['gdpr:data_protection', 'hipaa:security_rule', 'ccpa:business_obligations'],
            'detect': ['hipaa:security_rule'],
            'respond': ['hipaa:breach_notification'],
            'recover': ['hipaa:security_rule']
        },
        'ccpa': {
            'notice': ['gdpr:data_protection'],
            'consumer_rights': ['gdpr:data_subject_rights', 'hipaa:privacy_rule'],
            'business_obligations': ['gdpr:data_protection', 'nist:protect'],
            'exemptions': []
        }
    }

    def __init__(self, cross_framework_analysis: bool = True):
        """
        Initialize the Impact Analyzer.

        Args:
            cross_framework_analysis: Whether to perform cross-framework impact analysis
        """
        logger.info("Initializing Impact Analyzer")

        # Initialize the organization profile
        self.organization_profile: Dict[str, Any] = {}

        # Initialize the compliance posture
        self.compliance_posture: Dict[str, Any] = {}

        # Set cross-framework analysis flag
        self.cross_framework_analysis = cross_framework_analysis

        # Load default organization profile and compliance posture
        self._load_default_profile()

        logger.info(f"Impact Analyzer initialized (cross-framework analysis: {cross_framework_analysis})")

    def _load_default_profile(self) -> None:
        """Load the default organization profile and compliance posture."""
        # Default organization profile
        self.organization_profile = {
            'industry': 'technology',
            'size': 'medium',
            'regions': ['us', 'eu', 'asia'],
            'data_types': ['personal', 'financial', 'health'],
            'systems': ['cloud', 'on_premise', 'mobile'],
            'third_parties': ['vendors', 'partners', 'service_providers']
        }

        # Default compliance posture
        self.compliance_posture = {
            'gdpr': {
                'compliance_level': 'high',
                'implemented_controls': [
                    'data_protection_officer',
                    'data_protection_impact_assessment',
                    'data_subject_rights_process',
                    'consent_management',
                    'data_breach_notification_process'
                ],
                'gaps': [
                    'cross_border_data_transfer_mechanism'
                ]
            },
            'hipaa': {
                'compliance_level': 'medium',
                'implemented_controls': [
                    'security_risk_assessment',
                    'access_controls',
                    'audit_controls',
                    'integrity_controls',
                    'transmission_security'
                ],
                'gaps': [
                    'business_associate_agreements',
                    'device_and_media_controls'
                ]
            },
            'soc2': {
                'compliance_level': 'high',
                'implemented_controls': [
                    'security_policies',
                    'risk_management',
                    'system_operations',
                    'change_management',
                    'logical_access',
                    'physical_access',
                    'system_monitoring',
                    'incident_management'
                ],
                'gaps': [
                    'vendor_management'
                ]
            }
        }

    def set_organization_profile(self, profile: Dict[str, Any]) -> None:
        """
        Set the organization profile.

        Args:
            profile: The organization profile
        """
        self.organization_profile = profile
        logger.info("Updated organization profile")

    def set_compliance_posture(self, posture: Dict[str, Any]) -> None:
        """
        Set the compliance posture.

        Args:
            posture: The compliance posture
        """
        self.compliance_posture = posture
        logger.info("Updated compliance posture")

    def analyze_impact(self, change: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze the impact of a regulatory change.

        Args:
            change: The regulatory change

        Returns:
            The impact analysis
        """
        logger.info(f"Analyzing impact of regulatory change: {change.get('id')}")

        # Get the regulation type
        regulation_type = change.get('regulation_type')

        # Check if we have compliance posture for this regulation
        if regulation_type not in self.compliance_posture:
            logger.warning(f"No compliance posture found for regulation type: {regulation_type}")
            return self._create_default_impact_analysis(change)

        # Get the compliance posture for this regulation
        posture = self.compliance_posture[regulation_type]

        # Analyze the impact based on the change and posture
        impact = self._analyze_impact_for_regulation(change, posture)

        # Perform cross-framework impact analysis if enabled
        if self.cross_framework_analysis:
            cross_framework_impact = self._analyze_cross_framework_impact(change, impact)
            impact['cross_framework_impact'] = cross_framework_impact

        logger.info(f"Impact analysis completed for regulatory change: {change.get('id')}")

        return impact

    def _analyze_cross_framework_impact(self, change: Dict[str, Any], primary_impact: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze the cross-framework impact of a regulatory change.

        Args:
            change: The regulatory change
            primary_impact: The primary impact analysis

        Returns:
            The cross-framework impact analysis
        """
        logger.info(f"Analyzing cross-framework impact of regulatory change: {change.get('id')}")

        # Get the regulation type and affected requirements
        regulation_type = change.get('regulation_type')
        affected_requirements = change.get('affected_requirements', [])

        # Initialize cross-framework impact
        cross_framework_impact = {
            'affected_frameworks': {},
            'overall_impact': 'low'
        }

        # Check if we have cross-framework mappings for this regulation
        if regulation_type not in self.CROSS_FRAMEWORK_MAPPINGS:
            logger.warning(f"No cross-framework mappings found for regulation type: {regulation_type}")
            return cross_framework_impact

        # Get the cross-framework mappings for this regulation
        mappings = self.CROSS_FRAMEWORK_MAPPINGS[regulation_type]

        # Track the highest impact level across frameworks
        highest_impact_level = 'low'

        # Analyze impact on each affected requirement
        for requirement in affected_requirements:
            # Check if we have mappings for this requirement
            if requirement not in mappings:
                logger.debug(f"No cross-framework mappings found for requirement: {requirement}")
                continue

            # Get the mapped requirements
            mapped_requirements = mappings[requirement]

            # Analyze impact on each mapped requirement
            for mapped_requirement in mapped_requirements:
                # Parse the mapped requirement
                parts = mapped_requirement.split(':')
                if len(parts) != 2:
                    logger.warning(f"Invalid mapped requirement format: {mapped_requirement}")
                    continue

                mapped_framework, mapped_req = parts

                # Initialize the framework impact if not already present
                if mapped_framework not in cross_framework_impact['affected_frameworks']:
                    cross_framework_impact['affected_frameworks'][mapped_framework] = {
                        'affected_requirements': [],
                        'impact_level': 'low',
                        'details': {}
                    }

                # Add the mapped requirement if not already present
                if mapped_req not in cross_framework_impact['affected_frameworks'][mapped_framework]['affected_requirements']:
                    cross_framework_impact['affected_frameworks'][mapped_framework]['affected_requirements'].append(mapped_req)

                # Add details about the mapping
                cross_framework_impact['affected_frameworks'][mapped_framework]['details'][mapped_req] = {
                    'mapped_from': f"{regulation_type}:{requirement}",
                    'description': f"Mapped from {regulation_type.upper()} requirement '{requirement}'"
                }

                # Determine the impact level for this framework
                framework_impact_level = self._determine_cross_framework_impact_level(
                    primary_impact['impact_level'],
                    mapped_framework,
                    len(cross_framework_impact['affected_frameworks'][mapped_framework]['affected_requirements'])
                )

                cross_framework_impact['affected_frameworks'][mapped_framework]['impact_level'] = framework_impact_level

                # Update the highest impact level
                if self._compare_impact_levels(framework_impact_level, highest_impact_level) > 0:
                    highest_impact_level = framework_impact_level

        # Set the overall impact level
        cross_framework_impact['overall_impact'] = highest_impact_level

        return cross_framework_impact

    def _determine_cross_framework_impact_level(self, primary_impact_level: str, framework: str, num_affected_requirements: int) -> str:
        """
        Determine the impact level for a cross-framework impact.

        Args:
            primary_impact_level: The impact level of the primary change
            framework: The affected framework
            num_affected_requirements: The number of affected requirements

        Returns:
            The impact level
        """
        # Start with the primary impact level
        impact_level = primary_impact_level

        # Adjust based on the number of affected requirements
        if num_affected_requirements > 2:
            impact_level = self._increase_impact_level(impact_level)
        elif num_affected_requirements == 1 and impact_level != 'low':
            impact_level = self._decrease_impact_level(impact_level)

        # Adjust based on the framework (some frameworks may have higher priority)
        if framework == 'hipaa' and impact_level == 'low':
            # HIPAA violations can have significant penalties
            impact_level = 'medium'

        return impact_level

    def _compare_impact_levels(self, level1: str, level2: str) -> int:
        """
        Compare two impact levels.

        Args:
            level1: The first impact level
            level2: The second impact level

        Returns:
            -1 if level1 < level2, 0 if level1 == level2, 1 if level1 > level2
        """
        impact_order = {'low': 0, 'medium': 1, 'high': 2}

        if impact_order.get(level1, 0) < impact_order.get(level2, 0):
            return -1
        elif impact_order.get(level1, 0) > impact_order.get(level2, 0):
            return 1
        else:
            return 0

    def _decrease_impact_level(self, impact_level: str) -> str:
        """
        Decrease the impact level.

        Args:
            impact_level: The current impact level

        Returns:
            The decreased impact level
        """
        if impact_level == 'high':
            return 'medium'
        elif impact_level == 'medium':
            return 'low'
        else:
            return 'low'

    def _analyze_impact_for_regulation(self, change: Dict[str, Any], posture: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze the impact of a regulatory change for a specific regulation.

        Args:
            change: The regulatory change
            posture: The compliance posture for the regulation

        Returns:
            The impact analysis
        """
        # Get the affected requirements
        affected_requirements = change.get('affected_requirements', [])

        # Determine the affected controls
        affected_controls = self._determine_affected_controls(change, posture)

        # Determine the affected gaps
        affected_gaps = self._determine_affected_gaps(change, posture)

        # Calculate the implementation time in days
        implementation_deadline = self._calculate_implementation_deadline(change)
        implementation_time = self._calculate_implementation_time(change.get('effective_date', ''), implementation_deadline)

        # Calculate the risk score
        risk_score = self._calculate_risk_score(
            posture.get('compliance_level', 'low'),
            len(affected_requirements),
            len(affected_controls),
            len(affected_gaps),
            implementation_time
        )

        # Determine the impact level based on the risk score
        impact_level = self._determine_impact_level_from_score(risk_score)

        # Determine the required actions
        required_actions = self._determine_required_actions(change, posture, affected_controls, affected_gaps)

        # Create the impact analysis
        impact = {
            'change_id': change.get('id'),
            'regulation_type': change.get('regulation_type'),
            'risk_score': risk_score,
            'impact_level': impact_level,
            'affected_requirements': affected_requirements,
            'affected_controls': affected_controls,
            'affected_gaps': affected_gaps,
            'required_actions': required_actions,
            'timeline': {
                'change_date': change.get('change_date'),
                'effective_date': change.get('effective_date'),
                'implementation_deadline': implementation_deadline,
                'estimated_implementation_time_days': implementation_time
            },
            'risk_factors': {
                'compliance_level': posture.get('compliance_level', 'low'),
                'affected_requirements_count': len(affected_requirements),
                'affected_controls_count': len(affected_controls),
                'affected_gaps_count': len(affected_gaps),
                'implementation_time_days': implementation_time
            }
        }

        return impact

    def _calculate_risk_score(self, compliance_level: str, num_requirements: int,
                             num_controls: int, num_gaps: int, implementation_time: int) -> float:
        """
        Calculate a risk score based on various factors.

        Args:
            compliance_level: The compliance level
            num_requirements: The number of affected requirements
            num_controls: The number of affected controls
            num_gaps: The number of affected gaps
            implementation_time: The implementation time in days

        Returns:
            The risk score (0-10)
        """
        # Convert compliance level to a score (0-10)
        compliance_scores = {'high': 2.0, 'medium': 5.0, 'low': 8.0}
        compliance_score = compliance_scores.get(compliance_level, 5.0)

        # Calculate the requirements score (0-10)
        requirements_score = min(10.0, num_requirements * 3.0)

        # Calculate the controls score (0-10)
        controls_score = min(10.0, num_controls * 2.0)

        # Calculate the gaps score (0-10)
        gaps_score = min(10.0, num_gaps * 3.0)

        # Calculate the implementation time score (0-10)
        if implementation_time <= 30:
            time_score = 3.0
        elif implementation_time <= 60:
            time_score = 5.0
        elif implementation_time <= 90:
            time_score = 7.0
        else:
            time_score = 10.0

        # Calculate the weighted risk score
        risk_score = (
            compliance_score * self.RISK_WEIGHTS['compliance_level'] +
            requirements_score * self.RISK_WEIGHTS['affected_requirements'] +
            controls_score * self.RISK_WEIGHTS['affected_controls'] +
            gaps_score * self.RISK_WEIGHTS['affected_gaps'] +
            time_score * self.RISK_WEIGHTS['implementation_time']
        )

        # Round to one decimal place
        return round(risk_score, 1)

    def _determine_impact_level_from_score(self, risk_score: float) -> str:
        """
        Determine the impact level based on a risk score.

        Args:
            risk_score: The risk score

        Returns:
            The impact level
        """
        if risk_score <= self.RISK_LEVELS['low']['max']:
            return 'low'
        elif risk_score <= self.RISK_LEVELS['medium']['max']:
            return 'medium'
        else:
            return 'high'

    def _calculate_implementation_time(self, effective_date: str, implementation_deadline: str) -> int:
        """
        Calculate the implementation time in days.

        Args:
            effective_date: The effective date of the change
            implementation_deadline: The implementation deadline

        Returns:
            The implementation time in days
        """
        # In a real implementation, this would calculate the time between dates
        # For now, return a default value based on the regulation type
        try:
            # Try to parse the dates
            effective_date_obj = datetime.strptime(effective_date, '%Y-%m-%d')
            implementation_deadline_obj = datetime.strptime(implementation_deadline, '%Y-%m-%d')

            # Calculate the difference in days
            delta = implementation_deadline_obj - effective_date_obj
            return max(0, delta.days)
        except:
            # Default to 60 days if date parsing fails
            return 60

    def _determine_affected_controls(self, change: Dict[str, Any], posture: Dict[str, Any]) -> List[str]:
        """
        Determine the controls affected by a regulatory change.

        Args:
            change: The regulatory change
            posture: The compliance posture for the regulation

        Returns:
            List of affected controls
        """
        # Get the implemented controls
        implemented_controls = posture.get('implemented_controls', [])

        # In a real implementation, this would analyze the change details
        # to determine which controls are affected
        # For now, return a subset of the implemented controls

        # Get the affected requirements
        affected_requirements = change.get('affected_requirements', [])

        # Map requirements to controls (simplified mapping)
        requirement_to_controls = {
            'data_protection': ['data_protection_officer', 'data_protection_impact_assessment'],
            'data_subject_rights': ['data_subject_rights_process', 'consent_management'],
            'controller_processor': ['data_protection_officer', 'vendor_management'],
            'data_transfers': ['cross_border_data_transfer_mechanism'],
            'remedies_liability': ['data_breach_notification_process'],

            'privacy_rule': ['security_risk_assessment', 'access_controls'],
            'security_rule': ['access_controls', 'audit_controls', 'integrity_controls', 'transmission_security'],
            'breach_notification': ['data_breach_notification_process'],
            'enforcement_rule': ['business_associate_agreements'],

            'security': ['security_policies', 'risk_management', 'system_operations', 'logical_access', 'physical_access', 'system_monitoring'],
            'availability': ['system_operations', 'incident_management'],
            'processing_integrity': ['change_management', 'system_monitoring'],
            'confidentiality': ['logical_access', 'physical_access'],
            'privacy': ['data_subject_rights_process', 'consent_management']
        }

        # Collect affected controls
        affected_controls = []
        for requirement in affected_requirements:
            if requirement in requirement_to_controls:
                for control in requirement_to_controls[requirement]:
                    if control in implemented_controls and control not in affected_controls:
                        affected_controls.append(control)

        return affected_controls

    def _determine_affected_gaps(self, change: Dict[str, Any], posture: Dict[str, Any]) -> List[str]:
        """
        Determine the gaps affected by a regulatory change.

        Args:
            change: The regulatory change
            posture: The compliance posture for the regulation

        Returns:
            List of affected gaps
        """
        # Get the gaps
        gaps = posture.get('gaps', [])

        # In a real implementation, this would analyze the change details
        # to determine which gaps are affected
        # For now, return a subset of the gaps

        # Get the affected requirements
        affected_requirements = change.get('affected_requirements', [])

        # Map requirements to gaps (simplified mapping)
        requirement_to_gaps = {
            'data_protection': [],
            'data_subject_rights': [],
            'controller_processor': ['vendor_management'],
            'data_transfers': ['cross_border_data_transfer_mechanism'],
            'remedies_liability': [],

            'privacy_rule': [],
            'security_rule': ['device_and_media_controls'],
            'breach_notification': [],
            'enforcement_rule': ['business_associate_agreements'],

            'security': [],
            'availability': [],
            'processing_integrity': [],
            'confidentiality': [],
            'privacy': []
        }

        # Collect affected gaps
        affected_gaps = []
        for requirement in affected_requirements:
            if requirement in requirement_to_gaps:
                for gap in requirement_to_gaps[requirement]:
                    if gap in gaps and gap not in affected_gaps:
                        affected_gaps.append(gap)

        return affected_gaps

    def _determine_required_actions(self, change: Dict[str, Any], posture: Dict[str, Any], affected_controls: List[str], affected_gaps: List[str]) -> List[Dict[str, Any]]:
        """
        Determine the actions required to address a regulatory change.

        Args:
            change: The regulatory change
            posture: The compliance posture for the regulation
            affected_controls: List of affected controls
            affected_gaps: List of affected gaps

        Returns:
            List of required actions
        """
        required_actions = []

        # Actions for affected controls
        for control in affected_controls:
            action = {
                'type': 'update_control',
                'control': control,
                'description': f"Update {control} to comply with the new requirements",
                'priority': 'medium'
            }
            required_actions.append(action)

        # Actions for affected gaps
        for gap in affected_gaps:
            action = {
                'type': 'implement_control',
                'control': gap,
                'description': f"Implement {gap} to address the compliance gap",
                'priority': 'high'
            }
            required_actions.append(action)

        # Add general actions
        required_actions.append({
            'type': 'update_documentation',
            'description': 'Update compliance documentation to reflect the new requirements',
            'priority': 'medium'
        })

        required_actions.append({
            'type': 'train_staff',
            'description': 'Train staff on the new requirements',
            'priority': 'medium'
        })

        return required_actions

    def _calculate_implementation_deadline(self, change: Dict[str, Any]) -> str:
        """
        Calculate the implementation deadline for a regulatory change.

        Args:
            change: The regulatory change

        Returns:
            The implementation deadline
        """
        # In a real implementation, this would calculate a deadline based on
        # the effective date and other factors
        # For now, return the effective date
        return change.get('effective_date', 'unknown')

    def _increase_impact_level(self, impact_level: str) -> str:
        """
        Increase the impact level.

        Args:
            impact_level: The current impact level

        Returns:
            The increased impact level
        """
        if impact_level == 'low':
            return 'medium'
        elif impact_level == 'medium':
            return 'high'
        else:
            return 'high'

    def _create_default_impact_analysis(self, change: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a default impact analysis for a regulatory change.

        Args:
            change: The regulatory change

        Returns:
            The default impact analysis
        """
        # Default implementation time is 60 days
        implementation_time = 60

        # Default risk score is 5.0 (medium)
        risk_score = 5.0

        # Create the impact analysis
        impact = {
            'change_id': change.get('id'),
            'regulation_type': change.get('regulation_type'),
            'risk_score': risk_score,
            'impact_level': 'medium',
            'affected_requirements': change.get('affected_requirements', []),
            'affected_controls': [],
            'affected_gaps': [],
            'required_actions': [
                {
                    'type': 'analyze_change',
                    'description': 'Analyze the regulatory change to determine its impact',
                    'priority': 'high'
                },
                {
                    'type': 'update_compliance_posture',
                    'description': 'Update the compliance posture to include this regulation',
                    'priority': 'high'
                }
            ],
            'timeline': {
                'change_date': change.get('change_date'),
                'effective_date': change.get('effective_date'),
                'implementation_deadline': change.get('effective_date'),
                'estimated_implementation_time_days': implementation_time
            },
            'risk_factors': {
                'compliance_level': 'unknown',
                'affected_requirements_count': len(change.get('affected_requirements', [])),
                'affected_controls_count': 0,
                'affected_gaps_count': 0,
                'implementation_time_days': implementation_time
            }
        }

        # If cross-framework analysis is enabled, add an empty cross-framework impact section
        if self.cross_framework_analysis:
            impact['cross_framework_impact'] = {
                'affected_frameworks': {},
                'overall_impact': 'low'
            }

        return impact
