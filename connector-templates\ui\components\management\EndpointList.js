/**
 * Endpoint List Component
 * 
 * This component displays a list of connector endpoints with details.
 */

import React, { useState } from 'react';
import { 
  Accordion, 
  AccordionDetails, 
  AccordionSummary, 
  Box, 
  Button, 
  Chip, 
  Divider, 
  Grid, 
  Paper, 
  Table, 
  TableBody, 
  TableCell, 
  TableContainer, 
  TableHead, 
  TableRow, 
  Typography 
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';

const EndpointList = ({ endpoints }) => {
  const [expandedEndpoint, setExpandedEndpoint] = useState(null);
  
  const handleAccordionChange = (endpointId) => (event, isExpanded) => {
    setExpandedEndpoint(isExpanded ? endpointId : null);
  };
  
  const getMethodColor = (method) => {
    switch (method.toUpperCase()) {
      case 'GET':
        return '#61affe';
      case 'POST':
        return '#49cc90';
      case 'PUT':
        return '#fca130';
      case 'DELETE':
        return '#f93e3e';
      case 'PATCH':
        return '#50e3c2';
      default:
        return '#ebebeb';
    }
  };
  
  const renderParameterType = (parameter) => {
    if (parameter.type === 'enum' && parameter.options) {
      return (
        <Box>
          <Typography variant="body2">
            enum
          </Typography>
          <Typography variant="caption" color="textSecondary">
            {parameter.options.map(opt => opt.value || opt).join(', ')}
          </Typography>
        </Box>
      );
    }
    
    return parameter.type;
  };
  
  const renderRequestExample = (endpoint) => {
    const method = endpoint.method.toUpperCase();
    const path = endpoint.path;
    
    // Generate example parameters
    const queryParams = {};
    const pathParams = {};
    const bodyParams = {};
    
    if (endpoint.parameters) {
      endpoint.parameters.forEach(param => {
        if (param.in === 'query' || !param.in) {
          if (param.default !== undefined) {
            queryParams[param.name] = param.default;
          } else if (param.type === 'string') {
            queryParams[param.name] = 'example';
          } else if (param.type === 'number') {
            queryParams[param.name] = 1;
          } else if (param.type === 'boolean') {
            queryParams[param.name] = true;
          } else if (param.type === 'enum' && param.options && param.options.length > 0) {
            queryParams[param.name] = param.options[0].value || param.options[0];
          }
        } else if (param.in === 'path') {
          if (param.default !== undefined) {
            pathParams[param.name] = param.default;
          } else {
            pathParams[param.name] = `{${param.name}}`;
          }
        } else if (param.in === 'body' || method === 'POST' || method === 'PUT' || method === 'PATCH') {
          if (param.default !== undefined) {
            bodyParams[param.name] = param.default;
          } else if (param.type === 'string') {
            bodyParams[param.name] = 'example';
          } else if (param.type === 'number') {
            bodyParams[param.name] = 1;
          } else if (param.type === 'boolean') {
            bodyParams[param.name] = true;
          } else if (param.type === 'enum' && param.options && param.options.length > 0) {
            bodyParams[param.name] = param.options[0].value || param.options[0];
          } else if (param.type === 'object') {
            bodyParams[param.name] = {};
          } else if (param.type === 'array') {
            bodyParams[param.name] = [];
          }
        }
      });
    }
    
    // Build URL with path parameters
    let url = path;
    Object.entries(pathParams).forEach(([key, value]) => {
      url = url.replace(`{${key}}`, value);
    });
    
    // Add query parameters
    if (Object.keys(queryParams).length > 0) {
      const queryString = Object.entries(queryParams)
        .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
        .join('&');
      url = `${url}?${queryString}`;
    }
    
    // Build request example
    let requestExample = `${method} ${url} HTTP/1.1\n`;
    requestExample += 'Host: api.example.com\n';
    requestExample += 'Content-Type: application/json\n';
    requestExample += 'Authorization: Bearer YOUR_API_KEY\n';
    
    if (method === 'POST' || method === 'PUT' || method === 'PATCH') {
      requestExample += '\n';
      requestExample += JSON.stringify(bodyParams, null, 2);
    }
    
    return requestExample;
  };
  
  const renderResponseExample = (endpoint) => {
    // This is a simplified example response
    // In a real implementation, this would be based on the endpoint's response schema
    
    const exampleResponse = {
      success: true,
      data: {
        id: '123456',
        name: 'Example Response',
        timestamp: new Date().toISOString()
      }
    };
    
    let responseExample = 'HTTP/1.1 200 OK\n';
    responseExample += 'Content-Type: application/json\n';
    responseExample += '\n';
    responseExample += JSON.stringify(exampleResponse, null, 2);
    
    return responseExample;
  };
  
  return (
    <Box>
      {endpoints.length === 0 ? (
        <Paper sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant="body1" color="textSecondary">
            No endpoints defined for this connector.
          </Typography>
        </Paper>
      ) : (
        endpoints.map(endpoint => (
          <Accordion 
            key={endpoint.id || endpoint.name} 
            expanded={expandedEndpoint === (endpoint.id || endpoint.name)}
            onChange={handleAccordionChange(endpoint.id || endpoint.name)}
            sx={{ mb: 2 }}
          >
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                <Chip 
                  label={endpoint.method.toUpperCase()} 
                  size="small" 
                  sx={{ 
                    mr: 2, 
                    bgcolor: getMethodColor(endpoint.method),
                    color: '#fff',
                    fontWeight: 'bold',
                    minWidth: 60,
                    textAlign: 'center'
                  }} 
                />
                
                <Typography variant="subtitle1" sx={{ flexGrow: 1 }}>
                  {endpoint.path}
                </Typography>
                
                <Typography variant="body2" color="textSecondary" sx={{ mr: 2 }}>
                  {endpoint.name}
                </Typography>
              </Box>
            </AccordionSummary>
            
            <AccordionDetails>
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <Typography variant="subtitle2" gutterBottom>
                    Description
                  </Typography>
                  <Typography variant="body2">
                    {endpoint.description || 'No description provided.'}
                  </Typography>
                </Grid>
                
                <Grid item xs={12}>
                  <Divider sx={{ my: 2 }} />
                  <Typography variant="subtitle2" gutterBottom>
                    Parameters
                  </Typography>
                  
                  {endpoint.parameters && endpoint.parameters.length > 0 ? (
                    <TableContainer component={Paper} variant="outlined">
                      <Table size="small">
                        <TableHead>
                          <TableRow>
                            <TableCell>Name</TableCell>
                            <TableCell>Type</TableCell>
                            <TableCell>Location</TableCell>
                            <TableCell>Required</TableCell>
                            <TableCell>Description</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {endpoint.parameters.map(param => (
                            <TableRow key={param.name}>
                              <TableCell>{param.name}</TableCell>
                              <TableCell>{renderParameterType(param)}</TableCell>
                              <TableCell>{param.in || 'query'}</TableCell>
                              <TableCell>
                                {param.required ? (
                                  <Chip label="Required" size="small" color="primary" />
                                ) : (
                                  <Chip label="Optional" size="small" variant="outlined" />
                                )}
                              </TableCell>
                              <TableCell>{param.description || '-'}</TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  ) : (
                    <Typography variant="body2" color="textSecondary">
                      No parameters required.
                    </Typography>
                  )}
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" gutterBottom>
                    Request Example
                  </Typography>
                  <Paper variant="outlined" sx={{ overflow: 'auto' }}>
                    <SyntaxHighlighter language="http" style={vscDarkPlus} showLineNumbers>
                      {renderRequestExample(endpoint)}
                    </SyntaxHighlighter>
                  </Paper>
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" gutterBottom>
                    Response Example
                  </Typography>
                  <Paper variant="outlined" sx={{ overflow: 'auto' }}>
                    <SyntaxHighlighter language="http" style={vscDarkPlus} showLineNumbers>
                      {renderResponseExample(endpoint)}
                    </SyntaxHighlighter>
                  </Paper>
                </Grid>
                
                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
                    <Button
                      variant="contained"
                      startIcon={<PlayArrowIcon />}
                      onClick={() => {
                        // In a real implementation, this would navigate to the testing page
                        // with this endpoint pre-selected
                        alert(`Test endpoint: ${endpoint.name}`);
                      }}
                    >
                      Test Endpoint
                    </Button>
                  </Box>
                </Grid>
              </Grid>
            </AccordionDetails>
          </Accordion>
        ))
      )}
    </Box>
  );
};

export default EndpointList;
