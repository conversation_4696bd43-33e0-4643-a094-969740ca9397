# Configure GCP Permissions for NovaFuse CSDE
# This script grants the necessary permissions to the NovaFuse service account

param (
    [Parameter(Mandatory=$true)]
    [string]$ProjectId,
    
    [Parameter(Mandatory=$false)]
    [string]$ServiceAccount = "novafuse-csde@${ProjectId}.iam.gserviceaccount.com"
)

Write-Host "Configuring GCP permissions for NovaFuse CSDE..."
Write-Host "Project ID: $ProjectId"
Write-Host "Service Account: $ServiceAccount"

# Ensure gcloud is authenticated
Write-Host "Checking gcloud authentication..."
$auth = gcloud auth list --format="value(account)" 2>&1
if ($LASTEXITCODE -ne 0) {
    Write-Host "Error: gcloud not authenticated. Please run 'gcloud auth login' first."
    exit 1
}

Write-Host "Authenticated as: $auth"

# Grant Security Command Center permissions
Write-Host "Granting Security Command Center permissions..."
gcloud projects add-iam-policy-binding $ProjectId `
    --member="serviceAccount:$ServiceAccount" `
    --role="roles/securitycenter.admin"

if ($LASTEXITCODE -ne 0) {
    Write-Host "Error granting Security Command Center permissions."
    exit 1
}

# Grant IAM permissions
Write-Host "Granting IAM permissions..."
gcloud projects add-iam-policy-binding $ProjectId `
    --member="serviceAccount:$ServiceAccount" `
    --role="roles/iam.roleViewer"

if ($LASTEXITCODE -ne 0) {
    Write-Host "Error granting IAM permissions."
    exit 1
}

# Grant BigQuery permissions
Write-Host "Granting BigQuery permissions..."
gcloud projects add-iam-policy-binding $ProjectId `
    --member="serviceAccount:$ServiceAccount" `
    --role="roles/bigquery.dataViewer"

if ($LASTEXITCODE -ne 0) {
    Write-Host "Error granting BigQuery permissions."
    exit 1
}

# Verify permissions
Write-Host "Verifying permissions..."
gcloud projects get-iam-policy $ProjectId `
    --format="table(bindings.role,bindings.members)" `
    --filter="bindings.members:$ServiceAccount"

Write-Host "Permissions configured successfully!"
Write-Host "You can now run the GCP integration test with live data:"
Write-Host "node gcp_integration_test.js --live"
