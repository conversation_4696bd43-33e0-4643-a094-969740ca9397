fa7490b6448b4e009219b917ced06655
/**
 * NovaFuse Universal API Connector - Error Converter
 * 
 * This module provides utilities for converting errors from external libraries
 * to UAC-specific error types.
 */

const {
  createLogger
} = require('./logger');
const {
  UAConnectorError,
  ConnectionError,
  TimeoutError,
  NetworkError,
  ServiceUnavailableError,
  AuthenticationError,
  InvalidCredentialsError,
  RateLimitExceededError,
  ResourceNotFoundError,
  BadRequestError,
  ServerError,
  ValidationError
} = require('../errors');
const logger = createLogger('error-converter');

/**
 * Convert an Axios error to a UAC-specific error
 * 
 * @param {Error} error - The Axios error
 * @param {Object} context - Additional context for the error
 * @returns {UAConnectorError} - The converted error
 */
function convertAxiosError(error, context = {}) {
  // If it's already a UAConnectorError, return it
  if (error instanceof UAConnectorError) {
    return error;
  }

  // Extract response data if available
  const response = error.response || {};
  const status = response.status;
  const data = response.data || {};

  // Create context with request details
  const errorContext = {
    ...context,
    request: {
      url: error.config?.url,
      method: error.config?.method,
      headers: error.config?.headers
    }
  };

  // Handle different error types based on the status code
  if (error.code === 'ECONNABORTED') {
    return new TimeoutError('Request timed out', {
      cause: error,
      context: errorContext
    });
  }
  if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
    return new NetworkError(`Network error: ${error.code}`, {
      cause: error,
      context: errorContext
    });
  }

  // Handle HTTP status codes
  if (status) {
    // 401 Unauthorized
    if (status === 401) {
      return new InvalidCredentialsError('Authentication failed', {
        cause: error,
        context: errorContext,
        statusCode: status,
        response: data
      });
    }

    // 403 Forbidden
    if (status === 403) {
      return new AuthenticationError('Access forbidden', {
        code: 'AUTH_FORBIDDEN',
        cause: error,
        context: errorContext,
        statusCode: status,
        response: data
      });
    }

    // 404 Not Found
    if (status === 404) {
      return new ResourceNotFoundError('Resource', error.config?.url, {
        cause: error,
        context: errorContext,
        statusCode: status,
        response: data
      });
    }

    // 429 Too Many Requests
    if (status === 429) {
      const retryAfter = response.headers?.['retry-after'];
      return new RateLimitExceededError('Rate limit exceeded', {
        cause: error,
        context: errorContext,
        statusCode: status,
        response: data,
        retryAfter: retryAfter ? parseInt(retryAfter, 10) : undefined
      });
    }

    // 400 Bad Request
    if (status === 400) {
      return new BadRequestError(data.message || 'Bad request', {
        cause: error,
        context: errorContext,
        statusCode: status,
        response: data
      });
    }

    // 500 Server Error
    if (status >= 500) {
      return new ServerError(data.message || 'Server error', {
        cause: error,
        context: errorContext,
        statusCode: status,
        response: data
      });
    }
  }

  // Default to a generic ConnectionError
  return new ConnectionError(error.message, {
    cause: error,
    context: errorContext
  });
}

/**
 * Convert a JSON Schema validation error to a UAC-specific error
 * 
 * @param {Error} error - The JSON Schema validation error
 * @param {string} schemaName - The name of the schema
 * @param {Object} context - Additional context for the error
 * @returns {ValidationError} - The converted error
 */
function convertJsonSchemaError(error, schemaName, context = {}) {
  // If it's already a UAConnectorError, return it
  if (error instanceof UAConnectorError) {
    return error;
  }

  // Extract validation errors
  const validationErrors = error.errors || [];

  // Map validation errors to a format we can use
  const mappedErrors = validationErrors.map(err => ({
    field: err.dataPath || err.instancePath,
    message: err.message,
    code: err.keyword,
    schemaPath: err.schemaPath
  }));

  // Create a SchemaValidationError
  return new ValidationError(`Schema validation failed for ${schemaName}`, {
    code: 'VALIDATION_SCHEMA_ERROR',
    cause: error,
    context: {
      ...context,
      schemaName
    },
    validationErrors: mappedErrors
  });
}

/**
 * Convert any error to a UAC-specific error
 * 
 * @param {Error} error - The error to convert
 * @param {Object} context - Additional context for the error
 * @returns {UAConnectorError} - The converted error
 */
function convertError(error, context = {}) {
  // If it's already a UAConnectorError, return it
  if (error instanceof UAConnectorError) {
    return error;
  }

  // Check if it's an Axios error
  if (error.isAxiosError) {
    return convertAxiosError(error, context);
  }

  // Check if it's a JSON Schema validation error
  if (error.errors && Array.isArray(error.errors)) {
    return convertJsonSchemaError(error, context.schemaName || 'unknown', context);
  }

  // Default to a generic UAConnectorError
  return new UAConnectorError(error.message, {
    cause: error,
    context
  });
}
module.exports = {
  convertAxiosError,
  convertJsonSchemaError,
  convertError
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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