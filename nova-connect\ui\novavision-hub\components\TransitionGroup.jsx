/**
 * TransitionGroup Component
 * 
 * A component for managing a set of transition components.
 */

import React, { useState, useEffect, Children, cloneElement, isValidElement } from 'react';
import PropTypes from 'prop-types';

/**
 * TransitionGroup component
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components
 * @param {string} [props.component='div'] - Component to render
 * @param {string} [props.className] - Additional CSS class names
 * @param {Object} [props.style] - Additional inline styles
 * @param {Object} [props.childFactory] - Factory for cloning children
 * @returns {React.ReactElement} TransitionGroup component
 */
const TransitionGroup = ({
  children,
  component: Component = 'div',
  className = '',
  style = {},
  childFactory = child => child,
  ...rest
}) => {
  // State to track children
  const [state, setState] = useState({
    children: getChildMapping(children),
    firstRender: true
  });
  
  // Update children when props change
  useEffect(() => {
    setState(prevState => {
      const prevChildMapping = prevState.children;
      const nextChildMapping = getChildMapping(children);
      const allChildren = { ...prevChildMapping };
      
      // Remove old children that are not in the new children
      Object.keys(prevChildMapping).forEach(key => {
        if (!nextChildMapping[key]) {
          allChildren[key] = cloneElement(prevChildMapping[key], {
            in: false
          });
        }
      });
      
      // Add new children
      Object.keys(nextChildMapping).forEach(key => {
        // If the child is new or was removed and re-added
        if (!prevChildMapping[key]) {
          allChildren[key] = cloneElement(nextChildMapping[key], {
            in: true
          });
        } else {
          // If the child was already there, clone it with the new props
          allChildren[key] = cloneElement(nextChildMapping[key], {
            in: true,
            onExited: null // Remove onExited to prevent removal during transition
          });
        }
      });
      
      return {
        children: allChildren,
        firstRender: false
      };
    });
  }, [children]);
  
  // Handle child exited
  const handleExited = (child, key) => {
    // Call the original onExited callback
    const onExited = child.props.onExited;
    if (onExited) {
      onExited();
    }
    
    // Remove the child from state
    setState(prevState => {
      const children = { ...prevState.children };
      delete children[key];
      return { ...prevState, children };
    });
  };
  
  // Render children
  const childrenToRender = [];
  Object.entries(state.children).forEach(([key, child]) => {
    // Skip invalid elements
    if (!isValidElement(child)) return;
    
    const childProps = {
      key,
      onExited: () => handleExited(child, key)
    };
    
    // Add in prop for first render
    if (state.firstRender) {
      childProps.in = true;
    }
    
    // Clone child with new props
    childrenToRender.push(childFactory(cloneElement(child, childProps)));
  });
  
  // Render
  if (Component === null) {
    return childrenToRender;
  }
  
  return (
    <Component
      className={className}
      style={style}
      {...rest}
    >
      {childrenToRender}
    </Component>
  );
};

TransitionGroup.propTypes = {
  children: PropTypes.node,
  component: PropTypes.elementType,
  className: PropTypes.string,
  style: PropTypes.object,
  childFactory: PropTypes.func
};

/**
 * Get child mapping
 * 
 * @param {React.ReactNode} children - Children
 * @returns {Object} Child mapping
 */
function getChildMapping(children) {
  const mapping = {};
  
  if (children) {
    Children.forEach(children, child => {
      if (isValidElement(child) && child.key) {
        mapping[child.key] = child;
      }
    });
  }
  
  return mapping;
}

export default TransitionGroup;
