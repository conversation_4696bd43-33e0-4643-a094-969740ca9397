import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import UACInfographic from '../../components/UACInfographic';

describe('UACInfographic', () => {
  it('renders the header correctly', () => {
    render(<UACInfographic />);

    // Check if the header is rendered
    expect(screen.getByText('The Dual Power of UAC')).toBeInTheDocument();
    expect(screen.getByText('One acronym, two complementary meanings, infinite possibilities')).toBeInTheDocument();
  });

  it('shows the Universal API Connector tab by default', () => {
    render(<UACInfographic />);

    // Check if the Universal API Connector tab is active by default
    const connectorTab = screen.getByRole('button', { name: 'Universal API Connector' });
    expect(connectorTab).toHaveClass('bg-blue-800');
    expect(connectorTab).toHaveClass('text-white');

    // Check if the Unified AI Compliance tab is not active
    const complianceTab = screen.getByRole('button', { name: 'Unified AI Compliance' });
    expect(complianceTab).not.toHaveClass('bg-blue-800');

    // Check if the Universal API Connector content is displayed
    expect(screen.getByText(/connectivity layer/)).toBeInTheDocument();
    expect(screen.getByText(/Connect to any API/)).toBeInTheDocument();
  });

  it('switches to the Unified AI Compliance tab when clicked', () => {
    render(<UACInfographic />);

    // Click on the Unified AI Compliance tab
    const complianceTab = screen.getByRole('button', { name: 'Unified AI Compliance' });
    fireEvent.click(complianceTab);

    // Check if the Unified AI Compliance tab is now active
    expect(complianceTab).toHaveClass('bg-blue-800');
    expect(complianceTab).toHaveClass('text-white');

    // Check if the Universal API Connector tab is not active
    const connectorTab = screen.getByRole('button', { name: 'Universal API Connector' });
    expect(connectorTab).not.toHaveClass('bg-blue-800');

    // Check if the Unified AI Compliance content is displayed
    expect(screen.getByText(/intelligence layer/)).toBeInTheDocument();
    expect(screen.getByText(/Real-Time Risk Interception/)).toBeInTheDocument();
  });

  it('switches back to the Universal API Connector tab when clicked', () => {
    render(<UACInfographic />);

    // First switch to the Unified AI Compliance tab
    const complianceTab = screen.getByRole('button', { name: 'Unified AI Compliance' });
    fireEvent.click(complianceTab);

    // Then switch back to the Universal API Connector tab
    const connectorTab = screen.getByRole('button', { name: 'Universal API Connector' });
    fireEvent.click(connectorTab);

    // Check if the Universal API Connector tab is now active
    expect(connectorTab).toHaveClass('bg-blue-800');
    expect(connectorTab).toHaveClass('text-white');

    // Check if the Universal API Connector content is displayed
    expect(screen.getByText(/connectivity layer/)).toBeInTheDocument();
  });

  it('renders the "Power of Integration" section', () => {
    render(<UACInfographic />);

    // Check if the Power of Integration section is rendered
    expect(screen.getByText('The Power of Integration')).toBeInTheDocument();
    expect(screen.getByText('When Universal API Connector meets Unified AI Compliance, organizations gain:')).toBeInTheDocument();

    // Check if the benefits are rendered
    expect(screen.getByText('Proactive Protection')).toBeInTheDocument();
    expect(screen.getByText('Accelerated Innovation')).toBeInTheDocument();
    expect(screen.getByText('Unified Governance')).toBeInTheDocument();
    expect(screen.getByText('Cost Reduction')).toBeInTheDocument();
  });

  it('renders the footer quote', () => {
    render(<UACInfographic />);

    // Check if the footer quote is rendered
    expect(screen.getByText(/The UAC is not just a product. It's a platform. A movement./)).toBeInTheDocument();
  });
});
