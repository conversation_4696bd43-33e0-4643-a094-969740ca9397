/**
 * Validation Schemas Index
 * 
 * This file exports all validation schemas for the NovaConnect API.
 */

const { commonSchemas } = require('./common/commonSchemas');
const authSchemas = require('./auth/authSchemas');
const userSchemas = require('./users/userSchemas');
const connectorSchemas = require('./connectors/connectorSchemas');
const auditSchemas = require('./audit/auditSchemas');
const reportSchemas = require('./reports/reportSchemas');
const analyticsSchemas = require('./analytics/analyticsSchemas');

module.exports = {
  commonSchemas,
  ...authSchemas,
  ...userSchemas,
  ...connectorSchemas,
  ...auditSchemas,
  ...reportSchemas,
  ...analyticsSchemas
};
