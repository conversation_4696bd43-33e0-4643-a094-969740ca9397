/**
 * NovaPulse Dashboard
 *
 * This dashboard provides an overview of the organization's compliance status,
 * regulatory changes, and key metrics.
 */

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useAuth } from '../contexts/AuthContext';
import ProtectedRoute from '../components/ProtectedRoute';
import useNovaPulseApi from '../hooks/useNovaPulseApi';
import useFetch from '../hooks/useFetch';
import LoadingSpinner from '../components/ui/loading-spinner';
import ErrorDisplay from '../components/ui/error-display';
import { ComplianceNavigation } from '../components/compliance/ComplianceNavigation';
import { ComplianceGapChart } from '../components/dashboard/ComplianceGapChart';
import { ComplianceMetricsChart } from '../components/dashboard/ComplianceMetricsChart';
import { PreventionInfoCards } from '../components/regulatory/PreventionInfoCards';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { ArrowRight, AlertTriangle, Clock, FileText, Shield, BookOpen, LogOut } from 'lucide-react';
import Link from 'next/link';

export default function Dashboard() {
  return (
    <ProtectedRoute>
      <DashboardContent />
    </ProtectedRoute>
  );
}

function DashboardContent() {
  const router = useRouter();
  const { logout } = useAuth();
  const { api, callApi } = useNovaPulseApi();
  const [error, setError] = useState<Error | null>(null);

  // Fetch compliance data
  const {
    data: complianceData = [],
    loading: complianceLoading,
    error: complianceError
  } = useFetch(
    async () => await callApi(() => api.getComplianceStatusData()),
    { dependencies: [api] }
  );

  // Fetch regulatory change data
  const {
    data: regulatoryChangeData = {
      byImpact: { high: 0, medium: 0, low: 0 },
      byStatus: { pending: 0, overdue: 0, upcoming: 0, completed: 0 },
      byCategory: []
    },
    loading: regulatoryLoading,
    error: regulatoryError
  } = useFetch(
    async () => await callApi(() => api.getRegulatoryChangeData()),
    { dependencies: [api] }
  );

  // Fetch framework count
  const {
    data: frameworksResponse,
    loading: frameworksLoading
  } = useFetch(
    async () => await callApi(() => api.getFrameworks()),
    { dependencies: [api] }
  );

  // Fetch regulation count
  const {
    data: regulationsResponse,
    loading: regulationsLoading
  } = useFetch(
    async () => await callApi(() => api.getRegulations()),
    { dependencies: [api] }
  );

  // Combine all loading states
  const loading = complianceLoading || regulatoryLoading || frameworksLoading || regulationsLoading;

  // Combine all errors
  useEffect(() => {
    const firstError = complianceError || regulatoryError;
    if (firstError) {
      setError(firstError);
    } else {
      setError(null);
    }
  }, [complianceError, regulatoryError]);

  const handleLogout = () => {
    logout();
    router.push('/login');
  };

  const handleDismissError = () => {
    setError(null);
  };

  const frameworkCount = frameworksResponse?.pagination.total || 0;
  const regulationCount = regulationsResponse?.pagination.total || 0;

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Compliance Dashboard</h1>
        <Button variant="outline" size="sm" onClick={handleLogout} className="flex items-center">
          <LogOut className="h-4 w-4 mr-2" />
          Logout
        </Button>
      </div>

      {error && (
        <div className="mb-6">
          <ErrorDisplay
            message="Error loading dashboard data"
            details={error.message}
            severity="error"
            onDismiss={handleDismissError}
          />
        </div>
      )}

      {loading && <LoadingSpinner size="large" text="Loading dashboard data..." fullPage />}

      <ComplianceNavigation />

      <div className="mb-6">
        <PreventionInfoCards
          pending={regulatoryChangeData.byStatus.pending}
          overdue={regulatoryChangeData.byStatus.overdue}
          upcoming={regulatoryChangeData.byStatus.upcoming}
          completed={regulatoryChangeData.byStatus.completed}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center">
              <Shield className="h-5 w-5 mr-2 text-blue-500" />
              Compliance Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            {complianceLoading ? (
              <div className="h-64 flex items-center justify-center">
                <LoadingSpinner size="medium" text="Loading compliance data..." />
              </div>
            ) : complianceData.length === 0 ? (
              <div className="h-64 flex items-center justify-center">
                <div className="text-center">
                  <div className="text-gray-500 mb-2">No compliance data available</div>
                  <Link href="/compliance-profiles">
                    <Button size="sm">Set Up Compliance Profile</Button>
                  </Link>
                </div>
              </div>
            ) : (
              <>
                <ComplianceGapChart data={complianceData} />
                <div className="mt-4 text-right">
                  <Link href="/compliance-profiles">
                    <Button variant="link" size="sm" className="text-blue-600">
                      View Compliance Profiles
                      <ArrowRight className="h-4 w-4 ml-1" />
                    </Button>
                  </Link>
                </div>
              </>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center">
              <AlertTriangle className="h-5 w-5 mr-2 text-yellow-500" />
              Regulatory Impact
            </CardTitle>
          </CardHeader>
          <CardContent>
            {regulatoryLoading ? (
              <div className="h-64 flex items-center justify-center">
                <LoadingSpinner size="medium" text="Loading regulatory data..." />
              </div>
            ) : (
              <>
                <div className="grid grid-cols-3 gap-4 mb-6">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-600">{regulatoryChangeData.byImpact.high}</div>
                    <div className="text-sm text-gray-500">High Impact</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-yellow-600">{regulatoryChangeData.byImpact.medium}</div>
                    <div className="text-sm text-gray-500">Medium Impact</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">{regulatoryChangeData.byImpact.low}</div>
                    <div className="text-sm text-gray-500">Low Impact</div>
                  </div>
                </div>

                <div className="space-y-3">
                  {regulatoryChangeData.byCategory.slice(0, 4).map((item: { category: string; count: number }, index: number) => (
                    <div key={index} className="flex justify-between items-center">
                      <div className="text-sm">{item.category}</div>
                      <div className="text-sm font-medium">{item.count}</div>
                    </div>
                  ))}
                </div>

                <div className="mt-4 text-right">
                  <Link href="/regulatory-changes">
                    <Button variant="link" size="sm" className="text-blue-600">
                      View Regulatory Changes
                      <ArrowRight className="h-4 w-4 ml-1" />
                    </Button>
                  </Link>
                </div>
              </>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center">
              <BookOpen className="h-5 w-5 mr-2 text-purple-500" />
              Frameworks & Regulations
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4 mb-6">
              <div className="bg-white p-4 rounded-lg border shadow-sm text-center">
                <div className="text-3xl font-bold text-blue-600">{frameworkCount}</div>
                <div className="text-sm text-gray-500">Frameworks</div>
              </div>
              <div className="bg-white p-4 rounded-lg border shadow-sm text-center">
                <div className="text-3xl font-bold text-green-600">{regulationCount}</div>
                <div className="text-sm text-gray-500">Regulations</div>
              </div>
            </div>

            <div className="space-y-4">
              <Link href="/frameworks">
                <Button variant="outline" size="sm" className="w-full justify-between">
                  <span className="flex items-center">
                    <Shield className="h-4 w-4 mr-2" />
                    Manage Frameworks
                  </span>
                  <ArrowRight className="h-4 w-4" />
                </Button>
              </Link>

              <Link href="/regulations">
                <Button variant="outline" size="sm" className="w-full justify-between">
                  <span className="flex items-center">
                    <FileText className="h-4 w-4 mr-2" />
                    Manage Regulations
                  </span>
                  <ArrowRight className="h-4 w-4" />
                </Button>
              </Link>

              <Link href="/regulatory-changes">
                <Button variant="outline" size="sm" className="w-full justify-between">
                  <span className="flex items-center">
                    <Clock className="h-4 w-4 mr-2" />
                    Track Regulatory Changes
                  </span>
                  <ArrowRight className="h-4 w-4" />
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Compliance Metrics</CardTitle>
            <CardDescription>Detailed compliance metrics across frameworks</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-96">
              <ComplianceMetricsChart />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
