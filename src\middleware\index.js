/**
 * NovaFuse Universal API Connector - Middleware Index
 * 
 * This module exports all middleware.
 */

const auth = require('./auth');
const errorHandler = require('./error-handler');
const rateLimiter = require('./rate-limiter');
const validator = require('./validator');
const security = require('./security');
const sanitizer = require('./sanitizer');

module.exports = {
  auth,
  errorHandler,
  rateLimiter,
  validator,
  security,
  sanitizer
};
