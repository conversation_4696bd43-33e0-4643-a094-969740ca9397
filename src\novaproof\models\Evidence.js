/**
 * Evidence.js
 * 
 * This file defines the Evidence class, which represents a piece of compliance evidence
 * that can be verified on the blockchain.
 */

const crypto = require('crypto');

/**
 * Evidence status enum
 * @enum {string}
 */
const EvidenceStatus = {
  COLLECTED: 'COLLECTED',
  VERIFIED: 'VERIFIED',
  INVALID: 'INVALID',
  EXPIRED: 'EXPIRED',
  PENDING: 'PENDING'
};

/**
 * Evidence class representing a piece of compliance evidence
 */
class Evidence {
  /**
   * Create a new Evidence instance
   * @param {Object} data - The evidence data
   * @param {Object} options - Additional options for the evidence
   */
  constructor(data, options = {}) {
    this.id = options.id || crypto.randomUUID();
    this.controlId = data.controlId;
    this.framework = data.framework;
    this.source = data.source;
    this.timestamp = data.timestamp || new Date().toISOString();
    this.data = data.data || {};
    this.status = data.status || EvidenceStatus.COLLECTED;
    this.metadata = {
      created: new Date().toISOString(),
      collector: 'NovaProof',
      version: '1.0.0',
      ...options.metadata
    };
    
    if (data.attachment) {
      this.attachment = data.attachment;
    }
    
    this.verifications = [];
  }
  
  /**
   * Add a verification record to the evidence
   * @param {Object} verification - The verification record
   * @returns {Evidence} - The evidence with the added verification
   */
  addVerification(verification) {
    this.verifications.push({
      ...verification,
      timestamp: verification.timestamp || new Date().toISOString()
    });
    
    // Update the evidence status based on the verification
    if (verification.status === 'VERIFIED') {
      this.status = EvidenceStatus.VERIFIED;
    } else if (verification.status === 'INVALID') {
      this.status = EvidenceStatus.INVALID;
    }
    
    return this;
  }
  
  /**
   * Get the latest verification record
   * @returns {Object|null} - The latest verification record, or null if none exists
   */
  getLatestVerification() {
    if (this.verifications.length === 0) {
      return null;
    }
    
    return this.verifications.reduce((latest, current) => {
      return new Date(current.timestamp) > new Date(latest.timestamp) ? current : latest;
    }, this.verifications[0]);
  }
  
  /**
   * Check if the evidence is verified
   * @returns {boolean} - Whether the evidence is verified
   */
  isVerified() {
    return this.status === EvidenceStatus.VERIFIED;
  }
  
  /**
   * Calculate a hash of the evidence data
   * @returns {string} - A SHA-256 hash of the evidence data
   */
  hash() {
    // Create a copy of the evidence without the verifications
    const evidenceData = {
      controlId: this.controlId,
      framework: this.framework,
      source: this.source,
      timestamp: this.timestamp,
      data: this.data
    };
    
    if (this.attachment) {
      evidenceData.attachment = this.attachment;
    }
    
    const dataString = JSON.stringify(evidenceData);
    return crypto.createHash('sha256').update(dataString).digest('hex');
  }
  
  /**
   * Check if the evidence is expired
   * @param {Object} options - Options for checking expiration
   * @returns {boolean} - Whether the evidence is expired
   */
  isExpired(options = {}) {
    const expirationDays = options.expirationDays || 90; // Default: 90 days
    const evidenceDate = new Date(this.timestamp);
    const expirationDate = new Date(evidenceDate);
    expirationDate.setDate(expirationDate.getDate() + expirationDays);
    
    const now = new Date();
    const isExpired = now > expirationDate;
    
    if (isExpired && this.status !== EvidenceStatus.EXPIRED) {
      this.status = EvidenceStatus.EXPIRED;
    }
    
    return isExpired;
  }
  
  /**
   * Convert the evidence to a JSON-serializable object
   * @returns {Object} - A JSON-serializable representation of the evidence
   */
  toJSON() {
    return {
      id: this.id,
      controlId: this.controlId,
      framework: this.framework,
      source: this.source,
      timestamp: this.timestamp,
      data: this.data,
      status: this.status,
      metadata: this.metadata,
      attachment: this.attachment,
      verifications: this.verifications,
      hash: this.hash()
    };
  }
  
  /**
   * Create an evidence instance from a JSON object
   * @param {Object} json - The JSON representation of an evidence
   * @returns {Evidence} - A new evidence instance created from the JSON
   */
  static fromJSON(json) {
    const evidence = new Evidence({
      controlId: json.controlId,
      framework: json.framework,
      source: json.source,
      timestamp: json.timestamp,
      data: json.data,
      status: json.status,
      attachment: json.attachment
    }, {
      id: json.id,
      metadata: json.metadata
    });
    
    if (json.verifications) {
      json.verifications.forEach(verification => {
        evidence.addVerification(verification);
      });
    }
    
    return evidence;
  }
}

module.exports = {
  Evidence,
  EvidenceStatus
};
