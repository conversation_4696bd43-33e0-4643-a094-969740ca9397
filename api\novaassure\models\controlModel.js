/**
 * Control Model
 * 
 * This model defines the schema for controls.
 */

const mongoose = require('mongoose');

const controlSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    required: true,
    trim: true
  },
  framework: {
    type: String,
    required: true,
    trim: true,
    index: true
  },
  category: {
    type: String,
    trim: true,
    index: true
  },
  requirements: [{
    type: String,
    trim: true
  }],
  testProcedures: [{
    type: String,
    trim: true
  }],
  references: [{
    framework: {
      type: String,
      trim: true
    },
    controlId: {
      type: String,
      trim: true
    }
  }],
  riskLevel: {
    type: String,
    enum: ['low', 'medium', 'high', 'critical'],
    default: 'medium'
  },
  implementationStatus: {
    type: String,
    enum: ['not-implemented', 'partially-implemented', 'implemented'],
    default: 'not-implemented'
  },
  owner: {
    type: String,
    trim: true
  },
  tags: [{
    type: String,
    trim: true
  }],
  metadata: {
    type: mongoose.Schema.Types.Mixed
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Create indexes
controlSchema.index({ name: 'text', description: 'text' });
controlSchema.index({ framework: 1, category: 1 });
controlSchema.index({ 'references.framework': 1, 'references.controlId': 1 });

/**
 * Get control by ID
 * @param {string} id - Control ID
 * @returns {Promise<Object>} - Control object
 */
controlSchema.statics.getById = async function(id) {
  return this.findById(id);
};

/**
 * Get controls by framework
 * @param {string} framework - Framework name
 * @returns {Promise<Array>} - Array of control objects
 */
controlSchema.statics.getByFramework = async function(framework) {
  return this.find({ framework });
};

/**
 * Get controls by category
 * @param {string} category - Category name
 * @returns {Promise<Array>} - Array of control objects
 */
controlSchema.statics.getByCategory = async function(category) {
  return this.find({ category });
};

/**
 * Search controls
 * @param {string} query - Search query
 * @returns {Promise<Array>} - Array of control objects
 */
controlSchema.statics.search = async function(query) {
  return this.find({ $text: { $search: query } });
};

/**
 * Get control mapping
 * @param {string} sourceFramework - Source framework
 * @param {string} targetFramework - Target framework
 * @returns {Promise<Array>} - Array of control mapping objects
 */
controlSchema.statics.getMapping = async function(sourceFramework, targetFramework) {
  const sourceControls = await this.find({ framework: sourceFramework });
  const mappings = [];
  
  for (const sourceControl of sourceControls) {
    const targetControls = await this.find({
      framework: targetFramework,
      'references.framework': sourceFramework,
      'references.controlId': sourceControl._id.toString()
    });
    
    mappings.push({
      sourceControl,
      targetControls
    });
  }
  
  return mappings;
};

const Control = mongoose.model('Control', controlSchema);

module.exports = Control;
