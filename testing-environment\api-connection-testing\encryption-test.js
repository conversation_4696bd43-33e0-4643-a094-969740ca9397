/**
 * Encryption Test for NovaConnect Universal API Connector
 * 
 * This test verifies the strength and correctness of the encryption implementation.
 */

const { encrypt, decrypt, hashData, verifyHash, generateToken } = require('../utils/encryption');
const keyManagement = require('../utils/key-management');
const crypto = require('crypto');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

/**
 * Test basic encryption and decryption
 */
async function testBasicEncryption() {
  console.log(`${colors.bright}${colors.blue}Testing Basic Encryption and Decryption...${colors.reset}`);
  
  // Test data
  const testData = {
    apiKey: 'test-api-key-12345',
    secret: 'very-secret-value',
    username: 'testuser',
    password: 'p@ssw0rd!'
  };
  
  // Generate a random encryption key
  const key = crypto.randomBytes(32);
  
  try {
    // Encrypt the data
    console.log(`${colors.yellow}Encrypting test data...${colors.reset}`);
    const encryptedData = encrypt(testData, key);
    
    console.log(`${colors.cyan}Encrypted data: ${JSON.stringify(encryptedData, null, 2)}${colors.reset}`);
    
    // Verify that the encrypted data contains all required fields
    if (!encryptedData.encrypted || !encryptedData.iv || !encryptedData.authTag || !encryptedData.salt) {
      throw new Error('Encrypted data is missing required fields');
    }
    
    // Decrypt the data
    console.log(`${colors.yellow}Decrypting test data...${colors.reset}`);
    const decryptedData = decrypt(encryptedData, key, { parseJson: true });
    
    console.log(`${colors.cyan}Decrypted data: ${JSON.stringify(decryptedData, null, 2)}${colors.reset}`);
    
    // Verify that the decrypted data matches the original
    if (JSON.stringify(decryptedData) !== JSON.stringify(testData)) {
      throw new Error('Decrypted data does not match original data');
    }
    
    console.log(`${colors.green}✓ Basic encryption test passed${colors.reset}`);
    return true;
  } catch (error) {
    console.log(`${colors.red}✗ Basic encryption test failed: ${error.message}${colors.reset}`);
    return false;
  }
}

/**
 * Test encryption with key derivation
 */
async function testKeyDerivation() {
  console.log(`${colors.bright}${colors.blue}Testing Encryption with Key Derivation...${colors.reset}`);
  
  // Test data
  const testData = {
    apiKey: 'test-api-key-12345',
    secret: 'very-secret-value'
  };
  
  // Use a password instead of a key
  const password = 'my-secure-password';
  
  try {
    // Encrypt the data with key derivation
    console.log(`${colors.yellow}Encrypting test data with key derivation...${colors.reset}`);
    const encryptedData = encrypt(testData, password, { deriveKey: true });
    
    console.log(`${colors.cyan}Encrypted data: ${JSON.stringify(encryptedData, null, 2)}${colors.reset}`);
    
    // Verify that the encrypted data contains all required fields
    if (!encryptedData.encrypted || !encryptedData.iv || !encryptedData.authTag || !encryptedData.salt) {
      throw new Error('Encrypted data is missing required fields');
    }
    
    // Decrypt the data with key derivation
    console.log(`${colors.yellow}Decrypting test data with key derivation...${colors.reset}`);
    const decryptedData = decrypt(encryptedData, password, { deriveKey: true, parseJson: true });
    
    console.log(`${colors.cyan}Decrypted data: ${JSON.stringify(decryptedData, null, 2)}${colors.reset}`);
    
    // Verify that the decrypted data matches the original
    if (JSON.stringify(decryptedData) !== JSON.stringify(testData)) {
      throw new Error('Decrypted data does not match original data');
    }
    
    console.log(`${colors.green}✓ Key derivation test passed${colors.reset}`);
    return true;
  } catch (error) {
    console.log(`${colors.red}✗ Key derivation test failed: ${error.message}${colors.reset}`);
    return false;
  }
}

/**
 * Test encryption with additional authenticated data (AAD)
 */
async function testAuthenticatedData() {
  console.log(`${colors.bright}${colors.blue}Testing Encryption with Additional Authenticated Data...${colors.reset}`);
  
  // Test data
  const testData = {
    apiKey: 'test-api-key-12345',
    secret: 'very-secret-value'
  };
  
  // Generate a random encryption key
  const key = crypto.randomBytes(32);
  
  // Additional authenticated data
  const aad = 'user-123-connector-456';
  
  try {
    // Encrypt the data with AAD
    console.log(`${colors.yellow}Encrypting test data with AAD...${colors.reset}`);
    const encryptedData = encrypt(testData, key, { associatedData: aad });
    
    console.log(`${colors.cyan}Encrypted data: ${JSON.stringify(encryptedData, null, 2)}${colors.reset}`);
    
    // Decrypt the data with correct AAD
    console.log(`${colors.yellow}Decrypting test data with correct AAD...${colors.reset}`);
    const decryptedData = decrypt(encryptedData, key, { associatedData: aad, parseJson: true });
    
    console.log(`${colors.cyan}Decrypted data: ${JSON.stringify(decryptedData, null, 2)}${colors.reset}`);
    
    // Verify that the decrypted data matches the original
    if (JSON.stringify(decryptedData) !== JSON.stringify(testData)) {
      throw new Error('Decrypted data does not match original data');
    }
    
    // Try to decrypt with incorrect AAD
    console.log(`${colors.yellow}Attempting to decrypt with incorrect AAD...${colors.reset}`);
    try {
      decrypt(encryptedData, key, { associatedData: 'wrong-aad', parseJson: true });
      throw new Error('Decryption with incorrect AAD should have failed');
    } catch (error) {
      console.log(`${colors.green}✓ Decryption with incorrect AAD failed as expected: ${error.message}${colors.reset}`);
    }
    
    console.log(`${colors.green}✓ Authenticated data test passed${colors.reset}`);
    return true;
  } catch (error) {
    console.log(`${colors.red}✗ Authenticated data test failed: ${error.message}${colors.reset}`);
    return false;
  }
}

/**
 * Test key management
 */
async function testKeyManagement() {
  console.log(`${colors.bright}${colors.blue}Testing Key Management...${colors.reset}`);
  
  try {
    // Initialize key management
    console.log(`${colors.yellow}Initializing key management...${colors.reset}`);
    await keyManagement.initialize();
    
    // Get current key
    console.log(`${colors.yellow}Getting current key...${colors.reset}`);
    const currentKey = keyManagement.getCurrentKey();
    
    console.log(`${colors.cyan}Current key ID: ${currentKey.id}${colors.reset}`);
    
    // Test data
    const testData = {
      apiKey: 'test-api-key-12345',
      secret: 'very-secret-value'
    };
    
    // Encrypt data with current key
    console.log(`${colors.yellow}Encrypting test data with current key...${colors.reset}`);
    const encryptedData = encrypt(testData, currentKey.key);
    
    // Get key by ID
    console.log(`${colors.yellow}Getting key by ID...${colors.reset}`);
    const retrievedKey = keyManagement.getKeyById(currentKey.id);
    
    if (!retrievedKey || retrievedKey.id !== currentKey.id) {
      throw new Error('Retrieved key does not match current key');
    }
    
    // Decrypt data with retrieved key
    console.log(`${colors.yellow}Decrypting test data with retrieved key...${colors.reset}`);
    const decryptedData = decrypt(encryptedData, retrievedKey.key, { parseJson: true });
    
    // Verify that the decrypted data matches the original
    if (JSON.stringify(decryptedData) !== JSON.stringify(testData)) {
      throw new Error('Decrypted data does not match original data');
    }
    
    // Force key rotation
    console.log(`${colors.yellow}Forcing key rotation...${colors.reset}`);
    const newKeyId = keyManagement.forceKeyRotation();
    
    console.log(`${colors.cyan}New key ID: ${newKeyId}${colors.reset}`);
    
    // Get new current key
    const newCurrentKey = keyManagement.getCurrentKey();
    
    if (newCurrentKey.id !== newKeyId) {
      throw new Error('New current key ID does not match rotated key ID');
    }
    
    // Verify that old key is still accessible
    console.log(`${colors.yellow}Verifying that old key is still accessible...${colors.reset}`);
    const oldKey = keyManagement.getKeyById(currentKey.id);
    
    if (!oldKey || oldKey.id !== currentKey.id) {
      throw new Error('Old key is not accessible after rotation');
    }
    
    // Decrypt data with old key
    console.log(`${colors.yellow}Decrypting test data with old key...${colors.reset}`);
    const decryptedDataWithOldKey = decrypt(encryptedData, oldKey.key, { parseJson: true });
    
    // Verify that the decrypted data matches the original
    if (JSON.stringify(decryptedDataWithOldKey) !== JSON.stringify(testData)) {
      throw new Error('Decrypted data with old key does not match original data');
    }
    
    console.log(`${colors.green}✓ Key management test passed${colors.reset}`);
    return true;
  } catch (error) {
    console.log(`${colors.red}✗ Key management test failed: ${error.message}${colors.reset}`);
    return false;
  }
}

/**
 * Test password hashing
 */
async function testPasswordHashing() {
  console.log(`${colors.bright}${colors.blue}Testing Password Hashing...${colors.reset}`);
  
  // Test password
  const password = 'my-secure-password';
  
  try {
    // Hash the password
    console.log(`${colors.yellow}Hashing password...${colors.reset}`);
    const { hash, salt } = hashData(password);
    
    console.log(`${colors.cyan}Hash: ${hash}${colors.reset}`);
    console.log(`${colors.cyan}Salt: ${salt}${colors.reset}`);
    
    // Verify the hash
    console.log(`${colors.yellow}Verifying hash with correct password...${colors.reset}`);
    const isValid = verifyHash(password, hash, salt);
    
    if (!isValid) {
      throw new Error('Hash verification failed with correct password');
    }
    
    // Verify with incorrect password
    console.log(`${colors.yellow}Verifying hash with incorrect password...${colors.reset}`);
    const isInvalidValid = verifyHash('wrong-password', hash, salt);
    
    if (isInvalidValid) {
      throw new Error('Hash verification succeeded with incorrect password');
    }
    
    console.log(`${colors.green}✓ Password hashing test passed${colors.reset}`);
    return true;
  } catch (error) {
    console.log(`${colors.red}✗ Password hashing test failed: ${error.message}${colors.reset}`);
    return false;
  }
}

/**
 * Test token generation
 */
async function testTokenGeneration() {
  console.log(`${colors.bright}${colors.blue}Testing Token Generation...${colors.reset}`);
  
  try {
    // Generate a token
    console.log(`${colors.yellow}Generating token...${colors.reset}`);
    const token = generateToken();
    
    console.log(`${colors.cyan}Token: ${token}${colors.reset}`);
    
    // Verify token length
    if (token.length !== 64) { // 32 bytes = 64 hex characters
      throw new Error(`Token length is ${token.length}, expected 64`);
    }
    
    // Generate another token and verify uniqueness
    const anotherToken = generateToken();
    
    if (token === anotherToken) {
      throw new Error('Generated tokens are not unique');
    }
    
    console.log(`${colors.green}✓ Token generation test passed${colors.reset}`);
    return true;
  } catch (error) {
    console.log(`${colors.red}✗ Token generation test failed: ${error.message}${colors.reset}`);
    return false;
  }
}

/**
 * Run all encryption tests
 */
async function runEncryptionTests() {
  console.log(`${colors.bright}${colors.magenta}=== NovaConnect Encryption Tests ===${colors.reset}\n`);
  
  const results = [];
  
  // Run basic encryption test
  results.push({
    name: 'Basic Encryption',
    passed: await testBasicEncryption()
  });
  
  console.log();
  
  // Run key derivation test
  results.push({
    name: 'Key Derivation',
    passed: await testKeyDerivation()
  });
  
  console.log();
  
  // Run authenticated data test
  results.push({
    name: 'Authenticated Data',
    passed: await testAuthenticatedData()
  });
  
  console.log();
  
  // Run key management test
  results.push({
    name: 'Key Management',
    passed: await testKeyManagement()
  });
  
  console.log();
  
  // Run password hashing test
  results.push({
    name: 'Password Hashing',
    passed: await testPasswordHashing()
  });
  
  console.log();
  
  // Run token generation test
  results.push({
    name: 'Token Generation',
    passed: await testTokenGeneration()
  });
  
  // Print summary
  console.log(`\n${colors.bright}${colors.magenta}=== Test Summary ===${colors.reset}\n`);
  
  let passCount = 0;
  let failCount = 0;
  
  for (const { name, passed } of results) {
    if (passed) {
      console.log(`${colors.green}✓ ${name}${colors.reset}`);
      passCount++;
    } else {
      console.log(`${colors.red}✗ ${name}${colors.reset}`);
      failCount++;
    }
  }
  
  const totalTests = results.length;
  const passRate = (passCount / totalTests) * 100;
  
  console.log(`\n${colors.bright}${colors.white}Total: ${totalTests}${colors.reset}`);
  console.log(`${colors.bright}${colors.green}Passed: ${passCount} (${passRate.toFixed(2)}%)${colors.reset}`);
  console.log(`${colors.bright}${colors.red}Failed: ${failCount}${colors.reset}`);
  
  if (failCount === 0) {
    console.log(`\n${colors.bright}${colors.green}All encryption tests passed!${colors.reset}`);
    console.log(`${colors.green}The NovaConnect Universal API Connector has strong encryption capabilities.${colors.reset}`);
  } else {
    console.log(`\n${colors.bright}${colors.red}Some encryption tests failed!${colors.reset}`);
    console.log(`${colors.red}The NovaConnect Universal API Connector's encryption capabilities need improvement.${colors.reset}`);
  }
}

// Run the tests
runEncryptionTests();
