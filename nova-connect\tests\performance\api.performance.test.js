/**
 * Performance tests for the NovaConnect API
 */

const { expect } = require('chai');
const supertest = require('supertest');
const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');

// Import the app
const app = require('../../server');

describe('NovaConnect API Performance Tests', () => {
  let mongoServer;
  let request;

  before(async () => {
    // Create an in-memory MongoDB server
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();

    // Connect to the in-memory database
    await mongoose.connect(mongoUri, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });

    // Create a supertest request object
    request = supertest(app);
  });

  after(async () => {
    // Disconnect from the database
    await mongoose.disconnect();
    // Stop the in-memory MongoDB server
    await mongoServer.stop();
  });

  describe('Health Check Performance', () => {
    it('should respond to health check within 100ms', async () => {
      const start = Date.now();
      const response = await request.get('/health');
      const end = Date.now();
      const duration = end - start;

      expect(response.status).to.equal(200);
      expect(duration).to.be.lessThan(100);
    });
  });

  describe('Connectors API Performance', () => {
    // Test data for creating multiple connectors
    const createTestConnectors = async (count) => {
      const connectors = [];
      for (let i = 0; i < count; i++) {
        const connector = {
          name: `Test Connector ${i}`,
          type: 'http',
          description: `Test connector ${i} for performance tests`,
          config: {
            baseUrl: 'https://api.example.com',
            authentication: {
              type: 'api_key',
              header: 'X-API-Key'
            }
          }
        };

        const response = await request
          .post('/api/connectors')
          .send(connector);

        connectors.push(response.body);
      }
      return connectors;
    };

    it('should create 10 connectors in under 1 second', async () => {
      const start = Date.now();
      await createTestConnectors(10);
      const end = Date.now();
      const duration = end - start;

      expect(duration).to.be.lessThan(1000);
    });

    it('should retrieve 100 connectors in under 500ms', async () => {
      // Create 100 connectors
      await createTestConnectors(90); // We already created 10 in the previous test

      // Measure the time to retrieve all connectors
      const start = Date.now();
      const response = await request.get('/api/connectors');
      const end = Date.now();
      const duration = end - start;

      expect(response.status).to.equal(200);
      expect(response.body.data).to.be.an('array');
      expect(response.body.data.length).to.equal(100);
      expect(duration).to.be.lessThan(500);
    });

    it('should handle pagination efficiently', async () => {
      // Measure the time to retrieve paginated results
      const start = Date.now();
      const response = await request.get('/api/connectors?page=2&limit=20');
      const end = Date.now();
      const duration = end - start;

      expect(response.status).to.equal(200);
      expect(response.body.data).to.be.an('array');
      expect(response.body.data.length).to.equal(20);
      expect(response.body.pagination.page).to.equal(2);
      expect(response.body.pagination.limit).to.equal(20);
      expect(duration).to.be.lessThan(100);
    });

    it('should handle filtering efficiently', async () => {
      // Measure the time to retrieve filtered results
      const start = Date.now();
      const response = await request.get('/api/connectors?type=http');
      const end = Date.now();
      const duration = end - start;

      expect(response.status).to.equal(200);
      expect(response.body.data).to.be.an('array');
      expect(response.body.data.length).to.be.at.least(1);
      expect(response.body.data[0].type).to.equal('http');
      expect(duration).to.be.lessThan(100);
    });

    it('should handle sorting efficiently', async () => {
      // Measure the time to retrieve sorted results
      const start = Date.now();
      const response = await request.get('/api/connectors?sort=name:asc');
      const end = Date.now();
      const duration = end - start;

      expect(response.status).to.equal(200);
      expect(response.body.data).to.be.an('array');
      expect(response.body.data.length).to.be.at.least(1);
      
      // Verify sorting
      const names = response.body.data.map(c => c.name);
      const sortedNames = [...names].sort();
      expect(names).to.deep.equal(sortedNames);
      
      expect(duration).to.be.lessThan(100);
    });
  });

  describe('Caching Performance', () => {
    it('should respond faster on cached requests', async () => {
      // First request (uncached)
      const start1 = Date.now();
      const response1 = await request.get('/api/connectors');
      const end1 = Date.now();
      const duration1 = end1 - start1;

      expect(response1.status).to.equal(200);

      // Second request (should be cached)
      const start2 = Date.now();
      const response2 = await request.get('/api/connectors');
      const end2 = Date.now();
      const duration2 = end2 - start2;

      expect(response2.status).to.equal(200);
      
      // The cached request should be faster
      expect(duration2).to.be.lessThan(duration1);
    });
  });
});
