/**
 * Fusion Operator
 * 
 * This module implements fusion operations for the Comphyology framework.
 * It provides functionality for non-linear synergy between components
 * used in the UUFT formula.
 */

/**
 * FusionOperator class
 */
class FusionOperator {
  /**
   * Create a new FusionOperator
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    this.options = {
      synergisticFactor: 1.618033988749895, // Golden Ratio
      precision: 6, // Decimal precision
      entropyEstimator: 'shannon', // Default entropy estimator
      ...options
    };
    
    console.log(`FusionOperator initialized with synergistic factor: ${this.options.synergisticFactor}`);
  }
  
  /**
   * Fuse two vectors
   * @param {Array} a - First vector
   * @param {Array} b - Second vector
   * @returns {Array} - Fusion result
   */
  fuse(a, b) {
    if (!Array.isArray(a) || !Array.isArray(b)) {
      throw new Error('Inputs must be arrays');
    }
    
    if (a.length === 0 || b.length === 0) {
      return [];
    }
    
    // Determine the minimum length
    const minLength = Math.min(a.length, b.length);
    
    // Create result array
    const result = [];
    
    // Apply fusion operation
    for (let i = 0; i < minLength; i++) {
      // Apply synergistic factor to the sum
      result.push(this._round((a[i] + b[i]) * this.options.synergisticFactor));
    }
    
    return result;
  }
  
  /**
   * Fuse multiple vectors
   * @param {Array} vectors - Array of vectors
   * @returns {Array} - Fusion result
   */
  multiFuse(vectors) {
    if (!Array.isArray(vectors) || vectors.length === 0) {
      return [];
    }
    
    if (vectors.length === 1) {
      return vectors[0];
    }
    
    let result = this.fuse(vectors[0], vectors[1]);
    
    for (let i = 2; i < vectors.length; i++) {
      result = this.fuse(result, vectors[i]);
    }
    
    return result;
  }
  
  /**
   * Calculate synergy score between two vectors
   * @param {Array} a - First vector
   * @param {Array} b - Second vector
   * @returns {number} - Synergy score
   */
  synergy(a, b) {
    if (!Array.isArray(a) || !Array.isArray(b)) {
      throw new Error('Inputs must be arrays');
    }
    
    if (a.length === 0 || b.length === 0) {
      return 0;
    }
    
    // Fuse the vectors
    const fused = this.fuse(a, b);
    
    // Concatenate the vectors
    const concatenated = [...a, ...b];
    
    // Calculate entropy of fused vector
    const fusedEntropy = this._calculateEntropy(fused);
    
    // Calculate entropy of concatenated vector
    const concatenatedEntropy = this._calculateEntropy(concatenated);
    
    // Synergy is the difference between entropies
    return this._round(fusedEntropy - concatenatedEntropy);
  }
  
  /**
   * Apply 18/82 principle to fusion
   * @param {Array} positiveFactors - Positive factors
   * @param {Array} negativeFactors - Negative factors
   * @returns {Array} - Fusion result with 18/82 principle applied
   */
  apply1882Principle(positiveFactors, negativeFactors) {
    if (!Array.isArray(positiveFactors) || !Array.isArray(negativeFactors)) {
      throw new Error('Inputs must be arrays');
    }
    
    if (positiveFactors.length === 0 || negativeFactors.length === 0) {
      return [];
    }
    
    // Determine the minimum length
    const minLength = Math.min(positiveFactors.length, negativeFactors.length);
    
    // Create result array
    const result = [];
    
    // Apply 18/82 principle
    for (let i = 0; i < minLength; i++) {
      result.push(this._round(0.18 * positiveFactors[i] + 0.82 * negativeFactors[i]));
    }
    
    return result;
  }
  
  /**
   * Apply non-linear transformation to vector
   * @param {Array} vector - Input vector
   * @param {number} power - Power for non-linear transformation
   * @returns {Array} - Transformed vector
   */
  nonLinearTransform(vector, power = 1.5) {
    if (!Array.isArray(vector)) {
      throw new Error('Input must be an array');
    }
    
    if (vector.length === 0) {
      return [];
    }
    
    // Apply non-linear transformation
    return vector.map(val => this._round(Math.pow(Math.abs(val), power) * Math.sign(val)));
  }
  
  /**
   * Calculate correlation between two vectors
   * @param {Array} a - First vector
   * @param {Array} b - Second vector
   * @returns {number} - Correlation coefficient
   */
  correlation(a, b) {
    if (!Array.isArray(a) || !Array.isArray(b)) {
      throw new Error('Inputs must be arrays');
    }
    
    if (a.length !== b.length || a.length === 0) {
      throw new Error('Vectors must have the same non-zero length');
    }
    
    // Calculate means
    const meanA = a.reduce((sum, val) => sum + val, 0) / a.length;
    const meanB = b.reduce((sum, val) => sum + val, 0) / b.length;
    
    // Calculate covariance and variances
    let covariance = 0;
    let varianceA = 0;
    let varianceB = 0;
    
    for (let i = 0; i < a.length; i++) {
      const diffA = a[i] - meanA;
      const diffB = b[i] - meanB;
      covariance += diffA * diffB;
      varianceA += diffA * diffA;
      varianceB += diffB * diffB;
    }
    
    // Calculate correlation coefficient
    const correlation = covariance / (Math.sqrt(varianceA) * Math.sqrt(varianceB));
    
    return this._round(correlation);
  }
  
  /**
   * Calculate entropy of a vector
   * @param {Array} vector - Input vector
   * @returns {number} - Entropy value
   * @private
   */
  _calculateEntropy(vector) {
    // Normalize vector to probabilities
    const probabilities = this._normalizeToProbabilities(vector);
    
    // Calculate entropy based on selected estimator
    switch (this.options.entropyEstimator) {
      case 'renyi':
        return this._calculateRenyiEntropy(probabilities);
      case 'tsallis':
        return this._calculateTsallisEntropy(probabilities);
      case 'shannon':
      default:
        return this._calculateShannonEntropy(probabilities);
    }
  }
  
  /**
   * Calculate Shannon entropy
   * @param {Array} probabilities - Probability distribution
   * @returns {number} - Shannon entropy
   * @private
   */
  _calculateShannonEntropy(probabilities) {
    return -probabilities.reduce((sum, p) => {
      return sum + (p > 0 ? p * Math.log2(p) : 0);
    }, 0);
  }
  
  /**
   * Calculate Rényi entropy
   * @param {Array} probabilities - Probability distribution
   * @param {number} alpha - Alpha parameter (default: 2.0)
   * @returns {number} - Rényi entropy
   * @private
   */
  _calculateRenyiEntropy(probabilities, alpha = 2.0) {
    // Handle special case: alpha = 1 (Shannon entropy)
    if (Math.abs(alpha - 1.0) < 0.0001) {
      return this._calculateShannonEntropy(probabilities);
    }
    
    const sum = probabilities.reduce((acc, p) => {
      return acc + (p > 0 ? Math.pow(p, alpha) : 0);
    }, 0);
    
    return (1 / (1 - alpha)) * Math.log2(sum);
  }
  
  /**
   * Calculate Tsallis entropy
   * @param {Array} probabilities - Probability distribution
   * @param {number} q - q parameter (default: 1.5)
   * @returns {number} - Tsallis entropy
   * @private
   */
  _calculateTsallisEntropy(probabilities, q = 1.5) {
    // Handle special case: q = 1 (Shannon entropy)
    if (Math.abs(q - 1.0) < 0.0001) {
      return this._calculateShannonEntropy(probabilities);
    }
    
    const sum = probabilities.reduce((acc, p) => {
      return acc + (p > 0 ? Math.pow(p, q) : 0);
    }, 0);
    
    return (1 - sum) / (q - 1);
  }
  
  /**
   * Normalize values to probabilities
   * @param {Array} values - Input values
   * @returns {Array} - Probability distribution
   * @private
   */
  _normalizeToProbabilities(values) {
    const sum = values.reduce((acc, val) => acc + Math.abs(val), 0);
    
    if (sum === 0) {
      // If sum is 0, return uniform distribution
      return values.map(() => 1 / values.length);
    }
    
    // Normalize values to probabilities
    return values.map(val => Math.abs(val) / sum);
  }
  
  /**
   * Round number to specified precision
   * @param {number} value - Value to round
   * @returns {number} - Rounded value
   * @private
   */
  _round(value) {
    const factor = Math.pow(10, this.options.precision);
    return Math.round(value * factor) / factor;
  }
}

module.exports = FusionOperator;
