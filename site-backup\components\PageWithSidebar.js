import React from 'react';
import Layout from './Layout';
import Sidebar from './Sidebar';
import Navigation from './Navigation';

const PageWithSidebar = ({ children, sidebarItems, sidebarTitle, title }) => {
  return (
    <Layout title={title}>
      <Navigation />
      <div className="flex flex-col md:flex-row gap-8">
        {/* Sidebar - Hidden on mobile, shown on desktop */}
        <div className="hidden md:block md:w-1/4 lg:w-1/5">
          <div className="sticky top-4">
            <Sidebar items={sidebarItems} title={sidebarTitle} />
          </div>
        </div>

        {/* Mobile Sidebar Toggle - Shown on mobile only */}
        <div className="md:hidden mb-4">
          <select
            className="w-full bg-secondary text-white border border-gray-700 rounded p-2"
            onChange={(e) => {
              if (e.target.value) window.location.href = e.target.value;
            }}
            defaultValue=""
          >
            <option value="" disabled>Navigate to...</option>
            {sidebarItems.map((item, index) => (
              item.type === 'category' ? (
                <optgroup key={index} label={item.label}>
                  {item.items.map((subItem, subIndex) => (
                    <option key={`${index}-${subIndex}`} value={subItem.href}>
                      {subItem.label}
                    </option>
                  ))}
                </optgroup>
              ) : (
                <option key={index} value={item.href}>
                  {item.label}
                </option>
              )
            ))}
          </select>
        </div>

        {/* Main Content */}
        <div className="w-full md:w-3/4 lg:w-4/5">
          {children}
        </div>
      </div>
    </Layout>
  );
};

export default PageWithSidebar;
