const express = require('express');
const router = express.Router();
const { authenticateJWT, authorizePartner } = require('../middleware/auth');
const { validateSubmission } = require('../middleware/validators');

// Import controllers
const submissionController = require('../controllers/submissionController');

/**
 * @route POST /api/submissions
 * @desc Submit a new connector for review
 * @access Private (Partner)
 */
router.post('/', authenticateJWT, authorizePartner, validateSubmission, submissionController.submitConnector);

/**
 * @route GET /api/submissions
 * @desc Get all submissions for the authenticated partner
 * @access Private (Partner)
 */
router.get('/', authenticateJWT, authorizePartner, submissionController.getPartnerSubmissions);

/**
 * @route GET /api/submissions/:id
 * @desc Get a specific submission
 * @access Private (Owner or Admin)
 */
router.get('/:id', authenticateJWT, submissionController.getSubmissionById);

/**
 * @route PUT /api/submissions/:id
 * @desc Update a submission (before review)
 * @access Private (Owner)
 */
router.put('/:id', authenticateJWT, submissionController.updateSubmission);

/**
 * @route DELETE /api/submissions/:id
 * @desc Delete a submission (before review)
 * @access Private (Owner)
 */
router.delete('/:id', authenticateJWT, submissionController.deleteSubmission);

/**
 * @route GET /api/submissions/:id/status
 * @desc Get the review status of a submission
 * @access Private (Owner or Admin)
 */
router.get('/:id/status', authenticateJWT, submissionController.getSubmissionStatus);

/**
 * @route PUT /api/submissions/:id/status
 * @desc Update the review status of a submission (Admin only)
 * @access Private (Admin)
 */
router.put('/:id/status', authenticateJWT, submissionController.updateSubmissionStatus);

/**
 * @route POST /api/submissions/:id/feedback
 * @desc Add feedback to a submission (Admin only)
 * @access Private (Admin)
 */
router.post('/:id/feedback', authenticateJWT, submissionController.addSubmissionFeedback);

module.exports = router;
