const express = require('express');
const router = express.Router();
const { authenticateJWT } = require('../middleware/auth');

// Import controllers
const mappingController = require('../controllers/mappingController');

/**
 * @route GET /api/mapping/frameworks
 * @desc Get all available frameworks
 * @access Public
 */
router.get('/frameworks', mappingController.getAllFrameworksHandler);

/**
 * @route GET /api/mapping/frameworks/:id
 * @desc Get a specific framework by ID
 * @access Public
 */
router.get('/frameworks/:id', mappingController.getFrameworkHandler);

/**
 * @route GET /api/mapping/:sourceFramework/:sourceControl/:targetFramework
 * @desc Map a control from one framework to another
 * @access Private
 */
router.get('/:sourceFramework/:sourceControl/:targetFramework', authenticateJWT, mappingController.mapControlHandler);

/**
 * @route POST /api/mapping/:sourceFramework/:targetFramework
 * @desc Map multiple controls from one framework to another
 * @access Private
 */
router.post('/:sourceFramework/:targetFramework', authenticateJWT, mappingController.mapControlsHandler);

/**
 * @route GET /api/mapping/:sourceFramework/:sourceControl/all
 * @desc Get all possible mappings for a control
 * @access Private
 */
router.get('/:sourceFramework/:sourceControl/all', authenticateJWT, mappingController.getAllMappingsForControlHandler);

/**
 * @route GET /api/mapping/:sourceFramework/to/:targetFramework
 * @desc Get all mappings between two frameworks
 * @access Private
 */
router.get('/:sourceFramework/to/:targetFramework', authenticateJWT, mappingController.getFrameworkToFrameworkMappingsHandler);

/**
 * @route POST /api/mapping/:sourceFramework/coverage/:targetFramework
 * @desc Calculate compliance coverage across frameworks
 * @access Private
 */
router.post('/:sourceFramework/coverage/:targetFramework', authenticateJWT, mappingController.calculateComplianceCoverageHandler);

module.exports = router;
