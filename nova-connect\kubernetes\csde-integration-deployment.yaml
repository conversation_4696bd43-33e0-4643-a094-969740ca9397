apiVersion: v1
kind: Namespace
metadata:
  name: nova-connect
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: csde-integration-config
  namespace: nova-connect
data:
  NODE_ENV: "production"
  PORT: "3001"
  LOG_LEVEL: "info"
  ENABLE_CACHING: "true"
  ENABLE_METRICS: "true"
  CACHE_SIZE: "1000"
  DOMAIN: "security"
  BATCH_SIZE: "10"
---
apiVersion: v1
kind: Secret
metadata:
  name: csde-integration-secrets
  namespace: nova-connect
type: Opaque
data:
  # Base64 encoded values
  # Example: echo -n "your-secret" | base64
  # These are placeholder values - replace with actual secrets
  API_KEY: "cGxhY2Vob2xkZXI="  # placeholder
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nova-connect-api
  namespace: nova-connect
  labels:
    app: nova-connect-api
spec:
  replicas: 2
  selector:
    matchLabels:
      app: nova-connect-api
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        app: nova-connect-api
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "3001"
        prometheus.io/path: "/metrics"
    spec:
      containers:
      - name: nova-connect-api
        image: nova-connect:latest
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 3001
          name: http
        env:
        - name: NODE_ENV
          valueFrom:
            configMapKeyRef:
              name: csde-integration-config
              key: NODE_ENV
        - name: PORT
          valueFrom:
            configMapKeyRef:
              name: csde-integration-config
              key: PORT
        - name: LOG_LEVEL
          valueFrom:
            configMapKeyRef:
              name: csde-integration-config
              key: LOG_LEVEL
        - name: ENABLE_CACHING
          valueFrom:
            configMapKeyRef:
              name: csde-integration-config
              key: ENABLE_CACHING
        - name: ENABLE_METRICS
          valueFrom:
            configMapKeyRef:
              name: csde-integration-config
              key: ENABLE_METRICS
        - name: CACHE_SIZE
          valueFrom:
            configMapKeyRef:
              name: csde-integration-config
              key: CACHE_SIZE
        - name: DOMAIN
          valueFrom:
            configMapKeyRef:
              name: csde-integration-config
              key: DOMAIN
        - name: BATCH_SIZE
          valueFrom:
            configMapKeyRef:
              name: csde-integration-config
              key: BATCH_SIZE
        - name: CSDE_API_URL
          value: "http://csde-api:3010"
        - name: API_KEY
          valueFrom:
            secretKeyRef:
              name: csde-integration-secrets
              key: API_KEY
        resources:
          requests:
            cpu: "100m"
            memory: "256Mi"
          limits:
            cpu: "500m"
            memory: "512Mi"
        livenessProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 5
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        volumeMounts:
        - name: data-volume
          mountPath: /app/data
        - name: logs-volume
          mountPath: /app/logs
      volumes:
      - name: data-volume
        persistentVolumeClaim:
          claimName: nova-connect-data-pvc
      - name: logs-volume
        persistentVolumeClaim:
          claimName: nova-connect-logs-pvc
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: csde-api
  namespace: nova-connect
  labels:
    app: csde-api
spec:
  replicas: 2
  selector:
    matchLabels:
      app: csde-api
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        app: csde-api
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "3010"
        prometheus.io/path: "/metrics"
    spec:
      containers:
      - name: csde-api
        image: csde-api:latest
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 3010
          name: http
        env:
        - name: NODE_ENV
          value: "production"
        - name: PORT
          value: "3010"
        - name: LOG_LEVEL
          value: "info"
        resources:
          requests:
            cpu: "200m"
            memory: "512Mi"
          limits:
            cpu: "1000m"
            memory: "1Gi"
        livenessProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 5
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        volumeMounts:
        - name: csde-config-volume
          mountPath: /app/config
        - name: csde-logs-volume
          mountPath: /app/logs
      volumes:
      - name: csde-config-volume
        configMap:
          name: csde-api-config
      - name: csde-logs-volume
        persistentVolumeClaim:
          claimName: csde-logs-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: nova-connect-api
  namespace: nova-connect
  labels:
    app: nova-connect-api
spec:
  selector:
    app: nova-connect-api
  ports:
  - port: 3001
    targetPort: 3001
    name: http
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  name: csde-api
  namespace: nova-connect
  labels:
    app: csde-api
spec:
  selector:
    app: csde-api
  ports:
  - port: 3010
    targetPort: 3010
    name: http
  type: ClusterIP
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: nova-connect-data-pvc
  namespace: nova-connect
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: standard
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: nova-connect-logs-pvc
  namespace: nova-connect
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
  storageClassName: standard
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: csde-logs-pvc
  namespace: nova-connect
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
  storageClassName: standard
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: nova-connect-ingress
  namespace: nova-connect
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "10m"
spec:
  rules:
  - host: nova-connect.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: nova-connect-api
            port:
              number: 3001
  - host: csde-api.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: csde-api
            port:
              number: 3010
