{"name": "novafuse-api", "version": "1.0.0", "description": "NovaFuse GRC API Services", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "dependencies": {"express": "^4.18.2", "mongoose": "^7.5.0", "cors": "^2.8.5", "helmet": "^7.0.0", "morgan": "^1.10.0", "winston": "^3.10.0", "dotenv": "^16.3.1", "jsonwebtoken": "^9.0.1", "bcryptjs": "^2.4.3", "express-validator": "^7.0.1", "redis": "^4.6.8"}, "devDependencies": {"jest": "^29.6.4", "nodemon": "^3.0.1", "supertest": "^6.3.3"}}