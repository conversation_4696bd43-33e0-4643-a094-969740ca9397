/**
 * NovaFuse Universal API Connector - Authentication Manager Tests
 */

const authenticationManager = require('../../src/auth/authentication-manager');
const { credentialRepository } = require('../../src/data/repositories');
const axios = require('axios');

// Mock dependencies
jest.mock('../../src/data/repositories', () => ({
  credentialRepository: {
    createCredential: jest.fn(),
    getDecryptedCredentials: jest.fn(),
    deleteCredential: jest.fn(),
    updateLastUsed: jest.fn()
  }
}));

jest.mock('axios');

describe('Authentication Manager', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });
  
  const mockConnector = {
    id: 'test-connector-1.0',
    authentication: {
      type: 'API_KEY',
      fields: {
        apiKey: {
          type: 'string',
          description: 'API Key',
          required: true,
          sensitive: true
        }
      },
      placement: 'header',
      paramName: 'x-api-key',
      testConnection: {
        endpoint: '/test',
        method: 'GET',
        expectedResponse: {
          status: 200
        }
      }
    },
    configuration: {
      baseUrl: 'https://api.example.com',
      headers: {
        'Content-Type': 'application/json'
      }
    }
  };
  
  const mockCredentials = {
    apiKey: 'test-api-key'
  };
  
  const mockRequest = {
    method: 'GET',
    url: 'https://api.example.com/data',
    headers: {
      'Content-Type': 'application/json'
    }
  };
  
  it('should initialize successfully', async () => {
    const result = await authenticationManager.initialize();
    expect(result).toBe(true);
  });
  
  it('should store credentials', async () => {
    const mockCredentialData = {
      id: 'cred-12345',
      name: 'Test Credential',
      connectorId: 'test-connector-1.0',
      credentials: new Map(Object.entries(mockCredentials)),
      ownerId: 'user-123'
    };
    
    credentialRepository.createCredential.mockResolvedValue(mockCredentialData);
    
    const result = await authenticationManager.storeCredentials(
      'test-connector-1.0',
      mockCredentials,
      'user-123',
      'Test Credential'
    );
    
    expect(credentialRepository.createCredential).toHaveBeenCalled();
    expect(result).toEqual(mockCredentialData);
  });
  
  it('should get credentials', async () => {
    credentialRepository.getDecryptedCredentials.mockResolvedValue(mockCredentials);
    
    const result = await authenticationManager.getCredentials('cred-12345');
    
    expect(credentialRepository.getDecryptedCredentials).toHaveBeenCalledWith('cred-12345');
    expect(result).toEqual(mockCredentials);
  });
  
  it('should delete credentials', async () => {
    credentialRepository.deleteCredential.mockResolvedValue(true);
    
    const result = await authenticationManager.deleteCredentials('cred-12345');
    
    expect(credentialRepository.deleteCredential).toHaveBeenCalledWith('cred-12345');
    expect(result).toBe(true);
  });
  
  it('should authenticate request with API key', async () => {
    const result = await authenticationManager.authenticateRequest(
      mockConnector,
      mockCredentials,
      mockRequest
    );
    
    expect(result.headers['x-api-key']).toBe('test-api-key');
  });
  
  it('should authenticate request with Basic auth', async () => {
    const basicAuthConnector = {
      ...mockConnector,
      authentication: {
        type: 'BASIC',
        fields: {
          username: {
            type: 'string',
            required: true
          },
          password: {
            type: 'string',
            required: true,
            sensitive: true
          }
        }
      }
    };
    
    const basicAuthCredentials = {
      username: 'testuser',
      password: 'testpass'
    };
    
    const result = await authenticationManager.authenticateRequest(
      basicAuthConnector,
      basicAuthCredentials,
      mockRequest
    );
    
    const expectedAuthHeader = `Basic ${Buffer.from('testuser:testpass').toString('base64')}`;
    expect(result.headers['Authorization']).toBe(expectedAuthHeader);
  });
  
  it('should test connection successfully', async () => {
    axios.mockResolvedValue({
      status: 200,
      statusText: 'OK',
      data: { success: true }
    });
    
    const result = await authenticationManager.testConnection(
      mockConnector,
      mockCredentials
    );
    
    expect(axios).toHaveBeenCalled();
    expect(result.success).toBe(true);
  });
  
  it('should handle connection test failure', async () => {
    axios.mockRejectedValue({
      message: 'Request failed',
      response: {
        status: 401,
        statusText: 'Unauthorized',
        data: { error: 'Invalid API key' }
      }
    });
    
    const result = await authenticationManager.testConnection(
      mockConnector,
      mockCredentials
    );
    
    expect(axios).toHaveBeenCalled();
    expect(result.success).toBe(false);
    expect(result.error.status).toBe(401);
  });
  
  it('should handle no auth', async () => {
    const noAuthConnector = {
      ...mockConnector,
      authentication: {
        type: 'NONE'
      }
    };
    
    const result = await authenticationManager.authenticateRequest(
      noAuthConnector,
      {},
      mockRequest
    );
    
    expect(result).toEqual(mockRequest);
  });
});
