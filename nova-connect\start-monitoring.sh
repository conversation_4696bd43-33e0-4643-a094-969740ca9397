#!/bin/bash
# Start the monitoring infrastructure for NovaConnect UAC

# Create necessary directories
mkdir -p monitoring/prometheus
mkdir -p monitoring/grafana/provisioning/datasources
mkdir -p monitoring/grafana/provisioning/dashboards

# Start the monitoring infrastructure
docker-compose -f docker-compose.monitoring.yml up -d

# Wait for services to start
echo "Waiting for services to start..."
sleep 5

# Open Grafana in the browser
echo "Opening Grafana in the browser..."
echo "Username: admin"
echo "Password: admin"
echo "URL: http://localhost:3000"

# Open Prometheus in the browser
echo "Opening Prometheus in the browser..."
echo "URL: http://localhost:9090"

# Open Zipkin in the browser
echo "Opening Zipkin in the browser..."
echo "URL: http://localhost:9411"

# Open Jaeger in the browser
echo "Opening Jaeger in the browser..."
echo "URL: http://localhost:16686"

# Open NovaConnect in the browser
echo "Opening NovaConnect in the browser..."
echo "URL: http://localhost:3001"

echo "Monitoring infrastructure started successfully!"
