/**
 * Security Tests for NovaTrack API
 * 
 * These tests verify the security of the NovaTrack API endpoints
 */

const request = require('supertest');
const { describe, it, beforeAll, afterAll, expect } = require('@jest/globals');
const fs = require('fs');
const path = require('path');
const os = require('os');

// Import the Express app
const app = require('../../../server');

describe('NovaTrack API - Security Tests', () => {
  let tempDir;
  let testRequirementId;
  
  beforeAll(async () => {
    // Create a temporary directory for test data
    tempDir = fs.mkdtempSync(path.join(os.tmpdir(), 'novatrack-security-test-'));
    
    // Configure the app to use the temporary directory
    process.env.NOVATRACK_DATA_DIR = tempDir;
    
    // Wait for the app to initialize
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Create a test requirement
    const response = await request(app)
      .post('/api/novatrack/requirements')
      .send({
        name: 'Security Test Requirement'
      });
    
    testRequirementId = response.body.id;
  });
  
  afterAll(async () => {
    // Clean up the temporary directory
    if (fs.existsSync(tempDir)) {
      fs.rmSync(tempDir, { recursive: true, force: true });
    }
    
    // Reset the environment variable
    delete process.env.NOVATRACK_DATA_DIR;
    
    // Close the server
    await new Promise(resolve => {
      if (app.server) {
        app.server.close(resolve);
      } else {
        resolve();
      }
    });
  });
  
  describe('Input Validation', () => {
    it('should reject XSS attacks in requirement name', async () => {
      const response = await request(app)
        .post('/api/novatrack/requirements')
        .send({
          name: '<script>alert("XSS")</script>',
          description: 'Test description'
        });
      
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
    });
    
    it('should reject XSS attacks in requirement description', async () => {
      const response = await request(app)
        .post('/api/novatrack/requirements')
        .send({
          name: 'Test Requirement',
          description: '<script>alert("XSS")</script>'
        });
      
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
    });
    
    it('should reject XSS attacks in activity name', async () => {
      const response = await request(app)
        .post('/api/novatrack/activities')
        .send({
          name: '<script>alert("XSS")</script>',
          requirement_id: testRequirementId
        });
      
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
    });
    
    it('should reject XSS attacks in activity description', async () => {
      const response = await request(app)
        .post('/api/novatrack/activities')
        .send({
          name: 'Test Activity',
          description: '<script>alert("XSS")</script>',
          requirement_id: testRequirementId
        });
      
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
    });
  });
  
  describe('SQL Injection Prevention', () => {
    it('should prevent SQL injection in requirement ID', async () => {
      const response = await request(app)
        .get('/api/novatrack/requirements/1%27%20OR%20%271%27=%271');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
    });
    
    it('should prevent SQL injection in activity ID', async () => {
      const response = await request(app)
        .get('/api/novatrack/activities/1%27%20OR%20%271%27=%271');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
    });
    
    it('should prevent SQL injection in query parameters', async () => {
      const response = await request(app)
        .get('/api/novatrack/requirements')
        .query({ framework: "' OR '1'='1" });
      
      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBe(0); // Should not return all requirements
    });
  });
  
  describe('Path Traversal Prevention', () => {
    it('should prevent path traversal in requirement ID', async () => {
      const response = await request(app)
        .get('/api/novatrack/requirements/..%2F..%2F..%2Fetc%2Fpasswd');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
    });
    
    it('should prevent path traversal in activity ID', async () => {
      const response = await request(app)
        .get('/api/novatrack/activities/..%2F..%2F..%2Fetc%2Fpasswd');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
    });
  });
  
  describe('CSRF Protection', () => {
    it('should include CSRF protection headers', async () => {
      const response = await request(app)
        .get('/api/novatrack/requirements');
      
      expect(response.headers).toHaveProperty('x-content-type-options', 'nosniff');
      expect(response.headers).toHaveProperty('x-frame-options', 'DENY');
      expect(response.headers).toHaveProperty('x-xss-protection', '1; mode=block');
    });
  });
  
  describe('Rate Limiting', () => {
    it('should apply rate limiting for multiple requests', async () => {
      // Make multiple requests in quick succession
      const requests = [];
      for (let i = 0; i < 50; i++) {
        requests.push(request(app).get('/api/novatrack/requirements'));
      }
      
      const responses = await Promise.all(requests);
      
      // At least one response should have rate limiting headers
      const hasRateLimitHeaders = responses.some(response => 
        response.headers['x-ratelimit-limit'] !== undefined &&
        response.headers['x-ratelimit-remaining'] !== undefined
      );
      
      expect(hasRateLimitHeaders).toBe(true);
    });
  });
  
  describe('Authentication and Authorization', () => {
    // Note: These tests assume that authentication is implemented
    // If authentication is not implemented, these tests will fail
    
    it('should require authentication for sensitive operations', async () => {
      // This test is a placeholder and may need to be adjusted based on the actual authentication implementation
      const response = await request(app)
        .post('/api/novatrack/requirements')
        .send({
          name: 'Test Requirement'
        })
        .set('Authorization', 'invalid_token');
      
      // If authentication is implemented, this should return 401
      // If authentication is not implemented, this will pass anyway
      expect([201, 401]).toContain(response.status);
    });
  });
  
  describe('Error Handling', () => {
    it('should not expose sensitive information in error messages', async () => {
      const response = await request(app)
        .get('/api/novatrack/requirements/invalid_id');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
      expect(response.body.error).not.toContain('stack');
      expect(response.body.error).not.toContain('at ');
      expect(response.body.error).not.toContain('node_modules');
    });
  });
});
