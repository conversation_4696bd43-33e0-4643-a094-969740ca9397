const controllers = require('../../../../apis/security/controls/controllers');
const models = require('../../../../apis/security/controls/models');

// Mock the models
jest.mock('../../../../apis/security/controls/models', () => ({
  controlFrameworks: [
    {
      id: 'cf-********',
      name: 'NIST SP 800-53',
      description: 'Security and Privacy Controls for Information Systems and Organizations',
      version: 'Rev. 5',
      category: 'government',
      status: 'active',
      controls: [
        {
          id: 'control-12345',
          identifier: 'AC-1',
          title: 'Access Control Policy and Procedures',
          description: 'The organization develops, documents, and disseminates an access control policy...',
          category: 'access-control',
          priority: 'P1',
          status: 'active'
        },
        {
          id: 'control-67890',
          identifier: 'AC-2',
          title: 'Account Management',
          description: 'The organization manages information system accounts...',
          category: 'access-control',
          priority: 'P1',
          status: 'active'
        }
      ],
      createdAt: '2023-01-01T00:00:00Z',
      updatedAt: '2023-01-01T00:00:00Z'
    },
    {
      id: 'cf-********',
      name: 'ISO 27001',
      description: 'Information Security Management System Requirements',
      version: '2022',
      category: 'international',
      status: 'active',
      controls: [
        {
          id: 'control-54321',
          identifier: 'A.5.1',
          title: 'Information Security Policies',
          description: 'To provide management direction and support for information security...',
          category: 'policies',
          priority: 'high',
          status: 'active'
        }
      ],
      createdAt: '2023-02-01T00:00:00Z',
      updatedAt: '2023-02-01T00:00:00Z'
    }
  ],
  controlTests: [
    {
      id: 'ct-********',
      name: 'Annual Access Control Review',
      description: 'Annual review of access control policies and procedures',
      controlId: 'control-12345',
      frameworkId: 'cf-********',
      type: 'manual',
      frequency: 'annual',
      status: 'active',
      testSteps: [
        {
          id: 'step-12345',
          order: 1,
          description: 'Review access control policy document',
          expectedResult: 'Policy document exists and is up to date',
          evidence: 'Document screenshot or reference'
        },
        {
          id: 'step-67890',
          order: 2,
          description: 'Verify policy approval by management',
          expectedResult: 'Approval signature or record exists',
          evidence: 'Approval record screenshot'
        }
      ],
      lastExecuted: '2023-03-15T00:00:00Z',
      nextScheduled: '2024-03-15T00:00:00Z',
      owner: 'Security Team',
      createdAt: '2023-01-15T00:00:00Z',
      updatedAt: '2023-03-15T00:00:00Z'
    },
    {
      id: 'ct-********',
      name: 'Quarterly Account Review',
      description: 'Quarterly review of user accounts and privileges',
      controlId: 'control-67890',
      frameworkId: 'cf-********',
      type: 'automated',
      frequency: 'quarterly',
      status: 'active',
      testSteps: [
        {
          id: 'step-54321',
          order: 1,
          description: 'Run account audit script',
          expectedResult: 'Script executes successfully and generates report',
          evidence: 'Audit report'
        }
      ],
      lastExecuted: '2023-04-01T00:00:00Z',
      nextScheduled: '2023-07-01T00:00:00Z',
      owner: 'IT Team',
      createdAt: '2023-01-20T00:00:00Z',
      updatedAt: '2023-04-01T00:00:00Z'
    }
  ],
  testResults: [
    {
      id: 'tr-********',
      testId: 'ct-********',
      executionDate: '2023-03-15T00:00:00Z',
      status: 'passed',
      summary: 'All test steps passed successfully',
      stepResults: [
        {
          stepId: 'step-12345',
          status: 'passed',
          actualResult: 'Policy document reviewed and confirmed up to date',
          evidence: 'https://example.com/evidence/policy-screenshot.png',
          notes: 'Policy was updated in January 2023'
        },
        {
          stepId: 'step-67890',
          status: 'passed',
          actualResult: 'Approval signature verified',
          evidence: 'https://example.com/evidence/approval-record.png',
          notes: 'Approved by CISO on January 10, 2023'
        }
      ],
      tester: 'Jane Smith',
      reviewedBy: 'John Doe',
      reviewDate: '2023-03-20T00:00:00Z',
      createdAt: '2023-03-15T00:00:00Z',
      updatedAt: '2023-03-20T00:00:00Z'
    },
    {
      id: 'tr-********',
      testId: 'ct-********',
      executionDate: '2023-04-01T00:00:00Z',
      status: 'failed',
      summary: 'Test failed due to inactive accounts found',
      stepResults: [
        {
          stepId: 'step-54321',
          status: 'failed',
          actualResult: 'Script executed but found 5 inactive accounts not removed',
          evidence: 'https://example.com/evidence/account-audit-report.pdf',
          notes: 'Inactive accounts belong to former employees'
        }
      ],
      tester: 'Automated System',
      reviewedBy: 'Jane Smith',
      reviewDate: '2023-04-02T00:00:00Z',
      createdAt: '2023-04-01T00:00:00Z',
      updatedAt: '2023-04-02T00:00:00Z'
    }
  ]
}));

// Mock Express request and response
const mockRequest = (params = {}, query = {}, body = {}) => ({
  params,
  query,
  body
});

const mockResponse = () => {
  const res = {};
  res.status = jest.fn().mockReturnValue(res);
  res.json = jest.fn().mockReturnValue(res);
  return res;
};

describe('Control Testing Controllers', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('getControlFrameworks', () => {
    it('should return all control frameworks with default pagination', () => {
      const req = mockRequest({}, {});
      const res = mockResponse();

      controllers.getControlFrameworks(req, res);

      expect(res.json).toHaveBeenCalledWith({
        data: models.controlFrameworks,
        pagination: {
          total: 2,
          page: 1,
          limit: 10,
          pages: 1
        }
      });
    });

    it('should filter frameworks by category', () => {
      const req = mockRequest({}, { category: 'government' });
      const res = mockResponse();

      controllers.getControlFrameworks(req, res);

      expect(res.json).toHaveBeenCalledWith({
        data: [models.controlFrameworks[0]],
        pagination: {
          total: 1,
          page: 1,
          limit: 10,
          pages: 1
        }
      });
    });

    it('should handle errors', () => {
      const req = mockRequest();
      const res = mockResponse();

      // Force an error
      jest.spyOn(console, 'error').mockImplementation(() => {});
      jest.spyOn(Array.prototype, 'filter').mockImplementation(() => {
        throw new Error('Test error');
      });

      controllers.getControlFrameworks(req, res);

      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Internal Server Error',
        message: 'Test error'
      });

      // Restore console.error
      console.error.mockRestore();
    });
  });

  describe('getControlFrameworkById', () => {
    it('should return a specific control framework by ID', () => {
      const req = mockRequest({ id: 'cf-********' });
      const res = mockResponse();

      controllers.getControlFrameworkById(req, res);

      expect(res.json).toHaveBeenCalledWith({
        data: models.controlFrameworks[0]
      });
    });

    it('should return 404 if framework not found', () => {
      const req = mockRequest({ id: 'non-existent-id' });
      const res = mockResponse();

      controllers.getControlFrameworkById(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'Control framework with ID non-existent-id not found'
      });
    });
  });

  describe('getControlById', () => {
    it('should return a specific control by ID', () => {
      const req = mockRequest({ frameworkId: 'cf-********', controlId: 'control-12345' });
      const res = mockResponse();

      controllers.getControlById(req, res);

      expect(res.json).toHaveBeenCalledWith({
        data: models.controlFrameworks[0].controls[0]
      });
    });

    it('should return 404 if control not found', () => {
      const req = mockRequest({ frameworkId: 'cf-********', controlId: 'non-existent-id' });
      const res = mockResponse();

      controllers.getControlById(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'Control with ID non-existent-id not found'
      });
    });
  });

  describe('getControlTests', () => {
    it('should return all control tests with default pagination', () => {
      const req = mockRequest({}, {});
      const res = mockResponse();

      controllers.getControlTests(req, res);

      expect(res.json).toHaveBeenCalledWith({
        data: models.controlTests,
        pagination: {
          total: 2,
          page: 1,
          limit: 10,
          pages: 1
        }
      });
    });

    it('should filter tests by type', () => {
      const req = mockRequest({}, { type: 'manual' });
      const res = mockResponse();

      controllers.getControlTests(req, res);

      expect(res.json).toHaveBeenCalledWith({
        data: [models.controlTests[0]],
        pagination: {
          total: 1,
          page: 1,
          limit: 10,
          pages: 1
        }
      });
    });

    it('should filter tests by frequency', () => {
      const req = mockRequest({}, { frequency: 'quarterly' });
      const res = mockResponse();

      controllers.getControlTests(req, res);

      expect(res.json).toHaveBeenCalledWith({
        data: [models.controlTests[1]],
        pagination: {
          total: 1,
          page: 1,
          limit: 10,
          pages: 1
        }
      });
    });
  });

  describe('getControlTestById', () => {
    it('should return a specific control test by ID', () => {
      const req = mockRequest({ id: 'ct-********' });
      const res = mockResponse();

      controllers.getControlTestById(req, res);

      expect(res.json).toHaveBeenCalledWith({
        data: models.controlTests[0]
      });
    });

    it('should return 404 if test not found', () => {
      const req = mockRequest({ id: 'non-existent-id' });
      const res = mockResponse();

      controllers.getControlTestById(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'Control test with ID non-existent-id not found'
      });
    });
  });

  describe('createControlTest', () => {
    it('should create a new control test', () => {
      const newTest = {
        name: 'Monthly Password Policy Check',
        description: 'Monthly verification of password policy enforcement',
        controlId: 'control-12345',
        frameworkId: 'cf-********',
        type: 'automated',
        frequency: 'monthly',
        status: 'active',
        testSteps: [
          {
            order: 1,
            description: 'Run password policy audit script',
            expectedResult: 'All accounts comply with password policy',
            evidence: 'Audit report'
          }
        ],
        owner: 'Security Team'
      };

      const req = mockRequest({}, {}, newTest);
      const res = mockResponse();

      // Mock Date and UUID
      const originalDateNow = Date.now;
      Date.now = jest.fn(() => *************); // 2020-01-01T00:00:00Z
      global.Date = jest.fn(() => ({
        toISOString: () => '2020-01-01T00:00:00Z'
      }));
      jest.mock('uuid', () => ({
        v4: jest.fn(() => '********-0000-0000-0000-********0000')
      }));

      controllers.createControlTest(req, res);

      expect(res.status).toHaveBeenCalledWith(201);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({
          name: 'Monthly Password Policy Check',
          type: 'automated'
        }),
        message: 'Control test created successfully'
      }));

      // Restore Date
      Date.now = originalDateNow;
    });
  });

  describe('updateControlTest', () => {
    it('should update an existing control test', () => {
      const updatedTest = {
        name: 'Updated Test Name',
        frequency: 'semi-annual'
      };

      const req = mockRequest({ id: 'ct-********' }, {}, updatedTest);
      const res = mockResponse();

      controllers.updateControlTest(req, res);

      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({
          id: 'ct-********',
          name: 'Updated Test Name',
          frequency: 'semi-annual'
        }),
        message: 'Control test updated successfully'
      }));
    });

    it('should return 404 if test not found', () => {
      const req = mockRequest({ id: 'non-existent-id' }, {}, { name: 'Updated Name' });
      const res = mockResponse();

      controllers.updateControlTest(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'Control test with ID non-existent-id not found'
      });
    });
  });

  describe('deleteControlTest', () => {
    it('should delete an existing control test', () => {
      const req = mockRequest({ id: 'ct-********' });
      const res = mockResponse();

      controllers.deleteControlTest(req, res);

      expect(res.json).toHaveBeenCalledWith({
        message: 'Control test deleted successfully'
      });
    });

    it('should return 404 if test not found', () => {
      const req = mockRequest({ id: 'non-existent-id' });
      const res = mockResponse();

      controllers.deleteControlTest(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'Control test with ID non-existent-id not found'
      });
    });
  });

  describe('getTestResults', () => {
    it('should return all test results with default pagination', () => {
      const req = mockRequest({}, {});
      const res = mockResponse();

      controllers.getTestResults(req, res);

      expect(res.json).toHaveBeenCalledWith({
        data: models.testResults,
        pagination: {
          total: 2,
          page: 1,
          limit: 10,
          pages: 1
        }
      });
    });

    it('should filter results by status', () => {
      const req = mockRequest({}, { status: 'passed' });
      const res = mockResponse();

      controllers.getTestResults(req, res);

      expect(res.json).toHaveBeenCalledWith({
        data: [models.testResults[0]],
        pagination: {
          total: 1,
          page: 1,
          limit: 10,
          pages: 1
        }
      });
    });

    it('should filter results by test ID', () => {
      const req = mockRequest({}, { testId: 'ct-********' });
      const res = mockResponse();

      controllers.getTestResults(req, res);

      expect(res.json).toHaveBeenCalledWith({
        data: [models.testResults[1]],
        pagination: {
          total: 1,
          page: 1,
          limit: 10,
          pages: 1
        }
      });
    });
  });

  describe('getTestResultById', () => {
    it('should return a specific test result by ID', () => {
      const req = mockRequest({ id: 'tr-********' });
      const res = mockResponse();

      controllers.getTestResultById(req, res);

      expect(res.json).toHaveBeenCalledWith({
        data: models.testResults[0]
      });
    });

    it('should return 404 if result not found', () => {
      const req = mockRequest({ id: 'non-existent-id' });
      const res = mockResponse();

      controllers.getTestResultById(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'Test result with ID non-existent-id not found'
      });
    });
  });

  describe('createTestResult', () => {
    it('should create a new test result', () => {
      const newResult = {
        testId: 'ct-********',
        status: 'passed',
        summary: 'All test steps passed successfully',
        stepResults: [
          {
            stepId: 'step-12345',
            status: 'passed',
            actualResult: 'Policy document reviewed and confirmed up to date',
            evidence: 'https://example.com/evidence/policy-screenshot.png',
            notes: 'Policy was updated in January 2023'
          },
          {
            stepId: 'step-67890',
            status: 'passed',
            actualResult: 'Approval signature verified',
            evidence: 'https://example.com/evidence/approval-record.png',
            notes: 'Approved by CISO on January 10, 2023'
          }
        ],
        tester: 'Jane Smith'
      };

      const req = mockRequest({}, {}, newResult);
      const res = mockResponse();

      controllers.createTestResult(req, res);

      expect(res.status).toHaveBeenCalledWith(201);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({
          testId: 'ct-********',
          status: 'passed'
        }),
        message: 'Test result created successfully'
      }));
    });
  });

  describe('updateTestResult', () => {
    it('should update an existing test result', () => {
      const updatedResult = {
        status: 'failed',
        summary: 'Updated test result summary'
      };

      const req = mockRequest({ id: 'tr-********' }, {}, updatedResult);
      const res = mockResponse();

      controllers.updateTestResult(req, res);

      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({
          id: 'tr-********',
          status: 'failed',
          summary: 'Updated test result summary'
        }),
        message: 'Test result updated successfully'
      }));
    });

    it('should return 404 if result not found', () => {
      const req = mockRequest({ id: 'non-existent-id' }, {}, { status: 'failed' });
      const res = mockResponse();

      controllers.updateTestResult(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'Test result with ID non-existent-id not found'
      });
    });
  });

  describe('reviewTestResult', () => {
    it('should review a test result', () => {
      const reviewData = {
        reviewedBy: 'John Doe',
        reviewNotes: 'Reviewed and approved'
      };

      const req = mockRequest({ id: 'tr-********' }, {}, reviewData);
      const res = mockResponse();

      controllers.reviewTestResult(req, res);

      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({
          id: 'tr-********',
          reviewedBy: 'John Doe'
        }),
        message: 'Test result reviewed successfully'
      }));
    });

    it('should return 404 if result not found', () => {
      const req = mockRequest({ id: 'non-existent-id' }, {}, { reviewedBy: 'John Doe' });
      const res = mockResponse();

      controllers.reviewTestResult(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'Test result with ID non-existent-id not found'
      });
    });
  });
});
