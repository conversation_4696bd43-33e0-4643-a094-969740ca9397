# Contributing to NovaFuse

Thank you for your interest in contributing to NovaFuse! This document provides guidelines and instructions for contributing to the project.

## Code of Conduct

Please read and follow our [Code of Conduct](CODE_OF_CONDUCT.md) to foster an open and welcoming environment.

## Getting Started

### Prerequisites

- Node.js (v14 or higher)
- <PERSON>er and Docker Compose
- MongoDB
- Git

### Setting Up the Development Environment

1. Fork the repository you want to contribute to:
   - [nova-fuse](https://github.com/Dartan1983/nova-fuse)
   - [nova-connect](https://github.com/Dartan1983/nova-connect)
   - [nova-grc-apis](https://github.com/Dartan1983/nova-grc-apis)
   - [nova-ui](https://github.com/Dartan1983/nova-ui)
   - [nova-gateway](https://github.com/Dartan1983/nova-gateway)

2. Clone your fork:
   ```
   git clone https://github.com/YOUR_USERNAME/REPOSITORY_NAME.git
   ```

3. Add the upstream repository:
   ```
   git remote add upstream https://github.com/Dartan1983/REPOSITORY_NAME.git
   ```

4. Install dependencies:
   ```
   npm install
   ```

5. Configure environment variables:
   ```
   cp .env.example .env
   # Edit .env with your configuration
   ```

6. Start the development server:
   ```
   npm run dev
   ```

## Development Workflow

### Branching Strategy

- `main` - Production-ready code
- `develop` - Development branch
- `feature/*` - Feature branches
- `bugfix/*` - Bug fix branches
- `release/*` - Release branches

### Making Changes

1. Create a new branch from `develop`:
   ```
   git checkout develop
   git pull upstream develop
   git checkout -b feature/your-feature-name
   ```

2. Make your changes and commit them:
   ```
   git add .
   git commit -m "feat: add your feature"
   ```

   Please follow the [Conventional Commits](https://www.conventionalcommits.org/) specification for commit messages.

3. Push your changes to your fork:
   ```
   git push origin feature/your-feature-name
   ```

4. Create a pull request to the `develop` branch of the upstream repository.

### Pull Request Process

1. Ensure your code follows the coding standards
2. Update documentation if necessary
3. Write tests for your changes
4. Ensure all tests pass
5. Get approval from at least one reviewer

## Coding Standards

### JavaScript/TypeScript

- Follow the ESLint configuration in each repository
- Use ES6+ features
- Use async/await for asynchronous code
- Document your code with JSDoc comments

### React

- Use functional components and hooks
- Follow the component structure in the repository
- Use the provided UI components
- Implement proper error handling

### Testing

- Write unit tests for all new features
- Maintain test coverage of at least 80%
- Write integration tests for API endpoints
- Write end-to-end tests for critical workflows

## Documentation

- Update documentation for any changes to the API
- Document new features and changes to existing features
- Keep the README up to date
- Add JSDoc comments to your code

## Reporting Bugs

Please report bugs by opening an issue in the appropriate repository. Include the following information:

- A clear and descriptive title
- Steps to reproduce the bug
- Expected behavior
- Actual behavior
- Screenshots if applicable
- Environment information (browser, OS, etc.)

## Requesting Features

Please request features by opening an issue in the appropriate repository. Include the following information:

- A clear and descriptive title
- A detailed description of the feature
- Why the feature would be useful
- Any alternatives you've considered
- Mockups or examples if applicable

## License

By contributing to NovaFuse, you agree that your contributions will be licensed under the project's MIT License.
