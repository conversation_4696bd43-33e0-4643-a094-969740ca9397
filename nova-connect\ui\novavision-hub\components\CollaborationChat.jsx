/**
 * CollaborationChat Component
 * 
 * A component for real-time chat in collaborative sessions.
 */

import React, { useState, useRef, useEffect } from 'react';
import PropTypes from 'prop-types';
import { useCollaboration } from '../collaboration/CollaborationContext';
import { useAuth } from '../auth/AuthContext';
import { useTheme } from '../theme/ThemeContext';
import { Animated } from './Animated';

/**
 * CollaborationChat component
 * 
 * @param {Object} props - Component props
 * @param {string} [props.roomId] - Room ID (if not using the current room from context)
 * @param {boolean} [props.showHeader=true] - Whether to show the header
 * @param {boolean} [props.showUserList=true] - Whether to show the user list
 * @param {boolean} [props.showTimestamp=true] - Whether to show message timestamps
 * @param {boolean} [props.autoScroll=true] - Whether to auto-scroll to the bottom
 * @param {number} [props.maxHeight=400] - Maximum height of the chat
 * @param {string} [props.className] - Additional CSS class names
 * @param {Object} [props.style] - Additional inline styles
 * @returns {React.ReactElement} CollaborationChat component
 */
const CollaborationChat = ({
  roomId: externalRoomId,
  showHeader = true,
  showUserList = true,
  showTimestamp = true,
  autoScroll = true,
  maxHeight = 400,
  className = '',
  style = {}
}) => {
  const { theme } = useTheme();
  const { user } = useAuth();
  const {
    isConnected,
    isLoading,
    error,
    activeUsers,
    currentRoom,
    messages,
    sendMessage
  } = useCollaboration();
  
  // State
  const [messageText, setMessageText] = useState('');
  const [isSending, setIsSending] = useState(false);
  
  // Refs
  const messagesEndRef = useRef(null);
  const chatContainerRef = useRef(null);
  
  // Get room ID
  const roomId = externalRoomId || (currentRoom ? currentRoom.id : null);
  
  // Scroll to bottom
  const scrollToBottom = () => {
    if (messagesEndRef.current && autoScroll) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  };
  
  // Scroll to bottom when messages change
  useEffect(() => {
    scrollToBottom();
  }, [messages]);
  
  // Handle message submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!messageText.trim() || !roomId || !isConnected || isSending) {
      return;
    }
    
    setIsSending(true);
    
    try {
      await sendMessage(messageText.trim());
      setMessageText('');
    } catch (err) {
      console.error('Error sending message:', err);
    } finally {
      setIsSending(false);
    }
  };
  
  // Format timestamp
  const formatTimestamp = (timestamp) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };
  
  // Format date
  const formatDate = (timestamp) => {
    const date = new Date(timestamp);
    return date.toLocaleDateString();
  };
  
  // Group messages by date
  const groupedMessages = messages.reduce((groups, message) => {
    const date = formatDate(message.timestamp);
    
    if (!groups[date]) {
      groups[date] = [];
    }
    
    groups[date].push(message);
    
    return groups;
  }, {});
  
  // Check if user is in the room
  const isUserInRoom = currentRoom && user && currentRoom.members.includes(user.id);
  
  return (
    <div
      className={`bg-surface rounded-lg shadow-md overflow-hidden flex flex-col ${className}`}
      style={{ ...style, maxHeight }}
      data-testid="collaboration-chat"
    >
      {/* Header */}
      {showHeader && (
        <div className="bg-background p-3 border-b border-divider flex justify-between items-center">
          <div>
            <h3 className="text-lg font-medium text-textPrimary">
              {currentRoom ? currentRoom.name : 'Chat'}
            </h3>
            {currentRoom && (
              <p className="text-sm text-textSecondary">
                {currentRoom.description}
              </p>
            )}
          </div>
          
          {isConnected ? (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-success bg-opacity-10 text-success">
              <span className="w-2 h-2 rounded-full bg-success mr-1"></span>
              Connected
            </span>
          ) : (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-error bg-opacity-10 text-error">
              <span className="w-2 h-2 rounded-full bg-error mr-1"></span>
              Disconnected
            </span>
          )}
        </div>
      )}
      
      <div className="flex flex-1 overflow-hidden">
        {/* Messages */}
        <div
          ref={chatContainerRef}
          className="flex-1 p-4 overflow-y-auto"
        >
          {isLoading ? (
            <div className="flex items-center justify-center h-full">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
            </div>
          ) : error ? (
            <div className="bg-error bg-opacity-10 border border-error text-error px-4 py-3 rounded">
              {error.message || 'An error occurred'}
            </div>
          ) : !roomId ? (
            <div className="text-center text-textSecondary py-4">
              No room selected
            </div>
          ) : messages.length === 0 ? (
            <div className="text-center text-textSecondary py-4">
              No messages yet
            </div>
          ) : (
            <div className="space-y-4">
              {Object.entries(groupedMessages).map(([date, dateMessages]) => (
                <div key={date}>
                  <div className="flex items-center my-4">
                    <div className="flex-1 border-t border-divider"></div>
                    <div className="mx-4 text-xs text-textSecondary">{date}</div>
                    <div className="flex-1 border-t border-divider"></div>
                  </div>
                  
                  <div className="space-y-3">
                    {dateMessages.map((message) => (
                      <Animated
                        key={message.id}
                        animation="fadeIn"
                        className={`flex ${message.userId === 'system' ? 'justify-center' : message.userId === user?.id ? 'justify-end' : 'justify-start'}`}
                      >
                        {message.userId === 'system' ? (
                          <div className="bg-background text-textSecondary text-xs py-1 px-3 rounded-full">
                            {message.content}
                          </div>
                        ) : (
                          <div className={`max-w-xs ${message.userId === user?.id ? 'bg-primary text-primaryContrast' : 'bg-background text-textPrimary'} rounded-lg px-4 py-2 shadow-sm`}>
                            {message.userId !== user?.id && (
                              <div className="font-medium text-xs mb-1">
                                {message.userDisplayName || 'Unknown User'}
                              </div>
                            )}
                            <div className="break-words">
                              {message.content}
                            </div>
                            {showTimestamp && (
                              <div className={`text-xs mt-1 text-right ${message.userId === user?.id ? 'text-primaryContrast text-opacity-75' : 'text-textSecondary'}`}>
                                {formatTimestamp(message.timestamp)}
                              </div>
                            )}
                          </div>
                        )}
                      </Animated>
                    ))}
                  </div>
                </div>
              ))}
              <div ref={messagesEndRef} />
            </div>
          )}
        </div>
        
        {/* User list */}
        {showUserList && (
          <div className="w-48 border-l border-divider p-3 overflow-y-auto hidden md:block">
            <h4 className="text-sm font-medium text-textPrimary mb-2">
              Active Users ({activeUsers.length})
            </h4>
            <ul className="space-y-2">
              {activeUsers.map((activeUser) => (
                <li key={activeUser.id} className="flex items-center">
                  <div className="relative">
                    <img
                      src={activeUser.photoURL || 'https://via.placeholder.com/32'}
                      alt={activeUser.displayName}
                      className="w-6 h-6 rounded-full mr-2"
                    />
                    <span className={`absolute bottom-0 right-1 w-2 h-2 rounded-full ${activeUser.status === 'online' ? 'bg-success' : 'bg-warning'}`}></span>
                  </div>
                  <span className="text-sm text-textPrimary truncate">
                    {activeUser.displayName}
                  </span>
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
      
      {/* Message input */}
      <div className="border-t border-divider p-3">
        {!isConnected ? (
          <div className="text-center text-textSecondary text-sm py-1">
            Disconnected. Reconnecting...
          </div>
        ) : !roomId ? (
          <div className="text-center text-textSecondary text-sm py-1">
            Select a room to start chatting
          </div>
        ) : !isUserInRoom ? (
          <div className="text-center text-textSecondary text-sm py-1">
            Join the room to send messages
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="flex">
            <input
              type="text"
              value={messageText}
              onChange={(e) => setMessageText(e.target.value)}
              placeholder="Type a message..."
              className="flex-1 px-3 py-2 border border-divider rounded-l-md bg-background text-textPrimary focus:outline-none focus:ring-1 focus:ring-primary"
              disabled={isSending}
            />
            <button
              type="submit"
              className="bg-primary text-primaryContrast px-4 py-2 rounded-r-md hover:bg-primaryDark transition-colors duration-200 disabled:opacity-50"
              disabled={!messageText.trim() || isSending}
            >
              {isSending ? (
                <span className="inline-block w-4 h-4 border-2 border-primaryContrast border-t-transparent rounded-full animate-spin"></span>
              ) : (
                'Send'
              )}
            </button>
          </form>
        )}
      </div>
    </div>
  );
};

CollaborationChat.propTypes = {
  roomId: PropTypes.string,
  showHeader: PropTypes.bool,
  showUserList: PropTypes.bool,
  showTimestamp: PropTypes.bool,
  autoScroll: PropTypes.bool,
  maxHeight: PropTypes.number,
  className: PropTypes.string,
  style: PropTypes.object
};

export default CollaborationChat;
