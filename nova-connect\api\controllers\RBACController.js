/**
 * RBAC Controller
 *
 * This controller handles role-based access control operations.
 */

const RBACService = require('../services/RBACService');
const AuditService = require('../services/AuditService');
const logger = require('../../config/logger');
const { ValidationError, NotFoundError, AuthorizationError } = require('../utils/errors');

// Initialize services
const rbacService = new RBACService();
const auditService = new AuditService();

/**
 * Get all roles
 */
const getAllRoles = async (req, res, next) => {
  try {
    const roles = await rbacService.getAllRoles();
    res.json(roles);
  } catch (error) {
    logger.error('Error getting all roles', { error: error.message });
    next(error);
  }
};

/**
 * Get role by ID
 */
const getRoleById = async (req, res, next) => {
  try {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Role ID is required'
      });
    }

    const role = await rbacService.getRoleById(id);
    res.json(role);
  } catch (error) {
    logger.error('Error getting role by ID', { error: error.message });
    next(error);
  }
};

/**
 * Create a new role
 */
const createRole = async (req, res, next) => {
  try {
    // Add user ID to request body
    const roleData = {
      ...req.body,
      createdBy: req.user.id
    };

    const role = await rbacService.createRole(roleData);
    res.status(201).json(role);

    // Log audit event
    auditService.logEvent({
      userId: req.user.id,
      action: 'CREATE',
      resourceType: 'role',
      resourceId: role._id.toString(),
      details: {
        name: role.name,
        permissions: role.permissions
      },
      ip: req.ip,
      userAgent: req.headers['user-agent']
    });
  } catch (error) {
    logger.error('Error creating role', { error: error.message });
    next(error);
  }
};

/**
 * Update a role
 */
const updateRole = async (req, res, next) => {
  try {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Role ID is required'
      });
    }

    // Add user ID to request body
    const roleData = {
      ...req.body,
      updatedBy: req.user.id
    };

    const role = await rbacService.updateRole(id, roleData);
    res.json(role);

    // Log audit event
    auditService.logEvent({
      userId: req.user.id,
      action: 'UPDATE',
      resourceType: 'role',
      resourceId: role._id.toString(),
      details: {
        name: role.name,
        permissions: role.permissions
      },
      ip: req.ip,
      userAgent: req.headers['user-agent']
    });
  } catch (error) {
    logger.error('Error updating role', { error: error.message });
    next(error);
  }
};

/**
 * Delete a role
 */
const deleteRole = async (req, res, next) => {
  try {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Role ID is required'
      });
    }

    // Get role details before deletion for audit log
    const role = await rbacService.getRoleById(id);

    const result = await rbacService.deleteRole(id);
    res.json(result);

    // Log audit event
    auditService.logEvent({
      userId: req.user.id,
      action: 'DELETE',
      resourceType: 'role',
      resourceId: id,
      details: {
        name: role.name
      },
      ip: req.ip,
      userAgent: req.headers['user-agent']
    });
  } catch (error) {
    logger.error('Error deleting role', { error: error.message });
    next(error);
  }
};

/**
 * Get all permissions
 */
const getAllPermissions = async (req, res, next) => {
  try {
    const permissions = await rbacService.getAllPermissions();
    res.json(permissions);
  } catch (error) {
    logger.error('Error getting all permissions', { error: error.message });
    next(error);
  }
};

/**
 * Get permission by ID
 */
const getPermissionById = async (req, res, next) => {
  try {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Permission ID is required'
      });
    }

    const permission = await rbacService.getPermissionById(id);
    res.json(permission);
  } catch (error) {
    logger.error('Error getting permission by ID', { error: error.message });
    next(error);
  }
};

/**
 * Create a new permission
 */
const createPermission = async (req, res, next) => {
  try {
    // Add user ID to request body
    const permissionData = {
      ...req.body,
      createdBy: req.user.id
    };

    const permission = await rbacService.createPermission(permissionData);
    res.status(201).json(permission);

    // Log audit event
    auditService.logEvent({
      userId: req.user.id,
      action: 'CREATE',
      resourceType: 'permission',
      resourceId: permission._id.toString(),
      details: {
        name: permission.name,
        resource: permission.resource,
        action: permission.action
      },
      ip: req.ip,
      userAgent: req.headers['user-agent']
    });
  } catch (error) {
    logger.error('Error creating permission', { error: error.message });
    next(error);
  }
};

/**
 * Update a permission
 */
const updatePermission = async (req, res, next) => {
  try {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Permission ID is required'
      });
    }

    const permission = await rbacService.updatePermission(id, req.body);
    res.json(permission);
  } catch (error) {
    logger.error('Error updating permission', { error: error.message });
    next(error);
  }
};

/**
 * Delete a permission
 */
const deletePermission = async (req, res, next) => {
  try {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Permission ID is required'
      });
    }

    const result = await rbacService.deletePermission(id);
    res.json(result);
  } catch (error) {
    logger.error('Error deleting permission', { error: error.message });
    next(error);
  }
};

/**
 * Get user roles
 */
const getUserRoles = async (req, res, next) => {
  try {
    const { userId } = req.params;

    if (!userId) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'User ID is required'
      });
    }

    const roles = await rbacService.getUserRoles(userId);
    res.json(roles);
  } catch (error) {
    logger.error('Error getting user roles', { error: error.message });
    next(error);
  }
};

/**
 * Assign role to user
 */
const assignRoleToUser = async (req, res, next) => {
  try {
    const { userId } = req.params;
    const { roleId, scope, scopeId } = req.body;

    if (!userId) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'User ID is required'
      });
    }

    if (!roleId) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Role ID is required'
      });
    }

    // Get role details for audit log
    const role = await rbacService.getRoleById(roleId);

    const result = await rbacService.assignRoleToUser(userId, roleId, scope, scopeId);
    res.json(result);

    // Log audit event
    auditService.logEvent({
      userId: req.user.id,
      action: 'ASSIGN_ROLE',
      resourceType: 'user_role',
      resourceId: `${userId}:${roleId}`,
      details: {
        userId,
        roleId,
        roleName: role.name,
        scope: scope || 'global',
        scopeId: scopeId || null
      },
      ip: req.ip,
      userAgent: req.headers['user-agent']
    });
  } catch (error) {
    logger.error('Error assigning role to user', { error: error.message });
    next(error);
  }
};

/**
 * Remove role from user
 */
const removeRoleFromUser = async (req, res, next) => {
  try {
    const { userId, roleId } = req.params;
    const { scope, scopeId } = req.body;

    if (!userId) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'User ID is required'
      });
    }

    if (!roleId) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Role ID is required'
      });
    }

    // Get role details for audit log
    let roleName = roleId;
    try {
      const role = await rbacService.getRoleById(roleId);
      roleName = role.name;
    } catch (error) {
      // Role might have been deleted, continue with removal
      logger.warn(`Role ${roleId} not found for audit log`);
    }

    const result = await rbacService.removeRoleFromUser(userId, roleId, scope, scopeId);
    res.json(result);

    // Log audit event
    auditService.logEvent({
      userId: req.user.id,
      action: 'REMOVE_ROLE',
      resourceType: 'user_role',
      resourceId: `${userId}:${roleId}`,
      details: {
        userId,
        roleId,
        roleName,
        scope: scope || 'global',
        scopeId: scopeId || null
      },
      ip: req.ip,
      userAgent: req.headers['user-agent']
    });
  } catch (error) {
    logger.error('Error removing role from user', { error: error.message });
    next(error);
  }
};

/**
 * Check if user has permission
 */
const checkUserPermission = async (req, res, next) => {
  try {
    const { userId, permissionId } = req.params;

    if (!userId) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'User ID is required'
      });
    }

    if (!permissionId) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Permission ID is required'
      });
    }

    const hasPermission = await rbacService.hasPermission(userId, permissionId);
    res.json({ hasPermission });
  } catch (error) {
    logger.error('Error checking user permission', { error: error.message });
    next(error);
  }
};

/**
 * Get user permissions
 */
const getUserPermissions = async (req, res, next) => {
  try {
    const { userId } = req.params;

    if (!userId) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'User ID is required'
      });
    }

    const permissions = await rbacService.getUserPermissions(userId);
    res.json(permissions);
  } catch (error) {
    logger.error('Error getting user permissions', { error: error.message });
    next(error);
  }
};

/**
 * Get role permissions including inherited permissions
 */
const getRolePermissions = async (req, res, next) => {
  try {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Role ID is required'
      });
    }

    const permissions = await rbacService.getRolePermissions(id);
    res.json(permissions);
  } catch (error) {
    logger.error('Error getting role permissions', { error: error.message });
    next(error);
  }
};

/**
 * Get all roles with their inherited permissions
 */
const getAllRolesWithPermissions = async (req, res, next) => {
  try {
    // Get all roles
    const roles = await rbacService.getAllRoles();

    // Get permissions for each role
    const rolesWithPermissions = await Promise.all(
      roles.map(async (role) => {
        const permissions = await rbacService.getRolePermissions(role._id);
        return {
          ...role.toObject(),
          effectivePermissions: permissions
        };
      })
    );

    res.json(rolesWithPermissions);
  } catch (error) {
    logger.error('Error getting all roles with permissions', { error: error.message });
    next(error);
  }
};

/**
 * Get role inheritance tree
 */
const getRoleInheritanceTree = async (req, res, next) => {
  try {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Role ID is required'
      });
    }

    const inheritanceTree = await rbacService.getRoleInheritanceTree(id);
    res.json(inheritanceTree);
  } catch (error) {
    logger.error('Error getting role inheritance tree', { error: error.message });
    next(error);
  }
};

module.exports = {
  getAllRoles,
  getRoleById,
  createRole,
  updateRole,
  deleteRole,
  getAllPermissions,
  getPermissionById,
  createPermission,
  updatePermission,
  deletePermission,
  getUserRoles,
  assignRoleToUser,
  removeRoleFromUser,
  checkUserPermission,
  getUserPermissions,
  getRolePermissions,
  getAllRolesWithPermissions,
  getRoleInheritanceTree
};
