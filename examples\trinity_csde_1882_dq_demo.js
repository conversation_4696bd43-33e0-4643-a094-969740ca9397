/**
 * Trinity CSDE 18/82 with Data Quality Framework Demo
 * 
 * This script demonstrates the integrated system that combines:
 * 1. Trinity CSDE with 18/82 principle
 * 2. UUFT Data Quality Framework
 * 3. NovaVision visualization
 */

const { TrinityCSDE1882DQEngine } = require('../src/csde');
const TrinityCSDE1882Dashboard = require('../src/novavision/trinity_csde_dashboard');
const fs = require('fs');
const path = require('path');

// Create a logger
const logger = {
  info: (message) => console.log(`[INFO] ${message}`),
  error: (message) => console.log(`[ERROR] ${message}`),
  warn: (message) => console.log(`[WARN] ${message}`),
  debug: (message) => console.log(`[DEBUG] ${message}`)
};

// Sample data
const governanceData = {
  complianceScore: 0.85,
  auditFrequency: 4,
  timestamp: new Date().toISOString(),
  source: 'official',
  confidence: 0.9,
  policies: [
    { id: 'POL-001', name: 'Access Control Policy', effectiveness: 0.9 },
    { id: 'POL-002', name: 'Data Protection Policy', effectiveness: 0.8 },
    { id: 'POL-003', name: 'Incident Response Policy', effectiveness: 0.85 },
    { id: 'POL-004', name: 'Risk Management Policy', effectiveness: 0.75 },
    { id: 'POL-005', name: 'Governance Policy', effectiveness: 0.9 }
  ]
};

const detectionData = {
  detectionCapability: 0.75,
  threatSeverity: 0.8,
  threatConfidence: 0.7,
  baselineSignals: 0.65,
  timestamp: new Date().toISOString(),
  source: 'sensor',
  confidence: 0.85,
  threats: {
    malware: 0.9,
    phishing: 0.8,
    ddos: 0.7,
    insider: 0.6
  }
};

const responseData = {
  baseResponseTime: 50,
  threatSurface: 175,
  systemRadius: 150,
  reactionTime: 0.8,
  mitigationSurface: 0.7,
  timestamp: new Date().toISOString(),
  source: 'verified',
  confidence: 0.8,
  threats: {
    malware: 0.9,
    phishing: 0.8,
    ddos: 0.7,
    insider: 0.6
  }
};

// Create results directory
const RESULTS_DIR = path.join(__dirname, '../trinity_csde_results');
if (!fs.existsSync(RESULTS_DIR)) {
  fs.mkdirSync(RESULTS_DIR, { recursive: true });
}

/**
 * Demo Trinity CSDE 18/82 with Data Quality
 */
function demoTrinityCSDE1882DQ() {
  logger.info('Demo: Trinity CSDE 18/82 with Data Quality Framework');
  
  try {
    // Initialize Trinity CSDE Engine with Data Quality Framework
    const csdeEngine = new TrinityCSDE1882DQEngine({
      enableMetrics: true,
      enableCaching: true
    });
    
    // Calculate Trinity CSDE
    const result = csdeEngine.calculateTrinityCSDE(
      governanceData,
      detectionData,
      responseData
    );
    
    // Print results
    logger.info('Trinity CSDE 18/82 with Data Quality Results:');
    logger.info(`Father Component (πG): ${result.fatherComponent.result.toFixed(4)}`);
    logger.info(`Son Component (ϕD): ${result.sonComponent.result.toFixed(4)}`);
    logger.info(`Spirit Component ((ℏ + c^-1)R): ${result.spiritComponent.result.toFixed(4)}`);
    logger.info(`Trinity CSDE Value: ${result.csdeTrinity.toFixed(4)}`);
    
    logger.info('Data Quality Metrics:');
    logger.info(`Overall Quality: ${result.dataQuality.overall.toFixed(4)}`);
    logger.info(`Governance Quality: ${result.dataQuality.governance.toFixed(4)}`);
    logger.info(`Detection Quality: ${result.dataQuality.detection.toFixed(4)}`);
    logger.info(`Response Quality: ${result.dataQuality.response.toFixed(4)}`);
    
    // Save results to file
    const resultFile = path.join(RESULTS_DIR, `trinity_csde_1882_dq_result_${new Date().toISOString().replace(/:/g, '-')}.json`);
    fs.writeFileSync(resultFile, JSON.stringify(result, null, 2));
    logger.info(`Results saved to ${resultFile}`);
    
    return result;
  } catch (error) {
    logger.error(`Error in Trinity CSDE 18/82 with Data Quality demo: ${error.message}`);
    throw error;
  }
}

/**
 * Demo NovaVision Dashboard
 * @param {Object} result - Trinity CSDE result
 */
function demoNovaVisionDashboard(result) {
  logger.info('Demo: NovaVision Dashboard');
  
  try {
    // Initialize NovaVision Dashboard
    const dashboard = new TrinityCSDE1882Dashboard();
    
    // Process data for visualization
    const visualizationData = dashboard.processData(
      governanceData,
      detectionData,
      responseData
    );
    
    // Render Trinity visualization
    const trinityVisualization = dashboard.renderTrinityVisualization(
      visualizationData.visualizations.trinity
    );
    
    // Render Data Quality visualization
    const dataQualityVisualization = dashboard.renderDataQualityVisualization(
      visualizationData.visualizations.dataQuality
    );
    
    // Save visualizations to files
    const trinityFile = path.join(RESULTS_DIR, `trinity_visualization_${new Date().toISOString().replace(/:/g, '-')}.html`);
    fs.writeFileSync(trinityFile, trinityVisualization);
    logger.info(`Trinity visualization saved to ${trinityFile}`);
    
    const dataQualityFile = path.join(RESULTS_DIR, `data_quality_visualization_${new Date().toISOString().replace(/:/g, '-')}.html`);
    fs.writeFileSync(dataQualityFile, dataQualityVisualization);
    logger.info(`Data Quality visualization saved to ${dataQualityFile}`);
    
    // Create a combined dashboard
    const combinedDashboard = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Trinity CSDE 18/82 with Data Quality Dashboard</title>
        <style>
          body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
          }
          .dashboard {
            display: flex;
            flex-direction: column;
            gap: 20px;
          }
          .trinity-visualization, .data-quality-visualization {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          }
          h2 {
            margin-top: 0;
            color: #333;
          }
          .trinity-components, .quality-components {
            display: flex;
            gap: 20px;
            margin-top: 20px;
          }
          .trinity-component, .quality-component {
            flex: 1;
            padding: 15px;
            border-radius: 8px;
          }
          .formula {
            font-family: monospace;
            margin: 10px 0;
          }
          .score, .contribution, .quality {
            margin: 5px 0;
            font-weight: bold;
          }
          .details, .metrics {
            margin-top: 10px;
          }
          .detail, .metric {
            display: flex;
            justify-content: space-between;
            margin: 5px 0;
          }
          .total-score, .overall-quality, .evolution {
            margin-top: 20px;
            padding: 10px;
            background-color: #f0f0f0;
            border-radius: 4px;
          }
        </style>
      </head>
      <body>
        <div class="dashboard">
          ${trinityVisualization}
          ${dataQualityVisualization}
        </div>
      </body>
      </html>
    `;
    
    const dashboardFile = path.join(RESULTS_DIR, `trinity_csde_dashboard_${new Date().toISOString().replace(/:/g, '-')}.html`);
    fs.writeFileSync(dashboardFile, combinedDashboard);
    logger.info(`Combined dashboard saved to ${dashboardFile}`);
    
  } catch (error) {
    logger.error(`Error in NovaVision Dashboard demo: ${error.message}`);
    throw error;
  }
}

/**
 * Main function
 */
function main() {
  logger.info('Trinity CSDE 18/82 with Data Quality Framework and NovaVision Demo');
  
  try {
    // Demo 1: Trinity CSDE 18/82 with Data Quality
    const result = demoTrinityCSDE1882DQ();
    
    // Demo 2: NovaVision Dashboard
    demoNovaVisionDashboard(result);
    
    logger.info('Demo completed successfully');
  } catch (error) {
    logger.error(`Error running demo: ${error.message}`);
  }
}

// Run the demo
main();
