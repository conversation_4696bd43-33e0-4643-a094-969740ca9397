import React, { useEffect } from 'react';
import { motion, useAnimation } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { fadeInUp, fadeInLeft, fadeInRight } from '../utils/animations';

/**
 * Animated section component that animates when it comes into view
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Section content
 * @param {string} props.className - Additional CSS classes
 * @param {string} props.animation - Animation type ('fadeInUp', 'fadeInLeft', or 'fadeInRight')
 * @param {number} props.delay - Animation delay in milliseconds
 * @param {string} props.id - Section ID for navigation
 */
const AnimatedSection = ({ 
  children, 
  className = '', 
  animation = 'fadeInUp', 
  delay = 0,
  id
}) => {
  const controls = useAnimation();
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1
  });

  // Start animation when section comes into view
  useEffect(() => {
    if (inView) {
      controls.start('animate');
    }
  }, [controls, inView]);

  // Get animation properties based on animation type
  const getAnimationProps = () => {
    switch (animation) {
      case 'fadeInLeft':
        return fadeInLeft(500, delay);
      case 'fadeInRight':
        return fadeInRight(500, delay);
      case 'fadeInUp':
      default:
        return fadeInUp(500, delay);
    }
  };

  const { initial, animate, transition } = getAnimationProps();

  return (
    <motion.div
      id={id}
      ref={ref}
      className={className}
      initial={initial}
      animate={controls}
      variants={{ animate }}
      transition={transition}
    >
      {children}
    </motion.div>
  );
};

export default AnimatedSection;
