/**
 * Data Processing Activity Model
 * 
 * Represents a data processing activity within the organization.
 * Data processing activities describe how personal data is processed,
 * including the purpose, legal basis, data categories, and more.
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const dataProcessingActivitySchema = new Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    required: true,
    trim: true
  },
  purpose: {
    type: String,
    required: true,
    trim: true
  },
  dataCategories: [{
    type: String,
    required: true,
    trim: true
  }],
  dataSubjects: [{
    type: String,
    required: true,
    trim: true
  }],
  legalBasis: {
    type: String,
    required: true,
    enum: ['consent', 'contract', 'legal_obligation', 'vital_interests', 'public_interest', 'legitimate_interests'],
    trim: true
  },
  retentionPeriod: {
    type: String,
    required: true,
    trim: true
  },
  status: {
    type: String,
    required: true,
    enum: ['active', 'inactive', 'archived'],
    default: 'active'
  },
  processors: [{
    name: {
      type: String,
      required: true,
      trim: true
    },
    role: {
      type: String,
      required: true,
      trim: true
    },
    location: {
      type: String,
      required: true,
      trim: true
    },
    dataTransferred: {
      type: Boolean,
      default: false
    }
  }],
  securityMeasures: [{
    type: String,
    trim: true
  }],
  risks: [{
    description: {
      type: String,
      required: true,
      trim: true
    },
    likelihood: {
      type: String,
      required: true,
      enum: ['low', 'medium', 'high'],
      default: 'medium'
    },
    impact: {
      type: String,
      required: true,
      enum: ['low', 'medium', 'high'],
      default: 'medium'
    },
    mitigationMeasures: {
      type: String,
      trim: true
    }
  }],
  responsibleParty: {
    name: {
      type: String,
      required: true,
      trim: true
    },
    email: {
      type: String,
      required: true,
      trim: true
    },
    role: {
      type: String,
      required: true,
      trim: true
    }
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Create a text index for searching
dataProcessingActivitySchema.index({
  name: 'text',
  description: 'text',
  purpose: 'text'
});

// Pre-save hook to update the updatedAt field
dataProcessingActivitySchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

const DataProcessingActivity = mongoose.model('DataProcessingActivity', dataProcessingActivitySchema);

module.exports = DataProcessingActivity;
