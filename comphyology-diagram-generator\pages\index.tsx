import React, { useState } from 'react';
import Head from 'next/head';
import styled from 'styled-components';
import DiagramTemplate from '../components/DiagramTemplate';
import FiniteUniverseParadigm from '../components/diagrams/FiniteUniverseParadigm';
import ThreeBodyProblem from '../components/diagrams/ThreeBodyProblem';
import UUFTEquationFlow from '../components/diagrams/UUFTEquationFlow';
import TrinityEquationVisualization from '../components/diagrams/TrinityEquationVisualization';
import MetaFieldSchema from '../components/diagrams/MetaFieldSchema';
import PatternTranslation from '../components/diagrams/PatternTranslation';
import NovaFuseComponents from '../components/diagrams/NovaFuseComponents';
import AlignmentArchitecture from '../components/diagrams/AlignmentArchitecture';
import DataProcessingPipeline from '../components/diagrams/DataProcessingPipeline';
import IncidentResponse from '../components/diagrams/IncidentResponse';
import HealthcareImplementation from '../components/diagrams/HealthcareImplementation';
import DashboardVisualization from '../components/diagrams/DashboardVisualization';
import HardwareArchitecture from '../components/diagrams/HardwareArchitecture';
import { toPng } from 'html-to-image';

const Container = styled.div`
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
`;

const Title = styled.h1`
  font-size: 2rem;
  margin-bottom: 1rem;
  color: #333;
`;

const DiagramSelector = styled.div`
  margin-bottom: 2rem;
`;

const Select = styled.select`
  padding: 0.5rem;
  font-size: 1rem;
  border-radius: 4px;
  border: 1px solid #ccc;
  width: 300px;
`;

const DiagramContainer = styled.div`
  margin-bottom: 2rem;
  border: 1px solid #eee;
  padding: 1rem;
  border-radius: 8px;
`;

const ButtonContainer = styled.div`
  margin-top: 1rem;
  display: flex;
  gap: 1rem;
`;

const Button = styled.button`
  padding: 0.5rem 1rem;
  background-color: #0070f3;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;

  &:hover {
    background-color: #0060df;
  }
`;

// Diagram components
const diagramComponents = [
  {
    id: 'high-level-architecture',
    name: '1. High-Level System Architecture',
    component: (
      <DiagramTemplate
        elements={[
          {
            id: 'novafuse',
            top: 50,
            left: 300,
            width: 200,
            text: 'NovaFuse Platform',
            number: '1',
            bold: true,
            fontSize: '18px',
            backgroundColor: '#e6f7ff'
          },
          {
            id: 'input',
            top: 150,
            left: 100,
            width: 150,
            text: 'Input Data Sources',
            number: '2',
            fontSize: '14px'
          },
          {
            id: 'output',
            top: 150,
            left: 550,
            width: 150,
            text: 'Output Actions',
            number: '3',
            fontSize: '14px'
          },
          {
            id: 'comphyology',
            top: 250,
            left: 300,
            width: 200,
            text: 'Comphyology (Ψᶜ) Framework',
            number: '4',
            bold: true,
            fontSize: '16px',
            backgroundColor: '#fff0f6'
          }
        ]}
        connections={[
          {
            start: { x: 175, y: 150 },
            end: { x: 300, y: 100 },
            type: 'arrow'
          },
          {
            start: { x: 500, y: 100 },
            end: { x: 550, y: 150 },
            type: 'arrow'
          },
          {
            start: { x: 400, y: 120 },
            end: { x: 400, y: 250 },
            type: 'arrow'
          }
        ]}
        width="800px"
        height="350px"
      />
    )
  },
  {
    id: 'finite-universe',
    name: '2. Finite Universe Paradigm',
    component: <FiniteUniverseParadigm />
  },
  {
    id: 'three-body-problem',
    name: '3. Three-Body Problem Reframing',
    component: <ThreeBodyProblem />
  },
  {
    id: 'uuft-equation-flow',
    name: '4. UUFT Equation Flow',
    component: <UUFTEquationFlow />
  },
  {
    id: 'trinity-equation',
    name: '5. Trinity Equation Visualization',
    component: <TrinityEquationVisualization />
  },
  {
    id: 'meta-field-schema',
    name: '6. Meta-Field Schema',
    component: <MetaFieldSchema />
  },
  {
    id: 'pattern-translation',
    name: '8. Pattern Translation Process',
    component: <PatternTranslation />
  },
  {
    id: 'novafuse-components',
    name: '9. 13 NovaFuse Components',
    component: <NovaFuseComponents />
  },
  {
    id: 'alignment-architecture',
    name: '10. 3-6-9-12-13 Alignment Architecture',
    component: <AlignmentArchitecture />
  },
  {
    id: 'data-processing-pipeline',
    name: '11. Cross-Module Data Processing Pipeline',
    component: <DataProcessingPipeline />
  },
  {
    id: 'incident-response',
    name: '12. Cyber-Safety Incident Response Workflow',
    component: <IncidentResponse />
  },
  {
    id: 'healthcare-implementation',
    name: '13. Healthcare Implementation Diagram',
    component: <HealthcareImplementation />
  },
  {
    id: 'dashboard-visualization',
    name: '14. Dashboard and Visualization Examples',
    component: <DashboardVisualization />
  },
  {
    id: 'hardware-architecture',
    name: '15. Hardware Architecture Diagram',
    component: <HardwareArchitecture />
  }
];

export default function Home() {
  const [selectedDiagram, setSelectedDiagram] = useState(diagramComponents[0].id);

  const handleDiagramChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedDiagram(e.target.value);
  };

  const currentDiagram = diagramComponents.find(d => d.id === selectedDiagram) || diagramComponents[0];

  const downloadDiagram = () => {
    const element = document.getElementById('diagram-container');
    if (!element) return;

    toPng(element)
      .then((dataUrl) => {
        const link = document.createElement('a');
        link.download = `${currentDiagram.name.replace(/\s+/g, '-').toLowerCase()}.png`;
        link.href = dataUrl;
        link.click();
      })
      .catch((error) => {
        console.error('Error generating image:', error);
      });
  };

  return (
    <Container>
      <Head>
        <title>Comphyology Patent Diagram Generator</title>
        <meta name="description" content="Generate diagrams for Comphyology Patent" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <Title>Comphyology Patent Diagram Generator</Title>

      <DiagramSelector>
        <Select value={selectedDiagram} onChange={handleDiagramChange}>
          {diagramComponents.map(diagram => (
            <option key={diagram.id} value={diagram.id}>
              {diagram.name}
            </option>
          ))}
        </Select>
      </DiagramSelector>

      <DiagramContainer id="diagram-container">
        {currentDiagram.component}
      </DiagramContainer>

      <ButtonContainer>
        <Button onClick={downloadDiagram}>
          Download Diagram
        </Button>
      </ButtonContainer>
    </Container>
  );
}
