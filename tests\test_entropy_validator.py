"""
Tests for the quantum-enhanced EntropyValidator
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timedelta
import numpy as np

from src.governance.entropy_validator import (
    EntropyValidator,
    DecisionType,
    Decision,
    RiskLevel,
    ComplianceLevel
)
from src.governance.quantum_utils import QuantumEntropyProfile

# Test data
SAMPLE_TASK = {
    "id": "task_123",
    "name": "Quantum Feature Implementation",
    "type": "feature_dev",
    "budget": 5000.0,
    "deadline_hours": 48,
    "vendor_id": "vendor_quantum_1",
    "role_id": "role_dev_1",
    "vendor_score": 8.5,
    "role_spend_limit": 10000.0,
    "role_q_threshold": 7.0,
    "compliance_level": "enterprise",
    "signature": "NOVA-QUANTUM-SIGNATURE-123"
}

@pytest.fixture
def mock_supabase():
    """Create a mock Supabase client"""
    mock = AsyncMock()
    
    # Mock vendor query
    mock.table.return_value.select.return_value.eq.return_value.single.return_value.execute.return_value.data = {
        'current_q_score': 8.5
    }
    
    # Mock decision insert
    mock.table.return_value.insert.return_value.execute.return_value.data = [{"id": "dec_123"}]
    
    # Mock RPC calls
    mock.rpc.return_value.execute.return_value.data = {
        'valid': True,
        'threshold': 25.0
    }
    
    return mock

@pytest.fixture
def validator(mock_supabase):
    """Create an EntropyValidator with mocked dependencies"""
    validator = EntropyValidator(supabase=mock_supabase)
    
    # Patch quantum_engine to avoid real quantum computations in tests
    with patch('src.governance.quantum_utils.quantum_engine') as mock_qe:
        mock_qe.calculate_quantum_entropy.return_value = QuantumEntropyProfile(
            base_entropy=100.0,
            quantum_factor=0.95,
            confidence_interval=(0.9, 1.0),
            last_updated=datetime.utcnow()
        )
        mock_qe.get_quantum_factor.return_value = 0.95
        yield validator

@pytest.mark.asyncio
async def test_calculate_entropy(validator):
    """Test quantum entropy calculation"""
    entropy, profile = await validator.calculate_entropy(
        SAMPLE_TASK, 
        vendor_qscore=8.5
    )
    
    assert isinstance(entropy, float)
    assert entropy > 0
    assert isinstance(profile, QuantumEntropyProfile)
    assert 0.8 <= profile.quantum_factor <= 1.2  # Should be within expected range

@pytest.mark.asyncio
async def test_evaluate_task_auto_approval(validator, mock_supabase):
    """Test task evaluation with auto-approval"""
    task = SAMPLE_TASK.copy()
    task['budget'] = 8000  # Below spend limit
    
    decision = await validator.evaluate_task(task)
    
    assert decision.decision == DecisionType.AUTO_APPROVED
    assert decision.entropy_value > 0
    assert decision.threshold > 0
    assert decision.risk_level == RiskLevel.HIGH.value  # feature_dev is HIGH risk

@pytest.mark.asyncio
async def test_evaluate_task_escalation(validator, mock_supabase):
    """Test task evaluation with escalation"""
    task = SAMPLE_TASK.copy()
    task['budget'] = 50000  # Very high budget
    task['deadline_hours'] = 2  # Very short deadline
    
    decision = await validator.evaluate_task(task)
    
    assert decision.decision == DecisionType.ESCALATE
    assert decision.entropy_value > decision.threshold * 1.2  # Should be well above threshold

@pytest.mark.asyncio
async def test_evaluate_task_requires_approval(validator, mock_supabase):
    """Test task evaluation requiring approval"""
    task = SAMPLE_TASK.copy()
    task['budget'] = 15000  # Above spend limit
    
    decision = await validator.evaluate_task(task)
    
    assert decision.decision == DecisionType.REQUIRES_APPROVAL
    assert decision.entropy_value > 0

@pytest.mark.asyncio
async def test_invalid_novadna_signature(validator, mock_supabase):
    """Test handling of invalid NovaDNA signatures"""
    mock_supabase.rpc.return_value.execute.return_value.data = {'valid': False}
    
    task = SAMPLE_TASK.copy()
    task['signature'] = 'INVALID-SIGNATURE'
    
    decision = await validator.evaluate_task(task)
    assert decision.decision == DecisionType.REJECTED

@pytest.mark.asyncio
async def test_decision_serialization():
    """Test that Decision objects can be serialized"""
    decision = Decision(
        task_id="task_123",
        entropy_value=100.0,
        threshold=80.0,
        q_factor=0.95,
        decision=DecisionType.AUTO_APPROVED,
        entropy_signature="test_sig",
        risk_level=RiskLevel.HIGH.value,
        compliance_level=ComplianceLevel.ENTERPRISE.value,
        quantum_confidence=(0.9, 1.0),
        is_recurring=False
    )
    
    # Convert to dict and back
    dict_repr = decision.to_dict()
    assert isinstance(dict_repr, dict)
    assert dict_repr["task_id"] == "task_123"
    assert dict_repr["entropy_value"] == 100.0
    assert dict_repr["decision"] == "Auto-Approved"
    assert dict_repr["risk_level"] == "high"
    assert dict_repr["compliance_level"] == "enterprise"
    assert dict_repr["quantum_confidence"] == [0.9, 1.0]
    assert "timestamp" in dict_repr

@pytest.mark.asyncio
async def test_recurring_pattern_detection(validator):
    """Test detection of recurring decision patterns"""
    task1 = SAMPLE_TASK.copy()
    task1['id'] = 'task_001'
    
    task2 = SAMPLE_TASK.copy()
    task2['id'] = 'task_002'  # Same signature as task1
    
    # First evaluation
    decision1 = await validator.evaluate_task(task1)
    assert not decision1.is_recurring
    
    # Second evaluation with same signature
    decision2 = await validator.evaluate_task(task2)
    assert decision2.is_recurring

@pytest.mark.asyncio
async def test_quantum_entropy_edge_cases(validator):
    """Test entropy calculation with edge cases"""
    # Test with minimum values
    task = SAMPLE_TASK.copy()
    task.update({
        'budget': 1.0,
        'deadline_hours': 1,
        'vendor_score': 10.0
    })
    
    entropy, _ = await validator.calculate_entropy(task, vendor_qscore=10.0)
    assert entropy > 0
    
    # Test with zero deadline (should use 1 to avoid division by zero)
    task['deadline_hours'] = 0
    entropy, _ = await validator.calculate_entropy(task, vendor_qscore=10.0)
    assert entropy > 0

@pytest.mark.asyncio
async def test_risk_level_assignment(validator, mock_supabase):
    """Test that risk levels are assigned correctly"""
    test_cases = [
        ('critical_bugfix', 9.5, RiskLevel.CRITICAL),
        ('security_update', 9.0, RiskLevel.CRITICAL),
        ('feature_dev', 8.0, RiskLevel.HIGH),
        ('data_processing', 7.0, RiskLevel.HIGH),
        ('testing', 6.0, RiskLevel.STANDARD),
        ('documentation', 5.0, RiskLevel.LOW)
    ]
    
    for task_type, qscore, expected_risk in test_cases:
        task = SAMPLE_TASK.copy()
        task['type'] = task_type
        
        decision = await validator.evaluate_task(task)
        assert decision.risk_level == expected_risk.value

# Run tests if executed directly
if __name__ == "__main__":
    pytest.main(["-v", "test_entropy_validator.py"])
