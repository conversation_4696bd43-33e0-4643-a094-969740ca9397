<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NovaFuse Integration Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
        }
        button {
            background-color: #3498db;
            border: none;
            color: white;
            padding: 10px 20px;
            margin: 10px 10px 10px 0;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #2980b9;
        }
        pre {
            background-color: #f8f8f8;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>NovaFuse Integration Test</h1>
        <p>This page tests the integration with Google Cloud services.</p>
        
        <div>
            <button onclick="testEndpoint('/health')">Test UI Health</button>
            <button onclick="testEndpoint('/scc/health')">Test SCC Integration</button>
            <button onclick="testEndpoint('/iam/health')">Test IAM Integration</button>
            <button onclick="testEndpoint('/bigquery/health')">Test BigQuery Integration</button>
        </div>
        
        <h2>Results:</h2>
        <pre id="results">Click a button to test an endpoint.</pre>
    </div>
    
    <script>
        function testEndpoint(endpoint) {
            const resultsElement = document.getElementById('results');
            resultsElement.textContent = `Testing ${endpoint}...`;
            
            fetch(endpoint)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    return response.text();
                })
                .then(text => {
                    try {
                        // Try to parse as JSON
                        const data = JSON.parse(text);
                        resultsElement.textContent = `Success! Response:\n${JSON.stringify(data, null, 2)}`;
                    } catch (e) {
                        // If not JSON, show as text
                        resultsElement.textContent = `Response (not JSON):\n${text}`;
                    }
                })
                .catch(error => {
                    resultsElement.textContent = `Error: ${error.message}`;
                });
        }
    </script>
</body>
</html>
