/**
 * Compliance Automation API - Entry Point
 * 
 * This is the entry point for the Compliance Automation API.
 */

const express = require('express');
const mongoose = require('mongoose');
const routes = require('./routes');
const logger = require('../../../utils/logger');

// Create Express app
const app = express();

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Routes
app.use('/api/compliance/automation', routes);

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'ok',
    service: 'compliance-automation-api',
    timestamp: new Date().toISOString()
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  logger.error(`Error: ${err.message}`, { service: 'compliance-automation-api', error: err });
  res.status(err.status || 500).json({
    success: false,
    error: err.name || 'Internal Server Error',
    message: err.message || 'Something went wrong'
  });
});

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/novagrc', {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    logger.info('Connected to MongoDB', { service: 'compliance-automation-api' });
  } catch (error) {
    logger.error(`Error connecting to MongoDB: ${error.message}`, { service: 'compliance-automation-api', error });
    process.exit(1);
  }
};

// Start server
const PORT = process.env.PORT || 3007;
const startServer = async () => {
  await connectDB();
  app.listen(PORT, () => {
    logger.info(`Compliance Automation API running on port ${PORT}`, { service: 'compliance-automation-api' });
  });
};

// Start server if this file is run directly
if (require.main === module) {
  startServer();
}

module.exports = app;
