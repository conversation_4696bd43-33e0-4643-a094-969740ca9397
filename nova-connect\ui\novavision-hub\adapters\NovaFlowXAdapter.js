/**
 * NovaFlowX Adapter for NovaVision
 * 
 * This adapter connects NovaFlowX with NovaVision, allowing NovaVision to render UI schemas
 * based on NovaFlowX data and functionality for workflow automation.
 */

/**
 * NovaFlowX Adapter class
 */
class NovaFlowXAdapter {
  /**
   * Constructor
   * 
   * @param {Object} options - Adapter options
   * @param {Object} options.novaFlowX - NovaFlowX instance
   * @param {Object} options.novaVision - NovaVision instance
   * @param {boolean} [options.enableLogging=false] - Whether to enable logging
   * @param {Array} [options.subscribeTopics=[]] - Topics to subscribe to
   */
  constructor(options = {}) {
    this.options = {
      enableLogging: false,
      subscribeTopics: [],
      ...options
    };
    
    this.novaFlowX = options.novaFlowX;
    this.novaVision = options.novaVision;
    this.logger = options.logger || console;
    
    if (!this.novaFlowX) {
      throw new Error('NovaFlowX instance is required');
    }
    
    if (!this.novaVision) {
      throw new Error('NovaVision instance is required');
    }
    
    this.logger.info('NovaFlowX Adapter initialized');
  }
  
  /**
   * Initialize the adapter
   * 
   * @returns {Promise} - Promise that resolves when initialization is complete
   */
  async initialize() {
    if (this.options.enableLogging) {
      this.logger.info('Initializing NovaFlowX Adapter...');
    }
    
    try {
      // Subscribe to NovaFlowX events
      if (this.options.subscribeTopics.length > 0) {
        await this._subscribeToEvents();
      }
      
      if (this.options.enableLogging) {
        this.logger.info('NovaFlowX Adapter initialized successfully');
      }
      
      return true;
    } catch (error) {
      this.logger.error('Error initializing NovaFlowX Adapter', error);
      throw error;
    }
  }
  
  /**
   * Subscribe to NovaFlowX events
   * 
   * @private
   * @returns {Promise} - Promise that resolves when subscription is complete
   */
  async _subscribeToEvents() {
    if (this.options.enableLogging) {
      this.logger.info('Subscribing to NovaFlowX events...');
    }
    
    try {
      // Subscribe to events
      for (const topic of this.options.subscribeTopics) {
        if (topic.startsWith('novaFlowX.')) {
          const eventName = topic.split('.')[1];
          
          // Subscribe to event
          this.novaFlowX.on(eventName, (data) => {
            if (this.options.enableLogging) {
              this.logger.info(`NovaFlowX event: ${eventName}`, data);
            }
            
            // Handle event
            this._handleEvent(eventName, data);
          });
          
          if (this.options.enableLogging) {
            this.logger.info(`Subscribed to NovaFlowX event: ${eventName}`);
          }
        }
      }
      
      return true;
    } catch (error) {
      this.logger.error('Error subscribing to NovaFlowX events', error);
      throw error;
    }
  }
  
  /**
   * Handle NovaFlowX event
   * 
   * @private
   * @param {string} eventName - Event name
   * @param {Object} data - Event data
   */
  _handleEvent(eventName, data) {
    // Handle event based on event name
    switch (eventName) {
      case 'workflowCreated':
      case 'workflowUpdated':
      case 'workflowDeleted':
        // Update workflow list UI
        this._updateWorkflowListUI();
        break;
      
      case 'workflowExecuted':
        // Update workflow execution UI
        this._updateWorkflowExecutionUI(data);
        break;
      
      case 'taskCompleted':
        // Update task UI
        this._updateTaskUI(data);
        break;
      
      case 'workflowCompleted':
        // Update workflow completion UI
        this._updateWorkflowCompletionUI(data);
        break;
      
      default:
        // Unknown event
        if (this.options.enableLogging) {
          this.logger.warn(`Unknown NovaFlowX event: ${eventName}`);
        }
        break;
    }
  }
  
  /**
   * Update workflow list UI
   * 
   * @private
   */
  async _updateWorkflowListUI() {
    try {
      // Get workflow list schema
      const schema = await this.getUISchema('workflows');
      
      // Notify NovaVision of schema update
      this.novaVision.updateSchema('novaFlowX.workflows', schema);
    } catch (error) {
      this.logger.error('Error updating workflow list UI', error);
    }
  }
  
  /**
   * Update workflow execution UI
   * 
   * @private
   * @param {Object} data - Workflow execution data
   */
  async _updateWorkflowExecutionUI(data) {
    try {
      // Get workflow execution schema
      const schema = await this.getUISchema('workflowExecution', { executionId: data.executionId });
      
      // Notify NovaVision of schema update
      this.novaVision.updateSchema('novaFlowX.workflowExecution', schema);
    } catch (error) {
      this.logger.error('Error updating workflow execution UI', error);
    }
  }
  
  /**
   * Update task UI
   * 
   * @private
   * @param {Object} data - Task data
   */
  async _updateTaskUI(data) {
    try {
      // Get task schema
      const schema = await this.getUISchema('tasks');
      
      // Notify NovaVision of schema update
      this.novaVision.updateSchema('novaFlowX.tasks', schema);
    } catch (error) {
      this.logger.error('Error updating task UI', error);
    }
  }
  
  /**
   * Update workflow completion UI
   * 
   * @private
   * @param {Object} data - Workflow completion data
   */
  async _updateWorkflowCompletionUI(data) {
    try {
      // Get workflow completion schema
      const schema = await this.getUISchema('workflowCompletion', { executionId: data.executionId });
      
      // Notify NovaVision of schema update
      this.novaVision.updateSchema('novaFlowX.workflowCompletion', schema);
    } catch (error) {
      this.logger.error('Error updating workflow completion UI', error);
    }
  }
  
  /**
   * Get UI schema for NovaFlowX
   * 
   * @param {string} schemaType - Schema type
   * @param {Object} options - Schema options
   * @returns {Promise<Object>} - UI schema
   */
  async getUISchema(schemaType, options = {}) {
    if (this.options.enableLogging) {
      this.logger.info(`Getting UI schema for NovaFlowX.${schemaType}...`);
    }
    
    try {
      // Get schema based on schema type
      switch (schemaType) {
        case 'workflows':
          return await this._getWorkflowsSchema(options);
        
        case 'workflowDesigner':
          return await this._getWorkflowDesignerSchema(options);
        
        case 'workflowExecution':
          return await this._getWorkflowExecutionSchema(options);
        
        case 'tasks':
          return await this._getTasksSchema(options);
        
        case 'workflowCompletion':
          return await this._getWorkflowCompletionSchema(options);
        
        case 'dashboard':
          return await this.getDashboardSchema(options);
        
        default:
          throw new Error(`Unknown schema type: ${schemaType}`);
      }
    } catch (error) {
      this.logger.error(`Error getting UI schema for NovaFlowX.${schemaType}`, error);
      throw error;
    }
  }
  
  /**
   * Get workflows schema
   * 
   * @private
   * @param {Object} options - Schema options
   * @returns {Promise<Object>} - Workflows schema
   */
  async _getWorkflowsSchema(options = {}) {
    try {
      // Get workflows from NovaFlowX
      const workflows = await this.novaFlowX.getWorkflows({
        limit: options.limit || 50,
        offset: options.offset || 0,
        category: options.category,
        status: options.status
      });
      
      // Create workflows schema
      return {
        type: 'table',
        title: 'Workflows',
        columns: [
          { field: 'id', header: 'ID' },
          { field: 'name', header: 'Name' },
          { field: 'category', header: 'Category' },
          { field: 'version', header: 'Version' },
          { field: 'status', header: 'Status' },
          { field: 'lastExecuted', header: 'Last Executed' },
          { field: 'actions', header: 'Actions' }
        ],
        data: workflows.map(workflow => ({
          id: workflow.id,
          name: workflow.name,
          category: workflow.category,
          version: workflow.version,
          status: workflow.status,
          lastExecuted: workflow.lastExecuted,
          actions: {
            type: 'buttonGroup',
            buttons: [
              {
                type: 'button',
                text: 'View',
                variant: 'primary',
                size: 'sm',
                onClick: `novaFlowX.viewWorkflow:${workflow.id}`
              },
              {
                type: 'button',
                text: 'Edit',
                variant: 'secondary',
                size: 'sm',
                onClick: `novaFlowX.editWorkflow:${workflow.id}`
              },
              {
                type: 'button',
                text: 'Execute',
                variant: 'success',
                size: 'sm',
                onClick: `novaFlowX.executeWorkflow:${workflow.id}`
              },
              {
                type: 'button',
                text: 'Delete',
                variant: 'danger',
                size: 'sm',
                onClick: `novaFlowX.deleteWorkflow:${workflow.id}`
              }
            ]
          }
        })),
        actions: [
          {
            type: 'button',
            text: 'Create Workflow',
            variant: 'primary',
            onClick: 'novaFlowX.createWorkflow'
          },
          {
            type: 'button',
            text: 'Import Workflow',
            variant: 'secondary',
            onClick: 'novaFlowX.importWorkflow'
          }
        ],
        pagination: {
          total: workflows.total,
          limit: workflows.limit,
          offset: workflows.offset,
          onPageChange: 'novaFlowX.changeWorkflowsPage'
        }
      };
    } catch (error) {
      this.logger.error('Error getting workflows schema', error);
      throw error;
    }
  }
  
  /**
   * Get workflow designer schema
   * 
   * @private
   * @param {Object} options - Schema options
   * @returns {Promise<Object>} - Workflow designer schema
   */
  async _getWorkflowDesignerSchema(options = {}) {
    try {
      // Get workflow from NovaFlowX if workflowId is provided
      const workflow = options.workflowId
        ? await this.novaFlowX.getWorkflow(options.workflowId)
        : null;
      
      // Create workflow designer schema
      return {
        type: 'workflowDesigner',
        title: workflow ? `Edit Workflow: ${workflow.name}` : 'Create Workflow',
        workflow: workflow ? {
          id: workflow.id,
          name: workflow.name,
          description: workflow.description,
          category: workflow.category,
          nodes: workflow.nodes,
          edges: workflow.edges,
          properties: workflow.properties
        } : {
          name: '',
          description: '',
          category: '',
          nodes: [],
          edges: [],
          properties: {}
        },
        nodeTypes: [
          { type: 'start', label: 'Start', color: '#28a745' },
          { type: 'end', label: 'End', color: '#dc3545' },
          { type: 'task', label: 'Task', color: '#007bff' },
          { type: 'decision', label: 'Decision', color: '#ffc107' },
          { type: 'subprocess', label: 'Subprocess', color: '#17a2b8' },
          { type: 'event', label: 'Event', color: '#6f42c1' }
        ],
        actions: [
          {
            type: 'button',
            text: 'Save',
            variant: 'primary',
            onClick: 'novaFlowX.saveWorkflow'
          },
          {
            type: 'button',
            text: 'Cancel',
            variant: 'secondary',
            onClick: 'novaFlowX.cancelWorkflow'
          },
          {
            type: 'button',
            text: 'Validate',
            variant: 'info',
            onClick: 'novaFlowX.validateWorkflow'
          }
        ]
      };
    } catch (error) {
      this.logger.error('Error getting workflow designer schema', error);
      throw error;
    }
  }
  
  /**
   * Get dashboard schema
   * 
   * @param {Object} options - Schema options
   * @returns {Promise<Object>} - Dashboard schema
   */
  async getDashboardSchema(options = {}) {
    try {
      // Get workflow stats from NovaFlowX
      const stats = await this.novaFlowX.getWorkflowStats();
      
      // Create dashboard schema
      return {
        type: 'card',
        title: 'NovaFlowX Dashboard',
        content: {
          type: 'grid',
          columns: 2,
          rows: 2,
          areas: [
            ['workflowStats', 'executionDistribution'],
            ['recentExecutions', 'recentExecutions']
          ],
          components: [
            {
              type: 'card',
              gridArea: 'workflowStats',
              header: 'Workflow Statistics',
              content: {
                type: 'stats',
                stats: [
                  { label: 'Total Workflows', value: stats.totalWorkflows },
                  { label: 'Active Workflows', value: stats.activeWorkflows },
                  { label: 'Executions Today', value: stats.executionsToday },
                  { label: 'Success Rate', value: `${stats.successRate}%` }
                ]
              }
            },
            {
              type: 'card',
              gridArea: 'executionDistribution',
              header: 'Execution Distribution',
              content: {
                type: 'chart',
                chartType: 'pie',
                data: {
                  labels: Object.keys(stats.executionDistribution),
                  datasets: [
                    {
                      data: Object.values(stats.executionDistribution),
                      backgroundColor: [
                        '#28a745',
                        '#dc3545',
                        '#ffc107',
                        '#17a2b8'
                      ]
                    }
                  ]
                }
              }
            },
            {
              type: 'card',
              gridArea: 'recentExecutions',
              header: 'Recent Executions',
              content: {
                type: 'table',
                columns: [
                  { field: 'workflowName', header: 'Workflow' },
                  { field: 'startTime', header: 'Start Time' },
                  { field: 'duration', header: 'Duration' },
                  { field: 'status', header: 'Status' },
                  { field: 'initiatedBy', header: 'Initiated By' }
                ],
                data: stats.recentExecutions
              }
            }
          ]
        }
      };
    } catch (error) {
      this.logger.error('Error getting dashboard schema', error);
      throw error;
    }
  }
  
  /**
   * Handle action from NovaVision
   * 
   * @param {string} action - Action name
   * @param {Object} data - Action data
   * @returns {Promise<Object>} - Action result
   */
  async handleAction(action, data) {
    if (this.options.enableLogging) {
      this.logger.info(`Handling NovaFlowX action: ${action}...`, data);
    }
    
    try {
      // Handle action based on action name
      switch (action) {
        case 'viewWorkflow':
          return await this.novaFlowX.viewWorkflow(data.workflowId);
        
        case 'editWorkflow':
          return await this.novaFlowX.editWorkflow(data.workflowId);
        
        case 'executeWorkflow':
          return await this.novaFlowX.executeWorkflow(data.workflowId, data.inputs);
        
        case 'deleteWorkflow':
          return await this.novaFlowX.deleteWorkflow(data.workflowId);
        
        case 'createWorkflow':
          return await this.novaFlowX.createWorkflow();
        
        case 'importWorkflow':
          return await this.novaFlowX.importWorkflow(data);
        
        case 'saveWorkflow':
          return await this.novaFlowX.saveWorkflow(data.workflow);
        
        case 'cancelWorkflow':
          return await this.novaFlowX.cancelWorkflow();
        
        case 'validateWorkflow':
          return await this.novaFlowX.validateWorkflow(data.workflow);
        
        case 'changeWorkflowsPage':
          return await this.novaFlowX.getWorkflows({
            limit: data.limit,
            offset: data.offset
          });
        
        default:
          throw new Error(`Unknown action: ${action}`);
      }
    } catch (error) {
      this.logger.error(`Error handling NovaFlowX action: ${action}`, error);
      throw error;
    }
  }
}

export default NovaFlowXAdapter;
