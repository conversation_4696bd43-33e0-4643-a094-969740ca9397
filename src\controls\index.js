/**
 * Controls Module
 * 
 * This module exports all the components of the controls system.
 */

const ControlPanel = require('./control-panel');
const TensorControls = require('./tensor-controls');
const VisualizationControls = require('./visualization-controls');
const AnalyticsControls = require('./analytics-controls');

/**
 * Create a control panel
 * @param {Object} options - Configuration options
 * @returns {ControlPanel} - Control panel instance
 */
function createControlPanel(options = {}) {
  return new ControlPanel(options);
}

/**
 * Create tensor controls
 * @param {Object} options - Configuration options
 * @returns {TensorControls} - Tensor controls instance
 */
function createTensorControls(options = {}) {
  return new TensorControls(options);
}

/**
 * Create visualization controls
 * @param {Object} options - Configuration options
 * @returns {VisualizationControls} - Visualization controls instance
 */
function createVisualizationControls(options = {}) {
  return new VisualizationControls(options);
}

/**
 * Create analytics controls
 * @param {Object} options - Configuration options
 * @returns {AnalyticsControls} - Analytics controls instance
 */
function createAnalyticsControls(options = {}) {
  return new AnalyticsControls(options);
}

/**
 * Create a unified control system
 * @param {Object} options - Configuration options
 * @returns {Object} - Unified control system
 */
function createUnifiedControlSystem(options = {}) {
  // Create control panel
  const controlPanel = createControlPanel({
    enableLogging: options.enableLogging,
    wsUrl: options.wsUrl,
    autoConnect: options.autoConnect
  });
  
  // Create controls
  const controls = {
    tensor: createTensorControls({
      enableLogging: options.enableLogging,
      wsUrl: options.wsUrl,
      controlPanel
    }),
    
    visualization: createVisualizationControls({
      enableLogging: options.enableLogging,
      wsUrl: options.wsUrl,
      controlPanel
    }),
    
    analytics: createAnalyticsControls({
      enableLogging: options.enableLogging,
      wsUrl: options.wsUrl,
      controlPanel
    })
  };
  
  return {
    controlPanel,
    controls,
    
    /**
     * Connect to the WebSocket server
     * @returns {Promise<void>} - Promise that resolves when connected
     */
    async connect() {
      // Connect control panel
      await controlPanel.connect();
      
      // Connect controls
      await controls.tensor.connect();
      await controls.visualization.connect();
      await controls.analytics.connect();
      
      if (options.enableLogging) {
        console.log('UnifiedControlSystem: Connected');
      }
    },
    
    /**
     * Disconnect from the WebSocket server
     * @returns {Promise<void>} - Promise that resolves when disconnected
     */
    async disconnect() {
      // Disconnect controls
      await controls.tensor.disconnect();
      await controls.visualization.disconnect();
      await controls.analytics.disconnect();
      
      // Disconnect control panel
      await controlPanel.disconnect();
      
      if (options.enableLogging) {
        console.log('UnifiedControlSystem: Disconnected');
      }
    },
    
    /**
     * Register a tensor
     * @param {string} id - Tensor ID
     * @param {Object} tensor - Tensor data
     * @param {string} [domain='universal'] - Domain
     * @returns {Promise<Object>} - Promise that resolves with the registered tensor
     */
    async registerTensor(id, tensor, domain = 'universal') {
      return controls.tensor.registerTensor(id, tensor, domain);
    },
    
    /**
     * Heal a tensor
     * @param {string} [id] - Tensor ID (uses selected tensor if not provided)
     * @returns {Promise<Object>} - Promise that resolves with the healed tensor
     */
    async healTensor(id) {
      return controls.tensor.healTensor(id);
    },
    
    /**
     * Damage a tensor
     * @param {string} [id] - Tensor ID (uses selected tensor if not provided)
     * @param {number} [damageLevel] - Damage level (uses control value if not provided)
     * @returns {Promise<Object>} - Promise that resolves with the damaged tensor
     */
    async damageTensor(id, damageLevel) {
      return controls.tensor.damageTensor(id, damageLevel);
    },
    
    /**
     * Create a visualization
     * @param {string} visualizationType - Visualization type
     * @param {Object} data - Visualization data
     * @param {Object} [options] - Visualization options
     * @returns {Promise<Object>} - Promise that resolves with the created visualization
     */
    async createVisualization(visualizationType, data, options = {}) {
      return controls.visualization.createVisualization(visualizationType, data, options);
    },
    
    /**
     * Update a visualization
     * @param {string} [id] - Visualization ID (uses selected visualization if not provided)
     * @param {Object} data - Updated visualization data
     * @returns {Promise<Object>} - Promise that resolves with the updated visualization
     */
    async updateVisualization(id, data) {
      return controls.visualization.updateVisualization(id, data);
    },
    
    /**
     * Delete a visualization
     * @param {string} [id] - Visualization ID (uses selected visualization if not provided)
     * @returns {Promise<Object>} - Promise that resolves with the result
     */
    async deleteVisualization(id) {
      return controls.visualization.deleteVisualization(id);
    },
    
    /**
     * Execute a query
     * @param {string} query - Query to execute
     * @param {Object} [params] - Query parameters
     * @returns {Promise<Object>} - Promise that resolves with the query result
     */
    async executeQuery(query, params = {}) {
      return controls.analytics.executeQuery(query, params);
    },
    
    /**
     * Refresh metrics
     * @returns {Promise<Object>} - Promise that resolves with the metrics
     */
    async refreshMetrics() {
      return controls.analytics.refreshMetrics();
    },
    
    /**
     * Refresh a dashboard
     * @param {string} [id] - Dashboard ID (uses selected dashboard if not provided)
     * @returns {Promise<Object>} - Promise that resolves with the dashboard
     */
    async refreshDashboard(id) {
      return controls.analytics.refreshDashboard(id);
    }
  };
}

module.exports = {
  ControlPanel,
  TensorControls,
  VisualizationControls,
  AnalyticsControls,
  createControlPanel,
  createTensorControls,
  createVisualizationControls,
  createAnalyticsControls,
  createUnifiedControlSystem
};
