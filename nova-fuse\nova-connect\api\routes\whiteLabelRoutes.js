/**
 * White Label Routes
 */

const express = require('express');
const router = express.Router();
const WhiteLabelController = require('../controllers/WhiteLabelController');
const { authenticate, hasPermission, optionalAuth } = require('../middleware/authMiddleware');

// Public routes for white label package
router.get('/package/:domain', (req, res, next) => {
  WhiteLabelController.getWhiteLabelPackage(req, res, next);
});

// All other routes require authentication
router.use(authenticate);

// Routes that require system settings permission
router.use(hasPermission('system:settings'));

// Get white label settings for an organization
router.get('/organization/:organizationId', (req, res, next) => {
  WhiteLabelController.getWhiteLabelSettings(req, res, next);
});

// Update white label settings for an organization
router.put('/organization/:organizationId', (req, res, next) => {
  WhiteLabelController.updateWhiteLabelSettings(req, res, next);
});

// Get custom domains for an organization
router.get('/organization/:organizationId/domains', (req, res, next) => {
  WhiteLabelController.getCustomDomains(req, res, next);
});

// Add custom domain for an organization
router.post('/organization/:organizationId/domains', (req, res, next) => {
  WhiteLabelController.addCustomDomain(req, res, next);
});

// Verify custom domain for an organization
router.post('/organization/:organizationId/domains/:domain/verify', (req, res, next) => {
  WhiteLabelController.verifyCustomDomain(req, res, next);
});

// Delete custom domain for an organization
router.delete('/organization/:organizationId/domains/:domain', (req, res, next) => {
  WhiteLabelController.deleteCustomDomain(req, res, next);
});

// Get domain verification instructions
router.get('/organization/:organizationId/domains/:domain/instructions', (req, res, next) => {
  WhiteLabelController.getDomainVerificationInstructions(req, res, next);
});

module.exports = router;
