# ALPHA Observer-Class Coherence Engine
# Nginx Configuration for Calibration System

events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    # Logging
    log_format alpha_format '$remote_addr - $remote_user [$time_local] '
                           '"$request" $status $body_bytes_sent '
                           '"$http_referer" "$http_user_agent" '
                           'alpha_component="$upstream_addr"';

    access_log /var/log/nginx/alpha_access.log alpha_format;
    error_log  /var/log/nginx/alpha_error.log warn;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

    # Rate limiting for ALPHA API
    limit_req_zone $binary_remote_addr zone=alpha_api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=alpha_dashboard:10m rate=5r/s;

    # Upstream servers
    upstream alpha_calibration {
        server alpha-calibration:3000;
        keepalive 32;
    }

    upstream alpha_dashboard {
        server alpha-dashboard:3000;
        keepalive 16;
    }

    # ALPHA Calibration Engine
    server {
        listen 80;
        server_name alpha.localhost;

        # Security headers
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Referrer-Policy strict-origin-when-cross-origin;
        add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';";

        # Rate limiting
        limit_req zone=alpha_api burst=20 nodelay;

        location / {
            proxy_pass http://alpha_calibration;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
            proxy_read_timeout 86400;
        }

        # Health check endpoint
        location /health {
            proxy_pass http://alpha_calibration/health;
            access_log off;
        }

        # Calibration status endpoint
        location /calibration/status {
            proxy_pass http://alpha_calibration/calibration/status;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
    }

    # ALPHA Dashboard
    server {
        listen 80;
        server_name dashboard.alpha.localhost;

        # Security headers
        add_header X-Frame-Options SAMEORIGIN;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";

        # Rate limiting
        limit_req zone=alpha_dashboard burst=10 nodelay;

        location / {
            proxy_pass http://alpha_dashboard;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
        }

        # WebSocket support for real-time dashboard updates
        location /ws {
            proxy_pass http://alpha_dashboard;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "Upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }

    # Default server block (catch-all)
    server {
        listen 80 default_server;
        server_name _;
        
        location / {
            return 444;
        }
    }
}
