/**
 * RBAC Test for NovaVision
 *
 * This example tests the RBAC implementation for NovaVision.
 */

const { NovaVisionRBAC, ROLES, PERMISSIONS } = require('../src/novavision/security/rbac');

// Create a new RBAC instance
const rbac = new NovaVisionRBAC();

// Example user IDs
const users = {
  ciso: 'user-123',
  admin: 'user-456',
  standardUser: 'user-789',
  partner: 'user-101'
};

// Assign roles to users
console.log('=== Assigning Roles to Users ===');
console.log(`Assigning CISO role to user ${users.ciso}: ${rbac.assignRole(users.ciso, 'CISO')}`);
console.log(`Assigning ADMIN role to user ${users.admin}: ${rbac.assignRole(users.admin, 'ADMIN')}`);
console.log(`Assigning USER role to user ${users.standardUser}: ${rbac.assignRole(users.standardUser, 'USER')}`);
console.log(`Assigning PARTNER role to user ${users.partner}: ${rbac.assignRole(users.partner, 'PARTNER')}`);

// Check user roles
console.log('\n=== User Roles ===');
console.log(`CISO user roles: ${rbac.getUserRoles(users.ciso)}`);
console.log(`ADMIN user roles: ${rbac.getUserRoles(users.admin)}`);
console.log(`Standard user roles: ${rbac.getUserRoles(users.standardUser)}`);
console.log(`Partner user roles: ${rbac.getUserRoles(users.partner)}`);

// Check user permissions
console.log('\n=== User Permissions ===');
console.log(`CISO user permissions: ${rbac.getUserPermissions(users.ciso)}`);
console.log(`ADMIN user permissions: ${rbac.getUserPermissions(users.admin)}`);
console.log(`Standard user permissions: ${rbac.getUserPermissions(users.standardUser)}`);
console.log(`Partner user permissions: ${rbac.getUserPermissions(users.partner)}`);

// Check specific permissions
console.log('\n=== Permission Checks ===');
console.log('1. VIEW_TRINITY_CSDE Permission:');
console.log(`- CISO: ${rbac.hasPermission(users.ciso, 'view:trinity_csde')}`);
console.log(`- ADMIN: ${rbac.hasPermission(users.admin, 'view:trinity_csde')}`);
console.log(`- Standard User: ${rbac.hasPermission(users.standardUser, 'view:trinity_csde')}`);
console.log(`- Partner: ${rbac.hasPermission(users.partner, 'view:trinity_csde')}`);

console.log('\n2. VIEW_OPTIMIZATION Permission:');
console.log(`- CISO: ${rbac.hasPermission(users.ciso, 'view:optimization')}`);
console.log(`- ADMIN: ${rbac.hasPermission(users.admin, 'view:optimization')}`);
console.log(`- Standard User: ${rbac.hasPermission(users.standardUser, 'view:optimization')}`);
console.log(`- Partner: ${rbac.hasPermission(users.partner, 'view:optimization')}`);

console.log('\n3. VIEW_SUMMARY Permission:');
console.log(`- CISO: ${rbac.hasPermission(users.ciso, 'view:summary')}`);
console.log(`- ADMIN: ${rbac.hasPermission(users.admin, 'view:summary')}`);
console.log(`- Standard User: ${rbac.hasPermission(users.standardUser, 'view:summary')}`);
console.log(`- Partner: ${rbac.hasPermission(users.partner, 'view:summary')}`);

console.log('\n4. MANAGE_USERS Permission:');
console.log(`- CISO: ${rbac.hasPermission(users.ciso, 'manage:users')}`);
console.log(`- ADMIN: ${rbac.hasPermission(users.admin, 'manage:users')}`);
console.log(`- Standard User: ${rbac.hasPermission(users.standardUser, 'manage:users')}`);
console.log(`- Partner: ${rbac.hasPermission(users.partner, 'manage:users')}`);

// Remove a role
console.log('\n=== Removing Roles ===');
console.log(`Removing CISO role from user ${users.ciso}: ${rbac.removeRole(users.ciso, 'CISO')}`);
console.log(`CISO user roles after removal: ${rbac.getUserRoles(users.ciso)}`);
console.log(`CISO can view Trinity CSDE after role removal: ${rbac.hasPermission(users.ciso, 'view:trinity_csde')}`);

// Add the role back
console.log('\n=== Adding Roles Back ===');
console.log(`Adding CISO role back to user ${users.ciso}: ${rbac.assignRole(users.ciso, 'CISO')}`);
console.log(`CISO user roles after adding back: ${rbac.getUserRoles(users.ciso)}`);
console.log(`CISO can view Trinity CSDE after role addition: ${rbac.hasPermission(users.ciso, 'view:trinity_csde')}`);

console.log('\n=== End of RBAC Test ===');
