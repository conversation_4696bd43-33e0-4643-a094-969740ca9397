# Run GCP Simulation Environment
# This script sets up and runs the GCP simulation environment for NovaFuse testing

Write-Host "Starting GCP Simulation Environment" -ForegroundColor Yellow

# Check if Docker is running
try {
    docker info | Out-Null
} catch {
    Write-Host "Docker is not running. Please start Docker and try again." -ForegroundColor Red
    exit 1
}

# Create necessary directories
New-Item -ItemType Directory -Force -Path output | Out-Null
New-Item -ItemType Directory -Force -Path gcp-simulators/iam | Out-Null
New-Item -ItemType Directory -Force -Path gcp-simulators/bigquery | Out-Null
New-Item -ItemType Directory -Force -Path novafuse-ui | Out-Null

# Create placeholder files for services that aren't fully implemented yet
if (-not (Test-Path "gcp-simulators/iam/Dockerfile")) {
    Write-Host "Creating placeholder for Cloud IAM simulator..." -ForegroundColor Yellow
    New-Item -ItemType Directory -Force -Path gcp-simulators/iam | Out-Null
    @"
FROM node:18-alpine
WORKDIR /app
RUN npm init -y && npm install express cors helmet morgan winston dotenv
COPY server.js .
EXPOSE 8082
CMD ["node", "server.js"]
"@ | Out-File -FilePath gcp-simulators/iam/Dockerfile -Encoding utf8

    @"
const express = require('express');
const app = express();
app.use(express.json());
app.get('/health', (req, res) => res.json({ status: 'ok' }));
app.post('/token', (req, res) => res.json({ access_token: 'simulation-token', expires_in: 3600 }));
app.get('/v1/projects/:projectId/roles', (req, res) => res.json({ roles: [{ name: 'roles/viewer' }, { name: 'roles/editor' }] }));
app.post('/v1/projects/:projectId:getIamPolicy', (req, res) => res.json({ bindings: [{ role: 'roles/viewer', members: ['user:<EMAIL>'] }] }));
app.listen(8082, () => console.log('Cloud IAM Simulator running on port 8082'));
"@ | Out-File -FilePath gcp-simulators/iam/server.js -Encoding utf8
}

if (-not (Test-Path "gcp-simulators/bigquery/Dockerfile")) {
    Write-Host "Creating placeholder for BigQuery simulator..." -ForegroundColor Yellow
    New-Item -ItemType Directory -Force -Path gcp-simulators/bigquery | Out-Null
    @"
FROM node:18-alpine
WORKDIR /app
RUN npm init -y && npm install express cors helmet morgan winston dotenv
COPY server.js .
EXPOSE 8083
CMD ["node", "server.js"]
"@ | Out-File -FilePath gcp-simulators/bigquery/Dockerfile -Encoding utf8

    @"
const express = require('express');
const app = express();
app.use(express.json());
app.get('/health', (req, res) => res.json({ status: 'ok' }));
app.post('/token', (req, res) => res.json({ access_token: 'simulation-token', expires_in: 3600 }));
app.post('/v2/projects/:projectId/queries', (req, res) => res.json({ jobComplete: true, rows: [{ f: [{ v: 'result1' }] }] }));
app.get('/v2/projects/:projectId/datasets/:datasetId', (req, res) => res.json({ id: req.params.datasetId, friendlyName: 'Test Dataset' }));
app.listen(8083, () => console.log('BigQuery Simulator running on port 8083'));
"@ | Out-File -FilePath gcp-simulators/bigquery/server.js -Encoding utf8
}

if (-not (Test-Path "novafuse-ui/Dockerfile")) {
    Write-Host "Creating placeholder for NovaFuse UI..." -ForegroundColor Yellow
    New-Item -ItemType Directory -Force -Path novafuse-ui | Out-Null
    @"
FROM node:18-alpine
WORKDIR /app
RUN npm init -y && npm install express cors helmet
COPY server.js .
EXPOSE 3000
CMD ["node", "server.js"]
"@ | Out-File -FilePath novafuse-ui/Dockerfile -Encoding utf8

    @"
const express = require('express');
const app = express();
app.use(express.json());
app.use(express.static('public'));
app.get('/health', (req, res) => res.json({ status: 'ok' }));
app.get('/', (req, res) => res.send('<h1>NovaFuse UI Simulation</h1><p>This is a placeholder for the NovaFuse UI.</p>'));
app.listen(3000, () => console.log('NovaFuse UI running on port 3000'));
"@ | Out-File -FilePath novafuse-ui/server.js -Encoding utf8
    New-Item -ItemType Directory -Force -Path novafuse-ui/public | Out-Null
}

# Build and start the Docker containers
Write-Host "Building and starting Docker containers..." -ForegroundColor Yellow
docker-compose up -d --build

# Wait for the containers to start
Write-Host "Waiting for containers to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# Check if the containers are running
$containersRunning = docker-compose ps | Select-String "Up"
if (-not $containersRunning) {
    Write-Host "Containers failed to start. Please check the logs." -ForegroundColor Red
    docker-compose logs
    exit 1
}

Write-Host "GCP Simulation Environment started successfully." -ForegroundColor Green
Write-Host "NovaFuse API: http://localhost:3001" -ForegroundColor Green
Write-Host "NovaConnect UAC: http://localhost:3002" -ForegroundColor Green
Write-Host "NovaFuse UI: http://localhost:3003" -ForegroundColor Green
Write-Host "API Gateway: http://localhost:3000" -ForegroundColor Green

# Display logs
Write-Host "Displaying container logs (press Ctrl+C to exit)..." -ForegroundColor Yellow
docker-compose logs -f
