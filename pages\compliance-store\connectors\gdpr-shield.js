import React from 'react';
import Link from 'next/link';
import PageWithSidebar from '../../../components/PageWithSidebar';

export default function GDPRShieldConnector() {
  // Define sidebar items for the connector detail page
  const sidebarItems = [
    { type: 'category', label: 'GDPR Shield', items: [
      { label: 'Overview', href: '#overview' },
      { label: 'Features', href: '#features' },
      { label: 'Pricing', href: '#pricing' },
      { label: 'Integration', href: '#integration' },
      { label: 'Support', href: '#support' }
    ]},
    { type: 'category', label: 'Navigation', items: [
      { label: 'Back to Browse', href: '/compliance-store/browse' },
      { label: 'Compliance Store Home', href: '/compliance-store' }
    ]}
  ];

  // SEO metadata
  const pageProps = {
    title: 'GDPR Shield - NovaFuse Compliance App Store',
    description: 'GDPR Shield: Comprehensive GDPR compliance solution with automated data mapping, consent management, and breach notification.',
    keywords: 'GDPR compliance, data privacy, consent management, breach notification, GDPR Shield',
    canonical: 'https://novafuse.io/compliance-store/connectors/gdpr-shield',
    ogImage: '/images/connectors/gdpr-shield-og-image.png'
  };

  // Connector data (in a real app, this would come from an API)
  const connector = {
    id: 'gdpr-shield',
    name: 'GDPR Shield',
    vendor: 'Compliance Partners Inc.',
    description: 'Comprehensive GDPR compliance solution with automated data mapping, consent management, and breach notification.',
    longDescription: 'GDPR Shield provides a complete solution for organizations that need to comply with the European Union\'s General Data Protection Regulation (GDPR). Our connector automates the most challenging aspects of GDPR compliance, including data mapping, consent management, and breach notification processes.',
    category: 'data-privacy',
    framework: 'gdpr',
    rating: 4.5,
    reviewCount: 48,
    price: '$499/mo',
    features: [
      'Automated data mapping and inventory',
      'Consent management system',
      'Data subject request handling',
      'Breach notification workflow',
      'Risk assessment tools',
      'Compliance documentation generator',
      'Regular compliance updates',
      'Cross-border data transfer management'
    ],
    integrations: [
      'CRM systems',
      'Marketing platforms',
      'HR systems',
      'Cloud storage providers',
      'E-commerce platforms'
    ]
  };

  return (
    <PageWithSidebar title={pageProps.title} sidebarItems={sidebarItems} {...pageProps}>
      {/* Connector Header */}
      <div id="overview" className="bg-secondary rounded-lg p-8 mb-8">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <div>
            <div className="flex items-center mb-4">
              <div className="bg-blue-600 w-16 h-16 rounded-lg flex items-center justify-center text-white text-2xl font-bold mr-4">
                GDPR
              </div>
              <div>
                <h1 className="text-3xl font-bold">{connector.name}</h1>
                <p className="text-gray-400">by {connector.vendor}</p>
              </div>
            </div>

            <div className="flex flex-wrap gap-2 mb-4">
              <span className="bg-blue-600 text-xs px-2 py-1 rounded-full">{connector.framework.toUpperCase()}</span>
              <span className="bg-gray-700 text-xs px-2 py-1 rounded-full">{connector.category.replace('-', ' ')}</span>
            </div>
          </div>

          <div className="mt-4 md:mt-0">
            <div className="flex items-center mb-2">
              <div className="flex text-yellow-400 mr-2">
                {'★'.repeat(Math.floor(connector.rating))}
                {connector.rating % 1 !== 0 ? '½' : ''}
                {'☆'.repeat(5 - Math.ceil(connector.rating))}
              </div>
              <span>({connector.reviewCount} reviews)</span>
            </div>
            <div className="text-green-400 font-bold text-xl">{connector.price}</div>
          </div>
        </div>

        <p className="mt-4 text-lg">{connector.longDescription}</p>
      </div>

      {/* Coming Soon Notice */}
      <div className="bg-blue-900 bg-opacity-30 border border-blue-700 rounded-lg p-6 mb-8">
        <h2 className="text-xl font-bold mb-2">Coming Soon</h2>
        <p>
          This connector detail page is a preview. The full functionality, including one-click deployment,
          will be available when the Compliance App Store launches.
        </p>
      </div>

      {/* Features Section */}
      <div id="features" className="mb-8">
        <h2 className="text-2xl font-bold mb-4">Key Features</h2>
        <div className="bg-secondary rounded-lg p-6">
          <ul className="space-y-3">
            {connector.features.map((feature, index) => (
              <li key={index} className="flex items-start">
                <span className="text-green-400 mr-2">✓</span>
                <span>{feature}</span>
              </li>
            ))}
          </ul>
        </div>
      </div>

      {/* Pricing Section */}
      <div id="pricing" className="mb-8">
        <h2 className="text-2xl font-bold mb-4">Pricing</h2>
        <div className="bg-secondary rounded-lg p-6">
          <div className="text-center mb-4">
            <span className="text-3xl font-bold text-green-400">{connector.price}</span>
            <span className="text-gray-400 ml-2">per month</span>
          </div>
          <p className="text-center mb-4">
            Includes all features, unlimited users, and premium support.
          </p>
          <div className="text-center">
            <button className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded-lg" disabled>
              Coming Soon
            </button>
          </div>
        </div>
      </div>

      {/* Integration Section */}
      <div id="integration" className="mb-8">
        <h2 className="text-2xl font-bold mb-4">Integrations</h2>
        <div className="bg-secondary rounded-lg p-6">
          <p className="mb-4">GDPR Shield integrates seamlessly with:</p>
          <div className="flex flex-wrap gap-2">
            {connector.integrations.map((integration, index) => (
              <span key={index} className="bg-gray-700 text-sm px-3 py-1 rounded-full">
                {integration}
              </span>
            ))}
          </div>
        </div>
      </div>

      {/* Support Section */}
      <div id="support" className="mb-8">
        <h2 className="text-2xl font-bold mb-4">Support</h2>
        <div className="bg-secondary rounded-lg p-6">
          <p className="mb-4">
            GDPR Shield comes with comprehensive support to ensure your compliance journey is smooth and effective:
          </p>
          <ul className="space-y-3">
            <li className="flex items-start">
              <span className="text-green-400 mr-2">✓</span>
              <span>24/7 technical support</span>
            </li>
            <li className="flex items-start">
              <span className="text-green-400 mr-2">✓</span>
              <span>Implementation assistance</span>
            </li>
            <li className="flex items-start">
              <span className="text-green-400 mr-2">✓</span>
              <span>Regular compliance updates</span>
            </li>
            <li className="flex items-start">
              <span className="text-green-400 mr-2">✓</span>
              <span>Access to compliance experts</span>
            </li>
          </ul>
        </div>
      </div>

      {/* Call to Action */}
      <div className="bg-blue-900 rounded-lg p-8 text-center mb-12">
        <h2 className="text-2xl font-bold mb-4">Ready to simplify GDPR compliance?</h2>
        <p className="mb-6">Get early access to GDPR Shield and transform your data privacy program.</p>
        <Link href="/contact" className="inline-block bg-white text-blue-700 hover:bg-gray-100 font-bold py-2 px-6 rounded-lg">
          Contact Sales
        </Link>
      </div>

      {/* Confidentiality Notice */}
      <div className="border border-blue-800 bg-blue-900 bg-opacity-20 rounded-lg p-4">
        <div className="flex items-start">
          <div className="text-yellow-400 mr-3 mt-1">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
          </div>
          <div>
            <p className="text-sm">
              <strong>CONFIDENTIAL:</strong> The NovaFuse Compliance App Store is currently under IP protection review.
              All content is considered confidential and proprietary. Unauthorized access or sharing is prohibited.
            </p>
          </div>
        </div>
      </div>
    </PageWithSidebar>
  );
}
