# NovaFuse Visualizations

This directory contains visualization components for the NovaFuse UI. These components provide interactive 3D visualizations of tensor data using Three.js.

## Overview

The visualization system consists of the following components:

### Core Visualization Components
- **VisualizationRenderer**: Factory component that renders the appropriate visualization based on the type
- **TensorVisualization3D**: Renders tensors in 3D space
- **ResonanceSpectrogram**: Visualizes tensor data as a 3D spectrogram
- **PhaseSpaceVisualization**: Renders a 3D phase space visualization of tensor data
- **HarmonicPatternExplorer**: Visualizes harmonic patterns in tensor data

### Cyber-Safety Fusion Visualization Components
- **TriDomainTensorVisualization**: Shows how data flows between GRC, IT, and Cybersecurity domains
- **CyberSafetyHarmonyIndex**: Displays alignment and harmony between domains using the 3-6-9-12-13 pattern
- **RiskControlFusionMap**: Maps risks across domains to controls and shows coverage
- **CyberSafetyResonanceSpectrogram**: Shows 3D resonance patterns for domain interactions
- **UnifiedComplianceSecurityVisualizer**: Shows how compliance requirements map to security controls

## Visualization Types

### 3D Tensor Visualization

The `TensorVisualization3D` component renders tensors in 3D space. It supports:

- 1D tensors as lines with points
- 2D tensors as surfaces
- 3D tensors as volumes

#### Options

- `renderMode`: Quality of rendering ('low', 'medium', 'high')
- `showAxes`: Whether to show axes
- `showGrid`: Whether to show grid
- `rotationSpeed`: Speed of rotation
- `colorScheme`: Color scheme ('default', 'rainbow', 'heatmap', 'grayscale')

### Resonance Spectrogram

The `ResonanceSpectrogram` component visualizes tensor data as a 3D spectrogram. It shows:

- Frequency and time data as a surface
- Frequency lines
- Resonance peaks

#### Options

- `renderMode`: Quality of rendering ('low', 'medium', 'high')
- `showAxes`: Whether to show axes
- `showGrid`: Whether to show grid
- `rotationSpeed`: Speed of rotation
- `colorScheme`: Color scheme ('default', 'rainbow', 'heatmap', 'grayscale')

### Phase Space Visualization

The `PhaseSpaceVisualization` component renders a 3D phase space visualization of tensor data. It shows:

- Phase space points
- Phase space trajectory
- Phase space boundaries

#### Options

- `renderMode`: Quality of rendering ('low', 'medium', 'high')
- `showAxes`: Whether to show axes
- `showGrid`: Whether to show grid
- `rotationSpeed`: Speed of rotation
- `colorScheme`: Color scheme ('default', 'rainbow', 'heatmap', 'grayscale')
- `dimensions`: Number of dimensions (2 or 3)
- `trailLength`: Length of the trail

### Harmonic Pattern Explorer

The `HarmonicPatternExplorer` component visualizes harmonic patterns in tensor data. It shows:

- Central sphere
- Harmonic nodes
- Connections between nodes
- Resonance patterns

#### Options

- `renderMode`: Quality of rendering ('low', 'medium', 'high')
- `showAxes`: Whether to show axes
- `showGrid`: Whether to show grid
- `rotationSpeed`: Speed of rotation
- `colorScheme`: Color scheme ('default', 'rainbow', 'heatmap', 'grayscale')
- `harmonicThreshold`: Threshold for detecting harmonics
- `resonancePatterns`: Whether to show resonance patterns

## Usage

### Basic Usage

```jsx
import VisualizationRenderer from './components/visualizations/VisualizationRenderer';

function MyComponent() {
  return (
    <VisualizationRenderer
      visualizationType="3d_tensor_visualization"
      tensor={{
        values: [0.5, 0.6, 0.7, 0.8, 0.9],
        health: 0.95,
        entropyContainment: 0.02
      }}
      dimensions={[5, 1, 1]}
      options={{
        renderMode: 'high',
        showAxes: true,
        showGrid: true,
        rotationSpeed: 1,
        colorScheme: 'default'
      }}
      width="100%"
      height="500px"
    />
  );
}
```

### Using Specific Visualization Components

You can also use the specific visualization components directly:

```jsx
import TensorVisualization3D from './components/visualizations/TensorVisualization3D';

function MyComponent() {
  return (
    <TensorVisualization3D
      tensor={{
        values: [0.5, 0.6, 0.7, 0.8, 0.9],
        health: 0.95,
        entropyContainment: 0.02
      }}
      dimensions={[5, 1, 1]}
      options={{
        renderMode: 'high',
        showAxes: true,
        showGrid: true,
        rotationSpeed: 1,
        colorScheme: 'default'
      }}
      width="100%"
      height="500px"
    />
  );
}
```

## Visualization Demo

The `VisualizationDemo` page demonstrates all visualization types with configurable options and sample data. It allows you to:

- Select a visualization type
- Configure visualization options
- Generate different types of sample data
- Adjust data size and noise level

To access the demo, navigate to `/visualization-demo` in the application.

## Implementation Details

### Three.js Integration

The visualizations use Three.js for 3D rendering. Each visualization component:

1. Creates a Three.js scene, camera, and renderer
2. Sets up lighting and controls
3. Creates the visualization based on the tensor data
4. Handles animation and interaction
5. Cleans up resources when unmounted

### Performance Optimization

### Tri-Domain Tensor Visualization

The Tri-Domain Tensor Visualization shows how data flows between the three domains of Cyber-Safety:
- GRC (Governance, Risk, Compliance)
- IT (Information Technology)
- Cybersecurity

```jsx
<VisualizationRenderer
  visualizationType="tri_domain_tensor_visualization"
  tensor={{
    domainData: {
      grc: { values: [...], health: 0.8, entropyContainment: 0.03 },
      it: { values: [...], health: 0.8, entropyContainment: 0.03 },
      cybersecurity: { values: [...], health: 0.8, entropyContainment: 0.03 },
      connections: [
        { source: 'grc', target: 'it', strength: 0.7 },
        { source: 'it', target: 'cybersecurity', strength: 0.8 },
        { source: 'cybersecurity', target: 'grc', strength: 0.5 }
      ]
    }
  }}
  options={{
    renderMode: 'high',
    showAxes: true,
    showGrid: true,
    rotationSpeed: 1,
    showLabels: true,
    highlightFusionPoints: true,
    connectionScale: 1.0
  }}
  width="100%"
  height="500px"
/>
```

### Cyber-Safety Harmony Index

The Cyber-Safety Harmony Index shows the alignment and harmony between the three domains using a radar chart and harmony score calculated using the 3-6-9-12-13 pattern.

```jsx
<VisualizationRenderer
  visualizationType="cyber_safety_harmony_index"
  tensor={{
    domainData: {
      grc: { score: 0.7, metrics: { governance: 0.6, risk: 0.7, compliance: 0.8 } },
      it: { score: 0.8, metrics: { infrastructure: 0.8, applications: 0.7, data: 0.9 } },
      cybersecurity: { score: 0.6, metrics: { prevention: 0.5, detection: 0.6, response: 0.7 } }
    },
    harmonyHistory: [0.65, 0.68, 0.72] // Optional historical harmony scores for trend analysis
  }}
  options={{
    showTrend: true,
    showDetails: true,
    colorScheme: 'default',
    enableAnimation: true
  }}
  width="100%"
  height="500px"
/>
```

### Risk-Control Fusion Map

The Risk-Control Fusion Map shows a heat map of risk concentration across domains with control coverage overlay.

```jsx
<VisualizationRenderer
  visualizationType="risk_control_fusion_map"
  tensor={{
    riskData: {
      grc: { governance: 0.7, risk: 0.5, compliance: 0.8 },
      it: { infrastructure: 0.6, applications: 0.4, data: 0.7 },
      cybersecurity: { prevention: 0.8, detection: 0.5, response: 0.3 }
    },
    controlData: {
      grc: { governance: 0.8, risk: 0.6, compliance: 0.9 },
      it: { infrastructure: 0.7, applications: 0.5, data: 0.6 },
      cybersecurity: { prevention: 0.7, detection: 0.4, response: 0.2 }
    }
  }}
  options={{
    showLegend: true,
    showEfficiencyIndicators: true,
    highlightGaps: true,
    showTooltips: true
  }}
  width="100%"
  height="500px"
/>
```

### Cyber-Safety Resonance Spectrogram

The Cyber-Safety Resonance Spectrogram shows 3D resonance patterns for domain interactions and frequency analysis of cross-domain data flows.

```jsx
<VisualizationRenderer
  visualizationType="cyber_safety_resonance_spectrogram"
  tensor={{
    domainData: {
      grc: { values: [...], frequency: 0.3, amplitude: 0.7, phase: 0 },
      it: { values: [...], frequency: 0.6, amplitude: 0.8, phase: Math.PI / 3 },
      cybersecurity: { values: [...], frequency: 0.9, amplitude: 0.6, phase: Math.PI / 2 },
      crossDomainFlows: [
        { source: 'grc', target: 'it', strength: 0.7, frequency: 0.5 },
        { source: 'it', target: 'cybersecurity', strength: 0.8, frequency: 0.7 },
        { source: 'cybersecurity', target: 'grc', strength: 0.5, frequency: 0.4 }
      ]
    },
    predictionData: {
      timeHorizon: 10,
      dissonanceProbability: 0.2,
      criticalPoints: [
        { timeStep: 5, severity: 0.7, description: 'Potential dissonance between GRC and IT' }
      ]
    }
  }}
  options={{
    renderMode: 'high',
    showAxes: true,
    showGrid: true,
    rotationSpeed: 1,
    showPredictions: true,
    showCrossDomainFlows: true,
    showLabels: true,
    highlightDissonance: true
  }}
  width="100%"
  height="500px"
/>
```

### Unified Compliance-Security Visualizer

The Unified Compliance-Security Visualizer shows how compliance requirements directly map to security controls using a Sankey diagram.

```jsx
<VisualizationRenderer
  visualizationType="unified_compliance_security_visualizer"
  tensor={{
    complianceData: {
      requirements: [
        { id: 'req1', name: 'Data Protection', domain: 'grc', completeness: 0.8 },
        { id: 'req2', name: 'Access Control', domain: 'grc', completeness: 0.6 },
        { id: 'req3', name: 'Incident Response', domain: 'grc', completeness: 0.4 }
      ],
      controls: [
        { id: 'ctrl1', name: 'Encryption', domain: 'it', completeness: 0.9 },
        { id: 'ctrl2', name: 'Authentication', domain: 'it', completeness: 0.7 },
        { id: 'ctrl3', name: 'Monitoring', domain: 'cybersecurity', completeness: 0.5 },
        { id: 'ctrl4', name: 'Firewalls', domain: 'cybersecurity', completeness: 0.8 }
      ],
      implementations: [
        { id: 'impl1', name: 'AES-256', domain: 'it', completeness: 0.9 },
        { id: 'impl2', name: 'MFA', domain: 'it', completeness: 0.6 },
        { id: 'impl3', name: 'SIEM', domain: 'cybersecurity', completeness: 0.4 },
        { id: 'impl4', name: 'Next-Gen FW', domain: 'cybersecurity', completeness: 0.7 }
      ],
      links: [
        { source: 'req1', target: 'ctrl1', strength: 0.8, efficiency: 0.9 },
        { source: 'req1', target: 'ctrl3', strength: 0.4, efficiency: 0.5 },
        { source: 'req2', target: 'ctrl2', strength: 0.7, efficiency: 0.8 },
        { source: 'req2', target: 'ctrl4', strength: 0.5, efficiency: 0.6 },
        { source: 'req3', target: 'ctrl3', strength: 0.6, efficiency: 0.7 },
        { source: 'ctrl1', target: 'impl1', strength: 0.9, efficiency: 0.9 },
        { source: 'ctrl2', target: 'impl2', strength: 0.6, efficiency: 0.7 },
        { source: 'ctrl3', target: 'impl3', strength: 0.4, efficiency: 0.5 },
        { source: 'ctrl4', target: 'impl4', strength: 0.7, efficiency: 0.8 }
      ]
    },
    impactAnalysis: {
      proposedChanges: [
        { id: 'change1', target: 'ctrl2', impact: 0.7, description: 'Upgrade to biometric authentication' },
        { id: 'change2', target: 'impl3', impact: 0.5, description: 'Implement AI-based threat detection' }
      ]
    }
  }}
  options={{
    showLegend: true,
    showEfficiencyMetrics: true,
    showImpactAnalysis: true,
    showTooltips: true,
    highlightDomains: true
  }}
  width="100%"
  height="500px"
/>
```

The visualizations include several performance optimizations:

- Efficient rendering of large tensors
- Proper cleanup of Three.js resources
- Throttling of animations
- Level of detail based on render mode

### Error Handling

The visualizations include robust error handling:

- Graceful handling of missing or invalid data
- Visual feedback for errors
- Fallback visualizations when appropriate

## Extending the Visualization System

To add a new visualization type:

1. Create a new visualization component in this directory
2. Update the `VisualizationRenderer` component to include the new type
3. Add the new type to the `VisualizationDemo` page

## Future Enhancements

Planned enhancements for the visualization system:

1. More visualization types (e.g., network graphs, correlation matrices)
2. More interaction options (e.g., selection, filtering)
3. Export functionality (e.g., images, videos)
4. Performance improvements for very large tensors
5. Integration with real-time data streams
