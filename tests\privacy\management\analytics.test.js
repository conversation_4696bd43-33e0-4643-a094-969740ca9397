const request = require('supertest');
const app = require('./mockApp');

describe('Privacy Management API - Analytics and Reporting', () => {
  describe('GET /privacy/management/reports/:reportType', () => {
    it('should generate a DSR summary report', async () => {
      const response = await request(app)
        .get('/privacy/management/reports/dsr-summary')
        .query({ period: 'last-30-days' });

      expect(response.status).toBe(200);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.reportType).toBe('dsr-summary');
      expect(response.body.data.dateRange).toBeDefined();
      expect(response.body.data.metrics).toBeDefined();
      expect(response.body.data.groupedData).toBeDefined();
    });

    it('should generate a consent management report', async () => {
      const response = await request(app)
        .get('/privacy/management/reports/consent-management')
        .query({ period: 'last-30-days' });

      expect(response.status).toBe(200);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.reportType).toBe('consent-management');
      expect(response.body.data.dateRange).toBeDefined();
      expect(response.body.data.metrics).toBeDefined();
      expect(response.body.data.groupedData).toBeDefined();
    });

    it('should generate a data breach report', async () => {
      const response = await request(app)
        .get('/privacy/management/reports/data-breach')
        .query({ period: 'last-90-days' });

      expect(response.status).toBe(200);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.reportType).toBe('data-breach');
      expect(response.body.data.dateRange).toBeDefined();
      expect(response.body.data.metrics).toBeDefined();
      expect(response.body.data.groupedData).toBeDefined();
    });

    it('should generate a processing activities report', async () => {
      const response = await request(app)
        .get('/privacy/management/reports/processing-activities')
        .query({ period: 'last-12-months' });

      expect(response.status).toBe(200);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.reportType).toBe('processing-activities');
      expect(response.body.data.dateRange).toBeDefined();
      expect(response.body.data.metrics).toBeDefined();
      expect(response.body.data.groupedData).toBeDefined();
    });

    it('should generate a compliance status report', async () => {
      const response = await request(app)
        .get('/privacy/management/reports/compliance-status')
        .query({ period: 'year-to-date' });

      expect(response.status).toBe(200);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.reportType).toBe('compliance-status');
      expect(response.body.data.dateRange).toBeDefined();
      expect(response.body.data.metrics).toBeDefined();
      expect(response.body.data.complianceGaps).toBeDefined();
    });

    it('should support custom date ranges', async () => {
      const response = await request(app)
        .get('/privacy/management/reports/dsr-summary')
        .query({
          period: 'custom',
          startDate: '2023-01-01T00:00:00Z',
          endDate: '2023-06-30T23:59:59Z'
        });

      expect(response.status).toBe(200);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.dateRange).toBeDefined();
      expect(new Date(response.body.data.dateRange.start)).toEqual(new Date('2023-01-01T00:00:00Z'));
      // Don't test exact time due to timezone differences
      const endDate = new Date(response.body.data.dateRange.end);
      expect(endDate.getFullYear()).toBe(2023);
      expect(endDate.getMonth()).toBe(5); // June (0-based)
      expect(endDate.getDate()).toBe(30);
    });

    it('should support grouping by different attributes', async () => {
      // First create a data subject request to ensure we have data
      const newRequest = {
        requestType: 'access',
        dataSubjectName: 'John Doe',
        dataSubjectEmail: '<EMAIL>',
        requestDetails: 'I would like to access all my personal data',
        status: 'pending'
      };

      await request(app)
        .post('/privacy/management/subject-requests')
        .send(newRequest);

      const response = await request(app)
        .get('/privacy/management/reports/dsr-summary')
        .query({
          period: 'last-30-days',
          groupBy: 'status'
        });

      expect(response.status).toBe(200);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.groupedBy).toBe('status');
      expect(response.body.data.groupedData).toBeDefined();
    });

    it('should return 400 for invalid report type', async () => {
      const response = await request(app)
        .get('/privacy/management/reports/invalid-report')
        .query({ period: 'last-30-days' });

      expect(response.status).toBe(400);
      expect(response.body.error).toBe('Bad Request');
    });

    it('should return 400 for invalid period', async () => {
      const response = await request(app)
        .get('/privacy/management/reports/dsr-summary')
        .query({ period: 'invalid-period' });

      expect(response.status).toBe(400);
      expect(response.body.error).toBe('Bad Request');
    });

    it('should return 400 for custom period without dates', async () => {
      const response = await request(app)
        .get('/privacy/management/reports/dsr-summary')
        .query({ period: 'custom' });

      expect(response.status).toBe(400);
      expect(response.body.error).toBe('Bad Request');
    });
  });

  describe('GET /privacy/management/dashboard/metrics', () => {
    it('should return dashboard metrics', async () => {
      const response = await request(app)
        .get('/privacy/management/dashboard/metrics');

      expect(response.status).toBe(200);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.dsrMetrics).toBeDefined();
      expect(response.body.data.consentMetrics).toBeDefined();
      expect(response.body.data.dataBreachMetrics).toBeDefined();
      expect(response.body.data.complianceMetrics).toBeDefined();
      expect(response.body.data.dateRanges).toBeDefined();
    });
  });
});
