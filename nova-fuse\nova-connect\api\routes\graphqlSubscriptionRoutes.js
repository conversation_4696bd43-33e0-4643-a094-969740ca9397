/**
 * GraphQL Subscription Routes
 */

const express = require('express');
const router = express.Router();
const GraphQLSubscriptionController = require('../controllers/GraphQLSubscriptionController');

// Create a new subscription
router.post('/', (req, res, next) => {
  GraphQLSubscriptionController.createSubscription(req, res, next);
});

// Get all active subscriptions
router.get('/', (req, res, next) => {
  GraphQLSubscriptionController.getActiveSubscriptions(req, res, next);
});

// Get subscription by ID
router.get('/:id', (req, res, next) => {
  GraphQLSubscriptionController.getSubscription(req, res, next);
});

// Cancel a subscription
router.delete('/:id', (req, res, next) => {
  GraphQLSubscriptionController.cancelSubscription(req, res, next);
});

// Get subscription messages
router.get('/:id/messages', (req, res, next) => {
  GraphQLSubscriptionController.getSubscriptionMessages(req, res, next);
});

// Get subscription errors
router.get('/:id/errors', (req, res, next) => {
  GraphQLSubscriptionController.getSubscriptionErrors(req, res, next);
});

module.exports = router;
