9eea7546588d629cebcf456545566de5
// Mock fs.promises
_getJestObj().mock('fs', () => ({
  promises: {
    mkdir: jest.fn().mockResolvedValue(undefined),
    readFile: jest.fn(),
    writeFile: jest.fn().mockResolvedValue(undefined)
  }
}));

// Mock ip-range-check
_getJestObj().mock('ip-range-check', () => jest.fn());
function _getJestObj() {
  const {
    jest
  } = require("@jest/globals");
  _getJestObj = () => jest;
  return jest;
}
/**
 * IP Restriction Service Tests
 */

const IpRestrictionService = require('../../../api/services/IpRestrictionService');
const fs = require('fs').promises;
const path = require('path');
const ipRangeCheck = require('ip-range-check');
describe('IpRestrictionService', () => {
  let ipRestrictionService;
  const testDataDir = path.join(__dirname, 'test-data');
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();

    // Create a new instance for each test
    ipRestrictionService = new IpRestrictionService(testDataDir);

    // Reset ip-range-check mock
    ipRangeCheck.mockReset();
  });
  describe('constructor', () => {
    it('should initialize with the correct data directory', () => {
      expect(ipRestrictionService.dataDir).toBe(testDataDir);
      expect(ipRestrictionService.ipRestrictionsFile).toBe(path.join(testDataDir, 'ip_restrictions.json'));
    });
    it('should call ensureDataDir', () => {
      expect(fs.mkdir).toHaveBeenCalledWith(testDataDir, {
        recursive: true
      });
    });
    it('should initialize with default config', () => {
      expect(ipRestrictionService.defaultConfig).toEqual({
        enabled: false,
        mode: 'allowlist',
        allowlist: [],
        blocklist: [],
        rules: []
      });
    });
  });
  describe('loadRestrictions', () => {
    it('should load restrictions from file', async () => {
      const mockRestrictions = {
        enabled: true,
        mode: 'allowlist',
        allowlist: ['***********'],
        blocklist: [],
        rules: []
      };
      fs.readFile.mockResolvedValueOnce(JSON.stringify(mockRestrictions));
      const restrictions = await ipRestrictionService.loadRestrictions();
      expect(fs.readFile).toHaveBeenCalledWith(ipRestrictionService.ipRestrictionsFile, 'utf8');
      expect(restrictions).toEqual(mockRestrictions);
    });
    it('should return default config if file does not exist', async () => {
      const error = new Error('File not found');
      error.code = 'ENOENT';
      fs.readFile.mockRejectedValueOnce(error);
      const restrictions = await ipRestrictionService.loadRestrictions();
      expect(restrictions).toEqual(ipRestrictionService.defaultConfig);
    });
    it('should throw error if file read fails for other reasons', async () => {
      const error = new Error('Permission denied');
      fs.readFile.mockRejectedValueOnce(error);
      await expect(ipRestrictionService.loadRestrictions()).rejects.toThrow('Permission denied');
    });
  });
  describe('saveRestrictions', () => {
    it('should save restrictions to file', async () => {
      const restrictions = {
        enabled: true,
        mode: 'allowlist',
        allowlist: ['***********'],
        blocklist: [],
        rules: []
      };
      await ipRestrictionService.saveRestrictions(restrictions);
      expect(fs.writeFile).toHaveBeenCalledWith(ipRestrictionService.ipRestrictionsFile, JSON.stringify(restrictions, null, 2));
    });
    it('should throw error if file write fails', async () => {
      const error = new Error('Permission denied');
      fs.writeFile.mockRejectedValueOnce(error);
      await expect(ipRestrictionService.saveRestrictions({})).rejects.toThrow('Permission denied');
    });
  });
  describe('isAllowed', () => {
    it('should allow all IPs if restrictions are disabled', async () => {
      const mockRestrictions = {
        enabled: false
      };
      fs.readFile.mockResolvedValueOnce(JSON.stringify(mockRestrictions));
      const result = await ipRestrictionService.isAllowed('***********');
      expect(result).toBe(true);
    });
    it('should check rules first', async () => {
      const mockRestrictions = {
        enabled: true,
        mode: 'allowlist',
        allowlist: [],
        blocklist: [],
        rules: [{
          name: 'Allow Office',
          action: 'allow',
          ipRange: '***********/16'
        }]
      };
      fs.readFile.mockResolvedValueOnce(JSON.stringify(mockRestrictions));
      ipRangeCheck.mockReturnValueOnce(true); // IP matches rule

      const result = await ipRestrictionService.isAllowed('***********');
      expect(ipRangeCheck).toHaveBeenCalledWith('***********', '***********/16');
      expect(result).toBe(true);
    });
    it('should check allowlist in allowlist mode', async () => {
      const mockRestrictions = {
        enabled: true,
        mode: 'allowlist',
        allowlist: ['***********'],
        blocklist: [],
        rules: []
      };
      fs.readFile.mockResolvedValueOnce(JSON.stringify(mockRestrictions));
      const result = await ipRestrictionService.isAllowed('***********');
      expect(result).toBe(true);
    });
    it('should deny IP not in allowlist in allowlist mode', async () => {
      const mockRestrictions = {
        enabled: true,
        mode: 'allowlist',
        allowlist: ['***********'],
        blocklist: [],
        rules: []
      };
      fs.readFile.mockResolvedValueOnce(JSON.stringify(mockRestrictions));
      const result = await ipRestrictionService.isAllowed('***********');
      expect(result).toBe(false);
    });
    it('should check blocklist in blocklist mode', async () => {
      const mockRestrictions = {
        enabled: true,
        mode: 'blocklist',
        allowlist: [],
        blocklist: ['***********'],
        rules: []
      };
      fs.readFile.mockResolvedValueOnce(JSON.stringify(mockRestrictions));
      const result = await ipRestrictionService.isAllowed('***********');
      expect(result).toBe(false);
    });
    it('should allow IP not in blocklist in blocklist mode', async () => {
      const mockRestrictions = {
        enabled: true,
        mode: 'blocklist',
        allowlist: [],
        blocklist: ['***********'],
        rules: []
      };
      fs.readFile.mockResolvedValueOnce(JSON.stringify(mockRestrictions));
      const result = await ipRestrictionService.isAllowed('***********');
      expect(result).toBe(true);
    });
  });
  describe('addToAllowlist', () => {
    it('should add IP to allowlist', async () => {
      const mockRestrictions = {
        enabled: true,
        mode: 'allowlist',
        allowlist: [],
        blocklist: [],
        rules: []
      };
      fs.readFile.mockResolvedValueOnce(JSON.stringify(mockRestrictions));

      // Mock isValidIpOrRange
      jest.spyOn(ipRestrictionService, 'isValidIpOrRange').mockReturnValueOnce(true);
      await ipRestrictionService.addToAllowlist('***********');
      expect(fs.writeFile).toHaveBeenCalled();
      const savedRestrictions = JSON.parse(fs.writeFile.mock.calls[0][1]);
      expect(savedRestrictions.allowlist).toContain('***********');
    });
    it('should remove IP from blocklist if added to allowlist', async () => {
      const mockRestrictions = {
        enabled: true,
        mode: 'allowlist',
        allowlist: [],
        blocklist: ['***********'],
        rules: []
      };
      fs.readFile.mockResolvedValueOnce(JSON.stringify(mockRestrictions));

      // Mock isValidIpOrRange
      jest.spyOn(ipRestrictionService, 'isValidIpOrRange').mockReturnValueOnce(true);
      await ipRestrictionService.addToAllowlist('***********');
      expect(fs.writeFile).toHaveBeenCalled();
      const savedRestrictions = JSON.parse(fs.writeFile.mock.calls[0][1]);
      expect(savedRestrictions.allowlist).toContain('***********');
      expect(savedRestrictions.blocklist).not.toContain('***********');
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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