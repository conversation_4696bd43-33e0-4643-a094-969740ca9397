/**
 * NovaFuse Universal API Connector - Connector Management Test
 * 
 * This script tests the connector management implementation.
 */

const connectorRegistryService = require('./src/connectors/services/connector-registry');
const connectorConfigService = require('./src/connectors/services/connector-config');
const connectorRuntimeService = require('./src/connectors/services/connector-runtime');

async function testConnectorManagement() {
  console.log('Testing Connector Management...');

  try {
    // Create a connector
    console.log('\nCreating connector...');
    const connector = await connectorRegistryService.createConnector({
      name: 'Test Connector',
      description: 'A test connector for testing purposes',
      version: '1.0.0',
      type: 'source',
      status: 'draft'
    });
    console.log('Connector created:', connector);

    // Get all connectors
    console.log('\nGetting all connectors...');
    const connectors = await connectorRegistryService.getAllConnectors();
    console.log('Connectors:', connectors);

    // Get connector by ID
    console.log('\nGetting connector by ID...');
    const retrievedConnector = await connectorRegistryService.getConnector(connector.id);
    console.log('Retrieved connector:', retrievedConnector);

    // Update connector
    console.log('\nUpdating connector...');
    const updatedConnector = await connectorRegistryService.updateConnector(connector.id, {
      description: 'An updated test connector'
    });
    console.log('Updated connector:', updatedConnector);

    // Create a configuration
    console.log('\nCreating configuration...');
    const config = await connectorConfigService.createConfiguration({
      name: 'Test Configuration',
      description: 'A test configuration for testing purposes',
      connectorId: connector.id,
      values: {
        apiKey: 'test-api-key',
        endpoint: 'https://api.example.com'
      }
    });
    console.log('Configuration created:', config);

    // Get configurations for connector
    console.log('\nGetting configurations for connector...');
    const configs = await connectorConfigService.getConfigurationsForConnector(connector.id);
    console.log('Configurations:', configs);

    // Execute connector
    console.log('\nExecuting connector...');
    const result = await connectorRuntimeService.executeConnector(connector.id, config.id, {
      timeout: 5000
    });
    console.log('Execution result:', result);

    // Get execution status
    console.log('\nGetting execution status...');
    const status = connectorRuntimeService.getExecutionStatus(result.executionId);
    console.log('Execution status:', status);

    // Get all executions
    console.log('\nGetting all executions...');
    const executions = connectorRuntimeService.getAllExecutions();
    console.log('Executions:', executions);

    // Delete configuration
    console.log('\nDeleting configuration...');
    const configDeleted = await connectorConfigService.deleteConfiguration(config.id);
    console.log('Configuration deleted:', configDeleted);

    // Delete connector
    console.log('\nDeleting connector...');
    const connectorDeleted = await connectorRegistryService.deleteConnector(connector.id);
    console.log('Connector deleted:', connectorDeleted);

    console.log('\nConnector Management Test Completed Successfully!');
  } catch (error) {
    console.error('Error testing connector management:', error);
  }
}

// Run the test
testConnectorManagement();
