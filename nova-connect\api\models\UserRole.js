/**
 * UserRole Model
 * 
 * This model defines the user-role relationship schema for the RBAC system.
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const userRoleSchema = new Schema({
  user: { 
    type: Schema.Types.ObjectId, 
    ref: 'User', 
    required: true 
  },
  role: { 
    type: Schema.Types.ObjectId, 
    ref: 'Role', 
    required: true 
  },
  scope: { 
    type: String, 
    enum: ['global', 'team', 'project', 'environment'], 
    default: 'global' 
  },
  scopeId: { 
    type: Schema.Types.ObjectId 
  },
  createdBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'User' 
  },
  updatedBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'User' 
  }
}, { 
  timestamps: true 
});

// Add compound unique index to prevent duplicate assignments
userRoleSchema.index({ user: 1, role: 1, scope: 1, scopeId: 1 }, { unique: true });

// Add indexes for efficient querying
userRoleSchema.index({ user: 1 });
userRoleSchema.index({ role: 1 });
userRoleSchema.index({ scope: 1, scopeId: 1 });

// Create model
const UserRole = mongoose.model('UserRole', userRoleSchema);

module.exports = UserRole;
