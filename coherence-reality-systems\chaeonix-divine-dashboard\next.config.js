/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  swcMinify: true,
  experimental: {
    appDir: false, // Using pages directory for now
  },
  env: {
    CHAEONIX_API_URL: process.env.CHAEONIX_API_URL || 'http://localhost:8000',
    CHAEONIX_WS_URL: process.env.CHAEONIX_WS_URL || 'ws://localhost:8000',
    DIVINE_MODE: process.env.DIVINE_MODE || 'true',
  },
  async rewrites() {
    return [
      {
        source: '/api/chaeonix/:path*',
        destination: 'http://localhost:8000/api/:path*',
      },
      {
        source: '/divine/:path*',
        destination: 'http://localhost:8000/divine/:path*',
      },
    ];
  },
  webpack: (config, { isServer }) => {
    // Handle Three.js and D3.js properly
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        path: false,
        os: false,
      };
    }
    
    return config;
  },
  images: {
    domains: ['localhost'],
    unoptimized: true,
  },
  // Custom headers for CHAEONIX
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-CHAEONIX-Version',
            value: '1.0.0-COHERENCE_DRIVEN_AEONIC_INTELLIGENCE',
          },
          {
            key: 'X-Divine-Mode',
            value: 'ACTIVE',
          },
        ],
      },
    ];
  },
}

module.exports = nextConfig
