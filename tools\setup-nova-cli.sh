#!/bin/bash
# NovaFuse CLI Setup Script
# Sets up the Nova scaffolding and development environment

set -e

NOVA_CLI_DIR="tools/nova-cli"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
WORKSPACE_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${CYAN}"
    echo "================================================"
    echo "        NOVA CLI SETUP & INSTALLATION"
    echo "        NovaFuse Technologies"
    echo "================================================"
    echo -e "${NC}"
}

print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

check_prerequisites() {
    print_info "Checking prerequisites..."
    
    # Check Python
    if ! command -v python3 &> /dev/null; then
        print_error "Python 3 is required but not installed"
        exit 1
    fi
    print_status "Python 3 found: $(python3 --version)"
    
    # Check pip
    if ! command -v pip3 &> /dev/null; then
        print_error "pip3 is required but not installed"
        exit 1
    fi
    print_status "pip3 found"
    
    # Check Node.js (optional)
    if command -v node &> /dev/null; then
        print_status "Node.js found: $(node --version)"
    else
        print_warning "Node.js not found (optional for TypeScript components)"
    fi
    
    # Check Go (optional)
    if command -v go &> /dev/null; then
        print_status "Go found: $(go version)"
    else
        print_warning "Go not found (optional for Go components)"
    fi
}

setup_directory_structure() {
    print_info "Setting up Nova CLI directory structure..."
    
    cd "$WORKSPACE_ROOT"
    
    # Create main directories
    mkdir -p "$NOVA_CLI_DIR"
    mkdir -p "$NOVA_CLI_DIR/templates"
    mkdir -p "$NOVA_CLI_DIR/templates/go"
    mkdir -p "$NOVA_CLI_DIR/templates/python"
    mkdir -p "$NOVA_CLI_DIR/templates/typescript"
    mkdir -p "$NOVA_CLI_DIR/templates/rust"
    mkdir -p "$NOVA_CLI_DIR/templates/testing"
    mkdir -p "$NOVA_CLI_DIR/templates/docs"
    
    print_status "Directory structure created"
}

install_python_dependencies() {
    print_info "Installing Python dependencies..."

    # Create requirements file for Nova CLI
    cat > "$NOVA_CLI_DIR/requirements.txt" << EOF
fastapi>=0.68.0
uvicorn>=0.15.0
requests>=2.25.0
pytest>=6.0.0
prometheus-client>=0.12.0
pyjwt>=2.0.0
pydantic>=1.8.0
click>=8.0.0
jinja2>=3.0.0
pyyaml>=5.4.0
networkx>=2.8.0
argparse>=1.4.0
dataclasses>=0.6.0
pathlib>=1.0.0
EOF

    # Install dependencies
    pip3 install -r "$NOVA_CLI_DIR/requirements.txt"

    print_status "Python dependencies installed"
}

create_cli_executable() {
    print_info "Creating Nova CLI executable..."
    
    # Create main CLI script
    cat > "$NOVA_CLI_DIR/nova" << 'EOF'
#!/usr/bin/env python3
"""
Nova CLI - NovaFuse Technologies Scaffolding Tool
"""

import sys
import os
from pathlib import Path

# Add the nova-cli directory to Python path
cli_dir = Path(__file__).parent
sys.path.insert(0, str(cli_dir))

from nova_scaffold import main

if __name__ == "__main__":
    main()
EOF
    
    # Make executable
    chmod +x "$NOVA_CLI_DIR/nova"
    
    # Create symlink in workspace root for easy access
    ln -sf "$NOVA_CLI_DIR/nova" "$WORKSPACE_ROOT/nova"
    
    print_status "Nova CLI executable created"
}

create_config_file() {
    print_info "Creating Nova CLI configuration..."
    
    cat > "$NOVA_CLI_DIR/nova-config.yaml" << EOF
# Nova CLI Configuration
nova_cli:
  version: "1.0.0"
  workspace_root: "$WORKSPACE_ROOT"
  
standards:
  q_score_threshold: 0.85
  required_endpoints:
    - "/health"
    - "/metrics" 
    - "/auth"
  
  security_requirements:
    - "JWT validation"
    - "Q-Score compliance"
    - "∂Ψ=0 enforcement"
  
  observability_requirements:
    - "Prometheus metrics"
    - "Anomaly detection"
    - "NovaPulse+ integration"

languages:
  go:
    use_case: "Infrastructure, Security, Performance-critical"
    template_dir: "templates/go"
    
  python:
    use_case: "AI/ML, Telemetry, Rapid prototyping"
    template_dir: "templates/python"
    
  typescript:
    use_case: "UI/API layers, Frontend"
    template_dir: "templates/typescript"
    
  rust:
    use_case: "Event bus, High-throughput systems"
    template_dir: "templates/rust"

component_types:
  - "Security"
  - "Infrastructure" 
  - "AI/ML"
  - "Telemetry"
  - "UI/Dashboard"
  - "Integration"
  - "Finance"
  - "Healthcare"
EOF
    
    print_status "Configuration file created"
}

setup_git_hooks() {
    print_info "Setting up Git hooks for Nova standards validation..."
    
    # Create pre-commit hook
    mkdir -p .git/hooks
    
    cat > .git/hooks/pre-commit << 'EOF'
#!/bin/bash
# Nova Standards Pre-commit Hook

echo "🔍 Running Nova standards validation..."

# Run Nova CLI validation
if [ -f "tools/nova-cli/validate-standards.py" ]; then
    python3 tools/nova-cli/validate-standards.py
    if [ $? -ne 0 ]; then
        echo "❌ Nova standards validation failed"
        echo "Run 'python3 tools/nova-cli/validate-standards.py' for details"
        exit 1
    fi
    echo "✅ Nova standards validation passed"
fi
EOF
    
    chmod +x .git/hooks/pre-commit
    
    print_status "Git hooks configured"
}

create_usage_examples() {
    print_info "Creating usage examples..."

    cat > "$NOVA_CLI_DIR/USAGE.md" << 'EOF'
# Nova CLI Usage Guide

## Quick Start

```bash
# Create a new Nova component
./nova create --name NovaExample --type Security --language go --description "Example security component"

# Validate existing components against standards
./nova validate

# Check component health
./nova health

# Map component dependencies
./nova dependencies

# Update manifest automatically
./nova update-manifest

# List all components
./nova list

# Generate comprehensive dashboard
python tools/nova-cli/dashboard-generator.py
```

## Component Creation Examples

```bash
# Security component in Go
./nova create --name NovaAuth --type Security --language go --description "Authentication service"

# AI/ML component in Python
./nova create --name NovaIntel --type AI/ML --language python --description "Intelligence analysis"

# UI component in TypeScript
./nova create --name NovaDash --type UI/Dashboard --language typescript --description "Management dashboard"

# Infrastructure component in Rust
./nova create --name NovaCore --type Infrastructure --language rust --description "Core event bus"
```

## Monitoring and Analysis

```bash
# Validate all components against standards
./nova validate

# Assess component health metrics
./nova health

# Map component dependencies and relationships
./nova dependencies

# Update NOVA_MANIFEST.md automatically
./nova update-manifest

# Generate visual dashboard
python tools/nova-cli/dashboard-generator.py
```

## Advanced Usage

```bash
# Run comprehensive validation with custom workspace
./nova validate --workspace /path/to/workspace

# Generate health report for specific workspace
./nova health --workspace /path/to/workspace

# Export dependency graph for visualization
python tools/nova-cli/dependency-mapper.py /path/to/workspace

# Create compliance dashboard
python tools/nova-cli/dashboard-generator.py /path/to/workspace
```

## Configuration

Edit `tools/nova-cli/nova-config.yaml` to customize:
- Q-Score thresholds
- Required endpoints
- Security requirements
- Language preferences
- Health monitoring thresholds
- Dependency analysis settings

## CI/CD Integration

The Nova CLI integrates with GitHub Actions for automated validation:
- Standards validation on every commit
- Health monitoring on schedule
- Automatic manifest updates
- Compliance reporting

See `.github/workflows/nova-standards-check.yml` for configuration.
EOF

    print_status "Usage examples created"
}

print_completion_message() {
    echo ""
    echo -e "${GREEN}🎉 Nova CLI Setup Complete!${NC}"
    echo ""
    echo -e "${CYAN}📋 What's Available:${NC}"
    echo -e "   ${BLUE}•${NC} Nova CLI tool: ${YELLOW}./nova${NC}"
    echo -e "   ${BLUE}•${NC} Component scaffolding: ${YELLOW}./nova create${NC}"
    echo -e "   ${BLUE}•${NC} Standards validation: ${YELLOW}./nova validate${NC}"
    echo -e "   ${BLUE}•${NC} Health monitoring: ${YELLOW}./nova health${NC}"
    echo -e "   ${BLUE}•${NC} Dependency mapping: ${YELLOW}./nova dependencies${NC}"
    echo -e "   ${BLUE}•${NC} Manifest updates: ${YELLOW}./nova update-manifest${NC}"
    echo -e "   ${BLUE}•${NC} Visual dashboard: ${YELLOW}python tools/nova-cli/dashboard-generator.py${NC}"
    echo -e "   ${BLUE}•${NC} CI/CD integration: ${YELLOW}.github/workflows/nova-standards-check.yml${NC}"
    echo ""
    echo -e "${CYAN}🚀 Quick Start:${NC}"
    echo -e "   ${YELLOW}./nova create --name NovaExample --type Security --language python${NC}"
    echo -e "   ${YELLOW}./nova validate${NC}"
    echo -e "   ${YELLOW}./nova health${NC}"
    echo ""
    echo -e "${CYAN}📊 Dashboard:${NC}"
    echo -e "   ${YELLOW}python tools/nova-cli/dashboard-generator.py${NC}"
    echo -e "   ${YELLOW}# Then open nova-dashboard/index.html in your browser${NC}"
    echo ""
    echo -e "${CYAN}📖 Documentation:${NC}"
    echo -e "   ${YELLOW}cat tools/nova-cli/USAGE.md${NC}"
    echo ""
    echo -e "${PURPLE}✨ Happy coding with NovaFuse Technologies!${NC}"
}

# Main execution
main() {
    print_header
    
    check_prerequisites
    setup_directory_structure
    install_python_dependencies
    create_cli_executable
    create_config_file
    setup_git_hooks
    create_usage_examples
    
    print_completion_message
}

# Run main function
main "$@"
