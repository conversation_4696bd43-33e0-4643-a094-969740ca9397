/**
 * Schema Routes
 * 
 * This file defines the routes for schema management.
 */

const express = require('express');
const router = express.Router();
const schemaController = require('./schemaController');
const authMiddleware = require('../middleware/authMiddleware');

/**
 * @swagger
 * /api/v1/schemas:
 *   get:
 *     summary: List all schemas
 *     description: Get a list of all available schemas
 *     tags: [Schemas]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of schemas
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/', authMiddleware.authenticate, schemaController.listSchemas);

/**
 * @swagger
 * /api/v1/schemas/{entityType}:
 *   get:
 *     summary: Get schema
 *     description: Get schema for entity type
 *     tags: [Schemas]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: entityType
 *         required: true
 *         schema:
 *           type: string
 *         description: Entity type
 *     responses:
 *       200:
 *         description: Schema
 *       404:
 *         description: Schema not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/:entityType', authMiddleware.authenticate, schemaController.getSchema);

/**
 * @swagger
 * /api/v1/schemas/{entityType}:
 *   post:
 *     summary: Save schema
 *     description: Save custom schema for entity type
 *     tags: [Schemas]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: entityType
 *         required: true
 *         schema:
 *           type: string
 *         description: Entity type
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: Schema saved
 *       400:
 *         description: Invalid schema
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post('/:entityType', 
  authMiddleware.authenticate, 
  authMiddleware.hasRole(['admin']), 
  schemaController.saveSchema
);

/**
 * @swagger
 * /api/v1/schemas/{entityType}:
 *   delete:
 *     summary: Delete schema
 *     description: Delete custom schema for entity type
 *     tags: [Schemas]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: entityType
 *         required: true
 *         schema:
 *           type: string
 *         description: Entity type
 *     responses:
 *       200:
 *         description: Schema deleted
 *       404:
 *         description: Schema not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.delete('/:entityType', 
  authMiddleware.authenticate, 
  authMiddleware.hasRole(['admin']), 
  schemaController.deleteSchema
);

/**
 * @swagger
 * /api/v1/schemas/generate:
 *   post:
 *     summary: Generate schema
 *     description: Generate schema from JSON data
 *     tags: [Schemas]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - entityName
 *               - jsonData
 *             properties:
 *               entityName:
 *                 type: string
 *                 description: Entity name
 *               jsonData:
 *                 type: object
 *                 description: JSON data
 *     responses:
 *       200:
 *         description: Generated schema
 *       400:
 *         description: Invalid request
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post('/generate', 
  authMiddleware.authenticate, 
  authMiddleware.hasRole(['admin']), 
  schemaController.generateSchema
);

module.exports = router;
