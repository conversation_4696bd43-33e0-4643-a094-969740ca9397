version: '3.8'

services:
  # NovaConnect API
  api:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=development
      - PORT=3001
      - MONGODB_URI=mongodb://mongo:27017/nova-connect
      - REDIS_URI=redis://redis:6379
      - JWT_SECRET=development-jwt-secret
      - METRICS_ENABLED=true
      - TRACING_ENABLED=true
      - TRACING_EXPORTER=console
      - CSDE_API_URL=http://csde-api:3010
    volumes:
      - ./:/app
      - /app/node_modules
    depends_on:
      - mongo
      - redis
      - csde-api
    networks:
      - nova-network

  # CSDE API
  csde-api:
    build:
      context: ../src/csde
      dockerfile: Dockerfile
    image: csde-api:latest
    container_name: csde-api
    restart: unless-stopped
    ports:
      - "3010:3010"
    environment:
      - NODE_ENV=development
      - PORT=3010
      - LOG_LEVEL=info
    volumes:
      - ../src/csde/config:/app/config
      - ../src/csde/logs:/app/logs
    networks:
      - nova-network

  # MongoDB
  mongo:
    image: mongo:4.4
    ports:
      - "27017:27017"
    volumes:
      - mongo-data:/data/db
    networks:
      - nova-network

  # Redis
  redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - nova-network

  # MongoDB Express (Web-based MongoDB admin interface)
  mongo-express:
    image: mongo-express:latest
    ports:
      - "8081:8081"
    environment:
      - ME_CONFIG_MONGODB_SERVER=mongo
      - ME_CONFIG_MONGODB_PORT=27017
      - ME_CONFIG_BASICAUTH_USERNAME=admin
      - ME_CONFIG_BASICAUTH_PASSWORD=admin
    depends_on:
      - mongo
    networks:
      - nova-network

  # Redis Commander (Web-based Redis admin interface)
  redis-commander:
    image: rediscommander/redis-commander:latest
    ports:
      - "8082:8081"
    environment:
      - REDIS_HOSTS=local:redis:6379
    depends_on:
      - redis
    networks:
      - nova-network

volumes:
  mongo-data:
  redis-data:

networks:
  nova-network:
    driver: bridge
