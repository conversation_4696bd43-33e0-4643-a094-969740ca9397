# Server Configuration
PORT=3000
NODE_ENV=development
LOG_LEVEL=info

# MongoDB Configuration
MONGODB_URI=mongodb://localhost:27017/nova-marketplace

# JWT Authentication
JWT_SECRET=your_jwt_secret_here
JWT_EXPIRATION=1d

# Stripe Payment Processing
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret

# Email Configuration
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USER=your_smtp_username
SMTP_PASS=your_smtp_password
EMAIL_FROM=<EMAIL>

# API Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,https://novafuse.com

# Encryption Keys
ENCRYPTION_KEY=your_encryption_key_here
KEY_ROTATION_DAYS=90

# Partner Revenue Sharing
PIONEER_PARTNER_SHARE=0.9
GOLD_PARTNER_SHARE=0.85
SILVER_PARTNER_SHARE=0.8
STANDARD_PARTNER_SHARE=0.7
