/**
 * NovaFuse Universal API Connector - Error Handler Middleware
 * 
 * This module provides middleware for handling errors in the UAC API.
 */

const { createLogger } = require('../utils/logger');
const { 
  UAConnectorError,
  ValidationError,
  AuthenticationError,
  ConnectionError,
  ApiError,
  RateLimitExceededError,
  ResourceNotFoundError
} = require('../errors');

const logger = createLogger('error-handler');

/**
 * Map error types to HTTP status codes
 * @type {Object}
 */
const ERROR_STATUS_MAP = {
  ValidationError: 400,
  AuthenticationError: 401,
  InsufficientPermissionsError: 403,
  ResourceNotFoundError: 404,
  ConnectorNotFoundError: 404,
  RateLimitExceededError: 429,
  ConnectionError: 502,
  ServiceUnavailableError: 503
};

/**
 * Determine the HTTP status code for an error
 * 
 * @param {Error} error - The error
 * @returns {number} - The HTTP status code
 */
function getStatusCode(error) {
  // If the error has a statusCode property, use it
  if (error.statusCode) {
    return error.statusCode;
  }
  
  // Check if the error type is in the map
  for (const [errorType, statusCode] of Object.entries(ERROR_STATUS_MAP)) {
    if (error.name === errorType) {
      return statusCode;
    }
  }
  
  // Default to 500 for unknown errors
  return 500;
}

/**
 * Format the error response based on the environment
 * 
 * @param {Error} error - The error
 * @param {boolean} isDevelopment - Whether the environment is development
 * @returns {Object} - The formatted error response
 */
function formatErrorResponse(error, isDevelopment) {
  // For UAConnectorError, use its built-in methods
  if (error instanceof UAConnectorError) {
    const response = {
      error: {
        message: isDevelopment ? error.getDeveloperMessage() : error.getUserMessage(),
        code: error.code,
        id: error.errorId
      }
    };
    
    // In development mode, include more details
    if (isDevelopment) {
      response.error.details = error.toJSON(true);
    }
    
    return response;
  }
  
  // For other errors, create a generic response
  const response = {
    error: {
      message: isDevelopment ? error.message : 'An unexpected error occurred',
      code: 'INTERNAL_ERROR'
    }
  };
  
  // In development mode, include more details
  if (isDevelopment) {
    response.error.details = {
      name: error.name,
      message: error.message,
      stack: error.stack
    };
  }
  
  return response;
}

/**
 * Error handler middleware
 * 
 * @param {Error} err - The error
 * @param {Object} req - The request object
 * @param {Object} res - The response object
 * @param {Function} next - The next middleware function
 */
function errorHandler(err, req, res, next) {
  // Determine if we're in development mode
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  // Get the status code
  const statusCode = getStatusCode(err);
  
  // Format the error response
  const errorResponse = formatErrorResponse(err, isDevelopment);
  
  // Log the error
  if (statusCode >= 500) {
    logger.error('Server error', { 
      error: err.message, 
      stack: err.stack,
      url: req.originalUrl,
      method: req.method,
      statusCode
    });
  } else {
    logger.warn('Client error', { 
      error: err.message,
      url: req.originalUrl,
      method: req.method,
      statusCode
    });
  }
  
  // Set the response status and send the error
  res.status(statusCode).json(errorResponse);
}

/**
 * Not found handler middleware
 * 
 * @param {Object} req - The request object
 * @param {Object} res - The response object
 * @param {Function} next - The next middleware function
 */
function notFoundHandler(req, res, next) {
  const error = new ResourceNotFoundError('Route', req.originalUrl, {
    context: {
      method: req.method,
      url: req.originalUrl
    }
  });
  
  next(error);
}

module.exports = {
  errorHandler,
  notFoundHandler
};
