const Joi = require('joi');

// Validation schemas
const schemas = {
  // Report validation schemas
  createReport: Joi.object({
    title: Joi.string().required().min(3).max(100),
    description: Joi.string().optional().max(500),
    reportType: Joi.string().required().valid('annual', 'quarterly', 'sustainability', 'impact', 'custom'),
    reportingPeriod: Joi.object({
      startDate: Joi.string().required().pattern(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
      endDate: Joi.string().required().pattern(/^\d{4}-\d{2}-\d{2}$/) // YYYY-MM-DD format
    }).required(),
    frameworks: Joi.array().items(Joi.string()).optional(),
    metrics: Joi.array().items(Joi.string()).optional(),
    sections: Joi.array().items(
      Joi.object({
        title: Joi.string().required().min(1).max(100),
        content: Joi.string().required(),
        order: Joi.number().integer().min(1).required()
      })
    ).optional(),
    attachments: Joi.array().items(
      Joi.object({
        name: Joi.string().required().min(1).max(100),
        fileType: Joi.string().required(),
        url: Joi.string().required().uri()
      })
    ).optional()
  }),
  
  updateReport: Joi.object({
    title: Joi.string().optional().min(3).max(100),
    description: Joi.string().optional().max(500),
    reportType: Joi.string().optional().valid('annual', 'quarterly', 'sustainability', 'impact', 'custom'),
    reportingPeriod: Joi.object({
      startDate: Joi.string().required().pattern(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
      endDate: Joi.string().required().pattern(/^\d{4}-\d{2}-\d{2}$/) // YYYY-MM-DD format
    }).optional(),
    status: Joi.string().optional().valid('draft', 'in-review', 'published', 'archived'),
    frameworks: Joi.array().items(Joi.string()).optional(),
    metrics: Joi.array().items(Joi.string()).optional(),
    sections: Joi.array().items(
      Joi.object({
        title: Joi.string().required().min(1).max(100),
        content: Joi.string().required(),
        order: Joi.number().integer().min(1).required()
      })
    ).optional(),
    attachments: Joi.array().items(
      Joi.object({
        name: Joi.string().required().min(1).max(100),
        fileType: Joi.string().required(),
        url: Joi.string().required().uri()
      })
    ).optional()
  }).min(1), // At least one field must be provided
  
  publishReport: Joi.object({
    publishedAt: Joi.string().optional().pattern(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}Z$/), // ISO 8601 format
  }),
  
  // Template validation schemas
  createTemplate: Joi.object({
    name: Joi.string().required().min(3).max(100),
    description: Joi.string().optional().max(500),
    reportType: Joi.string().required().valid('annual', 'quarterly', 'sustainability', 'impact', 'custom'),
    frameworks: Joi.array().items(Joi.string()).optional(),
    structure: Joi.array().items(
      Joi.object({
        title: Joi.string().required().min(1).max(100),
        description: Joi.string().optional().max(500),
        order: Joi.number().integer().min(1).required(),
        metricCategories: Joi.array().items(Joi.string()).optional()
      })
    ).required(),
    isDefault: Joi.boolean().optional()
  }),
  
  updateTemplate: Joi.object({
    name: Joi.string().optional().min(3).max(100),
    description: Joi.string().optional().max(500),
    reportType: Joi.string().optional().valid('annual', 'quarterly', 'sustainability', 'impact', 'custom'),
    frameworks: Joi.array().items(Joi.string()).optional(),
    structure: Joi.array().items(
      Joi.object({
        title: Joi.string().required().min(1).max(100),
        description: Joi.string().optional().max(500),
        order: Joi.number().integer().min(1).required(),
        metricCategories: Joi.array().items(Joi.string()).optional()
      })
    ).optional(),
    isDefault: Joi.boolean().optional()
  }).min(1) // At least one field must be provided
};

/**
 * Middleware to validate request data against a schema
 * @param {string} schemaName - Name of the schema to validate against
 * @returns {Function} Express middleware function
 */
const validateRequest = (schemaName) => {
  return (req, res, next) => {
    const schema = schemas[schemaName];
    
    if (!schema) {
      return res.status(500).json({
        error: 'Internal Server Error',
        message: `Validation schema '${schemaName}' not found`
      });
    }
    
    const { error, value } = schema.validate(req.body, {
      abortEarly: false, // Return all errors, not just the first one
      stripUnknown: true // Remove unknown fields
    });
    
    if (error) {
      const errorMessage = error.details.map(detail => detail.message).join(', ');
      return res.status(400).json({
        error: 'Bad Request',
        message: errorMessage
      });
    }
    
    // Replace request body with validated value
    req.body = value;
    next();
  };
};

module.exports = {
  validateRequest
};
