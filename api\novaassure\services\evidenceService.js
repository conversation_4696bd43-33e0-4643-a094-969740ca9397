/**
 * Evidence Service
 * 
 * This service provides functionality for evidence management.
 */

const { Evidence, Control, TestExecution } = require('../models');
const logger = require('../utils/logger');
const fs = require('fs');
const path = require('path');
const axios = require('axios');
const { exec } = require('child_process');
const util = require('util');
const execPromise = util.promisify(exec);

/**
 * Get all evidence
 * @param {Object} filters - Filters
 * @param {number} page - Page number
 * @param {number} limit - Items per page
 * @returns {Promise<Object>} - Evidence with pagination
 */
async function getAllEvidence(filters = {}, page = 1, limit = 10) {
  try {
    // Build query
    const query = {};
    
    if (filters.controlId) {
      query.control = filters.controlId;
    }
    
    if (filters.testExecutionId) {
      query.testExecution = filters.testExecutionId;
    }
    
    if (filters.type) {
      query.type = filters.type;
    }
    
    if (filters.search) {
      query.$text = { $search: filters.search };
    }
    
    // Count total
    const total = await Evidence.countDocuments(query);
    
    // Get evidence
    const evidence = await Evidence.find(query)
      .populate('control')
      .populate('testExecution')
      .populate('createdBy')
      .skip((page - 1) * limit)
      .limit(limit)
      .sort({ createdAt: -1 });
    
    return {
      evidence,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    };
  } catch (error) {
    logger.error('Failed to get evidence', error);
    throw error;
  }
}

/**
 * Get evidence by ID
 * @param {string} id - Evidence ID
 * @returns {Promise<Object>} - Evidence object
 */
async function getEvidenceById(id) {
  try {
    const evidence = await Evidence.getById(id);
    
    if (!evidence) {
      throw new Error('Evidence not found');
    }
    
    return evidence;
  } catch (error) {
    logger.error(`Failed to get evidence ${id}`, error);
    throw error;
  }
}

/**
 * Create evidence from file
 * @param {Object} evidenceData - Evidence data
 * @param {Object} file - File object
 * @param {string} userId - User ID
 * @returns {Promise<Object>} - Created evidence
 */
async function createEvidenceFromFile(evidenceData, file, userId) {
  try {
    // Validate control if provided
    if (evidenceData.controlId) {
      const control = await Control.findById(evidenceData.controlId);
      
      if (!control) {
        throw new Error(`Control ${evidenceData.controlId} not found`);
      }
    }
    
    // Validate test execution if provided
    if (evidenceData.testExecutionId) {
      const testExecution = await TestExecution.findById(evidenceData.testExecutionId);
      
      if (!testExecution) {
        throw new Error(`Test execution ${evidenceData.testExecutionId} not found`);
      }
    }
    
    // Create evidence
    const evidence = await Evidence.createFromFile(evidenceData, file, userId);
    
    logger.info(`Evidence ${evidence._id} created by ${userId}`);
    
    return evidence;
  } catch (error) {
    logger.error('Failed to create evidence from file', error);
    throw error;
  }
}

/**
 * Create evidence from content
 * @param {Object} evidenceData - Evidence data
 * @param {string} userId - User ID
 * @returns {Promise<Object>} - Created evidence
 */
async function createEvidenceFromContent(evidenceData, userId) {
  try {
    // Validate control if provided
    if (evidenceData.controlId) {
      const control = await Control.findById(evidenceData.controlId);
      
      if (!control) {
        throw new Error(`Control ${evidenceData.controlId} not found`);
      }
    }
    
    // Validate test execution if provided
    if (evidenceData.testExecutionId) {
      const testExecution = await TestExecution.findById(evidenceData.testExecutionId);
      
      if (!testExecution) {
        throw new Error(`Test execution ${evidenceData.testExecutionId} not found`);
      }
    }
    
    // Create evidence
    const evidence = await Evidence.createFromContent(evidenceData, userId);
    
    logger.info(`Evidence ${evidence._id} created by ${userId}`);
    
    return evidence;
  } catch (error) {
    logger.error('Failed to create evidence from content', error);
    throw error;
  }
}

/**
 * Update evidence
 * @param {string} id - Evidence ID
 * @param {Object} evidenceData - Evidence data
 * @param {string} userId - User ID
 * @returns {Promise<Object>} - Updated evidence
 */
async function updateEvidence(id, evidenceData, userId) {
  try {
    // Get evidence
    const evidence = await Evidence.getById(id);
    
    if (!evidence) {
      throw new Error('Evidence not found');
    }
    
    // Validate control if provided
    if (evidenceData.controlId) {
      const control = await Control.findById(evidenceData.controlId);
      
      if (!control) {
        throw new Error(`Control ${evidenceData.controlId} not found`);
      }
      
      evidence.control = evidenceData.controlId;
    }
    
    // Validate test execution if provided
    if (evidenceData.testExecutionId) {
      const testExecution = await TestExecution.findById(evidenceData.testExecutionId);
      
      if (!testExecution) {
        throw new Error(`Test execution ${evidenceData.testExecutionId} not found`);
      }
      
      evidence.testExecution = evidenceData.testExecutionId;
    }
    
    // Update evidence
    if (evidenceData.name) {
      evidence.name = evidenceData.name;
    }
    
    if (evidenceData.description) {
      evidence.description = evidenceData.description;
    }
    
    if (evidenceData.type) {
      evidence.type = evidenceData.type;
    }
    
    if (evidenceData.metadata) {
      evidence.metadata = evidenceData.metadata;
    }
    
    evidence.updatedBy = userId;
    
    // Save evidence
    await evidence.save();
    
    logger.info(`Evidence ${id} updated by ${userId}`);
    
    return evidence;
  } catch (error) {
    logger.error(`Failed to update evidence ${id}`, error);
    throw error;
  }
}

/**
 * Delete evidence
 * @param {string} id - Evidence ID
 * @returns {Promise<Object>} - Deleted evidence
 */
async function deleteEvidence(id) {
  try {
    // Get evidence
    const evidence = await Evidence.getById(id);
    
    if (!evidence) {
      throw new Error('Evidence not found');
    }
    
    // Delete file if exists
    if (evidence.filePath && fs.existsSync(evidence.filePath)) {
      fs.unlinkSync(evidence.filePath);
    }
    
    // Delete evidence
    await evidence.remove();
    
    logger.info(`Evidence ${id} deleted`);
    
    return evidence;
  } catch (error) {
    logger.error(`Failed to delete evidence ${id}`, error);
    throw error;
  }
}

/**
 * Get evidence file
 * @param {string} id - Evidence ID
 * @returns {Promise<Object>} - File information
 */
async function getEvidenceFile(id) {
  try {
    // Get evidence
    const evidence = await Evidence.getById(id);
    
    if (!evidence) {
      throw new Error('Evidence not found');
    }
    
    // Check if evidence has a file
    if (!evidence.filePath) {
      throw new Error('Evidence does not have a file');
    }
    
    // Check if file exists
    if (!fs.existsSync(evidence.filePath)) {
      throw new Error('Evidence file not found');
    }
    
    return {
      filePath: evidence.filePath,
      fileType: evidence.fileType,
      fileName: path.basename(evidence.filePath)
    };
  } catch (error) {
    logger.error(`Failed to get evidence file ${id}`, error);
    throw error;
  }
}

/**
 * Verify evidence
 * @param {string} id - Evidence ID
 * @returns {Promise<Object>} - Verification result
 */
async function verifyEvidence(id) {
  try {
    // Verify evidence
    const verification = await Evidence.verify(id);
    
    logger.info(`Evidence ${id} verification: ${verification.verified}`);
    
    return verification;
  } catch (error) {
    logger.error(`Failed to verify evidence ${id}`, error);
    throw error;
  }
}

/**
 * Collect API evidence
 * @param {Object} evidenceData - Evidence data
 * @param {string} userId - User ID
 * @returns {Promise<Object>} - Created evidence
 */
async function collectApiEvidence(evidenceData, userId) {
  try {
    // Validate control
    if (!evidenceData.controlId) {
      throw new Error('Control ID is required');
    }
    
    const control = await Control.findById(evidenceData.controlId);
    
    if (!control) {
      throw new Error(`Control ${evidenceData.controlId} not found`);
    }
    
    // Validate API URL
    if (!evidenceData.apiUrl) {
      throw new Error('API URL is required');
    }
    
    // Make API request
    const method = evidenceData.method || 'GET';
    const headers = evidenceData.headers || {};
    const body = evidenceData.body || null;
    
    let response;
    try {
      response = await axios({
        method,
        url: evidenceData.apiUrl,
        headers,
        data: body
      });
    } catch (error) {
      // Capture error response
      response = {
        status: error.response ? error.response.status : 500,
        statusText: error.response ? error.response.statusText : error.message,
        headers: error.response ? error.response.headers : {},
        data: error.response ? error.response.data : { error: error.message }
      };
    }
    
    // Create evidence content
    const content = JSON.stringify({
      request: {
        method,
        url: evidenceData.apiUrl,
        headers,
        body
      },
      response: {
        status: response.status,
        statusText: response.statusText,
        headers: response.headers,
        data: response.data
      },
      timestamp: new Date().toISOString()
    }, null, 2);
    
    // Create evidence
    const evidence = await Evidence.createFromContent({
      name: evidenceData.name,
      description: evidenceData.description || `API evidence from ${evidenceData.apiUrl}`,
      type: 'api-response',
      controlId: evidenceData.controlId,
      testExecutionId: evidenceData.testExecutionId,
      content,
      metadata: {
        apiUrl: evidenceData.apiUrl,
        method,
        status: response.status,
        timestamp: new Date().toISOString()
      }
    }, userId);
    
    logger.info(`API evidence ${evidence._id} collected by ${userId}`);
    
    return evidence;
  } catch (error) {
    logger.error('Failed to collect API evidence', error);
    throw error;
  }
}

/**
 * Collect system evidence
 * @param {Object} evidenceData - Evidence data
 * @param {string} userId - User ID
 * @returns {Promise<Object>} - Created evidence
 */
async function collectSystemEvidence(evidenceData, userId) {
  try {
    // Validate control
    if (!evidenceData.controlId) {
      throw new Error('Control ID is required');
    }
    
    const control = await Control.findById(evidenceData.controlId);
    
    if (!control) {
      throw new Error(`Control ${evidenceData.controlId} not found`);
    }
    
    // Validate system type
    if (!evidenceData.systemType) {
      throw new Error('System type is required');
    }
    
    // Collect system evidence
    let content = '';
    let metadata = {
      systemType: evidenceData.systemType,
      timestamp: new Date().toISOString()
    };
    
    switch (evidenceData.systemType) {
      case 'os':
        // Collect OS information
        if (process.platform === 'win32') {
          // Windows
          const { stdout: systemInfo } = await execPromise('systeminfo');
          content = systemInfo;
        } else {
          // Linux/Unix
          const { stdout: uname } = await execPromise('uname -a');
          const { stdout: osRelease } = await execPromise('cat /etc/os-release');
          content = `${uname}\n\n${osRelease}`;
        }
        break;
        
      case 'database':
        // Collect database information
        // This is a placeholder - in a real implementation, this would connect to a database
        content = JSON.stringify({
          type: 'database',
          query: evidenceData.query,
          result: 'Database evidence collection not implemented'
        }, null, 2);
        break;
        
      case 'application':
        // Collect application information
        // This is a placeholder - in a real implementation, this would collect application logs or configuration
        content = JSON.stringify({
          type: 'application',
          query: evidenceData.query,
          result: 'Application evidence collection not implemented'
        }, null, 2);
        break;
        
      case 'network':
        // Collect network information
        if (process.platform === 'win32') {
          // Windows
          const { stdout: ipconfig } = await execPromise('ipconfig /all');
          content = ipconfig;
        } else {
          // Linux/Unix
          const { stdout: ifconfig } = await execPromise('ifconfig');
          content = ifconfig;
        }
        break;
        
      case 'cloud':
        // Collect cloud information
        // This is a placeholder - in a real implementation, this would connect to cloud APIs
        content = JSON.stringify({
          type: 'cloud',
          query: evidenceData.query,
          result: 'Cloud evidence collection not implemented'
        }, null, 2);
        break;
        
      default:
        throw new Error(`Unsupported system type: ${evidenceData.systemType}`);
    }
    
    // Create evidence
    const evidence = await Evidence.createFromContent({
      name: evidenceData.name,
      description: evidenceData.description || `System evidence from ${evidenceData.systemType}`,
      type: 'log',
      controlId: evidenceData.controlId,
      testExecutionId: evidenceData.testExecutionId,
      content,
      metadata
    }, userId);
    
    logger.info(`System evidence ${evidence._id} collected by ${userId}`);
    
    return evidence;
  } catch (error) {
    logger.error('Failed to collect system evidence', error);
    throw error;
  }
}

module.exports = {
  getAllEvidence,
  getEvidenceById,
  createEvidenceFromFile,
  createEvidenceFromContent,
  updateEvidence,
  deleteEvidence,
  getEvidenceFile,
  verifyEvidence,
  collectApiEvidence,
  collectSystemEvidence
};
