/**
 * Consent Controller Tests
 *
 * This file contains unit tests for the Consent controller.
 */

// Mock the models module before importing the controller
jest.mock('../../models', () => {
  // Create mock instances
  const mockConsentRecord = {
    _id: 'mock-consent-id-1',
    dataSubjectName: '<PERSON>',
    dataSubjectEmail: '<EMAIL>',
    consentType: 'marketing',
    status: 'active',
    expiryDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
    isExpired: false,
    timeRemaining: 30,
    save: jest.fn().mockResolvedValue({ _id: 'mock-consent-id-1' }),
    toJSON: jest.fn().mockReturnValue({ _id: 'mock-consent-id-1' })
  };

  // Create mock constructor
  const MockConsentRecord = jest.fn(() => mockConsentRecord);

  // Add static methods
  MockConsentRecord.find = jest.fn().mockReturnValue({
    sort: jest.fn().mockReturnThis(),
    skip: jest.fn().mockReturnThis(),
    limit: jest.fn().mockResolvedValue([mockConsentRecord])
  });

  MockConsentRecord.findById = jest.fn().mockResolvedValue(mockConsentRecord);
  MockConsentRecord.countDocuments = jest.fn().mockResolvedValue(1);

  return {
    ConsentRecord: MockConsentRecord
  };
});

// Now import the controller and mocked model
const { consentController } = require('../../controllers');
const { ConsentRecord } = require('../../models');

describe('Consent Controller', () => {
  let req, res, next;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Setup request, response and next function
    req = {
      params: {},
      query: {},
      body: {}
    };

    res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    };

    next = jest.fn();
  });

  describe('getAllConsentRecords', () => {
    it('should return all consent records with pagination', async () => {
      // Setup request query
      req.query = {
        page: '1',
        limit: '10'
      };

      // Call the controller method
      await consentController.getAllConsentRecords(req, res, next);

      // Verify the model methods were called correctly
      expect(ConsentRecord.find).toHaveBeenCalled();
      expect(ConsentRecord.countDocuments).toHaveBeenCalled();

      // Verify the response
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.any(Array),
        pagination: expect.objectContaining({
          total: 1,
          page: 1,
          limit: 10,
          pages: 1
        })
      }));
    });

    it('should use default pagination values if not provided', async () => {
      // Setup empty request query
      req.query = {};

      // Call the controller method
      await consentController.getAllConsentRecords(req, res, next);

      // Verify the model methods were called with default pagination
      expect(ConsentRecord.find().skip).toHaveBeenCalledWith(0);
      expect(ConsentRecord.find().limit).toHaveBeenCalledWith(10);
    });

    it('should handle filtering by status', async () => {
      // Setup request query with status filter
      req.query = {
        page: '1',
        limit: '10',
        status: 'active'
      };

      // Call the controller method
      await consentController.getAllConsentRecords(req, res, next);

      // Verify the model methods were called with the correct filter
      expect(ConsentRecord.find).toHaveBeenCalledWith(expect.objectContaining({
        status: 'active'
      }));
    });

    it('should handle filtering by consentType', async () => {
      // Setup request query with consentType filter
      req.query = {
        page: '1',
        limit: '10',
        consentType: 'marketing'
      };

      // Call the controller method
      await consentController.getAllConsentRecords(req, res, next);

      // Verify the model methods were called with the correct filter
      expect(ConsentRecord.find).toHaveBeenCalledWith(expect.objectContaining({
        consentType: 'marketing'
      }));
    });

    it('should handle filtering by dataSubjectId', async () => {
      // Setup request query with dataSubjectId filter
      req.query = {
        page: '1',
        limit: '10',
        dataSubjectId: 'ds-123'
      };

      // Call the controller method
      await consentController.getAllConsentRecords(req, res, next);

      // Verify the model methods were called with the correct filter
      expect(ConsentRecord.find).toHaveBeenCalledWith(expect.objectContaining({
        dataSubjectId: 'ds-123'
      }));
    });

    it('should handle filtering by email', async () => {
      // Setup request query with email filter
      req.query = {
        page: '1',
        limit: '10',
        email: '<EMAIL>'
      };

      // Call the controller method
      await consentController.getAllConsentRecords(req, res, next);

      // Verify the model methods were called with the correct filter
      expect(ConsentRecord.find).toHaveBeenCalledWith(expect.objectContaining({
        dataSubjectEmail: '<EMAIL>'
      }));
    });

    it('should handle filtering by search term', async () => {
      // Setup request query with search filter
      req.query = {
        page: '1',
        limit: '10',
        search: 'marketing'
      };

      // Call the controller method
      await consentController.getAllConsentRecords(req, res, next);

      // Verify the model methods were called with the correct filter
      expect(ConsentRecord.find).toHaveBeenCalledWith(expect.objectContaining({
        $text: { $search: 'marketing' }
      }));
    });

    it('should handle custom sorting', async () => {
      // Setup request query with sorting parameters
      req.query = {
        page: '1',
        limit: '10',
        sortBy: 'status',
        sortOrder: 'asc'
      };

      // Call the controller method
      await consentController.getAllConsentRecords(req, res, next);

      // Verify the sort method was called with the correct parameters
      expect(ConsentRecord.find().sort).toHaveBeenCalledWith({ status: 1 });
    });

    it('should handle descending sort order', async () => {
      // Setup request query with sorting parameters
      req.query = {
        page: '1',
        limit: '10',
        sortBy: 'createdAt',
        sortOrder: 'desc'
      };

      // Call the controller method
      await consentController.getAllConsentRecords(req, res, next);

      // Verify the sort method was called with the correct parameters
      expect(ConsentRecord.find().sort).toHaveBeenCalledWith({ createdAt: -1 });
    });

    it('should use default sort order when sortOrder is not specified', async () => {
      // Setup request query with only sortBy parameter
      req.query = {
        page: '1',
        limit: '10',
        sortBy: 'status'
      };

      // Call the controller method
      await consentController.getAllConsentRecords(req, res, next);

      // Verify the sort method was called with the correct parameters (ascending by default)
      expect(ConsentRecord.find().sort).toHaveBeenCalledWith({ status: 1 });
    });

    it('should use default sort when sortBy is not specified', async () => {
      // Setup request query without sort parameters
      req.query = {
        page: '1',
        limit: '10'
      };

      // Call the controller method
      await consentController.getAllConsentRecords(req, res, next);

      // Verify the sort method was called with the default sort (createdAt descending)
      expect(ConsentRecord.find().sort).toHaveBeenCalledWith({ createdAt: -1 });
    });

    it('should handle errors', async () => {
      // Setup error
      const error = new Error('Database error');
      ConsentRecord.find.mockImplementationOnce(() => {
        throw error;
      });

      // Call the controller method
      await consentController.getAllConsentRecords(req, res, next);

      // Verify error handling
      expect(next).toHaveBeenCalledWith(error);
    });
  });

  describe('getConsentRecordById', () => {
    it('should return a consent record by ID', async () => {
      // Setup request params
      req.params.id = 'mock-consent-id-1';

      // Call the controller method
      await consentController.getConsentRecordById(req, res, next);

      // Verify the model methods were called correctly
      expect(ConsentRecord.findById).toHaveBeenCalledWith('mock-consent-id-1');

      // Verify the response
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({ _id: 'mock-consent-id-1' })
      }));
    });

    it('should return 404 if the consent record is not found', async () => {
      // Setup request params
      req.params.id = 'non-existent-id';

      // Setup model to return null
      ConsentRecord.findById.mockResolvedValueOnce(null);

      // Call the controller method
      await consentController.getConsentRecordById(req, res, next);

      // Verify error handling
      expect(next).toHaveBeenCalledWith(expect.objectContaining({
        name: 'NotFoundError',
        message: 'Consent record not found'
      }));
    });

    it('should handle errors', async () => {
      // Setup request params
      req.params.id = 'mock-consent-id-1';

      // Setup error
      const error = new Error('Database error');
      ConsentRecord.findById.mockRejectedValueOnce(error);

      // Call the controller method
      await consentController.getConsentRecordById(req, res, next);

      // Verify error handling
      expect(next).toHaveBeenCalledWith(error);
    });
  });

  describe('createConsentRecord', () => {
    it('should create a new consent record', async () => {
      // Setup request body
      req.body = {
        dataSubjectName: 'John Doe',
        dataSubjectEmail: '<EMAIL>',
        consentType: 'marketing',
        consentDetails: 'Marketing emails',
        purposes: ['newsletters', 'promotions'],
        dataCategories: ['contact_info', 'preferences'],
        collectionMethod: 'web-form',
        collectionTimestamp: new Date()
      };

      // Call the controller method
      await consentController.createConsentRecord(req, res, next);

      // Verify the model constructor was called with the correct data
      expect(ConsentRecord).toHaveBeenCalledWith(req.body);

      // Verify save was called
      expect(ConsentRecord().save).toHaveBeenCalled();

      // Verify the response
      expect(res.status).toHaveBeenCalledWith(201);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.anything(),
        message: 'Consent record created successfully'
      }));
    });

    it('should handle errors', async () => {
      // Setup request body
      req.body = {
        dataSubjectName: 'John Doe',
        dataSubjectEmail: '<EMAIL>',
        consentType: 'marketing',
        consentDetails: 'Marketing emails',
        purposes: ['newsletters', 'promotions'],
        dataCategories: ['contact_info', 'preferences'],
        collectionMethod: 'web-form',
        collectionTimestamp: new Date()
      };

      // Setup error
      const error = new Error('Database error');
      ConsentRecord().save.mockRejectedValueOnce(error);

      // Call the controller method
      await consentController.createConsentRecord(req, res, next);

      // Verify error handling
      expect(next).toHaveBeenCalledWith(error);
    });
  });

  describe('updateConsentRecord', () => {
    it('should update a consent record', async () => {
      // Setup request params and body
      req.params.id = 'mock-consent-id-1';
      req.body = {
        status: 'active',
        consentDetails: 'Updated marketing emails consent'
      };

      // Setup mock consent record object with properties to be updated
      const mockConsentRecord = {
        _id: 'mock-consent-id-1',
        status: 'pending',
        consentDetails: 'Marketing emails',
        save: jest.fn().mockResolvedValue({
          _id: 'mock-consent-id-1',
          status: 'active',
          consentDetails: 'Updated marketing emails consent'
        })
      };

      // Override the findById mock for this test
      ConsentRecord.findById.mockResolvedValueOnce(mockConsentRecord);

      // Call the controller method
      await consentController.updateConsentRecord(req, res, next);

      // Verify the model methods were called correctly
      expect(ConsentRecord.findById).toHaveBeenCalledWith('mock-consent-id-1');

      // Verify the consent record was updated correctly
      expect(mockConsentRecord.status).toBe('active');
      expect(mockConsentRecord.consentDetails).toBe('Updated marketing emails consent');

      // Verify save was called
      expect(mockConsentRecord.save).toHaveBeenCalled();

      // Verify the response
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.anything(),
        message: 'Consent record updated successfully'
      }));
    });

    it('should return 404 if the consent record is not found', async () => {
      // Setup request params and body
      req.params.id = 'non-existent-id';
      req.body = {
        status: 'active'
      };

      // Setup model to return null
      ConsentRecord.findById.mockResolvedValueOnce(null);

      // Call the controller method
      await consentController.updateConsentRecord(req, res, next);

      // Verify error handling
      expect(next).toHaveBeenCalledWith(expect.objectContaining({
        name: 'NotFoundError',
        message: 'Consent record not found'
      }));
    });

    it('should handle errors', async () => {
      // Setup request params and body
      req.params.id = 'mock-consent-id-1';
      req.body = {
        status: 'active'
      };

      // Setup error
      const error = new Error('Database error');
      ConsentRecord.findById.mockRejectedValueOnce(error);

      // Call the controller method
      await consentController.updateConsentRecord(req, res, next);

      // Verify error handling
      expect(next).toHaveBeenCalledWith(error);
    });
  });

  describe('withdrawConsent', () => {
    it('should withdraw consent', async () => {
      // Setup request params and body
      req.params.id = 'mock-consent-id-1';
      req.body = {
        withdrawalMethod: 'web-form',
        withdrawalReason: 'No longer interested'
      };

      // Setup mock consent record object
      const mockConsentRecord = {
        _id: 'mock-consent-id-1',
        status: 'active',
        withdrawalTimestamp: null,
        withdrawalMethod: null,
        withdrawalReason: null,
        save: jest.fn().mockResolvedValue({
          _id: 'mock-consent-id-1',
          status: 'withdrawn',
          withdrawalTimestamp: expect.any(Date),
          withdrawalMethod: 'web-form',
          withdrawalReason: 'No longer interested'
        })
      };

      // Override the findById mock for this test
      ConsentRecord.findById.mockResolvedValueOnce(mockConsentRecord);

      // Call the controller method
      await consentController.withdrawConsent(req, res, next);

      // Verify the model methods were called correctly
      expect(ConsentRecord.findById).toHaveBeenCalledWith('mock-consent-id-1');

      // Verify the consent record was updated correctly
      expect(mockConsentRecord.status).toBe('withdrawn');
      expect(mockConsentRecord.withdrawalTimestamp).toBeInstanceOf(Date);
      expect(mockConsentRecord.withdrawalMethod).toBe('web-form');
      expect(mockConsentRecord.withdrawalReason).toBe('No longer interested');

      // Verify save was called
      expect(mockConsentRecord.save).toHaveBeenCalled();

      // Verify the response
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.anything(),
        message: 'Consent withdrawn successfully'
      }));
    });

    it('should return 404 if the consent record is not found', async () => {
      // Setup request params and body
      req.params.id = 'non-existent-id';
      req.body = {
        withdrawalMethod: 'web-form',
        withdrawalReason: 'No longer interested'
      };

      // Setup model to return null
      ConsentRecord.findById.mockResolvedValueOnce(null);

      // Call the controller method
      await consentController.withdrawConsent(req, res, next);

      // Verify error handling
      expect(next).toHaveBeenCalledWith(expect.objectContaining({
        name: 'NotFoundError',
        message: 'Consent record not found'
      }));
    });

    it('should return validation error if consent is already withdrawn', async () => {
      // Setup request params and body
      req.params.id = 'mock-consent-id-1';
      req.body = {
        withdrawalMethod: 'web-form',
        withdrawalReason: 'No longer interested'
      };

      // Setup mock consent record object that is already withdrawn
      const mockConsentRecord = {
        _id: 'mock-consent-id-1',
        status: 'withdrawn',
        withdrawalTimestamp: new Date(),
        withdrawalMethod: 'email',
        withdrawalReason: 'Previous withdrawal'
      };

      // Override the findById mock for this test
      ConsentRecord.findById.mockResolvedValueOnce(mockConsentRecord);

      // Call the controller method
      await consentController.withdrawConsent(req, res, next);

      // Verify error handling
      expect(next).toHaveBeenCalledWith(expect.objectContaining({
        name: 'ValidationError',
        message: 'Consent has already been withdrawn'
      }));
    });

    it('should handle errors', async () => {
      // Setup request params and body
      req.params.id = 'mock-consent-id-1';
      req.body = {
        withdrawalMethod: 'web-form',
        withdrawalReason: 'No longer interested'
      };

      // Setup error
      const error = new Error('Database error');
      ConsentRecord.findById.mockRejectedValueOnce(error);

      // Call the controller method
      await consentController.withdrawConsent(req, res, next);

      // Verify error handling
      expect(next).toHaveBeenCalledWith(error);
    });
  });

  describe('verifyConsentValidity', () => {
    it('should verify a valid consent record', async () => {
      // Setup request params
      req.params.id = 'mock-consent-id-1';

      // Setup mock consent record object
      const mockConsentRecord = {
        _id: 'mock-consent-id-1',
        status: 'active',
        expiryDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        isExpired: false,
        timeRemaining: 30
      };

      // Override the findById mock for this test
      ConsentRecord.findById.mockResolvedValueOnce(mockConsentRecord);

      // Call the controller method
      await consentController.verifyConsentValidity(req, res, next);

      // Verify the model methods were called correctly
      expect(ConsentRecord.findById).toHaveBeenCalledWith('mock-consent-id-1');

      // Verify the response
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({
          id: 'mock-consent-id-1',
          isValid: true,
          status: 'active',
          expiryDate: expect.any(Date),
          isExpired: false,
          timeRemaining: 30
        }),
        message: 'Consent is valid'
      }));
    });

    it('should verify a valid consent record with no expiry date', async () => {
      // Setup request params
      req.params.id = 'mock-consent-id-1';

      // Setup mock consent record object with no expiry date
      const mockConsentRecord = {
        _id: 'mock-consent-id-1',
        status: 'active',
        expiryDate: null,
        isExpired: false,
        timeRemaining: null
      };

      // Override the findById mock for this test
      ConsentRecord.findById.mockResolvedValueOnce(mockConsentRecord);

      // Call the controller method
      await consentController.verifyConsentValidity(req, res, next);

      // Verify the model methods were called correctly
      expect(ConsentRecord.findById).toHaveBeenCalledWith('mock-consent-id-1');

      // Verify the response
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({
          id: 'mock-consent-id-1',
          isValid: true,
          status: 'active',
          expiryDate: null,
          isExpired: false,
          timeRemaining: null
        }),
        message: 'Consent is valid'
      }));
    });

    it('should verify an invalid consent record', async () => {
      // Setup request params
      req.params.id = 'mock-consent-id-1';

      // Setup mock consent record object
      const mockConsentRecord = {
        _id: 'mock-consent-id-1',
        status: 'withdrawn',
        expiryDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
        isExpired: true,
        timeRemaining: 0
      };

      // Override the findById mock for this test
      ConsentRecord.findById.mockResolvedValueOnce(mockConsentRecord);

      // Call the controller method
      await consentController.verifyConsentValidity(req, res, next);

      // Verify the model methods were called correctly
      expect(ConsentRecord.findById).toHaveBeenCalledWith('mock-consent-id-1');

      // Verify the response
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({
          id: 'mock-consent-id-1',
          isValid: false,
          status: 'withdrawn',
          expiryDate: expect.any(Date),
          isExpired: true,
          timeRemaining: 0
        }),
        message: 'Consent is not valid'
      }));
    });

    it('should return 404 if the consent record is not found', async () => {
      // Setup request params
      req.params.id = 'non-existent-id';

      // Setup model to return null
      ConsentRecord.findById.mockResolvedValueOnce(null);

      // Call the controller method
      await consentController.verifyConsentValidity(req, res, next);

      // Verify error handling
      expect(next).toHaveBeenCalledWith(expect.objectContaining({
        name: 'NotFoundError',
        message: 'Consent record not found'
      }));
    });

    it('should handle errors', async () => {
      // Setup request params
      req.params.id = 'mock-consent-id-1';

      // Setup error
      const error = new Error('Database error');
      ConsentRecord.findById.mockRejectedValueOnce(error);

      // Call the controller method
      await consentController.verifyConsentValidity(req, res, next);

      // Verify error handling
      expect(next).toHaveBeenCalledWith(error);
    });
  });

  describe('getConsentRecordsByDataSubject', () => {
    it('should get consent records by data subject ID', async () => {
      // Setup request params
      req.params.dataSubjectId = 'ds-123';

      // Setup mock consent records
      const mockConsentRecords = [
        { _id: 'mock-consent-id-1', dataSubjectId: 'ds-123', consentType: 'marketing' },
        { _id: 'mock-consent-id-2', dataSubjectId: 'ds-123', consentType: 'analytics' }
      ];

      // Override the find mock for this test
      ConsentRecord.find.mockReturnValueOnce(mockConsentRecords);

      // Call the controller method
      await consentController.getConsentRecordsByDataSubject(req, res, next);

      // Verify the model methods were called correctly
      expect(ConsentRecord.find).toHaveBeenCalledWith({ dataSubjectId: 'ds-123' });

      // Verify the response
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: mockConsentRecords
      }));
    });

    it('should handle errors', async () => {
      // Setup request params
      req.params.dataSubjectId = 'ds-123';

      // Setup error
      const error = new Error('Database error');
      ConsentRecord.find.mockImplementationOnce(() => {
        throw error;
      });

      // Call the controller method
      await consentController.getConsentRecordsByDataSubject(req, res, next);

      // Verify error handling
      expect(next).toHaveBeenCalledWith(error);
    });
  });

  describe('getConsentRecordsByEmail', () => {
    it('should get consent records by email', async () => {
      // Setup request params
      req.params.email = '<EMAIL>';

      // Setup mock consent records
      const mockConsentRecords = [
        { _id: 'mock-consent-id-1', dataSubjectEmail: '<EMAIL>', consentType: 'marketing' },
        { _id: 'mock-consent-id-2', dataSubjectEmail: '<EMAIL>', consentType: 'analytics' }
      ];

      // Override the find mock for this test
      ConsentRecord.find.mockReturnValueOnce(mockConsentRecords);

      // Call the controller method
      await consentController.getConsentRecordsByEmail(req, res, next);

      // Verify the model methods were called correctly
      expect(ConsentRecord.find).toHaveBeenCalledWith({ dataSubjectEmail: '<EMAIL>' });

      // Verify the response
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: mockConsentRecords
      }));
    });

    it('should handle errors', async () => {
      // Setup request params
      req.params.email = '<EMAIL>';

      // Setup error
      const error = new Error('Database error');
      ConsentRecord.find.mockImplementationOnce(() => {
        throw error;
      });

      // Call the controller method
      await consentController.getConsentRecordsByEmail(req, res, next);

      // Verify error handling
      expect(next).toHaveBeenCalledWith(error);
    });
  });

  describe('generateConsentForm', () => {
    it('should generate a consent form', async () => {
      // Setup request query
      req.query.consentType = 'marketing';

      // Call the controller method
      await consentController.generateConsentForm(req, res, next);

      // Verify the response
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({
          title: 'Marketing Consent Form',
          content: expect.stringContaining('marketing'),
          version: expect.any(String),
          effectiveDate: expect.any(Date),
          purposes: expect.any(Array),
          dataCategories: expect.any(Array),
          thirdParties: expect.any(Array),
          contactInformation: expect.any(Object)
        }),
        message: 'Consent form generated successfully'
      }));
    });

    it('should return validation error if consent type is not provided', async () => {
      // Setup request query without consent type
      req.query = {};

      // Call the controller method
      await consentController.generateConsentForm(req, res, next);

      // Verify error handling
      expect(next).toHaveBeenCalledWith(expect.objectContaining({
        name: 'ValidationError',
        message: 'Consent type is required'
      }));
    });

    it('should handle errors', async () => {
      // Setup request query
      req.query.consentType = 'marketing';

      // Setup error handler to simulate an error
      next.mockImplementationOnce((error) => {
        expect(error).toBeDefined();
        expect(error instanceof Error).toBe(true);
      });

      // Mock res.json to throw an error
      res.json.mockImplementationOnce(() => {
        throw new Error('Unexpected error');
      });

      // Call the controller method
      await consentController.generateConsentForm(req, res, next);

      // Verify error handling
      expect(next).toHaveBeenCalled();
    });
  });

  describe('verifyConsentProof', () => {
    it('should verify a consent proof', async () => {
      // Setup request body
      req.body = {
        consentProof: 'valid-consent-proof-123'
      };

      // Call the controller method
      await consentController.verifyConsentProof(req, res, next);

      // Verify the response
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({
          isValid: true,
          consentId: expect.any(String),
          verificationTimestamp: expect.any(Date)
        }),
        message: 'Consent proof verified successfully'
      }));
    });

    it('should return validation error if consent proof is not provided', async () => {
      // Setup request body without consent proof
      req.body = {};

      // Call the controller method
      await consentController.verifyConsentProof(req, res, next);

      // Verify error handling
      expect(next).toHaveBeenCalledWith(expect.objectContaining({
        name: 'ValidationError',
        message: 'Consent proof is required'
      }));
    });

    it('should handle errors', async () => {
      // Setup request body
      req.body = {
        consentProof: 'valid-consent-proof-123'
      };

      // Setup error handler to simulate an error
      next.mockImplementationOnce((error) => {
        expect(error).toBeDefined();
        expect(error instanceof Error).toBe(true);
      });

      // Mock res.json to throw an error
      res.json.mockImplementationOnce(() => {
        throw new Error('Unexpected error');
      });

      // Call the controller method
      await consentController.verifyConsentProof(req, res, next);

      // Verify error handling
      expect(next).toHaveBeenCalled();
    });
  });

  describe('generateConsentProof', () => {
    it('should generate a consent proof', async () => {
      // Setup request body
      req.body = {
        consentId: 'mock-consent-id-1',
        proofType: 'form-submission'
      };

      // Call the controller method
      await consentController.generateConsentProof(req, res, next);

      // Verify the model methods were called correctly
      expect(ConsentRecord.findById).toHaveBeenCalledWith('mock-consent-id-1');

      // Verify the response
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({
          consentId: 'mock-consent-id-1',
          proofType: 'form-submission',
          proof: expect.stringMatching(/^proof-\d+$/),
          generationTimestamp: expect.any(Date)
        }),
        message: 'Consent proof generated successfully'
      }));
    });

    it('should return validation error if consent ID is not provided', async () => {
      // Setup request body without consent ID
      req.body = {
        proofType: 'form-submission'
      };

      // Call the controller method
      await consentController.generateConsentProof(req, res, next);

      // Verify error handling
      expect(next).toHaveBeenCalledWith(expect.objectContaining({
        name: 'ValidationError',
        message: 'Consent ID is required'
      }));
    });

    it('should return validation error if proof type is not provided', async () => {
      // Setup request body without proof type
      req.body = {
        consentId: 'mock-consent-id-1'
      };

      // Call the controller method
      await consentController.generateConsentProof(req, res, next);

      // Verify error handling
      expect(next).toHaveBeenCalledWith(expect.objectContaining({
        name: 'ValidationError',
        message: 'Proof type is required'
      }));
    });

    it('should return 404 if the consent record is not found', async () => {
      // Setup request body
      req.body = {
        consentId: 'non-existent-id',
        proofType: 'form-submission'
      };

      // Setup model to return null
      ConsentRecord.findById.mockResolvedValueOnce(null);

      // Call the controller method
      await consentController.generateConsentProof(req, res, next);

      // Verify error handling
      expect(next).toHaveBeenCalledWith(expect.objectContaining({
        name: 'NotFoundError',
        message: 'Consent record not found'
      }));
    });

    it('should handle errors', async () => {
      // Setup request body
      req.body = {
        consentId: 'mock-consent-id-1',
        proofType: 'form-submission'
      };

      // Setup error
      const error = new Error('Database error');
      ConsentRecord.findById.mockRejectedValueOnce(error);

      // Call the controller method
      await consentController.generateConsentProof(req, res, next);

      // Verify error handling
      expect(next).toHaveBeenCalledWith(error);
    });
  });
});
