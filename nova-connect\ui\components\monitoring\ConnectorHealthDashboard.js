/**
 * Connector Health Dashboard Component
 * 
 * This component displays the health status of all connectors.
 */

import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Button, 
  Card, 
  CardContent, 
  Chip, 
  CircularProgress, 
  Divider, 
  Grid, 
  IconButton, 
  Paper, 
  Table, 
  TableBody, 
  TableCell, 
  TableContainer, 
  TableHead, 
  TableRow, 
  Tooltip, 
  Typography 
} from '@mui/material';
import RefreshIcon from '@mui/icons-material/Refresh';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import WarningIcon from '@mui/icons-material/Warning';
import VisibilityIcon from '@mui/icons-material/Visibility';
import { useRouter } from 'next/router';
import HealthStatusChart from './HealthStatusChart';
import ResponseTimeChart from './ResponseTimeChart';

const ConnectorHealthDashboard = ({ connectors }) => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [healthData, setHealthData] = useState([]);
  const [lastUpdated, setLastUpdated] = useState(new Date());
  
  useEffect(() => {
    fetchHealthData();
  }, [connectors]);
  
  const fetchHealthData = async () => {
    setLoading(true);
    
    try {
      // In a real implementation, this would call an API to get health data
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Generate mock health data
      const mockHealthData = connectors.map(connector => {
        // Randomly generate health status with 80% chance of healthy
        const statusRandom = Math.random();
        let status;
        if (statusRandom > 0.9) {
          status = 'error';
        } else if (statusRandom > 0.8) {
          status = 'warning';
        } else {
          status = 'healthy';
        }
        
        // Generate random response time between 100ms and 1000ms
        const responseTime = Math.floor(Math.random() * 900) + 100;
        
        // Generate random success rate between 90% and 100% for healthy,
        // 70% and 90% for warning, and 0% and 70% for error
        let successRate;
        if (status === 'healthy') {
          successRate = Math.floor(Math.random() * 10) + 90;
        } else if (status === 'warning') {
          successRate = Math.floor(Math.random() * 20) + 70;
        } else {
          successRate = Math.floor(Math.random() * 70);
        }
        
        // Generate random error count based on success rate
        const requestCount = Math.floor(Math.random() * 1000) + 100;
        const errorCount = Math.floor(requestCount * (100 - successRate) / 100);
        
        // Generate random last check time within the last hour
        const lastCheck = new Date(Date.now() - Math.floor(Math.random() * 60 * 60 * 1000));
        
        // Generate random issues based on status
        const issues = [];
        if (status === 'error') {
          issues.push({
            type: 'connection',
            message: 'Connection timeout',
            timestamp: new Date(Date.now() - Math.floor(Math.random() * 30 * 60 * 1000))
          });
          issues.push({
            type: 'authentication',
            message: 'Authentication failed',
            timestamp: new Date(Date.now() - Math.floor(Math.random() * 45 * 60 * 1000))
          });
        } else if (status === 'warning') {
          issues.push({
            type: 'performance',
            message: 'High response time',
            timestamp: new Date(Date.now() - Math.floor(Math.random() * 20 * 60 * 1000))
          });
        }
        
        return {
          id: connector.id,
          name: connector.name,
          status,
          responseTime,
          successRate,
          requestCount,
          errorCount,
          lastCheck,
          issues,
          // Generate random historical data for charts
          history: {
            status: Array(24).fill(0).map((_, i) => {
              const hourRandom = Math.random();
              if (hourRandom > 0.95) {
                return 'error';
              } else if (hourRandom > 0.85) {
                return 'warning';
              } else {
                return 'healthy';
              }
            }),
            responseTimes: Array(24).fill(0).map(() => Math.floor(Math.random() * 900) + 100)
          }
        };
      });
      
      setHealthData(mockHealthData);
      setLastUpdated(new Date());
    } catch (error) {
      console.error('Error fetching health data:', error);
    } finally {
      setLoading(false);
    }
  };
  
  const handleRefresh = () => {
    fetchHealthData();
  };
  
  const handleViewDetails = (connectorId) => {
    router.push(`/monitoring/connectors/${connectorId}`);
  };
  
  const getStatusIcon = (status) => {
    switch (status) {
      case 'healthy':
        return <CheckCircleIcon color="success" />;
      case 'warning':
        return <WarningIcon color="warning" />;
      case 'error':
        return <ErrorIcon color="error" />;
      default:
        return null;
    }
  };
  
  const getStatusColor = (status) => {
    switch (status) {
      case 'healthy':
        return 'success';
      case 'warning':
        return 'warning';
      case 'error':
        return 'error';
      default:
        return 'default';
    }
  };
  
  const getStatusText = (status) => {
    switch (status) {
      case 'healthy':
        return 'Healthy';
      case 'warning':
        return 'Warning';
      case 'error':
        return 'Error';
      default:
        return 'Unknown';
    }
  };
  
  // Calculate summary statistics
  const healthySummary = healthData.filter(data => data.status === 'healthy').length;
  const warningSummary = healthData.filter(data => data.status === 'warning').length;
  const errorSummary = healthData.filter(data => data.status === 'error').length;
  const totalConnectors = healthData.length;
  
  return (
    <Box>
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h5" component="h2">
          Connector Health Dashboard
        </Typography>
        
        <Box>
          <Typography variant="body2" color="textSecondary" sx={{ mr: 2, display: 'inline' }}>
            Last updated: {lastUpdated.toLocaleTimeString()}
          </Typography>
          
          <Button
            variant="outlined"
            startIcon={loading ? <CircularProgress size={20} /> : <RefreshIcon />}
            onClick={handleRefresh}
            disabled={loading}
          >
            {loading ? 'Refreshing...' : 'Refresh'}
          </Button>
        </Box>
      </Box>
      
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h6" color="textSecondary" gutterBottom>
                Total Connectors
              </Typography>
              <Typography variant="h3">
                {totalConnectors}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ bgcolor: 'success.light' }}>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h6" color="textSecondary" gutterBottom>
                Healthy
              </Typography>
              <Typography variant="h3">
                {healthySummary}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                {totalConnectors > 0 ? `${Math.round(healthySummary / totalConnectors * 100)}%` : '0%'}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ bgcolor: 'warning.light' }}>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h6" color="textSecondary" gutterBottom>
                Warning
              </Typography>
              <Typography variant="h3">
                {warningSummary}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                {totalConnectors > 0 ? `${Math.round(warningSummary / totalConnectors * 100)}%` : '0%'}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ bgcolor: 'error.light' }}>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h6" color="textSecondary" gutterBottom>
                Error
              </Typography>
              <Typography variant="h3">
                {errorSummary}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                {totalConnectors > 0 ? `${Math.round(errorSummary / totalConnectors * 100)}%` : '0%'}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
      
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Health Status (Last 24 Hours)
              </Typography>
              <Box sx={{ height: 300 }}>
                <HealthStatusChart data={healthData} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Average Response Time (Last 24 Hours)
              </Typography>
              <Box sx={{ height: 300 }}>
                <ResponseTimeChart data={healthData} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
      
      <TableContainer component={Paper} variant="outlined">
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Connector</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Response Time</TableCell>
              <TableCell>Success Rate</TableCell>
              <TableCell>Last Check</TableCell>
              <TableCell>Issues</TableCell>
              <TableCell align="right">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {healthData.map(data => (
              <TableRow key={data.id} hover>
                <TableCell>
                  <Typography variant="subtitle2">{data.name}</Typography>
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    {getStatusIcon(data.status)}
                    <Typography variant="body2" sx={{ ml: 1 }}>
                      {getStatusText(data.status)}
                    </Typography>
                  </Box>
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {data.responseTime} ms
                  </Typography>
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Box
                      sx={{
                        width: 100,
                        mr: 1,
                        bgcolor: 'grey.300',
                        borderRadius: 1,
                        height: 8
                      }}
                    >
                      <Box
                        sx={{
                          width: `${data.successRate}%`,
                          bgcolor: data.successRate > 90 ? 'success.main' : data.successRate > 70 ? 'warning.main' : 'error.main',
                          borderRadius: 1,
                          height: 8
                        }}
                      />
                    </Box>
                    <Typography variant="body2">
                      {data.successRate}%
                    </Typography>
                  </Box>
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {data.lastCheck.toLocaleTimeString()}
                  </Typography>
                </TableCell>
                <TableCell>
                  {data.issues.length > 0 ? (
                    <Box>
                      {data.issues.map((issue, index) => (
                        <Chip
                          key={index}
                          label={issue.type}
                          size="small"
                          color={issue.type === 'connection' || issue.type === 'authentication' ? 'error' : 'warning'}
                          sx={{ mr: 0.5, mb: 0.5 }}
                        />
                      ))}
                    </Box>
                  ) : (
                    <Typography variant="body2" color="textSecondary">
                      No issues
                    </Typography>
                  )}
                </TableCell>
                <TableCell align="right">
                  <Tooltip title="View Details">
                    <IconButton
                      size="small"
                      onClick={() => handleViewDetails(data.id)}
                    >
                      <VisibilityIcon />
                    </IconButton>
                  </Tooltip>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
};

export default ConnectorHealthDashboard;
