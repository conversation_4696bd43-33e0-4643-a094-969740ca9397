/**
 * NovaCore Compliance Profile Controller
 * 
 * This controller handles API requests related to compliance profiles.
 * NovaPulse is the Universal Regulatory Compliance Monitoring System (URCMS) component
 * of the NovaFuse Cyber-Safety Platform.
 */

const { ComplianceProfileService } = require('../services');
const logger = require('../../../config/logger');

class ComplianceProfileController {
  /**
   * Create a new compliance profile
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async createComplianceProfile(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      
      // Set organization ID from params
      req.body.organizationId = req.params.organizationId;
      
      const complianceProfile = await ComplianceProfileService.createComplianceProfile(req.body, userId);
      
      res.status(201).json({
        success: true,
        data: complianceProfile
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Get all compliance profiles
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async getAllComplianceProfiles(req, res, next) {
    try {
      const organizationId = req.params.organizationId;
      
      // Extract filter criteria from query params
      const filter = {};
      
      if (req.query.name) filter.name = req.query.name;
      if (req.query.status) filter.status = req.query.status;
      if (req.query.industry) filter.industry = req.query.industry;
      if (req.query.size) filter.size = req.query.size;
      if (req.query.country) filter.country = req.query.country;
      if (req.query.region) filter.region = req.query.region;
      if (req.query.dataType) filter.dataType = req.query.dataType;
      if (req.query.classification) filter.classification = req.query.classification;
      if (req.query.frameworkId) filter.frameworkId = req.query.frameworkId;
      if (req.query.regulationId) filter.regulationId = req.query.regulationId;
      if (req.query.complianceStatus) filter.complianceStatus = req.query.complianceStatus;
      if (req.query.search) filter.search = req.query.search;
      
      // Extract pagination and sorting options
      const options = {
        page: parseInt(req.query.page, 10) || 1,
        limit: parseInt(req.query.limit, 10) || 10
      };
      
      if (req.query.sort) {
        const [field, order] = req.query.sort.split(':');
        options.sort = { [field]: order === 'desc' ? -1 : 1 };
      }
      
      const result = await ComplianceProfileService.getAllComplianceProfiles(organizationId, filter, options);
      
      res.status(200).json({
        success: true,
        data: result.data,
        pagination: result.pagination
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Get compliance profile by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async getComplianceProfileById(req, res, next) {
    try {
      const complianceProfile = await ComplianceProfileService.getComplianceProfileById(req.params.id);
      
      res.status(200).json({
        success: true,
        data: complianceProfile
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Update compliance profile
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async updateComplianceProfile(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const complianceProfile = await ComplianceProfileService.updateComplianceProfile(req.params.id, req.body, userId);
      
      res.status(200).json({
        success: true,
        data: complianceProfile
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Delete compliance profile
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async deleteComplianceProfile(req, res, next) {
    try {
      await ComplianceProfileService.deleteComplianceProfile(req.params.id);
      
      res.status(200).json({
        success: true,
        message: 'Compliance profile deleted successfully'
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Add applicable framework
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async addApplicableFramework(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const complianceProfile = await ComplianceProfileService.addApplicableFramework(req.params.id, req.body, userId);
      
      res.status(200).json({
        success: true,
        data: complianceProfile
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Update applicable framework
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async updateApplicableFramework(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const complianceProfile = await ComplianceProfileService.updateApplicableFramework(
        req.params.id, 
        req.params.frameworkId, 
        req.body, 
        userId
      );
      
      res.status(200).json({
        success: true,
        data: complianceProfile
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Remove applicable framework
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async removeApplicableFramework(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const complianceProfile = await ComplianceProfileService.removeApplicableFramework(
        req.params.id, 
        req.params.frameworkId, 
        userId
      );
      
      res.status(200).json({
        success: true,
        data: complianceProfile
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Add applicable regulation
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async addApplicableRegulation(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const complianceProfile = await ComplianceProfileService.addApplicableRegulation(req.params.id, req.body, userId);
      
      res.status(200).json({
        success: true,
        data: complianceProfile
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Add data inventory item
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async addDataInventoryItem(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const complianceProfile = await ComplianceProfileService.addDataInventoryItem(req.params.id, req.body, userId);
      
      res.status(200).json({
        success: true,
        data: complianceProfile
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Add business activity
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async addBusinessActivity(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const complianceProfile = await ComplianceProfileService.addBusinessActivity(req.params.id, req.body, userId);
      
      res.status(200).json({
        success: true,
        data: complianceProfile
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Update overall compliance status
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async updateOverallComplianceStatus(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const complianceProfile = await ComplianceProfileService.updateOverallComplianceStatus(req.params.id, userId);
      
      res.status(200).json({
        success: true,
        data: complianceProfile
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Find compliance profiles by organization
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async findByOrganization(req, res, next) {
    try {
      const organizationId = req.params.organizationId;
      const complianceProfiles = await ComplianceProfileService.findByOrganization(organizationId);
      
      res.status(200).json({
        success: true,
        data: complianceProfiles
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Find compliance profiles by framework
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async findByFramework(req, res, next) {
    try {
      const frameworkId = req.params.frameworkId;
      const complianceProfiles = await ComplianceProfileService.findByFramework(frameworkId);
      
      res.status(200).json({
        success: true,
        data: complianceProfiles
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Find compliance profiles by regulation
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async findByRegulation(req, res, next) {
    try {
      const regulationId = req.params.regulationId;
      const complianceProfiles = await ComplianceProfileService.findByRegulation(regulationId);
      
      res.status(200).json({
        success: true,
        data: complianceProfiles
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Find compliance profiles by compliance status
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async findByComplianceStatus(req, res, next) {
    try {
      const status = req.params.status;
      const complianceProfiles = await ComplianceProfileService.findByComplianceStatus(status);
      
      res.status(200).json({
        success: true,
        data: complianceProfiles
      });
    } catch (error) {
      next(error);
    }
  }
}

module.exports = new ComplianceProfileController();
