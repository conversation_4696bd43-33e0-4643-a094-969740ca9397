# Script to apply consistent template to all diagram HTML files
# This script updates CSS styles and ensures consistent formatting across all diagrams

# Get all HTML files in the current directory
$htmlFiles = Get-ChildItem -Path . -Filter "*.html"

# CSS Template to be applied to all files
$cssTemplate = @"
body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 20px;
    background-color: #f5f5f5;
}
.diagram-container {
    position: relative;
    width: 800px;
    height: 650px;
    margin: 0 auto;
    background-color: white;
    border: 1px solid #ddd;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
    box-sizing: border-box;
}
.container-box {
    position: absolute;
    border: 2px solid #666;
    border-radius: 10px;
    padding: 30px 10px 10px;
    box-sizing: border-box;
}
.container-label {
    position: absolute;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    font-weight: bold;
    font-size: 16px;
    text-align: center;
    width: 90%;
}
.component-box {
    position: absolute;
    background-color: white;
    border: 2px solid #333;
    border-radius: 6px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    padding: 10px;
    box-sizing: border-box;
    font-size: 14px;
    line-height: 1.2;
    z-index: 1;
}
.component-number {
    position: absolute;
    top: -10px;
    left: -10px;
    width: 20px;
    height: 20px;
    background-color: #555555; /* Grey color for patent compliance */
    color: white;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 12px;
    font-weight: bold;
}
.component-label {
    font-weight: bold;
    margin-bottom: 4px;
    text-align: center;
    width: 100%;
}
.legend {
    position: absolute;
    right: 10px;
    bottom: 10px;
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 5px;
    z-index: 10;
    width: 200px;
}
.legend-item {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
    font-size: 12px;
}
.legend-color {
    width: 15px;
    height: 15px;
    margin-right: 5px;
    border: 1px solid #333;
}
.inventor-label {
    position: absolute;
    left: 10px;
    bottom: 10px;
    font-size: 12px;
    font-style: italic;
    color: #333;
}
h1 {
    text-align: center;
    margin-bottom: 30px;
}
"@

# Process each HTML file
foreach ($file in $htmlFiles) {
    Write-Host "Processing $($file.Name)..."
    
    # Read the file content
    $content = Get-Content -Path $file.FullName -Raw
    
    # Skip the template script itself if it's in HTML format
    if ($file.Name -eq "apply-template.html") {
        Write-Host "Skipping template file..."
        continue
    }
    
    # Create a backup of the original file
    Copy-Item -Path $file.FullName -Destination "$($file.FullName).bak"
    
    # Replace CSS styles
    # First, find the style section
    if ($content -match "<style>(.*?)</style>") {
        $styleSection = $Matches[0]
        $newStyleSection = "<style>`n$cssTemplate`n    </style>"
        $content = $content -replace [regex]::Escape($styleSection), $newStyleSection
    }
    
    # Update arrow colors from #333 to #555555
    $content = $content -replace "border-color: transparent transparent transparent #333", "border-color: transparent transparent transparent #555555"
    $content = $content -replace "border-color: transparent #333 transparent transparent", "border-color: transparent #555555 transparent transparent"
    $content = $content -replace "border-color: #333 transparent transparent transparent", "border-color: #555555 transparent transparent transparent"
    
    # Update any stroke="#333" in SVG elements
    $content = $content -replace 'stroke="#333"', 'stroke="#555555"'
    
    # Update any fill="#333" in SVG elements
    $content = $content -replace 'fill="#333"', 'fill="#555555"'
    
    # Ensure inventor label is positioned correctly
    if ($content -match '<div class="inventor-label">') {
        if (!($content -match '<div class="inventor-label"[^>]*style="[^"]*position: absolute[^"]*"')) {
            $content = $content -replace '<div class="inventor-label">', '<div class="inventor-label" style="position: absolute; left: 10px; bottom: 10px; font-size: 12px; font-style: italic; color: #333;">'
        }
    } else {
        # Add inventor label if missing
        $content = $content -replace '</div>\s*</body>', '    <div class="inventor-label">Inventor: David Nigel Irvin</div>
</div>
</body>'
    }
    
    # Ensure legend is positioned correctly
    if ($content -match '<div class="legend">') {
        if (!($content -match '<div class="legend"[^>]*style="[^"]*position: absolute[^"]*"')) {
            $content = $content -replace '<div class="legend">', '<div class="legend" style="position: absolute; right: 10px; bottom: 10px; background-color: white; border: 1px solid #ddd; border-radius: 4px; padding: 5px; z-index: 10; width: 200px;">'
        }
    }
    
    # Write the updated content back to the file
    Set-Content -Path $file.FullName -Value $content
    
    Write-Host "Updated $($file.Name) successfully."
}

Write-Host "All diagram files have been updated with the consistent template."
