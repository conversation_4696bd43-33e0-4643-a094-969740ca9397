'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { motion } from 'framer-motion'
import { 
  DollarSign, 
  Heart, 
  Globe, 
  Rocket, 
  Users, 
  Brain, 
  Shield, 
  Zap,
  TrendingUp,
  Activity
} from 'lucide-react'

interface Studio {
  id: string
  name: string
  icon: React.ReactNode
  description: string
  revenue: string
  status: 'operational' | 'development' | 'planned'
  phase: number
  patents: string[]
  metrics: {
    label: string
    value: string
  }[]
}

export default function StudiosPage() {
  const [totalRevenue, setTotalRevenue] = useState(127.3)
  const [activeStudio, setActiveStudio] = useState<string | null>(null)

  const studios: Studio[] = [
    {
      id: 'financial',
      name: 'Financial Reality Studio',
      icon: <DollarSign className="w-12 h-12" />,
      description: 'Market manipulation through consciousness arbitrage. Live Ψ-field trading terminals with multiverse backtesting and SEC-proof algorithms.',
      revenue: '$47.3B',
      status: 'operational',
      phase: 1,
      patents: ['US2025NHETX-FIN', 'Volatility Surface Consciousness Mapping'],
      metrics: [
        { label: 'Hedge Fund Profits', value: '20% share' },
        { label: 'Central Bank Licensing', value: '$10M/year' },
        { label: 'Daily Arbitrage', value: '$2.8M+' }
      ]
    },
    {
      id: 'medical',
      name: 'Medical Reality Studio',
      icon: <Heart className="w-12 h-12" />,
      description: 'Disease eradication and longevity programming. Cancer deletion via NEPI truth optimization, protein folding correction, and aging reversal.',
      revenue: '$35.7B',
      status: 'operational',
      phase: 1,
      patents: ['Biological Consciousness Field Interfacing', 'Protein Folding Consciousness'],
      metrics: [
        { label: 'Big Pharma Revenue', value: '$10B/year' },
        { label: 'Hospital Diagnostics', value: '$1M/month' },
        { label: 'Success Rate', value: '97.3%' }
      ]
    },
    {
      id: 'ai-alignment',
      name: 'AI Alignment Studio',
      icon: <Brain className="w-12 h-12" />,
      description: 'Consciousness-based AI alignment and safety. AGI consciousness programming, AI ethics enforcement, and superintelligence consciousness control.',
      revenue: '$89.2B',
      status: 'operational',
      phase: 1,
      patents: ['AI Consciousness Alignment Protocol', 'Superintelligence Safety Framework'],
      metrics: [
        { label: 'AGI Partnerships', value: '$50B/year' },
        { label: 'Safety Contracts', value: '$25B/year' },
        { label: 'Alignment Success', value: '99.7%' }
      ]
    },
    {
      id: 'climate',
      name: 'Climate Reality Studio',
      icon: <Globe className="w-12 h-12" />,
      description: 'Weather control and disaster prevention. Hurricane path rewriting, CO2 Ψ-absorption grids, and drought-to-rainfall conversion algorithms.',
      revenue: '$100B',
      status: 'development',
      phase: 2,
      patents: ['Atmospheric Ψ-Field Programming', 'Weather Consciousness Manipulation'],
      metrics: [
        { label: 'Climate Bonds', value: '$100B/year' },
        { label: 'Carbon Credits', value: 'Arbitrage' },
        { label: 'Disaster Prevention', value: '89% success' }
      ]
    },
    {
      id: 'cosmology',
      name: 'Cosmology Reality Studio',
      icon: <Rocket className="w-12 h-12" />,
      description: 'Alien communications and Dyson sphere engineering. Extraterrestrial Ψ-communication, exoplanet terraforming, and dark matter energy harvesting.',
      revenue: '$15B',
      status: 'development',
      phase: 2,
      patents: ['Quantum Entanglement Comm Protocol', 'Extraterrestrial Consciousness Interface'],
      metrics: [
        { label: 'NASA/ESA Contracts', value: '$1B/year' },
        { label: 'Asteroid Mining', value: 'Royalties' },
        { label: 'Alien Contact', value: '47% probability' }
      ]
    },
    {
      id: 'social',
      name: 'Social Reality Studio',
      icon: <Users className="w-12 h-12" />,
      description: 'Memetic engineering and conflict resolution. Memetic virus inoculation, war probability suppression, and 18/82 wealth harmony enforcement.',
      revenue: '$12B',
      status: 'planned',
      phase: 3,
      patents: ['18/82 Harmony Algorithm', 'Collective Consciousness Optimization'],
      metrics: [
        { label: 'UN Contracts', value: 'Conflict prevention' },
        { label: 'Social Media', value: 'Platform licensing' },
        { label: 'War Prevention', value: '73% reduction' }
      ]
    }
  ]

  useEffect(() => {
    // Simulate revenue growth
    const interval = setInterval(() => {
      setTotalRevenue(prev => prev + Math.random() * 0.1)
    }, 3000)

    return () => clearInterval(interval)
  }, [])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'operational': return 'text-green-400 border-green-400'
      case 'development': return 'text-yellow-400 border-yellow-400'
      case 'planned': return 'text-blue-400 border-blue-400'
      default: return 'text-gray-400 border-gray-400'
    }
  }

  const getPhaseInfo = (phase: number) => {
    switch (phase) {
      case 1: return { label: 'Phase 1 (2025)', color: 'text-green-400' }
      case 2: return { label: 'Phase 2 (2026)', color: 'text-yellow-400' }
      case 3: return { label: 'Phase 3 (2027)', color: 'text-blue-400' }
      default: return { label: 'Future', color: 'text-gray-400' }
    }
  }

  return (
    <div className="min-h-screen p-6 bg-black/10">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-12"
        >
          <h1 className="text-5xl font-bold mb-4 text-consciousness">
            Reality Engineering Suite
          </h1>
          <p className="text-xl text-white/70 mb-6">
            "We don't just adapt to reality—we copyright it."
          </p>
          <div className="text-lg text-cyan-400">
            Vertical Specialization Strategy for Maximum IP Value & Defensibility
          </div>
        </motion.div>

        {/* Revenue Overview */}
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="consciousness-card mb-12"
        >
          <div className="grid md:grid-cols-4 gap-6 text-center">
            <div>
              <div className="text-sm text-white/70 mb-2">Total Annual Revenue</div>
              <div className="text-4xl font-bold text-consciousness mb-2">
                ${totalRevenue.toFixed(1)}B
              </div>
              <div className="text-xs text-white/50">Across all studios</div>
            </div>
            <div>
              <div className="text-sm text-white/70 mb-2">Patent Licensing</div>
              <div className="text-4xl font-bold text-cyan-400 mb-2">
                $47.2B
              </div>
              <div className="text-xs text-white/50">IP monetization</div>
            </div>
            <div>
              <div className="text-sm text-white/70 mb-2">Active Contracts</div>
              <div className="text-4xl font-bold text-purple-400 mb-2">
                2,847
              </div>
              <div className="text-xs text-white/50">Global partnerships</div>
            </div>
            <div>
              <div className="text-sm text-white/70 mb-2">Market Dominance</div>
              <div className="text-4xl font-bold text-yellow-400 mb-2">
                94.7%
              </div>
              <div className="text-xs text-white/50">Reality engineering</div>
            </div>
          </div>
        </motion.div>

        {/* Studios Grid */}
        <div className="grid lg:grid-cols-2 xl:grid-cols-3 gap-8 mb-12">
          {studios.map((studio, index) => {
            const phaseInfo = getPhaseInfo(studio.phase)
            
            return (
              <motion.div
                key={studio.id}
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                className={`consciousness-card cursor-pointer transition-all duration-300 ${
                  activeStudio === studio.id ? 'ring-2 ring-cyan-400' : ''
                }`}
                onClick={() => setActiveStudio(activeStudio === studio.id ? null : studio.id)}
              >
                <div className="flex items-center justify-between mb-4">
                  <div className="text-cyan-400">{studio.icon}</div>
                  <div className={`px-3 py-1 rounded-full border text-xs font-medium ${getStatusColor(studio.status)}`}>
                    {studio.status.toUpperCase()}
                  </div>
                </div>
                
                <h3 className="text-xl font-bold text-white mb-3">{studio.name}</h3>
                <p className="text-white/70 text-sm mb-4 line-clamp-3">{studio.description}</p>
                
                <div className="space-y-2 mb-4">
                  <div className="flex justify-between">
                    <span className="text-white/60 text-sm">Annual Revenue:</span>
                    <span className="text-consciousness font-bold">{studio.revenue}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/60 text-sm">Phase:</span>
                    <span className={`font-medium ${phaseInfo.color}`}>{phaseInfo.label}</span>
                  </div>
                </div>

                {activeStudio === studio.id && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    transition={{ duration: 0.3 }}
                    className="border-t border-white/20 pt-4 mt-4"
                  >
                    <div className="space-y-3">
                      <div>
                        <h4 className="text-sm font-medium text-cyan-400 mb-2">Key Metrics:</h4>
                        {studio.metrics.map((metric, idx) => (
                          <div key={idx} className="flex justify-between text-xs">
                            <span className="text-white/60">{metric.label}:</span>
                            <span className="text-white">{metric.value}</span>
                          </div>
                        ))}
                      </div>
                      
                      <div>
                        <h4 className="text-sm font-medium text-purple-400 mb-2">Patents:</h4>
                        {studio.patents.map((patent, idx) => (
                          <div key={idx} className="text-xs text-white/70">• {patent}</div>
                        ))}
                      </div>
                    </div>
                  </motion.div>
                )}

                <div className="mt-4">
                  {studio.status === 'operational' ? (
                    <Link 
                      href={`/studios/${studio.id}`}
                      className="trinity-button w-full text-center block"
                    >
                      Launch Studio
                    </Link>
                  ) : (
                    <button 
                      disabled 
                      className="w-full py-3 px-6 rounded-lg bg-gray-600 text-gray-400 cursor-not-allowed"
                    >
                      {studio.status === 'development' ? 'In Development' : 'Coming Soon'}
                    </button>
                  )}
                </div>
              </motion.div>
            )
          })}
        </div>

        {/* Implementation Roadmap */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="consciousness-card"
        >
          <h2 className="text-2xl font-bold text-consciousness mb-8 text-center">
            🚀 Implementation Roadmap
          </h2>
          
          <div className="space-y-6">
            <div className="flex items-center space-x-6 p-6 bg-green-500/10 border border-green-500/30 rounded-lg">
              <div className="text-3xl font-bold text-green-400 min-w-[60px]">1</div>
              <div>
                <h3 className="text-xl font-bold text-white mb-2">Phase 1 (2025): Core Revenue Studios</h3>
                <p className="text-white/70">Financial + Medical + AI Alignment Studios. Financial funds ecosystem, Medical defends ethics, AI Alignment ensures safety.</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-6 p-6 bg-yellow-500/10 border border-yellow-500/30 rounded-lg">
              <div className="text-3xl font-bold text-yellow-400 min-w-[60px]">2</div>
              <div>
                <h3 className="text-xl font-bold text-white mb-2">Phase 2 (2026): Expansion Verticals</h3>
                <p className="text-white/70">Climate + Cosmology Studios. Climate buys political protection, Cosmology secures the future.</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-6 p-6 bg-blue-500/10 border border-blue-500/30 rounded-lg">
              <div className="text-3xl font-bold text-blue-400 min-w-[60px]">3</div>
              <div>
                <h3 className="text-xl font-bold text-white mb-2">Phase 3 (2027): Complete Oligopoly</h3>
                <p className="text-white/70">Social Reality Studio controls narrative. Emerging studios (Quantum Crime, Consciousness NFTs, Temporal Tourism).</p>
              </div>
            </div>
          </div>
        </motion.div>

        {/* HOD Patent Reference */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.8 }}
          className="consciousness-card mt-8 text-center"
        >
          <Shield className="w-16 h-16 text-yellow-400 mx-auto mb-4" />
          <div className="text-consciousness font-bold text-lg mb-2">
            🏛️ Powered by HOD Patent Technology
          </div>
          <p className="text-white/70 mb-4">
            System for Coherent Reality Optimization - The foundational framework for all reality engineering operations across every studio
          </p>
          <div className="border-t border-white/20 pt-4">
            <div className="text-cyan-400 text-sm font-medium">Created by</div>
            <div className="text-cyan-300 text-xl font-bold">NovaFuse Technologies</div>
            <div className="text-cyan-500 text-sm">A Comphyology-based company</div>
          </div>
        </motion.div>
      </div>
    </div>
  )
}
