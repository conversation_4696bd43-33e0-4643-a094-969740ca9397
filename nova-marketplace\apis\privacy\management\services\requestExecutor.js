/**
 * Request Executor Service
 * 
 * This service executes requests to external systems.
 */

const axios = require('axios');
const { getIntegrationById } = require('./integrationRegistry');
const { generateAuthHeaders } = require('./authenticationManager');
const { transformRequest, transformResponse } = require('./dataTransformer');

/**
 * Execute a request to an external system
 * @param {string} integrationId - Integration ID
 * @param {string} action - Action to execute
 * @param {Object} data - Request data
 * @returns {Promise<Object>} Response data
 */
const executeRequest = async (integrationId, action, data) => {
  try {
    // Get the integration
    const integration = await getIntegrationById(integrationId);
    
    // Check if the integration supports the action
    if (!integration.capabilities.includes(action)) {
      const error = new Error(`Integration does not support action '${action}'`);
      error.name = 'ValidationError';
      throw error;
    }
    
    // Check if the integration is active
    if (integration.status !== 'active') {
      const error = new Error(`Integration is not active`);
      error.name = 'ValidationError';
      throw error;
    }
    
    // Get the handler for the action
    const handler = integration.handlers[action];
    
    if (!handler || !handler.endpoint) {
      const error = new Error(`No handler configured for action '${action}'`);
      error.name = 'ValidationError';
      throw error;
    }
    
    // Generate authentication headers
    const authHeaders = await generateAuthHeaders(integration);
    
    // Transform the request data
    const transformedData = await transformRequest(data, integration.type, action);
    
    // Execute the request
    const response = await executeHttpRequest(
      handler.endpoint,
      handler.method || 'POST',
      transformedData,
      authHeaders
    );
    
    // Transform the response data
    const transformedResponse = await transformResponse(response, integration.type, action);
    
    // Update integration metrics
    await updateIntegrationMetrics(integration, true, response.responseTime);
    
    return transformedResponse;
  } catch (error) {
    // Update integration metrics
    if (error.integrationId) {
      const integration = await getIntegrationById(integrationId);
      await updateIntegrationMetrics(integration, false, error.responseTime);
    }
    
    throw error;
  }
};

/**
 * Execute an HTTP request
 * @param {string} endpoint - Request endpoint
 * @param {string} method - HTTP method
 * @param {Object} data - Request data
 * @param {Object} headers - Request headers
 * @returns {Promise<Object>} Response data
 */
const executeHttpRequest = async (endpoint, method, data, headers) => {
  try {
    const startTime = Date.now();
    
    // Execute the request
    const response = await axios({
      url: endpoint,
      method: method,
      data: method !== 'GET' ? data : undefined,
      params: method === 'GET' ? data : undefined,
      headers: headers
    });
    
    const endTime = Date.now();
    const responseTime = endTime - startTime;
    
    return {
      data: response.data,
      status: response.status,
      headers: response.headers,
      responseTime
    };
  } catch (error) {
    // Enhance the error with additional information
    const enhancedError = new Error(error.message);
    enhancedError.name = 'IntegrationError';
    enhancedError.status = error.response ? error.response.status : 500;
    enhancedError.data = error.response ? error.response.data : null;
    enhancedError.responseTime = error.config ? Date.now() - error.config.startTime : 0;
    
    throw enhancedError;
  }
};

/**
 * Update integration metrics
 * @param {Object} integration - Integration object
 * @param {boolean} success - Whether the request was successful
 * @param {number} responseTime - Response time in milliseconds
 * @returns {Promise<void>}
 */
const updateIntegrationMetrics = async (integration, success, responseTime) => {
  // Update metrics
  integration.metrics.totalRequests += 1;
  
  if (success) {
    integration.metrics.successfulRequests += 1;
  } else {
    integration.metrics.failedRequests += 1;
  }
  
  // Calculate new average response time
  const totalResponseTime = integration.metrics.averageResponseTime * (integration.metrics.totalRequests - 1) + responseTime;
  integration.metrics.averageResponseTime = totalResponseTime / integration.metrics.totalRequests;
  
  // Save the updated integration
  await integration.save();
};

/**
 * Execute a batch of requests to external systems
 * @param {Array} requests - Array of request objects
 * @returns {Promise<Array>} Array of response objects
 */
const executeBatchRequests = async (requests) => {
  const results = [];
  
  for (const request of requests) {
    try {
      const response = await executeRequest(
        request.integrationId,
        request.action,
        request.data
      );
      
      results.push({
        integrationId: request.integrationId,
        action: request.action,
        success: true,
        data: response
      });
    } catch (error) {
      results.push({
        integrationId: request.integrationId,
        action: request.action,
        success: false,
        error: {
          message: error.message,
          name: error.name,
          status: error.status
        }
      });
    }
  }
  
  return results;
};

module.exports = {
  executeRequest,
  executeBatchRequests
};
