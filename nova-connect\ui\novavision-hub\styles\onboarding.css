/**
 * Onboarding Styles
 * 
 * Styles for onboarding components.
 */

/* Onboarding Step */
.onboarding-step {
  position: fixed;
  z-index: 9999;
  width: 300px;
  max-width: 90vw;
  background-color: var(--color-background);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid var(--color-border);
  opacity: 0;
  transform: scale(0.95);
  transition: opacity 0.2s ease, transform 0.2s ease;
  overflow: hidden;
}

.onboarding-step--visible {
  opacity: 1;
  transform: scale(1);
}

.onboarding-step__arrow {
  position: absolute;
  width: 0;
  height: 0;
  border-style: solid;
}

.onboarding-step__arrow--top {
  border-width: 0 10px 10px 10px;
  border-color: transparent transparent var(--color-border) transparent;
}

.onboarding-step__arrow--bottom {
  border-width: 10px 10px 0 10px;
  border-color: var(--color-border) transparent transparent transparent;
}

.onboarding-step__arrow--left {
  border-width: 10px 0 10px 10px;
  border-color: transparent transparent transparent var(--color-border);
}

.onboarding-step__arrow--right {
  border-width: 10px 10px 10px 0;
  border-color: transparent var(--color-border) transparent transparent;
}

.onboarding-step__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid var(--color-border);
  background-color: var(--color-surface);
}

.onboarding-step__title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text-primary);
}

.onboarding-step__close {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  padding: 0;
  background: none;
  border: none;
  border-radius: 50%;
  color: var(--color-text-secondary);
  cursor: pointer;
  transition: background-color 0.2s ease, color 0.2s ease;
}

.onboarding-step__close:hover {
  background-color: var(--color-hover);
  color: var(--color-text-primary);
}

.onboarding-step__content {
  padding: 16px;
}

.onboarding-step__description {
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
  color: var(--color-text-secondary);
}

.onboarding-step__footer {
  padding: 12px 16px;
  border-top: 1px solid var(--color-border);
  background-color: var(--color-surface);
}

.onboarding-step__progress {
  margin-bottom: 12px;
}

.onboarding-step__progress-text {
  font-size: 12px;
  color: var(--color-text-secondary);
  margin-bottom: 4px;
}

.onboarding-step__progress-bar {
  height: 4px;
  background-color: var(--color-border);
  border-radius: 2px;
  overflow: hidden;
}

.onboarding-step__progress-indicator {
  height: 100%;
  background-color: var(--color-primary);
  border-radius: 2px;
  transition: width 0.3s ease;
}

.onboarding-step__buttons {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.onboarding-step__skip {
  padding: 6px 12px;
  background: none;
  border: none;
  color: var(--color-text-secondary);
  font-size: 14px;
  cursor: pointer;
  transition: color 0.2s ease;
}

.onboarding-step__skip:hover {
  color: var(--color-text-primary);
}

.onboarding-step__prev {
  padding: 6px 12px;
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: 4px;
  color: var(--color-text-primary);
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.onboarding-step__prev:hover {
  background-color: var(--color-hover);
}

.onboarding-step__next {
  padding: 6px 12px;
  background-color: var(--color-primary);
  border: none;
  border-radius: 4px;
  color: white;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.onboarding-step__next:hover {
  background-color: var(--color-primary-dark);
}

/* Onboarding Spotlight */
.onboarding-spotlight {
  position: fixed;
  z-index: 9998;
  border: 2px solid;
  border-radius: 4px;
  box-shadow: 0 0 0 4px rgba(25, 118, 210, 0.2);
  pointer-events: none;
  transition: top 0.3s ease, left 0.3s ease, width 0.3s ease, height 0.3s ease;
}

.onboarding-spotlight--pulse {
  animation: spotlight-pulse 2s infinite;
}

@keyframes spotlight-pulse {
  0% {
    box-shadow: 0 0 0 4px rgba(25, 118, 210, 0.2);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(25, 118, 210, 0.1);
  }
  100% {
    box-shadow: 0 0 0 4px rgba(25, 118, 210, 0.2);
  }
}

/* Onboarding Progress Tracker */
.onboarding-progress-tracker {
  background-color: var(--color-background);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--color-border);
  overflow: hidden;
}

.onboarding-progress-tracker__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid var(--color-border);
  background-color: var(--color-surface);
}

.onboarding-progress-tracker__title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--color-text-primary);
}

.onboarding-progress-tracker__reset-all {
  padding: 6px 12px;
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: 4px;
  color: var(--color-text-primary);
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.onboarding-progress-tracker__reset-all:hover {
  background-color: var(--color-hover);
}

.onboarding-progress-tracker__progress {
  padding: 16px;
  border-bottom: 1px solid var(--color-border);
}

.onboarding-progress-tracker__progress-bar {
  height: 8px;
  background-color: var(--color-border);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
}

.onboarding-progress-tracker__progress-indicator {
  height: 100%;
  background-color: var(--color-primary);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.onboarding-progress-tracker__progress-text {
  font-size: 14px;
  color: var(--color-text-secondary);
}

.onboarding-progress-tracker__tours {
  padding: 16px;
}

.onboarding-progress-tracker__section {
  margin-bottom: 24px;
}

.onboarding-progress-tracker__section-title {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text-primary);
}

.onboarding-progress-tracker__list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.onboarding-progress-tracker__item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 12px;
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: 6px;
  transition: box-shadow 0.2s ease;
}

.onboarding-progress-tracker__item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.onboarding-progress-tracker__item--completed {
  border-left: 4px solid var(--color-success);
}

.onboarding-progress-tracker__item-content {
  flex: 1;
}

.onboarding-progress-tracker__item-title {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 500;
  color: var(--color-text-primary);
}

.onboarding-progress-tracker__item-description {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: var(--color-text-secondary);
}

.onboarding-progress-tracker__item-meta {
  font-size: 12px;
  color: var(--color-text-tertiary);
}

.onboarding-progress-tracker__item-actions {
  display: flex;
  gap: 8px;
  margin-left: 16px;
}

.onboarding-progress-tracker__start,
.onboarding-progress-tracker__restart {
  padding: 6px 12px;
  background-color: var(--color-primary);
  border: none;
  border-radius: 4px;
  color: white;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.onboarding-progress-tracker__start:hover,
.onboarding-progress-tracker__restart:hover {
  background-color: var(--color-primary-dark);
}

.onboarding-progress-tracker__reset {
  padding: 6px 12px;
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: 4px;
  color: var(--color-text-primary);
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.onboarding-progress-tracker__reset:hover {
  background-color: var(--color-hover);
}

.onboarding-progress-tracker__empty {
  padding: 24px;
  text-align: center;
  color: var(--color-text-secondary);
  font-size: 14px;
}
