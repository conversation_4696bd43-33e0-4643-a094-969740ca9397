/**
 * Error Handling Middleware Tests
 *
 * This file contains unit tests for the error handling middleware.
 */

const { errorHandler } = require('../../middleware/error');

describe('Error Handling Middleware', () => {
  let req, res, next;

  beforeEach(() => {
    req = {};

    res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    };

    next = jest.fn();
  });

  it('should handle ValidationError', () => {
    const error = new Error('Validation failed');
    error.name = 'ValidationError';
    error.errors = [
      { field: 'name', message: 'Name is required' }
    ];

    errorHandler(error, req, res, next);

    expect(res.status).toHaveBeenCalledWith(400);
    expect(res.json).toHaveBeenCalledWith({
      error: 'ValidationError',
      message: 'Validation failed',
      errors: error.errors
    });
    expect(next).not.toHaveBeenCalled();
  });

  it('should handle AuthenticationError', () => {
    const error = new Error('Invalid token');
    error.name = 'AuthenticationError';

    errorHandler(error, req, res, next);

    expect(res.status).toHaveBeenCalledWith(401);
    expect(res.json).toHaveBeenCalledWith({
      error: 'Unauthorized',
      message: 'Invalid token'
    });
    expect(next).not.toHaveBeenCalled();
  });

  it('should handle NotFoundError', () => {
    const error = new Error('Resource not found');
    error.name = 'NotFoundError';

    errorHandler(error, req, res, next);

    expect(res.status).toHaveBeenCalledWith(404);
    expect(res.json).toHaveBeenCalledWith({
      error: 'NotFound',
      message: 'Resource not found'
    });
    expect(next).not.toHaveBeenCalled();
  });

  it('should handle ConflictError', () => {
    const error = new Error('Resource already exists');
    error.name = 'ConflictError';

    errorHandler(error, req, res, next);

    expect(res.status).toHaveBeenCalledWith(409);
    expect(res.json).toHaveBeenCalledWith({
      error: 'Conflict',
      message: 'Resource already exists'
    });
    expect(next).not.toHaveBeenCalled();
  });

  it('should handle ForbiddenError', () => {
    const error = new Error('Insufficient permissions');
    error.name = 'ForbiddenError';

    errorHandler(error, req, res, next);

    expect(res.status).toHaveBeenCalledWith(403);
    expect(res.json).toHaveBeenCalledWith({
      error: 'Forbidden',
      message: 'Insufficient permissions'
    });
    expect(next).not.toHaveBeenCalled();
  });

  it('should handle BadRequestError', () => {
    const error = new Error('Bad request');
    error.name = 'BadRequestError';

    errorHandler(error, req, res, next);

    expect(res.status).toHaveBeenCalledWith(400);
    expect(res.json).toHaveBeenCalledWith({
      error: 'BadRequest',
      message: 'Bad request'
    });
    expect(next).not.toHaveBeenCalled();
  });

  it('should handle UnprocessableEntityError', () => {
    const error = new Error('Unprocessable entity');
    error.name = 'UnprocessableEntityError';

    errorHandler(error, req, res, next);

    expect(res.status).toHaveBeenCalledWith(422);
    expect(res.json).toHaveBeenCalledWith({
      error: 'UnprocessableEntity',
      message: 'Unprocessable entity'
    });
    expect(next).not.toHaveBeenCalled();
  });

  it('should handle TooManyRequestsError', () => {
    const error = new Error('Too many requests');
    error.name = 'TooManyRequestsError';

    errorHandler(error, req, res, next);

    expect(res.status).toHaveBeenCalledWith(429);
    expect(res.json).toHaveBeenCalledWith({
      error: 'TooManyRequests',
      message: 'Too many requests'
    });
    expect(next).not.toHaveBeenCalled();
  });

  it('should handle generic errors with status code', () => {
    const error = new Error('Generic error');
    error.status = 418; // I'm a teapot

    errorHandler(error, req, res, next);

    expect(res.status).toHaveBeenCalledWith(418);
    expect(res.json).toHaveBeenCalledWith({
      error: 'InternalServerError',
      message: 'An unexpected error occurred',
      originalError: 'Generic error',
      stack: undefined
    });
    expect(next).not.toHaveBeenCalled();
  });

  it('should handle unknown errors as 500 Internal Server Error', () => {
    const error = new Error('Unknown error');

    errorHandler(error, req, res, next);

    expect(res.status).toHaveBeenCalledWith(500);
    expect(res.json).toHaveBeenCalledWith({
      error: 'InternalServerError',
      message: 'An unexpected error occurred'
    });
    expect(next).not.toHaveBeenCalled();
  });

  it('should include stack trace in development environment', () => {
    const originalEnv = process.env.NODE_ENV;
    process.env.NODE_ENV = 'development';

    const error = new Error('Unknown error');
    error.stack = 'Error stack trace';

    errorHandler(error, req, res, next);

    expect(res.status).toHaveBeenCalledWith(500);
    expect(res.json).toHaveBeenCalledWith({
      error: 'InternalServerError',
      message: 'An unexpected error occurred',
      stack: 'Error stack trace'
    });

    // Restore original environment
    process.env.NODE_ENV = originalEnv;
  });
});
