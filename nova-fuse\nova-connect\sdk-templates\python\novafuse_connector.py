﻿"""
NovaFuse API Connector - Python SDK

This SDK provides a simple interface to connect to the NovaFuse API Superstore.

Version: 1.0.0
"""

import requests
import json
from typing import Dict, List, Any, Optional


class NovaFuseConnector:
    """Base connector for NovaFuse API Superstore"""
    
    def __init__(self, api_key: str, base_url: str = "http://localhost:8000", category: str = None):
        """
        Initialize the NovaFuse connector
        
        Args:
            api_key (str): Your NovaFuse API key
            base_url (str, optional): Base URL for the API. Defaults to "http://localhost:8000".
            category (str, optional): API category (legal, security, etc.). Defaults to None.
        
        Raises:
            ValueError: If API key or category is not provided
        """
        self.api_key = api_key
        self.base_url = base_url
        self.category = category
        
        if not self.api_key:
            raise ValueError("API key is required")
        
        if not self.category:
            raise ValueError("Category is required")
    
    def request(self, endpoint: str, method: str = "GET", data: Dict = None, params: Dict = None) -> Dict:
        """
        Make a request to the NovaFuse API
        
        Args:
            endpoint (str): API endpoint
            method (str, optional): HTTP method. Defaults to "GET".
            data (Dict, optional): Request data for POST, PUT, etc. Defaults to None.
            params (Dict, optional): Query parameters. Defaults to None.
        
        Returns:
            Dict: API response
        
        Raises:
            Exception: If the API request fails
        """
        url = f"{self.base_url}/{self.category}{endpoint}"
        
        headers = {
            "Content-Type": "application/json",
            "apikey": self.api_key
        }
        
        try:
            if method == "GET":
                response = requests.get(url, headers=headers, params=params)
            elif method == "POST":
                response = requests.post(url, headers=headers, json=data, params=params)
            elif method == "PUT":
                response = requests.put(url, headers=headers, json=data, params=params)
            elif method == "DELETE":
                response = requests.delete(url, headers=headers, params=params)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
            
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f"NovaFuse API request failed: {e}")
            raise
    
    def check_health(self) -> Dict:
        """
        Check if the API is healthy
        
        Returns:
            Dict: Health status
        """
        return self.request("/health")

