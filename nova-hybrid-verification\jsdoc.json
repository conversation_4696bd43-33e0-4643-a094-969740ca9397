{"source": {"include": ["src", "README.md"], "includePattern": ".+\\.js(doc|x)?$", "excludePattern": "(^|\\/|\\\\)_"}, "plugins": ["plugins/markdown"], "opts": {"destination": "./docs/", "recurse": true, "readme": "README.md", "template": "node_modules/docdash"}, "templates": {"cleverLinks": false, "monospaceLinks": false, "default": {"outputSourceFiles": true, "includeDate": true}}, "docdash": {"static": true, "sort": true, "sectionOrder": ["Classes", "<PERSON><PERSON><PERSON>", "Externals", "Events", "Namespaces", "Mixins", "Tutorials", "Interfaces"], "meta": {"title": "Hybrid DAG-based Zero-Knowledge System Documentation", "description": "Documentation for the Hybrid DAG-based Zero-Knowledge System", "keyword": "dag, zero-knowledge, verification, comphyology, novafuse"}, "search": true, "collapse": true, "wrap": true, "typedefs": true, "navLevel": 3, "private": true, "removeQuotes": "none", "scripts": [], "menu": {"GitHub": {"href": "https://github.com/Dartan1983/nova-hybrid-verification", "target": "_blank", "class": "menu-item", "id": "github_link"}}}}