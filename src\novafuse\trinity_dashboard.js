/**
 * Trinity Dashboard
 * 
 * This module provides a real-time dashboard for monitoring the Comphyological Trinity
 * and Comphyon Meter-Governor metrics. It serves as the visualization layer for the
 * unified architecture, providing insights into system health and performance.
 * 
 * The dashboard includes:
 * 1. Trinity Metrics - First, Second, and Third Law compliance
 * 2. Comphyon Metrics - Emergent intelligence measurements
 * 3. Integration Metrics - NovaFuse component integration statistics
 * 4. Alerts - Resonance violations and high Comphyon values
 * 5. Visualizations - Real-time charts and graphs
 */

const EventEmitter = require('events');
const NovaTrinityIntegration = require('./trinity_integration');

/**
 * Trinity Dashboard class
 */
class TrinityDashboard extends EventEmitter {
  /**
   * Constructor
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      // Dashboard options
      refreshInterval: 1000, // ms
      alertThreshold: 0.8,
      criticalThreshold: 0.95,
      maxHistory: 100,
      
      // Integration options
      integration: null, // NovaTrinityIntegration instance
      createIntegration: true,
      integrationOptions: {},
      
      // Visualization options
      enableCharts: true,
      chartColors: {
        firstLaw: '#cc0000',
        secondLaw: '#009900',
        thirdLaw: '#0066cc',
        comphyon: '#9900cc'
      },
      
      // Logging options
      logDashboard: false,
      
      ...options
    };
    
    // Initialize components
    this._initializeComponents();
    
    // Initialize metrics
    this.metrics = {
      dashboardRefreshes: 0,
      alerts: 0,
      criticalAlerts: 0,
      totalOperations: 0
    };
    
    // Initialize history
    this.history = {
      trinity: [],
      comphyon: [],
      integration: []
    };
    
    if (this.options.logDashboard) {
      console.log('Trinity Dashboard initialized with options:', this.options);
    }
  }
  
  /**
   * Initialize components
   * @private
   */
  _initializeComponents() {
    // Use provided integration or create a new one
    if (this.options.integration) {
      this.integration = this.options.integration;
    } else if (this.options.createIntegration) {
      this.integration = new NovaTrinityIntegration(this.options.integrationOptions);
    } else {
      throw new Error('No integration provided and createIntegration is false');
    }
    
    // Forward events from integration
    if (typeof this.integration.on === 'function') {
      this.integration.on('api-validation', (data) => {
        this._checkForAlerts(data);
        this.emit('api-validation', data);
      });
      
      this.integration.on('state-transition', (data) => {
        this._checkForAlerts(data);
        this.emit('state-transition', data);
      });
      
      this.integration.on('domain-translation', (data) => {
        this._checkForAlerts(data);
        this.emit('domain-translation', data);
      });
      
      this.integration.on('security-validation', (data) => {
        this._checkForAlerts(data);
        this.emit('security-validation', data);
      });
    }
    
    // Start refresh interval
    this._startRefreshInterval();
  }
  
  /**
   * Start refresh interval
   * @private
   */
  _startRefreshInterval() {
    if (this.refreshInterval) {
      clearInterval(this.refreshInterval);
    }
    
    this.refreshInterval = setInterval(() => {
      this._refreshDashboard();
    }, this.options.refreshInterval);
  }
  
  /**
   * Stop refresh interval
   */
  stopRefreshInterval() {
    if (this.refreshInterval) {
      clearInterval(this.refreshInterval);
      this.refreshInterval = null;
    }
  }
  
  /**
   * Refresh dashboard
   * @private
   */
  _refreshDashboard() {
    this.metrics.dashboardRefreshes++;
    this.metrics.totalOperations++;
    
    // Get current metrics
    const currentMetrics = this._getCurrentMetrics();
    
    // Update history
    this._updateHistory(currentMetrics);
    
    // Check for alerts
    this._checkMetricsForAlerts(currentMetrics);
    
    // Emit refresh event
    this.emit('dashboard-refresh', {
      metrics: currentMetrics,
      history: this.history,
      timestamp: Date.now()
    });
    
    if (this.options.logDashboard) {
      console.log('Dashboard refreshed:', currentMetrics);
    }
  }
  
  /**
   * Get current metrics
   * @returns {Object} - Current metrics
   * @private
   */
  _getCurrentMetrics() {
    // Get metrics from integration
    const integrationMetrics = this.integration.getMetrics();
    
    // Extract Trinity metrics
    const trinityMetrics = integrationMetrics.trinityBridge ? 
      integrationMetrics.trinityBridge.trinity : {};
    
    // Extract Comphyon metrics
    const comphyonMetrics = {
      meter: integrationMetrics.trinityBridge ? 
        integrationMetrics.trinityBridge.comphyonMeter : {},
      governor: integrationMetrics.trinityBridge ? 
        integrationMetrics.trinityBridge.comphyonGovernor : {}
    };
    
    // Calculate derived metrics
    const derivedMetrics = this._calculateDerivedMetrics(
      trinityMetrics, 
      comphyonMetrics, 
      integrationMetrics
    );
    
    return {
      trinity: trinityMetrics,
      comphyon: comphyonMetrics,
      integration: integrationMetrics.integration,
      derived: derivedMetrics,
      dashboard: { ...this.metrics },
      timestamp: Date.now()
    };
  }
  
  /**
   * Calculate derived metrics
   * @param {Object} trinityMetrics - Trinity metrics
   * @param {Object} comphyonMetrics - Comphyon metrics
   * @param {Object} integrationMetrics - Integration metrics
   * @returns {Object} - Derived metrics
   * @private
   */
  _calculateDerivedMetrics(trinityMetrics, comphyonMetrics, integrationMetrics) {
    // Calculate Trinity compliance
    const firstLawCompliance = trinityMetrics.trinity ? 
      1 - (trinityMetrics.trinity.firstLawViolations / Math.max(trinityMetrics.trinity.firstLawEnforcements, 1)) : 1;
    
    const secondLawEfficiency = trinityMetrics.secondLaw ? 
      trinityMetrics.secondLaw.selfSimilarityScore : 0;
    
    const thirdLawFidelity = trinityMetrics.thirdLaw ? 
      trinityMetrics.thirdLaw.crossDomainFidelity : 0;
    
    // Calculate Comphyon health
    const comphyonHealth = comphyonMetrics.meter ? 
      1 - (comphyonMetrics.meter.warnings / Math.max(comphyonMetrics.meter.measurements, 1)) : 1;
    
    // Calculate overall system health
    const systemHealth = (
      (firstLawCompliance * 0.3) + 
      (secondLawEfficiency * 0.3) + 
      (thirdLawFidelity * 0.3) + 
      (comphyonHealth * 0.1)
    );
    
    return {
      firstLawCompliance,
      secondLawEfficiency,
      thirdLawFidelity,
      comphyonHealth,
      systemHealth
    };
  }
  
  /**
   * Update history
   * @param {Object} currentMetrics - Current metrics
   * @private
   */
  _updateHistory(currentMetrics) {
    // Add current metrics to history
    this.history.trinity.push({
      firstLawCompliance: currentMetrics.derived.firstLawCompliance,
      secondLawEfficiency: currentMetrics.derived.secondLawEfficiency,
      thirdLawFidelity: currentMetrics.derived.thirdLawFidelity,
      timestamp: currentMetrics.timestamp
    });
    
    this.history.comphyon.push({
      comphyonHealth: currentMetrics.derived.comphyonHealth,
      measurements: currentMetrics.comphyon.meter ? 
        currentMetrics.comphyon.meter.measurements : 0,
      warnings: currentMetrics.comphyon.meter ? 
        currentMetrics.comphyon.meter.warnings : 0,
      timestamp: currentMetrics.timestamp
    });
    
    this.history.integration.push({
      apiValidations: currentMetrics.integration ? 
        currentMetrics.integration.apiValidations : 0,
      stateTransitions: currentMetrics.integration ? 
        currentMetrics.integration.stateTransitions : 0,
      crossDomainOperations: currentMetrics.integration ? 
        currentMetrics.integration.crossDomainOperations : 0,
      timestamp: currentMetrics.timestamp
    });
    
    // Trim history if needed
    if (this.history.trinity.length > this.options.maxHistory) {
      this.history.trinity.shift();
    }
    
    if (this.history.comphyon.length > this.options.maxHistory) {
      this.history.comphyon.shift();
    }
    
    if (this.history.integration.length > this.options.maxHistory) {
      this.history.integration.shift();
    }
  }
  
  /**
   * Check metrics for alerts
   * @param {Object} metrics - Metrics to check
   * @private
   */
  _checkMetricsForAlerts(metrics) {
    // Check for critical alerts
    if (metrics.derived.systemHealth < this.options.criticalThreshold) {
      this.metrics.criticalAlerts++;
      
      this.emit('critical-alert', {
        type: 'system_health',
        value: metrics.derived.systemHealth,
        threshold: this.options.criticalThreshold,
        metrics,
        timestamp: Date.now()
      });
      
      if (this.options.logDashboard) {
        console.error(`CRITICAL: System health ${metrics.derived.systemHealth} below threshold ${this.options.criticalThreshold}`);
      }
    }
    // Check for alerts
    else if (metrics.derived.systemHealth < this.options.alertThreshold) {
      this.metrics.alerts++;
      
      this.emit('alert', {
        type: 'system_health',
        value: metrics.derived.systemHealth,
        threshold: this.options.alertThreshold,
        metrics,
        timestamp: Date.now()
      });
      
      if (this.options.logDashboard) {
        console.warn(`WARNING: System health ${metrics.derived.systemHealth} below threshold ${this.options.alertThreshold}`);
      }
    }
    
    // Check individual metrics for alerts
    this._checkIndividualMetricsForAlerts(metrics);
  }
  
  /**
   * Check individual metrics for alerts
   * @param {Object} metrics - Metrics to check
   * @private
   */
  _checkIndividualMetricsForAlerts(metrics) {
    // Check First Law compliance
    if (metrics.derived.firstLawCompliance < this.options.alertThreshold) {
      this.metrics.alerts++;
      
      this.emit('alert', {
        type: 'first_law_compliance',
        value: metrics.derived.firstLawCompliance,
        threshold: this.options.alertThreshold,
        metrics,
        timestamp: Date.now()
      });
    }
    
    // Check Second Law efficiency
    if (metrics.derived.secondLawEfficiency < this.options.alertThreshold) {
      this.metrics.alerts++;
      
      this.emit('alert', {
        type: 'second_law_efficiency',
        value: metrics.derived.secondLawEfficiency,
        threshold: this.options.alertThreshold,
        metrics,
        timestamp: Date.now()
      });
    }
    
    // Check Third Law fidelity
    if (metrics.derived.thirdLawFidelity < this.options.alertThreshold) {
      this.metrics.alerts++;
      
      this.emit('alert', {
        type: 'third_law_fidelity',
        value: metrics.derived.thirdLawFidelity,
        threshold: this.options.alertThreshold,
        metrics,
        timestamp: Date.now()
      });
    }
    
    // Check Comphyon health
    if (metrics.derived.comphyonHealth < this.options.alertThreshold) {
      this.metrics.alerts++;
      
      this.emit('alert', {
        type: 'comphyon_health',
        value: metrics.derived.comphyonHealth,
        threshold: this.options.alertThreshold,
        metrics,
        timestamp: Date.now()
      });
    }
  }
  
  /**
   * Check for alerts in event data
   * @param {Object} data - Event data
   * @private
   */
  _checkForAlerts(data) {
    // Check for high Comphyon values
    if (data.comphyonValue > this.options.criticalThreshold * 3.142) {
      this.metrics.criticalAlerts++;
      
      this.emit('critical-alert', {
        type: 'high_comphyon',
        value: data.comphyonValue,
        threshold: this.options.criticalThreshold * 3.142,
        data,
        timestamp: Date.now()
      });
    }
    else if (data.comphyonValue > this.options.alertThreshold * 3.142) {
      this.metrics.alerts++;
      
      this.emit('alert', {
        type: 'high_comphyon',
        value: data.comphyonValue,
        threshold: this.options.alertThreshold * 3.142,
        data,
        timestamp: Date.now()
      });
    }
  }
  
  /**
   * Get metrics
   * @returns {Object} - Metrics
   */
  getMetrics() {
    return {
      dashboard: { ...this.metrics },
      integration: this.integration ? this.integration.getMetrics() : null,
      history: this.history
    };
  }
  
  /**
   * Get current system health
   * @returns {number} - System health (0-1)
   */
  getSystemHealth() {
    const metrics = this._getCurrentMetrics();
    return metrics.derived.systemHealth;
  }
}

module.exports = TrinityDashboard;
