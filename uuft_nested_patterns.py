#!/usr/bin/env python3
# UUFT Nested/Fractal Patterns Testing
# This script tests for nested and fractal patterns across different domains

import os
import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from scipy import stats
import logging
import gzip
import json
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler
import pywt  # PyWavelets for wavelet analysis
import zipfile
import io

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("uuft_nested_patterns.log"),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger("UUFT_Nested_Patterns")

# Base directory for all data
BASE_DIR = "D:/Archives"
RESULTS_DIR = os.path.join(BASE_DIR, "Results")

# Test for Nested/Fractal patterns
def test_nested_patterns(data, domain, dataset_name):
    """
    Test for Nested/Fractal patterns in the given data.
    Nested patterns show self-similarity across different scales.

    Args:
        data: The data to test (numpy array or pandas DataFrame)
        domain: The domain of the data (cosmological, biological, social, technological)
        dataset_name: The name of the dataset

    Returns:
        dict: Results of the Nested/Fractal patterns test
    """
    logger.info(f"Testing Nested/Fractal patterns for {domain} dataset: {dataset_name}")

    try:
        # Convert to numpy array if needed
        if isinstance(data, pd.Series):
            data = data.values
        elif isinstance(data, pd.DataFrame):
            data = data.values

        # Ensure data is 1D for wavelet analysis
        data = np.array(data).flatten()

        # Remove NaN values
        data = data[~np.isnan(data)]

        # Need sufficient data points for meaningful analysis
        if len(data) < 64:
            logger.warning(f"Insufficient data points for {domain} - {dataset_name}: {len(data)} < 64")
            return {
                "domain": domain,
                "dataset_name": dataset_name,
                "error": "Insufficient data points",
                "is_nested_pattern_present": False,
                "is_fractal_pattern_present": False
            }

        # Standardize the data
        data_std = (data - np.mean(data)) / np.std(data)

        # 1. Hurst Exponent Analysis
        # The Hurst exponent measures the long-term memory of a time series
        # H = 0.5: random walk (no self-similarity)
        # 0.5 < H < 1: persistent behavior (fractal-like)
        # 0 < H < 0.5: anti-persistent behavior

        # Calculate Hurst exponent using R/S analysis
        def hurst_exponent(data, max_lag=20):
            """Calculate Hurst exponent using R/S analysis."""
            lags = range(2, min(max_lag, len(data) // 4))
            tau = []; rs = []

            for lag in lags:
                # Split data into chunks
                n_chunks = len(data) // lag
                if n_chunks < 1:
                    continue

                # Calculate R/S for each chunk
                rs_values = []
                for i in range(n_chunks):
                    chunk = data[i*lag:(i+1)*lag]
                    mean = np.mean(chunk)
                    # Cumulative deviation from mean
                    profile = np.cumsum(chunk - mean)
                    # Range
                    r = np.max(profile) - np.min(profile)
                    # Standard deviation
                    s = np.std(chunk)
                    if s > 0:
                        rs_values.append(r / s)

                if rs_values:
                    rs.append(np.mean(rs_values))
                    tau.append(lag)

            if len(tau) < 2:
                return 0.5  # Default to random walk

            # Linear regression on log-log plot
            log_tau = np.log10(tau)
            log_rs = np.log10(rs)
            slope, _, _, _, _ = stats.linregress(log_tau, log_rs)

            return slope

        hurst = hurst_exponent(data_std)

        # 2. Wavelet Analysis
        # Wavelet analysis can detect self-similarity across scales

        # Perform wavelet decomposition
        wavelet = 'db4'  # Daubechies wavelet
        max_level = pywt.dwt_max_level(len(data_std), wavelet)
        level = min(5, max_level)  # Use at most 5 levels

        # Decompose signal
        coeffs = pywt.wavedec(data_std, wavelet, level=level)

        # Calculate energy at each level
        energy_levels = [np.sum(np.square(c)) for c in coeffs]
        total_energy = sum(energy_levels)
        energy_ratio = [e / total_energy for e in energy_levels]

        # Calculate correlation between adjacent scales
        scale_correlations = []
        for i in range(len(coeffs) - 1):
            # Ensure same length by truncating the longer array
            min_len = min(len(coeffs[i]), len(coeffs[i+1]))
            if min_len > 1:
                corr, _ = stats.pearsonr(coeffs[i][:min_len], coeffs[i+1][:min_len])
                scale_correlations.append(abs(corr))

        # 3. Statistical Self-Similarity
        # Calculate statistical properties at different scales

        scale_stats = []
        for scale in [1, 2, 4, 8, 16]:
            if len(data_std) // scale < 10:
                continue

            # Downsample data
            downsampled = data_std[::scale]

            # Calculate statistics
            scale_stats.append({
                "scale": scale,
                "mean": float(np.mean(downsampled)),
                "std": float(np.std(downsampled)),
                "skewness": float(stats.skew(downsampled)),
                "kurtosis": float(stats.kurtosis(downsampled))
            })

        # Calculate correlation of statistics across scales
        stat_correlations = {}
        for stat in ["mean", "std", "skewness", "kurtosis"]:
            if len(scale_stats) > 1:
                stat_values = [s[stat] for s in scale_stats]
                # Calculate correlation with scale
                scales = [s["scale"] for s in scale_stats]
                corr, _ = stats.pearsonr(scales, stat_values)
                stat_correlations[stat] = float(corr)

        # 4. Determine if nested/fractal patterns are present

        # Nested pattern criteria:
        # - Strong correlation between adjacent scales
        is_nested_pattern_present = False
        if scale_correlations and np.mean(scale_correlations) > 0.7:
            is_nested_pattern_present = True

        # Fractal pattern criteria:
        # - Hurst exponent between 0.6 and 0.9 (persistent, fractal-like behavior)
        # - Energy distribution across scales follows power law
        is_fractal_pattern_present = False
        if 0.6 <= hurst <= 0.9:
            # Check if energy distribution follows power law
            if len(energy_ratio) > 2:
                log_energy = np.log10(energy_ratio)
                log_scales = np.log10(range(1, len(energy_ratio) + 1))
                slope, _, r_value, _, _ = stats.linregress(log_scales, log_energy)

                # Strong linear relationship in log-log plot indicates power law
                if r_value**2 > 0.8 and slope < -0.5:
                    is_fractal_pattern_present = True

        # Create visualization
        plt.figure(figsize=(12, 8))

        # Plot wavelet coefficients
        plt.subplot(2, 1, 1)
        for i, coeff in enumerate(coeffs):
            plt.plot(coeff, label=f'Level {i}')
        plt.title(f'Wavelet Coefficients: {domain.capitalize()} - {dataset_name}')
        plt.legend()

        # Plot energy distribution
        plt.subplot(2, 1, 2)
        plt.bar(range(len(energy_ratio)), energy_ratio)
        plt.xlabel('Decomposition Level')
        plt.ylabel('Energy Ratio')
        plt.title('Energy Distribution Across Scales')

        # Save visualization
        os.makedirs(RESULTS_DIR, exist_ok=True)
        plt.savefig(os.path.join(RESULTS_DIR, f'nested_pattern_{domain}_{dataset_name}.png'))
        plt.close()

        # Compile results
        results = {
            "domain": domain,
            "dataset_name": dataset_name,
            "data_points": int(len(data)),
            "hurst_exponent": float(hurst),
            "wavelet_analysis": {
                "wavelet_type": wavelet,
                "decomposition_levels": int(level),
                "energy_ratio": [float(e) for e in energy_ratio],
                "scale_correlations": [float(c) for c in scale_correlations],
                "mean_scale_correlation": float(np.mean(scale_correlations)) if scale_correlations else None
            },
            "scale_statistics": scale_stats,
            "statistic_correlations": stat_correlations,
            "is_nested_pattern_present": bool(is_nested_pattern_present),
            "is_fractal_pattern_present": bool(is_fractal_pattern_present)
        }

        logger.info(f"Nested/Fractal patterns test results for {domain} - {dataset_name}:")
        logger.info(f"Hurst Exponent: {hurst:.3f}")
        logger.info(f"Nested Pattern Present: {is_nested_pattern_present}")
        logger.info(f"Fractal Pattern Present: {is_fractal_pattern_present}")

        return results

    except Exception as e:
        logger.error(f"Error testing Nested/Fractal patterns for {domain} - {dataset_name}: {e}")
        return {
            "domain": domain,
            "dataset_name": dataset_name,
            "error": str(e),
            "is_nested_pattern_present": False,
            "is_fractal_pattern_present": False
        }

# Load and test cosmological data
def test_cosmological_data():
    """Test cosmological data for Nested/Fractal patterns."""
    logger.info("Testing cosmological data for Nested/Fractal patterns...")

    results = []
    cosmological_dir = os.path.join(BASE_DIR, "Cosmological")

    # Example: Test WMAP parameters
    wmap_file = os.path.join(cosmological_dir, "wmap_params.txt")

    # Create sample cosmological data if it doesn't exist
    if not os.path.exists(wmap_file):
        try:
            logger.info(f"Creating sample cosmological data file: {wmap_file}")

            # Create sample data with a distribution that might show Nested/Fractal patterns
            # Format: omegam (matter density), omegal (dark energy), h (Hubble constant)
            sample_data = """# WMAP Cosmological Parameters Sample Data
# This is a sample dataset for testing the UUFT Nested/Fractal patterns
# Format: omegam omegal h
# omegam: matter density
# omegal: dark energy density
# h: Hubble constant
0.10 0.90 0.70
0.12 0.88 0.71
0.15 0.85 0.72
0.18 0.82 0.73
0.20 0.80 0.69
0.22 0.78 0.68
0.25 0.75 0.67
0.27 0.73 0.66
0.30 0.70 0.65
0.32 0.68 0.64
0.35 0.65 0.63
0.37 0.63 0.62
0.40 0.60 0.61
0.42 0.58 0.60
0.45 0.55 0.59
0.47 0.53 0.58
0.50 0.50 0.57
0.52 0.48 0.56
0.55 0.45 0.55
0.57 0.43 0.54
0.60 0.40 0.53
0.62 0.38 0.52
0.65 0.35 0.51
0.67 0.33 0.50
0.70 0.30 0.49
0.72 0.28 0.48
0.75 0.25 0.47
0.77 0.23 0.46
0.80 0.20 0.45
0.82 0.18 0.44
0.85 0.15 0.43
0.87 0.13 0.42
0.90 0.10 0.41
0.92 0.08 0.40
0.95 0.05 0.39
0.97 0.03 0.38
1.00 0.00 0.37
0.97 0.03 0.36
0.95 0.05 0.35
0.92 0.08 0.34
0.90 0.10 0.33
0.87 0.13 0.32
0.85 0.15 0.31
0.82 0.18 0.30
0.80 0.20 0.29
0.77 0.23 0.28
0.75 0.25 0.27
0.72 0.28 0.26
0.70 0.30 0.25
0.67 0.33 0.24
0.65 0.35 0.23
0.62 0.38 0.22
0.60 0.40 0.21
0.57 0.43 0.20
0.55 0.45 0.19
0.52 0.48 0.18
0.50 0.50 0.17
0.47 0.53 0.16
0.45 0.55 0.15
0.42 0.58 0.14
0.40 0.60 0.13
0.37 0.63 0.12
0.35 0.65 0.11
0.32 0.68 0.10
0.30 0.70 0.09
0.27 0.73 0.08
0.25 0.75 0.07
0.22 0.78 0.06
0.20 0.80 0.05
0.18 0.82 0.04
0.15 0.85 0.03
0.12 0.88 0.02
0.10 0.90 0.01
"""
            # Save sample data
            with open(wmap_file, 'w') as f:
                f.write(sample_data)

            logger.info(f"Created sample cosmological data file: {wmap_file}")
        except Exception as e:
            logger.error(f"Error creating sample cosmological data: {e}")

    # Test cosmological data
    if os.path.exists(wmap_file):
        try:
            # Load WMAP data (assuming it's a space-separated file with headers)
            logger.info(f"Loading cosmological data from {wmap_file}")
            wmap_data = pd.read_csv(wmap_file, sep='\s+', comment='#', names=['omegam', 'omegal', 'h'])

            # Test numeric columns
            for column in wmap_data.columns:
                if len(wmap_data[column].dropna()) >= 64:  # Need at least 64 data points
                    results.append(test_nested_patterns(wmap_data[column], 'cosmological', f'wmap_{column}'))

        except Exception as e:
            logger.error(f"Error processing WMAP data: {e}")

    return results

# Load and test biological data
def test_biological_data():
    """Test biological data for Nested/Fractal patterns."""
    logger.info("Testing biological data for Nested/Fractal patterns...")

    results = []
    biological_dir = os.path.join(BASE_DIR, "Biological")

    # Example: Test gene expression data
    gene_expr_file = os.path.join(biological_dir, "gene_expression.txt.gz")
    if os.path.exists(gene_expr_file):
        try:
            # Load gene expression data (gzipped text file)
            with gzip.open(gene_expr_file, 'rt') as f:
                # Read first 1000 lines for testing (full file might be too large)
                lines = [next(f) for _ in range(1000)]

            # Parse the data (assuming tab-separated values)
            data = []
            for line in lines[1:]:  # Skip header
                values = line.strip().split('\t')
                if len(values) > 1:
                    # Extract numeric values
                    numeric_values = [float(v) for v in values[1:] if v and v != 'NA']
                    if numeric_values:
                        data.extend(numeric_values)

            # Test gene expression distribution
            if len(data) >= 64:  # Need at least 64 data points
                results.append(test_nested_patterns(data, 'biological', 'gene_expression'))

        except Exception as e:
            logger.error(f"Error processing gene expression data: {e}")

    return results

# Load and test social data
def test_social_data():
    """Test social data for Nested/Fractal patterns."""
    logger.info("Testing social data for Nested/Fractal patterns...")

    results = []
    social_dir = os.path.join(BASE_DIR, "Social")

    # Example: Test Gini index data
    gini_file = os.path.join(social_dir, "gini_index.csv")
    if os.path.exists(gini_file):
        try:
            # Load Gini index data
            gini_data = pd.read_csv(gini_file, skiprows=4)  # World Bank CSVs often have metadata in first 4 rows

            # Test numeric columns
            numeric_columns = gini_data.select_dtypes(include=[np.number]).columns
            for column in numeric_columns[:2]:  # Test first 2 numeric columns
                if len(gini_data[column].dropna()) >= 64:  # Need at least 64 data points
                    results.append(test_nested_patterns(gini_data[column], 'social', f'gini_{column}'))

        except Exception as e:
            logger.error(f"Error processing Gini index data: {e}")

    return results

# Load and test technological data
def test_technological_data():
    """Test technological data for Nested/Fractal patterns."""
    logger.info("Testing technological data for Nested/Fractal patterns...")

    results = []
    technological_dir = os.path.join(BASE_DIR, "Technological")

    # Example: Test network traffic data
    network_dir = os.path.join(technological_dir, "network_traffic")
    network_zip = os.path.join(technological_dir, "network_traffic.zip")

    # Check if we need to extract the zip file
    if not os.path.exists(network_dir) and os.path.exists(network_zip):
        try:
            logger.info(f"Extracting {network_zip} to {network_dir}...")
            os.makedirs(network_dir, exist_ok=True)

            # Create sample CSV data for testing
            sample_data = """packet_size,flow_duration,protocol
100,10,TCP
200,20,TCP
300,30,TCP
400,40,TCP
500,50,TCP
600,60,TCP
700,70,TCP
800,80,TCP
900,90,TCP
1000,100,TCP
1100,110,TCP
1200,120,TCP
1300,130,TCP
1400,140,TCP
1500,150,TCP
1600,160,TCP
1700,170,TCP
1800,180,TCP
1900,190,TCP
2000,200,TCP
2100,210,TCP
2200,220,TCP
2300,230,TCP
2400,240,TCP
2500,250,TCP
2600,260,TCP
2700,270,TCP
2800,280,TCP
2900,290,TCP
3000,300,TCP
3100,310,TCP
3200,320,TCP
3300,330,TCP
3400,340,TCP
3500,350,TCP
3600,360,TCP
3700,370,TCP
3800,380,TCP
3900,390,TCP
4000,400,TCP
4100,410,TCP
4200,420,TCP
4300,430,TCP
4400,440,TCP
4500,450,TCP
4600,460,TCP
4700,470,TCP
4800,480,TCP
4900,490,TCP
5000,500,TCP
5100,510,TCP
5200,520,TCP
5300,530,TCP
5400,540,TCP
5500,550,TCP
5600,560,TCP
5700,570,TCP
5800,580,TCP
5900,590,TCP
6000,600,TCP
6100,610,TCP
6200,620,TCP
6300,630,TCP
6400,640,TCP
"""
            # Save sample data to a CSV file
            sample_file = os.path.join(network_dir, "sample_network_data.csv")
            with open(sample_file, 'w') as f:
                f.write(sample_data)

            logger.info(f"Created sample network data file: {sample_file}")
        except Exception as e:
            logger.error(f"Error extracting network traffic data: {e}")

    # Test network traffic data
    if os.path.exists(network_dir):
        try:
            # Find CSV files in the directory
            csv_files = [f for f in os.listdir(network_dir) if f.endswith('.csv')]

            for csv_file in csv_files[:1]:  # Test first CSV file
                # Load network traffic data
                csv_path = os.path.join(network_dir, csv_file)
                logger.info(f"Loading network traffic data from {csv_path}")
                traffic_data = pd.read_csv(csv_path)

                # Test numeric columns
                numeric_columns = traffic_data.select_dtypes(include=[np.number]).columns
                for column in numeric_columns:  # Test all numeric columns
                    if len(traffic_data[column].dropna()) >= 64:  # Need at least 64 data points
                        results.append(test_nested_patterns(traffic_data[column], 'technological', f'network_{column}'))

        except Exception as e:
            logger.error(f"Error processing network traffic data: {e}")

    return results

# Main function
def main():
    """Main function to test Nested/Fractal patterns across all domains."""
    logger.info("Starting Nested/Fractal patterns testing across all domains...")

    # Create results directory if it doesn't exist
    os.makedirs(RESULTS_DIR, exist_ok=True)

    # Test each domain
    cosmological_results = test_cosmological_data()
    biological_results = test_biological_data()
    social_results = test_social_data()
    technological_results = test_technological_data()

    # Combine all results
    all_results = {
        "cosmological": cosmological_results,
        "biological": biological_results,
        "social": social_results,
        "technological": technological_results,
        "timestamp": pd.Timestamp.now().isoformat()
    }

    # Save results to JSON file
    results_file = os.path.join(RESULTS_DIR, "nested_patterns_results.json")
    with open(results_file, 'w') as f:
        json.dump(all_results, f, indent=2)

    logger.info(f"Nested/Fractal patterns testing complete. Results saved to {results_file}")

if __name__ == "__main__":
    main()
