import React from 'react';
import Sidebar from '../components/Sidebar';

export default function CompetitorMatrix() {
  const sidebarItems = [
    { type: 'category', label: 'Comparison Areas', items: [
      { label: 'API-First Architecture', href: '#api-first' },
      { label: 'Composable Ecosystem', href: '#composable' },
      { label: 'AI/ML Capabilities', href: '#ai-ml' },
      { label: 'Partner Revenue Share', href: '#revenue-share' },
      { label: 'Developer SDKs', href: '#sdks' },
      { label: 'Deployment Speed', href: '#deployment' }
    ]},
    { type: 'category', label: 'Competitors', items: [
      { label: 'ServiceNow GRC', href: '#servicenow' },
      { label: 'RSA Archer', href: '#rsa' },
      { label: 'MetricStream', href: '#metricstream' }
    ]},
    { type: 'category', label: 'Resources', items: [
      { label: 'Download Comparison', href: '#download' },
      { label: 'Request Demo', href: '#demo' }
    ]}
  ];

  return (
    <div className="flex flex-col md:flex-row gap-8">
      {/* Sidebar - Hidden on mobile, shown on desktop */}
      <div className="hidden md:block md:w-1/4 lg:w-1/5">
        <div className="sticky top-4">
          <Sidebar items={sidebarItems} title="Competitive Matrix" />
        </div>
      </div>

      {/* Mobile Sidebar Toggle - Shown on mobile only */}
      <div className="md:hidden mb-4">
        <select 
          className="w-full bg-secondary text-white border border-gray-700 rounded p-2"
          onChange={(e) => {
            if (e.target.value) window.location.href = e.target.value;
          }}
          defaultValue=""
        >
          <option value="" disabled>Navigate to...</option>
          {sidebarItems.map((item, index) => (
            item.type === 'category' ? (
              <optgroup key={index} label={item.label}>
                {item.items.map((subItem, subIndex) => (
                  <option key={`${index}-${subIndex}`} value={subItem.href}>
                    {subItem.label}
                  </option>
                ))}
              </optgroup>
            ) : (
              <option key={index} value={item.href}>
                {item.label}
              </option>
            )
          ))}
        </select>
      </div>

      {/* Main Content */}
      <div className="w-full md:w-3/4 lg:w-4/5">
        <h2 className="text-3xl font-bold mb-8 text-center">NovaFuse vs. Legacy GRC Platforms</h2>
        
        <div className="overflow-x-auto">
          <table className="w-full bg-secondary rounded-lg overflow-hidden">
            <thead>
              <tr className="border-b border-gray-700">
                <th className="p-4 text-left">Feature</th>
                <th className="p-4">
                  <div className="h-15 flex items-center justify-center">
                    <div className="font-bold text-blue-500">NovaFuse</div>
                  </div>
                </th>
                <th className="p-4">
                  <div className="h-15 flex items-center justify-center">
                    <div className="font-bold text-green-500">ServiceNow GRC</div>
                  </div>
                </th>
                <th className="p-4">
                  <div className="h-15 flex items-center justify-center">
                    <div className="font-bold text-yellow-500">RSA Archer</div>
                  </div>
                </th>
                <th className="p-4">
                  <div className="h-15 flex items-center justify-center">
                    <div className="font-bold text-red-500">MetricStream</div>
                  </div>
                </th>
              </tr>
            </thead>
            <tbody>
              {/* API-First Architecture */}
              <tr id="api-first" className="border-b border-gray-700">
                <td className="p-4 font-semibold">API-First Architecture</td>
                <td className="p-4 text-center">
                  <div className="flex flex-col items-center">
                    <span className="text-3xl text-green-500">✓</span>
                    <span className="text-sm text-gray-400 mt-1">Native</span>
                  </div>
                </td>
                <td className="p-4 text-center">
                  <div className="flex flex-col items-center">
                    <span className="text-3xl text-red-500">✗</span>
                    <span className="text-sm text-gray-400 mt-1">Add-on</span>
                  </div>
                </td>
                <td className="p-4 text-center">
                  <div className="flex flex-col items-center">
                    <span className="text-3xl text-yellow-500">~</span>
                    <span className="text-sm text-gray-400 mt-1">Partial</span>
                  </div>
                </td>
                <td className="p-4 text-center">
                  <div className="flex flex-col items-center">
                    <span className="text-3xl text-red-500">✗</span>
                    <span className="text-sm text-gray-400 mt-1">Legacy</span>
                  </div>
                </td>
              </tr>
              
              {/* Composable Ecosystem */}
              <tr id="composable" className="border-b border-gray-700">
                <td className="p-4 font-semibold">Composable Ecosystem</td>
                <td className="p-4 text-center">
                  <div className="flex flex-col items-center">
                    <span className="text-3xl text-green-500">✓</span>
                    <span className="text-sm text-gray-400 mt-1">Marketplace Model</span>
                  </div>
                </td>
                <td className="p-4 text-center">
                  <div className="flex flex-col items-center">
                    <span className="text-3xl text-red-500">✗</span>
                    <span className="text-sm text-gray-400 mt-1">Siloed Modules</span>
                  </div>
                </td>
                <td className="p-4 text-center">
                  <div className="flex flex-col items-center">
                    <span className="text-3xl text-red-500">✗</span>
                    <span className="text-sm text-gray-400 mt-1">Siloed</span>
                  </div>
                </td>
                <td className="p-4 text-center">
                  <div className="flex flex-col items-center">
                    <span className="text-3xl text-red-500">✗</span>
                    <span className="text-sm text-gray-400 mt-1">Siloed</span>
                  </div>
                </td>
              </tr>
              
              {/* AI/ML Capabilities */}
              <tr id="ai-ml" className="border-b border-gray-700">
                <td className="p-4 font-semibold">AI/ML Capabilities</td>
                <td className="p-4 text-center">
                  <div className="flex flex-col items-center">
                    <span className="text-3xl text-green-500">✓</span>
                    <span className="text-sm text-gray-400 mt-1">XAI + NLP</span>
                  </div>
                </td>
                <td className="p-4 text-center">
                  <div className="flex flex-col items-center">
                    <span className="text-3xl text-yellow-500">~</span>
                    <span className="text-sm text-gray-400 mt-1">Limited</span>
                  </div>
                </td>
                <td className="p-4 text-center">
                  <div className="flex flex-col items-center">
                    <span className="text-3xl text-yellow-500">~</span>
                    <span className="text-sm text-gray-400 mt-1">Rules-Based</span>
                  </div>
                </td>
                <td className="p-4 text-center">
                  <div className="flex flex-col items-center">
                    <span className="text-3xl text-yellow-500">~</span>
                    <span className="text-sm text-gray-400 mt-1">Rules-Based</span>
                  </div>
                </td>
              </tr>
              
              {/* Partner Revenue Share */}
              <tr id="revenue-share" className="border-b border-gray-700">
                <td className="p-4 font-semibold">Partner Revenue Share</td>
                <td className="p-4 text-center">
                  <div className="flex flex-col items-center">
                    <span className="text-3xl text-green-500">✓</span>
                    <span className="text-sm text-gray-400 mt-1">85%</span>
                  </div>
                </td>
                <td className="p-4 text-center">
                  <div className="flex flex-col items-center">
                    <span className="text-3xl text-red-500">✗</span>
                    <span className="text-sm text-gray-400 mt-1">None</span>
                  </div>
                </td>
                <td className="p-4 text-center">
                  <div className="flex flex-col items-center">
                    <span className="text-3xl text-red-500">✗</span>
                    <span className="text-sm text-gray-400 mt-1">None</span>
                  </div>
                </td>
                <td className="p-4 text-center">
                  <div className="flex flex-col items-center">
                    <span className="text-3xl text-red-500">✗</span>
                    <span className="text-sm text-gray-400 mt-1">None</span>
                  </div>
                </td>
              </tr>
              
              {/* Developer SDKs */}
              <tr id="sdks" className="border-b border-gray-700">
                <td className="p-4 font-semibold">Developer SDKs</td>
                <td className="p-4 text-center">
                  <div className="flex flex-col items-center">
                    <span className="text-3xl text-green-500">✓</span>
                    <span className="text-sm text-gray-400 mt-1">Open-Core</span>
                  </div>
                </td>
                <td className="p-4 text-center">
                  <div className="flex flex-col items-center">
                    <span className="text-3xl text-red-500">✗</span>
                    <span className="text-sm text-gray-400 mt-1">Proprietary</span>
                  </div>
                </td>
                <td className="p-4 text-center">
                  <div className="flex flex-col items-center">
                    <span className="text-3xl text-red-500">✗</span>
                    <span className="text-sm text-gray-400 mt-1">Proprietary</span>
                  </div>
                </td>
                <td className="p-4 text-center">
                  <div className="flex flex-col items-center">
                    <span className="text-3xl text-red-500">✗</span>
                    <span className="text-sm text-gray-400 mt-1">Proprietary</span>
                  </div>
                </td>
              </tr>
              
              {/* Deployment Speed */}
              <tr id="deployment">
                <td className="p-4 font-semibold">Deployment Speed</td>
                <td className="p-4 text-center">
                  <div className="flex flex-col items-center">
                    <span className="text-3xl text-green-500">✓</span>
                    <span className="text-sm text-gray-400 mt-1">Weeks</span>
                  </div>
                </td>
                <td className="p-4 text-center">
                  <div className="flex flex-col items-center">
                    <span className="text-3xl text-red-500">✗</span>
                    <span className="text-sm text-gray-400 mt-1">Months</span>
                  </div>
                </td>
                <td className="p-4 text-center">
                  <div className="flex flex-col items-center">
                    <span className="text-3xl text-red-500">✗</span>
                    <span className="text-sm text-gray-400 mt-1">Months</span>
                  </div>
                </td>
                <td className="p-4 text-center">
                  <div className="flex flex-col items-center">
                    <span className="text-3xl text-red-500">✗</span>
                    <span className="text-sm text-gray-400 mt-1">Months</span>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        
        {/* Bottom Section: Key Takeaways */}
        <div className="bg-secondary p-6 rounded-lg mt-8">
          <h3 className="text-2xl font-bold mb-4 text-center">Key Competitive Advantages</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="p-4 border border-blue-600 rounded-lg">
              <h4 className="text-xl font-semibold mb-2 text-blue-400">API-First Architecture</h4>
              <p className="text-gray-300">While competitors bolt on APIs as an afterthought, NovaFuse is built API-first from the ground up. This enables rapid integration, flexibility, and a true ecosystem approach that legacy vendors can't match.</p>
            </div>
            
            <div className="p-4 border border-blue-600 rounded-lg">
              <h4 className="text-xl font-semibold mb-2 text-blue-400">Partner Revenue Model</h4>
              <p className="text-gray-300">NovaFuse's 85% revenue share creates a powerful incentive for partners to join and actively promote our platform. Legacy vendors offer no revenue sharing, treating partners as an expense rather than a growth driver.</p>
            </div>
            
            <div className="p-4 border border-blue-600 rounded-lg">
              <h4 className="text-xl font-semibold mb-2 text-blue-400">AI/ML Capabilities</h4>
              <p className="text-gray-300">NovaFuse leverages explainable AI (XAI) and natural language processing (NLP) to deliver intelligent insights across the GRC ecosystem. Competitors rely on basic rules-based approaches that lack true intelligence.</p>
            </div>
            
            <div className="p-4 border border-blue-600 rounded-lg">
              <h4 className="text-xl font-semibold mb-2 text-blue-400">Open-Core Approach</h4>
              <p className="text-gray-300">NovaFuse's open-core model with FuseCore creates a developer-friendly ecosystem that encourages innovation and adoption. Legacy vendors maintain closed, proprietary systems that limit extensibility and customization.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
