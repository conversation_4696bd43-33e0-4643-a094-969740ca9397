/**
 * Analytics Validation Schemas
 * 
 * This file contains validation schemas for analytics-related endpoints.
 */

const Joi = require('joi');
const { commonSchemas } = require('../common/commonSchemas');

/**
 * Get user analytics schema
 */
const getUserAnalyticsSchema = {
  query: Joi.object({
    startDate: Joi.date().iso().optional(),
    endDate: Joi.date().iso().min(Joi.ref('startDate')).optional(),
    userId: commonSchemas.id.optional(),
    teamId: commonSchemas.id.optional(),
    groupBy: Joi.string().valid('hour', 'day', 'week', 'month').default('day'),
    metrics: Joi.array().items(Joi.string().valid('logins', 'api_calls', 'active_time')).default(['logins', 'api_calls', 'active_time'])
  })
};

/**
 * Get connector analytics schema
 */
const getConnectorAnalyticsSchema = {
  query: Joi.object({
    startDate: Joi.date().iso().optional(),
    endDate: Joi.date().iso().min(Joi.ref('startDate')).optional(),
    connectorId: commonSchemas.id.optional(),
    connectorType: commonSchemas.connectorType.optional(),
    groupBy: Joi.string().valid('hour', 'day', 'week', 'month').default('day'),
    metrics: Joi.array().items(Joi.string().valid('executions', 'success_rate', 'average_duration', 'error_rate')).default(['executions', 'success_rate', 'average_duration', 'error_rate'])
  })
};

/**
 * Get compliance analytics schema
 */
const getComplianceAnalyticsSchema = {
  query: Joi.object({
    startDate: Joi.date().iso().optional(),
    endDate: Joi.date().iso().min(Joi.ref('startDate')).optional(),
    framework: Joi.string().optional(),
    controlId: Joi.string().optional(),
    groupBy: Joi.string().valid('day', 'week', 'month', 'quarter').default('day'),
    metrics: Joi.array().items(Joi.string().valid('compliance_score', 'findings', 'remediation_rate')).default(['compliance_score', 'findings', 'remediation_rate'])
  })
};

/**
 * Get predictive analytics schema
 */
const getPredictiveAnalyticsSchema = {
  query: Joi.object({
    metric: Joi.string().required(),
    horizon: Joi.number().integer().min(1).max(365).default(30),
    confidenceInterval: Joi.number().min(0.1).max(0.99).default(0.95),
    modelType: Joi.string().valid('auto', 'arima', 'exponential_smoothing', 'prophet', 'neural_network').default('auto')
  })
};

/**
 * Get real-time analytics schema
 */
const getRealTimeAnalyticsSchema = {
  query: Joi.object({
    metrics: Joi.array().items(Joi.string().valid('active_users', 'api_calls_per_minute', 'error_rate', 'response_time')).default(['active_users', 'api_calls_per_minute', 'error_rate', 'response_time'])
  })
};

/**
 * Get custom analytics schema
 */
const getCustomAnalyticsSchema = {
  body: Joi.object({
    metrics: Joi.array().items(Joi.string()).min(1).required(),
    dimensions: Joi.array().items(Joi.string()).optional(),
    filters: Joi.object().optional(),
    startDate: Joi.date().iso().optional(),
    endDate: Joi.date().iso().min(Joi.ref('startDate')).optional(),
    groupBy: Joi.string().valid('hour', 'day', 'week', 'month', 'quarter', 'year').default('day'),
    limit: Joi.number().integer().min(1).max(10000).default(1000),
    sort: Joi.object().pattern(
      Joi.string(),
      Joi.string().valid('asc', 'desc')
    ).optional()
  })
};

/**
 * Get analytics dashboard schema
 */
const getAnalyticsDashboardSchema = {
  query: Joi.object({
    startDate: Joi.date().iso().optional(),
    endDate: Joi.date().iso().min(Joi.ref('startDate')).optional(),
    refresh: Joi.boolean().default(false)
  })
};

/**
 * Export analytics schema
 */
const exportAnalyticsSchema = {
  query: Joi.object({
    type: Joi.string().valid('user', 'connector', 'compliance', 'custom').required(),
    format: Joi.string().valid('csv', 'json', 'excel').default('csv'),
    startDate: Joi.date().iso().optional(),
    endDate: Joi.date().iso().min(Joi.ref('startDate')).optional(),
    userId: commonSchemas.id.optional(),
    teamId: commonSchemas.id.optional(),
    connectorId: commonSchemas.id.optional(),
    connectorType: commonSchemas.connectorType.optional(),
    framework: Joi.string().optional(),
    controlId: Joi.string().optional(),
    metrics: Joi.array().items(Joi.string()).optional(),
    groupBy: Joi.string().valid('hour', 'day', 'week', 'month', 'quarter', 'year').default('day')
  })
};

/**
 * Get user-specific analytics schema
 */
const getUserSpecificAnalyticsSchema = {
  params: Joi.object({
    userId: commonSchemas.id.required()
  }),
  query: Joi.object({
    startDate: Joi.date().iso().optional(),
    endDate: Joi.date().iso().min(Joi.ref('startDate')).optional(),
    groupBy: Joi.string().valid('hour', 'day', 'week', 'month').default('day'),
    metrics: Joi.array().items(Joi.string().valid('logins', 'api_calls', 'active_time')).default(['logins', 'api_calls', 'active_time'])
  })
};

/**
 * Get team-specific analytics schema
 */
const getTeamSpecificAnalyticsSchema = {
  params: Joi.object({
    teamId: commonSchemas.id.required()
  }),
  query: Joi.object({
    startDate: Joi.date().iso().optional(),
    endDate: Joi.date().iso().min(Joi.ref('startDate')).optional(),
    groupBy: Joi.string().valid('hour', 'day', 'week', 'month').default('day'),
    metrics: Joi.array().items(Joi.string().valid('logins', 'api_calls', 'active_time')).default(['logins', 'api_calls', 'active_time'])
  })
};

/**
 * Get connector-specific analytics schema
 */
const getConnectorSpecificAnalyticsSchema = {
  params: Joi.object({
    connectorId: commonSchemas.id.required()
  }),
  query: Joi.object({
    startDate: Joi.date().iso().optional(),
    endDate: Joi.date().iso().min(Joi.ref('startDate')).optional(),
    groupBy: Joi.string().valid('hour', 'day', 'week', 'month').default('day'),
    metrics: Joi.array().items(Joi.string().valid('executions', 'success_rate', 'average_duration', 'error_rate')).default(['executions', 'success_rate', 'average_duration', 'error_rate'])
  })
};

/**
 * Get connector-type-specific analytics schema
 */
const getConnectorTypeSpecificAnalyticsSchema = {
  params: Joi.object({
    connectorType: commonSchemas.connectorType.required()
  }),
  query: Joi.object({
    startDate: Joi.date().iso().optional(),
    endDate: Joi.date().iso().min(Joi.ref('startDate')).optional(),
    groupBy: Joi.string().valid('hour', 'day', 'week', 'month').default('day'),
    metrics: Joi.array().items(Joi.string().valid('executions', 'success_rate', 'average_duration', 'error_rate')).default(['executions', 'success_rate', 'average_duration', 'error_rate'])
  })
};

/**
 * Get compliance-specific analytics schema
 */
const getComplianceSpecificAnalyticsSchema = {
  params: Joi.object({
    framework: Joi.string().required()
  }),
  query: Joi.object({
    startDate: Joi.date().iso().optional(),
    endDate: Joi.date().iso().min(Joi.ref('startDate')).optional(),
    controlId: Joi.string().optional(),
    groupBy: Joi.string().valid('day', 'week', 'month', 'quarter').default('day'),
    metrics: Joi.array().items(Joi.string().valid('compliance_score', 'findings', 'remediation_rate')).default(['compliance_score', 'findings', 'remediation_rate'])
  })
};

/**
 * Get compliance-control-specific analytics schema
 */
const getComplianceControlSpecificAnalyticsSchema = {
  params: Joi.object({
    framework: Joi.string().required(),
    controlId: Joi.string().required()
  }),
  query: Joi.object({
    startDate: Joi.date().iso().optional(),
    endDate: Joi.date().iso().min(Joi.ref('startDate')).optional(),
    groupBy: Joi.string().valid('day', 'week', 'month', 'quarter').default('day'),
    metrics: Joi.array().items(Joi.string().valid('compliance_score', 'findings', 'remediation_rate')).default(['compliance_score', 'findings', 'remediation_rate'])
  })
};

module.exports = {
  getUserAnalyticsSchema,
  getConnectorAnalyticsSchema,
  getComplianceAnalyticsSchema,
  getPredictiveAnalyticsSchema,
  getRealTimeAnalyticsSchema,
  getCustomAnalyticsSchema,
  getAnalyticsDashboardSchema,
  exportAnalyticsSchema,
  getUserSpecificAnalyticsSchema,
  getTeamSpecificAnalyticsSchema,
  getConnectorSpecificAnalyticsSchema,
  getConnectorTypeSpecificAnalyticsSchema,
  getComplianceSpecificAnalyticsSchema,
  getComplianceControlSpecificAnalyticsSchema
};
