/**
 * NovaFuse API Superstore - Error Handler Middleware
 * 
 * This middleware handles errors for the API Gateway.
 */

const winston = require('winston');
const config = require('../config');

// Create logger
const logger = winston.createLogger({
  level: config.logging.level,
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  defaultMeta: { service: 'api-gateway' },
  transports: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    }),
    new winston.transports.File({ 
      filename: `${config.logging.directory}/error.log`, 
      level: 'error' 
    }),
    new winston.transports.File({ 
      filename: `${config.logging.directory}/combined.log` 
    })
  ]
});

/**
 * Error handler middleware
 * @param {Error} err - Error object
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const errorHandler = (err, req, res, next) => {
  // Log error
  logger.error(`Error: ${err.message}`, {
    error: {
      name: err.name,
      message: err.message,
      stack: err.stack
    },
    request: {
      method: req.method,
      url: req.originalUrl,
      headers: req.headers,
      body: req.body
    }
  });
  
  // Set status code
  const statusCode = err.statusCode || 500;
  
  // Send error response
  res.status(statusCode).json({
    success: false,
    error: err.name || 'Internal Server Error',
    message: err.message || 'Something went wrong',
    ...(config.server.env === 'development' && { stack: err.stack })
  });
};

/**
 * Not found middleware
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const notFound = (req, res) => {
  logger.warn(`Not found: ${req.originalUrl}`);
  
  res.status(404).json({
    success: false,
    error: 'Not Found',
    message: `The requested resource at ${req.originalUrl} was not found`
  });
};

module.exports = {
  errorHandler,
  notFound
};
