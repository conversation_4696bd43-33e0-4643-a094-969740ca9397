/**
 * NovaUI Dashboard Page
 *
 * This is the main dashboard page for the NovaUI application.
 */

import { useContext } from 'react';
import Link from 'next/link';
import Head from 'next/head';
import { ProductContext, PRODUCTS } from '../../packages/feature-flags/ProductContext';
import { FeatureFlaggedComponent, UpgradePrompt } from '../../packages/ui-components/FeatureFlaggedComponent';
import Layout from '../../shared/layouts/Layout';

/**
 * Dashboard Page
 * @returns {React.ReactNode} - The rendered component
 */
export default function Dashboard() {
  const { product, setProduct } = useContext(ProductContext);

  const platforms = [
    {
      name: 'NovaAlign Studio',
      href: '/nova-align',
      icon: '🎯',
      description: 'AI Alignment & Safety Monitoring'
    },
    {
      name: 'NovaMatrix',
      href: '/novamatrix',
      icon: '🌌',
      description: 'Pentagonal Consciousness Fusion Platform'
    },
    {
      name: 'NovaFold Enhanced',
      href: '/novafold',
      icon: '🧬',
      description: 'Consciousness-Enhanced Protein Folding'
    },
    {
      name: 'NECE',
      href: '/nece',
      icon: '⚗️',
      description: 'Neuroemotive-Compatible Engine for Chemistry'
    }
  ];

  return (
    <Layout>
      <Head>
        <title>NovaUI Dashboard</title>
        <meta name="description" content="NovaUI Dashboard" />
      </Head>

      <div className="min-h-screen bg-gray-100">
        <header className="bg-white shadow">
          <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center">
              <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
              <div className="flex space-x-4">
                <select
                  className="block w-48 pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                  value={product}
                  onChange={(e) => setProduct(e.target.value)}
                >
                  {Object.entries(PRODUCTS).map(([value, { name }]) => (
                    <option key={value} value={value}>
                      {name}
                    </option>
                  ))}
                </select>
                <button
                  type="button"
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  NovaAssistAI
                </button>
              </div>
            </div>
          </div>
        </header>

        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Platform Cards */}
            <div className="bg-white p-6 rounded-lg shadow">
              <h2 className="text-xl font-semibold mb-4">Platforms</h2>
              <div className="space-y-4">
                {platforms.map((platform) => (
                  <Link key={platform.name} href={platform.href}>
                    <div className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors">
                      <div className="flex items-center space-x-4">
                        <span className="text-2xl">{platform.icon}</span>
                        <div>
                          <h3 className="font-medium text-gray-900">{platform.name}</h3>
                          <p className="text-sm text-gray-500">{platform.description}</p>
                        </div>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            </div>

            {/* Quick Stats */}
            <div className="space-y-6">
              <div className="bg-white p-6 rounded-lg shadow">
                <h2 className="text-xl font-semibold mb-4">Quick Stats</h2>
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <p className="text-sm font-medium text-blue-700">Active Projects</p>
                    <p className="text-2xl font-bold text-blue-900">24</p>
                  </div>
                  <div className="bg-green-50 p-4 rounded-lg">
                    <p className="text-sm font-medium text-green-700">Tasks Completed</p>
                    <p className="text-2xl font-bold text-green-900">156</p>
                  </div>
                </div>
              </div>

              <div className="bg-white p-6 rounded-lg shadow">
                <h2 className="text-xl font-semibold mb-4">Recent Activity</h2>
                <div className="space-y-4">
                  {[
                    { action: 'Created new project', time: '2 min ago' },
                    { action: 'Updated settings', time: '1 hour ago' },
                    { action: 'Completed task', time: '3 hours ago' }
                  ].map((item, index) => (
                    <div key={index} className="flex items-start space-x-3">
                      <div className="flex-shrink-0 h-2 w-2 mt-1.5 rounded-full bg-blue-500"></div>
                      <div>
                        <p className="text-sm font-medium text-gray-900">{item.action}</p>
                        <p className="text-xs text-gray-500">{item.time}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </Layout>
  );
}
