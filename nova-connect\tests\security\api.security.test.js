/**
 * Security tests for the NovaConnect API
 */

const { expect } = require('chai');
const supertest = require('supertest');
const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const jwt = require('jsonwebtoken');

// Import the app
const app = require('../../server');

describe('NovaConnect API Security Tests', () => {
  let mongoServer;
  let request;
  let validApiKey;
  let validJwtToken;

  before(async () => {
    // Create an in-memory MongoDB server
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();

    // Connect to the in-memory database
    await mongoose.connect(mongoUri, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });

    // Create a supertest request object
    request = supertest(app);

    // Create a valid API key for testing
    validApiKey = 'test-api-key-12345';

    // Create a valid JWT token for testing
    const jwtSecret = process.env.JWT_SECRET || 'test-jwt-secret';
    validJwtToken = jwt.sign(
      { 
        userId: 'test-user-id',
        role: 'admin'
      },
      jwtSecret,
      { expiresIn: '1h' }
    );
  });

  after(async () => {
    // Disconnect from the database
    await mongoose.disconnect();
    // Stop the in-memory MongoDB server
    await mongoServer.stop();
  });

  describe('Authentication', () => {
    it('should reject requests without authentication', async () => {
      const response = await request.get('/api/connectors');
      expect(response.status).to.equal(401);
    });

    it('should accept requests with valid API key', async () => {
      const response = await request
        .get('/api/connectors')
        .set('X-API-Key', validApiKey);
      
      expect(response.status).to.equal(200);
    });

    it('should reject requests with invalid API key', async () => {
      const response = await request
        .get('/api/connectors')
        .set('X-API-Key', 'invalid-api-key');
      
      expect(response.status).to.equal(401);
    });

    it('should accept requests with valid JWT token', async () => {
      const response = await request
        .get('/api/connectors')
        .set('Authorization', `Bearer ${validJwtToken}`);
      
      expect(response.status).to.equal(200);
    });

    it('should reject requests with invalid JWT token', async () => {
      const response = await request
        .get('/api/connectors')
        .set('Authorization', 'Bearer invalid-token');
      
      expect(response.status).to.equal(401);
    });

    it('should reject requests with expired JWT token', async () => {
      // Create an expired JWT token
      const jwtSecret = process.env.JWT_SECRET || 'test-jwt-secret';
      const expiredToken = jwt.sign(
        { 
          userId: 'test-user-id',
          role: 'admin'
        },
        jwtSecret,
        { expiresIn: '-1h' } // Expired 1 hour ago
      );

      const response = await request
        .get('/api/connectors')
        .set('Authorization', `Bearer ${expiredToken}`);
      
      expect(response.status).to.equal(401);
    });
  });

  describe('Authorization', () => {
    it('should allow admin users to access admin endpoints', async () => {
      // Create an admin JWT token
      const jwtSecret = process.env.JWT_SECRET || 'test-jwt-secret';
      const adminToken = jwt.sign(
        { 
          userId: 'admin-user-id',
          role: 'admin'
        },
        jwtSecret,
        { expiresIn: '1h' }
      );

      const response = await request
        .get('/api/admin/users')
        .set('Authorization', `Bearer ${adminToken}`);
      
      expect(response.status).to.equal(200);
    });

    it('should deny regular users access to admin endpoints', async () => {
      // Create a regular user JWT token
      const jwtSecret = process.env.JWT_SECRET || 'test-jwt-secret';
      const userToken = jwt.sign(
        { 
          userId: 'regular-user-id',
          role: 'user'
        },
        jwtSecret,
        { expiresIn: '1h' }
      );

      const response = await request
        .get('/api/admin/users')
        .set('Authorization', `Bearer ${userToken}`);
      
      expect(response.status).to.equal(403);
    });
  });

  describe('Rate Limiting', () => {
    it('should rate limit excessive requests', async () => {
      // Make multiple requests in quick succession
      const requests = [];
      for (let i = 0; i < 110; i++) {
        requests.push(
          request
            .get('/api/connectors')
            .set('X-API-Key', validApiKey)
        );
      }

      // Wait for all requests to complete
      const responses = await Promise.all(requests);
      
      // At least one request should be rate limited
      const rateLimitedResponses = responses.filter(r => r.status === 429);
      expect(rateLimitedResponses.length).to.be.greaterThan(0);
    });
  });

  describe('Input Validation', () => {
    it('should reject invalid input', async () => {
      const invalidConnector = {
        // Missing required fields
        type: 'http'
      };

      const response = await request
        .post('/api/connectors')
        .set('X-API-Key', validApiKey)
        .send(invalidConnector);
      
      expect(response.status).to.equal(400);
      expect(response.body).to.have.property('error');
    });

    it('should sanitize input to prevent XSS', async () => {
      const xssConnector = {
        name: '<script>alert("XSS")</script>',
        type: 'http',
        description: 'Test connector with XSS payload'
      };

      const response = await request
        .post('/api/connectors')
        .set('X-API-Key', validApiKey)
        .send(xssConnector);
      
      expect(response.status).to.equal(201);
      expect(response.body.name).to.not.include('<script>');
    });

    it('should prevent SQL injection', async () => {
      const sqlInjectionQuery = "'; DROP TABLE users; --";
      
      const response = await request
        .get(`/api/connectors?query=${sqlInjectionQuery}`)
        .set('X-API-Key', validApiKey);
      
      expect(response.status).to.equal(200);
      // The query should be sanitized and not cause any errors
    });
  });

  describe('Security Headers', () => {
    it('should include security headers in responses', async () => {
      const response = await request
        .get('/health');
      
      expect(response.headers).to.have.property('x-content-type-options');
      expect(response.headers['x-content-type-options']).to.equal('nosniff');
      
      expect(response.headers).to.have.property('x-frame-options');
      expect(response.headers['x-frame-options']).to.equal('DENY');
      
      expect(response.headers).to.have.property('content-security-policy');
      
      expect(response.headers).to.have.property('strict-transport-security');
    });
  });
});
