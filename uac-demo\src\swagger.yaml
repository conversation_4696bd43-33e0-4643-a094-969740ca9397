openapi: 3.0.0
info:
  title: NovaFuse Universal API Connector (UAC) Demo
  description: |
    This is a demo of the NovaFuse Universal API Connector (UAC).
    The UAC is a powerful tool for connecting to various APIs and applying compliance rules in real-time.
  version: 0.1.0
  contact:
    name: NovaFuse Support
    email: <EMAIL>
    url: https://novafuse.io

servers:
  - url: http://localhost:3030
    description: Local development server

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
  
  schemas:
    Error:
      type: object
      properties:
        error:
          type: string
          description: Error message
      required:
        - error
    
    LoginRequest:
      type: object
      properties:
        username:
          type: string
          description: Username
        password:
          type: string
          description: Password
      required:
        - username
        - password
    
    LoginResponse:
      type: object
      properties:
        token:
          type: string
          description: JWT token
        user:
          type: object
          properties:
            username:
              type: string
            role:
              type: string
            name:
              type: string
            createdAt:
              type: string
              format: date-time
      required:
        - token
        - user
    
    ApiConfig:
      type: object
      properties:
        baseUrl:
          type: string
          description: Base URL for the API
        type:
          type: string
          description: API type (REST, GraphQL, etc.)
        authMethod:
          type: string
          description: Authentication method
        dataFormat:
          type: string
          description: Data format
        headers:
          type: object
          description: Default headers
      required:
        - baseUrl
    
    ComplianceRule:
      type: object
      properties:
        framework:
          type: string
          description: Compliance framework
        description:
          type: string
          description: Rule description
        check:
          type: string
          description: Check function as string
        logData:
          type: boolean
          description: Whether to log data
      required:
        - framework
        - check

security:
  - BearerAuth: []

paths:
  /:
    get:
      summary: Get API information
      description: Returns basic information about the API
      security: []
      responses:
        '200':
          description: API information
          content:
            application/json:
              schema:
                type: object
                properties:
                  name:
                    type: string
                  version:
                    type: string
                  status:
                    type: string
                  documentation:
                    type: string
  
  /api/status:
    get:
      summary: Get API status
      description: Returns the current status of the API
      security: []
      responses:
        '200':
          description: API status
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  timestamp:
                    type: string
                    format: date-time
                  version:
                    type: string
  
  /api/auth/login:
    post:
      summary: Login
      description: Authenticate a user and get a JWT token
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoginRequest'
      responses:
        '200':
          description: Login successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoginResponse'
        '401':
          description: Login failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  
  /api/auth/me:
    get:
      summary: Get current user
      description: Get information about the currently authenticated user
      responses:
        '200':
          description: User information
          content:
            application/json:
              schema:
                type: object
                properties:
                  username:
                    type: string
                  role:
                    type: string
                  name:
                    type: string
                  createdAt:
                    type: string
                    format: date-time
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  
  /api/connector/apis:
    get:
      summary: List APIs
      description: Get a list of all registered APIs
      responses:
        '200':
          description: List of APIs
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ApiConfig'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    
    post:
      summary: Register API
      description: Register a new API configuration
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ApiConfig'
      responses:
        '201':
          description: API registered successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
        '400':
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  
  /api/compliance/rules:
    get:
      summary: List compliance rules
      description: Get a list of all registered compliance rules
      parameters:
        - name: framework
          in: query
          description: Filter by compliance framework
          required: false
          schema:
            type: string
      responses:
        '200':
          description: List of compliance rules
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ComplianceRule'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    
    post:
      summary: Register compliance rule
      description: Register a new compliance rule
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ComplianceRule'
      responses:
        '201':
          description: Compliance rule registered successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
        '400':
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  
  /api/demo/init:
    post:
      summary: Initialize demo data
      description: Initialize demo APIs and compliance rules
      responses:
        '200':
          description: Demo data initialized successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  
  /api/demo/hipaa-check:
    post:
      summary: HIPAA compliance check
      description: Check if patient data complies with HIPAA regulations
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                patientData:
                  type: object
                  description: Patient data to check
      responses:
        '200':
          description: Compliance check results
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                  compliance:
                    type: object
        '400':
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  
  /api/demo/gdpr-check:
    post:
      summary: GDPR compliance check
      description: Check if user data complies with GDPR regulations
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                userData:
                  type: object
                  description: User data to check
      responses:
        '200':
          description: Compliance check results
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                  compliance:
                    type: object
        '400':
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  
  /api/demo/pci-check:
    post:
      summary: PCI DSS compliance check
      description: Check if payment data complies with PCI DSS regulations
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                paymentData:
                  type: object
                  description: Payment data to check
      responses:
        '200':
          description: Compliance check results
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                  compliance:
                    type: object
        '400':
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  
  /api/demo/multi-api:
    post:
      summary: Multi-API demo
      description: Demonstrate calling multiple APIs and applying compliance checks
      responses:
        '200':
          description: Multi-API results
          content:
            application/json:
              schema:
                type: object
                properties:
                  apiResults:
                    type: object
                  complianceResults:
                    type: object
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
