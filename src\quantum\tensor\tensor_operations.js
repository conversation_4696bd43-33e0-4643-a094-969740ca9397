/**
 * Tensor Operations
 * 
 * This module provides tensor operations for the Comphyological Tensor Core.
 * It implements various tensor operations such as tensor product, direct sum,
 * and other operations needed for the Comphyological framework.
 */

const { performance } = require('perf_hooks');

/**
 * TensorOperations class
 * 
 * Provides tensor operations for the Comphyological Tensor Core.
 */
class TensorOperations {
  /**
   * Constructor
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    this.options = {
      enableLogging: true,
      precision: 6, // Decimal precision
      useOptimizedOperations: true, // Use optimized operations when possible
      ...options
    };

    // Constants
    this.PI = Math.PI;
    this.PI_103 = 3141.59; // π103 scaling factor
    this.PHI = 1.618033988749895; // Golden ratio
    this.PHI_CONJUGATE = 0.618033988749895; // Golden ratio conjugate

    if (this.options.enableLogging) {
      console.log('TensorOperations initialized with options:', {
        precision: this.options.precision,
        useOptimizedOperations: this.options.useOptimizedOperations
      });
    }
  }

  /**
   * Tensor product (Kronecker product)
   * @param {Array|number} a - First tensor
   * @param {Array|number} b - Second tensor
   * @returns {Array|number} - Tensor product result
   */
  tensorProduct(a, b) {
    const startTime = performance.now();

    let result;

    // Handle scalar inputs
    if (typeof a === 'number' && typeof b === 'number') {
      result = a * b;
    }
    // Handle array inputs
    else if (Array.isArray(a) && Array.isArray(b)) {
      result = this._arrayTensorProduct(a, b);
    }
    // Handle mixed inputs
    else if (typeof a === 'number' && Array.isArray(b)) {
      result = b.map(val => this._round(a * val));
    }
    else if (Array.isArray(a) && typeof b === 'number') {
      result = a.map(val => this._round(val * b));
    }
    // Handle object inputs
    else if (typeof a === 'object' && typeof b === 'object') {
      result = this._objectTensorProduct(a, b);
    }
    else {
      throw new Error('Unsupported tensor types for tensor product');
    }

    if (this.options.enableLogging) {
      const endTime = performance.now();
      console.log(`Tensor product computed in ${endTime - startTime}ms`);
    }

    return result;
  }

  /**
   * Direct sum (concatenation)
   * @param {Array|number} a - First tensor
   * @param {Array|number} b - Second tensor
   * @returns {Array} - Direct sum result
   */
  directSum(a, b) {
    const startTime = performance.now();

    let result;

    // Handle scalar inputs
    if (typeof a === 'number' && typeof b === 'number') {
      result = [a, b];
    }
    // Handle array inputs
    else if (Array.isArray(a) && Array.isArray(b)) {
      result = [...a, ...b];
    }
    // Handle mixed inputs
    else if (typeof a === 'number' && Array.isArray(b)) {
      result = [a, ...b];
    }
    else if (Array.isArray(a) && typeof b === 'number') {
      result = [...a, b];
    }
    // Handle object inputs
    else if (typeof a === 'object' && typeof b === 'object') {
      result = this._objectDirectSum(a, b);
    }
    else {
      throw new Error('Unsupported tensor types for direct sum');
    }

    if (this.options.enableLogging) {
      const endTime = performance.now();
      console.log(`Direct sum computed in ${endTime - startTime}ms`);
    }

    return result;
  }

  /**
   * Scale a tensor by a scalar
   * @param {Array|number} tensor - Tensor to scale
   * @param {number} scalar - Scalar value
   * @returns {Array|number} - Scaled tensor
   */
  scaleTensor(tensor, scalar) {
    const startTime = performance.now();

    let result;

    // Handle scalar input
    if (typeof tensor === 'number') {
      result = this._round(tensor * scalar);
    }
    // Handle array input
    else if (Array.isArray(tensor)) {
      result = tensor.map(val => this._round(val * scalar));
    }
    // Handle object input
    else if (typeof tensor === 'object') {
      result = this._objectScaleTensor(tensor, scalar);
    }
    else {
      throw new Error('Unsupported tensor type for scaling');
    }

    if (this.options.enableLogging) {
      const endTime = performance.now();
      console.log(`Tensor scaling computed in ${endTime - startTime}ms`);
    }

    return result;
  }

  /**
   * Tensor product for arrays
   * @param {Array} a - First array
   * @param {Array} b - Second array
   * @returns {Array} - Tensor product result
   * @private
   */
  _arrayTensorProduct(a, b) {
    const result = [];

    for (let i = 0; i < a.length; i++) {
      for (let j = 0; j < b.length; j++) {
        result.push(this._round(a[i] * b[j]));
      }
    }

    return result;
  }

  /**
   * Tensor product for objects
   * @param {Object} a - First object
   * @param {Object} b - Second object
   * @returns {Object} - Tensor product result
   * @private
   */
  _objectTensorProduct(a, b) {
    // Extract values from objects
    const valuesA = Object.values(a).filter(val => typeof val === 'number');
    const valuesB = Object.values(b).filter(val => typeof val === 'number');

    // Compute tensor product of values
    const productValues = this._arrayTensorProduct(valuesA, valuesB);

    // Create result object
    return {
      type: 'tensor_product',
      values: productValues,
      dimensions: valuesA.length * valuesB.length,
      tensorA: a,
      tensorB: b,
      timestamp: Date.now()
    };
  }

  /**
   * Direct sum for objects
   * @param {Object} a - First object
   * @param {Object} b - Second object
   * @returns {Object} - Direct sum result
   * @private
   */
  _objectDirectSum(a, b) {
    // Extract values from objects
    const valuesA = Object.values(a).filter(val => typeof val === 'number');
    const valuesB = Object.values(b).filter(val => typeof val === 'number');

    // Compute direct sum of values
    const sumValues = [...valuesA, ...valuesB];

    // Create result object
    return {
      type: 'direct_sum',
      values: sumValues,
      dimensions: valuesA.length + valuesB.length,
      tensorA: a,
      tensorB: b,
      timestamp: Date.now()
    };
  }

  /**
   * Scale tensor for objects
   * @param {Object} tensor - Object tensor
   * @param {number} scalar - Scalar value
   * @returns {Object} - Scaled tensor
   * @private
   */
  _objectScaleTensor(tensor, scalar) {
    // Extract values from object
    const values = Object.values(tensor).filter(val => typeof val === 'number');

    // Scale values
    const scaledValues = values.map(val => this._round(val * scalar));

    // Create result object
    return {
      type: 'scaled_tensor',
      values: scaledValues,
      dimensions: values.length,
      originalTensor: tensor,
      scalar,
      timestamp: Date.now()
    };
  }

  /**
   * Round a number to the specified precision
   * @param {number} value - Value to round
   * @returns {number} - Rounded value
   * @private
   */
  _round(value) {
    const factor = Math.pow(10, this.options.precision);
    return Math.round(value * factor) / factor;
  }

  /**
   * Create a tensor from an array
   * @param {Array} array - Array of values
   * @returns {Object} - Tensor object
   */
  createTensor(array) {
    return {
      type: 'tensor',
      values: array,
      dimensions: array.length,
      timestamp: Date.now()
    };
  }

  /**
   * Create a tensor from a domain engine
   * @param {Object} engine - Domain engine
   * @param {string} domain - Domain name
   * @returns {Object} - Tensor object
   */
  createTensorFromEngine(engine, domain) {
    // Extract relevant values from engine
    const values = this._extractEngineValues(engine, domain);

    return this.createTensor(values);
  }

  /**
   * Extract values from a domain engine
   * @param {Object} engine - Domain engine
   * @param {string} domain - Domain name
   * @returns {Array} - Array of values
   * @private
   */
  _extractEngineValues(engine, domain) {
    const values = [];

    switch (domain) {
      case 'csde':
        // Extract CSDE values
        values.push(
          engine.governance || 0,
          engine.data || 0,
          engine.action || 0,
          engine.confidence || 0
        );
        break;
      case 'csfe':
        // Extract CSFE values
        values.push(
          engine.risk || 0,
          engine.finance || 0,
          engine.action || 0,
          engine.confidence || 0
        );
        break;
      case 'csme':
        // Extract CSME values
        values.push(
          engine.bio || 0,
          engine.medCompliance || 0,
          engine.action || 0,
          engine.confidence || 0
        );
        break;
      default:
        throw new Error(`Unknown domain: ${domain}`);
    }

    return values;
  }
}

module.exports = TensorOperations;
