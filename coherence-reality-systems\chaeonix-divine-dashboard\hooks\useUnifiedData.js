/**
 * UNIFIED DATA HOOK
 * React hook that provides unified data from Central Data Coordinator
 * Replaces all disconnected simulation data sources
 */

import { useState, useEffect, useRef } from 'react';
import centralDataCoordinator from '../utils/centralDataCoordinator';

export function useUnifiedData() {
  const [data, setData] = useState(() => {
    // Only access centralDataCoordinator in browser
    if (typeof window !== 'undefined') {
      return centralDataCoordinator.getCurrentData();
    }
    // Return default data for SSR
    return {
      mt5_connection: { status: 'disconnected', account: null, positions: [], balance: 0, equity: 0, profit: 0 },
      trading_session: { trades_this_hour: 0, hourly_profit: 0, total_trades: 0, win_rate: 0, avg_profit_per_trade: 0 },
      performance_metrics: { daily_pnl: 0, weekly_pnl: 0, monthly_pnl: 0, total_pnl: 0, max_drawdown: 0, sharpe_ratio: 0 },
      market_allocation: { forex: 0, stocks: 0, crypto: 0 },
      last_update: new Date().toISOString()
    };
  });
  const [isConnected, setIsConnected] = useState(false);
  const [lastUpdate, setLastUpdate] = useState(new Date());
  const unsubscribeRef = useRef(null);

  useEffect(() => {
    // Only run in browser
    if (typeof window === 'undefined') {
      return;
    }

    console.log('🔗 Subscribing to unified data stream');

    // Subscribe to central data coordinator
    const unsubscribe = centralDataCoordinator.subscribe((newData) => {
      setData(newData);
      setLastUpdate(new Date());
      const connectionStatus = newData.mt5_connection.status;
      setIsConnected(connectionStatus === 'connected' || connectionStatus === 'CONNECTED' || newData.mt5_connection.isConnected);
    });

    unsubscribeRef.current = unsubscribe;

    // Cleanup subscription on unmount
    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
      }
    };
  }, []);

  // Force refresh data
  const refresh = async () => {
    await centralDataCoordinator.refresh();
  };

  // Add trade to the system
  const addTrade = (tradeData) => {
    centralDataCoordinator.addTrade(tradeData);
  };

  return {
    // MT5 Connection Data
    mt5Connection: data.mt5_connection,
    isConnected,

    // Trading Session Data
    tradingSession: data.trading_session,

    // Performance Metrics
    performance: data.performance_metrics,

    // Market Allocation
    marketAllocation: data.market_allocation,

    // Metadata
    lastUpdate,

    // Actions
    refresh,
    addTrade,

    // Raw data (for debugging)
    rawData: data
  };
}

// Hook specifically for Hourly Revenue Target component
export function useHourlyRevenue() {
  const { tradingSession, mt5Connection, isConnected } = useUnifiedData();

  // Calculate hourly revenue metrics
  const target = 650; // $650/hour target
  const progress = tradingSession.hourly_profit;
  const needed = Math.max(0, target - progress);
  const percentage = Math.min(100, (progress / target) * 100);

  // Calculate time progress (assuming 1-hour window)
  const currentMinute = new Date().getMinutes();
  const timeElapsed = currentMinute;
  const timeRemaining = 60 - currentMinute;

  return {
    target,
    progress,
    needed,
    percentage,
    timeElapsed,
    timeRemaining,
    tradesThisHour: tradingSession.trades_this_hour,
    avgPerTrade: tradingSession.avg_profit_per_trade,
    projectedHour: tradingSession.trades_this_hour > 0 ?
      (tradingSession.hourly_profit / tradingSession.trades_this_hour) *
      (60 / Math.max(1, timeElapsed)) : 0,
    isLive: isConnected,
    dataSource: isConnected ? 'MT5_LIVE' : 'SIMULATION'
  };
}

// Hook specifically for Performance Tracker
export function usePerformanceTracker() {
  const { performance, mt5Connection, isConnected } = useUnifiedData();

  return {
    dailyPnL: performance.daily_pnl,
    weeklyPnL: performance.weekly_pnl,
    monthlyPnL: performance.monthly_pnl,
    totalPnL: performance.total_pnl,
    maxDrawdown: performance.max_drawdown,
    sharpeRatio: performance.sharpe_ratio,
    balance: mt5Connection.balance,
    equity: mt5Connection.equity,
    profit: mt5Connection.profit,
    isLive: isConnected,
    dataSource: isConnected ? 'MT5_LIVE' : 'SIMULATION'
  };
}

// Hook specifically for Profit Analytics
export function useProfitAnalytics() {
  const { tradingSession, performance, mt5Connection, isConnected } = useUnifiedData();

  return {
    totalTrades: tradingSession.total_trades,
    winRate: tradingSession.win_rate,
    avgProfitPerTrade: tradingSession.avg_profit_per_trade,
    hourlyProfit: tradingSession.hourly_profit,
    dailyProfit: performance.daily_pnl,
    totalProfit: performance.total_pnl,
    currentBalance: mt5Connection.balance,
    currentEquity: mt5Connection.equity,
    isLive: isConnected,
    dataSource: isConnected ? 'MT5_LIVE' : 'SIMULATION'
  };
}

// Hook specifically for Market Allocation
export function useMarketAllocation() {
  const { marketAllocation, mt5Connection, isConnected } = useUnifiedData();

  return {
    forex: marketAllocation.forex,
    stocks: marketAllocation.stocks,
    crypto: marketAllocation.crypto,
    positions: mt5Connection.positions,
    isLive: isConnected,
    dataSource: isConnected ? 'MT5_LIVE' : 'SIMULATION'
  };
}

// Hook for MT5 Connection Status
export function useMT5Status() {
  const { mt5Connection, isConnected, lastUpdate } = useUnifiedData();

  return {
    status: mt5Connection.status,
    isConnected,
    account: mt5Connection.account,
    balance: mt5Connection.balance,
    equity: mt5Connection.equity,
    profit: mt5Connection.profit,
    positions: mt5Connection.positions,
    mode: mt5Connection.mode,
    lastUpdate,
    connectionHealth: isConnected ? 'HEALTHY' : 'DISCONNECTED'
  };
}
