# NovaGRC APIs for Google Cloud Marketplace

This document provides information about deploying and using NovaGRC APIs through Google Cloud Marketplace.

## Overview

NovaGRC APIs provide a comprehensive set of APIs for governance, risk, and compliance management. When deployed through Google Cloud Marketplace, these APIs are fully integrated with Google Cloud services, providing a seamless experience for Google Cloud users.

## Deployment Options

NovaGRC APIs can be deployed through Google Cloud Marketplace in the following ways:

1. **Standalone Deployment**: Deploy NovaGRC APIs as a standalone application
2. **Integrated Deployment**: Deploy NovaGRC APIs as part of the NovaFuse GRC Suite

## Subscription Tiers

NovaGRC APIs are available in the following subscription tiers:

### NovaGRC Core

Basic functionality for small projects:

- Privacy Management API
- Regulatory Compliance API
- Security Assessment API
- Control Testing API
- ESG API

### NovaGRC Enterprise

Advanced functionality for professional teams:

- All Core features
- Advanced analytics
- Custom reporting
- Integration with Google Cloud Security Command Center
- Integration with Google Cloud Compliance

### NovaGRC AI Boost

Enterprise-grade functionality with AI capabilities:

- All Enterprise features
- AI-powered risk assessment
- AI-powered compliance monitoring
- AI-powered security assessment
- AI-powered ESG reporting

## Integration with Google Cloud Services

NovaGRC APIs integrate with the following Google Cloud services:

- **Google Cloud Security Command Center**: Send security findings to Security Command Center
- **Google Cloud Logging**: Send logs to Google Cloud Logging
- **Google Cloud Monitoring**: Send metrics to Google Cloud Monitoring
- **Google Cloud Trace**: Send traces to Google Cloud Trace
- **Google Cloud Error Reporting**: Send errors to Google Cloud Error Reporting
- **Google Cloud Secret Manager**: Store secrets in Google Cloud Secret Manager
- **Google Cloud IAM**: Use Google Cloud IAM for authentication and authorization

## Configuration

NovaGRC APIs can be configured through the Google Cloud Marketplace UI during deployment. The following configuration options are available:

- **Subscription Tier**: Select the subscription tier
- **Database Configuration**: Configure the database connection
- **Redis Configuration**: Configure the Redis connection
- **API Key**: Configure the API key for authentication
- **JWT Secret**: Configure the JWT secret for authentication
- **CORS Origin**: Configure the CORS allowed origins
- **Log Level**: Configure the logging level
- **Monitoring**: Configure monitoring options
- **Security**: Configure security options

## Usage

NovaGRC APIs can be accessed through the following endpoints:

- **Privacy Management API**: `/api/privacy`
- **Regulatory Compliance API**: `/api/compliance`
- **Security Assessment API**: `/api/security`
- **Control Testing API**: `/api/control`
- **ESG API**: `/api/esg`

## Documentation

NovaGRC APIs documentation is available at the following endpoints:

- **API Reference**: `/api-docs`
- **Swagger UI**: `/api-docs/swagger-ui`
- **OpenAPI Specification**: `/api-docs/openapi.json`

## Support

For support, please contact [<EMAIL>](mailto:<EMAIL>) or visit [novafuse.io/support](https://novafuse.io/support).

## Conclusion

NovaGRC APIs provide a comprehensive set of APIs for governance, risk, and compliance management. When deployed through Google Cloud Marketplace, these APIs are fully integrated with Google Cloud services, providing a seamless experience for Google Cloud users.
