/**
 * Participant Recruitment Controller
 * 
 * This controller handles participant recruitment for user testing.
 */

const logger = require('../../config/logger');
const { v4: uuidv4 } = require('uuid');
const { getRedisClient } = require('../../config/redis');
const emailService = require('../services/emailService');
const config = require('../config/config');

// Redis client
const redisClient = getRedisClient();

// Redis keys
const PARTICIPANTS_KEY = 'user_testing:participants';
const RECRUITMENT_CAMPAIGNS_KEY = 'user_testing:recruitment_campaigns';

/**
 * Create a new participant
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const createParticipant = async (req, res, next) => {
  try {
    logger.debug('Creating new participant');
    
    // Get participant data from request body
    const participantData = req.body;
    
    // Validate required fields
    if (!participantData.name || !participantData.email) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Name and email are required',
          code: 'INVALID_REQUEST'
        }
      });
    }
    
    // Create participant object
    const participant = {
      id: uuidv4(),
      name: participantData.name,
      email: participantData.email,
      phone: participantData.phone || '',
      age: participantData.age || '',
      gender: participantData.gender || '',
      role: participantData.role || '',
      company: participantData.company || '',
      industry: participantData.industry || '',
      experience: participantData.experience || 'beginner',
      deviceTypes: participantData.deviceTypes || [],
      visualizationInterest: participantData.visualizationInterest || [],
      availability: participantData.availability || [],
      preferredContactMethod: participantData.preferredContactMethod || 'email',
      additionalInfo: participantData.additionalInfo || '',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      status: 'pending'
    };
    
    // Save participant to Redis
    await saveParticipant(participant);
    
    // Return success
    res.status(201).json({
      success: true,
      message: 'Participant created successfully',
      participant: {
        id: participant.id,
        name: participant.name,
        email: participant.email
      }
    });
  } catch (error) {
    logger.error('Error creating participant:', error);
    next(error);
  }
};

/**
 * Get all participants
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getParticipants = async (req, res, next) => {
  try {
    logger.debug('Getting all participants');
    
    // Get all participants from Redis
    const participants = await getAllParticipants();
    
    // Return participants
    res.status(200).json(participants);
  } catch (error) {
    logger.error('Error getting participants:', error);
    next(error);
  }
};

/**
 * Get a participant by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getParticipantById = async (req, res, next) => {
  try {
    const { id } = req.params;
    
    logger.debug(`Getting participant with ID: ${id}`);
    
    // Get participant from Redis
    const participant = await getParticipant(id);
    
    if (!participant) {
      return res.status(404).json({
        success: false,
        error: {
          message: `Participant with ID ${id} not found`,
          code: 'PARTICIPANT_NOT_FOUND'
        }
      });
    }
    
    // Return participant
    res.status(200).json(participant);
  } catch (error) {
    logger.error(`Error getting participant with ID ${req.params.id}:`, error);
    next(error);
  }
};

/**
 * Update a participant
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const updateParticipant = async (req, res, next) => {
  try {
    const { id } = req.params;
    
    logger.debug(`Updating participant with ID: ${id}`);
    
    // Get participant from Redis
    const participant = await getParticipant(id);
    
    if (!participant) {
      return res.status(404).json({
        success: false,
        error: {
          message: `Participant with ID ${id} not found`,
          code: 'PARTICIPANT_NOT_FOUND'
        }
      });
    }
    
    // Update participant
    const updatedParticipant = {
      ...participant,
      ...req.body,
      updatedAt: new Date().toISOString()
    };
    
    // Save updated participant to Redis
    await saveParticipant(updatedParticipant);
    
    // Return updated participant
    res.status(200).json(updatedParticipant);
  } catch (error) {
    logger.error(`Error updating participant with ID ${req.params.id}:`, error);
    next(error);
  }
};

/**
 * Delete a participant
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const deleteParticipant = async (req, res, next) => {
  try {
    const { id } = req.params;
    
    logger.debug(`Deleting participant with ID: ${id}`);
    
    // Get participant from Redis
    const participant = await getParticipant(id);
    
    if (!participant) {
      return res.status(404).json({
        success: false,
        error: {
          message: `Participant with ID ${id} not found`,
          code: 'PARTICIPANT_NOT_FOUND'
        }
      });
    }
    
    // Delete participant from Redis
    await deleteParticipantFromRedis(id);
    
    // Return success
    res.status(200).json({
      success: true,
      message: `Participant with ID ${id} deleted successfully`
    });
  } catch (error) {
    logger.error(`Error deleting participant with ID ${req.params.id}:`, error);
    next(error);
  }
};

/**
 * Create a recruitment campaign
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const createRecruitmentCampaign = async (req, res, next) => {
  try {
    logger.debug('Creating new recruitment campaign');
    
    // Get campaign data from request body
    const campaignData = req.body;
    
    // Validate required fields
    if (!campaignData.name || !campaignData.testingInfo) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Name and testing info are required',
          code: 'INVALID_REQUEST'
        }
      });
    }
    
    // Create campaign object
    const campaign = {
      id: uuidv4(),
      name: campaignData.name,
      description: campaignData.description || '',
      testingInfo: campaignData.testingInfo,
      targetParticipants: campaignData.targetParticipants || [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      status: 'draft'
    };
    
    // Save campaign to Redis
    await saveRecruitmentCampaign(campaign);
    
    // Return success
    res.status(201).json({
      success: true,
      message: 'Recruitment campaign created successfully',
      campaign: {
        id: campaign.id,
        name: campaign.name
      }
    });
  } catch (error) {
    logger.error('Error creating recruitment campaign:', error);
    next(error);
  }
};

/**
 * Send recruitment emails
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const sendRecruitmentEmails = async (req, res, next) => {
  try {
    const { campaignId } = req.params;
    
    logger.debug(`Sending recruitment emails for campaign: ${campaignId}`);
    
    // Get campaign from Redis
    const campaign = await getRecruitmentCampaign(campaignId);
    
    if (!campaign) {
      return res.status(404).json({
        success: false,
        error: {
          message: `Recruitment campaign with ID ${campaignId} not found`,
          code: 'CAMPAIGN_NOT_FOUND'
        }
      });
    }
    
    // Get target participants
    let targetParticipants = [];
    
    if (campaign.targetParticipants && campaign.targetParticipants.length > 0) {
      // Get specific participants
      for (const participantId of campaign.targetParticipants) {
        const participant = await getParticipant(participantId);
        if (participant) {
          targetParticipants.push(participant);
        }
      }
    } else {
      // Get all participants
      targetParticipants = await getAllParticipants();
    }
    
    // Send emails
    const results = [];
    
    for (const participant of targetParticipants) {
      try {
        // Send recruitment email
        await emailService.sendTestRecruitmentEmail(participant, campaign.testingInfo);
        
        // Add to results
        results.push({
          participantId: participant.id,
          email: participant.email,
          status: 'sent'
        });
      } catch (error) {
        logger.error(`Error sending recruitment email to participant ${participant.id}:`, error);
        
        // Add to results
        results.push({
          participantId: participant.id,
          email: participant.email,
          status: 'failed',
          error: error.message
        });
      }
    }
    
    // Update campaign status
    campaign.status = 'sent';
    campaign.sentAt = new Date().toISOString();
    campaign.results = results;
    campaign.updatedAt = new Date().toISOString();
    
    // Save updated campaign to Redis
    await saveRecruitmentCampaign(campaign);
    
    // Return results
    res.status(200).json({
      success: true,
      message: `Sent recruitment emails to ${results.filter(r => r.status === 'sent').length} participants`,
      results
    });
  } catch (error) {
    logger.error(`Error sending recruitment emails for campaign ${req.params.campaignId}:`, error);
    next(error);
  }
};

/**
 * Get all participants from Redis
 * @returns {Array} - All participants
 */
const getAllParticipants = async () => {
  try {
    // Get all participant IDs
    const participantIds = await redisClient.smembers(PARTICIPANTS_KEY);
    
    // Get participants
    const participants = [];
    
    for (const id of participantIds) {
      const participant = await getParticipant(id);
      if (participant) {
        participants.push(participant);
      }
    }
    
    return participants;
  } catch (error) {
    logger.error('Error getting all participants from Redis:', error);
    throw error;
  }
};

/**
 * Get a participant from Redis
 * @param {string} id - Participant ID
 * @returns {Object|null} - Participant or null if not found
 */
const getParticipant = async (id) => {
  try {
    // Get participant from Redis
    const participantJson = await redisClient.get(`${PARTICIPANTS_KEY}:${id}`);
    
    if (!participantJson) {
      return null;
    }
    
    return JSON.parse(participantJson);
  } catch (error) {
    logger.error(`Error getting participant with ID ${id} from Redis:`, error);
    throw error;
  }
};

/**
 * Save a participant to Redis
 * @param {Object} participant - Participant to save
 */
const saveParticipant = async (participant) => {
  try {
    // Save participant to Redis
    await redisClient.set(`${PARTICIPANTS_KEY}:${participant.id}`, JSON.stringify(participant));
    
    // Add participant ID to set
    await redisClient.sadd(PARTICIPANTS_KEY, participant.id);
  } catch (error) {
    logger.error(`Error saving participant with ID ${participant.id} to Redis:`, error);
    throw error;
  }
};

/**
 * Delete a participant from Redis
 * @param {string} id - Participant ID
 */
const deleteParticipantFromRedis = async (id) => {
  try {
    // Delete participant from Redis
    await redisClient.del(`${PARTICIPANTS_KEY}:${id}`);
    
    // Remove participant ID from set
    await redisClient.srem(PARTICIPANTS_KEY, id);
  } catch (error) {
    logger.error(`Error deleting participant with ID ${id} from Redis:`, error);
    throw error;
  }
};

/**
 * Get a recruitment campaign from Redis
 * @param {string} id - Campaign ID
 * @returns {Object|null} - Campaign or null if not found
 */
const getRecruitmentCampaign = async (id) => {
  try {
    // Get campaign from Redis
    const campaignJson = await redisClient.get(`${RECRUITMENT_CAMPAIGNS_KEY}:${id}`);
    
    if (!campaignJson) {
      return null;
    }
    
    return JSON.parse(campaignJson);
  } catch (error) {
    logger.error(`Error getting recruitment campaign with ID ${id} from Redis:`, error);
    throw error;
  }
};

/**
 * Save a recruitment campaign to Redis
 * @param {Object} campaign - Campaign to save
 */
const saveRecruitmentCampaign = async (campaign) => {
  try {
    // Save campaign to Redis
    await redisClient.set(`${RECRUITMENT_CAMPAIGNS_KEY}:${campaign.id}`, JSON.stringify(campaign));
    
    // Add campaign ID to set
    await redisClient.sadd(RECRUITMENT_CAMPAIGNS_KEY, campaign.id);
  } catch (error) {
    logger.error(`Error saving recruitment campaign with ID ${campaign.id} to Redis:`, error);
    throw error;
  }
};

module.exports = {
  createParticipant,
  getParticipants,
  getParticipantById,
  updateParticipant,
  deleteParticipant,
  createRecruitmentCampaign,
  sendRecruitmentEmails
};
