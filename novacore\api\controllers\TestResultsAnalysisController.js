/**
 * Test Results Analysis Controller
 * 
 * This controller provides endpoints for analyzing user testing results.
 */

const logger = require('../../config/logger');
const { getRedisClient } = require('../../config/redis');
const userTestingController = require('./UserTestingController');
const { Parser } = require('json2csv');

// Redis client
const redisClient = getRedisClient();

/**
 * Get test results analysis
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getTestResultsAnalysis = async (req, res, next) => {
  try {
    logger.debug('Getting test results analysis');
    
    // Get query parameters
    const { visualizationType, testType, timeRange } = req.query;
    
    // Get test results
    const testResults = await userTestingController.getTestResults();
    
    // Filter results based on query parameters
    const filteredResults = filterTestResults(testResults, visualizationType, testType, timeRange);
    
    // Generate analysis
    const analysis = generateAnalysis(filteredResults);
    
    // Return analysis
    res.status(200).json(analysis);
  } catch (error) {
    logger.error('Error getting test results analysis:', error);
    next(error);
  }
};

/**
 * Export test results analysis
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const exportTestResultsAnalysis = async (req, res, next) => {
  try {
    logger.debug('Exporting test results analysis');
    
    // Get query parameters
    const { visualizationType, testType, timeRange } = req.query;
    
    // Get test results
    const testResults = await userTestingController.getTestResults();
    
    // Filter results based on query parameters
    const filteredResults = filterTestResults(testResults, visualizationType, testType, timeRange);
    
    // Generate export data
    const exportData = generateExportData(filteredResults);
    
    // Convert to CSV
    const parser = new Parser();
    const csv = parser.parse(exportData);
    
    // Set response headers
    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', `attachment; filename=test-results-analysis-${new Date().toISOString().split('T')[0]}.csv`);
    
    // Send CSV
    res.status(200).send(csv);
  } catch (error) {
    logger.error('Error exporting test results analysis:', error);
    next(error);
  }
};

/**
 * Filter test results based on query parameters
 * @param {Array} testResults - Test results
 * @param {string} visualizationType - Visualization type filter
 * @param {string} testType - Test type filter
 * @param {string} timeRange - Time range filter
 * @returns {Array} - Filtered test results
 */
const filterTestResults = (testResults, visualizationType, testType, timeRange) => {
  // Start with all results
  let filteredResults = [...testResults];
  
  // Filter by visualization type
  if (visualizationType && visualizationType !== 'all') {
    filteredResults = filteredResults.filter(result => result.visualizationType === visualizationType);
  }
  
  // Filter by test type
  if (testType && testType !== 'all') {
    filteredResults = filteredResults.filter(result => result.testType === testType);
  }
  
  // Filter by time range
  if (timeRange && timeRange !== 'all') {
    const now = Date.now();
    let startTime;
    
    switch (timeRange) {
      case '7d':
        startTime = now - 7 * 24 * 60 * 60 * 1000; // 7 days
        break;
      case '30d':
        startTime = now - 30 * 24 * 60 * 60 * 1000; // 30 days
        break;
      case '90d':
        startTime = now - 90 * 24 * 60 * 60 * 1000; // 90 days
        break;
      default:
        startTime = now - 30 * 24 * 60 * 60 * 1000; // Default to 30 days
    }
    
    filteredResults = filteredResults.filter(result => {
      const submittedAt = new Date(result.submittedAt).getTime();
      return submittedAt >= startTime;
    });
  }
  
  return filteredResults;
};

/**
 * Generate analysis from test results
 * @param {Array} testResults - Test results
 * @returns {Object} - Analysis data
 */
const generateAnalysis = (testResults) => {
  // Return empty analysis if no results
  if (!testResults || testResults.length === 0) {
    return {
      overview: {
        totalSessions: 0,
        uniqueParticipants: 0,
        avgSuccessRate: 0,
        avgSatisfaction: 0,
        sessionsByVisualizationType: {},
        sessionsByTestType: {},
        successRateByVisualizationType: {}
      },
      taskAnalysis: {
        overallCompletionRate: 0,
        totalTasks: 0,
        totalCompletedTasks: 0,
        avgTaskDuration: 0,
        minTaskDuration: 0,
        maxTaskDuration: 0,
        totalHelpRequests: 0,
        helpRequestRate: 0,
        completionRateByVisualizationType: {},
        durationByVisualizationType: {},
        taskDifficultyAnalysis: {}
      },
      feedbackAnalysis: {
        overallSatisfaction: 0,
        usabilityRating: 0,
        visualAppealRating: 0,
        recommendationScore: 0,
        satisfactionByVisualizationType: {},
        usabilityByVisualizationType: {},
        commonThemes: {},
        improvementSuggestions: []
      },
      interactionAnalysis: {
        totalInteractions: 0,
        avgInteractionsPerSession: 0,
        mostCommonInteraction: { type: 'none', count: 0 },
        interactionDiversity: 0,
        interactionTypeBreakdown: {},
        interactionsByVisualizationType: {},
        commonPatterns: [],
        heatmapSummary: {}
      }
    };
  }
  
  // Generate overview analysis
  const overview = generateOverviewAnalysis(testResults);
  
  // Generate task analysis
  const taskAnalysis = generateTaskAnalysis(testResults);
  
  // Generate feedback analysis
  const feedbackAnalysis = generateFeedbackAnalysis(testResults);
  
  // Generate interaction analysis
  const interactionAnalysis = generateInteractionAnalysis(testResults);
  
  return {
    overview,
    taskAnalysis,
    feedbackAnalysis,
    interactionAnalysis
  };
};

/**
 * Generate overview analysis
 * @param {Array} testResults - Test results
 * @returns {Object} - Overview analysis
 */
const generateOverviewAnalysis = (testResults) => {
  // Calculate total sessions
  const totalSessions = testResults.length;
  
  // Calculate unique participants
  const uniqueParticipants = new Set(testResults.map(result => result.participantId)).size;
  
  // Calculate average success rate
  const avgSuccessRate = testResults.reduce((sum, result) => sum + (result.successRate || 0), 0) / totalSessions;
  
  // Calculate average satisfaction
  const avgSatisfaction = testResults.reduce((sum, result) => {
    const satisfaction = result.finalFeedback?.overallSatisfaction || 0;
    return sum + satisfaction;
  }, 0) / totalSessions;
  
  // Calculate sessions by visualization type
  const sessionsByVisualizationType = {};
  testResults.forEach(result => {
    const type = result.visualizationType;
    sessionsByVisualizationType[type] = (sessionsByVisualizationType[type] || 0) + 1;
  });
  
  // Calculate sessions by test type
  const sessionsByTestType = {};
  testResults.forEach(result => {
    const type = result.testType;
    sessionsByTestType[type] = (sessionsByTestType[type] || 0) + 1;
  });
  
  // Calculate success rate by visualization type
  const successRateByVisualizationType = {};
  Object.keys(sessionsByVisualizationType).forEach(type => {
    const typeResults = testResults.filter(result => result.visualizationType === type);
    const typeSuccessRate = typeResults.reduce((sum, result) => sum + (result.successRate || 0), 0) / typeResults.length;
    successRateByVisualizationType[type] = typeSuccessRate;
  });
  
  return {
    totalSessions,
    uniqueParticipants,
    avgSuccessRate,
    avgSatisfaction,
    sessionsByVisualizationType,
    sessionsByTestType,
    successRateByVisualizationType
  };
};

/**
 * Generate task analysis
 * @param {Array} testResults - Test results
 * @returns {Object} - Task analysis
 */
const generateTaskAnalysis = (testResults) => {
  // Placeholder for task analysis
  // In a real implementation, this would analyze task completion, duration, etc.
  return {
    overallCompletionRate: 0.75,
    totalTasks: 100,
    totalCompletedTasks: 75,
    avgTaskDuration: 45,
    minTaskDuration: 10,
    maxTaskDuration: 120,
    totalHelpRequests: 25,
    helpRequestRate: 0.25,
    completionRateByVisualizationType: {
      triDomainTensor: 0.8,
      harmonyIndex: 0.7,
      riskControlFusion: 0.75,
      resonanceSpectrogram: 0.65,
      unifiedComplianceSecurity: 0.7
    },
    durationByVisualizationType: {
      triDomainTensor: 40,
      harmonyIndex: 35,
      riskControlFusion: 50,
      resonanceSpectrogram: 60,
      unifiedComplianceSecurity: 55
    },
    taskDifficultyAnalysis: {
      task1: {
        taskName: 'Explore the visualization',
        completionRate: 0.9,
        avgDuration: 30,
        helpRequests: 5,
        difficultyRating: 2.5
      },
      task2: {
        taskName: 'Apply filters',
        completionRate: 0.8,
        avgDuration: 45,
        helpRequests: 10,
        difficultyRating: 3.2
      },
      task3: {
        taskName: 'Identify patterns',
        completionRate: 0.6,
        avgDuration: 60,
        helpRequests: 15,
        difficultyRating: 4.1
      }
    }
  };
};

/**
 * Generate feedback analysis
 * @param {Array} testResults - Test results
 * @returns {Object} - Feedback analysis
 */
const generateFeedbackAnalysis = (testResults) => {
  // Placeholder for feedback analysis
  // In a real implementation, this would analyze user feedback
  return {
    overallSatisfaction: 3.8,
    usabilityRating: 3.5,
    visualAppealRating: 4.2,
    recommendationScore: 3.9,
    satisfactionByVisualizationType: {
      triDomainTensor: 4.1,
      harmonyIndex: 3.9,
      riskControlFusion: 3.7,
      resonanceSpectrogram: 3.5,
      unifiedComplianceSecurity: 3.8
    },
    usabilityByVisualizationType: {
      triDomainTensor: 3.8,
      harmonyIndex: 3.6,
      riskControlFusion: 3.4,
      resonanceSpectrogram: 3.2,
      unifiedComplianceSecurity: 3.5
    },
    commonThemes: {
      'Intuitive Interface': 25,
      'Clear Visualization': 20,
      'Difficult Navigation': 15,
      'Slow Performance': 10,
      'Helpful Tooltips': 8,
      'Missing Features': 7
    },
    improvementSuggestions: [
      {
        category: 'Navigation',
        text: 'Add breadcrumbs to make navigation easier',
        visualizationType: 'triDomainTensor',
        testType: 'usability'
      },
      {
        category: 'Performance',
        text: 'Improve loading time for large datasets',
        visualizationType: 'resonanceSpectrogram',
        testType: 'performance'
      },
      {
        category: 'Accessibility',
        text: 'Add keyboard shortcuts for common actions',
        visualizationType: 'harmonyIndex',
        testType: 'accessibility'
      }
    ]
  };
};

/**
 * Generate interaction analysis
 * @param {Array} testResults - Test results
 * @returns {Object} - Interaction analysis
 */
const generateInteractionAnalysis = (testResults) => {
  // Placeholder for interaction analysis
  // In a real implementation, this would analyze user interactions
  return {
    totalInteractions: 1250,
    avgInteractionsPerSession: 25,
    mostCommonInteraction: { type: 'click', count: 500 },
    interactionDiversity: 0.75,
    interactionTypeBreakdown: {
      click: 500,
      hover: 300,
      drag: 200,
      zoom: 150,
      rotate: 100
    },
    interactionsByVisualizationType: {
      triDomainTensor: 350,
      harmonyIndex: 300,
      riskControlFusion: 250,
      resonanceSpectrogram: 200,
      unifiedComplianceSecurity: 150
    },
    commonPatterns: [
      {
        sequence: [
          { type: 'click', target: 'node' },
          { type: 'hover', target: 'tooltip' },
          { type: 'click', target: 'details' }
        ],
        frequency: 75,
        avgDuration: 8,
        successRate: 0.85
      },
      {
        sequence: [
          { type: 'zoom', target: 'visualization' },
          { type: 'drag', target: 'element' },
          { type: 'click', target: 'element' }
        ],
        frequency: 50,
        avgDuration: 12,
        successRate: 0.7
      }
    ],
    heatmapSummary: {
      triDomainTensor: [
        { name: 'Center Node', interactionCount: 120 },
        { name: 'Connection Lines', interactionCount: 85 },
        { name: 'Domain Labels', interactionCount: 65 }
      ],
      harmonyIndex: [
        { name: 'Chart Area', interactionCount: 110 },
        { name: 'Legend', interactionCount: 75 },
        { name: 'Domain Selector', interactionCount: 55 }
      ]
    }
  };
};

/**
 * Generate export data from test results
 * @param {Array} testResults - Test results
 * @returns {Array} - Export data
 */
const generateExportData = (testResults) => {
  // Placeholder for export data
  // In a real implementation, this would generate data for export
  return testResults.map(result => ({
    sessionId: result.id,
    participantId: result.participantId,
    visualizationType: result.visualizationType,
    testType: result.testType,
    startTime: result.startTime,
    endTime: result.endTime,
    duration: result.duration,
    successRate: result.successRate,
    completedTasks: result.completedTasks?.length || 0,
    overallSatisfaction: result.finalFeedback?.overallSatisfaction || 0,
    usabilityRating: result.finalFeedback?.usability || 0,
    visualAppealRating: result.finalFeedback?.visualAppeal || 0,
    recommendationScore: result.finalFeedback?.likelyToRecommend || 0
  }));
};

module.exports = {
  getTestResultsAnalysis,
  exportTestResultsAnalysis
};
