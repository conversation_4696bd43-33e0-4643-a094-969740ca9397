/**
 * NovaStore Demo
 * 
 * This script demonstrates the use of NovaStore as the brain and nervous system
 * of the NovaFuse platform, showcasing:
 * 1. Direct CSDE Integration (Physics Tier)
 * 2. Enhanced NovaConnect + CSDE Integration (Transition Tier)
 * 3. Traditional NovaConnect (Legacy Tier)
 * 4. Cross-Domain Intelligence
 */

const NovaStore = require('../src/novastore');
const winston = require('winston');

// Create logger
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.printf(({ level, message, timestamp }) => {
      return `${timestamp} ${level.toUpperCase()}: ${message}`;
    })
  ),
  transports: [
    new winston.transports.Console()
  ]
});

// Sample data
const complianceData = {
  framework: 'NIST_CSF',
  controls: {
    'ID.AM-1': { status: 'IMPLEMENTED', evidence: ['asset-inventory.pdf'] },
    'PR.AC-1': { status: 'PARTIALLY_IMPLEMENTED', evidence: [] },
    'DE.CM-1': { status: 'NOT_IMPLEMENTED', evidence: [] }
  }
};

const gcpData = {
  services: ['compute', 'storage', 'bigquery'],
  regions: ['us-central1', 'europe-west1'],
  projects: ['project-1', 'project-2']
};

const cyberSafetyData = {
  threats: [
    { type: 'malware', severity: 'high', count: 5 },
    { type: 'phishing', severity: 'medium', count: 10 }
  ],
  vulnerabilities: [
    { cve: 'CVE-2023-1234', severity: 'critical', status: 'open' },
    { cve: 'CVE-2023-5678', severity: 'high', status: 'open' }
  ]
};

const securityData = {
  'malware-detection': 0.8,
  'unauthorized-access': 0.6,
  'data-exfiltration': 0.3,
  'vulnerability-scan': 0.9,
  'patch-management': 0.5
};

/**
 * Run the NovaStore demo
 */
async function runDemo() {
  logger.info('Starting NovaStore Demo');
  
  try {
    // Initialize NovaStore
    const novaStore = NovaStore.initialize({ logger });
    
    // Start NovaStore
    await novaStore.start();
    
    // Demo 1: Direct CSDE Integration (Physics Tier)
    await demoDirectIntegration(novaStore);
    
    // Demo 2: Enhanced NovaConnect + CSDE Integration (Transition Tier)
    await demoEnhancedIntegration(novaStore);
    
    // Demo 3: Traditional NovaConnect (Legacy Tier)
    await demoTraditionalIntegration(novaStore);
    
    // Demo 4: Cross-Domain Intelligence
    await demoCrossDomainIntelligence(novaStore);
    
    // Demo 5: Three-Tier Product Portfolio
    demoProductPortfolio(novaStore);
    
    // Stop NovaStore
    await novaStore.stop();
    
    logger.info('NovaStore Demo completed successfully');
  } catch (error) {
    logger.error('Error running NovaStore Demo:', error);
  }
}

/**
 * Demo Direct CSDE Integration (Physics Tier)
 * @param {Object} novaStore - Initialized NovaStore
 */
async function demoDirectIntegration(novaStore) {
  logger.info('Demo 1: Direct CSDE Integration (Physics Tier)');
  
  try {
    // Create client for Physics tier
    const client = novaStore.createClient({
      tier: 'physics',
      clientName: 'Physics Demo Client'
    });
    
    // Calculate CSDE value
    const result = await client.calculateCSDE(complianceData, gcpData, cyberSafetyData);
    
    logger.info(`CSDE Value: ${result.csdeValue}`);
    logger.info(`Performance Factor: ${result.performanceFactor}x`);
    logger.info(`Processing Time: ${result.processingTime.toFixed(3)}ms`);
    logger.info(`Wilson Loop ID: ${result.wilsonLoopId}`);
    logger.info(`Remediation Actions: ${result.remediationActions.length}`);
    
    // Close client
    await client.close();
  } catch (error) {
    logger.error('Error in Direct CSDE Integration demo:', error);
  }
}

/**
 * Demo Enhanced NovaConnect + CSDE Integration (Transition Tier)
 * @param {Object} novaStore - Initialized NovaStore
 */
async function demoEnhancedIntegration(novaStore) {
  logger.info('Demo 2: Enhanced NovaConnect + CSDE Integration (Transition Tier)');
  
  try {
    // Create client for Transition tier
    const client = novaStore.createClient({
      tier: 'transition',
      clientName: 'Transition Demo Client'
    });
    
    // Calculate CSDE value
    const result = await client.calculateCSDE(complianceData, gcpData, cyberSafetyData, {
      priority: 'high' // Use Physics tier for high-priority operations
    });
    
    logger.info(`CSDE Value: ${result.csdeValue}`);
    logger.info(`Performance Factor: ${result.performanceFactor}x`);
    logger.info(`Processing Time: ${result.processingTime.toFixed(3)}ms`);
    
    // Close client
    await client.close();
  } catch (error) {
    logger.error('Error in Enhanced Integration demo:', error);
  }
}

/**
 * Demo Traditional NovaConnect (Legacy Tier)
 * @param {Object} novaStore - Initialized NovaStore
 */
async function demoTraditionalIntegration(novaStore) {
  logger.info('Demo 3: Traditional NovaConnect (Legacy Tier)');
  
  try {
    // Create client for Legacy tier
    const client = novaStore.createClient({
      tier: 'legacy',
      clientName: 'Legacy Demo Client'
    });
    
    // This would normally make a REST API call
    // For demo purposes, we'll just log the request
    logger.info('Making REST API call to /csde/calculate');
    logger.info('Request payload:');
    logger.info(`- Compliance Data: ${Object.keys(complianceData).length} items`);
    logger.info(`- GCP Data: ${Object.keys(gcpData).length} items`);
    logger.info(`- Cyber-Safety Data: ${Object.keys(cyberSafetyData).length} items`);
    
    // Close client
    await client.close();
  } catch (error) {
    logger.error('Error in Traditional Integration demo:', error);
  }
}

/**
 * Demo Cross-Domain Intelligence
 * @param {Object} novaStore - Initialized NovaStore
 */
async function demoCrossDomainIntelligence(novaStore) {
  logger.info('Demo 4: Cross-Domain Intelligence');
  
  try {
    // Predict patterns in compliance domain based on security data
    const result = await novaStore.crossDomain.predict('security', 'compliance', securityData);
    
    logger.info(`Source Domain: ${result.sourceDomain}`);
    logger.info(`Target Domain: ${result.targetDomain}`);
    logger.info(`Source Patterns: ${result.sourcePatterns.length}`);
    logger.info(`Target Patterns: ${result.targetPatterns.length}`);
    logger.info(`Predictions: ${result.predictions.length}`);
    logger.info(`Confidence: ${(result.confidence * 100).toFixed(2)}%`);
    
    // Log predictions
    logger.info('Predictions:');
    for (const prediction of result.predictions) {
      logger.info(`- Category: ${prediction.category}`);
      logger.info(`  Score: ${prediction.score.toFixed(2)}`);
      logger.info(`  Enhanced Score: ${prediction.enhancedScore.toFixed(2)}`);
      logger.info(`  Confidence: ${(prediction.confidence * 100).toFixed(2)}%`);
      logger.info(`  UUFT Factor: ${prediction.uuftFactor.toFixed(2)}`);
    }
  } catch (error) {
    logger.error('Error in Cross-Domain Intelligence demo:', error);
  }
}

/**
 * Demo Three-Tier Product Portfolio
 * @param {Object} novaStore - Initialized NovaStore
 */
function demoProductPortfolio(novaStore) {
  logger.info('Demo 5: Three-Tier Product Portfolio');
  
  try {
    // Get all tiers
    const tiers = novaStore.portfolio.getTiers();
    
    logger.info(`Product Tiers: ${tiers.length}`);
    
    // Log tier information
    for (const tier of tiers) {
      logger.info(`- Tier: ${tier.name}`);
      logger.info(`  Description: ${tier.description}`);
      logger.info(`  Features: ${tier.featureCount}`);
      logger.info(`  Base Price: $${tier.pricing.base}`);
    }
    
    // Get features for Physics tier
    const physicsFeatures = novaStore.portfolio.getTierFeatures('physics');
    
    logger.info(`Physics Tier Features: ${physicsFeatures.length}`);
    
    // Log feature information
    for (const feature of physicsFeatures) {
      logger.info(`- Feature: ${feature.name}`);
      logger.info(`  Description: ${feature.description}`);
      logger.info(`  Category: ${feature.category}`);
    }
    
    // Calculate price for a customer
    const price = novaStore.portfolio.calculatePrice('physics', {
      users: 10,
      events: 1000000,
      addons: [
        { name: 'Premium Support', price: 5000 },
        { name: 'Custom Integration', price: 10000 }
      ]
    });
    
    logger.info(`Price Calculation for Physics Tier:`);
    logger.info(`- Base Price: $${price.basePrice}`);
    logger.info(`- User Price: $${price.userPrice} (${price.breakdown.users.count} users)`);
    logger.info(`- Event Price: $${price.eventPrice} (${price.breakdown.events.count} events)`);
    logger.info(`- Addon Price: $${price.addonPrice} (${price.breakdown.addons.count} addons)`);
    logger.info(`- Total Price: $${price.totalPrice}`);
    
    // Get migration path
    const migrationPath = novaStore.portfolio.getMigrationPath('legacy', 'transition');
    
    logger.info(`Migration Path: ${migrationPath.sourceId} to ${migrationPath.targetId}`);
    
    // Log migration steps
    logger.info('Migration Steps:');
    for (const step of migrationPath.steps) {
      logger.info(`- Step ${step.id}: ${step.name}`);
      logger.info(`  Description: ${step.description}`);
    }
  } catch (error) {
    logger.error('Error in Product Portfolio demo:', error);
  }
}

// Run the demo
runDemo();
