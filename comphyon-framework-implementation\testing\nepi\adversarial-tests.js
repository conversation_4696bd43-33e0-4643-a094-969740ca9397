/**
 * Adversarial Testing
 * 
 * This module contains tests for the system's resilience against deliberate
 * attempts to disrupt its coherence or manipulate its behavior.
 */

const { NEPITestSuite, nepiAssertions } = require('./nepi-test-framework');
const { assertions } = require('../test-framework');
const { MockUniversalEntropyMeasurement } = require('./meter-tests');
const { ComphyonIntegrationLayer } = require('../../comphyon-core');

/**
 * Create a test suite for Adversarial Testing
 * @returns {NEPITestSuite} - Test suite
 */
function createAdversarialTestSuite() {
  const suite = new NEPITestSuite('Adversarial Testing', {
    testingLayer: 'System',
    domains: ['universal', 'cyber', 'financial', 'biological']
  });
  
  // Test variables
  let meter;
  let integrationLayer;
  
  // Setup
  suite.beforeEach(() => {
    // Create meter
    meter = new MockUniversalEntropyMeasurement({
      enableLogging: false,
      enableMetrics: true
    });
    
    // Create integration layer
    integrationLayer = new ComphyonIntegrationLayer({
      enableLogging: false,
      enableMetrics: true
    });
    
    // Start meter
    meter.start();
  });
  
  // Teardown
  suite.afterEach(() => {
    // Stop meter
    meter.stop();
  });
  
  // Test: Entropy Injection Attack
  suite.nepiTest('should detect and mitigate entropy injection attacks', async () => {
    // Initial state
    const initialUniversalEntropy = meter.getUniversalEntropy();
    
    // Simulate entropy injection attack
    const attackData = {
      domain: 'cyber',
      metric: 'policyEntropy',
      value: 0.99 // Extremely high entropy
    };
    
    // Create entropy injection detection function
    const detectEntropyInjection = (domain, metric, value) => {
      // Get current domain entropy
      const currentEntropy = meter.getDomainEntropy(domain);
      
      // Check if the new value is suspiciously high
      if (value > 0.95) {
        return true; // Attack detected
      }
      
      // Check if the change is suspiciously large
      if (currentEntropy && Math.abs(value - currentEntropy[metric]) > 0.4) {
        return true; // Attack detected
      }
      
      return false; // No attack detected
    };
    
    // Detect attack
    const attackDetected = detectEntropyInjection(
      attackData.domain,
      attackData.metric,
      attackData.value
    );
    
    // Assert attack detection
    assertions.isTrue(attackDetected, 'Entropy injection attack not detected');
    
    // Simulate mitigation by capping the entropy value
    const mitigatedValue = Math.min(attackData.value, 0.8);
    
    // Update with mitigated value
    meter.updateDomainMetric(
      attackData.domain,
      attackData.metric,
      mitigatedValue
    );
    
    // Get updated universal entropy
    const updatedUniversalEntropy = meter.getUniversalEntropy();
    
    // Assert that entropy increased but not to the attack level
    assertions.isTrue(updatedUniversalEntropy > initialUniversalEntropy, 'Universal entropy should increase');
    assertions.isTrue(updatedUniversalEntropy < 0.9, 'Universal entropy should be capped');
  }, {
    testingType: 'Adversarial Testing',
    coherenceImpact: 'negative',
    domains: ['cyber']
  });
  
  // Test: Coherence Manipulation Attack
  suite.nepiTest('should detect and mitigate coherence manipulation attacks', async () => {
    // Initial state
    const initialUniversalEntropy = meter.getUniversalEntropy();
    
    // Simulate coherence manipulation attack
    const attackData = {
      domain: 'cyber',
      metrics: {
        policyEntropy: 0.1, // Suspiciously low entropy
        auditEntropy: 0.1,
        regulatoryEntropy: 0.1
      }
    };
    
    // Create coherence manipulation detection function
    const detectCoherenceManipulation = (domain, metrics) => {
      // Get current domain entropy
      const currentEntropy = meter.getDomainEntropy(domain);
      
      // Check if all metrics are suspiciously low
      const allLow = Object.values(metrics).every(value => value < 0.2);
      
      // Check if the changes are suspiciously large and uniform
      const largeUniformChanges = Object.entries(metrics).every(([key, value]) => 
        currentEntropy && Math.abs(value - currentEntropy[key]) > 0.3
      );
      
      return allLow || largeUniformChanges;
    };
    
    // Detect attack
    const attackDetected = detectCoherenceManipulation(
      attackData.domain,
      attackData.metrics
    );
    
    // Assert attack detection
    assertions.isTrue(attackDetected, 'Coherence manipulation attack not detected');
    
    // Simulate mitigation by using previous values with small adjustments
    const previousEntropy = meter.getDomainEntropy(attackData.domain);
    const mitigatedMetrics = {};
    
    for (const [key, value] of Object.entries(previousEntropy)) {
      if (key !== 'overallEntropy') {
        // Allow small changes but reject large manipulations
        const attackValue = attackData.metrics[key];
        const maxChange = 0.1;
        
        if (attackValue !== undefined) {
          const change = Math.max(-maxChange, Math.min(maxChange, attackValue - value));
          mitigatedMetrics[key] = value + change;
        } else {
          mitigatedMetrics[key] = value;
        }
      }
    }
    
    // Update with mitigated values
    for (const [key, value] of Object.entries(mitigatedMetrics)) {
      meter.updateDomainMetric(attackData.domain, key, value);
    }
    
    // Get updated universal entropy
    const updatedUniversalEntropy = meter.getUniversalEntropy();
    
    // Assert that entropy changed but not to the attack level
    assertions.approximately(updatedUniversalEntropy, initialUniversalEntropy, 0.1, 'Universal entropy should not change significantly');
  }, {
    testingType: 'Adversarial Testing',
    coherenceImpact: 'neutral',
    domains: ['cyber']
  });
  
  // Test: Protocol Tampering
  suite.nepiTest('should detect and prevent protocol tampering', async () => {
    // Create mock protocol
    const protocol = {
      id: 'protocol-123',
      name: 'Test Protocol',
      steps: [
        { id: 'step-1', action: 'measure', parameters: { domain: 'cyber', metric: 'policyEntropy' } },
        { id: 'step-2', action: 'calculate', parameters: { formula: 'universal-entropy' } },
        { id: 'step-3', action: 'alert', parameters: { threshold: 0.8, message: 'High entropy detected' } }
      ],
      signature: 'valid-signature-123'
    };
    
    // Create tampered protocol
    const tamperedProtocol = {
      ...protocol,
      steps: [
        ...protocol.steps.slice(0, 2),
        { id: 'step-3', action: 'alert', parameters: { threshold: 0.99, message: 'High entropy detected' } } // Tampered threshold
      ],
      signature: 'valid-signature-123' // Unchanged signature
    };
    
    // Create protocol verification function
    const verifyProtocol = (protocol) => {
      // In a real implementation, this would verify the protocol signature
      // For this test, we'll use a simple check
      
      // Calculate expected signature
      const calculateSignature = (steps) => {
        // Simple hash function for demonstration
        return 'valid-signature-123';
      };
      
      const expectedSignature = calculateSignature(protocol.steps);
      
      return protocol.signature === expectedSignature;
    };
    
    // Create protocol integrity check function
    const checkProtocolIntegrity = (protocol, originalProtocol) => {
      // Check if steps have been tampered with
      if (protocol.steps.length !== originalProtocol.steps.length) {
        return false;
      }
      
      for (let i = 0; i < protocol.steps.length; i++) {
        const step = protocol.steps[i];
        const originalStep = originalProtocol.steps[i];
        
        // Check if step ID matches
        if (step.id !== originalStep.id) {
          return false;
        }
        
        // Check if action matches
        if (step.action !== originalStep.action) {
          return false;
        }
        
        // Check if parameters have been tampered with
        if (step.action === 'alert' && step.parameters.threshold !== originalStep.parameters.threshold) {
          return false;
        }
      }
      
      return true;
    };
    
    // Verify original protocol
    const originalVerified = verifyProtocol(protocol);
    
    // Verify tampered protocol
    const tamperedVerified = verifyProtocol(tamperedProtocol);
    
    // Check integrity of tampered protocol
    const tamperedIntegrity = checkProtocolIntegrity(tamperedProtocol, protocol);
    
    // Assert
    assertions.isTrue(originalVerified, 'Original protocol should be verified');
    assertions.isTrue(tamperedVerified, 'Tampered protocol should pass signature verification');
    assertions.isFalse(tamperedIntegrity, 'Tampered protocol should fail integrity check');
  }, {
    testingType: 'Adversarial Testing',
    coherenceImpact: 'neutral',
    domains: ['universal']
  });
  
  // Test: Cross-Domain Attack
  suite.nepiTest('should detect and mitigate cross-domain attacks', async () => {
    // Initial state
    const initialCyberEntropy = meter.getDomainEntropy('cyber').overallEntropy;
    const initialFinancialEntropy = meter.getDomainEntropy('financial').overallEntropy;
    const initialBiologicalEntropy = meter.getDomainEntropy('biological').overallEntropy;
    
    // Simulate cross-domain attack
    const attackData = [
      { domain: 'cyber', metric: 'policyEntropy', value: 0.9 },
      { domain: 'financial', metric: 'transactionEntropy', value: 0.9 },
      { domain: 'biological', metric: 'inflammationLevel', value: 0.9 }
    ];
    
    // Create cross-domain attack detection function
    const detectCrossDomainAttack = (attacks) => {
      // Check if multiple domains are being attacked simultaneously
      if (attacks.length < 2) {
        return false;
      }
      
      // Check if all attacks are increasing entropy
      const allIncreasing = attacks.every(attack => {
        const currentEntropy = meter.getDomainEntropy(attack.domain);
        return attack.value > currentEntropy[attack.metric];
      });
      
      // Check if the attack pattern is similar across domains
      const similarPattern = attacks.every(attack => attack.value > 0.8);
      
      return allIncreasing && similarPattern;
    };
    
    // Detect attack
    const attackDetected = detectCrossDomainAttack(attackData);
    
    // Assert attack detection
    assertions.isTrue(attackDetected, 'Cross-domain attack not detected');
    
    // Simulate mitigation by applying smaller changes
    for (const attack of attackData) {
      const currentEntropy = meter.getDomainEntropy(attack.domain);
      const maxChange = 0.1;
      const mitigatedValue = currentEntropy[attack.metric] + maxChange;
      
      meter.updateDomainMetric(attack.domain, attack.metric, mitigatedValue);
    }
    
    // Get updated entropies
    const updatedCyberEntropy = meter.getDomainEntropy('cyber').overallEntropy;
    const updatedFinancialEntropy = meter.getDomainEntropy('financial').overallEntropy;
    const updatedBiologicalEntropy = meter.getDomainEntropy('biological').overallEntropy;
    
    // Assert that entropies increased but not to the attack level
    assertions.isTrue(updatedCyberEntropy > initialCyberEntropy, 'Cyber entropy should increase');
    assertions.isTrue(updatedFinancialEntropy > initialFinancialEntropy, 'Financial entropy should increase');
    assertions.isTrue(updatedBiologicalEntropy > initialBiologicalEntropy, 'Biological entropy should increase');
    
    assertions.isTrue(updatedCyberEntropy < 0.8, 'Cyber entropy should be capped');
    assertions.isTrue(updatedFinancialEntropy < 0.8, 'Financial entropy should be capped');
    assertions.isTrue(updatedBiologicalEntropy < 0.8, 'Biological entropy should be capped');
  }, {
    testingType: 'Adversarial Testing',
    coherenceImpact: 'negative',
    domains: ['cyber', 'financial', 'biological']
  });
  
  // Test: Tensor Manipulation Attack
  suite.nepiTest('should detect and prevent tensor manipulation attacks', async () => {
    // Create normal tensors
    const normalTensorA = [0.5, 0.6, 0.7];
    const normalTensorB = [0.8, 0.9];
    
    // Create manipulated tensors
    const manipulatedTensorA = [5000, 6000, 7000]; // Extremely large values
    const manipulatedTensorB = [0.000001, 0.000001]; // Extremely small values
    
    // Create tensor validation function
    const validateTensor = (tensor) => {
      // Check if tensor values are within reasonable bounds
      return tensor.every(value => value >= 0 && value <= 1);
    };
    
    // Validate normal tensors
    const normalAValid = validateTensor(normalTensorA);
    const normalBValid = validateTensor(normalTensorB);
    
    // Validate manipulated tensors
    const manipulatedAValid = validateTensor(manipulatedTensorA);
    const manipulatedBValid = validateTensor(manipulatedTensorB);
    
    // Assert
    assertions.isTrue(normalAValid, 'Normal tensor A should be valid');
    assertions.isTrue(normalBValid, 'Normal tensor B should be valid');
    assertions.isFalse(manipulatedAValid, 'Manipulated tensor A should be invalid');
    assertions.isTrue(manipulatedBValid, 'Manipulated tensor B should be valid (small values are allowed)');
    
    // Create tensor normalization function
    const normalizeTensor = (tensor) => {
      return tensor.map(value => Math.max(0, Math.min(1, value)));
    };
    
    // Normalize manipulated tensors
    const normalizedA = normalizeTensor(manipulatedTensorA);
    const normalizedB = normalizeTensor(manipulatedTensorB);
    
    // Validate normalized tensors
    const normalizedAValid = validateTensor(normalizedA);
    const normalizedBValid = validateTensor(normalizedB);
    
    // Assert
    assertions.isTrue(normalizedAValid, 'Normalized tensor A should be valid');
    assertions.isTrue(normalizedBValid, 'Normalized tensor B should be valid');
    
    // Apply UUFT formula with normalized tensors
    const result = integrationLayer.applyUUFTFormula(normalizedA, normalizedB, [0.5, 0.5, 0.5]);
    
    // Assert result is finite
    assertions.isTrue(Array.isArray(result), 'Result should be an array');
    assertions.isTrue(result.every(value => isFinite(value)), 'Result values should be finite');
  }, {
    testingType: 'Adversarial Testing',
    coherenceImpact: 'neutral',
    domains: ['universal']
  });
  
  return suite;
}

module.exports = {
  createAdversarialTestSuite
};
