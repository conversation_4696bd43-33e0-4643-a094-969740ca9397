/**
 * NovaStore Component Model
 * 
 * This module defines the component model for the NovaStore marketplace.
 * It includes integration with the Trinity CSDE verification system.
 */

class NovaStoreComponent {
  /**
   * Create a new NovaStore Component
   * @param {Object} data - Component data
   */
  constructor(data = {}) {
    // Basic component information
    this.id = data.id || this._generateId();
    this.name = data.name || 'Unnamed Component';
    this.description = data.description || '';
    this.version = data.version || '1.0.0';
    this.author = data.author || '';
    this.license = data.license || 'MIT';
    this.category = data.category || 'general';
    this.tags = data.tags || [];
    this.timestamp = data.timestamp || new Date().toISOString();
    
    // Component capabilities
    this.capabilities = data.capabilities || [];
    this.interfaces = data.interfaces || [];
    this.dependencies = data.dependencies || [];
    
    // Security and compliance
    this.policies = data.policies || [];
    this.complianceScore = data.complianceScore || 0.5;
    this.auditFrequency = data.auditFrequency || 1;
    
    // Detection capabilities
    this.detectionCapability = data.detectionCapability || 0.5;
    this.threatSeverity = data.threatSeverity || 0.5;
    this.threatConfidence = data.threatConfidence || 0.5;
    this.baselineSignals = data.baselineSignals || this.detectionCapability;
    this.threats = data.threats || {};
    
    // Response capabilities
    this.baseResponseTime = data.baseResponseTime || 100;
    this.threatSurface = data.threatSurface || 1;
    this.systemRadius = data.systemRadius || 100;
    this.reactionTime = data.reactionTime || 0.5;
    this.mitigationSurface = data.mitigationSurface || 0.5;
    
    // Verification status
    this.verification = data.verification || {
      status: 'unverified',
      score: 0,
      timestamp: null,
      level: null,
      qualityMetrics: null,
      adaptiveRatios: null
    };
    
    // Revenue information
    this.pricing = data.pricing || {
      model: 'free',  // free, one-time, subscription
      price: 0,
      currency: 'USD',
      billingCycle: null  // monthly, yearly, null
    };
    this.estimatedRevenue = data.estimatedRevenue || 0;
    this.revenueShare = data.revenueShare || {
      novaFuse: 0.18,
      partners: 0.82
    };
    
    // Usage metrics
    this.downloads = data.downloads || 0;
    this.ratings = data.ratings || [];
    this.averageRating = data.averageRating || 0;
  }
  
  /**
   * Generate a unique ID
   * @returns {string} - Unique ID
   * @private
   */
  _generateId() {
    return 'comp_' + Math.random().toString(36).substring(2, 15) + 
      Math.random().toString(36).substring(2, 15);
  }
  
  /**
   * Update verification status
   * @param {Object} verificationResult - Verification result
   */
  updateVerification(verificationResult) {
    this.verification = {
      status: verificationResult.verificationStatus,
      score: verificationResult.verificationScore,
      timestamp: verificationResult.timestamp,
      level: verificationResult.verificationLevel,
      qualityMetrics: verificationResult.qualityMetrics,
      adaptiveRatios: verificationResult.adaptiveRatios
    };
    
    // Update revenue share
    if (verificationResult.revenueShare) {
      this.revenueShare = {
        novaFuse: verificationResult.revenueShare.novaFuse,
        partners: verificationResult.revenueShare.partners
      };
    }
  }
  
  /**
   * Calculate average rating
   */
  calculateAverageRating() {
    if (this.ratings.length === 0) {
      this.averageRating = 0;
      return;
    }
    
    const sum = this.ratings.reduce((total, rating) => total + rating.value, 0);
    this.averageRating = sum / this.ratings.length;
  }
  
  /**
   * Add a rating
   * @param {Object} rating - Rating object
   */
  addRating(rating) {
    this.ratings.push({
      userId: rating.userId,
      value: rating.value,
      comment: rating.comment,
      timestamp: rating.timestamp || new Date().toISOString()
    });
    
    this.calculateAverageRating();
  }
  
  /**
   * Increment download count
   */
  incrementDownloads() {
    this.downloads += 1;
  }
  
  /**
   * Check if component is verified
   * @returns {boolean} - Whether component is verified
   */
  isVerified() {
    return this.verification.status === 'verified';
  }
  
  /**
   * Get verification badge level
   * @returns {string} - Badge level (gold, silver, bronze, none)
   */
  getVerificationBadge() {
    if (!this.isVerified()) {
      return 'none';
    }
    
    const score = this.verification.score;
    
    if (score >= 0.9) {
      return 'gold';
    } else if (score >= 0.8) {
      return 'silver';
    } else if (score >= 0.7) {
      return 'bronze';
    } else {
      return 'none';
    }
  }
  
  /**
   * Get component data for API response
   * @param {boolean} [includeDetails=false] - Whether to include all details
   * @returns {Object} - Component data
   */
  toJSON(includeDetails = false) {
    const basic = {
      id: this.id,
      name: this.name,
      description: this.description,
      version: this.version,
      author: this.author,
      category: this.category,
      tags: this.tags,
      verification: {
        status: this.verification.status,
        score: this.verification.score,
        badge: this.getVerificationBadge(),
        timestamp: this.verification.timestamp
      },
      pricing: {
        model: this.pricing.model,
        price: this.pricing.price,
        currency: this.pricing.currency
      },
      downloads: this.downloads,
      averageRating: this.averageRating
    };
    
    if (!includeDetails) {
      return basic;
    }
    
    return {
      ...basic,
      license: this.license,
      timestamp: this.timestamp,
      capabilities: this.capabilities,
      interfaces: this.interfaces,
      dependencies: this.dependencies,
      policies: this.policies,
      complianceScore: this.complianceScore,
      auditFrequency: this.auditFrequency,
      detectionCapability: this.detectionCapability,
      threatSeverity: this.threatSeverity,
      threatConfidence: this.threatConfidence,
      baselineSignals: this.baselineSignals,
      threats: this.threats,
      baseResponseTime: this.baseResponseTime,
      threatSurface: this.threatSurface,
      systemRadius: this.systemRadius,
      reactionTime: this.reactionTime,
      mitigationSurface: this.mitigationSurface,
      verification: this.verification,
      pricing: this.pricing,
      estimatedRevenue: this.estimatedRevenue,
      revenueShare: this.revenueShare,
      ratings: this.ratings
    };
  }
}

module.exports = NovaStoreComponent;
