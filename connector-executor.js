/**
 * NovaFuse Universal API Connector Executor
 * 
 * This module handles the execution of API connectors based on templates from the registry.
 */

const axios = require('axios');
const connectorRegistry = require('./registry/connector-registry');
const jsonpath = require('jsonpath');

class ConnectorExecutor {
  constructor() {
    this.activeRequests = new Map();
    this.metrics = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      totalResponseTime: 0
    };
  }

  /**
   * Execute a connector endpoint
   * 
   * @param {string} connectorId - The ID of the connector to execute
   * @param {string} endpointId - The ID of the endpoint to execute
   * @param {Object} parameters - Parameters for the endpoint
   * @returns {Promise<Object>} - The response from the API
   */
  async executeConnector(connectorId, endpointId, parameters = {}) {
    try {
      // Get the connector from the registry
      const connector = connectorRegistry.getConnector(connectorId);
      
      if (!connector) {
        throw new Error(`Connector ${connectorId} not found`);
      }
      
      // Find the endpoint in the connector
      const endpoint = connector.endpoints.find(e => e.id === endpointId);
      
      if (!endpoint) {
        throw new Error(`Endpoint ${endpointId} not found in connector ${connectorId}`);
      }
      
      // Start timing the request
      const startTime = Date.now();
      
      // Generate a unique request ID
      const requestId = `${connectorId}-${endpointId}-${Date.now()}`;
      
      // Track the active request
      this.activeRequests.set(requestId, {
        connectorId,
        endpointId,
        startTime,
        parameters
      });
      
      // Build the request URL
      let url = connector.configuration.baseUrl;
      let path = endpoint.path;
      
      // Replace path parameters
      if (parameters.path) {
        for (const [key, value] of Object.entries(parameters.path)) {
          path = path.replace(`{${key}}`, encodeURIComponent(value));
        }
      }
      
      url = url.endsWith('/') ? `${url}${path.startsWith('/') ? path.substring(1) : path}` : `${url}${path.startsWith('/') ? path : `/${path}`}`;
      
      // Build request headers
      const headers = {
        ...connector.configuration.headers
      };
      
      // Add authentication headers
      if (connector.authentication.type === 'API_KEY') {
        const apiKeyField = Object.keys(connector.authentication.fields).find(f => 
          connector.authentication.fields[f].type === 'string' && 
          connector.authentication.fields[f].sensitive === true
        );
        
        if (apiKeyField && parameters.auth && parameters.auth[apiKeyField]) {
          headers['Authorization'] = `Bearer ${parameters.auth[apiKeyField]}`;
        }
      } else if (connector.authentication.type === 'BASIC') {
        if (parameters.auth && parameters.auth.username && parameters.auth.password) {
          const auth = Buffer.from(`${parameters.auth.username}:${parameters.auth.password}`).toString('base64');
          headers['Authorization'] = `Basic ${auth}`;
        }
      } else if (connector.authentication.type === 'OAUTH2') {
        if (parameters.auth && parameters.auth.token) {
          headers['Authorization'] = `Bearer ${parameters.auth.token}`;
        }
      }
      
      // Add custom headers from parameters
      if (parameters.headers) {
        Object.assign(headers, parameters.headers);
      }
      
      // Build request config
      const requestConfig = {
        url,
        method: endpoint.method,
        headers,
        params: parameters.query,
        data: parameters.body,
        timeout: connector.configuration.timeout || 30000
      };
      
      // Execute the request
      const response = await axios(requestConfig);
      
      // Extract data using JSONPath if specified
      let result = response.data;
      
      if (endpoint.response && endpoint.response.dataPath) {
        result = jsonpath.query(response.data, endpoint.response.dataPath);
      }
      
      // Calculate response time
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      // Update metrics
      this.metrics.totalRequests++;
      this.metrics.successfulRequests++;
      this.metrics.totalResponseTime += responseTime;
      this.metrics.averageResponseTime = this.metrics.totalResponseTime / this.metrics.totalRequests;
      
      // Remove from active requests
      this.activeRequests.delete(requestId);
      
      return {
        success: true,
        data: result,
        responseTime,
        statusCode: response.status
      };
    } catch (error) {
      // Update metrics
      this.metrics.totalRequests++;
      this.metrics.failedRequests++;
      
      return {
        success: false,
        error: error.message,
        statusCode: error.response ? error.response.status : 500
      };
    }
  }

  /**
   * Get metrics for the connector executor
   * 
   * @returns {Object} - Metrics
   */
  getMetrics() {
    return this.metrics;
  }

  /**
   * Get active requests
   * 
   * @returns {Array} - Active requests
   */
  getActiveRequests() {
    return Array.from(this.activeRequests.values());
  }
}

// Create and export a singleton instance
const connectorExecutor = new ConnectorExecutor();

module.exports = connectorExecutor;
