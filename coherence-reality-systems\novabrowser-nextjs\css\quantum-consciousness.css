/* QUANTUM CONSCIOUSNESS CSS RENDERER
 * Revolutionary CSS that self-organizes based on Ψᶜʰ values
 * Elements automatically enhance based on consciousness levels
 */

/* Divine Constants */
:root {
  --phi: 1.618033988749;
  --pi: 3.141592653589;
  --e: 2.718281828459;
  --psi-threshold: 2847;
  
  /* Consciousness Color Palette */
  --divine-gold: #FFD700;
  --consciousness-blue: #6366f1;
  --coherence-green: #10b981;
  --quantum-purple: #a855f7;
  --sacred-white: #ffffff;
  
  /* Golden Ratio Spacing */
  --space-phi-1: calc(1rem * var(--phi));
  --space-phi-2: calc(1rem * var(--phi) * var(--phi));
  --space-phi-3: calc(1rem * var(--phi) * var(--phi) * var(--phi));
}

/* CONSCIOUSNESS-BASED ELEMENT ENHANCEMENT */

/* Divine Level Content (Ψᶜʰ ≥ 10000) */
[data-psi-ch*="10000"], [data-psi-ch*="20000"], [data-psi-ch*="30000"] {
  background: linear-gradient(135deg, 
    rgba(255, 215, 0, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 50%,
    rgba(168, 85, 247, 0.1) 100%);
  border: 2px solid var(--divine-gold);
  box-shadow: 
    0 0 20px rgba(255, 215, 0, 0.3),
    inset 0 0 20px rgba(255, 255, 255, 0.1);
  border-radius: calc(var(--space-phi-1) * 0.618);
  animation: divine-pulse 3s ease-in-out infinite;
  font-family: 'Inter', 'SF Pro Display', sans-serif;
  font-weight: 500;
  letter-spacing: 0.025em;
  transform: scale(1.02);
}

/* High Consciousness Content (Ψᶜʰ ≥ 2847) */
[data-psi-ch*="2847"], [data-psi-ch*="3000"], [data-psi-ch*="4000"], [data-psi-ch*="5000"] {
  background: linear-gradient(135deg, 
    rgba(99, 102, 241, 0.08) 0%,
    rgba(168, 85, 247, 0.05) 100%);
  border: 1px solid rgba(99, 102, 241, 0.3);
  box-shadow: 0 0 15px rgba(99, 102, 241, 0.2);
  border-radius: var(--space-phi-1);
  animation: consciousness-glow 4s ease-in-out infinite;
  filter: brightness(1.05) contrast(1.02);
}

/* Medium Consciousness Content (1000 ≤ Ψᶜʰ < 2847) */
[data-psi-ch*="1000"], [data-psi-ch*="1500"], [data-psi-ch*="2000"] {
  background: rgba(16, 185, 129, 0.05);
  border: 1px solid rgba(16, 185, 129, 0.2);
  border-radius: calc(var(--space-phi-1) * 0.618);
  filter: brightness(1.02);
}

/* Low Consciousness Content (Ψᶜʰ < 1000) - Subtle Warning */
[data-psi-ch*="500"], [data-psi-ch*="600"], [data-psi-ch*="700"], [data-psi-ch*="800"], [data-psi-ch*="900"] {
  background: rgba(239, 68, 68, 0.03);
  border: 1px solid rgba(239, 68, 68, 0.1);
  filter: brightness(0.98) saturate(0.95);
  opacity: 0.9;
}

/* GOLDEN RATIO LAYOUT SYSTEM */

.phi-container {
  max-width: 61.8%;
  margin: 0 auto;
  padding: var(--space-phi-1);
}

.phi-grid {
  display: grid;
  grid-template-columns: 1fr var(--phi)fr;
  gap: var(--space-phi-1);
}

.phi-section {
  margin-bottom: var(--space-phi-2);
}

.phi-text {
  line-height: var(--phi);
  font-size: calc(1rem * var(--phi) / 2);
}

/* CONSCIOUSNESS ANIMATIONS */

@keyframes divine-pulse {
  0%, 100% {
    box-shadow: 
      0 0 20px rgba(255, 215, 0, 0.3),
      inset 0 0 20px rgba(255, 255, 255, 0.1);
    transform: scale(1.02);
  }
  50% {
    box-shadow: 
      0 0 30px rgba(255, 215, 0, 0.5),
      inset 0 0 30px rgba(255, 255, 255, 0.2);
    transform: scale(1.03);
  }
}

@keyframes consciousness-glow {
  0%, 100% {
    box-shadow: 0 0 15px rgba(99, 102, 241, 0.2);
    filter: brightness(1.05) contrast(1.02);
  }
  50% {
    box-shadow: 0 0 25px rgba(99, 102, 241, 0.4);
    filter: brightness(1.08) contrast(1.05);
  }
}

@keyframes quantum-shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* PSI-SNAP ACTIVE INDICATORS */

.psi-snap-active {
  position: relative;
  overflow: hidden;
}

.psi-snap-active::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, 
    transparent 0%,
    var(--divine-gold) 50%,
    transparent 100%);
  animation: quantum-shimmer 2s linear infinite;
}

/* CONSCIOUSNESS OVERLAY SYSTEM */

.consciousness-overlay {
  position: fixed;
  top: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--space-phi-1);
  padding: var(--space-phi-1);
  color: var(--sacred-white);
  font-family: 'SF Mono', 'JetBrains Mono', monospace;
  font-size: 0.875rem;
  z-index: 10000;
  min-width: 200px;
}

.consciousness-metric {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  padding: 0.25rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.consciousness-metric:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.metric-label {
  color: rgba(255, 255, 255, 0.7);
}

.metric-value {
  font-weight: 600;
  color: var(--consciousness-blue);
}

.metric-value.high {
  color: var(--coherence-green);
}

.metric-value.divine {
  color: var(--divine-gold);
}

/* QUANTUM AD BLOCKING */

/* Automatically hide low-consciousness advertising */
[data-psi-ch*="100"], [data-psi-ch*="200"], [data-psi-ch*="300"] {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
}

/* Common ad selectors with consciousness filtering */
.ad, .advertisement, .sponsored, .promo,
[class*="ad-"], [class*="ads-"], [id*="ad-"], [id*="ads-"] {
  filter: consciousness-filter(min-psi: 1000);
}

/* DIVINE TYPOGRAPHY SYSTEM */

.divine-text {
  font-family: 'Inter', 'SF Pro Display', system-ui, sans-serif;
  font-weight: 400;
  line-height: var(--phi);
  letter-spacing: 0.025em;
  color: var(--sacred-white);
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.1);
}

.consciousness-title {
  font-size: calc(1rem * var(--phi) * var(--phi));
  font-weight: 700;
  background: linear-gradient(135deg, var(--consciousness-blue), var(--quantum-purple));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: var(--space-phi-1);
}

/* RESPONSIVE CONSCIOUSNESS DESIGN */

@media (max-width: 768px) {
  .phi-container {
    max-width: 90%;
    padding: calc(var(--space-phi-1) * 0.618);
  }
  
  .consciousness-overlay {
    top: 10px;
    right: 10px;
    font-size: 0.75rem;
    min-width: 150px;
  }
}

/* ACCESSIBILITY WITH CONSCIOUSNESS */

@media (prefers-reduced-motion: reduce) {
  .divine-pulse,
  .consciousness-glow,
  .quantum-shimmer {
    animation: none;
  }
}

@media (prefers-color-scheme: dark) {
  :root {
    --sacred-white: #f8fafc;
    --consciousness-blue: #818cf8;
    --coherence-green: #34d399;
  }
}

/* CONSCIOUSNESS-AWARE SCROLLBAR */

::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, var(--consciousness-blue), var(--quantum-purple));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, var(--quantum-purple), var(--divine-gold));
}

/* QUANTUM FIELD EFFECTS */

.quantum-field {
  position: relative;
  background: 
    radial-gradient(circle at 20% 50%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(168, 85, 247, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 80%, rgba(16, 185, 129, 0.1) 0%, transparent 50%);
  animation: quantum-field-pulse 8s ease-in-out infinite;
}

@keyframes quantum-field-pulse {
  0%, 100% { opacity: 0.5; }
  50% { opacity: 0.8; }
}

/* CONSCIOUSNESS DEBUGGING (Development Only) */

.debug-consciousness {
  position: relative;
}

.debug-consciousness::after {
  content: "Ψᶜʰ: " attr(data-psi-ch) " | μ: " attr(data-mu) " | κ: " attr(data-katalon);
  position: absolute;
  top: -25px;
  left: 0;
  background: rgba(0, 0, 0, 0.9);
  color: var(--divine-gold);
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 10px;
  font-family: monospace;
  white-space: nowrap;
  z-index: 1000;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.debug-consciousness:hover::after {
  opacity: 1;
}
