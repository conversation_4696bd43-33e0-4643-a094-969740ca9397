/**
 * Threat Detector
 *
 * This module provides advanced threat detection capabilities for the Finite Universe
 * Principle defense system, enabling behavior analysis, anomaly detection, and
 * threat intelligence integration.
 */

const EventEmitter = require('events');

/**
 * ThreatDetector class
 * 
 * Provides advanced threat detection capabilities.
 */
class ThreatDetector extends EventEmitter {
  /**
   * Constructor
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();

    this.options = {
      enableLogging: true,
      behaviorAnalysisEnabled: options.behaviorAnalysisEnabled !== undefined ? options.behaviorAnalysisEnabled : true,
      anomalyDetectionEnabled: options.anomalyDetectionEnabled !== undefined ? options.anomalyDetectionEnabled : true,
      threatIntelligenceEnabled: options.threatIntelligenceEnabled !== undefined ? options.threatIntelligenceEnabled : true,
      behaviorAnalysisThreshold: options.behaviorAnalysisThreshold || 0.7, // 0.0 to 1.0
      anomalyDetectionThreshold: options.anomalyDetectionThreshold || 3.0, // Z-score
      threatIntelligenceThreshold: options.threatIntelligenceThreshold || 0.5, // 0.0 to 1.0
      historySize: options.historySize || 1000,
      analysisInterval: options.analysisInterval || 60000, // 1 minute in milliseconds
      ...options
    };

    // Initialize behavior profiles
    this.behaviorProfiles = new Map();
    
    // Initialize anomaly history
    this.anomalyHistory = new Map();
    
    // Initialize threat intelligence
    this.threatIntelligence = new Map();
    
    // Initialize event history
    this.eventHistory = [];
    
    // Initialize threat intelligence providers
    this.threatIntelligenceProviders = [];
    
    // Initialize analysis interval
    this.analysisInterval = null;

    if (this.options.enableLogging) {
      console.log('ThreatDetector initialized with options:', this.options);
    }
  }

  /**
   * Start threat detection
   */
  start() {
    if (this.analysisInterval) {
      return;
    }

    // Start analysis interval
    this.analysisInterval = setInterval(() => {
      this._analyzeThreats();
    }, this.options.analysisInterval);

    if (this.options.enableLogging) {
      console.log('ThreatDetector started');
    }
    
    // Emit start event
    this.emit('start');
  }

  /**
   * Stop threat detection
   */
  stop() {
    if (!this.analysisInterval) {
      return;
    }

    // Clear analysis interval
    clearInterval(this.analysisInterval);
    this.analysisInterval = null;

    if (this.options.enableLogging) {
      console.log('ThreatDetector stopped');
    }
    
    // Emit stop event
    this.emit('stop');
  }

  /**
   * Register a threat intelligence provider
   * @param {Object} provider - Threat intelligence provider
   * @returns {boolean} - True if provider was registered, false otherwise
   */
  registerThreatIntelligenceProvider(provider) {
    // Check if provider has required methods
    if (!provider.checkThreat) {
      return false;
    }
    
    // Register provider
    this.threatIntelligenceProviders.push(provider);
    
    if (this.options.enableLogging) {
      console.log('Threat intelligence provider registered');
    }
    
    // Emit provider-registered event
    this.emit('provider-registered', { provider });
    
    return true;
  }

  /**
   * Process an event for threat detection
   * @param {Object} event - Event to process
   * @returns {Object} - Threat detection result
   */
  async processEvent(event) {
    // Validate event
    if (!event || !event.type || !event.source) {
      throw new Error('Invalid event');
    }
    
    // Add timestamp if not present
    if (!event.timestamp) {
      event.timestamp = Date.now();
    }
    
    // Add event to history
    this._addToEventHistory(event);
    
    // Initialize threat detection result
    const result = {
      event,
      threatDetected: false,
      threatLevel: 0,
      threatType: null,
      threatSource: null,
      threatDetails: null,
      behaviorAnalysis: null,
      anomalyDetection: null,
      threatIntelligence: null
    };
    
    // Perform behavior analysis
    if (this.options.behaviorAnalysisEnabled) {
      const behaviorResult = this._analyzeBehavior(event);
      result.behaviorAnalysis = behaviorResult;
      
      if (behaviorResult.threatDetected) {
        result.threatDetected = true;
        result.threatLevel = Math.max(result.threatLevel, behaviorResult.threatLevel);
        result.threatType = result.threatType || behaviorResult.threatType;
        result.threatSource = result.threatSource || 'behavior-analysis';
        result.threatDetails = result.threatDetails || behaviorResult.details;
      }
    }
    
    // Perform anomaly detection
    if (this.options.anomalyDetectionEnabled) {
      const anomalyResult = this._detectAnomalies(event);
      result.anomalyDetection = anomalyResult;
      
      if (anomalyResult.threatDetected) {
        result.threatDetected = true;
        result.threatLevel = Math.max(result.threatLevel, anomalyResult.threatLevel);
        result.threatType = result.threatType || anomalyResult.threatType;
        result.threatSource = result.threatSource || 'anomaly-detection';
        result.threatDetails = result.threatDetails || anomalyResult.details;
      }
    }
    
    // Check threat intelligence
    if (this.options.threatIntelligenceEnabled && this.threatIntelligenceProviders.length > 0) {
      const intelResult = await this._checkThreatIntelligence(event);
      result.threatIntelligence = intelResult;
      
      if (intelResult.threatDetected) {
        result.threatDetected = true;
        result.threatLevel = Math.max(result.threatLevel, intelResult.threatLevel);
        result.threatType = result.threatType || intelResult.threatType;
        result.threatSource = result.threatSource || 'threat-intelligence';
        result.threatDetails = result.threatDetails || intelResult.details;
      }
    }
    
    // Emit events based on threat detection
    if (result.threatDetected) {
      if (this.options.enableLogging) {
        console.log(`Threat detected: ${result.threatType} (Level: ${result.threatLevel})`);
      }
      
      // Emit threat-detected event
      this.emit('threat-detected', result);
      
      // Emit specific threat events
      if (result.behaviorAnalysis && result.behaviorAnalysis.threatDetected) {
        this.emit('behavior-threat-detected', result.behaviorAnalysis);
      }
      
      if (result.anomalyDetection && result.anomalyDetection.threatDetected) {
        this.emit('anomaly-threat-detected', result.anomalyDetection);
      }
      
      if (result.threatIntelligence && result.threatIntelligence.threatDetected) {
        this.emit('intelligence-threat-detected', result.threatIntelligence);
      }
    }
    
    return result;
  }

  /**
   * Add event to history
   * @param {Object} event - Event to add
   * @private
   */
  _addToEventHistory(event) {
    // Add event to history
    this.eventHistory.push(event);
    
    // Trim history if needed
    if (this.eventHistory.length > this.options.historySize) {
      this.eventHistory.shift();
    }
    
    // Update behavior profile
    this._updateBehaviorProfile(event);
    
    // Update anomaly history
    this._updateAnomalyHistory(event);
  }

  /**
   * Update behavior profile
   * @param {Object} event - Event to process
   * @private
   */
  _updateBehaviorProfile(event) {
    // Get source identifier
    const sourceId = this._getSourceId(event);
    
    // Get or create behavior profile
    let profile = this.behaviorProfiles.get(sourceId);
    
    if (!profile) {
      profile = {
        sourceId,
        eventCounts: {},
        eventSequences: {},
        timePatterns: {},
        lastSeen: event.timestamp,
        firstSeen: event.timestamp,
        totalEvents: 0
      };
      
      this.behaviorProfiles.set(sourceId, profile);
    }
    
    // Update event counts
    profile.eventCounts[event.type] = (profile.eventCounts[event.type] || 0) + 1;
    
    // Update event sequences
    if (profile.lastEventType) {
      const sequence = `${profile.lastEventType}->${event.type}`;
      profile.eventSequences[sequence] = (profile.eventSequences[sequence] || 0) + 1;
    }
    
    // Update time patterns
    const hour = new Date(event.timestamp).getHours();
    profile.timePatterns[hour] = (profile.timePatterns[hour] || 0) + 1;
    
    // Update profile metadata
    profile.lastEventType = event.type;
    profile.lastSeen = event.timestamp;
    profile.totalEvents++;
  }

  /**
   * Update anomaly history
   * @param {Object} event - Event to process
   * @private
   */
  _updateAnomalyHistory(event) {
    // Get source identifier
    const sourceId = this._getSourceId(event);
    
    // Get or create anomaly history
    let history = this.anomalyHistory.get(sourceId);
    
    if (!history) {
      history = {
        sourceId,
        events: [],
        metrics: {}
      };
      
      this.anomalyHistory.set(sourceId, history);
    }
    
    // Add event to history
    history.events.push(event);
    
    // Trim history if needed
    if (history.events.length > this.options.historySize) {
      history.events.shift();
    }
    
    // Update metrics
    this._updateAnomalyMetrics(history, event);
  }

  /**
   * Update anomaly metrics
   * @param {Object} history - Anomaly history
   * @param {Object} event - Event to process
   * @private
   */
  _updateAnomalyMetrics(history, event) {
    // Calculate event frequency
    const now = event.timestamp;
    const recentEvents = history.events.filter(e => now - e.timestamp < 3600000); // Last hour
    
    history.metrics.hourlyFrequency = recentEvents.length;
    
    // Calculate time between events
    if (history.events.length >= 2) {
      const lastEvent = history.events[history.events.length - 2];
      const timeBetween = event.timestamp - lastEvent.timestamp;
      
      if (!history.metrics.timeBetweenEvents) {
        history.metrics.timeBetweenEvents = {
          values: [],
          mean: 0,
          stdDev: 0
        };
      }
      
      history.metrics.timeBetweenEvents.values.push(timeBetween);
      
      // Trim values if needed
      if (history.metrics.timeBetweenEvents.values.length > 100) {
        history.metrics.timeBetweenEvents.values.shift();
      }
      
      // Calculate mean and standard deviation
      const values = history.metrics.timeBetweenEvents.values;
      const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
      const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
      const stdDev = Math.sqrt(variance);
      
      history.metrics.timeBetweenEvents.mean = mean;
      history.metrics.timeBetweenEvents.stdDev = stdDev;
    }
    
    // Calculate event type frequency
    if (!history.metrics.eventTypeFrequency) {
      history.metrics.eventTypeFrequency = {};
    }
    
    history.metrics.eventTypeFrequency[event.type] = (history.metrics.eventTypeFrequency[event.type] || 0) + 1;
  }

  /**
   * Analyze behavior for threats
   * @param {Object} event - Event to analyze
   * @returns {Object} - Behavior analysis result
   * @private
   */
  _analyzeBehavior(event) {
    // Get source identifier
    const sourceId = this._getSourceId(event);
    
    // Get behavior profile
    const profile = this.behaviorProfiles.get(sourceId);
    
    if (!profile || profile.totalEvents < 10) {
      return {
        threatDetected: false,
        threatLevel: 0,
        threatType: null,
        details: 'Insufficient behavior data'
      };
    }
    
    // Initialize threat indicators
    const indicators = [];
    
    // Check for unusual event type
    const eventTypeFrequency = profile.eventCounts[event.type] || 0;
    const eventTypeRatio = eventTypeFrequency / profile.totalEvents;
    
    if (eventTypeRatio < 0.01 && profile.totalEvents > 100) {
      indicators.push({
        type: 'unusual-event-type',
        score: 0.7,
        details: `Unusual event type: ${event.type} (${eventTypeFrequency}/${profile.totalEvents})`
      });
    }
    
    // Check for unusual event sequence
    if (profile.lastEventType) {
      const sequence = `${profile.lastEventType}->${event.type}`;
      const sequenceFrequency = profile.eventSequences[sequence] || 0;
      const sequenceRatio = sequenceFrequency / profile.totalEvents;
      
      if (sequenceRatio < 0.01 && profile.totalEvents > 100) {
        indicators.push({
          type: 'unusual-event-sequence',
          score: 0.8,
          details: `Unusual event sequence: ${sequence} (${sequenceFrequency}/${profile.totalEvents})`
        });
      }
    }
    
    // Check for unusual time pattern
    const hour = new Date(event.timestamp).getHours();
    const hourFrequency = profile.timePatterns[hour] || 0;
    const hourRatio = hourFrequency / profile.totalEvents;
    
    if (hourRatio < 0.01 && profile.totalEvents > 100) {
      indicators.push({
        type: 'unusual-time-pattern',
        score: 0.6,
        details: `Unusual time pattern: Hour ${hour} (${hourFrequency}/${profile.totalEvents})`
      });
    }
    
    // Calculate overall threat score
    let threatScore = 0;
    
    if (indicators.length > 0) {
      threatScore = indicators.reduce((max, indicator) => Math.max(max, indicator.score), 0);
    }
    
    // Determine if threat is detected
    const threatDetected = threatScore >= this.options.behaviorAnalysisThreshold;
    
    return {
      threatDetected,
      threatLevel: threatScore,
      threatType: threatDetected ? 'suspicious-behavior' : null,
      details: indicators.length > 0 ? indicators : 'No suspicious behavior detected'
    };
  }

  /**
   * Detect anomalies
   * @param {Object} event - Event to analyze
   * @returns {Object} - Anomaly detection result
   * @private
   */
  _detectAnomalies(event) {
    // Get source identifier
    const sourceId = this._getSourceId(event);
    
    // Get anomaly history
    const history = this.anomalyHistory.get(sourceId);
    
    if (!history || history.events.length < 10) {
      return {
        threatDetected: false,
        threatLevel: 0,
        threatType: null,
        details: 'Insufficient anomaly data'
      };
    }
    
    // Initialize anomalies
    const anomalies = [];
    
    // Check for frequency anomalies
    if (history.metrics.hourlyFrequency > 100) {
      anomalies.push({
        type: 'high-frequency',
        score: 0.7,
        details: `High event frequency: ${history.metrics.hourlyFrequency} events/hour`
      });
    }
    
    // Check for time between events anomalies
    if (history.metrics.timeBetweenEvents && history.metrics.timeBetweenEvents.values.length >= 10) {
      const timeBetween = event.timestamp - history.events[history.events.length - 2].timestamp;
      const mean = history.metrics.timeBetweenEvents.mean;
      const stdDev = history.metrics.timeBetweenEvents.stdDev;
      
      if (stdDev > 0) {
        const zScore = Math.abs(timeBetween - mean) / stdDev;
        
        if (zScore > this.options.anomalyDetectionThreshold) {
          anomalies.push({
            type: 'time-between-anomaly',
            score: Math.min(1.0, zScore / 10),
            details: `Anomalous time between events: ${timeBetween}ms (Z-score: ${zScore.toFixed(2)})`
          });
        }
      }
    }
    
    // Calculate overall threat score
    let threatScore = 0;
    
    if (anomalies.length > 0) {
      threatScore = anomalies.reduce((max, anomaly) => Math.max(max, anomaly.score), 0);
    }
    
    // Determine if threat is detected
    const threatDetected = threatScore >= this.options.anomalyDetectionThreshold / 10;
    
    return {
      threatDetected,
      threatLevel: threatScore,
      threatType: threatDetected ? 'anomalous-activity' : null,
      details: anomalies.length > 0 ? anomalies : 'No anomalies detected'
    };
  }

  /**
   * Check threat intelligence
   * @param {Object} event - Event to check
   * @returns {Promise<Object>} - Threat intelligence result
   * @private
   */
  async _checkThreatIntelligence(event) {
    // Check if there are any providers
    if (this.threatIntelligenceProviders.length === 0) {
      return {
        threatDetected: false,
        threatLevel: 0,
        threatType: null,
        details: 'No threat intelligence providers'
      };
    }
    
    // Initialize threats
    const threats = [];
    
    // Check each provider
    for (const provider of this.threatIntelligenceProviders) {
      try {
        const result = await provider.checkThreat(event);
        
        if (result.threatDetected) {
          threats.push({
            provider: provider.name || 'unknown',
            type: result.threatType || 'unknown-threat',
            score: result.threatLevel || 0.5,
            details: result.details || 'No details provided'
          });
        }
      } catch (error) {
        if (this.options.enableLogging) {
          console.error('Error checking threat intelligence:', error);
        }
      }
    }
    
    // Calculate overall threat score
    let threatScore = 0;
    
    if (threats.length > 0) {
      threatScore = threats.reduce((max, threat) => Math.max(max, threat.score), 0);
    }
    
    // Determine if threat is detected
    const threatDetected = threatScore >= this.options.threatIntelligenceThreshold;
    
    return {
      threatDetected,
      threatLevel: threatScore,
      threatType: threatDetected ? (threats[0]?.type || 'known-threat') : null,
      details: threats.length > 0 ? threats : 'No threats detected'
    };
  }

  /**
   * Analyze threats
   * @private
   */
  _analyzeThreats() {
    // Analyze behavior profiles
    this._analyzeBehaviorProfiles();
    
    // Analyze anomaly history
    this._analyzeAnomalyHistory();
    
    // Analyze threat intelligence
    this._analyzeThreatIntelligence();
    
    if (this.options.enableLogging) {
      console.log('Threat analysis completed');
    }
    
    // Emit analysis-complete event
    this.emit('analysis-complete');
  }

  /**
   * Analyze behavior profiles
   * @private
   */
  _analyzeBehaviorProfiles() {
    // Implement advanced behavior analysis
    // This is a placeholder for more advanced analysis
  }

  /**
   * Analyze anomaly history
   * @private
   */
  _analyzeAnomalyHistory() {
    // Implement advanced anomaly analysis
    // This is a placeholder for more advanced analysis
  }

  /**
   * Analyze threat intelligence
   * @private
   */
  _analyzeThreatIntelligence() {
    // Implement advanced threat intelligence analysis
    // This is a placeholder for more advanced analysis
  }

  /**
   * Get source identifier from event
   * @param {Object} event - Event to process
   * @returns {string} - Source identifier
   * @private
   */
  _getSourceId(event) {
    // Get source identifier
    if (event.source.ip) {
      return `ip:${event.source.ip}`;
    } else if (event.source.userId) {
      return `user:${event.source.userId}`;
    } else if (event.source.sessionId) {
      return `session:${event.source.sessionId}`;
    } else if (event.source.id) {
      return `id:${event.source.id}`;
    } else {
      return `unknown:${JSON.stringify(event.source)}`;
    }
  }

  /**
   * Dispose resources
   */
  dispose() {
    this.stop();
    
    // Clear registries
    this.behaviorProfiles.clear();
    this.anomalyHistory.clear();
    this.threatIntelligence.clear();
    this.eventHistory = [];
    this.threatIntelligenceProviders = [];
    
    if (this.options.enableLogging) {
      console.log('ThreatDetector disposed');
    }
  }
}

/**
 * Create a threat detector with recommended settings
 * @param {Object} options - Configuration options
 * @returns {ThreatDetector} - Configured threat detector
 */
function createThreatDetector(options = {}) {
  return new ThreatDetector({
    enableLogging: true,
    behaviorAnalysisEnabled: true,
    anomalyDetectionEnabled: true,
    threatIntelligenceEnabled: true,
    behaviorAnalysisThreshold: 0.7,
    anomalyDetectionThreshold: 3.0,
    threatIntelligenceThreshold: 0.5,
    historySize: 1000,
    analysisInterval: 60000,
    ...options
  });
}

module.exports = {
  ThreatDetector,
  createThreatDetector
};
