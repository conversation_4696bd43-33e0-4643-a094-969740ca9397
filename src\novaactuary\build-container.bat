@echo off
REM NovaActuary™ Container Build Script - Enhanced

echo.
echo 🚀 Building NovaActuary™ Container
echo The ∂Ψ=0 Underwriting Revolution
echo ================================
echo.

REM Check Docker Desktop
echo [INFO] Checking Docker Desktop status...
docker version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker Desktop not running or not installed
    echo.
    echo Please ensure Docker Desktop is:
    echo 1. Installed on your system
    echo 2. Running (check system tray)
    echo 3. Properly configured
    echo.
    echo Download from: https://www.docker.com/products/docker-desktop
    pause
    exit /b 1
)

echo ✅ Docker Desktop is running
docker --version
echo.

REM Check Docker Compose
docker-compose --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker Compose not available
    echo Please ensure Docker Compose is installed
    pause
    exit /b 1
)

echo ✅ Docker Compose available
echo.

REM Clean up any existing containers
echo [INFO] Cleaning up existing NovaActuary™ containers...
docker-compose down --remove-orphans >nul 2>&1
docker container prune -f >nul 2>&1
echo ✅ Cleanup completed
echo.

REM Build the container
echo 🔨 Building NovaActuary™ container...
echo This may take 5-10 minutes on first build...
echo Please wait while we install dependencies...
echo.

REM Build with verbose output
docker-compose build --no-cache novaactuary

if %errorlevel% equ 0 (
    echo.
    echo ✅ NovaActuary™ container built successfully!
    echo.
    echo 📦 Container Details:
    docker images | findstr novaactuary
    echo.
    echo 🚀 To run the container:
    echo   docker-compose up -d novaactuary
    echo.
    echo 🧪 To test the container:
    echo   run-container.bat
    echo.
    echo 🔍 To view container logs:
    echo   docker-compose logs novaactuary
    echo.
) else (
    echo.
    echo ❌ Container build failed!
    echo.
    echo Common issues:
    echo 1. Docker Desktop not running
    echo 2. Insufficient disk space
    echo 3. Network connectivity issues
    echo 4. Missing dependencies
    echo.
    echo Please check the error messages above and try again.
    echo.
)

pause
