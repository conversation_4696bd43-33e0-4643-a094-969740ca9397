@echo off
REM Simple NovaActuary™ Container Build Script

echo.
echo 🚀 Building NovaActuary™ Container
echo The ∂Ψ=0 Underwriting Revolution
echo ================================
echo.

REM Check Docker
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker not found. Please install Docker Desktop.
    pause
    exit /b 1
)

echo ✅ Docker found
echo.

REM Build the container
echo 🔨 Building NovaActuary™ container...
echo This may take a few minutes...
echo.

docker-compose build novaactuary

if %errorlevel% equ 0 (
    echo.
    echo ✅ NovaActuary™ container built successfully!
    echo.
    echo 📦 Container Details:
    echo   Name: novaactuary-core
    echo   Image: Built from Dockerfile
    echo   Status: Ready for testing
    echo.
    echo 🚀 To run the container:
    echo   docker-compose up -d novaactuary
    echo.
    echo 🧪 To test the container:
    echo   docker-test.bat
    echo.
) else (
    echo.
    echo ❌ Container build failed!
    echo Please check the error messages above.
    echo.
)

pause
