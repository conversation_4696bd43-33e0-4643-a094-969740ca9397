/**
 * Cyber-Safety Visualization API Monitor
 * 
 * This module provides monitoring for the Cyber-Safety visualization API endpoints.
 * It tracks usage, performance, and errors for the API endpoints.
 */

const logger = require('../../config/logger');
const { performance } = require('perf_hooks');
const { createMetric } = require('../../monitoring/metrics');
const { createAlert } = require('../../monitoring/alerts');
const { getRedisClient } = require('../../config/redis');

// Redis client for storing metrics
const redisClient = getRedisClient();

// Metrics
const metrics = {
  requestCount: createMetric('cyber_safety_visualization_request_count', 'counter', 'Number of requests to Cyber-Safety visualization endpoints'),
  responseTime: createMetric('cyber_safety_visualization_response_time', 'histogram', 'Response time for Cyber-Safety visualization endpoints in ms'),
  errorCount: createMetric('cyber_safety_visualization_error_count', 'counter', 'Number of errors from Cyber-Safety visualization endpoints'),
  dataSize: createMetric('cyber_safety_visualization_data_size', 'histogram', 'Size of data returned from Cyber-Safety visualization endpoints in bytes'),
  cacheHitRate: createMetric('cyber_safety_visualization_cache_hit_rate', 'gauge', 'Cache hit rate for Cyber-Safety visualization endpoints')
};

// Alerts
const alerts = {
  highErrorRate: createAlert('cyber_safety_visualization_high_error_rate', 'High error rate for Cyber-Safety visualization endpoints', {
    threshold: 0.05, // 5% error rate
    windowSize: 60 * 1000, // 1 minute
    minSampleSize: 10 // Minimum number of requests to trigger alert
  }),
  slowResponseTime: createAlert('cyber_safety_visualization_slow_response_time', 'Slow response time for Cyber-Safety visualization endpoints', {
    threshold: 1000, // 1 second
    windowSize: 5 * 60 * 1000, // 5 minutes
    minSampleSize: 5 // Minimum number of requests to trigger alert
  })
};

// Constants
const METRICS_TTL = 24 * 60 * 60; // 24 hours in seconds
const RATE_LIMIT_WINDOW = 60; // 1 minute in seconds
const RATE_LIMIT_MAX = 100; // Maximum number of requests per minute

/**
 * Middleware for monitoring API endpoints
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const monitorApiEndpoint = (req, res, next) => {
  // Skip monitoring for non-visualization endpoints
  if (!req.path.includes('/visualizations')) {
    return next();
  }

  // Extract visualization type from path
  const visualizationType = getVisualizationTypeFromPath(req.path);
  
  // Start timer
  const startTime = performance.now();
  
  // Track original end method
  const originalEnd = res.end;
  
  // Override end method to capture response data
  res.end = function(chunk, encoding) {
    // Calculate response time
    const responseTime = performance.now() - startTime;
    
    // Restore original end method
    res.end = originalEnd;
    
    // Call original end method
    res.end(chunk, encoding);
    
    // Process metrics after response is sent
    process.nextTick(() => {
      try {
        // Record request count
        metrics.requestCount.inc({
          path: req.path,
          method: req.method,
          visualizationType: visualizationType || 'unknown',
          statusCode: res.statusCode
        });
        
        // Record response time
        metrics.responseTime.observe({
          path: req.path,
          method: req.method,
          visualizationType: visualizationType || 'unknown',
          statusCode: res.statusCode
        }, responseTime);
        
        // Record error count if status code is 4xx or 5xx
        if (res.statusCode >= 400) {
          metrics.errorCount.inc({
            path: req.path,
            method: req.method,
            visualizationType: visualizationType || 'unknown',
            statusCode: res.statusCode,
            errorType: res.statusCode >= 500 ? 'server' : 'client'
          });
          
          // Check if error rate is high
          checkErrorRate(visualizationType);
        }
        
        // Record data size
        if (chunk) {
          const dataSize = Buffer.isBuffer(chunk) ? chunk.length : Buffer.byteLength(chunk, encoding);
          metrics.dataSize.observe({
            path: req.path,
            method: req.method,
            visualizationType: visualizationType || 'unknown'
          }, dataSize);
        }
        
        // Record cache hit/miss
        if (res.locals.cacheHit !== undefined) {
          recordCacheMetrics(visualizationType, res.locals.cacheHit);
        }
        
        // Check if response time is slow
        if (responseTime > alerts.slowResponseTime.options.threshold) {
          logger.warn(`Slow response time for ${req.path}: ${responseTime.toFixed(2)}ms`);
          
          // Trigger alert if response time is consistently slow
          checkResponseTime(visualizationType, responseTime);
        }
        
        // Store metrics in Redis for historical analysis
        storeMetricsInRedis(visualizationType, {
          timestamp: Date.now(),
          path: req.path,
          method: req.method,
          statusCode: res.statusCode,
          responseTime,
          dataSize: chunk ? (Buffer.isBuffer(chunk) ? chunk.length : Buffer.byteLength(chunk, encoding)) : 0,
          cacheHit: res.locals.cacheHit
        });
        
        // Update rate limiting metrics
        updateRateLimitMetrics(req.ip, visualizationType);
      } catch (error) {
        logger.error('Error in visualization API monitoring:', error);
      }
    });
  };
  
  next();
};

/**
 * Extract visualization type from path
 * @param {string} path - Request path
 * @returns {string|null} - Visualization type or null if not found
 */
const getVisualizationTypeFromPath = (path) => {
  // Check for specific visualization endpoints
  if (path.includes('/tri-domain-tensor')) {
    return 'triDomainTensor';
  } else if (path.includes('/harmony-index')) {
    return 'harmonyIndex';
  } else if (path.includes('/risk-control-fusion')) {
    return 'riskControlFusion';
  } else if (path.includes('/resonance-spectrogram')) {
    return 'resonanceSpectrogram';
  } else if (path.includes('/unified-compliance-security')) {
    return 'unifiedComplianceSecurity';
  }
  
  // Check for dynamic visualization type in path
  const match = path.match(/\/visualizations\/([^\/]+)/);
  if (match && match[1]) {
    return match[1];
  }
  
  return null;
};

/**
 * Record cache metrics
 * @param {string} visualizationType - Visualization type
 * @param {boolean} cacheHit - Whether the request was a cache hit
 */
const recordCacheMetrics = async (visualizationType, cacheHit) => {
  try {
    // Get current cache hit count
    const cacheHitKey = `cyber_safety:cache:${visualizationType}:hit`;
    const cacheMissKey = `cyber_safety:cache:${visualizationType}:miss`;
    
    // Increment hit or miss count
    if (cacheHit) {
      await redisClient.incr(cacheHitKey);
    } else {
      await redisClient.incr(cacheMissKey);
    }
    
    // Set TTL for keys
    await redisClient.expire(cacheHitKey, METRICS_TTL);
    await redisClient.expire(cacheMissKey, METRICS_TTL);
    
    // Calculate cache hit rate
    const hitCount = parseInt(await redisClient.get(cacheHitKey) || '0', 10);
    const missCount = parseInt(await redisClient.get(cacheMissKey) || '0', 10);
    const total = hitCount + missCount;
    
    if (total > 0) {
      const hitRate = hitCount / total;
      
      // Update cache hit rate metric
      metrics.cacheHitRate.set({
        visualizationType: visualizationType || 'unknown'
      }, hitRate);
    }
  } catch (error) {
    logger.error('Error recording cache metrics:', error);
  }
};

/**
 * Check error rate and trigger alert if necessary
 * @param {string} visualizationType - Visualization type
 */
const checkErrorRate = async (visualizationType) => {
  try {
    // Get error count and request count for the last minute
    const now = Date.now();
    const windowStart = now - alerts.highErrorRate.options.windowSize;
    
    // Get metrics from Redis
    const metrics = await getMetricsFromRedis(visualizationType, windowStart, now);
    
    // Calculate error rate
    const totalRequests = metrics.length;
    const errorRequests = metrics.filter(m => m.statusCode >= 400).length;
    
    if (totalRequests >= alerts.highErrorRate.options.minSampleSize) {
      const errorRate = errorRequests / totalRequests;
      
      // Trigger alert if error rate is above threshold
      if (errorRate >= alerts.highErrorRate.options.threshold) {
        alerts.highErrorRate.trigger({
          visualizationType,
          errorRate,
          totalRequests,
          errorRequests,
          timestamp: now
        });
      }
    }
  } catch (error) {
    logger.error('Error checking error rate:', error);
  }
};

/**
 * Check response time and trigger alert if necessary
 * @param {string} visualizationType - Visualization type
 * @param {number} responseTime - Response time in milliseconds
 */
const checkResponseTime = async (visualizationType, responseTime) => {
  try {
    // Get response times for the last 5 minutes
    const now = Date.now();
    const windowStart = now - alerts.slowResponseTime.options.windowSize;
    
    // Get metrics from Redis
    const metrics = await getMetricsFromRedis(visualizationType, windowStart, now);
    
    // Calculate average response time
    const responseTimes = metrics.map(m => m.responseTime);
    
    if (responseTimes.length >= alerts.slowResponseTime.options.minSampleSize) {
      const avgResponseTime = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
      
      // Trigger alert if average response time is above threshold
      if (avgResponseTime >= alerts.slowResponseTime.options.threshold) {
        alerts.slowResponseTime.trigger({
          visualizationType,
          avgResponseTime,
          currentResponseTime: responseTime,
          sampleSize: responseTimes.length,
          timestamp: now
        });
      }
    }
  } catch (error) {
    logger.error('Error checking response time:', error);
  }
};

/**
 * Store metrics in Redis for historical analysis
 * @param {string} visualizationType - Visualization type
 * @param {Object} metrics - Metrics to store
 */
const storeMetricsInRedis = async (visualizationType, metrics) => {
  try {
    // Create key for metrics
    const key = `cyber_safety:metrics:${visualizationType}:${metrics.timestamp}`;
    
    // Store metrics in Redis
    await redisClient.set(key, JSON.stringify(metrics));
    
    // Set TTL for metrics
    await redisClient.expire(key, METRICS_TTL);
  } catch (error) {
    logger.error('Error storing metrics in Redis:', error);
  }
};

/**
 * Get metrics from Redis for a specific time range
 * @param {string} visualizationType - Visualization type
 * @param {number} startTime - Start time in milliseconds
 * @param {number} endTime - End time in milliseconds
 * @returns {Array} - Array of metrics
 */
const getMetricsFromRedis = async (visualizationType, startTime, endTime) => {
  try {
    // Get all keys for the visualization type
    const keys = await redisClient.keys(`cyber_safety:metrics:${visualizationType}:*`);
    
    // Filter keys by timestamp
    const filteredKeys = keys.filter(key => {
      const timestamp = parseInt(key.split(':').pop(), 10);
      return timestamp >= startTime && timestamp <= endTime;
    });
    
    // Get metrics for filtered keys
    const metrics = [];
    
    for (const key of filteredKeys) {
      const data = await redisClient.get(key);
      if (data) {
        metrics.push(JSON.parse(data));
      }
    }
    
    return metrics;
  } catch (error) {
    logger.error('Error getting metrics from Redis:', error);
    return [];
  }
};

/**
 * Update rate limiting metrics
 * @param {string} ip - Client IP address
 * @param {string} visualizationType - Visualization type
 */
const updateRateLimitMetrics = async (ip, visualizationType) => {
  try {
    // Create key for rate limiting
    const key = `cyber_safety:rate_limit:${ip}:${visualizationType}`;
    
    // Increment request count
    await redisClient.incr(key);
    
    // Set TTL for key
    await redisClient.expire(key, RATE_LIMIT_WINDOW);
    
    // Get current request count
    const count = parseInt(await redisClient.get(key) || '0', 10);
    
    // Log if rate limit is approaching
    if (count > RATE_LIMIT_MAX * 0.8) {
      logger.warn(`Rate limit approaching for ${ip} on ${visualizationType}: ${count}/${RATE_LIMIT_MAX}`);
    }
  } catch (error) {
    logger.error('Error updating rate limit metrics:', error);
  }
};

module.exports = {
  monitorApiEndpoint,
  metrics,
  alerts
};
