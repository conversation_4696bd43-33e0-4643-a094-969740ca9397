/**
 * NovaCore Connector Service
 * 
 * This service provides functionality for managing API connectors.
 */

const { Connector } = require('../models');
const logger = require('../../config/logger');
const { ValidationError, NotFoundError } = require('../utils/errors');
const axios = require('axios');

class ConnectorService {
  /**
   * Create a new connector
   * @param {Object} data - Connector data
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Created connector
   */
  async createConnector(data, userId) {
    try {
      logger.info('Creating new connector', { name: data.name });
      
      // Check if connector with same name and version exists
      const existingConnector = await Connector.findOne({
        name: data.name,
        version: data.version
      });
      
      if (existingConnector) {
        throw new ValidationError(`Connector with name ${data.name} and version ${data.version} already exists`);
      }
      
      // Set created by
      data.createdBy = userId;
      data.updatedBy = userId;
      
      // Create connector
      const connector = new Connector(data);
      await connector.save();
      
      logger.info('Connector created successfully', { id: connector._id });
      
      return connector;
    } catch (error) {
      logger.error('Error creating connector', { error });
      throw error;
    }
  }
  
  /**
   * Get all connectors with optional filtering
   * @param {Object} filter - Filter criteria
   * @param {Object} options - Query options (pagination, sorting)
   * @returns {Promise<Object>} - Connectors with pagination info
   */
  async getAllConnectors(filter = {}, options = {}) {
    try {
      const { page = 1, limit = 10, sort = { createdAt: -1 } } = options;
      
      // Build query
      const query = {};
      
      // Apply filters
      if (filter.name) {
        query.name = { $regex: filter.name, $options: 'i' };
      }
      
      if (filter.category) {
        query.category = filter.category;
      }
      
      if (filter.status) {
        query.status = filter.status;
      }
      
      if (filter.tags) {
        query.tags = { $all: Array.isArray(filter.tags) ? filter.tags : [filter.tags] };
      }
      
      if (filter.isPublic !== undefined) {
        query.isPublic = filter.isPublic;
      }
      
      // Execute query with pagination
      const skip = (page - 1) * limit;
      
      const [connectors, total] = await Promise.all([
        Connector.find(query)
          .sort(sort)
          .skip(skip)
          .limit(limit),
        Connector.countDocuments(query)
      ]);
      
      // Calculate pagination info
      const totalPages = Math.ceil(total / limit);
      const hasNext = page < totalPages;
      const hasPrev = page > 1;
      
      return {
        data: connectors,
        pagination: {
          total,
          page,
          limit,
          totalPages,
          hasNext,
          hasPrev
        }
      };
    } catch (error) {
      logger.error('Error getting connectors', { error });
      throw error;
    }
  }
  
  /**
   * Get connector by ID
   * @param {string} id - Connector ID
   * @returns {Promise<Object>} - Connector
   */
  async getConnectorById(id) {
    try {
      const connector = await Connector.findById(id);
      
      if (!connector) {
        throw new NotFoundError(`Connector with ID ${id} not found`);
      }
      
      return connector;
    } catch (error) {
      logger.error('Error getting connector by ID', { id, error });
      throw error;
    }
  }
  
  /**
   * Update connector by ID
   * @param {string} id - Connector ID
   * @param {Object} data - Updated connector data
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Updated connector
   */
  async updateConnector(id, data, userId) {
    try {
      // Get existing connector
      const connector = await this.getConnectorById(id);
      
      // Check if updating name and version would create a duplicate
      if (data.name && data.version && 
          (data.name !== connector.name || data.version !== connector.version)) {
        const existingConnector = await Connector.findOne({
          name: data.name,
          version: data.version,
          _id: { $ne: id }
        });
        
        if (existingConnector) {
          throw new ValidationError(`Connector with name ${data.name} and version ${data.version} already exists`);
        }
      }
      
      // Set updated by
      data.updatedBy = userId;
      
      // Update connector
      Object.assign(connector, data);
      await connector.save();
      
      logger.info('Connector updated successfully', { id });
      
      return connector;
    } catch (error) {
      logger.error('Error updating connector', { id, error });
      throw error;
    }
  }
  
  /**
   * Delete connector by ID
   * @param {string} id - Connector ID
   * @returns {Promise<boolean>} - Deletion success
   */
  async deleteConnector(id) {
    try {
      const result = await Connector.findByIdAndDelete(id);
      
      if (!result) {
        throw new NotFoundError(`Connector with ID ${id} not found`);
      }
      
      logger.info('Connector deleted successfully', { id });
      
      return true;
    } catch (error) {
      logger.error('Error deleting connector', { id, error });
      throw error;
    }
  }
  
  /**
   * Test connector connection
   * @param {string} id - Connector ID
   * @param {Object} credentials - Authentication credentials
   * @returns {Promise<Object>} - Test result
   */
  async testConnection(id, credentials) {
    try {
      logger.info('Testing connector connection', { id });
      
      // Get connector
      const connector = await this.getConnectorById(id);
      
      // Check if connector has test connection endpoint
      if (!connector.authentication.testConnection || !connector.authentication.testConnection.endpoint) {
        throw new ValidationError('Connector does not have a test connection endpoint');
      }
      
      // Build request URL
      const endpoint = connector.authentication.testConnection.endpoint;
      const url = endpoint.startsWith('http') ? endpoint : `${connector.baseUrl}${endpoint}`;
      
      // Build request headers
      const headers = {};
      
      // Add authentication
      switch (connector.authentication.type) {
        case 'API_KEY':
          if (credentials.apiKey) {
            headers['X-API-Key'] = credentials.apiKey;
          } else if (credentials.headerName && credentials.headerValue) {
            headers[credentials.headerName] = credentials.headerValue;
          }
          break;
        case 'BASIC':
          if (credentials.username && credentials.password) {
            const auth = Buffer.from(`${credentials.username}:${credentials.password}`).toString('base64');
            headers['Authorization'] = `Basic ${auth}`;
          }
          break;
        case 'OAUTH2':
          if (credentials.accessToken) {
            headers['Authorization'] = `Bearer ${credentials.accessToken}`;
          }
          break;
        // Add other authentication types as needed
      }
      
      // Execute request
      const method = connector.authentication.testConnection.method || 'GET';
      const expectedStatus = connector.authentication.testConnection.expectedResponse?.status || 200;
      
      const response = await axios({
        method,
        url,
        headers,
        timeout: 10000 // 10 seconds timeout
      });
      
      // Check response status
      const success = response.status === expectedStatus;
      
      return {
        success,
        status: response.status,
        message: success ? 'Connection successful' : `Expected status ${expectedStatus}, got ${response.status}`,
        data: response.data
      };
    } catch (error) {
      logger.error('Error testing connector connection', { id, error });
      
      return {
        success: false,
        status: error.response?.status || 500,
        message: error.message,
        error: error.response?.data || error.message
      };
    }
  }
  
  /**
   * Execute connector endpoint
   * @param {string} id - Connector ID
   * @param {string} endpointId - Endpoint ID
   * @param {Object} credentials - Authentication credentials
   * @param {Object} parameters - Endpoint parameters
   * @returns {Promise<Object>} - Execution result
   */
  async executeEndpoint(id, endpointId, credentials, parameters = {}) {
    try {
      logger.info('Executing connector endpoint', { id, endpointId });
      
      // Get connector
      const connector = await this.getConnectorById(id);
      
      // Find endpoint
      const endpoint = connector.getEndpoint(endpointId);
      
      if (!endpoint) {
        throw new NotFoundError(`Endpoint with ID ${endpointId} not found in connector ${id}`);
      }
      
      // Build request URL
      let url = endpoint.path.startsWith('http') ? endpoint.path : `${connector.baseUrl}${endpoint.path}`;
      
      // Replace path parameters
      if (parameters) {
        Object.keys(parameters).forEach(key => {
          url = url.replace(`{${key}}`, encodeURIComponent(parameters[key]));
        });
      }
      
      // Build request headers
      const headers = { ...endpoint.headers };
      
      // Add authentication
      switch (connector.authentication.type) {
        case 'API_KEY':
          if (credentials.apiKey) {
            headers['X-API-Key'] = credentials.apiKey;
          } else if (credentials.headerName && credentials.headerValue) {
            headers[credentials.headerName] = credentials.headerValue;
          }
          break;
        case 'BASIC':
          if (credentials.username && credentials.password) {
            const auth = Buffer.from(`${credentials.username}:${credentials.password}`).toString('base64');
            headers['Authorization'] = `Basic ${auth}`;
          }
          break;
        case 'OAUTH2':
          if (credentials.accessToken) {
            headers['Authorization'] = `Bearer ${credentials.accessToken}`;
          }
          break;
        // Add other authentication types as needed
      }
      
      // Build query parameters
      const queryParams = {};
      
      if (endpoint.parameters) {
        endpoint.parameters.forEach(param => {
          if (param.in === 'query' && parameters[param.name] !== undefined) {
            queryParams[param.name] = parameters[param.name];
          }
        });
      }
      
      // Build request body
      let data = null;
      
      if (['POST', 'PUT', 'PATCH'].includes(endpoint.method)) {
        if (endpoint.body) {
          data = { ...endpoint.body };
          
          // Replace body parameters
          if (parameters) {
            Object.keys(parameters).forEach(key => {
              if (typeof data === 'object') {
                // Replace placeholders in object
                JSON.stringify(data).replace(`{${key}}`, parameters[key]);
              }
            });
          }
        } else {
          // Use parameters as body
          data = { ...parameters };
        }
      }
      
      // Execute request
      const response = await axios({
        method: endpoint.method,
        url,
        headers,
        params: queryParams,
        data,
        timeout: 30000 // 30 seconds timeout
      });
      
      return {
        success: true,
        status: response.status,
        data: response.data,
        headers: response.headers
      };
    } catch (error) {
      logger.error('Error executing connector endpoint', { id, endpointId, error });
      
      return {
        success: false,
        status: error.response?.status || 500,
        message: error.message,
        error: error.response?.data || error.message
      };
    }
  }
  
  /**
   * Find connectors by category
   * @param {string} category - Category name
   * @returns {Promise<Array>} - Connectors
   */
  async findByCategory(category) {
    try {
      return await Connector.findByCategory(category);
    } catch (error) {
      logger.error('Error finding connectors by category', { category, error });
      throw error;
    }
  }
  
  /**
   * Find connectors by tags
   * @param {Array<string>} tags - Tags to search for
   * @returns {Promise<Array>} - Connectors
   */
  async findByTags(tags) {
    try {
      return await Connector.findByTags(tags);
    } catch (error) {
      logger.error('Error finding connectors by tags', { tags, error });
      throw error;
    }
  }
  
  /**
   * Find active connectors
   * @returns {Promise<Array>} - Active connectors
   */
  async findActive() {
    try {
      return await Connector.findActive();
    } catch (error) {
      logger.error('Error finding active connectors', { error });
      throw error;
    }
  }
}

module.exports = new ConnectorService();
