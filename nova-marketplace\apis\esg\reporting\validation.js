/**
 * ESG Reporting API - Validation Schemas
 * 
 * This file defines the validation schemas for the ESG Reporting API.
 */

const Joi = require('joi');

// Common validation schemas
const idSchema = Joi.string().regex(/^[0-9a-fA-F]{24}$/);
const dateSchema = Joi.date().iso();

// Report Template validation schemas
const reportTemplateSchema = {
  create: Joi.object({
    name: Joi.string().required(),
    description: Joi.string().required(),
    framework: Joi.string().valid('GRI', 'SASB', 'TCFD', 'CDP', 'SDG', 'Custom').required(),
    sections: Joi.array().items(Joi.object({
      title: Joi.string().required(),
      description: Joi.string(),
      metrics: Joi.array().items(Joi.object({
        metric: idSchema.required(),
        required: Joi.boolean().default(true),
        displayOrder: Joi.number()
      }))
    })).required(),
    status: Joi.string().valid('Draft', 'Active', 'Archived').default('Draft')
  }),
  update: Joi.object({
    name: Joi.string(),
    description: Joi.string(),
    framework: Joi.string().valid('GRI', 'SASB', 'TCFD', 'CDP', 'SDG', 'Custom'),
    sections: Joi.array().items(Joi.object({
      title: Joi.string().required(),
      description: Joi.string(),
      metrics: Joi.array().items(Joi.object({
        metric: idSchema.required(),
        required: Joi.boolean(),
        displayOrder: Joi.number()
      }))
    })),
    status: Joi.string().valid('Draft', 'Active', 'Archived')
  })
};

// Report validation schemas
const reportSchema = {
  create: Joi.object({
    name: Joi.string().required(),
    description: Joi.string().required(),
    template: idSchema.required(),
    reportingPeriod: Joi.object({
      startDate: dateSchema.required(),
      endDate: dateSchema.required()
    }).required(),
    status: Joi.string().valid('Draft', 'In Progress', 'Completed', 'Published', 'Archived').default('Draft')
  }),
  update: Joi.object({
    name: Joi.string(),
    description: Joi.string(),
    reportingPeriod: Joi.object({
      startDate: dateSchema,
      endDate: dateSchema
    }),
    status: Joi.string().valid('Draft', 'In Progress', 'Completed', 'Published', 'Archived'),
    sections: Joi.array().items(Joi.object({
      title: Joi.string().required(),
      description: Joi.string(),
      content: Joi.string(),
      metrics: Joi.array().items(Joi.object({
        metric: idSchema.required(),
        value: Joi.any(),
        notes: Joi.string()
      }))
    }))
  }),
  submitForApproval: Joi.object({
    approvers: Joi.array().items(Joi.string()).min(1).required()
  }),
  approveOrReject: Joi.object({
    status: Joi.string().valid('Approved', 'Rejected').required(),
    comments: Joi.string()
  })
};

// Query validation schemas
const querySchema = {
  pagination: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(10),
    sort: Joi.string().default('-createdAt')
  }),
  reportTemplateFilters: Joi.object({
    framework: Joi.string().valid('GRI', 'SASB', 'TCFD', 'CDP', 'SDG', 'Custom'),
    status: Joi.string().valid('Draft', 'Active', 'Archived')
  }),
  reportFilters: Joi.object({
    status: Joi.string().valid('Draft', 'In Progress', 'Completed', 'Published', 'Archived'),
    template: idSchema
  })
};

module.exports = {
  reportTemplate: reportTemplateSchema,
  report: reportSchema,
  query: querySchema
};
