/**
 * VisualizationExportService
 * 
 * This service provides methods for exporting visualizations as images or videos.
 */

class VisualizationExportService {
  /**
   * Export visualization as image
   * @param {HTMLCanvasElement} canvas - Canvas element to export
   * @param {Object} options - Export options
   * @returns {Promise<string>} - Promise that resolves with the data URL
   */
  async exportAsImage(canvas, options = {}) {
    const {
      format = 'png',
      quality = 0.9,
      fileName = `visualization-${Date.now()}`
    } = options;
    
    return new Promise((resolve, reject) => {
      try {
        // Get data URL
        const mimeType = format === 'jpg' ? 'image/jpeg' : 'image/png';
        const dataUrl = canvas.toDataURL(mimeType, quality);
        
        // Create download link
        const link = document.createElement('a');
        link.href = dataUrl;
        link.download = `${fileName}.${format}`;
        
        // Trigger download
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        resolve(dataUrl);
      } catch (error) {
        console.error('Error exporting visualization as image:', error);
        reject(error);
      }
    });
  }
  
  /**
   * Export visualization as video
   * @param {HTMLCanvasElement} canvas - Canvas element to export
   * @param {Object} options - Export options
   * @returns {Promise<string>} - Promise that resolves with the data URL
   */
  async exportAsVideo(canvas, options = {}) {
    const {
      duration = 5,
      fps = 30,
      fileName = `visualization-${Date.now()}`,
      rotationSpeed = 1
    } = options;
    
    return new Promise((resolve, reject) => {
      try {
        // Check if MediaRecorder is available
        if (!window.MediaRecorder) {
          throw new Error('MediaRecorder not supported in this browser');
        }
        
        // Create media stream from canvas
        const stream = canvas.captureStream(fps);
        
        // Create media recorder
        const recorder = new MediaRecorder(stream, {
          mimeType: 'video/webm;codecs=vp9',
          videoBitsPerSecond: 5000000
        });
        
        // Store recorded chunks
        const chunks = [];
        recorder.ondataavailable = (event) => {
          if (event.data.size > 0) {
            chunks.push(event.data);
          }
        };
        
        // Handle recording stop
        recorder.onstop = () => {
          // Create blob from chunks
          const blob = new Blob(chunks, { type: 'video/webm' });
          
          // Create data URL
          const dataUrl = URL.createObjectURL(blob);
          
          // Create download link
          const link = document.createElement('a');
          link.href = dataUrl;
          link.download = `${fileName}.webm`;
          
          // Trigger download
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          
          resolve(dataUrl);
        };
        
        // Start recording
        recorder.start();
        
        // Stop recording after duration
        setTimeout(() => {
          recorder.stop();
        }, duration * 1000);
      } catch (error) {
        console.error('Error exporting visualization as video:', error);
        reject(error);
      }
    });
  }
  
  /**
   * Capture screenshot of visualization
   * @param {HTMLCanvasElement} canvas - Canvas element to capture
   * @returns {string} - Data URL of the screenshot
   */
  captureScreenshot(canvas) {
    return canvas.toDataURL('image/png');
  }
  
  /**
   * Create animated GIF from visualization
   * @param {HTMLCanvasElement} canvas - Canvas element to capture
   * @param {Object} options - GIF options
   * @returns {Promise<string>} - Promise that resolves with the data URL
   */
  async createAnimatedGif(canvas, options = {}) {
    const {
      duration = 3,
      fps = 10,
      fileName = `visualization-${Date.now()}`,
      quality = 10,
      width = 320,
      height = 240
    } = options;
    
    return new Promise((resolve, reject) => {
      try {
        // Check if gif.js is available
        if (!window.GIF) {
          // Load gif.js dynamically
          const script = document.createElement('script');
          script.src = 'https://cdnjs.cloudflare.com/ajax/libs/gif.js/0.2.0/gif.js';
          document.body.appendChild(script);
          
          script.onload = () => {
            this._createGif(canvas, { duration, fps, fileName, quality, width, height })
              .then(resolve)
              .catch(reject);
          };
          
          script.onerror = () => {
            reject(new Error('Failed to load gif.js library'));
          };
          
          return;
        }
        
        this._createGif(canvas, { duration, fps, fileName, quality, width, height })
          .then(resolve)
          .catch(reject);
      } catch (error) {
        console.error('Error creating animated GIF:', error);
        reject(error);
      }
    });
  }
  
  /**
   * Internal method to create GIF
   * @private
   */
  async _createGif(canvas, options) {
    const { duration, fps, fileName, quality, width, height } = options;
    
    return new Promise((resolve, reject) => {
      // Create GIF
      const gif = new window.GIF({
        workers: 2,
        quality,
        width,
        height,
        workerScript: 'https://cdnjs.cloudflare.com/ajax/libs/gif.js/0.2.0/gif.worker.js'
      });
      
      // Calculate total frames
      const totalFrames = duration * fps;
      let framesAdded = 0;
      
      // Add frames
      const addFrame = () => {
        if (framesAdded >= totalFrames) {
          // Render GIF
          gif.render();
          return;
        }
        
        // Add frame
        gif.addFrame(canvas, { copy: true, delay: 1000 / fps });
        framesAdded++;
        
        // Add next frame
        setTimeout(addFrame, 1000 / fps);
      };
      
      // Start adding frames
      addFrame();
      
      // Handle GIF creation
      gif.on('finished', (blob) => {
        // Create data URL
        const dataUrl = URL.createObjectURL(blob);
        
        // Create download link
        const link = document.createElement('a');
        link.href = dataUrl;
        link.download = `${fileName}.gif`;
        
        // Trigger download
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        resolve(dataUrl);
      });
      
      // Handle errors
      gif.on('error', (error) => {
        reject(error);
      });
    });
  }
}

// Create singleton instance
const visualizationExportService = new VisualizationExportService();

export default visualizationExportService;
