/**
 * CSDE Performance Monitoring Routes
 * 
 * This module defines routes for the CSDE performance monitoring service.
 */

const express = require('express');
const createPerformanceMonitoringController = require('./performance-monitoring-controller');

/**
 * Create CSDE performance monitoring routes
 * @param {Object} options - Routes options
 * @param {Object} options.performanceMonitor - Performance monitoring service
 * @param {Object} options.logger - Logger instance
 * @returns {Object} Express router
 */
function createPerformanceMonitoringRoutes(options = {}) {
  const router = express.Router();
  const controller = createPerformanceMonitoringController(options);
  
  /**
   * @api {get} /metrics Get performance metrics
   * @apiName GetMetrics
   * @apiGroup Performance
   * @apiVersion 1.0.0
   * 
   * @apiSuccess {Boolean} success Success status
   * @apiSuccess {Object} metrics Performance metrics
   */
  router.get('/metrics', controller.getMetrics);
  
  /**
   * @api {get} /visualization Get visualization data
   * @apiName GetVisualizationData
   * @apiGroup Performance
   * @apiVersion 1.0.0
   * 
   * @apiSuccess {Boolean} success Success status
   * @apiSuccess {Object} data Visualization data
   */
  router.get('/visualization', controller.getVisualizationData);
  
  /**
   * @api {post} /metrics/reset Reset metrics
   * @apiName ResetMetrics
   * @apiGroup Performance
   * @apiVersion 1.0.0
   * 
   * @apiSuccess {Boolean} success Success status
   * @apiSuccess {String} message Success message
   */
  router.post('/metrics/reset', controller.resetMetrics);
  
  /**
   * @api {get} /alerts Get alerts
   * @apiName GetAlerts
   * @apiGroup Performance
   * @apiVersion 1.0.0
   * 
   * @apiSuccess {Boolean} success Success status
   * @apiSuccess {Array} alerts Alerts
   */
  router.get('/alerts', controller.getAlerts);
  
  /**
   * @api {get} /health Get health status
   * @apiName GetHealth
   * @apiGroup Performance
   * @apiVersion 1.0.0
   * 
   * @apiSuccess {Boolean} success Success status
   * @apiSuccess {Object} health Health status
   */
  router.get('/health', controller.getHealth);
  
  return router;
}

module.exports = createPerformanceMonitoringRoutes;
