/**
 * NovaCore NovaPulse Module
 * 
 * This is the main entry point for the NovaPulse module.
 * NovaPulse is the Universal Regulatory Compliance Monitoring System (URCMS) component
 * of the NovaFuse Cyber-Safety Platform.
 */

const routes = require('./routes');
const controllers = require('./controllers');
const services = require('./services');
const models = require('./models');

module.exports = {
  routes,
  controllers,
  services,
  models,
  
  // Export individual components for direct access
  RegulationController: controllers.RegulationController,
  RegulatoryChangeController: controllers.RegulatoryChangeController,
  FrameworkController: controllers.FrameworkController,
  ComplianceProfileController: controllers.ComplianceProfileController,
  
  RegulationService: services.RegulationService,
  RegulatoryChangeService: services.RegulatoryChangeService,
  FrameworkService: services.FrameworkService,
  ComplianceProfileService: services.ComplianceProfileService,
  
  Regulation: models.Regulation,
  RegulatoryChange: models.RegulatoryChange,
  Framework: models.Framework,
  ComplianceProfile: models.ComplianceProfile
};
