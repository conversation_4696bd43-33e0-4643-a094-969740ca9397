/**
 * Performance Benchmarking Script for NovaConnect Universal API Connector
 * 
 * This script runs performance benchmarks against the NovaConnect API.
 */

const axios = require('axios');
const PerformanceBenchmark = require('./benchmark');
const { encrypt, decrypt } = require('../utils/encryption');
const crypto = require('crypto');

// Create benchmark instance
const benchmark = new PerformanceBenchmark({
  thresholds: {
    authHandshake: 500, // ms
    transformation: 200, // ms
    encryption: 50, // ms
    decryption: 50, // ms
    apiRequest: 1000, // ms
    connectorExecution: 2000 // ms
  }
});

// Configuration
const BASE_URL = 'http://localhost:3000'; // Auth service
const REGISTRY_URL = 'http://localhost:3001'; // Connector registry
const EXECUTOR_URL = 'http://localhost:3002'; // Connector executor

// Test data
const testData = {
  connector: {
    name: 'Benchmark Connector',
    version: '1.0.0',
    category: 'Benchmark',
    description: 'Connector for performance benchmarking',
    author: 'NovaGRC',
    tags: ['benchmark', 'performance'],
    authentication: {
      type: 'API_KEY',
      fields: {
        apiKey: {
          type: 'string',
          description: 'API Key',
          required: true
        }
      }
    },
    configuration: {
      baseUrl: 'http://localhost:3005',
      headers: {
        'Content-Type': 'application/json'
      }
    },
    endpoints: [
      {
        id: 'getResource',
        name: 'Get Resource',
        path: '/resource',
        method: 'GET'
      }
    ],
    mappings: [
      {
        sourceEndpoint: 'getResource',
        targetEntity: 'Resource',
        transformations: [
          {
            source: '$.id',
            target: 'resourceId'
          },
          {
            source: '$.name',
            target: 'resourceName'
          }
        ]
      }
    ]
  },
  credential: {
    name: 'Benchmark Credential',
    authType: 'API_KEY',
    credentials: {
      apiKey: 'benchmark-api-key'
    },
    userId: 'benchmark-user'
  },
  transformation: {
    name: 'benchmarkTransform',
    code: `
      function transform(value) {
        // Simulate some complex transformation
        let result = value;
        for (let i = 0; i < 1000; i++) {
          result = JSON.stringify(result);
        }
        return JSON.parse(result);
      }
    `,
    description: 'Benchmark transformation'
  }
};

/**
 * Run all benchmarks
 */
async function runBenchmarks() {
  console.log('Starting NovaConnect Performance Benchmarks...');
  
  try {
    // Benchmark 1: API Request
    await benchmark.measure('apiRequest', async () => {
      return await axios.get(`${REGISTRY_URL}/health`);
    });
    
    // Benchmark 2: Create Connector
    const { result: connector } = await benchmark.measure('createConnector', async () => {
      const response = await axios.post(`${REGISTRY_URL}/connectors`, testData.connector);
      testData.connectorId = response.data.id;
      return response.data;
    });
    
    // Benchmark 3: Create Credential
    const { result: credential } = await benchmark.measure('createCredential', async () => {
      const credential = {
        ...testData.credential,
        connectorId: testData.connectorId
      };
      const response = await axios.post(`${BASE_URL}/credentials`, credential);
      testData.credentialId = response.data.id;
      return response.data;
    });
    
    // Benchmark 4: Create Transformation
    await benchmark.measure('createTransformation', async () => {
      const response = await axios.post(`${REGISTRY_URL}/transformations`, testData.transformation);
      return response.data;
    });
    
    // Benchmark 5: Encryption
    await benchmark.measure('encryption', async () => {
      const data = {
        apiKey: 'test-api-key',
        secret: 'very-secret-value',
        username: 'testuser',
        password: 'p@ssw0rd!'
      };
      
      const key = crypto.randomBytes(32);
      
      // Run encryption 100 times to get a good average
      for (let i = 0; i < 100; i++) {
        encrypt(data, key);
      }
      
      return true;
    });
    
    // Benchmark 6: Decryption
    await benchmark.measure('decryption', async () => {
      const data = {
        apiKey: 'test-api-key',
        secret: 'very-secret-value',
        username: 'testuser',
        password: 'p@ssw0rd!'
      };
      
      const key = crypto.randomBytes(32);
      const encryptedData = encrypt(data, key);
      
      // Run decryption 100 times to get a good average
      for (let i = 0; i < 100; i++) {
        decrypt(encryptedData, key, { parseJson: true });
      }
      
      return true;
    });
    
    // Benchmark 7: Transformation
    await benchmark.measure('transformation', async () => {
      // Simulate a transformation
      const data = { id: 'resource-1', name: 'Test Resource' };
      
      // Run transformation 100 times to get a good average
      for (let i = 0; i < 100; i++) {
        const transformed = {
          resourceId: data.id,
          resourceName: data.name
        };
      }
      
      return true;
    });
    
    // Save benchmark results
    const reportPath = benchmark.saveHistory();
    
    // Generate HTML report
    const htmlReportPath = benchmark.generateHtmlReport();
    
    console.log(`\nBenchmarks complete. Report saved to: ${reportPath}`);
    console.log(`HTML report saved to: ${htmlReportPath}`);
    
    // Print summary
    const report = benchmark.generateReport();
    
    console.log('\nSummary:');
    for (const [metricName, metricData] of Object.entries(report.metrics)) {
      const status = metricData.exceedsThreshold ? '❌' : '✅';
      console.log(`${status} ${metricName}: ${metricData.duration.toFixed(2)} ms (Threshold: ${metricData.threshold || 'None'})`);
    }
    
    if (report.thresholdViolations.length > 0) {
      console.log(`\n❌ ${report.thresholdViolations.length} threshold violations detected.`);
    } else {
      console.log('\n✅ All benchmarks passed thresholds.');
    }
  } catch (error) {
    console.error('Error running benchmarks:', error.message);
  }
}

// Run the benchmarks
runBenchmarks();
