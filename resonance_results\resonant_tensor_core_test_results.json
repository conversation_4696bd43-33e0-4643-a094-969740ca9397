{"validatorResults": {"validator": {"options": {"strictMode": false, "logValidation": true, "resonanceLock": true}}, "testValues": [3, 6, 9, 12, 13, 0.3, 0.6, 0.9, 0.03, 0.06, 0.09, 0.12, 0.13, 0.7, 0.8, 0.4, 0.07], "nonResonantValues": [0.7, 0.8, 0.4, 0.07, 0.25, 0.55, 0.85], "valuesToValidate": [0.3, 0.7, 0.9, 0.07], "testTensor": {"dimensions": [3], "values": [0.3, 0.7, 0.9, 0.07]}, "tensorValidationResult": {"isValid": true, "originalTensor": {"dimensions": [3], "values": [0.3, 0.7, 0.9, 0.07]}, "harmonizedTensor": {"dimensions": [3], "values": [0.3, 0.699, 0.9, 0.069]}, "isResonant": false, "wasHarmonized": true, "validationResults": [{"isValid": true, "originalValue": 0.3, "harmonizedValue": 0.3, "isResonant": true, "type": "generic"}, {"isValid": true, "originalValue": 0.7, "harmonizedValue": 0.699, "isResonant": false, "wasHarmonized": true, "type": "generic", "resonanceDrift": 0.0010000000000000009}, {"isValid": true, "originalValue": 0.9, "harmonizedValue": 0.9, "isResonant": true, "type": "generic"}, {"isValid": true, "originalValue": 0.07, "harmonizedValue": 0.069, "isResonant": false, "wasHarmonized": true, "type": "generic", "resonanceDrift": 0.0010000000000000009}], "resonanceDrift": 0.0005000000000000004}}, "tensorCoreResults": {"mockTensorCore": {}, "resonantTensorCore": {"_events": {}, "_eventsCount": 0, "tensorCore": {}, "options": {"strictMode": false, "logValidation": true, "resonanceLock": true, "trackDrift": true}, "validator": {"options": {"strictMode": false, "logValidation": true, "resonanceLock": true}}, "metrics": {"validations": 9, "harmonizations": 1, "rejections": 0, "totalDrift": 0.0005000000000000004, "averageDrift": 0.0005000000000000004, "operations": {"tensorProduct": 1, "directSum": 1, "fusion": 1, "scaling": 0}}}, "testTensor": {"dimensions": [3], "values": [0.3, 0.699, 0.9, 0.069], "domain": "test", "resonance": {"isResonant": false, "wasHarmonized": true, "resonanceDrift": 0.0005000000000000004}}, "tensorA": {"dimensions": [2], "values": [0.3, 0.6], "domain": "test", "resonance": {"isResonant": true}}, "tensorB": {"dimensions": [2], "values": [0.9, 0.3], "domain": "test", "resonance": {"isResonant": true}}, "productTensor": {"dimensions": [2, 2], "values": [0.27, 0.09, 0.54, 0.18], "domain": "test", "resonance": {"isResonant": true, "operation": "tensorProduct"}}, "sumTensor": {"dimensions": [4], "values": [0.3, 0.6, 0.9, 0.3], "domain": "test", "resonance": {"isResonant": true, "operation": "directSum"}}, "csde_tensor": {"dimensions": [4], "values": [0.3, 0.6, 0.9, 0.3], "domain": "csde", "resonance": {"isResonant": true}}, "csfe_tensor": {"dimensions": [4], "values": [0.6, 0.3, 0.6, 0.9], "domain": "csfe", "resonance": {"isResonant": true}}, "csme_tensor": {"dimensions": [4], "values": [0.9, 0.3, 0.6, 0.3], "domain": "csme", "resonance": {"isResonant": true}}, "fusedTensor": {"dimensions": [8], "values": [0.18, 0.09, 0.18, 0.27, 0.36, 0.18, 0.36, 0.54, 0.54, 0.27, 0.54, 0.81, 0.18, 0.09, 0.18, 0.27, 2827.431, 942.477, 1884.954, 942.477], "domain": "csde", "resonance": {"isResonant": true, "operation": "fuseEngines"}}, "metrics": {"validations": 9, "harmonizations": 1, "rejections": 0, "totalDrift": 0.0005000000000000004, "averageDrift": 0.0005000000000000004, "operations": {"tensorProduct": 1, "directSum": 1, "fusion": 1, "scaling": 0}, "resonanceLockEnabled": true, "strictModeEnabled": false}}, "unifiedResults": {"error": {"code": "MODULE_NOT_FOUND", "requireStack": ["D:\\novafuse-api-superstore\\src\\comphyology\\unified_resonant_tensor_core.js", "D:\\novafuse-api-superstore\\test\\comphyology\\test_resonant_tensor_core.js"]}}}