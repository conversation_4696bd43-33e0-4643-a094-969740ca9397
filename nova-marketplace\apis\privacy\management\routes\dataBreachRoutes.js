/**
 * Data Breach Routes
 * 
 * This file defines the routes for data breaches.
 */

const express = require('express');
const router = express.Router();
const { dataBreachController } = require('../controllers');

// Get all data breaches
router.get('/', dataBreachController.getAllDataBreaches);

// Get a specific data breach by ID
router.get('/:id', dataBreachController.getDataBreachById);

// Create a new data breach
router.post('/', dataBreachController.createDataBreach);

// Update a data breach
router.put('/:id', dataBreachController.updateDataBreach);

// Delete a data breach
router.delete('/:id', dataBreachController.deleteDataBreach);

// Send notifications for a data breach
router.post('/:id/notify', dataBreachController.sendBreachNotifications);

// Generate a breach report
router.get('/:id/report', dataBreachController.generateBreachReport);

// Assess notification requirements for a data breach
router.get('/:id/notification-requirements', dataBreachController.assessNotificationRequirements);

module.exports = router;
