import React from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';

/**
 * Animated button component with hover and tap effects
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Button content
 * @param {string} props.className - Additional CSS classes
 * @param {string} props.href - Link URL (if button is a link)
 * @param {Function} props.onClick - Click handler (if button is not a link)
 * @param {boolean} props.disabled - Whether the button is disabled
 * @param {string} props.type - Button type (if button is not a link)
 * @param {string} props.variant - Button variant ('primary', 'secondary', 'outline', or 'ghost')
 */
const AnimatedButton = ({
  children,
  className = '',
  href,
  onClick,
  disabled = false,
  type = 'button',
  variant = 'primary'
}) => {
  // Get button classes based on variant
  const getButtonClasses = () => {
    const baseClasses = 'font-bold py-2 px-6 rounded-lg transition-colors duration-200 inline-block';

    switch (variant) {
      case 'secondary':
        return `${baseClasses} bg-gray-700 hover:bg-gray-600 text-white`;
      case 'outline':
        return `${baseClasses} border border-blue-600 text-blue-500 hover:bg-blue-900 hover:bg-opacity-20`;
      case 'ghost':
        return `${baseClasses} text-blue-500 hover:bg-blue-900 hover:bg-opacity-10`;
      case 'primary':
      default:
        return `${baseClasses} bg-blue-600 hover:bg-blue-700 text-white`;
    }
  };

  // Hover animation
  const hoverAnimation = {
    scale: 1.05,
    transition: { duration: 0.2 }
  };

  // Tap animation
  const tapAnimation = {
    scale: 0.95,
    transition: { duration: 0.1 }
  };

  // Button content with animations
  const buttonContent = (
    <motion.span
      className="inline-block w-full"
      whileHover={!disabled && hoverAnimation}
      whileTap={!disabled && tapAnimation}
    >
      {children}
    </motion.span>
  );

  // If button is a link
  if (href) {
    return (
      <Link href={href} className={`${getButtonClasses()} ${className} ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}>
        {buttonContent}
      </Link>
    );
  }

  // If button is not a link
  return (
    <button
      type={type}
      className={`${getButtonClasses()} ${className} ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
      onClick={onClick}
      disabled={disabled}
    >
      {buttonContent}
    </button>
  );
};

export default AnimatedButton;
