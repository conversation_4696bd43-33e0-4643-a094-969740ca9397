module.exports = {
  testEnvironment: 'node',
  testMatch: ['**/__tests__/**/*.js', '**/?(*.)+(spec|test).js'],
  collectCoverage: true,
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'clover', 'json-summary'],
  coverageThreshold: {
    global: {
      branches: 96,
      functions: 96,
      lines: 96,
      statements: 96
    }
  },
  verbose: true,
  testTimeout: 30000,
  setupFilesAfterEnv: ['./jest.setup.js'],
  reporters: [
    'default',
    ['jest-junit', {
      outputDirectory: './test-results',
      outputName: 'junit.xml',
    }],
    ['./custom-reporter.js', {}]
  ]
};
