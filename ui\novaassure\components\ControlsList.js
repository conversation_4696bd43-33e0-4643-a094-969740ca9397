/**
 * Controls List Component
 */

import React, { useState } from 'react';
import {
  Box,
  Button,
  Flex,
  Heading,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Badge,
  IconButton,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  Input,
  Select,
  HStack,
  Text,
  useDisclosure,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  FormControl,
  FormLabel,
  Textarea,
  useToast
} from '@chakra-ui/react';
import { 
  AddIcon, 
  EditIcon, 
  DeleteIcon, 
  DownloadIcon, 
  UploadIcon, 
  ChevronDownIcon,
  SearchIcon
} from '@chakra-ui/icons';
import useControls from '../hooks/useControls';

// Risk level badge colors
const riskLevelColors = {
  low: 'green',
  medium: 'yellow',
  high: 'orange',
  critical: 'red'
};

// Implementation status badge colors
const implementationStatusColors = {
  'not-implemented': 'red',
  'partially-implemented': 'yellow',
  'implemented': 'green'
};

export default function ControlsList() {
  const {
    controls,
    loading,
    error,
    pagination,
    fetchControls,
    createControl,
    updateControl,
    deleteControl,
    importControls,
    exportControls,
    changePage,
    changeLimit
  } = useControls();

  const toast = useToast();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [isEditing, setIsEditing] = useState(false);
  const [currentControl, setCurrentControl] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [frameworkFilter, setFrameworkFilter] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('');

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    framework: '',
    category: '',
    requirements: '',
    testProcedures: '',
    riskLevel: 'medium',
    implementationStatus: 'not-implemented'
  });

  // Handle form input change
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  // Handle form submission
  const handleSubmit = async () => {
    try {
      // Convert requirements and testProcedures from string to array
      const data = {
        ...formData,
        requirements: formData.requirements.split('\n').filter(Boolean),
        testProcedures: formData.testProcedures.split('\n').filter(Boolean)
      };

      if (isEditing && currentControl) {
        await updateControl(currentControl._id, data);
        toast({
          title: 'Control updated',
          status: 'success',
          duration: 3000,
          isClosable: true
        });
      } else {
        await createControl(data);
        toast({
          title: 'Control created',
          status: 'success',
          duration: 3000,
          isClosable: true
        });
      }

      onClose();
      resetForm();
    } catch (err) {
      toast({
        title: 'Error',
        description: err.message,
        status: 'error',
        duration: 5000,
        isClosable: true
      });
    }
  };

  // Handle delete control
  const handleDelete = async (id) => {
    if (window.confirm('Are you sure you want to delete this control?')) {
      try {
        await deleteControl(id);
        toast({
          title: 'Control deleted',
          status: 'success',
          duration: 3000,
          isClosable: true
        });
      } catch (err) {
        toast({
          title: 'Error',
          description: err.message,
          status: 'error',
          duration: 5000,
          isClosable: true
        });
      }
    }
  };

  // Handle edit control
  const handleEdit = (control) => {
    setIsEditing(true);
    setCurrentControl(control);
    setFormData({
      name: control.name,
      description: control.description,
      framework: control.framework,
      category: control.category || '',
      requirements: control.requirements ? control.requirements.join('\n') : '',
      testProcedures: control.testProcedures ? control.testProcedures.join('\n') : '',
      riskLevel: control.riskLevel,
      implementationStatus: control.implementationStatus
    });
    onOpen();
  };

  // Handle add new control
  const handleAddNew = () => {
    setIsEditing(false);
    setCurrentControl(null);
    resetForm();
    onOpen();
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      framework: '',
      category: '',
      requirements: '',
      testProcedures: '',
      riskLevel: 'medium',
      implementationStatus: 'not-implemented'
    });
  };

  // Handle import controls
  const handleImport = (e) => {
    const file = e.target.files[0];
    if (!file) return;

    const formData = new FormData();
    formData.append('file', file);

    importControls(formData)
      .then((result) => {
        toast({
          title: 'Controls imported',
          description: `Imported ${result.imported} controls with ${result.errors} errors`,
          status: 'success',
          duration: 5000,
          isClosable: true
        });
      })
      .catch((err) => {
        toast({
          title: 'Import error',
          description: err.message,
          status: 'error',
          duration: 5000,
          isClosable: true
        });
      });

    // Reset file input
    e.target.value = null;
  };

  // Handle export controls
  const handleExport = (format) => {
    exportControls({ 
      framework: frameworkFilter,
      format 
    })
      .then(() => {
        toast({
          title: 'Controls exported',
          status: 'success',
          duration: 3000,
          isClosable: true
        });
      })
      .catch((err) => {
        toast({
          title: 'Export error',
          description: err.message,
          status: 'error',
          duration: 5000,
          isClosable: true
        });
      });
  };

  // Handle search
  const handleSearch = () => {
    fetchControls({
      search: searchTerm,
      framework: frameworkFilter,
      category: categoryFilter
    });
  };

  // Handle clear filters
  const handleClearFilters = () => {
    setSearchTerm('');
    setFrameworkFilter('');
    setCategoryFilter('');
    fetchControls();
  };

  // Get unique frameworks from controls
  const frameworks = [...new Set(controls.map(control => control.framework))];

  // Get unique categories from controls
  const categories = [...new Set(controls.map(control => control.category).filter(Boolean))];

  return (
    <Box p={4}>
      <Flex justify="space-between" align="center" mb={6}>
        <Heading size="lg">Controls</Heading>
        <HStack>
          <Button leftIcon={<AddIcon />} colorScheme="blue" onClick={handleAddNew}>
            Add Control
          </Button>
          <Menu>
            <MenuButton as={Button} rightIcon={<ChevronDownIcon />}>
              Export
            </MenuButton>
            <MenuList>
              <MenuItem onClick={() => handleExport('json')}>JSON</MenuItem>
              <MenuItem onClick={() => handleExport('csv')}>CSV</MenuItem>
              <MenuItem onClick={() => handleExport('excel')}>Excel</MenuItem>
            </MenuList>
          </Menu>
          <Button
            as="label"
            htmlFor="file-upload"
            leftIcon={<UploadIcon />}
            cursor="pointer"
          >
            Import
            <input
              id="file-upload"
              type="file"
              accept=".json,.csv,.xlsx,.xls"
              style={{ display: 'none' }}
              onChange={handleImport}
            />
          </Button>
        </HStack>
      </Flex>

      {/* Filters */}
      <Flex mb={6} gap={4} flexWrap="wrap">
        <FormControl maxW="200px">
          <FormLabel>Search</FormLabel>
          <Input
            placeholder="Search controls..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </FormControl>
        <FormControl maxW="200px">
          <FormLabel>Framework</FormLabel>
          <Select
            placeholder="All frameworks"
            value={frameworkFilter}
            onChange={(e) => setFrameworkFilter(e.target.value)}
          >
            {frameworks.map(framework => (
              <option key={framework} value={framework}>{framework}</option>
            ))}
          </Select>
        </FormControl>
        <FormControl maxW="200px">
          <FormLabel>Category</FormLabel>
          <Select
            placeholder="All categories"
            value={categoryFilter}
            onChange={(e) => setCategoryFilter(e.target.value)}
          >
            {categories.map(category => (
              <option key={category} value={category}>{category}</option>
            ))}
          </Select>
        </FormControl>
        <Box alignSelf="flex-end">
          <Button leftIcon={<SearchIcon />} onClick={handleSearch} mr={2}>
            Search
          </Button>
          <Button variant="outline" onClick={handleClearFilters}>
            Clear
          </Button>
        </Box>
      </Flex>

      {/* Error message */}
      {error && (
        <Box mb={4} p={3} bg="red.100" color="red.800" borderRadius="md">
          {error}
        </Box>
      )}

      {/* Controls table */}
      <Box overflowX="auto">
        <Table variant="simple">
          <Thead>
            <Tr>
              <Th>Name</Th>
              <Th>Framework</Th>
              <Th>Category</Th>
              <Th>Risk Level</Th>
              <Th>Status</Th>
              <Th>Actions</Th>
            </Tr>
          </Thead>
          <Tbody>
            {loading ? (
              <Tr>
                <Td colSpan={6} textAlign="center">Loading...</Td>
              </Tr>
            ) : controls.length === 0 ? (
              <Tr>
                <Td colSpan={6} textAlign="center">No controls found</Td>
              </Tr>
            ) : (
              controls.map(control => (
                <Tr key={control._id}>
                  <Td>{control.name}</Td>
                  <Td>{control.framework}</Td>
                  <Td>{control.category || '-'}</Td>
                  <Td>
                    <Badge colorScheme={riskLevelColors[control.riskLevel]}>
                      {control.riskLevel}
                    </Badge>
                  </Td>
                  <Td>
                    <Badge colorScheme={implementationStatusColors[control.implementationStatus]}>
                      {control.implementationStatus.replace('-', ' ')}
                    </Badge>
                  </Td>
                  <Td>
                    <IconButton
                      icon={<EditIcon />}
                      aria-label="Edit control"
                      size="sm"
                      mr={2}
                      onClick={() => handleEdit(control)}
                    />
                    <IconButton
                      icon={<DeleteIcon />}
                      aria-label="Delete control"
                      size="sm"
                      colorScheme="red"
                      onClick={() => handleDelete(control._id)}
                    />
                  </Td>
                </Tr>
              ))
            )}
          </Tbody>
        </Table>
      </Box>

      {/* Pagination */}
      <Flex justify="space-between" align="center" mt={4}>
        <Text>
          Showing {controls.length} of {pagination.total} controls
        </Text>
        <HStack>
          <Button
            onClick={() => changePage(pagination.page - 1)}
            isDisabled={pagination.page === 1}
          >
            Previous
          </Button>
          <Text>
            Page {pagination.page} of {pagination.pages}
          </Text>
          <Button
            onClick={() => changePage(pagination.page + 1)}
            isDisabled={pagination.page === pagination.pages}
          >
            Next
          </Button>
          <Select
            value={pagination.limit}
            onChange={(e) => changeLimit(Number(e.target.value))}
            w="80px"
          >
            <option value={10}>10</option>
            <option value={25}>25</option>
            <option value={50}>50</option>
            <option value={100}>100</option>
          </Select>
        </HStack>
      </Flex>

      {/* Control form modal */}
      <Modal isOpen={isOpen} onClose={onClose} size="xl">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>
            {isEditing ? 'Edit Control' : 'Add New Control'}
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <FormControl mb={4} isRequired>
              <FormLabel>Name</FormLabel>
              <Input
                name="name"
                value={formData.name}
                onChange={handleInputChange}
              />
            </FormControl>
            <FormControl mb={4} isRequired>
              <FormLabel>Description</FormLabel>
              <Textarea
                name="description"
                value={formData.description}
                onChange={handleInputChange}
              />
            </FormControl>
            <FormControl mb={4} isRequired>
              <FormLabel>Framework</FormLabel>
              <Input
                name="framework"
                value={formData.framework}
                onChange={handleInputChange}
                placeholder="e.g., soc2, gdpr, hipaa"
              />
            </FormControl>
            <FormControl mb={4}>
              <FormLabel>Category</FormLabel>
              <Input
                name="category"
                value={formData.category}
                onChange={handleInputChange}
                placeholder="e.g., access-control, data-protection"
              />
            </FormControl>
            <FormControl mb={4}>
              <FormLabel>Requirements (one per line)</FormLabel>
              <Textarea
                name="requirements"
                value={formData.requirements}
                onChange={handleInputChange}
                placeholder="Enter each requirement on a new line"
              />
            </FormControl>
            <FormControl mb={4}>
              <FormLabel>Test Procedures (one per line)</FormLabel>
              <Textarea
                name="testProcedures"
                value={formData.testProcedures}
                onChange={handleInputChange}
                placeholder="Enter each test procedure on a new line"
              />
            </FormControl>
            <FormControl mb={4}>
              <FormLabel>Risk Level</FormLabel>
              <Select
                name="riskLevel"
                value={formData.riskLevel}
                onChange={handleInputChange}
              >
                <option value="low">Low</option>
                <option value="medium">Medium</option>
                <option value="high">High</option>
                <option value="critical">Critical</option>
              </Select>
            </FormControl>
            <FormControl mb={4}>
              <FormLabel>Implementation Status</FormLabel>
              <Select
                name="implementationStatus"
                value={formData.implementationStatus}
                onChange={handleInputChange}
              >
                <option value="not-implemented">Not Implemented</option>
                <option value="partially-implemented">Partially Implemented</option>
                <option value="implemented">Implemented</option>
              </Select>
            </FormControl>
          </ModalBody>
          <ModalFooter>
            <Button variant="ghost" mr={3} onClick={onClose}>
              Cancel
            </Button>
            <Button colorScheme="blue" onClick={handleSubmit}>
              {isEditing ? 'Update' : 'Create'}
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Box>
  );
}
