/**
 * CSDE Batch Processor
 * 
 * This module provides a batch processing system for handling large volumes
 * of security findings with the CSDE integration.
 */

const fs = require('fs');
const path = require('path');
const { performance } = require('perf_hooks');
const { EventEmitter } = require('events');
const CSEDRemediationIntegration = require('./csde-remediation');

class CSEDBatchProcessor extends EventEmitter {
  /**
   * Create a new CSDE Batch Processor
   * @param {Object} options - Processor options
   * @param {Object} options.csdeConnector - CSDE connector instance
   * @param {Object} options.logger - Logger instance
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      batchSize: options.batchSize || 10,
      concurrency: options.concurrency || 5,
      inputDir: options.inputDir || path.join(process.cwd(), 'data', 'input'),
      outputDir: options.outputDir || path.join(process.cwd(), 'data', 'output'),
      archiveDir: options.archiveDir || path.join(process.cwd(), 'data', 'archive'),
      enableRemediation: options.enableRemediation !== false,
      ...options
    };
    
    this.csdeConnector = options.csdeConnector;
    this.logger = options.logger || console;
    
    // Create remediation integration if enabled
    if (this.options.enableRemediation) {
      this.remediationIntegration = new CSEDRemediationIntegration({
        csdeConnector: this.csdeConnector,
        logger: this.logger,
        autoRemediate: this.options.autoRemediate,
        remediationThreshold: this.options.remediationThreshold,
        maxConcurrentRemediations: this.options.maxConcurrentRemediations,
        remediationServiceUrl: this.options.remediationServiceUrl,
        remediationServiceToken: this.options.remediationServiceToken,
        dryRun: this.options.dryRun
      });
    }
    
    // Initialize state
    this.isProcessing = false;
    this.processingQueue = [];
    this.activeJobs = new Map();
    this.jobHistory = [];
    
    this.logger.info('CSDE Batch Processor initialized', {
      batchSize: this.options.batchSize,
      concurrency: this.options.concurrency,
      enableRemediation: this.options.enableRemediation
    });
    
    // Create directories if they don't exist
    this.createDirectories();
  }
  
  /**
   * Create necessary directories
   */
  createDirectories() {
    this.logger.debug('Creating directories');
    
    try {
      // Create input directory
      if (!fs.existsSync(this.options.inputDir)) {
        fs.mkdirSync(this.options.inputDir, { recursive: true });
        this.logger.info(`Created input directory: ${this.options.inputDir}`);
      }
      
      // Create output directory
      if (!fs.existsSync(this.options.outputDir)) {
        fs.mkdirSync(this.options.outputDir, { recursive: true });
        this.logger.info(`Created output directory: ${this.options.outputDir}`);
      }
      
      // Create archive directory
      if (!fs.existsSync(this.options.archiveDir)) {
        fs.mkdirSync(this.options.archiveDir, { recursive: true });
        this.logger.info(`Created archive directory: ${this.options.archiveDir}`);
      }
    } catch (error) {
      this.logger.error('Error creating directories', { error: error.message });
      throw error;
    }
  }
  
  /**
   * Start batch processing
   * @param {Object} options - Processing options
   * @returns {string} - Job ID
   */
  async startProcessing(options = {}) {
    this.logger.info('Starting batch processing');
    
    try {
      // Check if already processing
      if (this.isProcessing) {
        this.logger.warn('Batch processing already in progress');
        return null;
      }
      
      // Set processing flag
      this.isProcessing = true;
      
      // Create job
      const jobId = `job-${Date.now()}`;
      const job = {
        id: jobId,
        status: 'STARTING',
        startedAt: new Date().toISOString(),
        options: {
          ...this.options,
          ...options
        },
        stats: {
          totalFiles: 0,
          totalFindings: 0,
          processedFiles: 0,
          processedFindings: 0,
          successfulFindings: 0,
          failedFindings: 0,
          remediationActions: 0,
          startTime: performance.now(),
          endTime: null,
          duration: null
        }
      };
      
      // Add job to active jobs
      this.activeJobs.set(jobId, job);
      
      // Emit job started event
      this.emit('jobStarted', job);
      
      // Process files
      this.processFiles(job);
      
      return jobId;
    } catch (error) {
      this.logger.error('Error starting batch processing', { error: error.message });
      this.isProcessing = false;
      throw error;
    }
  }
  
  /**
   * Process files in input directory
   * @param {Object} job - Job object
   */
  async processFiles(job) {
    this.logger.info('Processing files', { jobId: job.id });
    
    try {
      // Update job status
      job.status = 'PROCESSING';
      
      // Get files from input directory
      const files = fs.readdirSync(this.options.inputDir)
        .filter(file => file.endsWith('.json'))
        .map(file => path.join(this.options.inputDir, file));
      
      // Update job stats
      job.stats.totalFiles = files.length;
      
      if (files.length === 0) {
        this.logger.warn('No files found in input directory', { 
          inputDir: this.options.inputDir 
        });
        
        // Complete job
        this.completeJob(job);
        return;
      }
      
      this.logger.info(`Found ${files.length} files to process`);
      
      // Process files in parallel with concurrency limit
      const concurrency = job.options.concurrency;
      
      // Process files in chunks
      for (let i = 0; i < files.length; i += concurrency) {
        const chunk = files.slice(i, i + concurrency);
        
        // Process chunk in parallel
        await Promise.all(chunk.map(file => this.processFile(file, job)));
        
        // Check if job was cancelled
        if (job.status === 'CANCELLED') {
          this.logger.info('Job was cancelled, stopping processing', { jobId: job.id });
          break;
        }
      }
      
      // Complete job
      this.completeJob(job);
    } catch (error) {
      this.logger.error('Error processing files', { 
        jobId: job.id,
        error: error.message 
      });
      
      // Fail job
      this.failJob(job, error);
    }
  }
  
  /**
   * Process a single file
   * @param {string} filePath - File path
   * @param {Object} job - Job object
   */
  async processFile(filePath, job) {
    const fileName = path.basename(filePath);
    
    this.logger.debug(`Processing file: ${fileName}`, { jobId: job.id });
    
    try {
      // Read file
      const fileContent = fs.readFileSync(filePath, 'utf8');
      const findings = JSON.parse(fileContent);
      
      // Check if findings is an array
      const findingsArray = Array.isArray(findings) ? findings : [findings];
      
      // Update job stats
      job.stats.totalFindings += findingsArray.length;
      
      // Process findings in batches
      const batches = [];
      for (let i = 0; i < findingsArray.length; i += job.options.batchSize) {
        batches.push(findingsArray.slice(i, i + job.options.batchSize));
      }
      
      this.logger.debug(`Processing ${findingsArray.length} findings in ${batches.length} batches`, { 
        jobId: job.id,
        fileName
      });
      
      // Process batches sequentially
      const processedFindings = [];
      const failedFindings = [];
      
      for (const batch of batches) {
        try {
          // Process batch
          const results = await this.csdeConnector.processBatch(batch, {
            rules: job.options.transformationRules
          });
          
          // Process remediation actions if enabled
          if (job.options.enableRemediation && this.remediationIntegration) {
            for (let i = 0; i < results.length; i++) {
              const result = results[i];
              const originalData = batch[i];
              
              // Process remediation actions
              const remediationActions = this.remediationIntegration.processRemediationActions(
                result,
                originalData
              );
              
              // Update job stats
              job.stats.remediationActions += remediationActions.length;
            }
          }
          
          // Add to processed findings
          processedFindings.push(...results);
          
          // Update job stats
          job.stats.processedFindings += batch.length;
          job.stats.successfulFindings += batch.length;
          
          // Emit progress event
          this.emit('progress', {
            jobId: job.id,
            fileName,
            processedFindings: job.stats.processedFindings,
            totalFindings: job.stats.totalFindings,
            progress: job.stats.totalFindings > 0 ? 
              job.stats.processedFindings / job.stats.totalFindings : 0
          });
        } catch (error) {
          this.logger.error(`Error processing batch in file ${fileName}`, { 
            jobId: job.id,
            error: error.message 
          });
          
          // Add to failed findings
          failedFindings.push(...batch);
          
          // Update job stats
          job.stats.processedFindings += batch.length;
          job.stats.failedFindings += batch.length;
          
          // Emit error event
          this.emit('error', {
            jobId: job.id,
            fileName,
            error: error.message,
            batch
          });
        }
        
        // Check if job was cancelled
        if (job.status === 'CANCELLED') {
          this.logger.info('Job was cancelled, stopping processing', { 
            jobId: job.id,
            fileName
          });
          break;
        }
      }
      
      // Save processed findings
      if (processedFindings.length > 0) {
        const outputFileName = `processed-${fileName}`;
        const outputFilePath = path.join(this.options.outputDir, outputFileName);
        
        fs.writeFileSync(outputFilePath, JSON.stringify(processedFindings, null, 2));
        
        this.logger.debug(`Saved ${processedFindings.length} processed findings to ${outputFileName}`, { 
          jobId: job.id
        });
      }
      
      // Save failed findings
      if (failedFindings.length > 0) {
        const failedFileName = `failed-${fileName}`;
        const failedFilePath = path.join(this.options.outputDir, failedFileName);
        
        fs.writeFileSync(failedFilePath, JSON.stringify(failedFindings, null, 2));
        
        this.logger.debug(`Saved ${failedFindings.length} failed findings to ${failedFileName}`, { 
          jobId: job.id
        });
      }
      
      // Archive file
      const archiveFilePath = path.join(this.options.archiveDir, fileName);
      fs.renameSync(filePath, archiveFilePath);
      
      this.logger.debug(`Archived file ${fileName}`, { jobId: job.id });
      
      // Update job stats
      job.stats.processedFiles++;
      
      // Emit file processed event
      this.emit('fileProcessed', {
        jobId: job.id,
        fileName,
        processedFindings: processedFindings.length,
        failedFindings: failedFindings.length
      });
      
      return {
        fileName,
        processedFindings: processedFindings.length,
        failedFindings: failedFindings.length
      };
    } catch (error) {
      this.logger.error(`Error processing file ${fileName}`, { 
        jobId: job.id,
        error: error.message 
      });
      
      // Emit file error event
      this.emit('fileError', {
        jobId: job.id,
        fileName,
        error: error.message
      });
      
      throw error;
    }
  }
  
  /**
   * Complete a job
   * @param {Object} job - Job object
   */
  completeJob(job) {
    this.logger.info('Completing job', { jobId: job.id });
    
    // Update job status
    job.status = 'COMPLETED';
    job.completedAt = new Date().toISOString();
    
    // Update job stats
    job.stats.endTime = performance.now();
    job.stats.duration = job.stats.endTime - job.stats.startTime;
    
    // Remove from active jobs
    this.activeJobs.delete(job.id);
    
    // Add to job history
    this.jobHistory.push(job);
    
    // Reset processing flag
    this.isProcessing = false;
    
    // Emit job completed event
    this.emit('jobCompleted', job);
    
    // Process next job in queue
    this.processNextJob();
  }
  
  /**
   * Fail a job
   * @param {Object} job - Job object
   * @param {Error} error - Error object
   */
  failJob(job, error) {
    this.logger.error('Failing job', { 
      jobId: job.id,
      error: error.message 
    });
    
    // Update job status
    job.status = 'FAILED';
    job.completedAt = new Date().toISOString();
    job.error = error.message;
    
    // Update job stats
    job.stats.endTime = performance.now();
    job.stats.duration = job.stats.endTime - job.stats.startTime;
    
    // Remove from active jobs
    this.activeJobs.delete(job.id);
    
    // Add to job history
    this.jobHistory.push(job);
    
    // Reset processing flag
    this.isProcessing = false;
    
    // Emit job failed event
    this.emit('jobFailed', job);
    
    // Process next job in queue
    this.processNextJob();
  }
  
  /**
   * Cancel a job
   * @param {string} jobId - Job ID
   * @returns {boolean} - Whether the job was cancelled
   */
  cancelJob(jobId) {
    this.logger.info('Cancelling job', { jobId });
    
    // Check if job exists
    if (!this.activeJobs.has(jobId)) {
      this.logger.warn('Job not found', { jobId });
      return false;
    }
    
    // Get job
    const job = this.activeJobs.get(jobId);
    
    // Update job status
    job.status = 'CANCELLED';
    job.completedAt = new Date().toISOString();
    
    // Update job stats
    job.stats.endTime = performance.now();
    job.stats.duration = job.stats.endTime - job.stats.startTime;
    
    // Emit job cancelled event
    this.emit('jobCancelled', job);
    
    return true;
  }
  
  /**
   * Queue a job for processing
   * @param {Object} options - Processing options
   * @returns {string} - Job ID
   */
  queueJob(options = {}) {
    this.logger.info('Queuing job');
    
    // Create job
    const jobId = `job-${Date.now()}`;
    const job = {
      id: jobId,
      status: 'QUEUED',
      queuedAt: new Date().toISOString(),
      options: {
        ...this.options,
        ...options
      }
    };
    
    // Add job to queue
    this.processingQueue.push(job);
    
    this.logger.info('Job queued', { 
      jobId,
      queuePosition: this.processingQueue.length 
    });
    
    // Emit job queued event
    this.emit('jobQueued', job);
    
    // Process next job if not already processing
    if (!this.isProcessing) {
      this.processNextJob();
    }
    
    return jobId;
  }
  
  /**
   * Process next job in queue
   */
  processNextJob() {
    this.logger.debug('Processing next job in queue', { 
      queueLength: this.processingQueue.length 
    });
    
    // Check if already processing
    if (this.isProcessing) {
      this.logger.debug('Already processing, skipping');
      return;
    }
    
    // Check if queue is empty
    if (this.processingQueue.length === 0) {
      this.logger.debug('Queue is empty, nothing to process');
      return;
    }
    
    // Get next job
    const job = this.processingQueue.shift();
    
    // Start processing
    this.startProcessing(job.options);
  }
  
  /**
   * Get active jobs
   * @returns {Array} - Active jobs
   */
  getActiveJobs() {
    return Array.from(this.activeJobs.values());
  }
  
  /**
   * Get job queue
   * @returns {Array} - Job queue
   */
  getJobQueue() {
    return [...this.processingQueue];
  }
  
  /**
   * Get job history
   * @returns {Array} - Job history
   */
  getJobHistory() {
    return [...this.jobHistory];
  }
  
  /**
   * Get job by ID
   * @param {string} jobId - Job ID
   * @returns {Object} - Job object
   */
  getJob(jobId) {
    // Check active jobs
    if (this.activeJobs.has(jobId)) {
      return this.activeJobs.get(jobId);
    }
    
    // Check job history
    const historyJob = this.jobHistory.find(job => job.id === jobId);
    if (historyJob) {
      return historyJob;
    }
    
    // Check job queue
    const queuedJob = this.processingQueue.find(job => job.id === jobId);
    if (queuedJob) {
      return queuedJob;
    }
    
    return null;
  }
  
  /**
   * Clear job history
   */
  clearJobHistory() {
    this.logger.info('Clearing job history');
    this.jobHistory = [];
  }
}

module.exports = CSEDBatchProcessor;
