/**
 * Automation Manager for the Universal Compliance Tracking Optimizer.
 *
 * This module provides functionality for managing automated compliance workflows.
 */

const path = require('path');
const fs = require('fs');
const automationSchema = require('../schema/automation-schema');

/**
 * Manager for the UCTO Compliance Automation Framework.
 */
class AutomationManager {
  /**
   * Initialize the Automation Manager.
   * @param {Object} options - Options for the Automation Manager
   */
  constructor(options = {}) {
    console.log("Initializing Automation Manager");
    
    // Set the data directory
    this.dataDir = options.dataDir || path.join(process.cwd(), 'automation_data');
    
    // Create the data directory if it doesn't exist
    if (!fs.existsSync(this.dataDir)) {
      fs.mkdirSync(this.dataDir, { recursive: true });
    }
    
    // Store the automation schema
    this.schema = automationSchema;
    
    // Initialize workflows
    this.workflows = {};
    
    // Initialize triggers
    this.triggers = {};
    
    // Initialize actions
    this.actions = {};
    
    console.log(`Automation Manager initialized with data directory: ${this.dataDir}`);
  }
  
  /**
   * Get the automation schema.
   * @returns {Object} The automation schema
   */
  getSchema() {
    return this.schema;
  }
  
  /**
   * Register a workflow.
   * @param {Object} workflow - Workflow definition
   * @returns {string} Workflow ID
   */
  registerWorkflow(workflow) {
    console.log(`Registering workflow: ${workflow.name}`);
    
    // Validate the workflow
    this._validateWorkflow(workflow);
    
    // Store the workflow
    this.workflows[workflow.id] = workflow;
    
    // Save the workflow to disk
    this._saveWorkflow(workflow);
    
    return workflow.id;
  }
  
  /**
   * Get a workflow by ID.
   * @param {string} workflowId - Workflow ID
   * @returns {Object} Workflow definition
   */
  getWorkflow(workflowId) {
    return this.workflows[workflowId];
  }
  
  /**
   * Get all workflows.
   * @returns {Object} All workflows
   */
  getAllWorkflows() {
    return this.workflows;
  }
  
  /**
   * Update a workflow.
   * @param {string} workflowId - Workflow ID
   * @param {Object} updates - Updates to apply
   * @returns {Object} Updated workflow
   */
  updateWorkflow(workflowId, updates) {
    console.log(`Updating workflow: ${workflowId}`);
    
    // Get the workflow
    const workflow = this.workflows[workflowId];
    
    if (!workflow) {
      throw new Error(`Workflow not found: ${workflowId}`);
    }
    
    // Apply updates
    const updatedWorkflow = { ...workflow, ...updates };
    
    // Validate the updated workflow
    this._validateWorkflow(updatedWorkflow);
    
    // Store the updated workflow
    this.workflows[workflowId] = updatedWorkflow;
    
    // Save the workflow to disk
    this._saveWorkflow(updatedWorkflow);
    
    return updatedWorkflow;
  }
  
  /**
   * Delete a workflow.
   * @param {string} workflowId - Workflow ID
   * @returns {boolean} Success
   */
  deleteWorkflow(workflowId) {
    console.log(`Deleting workflow: ${workflowId}`);
    
    // Get the workflow
    const workflow = this.workflows[workflowId];
    
    if (!workflow) {
      throw new Error(`Workflow not found: ${workflowId}`);
    }
    
    // Delete the workflow
    delete this.workflows[workflowId];
    
    // Delete the workflow file
    const workflowPath = path.join(this.dataDir, 'workflows', `${workflowId}.json`);
    if (fs.existsSync(workflowPath)) {
      fs.unlinkSync(workflowPath);
    }
    
    return true;
  }
  
  /**
   * Execute a workflow.
   * @param {string} workflowId - Workflow ID
   * @param {Object} context - Execution context
   * @returns {Promise<Object>} Execution result
   */
  async executeWorkflow(workflowId, context = {}) {
    console.log(`Executing workflow: ${workflowId}`);
    
    // Get the workflow
    const workflow = this.workflows[workflowId];
    
    if (!workflow) {
      throw new Error(`Workflow not found: ${workflowId}`);
    }
    
    // Check if the workflow is enabled
    if (!workflow.enabled) {
      console.log(`Workflow is disabled: ${workflowId}`);
      return { success: false, message: "Workflow is disabled" };
    }
    
    // Create execution context
    const executionContext = {
      ...context,
      workflow,
      variables: { ...workflow.variables },
      results: {}
    };
    
    // Execute actions
    try {
      for (const action of workflow.actions) {
        // Check if the action has a condition
        if (action.condition) {
          // Evaluate the condition
          const conditionResult = this._evaluateCondition(action.condition, executionContext);
          
          if (!conditionResult) {
            console.log(`Skipping action due to condition: ${action.name}`);
            continue;
          }
        }
        
        // Execute the action
        const actionResult = await this._executeAction(action, executionContext);
        
        // Store the result
        executionContext.results[action.id] = actionResult;
        
        // Check if the action succeeded
        if (actionResult.success) {
          // Execute onSuccess actions if defined
          if (action.onSuccess && action.onSuccess.actions) {
            for (const successAction of action.onSuccess.actions) {
              const successActionResult = await this._executeAction(successAction, executionContext);
              executionContext.results[`${action.id}_success_${successAction.id}`] = successActionResult;
            }
          }
        } else {
          // Execute onFailure actions if defined
          if (action.onFailure && action.onFailure.actions) {
            for (const failureAction of action.onFailure.actions) {
              const failureActionResult = await this._executeAction(failureAction, executionContext);
              executionContext.results[`${action.id}_failure_${failureAction.id}`] = failureActionResult;
            }
          }
        }
      }
      
      return {
        success: true,
        workflowId,
        results: executionContext.results
      };
    } catch (error) {
      console.error(`Error executing workflow: ${workflowId}`, error);
      
      return {
        success: false,
        workflowId,
        error: error.message,
        results: executionContext.results
      };
    }
  }
  
  /**
   * Register a trigger.
   * @param {string} triggerType - Trigger type
   * @param {Function} handler - Trigger handler
   * @returns {boolean} Success
   */
  registerTrigger(triggerType, handler) {
    console.log(`Registering trigger: ${triggerType}`);
    
    // Validate the trigger type
    const triggerTypeDefinition = this.schema.triggerTypes.find(t => t.id === triggerType);
    
    if (!triggerTypeDefinition) {
      throw new Error(`Invalid trigger type: ${triggerType}`);
    }
    
    // Store the trigger handler
    this.triggers[triggerType] = handler;
    
    return true;
  }
  
  /**
   * Register an action.
   * @param {string} actionType - Action type
   * @param {Function} handler - Action handler
   * @returns {boolean} Success
   */
  registerAction(actionType, handler) {
    console.log(`Registering action: ${actionType}`);
    
    // Validate the action type
    const actionTypeDefinition = this.schema.actionTypes.find(a => a.id === actionType);
    
    if (!actionTypeDefinition) {
      throw new Error(`Invalid action type: ${actionType}`);
    }
    
    // Store the action handler
    this.actions[actionType] = handler;
    
    return true;
  }
  
  /**
   * Validate a workflow.
   * @param {Object} workflow - Workflow definition
   * @returns {boolean} Valid
   * @private
   */
  _validateWorkflow(workflow) {
    // Check required fields
    if (!workflow.id) {
      throw new Error("Workflow ID is required");
    }
    
    if (!workflow.name) {
      throw new Error("Workflow name is required");
    }
    
    if (!workflow.trigger) {
      throw new Error("Workflow trigger is required");
    }
    
    if (!workflow.trigger.type) {
      throw new Error("Workflow trigger type is required");
    }
    
    if (!workflow.actions || !Array.isArray(workflow.actions) || workflow.actions.length === 0) {
      throw new Error("Workflow must have at least one action");
    }
    
    // Validate trigger type
    const triggerTypeDefinition = this.schema.triggerTypes.find(t => t.id === workflow.trigger.type);
    
    if (!triggerTypeDefinition) {
      throw new Error(`Invalid trigger type: ${workflow.trigger.type}`);
    }
    
    // Validate actions
    for (const action of workflow.actions) {
      if (!action.id) {
        throw new Error("Action ID is required");
      }
      
      if (!action.type) {
        throw new Error("Action type is required");
      }
      
      // Validate action type
      const actionTypeDefinition = this.schema.actionTypes.find(a => a.id === action.type);
      
      if (!actionTypeDefinition) {
        throw new Error(`Invalid action type: ${action.type}`);
      }
    }
    
    return true;
  }
  
  /**
   * Save a workflow to disk.
   * @param {Object} workflow - Workflow definition
   * @returns {boolean} Success
   * @private
   */
  _saveWorkflow(workflow) {
    // Create the workflows directory if it doesn't exist
    const workflowsDir = path.join(this.dataDir, 'workflows');
    if (!fs.existsSync(workflowsDir)) {
      fs.mkdirSync(workflowsDir, { recursive: true });
    }
    
    // Save the workflow to a file
    const workflowPath = path.join(workflowsDir, `${workflow.id}.json`);
    fs.writeFileSync(workflowPath, JSON.stringify(workflow, null, 2));
    
    return true;
  }
  
  /**
   * Load workflows from disk.
   * @returns {boolean} Success
   * @private
   */
  _loadWorkflows() {
    console.log("Loading workflows from disk");
    
    // Create the workflows directory if it doesn't exist
    const workflowsDir = path.join(this.dataDir, 'workflows');
    if (!fs.existsSync(workflowsDir)) {
      fs.mkdirSync(workflowsDir, { recursive: true });
      return true;
    }
    
    // Get all workflow files
    const workflowFiles = fs.readdirSync(workflowsDir).filter(file => file.endsWith('.json'));
    
    // Load each workflow
    for (const workflowFile of workflowFiles) {
      try {
        const workflowPath = path.join(workflowsDir, workflowFile);
        const workflowData = fs.readFileSync(workflowPath, 'utf8');
        const workflow = JSON.parse(workflowData);
        
        // Store the workflow
        this.workflows[workflow.id] = workflow;
        
        console.log(`Loaded workflow from disk: ${workflow.id}`);
      } catch (error) {
        console.error(`Error loading workflow: ${workflowFile}`, error);
      }
    }
    
    return true;
  }
  
  /**
   * Evaluate a condition.
   * @param {string} condition - Condition expression
   * @param {Object} context - Execution context
   * @returns {boolean} Result
   * @private
   */
  _evaluateCondition(condition, context) {
    // For now, just return true
    // In a real implementation, this would evaluate the condition expression
    return true;
  }
  
  /**
   * Execute an action.
   * @param {Object} action - Action definition
   * @param {Object} context - Execution context
   * @returns {Promise<Object>} Execution result
   * @private
   */
  async _executeAction(action, context) {
    console.log(`Executing action: ${action.name || action.id}`);
    
    // Get the action handler
    const actionHandler = this.actions[action.type];
    
    if (!actionHandler) {
      throw new Error(`No handler registered for action type: ${action.type}`);
    }
    
    try {
      // Execute the action handler
      const result = await actionHandler(action.parameters, context);
      
      return {
        success: true,
        actionId: action.id,
        actionType: action.type,
        result
      };
    } catch (error) {
      console.error(`Error executing action: ${action.name || action.id}`, error);
      
      return {
        success: false,
        actionId: action.id,
        actionType: action.type,
        error: error.message
      };
    }
  }
}

module.exports = AutomationManager;
