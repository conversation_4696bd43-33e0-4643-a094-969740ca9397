/**
 * GraphQL Testing Page
 *
 * This page provides tools for testing GraphQL APIs.
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Container,
  Divider,
  Grid,
  Paper,
  Tab,
  Tabs,
  TextField,
  Typography
} from '@mui/material';
import DashboardLayout from '../../layouts/DashboardLayout';
import GraphQLQueryBuilder from '../../components/graphql/GraphQLQueryBuilder';
import GraphQLResponseViewer from '../../components/graphql/GraphQLResponseViewer';
import GraphQLConnectorConfig from '../../components/graphql/GraphQLConnectorConfig';
import GraphQLSubscriptionManager from '../../components/graphql/GraphQLSubscriptionManager';
import { graphqlApi } from '../../services/api';

const GraphQLPage = () => {
  const [activeTab, setActiveTab] = useState('query');
  const [endpoint, setEndpoint] = useState('');
  const [headers, setHeaders] = useState({});
  const [auth, setAuth] = useState(null);
  const [response, setResponse] = useState(null);
  const [loading, setLoading] = useState(false);
  const [connector, setConnector] = useState(null);

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const handleExecuteQuery = async (query, variables) => {
    setLoading(true);

    try {
      const result = await graphqlApi.executeQuery(endpoint, query, variables, headers, auth);
      setResponse(result.data);
    } catch (error) {
      console.error('Error executing GraphQL query:', error);
      setResponse({
        status: error.response?.status || 0,
        statusText: error.message || 'Request failed',
        data: {
          errors: [
            {
              message: error.message || 'An error occurred while executing the query',
              extensions: {
                code: 'NETWORK_ERROR',
                exception: {
                  stacktrace: error.stack?.split('\n') || []
                }
              }
            }
          ]
        },
        headers: {},
        responseTime: 0
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSaveConfig = (config) => {
    setConnector(config);
    setEndpoint(config.baseUrl);
    setHeaders(config.headers);

    // Set authentication
    if (config.authentication.type === 'none') {
      setAuth(null);
    } else {
      setAuth({
        type: config.authentication.type,
        ...config.authentication.fields
      });
    }

    // Switch to query tab
    setActiveTab('query');
  };

  return (
    <DashboardLayout>
      <Container maxWidth="xl">
        <Box sx={{ mb: 3 }}>
          <Typography variant="h4" component="h1">
            GraphQL Testing
          </Typography>
          <Typography variant="body1" color="textSecondary">
            Test and explore GraphQL APIs
          </Typography>
        </Box>

        <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
          <Tabs value={activeTab} onChange={handleTabChange} aria-label="graphql tabs">
            <Tab label="Query Builder" value="query" />
            <Tab label="Subscriptions" value="subscription" />
            <Tab label="Configuration" value="config" />
          </Tabs>
        </Box>

        {activeTab === 'query' && (
          <Box>
            <Paper variant="outlined" sx={{ p: 2, mb: 3 }}>
              <Grid container spacing={2} alignItems="center">
                <Grid item xs={12} md={8}>
                  <TextField
                    fullWidth
                    label="GraphQL Endpoint"
                    value={endpoint}
                    onChange={(e) => setEndpoint(e.target.value)}
                    placeholder="https://api.example.com/graphql"
                    required
                  />
                </Grid>

                <Grid item xs={12} md={4}>
                  <Button
                    variant="outlined"
                    onClick={() => setActiveTab('config')}
                    fullWidth
                  >
                    Configure Authentication & Headers
                  </Button>
                </Grid>
              </Grid>
            </Paper>

            <Grid container spacing={3}>
              <Grid item xs={12}>
                <GraphQLQueryBuilder
                  endpoint={endpoint}
                  headers={headers}
                  auth={auth}
                  onExecute={handleExecuteQuery}
                />
              </Grid>

              <Grid item xs={12}>
                <Divider sx={{ my: 3 }} />

                <Typography variant="h6" gutterBottom>
                  Response
                </Typography>

                <GraphQLResponseViewer response={response} />
              </Grid>
            </Grid>
          </Box>
        )}

        {activeTab === 'subscription' && (
          <Box>
            <Paper variant="outlined" sx={{ p: 2, mb: 3 }}>
              <Grid container spacing={2} alignItems="center">
                <Grid item xs={12} md={8}>
                  <TextField
                    fullWidth
                    label="GraphQL Endpoint"
                    value={endpoint}
                    onChange={(e) => setEndpoint(e.target.value)}
                    placeholder="https://api.example.com/graphql"
                    required
                  />
                </Grid>

                <Grid item xs={12} md={4}>
                  <Button
                    variant="outlined"
                    onClick={() => setActiveTab('config')}
                    fullWidth
                  >
                    Configure Authentication & Headers
                  </Button>
                </Grid>
              </Grid>
            </Paper>

            <GraphQLSubscriptionManager
              endpoint={endpoint}
              headers={headers}
              auth={auth}
            />
          </Box>
        )}

        {activeTab === 'config' && (
          <GraphQLConnectorConfig
            connector={connector}
            onSave={handleSaveConfig}
            onTest={(result) => console.log('Test result:', result)}
          />
        )}
      </Container>
    </DashboardLayout>
  );
};

export default GraphQLPage;
