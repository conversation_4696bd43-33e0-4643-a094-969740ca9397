"""
Model integration utilities for the Universal Compliance Intelligence Architecture (UCIA).

This module provides utilities for integrating fine-tuned models with the UCIA.
"""

import os
import logging
from typing import Dict, List, Optional, Any, Tuple

import torch
from transformers import AutoModelForCausalLM, AutoTokenizer

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ComplianceModelIntegration:
    """
    Integration of fine-tuned compliance models with the UCIA.
    
    This class provides methods for loading and using fine-tuned models.
    """
    
    @staticmethod
    def load_model(model_path: str, use_gpu: bool = True) -> Tuple[Any, Any]:
        """
        Load a fine-tuned model and tokenizer.
        
        Args:
            model_path: Path to the fine-tuned model
            use_gpu: Whether to use GPU if available
            
        Returns:
            Tuple of (model, tokenizer)
        """
        # Check if GPU is available
        device = torch.device("cuda" if torch.cuda.is_available() and use_gpu else "cpu")
        logger.info(f"Loading model from {model_path} on {device}")
        
        try:
            # Load tokenizer and model
            tokenizer = AutoTokenizer.from_pretrained(model_path)
            model = AutoModelForCausalLM.from_pretrained(model_path).to(device)
            logger.info("Model and tokenizer loaded successfully")
            return model, tokenizer
        except Exception as e:
            logger.error(f"Failed to load model or tokenizer: {e}")
            raise
    
    @staticmethod
    def generate_response(model: Any, tokenizer: Any, query: str, max_length: int = 512) -> str:
        """
        Generate a response to a compliance query.
        
        Args:
            model: The language model
            tokenizer: The tokenizer
            query: The compliance query
            max_length: Maximum length of the generated response
            
        Returns:
            The generated response
        """
        # Format the input
        input_text = f"Question: {query}\nAnswer:"
        
        # Tokenize the input
        inputs = tokenizer(input_text, return_tensors="pt").to(model.device)
        
        # Generate a response
        with torch.no_grad():
            outputs = model.generate(
                inputs['input_ids'],
                max_length=max_length,
                num_return_sequences=1,
                temperature=0.7,
                top_p=0.9,
                do_sample=True,
                pad_token_id=tokenizer.eos_token_id
            )
        
        # Decode the response
        response = tokenizer.decode(outputs[0], skip_special_tokens=True)
        
        # Extract the answer part
        if "Answer:" in response:
            response = response.split("Answer:")[1].strip()
        
        return response
    
    @staticmethod
    def get_available_models() -> List[Dict[str, str]]:
        """
        Get a list of available fine-tuned models.
        
        Returns:
            List of dictionaries with model information
        """
        models = []
        
        # Check for models in the fine_tuning directory
        fine_tuning_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'fine_tuning', 'runs')
        if os.path.isdir(fine_tuning_dir):
            for run_dir in os.listdir(fine_tuning_dir):
                model_dir = os.path.join(fine_tuning_dir, run_dir, 'model')
                if os.path.isdir(model_dir) and os.path.exists(os.path.join(model_dir, 'config.json')):
                    models.append({
                        'id': run_dir,
                        'path': model_dir,
                        'name': f"Fine-tuned model ({run_dir})",
                        'description': "Custom fine-tuned compliance model"
                    })
        
        # Add some pre-trained models
        models.extend([
            {
                'id': 'gpt2',
                'path': 'gpt2',
                'name': 'GPT-2 (Small)',
                'description': 'OpenAI GPT-2 small model (124M parameters)'
            },
            {
                'id': 'gpt2-medium',
                'path': 'gpt2-medium',
                'name': 'GPT-2 (Medium)',
                'description': 'OpenAI GPT-2 medium model (355M parameters)'
            },
            {
                'id': 'EleutherAI/gpt-neo-1.3B',
                'path': 'EleutherAI/gpt-neo-1.3B',
                'name': 'GPT-Neo (1.3B)',
                'description': 'EleutherAI GPT-Neo model (1.3B parameters)'
            }
        ])
        
        return models
