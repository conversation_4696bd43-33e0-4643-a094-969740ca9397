version: '3.8'

# NovaFuse Unified Deployment Pipeline
# Orchestrates all 200+ components across 8 core categories
# Supports selective deployment and dependency management

services:
  # ===== CORE NOVA PLATFORMS =====
  
  novafuse-core:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: novafuse-core
    ports:
      - "3000:3000"
      - "8080:8080"
    environment:
      - NODE_ENV=production
      - NOVA_PLATFORM=core
      - PSI_STABILITY_THRESHOLD=0.8
      - COHERENCE_MODE=enabled
    volumes:
      - ./src/novafuse:/app/novafuse
      - ./api:/app/api
      - nova-data:/data
    networks:
      - nova-network
    depends_on:
      - nova-database
      - nova-redis
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    labels:
      - "nova.platform=core"
      - "nova.category=platform"
      - "nova.priority=critical"

  novaconnect:
    build:
      context: .
      dockerfile: Dockerfile.novaconnect
    container_name: novaconnect
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - NOVA_PLATFORM=connect
      - API_GATEWAY_URL=http://novafuse-core:3000
    volumes:
      - ./src/novaconnect:/app/novaconnect
      - ./api-gateway:/app/gateway
    networks:
      - nova-network
    depends_on:
      - novafuse-core
    labels:
      - "nova.platform=connect"
      - "nova.category=platform"

  novacore:
    build:
      context: .
      dockerfile: Dockerfile.novacore
    container_name: novacore
    ports:
      - "3002:3002"
    environment:
      - NODE_ENV=production
      - NOVA_PLATFORM=core
      - TENSOR_RUNTIME=enabled
    volumes:
      - ./src/novacore:/app/novacore
    networks:
      - nova-network
    labels:
      - "nova.platform=core"
      - "nova.category=platform"

  novashield:
    build:
      context: .
      dockerfile: Dockerfile.security
    container_name: novashield
    ports:
      - "3003:3003"
    environment:
      - NODE_ENV=production
      - NOVA_PLATFORM=shield
      - SECURITY_LEVEL=maximum
      - TRACE_GUARD=enabled
    volumes:
      - ./src/novashield:/app/novashield
    networks:
      - nova-network
    labels:
      - "nova.platform=shield"
      - "nova.category=security"
      - "nova.priority=critical"

  # ===== COHERENCE-NATIVE ENGINES =====
  
  neri-engine:
    build:
      context: .
      dockerfile: Dockerfile.coherence
    container_name: neri-engine
    ports:
      - "4001:4001"
    environment:
      - COHERENCE_ENGINE=neri
      - PSI_STABILITY_REQUIRED=true
      - PROTEIN_FOLDING=enabled
    volumes:
      - ./src/neri:/app/neri
      - coherence-data:/data/coherence
    networks:
      - nova-network
    depends_on:
      - coherence-validator
    labels:
      - "nova.engine=neri"
      - "nova.category=coherence"
      - "nova.priority=critical"

  nece-engine:
    build:
      context: .
      dockerfile: Dockerfile.coherence
    container_name: nece-engine
    ports:
      - "4002:4002"
    environment:
      - COHERENCE_ENGINE=nece
      - CHEMISTRY_MODE=emergent
      - TRANSMUTATION=enabled
    volumes:
      - ./src/nece:/app/nece
    networks:
      - nova-network
    labels:
      - "nova.engine=nece"
      - "nova.category=coherence"

  csme-engine:
    build:
      context: .
      dockerfile: Dockerfile.medical
    container_name: csme-engine
    ports:
      - "4003:4003"
    environment:
      - COHERENCE_ENGINE=csme
      - MEDICAL_SAFETY=enabled
      - HIPAA_COMPLIANCE=true
    volumes:
      - ./src/csme:/app/csme
    networks:
      - nova-network
    labels:
      - "nova.engine=csme"
      - "nova.category=medical"
      - "nova.priority=high"

  novasentient:
    build:
      context: .
      dockerfile: Dockerfile.coherence
    container_name: novasentient
    ports:
      - "4004:4004"
    environment:
      - COHERENCE_ENGINE=sentient
      - GENERAL_INTELLIGENCE=true
      - PSI_GUIDED_REASONING=enabled
    volumes:
      - ./src/novasentient:/app/novasentient
    networks:
      - nova-network
    labels:
      - "nova.engine=sentient"
      - "nova.category=coherence"
      - "nova.priority=critical"

  # ===== BLOCKCHAIN & FINANCIAL SYSTEMS =====
  
  kethernet:
    build:
      context: ./coherence-reality-systems
      dockerfile: Dockerfile
    container_name: kethernet
    ports:
      - "5001:5001"
      - "5002:5002"  # P2P port
    environment:
      - BLOCKCHAIN_TYPE=coherence
      - CONSENSUS_ALGORITHM=coherence
      - PSI_COHERENCE_THRESHOLD=2847
      - TRINITY_VALIDATION=enabled
    volumes:
      - ./coherence-reality-systems:/app
      - kethernet-data:/data/blockchain
    networks:
      - nova-network
    labels:
      - "nova.blockchain=kethernet"
      - "nova.category=blockchain"
      - "nova.priority=high"

  chaeonix-trading:
    build:
      context: ./coherence-reality-systems/chaeonix-divine-dashboard
      dockerfile: ../Dockerfile
    container_name: chaeonix-trading
    ports:
      - "5003:5003"
    environment:
      - TRADING_ENGINE=divine
      - MT5_INTEGRATION=enabled
      - PROPHECY_ENGINE=active
    volumes:
      - ./coherence-reality-systems/chaeonix-trading-engine:/app/trading
    networks:
      - nova-network
    labels:
      - "nova.trading=chaeonix"
      - "nova.category=financial"

  # ===== TESTING & VALIDATION =====
  
  unified-test-runner:
    build:
      context: .
      dockerfile: Dockerfile.testing
    container_name: unified-test-runner
    environment:
      - TEST_ENVIRONMENT=production
      - COMPREHENSIVE_TESTING=true
      - TEST_CATEGORIES=all
    volumes:
      - .:/app
      - test-results:/app/test-results
    networks:
      - nova-network
    command: ["node", "novafuse-unified-test-runner.js", "--continuous"]
    labels:
      - "nova.service=testing"
      - "nova.category=validation"

  # ===== SUPPORTING INFRASTRUCTURE =====
  
  nova-database:
    image: postgres:15
    container_name: nova-database
    environment:
      - POSTGRES_DB=novafuse
      - POSTGRES_USER=nova
      - POSTGRES_PASSWORD=${NOVA_DB_PASSWORD}
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    networks:
      - nova-network
    labels:
      - "nova.service=database"
      - "nova.category=infrastructure"

  nova-redis:
    image: redis:7-alpine
    container_name: nova-redis
    command: redis-server --appendonly yes
    volumes:
      - redis-data:/data
    networks:
      - nova-network
    labels:
      - "nova.service=cache"
      - "nova.category=infrastructure"

  coherence-validator:
    build:
      context: .
      dockerfile: Dockerfile.coherence
    container_name: coherence-validator
    environment:
      - SERVICE_TYPE=validator
      - PSI_VALIDATION=continuous
      - TRINITY_MONITORING=enabled
    volumes:
      - coherence-data:/data/coherence
    networks:
      - nova-network
    labels:
      - "nova.service=validator"
      - "nova.category=coherence"

  # ===== MONITORING & OBSERVABILITY =====
  
  prometheus:
    image: prom/prometheus:latest
    container_name: nova-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus-data:/prometheus
    networks:
      - nova-network
    labels:
      - "nova.service=monitoring"
      - "nova.category=observability"

  grafana:
    image: grafana/grafana:latest
    container_name: nova-grafana
    ports:
      - "3030:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana:/etc/grafana/provisioning
    networks:
      - nova-network
    depends_on:
      - prometheus
    labels:
      - "nova.service=visualization"
      - "nova.category=observability"

  # ===== DEMO & DOCUMENTATION SERVICES =====
  
  demo-hub:
    build:
      context: .
      dockerfile: Dockerfile.demos
    container_name: demo-hub
    ports:
      - "8000:8000"
    environment:
      - DEMO_MODE=production
      - TOTAL_DEMOS=93
    volumes:
      - .:/app
      - ./novafuse-demo-selector.html:/app/public/index.html
    networks:
      - nova-network
    labels:
      - "nova.service=demos"
      - "nova.category=presentation"

  master-control-hub:
    build:
      context: .
      dockerfile: Dockerfile.control
    container_name: master-control-hub
    ports:
      - "8001:8001"
    environment:
      - CONTROL_HUB=master
      - ECOSYSTEM_COMPONENTS=200
    volumes:
      - .:/app
      - ./novafuse-master-control-hub.html:/app/public/index.html
    networks:
      - nova-network
    labels:
      - "nova.service=control"
      - "nova.category=management"

# ===== NETWORKS =====
networks:
  nova-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# ===== VOLUMES =====
volumes:
  nova-data:
    driver: local
  coherence-data:
    driver: local
  kethernet-data:
    driver: local
  postgres-data:
    driver: local
  redis-data:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local
  test-results:
    driver: local
