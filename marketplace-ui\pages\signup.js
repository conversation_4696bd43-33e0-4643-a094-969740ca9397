import { useState } from "react";
import Head from "next/head";
import Link from "next/link";
import { useRouter } from "next/router";

export default function Signup() {
  const router = useRouter();
  const { plan } = router.query;

  const [step, setStep] = useState(1);
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    password: "",
    confirmPassword: "",
    company: "",
    agreeTerms: false,
    plan: plan || "basic"
  });
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const plans = {
    basic: {
      name: "Basic",
      price: "$49/month",
      features: [
        "5 API connectors",
        "10,000 API calls per month",
        "Basic support",
        "Standard SLA",
        "Community access"
      ]
    },
    pro: {
      name: "Pro",
      price: "$149/month",
      features: [
        "20 API connectors",
        "100,000 API calls per month",
        "Priority support",
        "Enhanced SLA",
        "Advanced analytics",
        "Custom connectors"
      ]
    },
    enterprise: {
      name: "Enterprise",
      price: "Custom pricing",
      features: [
        "Unlimited API connectors",
        "Unlimited API calls",
        "24/7 dedicated support",
        "Custom SLA",
        "Advanced security features",
        "Custom integrations",
        "Dedicated account manager"
      ]
    }
  };

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === "checkbox" ? checked : value
    });

    // Clear error when field is changed
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: null
      });
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (step === 1) {
      if (!formData.firstName) newErrors.firstName = "First name is required";
      if (!formData.lastName) newErrors.lastName = "Last name is required";
      if (!formData.email) newErrors.email = "Email is required";
      if (formData.email && !/\S+@\S+\.\S+/.test(formData.email)) newErrors.email = "Email is invalid";
      if (!formData.company) newErrors.company = "Company name is required";
    } else if (step === 2) {
      if (!formData.password) newErrors.password = "Password is required";
      if (formData.password && formData.password.length < 8) newErrors.password = "Password must be at least 8 characters";
      if (!formData.confirmPassword) newErrors.confirmPassword = "Please confirm your password";
      if (formData.password !== formData.confirmPassword) newErrors.confirmPassword = "Passwords do not match";
      if (!formData.agreeTerms) newErrors.agreeTerms = "You must agree to the terms and conditions";
    }

    return newErrors;
  };

  const handleNextStep = () => {
    const newErrors = validateForm();
    if (Object.keys(newErrors).length === 0) {
      setStep(step + 1);
    } else {
      setErrors(newErrors);
    }
  };

  const handlePrevStep = () => {
    setStep(step - 1);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    const newErrors = validateForm();
    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    setIsSubmitting(true);

    // In a real implementation, this would submit to an API
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Redirect to success page
      router.push("/signup-success");
    } catch (error) {
      console.error("Error submitting form:", error);
      setErrors({
        submit: "There was an error creating your account. Please try again."
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Head>
        <title>Sign Up | NovaFuse</title>
        <meta name="description" content="Create your NovaFuse account" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="max-w-md mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-extrabold text-gray-900">
              Create Your Account
            </h1>
            <p className="mt-2 text-gray-600">
              Get started with NovaFuse in just a few steps
            </p>
          </div>

          {/* Progress Steps */}
          <div className="mb-8">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
                  step >= 1 ? "bg-blue-600" : "bg-gray-300"
                }`}>
                  <span className="text-white text-sm font-medium">1</span>
                </div>
                <div className="ml-2">
                  <p className={`text-sm font-medium ${
                    step >= 1 ? "text-blue-600" : "text-gray-500"
                  }`}>Account</p>
                </div>
              </div>
              <div className={`flex-1 h-0.5 mx-4 ${
                step >= 2 ? "bg-blue-600" : "bg-gray-300"
              }`}></div>
              <div className="flex items-center">
                <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
                  step >= 2 ? "bg-blue-600" : "bg-gray-300"
                }`}>
                  <span className="text-white text-sm font-medium">2</span>
                </div>
                <div className="ml-2">
                  <p className={`text-sm font-medium ${
                    step >= 2 ? "text-blue-600" : "text-gray-500"
                  }`}>Security</p>
                </div>
              </div>
              <div className={`flex-1 h-0.5 mx-4 ${
                step >= 3 ? "bg-blue-600" : "bg-gray-300"
              }`}></div>
              <div className="flex items-center">
                <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
                  step >= 3 ? "bg-blue-600" : "bg-gray-300"
                }`}>
                  <span className="text-white text-sm font-medium">3</span>
                </div>
                <div className="ml-2">
                  <p className={`text-sm font-medium ${
                    step >= 3 ? "text-blue-600" : "text-gray-500"
                  }`}>Plan</p>
                </div>
              </div>
            </div>
          </div>

          {/* Selected Plan Summary */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <div className="flex justify-between items-center">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">
                  {plans[formData.plan]?.name || "Basic"} Plan
                </h3>
                <p className="text-sm text-gray-600">
                  {plans[formData.plan]?.price || "$49/month"}
                </p>
              </div>
              <button
                type="button"
                onClick={() => setStep(3)}
                className="text-blue-600 text-sm font-medium hover:text-blue-800"
              >
                Change
              </button>
            </div>
          </div>

          {/* Form */}
          <div className="bg-white shadow overflow-hidden rounded-lg">
            <form onSubmit={handleSubmit}>
              {/* Step 1: Account Information */}
              {step === 1 && (
                <div className="px-4 py-5 sm:p-6">
                  <h2 className="text-lg font-medium text-gray-900 mb-4">Account Information</h2>
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                      <div>
                        <label htmlFor="firstName" className="block text-sm font-medium text-gray-700">
                          First Name *
                        </label>
                        <input
                          type="text"
                          name="firstName"
                          id="firstName"
                          className={`mt-1 block w-full border ${
                            errors.firstName ? "border-red-500" : "border-gray-300"
                          } rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm`}
                          value={formData.firstName}
                          onChange={handleChange}
                        />
                        {errors.firstName && (
                          <p className="mt-1 text-sm text-red-600">{errors.firstName}</p>
                        )}
                      </div>
                      <div>
                        <label htmlFor="lastName" className="block text-sm font-medium text-gray-700">
                          Last Name *
                        </label>
                        <input
                          type="text"
                          name="lastName"
                          id="lastName"
                          className={`mt-1 block w-full border ${
                            errors.lastName ? "border-red-500" : "border-gray-300"
                          } rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm`}
                          value={formData.lastName}
                          onChange={handleChange}
                        />
                        {errors.lastName && (
                          <p className="mt-1 text-sm text-red-600">{errors.lastName}</p>
                        )}
                      </div>
                    </div>
                    <div>
                      <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                        Email *
                      </label>
                      <input
                        type="email"
                        name="email"
                        id="email"
                        className={`mt-1 block w-full border ${
                          errors.email ? "border-red-500" : "border-gray-300"
                        } rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm`}
                        value={formData.email}
                        onChange={handleChange}
                      />
                      {errors.email && (
                        <p className="mt-1 text-sm text-red-600">{errors.email}</p>
                      )}
                    </div>
                    <div>
                      <label htmlFor="company" className="block text-sm font-medium text-gray-700">
                        Company Name *
                      </label>
                      <input
                        type="text"
                        name="company"
                        id="company"
                        className={`mt-1 block w-full border ${
                          errors.company ? "border-red-500" : "border-gray-300"
                        } rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm`}
                        value={formData.company}
                        onChange={handleChange}
                      />
                      {errors.company && (
                        <p className="mt-1 text-sm text-red-600">{errors.company}</p>
                      )}
                    </div>
                  </div>
                </div>
              )}

              {/* Step 2: Security */}
              {step === 2 && (
                <div className="px-4 py-5 sm:p-6">
                  <h2 className="text-lg font-medium text-gray-900 mb-4">Security</h2>
                  <div className="space-y-4">
                    <div>
                      <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                        Password *
                      </label>
                      <input
                        type="password"
                        name="password"
                        id="password"
                        className={`mt-1 block w-full border ${
                          errors.password ? "border-red-500" : "border-gray-300"
                        } rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm`}
                        value={formData.password}
                        onChange={handleChange}
                      />
                      {errors.password && (
                        <p className="mt-1 text-sm text-red-600">{errors.password}</p>
                      )}
                      <p className="mt-1 text-xs text-gray-500">
                        Password must be at least 8 characters long
                      </p>
                    </div>
                    <div>
                      <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700">
                        Confirm Password *
                      </label>
                      <input
                        type="password"
                        name="confirmPassword"
                        id="confirmPassword"
                        className={`mt-1 block w-full border ${
                          errors.confirmPassword ? "border-red-500" : "border-gray-300"
                        } rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm`}
                        value={formData.confirmPassword}
                        onChange={handleChange}
                      />
                      {errors.confirmPassword && (
                        <p className="mt-1 text-sm text-red-600">{errors.confirmPassword}</p>
                      )}
                    </div>
                    <div className="flex items-start">
                      <div className="flex items-center h-5">
                        <input
                          id="agreeTerms"
                          name="agreeTerms"
                          type="checkbox"
                          className={`focus:ring-blue-500 h-4 w-4 text-blue-600 border ${
                            errors.agreeTerms ? "border-red-500" : "border-gray-300"
                          } rounded`}
                          checked={formData.agreeTerms}
                          onChange={handleChange}
                        />
                      </div>
                      <div className="ml-3 text-sm">
                        <label htmlFor="agreeTerms" className={`font-medium ${
                          errors.agreeTerms ? "text-red-500" : "text-gray-700"
                        }`}>
                          I agree to the Terms of Service and Privacy Policy
                        </label>
                      </div>
                    </div>
                    {errors.agreeTerms && (
                      <p className="text-sm text-red-600">{errors.agreeTerms}</p>
                    )}
                  </div>
                </div>
              )}

              {/* Step 3: Plan Selection */}
              {step === 3 && (
                <div className="px-4 py-5 sm:p-6">
                  <h2 className="text-lg font-medium text-gray-900 mb-4">Select Your Plan</h2>
                  <div className="space-y-4">
                    <div
                      className={`border rounded-lg p-4 cursor-pointer ${
                        formData.plan === "basic"
                          ? "border-blue-500 bg-blue-50"
                          : "border-gray-300 hover:border-blue-300"
                      }`}
                      onClick={() => setFormData({...formData, plan: "basic"})}
                    >
                      <div className="flex justify-between items-start mb-2">
                        <h3 className="font-semibold">Basic</h3>
                        {formData.plan === "basic" && (
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                          </svg>
                        )}
                      </div>
                      <p className="text-lg font-bold mb-2">$49/month</p>
                      <ul className="space-y-2">
                        {plans.basic.features.map((feature, index) => (
                          <li key={index} className="flex items-start text-sm">
                            <svg className="h-5 w-5 text-green-500 mr-2 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                            {feature}
                          </li>
                        ))}
                      </ul>
                    </div>
                    <div
                      className={`border rounded-lg p-4 cursor-pointer ${
                        formData.plan === "pro"
                          ? "border-blue-500 bg-blue-50"
                          : "border-gray-300 hover:border-blue-300"
                      }`}
                      onClick={() => setFormData({...formData, plan: "pro"})}
                    >
                      <div className="flex justify-between items-start mb-2">
                        <h3 className="font-semibold">Pro</h3>
                        {formData.plan === "pro" && (
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                          </svg>
                        )}
                      </div>
                      <p className="text-lg font-bold mb-2">$149/month</p>
                      <ul className="space-y-2">
                        {plans.pro.features.map((feature, index) => (
                          <li key={index} className="flex items-start text-sm">
                            <svg className="h-5 w-5 text-green-500 mr-2 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                            {feature}
                          </li>
                        ))}
                      </ul>
                    </div>
                    <div
                      className={`border rounded-lg p-4 cursor-pointer ${
                        formData.plan === "enterprise"
                          ? "border-blue-500 bg-blue-50"
                          : "border-gray-300 hover:border-blue-300"
                      }`}
                      onClick={() => setFormData({...formData, plan: "enterprise"})}
                    >
                      <div className="flex justify-between items-start mb-2">
                        <h3 className="font-semibold">Enterprise</h3>
                        {formData.plan === "enterprise" && (
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                          </svg>
                        )}
                      </div>
                      <p className="text-lg font-bold mb-2">Custom pricing</p>
                      <ul className="space-y-2">
                        {plans.enterprise.features.map((feature, index) => (
                          <li key={index} className="flex items-start text-sm">
                            <svg className="h-5 w-5 text-green-500 mr-2 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                            {feature}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              )}

              {/* Form Navigation */}
              <div className="px-4 py-3 bg-gray-50 text-right sm:px-6 flex justify-between">
                {step > 1 ? (
                  <button
                    type="button"
                    onClick={handlePrevStep}
                    className="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    Back
                  </button>
                ) : (
                  <div></div>
                )}

                {step < 3 ? (
                  <button
                    type="button"
                    onClick={handleNextStep}
                    className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    Next
                  </button>
                ) : (
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className={`inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${
                      isSubmitting ? "opacity-50 cursor-not-allowed" : ""
                    }`}
                  >
                    {isSubmitting ? "Creating Account..." : "Create Account"}
                  </button>
                )}
              </div>
            </form>
          </div>

          <div className="mt-6 text-center">
            <p className="text-sm text-gray-600">
              Already have an account?{" "}
              <Link
                href="/login"
                className="font-medium text-blue-600 hover:text-blue-500"
              >
                Sign in
              </Link>
            </p>
          </div>
        </div>
      </main>
    </div>
  );
}
