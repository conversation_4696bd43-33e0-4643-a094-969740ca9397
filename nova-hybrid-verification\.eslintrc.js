module.exports = {
  "env": {
    "node": true,
    "commonjs": true,
    "es2021": true,
    "jest": true
  },
  "extends": "eslint:recommended",
  "parserOptions": {
    "ecmaVersion": 12
  },
  "rules": {
    "indent": [
      "error",
      2
    ],
    "linebreak-style": [
      "error",
      "unix"
    ],
    "quotes": [
      "error",
      "single",
      { "allowTemplateLiterals": true }
    ],
    "semi": [
      "error",
      "always"
    ],
    "no-unused-vars": [
      "warn",
      { 
        "vars": "all", 
        "args": "after-used", 
        "ignoreRestSiblings": false 
      }
    ],
    "no-console": [
      "warn",
      { 
        "allow": ["warn", "error", "info", "debug"] 
      }
    ],
    "no-trailing-spaces": "error",
    "comma-dangle": ["error", "never"],
    "arrow-spacing": ["error", { "before": true, "after": true }],
    "block-spacing": "error",
    "comma-spacing": ["error", { "before": false, "after": true }],
    "keyword-spacing": ["error", { "before": true, "after": true }],
    "space-before-blocks": "error",
    "space-before-function-paren": ["error", {
      "anonymous": "always",
      "named": "never",
      "asyncArrow": "always"
    }],
    "space-in-parens": ["error", "never"],
    "space-infix-ops": "error",
    "spaced-comment": ["error", "always"],
    "no-multiple-empty-lines": ["error", { "max": 2, "maxEOF": 1 }],
    "no-var": "error",
    "prefer-const": "warn",
    "object-curly-spacing": ["error", "always"],
    "array-bracket-spacing": ["error", "never"],
    "brace-style": ["error", "1tbs", { "allowSingleLine": true }],
    "camelcase": ["error", { "properties": "never" }],
    "eqeqeq": ["error", "always"],
    "max-len": ["warn", { "code": 100, "ignoreComments": true, "ignoreStrings": true, "ignoreTemplateLiterals": true }],
    "no-duplicate-imports": "error",
    "no-useless-constructor": "error",
    "prefer-template": "warn",
    "require-await": "warn"
  }
};
