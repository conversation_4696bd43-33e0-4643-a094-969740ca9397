/**
 * NovaFuse Universal API Connector - Connector Registry Service
 *
 * This module provides a service for managing the connector registry.
 */

const { Connector } = require('../models/connector');
const { createLogger } = require('../../utils/logger');
const cache = require('../../utils/cache');
const performanceMonitor = require('../../utils/performance-monitor');

const logger = createLogger('connector-registry-service');

/**
 * Connector Registry Service class
 */
class ConnectorRegistryService {
  constructor() {
    this.connectors = new Map();
    logger.info('Connector registry service initialized');
  }

  /**
   * Get all connectors
   *
   * @param {Object} filters - Filters to apply
   * @returns {Array<Connector>} - The connectors
   */
  async getAllConnectors(filters = {}) {
    const startTime = Date.now();

    // Create cache key based on filters
    const cacheKey = `connectors:all:${JSON.stringify(filters)}`;

    // Check cache first
    const cachedConnectors = cache.get(cacheKey);
    if (cachedConnectors) {
      logger.debug('Returning connectors from cache', { filters });

      // Record performance metrics
      performanceMonitor.recordRequest({
        success: true,
        latency: Date.now() - startTime
      });

      return cachedConnectors;
    }

    // Get connectors from storage
    let connectors = Array.from(this.connectors.values());

    // Apply filters
    if (filters.status) {
      connectors = connectors.filter(c => c.status === filters.status);
    }

    if (filters.type) {
      connectors = connectors.filter(c => c.type === filters.type);
    }

    // Default sort by name
    connectors.sort((a, b) => a.name.localeCompare(b.name));

    // Cache results
    cache.set(cacheKey, connectors, 60); // Cache for 1 minute

    logger.debug('Retrieved connectors from storage', {
      count: connectors.length,
      filters
    });

    // Record performance metrics
    performanceMonitor.recordRequest({
      success: true,
      latency: Date.now() - startTime
    });

    return connectors;
  }

  /**
   * Get a connector by ID
   *
   * @param {string} id - The connector ID
   * @returns {Connector} - The connector
   */
  async getConnector(id) {
    const startTime = Date.now();

    // Create cache key
    const cacheKey = `connector:${id}`;

    // Check cache first
    const cachedConnector = cache.get(cacheKey);
    if (cachedConnector) {
      logger.debug('Returning connector from cache', { id });

      // Record performance metrics
      performanceMonitor.recordRequest({
        success: true,
        latency: Date.now() - startTime
      });

      return cachedConnector;
    }

    // Get connector from storage
    const connector = this.connectors.get(id);

    if (!connector) {
      logger.warn('Connector not found', { id });

      // Record performance metrics
      performanceMonitor.recordRequest({
        success: false,
        latency: Date.now() - startTime
      });

      throw new Error(`Connector not found with ID: ${id}`);
    }

    // Cache connector
    cache.set(cacheKey, connector, 300); // Cache for 5 minutes

    logger.debug('Retrieved connector from storage', { id });

    // Record performance metrics
    performanceMonitor.recordRequest({
      success: true,
      latency: Date.now() - startTime
    });

    return connector;
  }

  /**
   * Create a new connector
   *
   * @param {Object} data - The connector data
   * @returns {Connector} - The created connector
   */
  async createConnector(data) {
    const startTime = Date.now();

    try {
      // Create connector
      const connector = new Connector(data);

      // Validate connector
      const validation = connector.validate();
      if (!validation.valid) {
        logger.warn('Invalid connector data', { errors: validation.errors });

        // Record performance metrics
        performanceMonitor.recordRequest({
          success: false,
          latency: Date.now() - startTime
        });

        throw new Error(`Invalid connector data: ${validation.errors.join(', ')}`);
      }

      // Save connector
      this.connectors.set(connector.id, connector);

      // Invalidate cache
      cache.delete(`connector:${connector.id}`);

      // Invalidate all connectors cache
      // We use a simple approach here - in a real implementation, we would use a more targeted approach
      for (const key of Array.from(cache.cache.keys())) {
        if (key.startsWith('connectors:all:')) {
          cache.delete(key);
        }
      }

      logger.info('Connector created', { id: connector.id, name: connector.name });

      // Record performance metrics
      performanceMonitor.recordRequest({
        success: true,
        latency: Date.now() - startTime
      });

      return connector;
    } catch (error) {
      // Record performance metrics
      performanceMonitor.recordRequest({
        success: false,
        latency: Date.now() - startTime
      });

      throw error;
    }
  }

  /**
   * Update a connector
   *
   * @param {string} id - The connector ID
   * @param {Object} data - The data to update
   * @returns {Connector} - The updated connector
   */
  async updateConnector(id, data) {
    const startTime = Date.now();

    try {
      // Get connector
      const connector = await this.getConnector(id);

      // Update fields
      Object.assign(connector, {
        ...data,
        id: connector.id, // Ensure ID doesn't change
        updatedAt: new Date().toISOString()
      });

      // Validate connector
      const validation = connector.validate();
      if (!validation.valid) {
        logger.warn('Invalid connector data for update', { id, errors: validation.errors });

        // Record performance metrics
        performanceMonitor.recordRequest({
          success: false,
          latency: Date.now() - startTime
        });

        throw new Error(`Invalid connector data: ${validation.errors.join(', ')}`);
      }

      // Invalidate cache
      cache.delete(`connector:${id}`);

      // Invalidate all connectors cache
      for (const key of Array.from(cache.cache.keys())) {
        if (key.startsWith('connectors:all:')) {
          cache.delete(key);
        }
      }

      logger.info('Connector updated', { id, name: connector.name });

      // Record performance metrics
      performanceMonitor.recordRequest({
        success: true,
        latency: Date.now() - startTime
      });

      return connector;
    } catch (error) {
      // Record performance metrics
      performanceMonitor.recordRequest({
        success: false,
        latency: Date.now() - startTime
      });

      throw error;
    }
  }

  /**
   * Delete a connector
   *
   * @param {string} id - The connector ID
   * @returns {boolean} - Whether the connector was deleted
   */
  async deleteConnector(id) {
    const startTime = Date.now();

    try {
      // Get connector
      await this.getConnector(id);

      // Delete connector
      const result = this.connectors.delete(id);

      // Invalidate cache
      cache.delete(`connector:${id}`);

      // Invalidate all connectors cache
      for (const key of Array.from(cache.cache.keys())) {
        if (key.startsWith('connectors:all:')) {
          cache.delete(key);
        }
      }

      logger.info('Connector deleted', { id });

      // Record performance metrics
      performanceMonitor.recordRequest({
        success: true,
        latency: Date.now() - startTime
      });

      return result;
    } catch (error) {
      // Record performance metrics
      performanceMonitor.recordRequest({
        success: false,
        latency: Date.now() - startTime
      });

      throw error;
    }
  }
}

// Create singleton instance
const connectorRegistryService = new ConnectorRegistryService();

module.exports = connectorRegistryService;
