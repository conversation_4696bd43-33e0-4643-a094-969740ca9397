# CHAEONIX Metrics Reset Script
# Resets all profit analytics to $0.00

Write-Host "🔄 CHAEONIX METRICS RESET UTILITY" -ForegroundColor Cyan
Write-Host "=" * 50 -ForegroundColor Cyan

Write-Host "📡 Sending reset request to CHAEONIX dashboard..." -ForegroundColor Yellow

try {
    $body = @{
        action = "RESET_METRICS"
    } | ConvertTo-Json

    $response = Invoke-RestMethod -Uri "http://localhost:3141/api/analytics/profit-tracker" `
                                  -Method POST `
                                  -ContentType "application/json" `
                                  -Body $body

    if ($response.success) {
        Write-Host "✅ SUCCESS! Metrics reset completed" -ForegroundColor Green
        Write-Host "💰 Message: $($response.message)" -ForegroundColor Green
        Write-Host "📈 Total Trades: $($response.total_trades)" -ForegroundColor Green
        Write-Host "💵 Total Profit: $($response.total_profit)" -ForegroundColor Green
        Write-Host "⏰ Reset Time: $($response.reset_timestamp)" -ForegroundColor Green
        Write-Host ""
        Write-Host "🎯 NEXT STEPS:" -ForegroundColor Cyan
        Write-Host "1. Refresh your CHAEONIX dashboard" -ForegroundColor White
        Write-Host "2. Verify all profit numbers show $0.00" -ForegroundColor White
        Write-Host "3. Start real trading with clean metrics" -ForegroundColor White
        Write-Host ""
        Write-Host "🚀 CHAEONIX is ready for real trading data!" -ForegroundColor Green
    } else {
        Write-Host "❌ FAILED! Reset was not successful" -ForegroundColor Red
        Write-Host "🚨 Error: $($response.error)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ FAILED! Network error occurred" -ForegroundColor Red
    Write-Host "🚨 Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "🔧 TROUBLESHOOTING:" -ForegroundColor Yellow
    Write-Host "1. Make sure CHAEONIX dashboard is running (npm run dev)" -ForegroundColor White
    Write-Host "2. Verify dashboard is accessible at http://localhost:3141" -ForegroundColor White
    Write-Host "3. Check if port 3141 is available" -ForegroundColor White
}

Write-Host ""
Write-Host "Press any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
