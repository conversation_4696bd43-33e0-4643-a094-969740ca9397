"""
Quantum computing module for protein folding.

This module provides interfaces and implementations for quantum computing backends
used in hybrid quantum-classical protein folding.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List, Tuple
import numpy as np

class QuantumBackend(ABC):
    """Abstract base class for quantum computing backends."""
    
    @abstractmethod
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize the quantum backend.
        
        Args:
            config: Configuration dictionary for the backend
        """
        self.config = config or {}
    
    @abstractmethod
    def run_circuit(
        self, 
        circuit: Any,  # Type depends on the backend
        num_qubits: int,
        depth: int,
        shots: int
    ) -> Dict[str, int]:
        """Run a quantum circuit and return the measurement results.
        
        Args:
            circuit: Quantum circuit to run
            num_qubits: Number of qubits in the circuit
            depth: Depth of the quantum circuit
            shots: Number of measurement shots
            
        Returns:
            Dictionary mapping measurement outcomes to counts
        """
        pass
    
    @abstractmethod
    def get_quantum_volume(self) -> int:
        """Get the quantum volume of the backend.
        
        Returns:
            The quantum volume as an integer
        """
        pass
    
    @abstractmethod
    def get_backend_info(self) -> Dict[str, Any]:
        """Get information about the backend.
        
        Returns:
            Dictionary containing backend information
        """
        pass
    
    @classmethod
    @abstractmethod
    def is_available(cls) -> bool:
        """Check if the backend is available.
        
        Returns:
            True if the backend is available, False otherwise
        """
        pass
