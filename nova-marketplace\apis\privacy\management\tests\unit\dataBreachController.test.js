/**
 * Data Breach Controller Tests
 *
 * This file contains unit tests for the Data Breach controller.
 */

// Mock the services module before importing the controller
jest.mock('../../services', () => {
  // Create mock service methods
  const mockDataBreachService = {
    getAllDataBreaches: jest.fn().mockResolvedValue({
      data: [{ _id: 'mock-id-1', title: 'Test Breach', severity: 'high' }],
      pagination: {
        total: 1,
        page: 1,
        limit: 10,
        pages: 1
      }
    }),
    getDataBreachById: jest.fn().mockResolvedValue({ _id: 'mock-id-1', title: 'Test Breach', severity: 'high' }),
    createDataBreach: jest.fn().mockResolvedValue({ _id: 'mock-id-1', title: 'Test Breach', severity: 'high' }),
    updateDataBreach: jest.fn().mockResolvedValue({ _id: 'mock-id-1', title: 'Updated Breach', severity: 'critical' }),
    deleteDataBreach: jest.fn().mockResolvedValue({ _id: 'mock-id-1' }),
    sendBreachNotifications: jest.fn().mockResolvedValue({
      id: 'mock-id-1',
      notificationType: 'supervisory-authority',
      notificationMethod: 'email',
      notificationTimestamp: new Date()
    }),
    generateBreachReport: jest.fn().mockResolvedValue({
      id: 'mock-id-1',
      title: 'Test Breach',
      severity: 'high',
      timeline: {},
      impact: {},
      response: {},
      notifications: {},
      generatedAt: new Date()
    }),
    assessNotificationRequirements: jest.fn().mockResolvedValue({
      id: 'mock-id-1',
      authorityNotification: {
        required: true,
        reasoning: 'Notification required due to high severity',
        threshold: '72-hours',
        status: 'pending'
      },
      dataSubjectNotification: {
        required: false,
        reasoning: 'Notification not required based on current assessment',
        status: 'pending'
      },
      factors: {},
      assessedAt: new Date()
    })
  };

  return {
    dataBreachService: mockDataBreachService
  };
});

// Now import the controller and mocked service
const { dataBreachController } = require('../../controllers');
const { dataBreachService } = require('../../services');

describe('Data Breach Controller', () => {
  // Setup request, response, and next function for testing
  let req;
  let res;
  let next;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Setup request object
    req = {
      params: {},
      query: {},
      body: {}
    };

    // Setup response object
    res = {
      json: jest.fn(),
      status: jest.fn().mockReturnThis()
    };

    // Setup next function
    next = jest.fn();
  });

  describe('getAllDataBreaches', () => {
    it('should return all data breaches with pagination', async () => {
      // Setup request query
      req.query = {
        page: '1',
        limit: '10',
        status: 'active',
        severity: 'high',
        search: 'test',
        startDate: '2023-01-01',
        endDate: '2023-12-31',
        sortBy: 'severity',
        sortOrder: 'desc'
      };

      // Call the controller method
      await dataBreachController.getAllDataBreaches(req, res, next);

      // Verify the service method was called with the correct parameters
      expect(dataBreachService.getAllDataBreaches).toHaveBeenCalledWith(expect.objectContaining({
        page: 1,
        limit: 10,
        filter: expect.objectContaining({
          status: 'active',
          severity: 'high',
          $text: { $search: 'test' },
          detectionDate: expect.objectContaining({
            $gte: expect.any(Date),
            $lte: expect.any(Date)
          })
        }),
        sort: expect.objectContaining({
          severity: -1
        })
      }));

      // Verify the response
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.any(Array),
        pagination: expect.objectContaining({
          total: 1,
          page: 1,
          limit: 10,
          pages: 1
        })
      }));
    });

    it('should use default values if query parameters are not provided', async () => {
      // Setup empty request query
      req.query = {};

      // Call the controller method
      await dataBreachController.getAllDataBreaches(req, res, next);

      // Verify the service method was called with default parameters
      expect(dataBreachService.getAllDataBreaches).toHaveBeenCalledWith(expect.objectContaining({
        page: 1,
        limit: 10,
        filter: expect.any(Object),
        sort: expect.objectContaining({
          detectionDate: -1
        })
      }));
    });

    it('should handle only startDate parameter', async () => {
      // Setup request query with only startDate
      req.query = {
        startDate: '2023-01-01'
      };

      // Call the controller method
      await dataBreachController.getAllDataBreaches(req, res, next);

      // Verify the service method was called with the correct filter
      expect(dataBreachService.getAllDataBreaches).toHaveBeenCalledWith(expect.objectContaining({
        filter: expect.objectContaining({
          detectionDate: expect.objectContaining({
            $gte: expect.any(Date)
          })
        })
      }));
    });

    it('should handle only endDate parameter', async () => {
      // Setup request query with only endDate
      req.query = {
        endDate: '2023-12-31'
      };

      // Call the controller method
      await dataBreachController.getAllDataBreaches(req, res, next);

      // Verify the service method was called with the correct filter
      expect(dataBreachService.getAllDataBreaches).toHaveBeenCalledWith(expect.objectContaining({
        filter: expect.objectContaining({
          detectionDate: expect.objectContaining({
            $lte: expect.any(Date)
          })
        })
      }));
    });

    it('should handle custom sorting with ascending order', async () => {
      // Setup request query with sorting parameters
      req.query = {
        sortBy: 'title',
        sortOrder: 'asc'
      };

      // Call the controller method
      await dataBreachController.getAllDataBreaches(req, res, next);

      // Verify the service method was called with the correct sort
      expect(dataBreachService.getAllDataBreaches).toHaveBeenCalledWith(expect.objectContaining({
        sort: expect.objectContaining({
          title: 1
        })
      }));
    });

    it('should handle errors', async () => {
      // Setup error
      const error = new Error('Service error');
      dataBreachService.getAllDataBreaches.mockRejectedValueOnce(error);

      // Call the controller method
      await dataBreachController.getAllDataBreaches(req, res, next);

      // Verify error handling
      expect(next).toHaveBeenCalledWith(error);
    });


  });

  describe('getDataBreachById', () => {
    it('should return a data breach by ID', async () => {
      // Setup request params
      req.params.id = 'mock-id-1';

      // Call the controller method
      await dataBreachController.getDataBreachById(req, res, next);

      // Verify the service method was called with the correct parameters
      expect(dataBreachService.getDataBreachById).toHaveBeenCalledWith('mock-id-1');

      // Verify the response
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({ _id: 'mock-id-1' })
      }));
    });

    it('should handle NotFoundError', async () => {
      // Setup request params
      req.params.id = 'non-existent-id';

      // Setup service to throw NotFoundError
      const notFoundError = new Error('Data breach not found');
      notFoundError.name = 'NotFoundError';
      dataBreachService.getDataBreachById.mockRejectedValueOnce(notFoundError);

      // Call the controller method
      await dataBreachController.getDataBreachById(req, res, next);

      // Verify error handling
      expect(next).toHaveBeenCalledWith(expect.objectContaining({
        name: 'NotFoundError',
        message: 'Data breach not found'
      }));
    });

    it('should handle other errors', async () => {
      // Setup request params
      req.params.id = 'mock-id-1';

      // Setup error
      const error = new Error('Service error');
      dataBreachService.getDataBreachById.mockRejectedValueOnce(error);

      // Call the controller method
      await dataBreachController.getDataBreachById(req, res, next);

      // Verify error handling
      expect(next).toHaveBeenCalledWith(error);
    });
  });

  describe('createDataBreach', () => {
    it('should create a new data breach', async () => {
      // Setup request body
      req.body = {
        title: 'Test Breach',
        description: 'This is a test breach',
        severity: 'high',
        breachDate: new Date(),
        detectionDate: new Date()
      };

      // Call the controller method
      await dataBreachController.createDataBreach(req, res, next);

      // Verify the service method was called with the correct parameters
      expect(dataBreachService.createDataBreach).toHaveBeenCalledWith(req.body);

      // Verify the response
      expect(res.status).toHaveBeenCalledWith(201);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({ _id: 'mock-id-1' }),
        message: 'Data breach created successfully'
      }));
    });

    it('should create a data breach with affected data subjects', async () => {
      // Setup request body with affected data subjects
      req.body = {
        title: 'Test Breach',
        description: 'This is a test breach',
        severity: 'high',
        breachDate: new Date(),
        detectionDate: new Date(),
        affectedDataSubjects: [
          { type: 'customer', count: 100 },
          { type: 'employee', count: 10 }
        ]
      };

      // Call the controller method
      await dataBreachController.createDataBreach(req, res, next);

      // Verify the service method was called with the correct parameters
      expect(dataBreachService.createDataBreach).toHaveBeenCalledWith(expect.objectContaining({
        affectedDataSubjects: expect.arrayContaining([
          expect.objectContaining({ type: 'customer', count: 100 }),
          expect.objectContaining({ type: 'employee', count: 10 })
        ])
      }));
    });

    it('should create a data breach with data categories', async () => {
      // Setup request body with data categories
      req.body = {
        title: 'Test Breach',
        description: 'This is a test breach',
        severity: 'high',
        breachDate: new Date(),
        detectionDate: new Date(),
        dataCategories: ['personal', 'financial', 'health']
      };

      // Call the controller method
      await dataBreachController.createDataBreach(req, res, next);

      // Verify the service method was called with the correct parameters
      expect(dataBreachService.createDataBreach).toHaveBeenCalledWith(expect.objectContaining({
        dataCategories: expect.arrayContaining(['personal', 'financial', 'health'])
      }));
    });

    it('should handle validation errors', async () => {
      // Setup request body
      req.body = {
        // Missing required fields
      };

      // Setup error
      const validationError = new Error('Validation error');
      validationError.name = 'ValidationError';
      dataBreachService.createDataBreach.mockRejectedValueOnce(validationError);

      // Call the controller method
      await dataBreachController.createDataBreach(req, res, next);

      // Verify error handling
      expect(next).toHaveBeenCalledWith(validationError);
    });

    it('should handle other errors', async () => {
      // Setup request body
      req.body = {
        title: 'Test Breach',
        description: 'This is a test breach',
        severity: 'high'
      };

      // Setup error
      const error = new Error('Service error');
      dataBreachService.createDataBreach.mockRejectedValueOnce(error);

      // Call the controller method
      await dataBreachController.createDataBreach(req, res, next);

      // Verify error handling
      expect(next).toHaveBeenCalledWith(error);
    });
  });

  describe('updateDataBreach', () => {
    it('should update a data breach', async () => {
      // Setup request params and body
      req.params.id = 'mock-id-1';
      req.body = {
        title: 'Updated Breach',
        severity: 'critical'
      };

      // Call the controller method
      await dataBreachController.updateDataBreach(req, res, next);

      // Verify the service method was called with the correct parameters
      expect(dataBreachService.updateDataBreach).toHaveBeenCalledWith('mock-id-1', req.body);

      // Verify the response
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({
          _id: 'mock-id-1',
          title: 'Updated Breach',
          severity: 'critical'
        }),
        message: 'Data breach updated successfully'
      }));
    });

    it('should update a data breach status', async () => {
      // Setup request params and body
      req.params.id = 'mock-id-1';
      req.body = {
        status: 'resolved',
        resolutionDate: new Date(),
        resolutionNotes: 'Issue has been resolved'
      };

      // Call the controller method
      await dataBreachController.updateDataBreach(req, res, next);

      // Verify the service method was called with the correct parameters
      expect(dataBreachService.updateDataBreach).toHaveBeenCalledWith('mock-id-1', req.body);

      // Verify the response
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({
          _id: 'mock-id-1',
          title: 'Updated Breach',
          severity: 'critical'
        }),
        message: 'Data breach updated successfully'
      }));
    });

    it('should handle NotFoundError', async () => {
      // Setup request params and body
      req.params.id = 'non-existent-id';
      req.body = {
        title: 'Updated Breach'
      };

      // Setup service to throw NotFoundError
      const notFoundError = new Error('Data breach not found');
      notFoundError.name = 'NotFoundError';
      dataBreachService.updateDataBreach.mockRejectedValueOnce(notFoundError);

      // Call the controller method
      await dataBreachController.updateDataBreach(req, res, next);

      // Verify error handling
      expect(next).toHaveBeenCalledWith(expect.objectContaining({
        name: 'NotFoundError',
        message: 'Data breach not found'
      }));
    });

    it('should handle validation errors', async () => {
      // Setup request params and body
      req.params.id = 'mock-id-1';
      req.body = {
        severity: 'invalid-severity'
      };

      // Setup error
      const validationError = new Error('Validation error');
      validationError.name = 'ValidationError';
      dataBreachService.updateDataBreach.mockRejectedValueOnce(validationError);

      // Call the controller method
      await dataBreachController.updateDataBreach(req, res, next);

      // Verify error handling
      expect(next).toHaveBeenCalledWith(validationError);
    });

    it('should handle other errors', async () => {
      // Setup request params and body
      req.params.id = 'mock-id-1';
      req.body = {
        title: 'Updated Breach'
      };

      // Setup error
      const error = new Error('Service error');
      dataBreachService.updateDataBreach.mockRejectedValueOnce(error);

      // Call the controller method
      await dataBreachController.updateDataBreach(req, res, next);

      // Verify error handling
      expect(next).toHaveBeenCalledWith(error);
    });
  });

  describe('deleteDataBreach', () => {
    it('should delete a data breach', async () => {
      // Setup request params
      req.params.id = 'mock-id-1';

      // Call the controller method
      await dataBreachController.deleteDataBreach(req, res, next);

      // Verify the service method was called with the correct parameters
      expect(dataBreachService.deleteDataBreach).toHaveBeenCalledWith('mock-id-1');

      // Verify the response
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        message: 'Data breach deleted successfully'
      }));
    });

    it('should handle NotFoundError', async () => {
      // Setup request params
      req.params.id = 'non-existent-id';

      // Setup service to throw NotFoundError
      const notFoundError = new Error('Data breach not found');
      notFoundError.name = 'NotFoundError';
      dataBreachService.deleteDataBreach.mockRejectedValueOnce(notFoundError);

      // Call the controller method
      await dataBreachController.deleteDataBreach(req, res, next);

      // Verify error handling
      expect(next).toHaveBeenCalledWith(expect.objectContaining({
        name: 'NotFoundError',
        message: 'Data breach not found'
      }));
    });

    it('should handle other errors', async () => {
      // Setup request params
      req.params.id = 'mock-id-1';

      // Setup error
      const error = new Error('Service error');
      dataBreachService.deleteDataBreach.mockRejectedValueOnce(error);

      // Call the controller method
      await dataBreachController.deleteDataBreach(req, res, next);

      // Verify error handling
      expect(next).toHaveBeenCalledWith(error);
    });
  });

  describe('sendBreachNotifications', () => {
    it('should send notifications for a data breach', async () => {
      // Setup request params and body
      req.params.id = 'mock-id-1';
      req.body = {
        notificationType: 'supervisory-authority',
        notificationMethod: 'email',
        notificationContent: 'This is a test notification'
      };

      // Call the controller method
      await dataBreachController.sendBreachNotifications(req, res, next);

      // Verify the service method was called with the correct parameters
      expect(dataBreachService.sendBreachNotifications).toHaveBeenCalledWith('mock-id-1', expect.objectContaining({
        notificationType: 'supervisory-authority',
        notificationMethod: 'email',
        notificationContent: 'This is a test notification'
      }));

      // Verify the response
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({
          id: 'mock-id-1',
          notificationType: 'supervisory-authority',
          notificationMethod: 'email'
        }),
        message: 'Supervisory authority notified successfully'
      }));
    });

    it('should handle missing notification parameters', async () => {
      // Setup request params and body with missing parameters
      req.params.id = 'mock-id-1';
      req.body = {
        notificationType: 'supervisory-authority',
        // Missing notificationMethod and notificationContent
      };

      // Call the controller method
      await dataBreachController.sendBreachNotifications(req, res, next);

      // Verify error handling
      expect(next).toHaveBeenCalledWith(expect.objectContaining({
        name: 'ValidationError',
        message: 'Notification type, method, and content are required'
      }));
    });

    it('should handle NotFoundError', async () => {
      // Setup request params and body
      req.params.id = 'non-existent-id';
      req.body = {
        notificationType: 'supervisory-authority',
        notificationMethod: 'email',
        notificationContent: 'This is a test notification'
      };

      // Setup service to throw NotFoundError
      const notFoundError = new Error('Data breach not found');
      notFoundError.name = 'NotFoundError';
      dataBreachService.sendBreachNotifications.mockRejectedValueOnce(notFoundError);

      // Call the controller method
      await dataBreachController.sendBreachNotifications(req, res, next);

      // Verify error handling
      expect(next).toHaveBeenCalledWith(expect.objectContaining({
        name: 'NotFoundError',
        message: 'Data breach not found'
      }));
    });

    it('should handle other errors', async () => {
      // Setup request params and body
      req.params.id = 'mock-id-1';
      req.body = {
        notificationType: 'supervisory-authority',
        notificationMethod: 'email',
        notificationContent: 'This is a test notification'
      };

      // Setup error
      const error = new Error('Service error');
      dataBreachService.sendBreachNotifications.mockRejectedValueOnce(error);

      // Call the controller method
      await dataBreachController.sendBreachNotifications(req, res, next);

      // Verify error handling
      expect(next).toHaveBeenCalledWith(error);
    });
  });

  describe('generateBreachReport', () => {
    it('should generate a report for a data breach', async () => {
      // Setup request params
      req.params.id = 'mock-id-1';

      // Call the controller method
      await dataBreachController.generateBreachReport(req, res, next);

      // Verify the service method was called with the correct parameters
      expect(dataBreachService.generateBreachReport).toHaveBeenCalledWith('mock-id-1');

      // Verify the response
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({
          id: 'mock-id-1',
          title: 'Test Breach',
          severity: 'high'
        }),
        message: 'Breach report generated successfully'
      }));
    });

    it('should handle NotFoundError', async () => {
      // Setup request params
      req.params.id = 'non-existent-id';

      // Setup service to throw NotFoundError
      const notFoundError = new Error('Data breach not found');
      notFoundError.name = 'NotFoundError';
      dataBreachService.generateBreachReport.mockRejectedValueOnce(notFoundError);

      // Call the controller method
      await dataBreachController.generateBreachReport(req, res, next);

      // Verify error handling
      expect(next).toHaveBeenCalledWith(expect.objectContaining({
        name: 'NotFoundError',
        message: 'Data breach not found'
      }));
    });

    it('should handle other errors', async () => {
      // Setup request params
      req.params.id = 'mock-id-1';

      // Setup error
      const error = new Error('Service error');
      dataBreachService.generateBreachReport.mockRejectedValueOnce(error);

      // Call the controller method
      await dataBreachController.generateBreachReport(req, res, next);

      // Verify error handling
      expect(next).toHaveBeenCalledWith(error);
    });
  });

  describe('assessNotificationRequirements', () => {
    it('should assess notification requirements for a data breach', async () => {
      // Setup request params
      req.params.id = 'mock-id-1';

      // Call the controller method
      await dataBreachController.assessNotificationRequirements(req, res, next);

      // Verify the service method was called with the correct parameters
      expect(dataBreachService.assessNotificationRequirements).toHaveBeenCalledWith('mock-id-1');

      // Verify the response
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({
          id: 'mock-id-1',
          authorityNotification: expect.objectContaining({
            required: true,
            reasoning: expect.any(String)
          }),
          dataSubjectNotification: expect.objectContaining({
            required: false,
            reasoning: expect.any(String)
          })
        }),
        message: 'Notification requirements assessed successfully'
      }));
    });

    it('should handle NotFoundError', async () => {
      // Setup request params
      req.params.id = 'non-existent-id';

      // Setup service to throw NotFoundError
      const notFoundError = new Error('Data breach not found');
      notFoundError.name = 'NotFoundError';
      dataBreachService.assessNotificationRequirements.mockRejectedValueOnce(notFoundError);

      // Call the controller method
      await dataBreachController.assessNotificationRequirements(req, res, next);

      // Verify error handling
      expect(next).toHaveBeenCalledWith(expect.objectContaining({
        name: 'NotFoundError',
        message: 'Data breach not found'
      }));
    });

    it('should handle other errors', async () => {
      // Setup request params
      req.params.id = 'mock-id-1';

      // Setup error
      const error = new Error('Service error');
      dataBreachService.assessNotificationRequirements.mockRejectedValueOnce(error);

      // Call the controller method
      await dataBreachController.assessNotificationRequirements(req, res, next);

      // Verify error handling
      expect(next).toHaveBeenCalledWith(error);
    });
  });
});
