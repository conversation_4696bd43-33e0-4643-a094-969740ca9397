/**
 * Integration Tests for NovaTrack API
 */

const { describe, it, before, after, expect } = require('@jest/globals');
const request = require('supertest');
const express = require('express');
const bodyParser = require('body-parser');
const fs = require('fs');
const path = require('path');
const os = require('os');

// Import the TrackingManager
const { TrackingManager } = require('../../../src/novatrack');

describe('NovaTrack API', () => {
  let app;
  let server;
  let tempDir;
  let trackingManager;
  let testRequirement;
  
  before(async () => {
    // Create a temporary directory for test data
    tempDir = fs.mkdtempSync(path.join(os.tmpdir(), 'novatrack-api-test-'));
    
    // Initialize the TrackingManager with the temporary directory
    trackingManager = new TrackingManager(tempDir);
    
    // Create a test requirement
    testRequirement = trackingManager.create_requirement({
      name: 'Data Subject Rights',
      description: 'Implement processes for handling data subject rights requests',
      framework: 'GDPR',
      category: 'privacy',
      priority: 'high',
      status: 'in_progress',
      due_date: '2023-12-31',
      assigned_to: 'privacy_officer',
      tags: ['gdpr', 'data_subject_rights', 'privacy']
    });
    
    // Create a test activity
    trackingManager.create_activity({
      name: 'Document Data Subject Rights Process',
      description: 'Create documentation for handling data subject rights requests',
      requirement_id: testRequirement.id,
      type: 'documentation',
      status: 'completed',
      start_date: '2023-01-01',
      end_date: '2023-01-15',
      assigned_to: 'privacy_officer',
      notes: 'Documentation completed and reviewed'
    });
    
    // Create an Express app for testing
    app = express();
    app.use(bodyParser.json());
    
    // Define API routes
    app.get('/api/v1/novatrack/requirements', (req, res) => {
      try {
        const framework = req.query.framework;
        let requirements;
        
        if (framework) {
          requirements = trackingManager.get_requirements_by_framework(framework);
        } else {
          requirements = Object.values(trackingManager.requirements);
        }
        
        res.json({
          success: true,
          data: requirements
        });
      } catch (error) {
        res.status(500).json({
          success: false,
          error: error.message
        });
      }
    });
    
    app.get('/api/v1/novatrack/requirements/:id', (req, res) => {
      try {
        const requirementId = req.params.id;
        const requirement = trackingManager.get_requirement(requirementId);
        
        res.json({
          success: true,
          data: requirement
        });
      } catch (error) {
        res.status(404).json({
          success: false,
          error: error.message
        });
      }
    });
    
    app.get('/api/v1/novatrack/requirements/:id/activities', (req, res) => {
      try {
        const requirementId = req.params.id;
        const activities = trackingManager.get_requirement_activities(requirementId);
        
        res.json({
          success: true,
          data: activities
        });
      } catch (error) {
        res.status(500).json({
          success: false,
          error: error.message
        });
      }
    });
    
    app.post('/api/v1/novatrack/requirements', (req, res) => {
      try {
        const requirementData = req.body;
        const requirement = trackingManager.create_requirement(requirementData);
        
        res.status(201).json({
          success: true,
          data: requirement
        });
      } catch (error) {
        res.status(400).json({
          success: false,
          error: error.message
        });
      }
    });
    
    app.post('/api/v1/novatrack/activities', (req, res) => {
      try {
        const activityData = req.body;
        const activity = trackingManager.create_activity(activityData);
        
        res.status(201).json({
          success: true,
          data: activity
        });
      } catch (error) {
        res.status(400).json({
          success: false,
          error: error.message
        });
      }
    });
    
    // Start the server
    server = app.listen(3001);
  });
  
  after(async () => {
    // Close the server
    if (server) {
      server.close();
    }
    
    // Clean up the temporary directory
    if (fs.existsSync(tempDir)) {
      fs.rmSync(tempDir, { recursive: true, force: true });
    }
  });
  
  describe('GET /api/v1/novatrack/requirements', () => {
    it('should return all requirements', async () => {
      // Act
      const response = await request(app).get('/api/v1/novatrack/requirements');
      
      // Assert
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.length).toBeGreaterThan(0);
      expect(response.body.data[0].id).toBe(testRequirement.id);
    });
    
    it('should filter requirements by framework', async () => {
      // Act
      const response = await request(app).get('/api/v1/novatrack/requirements?framework=GDPR');
      
      // Assert
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.length).toBeGreaterThan(0);
      expect(response.body.data[0].framework).toBe('GDPR');
    });
    
    it('should return an empty array for a framework with no requirements', async () => {
      // Act
      const response = await request(app).get('/api/v1/novatrack/requirements?framework=HIPAA');
      
      // Assert
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.length).toBe(0);
    });
  });
  
  describe('GET /api/v1/novatrack/requirements/:id', () => {
    it('should return a specific requirement', async () => {
      // Act
      const response = await request(app).get(`/api/v1/novatrack/requirements/${testRequirement.id}`);
      
      // Assert
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.id).toBe(testRequirement.id);
      expect(response.body.data.name).toBe(testRequirement.name);
    });
    
    it('should return a 404 error for a non-existent requirement', async () => {
      // Act
      const response = await request(app).get('/api/v1/novatrack/requirements/non-existent-id');
      
      // Assert
      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toBeDefined();
    });
  });
  
  describe('GET /api/v1/novatrack/requirements/:id/activities', () => {
    it('should return activities for a specific requirement', async () => {
      // Act
      const response = await request(app).get(`/api/v1/novatrack/requirements/${testRequirement.id}/activities`);
      
      // Assert
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.length).toBeGreaterThan(0);
      expect(response.body.data[0].requirement_id).toBe(testRequirement.id);
    });
    
    it('should return an empty array for a requirement with no activities', async () => {
      // Create a new requirement with no activities
      const newRequirement = trackingManager.create_requirement({
        name: 'New Requirement',
        description: 'A new requirement with no activities',
        framework: 'GDPR',
        category: 'privacy',
        priority: 'medium',
        status: 'pending',
        due_date: '2023-12-31',
        assigned_to: 'privacy_officer',
        tags: ['gdpr', 'privacy']
      });
      
      // Act
      const response = await request(app).get(`/api/v1/novatrack/requirements/${newRequirement.id}/activities`);
      
      // Assert
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.length).toBe(0);
    });
  });
  
  describe('POST /api/v1/novatrack/requirements', () => {
    it('should create a new requirement', async () => {
      // Arrange
      const newRequirementData = {
        name: 'Data Protection Impact Assessment',
        description: 'Conduct data protection impact assessments for high-risk processing',
        framework: 'GDPR',
        category: 'risk_assessment',
        priority: 'medium',
        status: 'pending',
        due_date: '2023-12-31',
        assigned_to: 'privacy_officer',
        tags: ['gdpr', 'dpia', 'risk_assessment']
      };
      
      // Act
      const response = await request(app)
        .post('/api/v1/novatrack/requirements')
        .send(newRequirementData);
      
      // Assert
      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.id).toBeDefined();
      expect(response.body.data.name).toBe(newRequirementData.name);
      expect(response.body.data.framework).toBe(newRequirementData.framework);
    });
    
    it('should return a 400 error for invalid requirement data', async () => {
      // Arrange
      const invalidRequirementData = {
        // Missing required name field
        description: 'Conduct data protection impact assessments for high-risk processing',
        framework: 'GDPR',
        category: 'risk_assessment',
        priority: 'medium',
        status: 'pending',
        due_date: '2023-12-31',
        assigned_to: 'privacy_officer',
        tags: ['gdpr', 'dpia', 'risk_assessment']
      };
      
      // Act
      const response = await request(app)
        .post('/api/v1/novatrack/requirements')
        .send(invalidRequirementData);
      
      // Assert
      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toBeDefined();
    });
  });
  
  describe('POST /api/v1/novatrack/activities', () => {
    it('should create a new activity', async () => {
      // Arrange
      const newActivityData = {
        name: 'Implement Data Subject Rights Portal',
        description: 'Develop a portal for handling data subject rights requests',
        requirement_id: testRequirement.id,
        type: 'task',
        status: 'in_progress',
        start_date: '2023-01-16',
        end_date: '2023-02-15',
        assigned_to: 'developer',
        notes: 'Portal development in progress'
      };
      
      // Act
      const response = await request(app)
        .post('/api/v1/novatrack/activities')
        .send(newActivityData);
      
      // Assert
      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.id).toBeDefined();
      expect(response.body.data.name).toBe(newActivityData.name);
      expect(response.body.data.requirement_id).toBe(newActivityData.requirement_id);
    });
    
    it('should return a 400 error for invalid activity data', async () => {
      // Arrange
      const invalidActivityData = {
        // Missing required name field
        description: 'Develop a portal for handling data subject rights requests',
        requirement_id: testRequirement.id,
        type: 'task',
        status: 'in_progress',
        start_date: '2023-01-16',
        end_date: '2023-02-15',
        assigned_to: 'developer',
        notes: 'Portal development in progress'
      };
      
      // Act
      const response = await request(app)
        .post('/api/v1/novatrack/activities')
        .send(invalidActivityData);
      
      // Assert
      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toBeDefined();
    });
  });
});
