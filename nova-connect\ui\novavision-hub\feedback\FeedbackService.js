/**
 * Feedback Service
 * 
 * This module provides utilities for submitting and managing feedback.
 */

/**
 * Feedback Service class
 */
class FeedbackService {
  /**
   * Constructor
   * 
   * @param {Object} options - Service options
   * @param {string} [options.apiUrl='/api/feedback'] - API URL
   * @param {Function} [options.onError] - Error handler
   */
  constructor(options = {}) {
    this.apiUrl = options.apiUrl || '/api/feedback';
    this.onError = options.onError || console.error;
    this.localStorageKey = 'novavision_feedback_history';
  }
  
  /**
   * Submit feedback
   * 
   * @param {Object} feedbackData - Feedback data
   * @returns {Promise<Object>} - Submitted feedback
   */
  async submitFeedback(feedbackData) {
    try {
      // Add timestamp if not present
      if (!feedbackData.metadata) {
        feedbackData.metadata = {};
      }
      
      if (!feedbackData.metadata.timestamp) {
        feedbackData.metadata.timestamp = new Date().toISOString();
      }
      
      // Submit feedback
      const response = await fetch(this.apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(feedbackData)
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Error submitting feedback');
      }
      
      const result = await response.json();
      
      // Store in local history
      this.addToLocalHistory({
        ...feedbackData,
        id: result.data._id || result.data.id,
        status: 'submitted',
        submittedAt: new Date().toISOString()
      });
      
      return result;
    } catch (error) {
      this.onError('Error submitting feedback:', error);
      
      // Store in local history as failed
      this.addToLocalHistory({
        ...feedbackData,
        status: 'failed',
        error: error.message,
        submittedAt: new Date().toISOString()
      });
      
      throw error;
    }
  }
  
  /**
   * Get my feedback
   * 
   * @param {Object} options - Query options
   * @returns {Promise<Object>} - Feedback list with pagination
   */
  async getMyFeedback(options = {}) {
    try {
      // Build query string
      const queryParams = new URLSearchParams();
      
      if (options.page) {
        queryParams.append('page', options.page);
      }
      
      if (options.limit) {
        queryParams.append('limit', options.limit);
      }
      
      if (options.sortBy) {
        queryParams.append('sortBy', options.sortBy);
      }
      
      if (options.sortOrder) {
        queryParams.append('sortOrder', options.sortOrder);
      }
      
      if (options.type) {
        queryParams.append('type', options.type);
      }
      
      if (options.component) {
        queryParams.append('component', options.component);
      }
      
      if (options.status) {
        queryParams.append('status', options.status);
      }
      
      // Get feedback
      const response = await fetch(`${this.apiUrl}/user/me?${queryParams.toString()}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Error getting feedback');
      }
      
      return await response.json();
    } catch (error) {
      this.onError('Error getting feedback:', error);
      throw error;
    }
  }
  
  /**
   * Get feedback by ID
   * 
   * @param {string} id - Feedback ID
   * @returns {Promise<Object>} - Feedback
   */
  async getFeedbackById(id) {
    try {
      const response = await fetch(`${this.apiUrl}/${id}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Error getting feedback');
      }
      
      return await response.json();
    } catch (error) {
      this.onError(`Error getting feedback with ID ${id}:`, error);
      throw error;
    }
  }
  
  /**
   * Add to local history
   * 
   * @param {Object} feedback - Feedback data
   */
  addToLocalHistory(feedback) {
    try {
      // Get existing history
      const history = this.getLocalHistory();
      
      // Add to history
      history.unshift(feedback);
      
      // Limit history size
      if (history.length > 50) {
        history.pop();
      }
      
      // Save history
      localStorage.setItem(this.localStorageKey, JSON.stringify(history));
    } catch (error) {
      this.onError('Error adding to local history:', error);
    }
  }
  
  /**
   * Get local history
   * 
   * @returns {Array} - Feedback history
   */
  getLocalHistory() {
    try {
      const history = localStorage.getItem(this.localStorageKey);
      return history ? JSON.parse(history) : [];
    } catch (error) {
      this.onError('Error getting local history:', error);
      return [];
    }
  }
  
  /**
   * Clear local history
   */
  clearLocalHistory() {
    try {
      localStorage.removeItem(this.localStorageKey);
    } catch (error) {
      this.onError('Error clearing local history:', error);
    }
  }
  
  /**
   * Submit offline feedback
   * 
   * This method attempts to submit any feedback that was stored locally
   * when the user was offline.
   * 
   * @returns {Promise<Object>} - Result with success count and error count
   */
  async submitOfflineFeedback() {
    try {
      // Get local history
      const history = this.getLocalHistory();
      
      // Filter failed feedback
      const failedFeedback = history.filter(feedback => feedback.status === 'failed');
      
      if (failedFeedback.length === 0) {
        return {
          success: true,
          successCount: 0,
          errorCount: 0,
          message: 'No offline feedback to submit'
        };
      }
      
      // Submit each feedback
      const results = await Promise.allSettled(
        failedFeedback.map(async (feedback) => {
          // Remove status and error
          const { status, error, id, submittedAt, ...feedbackData } = feedback;
          
          // Submit feedback
          return await this.submitFeedback(feedbackData);
        })
      );
      
      // Count successes and errors
      const successCount = results.filter(result => result.status === 'fulfilled').length;
      const errorCount = results.filter(result => result.status === 'rejected').length;
      
      return {
        success: true,
        successCount,
        errorCount,
        message: `Submitted ${successCount} feedback items, ${errorCount} failed`
      };
    } catch (error) {
      this.onError('Error submitting offline feedback:', error);
      
      return {
        success: false,
        successCount: 0,
        errorCount: 0,
        message: `Error submitting offline feedback: ${error.message}`
      };
    }
  }
}

// Create singleton instance
const feedbackService = new FeedbackService();

export default feedbackService;
