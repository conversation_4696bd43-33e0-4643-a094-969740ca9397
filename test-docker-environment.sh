#!/bin/bash

# Test Docker Environment Script
# This script sets up the Docker environment and tests the feature flag system

# Set colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Starting Docker Environment Test${NC}"

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
  echo -e "${RED}Docker is not running. Please start Docker and try again.${NC}"
  exit 1
fi

# Build and start the Docker containers
echo -e "${YELLOW}Building and starting Docker containers...${NC}"
docker-compose up -d

# Wait for the containers to start
echo -e "${YELLOW}Waiting for containers to start...${NC}"
sleep 10

# Check if the containers are running
if ! docker-compose ps | grep -q "Up"; then
  echo -e "${RED}Containers failed to start. Please check the logs.${NC}"
  docker-compose logs
  exit 1
fi

echo -e "${GREEN}Containers started successfully.${NC}"

# Run the feature flag tests
echo -e "${YELLOW}Running feature flag tests...${NC}"
docker-compose exec nova-ui npm run test:feature-flags

# Check the test results
if [ $? -eq 0 ]; then
  echo -e "${GREEN}Feature flag tests passed.${NC}"
else
  echo -e "${RED}Feature flag tests failed. Please check the logs.${NC}"
  exit 1
fi

# Clean up
echo -e "${YELLOW}Cleaning up...${NC}"
docker-compose down

echo -e "${GREEN}Docker Environment Test completed successfully.${NC}"
