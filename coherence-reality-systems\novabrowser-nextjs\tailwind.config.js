/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        'nova-primary': '#6366f1',
        'nova-secondary': '#8b5cf6',
        'nova-accent': '#a855f7',
        'coherence-high': '#10b981',
        'coherence-medium': '#f59e0b',
        'coherence-low': '#ef4444',
      },
      fontFamily: {
        'sf-pro': ['SF Pro Display', 'Inter', 'system-ui', 'sans-serif'],
        'mono': ['SF Mono', 'JetBrains Mono', 'monospace'],
      },
      backdropBlur: {
        'xs': '2px',
      },
    },
  },
  plugins: [],
}
