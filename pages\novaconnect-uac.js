import React from 'react';
import Link from 'next/link';
import PageWithSidebar from '../components/PageWithSidebar';

const NovaConnectUAC = () => {
  // SEO metadata
  const pageProps = {
    title: 'NovaConnect Universal API Connector (UAC) - NovaFuse',
    description: 'The world\'s first bidirectional compliance orchestration layer with 100ms data normalization. Connect any API to any API with built-in compliance intelligence.',
    keywords: 'NovaConnect, Universal API Connector, UAC, API integration, compliance orchestration, data normalization, NovaFuse',
    canonical: 'https://novafuse.io/novaconnect-uac',
    ogImage: '/images/novaconnect-uac-og-image.png'
  };

  const sidebarItems = [
    { label: 'Technology Overview', href: '#technology-overview' },
    { label: 'Key Differentiators', href: '#key-differentiators' },
    { label: 'How It Works', href: '#how-it-works' },
    { label: 'Use Cases', href: '#use-cases' },
    { label: 'Technical Specifications', href: '#technical-specifications' },
    { label: 'Demo', href: '/uac-demo' },
  ];

  return (
    <PageWithSidebar title={pageProps.title} sidebarItems={sidebarItems} {...pageProps}>
      <div className="space-y-12">
        {/* Hero Section */}
        <div className="bg-blue-900 p-8 rounded-lg shadow-lg">
          <div className="flex flex-col md:flex-row items-center">
            <div className="md:w-2/3 mb-6 md:mb-0 md:pr-8">
              <h1 className="text-3xl font-bold mb-4">Universal API Connector</h1>
              <p className="text-xl mb-6">
                The world's first bidirectional compliance orchestration layer with 100ms data normalization.
              </p>
              <div className="flex flex-wrap gap-4">
                <Link href="/uac-demo" className="bg-gradient-to-r from-blue-700 to-purple-700 text-white px-6 py-3 rounded-lg font-bold hover:from-blue-800 hover:to-purple-800 inline-block shadow-md hover:shadow-lg transform hover:-translate-y-1 transition-all duration-200 border border-blue-400">
                  See the UAC Demo
                </Link>
                <Link href="/api-docs" className="bg-white text-blue-700 px-6 py-3 rounded-lg font-bold hover:bg-gray-100 inline-block shadow-md hover:shadow-lg transform hover:-translate-y-1 transition-all duration-200 border border-blue-400">
                  View Documentation
                </Link>
              </div>
            </div>
            <div className="md:w-1/3">
              <div className="bg-blue-800 p-6 rounded-lg shadow-inner">
                <h3 className="text-xl font-bold mb-3">Key Benefits</h3>
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <span className="mr-2 bg-gradient-to-r from-blue-600 to-purple-600 text-transparent bg-clip-text font-bold">✓</span>
                    <span>100ms Data Normalization</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 bg-gradient-to-r from-blue-600 to-purple-600 text-transparent bg-clip-text font-bold">✓</span>
                    <span>Cross-Domain Intelligence</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 bg-gradient-to-r from-blue-600 to-purple-600 text-transparent bg-clip-text font-bold">✓</span>
                    <span>Bidirectional Control</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 bg-gradient-to-r from-blue-600 to-purple-600 text-transparent bg-clip-text font-bold">✓</span>
                    <span>AI-Powered Mapping</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Technology Overview Section */}
        <div id="technology-overview" className="scroll-mt-20">
          <h2 className="text-2xl font-bold mb-6">Technology Overview</h2>
          <div className="bg-secondary p-6 rounded-lg shadow-md">
            <p className="mb-4">
              The NovaConnect Universal API Connector (UAC) is a revolutionary technology that enables seamless integration between any API-based systems, with a special focus on compliance and governance data.
            </p>
            <p className="mb-4">
              Unlike traditional API gateways or iPaaS solutions, the UAC is specifically designed to understand and normalize compliance-related data across different domains, frameworks, and systems.
            </p>
            <p className="mb-4">
              With 4 patents pending, our technology represents a fundamental shift in how organizations manage compliance across their technology stack.
            </p>

            <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-blue-900 p-4 rounded-lg">
                <h3 className="text-xl font-bold mb-3">Core Architecture</h3>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Universal Adapter Layer</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Compliance Intelligence Engine</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Real-time Normalization Pipeline</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Bidirectional Control Framework</span>
                  </li>
                </ul>
              </div>

              <div className="bg-blue-900 p-4 rounded-lg">
                <h3 className="text-xl font-bold mb-3">Technical Capabilities</h3>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>100ms data normalization across systems</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Support for 59+ compliance domains</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>AI-powered schema mapping and transformation</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Automatic evidence collection and validation</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Key Differentiators Section */}
        <div id="key-differentiators" className="scroll-mt-20">
          <h2 className="text-2xl font-bold mb-6">Key Differentiators</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-gradient-to-r from-blue-700 to-purple-700 p-6 rounded-lg shadow-md border border-blue-400">
              <h3 className="text-xl font-bold mb-3">100ms Normalization</h3>
              <p className="text-sm text-blue-100 mb-4">
                Our proprietary data pipeline normalizes compliance data across different systems in under 100 milliseconds, enabling real-time compliance monitoring and enforcement.
              </p>
              <div className="bg-blue-800 bg-opacity-50 p-3 rounded-lg">
                <p className="text-sm italic">
                  "Traditional ETL processes take minutes to hours. The UAC's 100ms normalization is a game-changer for real-time compliance."
                </p>
              </div>
            </div>

            <div className="bg-gradient-to-r from-blue-700 to-purple-700 p-6 rounded-lg shadow-md border border-blue-400">
              <h3 className="text-xl font-bold mb-3">Universal Connectivity</h3>
              <p className="text-sm text-blue-100 mb-4">
                Connect any API to any API, regardless of format, protocol, or data structure. The UAC handles REST, GraphQL, SOAP, and even legacy systems through our adapter framework.
              </p>
              <div className="bg-blue-800 bg-opacity-50 p-3 rounded-lg">
                <p className="text-sm italic">
                  "No more custom integration work. The UAC connects to anything with an API, from cloud services to on-prem legacy systems."
                </p>
              </div>
            </div>

            <div className="bg-gradient-to-r from-blue-700 to-purple-700 p-6 rounded-lg shadow-md border border-blue-400">
              <h3 className="text-xl font-bold mb-3">Cross-Domain Mapping</h3>
              <p className="text-sm text-blue-100 mb-4">
                Our AI-powered mapping engine understands relationships between 59+ compliance domains, automatically connecting related controls, evidence, and requirements across frameworks.
              </p>
              <div className="bg-blue-800 bg-opacity-50 p-3 rounded-lg">
                <p className="text-sm italic">
                  "Map once, comply everywhere. The UAC understands that a GDPR control might satisfy parts of SOC 2, ISO 27001, and more."
                </p>
              </div>
            </div>

            <div className="bg-gradient-to-r from-blue-700 to-purple-700 p-6 rounded-lg shadow-md border border-blue-400">
              <h3 className="text-xl font-bold mb-3">Bidirectional Control</h3>
              <p className="text-sm text-blue-100 mb-4">
                Unlike one-way data pipelines, the UAC enables two-way communication, allowing systems to not just share data but also enforce policies and controls across the connected ecosystem.
              </p>
              <div className="bg-blue-800 bg-opacity-50 p-3 rounded-lg">
                <p className="text-sm italic">
                  "When a policy changes in your GRC tool, the UAC automatically updates configurations across your cloud infrastructure."
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* How It Works Section */}
        <div id="how-it-works" className="scroll-mt-20">
          <h2 className="text-2xl font-bold mb-6">How It Works</h2>

          <div className="bg-secondary p-6 rounded-lg shadow-md">
            <div className="flex flex-col space-y-8">
              <div className="flex flex-col md:flex-row">
                <div className="md:w-1/4 flex justify-center items-center mb-4 md:mb-0">
                  <div className="w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center shadow-md border border-blue-400">
                    <span className="text-2xl font-bold">1</span>
                  </div>
                </div>
                <div className="md:w-3/4">
                  <h3 className="text-xl font-bold mb-2">Connect</h3>
                  <p className="text-gray-300">
                    The UAC connects to your existing systems through our library of pre-built adapters or custom connectors. No code changes required to your existing systems - just API credentials.
                  </p>
                </div>
              </div>

              <div className="flex flex-col md:flex-row">
                <div className="md:w-1/4 flex justify-center items-center mb-4 md:mb-0">
                  <div className="w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center shadow-md border border-blue-400">
                    <span className="text-2xl font-bold">2</span>
                  </div>
                </div>
                <div className="md:w-3/4">
                  <h3 className="text-xl font-bold mb-2">Map</h3>
                  <p className="text-gray-300">
                    Our AI-powered mapping engine automatically identifies relationships between data schemas, compliance controls, and system entities. Manual fine-tuning available through our intuitive interface.
                  </p>
                </div>
              </div>

              <div className="flex flex-col md:flex-row">
                <div className="md:w-1/4 flex justify-center items-center mb-4 md:mb-0">
                  <div className="w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center shadow-md border border-blue-400">
                    <span className="text-2xl font-bold">3</span>
                  </div>
                </div>
                <div className="md:w-3/4">
                  <h3 className="text-xl font-bold mb-2">Normalize</h3>
                  <p className="text-gray-300">
                    The UAC's real-time normalization pipeline transforms data between systems in under 100ms, ensuring consistent representation of compliance information regardless of source.
                  </p>
                </div>
              </div>

              <div className="flex flex-col md:flex-row">
                <div className="md:w-1/4 flex justify-center items-center mb-4 md:mb-0">
                  <div className="w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center shadow-md border border-blue-400">
                    <span className="text-2xl font-bold">4</span>
                  </div>
                </div>
                <div className="md:w-3/4">
                  <h3 className="text-xl font-bold mb-2">Orchestrate</h3>
                  <p className="text-gray-300">
                    Define workflows, policies, and automation rules that work across your entire connected ecosystem. The UAC enforces controls bidirectionally, ensuring compliance at every level.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Use Cases Section */}
        <div id="use-cases" className="scroll-mt-20">
          <h2 className="text-2xl font-bold mb-6">Use Cases</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-blue-900 p-6 rounded-lg shadow-md border border-blue-700">
              <h3 className="text-xl font-bold mb-3">Compliance & GRC</h3>
              <ul className="space-y-2">
                <li className="flex items-start">
                  <span className="mr-2 bg-gradient-to-r from-blue-600 to-purple-600 text-transparent bg-clip-text font-bold">✓</span>
                  <span>Automated evidence collection across systems</span>
                </li>
                <li className="flex items-start">
                  <span className="mr-2 bg-gradient-to-r from-blue-600 to-purple-600 text-transparent bg-clip-text font-bold">✓</span>
                  <span>Real-time compliance monitoring and alerts</span>
                </li>
                <li className="flex items-start">
                  <span className="mr-2 bg-gradient-to-r from-blue-600 to-purple-600 text-transparent bg-clip-text font-bold">✓</span>
                  <span>Cross-framework control mapping</span>
                </li>
                <li className="flex items-start">
                  <span className="mr-2 bg-gradient-to-r from-blue-600 to-purple-600 text-transparent bg-clip-text font-bold">✓</span>
                  <span>Audit-ready reporting and documentation</span>
                </li>
              </ul>
            </div>

            <div className="bg-blue-900 p-6 rounded-lg shadow-md border border-blue-700">
              <h3 className="text-xl font-bold mb-3">Business Process Automation</h3>
              <ul className="space-y-2">
                <li className="flex items-start">
                  <span className="mr-2 bg-gradient-to-r from-blue-600 to-purple-600 text-transparent bg-clip-text font-bold">✓</span>
                  <span>Compliance-aware workflow automation</span>
                </li>
                <li className="flex items-start">
                  <span className="mr-2 bg-gradient-to-r from-blue-600 to-purple-600 text-transparent bg-clip-text font-bold">✓</span>
                  <span>Policy-driven process orchestration</span>
                </li>
                <li className="flex items-start">
                  <span className="mr-2 bg-gradient-to-r from-blue-600 to-purple-600 text-transparent bg-clip-text font-bold">✓</span>
                  <span>Secure data exchange between systems</span>
                </li>
                <li className="flex items-start">
                  <span className="mr-2 bg-gradient-to-r from-blue-600 to-purple-600 text-transparent bg-clip-text font-bold">✓</span>
                  <span>Automated approval and verification flows</span>
                </li>
              </ul>
            </div>

            <div className="bg-blue-900 p-6 rounded-lg shadow-md border border-blue-700">
              <h3 className="text-xl font-bold mb-3">Cross-System Intelligence</h3>
              <ul className="space-y-2">
                <li className="flex items-start">
                  <span className="mr-2 bg-gradient-to-r from-blue-600 to-purple-600 text-transparent bg-clip-text font-bold">✓</span>
                  <span>Unified compliance dashboards and reporting</span>
                </li>
                <li className="flex items-start">
                  <span className="mr-2 bg-gradient-to-r from-blue-600 to-purple-600 text-transparent bg-clip-text font-bold">✓</span>
                  <span>Risk correlation across multiple systems</span>
                </li>
                <li className="flex items-start">
                  <span className="mr-2 bg-gradient-to-r from-blue-600 to-purple-600 text-transparent bg-clip-text font-bold">✓</span>
                  <span>Predictive compliance analytics</span>
                </li>
                <li className="flex items-start">
                  <span className="mr-2 bg-gradient-to-r from-blue-600 to-purple-600 text-transparent bg-clip-text font-bold">✓</span>
                  <span>AI-powered compliance recommendations</span>
                </li>
              </ul>
            </div>

            <div className="bg-blue-900 p-6 rounded-lg shadow-md border border-blue-700">
              <h3 className="text-xl font-bold mb-3">Real-Time Monitoring</h3>
              <ul className="space-y-2">
                <li className="flex items-start">
                  <span className="mr-2 bg-gradient-to-r from-blue-600 to-purple-600 text-transparent bg-clip-text font-bold">✓</span>
                  <span>Continuous compliance monitoring</span>
                </li>
                <li className="flex items-start">
                  <span className="mr-2 bg-gradient-to-r from-blue-600 to-purple-600 text-transparent bg-clip-text font-bold">✓</span>
                  <span>Instant policy violation alerts</span>
                </li>
                <li className="flex items-start">
                  <span className="mr-2 bg-gradient-to-r from-blue-600 to-purple-600 text-transparent bg-clip-text font-bold">✓</span>
                  <span>Automated remediation workflows</span>
                </li>
                <li className="flex items-start">
                  <span className="mr-2 bg-gradient-to-r from-blue-600 to-purple-600 text-transparent bg-clip-text font-bold">✓</span>
                  <span>Real-time compliance posture visualization</span>
                </li>
              </ul>
            </div>
          </div>
        </div>

        {/* Technical Specifications Section */}
        <div id="technical-specifications" className="scroll-mt-20">
          <h2 className="text-2xl font-bold mb-6">Technical Specifications</h2>

          <div className="bg-secondary p-6 rounded-lg shadow-md">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-xl font-bold mb-3">Supported Protocols</h3>
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>REST (JSON, XML)</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>GraphQL</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>SOAP</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>gRPC</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>WebSockets</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Webhooks</span>
                  </li>
                </ul>
              </div>

              <div>
                <h3 className="text-xl font-bold mb-3">Authentication Methods</h3>
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>OAuth 1.0/2.0</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>API Keys</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>JWT</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Basic Auth</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>SAML</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Custom Auth Schemes</span>
                  </li>
                </ul>
              </div>

              <div>
                <h3 className="text-xl font-bold mb-3">Deployment Options</h3>
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Cloud-hosted (SaaS)</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>On-premises</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Hybrid</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Private Cloud</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Kubernetes</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Docker</span>
                  </li>
                </ul>
              </div>

              <div>
                <h3 className="text-xl font-bold mb-3">Performance Metrics</h3>
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Data Normalization: &lt;100ms</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Throughput: 10,000+ TPS</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Latency: &lt;5ms average</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Availability: 99.99%</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Horizontal Scaling: Unlimited</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Concurrent Connections: 100,000+</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="bg-gradient-to-r from-blue-900 to-purple-900 p-8 rounded-lg shadow-lg border border-blue-400">
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-4">Ready to Experience the Universal API Connector?</h2>
            <p className="mb-6">
              See the power of 100ms data normalization and bidirectional compliance control in action.
            </p>
            <div className="flex flex-wrap justify-center gap-4">
              <Link href="/uac-demo" className="bg-gradient-to-r from-blue-700 to-purple-700 text-white px-6 py-3 rounded-lg font-bold hover:from-blue-800 hover:to-purple-800 inline-block shadow-md hover:shadow-lg transform hover:-translate-y-1 transition-all duration-200 border border-blue-400">
                See the UAC Demo
              </Link>
              <Link href="/partner-empowerment" className="bg-white text-blue-700 px-6 py-3 rounded-lg font-bold hover:bg-gray-100 inline-block shadow-md hover:shadow-lg transform hover:-translate-y-1 transition-all duration-200 border border-blue-400">
                Partner With Us
              </Link>
            </div>
          </div>
        </div>
      </div>
    </PageWithSidebar>
  );
};

export default NovaConnectUAC;
