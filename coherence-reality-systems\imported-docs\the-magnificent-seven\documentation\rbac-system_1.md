# NovaConnect Role-Based Access Control (RBAC) System

## Overview

The NovaConnect RBAC system provides comprehensive role-based access control for the platform. It allows administrators to define roles, assign permissions to roles, and assign roles to users. The system is designed to be flexible, scalable, and easy to use.

## Key Components

### 1. Roles

Roles are collections of permissions that can be assigned to users. Each role has a name, description, and a set of permissions. Roles can be system-defined or custom-defined.

System-defined roles include:
- **Administrator**: Full access to all resources
- **Manager**: Access to most resources, but cannot manage users or system settings
- **User**: Access to connectors, data normalization, and workflows
- **Viewer**: Read-only access to connectors, data normalization, and workflows

### 2. Permissions

Permissions define what actions a user can perform on a specific resource. Each permission has a resource (e.g., "connector") and an action (e.g., "view"). Permissions can be combined to create roles.

Permission format: `resource:action`

Examples:
- `connector:view`: Permission to view connectors
- `connector:create`: Permission to create connectors
- `workflow:execute`: Permission to execute workflows

Special permission formats:
- `*`: Wildcard permission that grants access to all resources and actions
- `resource:*`: Wildcard permission that grants access to all actions on a specific resource

### 3. User Roles

User roles associate users with roles. A user can have multiple roles, and roles can be assigned to multiple users. User roles can also have a scope (e.g., "global", "team", "project") and a scope ID.

## API Endpoints

### Role Endpoints

- `GET /api/rbac/roles`: Get all roles
- `GET /api/rbac/roles/:id`: Get role by ID
- `POST /api/rbac/roles`: Create a new role
- `PUT /api/rbac/roles/:id`: Update a role
- `DELETE /api/rbac/roles/:id`: Delete a role

### Permission Endpoints

- `GET /api/rbac/permissions`: Get all permissions
- `GET /api/rbac/permissions/:id`: Get permission by ID
- `POST /api/rbac/permissions`: Create a new permission
- `PUT /api/rbac/permissions/:id`: Update a permission
- `DELETE /api/rbac/permissions/:id`: Delete a permission

### User Role Endpoints

- `GET /api/rbac/users/:userId/roles`: Get roles for a user
- `POST /api/rbac/users/:userId/roles`: Assign role to user
- `DELETE /api/rbac/users/:userId/roles/:roleId`: Remove role from user
- `GET /api/rbac/users/:userId/permissions`: Get permissions for a user
- `GET /api/rbac/users/me/roles`: Get roles for the current user
- `GET /api/rbac/users/me/permissions`: Get permissions for the current user

## Usage Examples

### Creating a Role

```javascript
const role = {
  name: 'Connector Manager',
  description: 'Can manage connectors but not workflows',
  permissions: [
    'connector:view',
    'connector:create',
    'connector:edit',
    'connector:delete',
    'workflow:view'
  ]
};

const response = await api.post('/api/rbac/roles', role);
```

### Assigning a Role to a User

```javascript
const response = await api.post(`/api/rbac/users/${userId}/roles`, {
  roleId: roleId,
  scope: 'team',
  scopeId: teamId
});
```

### Checking if a User Has a Permission

```javascript
const response = await api.get(`/api/rbac/users/${userId}/permissions/connector:view`);
const hasPermission = response.data.hasPermission;
```

## RBAC Middleware

The RBAC system includes middleware for protecting API endpoints:

```javascript
const { hasPermission, hasRole } = require('../middleware/rbacMiddleware');

// Protect route with permission check
router.get('/connectors', hasPermission('connector:view'), connectorController.getAllConnectors);

// Protect route with role check
router.post('/connectors', hasRole('Administrator'), connectorController.createConnector);

// Protect route with multiple permissions (any one is sufficient)
router.put('/connectors/:id', hasPermission(['connector:edit', 'connector:admin']), connectorController.updateConnector);
```

## Performance Considerations

The RBAC system uses caching to improve performance:

1. **Permission Caching**: Permission checks are cached to reduce database queries.
2. **Role Caching**: User roles are cached to reduce database queries.
3. **Cache Invalidation**: Caches are invalidated when roles, permissions, or user roles change.

## Security Considerations

1. **Principle of Least Privilege**: Users should be assigned the minimum permissions necessary to perform their tasks.
2. **Role Separation**: Separate administrative roles from regular user roles.
3. **Audit Logging**: All RBAC-related operations are logged for audit purposes.

## Best Practices

1. **Use System Roles**: Use system-defined roles when possible to ensure consistency.
2. **Custom Roles**: Create custom roles for specific use cases rather than modifying system roles.
3. **Role Naming**: Use clear, descriptive names for roles and permissions.
4. **Permission Granularity**: Define permissions at an appropriate level of granularity.
5. **Regular Review**: Regularly review and audit role assignments and permissions.

## Troubleshooting

### Common Issues

1. **Permission Denied**: User does not have the required permission. Check user roles and permissions.
2. **Role Not Found**: Role does not exist or has been deleted. Check role ID.
3. **Permission Not Found**: Permission does not exist or has been deleted. Check permission format.
4. **Cache Inconsistency**: Cache may be out of sync with database. Try clearing the cache.

### Debugging

1. **Check User Roles**: Use the `/api/rbac/users/:userId/roles` endpoint to check user roles.
2. **Check User Permissions**: Use the `/api/rbac/users/:userId/permissions` endpoint to check user permissions.
3. **Check Role Permissions**: Use the `/api/rbac/roles/:id` endpoint to check role permissions.
4. **Check Audit Logs**: Check audit logs for RBAC-related operations.

## Audit Logging

All RBAC-related operations are logged for audit purposes. Audit logs include:

- User ID
- Action (CREATE, UPDATE, DELETE, ASSIGN_ROLE, REMOVE_ROLE)
- Resource Type (role, permission, user_role)
- Resource ID
- Details (role name, permission name, etc.)
- Timestamp
- IP Address
- User Agent

Audit logs can be viewed in the RBAC Manager UI or accessed via the API.
