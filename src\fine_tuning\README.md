# Fine-Tuning for Universal Compliance Intelligence Architecture (UCIA)

This directory contains scripts for fine-tuning language models for compliance-specific tasks. These fine-tuned models power the UCIA's ability to understand and respond to compliance queries across multiple regulatory frameworks.

## Overview

The fine-tuning process consists of three main steps:

1. **Data Preparation**: Generate question-answer pairs from regulatory requirements
2. **Fine-Tuning**: Train a pre-trained language model on the compliance data
3. **Evaluation**: Assess the performance of the fine-tuned model

## Requirements

- Python 3.8+
- PyTorch
- Transformers
- CUDA-capable GPU (recommended for faster training)

## Installation

```bash
pip install torch transformers
```

## Usage

### Running the Complete Pipeline

The easiest way to run the entire fine-tuning process is to use the `run_pipeline.py` script:

```bash
python run_pipeline.py --model_name gpt2 --data_dir ../ucia/data --output_dir ./runs
```

This will:
1. Prepare the data from the UCIA data directory
2. Fine-tune the model
3. Evaluate the model
4. Save all outputs to a timestamped directory in `./runs`

### Running Individual Steps

You can also run each step of the pipeline individually:

#### 1. Data Preparation

```bash
python prepare_data.py --data_dir ../ucia/data --output_dir ./data
```

This will generate training and validation data from the regulatory requirements in the UCIA data directory.

#### 2. Fine-Tuning

```bash
python fine_tune.py --model_name gpt2 --train_file ./data/train.jsonl --val_file ./data/val.jsonl --output_dir ./fine_tuned_model
```

This will fine-tune the specified model on the prepared data.

#### 3. Evaluation

```bash
python evaluate_model.py --model_path ./fine_tuned_model --test_file ./data/val.jsonl --output_file ./evaluation_results.json
```

This will evaluate the fine-tuned model on the validation data and save the results.

## Parameters

### Data Preparation

- `--data_dir`: Path to the UCIA data directory
- `--output_dir`: Directory to save the prepared data
- `--output_format`: Format of the output data (`jsonl` or `chat`)
- `--train_split`: Proportion of data to use for training (default: 0.8)

### Fine-Tuning

- `--model_name`: Name or path of the pre-trained model
- `--train_file`: Path to the training data file
- `--val_file`: Path to the validation data file
- `--output_dir`: Directory to save the fine-tuned model
- `--num_train_epochs`: Number of training epochs
- `--per_device_train_batch_size`: Batch size per device during training
- `--learning_rate`: Learning rate
- `--block_size`: Maximum sequence length
- `--fp16`: Whether to use 16-bit precision
- `--no_gpu`: Disable GPU usage even if available

### Evaluation

- `--model_path`: Path to the fine-tuned model
- `--test_file`: Path to the test file
- `--output_file`: Path to save the evaluation results
- `--max_length`: Maximum length of the generated responses
- `--no_gpu`: Disable GPU usage even if available

## Using the Fine-Tuned Model in UCIA

To use the fine-tuned model in the UCIA, update the `model_name` parameter in the `CoreComplianceEngine` initialization:

```python
from ucia import UCIA

# Initialize UCIA with the fine-tuned model
ucia = UCIA(model_name="path/to/fine_tuned_model")

# Process a query
response = ucia.process_query("What are the GDPR requirements for data breach notification?")
```

## Recommended Models

For best results, we recommend starting with one of the following base models:

- `gpt2-medium`: A good balance of size and performance
- `EleutherAI/gpt-neo-1.3B`: Larger model with better performance
- `microsoft/DialoGPT-medium`: Good for conversational responses

## Advanced Usage

### Custom Data

You can create custom compliance data by adding new requirements to the UCIA data directory or by creating your own data files in JSONL format.

### Multi-Framework Training

The data preparation script automatically generates cross-framework questions and answers, helping the model understand relationships between different regulatory frameworks.

### Continuous Learning

You can periodically re-train the model with new regulatory requirements to keep it up-to-date with the latest compliance standards.
