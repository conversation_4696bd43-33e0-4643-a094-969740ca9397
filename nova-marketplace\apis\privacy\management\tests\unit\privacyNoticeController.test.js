/**
 * Privacy Notice Controller Tests
 * 
 * This file contains unit tests for the Privacy Notice controller.
 */

// Mock the services module before importing the controller
jest.mock('../../services', () => {
  // Create mock service methods
  const mockPrivacyNoticeService = {
    getAllPrivacyNotices: jest.fn().mockResolvedValue({
      data: [{ _id: 'mock-id-1', title: 'Test Notice', audience: 'customers' }],
      pagination: {
        total: 1,
        page: 1,
        limit: 10,
        pages: 1
      }
    }),
    getPrivacyNoticeById: jest.fn().mockResolvedValue({ _id: 'mock-id-1', title: 'Test Notice', audience: 'customers' }),
    getLatestPrivacyNotice: jest.fn().mockResolvedValue({ _id: 'mock-id-1', title: 'Test Notice', audience: 'customers', version: 2 }),
    createPrivacyNotice: jest.fn().mockResolvedValue({ _id: 'mock-id-1', title: 'Test Notice', audience: 'customers' }),
    updatePrivacyNotice: jest.fn().mockResolvedValue({ _id: 'mock-id-1', title: 'Updated Notice', audience: 'customers' }),
    deletePrivacyNotice: jest.fn().mockResolvedValue({ _id: 'mock-id-1' }),
    archivePrivacyNotice: jest.fn().mockResolvedValue({ _id: 'mock-id-1', status: 'archived' }),
    comparePrivacyNotices: jest.fn().mockResolvedValue({
      notice1: {
        id: 'mock-id-1',
        version: 1,
        status: 'archived'
      },
      notice2: {
        id: 'mock-id-2',
        version: 2,
        status: 'active'
      },
      differences: []
    })
  };
  
  return {
    privacyNoticeService: mockPrivacyNoticeService
  };
});

// Now import the controller and mocked service
const { privacyNoticeController } = require('../../controllers');
const { privacyNoticeService } = require('../../services');

describe('Privacy Notice Controller', () => {
  // Setup request, response, and next function for testing
  let req;
  let res;
  let next;
  
  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();
    
    // Setup request object
    req = {
      params: {},
      query: {},
      body: {},
      user: {
        id: 'user-123'
      }
    };
    
    // Setup response object
    res = {
      json: jest.fn(),
      status: jest.fn().mockReturnThis()
    };
    
    // Setup next function
    next = jest.fn();
  });
  
  describe('getAllPrivacyNotices', () => {
    it('should return all privacy notices with pagination', async () => {
      // Setup request query
      req.query = {
        page: '1',
        limit: '10',
        status: 'active',
        audience: 'customers',
        language: 'en',
        version: '1',
        search: 'test',
        sortBy: 'effectiveDate',
        sortOrder: 'desc'
      };
      
      // Call the controller method
      await privacyNoticeController.getAllPrivacyNotices(req, res, next);
      
      // Verify the service method was called with the correct parameters
      expect(privacyNoticeService.getAllPrivacyNotices).toHaveBeenCalledWith(expect.objectContaining({
        page: 1,
        limit: 10,
        filter: expect.objectContaining({
          status: 'active',
          audience: 'customers',
          language: 'en',
          version: '1',
          $text: { $search: 'test' }
        }),
        sort: expect.objectContaining({
          effectiveDate: -1
        })
      }));
      
      // Verify the response
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.any(Array),
        pagination: expect.objectContaining({
          total: 1,
          page: 1,
          limit: 10,
          pages: 1
        })
      }));
    });
    
    it('should use default values if query parameters are not provided', async () => {
      // Call the controller method with empty query
      await privacyNoticeController.getAllPrivacyNotices(req, res, next);
      
      // Verify the service method was called with default values
      expect(privacyNoticeService.getAllPrivacyNotices).toHaveBeenCalledWith(expect.objectContaining({
        page: 1,
        limit: 10,
        filter: expect.any(Object),
        sort: expect.objectContaining({
          effectiveDate: -1
        })
      }));
    });
    
    it('should handle errors', async () => {
      // Setup error
      const error = new Error('Service error');
      privacyNoticeService.getAllPrivacyNotices.mockRejectedValueOnce(error);
      
      // Call the controller method
      await privacyNoticeController.getAllPrivacyNotices(req, res, next);
      
      // Verify error handling
      expect(next).toHaveBeenCalledWith(error);
    });
  });
  
  describe('getPrivacyNoticeById', () => {
    it('should return a privacy notice by ID', async () => {
      // Setup request params
      req.params.id = 'mock-id-1';
      
      // Call the controller method
      await privacyNoticeController.getPrivacyNoticeById(req, res, next);
      
      // Verify the service method was called with the correct parameters
      expect(privacyNoticeService.getPrivacyNoticeById).toHaveBeenCalledWith('mock-id-1');
      
      // Verify the response
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({ _id: 'mock-id-1' })
      }));
    });
    
    it('should handle NotFoundError', async () => {
      // Setup request params
      req.params.id = 'non-existent-id';
      
      // Setup service to throw NotFoundError
      const notFoundError = new Error('Privacy notice not found');
      notFoundError.name = 'NotFoundError';
      privacyNoticeService.getPrivacyNoticeById.mockRejectedValueOnce(notFoundError);
      
      // Call the controller method
      await privacyNoticeController.getPrivacyNoticeById(req, res, next);
      
      // Verify error handling
      expect(next).toHaveBeenCalledWith(expect.objectContaining({
        name: 'NotFoundError',
        message: 'Privacy notice not found'
      }));
    });
    
    it('should handle other errors', async () => {
      // Setup request params
      req.params.id = 'mock-id-1';
      
      // Setup error
      const error = new Error('Service error');
      privacyNoticeService.getPrivacyNoticeById.mockRejectedValueOnce(error);
      
      // Call the controller method
      await privacyNoticeController.getPrivacyNoticeById(req, res, next);
      
      // Verify error handling
      expect(next).toHaveBeenCalledWith(error);
    });
  });
  
  describe('createPrivacyNotice', () => {
    it('should create a new privacy notice', async () => {
      // Setup request body
      req.body = {
        title: 'Test Notice',
        audience: 'customers',
        language: 'en',
        content: 'This is a test privacy notice'
      };
      
      // Call the controller method
      await privacyNoticeController.createPrivacyNotice(req, res, next);
      
      // Verify the service method was called with the correct parameters
      expect(privacyNoticeService.createPrivacyNotice).toHaveBeenCalledWith(expect.objectContaining({
        title: 'Test Notice',
        audience: 'customers',
        language: 'en',
        content: 'This is a test privacy notice',
        revisionHistory: expect.arrayContaining([
          expect.objectContaining({
            version: expect.any(Number),
            date: expect.any(Date),
            changes: 'Initial version',
            changedBy: 'user-123'
          })
        ])
      }));
      
      // Verify the response
      expect(res.status).toHaveBeenCalledWith(201);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({ _id: 'mock-id-1' }),
        message: 'Privacy notice created successfully'
      }));
    });
    
    it('should handle validation errors', async () => {
      // Setup request body
      req.body = {
        // Missing required fields
      };
      
      // Setup error
      const validationError = new Error('Validation error');
      validationError.name = 'ValidationError';
      privacyNoticeService.createPrivacyNotice.mockRejectedValueOnce(validationError);
      
      // Call the controller method
      await privacyNoticeController.createPrivacyNotice(req, res, next);
      
      // Verify error handling
      expect(next).toHaveBeenCalledWith(validationError);
    });
    
    it('should handle other errors', async () => {
      // Setup request body
      req.body = {
        title: 'Test Notice',
        audience: 'customers'
      };
      
      // Setup error
      const error = new Error('Service error');
      privacyNoticeService.createPrivacyNotice.mockRejectedValueOnce(error);
      
      // Call the controller method
      await privacyNoticeController.createPrivacyNotice(req, res, next);
      
      // Verify error handling
      expect(next).toHaveBeenCalledWith(error);
    });
  });
  
  describe('updatePrivacyNotice', () => {
    it('should update a privacy notice', async () => {
      // Setup request params and body
      req.params.id = 'mock-id-1';
      req.body = {
        title: 'Updated Notice',
        version: 2,
        changes: 'Updated title'
      };
      
      // Mock the current notice
      privacyNoticeService.getPrivacyNoticeById.mockResolvedValueOnce({
        _id: 'mock-id-1',
        title: 'Test Notice',
        version: 1,
        revisionHistory: [
          {
            version: 1,
            date: new Date(),
            changes: 'Initial version',
            changedBy: 'user-123'
          }
        ]
      });
      
      // Call the controller method
      await privacyNoticeController.updatePrivacyNotice(req, res, next);
      
      // Verify the service method was called with the correct parameters
      expect(privacyNoticeService.updatePrivacyNotice).toHaveBeenCalledWith('mock-id-1', expect.objectContaining({
        title: 'Updated Notice',
        version: 2,
        changes: 'Updated title',
        revisionHistory: expect.arrayContaining([
          expect.any(Object),
          expect.objectContaining({
            version: 2,
            changes: 'Updated title',
            changedBy: 'user-123'
          })
        ])
      }));
      
      // Verify the response
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({
          _id: 'mock-id-1',
          title: 'Updated Notice'
        }),
        message: 'Privacy notice updated successfully'
      }));
    });
    
    it('should handle NotFoundError', async () => {
      // Setup request params and body
      req.params.id = 'non-existent-id';
      req.body = {
        title: 'Updated Notice'
      };
      
      // Setup service to throw NotFoundError
      const notFoundError = new Error('Privacy notice not found');
      notFoundError.name = 'NotFoundError';
      privacyNoticeService.getPrivacyNoticeById.mockRejectedValueOnce(notFoundError);
      
      // Call the controller method
      await privacyNoticeController.updatePrivacyNotice(req, res, next);
      
      // Verify error handling
      expect(next).toHaveBeenCalledWith(expect.objectContaining({
        name: 'NotFoundError',
        message: 'Privacy notice not found'
      }));
    });
    
    it('should handle validation errors', async () => {
      // Setup request params and body
      req.params.id = 'mock-id-1';
      req.body = {
        status: 'invalid-status'
      };
      
      // Mock the current notice
      privacyNoticeService.getPrivacyNoticeById.mockResolvedValueOnce({
        _id: 'mock-id-1',
        title: 'Test Notice',
        version: 1,
        revisionHistory: []
      });
      
      // Setup error
      const validationError = new Error('Validation error');
      validationError.name = 'ValidationError';
      privacyNoticeService.updatePrivacyNotice.mockRejectedValueOnce(validationError);
      
      // Call the controller method
      await privacyNoticeController.updatePrivacyNotice(req, res, next);
      
      // Verify error handling
      expect(next).toHaveBeenCalledWith(validationError);
    });
    
    it('should handle other errors', async () => {
      // Setup request params and body
      req.params.id = 'mock-id-1';
      req.body = {
        title: 'Updated Notice'
      };
      
      // Mock the current notice
      privacyNoticeService.getPrivacyNoticeById.mockResolvedValueOnce({
        _id: 'mock-id-1',
        title: 'Test Notice',
        version: 1,
        revisionHistory: []
      });
      
      // Setup error
      const error = new Error('Service error');
      privacyNoticeService.updatePrivacyNotice.mockRejectedValueOnce(error);
      
      // Call the controller method
      await privacyNoticeController.updatePrivacyNotice(req, res, next);
      
      // Verify error handling
      expect(next).toHaveBeenCalledWith(error);
    });
  });
  
  describe('deletePrivacyNotice', () => {
    it('should delete a privacy notice', async () => {
      // Setup request params
      req.params.id = 'mock-id-1';
      
      // Call the controller method
      await privacyNoticeController.deletePrivacyNotice(req, res, next);
      
      // Verify the service method was called with the correct parameters
      expect(privacyNoticeService.deletePrivacyNotice).toHaveBeenCalledWith('mock-id-1');
      
      // Verify the response
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        message: 'Privacy notice deleted successfully'
      }));
    });
    
    it('should handle NotFoundError', async () => {
      // Setup request params
      req.params.id = 'non-existent-id';
      
      // Setup service to throw NotFoundError
      const notFoundError = new Error('Privacy notice not found');
      notFoundError.name = 'NotFoundError';
      privacyNoticeService.deletePrivacyNotice.mockRejectedValueOnce(notFoundError);
      
      // Call the controller method
      await privacyNoticeController.deletePrivacyNotice(req, res, next);
      
      // Verify error handling
      expect(next).toHaveBeenCalledWith(expect.objectContaining({
        name: 'NotFoundError',
        message: 'Privacy notice not found'
      }));
    });
    
    it('should handle validation errors', async () => {
      // Setup request params
      req.params.id = 'mock-id-1';
      
      // Setup service to throw ValidationError
      const validationError = new Error('Cannot delete a published privacy notice');
      validationError.name = 'ValidationError';
      privacyNoticeService.deletePrivacyNotice.mockRejectedValueOnce(validationError);
      
      // Call the controller method
      await privacyNoticeController.deletePrivacyNotice(req, res, next);
      
      // Verify error handling
      expect(next).toHaveBeenCalledWith(expect.objectContaining({
        name: 'ValidationError',
        message: 'Cannot delete a published privacy notice'
      }));
    });
    
    it('should handle other errors', async () => {
      // Setup request params
      req.params.id = 'mock-id-1';
      
      // Setup error
      const error = new Error('Service error');
      privacyNoticeService.deletePrivacyNotice.mockRejectedValueOnce(error);
      
      // Call the controller method
      await privacyNoticeController.deletePrivacyNotice(req, res, next);
      
      // Verify error handling
      expect(next).toHaveBeenCalledWith(error);
    });
  });
  
  describe('archivePrivacyNotice', () => {
    it('should archive a privacy notice', async () => {
      // Setup request params
      req.params.id = 'mock-id-1';
      
      // Call the controller method
      await privacyNoticeController.archivePrivacyNotice(req, res, next);
      
      // Verify the service method was called with the correct parameters
      expect(privacyNoticeService.archivePrivacyNotice).toHaveBeenCalledWith('mock-id-1');
      
      // Verify the response
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({
          _id: 'mock-id-1',
          status: 'archived'
        }),
        message: 'Privacy notice archived successfully'
      }));
    });
    
    it('should handle NotFoundError', async () => {
      // Setup request params
      req.params.id = 'non-existent-id';
      
      // Setup service to throw NotFoundError
      const notFoundError = new Error('Privacy notice not found');
      notFoundError.name = 'NotFoundError';
      privacyNoticeService.archivePrivacyNotice.mockRejectedValueOnce(notFoundError);
      
      // Call the controller method
      await privacyNoticeController.archivePrivacyNotice(req, res, next);
      
      // Verify error handling
      expect(next).toHaveBeenCalledWith(expect.objectContaining({
        name: 'NotFoundError',
        message: 'Privacy notice not found'
      }));
    });
    
    it('should handle validation errors', async () => {
      // Setup request params
      req.params.id = 'mock-id-1';
      
      // Setup service to throw ValidationError
      const validationError = new Error('Only published privacy notices can be archived');
      validationError.name = 'ValidationError';
      privacyNoticeService.archivePrivacyNotice.mockRejectedValueOnce(validationError);
      
      // Call the controller method
      await privacyNoticeController.archivePrivacyNotice(req, res, next);
      
      // Verify error handling
      expect(next).toHaveBeenCalledWith(expect.objectContaining({
        name: 'ValidationError',
        message: 'Only published privacy notices can be archived'
      }));
    });
    
    it('should handle other errors', async () => {
      // Setup request params
      req.params.id = 'mock-id-1';
      
      // Setup error
      const error = new Error('Service error');
      privacyNoticeService.archivePrivacyNotice.mockRejectedValueOnce(error);
      
      // Call the controller method
      await privacyNoticeController.archivePrivacyNotice(req, res, next);
      
      // Verify error handling
      expect(next).toHaveBeenCalledWith(error);
    });
  });
  
  describe('getPrivacyNoticesByAudience', () => {
    it('should return privacy notices by audience', async () => {
      // Setup request params
      req.params.audience = 'customers';
      
      // Call the controller method
      await privacyNoticeController.getPrivacyNoticesByAudience(req, res, next);
      
      // Verify the service method was called with the correct parameters
      expect(privacyNoticeService.getAllPrivacyNotices).toHaveBeenCalledWith(expect.objectContaining({
        filter: expect.objectContaining({
          audience: 'customers',
          status: 'active'
        }),
        sort: expect.objectContaining({
          effectiveDate: -1
        })
      }));
      
      // Verify the response
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.any(Array)
      }));
    });
    
    it('should handle errors', async () => {
      // Setup request params
      req.params.audience = 'customers';
      
      // Setup error
      const error = new Error('Service error');
      privacyNoticeService.getAllPrivacyNotices.mockRejectedValueOnce(error);
      
      // Call the controller method
      await privacyNoticeController.getPrivacyNoticesByAudience(req, res, next);
      
      // Verify error handling
      expect(next).toHaveBeenCalledWith(error);
    });
  });
  
  describe('getLatestPrivacyNotice', () => {
    it('should return the latest privacy notice', async () => {
      // Setup request query
      req.query = {
        type: 'privacy-policy',
        audience: 'customers',
        language: 'en'
      };
      
      // Call the controller method
      await privacyNoticeController.getLatestPrivacyNotice(req, res, next);
      
      // Verify the service method was called with the correct parameters
      expect(privacyNoticeService.getLatestPrivacyNotice).toHaveBeenCalledWith('privacy-policy', 'customers', 'en');
      
      // Verify the response
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({
          _id: 'mock-id-1',
          audience: 'customers',
          version: 2
        })
      }));
    });
    
    it('should handle missing audience parameter', async () => {
      // Setup request query with missing audience
      req.query = {
        type: 'privacy-policy'
      };
      
      // Call the controller method
      await privacyNoticeController.getLatestPrivacyNotice(req, res, next);
      
      // Verify error handling
      expect(next).toHaveBeenCalledWith(expect.objectContaining({
        name: 'ValidationError',
        message: 'Audience is required'
      }));
    });
    
    it('should handle missing type parameter', async () => {
      // Setup request query with missing type
      req.query = {
        audience: 'customers'
      };
      
      // Call the controller method
      await privacyNoticeController.getLatestPrivacyNotice(req, res, next);
      
      // Verify error handling
      expect(next).toHaveBeenCalledWith(expect.objectContaining({
        name: 'ValidationError',
        message: 'Type is required'
      }));
    });
    
    it('should handle NotFoundError', async () => {
      // Setup request query
      req.query = {
        type: 'privacy-policy',
        audience: 'non-existent-audience',
        language: 'en'
      };
      
      // Setup service to throw NotFoundError
      const notFoundError = new Error('Privacy notice not found');
      notFoundError.name = 'NotFoundError';
      privacyNoticeService.getLatestPrivacyNotice.mockRejectedValueOnce(notFoundError);
      
      // Call the controller method
      await privacyNoticeController.getLatestPrivacyNotice(req, res, next);
      
      // Verify error handling
      expect(next).toHaveBeenCalledWith(expect.objectContaining({
        name: 'NotFoundError',
        message: 'Privacy notice not found'
      }));
    });
    
    it('should handle other errors', async () => {
      // Setup request query
      req.query = {
        type: 'privacy-policy',
        audience: 'customers',
        language: 'en'
      };
      
      // Setup error
      const error = new Error('Service error');
      privacyNoticeService.getLatestPrivacyNotice.mockRejectedValueOnce(error);
      
      // Call the controller method
      await privacyNoticeController.getLatestPrivacyNotice(req, res, next);
      
      // Verify error handling
      expect(next).toHaveBeenCalledWith(error);
    });
  });
  
  describe('comparePrivacyNoticeVersions', () => {
    it('should compare two privacy notice versions', async () => {
      // Setup request query
      req.query = {
        id1: 'mock-id-1',
        id2: 'mock-id-2'
      };
      
      // Call the controller method
      await privacyNoticeController.comparePrivacyNoticeVersions(req, res, next);
      
      // Verify the service method was called with the correct parameters
      expect(privacyNoticeService.comparePrivacyNotices).toHaveBeenCalledWith('mock-id-1', 'mock-id-2');
      
      // Verify the response
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({
          notice1: expect.objectContaining({
            id: 'mock-id-1',
            version: 1
          }),
          notice2: expect.objectContaining({
            id: 'mock-id-2',
            version: 2
          })
        }),
        message: 'Privacy notice versions compared successfully'
      }));
    });
    
    it('should handle missing ID parameters', async () => {
      // Setup request query with missing IDs
      req.query = {
        id1: 'mock-id-1'
        // Missing id2
      };
      
      // Call the controller method
      await privacyNoticeController.comparePrivacyNoticeVersions(req, res, next);
      
      // Verify error handling
      expect(next).toHaveBeenCalledWith(expect.objectContaining({
        name: 'ValidationError',
        message: 'Both privacy notice IDs are required'
      }));
    });
    
    it('should handle NotFoundError', async () => {
      // Setup request query
      req.query = {
        id1: 'mock-id-1',
        id2: 'non-existent-id'
      };
      
      // Setup service to throw NotFoundError
      const notFoundError = new Error('One or both privacy notice versions not found');
      notFoundError.name = 'NotFoundError';
      privacyNoticeService.comparePrivacyNotices.mockRejectedValueOnce(notFoundError);
      
      // Call the controller method
      await privacyNoticeController.comparePrivacyNoticeVersions(req, res, next);
      
      // Verify error handling
      expect(next).toHaveBeenCalledWith(expect.objectContaining({
        name: 'NotFoundError',
        message: 'One or both privacy notice versions not found'
      }));
    });
    
    it('should handle validation errors', async () => {
      // Setup request query
      req.query = {
        id1: 'mock-id-1',
        id2: 'mock-id-2'
      };
      
      // Setup service to throw ValidationError
      const validationError = new Error('Cannot compare privacy notices of different types or audiences');
      validationError.name = 'ValidationError';
      privacyNoticeService.comparePrivacyNotices.mockRejectedValueOnce(validationError);
      
      // Call the controller method
      await privacyNoticeController.comparePrivacyNoticeVersions(req, res, next);
      
      // Verify error handling
      expect(next).toHaveBeenCalledWith(expect.objectContaining({
        name: 'ValidationError',
        message: 'Cannot compare privacy notices of different types or audiences'
      }));
    });
    
    it('should handle other errors', async () => {
      // Setup request query
      req.query = {
        id1: 'mock-id-1',
        id2: 'mock-id-2'
      };
      
      // Setup error
      const error = new Error('Service error');
      privacyNoticeService.comparePrivacyNotices.mockRejectedValueOnce(error);
      
      // Call the controller method
      await privacyNoticeController.comparePrivacyNoticeVersions(req, res, next);
      
      // Verify error handling
      expect(next).toHaveBeenCalledWith(error);
    });
  });
});
