{"validationResults": {"validator": {"options": {"strictMode": false, "logValidation": true, "resonanceLock": true}}, "testValues": [0.3, 0.6, 0.9, 0.03, 0.06, 0.09, 0.12, 0.13, 3, 6, 9, 12, 0.7, 0.8, 0.4, 0.07], "results": [{"isValid": true, "originalValue": 0.3, "harmonizedValue": 0.3, "isResonant": true, "type": "generic"}, {"isValid": true, "originalValue": 0.6, "harmonizedValue": 0.6, "isResonant": true, "type": "generic"}, {"isValid": true, "originalValue": 0.9, "harmonizedValue": 0.9, "isResonant": true, "type": "generic"}, {"isValid": true, "originalValue": 0.03, "harmonizedValue": 0.03, "isResonant": true, "type": "generic"}, {"isValid": true, "originalValue": 0.06, "harmonizedValue": 0.06, "isResonant": true, "type": "generic"}, {"isValid": true, "originalValue": 0.09, "harmonizedValue": 0.09, "isResonant": true, "type": "generic"}, {"isValid": true, "originalValue": 0.12, "harmonizedValue": 0.12, "isResonant": true, "type": "generic"}, {"isValid": true, "originalValue": 0.13, "harmonizedValue": 0.111, "isResonant": false, "wasHarmonized": true, "type": "generic", "resonanceDrift": 0.019000000000000003}, {"isValid": true, "originalValue": 3, "harmonizedValue": 3, "isResonant": true, "type": "generic"}, {"isValid": true, "originalValue": 6, "harmonizedValue": 6, "isResonant": true, "type": "generic"}, {"isValid": true, "originalValue": 9, "harmonizedValue": 9, "isResonant": true, "type": "generic"}, {"isValid": true, "originalValue": 12, "harmonizedValue": 12, "isResonant": true, "type": "generic"}, {"isValid": true, "originalValue": 0.7, "harmonizedValue": 0.699, "isResonant": false, "wasHarmonized": true, "type": "generic", "resonanceDrift": 0.0010000000000000009}, {"isValid": true, "originalValue": 0.8, "harmonizedValue": 0.801, "isResonant": false, "wasHarmonized": true, "type": "generic", "resonanceDrift": 0.0010000000000000009}, {"isValid": true, "originalValue": 0.4, "harmonizedValue": 0.399, "isResonant": false, "wasHarmonized": true, "type": "generic", "resonanceDrift": 0.0010000000000000009}, {"isValid": true, "originalValue": 0.07, "harmonizedValue": 0.069, "isResonant": false, "wasHarmonized": true, "type": "generic", "resonanceDrift": 0.0010000000000000009}], "resonantCount": 11, "harmonizedCount": 5}, "coreResults": {"comphyologyCore": {"options": {"enableLogging": true, "enableCaching": false, "morphologicalWeight": 0.33, "quantumWeight": 0.33, "emergentWeight": 0.34, "resonanceLock": true, "strictMode": false}, "PI": 3.141592653589793, "PHI": 1.618033988749895, "PLANCK_CONSTANT": 6.62607015e-34, "SPEED_OF_LIGHT": 299792458, "SPEED_OF_LIGHT_INV": 3.3356409519815204e-09, "RESONANCE_PATTERN": {"CYCLES": [3, 6, 9, 12], "THRESHOLDS": [0.3, 0.6, 0.9], "DECAY_RATES": [0.03, 0.06, 0.09, 0.12, 0.13], "EFFECTIVENESS_TARGETS": [0.3, 0.6, 0.9]}, "cache": {}, "tensorOperator": {}, "fusionOperator": {}, "resonanceValidator": {"options": {"strictMode": false, "logValidation": true, "resonanceLock": true}}, "resonanceMetrics": {"validations": 11, "harmonizations": 7, "rejections": 0, "totalDrift": 0.*****************, "averageDrift": 0.008857142857142517}}, "systemState": {"structure": {"complexity": 0.6, "adaptability": 0.6, "resilience": 0.6}, "environment": {"volatility": 0.3, "uncertainty": 0.3, "complexity": 0.3, "ambiguity": 0.3}, "quantum": {"entropy": {"value": 0.3, "gradient": 0.1, "threshold": 0.3}, "phase": {"value": 0.6, "coherence": 0.6, "stability": 0.6}}, "ethics": {"fairness": 0.9, "transparency": 0.9, "accountability": 0.9}}, "resultWithLock": {"comphyologyValue": 10.***************, "timestamp": "2025-05-17T05:51:20.963Z", "morphologicalComponent": {"component": "Morphological", "resonance": 0.****************, "adaptationVector": 0.*****************, "transformation": 0.****************, "morphologicalScore": 0.****************, "result": 1.****************}, "quantumComponent": {"component": "Quantum", "phaseSpace": 0.****************, "patterns": 0.***************, "certainty": 0.****************, "quantumScore": 0.****************, "result": 0.****************}, "emergentComponent": {"component": "Emergent", "ethicalTensor": {"value": 0.****************, "dimensions": {"fairness": 0.9, "transparency": 0.9, "accountability": 0.9, "criticality": 0.5, "uncertainty": 0.5, "timePressure": 0.5}, "options": [{"option": "default", "utility": 0.5, "risk": 0.5, "ethicalScore": 0.*****************}]}, "ethicalEvaluation": {"score": 0.****************, "option": "default", "utility": 0.5, "risk": 0.5, "compliance": 0.45, "fairness": 0.45, "transparency": 0.9}, "adjustedDecision": {"adjustmentScore": 0.95, "originalOption": "default", "adjustedOption": "default", "justification": "Decision meets ethical standards", "ethicalScore": 0.****************}, "emergentScore": 0.****************, "result": 8.***************}, "tensorProduct": 1.****************, "fusionResult": 10.***************, "processingTime": 3.****************, "resonance": {"isResonant": true, "wasHarmonized": true, "resonanceDrift": 0.009999999999999787, "metrics": {"validations": 1, "harmonizations": 1, "rejections": 0, "totalDrift": 0.009999999999999787, "averageDrift": 0.009999999999999787}}}, "resultWithoutLock": {"comphyologyValue": 10.***************, "timestamp": "2025-05-17T05:51:20.966Z", "morphologicalComponent": {"component": "Morphological", "resonance": 0.****************, "adaptationVector": 0.*****************, "transformation": 0.****************, "morphologicalScore": 0.****************, "result": 1.****************}, "quantumComponent": {"component": "Quantum", "phaseSpace": 0.****************, "patterns": 0.***************, "certainty": 0.****************, "quantumScore": 0.****************, "result": 0.****************}, "emergentComponent": {"component": "Emergent", "ethicalTensor": {"value": 0.****************, "dimensions": {"fairness": 0.9, "transparency": 0.9, "accountability": 0.9, "criticality": 0.5, "uncertainty": 0.5, "timePressure": 0.5}, "options": [{"option": "default", "utility": 0.5, "risk": 0.5, "ethicalScore": 0.*****************}]}, "ethicalEvaluation": {"score": 0.****************, "option": "default", "utility": 0.5, "risk": 0.5, "compliance": 0.45, "fairness": 0.45, "transparency": 0.9}, "adjustedDecision": {"adjustmentScore": 0.95, "originalOption": "default", "adjustedOption": "default", "justification": "Decision meets ethical standards", "ethicalScore": 0.****************}, "emergentScore": 0.****************, "result": 8.***************}, "tensorProduct": 1.****************, "fusionResult": 10.***************, "processingTime": 0.*****************, "resonance": {"isResonant": true, "wasHarmonized": false, "resonanceDrift": 0, "metrics": {"validations": 1, "harmonizations": 1, "rejections": 0, "totalDrift": 0.009999999999999787, "averageDrift": 0.009999999999999787}}}, "variations": [{"variation": 0, "withLock": 10.********8226233, "withoutLock": 10.821569248226233, "difference": 0.0009999999999994458, "isResonant": true, "wasHarmonized": true}, {"variation": 1, "withLock": 10.801024322607537, "withoutLock": 10.802024322607537, "difference": 0.0009999999999994458, "isResonant": true, "wasHarmonized": true}, {"variation": 2, "withLock": 10.782027827463118, "withoutLock": 10.782027827463118, "difference": 0, "isResonant": true, "wasHarmonized": false}, {"variation": 3, "withLock": 10.76158790611422, "withoutLock": 10.76158790611422, "difference": 0, "isResonant": true, "wasHarmonized": false}, {"variation": 4, "withLock": 10.750713074965413, "withoutLock": 10.740713074965413, "difference": 0.009999999999999787, "isResonant": true, "wasHarmonized": true}, {"variation": 5, "withLock": 10.73041221286401, "withoutLock": 10.719412212864011, "difference": 0.010999999999999233, "isResonant": true, "wasHarmonized": true}, {"variation": 6, "withLock": 10.707694550108775, "withoutLock": 10.697694550108775, "difference": 0.009999999999999787, "isResonant": true, "wasHarmonized": true}, {"variation": 7, "withLock": 10.65656965712856, "withoutLock": 10.67556965712856, "difference": 0.019000000000000128, "isResonant": true, "wasHarmonized": true}, {"variation": 8, "withLock": 10.653047432852091, "withoutLock": 10.653047432852091, "difference": 0, "isResonant": true, "wasHarmonized": false}, {"variation": 9, "withLock": 10.626720158860044, "withoutLock": 10.626720158860044, "difference": 0, "isResonant": true, "wasHarmonized": false}], "averageDifference": 0.005199999999999783, "resonantCount": 10, "harmonizedCount": 6}, "reportPath": "D:\\novafuse-api-superstore\\resonance_results\\resonance_lock_demo_report.html"}