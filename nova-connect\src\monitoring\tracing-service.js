/**
 * NovaFuse Universal API Connector - Tracing Service
 * 
 * This module provides a service for distributed tracing.
 */

const { createLogger } = require('../utils/logger');
const { v4: uuidv4 } = require('uuid');

const logger = createLogger('tracing-service');

/**
 * Trace context class for storing trace information
 */
class TraceContext {
  constructor(traceId, spanId, parentSpanId = null, sampled = true) {
    this.traceId = traceId;
    this.spanId = spanId;
    this.parentSpanId = parentSpanId;
    this.sampled = sampled;
    this.startTime = Date.now();
    this.endTime = null;
    this.attributes = {};
    this.events = [];
  }

  /**
   * Add an attribute to the trace context
   * 
   * @param {string} key - The attribute key
   * @param {any} value - The attribute value
   */
  setAttribute(key, value) {
    this.attributes[key] = value;
  }

  /**
   * Add multiple attributes to the trace context
   * 
   * @param {Object} attributes - The attributes to add
   */
  setAttributes(attributes) {
    Object.assign(this.attributes, attributes);
  }

  /**
   * Add an event to the trace context
   * 
   * @param {string} name - The event name
   * @param {Object} attributes - The event attributes
   */
  addEvent(name, attributes = {}) {
    this.events.push({
      name,
      timestamp: Date.now(),
      attributes
    });
  }

  /**
   * End the trace context
   */
  end() {
    this.endTime = Date.now();
  }

  /**
   * Get the duration of the trace context in milliseconds
   * 
   * @returns {number} - The duration in milliseconds
   */
  getDuration() {
    return (this.endTime || Date.now()) - this.startTime;
  }

  /**
   * Convert the trace context to a JSON object
   * 
   * @returns {Object} - The JSON representation
   */
  toJSON() {
    return {
      traceId: this.traceId,
      spanId: this.spanId,
      parentSpanId: this.parentSpanId,
      sampled: this.sampled,
      startTime: this.startTime,
      endTime: this.endTime,
      duration: this.getDuration(),
      attributes: this.attributes,
      events: this.events
    };
  }
}

/**
 * Tracing Service class for distributed tracing
 */
class TracingService {
  constructor() {
    // Initialize trace storage
    this.activeTraces = new Map();
    
    // Initialize trace context storage
    this.asyncLocalStorage = null;
    
    // Initialize sampling rate
    this.samplingRate = parseFloat(process.env.TRACE_SAMPLING_RATE || '1.0');
    
    // Initialize exporters
    this.exporters = [];
    
    logger.info('Tracing service initialized');
  }

  /**
   * Initialize the tracing service
   */
  initialize() {
    try {
      // Try to use AsyncLocalStorage if available
      const { AsyncLocalStorage } = require('async_hooks');
      this.asyncLocalStorage = new AsyncLocalStorage();
      logger.info('AsyncLocalStorage initialized for trace context propagation');
    } catch (error) {
      logger.warn('AsyncLocalStorage not available, falling back to manual context propagation');
    }
  }

  /**
   * Add a trace exporter
   * 
   * @param {Function} exporterFn - The exporter function
   */
  addExporter(exporterFn) {
    this.exporters.push(exporterFn);
    logger.debug('Trace exporter added');
  }

  /**
   * Set the sampling rate
   * 
   * @param {number} rate - The sampling rate (0.0 - 1.0)
   */
  setSamplingRate(rate) {
    this.samplingRate = Math.max(0, Math.min(1, rate));
    logger.info(`Trace sampling rate set to ${this.samplingRate}`);
  }

  /**
   * Determine if a trace should be sampled
   * 
   * @returns {boolean} - Whether the trace should be sampled
   */
  shouldSample() {
    return Math.random() < this.samplingRate;
  }

  /**
   * Start a new trace
   * 
   * @param {string} name - The trace name
   * @param {Object} options - Trace options
   * @param {string} options.traceId - The trace ID (generated if not provided)
   * @param {string} options.parentSpanId - The parent span ID
   * @param {boolean} options.sampled - Whether the trace is sampled
   * @param {Object} options.attributes - Initial attributes
   * @returns {TraceContext} - The trace context
   */
  startTrace(name, options = {}) {
    const traceId = options.traceId || uuidv4();
    const spanId = uuidv4();
    const parentSpanId = options.parentSpanId || null;
    const sampled = options.sampled !== undefined ? options.sampled : this.shouldSample();
    
    const traceContext = new TraceContext(traceId, spanId, parentSpanId, sampled);
    
    // Set trace name
    traceContext.setAttribute('name', name);
    
    // Set initial attributes
    if (options.attributes) {
      traceContext.setAttributes(options.attributes);
    }
    
    // Store trace context
    this.activeTraces.set(spanId, traceContext);
    
    // Set current trace context if AsyncLocalStorage is available
    if (this.asyncLocalStorage) {
      this.asyncLocalStorage.enterWith(traceContext);
    }
    
    logger.debug(`Trace started: ${name} (${traceId}/${spanId})`);
    
    return traceContext;
  }

  /**
   * Start a child span
   * 
   * @param {string} name - The span name
   * @param {Object} options - Span options
   * @param {TraceContext} options.parent - The parent trace context
   * @param {Object} options.attributes - Initial attributes
   * @returns {TraceContext} - The child trace context
   */
  startSpan(name, options = {}) {
    // Get parent trace context
    let parent = options.parent;
    
    if (!parent && this.asyncLocalStorage) {
      parent = this.asyncLocalStorage.getStore();
    }
    
    if (!parent) {
      logger.warn(`No parent trace context found for span: ${name}`);
      return this.startTrace(name, options);
    }
    
    // Create child span
    return this.startTrace(name, {
      traceId: parent.traceId,
      parentSpanId: parent.spanId,
      sampled: parent.sampled,
      attributes: options.attributes
    });
  }

  /**
   * End a trace
   * 
   * @param {TraceContext} traceContext - The trace context
   */
  endTrace(traceContext) {
    if (!traceContext) {
      logger.warn('No trace context provided to endTrace');
      return;
    }
    
    // End trace
    traceContext.end();
    
    // Remove from active traces
    this.activeTraces.delete(traceContext.spanId);
    
    // Export trace if sampled
    if (traceContext.sampled) {
      this._exportTrace(traceContext);
    }
    
    logger.debug(`Trace ended: ${traceContext.attributes.name} (${traceContext.traceId}/${traceContext.spanId})`);
  }

  /**
   * Get the current trace context
   * 
   * @returns {TraceContext|null} - The current trace context
   */
  getCurrentTraceContext() {
    if (this.asyncLocalStorage) {
      return this.asyncLocalStorage.getStore();
    }
    
    return null;
  }

  /**
   * Export a trace
   * 
   * @param {TraceContext} traceContext - The trace context
   * @private
   */
  _exportTrace(traceContext) {
    for (const exporter of this.exporters) {
      try {
        exporter(traceContext);
      } catch (error) {
        logger.error('Error exporting trace:', { error });
      }
    }
  }

  /**
   * Create trace headers for propagation
   * 
   * @param {TraceContext} traceContext - The trace context
   * @returns {Object} - The trace headers
   */
  createTraceHeaders(traceContext) {
    if (!traceContext) {
      traceContext = this.getCurrentTraceContext();
    }
    
    if (!traceContext) {
      return {};
    }
    
    return {
      'X-Trace-ID': traceContext.traceId,
      'X-Span-ID': traceContext.spanId,
      'X-Sampled': traceContext.sampled ? '1' : '0'
    };
  }

  /**
   * Parse trace headers
   * 
   * @param {Object} headers - The headers
   * @returns {Object} - The parsed trace context
   */
  parseTraceHeaders(headers) {
    const traceId = headers['x-trace-id'] || headers['X-Trace-ID'];
    const spanId = headers['x-span-id'] || headers['X-Span-ID'];
    const sampled = headers['x-sampled'] || headers['X-Sampled'];
    
    if (!traceId) {
      return null;
    }
    
    return {
      traceId,
      parentSpanId: spanId,
      sampled: sampled === '1' || sampled === 'true'
    };
  }

  /**
   * Wrap a function with tracing
   * 
   * @param {string} name - The span name
   * @param {Function} fn - The function to wrap
   * @param {Object} options - Trace options
   * @returns {Function} - The wrapped function
   */
  traceFunction(name, fn, options = {}) {
    return async (...args) => {
      const span = this.startSpan(name, options);
      
      try {
        const result = await fn(...args);
        return result;
      } catch (error) {
        span.setAttribute('error', true);
        span.setAttribute('error.message', error.message);
        span.setAttribute('error.stack', error.stack);
        throw error;
      } finally {
        this.endTrace(span);
      }
    };
  }
}

// Create singleton instance
const tracingService = new TracingService();

// Initialize the service
tracingService.initialize();

module.exports = tracingService;
