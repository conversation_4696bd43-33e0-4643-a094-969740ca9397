/**
 * NovaConnect API Server
 *
 * This is the main entry point for the NovaConnect API server.
 */

const express = require('express');
const cors = require('cors');
const morgan = require('morgan');
const helmet = require('helmet');
const errorHandler = require('./middleware/errorHandler');
const databaseManager = require('./config/database');
const logger = require('./config/logger');
const initializeDatabase = require('./config/init-db');
const UserAuthenticationService = require('../auth/user-authentication-service');

// Import routes
const connectorRoutes = require('./routes/connectorRoutes');
const testingRoutes = require('./routes/testingRoutes');
const monitoringRoutes = require('./routes/monitoringRoutes');
const credentialRoutes = require('./routes/credentialRoutes');
const graphqlRoutes = require('./routes/graphqlRoutes');
const graphqlSubscriptionRoutes = require('./routes/graphqlSubscriptionRoutes');
const authRoutes = require('./routes/authRoutes');
const apiKeyRoutes = require('./routes/apiKeyRoutes');
const analyticsRoutes = require('./routes/analyticsRoutes');
const environmentRoutes = require('./routes/environmentRoutes');
const teamRoutes = require('./routes/teamRoutes');
const rolePermissionRoutes = require('./routes/rolePermissionRoutes');
const auditRoutes = require('./routes/auditRoutes');
const changeRequestRoutes = require('./routes/changeRequestRoutes');
const policyRoutes = require('./routes/policyRoutes');
const resourceLockRoutes = require('./routes/resourceLockRoutes');
const identityProviderRoutes = require('./routes/identityProviderRoutes');
const ssoAuthRoutes = require('./routes/ssoAuthRoutes');
const themeRoutes = require('./routes/themeRoutes');
const brandingRoutes = require('./routes/brandingRoutes');
const whiteLabelRoutes = require('./routes/whiteLabelRoutes');
const reportRoutes = require('./routes/reportRoutes');
const exportImportRoutes = require('./routes/exportImportRoutes');
const workflowRoutes = require('./routes/workflowRoutes');
const featureFlagRoutes = require('./routes/featureFlagRoutes');
const aiAssistRoutes = require('./routes/aiAssistRoutes');
const governanceRoutes = require('./routes/governanceRoutes');
const securityRoutes = require('./routes/securityRoutes');

// Import middleware
const { optionalAuth } = require('./middleware/authMiddleware');
const { trackApiUsage, trackApiErrors } = require('./middleware/analyticsMiddleware');
const AuditService = require('./services/AuditService');
const auditService = new AuditService();
const auditMiddleware = auditService.createAuditMiddleware();

// Initialize authentication service
const userAuthService = new UserAuthenticationService();

// Create Express app
const app = express();

// Middleware
app.use(helmet()); // Security headers
app.use(cors()); // Enable CORS
app.use(morgan('dev')); // Logging
app.use(express.json()); // Parse JSON bodies
app.use(express.urlencoded({ extended: true })); // Parse URL-encoded bodies

// Apply analytics middleware to track API usage
app.use(trackApiUsage);

// Apply audit middleware to log user actions
app.use(auditMiddleware);

// Apply optional authentication to all routes
app.use(optionalAuth);

// API routes
app.use('/api/auth', authRoutes);
app.use('/api/api-keys', apiKeyRoutes);
app.use('/api/analytics', analyticsRoutes);
app.use('/api/environments', environmentRoutes);
app.use('/api/teams', teamRoutes);
app.use('/api/roles', rolePermissionRoutes);
app.use('/api/audit', auditRoutes);
app.use('/api/change-requests', changeRequestRoutes);
app.use('/api/policies', policyRoutes);
app.use('/api/resource-locks', resourceLockRoutes);
app.use('/api/identity-providers', identityProviderRoutes);
app.use('/api/sso', ssoAuthRoutes);
app.use('/api/themes', themeRoutes);
app.use('/api/branding', brandingRoutes);
app.use('/api/white-label', whiteLabelRoutes);
app.use('/api/reports', reportRoutes);
app.use('/api/export-import', exportImportRoutes);
app.use('/api/workflows', workflowRoutes);
app.use('/api/subscription', featureFlagRoutes);
app.use('/api/ai-assist', aiAssistRoutes);
app.use('/api/governance', governanceRoutes);
app.use('/api/security', securityRoutes);
app.use('/api/connectors', connectorRoutes);
app.use('/api/testing', testingRoutes);
app.use('/api/monitoring', monitoringRoutes);
app.use('/api/credentials', credentialRoutes);
app.use('/api/graphql', graphqlRoutes);
app.use('/api/graphql/subscriptions', graphqlSubscriptionRoutes);

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok' });
});

// Apply error tracking middleware
app.use(trackApiErrors);

// Error handling
app.use(errorHandler);

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    error: 'Not Found',
    message: `Route ${req.method} ${req.url} not found`
  });
});

// Start server
const PORT = process.env.PORT || 3001;

// Connect to database and initialize server
async function startServer() {
  try {
    logger.info('Starting NovaConnect API server...');

    // Connect to database
    logger.info('Connecting to database...');
    await databaseManager.connect();

    // Initialize database with default data
    await initializeDatabase();

    // Initialize authentication service
    await userAuthService.initialize(databaseManager.connection);

    // Start server
    app.listen(PORT, () => {
      logger.info(`NovaConnect API server running on port ${PORT}`);
      logger.info('Available routes:');
      logger.info('- /api/auth/login');
      logger.info('- /api/auth/register');
      logger.info('- /api/connectors');
      logger.info('- /api/testing');
      logger.info('- /api/graphql/execute');
    });
  } catch (error) {
    logger.error('Failed to start server:', { error: error.message });
    process.exit(1);
  }
}

// Start the server
startServer();

module.exports = app;
