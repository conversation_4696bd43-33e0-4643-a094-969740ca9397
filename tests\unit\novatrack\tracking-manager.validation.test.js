/**
 * Validation Tests for NovaTrack TrackingManager
 * 
 * These tests focus on input validation and data integrity
 */

const { describe, it, beforeEach, afterEach, expect } = require('@jest/globals');
const fs = require('fs');
const path = require('path');
const os = require('os');

// Import the TrackingManager mock
const { TrackingManager } = require('../../mocks/tracking-manager.mock');

describe('NovaTrack TrackingManager - Validation', () => {
  let trackingManager;
  let tempDir;
  
  beforeEach(() => {
    // Create a temporary directory for test data
    tempDir = fs.mkdtempSync(path.join(os.tmpdir(), 'novatrack-validation-test-'));
    
    // Initialize the TrackingManager with the temporary directory
    trackingManager = new TrackingManager(tempDir);
  });
  
  afterEach(() => {
    // Clean up the temporary directory
    if (fs.existsSync(tempDir)) {
      fs.rmSync(tempDir, { recursive: true, force: true });
    }
  });
  
  describe('Requirement Validation', () => {
    it('should validate requirement name is required', () => {
      // Attempt to create a requirement without a name
      expect(() => {
        trackingManager.create_requirement({
          description: 'Test description'
        });
      }).toThrow('Requirement name is required');
    });
    
    it('should validate requirement name is not empty', () => {
      // Attempt to create a requirement with an empty name
      expect(() => {
        trackingManager.create_requirement({
          name: '',
          description: 'Test description'
        });
      }).toThrow('Requirement name cannot be empty');
    });
    
    it('should validate requirement priority is valid', () => {
      // Attempt to create a requirement with an invalid priority
      expect(() => {
        trackingManager.create_requirement({
          name: 'Test Requirement',
          priority: 'invalid_priority'
        });
      }).toThrow('Invalid priority: invalid_priority. Must be one of: low, medium, high');
    });
    
    it('should validate requirement status is valid', () => {
      // Attempt to create a requirement with an invalid status
      expect(() => {
        trackingManager.create_requirement({
          name: 'Test Requirement',
          status: 'invalid_status'
        });
      }).toThrow('Invalid status: invalid_status. Must be one of: pending, in_progress, completed, deferred, cancelled');
    });
    
    it('should validate requirement due_date format', () => {
      // Attempt to create a requirement with an invalid due_date
      expect(() => {
        trackingManager.create_requirement({
          name: 'Test Requirement',
          due_date: 'invalid_date'
        });
      }).toThrow('Invalid due_date format: invalid_date. Must be YYYY-MM-DD or empty');
    });
    
    it('should validate requirement tags is an array', () => {
      // Attempt to create a requirement with tags that is not an array
      expect(() => {
        trackingManager.create_requirement({
          name: 'Test Requirement',
          tags: 'not_an_array'
        });
      }).toThrow('Requirement tags must be an array');
    });
  });
  
  describe('Activity Validation', () => {
    it('should validate activity name is required', () => {
      // Attempt to create an activity without a name
      expect(() => {
        trackingManager.create_activity({
          description: 'Test description'
        });
      }).toThrow('Activity name is required');
    });
    
    it('should validate activity name is not empty', () => {
      // Attempt to create an activity with an empty name
      expect(() => {
        trackingManager.create_activity({
          name: '',
          description: 'Test description'
        });
      }).toThrow('Activity name cannot be empty');
    });
    
    it('should validate activity type is valid', () => {
      // Attempt to create an activity with an invalid type
      expect(() => {
        trackingManager.create_activity({
          name: 'Test Activity',
          type: 'invalid_type'
        });
      }).toThrow('Invalid type: invalid_type. Must be one of: task, meeting, review, documentation, implementation, assessment, training');
    });
    
    it('should validate activity status is valid', () => {
      // Attempt to create an activity with an invalid status
      expect(() => {
        trackingManager.create_activity({
          name: 'Test Activity',
          status: 'invalid_status'
        });
      }).toThrow('Invalid status: invalid_status. Must be one of: pending, in_progress, completed, deferred, cancelled');
    });
    
    it('should validate activity start_date format', () => {
      // Attempt to create an activity with an invalid start_date
      expect(() => {
        trackingManager.create_activity({
          name: 'Test Activity',
          start_date: 'invalid_date'
        });
      }).toThrow('Invalid start_date format: invalid_date. Must be YYYY-MM-DD or empty');
    });
    
    it('should validate activity end_date format', () => {
      // Attempt to create an activity with an invalid end_date
      expect(() => {
        trackingManager.create_activity({
          name: 'Test Activity',
          end_date: 'invalid_date'
        });
      }).toThrow('Invalid end_date format: invalid_date. Must be YYYY-MM-DD or empty');
    });
    
    it('should validate activity requirement_id exists if provided', () => {
      // Attempt to create an activity with a non-existent requirement_id
      expect(() => {
        trackingManager.create_activity({
          name: 'Test Activity',
          requirement_id: 'non_existent_id'
        });
      }).toThrow('Requirement with ID non_existent_id does not exist');
    });
  });
  
  describe('Update Validation', () => {
    it('should validate requirement exists when updating', () => {
      // Attempt to update a non-existent requirement
      expect(() => {
        trackingManager.update_requirement('non_existent_id', {
          name: 'Updated Name'
        });
      }).toThrow('Requirement with ID non_existent_id does not exist');
    });
    
    it('should validate activity exists when updating', () => {
      // Attempt to update a non-existent activity
      expect(() => {
        trackingManager.update_activity('non_existent_id', {
          name: 'Updated Name'
        });
      }).toThrow('Activity with ID non_existent_id does not exist');
    });
    
    it('should validate requirement update data', () => {
      // Create a requirement
      const requirement = trackingManager.create_requirement({
        name: 'Test Requirement'
      });
      
      // Attempt to update with invalid priority
      expect(() => {
        trackingManager.update_requirement(requirement.id, {
          priority: 'invalid_priority'
        });
      }).toThrow('Invalid priority: invalid_priority. Must be one of: low, medium, high');
    });
    
    it('should validate activity update data', () => {
      // Create an activity
      const activity = trackingManager.create_activity({
        name: 'Test Activity'
      });
      
      // Attempt to update with invalid type
      expect(() => {
        trackingManager.update_activity(activity.id, {
          type: 'invalid_type'
        });
      }).toThrow('Invalid type: invalid_type. Must be one of: task, meeting, review, documentation, implementation, assessment, training');
    });
  });
  
  describe('Delete Validation', () => {
    it('should validate requirement exists when deleting', () => {
      // Attempt to delete a non-existent requirement
      expect(() => {
        trackingManager.delete_requirement('non_existent_id');
      }).toThrow('Requirement with ID non_existent_id does not exist');
    });
    
    it('should validate activity exists when deleting', () => {
      // Attempt to delete a non-existent activity
      expect(() => {
        trackingManager.delete_activity('non_existent_id');
      }).toThrow('Activity with ID non_existent_id does not exist');
    });
  });
  
  describe('Data Integrity', () => {
    it('should maintain data integrity when creating requirements', () => {
      // Create a requirement
      const requirement = trackingManager.create_requirement({
        name: 'Test Requirement',
        description: 'Test description',
        framework: 'Test framework',
        category: 'Test category',
        priority: 'high',
        status: 'in_progress',
        due_date: '2023-12-31',
        assigned_to: 'test_user',
        tags: ['test', 'requirement']
      });
      
      // Verify the requirement was created with the correct data
      expect(requirement.name).toBe('Test Requirement');
      expect(requirement.description).toBe('Test description');
      expect(requirement.framework).toBe('Test framework');
      expect(requirement.category).toBe('Test category');
      expect(requirement.priority).toBe('high');
      expect(requirement.status).toBe('in_progress');
      expect(requirement.due_date).toBe('2023-12-31');
      expect(requirement.assigned_to).toBe('test_user');
      expect(requirement.tags).toEqual(['test', 'requirement']);
      expect(requirement.id).toBeDefined();
      expect(requirement.created_at).toBeDefined();
      expect(requirement.updated_at).toBeDefined();
    });
    
    it('should maintain data integrity when creating activities', () => {
      // Create a requirement
      const requirement = trackingManager.create_requirement({
        name: 'Test Requirement'
      });
      
      // Create an activity
      const activity = trackingManager.create_activity({
        name: 'Test Activity',
        description: 'Test description',
        requirement_id: requirement.id,
        type: 'task',
        status: 'in_progress',
        start_date: '2023-01-01',
        end_date: '2023-01-31',
        assigned_to: 'test_user',
        notes: 'Test notes'
      });
      
      // Verify the activity was created with the correct data
      expect(activity.name).toBe('Test Activity');
      expect(activity.description).toBe('Test description');
      expect(activity.requirement_id).toBe(requirement.id);
      expect(activity.type).toBe('task');
      expect(activity.status).toBe('in_progress');
      expect(activity.start_date).toBe('2023-01-01');
      expect(activity.end_date).toBe('2023-01-31');
      expect(activity.assigned_to).toBe('test_user');
      expect(activity.notes).toBe('Test notes');
      expect(activity.id).toBeDefined();
      expect(activity.created_at).toBeDefined();
      expect(activity.updated_at).toBeDefined();
    });
    
    it('should maintain data integrity when updating requirements', () => {
      // Create a requirement
      const requirement = trackingManager.create_requirement({
        name: 'Test Requirement',
        priority: 'medium',
        status: 'pending'
      });
      
      // Update the requirement
      const updatedRequirement = trackingManager.update_requirement(requirement.id, {
        name: 'Updated Requirement',
        priority: 'high',
        status: 'in_progress'
      });
      
      // Verify the requirement was updated with the correct data
      expect(updatedRequirement.name).toBe('Updated Requirement');
      expect(updatedRequirement.priority).toBe('high');
      expect(updatedRequirement.status).toBe('in_progress');
      expect(updatedRequirement.id).toBe(requirement.id);
      expect(updatedRequirement.created_at).toBe(requirement.created_at);
      expect(updatedRequirement.updated_at).not.toBe(requirement.updated_at);
    });
    
    it('should maintain data integrity when updating activities', () => {
      // Create an activity
      const activity = trackingManager.create_activity({
        name: 'Test Activity',
        type: 'task',
        status: 'pending'
      });
      
      // Update the activity
      const updatedActivity = trackingManager.update_activity(activity.id, {
        name: 'Updated Activity',
        type: 'meeting',
        status: 'in_progress'
      });
      
      // Verify the activity was updated with the correct data
      expect(updatedActivity.name).toBe('Updated Activity');
      expect(updatedActivity.type).toBe('meeting');
      expect(updatedActivity.status).toBe('in_progress');
      expect(updatedActivity.id).toBe(activity.id);
      expect(updatedActivity.created_at).toBe(activity.created_at);
      expect(updatedActivity.updated_at).not.toBe(activity.updated_at);
    });
  });
});
