/**
 * Schema Controller
 * 
 * This controller provides API endpoints for schema management.
 */

const { generateSchemaFromModel, generateSchemaFromJSON } = require('./schemaGenerator');
const models = require('../models');
const fs = require('fs').promises;
const path = require('path');
const logger = require('../utils/logger');

/**
 * Get schema for entity
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getSchema = async (req, res) => {
  try {
    const { entityType } = req.params;
    
    // Check if we have a custom schema file
    const customSchemaPath = path.join(__dirname, 'custom', `${entityType}.schema.json`);
    
    try {
      // Try to read custom schema
      const customSchemaContent = await fs.readFile(customSchemaPath, 'utf8');
      const customSchema = JSON.parse(customSchemaContent);
      
      return res.json({
        success: true,
        data: customSchema,
        source: 'custom'
      });
    } catch (err) {
      // No custom schema, generate from model
      if (err.code === 'ENOENT') {
        // Get model
        const model = models[entityType];
        
        if (!model) {
          return res.status(404).json({
            success: false,
            error: `No model found for ${entityType}`
          });
        }
        
        // Generate schema
        const schema = generateSchemaFromModel(model);
        
        return res.json({
          success: true,
          data: schema,
          source: 'generated'
        });
      } else {
        throw err;
      }
    }
  } catch (error) {
    logger.error(`Error getting schema for ${req.params.entityType}`, error);
    
    res.status(500).json({
      success: false,
      error: 'Failed to get schema'
    });
  }
};

/**
 * Save custom schema
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const saveSchema = async (req, res) => {
  try {
    const { entityType } = req.params;
    const schema = req.body;
    
    // Validate schema
    if (!schema.entityName || !schema.apiEndpoint || !Array.isArray(schema.fields)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid schema format'
      });
    }
    
    // Create custom schemas directory if it doesn't exist
    const customSchemasDir = path.join(__dirname, 'custom');
    try {
      await fs.mkdir(customSchemasDir, { recursive: true });
    } catch (err) {
      if (err.code !== 'EEXIST') {
        throw err;
      }
    }
    
    // Save schema
    const schemaPath = path.join(customSchemasDir, `${entityType}.schema.json`);
    await fs.writeFile(schemaPath, JSON.stringify(schema, null, 2), 'utf8');
    
    res.json({
      success: true,
      message: `Schema for ${entityType} saved successfully`
    });
  } catch (error) {
    logger.error(`Error saving schema for ${req.params.entityType}`, error);
    
    res.status(500).json({
      success: false,
      error: 'Failed to save schema'
    });
  }
};

/**
 * Generate schema from JSON
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const generateSchema = async (req, res) => {
  try {
    const { entityName, jsonData } = req.body;
    
    if (!entityName || !jsonData) {
      return res.status(400).json({
        success: false,
        error: 'Entity name and JSON data are required'
      });
    }
    
    // Generate schema
    const schema = generateSchemaFromJSON(jsonData, entityName);
    
    res.json({
      success: true,
      data: schema
    });
  } catch (error) {
    logger.error('Error generating schema from JSON', error);
    
    res.status(500).json({
      success: false,
      error: 'Failed to generate schema'
    });
  }
};

/**
 * List all available schemas
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const listSchemas = async (req, res) => {
  try {
    const schemas = [];
    
    // Get custom schemas
    const customSchemasDir = path.join(__dirname, 'custom');
    try {
      const files = await fs.readdir(customSchemasDir);
      
      for (const file of files) {
        if (file.endsWith('.schema.json')) {
          const content = await fs.readFile(path.join(customSchemasDir, file), 'utf8');
          const schema = JSON.parse(content);
          
          schemas.push({
            entityType: file.replace('.schema.json', ''),
            entityName: schema.entityName,
            entityNamePlural: schema.entityNamePlural,
            apiEndpoint: schema.apiEndpoint,
            source: 'custom'
          });
        }
      }
    } catch (err) {
      if (err.code !== 'ENOENT') {
        throw err;
      }
    }
    
    // Get model-based schemas
    for (const modelName in models) {
      // Skip if we already have a custom schema
      if (schemas.some(s => s.entityType === modelName)) {
        continue;
      }
      
      const model = models[modelName];
      
      schemas.push({
        entityType: modelName,
        entityName: modelName,
        entityNamePlural: model.collection.name,
        apiEndpoint: `/api/v1/${model.collection.name.toLowerCase()}`,
        source: 'model'
      });
    }
    
    res.json({
      success: true,
      data: schemas
    });
  } catch (error) {
    logger.error('Error listing schemas', error);
    
    res.status(500).json({
      success: false,
      error: 'Failed to list schemas'
    });
  }
};

/**
 * Delete custom schema
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const deleteSchema = async (req, res) => {
  try {
    const { entityType } = req.params;
    
    // Check if custom schema exists
    const schemaPath = path.join(__dirname, 'custom', `${entityType}.schema.json`);
    
    try {
      await fs.access(schemaPath);
    } catch (err) {
      return res.status(404).json({
        success: false,
        error: `No custom schema found for ${entityType}`
      });
    }
    
    // Delete schema
    await fs.unlink(schemaPath);
    
    res.json({
      success: true,
      message: `Schema for ${entityType} deleted successfully`
    });
  } catch (error) {
    logger.error(`Error deleting schema for ${req.params.entityType}`, error);
    
    res.status(500).json({
      success: false,
      error: 'Failed to delete schema'
    });
  }
};

module.exports = {
  getSchema,
  saveSchema,
  generateSchema,
  listSchemas,
  deleteSchema
};
