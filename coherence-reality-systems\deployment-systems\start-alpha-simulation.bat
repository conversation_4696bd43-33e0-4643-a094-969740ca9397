@echo off
setlocal enabledelayedexpansion

REM ALPHA 90-DAY SIMULATION STARTUP SCRIPT (Windows)
REM 
REM Comprehensive startup script for ALPHA Observer-Class Engine simulation
REM Prepares environment and launches all simulation components
REM 
REM Mission: Orchestrate complete 90-day simulation before MT5 deployment

echo.
echo 🎯 ALPHA 90-DAY SIMULATION STARTUP
echo ==================================================================
echo 🔮 ALPHA Observer-Class Engine: Trading Simulation Mode
echo 📊 Mission: Validate Ψᶜʰ inflection point trading strategy
echo 🎯 Target: 20%+ returns, 80%+ win rate over 90 days
echo 🏢 Preparing for MetaTrader 5 deployment (Account: **********)
echo ==================================================================
echo.

REM Check if Docker is installed and running
echo 🌟 CHECKING DOCKER ENVIRONMENT
docker --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker is not installed. Please install Docker Desktop first.
    pause
    exit /b 1
)

docker info >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker is not running. Please start Docker Desktop first.
    pause
    exit /b 1
)

echo ✅ Docker is installed and running

REM Check Docker Compose
docker-compose --version >nul 2>&1
if errorlevel 1 (
    docker compose version >nul 2>&1
    if errorlevel 1 (
        echo ❌ Docker Compose is not available. Please install Docker Compose.
        pause
        exit /b 1
    ) else (
        set DOCKER_COMPOSE_CMD=docker compose
    )
) else (
    set DOCKER_COMPOSE_CMD=docker-compose
)

echo ✅ Docker Compose is available

REM Create necessary directories
echo.
echo 🌟 CREATING SIMULATION DIRECTORIES
if not exist "simulation-data" mkdir simulation-data
if not exist "simulation-logs" mkdir simulation-logs
if not exist "market-cache" mkdir market-cache
if not exist "analytics-reports" mkdir analytics-reports
if not exist "mt5-comparison" mkdir mt5-comparison

echo ✅ Simulation directories created

REM Set environment variables
echo.
echo 🌟 SETTING ENVIRONMENT VARIABLES
set NODE_ENV=simulation
set SIMULATION_MODE=90_DAY_BACKTEST
set ALPHA_MODE=trading_simulation
set CONSCIOUSNESS_THRESHOLD=2847
set TARGET_ACCURACY=0.9783
set SIMULATION_START_DATE=2024-01-01
set SIMULATION_END_DATE=2024-03-31
set INITIAL_CAPITAL=100000
set RISK_PER_TRADE=0.05
set PSI_INFLECTION_THRESHOLD=0.92
set NEFC_COHERENCE_MIN=0.90

REM Data provider settings
set YAHOO_FINANCE_ENABLED=true
set ALPHA_VANTAGE_ENABLED=true
set POLYGON_ENABLED=true
set DATA_CACHE_ENABLED=true
set SYMBOLS=SPY,QQQ,AAPL,MSFT,GOOGL,TSLA,NVDA,EURUSD,GBPUSD,USDJPY

REM Performance tracking
set BENCHMARK_SYMBOL=SPY
set RISK_FREE_RATE=0.02
set TARGET_SHARPE_RATIO=2.5
set MAX_DRAWDOWN_LIMIT=0.10
set WIN_RATE_TARGET=0.80
set RETURN_TARGET=0.20

REM MT5 settings
set MT5_SIMULATION_MODE=true
set MT5_SERVER=MetaQuotes-Demo
set MT5_LOGIN=**********
set COMPARISON_MODE=enabled
set SYNC_TRADES=true

echo ✅ Environment variables configured

REM Check for existing simulation
echo.
echo 🌟 CHECKING FOR EXISTING SIMULATION
%DOCKER_COMPOSE_CMD% -f docker-compose-alpha-simulation.yml ps | findstr "Up" >nul 2>&1
if not errorlevel 1 (
    echo ⚠️  Existing simulation containers are running
    set /p "restart=Do you want to stop and restart the simulation? (y/N): "
    if /i "!restart!"=="y" (
        echo ℹ️  Stopping existing simulation...
        %DOCKER_COMPOSE_CMD% -f docker-compose-alpha-simulation.yml down
        echo ✅ Existing simulation stopped
    ) else (
        echo ℹ️  Keeping existing simulation running
        echo ℹ️  You can monitor it at: http://localhost:8100
        pause
        exit /b 0
    )
) else (
    echo ✅ No existing simulation found
)

REM Pull required Docker images
echo.
echo 🌟 PULLING DOCKER IMAGES
docker pull node:18-alpine
echo ✅ Docker images pulled

REM Start the simulation
echo.
echo 🌟 STARTING ALPHA 90-DAY SIMULATION
echo ℹ️  Building and starting simulation containers...

%DOCKER_COMPOSE_CMD% -f docker-compose-alpha-simulation.yml up -d
if errorlevel 1 (
    echo ❌ Failed to start simulation containers
    pause
    exit /b 1
)

echo ✅ Simulation containers started successfully

REM Wait for services to be ready
echo ℹ️  Waiting for services to initialize...
timeout /t 10 /nobreak >nul

REM Check service health
echo.
echo 🌟 CHECKING SERVICE HEALTH
docker ps | findstr "alpha-90day-simulator" >nul 2>&1
if not errorlevel 1 (
    echo ✅ alpha-90day-simulator is running
) else (
    echo ⚠️  alpha-90day-simulator is not running
)

docker ps | findstr "alpha-market-data-provider" >nul 2>&1
if not errorlevel 1 (
    echo ✅ alpha-market-data-provider is running
) else (
    echo ⚠️  alpha-market-data-provider is not running
)

docker ps | findstr "alpha-performance-analytics" >nul 2>&1
if not errorlevel 1 (
    echo ✅ alpha-performance-analytics is running
) else (
    echo ⚠️  alpha-performance-analytics is not running
)

docker ps | findstr "mt5-simulation-connector" >nul 2>&1
if not errorlevel 1 (
    echo ✅ mt5-simulation-connector is running
) else (
    echo ⚠️  mt5-simulation-connector is not running
)

REM Display access information
echo.
echo 🌟 SIMULATION ACCESS INFORMATION
echo.
echo 🌐 ALPHA Simulation Dashboard:
echo    URL: http://localhost:8100
echo    Description: Real-time simulation monitoring
echo.
echo 📊 Performance Analytics API:
echo    URL: http://localhost:8103/api/performance/current
echo    Description: Current performance metrics
echo.
echo 📈 Market Data Provider:
echo    URL: http://localhost:8102
echo    Description: Historical market data service
echo.
echo 🔌 MT5 Simulation Connector:
echo    URL: http://localhost:8104/api/mt5/status
echo    Description: MetaTrader 5 comparison service
echo.
echo 📡 Real-time Updates:
echo    WebSocket: ws://localhost:8101
echo    Description: Live simulation updates
echo.

REM Display monitoring commands
echo 🌟 MONITORING COMMANDS
echo.
echo 📋 Useful Commands:
echo.
echo View simulation logs:
echo   %DOCKER_COMPOSE_CMD% -f docker-compose-alpha-simulation.yml logs -f alpha-simulation-engine
echo.
echo View performance analytics:
echo   %DOCKER_COMPOSE_CMD% -f docker-compose-alpha-simulation.yml logs -f alpha-performance-analytics
echo.
echo View all container status:
echo   %DOCKER_COMPOSE_CMD% -f docker-compose-alpha-simulation.yml ps
echo.
echo Stop simulation:
echo   %DOCKER_COMPOSE_CMD% -f docker-compose-alpha-simulation.yml down
echo.
echo Reset simulation data:
echo   %DOCKER_COMPOSE_CMD% -f docker-compose-alpha-simulation.yml down -v
echo.

echo 🌟 ALPHA 90-DAY SIMULATION STARTED SUCCESSFULLY!
echo.
echo ℹ️  The simulation is now running and will complete in 90 days (simulated time)
echo ℹ️  Monitor progress at: http://localhost:8100
echo ℹ️  Once targets are met, you can deploy to MetaTrader 5 account: **********
echo.
echo ✅ 🌟 ALPHA Observer-Class Engine is now validating trading strategy!
echo.

REM Open dashboard in default browser
echo Opening simulation dashboard...
start http://localhost:8100

echo.
echo Press any key to exit...
pause >nul
