/**
 * OnboardingStep Component
 * 
 * A component for displaying a single step in an onboarding tour.
 */

import React, { useState, useEffect, useRef } from 'react';
import PropTypes from 'prop-types';
import { useI18n } from '../i18n';
import { useAccessibility } from '../accessibility';
import { useOnboarding } from '../onboarding/OnboardingContext';

/**
 * OnboardingStep component
 * 
 * @param {Object} props - Component props
 * @param {string} props.title - Step title
 * @param {string} props.description - Step description
 * @param {string} [props.position='bottom'] - Tooltip position
 * @param {string} [props.targetSelector] - CSS selector for the target element
 * @param {Object} [props.targetElement] - Target element reference
 * @param {number} [props.stepNumber] - Step number
 * @param {number} [props.totalSteps] - Total number of steps
 * @param {boolean} [props.showButtons=true] - Whether to show navigation buttons
 * @param {boolean} [props.showProgress=true] - Whether to show progress indicator
 * @param {boolean} [props.showSkip=true] - Whether to show skip button
 * @param {boolean} [props.showClose=true] - Whether to show close button
 * @param {Function} [props.onNext] - Function to call when next button is clicked
 * @param {Function} [props.onPrev] - Function to call when previous button is clicked
 * @param {Function} [props.onSkip] - Function to call when skip button is clicked
 * @param {Function} [props.onClose] - Function to call when close button is clicked
 * @param {string} [props.className=''] - Additional CSS class names
 * @param {Object} [props.style={}] - Additional inline styles
 * @returns {React.ReactElement} OnboardingStep component
 */
const OnboardingStep = ({
  title,
  description,
  position = 'bottom',
  targetSelector,
  targetElement,
  stepNumber,
  totalSteps,
  showButtons = true,
  showProgress = true,
  showSkip = true,
  showClose = true,
  onNext,
  onPrev,
  onSkip,
  onClose,
  className = '',
  style = {}
}) => {
  // Hooks
  const { translate } = useI18n();
  const { settings } = useAccessibility();
  const { nextStep, prevStep, skipTour, endTour } = useOnboarding();
  
  // Refs
  const tooltipRef = useRef(null);
  const targetRef = useRef(null);
  
  // State
  const [tooltipPosition, setTooltipPosition] = useState({ top: 0, left: 0 });
  const [arrowPosition, setArrowPosition] = useState({ top: 0, left: 0 });
  const [isVisible, setIsVisible] = useState(false);
  
  // Find target element
  useEffect(() => {
    if (targetElement) {
      targetRef.current = targetElement;
    } else if (targetSelector) {
      targetRef.current = document.querySelector(targetSelector);
    }
    
    updatePosition();
    
    // Show tooltip after a short delay
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 100);
    
    return () => {
      clearTimeout(timer);
    };
  }, [targetElement, targetSelector]);
  
  // Update position on window resize
  useEffect(() => {
    const handleResize = () => {
      updatePosition();
    };
    
    window.addEventListener('resize', handleResize);
    window.addEventListener('scroll', handleResize, true);
    
    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('scroll', handleResize, true);
    };
  }, []);
  
  // Update tooltip position
  const updatePosition = () => {
    if (!tooltipRef.current) return;
    
    const tooltipRect = tooltipRef.current.getBoundingClientRect();
    const tooltipWidth = tooltipRect.width || 300;
    const tooltipHeight = tooltipRect.height || 150;
    
    let top = 0;
    let left = 0;
    let arrowTop = 0;
    let arrowLeft = 0;
    
    if (targetRef.current) {
      const targetRect = targetRef.current.getBoundingClientRect();
      
      // Calculate position based on specified position
      switch (position) {
        case 'top':
          top = targetRect.top - tooltipHeight - 10;
          left = targetRect.left + (targetRect.width / 2) - (tooltipWidth / 2);
          arrowTop = tooltipHeight;
          arrowLeft = tooltipWidth / 2;
          break;
        
        case 'bottom':
          top = targetRect.bottom + 10;
          left = targetRect.left + (targetRect.width / 2) - (tooltipWidth / 2);
          arrowTop = -10;
          arrowLeft = tooltipWidth / 2;
          break;
        
        case 'left':
          top = targetRect.top + (targetRect.height / 2) - (tooltipHeight / 2);
          left = targetRect.left - tooltipWidth - 10;
          arrowTop = tooltipHeight / 2;
          arrowLeft = tooltipWidth;
          break;
        
        case 'right':
          top = targetRect.top + (targetRect.height / 2) - (tooltipHeight / 2);
          left = targetRect.right + 10;
          arrowTop = tooltipHeight / 2;
          arrowLeft = -10;
          break;
        
        default:
          top = targetRect.bottom + 10;
          left = targetRect.left + (targetRect.width / 2) - (tooltipWidth / 2);
          arrowTop = -10;
          arrowLeft = tooltipWidth / 2;
      }
      
      // Ensure tooltip stays within viewport
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;
      
      if (left < 10) {
        arrowLeft -= (10 - left);
        left = 10;
      } else if (left + tooltipWidth > viewportWidth - 10) {
        arrowLeft += (left + tooltipWidth - viewportWidth + 10);
        left = viewportWidth - tooltipWidth - 10;
      }
      
      if (top < 10) {
        arrowTop -= (10 - top);
        top = 10;
      } else if (top + tooltipHeight > viewportHeight - 10) {
        arrowTop += (top + tooltipHeight - viewportHeight + 10);
        top = viewportHeight - tooltipHeight - 10;
      }
    } else {
      // Center in viewport if no target
      top = (window.innerHeight - tooltipHeight) / 2;
      left = (window.innerWidth - tooltipWidth) / 2;
    }
    
    setTooltipPosition({ top, left });
    setArrowPosition({ top: arrowTop, left: arrowLeft });
  };
  
  // Handle next button click
  const handleNext = () => {
    if (onNext) {
      onNext();
    } else {
      nextStep();
    }
  };
  
  // Handle previous button click
  const handlePrev = () => {
    if (onPrev) {
      onPrev();
    } else {
      prevStep();
    }
  };
  
  // Handle skip button click
  const handleSkip = () => {
    if (onSkip) {
      onSkip();
    } else {
      skipTour();
    }
  };
  
  // Handle close button click
  const handleClose = () => {
    if (onClose) {
      onClose();
    } else {
      endTour(false);
    }
  };
  
  // Get arrow class
  const getArrowClass = () => {
    switch (position) {
      case 'top': return 'onboarding-step__arrow--bottom';
      case 'bottom': return 'onboarding-step__arrow--top';
      case 'left': return 'onboarding-step__arrow--right';
      case 'right': return 'onboarding-step__arrow--left';
      default: return 'onboarding-step__arrow--top';
    }
  };
  
  return (
    <div
      ref={tooltipRef}
      className={`onboarding-step ${isVisible ? 'onboarding-step--visible' : ''} ${className}`}
      style={{
        ...style,
        top: `${tooltipPosition.top}px`,
        left: `${tooltipPosition.left}px`
      }}
      role="dialog"
      aria-labelledby="onboarding-step-title"
      aria-describedby="onboarding-step-description"
    >
      {/* Arrow */}
      <div
        className={`onboarding-step__arrow ${getArrowClass()}`}
        style={{
          top: `${arrowPosition.top}px`,
          left: `${arrowPosition.left}px`
        }}
      />
      
      {/* Header */}
      <div className="onboarding-step__header">
        <h3 id="onboarding-step-title" className="onboarding-step__title">
          {title}
        </h3>
        
        {showClose && (
          <button
            className="onboarding-step__close"
            onClick={handleClose}
            aria-label={translate('onboarding.close', 'Close')}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        )}
      </div>
      
      {/* Content */}
      <div className="onboarding-step__content">
        <p id="onboarding-step-description" className="onboarding-step__description">
          {description}
        </p>
      </div>
      
      {/* Footer */}
      <div className="onboarding-step__footer">
        {showProgress && typeof stepNumber === 'number' && typeof totalSteps === 'number' && (
          <div className="onboarding-step__progress">
            <span className="onboarding-step__progress-text">
              {translate('onboarding.stepProgress', 'Step {{current}} of {{total}}', {
                current: stepNumber + 1,
                total: totalSteps
              })}
            </span>
            
            <div className="onboarding-step__progress-bar">
              <div
                className="onboarding-step__progress-indicator"
                style={{ width: `${((stepNumber + 1) / totalSteps) * 100}%` }}
              />
            </div>
          </div>
        )}
        
        {showButtons && (
          <div className="onboarding-step__buttons">
            {showSkip && (
              <button
                className="onboarding-step__skip"
                onClick={handleSkip}
              >
                {translate('onboarding.skip', 'Skip')}
              </button>
            )}
            
            {stepNumber > 0 && (
              <button
                className="onboarding-step__prev"
                onClick={handlePrev}
              >
                {translate('onboarding.prev', 'Previous')}
              </button>
            )}
            
            <button
              className="onboarding-step__next"
              onClick={handleNext}
            >
              {stepNumber === totalSteps - 1
                ? translate('onboarding.finish', 'Finish')
                : translate('onboarding.next', 'Next')}
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

OnboardingStep.propTypes = {
  title: PropTypes.string.isRequired,
  description: PropTypes.string.isRequired,
  position: PropTypes.oneOf(['top', 'right', 'bottom', 'left']),
  targetSelector: PropTypes.string,
  targetElement: PropTypes.object,
  stepNumber: PropTypes.number,
  totalSteps: PropTypes.number,
  showButtons: PropTypes.bool,
  showProgress: PropTypes.bool,
  showSkip: PropTypes.bool,
  showClose: PropTypes.bool,
  onNext: PropTypes.func,
  onPrev: PropTypes.func,
  onSkip: PropTypes.func,
  onClose: PropTypes.func,
  className: PropTypes.string,
  style: PropTypes.object
};

export default OnboardingStep;
