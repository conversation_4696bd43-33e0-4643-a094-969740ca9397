// ***********************************************
// This example commands.js shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************

// -- This is a parent command --
Cypress.Commands.add('login', (email, password) => {
  cy.visit('/signin');
  cy.get('input[name="email"]').type(email);
  cy.get('input[name="password"]').type(password);
  cy.get('form').submit();
  cy.url().should('include', '/dashboard');
});

// Command to navigate to a specific product page
Cypress.Commands.add('navigateToProduct', (productName) => {
  cy.visit('/');
  cy.get('nav').contains('Products').click();
  cy.contains(productName).click();
});

// Command to check if an element is in viewport
Cypress.Commands.add('isInViewport', { prevSubject: true }, (subject) => {
  const bottom = Cypress.$(cy.state('window')).height();
  const rect = subject[0].getBoundingClientRect();

  expect(rect.top).to.be.lessThan(bottom);
  expect(rect.bottom).to.be.greaterThan(0);

  return subject;
});

// Command to test the NovaConcierge chat
Cypress.Commands.add('testNovaConcierge', (message) => {
  cy.get('[data-testid="nova-concierge-button"]').click();
  cy.get('[data-testid="chat-input"]').type(message);
  cy.get('[data-testid="chat-form"]').submit();
  cy.get('[data-testid="chat-messages"]').contains(message).should('be.visible');
});

// Command to test the implementation calculator
Cypress.Commands.add('testImplementationCalculator', (framework, controls) => {
  cy.visit('/partner-empowerment');
  cy.get('[data-testid="implementation-calculator"]').scrollIntoView();
  cy.get('select[name="framework"]').select(framework);
  cy.get('input[name="controls"]').clear().type(controls);
  cy.get('button').contains('Calculate').click();
  cy.get('[data-testid="calculator-results"]').should('be.visible');
});

// -- Compliance Testing Commands --

// Command to initiate a compliance audit
Cypress.Commands.add('initiateAudit', (framework, scope) => {
  cy.contains('Compliance').click();
  cy.contains('New Audit').click();
  cy.get('select[name="framework"]').select(framework);
  cy.get('input[name="scope"]').type(scope);
  cy.get('[data-testid="select-all-controls"]').click();
  cy.contains('Next').click();
  cy.contains('Next').click();
  cy.contains('Start Audit').click();
});

// Command to collect evidence for an audit
Cypress.Commands.add('collectEvidence', (sources = ['aws', 'azure', 'gcp']) => {
  // Navigate to evidence collection if not already there
  cy.url().then(url => {
    if (!url.includes('/audit/')) {
      cy.contains('View Progress').click();
    }
  });

  // Select evidence sources
  sources.forEach(source => {
    cy.get(`[data-testid="${source}-source"]`).click();
  });

  cy.contains('Collect Evidence').click();

  // Mock evidence collection completion
  cy.window().then(win => {
    win.mockCompleteEvidenceCollection = function() {
      const event = new CustomEvent('evidenceCollectionComplete');
      window.dispatchEvent(event);
    };
    win.mockCompleteEvidenceCollection();
  });

  cy.contains('Evidence Collection Complete', { timeout: 10000 }).should('be.visible');
});

// Command to generate an audit report
Cypress.Commands.add('generateReport', (options = { format: 'PDF', includeEvidence: true }) => {
  cy.contains('Generate Report').click();
  cy.get('select[name="reportFormat"]').select(options.format);

  if (options.includeEvidence) {
    cy.get('input[name="includeEvidence"]').check();
  }

  cy.contains('Generate').click();
  cy.contains('Report Generated Successfully', { timeout: 10000 }).should('be.visible');
});

// Command to handle evidence collection failures
Cypress.Commands.add('handleEvidenceFailure', (action = 'retry') => {
  cy.contains('Evidence Collection Issues Detected', { timeout: 10000 }).should('be.visible');

  if (action === 'retry') {
    cy.contains('Retry Failed Sources').click();

    // Mock successful retry
    cy.window().then(win => {
      win.mockSuccessfulRetry = function() {
        const event = new CustomEvent('retrySuccessful');
        window.dispatchEvent(event);
      };
      win.mockSuccessfulRetry();
    });

    cy.contains('Evidence Collection Complete', { timeout: 10000 }).should('be.visible');
  } else if (action === 'continue') {
    cy.contains('Continue Without Failed Evidence').click();
  }
});

// Command to upload manual evidence
Cypress.Commands.add('uploadManualEvidence', (control, filePath, description) => {
  cy.contains('Upload Manual Evidence').click();
  cy.get('select[name="control"]').select(control);
  cy.get('input[type="file"]').selectFile(filePath);
  cy.get('textarea[name="description"]').type(description);
  cy.contains('Upload Evidence').click();
  cy.contains('Evidence Uploaded Successfully').should('be.visible');
});

// Command to complete an audit
Cypress.Commands.add('completeAudit', () => {
  cy.contains('Complete Audit').click();
  cy.get('[data-testid="audit-complete-confirmation"]').should('be.visible');
  cy.contains('Yes, Complete Audit').click();
  cy.contains('Audit Completed Successfully').should('be.visible');
});
