/**
 * Environment Selector Component
 * 
 * This component allows users to select and switch between environments.
 */

import React, { useState } from 'react';
import { 
  Box, 
  Button, 
  Chip, 
  CircularProgress, 
  FormControl, 
  InputLabel, 
  MenuItem, 
  Select, 
  Tooltip 
} from '@mui/material';
import SettingsIcon from '@mui/icons-material/Settings';
import { useEnvironment } from '../../contexts/EnvironmentContext';

const EnvironmentSelector = ({ onManageClick }) => {
  const { 
    environments, 
    currentEnvironment, 
    loading, 
    switchEnvironment 
  } = useEnvironment();
  
  const [switching, setSwitching] = useState(false);
  
  // Handle environment change
  const handleEnvironmentChange = async (event) => {
    const environmentId = event.target.value;
    
    if (environmentId === currentEnvironment?.id) {
      return;
    }
    
    try {
      setSwitching(true);
      await switchEnvironment(environmentId);
    } catch (error) {
      console.error('Error switching environment:', error);
    } finally {
      setSwitching(false);
    }
  };
  
  // Get environment color
  const getEnvironmentColor = (environment) => {
    return environment.color || '#2196f3';
  };
  
  if (loading) {
    return (
      <Box sx={{ display: 'flex', alignItems: 'center' }}>
        <CircularProgress size={24} sx={{ mr: 1 }} />
        Loading environments...
      </Box>
    );
  }
  
  return (
    <Box sx={{ display: 'flex', alignItems: 'center' }}>
      <FormControl size="small" sx={{ minWidth: 200, mr: 1 }}>
        <InputLabel id="environment-select-label">Environment</InputLabel>
        <Select
          labelId="environment-select-label"
          value={currentEnvironment?.id || ''}
          label="Environment"
          onChange={handleEnvironmentChange}
          disabled={switching || environments.length === 0}
          startAdornment={
            currentEnvironment && (
              <Chip
                size="small"
                label={currentEnvironment.isDefault ? 'Default' : ''}
                sx={{
                  bgcolor: getEnvironmentColor(currentEnvironment),
                  color: 'white',
                  height: 16,
                  width: 16,
                  mr: 1,
                  '& .MuiChip-label': { 
                    p: 0,
                    fontSize: 0
                  }
                }}
              />
            )
          }
        >
          {environments.map((environment) => (
            <MenuItem key={environment.id} value={environment.id}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Chip
                  size="small"
                  label={environment.isDefault ? 'Default' : ''}
                  sx={{
                    bgcolor: getEnvironmentColor(environment),
                    color: 'white',
                    height: 16,
                    width: 16,
                    mr: 1,
                    '& .MuiChip-label': { 
                      p: 0,
                      fontSize: 0
                    }
                  }}
                />
                {environment.name}
                {environment.isDefault && (
                  <Chip
                    size="small"
                    label="Default"
                    sx={{ ml: 1, height: 20 }}
                  />
                )}
              </Box>
            </MenuItem>
          ))}
        </Select>
      </FormControl>
      
      <Tooltip title="Manage Environments">
        <Button
          variant="outlined"
          size="small"
          startIcon={<SettingsIcon />}
          onClick={onManageClick}
        >
          Manage
        </Button>
      </Tooltip>
    </Box>
  );
};

export default EnvironmentSelector;
