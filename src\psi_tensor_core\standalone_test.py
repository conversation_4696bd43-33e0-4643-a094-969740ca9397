#!/usr/bin/env python3
"""
Standalone Test Script for Ψ Tensor Core

This script demonstrates the functionality of the Ψ Tensor Core by:
1. Creating sample data for CSDE, CSFE, and CSME engines
2. Processing the data through the core components
3. Displaying the results
"""

import torch
import numpy as np
import json
import time
import matplotlib.pyplot as plt

# Constants
PI_103 = 3141.59  # π103 scaling factor

class PsiTensorCore:
    """
    Implements the Ψ Tensor Core for fusing CSDE, CSFE, and CSME engines.
    """

    def __init__(self, use_gpu=False):
        """
        Initialize the Ψ Tensor Core.
        """
        self.device = torch.device("cuda" if use_gpu and torch.cuda.is_available() else "cpu")
        print(f"Ψ Tensor Core initialized on {self.device}")

        # Initialize engine weights
        self.w_CSDE = 0.33
        self.w_CSFE = 0.33
        self.w_CSME = 0.34

    def create_csde_tensor(self, governance, data, action, confidence):
        """
        Create a tensor representation for CSDE.
        """
        # Convert action to embedding if it's a string or list of strings
        action_embedding = self._action_to_embedding(action)

        # Create tensor [G, D, A1, c1]
        tensor = torch.tensor([governance, data, action_embedding, confidence],
                             dtype=torch.float32,
                             device=self.device)

        return tensor

    def create_csfe_tensor(self, risk, finance, action, confidence):
        """
        Create a tensor representation for CSFE.
        """
        # Convert action to embedding if it's a string or list of strings
        action_embedding = self._action_to_embedding(action)

        # Create tensor [R, φ, A2, c2]
        tensor = torch.tensor([risk, finance, action_embedding, confidence],
                             dtype=torch.float32,
                             device=self.device)

        return tensor

    def create_csme_tensor(self, bio, med_compliance, action, confidence):
        """
        Create a tensor representation for CSME.
        """
        # Convert action to embedding if it's a string or list of strings
        action_embedding = self._action_to_embedding(action)

        # Create tensor [B, Γ, A3, c3]
        tensor = torch.tensor([bio, med_compliance, action_embedding, confidence],
                             dtype=torch.float32,
                             device=self.device)

        return tensor

    def tensor_product(self, tensor_a, tensor_b):
        """
        Implement the tensor product operator (⊗) using Kronecker product.
        """
        return torch.kron(tensor_a, tensor_b)

    def direct_sum(self, tensor_a, tensor_b):
        """
        Implement the direct sum operator (⊕) using matrix block stacking.
        """
        # Ensure tensors are 1D
        if tensor_a.dim() == 1:
            tensor_a = tensor_a.unsqueeze(0)
        if tensor_b.dim() == 1:
            tensor_b = tensor_b.unsqueeze(0)

        # Concatenate tensors
        return torch.cat([tensor_a, tensor_b], dim=1)

    def apply_dynamic_weighting(self, csde_tensor, csfe_tensor, csme_tensor):
        """
        Apply dynamic weighting to determine which engine dominates per cycle.
        """
        # Extract components for weighting
        G = csde_tensor[0].item()
        D = csde_tensor[1].item()
        R = csfe_tensor[0].item()
        phi = csfe_tensor[1].item()
        B = csme_tensor[0].item()
        gamma = csme_tensor[1].item()

        # Apply 18/82 principle
        w_CSDE_raw = 0.18 * G + 0.82 * D
        w_CSFE_raw = 0.18 * R + 0.82 * phi
        w_CSME_raw = 0.18 * B + 0.82 * gamma

        # Apply sigmoid function for normalization
        w_CSDE = torch.sigmoid(torch.tensor(w_CSDE_raw)).item()
        w_CSFE = torch.sigmoid(torch.tensor(w_CSFE_raw)).item()
        w_CSME = torch.sigmoid(torch.tensor(w_CSME_raw)).item()

        # Normalize weights to sum to 1
        total = w_CSDE + w_CSFE + w_CSME
        w_CSDE /= total
        w_CSFE /= total
        w_CSME /= total

        # Update instance weights
        self.w_CSDE = w_CSDE
        self.w_CSFE = w_CSFE
        self.w_CSME = w_CSME

        return w_CSDE, w_CSFE, w_CSME

    def fuse_engines(self, csde_tensor, csfe_tensor, csme_tensor):
        """
        Fuse the CSDE, CSFE, and CSME engines using the core fusion equation:
        Ψ_fused = (Ψ_CSDE ⊗ Ψ_CSFE) ⊕ (Ψ_CSME * π103)
        """
        # Apply dynamic weighting
        self.apply_dynamic_weighting(csde_tensor, csfe_tensor, csme_tensor)

        # Apply tensor product: (Ψ_CSDE ⊗ Ψ_CSFE)
        csde_csfe_product = self.tensor_product(csde_tensor, csfe_tensor)

        # Apply scaling: (Ψ_CSME * π103)
        csme_scaled = csme_tensor * PI_103

        # Apply direct sum: (Ψ_CSDE ⊗ Ψ_CSFE) ⊕ (Ψ_CSME * π103)
        fused_tensor = self.direct_sum(csde_csfe_product, csme_scaled)

        return fused_tensor

    def simulate_qft(self, tensor):
        """
        Simulate a Quantum Fourier Transform (QFT) on the tensor.
        """
        # Flatten tensor if needed
        if tensor.dim() > 1:
            tensor = tensor.flatten()

        # Apply FFT as a simulation of QFT
        tensor_complex = torch.fft.fft(tensor.float())

        # Convert back to real tensor (magnitude)
        return torch.abs(tensor_complex)

    def quantum_consensus(self, csde_action, csfe_action, csme_action, confidences):
        """
        Aggregate and resolve action proposals via emergent quantum-like consensus.
        """
        # Convert actions to embeddings
        actions = [csde_action, csfe_action, csme_action]
        action_embeddings = [self._action_to_embedding(action) for action in actions]

        # Create action state vector
        action_state = torch.tensor(action_embeddings, dtype=torch.float32, device=self.device)

        # Weight by confidence
        confidence_tensor = torch.tensor(confidences, dtype=torch.float32, device=self.device)
        weighted_state = action_state * confidence_tensor.unsqueeze(1)

        # Apply QFT simulation
        consensus_state = self.simulate_qft(weighted_state.sum(dim=0))

        # Find dominant action
        dominant_idx = torch.argmax(consensus_state).item()
        consensus_confidence = consensus_state[dominant_idx].item() / consensus_state.sum().item()

        # Map back to action
        consensus_action = actions[dominant_idx] if dominant_idx < len(actions) else "No consensus"

        return consensus_action, consensus_confidence

    def _action_to_embedding(self, action):
        """
        Convert an action (string or list of strings) to a numerical embedding.
        """
        # This is a simplified embedding - in a real implementation,
        # we would use a more sophisticated embedding technique
        if isinstance(action, list):
            # Average hash values of all actions
            return sum(hash(a) % 1000 for a in action) / len(action) / 1000
        else:
            # Use hash value normalized to 0-1 range
            return (hash(action) % 1000) / 1000

def create_sample_data():
    """
    Create sample data for CSDE, CSFE, and CSME engines.
    """
    # Sample CSDE data
    csde_data = {
        "governance": 0.75,  # High governance score
        "data": 0.85,        # High data quality score
        "action": "Implement enhanced access controls",
        "confidence": 0.9    # High confidence
    }

    # Sample CSFE data
    csfe_data = {
        "risk": 0.65,        # Moderate risk score
        "finance": 0.70,     # Moderate financial impact score
        "action": "Allocate additional budget to security",
        "confidence": 0.8    # High confidence
    }

    # Sample CSME data
    csme_data = {
        "bio": 0.40,         # Low biological risk score
        "med_compliance": 0.90,  # High medical compliance score
        "action": "Update patient data protection protocols",
        "confidence": 0.85   # High confidence
    }

    return csde_data, csfe_data, csme_data

def main():
    """
    Main function to demonstrate the Ψ Tensor Core.
    """
    print("=== Ψ Tensor Core Demonstration ===")
    print(f"PyTorch Version: {torch.__version__}")
    print(f"NumPy Version: {np.__version__}")
    print(f"CUDA Available: {torch.cuda.is_available()}")

    try:
        # Initialize Ψ Tensor Core
        print("\nInitializing Ψ Tensor Core...")
        psi_core = PsiTensorCore()

        # Create sample data
        print("\nCreating sample data...")
        csde_data, csfe_data, csme_data = create_sample_data()

        print("\nSample Data:")
        print(f"CSDE Data: {json.dumps(csde_data, indent=2)}")
        print(f"CSFE Data: {json.dumps(csfe_data, indent=2)}")
        print(f"CSME Data: {json.dumps(csme_data, indent=2)}")

        print("\n1. Creating tensors for each engine...")
        csde_tensor = psi_core.create_csde_tensor(
            governance=csde_data["governance"],
            data=csde_data["data"],
            action=csde_data["action"],
            confidence=csde_data["confidence"]
        )

        csfe_tensor = psi_core.create_csfe_tensor(
            risk=csfe_data["risk"],
            finance=csfe_data["finance"],
            action=csfe_data["action"],
            confidence=csfe_data["confidence"]
        )

        csme_tensor = psi_core.create_csme_tensor(
            bio=csme_data["bio"],
            med_compliance=csme_data["med_compliance"],
            action=csme_data["action"],
            confidence=csme_data["confidence"]
        )

        print(f"CSDE Tensor: {csde_tensor}")
        print(f"CSFE Tensor: {csfe_tensor}")
        print(f"CSME Tensor: {csme_tensor}")

        print("\n2. Applying dynamic weighting...")
        print("Using 18/82 principle:")
        print(f"CSDE: 0.18 * {csde_data['governance']:.2f} + 0.82 * {csde_data['data']:.2f}")
        print(f"CSFE: 0.18 * {csfe_data['risk']:.2f} + 0.82 * {csfe_data['finance']:.2f}")
        print(f"CSME: 0.18 * {csme_data['bio']:.2f} + 0.82 * {csme_data['med_compliance']:.2f}")

        w_CSDE, w_CSFE, w_CSME = psi_core.apply_dynamic_weighting(csde_tensor, csfe_tensor, csme_tensor)

        print(f"CSDE Weight: {w_CSDE:.4f}")
        print(f"CSFE Weight: {w_CSFE:.4f}")
        print(f"CSME Weight: {w_CSME:.4f}")
        print(f"Dominant Engine: {'CSDE' if w_CSDE > max(w_CSFE, w_CSME) else 'CSFE' if w_CSFE > w_CSME else 'CSME'}")

        print("\n3. Fusing engines...")
        print(f"Applying fusion equation: Ψ_fused = (Ψ_CSDE ⊗ Ψ_CSFE) ⊕ (Ψ_CSME * π103)")
        print(f"Where π103 = {PI_103}")

        start_time = time.time()

        # Step 1: Tensor product (Ψ_CSDE ⊗ Ψ_CSFE)
        csde_csfe_product = psi_core.tensor_product(csde_tensor, csfe_tensor)
        print(f"Tensor Product (Ψ_CSDE ⊗ Ψ_CSFE) Shape: {csde_csfe_product.shape}")

        # Step 2: Scaling (Ψ_CSME * π103)
        csme_scaled = csme_tensor * PI_103
        print(f"Scaled CSME (Ψ_CSME * π103): {csme_scaled}")

        # Step 3: Direct sum
        fused_tensor = psi_core.direct_sum(csde_csfe_product, csme_scaled)

        fusion_time = time.time() - start_time

        print(f"Fusion Time: {fusion_time:.4f} seconds")
        print(f"Fused Tensor Shape: {fused_tensor.shape}")
        print(f"Fused Tensor (first 10 elements): {fused_tensor.flatten()[:10]}")

        print("\n4. Reaching consensus on actions...")
        print(f"Actions proposed:")
        print(f"  CSDE: '{csde_data['action']}' (Confidence: {csde_data['confidence']:.2f})")
        print(f"  CSFE: '{csfe_data['action']}' (Confidence: {csfe_data['confidence']:.2f})")
        print(f"  CSME: '{csme_data['action']}' (Confidence: {csme_data['confidence']:.2f})")

        consensus_action, consensus_confidence = psi_core.quantum_consensus(
            csde_action=csde_data["action"],
            csfe_action=csfe_data["action"],
            csme_action=csme_data["action"],
            confidences=[csde_data["confidence"], csfe_data["confidence"], csme_data["confidence"]]
        )

        print(f"Consensus Action: {consensus_action}")
        print(f"Consensus Confidence: {consensus_confidence:.4f}")
        print(f"Execute Action: {consensus_confidence > 0.82}")

        print("\n=== Demonstration Complete ===")

    except Exception as e:
        print(f"\nError during demonstration: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
