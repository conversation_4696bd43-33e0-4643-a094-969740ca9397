/**
 * NovaFuse Universal API Connector Registry
 * 
 * This module manages the registration, validation, and retrieval of connector templates.
 */

const fs = require('fs').promises;
const path = require('path');
const Ajv = require('ajv');
const addFormats = require('ajv-formats');

class ConnectorRegistry {
  constructor() {
    this.connectors = new Map();
    this.ajv = new Ajv({ allErrors: true });
    addFormats(this.ajv);
    this.schema = null;
  }

  /**
   * Initialize the connector registry
   */
  async initialize() {
    try {
      // Load the connector schema
      const schemaPath = path.join(__dirname, '../schema/connector-schema.json');
      const schemaContent = await fs.readFile(schemaPath, 'utf8');
      this.schema = JSON.parse(schemaContent);
      
      // Compile the schema validator
      this.validateConnector = this.ajv.compile(this.schema);
      
      // Load all connector templates from the templates directory
      await this.loadConnectorTemplates();
      
      console.log(`Connector Registry initialized with ${this.connectors.size} connectors`);
      return true;
    } catch (error) {
      console.error('Failed to initialize Connector Registry:', error);
      throw error;
    }
  }

  /**
   * Load all connector templates from the templates directory
   */
  async loadConnectorTemplates() {
    try {
      const templatesDir = path.join(__dirname, '../templates');
      const files = await fs.readdir(templatesDir);
      
      for (const file of files) {
        if (file.endsWith('.json')) {
          const filePath = path.join(templatesDir, file);
          const content = await fs.readFile(filePath, 'utf8');
          const template = JSON.parse(content);
          
          await this.registerConnector(template, false);
        }
      }
    } catch (error) {
      console.error('Failed to load connector templates:', error);
      throw error;
    }
  }

  /**
   * Register a new connector template
   * 
   * @param {Object} template - The connector template to register
   * @param {boolean} persist - Whether to persist the template to disk
   * @returns {boolean} - Whether the registration was successful
   */
  async registerConnector(template, persist = true) {
    try {
      // Validate the template against the schema
      const isValid = this.validateConnector(template);
      
      if (!isValid) {
        const errors = this.validateConnector.errors;
        console.error('Invalid connector template:', errors);
        throw new Error(`Invalid connector template: ${JSON.stringify(errors)}`);
      }
      
      // Generate a unique ID for the connector
      const connectorId = `${template.metadata.name.toLowerCase().replace(/[^a-z0-9]/g, '-')}-${template.metadata.version}`;
      
      // Check if the connector already exists
      if (this.connectors.has(connectorId)) {
        console.warn(`Connector ${connectorId} already exists. Overwriting.`);
      }
      
      // Add the connector to the registry
      this.connectors.set(connectorId, template);
      
      // Persist the connector to disk if requested
      if (persist) {
        const templatesDir = path.join(__dirname, '../templates');
        const filePath = path.join(templatesDir, `${connectorId}.json`);
        await fs.writeFile(filePath, JSON.stringify(template, null, 2), 'utf8');
      }
      
      console.log(`Registered connector: ${connectorId}`);
      return true;
    } catch (error) {
      console.error('Failed to register connector:', error);
      throw error;
    }
  }

  /**
   * Get a connector template by ID
   * 
   * @param {string} connectorId - The ID of the connector to retrieve
   * @returns {Object|null} - The connector template or null if not found
   */
  getConnector(connectorId) {
    return this.connectors.get(connectorId) || null;
  }

  /**
   * Get all connector templates
   * 
   * @returns {Array} - Array of all connector templates
   */
  getAllConnectors() {
    return Array.from(this.connectors.values());
  }

  /**
   * Get connectors by category
   * 
   * @param {string} category - The category to filter by
   * @returns {Array} - Array of connector templates in the specified category
   */
  getConnectorsByCategory(category) {
    return Array.from(this.connectors.values())
      .filter(connector => connector.metadata.category === category);
  }

  /**
   * Search connectors by name, description, or tags
   * 
   * @param {string} query - The search query
   * @returns {Array} - Array of matching connector templates
   */
  searchConnectors(query) {
    const lowerQuery = query.toLowerCase();
    
    return Array.from(this.connectors.values())
      .filter(connector => {
        const name = connector.metadata.name.toLowerCase();
        const description = connector.metadata.description.toLowerCase();
        const tags = connector.metadata.tags || [];
        
        return name.includes(lowerQuery) || 
               description.includes(lowerQuery) || 
               tags.some(tag => tag.toLowerCase().includes(lowerQuery));
      });
  }

  /**
   * Update an existing connector template
   * 
   * @param {string} connectorId - The ID of the connector to update
   * @param {Object} template - The updated connector template
   * @returns {boolean} - Whether the update was successful
   */
  async updateConnector(connectorId, template) {
    try {
      // Check if the connector exists
      if (!this.connectors.has(connectorId)) {
        throw new Error(`Connector ${connectorId} not found`);
      }
      
      // Validate the template against the schema
      const isValid = this.validateConnector(template);
      
      if (!isValid) {
        const errors = this.validateConnector.errors;
        console.error('Invalid connector template:', errors);
        throw new Error(`Invalid connector template: ${JSON.stringify(errors)}`);
      }
      
      // Update the connector in the registry
      this.connectors.set(connectorId, template);
      
      // Persist the updated connector to disk
      const templatesDir = path.join(__dirname, '../templates');
      const filePath = path.join(templatesDir, `${connectorId}.json`);
      await fs.writeFile(filePath, JSON.stringify(template, null, 2), 'utf8');
      
      console.log(`Updated connector: ${connectorId}`);
      return true;
    } catch (error) {
      console.error('Failed to update connector:', error);
      throw error;
    }
  }

  /**
   * Delete a connector template
   * 
   * @param {string} connectorId - The ID of the connector to delete
   * @returns {boolean} - Whether the deletion was successful
   */
  async deleteConnector(connectorId) {
    try {
      // Check if the connector exists
      if (!this.connectors.has(connectorId)) {
        throw new Error(`Connector ${connectorId} not found`);
      }
      
      // Remove the connector from the registry
      this.connectors.delete(connectorId);
      
      // Delete the connector file from disk
      const templatesDir = path.join(__dirname, '../templates');
      const filePath = path.join(templatesDir, `${connectorId}.json`);
      await fs.unlink(filePath);
      
      console.log(`Deleted connector: ${connectorId}`);
      return true;
    } catch (error) {
      console.error('Failed to delete connector:', error);
      throw error;
    }
  }
}

// Create and export a singleton instance
const connectorRegistry = new ConnectorRegistry();

module.exports = connectorRegistry;
