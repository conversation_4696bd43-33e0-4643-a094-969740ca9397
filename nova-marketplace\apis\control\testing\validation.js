/**
 * Control Testing API - Validation Schemas
 * 
 * This file defines the validation schemas for the Control Testing API.
 */

const Joi = require('joi');

// Common validation schemas
const idSchema = Joi.string().regex(/^[0-9a-fA-F]{24}$/);
const dateSchema = Joi.date().iso();
const metadataSchema = Joi.object().pattern(Joi.string(), Joi.string());
const tagsSchema = Joi.array().items(Joi.string());

// Control validation schemas
const controlSchema = {
  create: Joi.object({
    name: Joi.string().required(),
    description: Joi.string().required(),
    type: Joi.string().valid('Preventive', 'Detective', 'Corrective', 'Directive').required(),
    category: Joi.string().valid('Administrative', 'Technical', 'Physical').required(),
    status: Joi.string().valid('Active', 'Inactive', 'Deprecated').default('Active'),
    owner: Joi.string().required(),
    implementationDate: dateSchema.required(),
    lastReviewDate: dateSchema,
    nextReviewDate: dateSchema,
    relatedFrameworks: Joi.array().items(idSchema),
    relatedPolicies: Joi.array().items(idSchema),
    tags: tagsSchema,
    metadata: metadataSchema
  }),
  update: Joi.object({
    name: Joi.string(),
    description: Joi.string(),
    type: Joi.string().valid('Preventive', 'Detective', 'Corrective', 'Directive'),
    category: Joi.string().valid('Administrative', 'Technical', 'Physical'),
    status: Joi.string().valid('Active', 'Inactive', 'Deprecated'),
    owner: Joi.string(),
    implementationDate: dateSchema,
    lastReviewDate: dateSchema,
    nextReviewDate: dateSchema,
    relatedFrameworks: Joi.array().items(idSchema),
    relatedPolicies: Joi.array().items(idSchema),
    tags: tagsSchema,
    metadata: metadataSchema
  })
};

// Test Plan validation schemas
const testPlanSchema = {
  create: Joi.object({
    name: Joi.string().required(),
    description: Joi.string().required(),
    status: Joi.string().valid('Draft', 'Active', 'Completed', 'Archived').default('Draft'),
    owner: Joi.string().required(),
    startDate: dateSchema.required(),
    endDate: dateSchema.required(),
    frequency: Joi.string().valid('One-time', 'Daily', 'Weekly', 'Monthly', 'Quarterly', 'Semi-annually', 'Annually').default('One-time'),
    controls: Joi.array().items(Joi.object({
      control: idSchema.required(),
      testProcedure: Joi.string().required(),
      expectedResults: Joi.string().required(),
      assignedTo: Joi.string(),
      dueDate: dateSchema
    })),
    tags: tagsSchema,
    metadata: metadataSchema
  }),
  update: Joi.object({
    name: Joi.string(),
    description: Joi.string(),
    status: Joi.string().valid('Draft', 'Active', 'Completed', 'Archived'),
    owner: Joi.string(),
    startDate: dateSchema,
    endDate: dateSchema,
    frequency: Joi.string().valid('One-time', 'Daily', 'Weekly', 'Monthly', 'Quarterly', 'Semi-annually', 'Annually'),
    controls: Joi.array().items(Joi.object({
      control: idSchema.required(),
      testProcedure: Joi.string().required(),
      expectedResults: Joi.string().required(),
      assignedTo: Joi.string(),
      dueDate: dateSchema
    })),
    tags: tagsSchema,
    metadata: metadataSchema
  }),
  addControl: Joi.object({
    control: idSchema.required(),
    testProcedure: Joi.string().required(),
    expectedResults: Joi.string().required(),
    assignedTo: Joi.string(),
    dueDate: dateSchema
  }),
  updateControl: Joi.object({
    testProcedure: Joi.string(),
    expectedResults: Joi.string(),
    assignedTo: Joi.string(),
    dueDate: dateSchema
  })
};

// Test Result validation schemas
const testResultSchema = {
  create: Joi.object({
    testPlan: idSchema.required(),
    control: idSchema.required(),
    tester: Joi.string().required(),
    testDate: dateSchema.default(new Date().toISOString()),
    status: Joi.string().valid('Passed', 'Failed', 'Inconclusive', 'Not Tested').default('Not Tested'),
    result: Joi.string().required(),
    evidence: Joi.array().items(Joi.object({
      name: Joi.string().required(),
      description: Joi.string(),
      fileUrl: Joi.string(),
      fileType: Joi.string(),
      uploadDate: dateSchema.default(new Date().toISOString())
    })),
    notes: Joi.string(),
    remediation: Joi.object({
      required: Joi.boolean().default(false),
      plan: Joi.string(),
      assignedTo: Joi.string(),
      dueDate: dateSchema,
      status: Joi.string().valid('Not Started', 'In Progress', 'Completed', 'Deferred').default('Not Started'),
      completionDate: dateSchema,
      notes: Joi.string()
    }),
    reviewer: Joi.string(),
    reviewDate: dateSchema,
    reviewNotes: Joi.string(),
    tags: tagsSchema,
    metadata: metadataSchema
  }),
  update: Joi.object({
    tester: Joi.string(),
    testDate: dateSchema,
    status: Joi.string().valid('Passed', 'Failed', 'Inconclusive', 'Not Tested'),
    result: Joi.string(),
    evidence: Joi.array().items(Joi.object({
      name: Joi.string().required(),
      description: Joi.string(),
      fileUrl: Joi.string(),
      fileType: Joi.string(),
      uploadDate: dateSchema
    })),
    notes: Joi.string(),
    remediation: Joi.object({
      required: Joi.boolean(),
      plan: Joi.string(),
      assignedTo: Joi.string(),
      dueDate: dateSchema,
      status: Joi.string().valid('Not Started', 'In Progress', 'Completed', 'Deferred'),
      completionDate: dateSchema,
      notes: Joi.string()
    }),
    reviewer: Joi.string(),
    reviewDate: dateSchema,
    reviewNotes: Joi.string(),
    tags: tagsSchema,
    metadata: metadataSchema
  }),
  addEvidence: Joi.object({
    name: Joi.string().required(),
    description: Joi.string(),
    fileUrl: Joi.string(),
    fileType: Joi.string(),
    uploadDate: dateSchema.default(new Date().toISOString())
  }),
  updateRemediation: Joi.object({
    required: Joi.boolean(),
    plan: Joi.string(),
    assignedTo: Joi.string(),
    dueDate: dateSchema,
    status: Joi.string().valid('Not Started', 'In Progress', 'Completed', 'Deferred'),
    completionDate: dateSchema,
    notes: Joi.string()
  }),
  review: Joi.object({
    reviewer: Joi.string().required(),
    reviewDate: dateSchema.default(new Date().toISOString()),
    reviewNotes: Joi.string(),
    status: Joi.string().valid('Passed', 'Failed', 'Inconclusive').required()
  })
};

// Framework validation schemas
const frameworkSchema = {
  create: Joi.object({
    name: Joi.string().required(),
    description: Joi.string().required(),
    version: Joi.string().required(),
    publisher: Joi.string().required(),
    publishDate: dateSchema.required(),
    controls: Joi.array().items(idSchema),
    categories: Joi.array().items(Joi.object({
      name: Joi.string().required(),
      description: Joi.string(),
      controls: Joi.array().items(idSchema)
    })),
    status: Joi.string().valid('Active', 'Inactive', 'Deprecated').default('Active'),
    tags: tagsSchema,
    metadata: metadataSchema
  }),
  update: Joi.object({
    name: Joi.string(),
    description: Joi.string(),
    version: Joi.string(),
    publisher: Joi.string(),
    publishDate: dateSchema,
    controls: Joi.array().items(idSchema),
    categories: Joi.array().items(Joi.object({
      name: Joi.string().required(),
      description: Joi.string(),
      controls: Joi.array().items(idSchema)
    })),
    status: Joi.string().valid('Active', 'Inactive', 'Deprecated'),
    tags: tagsSchema,
    metadata: metadataSchema
  }),
  addCategory: Joi.object({
    name: Joi.string().required(),
    description: Joi.string(),
    controls: Joi.array().items(idSchema)
  }),
  updateCategory: Joi.object({
    name: Joi.string(),
    description: Joi.string(),
    controls: Joi.array().items(idSchema)
  })
};

// Policy validation schemas
const policySchema = {
  create: Joi.object({
    name: Joi.string().required(),
    description: Joi.string().required(),
    content: Joi.string().required(),
    version: Joi.string().required(),
    status: Joi.string().valid('Draft', 'Active', 'Archived').default('Draft'),
    owner: Joi.string().required(),
    approver: Joi.string(),
    approvalDate: dateSchema,
    effectiveDate: dateSchema,
    expirationDate: dateSchema,
    lastReviewDate: dateSchema,
    nextReviewDate: dateSchema,
    relatedControls: Joi.array().items(idSchema),
    relatedFrameworks: Joi.array().items(idSchema),
    tags: tagsSchema,
    metadata: metadataSchema
  }),
  update: Joi.object({
    name: Joi.string(),
    description: Joi.string(),
    content: Joi.string(),
    version: Joi.string(),
    status: Joi.string().valid('Draft', 'Active', 'Archived'),
    owner: Joi.string(),
    approver: Joi.string(),
    approvalDate: dateSchema,
    effectiveDate: dateSchema,
    expirationDate: dateSchema,
    lastReviewDate: dateSchema,
    nextReviewDate: dateSchema,
    relatedControls: Joi.array().items(idSchema),
    relatedFrameworks: Joi.array().items(idSchema),
    tags: tagsSchema,
    metadata: metadataSchema
  }),
  approve: Joi.object({
    approver: Joi.string().required(),
    approvalDate: dateSchema.default(new Date().toISOString()),
    effectiveDate: dateSchema,
    expirationDate: dateSchema,
    notes: Joi.string()
  })
};

// Dashboard validation schemas
const dashboardSchema = {
  create: Joi.object({
    name: Joi.string().required(),
    description: Joi.string(),
    owner: Joi.string().required(),
    widgets: Joi.array().items(Joi.object({
      name: Joi.string().required(),
      type: Joi.string().valid('ControlStatus', 'TestPlanStatus', 'TestResultTrend', 'FrameworkCompliance', 'RemediationStatus', 'Custom').required(),
      config: Joi.object().required(),
      position: Joi.object({
        x: Joi.number().required(),
        y: Joi.number().required(),
        width: Joi.number().required(),
        height: Joi.number().required()
      }).required()
    })),
    isPublic: Joi.boolean().default(false),
    sharedWith: Joi.array().items(Joi.string()),
    tags: tagsSchema,
    metadata: metadataSchema
  }),
  update: Joi.object({
    name: Joi.string(),
    description: Joi.string(),
    owner: Joi.string(),
    widgets: Joi.array().items(Joi.object({
      name: Joi.string().required(),
      type: Joi.string().valid('ControlStatus', 'TestPlanStatus', 'TestResultTrend', 'FrameworkCompliance', 'RemediationStatus', 'Custom').required(),
      config: Joi.object().required(),
      position: Joi.object({
        x: Joi.number().required(),
        y: Joi.number().required(),
        width: Joi.number().required(),
        height: Joi.number().required()
      }).required()
    })),
    isPublic: Joi.boolean(),
    sharedWith: Joi.array().items(Joi.string()),
    tags: tagsSchema,
    metadata: metadataSchema
  }),
  addWidget: Joi.object({
    name: Joi.string().required(),
    type: Joi.string().valid('ControlStatus', 'TestPlanStatus', 'TestResultTrend', 'FrameworkCompliance', 'RemediationStatus', 'Custom').required(),
    config: Joi.object().required(),
    position: Joi.object({
      x: Joi.number().required(),
      y: Joi.number().required(),
      width: Joi.number().required(),
      height: Joi.number().required()
    }).required()
  }),
  updateWidget: Joi.object({
    name: Joi.string(),
    type: Joi.string().valid('ControlStatus', 'TestPlanStatus', 'TestResultTrend', 'FrameworkCompliance', 'RemediationStatus', 'Custom'),
    config: Joi.object(),
    position: Joi.object({
      x: Joi.number(),
      y: Joi.number(),
      width: Joi.number(),
      height: Joi.number()
    })
  }),
  share: Joi.object({
    users: Joi.array().items(Joi.string()).required(),
    isPublic: Joi.boolean()
  })
};

// Query validation schemas
const querySchema = {
  pagination: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(10),
    sort: Joi.string().default('-createdAt')
  }),
  controlFilters: Joi.object({
    name: Joi.string(),
    type: Joi.string().valid('Preventive', 'Detective', 'Corrective', 'Directive'),
    category: Joi.string().valid('Administrative', 'Technical', 'Physical'),
    status: Joi.string().valid('Active', 'Inactive', 'Deprecated'),
    owner: Joi.string(),
    implementationDateFrom: dateSchema,
    implementationDateTo: dateSchema,
    lastReviewDateFrom: dateSchema,
    lastReviewDateTo: dateSchema,
    nextReviewDateFrom: dateSchema,
    nextReviewDateTo: dateSchema,
    relatedFramework: idSchema,
    relatedPolicy: idSchema,
    tag: Joi.string()
  }),
  testPlanFilters: Joi.object({
    name: Joi.string(),
    status: Joi.string().valid('Draft', 'Active', 'Completed', 'Archived'),
    owner: Joi.string(),
    startDateFrom: dateSchema,
    startDateTo: dateSchema,
    endDateFrom: dateSchema,
    endDateTo: dateSchema,
    frequency: Joi.string().valid('One-time', 'Daily', 'Weekly', 'Monthly', 'Quarterly', 'Semi-annually', 'Annually'),
    control: idSchema,
    assignedTo: Joi.string(),
    tag: Joi.string()
  }),
  testResultFilters: Joi.object({
    testPlan: idSchema,
    control: idSchema,
    tester: Joi.string(),
    testDateFrom: dateSchema,
    testDateTo: dateSchema,
    status: Joi.string().valid('Passed', 'Failed', 'Inconclusive', 'Not Tested'),
    remediationRequired: Joi.boolean(),
    remediationStatus: Joi.string().valid('Not Started', 'In Progress', 'Completed', 'Deferred'),
    reviewer: Joi.string(),
    reviewDateFrom: dateSchema,
    reviewDateTo: dateSchema,
    tag: Joi.string()
  }),
  frameworkFilters: Joi.object({
    name: Joi.string(),
    version: Joi.string(),
    publisher: Joi.string(),
    publishDateFrom: dateSchema,
    publishDateTo: dateSchema,
    status: Joi.string().valid('Active', 'Inactive', 'Deprecated'),
    control: idSchema,
    category: Joi.string(),
    tag: Joi.string()
  }),
  policyFilters: Joi.object({
    name: Joi.string(),
    status: Joi.string().valid('Draft', 'Active', 'Archived'),
    owner: Joi.string(),
    approver: Joi.string(),
    approvalDateFrom: dateSchema,
    approvalDateTo: dateSchema,
    effectiveDateFrom: dateSchema,
    effectiveDateTo: dateSchema,
    expirationDateFrom: dateSchema,
    expirationDateTo: dateSchema,
    lastReviewDateFrom: dateSchema,
    lastReviewDateTo: dateSchema,
    nextReviewDateFrom: dateSchema,
    nextReviewDateTo: dateSchema,
    relatedControl: idSchema,
    relatedFramework: idSchema,
    tag: Joi.string()
  }),
  dashboardFilters: Joi.object({
    name: Joi.string(),
    owner: Joi.string(),
    isPublic: Joi.boolean(),
    sharedWith: Joi.string(),
    widgetType: Joi.string().valid('ControlStatus', 'TestPlanStatus', 'TestResultTrend', 'FrameworkCompliance', 'RemediationStatus', 'Custom'),
    tag: Joi.string()
  })
};

module.exports = {
  control: controlSchema,
  testPlan: testPlanSchema,
  testResult: testResultSchema,
  framework: frameworkSchema,
  policy: policySchema,
  dashboard: dashboardSchema,
  query: querySchema
};
