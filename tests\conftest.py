"""
Pytest configuration for NovaCortex tests.
"""
import pytest
import asyncio
from typing import Generator, AsyncGenerator
from tests.novacortex.mock_novacortex import NovaCortex

# Add async test support
def pytest_configure(config):
    config.addinivalue_line("markers", "asyncio: mark test as async")

# Fixtures
@pytest.fixture
def event_loop():
    """Create an instance of the default event loop for each test case."""
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()

@pytest.fixture
async def nova_cortex() -> AsyncGenerator[NovaCortex, None]:
    """Provide an initialized NovaCortex instance for testing."""
    cortex = NovaCortex()
    await cortex.initialize()
    try:
        yield cortex
    finally:
        # Any cleanup code would go here
        pass
