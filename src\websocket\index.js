/**
 * WebSocket Module
 * 
 * This module exports all the components of the WebSocket system.
 */

const WebSocketServer = require('./websocket-server');
const WebSocketClient = require('./websocket-client');
const {
  <PERSON><PERSON><PERSON>age<PERSON><PERSON><PERSON>,
  TensorMessageHandler,
  VisualizationMessageHandler,
  AnalyticsMessageHandler
} = require('./message-handlers');

/**
 * Create a WebSocket server
 * @param {Object} options - Configuration options
 * @returns {WebSocketServer} - WebSocket server instance
 */
function createWebSocketServer(options = {}) {
  return new WebSocketServer(options);
}

/**
 * Create a WebSocket client
 * @param {Object} options - Configuration options
 * @returns {WebSocketClient} - WebSocket client instance
 */
function createWebSocketClient(options = {}) {
  return new WebSocketClient(options);
}

/**
 * Create a message handler
 * @param {string} type - Handler type ('tensor', 'visualization', 'analytics')
 * @param {Object} options - Configuration options
 * @returns {BaseMessageHandler} - Message handler instance
 */
function createMessageHandler(type, options = {}) {
  switch (type) {
    case 'tensor':
      return new TensorMessageHandler(options);
      
    case 'visualization':
      return new VisualizationMessageHandler(options);
      
    case 'analytics':
      return new AnalyticsMessageHandler(options);
      
    default:
      throw new Error(`Unknown message handler type: ${type}`);
  }
}

/**
 * Create a real-time communication system
 * @param {Object} options - Configuration options
 * @returns {Object} - Real-time communication system
 */
function createRealTimeCommunication(options = {}) {
  // Create WebSocket server
  const server = createWebSocketServer({
    port: options.port || 3001,
    path: options.path || '/ws',
    enableLogging: options.enableLogging,
    enableMetrics: options.enableMetrics,
    heartbeatInterval: options.heartbeatInterval
  });
  
  // Create message handlers
  const handlers = {
    tensor: options.tensorAdapter ? createMessageHandler('tensor', {
      tensorAdapter: options.tensorAdapter,
      enableLogging: options.enableLogging
    }) : null,
    
    visualization: options.visualizationAdapter ? createMessageHandler('visualization', {
      visualizationAdapter: options.visualizationAdapter,
      enableLogging: options.enableLogging
    }) : null,
    
    analytics: options.analyticsAdapter ? createMessageHandler('analytics', {
      analyticsAdapter: options.analyticsAdapter,
      enableLogging: options.enableLogging
    }) : null
  };
  
  // Set up message handling
  server.on('message', async (data) => {
    const { clientId, message } = data;
    
    // Determine handler based on message type
    let handler;
    
    if (message.component === 'tensor' && handlers.tensor) {
      handler = handlers.tensor;
    } else if (message.component === 'visualization' && handlers.visualization) {
      handler = handlers.visualization;
    } else if (message.component === 'analytics' && handlers.analytics) {
      handler = handlers.analytics;
    }
    
    if (handler) {
      try {
        // Handle message
        const result = await handler.handleMessage(message, message.channel);
        
        // Send response
        server.publish(`response:${clientId}`, {
          id: message.id,
          result
        });
      } catch (error) {
        // Send error
        server.publish(`response:${clientId}`, {
          id: message.id,
          error: error.message
        });
        
        if (options.enableLogging) {
          console.error(`Error handling message from client ${clientId}:`, error);
        }
      }
    }
  });
  
  // Start server
  server.start().catch((error) => {
    if (options.enableLogging) {
      console.error('Error starting WebSocket server:', error);
    }
  });
  
  return {
    server,
    handlers,
    
    /**
     * Create a client for a component
     * @param {string} componentType - Component type ('tensor', 'visualization', 'analytics')
     * @param {Object} clientOptions - Client options
     * @returns {WebSocketClient} - WebSocket client instance
     */
    createClient(componentType, clientOptions = {}) {
      const client = createWebSocketClient({
        url: `ws://localhost:${options.port || 3001}/ws`,
        clientId: `${componentType}-${Date.now()}`,
        enableLogging: options.enableLogging,
        ...clientOptions
      });
      
      // Set up response handling
      client.on('connected', async () => {
        // Subscribe to response channel
        await client.subscribe(`response:${client.options.clientId}`);
      });
      
      return client;
    },
    
    /**
     * Stop the real-time communication system
     * @returns {Promise<void>} - Promise that resolves when stopped
     */
    async stop() {
      await server.stop();
    }
  };
}

module.exports = {
  WebSocketServer,
  WebSocketClient,
  BaseMessageHandler,
  TensorMessageHandler,
  VisualizationMessageHandler,
  AnalyticsMessageHandler,
  createWebSocketServer,
  createWebSocketClient,
  createMessageHandler,
  createRealTimeCommunication
};
