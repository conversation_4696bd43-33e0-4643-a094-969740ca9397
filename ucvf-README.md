# Universal Compliance Visualization Framework (UCVF)

The Universal Compliance Visualization Framework (UCVF) transforms complex compliance data into intuitive visualizations tailored to different stakeholders. It provides role-based dashboards and reports that make compliance data actionable for everyone from board members to technical staff.

## Overview

UCVF enables organizations to present compliance data in a way that is most relevant and actionable for each stakeholder, helping them understand their compliance posture and make informed decisions.

## Key Features

- **Role-Based Visualization**: Tailors visualizations to the specific needs and responsibilities of different stakeholders
- **Multi-Framework Support**: Handles data from multiple compliance frameworks (GDPR, HIPAA, SOC2, etc.)
- **Flexible Templates**: Provides customizable templates for different visualization types
- **Data Transformation**: Intelligently transforms and aggregates compliance data based on role and context
- **Extensible Architecture**: Easily add new roles, templates, and visualization types
- **Real-Time Updates**: Supports real-time updates through event-driven architecture
- **Integration with Testing Frameworks**: Seamlessly integrates with compliance testing frameworks like UCTF

## Architecture

The UCVF consists of several core components:

- **Visualization Engine**: The main engine that orchestrates the visualization process
- **Role Manager**: Manages stakeholder roles and their configurations
- **Template Manager**: Manages visualization templates for different roles and visualization types
- **Data Transformer**: Transforms compliance data based on role and template

## Supported Roles

The UCVF supports the following stakeholder roles out of the box:

- **Board of Directors**: High-level oversight of compliance posture and risk
- **CISO**: Technical and strategic oversight of security and compliance
- **Compliance Manager**: Day-to-day management of compliance activities
- **IT Manager**: Management of IT systems and infrastructure
- **Auditor**: Independent verification of compliance

## Visualization Types

The UCVF supports the following visualization types:

- **Dashboard**: Interactive dashboard with multiple visualization components
- **Report**: Structured report with detailed compliance information
- **Test Result**: Specialized visualizations for compliance test results

## Installation

```bash
# Clone the repository
git clone https://github.com/novafuse/ucvf.git
cd ucvf

# Install the package
pip install -e .
```

## Usage

Here's a simple example of how to use the UCVF:

```python
from ucvf import VisualizationEngine
from ucvf.utils.data_utils import load_compliance_data

# Initialize the Visualization Engine
engine = VisualizationEngine()

# Load compliance data
compliance_data = load_compliance_data('compliance_data.json')

# Generate a visualization for a specific role and type
visualization = engine.generate_visualization(
    data=compliance_data,
    role='board',
    visualization_type='dashboard'
)

# Print the visualization
print(f"Visualization title: {visualization['title']}")
print(f"Visualization subtitle: {visualization['subtitle']}")
print(f"Number of charts: {len(visualization['charts'])}")
print(f"Number of tables: {len(visualization['tables'])}")
```

## Extending the Framework

### Adding a Custom Role

```python
from ucvf import VisualizationEngine, RoleManager

# Initialize the Visualization Engine
engine = VisualizationEngine()

# Define a custom role configuration
custom_role_config = {
    'name': 'security_analyst',
    'description': 'Security analyst responsible for investigating security incidents',
    'visualization_preferences': {
        'complexity': 'high',
        'focus_areas': ['security_incidents', 'vulnerabilities'],
        'data_granularity': 'detailed'
    }
}

# Register the custom role
engine.role_manager.register_role('security_analyst', custom_role_config)

# Generate a visualization for the custom role
visualization = engine.generate_visualization(
    data=compliance_data,
    role='security_analyst',
    visualization_type='dashboard'
)
```

### Adding a Custom Template

```python
from ucvf import VisualizationEngine
from ucvf.core.template_manager import VisualizationTemplate

# Define a custom template class
class CustomDashboardTemplate(VisualizationTemplate):
    def __init__(self):
        super().__init__(
            name="Custom Dashboard",
            description="Custom dashboard template for security analysts"
        )

    def apply(self, data, context=None):
        # Custom implementation to transform data into a visualization
        # ...
        return {
            'title': 'Custom Security Dashboard',
            'subtitle': 'Security incidents and vulnerabilities',
            'charts': [
                # Custom charts
            ],
            'tables': [
                # Custom tables
            ]
        }

# Initialize the Visualization Engine
engine = VisualizationEngine()

# Register the custom template
engine.template_manager.register_template(
    role='security_analyst',
    template_type='dashboard',
    template=CustomDashboardTemplate()
)

# Generate a visualization using the custom template
visualization = engine.generate_visualization(
    data=compliance_data,
    role='security_analyst',
    visualization_type='dashboard'
)
```

## Integration with UCTF

UCVF can be integrated with the Universal Control Testing Framework (UCTF) to visualize test results:

```python
from ucvf import initialize_test_result_subscriber
from uctf import TestEngine

# Initialize the Test Result Subscriber
test_result_subscriber = initialize_test_result_subscriber()

# Initialize the Test Engine
engine = TestEngine()

# Run a test (the test result will be automatically visualized)
test_run = engine.run_test('gdpr_data_protection', {
    'strict_mode': True,
    'target_system': 'customer_database'
})
```

## License

Copyright © 2023-2025 NovaFuse. All rights reserved.

## Contributing

Please read [CONTRIBUTING.md](CONTRIBUTING.md) for details on our code of conduct and the process for submitting pull requests.