# NovaConnect UAC Configuration Guide

This document provides comprehensive instructions for configuring NovaConnect UAC for different environments, including Google Cloud Marketplace.

## Overview

NovaConnect UAC can be configured in several ways:

1. **Environment Variables**: For runtime configuration
2. **Configuration Files**: For static configuration
3. **Feature Flags**: For dynamic feature configuration
4. **Secrets Management**: For secure storage of sensitive information

## Environment Variables

NovaConnect UAC uses environment variables for runtime configuration. These can be set in the following ways:

1. **Local Development**: Using a `.env` file
2. **Docker**: Using environment variables in `docker-compose.yml` or `docker run`
3. **Kubernetes**: Using ConfigMaps and Secrets
4. **Google Cloud Marketplace**: Using the Marketplace configuration UI

### Core Environment Variables

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `NODE_ENV` | Environment (development, staging, production) | `development` | Yes |
| `PORT` | HTTP port | `3001` | Yes |
| `HOST` | HTTP host | `0.0.0.0` | Yes |
| `LOG_LEVEL` | Logging level (debug, info, warn, error) | `info` | No |
| `API_KEY` | API key for authentication | - | Yes |
| `JWT_SECRET` | Secret for JWT authentication | - | Yes |
| `CORS_ORIGIN` | CORS allowed origins | `*` | No |

### Database Configuration

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `MONGODB_URI` | MongoDB connection URI | `mongodb://localhost:27017/novafuse` | Yes |
| `MONGODB_USER` | MongoDB username | - | No |
| `MONGODB_PASSWORD` | MongoDB password | - | No |
| `MONGODB_DATABASE` | MongoDB database name | `novafuse` | No |
| `MONGODB_AUTH_SOURCE` | MongoDB authentication source | `admin` | No |
| `MONGODB_REPLICA_SET` | MongoDB replica set name | - | No |
| `MONGODB_SSL` | Enable MongoDB SSL | `false` | No |
| `MONGODB_POOL_SIZE` | MongoDB connection pool size | `10` | No |

### Redis Configuration

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `REDIS_URI` | Redis connection URI | `redis://localhost:6379` | Yes |
| `REDIS_HOST` | Redis host | `localhost` | No |
| `REDIS_PORT` | Redis port | `6379` | No |
| `REDIS_PASSWORD` | Redis password | - | No |
| `REDIS_DB` | Redis database number | `0` | No |
| `REDIS_SSL` | Enable Redis SSL | `false` | No |
| `REDIS_CLUSTER` | Enable Redis cluster mode | `false` | No |

### Performance Configuration

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `CLUSTER_ENABLED` | Enable cluster mode | `false` | No |
| `CLUSTER_WORKERS` | Number of cluster workers | Number of CPU cores | No |
| `CACHE_ENABLED` | Enable caching | `true` | No |
| `CACHE_TTL` | Cache TTL in seconds | `300` | No |
| `COMPRESSION_ENABLED` | Enable response compression | `true` | No |
| `RATE_LIMIT_ENABLED` | Enable rate limiting | `true` | No |
| `RATE_LIMIT_WINDOW_MS` | Rate limit window in milliseconds | `60000` | No |
| `RATE_LIMIT_MAX` | Maximum requests per window | `100` | No |

### Security Configuration

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `HELMET_ENABLED` | Enable Helmet security headers | `true` | No |
| `CSRF_ENABLED` | Enable CSRF protection | `true` | No |
| `CSRF_SECRET` | CSRF secret | - | No |
| `IP_FILTERING_ENABLED` | Enable IP filtering | `false` | No |
| `ALLOWED_IPS` | Comma-separated list of allowed IPs | - | No |
| `SENTRY_ENABLED` | Enable Sentry error reporting | `false` | No |
| `SENTRY_DSN` | Sentry DSN | - | No |
| `SENTRY_DSN_SECRET_NAME` | Sentry DSN secret name in Secret Manager | - | No |

### Google Cloud Configuration

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `GCP_PROJECT_ID` | Google Cloud project ID | - | Yes (for GCP) |
| `GCP_REGION` | Google Cloud region | `us-central1` | No |
| `GCP_ZONE` | Google Cloud zone | `us-central1-a` | No |
| `GCP_MONITORING_ENABLED` | Enable Google Cloud Monitoring | `true` | No |
| `GCP_LOGGING_ENABLED` | Enable Google Cloud Logging | `true` | No |
| `GCP_ERROR_REPORTING_ENABLED` | Enable Google Cloud Error Reporting | `true` | No |
| `GCP_TRACING_ENABLED` | Enable Google Cloud Trace | `true` | No |
| `GCP_PROFILING_ENABLED` | Enable Google Cloud Profiler | `true` | No |
| `GCP_DEBUGGING_ENABLED` | Enable Google Cloud Debug Agent | `false` | No |
| `GCP_SECRET_MANAGER_ENABLED` | Enable Google Cloud Secret Manager | `true` | No |

### Feature Flag Configuration

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `FEATURE_FLAG_ENABLED` | Enable feature flags | `true` | No |
| `FEATURE_FLAG_SOURCE` | Feature flag source (file, database, api) | `file` | No |
| `FEATURE_FLAG_FILE_PATH` | Path to feature flag file | `config/feature_flags.json` | No |
| `FEATURE_FLAG_API_URL` | URL of feature flag API | - | No |
| `FEATURE_FLAG_API_KEY` | API key for feature flag API | - | No |
| `DEFAULT_TIER` | Default product tier | `core` | No |

## Configuration Files

NovaConnect UAC uses configuration files for static configuration. These files are located in the `config` directory:

### config/default.json

Default configuration for all environments:

```json
{
  "server": {
    "port": 3001,
    "host": "0.0.0.0",
    "cors": {
      "origin": "*",
      "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
      "allowedHeaders": ["Content-Type", "Authorization", "X-API-Key", "X-Request-ID"],
      "exposedHeaders": ["X-Request-ID", "X-RateLimit-Limit", "X-RateLimit-Remaining", "X-RateLimit-Reset"],
      "credentials": true,
      "maxAge": 86400
    }
  },
  "database": {
    "mongodb": {
      "uri": "mongodb://localhost:27017/novafuse",
      "options": {
        "useNewUrlParser": true,
        "useUnifiedTopology": true,
        "poolSize": 10
      }
    },
    "redis": {
      "uri": "redis://localhost:6379",
      "options": {
        "db": 0
      }
    }
  },
  "logging": {
    "level": "info",
    "format": "json",
    "colorize": true,
    "timestamp": true
  },
  "security": {
    "helmet": {
      "enabled": true
    },
    "csrf": {
      "enabled": true
    },
    "rateLimit": {
      "enabled": true,
      "windowMs": 60000,
      "max": 100
    },
    "ipFiltering": {
      "enabled": false,
      "allowedIps": []
    }
  },
  "performance": {
    "cluster": {
      "enabled": false
    },
    "cache": {
      "enabled": true,
      "ttl": 300
    },
    "compression": {
      "enabled": true
    }
  },
  "featureFlags": {
    "enabled": true,
    "source": "file",
    "filePath": "config/feature_flags.json",
    "defaultTier": "core"
  },
  "monitoring": {
    "metrics": {
      "enabled": true,
      "path": "/metrics"
    },
    "health": {
      "enabled": true,
      "path": "/health"
    },
    "sentry": {
      "enabled": false
    }
  },
  "gcp": {
    "enabled": false,
    "projectId": "",
    "region": "us-central1",
    "zone": "us-central1-a",
    "monitoring": {
      "enabled": false
    },
    "logging": {
      "enabled": false
    },
    "errorReporting": {
      "enabled": false
    },
    "tracing": {
      "enabled": false
    },
    "profiling": {
      "enabled": false
    },
    "debugging": {
      "enabled": false
    },
    "secretManager": {
      "enabled": false
    }
  }
}
```

### config/production.json

Production-specific configuration:

```json
{
  "server": {
    "cors": {
      "origin": "https://app.novafuse.io"
    }
  },
  "logging": {
    "level": "info",
    "colorize": false
  },
  "security": {
    "helmet": {
      "enabled": true
    },
    "csrf": {
      "enabled": true
    },
    "rateLimit": {
      "enabled": true,
      "windowMs": 60000,
      "max": 100
    },
    "ipFiltering": {
      "enabled": true,
      "allowedIps": []
    }
  },
  "performance": {
    "cluster": {
      "enabled": true
    },
    "cache": {
      "enabled": true,
      "ttl": 300
    },
    "compression": {
      "enabled": true
    }
  },
  "monitoring": {
    "sentry": {
      "enabled": true
    }
  },
  "gcp": {
    "enabled": true,
    "monitoring": {
      "enabled": true
    },
    "logging": {
      "enabled": true
    },
    "errorReporting": {
      "enabled": true
    },
    "tracing": {
      "enabled": true
    },
    "profiling": {
      "enabled": true
    },
    "debugging": {
      "enabled": false
    },
    "secretManager": {
      "enabled": true
    }
  }
}
```

## Feature Flags

NovaConnect UAC uses feature flags for dynamic feature configuration. These are defined in the `config/feature_flags.json` file:

```json
[
  {
    "id": "core.basic_connectors",
    "name": "Basic Connectors",
    "description": "Access to basic connectors",
    "category": "core",
    "enabled": true,
    "tiers": ["core", "secure", "enterprise", "ai_boost"]
  },
  {
    "id": "core.manual_execution",
    "name": "Manual Execution",
    "description": "Manual execution of connectors",
    "category": "core",
    "enabled": true,
    "tiers": ["core", "secure", "enterprise", "ai_boost"],
    "limits": {
      "core": { "operations_per_day": 100 },
      "secure": { "operations_per_day": 1000 },
      "enterprise": { "operations_per_day": 10000 },
      "ai_boost": { "operations_per_day": -1 }
    }
  },
  {
    "id": "workflow.scheduled",
    "name": "Scheduled Execution",
    "description": "Scheduled execution of connectors",
    "category": "workflow",
    "enabled": true,
    "tiers": ["secure", "enterprise", "ai_boost"],
    "limits": {
      "secure": { "schedules": 10 },
      "enterprise": { "schedules": 100 },
      "ai_boost": { "schedules": -1 }
    }
  },
  {
    "id": "workflow.triggers",
    "name": "Event Triggers",
    "description": "Event-based triggers for connectors",
    "category": "workflow",
    "enabled": true,
    "tiers": ["secure", "enterprise", "ai_boost"],
    "limits": {
      "secure": { "triggers": 10 },
      "enterprise": { "triggers": 100 },
      "ai_boost": { "triggers": -1 }
    }
  },
  {
    "id": "export.config",
    "name": "Configuration Export",
    "description": "Export connector configurations",
    "category": "export",
    "enabled": true,
    "tiers": ["secure", "enterprise", "ai_boost"]
  },
  {
    "id": "import.config",
    "name": "Configuration Import",
    "description": "Import connector configurations",
    "category": "import",
    "enabled": true,
    "tiers": ["secure", "enterprise", "ai_boost"]
  },
  {
    "id": "security.audit",
    "name": "Security Audit",
    "description": "Security audit logging",
    "category": "security",
    "enabled": true,
    "tiers": ["secure", "enterprise", "ai_boost"]
  },
  {
    "id": "security.encryption",
    "name": "Field Encryption",
    "description": "Field-level encryption",
    "category": "security",
    "enabled": true,
    "tiers": ["secure", "enterprise", "ai_boost"]
  },
  {
    "id": "monitoring.alerts",
    "name": "Monitoring Alerts",
    "description": "Monitoring and alerting",
    "category": "monitoring",
    "enabled": true,
    "tiers": ["enterprise", "ai_boost"]
  },
  {
    "id": "monitoring.dashboards",
    "name": "Custom Dashboards",
    "description": "Custom monitoring dashboards",
    "category": "monitoring",
    "enabled": true,
    "tiers": ["enterprise", "ai_boost"],
    "limits": {
      "enterprise": { "dashboards": 5 },
      "ai_boost": { "dashboards": -1 }
    }
  },
  {
    "id": "analytics.reports",
    "name": "Analytics Reports",
    "description": "Analytics and reporting",
    "category": "analytics",
    "enabled": true,
    "tiers": ["enterprise", "ai_boost"],
    "limits": {
      "enterprise": { "reports": 10 },
      "ai_boost": { "reports": -1 }
    }
  },
  {
    "id": "analytics.export",
    "name": "Analytics Export",
    "description": "Export analytics data",
    "category": "analytics",
    "enabled": true,
    "tiers": ["enterprise", "ai_boost"]
  },
  {
    "id": "ai.normalization",
    "name": "AI Normalization",
    "description": "AI-powered data normalization",
    "category": "ai",
    "enabled": true,
    "tiers": ["ai_boost"],
    "limits": {
      "ai_boost": { "operations_per_day": 1000 }
    }
  },
  {
    "id": "ai.insights",
    "name": "AI Insights",
    "description": "AI-powered insights",
    "category": "ai",
    "enabled": true,
    "tiers": ["ai_boost"],
    "limits": {
      "ai_boost": { "operations_per_day": 100 }
    }
  },
  {
    "id": "governance.policies",
    "name": "Governance Policies",
    "description": "Governance policy management",
    "category": "governance",
    "enabled": true,
    "tiers": ["enterprise", "ai_boost"],
    "limits": {
      "enterprise": { "policies": 10 },
      "ai_boost": { "policies": -1 }
    }
  },
  {
    "id": "governance.compliance",
    "name": "Compliance Reporting",
    "description": "Compliance reporting",
    "category": "governance",
    "enabled": true,
    "tiers": ["enterprise", "ai_boost"]
  }
]
```

## Subscription Tiers

NovaConnect UAC offers the following subscription tiers for Google Cloud Marketplace:

### NovaConnect Core

Basic functionality for small projects:

- Basic connectors
- Manual execution (limited to 100 operations per day)
- API access
- Basic documentation

### NovaConnect Secure

Standard functionality for growing teams:

- All Core features
- Scheduled execution (limited to 10 schedules)
- Event triggers (limited to 10 triggers)
- Configuration export/import
- Security audit logging
- Field-level encryption
- Enhanced documentation

### NovaConnect Enterprise

Advanced functionality for professional teams:

- All Secure features
- Monitoring alerts
- Custom dashboards (limited to 5 dashboards)
- Analytics and reporting (limited to 10 reports)
- Analytics export
- Governance policy management (limited to 10 policies)
- Compliance reporting
- Premium support

### NovaConnect AI Boost

Enterprise-grade functionality with AI capabilities:

- All Enterprise features
- AI-powered data normalization
- AI-powered insights
- Unlimited schedules, triggers, dashboards, reports, and policies
- Priority support

## Secrets Management

NovaConnect UAC uses Google Cloud Secret Manager for secure storage of sensitive information:

### Required Secrets

| Secret Name | Description | Required |
|-------------|-------------|----------|
| `jwt-secret` | Secret for JWT authentication | Yes |
| `api-key` | API key for authentication | Yes |
| `mongodb-password` | MongoDB password | Yes |
| `redis-password` | Redis password | Yes |
| `sentry-dsn` | Sentry DSN | No |

### Creating Secrets

```bash
# Create secrets
gcloud secrets create jwt-secret --replication-policy="automatic"
gcloud secrets create api-key --replication-policy="automatic"
gcloud secrets create mongodb-password --replication-policy="automatic"
gcloud secrets create redis-password --replication-policy="automatic"
gcloud secrets create sentry-dsn --replication-policy="automatic"

# Add secret versions
echo -n "your-jwt-secret" | gcloud secrets versions add jwt-secret --data-file=-
echo -n "your-api-key" | gcloud secrets versions add api-key --data-file=-
echo -n "your-mongodb-password" | gcloud secrets versions add mongodb-password --data-file=-
echo -n "your-redis-password" | gcloud secrets versions add redis-password --data-file=-
echo -n "your-sentry-dsn" | gcloud secrets versions add sentry-dsn --data-file=-

# Grant access to service account
gcloud secrets add-iam-policy-binding jwt-secret \
  --member="serviceAccount:<EMAIL>" \
  --role="roles/secretmanager.secretAccessor"
```

## Google Cloud Marketplace Configuration

NovaConnect UAC can be configured for Google Cloud Marketplace using the following parameters:

### Required Parameters

| Parameter | Description | Default |
|-----------|-------------|---------|
| `name` | Deployment name | `novafuse-uac` |
| `namespace` | Kubernetes namespace | `default` |
| `tier` | Subscription tier (core, secure, enterprise, ai_boost) | `core` |

### Optional Parameters

| Parameter | Description | Default |
|-----------|-------------|---------|
| `replicas` | Number of replicas | `3` |
| `mongodb.uri` | MongoDB connection URI | - |
| `redis.uri` | Redis connection URI | - |
| `image.repository` | Docker image repository | `gcr.io/novafuse/novafuse-uac` |
| `image.tag` | Docker image tag | `latest` |
| `resources.requests.cpu` | CPU request | `100m` |
| `resources.requests.memory` | Memory request | `256Mi` |
| `resources.limits.cpu` | CPU limit | `500m` |
| `resources.limits.memory` | Memory limit | `512Mi` |

## Conclusion

This configuration guide provides comprehensive instructions for configuring NovaConnect UAC for different environments, including Google Cloud Marketplace. By following these instructions, you can ensure that NovaConnect UAC is properly configured for your specific needs.
