const { v4: uuidv4 } = require('uuid');
const models = require('./models');

/**
 * Get a list of disclosures
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getDisclosures = (req, res) => {
  try {
    const { page = 1, limit = 10, status, regulationType, jurisdiction, reportingFrequency, dueBefore, dueAfter, sortBy = 'nextDueDate', sortOrder = 'asc' } = req.query;
    
    // Convert page and limit to numbers
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);
    
    // Filter disclosures based on query parameters
    let filteredDisclosures = [...models.disclosures];
    
    if (status) {
      filteredDisclosures = filteredDisclosures.filter(disclosure => disclosure.status === status);
    }
    
    if (regulationType) {
      filteredDisclosures = filteredDisclosures.filter(disclosure => disclosure.regulationType === regulationType);
    }
    
    if (jurisdiction) {
      filteredDisclosures = filteredDisclosures.filter(disclosure => disclosure.jurisdiction === jurisdiction);
    }
    
    if (reportingFrequency) {
      filteredDisclosures = filteredDisclosures.filter(disclosure => disclosure.reportingFrequency === reportingFrequency);
    }
    
    if (dueBefore) {
      filteredDisclosures = filteredDisclosures.filter(disclosure => disclosure.nextDueDate <= dueBefore);
    }
    
    if (dueAfter) {
      filteredDisclosures = filteredDisclosures.filter(disclosure => disclosure.nextDueDate >= dueAfter);
    }
    
    // Sort disclosures
    filteredDisclosures.sort((a, b) => {
      if (sortOrder.toLowerCase() === 'desc') {
        return a[sortBy] > b[sortBy] ? -1 : 1;
      }
      return a[sortBy] > b[sortBy] ? 1 : -1;
    });
    
    // Paginate results
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = pageNum * limitNum;
    const paginatedDisclosures = filteredDisclosures.slice(startIndex, endIndex);
    
    // Calculate pagination info
    const totalDisclosures = filteredDisclosures.length;
    const totalPages = Math.ceil(totalDisclosures / limitNum);
    
    res.json({
      data: paginatedDisclosures,
      pagination: {
        total: totalDisclosures,
        page: pageNum,
        limit: limitNum,
        pages: totalPages
      }
    });
  } catch (error) {
    console.error('Error in getDisclosures:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get a specific disclosure by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getDisclosureById = (req, res) => {
  try {
    const { id } = req.params;
    const disclosure = models.disclosures.find(d => d.id === id);
    
    if (!disclosure) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Disclosure with ID ${id} not found`
      });
    }
    
    res.json({ data: disclosure });
  } catch (error) {
    console.error('Error in getDisclosureById:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Create a new disclosure
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const createDisclosure = (req, res) => {
  try {
    const { 
      title, 
      description, 
      regulationType, 
      regulationName, 
      jurisdiction, 
      applicabilityDate, 
      reportingFrequency, 
      nextDueDate, 
      status, 
      assignedTo, 
      frameworkIds, 
      metricIds, 
      documents 
    } = req.body;
    
    // Create a new disclosure with a unique ID
    const newDisclosure = {
      id: `disc-${uuidv4().substring(0, 8)}`,
      title,
      description: description || '',
      regulationType,
      regulationName,
      jurisdiction,
      applicabilityDate: applicabilityDate || null,
      reportingFrequency,
      nextDueDate: nextDueDate || null,
      status,
      assignedTo: assignedTo || '',
      frameworkIds: frameworkIds || [],
      metricIds: metricIds || [],
      documents: documents ? documents.map(doc => ({
        id: `doc-${uuidv4().substring(0, 8)}`,
        ...doc,
        uploadedBy: req.user?.id || 'system', // Assuming user info is available in req.user
        uploadedAt: new Date().toISOString()
      })) : [],
      submissionHistory: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    // Add the new disclosure to the collection
    models.disclosures.push(newDisclosure);
    
    res.status(201).json({
      data: newDisclosure,
      message: 'Disclosure created successfully'
    });
  } catch (error) {
    console.error('Error in createDisclosure:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Update an existing disclosure
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const updateDisclosure = (req, res) => {
  try {
    const { id } = req.params;
    const { 
      title, 
      description, 
      regulationType, 
      regulationName, 
      jurisdiction, 
      applicabilityDate, 
      reportingFrequency, 
      nextDueDate, 
      status, 
      assignedTo, 
      frameworkIds, 
      metricIds 
    } = req.body;
    
    // Find the disclosure to update
    const disclosureIndex = models.disclosures.findIndex(d => d.id === id);
    
    if (disclosureIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Disclosure with ID ${id} not found`
      });
    }
    
    const currentDisclosure = models.disclosures[disclosureIndex];
    
    // Update the disclosure
    const updatedDisclosure = {
      ...currentDisclosure,
      title: title || currentDisclosure.title,
      description: description !== undefined ? description : currentDisclosure.description,
      regulationType: regulationType || currentDisclosure.regulationType,
      regulationName: regulationName || currentDisclosure.regulationName,
      jurisdiction: jurisdiction || currentDisclosure.jurisdiction,
      applicabilityDate: applicabilityDate !== undefined ? applicabilityDate : currentDisclosure.applicabilityDate,
      reportingFrequency: reportingFrequency || currentDisclosure.reportingFrequency,
      nextDueDate: nextDueDate !== undefined ? nextDueDate : currentDisclosure.nextDueDate,
      status: status || currentDisclosure.status,
      assignedTo: assignedTo !== undefined ? assignedTo : currentDisclosure.assignedTo,
      frameworkIds: frameworkIds || currentDisclosure.frameworkIds,
      metricIds: metricIds || currentDisclosure.metricIds,
      updatedAt: new Date().toISOString()
    };
    
    // Replace the old disclosure with the updated one
    models.disclosures[disclosureIndex] = updatedDisclosure;
    
    res.json({
      data: updatedDisclosure,
      message: 'Disclosure updated successfully'
    });
  } catch (error) {
    console.error('Error in updateDisclosure:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Delete a disclosure
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const deleteDisclosure = (req, res) => {
  try {
    const { id } = req.params;
    
    // Find the disclosure to delete
    const disclosureIndex = models.disclosures.findIndex(d => d.id === id);
    
    if (disclosureIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Disclosure with ID ${id} not found`
      });
    }
    
    // Check if the disclosure has been submitted
    const disclosure = models.disclosures[disclosureIndex];
    if (disclosure.status === 'submitted' || disclosure.status === 'approved') {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Cannot delete a disclosure that has been submitted or approved'
      });
    }
    
    // Remove the disclosure from the collection
    models.disclosures.splice(disclosureIndex, 1);
    
    res.json({
      message: 'Disclosure deleted successfully'
    });
  } catch (error) {
    console.error('Error in deleteDisclosure:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Add a document to a disclosure
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const addDocument = (req, res) => {
  try {
    const { id } = req.params;
    const { name, type, url } = req.body;
    
    // Find the disclosure
    const disclosureIndex = models.disclosures.findIndex(d => d.id === id);
    
    if (disclosureIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Disclosure with ID ${id} not found`
      });
    }
    
    // Create a new document
    const newDocument = {
      id: `doc-${uuidv4().substring(0, 8)}`,
      name,
      type,
      url,
      uploadedBy: req.user?.id || 'system', // Assuming user info is available in req.user
      uploadedAt: new Date().toISOString()
    };
    
    // Add the document to the disclosure
    const updatedDisclosure = {
      ...models.disclosures[disclosureIndex],
      documents: [...models.disclosures[disclosureIndex].documents, newDocument],
      updatedAt: new Date().toISOString()
    };
    
    // Update the disclosure
    models.disclosures[disclosureIndex] = updatedDisclosure;
    
    res.status(201).json({
      data: newDocument,
      message: 'Document added successfully'
    });
  } catch (error) {
    console.error('Error in addDocument:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Remove a document from a disclosure
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const removeDocument = (req, res) => {
  try {
    const { id, documentId } = req.params;
    
    // Find the disclosure
    const disclosureIndex = models.disclosures.findIndex(d => d.id === id);
    
    if (disclosureIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Disclosure with ID ${id} not found`
      });
    }
    
    const disclosure = models.disclosures[disclosureIndex];
    
    // Find the document
    const documentIndex = disclosure.documents.findIndex(d => d.id === documentId);
    
    if (documentIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Document with ID ${documentId} not found in disclosure ${id}`
      });
    }
    
    // Remove the document
    const updatedDocuments = [...disclosure.documents];
    updatedDocuments.splice(documentIndex, 1);
    
    // Update the disclosure
    const updatedDisclosure = {
      ...disclosure,
      documents: updatedDocuments,
      updatedAt: new Date().toISOString()
    };
    
    models.disclosures[disclosureIndex] = updatedDisclosure;
    
    res.json({
      message: 'Document removed successfully'
    });
  } catch (error) {
    console.error('Error in removeDocument:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Add a submission record to a disclosure
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const addSubmission = (req, res) => {
  try {
    const { id } = req.params;
    const { date, status, notes } = req.body;
    
    // Find the disclosure
    const disclosureIndex = models.disclosures.findIndex(d => d.id === id);
    
    if (disclosureIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Disclosure with ID ${id} not found`
      });
    }
    
    // Create a new submission record
    const newSubmission = {
      id: `sub-${uuidv4().substring(0, 8)}`,
      date,
      status,
      submittedBy: req.user?.id || 'system', // Assuming user info is available in req.user
      notes: notes || ''
    };
    
    // Add the submission to the disclosure
    const updatedDisclosure = {
      ...models.disclosures[disclosureIndex],
      submissionHistory: [...models.disclosures[disclosureIndex].submissionHistory, newSubmission],
      status: status, // Update the disclosure status to match the submission status
      updatedAt: new Date().toISOString()
    };
    
    // Update the disclosure
    models.disclosures[disclosureIndex] = updatedDisclosure;
    
    res.status(201).json({
      data: newSubmission,
      message: 'Submission record added successfully'
    });
  } catch (error) {
    console.error('Error in addSubmission:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get a list of regulations
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getRegulations = (req, res) => {
  try {
    const { page = 1, limit = 10, type, category, jurisdiction, reportingFrequency, sortBy = 'name', sortOrder = 'asc' } = req.query;
    
    // Convert page and limit to numbers
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);
    
    // Filter regulations based on query parameters
    let filteredRegulations = [...models.regulations];
    
    if (type) {
      filteredRegulations = filteredRegulations.filter(regulation => regulation.type === type);
    }
    
    if (category) {
      filteredRegulations = filteredRegulations.filter(regulation => regulation.category === category);
    }
    
    if (jurisdiction) {
      filteredRegulations = filteredRegulations.filter(regulation => regulation.jurisdiction === jurisdiction);
    }
    
    if (reportingFrequency) {
      filteredRegulations = filteredRegulations.filter(regulation => regulation.reportingFrequency === reportingFrequency);
    }
    
    // Sort regulations
    filteredRegulations.sort((a, b) => {
      if (sortOrder.toLowerCase() === 'desc') {
        return a[sortBy] > b[sortBy] ? -1 : 1;
      }
      return a[sortBy] > b[sortBy] ? 1 : -1;
    });
    
    // Paginate results
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = pageNum * limitNum;
    const paginatedRegulations = filteredRegulations.slice(startIndex, endIndex);
    
    // Calculate pagination info
    const totalRegulations = filteredRegulations.length;
    const totalPages = Math.ceil(totalRegulations / limitNum);
    
    res.json({
      data: paginatedRegulations,
      pagination: {
        total: totalRegulations,
        page: pageNum,
        limit: limitNum,
        pages: totalPages
      }
    });
  } catch (error) {
    console.error('Error in getRegulations:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get a specific regulation by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getRegulationById = (req, res) => {
  try {
    const { id } = req.params;
    const regulation = models.regulations.find(r => r.id === id);
    
    if (!regulation) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Regulation with ID ${id} not found`
      });
    }
    
    res.json({ data: regulation });
  } catch (error) {
    console.error('Error in getRegulationById:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Create a new regulation
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const createRegulation = (req, res) => {
  try {
    const { 
      name, 
      description, 
      type, 
      category, 
      jurisdiction, 
      issuingAuthority, 
      effectiveDate, 
      reportingFrequency, 
      website, 
      requirements, 
      frameworkIds 
    } = req.body;
    
    // Check if a regulation with the same name already exists
    const existingRegulation = models.regulations.find(r => r.name === name);
    if (existingRegulation) {
      return res.status(400).json({
        error: 'Bad Request',
        message: `A regulation with name '${name}' already exists`
      });
    }
    
    // Create a new regulation with a unique ID
    const newRegulation = {
      id: `reg-${uuidv4().substring(0, 8)}`,
      name,
      description,
      type,
      category,
      jurisdiction,
      issuingAuthority,
      effectiveDate,
      reportingFrequency,
      website: website || '',
      requirements: requirements ? requirements.map(req => ({
        id: `req-${uuidv4().substring(0, 8)}`,
        ...req
      })) : [],
      frameworkIds: frameworkIds || [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    // Add the new regulation to the collection
    models.regulations.push(newRegulation);
    
    res.status(201).json({
      data: newRegulation,
      message: 'Regulation created successfully'
    });
  } catch (error) {
    console.error('Error in createRegulation:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Update an existing regulation
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const updateRegulation = (req, res) => {
  try {
    const { id } = req.params;
    const { 
      name, 
      description, 
      type, 
      category, 
      jurisdiction, 
      issuingAuthority, 
      effectiveDate, 
      reportingFrequency, 
      website, 
      requirements, 
      frameworkIds 
    } = req.body;
    
    // Find the regulation to update
    const regulationIndex = models.regulations.findIndex(r => r.id === id);
    
    if (regulationIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Regulation with ID ${id} not found`
      });
    }
    
    const currentRegulation = models.regulations[regulationIndex];
    
    // If name is being updated, check if it conflicts with an existing regulation
    if (name && name !== currentRegulation.name) {
      const existingRegulation = models.regulations.find(r => r.name === name && r.id !== id);
      if (existingRegulation) {
        return res.status(400).json({
          error: 'Bad Request',
          message: `A regulation with name '${name}' already exists`
        });
      }
    }
    
    // Process requirements if provided
    let updatedRequirements = currentRegulation.requirements;
    if (requirements) {
      updatedRequirements = requirements.map(req => {
        if (req.id) {
          // Update existing requirement
          const existingIndex = currentRegulation.requirements.findIndex(r => r.id === req.id);
          if (existingIndex !== -1) {
            return {
              ...currentRegulation.requirements[existingIndex],
              ...req
            };
          }
        }
        // Add new requirement
        return {
          id: `req-${uuidv4().substring(0, 8)}`,
          ...req
        };
      });
    }
    
    // Update the regulation
    const updatedRegulation = {
      ...currentRegulation,
      name: name || currentRegulation.name,
      description: description || currentRegulation.description,
      type: type || currentRegulation.type,
      category: category || currentRegulation.category,
      jurisdiction: jurisdiction || currentRegulation.jurisdiction,
      issuingAuthority: issuingAuthority || currentRegulation.issuingAuthority,
      effectiveDate: effectiveDate || currentRegulation.effectiveDate,
      reportingFrequency: reportingFrequency || currentRegulation.reportingFrequency,
      website: website !== undefined ? website : currentRegulation.website,
      requirements: updatedRequirements,
      frameworkIds: frameworkIds || currentRegulation.frameworkIds,
      updatedAt: new Date().toISOString()
    };
    
    // Replace the old regulation with the updated one
    models.regulations[regulationIndex] = updatedRegulation;
    
    res.json({
      data: updatedRegulation,
      message: 'Regulation updated successfully'
    });
  } catch (error) {
    console.error('Error in updateRegulation:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Delete a regulation
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const deleteRegulation = (req, res) => {
  try {
    const { id } = req.params;
    
    // Find the regulation to delete
    const regulationIndex = models.regulations.findIndex(r => r.id === id);
    
    if (regulationIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Regulation with ID ${id} not found`
      });
    }
    
    // Check if there are any disclosures associated with this regulation
    const hasDisclosures = models.disclosures.some(d => d.regulationName === models.regulations[regulationIndex].name);
    
    if (hasDisclosures) {
      return res.status(400).json({
        error: 'Bad Request',
        message: `Cannot delete regulation with ID ${id} because it has associated disclosures`
      });
    }
    
    // Remove the regulation from the collection
    models.regulations.splice(regulationIndex, 1);
    
    res.json({
      message: 'Regulation deleted successfully'
    });
  } catch (error) {
    console.error('Error in deleteRegulation:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

module.exports = {
  getDisclosures,
  getDisclosureById,
  createDisclosure,
  updateDisclosure,
  deleteDisclosure,
  addDocument,
  removeDocument,
  addSubmission,
  getRegulations,
  getRegulationById,
  createRegulation,
  updateRegulation,
  deleteRegulation
};
