/**
 * Integration Routes
 * 
 * This file defines the routes for integrations with external systems.
 */

const express = require('express');
const router = express.Router();
const { integrationController } = require('../controllers');

// Get all integrations
router.get('/', integrationController.getAllIntegrations);

// Get a specific integration by ID
router.get('/:id', integrationController.getIntegrationById);

// Create a new integration
router.post('/', integrationController.createIntegration);

// Update an integration
router.put('/:id', integrationController.updateIntegration);

// Delete an integration
router.delete('/:id', integrationController.deleteIntegration);

// Configure an integration
router.post('/:id/configure', integrationController.configureIntegration);

// Execute an integration action
router.post('/:id/:action', integrationController.executeIntegrationAction);

// Check integration health
router.get('/:id/health', integrationController.checkIntegrationHealth);

// Get integration logs
router.get('/:id/logs', integrationController.getIntegrationLogs);

// Get integration metrics
router.get('/:id/metrics', integrationController.getIntegrationMetrics);

module.exports = router;
