#!/usr/bin/env python3
"""
Nova π-Pulse Analyzer
Real-time Q-score + risk score anomaly detection for NovaFuse ecosystem
Implements π-coherence pattern detection and CASTL compliance monitoring
"""

import asyncio
import json
import time
import math
import statistics
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import websockets
import threading
import queue


@dataclass
class PiPulse:
    """π-coherence pulse measurement"""
    timestamp: float
    component: str
    q_score: float
    pi_coherence: float  # π-pattern alignment (31, 42, 53, 64...)
    risk_score: float
    anomaly_detected: bool
    pulse_strength: float


@dataclass
class AnomalyAlert:
    """Anomaly detection alert"""
    timestamp: float
    component: str
    anomaly_type: str  # "q_score_drop", "pi_desync", "risk_spike", "pulse_weak"
    severity: str  # "low", "medium", "high", "critical"
    current_value: float
    expected_value: float
    deviation: float
    recommendation: str


class PiCoherenceCalculator:
    """Calculates π-coherence patterns (31, 42, 53, 64... +11 sequence)"""
    
    def __init__(self):
        self.pi_sequence = [31, 42, 53, 64, 75, 86, 97, 108, 119, 130]  # +11 pattern
        self.golden_ratio = 1.618033988749
    
    def calculate_pi_coherence(self, metrics: Dict[str, float]) -> float:
        """Calculate π-coherence score based on component metrics"""
        
        # Extract key metrics
        code_quality = metrics.get('code_quality', 0.5)
        test_coverage = metrics.get('test_coverage', 0.5)
        documentation = metrics.get('documentation', 0.5)
        security = metrics.get('security', 0.5)
        performance = metrics.get('performance', 0.5)
        
        # Apply π-pattern weighting
        weighted_scores = []
        base_metrics = [code_quality, test_coverage, documentation, security, performance]
        
        for i, metric in enumerate(base_metrics):
            if i < len(self.pi_sequence):
                # Apply π-sequence weighting
                pi_weight = self.pi_sequence[i] / 100.0
                weighted_score = metric * pi_weight
                weighted_scores.append(weighted_score)
        
        # Calculate coherence using golden ratio harmonics
        if weighted_scores:
            coherence = sum(weighted_scores) / len(weighted_scores)
            # Apply golden ratio normalization
            coherence = coherence * (1 / self.golden_ratio)
            return min(1.0, max(0.0, coherence))
        
        return 0.5
    
    def detect_pi_pattern_anomaly(self, historical_coherence: List[float]) -> bool:
        """Detect anomalies in π-coherence patterns"""
        
        if len(historical_coherence) < 5:
            return False
        
        # Calculate moving average and standard deviation
        recent_values = historical_coherence[-5:]
        mean_coherence = statistics.mean(recent_values)
        
        if len(recent_values) > 1:
            std_dev = statistics.stdev(recent_values)
            
            # Check for π-pattern disruption (deviation > 2σ)
            latest_value = recent_values[-1]
            if abs(latest_value - mean_coherence) > (2 * std_dev):
                return True
        
        return False


class NovaPulseAnalyzer:
    """Real-time π-pulse analyzer for Nova ecosystem"""
    
    def __init__(self, workspace_path: str = "."):
        self.workspace_path = Path(workspace_path)
        self.pi_calculator = PiCoherenceCalculator()
        self.pulse_history: Dict[str, List[PiPulse]] = {}
        self.anomaly_queue = queue.Queue()
        self.alert_subscribers = []
        self.running = False
        self.analysis_interval = 30  # seconds
    
    async def start_pulse_monitoring(self):
        """Start continuous π-pulse monitoring"""
        print("🔮 Starting Nova π-Pulse Analyzer...")
        print(f"📡 Monitoring interval: {self.analysis_interval}s")
        print("🧬 π-coherence pattern detection: ACTIVE")
        print("⚡ Q-score anomaly detection: ACTIVE")
        print("🎯 Real-time anomaly alerts: ENABLED")
        print("📊 Pulse strength monitoring: ACTIVE")
        print()

        self.running = True

        # Start background tasks
        asyncio.create_task(self._pulse_collection_loop())
        asyncio.create_task(self._anomaly_detection_loop())
        asyncio.create_task(self._alert_processing_loop())
        asyncio.create_task(self._status_reporting_loop())

        # Keep running
        while self.running:
            await asyncio.sleep(1)
    
    async def _pulse_collection_loop(self):
        """Continuously collect π-pulse data from components"""
        
        while self.running:
            try:
                components = await self._discover_components()
                
                for component in components:
                    pulse = await self._measure_component_pulse(component)
                    
                    # Store pulse data
                    if component['name'] not in self.pulse_history:
                        self.pulse_history[component['name']] = []
                    
                    self.pulse_history[component['name']].append(pulse)
                    
                    # Keep only last 100 pulses per component
                    if len(self.pulse_history[component['name']]) > 100:
                        self.pulse_history[component['name']] = self.pulse_history[component['name']][-100:]
                
                await asyncio.sleep(self.analysis_interval)
                
            except Exception as e:
                print(f"⚠️ Pulse collection error: {e}")
                await asyncio.sleep(5)
    
    async def _measure_component_pulse(self, component: Dict[str, Any]) -> PiPulse:
        """Measure π-pulse for a single component"""
        
        path = Path(component['path'])
        timestamp = time.time()
        
        # Collect component metrics
        metrics = await self._collect_component_metrics(path)
        
        # Calculate Q-score (simplified for demo)
        q_score = sum(metrics.values()) / len(metrics) if metrics else 0.5
        
        # Calculate π-coherence
        pi_coherence = self.pi_calculator.calculate_pi_coherence(metrics)
        
        # Calculate risk score (inverse of health)
        risk_score = 1.0 - ((q_score + pi_coherence) / 2.0)
        
        # Calculate pulse strength (based on recent activity)
        pulse_strength = await self._calculate_pulse_strength(path)
        
        # Check for anomalies
        historical_coherence = [p.pi_coherence for p in self.pulse_history.get(component['name'], [])]
        anomaly_detected = self.pi_calculator.detect_pi_pattern_anomaly(historical_coherence + [pi_coherence])
        
        return PiPulse(
            timestamp=timestamp,
            component=component['name'],
            q_score=q_score,
            pi_coherence=pi_coherence,
            risk_score=risk_score,
            anomaly_detected=anomaly_detected,
            pulse_strength=pulse_strength
        )
    
    async def _collect_component_metrics(self, path: Path) -> Dict[str, float]:
        """Collect metrics for π-coherence calculation"""
        
        metrics = {}
        
        # Code quality metric
        code_files = list(path.rglob("*.py")) + list(path.rglob("*.js")) + list(path.rglob("*.ts"))
        if code_files:
            total_lines = 0
            comment_lines = 0
            
            for file_path in code_files[:10]:  # Sample first 10 files
                try:
                    content = file_path.read_text()
                    lines = content.split('\n')
                    total_lines += len([l for l in lines if l.strip()])
                    comment_lines += len([l for l in lines if l.strip().startswith('#') or l.strip().startswith('//')])
                except:
                    pass
            
            comment_ratio = comment_lines / total_lines if total_lines > 0 else 0
            metrics['code_quality'] = min(1.0, comment_ratio * 5)  # Scale comment ratio
        else:
            metrics['code_quality'] = 0.0
        
        # Test coverage metric
        test_files = list(path.rglob("*test*")) + list(path.rglob("*spec*"))
        test_ratio = len(test_files) / len(code_files) if code_files else 0
        metrics['test_coverage'] = min(1.0, test_ratio * 3)
        
        # Documentation metric
        doc_files = list(path.rglob("*.md"))
        metrics['documentation'] = min(1.0, len(doc_files) * 0.3)
        
        # Security metric (simplified)
        security_patterns = 0
        for file_path in code_files[:5]:
            try:
                content = file_path.read_text().lower()
                if any(pattern in content for pattern in ["jwt", "auth", "security", "encrypt"]):
                    security_patterns += 1
            except:
                pass
        metrics['security'] = min(1.0, security_patterns * 0.2)
        
        # Performance metric (simplified)
        perf_patterns = 0
        for file_path in code_files[:5]:
            try:
                content = file_path.read_text().lower()
                if any(pattern in content for pattern in ["cache", "async", "optimize"]):
                    perf_patterns += 1
            except:
                pass
        metrics['performance'] = min(1.0, perf_patterns * 0.25)
        
        return metrics
    
    async def _calculate_pulse_strength(self, path: Path) -> float:
        """Calculate pulse strength based on recent file activity"""
        
        try:
            # Get file modification times
            recent_activity = 0
            total_files = 0
            current_time = time.time()
            
            for file_path in path.rglob("*"):
                if file_path.is_file():
                    total_files += 1
                    mod_time = file_path.stat().st_mtime
                    
                    # Files modified in last 24 hours contribute to pulse strength
                    if current_time - mod_time < 86400:  # 24 hours
                        recent_activity += 1
            
            if total_files > 0:
                return recent_activity / total_files
            else:
                return 0.0
                
        except:
            return 0.5  # Default pulse strength
    
    async def _anomaly_detection_loop(self):
        """Detect anomalies in π-pulse patterns"""
        
        while self.running:
            try:
                for component_name, pulses in self.pulse_history.items():
                    if len(pulses) >= 5:  # Need minimum history for anomaly detection
                        await self._analyze_component_anomalies(component_name, pulses)
                
                await asyncio.sleep(10)  # Check every 10 seconds
                
            except Exception as e:
                print(f"⚠️ Anomaly detection error: {e}")
                await asyncio.sleep(5)
    
    async def _analyze_component_anomalies(self, component_name: str, pulses: List[PiPulse]):
        """Analyze anomalies for a specific component"""
        
        recent_pulses = pulses[-10:]  # Last 10 pulses
        
        # Q-score anomaly detection
        q_scores = [p.q_score for p in recent_pulses]
        if len(q_scores) > 1:
            mean_q = statistics.mean(q_scores)
            latest_q = q_scores[-1]
            
            if latest_q < mean_q - 0.2:  # Significant Q-score drop
                alert = AnomalyAlert(
                    timestamp=time.time(),
                    component=component_name,
                    anomaly_type="q_score_drop",
                    severity="high" if latest_q < 0.5 else "medium",
                    current_value=latest_q,
                    expected_value=mean_q,
                    deviation=mean_q - latest_q,
                    recommendation=f"Q-score dropped to {latest_q:.2f}. Check recent changes in {component_name}."
                )
                self.anomaly_queue.put(alert)
        
        # π-coherence anomaly detection
        pi_coherences = [p.pi_coherence for p in recent_pulses]
        if self.pi_calculator.detect_pi_pattern_anomaly(pi_coherences):
            alert = AnomalyAlert(
                timestamp=time.time(),
                component=component_name,
                anomaly_type="pi_desync",
                severity="critical",
                current_value=pi_coherences[-1],
                expected_value=statistics.mean(pi_coherences[:-1]),
                deviation=abs(pi_coherences[-1] - statistics.mean(pi_coherences[:-1])),
                recommendation=f"π-coherence pattern disrupted in {component_name}. Investigate architectural changes."
            )
            self.anomaly_queue.put(alert)
        
        # Risk score spike detection
        risk_scores = [p.risk_score for p in recent_pulses]
        if len(risk_scores) > 1:
            mean_risk = statistics.mean(risk_scores)
            latest_risk = risk_scores[-1]
            
            if latest_risk > mean_risk + 0.3:  # Significant risk increase
                alert = AnomalyAlert(
                    timestamp=time.time(),
                    component=component_name,
                    anomaly_type="risk_spike",
                    severity="high",
                    current_value=latest_risk,
                    expected_value=mean_risk,
                    deviation=latest_risk - mean_risk,
                    recommendation=f"Risk score spiked to {latest_risk:.2f} in {component_name}. Review security and stability."
                )
                self.anomaly_queue.put(alert)
    
    async def _alert_processing_loop(self):
        """Process and dispatch anomaly alerts"""

        while self.running:
            try:
                if not self.anomaly_queue.empty():
                    alert = self.anomaly_queue.get()
                    await self._dispatch_alert(alert)

                await asyncio.sleep(1)

            except Exception as e:
                print(f"⚠️ Alert processing error: {e}")
                await asyncio.sleep(5)

    async def _status_reporting_loop(self):
        """Periodic status reporting"""

        while self.running:
            try:
                # Report status every 5 minutes
                await asyncio.sleep(300)

                if self.pulse_history:
                    total_components = len(self.pulse_history)
                    active_anomalies = self.anomaly_queue.qsize()

                    # Calculate average health
                    total_health = 0
                    total_coherence = 0
                    component_count = 0

                    for component_name, pulses in self.pulse_history.items():
                        if pulses:
                            latest_pulse = pulses[-1]
                            total_health += latest_pulse.q_score
                            total_coherence += latest_pulse.pi_coherence
                            component_count += 1

                    if component_count > 0:
                        avg_health = total_health / component_count
                        avg_coherence = total_coherence / component_count

                        print(f"\n📊 π-PULSE STATUS REPORT [{datetime.now().strftime('%H:%M:%S')}]")
                        print(f"   🔮 Components Monitored: {total_components}")
                        print(f"   💚 Average Health: {avg_health:.3f}")
                        print(f"   🧬 Average π-Coherence: {avg_coherence:.3f}")
                        print(f"   ⚠️ Active Anomalies: {active_anomalies}")
                        print(f"   🎯 System Status: {'STABLE' if active_anomalies == 0 else 'MONITORING'}")
                        print()

            except Exception as e:
                print(f"⚠️ Status reporting error: {e}")
                await asyncio.sleep(60)
    
    async def _dispatch_alert(self, alert: AnomalyAlert):
        """Dispatch alert to subscribers"""
        
        # Console output
        severity_emoji = {
            "low": "🟡",
            "medium": "🟠", 
            "high": "🔴",
            "critical": "💀"
        }
        
        emoji = severity_emoji.get(alert.severity, "⚠️")
        timestamp_str = datetime.fromtimestamp(alert.timestamp).strftime("%H:%M:%S")
        
        print(f"\n{emoji} ANOMALY DETECTED [{timestamp_str}]")
        print(f"   Component: {alert.component}")
        print(f"   Type: {alert.anomaly_type}")
        print(f"   Severity: {alert.severity.upper()}")
        print(f"   Current: {alert.current_value:.3f} | Expected: {alert.expected_value:.3f}")
        print(f"   Deviation: {alert.deviation:.3f}")
        print(f"   💡 {alert.recommendation}")
        
        # Save to alert log
        await self._log_alert(alert)
        
        # TODO: Add webhook/Slack/email notifications here
    
    async def _log_alert(self, alert: AnomalyAlert):
        """Log alert to file"""
        
        log_path = self.workspace_path / "nova-pulse-alerts.jsonl"
        
        try:
            with open(log_path, 'a', encoding='utf-8') as f:
                f.write(json.dumps(asdict(alert)) + '\n')
        except Exception as e:
            print(f"⚠️ Failed to log alert: {e}")
    
    async def _discover_components(self) -> List[Dict[str, Any]]:
        """Discover Nova components"""
        
        components = []
        
        # Check src/ directory
        src_path = self.workspace_path / "src"
        if src_path.exists():
            for item in src_path.iterdir():
                if item.is_dir() and item.name.lower().startswith("nova"):
                    components.append({
                        "name": item.name,
                        "path": str(item),
                        "type": "Service"
                    })
        
        return components
    
    def get_pulse_summary(self) -> Dict[str, Any]:
        """Get current pulse summary"""
        
        summary = {
            "timestamp": time.time(),
            "total_components": len(self.pulse_history),
            "active_anomalies": self.anomaly_queue.qsize(),
            "components": {}
        }
        
        for component_name, pulses in self.pulse_history.items():
            if pulses:
                latest_pulse = pulses[-1]
                summary["components"][component_name] = {
                    "q_score": latest_pulse.q_score,
                    "pi_coherence": latest_pulse.pi_coherence,
                    "risk_score": latest_pulse.risk_score,
                    "pulse_strength": latest_pulse.pulse_strength,
                    "anomaly_detected": latest_pulse.anomaly_detected,
                    "last_updated": latest_pulse.timestamp
                }
        
        return summary
    
    def stop(self):
        """Stop pulse monitoring"""
        print("🛑 Stopping Nova π-Pulse Analyzer...")
        self.running = False


async def main():
    import sys
    
    workspace = sys.argv[1] if len(sys.argv) > 1 else "."
    
    analyzer = NovaPulseAnalyzer(workspace)
    
    try:
        await analyzer.start_pulse_monitoring()
    except KeyboardInterrupt:
        analyzer.stop()
        print("\n✅ Nova π-Pulse Analyzer stopped")


if __name__ == "__main__":
    asyncio.run(main())
