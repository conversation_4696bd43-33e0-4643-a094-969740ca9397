/**
 * NovaFuse Universal API Connector - Metrics Routes
 *
 * This module provides routes for performance metrics.
 */

const express = require('express');
const router = express.Router();
const os = require('os');
const { createLogger } = require('../utils/logger');
const performanceMonitor = require('../utils/performance-monitor');
const cache = require('../utils/cache');
const requestQueue = require('../utils/request-queue');
const dbPool = require('../utils/db-pool');

const logger = createLogger('metrics-routes');

// Async handler to catch errors
const asyncHandler = (fn) => (req, res, next) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

/**
 * @route GET /api/metrics
 * @description Get performance metrics
 * @access Public
 */
router.get('/', asyncHandler(async (req, res) => {
  // Get performance metrics
  const metrics = performanceMonitor.getMetrics();

  // Get cache stats
  const cacheStats = cache.getStats();

  // Get request queue stats
  const queueStats = requestQueue.getStats();

  // Get database pool stats
  const dbStats = dbPool.getStats();

  // Calculate request rate (requests per second)
  const uptime = (Date.now() - metrics.uptime) / 1000; // in seconds
  const requestRate = metrics.requests.total / Math.max(uptime, 1);

  // Response
  res.json({
    uptime: metrics.uptime,
    nodeVersion: process.version,
    cpu: {
      current: metrics.cpu.current,
      average: metrics.cpu.average,
      cores: os.cpus().length
    },
    memory: {
      current: metrics.memory.current,
      average: metrics.memory.average,
      total: os.totalmem(),
      free: os.freemem()
    },
    eventLoop: {
      current: metrics.eventLoop.current,
      average: metrics.eventLoop.average
    },
    requests: {
      total: metrics.requests.total,
      success: metrics.requests.success,
      error: metrics.requests.error,
      successRate: metrics.requests.successRate
    },
    requestRate,
    responseTime: {
      average: metrics.requests.latency.average,
      p95: metrics.requests.latency.p95,
      p99: metrics.requests.latency.p99
    },
    connectors: {
      executions: metrics.connectors.executions,
      success: metrics.connectors.success,
      error: metrics.connectors.error,
      successRate: metrics.connectors.successRate,
      latency: {
        average: metrics.connectors.latency.average,
        p95: metrics.connectors.latency.p95,
        p99: metrics.connectors.latency.p99
      }
    },
    cache: {
      size: cacheStats.size,
      maxSize: cacheStats.maxSize,
      hits: cacheStats.hits,
      misses: cacheStats.misses,
      hitRate: cacheStats.hitRate,
      evictions: cacheStats.evictions
    },
    queue: {
      length: queueStats.queueLength,
      processing: queueStats.processing,
      enqueued: queueStats.enqueued,
      processed: queueStats.processed,
      succeeded: queueStats.succeeded,
      failed: queueStats.failed,
      successRate: queueStats.successRate
    },
    database: {
      totalConnections: dbStats.totalConnections,
      activeConnections: dbStats.activeConnections,
      idleConnections: dbStats.idleConnections,
      waitingClients: dbStats.waitingClients
    },
    activeConnections: queueStats.processing + dbStats.activeConnections
  });
}));

/**
 * @route POST /api/metrics/reset
 * @description Reset performance metrics
 * @access Admin
 */
router.post('/reset', asyncHandler(async (req, res) => {
  logger.info('Metrics reset requested');

  // Reset metrics
  const result = performanceMonitor.resetMetrics();

  // Reset cache stats
  cache.resetStats();

  // Reset request queue stats
  requestQueue.resetStats();

  // Reset database pool stats
  dbPool.resetStats();

  res.json({
    success: true,
    timestamp: result.timestamp,
    message: 'All performance metrics have been reset'
  });
}));

module.exports = router;
