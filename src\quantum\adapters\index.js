/**
 * Domain Engine Adapters
 * 
 * This module exports adapters for connecting domain engines (CSDE, CSFE, CSME)
 * with the Comphyological Tensor Core.
 */

const { CSDEAdapter, createCSDEAdapter } = require('./csde_adapter');
const { CSFEAdapter, createCSFEAdapter } = require('./csfe_adapter');
const { CSMEAdapter, createCSMEAdapter } = require('./csme_adapter');

/**
 * Create all domain adapters
 * @param {Object} engines - Domain engines
 * @param {Object} engines.csdeEngine - CSDE engine instance
 * @param {Object} engines.csfeEngine - CSFE engine instance
 * @param {Object} engines.csmeEngine - CSME engine instance
 * @param {Object} options - Configuration options
 * @returns {Object} - Object containing all domain adapters
 */
function createAllDomainAdapters(engines = {}, options = {}) {
  const csdeAdapter = createCSDEAdapter(engines.csdeEngine, {
    ...options,
    ...options.csdeOptions
  });
  
  const csfeAdapter = createCSFEAdapter(engines.csfeEngine, {
    ...options,
    ...options.csfeOptions
  });
  
  const csmeAdapter = createCSMEAdapter(engines.csmeEngine, {
    ...options,
    ...options.csmeOptions
  });
  
  return {
    csdeAdapter,
    csfeAdapter,
    csmeAdapter
  };
}

/**
 * Create a unified adapter that integrates all domain engines
 * @param {Object} engines - Domain engines
 * @param {Object} engines.csdeEngine - CSDE engine instance
 * @param {Object} engines.csfeEngine - CSFE engine instance
 * @param {Object} engines.csmeEngine - CSME engine instance
 * @param {Object} options - Configuration options
 * @returns {Object} - Unified adapter
 */
function createUnifiedAdapter(engines = {}, options = {}) {
  const adapters = createAllDomainAdapters(engines, options);
  
  return {
    /**
     * Process data through all domain engines and the Comphyological Tensor Core
     * @param {Object} data - Data to process
     * @param {Object} data.csdeData - CSDE data
     * @param {Object} data.csfeData - CSFE data
     * @param {Object} data.csmeData - CSME data
     * @returns {Object} - Processing result
     */
    processData: (data = {}) => {
      const { csdeData = {}, csfeData = {}, csmeData = {} } = data;
      
      // Process data through CSDE adapter
      const csdeResult = adapters.csdeAdapter.processData(
        csdeData,
        csfeData,
        csmeData
      );
      
      return {
        result: csdeResult,
        csdeResult,
        csfeResult: adapters.csfeAdapter.getLastResult(),
        csmeResult: adapters.csmeAdapter.getLastResult(),
        timestamp: Date.now()
      };
    },
    
    /**
     * Get metrics from all adapters
     * @returns {Object} - Metrics
     */
    getMetrics: () => {
      return {
        csdeMetrics: adapters.csdeAdapter.getMetrics(),
        csfeMetrics: adapters.csfeAdapter.getMetrics(),
        csmeMetrics: adapters.csmeAdapter.getMetrics(),
        timestamp: Date.now()
      };
    },
    
    /**
     * Reset metrics for all adapters
     */
    resetMetrics: () => {
      adapters.csdeAdapter.resetMetrics();
      adapters.csfeAdapter.resetMetrics();
      adapters.csmeAdapter.resetMetrics();
    },
    
    /**
     * Get all adapters
     * @returns {Object} - All adapters
     */
    getAdapters: () => adapters
  };
}

module.exports = {
  // Adapter classes
  CSDEAdapter,
  CSFEAdapter,
  CSMEAdapter,
  
  // Factory functions
  createCSDEAdapter,
  createCSFEAdapter,
  createCSMEAdapter,
  createAllDomainAdapters,
  createUnifiedAdapter
};
