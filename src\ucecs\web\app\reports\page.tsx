'use client';

import { useState } from 'react';
import MainLayout from '@/components/MainLayout';
import { FiFileText, FiDownload, FiPrinter, FiMail, FiCalendar, FiBarChart2, FiPieChart, FiList } from 'react-icons/fi';

// Mock data for reports
const mockReports = [
  { 
    id: 'rep-001', 
    name: 'Monthly Compliance Summary', 
    type: 'Compliance', 
    format: 'PDF', 
    lastGenerated: '2023-10-15',
    size: '1.2 MB'
  },
  { 
    id: 'rep-002', 
    name: 'ISO 27001 Gap Analysis', 
    type: 'Gap Analysis', 
    format: 'Excel', 
    lastGenerated: '2023-10-10',
    size: '3.5 MB'
  },
  { 
    id: 'rep-003', 
    name: 'Evidence Collection Status', 
    type: 'Status', 
    format: 'PDF', 
    lastGenerated: '2023-10-05',
    size: '0.8 MB'
  },
  { 
    id: 'rep-004', 
    name: 'NIST CSF Compliance Report', 
    type: 'Compliance', 
    format: 'PDF', 
    lastGenerated: '2023-09-30',
    size: '2.1 MB'
  },
  { 
    id: 'rep-005', 
    name: 'Quarterly Audit Report', 
    type: 'Audit', 
    format: 'PDF', 
    lastGenerated: '2023-09-15',
    size: '4.3 MB'
  },
];

// Mock data for report templates
const mockTemplates = [
  { 
    id: 'tmpl-001', 
    name: 'Compliance Summary', 
    description: 'Overview of compliance status across all frameworks',
    type: 'Compliance',
    icon: FiBarChart2
  },
  { 
    id: 'tmpl-002', 
    name: 'Framework Gap Analysis', 
    description: 'Detailed analysis of compliance gaps for a specific framework',
    type: 'Gap Analysis',
    icon: FiPieChart
  },
  { 
    id: 'tmpl-003', 
    name: 'Evidence Collection Status', 
    description: 'Status report of evidence collection progress',
    type: 'Status',
    icon: FiList
  },
  { 
    id: 'tmpl-004', 
    name: 'Audit Report', 
    description: 'Comprehensive audit report for internal or external audits',
    type: 'Audit',
    icon: FiFileText
  },
];

export default function ReportsPage() {
  const [activeTab, setActiveTab] = useState('generated');
  const [selectedReport, setSelectedReport] = useState<string | null>(null);
  const [isGeneratingReport, setIsGeneratingReport] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);
  const [reportConfig, setReportConfig] = useState({
    name: '',
    framework: '',
    dateFrom: '',
    dateTo: '',
    format: 'PDF',
  });

  const handleReportConfigChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setReportConfig({
      ...reportConfig,
      [name]: value,
    });
  };

  const handleGenerateReport = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedTemplate || !reportConfig.name) return;
    
    setIsGeneratingReport(true);
    
    // Simulate API call
    setTimeout(() => {
      setIsGeneratingReport(false);
      setActiveTab('generated');
      // In a real application, we would redirect to the newly generated report
      alert('Report generated successfully!');
    }, 2000);
  };

  return (
    <MainLayout>
      <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Reports</h1>
          <p className="text-gray-500 dark:text-gray-400">Generate and manage compliance reports</p>
        </div>
        <div className="mt-4 sm:mt-0">
          <button 
            className="btn btn-primary flex items-center"
            onClick={() => setActiveTab('generate')}
          >
            <FiFileText className="mr-2" />
            Generate New Report
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex border-b border-gray-200 dark:border-gray-700 mb-6">
        <button
          className={`px-4 py-2 text-sm font-medium ${
            activeTab === 'generated'
              ? 'text-primary border-b-2 border-primary'
              : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
          }`}
          onClick={() => setActiveTab('generated')}
        >
          Generated Reports
        </button>
        <button
          className={`px-4 py-2 text-sm font-medium ${
            activeTab === 'scheduled'
              ? 'text-primary border-b-2 border-primary'
              : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
          }`}
          onClick={() => setActiveTab('scheduled')}
        >
          Scheduled Reports
        </button>
        <button
          className={`px-4 py-2 text-sm font-medium ${
            activeTab === 'generate'
              ? 'text-primary border-b-2 border-primary'
              : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
          }`}
          onClick={() => setActiveTab('generate')}
        >
          Generate Report
        </button>
      </div>

      {/* Generated Reports Tab */}
      {activeTab === 'generated' && (
        <div className="card">
          <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Generated Reports</h2>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-800">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Name
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Type
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Format
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Generated On
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Size
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-secondary-light divide-y divide-gray-200 dark:divide-gray-700">
                {mockReports.map((report) => (
                  <tr 
                    key={report.id}
                    className={`${selectedReport === report.id ? 'bg-blue-50 dark:bg-blue-900/20' : ''}`}
                    onClick={() => setSelectedReport(report.id)}
                  >
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                      {report.name}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                      {report.type}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                      {report.format}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                      {report.lastGenerated}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                      {report.size}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end space-x-2">
                        <button className="text-primary hover:text-primary-dark" title="Download">
                          <FiDownload />
                        </button>
                        <button className="text-primary hover:text-primary-dark" title="Print">
                          <FiPrinter />
                        </button>
                        <button className="text-primary hover:text-primary-dark" title="Email">
                          <FiMail />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Scheduled Reports Tab */}
      {activeTab === 'scheduled' && (
        <div className="card">
          <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Scheduled Reports</h2>
          <div className="bg-yellow-50 dark:bg-yellow-900/20 border-l-4 border-yellow-400 p-4 mb-6">
            <div className="flex">
              <div className="flex-shrink-0">
                <FiCalendar className="h-5 w-5 text-yellow-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm text-yellow-700 dark:text-yellow-200">
                  No scheduled reports found. You can schedule reports to be generated automatically on a recurring basis.
                </p>
              </div>
            </div>
          </div>
          <div className="text-center py-8">
            <button className="btn btn-primary">
              Schedule a Report
            </button>
          </div>
        </div>
      )}

      {/* Generate Report Tab */}
      {activeTab === 'generate' && (
        <div className="space-y-6">
          {/* Report Templates */}
          <div className="card">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Select Report Template</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {mockTemplates.map((template) => (
                <div 
                  key={template.id}
                  className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                    selectedTemplate === template.id 
                      ? 'border-primary bg-primary/5' 
                      : 'border-gray-200 dark:border-gray-700 hover:border-primary hover:bg-primary/5'
                  }`}
                  onClick={() => setSelectedTemplate(template.id)}
                >
                  <div className="flex items-center mb-2">
                    <template.icon className="h-5 w-5 text-primary mr-2" />
                    <h3 className="font-medium text-gray-900 dark:text-white">{template.name}</h3>
                  </div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">{template.description}</p>
                </div>
              ))}
            </div>
          </div>

          {/* Report Configuration */}
          {selectedTemplate && (
            <div className="card">
              <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Configure Report</h2>
              <form onSubmit={handleGenerateReport}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div>
                    <label htmlFor="name" className="label">Report Name</label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      className="input"
                      value={reportConfig.name}
                      onChange={handleReportConfigChange}
                      required
                    />
                  </div>
                  <div>
                    <label htmlFor="framework" className="label">Framework</label>
                    <select
                      id="framework"
                      name="framework"
                      className="input"
                      value={reportConfig.framework}
                      onChange={handleReportConfigChange}
                    >
                      <option value="">All Frameworks</option>
                      <option value="ISO 27001">ISO 27001</option>
                      <option value="NIST CSF">NIST CSF</option>
                      <option value="GDPR">GDPR</option>
                      <option value="HIPAA">HIPAA</option>
                      <option value="PCI DSS">PCI DSS</option>
                    </select>
                  </div>
                  <div>
                    <label htmlFor="dateFrom" className="label">From Date</label>
                    <input
                      type="date"
                      id="dateFrom"
                      name="dateFrom"
                      className="input"
                      value={reportConfig.dateFrom}
                      onChange={handleReportConfigChange}
                    />
                  </div>
                  <div>
                    <label htmlFor="dateTo" className="label">To Date</label>
                    <input
                      type="date"
                      id="dateTo"
                      name="dateTo"
                      className="input"
                      value={reportConfig.dateTo}
                      onChange={handleReportConfigChange}
                    />
                  </div>
                  <div>
                    <label htmlFor="format" className="label">Format</label>
                    <select
                      id="format"
                      name="format"
                      className="input"
                      value={reportConfig.format}
                      onChange={handleReportConfigChange}
                    >
                      <option value="PDF">PDF</option>
                      <option value="Excel">Excel</option>
                      <option value="CSV">CSV</option>
                      <option value="HTML">HTML</option>
                    </select>
                  </div>
                </div>
                <div className="flex justify-end">
                  <button
                    type="submit"
                    className="btn btn-primary"
                    disabled={isGeneratingReport}
                  >
                    {isGeneratingReport ? 'Generating...' : 'Generate Report'}
                  </button>
                </div>
              </form>
            </div>
          )}
        </div>
      )}
    </MainLayout>
  );
}
