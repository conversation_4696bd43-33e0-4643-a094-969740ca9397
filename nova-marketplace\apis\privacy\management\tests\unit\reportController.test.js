/**
 * Report Controller Tests
 *
 * This file contains unit tests for the report controller.
 */

const reportController = require('../../controllers/reportController');

describe('Report Controller', () => {
  // Mock Express request and response
  let req;
  let res;
  let next;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Setup request mock
    req = {
      params: {},
      query: {},
      body: {}
    };

    // Setup response mock
    res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    };

    // Setup next function mock
    next = jest.fn();
  });

  describe('generateReport', () => {
    it('should generate a DSR summary report', async () => {
      // Setup request parameters
      req.params.reportType = 'dsr-summary';
      req.query.period = 'last-30-days';

      // Call the controller
      await reportController.generateReport(req, res, next);

      // Assertions
      expect(res.json).toHaveBeenCalledWith({
        data: expect.objectContaining({
          summary: expect.any(Object),
          byType: expect.any(Object),
          byStatus: expect.any(Object),
          byMonth: expect.any(Array),
          topSystems: expect.any(Array)
        }),
        metadata: expect.objectContaining({
          reportType: 'dsr-summary',
          period: 'last-30-days',
          startDate: expect.any(Date),
          endDate: expect.any(Date),
          generatedAt: expect.any(Date)
        })
      });
    });

    it('should generate a consent management report', async () => {
      // Setup request parameters
      req.params.reportType = 'consent-management';
      req.query.period = 'last-30-days';

      // Call the controller
      await reportController.generateReport(req, res, next);

      // Assertions
      expect(res.json).toHaveBeenCalledWith({
        data: expect.objectContaining({
          summary: expect.any(Object),
          byType: expect.any(Object),
          byStatus: expect.any(Object),
          byCollectionMethod: expect.any(Object),
          byMonth: expect.any(Array)
        }),
        metadata: expect.objectContaining({
          reportType: 'consent-management',
          period: 'last-30-days'
        })
      });
    });

    it('should generate a data breach report', async () => {
      // Setup request parameters
      req.params.reportType = 'data-breach';
      req.query.period = 'last-30-days';

      // Call the controller
      await reportController.generateReport(req, res, next);

      // Assertions
      expect(res.json).toHaveBeenCalledWith({
        data: expect.objectContaining({
          summary: expect.any(Object),
          bySeverity: expect.any(Object),
          byStatus: expect.any(Object),
          byMonth: expect.any(Array)
        }),
        metadata: expect.objectContaining({
          reportType: 'data-breach',
          period: 'last-30-days'
        })
      });
    });

    it('should generate a processing activities report', async () => {
      // Setup request parameters
      req.params.reportType = 'processing-activities';
      req.query.period = 'last-30-days';

      // Call the controller
      await reportController.generateReport(req, res, next);

      // Assertions
      expect(res.json).toHaveBeenCalledWith({
        data: expect.objectContaining({
          summary: expect.any(Object),
          byLegalBasis: expect.any(Object),
          byDataCategory: expect.any(Object),
          byDataSubject: expect.any(Object)
        }),
        metadata: expect.objectContaining({
          reportType: 'processing-activities',
          period: 'last-30-days'
        })
      });
    });

    it('should generate a compliance status report', async () => {
      // Setup request parameters
      req.params.reportType = 'compliance-status';
      req.query.period = 'last-30-days';

      // Call the controller
      await reportController.generateReport(req, res, next);

      // Assertions
      expect(res.json).toHaveBeenCalledWith({
        data: expect.objectContaining({
          summary: expect.any(Object),
          byRegulation: expect.any(Array),
          byDomain: expect.any(Array),
          upcomingDeadlines: expect.any(Array)
        }),
        metadata: expect.objectContaining({
          reportType: 'compliance-status',
          period: 'last-30-days'
        })
      });
    });

    it('should handle invalid report type', async () => {
      // Setup request parameters
      req.params.reportType = 'invalid-report-type';

      // Call the controller
      await reportController.generateReport(req, res, next);

      // Assertions
      expect(next).toHaveBeenCalledWith(expect.objectContaining({
        name: 'ValidationError',
        message: expect.stringContaining('Invalid report type')
      }));
    });

    it('should handle custom period with missing dates', async () => {
      // Setup request parameters
      req.params.reportType = 'dsr-summary';
      req.query.period = 'custom';
      // Missing startDate and endDate

      // Call the controller
      await reportController.generateReport(req, res, next);

      // Assertions
      expect(next).toHaveBeenCalledWith(expect.objectContaining({
        name: 'ValidationError',
        message: expect.stringContaining('Start date and end date are required')
      }));
    });

    it('should handle custom period with valid dates', async () => {
      // Setup request parameters
      req.params.reportType = 'dsr-summary';
      req.query.period = 'custom';
      req.query.startDate = '2023-01-01';
      req.query.endDate = '2023-01-31';

      // Call the controller
      await reportController.generateReport(req, res, next);

      // Assertions
      expect(res.json).toHaveBeenCalledWith({
        data: expect.any(Object),
        metadata: expect.objectContaining({
          reportType: 'dsr-summary',
          period: 'custom',
          startDate: expect.any(Date),
          endDate: expect.any(Date)
        })
      });
    });

    it('should handle different period options', async () => {
      const periods = ['last-7-days', 'last-30-days', 'last-90-days', 'last-12-months', 'year-to-date'];

      for (const period of periods) {
        // Setup request parameters
        req.params.reportType = 'dsr-summary';
        req.query.period = period;

        // Call the controller
        await reportController.generateReport(req, res, next);

        // Assertions
        expect(res.json).toHaveBeenCalledWith({
          data: expect.any(Object),
          metadata: expect.objectContaining({
            reportType: 'dsr-summary',
            period: period
          })
        });

        // Reset mocks for next iteration
        jest.clearAllMocks();
        res.json.mockClear();
      }
    });

    it('should handle errors', async () => {
      // Setup request parameters to trigger an error
      req.params.reportType = 'dsr-summary';
      
      // Mock Date constructor to throw an error
      const originalDate = global.Date;
      global.Date = jest.fn(() => {
        throw new Error('Date error');
      });
      global.Date.now = originalDate.now;

      // Call the controller
      await reportController.generateReport(req, res, next);

      // Assertions
      expect(next).toHaveBeenCalledWith(expect.any(Error));

      // Restore Date constructor
      global.Date = originalDate;
    });
  });

  describe('getDashboardMetrics', () => {
    it('should return dashboard metrics', async () => {
      // Call the controller
      await reportController.getDashboardMetrics(req, res, next);

      // Assertions
      expect(res.json).toHaveBeenCalledWith({
        data: expect.objectContaining({
          dsr: expect.any(Object),
          consent: expect.any(Object),
          dataBreach: expect.any(Object),
          compliance: expect.any(Object),
          processingActivities: expect.any(Object),
          recentActivity: expect.any(Array)
        })
      });
    });

    it('should handle errors', async () => {
      // Mock res.json to throw an error
      res.json.mockImplementationOnce(() => {
        throw new Error('JSON error');
      });

      // Call the controller
      await reportController.getDashboardMetrics(req, res, next);

      // Assertions
      expect(next).toHaveBeenCalledWith(expect.any(Error));
    });
  });
});
