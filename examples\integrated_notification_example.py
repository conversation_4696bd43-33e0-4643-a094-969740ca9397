"""
Example of using the integrated Notification Manager with the Evidence Manager.

This example demonstrates how to use the Notification Manager integrated with
the Evidence Manager to send notifications about evidence events.
"""

import os
import sys
import json
import uuid
import logging
import datetime

# Add the parent directory to the path so we can import the UCECS modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.ucecs.core.evidence_manager import EvidenceManager
from src.ucecs.core.notification_manager import NotificationType

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """Run the integrated Notification Manager example."""
    # Create a temporary directory for the example
    temp_dir = os.path.join(os.getcwd(), 'temp_integrated_notification_example')
    os.makedirs(temp_dir, exist_ok=True)
    
    # Create directories for the Evidence Manager
    evidence_dir = os.path.join(temp_dir, 'evidence_data')
    notifications_dir = os.path.join(temp_dir, 'notifications')
    
    # Create an Evidence Manager
    manager = EvidenceManager(
        evidence_dir=evidence_dir,
        current_user_id="admin"
    )
    
    # Set the notifications directory for the Notification Manager
    manager.notification_manager.notifications_dir = notifications_dir
    
    try:
        # Register a new evidence item
        logger.info("Registering new evidence...")
        evidence_id = str(uuid.uuid4())
        evidence = {
            'id': evidence_id,
            'type': 'document',
            'source': 'user_upload',
            'data': {
                'title': 'Sample Document',
                'content': 'This is a sample document for testing the Notification Manager.',
                'created_at': datetime.datetime.now(datetime.timezone.utc).isoformat()
            },
            'metadata': {
                'tags': ['sample', 'document', 'test']
            }
        }
        
        # Register the evidence
        registered_evidence = manager.register_evidence(evidence)
        logger.info(f"Evidence registered: {registered_evidence.get('id')}")
        
        # Try to register the same evidence again (should generate a warning notification)
        logger.info("Trying to register the same evidence again...")
        duplicate_evidence = manager.register_evidence(evidence)
        logger.info(f"Duplicate evidence registration attempt: {duplicate_evidence.get('id')}")
        
        # Create a mock validation result
        validation_results = {
            'is_valid': True,
            'details': {
                'reason': 'Example validation result'
            }
        }
        
        # Update the evidence metadata with the validation results
        manager.evidence_metadata[evidence_id]['validation_results'] = validation_results
        manager.evidence_metadata[evidence_id]['status'] = 'validated'
        
        # Validate the evidence (should generate an info notification)
        logger.info("Validating evidence...")
        manager.validate_evidence(
            evidence=evidence,
            validator_id="file_exists",
            user_id="admin"
        )
        
        # Create an invalid validation result
        invalid_validation_results = {
            'is_valid': False,
            'errors': ['Invalid document format', 'Missing required fields'],
            'details': {
                'reason': 'Example invalid validation result'
            }
        }
        
        # Update the evidence with invalid validation results
        evidence['validation_results'] = invalid_validation_results
        
        # Validate the evidence again (should generate a warning notification)
        logger.info("Validating evidence with invalid results...")
        manager.validate_evidence(
            evidence=evidence,
            validator_id="file_exists",
            user_id="admin"
        )
        
        # Store the evidence (should generate an info notification)
        logger.info("Storing evidence...")
        manager.store_evidence(
            evidence=evidence,
            storage_id="file_system",
            user_id="admin"
        )
        
        # Delete the evidence (should generate a warning notification)
        logger.info("Deleting evidence...")
        manager.delete_evidence(
            evidence_id=evidence_id,
            storage_id="file_system",
            user_id="admin"
        )
        
        # Get all notifications
        logger.info("Getting all notifications...")
        all_notifications = manager.notification_manager.get_notifications()
        logger.info(f"All notifications: {json.dumps(all_notifications, indent=2)}")
        
        # Get only warning notifications
        logger.info("Getting warning notifications...")
        warning_notifications = manager.notification_manager.get_notifications(
            notification_type=NotificationType.WARNING
        )
        logger.info(f"Warning notifications: {json.dumps(warning_notifications, indent=2)}")
        
        # Check the notification log file
        notification_log_path = os.path.join(notifications_dir, 'notification_log.json')
        logger.info(f"Notification log saved to: {notification_log_path}")
        
    except Exception as e:
        logger.error(f"Error: {e}")
    
    finally:
        # Clean up the temporary directory
        # Uncomment the following line to delete the temporary directory
        # import shutil; shutil.rmtree(temp_dir)
        pass

if __name__ == "__main__":
    main()
