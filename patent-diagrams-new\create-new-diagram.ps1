# Script to create a new patent diagram from the template
# This script creates a new diagram file with the specified title and components

param(
    [Parameter(Mandatory=$true)]
    [string]$DiagramTitle,

    [Parameter(Mandatory=$true)]
    [string]$MainTitle,

    [Parameter(Mandatory=$true)]
    [string]$OutputFileName
)

# Get the template file
$templatePath = Join-Path -Path "D:\novafuse-api-superstore\patent-diagrams-new" -ChildPath "patent-diagram-template.html"
$outputPath = Join-Path -Path "D:\novafuse-api-superstore\patent-diagrams-new" -ChildPath $OutputFileName

# Check if template exists
if (-not (Test-Path $templatePath)) {
    Write-Error "Template file not found at $templatePath"
    exit 1
}

# Read the template content
$templateContent = Get-Content -Path $templatePath -Raw

# Replace the placeholders
$diagramContent = $templateContent -replace '\[DIAGRAM TITLE\]', $DiagramTitle
$diagramContent = $diagramContent -replace '\[MAIN TITLE\]', $MainTitle

# Remove the instructions section
$instructionsPattern = '(?s)<div style="max-width: 800px; margin: 0 auto; padding: 20px; background-color: #f0f0f0; border-radius: 8px;">.*?</div>\s*</body>'
$diagramContent = $diagramContent -replace $instructionsPattern, '</body>'

# Write the new diagram file
Set-Content -Path $outputPath -Value $diagramContent

Write-Host "Created new diagram: $OutputFileName"
Write-Host "Title: $DiagramTitle"
Write-Host "Main Title: $MainTitle"
Write-Host "Now edit the file to add your specific components and relationships."
