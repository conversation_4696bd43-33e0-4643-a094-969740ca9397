# NovaDNA Tests

This directory contains tests for the NovaDNA system.

## Running Tests

To run the tests, use the following commands:

```bash
# Run all tests
node run.js

# Run specific tests
node run.js novavision  # Run NovaVision integration tests
node run.js healthcare  # Run healthcare integration tests
```

## Test Structure

The tests are organized as follows:

- `integration/` - Integration tests for NovaDNA components
  - `NovaVisionIntegrationTest.js` - Tests for NovaVision integration
  - `HealthcareIntegrationTest.js` - Tests for healthcare integration

## Test Results

The tests will output detailed information about each test and a summary at the end. If all tests pass, you'll see:

```
=== Test Summary ===
✅ All tests passed successfully!
```

If any tests fail, you'll see:

```
=== Test Summary ===
❌ Some tests failed. See above for details.
```

## Adding New Tests

To add new tests:

1. Create a new test file in the appropriate directory
2. Export a test function that returns a result object with a `success` property
3. Update `run.js` to include your new test

Example:

```javascript
// myTest.js
function testMyFeature() {
  // Run tests
  // ...
  
  return {
    success: true,
    // Additional result data
  };
}

module.exports = {
  testMyFeature
};
```

Then update `run.js` to include your test:

```javascript
// In run.js
if (testName.toLowerCase() === 'myfeature') {
  testResult = await runMyFeatureTests();
  if (!testResult) {
    process.exit(1);
  }
}
```

## Test Coverage

The current tests cover:

1. **NovaVision Integration**
   - Emergency access UI
   - Emergency override UI
   - Profile view UI with different access levels
   - Security dashboard UI

2. **Healthcare Integration**
   - Emergency session management
   - Data source prioritization
   - Secure temporary caching
   - Integration with NovaConnect
