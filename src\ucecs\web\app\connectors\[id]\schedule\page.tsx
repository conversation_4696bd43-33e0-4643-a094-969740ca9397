'use client';

import { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import Link from 'next/link';
import MainLayout from '@/components/MainLayout';
import { FiArrowLeft, FiSave, FiClock } from 'react-icons/fi';
import { getConnectorById, scheduleCollection } from '@/services/novaCoreApi';

// Mock connector data (same as in the details page)
const mockConnector = {
  id: 'conn-001',
  name: 'AWS Security Hub',
  description: 'Collects security findings from AWS Security Hub',
  type: 'aws',
  status: 'active',
  config: {
    baseUrl: 'https://securityhub.us-east-1.amazonaws.com',
    apiVersion: '2018-10-26',
  },
  authentication: {
    type: 'aws_iam',
    credentials: {
      accessKey: 'AKIAXXXXXXXXXXXXXXXX',
      secretKey: '****************************************',
      region: 'us-east-1',
    },
  },
  schedule: {
    frequency: 'daily',
    time: '02:00',
    timezone: 'UTC',
    enabled: true,
    lastRun: '2023-10-15T02:00:00Z',
    nextRun: '2023-10-16T02:00:00Z',
  },
  lastCollection: '2023-10-15T02:00:00Z',
  evidenceCount: 128,
  createdAt: '2023-09-01T10:15:30Z',
  createdBy: 'admin',
  updatedAt: '2023-10-01T14:22:45Z',
  updatedBy: 'admin',
};

// Frequency options
const frequencyOptions = [
  { value: 'hourly', label: 'Hourly' },
  { value: 'daily', label: 'Daily' },
  { value: 'weekly', label: 'Weekly' },
  { value: 'monthly', label: 'Monthly' },
  { value: 'custom', label: 'Custom' },
];

// Timezone options
const timezoneOptions = [
  { value: 'UTC', label: 'UTC' },
  { value: 'America/New_York', label: 'Eastern Time (ET)' },
  { value: 'America/Chicago', label: 'Central Time (CT)' },
  { value: 'America/Denver', label: 'Mountain Time (MT)' },
  { value: 'America/Los_Angeles', label: 'Pacific Time (PT)' },
  { value: 'Europe/London', label: 'London' },
  { value: 'Europe/Paris', label: 'Paris' },
  { value: 'Asia/Tokyo', label: 'Tokyo' },
];

export default function ScheduleConnectorPage() {
  const params = useParams();
  const router = useRouter();
  const [connector, setConnector] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [formData, setFormData] = useState({
    frequency: 'daily',
    time: '00:00',
    timezone: 'UTC',
    enabled: true,
    interval: 1,
    dayOfWeek: 1, // Monday
    dayOfMonth: 1,
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Load connector data
  useEffect(() => {
    const loadConnector = async () => {
      try {
        // In a real implementation, this would fetch the connector from the API
        // const result = await getConnectorById(params.id as string);
        // setConnector(result);
        
        // For now, use mock data
        setConnector(mockConnector);
        
        // Initialize form data
        setFormData({
          frequency: mockConnector.schedule.frequency,
          time: mockConnector.schedule.time,
          timezone: mockConnector.schedule.timezone,
          enabled: mockConnector.schedule.enabled,
          interval: 1,
          dayOfWeek: 1,
          dayOfMonth: 1,
        });
        
        setLoading(false);
      } catch (error) {
        console.error('Error loading connector:', error);
        setLoading(false);
      }
    };
    
    loadConnector();
  }, [params.id]);

  // Handle form field change
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    if (type === 'checkbox') {
      const checkbox = e.target as HTMLInputElement;
      setFormData({
        ...formData,
        [name]: checkbox.checked,
      });
    } else if (type === 'number') {
      setFormData({
        ...formData,
        [name]: parseInt(value, 10),
      });
    } else {
      setFormData({
        ...formData,
        [name]: value,
      });
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!connector) {
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      // Prepare schedule data
      const schedule = {
        frequency: formData.frequency,
        time: formData.time,
        timezone: formData.timezone,
        enabled: formData.enabled,
      };
      
      // Add frequency-specific fields
      if (formData.frequency === 'hourly') {
        schedule['interval'] = formData.interval;
      } else if (formData.frequency === 'weekly') {
        schedule['dayOfWeek'] = formData.dayOfWeek;
      } else if (formData.frequency === 'monthly') {
        schedule['dayOfMonth'] = formData.dayOfMonth;
      }
      
      // In a real implementation, this would update the schedule via API
      // await scheduleCollection(connector.id, schedule);
      
      // For now, just log the data and redirect
      console.log('Updating schedule:', schedule);
      
      // Redirect to connector details page
      router.push(`/connectors/${connector.id}`);
    } catch (error) {
      console.error('Error updating schedule:', error);
      alert('Error updating schedule. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (loading) {
    return (
      <MainLayout>
        <div className="card p-8 text-center">
          <p className="text-gray-500 dark:text-gray-400">Loading connector...</p>
        </div>
      </MainLayout>
    );
  }

  if (!connector) {
    return (
      <MainLayout>
        <div className="card p-8 text-center">
          <p className="text-gray-500 dark:text-gray-400">Connector not found</p>
          <Link href="/connectors" className="btn btn-primary mt-4">
            Back to Connectors
          </Link>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="mb-6 flex items-center">
        <Link href={`/connectors/${connector.id}`} className="mr-4">
          <FiArrowLeft className="h-5 w-5 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200" />
        </Link>
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Collection Schedule</h1>
          <p className="text-gray-500 dark:text-gray-400">{connector.name}</p>
        </div>
      </div>

      <div className="card">
        <form onSubmit={handleSubmit}>
          <div className="mb-6">
            <div className="flex items-center mb-4">
              <FiClock className="h-5 w-5 text-primary mr-2" />
              <h2 className="text-lg font-medium text-gray-900 dark:text-white">Schedule Settings</h2>
            </div>
            
            <div className="mb-4">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  name="enabled"
                  className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                  checked={formData.enabled}
                  onChange={handleChange}
                />
                <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                  Enable scheduled collection
                </span>
              </label>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="frequency" className="label">Frequency</label>
                <select
                  id="frequency"
                  name="frequency"
                  className="input"
                  value={formData.frequency}
                  onChange={handleChange}
                >
                  {frequencyOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
              
              {formData.frequency === 'hourly' && (
                <div>
                  <label htmlFor="interval" className="label">Every X Hours</label>
                  <input
                    type="number"
                    id="interval"
                    name="interval"
                    className="input"
                    min="1"
                    max="24"
                    value={formData.interval}
                    onChange={handleChange}
                  />
                </div>
              )}
              
              {formData.frequency === 'weekly' && (
                <div>
                  <label htmlFor="dayOfWeek" className="label">Day of Week</label>
                  <select
                    id="dayOfWeek"
                    name="dayOfWeek"
                    className="input"
                    value={formData.dayOfWeek}
                    onChange={handleChange}
                  >
                    <option value="1">Monday</option>
                    <option value="2">Tuesday</option>
                    <option value="3">Wednesday</option>
                    <option value="4">Thursday</option>
                    <option value="5">Friday</option>
                    <option value="6">Saturday</option>
                    <option value="0">Sunday</option>
                  </select>
                </div>
              )}
              
              {formData.frequency === 'monthly' && (
                <div>
                  <label htmlFor="dayOfMonth" className="label">Day of Month</label>
                  <input
                    type="number"
                    id="dayOfMonth"
                    name="dayOfMonth"
                    className="input"
                    min="1"
                    max="31"
                    value={formData.dayOfMonth}
                    onChange={handleChange}
                  />
                </div>
              )}
              
              {formData.frequency !== 'hourly' && (
                <div>
                  <label htmlFor="time" className="label">Time</label>
                  <input
                    type="time"
                    id="time"
                    name="time"
                    className="input"
                    value={formData.time}
                    onChange={handleChange}
                  />
                </div>
              )}
              
              <div>
                <label htmlFor="timezone" className="label">Timezone</label>
                <select
                  id="timezone"
                  name="timezone"
                  className="input"
                  value={formData.timezone}
                  onChange={handleChange}
                >
                  {timezoneOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>
          
          <div className="flex justify-between">
            <Link href={`/connectors/${connector.id}`} className="btn btn-outline">
              Cancel
            </Link>
            <button
              type="submit"
              className="btn btn-primary flex items-center"
              disabled={isSubmitting}
            >
              <FiSave className="mr-2" />
              {isSubmitting ? 'Saving...' : 'Save Schedule'}
            </button>
          </div>
        </form>
      </div>
    </MainLayout>
  );
}
