/**
 * API Service
 *
 * This service handles API requests to the backend.
 */

import axios from 'axios';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3001/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json'
  }
});

// Add request interceptor for authentication
api.interceptors.request.use(
  (config) => {
    // Try to get token from localStorage
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }

    // Try to get API key from localStorage
    const apiKey = localStorage.getItem('api_key');
    if (apiKey) {
      config.headers['X-API-Key'] = apiKey;
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    // Store rate limit information if available
    if (response.headers['x-ratelimit-limit']) {
      localStorage.setItem('rate_limit', JSON.stringify({
        limit: response.headers['x-ratelimit-limit'],
        remaining: response.headers['x-ratelimit-remaining'],
        reset: response.headers['x-ratelimit-reset']
      }));
    }

    return response;
  },
  (error) => {
    // Handle authentication errors
    if (error.response && error.response.status === 401) {
      // Clear authentication data
      localStorage.removeItem('auth_token');
      localStorage.removeItem('user');

      // Redirect to login page
      if (typeof window !== 'undefined') {
        window.location.href = '/login';
      }
    }

    // Handle rate limit errors
    if (error.response && error.response.status === 429) {
      console.error('Rate limit exceeded:', error.response.data);
    }

    return Promise.reject(error);
  }
);

// Connector API
export const connectorApi = {
  // Get all connectors
  getAllConnectors: () => {
    return api.get('/connectors');
  },

  // Get connector by ID
  getConnectorById: (id) => {
    return api.get(`/connectors/${id}`);
  },

  // Create a new connector
  createConnector: (data) => {
    return api.post('/connectors', data);
  },

  // Update a connector
  updateConnector: (id, data) => {
    return api.put(`/connectors/${id}`, data);
  },

  // Delete a connector
  deleteConnector: (id) => {
    return api.delete(`/connectors/${id}`);
  },

  // Duplicate a connector
  duplicateConnector: (id) => {
    return api.post(`/connectors/${id}/duplicate`);
  },

  // Get connector versions
  getConnectorVersions: (id) => {
    return api.get(`/connectors/${id}/versions`);
  }
};

// Testing API
export const testingApi = {
  // Execute a connector endpoint
  executeEndpoint: (connectorId, endpointId, credentialId, parameters, headers) => {
    return api.post('/testing/execute', {
      connectorId,
      endpointId,
      credentialId,
      parameters,
      headers
    });
  },

  // Get request history
  getRequestHistory: (connectorId, endpointId) => {
    return api.get(`/testing/history/${connectorId}/${endpointId}`);
  },

  // Validate response against rules
  validateResponse: (response, rules) => {
    return api.post('/testing/validate', {
      response,
      rules
    });
  },

  // Simulate error scenarios
  simulateErrorScenario: (type, options) => {
    return api.post('/testing/simulate-error', {
      type,
      options
    });
  }
};

// Monitoring API
export const monitoringApi = {
  // Get health status of all connectors
  getHealthStatus: () => {
    return api.get('/monitoring/health');
  },

  // Get health status of a specific connector
  getConnectorHealth: (id) => {
    return api.get(`/monitoring/health/${id}`);
  },

  // Run health check for a connector
  runHealthCheck: (id, credentialId) => {
    return api.post(`/monitoring/health/${id}/check`, {
      credentialId
    });
  },

  // Get all alerts
  getAllAlerts: () => {
    return api.get('/monitoring/alerts');
  },

  // Get alert by ID
  getAlertById: (id) => {
    return api.get(`/monitoring/alerts/${id}`);
  },

  // Acknowledge alert
  acknowledgeAlert: (id, user, comment) => {
    return api.put(`/monitoring/alerts/${id}/acknowledge`, {
      user,
      comment
    });
  },

  // Resolve alert
  resolveAlert: (id, user, comment) => {
    return api.put(`/monitoring/alerts/${id}/resolve`, {
      user,
      comment
    });
  },

  // Add comment to alert
  addAlertComment: (id, user, comment) => {
    return api.post(`/monitoring/alerts/${id}/comments`, {
      user,
      comment
    });
  },

  // Get alert configuration
  getAlertConfig: () => {
    return api.get('/monitoring/config/alerts');
  },

  // Update alert configuration
  updateAlertConfig: (config) => {
    return api.put('/monitoring/config/alerts', config);
  },

  // Get all anomalies
  getAllAnomalies: () => {
    return api.get('/monitoring/anomalies');
  },

  // Get anomalies for a connector
  getConnectorAnomalies: (id) => {
    return api.get(`/monitoring/anomalies/${id}`);
  },

  // Detect anomalies for a connector
  detectAnomalies: (id) => {
    return api.post(`/monitoring/anomalies/${id}/detect`);
  }
};

// Credential API
export const credentialApi = {
  // Get all credentials
  getAllCredentials: () => {
    return api.get('/credentials');
  },

  // Get credential by ID
  getCredentialById: (id) => {
    return api.get(`/credentials/${id}`);
  },

  // Create a new credential
  createCredential: (data) => {
    return api.post('/credentials', data);
  },

  // Update a credential
  updateCredential: (id, data) => {
    return api.put(`/credentials/${id}`, data);
  },

  // Delete a credential
  deleteCredential: (id) => {
    return api.delete(`/credentials/${id}`);
  }
};

// GraphQL API
export const graphqlApi = {
  // Execute a GraphQL query
  executeQuery: (endpoint, query, variables, headers, auth) => {
    return api.post('/graphql/execute', {
      endpoint,
      query,
      variables,
      headers,
      auth
    });
  },

  // Fetch GraphQL schema
  fetchSchema: (endpoint, headers, auth) => {
    return api.post('/graphql/schema', {
      endpoint,
      headers,
      auth
    });
  },

  // Validate a GraphQL query
  validateQuery: (query, schema) => {
    return api.post('/graphql/validate', {
      query,
      schema
    });
  },

  // Generate a sample GraphQL query
  generateSampleQuery: (schema, operationType) => {
    return api.post('/graphql/sample', {
      schema,
      operationType
    });
  },

  // Create a GraphQL subscription
  createSubscription: (endpoint, query, variables, headers, auth) => {
    return api.post('/graphql/subscriptions', {
      endpoint,
      query,
      variables,
      headers,
      auth
    });
  },

  // Get all active subscriptions
  getActiveSubscriptions: () => {
    return api.get('/graphql/subscriptions');
  },

  // Get subscription by ID
  getSubscription: (id) => {
    return api.get(`/graphql/subscriptions/${id}`);
  },

  // Cancel a subscription
  cancelSubscription: (id) => {
    return api.delete(`/graphql/subscriptions/${id}`);
  },

  // Get subscription messages
  getSubscriptionMessages: (id, clientId) => {
    return api.get(`/graphql/subscriptions/${id}/messages`, {
      params: { clientId }
    });
  },

  // Get subscription errors
  getSubscriptionErrors: (id, clientId) => {
    return api.get(`/graphql/subscriptions/${id}/errors`, {
      params: { clientId }
    });
  }
};

// Authentication API
export const authApi = {
  // Register a new user
  register: (userData) => {
    return api.post('/auth/register', userData);
  },

  // Login a user
  login: (username, password) => {
    return api.post('/auth/login', { username, password });
  },

  // Logout a user
  logout: () => {
    return api.post('/auth/logout');
  },

  // Get current user
  getCurrentUser: () => {
    return api.get('/auth/me');
  },

  // Get all users (admin only)
  getAllUsers: () => {
    return api.get('/auth/users');
  },

  // Get user by ID
  getUserById: (id) => {
    return api.get(`/auth/users/${id}`);
  },

  // Update user
  updateUser: (id, userData) => {
    return api.put(`/auth/users/${id}`, userData);
  },

  // Delete user
  deleteUser: (id) => {
    return api.delete(`/auth/users/${id}`);
  },

  // Create API key
  createApiKey: (name, permissions, expiresIn) => {
    return api.post('/auth/api-keys', { name, permissions, expiresIn });
  },

  // Get API keys
  getApiKeys: () => {
    return api.get('/auth/api-keys');
  },

  // Delete API key
  deleteApiKey: (id) => {
    return api.delete(`/auth/api-keys/${id}`);
  }
};

// API Key API
export const apiKeyApi = {
  // Get all API keys (admin only)
  getAllApiKeys: () => {
    return api.get('/api-keys');
  },

  // Get API key by ID
  getApiKeyById: (id) => {
    return api.get(`/api-keys/${id}`);
  },

  // Update API key
  updateApiKey: (id, data) => {
    return api.put(`/api-keys/${id}`, data);
  },

  // Revoke all API keys for a user (admin only)
  revokeUserApiKeys: (userId) => {
    return api.delete(`/api-keys/user/${userId}`);
  }
};

// Analytics API
export const analyticsApi = {
  // Get dashboard analytics
  getDashboardAnalytics: () => {
    return api.get('/analytics/dashboard');
  },

  // Get usage analytics
  getUsageAnalytics: (filters = {}) => {
    return api.get('/analytics/usage', { params: filters });
  },

  // Get performance analytics
  getPerformanceAnalytics: (filters = {}) => {
    return api.get('/analytics/performance', { params: filters });
  },

  // Get error analytics
  getErrorAnalytics: (filters = {}) => {
    return api.get('/analytics/errors', { params: filters });
  },

  // Report Templates
  getReportTemplates: () => {
    return api.get('/analytics/report-templates');
  },

  getReportTemplateById: (id) => {
    return api.get(`/analytics/report-templates/${id}`);
  },

  createReportTemplate: (data) => {
    return api.post('/analytics/report-templates', data);
  },

  updateReportTemplate: (id, data) => {
    return api.put(`/analytics/report-templates/${id}`, data);
  },

  deleteReportTemplate: (id) => {
    return api.delete(`/analytics/report-templates/${id}`);
  },

  // Reports
  getReports: (filters = {}) => {
    return api.get('/analytics/reports', { params: filters });
  },

  getReportById: (id) => {
    return api.get(`/analytics/reports/${id}`);
  },

  generateReport: (templateId, customFilters = {}) => {
    return api.post(`/analytics/reports/generate/${templateId}`, customFilters);
  },

  deleteReport: (id) => {
    return api.delete(`/analytics/reports/${id}`);
  },

  scheduleReport: (templateId, schedule) => {
    return api.post(`/analytics/report-templates/${templateId}/schedule`, schedule);
  }
};

// Environment API
export const environmentApi = {
  // Get all environments
  getAllEnvironments: () => {
    return api.get('/environments');
  },

  // Get default environment
  getDefaultEnvironment: () => {
    return api.get('/environments/default');
  },

  // Get environment by ID
  getEnvironmentById: (id) => {
    return api.get(`/environments/${id}`);
  },

  // Create a new environment
  createEnvironment: (data) => {
    return api.post('/environments', data);
  },

  // Update an environment
  updateEnvironment: (id, data) => {
    return api.put(`/environments/${id}`, data);
  },

  // Delete an environment
  deleteEnvironment: (id) => {
    return api.delete(`/environments/${id}`);
  },

  // Set default environment
  setDefaultEnvironment: (id) => {
    return api.post(`/environments/${id}/default`);
  },

  // Promote environment
  promoteEnvironment: (sourceId, targetId) => {
    return api.post('/environments/promote', { sourceId, targetId });
  },

  // Get environment configuration
  getEnvironmentConfig: (id) => {
    return api.get(`/environments/${id}/config`);
  },

  // Compare environment configurations
  compareEnvironmentConfig: (sourceId, targetId) => {
    return api.post('/environments/compare', { sourceId, targetId });
  },

  // Get all connectors for an environment
  getEnvironmentConnectors: (environmentId) => {
    return api.get(`/environments/${environmentId}/connectors`);
  },

  // Get connector by ID for an environment
  getEnvironmentConnector: (environmentId, connectorId) => {
    return api.get(`/environments/${environmentId}/connectors/${connectorId}`);
  },

  // Create a new connector for an environment
  createEnvironmentConnector: (environmentId, data) => {
    return api.post(`/environments/${environmentId}/connectors`, data);
  },

  // Update a connector for an environment
  updateEnvironmentConnector: (environmentId, connectorId, data) => {
    return api.put(`/environments/${environmentId}/connectors/${connectorId}`, data);
  },

  // Delete a connector for an environment
  deleteEnvironmentConnector: (environmentId, connectorId) => {
    return api.delete(`/environments/${environmentId}/connectors/${connectorId}`);
  },

  // Clone a connector between environments
  cloneConnector: (sourceEnvironmentId, targetEnvironmentId, connectorId) => {
    return api.post('/environments/connectors/clone', {
      sourceEnvironmentId,
      targetEnvironmentId,
      connectorId
    });
  },

  // Promote all connectors from one environment to another
  promoteConnectors: (sourceEnvironmentId, targetEnvironmentId) => {
    return api.post('/environments/connectors/promote', {
      sourceEnvironmentId,
      targetEnvironmentId
    });
  }
};

// Team API
export const teamApi = {
  // Get all teams
  getAllTeams: () => {
    return api.get('/teams');
  },

  // Get my teams
  getMyTeams: () => {
    return api.get('/teams/my');
  },

  // Get team by ID
  getTeamById: (id) => {
    return api.get(`/teams/${id}`);
  },

  // Create a new team
  createTeam: (data) => {
    return api.post('/teams', data);
  },

  // Update a team
  updateTeam: (id, data) => {
    return api.put(`/teams/${id}`, data);
  },

  // Delete a team
  deleteTeam: (id) => {
    return api.delete(`/teams/${id}`);
  },

  // Get team members
  getTeamMembers: (id) => {
    return api.get(`/teams/${id}/members`);
  },

  // Add team member
  addTeamMember: (id, userId, role) => {
    return api.post(`/teams/${id}/members`, { userId, role });
  },

  // Update team member
  updateTeamMember: (id, memberId, data) => {
    return api.put(`/teams/${id}/members/${memberId}`, data);
  },

  // Remove team member
  removeTeamMember: (id, memberId) => {
    return api.delete(`/teams/${id}/members/${memberId}`);
  },

  // Get team invitations
  getTeamInvitations: (id) => {
    return api.get(`/teams/${id}/invitations`);
  },

  // Create team invitation
  createTeamInvitation: (id, email, role) => {
    return api.post(`/teams/${id}/invitations`, { email, role });
  },

  // Cancel team invitation
  cancelTeamInvitation: (id, invitationId) => {
    return api.delete(`/teams/${id}/invitations/${invitationId}`);
  },

  // Get my invitations
  getMyInvitations: () => {
    return api.get('/teams/invitations/my');
  },

  // Accept team invitation
  acceptTeamInvitation: (id) => {
    return api.post(`/teams/invitations/${id}/accept`);
  },

  // Decline team invitation
  declineTeamInvitation: (id) => {
    return api.post(`/teams/invitations/${id}/decline`);
  }
};

// Role Permission API
export const rolePermissionApi = {
  // Get all roles
  getAllRoles: () => {
    return api.get('/roles/roles');
  },

  // Get role by ID
  getRoleById: (id) => {
    return api.get(`/roles/roles/${id}`);
  },

  // Create a new role
  createRole: (data) => {
    return api.post('/roles/roles', data);
  },

  // Update a role
  updateRole: (id, data) => {
    return api.put(`/roles/roles/${id}`, data);
  },

  // Delete a role
  deleteRole: (id) => {
    return api.delete(`/roles/roles/${id}`);
  },

  // Get all permissions
  getAllPermissions: () => {
    return api.get('/roles/permissions');
  },

  // Get permissions for a role
  getPermissionsForRole: (id) => {
    return api.get(`/roles/roles/${id}/permissions`);
  },

  // Add permission to role
  addPermissionToRole: (id, permissionId) => {
    return api.post(`/roles/roles/${id}/permissions`, { permissionId });
  },

  // Remove permission from role
  removePermissionFromRole: (id, permissionId) => {
    return api.delete(`/roles/roles/${id}/permissions/${permissionId}`);
  },

  // Get roles for a user
  getRolesForUser: (id) => {
    return api.get(`/roles/users/${id}/roles`);
  },

  // Get my roles
  getMyRoles: () => {
    return api.get('/roles/users/me/roles');
  },

  // Assign role to user
  assignRoleToUser: (id, roleId) => {
    return api.post(`/roles/users/${id}/roles`, { roleId });
  },

  // Remove role from user
  removeRoleFromUser: (id, roleId) => {
    return api.delete(`/roles/users/${id}/roles/${roleId}`);
  },

  // Get permissions for a user
  getPermissionsForUser: (id) => {
    return api.get(`/roles/users/${id}/permissions`);
  },

  // Get my permissions
  getMyPermissions: () => {
    return api.get('/roles/users/me/permissions');
  }
};

// Audit API
export const auditApi = {
  // Get audit logs
  getAuditLogs: (filters = {}) => {
    return api.get('/audit', { params: filters });
  },

  // Get audit log by ID
  getAuditLogById: (id) => {
    return api.get(`/audit/${id}`);
  },

  // Get audit logs for a resource
  getAuditLogsForResource: (resourceType, resourceId) => {
    return api.get(`/audit/resource/${resourceType}/${resourceId}`);
  },

  // Get audit logs for a user
  getAuditLogsForUser: (id) => {
    return api.get(`/audit/user/${id}`);
  },

  // Get my audit logs
  getMyAuditLogs: () => {
    return api.get('/audit/user/me');
  },

  // Get audit logs for a team
  getAuditLogsForTeam: (id) => {
    return api.get(`/audit/team/${id}`);
  }
};

// Change Request API
export const changeRequestApi = {
  // Get all change requests
  getAllChangeRequests: (filters = {}) => {
    return api.get('/change-requests', { params: filters });
  },

  // Get change requests for current user
  getMyChangeRequests: (filters = {}) => {
    return api.get('/change-requests/my', { params: filters });
  },

  // Get change requests for approval
  getChangeRequestsForApproval: (filters = {}) => {
    return api.get('/change-requests/approval', { params: filters });
  },

  // Get change request by ID
  getChangeRequestById: (id) => {
    return api.get(`/change-requests/${id}`);
  },

  // Create a new change request
  createChangeRequest: (data) => {
    return api.post('/change-requests', data);
  },

  // Update a change request
  updateChangeRequest: (id, data) => {
    return api.put(`/change-requests/${id}`, data);
  },

  // Delete a change request
  deleteChangeRequest: (id) => {
    return api.delete(`/change-requests/${id}`);
  },

  // Get change request comments
  getChangeRequestComments: (id) => {
    return api.get(`/change-requests/${id}/comments`);
  },

  // Add comment to change request
  addChangeRequestComment: (id, data) => {
    return api.post(`/change-requests/${id}/comments`, data);
  },

  // Update comment
  updateComment: (id, commentId, data) => {
    return api.put(`/change-requests/${id}/comments/${commentId}`, data);
  },

  // Delete comment
  deleteComment: (id, commentId) => {
    return api.delete(`/change-requests/${id}/comments/${commentId}`);
  },

  // Get change request approvals
  getChangeRequestApprovals: (id) => {
    return api.get(`/change-requests/${id}/approvals`);
  },

  // Approve change request
  approveChangeRequest: (id, data = {}) => {
    return api.post(`/change-requests/${id}/approve`, data);
  },

  // Reject change request
  rejectChangeRequest: (id, data = {}) => {
    return api.post(`/change-requests/${id}/reject`, data);
  },

  // Implement change request
  implementChangeRequest: (id, data = {}) => {
    return api.post(`/change-requests/${id}/implement`, data);
  }
};

// Policy API
export const policyApi = {
  // Get all policies
  getAllPolicies: (filters = {}) => {
    return api.get('/policies', { params: filters });
  },

  // Get policies for a team
  getPoliciesForTeam: (id, filters = {}) => {
    return api.get(`/policies/team/${id}`, { params: filters });
  },

  // Get policy by ID
  getPolicyById: (id) => {
    return api.get(`/policies/${id}`);
  },

  // Create a new policy
  createPolicy: (data) => {
    return api.post('/policies', data);
  },

  // Update a policy
  updatePolicy: (id, data) => {
    return api.put(`/policies/${id}`, data);
  },

  // Delete a policy
  deletePolicy: (id) => {
    return api.delete(`/policies/${id}`);
  },

  // Evaluate a resource against policies
  evaluateResource: (resourceType, resource, environmentId = null, teamId = null) => {
    return api.post(`/policies/evaluate/${resourceType}`, { resource, environmentId, teamId });
  },

  // Get policy violations
  getPolicyViolations: (filters = {}) => {
    return api.get('/policies/violations', { params: filters });
  },

  // Get policy violation by ID
  getPolicyViolationById: (id) => {
    return api.get(`/policies/violations/${id}`);
  },

  // Update policy violation
  updatePolicyViolation: (id, data) => {
    return api.put(`/policies/violations/${id}`, data);
  },

  // Record a policy violation
  recordPolicyViolation: (policyId, resourceType, resourceId, violations) => {
    return api.post(`/policies/${policyId}/violations`, { resourceType, resourceId, violations });
  }
};

// Resource Lock API
export const resourceLockApi = {
  // Get all resource locks
  getAllResourceLocks: (filters = {}) => {
    return api.get('/resource-locks', { params: filters });
  },

  // Get resource locks for a team
  getResourceLocksForTeam: (id, filters = {}) => {
    return api.get(`/resource-locks/team/${id}`, { params: filters });
  },

  // Check if a resource is locked
  isResourceLocked: (resourceType, resourceId, environmentId = null) => {
    return api.get(`/resource-locks/check/${resourceType}/${resourceId}`, {
      params: { environmentId }
    });
  },

  // Check if a user can modify a resource
  canModifyResource: (resourceType, resourceId, environmentId = null) => {
    return api.get(`/resource-locks/can-modify/${resourceType}/${resourceId}`, {
      params: { environmentId }
    });
  },

  // Clean up expired locks
  cleanupExpiredLocks: () => {
    return api.post('/resource-locks/cleanup');
  },

  // Get resource lock by ID
  getResourceLockById: (id) => {
    return api.get(`/resource-locks/${id}`);
  },

  // Create a new resource lock
  createResourceLock: (data) => {
    return api.post('/resource-locks', data);
  },

  // Update a resource lock
  updateResourceLock: (id, data) => {
    return api.put(`/resource-locks/${id}`, data);
  },

  // Delete a resource lock (unlock)
  deleteResourceLock: (id, reason = '') => {
    return api.delete(`/resource-locks/${id}`, { data: { reason } });
  },

  // Get lock overrides for a lock
  getLockOverrides: (id) => {
    return api.get(`/resource-locks/${id}/overrides`);
  },

  // Create a lock override
  createLockOverride: (id, userId, reason) => {
    return api.post(`/resource-locks/${id}/overrides`, { userId, reason });
  },

  // Delete a lock override
  deleteLockOverride: (id, overrideId) => {
    return api.delete(`/resource-locks/${id}/overrides/${overrideId}`);
  }
};

// Identity Provider API
export const identityProviderApi = {
  // Get all identity providers
  getAllProviders: () => {
    return api.get('/identity-providers');
  },

  // Get identity provider by ID
  getProviderById: (id) => {
    return api.get(`/identity-providers/${id}`);
  },

  // Create a new identity provider
  createProvider: (data) => {
    return api.post('/identity-providers', data);
  },

  // Update an identity provider
  updateProvider: (id, data) => {
    return api.put(`/identity-providers/${id}`, data);
  },

  // Delete an identity provider
  deleteProvider: (id) => {
    return api.delete(`/identity-providers/${id}`);
  },

  // Test an identity provider connection
  testProviderConnection: (id) => {
    return api.post(`/identity-providers/${id}/test`);
  },

  // Generate SAML metadata
  generateSamlMetadata: (id) => {
    return api.get(`/identity-providers/${id}/saml/metadata`);
  },

  // Get OIDC configuration
  getOidcConfiguration: (id) => {
    return api.get(`/identity-providers/${id}/oidc/configuration`);
  }
};

// SSO Authentication API
export const ssoAuthApi = {
  // Get provider by domain
  getProviderByDomain: (domain) => {
    return api.get(`/sso/provider/domain/${domain}`);
  },

  // Initiate SSO authentication
  initiateAuth: (providerId, redirectUri, relayState, nonce) => {
    return api.get(`/sso/initiate/${providerId}`, {
      params: { redirectUri, relayState, nonce }
    });
  },

  // Initiate SSO logout
  initiateLogout: (providerId, idToken, postLogoutRedirectUri) => {
    return api.post(`/sso/logout/${providerId}`, {
      idToken,
      postLogoutRedirectUri
    });
  },

  // Refresh OIDC tokens
  refreshTokens: (refreshToken, providerId) => {
    return api.post('/sso/refresh', {
      refreshToken,
      providerId
    });
  },

  // Clean up expired data (admin only)
  cleanup: () => {
    return api.post('/sso/cleanup');
  }
};

// Theme API
export const themeApi = {
  // Get all themes
  getAllThemes: () => {
    return api.get('/themes');
  },

  // Get theme by ID
  getThemeById: (id) => {
    return api.get(`/themes/${id}`);
  },

  // Create a new theme
  createTheme: (data) => {
    return api.post('/themes', data);
  },

  // Update a theme
  updateTheme: (id, data) => {
    return api.put(`/themes/${id}`, data);
  },

  // Delete a theme
  deleteTheme: (id) => {
    return api.delete(`/themes/${id}`);
  },

  // Clone a theme
  cloneTheme: (id, data = {}) => {
    return api.post(`/themes/${id}/clone`, data);
  },

  // Get organization theme
  getOrganizationTheme: (organizationId) => {
    return api.get(`/themes/organization/${organizationId}`);
  },

  // Set organization theme
  setOrganizationTheme: (organizationId, themeId) => {
    return api.post(`/themes/organization/${organizationId}`, { themeId });
  },

  // Reset organization theme to default
  resetOrganizationTheme: (organizationId) => {
    return api.delete(`/themes/organization/${organizationId}`);
  },

  // Get theme CSS
  getThemeCssUrl: (id) => {
    return `${api.defaults.baseURL}/themes/${id}/css`;
  },

  // Get organization theme CSS
  getOrganizationThemeCssUrl: (organizationId) => {
    return `${api.defaults.baseURL}/themes/organization/${organizationId}/css`;
  }
};

// Branding API
export const brandingApi = {
  // Get organization branding
  getOrganizationBranding: (organizationId) => {
    return api.get(`/branding/organization/${organizationId}`);
  },

  // Update organization branding
  updateOrganizationBranding: (organizationId, data) => {
    return api.put(`/branding/organization/${organizationId}`, data);
  },

  // Reset organization branding to default
  resetOrganizationBranding: (organizationId) => {
    return api.delete(`/branding/organization/${organizationId}`);
  },

  // Upload branding asset
  uploadBrandingAsset: (organizationId, assetType, file) => {
    const formData = new FormData();
    formData.append('file', file);

    return api.post(`/branding/organization/${organizationId}/assets?assetType=${assetType}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  },

  // Delete branding asset
  deleteBrandingAsset: (organizationId, fileName) => {
    return api.delete(`/branding/organization/${organizationId}/assets/${fileName}`);
  },

  // Get branding asset URL
  getBrandingAssetUrl: (fileName) => {
    return `${api.defaults.baseURL}/branding/assets/${fileName}`;
  },

  // Get complete branding package
  getBrandingPackage: (organizationId) => {
    return api.get(`/branding/package/${organizationId}`);
  }
};

// White Label API
export const whiteLabelApi = {
  // Get white label settings for an organization
  getWhiteLabelSettings: (organizationId) => {
    return api.get(`/white-label/organization/${organizationId}`);
  },

  // Update white label settings for an organization
  updateWhiteLabelSettings: (organizationId, data) => {
    return api.put(`/white-label/organization/${organizationId}`, data);
  },

  // Get custom domains for an organization
  getCustomDomains: (organizationId) => {
    return api.get(`/white-label/organization/${organizationId}/domains`);
  },

  // Add custom domain for an organization
  addCustomDomain: (organizationId, domain) => {
    return api.post(`/white-label/organization/${organizationId}/domains`, { domain });
  },

  // Verify custom domain for an organization
  verifyCustomDomain: (organizationId, domain) => {
    return api.post(`/white-label/organization/${organizationId}/domains/${domain}/verify`);
  },

  // Delete custom domain for an organization
  deleteCustomDomain: (organizationId, domain) => {
    return api.delete(`/white-label/organization/${organizationId}/domains/${domain}`);
  },

  // Get domain verification instructions
  getDomainVerificationInstructions: (organizationId, domain) => {
    return api.get(`/white-label/organization/${organizationId}/domains/${domain}/instructions`);
  },

  // Get white label package by domain
  getWhiteLabelPackage: (domain) => {
    return api.get(`/white-label/package/${domain}`);
  }
};

// Report API
export const reportApi = {
  // Report Templates
  getAllReportTemplates: () => {
    return api.get('/reports/templates');
  },

  getReportTemplatesByType: (type) => {
    return api.get(`/reports/templates/type/${type}`);
  },

  getReportTemplateById: (id) => {
    return api.get(`/reports/templates/${id}`);
  },

  createReportTemplate: (data) => {
    return api.post('/reports/templates', data);
  },

  updateReportTemplate: (id, data) => {
    return api.put(`/reports/templates/${id}`, data);
  },

  deleteReportTemplate: (id) => {
    return api.delete(`/reports/templates/${id}`);
  },

  cloneReportTemplate: (id, data = {}) => {
    return api.post(`/reports/templates/${id}/clone`, data);
  },

  // Reports
  generateReport: (templateId, parameters = {}) => {
    return api.post(`/reports/generate/${templateId}`, parameters);
  },

  getAllReports: (filters = {}) => {
    return api.get('/reports', { params: filters });
  },

  getMyReports: (filters = {}) => {
    return api.get('/reports/my', { params: filters });
  },

  getReportById: (id) => {
    return api.get(`/reports/${id}`);
  },

  deleteReport: (id) => {
    return api.delete(`/reports/${id}`);
  },

  getReportExportUrl: (id, format) => {
    return `${api.defaults.baseURL}/reports/${id}/export/${format}`;
  },

  // Scheduled Reports
  getAllScheduledReports: (filters = {}) => {
    return api.get('/reports/scheduled', { params: filters });
  },

  getMyScheduledReports: (filters = {}) => {
    return api.get('/reports/scheduled/my', { params: filters });
  },

  getScheduledReportById: (id) => {
    return api.get(`/reports/scheduled/${id}`);
  },

  createScheduledReport: (data) => {
    return api.post('/reports/scheduled', data);
  },

  updateScheduledReport: (id, data) => {
    return api.put(`/reports/scheduled/${id}`, data);
  },

  deleteScheduledReport: (id) => {
    return api.delete(`/reports/scheduled/${id}`);
  },

  runScheduledReports: () => {
    return api.post('/reports/scheduled/run');
  }
};

// Export/Import API
export const exportImportApi = {
  // Exports
  getAllExports: (filters = {}) => {
    return api.get('/export-import/exports', { params: filters });
  },

  getMyExports: (filters = {}) => {
    return api.get('/export-import/exports/my', { params: filters });
  },

  getExportById: (id) => {
    return api.get(`/export-import/exports/${id}`);
  },

  createExport: (data) => {
    return api.post('/export-import/exports', data);
  },

  deleteExport: (id) => {
    return api.delete(`/export-import/exports/${id}`);
  },

  getExportDownloadUrl: (id) => {
    return `${api.defaults.baseURL}/export-import/exports/${id}/download`;
  },

  // Imports
  getAllImports: (filters = {}) => {
    return api.get('/export-import/imports', { params: filters });
  },

  getMyImports: (filters = {}) => {
    return api.get('/export-import/imports/my', { params: filters });
  },

  getImportById: (id) => {
    return api.get(`/export-import/imports/${id}`);
  },

  createImport: (name, description, file, options = {}) => {
    const formData = new FormData();
    formData.append('name', name);

    if (description) {
      formData.append('description', description);
    }

    if (options) {
      formData.append('options', JSON.stringify(options));
    }

    formData.append('file', file);

    return api.post('/export-import/imports', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  },

  processImport: (id, options = {}) => {
    return api.post(`/export-import/imports/${id}/process`, options);
  },

  deleteImport: (id) => {
    return api.delete(`/export-import/imports/${id}`);
  }
};

// Workflow API
export const workflowApi = {
  // Workflows
  getAllWorkflows: (filters = {}) => {
    return api.get('/workflows', { params: filters });
  },

  getMyWorkflows: (filters = {}) => {
    return api.get('/workflows/my', { params: filters });
  },

  getWorkflowById: (id) => {
    return api.get(`/workflows/${id}`);
  },

  createWorkflow: (data) => {
    return api.post('/workflows', data);
  },

  updateWorkflow: (id, data) => {
    return api.put(`/workflows/${id}`, data);
  },

  deleteWorkflow: (id) => {
    return api.delete(`/workflows/${id}`);
  },

  enableWorkflow: (id) => {
    return api.post(`/workflows/${id}/enable`);
  },

  disableWorkflow: (id) => {
    return api.post(`/workflows/${id}/disable`);
  },

  executeWorkflow: (id, inputs = {}) => {
    return api.post(`/workflows/${id}/execute`, inputs);
  },

  // Workflow Runs
  getAllWorkflowRuns: (filters = {}) => {
    return api.get('/workflows/runs', { params: filters });
  },

  getWorkflowRunsForWorkflow: (id, filters = {}) => {
    return api.get(`/workflows/${id}/runs`, { params: filters });
  },

  getWorkflowRunById: (id) => {
    return api.get(`/workflows/runs/${id}`);
  },

  // Admin
  processScheduledWorkflows: () => {
    return api.post('/workflows/scheduled/process');
  },

  triggerEventWorkflows: (eventType, eventData = {}) => {
    return api.post(`/workflows/events/${eventType}`, eventData);
  }
};

// Subscription API
export const subscriptionApi = {
  // Feature Flags
  getAllFeatureFlags: () => {
    return api.get('/subscription/flags');
  },

  getFeatureFlagById: (id) => {
    return api.get(`/subscription/flags/${id}`);
  },

  updateFeatureFlag: (id, data) => {
    return api.put(`/subscription/flags/${id}`, data);
  },

  // Subscription Tiers
  getAllSubscriptionTiers: () => {
    return api.get('/subscription/tiers');
  },

  getSubscriptionTierById: (id) => {
    return api.get(`/subscription/tiers/${id}`);
  },

  // User Entitlements
  getUserEntitlement: (userId) => {
    return api.get(`/subscription/users/${userId}/entitlement`);
  },

  updateUserEntitlement: (userId, data) => {
    return api.put(`/subscription/users/${userId}/entitlement`, data);
  },

  // Feature Access
  hasFeatureAccess: (userId, featureId) => {
    return api.get(`/subscription/users/${userId}/features/${featureId}/access`);
  },

  getFeatureLimit: (userId, featureId, limitKey) => {
    return api.get(`/subscription/users/${userId}/features/${featureId}/limits/${limitKey}`);
  },

  trackFeatureUsage: (userId, featureId, quantity = 1) => {
    return api.post(`/subscription/users/${userId}/features/${featureId}/usage`, { quantity });
  },

  getFeatureUsageForUser: (userId, params = {}) => {
    return api.get(`/subscription/users/${userId}/usage`, { params });
  },

  hasReachedFeatureLimit: (userId, featureId, limitKey) => {
    return api.get(`/subscription/users/${userId}/features/${featureId}/limits/${limitKey}/check`);
  },

  // User Subscription
  getUserAvailableFeatures: (userId) => {
    return api.get(`/subscription/users/${userId}/features`);
  },

  getUserSubscriptionDetails: (userId) => {
    return api.get(`/subscription/users/${userId}/subscription`);
  },

  // Current User Subscription
  getCurrentUserSubscriptionDetails: () => {
    return api.get('/subscription/my/subscription');
  }
};

// AI Assistant API
export const aiAssistApi = {
  // Connector Generation
  generateConnectorConfig: (data) => {
    return api.post('/ai-assist/connectors/generate', data);
  },

  generateFromDescription: (description) => {
    return api.post('/ai-assist/connectors/generate-from-description', { description });
  },

  // Error Resolution
  suggestErrorFixes: (error, context, connector = null) => {
    return api.post('/ai-assist/errors/suggest-fixes', { error, context, connector });
  },

  // Workflow Optimization
  optimizeWorkflow: (workflow, executionHistory = []) => {
    return api.post('/ai-assist/workflows/optimize', { workflow, executionHistory });
  }
};

// Governance API
export const governanceApi = {
  // Approval Workflows
  getAllApprovalWorkflows: (filters = {}) => {
    return api.get('/governance/approvals', { params: filters });
  },

  getMyApprovalWorkflows: (role) => {
    return api.get('/governance/approvals/my', { params: { role } });
  },

  getApprovalWorkflowById: (id) => {
    return api.get(`/governance/approvals/${id}`);
  },

  createApprovalWorkflow: (data) => {
    return api.post('/governance/approvals', data);
  },

  updateApprovalStatus: (id, status, comment) => {
    return api.post(`/governance/approvals/${id}/status`, { status, comment });
  },

  cancelApprovalWorkflow: (id, reason) => {
    return api.post(`/governance/approvals/${id}/cancel`, { reason });
  },

  getApprovalWorkflowsForResource: (resourceType, resourceId) => {
    return api.get(`/governance/resources/${resourceType}/${resourceId}/approvals`);
  },

  // Compliance Templates
  getAllComplianceTemplates: (filters = {}) => {
    return api.get('/governance/compliance-templates', { params: filters });
  },

  getComplianceTemplateById: (id) => {
    return api.get(`/governance/compliance-templates/${id}`);
  },

  createComplianceTemplate: (data) => {
    return api.post('/governance/compliance-templates', data);
  },

  deleteComplianceTemplate: (id) => {
    return api.delete(`/governance/compliance-templates/${id}`);
  },

  // Data Lineage
  trackDataLineage: (data) => {
    return api.post('/governance/data-lineage', data);
  },

  getDataLineageForResource: (resourceType, resourceId, direction) => {
    return api.get(`/governance/resources/${resourceType}/${resourceId}/lineage`, {
      params: { direction }
    });
  },

  buildDataLineageGraph: (resourceType, resourceId, depth) => {
    return api.get(`/governance/resources/${resourceType}/${resourceId}/lineage-graph`, {
      params: { depth }
    });
  }
};

// Security API
export const securityApi = {
  // Security Audit
  getSecurityAuditLogs: (filters = {}) => {
    return api.get('/security/audit', { params: filters });
  },

  // IP Restrictions
  getAllIpRestrictions: (filters = {}) => {
    return api.get('/security/ip-restrictions', { params: filters });
  },

  getIpRestrictionById: (id) => {
    return api.get(`/security/ip-restrictions/${id}`);
  },

  createIpRestriction: (data) => {
    return api.post('/security/ip-restrictions', data);
  },

  updateIpRestriction: (id, data) => {
    return api.put(`/security/ip-restrictions/${id}`, data);
  },

  deleteIpRestriction: (id) => {
    return api.delete(`/security/ip-restrictions/${id}`);
  },

  checkIpAccess: (resourceType, resourceId, ip) => {
    return api.get(`/security/resources/${resourceType}/${resourceId}/ip-access`, {
      params: { ip }
    });
  },

  // Encryption
  getAllEncryptionKeys: (filters = {}) => {
    return api.get('/security/encryption-keys', { params: filters });
  },

  getEncryptionKeyById: (id) => {
    return api.get(`/security/encryption-keys/${id}`);
  },

  createEncryptionKey: (data) => {
    return api.post('/security/encryption-keys', data);
  },

  updateEncryptionKey: (id, data) => {
    return api.put(`/security/encryption-keys/${id}`, data);
  },

  rotateEncryptionKey: (id) => {
    return api.post(`/security/encryption-keys/${id}/rotate`);
  },

  deleteEncryptionKey: (id) => {
    return api.delete(`/security/encryption-keys/${id}`);
  },

  encryptData: (keyId, data) => {
    return api.post('/security/encrypt', { keyId, data });
  },

  decryptData: (encryptedPackage) => {
    return api.post('/security/decrypt', encryptedPackage);
  },

  // Security Policies
  getAllSecurityPolicies: (filters = {}) => {
    return api.get('/security/policies', { params: filters });
  },

  getSecurityPolicyById: (id) => {
    return api.get(`/security/policies/${id}`);
  },

  createSecurityPolicy: (data) => {
    return api.post('/security/policies', data);
  },

  updateSecurityPolicy: (id, data) => {
    return api.put(`/security/policies/${id}`, data);
  },

  deleteSecurityPolicy: (id) => {
    return api.delete(`/security/policies/${id}`);
  },

  evaluateSecurityPolicy: (id, context) => {
    return api.post(`/security/policies/${id}/evaluate`, context);
  }
};

export default {
  connectorApi,
  testingApi,
  monitoringApi,
  credentialApi,
  graphqlApi,
  authApi,
  apiKeyApi,
  analyticsApi,
  environmentApi,
  teamApi,
  rolePermissionApi,
  auditApi,
  changeRequestApi,
  policyApi,
  resourceLockApi,
  identityProviderApi,
  ssoAuthApi,
  themeApi,
  brandingApi,
  whiteLabelApi,
  reportApi,
  exportImportApi,
  workflowApi,
  subscriptionApi,
  aiAssistApi,
  governanceApi,
  securityApi
};
