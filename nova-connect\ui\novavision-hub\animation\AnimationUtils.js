/**
 * Animation Utilities
 * 
 * This module provides utilities for creating and managing animations.
 */

/**
 * Easing functions
 */
export const easings = {
  // Linear
  linear: t => t,
  
  // Sine
  easeInSine: t => 1 - Math.cos((t * Math.PI) / 2),
  easeOutSine: t => Math.sin((t * Math.PI) / 2),
  easeInOutSine: t => -(Math.cos(Math.PI * t) - 1) / 2,
  
  // Quad
  easeInQuad: t => t * t,
  easeOutQuad: t => 1 - (1 - t) * (1 - t),
  easeInOutQuad: t => t < 0.5 ? 2 * t * t : 1 - Math.pow(-2 * t + 2, 2) / 2,
  
  // Cubic
  easeInCubic: t => t * t * t,
  easeOutCubic: t => 1 - Math.pow(1 - t, 3),
  easeInOutCubic: t => t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2,
  
  // Quart
  easeInQuart: t => t * t * t * t,
  easeOutQuart: t => 1 - Math.pow(1 - t, 4),
  easeInOutQuart: t => t < 0.5 ? 8 * t * t * t * t : 1 - Math.pow(-2 * t + 2, 4) / 2,
  
  // Quint
  easeInQuint: t => t * t * t * t * t,
  easeOutQuint: t => 1 - Math.pow(1 - t, 5),
  easeInOutQuint: t => t < 0.5 ? 16 * t * t * t * t * t : 1 - Math.pow(-2 * t + 2, 5) / 2,
  
  // Expo
  easeInExpo: t => t === 0 ? 0 : Math.pow(2, 10 * t - 10),
  easeOutExpo: t => t === 1 ? 1 : 1 - Math.pow(2, -10 * t),
  easeInOutExpo: t => t === 0 ? 0 : t === 1 ? 1 : t < 0.5 ? Math.pow(2, 20 * t - 10) / 2 : (2 - Math.pow(2, -20 * t + 10)) / 2,
  
  // Circ
  easeInCirc: t => 1 - Math.sqrt(1 - Math.pow(t, 2)),
  easeOutCirc: t => Math.sqrt(1 - Math.pow(t - 1, 2)),
  easeInOutCirc: t => t < 0.5 ? (1 - Math.sqrt(1 - Math.pow(2 * t, 2))) / 2 : (Math.sqrt(1 - Math.pow(-2 * t + 2, 2)) + 1) / 2,
  
  // Back
  easeInBack: t => {
    const c1 = 1.70158;
    const c3 = c1 + 1;
    return c3 * t * t * t - c1 * t * t;
  },
  easeOutBack: t => {
    const c1 = 1.70158;
    const c3 = c1 + 1;
    return 1 + c3 * Math.pow(t - 1, 3) + c1 * Math.pow(t - 1, 2);
  },
  easeInOutBack: t => {
    const c1 = 1.70158;
    const c2 = c1 * 1.525;
    return t < 0.5
      ? (Math.pow(2 * t, 2) * ((c2 + 1) * 2 * t - c2)) / 2
      : (Math.pow(2 * t - 2, 2) * ((c2 + 1) * (t * 2 - 2) + c2) + 2) / 2;
  },
  
  // Elastic
  easeInElastic: t => {
    const c4 = (2 * Math.PI) / 3;
    return t === 0 ? 0 : t === 1 ? 1 : -Math.pow(2, 10 * t - 10) * Math.sin((t * 10 - 10.75) * c4);
  },
  easeOutElastic: t => {
    const c4 = (2 * Math.PI) / 3;
    return t === 0 ? 0 : t === 1 ? 1 : Math.pow(2, -10 * t) * Math.sin((t * 10 - 0.75) * c4) + 1;
  },
  easeInOutElastic: t => {
    const c5 = (2 * Math.PI) / 4.5;
    return t === 0 ? 0 : t === 1 ? 1 : t < 0.5
      ? -(Math.pow(2, 20 * t - 10) * Math.sin((20 * t - 11.125) * c5)) / 2
      : (Math.pow(2, -20 * t + 10) * Math.sin((20 * t - 11.125) * c5)) / 2 + 1;
  },
  
  // Bounce
  easeInBounce: t => 1 - easings.easeOutBounce(1 - t),
  easeOutBounce: t => {
    const n1 = 7.5625;
    const d1 = 2.75;
    
    if (t < 1 / d1) {
      return n1 * t * t;
    } else if (t < 2 / d1) {
      return n1 * (t -= 1.5 / d1) * t + 0.75;
    } else if (t < 2.5 / d1) {
      return n1 * (t -= 2.25 / d1) * t + 0.9375;
    } else {
      return n1 * (t -= 2.625 / d1) * t + 0.984375;
    }
  },
  easeInOutBounce: t => t < 0.5
    ? (1 - easings.easeOutBounce(1 - 2 * t)) / 2
    : (1 + easings.easeOutBounce(2 * t - 1)) / 2
};

/**
 * Animation presets
 */
export const presets = {
  // Fade
  fadeIn: {
    from: { opacity: 0 },
    to: { opacity: 1 },
    duration: 300,
    easing: 'easeOutCubic'
  },
  fadeOut: {
    from: { opacity: 1 },
    to: { opacity: 0 },
    duration: 300,
    easing: 'easeOutCubic'
  },
  
  // Slide
  slideInRight: {
    from: { transform: 'translateX(100%)' },
    to: { transform: 'translateX(0)' },
    duration: 300,
    easing: 'easeOutCubic'
  },
  slideOutRight: {
    from: { transform: 'translateX(0)' },
    to: { transform: 'translateX(100%)' },
    duration: 300,
    easing: 'easeOutCubic'
  },
  slideInLeft: {
    from: { transform: 'translateX(-100%)' },
    to: { transform: 'translateX(0)' },
    duration: 300,
    easing: 'easeOutCubic'
  },
  slideOutLeft: {
    from: { transform: 'translateX(0)' },
    to: { transform: 'translateX(-100%)' },
    duration: 300,
    easing: 'easeOutCubic'
  },
  slideInUp: {
    from: { transform: 'translateY(100%)' },
    to: { transform: 'translateY(0)' },
    duration: 300,
    easing: 'easeOutCubic'
  },
  slideOutUp: {
    from: { transform: 'translateY(0)' },
    to: { transform: 'translateY(-100%)' },
    duration: 300,
    easing: 'easeOutCubic'
  },
  slideInDown: {
    from: { transform: 'translateY(-100%)' },
    to: { transform: 'translateY(0)' },
    duration: 300,
    easing: 'easeOutCubic'
  },
  slideOutDown: {
    from: { transform: 'translateY(0)' },
    to: { transform: 'translateY(100%)' },
    duration: 300,
    easing: 'easeOutCubic'
  },
  
  // Scale
  zoomIn: {
    from: { transform: 'scale(0.5)', opacity: 0 },
    to: { transform: 'scale(1)', opacity: 1 },
    duration: 300,
    easing: 'easeOutCubic'
  },
  zoomOut: {
    from: { transform: 'scale(1)', opacity: 1 },
    to: { transform: 'scale(0.5)', opacity: 0 },
    duration: 300,
    easing: 'easeOutCubic'
  },
  
  // Flip
  flipInX: {
    from: { transform: 'perspective(400px) rotateX(90deg)', opacity: 0 },
    to: { transform: 'perspective(400px) rotateX(0)', opacity: 1 },
    duration: 500,
    easing: 'easeOutCubic'
  },
  flipOutX: {
    from: { transform: 'perspective(400px) rotateX(0)', opacity: 1 },
    to: { transform: 'perspective(400px) rotateX(90deg)', opacity: 0 },
    duration: 500,
    easing: 'easeInCubic'
  },
  flipInY: {
    from: { transform: 'perspective(400px) rotateY(90deg)', opacity: 0 },
    to: { transform: 'perspective(400px) rotateY(0)', opacity: 1 },
    duration: 500,
    easing: 'easeOutCubic'
  },
  flipOutY: {
    from: { transform: 'perspective(400px) rotateY(0)', opacity: 1 },
    to: { transform: 'perspective(400px) rotateY(90deg)', opacity: 0 },
    duration: 500,
    easing: 'easeInCubic'
  },
  
  // Attention seekers
  pulse: {
    keyframes: [
      { transform: 'scale(1)' },
      { transform: 'scale(1.05)' },
      { transform: 'scale(1)' }
    ],
    duration: 500,
    easing: 'easeInOutCubic'
  },
  shake: {
    keyframes: [
      { transform: 'translateX(0)' },
      { transform: 'translateX(-10px)' },
      { transform: 'translateX(10px)' },
      { transform: 'translateX(-10px)' },
      { transform: 'translateX(10px)' },
      { transform: 'translateX(-5px)' },
      { transform: 'translateX(5px)' },
      { transform: 'translateX(0)' }
    ],
    duration: 500,
    easing: 'easeInOutCubic'
  },
  bounce: {
    keyframes: [
      { transform: 'translateY(0)' },
      { transform: 'translateY(-30px)' },
      { transform: 'translateY(0)' },
      { transform: 'translateY(-15px)' },
      { transform: 'translateY(0)' }
    ],
    duration: 800,
    easing: 'easeInOutCubic'
  }
};

/**
 * Create animation
 * 
 * @param {Object} options - Animation options
 * @param {Object} [options.from] - Starting styles
 * @param {Object} [options.to] - Ending styles
 * @param {Array} [options.keyframes] - Animation keyframes
 * @param {number} [options.duration=300] - Animation duration in milliseconds
 * @param {string|Function} [options.easing='easeOutCubic'] - Easing function or name
 * @param {number} [options.delay=0] - Animation delay in milliseconds
 * @param {boolean} [options.fillMode='forwards'] - Animation fill mode
 * @param {number} [options.iterations=1] - Number of iterations
 * @param {string} [options.direction='normal'] - Animation direction
 * @returns {Object} Animation object
 */
export const createAnimation = ({
  from,
  to,
  keyframes,
  duration = 300,
  easing = 'easeOutCubic',
  delay = 0,
  fillMode = 'forwards',
  iterations = 1,
  direction = 'normal'
}) => {
  // Get easing function
  const easingFn = typeof easing === 'function' ? easing : easings[easing] || easings.easeOutCubic;
  
  // Create keyframes if from/to are provided
  const animationKeyframes = keyframes || [from, to];
  
  // Create animation options
  const animationOptions = {
    duration,
    easing: easingFn.toString().includes('cubic-bezier') ? easing : easingFn,
    delay,
    fill: fillMode,
    iterations,
    direction
  };
  
  return {
    keyframes: animationKeyframes,
    options: animationOptions
  };
};

/**
 * Create animation from preset
 * 
 * @param {string} presetName - Preset name
 * @param {Object} [options] - Override options
 * @returns {Object} Animation object
 */
export const createAnimationFromPreset = (presetName, options = {}) => {
  const preset = presets[presetName];
  
  if (!preset) {
    console.warn(`Animation preset "${presetName}" not found`);
    return createAnimation(options);
  }
  
  return createAnimation({
    ...preset,
    ...options
  });
};

/**
 * Create CSS keyframes
 * 
 * @param {string} name - Animation name
 * @param {Array} keyframes - Keyframes
 * @returns {string} CSS keyframes
 */
export const createCSSKeyframes = (name, keyframes) => {
  const keyframeRules = keyframes.map((frame, index) => {
    const percentage = Math.round((index / (keyframes.length - 1)) * 100);
    const styles = Object.entries(frame)
      .map(([prop, value]) => `${prop}: ${value};`)
      .join(' ');
    
    return `${percentage}% { ${styles} }`;
  }).join('\n  ');
  
  return `@keyframes ${name} {
  ${keyframeRules}
}`;
};

/**
 * Create CSS animation
 * 
 * @param {string} name - Animation name
 * @param {Object} options - Animation options
 * @returns {string} CSS animation
 */
export const createCSSAnimation = (name, options) => {
  const {
    duration = 300,
    easing = 'easeOutCubic',
    delay = 0,
    fillMode = 'forwards',
    iterations = 1,
    direction = 'normal'
  } = options;
  
  // Get easing function
  const easingValue = typeof easing === 'string' && !easing.includes('cubic-bezier')
    ? `cubic-bezier(${easings[easing] || easings.easeOutCubic})`
    : easing;
  
  return `animation: ${name} ${duration}ms ${easingValue} ${delay}ms ${fillMode} ${iterations} ${direction};`;
};
