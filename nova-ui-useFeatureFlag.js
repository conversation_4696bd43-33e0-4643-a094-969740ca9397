/**
 * useFeatureFlag Hook
 * 
 * A custom React hook for checking if a feature is enabled based on the current product.
 */

import { useContext } from 'react';
import { ProductContext } from '../contexts/ProductContext';
import { isFeatureEnabled } from '../config/feature-flags';

/**
 * Hook to check if a feature is enabled for the current product
 * @param {string} category - The feature category (dashboard, grc, etc.)
 * @param {string} feature - The feature name (overview, analytics, etc.)
 * @returns {boolean} - Whether the feature is enabled
 */
export function useFeatureFlag(category, feature) {
  const { product } = useContext(ProductContext);
  
  if (!product) {
    console.warn('No product found in ProductContext');
    return false;
  }
  
  return isFeatureEnabled(product, category, feature);
}

/**
 * Example usage:
 * 
 * import { useFeatureFlag } from '../hooks/useFeatureFlag';
 * 
 * function AnalyticsPanel() {
 *   const isAnalyticsEnabled = useFeatureFlag('dashboard', 'analytics');
 *   
 *   if (!isAnalyticsEnabled) {
 *     return null;
 *   }
 *   
 *   return (
 *     <div className="analytics-panel">
 *       {/* Analytics content */}
 *     </div>
 *   );
 * }
 */
