/**
 * Authentication Context
 * 
 * This module provides authentication context and provider for the NovaVision Hub.
 */

import React, { createContext, useContext, useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { usePerformance } from '../performance/usePerformance';

// Create authentication context
const AuthContext = createContext();

/**
 * Authentication provider component
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components
 * @param {Object} [props.authService] - Authentication service
 * @param {boolean} [props.requireAuth=false] - Whether authentication is required
 * @param {string} [props.loginPath='/login'] - Login path
 * @param {Function} [props.onAuthStateChanged] - Callback when auth state changes
 * @returns {React.ReactElement} Authentication provider component
 */
export const AuthProvider = ({
  children,
  authService,
  requireAuth = false,
  loginPath = '/login',
  onAuthStateChanged
}) => {
  const { measureOperation } = usePerformance('AuthProvider');
  
  // State
  const [user, setUser] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  
  // Initialize authentication
  useEffect(() => {
    const initializeAuth = async () => {
      if (!authService) {
        setIsLoading(false);
        return;
      }
      
      try {
        // Check if user is already authenticated
        const currentUser = await measureOperation('getCurrentUser', () => 
          authService.getCurrentUser()
        );
        
        if (currentUser) {
          setUser(currentUser);
          setIsAuthenticated(true);
          
          if (onAuthStateChanged) {
            onAuthStateChanged({ user: currentUser, isAuthenticated: true });
          }
        }
      } catch (err) {
        console.error('Error initializing authentication:', err);
        setError(err);
      } finally {
        setIsLoading(false);
      }
    };
    
    initializeAuth();
    
    // Set up auth state change listener
    if (authService && authService.onAuthStateChanged) {
      const unsubscribe = authService.onAuthStateChanged((user) => {
        setUser(user);
        setIsAuthenticated(!!user);
        
        if (onAuthStateChanged) {
          onAuthStateChanged({ user, isAuthenticated: !!user });
        }
      });
      
      return () => {
        if (unsubscribe) {
          unsubscribe();
        }
      };
    }
  }, [authService, onAuthStateChanged, measureOperation]);
  
  /**
   * Sign in with email and password
   * 
   * @param {string} email - Email
   * @param {string} password - Password
   * @returns {Promise<Object>} User
   */
  const signInWithEmailAndPassword = async (email, password) => {
    if (!authService) {
      throw new Error('Authentication service not provided');
    }
    
    setIsLoading(true);
    setError(null);
    
    try {
      const user = await measureOperation('signInWithEmailAndPassword', () => 
        authService.signInWithEmailAndPassword(email, password)
      );
      
      setUser(user);
      setIsAuthenticated(true);
      return user;
    } catch (err) {
      console.error('Error signing in:', err);
      setError(err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };
  
  /**
   * Sign in with provider
   * 
   * @param {string} provider - Provider name
   * @returns {Promise<Object>} User
   */
  const signInWithProvider = async (provider) => {
    if (!authService) {
      throw new Error('Authentication service not provided');
    }
    
    setIsLoading(true);
    setError(null);
    
    try {
      const user = await measureOperation('signInWithProvider', () => 
        authService.signInWithProvider(provider)
      );
      
      setUser(user);
      setIsAuthenticated(true);
      return user;
    } catch (err) {
      console.error('Error signing in with provider:', err);
      setError(err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };
  
  /**
   * Sign up with email and password
   * 
   * @param {string} email - Email
   * @param {string} password - Password
   * @param {Object} [userData] - Additional user data
   * @returns {Promise<Object>} User
   */
  const signUpWithEmailAndPassword = async (email, password, userData = {}) => {
    if (!authService) {
      throw new Error('Authentication service not provided');
    }
    
    setIsLoading(true);
    setError(null);
    
    try {
      const user = await measureOperation('signUpWithEmailAndPassword', () => 
        authService.signUpWithEmailAndPassword(email, password, userData)
      );
      
      setUser(user);
      setIsAuthenticated(true);
      return user;
    } catch (err) {
      console.error('Error signing up:', err);
      setError(err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };
  
  /**
   * Sign out
   * 
   * @returns {Promise<void>}
   */
  const signOut = async () => {
    if (!authService) {
      throw new Error('Authentication service not provided');
    }
    
    setIsLoading(true);
    setError(null);
    
    try {
      await measureOperation('signOut', () => 
        authService.signOut()
      );
      
      setUser(null);
      setIsAuthenticated(false);
    } catch (err) {
      console.error('Error signing out:', err);
      setError(err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };
  
  /**
   * Reset password
   * 
   * @param {string} email - Email
   * @returns {Promise<void>}
   */
  const resetPassword = async (email) => {
    if (!authService) {
      throw new Error('Authentication service not provided');
    }
    
    setIsLoading(true);
    setError(null);
    
    try {
      await measureOperation('resetPassword', () => 
        authService.resetPassword(email)
      );
    } catch (err) {
      console.error('Error resetting password:', err);
      setError(err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };
  
  /**
   * Update user profile
   * 
   * @param {Object} data - Profile data
   * @returns {Promise<Object>} Updated user
   */
  const updateProfile = async (data) => {
    if (!authService) {
      throw new Error('Authentication service not provided');
    }
    
    if (!user) {
      throw new Error('User not authenticated');
    }
    
    setIsLoading(true);
    setError(null);
    
    try {
      const updatedUser = await measureOperation('updateProfile', () => 
        authService.updateProfile(user, data)
      );
      
      setUser(updatedUser);
      return updatedUser;
    } catch (err) {
      console.error('Error updating profile:', err);
      setError(err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };
  
  /**
   * Change password
   * 
   * @param {string} currentPassword - Current password
   * @param {string} newPassword - New password
   * @returns {Promise<void>}
   */
  const changePassword = async (currentPassword, newPassword) => {
    if (!authService) {
      throw new Error('Authentication service not provided');
    }
    
    if (!user) {
      throw new Error('User not authenticated');
    }
    
    setIsLoading(true);
    setError(null);
    
    try {
      await measureOperation('changePassword', () => 
        authService.changePassword(user, currentPassword, newPassword)
      );
    } catch (err) {
      console.error('Error changing password:', err);
      setError(err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };
  
  /**
   * Get user token
   * 
   * @returns {Promise<string>} Token
   */
  const getToken = async () => {
    if (!authService) {
      throw new Error('Authentication service not provided');
    }
    
    if (!user) {
      throw new Error('User not authenticated');
    }
    
    try {
      return await measureOperation('getToken', () => 
        authService.getToken(user)
      );
    } catch (err) {
      console.error('Error getting token:', err);
      setError(err);
      throw err;
    }
  };
  
  // Create context value
  const contextValue = {
    user,
    isLoading,
    error,
    isAuthenticated,
    requireAuth,
    loginPath,
    signInWithEmailAndPassword,
    signInWithProvider,
    signUpWithEmailAndPassword,
    signOut,
    resetPassword,
    updateProfile,
    changePassword,
    getToken
  };
  
  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

AuthProvider.propTypes = {
  children: PropTypes.node.isRequired,
  authService: PropTypes.object,
  requireAuth: PropTypes.bool,
  loginPath: PropTypes.string,
  onAuthStateChanged: PropTypes.func
};

/**
 * Use authentication hook
 * 
 * @returns {Object} Authentication context
 */
export const useAuth = () => {
  const context = useContext(AuthContext);
  
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  
  return context;
};

export default AuthContext;
