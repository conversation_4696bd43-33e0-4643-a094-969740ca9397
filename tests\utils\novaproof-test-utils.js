/**
 * NovaProof Test Utilities
 * 
 * This file contains utility functions for testing NovaProof components.
 */

const crypto = require('crypto');
const { performance } = require('perf_hooks');

/**
 * Generate a random evidence item for testing
 * @param {Object} options - Options for generating the evidence
 * @returns {Object} - A random evidence item
 */
function generateEvidenceItem(options = {}) {
  const defaultOptions = {
    controlId: `C-${Math.floor(Math.random() * 1000)}`,
    framework: 'NIST-CSF',
    source: 'test-system',
    withAttachment: false
  };

  const mergedOptions = { ...defaultOptions, ...options };
  
  const evidenceItem = {
    id: crypto.randomUUID(),
    controlId: mergedOptions.controlId,
    framework: mergedOptions.framework,
    source: mergedOptions.source,
    timestamp: new Date().toISOString(),
    data: {
      value: Math.random() > 0.5,
      details: `Evidence for ${mergedOptions.controlId}`,
      score: Math.floor(Math.random() * 100)
    },
    metadata: {
      collector: 'NovaProof-Test',
      version: '1.0.0',
      environment: 'test'
    },
    status: 'COLLECTED'
  };

  if (mergedOptions.withAttachment) {
    evidenceItem.attachment = {
      filename: 'test-attachment.pdf',
      contentType: 'application/pdf',
      size: 1024,
      hash: crypto.createHash('sha256').update('test-content').digest('hex')
    };
  }

  return evidenceItem;
}

/**
 * Generate a blockchain transaction for testing
 * @param {Object} evidenceItem - The evidence item to verify
 * @returns {Object} - A blockchain transaction
 */
function generateBlockchainTransaction(evidenceItem) {
  const contentHash = crypto.createHash('sha256')
    .update(JSON.stringify(evidenceItem))
    .digest('hex');
  
  return {
    id: crypto.randomUUID(),
    evidenceId: evidenceItem.id,
    timestamp: new Date().toISOString(),
    contentHash,
    blockchainType: 'ETHEREUM',
    transactionId: `0x${crypto.randomBytes(32).toString('hex')}`,
    blockNumber: Math.floor(Math.random() * 1000000),
    status: 'CONFIRMED'
  };
}

/**
 * Generate a verification proof for testing
 * @param {Object} evidenceItem - The evidence item
 * @param {Object} transaction - The blockchain transaction
 * @returns {Object} - A verification proof
 */
function generateVerificationProof(evidenceItem, transaction) {
  const contentHash = transaction.contentHash;
  
  // Generate a simple Merkle proof
  const siblingHash1 = crypto.randomBytes(32).toString('hex');
  const siblingHash2 = crypto.randomBytes(32).toString('hex');
  
  const combinedHash1 = crypto.createHash('sha256')
    .update(contentHash + siblingHash1)
    .digest('hex');
  
  const combinedHash2 = crypto.createHash('sha256')
    .update(combinedHash1 + siblingHash2)
    .digest('hex');
  
  return {
    id: crypto.randomUUID(),
    evidenceId: evidenceItem.id,
    transactionId: transaction.transactionId,
    contentHash,
    merkleRoot: combinedHash2,
    merkleProof: [
      { position: 'right', hash: siblingHash1 },
      { position: 'right', hash: siblingHash2 }
    ],
    timestamp: new Date().toISOString(),
    status: 'VALID'
  };
}

/**
 * Verify a Merkle proof
 * @param {string} contentHash - The content hash to verify
 * @param {string} merkleRoot - The Merkle root
 * @param {Array} merkleProof - The Merkle proof
 * @returns {boolean} - Whether the proof is valid
 */
function verifyMerkleProof(contentHash, merkleRoot, merkleProof) {
  let currentHash = contentHash;
  
  for (const node of merkleProof) {
    if (node.position === 'right') {
      currentHash = crypto.createHash('sha256')
        .update(currentHash + node.hash)
        .digest('hex');
    } else {
      currentHash = crypto.createHash('sha256')
        .update(node.hash + currentHash)
        .digest('hex');
    }
  }
  
  return currentHash === merkleRoot;
}

/**
 * Generate a complete evidence verification record
 * @param {Object} options - Options for generating the record
 * @returns {Object} - A complete evidence verification record
 */
function generateCompleteVerificationRecord(options = {}) {
  const evidenceItem = generateEvidenceItem(options);
  const transaction = generateBlockchainTransaction(evidenceItem);
  const proof = generateVerificationProof(evidenceItem, transaction);
  
  return {
    evidence: evidenceItem,
    transaction,
    proof,
    isValid: true
  };
}

/**
 * Measure performance of a verification function
 * @param {Function} fn - Function to measure
 * @param {Array} args - Arguments to pass to the function
 * @returns {Object} - Performance metrics
 */
async function measureVerificationPerformance(fn, args = []) {
  const start = performance.now();
  const result = await fn(...args);
  const end = performance.now();
  
  return {
    result,
    executionTime: end - start,
    timestamp: new Date().toISOString()
  };
}

module.exports = {
  generateEvidenceItem,
  generateBlockchainTransaction,
  generateVerificationProof,
  verifyMerkleProof,
  generateCompleteVerificationRecord,
  measureVerificationPerformance
};
