/**
 * NovaFuse Universal API Connector User Authentication Service
 * 
 * This service handles user authentication, including JWT, OAuth2, and SSO integration.
 * It provides a comprehensive authentication system for the NovaConnect UAC.
 */

const jwt = require('jsonwebtoken');
const bcrypt = require('bcrypt');
const crypto = require('crypto');
const mongoose = require('mongoose');
const { v4: uuidv4 } = require('uuid');

class UserAuthenticationService {
  constructor(config = {}) {
    this.config = {
      jwtSecret: process.env.JWT_SECRET || 'novafuse-uac-jwt-secret-key',
      jwtExpiresIn: process.env.JWT_EXPIRES_IN || '24h',
      refreshTokenExpiresIn: process.env.REFRESH_TOKEN_EXPIRES_IN || '7d',
      passwordResetExpiresIn: process.env.PASSWORD_RESET_EXPIRES_IN || '1h',
      bcryptSaltRounds: parseInt(process.env.BCRYPT_SALT_ROUNDS) || 10,
      ...config
    };
    
    this.db = null;
    this.UserModel = null;
    this.TokenModel = null;
  }

  /**
   * Initialize the authentication service
   * @param {Object} db - MongoDB connection
   * @returns {Promise<void>}
   */
  async initialize(db) {
    this.db = db;
    
    // Define User schema
    const userSchema = new mongoose.Schema({
      username: { 
        type: String, 
        required: true, 
        unique: true, 
        trim: true 
      },
      email: { 
        type: String, 
        required: true, 
        unique: true, 
        trim: true,
        lowercase: true,
        match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please provide a valid email address']
      },
      password: { 
        type: String, 
        required: true 
      },
      firstName: { 
        type: String, 
        trim: true 
      },
      lastName: { 
        type: String, 
        trim: true 
      },
      role: { 
        type: String, 
        enum: ['admin', 'user', 'readonly'], 
        default: 'user' 
      },
      permissions: [{ 
        type: String 
      }],
      status: { 
        type: String, 
        enum: ['active', 'inactive', 'suspended', 'pending'], 
        default: 'pending' 
      },
      lastLogin: { 
        type: Date 
      },
      failedLoginAttempts: { 
        type: Number, 
        default: 0 
      },
      lockUntil: { 
        type: Date 
      },
      passwordResetToken: { 
        type: String 
      },
      passwordResetExpires: { 
        type: Date 
      },
      emailVerificationToken: { 
        type: String 
      },
      emailVerified: { 
        type: Boolean, 
        default: false 
      },
      twoFactorEnabled: { 
        type: Boolean, 
        default: false 
      },
      twoFactorSecret: { 
        type: String 
      },
      authProvider: { 
        type: String, 
        enum: ['local', 'google', 'microsoft', 'saml', 'oidc'], 
        default: 'local' 
      },
      authProviderId: { 
        type: String 
      },
      metadata: { 
        type: Object 
      },
      createdAt: { 
        type: Date, 
        default: Date.now 
      },
      updatedAt: { 
        type: Date, 
        default: Date.now 
      }
    });

    // Add pre-save hook to hash password
    userSchema.pre('save', async function(next) {
      // Only hash the password if it's modified or new
      if (!this.isModified('password')) return next();
      
      try {
        // Generate salt and hash password
        const salt = await bcrypt.genSalt(this.config?.bcryptSaltRounds || 10);
        this.password = await bcrypt.hash(this.password, salt);
        next();
      } catch (error) {
        next(error);
      }
    });

    // Add method to compare password
    userSchema.methods.comparePassword = async function(candidatePassword) {
      return bcrypt.compare(candidatePassword, this.password);
    };

    // Add method to check if account is locked
    userSchema.methods.isLocked = function() {
      return !!(this.lockUntil && this.lockUntil > Date.now());
    };

    // Define Token schema
    const tokenSchema = new mongoose.Schema({
      userId: { 
        type: mongoose.Schema.Types.ObjectId, 
        ref: 'User', 
        required: true 
      },
      token: { 
        type: String, 
        required: true 
      },
      type: { 
        type: String, 
        enum: ['access', 'refresh', 'reset', 'verification'], 
        required: true 
      },
      expiresAt: { 
        type: Date, 
        required: true 
      },
      createdAt: { 
        type: Date, 
        default: Date.now 
      },
      revokedAt: { 
        type: Date 
      },
      revokedReason: { 
        type: String 
      },
      metadata: { 
        type: Object 
      }
    });

    // Add index for token expiration
    tokenSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });

    // Register models
    this.UserModel = mongoose.model('User', userSchema);
    this.TokenModel = mongoose.model('Token', tokenSchema);

    console.log('User Authentication Service initialized');
  }

  /**
   * Register a new user
   * @param {Object} userData - User data
   * @returns {Promise<Object>} - Registered user
   */
  async registerUser(userData) {
    // Validate required fields
    if (!userData.username || !userData.email || !userData.password) {
      throw new Error('Username, email, and password are required');
    }

    // Check if user already exists
    const existingUser = await this.UserModel.findOne({
      $or: [
        { username: userData.username },
        { email: userData.email }
      ]
    });

    if (existingUser) {
      throw new Error('User with this username or email already exists');
    }

    // Create new user
    const user = new this.UserModel({
      username: userData.username,
      email: userData.email,
      password: userData.password,
      firstName: userData.firstName,
      lastName: userData.lastName,
      role: userData.role || 'user',
      permissions: userData.permissions || [],
      status: userData.status || 'pending',
      emailVerificationToken: crypto.randomBytes(32).toString('hex'),
      metadata: userData.metadata || {}
    });

    // Save user
    await user.save();

    // Return user without sensitive data
    const userObject = user.toObject();
    delete userObject.password;
    delete userObject.twoFactorSecret;
    delete userObject.emailVerificationToken;
    delete userObject.passwordResetToken;
    delete userObject.passwordResetExpires;

    return userObject;
  }

  /**
   * Login user
   * @param {string} username - Username or email
   * @param {string} password - Password
   * @returns {Promise<Object>} - Login result with tokens
   */
  async loginUser(username, password) {
    // Find user by username or email
    const user = await this.UserModel.findOne({
      $or: [
        { username },
        { email: username }
      ]
    });

    if (!user) {
      throw new Error('Invalid username or password');
    }

    // Check if account is locked
    if (user.isLocked()) {
      throw new Error('Account is locked. Please try again later');
    }

    // Check if account is active
    if (user.status !== 'active') {
      throw new Error(`Account is ${user.status}. Please contact support`);
    }

    // Verify password
    const isPasswordValid = await user.comparePassword(password);

    if (!isPasswordValid) {
      // Increment failed login attempts
      user.failedLoginAttempts += 1;

      // Lock account after 5 failed attempts
      if (user.failedLoginAttempts >= 5) {
        user.lockUntil = new Date(Date.now() + 30 * 60 * 1000); // Lock for 30 minutes
      }

      await user.save();
      throw new Error('Invalid username or password');
    }

    // Reset failed login attempts
    user.failedLoginAttempts = 0;
    user.lastLogin = new Date();
    await user.save();

    // Generate tokens
    const accessToken = this.generateAccessToken(user);
    const refreshToken = this.generateRefreshToken(user);

    // Save refresh token
    await this.saveToken(user._id, refreshToken, 'refresh', this.config.refreshTokenExpiresIn);

    return {
      user: {
        id: user._id,
        username: user.username,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        permissions: user.permissions,
        twoFactorEnabled: user.twoFactorEnabled
      },
      accessToken,
      refreshToken,
      expiresIn: this.getExpirationTime(this.config.jwtExpiresIn)
    };
  }

  /**
   * Verify JWT token
   * @param {string} token - JWT token
   * @returns {Promise<Object>} - Decoded token
   */
  async verifyToken(token) {
    try {
      // Verify token signature
      const decoded = jwt.verify(token, this.config.jwtSecret);

      // Check if token is in the database and not revoked
      const tokenRecord = await this.TokenModel.findOne({
        token,
        type: 'access',
        revokedAt: null
      });

      if (!tokenRecord) {
        throw new Error('Token not found or revoked');
      }

      // Check if user exists and is active
      const user = await this.UserModel.findById(decoded.sub);

      if (!user || user.status !== 'active') {
        throw new Error('User not found or inactive');
      }

      return decoded;
    } catch (error) {
      if (error.name === 'TokenExpiredError') {
        throw new Error('Token expired');
      }

      if (error.name === 'JsonWebTokenError') {
        throw new Error('Invalid token');
      }

      throw error;
    }
  }

  /**
   * Refresh access token using refresh token
   * @param {string} refreshToken - Refresh token
   * @returns {Promise<Object>} - New tokens
   */
  async refreshToken(refreshToken) {
    try {
      // Verify refresh token
      const decoded = jwt.verify(refreshToken, this.config.jwtSecret);

      // Check if token is in the database and not revoked
      const tokenRecord = await this.TokenModel.findOne({
        token: refreshToken,
        type: 'refresh',
        revokedAt: null
      });

      if (!tokenRecord) {
        throw new Error('Refresh token not found or revoked');
      }

      // Get user
      const user = await this.UserModel.findById(decoded.sub);

      if (!user || user.status !== 'active') {
        throw new Error('User not found or inactive');
      }

      // Generate new tokens
      const accessToken = this.generateAccessToken(user);
      const newRefreshToken = this.generateRefreshToken(user);

      // Revoke old refresh token
      await this.revokeToken(refreshToken, 'Refreshed');

      // Save new refresh token
      await this.saveToken(user._id, newRefreshToken, 'refresh', this.config.refreshTokenExpiresIn);

      return {
        accessToken,
        refreshToken: newRefreshToken,
        expiresIn: this.getExpirationTime(this.config.jwtExpiresIn)
      };
    } catch (error) {
      if (error.name === 'TokenExpiredError') {
        throw new Error('Refresh token expired');
      }

      if (error.name === 'JsonWebTokenError') {
        throw new Error('Invalid refresh token');
      }

      throw error;
    }
  }

  /**
   * Logout user
   * @param {string} userId - User ID
   * @param {string} refreshToken - Refresh token
   * @returns {Promise<boolean>} - Logout success
   */
  async logoutUser(userId, refreshToken) {
    // Revoke refresh token
    await this.revokeToken(refreshToken, 'Logout');

    // Revoke all access tokens for user
    await this.TokenModel.updateMany(
      { userId, type: 'access', revokedAt: null },
      { revokedAt: new Date(), revokedReason: 'Logout' }
    );

    return true;
  }

  /**
   * Request password reset
   * @param {string} email - User email
   * @returns {Promise<Object>} - Password reset token
   */
  async requestPasswordReset(email) {
    // Find user by email
    const user = await this.UserModel.findOne({ email });

    if (!user) {
      throw new Error('User not found');
    }

    // Generate reset token
    const resetToken = crypto.randomBytes(32).toString('hex');
    const hashedToken = crypto.createHash('sha256').update(resetToken).digest('hex');

    // Save reset token
    user.passwordResetToken = hashedToken;
    user.passwordResetExpires = new Date(Date.now() + this.getExpirationTime(this.config.passwordResetExpiresIn) * 1000);
    await user.save();

    return {
      userId: user._id,
      resetToken,
      expiresIn: this.getExpirationTime(this.config.passwordResetExpiresIn)
    };
  }

  /**
   * Reset password
   * @param {string} resetToken - Reset token
   * @param {string} newPassword - New password
   * @returns {Promise<boolean>} - Password reset success
   */
  async resetPassword(resetToken, newPassword) {
    // Hash token
    const hashedToken = crypto.createHash('sha256').update(resetToken).digest('hex');

    // Find user by reset token
    const user = await this.UserModel.findOne({
      passwordResetToken: hashedToken,
      passwordResetExpires: { $gt: Date.now() }
    });

    if (!user) {
      throw new Error('Invalid or expired reset token');
    }

    // Update password
    user.password = newPassword;
    user.passwordResetToken = undefined;
    user.passwordResetExpires = undefined;
    await user.save();

    // Revoke all tokens for user
    await this.TokenModel.updateMany(
      { userId: user._id, revokedAt: null },
      { revokedAt: new Date(), revokedReason: 'Password reset' }
    );

    return true;
  }

  /**
   * Verify email
   * @param {string} verificationToken - Email verification token
   * @returns {Promise<boolean>} - Email verification success
   */
  async verifyEmail(verificationToken) {
    // Find user by verification token
    const user = await this.UserModel.findOne({
      emailVerificationToken: verificationToken
    });

    if (!user) {
      throw new Error('Invalid verification token');
    }

    // Update user
    user.emailVerified = true;
    user.emailVerificationToken = undefined;
    user.status = 'active';
    await user.save();

    return true;
  }

  /**
   * Enable two-factor authentication
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - 2FA setup data
   */
  async enableTwoFactor(userId) {
    // Find user
    const user = await this.UserModel.findById(userId);

    if (!user) {
      throw new Error('User not found');
    }

    // Generate 2FA secret
    const secret = crypto.randomBytes(20).toString('hex');

    // Save secret
    user.twoFactorSecret = secret;
    await user.save();

    // In a real implementation, this would generate a QR code for the user
    // to scan with their authenticator app

    return {
      secret,
      otpAuthUrl: `otpauth://totp/NovaConnect:${user.username}?secret=${secret}&issuer=NovaConnect`
    };
  }

  /**
   * Verify two-factor authentication code
   * @param {string} userId - User ID
   * @param {string} code - 2FA code
   * @returns {Promise<boolean>} - Verification success
   */
  async verifyTwoFactorCode(userId, code) {
    // Find user
    const user = await this.UserModel.findById(userId);

    if (!user || !user.twoFactorSecret) {
      throw new Error('User not found or 2FA not enabled');
    }

    // In a real implementation, this would verify the code against the user's secret
    // using a library like speakeasy

    // For now, we'll just check if the code is '123456' for testing
    if (code !== '123456') {
      throw new Error('Invalid 2FA code');
    }

    // If this is the first verification, enable 2FA
    if (!user.twoFactorEnabled) {
      user.twoFactorEnabled = true;
      await user.save();
    }

    return true;
  }

  /**
   * Disable two-factor authentication
   * @param {string} userId - User ID
   * @returns {Promise<boolean>} - Disable success
   */
  async disableTwoFactor(userId) {
    // Find user
    const user = await this.UserModel.findById(userId);

    if (!user) {
      throw new Error('User not found');
    }

    // Disable 2FA
    user.twoFactorEnabled = false;
    user.twoFactorSecret = undefined;
    await user.save();

    return true;
  }

  /**
   * Generate access token
   * @param {Object} user - User object
   * @returns {string} - JWT token
   */
  generateAccessToken(user) {
    const payload = {
      sub: user._id,
      username: user.username,
      email: user.email,
      role: user.role,
      permissions: user.permissions,
      type: 'access'
    };

    return jwt.sign(payload, this.config.jwtSecret, { expiresIn: this.config.jwtExpiresIn });
  }

  /**
   * Generate refresh token
   * @param {Object} user - User object
   * @returns {string} - JWT token
   */
  generateRefreshToken(user) {
    const payload = {
      sub: user._id,
      type: 'refresh'
    };

    return jwt.sign(payload, this.config.jwtSecret, { expiresIn: this.config.refreshTokenExpiresIn });
  }

  /**
   * Save token to database
   * @param {string} userId - User ID
   * @param {string} token - Token
   * @param {string} type - Token type
   * @param {string} expiresIn - Expiration time
   * @returns {Promise<Object>} - Saved token
   */
  async saveToken(userId, token, type, expiresIn) {
    const expiresAt = new Date(Date.now() + this.getExpirationTime(expiresIn) * 1000);

    const tokenRecord = new this.TokenModel({
      userId,
      token,
      type,
      expiresAt
    });

    return tokenRecord.save();
  }

  /**
   * Revoke token
   * @param {string} token - Token
   * @param {string} reason - Revocation reason
   * @returns {Promise<boolean>} - Revocation success
   */
  async revokeToken(token, reason = 'Revoked') {
    const result = await this.TokenModel.updateOne(
      { token, revokedAt: null },
      { revokedAt: new Date(), revokedReason: reason }
    );

    return result.modifiedCount > 0;
  }

  /**
   * Get expiration time in seconds
   * @param {string} expiresIn - Expiration time string
   * @returns {number} - Expiration time in seconds
   */
  getExpirationTime(expiresIn) {
    const match = expiresIn.match(/^(\d+)([smhd])$/);
    
    if (!match) {
      return 3600; // Default to 1 hour
    }
    
    const value = parseInt(match[1]);
    const unit = match[2];
    
    switch (unit) {
      case 's': return value;
      case 'm': return value * 60;
      case 'h': return value * 60 * 60;
      case 'd': return value * 24 * 60 * 60;
      default: return 3600;
    }
  }
}

module.exports = UserAuthenticationService;
