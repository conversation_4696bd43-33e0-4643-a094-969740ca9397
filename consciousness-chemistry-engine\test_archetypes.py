"""
Test script for folding archetypes in NovaFoldClient.
"""

from src.ConsciousNovaFold import NovaFoldClient

def run_tests():
    print("Starting folding archetypes test...\n")
    
    # Initialize client
    client = NovaFoldClient(enable_benchmark=False)
    test_sequence = "ACDEFGHIKLMNPQRSTVWY"  # 20 amino acids
    
    # Test 1: List available archetypes
    print("1. Testing list_archetypes()...")
    archetypes = client.list_archetypes()
    print(f"Available archetypes: {', '.join(archetypes)}")
    
    # Test 2: Get archetype info
    print("\n2. Testing get_archetype_info()...")
    for archetype in ['native', 'misfolded', 'amyloid', 'consciousness_enhanced', 'entangled']:
        info = client.get_archetype_info(archetype)
        print(f"{archetype}: {info.get('description', 'No description')}")
    
    # Test 3: Make predictions with different archetypes
    print("\n3. Testing predictions with different archetypes...")
    variants = ['native', 'misfolded', 'amyloid', 'consciousness_enhanced', 'entangled']
    
    for variant in variants:
        print(f"\n--- {variant.upper()} ---")
        result = client.predict(
            sequence=test_sequence,
            folding_variant=variant,
            validate_against=None
        )
        
        # Extract and display relevant information
        metadata = result['structure']['metadata']
        ss = result['structure']['secondary_structure']
        ss_counts = {s: ss.count(s) for s in set(ss)}
        plddt_avg = sum(result['structure']['plddt']) / len(result['structure']['plddt'])
        
        print(f"Archetype: {metadata['folding_archetype']}")
        print(f"Description: {metadata['archetype_description']}")
        print(f"Consciousness impact: {metadata['consciousness_impact']}")
        print(f"Secondary structure: {ss}")
        print(f"SS counts: {ss_counts}")
        print(f"Avg pLDDT: {plddt_avg:.2f}")
        
        # Print special properties if they exist
        if 'cross_beta_pattern' in metadata:
            print("Cross-beta pattern detected (amyloid)")
        if 'quantum_entangled' in metadata:
            print(f"Quantum properties: {metadata.get('quantum_entropy', 'N/A')}")
    
    print("\nAll tests completed!")

if __name__ == "__main__":
    run_tests()
