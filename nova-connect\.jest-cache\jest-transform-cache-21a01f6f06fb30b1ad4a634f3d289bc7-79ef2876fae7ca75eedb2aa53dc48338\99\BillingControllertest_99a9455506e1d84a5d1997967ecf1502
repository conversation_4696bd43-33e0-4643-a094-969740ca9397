c80be5cdd76fe25c108191f4f409ca22
// Mock the BillingService
_getJestObj().mock('../../../api/services/BillingService');
function _getJestObj() {
  const {
    jest
  } = require("@jest/globals");
  _getJestObj = () => jest;
  return jest;
}
/**
 * Billing Controller Tests
 */

const BillingController = require('../../../api/controllers/BillingController');
const BillingService = require('../../../api/services/BillingService');
describe('BillingController', () => {
  let req, res, next;
  beforeEach(() => {
    // Mock request, response, and next
    req = {
      params: {},
      query: {},
      body: {}
    };
    res = {
      json: jest.fn(),
      status: jest.fn().mockReturnThis()
    };
    next = jest.fn();

    // Mock BillingService methods
    BillingService.mockImplementation(() => ({
      enableFeatures: jest.fn().mockResolvedValue(),
      updateFeatures: jest.fn().mockResolvedValue(),
      disableFeatures: jest.fn().mockResolvedValue(),
      activateFeatures: jest.fn().mockResolvedValue(),
      suspendFeatures: jest.fn().mockResolvedValue(),
      getCustomerEntitlements: jest.fn().mockResolvedValue({
        status: 'ACTIVE',
        plan: 'enterprise'
      }),
      getCustomerUsage: jest.fn().mockResolvedValue({
        customerId: 'test-customer',
        usage: {},
        totals: {}
      }),
      reportUsage: jest.fn().mockResolvedValue(),
      reportTenantUsage: jest.fn().mockResolvedValue()
    }));
  });
  afterEach(() => {
    jest.clearAllMocks();
  });
  test('handleEntitlementCreation should create entitlement', async () => {
    req.body = {
      customerId: 'test-customer',
      entitlement: {
        plan: 'enterprise'
      }
    };
    await BillingController.handleEntitlementCreation(req, res, next);
    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith({
      message: 'Entitlement created successfully',
      customerId: 'test-customer',
      entitlement: {
        plan: 'enterprise',
        status: 'ACTIVE'
      }
    });
  });
  test('handleEntitlementCreation should validate required fields', async () => {
    req.body = {
      customerId: 'test-customer'
      // Missing entitlement
    };
    await BillingController.handleEntitlementCreation(req, res, next);
    expect(res.status).toHaveBeenCalledWith(400);
    expect(res.json).toHaveBeenCalledWith({
      error: 'Bad Request',
      message: 'Customer ID and entitlement are required'
    });
  });
  test('handleEntitlementUpdate should update entitlement', async () => {
    req.body = {
      customerId: 'test-customer',
      entitlement: {
        plan: 'enterprise'
      }
    };
    await BillingController.handleEntitlementUpdate(req, res, next);
    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith({
      message: 'Entitlement updated successfully',
      customerId: 'test-customer',
      entitlement: {
        plan: 'enterprise'
      }
    });
  });
  test('handleEntitlementDeletion should delete entitlement', async () => {
    req.body = {
      customerId: 'test-customer',
      entitlement: {
        plan: 'enterprise'
      }
    };
    await BillingController.handleEntitlementDeletion(req, res, next);
    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith({
      message: 'Entitlement deleted successfully',
      customerId: 'test-customer'
    });
  });
  test('handleEntitlementActivation should activate entitlement', async () => {
    req.body = {
      customerId: 'test-customer',
      entitlement: {
        plan: 'enterprise'
      }
    };
    await BillingController.handleEntitlementActivation(req, res, next);
    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith({
      message: 'Entitlement activated successfully',
      customerId: 'test-customer'
    });
  });
  test('handleEntitlementSuspension should suspend entitlement', async () => {
    req.body = {
      customerId: 'test-customer',
      entitlement: {
        plan: 'enterprise'
      }
    };
    await BillingController.handleEntitlementSuspension(req, res, next);
    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith({
      message: 'Entitlement suspended successfully',
      customerId: 'test-customer'
    });
  });
  test('getCustomerEntitlements should get entitlements', async () => {
    req.params = {
      customerId: 'test-customer'
    };
    await BillingController.getCustomerEntitlements(req, res, next);
    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith({
      status: 'ACTIVE',
      plan: 'enterprise'
    });
  });
  test('getCustomerUsage should get usage', async () => {
    req.params = {
      customerId: 'test-customer'
    };
    req.query = {
      startDate: '2023-01-01',
      endDate: '2023-01-31'
    };
    await BillingController.getCustomerUsage(req, res, next);
    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith({
      customerId: 'test-customer',
      usage: {},
      totals: {}
    });
  });
  test('reportUsage should report usage', async () => {
    req.body = {
      customerId: 'test-customer',
      metricName: 'api-calls',
      quantity: 10,
      timestamp: '2023-01-01T00:00:00Z',
      tenantId: 'test-tenant'
    };
    await BillingController.reportUsage(req, res, next);
    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith({
      message: 'Usage reported successfully',
      customerId: 'test-customer',
      metricName: 'api-calls',
      quantity: 10
    });
  });
  test('reportTenantUsage should report tenant usage', async () => {
    req.body = {
      tenantId: 'test-tenant',
      metricName: 'api-calls',
      quantity: 10,
      timestamp: '2023-01-01T00:00:00Z'
    };
    await BillingController.reportTenantUsage(req, res, next);
    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith({
      message: 'Tenant usage reported successfully',
      tenantId: 'test-tenant',
      metricName: 'api-calls',
      quantity: 10
    });
  });
  test('handleMarketplaceWebhook should handle webhook', async () => {
    req.body = {
      event: 'ENTITLEMENT_CREATION',
      resource: {
        customerId: 'test-customer',
        plan: 'enterprise'
      }
    };
    await BillingController.handleMarketplaceWebhook(req, res, next);
    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith({
      message: 'Webhook processed successfully',
      event: 'ENTITLEMENT_CREATION',
      customerId: 'test-customer'
    });
  });
  test('handleMarketplaceWebhook should validate required fields', async () => {
    req.body = {
      event: 'ENTITLEMENT_CREATION'
      // Missing resource
    };
    await BillingController.handleMarketplaceWebhook(req, res, next);
    expect(res.status).toHaveBeenCalledWith(400);
    expect(res.json).toHaveBeenCalledWith({
      error: 'Bad Request',
      message: 'Event and resource are required'
    });
  });
  test('handleMarketplaceWebhook should validate customer ID', async () => {
    req.body = {
      event: 'ENTITLEMENT_CREATION',
      resource: {
        // Missing customerId
        plan: 'enterprise'
      }
    };
    await BillingController.handleMarketplaceWebhook(req, res, next);
    expect(res.status).toHaveBeenCalledWith(400);
    expect(res.json).toHaveBeenCalledWith({
      error: 'Bad Request',
      message: 'Customer ID is required in resource'
    });
  });
  test('handleMarketplaceWebhook should validate event type', async () => {
    req.body = {
      event: 'UNKNOWN_EVENT',
      resource: {
        customerId: 'test-customer',
        plan: 'enterprise'
      }
    };
    await BillingController.handleMarketplaceWebhook(req, res, next);
    expect(res.status).toHaveBeenCalledWith(400);
    expect(res.json).toHaveBeenCalledWith({
      error: 'Bad Request',
      message: 'Unknown event type: UNKNOWN_EVENT'
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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