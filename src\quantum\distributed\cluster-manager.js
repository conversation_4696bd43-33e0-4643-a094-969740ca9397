/**
 * Cluster Manager
 *
 * This module provides cluster management capabilities for the Finite Universe
 * Principle defense system, enabling distributed processing across multiple nodes.
 */

const EventEmitter = require('events');
const os = require('os');
const crypto = require('crypto');
const { v4: uuidv4 } = require('uuid');

/**
 * ClusterManager class
 * 
 * Manages a cluster of nodes for distributed processing.
 */
class ClusterManager extends EventEmitter {
  /**
   * Constructor
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();

    this.options = {
      enableLogging: true,
      nodeId: options.nodeId || this._generateNodeId(),
      isMaster: options.isMaster !== undefined ? options.isMaster : true,
      masterAddress: options.masterAddress || 'localhost',
      masterPort: options.masterPort || 8000,
      heartbeatInterval: options.heartbeatInterval || 5000, // 5 seconds
      nodeTimeout: options.nodeTimeout || 15000, // 15 seconds
      discoveryEnabled: options.discoveryEnabled !== undefined ? options.discoveryEnabled : true,
      discoveryInterval: options.discoveryInterval || 10000, // 10 seconds
      ...options
    };

    // Initialize node information
    this.nodeInfo = {
      id: this.options.nodeId,
      hostname: os.hostname(),
      address: this._getLocalAddress(),
      isMaster: this.options.isMaster,
      cpus: os.cpus().length,
      memory: os.totalmem(),
      freeMemory: os.freemem(),
      platform: os.platform(),
      arch: os.arch(),
      uptime: process.uptime(),
      status: 'initializing',
      lastHeartbeat: Date.now(),
      capabilities: options.capabilities || ['default'],
      load: 0,
      tasks: 0
    };

    // Initialize nodes registry
    this.nodes = new Map();
    if (this.options.isMaster) {
      // Register self as master
      this.nodes.set(this.nodeInfo.id, { ...this.nodeInfo });
    }

    // Initialize tasks registry
    this.tasks = new Map();
    this.taskResults = new Map();

    // Initialize intervals
    this.heartbeatInterval = null;
    this.discoveryInterval = null;
    this.nodeCleanupInterval = null;

    if (this.options.enableLogging) {
      console.log('ClusterManager initialized with options:', this.options);
      console.log('Node information:', this.nodeInfo);
    }
  }

  /**
   * Start the cluster manager
   */
  start() {
    if (this.nodeInfo.status !== 'initializing') {
      return;
    }

    // Update node status
    this.nodeInfo.status = 'active';
    
    // Start heartbeat interval
    this.heartbeatInterval = setInterval(() => {
      this._sendHeartbeat();
    }, this.options.heartbeatInterval);
    
    // Start discovery interval if enabled
    if (this.options.discoveryEnabled) {
      this.discoveryInterval = setInterval(() => {
        this._discoverNodes();
      }, this.options.discoveryInterval);
    }
    
    // Start node cleanup interval if master
    if (this.options.isMaster) {
      this.nodeCleanupInterval = setInterval(() => {
        this._cleanupInactiveNodes();
      }, this.options.nodeTimeout);
    }
    
    if (this.options.enableLogging) {
      console.log(`ClusterManager started. Node ID: ${this.nodeInfo.id}, Role: ${this.options.isMaster ? 'Master' : 'Worker'}`);
    }
    
    // Emit start event
    this.emit('start', { nodeId: this.nodeInfo.id, isMaster: this.options.isMaster });
  }

  /**
   * Stop the cluster manager
   */
  stop() {
    // Update node status
    this.nodeInfo.status = 'stopping';
    
    // Clear intervals
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
    
    if (this.discoveryInterval) {
      clearInterval(this.discoveryInterval);
      this.discoveryInterval = null;
    }
    
    if (this.nodeCleanupInterval) {
      clearInterval(this.nodeCleanupInterval);
      this.nodeCleanupInterval = null;
    }
    
    // Notify other nodes if connected
    this._notifyShutdown();
    
    // Update node status
    this.nodeInfo.status = 'stopped';
    
    if (this.options.enableLogging) {
      console.log(`ClusterManager stopped. Node ID: ${this.nodeInfo.id}`);
    }
    
    // Emit stop event
    this.emit('stop', { nodeId: this.nodeInfo.id });
  }

  /**
   * Register a node in the cluster
   * @param {Object} nodeInfo - Node information
   * @returns {boolean} - True if node was registered, false otherwise
   */
  registerNode(nodeInfo) {
    if (!this.options.isMaster) {
      if (this.options.enableLogging) {
        console.log(`Cannot register node ${nodeInfo.id}: Not a master node`);
      }
      return false;
    }
    
    // Validate node information
    if (!nodeInfo || !nodeInfo.id) {
      if (this.options.enableLogging) {
        console.log('Invalid node information:', nodeInfo);
      }
      return false;
    }
    
    // Update node information
    const existingNode = this.nodes.get(nodeInfo.id);
    if (existingNode) {
      // Update existing node
      this.nodes.set(nodeInfo.id, {
        ...existingNode,
        ...nodeInfo,
        lastHeartbeat: Date.now()
      });
      
      if (this.options.enableLogging) {
        console.log(`Node ${nodeInfo.id} updated`);
      }
    } else {
      // Register new node
      this.nodes.set(nodeInfo.id, {
        ...nodeInfo,
        lastHeartbeat: Date.now()
      });
      
      if (this.options.enableLogging) {
        console.log(`Node ${nodeInfo.id} registered`);
      }
      
      // Emit node-registered event
      this.emit('node-registered', { nodeId: nodeInfo.id, nodeInfo });
    }
    
    return true;
  }

  /**
   * Unregister a node from the cluster
   * @param {string} nodeId - Node ID
   * @returns {boolean} - True if node was unregistered, false otherwise
   */
  unregisterNode(nodeId) {
    if (!this.options.isMaster) {
      if (this.options.enableLogging) {
        console.log(`Cannot unregister node ${nodeId}: Not a master node`);
      }
      return false;
    }
    
    // Check if node exists
    if (!this.nodes.has(nodeId)) {
      if (this.options.enableLogging) {
        console.log(`Node ${nodeId} not found`);
      }
      return false;
    }
    
    // Get node information
    const nodeInfo = this.nodes.get(nodeId);
    
    // Unregister node
    this.nodes.delete(nodeId);
    
    if (this.options.enableLogging) {
      console.log(`Node ${nodeId} unregistered`);
    }
    
    // Emit node-unregistered event
    this.emit('node-unregistered', { nodeId, nodeInfo });
    
    return true;
  }

  /**
   * Get all registered nodes
   * @returns {Array} - Array of node information
   */
  getNodes() {
    return Array.from(this.nodes.values());
  }

  /**
   * Get node information
   * @param {string} nodeId - Node ID
   * @returns {Object} - Node information
   */
  getNodeInfo(nodeId) {
    return this.nodes.get(nodeId);
  }

  /**
   * Update node information
   * @param {Object} nodeInfo - Node information
   * @returns {boolean} - True if node was updated, false otherwise
   */
  updateNodeInfo(nodeInfo) {
    // Update local node information
    if (nodeInfo.id === this.nodeInfo.id) {
      this.nodeInfo = {
        ...this.nodeInfo,
        ...nodeInfo,
        lastHeartbeat: Date.now()
      };
      
      // Update node in registry if master
      if (this.options.isMaster) {
        this.nodes.set(this.nodeInfo.id, { ...this.nodeInfo });
      }
      
      return true;
    }
    
    // Update other node if master
    if (this.options.isMaster && this.nodes.has(nodeInfo.id)) {
      const existingNode = this.nodes.get(nodeInfo.id);
      this.nodes.set(nodeInfo.id, {
        ...existingNode,
        ...nodeInfo,
        lastHeartbeat: Date.now()
      });
      
      return true;
    }
    
    return false;
  }

  /**
   * Submit a task for distributed processing
   * @param {Object} task - Task to process
   * @param {string} domain - Domain of the task
   * @returns {Promise<Object>} - Task result
   */
  async submitTask(task, domain = '') {
    // Generate task ID
    const taskId = uuidv4();
    
    // Create task object
    const taskObj = {
      id: taskId,
      task,
      domain,
      status: 'pending',
      submittedAt: Date.now(),
      submittedBy: this.nodeInfo.id
    };
    
    // Register task
    this.tasks.set(taskId, taskObj);
    
    if (this.options.enableLogging) {
      console.log(`Task ${taskId} submitted`);
    }
    
    // Emit task-submitted event
    this.emit('task-submitted', { taskId, task, domain });
    
    // Process task
    return this._processTask(taskObj);
  }

  /**
   * Process a task
   * @param {Object} taskObj - Task object
   * @returns {Promise<Object>} - Task result
   * @private
   */
  async _processTask(taskObj) {
    // Update task status
    taskObj.status = 'processing';
    taskObj.startedAt = Date.now();
    taskObj.processedBy = this.nodeInfo.id;
    
    try {
      // Process task
      // In a real implementation, this would distribute the task to the appropriate node
      // For now, we'll just process it locally
      const result = await this._executeTask(taskObj);
      
      // Update task status
      taskObj.status = 'completed';
      taskObj.completedAt = Date.now();
      taskObj.result = result;
      
      // Store task result
      this.taskResults.set(taskObj.id, result);
      
      if (this.options.enableLogging) {
        console.log(`Task ${taskObj.id} completed`);
      }
      
      // Emit task-completed event
      this.emit('task-completed', { taskId: taskObj.id, result });
      
      return result;
    } catch (error) {
      // Update task status
      taskObj.status = 'failed';
      taskObj.failedAt = Date.now();
      taskObj.error = error.message;
      
      if (this.options.enableLogging) {
        console.error(`Task ${taskObj.id} failed:`, error);
      }
      
      // Emit task-failed event
      this.emit('task-failed', { taskId: taskObj.id, error });
      
      throw error;
    }
  }

  /**
   * Execute a task
   * @param {Object} taskObj - Task object
   * @returns {Promise<Object>} - Task result
   * @private
   */
  async _executeTask(taskObj) {
    // This is a placeholder for actual task execution
    // In a real implementation, this would be replaced with actual processing logic
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          processed: true,
          task: taskObj.task,
          domain: taskObj.domain,
          nodeId: this.nodeInfo.id,
          timestamp: new Date().toISOString()
        });
      }, 100);
    });
  }

  /**
   * Generate a unique node ID
   * @returns {string} - Node ID
   * @private
   */
  _generateNodeId() {
    const hostname = os.hostname();
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000000);
    const hash = crypto.createHash('md5').update(`${hostname}-${timestamp}-${random}`).digest('hex');
    return hash.substring(0, 8);
  }

  /**
   * Get local IP address
   * @returns {string} - Local IP address
   * @private
   */
  _getLocalAddress() {
    const interfaces = os.networkInterfaces();
    for (const name of Object.keys(interfaces)) {
      for (const iface of interfaces[name]) {
        if (iface.family === 'IPv4' && !iface.internal) {
          return iface.address;
        }
      }
    }
    return '127.0.0.1';
  }

  /**
   * Send heartbeat to master node
   * @private
   */
  _sendHeartbeat() {
    // Update node information
    this.nodeInfo.uptime = process.uptime();
    this.nodeInfo.freeMemory = os.freemem();
    this.nodeInfo.lastHeartbeat = Date.now();
    
    // If master, update node in registry
    if (this.options.isMaster) {
      this.nodes.set(this.nodeInfo.id, { ...this.nodeInfo });
      return;
    }
    
    // Send heartbeat to master
    // In a real implementation, this would send a heartbeat message to the master node
    // For now, we'll just emit an event
    this.emit('heartbeat', { nodeId: this.nodeInfo.id, nodeInfo: this.nodeInfo });
  }

  /**
   * Discover nodes in the cluster
   * @private
   */
  _discoverNodes() {
    // In a real implementation, this would discover nodes in the cluster
    // For now, we'll just emit an event
    this.emit('discovery', { nodeId: this.nodeInfo.id });
  }

  /**
   * Clean up inactive nodes
   * @private
   */
  _cleanupInactiveNodes() {
    if (!this.options.isMaster) {
      return;
    }
    
    const now = Date.now();
    const timeout = this.options.nodeTimeout;
    
    // Check each node
    for (const [nodeId, nodeInfo] of this.nodes.entries()) {
      // Skip self
      if (nodeId === this.nodeInfo.id) {
        continue;
      }
      
      // Check if node is inactive
      if (now - nodeInfo.lastHeartbeat > timeout) {
        // Unregister node
        this.unregisterNode(nodeId);
        
        if (this.options.enableLogging) {
          console.log(`Node ${nodeId} timed out`);
        }
      }
    }
  }

  /**
   * Notify other nodes of shutdown
   * @private
   */
  _notifyShutdown() {
    // In a real implementation, this would notify other nodes of shutdown
    // For now, we'll just emit an event
    this.emit('shutdown', { nodeId: this.nodeInfo.id });
  }

  /**
   * Dispose resources
   */
  dispose() {
    this.stop();
    
    // Clear registries
    this.nodes.clear();
    this.tasks.clear();
    this.taskResults.clear();
    
    if (this.options.enableLogging) {
      console.log('ClusterManager disposed');
    }
  }
}

/**
 * Create a cluster manager with recommended settings
 * @param {Object} options - Configuration options
 * @returns {ClusterManager} - Configured cluster manager
 */
function createClusterManager(options = {}) {
  return new ClusterManager({
    enableLogging: true,
    isMaster: true,
    discoveryEnabled: true,
    ...options
  });
}

module.exports = {
  ClusterManager,
  createClusterManager
};
