import React from 'react';
import Head from 'next/head';
import FloatingNovaConcierge from './FloatingNovaConcierge';

const Layout = ({
  children,
  title = 'NovaFuse API Superstore',
  description = 'NovaFuse API Superstore: A comprehensive marketplace for GRC APIs',
  keywords = 'NovaFuse, API, GRC, Governance, Risk, Compliance',
  canonical,
  ogImage
}) => {
  return (
    <>
      <Head>
        <title>{title}</title>
        <meta name="description" content={description} />
        <meta name="keywords" content={keywords} />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta name="author" content="NovaFuse" />
        <meta name="robots" content="index, follow" />

        {/* Open Graph / Facebook */}
        <meta property="og:type" content="website" />
        <meta property="og:url" content={canonical || 'https://novafuse.io'} />
        <meta property="og:title" content={title} />
        <meta property="og:description" content={description} />
        <meta property="og:image" content={ogImage || '/images/novafuse-og-image.png'} />

        {/* Twitter */}
        <meta property="twitter:card" content="summary_large_image" />
        <meta property="twitter:url" content={canonical || 'https://novafuse.io'} />
        <meta property="twitter:title" content={title} />
        <meta property="twitter:description" content={description} />
        <meta property="twitter:image" content={ogImage || '/images/novafuse-og-image.png'} />

        {/* Canonical URL */}
        {canonical && <link rel="canonical" href={canonical} />}

        {/* Favicon */}
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
        <link rel="manifest" href="/site.webmanifest" />
      </Head>

      <div className="min-h-screen flex flex-col bg-primary text-primary">
        {/* Navigation is now handled separately */}

        <main className="flex-grow container mx-auto px-4 py-8">
          {children}
        </main>

        {/* Footer is now handled in _app.js */}
      </div>

      {/* Floating NovaConcierge Widget */}
      <FloatingNovaConcierge />
    </>
  );
};

export default Layout;
