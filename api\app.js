/**
 * NovaFuse API
 *
 * Main application file.
 */

const express = require('express');
const cors = require('cors');
const morgan = require('morgan');
const helmet = require('helmet');
const compression = require('compression');
const path = require('path');
const config = require('./config');
const errorHandler = require('./utils/errorHandler');
const logger = require('./utils/logger');

// Import routes
const userRoutes = require('./routes/userRoutes');
const authRoutes = require('./routes/authRoutes');
const schemaRoutes = require('./routes/schemaRoutes');
const largeFileRoutes = require('./routes/largeFileRoutes');

// Import components
const novaAssure = require('./novaassure');

// Import utilities
const { scheduleCleanup } = require('./utils/fileCleanup');

// Create Express app
const app = express();

// Middleware
app.use(helmet());
app.use(cors());
app.use(morgan('dev'));
app.use(express.json({ limit: '50mb' })); // Increased JSON size limit
app.use(express.urlencoded({ extended: true, limit: '50mb' })); // Increased URL-encoded size limit
app.use(compression());

// Static files
app.use(express.static(path.join(__dirname, '../public')));

// API routes
app.use('/api/v1/users', userRoutes);
app.use('/api/v1/auth', authRoutes);
app.use('/api/v1/schemas', schemaRoutes);
app.use('/api/v1/large-files', largeFileRoutes);

// Initialize components
novaAssure.initialize(app);

// Schedule temporary file cleanup (runs every hour)
if (process.env.NODE_ENV !== 'test') {
  scheduleCleanup(3600000); // 1 hour
}

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    version: '1.0.0',
    timestamp: new Date().toISOString()
  });
});

// Error handling middleware
app.use(errorHandler.errorHandler);

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    success: false,
    error: 'Not found'
  });
});

// Start server
if (process.env.NODE_ENV !== 'test') {
  const PORT = config.server.port || 3000;
  app.listen(PORT, () => {
    logger.info(`Server running on port ${PORT}`);
  });
}

module.exports = app;
