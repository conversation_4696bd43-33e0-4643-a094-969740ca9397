#!/usr/bin/env python3
"""
NovaLift-NovaCortex FastAPI Translation Adapter
Provides lightweight event translation between NovaLift and NovaCortex systems
"""

import os
import json
import logging
import asyncio
from datetime import datetime
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict

import uvicorn
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import httpx
from prometheus_client import Counter, Gauge, Histogram, generate_latest
from prometheus_client.openmetrics.exposition import CONTENT_TYPE_LATEST
import pydantic
from pydantic import BaseModel

# Configure logging
logging.basicConfig(
    level=getattr(logging, os.getenv('LOG_LEVEL', 'INFO')),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Configuration
@dataclass
class Config:
    novacortex_url: str = os.getenv('NOVACORTEX_URL', 'http://localhost:3010')
    novalift_url: str = os.getenv('NOVALIFT_URL', 'http://localhost:3014')  
    fusion_url: str = os.getenv('FUSION_URL', 'http://localhost:3015')
    adapter_port: int = int(os.getenv('ADAPTER_PORT', '8080'))
    metrics_enabled: bool = os.getenv('METRICS_ENABLED', 'true').lower() == 'true'
    timeout: int = int(os.getenv('REQUEST_TIMEOUT', '30'))
    max_retries: int = int(os.getenv('MAX_RETRIES', '3'))

config = Config()

# Prometheus metrics
if config.metrics_enabled:
    translation_counter = Counter(
        'adapter_translations_total',
        'Total number of event translations',
        ['source', 'target', 'event_type', 'status']
    )
    
    translation_duration = Histogram(
        'adapter_translation_duration_seconds',
        'Time spent translating events',
        ['source', 'target']
    )
    
    active_connections = Gauge(
        'adapter_active_connections',
        'Number of active connections'
    )
    
    handshake_success = Counter(
        'adapter_handshakes_total',
        'Total successful handshakes',
        ['system']
    )

# Pydantic models for validation
class NovaLiftEvent(BaseModel):
    source: str = "novalift"
    event_type: str
    timestamp: Optional[str] = None
    data: Dict[str, Any]

class NovaCortexEvent(BaseModel):
    source: str = "novacortex"
    event_type: str
    timestamp: Optional[str] = None
    data: Dict[str, Any]

class TranslationRequest(BaseModel):
    source_system: str
    target_system: str
    event: Dict[str, Any]

class HealthStatus(BaseModel):
    status: str
    timestamp: str
    version: str = "1.0.0"
    systems: Dict[str, str]

# FastAPI app setup
app = FastAPI(
    title="NovaLift-NovaCortex Translation Adapter",
    description="Lightweight adapter for bidirectional event translation",
    version="1.0.0"
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# HTTP client with connection pooling
http_client = httpx.AsyncClient(
    timeout=httpx.Timeout(config.timeout),
    limits=httpx.Limits(max_keepalive_connections=20, max_connections=100)
)

class EventTranslator:
    """Handles bidirectional event translation between NovaLift and NovaCortex"""
    
    @staticmethod
    def novalift_to_novacortex(event: NovaLiftEvent) -> Dict[str, Any]:
        """Translate NovaLift event to NovaCortex format"""
        
        data = event.data
        
        if event.event_type == "optimization_complete":
            # Convert Ψ-Score to consciousness level (Ψ-Score range 0-3+ → 0-1)
            psi_score = data.get("psi_score", 1.0)
            consciousness_level = min(1.0, psi_score / 3.0)
            
            return {
                "coherence_context": {
                    "performance_factor": data.get("performance_multiplier", 1.0),
                    "consciousness_level": consciousness_level,
                    "ethical_compliance_required": True,
                    "psi_score_source": psi_score,
                    "coherence_status": data.get("coherence_status", "COHERENT")
                },
                "event_metadata": {
                    "source_event": event.event_type,
                    "translation_timestamp": datetime.utcnow().isoformat(),
                    "translator": "fastapi_adapter"
                }
            }
            
        elif event.event_type == "performance_metrics":
            return {
                "system_context": {
                    "performance_multiplier": data.get("performance_multiplier", 1.0),
                    "system_health": data.get("system_health", "unknown"),
                    "active_optimizations": data.get("active_optimizations", 0),
                    "infrastructure_state": data.get("coherence_status", "UNKNOWN")
                }
            }
            
        elif event.event_type == "ethical_query":
            return {
                "castl_evaluation_request": {
                    "scenario": data.get("scenario", {}),
                    "context": data.get("context", {}),
                    "urgency": data.get("urgency", "normal"),
                    "infrastructure_impact": True
                }
            }
        
        # Default translation for unknown events
        return {
            "generic_context": data,
            "event_metadata": {
                "source_event": event.event_type,
                "translation_timestamp": datetime.utcnow().isoformat(),
                "translator": "fastapi_adapter",
                "note": "Generic translation applied"
            }
        }
    
    @staticmethod 
    def novacortex_to_novalift(event: NovaCortexEvent) -> Dict[str, Any]:
        """Translate NovaCortex event to NovaLift format"""
        
        data = event.data
        
        if event.event_type == "coherence_update":
            return {
                "optimization_constraints": {
                    "coherence_constraint": data.get("coherence_level", 0.95),
                    "pi_sync": data.get("pi_rhythm_synchronized", False),
                    "consciousness_guided": data.get("coherence_level", 0) >= 0.95,
                    "ethical_clearance": data.get("castl_clearance", "pending")
                },
                "consciousness_state": {
                    "coherence": data.get("coherence_level", 0.95),
                    "pi_rhythm_status": "synchronized" if data.get("pi_rhythm_synchronized") else "desynchronized",
                    "coherence_status": data.get("status", "stable")
                }
            }
            
        elif event.event_type == "castl_decision":
            return {
                "ethical_decision": {
                    "approved": data.get("decision") != "deny",
                    "decision": data.get("decision", "unknown"),
                    "reasoning": data.get("reasoning", "CASTL evaluation completed"),
                    "principles_applied": data.get("principles_applied", []),
                    "confidence": data.get("confidence", 0.85)
                }
            }
            
        elif event.event_type == "pi_rhythm_sync":
            return {
                "synchronization_update": {
                    "pi_deviation": data.get("deviation", 0.1),
                    "frequency": data.get("frequency", 3.14159),
                    "synchronized": data.get("status") == "synchronized",
                    "phase": data.get("phase", 0.0)
                }
            }
        
        # Default translation
        return {
            "generic_update": data,
            "event_metadata": {
                "source_event": event.event_type,
                "translation_timestamp": datetime.utcnow().isoformat(),
                "translator": "fastapi_adapter",
                "note": "Generic translation applied"
            }
        }

translator = EventTranslator()

# Health check endpoint
@app.get("/health", response_model=HealthStatus)
async def health_check():
    """Check adapter and connected system health"""
    
    systems = {}
    
    # Check NovaCortex
    try:
        response = await http_client.get(f"{config.novacortex_url}/health", timeout=5)
        systems["novacortex"] = "operational" if response.status_code == 200 else "degraded"
    except Exception as e:
        systems["novacortex"] = "unavailable"
        logger.warning(f"NovaCortex health check failed: {e}")
    
    # Check Fusion server
    try:
        response = await http_client.get(f"{config.fusion_url}/health", timeout=5)
        systems["fusion"] = "operational" if response.status_code == 200 else "degraded"
    except Exception as e:
        systems["fusion"] = "unavailable"
        logger.warning(f"Fusion server health check failed: {e}")
    
    return HealthStatus(
        status="healthy" if all(s != "unavailable" for s in systems.values()) else "degraded",
        timestamp=datetime.utcnow().isoformat(),
        systems=systems
    )

# Translation endpoints
@app.post("/translate/novalift-to-novacortex")
async def translate_novalift_to_novacortex(event: NovaLiftEvent):
    """Translate NovaLift event to NovaCortex format"""
    
    if config.metrics_enabled:
        with translation_duration.labels(source="novalift", target="novacortex").time():
            try:
                translated = translator.novalift_to_novacortex(event)
                translation_counter.labels(
                    source="novalift", 
                    target="novacortex", 
                    event_type=event.event_type, 
                    status="success"
                ).inc()
                
                return {
                    "status": "translated",
                    "source_event": event.dict(),
                    "translated_event": translated,
                    "timestamp": datetime.utcnow().isoformat()
                }
            except Exception as e:
                translation_counter.labels(
                    source="novalift", 
                    target="novacortex", 
                    event_type=event.event_type, 
                    status="error"
                ).inc()
                raise HTTPException(status_code=500, detail=f"Translation failed: {str(e)}")
    else:
        try:
            translated = translator.novalift_to_novacortex(event)
            return {
                "status": "translated",
                "source_event": event.dict(),
                "translated_event": translated,
                "timestamp": datetime.utcnow().isoformat()
            }
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Translation failed: {str(e)}")

@app.post("/translate/novacortex-to-novalift")
async def translate_novacortex_to_novalift(event: NovaCortexEvent):
    """Translate NovaCortex event to NovaLift format"""
    
    if config.metrics_enabled:
        with translation_duration.labels(source="novacortex", target="novalift").time():
            try:
                translated = translator.novacortex_to_novalift(event)
                translation_counter.labels(
                    source="novacortex", 
                    target="novalift", 
                    event_type=event.event_type, 
                    status="success"
                ).inc()
                
                return {
                    "status": "translated",
                    "source_event": event.dict(),
                    "translated_event": translated,
                    "timestamp": datetime.utcnow().isoformat()
                }
            except Exception as e:
                translation_counter.labels(
                    source="novacortex", 
                    target="novalift", 
                    event_type=event.event_type, 
                    status="error"
                ).inc()
                raise HTTPException(status_code=500, detail=f"Translation failed: {str(e)}")
    else:
        try:
            translated = translator.novacortex_to_novalift(event)
            return {
                "status": "translated", 
                "source_event": event.dict(),
                "translated_event": translated,
                "timestamp": datetime.utcnow().isoformat()
            }
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Translation failed: {str(e)}")

# Bidirectional handshake endpoint
@app.post("/handshake/{system}")
async def perform_handshake(system: str, background_tasks: BackgroundTasks):
    """Perform bidirectional handshake with specified system"""
    
    if system not in ["novacortex", "novalift", "fusion"]:
        raise HTTPException(status_code=400, detail="Invalid system. Use 'novacortex', 'novalift', or 'fusion'")
    
    try:
        url_map = {
            "novacortex": config.novacortex_url,
            "novalift": config.novalift_url,
            "fusion": config.fusion_url
        }
        
        base_url = url_map[system]
        
        # Perform handshake
        handshake_data = {
            "adapter_id": "fastapi_adapter",
            "timestamp": datetime.utcnow().isoformat(),
            "capabilities": [
                "event_translation",
                "bidirectional_messaging",
                "prometheus_metrics"
            ],
            "version": "1.0.0"
        }
        
        # Try different handshake endpoints based on system
        endpoints_to_try = [
            f"{base_url}/health",
            f"{base_url}/api/{system}/health" if system != "fusion" else f"{base_url}/fusion/health",
        ]
        
        response = None
        for endpoint in endpoints_to_try:
            try:
                response = await http_client.get(endpoint, timeout=10)
                if response.status_code == 200:
                    break
            except Exception:
                continue
        
        if response and response.status_code == 200:
            if config.metrics_enabled:
                handshake_success.labels(system=system).inc()
            
            return {
                "status": "handshake_successful",
                "system": system,
                "endpoint": endpoint,
                "response": response.json(),
                "adapter_data": handshake_data,
                "timestamp": datetime.utcnow().isoformat()
            }
        else:
            raise HTTPException(
                status_code=503, 
                detail=f"Handshake failed: {system} not responding"
            )
            
    except Exception as e:
        raise HTTPException(
            status_code=500, 
            detail=f"Handshake error with {system}: {str(e)}"
        )

# Batch translation endpoint
@app.post("/translate/batch")
async def batch_translate(requests: List[TranslationRequest]):
    """Process multiple translation requests in batch"""
    
    results = []
    
    for req in requests:
        try:
            if req.source_system == "novalift" and req.target_system == "novacortex":
                event = NovaLiftEvent(**req.event)
                translated = translator.novalift_to_novacortex(event)
                
            elif req.source_system == "novacortex" and req.target_system == "novalift":
                event = NovaCortexEvent(**req.event)
                translated = translator.novacortex_to_novalift(event)
                
            else:
                raise ValueError(f"Invalid translation path: {req.source_system} → {req.target_system}")
            
            results.append({
                "status": "success",
                "original": req.event,
                "translated": translated
            })
            
            if config.metrics_enabled:
                translation_counter.labels(
                    source=req.source_system,
                    target=req.target_system,
                    event_type=req.event.get("event_type", "unknown"),
                    status="success"
                ).inc()
                
        except Exception as e:
            results.append({
                "status": "error",
                "original": req.event,
                "error": str(e)
            })
            
            if config.metrics_enabled:
                translation_counter.labels(
                    source=req.source_system,
                    target=req.target_system,
                    event_type=req.event.get("event_type", "unknown"),
                    status="error"
                ).inc()
    
    return {
        "batch_results": results,
        "processed": len(requests),
        "successful": len([r for r in results if r["status"] == "success"]),
        "failed": len([r for r in results if r["status"] == "error"]),
        "timestamp": datetime.utcnow().isoformat()
    }

# Metrics endpoint
@app.get("/metrics")
async def get_metrics():
    """Expose Prometheus metrics"""
    
    if config.metrics_enabled:
        return Response(
            content=generate_latest(),
            media_type=CONTENT_TYPE_LATEST
        )
    else:
        return {"message": "Metrics disabled"}

# Message logging endpoint
@app.get("/logs/messages")
async def get_message_logs(limit: int = 100):
    """Get recent message translation logs"""
    
    # This would integrate with your logging system
    # For now, return a placeholder
    return {
        "message": "Message logging endpoint - integrate with your log aggregation system",
        "limit": limit,
        "timestamp": datetime.utcnow().isoformat()
    }

# Cleanup on shutdown
@app.on_event("shutdown")
async def shutdown():
    await http_client.aclose()
    logger.info("FastAPI adapter shutdown complete")

if __name__ == "__main__":
    logger.info(f"Starting NovaLift-NovaCortex FastAPI Adapter on port {config.adapter_port}")
    logger.info(f"NovaCortex URL: {config.novacortex_url}")
    logger.info(f"Fusion URL: {config.fusion_url}")
    logger.info(f"Metrics enabled: {config.metrics_enabled}")
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=config.adapter_port,
        log_level=os.getenv('LOG_LEVEL', 'info').lower()
    )
