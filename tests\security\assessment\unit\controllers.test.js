const controllers = require('../../../../apis/security/assessment/controllers');
const models = require('../../../../apis/security/assessment/models');

// Mock the models
jest.mock('../../../../apis/security/assessment/models', () => ({
  securityAssessments: [
    {
      id: 'sa-12345678',
      name: 'Annual Security Assessment 2023',
      description: 'Comprehensive security assessment of all systems and applications',
      type: 'internal',
      status: 'completed',
      scope: {
        systems: ['CRM', 'ERP', 'Website'],
        applications: ['Mobile App', 'Admin Portal'],
        networks: ['Corporate Network', 'Guest WiFi']
      },
      methodology: 'NIST CSF',
      startDate: '2023-01-15',
      endDate: '2023-02-28',
      findings: [
        {
          id: 'finding-12345',
          title: 'Weak Password Policy',
          description: 'Password policy does not enforce sufficient complexity',
          severity: 'high',
          category: 'authentication',
          status: 'remediated',
          affectedSystems: ['CRM', 'ERP'],
          remediationPlan: 'Implement stronger password policy with minimum 12 characters',
          remediationDate: '2023-03-15',
          assignedTo: 'Security Team'
        },
        {
          id: 'finding-67890',
          title: 'Outdated SSL Certificates',
          description: 'Several SSL certificates are using outdated encryption standards',
          severity: 'medium',
          category: 'encryption',
          status: 'in-progress',
          affectedSystems: ['Website'],
          remediationPlan: 'Update all certificates to use TLS 1.3',
          assignedTo: 'Infrastructure Team'
        }
      ],
      assessors: ['John Doe', 'Jane Smith'],
      stakeholders: ['IT Director', 'CISO'],
      attachments: [
        {
          id: 'att-12345',
          name: 'Final Report',
          type: 'pdf',
          url: 'https://example.com/reports/security-assessment-2023.pdf',
          uploadedBy: 'John Doe',
          uploadedAt: '2023-03-01T00:00:00Z'
        }
      ],
      createdAt: '2023-01-10T00:00:00Z',
      updatedAt: '2023-03-01T00:00:00Z'
    },
    {
      id: 'sa-87654321',
      name: 'Vendor Security Assessment - CloudProvider',
      description: 'Security assessment of CloudProvider services',
      type: 'vendor',
      status: 'in-progress',
      scope: {
        systems: ['Cloud Storage', 'Cloud Compute'],
        applications: ['Management Console'],
        networks: ['Cloud Network']
      },
      methodology: 'ISO 27001',
      startDate: '2023-04-01',
      findings: [],
      assessors: ['Jane Smith'],
      stakeholders: ['Procurement Manager', 'CISO'],
      attachments: [],
      createdAt: '2023-03-15T00:00:00Z',
      updatedAt: '2023-04-01T00:00:00Z'
    }
  ],
  assessmentTemplates: [
    {
      id: 'template-12345',
      name: 'Internal Security Assessment Template',
      description: 'Template for internal security assessments',
      type: 'internal',
      methodology: 'NIST CSF',
      sections: [
        {
          title: 'Identify',
          description: 'Asset management, business environment, governance, risk assessment, risk management strategy',
          questions: [
            {
              id: 'q-12345',
              text: 'Are all assets inventoried and tracked?',
              type: 'yes-no',
              required: true
            },
            {
              id: 'q-23456',
              text: 'How is the asset inventory maintained?',
              type: 'text',
              required: false
            }
          ]
        },
        {
          title: 'Protect',
          description: 'Access control, awareness and training, data security, protective technology',
          questions: [
            {
              id: 'q-34567',
              text: 'Is multi-factor authentication implemented for all remote access?',
              type: 'yes-no',
              required: true
            }
          ]
        }
      ],
      createdAt: '2022-12-01T00:00:00Z',
      updatedAt: '2022-12-01T00:00:00Z'
    }
  ]
}));

// Mock Express request and response
const mockRequest = (params = {}, query = {}, body = {}) => ({
  params,
  query,
  body
});

const mockResponse = () => {
  const res = {};
  res.status = jest.fn().mockReturnValue(res);
  res.json = jest.fn().mockReturnValue(res);
  return res;
};

describe('Security Assessment Controllers', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('getAssessments', () => {
    it('should return all assessments with default pagination', () => {
      const req = mockRequest({}, {});
      const res = mockResponse();

      controllers.getAssessments(req, res);

      expect(res.json).toHaveBeenCalledWith({
        data: models.securityAssessments,
        pagination: {
          total: 2,
          page: 1,
          limit: 10,
          pages: 1
        }
      });
    });

    it('should filter assessments by type', () => {
      const req = mockRequest({}, { type: 'internal' });
      const res = mockResponse();

      controllers.getAssessments(req, res);

      expect(res.json).toHaveBeenCalledWith({
        data: [models.securityAssessments[0]],
        pagination: {
          total: 1,
          page: 1,
          limit: 10,
          pages: 1
        }
      });
    });

    it('should filter assessments by status', () => {
      const req = mockRequest({}, { status: 'in-progress' });
      const res = mockResponse();

      controllers.getAssessments(req, res);

      expect(res.json).toHaveBeenCalledWith({
        data: [models.securityAssessments[1]],
        pagination: {
          total: 1,
          page: 1,
          limit: 10,
          pages: 1
        }
      });
    });

    it('should handle errors', () => {
      const req = mockRequest();
      const res = mockResponse();

      // Force an error
      jest.spyOn(console, 'error').mockImplementation(() => {});
      jest.spyOn(Array.prototype, 'filter').mockImplementation(() => {
        throw new Error('Test error');
      });

      controllers.getAssessments(req, res);

      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Internal Server Error',
        message: 'Test error'
      });

      // Restore console.error
      console.error.mockRestore();
    });
  });

  describe('getAssessmentById', () => {
    it('should return a specific assessment by ID', () => {
      const req = mockRequest({ id: 'sa-12345678' });
      const res = mockResponse();

      controllers.getAssessmentById(req, res);

      expect(res.json).toHaveBeenCalledWith({
        data: models.securityAssessments[0]
      });
    });

    it('should return 404 if assessment not found', () => {
      const req = mockRequest({ id: 'non-existent-id' });
      const res = mockResponse();

      controllers.getAssessmentById(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'Security assessment with ID non-existent-id not found'
      });
    });
  });

  describe('createAssessment', () => {
    it('should create a new assessment', () => {
      const newAssessment = {
        name: 'Application Security Assessment',
        description: 'Security assessment of the new mobile application',
        type: 'application',
        status: 'planned',
        scope: {
          applications: ['Mobile App v2.0']
        },
        methodology: 'OWASP MASVS',
        startDate: '2023-06-01',
        assessors: ['Jane Smith'],
        stakeholders: ['Mobile App Team Lead', 'CISO']
      };

      const req = mockRequest({}, {}, newAssessment);
      const res = mockResponse();

      // Mock Date and UUID
      const originalDateNow = Date.now;
      Date.now = jest.fn(() => 1577836800000); // 2020-01-01T00:00:00Z
      global.Date = jest.fn(() => ({
        toISOString: () => '2020-01-01T00:00:00Z'
      }));
      jest.mock('uuid', () => ({
        v4: jest.fn(() => '00000000-0000-0000-0000-000000000000')
      }));

      controllers.createAssessment(req, res);

      expect(res.status).toHaveBeenCalledWith(201);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({
          name: 'Application Security Assessment',
          type: 'application'
        }),
        message: 'Security assessment created successfully'
      }));

      // Restore Date
      Date.now = originalDateNow;
    });
  });

  describe('updateAssessment', () => {
    it('should update an existing assessment', () => {
      const updatedAssessment = {
        name: 'Updated Assessment Name',
        status: 'completed',
        endDate: '2023-04-15'
      };

      const req = mockRequest({ id: 'sa-87654321' }, {}, updatedAssessment);
      const res = mockResponse();

      controllers.updateAssessment(req, res);

      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({
          id: 'sa-87654321',
          name: 'Updated Assessment Name',
          status: 'completed',
          endDate: '2023-04-15'
        }),
        message: 'Security assessment updated successfully'
      }));
    });

    it('should return 404 if assessment not found', () => {
      const req = mockRequest({ id: 'non-existent-id' }, {}, { name: 'Updated Name' });
      const res = mockResponse();

      controllers.updateAssessment(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'Security assessment with ID non-existent-id not found'
      });
    });
  });

  describe('deleteAssessment', () => {
    it('should delete an existing assessment', () => {
      const req = mockRequest({ id: 'sa-12345678' });
      const res = mockResponse();

      controllers.deleteAssessment(req, res);

      expect(res.json).toHaveBeenCalledWith({
        message: 'Security assessment deleted successfully'
      });
    });

    it('should return 404 if assessment not found', () => {
      const req = mockRequest({ id: 'non-existent-id' });
      const res = mockResponse();

      controllers.deleteAssessment(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'Security assessment with ID non-existent-id not found'
      });
    });
  });

  describe('getAssessmentFindings', () => {
    it('should return findings for a specific assessment', () => {
      const req = mockRequest({ id: 'sa-12345678' });
      const res = mockResponse();

      controllers.getAssessmentFindings(req, res);

      expect(res.json).toHaveBeenCalledWith({
        data: models.securityAssessments[0].findings
      });
    });

    it('should return 404 if assessment not found', () => {
      const req = mockRequest({ id: 'non-existent-id' });
      const res = mockResponse();

      controllers.getAssessmentFindings(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'Security assessment with ID non-existent-id not found'
      });
    });
  });

  describe('getAssessmentFindingById', () => {
    it('should return a specific finding by ID', () => {
      const req = mockRequest({ id: 'sa-12345678', findingId: 'finding-12345' });
      const res = mockResponse();

      controllers.getAssessmentFindingById(req, res);

      expect(res.json).toHaveBeenCalledWith({
        data: models.securityAssessments[0].findings[0]
      });
    });

    it('should return 404 if finding not found', () => {
      const req = mockRequest({ id: 'sa-12345678', findingId: 'non-existent-id' });
      const res = mockResponse();

      controllers.getAssessmentFindingById(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'Finding with ID non-existent-id not found'
      });
    });
  });

  describe('addAssessmentFinding', () => {
    it('should add a new finding to an assessment', () => {
      const newFinding = {
        title: 'Insecure API Endpoints',
        description: 'Several API endpoints lack proper authentication',
        severity: 'high',
        category: 'authentication',
        status: 'open',
        affectedSystems: ['Website'],
        remediationPlan: 'Implement OAuth 2.0 for all API endpoints',
        assignedTo: 'Development Team'
      };

      const req = mockRequest({ id: 'sa-87654321' }, {}, newFinding);
      const res = mockResponse();

      controllers.addAssessmentFinding(req, res);

      expect(res.status).toHaveBeenCalledWith(201);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({
          title: 'Insecure API Endpoints',
          severity: 'high'
        }),
        message: 'Finding added successfully'
      }));
    });

    it('should return 404 if assessment not found', () => {
      const req = mockRequest({ id: 'non-existent-id' }, {}, { title: 'New Finding' });
      const res = mockResponse();

      controllers.addAssessmentFinding(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'Security assessment with ID non-existent-id not found'
      });
    });
  });

  describe('updateAssessmentFinding', () => {
    it('should update an existing finding', () => {
      const updatedFinding = {
        title: 'Updated Finding Title',
        status: 'remediated',
        remediationDate: '2023-04-15'
      };

      const req = mockRequest({ id: 'sa-12345678', findingId: 'finding-67890' }, {}, updatedFinding);
      const res = mockResponse();

      controllers.updateAssessmentFinding(req, res);

      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({
          id: 'finding-67890',
          title: 'Updated Finding Title',
          status: 'remediated',
          remediationDate: '2023-04-15'
        }),
        message: 'Finding updated successfully'
      }));
    });

    it('should return 404 if finding not found', () => {
      const req = mockRequest({ id: 'sa-12345678', findingId: 'non-existent-id' }, {}, { title: 'Updated Title' });
      const res = mockResponse();

      controllers.updateAssessmentFinding(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'Finding with ID non-existent-id not found'
      });
    });
  });

  describe('removeAssessmentFinding', () => {
    it('should remove a finding from an assessment', () => {
      const req = mockRequest({ id: 'sa-12345678', findingId: 'finding-12345' });
      const res = mockResponse();

      controllers.removeAssessmentFinding(req, res);

      expect(res.json).toHaveBeenCalledWith({
        message: 'Finding removed successfully'
      });
    });

    it('should return 404 if finding not found', () => {
      const req = mockRequest({ id: 'sa-12345678', findingId: 'non-existent-id' });
      const res = mockResponse();

      controllers.removeAssessmentFinding(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'Finding with ID non-existent-id not found'
      });
    });
  });

  describe('getAssessmentAttachments', () => {
    it('should return attachments for a specific assessment', () => {
      const req = mockRequest({ id: 'sa-12345678' });
      const res = mockResponse();

      controllers.getAssessmentAttachments(req, res);

      expect(res.json).toHaveBeenCalledWith({
        data: models.securityAssessments[0].attachments
      });
    });

    it('should return 404 if assessment not found', () => {
      const req = mockRequest({ id: 'non-existent-id' });
      const res = mockResponse();

      controllers.getAssessmentAttachments(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'Security assessment with ID non-existent-id not found'
      });
    });
  });

  describe('addAssessmentAttachment', () => {
    it('should add a new attachment to an assessment', () => {
      const newAttachment = {
        name: 'Vulnerability Scan Results',
        type: 'pdf',
        url: 'https://example.com/reports/vulnerability-scan.pdf'
      };

      const req = mockRequest({ id: 'sa-87654321' }, {}, newAttachment);
      const res = mockResponse();

      controllers.addAssessmentAttachment(req, res);

      expect(res.status).toHaveBeenCalledWith(201);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({
          name: 'Vulnerability Scan Results',
          type: 'pdf'
        }),
        message: 'Attachment added successfully'
      }));
    });

    it('should return 404 if assessment not found', () => {
      const req = mockRequest({ id: 'non-existent-id' }, {}, { name: 'New Attachment' });
      const res = mockResponse();

      controllers.addAssessmentAttachment(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'Security assessment with ID non-existent-id not found'
      });
    });
  });

  describe('removeAssessmentAttachment', () => {
    it('should remove an attachment from an assessment', () => {
      const req = mockRequest({ id: 'sa-12345678', attachmentId: 'att-12345' });
      const res = mockResponse();

      controllers.removeAssessmentAttachment(req, res);

      expect(res.json).toHaveBeenCalledWith({
        message: 'Attachment removed successfully'
      });
    });

    it('should return 404 if attachment not found', () => {
      const req = mockRequest({ id: 'sa-12345678', attachmentId: 'non-existent-id' });
      const res = mockResponse();

      controllers.removeAssessmentAttachment(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'Attachment with ID non-existent-id not found'
      });
    });
  });

  describe('getAssessmentTemplates', () => {
    it('should return all assessment templates', () => {
      const req = mockRequest();
      const res = mockResponse();

      controllers.getAssessmentTemplates(req, res);

      expect(res.json).toHaveBeenCalledWith({
        data: models.assessmentTemplates
      });
    });
  });

  describe('getAssessmentTemplateById', () => {
    it('should return a specific assessment template by ID', () => {
      const req = mockRequest({ id: 'template-12345' });
      const res = mockResponse();

      controllers.getAssessmentTemplateById(req, res);

      expect(res.json).toHaveBeenCalledWith({
        data: models.assessmentTemplates[0]
      });
    });

    it('should return 404 if template not found', () => {
      const req = mockRequest({ id: 'non-existent-id' });
      const res = mockResponse();

      controllers.getAssessmentTemplateById(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'Assessment template with ID non-existent-id not found'
      });
    });
  });

  describe('createAssessmentTemplate', () => {
    it('should create a new assessment template', () => {
      const newTemplate = {
        name: 'Vendor Security Assessment Template',
        description: 'Template for vendor security assessments',
        type: 'vendor',
        methodology: 'ISO 27001',
        sections: [
          {
            title: 'Vendor Information',
            description: 'Basic information about the vendor',
            questions: [
              {
                text: 'Vendor name and contact information',
                type: 'text',
                required: true
              },
              {
                text: 'Services provided',
                type: 'text',
                required: true
              }
            ]
          },
          {
            title: 'Security Controls',
            description: 'Assessment of vendor security controls',
            questions: [
              {
                text: 'Does the vendor have a documented security policy?',
                type: 'yes-no',
                required: true
              }
            ]
          }
        ]
      };

      const req = mockRequest({}, {}, newTemplate);
      const res = mockResponse();

      controllers.createAssessmentTemplate(req, res);

      expect(res.status).toHaveBeenCalledWith(201);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({
          name: 'Vendor Security Assessment Template',
          type: 'vendor'
        }),
        message: 'Assessment template created successfully'
      }));
    });
  });

  describe('updateAssessmentTemplate', () => {
    it('should update an existing assessment template', () => {
      const updatedTemplate = {
        name: 'Updated Template Name',
        sections: [
          {
            title: 'New Section',
            description: 'New section description',
            questions: [
              {
                text: 'New question',
                type: 'text',
                required: false
              }
            ]
          }
        ]
      };

      const req = mockRequest({ id: 'template-12345' }, {}, updatedTemplate);
      const res = mockResponse();

      controllers.updateAssessmentTemplate(req, res);

      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({
          id: 'template-12345',
          name: 'Updated Template Name'
        }),
        message: 'Assessment template updated successfully'
      }));
    });

    it('should return 404 if template not found', () => {
      const req = mockRequest({ id: 'non-existent-id' }, {}, { name: 'Updated Name' });
      const res = mockResponse();

      controllers.updateAssessmentTemplate(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'Assessment template with ID non-existent-id not found'
      });
    });
  });

  describe('deleteAssessmentTemplate', () => {
    it('should delete an existing assessment template', () => {
      const req = mockRequest({ id: 'template-12345' });
      const res = mockResponse();

      controllers.deleteAssessmentTemplate(req, res);

      expect(res.json).toHaveBeenCalledWith({
        message: 'Assessment template deleted successfully'
      });
    });

    it('should return 404 if template not found', () => {
      const req = mockRequest({ id: 'non-existent-id' });
      const res = mockResponse();

      controllers.deleteAssessmentTemplate(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'Assessment template with ID non-existent-id not found'
      });
    });
  });
});
