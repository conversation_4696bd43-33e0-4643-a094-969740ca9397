# Script to fix markdown linting issues and rename the dictionary file
$sourceFile = "d:\\novafuse-api-superstore\\coherence-reality-systems\\imported-docs\\the-magnificent-seven\\documentation\\Comphyological_Dictionary_Standardized_Format_1_with_headers.md"
$destFile = "d:\\novafuse-api-superstore\\coherence-reality-systems\\imported-docs\\the-magnificent-seven\\documentation\\The Comphyological Dictionary 1st Edition.md"

# Read the file content
$content = Get-Content -Path $sourceFile -Raw

# Fix markdown linting issues:
# 1. Ensure blank lines around tables
$content = [regex]::Replace($content, '(?<!\n\n)(\|.*\|\s*\|\s*-+\s*\|\s*\n(?:\|.*\|\s*\n)*\|.*\|\s*\n)', "`n`n`$1`n`n")

# 2. Ensure blank lines around lists
$content = [regex]::Replace($content, '(?<!\n\n)((?:[-*+] .*\n)+)', "`n`n`$1`n`n")

# 3. Fix any triple newlines (replace with double newlines)
$content = $content -replace '(\r?\n){3,}', "`n`n"

# 4. Remove trailing whitespace
$content = $content -replace ' +\r?\n', "`n"

# 5. Ensure consistent line endings
$content = $content -replace '\r?\n', "`r`n"

# Write the fixed content to the new file
[System.IO.File]::WriteAllText($destFile, $content.Trim())

# Remove the intermediate file if it exists
if (Test-Path $sourceFile) {
    Remove-Item $sourceFile
}

Write-Host "Dictionary has been formatted and saved as: $destFile"
