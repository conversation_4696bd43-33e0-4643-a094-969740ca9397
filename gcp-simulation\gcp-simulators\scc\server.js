const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const winston = require('winston');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Create Express app
const app = express();

// Configure middleware
app.use(cors());
app.use(helmet());
app.use(express.json());
app.use(morgan('combined'));

// Configure logger
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  defaultMeta: { service: 'gcp-scc-simulator' },
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' })
  ]
});

// In-memory storage for findings
const findings = [
  {
    id: 'finding-1',
    name: 'organizations/123456789/sources/987654321/findings/finding-1',
    parent: 'organizations/123456789/sources/987654321',
    resourceName: 'projects/novafuse-testing/instances/vm-instance-1',
    category: 'VULNERABILITY',
    severity: 'HIGH',
    eventTime: '2023-08-01T10:00:00Z',
    createTime: '2023-08-01T10:05:00Z',
    state: 'ACTIVE',
    sourceProperties: {
      compliance_standards: ['GDPR', 'HIPAA'],
      description: 'Unpatched vulnerability detected in VM instance'
    }
  },
  {
    id: 'finding-2',
    name: 'organizations/123456789/sources/987654321/findings/finding-2',
    parent: 'organizations/123456789/sources/987654321',
    resourceName: 'projects/novafuse-testing/storage/bucket-1',
    category: 'MISCONFIGURATION',
    severity: 'MEDIUM',
    eventTime: '2023-08-02T14:30:00Z',
    createTime: '2023-08-02T14:35:00Z',
    state: 'ACTIVE',
    sourceProperties: {
      compliance_standards: ['GDPR', 'SOC2'],
      description: 'Public access enabled on storage bucket'
    }
  },
  {
    id: 'finding-3',
    name: 'organizations/123456789/sources/987654321/findings/finding-3',
    parent: 'organizations/123456789/sources/987654321',
    resourceName: 'projects/novafuse-testing/networks/network-1',
    category: 'THREAT',
    severity: 'LOW',
    eventTime: '2023-08-03T09:15:00Z',
    createTime: '2023-08-03T09:20:00Z',
    state: 'ACTIVE',
    sourceProperties: {
      compliance_standards: ['PCI-DSS'],
      description: 'Unusual traffic pattern detected in network'
    }
  }
];

// Define routes
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok' });
});

// OAuth token endpoint (simulation)
app.post('/token', (req, res) => {
  res.json({
    access_token: 'simulation-access-token',
    token_type: 'Bearer',
    expires_in: 3600
  });
});

// List findings
app.get('/v1/organizations/:organizationId/sources/:sourceId/findings', (req, res) => {
  const { organizationId, sourceId } = req.params;
  const { filter, pageSize, pageToken } = req.query;
  
  logger.info(`Listing findings for organization ${organizationId}, source ${sourceId}`);
  
  // Apply filters if provided
  let filteredFindings = findings;
  if (filter) {
    // In a real implementation, this would parse and apply the filter
    logger.info(`Filter would be applied: ${filter}`);
  }
  
  // Apply pagination
  const size = pageSize ? parseInt(pageSize) : 10;
  const start = pageToken ? parseInt(pageToken) : 0;
  const paginatedFindings = filteredFindings.slice(start, start + size);
  const nextPageToken = start + size < filteredFindings.length ? (start + size).toString() : null;
  
  res.json({
    findings: paginatedFindings,
    nextPageToken
  });
});

// Get a finding
app.get('/v1/organizations/:organizationId/sources/:sourceId/findings/:findingId', (req, res) => {
  const { organizationId, sourceId, findingId } = req.params;
  
  logger.info(`Getting finding ${findingId} for organization ${organizationId}, source ${sourceId}`);
  
  const finding = findings.find(f => f.id === findingId);
  if (!finding) {
    return res.status(404).json({ error: 'Finding not found' });
  }
  
  res.json(finding);
});

// Create a finding
app.post('/v1/organizations/:organizationId/sources/:sourceId/findings', (req, res) => {
  const { organizationId, sourceId } = req.params;
  const findingData = req.body;
  
  logger.info(`Creating finding for organization ${organizationId}, source ${sourceId}`);
  
  // Generate a new finding ID
  const findingId = `finding-${findings.length + 1}`;
  
  // Create the new finding
  const newFinding = {
    id: findingId,
    name: `organizations/${organizationId}/sources/${sourceId}/findings/${findingId}`,
    parent: `organizations/${organizationId}/sources/${sourceId}`,
    ...findingData,
    createTime: new Date().toISOString(),
    state: 'ACTIVE'
  };
  
  // Add to the findings list
  findings.push(newFinding);
  
  res.status(201).json(newFinding);
});

// Update a finding
app.patch('/v1/organizations/:organizationId/sources/:sourceId/findings/:findingId', (req, res) => {
  const { organizationId, sourceId, findingId } = req.params;
  const updateData = req.body;
  
  logger.info(`Updating finding ${findingId} for organization ${organizationId}, source ${sourceId}`);
  
  const findingIndex = findings.findIndex(f => f.id === findingId);
  if (findingIndex === -1) {
    return res.status(404).json({ error: 'Finding not found' });
  }
  
  // Update the finding
  findings[findingIndex] = {
    ...findings[findingIndex],
    ...updateData
  };
  
  res.json(findings[findingIndex]);
});

// List sources
app.get('/v1/organizations/:organizationId/sources', (req, res) => {
  const { organizationId } = req.params;
  
  logger.info(`Listing sources for organization ${organizationId}`);
  
  res.json({
    sources: [
      {
        name: `organizations/${organizationId}/sources/987654321`,
        displayName: 'NovaFuse Security',
        description: 'Security findings from NovaFuse'
      }
    ]
  });
});

// Start the server
const PORT = process.env.PORT || 8081;
app.listen(PORT, () => {
  logger.info(`Server running on port ${PORT}`);
  console.log(`Security Command Center Simulator running on http://localhost:${PORT}`);
});

module.exports = app;
