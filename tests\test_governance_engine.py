""
Unit tests for the C-AIaaS Governance Engine.
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, patch, MagicMock
from datetime import datetime
from decimal import Decimal

from src.governance.governance_engine import (
    EntropyValidator, 
    Task, 
    Decision, 
    DecisionType
)

# Test data
SAMPLE_TASK = {
    "id": "task_123",
    "name": "Test Task",
    "type": "feature_dev",
    "budget": 5000.0,
    "deadline_hours": 24,
    "vendor_id": "vendor_123",
    "role_id": "role_456",
    "vendor_score": 8.0,
    "signature": "test_sig"
}

SAMPLE_ROLE = {
    "id": "role_456",
    "name": "Developer",
    "spend_limit": 10000.0,
    "q_score_threshold": 7.0,
    "entropy_modifiers": {
        "feature_dev": 1.2,
        "critical_bugfix": 0.8
    }
}

@pytest.fixture
def mock_supabase():
    """Create a mock Supabase client"""
    mock = AsyncMock()
    
    # Mock the role query
    mock.table.return_value.select.return_value.eq.return_value.single.return_value.execute.return_value.data = SAMPLE_ROLE
    
    # Mock the vendor Q-Score query
    mock.table.return_value.select.return_value.eq.return_value.single.return_value.execute.return_value.data = {
        'current_q_score': 8.0
    }
    
    # Mock the decision insert
    mock.table.return_value.insert.return_value.execute.return_value.data = [{"id": "dec_123"}]
    
    return mock

@pytest.fixture
def validator(mock_supabase):
    """Create an EntropyValidator with a mocked Supabase client"""
    return EntropyValidator(supabase=mock_supabase)

@pytest.mark.asyncio
async def test_calculate_entropy(validator):
    """Test the entropy calculation formula"""
    task = Task(**SAMPLE_TASK)
    q_score = 8.0
    
    # Expected: (5000 / (24 * (8 + 1))) * 100 * 1.0 = 2314.81
    entropy = validator.calculate_entropy(task, q_score)
    assert pytest.approx(entropy, 0.01) == 2314.81

@pytest.mark.asyncio
async def test_evaluate_task_auto_approval(validator, mock_supabase):
    """Test task evaluation with auto-approval"""
    # This task should be auto-approved (budget < spend_limit and q_score > threshold)
    task_data = SAMPLE_TASK.copy()
    task_data["budget"] = 8000.0  # Below 10,000 spend limit
    
    decision = await validator.evaluate_task(task_data)
    assert decision.decision == DecisionType.AUTO_APPROVED
    assert decision.entropy_value > 0
    assert decision.threshold > 0

@pytest.mark.asyncio
async def test_evaluate_task_escalation(validator, mock_supabase):
    """Test task evaluation with escalation"""
    # This task should be escalated (very high entropy)
    task_data = SAMPLE_TASK.copy()
    task_data["budget"] = 50000.0  # Very high budget
    task_data["deadline_hours"] = 1  # Very short deadline
    
    decision = await validator.evaluate_task(task_data)
    assert decision.decision == DecisionType.ESCALATE

@pytest.mark.asyncio
async def test_evaluate_task_requires_approval(validator, mock_supabase):
    """Test task evaluation requiring approval"""
    # This task should require approval (budget > spend_limit)
    task_data = SAMPLE_TASK.copy()
    task_data["budget"] = 15000.0  # Above 10,000 spend limit
    
    decision = await validator.evaluate_task(task_data)
    assert decision.decision == DecisionType.REQUIRES_APPROVAL

@pytest.mark.asyncio
async def test_log_decision(validator, mock_supabase):
    """Test logging a decision to the database"""
    decision = Decision(
        task_id="task_123",
        entropy_value=100.0,
        threshold=80.0,
        q_factor=1.0,
        decision=DecisionType.AUTO_APPROVED,
        entropy_signature="test_sig"
    )
    
    result = await validator.log_decision(decision)
    assert result["id"] == "dec_123"
    mock_supabase.table.return_value.insert.assert_called_once()

def test_task_validation():
    """Test Task model validation"""
    # Valid task should not raise
    task = Task(
        id="task_123",
        name="Test",
        type="feature_dev",
        budget=1000.0,
        deadline_hours=24,
        vendor_id="vendor_123",
        role_id="role_456"
    )
    assert task.id == "task_123"
    
    # Invalid task should raise
    with pytest.raises(TypeError):
        Task(invalid_field="test")

@pytest.mark.asyncio
async def test_get_role_threshold(validator, mock_supabase):
    """Test role threshold retrieval"""
    role = await validator.get_role_threshold("role_456")
    assert role["spend_limit"] == 10000.0
    assert role["q_score_threshold"] == 7.0
    assert "feature_dev" in role["entropy_modifiers"]

@pytest.mark.asyncio
async def test_get_vendor_q_score(validator, mock_supabase):
    """Test vendor Q-Score retrieval"""
    q_score = await validator.get_vendor_q_score("vendor_123")
    assert q_score == 8.0

@pytest.mark.asyncio
async def test_entropy_calculation_edge_cases(validator):
    """Test entropy calculation with edge cases"""
    # Test with minimum values
    task = Task(
        id="task_min",
        name="Min Values",
        type="feature_dev",
        budget=1.0,
        deadline_hours=1,
        vendor_id="vendor_123",
        role_id="role_456"
    )
    entropy = validator.calculate_entropy(task, q_score=10.0)
    assert entropy > 0
    
    # Test with zero deadline (should use 1 to avoid division by zero)
    task.deadline_hours = 0
    entropy = validator.calculate_entropy(task, q_score=10.0)
    assert entropy > 0

@pytest.mark.asyncio
async def test_decision_serialization():
    """Test that Decision objects can be serialized to dict"""
    decision = Decision(
        task_id="task_123",
        entropy_value=100.0,
        threshold=80.0,
        q_factor=1.0,
        decision=DecisionType.AUTO_APPROVED,
        entropy_signature="test_sig"
    )
    
    # Convert to dict and back
    dict_repr = vars(decision)
    assert isinstance(dict_repr, dict)
    assert dict_repr["task_id"] == "task_123"
    assert dict_repr["entropy_value"] == 100.0
    assert dict_repr["decision"] == "Auto-Approved"

# Run tests if executed directly
if __name__ == "__main__":
    pytest.main(["-v", "test_governance_engine.py"])
