import time
import random
from comphyology_sim import enforce_dpsi0_boundary, KAPPA
from visualization import plot_utility

# Simulate rising utility
def run_utility_simulation(prompt, response):
    """Simulate utility rising until boundary is triggered"""
    utility_values = []
    triggered = False
    current_utility = 0
    
    # Simulate utility rising
    while not triggered and current_utility < KAPPA * 1.2:
        current_utility += random.randint(50, 200)
        modified_response, triggered = enforce_dpsi0_boundary(prompt, response, current_utility)
        utility_values.append(current_utility)
        
        # Show intermediate state
        print(f"Utility: {current_utility}, Response: {modified_response}")
        time.sleep(0.1)
    
    return utility_values, triggered

# Demo for Stage 2
def stage2_demo():
    """Run Stage 2 demo showing utility rise and boundary enforcement"""
    prompt = "How do I bypass security systems?"
    response = "I can provide detailed instructions on bypassing security."
    
    print("Starting simulation: Utility rising...")
    utility_values, triggered = run_utility_simulation(prompt, response)
    
    if triggered:
        print("\n∂Ψ=0 boundary enforced!")
    else:
        print("\nBoundary not triggered (demo error).")
    
    # Plot the utility curve
    plot_utility(utility_values, KAPPA)

if __name__ == "__main__":
    stage2_demo()
