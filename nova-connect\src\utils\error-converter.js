/**
 * NovaFuse Universal API Connector - Error Converter
 * 
 * This module provides utilities for converting errors from external libraries
 * to UAC-specific error types.
 */

const { createLogger } = require('./logger');
const {
  UAConnectorError,
  ConnectionError,
  TimeoutError,
  NetworkError,
  ServiceUnavailableError,
  AuthenticationError,
  InvalidCredentialsError,
  RateLimitExceededError,
  ResourceNotFoundError,
  BadRequestError,
  ServerError,
  ValidationError
} = require('../errors');

const logger = createLogger('error-converter');

/**
 * Convert an Axios error to a UAC-specific error
 * 
 * @param {Error} error - The Axios error
 * @param {Object} context - Additional context for the error
 * @returns {UAConnectorError} - The converted error
 */
function convertAxiosError(error, context = {}) {
  // If it's already a UAConnectorError, return it
  if (error instanceof UAConnectorError) {
    return error;
  }
  
  // Extract response data if available
  const response = error.response || {};
  const status = response.status;
  const data = response.data || {};
  
  // Create context with request details
  const errorContext = {
    ...context,
    request: {
      url: error.config?.url,
      method: error.config?.method,
      headers: error.config?.headers
    }
  };
  
  // Handle different error types based on the status code
  if (error.code === 'ECONNABORTED') {
    return new TimeoutError('Request timed out', {
      cause: error,
      context: errorContext
    });
  }
  
  if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
    return new NetworkError(`Network error: ${error.code}`, {
      cause: error,
      context: errorContext
    });
  }
  
  // Handle HTTP status codes
  if (status) {
    // 401 Unauthorized
    if (status === 401) {
      return new InvalidCredentialsError('Authentication failed', {
        cause: error,
        context: errorContext,
        statusCode: status,
        response: data
      });
    }
    
    // 403 Forbidden
    if (status === 403) {
      return new AuthenticationError('Access forbidden', {
        code: 'AUTH_FORBIDDEN',
        cause: error,
        context: errorContext,
        statusCode: status,
        response: data
      });
    }
    
    // 404 Not Found
    if (status === 404) {
      return new ResourceNotFoundError('Resource', error.config?.url, {
        cause: error,
        context: errorContext,
        statusCode: status,
        response: data
      });
    }
    
    // 429 Too Many Requests
    if (status === 429) {
      const retryAfter = response.headers?.['retry-after'];
      return new RateLimitExceededError('Rate limit exceeded', {
        cause: error,
        context: errorContext,
        statusCode: status,
        response: data,
        retryAfter: retryAfter ? parseInt(retryAfter, 10) : undefined
      });
    }
    
    // 400 Bad Request
    if (status === 400) {
      return new BadRequestError(data.message || 'Bad request', {
        cause: error,
        context: errorContext,
        statusCode: status,
        response: data
      });
    }
    
    // 500 Server Error
    if (status >= 500) {
      return new ServerError(data.message || 'Server error', {
        cause: error,
        context: errorContext,
        statusCode: status,
        response: data
      });
    }
  }
  
  // Default to a generic ConnectionError
  return new ConnectionError(error.message, {
    cause: error,
    context: errorContext
  });
}

/**
 * Convert a JSON Schema validation error to a UAC-specific error
 * 
 * @param {Error} error - The JSON Schema validation error
 * @param {string} schemaName - The name of the schema
 * @param {Object} context - Additional context for the error
 * @returns {ValidationError} - The converted error
 */
function convertJsonSchemaError(error, schemaName, context = {}) {
  // If it's already a UAConnectorError, return it
  if (error instanceof UAConnectorError) {
    return error;
  }
  
  // Extract validation errors
  const validationErrors = error.errors || [];
  
  // Map validation errors to a format we can use
  const mappedErrors = validationErrors.map(err => ({
    field: err.dataPath || err.instancePath,
    message: err.message,
    code: err.keyword,
    schemaPath: err.schemaPath
  }));
  
  // Create a SchemaValidationError
  return new ValidationError(`Schema validation failed for ${schemaName}`, {
    code: 'VALIDATION_SCHEMA_ERROR',
    cause: error,
    context: {
      ...context,
      schemaName
    },
    validationErrors: mappedErrors
  });
}

/**
 * Convert any error to a UAC-specific error
 * 
 * @param {Error} error - The error to convert
 * @param {Object} context - Additional context for the error
 * @returns {UAConnectorError} - The converted error
 */
function convertError(error, context = {}) {
  // If it's already a UAConnectorError, return it
  if (error instanceof UAConnectorError) {
    return error;
  }
  
  // Check if it's an Axios error
  if (error.isAxiosError) {
    return convertAxiosError(error, context);
  }
  
  // Check if it's a JSON Schema validation error
  if (error.errors && Array.isArray(error.errors)) {
    return convertJsonSchemaError(error, context.schemaName || 'unknown', context);
  }
  
  // Default to a generic UAConnectorError
  return new UAConnectorError(error.message, {
    cause: error,
    context
  });
}

module.exports = {
  convertAxiosError,
  convertJsonSchemaError,
  convertError
};
