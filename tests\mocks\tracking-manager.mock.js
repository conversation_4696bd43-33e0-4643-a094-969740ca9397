/**
 * Mock implementation of the TrackingManager class
 */

const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');

class TrackingManager {
  constructor(trackingDir) {
    this.trackingDir = trackingDir || path.join(process.cwd(), 'tracking_data');
    this.requirements = {};
    this.activities = {};
    
    // Create the tracking directory if it doesn't exist
    if (!fs.existsSync(this.trackingDir)) {
      fs.mkdirSync(this.trackingDir, { recursive: true });
    }
  }
  
  create_requirement(requirementData) {
    // Validate the requirement data
    if (!requirementData.name) {
      throw new Error('Requirement name is required');
    }
    
    // Validate status if provided
    if (requirementData.status) {
      const validStatuses = ['pending', 'in_progress', 'completed', 'deferred', 'cancelled'];
      if (!validStatuses.includes(requirementData.status)) {
        throw new Error(`Invalid status: ${requirementData.status}`);
      }
    }
    
    // Generate a unique requirement ID
    const requirementId = requirementData.id || `req-${uuidv4()}`;
    
    // Create the requirement object
    const requirement = {
      id: requirementId,
      name: requirementData.name,
      description: requirementData.description || '',
      framework: requirementData.framework || '',
      category: requirementData.category || '',
      priority: requirementData.priority || 'medium',
      status: requirementData.status || 'pending',
      due_date: requirementData.due_date || '',
      assigned_to: requirementData.assigned_to || '',
      tags: requirementData.tags || [],
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
    
    // Store the requirement in memory
    this.requirements[requirementId] = requirement;
    
    // Store the requirement on disk
    const requirementFilePath = path.join(this.trackingDir, `${requirementId}.json`);
    fs.writeFileSync(requirementFilePath, JSON.stringify(requirement, null, 2));
    
    return requirement;
  }
  
  get_requirement(requirementId) {
    if (!this.requirements[requirementId]) {
      throw new Error(`Requirement not found: ${requirementId}`);
    }
    
    return this.requirements[requirementId];
  }
  
  update_requirement(requirementId, requirementData) {
    if (!this.requirements[requirementId]) {
      throw new Error(`Requirement not found: ${requirementId}`);
    }
    
    const requirement = this.requirements[requirementId];
    
    // Update the requirement data
    if (requirementData.name) requirement.name = requirementData.name;
    if (requirementData.description) requirement.description = requirementData.description;
    if (requirementData.framework) requirement.framework = requirementData.framework;
    if (requirementData.category) requirement.category = requirementData.category;
    if (requirementData.priority) requirement.priority = requirementData.priority;
    if (requirementData.status) {
      const validStatuses = ['pending', 'in_progress', 'completed', 'deferred', 'cancelled'];
      if (!validStatuses.includes(requirementData.status)) {
        throw new Error(`Invalid status: ${requirementData.status}`);
      }
      requirement.status = requirementData.status;
    }
    if (requirementData.due_date) requirement.due_date = requirementData.due_date;
    if (requirementData.assigned_to) requirement.assigned_to = requirementData.assigned_to;
    if (requirementData.tags) requirement.tags = requirementData.tags;
    
    requirement.updated_at = new Date().toISOString();
    
    // Store the updated requirement on disk
    const requirementFilePath = path.join(this.trackingDir, `${requirementId}.json`);
    fs.writeFileSync(requirementFilePath, JSON.stringify(requirement, null, 2));
    
    return requirement;
  }
  
  delete_requirement(requirementId) {
    if (!this.requirements[requirementId]) {
      throw new Error(`Requirement not found: ${requirementId}`);
    }
    
    // Delete the requirement from memory
    delete this.requirements[requirementId];
    
    // Delete the requirement from disk
    const requirementFilePath = path.join(this.trackingDir, `${requirementId}.json`);
    if (fs.existsSync(requirementFilePath)) {
      fs.unlinkSync(requirementFilePath);
    }
    
    return true;
  }
  
  create_activity(activityData) {
    // Validate the activity data
    if (!activityData.name) {
      throw new Error('Activity name is required');
    }
    
    // Validate type if provided
    if (activityData.type) {
      const validTypes = ['task', 'meeting', 'review', 'audit', 'documentation', 'other'];
      if (!validTypes.includes(activityData.type)) {
        throw new Error(`Invalid type: ${activityData.type}`);
      }
    }
    
    // Validate status if provided
    if (activityData.status) {
      const validStatuses = ['pending', 'in_progress', 'completed', 'deferred', 'cancelled'];
      if (!validStatuses.includes(activityData.status)) {
        throw new Error(`Invalid status: ${activityData.status}`);
      }
    }
    
    // Generate a unique activity ID
    const activityId = activityData.id || `act-${uuidv4()}`;
    
    // Create the activity object
    const activity = {
      id: activityId,
      name: activityData.name,
      description: activityData.description || '',
      requirement_id: activityData.requirement_id,
      type: activityData.type || 'task',
      status: activityData.status || 'pending',
      start_date: activityData.start_date || '',
      end_date: activityData.end_date || '',
      assigned_to: activityData.assigned_to || '',
      notes: activityData.notes || '',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
    
    // Store the activity in memory
    this.activities[activityId] = activity;
    
    // Store the activity on disk
    const activityFilePath = path.join(this.trackingDir, `${activityId}.json`);
    fs.writeFileSync(activityFilePath, JSON.stringify(activity, null, 2));
    
    return activity;
  }
  
  get_activity(activityId) {
    if (!this.activities[activityId]) {
      throw new Error(`Activity not found: ${activityId}`);
    }
    
    return this.activities[activityId];
  }
  
  update_activity(activityId, activityData) {
    if (!this.activities[activityId]) {
      throw new Error(`Activity not found: ${activityId}`);
    }
    
    const activity = this.activities[activityId];
    
    // Update the activity data
    if (activityData.name) activity.name = activityData.name;
    if (activityData.description) activity.description = activityData.description;
    if (activityData.requirement_id) activity.requirement_id = activityData.requirement_id;
    if (activityData.type) {
      const validTypes = ['task', 'meeting', 'review', 'audit', 'documentation', 'other'];
      if (!validTypes.includes(activityData.type)) {
        throw new Error(`Invalid type: ${activityData.type}`);
      }
      activity.type = activityData.type;
    }
    if (activityData.status) {
      const validStatuses = ['pending', 'in_progress', 'completed', 'deferred', 'cancelled'];
      if (!validStatuses.includes(activityData.status)) {
        throw new Error(`Invalid status: ${activityData.status}`);
      }
      activity.status = activityData.status;
    }
    if (activityData.start_date) activity.start_date = activityData.start_date;
    if (activityData.end_date) activity.end_date = activityData.end_date;
    if (activityData.assigned_to) activity.assigned_to = activityData.assigned_to;
    if (activityData.notes) activity.notes = activityData.notes;
    
    activity.updated_at = new Date().toISOString();
    
    // Store the updated activity on disk
    const activityFilePath = path.join(this.trackingDir, `${activityId}.json`);
    fs.writeFileSync(activityFilePath, JSON.stringify(activity, null, 2));
    
    return activity;
  }
  
  delete_activity(activityId) {
    if (!this.activities[activityId]) {
      throw new Error(`Activity not found: ${activityId}`);
    }
    
    // Delete the activity from memory
    delete this.activities[activityId];
    
    // Delete the activity from disk
    const activityFilePath = path.join(this.trackingDir, `${activityId}.json`);
    if (fs.existsSync(activityFilePath)) {
      fs.unlinkSync(activityFilePath);
    }
    
    return true;
  }
  
  get_requirement_activities(requirementId) {
    return Object.values(this.activities).filter(activity => activity.requirement_id === requirementId);
  }
  
  get_requirements_by_framework(framework) {
    return Object.values(this.requirements).filter(requirement => requirement.framework === framework);
  }
}

module.exports = { TrackingManager };
