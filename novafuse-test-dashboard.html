<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NovaFuse Testing Dashboard - 210+ Test Files Unified</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.socket.io/4.7.4/socket.io.min.js"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .test-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }
        .test-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-pass { background-color: #10b981; }
        .status-fail { background-color: #ef4444; }
        .status-skip { background-color: #f59e0b; }
        .progress-bar {
            background: linear-gradient(90deg, #10b981 0%, #ffd700 50%, #ef4444 100%);
            height: 6px;
            border-radius: 3px;
        }
        .test-running {
            animation: pulse 1.5s infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
    </style>
</head>
<body class="gradient-bg min-h-screen text-white">
    <!-- Header -->
    <header class="p-6 border-b border-white/20">
        <div class="max-w-7xl mx-auto flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="p-3 rounded-full bg-white/10">
                    <i data-lucide="test-tube" class="w-8 h-8"></i>
                </div>
                <div>
                    <h1 class="text-3xl font-bold">NovaFuse Testing Dashboard</h1>
                    <p class="text-white/80">Unified Testing Framework • 210+ Test Files • 8 Categories</p>
                </div>
            </div>
            <div class="flex items-center space-x-4">
                <div class="text-right">
                    <div class="text-sm text-white/80">API Connection</div>
                    <div class="flex items-center">
                        <span class="w-2 h-2 bg-red-500 rounded-full mr-2"></span>
                        <span class="connection-status text-red-400 font-semibold">DISCONNECTED</span>
                    </div>
                </div>
                <button onclick="runAllTests()" class="px-4 py-2 bg-green-600 hover:bg-green-700 rounded-lg transition-colors flex items-center">
                    <i data-lucide="play" class="w-4 h-4 mr-2"></i>
                    Run All Tests
                </button>
                <button onclick="generateReport()" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors flex items-center">
                    <i data-lucide="file-text" class="w-4 h-4 mr-2"></i>
                    Generate Report
                </button>
            </div>
        </div>
    </header>

    <!-- Main Dashboard -->
    <main class="max-w-7xl mx-auto p-6">
        <!-- Test Summary -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-6 flex items-center">
                <i data-lucide="bar-chart" class="w-6 h-6 mr-2"></i>
                Test Execution Summary
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div class="test-card p-6 rounded-lg">
                    <div class="flex items-center justify-between mb-4">
                        <i data-lucide="list" class="w-8 h-8 text-blue-400"></i>
                        <span id="totalTests" class="text-2xl font-bold">210</span>
                    </div>
                    <h3 class="font-semibold mb-2">Total Tests</h3>
                    <p class="text-sm text-white/80">Across all categories</p>
                </div>
                
                <div class="test-card p-6 rounded-lg">
                    <div class="flex items-center justify-between mb-4">
                        <i data-lucide="check-circle" class="w-8 h-8 text-green-400"></i>
                        <span id="passedTests" class="text-2xl font-bold">192</span>
                    </div>
                    <h3 class="font-semibold mb-2">Passed</h3>
                    <p class="text-sm text-white/80">Successfully validated</p>
                </div>
                
                <div class="test-card p-6 rounded-lg">
                    <div class="flex items-center justify-between mb-4">
                        <i data-lucide="x-circle" class="w-8 h-8 text-red-400"></i>
                        <span id="failedTests" class="text-2xl font-bold">18</span>
                    </div>
                    <h3 class="font-semibold mb-2">Failed</h3>
                    <p class="text-sm text-white/80">Require attention</p>
                </div>
                
                <div class="test-card p-6 rounded-lg">
                    <div class="flex items-center justify-between mb-4">
                        <i data-lucide="percent" class="w-8 h-8 text-yellow-400"></i>
                        <span id="passRate" class="text-2xl font-bold">91.4%</span>
                    </div>
                    <h3 class="font-semibold mb-2">Pass Rate</h3>
                    <p class="text-sm text-white/80">Overall success rate</p>
                </div>
            </div>
        </section>

        <!-- Test Categories -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-6 flex items-center">
                <i data-lucide="folder" class="w-6 h-6 mr-2"></i>
                Test Categories (8 Frameworks)
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- UUFT Testing Suite -->
                <div class="test-card p-6 rounded-lg">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="font-semibold text-lg">UUFT Testing Suite</h3>
                        <span class="status-indicator status-pass"></span>
                    </div>
                    <p class="text-sm text-white/80 mb-3">Universal Unified Field Theory validation</p>
                    <div class="flex justify-between text-sm mb-2">
                        <span>19/20 passed</span>
                        <span class="text-green-400">95.0%</span>
                    </div>
                    <div class="progress-bar mb-3">
                        <div class="bg-green-500 h-full rounded" style="width: 95%"></div>
                    </div>
                    <div class="flex space-x-2">
                        <button onclick="runCategoryTests('uuft')" class="px-3 py-1 bg-blue-600 hover:bg-blue-700 rounded text-xs transition-colors">
                            Run Tests
                        </button>
                        <button onclick="viewDetails('uuft')" class="px-3 py-1 bg-gray-600 hover:bg-gray-700 rounded text-xs transition-colors">
                            View Details
                        </button>
                    </div>
                </div>

                <!-- Trinity Testing Framework -->
                <div class="test-card p-6 rounded-lg">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="font-semibold text-lg">Trinity Testing Framework</h3>
                        <span class="status-indicator status-fail"></span>
                    </div>
                    <p class="text-sm text-white/80 mb-3">Trinity consciousness validation</p>
                    <div class="flex justify-between text-sm mb-2">
                        <span>8/10 passed</span>
                        <span class="text-yellow-400">80.0%</span>
                    </div>
                    <div class="progress-bar mb-3">
                        <div class="bg-yellow-500 h-full rounded" style="width: 80%"></div>
                    </div>
                    <div class="flex space-x-2">
                        <button onclick="runCategoryTests('trinity')" class="px-3 py-1 bg-blue-600 hover:bg-blue-700 rounded text-xs transition-colors">
                            Run Tests
                        </button>
                        <button onclick="viewDetails('trinity')" class="px-3 py-1 bg-gray-600 hover:bg-gray-700 rounded text-xs transition-colors">
                            View Details
                        </button>
                    </div>
                </div>

                <!-- NovaConnect Testing -->
                <div class="test-card p-6 rounded-lg">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="font-semibold text-lg">NovaConnect Testing</h3>
                        <span class="status-indicator status-pass"></span>
                    </div>
                    <p class="text-sm text-white/80 mb-3">API and integration testing</p>
                    <div class="flex justify-between text-sm mb-2">
                        <span>47/50 passed</span>
                        <span class="text-green-400">94.0%</span>
                    </div>
                    <div class="progress-bar mb-3">
                        <div class="bg-green-500 h-full rounded" style="width: 94%"></div>
                    </div>
                    <div class="flex space-x-2">
                        <button onclick="runCategoryTests('novaconnect')" class="px-3 py-1 bg-blue-600 hover:bg-blue-700 rounded text-xs transition-colors">
                            Run Tests
                        </button>
                        <button onclick="viewDetails('novaconnect')" class="px-3 py-1 bg-gray-600 hover:bg-gray-700 rounded text-xs transition-colors">
                            View Details
                        </button>
                    </div>
                </div>

                <!-- Compliance Testing -->
                <div class="test-card p-6 rounded-lg">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="font-semibold text-lg">Compliance Testing</h3>
                        <span class="status-indicator status-pass"></span>
                    </div>
                    <p class="text-sm text-white/80 mb-3">Regulatory validation</p>
                    <div class="flex justify-between text-sm mb-2">
                        <span>15/15 passed</span>
                        <span class="text-green-400">100%</span>
                    </div>
                    <div class="progress-bar mb-3">
                        <div class="bg-green-500 h-full rounded" style="width: 100%"></div>
                    </div>
                    <div class="flex space-x-2">
                        <button onclick="runCategoryTests('compliance')" class="px-3 py-1 bg-blue-600 hover:bg-blue-700 rounded text-xs transition-colors">
                            Run Tests
                        </button>
                        <button onclick="viewDetails('compliance')" class="px-3 py-1 bg-gray-600 hover:bg-gray-700 rounded text-xs transition-colors">
                            View Details
                        </button>
                    </div>
                </div>

                <!-- Performance Testing -->
                <div class="test-card p-6 rounded-lg">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="font-semibold text-lg">Performance Testing</h3>
                        <span class="status-indicator status-pass"></span>
                    </div>
                    <p class="text-sm text-white/80 mb-3">Benchmark and load testing</p>
                    <div class="flex justify-between text-sm mb-2">
                        <span>23/25 passed</span>
                        <span class="text-green-400">92.0%</span>
                    </div>
                    <div class="progress-bar mb-3">
                        <div class="bg-green-500 h-full rounded" style="width: 92%"></div>
                    </div>
                    <div class="flex space-x-2">
                        <button onclick="runCategoryTests('performance')" class="px-3 py-1 bg-blue-600 hover:bg-blue-700 rounded text-xs transition-colors">
                            Run Tests
                        </button>
                        <button onclick="viewDetails('performance')" class="px-3 py-1 bg-gray-600 hover:bg-gray-700 rounded text-xs transition-colors">
                            View Details
                        </button>
                    </div>
                </div>

                <!-- Security Testing -->
                <div class="test-card p-6 rounded-lg">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="font-semibold text-lg">Security Testing</h3>
                        <span class="status-indicator status-pass"></span>
                    </div>
                    <p class="text-sm text-white/80 mb-3">Penetration and vulnerability testing</p>
                    <div class="flex justify-between text-sm mb-2">
                        <span>20/20 passed</span>
                        <span class="text-green-400">100%</span>
                    </div>
                    <div class="progress-bar mb-3">
                        <div class="bg-green-500 h-full rounded" style="width: 100%"></div>
                    </div>
                    <div class="flex space-x-2">
                        <button onclick="runCategoryTests('security')" class="px-3 py-1 bg-blue-600 hover:bg-blue-700 rounded text-xs transition-colors">
                            Run Tests
                        </button>
                        <button onclick="viewDetails('security')" class="px-3 py-1 bg-gray-600 hover:bg-gray-700 rounded text-xs transition-colors">
                            View Details
                        </button>
                    </div>
                </div>

                <!-- Coherence Testing -->
                <div class="test-card p-6 rounded-lg">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="font-semibold text-lg">Coherence Testing</h3>
                        <span class="status-indicator status-pass"></span>
                    </div>
                    <p class="text-sm text-white/80 mb-3">Coherence validation protocols</p>
                    <div class="flex justify-between text-sm mb-2">
                        <span>5/5 passed</span>
                        <span class="text-green-400">100%</span>
                    </div>
                    <div class="progress-bar mb-3">
                        <div class="bg-green-500 h-full rounded" style="width: 100%"></div>
                    </div>
                    <div class="flex space-x-2">
                        <button onclick="runCategoryTests('coherence')" class="px-3 py-1 bg-blue-600 hover:bg-blue-700 rounded text-xs transition-colors">
                            Run Tests
                        </button>
                        <button onclick="viewDetails('coherence')" class="px-3 py-1 bg-gray-600 hover:bg-gray-700 rounded text-xs transition-colors">
                            View Details
                        </button>
                    </div>
                </div>

                <!-- Specialized Testing -->
                <div class="test-card p-6 rounded-lg">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="font-semibold text-lg">Specialized Testing</h3>
                        <span class="status-indicator status-pass"></span>
                    </div>
                    <p class="text-sm text-white/80 mb-3">Domain-specific testing</p>
                    <div class="flex justify-between text-sm mb-2">
                        <span>60/65 passed</span>
                        <span class="text-green-400">92.3%</span>
                    </div>
                    <div class="progress-bar mb-3">
                        <div class="bg-green-500 h-full rounded" style="width: 92.3%"></div>
                    </div>
                    <div class="flex space-x-2">
                        <button onclick="runCategoryTests('specialized')" class="px-3 py-1 bg-blue-600 hover:bg-blue-700 rounded text-xs transition-colors">
                            Run Tests
                        </button>
                        <button onclick="viewDetails('specialized')" class="px-3 py-1 bg-gray-600 hover:bg-gray-700 rounded text-xs transition-colors">
                            View Details
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <!-- Quick Actions -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-6 flex items-center">
                <i data-lucide="zap" class="w-6 h-6 mr-2"></i>
                Quick Actions
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <button onclick="runCriticalTests()" class="test-card p-4 rounded-lg text-left hover:bg-white/20 transition-colors">
                    <i data-lucide="alert-triangle" class="w-6 h-6 text-red-400 mb-2"></i>
                    <h3 class="font-semibold mb-1">Critical Tests Only</h3>
                    <p class="text-xs text-white/80">Run high-priority tests</p>
                </button>
                
                <button onclick="runFailedTests()" class="test-card p-4 rounded-lg text-left hover:bg-white/20 transition-colors">
                    <i data-lucide="refresh-cw" class="w-6 h-6 text-yellow-400 mb-2"></i>
                    <h3 class="font-semibold mb-1">Re-run Failed</h3>
                    <p class="text-xs text-white/80">Retry failed tests only</p>
                </button>
                
                <button onclick="runCoverageAnalysis()" class="test-card p-4 rounded-lg text-left hover:bg-white/20 transition-colors">
                    <i data-lucide="pie-chart" class="w-6 h-6 text-blue-400 mb-2"></i>
                    <h3 class="font-semibold mb-1">Coverage Analysis</h3>
                    <p class="text-xs text-white/80">Analyze test coverage</p>
                </button>
                
                <button onclick="exportResults()" class="test-card p-4 rounded-lg text-left hover:bg-white/20 transition-colors">
                    <i data-lucide="download" class="w-6 h-6 text-green-400 mb-2"></i>
                    <h3 class="font-semibold mb-1">Export Results</h3>
                    <p class="text-xs text-white/80">Download test reports</p>
                </button>
            </div>
        </section>
    </main>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // API Configuration
        const API_BASE_URL = 'http://localhost:3100/api';
        let socket = null;
        let isConnected = false;

        // Initialize WebSocket connection
        function initializeWebSocket() {
            try {
                socket = io('http://localhost:3100');

                socket.on('connect', () => {
                    console.log('Connected to NovaFuse Test API Server');
                    isConnected = true;
                    updateConnectionStatus(true);
                });

                socket.on('disconnect', () => {
                    console.log('Disconnected from NovaFuse Test API Server');
                    isConnected = false;
                    updateConnectionStatus(false);
                });

                socket.on('test-started', (data) => {
                    console.log('Test execution started:', data);
                    updateTestProgress(data);
                });

                socket.on('test-progress', (data) => {
                    console.log('Test progress:', data);
                    updateTestProgress(data);
                });

                socket.on('test-completed', (data) => {
                    console.log('Test execution completed:', data);
                    updateTestResults(data);
                });

                socket.on('test-failed', (data) => {
                    console.log('Test execution failed:', data);
                    showTestError(data);
                });

            } catch (error) {
                console.error('WebSocket initialization failed:', error);
                isConnected = false;
                updateConnectionStatus(false);
            }
        }

        // Update connection status indicator
        function updateConnectionStatus(connected) {
            const statusElements = document.querySelectorAll('.connection-status');
            statusElements.forEach(el => {
                el.textContent = connected ? 'CONNECTED' : 'DISCONNECTED';
                el.className = connected ? 'text-green-400 font-semibold' : 'text-red-400 font-semibold';
            });
        }

        // API Helper Functions
        async function apiRequest(endpoint, options = {}) {
            try {
                const response = await fetch(`${API_BASE_URL}${endpoint}`, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });

                const data = await response.json();

                if (!data.success) {
                    throw new Error(data.error || 'API request failed');
                }

                return data.data;
            } catch (error) {
                console.error(`API request failed for ${endpoint}:`, error);
                throw error;
            }
        }

        // Test execution functions
        async function runAllTests() {
            if (!isConnected) {
                alert('Not connected to test server. Please check if the NovaFuse Test API Server is running on port 3100.');
                return;
            }

            try {
                showTestExecutionStarted();

                const result = await apiRequest('/tests/run', {
                    method: 'POST',
                    body: JSON.stringify({
                        categories: [], // Empty means all categories
                        options: {
                            comprehensive: true,
                            realTimeUpdates: true
                        }
                    })
                });

                console.log('Test execution started:', result);

            } catch (error) {
                console.error('Failed to start test execution:', error);
                alert(`Failed to start test execution: ${error.message}`);
            }
        }

        async function runCategoryTests(category) {
            if (!isConnected) {
                alert('Not connected to test server. Please check if the NovaFuse Test API Server is running.');
                return;
            }

            try {
                showTestExecutionStarted();

                const result = await apiRequest('/tests/run', {
                    method: 'POST',
                    body: JSON.stringify({
                        categories: [category],
                        options: {
                            realTimeUpdates: true
                        }
                    })
                });

                console.log(`${category} test execution started:`, result);

            } catch (error) {
                console.error(`Failed to start ${category} tests:`, error);
                alert(`Failed to start ${category} tests: ${error.message}`);
            }
        }

        async function runCriticalTests() {
            if (!isConnected) {
                alert('Not connected to test server.');
                return;
            }

            try {
                showTestExecutionStarted();

                const result = await apiRequest('/tests/run', {
                    method: 'POST',
                    body: JSON.stringify({
                        categories: ['uuft', 'trinity', 'compliance', 'coherence'],
                        options: {
                            priority: 'critical',
                            realTimeUpdates: true
                        }
                    })
                });

                console.log('Critical test execution started:', result);

            } catch (error) {
                console.error('Failed to start critical tests:', error);
                alert(`Failed to start critical tests: ${error.message}`);
            }
        }

        function runFailedTests() {
            alert('Re-running failed tests...\n\nThis would retry only the tests that previously failed.');
        }

        function runCoverageAnalysis() {
            alert('Analyzing test coverage...\n\nThis would generate a detailed coverage report for all components.');
        }

        function viewDetails(category) {
            alert(`Viewing detailed results for ${category}...\n\nThis would show individual test results, logs, and failure details.`);
        }

        function generateReport() {
            alert('Generating comprehensive test report...\n\nThis would create HTML, JSON, and PDF reports of all test results.');
        }

        function exportResults() {
            alert('Exporting test results...\n\nThis would download test reports in multiple formats (HTML, JSON, CSV, PDF).');
        }

        // Initialize WebSocket connection on page load
        document.addEventListener('DOMContentLoaded', function() {
            initializeWebSocket();
        });

        // Simulate real-time updates (fallback when not connected)
        setInterval(() => {
            if (!isConnected) {
                // Update pass rate with slight variations
                const currentRate = parseFloat(document.getElementById('passRate').textContent);
                const newRate = (currentRate + (Math.random() - 0.5) * 0.1).toFixed(1);
                document.getElementById('passRate').textContent = newRate + '%';
            }
        }, 10000);
    </script>
</body>
</html>
