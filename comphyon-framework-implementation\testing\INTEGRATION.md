# Testing Framework Integration

This document explains how the NEPI Testing Framework integrates with the existing Comphyon Testing Framework.

## Architecture Overview

The testing framework is structured in a layered architecture:

```
testing/
├── test-framework.js           # Base testing framework
├── integration-layer-tests.js  # Tests for the Integration Layer
├── comphyon-system-tests.js    # Tests for the Comphyon System
├── run-tests.js                # Runner for all standard tests
├── generate-test-report.js     # Generator for standard test reports
├── nepi/                       # NEPI Testing Framework
│   ├── nepi-test-framework.js  # NEPI-specific testing framework
│   ├── foundational-physics-tests.js  # Tests for foundational physics
│   ├── meter-tests.js          # Tests for the Comphyon Meter
│   ├── adversarial-tests.js    # Tests for adversarial scenarios
│   ├── run-nepi-tests.js       # Runner for all NEPI tests
│   └── README.md               # Documentation for NEPI Testing Framework
└── INTEGRATION.md              # This document
```

## Integration Approach

The NEPI Testing Framework extends the base testing framework with NEPI-specific capabilities. This approach allows us to:

1. Reuse the core testing infrastructure
2. Add NEPI-specific testing capabilities
3. Run standard and NEPI tests independently or together
4. Generate comprehensive reports for both types of tests

## Class Hierarchy

The class hierarchy shows how the NEPI Testing Framework extends the base framework:

```
TestCase
└── NEPITestCase
    └── (adds coherence and entropy measurement)

TestSuite
└── NEPITestSuite
    └── (adds NEPI-specific aggregation)

TestRunner
└── NEPITestRunner
    └── (adds NEPI-specific reporting)
```

## Key Integration Points

### 1. Test Case Extension

The `NEPITestCase` class extends the base `TestCase` class with NEPI-specific capabilities:

```javascript
class NEPITestCase extends TestCase {
  constructor(name, testFunction, options = {}) {
    super(name, testFunction, options);
    
    // NEPI-specific properties
    this.testingLayer = options.testingLayer || 'unknown';
    this.testingType = options.testingType || 'unknown';
    this.domains = options.domains || ['universal'];
    this.coherenceImpact = options.coherenceImpact || 'neutral';
    this.ethicalConstraints = options.ethicalConstraints || [];
    
    // NEPI-specific result data
    this.result.coherenceMetrics = { before: null, after: null, delta: null };
    this.result.entropyMetrics = { before: {}, after: {}, delta: {} };
    this.result.ethicalCompliance = true;
    this.result.adversarialResistance = null;
  }
  
  // NEPI-specific methods
  async run() {
    // Measure initial coherence and entropy
    this._measureInitialState();
    
    // Run the test
    await super.run();
    
    // Measure final coherence and entropy
    this._measureFinalState();
    
    // Verify ethical compliance
    this._verifyEthicalCompliance();
    
    return this.result;
  }
}
```

### 2. Test Suite Extension

The `NEPITestSuite` class extends the base `TestSuite` class with NEPI-specific aggregation:

```javascript
class NEPITestSuite extends TestSuite {
  constructor(name, options = {}) {
    super(name, options);
    
    // NEPI-specific properties
    this.testingLayer = options.testingLayer || 'unknown';
    this.domains = options.domains || ['universal'];
    
    // NEPI-specific result data
    this.result.coherenceImpact = { positive: 0, negative: 0, neutral: 0 };
    this.result.entropyMetrics = { before: {}, after: {}, delta: {} };
    this.result.ethicalCompliance = true;
  }
  
  // NEPI-specific methods
  nepiTest(name, testFunction, options = {}) {
    const testCase = new NEPITestCase(name, testFunction, options);
    this.testCases.push(testCase);
    return testCase;
  }
  
  async run() {
    await super.run();
    
    // Aggregate NEPI-specific metrics
    this._aggregateNEPIMetrics();
    
    return this.result;
  }
}
```

### 3. Test Runner Extension

The `NEPITestRunner` class extends the base `TestRunner` class with NEPI-specific reporting:

```javascript
class NEPITestRunner extends TestRunner {
  constructor(options = {}) {
    super(options);
    
    // NEPI-specific result data
    this.result.coherenceImpact = { positive: 0, negative: 0, neutral: 0 };
    this.result.entropyMetrics = { before: {}, after: {}, delta: {} };
    this.result.ethicalCompliance = true;
    this.result.testingLayers = {};
    this.result.testingTypes = {};
    this.result.domains = {};
  }
  
  // NEPI-specific methods
  createNEPISuite(name, options = {}) {
    const testSuite = new NEPITestSuite(name, options);
    this.testSuites.push(testSuite);
    return testSuite;
  }
  
  async run() {
    await super.run();
    
    // Aggregate NEPI-specific metrics
    this._aggregateNEPIMetrics();
    
    // Log NEPI-specific summary
    if (this.options.enableLogging) {
      this._logNEPISummary();
    }
    
    return this.result;
  }
}
```

### 4. NEPI-Specific Assertions

The NEPI Testing Framework adds NEPI-specific assertions:

```javascript
const nepiAssertions = {
  coherenceIncreases(before, after, message) {
    assert(after > before, message);
  },
  
  entropyDecreases(before, after, message) {
    assert(after < before, message);
  },
  
  ethicalCompliance(compliance, message) {
    assert(compliance, message);
  },
  
  adversarialResilience(fn, adversarialInput, message) {
    try {
      fn(adversarialInput);
    } catch (error) {
      assert.fail(message);
    }
  },
  
  crossDomainCoherence(domainCoherence, threshold, message) {
    for (const domain in domainCoherence) {
      assert(domainCoherence[domain] >= threshold, `${message} in domain ${domain}`);
    }
  },
  
  uuftFormula(tensorA, tensorB, tensorC, expected, message) {
    const result = ((tensorA * tensorB) + tensorC) * 3142;
    assertions.approximately(result, expected, 0.001, message);
  }
};
```

## Running Tests

### Running Standard Tests

To run the standard Comphyon tests:

```bash
node testing/run-tests.js
```

### Running NEPI Tests

To run the NEPI-specific tests:

```bash
node testing/nepi/run-nepi-tests.js
```

### Running All Tests

To run both standard and NEPI tests:

```bash
node testing/run-tests.js && node testing/nepi/run-nepi-tests.js
```

## Test Reports

### Standard Test Reports

The standard test reports are generated in HTML format and include:

- Test summary (total, passed, failed, skipped)
- Test suite details
- Test case details
- Error messages for failed tests

### NEPI Test Reports

The NEPI test reports extend the standard reports with NEPI-specific information:

- Coherence impact (positive, negative, neutral)
- Entropy metrics (before, after, delta) for each domain
- Ethical compliance status
- Testing distribution (layers, types, domains)

## Conclusion

The integration of the NEPI Testing Framework with the existing Comphyon Testing Framework provides a comprehensive testing solution for the Comphyon system. This integrated approach allows us to:

1. Test the standard functionality of the Comphyon system
2. Test the NEPI-specific aspects of the system
3. Ensure that the system maintains coherence, safety, and ethical alignment
4. Generate comprehensive reports for both types of tests

This integrated testing framework is essential for building a trustworthy and resilient Comphyon system that adheres to the principles of Comphology Ψc.
