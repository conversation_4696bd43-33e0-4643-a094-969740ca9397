/**
 * Bridge
 *
 * This module exports all components of the Bridge system.
 * The Bridge integrates the CSDE, CSFE, and CSME components.
 */

const DomainTranslationLayer = require('./domain-translation-layer');
const UnifiedRiskScoring = require('./unified-risk-scoring');
const CrossDomainIntegration = require('./cross-domain-integration');

/**
 * Create a basic Bridge system
 * @param {Object} options - Configuration options
 * @returns {Object} - Bridge system components
 */
function createBridgeSystem(options = {}) {
  // Create components
  const domainTranslationLayer = new DomainTranslationLayer(options.domainTranslationLayerOptions);
  const unifiedRiskScoring = new UnifiedRiskScoring(options.unifiedRiskScoringOptions);
  const crossDomainIntegration = new CrossDomainIntegration(options.crossDomainIntegrationOptions);

  return {
    domainTranslationLayer,
    unifiedRiskScoring,
    crossDomainIntegration
  };
}

/**
 * Create an enhanced Bridge system with integrated components
 * @param {Object} options - Configuration options
 * @param {Object} engines - Engine components (csde, csfe, csme)
 * @returns {Object} - Enhanced Bridge system
 */
function createEnhancedBridgeSystem(options = {}, engines = {}) {
  // Create basic system
  const bridgeSystem = createBridgeSystem(options);

  // Set up event listeners for integration

  // When domain correlations are updated in risk scoring, update translation layer
  bridgeSystem.unifiedRiskScoring.on('correlation-update', (data) => {
    bridgeSystem.domainTranslationLayer.updateDomainCorrelations(data.domainCorrelations);
  });

  // When a metric is translated, update risk scoring
  bridgeSystem.domainTranslationLayer.on('metric-translated', (data) => {
    // Update risk score for target domain
    bridgeSystem.unifiedRiskScoring.updateDomainRiskScore(
      data.targetDomain,
      data.targetValue,
      {
        source: 'translation',
        sourceDomain: data.sourceDomain,
        sourceMetric: data.sourceMetric,
        confidence: data.confidence
      }
    );
  });

  // When a pattern is detected, add risk factor
  bridgeSystem.crossDomainIntegration.on('pattern-detected', (data) => {
    bridgeSystem.unifiedRiskScoring.addRiskFactor({
      domain: 'cross-domain',
      type: 'pattern',
      impact: data.priority === 'critical' ? 0.9 :
              data.priority === 'high' ? 0.7 :
              data.priority === 'medium' ? 0.5 : 0.3,
      likelihood: 0.8,
      description: `Pattern: ${data.patternName}`,
      metadata: {
        patternId: data.patternId,
        triggeringDomain: data.triggeringDomain
      }
    });
  });

  // Set up engine integrations if provided
  if (engines.csde) {
    _integrateCSDE(bridgeSystem, engines.csde, options);
  }

  if (engines.csfe) {
    _integrateCSFE(bridgeSystem, engines.csfe, options);
  }

  if (engines.csme) {
    _integrateCSME(bridgeSystem, engines.csme, options);
  }

  // Add enhanced methods
  const enhancedSystem = {
    ...bridgeSystem,

    /**
     * Start all components
     * @returns {boolean} - Success status
     */
    start() {
      const dtlStarted = bridgeSystem.domainTranslationLayer ? true : true; // DTL doesn't have start method
      const ursStarted = bridgeSystem.unifiedRiskScoring.start();
      const cdiStarted = bridgeSystem.crossDomainIntegration.start();

      return dtlStarted && ursStarted && cdiStarted;
    },

    /**
     * Stop all components
     * @returns {boolean} - Success status
     */
    stop() {
      const dtlStopped = bridgeSystem.domainTranslationLayer ? true : true; // DTL doesn't have stop method
      const ursStopped = bridgeSystem.unifiedRiskScoring.stop();
      const cdiStopped = bridgeSystem.crossDomainIntegration.stop();

      return dtlStopped && ursStopped && cdiStopped;
    },

    /**
     * Get unified state from all components
     * @returns {Object} - Unified state
     */
    getUnifiedState() {
      return {
        unifiedRiskScoring: bridgeSystem.unifiedRiskScoring.getState(),
        crossDomainIntegration: bridgeSystem.crossDomainIntegration.getState(),
        domainCorrelations: bridgeSystem.domainTranslationLayer.getDomainCorrelations(),
        timestamp: Date.now()
      };
    },

    /**
     * Get unified metrics from all components
     * @returns {Object} - Unified metrics
     */
    getUnifiedMetrics() {
      return {
        domainTranslationLayer: bridgeSystem.domainTranslationLayer.getMetrics(),
        unifiedRiskScoring: bridgeSystem.unifiedRiskScoring.getMetrics(),
        crossDomainIntegration: bridgeSystem.crossDomainIntegration.getMetrics(),
        timestamp: Date.now()
      };
    },

    /**
     * Process domain data
     * @param {string} domain - Domain (cyber, financial, biological)
     * @param {string} key - Data key
     * @param {*} data - Data value
     * @param {Object} metadata - Additional metadata
     * @returns {Object} - Processing result
     */
    processDomainData(domain, key, data, metadata = {}) {
      // Skip processing if skipFeedback flag is set (to prevent circular updates)
      if (metadata && metadata.skipFeedback) {
        return {
          domain,
          key,
          data,
          detectedPatterns: [],
          translations: [],
          timestamp: Date.now(),
          skipped: true
        };
      }

      // Process data in cross-domain integration
      const detectedPatterns = bridgeSystem.crossDomainIntegration.processDomainData(domain, key, data, metadata);

      // Update risk score for domain
      let riskValue = data;
      if (typeof data === 'object' && data.value !== undefined) {
        riskValue = data.value;
      }

      if (typeof riskValue === 'number') {
        bridgeSystem.unifiedRiskScoring.updateDomainRiskScore(domain, riskValue, {
          source: 'data',
          key,
          metadata
        });
      }

      // Translate data to other domains
      const translations = [];
      const otherDomains = ['cyber', 'financial', 'biological'].filter(d => d !== domain);

      for (const targetDomain of otherDomains) {
        try {
          const translation = bridgeSystem.domainTranslationLayer.translateMetric(
            domain,
            targetDomain,
            key,
            typeof riskValue === 'number' ? riskValue : 0.5
          );

          translations.push(translation);
        } catch (error) {
          if (options.enableLogging) {
            console.error(`Bridge: Error translating metric from ${domain} to ${targetDomain}:`, error.message);
          }
        }
      }

      return {
        domain,
        key,
        data,
        detectedPatterns,
        translations,
        timestamp: Date.now()
      };
    },

    /**
     * Process domain event
     * @param {string} domain - Domain (cyber, financial, biological)
     * @param {Object} event - Domain event
     * @returns {Object} - Processing result
     */
    processDomainEvent(domain, event) {
      // Process event in cross-domain integration
      const crossDomainEvents = bridgeSystem.crossDomainIntegration.processDomainEvent(domain, event);

      // Add risk factor based on event
      const eventSeverity = event.severity || 'medium';
      const impact = eventSeverity === 'critical' ? 0.9 :
                    eventSeverity === 'high' ? 0.7 :
                    eventSeverity === 'medium' ? 0.5 : 0.3;

      bridgeSystem.unifiedRiskScoring.addRiskFactor({
        domain,
        type: 'event',
        impact,
        likelihood: 0.8,
        description: event.description || `${domain} event`,
        metadata: {
          eventId: event.id,
          eventType: event.type
        }
      });

      // Translate event to other domains
      const translations = [];
      const otherDomains = ['cyber', 'financial', 'biological'].filter(d => d !== domain);

      for (const targetDomain of otherDomains) {
        try {
          const translation = bridgeSystem.domainTranslationLayer.translateEvent(
            domain,
            targetDomain,
            event
          );

          translations.push(translation);
        } catch (error) {
          if (options.enableLogging) {
            console.error(`Bridge: Error translating event from ${domain} to ${targetDomain}:`, error.message);
          }
        }
      }

      return {
        domain,
        event,
        crossDomainEvents,
        translations,
        timestamp: Date.now()
      };
    },

    /**
     * Register cross-domain pattern
     * @param {Object} pattern - Pattern definition
     * @returns {Object} - Registered pattern
     */
    registerPattern(pattern) {
      return bridgeSystem.crossDomainIntegration.registerPattern(pattern);
    },

    /**
     * Get cross-domain risk assessment
     * @returns {Object} - Risk assessment
     */
    getCrossDomainRiskAssessment() {
      const unifiedRiskScore = bridgeSystem.unifiedRiskScoring.getUnifiedRiskScore();
      const domainRiskScores = bridgeSystem.unifiedRiskScoring.getDomainRiskScores();
      const riskStatus = bridgeSystem.unifiedRiskScoring.getRiskStatus();
      const domainCorrelations = bridgeSystem.unifiedRiskScoring.getDomainCorrelations();
      const riskFactors = bridgeSystem.unifiedRiskScoring.getRiskFactors();

      return {
        unifiedRiskScore,
        domainRiskScores,
        riskStatus,
        domainCorrelations,
        topRiskFactors: riskFactors.slice(0, 5),
        timestamp: Date.now()
      };
    }
  };

  return enhancedSystem;
}

/**
 * Integrate CSDE with Bridge
 * @param {Object} bridgeSystem - Bridge system
 * @param {Object} csde - CSDE system
 * @param {Object} options - Configuration options
 * @private
 */
function _integrateCSDE(bridgeSystem, csde, options = {}) {
  if (!csde) return;

  // Process initial CSDE data
  try {
    // Process policy entropy
    if (csde.entropicGRCControlSystem && csde.entropicGRCControlSystem.getPolicyEntropy) {
      const policyEntropy = csde.entropicGRCControlSystem.getPolicyEntropy();
      bridgeSystem.processDomainData('cyber', 'policy_entropy', policyEntropy, {
        source: 'csde',
        initialIntegration: true
      });
    }

    // Process audit entropy
    if (csde.entropicGRCControlSystem && csde.entropicGRCControlSystem.getAuditEntropy) {
      const auditEntropy = csde.entropicGRCControlSystem.getAuditEntropy();
      bridgeSystem.processDomainData('cyber', 'audit_entropy', auditEntropy, {
        source: 'csde',
        initialIntegration: true
      });
    }

    // Process regulatory entropy
    if (csde.entropicGRCControlSystem && csde.entropicGRCControlSystem.getRegulatoryEntropy) {
      const regulatoryEntropy = csde.entropicGRCControlSystem.getRegulatoryEntropy();
      bridgeSystem.processDomainData('cyber', 'regulatory_entropy', regulatoryEntropy, {
        source: 'csde',
        initialIntegration: true
      });
    }
  } catch (error) {
    if (options.enableLogging) {
      console.error('Bridge: Error integrating CSDE:', error.message);
    }
  }

  // Register pattern for policy violations
  bridgeSystem.crossDomainIntegration.registerPattern({
    name: 'Policy Violation Pattern',
    description: 'Detects policy violations and their cross-domain impacts',
    domains: ['cyber', 'financial'],
    conditions: [
      {
        domain: 'cyber',
        key: 'policy_entropy',
        operator: 'gt',
        value: 0.7
      },
      {
        domain: 'financial',
        key: 'transaction_entropy',
        operator: 'gt',
        value: 0.6
      }
    ],
    action: 'alert',
    priority: 'high',
    alertType: 'security',
    alertMessage: 'High policy entropy detected with financial impact'
  });
}

/**
 * Integrate CSFE with Bridge
 * @param {Object} bridgeSystem - Bridge system
 * @param {Object} csfe - CSFE system
 * @param {Object} options - Configuration options
 * @private
 */
function _integrateCSFE(bridgeSystem, csfe, options = {}) {
  if (!csfe) return;

  // Process initial CSFE data
  try {
    // Process transaction entropy
    if (csfe.transactionEntropy && csfe.transactionEntropy.getState) {
      const teState = csfe.transactionEntropy.getState();
      bridgeSystem.processDomainData('financial', 'transaction_entropy', teState.transactionEntropy || 0.5, {
        source: 'csfe',
        initialIntegration: true
      });
    }

    // Process attack surface coherence
    if (csfe.attackSurfaceCoherence && csfe.attackSurfaceCoherence.getCoherenceScore) {
      const coherenceScore = csfe.attackSurfaceCoherence.getCoherenceScore();
      bridgeSystem.processDomainData('financial', 'attack_surface_coherence', coherenceScore, {
        source: 'csfe',
        initialIntegration: true
      });
    }

    // Process market stress
    if (csfe.marketStressInfusion && csfe.marketStressInfusion.getMarketStress) {
      const marketStress = csfe.marketStressInfusion.getMarketStress();
      bridgeSystem.processDomainData('financial', 'market_stress', marketStress, {
        source: 'csfe',
        initialIntegration: true
      });
    }
  } catch (error) {
    if (options.enableLogging) {
      console.error('Bridge: Error integrating CSFE:', error.message);
    }
  }

  // Register pattern for market stress
  bridgeSystem.crossDomainIntegration.registerPattern({
    name: 'Market Stress Pattern',
    description: 'Detects market stress and its cross-domain impacts',
    domains: ['financial', 'biological'],
    conditions: [
      {
        domain: 'financial',
        key: 'market_stress',
        operator: 'gt',
        value: 0.7
      },
      {
        domain: 'biological',
        key: 'inflammation_level',
        operator: 'gt',
        value: 0.5
      }
    ],
    action: 'alert',
    priority: 'high',
    alertType: 'financial',
    alertMessage: 'High market stress detected with biological impact'
  });
}

/**
 * Integrate CSME with Bridge
 * @param {Object} bridgeSystem - Bridge system
 * @param {Object} csme - CSME system
 * @param {Object} options - Configuration options
 * @private
 */
function _integrateCSME(bridgeSystem, csme, options = {}) {
  if (!csme) return;

  // Process initial CSME data
  try {
    // Process telomere length
    if (csme.telomereErosionPrediction && csme.telomereErosionPrediction.getState) {
      const tepState = csme.telomereErosionPrediction.getState();
      bridgeSystem.processDomainData('biological', 'telomere_length', tepState.telomereLength || 0.5, {
        source: 'csme',
        initialIntegration: true
      });
    }

    // Process mTOR activation
    if (csme.mtorActivationMonitoring && csme.mtorActivationMonitoring.getState) {
      const mamState = csme.mtorActivationMonitoring.getState();
      bridgeSystem.processDomainData('biological', 'mtor_activation', mamState.activationLevel || 0.5, {
        source: 'csme',
        initialIntegration: true
      });
    }

    // Process inflammation level
    if (csme.inflammationCascadeModeling && csme.inflammationCascadeModeling.getState) {
      const icmState = csme.inflammationCascadeModeling.getState();
      bridgeSystem.processDomainData('biological', 'inflammation_level', icmState.inflammationLevel || 0.5, {
        source: 'csme',
        initialIntegration: true
      });
    }
  } catch (error) {
    if (options.enableLogging) {
      console.error('Bridge: Error integrating CSME:', error.message);
    }
  }

  // Register pattern for inflammation
  bridgeSystem.crossDomainIntegration.registerPattern({
    name: 'Inflammation Pattern',
    description: 'Detects inflammation and its cross-domain impacts',
    domains: ['biological', 'cyber'],
    conditions: [
      {
        domain: 'biological',
        key: 'inflammation_level',
        operator: 'gt',
        value: 0.7
      },
      {
        domain: 'cyber',
        key: 'policy_entropy',
        operator: 'gt',
        value: 0.5
      }
    ],
    action: 'alert',
    priority: 'medium',
    alertType: 'health',
    alertMessage: 'High inflammation detected with cyber impact'
  });
}

module.exports = {
  DomainTranslationLayer,
  UnifiedRiskScoring,
  CrossDomainIntegration,
  createBridgeSystem,
  createEnhancedBridgeSystem
};
