/**
 * NovaFuse Universal API Connector Rate Limiter
 * 
 * This module provides rate limiting functionality for the UAC.
 */

/**
 * Rate Limiting Utility
 * 
 * Provides methods for rate limiting API requests.
 */
class RateLimiter {
  constructor(options = {}) {
    this.options = {
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 100, // 100 requests per minute
      keyGenerator: (req) => req.ip || 'default',
      ...options
    };
    
    this.requests = new Map();
    this.cleanupInterval = setInterval(() => this.cleanup(), this.options.windowMs);
  }

  /**
   * Check if a request is allowed
   * @param {Object} req - Request object
   * @returns {boolean} - Whether the request is allowed
   */
  isAllowed(req) {
    const key = this.options.keyGenerator(req);
    const now = Date.now();
    
    // Get or create request record
    let record = this.requests.get(key);
    if (!record) {
      record = {
        count: 0,
        resetTime: now + this.options.windowMs
      };
      this.requests.set(key, record);
    }
    
    // Reset count if window has passed
    if (now > record.resetTime) {
      record.count = 0;
      record.resetTime = now + this.options.windowMs;
    }
    
    // Check if request is allowed
    if (record.count >= this.options.maxRequests) {
      return false;
    }
    
    // Increment count
    record.count++;
    
    return true;
  }

  /**
   * Get remaining requests for a key
   * @param {Object} req - Request object
   * @returns {number} - Remaining requests
   */
  getRemainingRequests(req) {
    const key = this.options.keyGenerator(req);
    const now = Date.now();
    
    // Get request record
    const record = this.requests.get(key);
    if (!record) {
      return this.options.maxRequests;
    }
    
    // Reset count if window has passed
    if (now > record.resetTime) {
      return this.options.maxRequests;
    }
    
    return Math.max(0, this.options.maxRequests - record.count);
  }

  /**
   * Get reset time for a key
   * @param {Object} req - Request object
   * @returns {number} - Reset time in milliseconds
   */
  getResetTime(req) {
    const key = this.options.keyGenerator(req);
    
    // Get request record
    const record = this.requests.get(key);
    if (!record) {
      return Date.now() + this.options.windowMs;
    }
    
    return record.resetTime;
  }

  /**
   * Clean up expired records
   */
  cleanup() {
    const now = Date.now();
    
    for (const [key, record] of this.requests.entries()) {
      if (now > record.resetTime) {
        this.requests.delete(key);
      }
    }
  }

  /**
   * Stop the rate limiter and clear the cleanup interval
   */
  stop() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
  }
}

module.exports = RateLimiter;
