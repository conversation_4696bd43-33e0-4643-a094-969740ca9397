5957b7b02d1fc5c6d8f17ac86ca973b0
/**
 * NovaFuse Universal API Connector - Error Classes Tests
 * 
 * This module tests the error classes for the UAC.
 */

const {
  UAConnectorError,
  AuthenticationError,
  MissingCredentialsError,
  ConnectionError,
  TimeoutError,
  ValidationError,
  MissingRequiredFieldError,
  ApiError,
  RateLimitExceededError,
  ResourceNotFoundError,
  TransformationError,
  ConnectorError,
  ConnectorNotFoundError
} = require('../../src/errors');
describe('Error Classes', () => {
  describe('UAConnectorError', () => {
    it('should create a base error with default values', () => {
      const error = new UAConnectorError('Test error');
      expect(error).toBeInstanceOf(Error);
      expect(error.name).toBe('UAConnectorError');
      expect(error.message).toBe('Test error');
      expect(error.code).toBe('UAC_ERROR');
      expect(error.severity).toBe('error');
      expect(error.context).toEqual({});
      expect(error.cause).toBeUndefined();
      expect(error.timestamp).toBeDefined();
      expect(error.errorId).toBeDefined();
    });
    it('should create a base error with custom values', () => {
      const cause = new Error('Original error');
      const error = new UAConnectorError('Test error', {
        code: 'CUSTOM_CODE',
        severity: 'warning',
        context: {
          foo: 'bar'
        },
        cause
      });
      expect(error.code).toBe('CUSTOM_CODE');
      expect(error.severity).toBe('warning');
      expect(error.context).toEqual({
        foo: 'bar'
      });
      expect(error.cause).toBe(cause);
    });
    it('should convert to JSON', () => {
      const error = new UAConnectorError('Test error', {
        code: 'CUSTOM_CODE',
        context: {
          foo: 'bar'
        }
      });
      const json = error.toJSON();
      expect(json.errorId).toBe(error.errorId);
      expect(json.name).toBe('UAConnectorError');
      expect(json.message).toBe('Test error');
      expect(json.code).toBe('CUSTOM_CODE');
      expect(json.severity).toBe('error');
      expect(json.context).toEqual({
        foo: 'bar'
      });
      expect(json.stack).toBeUndefined();
    });
    it('should convert to JSON with stack trace', () => {
      const error = new UAConnectorError('Test error');
      const json = error.toJSON(true);
      expect(json.stack).toBeDefined();
    });
    it('should provide user and developer messages', () => {
      const error = new UAConnectorError('Test error', {
        code: 'CUSTOM_CODE'
      });
      expect(error.getUserMessage()).toBe('Test error');
      expect(error.getDeveloperMessage()).toBe('[CUSTOM_CODE] Test error');
    });
  });
  describe('AuthenticationError', () => {
    it('should create an authentication error', () => {
      const error = new AuthenticationError('Auth failed');
      expect(error).toBeInstanceOf(UAConnectorError);
      expect(error.name).toBe('AuthenticationError');
      expect(error.code).toBe('AUTH_ERROR');
      expect(error.getUserMessage()).toBe('Authentication failed. Please check your credentials and try again.');
    });
    it('should create a missing credentials error', () => {
      const error = new MissingCredentialsError();
      expect(error).toBeInstanceOf(AuthenticationError);
      expect(error.name).toBe('MissingCredentialsError');
      expect(error.code).toBe('AUTH_MISSING_CREDENTIALS');
      expect(error.getUserMessage()).toBe('Authentication failed. Required credentials are missing.');
    });
  });
  describe('ConnectionError', () => {
    it('should create a connection error', () => {
      const error = new ConnectionError('Connection failed');
      expect(error).toBeInstanceOf(UAConnectorError);
      expect(error.name).toBe('ConnectionError');
      expect(error.code).toBe('CONNECTION_ERROR');
      expect(error.getUserMessage()).toBe('Failed to connect to the service. Please check your network connection and try again.');
    });
    it('should create a timeout error', () => {
      const error = new TimeoutError();
      expect(error).toBeInstanceOf(ConnectionError);
      expect(error.name).toBe('TimeoutError');
      expect(error.code).toBe('CONNECTION_TIMEOUT');
      expect(error.getUserMessage()).toBe('The request timed out. Please try again later or contact support if the issue persists.');
    });
  });
  describe('ValidationError', () => {
    it('should create a validation error', () => {
      const error = new ValidationError('Validation failed');
      expect(error).toBeInstanceOf(UAConnectorError);
      expect(error.name).toBe('ValidationError');
      expect(error.code).toBe('VALIDATION_ERROR');
      expect(error.getUserMessage()).toBe('The provided data is invalid. Please check your input and try again.');
    });
    it('should create a validation error with validation errors', () => {
      const validationErrors = [{
        field: 'name',
        message: 'Name is required'
      }, {
        field: 'email',
        message: 'Email is invalid'
      }];
      const error = new ValidationError('Validation failed', {
        validationErrors
      });
      expect(error.validationErrors).toEqual(validationErrors);
      expect(error.getUserMessage()).toBe('Validation failed: Name is required; Email is invalid');
    });
    it('should create a missing required field error', () => {
      const error = new MissingRequiredFieldError('name');
      expect(error).toBeInstanceOf(ValidationError);
      expect(error.name).toBe('MissingRequiredFieldError');
      expect(error.code).toBe('VALIDATION_MISSING_REQUIRED_FIELD');
      expect(error.getUserMessage()).toBe('Validation failed: Missing required field: name');
    });
    it('should create a missing required field error with multiple fields', () => {
      const error = new MissingRequiredFieldError(['name', 'email']);
      expect(error.validationErrors.length).toBe(2);
      expect(error.getUserMessage()).toBe('Validation failed: Missing required field: name; Missing required field: email');
    });
  });
  describe('ApiError', () => {
    it('should create an API error', () => {
      const error = new ApiError('API error');
      expect(error).toBeInstanceOf(UAConnectorError);
      expect(error.name).toBe('ApiError');
      expect(error.code).toBe('API_ERROR');
      expect(error.getUserMessage()).toBe('An error occurred while communicating with the external service. Please try again later.');
    });
    it('should create an API error with status code and response', () => {
      const error = new ApiError('API error', {
        statusCode: 400,
        response: {
          message: 'Bad request'
        }
      });
      expect(error.statusCode).toBe(400);
      expect(error.response).toEqual({
        message: 'Bad request'
      });
      const json = error.toJSON();
      expect(json.statusCode).toBe(400);
      expect(json.response).toEqual({
        message: 'Bad request'
      });
    });
    it('should create a rate limit exceeded error', () => {
      const error = new RateLimitExceededError('Rate limit exceeded', {
        retryAfter: 60
      });
      expect(error).toBeInstanceOf(ApiError);
      expect(error.name).toBe('RateLimitExceededError');
      expect(error.code).toBe('API_RATE_LIMIT_EXCEEDED');
      expect(error.retryAfter).toBe(60);
      expect(error.getUserMessage()).toBe('Rate limit exceeded. Please try again after 60 seconds.');
    });
    it('should create a resource not found error', () => {
      const error = new ResourceNotFoundError('User', '123');
      expect(error).toBeInstanceOf(ApiError);
      expect(error.name).toBe('ResourceNotFoundError');
      expect(error.code).toBe('API_RESOURCE_NOT_FOUND');
      expect(error.resourceType).toBe('User');
      expect(error.resourceId).toBe('123');
      expect(error.getUserMessage()).toBe('The requested user could not be found.');
    });
  });
  describe('TransformationError', () => {
    it('should create a transformation error', () => {
      const error = new TransformationError('Transformation failed');
      expect(error).toBeInstanceOf(UAConnectorError);
      expect(error.name).toBe('TransformationError');
      expect(error.code).toBe('TRANSFORMATION_ERROR');
      expect(error.getUserMessage()).toBe('An error occurred while processing the data. Please contact support if the issue persists.');
    });
  });
  describe('ConnectorError', () => {
    it('should create a connector error', () => {
      const error = new ConnectorError('Connector error', {
        connectorId: 'test-connector'
      });
      expect(error).toBeInstanceOf(UAConnectorError);
      expect(error.name).toBe('ConnectorError');
      expect(error.code).toBe('CONNECTOR_ERROR');
      expect(error.connectorId).toBe('test-connector');
      expect(error.getUserMessage()).toBe('An error occurred with the connector. Please try again later or contact support if the issue persists.');
    });
    it('should create a connector not found error', () => {
      const error = new ConnectorNotFoundError('test-connector');
      expect(error).toBeInstanceOf(ConnectorError);
      expect(error.name).toBe('ConnectorNotFoundError');
      expect(error.code).toBe('CONNECTOR_NOT_FOUND');
      expect(error.connectorId).toBe('test-connector');
      expect(error.getUserMessage()).toBe('The connector "test-connector" was not found. Please check the connector ID and try again.');
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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