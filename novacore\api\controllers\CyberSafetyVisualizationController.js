/**
 * Cyber-Safety Visualization Controller
 * 
 * This controller provides endpoints for the Cyber-Safety fusion visualizations.
 * It serves data for the following visualization types:
 * 1. Tri-Domain Tensor Visualization
 * 2. Cyber-Safety Harmony Index
 * 3. Risk-Control Fusion Map
 * 4. Cyber-Safety Resonance Spectrogram
 * 5. Unified Compliance-Security Visualizer
 */

const logger = require('../../config/logger');
const { visualizationService } = require('../../cyber-safety/services');

/**
 * Get Tri-Domain Tensor Visualization data
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getTriDomainTensorData = async (req, res, next) => {
  try {
    logger.debug('Getting Tri-Domain Tensor Visualization data');
    
    // Get query parameters
    const { timeframe, domains } = req.query;
    
    // Get data from service
    const data = await visualizationService.getTriDomainTensorData({
      timeframe,
      domains: domains ? domains.split(',') : undefined
    });
    
    // Return data
    res.status(200).json({
      success: true,
      data
    });
  } catch (error) {
    logger.error('Error getting Tri-Domain Tensor Visualization data:', error);
    next(error);
  }
};

/**
 * Get Cyber-Safety Harmony Index data
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getHarmonyIndexData = async (req, res, next) => {
  try {
    logger.debug('Getting Cyber-Safety Harmony Index data');
    
    // Get query parameters
    const { timeframe, historyPoints } = req.query;
    
    // Get data from service
    const data = await visualizationService.getHarmonyIndexData({
      timeframe,
      historyPoints: historyPoints ? parseInt(historyPoints, 10) : undefined
    });
    
    // Return data
    res.status(200).json({
      success: true,
      data
    });
  } catch (error) {
    logger.error('Error getting Cyber-Safety Harmony Index data:', error);
    next(error);
  }
};

/**
 * Get Risk-Control Fusion Map data
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getRiskControlFusionData = async (req, res, next) => {
  try {
    logger.debug('Getting Risk-Control Fusion Map data');
    
    // Get query parameters
    const { timeframe, domains, categories } = req.query;
    
    // Get data from service
    const data = await visualizationService.getRiskControlFusionData({
      timeframe,
      domains: domains ? domains.split(',') : undefined,
      categories: categories ? categories.split(',') : undefined
    });
    
    // Return data
    res.status(200).json({
      success: true,
      data
    });
  } catch (error) {
    logger.error('Error getting Risk-Control Fusion Map data:', error);
    next(error);
  }
};

/**
 * Get Cyber-Safety Resonance Spectrogram data
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getResonanceSpectrogramData = async (req, res, next) => {
  try {
    logger.debug('Getting Cyber-Safety Resonance Spectrogram data');
    
    // Get query parameters
    const { timeframe, domains, predictionHorizon } = req.query;
    
    // Get data from service
    const data = await visualizationService.getResonanceSpectrogramData({
      timeframe,
      domains: domains ? domains.split(',') : undefined,
      predictionHorizon: predictionHorizon ? parseInt(predictionHorizon, 10) : undefined
    });
    
    // Return data
    res.status(200).json({
      success: true,
      data
    });
  } catch (error) {
    logger.error('Error getting Cyber-Safety Resonance Spectrogram data:', error);
    next(error);
  }
};

/**
 * Get Unified Compliance-Security Visualizer data
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getUnifiedComplianceSecurityData = async (req, res, next) => {
  try {
    logger.debug('Getting Unified Compliance-Security Visualizer data');
    
    // Get query parameters
    const { timeframe, frameworks, domains } = req.query;
    
    // Get data from service
    const data = await visualizationService.getUnifiedComplianceSecurityData({
      timeframe,
      frameworks: frameworks ? frameworks.split(',') : undefined,
      domains: domains ? domains.split(',') : undefined
    });
    
    // Return data
    res.status(200).json({
      success: true,
      data
    });
  } catch (error) {
    logger.error('Error getting Unified Compliance-Security Visualizer data:', error);
    next(error);
  }
};

/**
 * Get real-time visualization data
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getRealTimeData = async (req, res, next) => {
  try {
    logger.debug('Getting real-time visualization data');
    
    // Get visualization type from path parameter
    const { visualizationType } = req.params;
    
    // Get data from service
    const data = await visualizationService.getRealTimeData({
      visualizationType
    });
    
    // Return data
    res.status(200).json({
      success: true,
      data
    });
  } catch (error) {
    logger.error('Error getting real-time visualization data:', error);
    next(error);
  }
};

/**
 * Get historical visualization data
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getHistoricalData = async (req, res, next) => {
  try {
    logger.debug('Getting historical visualization data');
    
    // Get visualization type from path parameter
    const { visualizationType } = req.params;
    
    // Get query parameters
    const { startDate, endDate, interval } = req.query;
    
    // Get data from service
    const data = await visualizationService.getHistoricalData({
      visualizationType,
      startDate,
      endDate,
      interval
    });
    
    // Return data
    res.status(200).json({
      success: true,
      data
    });
  } catch (error) {
    logger.error('Error getting historical visualization data:', error);
    next(error);
  }
};

/**
 * Export visualization data
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const exportData = async (req, res, next) => {
  try {
    logger.debug('Exporting visualization data');
    
    // Get visualization type from path parameter
    const { visualizationType } = req.params;
    
    // Get query parameters
    const { format } = req.query;
    
    // Get data from service
    const { data, contentType, filename } = await visualizationService.exportData({
      visualizationType,
      format: format || 'json'
    });
    
    // Set response headers
    res.setHeader('Content-Type', contentType);
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    
    // Return data
    res.status(200).send(data);
  } catch (error) {
    logger.error('Error exporting visualization data:', error);
    next(error);
  }
};

/**
 * Submit visualization feedback
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const submitFeedback = async (req, res, next) => {
  try {
    logger.debug('Submitting visualization feedback');
    
    // Get visualization type from path parameter
    const { visualizationType } = req.params;
    
    // Get feedback data from request body
    const feedbackData = req.body;
    
    // Submit feedback
    await visualizationService.submitFeedback({
      visualizationType,
      feedbackData
    });
    
    // Return success
    res.status(200).json({
      success: true,
      message: 'Feedback submitted successfully'
    });
  } catch (error) {
    logger.error('Error submitting visualization feedback:', error);
    next(error);
  }
};

module.exports = {
  getTriDomainTensorData,
  getHarmonyIndexData,
  getRiskControlFusionData,
  getResonanceSpectrogramData,
  getUnifiedComplianceSecurityData,
  getRealTimeData,
  getHistoricalData,
  exportData,
  submitFeedback
};
