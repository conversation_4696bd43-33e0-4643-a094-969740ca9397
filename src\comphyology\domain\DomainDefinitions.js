/**
 * DomainDefinitions.js
 * 
 * This module defines the boundaries and physics for each domain universe
 * (Biological, Financial, Cyber/GRC) within the Comphyological framework.
 * 
 * Each domain has its own unique boundaries and physics rules that govern
 * the behavior of entities within that domain.
 */

/**
 * Biological Domain Boundaries
 * 
 * Defines the boundaries of the biological domain universe, including
 * parameters related to cellular function, genetic expression, and
 * physiological systems.
 */
const BIOLOGICAL_BOUNDARIES = {
  // Cellular parameters
  cellularEnergy: { min: 0, max: 100 },
  cellularIntegrity: { min: 0, max: 1 },
  cellularReplication: { min: 0, max: 1 },
  
  // Genetic parameters
  geneticExpression: { min: 0, max: 1 },
  geneticStability: { min: 0, max: 1 },
  geneticDiversity: { min: 0, max: 1 },
  
  // Physiological parameters
  physiologicalFunction: { min: 0, max: 1 },
  immuneResponse: { min: 0, max: 1 },
  metabolicRate: { min: 0, max: 10 },
  
  // System parameters
  systemCoherence: { min: 0, max: 1 },
  systemResilience: { min: 0, max: 1 },
  systemAdaptability: { min: 0, max: 1 }
};

/**
 * Biological Domain Physics
 * 
 * Defines the physics rules that govern the biological domain universe,
 * including entropy rates, healing mechanisms, and resonance thresholds.
 */
const BIOLOGICAL_PHYSICS = {
  entropyRate: 0.005, // Biological systems naturally increase in entropy
  resonanceThreshold: 0.6, // Minimum resonance for biological stability
  healingRate: 0.1, // Biological systems have strong healing capabilities
  
  // Domain-specific physics rules
  cellularDynamics: {
    replicationThreshold: 0.7, // Minimum cellular integrity for replication
    energyConsumptionRate: 0.02, // Rate at which cellular energy is consumed
    damageRepairRate: 0.03 // Rate at which cellular damage is repaired
  },
  
  geneticDynamics: {
    mutationRate: 0.001, // Rate of genetic mutations
    expressionThreshold: 0.5, // Threshold for gene expression
    stabilityDecayRate: 0.002 // Rate at which genetic stability decays
  },
  
  physiologicalDynamics: {
    homeostasisStrength: 0.8, // Strength of homeostatic mechanisms
    adaptationRate: 0.05, // Rate at which systems adapt to changes
    immuneResponseThreshold: 0.4 // Threshold for immune response activation
  }
};

/**
 * Financial Domain Boundaries
 * 
 * Defines the boundaries of the financial domain universe, including
 * parameters related to value exchange, risk assessment, and resource allocation.
 */
const FINANCIAL_BOUNDARIES = {
  // Value parameters
  valueExchange: { min: 0, max: 1000000000 }, // Value exchange magnitude
  valueStability: { min: 0, max: 1 }, // Stability of value
  valueGrowth: { min: -0.5, max: 0.5 }, // Rate of value growth/decline
  
  // Risk parameters
  riskExposure: { min: 0, max: 1 }, // Exposure to risk
  riskMitigation: { min: 0, max: 1 }, // Effectiveness of risk mitigation
  riskDiversity: { min: 0, max: 1 }, // Diversity of risk
  
  // Resource parameters
  resourceAllocation: { min: 0, max: 1 }, // Efficiency of resource allocation
  resourceUtilization: { min: 0, max: 1 }, // Efficiency of resource utilization
  resourceSustainability: { min: 0, max: 1 }, // Sustainability of resource use
  
  // System parameters
  systemLiquidity: { min: 0, max: 1 }, // Liquidity of the system
  systemTransparency: { min: 0, max: 1 }, // Transparency of the system
  systemResilience: { min: 0, max: 1 } // Resilience of the system
};

/**
 * Financial Domain Physics
 * 
 * Defines the physics rules that govern the financial domain universe,
 * including entropy rates, healing mechanisms, and resonance thresholds.
 */
const FINANCIAL_PHYSICS = {
  entropyRate: 0.01, // Financial systems naturally increase in entropy
  resonanceThreshold: 0.7, // Minimum resonance for financial stability
  healingRate: 0.03, // Financial systems have moderate healing capabilities
  
  // Domain-specific physics rules
  valueDynamics: {
    volatilityFactor: 0.05, // Factor affecting value volatility
    inflationRate: 0.02, // Natural rate of value inflation
    exchangeEfficiency: 0.9 // Efficiency of value exchange
  },
  
  riskDynamics: {
    exposureGrowthRate: 0.03, // Rate at which risk exposure grows
    mitigationEffectiveness: 0.8, // Effectiveness of risk mitigation
    diversificationBenefit: 0.7 // Benefit from risk diversification
  },
  
  resourceDynamics: {
    allocationEfficiency: 0.8, // Efficiency of resource allocation
    utilizationDecayRate: 0.02, // Rate at which utilization efficiency decays
    sustainabilityThreshold: 0.6 // Threshold for resource sustainability
  }
};

/**
 * Cyber/GRC Domain Boundaries
 * 
 * Defines the boundaries of the cyber/GRC domain universe, including
 * parameters related to information flow, security boundaries, and compliance structures.
 */
const CYBER_BOUNDARIES = {
  // Information parameters
  informationIntegrity: { min: 0, max: 1 }, // Integrity of information
  informationAvailability: { min: 0, max: 1 }, // Availability of information
  informationConfidentiality: { min: 0, max: 1 }, // Confidentiality of information
  
  // Security parameters
  securityStrength: { min: 0, max: 1 }, // Strength of security measures
  threatExposure: { min: 0, max: 1 }, // Exposure to threats
  vulnerabilityDensity: { min: 0, max: 1 }, // Density of vulnerabilities
  
  // Compliance parameters
  complianceLevel: { min: 0, max: 1 }, // Level of compliance
  regulatoryBurden: { min: 0, max: 1 }, // Burden of regulatory requirements
  governanceEffectiveness: { min: 0, max: 1 }, // Effectiveness of governance
  
  // System parameters
  systemResilience: { min: 0, max: 1 }, // Resilience of the system
  systemAdaptability: { min: 0, max: 1 }, // Adaptability of the system
  systemComplexity: { min: 0, max: 1 } // Complexity of the system
};

/**
 * Cyber/GRC Domain Physics
 * 
 * Defines the physics rules that govern the cyber/GRC domain universe,
 * including entropy rates, healing mechanisms, and resonance thresholds.
 */
const CYBER_PHYSICS = {
  entropyRate: 0.02, // Cyber systems rapidly increase in entropy
  resonanceThreshold: 0.8, // High resonance required for cyber stability
  healingRate: 0.01, // Cyber systems have limited natural healing
  
  // Domain-specific physics rules
  informationDynamics: {
    integrityDecayRate: 0.03, // Rate at which information integrity decays
    availabilityFluctuationRate: 0.05, // Rate of availability fluctuations
    confidentialityBreachProbability: 0.01 // Probability of confidentiality breaches
  },
  
  securityDynamics: {
    strengthDecayRate: 0.04, // Rate at which security strength decays
    threatEvolutionRate: 0.05, // Rate at which threats evolve
    vulnerabilityDiscoveryRate: 0.03 // Rate at which vulnerabilities are discovered
  },
  
  complianceDynamics: {
    regulatoryChangeRate: 0.02, // Rate of regulatory changes
    governanceEffectivenessThreshold: 0.7, // Threshold for effective governance
    complianceVerificationFrequency: 0.1 // Frequency of compliance verification
  }
};

module.exports = {
  BIOLOGICAL_BOUNDARIES,
  BIOLOGICAL_PHYSICS,
  FINANCIAL_BOUNDARIES,
  FINANCIAL_PHYSICS,
  CYBER_BOUNDARIES,
  CYBER_PHYSICS
};
