/**
 * useFeatureFlag Hook
 * 
 * A custom React hook for checking if a feature is enabled based on the current product.
 */

import { useContext } from 'react';
import { ProductContext } from './ProductContext';
import { isFeatureEnabled } from './index';

/**
 * Hook to check if a feature is enabled for the current product
 * @param {string} category - The feature category (dashboard, grc, etc.)
 * @param {string} feature - The feature name (overview, analytics, etc.)
 * @returns {boolean} - Whether the feature is enabled
 */
export function useFeatureFlag(category, feature) {
  const { product } = useContext(ProductContext);
  
  if (!product) {
    console.warn('No product found in ProductContext');
    return false;
  }
  
  return isFeatureEnabled(product, category, feature);
}

/**
 * Hook to check if a feature is enabled for a specific product
 * @param {string} product - The product name (novaPrime, novaCore, etc.)
 * @param {string} category - The feature category (dashboard, grc, etc.)
 * @param {string} feature - The feature name (overview, analytics, etc.)
 * @returns {boolean} - Whether the feature is enabled
 */
export function useProductFeatureFlag(product, category, feature) {
  return isFeatureEnabled(product, category, feature);
}

/**
 * Hook to check if the current product is a specific product
 * @param {string} productToCheck - The product to check
 * @returns {boolean} - Whether the current product is the specified product
 */
export function useIsProduct(productToCheck) {
  const { product } = useContext(ProductContext);
  
  if (!product) {
    console.warn('No product found in ProductContext');
    return false;
  }
  
  return product === productToCheck;
}

export default useFeatureFlag;
