const { v4: uuidv4 } = require('uuid');
const models = require('./models');

/**
 * Get a list of ESG frameworks
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getFrameworks = (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      category,
      isActive,
      industry,
      region,
      regulatoryStatus,
      maturityLevel,
      implementationCost,
      implementationComplexity,
      tag,
      effectiveBefore,
      effectiveAfter,
      expirationBefore,
      expirationAfter,
      sortBy = 'name',
      sortOrder = 'asc'
    } = req.query;

    // Convert page and limit to numbers
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);

    // Filter frameworks based on query parameters
    let filteredFrameworks = [...models.esgFrameworks];

    if (category) {
      filteredFrameworks = filteredFrameworks.filter(framework => framework.category === category);
    }

    if (isActive !== undefined) {
      const activeFilter = isActive === 'true';
      filteredFrameworks = filteredFrameworks.filter(framework => framework.isActive === activeFilter);
    }

    if (industry) {
      filteredFrameworks = filteredFrameworks.filter(framework =>
        framework.industry && framework.industry.toLowerCase().includes(industry.toLowerCase())
      );
    }

    if (region) {
      filteredFrameworks = filteredFrameworks.filter(framework =>
        framework.region && framework.region.toLowerCase().includes(region.toLowerCase())
      );
    }

    if (regulatoryStatus) {
      filteredFrameworks = filteredFrameworks.filter(framework => framework.regulatoryStatus === regulatoryStatus);
    }

    if (maturityLevel) {
      filteredFrameworks = filteredFrameworks.filter(framework => framework.maturityLevel === maturityLevel);
    }

    if (implementationCost) {
      filteredFrameworks = filteredFrameworks.filter(framework => framework.implementationCost === implementationCost);
    }

    if (implementationComplexity) {
      filteredFrameworks = filteredFrameworks.filter(framework => framework.implementationComplexity === implementationComplexity);
    }

    if (tag) {
      filteredFrameworks = filteredFrameworks.filter(framework =>
        framework.tags && framework.tags.some(t => t.toLowerCase().includes(tag.toLowerCase()))
      );
    }

    if (effectiveBefore) {
      filteredFrameworks = filteredFrameworks.filter(framework =>
        framework.effectiveDate && framework.effectiveDate <= effectiveBefore
      );
    }

    if (effectiveAfter) {
      filteredFrameworks = filteredFrameworks.filter(framework =>
        framework.effectiveDate && framework.effectiveDate >= effectiveAfter
      );
    }

    if (expirationBefore) {
      filteredFrameworks = filteredFrameworks.filter(framework =>
        framework.expirationDate && framework.expirationDate <= expirationBefore
      );
    }

    if (expirationAfter) {
      filteredFrameworks = filteredFrameworks.filter(framework =>
        framework.expirationDate && framework.expirationDate >= expirationAfter
      );
    }

    // Sort frameworks
    filteredFrameworks.sort((a, b) => {
      if (sortOrder.toLowerCase() === 'desc') {
        return a[sortBy] > b[sortBy] ? -1 : 1;
      }
      return a[sortBy] > b[sortBy] ? 1 : -1;
    });

    // Paginate results
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = pageNum * limitNum;
    const paginatedFrameworks = filteredFrameworks.slice(startIndex, endIndex);

    // Calculate pagination info
    const totalFrameworks = filteredFrameworks.length;
    const totalPages = Math.ceil(totalFrameworks / limitNum);

    res.json({
      data: paginatedFrameworks,
      pagination: {
        total: totalFrameworks,
        page: pageNum,
        limit: limitNum,
        pages: totalPages
      }
    });
  } catch (error) {
    console.error('Error in getFrameworks:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get a specific ESG framework by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getFrameworkById = (req, res) => {
  try {
    const { id } = req.params;
    const framework = models.esgFrameworks.find(f => f.id === id);

    if (!framework) {
      return res.status(404).json({
        error: 'Not Found',
        message: `ESG framework with ID ${id} not found`
      });
    }

    res.json({ data: framework });
  } catch (error) {
    console.error('Error in getFrameworkById:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Create a new ESG framework
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const createFramework = (req, res) => {
  try {
    const {
      code,
      name,
      description,
      version,
      organization,
      website,
      category,
      industry,
      region,
      isActive,
      effectiveDate,
      expirationDate,
      regulatoryStatus,
      applicableRegulations,
      maturityLevel,
      adoptionRate,
      implementationCost,
      implementationComplexity,
      updateFrequency,
      lastUpdatedBy,
      tags
    } = req.body;

    // Check if a framework with the same code already exists
    const existingFramework = models.esgFrameworks.find(f => f.code === code);
    if (existingFramework) {
      return res.status(400).json({
        error: 'Bad Request',
        message: `A framework with code '${code}' already exists`
      });
    }

    // Validate regulatory status if provided
    if (regulatoryStatus && !['mandatory', 'voluntary', 'recommended'].includes(regulatoryStatus)) {
      return res.status(400).json({
        error: 'Bad Request',
        message: `Invalid regulatory status: ${regulatoryStatus}. Must be one of: mandatory, voluntary, recommended`
      });
    }

    // Validate maturity level if provided
    if (maturityLevel && !['emerging', 'established', 'mature', 'legacy'].includes(maturityLevel)) {
      return res.status(400).json({
        error: 'Bad Request',
        message: `Invalid maturity level: ${maturityLevel}. Must be one of: emerging, established, mature, legacy`
      });
    }

    // Validate implementation cost if provided
    if (implementationCost && !['low', 'medium', 'high'].includes(implementationCost)) {
      return res.status(400).json({
        error: 'Bad Request',
        message: `Invalid implementation cost: ${implementationCost}. Must be one of: low, medium, high`
      });
    }

    // Validate implementation complexity if provided
    if (implementationComplexity && !['low', 'medium', 'high'].includes(implementationComplexity)) {
      return res.status(400).json({
        error: 'Bad Request',
        message: `Invalid implementation complexity: ${implementationComplexity}. Must be one of: low, medium, high`
      });
    }

    // Create a new framework with a unique ID
    const newFramework = {
      id: `frm-${uuidv4().substring(0, 8)}`,
      code,
      name,
      description,
      version,
      organization,
      website: website || null,
      category,
      industry: industry || null,
      region: region || null,
      isActive: isActive !== undefined ? isActive : true,
      effectiveDate: effectiveDate || null,
      expirationDate: expirationDate || null,
      regulatoryStatus: regulatoryStatus || 'voluntary',
      applicableRegulations: applicableRegulations || [],
      maturityLevel: maturityLevel || null,
      adoptionRate: adoptionRate || null,
      implementationCost: implementationCost || null,
      implementationComplexity: implementationComplexity || null,
      updateFrequency: updateFrequency || null,
      lastUpdatedBy: lastUpdatedBy || organization,
      tags: tags || [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // Add the new framework to the collection
    models.esgFrameworks.push(newFramework);

    res.status(201).json({
      data: newFramework,
      message: 'ESG framework created successfully'
    });
  } catch (error) {
    console.error('Error in createFramework:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Update an existing ESG framework
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const updateFramework = (req, res) => {
  try {
    const { id } = req.params;
    const {
      code,
      name,
      description,
      version,
      organization,
      website,
      category,
      industry,
      region,
      isActive,
      effectiveDate,
      expirationDate,
      regulatoryStatus,
      applicableRegulations,
      maturityLevel,
      adoptionRate,
      implementationCost,
      implementationComplexity,
      updateFrequency,
      lastUpdatedBy,
      tags
    } = req.body;

    // Find the framework to update
    const frameworkIndex = models.esgFrameworks.findIndex(f => f.id === id);

    if (frameworkIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `ESG framework with ID ${id} not found`
      });
    }

    // If code is being updated, check if it conflicts with an existing framework
    if (code && code !== models.esgFrameworks[frameworkIndex].code) {
      const existingFramework = models.esgFrameworks.find(f => f.code === code && f.id !== id);
      if (existingFramework) {
        return res.status(400).json({
          error: 'Bad Request',
          message: `A framework with code '${code}' already exists`
        });
      }
    }

    // Validate regulatory status if provided
    if (regulatoryStatus && !['mandatory', 'voluntary', 'recommended'].includes(regulatoryStatus)) {
      return res.status(400).json({
        error: 'Bad Request',
        message: `Invalid regulatory status: ${regulatoryStatus}. Must be one of: mandatory, voluntary, recommended`
      });
    }

    // Validate maturity level if provided
    if (maturityLevel && !['emerging', 'established', 'mature', 'legacy'].includes(maturityLevel)) {
      return res.status(400).json({
        error: 'Bad Request',
        message: `Invalid maturity level: ${maturityLevel}. Must be one of: emerging, established, mature, legacy`
      });
    }

    // Validate implementation cost if provided
    if (implementationCost && !['low', 'medium', 'high'].includes(implementationCost)) {
      return res.status(400).json({
        error: 'Bad Request',
        message: `Invalid implementation cost: ${implementationCost}. Must be one of: low, medium, high`
      });
    }

    // Validate implementation complexity if provided
    if (implementationComplexity && !['low', 'medium', 'high'].includes(implementationComplexity)) {
      return res.status(400).json({
        error: 'Bad Request',
        message: `Invalid implementation complexity: ${implementationComplexity}. Must be one of: low, medium, high`
      });
    }

    // Update the framework
    const currentFramework = models.esgFrameworks[frameworkIndex];
    const updatedFramework = {
      ...currentFramework,
      code: code || currentFramework.code,
      name: name || currentFramework.name,
      description: description || currentFramework.description,
      version: version || currentFramework.version,
      organization: organization || currentFramework.organization,
      website: website !== undefined ? website : currentFramework.website,
      category: category || currentFramework.category,
      industry: industry !== undefined ? industry : currentFramework.industry,
      region: region !== undefined ? region : currentFramework.region,
      isActive: isActive !== undefined ? isActive : currentFramework.isActive,
      effectiveDate: effectiveDate !== undefined ? effectiveDate : currentFramework.effectiveDate,
      expirationDate: expirationDate !== undefined ? expirationDate : currentFramework.expirationDate,
      regulatoryStatus: regulatoryStatus || currentFramework.regulatoryStatus,
      applicableRegulations: applicableRegulations !== undefined ? applicableRegulations : currentFramework.applicableRegulations || [],
      maturityLevel: maturityLevel || currentFramework.maturityLevel,
      adoptionRate: adoptionRate !== undefined ? adoptionRate : currentFramework.adoptionRate,
      implementationCost: implementationCost || currentFramework.implementationCost,
      implementationComplexity: implementationComplexity || currentFramework.implementationComplexity,
      updateFrequency: updateFrequency !== undefined ? updateFrequency : currentFramework.updateFrequency,
      lastUpdatedBy: lastUpdatedBy || currentFramework.lastUpdatedBy,
      tags: tags !== undefined ? tags : currentFramework.tags || [],
      updatedAt: new Date().toISOString()
    };

    // Replace the old framework with the updated one
    models.esgFrameworks[frameworkIndex] = updatedFramework;

    res.json({
      data: updatedFramework,
      message: 'ESG framework updated successfully'
    });
  } catch (error) {
    console.error('Error in updateFramework:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message,
      code: error.code || 'UNKNOWN_ERROR'
    });
  }
};

/**
 * Delete an ESG framework
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const deleteFramework = (req, res) => {
  try {
    const { id } = req.params;

    // Find the framework to delete
    const frameworkIndex = models.esgFrameworks.findIndex(f => f.id === id);

    if (frameworkIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `ESG framework with ID ${id} not found`
      });
    }

    // Check if there are any elements associated with this framework
    const hasElements = models.frameworkElements.some(e => e.frameworkId === id);

    if (hasElements) {
      return res.status(400).json({
        error: 'Bad Request',
        message: `Cannot delete framework with ID ${id} because it has associated elements`
      });
    }

    // Check if there are any mappings associated with this framework
    const hasMappings = models.frameworkMappings.some(m =>
      m.sourceFrameworkId === id || m.targetFrameworkId === id
    );

    if (hasMappings) {
      return res.status(400).json({
        error: 'Bad Request',
        message: `Cannot delete framework with ID ${id} because it has associated mappings`
      });
    }

    // Remove the framework from the collection
    models.esgFrameworks.splice(frameworkIndex, 1);

    res.json({
      message: 'ESG framework deleted successfully'
    });
  } catch (error) {
    console.error('Error in deleteFramework:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get framework categories
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getFrameworkCategories = (req, res) => {
  try {
    res.json({ data: models.frameworkCategories });
  } catch (error) {
    console.error('Error in getFrameworkCategories:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get elements for a specific framework
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getFrameworkElements = (req, res) => {
  try {
    const { id } = req.params;
    const { page = 1, limit = 10, level, parentId, sortBy = 'code', sortOrder = 'asc' } = req.query;

    // Check if the framework exists
    const framework = models.esgFrameworks.find(f => f.id === id);

    if (!framework) {
      return res.status(404).json({
        error: 'Not Found',
        message: `ESG framework with ID ${id} not found`
      });
    }

    // Convert page and limit to numbers
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);

    // Filter elements based on query parameters
    let filteredElements = models.frameworkElements.filter(element => element.frameworkId === id);

    if (level !== undefined) {
      const levelNum = parseInt(level, 10);
      filteredElements = filteredElements.filter(element => element.level === levelNum);
    }

    if (parentId !== undefined) {
      if (parentId === 'null') {
        filteredElements = filteredElements.filter(element => element.parentId === null);
      } else {
        filteredElements = filteredElements.filter(element => element.parentId === parentId);
      }
    }

    // Sort elements
    filteredElements.sort((a, b) => {
      if (sortOrder.toLowerCase() === 'desc') {
        return a[sortBy] > b[sortBy] ? -1 : 1;
      }
      return a[sortBy] > b[sortBy] ? 1 : -1;
    });

    // Paginate results
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = pageNum * limitNum;
    const paginatedElements = filteredElements.slice(startIndex, endIndex);

    // Calculate pagination info
    const totalElements = filteredElements.length;
    const totalPages = Math.ceil(totalElements / limitNum);

    res.json({
      data: paginatedElements,
      pagination: {
        total: totalElements,
        page: pageNum,
        limit: limitNum,
        pages: totalPages
      }
    });
  } catch (error) {
    console.error('Error in getFrameworkElements:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get a specific framework element by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getElementById = (req, res) => {
  try {
    const { id } = req.params;
    const element = models.frameworkElements.find(e => e.id === id);

    if (!element) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Framework element with ID ${id} not found`
      });
    }

    res.json({ data: element });
  } catch (error) {
    console.error('Error in getElementById:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Create a new framework element
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const createElement = (req, res) => {
  try {
    const { frameworkId } = req.params;
    const { code, name, description, category, parentId, level, metrics } = req.body;

    // Check if the framework exists
    const framework = models.esgFrameworks.find(f => f.id === frameworkId);

    if (!framework) {
      return res.status(404).json({
        error: 'Not Found',
        message: `ESG framework with ID ${frameworkId} not found`
      });
    }

    // Check if an element with the same code already exists in this framework
    const existingElement = models.frameworkElements.find(e =>
      e.frameworkId === frameworkId && e.code === code
    );

    if (existingElement) {
      return res.status(400).json({
        error: 'Bad Request',
        message: `An element with code '${code}' already exists in this framework`
      });
    }

    // If parentId is provided, check if it exists and belongs to the same framework
    if (parentId) {
      const parentElement = models.frameworkElements.find(e => e.id === parentId);

      if (!parentElement) {
        return res.status(404).json({
          error: 'Not Found',
          message: `Parent element with ID ${parentId} not found`
        });
      }

      if (parentElement.frameworkId !== frameworkId) {
        return res.status(400).json({
          error: 'Bad Request',
          message: `Parent element with ID ${parentId} does not belong to the specified framework`
        });
      }
    }

    // Create a new element with a unique ID
    const newElement = {
      id: `ele-${uuidv4().substring(0, 8)}`,
      frameworkId,
      code,
      name,
      description,
      category: category || '',
      parentId: parentId || null,
      level,
      metrics: metrics || [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // Add the new element to the collection
    models.frameworkElements.push(newElement);

    res.status(201).json({
      data: newElement,
      message: 'Framework element created successfully'
    });
  } catch (error) {
    console.error('Error in createElement:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Update an existing framework element
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const updateElement = (req, res) => {
  try {
    const { id } = req.params;
    const { code, name, description, category, parentId, level, metrics } = req.body;

    // Find the element to update
    const elementIndex = models.frameworkElements.findIndex(e => e.id === id);

    if (elementIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Framework element with ID ${id} not found`
      });
    }

    const currentElement = models.frameworkElements[elementIndex];

    // If code is being updated, check if it conflicts with an existing element in the same framework
    if (code && code !== currentElement.code) {
      const existingElement = models.frameworkElements.find(e =>
        e.frameworkId === currentElement.frameworkId && e.code === code && e.id !== id
      );

      if (existingElement) {
        return res.status(400).json({
          error: 'Bad Request',
          message: `An element with code '${code}' already exists in this framework`
        });
      }
    }

    // If parentId is being updated, check if it exists and belongs to the same framework
    if (parentId && parentId !== currentElement.parentId) {
      // Check for circular reference
      if (parentId === id) {
        return res.status(400).json({
          error: 'Bad Request',
          message: 'An element cannot be its own parent'
        });
      }

      const parentElement = models.frameworkElements.find(e => e.id === parentId);

      if (!parentElement) {
        return res.status(404).json({
          error: 'Not Found',
          message: `Parent element with ID ${parentId} not found`
        });
      }

      if (parentElement.frameworkId !== currentElement.frameworkId) {
        return res.status(400).json({
          error: 'Bad Request',
          message: `Parent element with ID ${parentId} does not belong to the same framework`
        });
      }
    }

    // Update the element
    const updatedElement = {
      ...currentElement,
      code: code || currentElement.code,
      name: name || currentElement.name,
      description: description || currentElement.description,
      category: category !== undefined ? category : currentElement.category,
      parentId: parentId !== undefined ? parentId : currentElement.parentId,
      level: level || currentElement.level,
      metrics: metrics || currentElement.metrics,
      updatedAt: new Date().toISOString()
    };

    // Replace the old element with the updated one
    models.frameworkElements[elementIndex] = updatedElement;

    res.json({
      data: updatedElement,
      message: 'Framework element updated successfully'
    });
  } catch (error) {
    console.error('Error in updateElement:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Delete a framework element
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const deleteElement = (req, res) => {
  try {
    const { id } = req.params;

    // Find the element to delete
    const elementIndex = models.frameworkElements.findIndex(e => e.id === id);

    if (elementIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Framework element with ID ${id} not found`
      });
    }

    // Check if there are any child elements
    const hasChildren = models.frameworkElements.some(e => e.parentId === id);

    if (hasChildren) {
      return res.status(400).json({
        error: 'Bad Request',
        message: `Cannot delete element with ID ${id} because it has child elements`
      });
    }

    // Check if there are any mappings associated with this element
    const hasMappings = models.frameworkMappings.some(m =>
      m.sourceElementId === id || m.targetElementId === id
    );

    if (hasMappings) {
      return res.status(400).json({
        error: 'Bad Request',
        message: `Cannot delete element with ID ${id} because it has associated mappings`
      });
    }

    // Remove the element from the collection
    models.frameworkElements.splice(elementIndex, 1);

    res.json({
      message: 'Framework element deleted successfully'
    });
  } catch (error) {
    console.error('Error in deleteElement:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get framework mappings
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getMappings = (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      sourceFrameworkId,
      targetFrameworkId,
      mappingType,
      confidenceLevel,
      mappingMethod,
      verifiedBy,
      validFromBefore,
      validFromAfter,
      validToBefore,
      validToAfter,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    // Convert page and limit to numbers
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);

    // Filter mappings based on query parameters
    let filteredMappings = [...models.frameworkMappings];

    if (sourceFrameworkId) {
      filteredMappings = filteredMappings.filter(mapping => mapping.sourceFrameworkId === sourceFrameworkId);
    }

    if (targetFrameworkId) {
      filteredMappings = filteredMappings.filter(mapping => mapping.targetFrameworkId === targetFrameworkId);
    }

    if (mappingType) {
      filteredMappings = filteredMappings.filter(mapping => mapping.mappingType === mappingType);
    }

    if (confidenceLevel) {
      filteredMappings = filteredMappings.filter(mapping => mapping.confidenceLevel === confidenceLevel);
    }

    if (mappingMethod) {
      filteredMappings = filteredMappings.filter(mapping => mapping.mappingMethod === mappingMethod);
    }

    if (verifiedBy) {
      filteredMappings = filteredMappings.filter(mapping =>
        mapping.verifiedBy && mapping.verifiedBy.toLowerCase().includes(verifiedBy.toLowerCase())
      );
    }

    if (validFromBefore) {
      filteredMappings = filteredMappings.filter(mapping =>
        mapping.validFrom && mapping.validFrom <= validFromBefore
      );
    }

    if (validFromAfter) {
      filteredMappings = filteredMappings.filter(mapping =>
        mapping.validFrom && mapping.validFrom >= validFromAfter
      );
    }

    if (validToBefore) {
      filteredMappings = filteredMappings.filter(mapping =>
        mapping.validTo && mapping.validTo <= validToBefore
      );
    }

    if (validToAfter) {
      filteredMappings = filteredMappings.filter(mapping =>
        mapping.validTo && mapping.validTo >= validToAfter
      );
    }

    // Sort mappings
    filteredMappings.sort((a, b) => {
      if (sortOrder.toLowerCase() === 'desc') {
        return a[sortBy] > b[sortBy] ? -1 : 1;
      }
      return a[sortBy] > b[sortBy] ? 1 : -1;
    });

    // Paginate results
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = pageNum * limitNum;
    const paginatedMappings = filteredMappings.slice(startIndex, endIndex);

    // Calculate pagination info
    const totalMappings = filteredMappings.length;
    const totalPages = Math.ceil(totalMappings / limitNum);

    res.json({
      data: paginatedMappings,
      pagination: {
        total: totalMappings,
        page: pageNum,
        limit: limitNum,
        pages: totalPages
      }
    });
  } catch (error) {
    console.error('Error in getMappings:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get a specific framework mapping by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getMappingById = (req, res) => {
  try {
    const { id } = req.params;
    const mapping = models.frameworkMappings.find(m => m.id === id);

    if (!mapping) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Framework mapping with ID ${id} not found`
      });
    }

    res.json({ data: mapping });
  } catch (error) {
    console.error('Error in getMappingById:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Create a new framework mapping
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const createMapping = (req, res) => {
  try {
    const {
      sourceFrameworkId,
      sourceElementId,
      targetFrameworkId,
      targetElementId,
      mappingType,
      confidenceLevel,
      mappingMethod,
      mappingVersion,
      validFrom,
      validTo,
      verifiedBy,
      verifiedAt,
      notes
    } = req.body;

    // Check if the source framework exists
    const sourceFramework = models.esgFrameworks.find(f => f.id === sourceFrameworkId);
    if (!sourceFramework) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Source framework with ID ${sourceFrameworkId} not found`
      });
    }

    // Check if the target framework exists
    const targetFramework = models.esgFrameworks.find(f => f.id === targetFrameworkId);
    if (!targetFramework) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Target framework with ID ${targetFrameworkId} not found`
      });
    }

    // Check if the source element exists and belongs to the source framework
    const sourceElement = models.frameworkElements.find(e => e.id === sourceElementId);
    if (!sourceElement) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Source element with ID ${sourceElementId} not found`
      });
    }
    if (sourceElement.frameworkId !== sourceFrameworkId) {
      return res.status(400).json({
        error: 'Bad Request',
        message: `Source element with ID ${sourceElementId} does not belong to the specified source framework`
      });
    }

    // Check if the target element exists and belongs to the target framework
    const targetElement = models.frameworkElements.find(e => e.id === targetElementId);
    if (!targetElement) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Target element with ID ${targetElementId} not found`
      });
    }
    if (targetElement.frameworkId !== targetFrameworkId) {
      return res.status(400).json({
        error: 'Bad Request',
        message: `Target element with ID ${targetElementId} does not belong to the specified target framework`
      });
    }

    // Check if a mapping between these elements already exists
    const existingMapping = models.frameworkMappings.find(m =>
      m.sourceFrameworkId === sourceFrameworkId &&
      m.sourceElementId === sourceElementId &&
      m.targetFrameworkId === targetFrameworkId &&
      m.targetElementId === targetElementId
    );

    if (existingMapping) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'A mapping between these elements already exists'
      });
    }

    // Create a new mapping with a unique ID
    const newMapping = {
      id: `map-${uuidv4().substring(0, 8)}`,
      sourceFrameworkId,
      sourceElementId,
      targetFrameworkId,
      targetElementId,
      mappingType,
      confidenceLevel: confidenceLevel || 'medium',
      mappingMethod: mappingMethod || 'manual',
      mappingVersion: mappingVersion || '1.0',
      validFrom: validFrom || new Date().toISOString().split('T')[0], // Current date in YYYY-MM-DD format
      validTo: validTo || null,
      verifiedBy: verifiedBy || null,
      verifiedAt: verifiedAt || null,
      notes: notes || '',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // Add the new mapping to the collection
    models.frameworkMappings.push(newMapping);

    res.status(201).json({
      data: newMapping,
      message: 'Framework mapping created successfully'
    });
  } catch (error) {
    console.error('Error in createMapping:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Update an existing framework mapping
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const updateMapping = (req, res) => {
  try {
    const { id } = req.params;
    const {
      mappingType,
      confidenceLevel,
      mappingMethod,
      mappingVersion,
      validFrom,
      validTo,
      verifiedBy,
      verifiedAt,
      notes
    } = req.body;

    // Find the mapping to update
    const mappingIndex = models.frameworkMappings.findIndex(m => m.id === id);

    if (mappingIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Framework mapping with ID ${id} not found`
      });
    }

    // Update the mapping
    const currentMapping = models.frameworkMappings[mappingIndex];
    const updatedMapping = {
      ...currentMapping,
      mappingType: mappingType || currentMapping.mappingType,
      confidenceLevel: confidenceLevel || currentMapping.confidenceLevel,
      mappingMethod: mappingMethod || currentMapping.mappingMethod,
      mappingVersion: mappingVersion || currentMapping.mappingVersion,
      validFrom: validFrom || currentMapping.validFrom,
      validTo: validTo !== undefined ? validTo : currentMapping.validTo,
      verifiedBy: verifiedBy !== undefined ? verifiedBy : currentMapping.verifiedBy,
      verifiedAt: verifiedAt !== undefined ? verifiedAt : currentMapping.verifiedAt,
      notes: notes !== undefined ? notes : currentMapping.notes,
      updatedAt: new Date().toISOString()
    };

    // Replace the old mapping with the updated one
    models.frameworkMappings[mappingIndex] = updatedMapping;

    res.json({
      data: updatedMapping,
      message: 'Framework mapping updated successfully'
    });
  } catch (error) {
    console.error('Error in updateMapping:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Delete a framework mapping
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const deleteMapping = (req, res) => {
  try {
    const { id } = req.params;

    // Find the mapping to delete
    const mappingIndex = models.frameworkMappings.findIndex(m => m.id === id);

    if (mappingIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Framework mapping with ID ${id} not found`
      });
    }

    // Remove the mapping from the collection
    models.frameworkMappings.splice(mappingIndex, 1);

    res.json({
      message: 'Framework mapping deleted successfully'
    });
  } catch (error) {
    console.error('Error in deleteMapping:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

module.exports = {
  getFrameworks,
  getFrameworkById,
  createFramework,
  updateFramework,
  deleteFramework,
  getFrameworkCategories,
  getFrameworkElements,
  getElementById,
  createElement,
  updateElement,
  deleteElement,
  getMappings,
  getMappingById,
  createMapping,
  updateMapping,
  deleteMapping
/**
 * Compare two frameworks and generate a crosswalk analysis
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const compareFrameworks = (req, res) => {
  try {
    const { sourceFrameworkId, targetFrameworkId } = req.params;

    // Check if both frameworks exist
    const sourceFramework = models.esgFrameworks.find(f => f.id === sourceFrameworkId);
    if (!sourceFramework) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Source framework with ID ${sourceFrameworkId} not found`
      });
    }

    const targetFramework = models.esgFrameworks.find(f => f.id === targetFrameworkId);
    if (!targetFramework) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Target framework with ID ${targetFrameworkId} not found`
      });
    }

    // Get all elements for both frameworks
    const sourceElements = models.frameworkElements.filter(e => e.frameworkId === sourceFrameworkId);
    const targetElements = models.frameworkElements.filter(e => e.frameworkId === targetFrameworkId);

    // Get all mappings between these frameworks
    const mappings = models.frameworkMappings.filter(m =>
      (m.sourceFrameworkId === sourceFrameworkId && m.targetFrameworkId === targetFrameworkId) ||
      (m.sourceFrameworkId === targetFrameworkId && m.targetFrameworkId === sourceFrameworkId)
    );

    // Calculate coverage statistics
    const sourceMappedElements = new Set(mappings
      .filter(m => m.sourceFrameworkId === sourceFrameworkId)
      .map(m => m.sourceElementId));

    const targetMappedElements = new Set(mappings
      .filter(m => m.targetFrameworkId === targetFrameworkId)
      .map(m => m.targetElementId));

    const sourceCoverage = sourceElements.length > 0 ?
      (sourceMappedElements.size / sourceElements.length) * 100 : 0;

    const targetCoverage = targetElements.length > 0 ?
      (targetMappedElements.size / targetElements.length) * 100 : 0;

    // Group mappings by type
    const mappingsByType = {};
    mappings.forEach(mapping => {
      if (!mappingsByType[mapping.mappingType]) {
        mappingsByType[mapping.mappingType] = [];
      }
      mappingsByType[mapping.mappingType].push(mapping);
    });

    // Generate gap analysis
    const sourceGaps = sourceElements
      .filter(element => !sourceMappedElements.has(element.id))
      .map(element => ({
        id: element.id,
        code: element.code,
        name: element.name,
        description: element.description
      }));

    const targetGaps = targetElements
      .filter(element => !targetMappedElements.has(element.id))
      .map(element => ({
        id: element.id,
        code: element.code,
        name: element.name,
        description: element.description
      }));

    // Generate comparison result
    const comparison = {
      sourceFramework: {
        id: sourceFramework.id,
        code: sourceFramework.code,
        name: sourceFramework.name,
        version: sourceFramework.version,
        elementCount: sourceElements.length,
        mappedElementCount: sourceMappedElements.size,
        coverage: parseFloat(sourceCoverage.toFixed(2))
      },
      targetFramework: {
        id: targetFramework.id,
        code: targetFramework.code,
        name: targetFramework.name,
        version: targetFramework.version,
        elementCount: targetElements.length,
        mappedElementCount: targetMappedElements.size,
        coverage: parseFloat(targetCoverage.toFixed(2))
      },
      mappingSummary: {
        totalMappings: mappings.length,
        byType: Object.keys(mappingsByType).map(type => ({
          type,
          count: mappingsByType[type].length
        })),
        averageConfidence: mappings.length > 0 ?
          parseFloat((mappings
            .map(m => m.confidenceLevel === 'high' ? 3 : m.confidenceLevel === 'medium' ? 2 : 1)
            .reduce((sum, val) => sum + val, 0) / mappings.length).toFixed(2)) : 0
      },
      gapAnalysis: {
        sourceGaps: {
          count: sourceGaps.length,
          elements: sourceGaps
        },
        targetGaps: {
          count: targetGaps.length,
          elements: targetGaps
        }
      },
      recommendations: [
        {
          type: 'gap_filling',
          description: `Create mappings for the ${sourceGaps.length} unmapped elements in the source framework`,
          priority: sourceGaps.length > 0 ? 'high' : 'low'
        },
        {
          type: 'verification',
          description: `Review ${mappings.filter(m => m.confidenceLevel === 'low').length} low-confidence mappings`,
          priority: mappings.filter(m => m.confidenceLevel === 'low').length > 0 ? 'high' : 'low'
        }
      ]
    };

    res.json({
      data: comparison
    });
  } catch (error) {
    console.error('Error in compareFrameworks:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message,
      code: error.code || 'UNKNOWN_ERROR'
    });
  }
};

module.exports = {
  getFrameworks,
  getFrameworkById,
  createFramework,
  updateFramework,
  deleteFramework,
  getFrameworkCategories,
  getFrameworkElements,
  getElementById,
  createElement,
  updateElement,
  deleteElement,
  getMappings,
  getMappingById,
  createMapping,
  updateMapping,
  deleteMapping,
  compareFrameworks
};