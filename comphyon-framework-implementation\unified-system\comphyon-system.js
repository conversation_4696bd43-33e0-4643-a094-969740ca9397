/**
 * Comphyon System
 * 
 * This module implements the unified Comphyon System that integrates all components.
 * It provides a complete implementation of the Comphyology framework.
 */

const { EventEmitter } = require('events');
const { performance } = require('perf_hooks');

// Import all components
const { createEnhancedCSDE } = require('../csde');
const { createEnhancedCSFE } = require('../csfe');
const { createEnhancedCSME } = require('../csme');
const { createEnhancedBridgeSystem } = require('../bridge');
const { createEnhancedMeterSystem } = require('../meter');
const { createEnhancedGovernorSystem } = require('../governor');
const { createEnhancedDashboardSystem } = require('../dashboard');

/**
 * ComphyonSystem class
 */
class ComphyonSystem extends EventEmitter {
  /**
   * Create a new ComphyonSystem instance
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      enableLogging: true,
      enableMetrics: true,
      updateInterval: 5000, // ms
      novaVision: null, // NovaVision instance
      ...options
    };
    
    // Initialize state
    this.state = {
      isInitialized: false,
      isRunning: false,
      components: {
        csde: null,
        csfe: null,
        csme: null,
        bridge: null,
        meter: null,
        governor: null,
        dashboard: null
      },
      lastUpdateTime: Date.now()
    };
    
    // Initialize metrics
    this.metrics = {
      processingTimeMs: 0,
      updatesProcessed: 0,
      systemUptime: 0,
      startTime: 0,
      componentMetrics: {}
    };
    
    if (this.options.enableLogging) {
      console.log('ComphyonSystem initialized');
    }
  }
  
  /**
   * Initialize the system
   * @returns {boolean} - Success status
   */
  async initialize() {
    if (this.state.isInitialized) {
      if (this.options.enableLogging) {
        console.log('ComphyonSystem is already initialized');
      }
      return false;
    }
    
    const startTime = performance.now();
    
    try {
      if (this.options.enableLogging) {
        console.log('Initializing ComphyonSystem...');
      }
      
      // Initialize CSDE
      this.state.components.csde = createEnhancedCSDE({
        enableLogging: this.options.enableLogging,
        enableMetrics: this.options.enableMetrics
      });
      
      // Initialize CSFE
      this.state.components.csfe = createEnhancedCSFE({
        enableLogging: this.options.enableLogging,
        enableMetrics: this.options.enableMetrics
      });
      
      // Initialize CSME
      this.state.components.csme = createEnhancedCSME({
        enableLogging: this.options.enableLogging,
        enableMetrics: this.options.enableMetrics
      });
      
      // Initialize Bridge
      this.state.components.bridge = createEnhancedBridgeSystem({
        enableLogging: this.options.enableLogging,
        enableMetrics: this.options.enableMetrics
      }, {
        csde: this.state.components.csde,
        csfe: this.state.components.csfe,
        csme: this.state.components.csme
      });
      
      // Initialize Meter
      this.state.components.meter = createEnhancedMeterSystem({
        enableLogging: this.options.enableLogging,
        enableMetrics: this.options.enableMetrics
      }, this.state.components.bridge);
      
      // Initialize Governor
      this.state.components.governor = createEnhancedGovernorSystem({
        enableLogging: this.options.enableLogging,
        enableMetrics: this.options.enableMetrics
      }, this.state.components.meter);
      
      // Initialize Dashboard
      this.state.components.dashboard = createEnhancedDashboardSystem({
        enableLogging: this.options.enableLogging,
        enableMetrics: this.options.enableMetrics,
        updateInterval: this.options.updateInterval,
        novaVision: this.options.novaVision
      }, this.state.components.meter, this.state.components.bridge, this.state.components.governor);
      
      // Set up event listeners
      this._setupEventListeners();
      
      // Update state
      this.state.isInitialized = true;
      this.state.lastUpdateTime = Date.now();
      
      // Update metrics
      this.metrics.processingTimeMs += performance.now() - startTime;
      
      if (this.options.enableLogging) {
        console.log('ComphyonSystem initialized successfully');
      }
      
      this.emit('initialize');
      
      return true;
    } catch (error) {
      if (this.options.enableLogging) {
        console.error('Failed to initialize ComphyonSystem:', error);
      }
      
      throw error;
    }
  }
  
  /**
   * Start the system
   * @returns {boolean} - Success status
   */
  async start() {
    if (!this.state.isInitialized) {
      throw new Error('ComphyonSystem is not initialized');
    }
    
    if (this.state.isRunning) {
      if (this.options.enableLogging) {
        console.log('ComphyonSystem is already running');
      }
      return false;
    }
    
    const startTime = performance.now();
    
    try {
      if (this.options.enableLogging) {
        console.log('Starting ComphyonSystem...');
      }
      
      // Start all components
      await Promise.all([
        this.state.components.csde.start(),
        this.state.components.csfe.start(),
        this.state.components.csme.start(),
        this.state.components.bridge.start(),
        this.state.components.meter.start(),
        this.state.components.governor.start(),
        this.state.components.dashboard.start()
      ]);
      
      // Start update interval
      this._startUpdateInterval();
      
      // Update state
      this.state.isRunning = true;
      this.state.lastUpdateTime = Date.now();
      
      // Update metrics
      this.metrics.processingTimeMs += performance.now() - startTime;
      this.metrics.startTime = Date.now();
      
      if (this.options.enableLogging) {
        console.log('ComphyonSystem started successfully');
      }
      
      this.emit('start');
      
      return true;
    } catch (error) {
      if (this.options.enableLogging) {
        console.error('Failed to start ComphyonSystem:', error);
      }
      
      throw error;
    }
  }
  
  /**
   * Stop the system
   * @returns {boolean} - Success status
   */
  stop() {
    if (!this.state.isRunning) {
      if (this.options.enableLogging) {
        console.log('ComphyonSystem is not running');
      }
      return false;
    }
    
    const startTime = performance.now();
    
    try {
      if (this.options.enableLogging) {
        console.log('Stopping ComphyonSystem...');
      }
      
      // Stop update interval
      this._stopUpdateInterval();
      
      // Stop all components
      this.state.components.dashboard.stop();
      this.state.components.governor.stop();
      this.state.components.meter.stop();
      this.state.components.bridge.stop();
      this.state.components.csme.stop();
      this.state.components.csfe.stop();
      this.state.components.csde.stop();
      
      // Update state
      this.state.isRunning = false;
      this.state.lastUpdateTime = Date.now();
      
      // Update metrics
      this.metrics.processingTimeMs += performance.now() - startTime;
      this.metrics.systemUptime += Date.now() - this.metrics.startTime;
      
      if (this.options.enableLogging) {
        console.log('ComphyonSystem stopped successfully');
      }
      
      this.emit('stop');
      
      return true;
    } catch (error) {
      if (this.options.enableLogging) {
        console.error('Failed to stop ComphyonSystem:', error);
      }
      
      throw error;
    }
  }
  
  /**
   * Process data
   * @param {string} domain - Domain (cyber, financial, biological)
   * @param {string} key - Data key
   * @param {*} value - Data value
   * @param {Object} metadata - Additional metadata
   * @returns {Object} - Processing result
   */
  processData(domain, key, value, metadata = {}) {
    if (!this.state.isRunning) {
      throw new Error('ComphyonSystem is not running');
    }
    
    const startTime = performance.now();
    
    try {
      // Process data in Bridge
      const result = this.state.components.bridge.processDomainData(domain, key, value, {
        ...metadata,
        source: metadata.source || 'comphyon-system',
        timestamp: metadata.timestamp || Date.now()
      });
      
      // Update metrics
      this.metrics.processingTimeMs += performance.now() - startTime;
      this.metrics.updatesProcessed++;
      
      return result;
    } catch (error) {
      if (this.options.enableLogging) {
        console.error(`Failed to process data (${domain}.${key}):`, error);
      }
      
      throw error;
    }
  }
  
  /**
   * Process event
   * @param {string} domain - Domain (cyber, financial, biological)
   * @param {string} eventType - Event type
   * @param {Object} eventData - Event data
   * @param {Object} metadata - Additional metadata
   * @returns {Object} - Processing result
   */
  processEvent(domain, eventType, eventData, metadata = {}) {
    if (!this.state.isRunning) {
      throw new Error('ComphyonSystem is not running');
    }
    
    const startTime = performance.now();
    
    try {
      // Process event in Bridge
      const result = this.state.components.bridge.processDomainEvent(domain, eventType, eventData, {
        ...metadata,
        source: metadata.source || 'comphyon-system',
        timestamp: metadata.timestamp || Date.now()
      });
      
      // Update metrics
      this.metrics.processingTimeMs += performance.now() - startTime;
      this.metrics.updatesProcessed++;
      
      return result;
    } catch (error) {
      if (this.options.enableLogging) {
        console.error(`Failed to process event (${domain}.${eventType}):`, error);
      }
      
      throw error;
    }
  }
  
  /**
   * Execute action
   * @param {string} actionId - Action ID
   * @param {Object} parameters - Action parameters
   * @param {Object} context - Execution context
   * @returns {string} - Execution ID
   */
  executeAction(actionId, parameters, context = {}) {
    if (!this.state.isRunning) {
      throw new Error('ComphyonSystem is not running');
    }
    
    const startTime = performance.now();
    
    try {
      // Execute action in Governor
      const executionId = this.state.components.governor.executeAction(actionId, parameters, {
        ...context,
        source: context.source || 'comphyon-system',
        timestamp: context.timestamp || Date.now()
      });
      
      // Update metrics
      this.metrics.processingTimeMs += performance.now() - startTime;
      
      return executionId;
    } catch (error) {
      if (this.options.enableLogging) {
        console.error(`Failed to execute action (${actionId}):`, error);
      }
      
      throw error;
    }
  }
  
  /**
   * Render dashboard
   * @param {string} dashboardType - Dashboard type
   * @param {Object} target - Target element
   * @returns {boolean} - Success status
   */
  renderDashboard(dashboardType, target) {
    if (!this.state.isRunning) {
      throw new Error('ComphyonSystem is not running');
    }
    
    const startTime = performance.now();
    
    try {
      // Render dashboard
      let result;
      
      switch (dashboardType) {
        case 'universal-entropy':
          result = this.state.components.dashboard.renderUniversalEntropyDashboard(target);
          break;
        case 'cross-domain-risk':
          result = this.state.components.dashboard.renderCrossDomainRiskDashboard(target);
          break;
        default:
          throw new Error(`Unknown dashboard type: ${dashboardType}`);
      }
      
      // Update metrics
      this.metrics.processingTimeMs += performance.now() - startTime;
      
      return result;
    } catch (error) {
      if (this.options.enableLogging) {
        console.error(`Failed to render dashboard (${dashboardType}):`, error);
      }
      
      throw error;
    }
  }
  
  /**
   * Get system state
   * @returns {Object} - System state
   */
  getSystemState() {
    return {
      isInitialized: this.state.isInitialized,
      isRunning: this.state.isRunning,
      lastUpdateTime: this.state.lastUpdateTime,
      components: {
        csde: this.state.components.csde ? {
          isRunning: this.state.components.csde.isRunning
        } : null,
        csfe: this.state.components.csfe ? {
          isRunning: this.state.components.csfe.isRunning
        } : null,
        csme: this.state.components.csme ? {
          isRunning: this.state.components.csme.isRunning
        } : null,
        bridge: this.state.components.bridge ? {
          isRunning: this.state.components.bridge.isRunning
        } : null,
        meter: this.state.components.meter ? {
          isRunning: this.state.components.meter.isRunning
        } : null,
        governor: this.state.components.governor ? {
          isRunning: this.state.components.governor.isRunning
        } : null,
        dashboard: this.state.components.dashboard ? {
          isRunning: this.state.components.dashboard.isRunning
        } : null
      }
    };
  }
  
  /**
   * Get metrics
   * @returns {Object} - Metrics
   */
  getMetrics() {
    // Update component metrics
    this.metrics.componentMetrics = {
      csde: this.state.components.csde ? this.state.components.csde.getMetrics() : null,
      csfe: this.state.components.csfe ? this.state.components.csfe.getMetrics() : null,
      csme: this.state.components.csme ? this.state.components.csme.getMetrics() : null,
      bridge: this.state.components.bridge ? this.state.components.bridge.getMetrics() : null,
      meter: this.state.components.meter ? this.state.components.meter.getMetrics() : null,
      governor: this.state.components.governor ? this.state.components.governor.getMetrics() : null,
      dashboard: this.state.components.dashboard ? this.state.components.dashboard.getMetrics() : null
    };
    
    // Update system uptime
    if (this.state.isRunning) {
      this.metrics.systemUptime = Date.now() - this.metrics.startTime;
    }
    
    return { ...this.metrics };
  }
  
  /**
   * Get component
   * @param {string} componentName - Component name
   * @returns {Object} - Component
   */
  getComponent(componentName) {
    if (!this.state.components[componentName]) {
      throw new Error(`Component ${componentName} not found`);
    }
    
    return this.state.components[componentName];
  }
  
  /**
   * Start update interval
   * @private
   */
  _startUpdateInterval() {
    if (this._updateInterval) {
      clearInterval(this._updateInterval);
    }
    
    this._updateInterval = setInterval(() => {
      this.emit('update-interval');
    }, this.options.updateInterval);
  }
  
  /**
   * Stop update interval
   * @private
   */
  _stopUpdateInterval() {
    if (this._updateInterval) {
      clearInterval(this._updateInterval);
      this._updateInterval = null;
    }
  }
  
  /**
   * Set up event listeners
   * @private
   */
  _setupEventListeners() {
    // Listen for update interval
    this.on('update-interval', () => {
      // Update metrics
      this.getMetrics();
    });
  }
}

module.exports = ComphyonSystem;
