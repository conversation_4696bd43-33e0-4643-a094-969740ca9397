@echo off
echo Starting NovaFuse API Superstore...
cd /d D:\novafuse-api-superstore
docker-compose up -d
echo Waiting for services to start...
timeout /t 30
echo Setting up Kong API Gateway...
powershell -ExecutionPolicy Bypass -File setup-kong.ps1
echo NovaFuse API Superstore is ready!
echo.
echo Access the components:
echo - API Gateway: http://localhost:8000
echo - Kong Admin API: http://localhost:8001
echo - Konga Admin UI: http://localhost:1337
echo - Marketplace UI: http://localhost:3000
echo - Documentation Portal: http://localhost:8080
