package auth

// AuthSystem handles authentication and Q-Score validation
// Implements JWT, OAuth2, and biometric hooks (NovaDNA)
type AuthSystem struct {
	verifier Verifier
	castl    CASTLValidator
}

// NewAuthGate initializes the authentication system
func NewAuthGate() *AuthSystem {
	return &AuthSystem{
		verifier: NewVerifier(),
		castl:    NewCASTLValidator(),
	}
}

// ValidateRequest validates JWT and Q-Score compliance
func (a *AuthSystem) ValidateRequest(token string, qScore float64) bool {
	if !a.verifier.VerifyJWT(token) {
		return false
	}
	return a.castl.ValidateQScore(qScore)
}

// BiometricHook integrates NovaDNA for biometric auth
func (a *AuthSystem) BiometricHook(data []byte) bool {
	// TODO: Implement retina/voice auth
	return true
}
