/**
 * Framework Coverage Chart Component
 * 
 * This component displays a chart showing test coverage across different frameworks.
 */

import React from 'react';

interface FrameworkCoverageData {
  framework: string;
  totalControls: number;
  coveredControls: number;
  passRate: number;
}

interface FrameworkCoverageChartProps {
  data: FrameworkCoverageData[];
}

export const FrameworkCoverageChart: React.FC<FrameworkCoverageChartProps> = ({ data }) => {
  // If no data, show empty state
  if (!data || data.length === 0) {
    return (
      <div className="h-64 flex items-center justify-center text-gray-500">
        No framework coverage data available
      </div>
    );
  }
  
  return (
    <div className="space-y-6">
      {data.map((item, index) => {
        const coveragePercentage = Math.round((item.coveredControls / item.totalControls) * 100) || 0;
        
        return (
          <div key={index} className="space-y-2">
            <div className="flex justify-between items-center">
              <div>
                <div className="font-medium">{item.framework}</div>
                <div className="text-sm text-gray-500">
                  {item.coveredControls} of {item.totalControls} controls covered
                </div>
              </div>
              <div className="text-right">
                <div className="font-medium">{coveragePercentage}%</div>
                <div className="text-sm text-gray-500">Coverage</div>
              </div>
            </div>
            
            <div className="space-y-1">
              {/* Coverage bar */}
              <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                <div 
                  className="h-full bg-blue-600 rounded-full" 
                  style={{ width: `${coveragePercentage}%` }}
                />
              </div>
              
              {/* Pass rate bar */}
              <div className="flex items-center text-xs text-gray-500">
                <span className="mr-2">Pass Rate:</span>
                <div className="flex-grow h-1.5 bg-gray-200 rounded-full overflow-hidden">
                  <div 
                    className="h-full bg-green-500 rounded-full" 
                    style={{ width: `${item.passRate}%` }}
                  />
                </div>
                <span className="ml-2">{item.passRate}%</span>
              </div>
            </div>
            
            {/* Control details */}
            <div className="grid grid-cols-3 gap-2 text-center text-sm">
              <div className="p-2 bg-blue-50 rounded-md">
                <div className="font-medium text-blue-700">{item.totalControls}</div>
                <div className="text-xs text-blue-600">Total Controls</div>
              </div>
              <div className="p-2 bg-green-50 rounded-md">
                <div className="font-medium text-green-700">{item.coveredControls}</div>
                <div className="text-xs text-green-600">Covered</div>
              </div>
              <div className="p-2 bg-red-50 rounded-md">
                <div className="font-medium text-red-700">{item.totalControls - item.coveredControls}</div>
                <div className="text-xs text-red-600">Uncovered</div>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default FrameworkCoverageChart;
