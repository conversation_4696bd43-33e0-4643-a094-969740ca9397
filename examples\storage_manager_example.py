"""
Example of using the enhanced Storage Manager.

This example demonstrates how to use the enhanced Storage Manager with
versioning, encryption, access control, and audit logging.
"""

import os
import sys
import json
import uuid
import logging
import datetime

# Add the parent directory to the path so we can import the UCECS modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.ucecs.core.storage_manager import (
    StorageManager, 
    AccessLevel, 
    EncryptionType
)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """Run the Storage Manager example."""
    # Create a temporary directory for the example
    temp_dir = os.path.join(os.getcwd(), 'temp_storage_example')
    os.makedirs(temp_dir, exist_ok=True)
    
    # Create directories for the Storage Manager
    audit_log_dir = os.path.join(temp_dir, 'audit_logs')
    versions_dir = os.path.join(temp_dir, 'evidence_versions')
    access_control_dir = os.path.join(temp_dir, 'access_control')
    encryption_keys_dir = os.path.join(temp_dir, 'encryption_keys')
    
    # Create a Storage Manager
    storage_manager = StorageManager(
        audit_log_dir=audit_log_dir,
        versions_dir=versions_dir,
        access_control_dir=access_control_dir,
        encryption_keys_dir=encryption_keys_dir,
        enable_versioning=True,
        enable_encryption=True,
        enable_access_control=True,
        enable_audit_logging=True,
        default_encryption_type=EncryptionType.AES_256,
        max_versions_per_evidence=5,
        current_user_id="admin"
    )
    
    # Create a sample evidence item
    evidence_id = str(uuid.uuid4())
    evidence = {
        'id': evidence_id,
        'type': 'document',
        'source': 'user_upload',
        'data': {
            'title': 'Sample Document',
            'content': 'This is a sample document for testing the Storage Manager.',
            'created_at': datetime.datetime.now(datetime.timezone.utc).isoformat()
        },
        'metadata': {
            'tags': ['sample', 'document', 'test']
        }
    }
    
    try:
        # Store the evidence
        logger.info("Storing evidence...")
        storage_location = storage_manager.store(
            provider_id='file_system',
            evidence=evidence,
            user_id='admin',
            encrypt=True,
            encryption_type=EncryptionType.AES_256,
            create_version=True,
            version_comment='Initial version'
        )
        logger.info(f"Evidence stored at: {storage_location}")
        
        # Retrieve the evidence
        logger.info("Retrieving evidence...")
        retrieved_evidence = storage_manager.retrieve(
            provider_id='file_system',
            evidence_id=evidence_id,
            user_id='admin',
            decrypt=True
        )
        logger.info(f"Retrieved evidence: {json.dumps(retrieved_evidence, indent=2)}")
        
        # Update the evidence
        updated_evidence = retrieved_evidence.copy()
        updated_evidence['data']['content'] = 'This is an updated sample document.'
        updated_evidence['data']['updated_at'] = datetime.datetime.now(datetime.timezone.utc).isoformat()
        
        # Store the updated evidence
        logger.info("Storing updated evidence...")
        storage_location = storage_manager.store(
            provider_id='file_system',
            evidence=updated_evidence,
            user_id='admin',
            encrypt=True,
            create_version=True,
            version_comment='Updated content'
        )
        logger.info(f"Updated evidence stored at: {storage_location}")
        
        # Get the evidence versions
        logger.info("Getting evidence versions...")
        versions = storage_manager.get_evidence_versions(
            evidence_id=evidence_id,
            user_id='admin'
        )
        logger.info(f"Evidence versions: {json.dumps(versions, indent=2)}")
        
        # Create a new user
        user_id = 'user1'
        
        # Grant access to the user
        logger.info(f"Granting access to user {user_id}...")
        storage_manager.grant_access(
            evidence_id=evidence_id,
            user_id=user_id,
            access_level=AccessLevel.READ,
            granter_id='admin'
        )
        
        # Get access control information
        logger.info("Getting access control information...")
        access_control = storage_manager.get_access_control(
            evidence_id=evidence_id,
            user_id='admin'
        )
        logger.info(f"Access control: {json.dumps(access_control, indent=2)}")
        
        # Retrieve the evidence as the user
        logger.info(f"Retrieving evidence as user {user_id}...")
        user_retrieved_evidence = storage_manager.retrieve(
            provider_id='file_system',
            evidence_id=evidence_id,
            user_id=user_id,
            decrypt=True
        )
        logger.info(f"Evidence retrieved by user: {json.dumps(user_retrieved_evidence, indent=2)}")
        
        # Try to update the evidence as the user (should fail)
        try:
            logger.info(f"Trying to update evidence as user {user_id} (should fail)...")
            user_updated_evidence = user_retrieved_evidence.copy()
            user_updated_evidence['data']['content'] = 'This is an unauthorized update.'
            storage_manager.store(
                provider_id='file_system',
                evidence=user_updated_evidence,
                user_id=user_id,
                encrypt=True,
                create_version=True,
                version_comment='Unauthorized update'
            )
        except ValueError as e:
            logger.info(f"Expected error: {e}")
        
        # Upgrade the user's access level
        logger.info(f"Upgrading access level for user {user_id}...")
        storage_manager.grant_access(
            evidence_id=evidence_id,
            user_id=user_id,
            access_level=AccessLevel.WRITE,
            granter_id='admin'
        )
        
        # Update the evidence as the user (should succeed now)
        logger.info(f"Updating evidence as user {user_id} (should succeed now)...")
        user_updated_evidence = user_retrieved_evidence.copy()
        user_updated_evidence['data']['content'] = 'This is an authorized update by the user.'
        user_updated_evidence['data']['updated_at'] = datetime.datetime.now(datetime.timezone.utc).isoformat()
        storage_location = storage_manager.store(
            provider_id='file_system',
            evidence=user_updated_evidence,
            user_id=user_id,
            encrypt=True,
            create_version=True,
            version_comment='User update'
        )
        logger.info(f"User updated evidence stored at: {storage_location}")
        
        # Get the evidence versions again
        logger.info("Getting evidence versions after user update...")
        versions = storage_manager.get_evidence_versions(
            evidence_id=evidence_id,
            user_id='admin'
        )
        logger.info(f"Evidence versions: {json.dumps(versions, indent=2)}")
        
        # Restore an earlier version
        if len(versions) >= 2:
            earlier_version_id = versions[1]['version_id']  # Get the second version (index 1)
            logger.info(f"Restoring earlier version {earlier_version_id}...")
            storage_location = storage_manager.restore_evidence_version(
                evidence_id=evidence_id,
                version_id=earlier_version_id,
                provider_id='file_system',
                user_id='admin'
            )
            logger.info(f"Restored version stored at: {storage_location}")
        
        # Get the audit logs
        logger.info("Getting audit logs...")
        audit_logs = storage_manager.get_audit_logs(
            evidence_id=evidence_id,
            user_id='admin'
        )
        logger.info(f"Audit logs: {json.dumps(audit_logs, indent=2)}")
        
        # Revoke access from the user
        logger.info(f"Revoking access from user {user_id}...")
        storage_manager.revoke_access(
            evidence_id=evidence_id,
            user_id=user_id,
            revoker_id='admin'
        )
        
        # Try to retrieve the evidence as the user (should fail)
        try:
            logger.info(f"Trying to retrieve evidence as user {user_id} (should fail)...")
            storage_manager.retrieve(
                provider_id='file_system',
                evidence_id=evidence_id,
                user_id=user_id,
                decrypt=True
            )
        except ValueError as e:
            logger.info(f"Expected error: {e}")
        
        # Delete the evidence
        logger.info("Deleting evidence...")
        storage_manager.delete(
            provider_id='file_system',
            evidence_id=evidence_id,
            user_id='admin',
            delete_versions=True,
            delete_audit_logs=False  # Keep audit logs for reference
        )
        logger.info("Evidence deleted")
        
        # Get the audit logs after deletion
        logger.info("Getting audit logs after deletion...")
        audit_logs = storage_manager.get_audit_logs(
            evidence_id=evidence_id,
            user_id='admin'
        )
        logger.info(f"Audit logs after deletion: {json.dumps(audit_logs, indent=2)}")
        
    except Exception as e:
        logger.error(f"Error: {e}")
    
    finally:
        # Clean up the temporary directory
        # Uncomment the following line to delete the temporary directory
        # import shutil; shutil.rmtree(temp_dir)
        pass

if __name__ == "__main__":
    main()
