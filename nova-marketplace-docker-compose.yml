version: '3.8'

services:
  nova-marketplace:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: nova-marketplace
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - PORT=3000
    volumes:
      - .:/app
      - /app/node_modules
    restart: unless-stopped
    networks:
      - nova-network

  # Add a MongoDB service for future use
  # mongodb:
  #   image: mongo:latest
  #   container_name: nova-mongodb
  #   ports:
  #     - "27017:27017"
  #   volumes:
  #     - mongodb_data:/data/db
  #   networks:
  #     - nova-network

networks:
  nova-network:
    driver: bridge

# volumes:
#   mongodb_data:
