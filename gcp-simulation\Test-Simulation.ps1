# Test GCP Simulation Environment
# This script tests the GCP simulation environment for NovaFuse

Write-Host "Testing GCP Simulation Environment" -ForegroundColor Yellow

# Check if the containers are running
$containersRunning = docker-compose ps | Select-String "Up"
if (-not $containersRunning) {
    Write-Host "Containers are not running. Please start the simulation environment first." -ForegroundColor Red
    exit 1
}

# Test NovaFuse API
Write-Host "Testing NovaFuse API..." -ForegroundColor Yellow
try {
    $apiHealth = Invoke-RestMethod -Uri "http://localhost:3001/health" -Method Get
    if ($apiHealth.status -eq "ok") {
        Write-Host "NovaFuse API is running." -ForegroundColor Green
    } else {
        Write-Host "NovaFuse API is not responding correctly." -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "NovaFuse API is not responding correctly." -ForegroundColor Red
    exit 1
}

# Test NovaConnect UAC
Write-Host "Testing NovaConnect UAC..." -ForegroundColor Yellow
try {
    $uacHealth = Invoke-RestMethod -Uri "http://localhost:3002/health" -Method Get
    if ($uacHealth.status -eq "ok") {
        Write-Host "NovaConnect UAC is running." -ForegroundColor Green
    } else {
        Write-Host "NovaConnect UAC is not responding correctly." -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "NovaConnect UAC is not responding correctly." -ForegroundColor Red
    exit 1
}

# Test Security Command Center Simulator
Write-Host "Testing Security Command Center Simulator..." -ForegroundColor Yellow
try {
    $sccHealth = Invoke-RestMethod -Uri "http://localhost:8081/health" -Method Get
    if ($sccHealth.status -eq "ok") {
        Write-Host "Security Command Center Simulator is running." -ForegroundColor Green
    } else {
        Write-Host "Security Command Center Simulator is not responding correctly." -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "Security Command Center Simulator is not responding correctly." -ForegroundColor Red
    exit 1
}

# Test Cloud IAM Simulator
Write-Host "Testing Cloud IAM Simulator..." -ForegroundColor Yellow
try {
    $iamHealth = Invoke-RestMethod -Uri "http://localhost:8082/health" -Method Get
    if ($iamHealth.status -eq "ok") {
        Write-Host "Cloud IAM Simulator is running." -ForegroundColor Green
    } else {
        Write-Host "Cloud IAM Simulator is not responding correctly." -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "Cloud IAM Simulator is not responding correctly." -ForegroundColor Red
    exit 1
}

# Test BigQuery Simulator
Write-Host "Testing BigQuery Simulator..." -ForegroundColor Yellow
try {
    $bqHealth = Invoke-RestMethod -Uri "http://localhost:8083/health" -Method Get
    if ($bqHealth.status -eq "ok") {
        Write-Host "BigQuery Simulator is running." -ForegroundColor Green
    } else {
        Write-Host "BigQuery Simulator is not responding correctly." -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "BigQuery Simulator is not responding correctly." -ForegroundColor Red
    exit 1
}

# Test NovaFuse UI
Write-Host "Testing NovaFuse UI..." -ForegroundColor Yellow
try {
    $uiHealth = Invoke-RestMethod -Uri "http://localhost:3003/health" -Method Get
    if ($uiHealth.status -eq "ok") {
        Write-Host "NovaFuse UI is running." -ForegroundColor Green
    } else {
        Write-Host "NovaFuse UI is not responding correctly." -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "NovaFuse UI is not responding correctly." -ForegroundColor Red
    exit 1
}

# Test API Gateway
Write-Host "Testing API Gateway..." -ForegroundColor Yellow
try {
    $gatewayResponse = Invoke-WebRequest -Uri "http://localhost:3000" -Method Get
    if ($gatewayResponse.Content -match "NovaFuse") {
        Write-Host "API Gateway is running." -ForegroundColor Green
    } else {
        Write-Host "API Gateway is not responding correctly." -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "API Gateway is not responding correctly." -ForegroundColor Red
    exit 1
}

# Test Feature Flag System
Write-Host "Testing Feature Flag System..." -ForegroundColor Yellow

# Test with NovaPrime tier (should have access to all features)
Write-Host "Testing with NovaPrime tier..." -ForegroundColor Yellow
try {
    $privacyResponse = Invoke-RestMethod -Uri "http://localhost:3001/privacy/management/processing-activities" -Method Get -Headers @{"X-Product-Tier" = "novaPrime"}
    if ($privacyResponse.data) {
        Write-Host "NovaPrime has access to Privacy Management API." -ForegroundColor Green
    } else {
        Write-Host "NovaPrime does not have access to Privacy Management API." -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "NovaPrime does not have access to Privacy Management API." -ForegroundColor Red
    exit 1
}

# Test with NovaCore tier (should have access to privacy but not ESG)
Write-Host "Testing with NovaCore tier..." -ForegroundColor Yellow
try {
    $privacyResponse = Invoke-RestMethod -Uri "http://localhost:3001/privacy/management/processing-activities" -Method Get -Headers @{"X-Product-Tier" = "novaCore"}
    $hasPrivacyAccess = $true
} catch {
    $hasPrivacyAccess = $false
}

try {
    $esgResponse = Invoke-RestMethod -Uri "http://localhost:3001/esg/metrics" -Method Get -Headers @{"X-Product-Tier" = "novaCore"}
    $hasEsgAccess = $true
} catch {
    $hasEsgAccess = $false
}

if ($hasPrivacyAccess -and -not $hasEsgAccess) {
    Write-Host "NovaCore has correct feature access." -ForegroundColor Green
} else {
    Write-Host "NovaCore does not have correct feature access." -ForegroundColor Red
    exit 1
}

# Test Google Cloud Integration
Write-Host "Testing Google Cloud Integration..." -ForegroundColor Yellow
try {
    $sccIntegration = Invoke-RestMethod -Uri "http://localhost:3001/integrations/gcp/scc/findings" -Method Get
    if ($sccIntegration.data) {
        Write-Host "Google Cloud Integration is working." -ForegroundColor Green
    } else {
        Write-Host "Google Cloud Integration is not working." -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "Google Cloud Integration is not working." -ForegroundColor Red
    exit 1
}

# Test NovaConnect UAC with Google Cloud connectors
Write-Host "Testing NovaConnect UAC with Google Cloud connectors..." -ForegroundColor Yellow
try {
    $gcpConnectors = Invoke-RestMethod -Uri "http://localhost:3002/integrations/gcp/connectors" -Method Get
    if ($gcpConnectors.data -and $gcpConnectors.data[0].name -match "Google") {
        Write-Host "NovaConnect UAC has Google Cloud connectors." -ForegroundColor Green
    } else {
        Write-Host "NovaConnect UAC does not have Google Cloud connectors." -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "NovaConnect UAC does not have Google Cloud connectors." -ForegroundColor Red
    exit 1
}

Write-Host "All tests passed. GCP Simulation Environment is working correctly." -ForegroundColor Green

# Open the NovaFuse UI in the browser
Write-Host "Opening NovaFuse UI in the browser..." -ForegroundColor Yellow
Start-Process "http://localhost:3003"
