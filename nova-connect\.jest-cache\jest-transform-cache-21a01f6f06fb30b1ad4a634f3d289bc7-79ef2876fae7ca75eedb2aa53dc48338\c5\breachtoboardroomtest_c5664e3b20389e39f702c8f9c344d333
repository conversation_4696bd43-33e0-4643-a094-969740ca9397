9e6381deffa52703c613b91d976c39da
/**
 * NovaConnect "Breach to Boardroom" Demo Test
 * 
 * This test simulates the demo scenario described in the strategic document:
 * 1. Simulate PHI leak via misconfigured BigQuery dataset
 * 2. Auto-containment in under 8 seconds
 * 3. Boardroom-ready reporting with compliance score maintained at 99.9%
 */

const {
  TransformationEngine
} = require('../../src/engines/transformation-engine');
const {
  RemediationEngine
} = require('../../src/engines/remediation-engine');
const {
  SCCConnector
} = require('../../src/connectors/gcp/scc-connector');
const {
  EncryptionService
} = require('../../src/security/encryption-service');

// Mock GCP services
const mockBigQueryService = {
  getDataset: jest.fn(),
  updateDataset: jest.fn(),
  encryptDataset: jest.fn(),
  getAccessLogs: jest.fn()
};

// Mock Looker service
const mockLookerService = {
  updateDashboard: jest.fn(),
  getComplianceScore: jest.fn()
};
describe('Breach to Boardroom Demo', () => {
  let transformationEngine;
  let remediationEngine;
  let sccConnector;
  let encryptionService;
  beforeAll(async () => {
    // Initialize engines and services
    transformationEngine = new TransformationEngine();
    remediationEngine = new RemediationEngine();
    sccConnector = new SCCConnector();
    encryptionService = new EncryptionService();

    // Register remediation actions
    remediationEngine.registerAction('encrypt-dataset', async ({
      parameters
    }) => {
      const {
        datasetId,
        projectId
      } = parameters;
      await mockBigQueryService.encryptDataset(projectId, datasetId);
      return {
        success: true,
        datasetId,
        projectId
      };
    });
    remediationEngine.registerAction('update-access-controls', async ({
      parameters
    }) => {
      const {
        datasetId,
        projectId,
        accessLevel
      } = parameters;
      const dataset = await mockBigQueryService.getDataset(projectId, datasetId);
      dataset.access = accessLevel;
      await mockBigQueryService.updateDataset(projectId, datasetId, dataset);
      return {
        success: true,
        datasetId,
        projectId
      };
    });
    remediationEngine.registerAction('update-compliance-dashboard', async ({
      parameters
    }) => {
      const {
        dashboardId
      } = parameters;
      await mockLookerService.updateDashboard(dashboardId);
      return {
        success: true,
        dashboardId
      };
    });
  });
  it('should detect and remediate PHI leak in under 8 seconds', async () => {
    // 1. Simulate the finding from SCC
    const finding = {
      name: 'organizations/123/sources/456/findings/phi-leak-789',
      category: 'DATA_LEAK',
      severity: 'HIGH',
      resourceName: 'projects/healthcare-demo/datasets/patient_records',
      state: 'ACTIVE',
      eventTime: new Date().toISOString(),
      createTime: new Date().toISOString(),
      sourceProperties: {
        finding_type: 'Sensitive Data Exposure',
        finding_description: 'PHI data exposed in BigQuery dataset',
        data_type: 'PHI',
        compliance_frameworks: ['HIPAA', 'GDPR']
      }
    };

    // 2. Normalize the finding
    const startTime = Date.now();
    const normalizedFinding = {
      id: 'phi-leak-789',
      type: 'data_leak',
      severity: 'high',
      resourceName: 'projects/healthcare-demo/datasets/patient_records',
      resourceType: 'bigquery.dataset',
      createdAt: new Date().getTime(),
      description: 'PHI data exposed in BigQuery dataset',
      dataType: 'PHI',
      complianceFrameworks: ['HIPAA', 'GDPR']
    };

    // 3. Create remediation scenario
    const remediationScenario = {
      id: 'phi-leak-remediation-789',
      type: 'compliance',
      framework: 'HIPAA',
      control: '164.312(a)(1)',
      severity: 'high',
      resource: {
        id: 'patient_records',
        type: 'bigquery.dataset',
        name: 'patient_records',
        provider: 'gcp',
        projectId: 'healthcare-demo'
      },
      finding: normalizedFinding,
      remediationSteps: [{
        id: 'step-1',
        action: 'encrypt-dataset',
        parameters: {
          projectId: 'healthcare-demo',
          datasetId: 'patient_records',
          encryptionType: 'AES-256',
          keyRotationPeriod: '90d'
        }
      }, {
        id: 'step-2',
        action: 'update-access-controls',
        parameters: {
          projectId: 'healthcare-demo',
          datasetId: 'patient_records',
          accessLevel: 'restricted',
          allowedRoles: ['healthcare-admin', 'compliance-officer']
        }
      }, {
        id: 'step-3',
        action: 'update-compliance-dashboard',
        parameters: {
          dashboardId: 'hipaa-compliance-dashboard',
          findingId: 'phi-leak-789',
          remediationId: 'phi-leak-remediation-789'
        }
      }]
    };

    // Mock service responses
    mockBigQueryService.getDataset.mockResolvedValue({
      id: 'patient_records',
      access: 'public'
    });
    mockBigQueryService.updateDataset.mockResolvedValue({
      id: 'patient_records',
      access: 'restricted'
    });
    mockBigQueryService.encryptDataset.mockResolvedValue({
      id: 'patient_records',
      encrypted: true
    });
    mockBigQueryService.getAccessLogs.mockResolvedValue([{
      timestamp: new Date().toISOString(),
      user: 'system',
      action: 'encrypt'
    }, {
      timestamp: new Date().toISOString(),
      user: 'system',
      action: 'update_access'
    }]);
    mockLookerService.getComplianceScore.mockResolvedValue({
      overall: 99.9,
      hipaa: 99.8,
      gdpr: 100
    });

    // 4. Execute remediation
    const remediationResult = await remediationEngine.executeRemediation(remediationScenario);
    const endTime = Date.now();
    const duration = endTime - startTime;

    // 5. Verify results
    expect(remediationResult.success).toBe(true);
    expect(remediationResult.steps.length).toBe(3);
    expect(remediationResult.steps.every(step => step.success)).toBe(true);

    // Verify encryption was called
    expect(mockBigQueryService.encryptDataset).toHaveBeenCalledWith('healthcare-demo', 'patient_records');

    // Verify access controls were updated
    expect(mockBigQueryService.updateDataset).toHaveBeenCalledWith('healthcare-demo', 'patient_records', expect.objectContaining({
      access: 'restricted'
    }));

    // Verify dashboard was updated
    expect(mockLookerService.updateDashboard).toHaveBeenCalledWith('hipaa-compliance-dashboard');

    // Verify compliance score is maintained
    const complianceScore = await mockLookerService.getComplianceScore();
    expect(complianceScore.overall).toBeGreaterThanOrEqual(99.9);

    // Verify remediation completed in under 8 seconds
    expect(duration).toBeLessThan(8000);
    console.log(`Remediation completed in ${duration}ms`);

    // Verify access logs were updated
    const accessLogs = await mockBigQueryService.getAccessLogs();
    expect(accessLogs.length).toBeGreaterThanOrEqual(2);
  });
  it('should generate boardroom-ready report', async () => {
    // Mock compliance score
    mockLookerService.getComplianceScore.mockResolvedValue({
      overall: 99.9,
      hipaa: 99.8,
      gdpr: 100,
      details: {
        controls: {
          total: 500,
          compliant: 499,
          nonCompliant: 1,
          remediated: 1
        },
        frameworks: [{
          name: 'HIPAA',
          score: 99.8,
          controlsCompliant: 249,
          controlsTotal: 250
        }, {
          name: 'GDPR',
          score: 100,
          controlsCompliant: 250,
          controlsTotal: 250
        }]
      }
    });

    // Get compliance score
    const complianceScore = await mockLookerService.getComplianceScore();

    // Verify compliance score
    expect(complianceScore.overall).toBeGreaterThanOrEqual(99.9);
    expect(complianceScore.details.controls.remediated).toBeGreaterThanOrEqual(1);

    // Calculate cost savings
    const manualRemediationCostPerIncident = 4200; // $4,200 per incident
    const annualIncidents = 1000; // Estimated annual incidents
    const automatedRemediationRate = 0.92; // 92% of incidents automated

    const annualSavings = manualRemediationCostPerIncident * annualIncidents * automatedRemediationRate;

    // Verify cost savings
    expect(annualSavings).toBeGreaterThanOrEqual(3800000); // $3.8M+

    console.log(`Annual cost savings: $${annualSavings.toLocaleString()}`);
    console.log(`Compliance score maintained at ${complianceScore.overall}%`);
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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