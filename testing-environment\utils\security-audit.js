/**
 * Security Audit Module for NovaConnect Universal API Connector
 * 
 * This module provides comprehensive audit logging for security events.
 */

const fs = require('fs');
const path = require('path');
const winston = require('winston');
const crypto = require('crypto');

// Configure logger
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  defaultMeta: { service: 'security-audit' },
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ 
      filename: path.join(process.env.LOG_DIR || 'logs', 'security-audit.log'),
      maxsize: 10485760, // 10MB
      maxFiles: 10,
      tailable: true
    })
  ]
});

// Audit event types
const EVENT_TYPES = {
  // Authentication events
  AUTH_SUCCESS: 'authentication_success',
  AUTH_FAILURE: 'authentication_failure',
  AUTH_LOGOUT: 'authentication_logout',
  AUTH_PASSWORD_CHANGE: 'authentication_password_change',
  AUTH_PASSWORD_RESET: 'authentication_password_reset',
  
  // Access control events
  ACCESS_GRANTED: 'access_granted',
  ACCESS_DENIED: 'access_denied',
  PERMISSION_CHANGE: 'permission_change',
  
  // Encryption events
  ENCRYPTION_KEY_CREATED: 'encryption_key_created',
  ENCRYPTION_KEY_ROTATED: 'encryption_key_rotated',
  ENCRYPTION_KEY_ACCESSED: 'encryption_key_accessed',
  ENCRYPTION_OPERATION: 'encryption_operation',
  
  // Credential events
  CREDENTIAL_CREATED: 'credential_created',
  CREDENTIAL_UPDATED: 'credential_updated',
  CREDENTIAL_DELETED: 'credential_deleted',
  CREDENTIAL_ACCESSED: 'credential_accessed',
  
  // Connector events
  CONNECTOR_CREATED: 'connector_created',
  CONNECTOR_UPDATED: 'connector_updated',
  CONNECTOR_DELETED: 'connector_deleted',
  CONNECTOR_EXECUTED: 'connector_executed',
  
  // System events
  SYSTEM_STARTUP: 'system_startup',
  SYSTEM_SHUTDOWN: 'system_shutdown',
  SYSTEM_CONFIG_CHANGE: 'system_config_change',
  SYSTEM_ERROR: 'system_error',
  
  // Backup events
  BACKUP_CREATED: 'backup_created',
  BACKUP_RESTORED: 'backup_restored',
  BACKUP_VERIFIED: 'backup_verified',
  
  // Security events
  SECURITY_VIOLATION: 'security_violation',
  RATE_LIMIT_EXCEEDED: 'rate_limit_exceeded',
  SUSPICIOUS_ACTIVITY: 'suspicious_activity'
};

// Severity levels
const SEVERITY = {
  INFO: 'info',
  WARNING: 'warning',
  ERROR: 'error',
  CRITICAL: 'critical'
};

// Audit log storage
let auditLogs = [];
const MAX_MEMORY_LOGS = 1000;

/**
 * Generate a unique event ID
 * 
 * @returns {string} - Unique event ID
 */
function generateEventId() {
  return `evt-${Date.now()}-${crypto.randomBytes(4).toString('hex')}`;
}

/**
 * Log a security event
 * 
 * @param {string} eventType - The type of event (use EVENT_TYPES constants)
 * @param {Object} details - Event details
 * @param {string} details.userId - The user ID associated with the event
 * @param {string} details.resourceType - The type of resource (e.g., 'credential', 'connector')
 * @param {string} details.resourceId - The ID of the resource
 * @param {string} details.action - The action performed
 * @param {string} details.outcome - The outcome of the action ('success', 'failure')
 * @param {string} details.severity - The severity level (use SEVERITY constants)
 * @param {Object} details.metadata - Additional metadata
 * @returns {string} - The event ID
 */
function logSecurityEvent(eventType, details) {
  // Generate event ID
  const eventId = generateEventId();
  
  // Create audit log entry
  const auditLog = {
    eventId,
    eventType,
    timestamp: new Date().toISOString(),
    userId: details.userId || 'system',
    resourceType: details.resourceType,
    resourceId: details.resourceId,
    action: details.action,
    outcome: details.outcome || 'success',
    severity: details.severity || SEVERITY.INFO,
    sourceIp: details.sourceIp,
    userAgent: details.userAgent,
    metadata: details.metadata || {}
  };
  
  // Add to in-memory logs
  auditLogs.push(auditLog);
  
  // Trim in-memory logs if needed
  if (auditLogs.length > MAX_MEMORY_LOGS) {
    auditLogs = auditLogs.slice(-MAX_MEMORY_LOGS);
  }
  
  // Log the event
  const logLevel = auditLog.severity === SEVERITY.CRITICAL ? 'error' :
                  auditLog.severity === SEVERITY.ERROR ? 'error' :
                  auditLog.severity === SEVERITY.WARNING ? 'warn' : 'info';
  
  logger[logLevel](`Security event: ${eventType}`, auditLog);
  
  return eventId;
}

/**
 * Log an authentication event
 * 
 * @param {boolean} success - Whether authentication was successful
 * @param {Object} details - Event details
 * @returns {string} - The event ID
 */
function logAuthenticationEvent(success, details) {
  const eventType = success ? EVENT_TYPES.AUTH_SUCCESS : EVENT_TYPES.AUTH_FAILURE;
  const severity = success ? SEVERITY.INFO : SEVERITY.WARNING;
  
  return logSecurityEvent(eventType, {
    ...details,
    action: 'authenticate',
    outcome: success ? 'success' : 'failure',
    severity
  });
}

/**
 * Log an access control event
 * 
 * @param {boolean} granted - Whether access was granted
 * @param {Object} details - Event details
 * @returns {string} - The event ID
 */
function logAccessControlEvent(granted, details) {
  const eventType = granted ? EVENT_TYPES.ACCESS_GRANTED : EVENT_TYPES.ACCESS_DENIED;
  const severity = granted ? SEVERITY.INFO : SEVERITY.WARNING;
  
  return logSecurityEvent(eventType, {
    ...details,
    action: 'access',
    outcome: granted ? 'success' : 'failure',
    severity
  });
}

/**
 * Log an encryption key event
 * 
 * @param {string} action - The action performed ('created', 'rotated', 'accessed')
 * @param {Object} details - Event details
 * @returns {string} - The event ID
 */
function logEncryptionKeyEvent(action, details) {
  let eventType;
  
  switch (action) {
    case 'created':
      eventType = EVENT_TYPES.ENCRYPTION_KEY_CREATED;
      break;
    case 'rotated':
      eventType = EVENT_TYPES.ENCRYPTION_KEY_ROTATED;
      break;
    case 'accessed':
      eventType = EVENT_TYPES.ENCRYPTION_KEY_ACCESSED;
      break;
    default:
      eventType = EVENT_TYPES.ENCRYPTION_OPERATION;
  }
  
  return logSecurityEvent(eventType, {
    ...details,
    action,
    resourceType: 'encryption_key'
  });
}

/**
 * Log a credential event
 * 
 * @param {string} action - The action performed ('created', 'updated', 'deleted', 'accessed')
 * @param {Object} details - Event details
 * @returns {string} - The event ID
 */
function logCredentialEvent(action, details) {
  let eventType;
  
  switch (action) {
    case 'created':
      eventType = EVENT_TYPES.CREDENTIAL_CREATED;
      break;
    case 'updated':
      eventType = EVENT_TYPES.CREDENTIAL_UPDATED;
      break;
    case 'deleted':
      eventType = EVENT_TYPES.CREDENTIAL_DELETED;
      break;
    case 'accessed':
      eventType = EVENT_TYPES.CREDENTIAL_ACCESSED;
      break;
    default:
      eventType = EVENT_TYPES.CREDENTIAL_ACCESSED;
  }
  
  return logSecurityEvent(eventType, {
    ...details,
    action,
    resourceType: 'credential'
  });
}

/**
 * Log a connector event
 * 
 * @param {string} action - The action performed ('created', 'updated', 'deleted', 'executed')
 * @param {Object} details - Event details
 * @returns {string} - The event ID
 */
function logConnectorEvent(action, details) {
  let eventType;
  
  switch (action) {
    case 'created':
      eventType = EVENT_TYPES.CONNECTOR_CREATED;
      break;
    case 'updated':
      eventType = EVENT_TYPES.CONNECTOR_UPDATED;
      break;
    case 'deleted':
      eventType = EVENT_TYPES.CONNECTOR_DELETED;
      break;
    case 'executed':
      eventType = EVENT_TYPES.CONNECTOR_EXECUTED;
      break;
    default:
      eventType = EVENT_TYPES.CONNECTOR_EXECUTED;
  }
  
  return logSecurityEvent(eventType, {
    ...details,
    action,
    resourceType: 'connector'
  });
}

/**
 * Log a system event
 * 
 * @param {string} action - The action performed ('startup', 'shutdown', 'config_change', 'error')
 * @param {Object} details - Event details
 * @returns {string} - The event ID
 */
function logSystemEvent(action, details) {
  let eventType;
  let severity = SEVERITY.INFO;
  
  switch (action) {
    case 'startup':
      eventType = EVENT_TYPES.SYSTEM_STARTUP;
      break;
    case 'shutdown':
      eventType = EVENT_TYPES.SYSTEM_SHUTDOWN;
      break;
    case 'config_change':
      eventType = EVENT_TYPES.SYSTEM_CONFIG_CHANGE;
      break;
    case 'error':
      eventType = EVENT_TYPES.SYSTEM_ERROR;
      severity = SEVERITY.ERROR;
      break;
    default:
      eventType = EVENT_TYPES.SYSTEM_CONFIG_CHANGE;
  }
  
  return logSecurityEvent(eventType, {
    ...details,
    action,
    resourceType: 'system',
    severity
  });
}

/**
 * Log a backup event
 * 
 * @param {string} action - The action performed ('created', 'restored', 'verified')
 * @param {Object} details - Event details
 * @returns {string} - The event ID
 */
function logBackupEvent(action, details) {
  let eventType;
  
  switch (action) {
    case 'created':
      eventType = EVENT_TYPES.BACKUP_CREATED;
      break;
    case 'restored':
      eventType = EVENT_TYPES.BACKUP_RESTORED;
      break;
    case 'verified':
      eventType = EVENT_TYPES.BACKUP_VERIFIED;
      break;
    default:
      eventType = EVENT_TYPES.BACKUP_CREATED;
  }
  
  return logSecurityEvent(eventType, {
    ...details,
    action,
    resourceType: 'backup'
  });
}

/**
 * Log a security violation
 * 
 * @param {Object} details - Event details
 * @returns {string} - The event ID
 */
function logSecurityViolation(details) {
  return logSecurityEvent(EVENT_TYPES.SECURITY_VIOLATION, {
    ...details,
    action: 'violation',
    severity: SEVERITY.CRITICAL
  });
}

/**
 * Log a rate limit exceeded event
 * 
 * @param {Object} details - Event details
 * @returns {string} - The event ID
 */
function logRateLimitExceeded(details) {
  return logSecurityEvent(EVENT_TYPES.RATE_LIMIT_EXCEEDED, {
    ...details,
    action: 'rate_limit',
    severity: SEVERITY.WARNING
  });
}

/**
 * Log suspicious activity
 * 
 * @param {Object} details - Event details
 * @returns {string} - The event ID
 */
function logSuspiciousActivity(details) {
  return logSecurityEvent(EVENT_TYPES.SUSPICIOUS_ACTIVITY, {
    ...details,
    action: 'suspicious',
    severity: SEVERITY.WARNING
  });
}

/**
 * Get recent audit logs
 * 
 * @param {number} limit - Maximum number of logs to return
 * @returns {Array} - Recent audit logs
 */
function getRecentAuditLogs(limit = 100) {
  return auditLogs.slice(-Math.min(limit, auditLogs.length));
}

/**
 * Search audit logs
 * 
 * @param {Object} criteria - Search criteria
 * @param {string} criteria.eventType - Event type to search for
 * @param {string} criteria.userId - User ID to search for
 * @param {string} criteria.resourceType - Resource type to search for
 * @param {string} criteria.resourceId - Resource ID to search for
 * @param {string} criteria.action - Action to search for
 * @param {string} criteria.outcome - Outcome to search for
 * @param {string} criteria.severity - Severity to search for
 * @param {string} criteria.startTime - Start time (ISO string)
 * @param {string} criteria.endTime - End time (ISO string)
 * @param {number} limit - Maximum number of logs to return
 * @returns {Array} - Matching audit logs
 */
function searchAuditLogs(criteria, limit = 100) {
  return auditLogs.filter(log => {
    // Check each criterion
    if (criteria.eventType && log.eventType !== criteria.eventType) return false;
    if (criteria.userId && log.userId !== criteria.userId) return false;
    if (criteria.resourceType && log.resourceType !== criteria.resourceType) return false;
    if (criteria.resourceId && log.resourceId !== criteria.resourceId) return false;
    if (criteria.action && log.action !== criteria.action) return false;
    if (criteria.outcome && log.outcome !== criteria.outcome) return false;
    if (criteria.severity && log.severity !== criteria.severity) return false;
    
    // Check time range
    if (criteria.startTime && new Date(log.timestamp) < new Date(criteria.startTime)) return false;
    if (criteria.endTime && new Date(log.timestamp) > new Date(criteria.endTime)) return false;
    
    return true;
  }).slice(-Math.min(limit, auditLogs.length));
}

/**
 * Create Express middleware for audit logging
 * 
 * @returns {Function} - Express middleware
 */
function createAuditMiddleware() {
  return (req, res, next) => {
    // Capture original end method
    const originalEnd = res.end;
    
    // Override end method to log after response is sent
    res.end = function(chunk, encoding) {
      // Restore original end method
      res.end = originalEnd;
      
      // Call original end method
      res.end(chunk, encoding);
      
      // Log the request
      const userId = req.user ? req.user.id : 'anonymous';
      const resourceType = req.baseUrl.split('/')[1] || 'unknown';
      const resourceId = req.params.id || 'unknown';
      const action = req.method.toLowerCase();
      const outcome = res.statusCode < 400 ? 'success' : 'failure';
      const severity = res.statusCode < 400 ? SEVERITY.INFO :
                      res.statusCode < 500 ? SEVERITY.WARNING : SEVERITY.ERROR;
      
      logSecurityEvent(EVENT_TYPES.ACCESS_GRANTED, {
        userId,
        resourceType,
        resourceId,
        action,
        outcome,
        severity,
        sourceIp: req.ip,
        userAgent: req.headers['user-agent'],
        metadata: {
          method: req.method,
          url: req.originalUrl,
          statusCode: res.statusCode,
          responseTime: Date.now() - req.startTime
        }
      });
    };
    
    // Record request start time
    req.startTime = Date.now();
    
    next();
  };
}

module.exports = {
  EVENT_TYPES,
  SEVERITY,
  logSecurityEvent,
  logAuthenticationEvent,
  logAccessControlEvent,
  logEncryptionKeyEvent,
  logCredentialEvent,
  logConnectorEvent,
  logSystemEvent,
  logBackupEvent,
  logSecurityViolation,
  logRateLimitExceeded,
  logSuspiciousActivity,
  getRecentAuditLogs,
  searchAuditLogs,
  createAuditMiddleware
};
