.container {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 100%;
  max-height: 600px;
}

.header {
  background-color: #1a365d; /* Dark blue */
  color: white;
  padding: 16px;
  text-align: center;
}

.header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.header p {
  margin: 4px 0 0;
  font-size: 0.9rem;
  opacity: 0.9;
}

.chatContainer {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.message {
  padding: 12px 16px;
  border-radius: 8px;
  max-width: 80%;
  word-break: break-word;
}

.user {
  background-color: #e2e8f0;
  align-self: flex-end;
}

.assistant {
  background-color: #ebf8ff;
  align-self: flex-start;
}

.processingIndicator {
  align-self: flex-start;
  color: #718096;
  font-style: italic;
  padding: 8px 12px;
}

.inputForm {
  display: flex;
  padding: 12px;
  border-top: 1px solid #e2e8f0;
  background-color: white;
}

.input {
  flex: 1;
  padding: 10px 12px;
  border: 1px solid #cbd5e0;
  border-radius: 4px;
  font-size: 0.9rem;
}

.input:focus {
  outline: none;
  border-color: #4299e1;
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.2);
}

.button {
  margin-left: 8px;
  padding: 10px 16px;
  background-color: #4299e1;
  color: white;
  border: none;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.button:hover {
  background-color: #3182ce;
}

.button:disabled {
  background-color: #a0aec0;
  cursor: not-allowed;
}

.suggestionList {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 8px;
}

.suggestionItem {
  background-color: #f7fafc;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  padding: 10px 12px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.suggestionItem:hover {
  background-color: #edf2f7;
}

.suggestionTitle {
  font-weight: 500;
  color: #2d3748;
  margin-bottom: 4px;
}

.suggestionDescription {
  font-size: 0.85rem;
  color: #718096;
}

.useCaseContainer {
  background-color: #f7fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
  margin-top: 8px;
}

.useCaseTitle {
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 8px;
}

.useCaseSection {
  margin-bottom: 12px;
}

.useCaseSectionTitle {
  font-weight: 500;
  color: #4a5568;
  margin-bottom: 4px;
}

.useCaseResults {
  list-style-type: none;
  padding-left: 0;
  margin: 8px 0;
}

.useCaseResults li {
  padding-left: 24px;
  position: relative;
  margin-bottom: 6px;
}

.useCaseResults li:before {
  content: "✓";
  color: #38a169;
  position: absolute;
  left: 0;
}

.comparisonContainer {
  background-color: #f7fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
  margin-top: 8px;
}

.comparisonTitle {
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 12px;
}

.advantagesList {
  list-style-type: none;
  padding-left: 0;
  margin: 8px 0;
}

.advantagesList li {
  padding-left: 24px;
  position: relative;
  margin-bottom: 8px;
}

.advantagesList li:before {
  content: "→";
  color: #4299e1;
  position: absolute;
  left: 0;
}

.metricsContainer {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-top: 12px;
}

.metricCard {
  background-color: white;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 12px;
}

.metricTitle {
  font-weight: 500;
  color: #4a5568;
  margin-bottom: 8px;
}

.metricValue {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.metricLabel {
  color: #718096;
}

.metricNovaFuse {
  color: #38a169;
  font-weight: 500;
}

.metricCompetitor {
  color: #e53e3e;
}

.costSavings {
  background-color: #ebf8ff;
  border-radius: 6px;
  padding: 12px;
  margin-top: 12px;
}

.costSavingsAmount {
  font-size: 1.25rem;
  font-weight: 600;
  color: #2b6cb0;
  margin-bottom: 4px;
}

.costSavingsDescription {
  color: #4a5568;
  font-size: 0.9rem;
}

.roiCalculatorContainer {
  background-color: #f7fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
  margin-top: 8px;
}

.roiCalculatorTitle {
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 8px;
}

.roiCalculatorDescription {
  color: #4a5568;
  margin-bottom: 16px;
}

.roiInputGroup {
  margin-bottom: 12px;
}

.roiInputLabel {
  display: block;
  font-weight: 500;
  color: #4a5568;
  margin-bottom: 6px;
}

.roiInput {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #cbd5e0;
  border-radius: 4px;
}

.roiSelect {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #cbd5e0;
  border-radius: 4px;
  background-color: white;
}

.roiButton {
  width: 100%;
  padding: 10px 16px;
  background-color: #4299e1;
  color: white;
  border: none;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  margin-top: 8px;
}

.roiButton:hover {
  background-color: #3182ce;
}

.partnerEmpowermentContainer {
  background-color: #f7fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
  margin-top: 8px;
}

.partnerEmpowermentTitle {
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 8px;
}

.partnerEmpowermentDescription {
  color: #4a5568;
  margin-bottom: 16px;
}

.revenueModelsContainer {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
  margin-bottom: 16px;
}

.revenueModelCard {
  background-color: white;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 12px;
  text-align: center;
}

.revenueModelName {
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 4px;
}

.revenueModelSplit {
  font-size: 1.25rem;
  font-weight: 700;
  color: #4299e1;
  margin-bottom: 4px;
}

.revenueModelDescription {
  color: #718096;
  font-size: 0.9rem;
}

.benefitsList {
  list-style-type: none;
  padding-left: 0;
  margin: 16px 0;
}

.benefitsList li {
  padding-left: 24px;
  position: relative;
  margin-bottom: 8px;
}

.benefitsList li:before {
  content: "✓";
  color: #38a169;
  position: absolute;
  left: 0;
}

.onboardingContainer {
  background-color: #ebf8ff;
  border-radius: 6px;
  padding: 12px;
  margin-top: 12px;
}

.onboardingTitle {
  font-weight: 500;
  color: #2b6cb0;
  margin-bottom: 8px;
}

.onboardingSteps {
  list-style-type: decimal;
  padding-left: 24px;
  margin: 8px 0;
}

.onboardingSteps li {
  margin-bottom: 6px;
  color: #4a5568;
}

.onboardingTimeline {
  font-weight: 500;
  color: #2b6cb0;
  margin-top: 8px;
}
