/**
 * NovaFuse Universal API Connector Input Validator
 * 
 * This module provides input validation utilities for the UAC.
 */

/**
 * Input Validation Utility
 * 
 * Provides methods for validating user input to prevent injection attacks.
 */
class InputValidator {
  /**
   * Validate a string against SQL injection
   * @param {string} input - Input to validate
   * @returns {boolean} - Whether the input is safe
   */
  static isSqlSafe(input) {
    if (typeof input !== 'string') {
      return true;
    }
    
    // Check for common SQL injection patterns
    const sqlPatterns = [
      /(\s|^)(SELECT|INSERT|UPDATE|DELETE|DROP|ALTER|EXEC|UNION|CREATE|WHERE)(\s|$)/i,
      /(\s|^)(OR|AND)(\s+)(['"]?\d+['"]?\s*=\s*['"]?\d+['"]?)/i,
      /--/,
      /;.*/,
      /\/\*.+\*\//
    ];
    
    return !sqlPatterns.some(pattern => pattern.test(input));
  }

  /**
   * Validate a string against XSS
   * @param {string} input - Input to validate
   * @returns {boolean} - Whether the input is safe
   */
  static isXssSafe(input) {
    if (typeof input !== 'string') {
      return true;
    }
    
    return !/<script|javascript:|on\w+\s*=|data:|vbscript:|<iframe/i.test(input);
  }

  /**
   * Validate a string against command injection
   * @param {string} input - Input to validate
   * @returns {boolean} - Whether the input is safe
   */
  static isCommandSafe(input) {
    if (typeof input !== 'string') {
      return true;
    }
    
    // Check for common command injection patterns
    const commandPatterns = [
      /(\s|^)(bash|sh|cmd|powershell|exec|eval)(\s|$)/i,
      /[&|;`]/,
      /\$\(.+\)/,
      />\s*[a-zA-Z0-9]/,
      /<\s*[a-zA-Z0-9]/
    ];
    
    return !commandPatterns.some(pattern => pattern.test(input));
  }

  /**
   * Sanitize a string for safe use in HTML
   * @param {string} input - Input to sanitize
   * @returns {string} - Sanitized input
   */
  static sanitizeHtml(input) {
    if (typeof input !== 'string') {
      return input;
    }
    
    return input
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;')
      .replace(/\//g, '&#x2F;');
  }

  /**
   * Sanitize a string for safe use in SQL
   * @param {string} input - Input to sanitize
   * @returns {string} - Sanitized input
   */
  static sanitizeSql(input) {
    if (typeof input !== 'string') {
      return input;
    }
    
    return input
      .replace(/'/g, "''")
      .replace(/\\/g, '\\\\')
      .replace(/\0/g, '\\0')
      .replace(/\n/g, '\\n')
      .replace(/\r/g, '\\r')
      .replace(/\t/g, '\\t')
      .replace(/\x1a/g, '\\Z');
  }

  /**
   * Sanitize a string for safe use in shell commands
   * @param {string} input - Input to sanitize
   * @returns {string} - Sanitized input
   */
  static sanitizeCommand(input) {
    if (typeof input !== 'string') {
      return input;
    }
    
    return input
      .replace(/[&;|`$><!\\]/g, '')
      .replace(/\r/g, '')
      .replace(/\n/g, '');
  }

  /**
   * Validate an object against a schema
   * @param {Object} input - Input to validate
   * @param {Object} schema - Schema to validate against
   * @returns {Object} - Validation result with isValid and errors properties
   */
  static validateObject(input, schema) {
    const errors = [];
    const result = { isValid: true, errors };
    
    if (!input || typeof input !== 'object') {
      result.isValid = false;
      errors.push('Input must be an object');
      return result;
    }
    
    if (!schema || typeof schema !== 'object') {
      result.isValid = false;
      errors.push('Schema must be an object');
      return result;
    }
    
    // Check required fields
    if (schema.required && Array.isArray(schema.required)) {
      for (const field of schema.required) {
        if (input[field] === undefined) {
          result.isValid = false;
          errors.push(`Required field '${field}' is missing`);
        }
      }
    }
    
    // Check field types and constraints
    if (schema.properties && typeof schema.properties === 'object') {
      for (const [field, fieldSchema] of Object.entries(schema.properties)) {
        if (input[field] !== undefined) {
          // Check type
          if (fieldSchema.type) {
            const type = Array.isArray(input[field]) ? 'array' : typeof input[field];
            if (type !== fieldSchema.type) {
              result.isValid = false;
              errors.push(`Field '${field}' must be of type '${fieldSchema.type}'`);
            }
          }
          
          // Check enum
          if (fieldSchema.enum && Array.isArray(fieldSchema.enum)) {
            if (!fieldSchema.enum.includes(input[field])) {
              result.isValid = false;
              errors.push(`Field '${field}' must be one of: ${fieldSchema.enum.join(', ')}`);
            }
          }
          
          // Check pattern
          if (fieldSchema.pattern && typeof input[field] === 'string') {
            const pattern = new RegExp(fieldSchema.pattern);
            if (!pattern.test(input[field])) {
              result.isValid = false;
              errors.push(`Field '${field}' does not match pattern '${fieldSchema.pattern}'`);
            }
          }
          
          // Check min/max for numbers
          if (typeof input[field] === 'number') {
            if (fieldSchema.minimum !== undefined && input[field] < fieldSchema.minimum) {
              result.isValid = false;
              errors.push(`Field '${field}' must be greater than or equal to ${fieldSchema.minimum}`);
            }
            
            if (fieldSchema.maximum !== undefined && input[field] > fieldSchema.maximum) {
              result.isValid = false;
              errors.push(`Field '${field}' must be less than or equal to ${fieldSchema.maximum}`);
            }
          }
          
          // Check minLength/maxLength for strings
          if (typeof input[field] === 'string') {
            if (fieldSchema.minLength !== undefined && input[field].length < fieldSchema.minLength) {
              result.isValid = false;
              errors.push(`Field '${field}' must be at least ${fieldSchema.minLength} characters long`);
            }
            
            if (fieldSchema.maxLength !== undefined && input[field].length > fieldSchema.maxLength) {
              result.isValid = false;
              errors.push(`Field '${field}' must be at most ${fieldSchema.maxLength} characters long`);
            }
          }
          
          // Check minItems/maxItems for arrays
          if (Array.isArray(input[field])) {
            if (fieldSchema.minItems !== undefined && input[field].length < fieldSchema.minItems) {
              result.isValid = false;
              errors.push(`Field '${field}' must have at least ${fieldSchema.minItems} items`);
            }
            
            if (fieldSchema.maxItems !== undefined && input[field].length > fieldSchema.maxItems) {
              result.isValid = false;
              errors.push(`Field '${field}' must have at most ${fieldSchema.maxItems} items`);
            }
          }
        }
      }
    }
    
    return result;
  }
}

module.exports = InputValidator;
