import React from 'react';
import <PERSON> from 'next/link';
import PageWithSidebar from '../../components/PageWithSidebar';

export default function ApiDemos() {
  // SEO metadata
  const pageProps = {
    title: 'NovaFuse API Demos - NovaFuse',
    description: 'Explore NovaFuse\'s API demos showcasing our Regulatory Compliance API, Privacy Management API, Security Assessment API, and more.',
    keywords: 'NovaFuse API demos, GRC APIs, Regulatory Compliance API, Privacy Management API, Security Assessment API',
    canonical: 'https://novafuse.io/api-demos',
    ogImage: '/images/api-demos-og.png'
  };

  // API demo data
  const apiDemos = [
    {
      id: 'regulatory-compliance-api-demo',
      title: 'Regulatory Compliance API',
      description: 'Explore NovaFuse\'s Regulatory Compliance API for managing regulatory frameworks, requirements, and compliance status tracking.',
      icon: '📋',
      image: '/images/demos/regulatory-compliance-api-demo.png',
      url: '/api-demos/regulatory-compliance-api-demo',
      featured: true
    },
    {
      id: 'privacy-management-api-demo',
      title: 'Privacy Management API',
      description: 'Discover how NovaFuse\'s Privacy Management API helps you manage data privacy and compliance with regulations like GDPR, CCPA, and more.',
      icon: '🔒',
      image: '/images/demos/privacy-management-api-demo.png',
      url: '/api-demos/privacy-management-api-demo',
      featured: true
    },
    {
      id: 'security-assessment-api-demo',
      title: 'Security Assessment API',
      description: 'See how NovaFuse\'s Security Assessment API helps you manage security assessments and identify vulnerabilities in your systems.',
      icon: '🛡️',
      image: '/images/demos/security-assessment-api-demo.png',
      url: '/api-demos/security-assessment-api-demo'
    },
    {
      id: 'control-testing-api-demo',
      title: 'Control Testing API',
      description: 'Learn how NovaFuse\'s Control Testing API helps you manage control testing and ensure compliance with regulatory requirements.',
      icon: '✓',
      image: '/images/demos/control-testing-api-demo.png',
      url: '/api-demos/control-testing-api-demo'
    },
    {
      id: 'esg-api-demo',
      title: 'ESG API',
      description: 'Explore NovaFuse\'s ESG API for managing environmental, social, and governance data and reporting.',
      icon: '🌱',
      image: '/images/demos/esg-api-demo.png',
      url: '/api-demos/esg-api-demo',
      comingSoon: true
    },
    {
      id: 'compliance-automation-api-demo',
      title: 'Compliance Automation API',
      description: 'See how NovaFuse\'s Compliance Automation API helps you automate compliance processes and reduce manual effort.',
      icon: '⚙️',
      image: '/images/demos/compliance-automation-api-demo.png',
      url: '/api-demos/compliance-automation-api-demo',
      comingSoon: true
    }
  ];

  // Featured APIs
  const featuredApis = apiDemos.filter(demo => demo.featured);

  // Sidebar items
  const sidebarItems = [
    { type: 'category', label: 'API Demos', items: [
      { label: 'All APIs', href: '/api-demos' },
      { label: 'Featured APIs', href: '#featured' },
      { label: 'Coming Soon', href: '#coming-soon' }
    ]},
    { type: 'category', label: 'Featured APIs', items: 
      featuredApis.map(api => ({
        label: api.title,
        href: api.url
      }))
    },
    { type: 'category', label: 'Related Demos', items: [
      { label: 'Component Demos', href: '/component-demos' },
      { label: 'Partner Demos', href: '/partner-demos' },
      { label: 'Demo Hub', href: '/demo-hub' }
    ]}
  ];

  return (
    <PageWithSidebar
      title={pageProps.title}
      description={pageProps.description}
      sidebarItems={sidebarItems}
    >
      <div className="api-demos">
        {/* Header Section */}
        <div className="bg-gradient-to-r from-blue-900 to-indigo-900 text-white rounded-lg p-8 mb-8">
          <h1 className="text-3xl md:text-4xl font-bold mb-4 text-center">NovaFuse API Demos</h1>
          <p className="text-xl mb-6 text-center max-w-3xl mx-auto">
            Explore NovaFuse's comprehensive suite of GRC APIs that power our compliance automation platform.
          </p>
          <p className="text-center text-blue-200">
            All NovaFuse APIs are designed for seamless integration, high performance, and enterprise-grade security.
          </p>
        </div>

        {/* Featured APIs Section */}
        <div id="featured" className="mb-12 scroll-mt-16">
          <h2 className="text-2xl font-bold mb-6">Featured APIs</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {featuredApis.map(api => (
              <div key={api.id} className="bg-gray-800 rounded-lg overflow-hidden shadow-lg border border-gray-700 hover:border-blue-500 transition-all">
                {/* API Image/Icon */}
                <div className="h-40 bg-gray-900 relative">
                  {api.image ? (
                    <img 
                      src={api.image} 
                      alt={api.title} 
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center bg-gradient-to-r from-blue-900 to-purple-900">
                      <span className="text-5xl">{api.icon}</span>
                    </div>
                  )}
                  
                  {/* Featured Badge */}
                  <div className="absolute top-2 right-2 bg-blue-600 px-2 py-1 rounded text-xs font-bold">
                    FEATURED
                  </div>
                </div>
                
                {/* API Info */}
                <div className="p-5">
                  <h3 className="text-xl font-bold mb-2">{api.title}</h3>
                  <p className="text-gray-400 text-sm mb-4">{api.description}</p>
                  
                  {/* Action Button */}
                  <Link href={api.url} className="block w-full bg-blue-600 hover:bg-blue-700 text-white text-center py-2 rounded-lg font-medium">
                    View Demo
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Coming Soon Section */}
        <div id="coming-soon" className="mb-12 scroll-mt-16">
          <h2 className="text-2xl font-bold mb-6">Coming Soon</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {apiDemos.filter(demo => demo.comingSoon).map(api => (
              <div key={api.id} className="bg-gray-800 rounded-lg overflow-hidden shadow-lg border border-gray-700">
                {/* API Image/Icon */}
                <div className="h-40 bg-gray-900 relative">
                  {api.image ? (
                    <div className="relative w-full h-full">
                      <img 
                        src={api.image} 
                        alt={api.title} 
                        className="w-full h-full object-cover opacity-50"
                      />
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="bg-blue-900 bg-opacity-80 px-4 py-2 rounded-full text-sm font-bold">
                          COMING SOON
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="w-full h-full flex items-center justify-center bg-gradient-to-r from-blue-900 to-purple-900">
                      <span className="text-5xl opacity-50">{api.icon}</span>
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="bg-blue-900 bg-opacity-80 px-4 py-2 rounded-full text-sm font-bold">
                          COMING SOON
                        </div>
                      </div>
                    </div>
                  )}
                </div>
                
                {/* API Info */}
                <div className="p-5">
                  <h3 className="text-xl font-bold mb-2">{api.title}</h3>
                  <p className="text-gray-400 text-sm mb-4">{api.description}</p>
                  
                  {/* Action Button */}
                  <button className="block w-full bg-gray-700 text-gray-400 text-center py-2 rounded-lg font-medium cursor-not-allowed">
                    Coming Soon
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* All APIs Section */}
        <div>
          <h2 className="text-2xl font-bold mb-6">All APIs</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {apiDemos.map(api => (
              <div key={api.id} className="bg-gray-800 rounded-lg overflow-hidden shadow-lg border border-gray-700 hover:border-blue-500 transition-all">
                {/* API Info */}
                <div className="p-5">
                  <div className="flex items-center mb-3">
                    <span className="text-3xl mr-3">{api.icon}</span>
                    <h3 className="text-xl font-bold">{api.title}</h3>
                  </div>
                  
                  <p className="text-gray-400 text-sm mb-4">{api.description}</p>
                  
                  {/* Action Button */}
                  {api.comingSoon ? (
                    <button className="block w-full bg-gray-700 text-gray-400 text-center py-2 rounded-lg font-medium cursor-not-allowed">
                      Coming Soon
                    </button>
                  ) : (
                    <Link href={api.url} className="block w-full bg-blue-600 hover:bg-blue-700 text-white text-center py-2 rounded-lg font-medium">
                      View Demo
                    </Link>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* API Integration Section */}
        <div className="mt-12 bg-gray-800 rounded-lg p-6">
          <h2 className="text-2xl font-bold mb-4">API Integration</h2>
          <p className="text-gray-300 mb-6">
            NovaFuse APIs are designed for easy integration with your existing systems and workflows.
            Here's a simple example of how to use the Regulatory Compliance API:
          </p>
          
          <div className="bg-gray-900 p-4 rounded-lg mb-6">
            <pre className="text-sm text-gray-300 overflow-x-auto">
{`// Example: Get all regulatory frameworks
const response = await fetch('https://api.novafuse.io/compliance/regulatory/frameworks', {
  method: 'GET',
  headers: {
    'Authorization': \`Bearer \${apiKey}\`,
    'Content-Type': 'application/json'
  }
});

const data = await response.json();
console.log(data.data); // Array of regulatory frameworks`}
            </pre>
          </div>
          
          <div className="text-center">
            <Link href="/api-docs" className="text-blue-400 hover:text-blue-300">
              View full API documentation →
            </Link>
          </div>
        </div>

        {/* CTA Section */}
        <div className="mt-12 bg-gradient-to-r from-blue-900 to-indigo-900 rounded-lg p-8 text-center">
          <h2 className="text-2xl font-bold mb-3">Ready to Integrate with NovaFuse APIs?</h2>
          <p className="text-lg mb-6 max-w-3xl mx-auto">
            Join our partner ecosystem and start building with NovaFuse's powerful GRC APIs.
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Link href="/partner-program" className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-bold">
              Become a Partner
            </Link>
            <Link href="/api-docs" className="border border-white text-white px-6 py-3 rounded-lg font-bold hover:bg-white hover:text-blue-900">
              View API Documentation
            </Link>
          </div>
        </div>
      </div>
    </PageWithSidebar>
  );
}
