# CSFE NovaVision Dashboard Guide

This guide explains how to use the CSFE (Cyber-Safety Finance Equation) NovaVision Dashboard for visualizing CSFE calculations and financial predictions.

## Overview

The CSFE NovaVision Dashboard provides a comprehensive visualization interface for the CSFE engine, enabling users to:

- View CSFE calculations and components
- Explore financial market predictions
- Analyze optimal asset allocation
- Assess financial risks
- Project financial timelines
- Predict potential depressions in the 2027-2031 timeframe

## Dashboard Components

The dashboard consists of six main components:

1. **CSFE Overview**: Displays the CSFE value, performance factor, and component breakdown
2. **Market Predictions**: Shows market direction, strength, and asset class predictions
3. **Asset Allocation**: Displays optimal asset allocation based on CSFE value
4. **Risk Assessment**: Shows overall risk level and specific risk factors
5. **Timeline Predictions**: Projects CSFE value and market conditions over time
6. **Depression Prediction**: Focuses on depression prediction for the 2027-2031 timeframe

## Accessing the Dashboard

The dashboard can be accessed in two ways:

### 1. Standalone Mode

Run the dashboard server:

```bash
cd src/csfe
node ui/dashboard.js
```

Then open your browser and navigate to:

```
http://localhost:3002
```

### 2. Integrated Mode

When integrated with the NovaFuse platform, the dashboard can be accessed through the NovaVision Universal UI Connector:

```
http://[novafuse-host]/novavision/csfe
```

## Using the Dashboard

### Input Data

The dashboard allows you to input three types of data:

1. **Market Data**: Price, volume, liquidity, volatility, etc.
2. **Economic Data**: GDP, inflation, unemployment, interest rates, etc.
3. **Sentiment Data**: Retail sentiment, institutional positioning, media sentiment, etc.

You can either:

- Enter data manually in the input forms
- Load sample data using the "Load Sample Data" button
- Import data from NovaStore (when integrated with NovaFuse)

### Engine Selection

The dashboard supports three CSFE engine types:

1. **Standard CSFE**: The standard CSFE engine using the formula `CSFE = (M ⊗ E ⊕ S) × π10³`
2. **Trinity CSFE**: The Trinitarian version using the formula `CSFE_Trinity = πG + ϕD + (ℏ + c^-1)R`
3. **Trinity CSFE 18/82**: The Trinity CSFE with the 18/82 principle applied

Select the desired engine from the dropdown menu before calculating CSFE.

### Calculation

Click the "Calculate CSFE" button to perform the CSFE calculation. The dashboard will update all components with the calculation results.

## Dashboard Components in Detail

### 1. CSFE Overview

This component displays:

- **CSFE Value**: The calculated CSFE value
- **Performance Factor**: The performance improvement factor (3,142x)
- **Calculation Timestamp**: When the calculation was performed
- **Component Radar Chart**: Visualization of CSFE components (Market, Economic, Sentiment, Tensor, Fusion)

### 2. Market Predictions

This component displays:

- **Market Direction**: Overall market direction (Bullish, Bearish, Neutral)
- **Market Strength**: Strength of the market direction (0-100%)
- **Asset Class Predictions**: Expected returns for different asset classes (Equity, Fixed Income, Forex, Commodities, Crypto)

### 3. Asset Allocation

This component displays:

- **Risk Level**: Overall risk level (Aggressive, Moderate, Conservative)
- **Asset Allocation Chart**: Optimal allocation percentages for different asset classes

### 4. Risk Assessment

This component displays:

- **Overall Risk**: Overall risk level (0-100%)
- **Risk Factors**: Specific risk factors with their levels and impacts
- **Mitigation Strategies**: Strategies to mitigate identified risks

### 5. Timeline Predictions

This component displays:

- **Timeline Chart**: Projection of CSFE value over time (Short-term, Medium-term, Long-term)
- **Direction Projections**: Market direction projections for each timeframe
- **Confidence Levels**: Confidence in each projection

### 6. Depression Prediction

This component displays:

- **Depression Probability**: Probability of a depression in the 2027-2031 timeframe
- **Timeline Probability**: Probability distribution across the timeframe
- **Key Indicators**: Key indicators contributing to depression probability

## Integration with NovaFuse Platform

When integrated with the NovaFuse platform, the dashboard provides additional features:

- **Historical Data**: Access to historical CSFE data from NovaStore
- **Cross-Component Analysis**: Integration with CSDE and CSME data
- **AI Insights**: Integration with Nova Assist AI for enhanced insights
- **User Customization**: User-specific dashboard customization through NovaDNA

## Dashboard API

The dashboard provides a RESTful API for programmatic access:

### Get Dashboard Data

```
GET /api/dashboard
```

Returns dashboard configuration and available components.

### Calculate CSFE

```
POST /api/calculate
Content-Type: application/json

{
  "marketData": { ... },
  "economicData": { ... },
  "sentimentData": { ... },
  "engine": "standard" | "trinity" | "trinity-1882"
}
```

Calculates CSFE value and returns formatted dashboard data.

### Get Sample Data

```
GET /api/sample-data
```

Returns sample market, economic, and sentiment data for testing.

## Customizing the Dashboard

The dashboard can be customized in several ways:

### 1. Theme Customization

Edit the CSS file to customize the dashboard appearance:

```
src/csfe/ui/public/css/styles.css
```

### 2. Component Customization

Add or modify dashboard components by editing:

```
src/csfe/ui/public/index.html
src/csfe/ui/public/js/dashboard.js
```

### 3. Data Visualization Customization

Customize charts and visualizations by modifying:

```javascript
// In dashboard.js
function initializeCharts() {
  // Customize chart options here
}
```

## Best Practices

1. **Regular Updates**: Update input data regularly for accurate predictions
2. **Multiple Engines**: Compare results from different engines for comprehensive analysis
3. **Historical Comparison**: Compare current results with historical data for context
4. **Risk Focus**: Pay special attention to the Risk Assessment component for early warnings
5. **Timeline Tracking**: Track timeline predictions over time to identify trends

## Troubleshooting

### Dashboard Not Loading

- Check that the dashboard server is running
- Verify network connectivity
- Clear browser cache and reload

### Calculation Errors

- Verify input data format
- Check for missing required fields
- Try using sample data to test functionality

### Visualization Issues

- Update to the latest browser version
- Check for JavaScript console errors
- Verify Chart.js is loading correctly

## References

1. CSFE Engine Documentation
2. NovaVision Universal UI Connector Documentation
3. NovaFuse Platform Integration Guide
4. Depression Prediction Module Documentation
5. Comphyology (Ψᶜ) Framework Documentation
