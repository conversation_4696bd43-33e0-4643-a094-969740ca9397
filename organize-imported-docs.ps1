# Create the main organization directory
$baseDir = "d:\novafuse-api-superstore\coherence-reality-systems"
$importDir = Join-Path $baseDir "imported-docs"
New-Item -ItemType Directory -Path $importDir -Force

# Create subdirectories
$categories = @(
    "architecture",
    "research",
    "patents",
    "technical",
    "marketing",
    "documentation",
    "strategy",
    "testing",
    "development"
)

foreach ($category in $categories) {
    $path = Join-Path $importDir $category
    New-Item -ItemType Directory -Path $path -Force
}

# Get all MD files from novafuse-api-superstore
$mdFiles = Get-ChildItem -Path "d:\novafuse-api-superstore" -Recurse -File -Include *.md

# Function to categorize files
function Get-Category {
    param($fileName)
    
    if ($fileName -like "*ARCHITECTURE*" -or $fileName -like "*architecture*") {
        return "architecture"
    }
    elseif ($fileName -like "*research*" -or $fileName -like "*study*" -or $fileName -like "*analysis*") {
        return "research"
    }
    elseif ($fileName -like "*patent*" -or $fileName -like "*treatise*") {
        return "patents"
    }
    elseif ($fileName -like "*technical*" -or $fileName -like "*implementation*" -or $fileName -like "*analysis*") {
        return "technical"
    }
    elseif ($fileName -like "*marketing*" -or $fileName -like "*briefing*" -or $fileName -like "*demo*") {
        return "marketing"
    }
    elseif ($fileName -like "*strategy*" -or $fileName -like "*plan*" -or $fileName -like "*framework*") {
        return "strategy"
    }
    elseif ($fileName -like "*test*" -or $fileName -like "*validation*") {
        return "testing"
    }
    elseif ($fileName -like "*documentation*" -or $fileName -like "*guide.md" -or $fileName -like "*spec.md") {
        return "documentation"
    }
    else {
        return "development"
    }
}

# Create a log file
$logFile = Join-Path $importDir "organization-log.txt"
"Organizing imported documentation" | Out-File -FilePath $logFile

# Move and organize files
foreach ($file in $mdFiles) {
    # Skip files in coherence-reality-systems directory
    if ($file.DirectoryName -like "*coherence-reality-systems*") {
        continue
    }
    
    $category = Get-Category $file.Name
    $targetDir = Join-Path $importDir $category
    $targetPath = Join-Path $targetDir $file.Name
    
    Copy-Item -Path $file.FullName -Destination $targetPath -Force
    "$($file.Name) copied to $category" | Out-File -FilePath $logFile -Append
}

"Organization completed" | Out-File -FilePath $logFile -Append
