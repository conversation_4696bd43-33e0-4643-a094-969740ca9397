# Treatise Documentation - Style Guide

## Document Standards
- **Format**: 8.5" x 11" (Letter) or A4
- **Theme**: Professional, modern, and approachable
- **Font**: Inter (primary), <PERSON>o (secondary)
- **Color Palette**: 
  - Primary: #2A5CAA (Navy Blue)
  - Secondary: #4A90E2 (Sky Blue)
  - Accent: #FF6B6B (Coral)
  - Text: #333333 (Dark Gray)
  - Background: #FFFFFF (White) / #F8F9FA (Light Gray)

## Diagram Requirements
- **Format**: High-res PNG or SVG
- **Style**: Flat design with subtle shadows
- **Icons**: Consistent style (line icons preferred)
- **Color Usage**:
  - Primary color for main elements
  - Secondary for highlights
  - Grayscale for supporting elements
- **Watermark**: "For Educational Purposes" (bottom right, subtle)

## Naming Convention
`[Category]_[Concept]_[Version].[ext]`
Example: `Architecture_System-Overview_v1.0.png`

## Visual Hierarchy
1. Main concept (largest, boldest)
2. Supporting elements (medium weight)
3. Explanatory text (smallest)
4. Source/citation (small, italic)

## Version Control
- Use date-based versioning (YYYYMMDD)
- Maintain CHANGELOG.md with visual updates
- Include "Last Updated" timestamp

## Content Guidelines
- Focus on concepts, not implementation
- Use analogies and examples
- Include "Key Takeaways" callouts
- Add "Learn More" references
- Use sidebars for technical details

## Best Practices
- Maintain brand consistency
- Use white space effectively
- Ensure accessibility (color contrast, alt text)
- Include interactive elements where possible (in digital version)
- Add QR codes for additional resources
