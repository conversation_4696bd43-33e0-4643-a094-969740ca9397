/**
 * NovaCore Regulatory Change Model
 * 
 * This model defines the schema for regulatory changes in the NovaPulse module.
 * NovaPulse is the Universal Regulatory Compliance Monitoring System (URCMS) component
 * of the NovaFuse Cyber-Safety Platform.
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

// Define impact assessment schema
const impactAssessmentSchema = new Schema({
  level: { 
    type: String, 
    enum: ['none', 'low', 'medium', 'high', 'critical'], 
    required: true 
  },
  description: { 
    type: String, 
    required: true 
  },
  affectedAreas: [{ 
    type: String, 
    trim: true 
  }],
  affectedControls: [{
    frameworkId: { 
      type: Schema.Types.ObjectId, 
      ref: 'Framework' 
    },
    controlId: { 
      type: String, 
      trim: true 
    }
  }],
  affectedPolicies: [{
    policyId: { 
      type: Schema.Types.ObjectId, 
      ref: 'Policy' 
    },
    policyName: { 
      type: String, 
      trim: true 
    }
  }],
  affectedProcesses: [{
    processId: { 
      type: Schema.Types.ObjectId 
    },
    processName: { 
      type: String, 
      trim: true 
    }
  }],
  requiredActions: [{
    type: { 
      type: String, 
      enum: [
        'policy_update', 
        'control_implementation', 
        'process_change', 
        'training', 
        'technical_change', 
        'documentation', 
        'other'
      ], 
      required: true 
    },
    description: { 
      type: String, 
      required: true 
    },
    priority: { 
      type: String, 
      enum: ['low', 'medium', 'high', 'critical'], 
      default: 'medium' 
    },
    assignedTo: { 
      type: Schema.Types.ObjectId, 
      ref: 'User' 
    },
    dueDate: { 
      type: Date 
    },
    status: { 
      type: String, 
      enum: ['pending', 'in_progress', 'completed', 'deferred'], 
      default: 'pending' 
    }
  }],
  notes: { 
    type: String 
  },
  assessedBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'User' 
  },
  assessedAt: { 
    type: Date 
  }
}, { _id: false });

// Define notification schema
const notificationSchema = new Schema({
  id: { 
    type: String, 
    required: true, 
    trim: true 
  },
  type: { 
    type: String, 
    enum: ['email', 'in_app', 'webhook', 'sms'], 
    required: true 
  },
  recipients: [{ 
    type: Schema.Types.Mixed 
  }],
  sentAt: { 
    type: Date 
  },
  status: { 
    type: String, 
    enum: ['pending', 'sent', 'failed'], 
    default: 'pending' 
  },
  error: { 
    type: String 
  }
}, { _id: false });

// Define regulatory change schema
const regulatoryChangeSchema = new Schema({
  regulationId: { 
    type: Schema.Types.ObjectId, 
    ref: 'Regulation', 
    required: true 
  },
  regulationName: { 
    type: String, 
    required: true, 
    trim: true 
  },
  type: { 
    type: String, 
    enum: [
      'new_regulation', 
      'amendment', 
      'repeal', 
      'interpretation', 
      'guidance', 
      'enforcement_action'
    ], 
    required: true 
  },
  title: { 
    type: String, 
    required: true, 
    trim: true 
  },
  description: { 
    type: String, 
    required: true 
  },
  summary: { 
    type: String, 
    required: true 
  },
  source: { 
    type: String, 
    required: true, 
    trim: true 
  },
  sourceUrl: { 
    type: String, 
    trim: true 
  },
  publicationDate: { 
    type: Date, 
    required: true 
  },
  effectiveDate: { 
    type: Date 
  },
  complianceDeadline: { 
    type: Date 
  },
  jurisdiction: {
    country: { 
      type: String, 
      trim: true 
    },
    region: { 
      type: String, 
      trim: true 
    },
    state: { 
      type: String, 
      trim: true 
    },
    isGlobal: { 
      type: Boolean, 
      default: false 
    }
  },
  category: { 
    type: String, 
    enum: [
      'privacy', 
      'security', 
      'financial', 
      'healthcare', 
      'environmental', 
      'industry-specific', 
      'general'
    ], 
    required: true 
  },
  status: { 
    type: String, 
    enum: [
      'draft', 
      'published', 
      'under_review', 
      'assessed', 
      'implementation_planned', 
      'implemented', 
      'verified', 
      'archived'
    ], 
    default: 'draft' 
  },
  priority: { 
    type: String, 
    enum: ['low', 'medium', 'high', 'critical'], 
    default: 'medium' 
  },
  applicability: {
    industries: [{ 
      type: String, 
      trim: true 
    }],
    regions: [{ 
      type: String, 
      trim: true 
    }],
    organizationTypes: [{ 
      type: String, 
      trim: true 
    }],
    dataTypes: [{ 
      type: String, 
      trim: true 
    }],
    thresholds: [{
      type: { 
        type: String, 
        trim: true 
      },
      value: { 
        type: Schema.Types.Mixed 
      },
      description: { 
        type: String, 
        trim: true 
      }
    }]
  },
  changes: [{
    requirementId: { 
      type: String, 
      trim: true 
    },
    type: { 
      type: String, 
      enum: ['addition', 'modification', 'removal', 'clarification'], 
      required: true 
    },
    description: { 
      type: String, 
      required: true 
    },
    previousText: { 
      type: String 
    },
    newText: { 
      type: String 
    },
    section: { 
      type: String, 
      trim: true 
    },
    article: { 
      type: String, 
      trim: true 
    }
  }],
  impactAssessment: impactAssessmentSchema,
  implementationStatus: {
    status: { 
      type: String, 
      enum: [
        'not_started', 
        'in_progress', 
        'completed', 
        'verified', 
        'not_applicable'
      ], 
      default: 'not_started' 
    },
    progress: { 
      type: Number, 
      min: 0, 
      max: 100, 
      default: 0 
    },
    startDate: { 
      type: Date 
    },
    completionDate: { 
      type: Date 
    },
    verificationDate: { 
      type: Date 
    },
    notes: { 
      type: String 
    }
  },
  relatedWorkflows: [{
    workflowId: { 
      type: Schema.Types.ObjectId, 
      ref: 'Workflow' 
    },
    workflowName: { 
      type: String, 
      trim: true 
    },
    status: { 
      type: String, 
      trim: true 
    }
  }],
  notifications: [notificationSchema],
  tags: [{ 
    type: String, 
    trim: true 
  }],
  attachments: [{
    name: { 
      type: String, 
      required: true, 
      trim: true 
    },
    type: { 
      type: String, 
      trim: true 
    },
    url: { 
      type: String, 
      trim: true 
    },
    uploadedBy: { 
      type: Schema.Types.ObjectId, 
      ref: 'User' 
    },
    uploadedAt: { 
      type: Date 
    }
  }],
  metadata: {
    type: Map,
    of: Schema.Types.Mixed
  },
  createdBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'User' 
  },
  updatedBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'User' 
  },
  organizationId: { 
    type: Schema.Types.ObjectId, 
    ref: 'Organization', 
    required: true 
  }
}, {
  timestamps: true,
  versionKey: true
});

// Add indexes
regulatoryChangeSchema.index({ regulationId: 1 });
regulatoryChangeSchema.index({ regulationName: 1 });
regulatoryChangeSchema.index({ type: 1 });
regulatoryChangeSchema.index({ publicationDate: 1 });
regulatoryChangeSchema.index({ effectiveDate: 1 });
regulatoryChangeSchema.index({ complianceDeadline: 1 });
regulatoryChangeSchema.index({ 'jurisdiction.country': 1 });
regulatoryChangeSchema.index({ 'jurisdiction.region': 1 });
regulatoryChangeSchema.index({ category: 1 });
regulatoryChangeSchema.index({ status: 1 });
regulatoryChangeSchema.index({ priority: 1 });
regulatoryChangeSchema.index({ 'applicability.industries': 1 });
regulatoryChangeSchema.index({ 'applicability.regions': 1 });
regulatoryChangeSchema.index({ 'applicability.organizationTypes': 1 });
regulatoryChangeSchema.index({ 'applicability.dataTypes': 1 });
regulatoryChangeSchema.index({ 'changes.requirementId': 1 });
regulatoryChangeSchema.index({ 'implementationStatus.status': 1 });
regulatoryChangeSchema.index({ tags: 1 });
regulatoryChangeSchema.index({ organizationId: 1 });
regulatoryChangeSchema.index({ createdAt: 1 });
regulatoryChangeSchema.index({ updatedAt: 1 });

// Add methods
regulatoryChangeSchema.methods.isApplicable = function(criteria) {
  if (!criteria) {
    return true;
  }
  
  const { industry, region, organizationType, dataTypes } = criteria;
  
  // Check industry applicability
  if (industry && this.applicability.industries && this.applicability.industries.length > 0) {
    if (!this.applicability.industries.includes(industry)) {
      return false;
    }
  }
  
  // Check region applicability
  if (region && this.applicability.regions && this.applicability.regions.length > 0) {
    if (!this.applicability.regions.includes(region) && !this.jurisdiction.isGlobal) {
      return false;
    }
  }
  
  // Check organization type applicability
  if (organizationType && this.applicability.organizationTypes && this.applicability.organizationTypes.length > 0) {
    if (!this.applicability.organizationTypes.includes(organizationType)) {
      return false;
    }
  }
  
  // Check data type applicability
  if (dataTypes && dataTypes.length > 0 && this.applicability.dataTypes && this.applicability.dataTypes.length > 0) {
    const hasMatchingDataType = dataTypes.some(dataType => 
      this.applicability.dataTypes.includes(dataType)
    );
    
    if (!hasMatchingDataType) {
      return false;
    }
  }
  
  return true;
};

regulatoryChangeSchema.methods.isOverdue = function() {
  if (!this.complianceDeadline) {
    return false;
  }
  
  const now = new Date();
  return now > this.complianceDeadline && 
         this.implementationStatus.status !== 'completed' && 
         this.implementationStatus.status !== 'verified';
};

regulatoryChangeSchema.methods.getDaysUntilDeadline = function() {
  if (!this.complianceDeadline) {
    return null;
  }
  
  const now = new Date();
  const deadlineDate = new Date(this.complianceDeadline);
  const diffTime = deadlineDate - now;
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  return diffDays;
};

regulatoryChangeSchema.methods.updateImplementationProgress = function() {
  if (!this.impactAssessment || !this.impactAssessment.requiredActions || this.impactAssessment.requiredActions.length === 0) {
    return 0;
  }
  
  const totalActions = this.impactAssessment.requiredActions.length;
  const completedActions = this.impactAssessment.requiredActions.filter(
    action => action.status === 'completed'
  ).length;
  
  const progress = Math.round((completedActions / totalActions) * 100);
  this.implementationStatus.progress = progress;
  
  // Update status based on progress
  if (progress === 0) {
    this.implementationStatus.status = 'not_started';
  } else if (progress === 100) {
    this.implementationStatus.status = 'completed';
    this.implementationStatus.completionDate = new Date();
  } else {
    this.implementationStatus.status = 'in_progress';
    if (!this.implementationStatus.startDate) {
      this.implementationStatus.startDate = new Date();
    }
  }
  
  return progress;
};

// Add statics
regulatoryChangeSchema.statics.findByOrganization = function(organizationId) {
  return this.find({ organizationId });
};

regulatoryChangeSchema.statics.findByRegulation = function(regulationId) {
  return this.find({ regulationId });
};

regulatoryChangeSchema.statics.findPending = function(organizationId) {
  return this.find({
    organizationId,
    'implementationStatus.status': { $in: ['not_started', 'in_progress'] }
  });
};

regulatoryChangeSchema.statics.findOverdue = function(organizationId) {
  const now = new Date();
  
  return this.find({
    organizationId,
    complianceDeadline: { $lt: now },
    'implementationStatus.status': { $in: ['not_started', 'in_progress'] }
  });
};

regulatoryChangeSchema.statics.findUpcoming = function(organizationId, days = 30) {
  const now = new Date();
  const futureDate = new Date();
  futureDate.setDate(now.getDate() + days);
  
  return this.find({
    organizationId,
    complianceDeadline: { $gte: now, $lte: futureDate },
    'implementationStatus.status': { $in: ['not_started', 'in_progress'] }
  });
};

regulatoryChangeSchema.statics.findByImpactLevel = function(organizationId, level) {
  return this.find({
    organizationId,
    'impactAssessment.level': level
  });
};

regulatoryChangeSchema.statics.findByApplicability = function(organizationId, criteria) {
  const query = { organizationId };
  
  if (criteria.industry) {
    query['applicability.industries'] = criteria.industry;
  }
  
  if (criteria.region) {
    query['$or'] = [
      { 'applicability.regions': criteria.region },
      { 'jurisdiction.isGlobal': true }
    ];
  }
  
  if (criteria.organizationType) {
    query['applicability.organizationTypes'] = criteria.organizationType;
  }
  
  if (criteria.dataTypes && criteria.dataTypes.length > 0) {
    query['applicability.dataTypes'] = { $in: criteria.dataTypes };
  }
  
  return this.find(query);
};

// Create model
const RegulatoryChange = mongoose.model('RegulatoryChange', regulatoryChangeSchema);

module.exports = RegulatoryChange;
