import { useState, useEffect } from "react";
import Head from "next/head";
import { useRouter } from "next/router";

export default function PartnerAnalytics() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [dateRange, setDateRange] = useState("30d");
  const [apiUsage, setApiUsage] = useState([]);
  const [topEndpoints, setTopEndpoints] = useState([]);
  const [revenueData, setRevenueData] = useState({
    total: 0,
    byDay: [],
    byEndpoint: []
  });

  useEffect(() => {
    // In a real implementation, this would fetch from an API
    setTimeout(() => {
      // Generate mock data based on date range
      const days = dateRange === "7d" ? 7 : dateRange === "30d" ? 30 : 90;
      const mockApiUsage = [];
      const mockRevenueByDay = [];
      
      let totalRevenue = 0;
      let totalCalls = 0;
      
      for (let i = 0; i < days; i++) {
        const date = new Date();
        date.setDate(date.getDate() - (days - i - 1));
        
        const calls = Math.floor(Math.random() * 1000) + 500;
        const revenue = calls * 0.0001 * 100; // $0.0001 per call, with 100% revenue share for simplicity
        
        totalCalls += calls;
        totalRevenue += revenue;
        
        mockApiUsage.push({
          date: date.toISOString().split('T')[0],
          calls: calls,
          successRate: 95 + Math.random() * 5,
          avgResponseTime: 50 + Math.random() * 100
        });
        
        mockRevenueByDay.push({
          date: date.toISOString().split('T')[0],
          revenue: revenue
        });
      }
      
      // Mock top endpoints
      const mockTopEndpoints = [
        { 
          endpoint: "/governance/board/meetings", 
          calls: Math.floor(totalCalls * 0.3),
          revenue: totalRevenue * 0.3,
          avgResponseTime: 75
        },
        { 
          endpoint: "/governance/policies", 
          calls: Math.floor(totalCalls * 0.25),
          revenue: totalRevenue * 0.25,
          avgResponseTime: 68
        },
        { 
          endpoint: "/governance/compliance/reports", 
          calls: Math.floor(totalCalls * 0.2),
          revenue: totalRevenue * 0.2,
          avgResponseTime: 82
        },
        { 
          endpoint: "/governance/board/resolutions", 
          calls: Math.floor(totalCalls * 0.15),
          revenue: totalRevenue * 0.15,
          avgResponseTime: 63
        },
        { 
          endpoint: "/governance/committees", 
          calls: Math.floor(totalCalls * 0.1),
          revenue: totalRevenue * 0.1,
          avgResponseTime: 91
        }
      ];
      
      setApiUsage(mockApiUsage);
      setTopEndpoints(mockTopEndpoints);
      setRevenueData({
        total: totalRevenue,
        byDay: mockRevenueByDay,
        byEndpoint: mockTopEndpoints
      });
      
      setLoading(false);
    }, 1000);
  }, [dateRange]);

  const handleDateRangeChange = (range) => {
    setLoading(true);
    setDateRange(range);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Head>
        <title>NovaFuse Partner Analytics</title>
        <meta name="description" content="NovaFuse Partner Analytics Dashboard" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 py-6 sm:px-6 lg:px-8 flex justify-between items-center">
          <h1 className="text-3xl font-bold text-gray-900">Partner Analytics</h1>
          <div className="flex items-center space-x-4">
            <button 
              onClick={() => router.push("/partner-portal")}
              className="border border-blue-600 text-blue-600 px-4 py-2 rounded hover:bg-blue-50"
            >
              Back to Portal
            </button>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Date Range Selector */}
        <div className="bg-white p-4 rounded-lg shadow mb-6 flex justify-between items-center">
          <h2 className="text-lg font-semibold">Analytics Overview</h2>
          <div className="flex space-x-2">
            <button 
              onClick={() => handleDateRangeChange("7d")}
              className={`px-3 py-1 rounded text-sm ${
                dateRange === "7d" 
                  ? "bg-blue-600 text-white" 
                  : "bg-gray-200 text-gray-700 hover:bg-gray-300"
              }`}
            >
              Last 7 Days
            </button>
            <button 
              onClick={() => handleDateRangeChange("30d")}
              className={`px-3 py-1 rounded text-sm ${
                dateRange === "30d" 
                  ? "bg-blue-600 text-white" 
                  : "bg-gray-200 text-gray-700 hover:bg-gray-300"
              }`}
            >
              Last 30 Days
            </button>
            <button 
              onClick={() => handleDateRangeChange("90d")}
              className={`px-3 py-1 rounded text-sm ${
                dateRange === "90d" 
                  ? "bg-blue-600 text-white" 
                  : "bg-gray-200 text-gray-700 hover:bg-gray-300"
              }`}
            >
              Last 90 Days
            </button>
          </div>
        </div>

        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : (
          <>
            {/* Summary Cards */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
              <div className="bg-white p-6 rounded-lg shadow">
                <h3 className="text-sm font-medium text-gray-500 mb-1">Total API Calls</h3>
                <p className="text-3xl font-bold text-gray-900">
                  {apiUsage.reduce((sum, day) => sum + day.calls, 0).toLocaleString()}
                </p>
                <div className="mt-2 text-sm text-gray-500">
                  For the last {dateRange === "7d" ? "7" : dateRange === "30d" ? "30" : "90"} days
                </div>
              </div>
              
              <div className="bg-white p-6 rounded-lg shadow">
                <h3 className="text-sm font-medium text-gray-500 mb-1">Total Revenue</h3>
                <p className="text-3xl font-bold text-gray-900">
                  ${revenueData.total.toFixed(2)}
                </p>
                <div className="mt-2 text-sm text-gray-500">
                  85% revenue share
                </div>
              </div>
              
              <div className="bg-white p-6 rounded-lg shadow">
                <h3 className="text-sm font-medium text-gray-500 mb-1">Avg. Success Rate</h3>
                <p className="text-3xl font-bold text-gray-900">
                  {(apiUsage.reduce((sum, day) => sum + day.successRate, 0) / apiUsage.length).toFixed(2)}%
                </p>
                <div className="mt-2 text-sm text-green-500">
                  ↑ 1.2% from previous period
                </div>
              </div>
              
              <div className="bg-white p-6 rounded-lg shadow">
                <h3 className="text-sm font-medium text-gray-500 mb-1">Avg. Response Time</h3>
                <p className="text-3xl font-bold text-gray-900">
                  {(apiUsage.reduce((sum, day) => sum + day.avgResponseTime, 0) / apiUsage.length).toFixed(0)}ms
                </p>
                <div className="mt-2 text-sm text-green-500">
                  ↓ 5ms from previous period
                </div>
              </div>
            </div>
            
            {/* API Usage Chart */}
            <div className="bg-white p-6 rounded-lg shadow mb-6">
              <h3 className="text-lg font-semibold mb-4">API Usage Over Time</h3>
              <div className="h-64 relative">
                {/* This would be a real chart in production */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-gray-400 text-center">
                    <p>Chart visualization would be implemented here</p>
                    <p className="text-sm mt-2">Using a library like Chart.js or Recharts</p>
                  </div>
                </div>
                <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-blue-50 to-transparent opacity-50"></div>
              </div>
              <div className="flex justify-between mt-4 text-xs text-gray-500">
                {apiUsage.filter((_, i) => i % Math.floor(apiUsage.length / 5) === 0).map((day, i) => (
                  <div key={i}>{day.date}</div>
                ))}
              </div>
            </div>
            
            {/* Top Endpoints */}
            <div className="bg-white p-6 rounded-lg shadow mb-6">
              <h3 className="text-lg font-semibold mb-4">Top Endpoints</h3>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead>
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Endpoint</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Calls</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Revenue</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Avg. Response Time</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {topEndpoints.map((endpoint, index) => (
                      <tr key={index}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{endpoint.endpoint}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{endpoint.calls.toLocaleString()}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${endpoint.revenue.toFixed(2)}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{endpoint.avgResponseTime}ms</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
            
            {/* Revenue Distribution */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-white p-6 rounded-lg shadow">
                <h3 className="text-lg font-semibold mb-4">Revenue by Day</h3>
                <div className="h-64 relative">
                  {/* This would be a real chart in production */}
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-gray-400 text-center">
                      <p>Revenue chart would be implemented here</p>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="bg-white p-6 rounded-lg shadow">
                <h3 className="text-lg font-semibold mb-4">Revenue by Endpoint</h3>
                <div className="h-64 relative">
                  {/* This would be a real chart in production */}
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-gray-400 text-center">
                      <p>Pie chart would be implemented here</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </>
        )}
      </main>

      <footer className="bg-gray-800 text-white py-12 mt-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mt-8 pt-8 border-t border-gray-700 text-center text-gray-400">
            <p>&copy; {new Date().getFullYear()} NovaFuse. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
