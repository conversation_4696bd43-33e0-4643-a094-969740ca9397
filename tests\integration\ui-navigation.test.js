/**
 * NovaFuse UI Integration Tests - Navigation and Page Layout
 *
 * These tests validate the integration between navigation components,
 * page layouts, and routing in the NovaFuse UI.
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { NavigationProvider } from '../../components/NavigationContext';
import MainLayout from '../../components/MainLayout';
import Navigation from '../../components/Navigation';
import PageWithSidebar from '../../components/PageWithSidebar';

// Create a mock router context
const RouterContext = React.createContext({});

// Create a mock router
const createMockRouter = (pathname = '/') => ({
  pathname,
  route: pathname,
  query: {},
  asPath: pathname,
  basePath: '',
  back: jest.fn(),
  beforePopState: jest.fn(),
  forward: jest.fn(),
  push: jest.fn(),
  prefetch: jest.fn(() => Promise.resolve()),
  replace: jest.fn(),
  reload: jest.fn(),
  events: {
    on: jest.fn(),
    off: jest.fn(),
    emit: jest.fn()
  },
  isFallback: false,
  isLocaleDomain: false,
  isReady: true,
  isPreview: false
});

// Mock the FloatingNovaConcierge component
jest.mock('../../components/FloatingNovaConcierge', () => {
  return function MockFloatingNovaConcierge() {
    return <div data-testid="mock-nova-concierge">Mock NovaConcierge</div>;
  };
});

// Mock Next.js Link component
jest.mock('next/link', () => {
  return ({ children, href, className, onClick }) => {
    return (
      <a href={href} className={className} onClick={onClick}>
        {children}
      </a>
    );
  };
});

// Mock Next.js Head component
jest.mock('next/head', () => {
  return function MockHead({ children }) {
    return <div data-testid="mock-head">{children}</div>;
  };
});

describe('UI Navigation Integration Tests', () => {
  it('integrates Navigation with MainLayout correctly', () => {
    const mockRouter = createMockRouter();

    render(
      <RouterContext.Provider value={mockRouter}>
        <NavigationProvider>
          <MainLayout>
            <div data-testid="test-content">Test Content</div>
          </MainLayout>
        </NavigationProvider>
      </RouterContext.Provider>
    );

    // Check if the Navigation component is rendered within MainLayout
    expect(screen.getAllByText('NovaFuse API Superstore')[0]).toBeInTheDocument();
    expect(screen.getByText('Home')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Products' })).toBeInTheDocument();

    // Check if the content is rendered
    expect(screen.getByTestId('test-content')).toBeInTheDocument();

    // Check if the footer is rendered
    expect(screen.getByText('© 2025 NovaFuse. All rights reserved.')).toBeInTheDocument();

    // Check if the NovaConcierge component is rendered
    expect(screen.getByTestId('mock-nova-concierge')).toBeInTheDocument();
  });

  it('navigates between pages using the Navigation component', async () => {
    const mockRouter = createMockRouter();
    mockRouter.push = jest.fn();

    render(
      <RouterContext.Provider value={mockRouter}>
        <NavigationProvider>
          <MainLayout>
            <div data-testid="test-content">Test Content</div>
          </MainLayout>
        </NavigationProvider>
      </RouterContext.Provider>
    );

    // Click on the About Us link
    const aboutLink = screen.getByText('About Us');
    fireEvent.click(aboutLink);

    // Since we're using a mock implementation, we can't actually test the navigation
    // but we can verify the link has the correct href
    expect(aboutLink).toHaveAttribute('href', '/about');
  });

  it('integrates PageWithSidebar with NavigationContext correctly', () => {
    const mockRouter = createMockRouter('/partner-empowerment');

    // Define sidebar items
    const sidebarItems = [
      { label: 'Overview', href: '/partner-empowerment' },
      { label: 'Benefits', href: '/partner-empowerment/benefits' },
      { label: 'Case Studies', href: '/partner-empowerment/case-studies' },
      { label: 'Join Program', href: '/partner-empowerment/join' }
    ];

    render(
      <RouterContext.Provider value={mockRouter}>
        <NavigationProvider>
          <MainLayout>
            <PageWithSidebar
              title="Partner Empowerment"
              sidebarItems={sidebarItems}
            >
              <div data-testid="page-content">Partner Empowerment Content</div>
            </PageWithSidebar>
          </MainLayout>
        </NavigationProvider>
      </RouterContext.Provider>
    );

    // Check if the Navigation component is rendered
    expect(screen.getAllByText('NovaFuse API Superstore')[0]).toBeInTheDocument();

    // Check if the PageWithSidebar title is rendered
    expect(screen.getAllByText('Partner Empowerment')[0]).toBeInTheDocument();

    // Check if the sidebar items are rendered
    const sidebarLinks = screen.getAllByRole('listitem');
    expect(sidebarLinks.length).toBeGreaterThanOrEqual(4); // At least 4 sidebar items

    // Check if the page content is rendered
    expect(screen.getByTestId('page-content')).toBeInTheDocument();

    // Verify the page content is displayed correctly
    expect(screen.getByTestId('page-content')).toHaveTextContent('Partner Empowerment Content');
  });

  it('handles dropdown navigation correctly', async () => {
    const mockRouter = createMockRouter();

    render(
      <RouterContext.Provider value={mockRouter}>
        <NavigationProvider>
          <MainLayout>
            <div data-testid="test-content">Test Content</div>
          </MainLayout>
        </NavigationProvider>
      </RouterContext.Provider>
    );

    // Click on the Products dropdown
    const productsButton = screen.getByRole('button', { name: 'Products' });
    fireEvent.click(productsButton);

    // Check if the dropdown items are displayed
    expect(screen.getByText('NovaConnect UAC')).toBeInTheDocument();
    expect(screen.getByText('UAC FAQ')).toBeInTheDocument();
    expect(screen.getByText('NovaGRC Suite')).toBeInTheDocument();

    // Check if the dropdown links are properly rendered
    const uacLink = screen.getByText('NovaConnect UAC');
    expect(uacLink).toBeInTheDocument();
  });

  it('applies active styles to current route in both Navigation and PageWithSidebar', () => {
    const mockRouter = createMockRouter('/partner-empowerment');

    // Define sidebar items
    const sidebarItems = [
      { label: 'Overview', href: '/partner-empowerment' },
      { label: 'Benefits', href: '/partner-empowerment/benefits' },
      { label: 'Case Studies', href: '/partner-empowerment/case-studies' },
      { label: 'Join Program', href: '/partner-empowerment/join' }
    ];

    render(
      <RouterContext.Provider value={mockRouter}>
        <NavigationProvider>
          <MainLayout>
            <PageWithSidebar
              title="Partner Empowerment"
              sidebarItems={sidebarItems}
            >
              <div data-testid="page-content">Partner Empowerment Content</div>
            </PageWithSidebar>
          </MainLayout>
        </NavigationProvider>
      </RouterContext.Provider>
    );

    // Check if the Partner Empowerment link in the Navigation has the correct href
    const partnerEmpowermentLinks = screen.getAllByText('Partner Empowerment');
    // Find the link that's in the navigation
    const navLinks = Array.from(partnerEmpowermentLinks).filter(el =>
      el.closest('nav') !== null || (el.tagName.toLowerCase() === 'a' && el.getAttribute('href') === '/partner-empowerment')
    );

    // Verify we found at least one navigation link
    expect(navLinks.length).toBeGreaterThan(0);

    // Check if the sidebar has the correct structure
    const sidebar = screen.getAllByRole('list')[0];
    expect(sidebar).toBeInTheDocument();
  });
});
