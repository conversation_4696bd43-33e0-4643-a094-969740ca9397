/**
 * CSDE Test Program
 * 
 * This program demonstrates the CSDE Engine by processing a sample security event
 * and displaying the results.
 */

#include "csde_engine.h"
#include <iostream>
#include <iomanip>
#include <string>
#include <unordered_map>

// Convert remediation type to string
std::string remediation_type_to_string(RemediationAction::Type type) {
    switch (type) {
        case RemediationAction::ISOLATE:
            return "ISOLATE";
        case RemediationAction::BLOCK:
            return "BLOCK";
        case RemediationAction::PATCH:
            return "PATCH";
        case RemediationAction::ALERT:
            return "ALERT";
        case RemediationAction::LOG:
            return "LOG";
        default:
            return "UNKNOWN";
    }
}

int main() {
    // Print header
    std::cout << "CSDE Test Program" << std::endl;
    std::cout << "================" << std::endl;
    
    // Create CSDE Engine
    CSDEEngine engine;
    
    // Initialize engine
    if (!engine.initialize()) {
        std::cerr << "Failed to initialize CSDE Engine: " << engine.get_last_error() << std::endl;
        return 1;
    }
    
    std::cout << "CSDE Engine initialized successfully." << std::endl;
    
    // Create sample data
    std::unordered_map<std::string, float> compliance_data = {
        {"ID.AM-1", 0.85f},
        {"PR.AC-1", 0.90f},
        {"DE.CM-1", 0.75f},
        {"RS.RP-1", 0.80f},
        {"RC.RP-1", 0.70f}
    };
    
    std::unordered_map<std::string, float> cloud_data = {
        {"compute_instances_secure_boot", 0.95f},
        {"storage_bucket_encryption", 0.90f},
        {"iam_service_account_key_rotation", 0.85f},
        {"networking_vpc_flow_logs", 0.80f},
        {"security_command_center_tier", 0.75f}
    };
    
    std::unordered_map<std::string, float> security_data = {
        {"malware_detection", 0.95f},
        {"phishing_detection", 0.90f},
        {"data_exfiltration_detection", 0.85f},
        {"privilege_escalation_detection", 0.80f},
        {"insider_threat_detection", 0.75f}
    };
    
    // Print input data
    std::cout << "\nInput Data:" << std::endl;
    std::cout << "-----------" << std::endl;
    
    std::cout << "Compliance Data (N):" << std::endl;
    for (const auto& pair : compliance_data) {
        std::cout << "  " << pair.first << ": " << pair.second << std::endl;
    }
    
    std::cout << "\nCloud Data (G):" << std::endl;
    for (const auto& pair : cloud_data) {
        std::cout << "  " << pair.first << ": " << pair.second << std::endl;
    }
    
    std::cout << "\nSecurity Data (C):" << std::endl;
    for (const auto& pair : security_data) {
        std::cout << "  " << pair.first << ": " << pair.second << std::endl;
    }
    
    // Calculate CSDE
    std::cout << "\nCalculating CSDE..." << std::endl;
    
    // Run multiple iterations to measure performance
    const int iterations = 1000;
    double total_time = 0.0;
    
    for (int i = 0; i < iterations; i++) {
        CSDEResult result = engine.calculate_csde(compliance_data, cloud_data, security_data);
        total_time += result.processing_time_ms;
    }
    
    // Calculate average processing time
    double avg_time = total_time / iterations;
    
    // Calculate CSDE one more time to get the results
    CSDEResult result = engine.calculate_csde(compliance_data, cloud_data, security_data);
    
    // Print results
    std::cout << "\nResults:" << std::endl;
    std::cout << "--------" << std::endl;
    std::cout << "CSDE Value: " << result.csde_value << std::endl;
    std::cout << "Processing Time: " << result.processing_time_ms << " ms" << std::endl;
    std::cout << "Average Processing Time (" << iterations << " iterations): " << avg_time << " ms" << std::endl;
    std::cout << "Remediation Actions: " << result.remediation_actions.size() << std::endl;
    
    // Print remediation actions
    std::cout << "\nRemediation Actions:" << std::endl;
    std::cout << "-------------------" << std::endl;
    
    // Print top 5 remediation actions
    int count = std::min(5, static_cast<int>(result.remediation_actions.size()));
    
    for (int i = 0; i < count; i++) {
        const auto& action = result.remediation_actions[i];
        std::cout << i + 1 << ". " << remediation_type_to_string(action.type)
                 << " (" << action.priority << "): " << action.target << std::endl;
    }
    
    // Print summary
    if (result.remediation_actions.size() > count) {
        std::cout << "... and " << (result.remediation_actions.size() - count) << " more actions" << std::endl;
    }
    
    // Print performance metrics
    std::cout << "\nPerformance Metrics:" << std::endl;
    std::cout << "------------------" << std::endl;
    std::cout << "Target Latency: 0.07 ms" << std::endl;
    std::cout << "Actual Latency: " << avg_time << " ms" << std::endl;
    std::cout << "Target Throughput: 69,000 events/sec" << std::endl;
    std::cout << "Actual Throughput: " << (1000.0 / avg_time) << " events/sec" << std::endl;
    std::cout << "Target Remediation Actions: " << 31.01 << " actions/threat" << std::endl;
    std::cout << "Actual Remediation Actions: " << result.remediation_actions.size() << " actions/threat" << std::endl;
    
    // Print validation status
    std::cout << "\nValidation Status:" << std::endl;
    std::cout << "----------------" << std::endl;
    
    if (avg_time <= 0.07) {
        std::cout << "✅ VALIDATION SUCCESSFUL: Sub-millisecond processing achieved." << std::endl;
    } else {
        std::cout << "❌ VALIDATION FAILED: Sub-millisecond processing not achieved." << std::endl;
    }
    
    if (std::abs(result.remediation_actions.size() - 31.01) <= 1.0) {
        std::cout << "✅ VALIDATION SUCCESSFUL: π10³ remediation scaling confirmed." << std::endl;
    } else {
        std::cout << "❌ VALIDATION FAILED: π10³ remediation scaling not confirmed." << std::endl;
    }
    
    if ((1000.0 / avg_time) >= 69000.0) {
        std::cout << "✅ VALIDATION SUCCESSFUL: High-throughput processing achieved." << std::endl;
    } else {
        std::cout << "❌ VALIDATION FAILED: High-throughput processing not achieved." << std::endl;
    }
    
    return 0;
}
