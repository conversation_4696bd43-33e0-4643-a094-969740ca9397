# NovaActuary™ Docker Container
# The ∂Ψ=0 Underwriting Revolution - Production Ready
# 
# This container packages the complete NovaActuary™ platform
# for enterprise deployment and testing

FROM node:18-alpine

# Set working directory
WORKDIR /app

# Create NovaActuary™ user for security
RUN addgroup -g 1001 -S novaactuary && \
    adduser -S novaactuary -u 1001

# Install system dependencies
RUN apk add --no-cache \
    python3 \
    py3-pip \
    build-base \
    linux-headers

# Copy package files
COPY package*.json ./
COPY src/novaactuary/package.json ./novaactuary/

# Install Node.js dependencies
RUN npm install --production

# Copy NovaActuary™ source code
COPY src/novaactuary/ ./novaactuary/
COPY src/novaconnect/ ./novaconnect/
COPY src/novacortex/ ./novacortex/
COPY src/comphyology/ ./comphyology/
COPY src/wall_street_oracle/ ./wall_street_oracle/
COPY src/uvrms/ ./uvrms/

# Set up Python environment for Trinity Oracle integration
RUN pip3 install --no-cache-dir \
    numpy \
    scipy \
    pandas \
    scikit-learn

# Create data directories
RUN mkdir -p /app/data/assessments \
             /app/data/metrics \
             /app/data/logs \
             /app/data/cache

# Set permissions
RUN chown -R novaactuary:novaactuary /app

# Switch to NovaActuary™ user
USER novaactuary

# Expose ports
EXPOSE 3000 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD node novaactuary/health-check.js || exit 1

# Environment variables
ENV NODE_ENV=production
ENV NOVAACTUARY_VERSION=1.0.0-REVOLUTIONARY
ENV PSI_ZERO_THRESHOLD=0.1
ENV PI_COHERENCE_ENABLED=true
ENV CSM_PRS_VALIDATION=true
ENV TRINITY_ORACLE_ENABLED=true

# Default command
CMD ["node", "novaactuary/index.js"]

# Labels for container metadata
LABEL maintainer="David Nigel Irvin <<EMAIL>>"
LABEL version="1.0.0-REVOLUTIONARY"
LABEL description="NovaActuary™ - The ∂Ψ=0 Underwriting Revolution"
LABEL com.novafuse.product="NovaActuary"
LABEL com.novafuse.component="Core Platform"
LABEL com.novafuse.tier="Production"
