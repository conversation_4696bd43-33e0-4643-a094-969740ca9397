/**
 * Comphyology Data Integration Connectors
 *
 * This module provides connectors to integrate Comphyology visualizations with
 * real-time data from other NovaFuse components.
 */

const EventEmitter = require('events');

/**
 * Base class for all data connectors
 */
class BaseDataConnector extends EventEmitter {
  /**
   * Constructor
   *
   * @param {Object} options - Connector options
   * @param {boolean} options.enableLogging - Whether to enable logging
   * @param {number} options.pollingInterval - Polling interval in milliseconds (for polling connectors)
   * @param {number} options.bufferSize - Maximum number of data points to buffer
   */
  constructor(options = {}) {
    super();

    this.options = {
      enableLogging: options.enableLogging || false,
      pollingInterval: options.pollingInterval || 5000,
      bufferSize: options.bufferSize || 100,
      ...options
    };

    this.connected = false;
    this.dataBuffer = [];
    this.pollingTimer = null;

    if (this.options.enableLogging) {
      console.log(`${this.constructor.name} initialized with options:`, this.options);
    }
  }

  /**
   * Connect to the data source
   *
   * @returns {Promise} - Promise that resolves when connected
   */
  async connect() {
    if (this.connected) {
      return Promise.resolve();
    }

    try {
      await this._connect();
      this.connected = true;
      this.emit('connected');

      if (this.options.enableLogging) {
        console.log(`${this.constructor.name} connected successfully`);
      }

      return Promise.resolve();
    } catch (error) {
      if (this.options.enableLogging) {
        console.error(`${this.constructor.name} connection failed:`, error);
      }

      this.emit('error', error);
      return Promise.reject(error);
    }
  }

  /**
   * Disconnect from the data source
   *
   * @returns {Promise} - Promise that resolves when disconnected
   */
  async disconnect() {
    if (!this.connected) {
      return Promise.resolve();
    }

    try {
      await this._disconnect();
      this.connected = false;
      this.emit('disconnected');

      // Clear polling timer if it exists
      if (this.pollingTimer) {
        clearInterval(this.pollingTimer);
        this.pollingTimer = null;
      }

      if (this.options.enableLogging) {
        console.log(`${this.constructor.name} disconnected successfully`);
      }

      return Promise.resolve();
    } catch (error) {
      if (this.options.enableLogging) {
        console.error(`${this.constructor.name} disconnection failed:`, error);
      }

      this.emit('error', error);
      return Promise.reject(error);
    }
  }

  /**
   * Start polling for data
   *
   * @returns {Promise} - Promise that resolves when polling starts
   */
  startPolling() {
    if (this.pollingTimer) {
      return Promise.resolve();
    }

    try {
      this.pollingTimer = setInterval(async () => {
        try {
          const data = await this._poll();
          this._processData(data);
        } catch (error) {
          if (this.options.enableLogging) {
            console.error(`${this.constructor.name} polling error:`, error);
          }

          this.emit('error', error);
        }
      }, this.options.pollingInterval);

      if (this.options.enableLogging) {
        console.log(`${this.constructor.name} polling started with interval ${this.options.pollingInterval}ms`);
      }

      return Promise.resolve();
    } catch (error) {
      if (this.options.enableLogging) {
        console.error(`${this.constructor.name} failed to start polling:`, error);
      }

      this.emit('error', error);
      return Promise.reject(error);
    }
  }

  /**
   * Stop polling for data
   *
   * @returns {Promise} - Promise that resolves when polling stops
   */
  stopPolling() {
    if (!this.pollingTimer) {
      return Promise.resolve();
    }

    try {
      clearInterval(this.pollingTimer);
      this.pollingTimer = null;

      if (this.options.enableLogging) {
        console.log(`${this.constructor.name} polling stopped`);
      }

      return Promise.resolve();
    } catch (error) {
      if (this.options.enableLogging) {
        console.error(`${this.constructor.name} failed to stop polling:`, error);
      }

      this.emit('error', error);
      return Promise.reject(error);
    }
  }

  /**
   * Process data from the data source
   *
   * @param {Object} data - Data from the data source
   * @private
   */
  _processData(data) {
    if (!data) {
      return;
    }

    // Add data to buffer
    this.dataBuffer.push({
      timestamp: new Date(),
      data
    });

    // Trim buffer if it exceeds the maximum size
    if (this.dataBuffer.length > this.options.bufferSize) {
      this.dataBuffer = this.dataBuffer.slice(this.dataBuffer.length - this.options.bufferSize);
    }

    // Emit data event
    this.emit('data', data);

    if (this.options.enableLogging) {
      console.log(`${this.constructor.name} received data:`, data);
    }
  }

  /**
   * Get the latest data from the buffer
   *
   * @returns {Object} - Latest data
   */
  getLatestData() {
    if (this.dataBuffer.length === 0) {
      return null;
    }

    return this.dataBuffer[this.dataBuffer.length - 1].data;
  }

  /**
   * Get all data from the buffer
   *
   * @returns {Array} - All data
   */
  getAllData() {
    return this.dataBuffer;
  }

  /**
   * Clear the data buffer
   */
  clearBuffer() {
    this.dataBuffer = [];

    if (this.options.enableLogging) {
      console.log(`${this.constructor.name} buffer cleared`);
    }
  }

  /**
   * Connect to the data source (to be implemented by subclasses)
   *
   * @returns {Promise} - Promise that resolves when connected
   * @protected
   */
  async _connect() {
    throw new Error('_connect() must be implemented by subclasses');
  }

  /**
   * Disconnect from the data source (to be implemented by subclasses)
   *
   * @returns {Promise} - Promise that resolves when disconnected
   * @protected
   */
  async _disconnect() {
    throw new Error('_disconnect() must be implemented by subclasses');
  }

  /**
   * Poll for data (to be implemented by subclasses)
   *
   * @returns {Promise} - Promise that resolves with the data
   * @protected
   */
  async _poll() {
    throw new Error('_poll() must be implemented by subclasses');
  }
}

/**
 * NovaShield Data Connector
 *
 * Connects to NovaShield to get real-time threat data
 */
class NovaShieldConnector extends BaseDataConnector {
  /**
   * Constructor
   *
   * @param {Object} options - Connector options
   * @param {Object} options.novaShield - NovaShield instance
   */
  constructor(options = {}) {
    super(options);

    this.novaShield = options.novaShield;
    this.eventHandlers = {};
  }

  /**
   * Connect to NovaShield
   *
   * @returns {Promise} - Promise that resolves when connected
   * @protected
   */
  async _connect() {
    if (!this.novaShield) {
      throw new Error('NovaShield instance is required');
    }

    // Set up event handlers
    this.eventHandlers.threatDetected = (data) => this._processData(data);
    this.eventHandlers.threatAnalyzed = (data) => this._processData(data);

    // Register event handlers
    this.novaShield.on('threatDetected', this.eventHandlers.threatDetected);
    this.novaShield.on('threatAnalyzed', this.eventHandlers.threatAnalyzed);

    return Promise.resolve();
  }

  /**
   * Disconnect from NovaShield
   *
   * @returns {Promise} - Promise that resolves when disconnected
   * @protected
   */
  async _disconnect() {
    if (!this.novaShield) {
      return Promise.resolve();
    }

    // Remove event handlers
    this.novaShield.off('threatDetected', this.eventHandlers.threatDetected);
    this.novaShield.off('threatAnalyzed', this.eventHandlers.threatAnalyzed);

    return Promise.resolve();
  }

  /**
   * Poll for threat data
   *
   * @returns {Promise} - Promise that resolves with the threat data
   * @protected
   */
  async _poll() {
    if (!this.novaShield) {
      throw new Error('NovaShield instance is required');
    }

    try {
      const threatData = await this.novaShield.getThreatData();
      return threatData;
    } catch (error) {
      throw error;
    }
  }
}

/**
 * NovaTrack Data Connector
 *
 * Connects to NovaTrack to get real-time compliance data
 */
class NovaTrackConnector extends BaseDataConnector {
  /**
   * Constructor
   *
   * @param {Object} options - Connector options
   * @param {Object} options.novaTrack - NovaTrack instance
   */
  constructor(options = {}) {
    super(options);

    this.novaTrack = options.novaTrack;
    this.eventHandlers = {};
  }

  /**
   * Connect to NovaTrack
   *
   * @returns {Promise} - Promise that resolves when connected
   * @protected
   */
  async _connect() {
    if (!this.novaTrack) {
      throw new Error('NovaTrack instance is required');
    }

    // Set up event handlers
    this.eventHandlers.complianceChanged = (data) => this._processData(data);
    this.eventHandlers.regulationUpdated = (data) => this._processData(data);

    // Register event handlers
    this.novaTrack.on('complianceChanged', this.eventHandlers.complianceChanged);
    this.novaTrack.on('regulationUpdated', this.eventHandlers.regulationUpdated);

    return Promise.resolve();
  }

  /**
   * Disconnect from NovaTrack
   *
   * @returns {Promise} - Promise that resolves when disconnected
   * @protected
   */
  async _disconnect() {
    if (!this.novaTrack) {
      return Promise.resolve();
    }

    // Remove event handlers
    this.novaTrack.off('complianceChanged', this.eventHandlers.complianceChanged);
    this.novaTrack.off('regulationUpdated', this.eventHandlers.regulationUpdated);

    return Promise.resolve();
  }

  /**
   * Poll for compliance data
   *
   * @returns {Promise} - Promise that resolves with the compliance data
   * @protected
   */
  async _poll() {
    if (!this.novaTrack) {
      throw new Error('NovaTrack instance is required');
    }

    try {
      const complianceData = await this.novaTrack.getComplianceData();
      return complianceData;
    } catch (error) {
      throw error;
    }
  }
}

/**
 * NovaCore Data Connector
 *
 * Connects to NovaCore to get real-time decision data
 */
class NovaCoreConnector extends BaseDataConnector {
  /**
   * Constructor
   *
   * @param {Object} options - Connector options
   * @param {Object} options.novaCore - NovaCore instance
   */
  constructor(options = {}) {
    super(options);

    this.novaCore = options.novaCore;
    this.eventHandlers = {};
  }

  /**
   * Connect to NovaCore
   *
   * @returns {Promise} - Promise that resolves when connected
   * @protected
   */
  async _connect() {
    if (!this.novaCore) {
      throw new Error('NovaCore instance is required');
    }

    // Set up event handlers
    this.eventHandlers.decisionMade = (data) => this._processData(data);
    this.eventHandlers.policyApplied = (data) => this._processData(data);

    // Register event handlers
    this.novaCore.on('decisionMade', this.eventHandlers.decisionMade);
    this.novaCore.on('policyApplied', this.eventHandlers.policyApplied);

    return Promise.resolve();
  }

  /**
   * Disconnect from NovaCore
   *
   * @returns {Promise} - Promise that resolves when disconnected
   * @protected
   */
  async _disconnect() {
    if (!this.novaCore) {
      return Promise.resolve();
    }

    // Remove event handlers
    this.novaCore.off('decisionMade', this.eventHandlers.decisionMade);
    this.novaCore.off('policyApplied', this.eventHandlers.policyApplied);

    return Promise.resolve();
  }

  /**
   * Poll for decision data
   *
   * @returns {Promise} - Promise that resolves with the decision data
   * @protected
   */
  async _poll() {
    if (!this.novaCore) {
      throw new Error('NovaCore instance is required');
    }

    try {
      const decisionData = await this.novaCore.getDecisionData();
      return decisionData;
    } catch (error) {
      throw error;
    }
  }
}

// Export classes
module.exports = {
  BaseDataConnector,
  NovaShieldConnector,
  NovaTrackConnector,
  NovaCoreConnector
};
