import React from 'react';
import {
  <PERSON>agram<PERSON>rame,
  ComponentBox,
  ComponentLabel,
  ComponentNumber,
  Arrow,
  VerticalArrow,
  ContainerBox,
  ContainerLabel
} from '../components/DiagramComponents';

const SelfLearningFraud = () => {
  return (
    <DiagramFrame>
      <ContainerBox width="750px" height="400px" left="25px" top="20px">
        <ContainerLabel>SELF-LEARNING SYSTEM WITH ADAPTIVE THRESHOLDS</ContainerLabel>
      </ContainerBox>
      
      {/* Top row components */}
      <ComponentBox left="80px" top="100px" width="130px">
        <ComponentNumber>801</ComponentNumber>
        <ComponentLabel>Fraud Data</ComponentLabel>
        Collection
      </ComponentBox>
      
      <Arrow left="210px" top="130px" width="100px" />
      
      <ComponentBox left="320px" top="100px" width="130px">
        <ComponentNumber>802</ComponentNumber>
        <ComponentLabel>Pattern</ComponentLabel>
        Recognition
      </ComponentBox>
      
      <Arrow left="450px" top="130px" width="100px" />
      
      <ComponentBox left="560px" top="100px" width="130px">
        <ComponentNumber>803</ComponentNumber>
        <ComponentLabel>Threshold</ComponentLabel>
        Adjustment
      </ComponentBox>
      
      <Arrow left="690px" top="130px" width="50px" />
      
      <ComponentBox left="650px" top="100px" width="130px" style={{ left: '650px' }}>
        <ComponentNumber>804</ComponentNumber>
        <ComponentLabel>Compliance</ComponentLabel>
        Enforcement
      </ComponentBox>
      
      {/* Bottom row components */}
      <ComponentBox left="80px" top="250px" width="130px">
        <ComponentNumber>805</ComponentNumber>
        <ComponentLabel>Feedback</ComponentLabel>
        Loop
      </ComponentBox>
      
      <Arrow left="210px" top="280px" width="100px" transform="rotate(180deg)" />
      
      <ComponentBox left="320px" top="250px" width="130px">
        <ComponentNumber>806</ComponentNumber>
        <ComponentLabel>Machine Learning</ComponentLabel>
        Engine
      </ComponentBox>
      
      <Arrow left="450px" top="280px" width="100px" transform="rotate(180deg)" />
      
      <ComponentBox left="560px" top="250px" width="130px">
        <ComponentNumber>807</ComponentNumber>
        <ComponentLabel>Regulatory Impact</ComponentLabel>
        Analysis
      </ComponentBox>
      
      <Arrow left="690px" top="280px" width="50px" transform="rotate(180deg)" />
      
      <ComponentBox left="650px" top="250px" width="130px" style={{ left: '650px' }}>
        <ComponentNumber>808</ComponentNumber>
        <ComponentLabel>Enforcement</ComponentLabel>
        Results
      </ComponentBox>
      
      {/* Additional components */}
      <ComponentBox left="80px" top="350px" width="130px" height="50px" style={{ opacity: 0.7 }}>
        <ComponentNumber>809</ComponentNumber>
        <ComponentLabel>Continuous</ComponentLabel>
        Learning
      </ComponentBox>
      
      <ComponentBox left="240px" top="350px" width="130px" height="50px" style={{ opacity: 0.7 }}>
        <ComponentNumber>810</ComponentNumber>
        <ComponentLabel>Adaptive</ComponentLabel>
        Thresholds
      </ComponentBox>
      
      <ComponentBox left="400px" top="350px" width="130px" height="50px" style={{ opacity: 0.7 }}>
        <ComponentNumber>811</ComponentNumber>
        <ComponentLabel>Regulatory</ComponentLabel>
        Alignment
      </ComponentBox>
      
      <ComponentBox left="560px" top="350px" width="130px" height="50px" style={{ opacity: 0.7 }}>
        <ComponentNumber>812</ComponentNumber>
        <ComponentLabel>Audit Trail</ComponentLabel>
        Generator
      </ComponentBox>
    </DiagramFrame>
  );
};

export default SelfLearningFraud;
