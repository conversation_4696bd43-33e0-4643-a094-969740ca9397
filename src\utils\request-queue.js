/**
 * NovaFuse Universal API Connector - Request Queue
 *
 * This module provides a queue for handling high load scenarios.
 */

const { createLogger } = require('./logger');

const logger = createLogger('request-queue');

/**
 * Request queue for handling high load
 */
class RequestQueue {
  constructor(options = {}) {
    this.options = {
      concurrency: options.concurrency || 10,
      timeout: options.timeout || 30000,
      retries: options.retries || 3,
      ...options
    };

    this.queue = [];
    this.processing = 0;
    this.stats = {
      enqueued: 0,
      processed: 0,
      succeeded: 0,
      failed: 0,
      retried: 0,
      timedOut: 0
    };

    logger.info('Request queue initialized', {
      concurrency: this.options.concurrency,
      timeout: this.options.timeout,
      retries: this.options.retries
    });
  }

  /**
   * Enqueue a request
   *
   * @param {Function} task - The task to execute
   * @param {Object} options - Task options
   * @returns {Promise} - A promise that resolves with the task result
   */
  enqueue(task, options = {}) {
    return new Promise((resolve, reject) => {
      const taskWrapper = {
        id: `task-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
        task,
        options: {
          priority: options.priority || 0,
          timeout: options.timeout || this.options.timeout,
          retries: options.retries !== undefined ? options.retries : this.options.retries,
          ...options
        },
        resolve,
        reject,
        retryCount: 0,
        enqueueTime: Date.now()
      };

      this.stats.enqueued++;

      logger.debug('Task enqueued', {
        taskId: taskWrapper.id,
        priority: taskWrapper.options.priority
      });

      // Add to queue and sort by priority (higher priority first)
      this.queue.push(taskWrapper);
      this.queue.sort((a, b) => b.options.priority - a.options.priority);

      // Process queue
      this._processQueue();
    });
  }

  /**
   * Process the queue
   *
   * @private
   */
  _processQueue() {
    // Check if we can process more tasks
    while (this.processing < this.options.concurrency && this.queue.length > 0) {
      const task = this.queue.shift();
      this.processing++;

      this._executeTask(task);
    }
  }

  /**
   * Execute a task
   *
   * @param {Object} taskWrapper - The task wrapper
   * @private
   */
  async _executeTask(taskWrapper) {
    const startTime = Date.now();
    let timeoutId = null;

    logger.debug('Executing task', {
      taskId: taskWrapper.id,
      retryCount: taskWrapper.retryCount,
      queueTime: startTime - taskWrapper.enqueueTime
    });

    try {
      // Set up timeout
      const timeoutPromise = new Promise((_, reject) => {
        timeoutId = setTimeout(() => {
          reject(new Error(`Task timed out after ${taskWrapper.options.timeout}ms`));
        }, taskWrapper.options.timeout);
      });

      // Execute task with timeout
      const result = await Promise.race([
        taskWrapper.task(),
        timeoutPromise
      ]);

      // Clear timeout
      clearTimeout(timeoutId);

      // Task succeeded
      this.stats.succeeded++;

      logger.debug('Task completed successfully', {
        taskId: taskWrapper.id,
        duration: Date.now() - startTime
      });

      taskWrapper.resolve(result);
    } catch (error) {
      // Clear timeout if it exists
      if (timeoutId) {
        clearTimeout(timeoutId);
      }

      // Check if we should retry
      if (error.message.includes('timed out')) {
        this.stats.timedOut++;
        logger.warn('Task timed out', {
          taskId: taskWrapper.id,
          timeout: taskWrapper.options.timeout
        });
      }

      if (taskWrapper.retryCount < taskWrapper.options.retries) {
        // Retry task
        taskWrapper.retryCount++;
        this.stats.retried++;

        logger.debug('Retrying task', {
          taskId: taskWrapper.id,
          retryCount: taskWrapper.retryCount,
          error: error.message
        });

        // Add back to queue with exponential backoff
        const backoff = Math.pow(2, taskWrapper.retryCount) * 100;
        setTimeout(() => {
          this.queue.push(taskWrapper);
          this._processQueue();
        }, backoff);
      } else {
        // Task failed
        this.stats.failed++;

        logger.warn('Task failed', {
          taskId: taskWrapper.id,
          retryCount: taskWrapper.retryCount,
          error: error.message
        });

        taskWrapper.reject(error);
      }
    } finally {
      this.processing--;
      this.stats.processed++;

      // Process next task
      this._processQueue();
    }
  }

  /**
   * Get queue statistics
   *
   * @returns {Object} - The queue statistics
   */
  getStats() {
    return {
      queueLength: this.queue.length,
      processing: this.processing,
      successRate: this.stats.succeeded / this.stats.processed || 0,
      ...this.stats
    };
  }

  /**
   * Reset queue statistics
   *
   * @returns {Object} - The reset statistics
   */
  resetStats() {
    this.stats = {
      enqueued: 0,
      processed: 0,
      succeeded: 0,
      failed: 0,
      retried: 0,
      timedOut: 0
    };

    logger.info('Request queue statistics reset');

    return this.getStats();
  }
}

// Create singleton instance
const requestQueue = new RequestQueue();

module.exports = requestQueue;
