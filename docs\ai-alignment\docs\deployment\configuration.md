# Configuration Guide

This guide covers all configuration options available in NovaAlign, including environment variables, configuration files, and runtime settings.

## Table of Contents
- [Environment Variables](#environment-variables)
- [Configuration Files](#configuration-files)
- [Database Configuration](#database-configuration)
- [Authentication](#authentication)
- [Logging](#logging)
- [Monitoring](#monitoring)
- [Security](#security)
- [Performance Tuning](#performance-tuning)

## Environment Variables

### Core Configuration

| Variable | Required | Default | Description |
|----------|----------|---------|-------------|
| `NODE_ENV` | No | `development` | Runtime environment (`development`, `production`, `test`) |
| `PORT` | No | `3000` | Port for the web server |
| `API_URL` | Yes | - | Base URL for API requests |
| `FRONTEND_URL` | Yes | - | Base URL for frontend |
| `SESSION_SECRET` | Yes | - | Secret for session encryption |

### Database

| Variable | Required | Default | Description |
|----------|----------|---------|-------------|
| `DB_HOST` | Yes | - | Database host |
| `DB_PORT` | No | `5432` | Database port |
| `DB_NAME` | Yes | - | Database name |
| `DB_USER` | Yes | - | Database user |
| `DB_PASSWORD` | Yes | - | Database password |
| `DB_SSL` | No | `false` | Enable SSL for database connection |

### Authentication

| Variable | Required | Default | Description |
|----------|----------|---------|-------------|
| `AUTH_SECRET` | Yes | - | Secret for JWT token signing |
| `TOKEN_EXPIRY` | No | `24h` | JWT token expiry time |
| `REFRESH_TOKEN_EXPIRY` | No | `7d` | Refresh token expiry time |
| `OAUTH_*` | No | - | OAuth provider configurations |

## Configuration Files

### `config/default.json`

```json
{
  "server": {
    "port": 3000,
    "environment": "development",
    "logLevel": "info"
  },
  "database": {
    "host": "localhost",
    "port": 5432,
    "name": "nova_align",
    "user": "postgres"
  },
  "auth": {
    "secret": "your-jwt-secret",
    "tokenExpiry": "24h"
  },
  "cors": {
    "origin": ["http://localhost:3000"],
    "methods": ["GET", "POST", "PUT", "DELETE"],
    "credentials": true
  }
}
```

### Environment-specific Overrides

Create files named after the environment (e.g., `production.json`, `staging.json`) in the `config` directory to override default settings.

## Database Configuration

### Schema Management

```yaml
# config/migrations/config.yaml
migrations:
  directory: ./migrations
  table: migrations
  schema: public
  format: unix
```

### Connection Pooling

```yaml
# config/database.yaml
pool:
  min: 2
  max: 10
  idleTimeoutMillis: 30000
  connectionTimeoutMillis: 2000
```

## Authentication

### JWT Configuration

```yaml
# config/auth.yaml
jwt:
  secret: your-secret-key
  audience: nova-align
  issuer: nova-align
  expiresIn: 24h
  refreshExpiresIn: 7d
```

### OAuth Providers

```yaml
# config/oauth.yaml
google:
  clientID: your-google-client-id
  clientSecret: your-google-client-secret
  callbackURL: /auth/google/callback
```

## Logging

### Logging Configuration

```yaml
# config/logging.yaml
appenders:
  console:
    type: console
    layout:
      type: pattern
      pattern: "%[%d{ISO8601}] [%p] %c - %m%n"
  file:
    type: file
    filename: logs/app.log
    maxLogSize: 10485760
    backups: 5
    compress: true

categories:
  default:
    appenders: [console, file]
    level: info
  security:
    appenders: [console, file]
    level: warn
  database:
    appenders: [file]
    level: error
```

## Monitoring

### Prometheus Configuration

```yaml
# config/monitoring.yaml
prometheus:
  enabled: true
  path: /metrics
  defaultMetrics:
    enabled: true
    config:
      timeout: 10000
      prefix: nova_align_
  defaultLabels:
    service: nova-align
    environment: production
```

## Security

### Rate Limiting

```yaml
# config/security.yaml
rateLimit:
  windowMs: 15 * 60 * 1000  # 15 minutes
  max: 100  # Limit each IP to 100 requests per window
  message: 'Too many requests, please try again later.'
  headers: true
```

### CORS

```yaml
# config/cors.yaml
origin:
  - http://localhost:3000
  - https://app.novaalign.ai
methods: ["GET", "POST", "PUT", "DELETE"]
allowedHeaders: ["Content-Type", "Authorization"]
exposedHeaders: ["Content-Range"]
credentials: true
maxAge: 86400
```

## Performance Tuning

### Cache Configuration

```yaml
# config/cache.yaml
redis:
  host: localhost
  port: 6379
  password: ''
  db: 0
  keyPrefix: 'nova_align:'
  ttl: 3600
```

### API Caching

```yaml
# config/api.yaml
cache:
  enabled: true
  ttl: 300  # 5 minutes
  maxSize: 1000
  include: ['/api/v1/systems', '/api/v1/metrics']
  exclude: ['/api/v1/alerts']
```

## Best Practices

1. **Sensitive Data**
   - Never commit sensitive data to version control
   - Use environment variables for secrets
   - Use a secrets management tool for production

2. **Configuration Management**
   - Keep default configurations in version control
   - Use environment variables for environment-specific overrides
   - Document all configuration options

3. **Security**
   - Use HTTPS in production
   - Enable CORS only for trusted domains
   - Regularly rotate secrets and API keys

4. **Performance**
   - Tune database connection pool settings
   - Enable caching for frequently accessed data
   - Monitor and adjust rate limits as needed

## Troubleshooting

### Common Issues

**Configuration Not Loading**
- Verify file permissions
- Check for syntax errors in configuration files
- Ensure environment variables are properly set

**Database Connection Issues**
- Verify database server is running
- Check connection string and credentials
- Verify network connectivity

**Authentication Problems**
- Check JWT secret configuration
- Verify token expiration settings
- Check OAuth provider configurations

For additional help, refer to our [Troubleshooting Guide](./troubleshooting.md) <NAME_EMAIL>.
