/**
 * Connector Error Handler
 * 
 * This utility provides error handling functions for API connectors.
 */

const errorHandlingService = require('../services/ErrorHandlingService');
const logger = require('./logger');

/**
 * Handle connector errors
 * @param {Error} error - Error object
 * @param {Object} context - Error context
 * @returns {Object} - Error response
 */
const handleConnectorError = (error, context = {}) => {
  // Create connector-specific error
  const connectorError = createConnectorError(error, context);
  
  // Handle the error using the error handling service
  return errorHandlingService.handleError(connectorError, context);
};

/**
 * Create a connector-specific error
 * @param {Error} error - Original error
 * @param {Object} context - Error context
 * @returns {Error} - Connector error
 */
const createConnectorError = (error, context = {}) => {
  const { connector, endpoint, operation } = context;
  
  // Create a new error with connector-specific information
  const connectorError = new Error(
    `Connector error: ${connector ? connector + ' - ' : ''}${endpoint ? endpoint + ' - ' : ''}${operation ? operation + ' - ' : ''}${error.message}`
  );
  
  // Copy properties from the original error
  Object.assign(connectorError, error);
  
  // Set connector-specific properties
  connectorError.name = 'ConnectorError';
  connectorError.connector = connector;
  connectorError.endpoint = endpoint;
  connectorError.operation = operation;
  connectorError.originalError = error;
  
  return connectorError;
};

/**
 * Map connector-specific error codes to standard error types
 * @param {string} errorCode - Connector-specific error code
 * @param {string} connector - Connector name
 * @returns {string} - Standard error type
 */
const mapErrorCodeToType = (errorCode, connector) => {
  // Common error code mappings
  const commonMappings = {
    'ECONNREFUSED': 'connection_refused',
    'ECONNRESET': 'connection_reset',
    'ETIMEDOUT': 'timeout',
    'ENOTFOUND': 'host_not_found',
    '400': 'bad_request',
    '401': 'unauthorized',
    '403': 'forbidden',
    '404': 'not_found',
    '408': 'timeout',
    '429': 'rate_limit',
    '500': 'server_error',
    '502': 'bad_gateway',
    '503': 'service_unavailable',
    '504': 'gateway_timeout'
  };
  
  // Connector-specific mappings
  const connectorMappings = {
    'aws': {
      'ThrottlingException': 'rate_limit',
      'AccessDeniedException': 'unauthorized',
      'ResourceNotFoundException': 'not_found',
      'ValidationException': 'validation_error',
      'InternalServerException': 'server_error'
    },
    'azure': {
      'AuthorizationFailed': 'unauthorized',
      'ResourceNotFound': 'not_found',
      'InvalidResourceId': 'validation_error',
      'InternalServerError': 'server_error',
      'GatewayTimeout': 'gateway_timeout'
    },
    'gcp': {
      'PERMISSION_DENIED': 'unauthorized',
      'NOT_FOUND': 'not_found',
      'INVALID_ARGUMENT': 'validation_error',
      'INTERNAL': 'server_error',
      'DEADLINE_EXCEEDED': 'timeout'
    }
  };
  
  // Check connector-specific mappings first
  if (connector && connectorMappings[connector] && connectorMappings[connector][errorCode]) {
    return connectorMappings[connector][errorCode];
  }
  
  // Fall back to common mappings
  return commonMappings[errorCode] || 'unknown_error';
};

/**
 * Create a retry policy for a connector
 * @param {string} connector - Connector name
 * @param {Object} options - Retry options
 * @returns {Object} - Retry policy
 */
const createRetryPolicy = (connector, options = {}) => {
  // Default retry policies for different connectors
  const defaultPolicies = {
    'aws': {
      maxRetries: 3,
      initialDelay: 1000,
      maxDelay: 10000,
      backoffFactor: 2,
      jitter: true,
      retryableErrors: ['timeout', 'connection_refused', 'connection_reset', 'host_not_found', 'rate_limit', 'server_error', 'service_unavailable', 'gateway_timeout']
    },
    'azure': {
      maxRetries: 3,
      initialDelay: 2000,
      maxDelay: 15000,
      backoffFactor: 2,
      jitter: true,
      retryableErrors: ['timeout', 'connection_refused', 'connection_reset', 'host_not_found', 'rate_limit', 'server_error', 'service_unavailable', 'gateway_timeout']
    },
    'gcp': {
      maxRetries: 5,
      initialDelay: 1000,
      maxDelay: 20000,
      backoffFactor: 1.5,
      jitter: true,
      retryableErrors: ['timeout', 'connection_refused', 'connection_reset', 'host_not_found', 'rate_limit', 'server_error', 'service_unavailable', 'gateway_timeout']
    }
  };
  
  // Get default policy for the connector or use a generic default
  const defaultPolicy = defaultPolicies[connector] || {
    maxRetries: 3,
    initialDelay: 1000,
    maxDelay: 10000,
    backoffFactor: 2,
    jitter: true,
    retryableErrors: ['timeout', 'connection_refused', 'connection_reset', 'host_not_found', 'rate_limit', 'server_error', 'service_unavailable', 'gateway_timeout']
  };
  
  // Merge default policy with provided options
  return {
    ...defaultPolicy,
    ...options
  };
};

/**
 * Create a circuit breaker for a connector
 * @param {string} connector - Connector name
 * @param {Object} options - Circuit breaker options
 */
const createCircuitBreaker = (connector, options = {}) => {
  // Default circuit breaker options for different connectors
  const defaultOptions = {
    'aws': {
      failureThreshold: 5,
      resetTimeout: 30000
    },
    'azure': {
      failureThreshold: 3,
      resetTimeout: 60000
    },
    'gcp': {
      failureThreshold: 5,
      resetTimeout: 45000
    }
  };
  
  // Get default options for the connector or use a generic default
  const defaultOption = defaultOptions[connector] || {
    failureThreshold: 5,
    resetTimeout: 30000
  };
  
  // Merge default options with provided options
  const mergedOptions = {
    ...defaultOption,
    ...options
  };
  
  // Register circuit breaker
  errorHandlingService.registerCircuitBreaker(connector, mergedOptions);
};

/**
 * Create a wrapped connector function with error handling
 * @param {Function} fn - Connector function to wrap
 * @param {Object} options - Options
 * @returns {Function} - Wrapped function
 */
const withConnectorErrorHandling = (fn, options = {}) => {
  const { connector, endpoint, operation, retry = true, circuitBreaker = true, timeout = true } = options;
  
  // Create context
  const context = {
    connector,
    endpoint,
    operation
  };
  
  // Create wrapped function
  let wrappedFn = async (...args) => {
    try {
      return await fn(...args);
    } catch (error) {
      throw createConnectorError(error, context);
    }
  };
  
  // Add timeout if enabled
  if (timeout) {
    const timeoutMs = options.timeoutMs || 30000;
    wrappedFn = errorHandlingService.withTimeout(wrappedFn, { timeoutMs });
  }
  
  // Add retry if enabled
  if (retry) {
    const retryPolicy = createRetryPolicy(connector, options.retry);
    wrappedFn = errorHandlingService.withRetry(wrappedFn, retryPolicy);
  }
  
  // Add circuit breaker if enabled
  if (circuitBreaker) {
    createCircuitBreaker(connector, options.circuitBreaker);
    wrappedFn = errorHandlingService.withCircuitBreaker(wrappedFn, { resource: connector });
  }
  
  return wrappedFn;
};

module.exports = {
  handleConnectorError,
  createConnectorError,
  mapErrorCodeToType,
  createRetryPolicy,
  createCircuitBreaker,
  withConnectorErrorHandling
};
