/**
 * Regulatory Integration Module
 *
 * This module provides integration with regulatory compliance APIs.
 * It handles retrieving regulation details, compliance requirements,
 * jurisdiction information, and regulatory updates.
 */

const axios = require('axios');
const { logger } = require('./utils/logger');
const cache = require('./cache');
const config = require('./config/regulatory-api');

/**
 * Build the API URL for a specific endpoint
 * @param {string} endpoint - API endpoint
 * @returns {string} - Full API URL
 */
function buildApiUrl(endpoint) {
  return `${config.baseUrl}/${config.apiVersion}${endpoint}`;
}

/**
 * Get default request options for API calls
 * @returns {Object} - Request options
 */
function getRequestOptions() {
  return {
    headers: {
      'Authorization': `Bearer ${config.apiKey}`,
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    },
    timeout: config.timeout
  };
}

/**
 * Get regulation details by ID
 * @param {string} regulationId - Regulation ID
 * @returns {Promise<Object>} - Regulation details
 * @throws {Error} - If retrieval fails
 */
async function getRegulationDetails(regulationId) {
  try {
    // Check cache first
    const cacheKey = `regulation:${regulationId}`;
    const cachedData = cache.get(cacheKey);
    
    if (cachedData) {
      logger.debug(`Retrieved regulation details for ${regulationId} from cache`);
      return cachedData;
    }
    
    // Not in cache, fetch from API
    logger.debug(`Fetching regulation details for ${regulationId} from API`);
    const url = buildApiUrl(`/regulations/${regulationId}`);
    const response = await axios.get(url, getRequestOptions());
    const regulationData = response.data;
    
    // Cache the result
    cache.set(cacheKey, regulationData, config.cacheTtl.regulations);
    logger.debug(`Cached regulation details for ${regulationId}`);
    
    return regulationData;
  } catch (error) {
    logger.error(`Error retrieving regulation details for ${regulationId}`, error);
    throw new Error(`Failed to retrieve regulation details for ${regulationId}`);
  }
}

/**
 * Get compliance requirements for a regulation
 * @param {string} regulationId - Regulation ID
 * @returns {Promise<Array>} - Compliance requirements
 * @throws {Error} - If retrieval fails
 */
async function getComplianceRequirements(regulationId) {
  try {
    // Check cache first
    const cacheKey = `requirements:${regulationId}`;
    const cachedData = cache.get(cacheKey);
    
    if (cachedData) {
      logger.debug(`Retrieved compliance requirements for ${regulationId} from cache`);
      return cachedData;
    }
    
    // Not in cache, fetch from API
    logger.debug(`Fetching compliance requirements for ${regulationId} from API`);
    const url = buildApiUrl(`/regulations/${regulationId}/requirements`);
    const response = await axios.get(url, getRequestOptions());
    const requirementsData = response.data;
    
    // Cache the result
    cache.set(cacheKey, requirementsData, config.cacheTtl.requirements);
    logger.debug(`Cached compliance requirements for ${regulationId}`);
    
    return requirementsData;
  } catch (error) {
    logger.error(`Error retrieving compliance requirements for ${regulationId}`, error);
    throw new Error(`Failed to retrieve compliance requirements for ${regulationId}`);
  }
}

/**
 * Get jurisdiction information
 * @param {string} jurisdictionCode - Jurisdiction code
 * @returns {Promise<Object>} - Jurisdiction information
 * @throws {Error} - If retrieval fails
 */
async function getJurisdictionInfo(jurisdictionCode) {
  try {
    // Check cache first
    const cacheKey = `jurisdiction:${jurisdictionCode}`;
    const cachedData = cache.get(cacheKey);
    
    if (cachedData) {
      logger.debug(`Retrieved jurisdiction information for ${jurisdictionCode} from cache`);
      return cachedData;
    }
    
    // Not in cache, fetch from API
    logger.debug(`Fetching jurisdiction information for ${jurisdictionCode} from API`);
    const url = buildApiUrl(`/jurisdictions/${jurisdictionCode}`);
    const response = await axios.get(url, getRequestOptions());
    const jurisdictionData = response.data;
    
    // Cache the result
    cache.set(cacheKey, jurisdictionData, config.cacheTtl.jurisdictions);
    logger.debug(`Cached jurisdiction information for ${jurisdictionCode}`);
    
    return jurisdictionData;
  } catch (error) {
    logger.error(`Error retrieving jurisdiction information for ${jurisdictionCode}`, error);
    throw new Error(`Failed to retrieve jurisdiction information for ${jurisdictionCode}`);
  }
}

/**
 * Get regulatory updates
 * @returns {Promise<Array>} - Regulatory updates
 * @throws {Error} - If retrieval fails
 */
async function getRegulatoryUpdates() {
  try {
    // Check cache first
    const cacheKey = 'regulatory-updates';
    const cachedData = cache.get(cacheKey);
    
    if (cachedData) {
      logger.debug('Retrieved regulatory updates from cache');
      return cachedData;
    }
    
    // Not in cache, fetch from API
    logger.debug('Fetching regulatory updates from API');
    const url = buildApiUrl('/regulatory-updates');
    const response = await axios.get(url, getRequestOptions());
    const updatesData = response.data;
    
    // Cache the result
    cache.set(cacheKey, updatesData, config.cacheTtl.updates);
    logger.debug('Cached regulatory updates');
    
    return updatesData;
  } catch (error) {
    logger.error('Error retrieving regulatory updates', error);
    throw new Error('Failed to retrieve regulatory updates');
  }
}

/**
 * Get compliance status for a regulation and organization
 * @param {string} regulationId - Regulation ID
 * @param {string} organizationId - Organization ID
 * @returns {Promise<Object>} - Compliance status
 * @throws {Error} - If retrieval fails
 */
async function getComplianceStatus(regulationId, organizationId) {
  try {
    logger.debug(`Fetching compliance status for ${regulationId} and organization ${organizationId}`);
    const url = buildApiUrl(`/compliance-status/${regulationId}`);
    const options = {
      ...getRequestOptions(),
      params: { organizationId }
    };
    
    const response = await axios.get(url, options);
    return response.data;
  } catch (error) {
    logger.error(`Error retrieving compliance status for ${regulationId} and organization ${organizationId}`, error);
    throw new Error(`Failed to retrieve compliance status for ${regulationId}`);
  }
}

/**
 * Submit a compliance report
 * @param {string} regulationId - Regulation ID
 * @param {Object} reportData - Report data
 * @returns {Promise<Object>} - Submission response
 * @throws {Error} - If submission fails
 */
async function submitComplianceReport(regulationId, reportData) {
  try {
    logger.debug(`Submitting compliance report for ${regulationId}`);
    const url = buildApiUrl(`/regulations/${regulationId}/compliance-reports`);
    const response = await axios.post(url, reportData, getRequestOptions());
    
    logger.info(`Submitted compliance report for ${regulationId} - Report ID: ${response.data.id}`);
    return response.data;
  } catch (error) {
    logger.error(`Error submitting compliance report for ${regulationId}`, error);
    throw new Error(`Failed to submit compliance report for ${regulationId}`);
  }
}

/**
 * Refresh regulatory data and clear cache
 * @returns {Promise<void>}
 * @throws {Error} - If refresh fails
 */
async function refreshRegulatoryData() {
  try {
    logger.debug('Refreshing regulatory data');
    const url = buildApiUrl('/regulations');
    const response = await axios.get(url, getRequestOptions());
    const regulations = response.data;
    
    // Clear cache for regulatory updates
    cache.delete('regulatory-updates');
    
    // Clear cache for each regulation
    for (const regulation of regulations) {
      cache.delete(`regulation:${regulation.id}`);
      cache.delete(`requirements:${regulation.id}`);
    }
    
    logger.info(`Refreshed regulatory data for ${regulations.length} regulations`);
  } catch (error) {
    logger.error('Error refreshing regulatory data', error);
    throw new Error('Failed to refresh regulatory data');
  }
}

module.exports = {
  getRegulationDetails,
  getComplianceRequirements,
  getJurisdictionInfo,
  getRegulatoryUpdates,
  getComplianceStatus,
  submitComplianceReport,
  refreshRegulatoryData
};
