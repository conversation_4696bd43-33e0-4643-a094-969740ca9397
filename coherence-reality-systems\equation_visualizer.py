import json
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import numpy as np

class EquationVisualizer:
    def __init__(self, equations_file):
        with open(equations_file, 'r') as f:
            self.equations = json.load(f)
        
    def create_trinity_visualization(self, equation_name):
        """Create 3D visualization of trinity equations"""
        fig = plt.figure(figsize=(12, 8))
        ax = fig.add_subplot(111, projection='3d')
        
        # Create grid for π, φ, e axes
        pi_vals = np.linspace(0, 1, 100)
        phi_vals = np.linspace(0, 1.618, 100)
        e_vals = np.linspace(0, 2.718, 100)
        
        # Create meshgrid
        Pi, Phi, E = np.meshgrid(pi_vals, phi_vals, e_vals)
        
        # Calculate equation values
        if equation_name == "CSDE_Trinity":
            G = Pi
            D = Phi
            R = E
            Z = Pi + Phi + (np.hbar + 1/np.c) * E
        elif equation_name == "Trust":
            C = Pi
            R = Phi
            I = E
            S = 1 - Pi
            Z = (C * R * I) / S
        
        # Plot surface
        ax.plot_surface(Pi, Phi, Z, cmap='viridis', alpha=0.8)
        
        # Add ideal point
        ax.scatter([1], [1.618], [2.718], color='red', s=100, label='Ideal Point')
        
        # Labels and title
        ax.set_xlabel('π-axis (Governance/Structure)')
        ax.set_ylabel('φ-axis (Harmonic/Detection)')
        ax.set_zlabel('e-axis (Adaptive/Response)')
        ax.set_title(f'{equation_name} Visualization')
        
        # Add grid lines
        ax.grid(True)
        
        # Save and show
        plt.savefig(f'visualizations/{equation_name}.png')
        plt.show()

    def create_validation_metrics(self, equation_name):
        """Create validation metrics visualization"""
        fig, ax = plt.subplots(figsize=(12, 6))
        
        # Get metrics
        metrics = self.equations[equation_name]['validation_metrics']
        
        # Create bar chart
        labels = list(metrics.keys())
        values = [float(v.replace('>', '').replace('=', '').strip()) for v in metrics.values()]
        
        bars = ax.bar(labels, values, color=['#2c3e50', '#e67e22', '#3498db'])
        
        # Add labels
        for bar in bars:
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height,
                    f'{height:.2f}',
                    ha='center', va='bottom')
        
        ax.set_title(f'Validation Metrics for {equation_name}')
        ax.set_ylabel('Threshold Values')
        
        plt.savefig(f'visualizations/{equation_name}_metrics.png')
        plt.show()

if __name__ == "__main__":
    visualizer = EquationVisualizer('equations.json')
    visualizer.create_trinity_visualization('CSDE_Trinity')
    visualizer.create_trinity_visualization('Trust')
    visualizer.create_validation_metrics('CSDE_Trinity')
    visualizer.create_validation_metrics('Trust')
