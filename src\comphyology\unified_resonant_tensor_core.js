/**
 * Unified Resonant Tensor Core
 * 
 * This module provides a unified interface for the resonant tensor core,
 * implementing the Law of Resonant Encapsulation:
 * 
 * "No system shall externalize what its resonance core has not first harmonized."
 */

const EventEmitter = require('events');
const ResonantTensorCore = require('./resonant_tensor_core');
const ResonanceValidator = require('./resonance_validator');

/**
 * Unified Resonant Tensor Core class
 */
class UnifiedResonantTensorCore extends EventEmitter {
  /**
   * Constructor
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      strictMode: false, // Whether to reject non-resonant values (true) or harmonize them (false)
      logValidation: true, // Whether to log validation results
      resonanceLock: true, // Whether to enforce resonance constraints
      trackDrift: true, // Whether to track resonance drift
      useEnergyBasedComphyon: true, // Whether to use energy-based Comphyon calculation
      ...options
    };
    
    // Initialize components
    this._initializeComponents();
    
    // Initialize resonance metrics
    this.metrics = {
      validations: 0,
      harmonizations: 0,
      rejections: 0,
      totalDrift: 0,
      averageDrift: 0,
      operations: {
        process: 0,
        processBatch: 0
      }
    };
    
    // Log initialization
    if (this.options.logValidation) {
      console.log(`Unified Resonant Tensor Core initialized with options:`, {
        strictMode: this.options.strictMode,
        resonanceLock: this.options.resonanceLock,
        useEnergyBasedComphyon: this.options.useEnergyBasedComphyon
      });
    }
  }
  
  /**
   * Initialize components
   * @private
   */
  _initializeComponents() {
    try {
      // Import required components
      const PsiTensorCore = require('../psi_tensor_core/psi_tensor_core');
      const DynamicWeightingProtocol = require('../psi_tensor_core/dynamic_weighting');
      const QuantumConsensusEngine = require('../psi_tensor_core/quantum_consensus');
      const EnergyCalculator = require('../psi_tensor_core/energy_calculator');
      
      // Create base components
      this.psiTensorCore = new PsiTensorCore();
      this.dynamicWeighting = new DynamicWeightingProtocol();
      this.quantumConsensus = new QuantumConsensusEngine();
      
      // Create energy calculator if energy-based Comphyon is enabled
      if (this.options.useEnergyBasedComphyon) {
        this.energyCalculator = new EnergyCalculator();
      }
      
      // Create resonant tensor core wrapper
      this.resonantTensorCore = new ResonantTensorCore(this.psiTensorCore, {
        strictMode: this.options.strictMode,
        logValidation: this.options.logValidation,
        resonanceLock: this.options.resonanceLock,
        trackDrift: this.options.trackDrift
      });
      
      // Create resonance validator
      this.validator = new ResonanceValidator({
        strictMode: this.options.strictMode,
        logValidation: this.options.logValidation,
        resonanceLock: this.options.resonanceLock
      });
      
      // Forward events from resonant tensor core
      this.resonantTensorCore.on('tensor-validated', (data) => {
        this.emit('tensor-validated', data);
      });
      
      this.resonantTensorCore.on('validation-error', (data) => {
        this.emit('validation-error', data);
      });
    } catch (error) {
      console.error('Error initializing Unified Resonant Tensor Core:', error);
      throw error;
    }
  }
  
  /**
   * Process data from all engines with resonance validation
   * @param {Object} csde_data - CSDE data
   * @param {Object} csfe_data - CSFE data
   * @param {Object} csme_data - CSME data
   * @returns {Object} - Processing result
   */
  process(csde_data, csfe_data, csme_data) {
    try {
      // Update metrics
      this.metrics.operations.process++;
      
      // Create tensors for each engine
      const csde_tensor = this.resonantTensorCore.createTensor(
        csde_data.dimensions || [4],
        csde_data.values,
        'csde'
      );
      
      const csfe_tensor = this.resonantTensorCore.createTensor(
        csfe_data.dimensions || [4],
        csfe_data.values,
        'csfe'
      );
      
      const csme_tensor = this.resonantTensorCore.createTensor(
        csme_data.dimensions || [4],
        csme_data.values,
        'csme'
      );
      
      // Apply dynamic weighting
      const weights = this.dynamicWeighting.calculateWeights(
        csde_data,
        csfe_data,
        csme_data
      );
      
      // Fuse engines with resonance validation
      const fused_tensor = this.resonantTensorCore.fuseEngines(
        csde_tensor,
        csfe_tensor,
        csme_tensor
      );
      
      // Reach consensus on actions
      const consensus_result = this.quantumConsensus.reachConsensus(
        fused_tensor,
        weights
      );
      
      // Calculate Comphyon if energy-based Comphyon is enabled
      let comphyon = null;
      if (this.options.useEnergyBasedComphyon && this.energyCalculator) {
        comphyon = this.energyCalculator.calculateComphyon(
          csde_data,
          csfe_data,
          csme_data,
          fused_tensor
        );
        
        // Validate Comphyon value
        const comphyonValidation = this.validator.validate(
          comphyon.comphyon_value,
          'generic'
        );
        
        // Harmonize Comphyon value if needed
        if (comphyonValidation.wasHarmonized) {
          comphyon.comphyon_value = comphyonValidation.harmonizedValue;
          comphyon.resonance = {
            isResonant: comphyonValidation.isResonant,
            wasHarmonized: comphyonValidation.wasHarmonized,
            resonanceDrift: comphyonValidation.resonanceDrift
          };
          
          // Update metrics
          this.metrics.harmonizations++;
          this.metrics.totalDrift += comphyonValidation.resonanceDrift;
          this.metrics.averageDrift = this.metrics.totalDrift / this.metrics.harmonizations;
        }
      }
      
      // Create result object
      const result = {
        consensus_action: consensus_result.action,
        consensus_confidence: consensus_result.confidence,
        execute_action: consensus_result.execute,
        dominant_engine: weights.dominant_engine,
        weights,
        fused_tensor,
        resonance: {
          isResonant: fused_tensor.resonance.isResonant,
          wasHarmonized: fused_tensor.resonance.wasHarmonized,
          resonanceDrift: fused_tensor.resonance.resonanceDrift,
          metrics: this.getMetrics()
        }
      };
      
      // Add Comphyon data if available
      if (comphyon) {
        result.comphyon = comphyon;
      }
      
      // Emit process event
      this.emit('process-completed', {
        result,
        csde_data,
        csfe_data,
        csme_data
      });
      
      return result;
    } catch (error) {
      console.error('Error processing data:', error);
      
      // Emit error event
      this.emit('process-error', {
        error,
        csde_data,
        csfe_data,
        csme_data
      });
      
      throw error;
    }
  }
  
  /**
   * Process batch data from all engines with resonance validation
   * @param {Array} csde_batch - CSDE batch data
   * @param {Array} csfe_batch - CSFE batch data
   * @param {Array} csme_batch - CSME batch data
   * @returns {Array} - Batch processing results
   */
  processBatch(csde_batch, csfe_batch, csme_batch) {
    try {
      // Update metrics
      this.metrics.operations.processBatch++;
      
      // Validate batch sizes
      if (csde_batch.length !== csfe_batch.length || csde_batch.length !== csme_batch.length) {
        throw new Error('Batch sizes must be equal');
      }
      
      // Process each batch item
      const results = [];
      for (let i = 0; i < csde_batch.length; i++) {
        const result = this.process(
          csde_batch[i],
          csfe_batch[i],
          csme_batch[i]
        );
        
        results.push(result);
      }
      
      // Emit batch process event
      this.emit('batch-process-completed', {
        results,
        batchSize: csde_batch.length
      });
      
      return results;
    } catch (error) {
      console.error('Error processing batch data:', error);
      
      // Emit error event
      this.emit('batch-process-error', {
        error,
        batchSize: csde_batch ? csde_batch.length : 0
      });
      
      throw error;
    }
  }
  
  /**
   * Get combined metrics from all components
   * @returns {Object} - Combined metrics
   */
  getMetrics() {
    return {
      unified: this.metrics,
      tensorCore: this.resonantTensorCore.getMetrics(),
      resonanceLockEnabled: this.options.resonanceLock,
      strictModeEnabled: this.options.strictMode
    };
  }
  
  /**
   * Set resonance lock mode
   * @param {boolean} enabled - Whether to enable resonance lock
   */
  setResonanceLock(enabled) {
    this.options.resonanceLock = enabled;
    this.resonantTensorCore.options.resonanceLock = enabled;
    this.validator.options.resonanceLock = enabled;
    
    // Emit event
    this.emit('resonance-lock-changed', {
      enabled
    });
    
    if (this.options.logValidation) {
      console.log(`Resonance lock ${enabled ? 'enabled' : 'disabled'}`);
    }
  }
  
  /**
   * Set strict mode
   * @param {boolean} enabled - Whether to enable strict mode
   */
  setStrictMode(enabled) {
    this.options.strictMode = enabled;
    this.resonantTensorCore.options.strictMode = enabled;
    this.validator.options.strictMode = enabled;
    
    // Emit event
    this.emit('strict-mode-changed', {
      enabled
    });
    
    if (this.options.logValidation) {
      console.log(`Strict mode ${enabled ? 'enabled' : 'disabled'}`);
    }
  }
}

module.exports = UnifiedResonantTensorCore;
