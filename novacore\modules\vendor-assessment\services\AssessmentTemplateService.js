/**
 * NovaCore Assessment Template Service
 * 
 * This service provides functionality for managing assessment templates.
 */

const { AssessmentTemplate } = require('../models');
const logger = require('../../../config/logger');
const { ValidationError, NotFoundError } = require('../../../api/utils/errors');
const { v4: uuidv4 } = require('uuid');

class AssessmentTemplateService {
  /**
   * Create a new assessment template
   * @param {Object} data - Template data
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Created template
   */
  async createTemplate(data, userId) {
    try {
      logger.info('Creating new assessment template', { organizationId: data.organizationId });
      
      // Generate IDs for sections and questions if not provided
      if (data.sections) {
        for (const section of data.sections) {
          if (!section.id) {
            section.id = `section-${uuidv4().substring(0, 8)}`;
          }
          
          if (section.questions) {
            for (const question of section.questions) {
              if (!question.id) {
                question.id = `question-${uuidv4().substring(0, 8)}`;
              }
            }
          }
        }
      }
      
      // Set created by
      data.createdBy = userId;
      data.updatedBy = userId;
      
      // Create template
      const template = new AssessmentTemplate(data);
      await template.save();
      
      logger.info('Assessment template created successfully', { id: template._id });
      
      return template;
    } catch (error) {
      logger.error('Error creating assessment template', { error });
      throw error;
    }
  }
  
  /**
   * Get all assessment templates for an organization
   * @param {string} organizationId - Organization ID
   * @param {Object} filter - Filter criteria
   * @param {Object} options - Query options (pagination, sorting)
   * @returns {Promise<Object>} - Templates with pagination info
   */
  async getAllTemplates(organizationId, filter = {}, options = {}) {
    try {
      const { page = 1, limit = 10, sort = { createdAt: -1 } } = options;
      
      // Build query
      const query = { organizationId };
      
      // Apply filters
      if (filter.type) {
        query.type = filter.type;
      }
      
      if (filter.status) {
        query.status = filter.status;
      }
      
      if (filter.frameworks) {
        query.frameworks = { $all: Array.isArray(filter.frameworks) ? filter.frameworks : [filter.frameworks] };
      }
      
      if (filter.tags) {
        query.tags = { $all: Array.isArray(filter.tags) ? filter.tags : [filter.tags] };
      }
      
      if (filter.isDefault) {
        query.isDefault = filter.isDefault === 'true';
      }
      
      if (filter.search) {
        query.$or = [
          { name: { $regex: filter.search, $options: 'i' } },
          { description: { $regex: filter.search, $options: 'i' } }
        ];
      }
      
      // Execute query with pagination
      const skip = (page - 1) * limit;
      
      const [templates, total] = await Promise.all([
        AssessmentTemplate.find(query)
          .sort(sort)
          .skip(skip)
          .limit(limit),
        AssessmentTemplate.countDocuments(query)
      ]);
      
      // Calculate pagination info
      const totalPages = Math.ceil(total / limit);
      const hasNext = page < totalPages;
      const hasPrev = page > 1;
      
      return {
        data: templates,
        pagination: {
          total,
          page,
          limit,
          totalPages,
          hasNext,
          hasPrev
        }
      };
    } catch (error) {
      logger.error('Error getting assessment templates', { error });
      throw error;
    }
  }
  
  /**
   * Get assessment template by ID
   * @param {string} id - Template ID
   * @returns {Promise<Object>} - Template
   */
  async getTemplateById(id) {
    try {
      const template = await AssessmentTemplate.findById(id);
      
      if (!template) {
        throw new NotFoundError(`Assessment template with ID ${id} not found`);
      }
      
      return template;
    } catch (error) {
      logger.error('Error getting assessment template by ID', { id, error });
      throw error;
    }
  }
  
  /**
   * Update assessment template
   * @param {string} id - Template ID
   * @param {Object} data - Updated template data
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Updated template
   */
  async updateTemplate(id, data, userId) {
    try {
      // Get existing template
      const template = await this.getTemplateById(id);
      
      // Set updated by
      data.updatedBy = userId;
      
      // Update template
      Object.assign(template, data);
      await template.save();
      
      logger.info('Assessment template updated successfully', { id });
      
      return template;
    } catch (error) {
      logger.error('Error updating assessment template', { id, error });
      throw error;
    }
  }
  
  /**
   * Delete assessment template
   * @param {string} id - Template ID
   * @returns {Promise<boolean>} - Deletion success
   */
  async deleteTemplate(id) {
    try {
      const result = await AssessmentTemplate.findByIdAndDelete(id);
      
      if (!result) {
        throw new NotFoundError(`Assessment template with ID ${id} not found`);
      }
      
      logger.info('Assessment template deleted successfully', { id });
      
      return true;
    } catch (error) {
      logger.error('Error deleting assessment template', { id, error });
      throw error;
    }
  }
  
  /**
   * Set template as default
   * @param {string} id - Template ID
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Updated template
   */
  async setAsDefault(id, userId) {
    try {
      // Get template
      const template = await this.getTemplateById(id);
      
      // Get organization ID
      const organizationId = template.organizationId;
      
      // Clear default flag from other templates of the same type
      await AssessmentTemplate.updateMany(
        { 
          organizationId, 
          type: template.type, 
          isDefault: true 
        },
        { 
          isDefault: false,
          updatedBy: userId
        }
      );
      
      // Set this template as default
      template.isDefault = true;
      template.updatedBy = userId;
      await template.save();
      
      logger.info('Assessment template set as default successfully', { id });
      
      return template;
    } catch (error) {
      logger.error('Error setting assessment template as default', { id, error });
      throw error;
    }
  }
  
  /**
   * Create new version of template
   * @param {string} id - Template ID
   * @param {Object} data - New version data
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - New template version
   */
  async createNewVersion(id, data, userId) {
    try {
      // Get existing template
      const template = await this.getTemplateById(id);
      
      // Create new template data
      const newTemplateData = {
        ...JSON.parse(JSON.stringify(template)),
        _id: undefined,
        name: data.name || `${template.name} (v${data.version || 'new'})`,
        description: data.description || template.description,
        version: data.version || this._incrementVersion(template.version),
        previousVersion: template._id,
        status: 'draft',
        createdBy: userId,
        updatedBy: userId
      };
      
      // Apply any other updates
      if (data.sections) {
        newTemplateData.sections = data.sections;
      }
      
      if (data.frameworks) {
        newTemplateData.frameworks = data.frameworks;
      }
      
      if (data.passingScore) {
        newTemplateData.passingScore = data.passingScore;
      }
      
      // Create new template
      const newTemplate = new AssessmentTemplate(newTemplateData);
      await newTemplate.save();
      
      logger.info('New assessment template version created successfully', { 
        originalId: id, 
        newId: newTemplate._id 
      });
      
      return newTemplate;
    } catch (error) {
      logger.error('Error creating new assessment template version', { id, error });
      throw error;
    }
  }
  
  /**
   * Get default template
   * @param {string} organizationId - Organization ID
   * @param {string} type - Template type
   * @returns {Promise<Object>} - Default template
   */
  async getDefaultTemplate(organizationId, type = 'vendor') {
    try {
      const template = await AssessmentTemplate.findDefault(organizationId, type);
      
      if (!template) {
        throw new NotFoundError(`No default assessment template found for type ${type}`);
      }
      
      return template;
    } catch (error) {
      logger.error('Error getting default assessment template', { organizationId, type, error });
      throw error;
    }
  }
  
  /**
   * Import template from JSON
   * @param {Object} data - Template data
   * @param {string} organizationId - Organization ID
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Imported template
   */
  async importTemplate(data, organizationId, userId) {
    try {
      logger.info('Importing assessment template', { organizationId });
      
      // Validate template data
      if (!data.name || !data.sections) {
        throw new ValidationError('Invalid template data: name and sections are required');
      }
      
      // Set organization ID and user IDs
      data.organizationId = organizationId;
      data.createdBy = userId;
      data.updatedBy = userId;
      
      // Generate IDs for sections and questions if not provided
      for (const section of data.sections) {
        if (!section.id) {
          section.id = `section-${uuidv4().substring(0, 8)}`;
        }
        
        if (section.questions) {
          for (const question of section.questions) {
            if (!question.id) {
              question.id = `question-${uuidv4().substring(0, 8)}`;
            }
          }
        }
      }
      
      // Create template
      const template = new AssessmentTemplate(data);
      await template.save();
      
      logger.info('Assessment template imported successfully', { id: template._id });
      
      return template;
    } catch (error) {
      logger.error('Error importing assessment template', { error });
      throw error;
    }
  }
  
  /**
   * Increment version string
   * @param {string} version - Current version
   * @returns {string} - Incremented version
   * @private
   */
  _incrementVersion(version) {
    if (!version) {
      return '1.0';
    }
    
    const parts = version.split('.');
    
    if (parts.length === 1) {
      return `${parseInt(parts[0], 10) + 1}.0`;
    }
    
    const minor = parseInt(parts[parts.length - 1], 10) + 1;
    parts[parts.length - 1] = minor.toString();
    
    return parts.join('.');
  }
}

module.exports = new AssessmentTemplateService();
