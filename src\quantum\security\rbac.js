/**
 * Role-Based Access Control (RBAC)
 *
 * This module provides role-based access control capabilities for the Finite Universe
 * Principle defense system, enabling fine-grained access control.
 */

const EventEmitter = require('events');

/**
 * RBAC class
 * 
 * Provides role-based access control capabilities.
 */
class RBAC extends EventEmitter {
  /**
   * Constructor
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();

    this.options = {
      enableLogging: true,
      defaultRole: options.defaultRole || 'guest',
      superRole: options.superRole || 'admin',
      ...options
    };

    // Initialize roles registry
    this.roles = new Map();
    
    // Initialize permissions registry
    this.permissions = new Map();
    
    // Initialize user roles registry
    this.userRoles = new Map();
    
    // Initialize default roles
    this._initializeDefaultRoles();

    if (this.options.enableLogging) {
      console.log('RBAC initialized with options:', this.options);
    }
  }

  /**
   * Initialize default roles
   * @private
   */
  _initializeDefaultRoles() {
    // Create default roles
    this.createRole('guest', 'Guest user with limited access');
    this.createRole('user', 'Regular user with standard access');
    this.createRole('manager', 'Manager with elevated access');
    this.createRole(this.options.superRole, 'Super user with full access');
    
    // Create default permissions
    this.createPermission('read', 'Read access');
    this.createPermission('write', 'Write access');
    this.createPermission('delete', 'Delete access');
    this.createPermission('admin', 'Administrative access');
    
    // Assign default permissions to roles
    this.assignPermissionToRole('guest', 'read');
    this.assignPermissionToRole('user', 'read');
    this.assignPermissionToRole('user', 'write');
    this.assignPermissionToRole('manager', 'read');
    this.assignPermissionToRole('manager', 'write');
    this.assignPermissionToRole('manager', 'delete');
    this.assignPermissionToRole(this.options.superRole, 'read');
    this.assignPermissionToRole(this.options.superRole, 'write');
    this.assignPermissionToRole(this.options.superRole, 'delete');
    this.assignPermissionToRole(this.options.superRole, 'admin');
  }

  /**
   * Create a role
   * @param {string} roleId - Role ID
   * @param {string} description - Role description
   * @returns {boolean} - True if role was created, false otherwise
   */
  createRole(roleId, description = '') {
    if (this.roles.has(roleId)) {
      if (this.options.enableLogging) {
        console.log(`Role ${roleId} already exists`);
      }
      return false;
    }
    
    // Create role
    this.roles.set(roleId, {
      id: roleId,
      description,
      permissions: new Set(),
      createdAt: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log(`Role ${roleId} created`);
    }
    
    // Emit role-created event
    this.emit('role-created', { roleId, description });
    
    return true;
  }

  /**
   * Delete a role
   * @param {string} roleId - Role ID
   * @returns {boolean} - True if role was deleted, false otherwise
   */
  deleteRole(roleId) {
    // Check if role exists
    if (!this.roles.has(roleId)) {
      if (this.options.enableLogging) {
        console.log(`Role ${roleId} not found`);
      }
      return false;
    }
    
    // Check if role is protected
    if (roleId === this.options.superRole) {
      if (this.options.enableLogging) {
        console.log(`Cannot delete super role ${roleId}`);
      }
      return false;
    }
    
    // Get role information
    const role = this.roles.get(roleId);
    
    // Delete role
    this.roles.delete(roleId);
    
    // Remove role from users
    for (const [userId, roles] of this.userRoles.entries()) {
      if (roles.has(roleId)) {
        roles.delete(roleId);
        
        // If user has no roles, assign default role
        if (roles.size === 0) {
          roles.add(this.options.defaultRole);
        }
      }
    }
    
    if (this.options.enableLogging) {
      console.log(`Role ${roleId} deleted`);
    }
    
    // Emit role-deleted event
    this.emit('role-deleted', { roleId, role });
    
    return true;
  }

  /**
   * Create a permission
   * @param {string} permissionId - Permission ID
   * @param {string} description - Permission description
   * @returns {boolean} - True if permission was created, false otherwise
   */
  createPermission(permissionId, description = '') {
    if (this.permissions.has(permissionId)) {
      if (this.options.enableLogging) {
        console.log(`Permission ${permissionId} already exists`);
      }
      return false;
    }
    
    // Create permission
    this.permissions.set(permissionId, {
      id: permissionId,
      description,
      createdAt: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log(`Permission ${permissionId} created`);
    }
    
    // Emit permission-created event
    this.emit('permission-created', { permissionId, description });
    
    return true;
  }

  /**
   * Delete a permission
   * @param {string} permissionId - Permission ID
   * @returns {boolean} - True if permission was deleted, false otherwise
   */
  deletePermission(permissionId) {
    // Check if permission exists
    if (!this.permissions.has(permissionId)) {
      if (this.options.enableLogging) {
        console.log(`Permission ${permissionId} not found`);
      }
      return false;
    }
    
    // Get permission information
    const permission = this.permissions.get(permissionId);
    
    // Delete permission
    this.permissions.delete(permissionId);
    
    // Remove permission from roles
    for (const role of this.roles.values()) {
      role.permissions.delete(permissionId);
    }
    
    if (this.options.enableLogging) {
      console.log(`Permission ${permissionId} deleted`);
    }
    
    // Emit permission-deleted event
    this.emit('permission-deleted', { permissionId, permission });
    
    return true;
  }

  /**
   * Assign a permission to a role
   * @param {string} roleId - Role ID
   * @param {string} permissionId - Permission ID
   * @returns {boolean} - True if permission was assigned, false otherwise
   */
  assignPermissionToRole(roleId, permissionId) {
    // Check if role exists
    if (!this.roles.has(roleId)) {
      if (this.options.enableLogging) {
        console.log(`Role ${roleId} not found`);
      }
      return false;
    }
    
    // Check if permission exists
    if (!this.permissions.has(permissionId)) {
      if (this.options.enableLogging) {
        console.log(`Permission ${permissionId} not found`);
      }
      return false;
    }
    
    // Get role
    const role = this.roles.get(roleId);
    
    // Check if permission is already assigned
    if (role.permissions.has(permissionId)) {
      if (this.options.enableLogging) {
        console.log(`Permission ${permissionId} already assigned to role ${roleId}`);
      }
      return false;
    }
    
    // Assign permission to role
    role.permissions.add(permissionId);
    
    if (this.options.enableLogging) {
      console.log(`Permission ${permissionId} assigned to role ${roleId}`);
    }
    
    // Emit permission-assigned event
    this.emit('permission-assigned', { roleId, permissionId });
    
    return true;
  }

  /**
   * Revoke a permission from a role
   * @param {string} roleId - Role ID
   * @param {string} permissionId - Permission ID
   * @returns {boolean} - True if permission was revoked, false otherwise
   */
  revokePermissionFromRole(roleId, permissionId) {
    // Check if role exists
    if (!this.roles.has(roleId)) {
      if (this.options.enableLogging) {
        console.log(`Role ${roleId} not found`);
      }
      return false;
    }
    
    // Get role
    const role = this.roles.get(roleId);
    
    // Check if permission is assigned
    if (!role.permissions.has(permissionId)) {
      if (this.options.enableLogging) {
        console.log(`Permission ${permissionId} not assigned to role ${roleId}`);
      }
      return false;
    }
    
    // Revoke permission from role
    role.permissions.delete(permissionId);
    
    if (this.options.enableLogging) {
      console.log(`Permission ${permissionId} revoked from role ${roleId}`);
    }
    
    // Emit permission-revoked event
    this.emit('permission-revoked', { roleId, permissionId });
    
    return true;
  }

  /**
   * Assign a role to a user
   * @param {string} userId - User ID
   * @param {string} roleId - Role ID
   * @returns {boolean} - True if role was assigned, false otherwise
   */
  assignRoleToUser(userId, roleId) {
    // Check if role exists
    if (!this.roles.has(roleId)) {
      if (this.options.enableLogging) {
        console.log(`Role ${roleId} not found`);
      }
      return false;
    }
    
    // Get user roles
    let userRoles = this.userRoles.get(userId);
    
    // Create user roles if not exists
    if (!userRoles) {
      userRoles = new Set();
      this.userRoles.set(userId, userRoles);
    }
    
    // Check if role is already assigned
    if (userRoles.has(roleId)) {
      if (this.options.enableLogging) {
        console.log(`Role ${roleId} already assigned to user ${userId}`);
      }
      return false;
    }
    
    // Assign role to user
    userRoles.add(roleId);
    
    if (this.options.enableLogging) {
      console.log(`Role ${roleId} assigned to user ${userId}`);
    }
    
    // Emit role-assigned event
    this.emit('role-assigned', { userId, roleId });
    
    return true;
  }

  /**
   * Revoke a role from a user
   * @param {string} userId - User ID
   * @param {string} roleId - Role ID
   * @returns {boolean} - True if role was revoked, false otherwise
   */
  revokeRoleFromUser(userId, roleId) {
    // Get user roles
    const userRoles = this.userRoles.get(userId);
    
    // Check if user has roles
    if (!userRoles) {
      if (this.options.enableLogging) {
        console.log(`User ${userId} has no roles`);
      }
      return false;
    }
    
    // Check if role is assigned
    if (!userRoles.has(roleId)) {
      if (this.options.enableLogging) {
        console.log(`Role ${roleId} not assigned to user ${userId}`);
      }
      return false;
    }
    
    // Revoke role from user
    userRoles.delete(roleId);
    
    // If user has no roles, assign default role
    if (userRoles.size === 0) {
      userRoles.add(this.options.defaultRole);
    }
    
    if (this.options.enableLogging) {
      console.log(`Role ${roleId} revoked from user ${userId}`);
    }
    
    // Emit role-revoked event
    this.emit('role-revoked', { userId, roleId });
    
    return true;
  }

  /**
   * Check if user has permission
   * @param {string} userId - User ID
   * @param {string} permissionId - Permission ID
   * @returns {boolean} - True if user has permission, false otherwise
   */
  hasPermission(userId, permissionId) {
    // Get user roles
    const userRoles = this.userRoles.get(userId);
    
    // Check if user has roles
    if (!userRoles) {
      // Assign default role
      this.assignRoleToUser(userId, this.options.defaultRole);
      
      // Get default role
      const defaultRole = this.roles.get(this.options.defaultRole);
      
      // Check if default role has permission
      return defaultRole.permissions.has(permissionId);
    }
    
    // Check if user has super role
    if (userRoles.has(this.options.superRole)) {
      return true;
    }
    
    // Check if any of user's roles has permission
    for (const roleId of userRoles) {
      const role = this.roles.get(roleId);
      
      if (role && role.permissions.has(permissionId)) {
        return true;
      }
    }
    
    return false;
  }

  /**
   * Get user roles
   * @param {string} userId - User ID
   * @returns {Array} - Array of role IDs
   */
  getUserRoles(userId) {
    // Get user roles
    const userRoles = this.userRoles.get(userId);
    
    // Check if user has roles
    if (!userRoles) {
      // Assign default role
      this.assignRoleToUser(userId, this.options.defaultRole);
      
      return [this.options.defaultRole];
    }
    
    return Array.from(userRoles);
  }

  /**
   * Get role permissions
   * @param {string} roleId - Role ID
   * @returns {Array} - Array of permission IDs
   */
  getRolePermissions(roleId) {
    // Check if role exists
    if (!this.roles.has(roleId)) {
      if (this.options.enableLogging) {
        console.log(`Role ${roleId} not found`);
      }
      return [];
    }
    
    // Get role
    const role = this.roles.get(roleId);
    
    return Array.from(role.permissions);
  }

  /**
   * Get all roles
   * @returns {Array} - Array of role objects
   */
  getRoles() {
    return Array.from(this.roles.values()).map(role => ({
      id: role.id,
      description: role.description,
      permissions: Array.from(role.permissions),
      createdAt: role.createdAt
    }));
  }

  /**
   * Get all permissions
   * @returns {Array} - Array of permission objects
   */
  getPermissions() {
    return Array.from(this.permissions.values());
  }

  /**
   * Dispose resources
   */
  dispose() {
    // Clear registries
    this.roles.clear();
    this.permissions.clear();
    this.userRoles.clear();
    
    if (this.options.enableLogging) {
      console.log('RBAC disposed');
    }
  }
}

/**
 * Create an RBAC instance with recommended settings
 * @param {Object} options - Configuration options
 * @returns {RBAC} - Configured RBAC instance
 */
function createRBAC(options = {}) {
  return new RBAC({
    enableLogging: true,
    defaultRole: 'guest',
    superRole: 'admin',
    ...options
  });
}

module.exports = {
  RBAC,
  createRBAC
};
