#!/bin/bash

echo ""
echo "🌌 NovaFuse Cosmic Alignment Simulator (NCAS)"
echo "   International Demonstration Suite"
echo "   World's First Physics-Based AI Safety System"
echo ""
echo "🚀 Launching demonstration..."
echo ""

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed or not in PATH"
    echo "Please install Node.js from https://nodejs.org/"
    exit 1
fi

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install express socket.io
fi

# Launch the demonstration
echo "🌟 Starting NCAS International Demo..."
echo ""
echo "📍 Demo will be available at: http://localhost:3142"
echo "🌐 Browser will open automatically"
echo ""
echo "⚡ Press Ctrl+C to stop the demonstration"
echo ""

node launch_international_demo.js
