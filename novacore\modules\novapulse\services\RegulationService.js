/**
 * NovaCore Regulation Service
 * 
 * This service provides functionality for managing regulations.
 * NovaPulse is the Universal Regulatory Compliance Monitoring System (URCMS) component
 * of the NovaFuse Cyber-Safety Platform.
 */

const { Regulation } = require('../models');
const logger = require('../../../config/logger');
const { ValidationError, NotFoundError } = require('../../../api/utils/errors');

class RegulationService {
  /**
   * Create a new regulation
   * @param {Object} data - Regulation data
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Created regulation
   */
  async createRegulation(data, userId) {
    try {
      logger.info('Creating new regulation', { name: data.name });
      
      // Set created by and updated by
      data.createdBy = userId;
      data.updatedBy = userId;
      
      // Create regulation
      const regulation = new Regulation(data);
      await regulation.save();
      
      logger.info('Regulation created successfully', { id: regulation._id });
      
      return regulation;
    } catch (error) {
      logger.error('Error creating regulation', { error });
      throw error;
    }
  }
  
  /**
   * Get all regulations
   * @param {Object} filter - Filter criteria
   * @param {Object} options - Query options (pagination, sorting)
   * @returns {Promise<Object>} - Regulations with pagination info
   */
  async getAllRegulations(filter = {}, options = {}) {
    try {
      const { page = 1, limit = 10, sort = { name: 1 } } = options;
      
      // Build query
      const query = {};
      
      // Apply filters
      if (filter.name) {
        query.name = { $regex: filter.name, $options: 'i' };
      }
      
      if (filter.shortName) {
        query.shortName = { $regex: filter.shortName, $options: 'i' };
      }
      
      if (filter.type) {
        query.type = filter.type;
      }
      
      if (filter.category) {
        query.category = filter.category;
      }
      
      if (filter.status) {
        query.status = filter.status;
      }
      
      if (filter.country) {
        query['$or'] = [
          { 'jurisdiction.country': filter.country },
          { 'jurisdiction.isGlobal': true }
        ];
      }
      
      if (filter.region) {
        query['$or'] = [
          { 'jurisdiction.region': filter.region },
          { 'jurisdiction.isGlobal': true }
        ];
      }
      
      if (filter.industry) {
        query['applicability.industries'] = filter.industry;
      }
      
      if (filter.organizationType) {
        query['applicability.organizationTypes'] = filter.organizationType;
      }
      
      if (filter.dataType) {
        query['applicability.dataTypes'] = filter.dataType;
      }
      
      if (filter.search) {
        query['$or'] = [
          { name: { $regex: filter.search, $options: 'i' } },
          { shortName: { $regex: filter.search, $options: 'i' } },
          { description: { $regex: filter.search, $options: 'i' } }
        ];
      }
      
      // Execute query with pagination
      const skip = (page - 1) * limit;
      
      const [regulations, total] = await Promise.all([
        Regulation.find(query)
          .sort(sort)
          .skip(skip)
          .limit(limit),
        Regulation.countDocuments(query)
      ]);
      
      // Calculate pagination info
      const totalPages = Math.ceil(total / limit);
      const hasNext = page < totalPages;
      const hasPrev = page > 1;
      
      return {
        data: regulations,
        pagination: {
          total,
          page,
          limit,
          totalPages,
          hasNext,
          hasPrev
        }
      };
    } catch (error) {
      logger.error('Error getting regulations', { error });
      throw error;
    }
  }
  
  /**
   * Get regulation by ID
   * @param {string} id - Regulation ID
   * @returns {Promise<Object>} - Regulation
   */
  async getRegulationById(id) {
    try {
      const regulation = await Regulation.findById(id);
      
      if (!regulation) {
        throw new NotFoundError(`Regulation with ID ${id} not found`);
      }
      
      return regulation;
    } catch (error) {
      logger.error('Error getting regulation by ID', { id, error });
      throw error;
    }
  }
  
  /**
   * Update regulation
   * @param {string} id - Regulation ID
   * @param {Object} data - Updated regulation data
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Updated regulation
   */
  async updateRegulation(id, data, userId) {
    try {
      // Get existing regulation
      const regulation = await this.getRegulationById(id);
      
      // Set updated by
      data.updatedBy = userId;
      
      // Update regulation
      Object.assign(regulation, data);
      await regulation.save();
      
      logger.info('Regulation updated successfully', { id });
      
      return regulation;
    } catch (error) {
      logger.error('Error updating regulation', { id, error });
      throw error;
    }
  }
  
  /**
   * Delete regulation
   * @param {string} id - Regulation ID
   * @returns {Promise<boolean>} - Deletion success
   */
  async deleteRegulation(id) {
    try {
      const result = await Regulation.findByIdAndDelete(id);
      
      if (!result) {
        throw new NotFoundError(`Regulation with ID ${id} not found`);
      }
      
      logger.info('Regulation deleted successfully', { id });
      
      return true;
    } catch (error) {
      logger.error('Error deleting regulation', { id, error });
      throw error;
    }
  }
  
  /**
   * Add requirement to regulation
   * @param {string} id - Regulation ID
   * @param {Object} requirement - Requirement data
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Updated regulation
   */
  async addRequirement(id, requirement, userId) {
    try {
      // Get existing regulation
      const regulation = await this.getRegulationById(id);
      
      // Check if requirement ID already exists
      if (regulation.requirements.some(req => req.id === requirement.id)) {
        throw new ValidationError(`Requirement with ID ${requirement.id} already exists`);
      }
      
      // Add requirement
      regulation.requirements.push(requirement);
      
      // Set updated by
      regulation.updatedBy = userId;
      
      // Save regulation
      await regulation.save();
      
      logger.info('Requirement added successfully', { 
        regulationId: id, 
        requirementId: requirement.id 
      });
      
      return regulation;
    } catch (error) {
      logger.error('Error adding requirement', { id, error });
      throw error;
    }
  }
  
  /**
   * Update requirement
   * @param {string} id - Regulation ID
   * @param {string} requirementId - Requirement ID
   * @param {Object} data - Updated requirement data
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Updated regulation
   */
  async updateRequirement(id, requirementId, data, userId) {
    try {
      // Get existing regulation
      const regulation = await this.getRegulationById(id);
      
      // Find requirement
      const requirementIndex = regulation.requirements.findIndex(req => req.id === requirementId);
      
      if (requirementIndex === -1) {
        throw new NotFoundError(`Requirement with ID ${requirementId} not found`);
      }
      
      // Update requirement
      regulation.requirements[requirementIndex] = {
        ...regulation.requirements[requirementIndex].toObject(),
        ...data,
        id: requirementId // Ensure ID doesn't change
      };
      
      // Set updated by
      regulation.updatedBy = userId;
      
      // Save regulation
      await regulation.save();
      
      logger.info('Requirement updated successfully', { 
        regulationId: id, 
        requirementId 
      });
      
      return regulation;
    } catch (error) {
      logger.error('Error updating requirement', { id, requirementId, error });
      throw error;
    }
  }
  
  /**
   * Remove requirement
   * @param {string} id - Regulation ID
   * @param {string} requirementId - Requirement ID
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Updated regulation
   */
  async removeRequirement(id, requirementId, userId) {
    try {
      // Get existing regulation
      const regulation = await this.getRegulationById(id);
      
      // Find requirement
      const requirementIndex = regulation.requirements.findIndex(req => req.id === requirementId);
      
      if (requirementIndex === -1) {
        throw new NotFoundError(`Requirement with ID ${requirementId} not found`);
      }
      
      // Remove requirement
      regulation.requirements.splice(requirementIndex, 1);
      
      // Set updated by
      regulation.updatedBy = userId;
      
      // Save regulation
      await regulation.save();
      
      logger.info('Requirement removed successfully', { 
        regulationId: id, 
        requirementId 
      });
      
      return regulation;
    } catch (error) {
      logger.error('Error removing requirement', { id, requirementId, error });
      throw error;
    }
  }
  
  /**
   * Add version to regulation
   * @param {string} id - Regulation ID
   * @param {Object} version - Version data
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Updated regulation
   */
  async addVersion(id, version, userId) {
    try {
      // Get existing regulation
      const regulation = await this.getRegulationById(id);
      
      // Check if version number already exists
      if (regulation.versions.some(v => v.versionNumber === version.versionNumber)) {
        throw new ValidationError(`Version ${version.versionNumber} already exists`);
      }
      
      // Add version
      regulation.versions.push(version);
      
      // Update current version if specified
      if (version.status === 'effective') {
        regulation.currentVersion = version.versionNumber;
      }
      
      // Set updated by
      regulation.updatedBy = userId;
      
      // Save regulation
      await regulation.save();
      
      logger.info('Version added successfully', { 
        regulationId: id, 
        versionNumber: version.versionNumber 
      });
      
      return regulation;
    } catch (error) {
      logger.error('Error adding version', { id, error });
      throw error;
    }
  }
  
  /**
   * Update version
   * @param {string} id - Regulation ID
   * @param {string} versionNumber - Version number
   * @param {Object} data - Updated version data
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Updated regulation
   */
  async updateVersion(id, versionNumber, data, userId) {
    try {
      // Get existing regulation
      const regulation = await this.getRegulationById(id);
      
      // Find version
      const versionIndex = regulation.versions.findIndex(v => v.versionNumber === versionNumber);
      
      if (versionIndex === -1) {
        throw new NotFoundError(`Version ${versionNumber} not found`);
      }
      
      // Update version
      regulation.versions[versionIndex] = {
        ...regulation.versions[versionIndex].toObject(),
        ...data,
        versionNumber // Ensure version number doesn't change
      };
      
      // Update current version if specified
      if (data.status === 'effective') {
        regulation.currentVersion = versionNumber;
      }
      
      // Set updated by
      regulation.updatedBy = userId;
      
      // Save regulation
      await regulation.save();
      
      logger.info('Version updated successfully', { 
        regulationId: id, 
        versionNumber 
      });
      
      return regulation;
    } catch (error) {
      logger.error('Error updating version', { id, versionNumber, error });
      throw error;
    }
  }
  
  /**
   * Find regulations by jurisdiction
   * @param {string} country - Country
   * @param {string} region - Region
   * @returns {Promise<Array>} - Regulations
   */
  async findByJurisdiction(country, region) {
    try {
      return await Regulation.findByJurisdiction(country, region);
    } catch (error) {
      logger.error('Error finding regulations by jurisdiction', { country, region, error });
      throw error;
    }
  }
  
  /**
   * Find regulations by category
   * @param {string} category - Category
   * @returns {Promise<Array>} - Regulations
   */
  async findByCategory(category) {
    try {
      return await Regulation.findByCategory(category);
    } catch (error) {
      logger.error('Error finding regulations by category', { category, error });
      throw error;
    }
  }
  
  /**
   * Find regulations by applicability
   * @param {Object} criteria - Applicability criteria
   * @returns {Promise<Array>} - Regulations
   */
  async findByApplicability(criteria) {
    try {
      return await Regulation.findByApplicability(criteria);
    } catch (error) {
      logger.error('Error finding regulations by applicability', { criteria, error });
      throw error;
    }
  }
  
  /**
   * Find regulations by requirement ID
   * @param {string} requirementId - Requirement ID
   * @returns {Promise<Array>} - Regulations
   */
  async findByRequirementId(requirementId) {
    try {
      return await Regulation.findByRequirementId(requirementId);
    } catch (error) {
      logger.error('Error finding regulations by requirement ID', { requirementId, error });
      throw error;
    }
  }
  
  /**
   * Find regulations by control mapping
   * @param {string} frameworkId - Framework ID
   * @param {string} controlId - Control ID
   * @returns {Promise<Array>} - Regulations
   */
  async findByControlMapping(frameworkId, controlId) {
    try {
      return await Regulation.findByControlMapping(frameworkId, controlId);
    } catch (error) {
      logger.error('Error finding regulations by control mapping', { frameworkId, controlId, error });
      throw error;
    }
  }
}

module.exports = new RegulationService();
