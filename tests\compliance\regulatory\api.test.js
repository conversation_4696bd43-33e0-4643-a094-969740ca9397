const request = require('supertest');
const express = require('express');
const router = require('../../../apis/compliance/regulatory/routes');

// Mock data
jest.mock('../../../apis/compliance/regulatory/models', () => ({
  regulatoryFrameworks: [
    {
      id: 'rf-0001',
      name: 'General Data Protection Regulation',
      shortName: 'GDPR',
      description: 'EU regulation on data protection and privacy',
      version: '2016/679',
      effectiveDate: '2018-05-25',
      category: 'privacy',
      jurisdiction: 'eu',
      status: 'active',
      website: 'https://gdpr.eu/',
      enforcementAuthority: 'Data Protection Authorities of EU Member States',
      lastUpdated: '2021-06-15T10:30:00Z',
      createdAt: '2021-01-10T08:15:00Z',
      updatedAt: '2021-06-15T10:30:00Z'
    }
  ],
  regulatoryRequirements: [
    {
      id: 'rr-0001',
      frameworkId: 'rf-0001',
      code: 'GDPR-A5',
      name: 'Principles relating to processing of personal data',
      description: 'Personal data shall be processed lawfully, fairly and in a transparent manner',
      article: 'Article 5',
      section: '1(a)',
      category: 'data-processing',
      priority: 'high',
      status: 'applicable',
      applicableJurisdictions: ['eu', 'eea'],
      controlObjectives: ['Ensure lawful basis for processing'],
      relatedRequirements: [],
      createdAt: '2021-01-10T09:00:00Z',
      updatedAt: '2021-06-15T11:00:00Z'
    }
  ],
  jurisdictions: [
    {
      id: 'j-0001',
      code: 'global',
      name: 'Global',
      type: 'global',
      description: 'Applicable worldwide',
      createdAt: '2021-01-05T08:00:00Z',
      updatedAt: '2021-01-05T08:00:00Z'
    }
  ],
  regulatoryChanges: [
    {
      id: 'rc-0001',
      frameworkId: 'rf-0001',
      title: 'Updated Guidelines on Consent',
      description: 'European Data Protection Board published updated guidelines on consent under GDPR.',
      changeType: 'guidance',
      publicationDate: '2021-05-10',
      effectiveDate: '2021-06-01',
      source: 'European Data Protection Board',
      sourceUrl: 'https://edpb.europa.eu/guidelines',
      impactLevel: 'medium',
      affectedRequirements: ['rr-0003'],
      status: 'published',
      summary: 'The updated guidelines clarify requirements for valid consent, particularly in the context of cookie banners and tracking technologies.',
      createdAt: '2021-05-12T10:00:00Z',
      updatedAt: '2021-05-12T10:00:00Z'
    }
  ],
  regulatoryReports: [
    {
      id: 'rpt-0001',
      frameworkId: 'rf-0001',
      name: 'GDPR Annual Compliance Report',
      description: 'Annual report documenting GDPR compliance status and activities.',
      type: 'internal',
      frequency: 'annual',
      lastSubmissionDate: '2021-12-15',
      nextDueDate: '2022-12-15',
      assignee: 'Data Protection Officer',
      status: 'completed',
      templateUrl: '/templates/gdpr-annual-report.docx',
      sections: [
        'Executive Summary',
        'Data Processing Activities',
        'Data Subject Rights Requests'
      ],
      createdAt: '2021-01-20T11:00:00Z',
      updatedAt: '2021-12-16T09:30:00Z'
    }
  ]
}));

// Create a test Express app
const app = express();
app.use(express.json());
app.use('/compliance/regulatory', router);

describe('Regulatory Compliance API', () => {
  describe('GET /compliance/regulatory/frameworks', () => {
    it('should return a list of frameworks', async () => {
      const response = await request(app).get('/compliance/regulatory/frameworks');
      expect(response.status).toBe(200);
      expect(response.body.data.length).toBeGreaterThan(0);
      // Check if GDPR is in the list
      expect(response.body.data.some(framework => framework.shortName === 'GDPR')).toBe(true);
    });
  });

  describe('GET /compliance/regulatory/frameworks/:id', () => {
    it('should return a specific framework', async () => {
      const response = await request(app).get('/compliance/regulatory/frameworks/rf-0001');
      expect(response.status).toBe(200);
      expect(response.body.data.id).toBe('rf-0001');
      expect(response.body.data.shortName).toBe('GDPR');
    });

    it('should return 404 for non-existent framework', async () => {
      const response = await request(app).get('/compliance/regulatory/frameworks/non-existent');
      expect(response.status).toBe(404);
    });
  });

  describe('GET /compliance/regulatory/requirements', () => {
    it('should return a list of requirements', async () => {
      const response = await request(app).get('/compliance/regulatory/requirements');
      expect(response.status).toBe(200);
      expect(response.body.data.length).toBeGreaterThan(0);
      // Check if GDPR-A5 is in the list
      expect(response.body.data.some(req => req.code === 'GDPR-A5')).toBe(true);
    });
  });

  describe('GET /compliance/regulatory/jurisdictions', () => {
    it('should return a list of jurisdictions', async () => {
      const response = await request(app).get('/compliance/regulatory/jurisdictions');
      expect(response.status).toBe(200);
      expect(response.body.data.length).toBeGreaterThan(0);
      // Check if global jurisdiction is in the list
      expect(response.body.data.some(j => j.code === 'global')).toBe(true);
    });
  });

  describe('GET /compliance/regulatory/changes', () => {
    it('should return a list of regulatory changes', async () => {
      const response = await request(app).get('/compliance/regulatory/changes');
      expect(response.status).toBe(200);
      expect(response.body.data.length).toBeGreaterThan(0);
      // Check if the expected change is in the list
      expect(response.body.data.some(change => change.title === 'Updated Guidelines on Consent')).toBe(true);
    });
  });

  describe('GET /compliance/regulatory/changes/:id', () => {
    it('should return a specific regulatory change', async () => {
      const response = await request(app).get('/compliance/regulatory/changes/rc-0001');
      expect(response.status).toBe(200);
      expect(response.body.data.id).toBe('rc-0001');
      expect(response.body.data.changeType).toBe('guidance');
    });

    it('should return 404 for non-existent regulatory change', async () => {
      const response = await request(app).get('/compliance/regulatory/changes/non-existent');
      expect(response.status).toBe(404);
    });
  });

  describe('GET /compliance/regulatory/reports', () => {
    it('should return a list of regulatory reports', async () => {
      const response = await request(app).get('/compliance/regulatory/reports');
      expect(response.status).toBe(200);
      expect(response.body.data.length).toBeGreaterThan(0);
      // Check if the expected report is in the list
      expect(response.body.data.some(report => report.name === 'GDPR Annual Compliance Report')).toBe(true);
    });
  });

  describe('GET /compliance/regulatory/reports/:id', () => {
    it('should return a specific regulatory report', async () => {
      const response = await request(app).get('/compliance/regulatory/reports/rpt-0001');
      expect(response.status).toBe(200);
      expect(response.body.data.id).toBe('rpt-0001');
      expect(response.body.data.type).toBe('internal');
    });

    it('should return 404 for non-existent regulatory report', async () => {
      const response = await request(app).get('/compliance/regulatory/reports/non-existent');
      expect(response.status).toBe(404);
    });
  });
});
