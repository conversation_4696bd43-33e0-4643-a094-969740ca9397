const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  testTypes: ['unit', 'integration', 'performance', 'security'],
  apiCategories: [
    'api-management/lifecycle',
    'compliance/automation',
    'esg/disclosures',
    'esg/frameworks',
    'esg/metrics',
    'esg/reports',
    'esg/targets',
    'risk-audit/control-testing',
    'security/assessment'
  ]
};

// Create results directory if it doesn't exist
const resultsDir = path.join(__dirname, '../test-results');
if (!fs.existsSync(resultsDir)) {
  fs.mkdirSync(resultsDir, { recursive: true });
}

// Function to run tests for a specific API and test type
function runTests(apiCategory, testType) {
  const testDir = path.join(__dirname, apiCategory, testType);
  
  // Skip if directory doesn't exist
  if (!fs.existsSync(testDir)) {
    console.log(`Skipping ${apiCategory}/${testType} tests - directory not found`);
    return { success: true, skipped: true };
  }
  
  // Check if there are any test files
  const testFiles = fs.readdirSync(testDir).filter(file => file.endsWith('.test.js') || file.endsWith('.spec.js'));
  if (testFiles.length === 0) {
    console.log(`Skipping ${apiCategory}/${testType} tests - no test files found`);
    return { success: true, skipped: true };
  }
  
  console.log(`Running ${apiCategory}/${testType} tests...`);
  
  try {
    // Run Jest for the specific test directory
    const command = `npx jest ${testDir} --colors --reporters=default --reporters=jest-junit`;
    execSync(command, { stdio: 'inherit' });
    console.log(`✅ ${apiCategory}/${testType} tests passed`);
    return { success: true, skipped: false };
  } catch (error) {
    console.error(`❌ ${apiCategory}/${testType} tests failed`);
    return { success: false, skipped: false };
  }
}

// Main function to run all tests
function runAllTests() {
  console.log('Starting API tests...');
  
  const results = {
    total: 0,
    passed: 0,
    failed: 0,
    skipped: 0,
    details: []
  };
  
  // Run tests for each API category and test type
  for (const apiCategory of config.apiCategories) {
    for (const testType of config.testTypes) {
      const result = runTests(apiCategory, testType);
      
      results.total++;
      if (result.skipped) {
        results.skipped++;
      } else if (result.success) {
        results.passed++;
      } else {
        results.failed++;
      }
      
      results.details.push({
        apiCategory,
        testType,
        success: result.success,
        skipped: result.skipped
      });
    }
  }
  
  // Print summary
  console.log('\n--- Test Summary ---');
  console.log(`Total: ${results.total}`);
  console.log(`Passed: ${results.passed}`);
  console.log(`Failed: ${results.failed}`);
  console.log(`Skipped: ${results.skipped}`);
  console.log(`Success Rate: ${Math.round((results.passed / (results.total - results.skipped)) * 100)}%`);
  
  // Write results to file
  fs.writeFileSync(
    path.join(resultsDir, 'test-summary.json'),
    JSON.stringify(results, null, 2)
  );
  
  return results.failed === 0;
}

// Run all tests
const success = runAllTests();
process.exit(success ? 0 : 1);
