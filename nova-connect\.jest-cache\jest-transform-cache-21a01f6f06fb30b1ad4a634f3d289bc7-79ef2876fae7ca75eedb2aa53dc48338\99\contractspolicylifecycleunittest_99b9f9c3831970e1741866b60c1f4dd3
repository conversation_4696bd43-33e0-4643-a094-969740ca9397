45f0c4d08f02f38865e1e9a404682b2e
// Mock axios
_getJestObj().mock('axios');

// Mock logger
_getJestObj().mock('../../../../utils/logger', () => ({
  createLogger: jest.fn(() => ({
    info: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn()
  }))
}));
function _getJestObj() {
  const {
    jest
  } = require("@jest/globals");
  _getJestObj = () => jest;
  return jest;
}
/**
 * Unit tests for the Contracts & Policy Lifecycle Connector
 */

const axios = require('axios');
const ContractsPolicyLifecycleConnector = require('../../../../connector/implementations/contracts-policy-lifecycle');
describe('ContractsPolicyLifecycleConnector', () => {
  let connector;
  let mockConfig;
  let mockCredentials;
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();

    // Mock config and credentials
    mockConfig = {
      baseUrl: 'https://api.test.com'
    };
    mockCredentials = {
      clientId: 'test-client-id',
      clientSecret: 'test-client-secret',
      redirectUri: 'https://test-redirect.com'
    };

    // Create connector instance
    connector = new ContractsPolicyLifecycleConnector(mockConfig, mockCredentials);
  });
  describe('constructor', () => {
    it('should initialize with provided config and credentials', () => {
      expect(connector.config).toEqual(mockConfig);
      expect(connector.credentials).toEqual(mockCredentials);
      expect(connector.baseUrl).toBe(mockConfig.baseUrl);
    });
    it('should use default baseUrl if not provided', () => {
      const connectorWithDefaults = new ContractsPolicyLifecycleConnector();
      expect(connectorWithDefaults.baseUrl).toBe('https://api.example.com');
    });
  });
  describe('initialize', () => {
    it('should authenticate if credentials are provided', async () => {
      // Mock authenticate method
      connector.authenticate = jest.fn().mockResolvedValue();
      await connector.initialize();
      expect(connector.authenticate).toHaveBeenCalled();
    });
    it('should not authenticate if credentials are not provided', async () => {
      // Create connector without credentials
      const connectorWithoutCredentials = new ContractsPolicyLifecycleConnector(mockConfig, {});

      // Mock authenticate method
      connectorWithoutCredentials.authenticate = jest.fn().mockResolvedValue();
      await connectorWithoutCredentials.initialize();
      expect(connectorWithoutCredentials.authenticate).not.toHaveBeenCalled();
    });
  });
  describe('authenticate', () => {
    it('should make a POST request to the token endpoint', async () => {
      // Mock axios post response
      axios.post.mockResolvedValue({
        data: {
          access_token: 'test-access-token',
          expires_in: 3600
        }
      });
      await connector.authenticate();
      expect(axios.post).toHaveBeenCalledWith(`${mockConfig.baseUrl}/oauth2/token`, {
        grant_type: 'client_credentials',
        client_id: mockCredentials.clientId,
        client_secret: mockCredentials.clientSecret,
        scope: 'read:contracts write:contracts read:policies write:policies'
      }, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': 'application/json'
        }
      });
      expect(connector.accessToken).toBe('test-access-token');
      expect(connector.tokenExpiry).toBeDefined();
    });
    it('should throw an error if authentication fails', async () => {
      // Mock axios post error
      const errorMessage = 'Authentication failed';
      axios.post.mockRejectedValue(new Error(errorMessage));
      await expect(connector.authenticate()).rejects.toThrow(`Authentication failed: ${errorMessage}`);
    });
  });
  describe('getAuthHeaders', () => {
    it('should return authorization headers with access token', async () => {
      // Set access token and expiry
      connector.accessToken = 'test-access-token';
      connector.tokenExpiry = Date.now() + 3600000; // 1 hour from now

      const headers = await connector.getAuthHeaders();
      expect(headers).toEqual({
        'Authorization': 'Bearer test-access-token'
      });
    });
    it('should authenticate if access token is not set', async () => {
      // Mock authenticate method
      connector.authenticate = jest.fn().mockImplementation(() => {
        connector.accessToken = 'new-access-token';
        connector.tokenExpiry = Date.now() + 3600000;
      });
      const headers = await connector.getAuthHeaders();
      expect(connector.authenticate).toHaveBeenCalled();
      expect(headers).toEqual({
        'Authorization': 'Bearer new-access-token'
      });
    });
    it('should authenticate if token is about to expire', async () => {
      // Set access token and expiry to 4 minutes from now (less than 5 minutes)
      connector.accessToken = 'expiring-access-token';
      connector.tokenExpiry = Date.now() + 240000;

      // Mock authenticate method
      connector.authenticate = jest.fn().mockImplementation(() => {
        connector.accessToken = 'new-access-token';
        connector.tokenExpiry = Date.now() + 3600000;
      });
      const headers = await connector.getAuthHeaders();
      expect(connector.authenticate).toHaveBeenCalled();
      expect(headers).toEqual({
        'Authorization': 'Bearer new-access-token'
      });
    });
  });
  describe('listContracts', () => {
    it('should make a GET request to the contracts endpoint', async () => {
      // Mock getAuthHeaders method
      connector.getAuthHeaders = jest.fn().mockResolvedValue({
        'Authorization': 'Bearer test-access-token'
      });

      // Mock axios get response
      const mockResponse = {
        data: {
          data: [{
            id: 'contract-1',
            title: 'Contract 1'
          }, {
            id: 'contract-2',
            title: 'Contract 2'
          }],
          pagination: {
            page: 1,
            limit: 20,
            totalItems: 2,
            totalPages: 1
          }
        }
      };
      axios.get.mockResolvedValue(mockResponse);
      const params = {
        status: 'active',
        limit: 50
      };
      const result = await connector.listContracts(params);
      expect(axios.get).toHaveBeenCalledWith(`${mockConfig.baseUrl}/contracts`, {
        params,
        headers: {
          'Authorization': 'Bearer test-access-token',
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      expect(result).toEqual(mockResponse.data);
    });
    it('should throw an error if the request fails', async () => {
      // Mock getAuthHeaders method
      connector.getAuthHeaders = jest.fn().mockResolvedValue({
        'Authorization': 'Bearer test-access-token'
      });

      // Mock axios get error
      const errorMessage = 'Request failed';
      axios.get.mockRejectedValue(new Error(errorMessage));
      await expect(connector.listContracts()).rejects.toThrow(`Error listing contracts: ${errorMessage}`);
    });
  });
  describe('getContract', () => {
    it('should make a GET request to the specific contract endpoint', async () => {
      // Mock getAuthHeaders method
      connector.getAuthHeaders = jest.fn().mockResolvedValue({
        'Authorization': 'Bearer test-access-token'
      });

      // Mock axios get response
      const mockResponse = {
        data: {
          id: 'contract-123',
          title: 'Test Contract',
          description: 'Test Description'
        }
      };
      axios.get.mockResolvedValue(mockResponse);
      const contractId = 'contract-123';
      const result = await connector.getContract(contractId);
      expect(axios.get).toHaveBeenCalledWith(`${mockConfig.baseUrl}/contracts/${contractId}`, {
        headers: {
          'Authorization': 'Bearer test-access-token',
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      expect(result).toEqual(mockResponse.data);
    });
    it('should throw an error if contractId is not provided', async () => {
      await expect(connector.getContract()).rejects.toThrow('Contract ID is required');
    });
  });
  describe('createContract', () => {
    it('should make a POST request to the contracts endpoint', async () => {
      // Mock getAuthHeaders method
      connector.getAuthHeaders = jest.fn().mockResolvedValue({
        'Authorization': 'Bearer test-access-token'
      });

      // Mock axios post response
      const mockResponse = {
        data: {
          id: 'contract-new',
          title: 'New Contract',
          status: 'draft'
        }
      };
      axios.post.mockResolvedValue(mockResponse);
      const contractData = {
        title: 'New Contract',
        type: 'service',
        parties: [{
          name: 'Party 1',
          role: 'client'
        }],
        startDate: '2023-01-01'
      };
      const result = await connector.createContract(contractData);
      expect(axios.post).toHaveBeenCalledWith(`${mockConfig.baseUrl}/contracts`, contractData, {
        headers: {
          'Authorization': 'Bearer test-access-token',
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      expect(result).toEqual(mockResponse.data);
    });
    it('should throw an error if required fields are missing', async () => {
      const invalidData = {
        title: 'New Contract'
        // Missing required fields: type, parties, startDate
      };
      await expect(connector.createContract(invalidData)).rejects.toThrow('type is required');
    });
  });
  describe('listPolicies', () => {
    it('should make a GET request to the policies endpoint', async () => {
      // Mock getAuthHeaders method
      connector.getAuthHeaders = jest.fn().mockResolvedValue({
        'Authorization': 'Bearer test-access-token'
      });

      // Mock axios get response
      const mockResponse = {
        data: {
          data: [{
            id: 'policy-1',
            title: 'Policy 1'
          }, {
            id: 'policy-2',
            title: 'Policy 2'
          }],
          pagination: {
            page: 1,
            limit: 20,
            totalItems: 2,
            totalPages: 1
          }
        }
      };
      axios.get.mockResolvedValue(mockResponse);
      const params = {
        status: 'active',
        limit: 50
      };
      const result = await connector.listPolicies(params);
      expect(axios.get).toHaveBeenCalledWith(`${mockConfig.baseUrl}/policies`, {
        params,
        headers: {
          'Authorization': 'Bearer test-access-token',
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      expect(result).toEqual(mockResponse.data);
    });
  });
  describe('getPolicy', () => {
    it('should make a GET request to the specific policy endpoint', async () => {
      // Mock getAuthHeaders method
      connector.getAuthHeaders = jest.fn().mockResolvedValue({
        'Authorization': 'Bearer test-access-token'
      });

      // Mock axios get response
      const mockResponse = {
        data: {
          id: 'policy-123',
          title: 'Test Policy',
          description: 'Test Description'
        }
      };
      axios.get.mockResolvedValue(mockResponse);
      const policyId = 'policy-123';
      const result = await connector.getPolicy(policyId);
      expect(axios.get).toHaveBeenCalledWith(`${mockConfig.baseUrl}/policies/${policyId}`, {
        headers: {
          'Authorization': 'Bearer test-access-token',
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      expect(result).toEqual(mockResponse.data);
    });
    it('should throw an error if policyId is not provided', async () => {
      await expect(connector.getPolicy()).rejects.toThrow('Policy ID is required');
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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