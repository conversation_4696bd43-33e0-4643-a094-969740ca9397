/**
 * Rate Limit Routes
 */

const express = require('express');
const router = express.Router();
const RateLimitController = require('../controllers/RateLimitController');
const { authenticate, hasRole } = require('../middleware/authMiddleware');

// All routes require authentication and admin role
router.use(authenticate);
router.use(hasRole('admin'));

// Get rate limits
router.get('/', (req, res, next) => {
  RateLimitController.getRateLimits(req, res, next);
});

// Update rate limits
router.put('/', (req, res, next) => {
  RateLimitController.updateRateLimits(req, res, next);
});

// Reset rate limits to defaults
router.post('/reset', (req, res, next) => {
  RateLimitController.resetRateLimits(req, res, next);
});

module.exports = router;
