/**
 * EmergencyOverrideComponent.js
 * 
 * This component demonstrates how to use the NovaVisionIntegration
 * to render the emergency override interface using NovaVision.
 */

import React, { useEffect, useState } from 'react';
import NovaVisionComponents from '../NovaVisionComponents';
import NovaVisionIntegration from './NovaVisionIntegration';
import NovaVisionRenderer from '@nova-ui/ui-components/NovaVisionRenderer';

/**
 * Emergency Override Component
 * @param {Object} props - Component props
 * @returns {React.ReactNode} - The rendered component
 */
function EmergencyOverrideComponent(props) {
  const [uiSchema, setUiSchema] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [overrideResult, setOverrideResult] = useState(null);
  
  useEffect(() => {
    try {
      // Initialize NovaVisionComponents
      const novaVisionComponents = new NovaVisionComponents({
        baseUrl: '/novadna',
        theme: 'emergency'
      });
      
      // Initialize NovaVisionIntegration
      const novaVisionIntegration = new NovaVisionIntegration({
        apiBaseUrl: '/api',
        novaVisionComponents
      });
      
      // Get the emergency override UI schema
      const schema = novaVisionIntegration.getEmergencyOverrideUI();
      
      // Set the UI schema
      setUiSchema(schema);
      setLoading(false);
    } catch (err) {
      console.error('Failed to initialize NovaVision integration:', err);
      setError(err.message);
      setLoading(false);
    }
  }, []);
  
  // Handle loading state
  if (loading) {
    return (
      <div className="p-4 bg-gray-100 rounded-lg">
        <div className="text-center">
          <h2 className="text-xl font-bold text-gray-800">Loading Emergency Override...</h2>
          <p className="mt-2 text-gray-600">Please wait while we initialize the emergency override interface.</p>
        </div>
      </div>
    );
  }
  
  // Handle error state
  if (error) {
    return (
      <div className="p-4 bg-red-50 rounded-lg">
        <div className="text-center">
          <h2 className="text-xl font-bold text-red-800">Error</h2>
          <p className="mt-2 text-red-600">{error}</p>
          <button
            className="mt-4 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
            onClick={() => window.location.reload()}
          >
            Retry
          </button>
        </div>
      </div>
    );
  }
  
  // Show override result if available
  if (overrideResult) {
    return (
      <div className="p-4 bg-green-50 rounded-lg">
        <div className="text-center">
          <h2 className="text-xl font-bold text-green-800">Emergency Override Successful</h2>
          <p className="mt-2 text-green-600">
            Emergency override has been initiated. You now have access to the medical profile.
          </p>
          <div className="mt-4 p-4 bg-white rounded-lg shadow">
            <h3 className="text-lg font-semibold">Override Details</h3>
            <div className="mt-2 grid grid-cols-2 gap-2 text-sm">
              <div className="text-gray-600">Override ID:</div>
              <div className="font-medium">{overrideResult.override.overrideId}</div>
              <div className="text-gray-600">Status:</div>
              <div className="font-medium">{overrideResult.override.status}</div>
              <div className="text-gray-600">Expires At:</div>
              <div className="font-medium">{new Date(overrideResult.override.expiresAt).toLocaleString()}</div>
            </div>
          </div>
          <button
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            onClick={() => props.onViewProfile(overrideResult.profile)}
          >
            View Medical Profile
          </button>
        </div>
      </div>
    );
  }
  
  // Render the NovaVision UI
  return (
    <div className="bg-white rounded-lg shadow-lg overflow-hidden">
      <NovaVisionRenderer
        schema={uiSchema}
        onAction={handleAction}
        onSubmit={handleSubmit}
        onError={handleError}
      />
    </div>
  );
  
  /**
   * Handle NovaVision actions
   * @param {String} actionId - The action ID
   * @param {Object} actionData - The action data
   */
  function handleAction(actionId, actionData) {
    console.log('Action:', actionId, actionData);
    
    // Handle specific actions
    switch (actionId) {
      case 'cancel':
        // Call onCancel prop if provided
        if (props.onCancel) {
          props.onCancel();
        }
        break;
      
      default:
        // Handle other actions
        break;
    }
  }
  
  /**
   * Handle form submissions
   * @param {String} formId - The form ID
   * @param {Object} formData - The form data
   */
  function handleSubmit(formId, formData) {
    console.log('Form submission:', formId, formData);
    
    // Make API call to the NovaDNA API
    fetch('/api/access/override', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      body: JSON.stringify(formData)
    })
      .then(response => response.json())
      .then(data => {
        if (data.status === 'success') {
          // Handle successful response
          setOverrideResult(data.data);
          
          // Call onSuccess prop if provided
          if (props.onSuccess) {
            props.onSuccess(data.data);
          }
        } else {
          // Handle error
          handleError(data.error);
        }
      })
      .catch(err => {
        // Handle fetch error
        handleError(err.message);
      });
  }
  
  /**
   * Handle errors
   * @param {String} errorMessage - The error message
   */
  function handleError(errorMessage) {
    console.error('Error:', errorMessage);
    
    // Show error message
    setError(errorMessage);
    
    // Call onError prop if provided
    if (props.onError) {
      props.onError(errorMessage);
    }
  }
}

export default EmergencyOverrideComponent;
