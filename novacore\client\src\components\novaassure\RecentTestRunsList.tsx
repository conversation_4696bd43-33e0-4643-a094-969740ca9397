/**
 * Recent Test Runs List Component
 * 
 * This component displays a list of recently executed test runs.
 */

import React from 'react';
import Link from 'next/link';
import { CheckCircle, XCircle, AlertCircle, Clock, Calendar } from 'lucide-react';
import { Button } from '../ui/button';

interface TestRun {
  _id: string;
  testId: string;
  testName: string;
  controlId: string;
  controlName: string;
  frameworkId: string;
  frameworkName: string;
  status: 'pending' | 'in_progress' | 'completed' | 'failed' | 'cancelled';
  result: 'pass' | 'fail' | 'inconclusive' | null;
  startTime: string;
  endTime: string | null;
  executedBy: string;
}

interface RecentTestRunsListProps {
  testRuns: TestRun[];
}

export const RecentTestRunsList: React.FC<RecentTestRunsListProps> = ({ testRuns }) => {
  // Format date to readable format
  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric'
    }) + ' ' + date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };
  
  // Get status icon based on result
  const getStatusIcon = (status: string, result: string | null) => {
    if (status === 'pending' || status === 'in_progress') {
      return <Clock className="h-4 w-4 text-blue-500" />;
    }
    
    if (status === 'cancelled') {
      return <AlertCircle className="h-4 w-4 text-gray-500" />;
    }
    
    if (result === 'pass') {
      return <CheckCircle className="h-4 w-4 text-green-500" />;
    }
    
    if (result === 'fail') {
      return <XCircle className="h-4 w-4 text-red-500" />;
    }
    
    return <AlertCircle className="h-4 w-4 text-yellow-500" />;
  };
  
  // Get status text based on status and result
  const getStatusText = (status: string, result: string | null): string => {
    if (status === 'pending') return 'Pending';
    if (status === 'in_progress') return 'In Progress';
    if (status === 'cancelled') return 'Cancelled';
    if (result === 'pass') return 'Passed';
    if (result === 'fail') return 'Failed';
    return 'Inconclusive';
  };
  
  // Get status color based on status and result
  const getStatusColor = (status: string, result: string | null): string => {
    if (status === 'pending' || status === 'in_progress') return 'text-blue-600';
    if (status === 'cancelled') return 'text-gray-600';
    if (result === 'pass') return 'text-green-600';
    if (result === 'fail') return 'text-red-600';
    return 'text-yellow-600';
  };
  
  // If no test runs, show empty state
  if (!testRuns || testRuns.length === 0) {
    return (
      <div className="h-48 flex items-center justify-center text-gray-500">
        No recent test runs available
      </div>
    );
  }
  
  return (
    <div className="space-y-4">
      {testRuns.map((testRun) => {
        const statusIcon = getStatusIcon(testRun.status, testRun.result);
        const statusText = getStatusText(testRun.status, testRun.result);
        const statusColor = getStatusColor(testRun.status, testRun.result);
        
        return (
          <div key={testRun._id} className="p-3 border rounded-md bg-gray-50">
            <div className="flex justify-between items-start">
              <div>
                <div className="font-medium">{testRun.testName}</div>
                <div className="text-sm text-gray-500">{testRun.frameworkName} - {testRun.controlName}</div>
              </div>
              <div className={`flex items-center text-sm font-medium ${statusColor}`}>
                {statusIcon}
                <span className="ml-1">{statusText}</span>
              </div>
            </div>
            
            <div className="mt-2 flex justify-between items-center">
              <div className="flex items-center text-xs text-gray-500">
                <Calendar className="h-3 w-3 mr-1" />
                {formatDate(testRun.startTime)}
              </div>
              
              <Link href={`/novaassure/test-runs/${testRun._id}`}>
                <Button variant="outline" size="sm" className="text-xs">
                  View Details
                </Button>
              </Link>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default RecentTestRunsList;
