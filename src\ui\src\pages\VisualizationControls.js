import React, { useState } from 'react';
import { <PERSON>, <PERSON>rid, <PERSON><PERSON><PERSON>, Button, <PERSON>alog, <PERSON>alogActions, <PERSON>alogContent, DialogTitle, TextField, MenuItem } from '@mui/material';
import { Add as AddIcon, Delete as DeleteIcon, Refresh as RefreshIcon } from '@mui/icons-material';
import ControlGroupRenderer from '../components/controls/ControlGroupRenderer';
import ActionButton from '../components/controls/ActionButton';
import { useControl } from '../contexts/ControlContext';

function VisualizationControls() {
  const { getControlValue, getControl, createVisualization, deleteVisualization } = useControl();
  const [openDialog, setOpenDialog] = useState(false);
  const [newVisualizationType, setNewVisualizationType] = useState('3d_tensor_visualization');
  const [newVisualizationTensorId, setNewVisualizationTensorId] = useState('example-tensor');

  const selectedVisualizationId = getControlValue('visualization-selector');
  const visualizationTypeControl = getControl('visualization-type');
  const visualizationTypeOptions = visualizationTypeControl ? visualizationTypeControl.options : [];

  const handleOpenDialog = () => {
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const handleCreateVisualization = async () => {
    try {
      // Create visualization
      await createVisualization(
        newVisualizationType,
        {
          tensor: {
            id: newVisualizationTensorId,
            values: [0.5, 0.6, 0.7, 0.8, 0.9],
            health: 1.0,
            entropyContainment: 0.0
          },
          dimensions: [5, 1, 1]
        },
        {
          renderMode: getControlValue('render-quality'),
          showAxes: getControlValue('show-axes'),
          showGrid: getControlValue('show-grid'),
          rotationSpeed: getControlValue('rotation-speed'),
          colorScheme: getControlValue('color-scheme')
        }
      );

      // Close dialog
      handleCloseDialog();

      // Reset form
      setNewVisualizationType('3d_tensor_visualization');
      setNewVisualizationTensorId('example-tensor');
    } catch (error) {
      console.error('Error creating visualization:', error);
    }
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">
          Visualization Controls
        </Typography>
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={handleOpenDialog}
        >
          Create New Visualization
        </Button>
      </Box>

      <Grid container spacing={3}>
        <Grid item xs={12} md={4}>
          {/* Visualization Selector */}
          <ControlGroupRenderer
            groupId="visualization-controls"
            title="Select Visualization"
          />

          {/* Visualization Options */}
          <ControlGroupRenderer
            groupId="visualization-options"
            title="Visualization Options"
          />

          <Box sx={{ mt: 2, display: 'flex', gap: 2 }}>
            <ActionButton
              action="update-visualization"
              params={{
                id: selectedVisualizationId,
                data: {
                  options: {
                    renderMode: getControlValue('render-quality'),
                    showAxes: getControlValue('show-axes'),
                    showGrid: getControlValue('show-grid'),
                    rotationSpeed: getControlValue('rotation-speed'),
                    colorScheme: getControlValue('color-scheme')
                  }
                }
              }}
              label="Update Visualization"
              icon={<RefreshIcon />}
              color="primary"
              sx={{ flex: 1 }}
            />

            <ActionButton
              action="delete-visualization"
              params={{ id: selectedVisualizationId }}
              label="Delete Visualization"
              icon={<DeleteIcon />}
              color="error"
              sx={{ flex: 1 }}
            />
          </Box>
        </Grid>

        <Grid item xs={12} md={8}>
          {/* Visualization Preview */}
          <Box
            sx={{
              height: 500,
              bgcolor: 'background.paper',
              borderRadius: 1,
              display: 'flex',
              flexDirection: 'column',
              p: 2,
            }}
          >
            <Typography variant="h6" gutterBottom>
              Visualization Preview
            </Typography>
            <Box
              sx={{
                width: '100%',
                height: '100%',
                bgcolor: 'background.default',
                borderRadius: 1,
              }}
            >
              {selectedVisualizationId ? (
                <VisualizationRenderer
                  visualizationType={getControlValue('visualization-type') || '3d_tensor_visualization'}
                  tensor={{
                    values: Array.from({ length: 25 }, (_, i) => Math.sin(i * 0.2) * 0.5 + 0.5),
                    health: 0.95,
                    entropyContainment: 0.02
                  }}
                  dimensions={[5, 5, 1]}
                  options={{
                    renderMode: getControlValue('render-quality') || 'medium',
                    showAxes: getControlValue('show-axes') !== undefined ? getControlValue('show-axes') : true,
                    showGrid: getControlValue('show-grid') !== undefined ? getControlValue('show-grid') : true,
                    rotationSpeed: getControlValue('rotation-speed') || 1,
                    colorScheme: getControlValue('color-scheme') || 'default'
                  }}
                  height="100%"
                />
              ) : (
                <Box
                  sx={{
                    height: '100%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                >
                  <Typography variant="body1" color="text.secondary">
                    Select a visualization to preview
                  </Typography>
                </Box>
              )}
            </Box>
          </Box>
        </Grid>
      </Grid>

      {/* Create Visualization Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog}>
        <DialogTitle>Create New Visualization</DialogTitle>
        <DialogContent>
          <TextField
            select
            margin="dense"
            id="visualization-type"
            label="Visualization Type"
            fullWidth
            variant="outlined"
            value={newVisualizationType}
            onChange={(e) => setNewVisualizationType(e.target.value)}
            sx={{ mb: 2 }}
          >
            {visualizationTypeOptions.map((option) => (
              <MenuItem key={option.value} value={option.value}>
                {option.label}
              </MenuItem>
            ))}
          </TextField>
          <TextField
            margin="dense"
            id="tensor-id"
            label="Tensor ID"
            type="text"
            fullWidth
            variant="outlined"
            value={newVisualizationTensorId}
            onChange={(e) => setNewVisualizationTensorId(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button onClick={handleCreateVisualization} variant="contained" color="primary">
            Create
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

export default VisualizationControls;
