# Test NovaFuse Google Cloud Integration
# This script demonstrates how to use the NovaFuse API with Google Cloud services

Write-Host "Testing NovaFuse Google Cloud Integration" -ForegroundColor Yellow

# Test Security Command Center integration
Write-Host "`nTesting Security Command Center Integration..." -ForegroundColor Green
Write-Host "Getting SCC findings:" -ForegroundColor Cyan
Invoke-RestMethod -Uri "http://localhost:3001/integrations/gcp/scc/findings" -Method Get | ConvertTo-Json

# Create a new finding in SCC
Write-Host "`nCreating a new finding in SCC:" -ForegroundColor Cyan
$newFinding = @{
    category = "VULNERABILITY"
    severity = "HIGH"
    resource = "projects/novafuse-testing/instances/vm-instance-2"
    description = "Critical vulnerability detected in VM instance"
    sourceProperties = @{
        compliance_standards = @("GDPR", "PCI-DSS")
        description = "Critical vulnerability detected in VM instance"
    }
}
Invoke-RestMethod -Uri "http://localhost:3001/integrations/gcp/scc/findings" -Method Post -Body ($newFinding | ConvertTo-Json) -ContentType "application/json" | ConvertTo-Json

# Test Cloud IAM integration
Write-Host "`nTesting Cloud IAM Integration..." -ForegroundColor Green
Write-Host "Getting IAM roles:" -ForegroundColor Cyan
try {
    Invoke-RestMethod -Uri "http://localhost:8082/v1/projects/novafuse-testing/roles" -Method Get | ConvertTo-Json
} catch {
    Write-Host "Error accessing IAM API: $($_.Exception.Message)" -ForegroundColor Red
}

# Test BigQuery integration
Write-Host "`nTesting BigQuery Integration..." -ForegroundColor Green
Write-Host "Running a query:" -ForegroundColor Cyan
$query = @{
    query = "SELECT * FROM compliance_data.findings LIMIT 10"
}
try {
    Invoke-RestMethod -Uri "http://localhost:8083/v2/projects/novafuse-testing/queries" -Method Post -Body ($query | ConvertTo-Json) -ContentType "application/json" | ConvertTo-Json
} catch {
    Write-Host "Error accessing BigQuery API: $($_.Exception.Message)" -ForegroundColor Red
}

# Test NovaConnect UAC with Google Cloud connectors
Write-Host "`nTesting NovaConnect UAC with Google Cloud connectors..." -ForegroundColor Green
Write-Host "Getting GCP connectors:" -ForegroundColor Cyan
Invoke-RestMethod -Uri "http://localhost:3002/integrations/gcp/connectors" -Method Get | ConvertTo-Json

Write-Host "`nGetting sample connectors:" -ForegroundColor Cyan
Invoke-RestMethod -Uri "http://localhost:3002/sample-connectors" -Method Get | ConvertTo-Json

Write-Host "`nGoogle Cloud Integration Test Complete" -ForegroundColor Green
