{"novacore": {"base_url": "/api/v1/novacore", "endpoints": {"test_control": "/test-control", "validate_configuration": "/validate-configuration", "run_integration_test": "/run-integration-test", "get_test_results": "/test-results", "get_test_report": "/test-report"}}, "novashield": {"base_url": "/api/v1/novashield", "endpoints": {"assess_vendor": "/assess-vendor", "monitor_vendor": "/monitor-vendor", "get_threat_intelligence": "/threat-intelligence", "get_risk_score": "/risk-score", "track_remediation": "/remediation"}}, "novatrack": {"base_url": "/api/v1/novatrack", "endpoints": {"create_requirement": "/requirements", "get_requirements": "/requirements", "update_requirement": "/requirements/{id}", "delete_requirement": "/requirements/{id}", "create_activity": "/activities", "get_activities": "/activities", "update_activity": "/activities/{id}", "delete_activity": "/activities/{id}", "get_compliance_score": "/compliance-score", "get_framework_coverage": "/framework-coverage", "get_requirement_status": "/requirement-status", "get_activity_status": "/activity-status", "get_predictive_insights": "/predictive-insights", "optimize_workflow": "/optimize-workflow", "map_controls": "/map-controls"}}, "novalearn": {"base_url": "/api/v1/novalearn", "endpoints": {"create_training": "/training", "get_training": "/training", "update_training": "/training/{id}", "delete_training": "/training/{id}", "assess_competency": "/assess-competency", "get_learning_path": "/learning-path", "track_progress": "/progress", "get_certification": "/certification"}}, "novaview": {"base_url": "/api/v1/novaview", "endpoints": {"get_dashboard": "/dashboard", "get_dashboard_schema": "/dashboard-schema", "get_dashboard_data": "/dashboard-data", "get_visualization": "/visualization", "get_compliance_status": "/compliance-status", "get_framework_status": "/framework-status", "get_requirement_status": "/requirement-status", "get_activity_status": "/activity-status"}}, "novaflowx": {"base_url": "/api/v1/novaflowx", "endpoints": {"create_workflow": "/workflows", "get_workflows": "/workflows", "update_workflow": "/workflows/{id}", "delete_workflow": "/workflows/{id}", "register_trigger": "/triggers", "register_action": "/actions", "trigger_event": "/events", "schedule_workflow": "/schedule", "get_workflow_status": "/workflow-status", "get_workflow_results": "/workflow-results", "optimize_process": "/optimize-process"}}, "novapulse": {"base_url": "/api/v1/novapulse", "endpoints": {"monitor_regulations": "/monitor-regulations", "analyze_impact": "/analyze-impact", "assess_gap": "/assess-gap", "plan_remediation": "/plan-remediation", "generate_report": "/generate-report"}}, "novaproof": {"base_url": "/api/v1/novaproof", "endpoints": {"collect_evidence": "/evidence", "verify_evidence": "/verify-evidence", "get_audit_trail": "/audit-trail", "search_evidence": "/search-evidence", "generate_report": "/generate-report"}}, "novathink": {"base_url": "/api/v1/novathink", "endpoints": {"get_schema": "/schema", "get_entity_types": "/entity-types", "get_relationship_types": "/relationship-types", "create_entity": "/entities/{type}", "get_entity": "/entities/{type}/{id}", "get_entities": "/entities/{type}", "update_entity": "/entities/{type}/{id}", "delete_entity": "/entities/{type}/{id}", "create_relationship": "/relationships/{type}", "get_entity_relationships": "/entities/{type}/{id}/relationships", "delete_relationship": "/relationships/{type}/{id}", "search": "/search", "get_frameworks": "/frameworks", "get_framework": "/frameworks/{id}", "get_framework_controls": "/frameworks/{id}/controls", "get_control": "/controls/{id}", "get_control_guidance": "/controls/{id}/guidance", "get_control_resources": "/controls/{id}/resources", "get_glossary_terms": "/glossary-terms", "get_faqs": "/faqs"}}, "novaconnect": {"base_url": "/api/v1/novaconnect", "endpoints": {"discover_api": "/discover", "map_schema": "/map-schema", "configure_auth": "/configure-auth", "configure_rate_limit": "/configure-rate-limit", "configure_retry": "/configure-retry", "execute_request": "/execute", "get_response": "/response", "handle_error": "/handle-error"}}, "novavision": {"base_url": "/api/v1/novavision", "endpoints": {"render_ui": "/render", "get_ui_schema": "/ui-schema", "adapt_ui": "/adapt-ui", "generate_screen": "/generate-screen", "get_responsive_design": "/responsive-design"}}, "novadna": {"base_url": "/api/v1/novadna", "endpoints": {"verify_identity": "/verify-identity", "analyze_biometrics": "/analyze-biometrics", "profile_risk": "/profile-risk", "authenticate": "/authenticate", "visualize_identity_graph": "/identity-graph"}}, "novastore": {"base_url": "/api/v1/novastore", "endpoints": {"browse_apis": "/browse", "get_api_details": "/api-details", "certify_component": "/certify", "get_integration_template": "/integration-template", "get_documentation": "/documentation", "configure_feature_flag": "/feature-flag"}}}