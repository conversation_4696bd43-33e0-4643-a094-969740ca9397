version: '3.8'

services:
  # NovaConnect API
  nova-connect:
    build:
      context: ../../
      dockerfile: Dockerfile
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=test
      - PORT=3001
      - MONGODB_URI=mongodb://mongo:27017/nova-connect-test
      - REDIS_URI=redis://redis:6379
      - JWT_SECRET=test-jwt-secret
      - METRICS_ENABLED=true
      - TRACING_ENABLED=true
      - TRACING_EXPORTER=console
    volumes:
      - ../../:/app
      - /app/node_modules
    depends_on:
      - mongo
      - redis
    networks:
      - nova-test-network
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 2G

  # MongoDB
  mongo:
    image: mongo:4.4
    ports:
      - "27017:27017"
    volumes:
      - mongo-test-data:/data/db
    networks:
      - nova-test-network
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 1G

  # Redis
  redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis-test-data:/data
    networks:
      - nova-test-network
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M

  # k6 Load Testing
  k6:
    image: loadimpact/k6:latest
    volumes:
      - ./:/tests
      - ./results:/results
    environment:
      - K6_OUT=json=/results/k6-results.json
      - API_BASE_URL=http://nova-connect:3001
      - API_KEY=test-api-key
    networks:
      - nova-test-network
    depends_on:
      - nova-connect
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 2G

volumes:
  mongo-test-data:
  redis-test-data:

networks:
  nova-test-network:
    driver: bridge
