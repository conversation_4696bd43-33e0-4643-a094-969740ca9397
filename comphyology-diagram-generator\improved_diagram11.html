<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>11. Cross-Module Data Processing Pipeline</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: white;
        }
        .diagram-container {
            width: 1000px;
            height: 800px;
            position: relative;
            border: 2px solid black;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
        }
        .element {
            position: absolute;
            border-radius: 0; /* Square corners for patent diagrams */
            padding: 20px 20px 20px 50px; /* Extra padding on left for number */
            z-index: 2;
            background-color: white;
            border: 2px solid black;
        }
        .element-number {
            position: absolute;
            top: 50%;
            left: 15px;
            transform: translateY(-50%);
            background-color: black;
            color: white;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: bold;
            z-index: 3;
        }
        .connection {
            position: absolute;
            z-index: 1;
            background-color: black;
        }
        h1 {
            text-align: center;
            margin-bottom: 20px;
            font-size: 24px;
        }
        .bold-formula {
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>11. Cross-Module Data Processing Pipeline</h1>
    
    <div class="diagram-container">
        <!-- Data Processing -->
        <div class="element" style="top: 50px; left: 300px; width: 400px; font-weight: bold; font-size: 20px;">
            Cross-Module Data Processing Pipeline
            <div class="element-number">1</div>
        </div>
        
        <!-- Data Sources -->
        <div class="element" style="top: 150px; left: 300px; width: 400px; font-weight: bold; font-size: 16px;">
            External Data Sources
            <div class="element-number">2</div>
        </div>
        
        <div class="element" style="top: 250px; left: 100px; width: 200px; font-size: 14px;">
            Structured Data<br>(databases, APIs, CSV)
            <div class="element-number">3</div>
        </div>
        
        <div class="element" style="top: 250px; left: 325px; width: 200px; font-size: 14px;">
            Unstructured Data<br>(documents, emails, logs)
            <div class="element-number">4</div>
        </div>
        
        <div class="element" style="top: 250px; left: 550px; width: 200px; font-size: 14px;">
            Semi-structured Data<br>(JSON, XML, YAML)
            <div class="element-number">5</div>
        </div>
        
        <div class="element" style="top: 250px; left: 775px; width: 200px; font-size: 14px;">
            Real-time Streams<br>(events, telemetry)
            <div class="element-number">6</div>
        </div>
        
        <!-- Data Ingestion -->
        <div class="element" style="top: 350px; left: 300px; width: 400px; font-weight: bold; font-size: 16px;">
            Data Ingestion (NovaConnect)
            <div class="element-number">7</div>
        </div>
        
        <div class="element" style="top: 450px; left: 250px; width: 200px; font-size: 14px;">
            Universal API Connectors
            <div class="element-number">8</div>
        </div>
        
        <div class="element" style="top: 450px; left: 550px; width: 200px; font-size: 14px;">
            Data Format Adapters
            <div class="element-number">9</div>
        </div>
        
        <!-- Data Normalization -->
        <div class="element" style="top: 550px; left: 300px; width: 400px; font-weight: bold; font-size: 16px;">
            Data Normalization (NovaCore)
            <div class="element-number">10</div>
        </div>
        
        <div class="element" style="top: 650px; left: 100px; width: 200px; font-size: 14px;">
            Tensor Product <span class="bold-formula">(⊗)</span><br>Multi-dimensional relationships
            <div class="element-number">11</div>
        </div>
        
        <div class="element" style="top: 650px; left: 400px; width: 200px; font-size: 14px;">
            Fusion Operator <span class="bold-formula">(⊕)</span><br>Merges related data points
            <div class="element-number">12</div>
        </div>
        
        <div class="element" style="top: 650px; left: 700px; width: 200px; font-size: 14px;">
            <span class="bold-formula">π10³</span> Factor<br>Consistent processing
            <div class="element-number">13</div>
        </div>
        
        <!-- Data Quality Assessment -->
        <div class="element" style="top: 750px; left: 300px; width: 400px; font-weight: bold; font-size: 16px;">
            Data Quality Assessment (NovaTrack)
            <div class="element-number">14</div>
        </div>
        
        <!-- Connections -->
        <!-- Connect Data Processing to Data Sources -->
        <div class="connection" style="top: 100px; left: 500px; width: 2px; height: 50px;"></div>
        
        <!-- Connect Data Sources to specific sources -->
        <div class="connection" style="top: 200px; left: 200px; width: 2px; height: 50px;"></div>
        <div class="connection" style="top: 200px; left: 200px; width: 100px; height: 2px;"></div>
        
        <div class="connection" style="top: 200px; left: 425px; width: 2px; height: 50px;"></div>
        <div class="connection" style="top: 200px; left: 425px; width: 125px; height: 2px;"></div>
        
        <div class="connection" style="top: 200px; left: 650px; width: 2px; height: 50px;"></div>
        <div class="connection" style="top: 200px; left: 650px; width: 150px; height: 2px;"></div>
        
        <div class="connection" style="top: 200px; left: 875px; width: 2px; height: 50px;"></div>
        <div class="connection" style="top: 200px; left: 700px; width: 175px; height: 2px;"></div>
        
        <!-- Connect sources to Data Ingestion -->
        <div class="connection" style="top: 300px; left: 200px; width: 2px; height: 50px;"></div>
        <div class="connection" style="top: 350px; left: 200px; width: 100px; height: 2px;"></div>
        
        <div class="connection" style="top: 300px; left: 425px; width: 2px; height: 50px;"></div>
        
        <div class="connection" style="top: 300px; left: 650px; width: 2px; height: 50px;"></div>
        <div class="connection" style="top: 350px; left: 650px; width: 50px; height: 2px;"></div>
        
        <div class="connection" style="top: 300px; left: 875px; width: 2px; height: 50px;"></div>
        <div class="connection" style="top: 350px; left: 700px; width: 175px; height: 2px;"></div>
        
        <!-- Connect Data Ingestion to components -->
        <div class="connection" style="top: 400px; left: 350px; width: 2px; height: 50px;"></div>
        <div class="connection" style="top: 400px; left: 350px; width: 100px; height: 2px;"></div>
        
        <div class="connection" style="top: 400px; left: 650px; width: 2px; height: 50px;"></div>
        <div class="connection" style="top: 400px; left: 650px; width: 100px; height: 2px;"></div>
        
        <!-- Connect Data Ingestion to Data Normalization -->
        <div class="connection" style="top: 500px; left: 500px; width: 2px; height: 50px;"></div>
        
        <!-- Connect Data Normalization to components -->
        <div class="connection" style="top: 600px; left: 200px; width: 2px; height: 50px;"></div>
        <div class="connection" style="top: 600px; left: 200px; width: 100px; height: 2px;"></div>
        
        <div class="connection" style="top: 600px; left: 500px; width: 2px; height: 50px;"></div>
        <div class="connection" style="top: 600px; left: 500px; width: 100px; height: 2px;"></div>
        
        <div class="connection" style="top: 600px; left: 800px; width: 2px; height: 50px;"></div>
        <div class="connection" style="top: 600px; left: 700px; width: 100px; height: 2px;"></div>
        
        <!-- Connect Data Normalization to Data Quality Assessment -->
        <div class="connection" style="top: 700px; left: 500px; width: 2px; height: 50px;"></div>
    </div>
</body>
</html>
