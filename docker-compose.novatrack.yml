version: '3.8'

services:
  # MongoDB for NovaTrack data
  mongodb:
    image: mongo:latest
    volumes:
      - mongodb_data:/data/db
    ports:
      - "27017:27017"
    healthcheck:
      test: ["C<PERSON>", "mongo", "--eval", "db.adminCommand('ping')"]
      interval: 10s
      timeout: 5s
      retries: 3
    networks:
      - novatrack-test-network

  # NovaTrack Unit Tests
  novatrack-unit-tests:
    build:
      context: .
      dockerfile: Dockerfile.test
    volumes:
      - ./:/app
      - ./test-results:/app/test-results
      - ./coverage:/app/coverage
    environment:
      - NODE_ENV=test
      - TEST_ENV=docker
      - MONGODB_URI=mongodb://mongodb:27017/novatrack-test
    depends_on:
      mongodb:
        condition: service_healthy
    command: npm run test:novatrack
    networks:
      - novatrack-test-network

  # NovaTrack Integration Tests
  novatrack-integration-tests:
    build:
      context: .
      dockerfile: Dockerfile.test
    volumes:
      - ./:/app
      - ./test-results:/app/test-results
      - ./coverage:/app/coverage
    environment:
      - NODE_ENV=test
      - TEST_ENV=docker
      - MONGODB_URI=mongodb://mongodb:27017/novatrack-test
    ports:
      - "3002:3002"
    depends_on:
      mongodb:
        condition: service_healthy
    command: npm run test:novatrack:integration
    networks:
      - novatrack-test-network

  # NovaTrack Coverage Tests
  novatrack-coverage-tests:
    build:
      context: .
      dockerfile: Dockerfile.test
    volumes:
      - ./:/app
      - ./test-results:/app/test-results
      - ./coverage:/app/coverage
    environment:
      - NODE_ENV=test
      - TEST_ENV=docker
      - MONGODB_URI=mongodb://mongodb:27017/novatrack-test
    depends_on:
      mongodb:
        condition: service_healthy
    command: npm run test:coverage:novatrack
    networks:
      - novatrack-test-network

networks:
  novatrack-test-network:
    driver: bridge

volumes:
  mongodb_data:
