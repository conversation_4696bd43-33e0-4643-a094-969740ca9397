/**
 * Transaction Manager for NovaRollups
 * 
 * This module handles the management of individual transactions within NovaRollups,
 * providing transaction tracking, queuing, and status management.
 */

const { v4: uuidv4 } = require('uuid');

/**
 * Transaction Manager
 * @class TransactionManager
 */
class TransactionManager {
  /**
   * Create a new TransactionManager
   * @param {Object} options - Configuration options
   * @param {number} [options.maxQueueSize=100000] - Maximum queue size
   * @param {number} [options.transactionTTL=86400000] - Transaction time-to-live in ms (default: 24h)
   */
  constructor(options = {}) {
    this.options = {
      maxQueueSize: 100000,
      transactionTTL: 86400000, // 24 hours
      ...options
    };
    
    // Initialize transaction storage
    this.transactions = new Map();
    this.transactionQueue = [];
    this.batchMap = new Map(); // Map of batchId -> transactionIds
    
    // Initialize cleanup interval
    this._initCleanupInterval();
  }
  
  /**
   * Initialize cleanup interval for expired transactions
   * @private
   */
  _initCleanupInterval() {
    // Clean up expired transactions every hour
    setInterval(() => {
      this._cleanupExpiredTransactions();
    }, 3600000); // 1 hour
  }
  
  /**
   * Clean up expired transactions
   * @private
   */
  _cleanupExpiredTransactions() {
    const now = Date.now();
    const expiredIds = [];
    
    // Find expired transactions
    for (const [id, transaction] of this.transactions.entries()) {
      if (now - transaction.timestamp > this.options.transactionTTL) {
        expiredIds.push(id);
      }
    }
    
    // Remove expired transactions
    for (const id of expiredIds) {
      this.transactions.delete(id);
    }
    
    // Clean up transaction queue
    this.transactionQueue = this.transactionQueue.filter(id => !expiredIds.includes(id));
    
    // Clean up batch map
    for (const [batchId, transactionIds] of this.batchMap.entries()) {
      const updatedIds = transactionIds.filter(id => !expiredIds.includes(id));
      
      if (updatedIds.length === 0) {
        // If no transactions left in batch, remove batch
        this.batchMap.delete(batchId);
      } else if (updatedIds.length !== transactionIds.length) {
        // Update batch with remaining transactions
        this.batchMap.set(batchId, updatedIds);
      }
    }
  }
  
  /**
   * Add a transaction to the manager
   * @param {Object} transaction - Transaction to add
   * @param {string} [batchId] - Optional batch ID
   * @returns {Promise<Object>} - Added transaction with ID
   */
  async addTransaction(transaction, batchId = null) {
    // Check queue size
    if (this.transactionQueue.length >= this.options.maxQueueSize) {
      throw new Error(`Transaction queue full (max: ${this.options.maxQueueSize})`);
    }
    
    // Generate transaction ID if not provided
    const transactionId = transaction.id || uuidv4();
    
    // Create transaction object
    const transactionObj = {
      ...transaction,
      id: transactionId,
      timestamp: transaction.timestamp || Date.now(),
      status: transaction.status || 'pending'
    };
    
    // Store transaction
    this.transactions.set(transactionId, transactionObj);
    
    // Add to queue
    this.transactionQueue.push(transactionId);
    
    // Add to batch if provided
    if (batchId) {
      this._addTransactionToBatch(transactionId, batchId);
    }
    
    return transactionObj;
  }
  
  /**
   * Add a transaction to a batch
   * @param {string} transactionId - Transaction ID
   * @param {string} batchId - Batch ID
   * @private
   */
  _addTransactionToBatch(transactionId, batchId) {
    // Get current batch transactions
    const batchTransactions = this.batchMap.get(batchId) || [];
    
    // Add transaction to batch
    batchTransactions.push(transactionId);
    
    // Update batch map
    this.batchMap.set(batchId, batchTransactions);
    
    // Update transaction with batch ID
    const transaction = this.transactions.get(transactionId);
    if (transaction) {
      transaction.batchId = batchId;
      this.transactions.set(transactionId, transaction);
    }
  }
  
  /**
   * Get a transaction by ID
   * @param {string} transactionId - Transaction ID
   * @returns {Promise<Object>} - Transaction
   */
  async getTransaction(transactionId) {
    const transaction = this.transactions.get(transactionId);
    
    if (!transaction) {
      throw new Error(`Transaction not found: ${transactionId}`);
    }
    
    return { ...transaction };
  }
  
  /**
   * Update a transaction
   * @param {string} transactionId - Transaction ID
   * @param {Object} updates - Updates to apply
   * @returns {Promise<Object>} - Updated transaction
   */
  async updateTransaction(transactionId, updates) {
    const transaction = this.transactions.get(transactionId);
    
    if (!transaction) {
      throw new Error(`Transaction not found: ${transactionId}`);
    }
    
    // Apply updates
    const updatedTransaction = {
      ...transaction,
      ...updates,
      id: transactionId, // Ensure ID doesn't change
      updatedAt: Date.now()
    };
    
    // Store updated transaction
    this.transactions.set(transactionId, updatedTransaction);
    
    return { ...updatedTransaction };
  }
  
  /**
   * Get transactions by batch ID
   * @param {string} batchId - Batch ID
   * @returns {Promise<Array>} - Batch transactions
   */
  async getTransactionsByBatch(batchId) {
    const transactionIds = this.batchMap.get(batchId) || [];
    const transactions = [];
    
    for (const id of transactionIds) {
      const transaction = this.transactions.get(id);
      if (transaction) {
        transactions.push({ ...transaction });
      }
    }
    
    return transactions;
  }
  
  /**
   * Get next transactions from queue
   * @param {number} count - Number of transactions to get
   * @returns {Promise<Array>} - Transactions
   */
  async getNextTransactions(count) {
    const transactions = [];
    const ids = this.transactionQueue.slice(0, count);
    
    for (const id of ids) {
      const transaction = this.transactions.get(id);
      if (transaction) {
        transactions.push({ ...transaction });
      }
    }
    
    return transactions;
  }
  
  /**
   * Remove transactions from queue
   * @param {Array} transactionIds - Transaction IDs to remove
   * @returns {Promise<number>} - Number of transactions removed
   */
  async removeFromQueue(transactionIds) {
    const initialLength = this.transactionQueue.length;
    
    // Remove from queue
    this.transactionQueue = this.transactionQueue.filter(id => !transactionIds.includes(id));
    
    return initialLength - this.transactionQueue.length;
  }
  
  /**
   * Get transaction status
   * @param {string} transactionId - Transaction ID
   * @returns {Promise<Object>} - Transaction status
   */
  async getTransactionStatus(transactionId) {
    const transaction = this.transactions.get(transactionId);
    
    if (!transaction) {
      throw new Error(`Transaction not found: ${transactionId}`);
    }
    
    return {
      id: transactionId,
      status: transaction.status,
      batchId: transaction.batchId,
      timestamp: transaction.timestamp,
      updatedAt: transaction.updatedAt
    };
  }
  
  /**
   * Update transaction status
   * @param {string} transactionId - Transaction ID
   * @param {string} status - New status
   * @returns {Promise<Object>} - Updated transaction status
   */
  async updateTransactionStatus(transactionId, status) {
    return this.updateTransaction(transactionId, { status });
  }
  
  /**
   * Get queue statistics
   * @returns {Promise<Object>} - Queue statistics
   */
  async getQueueStats() {
    return {
      queueSize: this.transactionQueue.length,
      maxQueueSize: this.options.maxQueueSize,
      totalTransactions: this.transactions.size,
      totalBatches: this.batchMap.size
    };
  }
}

module.exports = TransactionManager;
