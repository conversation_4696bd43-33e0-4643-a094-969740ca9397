version: '3'

services:
  # Kong Database
  kong-database:
    image: postgres:13
    container_name: novafuse-kong-database
    environment:
      POSTGRES_USER: kong
      POSTGRES_DB: kong
      POSTGRES_PASSWORD: kong
    ports:
      - "5433:5432"  # Changed from 5432:5432 to avoid conflict
    volumes:
      - postgres-data:/var/lib/postgresql/data
    networks:
      - novafuse-net
    healthcheck:
      test: ["CMD", "pg_isready", "-U", "kong"]
      interval: 5s
      timeout: 5s
      retries: 5

  # Kong Migrations
  kong-migrations:
    image: kong:latest
    container_name: novafuse-kong-migrations
    depends_on:
      - kong-database
    environment:
      KONG_DATABASE: postgres
      KONG_PG_HOST: kong-database
      KONG_PG_USER: kong
      KONG_PG_PASSWORD: kong
    command: kong migrations bootstrap
    networks:
      - novafuse-net
    restart: on-failure

  # Kong API Gateway
  kong:
    image: kong:latest
    container_name: novafuse-kong
    depends_on:
      - kong-database
      - kong-migrations
    environment:
      KONG_DATABASE: postgres
      KONG_PG_HOST: kong-database
      KONG_PG_USER: kong
      KONG_PG_PASSWORD: kong
      KONG_PROXY_ACCESS_LOG: /dev/stdout
      KONG_ADMIN_ACCESS_LOG: /dev/stdout
      KONG_PROXY_ERROR_LOG: /dev/stderr
      KONG_ADMIN_ERROR_LOG: /dev/stderr
      KONG_ADMIN_LISTEN: 0.0.0.0:8001
      KONG_ADMIN_GUI_URL: http://localhost:8002
    volumes:
      - ./config:/etc/kong/config:rw
      - ./plugins:/usr/local/share/lua/5.1/kong/plugins/novafuse:rw
    ports:
      - "8000:8000"  # Kong Proxy
      - "8001:8001"  # Kong Admin API
      - "8443:8443"  # Kong Proxy SSL
      - "8444:8444"  # Kong Admin API SSL
    networks:
      - novafuse-net
    healthcheck:
      test: ["CMD", "kong", "health"]
      interval: 10s
      timeout: 10s
      retries: 10

  # Konga - Kong Admin UI
  konga:
    image: pantsel/konga:latest
    container_name: novafuse-konga
    depends_on:
      - kong
    environment:
      NODE_ENV: production
      DB_ADAPTER: postgres
      DB_HOST: kong-database
      DB_PORT: 5432
      DB_USER: kong
      DB_PASSWORD: kong
      DB_DATABASE: kong
    ports:
      - "1337:1337"
    networks:
      - novafuse-net

  # Documentation Portal
  docs-portal:
    image: swaggerapi/swagger-ui
    container_name: novafuse-docs
    environment:
      SWAGGER_JSON: /api-specs/openapi.json
    volumes:
      - ./api-specs:/api-specs
    ports:
      - "8889:8080"  # Changed from 8080:8080 to avoid conflict
    networks:
      - novafuse-net

  # Governance API
  governance-api:
    build: ./mock-apis/governance
    container_name: novafuse-governance-api
    ports:
      - "3001:3000"
    networks:
      - novafuse-net
    restart: unless-stopped

  # Security API
  security-api:
    build: ./mock-apis/security
    container_name: novafuse-security-api
    ports:
      - "3002:3000"
    networks:
      - novafuse-net
    restart: unless-stopped

  # APIs API
  apis-api:
    build: ./mock-apis/apis
    container_name: novafuse-apis-api
    ports:
      - "3003:3000"
    networks:
      - novafuse-net
    restart: unless-stopped

  # Marketplace UI
  marketplace-ui:
    build: ./marketplace-ui
    container_name: novafuse-marketplace
    ports:
      - "3000:3000"
    networks:
      - novafuse-net
    restart: unless-stopped

  # AI Alignment Demo
  ai-alignment-demo:
    build: ./ai-alignment-demo
    container_name: novafuse-ai-alignment
    ports:
      - "3004:3000"
    networks:
      - novafuse-net
    restart: unless-stopped
    environment:
      - NODE_ENV=production

  # Test Service
  test:
    build:
      context: .
      dockerfile: Dockerfile.test
    networks:
      - novafuse-net

networks:
  novafuse-net:
    driver: bridge

volumes:
  postgres-data:
