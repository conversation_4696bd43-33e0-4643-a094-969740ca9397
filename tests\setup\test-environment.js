/**
 * Test environment configuration
 * Detects whether tests are running locally or in Docker
 */

const isDocker = process.env.TEST_ENV === 'docker';

// Configure paths based on environment
const testDataPath = isDocker ? '/app/test-data' : './test-data';
const testResultsPath = isDocker ? '/app/test-results' : './test-results';
const coveragePath = isDocker ? '/app/coverage' : './coverage';

// Configure service URLs based on environment
const serviceUrls = {
  novaConnect: isDocker ? 'http://nova-connect:3001' : 'http://localhost:3001',
  novaTrack: isDocker ? 'http://nova-track:3002' : 'http://localhost:3002',
  novaProof: isDocker ? 'http://nova-proof:3003' : 'http://localhost:3003',
};

// Configure database connections based on environment
const dbConnections = {
  mongodb: isDocker ? 'mongodb://mongodb:27017/novatrack-test' : 'mongodb://localhost:27017/novatrack-test',
  redis: isDocker ? 'redis://redis:6379' : 'redis://localhost:6379',
};

module.exports = {
  isDocker,
  testDataPath,
  testResultsPath,
  coveragePath,
  serviceUrls,
  dbConnections,
};
