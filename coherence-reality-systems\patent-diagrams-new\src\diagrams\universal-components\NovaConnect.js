import React from 'react';
import {
  DiagramFrame,
  ComponentBox,
  ComponentLabel,
  ComponentNumber,
  Arrow,
  ContainerBox,
  ContainerLabel,
  CurvedArrow,
  DiagramLegend,
  LegendItem,
  LegendColor,
  LegendText
} from '../../components/DiagramComponents';

const NovaConnect = () => {
  return (
    <DiagramFrame>
      <ContainerBox width="750px" height="400px" left="25px" top="20px">
        <ContainerLabel>NOVACONNECT - UNIVERSAL API CONNECTOR</ContainerLabel>
      </ContainerBox>
      
      {/* Core Architecture */}
      <ContainerBox width="700px" height="350px" left="50px" top="70px">
        <ContainerLabel>UNIVERSAL API CONNECTOR ARCHITECTURE</ContainerLabel>
      </ContainerBox>
      
      {/* Central API Bridge */}
      <ContainerBox width="200px" height="200px" left="300px" top="120px" style={{ borderRadius: '50%' }}>
        <ContainerLabel style={{ top: '90px' }}>API BRIDGE</ContainerLabel>
      </ContainerBox>
      
      <ComponentBox left="340px" top="160px" width="120px" height="40px">
        <ComponentNumber>601</ComponentNumber>
        <ComponentLabel>Protocol-Agnostic</ComponentLabel>
        Adapter
      </ComponentBox>
      
      <ComponentBox left="340px" top="210px" width="120px" height="40px">
        <ComponentNumber>602</ComponentNumber>
        <ComponentLabel>Schema</ComponentLabel>
        Mapper
      </ComponentBox>
      
      {/* Left Side - Source Systems */}
      <ContainerBox width="150px" height="280px" left="100px" top="120px">
        <ContainerLabel>SOURCE SYSTEMS</ContainerLabel>
      </ContainerBox>
      
      <ComponentBox left="115px" top="160px" width="120px" height="40px">
        <ComponentNumber>603</ComponentNumber>
        <ComponentLabel>REST</ComponentLabel>
        APIs
      </ComponentBox>
      
      <ComponentBox left="115px" top="210px" width="120px" height="40px">
        <ComponentNumber>604</ComponentNumber>
        <ComponentLabel>SOAP</ComponentLabel>
        Services
      </ComponentBox>
      
      <ComponentBox left="115px" top="260px" width="120px" height="40px">
        <ComponentNumber>605</ComponentNumber>
        <ComponentLabel>GraphQL</ComponentLabel>
        Endpoints
      </ComponentBox>
      
      <ComponentBox left="115px" top="310px" width="120px" height="40px">
        <ComponentNumber>606</ComponentNumber>
        <ComponentLabel>Legacy</ComponentLabel>
        Systems
      </ComponentBox>
      
      {/* Right Side - Target Systems */}
      <ContainerBox width="150px" height="280px" left="550px" top="120px">
        <ContainerLabel>TARGET SYSTEMS</ContainerLabel>
      </ContainerBox>
      
      <ComponentBox left="565px" top="160px" width="120px" height="40px">
        <ComponentNumber>607</ComponentNumber>
        <ComponentLabel>Compliance</ComponentLabel>
        Systems
      </ComponentBox>
      
      <ComponentBox left="565px" top="210px" width="120px" height="40px">
        <ComponentNumber>608</ComponentNumber>
        <ComponentLabel>Security</ComponentLabel>
        Platforms
      </ComponentBox>
      
      <ComponentBox left="565px" top="260px" width="120px" height="40px">
        <ComponentNumber>609</ComponentNumber>
        <ComponentLabel>Risk</ComponentLabel>
        Management
      </ComponentBox>
      
      <ComponentBox left="565px" top="310px" width="120px" height="40px">
        <ComponentNumber>610</ComponentNumber>
        <ComponentLabel>Governance</ComponentLabel>
        Tools
      </ComponentBox>
      
      {/* Connecting Arrows - Left Side */}
      <Arrow left="235px" top="180px" width="65px" />
      <Arrow left="235px" top="230px" width="65px" />
      <Arrow left="235px" top="280px" width="65px" />
      <Arrow left="235px" top="330px" width="65px" />
      
      {/* Connecting Arrows - Right Side */}
      <Arrow left="500px" top="180px" width="65px" />
      <Arrow left="500px" top="230px" width="65px" />
      <Arrow left="500px" top="280px" width="65px" />
      <Arrow left="500px" top="330px" width="65px" />
      
      {/* Bottom Layer - Core Features */}
      <ContainerBox width="650px" height="70px" left="75px" top="410px">
        <ContainerLabel>CORE FEATURES</ContainerLabel>
      </ContainerBox>
      
      <ComponentBox left="100px" top="430px" width="120px" height="40px">
        <ComponentNumber>611</ComponentNumber>
        <ComponentLabel>Zero-Trust</ComponentLabel>
        Bidirectional Control
      </ComponentBox>
      
      <ComponentBox left="250px" top="430px" width="120px" height="40px">
        <ComponentNumber>612</ComponentNumber>
        <ComponentLabel>ML-Based</ComponentLabel>
        Schema Mapping
      </ComponentBox>
      
      <ComponentBox left="400px" top="430px" width="120px" height="40px">
        <ComponentNumber>613</ComponentNumber>
        <ComponentLabel>Regulatory</ComponentLabel>
        Compliance Layer
      </ComponentBox>
      
      <ComponentBox left="550px" top="430px" width="120px" height="40px">
        <ComponentNumber>614</ComponentNumber>
        <ComponentLabel>Real-Time</ComponentLabel>
        Transformation
      </ComponentBox>
      
      {/* Legend */}
      <DiagramLegend>
        <LegendItem>
          <LegendColor color="#fff" />
          <LegendText>API Bridge</LegendText>
        </LegendItem>
        <LegendItem>
          <LegendColor color="#fff" />
          <LegendText>Source Systems</LegendText>
        </LegendItem>
        <LegendItem>
          <LegendColor color="#fff" />
          <LegendText>Target Systems</LegendText>
        </LegendItem>
        <LegendItem>
          <LegendColor color="#fff" />
          <LegendText>Core Features</LegendText>
        </LegendItem>
      </DiagramLegend>
    </DiagramFrame>
  );
};

export default NovaConnect;
