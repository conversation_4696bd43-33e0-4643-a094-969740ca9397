<!DOCTYPE html>
<html>
<head>
    <title>Mermaid Diagram Viewer</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@8.14.0/dist/mermaid.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .diagram-container { margin: 20px 0; border: 1px solid #ccc; padding: 20px; }
        select { padding: 8px; margin: 10px 0; width: 100%; }
        button { padding: 8px 16px; margin: 5px; }
        #diagram { min-height: 300px; text-align: center; }
    </style>
</head>
<body>
    <h1>Mermaid Diagram Viewer</h1>
    
    <div>
        <select id="diagramSelector" onchange="loadDiagram()">
            <option value="">-- Select a Diagram --</option>
        </select>
    </div>
    
    <div class="diagram-container">
        <div id="diagram">
            <p>Select a diagram from the dropdown above</p>
        </div>
    </div>

    <script>
        // Initialize Mermaid
        mermaid.initialize({
            startOnLoad: false,
            theme: 'default',
            securityLevel: 'loose'
        });

        // List of available diagrams
        const diagrams = [
            'alignment_architecture.mmd',
            'consciousness_threshold.mmd',
            'cross_module_data_processing_pipeline.mmd',
            'dark_field_classification.mmd',
            'finite_universe_paradigm_visualization.mmd',
            'finite_universe_principle.mmd',
            'healthcare_implementation.mmd',
            'nova_components.mmd',
            'principle_18_82.mmd',
            'protein_folding.mmd',
            'three_body_problem_reframing.mmd',
            'uuft_core_architecture.mmd'
        ];

        // Populate the dropdown
        const selector = document.getElementById('diagramSelector');
        diagrams.forEach(diagram => {
            const option = document.createElement('option');
            option.value = `mermaid_diagrams/${diagram}`;
            option.textContent = diagram;
            selector.appendChild(option);
        });

        // Load and render the selected diagram
        async function loadDiagram() {
            const diagramPath = selector.value;
            if (!diagramPath) return;

            try {
                // Show loading message
                document.getElementById('diagram').innerHTML = '<p>Loading diagram...</p>';
                
                // Fetch the diagram content
                const response = await fetch(diagramPath);
                if (!response.ok) throw new Error('Failed to load diagram');
                
                const code = await response.text();
                
                // Render the diagram
                const container = document.getElementById('diagram');
                container.innerHTML = `<div class="mermaid">${code}</div>`;
                
                // Initialize and render
                await mermaid.init(undefined, container);
                
                // Add download button
                const downloadBtn = document.createElement('button');
                downloadBtn.textContent = 'Download SVG';
                downloadBtn.onclick = () => downloadSVG(container.querySelector('svg'), diagramPath.replace('.mmd', '.svg'));
                container.appendChild(document.createElement('br'));
                container.appendChild(downloadBtn);
                
            } catch (error) {
                console.error('Error loading diagram:', error);
                document.getElementById('diagram').innerHTML = 
                    `<div style="color:red">Error loading diagram: ${error.message}</div>`;
            }
        }

        // Download SVG
        function downloadSVG(svgElement, filename) {
            if (!svgElement) {
                alert('No SVG to download');
                return;
            }
            
            const serializer = new XMLSerializer();
            const svgStr = serializer.serializeToString(svgElement);
            
            const blob = new Blob([svgStr], { type: 'image/svg+xml' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = filename || 'diagram.svg';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }
    </script>
</body>
</html>
