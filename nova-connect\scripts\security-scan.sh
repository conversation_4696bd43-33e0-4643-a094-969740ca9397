#!/bin/bash
# <PERSON><PERSON>t to perform security scanning for NovaConnect UAC

# Set variables
PROJECT_ID=${1:-"novafuse-test"}
IMAGE_NAME=${2:-"novafuse-uac"}
IMAGE_TAG=${3:-"1.0.0"}
FULL_IMAGE_NAME="gcr.io/$PROJECT_ID/$IMAGE_NAME:$IMAGE_TAG"

# Install Trivy
echo "Installing Trivy..."
if ! command -v trivy &> /dev/null; then
  echo "Trivy not found, installing..."
  curl -sfL https://raw.githubusercontent.com/aquasecurity/trivy/main/contrib/install.sh | sh -s -- -b /usr/local/bin
fi

# Scan the Docker image
echo "Scanning Docker image $FULL_IMAGE_NAME..."
trivy image $FULL_IMAGE_NAME

# Scan the filesystem
echo "Scanning filesystem..."
trivy fs .

# Scan the Kubernetes manifests
echo "Scanning Kubernetes manifests..."
trivy config k8s/marketplace/test/

# Generate a report
echo "Generating security report..."
trivy image --format json --output security-report.json $FULL_IMAGE_NAME

echo "Security scanning complete!"
