/**
 * Impact Assessment Module
 *
 * This module provides functionality for managing privacy impact assessments.
 * It handles creating, retrieving, updating, and deleting assessments,
 * as well as managing assessment findings.
 */

const { logger } = require('./utils/logger');
const db = require('./db');

/**
 * Create a new impact assessment
 * @param {Object} assessmentData - Assessment data
 * @param {string} assessmentData.name - Assessment name
 * @param {string} assessmentData.description - Assessment description
 * @param {string} assessmentData.processingActivity - Processing activity type
 * @param {string} assessmentData.riskLevel - Risk level (low, medium, high)
 * @param {Array} assessmentData.mitigationMeasures - Mitigation measures
 * @param {string} assessmentData.status - Assessment status
 * @returns {Promise<Object>} - Created assessment
 * @throws {Error} - If creation fails
 */
async function createAssessment(assessmentData) {
  try {
    const {
      name,
      description,
      processingActivity,
      riskLevel,
      mitigationMeasures,
      status
    } = assessmentData;

    const query = `
      INSERT INTO privacy_impact_assessments (
        name,
        description,
        processing_activity,
        risk_level,
        mitigation_measures,
        status,
        created_at,
        updated_at
      )
      VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW())
      RETURNING *
    `;

    const params = [
      name,
      description,
      processingActivity,
      riskLevel,
      JSON.stringify(mitigationMeasures),
      status
    ];

    const result = await db.query(query, params);
    const assessment = result.rows[0];

    logger.info(`Created new impact assessment: ${assessment.id}`);
    return assessment;
  } catch (error) {
    logger.error('Error creating impact assessment', error);
    throw new Error('Failed to create impact assessment');
  }
}

/**
 * Get an impact assessment by ID
 * @param {string} assessmentId - Assessment ID
 * @returns {Promise<Object|null>} - Assessment or null if not found
 * @throws {Error} - If retrieval fails
 */
async function getAssessment(assessmentId) {
  try {
    const query = `
      SELECT * FROM privacy_impact_assessments
      WHERE id = $1
    `;

    const result = await db.query(query, [assessmentId]);
    
    if (result.rows.length === 0) {
      logger.debug(`Impact assessment not found: ${assessmentId}`);
      return null;
    }

    return result.rows[0];
  } catch (error) {
    logger.error(`Error retrieving impact assessment: ${assessmentId}`, error);
    throw new Error('Failed to retrieve impact assessment');
  }
}

/**
 * Update an existing impact assessment
 * @param {string} assessmentId - Assessment ID
 * @param {Object} updateData - Data to update
 * @returns {Promise<Object>} - Updated assessment
 * @throws {Error} - If update fails
 */
async function updateAssessment(assessmentId, updateData) {
  try {
    // Build the SET clause and parameters dynamically
    const setClauses = [];
    const params = [];
    let paramIndex = 1;

    // Process each field in the update data
    if (updateData.name !== undefined) {
      setClauses.push(`name = $${paramIndex++}`);
      params.push(updateData.name);
    }

    if (updateData.description !== undefined) {
      setClauses.push(`description = $${paramIndex++}`);
      params.push(updateData.description);
    }

    if (updateData.processingActivity !== undefined) {
      setClauses.push(`processing_activity = $${paramIndex++}`);
      params.push(updateData.processingActivity);
    }

    if (updateData.riskLevel !== undefined) {
      setClauses.push(`risk_level = $${paramIndex++}`);
      params.push(updateData.riskLevel);
    }

    if (updateData.mitigationMeasures !== undefined) {
      setClauses.push(`mitigation_measures = $${paramIndex++}`);
      params.push(JSON.stringify(updateData.mitigationMeasures));
    }

    if (updateData.status !== undefined) {
      setClauses.push(`status = $${paramIndex++}`);
      params.push(updateData.status);
    }

    // Add updated_at timestamp
    setClauses.push(`updated_at = NOW()`);

    // Add the assessment ID as the last parameter
    params.push(assessmentId);

    const query = `
      UPDATE privacy_impact_assessments
      SET ${setClauses.join(', ')}
      WHERE id = $${paramIndex}
      RETURNING *
    `;

    const result = await db.query(query, params);
    const assessment = result.rows[0];

    logger.info(`Updated impact assessment: ${assessmentId}`);
    return assessment;
  } catch (error) {
    logger.error(`Error updating impact assessment: ${assessmentId}`, error);
    throw new Error('Failed to update impact assessment');
  }
}

/**
 * Delete an impact assessment
 * @param {string} assessmentId - Assessment ID
 * @returns {Promise<boolean>} - True if deleted, false if not found
 * @throws {Error} - If deletion fails
 */
async function deleteAssessment(assessmentId) {
  try {
    const query = `
      DELETE FROM privacy_impact_assessments
      WHERE id = $1
    `;

    const result = await db.query(query, [assessmentId]);
    
    if (result.rowCount === 0) {
      logger.debug(`Impact assessment not found for deletion: ${assessmentId}`);
      return false;
    }

    logger.info(`Deleted impact assessment: ${assessmentId}`);
    return true;
  } catch (error) {
    logger.error(`Error deleting impact assessment: ${assessmentId}`, error);
    throw new Error('Failed to delete impact assessment');
  }
}

/**
 * List impact assessments with filtering and pagination
 * @param {Object} filters - Filter criteria
 * @param {Object} pagination - Pagination options
 * @param {number} pagination.page - Page number (default: 1)
 * @param {number} pagination.limit - Items per page (default: 20)
 * @returns {Promise<Object>} - Paginated list of assessments
 * @throws {Error} - If listing fails
 */
async function listAssessments(filters = {}, pagination = {}) {
  try {
    const page = pagination.page || 1;
    const limit = pagination.limit || 20;
    const offset = (page - 1) * limit;

    // Build the WHERE clause and parameters
    const whereClauses = [];
    const params = [];
    let paramIndex = 1;

    if (filters.status) {
      whereClauses.push(`status = $${paramIndex++}`);
      params.push(filters.status);
    }

    if (filters.riskLevel) {
      whereClauses.push(`risk_level = $${paramIndex++}`);
      params.push(filters.riskLevel);
    }

    if (filters.processingActivity) {
      whereClauses.push(`processing_activity = $${paramIndex++}`);
      params.push(filters.processingActivity);
    }

    // Construct the WHERE clause if filters are provided
    const whereClause = whereClauses.length > 0 
      ? `WHERE ${whereClauses.join(' AND ')}` 
      : '';

    // Query for data with pagination
    const dataQuery = `
      SELECT * FROM privacy_impact_assessments
      ${whereClause}
      ORDER BY created_at DESC
      LIMIT ${limit} OFFSET ${offset}
    `;

    // Query for total count
    const countQuery = `
      SELECT COUNT(*) as count FROM privacy_impact_assessments
      ${whereClause}
    `;

    // Execute both queries
    const dataResult = await db.query(dataQuery, params);
    const countResult = await db.query(countQuery, params);

    // Calculate pagination metadata
    const total = parseInt(countResult.rows[0].count, 10);
    const pages = Math.ceil(total / limit);

    return {
      data: dataResult.rows,
      pagination: {
        page,
        limit,
        total,
        pages
      }
    };
  } catch (error) {
    logger.error('Error listing impact assessments', error);
    throw new Error('Failed to list impact assessments');
  }
}

/**
 * Add a finding to an impact assessment
 * @param {string} assessmentId - Assessment ID
 * @param {Object} findingData - Finding data
 * @param {string} findingData.title - Finding title
 * @param {string} findingData.description - Finding description
 * @param {string} findingData.severity - Finding severity
 * @param {string} findingData.recommendation - Recommendation
 * @returns {Promise<Object>} - Created finding
 * @throws {Error} - If creation fails
 */
async function addAssessmentFinding(assessmentId, findingData) {
  try {
    const {
      title,
      description,
      severity,
      recommendation
    } = findingData;

    const query = `
      INSERT INTO assessment_findings (
        assessment_id,
        title,
        description,
        severity,
        recommendation,
        created_at,
        updated_at
      )
      VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
      RETURNING *
    `;

    const params = [
      assessmentId,
      title,
      description,
      severity,
      recommendation
    ];

    const result = await db.query(query, params);
    const finding = result.rows[0];

    logger.info(`Added finding to impact assessment: ${assessmentId}, finding ID: ${finding.id}`);
    return finding;
  } catch (error) {
    logger.error(`Error adding finding to impact assessment: ${assessmentId}`, error);
    throw new Error('Failed to add finding to impact assessment');
  }
}

/**
 * Get findings for an impact assessment
 * @param {string} assessmentId - Assessment ID
 * @returns {Promise<Array>} - List of findings
 * @throws {Error} - If retrieval fails
 */
async function getAssessmentFindings(assessmentId) {
  try {
    const query = `
      SELECT * FROM assessment_findings
      WHERE assessment_id = $1
      ORDER BY created_at DESC
    `;

    const result = await db.query(query, [assessmentId]);
    return result.rows;
  } catch (error) {
    logger.error(`Error retrieving assessment findings: ${assessmentId}`, error);
    throw new Error('Failed to retrieve assessment findings');
  }
}

module.exports = {
  createAssessment,
  getAssessment,
  updateAssessment,
  deleteAssessment,
  listAssessments,
  addAssessmentFinding,
  getAssessmentFindings
};
