/**
 * Logging Middleware
 * 
 * This middleware logs HTTP requests and responses.
 */

const morgan = require('morgan');
const logger = require('../config/logger');

// Create a custom Morgan format
morgan.token('body', (req) => {
  if (req.method === 'POST' || req.method === 'PUT') {
    // Mask sensitive data
    const body = { ...req.body };
    
    // Mask passwords
    if (body.password) {
      body.password = '********';
    }
    
    return JSON.stringify(body);
  }
  return '';
});

morgan.token('user', (req) => {
  return req.user ? req.user.username : 'anonymous';
});

// Create Morgan middleware
const httpLogger = morgan(
  ':remote-addr - :user [:date[clf]] ":method :url HTTP/:http-version" :status :res[content-length] ":referrer" ":user-agent" :body',
  { stream: logger.stream }
);

// Request logging middleware
const requestLogger = (req, res, next) => {
  // Log the request
  logger.info({
    type: 'request',
    method: req.method,
    url: req.url,
    ip: req.ip,
    user: req.user ? req.user.username : 'anonymous',
    body: req.method === 'POST' || req.method === 'PUT' ? maskSensitiveData(req.body) : undefined
  });
  
  next();
};

// Response logging middleware
const responseLogger = (req, res, next) => {
  // Save the original end method
  const originalEnd = res.end;
  
  // Override the end method
  res.end = function(chunk, encoding) {
    // Call the original end method
    originalEnd.call(this, chunk, encoding);
    
    // Log the response
    logger.info({
      type: 'response',
      method: req.method,
      url: req.url,
      status: res.statusCode,
      responseTime: Date.now() - req.startTime,
      user: req.user ? req.user.username : 'anonymous'
    });
  };
  
  next();
};

// Error logging middleware
const errorLogger = (err, req, res, next) => {
  // Log the error
  logger.error({
    type: 'error',
    method: req.method,
    url: req.url,
    status: err.status || 500,
    message: err.message,
    stack: err.stack,
    user: req.user ? req.user.username : 'anonymous'
  });
  
  next(err);
};

// Mask sensitive data
const maskSensitiveData = (data) => {
  if (!data) return data;
  
  const maskedData = { ...data };
  
  // Mask passwords
  if (maskedData.password) {
    maskedData.password = '********';
  }
  
  // Mask other sensitive fields
  const sensitiveFields = ['token', 'apiKey', 'secret', 'credentials'];
  
  sensitiveFields.forEach(field => {
    if (maskedData[field]) {
      maskedData[field] = '********';
    }
  });
  
  return maskedData;
};

module.exports = {
  httpLogger,
  requestLogger,
  responseLogger,
  errorLogger
};
