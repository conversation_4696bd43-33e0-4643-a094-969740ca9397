{"name": "triadic-affiliate-dashboard", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "prisma": "prisma", "postinstall": "next telemetry disable"}, "dependencies": {"@clerk/nextjs": "^4.28.0", "@heroicons/react": "^2.0.18", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-icons": "^1.0.3", "@radix-ui/react-toast": "^1.1.5", "autoprefixer": "^10.4.15", "framer-motion": "^10.16.4", "next": "13.4.19", "postcss": "^8.4.25", "prisma": "^5.8.1", "react": "18.2.0", "react-dom": "18.2.0", "recharts": "^2.10.3", "tailwindcss": "^3.3.3"}, "devDependencies": {"@prisma/client": "^5.8.1", "@types/node": "^20.1.0", "@types/react": "18.3.23", "@types/react-dom": "^18.2.0", "eslint": "^8.45.0", "eslint-config-next": "13.4.19", "typescript": "^5.0.4"}}