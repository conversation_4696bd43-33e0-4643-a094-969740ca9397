#!/usr/bin/env python3
"""
Simple UUFT Test Runner

This script runs the simple UUFT tests and compiles the results into a summary report.
"""

import os
import sys
import json
import time
from datetime import datetime

# Try to import the test modules
try:
    import pi_power_optimization_test
    import cross_domain_1882_test
    import uuft_performance_test
except ImportError as e:
    print(f"Error importing test modules: {e}")
    print("Make sure all test files are in the current directory.")
    sys.exit(1)

# Create results directory
RESULTS_DIR = "uuft_test_results"
os.makedirs(RESULTS_DIR, exist_ok=True)

def run_tests():
    """
    Run all UUFT tests and return the results.

    Returns:
        dict: Results from all tests
    """
    print("Running Simple UUFT Tests")
    print("========================")

    results = {}

    # Run Test 1: π Power Optimization Test
    print("\n1. Running π Power Optimization Test...")
    start_time = time.time()
    try:
        pi_power_results = pi_power_optimization_test.main()
        results["pi_power_optimization"] = {
            "status": "Success",
            "duration": time.time() - start_time,
            "results": pi_power_results
        }
    except Exception as e:
        print(f"Error running π Power Optimization Test: {e}")
        results["pi_power_optimization"] = {
            "status": "Failed",
            "duration": time.time() - start_time,
            "error": str(e)
        }

    # Run Test 2: Cross-Domain 18/82 Pattern Test
    print("\n2. Running Cross-Domain 18/82 Pattern Test...")
    start_time = time.time()
    try:
        cross_domain_results = cross_domain_1882_test.main()
        results["cross_domain_1882"] = {
            "status": "Success",
            "duration": time.time() - start_time,
            "results": cross_domain_results
        }
    except Exception as e:
        print(f"Error running Cross-Domain 18/82 Pattern Test: {e}")
        results["cross_domain_1882"] = {
            "status": "Failed",
            "duration": time.time() - start_time,
            "error": str(e)
        }

    # Run Test 3: UUFT Performance Test
    print("\n3. Running UUFT Performance Test...")
    start_time = time.time()
    try:
        performance_results = uuft_performance_test.main()
        results["uuft_performance"] = {
            "status": "Success",
            "duration": time.time() - start_time,
            "results": performance_results
        }
    except Exception as e:
        print(f"Error running UUFT Performance Test: {e}")
        results["uuft_performance"] = {
            "status": "Failed",
            "duration": time.time() - start_time,
            "error": str(e)
        }

    return results

def compile_summary(results):
    """
    Compile a summary of the test results.

    Args:
        results: Results from run_tests()

    Returns:
        dict: Summary of test results
    """
    summary = {
        "test_date": datetime.now().isoformat(),
        "tests_run": len(results),
        "tests_passed": sum(1 for test in results.values() if test["status"] == "Success"),
        "tests_failed": sum(1 for test in results.values() if test["status"] == "Failed"),
        "total_duration": sum(test["duration"] for test in results.values()),
        "test_results": {}
    }

    # Compile results for Test 1: π Power Optimization Test
    if "pi_power_optimization" in results and results["pi_power_optimization"]["status"] == "Success":
        pi_power_results = results["pi_power_optimization"]["results"]

        # Find the optimal power
        optimal_power = max(pi_power_results.items(), key=lambda x: x[1]["average_accuracy"])[0]
        optimal_accuracy = pi_power_results[optimal_power]["average_accuracy"]

        # Check if π10³ is optimal
        is_pi_10_3_optimal = optimal_power == "10^3"

        summary["test_results"]["pi_power_optimization"] = {
            "optimal_power": optimal_power,
            "optimal_accuracy": optimal_accuracy,
            "is_pi_10_3_optimal": is_pi_10_3_optimal,
            "conclusion": "π10³ is the optimal scaling factor" if is_pi_10_3_optimal else f"π{optimal_power} is the optimal scaling factor, not π10³"
        }

    # Compile results for Test 2: Cross-Domain 18/82 Pattern Test
    if "cross_domain_1882" in results and results["cross_domain_1882"]["status"] == "Success":
        cross_domain_results = results["cross_domain_1882"]["results"]

        # Count domains with 18/82 pattern
        domains_with_1882 = sum(1 for domain, domain_results in cross_domain_results.items()
                               if domain_results["is_1882_domain"])
        total_domains = len(cross_domain_results)

        # Check if control domain shows 18/82 pattern (it shouldn't)
        control_has_1882 = cross_domain_results["control"]["is_1882_domain"] if "control" in cross_domain_results else False

        summary["test_results"]["cross_domain_1882"] = {
            "domains_with_1882": domains_with_1882,
            "total_domains": total_domains,
            "control_has_1882": control_has_1882,
            "conclusion": f"{domains_with_1882}/{total_domains} domains show the 18/82 pattern" +
                         (", but the control domain also shows it (false positive)" if control_has_1882 else "")
        }

    # Compile results for Test 3: UUFT Performance Test
    if "uuft_performance" in results and results["uuft_performance"]["status"] == "Success":
        performance_results = results["uuft_performance"]["results"]

        # Calculate average performance improvement
        improvements = [domain_results["performance_improvement"] for domain_results in performance_results.values()]
        avg_improvement = sum(improvements) / len(improvements)

        # Calculate average UUFT accuracy
        accuracies = [domain_results["metrics"]["uuft"]["accuracy"] for domain_results in performance_results.values()]
        avg_accuracy = sum(accuracies) / len(accuracies)

        # Check if results are close to expected values
        is_improvement_close = abs(avg_improvement - 3142) / 3142 < 0.5  # Within 50% of expected
        is_accuracy_close = abs(avg_accuracy - 0.95) < 0.1  # Within 10% of expected

        summary["test_results"]["uuft_performance"] = {
            "average_improvement": avg_improvement,
            "average_accuracy": avg_accuracy,
            "is_improvement_close": is_improvement_close,
            "is_accuracy_close": is_accuracy_close,
            "conclusion": f"Average improvement: {avg_improvement:.1f}x (expected: 3,142x), " +
                         f"Average accuracy: {avg_accuracy:.4f} (expected: 0.95)"
        }

    # Overall conclusion
    if all(test["status"] == "Success" for test in results.values()):
        if (summary["test_results"].get("pi_power_optimization", {}).get("is_pi_10_3_optimal", False) and
            summary["test_results"].get("cross_domain_1882", {}).get("domains_with_1882", 0) > 0 and
            summary["test_results"].get("uuft_performance", {}).get("is_improvement_close", False)):
            summary["overall_conclusion"] = "Tests provide evidence supporting the UUFT framework's key claims."
        else:
            summary["overall_conclusion"] = "Tests completed successfully but results do not fully support all UUFT claims."
    else:
        summary["overall_conclusion"] = "Some tests failed, unable to fully evaluate UUFT claims."

    return summary

def save_summary(summary):
    """
    Save the summary to a text file.

    Args:
        summary: Summary from compile_summary()
    """
    with open(os.path.join(RESULTS_DIR, "simple_uuft_test_summary.txt"), "w") as f:
        f.write("Simple UUFT Test Summary\n")
        f.write("=======================\n")
        f.write(f"Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

        f.write(f"Tests Run: {summary['tests_run']}\n")
        f.write(f"Tests Passed: {summary['tests_passed']}\n")
        f.write(f"Tests Failed: {summary['tests_failed']}\n")
        f.write(f"Total Duration: {summary['total_duration']:.2f} seconds\n\n")

        f.write("Test Results:\n")

        # Write results for Test 1: π Power Optimization Test
        if "pi_power_optimization" in summary["test_results"]:
            pi_power_results = summary["test_results"]["pi_power_optimization"]
            f.write("\n1. π Power Optimization Test:\n")
            f.write(f"   Optimal Power: {pi_power_results['optimal_power']}\n")
            f.write(f"   Optimal Accuracy: {pi_power_results['optimal_accuracy']:.4f}\n")
            f.write(f"   Is π10³ Optimal? {'Yes' if pi_power_results['is_pi_10_3_optimal'] else 'No'}\n")
            f.write(f"   Conclusion: {pi_power_results['conclusion']}\n")

        # Write results for Test 2: Cross-Domain 18/82 Pattern Test
        if "cross_domain_1882" in summary["test_results"]:
            cross_domain_results = summary["test_results"]["cross_domain_1882"]
            f.write("\n2. Cross-Domain 18/82 Pattern Test:\n")
            f.write(f"   Domains with 18/82 Pattern: {cross_domain_results['domains_with_1882']}/{cross_domain_results['total_domains']}\n")
            f.write(f"   Control Domain Has 18/82 Pattern: {'Yes' if cross_domain_results['control_has_1882'] else 'No'}\n")
            f.write(f"   Conclusion: {cross_domain_results['conclusion']}\n")

        # Write results for Test 3: UUFT Performance Test
        if "uuft_performance" in summary["test_results"]:
            performance_results = summary["test_results"]["uuft_performance"]
            f.write("\n3. UUFT Performance Test:\n")
            f.write(f"   Average Improvement: {performance_results['average_improvement']:.1f}x\n")
            f.write(f"   Average Accuracy: {performance_results['average_accuracy']:.4f}\n")
            f.write(f"   Is Improvement Close to 3,142x? {'Yes' if performance_results['is_improvement_close'] else 'No'}\n")
            f.write(f"   Is Accuracy Close to 95%? {'Yes' if performance_results['is_accuracy_close'] else 'No'}\n")
            f.write(f"   Conclusion: {performance_results['conclusion']}\n")

        f.write(f"\nOverall Conclusion: {summary['overall_conclusion']}\n")

def main():
    """Run all UUFT tests and compile a summary."""
    print("Simple UUFT Test Runner")
    print("======================")

    try:
        # Run all tests
        print("Running tests...")
        results = run_tests()
        print(f"Tests completed. Results: {results.keys()}")

        # Compile summary
        print("Compiling summary...")
        summary = compile_summary(results)
        print(f"Summary compiled: {summary.keys()}")

        # Save summary
        print("Saving summary...")
        save_summary(summary)
        print(f"Summary saved to {os.path.join(RESULTS_DIR, 'simple_uuft_test_summary.txt')}")

        # Print overall conclusion
        if 'overall_conclusion' in summary:
            print(f"\nOverall Conclusion: {summary['overall_conclusion']}")
        else:
            print("\nWarning: No overall conclusion in summary")

        return summary

    except Exception as e:
        print(f"Error in main function: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    main()
