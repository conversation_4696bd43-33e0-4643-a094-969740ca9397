#!/bin/bash
# NovaPi Benchmark Runner - REAL AI MODELS EDITION
# Executes comprehensive AI performance benchmarking with π-coherence optimization

echo "🚀 NovaPi: π-Orchestrated AI Coordination Benchmark"
echo "================================================================"
echo "🧭 π-Coherence Theory: <PERSON>'s Discovery"
echo "🔬 Sequence: 31, 42, 53, 64, 75, 86... (+11 progression)"
echo "⚡ Testing SYSTEM-LEVEL coordination where timing matters:"
echo "   🔄 Batch job scheduling & resource management"
echo "   🤖 Distributed AI agent synchronization"
echo "   🌡️  Thermal regulation & power efficiency"
echo "================================================================"

# Set Python path
export PYTHONPATH="/app/novapi-benchmark:/app:$PYTHONPATH"

# Create output directory
mkdir -p /app/output

echo ""
echo "📋 System Information:"
echo "   Python Version: $(python --version)"
echo "   Available Memory: $(free -h | grep '^Mem:' | awk '{print $2}')"
echo "   CPU Cores: $(nproc)"
echo "   Disk Space: $(df -h /app | tail -1 | awk '{print $4}')"

echo ""
echo "🔧 Installing Real AI Dependencies..."
echo "   This may take several minutes for PyTorch and Transformers..."
pip install --no-cache-dir -r /app/novapi-benchmark/requirements.txt

echo ""
echo "🧪 Testing π-Coherence Scheduler..."
cd /app/novapi-benchmark
python -c "
from benchmarks.pi_scheduler import get_next_interval, get_all_intervals, PiCoherenceTimer
import time

print('🧭 π-Coherence Scheduler Test')
intervals = get_all_intervals()
print(f'✅ π-Intervals loaded: {intervals}')

# Test interval cycling
print('🔄 Testing interval cycling:')
for i in range(6):
    interval = get_next_interval()
    print(f'  Interval {i+1}: {interval:.5f}s ({interval*1000:.2f}ms)')

# Test timer
timer = PiCoherenceTimer()
print('⏱️  Testing π-coherence timer...')
for i in range(3):
    result = timer.apply_delay()
    print(f'  Delay {i+1}: {result[\"target_interval\"]:.5f}s')

print('✅ π-Coherence Scheduler working correctly')
"

echo ""
echo "🔄 Running System Coordination Benchmark..."
python system_coordination_benchmark.py

echo ""
echo "🌡️  Running Thermal Management Benchmark..."
python thermal_management_benchmark.py

echo ""
echo "📊 Generating Performance Report..."
python -c "
from metrics.logger import print_performance_report, calculate_performance_summary
import os

print('📈 REAL AI PERFORMANCE RESULTS:')
print_performance_report()

# Show individual result files
output_dir = '/app/output'
if os.path.exists(output_dir):
    result_files = [f for f in os.listdir(output_dir) if f.endswith('.json')]
    print(f'\\n📁 Result files created: {len(result_files)}')
    for file in result_files:
        print(f'   {file}')
else:
    print('\\n📁 No output directory found')
"

echo ""
echo "💓 Starting Continuous π-Coherence Monitoring..."
echo "   Container will continue running with periodic benchmarks..."
echo "   Use 'docker logs novasentient-consciousness-platform' to monitor"

# Keep container running with periodic status updates
while true; do
    echo "💓 $(date): π-Coherence monitoring active - NovaPi Benchmark Edition running"
    sleep 60
done
