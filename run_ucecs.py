"""
<PERSON><PERSON><PERSON> to run the UCECS system.

This script starts both the API server and the web application.
"""

import os
import sys
import subprocess
import time
import logging
import argparse
import signal

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Global variables to store the processes
api_process = None
web_process = None

def signal_handler(sig, frame):
    """Handle signals to gracefully shut down the processes."""
    logger.info("Shutting down...")
    
    if api_process:
        logger.info("Stopping API server...")
        api_process.terminate()
        api_process.wait()
    
    if web_process:
        logger.info("Stopping web application...")
        web_process.terminate()
        web_process.wait()
    
    sys.exit(0)

def main():
    """Run the UCECS system."""
    global api_process, web_process
    
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Run the UCECS system")
    parser.add_argument("--api-port", type=int, default=5000, help="Port for the API server")
    parser.add_argument("--web-port", type=int, default=3000, help="Port for the web application")
    parser.add_argument("--dev", action="store_true", help="Run in development mode")
    args = parser.parse_args()
    
    # Register signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # Get the paths to the API and web scripts
        api_script_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 
                                      'src', 'ucecs', 'api', 'run_api.py')
        web_script_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 
                                      'src', 'ucecs', 'web', 'run_web.py')
        
        # Create a temporary directory for the example
        temp_dir = os.path.join(os.getcwd(), 'temp_ucecs')
        os.makedirs(temp_dir, exist_ok=True)
        
        # Create an evidence directory
        evidence_dir = os.path.join(temp_dir, 'evidence_data')
        os.makedirs(evidence_dir, exist_ok=True)
        
        # Start the API server
        logger.info(f"Starting API server on port {args.api_port}...")
        api_process = subprocess.Popen([
            sys.executable, 
            api_script_path, 
            "--evidence-dir", evidence_dir,
            "--port", str(args.api_port),
            "--debug" if args.dev else ""
        ])
        
        # Wait for the API server to start
        logger.info("Waiting for API server to start...")
        time.sleep(3)
        
        # Start the web application
        logger.info(f"Starting web application on port {args.web_port}...")
        web_process = subprocess.Popen([
            sys.executable, 
            web_script_path, 
            "--port", str(args.web_port),
            "--dev" if args.dev else ""
        ])
        
        # Wait for the web application to start
        logger.info("Waiting for web application to start...")
        time.sleep(3)
        
        logger.info(f"UCECS system is running:")
        logger.info(f"- API server: http://localhost:{args.api_port}/api/v1")
        logger.info(f"- Web application: http://localhost:{args.web_port}")
        logger.info("Press Ctrl+C to stop")
        
        # Wait for the processes to complete
        api_process.wait()
        web_process.wait()
        
    except KeyboardInterrupt:
        logger.info("Interrupted by user")
    except Exception as e:
        logger.error(f"Error: {e}")
    finally:
        # Stop the processes
        signal_handler(None, None)

if __name__ == "__main__":
    main()
