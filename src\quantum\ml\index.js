/**
 * Machine Learning Components
 *
 * This module exports machine learning components for the Finite Universe
 * Principle defense system, including anomaly detection and predictive analytics.
 */

const { AnomalyDetector, createAnomalyDetector } = require('./anomaly-detector');
const { PredictiveAnalytics, createPredictiveAnalytics } = require('./predictive-analytics');

/**
 * Create all machine learning components
 * @param {Object} options - Configuration options
 * @returns {Object} - Object containing all machine learning components
 */
function createMLComponents(options = {}) {
  return {
    anomalyDetector: createAnomalyDetector(options.anomalyDetectorOptions),
    predictiveAnalytics: createPredictiveAnalytics(options.predictiveAnalyticsOptions)
  };
}

module.exports = {
  // Anomaly detection
  AnomalyDetector,
  createAnomalyDetector,
  
  // Predictive analytics
  PredictiveAnalytics,
  createPredictiveAnalytics,
  
  // Factory function
  createMLComponents
};
