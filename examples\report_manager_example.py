"""
Example of using the Report Manager.

This example demonstrates how to use the Report Manager to generate
evidence reports and compliance summary reports.
"""

import os
import sys
import json
import uuid
import logging
import datetime

# Add the parent directory to the path so we can import the UCECS modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.ucecs.core.report_manager import ReportManager

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """Run the Report Manager example."""
    # Create a temporary directory for the example
    temp_dir = os.path.join(os.getcwd(), 'temp_report_example')
    os.makedirs(temp_dir, exist_ok=True)
    
    # Create a reports directory
    reports_dir = os.path.join(temp_dir, 'reports')
    
    # Create a Report Manager
    report_manager = ReportManager(reports_dir=reports_dir)
    
    # Create sample evidence metadata
    evidence_metadata = {}
    evidence_tags = {}
    evidence_by_category = {
        'configuration': set(),
        'policy': set(),
        'procedure': set(),
        'log': set()
    }
    
    # Create sample requirements
    requirements = {
        'REQ-001': {
            'name': 'Password Policy',
            'description': 'The organization must have a password policy that requires complex passwords.',
            'framework': 'NIST SP 800-53',
            'control': 'IA-5'
        },
        'REQ-002': {
            'name': 'Access Control',
            'description': 'The organization must implement access controls to restrict system access to authorized users.',
            'framework': 'NIST SP 800-53',
            'control': 'AC-3'
        },
        'REQ-003': {
            'name': 'Audit Logging',
            'description': 'The organization must implement audit logging to record security-relevant events.',
            'framework': 'NIST SP 800-53',
            'control': 'AU-2'
        }
    }
    
    # Create sample evidence by requirement
    evidence_by_requirement = {
        'REQ-001': set(),
        'REQ-002': set(),
        'REQ-003': set()
    }
    
    # Create sample evidence items
    for i in range(10):
        # Generate a unique ID
        evidence_id = str(uuid.uuid4())
        
        # Determine the type and category
        if i < 3:
            evidence_type = 'document'
            category = 'policy'
            requirement_id = 'REQ-001'
            is_valid = True
        elif i < 6:
            evidence_type = 'configuration'
            category = 'configuration'
            requirement_id = 'REQ-002'
            is_valid = i != 5  # Make one invalid
        else:
            evidence_type = 'log'
            category = 'log'
            requirement_id = 'REQ-003'
            is_valid = i != 8  # Make one invalid
        
        # Create the evidence metadata
        created_at = (datetime.datetime.now(datetime.timezone.utc) - 
                     datetime.timedelta(days=i)).isoformat()
        
        metadata = {
            'id': evidence_id,
            'type': evidence_type,
            'source': 'example',
            'status': 'stored',
            'created_at': created_at,
            'updated_at': created_at,
            'validation_results': {
                'is_valid': is_valid,
                'details': {
                    'reason': 'Example validation result'
                }
            }
        }
        
        # Add the evidence metadata
        evidence_metadata[evidence_id] = metadata
        
        # Add tags
        tags = set(['example', evidence_type])
        if category:
            tags.add(category)
        evidence_tags[evidence_id] = tags
        
        # Add to category
        if category:
            evidence_by_category[category].add(evidence_id)
        
        # Add to requirement
        evidence_by_requirement[requirement_id].add(evidence_id)
    
    try:
        # Generate an evidence report in JSON format
        logger.info("Generating evidence report in JSON format...")
        json_report = report_manager.generate_evidence_report(
            evidence_metadata=evidence_metadata,
            evidence_tags=evidence_tags,
            evidence_by_category=evidence_by_category,
            format='json'
        )
        logger.info(f"JSON report generated: {json_report}")
        
        # Generate an evidence report in HTML format
        logger.info("Generating evidence report in HTML format...")
        html_report = report_manager.generate_evidence_report(
            evidence_metadata=evidence_metadata,
            evidence_tags=evidence_tags,
            evidence_by_category=evidence_by_category,
            format='html'
        )
        logger.info(f"HTML report generated: {html_report}")
        
        # Generate a filtered evidence report
        logger.info("Generating filtered evidence report...")
        filtered_report = report_manager.generate_evidence_report(
            evidence_metadata=evidence_metadata,
            evidence_tags=evidence_tags,
            evidence_by_category=evidence_by_category,
            format='json',
            filters={
                'category': 'policy',
                'is_valid': True
            }
        )
        logger.info(f"Filtered report generated: {filtered_report}")
        
        # Generate a compliance summary report in JSON format
        logger.info("Generating compliance summary report in JSON format...")
        json_compliance_report = report_manager.generate_compliance_summary_report(
            evidence_metadata=evidence_metadata,
            requirements=requirements,
            evidence_by_requirement=evidence_by_requirement,
            format='json'
        )
        logger.info(f"JSON compliance report generated: {json_compliance_report}")
        
        # Generate a compliance summary report in HTML format
        logger.info("Generating compliance summary report in HTML format...")
        html_compliance_report = report_manager.generate_compliance_summary_report(
            evidence_metadata=evidence_metadata,
            requirements=requirements,
            evidence_by_requirement=evidence_by_requirement,
            format='html'
        )
        logger.info(f"HTML compliance report generated: {html_compliance_report}")
        
        # Open the HTML reports in the browser
        logger.info("Opening HTML reports in the browser...")
        import webbrowser
        webbrowser.open(f"file://{os.path.abspath(html_report)}")
        webbrowser.open(f"file://{os.path.abspath(html_compliance_report)}")
        
    except Exception as e:
        logger.error(f"Error: {e}")
    
    finally:
        # Clean up the temporary directory
        # Uncomment the following line to delete the temporary directory
        # import shutil; shutil.rmtree(temp_dir)
        pass

if __name__ == "__main__":
    main()
