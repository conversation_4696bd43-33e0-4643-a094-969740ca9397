/**
 * User Personas API Routes
 * 
 * This file defines the API routes for user persona-related functionality.
 */

const express = require('express');
const router = express.Router();
const { userPersonas } = require('../../models');

// Feature flag middleware
const checkFeatureAccess = (req, res, next) => {
  // In a real implementation, this would check the user's product tier
  // For simulation, we'll check if the feature flag is enabled in the request
  const productTier = req.headers['x-product-tier'] || 'novaPrime';
  
  // For simulation, we'll allow all features for novaPrime and novaAssistAI
  if (productTier === 'novaPrime' || productTier === 'novaAssistAI') {
    return next();
  }
  
  return res.status(403).json({
    error: 'Feature not available',
    message: `This feature is not available in your current plan (${productTier})`
  });
};

/**
 * @route GET /personas
 * @description Get all user personas
 * @access Private
 */
router.get('/', checkFeatureAccess, (req, res) => {
  // Get query parameters for filtering
  const { role, department } = req.query;
  
  // Filter personas based on query parameters
  let filteredPersonas = [...userPersonas];
  
  if (role) {
    filteredPersonas = filteredPersonas.filter(persona => 
      persona.role.toLowerCase() === role.toLowerCase()
    );
  }
  
  if (department) {
    filteredPersonas = filteredPersonas.filter(persona => 
      persona.department.toLowerCase() === department.toLowerCase()
    );
  }
  
  // Return filtered personas with limited information
  res.json({
    data: filteredPersonas.map(persona => ({
      id: persona.id,
      role: persona.role,
      name: persona.name,
      department: persona.department,
      description: persona.description,
      dashboardCount: persona.dashboards.length
    }))
  });
});

/**
 * @route GET /personas/:id
 * @description Get a specific user persona by ID
 * @access Private
 */
router.get('/:id', checkFeatureAccess, (req, res) => {
  const { id } = req.params;
  
  // Find the persona by ID
  const persona = userPersonas.find(p => p.id === id);
  
  if (!persona) {
    return res.status(404).json({
      error: 'User persona not found',
      message: `No user persona found with ID ${id}`
    });
  }
  
  // Return the persona
  res.json({
    data: persona
  });
});

/**
 * @route GET /personas/:id/dashboards
 * @description Get dashboards for a specific user persona
 * @access Private
 */
router.get('/:id/dashboards', checkFeatureAccess, (req, res) => {
  const { id } = req.params;
  
  // Find the persona by ID
  const persona = userPersonas.find(p => p.id === id);
  
  if (!persona) {
    return res.status(404).json({
      error: 'User persona not found',
      message: `No user persona found with ID ${id}`
    });
  }
  
  // Return the dashboards
  res.json({
    data: {
      personaId: persona.id,
      personaRole: persona.role,
      dashboards: persona.dashboards.map(dashboard => ({
        id: dashboard.id,
        name: dashboard.name,
        description: dashboard.description,
        default: dashboard.default,
        widgetCount: dashboard.widgets.length
      }))
    }
  });
});

/**
 * @route GET /personas/:personaId/dashboards/:dashboardId
 * @description Get a specific dashboard for a user persona
 * @access Private
 */
router.get('/:personaId/dashboards/:dashboardId', checkFeatureAccess, (req, res) => {
  const { personaId, dashboardId } = req.params;
  
  // Find the persona by ID
  const persona = userPersonas.find(p => p.id === personaId);
  
  if (!persona) {
    return res.status(404).json({
      error: 'User persona not found',
      message: `No user persona found with ID ${personaId}`
    });
  }
  
  // Find the dashboard by ID
  const dashboard = persona.dashboards.find(d => d.id === dashboardId);
  
  if (!dashboard) {
    return res.status(404).json({
      error: 'Dashboard not found',
      message: `No dashboard found with ID ${dashboardId} for persona ${personaId}`
    });
  }
  
  // Return the dashboard
  res.json({
    data: {
      personaId: persona.id,
      personaRole: persona.role,
      dashboard
    }
  });
});

/**
 * @route GET /personas/roles
 * @description Get all unique roles from user personas
 * @access Private
 */
router.get('/roles', checkFeatureAccess, (req, res) => {
  // Extract unique roles
  const roles = [...new Set(userPersonas.map(persona => persona.role))];
  
  // Return the roles
  res.json({
    data: roles
  });
});

/**
 * @route GET /personas/departments
 * @description Get all unique departments from user personas
 * @access Private
 */
router.get('/departments', checkFeatureAccess, (req, res) => {
  // Extract unique departments
  const departments = [...new Set(userPersonas.map(persona => persona.department))];
  
  // Return the departments
  res.json({
    data: departments
  });
});

module.exports = router;
