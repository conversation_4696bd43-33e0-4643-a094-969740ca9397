import React, { useState } from 'react';
import { 
  Box, 
  Typography, 
  Paper, 
  Grid, 
  <PERSON>ton, 
  Card, 
  CardContent, 
  CardActions,
  Stepper,
  Step,
  StepLabel,
  Divider
} from '@mui/material';
import {
  Add as AddIcon,
  Dashboard as DashboardIcon,
  People as PeopleIcon,
  <PERSON><PERSON>hart as BarChartIcon,
  Settings as SettingsIcon
} from '@mui/icons-material';
import Layout from '../../components/Layout';

/**
 * Partner Portal Dashboard
 * 
 * This is the main dashboard for the Partner Portal.
 */
const PartnerPortal = () => {
  // Mock data for connectors
  const connectors = [
    { id: 1, name: 'AWS Security Hub', category: 'Cloud Security', status: 'Active', clients: 5 },
    { id: 2, name: 'Ok<PERSON>', category: 'Identity & Access Management', status: 'Active', clients: 8 },
    { id: 3, name: '<PERSON>ra', category: 'Workflow & Ticketing', status: 'Active', clients: 3 },
    { id: 4, name: 'ServiceNow', category: 'ITSM', status: 'Draft', clients: 0 }
  ];
  
  // Mock data for clients
  const clients = [
    { id: 1, name: 'Acme Corp', connectors: 3, status: 'Active' },
    { id: 2, name: 'Globex Industries', connectors: 2, status: 'Active' },
    { id: 3, name: 'Initech', connectors: 1, status: 'Active' },
    { id: 4, name: 'Umbrella Corporation', connectors: 3, status: 'Active' }
  ];
  
  // Mock data for usage
  const usage = {
    apiCalls: 45678,
    apiCallsLimit: 100000,
    connectors: 4,
    connectorsLimit: 10,
    clients: 4,
    clientsLimit: 10
  };

  return (
    <Box>
      <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Partner Portal
        </Typography>
        <Button 
          variant="contained" 
          startIcon={<AddIcon />}
          href="/partner-portal/connectors/new"
        >
          Create New Connector
        </Button>
      </Box>
      
      {/* Quick Stats */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Paper sx={{ p: 3, backgroundColor: 'background.paper', height: '100%' }}>
            <Typography variant="h6" gutterBottom>
              Connectors
            </Typography>
            <Typography variant="h3" color="primary">
              {connectors.length}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              of {usage.connectorsLimit} available
            </Typography>
            <Box sx={{ mt: 2, height: 4, backgroundColor: 'background.default', borderRadius: 2 }}>
              <Box 
                sx={{ 
                  height: '100%', 
                  width: `${(connectors.length / usage.connectorsLimit) * 100}%`, 
                  backgroundColor: 'primary.main',
                  borderRadius: 2
                }} 
              />
            </Box>
          </Paper>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Paper sx={{ p: 3, backgroundColor: 'background.paper', height: '100%' }}>
            <Typography variant="h6" gutterBottom>
              Clients
            </Typography>
            <Typography variant="h3" color="primary">
              {clients.length}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              of {usage.clientsLimit} available
            </Typography>
            <Box sx={{ mt: 2, height: 4, backgroundColor: 'background.default', borderRadius: 2 }}>
              <Box 
                sx={{ 
                  height: '100%', 
                  width: `${(clients.length / usage.clientsLimit) * 100}%`, 
                  backgroundColor: 'primary.main',
                  borderRadius: 2
                }} 
              />
            </Box>
          </Paper>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Paper sx={{ p: 3, backgroundColor: 'background.paper', height: '100%' }}>
            <Typography variant="h6" gutterBottom>
              API Calls
            </Typography>
            <Typography variant="h3" color="primary">
              {usage.apiCalls.toLocaleString()}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              of {usage.apiCallsLimit.toLocaleString()} available
            </Typography>
            <Box sx={{ mt: 2, height: 4, backgroundColor: 'background.default', borderRadius: 2 }}>
              <Box 
                sx={{ 
                  height: '100%', 
                  width: `${(usage.apiCalls / usage.apiCallsLimit) * 100}%`, 
                  backgroundColor: 'primary.main',
                  borderRadius: 2
                }} 
              />
            </Box>
          </Paper>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Paper sx={{ p: 3, backgroundColor: 'background.paper', height: '100%' }}>
            <Typography variant="h6" gutterBottom>
              Subscription
            </Typography>
            <Typography variant="h5" color="primary">
              Professional
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Renews on Jan 1, 2026
            </Typography>
            <Button variant="outlined" size="small" sx={{ mt: 2 }}>
              Upgrade Plan
            </Button>
          </Paper>
        </Grid>
      </Grid>
      
      {/* Connector Builder */}
      <Paper sx={{ p: 3, mb: 4, backgroundColor: 'background.paper' }}>
        <Typography variant="h5" gutterBottom>
          Connector Builder
        </Typography>
        <Typography variant="body1" paragraph>
          Create a new connector in minutes with our step-by-step wizard.
        </Typography>
        
        <Stepper activeStep={-1} alternativeLabel sx={{ mb: 3 }}>
          <Step>
            <StepLabel>Authentication</StepLabel>
          </Step>
          <Step>
            <StepLabel>Endpoints</StepLabel>
          </Step>
          <Step>
            <StepLabel>Data Mapping</StepLabel>
          </Step>
          <Step>
            <StepLabel>Testing</StepLabel>
          </Step>
          <Step>
            <StepLabel>Publish</StepLabel>
          </Step>
        </Stepper>
        
        <Box sx={{ display: 'flex', justifyContent: 'center' }}>
          <Button 
            variant="contained" 
            startIcon={<AddIcon />}
            href="/partner-portal/connectors/new"
            size="large"
          >
            Start Building
          </Button>
        </Box>
      </Paper>
      
      {/* Recent Connectors */}
      <Grid container spacing={4}>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, backgroundColor: 'background.paper' }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">
                Recent Connectors
              </Typography>
              <Button variant="text" size="small" href="/partner-portal/connectors">
                View All
              </Button>
            </Box>
            <Divider sx={{ mb: 2 }} />
            
            <Grid container spacing={2}>
              {connectors.map((connector) => (
                <Grid item xs={12} key={connector.id}>
                  <Card variant="outlined">
                    <CardContent sx={{ py: 2 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Box>
                          <Typography variant="subtitle1">{connector.name}</Typography>
                          <Typography variant="body2" color="text.secondary">
                            {connector.category} • {connector.status}
                          </Typography>
                        </Box>
                        <Box>
                          <Typography variant="body2">
                            {connector.clients} client{connector.clients !== 1 ? 's' : ''}
                          </Typography>
                        </Box>
                      </Box>
                    </CardContent>
                    <CardActions>
                      <Button size="small" href={`/partner-portal/connectors/${connector.id}`}>
                        View Details
                      </Button>
                      <Button size="small" href={`/partner-portal/connectors/${connector.id}/edit`}>
                        Edit
                      </Button>
                    </CardActions>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </Paper>
        </Grid>
        
        {/* Recent Clients */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, backgroundColor: 'background.paper' }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">
                Recent Clients
              </Typography>
              <Button variant="text" size="small" href="/partner-portal/clients">
                View All
              </Button>
            </Box>
            <Divider sx={{ mb: 2 }} />
            
            <Grid container spacing={2}>
              {clients.map((client) => (
                <Grid item xs={12} key={client.id}>
                  <Card variant="outlined">
                    <CardContent sx={{ py: 2 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Box>
                          <Typography variant="subtitle1">{client.name}</Typography>
                          <Typography variant="body2" color="text.secondary">
                            {client.status} • {client.connectors} connector{client.connectors !== 1 ? 's' : ''}
                          </Typography>
                        </Box>
                      </Box>
                    </CardContent>
                    <CardActions>
                      <Button size="small" href={`/partner-portal/clients/${client.id}`}>
                        View Details
                      </Button>
                      <Button size="small" href={`/partner-portal/clients/${client.id}/edit`}>
                        Manage
                      </Button>
                    </CardActions>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default PartnerPortal;
