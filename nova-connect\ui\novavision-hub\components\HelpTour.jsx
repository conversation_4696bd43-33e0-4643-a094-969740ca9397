/**
 * HelpTour Component
 * 
 * A component for displaying guided tours of the application.
 */

import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { useI18n } from '../i18n';
import { useAccessibility } from '../accessibility';
import { useHelp } from '../help/HelpContext';
import { OnboardingTour } from './';

/**
 * HelpTour component
 * 
 * @param {Object} props - Component props
 * @param {string} props.tourId - Tour ID
 * @param {Array} props.steps - Tour steps
 * @param {string} [props.title] - Tour title
 * @param {string} [props.description] - Tour description
 * @param {boolean} [props.autoStart=false] - Whether to start the tour automatically
 * @param {boolean} [props.showSkip=true] - Whether to show skip button
 * @param {boolean} [props.showProgress=true] - Whether to show progress indicator
 * @param {Function} [props.onComplete] - Function to call when the tour is completed
 * @param {Function} [props.onSkip] - Function to call when the tour is skipped
 * @param {Function} [props.onStepChange] - Function to call when the active step changes
 * @param {string} [props.className=''] - Additional CSS class names
 * @param {Object} [props.style={}] - Additional inline styles
 * @returns {React.ReactElement} HelpTour component
 */
const HelpTour = ({
  tourId,
  steps,
  title,
  description,
  autoStart = false,
  showSkip = true,
  showProgress = true,
  onComplete,
  onSkip,
  onStepChange,
  className = '',
  style = {}
}) => {
  // Hooks
  const { translate } = useI18n();
  const { settings } = useAccessibility();
  const { registerHelpContent } = useHelp();
  
  // Register help content for each step
  useEffect(() => {
    // Register tour as help content
    registerHelpContent(tourId, {
      title: title || translate('help.tour', 'Guided Tour'),
      description: description || translate('help.tourDescription', 'A guided tour of the application'),
      content: `
        # ${title || translate('help.tour', 'Guided Tour')}
        
        ${description || translate('help.tourDescription', 'A guided tour of the application')}
        
        ## Steps
        
        ${steps.map((step, index) => `${index + 1}. ${step.title}`).join('\n')}
      `,
      keywords: ['tour', 'guide', 'help', tourId]
    });
    
    // Register each step as help content
    steps.forEach((step, index) => {
      registerHelpContent(`${tourId}-step-${index}`, {
        title: step.title,
        description: step.description,
        content: step.content || step.description,
        keywords: ['tour', 'guide', 'help', 'step', tourId]
      });
    });
  }, [tourId, title, description, steps, registerHelpContent, translate]);
  
  // Handle tour completion
  const handleComplete = () => {
    if (onComplete) {
      onComplete();
    }
  };
  
  // Handle tour skip
  const handleSkip = () => {
    if (onSkip) {
      onSkip();
    }
  };
  
  // Handle step change
  const handleStepChange = (stepIndex) => {
    if (onStepChange) {
      onStepChange(stepIndex);
    }
  };
  
  return (
    <OnboardingTour
      tourId={tourId}
      title={title}
      description={description}
      steps={steps}
      autoStart={autoStart}
      showSkip={showSkip}
      showProgress={showProgress}
      onComplete={handleComplete}
      onSkip={handleSkip}
      onStepChange={handleStepChange}
      className={`help-tour ${className}`}
      style={style}
    />
  );
};

HelpTour.propTypes = {
  tourId: PropTypes.string.isRequired,
  steps: PropTypes.arrayOf(
    PropTypes.shape({
      title: PropTypes.string.isRequired,
      description: PropTypes.string.isRequired,
      position: PropTypes.oneOf(['top', 'right', 'bottom', 'left']),
      targetSelector: PropTypes.string,
      targetElement: PropTypes.object,
      content: PropTypes.string
    })
  ).isRequired,
  title: PropTypes.string,
  description: PropTypes.string,
  autoStart: PropTypes.bool,
  showSkip: PropTypes.bool,
  showProgress: PropTypes.bool,
  onComplete: PropTypes.func,
  onSkip: PropTypes.func,
  onStepChange: PropTypes.func,
  className: PropTypes.string,
  style: PropTypes.object
};

export default HelpTour;
