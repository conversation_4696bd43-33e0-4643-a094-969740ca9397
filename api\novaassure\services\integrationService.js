/**
 * Integration Service
 * 
 * This service provides integration with other NovaFuse components.
 */

const axios = require('axios');
const logger = require('../utils/logger');
const config = require('../../config');

/**
 * NovaConnect Integration
 * 
 * Integrates with NovaConnect for API-based evidence collection.
 */
const novaConnectIntegration = {
  /**
   * Get API connector
   * @param {string} connectorId - Connector ID
   * @returns {Promise<Object>} - Connector details
   */
  async getConnector(connectorId) {
    try {
      const response = await axios.get(`${config.novaConnect.baseUrl}/api/v1/connectors/${connectorId}`, {
        headers: {
          'Authorization': `Bearer ${config.novaConnect.apiKey}`
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error(`Failed to get connector ${connectorId} from NovaConnect`, error);
      throw error;
    }
  },
  
  /**
   * Execute API request
   * @param {string} connectorId - Connector ID
   * @param {string} endpoint - API endpoint
   * @param {string} method - HTTP method
   * @param {Object} [data] - Request data
   * @param {Object} [headers] - Request headers
   * @returns {Promise<Object>} - API response
   */
  async executeApiRequest(connectorId, endpoint, method, data = null, headers = {}) {
    try {
      const response = await axios.post(`${config.novaConnect.baseUrl}/api/v1/connectors/${connectorId}/execute`, {
        endpoint,
        method,
        data,
        headers
      }, {
        headers: {
          'Authorization': `Bearer ${config.novaConnect.apiKey}`
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error(`Failed to execute API request for connector ${connectorId}`, error);
      throw error;
    }
  },
  
  /**
   * Collect evidence from API
   * @param {string} connectorId - Connector ID
   * @param {string} endpoint - API endpoint
   * @param {string} method - HTTP method
   * @param {Object} [data] - Request data
   * @param {Object} [headers] - Request headers
   * @param {string} controlId - Control ID
   * @param {string} [testExecutionId] - Test execution ID
   * @returns {Promise<Object>} - Evidence
   */
  async collectEvidence(connectorId, endpoint, method, data, headers, controlId, testExecutionId) {
    try {
      // Get connector details
      const connector = await this.getConnector(connectorId);
      
      // Execute API request
      const apiResponse = await this.executeApiRequest(connectorId, endpoint, method, data, headers);
      
      // Create evidence
      const evidenceData = {
        name: `API Evidence - ${connector.name} - ${endpoint}`,
        description: `Evidence collected from ${connector.name} API`,
        type: 'api-response',
        controlId,
        testExecutionId,
        content: JSON.stringify({
          connector: {
            id: connectorId,
            name: connector.name,
            type: connector.type
          },
          request: {
            endpoint,
            method,
            data,
            headers
          },
          response: apiResponse,
          timestamp: new Date().toISOString()
        }, null, 2),
        metadata: {
          connectorId,
          endpoint,
          method,
          timestamp: new Date().toISOString()
        }
      };
      
      // Create evidence using NovaAssure API
      const response = await axios.post(`${config.baseUrl}/api/v1/novaassure/evidence`, evidenceData, {
        headers: {
          'Authorization': `Bearer ${config.apiKey}`
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error(`Failed to collect evidence from connector ${connectorId}`, error);
      throw error;
    }
  }
};

/**
 * NovaPulse Integration
 * 
 * Integrates with NovaPulse for real-time monitoring.
 */
const novaPulseIntegration = {
  /**
   * Register control for monitoring
   * @param {string} controlId - Control ID
   * @param {Object} monitoringConfig - Monitoring configuration
   * @returns {Promise<Object>} - Monitoring registration
   */
  async registerControlMonitoring(controlId, monitoringConfig) {
    try {
      const response = await axios.post(`${config.novaPulse.baseUrl}/api/v1/monitoring/controls`, {
        controlId,
        config: monitoringConfig
      }, {
        headers: {
          'Authorization': `Bearer ${config.novaPulse.apiKey}`
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error(`Failed to register control ${controlId} for monitoring`, error);
      throw error;
    }
  },
  
  /**
   * Get monitoring status
   * @param {string} controlId - Control ID
   * @returns {Promise<Object>} - Monitoring status
   */
  async getMonitoringStatus(controlId) {
    try {
      const response = await axios.get(`${config.novaPulse.baseUrl}/api/v1/monitoring/controls/${controlId}/status`, {
        headers: {
          'Authorization': `Bearer ${config.novaPulse.apiKey}`
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error(`Failed to get monitoring status for control ${controlId}`, error);
      throw error;
    }
  },
  
  /**
   * Get monitoring alerts
   * @param {string} controlId - Control ID
   * @param {Object} [filters] - Alert filters
   * @returns {Promise<Array>} - Monitoring alerts
   */
  async getMonitoringAlerts(controlId, filters = {}) {
    try {
      const response = await axios.get(`${config.novaPulse.baseUrl}/api/v1/monitoring/controls/${controlId}/alerts`, {
        params: filters,
        headers: {
          'Authorization': `Bearer ${config.novaPulse.apiKey}`
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error(`Failed to get monitoring alerts for control ${controlId}`, error);
      throw error;
    }
  }
};

/**
 * NovaFlow Integration
 * 
 * Integrates with NovaFlow for workflow orchestration.
 */
const novaFlowIntegration = {
  /**
   * Create workflow
   * @param {Object} workflowData - Workflow data
   * @returns {Promise<Object>} - Created workflow
   */
  async createWorkflow(workflowData) {
    try {
      const response = await axios.post(`${config.novaFlow.baseUrl}/api/v1/workflows`, workflowData, {
        headers: {
          'Authorization': `Bearer ${config.novaFlow.apiKey}`
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error('Failed to create workflow', error);
      throw error;
    }
  },
  
  /**
   * Get workflow
   * @param {string} workflowId - Workflow ID
   * @returns {Promise<Object>} - Workflow details
   */
  async getWorkflow(workflowId) {
    try {
      const response = await axios.get(`${config.novaFlow.baseUrl}/api/v1/workflows/${workflowId}`, {
        headers: {
          'Authorization': `Bearer ${config.novaFlow.apiKey}`
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error(`Failed to get workflow ${workflowId}`, error);
      throw error;
    }
  },
  
  /**
   * Execute workflow
   * @param {string} workflowId - Workflow ID
   * @param {Object} [inputs] - Workflow inputs
   * @returns {Promise<Object>} - Workflow execution
   */
  async executeWorkflow(workflowId, inputs = {}) {
    try {
      const response = await axios.post(`${config.novaFlow.baseUrl}/api/v1/workflows/${workflowId}/execute`, {
        inputs
      }, {
        headers: {
          'Authorization': `Bearer ${config.novaFlow.apiKey}`
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error(`Failed to execute workflow ${workflowId}`, error);
      throw error;
    }
  },
  
  /**
   * Create test execution workflow
   * @param {string} testPlanId - Test plan ID
   * @param {string} [assigneeId] - Assignee ID
   * @returns {Promise<Object>} - Created workflow
   */
  async createTestExecutionWorkflow(testPlanId, assigneeId) {
    try {
      // Create workflow for test execution
      const workflowData = {
        name: `Test Execution Workflow - ${testPlanId}`,
        description: 'Workflow for test execution',
        type: 'test-execution',
        steps: [
          {
            id: 'start-test-execution',
            name: 'Start Test Execution',
            type: 'api',
            config: {
              url: `${config.baseUrl}/api/v1/novaassure/test-execution/start`,
              method: 'POST',
              body: {
                testPlanId,
                executedBy: assigneeId
              }
            },
            next: 'collect-evidence'
          },
          {
            id: 'collect-evidence',
            name: 'Collect Evidence',
            type: 'task',
            config: {
              assignee: assigneeId,
              instructions: 'Collect evidence for each control in the test plan'
            },
            next: 'review-evidence'
          },
          {
            id: 'review-evidence',
            name: 'Review Evidence',
            type: 'task',
            config: {
              assignee: assigneeId,
              instructions: 'Review collected evidence'
            },
            next: 'complete-test-execution'
          },
          {
            id: 'complete-test-execution',
            name: 'Complete Test Execution',
            type: 'api',
            config: {
              url: `${config.baseUrl}/api/v1/novaassure/test-execution/{{testExecutionId}}/complete`,
              method: 'POST',
              body: {
                results: '{{results}}',
                notes: '{{notes}}'
              }
            },
            next: 'generate-report'
          },
          {
            id: 'generate-report',
            name: 'Generate Report',
            type: 'api',
            config: {
              url: `${config.baseUrl}/api/v1/novaassure/reports/generate/test-results`,
              method: 'POST',
              body: {
                name: `Test Results Report - ${testPlanId}`,
                description: 'Test results report',
                testPlanId,
                testExecutionId: '{{testExecutionId}}',
                format: 'pdf'
              }
            },
            next: null
          }
        ]
      };
      
      return await this.createWorkflow(workflowData);
    } catch (error) {
      logger.error(`Failed to create test execution workflow for test plan ${testPlanId}`, error);
      throw error;
    }
  }
};

/**
 * NovaAssistAI Integration
 * 
 * Integrates with NovaAssistAI for AI-powered assistance.
 */
const novaAssistAIIntegration = {
  /**
   * Get control recommendations
   * @param {string} controlId - Control ID
   * @returns {Promise<Array>} - Recommendations
   */
  async getControlRecommendations(controlId) {
    try {
      const response = await axios.post(`${config.novaAssistAI.baseUrl}/api/v1/recommendations/controls`, {
        controlId
      }, {
        headers: {
          'Authorization': `Bearer ${config.novaAssistAI.apiKey}`
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error(`Failed to get recommendations for control ${controlId}`, error);
      throw error;
    }
  },
  
  /**
   * Generate test procedures
   * @param {string} controlId - Control ID
   * @returns {Promise<Array>} - Test procedures
   */
  async generateTestProcedures(controlId) {
    try {
      const response = await axios.post(`${config.novaAssistAI.baseUrl}/api/v1/generate/test-procedures`, {
        controlId
      }, {
        headers: {
          'Authorization': `Bearer ${config.novaAssistAI.apiKey}`
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error(`Failed to generate test procedures for control ${controlId}`, error);
      throw error;
    }
  },
  
  /**
   * Analyze test results
   * @param {string} testExecutionId - Test execution ID
   * @returns {Promise<Object>} - Analysis
   */
  async analyzeTestResults(testExecutionId) {
    try {
      const response = await axios.post(`${config.novaAssistAI.baseUrl}/api/v1/analyze/test-results`, {
        testExecutionId
      }, {
        headers: {
          'Authorization': `Bearer ${config.novaAssistAI.apiKey}`
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error(`Failed to analyze test results for execution ${testExecutionId}`, error);
      throw error;
    }
  }
};

/**
 * NovaSphere Integration
 * 
 * Integrates with NovaSphere for visualization.
 */
const novaSphereIntegration = {
  /**
   * Create dashboard
   * @param {Object} dashboardData - Dashboard data
   * @returns {Promise<Object>} - Created dashboard
   */
  async createDashboard(dashboardData) {
    try {
      const response = await axios.post(`${config.novaSphere.baseUrl}/api/v1/dashboards`, dashboardData, {
        headers: {
          'Authorization': `Bearer ${config.novaSphere.apiKey}`
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error('Failed to create dashboard', error);
      throw error;
    }
  },
  
  /**
   * Create compliance dashboard
   * @param {string} framework - Framework
   * @param {string} name - Dashboard name
   * @param {string} description - Dashboard description
   * @returns {Promise<Object>} - Created dashboard
   */
  async createComplianceDashboard(framework, name, description) {
    try {
      const dashboardData = {
        name,
        description,
        type: 'compliance',
        config: {
          framework,
          widgets: [
            {
              type: 'compliance-summary',
              title: 'Compliance Summary',
              size: 'large',
              position: { x: 0, y: 0 }
            },
            {
              type: 'control-status',
              title: 'Control Status',
              size: 'medium',
              position: { x: 0, y: 1 }
            },
            {
              type: 'risk-distribution',
              title: 'Risk Distribution',
              size: 'medium',
              position: { x: 1, y: 1 }
            },
            {
              type: 'recent-test-executions',
              title: 'Recent Test Executions',
              size: 'large',
              position: { x: 0, y: 2 }
            },
            {
              type: 'evidence-summary',
              title: 'Evidence Summary',
              size: 'medium',
              position: { x: 0, y: 3 }
            },
            {
              type: 'compliance-trend',
              title: 'Compliance Trend',
              size: 'medium',
              position: { x: 1, y: 3 }
            }
          ]
        }
      };
      
      return await this.createDashboard(dashboardData);
    } catch (error) {
      logger.error(`Failed to create compliance dashboard for framework ${framework}`, error);
      throw error;
    }
  }
};

module.exports = {
  novaConnectIntegration,
  novaPulseIntegration,
  novaFlowIntegration,
  novaAssistAIIntegration,
  novaSphereIntegration
};
