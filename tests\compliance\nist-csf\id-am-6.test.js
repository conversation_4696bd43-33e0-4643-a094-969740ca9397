/**
 * NIST CSF ID.AM-6 Test Implementation
 * 
 * Tests NovaFuse compliance with NIST CSF ID.AM-6: Cybersecurity roles and responsibilities
 */

const { describe, it, expect } = require('@jest/globals');
const fs = require('fs');
const path = require('path');
const os = require('os');

// Import the TrackingManager mock
const { TrackingManager } = require('../../mocks/tracking-manager.mock');

describe('NIST CSF Compliance - ID.AM-6: Cybersecurity roles and responsibilities', () => {
  let trackingManager;
  let tempDir;
  
  beforeEach(() => {
    // Create a temporary directory for test data
    tempDir = fs.mkdtempSync(path.join(os.tmpdir(), 'nist-csf-test-'));
    
    // Initialize the TrackingManager with the temporary directory
    trackingManager = new TrackingManager(tempDir);
  });
  
  afterEach(() => {
    // Clean up the temporary directory
    if (fs.existsSync(tempDir)) {
      fs.rmSync(tempDir, { recursive: true, force: true });
    }
  });
  
  it('should support tracking cybersecurity roles and responsibilities', () => {
    // Create a requirement for cybersecurity roles and responsibilities
    const requirement = trackingManager.create_requirement({
      name: 'Define Cybersecurity Roles and Responsibilities',
      description: 'Define and document cybersecurity roles and responsibilities for all staff and third-party stakeholders',
      framework: 'NIST CSF',
      category: 'IDENTIFY - Asset Management',
      priority: 'high',
      status: 'in_progress',
      due_date: '2023-12-31',
      assigned_to: 'security_officer',
      tags: ['nist_csf', 'id.am-6', 'roles_responsibilities']
    });
    
    // Create activities for implementing the requirement
    const activity1 = trackingManager.create_activity({
      name: 'Define Cybersecurity Roles',
      description: 'Define cybersecurity roles for all staff and third-party stakeholders',
      requirement_id: requirement.id,
      type: 'documentation',
      status: 'completed',
      start_date: '2023-01-01',
      end_date: '2023-01-15',
      assigned_to: 'security_officer',
      notes: 'Cybersecurity roles defined and documented'
    });
    
    const activity2 = trackingManager.create_activity({
      name: 'Define Cybersecurity Responsibilities',
      description: 'Define cybersecurity responsibilities for all staff and third-party stakeholders',
      requirement_id: requirement.id,
      type: 'documentation',
      status: 'completed',
      start_date: '2023-01-16',
      end_date: '2023-01-31',
      assigned_to: 'security_officer',
      notes: 'Cybersecurity responsibilities defined and documented'
    });
    
    const activity3 = trackingManager.create_activity({
      name: 'Communicate Roles and Responsibilities',
      description: 'Communicate cybersecurity roles and responsibilities to all stakeholders',
      requirement_id: requirement.id,
      type: 'meeting',
      status: 'in_progress',
      start_date: '2023-02-01',
      end_date: '2023-02-15',
      assigned_to: 'security_officer',
      notes: 'Communication in progress'
    });
    
    // Verify the requirement was created correctly
    expect(requirement).toBeDefined();
    expect(requirement.framework).toBe('NIST CSF');
    expect(requirement.tags).toContain('id.am-6');
    
    // Verify the activities were created correctly
    expect(activity1).toBeDefined();
    expect(activity1.requirement_id).toBe(requirement.id);
    expect(activity1.status).toBe('completed');
    
    expect(activity2).toBeDefined();
    expect(activity2.requirement_id).toBe(requirement.id);
    expect(activity2.status).toBe('completed');
    
    expect(activity3).toBeDefined();
    expect(activity3.requirement_id).toBe(requirement.id);
    expect(activity3.status).toBe('in_progress');
    
    // Verify the activities can be retrieved for the requirement
    const activities = trackingManager.get_requirement_activities(requirement.id);
    expect(activities.length).toBe(3);
    expect(activities).toContainEqual(activity1);
    expect(activities).toContainEqual(activity2);
    expect(activities).toContainEqual(activity3);
    
    // Document compliance status
    console.log('COMPLIANCE STATUS: PARTIAL');
    console.log('COMPLIANCE NOTES: NovaFuse supports tracking cybersecurity roles and responsibilities, but does not enforce their definition or communication.');
    console.log('GAPS: NovaFuse does not provide templates or guidance for defining cybersecurity roles and responsibilities.');
    console.log('RECOMMENDATIONS: Add templates and guidance for defining cybersecurity roles and responsibilities.');
  });
});
