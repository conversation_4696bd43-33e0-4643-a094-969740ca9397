"""
Real Data Provider for 3Ms Simulation

This module provides real-world data from various domains to enhance the 3Ms simulation.
"""

import numpy as np
from typing import Dict, List, Tuple

# Real-world wealth distribution data (from analyze_wealth_distribution.py)
WEALTH_DATA = {
    "Global": {
        "Top_1_percent": 45.8,  # Percentage of global wealth held by top 1%
        "Top_10_percent": 81.7,  # Percentage of global wealth held by top 10%
        "Bottom_50_percent": 1.1,  # Percentage of global wealth held by bottom 50%
        "Middle_40_percent": 17.2  # Percentage of global wealth held by middle 40%
    },
    "US": {
        "Top_1_percent": 35.3,  # Percentage of US wealth held by top 1%
        "Top_10_percent": 76.0,  # Percentage of US wealth held by top 10%
        "Bottom_50_percent": 2.6,  # Percentage of US wealth held by bottom 50%
        "Middle_40_percent": 21.4  # Percentage of US wealth held by middle 40%
    },
    "China": {
        "Top_1_percent": 30.6,  # Percentage of China's wealth held by top 1%
        "Top_10_percent": 67.8,  # Percentage of China's wealth held by top 10%
        "Bottom_50_percent": 6.4,  # Percentage of China's wealth held by bottom 50%
        "Middle_40_percent": 25.8  # Percentage of China's wealth held by middle 40%
    }
}

# Synthetic proteomics data (inspired by analyze_proteomics_data.py)
PROTEOMICS_DATA = {
    "protein_abundance": np.random.lognormal(mean=2, sigma=1.5, size=1000),
    "expression_levels": np.random.weibull(1.5, 1000) * 100,
    "protein_complexes": np.random.pareto(1.2, 1000) * 50
}

def get_wealth_metrics(country: str = "Global") -> Dict[str, float]:
    """Get wealth distribution metrics for a specific country."""
    return WEALTH_DATA.get(country, WEALTH_DATA["Global"])

def get_proteomics_metrics() -> Dict[str, np.ndarray]:
    """Get proteomics metrics."""
    return PROTEOMICS_DATA

def get_3ms_metrics() -> Dict[str, List[float]]:
    """
    Generate 3Ms metrics (μ, Ψᶜʰ, κ) based on real-world data.
    
    Returns:
        Dict containing lists of depth (μ), ethics (Ψᶜʰ), and stability (κ) values
    """
    # Use wealth inequality as a proxy for ethics (Ψᶜʰ)
    wealth_metrics = get_wealth_metrics()
    ethics_values = [
        wealth_metrics["Top_1_percent"],
        wealth_metrics["Top_10_percent"],
        wealth_metrics["Middle_40_percent"],
        wealth_metrics["Bottom_50_percent"]
    ]
    
    # Use proteomics data for depth (μ) - more diverse data = more depth
    proteomics = get_proteomics_metrics()
    depth_values = [
        np.mean(proteomics["protein_abundance"]),
        np.median(proteomics["expression_levels"]),
        np.std(proteomics["protein_complexes"])
    ]
    
    # Calculate stability (κ) - inverse of variance in ethics
    stability = 1 / np.var(ethics_values) if np.var(ethics_values) > 0 else 1000
    
    return {
        "depth": depth_values,
        "ethics": ethics_values,
        "stability": [stability] * len(ethics_values)  # Same stability for all ethics values
    }
