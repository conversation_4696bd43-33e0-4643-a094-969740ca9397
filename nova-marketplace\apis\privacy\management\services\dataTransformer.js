/**
 * Data Transformer Service
 * 
 * This service transforms data between the Privacy Management API and external systems.
 */

/**
 * Transform request data for an external system
 * @param {Object} data - Request data
 * @param {string} integrationType - Integration type
 * @param {string} action - Action being performed
 * @returns {Promise<Object>} Transformed request data
 */
const transformRequest = async (data, integrationType, action) => {
  // Get the appropriate transformer for the integration type and action
  const transformer = getRequestTransformer(integrationType, action);
  
  // Transform the data
  return await transformer(data);
};

/**
 * Transform response data from an external system
 * @param {Object} response - Response data
 * @param {string} integrationType - Integration type
 * @param {string} action - Action being performed
 * @returns {Promise<Object>} Transformed response data
 */
const transformResponse = async (response, integrationType, action) => {
  // Get the appropriate transformer for the integration type and action
  const transformer = getResponseTransformer(integrationType, action);
  
  // Transform the data
  return await transformer(response);
};

/**
 * Get the appropriate request transformer for an integration type and action
 * @param {string} integrationType - Integration type
 * @param {string} action - Action being performed
 * @returns {Function} Request transformer function
 */
const getRequestTransformer = (integrationType, action) => {
  // Check if there's a specific transformer for this integration type and action
  const specificTransformer = requestTransformers[`${integrationType}_${action}`];
  
  if (specificTransformer) {
    return specificTransformer;
  }
  
  // Check if there's a transformer for this integration type
  const typeTransformer = requestTransformers[integrationType];
  
  if (typeTransformer) {
    return typeTransformer;
  }
  
  // Check if there's a transformer for this action
  const actionTransformer = requestTransformers[action];
  
  if (actionTransformer) {
    return actionTransformer;
  }
  
  // Use the default transformer
  return defaultRequestTransformer;
};

/**
 * Get the appropriate response transformer for an integration type and action
 * @param {string} integrationType - Integration type
 * @param {string} action - Action being performed
 * @returns {Function} Response transformer function
 */
const getResponseTransformer = (integrationType, action) => {
  // Check if there's a specific transformer for this integration type and action
  const specificTransformer = responseTransformers[`${integrationType}_${action}`];
  
  if (specificTransformer) {
    return specificTransformer;
  }
  
  // Check if there's a transformer for this integration type
  const typeTransformer = responseTransformers[integrationType];
  
  if (typeTransformer) {
    return typeTransformer;
  }
  
  // Check if there's a transformer for this action
  const actionTransformer = responseTransformers[action];
  
  if (actionTransformer) {
    return actionTransformer;
  }
  
  // Use the default transformer
  return defaultResponseTransformer;
};

/**
 * Default request transformer
 * @param {Object} data - Request data
 * @returns {Promise<Object>} Transformed request data
 */
const defaultRequestTransformer = async (data) => {
  // In a real implementation, this might do some basic transformations
  // For now, we'll just return the original data
  return data;
};

/**
 * Default response transformer
 * @param {Object} response - Response data
 * @returns {Promise<Object>} Transformed response data
 */
const defaultResponseTransformer = async (response) => {
  // In a real implementation, this might do some basic transformations
  // For now, we'll just return the response data
  return response.data;
};

// Request transformers for specific integration types and actions
const requestTransformers = {
  // CRM integration transformers
  crm: async (data) => {
    // Transform data for CRM systems
    return data;
  },
  
  // Marketing integration transformers
  marketing: async (data) => {
    // Transform data for marketing systems
    return data;
  },
  
  // Data export action transformers
  'data-export': async (data) => {
    // Transform data for data export actions
    return {
      identifiers: data.identifiers || {},
      dataCategories: data.dataCategories || [],
      dateRange: data.dateRange || { start: null, end: null }
    };
  },
  
  // Data deletion action transformers
  'data-deletion': async (data) => {
    // Transform data for data deletion actions
    return {
      identifiers: data.identifiers || {},
      dataCategories: data.dataCategories || [],
      deletionType: data.deletionType || 'soft'
    };
  },
  
  // Specific transformers for integration type + action combinations
  'crm_data-export': async (data) => {
    // Transform data for CRM data export
    return {
      email: data.identifiers.email,
      fields: mapDataCategoriesToCrmFields(data.dataCategories),
      dateRange: data.dateRange
    };
  },
  
  'marketing_data-export': async (data) => {
    // Transform data for marketing data export
    return {
      subscriberId: data.identifiers.email,
      dataTypes: mapDataCategoriesToMarketingFields(data.dataCategories),
      timeFrame: {
        from: data.dateRange?.start,
        to: data.dateRange?.end
      }
    };
  }
};

// Response transformers for specific integration types and actions
const responseTransformers = {
  // CRM integration transformers
  crm: async (response) => {
    // Transform response from CRM systems
    return response.data;
  },
  
  // Marketing integration transformers
  marketing: async (response) => {
    // Transform response from marketing systems
    return response.data;
  },
  
  // Data export action transformers
  'data-export': async (response) => {
    // Transform response from data export actions
    return {
      exportId: response.data.id || `export-${Date.now()}`,
      exportData: response.data,
      exportTimestamp: new Date()
    };
  },
  
  // Data deletion action transformers
  'data-deletion': async (response) => {
    // Transform response from data deletion actions
    return {
      deletionId: response.data.id || `deletion-${Date.now()}`,
      status: response.data.status || 'completed',
      deletionTimestamp: new Date()
    };
  },
  
  // Specific transformers for integration type + action combinations
  'crm_data-export': async (response) => {
    // Transform response from CRM data export
    return {
      exportId: `crm-export-${Date.now()}`,
      contactData: response.data.contact || {},
      accountData: response.data.account || {},
      activityData: response.data.activities || [],
      exportTimestamp: new Date()
    };
  },
  
  'marketing_data-export': async (response) => {
    // Transform response from marketing data export
    return {
      exportId: `marketing-export-${Date.now()}`,
      subscriberData: response.data.subscriber || {},
      campaignData: response.data.campaigns || [],
      interactionData: response.data.interactions || [],
      exportTimestamp: new Date()
    };
  }
};

/**
 * Map data categories to CRM fields
 * @param {Array} dataCategories - Data categories
 * @returns {Array} CRM fields
 */
const mapDataCategoriesToCrmFields = (dataCategories) => {
  const fieldMap = {
    'Contact Information': ['name', 'email', 'phone', 'address'],
    'Account Information': ['account_name', 'account_type', 'industry'],
    'Activity History': ['activities', 'notes', 'tasks'],
    'Purchase History': ['opportunities', 'deals', 'orders']
  };
  
  let fields = [];
  
  dataCategories.forEach(category => {
    if (fieldMap[category]) {
      fields = [...fields, ...fieldMap[category]];
    }
  });
  
  return fields;
};

/**
 * Map data categories to marketing fields
 * @param {Array} dataCategories - Data categories
 * @returns {Array} Marketing fields
 */
const mapDataCategoriesToMarketingFields = (dataCategories) => {
  const fieldMap = {
    'Contact Information': ['profile', 'contact_details'],
    'Marketing Preferences': ['preferences', 'subscriptions'],
    'Campaign History': ['campaigns', 'emails_received'],
    'Interaction Data': ['clicks', 'opens', 'website_visits']
  };
  
  let fields = [];
  
  dataCategories.forEach(category => {
    if (fieldMap[category]) {
      fields = [...fields, ...fieldMap[category]];
    }
  });
  
  return fields;
};

module.exports = {
  transformRequest,
  transformResponse
};
