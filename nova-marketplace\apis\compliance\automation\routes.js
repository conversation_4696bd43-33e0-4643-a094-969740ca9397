/**
 * Compliance Automation API - Routes
 * 
 * This file defines the routes for the Compliance Automation API.
 */

const express = require('express');
const router = express.Router();
const { automationRuleController, workflowController } = require('./controllers');
const validate = require('../../../middleware/validate');
const validationSchemas = require('./validation');
const auth = require('../../../middleware/auth');

/**
 * Automation Rule Routes
 */
// Get all automation rules
router.get('/rules', auth, validate(validationSchemas.query.pagination, 'query'), automationRuleController.getAllAutomationRules);

// Get automation rule by ID
router.get('/rules/:id', auth, automationRuleController.getAutomationRuleById);

// Create a new automation rule
router.post('/rules', auth, validate(validationSchemas.automationRule.create), automationRuleController.createAutomationRule);

// Update an automation rule
router.put('/rules/:id', auth, validate(validationSchemas.automationRule.update), automationRuleController.updateAutomationRule);

// Delete an automation rule
router.delete('/rules/:id', auth, automationRuleController.deleteAutomationRule);

/**
 * Workflow Routes
 */
// Get all workflows
router.get('/workflows', auth, validate(validationSchemas.query.pagination, 'query'), workflowController.getAllWorkflows);

// Get workflow by ID
router.get('/workflows/:id', auth, workflowController.getWorkflowById);

// Create a new workflow
router.post('/workflows', auth, validate(validationSchemas.workflow.create), workflowController.createWorkflow);

// Update a workflow
router.put('/workflows/:id', auth, validate(validationSchemas.workflow.update), workflowController.updateWorkflow);

// Delete a workflow
router.delete('/workflows/:id', auth, workflowController.deleteWorkflow);

module.exports = router;
