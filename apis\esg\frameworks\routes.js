const express = require('express');
const { validateRequest } = require('./validation');
const controllers = require('./controllers');

const router = express.Router();

/**
 * @swagger
 * /governance/esg/frameworks:
 *   get:
 *     summary: Get a list of ESG frameworks
 *     description: Returns a paginated list of ESG frameworks with optional filtering
 *     tags: [ESG Frameworks]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - $ref: '#/components/parameters/page'
 *       - $ref: '#/components/parameters/limit'
 *       - $ref: '#/components/parameters/sortBy'
 *       - $ref: '#/components/parameters/sortOrder'
 *       - name: category
 *         in: query
 *         description: Filter by framework category
 *         schema:
 *           type: string
 *           enum: [environmental, social, governance, general, industry-specific]
 *       - name: isActive
 *         in: query
 *         description: Filter by active status
 *         schema:
 *           type: boolean
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/ESGFramework'
 *                 pagination:
 *                   $ref: '#/components/schemas/Pagination'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/', controllers.getFrameworks);

/**
 * @swagger
 * /governance/esg/frameworks/{id}:
 *   get:
 *     summary: Get a specific ESG framework
 *     description: Returns a specific ESG framework by ID
 *     tags: [ESG Frameworks]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: ESG framework ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/ESGFramework'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/:id', controllers.getFrameworkById);

/**
 * @swagger
 * /governance/esg/frameworks:
 *   post:
 *     summary: Create a new ESG framework
 *     description: Creates a new ESG framework
 *     tags: [ESG Frameworks]
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               code:
 *                 type: string
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               version:
 *                 type: string
 *               organization:
 *                 type: string
 *               website:
 *                 type: string
 *               category:
 *                 type: string
 *                 enum: [environmental, social, governance, general, industry-specific]
 *               industry:
 *                 type: string
 *               region:
 *                 type: string
 *               isActive:
 *                 type: boolean
 *             required:
 *               - code
 *               - name
 *               - description
 *               - version
 *               - organization
 *               - category
 *     responses:
 *       201:
 *         description: Framework created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/ESGFramework'
 *                 message:
 *                   type: string
 *                   example: ESG framework created successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/', validateRequest('createFramework'), controllers.createFramework);

/**
 * @swagger
 * /governance/esg/frameworks/{id}:
 *   put:
 *     summary: Update an ESG framework
 *     description: Updates an existing ESG framework
 *     tags: [ESG Frameworks]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: ESG framework ID
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               code:
 *                 type: string
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               version:
 *                 type: string
 *               organization:
 *                 type: string
 *               website:
 *                 type: string
 *               category:
 *                 type: string
 *                 enum: [environmental, social, governance, general, industry-specific]
 *               industry:
 *                 type: string
 *               region:
 *                 type: string
 *               isActive:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: Framework updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/ESGFramework'
 *                 message:
 *                   type: string
 *                   example: ESG framework updated successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.put('/:id', validateRequest('updateFramework'), controllers.updateFramework);

/**
 * @swagger
 * /governance/esg/frameworks/{id}:
 *   delete:
 *     summary: Delete an ESG framework
 *     description: Deletes an existing ESG framework
 *     tags: [ESG Frameworks]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: ESG framework ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Framework deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: ESG framework deleted successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.delete('/:id', controllers.deleteFramework);

/**
 * @swagger
 * /governance/esg/frameworks/categories:
 *   get:
 *     summary: Get framework categories
 *     description: Returns a list of ESG framework categories
 *     tags: [ESG Frameworks]
 *     security:
 *       - ApiKeyAuth: []
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/FrameworkCategory'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/categories', controllers.getFrameworkCategories);

/**
 * @swagger
 * /governance/esg/frameworks/{id}/elements:
 *   get:
 *     summary: Get elements for a specific framework
 *     description: Returns a paginated list of elements for a specific ESG framework
 *     tags: [ESG Frameworks]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: ESG framework ID
 *         required: true
 *         schema:
 *           type: string
 *       - $ref: '#/components/parameters/page'
 *       - $ref: '#/components/parameters/limit'
 *       - $ref: '#/components/parameters/sortBy'
 *       - $ref: '#/components/parameters/sortOrder'
 *       - name: level
 *         in: query
 *         description: Filter by element level
 *         schema:
 *           type: integer
 *       - name: parentId
 *         in: query
 *         description: Filter by parent element ID (use 'null' for top-level elements)
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/FrameworkElement'
 *                 pagination:
 *                   $ref: '#/components/schemas/Pagination'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/:id/elements', controllers.getFrameworkElements);

/**
 * @swagger
 * /governance/esg/frameworks/elements/{id}:
 *   get:
 *     summary: Get a specific framework element
 *     description: Returns a specific framework element by ID
 *     tags: [ESG Frameworks]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Framework element ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/FrameworkElement'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/elements/:id', controllers.getElementById);

/**
 * @swagger
 * /governance/esg/frameworks/{frameworkId}/elements:
 *   post:
 *     summary: Create a new framework element
 *     description: Creates a new element for a specific ESG framework
 *     tags: [ESG Frameworks]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: frameworkId
 *         in: path
 *         description: ESG framework ID
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               code:
 *                 type: string
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               category:
 *                 type: string
 *               parentId:
 *                 type: string
 *               level:
 *                 type: integer
 *               metrics:
 *                 type: array
 *                 items:
 *                   type: string
 *             required:
 *               - code
 *               - name
 *               - description
 *               - level
 *     responses:
 *       201:
 *         description: Element created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/FrameworkElement'
 *                 message:
 *                   type: string
 *                   example: Framework element created successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/:frameworkId/elements', validateRequest('createElement'), controllers.createElement);

/**
 * @swagger
 * /governance/esg/frameworks/elements/{id}:
 *   put:
 *     summary: Update a framework element
 *     description: Updates an existing framework element
 *     tags: [ESG Frameworks]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Framework element ID
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               code:
 *                 type: string
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               category:
 *                 type: string
 *               parentId:
 *                 type: string
 *               level:
 *                 type: integer
 *               metrics:
 *                 type: array
 *                 items:
 *                   type: string
 *     responses:
 *       200:
 *         description: Element updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/FrameworkElement'
 *                 message:
 *                   type: string
 *                   example: Framework element updated successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.put('/elements/:id', validateRequest('updateElement'), controllers.updateElement);

/**
 * @swagger
 * /governance/esg/frameworks/elements/{id}:
 *   delete:
 *     summary: Delete a framework element
 *     description: Deletes an existing framework element
 *     tags: [ESG Frameworks]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Framework element ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Element deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Framework element deleted successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.delete('/elements/:id', controllers.deleteElement);

/**
 * @swagger
 * /governance/esg/frameworks/mappings:
 *   get:
 *     summary: Get framework mappings
 *     description: Returns a paginated list of framework mappings with optional filtering
 *     tags: [ESG Frameworks]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - $ref: '#/components/parameters/page'
 *       - $ref: '#/components/parameters/limit'
 *       - $ref: '#/components/parameters/sortBy'
 *       - $ref: '#/components/parameters/sortOrder'
 *       - name: sourceFrameworkId
 *         in: query
 *         description: Filter by source framework ID
 *         schema:
 *           type: string
 *       - name: targetFrameworkId
 *         in: query
 *         description: Filter by target framework ID
 *         schema:
 *           type: string
 *       - name: mappingType
 *         in: query
 *         description: Filter by mapping type
 *         schema:
 *           type: string
 *           enum: [exact, partial, related]
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/FrameworkMapping'
 *                 pagination:
 *                   $ref: '#/components/schemas/Pagination'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/mappings', controllers.getMappings);

/**
 * @swagger
 * /governance/esg/frameworks/mappings/{id}:
 *   get:
 *     summary: Get a specific framework mapping
 *     description: Returns a specific framework mapping by ID
 *     tags: [ESG Frameworks]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Framework mapping ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/FrameworkMapping'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/mappings/:id', controllers.getMappingById);

/**
 * @swagger
 * /governance/esg/frameworks/mappings:
 *   post:
 *     summary: Create a new framework mapping
 *     description: Creates a new mapping between framework elements
 *     tags: [ESG Frameworks]
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               sourceFrameworkId:
 *                 type: string
 *               sourceElementId:
 *                 type: string
 *               targetFrameworkId:
 *                 type: string
 *               targetElementId:
 *                 type: string
 *               mappingType:
 *                 type: string
 *                 enum: [exact, partial, related]
 *               notes:
 *                 type: string
 *             required:
 *               - sourceFrameworkId
 *               - sourceElementId
 *               - targetFrameworkId
 *               - targetElementId
 *               - mappingType
 *     responses:
 *       201:
 *         description: Mapping created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/FrameworkMapping'
 *                 message:
 *                   type: string
 *                   example: Framework mapping created successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/mappings', validateRequest('createMapping'), controllers.createMapping);

/**
 * @swagger
 * /governance/esg/frameworks/mappings/{id}:
 *   put:
 *     summary: Update a framework mapping
 *     description: Updates an existing framework mapping
 *     tags: [ESG Frameworks]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Framework mapping ID
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               mappingType:
 *                 type: string
 *                 enum: [exact, partial, related]
 *               notes:
 *                 type: string
 *     responses:
 *       200:
 *         description: Mapping updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/FrameworkMapping'
 *                 message:
 *                   type: string
 *                   example: Framework mapping updated successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.put('/mappings/:id', validateRequest('updateMapping'), controllers.updateMapping);

/**
 * @swagger
 * /governance/esg/frameworks/mappings/{id}:
 *   delete:
 *     summary: Delete a framework mapping
 *     description: Deletes an existing framework mapping
 *     tags: [ESG Frameworks]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Framework mapping ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Mapping deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Framework mapping deleted successfully
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.delete('/mappings/:id', controllers.deleteMapping);

/**
 * @swagger
 * /governance/esg/frameworks/compare/{sourceFrameworkId}/{targetFrameworkId}:
 *   get:
 *     summary: Compare two ESG frameworks
 *     description: Generates a crosswalk analysis between two frameworks, including coverage statistics, gap analysis, and recommendations
 *     tags: [ESG Frameworks]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: sourceFrameworkId
 *         in: path
 *         description: Source framework ID
 *         required: true
 *         schema:
 *           type: string
 *       - name: targetFrameworkId
 *         in: path
 *         description: Target framework ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: object
 *                   properties:
 *                     sourceFramework:
 *                       type: object
 *                       properties:
 *                         id:
 *                           type: string
 *                         code:
 *                           type: string
 *                         name:
 *                           type: string
 *                         version:
 *                           type: string
 *                         elementCount:
 *                           type: integer
 *                         mappedElementCount:
 *                           type: integer
 *                         coverage:
 *                           type: number
 *                     targetFramework:
 *                       type: object
 *                       properties:
 *                         id:
 *                           type: string
 *                         code:
 *                           type: string
 *                         name:
 *                           type: string
 *                         version:
 *                           type: string
 *                         elementCount:
 *                           type: integer
 *                         mappedElementCount:
 *                           type: integer
 *                         coverage:
 *                           type: number
 *                     mappingSummary:
 *                       type: object
 *                       properties:
 *                         totalMappings:
 *                           type: integer
 *                         byType:
 *                           type: array
 *                           items:
 *                             type: object
 *                             properties:
 *                               type:
 *                                 type: string
 *                               count:
 *                                 type: integer
 *                         averageConfidence:
 *                           type: number
 *                     gapAnalysis:
 *                       type: object
 *                       properties:
 *                         sourceGaps:
 *                           type: object
 *                           properties:
 *                             count:
 *                               type: integer
 *                             elements:
 *                               type: array
 *                               items:
 *                                 type: object
 *                                 properties:
 *                                   id:
 *                                     type: string
 *                                   code:
 *                                     type: string
 *                                   name:
 *                                     type: string
 *                                   description:
 *                                     type: string
 *                         targetGaps:
 *                           type: object
 *                           properties:
 *                             count:
 *                               type: integer
 *                             elements:
 *                               type: array
 *                               items:
 *                                 type: object
 *                                 properties:
 *                                   id:
 *                                     type: string
 *                                   code:
 *                                     type: string
 *                                   name:
 *                                     type: string
 *                                   description:
 *                                     type: string
 *                     recommendations:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           type:
 *                             type: string
 *                           description:
 *                             type: string
 *                           priority:
 *                             type: string
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/compare/:sourceFrameworkId/:targetFrameworkId', controllers.compareFrameworks);

module.exports = router;
