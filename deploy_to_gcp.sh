#!/bin/bash
# Deploy UUFT/CSDE tests to Google Cloud Platform

# Configuration
PROJECT_ID="novafuse-csde-testing"  # Replace with your GCP project ID
REGION="us-central1"
SERVICE_NAME="uuft-test"
IMAGE_NAME="gcr.io/${PROJECT_ID}/uuft-test"

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${GREEN}=== Deploying UUFT/CSDE Tests to GCP ===${NC}"
echo "Project: ${PROJECT_ID}"
echo "Region: ${REGION}"
echo "Service: ${SERVICE_NAME}"
echo "Image: ${IMAGE_NAME}"

# Check if gcloud is installed
if ! command -v gcloud &> /dev/null; then
    echo -e "${RED}Error: gcloud CLI not found. Please install Google Cloud SDK.${NC}"
    exit 1
fi

# Check if user is logged in
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" &> /dev/null; then
    echo -e "${YELLOW}You are not logged in to gcloud. Please login:${NC}"
    gcloud auth login
fi

# Set project
echo -e "${GREEN}Setting project...${NC}"
gcloud config set project ${PROJECT_ID}

# Enable required APIs
echo -e "${GREEN}Enabling required APIs...${NC}"
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable artifactregistry.googleapis.com
gcloud services enable cloudscheduler.googleapis.com

# Build and push Docker image
echo -e "${GREEN}Building and pushing Docker image...${NC}"
gcloud builds submit --tag ${IMAGE_NAME} .

# Deploy to Cloud Run
echo -e "${GREEN}Deploying to Cloud Run...${NC}"
gcloud run deploy ${SERVICE_NAME} \
  --image ${IMAGE_NAME} \
  --platform managed \
  --region ${REGION} \
  --memory 2Gi \
  --cpu 2 \
  --timeout 3600 \
  --no-allow-unauthenticated \
  --set-env-vars="GOOGLE_CLOUD_PROJECT=${PROJECT_ID},UUFT_ENVIRONMENT=gcp"

# Set up Cloud Scheduler for periodic testing (optional)
read -p "Do you want to set up a Cloud Scheduler job for periodic testing? (y/n) " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${GREEN}Setting up Cloud Scheduler...${NC}"
    
    # Enable Cloud Scheduler API
    gcloud services enable cloudscheduler.googleapis.com
    
    # Create service account for scheduler
    SA_NAME="uuft-scheduler"
    SA_EMAIL="${SA_NAME}@${PROJECT_ID}.iam.gserviceaccount.com"
    
    # Check if service account exists
    if ! gcloud iam service-accounts describe ${SA_EMAIL} &> /dev/null; then
        gcloud iam service-accounts create ${SA_NAME} \
            --display-name="UUFT Test Scheduler"
    fi
    
    # Grant invoker role to service account
    gcloud run services add-iam-policy-binding ${SERVICE_NAME} \
        --member="serviceAccount:${SA_EMAIL}" \
        --role="roles/run.invoker" \
        --region=${REGION}
    
    # Create scheduler job
    gcloud scheduler jobs create http uuft-daily-test \
        --schedule="0 0 * * *" \
        --uri="$(gcloud run services describe ${SERVICE_NAME} --region=${REGION} --format='value(status.url)')" \
        --http-method=POST \
        --oidc-service-account-email=${SA_EMAIL} \
        --oidc-token-audience="$(gcloud run services describe ${SERVICE_NAME} --region=${REGION} --format='value(status.url)')" \
        --message-body='{"experiment_id":"scheduled-daily-test"}'
    
    echo -e "${GREEN}Cloud Scheduler job created. Tests will run daily at midnight.${NC}"
fi

echo -e "${GREEN}Deployment complete!${NC}"
echo "You can manually trigger a test run with:"
echo "gcloud run services invoke ${SERVICE_NAME} --region=${REGION} --data='{}'"
echo
echo "View logs with:"
echo "gcloud logging read 'resource.type=cloud_run_revision AND resource.labels.service_name=${SERVICE_NAME}' --limit=50"
