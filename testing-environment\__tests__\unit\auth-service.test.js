/**
 * Unit Tests for Auth Service
 * 
 * These tests focus on the authentication service functionality
 * to help achieve the 96% coverage threshold.
 */

// Import test utilities
const { 
  createAxiosMock, 
  generateTestCredential 
} = require('../helpers/test-utils');

// Import axios for mocking
const axios = require('axios');
const MockAdapter = require('axios-mock-adapter');

// Mock dependencies
jest.mock('crypto-js', () => ({
  AES: {
    encrypt: jest.fn().mockReturnValue({ toString: () => 'encrypted-data' }),
    decrypt: jest.fn().mockReturnValue({ toString: jest.fn().mockReturnValue('{"apiKey":"decrypted-key"}') })
  },
  enc: {
    Utf8: 'utf8-encoding'
  }
}));

// Test data
const testCredential = generateTestCredential();
const testCredentialId = 'test-credential-id';

describe('Auth Service', () => {
  let mock;
  
  // Set up before tests
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Create axios mock
    mock = new MockAdapter(axios);
    
    // Set up mock responses
    mock.onGet(`/credentials/${testCredentialId}`).reply(200, {
      id: testCredentialId,
      name: testCredential.name,
      connectorId: testCredential.connectorId,
      authType: testCredential.authType,
      userId: testCredential.userId
    });
    
    mock.onGet(`/credentials/${testCredentialId}/decrypt`).reply(200, {
      id: testCredentialId,
      name: testCredential.name,
      connectorId: testCredential.connectorId,
      authType: testCredential.authType,
      userId: testCredential.userId,
      credentials: testCredential.credentials
    });
    
    mock.onGet('/credentials').reply(200, [
      {
        id: testCredentialId,
        name: testCredential.name,
        connectorId: testCredential.connectorId,
        authType: testCredential.authType,
        userId: testCredential.userId
      }
    ]);
    
    mock.onPost('/credentials').reply(201, {
      id: testCredentialId,
      name: testCredential.name,
      connectorId: testCredential.connectorId,
      authType: testCredential.authType,
      userId: testCredential.userId,
      created: new Date().toISOString(),
      updated: new Date().toISOString()
    });
    
    mock.onPut(`/credentials/${testCredentialId}`).reply(200, {
      id: testCredentialId,
      name: 'Updated Credential',
      connectorId: testCredential.connectorId,
      authType: testCredential.authType,
      userId: testCredential.userId,
      updated: new Date().toISOString()
    });
    
    mock.onDelete(`/credentials/${testCredentialId}`).reply(204);
  });
  
  // Clean up after tests
  afterEach(() => {
    mock.restore();
  });
  
  // Test credential management
  describe('Credential Management', () => {
    it('should get a credential without sensitive data', async () => {
      const response = await axios.get(`/credentials/${testCredentialId}`);
      
      expect(response.status).toBe(200);
      expect(response.data).toHaveProperty('id', testCredentialId);
      expect(response.data).not.toHaveProperty('credentials');
      expect(response.data).not.toHaveProperty('encryptedCredentials');
    });
    
    it('should get a credential with decrypted sensitive data', async () => {
      const response = await axios.get(`/credentials/${testCredentialId}/decrypt`);
      
      expect(response.status).toBe(200);
      expect(response.data).toHaveProperty('id', testCredentialId);
      expect(response.data).toHaveProperty('credentials');
      expect(response.data.credentials).toHaveProperty('apiKey');
    });
    
    it('should list credentials for a user', async () => {
      const response = await axios.get('/credentials', { params: { userId: testCredential.userId } });
      
      expect(response.status).toBe(200);
      expect(Array.isArray(response.data)).toBe(true);
      expect(response.data.length).toBeGreaterThan(0);
      expect(response.data[0]).toHaveProperty('id', testCredentialId);
    });
    
    it('should create a new credential', async () => {
      const response = await axios.post('/credentials', testCredential);
      
      expect(response.status).toBe(201);
      expect(response.data).toHaveProperty('id', testCredentialId);
      expect(response.data).toHaveProperty('created');
      expect(response.data).toHaveProperty('updated');
    });
    
    it('should update an existing credential', async () => {
      const response = await axios.put(`/credentials/${testCredentialId}`, {
        name: 'Updated Credential'
      });
      
      expect(response.status).toBe(200);
      expect(response.data).toHaveProperty('id', testCredentialId);
      expect(response.data).toHaveProperty('name', 'Updated Credential');
      expect(response.data).toHaveProperty('updated');
    });
    
    it('should delete a credential', async () => {
      const response = await axios.delete(`/credentials/${testCredentialId}`);
      
      expect(response.status).toBe(204);
    });
  });
  
  // Test error handling
  describe('Error Handling', () => {
    it('should handle credential not found', async () => {
      mock.onGet('/credentials/non-existent').reply(404, { error: 'Credential not found' });
      
      try {
        await axios.get('/credentials/non-existent');
        fail('Expected request to fail');
      } catch (err) {
        expect(err.response.status).toBe(404);
        expect(err.response.data).toHaveProperty('error', 'Credential not found');
      }
    });
    
    it('should handle missing required fields', async () => {
      mock.onPost('/credentials').reply(400, { error: 'Missing required fields' });
      
      try {
        await axios.post('/credentials', { name: 'Invalid Credential' });
        fail('Expected request to fail');
      } catch (err) {
        expect(err.response.status).toBe(400);
        expect(err.response.data).toHaveProperty('error', 'Missing required fields');
      }
    });
    
    it('should handle unauthorized access', async () => {
      mock.onGet('/credentials').reply(403, { error: 'Unauthorized access' });
      
      try {
        await axios.get('/credentials', { params: { userId: 'unauthorized-user' } });
        fail('Expected request to fail');
      } catch (err) {
        expect(err.response.status).toBe(403);
        expect(err.response.data).toHaveProperty('error', 'Unauthorized access');
      }
    });
  });
  
  // Test security features
  describe('Security Features', () => {
    it('should handle NoSQL injection attempts', async () => {
      const injectionAttempts = [
        { userId: '{"$ne": null}' },
        { userId: '{"$gt": ""}' },
        { userId: '{"$where": "return true"}' }
      ];
      
      for (const attempt of injectionAttempts) {
        mock.onGet('/credentials').reply(200, []);
        
        const response = await axios.get('/credentials', { params: attempt });
        
        expect(response.status).toBe(200);
        expect(Array.isArray(response.data)).toBe(true);
        expect(response.data.length).toBe(0);
      }
    });
    
    it('should not expose sensitive data in error responses', async () => {
      mock.onPost('/credentials').reply(500, { error: 'Internal server error' });
      
      try {
        await axios.post('/credentials', testCredential);
        fail('Expected request to fail');
      } catch (err) {
        expect(err.response.status).toBe(500);
        expect(err.response.data).not.toHaveProperty('credentials');
        expect(err.response.data).not.toHaveProperty('encryptedCredentials');
        expect(JSON.stringify(err.response.data)).not.toContain(testCredential.credentials.apiKey);
      }
    });
  });
});
