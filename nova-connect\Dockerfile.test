FROM node:18-alpine

WORKDIR /app

# Install necessary build tools
RUN apk add --no-cache python3 make g++

# Copy package.json and package-lock.json
COPY package*.json ./

# Install dependencies
RUN npm install

# Create necessary directories
RUN mkdir -p src/errors src/middleware src/utils src/monitoring public

# Copy source code
COPY src/ ./src/

# Copy public files
COPY public/ ./public/

# Copy test files
COPY test-monitoring.js ./

# Install additional dependencies
RUN npm install uuid winston

# Run the monitoring test script
CMD ["node", "test-monitoring.js"]
