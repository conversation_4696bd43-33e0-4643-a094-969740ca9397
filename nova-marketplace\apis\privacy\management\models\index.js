/**
 * Models Index
 *
 * This file exports all models for the Privacy Management API.
 */

const DataProcessingActivity = require('./dataProcessingActivity');
const SubjectRequest = require('./subjectRequest');
const ConsentRecord = require('./consentRecord');
const PrivacyNotice = require('./privacyNotice');
const DataBreach = require('./dataBreach');
const Integration = require('./integration');
const Notification = require('./notification');
const RegulatoryFramework = require('./RegulatoryFramework');
const ComplianceRequirement = require('./ComplianceRequirement');
const ComplianceStatus = require('./ComplianceStatus');

module.exports = {
  DataProcessingActivity,
  SubjectRequest,
  ConsentRecord,
  PrivacyNotice,
  DataBreach,
  Integration,
  Notification,
  RegulatoryFramework,
  ComplianceRequirement,
  ComplianceStatus
};
