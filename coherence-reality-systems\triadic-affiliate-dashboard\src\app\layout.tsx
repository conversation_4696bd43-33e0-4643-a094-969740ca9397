import type { Metadata } from 'next'
import './globals.css'
import { Clerk<PERSON>rov<PERSON> } from '@clerk/nextjs'
import { SignedIn, SignedOut, SignInButton, UserButton } from '@clerk/nextjs'
import { clerkConfig } from '@/config/dev'

export const metadata: Metadata = {
  title: 'Triadic Affiliate Dashboard',
  description: 'Consciousness-based affiliate marketing analytics',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <ClerkProvider 
      publishableKey={clerkConfig.publishableKey}
      signInUrl={clerkConfig.signInUrl}
      signUpUrl={clerkConfig.signUpUrl}
      afterSignInUrl={clerkConfig.afterSignInUrl}
      afterSignUpUrl={clerkConfig.afterSignUpUrl}
    >
      <html lang="en">
        <body className="bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">
          <div className="min-h-screen flex flex-col">
            <header className="bg-gradient-to-r from-purple-600 to-indigo-600 p-4 shadow-lg">
              <div className="container mx-auto px-4 flex justify-between items-center">
                <h1 className="text-3xl font-bold text-white">Triadic Affiliate Dashboard</h1>
                <div className="flex items-center gap-4">
                  <SignedOut>
                    <SignInButton />
                  </SignedOut>
                  <SignedIn>
                    <UserButton />
                  </SignedIn>
                </div>
              </div>
            </header>
            <main className="flex-1 container mx-auto px-4 py-8">
              {children}
            </main>
            <footer className="bg-gray-800 p-4">
              <div className="container mx-auto px-4">
                <p className="text-gray-400">© 2025 Triadic Systems. All rights reserved.</p>
              </div>
            </footer>
          </div>
        </body>
      </html>
    </ClerkProvider>
  )
}
