import { motion } from 'framer-motion'
import { useState, useEffect } from 'react'
import { useWebSocket } from '@/lib/useWebSocket'
import { auth } from '@clerk/nextjs'

interface Network {
  id: string
  name: string
  logo: string
  status: 'active' | 'inactive'
  earnings: number
  clicks: number
  conversions: number
  triadicMetrics: {
    psi: number
    phi: number
    kappa: number
  }
}

export function AffiliateNetworks() {
  const { userId } = auth()
  const { data, connected } = useWebSocket(userId || '')
  const [networks, setNetworks] = useState<Network[]>([])
  const [selectedNetwork, setSelectedNetwork] = useState<Network | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchNetworks = async () => {
      try {
        const response = await fetch('/api/networks')
        const data = await response.json()
        setNetworks(data)
      } catch (error) {
        console.error('Error fetching networks:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchNetworks()
  }, [])

  useEffect(() => {
    if (data?.type === 'network' && data.data) {
      setNetworks(prev => prev.map(network => 
        network.id === data.data.id ? data.data : network
      ))
    }
  }, [data])

  if (loading) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map(index => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="p-4 rounded-lg border bg-gray-50 animate-pulse"
          >
            <div className="h-8 w-32 bg-gray-200 rounded mb-4" />
            <div className="h-4 w-24 bg-gray-200 rounded" />
          </motion.div>
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-bold">Affiliate Networks</h2>
        <button
          onClick={() => window.location.href = '/networks/new'}
          className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
        >
          Add Network
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {networks.map((network) => (
          <motion.div
            key={network.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className={`p-4 rounded-lg border cursor-pointer hover:border-purple-500 transition-all ${
              selectedNetwork?.id === network.id ? 'border-purple-500' : 'border-white/10'
            }`}
            onClick={() => setSelectedNetwork(network)}
          >
            <div className="flex items-center space-x-4">
              <img
                src={network.logo}
                alt={network.name}
                className="w-12 h-12 rounded-lg"
              />
              <div>
                <h3 className="font-semibold">{network.name}</h3>
                <p className={`text-sm ${
                  network.status === 'active' ? 'text-green-500' : 'text-red-500'
                }`}>
                  Status: {network.status}
                </p>
              </div>
            </div>
            <div className="mt-4 space-y-2">
              <p className="text-sm text-gray-400">
                Earnings: ${network.earnings.toFixed(2)}
              </p>
              <p className="text-sm text-gray-400">
                Clicks: {network.clicks}
              </p>
              <p className="text-sm text-gray-400">
                Conversions: {network.conversions}
              </p>
              <div className="mt-2 space-x-2">
                <span className="px-2 py-1 bg-purple-100 text-purple-800 rounded-full text-xs">
                  PSI: {network.triadicMetrics.psi.toFixed(1)}%
                </span>
                <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">
                  PHI: {network.triadicMetrics.phi.toFixed(1)}%
                </span>
                <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">
                  KAPPA: {network.triadicMetrics.kappa.toFixed(1)}%
                </span>
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {selectedNetwork && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mt-6 p-4 bg-white/5 backdrop-blur-lg rounded-lg border border-white/10"
        >
          <h3 className="text-lg font-semibold mb-4">Network Details</h3>
          <div className="space-y-4">
            <div className="flex items-center space-x-4">
              <img
                src={selectedNetwork.logo}
                alt={selectedNetwork.name}
                className="w-24 h-24 rounded-lg"
              />
              <div>
                <h4 className="font-semibold">{selectedNetwork.name}</h4>
                <div className="mt-2 space-y-2">
                  <div className="flex items-center space-x-2">
                    <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">
                      Active
                    </span>
                    <span className="px-2 py-1 bg-purple-100 text-purple-800 rounded-full text-xs">
                      {selectedNetwork.conversions} Conversions
                    </span>
                  </div>
                  <p className="text-sm text-gray-400">
                    Total Earnings: ${selectedNetwork.earnings.toFixed(2)}
                  </p>
                  <p className="text-sm text-gray-400">
                    Clicks: {selectedNetwork.clicks}
                  </p>
                  <div className="mt-2">
                    <h4 className="text-sm font-semibold mb-2">Triadic Metrics</h4>
                    <div className="flex space-x-4">
                      <div className="flex-1">
                        <div className="w-16 h-16 rounded-full flex items-center justify-center bg-purple-500">
                          <span className="text-white">PSI</span>
                        </div>
                        <p className="text-sm text-center">{selectedNetwork.triadicMetrics.psi.toFixed(1)}%</p>
                      </div>
                      <div className="flex-1">
                        <div className="w-16 h-16 rounded-full flex items-center justify-center bg-blue-500">
                          <span className="text-white">PHI</span>
                        </div>
                        <p className="text-sm text-center">{selectedNetwork.triadicMetrics.phi.toFixed(1)}%</p>
                      </div>
                      <div className="flex-1">
                        <div className="w-16 h-16 rounded-full flex items-center justify-center bg-green-500">
                          <span className="text-white">KAPPA</span>
                        </div>
                        <p className="text-sm text-center">{selectedNetwork.triadicMetrics.kappa.toFixed(1)}%</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex justify-end">
              <button
                className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                onClick={() => setSelectedNetwork(null)}
              >
                Close Details
              </button>
            </div>
          </div>
        </motion.div>
      )}

      <div className="mt-4">
        <h4 className="text-sm font-semibold mb-2">WebSocket Status</h4>
        <div className={`px-2 py-1 rounded-full text-xs ${
          connected ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
        }`}>
          {connected ? 'Connected' : 'Disconnected'}
        </div>
      </div>
    </div>
  )
}
            <div className="flex items-center space-x-4">
              <img
                src={network.logo}
                alt={network.name}
                className="w-12 h-12 rounded-lg"
              />
              <div>
                <h3 className="font-semibold">{network.name}</h3>
                <p className={`text-sm ${
                  network.status === 'active' ? 'text-green-500' : 'text-red-500'
                }`}>
                  Status: {network.status}
                </p>
              </div>
            </div>
            <div className="mt-4 space-y-2">
              <p className="text-sm text-gray-400">
                Earnings: ${network.earnings.toFixed(2)}
              </p>
              <p className="text-sm text-gray-400">
                Clicks: {network.clicks}
              </p>
              <p className="text-sm text-gray-400">
                Conversions: {network.conversions}
              </p>
            </div>
          </motion.div>
        ))}
      </div>

      {selectedNetwork && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mt-6 p-4 bg-white/5 backdrop-blur-lg rounded-lg border border-white/10"
        >
          <h3 className="text-lg font-semibold mb-4">Network Details</h3>
          <div className="space-y-4">
            <div className="flex items-center space-x-4">
              <img
                src={selectedNetwork.logo}
                alt={selectedNetwork.name}
                className="w-24 h-24 rounded-lg"
              />
              <div>
                <h4 className="font-semibold">{selectedNetwork.name}</h4>
                <div className="mt-2 space-y-2">
                  <div className="flex items-center space-x-2">
                    <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">
                      Active
                    </span>
                    <span className="px-2 py-1 bg-purple-100 text-purple-800 rounded-full text-xs">
                      {selectedNetwork.conversions} Conversions
                    </span>
                  </div>
                  <p className="text-sm text-gray-400">
                    Total Earnings: ${selectedNetwork.earnings.toFixed(2)}
                  </p>
                </div>
              </div>
            </div>

            <div className="flex justify-end">
              <button
                className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                onClick={() => setSelectedNetwork(null)}
              >
                Close Details
              </button>
            </div>
          </div>
        </motion.div>
      )}
    </div>
  )
}
