/**
 * Error Middleware Tests
 * 
 * This file contains unit tests for the error handling middleware.
 */

const { errorHandler } = require('../../../middleware/error');
const logger = require('../../../config/logger');

// Mock the logger
jest.mock('../../../config/logger', () => ({
  error: jest.fn()
}));

describe('Error Middleware', () => {
  let req, res, next;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Setup request, response and next function
    req = {
      path: '/test',
      method: 'GET',
      user: { username: 'testuser' }
    };

    res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    };

    next = jest.fn();
  });

  it('should handle ValidationError', () => {
    // Setup error
    const error = new Error('Validation failed');
    error.name = 'ValidationError';
    error.errors = [{ field: 'name', message: 'Name is required' }];

    // Call the middleware
    errorHandler(error, req, res, next);

    // Verify the logger was called
    expect(logger.error).toHaveBeenCalledWith(expect.objectContaining({
      message: 'Validation failed',
      name: 'ValidationError',
      path: '/test',
      method: 'GET',
      user: 'testuser'
    }));

    // Verify the response
    expect(res.status).toHaveBeenCalledWith(400);
    expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
      error: 'ValidationError',
      message: 'Validation failed',
      errors: error.errors
    }));
  });

  it('should handle AuthenticationError', () => {
    // Setup error
    const error = new Error('Authentication required');
    error.name = 'AuthenticationError';

    // Call the middleware
    errorHandler(error, req, res, next);

    // Verify the logger was called
    expect(logger.error).toHaveBeenCalledWith(expect.objectContaining({
      message: 'Authentication required',
      name: 'AuthenticationError'
    }));

    // Verify the response
    expect(res.status).toHaveBeenCalledWith(401);
    expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
      error: 'Unauthorized',
      message: 'Authentication required'
    }));
  });

  it('should handle NotFoundError', () => {
    // Setup error
    const error = new Error('Resource not found');
    error.name = 'NotFoundError';

    // Call the middleware
    errorHandler(error, req, res, next);

    // Verify the logger was called
    expect(logger.error).toHaveBeenCalledWith(expect.objectContaining({
      message: 'Resource not found',
      name: 'NotFoundError'
    }));

    // Verify the response
    expect(res.status).toHaveBeenCalledWith(404);
    expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
      error: 'NotFound',
      message: 'Resource not found'
    }));
  });

  it('should handle ConflictError', () => {
    // Setup error
    const error = new Error('Resource already exists');
    error.name = 'ConflictError';

    // Call the middleware
    errorHandler(error, req, res, next);

    // Verify the logger was called
    expect(logger.error).toHaveBeenCalledWith(expect.objectContaining({
      message: 'Resource already exists',
      name: 'ConflictError'
    }));

    // Verify the response
    expect(res.status).toHaveBeenCalledWith(409);
    expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
      error: 'Conflict',
      message: 'Resource already exists'
    }));
  });

  it('should handle ForbiddenError', () => {
    // Setup error
    const error = new Error('Access denied');
    error.name = 'ForbiddenError';

    // Call the middleware
    errorHandler(error, req, res, next);

    // Verify the logger was called
    expect(logger.error).toHaveBeenCalledWith(expect.objectContaining({
      message: 'Access denied',
      name: 'ForbiddenError'
    }));

    // Verify the response
    expect(res.status).toHaveBeenCalledWith(403);
    expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
      error: 'Forbidden',
      message: 'Access denied'
    }));
  });

  it('should handle BadRequestError', () => {
    // Setup error
    const error = new Error('Bad request');
    error.name = 'BadRequestError';

    // Call the middleware
    errorHandler(error, req, res, next);

    // Verify the logger was called
    expect(logger.error).toHaveBeenCalledWith(expect.objectContaining({
      message: 'Bad request',
      name: 'BadRequestError'
    }));

    // Verify the response
    expect(res.status).toHaveBeenCalledWith(400);
    expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
      error: 'BadRequest',
      message: 'Bad request'
    }));
  });

  it('should handle RateLimitError', () => {
    // Setup error
    const error = new Error('Too many requests');
    error.name = 'RateLimitError';

    // Call the middleware
    errorHandler(error, req, res, next);

    // Verify the logger was called
    expect(logger.error).toHaveBeenCalledWith(expect.objectContaining({
      message: 'Too many requests',
      name: 'RateLimitError'
    }));

    // Verify the response
    expect(res.status).toHaveBeenCalledWith(429);
    expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
      error: 'TooManyRequests',
      message: 'Too many requests'
    }));
  });

  it('should handle default error case', () => {
    // Setup error
    const error = new Error('Unknown error');
    error.name = 'Error';

    // Call the middleware
    errorHandler(error, req, res, next);

    // Verify the logger was called
    expect(logger.error).toHaveBeenCalledWith(expect.objectContaining({
      message: 'Unknown error',
      name: 'Error'
    }));

    // Verify the response
    expect(res.status).toHaveBeenCalledWith(500);
    expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
      error: 'InternalServerError',
      message: 'An unexpected error occurred'
    }));
  });

  it('should handle error with custom status code', () => {
    // Setup error
    const error = new Error('Custom error');
    error.name = 'Error';
    error.status = 418; // I'm a teapot

    // Call the middleware
    errorHandler(error, req, res, next);

    // Verify the logger was called
    expect(logger.error).toHaveBeenCalledWith(expect.objectContaining({
      message: 'Custom error',
      name: 'Error',
      status: 418
    }));

    // Verify the response
    expect(res.status).toHaveBeenCalledWith(418);
    expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
      error: 'InternalServerError',
      message: 'An unexpected error occurred'
    }));
  });

  it('should handle anonymous user', () => {
    // Setup request without user
    req.user = null;

    // Setup error
    const error = new Error('Error without user');
    error.name = 'Error';

    // Call the middleware
    errorHandler(error, req, res, next);

    // Verify the logger was called with anonymous user
    expect(logger.error).toHaveBeenCalledWith(expect.objectContaining({
      message: 'Error without user',
      user: 'anonymous'
    }));
  });

  it('should handle ValidationError with default message', () => {
    // Setup error without message
    const error = new Error();
    error.name = 'ValidationError';
    error.message = '';

    // Call the middleware
    errorHandler(error, req, res, next);

    // Verify the response uses default message
    expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
      message: 'Validation failed'
    }));
  });

  it('should handle AuthenticationError with default message', () => {
    // Setup error without message
    const error = new Error();
    error.name = 'AuthenticationError';
    error.message = '';

    // Call the middleware
    errorHandler(error, req, res, next);

    // Verify the response uses default message
    expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
      message: 'Authentication required'
    }));
  });

  it('should handle NotFoundError with default message', () => {
    // Setup error without message
    const error = new Error();
    error.name = 'NotFoundError';
    error.message = '';

    // Call the middleware
    errorHandler(error, req, res, next);

    // Verify the response uses default message
    expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
      message: 'Resource not found'
    }));
  });
});
