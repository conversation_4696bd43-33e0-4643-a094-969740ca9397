/**
 * Transaction Entropy (Tᵋ)
 * 
 * This module implements the Transaction Entropy component of the CSFE.
 * It measures unpredictability in payment flows and transaction patterns.
 */

const { EventEmitter } = require('events');
const { performance } = require('perf_hooks');

/**
 * TransactionEntropy class
 */
class TransactionEntropy extends EventEmitter {
  /**
   * Create a new TransactionEntropy instance
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      updateInterval: 5000, // ms
      enableLogging: true,
      enableMetrics: true,
      historySize: 100, // Number of historical data points to keep
      thresholds: {
        volumeDeviation: {
          low: 0.1,
          medium: 0.2,
          high: 0.3,
          critical: 0.5
        },
        patternDeviation: {
          low: 0.1,
          medium: 0.2,
          high: 0.3,
          critical: 0.5
        },
        velocityDeviation: {
          low: 0.1,
          medium: 0.2,
          high: 0.3,
          critical: 0.5
        }
      },
      ...options
    };
    
    // Initialize state
    this.state = {
      transactionEntropy: 0.5,
      volumeDeviation: 0.0,
      patternDeviation: 0.0,
      velocityDeviation: 0.0,
      entropyHistory: [],
      transactionHistory: [],
      anomalies: [],
      isRunning: false,
      lastUpdateTime: Date.now()
    };
    
    // Initialize metrics
    this.metrics = {
      processingTimeMs: 0,
      totalUpdates: 0,
      anomaliesDetected: 0,
      transactionsProcessed: 0
    };
    
    // Initialize baseline models
    this.baselines = {
      volume: {
        daily: new Map(), // day of week -> average volume
        hourly: new Map(), // hour of day -> average volume
        total: 0 // overall average
      },
      pattern: {
        sourceDestPairs: new Map(), // source-dest -> frequency
        amounts: new Map() // amount range -> frequency
      },
      velocity: {
        transactionsPerMinute: 0,
        averageDelay: 0
      }
    };
    
    if (this.options.enableLogging) {
      console.log('TransactionEntropy initialized');
    }
  }
  
  /**
   * Start the transaction entropy monitor
   * @returns {boolean} - Success status
   */
  start() {
    if (this.state.isRunning) {
      if (this.options.enableLogging) {
        console.log('TransactionEntropy is already running');
      }
      return false;
    }
    
    this.state.isRunning = true;
    this._startUpdateInterval();
    
    if (this.options.enableLogging) {
      console.log('TransactionEntropy started');
    }
    
    this.emit('start');
    
    return true;
  }
  
  /**
   * Stop the transaction entropy monitor
   * @returns {boolean} - Success status
   */
  stop() {
    if (!this.state.isRunning) {
      if (this.options.enableLogging) {
        console.log('TransactionEntropy is not running');
      }
      return false;
    }
    
    this.state.isRunning = false;
    this._stopUpdateInterval();
    
    if (this.options.enableLogging) {
      console.log('TransactionEntropy stopped');
    }
    
    this.emit('stop');
    
    return true;
  }
  
  /**
   * Process a batch of transactions
   * @param {Array} transactions - Array of transaction objects
   * @returns {number} - Updated transaction entropy
   */
  processTransactions(transactions) {
    const startTime = performance.now();
    
    if (!Array.isArray(transactions)) {
      throw new Error('Transactions must be an array');
    }
    
    // Update transaction history
    this._updateTransactionHistory(transactions);
    
    // Update metrics
    this.metrics.transactionsProcessed += transactions.length;
    
    // Calculate deviations
    this._calculateVolumeDeviation(transactions);
    this._calculatePatternDeviation(transactions);
    this._calculateVelocityDeviation(transactions);
    
    // Update baselines
    this._updateBaselines(transactions);
    
    // Calculate transaction entropy
    this._calculateTransactionEntropy();
    
    // Detect anomalies
    this._detectAnomalies(transactions);
    
    // Update metrics
    this.metrics.processingTimeMs += performance.now() - startTime;
    this.metrics.totalUpdates++;
    
    // Emit update event
    this.emit('entropy-update', {
      transactionEntropy: this.state.transactionEntropy,
      volumeDeviation: this.state.volumeDeviation,
      patternDeviation: this.state.patternDeviation,
      velocityDeviation: this.state.velocityDeviation,
      timestamp: Date.now()
    });
    
    return this.state.transactionEntropy;
  }
  
  /**
   * Get current state
   * @returns {Object} - Current state
   */
  getState() {
    return { ...this.state };
  }
  
  /**
   * Get metrics
   * @returns {Object} - Metrics
   */
  getMetrics() {
    return { ...this.metrics };
  }
  
  /**
   * Get anomalies
   * @param {number} limit - Maximum number of anomalies to return
   * @returns {Array} - Detected anomalies
   */
  getAnomalies(limit = 10) {
    return this.state.anomalies.slice(0, limit);
  }
  
  /**
   * Get entropy history
   * @param {number} limit - Maximum number of history items to return
   * @returns {Array} - Entropy history
   */
  getEntropyHistory(limit = 10) {
    return this.state.entropyHistory.slice(0, limit);
  }
  
  /**
   * Update transaction history
   * @param {Array} transactions - New transactions
   * @private
   */
  _updateTransactionHistory(transactions) {
    // Add transactions to history
    this.state.transactionHistory.push(...transactions);
    
    // Limit history size
    if (this.state.transactionHistory.length > this.options.historySize) {
      this.state.transactionHistory = this.state.transactionHistory.slice(
        this.state.transactionHistory.length - this.options.historySize
      );
    }
  }
  
  /**
   * Calculate volume deviation
   * @param {Array} transactions - Transactions to analyze
   * @private
   */
  _calculateVolumeDeviation(transactions) {
    if (transactions.length === 0) {
      return;
    }
    
    // Calculate total volume
    const totalVolume = transactions.reduce((sum, tx) => sum + (tx.amount || 0), 0);
    
    // Get current time
    const now = new Date();
    const dayOfWeek = now.getDay();
    const hourOfDay = now.getHours();
    
    // Get baseline volumes
    const dailyBaseline = this.baselines.volume.daily.get(dayOfWeek) || totalVolume;
    const hourlyBaseline = this.baselines.volume.hourly.get(hourOfDay) || totalVolume;
    const overallBaseline = this.baselines.volume.total || totalVolume;
    
    // Calculate weighted baseline (18/82 principle)
    const weightedBaseline = (
      0.18 * overallBaseline +
      0.82 * ((dailyBaseline + hourlyBaseline) / 2)
    );
    
    // Calculate deviation
    const deviation = Math.abs(totalVolume - weightedBaseline) / weightedBaseline;
    
    // Update state
    this.state.volumeDeviation = this._clamp(deviation);
  }
  
  /**
   * Calculate pattern deviation
   * @param {Array} transactions - Transactions to analyze
   * @private
   */
  _calculatePatternDeviation(transactions) {
    if (transactions.length === 0) {
      return;
    }
    
    let patternDeviation = 0;
    let pairsAnalyzed = 0;
    
    // Analyze source-destination pairs
    for (const tx of transactions) {
      const sourceDestPair = `${tx.source || 'unknown'}-${tx.destination || 'unknown'}`;
      const baselineFrequency = this.baselines.pattern.sourceDestPairs.get(sourceDestPair) || 0;
      
      if (baselineFrequency > 0) {
        // Calculate deviation for this pair
        const pairDeviation = 1 - baselineFrequency;
        patternDeviation += pairDeviation;
        pairsAnalyzed++;
      }
    }
    
    // Calculate average deviation
    if (pairsAnalyzed > 0) {
      patternDeviation /= pairsAnalyzed;
    }
    
    // Update state
    this.state.patternDeviation = this._clamp(patternDeviation);
  }
  
  /**
   * Calculate velocity deviation
   * @param {Array} transactions - Transactions to analyze
   * @private
   */
  _calculateVelocityDeviation(transactions) {
    if (transactions.length === 0) {
      return;
    }
    
    // Calculate transactions per minute
    const timeSpan = (transactions[transactions.length - 1].timestamp - transactions[0].timestamp) / 60000; // in minutes
    const txPerMinute = timeSpan > 0 ? transactions.length / timeSpan : 0;
    
    // Calculate average delay between transactions
    let totalDelay = 0;
    for (let i = 1; i < transactions.length; i++) {
      totalDelay += transactions[i].timestamp - transactions[i - 1].timestamp;
    }
    const avgDelay = transactions.length > 1 ? totalDelay / (transactions.length - 1) : 0;
    
    // Calculate deviations
    const txPerMinuteDeviation = Math.abs(txPerMinute - this.baselines.velocity.transactionsPerMinute) / 
      (this.baselines.velocity.transactionsPerMinute || 1);
    
    const delayDeviation = Math.abs(avgDelay - this.baselines.velocity.averageDelay) / 
      (this.baselines.velocity.averageDelay || 1);
    
    // Calculate weighted deviation (18/82 principle)
    const velocityDeviation = (
      0.18 * txPerMinuteDeviation +
      0.82 * delayDeviation
    );
    
    // Update state
    this.state.velocityDeviation = this._clamp(velocityDeviation);
  }
  
  /**
   * Update baselines
   * @param {Array} transactions - Transactions to analyze
   * @private
   */
  _updateBaselines(transactions) {
    if (transactions.length === 0) {
      return;
    }
    
    // Update volume baselines
    const totalVolume = transactions.reduce((sum, tx) => sum + (tx.amount || 0), 0);
    const now = new Date();
    const dayOfWeek = now.getDay();
    const hourOfDay = now.getHours();
    
    // Update daily baseline
    const currentDailyBaseline = this.baselines.volume.daily.get(dayOfWeek) || 0;
    this.baselines.volume.daily.set(
      dayOfWeek,
      currentDailyBaseline === 0 ? totalVolume : (currentDailyBaseline * 0.9 + totalVolume * 0.1)
    );
    
    // Update hourly baseline
    const currentHourlyBaseline = this.baselines.volume.hourly.get(hourOfDay) || 0;
    this.baselines.volume.hourly.set(
      hourOfDay,
      currentHourlyBaseline === 0 ? totalVolume : (currentHourlyBaseline * 0.9 + totalVolume * 0.1)
    );
    
    // Update overall baseline
    this.baselines.volume.total = this.baselines.volume.total === 0 ? 
      totalVolume : (this.baselines.volume.total * 0.9 + totalVolume * 0.1);
    
    // Update pattern baselines
    for (const tx of transactions) {
      const sourceDestPair = `${tx.source || 'unknown'}-${tx.destination || 'unknown'}`;
      const currentFrequency = this.baselines.pattern.sourceDestPairs.get(sourceDestPair) || 0;
      this.baselines.pattern.sourceDestPairs.set(
        sourceDestPair,
        currentFrequency === 0 ? 0.1 : (currentFrequency * 0.9 + 0.1)
      );
      
      // Update amount range baseline
      const amountRange = this._getAmountRange(tx.amount || 0);
      const currentAmountFrequency = this.baselines.pattern.amounts.get(amountRange) || 0;
      this.baselines.pattern.amounts.set(
        amountRange,
        currentAmountFrequency === 0 ? 0.1 : (currentAmountFrequency * 0.9 + 0.1)
      );
    }
    
    // Update velocity baselines
    const timeSpan = (transactions[transactions.length - 1].timestamp - transactions[0].timestamp) / 60000; // in minutes
    const txPerMinute = timeSpan > 0 ? transactions.length / timeSpan : 0;
    
    let totalDelay = 0;
    for (let i = 1; i < transactions.length; i++) {
      totalDelay += transactions[i].timestamp - transactions[i - 1].timestamp;
    }
    const avgDelay = transactions.length > 1 ? totalDelay / (transactions.length - 1) : 0;
    
    // Update transactions per minute baseline
    this.baselines.velocity.transactionsPerMinute = this.baselines.velocity.transactionsPerMinute === 0 ?
      txPerMinute : (this.baselines.velocity.transactionsPerMinute * 0.9 + txPerMinute * 0.1);
    
    // Update average delay baseline
    this.baselines.velocity.averageDelay = this.baselines.velocity.averageDelay === 0 ?
      avgDelay : (this.baselines.velocity.averageDelay * 0.9 + avgDelay * 0.1);
  }
  
  /**
   * Calculate transaction entropy
   * @private
   */
  _calculateTransactionEntropy() {
    // Calculate transaction entropy using 18/82 principle
    const transactionEntropy = (
      0.18 * this.state.volumeDeviation +
      0.82 * ((this.state.patternDeviation + this.state.velocityDeviation) / 2)
    );
    
    // Update state
    this.state.transactionEntropy = this._clamp(transactionEntropy);
    
    // Add to history
    this.state.entropyHistory.push({
      transactionEntropy: this.state.transactionEntropy,
      volumeDeviation: this.state.volumeDeviation,
      patternDeviation: this.state.patternDeviation,
      velocityDeviation: this.state.velocityDeviation,
      timestamp: Date.now()
    });
    
    // Limit history size
    if (this.state.entropyHistory.length > this.options.historySize) {
      this.state.entropyHistory.shift();
    }
  }
  
  /**
   * Detect anomalies
   * @param {Array} transactions - Transactions to analyze
   * @private
   */
  _detectAnomalies(transactions) {
    const { thresholds } = this.options;
    const anomalies = [];
    
    // Check volume anomalies
    if (this.state.volumeDeviation >= thresholds.volumeDeviation.high) {
      anomalies.push({
        type: 'volume',
        severity: this.state.volumeDeviation >= thresholds.volumeDeviation.critical ? 'critical' : 'high',
        deviation: this.state.volumeDeviation,
        timestamp: Date.now(),
        description: `Unusual transaction volume detected (${this.state.volumeDeviation.toFixed(2)} deviation)`
      });
    }
    
    // Check pattern anomalies
    if (this.state.patternDeviation >= thresholds.patternDeviation.high) {
      anomalies.push({
        type: 'pattern',
        severity: this.state.patternDeviation >= thresholds.patternDeviation.critical ? 'critical' : 'high',
        deviation: this.state.patternDeviation,
        timestamp: Date.now(),
        description: `Unusual transaction pattern detected (${this.state.patternDeviation.toFixed(2)} deviation)`
      });
    }
    
    // Check velocity anomalies
    if (this.state.velocityDeviation >= thresholds.velocityDeviation.high) {
      anomalies.push({
        type: 'velocity',
        severity: this.state.velocityDeviation >= thresholds.velocityDeviation.critical ? 'critical' : 'high',
        deviation: this.state.velocityDeviation,
        timestamp: Date.now(),
        description: `Unusual transaction velocity detected (${this.state.velocityDeviation.toFixed(2)} deviation)`
      });
    }
    
    // Add anomalies to state
    if (anomalies.length > 0) {
      this.state.anomalies.push(...anomalies);
      
      // Limit anomalies size
      if (this.state.anomalies.length > this.options.historySize) {
        this.state.anomalies = this.state.anomalies.slice(
          this.state.anomalies.length - this.options.historySize
        );
      }
      
      // Update metrics
      this.metrics.anomaliesDetected += anomalies.length;
      
      // Emit anomaly events
      for (const anomaly of anomalies) {
        this.emit('anomaly', anomaly);
        
        if (this.options.enableLogging) {
          console.log(`TransactionEntropy: ${anomaly.description}`);
        }
      }
    }
  }
  
  /**
   * Get amount range
   * @param {number} amount - Transaction amount
   * @returns {string} - Amount range
   * @private
   */
  _getAmountRange(amount) {
    if (amount < 10) return '0-10';
    if (amount < 100) return '10-100';
    if (amount < 1000) return '100-1000';
    if (amount < 10000) return '1000-10000';
    if (amount < 100000) return '10000-100000';
    return '100000+';
  }
  
  /**
   * Start update interval
   * @private
   */
  _startUpdateInterval() {
    if (this._updateInterval) {
      clearInterval(this._updateInterval);
    }
    
    this._updateInterval = setInterval(() => {
      if (this.state.isRunning) {
        // In a real implementation, this would fetch real-time transaction data
        // For now, just simulate transaction data
        this._simulateTransactions();
      }
    }, this.options.updateInterval);
  }
  
  /**
   * Stop update interval
   * @private
   */
  _stopUpdateInterval() {
    if (this._updateInterval) {
      clearInterval(this._updateInterval);
      this._updateInterval = null;
    }
  }
  
  /**
   * Simulate transactions
   * @private
   */
  _simulateTransactions() {
    // Simulate a batch of transactions
    const transactions = [];
    const now = Date.now();
    
    // Generate 5-15 transactions
    const count = Math.floor(Math.random() * 10) + 5;
    
    for (let i = 0; i < count; i++) {
      transactions.push({
        id: `tx-${now}-${i}`,
        source: `account-${Math.floor(Math.random() * 10)}`,
        destination: `account-${Math.floor(Math.random() * 10)}`,
        amount: Math.random() * 1000,
        timestamp: now - (i * 1000) // 1 second between transactions
      });
    }
    
    // Process transactions
    this.processTransactions(transactions);
  }
  
  /**
   * Clamp value between 0 and 1
   * @param {number} value - Value to clamp
   * @returns {number} - Clamped value
   * @private
   */
  _clamp(value) {
    return Math.max(0, Math.min(1, value));
  }
}

module.exports = TransactionEntropy;
