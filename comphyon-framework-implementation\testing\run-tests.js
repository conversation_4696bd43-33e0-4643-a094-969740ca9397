/**
 * Comphyon Test Runner
 * 
 * This script runs all tests for the Comphyon framework.
 */

const { TestRunner } = require('./test-framework');
const { createIntegrationLayerTestSuite } = require('./integration-layer-tests');
const { createComphyonSystemTestSuite } = require('./comphyon-system-tests');

/**
 * Run all tests
 */
async function runTests() {
  console.log('=== Comphyon Framework Tests ===\n');
  
  // Create test runner
  const testRunner = new TestRunner({
    enableLogging: true,
    parallelSuites: false
  });
  
  // Add test suites
  testRunner.addSuite(createIntegrationLayerTestSuite());
  testRunner.addSuite(createComphyonSystemTestSuite());
  
  // Run tests
  try {
    const result = await testRunner.run();
    
    // Exit with appropriate code
    process.exit(result.failed > 0 ? 1 : 0);
  } catch (error) {
    console.error('Error running tests:', error);
    process.exit(1);
  }
}

// Run tests
runTests();
