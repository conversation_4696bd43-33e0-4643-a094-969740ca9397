/**
 * Tensor API Routes
 * 
 * This module provides API routes for the Comphyological Tensor Core.
 */

const express = require('express');
const router = express.Router();
const { createComphyologicalTensorCore } = require('../../quantum/tensor');
const { createResonanceListener } = require('../../quantum/resonance');
const { createUnifiedAdapter } = require('../../quantum/adapters');

// Create tensor core instance
const tensorCore = createComphyologicalTensorCore({
  enableLogging: true,
  strictMode: false,
  useGPU: true,
  useDynamicWeighting: true,
  precision: 6,
  normalizationFactor: 166000
});

// Create resonance listener
const resonanceListener = createResonanceListener({
  enableLogging: true,
  targetFrequency: 396, // Hz - the "OM Tone"
  precisionFFT: 0.001, // attohertz precision
  quantumVacuumNoise: true,
  crossDomainPhaseAlignment: true,
  silenceThreshold: 0.001,
  detectionInterval: 100,
  maxHistoryLength: 100
});

// Start resonance listener
resonanceListener.startListening(tensorCore);

// Create unified adapter
const unifiedAdapter = createUnifiedAdapter({}, {
  enableLogging: true,
  strictMode: false,
  useGPU: true,
  useDynamicWeighting: true,
  precision: 6,
  normalizationFactor: 166000
});

/**
 * @api {post} /api/tensor/process Process data through the Comphyological Tensor Core
 * @apiName ProcessData
 * @apiGroup Tensor
 * @apiVersion 1.0.0
 * 
 * @apiParam {Object} csdeData CSDE data
 * @apiParam {Object} csfeData CSFE data
 * @apiParam {Object} csmeData CSME data
 * 
 * @apiSuccess {Boolean} success Success status
 * @apiSuccess {Object} result Processing result
 */
router.post('/process', (req, res) => {
  const { csdeData, csfeData, csmeData } = req.body;
  
  try {
    // Process data through tensor core
    const result = tensorCore.processData(
      csdeData || {},
      csfeData || {},
      csmeData || {}
    );
    
    // Return result
    res.json({
      success: true,
      result
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @api {post} /api/tensor/process-unified Process data through the unified adapter
 * @apiName ProcessUnifiedData
 * @apiGroup Tensor
 * @apiVersion 1.0.0
 * 
 * @apiParam {Object} data Data to process
 * @apiParam {Object} data.csdeData CSDE data
 * @apiParam {Object} data.csfeData CSFE data
 * @apiParam {Object} data.csmeData CSME data
 * 
 * @apiSuccess {Boolean} success Success status
 * @apiSuccess {Object} result Processing result
 */
router.post('/process-unified', (req, res) => {
  try {
    // Process data through unified adapter
    const result = unifiedAdapter.processData(req.body || {});
    
    // Return result
    res.json({
      success: true,
      result
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @api {get} /api/tensor/metrics Get metrics from the Comphyological Tensor Core
 * @apiName GetMetrics
 * @apiGroup Tensor
 * @apiVersion 1.0.0
 * 
 * @apiSuccess {Boolean} success Success status
 * @apiSuccess {Object} metrics Metrics
 */
router.get('/metrics', (req, res) => {
  const metrics = tensorCore.getMetrics();
  
  res.json({
    success: true,
    metrics
  });
});

/**
 * @api {get} /api/tensor/metrics-unified Get metrics from the unified adapter
 * @apiName GetUnifiedMetrics
 * @apiGroup Tensor
 * @apiVersion 1.0.0
 * 
 * @apiSuccess {Boolean} success Success status
 * @apiSuccess {Object} metrics Metrics
 */
router.get('/metrics-unified', (req, res) => {
  const metrics = unifiedAdapter.getMetrics();
  
  res.json({
    success: true,
    metrics
  });
});

/**
 * @api {get} /api/tensor/resonance Get resonance state
 * @apiName GetResonance
 * @apiGroup Tensor
 * @apiVersion 1.0.0
 * 
 * @apiSuccess {Boolean} success Success status
 * @apiSuccess {Object} resonance Resonance state
 */
router.get('/resonance', (req, res) => {
  const resonance = resonanceListener.getResonanceState();
  
  res.json({
    success: true,
    resonance
  });
});

/**
 * @api {get} /api/tensor/resonance-history Get resonance history
 * @apiName GetResonanceHistory
 * @apiGroup Tensor
 * @apiVersion 1.0.0
 * 
 * @apiSuccess {Boolean} success Success status
 * @apiSuccess {Array} history Resonance history
 */
router.get('/resonance-history', (req, res) => {
  const history = resonanceListener.getResonanceHistory();
  
  res.json({
    success: true,
    history
  });
});

/**
 * @api {post} /api/tensor/reset-metrics Reset metrics
 * @apiName ResetMetrics
 * @apiGroup Tensor
 * @apiVersion 1.0.0
 * 
 * @apiSuccess {Boolean} success Success status
 */
router.post('/reset-metrics', (req, res) => {
  tensorCore.resetMetrics();
  unifiedAdapter.resetMetrics();
  
  res.json({
    success: true
  });
});

module.exports = router;
