/**
 * Mobile-Friendly Dashboard Example
 * 
 * This example demonstrates how to use the mobile-friendly components to create a responsive dashboard.
 */

import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import {
  DashboardCard,
  DataTable,
  GraphVisualization,
  MetricsCard,
  ChartCard,
  StatusIndicator,
  TabPanel,
  ResponsiveLayout,
  MobileMenu,
  TouchFriendlySlider,
  BottomNavigation
} from '../components';

/**
 * Mobile-Friendly Dashboard component
 * 
 * @param {Object} props - Component props
 * @param {Object} props.novaConnect - NovaConnect instance
 * @param {Object} props.novaShield - NovaShield instance
 * @param {Object} props.novaTrack - NovaTrack instance
 * @param {Object} props.novaDNA - NovaDNA instance
 * @param {Object} props.novaCore - NovaCore instance
 * @param {Object} props.novaPulse - NovaPulse instance
 * @param {Object} props.novaThink - NovaThink instance
 * @param {Object} props.novaGraph - NovaGraph instance
 * @param {Object} props.novaFlowX - NovaFlowX instance
 * @param {Object} props.novaProof - NovaProof instance
 * @param {Object} props.novaStore - NovaStore instance
 * @param {boolean} [props.enableLogging=false] - Whether to enable logging
 * @returns {React.ReactElement} Mobile-Friendly Dashboard component
 */
const MobileFriendlyDashboard = ({
  novaConnect,
  novaShield,
  novaTrack,
  novaDNA,
  novaCore,
  novaPulse,
  novaThink,
  novaGraph,
  novaFlowX,
  novaProof,
  novaStore,
  enableLogging = false
}) => {
  const [loading, setLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState(null);
  const [activeSection, setActiveSection] = useState('overview');
  const [isMobile, setIsMobile] = useState(false);
  
  // Check if device is mobile
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    // Initial check
    handleResize();
    
    // Add event listener
    window.addEventListener('resize', handleResize);
    
    // Cleanup
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);
  
  // Fetch dashboard data
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        if (enableLogging) {
          console.log('Fetching dashboard data...');
        }
        
        // Simulate API call to fetch dashboard data
        // In a real implementation, this would call the appropriate Nova components
        await new Promise(resolve => setTimeout(resolve, 1500));
        
        // Sample dashboard data
        const data = {
          complianceScore: 87,
          securityScore: 92,
          identityScore: 78,
          riskScore: 65,
          metrics: {
            totalConnectors: 24,
            activeConnectors: 18,
            failedConnectors: 2,
            pendingConnectors: 4
          },
          alerts: [
            { id: 'alert-001', severity: 'critical', type: 'compliance', message: 'GDPR compliance violation detected', timestamp: '2023-06-15T10:30:00Z', status: 'open' },
            { id: 'alert-002', severity: 'high', type: 'security', message: 'Unusual authentication pattern detected', timestamp: '2023-06-15T09:45:00Z', status: 'investigating' },
            { id: 'alert-003', severity: 'medium', type: 'identity', message: 'User access review pending', timestamp: '2023-06-14T16:20:00Z', status: 'open' },
            { id: 'alert-004', severity: 'low', type: 'compliance', message: 'Policy update required', timestamp: '2023-06-14T14:10:00Z', status: 'resolved' }
          ],
          recentConnections: [
            { id: 'conn-001', name: 'AWS S3', status: 'active', lastRun: '2023-06-15T11:00:00Z', duration: '45s', result: 'success' },
            { id: 'conn-002', name: 'Azure AD', status: 'active', lastRun: '2023-06-15T10:30:00Z', duration: '32s', result: 'success' },
            { id: 'conn-003', name: 'Salesforce', status: 'failed', lastRun: '2023-06-15T10:00:00Z', duration: '12s', result: 'error' },
            { id: 'conn-004', name: 'Google Workspace', status: 'active', lastRun: '2023-06-15T09:00:00Z', duration: '28s', result: 'success' }
          ],
          complianceChart: {
            labels: ['GDPR', 'HIPAA', 'PCI DSS', 'SOC 2', 'ISO 27001'],
            datasets: [
              {
                label: 'Compliance Score',
                data: [92, 85, 78, 90, 88],
                backgroundColor: 'rgba(54, 162, 235, 0.5)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 1
              }
            ]
          },
          securityChart: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            datasets: [
              {
                label: 'Threats Detected',
                data: [12, 19, 8, 15, 10, 7],
                backgroundColor: 'rgba(255, 99, 132, 0.5)',
                borderColor: 'rgba(255, 99, 132, 1)',
                borderWidth: 1
              },
              {
                label: 'Threats Remediated',
                data: [10, 15, 7, 12, 9, 5],
                backgroundColor: 'rgba(75, 192, 192, 0.5)',
                borderColor: 'rgba(75, 192, 192, 1)',
                borderWidth: 1
              }
            ]
          },
          riskMap: {
            nodes: [
              { id: 'n1', label: 'AWS S3', category: 'storage', riskScore: 25 },
              { id: 'n2', label: 'Azure AD', category: 'identity', riskScore: 15 },
              { id: 'n3', label: 'Salesforce', category: 'crm', riskScore: 40 },
              { id: 'n4', label: 'Google Workspace', category: 'productivity', riskScore: 20 },
              { id: 'n5', label: 'Customer Data', category: 'data', riskScore: 75 },
              { id: 'n6', label: 'Employee Data', category: 'data', riskScore: 60 },
              { id: 'n7', label: 'Payment Processing', category: 'finance', riskScore: 80 }
            ],
            edges: [
              { source: 'n1', target: 'n5', weight: 3 },
              { source: 'n1', target: 'n6', weight: 2 },
              { source: 'n2', target: 'n4', weight: 1 },
              { source: 'n2', target: 'n6', weight: 3 },
              { source: 'n3', target: 'n5', weight: 4 },
              { source: 'n4', target: 'n6', weight: 2 },
              { source: 'n5', target: 'n7', weight: 5 },
              { source: 'n6', target: 'n7', weight: 3 }
            ]
          }
        };
        
        setDashboardData(data);
        setLoading(false);
        
        if (enableLogging) {
          console.log('Dashboard data fetched successfully');
        }
      } catch (error) {
        console.error('Error fetching dashboard data', error);
        setLoading(false);
      }
    };
    
    fetchDashboardData();
  }, [enableLogging]);
  
  // Refresh dashboard data
  const handleRefresh = async () => {
    setLoading(true);
    
    try {
      if (enableLogging) {
        console.log('Refreshing dashboard data...');
      }
      
      // Simulate API call to refresh dashboard data
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Update dashboard data with new values
      setDashboardData(prevData => ({
        ...prevData,
        complianceScore: Math.floor(Math.random() * 20) + 80,
        securityScore: Math.floor(Math.random() * 20) + 80,
        identityScore: Math.floor(Math.random() * 20) + 70,
        riskScore: Math.floor(Math.random() * 30) + 60
      }));
      
      setLoading(false);
      
      if (enableLogging) {
        console.log('Dashboard data refreshed successfully');
      }
    } catch (error) {
      console.error('Error refreshing dashboard data', error);
      setLoading(false);
    }
  };
  
  // Render status indicator for alert severity
  const renderAlertSeverity = (severity) => {
    const severityMap = {
      critical: { status: 'error', label: 'Critical', pulse: true },
      high: { status: 'error', label: 'High' },
      medium: { status: 'warning', label: 'Medium' },
      low: { status: 'info', label: 'Low' }
    };
    
    const { status, label, pulse } = severityMap[severity] || { status: 'neutral', label: severity };
    
    return (
      <StatusIndicator
        status={status}
        label={isMobile ? undefined : label}
        pulse={pulse}
        size={isMobile ? 'sm' : 'md'}
      />
    );
  };
  
  // Render status indicator for connection status
  const renderConnectionStatus = (status, result) => {
    if (status === 'failed' || result === 'error') {
      return <StatusIndicator status="error" label={isMobile ? undefined : "Failed"} size={isMobile ? 'sm' : 'md'} />;
    } else if (status === 'active' && result === 'success') {
      return <StatusIndicator status="success" label={isMobile ? undefined : "Active"} size={isMobile ? 'sm' : 'md'} />;
    } else if (status === 'pending') {
      return <StatusIndicator status="pending" label={isMobile ? undefined : "Pending"} size={isMobile ? 'sm' : 'md'} />;
    } else {
      return <StatusIndicator status="neutral" label={isMobile ? undefined : status} size={isMobile ? 'sm' : 'md'} />;
    }
  };
  
  // Define navigation items for mobile menu and bottom navigation
  const navigationItems = [
    {
      id: 'overview',
      label: 'Overview',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
        </svg>
      ),
      onClick: () => setActiveSection('overview')
    },
    {
      id: 'metrics',
      label: 'Metrics',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
      ),
      onClick: () => setActiveSection('metrics')
    },
    {
      id: 'alerts',
      label: 'Alerts',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
        </svg>
      ),
      onClick: () => setActiveSection('alerts')
    },
    {
      id: 'connections',
      label: 'Connections',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
        </svg>
      ),
      onClick: () => setActiveSection('connections')
    },
    {
      id: 'risk-map',
      label: 'Risk Map',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
        </svg>
      ),
      onClick: () => setActiveSection('risk-map')
    }
  ];
  
  // Render section content based on active section
  const renderSectionContent = () => {
    switch (activeSection) {
      case 'overview':
        return (
          <div className="space-y-6">
            {/* Metrics Cards */}
            <ResponsiveLayout
              layouts={{
                xs: 1,
                sm: 1,
                md: 2,
                lg: 2,
                xl: 2
              }}
              gap={4}
            >
              <MetricsCard
                title="Compliance & Security"
                metrics={[
                  {
                    label: 'Compliance Score',
                    value: dashboardData?.complianceScore || 0,
                    suffix: '%',
                    trend: 5,
                    trendDirection: 'up',
                    color: 'text-blue-500'
                  },
                  {
                    label: 'Security Score',
                    value: dashboardData?.securityScore || 0,
                    suffix: '%',
                    trend: 2,
                    trendDirection: 'up',
                    color: 'text-green-500'
                  }
                ]}
                columns={isMobile ? 1 : 2}
                loading={loading}
              />
              
              <MetricsCard
                title="Identity & Risk"
                metrics={[
                  {
                    label: 'Identity Score',
                    value: dashboardData?.identityScore || 0,
                    suffix: '%',
                    trend: -3,
                    trendDirection: 'down',
                    color: 'text-purple-500'
                  },
                  {
                    label: 'Risk Score',
                    value: dashboardData?.riskScore || 0,
                    suffix: '%',
                    trend: 1,
                    trendDirection: 'up',
                    color: 'text-red-500'
                  }
                ]}
                columns={isMobile ? 1 : 2}
                loading={loading}
              />
            </ResponsiveLayout>
            
            {/* Charts */}
            <ResponsiveLayout
              layouts={{
                xs: 1,
                sm: 1,
                md: 2,
                lg: 2,
                xl: 2
              }}
              gap={4}
            >
              <ChartCard
                title="Compliance by Framework"
                chartType="radar"
                data={dashboardData?.complianceChart || { labels: [], datasets: [] }}
                loading={loading}
                collapsible={!isMobile}
                mobileCollapsible={true}
                onRefresh={handleRefresh}
              />
              
              <ChartCard
                title="Security Threats"
                chartType="bar"
                data={dashboardData?.securityChart || { labels: [], datasets: [] }}
                loading={loading}
                collapsible={!isMobile}
                mobileCollapsible={true}
                onRefresh={handleRefresh}
              />
            </ResponsiveLayout>
          </div>
        );
      
      case 'metrics':
        return (
          <div className="space-y-6">
            <MetricsCard
              title="Connector Statistics"
              metrics={[
                {
                  label: 'Total Connectors',
                  value: dashboardData?.metrics.totalConnectors || 0,
                  color: 'text-blue-500'
                },
                {
                  label: 'Active Connectors',
                  value: dashboardData?.metrics.activeConnectors || 0,
                  color: 'text-green-500'
                },
                {
                  label: 'Failed Connectors',
                  value: dashboardData?.metrics.failedConnectors || 0,
                  color: 'text-red-500'
                },
                {
                  label: 'Pending Connectors',
                  value: dashboardData?.metrics.pendingConnectors || 0,
                  color: 'text-yellow-500'
                }
              ]}
              columns={isMobile ? 2 : 4}
              loading={loading}
            />
            
            <TouchFriendlySlider
              items={[
                <ChartCard
                  title="Compliance by Framework"
                  chartType="radar"
                  data={dashboardData?.complianceChart || { labels: [], datasets: [] }}
                  loading={loading}
                  collapsible={false}
                  onRefresh={handleRefresh}
                />,
                <ChartCard
                  title="Security Threats"
                  chartType="bar"
                  data={dashboardData?.securityChart || { labels: [], datasets: [] }}
                  loading={loading}
                  collapsible={false}
                  onRefresh={handleRefresh}
                />
              ]}
              showDots={true}
              showArrows={true}
              autoPlay={false}
              loop={true}
              className="h-64 mb-4"
            />
          </div>
        );
      
      case 'alerts':
        return (
          <DashboardCard
            title="Recent Alerts"
            collapsible={!isMobile}
            mobileCollapsible={false}
            onRefresh={handleRefresh}
            loading={loading}
          >
            <DataTable
              columns={[
                { field: 'severity', header: 'Severity', render: (value) => renderAlertSeverity(value) },
                { field: 'type', header: 'Type', render: (value) => value.charAt(0).toUpperCase() + value.slice(1) },
                { field: 'message', header: 'Message' },
                ...(isMobile ? [] : [{ field: 'timestamp', header: 'Timestamp', render: (value) => new Date(value).toLocaleString() }]),
                { field: 'status', header: 'Status', render: (value) => (
                  <StatusIndicator
                    status={value === 'open' ? 'error' : value === 'investigating' ? 'warning' : 'success'}
                    label={isMobile ? undefined : value.charAt(0).toUpperCase() + value.slice(1)}
                    size={isMobile ? 'sm' : 'md'}
                  />
                )}
              ]}
              data={dashboardData?.alerts || []}
              loading={loading}
              emptyMessage="No alerts found"
            />
          </DashboardCard>
        );
      
      case 'connections':
        return (
          <DashboardCard
            title="Recent Connections"
            collapsible={!isMobile}
            mobileCollapsible={false}
            onRefresh={handleRefresh}
            loading={loading}
          >
            <DataTable
              columns={[
                { field: 'name', header: 'Connector' },
                { field: 'status', header: 'Status', render: (value, row) => renderConnectionStatus(value, row.result) },
                ...(isMobile ? [] : [{ field: 'lastRun', header: 'Last Run', render: (value) => new Date(value).toLocaleString() }]),
                ...(isMobile ? [] : [{ field: 'duration', header: 'Duration' }]),
                { field: 'result', header: 'Result', render: (value) => (
                  <StatusIndicator
                    status={value === 'success' ? 'success' : 'error'}
                    label={isMobile ? undefined : value.charAt(0).toUpperCase() + value.slice(1)}
                    size={isMobile ? 'sm' : 'md'}
                  />
                )}
              ]}
              data={dashboardData?.recentConnections || []}
              loading={loading}
              emptyMessage="No connections found"
            />
          </DashboardCard>
        );
      
      case 'risk-map':
        return (
          <DashboardCard
            title="Risk Map Visualization"
            collapsible={!isMobile}
            mobileCollapsible={false}
            onRefresh={handleRefresh}
            loading={loading}
          >
            <div className={`${isMobile ? 'h-[400px]' : 'h-[600px]'}`}>
              <GraphVisualization
                data={dashboardData?.riskMap || { nodes: [], edges: [] }}
                options={{
                  layout: 'force',
                  nodeSize: 'value',
                  nodeColor: 'category',
                  edgeWidth: 'value',
                  interactive: true,
                  zoomable: true,
                  draggable: true,
                  highlightNeighbors: true,
                  showLegend: !isMobile
                }}
                onNodeClick={(node) => {
                  if (enableLogging) {
                    console.log('Node clicked:', node);
                  }
                }}
              />
            </div>
          </DashboardCard>
        );
      
      default:
        return null;
    }
  };
  
  return (
    <div className="space-y-6 pb-20">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className={`${isMobile ? 'text-xl' : 'text-2xl'} font-bold text-gray-900`}>NovaFuse Dashboard</h1>
        
        {/* Desktop navigation */}
        {!isMobile && (
          <div className="flex space-x-4">
            {navigationItems.map(item => (
              <button
                key={item.id}
                className={`px-4 py-2 rounded-md transition-colors duration-200 ${
                  activeSection === item.id
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
                onClick={item.onClick}
              >
                {item.label}
              </button>
            ))}
            
            <button
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200"
              onClick={handleRefresh}
              disabled={loading}
            >
              {loading ? 'Refreshing...' : 'Refresh'}
            </button>
          </div>
        )}
        
        {/* Mobile menu */}
        {isMobile && (
          <div className="flex items-center space-x-2">
            <button
              className="px-3 py-1 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors duration-200"
              onClick={handleRefresh}
              disabled={loading}
            >
              {loading ? '...' : 'Refresh'}
            </button>
            
            <MobileMenu
              items={navigationItems}
              title="Dashboard Menu"
              activeItemId={activeSection}
              onItemClick={(item) => setActiveSection(item.id)}
              collapsible={true}
              defaultCollapsed={true}
            />
          </div>
        )}
      </div>
      
      {/* Section content */}
      {renderSectionContent()}
      
      {/* Bottom navigation for mobile */}
      <BottomNavigation
        items={navigationItems}
        activeItemId={activeSection}
        onItemClick={(item) => setActiveSection(item.id)}
        showLabels={true}
        showOnMobileOnly={true}
      />
    </div>
  );
};

MobileFriendlyDashboard.propTypes = {
  novaConnect: PropTypes.object,
  novaShield: PropTypes.object,
  novaTrack: PropTypes.object,
  novaDNA: PropTypes.object,
  novaCore: PropTypes.object,
  novaPulse: PropTypes.object,
  novaThink: PropTypes.object,
  novaGraph: PropTypes.object,
  novaFlowX: PropTypes.object,
  novaProof: PropTypes.object,
  novaStore: PropTypes.object,
  enableLogging: PropTypes.bool
};

export default MobileFriendlyDashboard;
