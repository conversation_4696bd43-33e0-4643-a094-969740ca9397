describe('Partner Empowerment Page', () => {
  beforeEach(() => {
    // Visit the partner empowerment page
    cy.visit('/partner-empowerment');
  });

  it('should display the partner empowerment page heading', () => {
    cy.get('h1').contains('Partner Empowerment').should('be.visible');
  });

  it('should display the partner benefits section', () => {
    cy.get('[data-testid="partner-benefits"]').should('be.visible');
    cy.contains('Up to 85% Revenue Share').should('be.visible');
    cy.contains('Accelerated Go-To-Market').should('be.visible');
    cy.contains('Differentiation Without Development Overhead').should('be.visible');
  });

  it('should display the partner program details', () => {
    cy.get('[data-testid="partner-program"]').should('be.visible');
  });

  it('should have a partner registration form', () => {
    cy.get('form').should('be.visible');
    cy.get('input[name="companyName"]').should('be.visible');
    cy.get('input[name="contactName"]').should('be.visible');
    cy.get('input[name="email"]').should('be.visible');
    cy.get('button').contains('Register').should('be.visible');
  });

  it('should have a white paper download section', () => {
    cy.get('[data-testid="white-paper"]').should('be.visible');
    cy.contains('Download White Paper').should('be.visible');
  });

  it('should have an implementation calculator', () => {
    cy.get('[data-testid="implementation-calculator"]').should('be.visible');
    
    // Test the calculator functionality
    cy.get('select[name="framework"]').select('PCI DSS');
    cy.get('input[name="controls"]').clear().type('300');
    cy.get('button').contains('Calculate').click();
    
    // Check that results are displayed
    cy.get('[data-testid="calculator-results"]').should('be.visible');
  });

  it('should display partner testimonials', () => {
    cy.get('[data-testid="partner-testimonials"]').should('be.visible');
  });

  it('should have a contact section', () => {
    cy.get('[data-testid="contact-section"]').should('be.visible');
    cy.contains('Contact Us').should('be.visible');
  });
});
