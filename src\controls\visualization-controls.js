/**
 * Visualization Controls
 * 
 * This module provides controls for manipulating visualizations.
 */

const ControlPanel = require('./control-panel');
const { WebSocketClient } = require('../websocket');

/**
 * VisualizationControls class
 */
class VisualizationControls {
  /**
   * Create a new VisualizationControls instance
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    this.options = {
      enableLogging: options.enableLogging !== undefined ? options.enableLogging : true,
      wsUrl: options.wsUrl || 'ws://localhost:3001/ws',
      controlPanel: options.controlPanel || null,
      ...options
    };
    
    // Create control panel if not provided
    if (!this.options.controlPanel) {
      this.controlPanel = new ControlPanel({
        enableLogging: this.options.enableLogging,
        wsUrl: this.options.wsUrl,
        autoConnect: false
      });
    } else {
      this.controlPanel = this.options.controlPanel;
    }
    
    // Create WebSocket client for visualization operations
    this.client = new WebSocketClient({
      url: this.options.wsUrl,
      clientId: `visualization-controls-${Date.now()}`,
      enableLogging: this.options.enableLogging,
      autoReconnect: true
    });
    
    // Initialize state
    this.state = {
      isConnected: false,
      visualizationTypes: [],
      visualizations: new Map(), // visualizationId -> visualization
      selectedVisualizationId: null,
      lastUpdate: Date.now()
    };
    
    // Set up event handlers
    this.client.on('connected', this._handleConnected.bind(this));
    this.client.on('disconnected', this._handleDisconnected.bind(this));
    this.client.on('error', this._handleError.bind(this));
    this.client.on('channel-message', this._handleChannelMessage.bind(this));
    
    // Set up control panel event handlers
    this.controlPanel.on('control-value-changed', this._handleControlValueChanged.bind(this));
    this.controlPanel.on('action-executed', this._handleActionExecuted.bind(this));
    
    if (this.options.enableLogging) {
      console.log('VisualizationControls initialized');
    }
  }
  
  /**
   * Connect to the WebSocket server
   * @returns {Promise<void>} - Promise that resolves when connected
   */
  async connect() {
    if (this.state.isConnected) {
      if (this.options.enableLogging) {
        console.log('VisualizationControls: Already connected');
      }
      return;
    }
    
    // Connect control panel
    if (!this.controlPanel.state.isConnected) {
      await this.controlPanel.connect();
    }
    
    // Connect WebSocket client
    await this.client.connect();
    
    // Subscribe to visualization channels
    await this.client.subscribe('visualization-updates');
    await this.client.subscribe('visualization-events');
    
    // Update state
    this.state.isConnected = true;
    this.state.lastUpdate = Date.now();
    
    // Register controls
    this._registerControls();
    
    // Get visualization types
    await this._fetchVisualizationTypes();
    
    // Get available visualizations
    await this._fetchVisualizations();
    
    if (this.options.enableLogging) {
      console.log('VisualizationControls: Connected');
    }
  }
  
  /**
   * Disconnect from the WebSocket server
   * @returns {Promise<void>} - Promise that resolves when disconnected
   */
  async disconnect() {
    if (!this.state.isConnected) {
      if (this.options.enableLogging) {
        console.log('VisualizationControls: Not connected');
      }
      return;
    }
    
    // Disconnect WebSocket client
    await this.client.disconnect();
    
    // Update state
    this.state.isConnected = false;
    this.state.lastUpdate = Date.now();
    
    if (this.options.enableLogging) {
      console.log('VisualizationControls: Disconnected');
    }
  }
  
  /**
   * Handle WebSocket connected event
   * @private
   */
  _handleConnected() {
    // Update state
    this.state.isConnected = true;
    this.state.lastUpdate = Date.now();
    
    if (this.options.enableLogging) {
      console.log('VisualizationControls: WebSocket connected');
    }
  }
  
  /**
   * Handle WebSocket disconnected event
   * @private
   */
  _handleDisconnected() {
    // Update state
    this.state.isConnected = false;
    this.state.lastUpdate = Date.now();
    
    if (this.options.enableLogging) {
      console.log('VisualizationControls: WebSocket disconnected');
    }
  }
  
  /**
   * Handle WebSocket error event
   * @param {Error} error - Error object
   * @private
   */
  _handleError(error) {
    if (this.options.enableLogging) {
      console.error('VisualizationControls: WebSocket error:', error);
    }
  }
  
  /**
   * Handle WebSocket channel message event
   * @param {Object} data - Message data
   * @private
   */
  _handleChannelMessage(data) {
    const { channel, data: messageData } = data;
    
    // Handle message based on channel
    switch (channel) {
      case 'visualization-updates':
        this._handleVisualizationUpdates(messageData);
        break;
        
      case 'visualization-events':
        this._handleVisualizationEvents(messageData);
        break;
    }
  }
  
  /**
   * Handle visualization updates
   * @param {Object} data - Update data
   * @private
   */
  _handleVisualizationUpdates(data) {
    if (!data || !data.id) {
      return;
    }
    
    // Update visualization
    this.state.visualizations.set(data.id, data.visualization);
    this.state.lastUpdate = Date.now();
    
    // Update visualization list control
    this._updateVisualizationListControl();
    
    // Update visualization details if this is the selected visualization
    if (this.state.selectedVisualizationId === data.id) {
      this._updateVisualizationDetailsControls(data.id);
    }
    
    if (this.options.enableLogging) {
      console.log(`VisualizationControls: Visualization updated - ${data.id}`);
    }
  }
  
  /**
   * Handle visualization events
   * @param {Object} data - Event data
   * @private
   */
  _handleVisualizationEvents(data) {
    if (!data || !data.event) {
      return;
    }
    
    // Handle event based on type
    switch (data.event) {
      case 'visualization-created':
        this._handleVisualizationCreated(data);
        break;
        
      case 'visualization-deleted':
        this._handleVisualizationDeleted(data);
        break;
    }
  }
  
  /**
   * Handle visualization created event
   * @param {Object} data - Event data
   * @private
   */
  _handleVisualizationCreated(data) {
    if (!data || !data.id) {
      return;
    }
    
    // Add visualization to list
    this.state.visualizations.set(data.id, data.visualization);
    this.state.lastUpdate = Date.now();
    
    // Update visualization list control
    this._updateVisualizationListControl();
    
    if (this.options.enableLogging) {
      console.log(`VisualizationControls: Visualization created - ${data.id}`);
    }
  }
  
  /**
   * Handle visualization deleted event
   * @param {Object} data - Event data
   * @private
   */
  _handleVisualizationDeleted(data) {
    if (!data || !data.id) {
      return;
    }
    
    // Remove visualization from list
    this.state.visualizations.delete(data.id);
    this.state.lastUpdate = Date.now();
    
    // Update visualization list control
    this._updateVisualizationListControl();
    
    // Clear selected visualization if it was deleted
    if (this.state.selectedVisualizationId === data.id) {
      this.state.selectedVisualizationId = null;
      this._clearVisualizationDetailsControls();
    }
    
    if (this.options.enableLogging) {
      console.log(`VisualizationControls: Visualization deleted - ${data.id}`);
    }
  }
  
  /**
   * Handle control value changed event
   * @param {Object} data - Event data
   * @private
   */
  _handleControlValueChanged(data) {
    const { controlId, value } = data;
    
    // Handle control value change based on control ID
    switch (controlId) {
      case 'visualization-selector':
        this._handleVisualizationSelected(value);
        break;
        
      case 'visualization-type':
        this._updateVisualizationOptionsControls(value);
        break;
        
      case 'render-quality':
      case 'show-axes':
      case 'show-grid':
      case 'rotation-speed':
      case 'color-scheme':
        // Update visualization if one is selected
        if (this.state.selectedVisualizationId) {
          this._updateVisualization(this.state.selectedVisualizationId);
        }
        break;
    }
  }
  
  /**
   * Handle action executed event
   * @param {Object} data - Event data
   * @private
   */
  _handleActionExecuted(data) {
    const { action, params } = data;
    
    // Handle action based on type
    switch (action) {
      case 'create-visualization':
        this._handleCreateVisualizationAction(params);
        break;
        
      case 'update-visualization':
        this._handleUpdateVisualizationAction(params);
        break;
        
      case 'delete-visualization':
        this._handleDeleteVisualizationAction(params);
        break;
    }
  }
  
  /**
   * Handle visualization selected
   * @param {string} visualizationId - Visualization ID
   * @private
   */
  _handleVisualizationSelected(visualizationId) {
    // Update selected visualization
    this.state.selectedVisualizationId = visualizationId;
    this.state.lastUpdate = Date.now();
    
    // Update visualization details controls
    this._updateVisualizationDetailsControls(visualizationId);
    
    if (this.options.enableLogging) {
      console.log(`VisualizationControls: Visualization selected - ${visualizationId}`);
    }
  }
  
  /**
   * Handle create visualization action
   * @param {Object} params - Action parameters
   * @private
   */
  async _handleCreateVisualizationAction(params) {
    if (!this.state.isConnected) {
      if (this.options.enableLogging) {
        console.log('VisualizationControls: Not connected');
      }
      return;
    }
    
    try {
      // Send create visualization message
      const response = await this.client.send({
        component: 'visualization',
        type: 'create-visualization',
        visualizationType: params.visualizationType,
        data: params.data,
        options: params.options
      });
      
      if (this.options.enableLogging) {
        console.log(`VisualizationControls: Visualization created - ${response.result.id}`);
      }
      
      // Add visualization to list
      if (response && response.result && response.result.visualization) {
        this.state.visualizations.set(response.result.id, response.result.visualization);
        this.state.lastUpdate = Date.now();
        
        // Update visualization list control
        this._updateVisualizationListControl();
        
        // Select the new visualization
        this.controlPanel.setControlValue('visualization-selector', response.result.id);
      }
    } catch (error) {
      if (this.options.enableLogging) {
        console.error('VisualizationControls: Error creating visualization:', error);
      }
    }
  }
  
  /**
   * Handle update visualization action
   * @param {Object} params - Action parameters
   * @private
   */
  async _handleUpdateVisualizationAction(params) {
    if (!this.state.isConnected) {
      if (this.options.enableLogging) {
        console.log('VisualizationControls: Not connected');
      }
      return;
    }
    
    const visualizationId = params.id || this.state.selectedVisualizationId;
    
    if (!visualizationId) {
      if (this.options.enableLogging) {
        console.log('VisualizationControls: No visualization selected');
      }
      return;
    }
    
    try {
      // Send update visualization message
      const response = await this.client.send({
        component: 'visualization',
        type: 'update-visualization',
        id: visualizationId,
        data: params.data
      });
      
      if (this.options.enableLogging) {
        console.log(`VisualizationControls: Visualization updated - ${visualizationId}`);
      }
      
      // Update visualization
      if (response && response.result && response.result.visualization) {
        this.state.visualizations.set(visualizationId, response.result.visualization);
        this.state.lastUpdate = Date.now();
        
        // Update visualization details controls
        this._updateVisualizationDetailsControls(visualizationId);
      }
    } catch (error) {
      if (this.options.enableLogging) {
        console.error(`VisualizationControls: Error updating visualization ${visualizationId}:`, error);
      }
    }
  }
  
  /**
   * Handle delete visualization action
   * @param {Object} params - Action parameters
   * @private
   */
  async _handleDeleteVisualizationAction(params) {
    if (!this.state.isConnected) {
      if (this.options.enableLogging) {
        console.log('VisualizationControls: Not connected');
      }
      return;
    }
    
    const visualizationId = params.id || this.state.selectedVisualizationId;
    
    if (!visualizationId) {
      if (this.options.enableLogging) {
        console.log('VisualizationControls: No visualization selected');
      }
      return;
    }
    
    try {
      // Send delete visualization message
      const response = await this.client.send({
        component: 'visualization',
        type: 'delete-visualization',
        id: visualizationId
      });
      
      if (this.options.enableLogging) {
        console.log(`VisualizationControls: Visualization deleted - ${visualizationId}`);
      }
      
      // Remove visualization from list
      if (response && response.result && response.result.success) {
        this.state.visualizations.delete(visualizationId);
        this.state.lastUpdate = Date.now();
        
        // Update visualization list control
        this._updateVisualizationListControl();
        
        // Clear selected visualization if it was deleted
        if (this.state.selectedVisualizationId === visualizationId) {
          this.state.selectedVisualizationId = null;
          this._clearVisualizationDetailsControls();
        }
      }
    } catch (error) {
      if (this.options.enableLogging) {
        console.error(`VisualizationControls: Error deleting visualization ${visualizationId}:`, error);
      }
    }
  }
  
  /**
   * Register controls
   * @private
   */
  _registerControls() {
    // Register visualization selector
    this.controlPanel.registerControl('visualization-selector', {
      type: 'select',
      label: 'Visualization',
      options: [],
      defaultValue: '',
      validate: (value) => typeof value === 'string'
    }, 'visualization-controls');
    
    // Register visualization type selector
    this.controlPanel.registerControl('visualization-type', {
      type: 'select',
      label: 'Visualization Type',
      options: [],
      defaultValue: '',
      validate: (value) => typeof value === 'string'
    }, 'visualization-creation');
    
    // Register visualization options controls
    this.controlPanel.registerControl('render-quality', {
      type: 'select',
      label: 'Render Quality',
      options: [
        { value: 'low', label: 'Low' },
        { value: 'medium', label: 'Medium' },
        { value: 'high', label: 'High' }
      ],
      defaultValue: 'medium',
      validate: (value) => ['low', 'medium', 'high'].includes(value)
    }, 'visualization-options');
    
    this.controlPanel.registerControl('show-axes', {
      type: 'checkbox',
      label: 'Show Axes',
      defaultValue: true,
      validate: (value) => typeof value === 'boolean'
    }, 'visualization-options');
    
    this.controlPanel.registerControl('show-grid', {
      type: 'checkbox',
      label: 'Show Grid',
      defaultValue: true,
      validate: (value) => typeof value === 'boolean'
    }, 'visualization-options');
    
    this.controlPanel.registerControl('rotation-speed', {
      type: 'slider',
      label: 'Rotation Speed',
      min: 0,
      max: 10,
      step: 0.1,
      defaultValue: 1,
      validate: (value) => typeof value === 'number' && value >= 0 && value <= 10
    }, 'visualization-options');
    
    this.controlPanel.registerControl('color-scheme', {
      type: 'select',
      label: 'Color Scheme',
      options: [
        { value: 'default', label: 'Default' },
        { value: 'rainbow', label: 'Rainbow' },
        { value: 'heatmap', label: 'Heat Map' },
        { value: 'grayscale', label: 'Grayscale' }
      ],
      defaultValue: 'default',
      validate: (value) => ['default', 'rainbow', 'heatmap', 'grayscale'].includes(value)
    }, 'visualization-options');
  }
  
  /**
   * Update visualization list control
   * @private
   */
  _updateVisualizationListControl() {
    // Get visualization IDs
    const visualizationIds = Array.from(this.state.visualizations.keys());
    
    // Update visualization selector options
    const visualizationSelector = this.controlPanel.getControl('visualization-selector');
    
    if (visualizationSelector) {
      visualizationSelector.options = visualizationIds.map((id) => ({
        value: id,
        label: id
      }));
      
      // Update control
      this.controlPanel.registerControl('visualization-selector', visualizationSelector, 'visualization-controls');
      
      // If no visualization is selected and there are visualizations available, select the first one
      if (!this.state.selectedVisualizationId && visualizationIds.length > 0) {
        this.controlPanel.setControlValue('visualization-selector', visualizationIds[0]);
      }
    }
  }
  
  /**
   * Update visualization details controls
   * @param {string} visualizationId - Visualization ID
   * @private
   */
  _updateVisualizationDetailsControls(visualizationId) {
    // Get visualization
    const visualization = this.state.visualizations.get(visualizationId);
    
    if (!visualization) {
      return;
    }
    
    // Get visualization options
    const options = visualization.options || {};
    
    // Update render quality
    if (options.renderMode) {
      this.controlPanel.setControlValue('render-quality', options.renderMode);
    }
    
    // Update show axes
    if (options.showAxes !== undefined) {
      this.controlPanel.setControlValue('show-axes', options.showAxes);
    }
    
    // Update show grid
    if (options.showGrid !== undefined) {
      this.controlPanel.setControlValue('show-grid', options.showGrid);
    }
    
    // Update rotation speed
    if (options.rotationSpeed !== undefined) {
      this.controlPanel.setControlValue('rotation-speed', options.rotationSpeed);
    }
    
    // Update color scheme
    if (options.colorScheme) {
      this.controlPanel.setControlValue('color-scheme', options.colorScheme);
    }
  }
  
  /**
   * Clear visualization details controls
   * @private
   */
  _clearVisualizationDetailsControls() {
    // Reset controls to default values
    this.controlPanel.setControlValue('render-quality', 'medium');
    this.controlPanel.setControlValue('show-axes', true);
    this.controlPanel.setControlValue('show-grid', true);
    this.controlPanel.setControlValue('rotation-speed', 1);
    this.controlPanel.setControlValue('color-scheme', 'default');
  }
  
  /**
   * Update visualization options controls based on visualization type
   * @param {string} visualizationType - Visualization type
   * @private
   */
  _updateVisualizationOptionsControls(visualizationType) {
    // Different visualization types might have different options
    // For now, we'll use the same options for all types
  }
  
  /**
   * Update a visualization
   * @param {string} visualizationId - Visualization ID
   * @private
   */
  async _updateVisualization(visualizationId) {
    // Get visualization
    const visualization = this.state.visualizations.get(visualizationId);
    
    if (!visualization) {
      return;
    }
    
    // Get control values
    const renderQuality = this.controlPanel.getControlValue('render-quality');
    const showAxes = this.controlPanel.getControlValue('show-axes');
    const showGrid = this.controlPanel.getControlValue('show-grid');
    const rotationSpeed = this.controlPanel.getControlValue('rotation-speed');
    const colorScheme = this.controlPanel.getControlValue('color-scheme');
    
    // Create updated visualization data
    const updatedData = {
      ...visualization.data,
      options: {
        renderMode: renderQuality,
        showAxes,
        showGrid,
        rotationSpeed,
        colorScheme
      }
    };
    
    // Update visualization
    await this.controlPanel.executeAction('update-visualization', {
      id: visualizationId,
      data: updatedData
    });
  }
  
  /**
   * Fetch visualization types from the server
   * @private
   */
  async _fetchVisualizationTypes() {
    if (!this.state.isConnected) {
      if (this.options.enableLogging) {
        console.log('VisualizationControls: Not connected');
      }
      return;
    }
    
    try {
      // Send get visualization types message
      const response = await this.client.send({
        component: 'visualization',
        type: 'get-visualization-types'
      });
      
      if (response && response.result && response.result.types) {
        // Update visualization types
        this.state.visualizationTypes = response.result.types;
        this.state.lastUpdate = Date.now();
        
        // Update visualization type selector
        const visualizationTypeSelector = this.controlPanel.getControl('visualization-type');
        
        if (visualizationTypeSelector) {
          visualizationTypeSelector.options = this.state.visualizationTypes.map((type) => ({
            value: type,
            label: type.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')
          }));
          
          // Update control
          this.controlPanel.registerControl('visualization-type', visualizationTypeSelector, 'visualization-creation');
          
          // Set default value if available
          if (this.state.visualizationTypes.length > 0) {
            this.controlPanel.setControlValue('visualization-type', this.state.visualizationTypes[0]);
          }
        }
      }
    } catch (error) {
      if (this.options.enableLogging) {
        console.error('VisualizationControls: Error fetching visualization types:', error);
      }
    }
  }
  
  /**
   * Fetch visualizations from the server
   * @private
   */
  async _fetchVisualizations() {
    if (!this.state.isConnected) {
      if (this.options.enableLogging) {
        console.log('VisualizationControls: Not connected');
      }
      return;
    }
    
    try {
      // TODO: Implement get-visualizations message to fetch all visualizations
      // For now, we'll just use the visualizations that are created during the session
    } catch (error) {
      if (this.options.enableLogging) {
        console.error('VisualizationControls: Error fetching visualizations:', error);
      }
    }
  }
  
  /**
   * Create a visualization
   * @param {string} visualizationType - Visualization type
   * @param {Object} data - Visualization data
   * @param {Object} [options] - Visualization options
   * @returns {Promise<Object>} - Promise that resolves with the created visualization
   */
  async createVisualization(visualizationType, data, options = {}) {
    return this.controlPanel.executeAction('create-visualization', {
      visualizationType,
      data,
      options
    });
  }
  
  /**
   * Update a visualization
   * @param {string} [id] - Visualization ID (uses selected visualization if not provided)
   * @param {Object} data - Updated visualization data
   * @returns {Promise<Object>} - Promise that resolves with the updated visualization
   */
  async updateVisualization(id, data) {
    return this.controlPanel.executeAction('update-visualization', {
      id: id || this.state.selectedVisualizationId,
      data
    });
  }
  
  /**
   * Delete a visualization
   * @param {string} [id] - Visualization ID (uses selected visualization if not provided)
   * @returns {Promise<Object>} - Promise that resolves with the result
   */
  async deleteVisualization(id) {
    return this.controlPanel.executeAction('delete-visualization', {
      id: id || this.state.selectedVisualizationId
    });
  }
}

module.exports = VisualizationControls;
