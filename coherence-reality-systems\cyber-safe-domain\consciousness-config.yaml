# Cyber-Safe Domain Consciousness Configuration
# Defines consciousness thresholds and coherence requirements for different security contexts

cyber_safe_domain:
  version: "1.0.0"
  description: "Consciousness-aligned security configuration"
  
  # Global consciousness thresholds
  global_thresholds:
    minimum_consciousness: 0.618  # Golden ratio baseline
    psi_snap_threshold: 0.82      # 82/18 Comphyological Model
    maximum_incoherence: 0.20     # Maximum allowed chaos
    
  # Domain-specific consciousness requirements
  domain_configurations:
    
    # Healthcare Domain (HIPAA, HITECH, FDA)
    healthcare:
      consciousness_threshold: 0.95
      coherence_requirement: 0.90
      audit_level: "comprehensive"
      encryption_minimum: "AES-256-GCM"
      access_logging: true
      data_minimization: true
      breach_notification: true
      
      # Medical device specific settings
      medical_devices:
        consciousness_threshold: 0.92
        real_time_monitoring: true
        emergency_override: true
        fda_validation_required: true
        
      # Patient data handling
      patient_data:
        consciousness_threshold: 0.95
        phi_protection: true
        minimum_necessary: true
        access_controls: "role_based"
        
    # Financial Services Domain (SOX, PCI-DSS, GDPR)
    financial:
      consciousness_threshold: 0.90
      coherence_requirement: 0.85
      audit_level: "detailed"
      encryption_minimum: "AES-256-GCM"
      transaction_validation: true
      fraud_detection: true
      
      # Payment processing
      payment_systems:
        consciousness_threshold: 0.95
        pci_compliance: true
        tokenization_required: true
        real_time_fraud_detection: true
        
      # Trading systems
      trading_platforms:
        consciousness_threshold: 0.93
        market_data_integrity: true
        order_validation: true
        regulatory_reporting: true
        
    # Government Domain (FedRAMP, FISMA, NIST)
    government:
      consciousness_threshold: 0.92
      coherence_requirement: 0.88
      audit_level: "comprehensive"
      encryption_minimum: "FIPS-140-2-Level-3"
      classification_handling: true
      insider_threat_protection: true
      
      # Classified systems
      classified:
        consciousness_threshold: 0.98
        clearance_validation: true
        compartmentalization: true
        need_to_know: true
        
      # Public services
      public_facing:
        consciousness_threshold: 0.85
        accessibility_compliance: true
        transparency_logging: true
        citizen_privacy: true
        
    # Enterprise Domain (SOC 2, ISO 27001, GDPR)
    enterprise:
      consciousness_threshold: 0.85
      coherence_requirement: 0.80
      audit_level: "standard"
      encryption_minimum: "AES-256-CBC"
      data_governance: true
      privacy_by_design: true
      
      # Executive systems
      c_suite:
        consciousness_threshold: 0.90
        executive_protection: true
        board_communications: true
        strategic_data_security: true
        
      # HR systems
      human_resources:
        consciousness_threshold: 0.88
        employee_privacy: true
        background_check_security: true
        performance_data_protection: true
        
      # Legal systems
      legal_compliance:
        consciousness_threshold: 0.92
        attorney_client_privilege: true
        litigation_hold: true
        regulatory_compliance: true
        
  # Network layer consciousness settings
  network_consciousness:
    
    # KetherNet routing consciousness
    routing:
      packet_validation: 0.82
      path_optimization: 0.75
      load_balancing: 0.70
      
    # API consciousness validation
    api_validation:
      request_consciousness: 0.80
      response_validation: 0.85
      rate_limiting: 0.75
      
    # Data transmission consciousness
    data_transmission:
      encryption_consciousness: 0.90
      integrity_validation: 0.88
      compression_optimization: 0.70
      
  # Application layer consciousness settings
  application_consciousness:
    
    # NovaBrowser consciousness
    browser:
      content_validation: 0.82
      ui_compliance: 0.85
      accessibility_enforcement: 0.90
      
    # NovaAgent consciousness
    endpoint:
      device_validation: 0.88
      process_monitoring: 0.85
      file_system_protection: 0.90
      
    # NovaLift consciousness
    integration:
      legacy_system_lifting: 0.75
      api_coherence: 0.80
      data_transformation: 0.85
      
  # Threat detection consciousness thresholds
  threat_detection:
    
    # Anomaly detection
    anomaly_detection:
      behavioral_analysis: 0.70
      pattern_recognition: 0.75
      statistical_deviation: 0.80
      
    # Malware detection
    malware_detection:
      signature_analysis: 0.85
      heuristic_analysis: 0.80
      behavioral_analysis: 0.88
      
    # Social engineering detection
    social_engineering:
      phishing_detection: 0.90
      pretexting_analysis: 0.85
      baiting_recognition: 0.88
      
  # Compliance consciousness mappings
  compliance_mappings:
    
    # Regulatory frameworks
    hipaa:
      minimum_consciousness: 0.95
      required_features: ["encryption", "access_logging", "audit_trails"]
      
    pci_dss:
      minimum_consciousness: 0.92
      required_features: ["tokenization", "network_segmentation", "vulnerability_scanning"]
      
    gdpr:
      minimum_consciousness: 0.88
      required_features: ["privacy_by_design", "data_minimization", "consent_management"]
      
    sox:
      minimum_consciousness: 0.90
      required_features: ["financial_controls", "audit_trails", "segregation_of_duties"]
      
    fedramp:
      minimum_consciousness: 0.95
      required_features: ["continuous_monitoring", "incident_response", "supply_chain_security"]
      
  # Performance optimization consciousness
  performance_optimization:
    
    # Resource allocation
    resource_allocation:
      cpu_consciousness: 0.75
      memory_consciousness: 0.80
      network_consciousness: 0.85
      
    # Caching strategies
    caching:
      cache_coherence: 0.80
      invalidation_consciousness: 0.75
      distribution_optimization: 0.85
      
    # Load balancing
    load_balancing:
      traffic_distribution: 0.75
      health_monitoring: 0.80
      failover_consciousness: 0.90
      
  # Monitoring and alerting consciousness
  monitoring:
    
    # Real-time monitoring
    real_time:
      metric_collection: 0.70
      alert_generation: 0.85
      dashboard_updates: 0.75
      
    # Audit logging
    audit_logging:
      event_capture: 0.90
      log_integrity: 0.95
      retention_management: 0.80
      
    # Incident response
    incident_response:
      detection_consciousness: 0.90
      response_automation: 0.85
      recovery_optimization: 0.88
      
  # Development and deployment consciousness
  development:
    
    # Secure development
    secure_development:
      code_consciousness: 0.85
      vulnerability_scanning: 0.90
      dependency_validation: 0.88
      
    # CI/CD consciousness
    cicd:
      build_validation: 0.80
      deployment_consciousness: 0.85
      rollback_capability: 0.90
      
    # Testing consciousness
    testing:
      unit_test_consciousness: 0.75
      integration_test_consciousness: 0.80
      security_test_consciousness: 0.90
