/**
 * NovaConnect - Universal API Connector
 * Cluster Mode Entry Point
 *
 * This file starts the NovaConnect UAC in cluster mode, utilizing all available CPU cores
 * for improved performance and reliability.
 */

require('dotenv').config();
const logger = require('./api/utils/logger');
const clusterService = require('./api/services/ClusterService');

/**
 * Main function
 */
async function main() {
  try {
    // Enable cluster mode
    process.env.CLUSTER_ENABLED = 'true';

    // Log startup
    logger.info('Starting NovaConnect UAC in cluster mode');

    // Initialize cluster service
    const initialized = await clusterService.initialize(() => {
      // Worker function - load the server
      require('./index');
    });

    if (initialized) {
      logger.info('Cluster mode initialized successfully');
    } else {
      logger.warn('Failed to initialize cluster mode, falling back to single process mode');
      require('./index');
    }
  } catch (error) {
    logger.error('Error initializing cluster mode', { error });
    process.exit(1);
  }
}

// Run main function
main();
