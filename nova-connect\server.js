/**
 * NovaConnect - Universal API Connector
 * Main Server Entry Point
 */

require('dotenv').config();
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const winston = require('winston');
const rateLimit = require('express-rate-limit');
const mongoose = require('mongoose');
const path = require('path');

// Import routes
const apiRoutes = require('./api/routes');
const metricsRoutes = require('./api/routes/metricsRoutes');
const featureRoutes = require('./api/routes/featureRoutes');
const optimizationRoutes = require('./api/routes/optimizationRoutes');
const errorDemoRoutes = require('./src/routes/error-demo');
const monitoringRoutes = require('./src/routes/monitoring-routes');
const connectorRoutes = require('./src/routes/connector-routes');
const performanceMetricsRoutes = require('./src/routes/metrics-routes');
const csdeDashboardRoutes = require('./src/routes/csde-dashboard-routes');
const csdeBatchRoutes = require('./src/routes/csde-batch-routes');
const csdeAdvancedRoutes = require('./src/routes/csde-advanced-routes');
const csdePerformanceRoutes = require('./src/routes/csde-performance-routes');

// Import middleware
const { metricsMiddleware, errorMetricsMiddleware } = require('./api/middleware/metricsMiddleware');
const { tracingMiddleware, tracingErrorMiddleware } = require('./api/middleware/tracingMiddleware');

// Import security middleware
const { auth, security, rateLimiter, sanitizer } = require('./src/middleware');

// Import enhanced monitoring middleware
const httpMetricsMiddleware = require('./src/monitoring/http-metrics-middleware');
const errorMetricsMiddleware2 = require('./src/monitoring/error-metrics-middleware');
const { tracingMiddleware: enhancedTracingMiddleware, tracingErrorMiddleware: enhancedTracingErrorMiddleware } = require('./src/monitoring/tracing-middleware');
const { requestLoggingMiddleware, errorLoggingMiddleware } = require('./src/monitoring/logging-middleware');
const { errorHandler: legacyErrorHandler, notFoundHandler: legacyNotFoundHandler, asyncHandler, retryHandler, circuitBreakerHandler, timeoutHandler } = require('./api/middleware/errorHandlingMiddleware');
const { errorHandler, notFoundHandler } = require('./src/middleware/error-handler');
const { asyncHandler: enhancedAsyncHandler } = require('./src/utils/async-handler');
const { clusterMiddleware, clusterHealth } = require('./api/middleware/clusterMiddleware');
const { cacheMiddleware, cacheClearMiddleware, cacheMetrics, cacheClear } = require('./api/middleware/cacheMiddleware');
const { requireFeature, checkFeatureLimit, getUserSubscription, getAvailableFeatures, getFeatureUsage, updateUserSubscription } = require('./api/middleware/featureFlagMiddleware');
const { configureSecurityMiddleware } = require('./api/middleware/securityMiddleware');
const { requestMonitoringMiddleware, metricsEndpoint, healthCheckEndpoint } = require('./api/middleware/monitoringMiddleware');

// Import services
const prometheusMetrics = require('./api/services/PrometheusMetricsService');
const googleCloudMonitoring = require('./api/services/GoogleCloudMonitoringService');
const tracingService = require('./api/services/TracingService');
const errorHandlingService = require('./api/services/ErrorHandlingService');
const clusterService = require('./api/services/ClusterService');
const cacheService = require('./api/services/CacheService');

// Create Express app
const app = express();
const PORT = process.env.PORT || 3001;

// Configure logger
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  defaultMeta: { service: 'nova-connect' },
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/combined.log' })
  ]
});

// Middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));
app.use(morgan('dev'));
app.use(express.static('public'));

// Configure security middleware
configureSecurityMiddleware(app);

// Add our enhanced security middleware
app.use(security.securityHeaders);
app.use(security.cors());
app.use(sanitizer.sanitize);
app.use(rateLimiter.rateLimiter({
  windowMs: 60 * 1000, // 1 minute
  max: 100 // 100 requests per minute
}));

// Add monitoring middleware
app.use(metricsMiddleware);
app.use(tracingMiddleware);
app.use(clusterMiddleware);
app.use(requestMonitoringMiddleware);

// Add enhanced monitoring middleware
app.use(httpMetricsMiddleware);
app.use(enhancedTracingMiddleware);
app.use(requestLoggingMiddleware);

// Rate limiting is now handled by the security middleware

// MongoDB connection
const connectToMongoDB = async () => {
  if (process.env.NODE_ENV === 'test') {
    logger.info('Skipping MongoDB connection in test environment');
    return;
  }

  try {
    const mongoOptions = {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
      family: 4
    };

    const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/nova-connect';

    await mongoose.connect(mongoURI, mongoOptions);
    logger.info('Connected to MongoDB successfully');

    mongoose.connection.on('error', (err) => {
      logger.error('MongoDB connection error:', { error: err.message });
    });

    mongoose.connection.on('disconnected', () => {
      logger.warn('MongoDB disconnected. Attempting to reconnect...');
    });

    mongoose.connection.on('reconnected', () => {
      logger.info('Reconnected to MongoDB');
    });

    process.on('SIGINT', async () => {
      try {
        await mongoose.connection.close();
        logger.info('MongoDB connection closed through app termination');
        process.exit(0);
      } catch (err) {
        logger.error('Error closing MongoDB connection:', { error: err.message });
        process.exit(1);
      }
    });

  } catch (err) {
    logger.error('Failed to connect to MongoDB:', { error: err.message });
  }
};

// Initialize MongoDB connection
connectToMongoDB();

// Routes
app.get('/', (req, res) => {
  res.json({
    name: 'NovaConnect',
    version: '1.0.0',
    description: 'Universal API Connector for seamless API integration',
    cluster: req.cluster ? req.cluster.info : { mode: 'single' },
    links: [
      { name: 'Error Handling Demo', url: '/error-demo.html' },
      { name: 'Monitoring Dashboard', url: '/monitoring-dashboard.html' },
      { name: 'Connector Management', url: '/connector-management' },
      { name: 'Health Check', url: '/health' },
      { name: 'Metrics', url: '/metrics' },
      { name: 'API Dashboard', url: '/monitoring/dashboard' },
      { name: 'CSDE Dashboard', url: '/csde/dashboard' },
      { name: 'CSDE Batch Processing', url: '/csde/batch' },
      { name: 'CSDE Advanced Features', url: '/csde/advanced' },
      { name: 'CSDE Performance', url: '/csde/performance' }
    ]
  });
});

// Health and monitoring endpoints
app.get('/health', healthCheckEndpoint);
app.get('/metrics', metricsEndpoint);

// Cluster health endpoint
app.get('/cluster/health', clusterHealth);

// Cache endpoints
app.get('/cache/metrics', cacheMetrics);
app.post('/cache/clear', cacheClear);

// Feature flag endpoints
app.get('/subscription', getUserSubscription);
app.get('/features', getAvailableFeatures);
app.get('/features/usage', getFeatureUsage);
app.put('/subscription/:userId', updateUserSubscription);

// API Routes
app.use('/api', cacheMiddleware({
  ttl: 300, // 5 minutes
  methods: ['GET'],
  shouldCache: (req) => {
    // Don't cache requests with query parameters that affect data
    const noCacheParams = ['refresh', 'nocache', 'timestamp'];
    return !noCacheParams.some(param => req.query[param]);
  }
}), apiRoutes);

// Feature Routes
app.use('/features', featureRoutes);

// Optimization Routes
app.use('/optimization', optimizationRoutes);

// Clear cache for POST, PUT, DELETE requests
app.use('/api', cacheClearMiddleware({
  getKeys: (req) => {
    // Clear all cache for the resource
    const path = req.path.split('/');
    const resource = path[1]; // First part after /api
    return [`GET:/api/${resource}`];
  },
  shouldClear: (req) => ['POST', 'PUT', 'DELETE'].includes(req.method)
}));

// Metrics Routes
app.use('/', metricsRoutes);

// Error Demo Routes
app.use('/error-demo', errorDemoRoutes);

// Monitoring Routes
app.use('/monitoring', monitoringRoutes);

// Connector Routes
app.use('/connectors', auth.apiKeyAuth, connectorRoutes);

// Performance Metrics Routes
app.use('/api/metrics', auth.apiKeyAuth, performanceMetricsRoutes);

// CSDE Dashboard Routes
app.use('/csde/dashboard', csdeDashboardRoutes.router);

// CSDE Batch Processing Routes
app.use('/csde/batch', csdeBatchRoutes.router);

// CSDE Advanced Features Routes
app.use('/csde/advanced', csdeAdvancedRoutes.router);

// CSDE Performance Routes
app.use('/csde/performance', csdePerformanceRoutes.router);

// Serve connector management page
app.get('/connector-management', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'connector-management.html'));
});

// Serve performance dashboard
app.get('/performance-dashboard', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'performance-dashboard.html'));
});

// Serve CSDE dashboard page
app.get('/csde/dashboard', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'csde-dashboard.html'));
});

// Serve CSDE batch processing page
app.get('/csde/batch', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'csde-batch.html'));
});

// Serve CSDE advanced features page
app.get('/csde/advanced', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'csde-advanced.html'));
});

// Serve CSDE performance page
app.get('/csde/performance', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'csde-performance.html'));
});

// Serve Compliance App Store static files
app.use('/compliance-store/static', express.static(path.join(__dirname, 'public', 'compliance-store')));

// Serve Compliance App Store
app.get('/compliance-store', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'compliance-store', 'index.html'));
});

// Test route for Compliance App Store
app.get('/compliance-store-test', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'compliance-store', 'test.html'));
});

// Apply circuit breaker to API routes
app.use('/api', circuitBreakerHandler({
  resource: 'api',
  failureThreshold: 5,
  resetTimeout: 30000
}));

// Apply timeout to API routes
app.use('/api', timeoutHandler({
  timeoutMs: 30000
}));

// Apply retry handler to API routes
app.use('/api', retryHandler({
  retryAfter: 1
}));

// 404 handler
app.use(notFoundHandler);

// Error tracking middleware
app.use(errorMetricsMiddleware);
app.use(tracingErrorMiddleware);

// Enhanced error tracking middleware
app.use(errorMetricsMiddleware2);
app.use(enhancedTracingErrorMiddleware);
app.use(errorLoggingMiddleware);

// Enhanced error handler
app.use(errorHandler);

// Start the server
const server = app.listen(PORT, () => {
  logger.info(`NovaConnect server running on port ${PORT}`);
  console.log(`NovaConnect server running on http://localhost:${PORT}`);

  // Log startup to Google Cloud if enabled
  if (googleCloudMonitoring.enabled) {
    googleCloudMonitoring.writeLog('INFO', 'NovaConnect server started', {
      port: PORT,
      environment: process.env.NODE_ENV || 'development',
      version: process.env.npm_package_version || '1.0.0'
    });

    // Create custom dashboard for NovaConnect
    googleCloudMonitoring.createDashboard('NovaConnect Dashboard', [
      'HTTP Request Rate',
      'Error Rate',
      'Response Time',
      'Connector Health'
    ]);

    // Create alert policies
    googleCloudMonitoring.createAlertPolicy(
      'High Error Rate',
      'novafuse_api_errors_total',
      'rate(5m) > 0.05',
      ['email']
    );

    googleCloudMonitoring.createAlertPolicy(
      'High Response Time',
      'novafuse_http_request_duration_seconds',
      'avg(5m) > 1.0',
      ['email']
    );
  }

  // Update active connections metric
  prometheusMetrics.updateActiveConnections(0);
});

// Track connections
server.on('connection', () => {
  const count = server.connections || 1;
  prometheusMetrics.updateActiveConnections(count);
});

// Handle graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');

  // Log shutdown to Google Cloud if enabled
  if (googleCloudMonitoring.enabled) {
    googleCloudMonitoring.writeLog('INFO', 'NovaConnect server shutting down', {
      reason: 'SIGTERM'
    });
  }

  server.close(() => {
    logger.info('Server closed');
    process.exit(0);
  });
});

module.exports = app;
