/**
 * Authentication Integration Tests
 * 
 * This file contains integration tests for the authentication routes.
 */

const request = require('supertest');
const app = require('../../server');
const authService = require('../../services/authService');

// Mock the authentication service
jest.mock('../../services/authService');

describe('Authentication Routes', () => {
  // Reset mocks before each test
  beforeEach(() => {
    jest.clearAllMocks();
  });
  
  describe('POST /auth/login', () => {
    it('should authenticate a user with valid credentials', async () => {
      // Mock the authenticate function to return a successful result
      authService.authenticate.mockResolvedValue({
        token: 'mock-token',
        expiresIn: '1h',
        user: {
          id: 'user-123',
          username: 'admin',
          role: 'admin'
        }
      });
      
      // Make a request to the login endpoint
      const response = await request(app)
        .post('/api/privacy/management/auth/login')
        .send({
          username: 'admin',
          password: 'password'
        });
      
      // Check that the authenticate function was called with the correct arguments
      expect(authService.authenticate).toHaveBeenCalledWith('admin', 'password');
      
      // Check the response
      expect(response.status).toBe(200);
      expect(response.body).toEqual({
        token: 'mock-token',
        expiresIn: '1h',
        user: {
          id: 'user-123',
          username: 'admin',
          role: 'admin'
        }
      });
    });
    
    it('should return a 400 error for missing credentials', async () => {
      // Make a request to the login endpoint with missing credentials
      const response = await request(app)
        .post('/api/privacy/management/auth/login')
        .send({});
      
      // Check that the authenticate function was not called
      expect(authService.authenticate).not.toHaveBeenCalled();
      
      // Check the response
      expect(response.status).toBe(400);
      expect(response.body).toEqual({
        error: 'ValidationError',
        message: 'Validation failed',
        errors: expect.arrayContaining([
          expect.objectContaining({
            field: 'username',
            message: expect.stringContaining('required')
          }),
          expect.objectContaining({
            field: 'password',
            message: expect.stringContaining('required')
          })
        ])
      });
    });
    
    it('should return a 401 error for invalid credentials', async () => {
      // Mock the authenticate function to throw an authentication error
      authService.authenticate.mockRejectedValue({
        name: 'AuthenticationError',
        message: 'Invalid credentials'
      });
      
      // Make a request to the login endpoint with invalid credentials
      const response = await request(app)
        .post('/api/privacy/management/auth/login')
        .send({
          username: 'admin',
          password: 'wrong-password'
        });
      
      // Check that the authenticate function was called with the correct arguments
      expect(authService.authenticate).toHaveBeenCalledWith('admin', 'wrong-password');
      
      // Check the response
      expect(response.status).toBe(401);
      expect(response.body).toEqual({
        error: 'Unauthorized',
        message: 'Invalid credentials'
      });
    });
  });
});
