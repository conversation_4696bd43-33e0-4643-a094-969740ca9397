/**
 * Utils Functions Tests
 *
 * This file contains unit tests for the dataUtils module.
 */

const dataUtils = require('../../utils/dataUtils');

describe('Utils Functions', () => {
  describe('advancedFilter', () => {
    const testItems = [
      { id: 1, name: 'Item 1', status: 'active', tags: ['tag1', 'tag2'], user: { name: '<PERSON>' } },
      { id: 2, name: 'Item 2', status: 'inactive', tags: ['tag2', 'tag3'], user: { name: '<PERSON>' } },
      { id: 3, name: 'Item 3', status: 'active', tags: ['tag1', 'tag3'], user: { name: 'Bob' } }
    ];

    it('should return all items when no filters are provided', () => {
      const result = dataUtils.advancedFilter(testItems, {});
      expect(result).toEqual(testItems);
    });

    it('should filter items based on exact match', () => {
      const result = dataUtils.advancedFilter(testItems, { status: 'active' }, { exactMatch: ['status'] });
      expect(result).toHaveLength(2);
      expect(result[0].id).toBe(1);
      expect(result[1].id).toBe(3);
    });

    it('should filter items based on nested properties', () => {
      const result = dataUtils.advancedFilter(testItems, { 'user.name': 'John' }, { exactMatch: ['user.name'] });
      expect(result).toHaveLength(1);
      expect(result[0].id).toBe(1);
    });

    it('should handle undefined or null filter values', () => {
      const result = dataUtils.advancedFilter(testItems, { status: undefined, name: null });
      expect(result).toEqual(testItems);
    });

    it('should handle undefined or null item values', () => {
      const itemsWithNull = [
        ...testItems,
        { id: 4, name: 'Item 4', status: null, tags: null, user: null }
      ];
      const result = dataUtils.advancedFilter(itemsWithNull, { 'user.name': 'John' }, { exactMatch: ['user.name'] });
      expect(result).toHaveLength(1);
      expect(result[0].id).toBe(1);
    });

    it('should handle exact match case insensitive', () => {
      const result = dataUtils.advancedFilter(testItems, { name: 'item 1' }, { exactMatchCaseInsensitive: ['name'] });
      expect(result).toHaveLength(1);
      expect(result[0].id).toBe(1);
    });

    it('should handle array contains filter', () => {
      const result = dataUtils.advancedFilter(testItems, { tags: 'tag1' }, { arrayContains: ['tags'] });
      expect(result).toHaveLength(2);
      expect(result[0].id).toBe(1);
      expect(result[1].id).toBe(3);
    });

    it('should handle array intersects filter', () => {
      const result = dataUtils.advancedFilter(testItems, { tags: ['tag1', 'tag4'] }, { arrayIntersects: ['tags'] });
      expect(result).toHaveLength(2);
      expect(result[0].id).toBe(1);
      expect(result[1].id).toBe(3);
    });

    it('should handle date range filter', () => {
      const itemsWithDates = [
        { id: 1, name: 'Item 1', createdAt: '2023-01-01' },
        { id: 2, name: 'Item 2', createdAt: '2023-02-01' },
        { id: 3, name: 'Item 3', createdAt: '2023-03-01' }
      ];

      // Test with from and to
      let result = dataUtils.advancedFilter(
        itemsWithDates,
        { createdAt: true },
        { dateRange: { createdAt: { from: '2023-01-15', to: '2023-02-15' } } }
      );
      expect(result).toHaveLength(1);
      expect(result[0].id).toBe(2);

      // Test with from only
      result = dataUtils.advancedFilter(
        itemsWithDates,
        { createdAt: true },
        { dateRange: { createdAt: { from: '2023-02-15' } } }
      );
      expect(result).toHaveLength(1);
      expect(result[0].id).toBe(3);

      // Test with to only
      result = dataUtils.advancedFilter(
        itemsWithDates,
        { createdAt: true },
        { dateRange: { createdAt: { to: '2023-01-15' } } }
      );
      expect(result).toHaveLength(1);
      expect(result[0].id).toBe(1);
    });

    it('should handle number range filter', () => {
      const itemsWithNumbers = [
        { id: 1, name: 'Item 1', price: 10 },
        { id: 2, name: 'Item 2', price: 20 },
        { id: 3, name: 'Item 3', price: 30 }
      ];

      // Test with min and max
      let result = dataUtils.advancedFilter(
        itemsWithNumbers,
        { price: true },
        { numberRange: { price: { min: 15, max: 25 } } }
      );
      expect(result).toHaveLength(1);
      expect(result[0].id).toBe(2);

      // Test with min only
      result = dataUtils.advancedFilter(
        itemsWithNumbers,
        { price: true },
        { numberRange: { price: { min: 25 } } }
      );
      expect(result).toHaveLength(1);
      expect(result[0].id).toBe(3);

      // Test with max only
      result = dataUtils.advancedFilter(
        itemsWithNumbers,
        { price: true },
        { numberRange: { price: { max: 15 } } }
      );
      expect(result).toHaveLength(1);
      expect(result[0].id).toBe(1);
    });

    it('should handle string partial match by default', () => {
      const result = dataUtils.advancedFilter(testItems, { name: 'Item' });
      expect(result).toHaveLength(3);
    });
  });

  describe('search', () => {
    const testItems = [
      { id: 1, name: 'Item 1', description: 'First item', tags: ['tag1', 'tag2'], user: { name: 'John' } },
      { id: 2, name: 'Item 2', description: 'Second item', tags: ['tag2', 'tag3'], user: { name: 'Jane' } },
      { id: 3, name: 'Item 3', description: 'Third item', tags: ['tag1', 'tag3'], user: { name: 'Bob' } },
      { id: 4, name: 'Test', description: 'Test item', tags: ['tag4'], user: { name: 'Alice' }, active: true, count: 42 }
    ];

    it('should return all items when no query is provided', () => {
      const result = dataUtils.search(testItems, '', ['name', 'description']);
      expect(result).toEqual(testItems);
    });

    it('should return all items when no fields are provided', () => {
      const result = dataUtils.search(testItems, 'item', []);
      expect(result).toEqual(testItems);
    });

    it('should search in specified fields', () => {
      const result = dataUtils.search(testItems, 'first', ['description']);
      expect(result).toHaveLength(1);
      expect(result[0].id).toBe(1);
    });

    it('should search in nested fields', () => {
      const result = dataUtils.search(testItems, 'john', ['user.name']);
      expect(result).toHaveLength(1);
      expect(result[0].id).toBe(1);
    });

    it('should handle array fields', () => {
      const result = dataUtils.search(testItems, 'tag1', ['tags']);
      expect(result).toHaveLength(2);
      expect(result[0].id).toBe(1);
      expect(result[1].id).toBe(3);
    });

    it('should handle number fields', () => {
      const result = dataUtils.search(testItems, '42', ['count']);
      expect(result).toHaveLength(1);
      expect(result[0].id).toBe(4);
    });

    it('should handle boolean fields', () => {
      const result = dataUtils.search(testItems, 'true', ['active']);
      expect(result).toHaveLength(1);
      expect(result[0].id).toBe(4);
    });

    it('should handle undefined or null item values', () => {
      const itemsWithNull = [
        ...testItems,
        { id: 5, name: 'Item 5', description: null, tags: null, user: null }
      ];
      const result = dataUtils.search(itemsWithNull, 'john', ['user.name']);
      expect(result).toHaveLength(1);
      expect(result[0].id).toBe(1);
    });
  });

  describe('sort', () => {
    const testItems = [
      { id: 3, name: 'C', createdAt: '2023-03-01', user: { name: 'Bob' } },
      { id: 1, name: 'A', createdAt: '2023-01-01', user: { name: 'John' } },
      { id: 2, name: 'B', createdAt: '2023-02-01', user: { name: 'Jane' } },
      { id: 4, name: 'D', createdAt: null, user: null }
    ];

    it('should sort by createdAt desc by default', () => {
      const result = dataUtils.sort(testItems);
      expect(result[0].id).toBe(3);
      expect(result[1].id).toBe(2);
      expect(result[2].id).toBe(1);
      expect(result[3].id).toBe(4);
    });

    it('should sort by specified field in ascending order', () => {
      const result = dataUtils.sort(testItems, 'name', 'asc');
      expect(result[0].id).toBe(1);
      expect(result[1].id).toBe(2);
      expect(result[2].id).toBe(3);
      expect(result[3].id).toBe(4);
    });

    it('should sort by specified field in descending order', () => {
      const result = dataUtils.sort(testItems, 'name', 'desc');
      expect(result[0].id).toBe(4);
      expect(result[1].id).toBe(3);
      expect(result[2].id).toBe(2);
      expect(result[3].id).toBe(1);
    });

    it('should sort by nested field', () => {
      const result = dataUtils.sort(testItems, 'user.name', 'asc');
      // Check that items with valid user.name values are sorted correctly
      const validItems = result.filter(item => item.user && item.user.name);
      expect(validItems[0].user.name).toBe('Bob');
      expect(validItems[1].user.name).toBe('Jane');
      expect(validItems[2].user.name).toBe('John');
      // Check that null items are at the end
      expect(result[result.length - 1].id).toBe(4);
    });

    it('should handle undefined values', () => {
      const result = dataUtils.sort(testItems, 'createdAt', 'asc');
      expect(result[0].id).toBe(1);
      expect(result[1].id).toBe(2);
      expect(result[2].id).toBe(3);
      expect(result[3].id).toBe(4);
    });

    it('should handle non-string values', () => {
      const itemsWithNumbers = [
        { id: 3, value: 30 },
        { id: 1, value: 10 },
        { id: 2, value: 20 }
      ];
      const result = dataUtils.sort(itemsWithNumbers, 'value', 'asc');
      expect(result[0].id).toBe(1);
      expect(result[1].id).toBe(2);
      expect(result[2].id).toBe(3);
    });

    it('should return items unchanged when sortBy is not provided', () => {
      const result = dataUtils.sort(testItems, null);
      expect(result).toEqual(testItems);
    });
  });

  describe('paginate', () => {
    const testItems = Array.from({ length: 25 }, (_, i) => ({ id: i + 1 }));

    it('should paginate with default values', () => {
      const result = dataUtils.paginate(testItems);
      expect(result.data).toHaveLength(10);
      expect(result.data[0].id).toBe(1);
      expect(result.pagination.total).toBe(25);
      expect(result.pagination.page).toBe(1);
      expect(result.pagination.limit).toBe(10);
      expect(result.pagination.pages).toBe(3);
      expect(result.pagination.next).toBe(2);
      expect(result.pagination.prev).toBe(null);
    });

    it('should paginate with custom page and limit', () => {
      const result = dataUtils.paginate(testItems, 2, 5);
      expect(result.data).toHaveLength(5);
      expect(result.data[0].id).toBe(6);
      expect(result.pagination.total).toBe(25);
      expect(result.pagination.page).toBe(2);
      expect(result.pagination.limit).toBe(5);
      expect(result.pagination.pages).toBe(5);
      expect(result.pagination.next).toBe(3);
      expect(result.pagination.prev).toBe(1);
    });

    it('should handle last page', () => {
      const result = dataUtils.paginate(testItems, 3, 10);
      expect(result.data).toHaveLength(5);
      expect(result.data[0].id).toBe(21);
      expect(result.pagination.total).toBe(25);
      expect(result.pagination.page).toBe(3);
      expect(result.pagination.limit).toBe(10);
      expect(result.pagination.pages).toBe(3);
      expect(result.pagination.next).toBe(null);
      expect(result.pagination.prev).toBe(2);
    });

    it('should limit to maximum of 100 items per page', () => {
      const largeItems = Array.from({ length: 200 }, (_, i) => ({ id: i + 1 }));
      const result = dataUtils.paginate(largeItems, 1, 150);
      expect(result.data).toHaveLength(100);
      expect(result.pagination.limit).toBe(100);
    });

    it('should throw error for invalid page', () => {
      expect(() => dataUtils.paginate(testItems, 0)).toThrow('Page must be greater than or equal to 1');
    });

    it('should throw error for invalid limit', () => {
      expect(() => dataUtils.paginate(testItems, 1, 0)).toThrow('Limit must be greater than or equal to 1');
    });
  });

  describe('cursorPaginate', () => {
    const testItems = Array.from({ length: 25 }, (_, i) => ({ id: i + 1, name: `Item ${i + 1}` }));

    it('should paginate with default values', () => {
      const result = dataUtils.cursorPaginate(testItems);
      expect(result.data).toHaveLength(10);
      expect(result.data[0].id).toBe(1);
      expect(result.pagination.limit).toBe(10);
      expect(result.pagination.nextCursor).toBe(10);
      expect(result.pagination.hasPrevious).toBe(false);
      expect(result.pagination.hasNext).toBe(true);
    });

    it('should paginate with cursor', () => {
      const result = dataUtils.cursorPaginate(testItems, 10, 5);
      expect(result.data).toHaveLength(5);
      expect(result.data[0].id).toBe(11);
      expect(result.pagination.limit).toBe(5);
      expect(result.pagination.nextCursor).toBe(15);
      expect(result.pagination.hasPrevious).toBe(true);
      expect(result.pagination.hasNext).toBe(true);
    });

    it('should handle last page', () => {
      const result = dataUtils.cursorPaginate(testItems, 20, 10);
      expect(result.data).toHaveLength(5);
      expect(result.data[0].id).toBe(21);
      expect(result.pagination.limit).toBe(10);
      expect(result.pagination.nextCursor).toBe(null);
      expect(result.pagination.hasPrevious).toBe(true);
      expect(result.pagination.hasNext).toBe(false);
    });

    it('should limit to maximum of 100 items per page', () => {
      const largeItems = Array.from({ length: 200 }, (_, i) => ({ id: i + 1 }));
      const result = dataUtils.cursorPaginate(largeItems, null, 150);
      expect(result.data).toHaveLength(100);
      expect(result.pagination.limit).toBe(100);
    });

    it('should throw error for invalid limit', () => {
      expect(() => dataUtils.cursorPaginate(testItems, null, 0)).toThrow('Limit must be greater than or equal to 1');
    });

    it('should handle custom cursor field', () => {
      const result = dataUtils.cursorPaginate(testItems, 'Item 10', 5, 'name');
      expect(result.data).toHaveLength(5);
      expect(result.data[0].id).toBe(11);
      expect(result.pagination.nextCursor).toBe('Item 15');
    });

    it('should handle reverse order', () => {
      const result = dataUtils.cursorPaginate(testItems, null, 5, 'id', true);
      expect(result.data).toHaveLength(5);
      expect(result.data[0].id).toBe(25);
      expect(result.data[4].id).toBe(21);
      expect(result.pagination.nextCursor).toBe(21);
    });
  });
});
