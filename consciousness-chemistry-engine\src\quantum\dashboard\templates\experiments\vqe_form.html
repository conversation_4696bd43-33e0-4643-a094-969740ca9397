<form id="vqe-form" onsubmit="submitVQEForm(event)">
    <input type="hidden" name="type" value="vqe">
    
    <div class="mb-3">
        <label for="vqe-name" class="form-label">Experiment Name</label>
        <input type="text" class="form-control" id="vqe-name" name="name" value="VQE Protein Folding" required>
    </div>
    
    <div class="row">
        <div class="col-md-6">
            <div class="mb-3">
                <label for="vqe-sequence" class="form-label">Protein Sequence</label>
                <input type="text" class="form-control" id="vqe-sequence" name="sequence" value="ACDEFGHIKLMNPQRSTVWY" required>
                <div class="form-text">Enter amino acid sequence (1-letter code)</div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="mb-3">
                <label for="vqe-backend" class="form-label">Quantum Backend</label>
                <select class="form-select" id="vqe-backend" name="quantum_backend" required>
                    <option value="qiskit">Qiskit (local simulator)</option>
                    <option value="pennylane">PennyLane</option>
                    <option value="braket">Amazon Braket</option>
                </select>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-4">
            <div class="mb-3">
                <label for="vqe-ansatz" class="form-label">Ansatz Type</label>
                <select class="form-select" id="vqe-ansatz" name="ansatz" required>
                    <option value="EfficientSU2">EfficientSU2</option>
                    <option value="TwoLocal">TwoLocal</option>
                    <option value="RealAmplitudes">RealAmplitudes</option>
                </select>
            </div>
        </div>
        <div class="col-md-4">
            <div class="mb-3">
                <label for="vqe-reps" class="form-label">Repetitions</label>
                <input type="number" class="form-control" id="vqe-reps" name="reps" min="1" max="10" value="2" required>
            </div>
        </div>
        <div class="col-md-4">
            <div class="mb-3">
                <label for="vqe-optimizer" class="form-label">Optimizer</label>
                <select class="form-select" id="vqe-optimizer" name="optimizer" required>
                    <option value="SPSA">SPSA</option>
                    <option value="COBYLA">COBYLA</option>
                    <option value="SLSQP">SLSQP</option>
                </select>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-6">
            <div class="mb-3">
                <label for="vqe-shots" class="form-label">Shots</label>
                <input type="number" class="form-control" id="vqe-shots" name="shots" min="100" step="100" value="1000" required>
            </div>
        </div>
        <div class="col-md-6">
            <div class="mb-3">
                <label for="vqe-maxiter" class="form-label">Max Iterations</label>
                <input type="number" class="form-control" id="vqe-maxiter" name="maxiter" min="10" max="1000" value="100" required>
            </div>
        </div>
    </div>
    
    <div class="mb-3">
        <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="vqe-include-rdkit" name="include_rdkit">
            <label class="form-check-label" for="vqe-include-rdkit">Include RDKit for 3D structure</label>
        </div>
        <div class="form-text">Enable to use RDKit for initial structure generation (requires RDKit installation)</div>
    </div>
    
    <div class="d-flex justify-content-end">
        <button type="button" class="btn btn-outline-secondary me-2" data-bs-dismiss="modal">Cancel</button>
        <button type="submit" class="btn btn-primary" id="vqe-submit">
            <span class="spinner-border spinner-border-sm d-none" id="vqe-submit-spinner" role="status" aria-hidden="true"></span>
            <span id="vqe-submit-text">Run Experiment</span>
        </button>
    </div>
</form>

<script>
function submitVQEForm(event) {
    event.preventDefault();
    
    const form = event.target;
    const submitButton = form.querySelector('#vqe-submit');
    const spinner = form.querySelector('#vqe-submit-spinner');
    const buttonText = form.querySelector('#vqe-submit-text');
    
    // Show loading state
    submitButton.disabled = true;
    spinner.classList.remove('d-none');
    buttonText.textContent = 'Running...';
    
    // Prepare form data
    const formData = new FormData(form);
    const data = Object.fromEntries(formData.entries());
    
    // Convert numeric fields to numbers and handle checkboxes
    data.reps = parseInt(data.reps);
    data.shots = parseInt(data.shots);
    data.maxiter = parseInt(data.maxiter);
    data.include_rdkit = form.querySelector('#vqe-include-rdkit').checked;
    
    // Submit via fetch
    fetch('/api/experiments', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => {
        if (!response.ok) {
            return response.json().then(err => { throw new Error(err.detail || 'Failed to start experiment'); });
        }
        return response.json();
    })
    .then(data => {
        // Close modal on success
        const modal = bootstrap.Modal.getInstance(document.getElementById('newExperimentModal'));
        modal.hide();
        
        // Show success message
        showAlert('VQE experiment started successfully!', 'success');
        
        // Reset form
        form.reset();
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert(`Error: ${error.message}`, 'danger');
    })
    .finally(() => {
        // Reset button state
        submitButton.disabled = false;
        spinner.classList.add('d-none');
        buttonText.textContent = 'Run Experiment';
    });
}

function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.role = 'alert';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    
    const container = document.querySelector('.main-content');
    container.prepend(alertDiv);
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        const alert = bootstrap.Alert.getOrCreateInstance(alertDiv);
        alert.close();
    }, 5000);
}
</script>
