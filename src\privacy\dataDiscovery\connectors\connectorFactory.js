/**
 * Connector Factory
 * Creates and returns appropriate data source connectors
 */

const BigQueryConnector = require('./bigQueryConnector');
const CloudStorageConnector = require('./cloudStorageConnector');
const MySQLConnector = require('./mySQLConnector');
const PostgreSQLConnector = require('./postgreSQLConnector');
const FirestoreConnector = require('./firestoreConnector');
const MongoDBConnector = require('./mongoDBConnector');
const RestApiConnector = require('./restApiConnector');

/**
 * Factory for creating data source connectors
 */
class ConnectorFactory {
  constructor() {
    this.connectors = {
      bigquery: BigQueryConnector,
      cloudstorage: CloudStorageConnector,
      mysql: MySQLConnector,
      postgresql: PostgreSQLConnector,
      firestore: FirestoreConnector,
      mongodb: MongoDBConnector,
      restapi: RestApiConnector
    };
  }

  /**
   * Get a connector for the specified data source type
   * @param {string} type - Type of data source
   * @returns {Object} Connector instance
   * @throws {Error} If connector type is not supported
   */
  getConnector(type) {
    const ConnectorClass = this.connectors[type.toLowerCase()];
    
    if (!ConnectorClass) {
      throw new Error(`Unsupported connector type: ${type}`);
    }
    
    return new ConnectorClass();
  }

  /**
   * Register a new connector type
   * @param {string} type - Type identifier for the connector
   * @param {Class} ConnectorClass - Connector class implementation
   */
  registerConnector(type, ConnectorClass) {
    this.connectors[type.toLowerCase()] = ConnectorClass;
  }

  /**
   * Get list of supported connector types
   * @returns {Array<string>} List of supported connector types
   */
  getSupportedConnectors() {
    return Object.keys(this.connectors);
  }
}

module.exports = new ConnectorFactory();
