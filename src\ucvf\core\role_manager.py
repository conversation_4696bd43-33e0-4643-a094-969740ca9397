"""
Role Manager for the Universal Compliance Visualization Framework.

This module provides functionality for managing stakeholder roles and their configurations.
"""

import os
import json
import logging
from typing import Dict, List, Any, Optional

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RoleManager:
    """
    Manager for stakeholder roles and their configurations.
    
    This class is responsible for loading, managing, and providing access to
    role configurations that define how visualizations should be tailored
    for different stakeholders.
    """
    
    def __init__(self, roles_file: Optional[str] = None):
        """
        Initialize the Role Manager.
        
        Args:
            roles_file: Path to a JSON file containing role configurations
        """
        logger.info("Initializing Role Manager")
        
        # Initialize the roles dictionary
        self.roles: Dict[str, Dict[str, Any]] = {}
        
        # Load default roles
        self._load_default_roles()
        
        # Load custom roles if provided
        if roles_file and os.path.exists(roles_file):
            self._load_roles_from_file(roles_file)
        
        logger.info(f"Role Manager initialized with {len(self.roles)} roles")
    
    def _load_default_roles(self) -> None:
        """Load the default stakeholder roles."""
        # Board of Directors role
        self.roles['board'] = {
            'name': 'Board of Directors',
            'description': 'High-level oversight of compliance posture and risk',
            'data_access_level': 'summary',
            'visualization_preferences': {
                'complexity': 'low',
                'detail_level': 'high_level',
                'focus_areas': ['risk', 'trends', 'financial_impact']
            }
        }
        
        # CISO role
        self.roles['ciso'] = {
            'name': 'Chief Information Security Officer',
            'description': 'Technical and strategic oversight of security and compliance',
            'data_access_level': 'detailed',
            'visualization_preferences': {
                'complexity': 'high',
                'detail_level': 'detailed',
                'focus_areas': ['security_controls', 'vulnerabilities', 'remediation']
            }
        }
        
        # Compliance Manager role
        self.roles['compliance_manager'] = {
            'name': 'Compliance Manager',
            'description': 'Day-to-day management of compliance activities',
            'data_access_level': 'detailed',
            'visualization_preferences': {
                'complexity': 'medium',
                'detail_level': 'detailed',
                'focus_areas': ['requirements', 'evidence', 'gaps']
            }
        }
        
        # IT Manager role
        self.roles['it_manager'] = {
            'name': 'IT Manager',
            'description': 'Management of IT systems and infrastructure',
            'data_access_level': 'detailed',
            'visualization_preferences': {
                'complexity': 'medium',
                'detail_level': 'detailed',
                'focus_areas': ['systems', 'configurations', 'patches']
            }
        }
        
        # Auditor role
        self.roles['auditor'] = {
            'name': 'Auditor',
            'description': 'Independent verification of compliance',
            'data_access_level': 'detailed',
            'visualization_preferences': {
                'complexity': 'high',
                'detail_level': 'detailed',
                'focus_areas': ['evidence', 'controls', 'testing']
            }
        }
    
    def _load_roles_from_file(self, file_path: str) -> None:
        """
        Load role configurations from a JSON file.
        
        Args:
            file_path: Path to the JSON file
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                custom_roles = json.load(f)
                
            for role_id, role_config in custom_roles.items():
                self.roles[role_id] = role_config
                
            logger.info(f"Loaded {len(custom_roles)} custom roles from {file_path}")
        except Exception as e:
            logger.error(f"Failed to load roles from {file_path}: {e}")
    
    def get_role_config(self, role: str) -> Dict[str, Any]:
        """
        Get the configuration for a specific role.
        
        Args:
            role: The role identifier
            
        Returns:
            The role configuration
            
        Raises:
            ValueError: If the role does not exist
        """
        if role not in self.roles:
            logger.warning(f"Role '{role}' not found, using default role 'compliance_manager'")
            return self.roles['compliance_manager']
        
        return self.roles[role]
    
    def get_available_roles(self) -> List[str]:
        """
        Get the list of available roles.
        
        Returns:
            A list of role identifiers
        """
        return list(self.roles.keys())
    
    def register_role(self, role: str, config: Dict[str, Any]) -> None:
        """
        Register a new role or update an existing one.
        
        Args:
            role: The role identifier
            config: The role configuration
        """
        self.roles[role] = config
        logger.info(f"Registered role '{role}'")
    
    def save_roles_to_file(self, file_path: str) -> None:
        """
        Save the current role configurations to a JSON file.
        
        Args:
            file_path: Path to the output JSON file
        """
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.roles, f, indent=2)
                
            logger.info(f"Saved {len(self.roles)} roles to {file_path}")
        except Exception as e:
            logger.error(f"Failed to save roles to {file_path}: {e}")
