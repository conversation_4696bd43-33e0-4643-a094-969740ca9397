# NovaFuse API Superstore Architecture

This document provides an overview of the NovaFuse API Superstore architecture and explains how to use the platform.

## Architecture Overview

The NovaFuse API Superstore is built using a microservices architecture with the following components:

### 1. Kong API Gateway

Kong serves as the central API Gateway for the NovaFuse API Superstore. It handles:
- Routing requests to the appropriate backend services
- Authentication via API keys
- Rate limiting
- Usage tracking

<PERSON> is configured to route requests with specific prefixes to the corresponding services:
- `/governance/*` → Governance API
- `/security/*` → Security API
- `/apis/*` → APIs API

### 2. Mock API Services

The platform includes three mock API services that simulate different categories of GRC APIs:

#### Governance API
- Provides endpoints for board meetings, policies, compliance reports, and board resolutions
- Accessible via `/governance` routes through Kong

#### Security API
- Provides endpoints for vulnerabilities, security policies, security incidents, and security scans
- Accessible via `/security` routes through Kong

#### APIs API
- Provides endpoints for API catalog, metrics, integration flows, API keys, and developer resources
- Accessible via `/apis` routes through Kong

### 3. Marketplace UI

A web-based user interface for browsing and interacting with the available APIs. The UI is built with:
- Next.js
- React
- Tailwind CSS (with a dark blue theme)
- Firebase integration for authentication and data storage

### 4. Documentation Portal

Swagger UI-based documentation portal that provides interactive API documentation.

### 5. Connector Templates

Templates for different types of API integrations that partners can use to connect their services to the NovaFuse platform.

## How to Use the API Superstore

### Starting the Platform

1. Start all services using the provided script:
   ```
   .\start.bat
   ```

2. This will start all Docker containers and configure Kong API Gateway.

3. Access the components:
   - API Gateway: http://localhost:8000
   - Kong Admin API: http://localhost:8001
   - Konga Admin UI: http://localhost:1337
   - Marketplace UI: http://localhost:3000
   - Documentation Portal: http://localhost:8889

### Stopping the Platform

To stop all services, run:
```
.\stop.bat
```

### Testing the APIs

You can test the APIs using the provided test script:
```
.\test-api.ps1
```

This will test all three API categories through the Kong API Gateway.

### Authentication

All APIs require authentication using an API key. The default test API key is `test-api-key`.

Example API request:
```
Invoke-RestMethod -Uri "http://localhost:8000/governance/board/meetings" -Headers @{apikey = "test-api-key"}
```

### Available API Endpoints

#### Governance API
- `/governance/board/meetings` - List board meetings
- `/governance/board/meetings/{id}` - Get a specific board meeting
- `/governance/policies` - List policies
- `/governance/policies/{id}` - Get a specific policy
- `/governance/compliance/reports` - List compliance reports
- `/governance/compliance/reports/{id}` - Get a specific compliance report
- `/governance/board/resolutions` - List board resolutions
- `/governance/board/resolutions/{id}` - Get a specific board resolution

#### Security API
- `/security/vulnerabilities` - List vulnerabilities
- `/security/vulnerabilities/{id}` - Get a specific vulnerability
- `/security/policies` - List security policies
- `/security/policies/{id}` - Get a specific security policy
- `/security/incidents` - List security incidents
- `/security/incidents/{id}` - Get a specific security incident
- `/security/scan` - Run a security scan (POST)

#### APIs API
- `/apis/catalog` - List APIs in the catalog
- `/apis/catalog/{id}` - Get a specific API
- `/apis/metrics` - Get API usage metrics
- `/apis/integration/flows` - List integration flows
- `/apis/integration/flows/{id}` - Get a specific integration flow
- `/apis/keys` - List API keys
- `/apis/resources` - List developer resources
- `/apis/test` - Test an API (POST)

## Partner Onboarding

Partners can onboard to the NovaFuse API Superstore by following these steps:

1. Apply for partnership through the partner application form
2. Sign the partner agreement and revenue sharing terms
3. Receive API key and access to the NovaFuse API Superstore
4. Choose the appropriate connector template for their service category
5. Implement the required endpoints based on the connector template
6. Test the integration using the sandbox environment
7. Submit the integration for review
8. Once approved, the integration will be published to the marketplace

## Development and Customization

The NovaFuse API Superstore can be customized and extended:

1. Add new API categories by creating new mock API services
2. Customize the UI theme by modifying the Tailwind CSS configuration
3. Add new connector templates for different types of integrations
4. Extend Kong with custom plugins for additional functionality

## Troubleshooting

If you encounter issues:

1. Check the Docker container logs for errors
2. Verify that all services are running
3. Ensure Kong is properly configured
4. Test the APIs directly (bypassing Kong) to isolate issues
