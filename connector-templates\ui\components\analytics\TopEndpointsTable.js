/**
 * Top Endpoints Table Component
 * 
 * This component displays a table of top endpoints by usage or response time.
 */

import React from 'react';
import { 
  Box,
  Table, 
  TableBody, 
  TableCell, 
  TableContainer, 
  TableHead, 
  TableRow, 
  Paper 
} from '@mui/material';

const TopEndpointsTable = ({ endpoints, countLabel = 'Requests' }) => {
  return (
    <TableContainer component={Paper} variant="outlined">
      <Table size="small">
        <TableHead>
          <TableRow>
            <TableCell>Endpoint</TableCell>
            <TableCell align="right">{countLabel}</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {endpoints.map((endpoint, index) => (
            <TableRow key={index}>
              <TableCell component="th" scope="row">
                <Box sx={{ 
                  maxWidth: 300, 
                  overflow: 'hidden', 
                  textOverflow: 'ellipsis', 
                  whiteSpace: 'nowrap' 
                }}>
                  {endpoint.endpoint}
                </Box>
              </TableCell>
              <TableCell align="right">{endpoint.count}</TableCell>
            </TableRow>
          ))}
          {endpoints.length === 0 && (
            <TableRow>
              <TableCell colSpan={2} align="center">
                No data available
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default TopEndpointsTable;
