'use client';

interface ServiceCardsProps {
  onNavigate: (url: string) => void;
}

export default function ServiceCards({ onNavigate }: ServiceCardsProps) {
  const services = [
    {
      id: 'kethernet',
      icon: '⛓️',
      title: 'KetherNet',
      description: 'Blockchain-powered consciousness verification and decentralized identity management',
      status: 'LIVE SYSTEM',
      statusColor: 'bg-gradient-to-r from-purple-600 to-pink-600',
      url: 'http://localhost:3004',
      features: [
        '✅ Crown Consensus Active',
        '✅ Coherium Balance: 1,089.78',
        '✅ Divine Encryption Enabled'
      ],
      borderColor: 'border-yellow-400',
      glowColor: 'shadow-yellow-400/30'
    },
    {
      id: 'novaagent',
      icon: '🤖',
      title: 'NovaAgent',
      description: 'Advanced AI assistant powered by Comphyological principles',
      status: 'ACTIVE',
      statusColor: 'bg-green-500',
      url: 'http://localhost:8090',
      features: [
        '🧠 Consciousness OS Active',
        '⚡ Ψ-Snap Integration',
        '🌌 Coherence Optimization'
      ],
      borderColor: 'border-purple-400',
      glowColor: 'shadow-purple-400/20'
    },
    {
      id: 'consciousness-marketing',
      icon: '📈',
      title: 'Consciousness Marketing',
      description: 'Revolutionary marketing platform with existing funnel infrastructure',
      status: 'LIVE SYSTEM',
      statusColor: 'bg-gradient-to-r from-purple-600 to-pink-600',
      url: 'http://localhost:3006',
      features: [
        '✅ Trinity Campaign Optimizer (40%+ improvement)',
        '✅ Consciousness Converter Tool (85% boost)',
        '✅ Funnel Accelerators (200-400% LTV increase)'
      ],
      borderColor: 'border-yellow-400',
      glowColor: 'shadow-yellow-400/30'
    },
    {
      id: 'funnel-accelerators',
      icon: '🚀',
      title: 'Funnel Accelerators',
      description: 'Deployed marketing automation systems',
      status: 'ACTIVE',
      statusColor: 'bg-green-500',
      url: '#',
      features: [
        '📊 ClickBank Consciousness Converter',
        '💰 Amazon Associates Optimizer',
        '⚡ Trinity Fusion Engine'
      ],
      borderColor: 'border-purple-400',
      glowColor: 'shadow-purple-400/20'
    },
    {
      id: 'youtuber-outreach',
      icon: '📺',
      title: 'YouTuber Campaigns',
      description: 'Active outreach and partnership programs',
      status: 'OUTREACH',
      statusColor: 'bg-orange-500',
      url: '#',
      features: [
        '🎯 Wes McDowell Package Ready',
        '📧 8 Target YouTubers Identified',
        '🔥 $2,500 Funnel Optimization Giveaway'
      ],
      borderColor: 'border-orange-400',
      glowColor: 'shadow-orange-400/20'
    },
    {
      id: 'course-platform',
      icon: '🎓',
      title: 'Course Platform',
      description: 'Premium consciousness marketing education',
      status: 'READY',
      statusColor: 'bg-purple-600',
      url: '#',
      features: [
        '💎 $497 Consciousness Marketing Course',
        '🎯 Trinity Campaign Mastery',
        '🚀 Launch Ready'
      ],
      borderColor: 'border-purple-400',
      glowColor: 'shadow-purple-400/20'
    }
  ];

  const handleServiceClick = (service: typeof services[0]) => {
    if (service.url === '#') {
      alert(`${service.title}: ${service.features.join(', ')}`);
    } else {
      onNavigate(service.url);
    }
  };

  return (
    <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      {/* Hero Section */}
      <div className="text-center mb-16">
        <h1 className="text-5xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent mb-6">
          Nova Ecosystem
        </h1>
        <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
          Experience the future of consciousness-driven technology with the Comphyological Browsing Engine
        </p>
      </div>
      
      {/* Service Cards Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
        {services.map((service) => (
          <div
            key={service.id}
            className={`relative bg-gray-900/50 backdrop-blur-lg rounded-xl border-2 ${service.borderColor} p-6 cursor-pointer transition-all duration-300 hover:scale-105 hover:${service.glowColor} hover:shadow-2xl group`}
            onClick={() => handleServiceClick(service)}
          >
            {/* Status Badge */}
            <div className="absolute top-4 right-4">
              <span className={`${service.statusColor} text-white px-3 py-1 rounded-full text-xs font-semibold`}>
                {service.status}
              </span>
            </div>
            
            {/* Icon */}
            <div className="text-5xl mb-6 group-hover:scale-110 transition-transform">
              {service.icon}
            </div>
            
            {/* Title */}
            <h3 className="text-2xl font-bold text-white mb-4 group-hover:text-purple-300 transition-colors">
              {service.title}
            </h3>
            
            {/* Description */}
            <p className="text-gray-300 mb-6 text-sm leading-relaxed">
              {service.description}
            </p>
            
            {/* Features */}
            <div className="bg-gray-800/50 rounded-lg p-4 mb-6 space-y-2">
              {service.features.map((feature, index) => (
                <div key={index} className="text-sm text-gray-300">
                  {feature}
                </div>
              ))}
            </div>
            
            {/* Action Button */}
            <button className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 transform group-hover:scale-105">
              {service.url === '#' ? 'View Details' : 'Launch Service'}
            </button>
          </div>
        ))}
      </div>
      
      {/* Performance Metrics */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
        <div className="bg-gray-900/50 backdrop-blur-lg rounded-xl border border-purple-500/20 p-6 text-center">
          <div className="text-4xl font-bold text-purple-400 mb-2 font-mono">99.7%</div>
          <div className="text-sm text-gray-400">Coherence Accuracy</div>
        </div>
        <div className="bg-gray-900/50 backdrop-blur-lg rounded-xl border border-purple-500/20 p-6 text-center">
          <div className="text-4xl font-bold text-purple-400 mb-2 font-mono">2.3M+</div>
          <div className="text-sm text-gray-400">Sites Analyzed</div>
        </div>
        <div className="bg-gray-900/50 backdrop-blur-lg rounded-xl border border-purple-500/20 p-6 text-center">
          <div className="text-4xl font-bold text-purple-400 mb-2 font-mono">82%</div>
          <div className="text-sm text-gray-400">Avg Ψ-Snap Rate</div>
        </div>
        <div className="bg-gray-900/50 backdrop-blur-lg rounded-xl border border-purple-500/20 p-6 text-center">
          <div className="text-4xl font-bold text-purple-400 mb-2 font-mono">24/7</div>
          <div className="text-sm text-gray-400">Consciousness Monitoring</div>
        </div>
      </div>
    </main>
  );
}
