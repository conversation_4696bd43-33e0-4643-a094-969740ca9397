/**
 * Feedback Routes
 * 
 * This file defines the API routes for user feedback.
 */

const express = require('express');
const router = express.Router();
const FeedbackController = require('../controllers/FeedbackController');
const { authenticate } = require('../middleware/authMiddleware');
const { hasPermission } = require('../middleware/permissionMiddleware');
const { validate } = require('../middleware/validationMiddleware');
const { feedbackSchemas } = require('../validation/feedback/feedbackSchemas');

// Create controller instance
const feedbackController = new FeedbackController();

// All routes require authentication
router.use(authenticate);

/**
 * @route   POST /api/feedback
 * @desc    Submit feedback
 * @access  Private
 */
router.post(
  '/',
  validate(feedbackSchemas.submitFeedbackSchema),
  (req, res, next) => {
    feedbackController.submitFeedback(req, res, next);
  }
);

/**
 * @route   GET /api/feedback/:id
 * @desc    Get feedback by ID
 * @access  Private (Owner or Admin)
 */
router.get(
  '/:id',
  validate(feedbackSchemas.getFeedbackByIdSchema),
  (req, res, next) => {
    feedbackController.getFeedbackById(req, res, next);
  }
);

/**
 * @route   GET /api/feedback/user/me
 * @desc    Get my feedback
 * @access  Private
 */
router.get(
  '/user/me',
  validate(feedbackSchemas.getMyFeedbackSchema),
  (req, res, next) => {
    feedbackController.getMyFeedback(req, res, next);
  }
);

/**
 * @route   GET /api/feedback
 * @desc    Get all feedback (admin only)
 * @access  Private (Admin)
 */
router.get(
  '/',
  hasPermission('feedback:read'),
  validate(feedbackSchemas.getAllFeedbackSchema),
  (req, res, next) => {
    feedbackController.getAllFeedback(req, res, next);
  }
);

/**
 * @route   PUT /api/feedback/:id/status
 * @desc    Update feedback status (admin only)
 * @access  Private (Admin)
 */
router.put(
  '/:id/status',
  hasPermission('feedback:update'),
  validate(feedbackSchemas.updateFeedbackStatusSchema),
  (req, res, next) => {
    feedbackController.updateFeedbackStatus(req, res, next);
  }
);

/**
 * @route   GET /api/feedback/statistics
 * @desc    Get feedback statistics (admin only)
 * @access  Private (Admin)
 */
router.get(
  '/statistics',
  hasPermission('feedback:read'),
  (req, res, next) => {
    feedbackController.getFeedbackStatistics(req, res, next);
  }
);

module.exports = router;
