import React, { useState, useEffect } from 'react';

const NovaAgentDashboard = () => {
  const [agentStatus, setAgentStatus] = useState(null);
  const [coherenceData, setCoherenceData] = useState(null);
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchAgentStatus = async () => {
      try {
        const response = await fetch('http://localhost:8080/status');
        if (response.ok) {
          const data = await response.json();
          setAgentStatus(data);
          setIsConnected(true);
          setError(null);
        } else {
          throw new Error('Failed to fetch agent status');
        }
      } catch (err) {
        setError(err.message);
        setIsConnected(false);
      }
    };

    const fetchCoherenceData = async () => {
      try {
        const response = await fetch('http://localhost:8080/coherence');
        if (response.ok) {
          const data = await response.json();
          setCoherenceData(data);
        }
      } catch (err) {
        console.error('Failed to fetch coherence data:', err);
      }
    };

    // Initial fetch
    fetchAgentStatus();
    fetchCoherenceData();

    // Set up polling every 5 seconds
    const interval = setInterval(() => {
      fetchAgentStatus();
      fetchCoherenceData();
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const getCoherenceColor = (level) => {
    if (level >= 0.82) return 'text-green-500';
    if (level >= 0.618) return 'text-yellow-500';
    return 'text-red-500';
  };

  const getStatusBadge = (status) => {
    const baseClasses = "px-3 py-1 rounded-full text-sm font-medium";
    if (status === 'coherent') {
      return `${baseClasses} bg-green-100 text-green-800`;
    }
    return `${baseClasses} bg-red-100 text-red-800`;
  };

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-6">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">Nova Agent Connection Error</h3>
            <p className="text-sm text-red-700 mt-1">{error}</p>
            <p className="text-xs text-red-600 mt-2">Make sure nova-agent-api.exe is running on port 8080</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-6 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold">🚀 Nova Agent</h2>
            <p className="text-blue-100">NovaFuse Coherence Operating System</p>
          </div>
          <div className="flex items-center space-x-2">
            <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-400' : 'bg-red-400'}`}></div>
            <span className="text-sm">{isConnected ? 'Connected' : 'Disconnected'}</span>
          </div>
        </div>
      </div>

      {agentStatus && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* Status Card */}
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Status</p>
                <p className="text-2xl font-semibold text-gray-900">{agentStatus.status}</p>
              </div>
              <span className={getStatusBadge(agentStatus.status)}>
                {agentStatus.status}
              </span>
            </div>
          </div>

          {/* Coherence Level */}
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Coherence Level</p>
                <p className={`text-2xl font-semibold ${getCoherenceColor(agentStatus.coherence)}`}>
                  {(agentStatus.coherence * 100).toFixed(1)}%
                </p>
              </div>
              <div className="text-right">
                <p className="text-xs text-gray-500">Ψ-Snap Point</p>
                <p className="text-sm font-medium">82%</p>
              </div>
            </div>
          </div>

          {/* Ψ-Snap Status */}
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Ψ-Snap Status</p>
                <p className={`text-2xl font-semibold ${agentStatus.psi_snap ? 'text-green-600' : 'text-yellow-600'}`}>
                  {agentStatus.psi_snap ? 'Active' : 'Building'}
                </p>
              </div>
              <div className={`w-12 h-12 rounded-full flex items-center justify-center ${agentStatus.psi_snap ? 'bg-green-100' : 'bg-yellow-100'}`}>
                <span className="text-2xl">{agentStatus.psi_snap ? '⚡' : '🔄'}</span>
              </div>
            </div>
          </div>

          {/* Uptime */}
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Uptime</p>
                <p className="text-lg font-semibold text-gray-900">{agentStatus.uptime}</p>
              </div>
              <div className="text-right">
                <p className="text-xs text-gray-500">Version</p>
                <p className="text-sm font-medium">{agentStatus.version}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Coherence Metrics */}
      {coherenceData && (
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Coherence Metrics</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <p className="text-sm text-gray-600">Structural Coherence</p>
              <p className="text-2xl font-bold text-blue-600">
                {(coherenceData.metrics.structural_coherence * 100).toFixed(1)}%
              </p>
            </div>
            <div className="text-center">
              <p className="text-sm text-gray-600">Functional Alignment</p>
              <p className="text-2xl font-bold text-green-600">
                {(coherenceData.metrics.functional_alignment * 100).toFixed(1)}%
              </p>
            </div>
            <div className="text-center">
              <p className="text-sm text-gray-600">Relational Integrity</p>
              <p className="text-2xl font-bold text-purple-600">
                {(coherenceData.metrics.relational_integrity * 100).toFixed(1)}%
              </p>
            </div>
          </div>
          <div className="mt-4 text-center">
            <p className="text-sm text-gray-600">Current Phase</p>
            <p className="text-lg font-semibold capitalize text-gray-900">
              {coherenceData.current_phase.replace('_', ' ')}
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default NovaAgentDashboard;
