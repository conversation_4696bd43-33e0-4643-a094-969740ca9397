/**
 * Connector List Component
 * 
 * This component displays a list of connectors with filtering and sorting options.
 */

import React, { useState } from 'react';
import { 
  Box, 
  Button, 
  Card, 
  CardContent, 
  Chip, 
  Divider, 
  Grid, 
  IconButton, 
  InputAdornment, 
  Menu, 
  MenuItem, 
  Paper, 
  Table, 
  TableBody, 
  TableCell, 
  TableContainer, 
  TableHead, 
  TablePagination, 
  TableRow, 
  TableSortLabel, 
  TextField, 
  Tooltip, 
  Typography 
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import SearchIcon from '@mui/icons-material/Search';
import FilterListIcon from '@mui/icons-material/FilterList';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import VisibilityIcon from '@mui/icons-material/Visibility';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import { useRouter } from 'next/router';

const ConnectorList = ({ connectors, onDelete, onDuplicate }) => {
  const router = useRouter();
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchQuery, setSearchQuery] = useState('');
  const [orderBy, setOrderBy] = useState('name');
  const [order, setOrder] = useState('asc');
  const [filterAnchorEl, setFilterAnchorEl] = useState(null);
  const [filters, setFilters] = useState({
    category: 'all',
    status: 'all'
  });
  const [actionMenuAnchorEl, setActionMenuAnchorEl] = useState(null);
  const [selectedConnector, setSelectedConnector] = useState(null);
  
  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };
  
  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };
  
  const handleSearchChange = (event) => {
    setSearchQuery(event.target.value);
    setPage(0);
  };
  
  const handleRequestSort = (property) => {
    const isAsc = orderBy === property && order === 'asc';
    setOrder(isAsc ? 'desc' : 'asc');
    setOrderBy(property);
  };
  
  const handleFilterClick = (event) => {
    setFilterAnchorEl(event.currentTarget);
  };
  
  const handleFilterClose = () => {
    setFilterAnchorEl(null);
  };
  
  const handleFilterChange = (filterType, value) => {
    setFilters({
      ...filters,
      [filterType]: value
    });
    setPage(0);
    handleFilterClose();
  };
  
  const handleActionMenuOpen = (event, connector) => {
    event.stopPropagation();
    setActionMenuAnchorEl(event.currentTarget);
    setSelectedConnector(connector);
  };
  
  const handleActionMenuClose = () => {
    setActionMenuAnchorEl(null);
    setSelectedConnector(null);
  };
  
  const handleEdit = () => {
    if (selectedConnector) {
      router.push(`/connectors/edit/${selectedConnector.id}`);
    }
    handleActionMenuClose();
  };
  
  const handleView = () => {
    if (selectedConnector) {
      router.push(`/connectors/${selectedConnector.id}`);
    }
    handleActionMenuClose();
  };
  
  const handleTest = () => {
    if (selectedConnector) {
      router.push(`/testing?connector=${selectedConnector.id}`);
    }
    handleActionMenuClose();
  };
  
  const handleDeleteConnector = () => {
    if (selectedConnector && onDelete) {
      onDelete(selectedConnector.id);
    }
    handleActionMenuClose();
  };
  
  const handleDuplicateConnector = () => {
    if (selectedConnector && onDuplicate) {
      onDuplicate(selectedConnector.id);
    }
    handleActionMenuClose();
  };
  
  const handleRowClick = (connector) => {
    router.push(`/connectors/${connector.id}`);
  };
  
  // Filter and sort connectors
  const filteredConnectors = connectors
    .filter(connector => {
      // Apply search filter
      if (searchQuery && !connector.name.toLowerCase().includes(searchQuery.toLowerCase())) {
        return false;
      }
      
      // Apply category filter
      if (filters.category !== 'all' && connector.category !== filters.category) {
        return false;
      }
      
      // Apply status filter
      if (filters.status !== 'all' && connector.status !== filters.status) {
        return false;
      }
      
      return true;
    })
    .sort((a, b) => {
      // Apply sorting
      const aValue = a[orderBy];
      const bValue = b[orderBy];
      
      if (order === 'asc') {
        if (typeof aValue === 'string') {
          return aValue.localeCompare(bValue);
        }
        return aValue - bValue;
      } else {
        if (typeof aValue === 'string') {
          return bValue.localeCompare(aValue);
        }
        return bValue - aValue;
      }
    });
  
  // Paginate connectors
  const paginatedConnectors = filteredConnectors.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage
  );
  
  const getStatusColor = (status) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'draft':
        return 'warning';
      case 'deprecated':
        return 'error';
      default:
        return 'default';
    }
  };
  
  return (
    <Box>
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between' }}>
        <TextField
          placeholder="Search connectors..."
          variant="outlined"
          size="small"
          value={searchQuery}
          onChange={handleSearchChange}
          sx={{ width: 300 }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            )
          }}
        />
        
        <Box>
          <Button
            variant="outlined"
            startIcon={<FilterListIcon />}
            onClick={handleFilterClick}
            sx={{ mr: 2 }}
          >
            Filter
          </Button>
          
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => router.push('/connectors/new')}
          >
            New Connector
          </Button>
        </Box>
      </Box>
      
      <Menu
        anchorEl={filterAnchorEl}
        open={Boolean(filterAnchorEl)}
        onClose={handleFilterClose}
      >
        <Typography variant="subtitle2" sx={{ px: 2, py: 1 }}>
          Category
        </Typography>
        <MenuItem 
          selected={filters.category === 'all'} 
          onClick={() => handleFilterChange('category', 'all')}
        >
          All Categories
        </MenuItem>
        <MenuItem 
          selected={filters.category === 'governance'} 
          onClick={() => handleFilterChange('category', 'governance')}
        >
          Governance
        </MenuItem>
        <MenuItem 
          selected={filters.category === 'risk'} 
          onClick={() => handleFilterChange('category', 'risk')}
        >
          Risk & Audit
        </MenuItem>
        <MenuItem 
          selected={filters.category === 'compliance'} 
          onClick={() => handleFilterChange('category', 'compliance')}
        >
          Compliance
        </MenuItem>
        <MenuItem 
          selected={filters.category === 'security'} 
          onClick={() => handleFilterChange('category', 'security')}
        >
          Security
        </MenuItem>
        <MenuItem 
          selected={filters.category === 'privacy'} 
          onClick={() => handleFilterChange('category', 'privacy')}
        >
          Privacy
        </MenuItem>
        
        <Divider sx={{ my: 1 }} />
        
        <Typography variant="subtitle2" sx={{ px: 2, py: 1 }}>
          Status
        </Typography>
        <MenuItem 
          selected={filters.status === 'all'} 
          onClick={() => handleFilterChange('status', 'all')}
        >
          All Statuses
        </MenuItem>
        <MenuItem 
          selected={filters.status === 'active'} 
          onClick={() => handleFilterChange('status', 'active')}
        >
          Active
        </MenuItem>
        <MenuItem 
          selected={filters.status === 'draft'} 
          onClick={() => handleFilterChange('status', 'draft')}
        >
          Draft
        </MenuItem>
        <MenuItem 
          selected={filters.status === 'deprecated'} 
          onClick={() => handleFilterChange('status', 'deprecated')}
        >
          Deprecated
        </MenuItem>
      </Menu>
      
      <TableContainer component={Paper} variant="outlined">
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>
                <TableSortLabel
                  active={orderBy === 'name'}
                  direction={orderBy === 'name' ? order : 'asc'}
                  onClick={() => handleRequestSort('name')}
                >
                  Name
                </TableSortLabel>
              </TableCell>
              <TableCell>
                <TableSortLabel
                  active={orderBy === 'category'}
                  direction={orderBy === 'category' ? order : 'asc'}
                  onClick={() => handleRequestSort('category')}
                >
                  Category
                </TableSortLabel>
              </TableCell>
              <TableCell>
                <TableSortLabel
                  active={orderBy === 'version'}
                  direction={orderBy === 'version' ? order : 'asc'}
                  onClick={() => handleRequestSort('version')}
                >
                  Version
                </TableSortLabel>
              </TableCell>
              <TableCell>
                <TableSortLabel
                  active={orderBy === 'status'}
                  direction={orderBy === 'status' ? order : 'asc'}
                  onClick={() => handleRequestSort('status')}
                >
                  Status
                </TableSortLabel>
              </TableCell>
              <TableCell>
                <TableSortLabel
                  active={orderBy === 'updated'}
                  direction={orderBy === 'updated' ? order : 'asc'}
                  onClick={() => handleRequestSort('updated')}
                >
                  Last Updated
                </TableSortLabel>
              </TableCell>
              <TableCell align="right">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {paginatedConnectors.length > 0 ? (
              paginatedConnectors.map(connector => (
                <TableRow 
                  key={connector.id} 
                  hover 
                  onClick={() => handleRowClick(connector)}
                  sx={{ cursor: 'pointer' }}
                >
                  <TableCell>
                    <Typography variant="subtitle2">{connector.name}</Typography>
                    <Typography variant="caption" color="textSecondary">
                      {connector.description}
                    </Typography>
                  </TableCell>
                  <TableCell>{connector.category}</TableCell>
                  <TableCell>{connector.version}</TableCell>
                  <TableCell>
                    <Chip 
                      label={connector.status} 
                      size="small" 
                      color={getStatusColor(connector.status)} 
                    />
                  </TableCell>
                  <TableCell>
                    {new Date(connector.updated).toLocaleDateString()}
                  </TableCell>
                  <TableCell align="right">
                    <IconButton
                      size="small"
                      onClick={(event) => handleActionMenuOpen(event, connector)}
                    >
                      <MoreVertIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={6} align="center" sx={{ py: 3 }}>
                  <Typography variant="body1" color="textSecondary">
                    No connectors found
                  </Typography>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
        
        <TablePagination
          rowsPerPageOptions={[5, 10, 25]}
          component="div"
          count={filteredConnectors.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      </TableContainer>
      
      <Menu
        anchorEl={actionMenuAnchorEl}
        open={Boolean(actionMenuAnchorEl)}
        onClose={handleActionMenuClose}
      >
        <MenuItem onClick={handleView}>
          <VisibilityIcon fontSize="small" sx={{ mr: 1 }} />
          View Details
        </MenuItem>
        <MenuItem onClick={handleEdit}>
          <EditIcon fontSize="small" sx={{ mr: 1 }} />
          Edit
        </MenuItem>
        <MenuItem onClick={handleTest}>
          <PlayArrowIcon fontSize="small" sx={{ mr: 1 }} />
          Test
        </MenuItem>
        <MenuItem onClick={handleDuplicateConnector}>
          <ContentCopyIcon fontSize="small" sx={{ mr: 1 }} />
          Duplicate
        </MenuItem>
        <Divider />
        <MenuItem onClick={handleDeleteConnector} sx={{ color: 'error.main' }}>
          <DeleteIcon fontSize="small" sx={{ mr: 1 }} />
          Delete
        </MenuItem>
      </Menu>
    </Box>
  );
};

export default ConnectorList;
