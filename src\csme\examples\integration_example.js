/**
 * CSME Integration Example
 * 
 * This example demonstrates how to integrate the CSME with the Comphy<PERSON> Meter and Governor.
 */

// Import CSME components
const { createCSMESystem } = require('../index');

/**
 * Mock Comphyon Meter for example purposes
 */
class MockComphyonMeter {
  constructor() {
    this.listeners = {
      alert: [],
      update: []
    };
    this.state = {
      currentCph: 0,
      csdeRate: 0,
      csfeRate: 0,
      csmeScore: 0.82
    };
  }
  
  on(event, callback) {
    if (this.listeners[event]) {
      this.listeners[event].push(callback);
    }
    return this;
  }
  
  removeListener(event, callback) {
    if (this.listeners[event]) {
      this.listeners[event] = this.listeners[event].filter(cb => cb !== callback);
    }
    return this;
  }
  
  updateTensorData(domain, tensor) {
    console.log(`Meter received tensor data from ${domain}:`, tensor);
    return this;
  }
  
  updateEthicalScore(score) {
    console.log(`Meter received ethical score: ${score}`);
    this.state.csmeScore = score;
    return this;
  }
  
  triggerAlert(level, reason) {
    const alertData = {
      level,
      reason,
      timestamp: new Date().toISOString()
    };
    
    this.listeners.alert.forEach(callback => callback(alertData));
    return this;
  }
}

/**
 * Mock Comphyon Governor for example purposes
 */
class MockComphyonGovernor {
  constructor() {
    this.listeners = {
      control: [],
      update: []
    };
  }
  
  on(event, callback) {
    if (this.listeners[event]) {
      this.listeners[event].push(callback);
    }
    return this;
  }
  
  removeListener(event, callback) {
    if (this.listeners[event]) {
      this.listeners[event] = this.listeners[event].filter(cb => cb !== callback);
    }
    return this;
  }
  
  receiveFeedback(controlAction, effectivenessMetrics) {
    console.log('Governor received feedback:', effectivenessMetrics);
    return this;
  }
  
  triggerControlAction(type, severity, actions) {
    const controlAction = {
      type,
      severity,
      actions,
      reason: `${severity} ${type} control action triggered`,
      trinity_levels: ['micro', 'meso'],
      timestamp: new Date().toISOString()
    };
    
    this.listeners.control.forEach(callback => callback(controlAction));
    return this;
  }
}

/**
 * Sample biological data for the example
 */
const sampleBiologicalData = {
  genomicData: {
    geneticRiskFactors: {
      alzheimers: 0.2,
      heartDisease: 0.3,
      diabetes: 0.1
    },
    geneticProtectiveFactors: {
      longevity: 0.4,
      immuneResponse: 0.5
    },
    telomereLength: 0.8
  },
  proteomicData: {
    proteinExpression: {
      p53: 0.7,
      mTOR: 0.6,
      FOXO3: 0.8
    },
    inflammatoryMarkers: {
      crp: 0.3,
      il6: 0.2,
      tnfAlpha: 0.1
    }
  },
  clinicalData: {
    symptoms: {
      fatigue: 0.3,
      pain: 0.2,
      cognitiveFog: 0.1
    },
    vitalSigns: {
      bloodPressure: 0.8,
      heartRate: 0.7,
      oxygenSaturation: 0.9
    }
  },
  environmentalData: {
    temperature: 22,
    humidity: 45,
    oxygenLevel: 21,
    atmosphericPressure: 1013,
    toxinLevel: 0.1
  }
};

/**
 * Run the integration example
 */
function runIntegrationExample() {
  console.log('=== CSME Integration Example ===');
  
  try {
    // Create CSME system
    console.log('Creating CSME system...');
    const csmeSystem = createCSMESystem({
      enableLogging: true
    });
    
    // Create mock Comphyon Meter and Governor
    console.log('Creating mock Comphyon Meter and Governor...');
    const meter = new MockComphyonMeter();
    const governor = new MockComphyonGovernor();
    
    // Connect CSME to Meter and Governor
    console.log('Connecting CSME to Meter and Governor...');
    csmeSystem.meterIntegrationInterface.connect(meter);
    csmeSystem.governorIntegrationInterface.connect(governor);
    
    // Process biological data
    console.log('\nProcessing biological data...');
    const result = csmeSystem.csmeController.processBiologicalData(
      sampleBiologicalData,
      sampleBiologicalData.environmentalData
    );
    
    console.log('Processing result:');
    console.log(`Coherence: ${result.coherence}`);
    console.log(`Decay Rate: ${result.decayRate}`);
    
    // Trigger Meter alert
    console.log('\nTriggering Meter alert...');
    meter.triggerAlert('medium', 'Coherence below threshold');
    
    // Trigger Governor control action
    console.log('\nTriggering Governor control action...');
    governor.triggerControlAction('resource_throttling', 'medium', [
      { type: 'throttle', target: 'environmental', value: 0.7 },
      { type: 'adjust', target: 'dietary', value: 0.8 }
    ]);
    
    // Recommend protocols
    console.log('\nRecommending protocols...');
    const recommendations = csmeSystem.csmeController.recommendProtocols();
    
    console.log(`Number of recommended protocols: ${recommendations.protocols.length}`);
    if (recommendations.protocols.length > 0) {
      console.log(`Top protocol: ${recommendations.protocols[0].name || 'Unnamed protocol'}`);
    }
    
    // Apply protocol
    if (recommendations.protocols.length > 0) {
      console.log('\nApplying protocol...');
      const applicationResult = csmeSystem.csmeController.applyProtocol(recommendations.protocols[0]);
      
      console.log(`Protocol applied: ${applicationResult.success}`);
      if (applicationResult.success) {
        console.log(`Intervention ID: ${applicationResult.intervention.id}`);
      }
    }
    
    // Project coherence trajectory
    console.log('\nProjecting coherence trajectory...');
    const projection = csmeSystem.csmeController.projectCoherenceTrajectory(10);
    
    console.log('Coherence projection:');
    console.log(`Initial coherence: ${projection.baselineProjection[0]}`);
    console.log(`Final coherence (10 steps): ${projection.baselineProjection[10]}`);
    
    // Disconnect from Meter and Governor
    console.log('\nDisconnecting from Meter and Governor...');
    csmeSystem.meterIntegrationInterface.disconnect();
    csmeSystem.governorIntegrationInterface.disconnect();
    
    console.log('\nIntegration example completed successfully.');
  } catch (error) {
    console.error('Error in integration example:', error);
  }
}

// Run the integration example
runIntegrationExample();
