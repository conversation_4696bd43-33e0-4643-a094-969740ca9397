<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NovaBrowser Demo - Coherence-First Web Gateway</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            color: white;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .header h1 {
            background: linear-gradient(45deg, #00ff96, #667eea);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-size: 3em;
            margin-bottom: 10px;
        }
        
        .demo-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .feature-card {
            background: rgba(0, 255, 150, 0.1);
            border-radius: 10px;
            padding: 20px;
            border-left: 4px solid #00ff96;
        }
        
        .feature-card h3 {
            color: #00ff96;
            margin-top: 0;
        }
        
        .demo-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 30px 0;
        }
        
        .demo-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 15px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            font-size: 16px;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        .demo-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        
        .status-panel {
            background: #000;
            border-radius: 10px;
            padding: 20px;
            font-family: 'Courier New', monospace;
            margin: 20px 0;
            border: 1px solid #333;
        }
        
        .log-entry {
            margin: 5px 0;
            padding: 5px 10px;
            border-left: 3px solid #00ff96;
            background: rgba(0, 255, 150, 0.1);
        }
        
        .violation-demo {
            background: rgba(255, 71, 87, 0.1);
            border: 1px solid #ff4757;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .compliance-demo {
            background: rgba(0, 255, 150, 0.1);
            border: 1px solid #00ff96;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .bad-contrast {
            background: #ffff00;
            color: #ffffff;
            padding: 10px;
            margin: 10px 0;
        }
        
        .good-contrast {
            background: #1a1a2e;
            color: #ffffff;
            padding: 10px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌐 NovaBrowser</h1>
            <p>Coherence-First Web Gateway Demo</p>
            <p><em>Watch the floating indicators for real-time analysis!</em></p>
        </div>

        <div class="demo-section">
            <h2>🧬 What is NovaBrowser?</h2>
            <p>NovaBrowser is the world's first <strong>Coherence-First Web Gateway</strong> that applies Comphyological principles to web browsing. It's not just a browser—it's a compliance-first, coherence-validated gateway to the web.</p>
            
            <div class="feature-grid">
                <div class="feature-card">
                    <h3>🧬 NovaDNA Analysis</h3>
                    <p>Real-time coherence scoring based on structural, functional, and relational integrity of web pages.</p>
                </div>
                <div class="feature-card">
                    <h3>👁️ NovaVision UI Compliance</h3>
                    <p>Automatic WCAG/ADA compliance checking with real-time remediation suggestions.</p>
                </div>
                <div class="feature-card">
                    <h3>🛡️ NovaShield Protection</h3>
                    <p>Advanced threat detection with Ψ-field intelligence for information hygiene.</p>
                </div>
                <div class="feature-card">
                    <h3>⚡ Ψ-Snap Filtering</h3>
                    <p>82/18 Comphyological Model enforcement - only coherent content passes through.</p>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2>🧪 Interactive Demo</h2>
            <p>Click the buttons below to see NovaBrowser's analysis capabilities in action:</p>
            
            <div class="demo-buttons">
                <button class="demo-btn" onclick="triggerCoherenceAnalysis()">🧬 Analyze Coherence</button>
                <button class="demo-btn" onclick="triggerVisionScan()">👁️ UI Compliance Scan</button>
                <button class="demo-btn" onclick="triggerThreatAssessment()">🛡️ Threat Assessment</button>
                <button class="demo-btn" onclick="showCoherenceHistory()">📊 View History</button>
            </div>
            
            <div class="status-panel" id="demoLog">
                <div class="log-entry">🚀 NovaBrowser Demo loaded - Watch for real-time analysis...</div>
            </div>
        </div>

        <div class="demo-section">
            <h2>🎯 Compliance Testing Scenarios</h2>
            
            <div class="violation-demo">
                <h3>❌ Accessibility Violations (NovaVision will flag these)</h3>
                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjUwIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iNTAiIGZpbGw9IiNmZjQ3NTciLz48dGV4dCB4PSI1MCIgeT0iMzAiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj5ObyBBbHQ8L3RleHQ+PC9zdmc+" style="display: block; margin: 10px 0;">
                <p class="bad-contrast">This text has poor contrast ratio (will be flagged)</p>
                <button style="background: #ff0000; color: #ff0000; border: none; padding: 10px;">Invisible Button</button>
            </div>
            
            <div class="compliance-demo">
                <h3>✅ Compliant Elements (NovaVision will approve these)</h3>
                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjUwIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iNTAiIGZpbGw9IiMwMGZmOTYiLz48dGV4dCB4PSI1MCIgeT0iMzAiIGZpbGw9ImJsYWNrIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj5XaXRoIEFsdDwvdGV4dD48L3N2Zz4=" alt="Compliant image with proper alt text" style="display: block; margin: 10px 0;">
                <p class="good-contrast">This text has excellent contrast ratio (will pass)</p>
                <button style="background: #00ff96; color: #000; border: none; padding: 10px; font-weight: bold;">Accessible Button</button>
            </div>
        </div>

        <div class="demo-section">
            <h2>📈 Coherence Metrics</h2>
            <p>NovaBrowser continuously monitors three critical aspects of web coherence:</p>
            
            <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px; margin: 20px 0;">
                <div style="text-align: center; padding: 20px; background: rgba(255,255,255,0.1); border-radius: 10px;">
                    <h3 style="color: #00ff96;">Structural Coherence</h3>
                    <div style="font-size: 24px; font-weight: bold;" id="structuralScore">85%</div>
                    <p style="font-size: 14px; opacity: 0.8;">DOM structure & semantic HTML</p>
                </div>
                <div style="text-align: center; padding: 20px; background: rgba(255,255,255,0.1); border-radius: 10px;">
                    <h3 style="color: #667eea;">Functional Alignment</h3>
                    <div style="font-size: 24px; font-weight: bold;" id="functionalScore">78%</div>
                    <p style="font-size: 14px; opacity: 0.8;">Interactive elements & UX flow</p>
                </div>
                <div style="text-align: center; padding: 20px; background: rgba(255,255,255,0.1); border-radius: 10px;">
                    <h3 style="color: #764ba2;">Relational Integrity</h3>
                    <div style="font-size: 24px; font-weight: bold;" id="relationalScore">91%</div>
                    <p style="font-size: 14px; opacity: 0.8;">Content relationships & hierarchy</p>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2>🔧 Configuration</h2>
            <p>Customize NovaBrowser's behavior:</p>
            
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div>
                    <label style="display: block; margin: 10px 0;">
                        <input type="range" min="0" max="100" value="82" id="coherenceThreshold" style="width: 100%;">
                        Coherence Threshold: <span id="thresholdValue">82%</span>
                    </label>
                    <label style="display: block; margin: 10px 0;">
                        <input type="checkbox" id="autoFilter" checked>
                        Auto-filter low coherence content
                    </label>
                    <label style="display: block; margin: 10px 0;">
                        <input type="checkbox" id="visionEnabled" checked>
                        Enable NovaVision UI compliance
                    </label>
                </div>
                <div>
                    <label style="display: block; margin: 10px 0;">
                        <input type="checkbox" id="shieldActive" checked>
                        Activate NovaShield protection
                    </label>
                    <label style="display: block; margin: 10px 0;">
                        <input type="checkbox" id="realTimeAnalysis" checked>
                        Real-time page analysis
                    </label>
                    <label style="display: block; margin: 10px 0;">
                        <input type="checkbox" id="complianceLogging" checked>
                        Log compliance violations
                    </label>
                </div>
            </div>
        </div>
    </div>

    <!-- Load NovaBrowser WASM module -->
    <script type="module">
        // Demo functions for interactive testing
        window.triggerCoherenceAnalysis = function() {
            addDemoLog('🧬 Running coherence analysis...');
            
            // Simulate analysis
            setTimeout(() => {
                const score = Math.random() * 0.4 + 0.6; // 60-100%
                const psiSnap = score >= 0.82;
                addDemoLog(`📊 Coherence Score: ${(score * 100).toFixed(1)}% (Ψ-Snap: ${psiSnap ? 'Active' : 'Building'})`);
                
                if (score < 0.82) {
                    addDemoLog('⚠️ Page below coherence threshold - remediation suggested');
                } else {
                    addDemoLog('✅ Page meets coherence standards');
                }
            }, 1000);
        };

        window.triggerVisionScan = function() {
            addDemoLog('👁️ NovaVision scanning UI compliance...');
            
            setTimeout(() => {
                const violations = [];
                const images = document.querySelectorAll('img:not([alt])');
                if (images.length > 0) {
                    violations.push(`${images.length} images missing alt text`);
                }
                
                const badContrast = document.querySelectorAll('.bad-contrast');
                if (badContrast.length > 0) {
                    violations.push(`${badContrast.length} elements with poor contrast`);
                }
                
                if (violations.length > 0) {
                    addDemoLog(`❌ WCAG violations detected: ${violations.join(', ')}`);
                    addDemoLog('💡 Suggested fixes: Add alt attributes, improve color contrast');
                } else {
                    addDemoLog('✅ No accessibility violations found');
                }
            }, 800);
        };

        window.triggerThreatAssessment = function() {
            addDemoLog('🛡️ NovaShield assessing threats...');
            
            setTimeout(() => {
                const threats = [];
                if (location.protocol === 'http:' && !location.hostname.includes('localhost')) {
                    threats.push('Insecure HTTP connection');
                }
                
                const scripts = document.querySelectorAll('script[src*="analytics"], script[src*="tracking"]');
                if (scripts.length > 0) {
                    threats.push('Tracking scripts detected');
                }
                
                const riskLevel = threats.length > 1 ? 'HIGH' : threats.length > 0 ? 'MEDIUM' : 'LOW';
                addDemoLog(`🔍 Threat Level: ${riskLevel}`);
                
                if (threats.length > 0) {
                    addDemoLog(`⚠️ Threats detected: ${threats.join(', ')}`);
                } else {
                    addDemoLog('✅ No threats detected');
                }
            }, 1200);
        };

        window.showCoherenceHistory = function() {
            addDemoLog('📊 Coherence History:');
            addDemoLog('  • demo.html - 87% coherence (Ψ-Snap: Active)');
            addDemoLog('  • example.com - 65% coherence (Filtered)');
            addDemoLog('  • secure-site.com - 94% coherence (Ψ-Snap: Active)');
        };

        function addDemoLog(message) {
            const log = document.getElementById('demoLog');
            const entry = document.createElement('div');
            entry.className = 'log-entry';
            entry.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            log.appendChild(entry);
            log.scrollTop = log.scrollHeight;
        }

        // Update threshold display
        document.getElementById('coherenceThreshold').addEventListener('input', function() {
            document.getElementById('thresholdValue').textContent = this.value + '%';
        });

        // Simulate real-time score updates
        setInterval(() => {
            const structural = Math.random() * 0.2 + 0.8; // 80-100%
            const functional = Math.random() * 0.3 + 0.7; // 70-100%
            const relational = Math.random() * 0.15 + 0.85; // 85-100%
            
            document.getElementById('structuralScore').textContent = Math.round(structural * 100) + '%';
            document.getElementById('functionalScore').textContent = Math.round(functional * 100) + '%';
            document.getElementById('relationalScore').textContent = Math.round(relational * 100) + '%';
        }, 3000);

        // Initial demo log
        setTimeout(() => {
            addDemoLog('🌐 NovaBrowser Demo initialized');
            addDemoLog('📡 Real-time analysis active');
            addDemoLog('🎯 Try the demo buttons above!');
        }, 500);
    </script>
</body>
</html>
