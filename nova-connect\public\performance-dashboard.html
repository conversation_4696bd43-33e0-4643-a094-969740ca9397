<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>NovaConnect UAC - Performance Dashboard</title>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: #f8f9fa;
      color: #212529;
    }
    .navbar {
      background-color: #0d6efd;
      color: white;
    }
    .navbar-brand {
      font-weight: bold;
    }
    .card {
      border-radius: 10px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      margin-bottom: 20px;
      transition: transform 0.2s;
    }
    .card:hover {
      transform: translateY(-5px);
    }
    .card-header {
      border-radius: 10px 10px 0 0;
      font-weight: bold;
    }
    .metric-card {
      text-align: center;
      padding: 20px;
    }
    .metric-value {
      font-size: 2.5rem;
      font-weight: bold;
      margin: 10px 0;
    }
    .metric-label {
      font-size: 1rem;
      color: #6c757d;
    }
    .chart-container {
      position: relative;
      height: 300px;
      width: 100%;
    }
    .status-indicator {
      display: inline-block;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      margin-right: 5px;
    }
    .status-healthy {
      background-color: #28a745;
    }
    .status-warning {
      background-color: #ffc107;
    }
    .status-critical {
      background-color: #dc3545;
    }
    .refresh-button {
      cursor: pointer;
      transition: transform 0.2s;
    }
    .refresh-button:hover {
      transform: rotate(90deg);
    }
    .connector-status-table {
      font-size: 0.9rem;
    }
    .connector-status-table th {
      font-weight: 600;
    }
  </style>
</head>
<body>
  <!-- Navigation -->
  <nav class="navbar navbar-expand-lg navbar-dark">
    <div class="container">
      <a class="navbar-brand" href="/">
        <i class="bi bi-hdd-network"></i> NovaConnect UAC
      </a>
      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
        <span class="navbar-toggler-icon"></span>
      </button>
      <div class="collapse navbar-collapse" id="navbarNav">
        <ul class="navbar-nav me-auto">
          <li class="nav-item">
            <a class="nav-link" href="/">Home</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="/connector-management.html">Connectors</a>
          </li>
          <li class="nav-item">
            <a class="nav-link active" href="/performance-dashboard.html">Dashboard</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="/csde-dashboard.html">CSDE</a>
          </li>
        </ul>
        <div class="d-flex">
          <button class="btn btn-outline-light" id="refreshDashboard">
            <i class="bi bi-arrow-clockwise refresh-button"></i> Refresh
          </button>
        </div>
      </div>
    </div>
  </nav>

  <!-- Main Content -->
  <div class="container mt-4">
    <div class="row mb-4">
      <div class="col-md-6">
        <h1>Performance Dashboard</h1>
        <p class="text-muted">Monitor the performance of your NovaConnect UAC instance</p>
      </div>
      <div class="col-md-6 text-end">
        <div class="btn-group" role="group">
          <button type="button" class="btn btn-outline-primary active" id="timeRange1h">1h</button>
          <button type="button" class="btn btn-outline-primary" id="timeRange24h">24h</button>
          <button type="button" class="btn btn-outline-primary" id="timeRange7d">7d</button>
          <button type="button" class="btn btn-outline-primary" id="timeRange30d">30d</button>
        </div>
      </div>
    </div>

    <!-- System Status -->
    <div class="row mb-4">
      <div class="col-12">
        <div class="card">
          <div class="card-header bg-primary text-white">
            System Status
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-3">
                <div class="metric-card">
                  <div class="metric-label">API Status</div>
                  <div class="metric-value text-success">
                    <i class="bi bi-check-circle-fill"></i>
                  </div>
                  <div>Healthy</div>
                </div>
              </div>
              <div class="col-md-3">
                <div class="metric-card">
                  <div class="metric-label">Database</div>
                  <div class="metric-value text-success">
                    <i class="bi bi-check-circle-fill"></i>
                  </div>
                  <div>Connected</div>
                </div>
              </div>
              <div class="col-md-3">
                <div class="metric-card">
                  <div class="metric-label">Cache</div>
                  <div class="metric-value text-success">
                    <i class="bi bi-check-circle-fill"></i>
                  </div>
                  <div>Operational</div>
                </div>
              </div>
              <div class="col-md-3">
                <div class="metric-card">
                  <div class="metric-label">Last Updated</div>
                  <div class="metric-value" style="font-size: 1.2rem;" id="lastUpdated">
                    Just now
                  </div>
                  <div>Auto-refresh: 1 min</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Key Metrics -->
    <div class="row mb-4">
      <div class="col-md-3">
        <div class="card h-100">
          <div class="card-body metric-card">
            <div class="metric-label">Requests / Minute</div>
            <div class="metric-value text-primary" id="requestsPerMinute">247</div>
            <div class="text-success">
              <i class="bi bi-arrow-up"></i> 12% from last period
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card h-100">
          <div class="card-body metric-card">
            <div class="metric-label">Average Response Time</div>
            <div class="metric-value text-primary" id="avgResponseTime">187 ms</div>
            <div class="text-success">
              <i class="bi bi-arrow-down"></i> 5% from last period
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card h-100">
          <div class="card-body metric-card">
            <div class="metric-label">Error Rate</div>
            <div class="metric-value text-primary" id="errorRate">0.8%</div>
            <div class="text-success">
              <i class="bi bi-arrow-down"></i> 2% from last period
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card h-100">
          <div class="card-body metric-card">
            <div class="metric-label">Active Connectors</div>
            <div class="metric-value text-primary" id="activeConnectors">18</div>
            <div class="text-success">
              <i class="bi bi-arrow-up"></i> 2 new this week
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Charts -->
    <div class="row mb-4">
      <div class="col-md-6">
        <div class="card">
          <div class="card-header bg-primary text-white">
            Request Volume
          </div>
          <div class="card-body">
            <div class="chart-container">
              <canvas id="requestVolumeChart"></canvas>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="card">
          <div class="card-header bg-primary text-white">
            Response Time
          </div>
          <div class="card-body">
            <div class="chart-container">
              <canvas id="responseTimeChart"></canvas>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="row mb-4">
      <div class="col-md-6">
        <div class="card">
          <div class="card-header bg-primary text-white">
            Error Rate
          </div>
          <div class="card-body">
            <div class="chart-container">
              <canvas id="errorRateChart"></canvas>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="card">
          <div class="card-header bg-primary text-white">
            Connector Usage
          </div>
          <div class="card-body">
            <div class="chart-container">
              <canvas id="connectorUsageChart"></canvas>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Connector Status Table -->
    <div class="row mb-4">
      <div class="col-12">
        <div class="card">
          <div class="card-header bg-primary text-white">
            Connector Status
          </div>
          <div class="card-body">
            <div class="table-responsive">
              <table class="table table-striped table-hover connector-status-table">
                <thead>
                  <tr>
                    <th>Connector</th>
                    <th>Category</th>
                    <th>Status</th>
                    <th>Uptime</th>
                    <th>Avg. Response</th>
                    <th>Error Rate</th>
                    <th>Last Used</th>
                  </tr>
                </thead>
                <tbody id="connectorStatusTableBody">
                  <!-- Table rows will be populated by JavaScript -->
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Scripts -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Initialize charts
      initializeCharts();
      
      // Load connector status table
      loadConnectorStatus();
      
      // Set up event listeners
      document.getElementById('refreshDashboard').addEventListener('click', refreshDashboard);
      document.getElementById('timeRange1h').addEventListener('click', () => changeTimeRange('1h'));
      document.getElementById('timeRange24h').addEventListener('click', () => changeTimeRange('24h'));
      document.getElementById('timeRange7d').addEventListener('click', () => changeTimeRange('7d'));
      document.getElementById('timeRange30d').addEventListener('click', () => changeTimeRange('30d'));
      
      // Auto-refresh every minute
      setInterval(refreshDashboard, 60000);
    });
    
    // Initialize charts with sample data
    function initializeCharts() {
      // Request Volume Chart
      const requestVolumeCtx = document.getElementById('requestVolumeChart').getContext('2d');
      const requestVolumeChart = new Chart(requestVolumeCtx, {
        type: 'line',
        data: {
          labels: generateTimeLabels(24),
          datasets: [{
            label: 'Requests',
            data: generateRandomData(24, 150, 350),
            borderColor: '#0d6efd',
            backgroundColor: 'rgba(13, 110, 253, 0.1)',
            borderWidth: 2,
            fill: true,
            tension: 0.4
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false
            }
          },
          scales: {
            y: {
              beginAtZero: true
            }
          }
        }
      });
      
      // Response Time Chart
      const responseTimeCtx = document.getElementById('responseTimeChart').getContext('2d');
      const responseTimeChart = new Chart(responseTimeCtx, {
        type: 'line',
        data: {
          labels: generateTimeLabels(24),
          datasets: [{
            label: 'Avg Response Time (ms)',
            data: generateRandomData(24, 150, 250),
            borderColor: '#20c997',
            backgroundColor: 'rgba(32, 201, 151, 0.1)',
            borderWidth: 2,
            fill: true,
            tension: 0.4
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false
            }
          },
          scales: {
            y: {
              beginAtZero: true
            }
          }
        }
      });
      
      // Error Rate Chart
      const errorRateCtx = document.getElementById('errorRateChart').getContext('2d');
      const errorRateChart = new Chart(errorRateCtx, {
        type: 'line',
        data: {
          labels: generateTimeLabels(24),
          datasets: [{
            label: 'Error Rate (%)',
            data: generateRandomData(24, 0.5, 2.5),
            borderColor: '#dc3545',
            backgroundColor: 'rgba(220, 53, 69, 0.1)',
            borderWidth: 2,
            fill: true,
            tension: 0.4
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false
            }
          },
          scales: {
            y: {
              beginAtZero: true
            }
          }
        }
      });
      
      // Connector Usage Chart
      const connectorUsageCtx = document.getElementById('connectorUsageChart').getContext('2d');
      const connectorUsageChart = new Chart(connectorUsageCtx, {
        type: 'doughnut',
        data: {
          labels: [
            'Governance & Board Compliance',
            'Legal & Regulatory Intelligence',
            'Risk & Audit',
            'Cybersecurity/InfoSec/Privacy',
            'Others'
          ],
          datasets: [{
            data: [25, 20, 15, 30, 10],
            backgroundColor: [
              '#0d6efd',
              '#6610f2',
              '#6f42c1',
              '#d63384',
              '#fd7e14'
            ],
            borderWidth: 1
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'right'
            }
          }
        }
      });
    }
    
    // Generate random data for charts
    function generateRandomData(count, min, max) {
      const data = [];
      for (let i = 0; i < count; i++) {
        data.push(Math.random() * (max - min) + min);
      }
      return data;
    }
    
    // Generate time labels for x-axis
    function generateTimeLabels(hours) {
      const labels = [];
      const now = new Date();
      for (let i = hours - 1; i >= 0; i--) {
        const time = new Date(now);
        time.setHours(now.getHours() - i);
        labels.push(time.getHours() + ':00');
      }
      return labels;
    }
    
    // Load connector status table
    function loadConnectorStatus() {
      const tableBody = document.getElementById('connectorStatusTableBody');
      const connectors = [
        { name: 'Governance Connector', category: 'Governance & Board Compliance', status: 'healthy', uptime: '99.9%', avgResponse: '156 ms', errorRate: '0.2%', lastUsed: '2 minutes ago' },
        { name: 'Legal Intelligence API', category: 'Legal & Regulatory Intelligence', status: 'healthy', uptime: '99.7%', avgResponse: '203 ms', errorRate: '0.5%', lastUsed: '5 minutes ago' },
        { name: 'Risk Assessment Tool', category: 'Risk & Audit', status: 'warning', uptime: '98.5%', avgResponse: '312 ms', errorRate: '1.2%', lastUsed: '15 minutes ago' },
        { name: 'Cybersecurity Scanner', category: 'Cybersecurity/InfoSec/Privacy', status: 'healthy', uptime: '99.8%', avgResponse: '178 ms', errorRate: '0.3%', lastUsed: '1 minute ago' },
        { name: 'Policy Manager', category: 'Contracts & Policy Lifecycle', status: 'healthy', uptime: '99.9%', avgResponse: '145 ms', errorRate: '0.1%', lastUsed: '8 minutes ago' },
        { name: 'Developer API Hub', category: 'APIs, iPaaS & Developer Tools', status: 'critical', uptime: '95.2%', avgResponse: '456 ms', errorRate: '3.8%', lastUsed: '1 hour ago' },
        { name: 'Business Intelligence', category: 'Business Intelligence & Workflow', status: 'healthy', uptime: '99.6%', avgResponse: '189 ms', errorRate: '0.4%', lastUsed: '12 minutes ago' },
        { name: 'Certification Manager', category: 'Certifications & Accreditation', status: 'warning', uptime: '97.8%', avgResponse: '267 ms', errorRate: '1.5%', lastUsed: '30 minutes ago' }
      ];
      
      tableBody.innerHTML = '';
      
      connectors.forEach(connector => {
        const row = document.createElement('tr');
        row.innerHTML = `
          <td>${connector.name}</td>
          <td>${connector.category}</td>
          <td>
            <span class="status-indicator status-${connector.status}"></span>
            ${connector.status === 'healthy' ? 'Healthy' : connector.status === 'warning' ? 'Warning' : 'Critical'}
          </td>
          <td>${connector.uptime}</td>
          <td>${connector.avgResponse}</td>
          <td>${connector.errorRate}</td>
          <td>${connector.lastUsed}</td>
        `;
        tableBody.appendChild(row);
      });
    }
    
    // Refresh dashboard
    function refreshDashboard() {
      // Update last updated time
      document.getElementById('lastUpdated').textContent = 'Just now';
      
      // Simulate data refresh
      document.getElementById('requestsPerMinute').textContent = Math.floor(Math.random() * 100) + 200;
      document.getElementById('avgResponseTime').textContent = Math.floor(Math.random() * 50) + 150 + ' ms';
      document.getElementById('errorRate').textContent = (Math.random() * 1.5).toFixed(1) + '%';
      
      // Reload connector status
      loadConnectorStatus();
      
      // Reinitialize charts
      initializeCharts();
    }
    
    // Change time range
    function changeTimeRange(range) {
      // Update active button
      document.querySelectorAll('.btn-group .btn').forEach(btn => {
        btn.classList.remove('active');
      });
      document.getElementById(`timeRange${range}`).classList.add('active');
      
      // Reinitialize charts with new time range
      initializeCharts();
    }
  </script>
</body>
</html>
