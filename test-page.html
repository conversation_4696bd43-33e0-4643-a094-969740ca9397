<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NovaFuse Server Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #1a1a1a;
            color: white;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background-color: #22c55e; }
        .error { background-color: #ef4444; }
        .info { background-color: #3b82f6; }
        button {
            background-color: #3b82f6;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #2563eb;
        }
        .dashboard-link {
            display: block;
            padding: 15px;
            margin: 10px 0;
            background-color: #374151;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            border: 2px solid #4b5563;
        }
        .dashboard-link:hover {
            background-color: #4b5563;
            border-color: #6b7280;
        }
    </style>
</head>
<body>
    <h1>🚀 NovaFuse Server Test Page</h1>
    
    <div id="serverStatus" class="status info">
        Testing server connection...
    </div>
    
    <h2>Server Tests</h2>
    <button onclick="testAPI()">Test API Connection</button>
    <button onclick="testWebSocket()">Test WebSocket</button>
    <button onclick="testAllEndpoints()">Test All Endpoints</button>
    
    <div id="testResults"></div>
    
    <h2>Dashboard Links</h2>
    <a href="/dashboard" class="dashboard-link">🧪 Test Dashboard</a>
    <a href="/deployment" class="dashboard-link">🚀 Deployment Dashboard</a>
    <a href="/demos" class="dashboard-link">🎮 Demo Selector</a>
    <a href="/docs" class="dashboard-link">📚 Documentation Portal</a>
    <a href="/report" class="dashboard-link">📊 Analytics Report</a>
    
    <h2>Direct File Tests</h2>
    <button onclick="testFileAccess()">Test File Access</button>
    <div id="fileResults"></div>
    
    <script src="https://cdn.socket.io/4.7.4/socket.io.min.js"></script>
    <script>
        let socket;
        
        // Test API connection
        async function testAPI() {
            try {
                const response = await fetch('/api/status');
                const data = await response.json();
                
                if (data.success) {
                    showResult('✅ API Connection: SUCCESS', 'success');
                    showResult(`Server: ${data.data.server}, Clients: ${data.data.connectedClients}`, 'info');
                } else {
                    showResult('❌ API Connection: FAILED', 'error');
                }
            } catch (error) {
                showResult(`❌ API Error: ${error.message}`, 'error');
            }
        }
        
        // Test WebSocket connection
        function testWebSocket() {
            try {
                socket = io();
                
                socket.on('connect', () => {
                    showResult('✅ WebSocket: CONNECTED', 'success');
                });
                
                socket.on('server-status', (data) => {
                    showResult(`✅ WebSocket Data: ${JSON.stringify(data)}`, 'info');
                });
                
                socket.on('disconnect', () => {
                    showResult('❌ WebSocket: DISCONNECTED', 'error');
                });
                
                socket.on('connect_error', (error) => {
                    showResult(`❌ WebSocket Error: ${error.message}`, 'error');
                });
                
            } catch (error) {
                showResult(`❌ WebSocket Error: ${error.message}`, 'error');
            }
        }
        
        // Test all endpoints
        async function testAllEndpoints() {
            const endpoints = [
                '/api/status',
                '/api/tests/discover',
                '/api/deployment/status',
                '/api/demos/discover',
                '/api/docs/stats'
            ];
            
            for (const endpoint of endpoints) {
                try {
                    const response = await fetch(endpoint);
                    const data = await response.json();
                    
                    if (data.success) {
                        showResult(`✅ ${endpoint}: SUCCESS`, 'success');
                    } else {
                        showResult(`❌ ${endpoint}: FAILED`, 'error');
                    }
                } catch (error) {
                    showResult(`❌ ${endpoint}: ERROR - ${error.message}`, 'error');
                }
            }
        }
        
        // Test file access
        async function testFileAccess() {
            const files = [
                'novafuse-test-dashboard.html',
                'novafuse-deployment-dashboard.html',
                'novafuse-demo-selector.html',
                'novafuse-docs-portal.html',
                'novafuse-test-report-live.html'
            ];
            
            const fileResults = document.getElementById('fileResults');
            fileResults.innerHTML = '';
            
            for (const file of files) {
                try {
                    const response = await fetch(`/${file}`);
                    
                    if (response.ok) {
                        const content = await response.text();
                        const div = document.createElement('div');
                        div.className = 'status success';
                        div.innerHTML = `✅ ${file}: ${content.length} bytes`;
                        fileResults.appendChild(div);
                    } else {
                        const div = document.createElement('div');
                        div.className = 'status error';
                        div.innerHTML = `❌ ${file}: ${response.status} ${response.statusText}`;
                        fileResults.appendChild(div);
                    }
                } catch (error) {
                    const div = document.createElement('div');
                    div.className = 'status error';
                    div.innerHTML = `❌ ${file}: ${error.message}`;
                    fileResults.appendChild(div);
                }
            }
        }
        
        function showResult(message, type) {
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.textContent = message;
            document.getElementById('testResults').appendChild(div);
        }
        
        // Auto-test on load
        window.onload = function() {
            testAPI();
            setTimeout(testWebSocket, 1000);
        };
    </script>
</body>
</html>
