@echo off
REM KetherNet + Comphyon + NEPI Simulation Runner
REM Consciousness-Validated Network Architecture Testing

echo 🚀 KetherNet Simulation Suite
echo ⚛️ Testing Trinity of Trust Architecture
echo ==================================================

REM Check if Docker is running
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker is not running. Please start Docker first.
    exit /b 1
)

REM Create necessary directories
if not exist "simulation-results" mkdir simulation-results
if not exist "evolution-data" mkdir evolution-data
if not exist "traffic-scripts" mkdir traffic-scripts
if not exist "aggregation-scripts" mkdir aggregation-scripts
if not exist "grafana-simulation-dashboards" mkdir grafana-simulation-dashboards

echo 📁 Created simulation directories

REM Start the main Trinity stack first
echo 🔄 Starting Trinity of Trust stack...
docker-compose -f docker-compose.yml up -d

REM Wait for services to be ready
echo ⏳ Waiting for Trinity services to initialize...
timeout /t 30 /nobreak >nul

REM Check service health
echo 🏥 Checking service health...
curl -f http://localhost:8080/health >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ KetherNet blockchain is healthy
) else (
    echo ⚠️ KetherNet blockchain may not be ready yet
)

curl -f http://localhost:8083/health >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ NovaDNA identity is healthy
) else (
    echo ⚠️ NovaDNA identity may not be ready yet
)

curl -f http://localhost:8085/health >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ NovaShield security is healthy
) else (
    echo ⚠️ NovaShield security may not be ready yet
)

REM Run the simulation suite based on parameter
if "%1"=="direct" (
    echo 🐍 Running direct Python simulation...
    python kethernet-simulation-suite.py
) else if "%1"=="docker" (
    echo 🐳 Running Docker simulation harness...
    docker-compose -f docker-simulation-harness.yml up --build
) else if "%1"=="consciousness" (
    echo 🧠 Running consciousness filtering tests only...
    python -c "import asyncio; from kethernet_simulation_suite import KetherNetSimulator; asyncio.run(KetherNetSimulator().test_consciousness_filtering())"
) else if "%1"=="trinity" (
    echo ⚛️ Running Trinity stack validation tests...
    python -c "import asyncio; from kethernet_simulation_suite import KetherNetSimulator; asyncio.run(KetherNetSimulator().test_trinity_stack_validation())"
) else if "%1"=="threats" (
    echo 🛡️ Running threat detection tests...
    python -c "import asyncio; from kethernet_simulation_suite import KetherNetSimulator; asyncio.run(KetherNetSimulator().test_threat_detection_auto_blocking())"
) else if "%1"=="evolution" (
    echo 🧬 Running evolution tracking tests...
    python -c "import asyncio; from kethernet_simulation_suite import KetherNetSimulator; asyncio.run(KetherNetSimulator().test_evolution_tracking())"
) else if "%1"=="nepi" (
    echo 🧬 Running NEPI adaptive behavior tests...
    python -c "import asyncio; from kethernet_simulation_suite import KetherNetSimulator; asyncio.run(KetherNetSimulator().test_nepi_adaptive_behavior())"
) else (
    echo 🌟 Running complete simulation suite...
    echo.
    echo Available options:
    echo   run-simulation.bat direct      - Run Python simulation directly
    echo   run-simulation.bat docker      - Run full Docker simulation harness
    echo   run-simulation.bat consciousness - Test consciousness filtering only
    echo   run-simulation.bat trinity     - Test Trinity stack only
    echo   run-simulation.bat threats     - Test threat detection only
    echo   run-simulation.bat evolution   - Test evolution tracking only
    echo   run-simulation.bat nepi        - Test NEPI adaptive behavior only
    echo.
    echo 🚀 Running complete simulation in 5 seconds...
    timeout /t 5 /nobreak >nul
    
    python kethernet-simulation-suite.py
)

echo.
echo ==================================================
echo ✅ Simulation complete!
echo 📊 Check simulation-results/ for detailed reports
echo 📈 View dashboards at:
echo    - Trinity Dashboard: http://localhost:3000
echo    - Simulation Dashboard: http://localhost:3001
echo    - Prometheus Metrics: http://localhost:9090
echo ==================================================

REM Optional: Open dashboards automatically
start http://localhost:3000
start http://localhost:3001
