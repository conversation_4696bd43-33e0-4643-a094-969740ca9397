#!/usr/bin/env python3
"""
Demo of quantum algorithms for protein folding.

This script demonstrates how to use the quantum algorithms (QAOA and VQE)
for protein folding optimization.
"""

import os
import sys
import time
import argparse
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path

# Add parent directory to path to import from src
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.quantum.algorithms import qaoa_folding, vqe_folding
from src.quantum.ml.quantum_models import QuantumProteinDataset, HybridQuantumProteinModel, train_quantum_model
from src.utils.energy_models import get_energy_function

def run_qaoa_demo(sequence: str, num_qubits: int = 4, p: int = 1, shots: int = 1000):
    """Run a demo of QAOA for protein folding."""
    print("=" * 50)
    print("QAOA Protein Folding Demo")
    print("=" * 50)
    print(f"Sequence: {sequence}")
    print(f"Qubits: {num_qubits}, Layers: {p}, Shots: {shots}")
    
    start_time = time.time()
    
    # Run QAOA
    results = qaoa_folding.run_qaoa_folding(
        sequence=sequence,
        num_qubits=num_qubits,
        p=p,
        optimizer='COBYLA',
        maxiter=50,
        quantum_backend='qiskit',
        shots=shots
    )
    
    # Print results
    print("\nQAOA Results:")
    print(f"Optimal energy: {results['optimal_energy']:.4f}")
    print(f"Optimal bitstring: {results['optimal_bitstring']}")
    print(f"Optimization time: {results['optimizer_time']:.2f} seconds")
    
    # Plot optimization history if available
    if 'optimizer_history' in results and results['optimizer_history'] is not None:
        plt.figure(figsize=(10, 5))
        plt.plot(results['optimizer_history']['energy'], 'b-')
        plt.xlabel('Iteration')
        plt.ylabel('Energy')
        plt.title('QAOA Optimization Progress')
        plt.grid(True)
        plt.show()
    
    return results

def run_vqe_demo(sequence: str, num_qubits: int = 4, ansatz: str = 'EfficientSU2', shots: int = 1000):
    """Run a demo of VQE for protein folding."""
    print("\n" + "=" * 50)
    print("VQE Protein Folding Demo")
    print("=" * 50)
    print(f"Sequence: {sequence}")
    print(f"Qubits: {num_qubits}, Ansatz: {ansatz}, Shots: {shots}")
    
    start_time = time.time()
    
    # Run VQE
    results = vqe_folding.run_vqe_folding(
        sequence=sequence,
        num_qubits=num_qubits,
        ansatz=ansatz,
        optimizer='COBYLA',
        maxiter=50,
        quantum_backend='qiskit',
        shots=shots
    )
    
    # Print results
    print("\nVQE Results:")
    print(f"Optimal energy: {results['optimal_energy']:.4f}")
    if 'optimal_bitstring' in results and results['optimal_bitstring'] is not None:
        print(f"Optimal bitstring: {results['optimal_bitstring']}")
    print(f"Optimization time: {results['optimizer_time']:.2f} seconds")
    
    # Plot optimization history if available
    if 'optimizer_history' in results and results['optimizer_history'] is not None:
        plt.figure(figsize=(10, 5))
        plt.plot(results['optimizer_history']['energy'], 'r-')
        plt.xlabel('Iteration')
        plt.ylabel('Energy')
        plt.title('VQE Optimization Progress')
        plt.grid(True)
        plt.show()
    
    return results

def run_quantum_ml_demo(sequences: list, targets: np.ndarray, test_sequences: list = None, test_targets: np.ndarray = None):
    """Run a demo of quantum machine learning for protein folding."""
    print("\n" + "=" * 50)
    print("Quantum Machine Learning for Protein Folding")
    print("=" * 50)
    
    # Create datasets
    train_dataset = QuantumProteinDataset(sequences, targets)
    
    if test_sequences is not None and test_targets is not None:
        test_dataset = QuantumProteinDataset(test_sequences, test_targets)
        test_loader = DataLoader(test_dataset, batch_size=32, shuffle=False)
    else:
        test_loader = None
    
    # Create data loaders
    train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True)
    
    # Create model
    input_dim = len(sequences[0]) * 20  # 20 amino acids
    output_dim = 1  # Predict energy
    
    model = HybridQuantumProteinModel(
        input_dim=input_dim,
        output_dim=output_dim,
        hidden_dims=[64, 32],
        num_qubits=4,
        quantum_layers=2,
        use_sampler=False
    )
    
    # Train model
    print("\nTraining quantum model...")
    metrics = train_quantum_model(
        model=model,
        train_loader=train_loader,
        val_loader=test_loader,
        num_epochs=10,
        learning_rate=0.01,
        device='cpu',
        patience=3
    )
    
    # Plot training history
    plt.figure(figsize=(12, 5))
    
    plt.subplot(1, 2, 1)
    plt.plot(metrics['train_loss'], 'b-', label='Train')
    if 'val_loss' in metrics:
        plt.plot(metrics['val_loss'], 'r-', label='Validation')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('Training History')
    plt.legend()
    plt.grid(True)
    
    # Make predictions on test set if available
    if test_loader is not None:
        model.eval()
        all_preds = []
        all_targets = []
        
        with torch.no_grad():
            for inputs, targets in test_loader:
                preds = model(inputs)
                all_preds.append(preds.numpy())
                all_targets.append(targets.numpy())
        
        all_preds = np.concatenate(all_preds)
        all_targets = np.concatenate(all_targets)
        
        # Plot predictions vs targets
        plt.subplot(1, 2, 2)
        plt.scatter(all_targets, all_preds, alpha=0.5)
        plt.plot([all_targets.min(), all_targets.max()], 
                 [all_targets.min(), all_targets.max()], 'r--')
        plt.xlabel('True Energy')
        plt.ylabel('Predicted Energy')
        plt.title('Predictions vs Targets')
        plt.grid(True)
    
    plt.tight_layout()
    plt.show()
    
    return model, metrics

def main():
    """Main function to run the demo."""
    parser = argparse.ArgumentParser(description='Quantum Protein Folding Demo')
    parser.add_argument('--sequence', type=str, default='HHPPHPPHPPHPPHPPHPPHPPHH',
                       help='Protein sequence (H for hydrophobic, P for polar)')
    parser.add_argument('--qubits', type=int, default=4,
                       help='Number of qubits to use')
    parser.add_argument('--layers', type=int, default=1,
                       help='Number of QAOA layers')
    parser.add_argument('--shots', type=int, default=1000,
                       help='Number of shots for quantum measurements')
    parser.add_argument('--ansatz', type=str, default='EfficientSU2',
                       choices=['EfficientSU2', 'RealAmplitudes', 'TwoLocal'],
                       help='Ansatz to use for VQE')
    parser.add_argument('--demo', type=str, default='all',
                       choices=['qaoa', 'vqe', 'qml', 'all'],
                       help='Which demo to run')
    
    args = parser.parse_args()
    
    # Run selected demos
    if args.demo in ['qaoa', 'all']:
        run_qaoa_demo(
            sequence=args.sequence,
            num_qubits=min(args.qubits, len(args.sequence)),
            p=args.layers,
            shots=args.shots
        )
    
    if args.demo in ['vqe', 'all']:
        run_vqe_demo(
            sequence=args.sequence,
            num_qubits=min(args.qubits, len(args.sequence)),
            ansatz=args.ansatz,
            shots=args.shots
        )
    
    if args.demo in ['qml', 'all']:
        # Generate some synthetic data for demonstration
        sequences = [
            'HHPPHPPH',
            'HPPHPPHH',
            'PHHPPHPH',
            'PPHHPHPH',
            'HPPHHPPH',
            'HPHPHPHP',
            'HHPHPHPH',
            'PPHHPPHH'
        ]
        
        # Random target energies for demonstration
        np.random.seed(42)
        targets = np.random.rand(len(sequences)) * 10
        
        run_quantum_ml_demo(sequences, targets)

if __name__ == "__main__":
    import torch  # Import here to avoid issues with Qiskit
    main()
