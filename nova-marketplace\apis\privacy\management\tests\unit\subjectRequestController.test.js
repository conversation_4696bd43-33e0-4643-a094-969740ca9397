/**
 * Data Subject Request Controller Tests
 *
 * This file contains unit tests for the Data Subject Request controller.
 */

// Mock the services module before importing the controller
jest.mock('../../services', () => {
  // Create mock service methods
  const mockSubjectRequestService = {
    getAllRequests: jest.fn().mockResolvedValue({
      data: [{ _id: 'mock-id-1', requestType: 'access' }],
      pagination: {
        total: 1,
        page: 1,
        limit: 10,
        pages: 1
      }
    }),
    getRequestById: jest.fn().mockResolvedValue({ _id: 'mock-id-1', requestType: 'access' }),
    createRequest: jest.fn().mockResolvedValue({ _id: 'mock-id-1', requestType: 'access' }),
    updateRequest: jest.fn().mockResolvedValue({ _id: 'mock-id-1', status: 'in-progress' }),
    processRequest: jest.fn().mockResolvedValue({
      _id: 'mock-id-1',
      status: 'in-progress',
      affectedSystems: [
        {
          systemId: 'system-1',
          systemName: 'CRM System',
          status: 'in-progress',
          details: 'Processing data export'
        },
        {
          systemId: 'system-2',
          systemName: 'Marketing System',
          status: 'pending',
          details: 'Scheduled for processing'
        }
      ]
    }),
    generateDataExport: jest.fn().mockResolvedValue({
      id: 'mock-id-1',
      exportId: 'export-1234567890',
      exportUrl: 'https://api.novafuse.com/privacy/management/exports/export-1234567890',
      exportFormat: 'json',
      exportSize: '1.2 MB',
      exportCreatedAt: new Date(),
      exportExpiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
    }),
    identifyAffectedSystems: jest.fn().mockResolvedValue({
      id: 'mock-id-1',
      dataSubjectEmail: '<EMAIL>',
      affectedSystems: [
        {
          systemId: 'system-1',
          systemName: 'CRM System',
          dataCategories: ['contact_info', 'preferences'],
          processingPurposes: ['customer_service', 'marketing'],
          retentionPeriod: '2 years',
          accessMethod: 'API'
        }
      ]
    })
  };

  return {
    subjectRequestService: mockSubjectRequestService
  };
});

// Now import the controller and mocked service
const { subjectRequestController } = require('../../controllers');
const { subjectRequestService } = require('../../services');

describe('Subject Request Controller', () => {
  let req, res, next;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Setup request, response and next function
    req = {
      params: {},
      query: {},
      body: {}
    };

    res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    };

    next = jest.fn();
  });

  describe('getAllSubjectRequests', () => {
    it('should return all subject requests with pagination', async () => {
      // Setup request query
      req.query = {
        page: '1',
        limit: '10'
      };

      // Call the controller method
      await subjectRequestController.getAllSubjectRequests(req, res, next);

      // Verify the service method was called with the correct parameters
      expect(subjectRequestService.getAllRequests).toHaveBeenCalledWith(expect.objectContaining({
        page: 1,
        limit: 10,
        filter: expect.any(Object),
        sort: expect.any(Object)
      }));

      // Verify the response
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.any(Array),
        pagination: expect.objectContaining({
          total: 1,
          page: 1,
          limit: 10,
          pages: 1
        })
      }));
    });

    it('should use default values if query parameters are not provided', async () => {
      // Setup empty request query
      req.query = {};

      // Call the controller method
      await subjectRequestController.getAllSubjectRequests(req, res, next);

      // Verify the service method was called with default parameters
      expect(subjectRequestService.getAllRequests).toHaveBeenCalledWith(expect.objectContaining({
        page: 1,
        limit: 10,
        filter: expect.any(Object),
        sort: expect.any(Object)
      }));
    });

    it('should handle filtering by status', async () => {
      // Setup request query with status filter
      req.query = {
        page: '1',
        limit: '10',
        status: 'pending'
      };

      // Call the controller method
      await subjectRequestController.getAllSubjectRequests(req, res, next);

      // Verify the service method was called with the correct filter
      expect(subjectRequestService.getAllRequests).toHaveBeenCalledWith(expect.objectContaining({
        filter: expect.objectContaining({
          status: 'pending'
        })
      }));
    });

    it('should handle filtering by requestType', async () => {
      // Setup request query with requestType filter
      req.query = {
        page: '1',
        limit: '10',
        requestType: 'access'
      };

      // Call the controller method
      await subjectRequestController.getAllSubjectRequests(req, res, next);

      // Verify the service method was called with the correct filter
      expect(subjectRequestService.getAllRequests).toHaveBeenCalledWith(expect.objectContaining({
        filter: expect.objectContaining({
          requestType: 'access'
        })
      }));
    });

    it('should handle filtering by email', async () => {
      // Setup request query with email filter
      req.query = {
        page: '1',
        limit: '10',
        email: '<EMAIL>'
      };

      // Call the controller method
      await subjectRequestController.getAllSubjectRequests(req, res, next);

      // Verify the service method was called with the correct filter
      expect(subjectRequestService.getAllRequests).toHaveBeenCalledWith(expect.objectContaining({
        filter: expect.objectContaining({
          'dataSubject.email': '<EMAIL>'
        })
      }));
    });

    it('should handle custom sorting', async () => {
      // Setup request query with sorting parameters
      req.query = {
        page: '1',
        limit: '10',
        sortBy: 'createdAt',
        sortOrder: 'desc'
      };

      // Call the controller method
      await subjectRequestController.getAllSubjectRequests(req, res, next);

      // Verify the service method was called with the correct sort parameters
      expect(subjectRequestService.getAllRequests).toHaveBeenCalledWith(expect.objectContaining({
        sort: { createdAt: -1 }
      }));
    });

    it('should handle errors', async () => {
      // Setup error
      const error = new Error('Test error');
      subjectRequestService.getAllRequests.mockRejectedValueOnce(error);

      // Call the controller method
      await subjectRequestController.getAllSubjectRequests(req, res, next);

      // Verify error was passed to next
      expect(next).toHaveBeenCalledWith(error);
    });
  });

  describe('getSubjectRequestById', () => {
    it('should return a subject request by ID', async () => {
      // Setup request params
      req.params.id = 'mock-id-1';

      // Call the controller method
      await subjectRequestController.getSubjectRequestById(req, res, next);

      // Verify the service method was called with the correct parameters
      expect(subjectRequestService.getRequestById).toHaveBeenCalledWith('mock-id-1');

      // Verify the response
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({ _id: 'mock-id-1' })
      }));
    });

    it('should return 404 if the subject request is not found', async () => {
      // Setup request params
      req.params.id = 'non-existent-id';

      // Setup service to throw NotFoundError
      const notFoundError = new Error('Data subject request not found');
      notFoundError.name = 'NotFoundError';
      subjectRequestService.getRequestById.mockRejectedValueOnce(notFoundError);

      // Call the controller method
      await subjectRequestController.getSubjectRequestById(req, res, next);

      // Verify error handling
      expect(next).toHaveBeenCalledWith(expect.objectContaining({
        name: 'NotFoundError',
        message: 'Data subject request not found'
      }));
    });

    it('should handle errors', async () => {
      // Setup request params
      req.params.id = 'mock-id-1';

      // Setup error
      const error = new Error('Service error');
      subjectRequestService.getRequestById.mockRejectedValueOnce(error);

      // Call the controller method
      await subjectRequestController.getSubjectRequestById(req, res, next);

      // Verify error handling
      expect(next).toHaveBeenCalledWith(error);
    });
  });

  describe('createSubjectRequest', () => {
    it('should create a new subject request', async () => {
      // Setup request body
      req.body = {
        requestType: 'access',
        dataSubjectName: 'John Doe',
        dataSubjectEmail: '<EMAIL>',
        requestDetails: 'I want to access my data'
      };

      // Call the controller method
      await subjectRequestController.createSubjectRequest(req, res, next);

      // Verify the service method was called with the correct data
      expect(subjectRequestService.createRequest).toHaveBeenCalledWith(req.body);

      // Verify the response
      expect(res.status).toHaveBeenCalledWith(201);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.anything(),
        message: 'Data subject request created successfully'
      }));
    });

    it('should handle errors', async () => {
      // Setup request body
      req.body = {
        requestType: 'access',
        dataSubjectName: 'John Doe',
        dataSubjectEmail: '<EMAIL>',
        requestDetails: 'I want to access my data'
      };

      // Setup error
      const error = new Error('Service error');
      subjectRequestService.createRequest.mockRejectedValueOnce(error);

      // Call the controller method
      await subjectRequestController.createSubjectRequest(req, res, next);

      // Verify error handling
      expect(next).toHaveBeenCalledWith(error);
    });
  });

  describe('updateSubjectRequest', () => {
    it('should update a subject request', async () => {
      // Setup request params and body
      req.params.id = 'mock-id-1';
      req.body = {
        status: 'in-progress',
        assignedTo: 'user-456'
      };

      // Call the controller method
      await subjectRequestController.updateSubjectRequest(req, res, next);

      // Verify the service method was called with the correct parameters
      expect(subjectRequestService.updateRequest).toHaveBeenCalledWith('mock-id-1', req.body);

      // Verify the response
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.anything(),
        message: 'Data subject request updated successfully'
      }));
    });

    it('should return 404 if the subject request is not found', async () => {
      // Setup request params and body
      req.params.id = 'non-existent-id';
      req.body = {
        status: 'in-progress',
        assignedTo: 'user-456'
      };

      // Setup service to throw NotFoundError
      const notFoundError = new Error('Data subject request not found');
      notFoundError.name = 'NotFoundError';
      subjectRequestService.updateRequest.mockRejectedValueOnce(notFoundError);

      // Call the controller method
      await subjectRequestController.updateSubjectRequest(req, res, next);

      // Verify error handling
      expect(next).toHaveBeenCalledWith(expect.objectContaining({
        name: 'NotFoundError',
        message: 'Data subject request not found'
      }));
    });

    it('should handle errors', async () => {
      // Setup request params and body
      req.params.id = 'mock-id-1';
      req.body = {
        status: 'in-progress',
        assignedTo: 'user-456'
      };

      // Setup error
      const error = new Error('Service error');
      subjectRequestService.updateRequest.mockRejectedValueOnce(error);

      // Call the controller method
      await subjectRequestController.updateSubjectRequest(req, res, next);

      // Verify error handling
      expect(next).toHaveBeenCalledWith(error);
    });
  });

  describe('processSubjectRequest', () => {
    it('should process a subject request', async () => {
      // Setup request params
      req.params.id = 'mock-id-1';

      // Call the controller method
      await subjectRequestController.processSubjectRequest(req, res, next);

      // Verify the service method was called with the correct parameters
      expect(subjectRequestService.processRequest).toHaveBeenCalledWith('mock-id-1');

      // Verify the response
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({
          _id: 'mock-id-1',
          status: 'in-progress',
          affectedSystems: expect.arrayContaining([
            expect.objectContaining({
              systemId: 'system-1',
              systemName: 'CRM System'
            })
          ])
        }),
        message: 'Data subject request processing initiated'
      }));
    });

    it('should return 404 if the subject request is not found', async () => {
      // Setup request params
      req.params.id = 'non-existent-id';

      // Setup service to throw NotFoundError
      const notFoundError = new Error('Data subject request not found');
      notFoundError.name = 'NotFoundError';
      subjectRequestService.processRequest.mockRejectedValueOnce(notFoundError);

      // Call the controller method
      await subjectRequestController.processSubjectRequest(req, res, next);

      // Verify error handling
      expect(next).toHaveBeenCalledWith(expect.objectContaining({
        name: 'NotFoundError',
        message: 'Data subject request not found'
      }));
    });

    it('should handle errors', async () => {
      // Setup request params
      req.params.id = 'mock-id-1';

      // Setup error
      const error = new Error('Service error');
      subjectRequestService.processRequest.mockRejectedValueOnce(error);

      // Call the controller method
      await subjectRequestController.processSubjectRequest(req, res, next);

      // Verify error handling
      expect(next).toHaveBeenCalledWith(error);
    });
  });

  describe('generateDataExport', () => {
    it('should generate a data export for a subject request', async () => {
      // Setup request params
      req.params.id = 'mock-id-1';

      // Call the controller method
      await subjectRequestController.generateDataExport(req, res, next);

      // Verify the service method was called with the correct parameters
      expect(subjectRequestService.generateDataExport).toHaveBeenCalledWith('mock-id-1');

      // Verify the response
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({
          id: 'mock-id-1',
          exportId: expect.any(String),
          exportUrl: expect.any(String),
          exportFormat: 'json',
          exportSize: expect.any(String),
          exportCreatedAt: expect.any(Date),
          exportExpiresAt: expect.any(Date)
        }),
        message: 'Data export generated successfully'
      }));
    });

    it('should return 404 if the subject request is not found', async () => {
      // Setup request params
      req.params.id = 'non-existent-id';

      // Setup service to throw NotFoundError
      const notFoundError = new Error('Data subject request not found');
      notFoundError.name = 'NotFoundError';
      subjectRequestService.generateDataExport.mockRejectedValueOnce(notFoundError);

      // Call the controller method
      await subjectRequestController.generateDataExport(req, res, next);

      // Verify error handling
      expect(next).toHaveBeenCalledWith(expect.objectContaining({
        name: 'NotFoundError',
        message: 'Data subject request not found'
      }));
    });

    it('should return validation error if request type is not access', async () => {
      // Setup request params
      req.params.id = 'mock-id-1';

      // Setup service to throw ValidationError
      const validationError = new Error('Data export is only available for access requests');
      validationError.name = 'ValidationError';
      subjectRequestService.generateDataExport.mockRejectedValueOnce(validationError);

      // Call the controller method
      await subjectRequestController.generateDataExport(req, res, next);

      // Verify error handling
      expect(next).toHaveBeenCalledWith(expect.objectContaining({
        name: 'ValidationError',
        message: 'Data export is only available for access requests'
      }));
    });

    it('should handle errors', async () => {
      // Setup request params
      req.params.id = 'mock-id-1';

      // Setup error
      const error = new Error('Service error');
      subjectRequestService.generateDataExport.mockRejectedValueOnce(error);

      // Call the controller method
      await subjectRequestController.generateDataExport(req, res, next);

      // Verify error handling
      expect(next).toHaveBeenCalledWith(error);
    });
  });

  describe('identifyAffectedSystems', () => {
    it('should identify affected systems for a subject request', async () => {
      // Setup request params
      req.params.id = 'mock-id-1';

      // Call the controller method
      await subjectRequestController.identifyAffectedSystems(req, res, next);

      // Verify the service method was called with the correct parameters
      expect(subjectRequestService.identifyAffectedSystems).toHaveBeenCalledWith('mock-id-1');

      // Verify the response
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({
          id: 'mock-id-1',
          dataSubjectEmail: '<EMAIL>',
          affectedSystems: expect.any(Array)
        }),
        message: 'Affected systems identified successfully'
      }));
    });

    it('should return 404 if the subject request is not found', async () => {
      // Setup request params
      req.params.id = 'non-existent-id';

      // Setup service to throw NotFoundError
      const notFoundError = new Error('Data subject request not found');
      notFoundError.name = 'NotFoundError';
      subjectRequestService.identifyAffectedSystems.mockRejectedValueOnce(notFoundError);

      // Call the controller method
      await subjectRequestController.identifyAffectedSystems(req, res, next);

      // Verify error handling
      expect(next).toHaveBeenCalledWith(expect.objectContaining({
        name: 'NotFoundError',
        message: 'Data subject request not found'
      }));
    });

    it('should handle errors', async () => {
      // Setup request params
      req.params.id = 'mock-id-1';

      // Setup error
      const error = new Error('Service error');
      subjectRequestService.identifyAffectedSystems.mockRejectedValueOnce(error);

      // Call the controller method
      await subjectRequestController.identifyAffectedSystems(req, res, next);

      // Verify error handling
      expect(next).toHaveBeenCalledWith(error);
    });
  });
});
