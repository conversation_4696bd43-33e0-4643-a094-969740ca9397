import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import AutoForm from './AutoForm';
import ComplianceEnforcer from './ComplianceEnforcer';
import complianceRules from '../config/compliance.json';

/**
 * ComplianceAutoForm Component
 * 
 * A component that wraps AutoForm with compliance enforcement capabilities.
 * 
 * @param {Object} props - Component props
 * @param {Object} props.schema - Form schema
 * @param {Function} props.onSubmit - Form submission handler
 * @param {Object} props.initialValues - Initial form values
 * @param {Object} props.user - Current user information
 * @param {string} props.className - Additional CSS class
 */
const ComplianceAutoForm = ({ 
  schema, 
  onSubmit, 
  initialValues = {}, 
  user = null,
  className = '' 
}) => {
  const [enforcedSchema, setEnforcedSchema] = useState(schema);
  const [formData, setFormData] = useState(initialValues);
  const [auditInfo, setAuditInfo] = useState({
    timestamp: new Date().toISOString(),
    userId: user?.id || 'anonymous',
    action: 'form_view',
    formId: schema.entity || 'unknown'
  });
  
  // Apply compliance rules to schema
  useEffect(() => {
    // Set initial enforced schema
    setEnforcedSchema(schema);
  }, [schema]);
  
  // Handle schema changes from ComplianceEnforcer
  const handleSchemaChange = (modifiedSchema) => {
    setEnforcedSchema(modifiedSchema);
  };
  
  // Handle form submission with compliance audit
  const handleSubmit = (data) => {
    // Update audit info
    const submitAuditInfo = {
      ...auditInfo,
      timestamp: new Date().toISOString(),
      action: 'form_submit',
      data: { ...data }
    };
    
    // Apply compliance behavior based on schema
    if (enforcedSchema.submitBehavior === 'log_and_verify') {
      // Log submission for verification
      console.log('Compliance audit:', submitAuditInfo);
      
      // In a real implementation, we would send this to a compliance logging service
      // logComplianceEvent(submitAuditInfo);
      
      // Show verification UI if needed
      if (enforcedSchema.compliance_mode === 'HIPAA' || enforcedSchema.compliance_mode === 'SOC2') {
        // For demo purposes, just log
        console.log(`${enforcedSchema.compliance_mode} verification required`);
      }
    } else if (enforcedSchema.submitBehavior === 'blockchain_verify') {
      // In a real implementation, we would send this to a blockchain verification service
      console.log('Blockchain verification:', submitAuditInfo);
    }
    
    // Call the original onSubmit
    onSubmit(data);
  };
  
  // Render compliance footer if needed
  const renderComplianceFooter = () => {
    if (!enforcedSchema.complianceFooter) return null;
    
    return (
      <div className="compliance-footer mt-3 p-2 bg-light border rounded">
        <small className="text-muted">
          <i className="fas fa-shield-alt mr-1"></i>
          {enforcedSchema.complianceFooter}
        </small>
      </div>
    );
  };
  
  // Render blockchain verification badge if needed
  const renderBlockchainBadge = () => {
    if (!enforcedSchema.auditTrail) return null;
    
    return (
      <div className="blockchain-badge mt-3 mb-3">
        <div className="d-flex align-items-center">
          <span className="badge badge-info mr-2">
            <i className="fas fa-link mr-1"></i>
            Blockchain Verified
          </span>
          <small className="text-muted">
            All actions on this form are recorded on an immutable blockchain ledger
          </small>
        </div>
      </div>
    );
  };
  
  // Add required compliance components
  const renderComplianceComponents = () => {
    if (!enforcedSchema.compliance_mode) return null;
    
    const mode = enforcedSchema.compliance_mode.toLowerCase();
    const rules = complianceRules.auto_ui_rules[mode];
    
    if (!rules) return null;
    
    // For demo purposes, just render a placeholder
    return (
      <div className="compliance-components mt-3 mb-3">
        <div className="card">
          <div className="card-header bg-light">
            <h5 className="mb-0">
              {enforcedSchema.compliance_mode} Compliance Components
            </h5>
          </div>
          <div className="card-body">
            <ul className="list-group">
              {rules.required_components.map((component, index) => (
                <li key={index} className="list-group-item d-flex justify-content-between align-items-center">
                  {component.replace(/_/g, ' ')}
                  <span className="badge badge-primary badge-pill">Active</span>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    );
  };
  
  return (
    <div className="compliance-auto-form">
      {/* Hidden ComplianceEnforcer component */}
      <ComplianceEnforcer 
        schema={schema} 
        user={user} 
        onSchemaChange={handleSchemaChange} 
      />
      
      {/* Render blockchain verification badge if needed */}
      {renderBlockchainBadge()}
      
      {/* Render the AutoForm with the enforced schema */}
      <AutoForm 
        schema={enforcedSchema} 
        onSubmit={handleSubmit} 
        initialValues={formData}
        className={className}
      />
      
      {/* Render compliance footer if needed */}
      {renderComplianceFooter()}
      
      {/* Render required compliance components */}
      {renderComplianceComponents()}
    </div>
  );
};

ComplianceAutoForm.propTypes = {
  schema: PropTypes.object.isRequired,
  onSubmit: PropTypes.func.isRequired,
  initialValues: PropTypes.object,
  user: PropTypes.object,
  className: PropTypes.string
};

export default ComplianceAutoForm;
