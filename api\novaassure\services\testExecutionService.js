/**
 * Test Execution Service
 * 
 * This service provides functionality for test execution.
 */

const { TestExecution, TestPlan, Control, Evidence } = require('../models');
const logger = require('../utils/logger');
const { scheduleJob } = require('node-schedule');

/**
 * Get all test executions
 * @param {Object} filters - Filters
 * @param {number} page - Page number
 * @param {number} limit - Items per page
 * @returns {Promise<Object>} - Test executions with pagination
 */
async function getAllTestExecutions(filters = {}, page = 1, limit = 10) {
  try {
    // Build query
    const query = {};
    
    if (filters.testPlanId) {
      query.testPlan = filters.testPlanId;
    }
    
    if (filters.status) {
      query.status = filters.status;
    }
    
    // Count total
    const total = await TestExecution.countDocuments(query);
    
    // Get test executions
    const testExecutions = await TestExecution.find(query)
      .populate('testPlan')
      .populate('executedBy')
      .skip((page - 1) * limit)
      .limit(limit)
      .sort({ startedAt: -1 });
    
    return {
      testExecutions,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    };
  } catch (error) {
    logger.error('Failed to get test executions', error);
    throw error;
  }
}

/**
 * Get test execution by ID
 * @param {string} id - Test execution ID
 * @returns {Promise<Object>} - Test execution object
 */
async function getTestExecutionById(id) {
  try {
    const testExecution = await TestExecution.getById(id);
    
    if (!testExecution) {
      throw new Error('Test execution not found');
    }
    
    return testExecution;
  } catch (error) {
    logger.error(`Failed to get test execution ${id}`, error);
    throw error;
  }
}

/**
 * Start test execution
 * @param {string} testPlanId - Test plan ID
 * @param {string} userId - User ID
 * @param {string} [notes] - Execution notes
 * @returns {Promise<Object>} - Created test execution
 */
async function startTestExecution(testPlanId, userId, notes = '') {
  try {
    // Validate test plan
    const testPlan = await TestPlan.getById(testPlanId);
    
    if (!testPlan) {
      throw new Error('Test plan not found');
    }
    
    // Check if there's already an in-progress execution for this test plan
    const existingExecution = await TestExecution.findOne({
      testPlan: testPlanId,
      status: 'in-progress'
    });
    
    if (existingExecution) {
      throw new Error('There is already an in-progress execution for this test plan');
    }
    
    // Start test execution
    const testExecution = await TestExecution.start(testPlanId, userId, notes);
    
    logger.info(`Test execution ${testExecution._id} started by ${userId}`);
    
    return testExecution;
  } catch (error) {
    logger.error('Failed to start test execution', error);
    throw error;
  }
}

/**
 * Complete test execution
 * @param {string} id - Test execution ID
 * @param {Array} results - Test results
 * @param {string} [notes] - Execution notes
 * @returns {Promise<Object>} - Updated test execution
 */
async function completeTestExecution(id, results, notes = '') {
  try {
    // Validate test execution
    const testExecution = await TestExecution.getById(id);
    
    if (!testExecution) {
      throw new Error('Test execution not found');
    }
    
    if (testExecution.status !== 'in-progress') {
      throw new Error('Test execution is not in progress');
    }
    
    // Validate results
    if (!results || !Array.isArray(results) || results.length === 0) {
      throw new Error('Results are required');
    }
    
    // Validate each result
    const validatedResults = [];
    
    for (const result of results) {
      // Validate control
      const control = await Control.findById(result.controlId);
      
      if (!control) {
        throw new Error(`Control ${result.controlId} not found`);
      }
      
      // Validate evidence
      if (result.evidenceIds && result.evidenceIds.length > 0) {
        for (const evidenceId of result.evidenceIds) {
          const evidence = await Evidence.findById(evidenceId);
          
          if (!evidence) {
            throw new Error(`Evidence ${evidenceId} not found`);
          }
        }
      }
      
      // Add validated result
      validatedResults.push({
        control: result.controlId,
        status: result.status,
        notes: result.notes,
        evidence: result.evidenceIds || [],
        executedBy: testExecution.executedBy,
        executedAt: new Date()
      });
    }
    
    // Complete test execution
    const completedExecution = await TestExecution.complete(id, validatedResults, notes);
    
    logger.info(`Test execution ${id} completed`);
    
    return completedExecution;
  } catch (error) {
    logger.error(`Failed to complete test execution ${id}`, error);
    throw error;
  }
}

/**
 * Cancel test execution
 * @param {string} id - Test execution ID
 * @param {string} reason - Cancellation reason
 * @returns {Promise<Object>} - Updated test execution
 */
async function cancelTestExecution(id, reason) {
  try {
    // Cancel test execution
    const cancelledExecution = await TestExecution.cancel(id, reason);
    
    logger.info(`Test execution ${id} cancelled`);
    
    return cancelledExecution;
  } catch (error) {
    logger.error(`Failed to cancel test execution ${id}`, error);
    throw error;
  }
}

/**
 * Add test result
 * @param {string} id - Test execution ID
 * @param {Object} result - Test result
 * @returns {Promise<Object>} - Updated test execution
 */
async function addTestResult(id, result) {
  try {
    // Validate test execution
    const testExecution = await TestExecution.getById(id);
    
    if (!testExecution) {
      throw new Error('Test execution not found');
    }
    
    if (testExecution.status !== 'in-progress') {
      throw new Error('Test execution is not in progress');
    }
    
    // Validate control
    const control = await Control.findById(result.controlId);
    
    if (!control) {
      throw new Error(`Control ${result.controlId} not found`);
    }
    
    // Validate evidence
    if (result.evidenceIds && result.evidenceIds.length > 0) {
      for (const evidenceId of result.evidenceIds) {
        const evidence = await Evidence.findById(evidenceId);
        
        if (!evidence) {
          throw new Error(`Evidence ${evidenceId} not found`);
        }
      }
    }
    
    // Add test result
    const updatedExecution = await TestExecution.addResult(id, result);
    
    logger.info(`Test result added to execution ${id}`);
    
    return updatedExecution;
  } catch (error) {
    logger.error(`Failed to add test result to execution ${id}`, error);
    throw error;
  }
}

/**
 * Schedule automated test execution
 * @param {string} testPlanId - Test plan ID
 * @param {Object} schedule - Schedule data
 * @param {string[]} [notifyUsers] - Users to notify
 * @returns {Promise<Object>} - Schedule result
 */
async function scheduleAutomatedTestExecution(testPlanId, schedule, notifyUsers = []) {
  try {
    // Validate test plan
    const testPlan = await TestPlan.getById(testPlanId);
    
    if (!testPlan) {
      throw new Error('Test plan not found');
    }
    
    // Validate schedule
    if (!schedule.frequency) {
      throw new Error('Schedule frequency is required');
    }
    
    if (!schedule.startDate) {
      throw new Error('Schedule start date is required');
    }
    
    // Create cron expression
    let cronExpression;
    
    switch (schedule.frequency) {
      case 'daily':
        cronExpression = '0 0 * * *'; // Every day at midnight
        break;
      case 'weekly':
        cronExpression = '0 0 * * 1'; // Every Monday at midnight
        break;
      case 'monthly':
        cronExpression = '0 0 1 * *'; // First day of every month at midnight
        break;
      case 'quarterly':
        cronExpression = '0 0 1 1,4,7,10 *'; // First day of Jan, Apr, Jul, Oct at midnight
        break;
      case 'annually':
        cronExpression = '0 0 1 1 *'; // First day of January at midnight
        break;
      default:
        throw new Error('Invalid schedule frequency');
    }
    
    // Schedule job
    const job = scheduleJob(cronExpression, async () => {
      try {
        // Check if schedule is still valid
        const currentDate = new Date();
        
        if (schedule.endDate && new Date(schedule.endDate) < currentDate) {
          // Schedule has ended
          job.cancel();
          return;
        }
        
        // Run automated test
        await runAutomatedTest(testPlanId, {});
        
        // Notify users
        if (notifyUsers && notifyUsers.length > 0) {
          // In a real implementation, this would send notifications
          logger.info(`Notifying users about automated test execution: ${notifyUsers.join(', ')}`);
        }
      } catch (error) {
        logger.error(`Failed to run scheduled automated test for test plan ${testPlanId}`, error);
      }
    });
    
    logger.info(`Scheduled automated test execution for test plan ${testPlanId}`);
    
    return {
      success: true,
      testPlanId,
      schedule,
      notifyUsers,
      nextExecution: job.nextInvocation()
    };
  } catch (error) {
    logger.error(`Failed to schedule automated test execution for test plan ${testPlanId}`, error);
    throw error;
  }
}

/**
 * Run automated test
 * @param {string} testPlanId - Test plan ID
 * @param {Object} parameters - Test parameters
 * @returns {Promise<Object>} - Test execution
 */
async function runAutomatedTest(testPlanId, parameters = {}) {
  try {
    // Validate test plan
    const testPlan = await TestPlan.getById(testPlanId);
    
    if (!testPlan) {
      throw new Error('Test plan not found');
    }
    
    // Create test execution
    const testExecution = await TestExecution.start(testPlanId, 'system', 'Automated test execution');
    
    // In a real implementation, this would run automated tests
    // For this placeholder, we'll just simulate a successful execution
    
    // Simulate test results
    const results = [];
    
    for (const controlId of testPlan.controls) {
      // Get control
      const control = await Control.findById(controlId);
      
      if (!control) {
        continue;
      }
      
      // Simulate test result
      results.push({
        control: controlId,
        status: 'pass', // Simulated result
        notes: 'Automated test passed',
        evidence: [],
        executedBy: 'system',
        executedAt: new Date()
      });
    }
    
    // Complete test execution
    await TestExecution.complete(testExecution._id, results, 'Automated test execution completed');
    
    logger.info(`Automated test execution ${testExecution._id} completed for test plan ${testPlanId}`);
    
    return testExecution;
  } catch (error) {
    logger.error(`Failed to run automated test for test plan ${testPlanId}`, error);
    throw error;
  }
}

module.exports = {
  getAllTestExecutions,
  getTestExecutionById,
  startTestExecution,
  completeTestExecution,
  cancelTestExecution,
  addTestResult,
  scheduleAutomatedTestExecution,
  runAutomatedTest
};
