apiVersion: v1
kind: Service
metadata:
  name: novafuse-uac
  namespace: novafuse-test
  labels:
    app: novafuse-uac
    component: api
    release: test
    app.kubernetes.io/name: novafuse-uac
    app.kubernetes.io/component: api
    app.kubernetes.io/managed-by: kubectl
  annotations:
    cloud.google.com/neg: '{"ingress": true}'
spec:
  type: ClusterIP
  ports:
    - port: 80
      targetPort: http
      protocol: TCP
      name: http
  selector:
    app: novafuse-uac
    component: api
    release: test
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: novafuse-uac-sa
  namespace: novafuse-test
  labels:
    app: novafuse-uac
    component: api
    release: test
    app.kubernetes.io/name: novafuse-uac
    app.kubernetes.io/component: api
    app.kubernetes.io/managed-by: kubectl
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: novafuse-uac-role
  namespace: novafuse-test
  labels:
    app: novafuse-uac
    component: api
    release: test
    app.kubernetes.io/name: novafuse-uac
    app.kubernetes.io/component: api
    app.kubernetes.io/managed-by: kubectl
rules:
  - apiGroups: [""]
    resources: ["configmaps", "secrets"]
    verbs: ["get", "list", "watch"]
  - apiGroups: [""]
    resources: ["pods"]
    verbs: ["get", "list", "watch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: novafuse-uac-rolebinding
  namespace: novafuse-test
  labels:
    app: novafuse-uac
    component: api
    release: test
    app.kubernetes.io/name: novafuse-uac
    app.kubernetes.io/component: api
    app.kubernetes.io/managed-by: kubectl
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: novafuse-uac-role
subjects:
  - kind: ServiceAccount
    name: novafuse-uac-sa
    namespace: novafuse-test
