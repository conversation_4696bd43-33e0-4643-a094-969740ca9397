"""
Evaluate a fine-tuned compliance language model.

This script evaluates a fine-tuned model on compliance Q&A tasks.
"""

import os
import json
import argparse
import logging
from typing import List, Dict, Any

import torch
from transformers import AutoModelForCausalLM, AutoTokenizer

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_test_questions(test_file: str) -> List[Dict[str, str]]:
    """
    Load test questions from a file.
    
    Args:
        test_file: Path to the test file
        
    Returns:
        List of dictionaries with 'question' and 'answer' keys
    """
    logger.info(f"Loading test questions from {test_file}")
    
    questions = []
    
    # Check file extension
    if test_file.endswith('.jsonl'):
        # Load JSONL format
        with open(test_file, 'r', encoding='utf-8') as f:
            for line in f:
                example = json.loads(line)
                if 'prompt' in example and 'completion' in example:
                    # Extract question from prompt
                    prompt = example['prompt']
                    if prompt.startswith('Question: ') and prompt.endswith('Answer:'):
                        question = prompt[len('Question: '):-len('Answer:')]
                        answer = example['completion'].strip()
                        questions.append({'question': question, 'answer': answer})
    elif test_file.endswith('.json'):
        # Load JSON format (chat)
        with open(test_file, 'r', encoding='utf-8') as f:
            examples = json.load(f)
            for example in examples:
                if 'messages' in example:
                    messages = example['messages']
                    if len(messages) >= 3 and messages[0]['role'] == 'system' and messages[1]['role'] == 'user' and messages[2]['role'] == 'assistant':
                        question = messages[1]['content']
                        answer = messages[2]['content']
                        questions.append({'question': question, 'answer': answer})
    else:
        logger.error(f"Unsupported file format: {test_file}")
    
    logger.info(f"Loaded {len(questions)} test questions")
    return questions

def generate_response(model, tokenizer, question: str, max_length: int = 512) -> str:
    """
    Generate a response to a question using the model.
    
    Args:
        model: The language model
        tokenizer: The tokenizer
        question: The question to answer
        max_length: Maximum length of the generated response
        
    Returns:
        The generated response
    """
    # Format the input
    input_text = f"Question: {question}\nAnswer:"
    
    # Tokenize the input
    inputs = tokenizer(input_text, return_tensors="pt").to(model.device)
    
    # Generate a response
    with torch.no_grad():
        outputs = model.generate(
            inputs['input_ids'],
            max_length=max_length,
            num_return_sequences=1,
            temperature=0.7,
            top_p=0.9,
            do_sample=True,
            pad_token_id=tokenizer.eos_token_id
        )
    
    # Decode the response
    response = tokenizer.decode(outputs[0], skip_special_tokens=True)
    
    # Extract the answer part
    if "Answer:" in response:
        response = response.split("Answer:")[1].strip()
    
    return response

def evaluate_model(
    model_path: str,
    test_file: str,
    output_file: Optional[str] = None,
    max_length: int = 512,
    use_gpu: bool = True
):
    """
    Evaluate a fine-tuned model on compliance Q&A tasks.
    
    Args:
        model_path: Path to the fine-tuned model
        test_file: Path to the test file
        output_file: Path to save the evaluation results
        max_length: Maximum length of the generated responses
        use_gpu: Whether to use GPU if available
    """
    # Check if GPU is available
    device = torch.device("cuda" if torch.cuda.is_available() and use_gpu else "cpu")
    logger.info(f"Using device: {device}")
    
    # Load tokenizer and model
    logger.info(f"Loading tokenizer and model from {model_path}")
    tokenizer = AutoTokenizer.from_pretrained(model_path)
    model = AutoModelForCausalLM.from_pretrained(model_path).to(device)
    
    # Load test questions
    test_questions = load_test_questions(test_file)
    
    # Generate responses
    logger.info("Generating responses")
    results = []
    for i, qa_pair in enumerate(test_questions):
        question = qa_pair['question']
        reference_answer = qa_pair['answer']
        
        logger.info(f"Processing question {i+1}/{len(test_questions)}")
        generated_answer = generate_response(model, tokenizer, question, max_length)
        
        results.append({
            'question': question,
            'reference_answer': reference_answer,
            'generated_answer': generated_answer
        })
    
    # Save results if output file is specified
    if output_file:
        logger.info(f"Saving evaluation results to {output_file}")
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2)
    
    # Print some example results
    logger.info("\nExample results:")
    for i in range(min(3, len(results))):
        logger.info(f"\nQuestion: {results[i]['question']}")
        logger.info(f"Generated Answer: {results[i]['generated_answer']}")
        logger.info(f"Reference Answer: {results[i]['reference_answer']}")
    
    logger.info("\nEvaluation complete")

def main():
    parser = argparse.ArgumentParser(description='Evaluate a fine-tuned compliance language model')
    parser.add_argument('--model_path', type=str, required=True, help='Path to the fine-tuned model')
    parser.add_argument('--test_file', type=str, required=True, help='Path to the test file')
    parser.add_argument('--output_file', type=str, help='Path to save the evaluation results')
    parser.add_argument('--max_length', type=int, default=512, help='Maximum length of the generated responses')
    parser.add_argument('--no_gpu', action='store_true', help='Disable GPU usage even if available')
    args = parser.parse_args()
    
    # Evaluate the model
    evaluate_model(
        model_path=args.model_path,
        test_file=args.test_file,
        output_file=args.output_file,
        max_length=args.max_length,
        use_gpu=not args.no_gpu
    )

if __name__ == "__main__":
    main()
