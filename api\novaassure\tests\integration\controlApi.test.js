/**
 * Integration tests for the control API
 */

const request = require('supertest');
const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const express = require('express');
const bodyParser = require('body-parser');
const { Control } = require('../../models');
const controlRoutes = require('../../routes/controlRoutes');

// Create a test app
const app = express();
app.use(bodyParser.json());
app.use('/api/v1/novaassure/controls', controlRoutes);

// Mock authentication middleware
jest.mock('../../middleware/authMiddleware', () => ({
  authenticate: (req, res, next) => {
    req.user = { id: 'test-user-id', role: 'admin' };
    next();
  },
  hasRole: () => (req, res, next) => next(),
  hasPermission: () => (req, res, next) => next()
}));

describe('Control API', () => {
  let mongoServer;

  // Setup MongoDB Memory Server before tests
  beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const uri = mongoServer.getUri();
    await mongoose.connect(uri, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
  });

  // Clean up after tests
  afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
  });

  // Clean up database between tests
  beforeEach(async () => {
    await Control.deleteMany({});
  });

  describe('GET /api/v1/novaassure/controls', () => {
    it('should return all controls with pagination', async () => {
      // Create test controls
      await Control.create([
        {
          name: 'Control 1',
          description: 'Test control 1',
          framework: 'soc2',
          category: 'access-control',
          requirements: ['Req 1'],
          testProcedures: ['Test 1'],
          riskLevel: 'medium',
          implementationStatus: 'implemented',
          createdBy: 'test-user-id',
          updatedBy: 'test-user-id'
        },
        {
          name: 'Control 2',
          description: 'Test control 2',
          framework: 'gdpr',
          category: 'data-protection',
          requirements: ['Req 2'],
          testProcedures: ['Test 2'],
          riskLevel: 'high',
          implementationStatus: 'partially-implemented',
          createdBy: 'test-user-id',
          updatedBy: 'test-user-id'
        }
      ]);

      // Make request
      const response = await request(app)
        .get('/api/v1/novaassure/controls')
        .expect(200);

      // Assertions
      expect(response.body).toHaveProperty('controls');
      expect(response.body).toHaveProperty('pagination');
      expect(response.body.controls).toHaveLength(2);
      expect(response.body.pagination).toEqual({
        page: 1,
        limit: 10,
        total: 2,
        pages: 1
      });
    });

    it('should filter controls by framework', async () => {
      // Create test controls
      await Control.create([
        {
          name: 'Control 1',
          description: 'Test control 1',
          framework: 'soc2',
          category: 'access-control',
          requirements: ['Req 1'],
          testProcedures: ['Test 1'],
          riskLevel: 'medium',
          implementationStatus: 'implemented',
          createdBy: 'test-user-id',
          updatedBy: 'test-user-id'
        },
        {
          name: 'Control 2',
          description: 'Test control 2',
          framework: 'gdpr',
          category: 'data-protection',
          requirements: ['Req 2'],
          testProcedures: ['Test 2'],
          riskLevel: 'high',
          implementationStatus: 'partially-implemented',
          createdBy: 'test-user-id',
          updatedBy: 'test-user-id'
        }
      ]);

      // Make request
      const response = await request(app)
        .get('/api/v1/novaassure/controls?framework=soc2')
        .expect(200);

      // Assertions
      expect(response.body).toHaveProperty('controls');
      expect(response.body.controls).toHaveLength(1);
      expect(response.body.controls[0].framework).toBe('soc2');
    });
  });

  describe('GET /api/v1/novaassure/controls/:id', () => {
    it('should return a control by ID', async () => {
      // Create test control
      const control = await Control.create({
        name: 'Control 1',
        description: 'Test control 1',
        framework: 'soc2',
        category: 'access-control',
        requirements: ['Req 1'],
        testProcedures: ['Test 1'],
        riskLevel: 'medium',
        implementationStatus: 'implemented',
        createdBy: 'test-user-id',
        updatedBy: 'test-user-id'
      });

      // Make request
      const response = await request(app)
        .get(`/api/v1/novaassure/controls/${control._id}`)
        .expect(200);

      // Assertions
      expect(response.body).toHaveProperty('_id', control._id.toString());
      expect(response.body).toHaveProperty('name', 'Control 1');
      expect(response.body).toHaveProperty('framework', 'soc2');
    });

    it('should return 404 if control not found', async () => {
      // Make request with non-existent ID
      await request(app)
        .get('/api/v1/novaassure/controls/5f7d5dc5d7613b3a4c7e9e1a')
        .expect(404);
    });
  });

  describe('POST /api/v1/novaassure/controls', () => {
    it('should create a new control', async () => {
      // Control data
      const controlData = {
        name: 'New Control',
        description: 'New test control',
        framework: 'hipaa',
        category: 'security',
        requirements: ['Req 1', 'Req 2'],
        testProcedures: ['Test 1', 'Test 2'],
        riskLevel: 'high',
        implementationStatus: 'not-implemented'
      };

      // Make request
      const response = await request(app)
        .post('/api/v1/novaassure/controls')
        .send(controlData)
        .expect(201);

      // Assertions
      expect(response.body).toHaveProperty('_id');
      expect(response.body).toHaveProperty('name', 'New Control');
      expect(response.body).toHaveProperty('framework', 'hipaa');
      expect(response.body).toHaveProperty('createdBy', 'test-user-id');

      // Check database
      const control = await Control.findById(response.body._id);
      expect(control).toBeTruthy();
      expect(control.name).toBe('New Control');
    });

    it('should return 400 if required fields are missing', async () => {
      // Incomplete control data
      const controlData = {
        name: 'New Control'
        // Missing required fields
      };

      // Make request
      await request(app)
        .post('/api/v1/novaassure/controls')
        .send(controlData)
        .expect(400);
    });
  });

  describe('PUT /api/v1/novaassure/controls/:id', () => {
    it('should update an existing control', async () => {
      // Create test control
      const control = await Control.create({
        name: 'Control 1',
        description: 'Test control 1',
        framework: 'soc2',
        category: 'access-control',
        requirements: ['Req 1'],
        testProcedures: ['Test 1'],
        riskLevel: 'medium',
        implementationStatus: 'implemented',
        createdBy: 'test-user-id',
        updatedBy: 'test-user-id'
      });

      // Update data
      const updateData = {
        name: 'Updated Control',
        description: 'Updated description',
        riskLevel: 'high'
      };

      // Make request
      const response = await request(app)
        .put(`/api/v1/novaassure/controls/${control._id}`)
        .send(updateData)
        .expect(200);

      // Assertions
      expect(response.body).toHaveProperty('_id', control._id.toString());
      expect(response.body).toHaveProperty('name', 'Updated Control');
      expect(response.body).toHaveProperty('description', 'Updated description');
      expect(response.body).toHaveProperty('riskLevel', 'high');
      expect(response.body).toHaveProperty('updatedBy', 'test-user-id');

      // Check database
      const updatedControl = await Control.findById(control._id);
      expect(updatedControl.name).toBe('Updated Control');
    });

    it('should return 404 if control not found', async () => {
      // Make request with non-existent ID
      await request(app)
        .put('/api/v1/novaassure/controls/5f7d5dc5d7613b3a4c7e9e1a')
        .send({ name: 'Updated Control' })
        .expect(404);
    });
  });

  describe('DELETE /api/v1/novaassure/controls/:id', () => {
    it('should delete an existing control', async () => {
      // Create test control
      const control = await Control.create({
        name: 'Control 1',
        description: 'Test control 1',
        framework: 'soc2',
        category: 'access-control',
        requirements: ['Req 1'],
        testProcedures: ['Test 1'],
        riskLevel: 'medium',
        implementationStatus: 'implemented',
        createdBy: 'test-user-id',
        updatedBy: 'test-user-id'
      });

      // Make request
      const response = await request(app)
        .delete(`/api/v1/novaassure/controls/${control._id}`)
        .expect(200);

      // Assertions
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('message', `Control ${control._id} deleted successfully`);

      // Check database
      const deletedControl = await Control.findById(control._id);
      expect(deletedControl).toBeNull();
    });

    it('should return 404 if control not found', async () => {
      // Make request with non-existent ID
      await request(app)
        .delete('/api/v1/novaassure/controls/5f7d5dc5d7613b3a4c7e9e1a')
        .expect(404);
    });
  });
});
