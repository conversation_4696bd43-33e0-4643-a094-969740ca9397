/**
 * Ψ Tensor Core
 * 
 * This module implements the Ψ Tensor Core for fusing CSDE, CSFE, and CSME engines
 * using tensor operations. It implements the core fusion equation:
 * 
 * Ψ_fused = (Ψ_CSDE ⊗ Ψ_CSFE) ⊕ (Ψ_CSME * π103)
 * 
 * Where:
 * - Ψ_CSDE = [G, D, A1, c1] (Governance, Data, Action, Confidence)
 * - Ψ_CSFE = [R, φ, A2, c2] (Risk, Finance, Action, Confidence)
 * - Ψ_CSME = [B, Γ, A3, c3] (Bio, MedCompliance, Action, Confidence)
 * - ⊗ = Tensor product operator (Kronecker product)
 * - ⊕ = Direct sum operator (matrix block stacking)
 * - π103 = 3141.59 (scaling factor)
 */

const { performance } = require('perf_hooks');
const TensorOperations = require('./tensor_operations');
const DynamicWeightingProtocol = require('./dynamic_weighting');

/**
 * PsiTensorCore class
 * 
 * Implements the Ψ Tensor Core for fusing CSDE, CSFE, and CSME engines.
 */
class PsiTensorCore {
  /**
   * Constructor
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    this.options = {
      enableLogging: true,
      useGPU: false, // GPU acceleration not implemented yet
      useDynamicWeighting: true, // Use dynamic weighting protocol
      ...options
    };

    // Initialize tensor operations
    this.tensorOps = new TensorOperations({
      enableLogging: this.options.enableLogging,
      precision: options.precision || 6
    });

    // Initialize dynamic weighting protocol
    this.dynamicWeighting = new DynamicWeightingProtocol({
      enableLogging: this.options.enableLogging,
      ...options.dynamicWeightingOptions
    });

    // Constants
    this.PI_103 = 3141.59; // π103 scaling factor

    if (this.options.enableLogging) {
      console.log('Ψ Tensor Core initialized with options:', {
        useGPU: this.options.useGPU,
        useDynamicWeighting: this.options.useDynamicWeighting
      });
    }
  }

  /**
   * Create a tensor representation for CSDE
   * @param {number} governance - Governance score (0-1)
   * @param {number} data - Data quality score (0-1)
   * @param {number|string} action - Action score or action string
   * @param {number} confidence - Confidence score (0-1)
   * @returns {Object} - CSDE tensor
   */
  createCsdeTensor(governance, data, action, confidence) {
    // Convert action to numeric value if it's a string
    const actionValue = typeof action === 'string' ? this._actionToValue(action) : action;

    // Create tensor [G, D, A1, c1]
    const values = [governance, data, actionValue, confidence];
    
    return {
      type: 'csde_tensor',
      values,
      dimensions: values.length,
      governance,
      data,
      action: actionValue,
      confidence,
      timestamp: Date.now()
    };
  }

  /**
   * Create a tensor representation for CSFE
   * @param {number} risk - Risk score (0-1)
   * @param {number} finance - Financial score (0-1)
   * @param {number|string} action - Action score or action string
   * @param {number} confidence - Confidence score (0-1)
   * @returns {Object} - CSFE tensor
   */
  createCsfeTensor(risk, finance, action, confidence) {
    // Convert action to numeric value if it's a string
    const actionValue = typeof action === 'string' ? this._actionToValue(action) : action;

    // Create tensor [R, φ, A2, c2]
    const values = [risk, finance, actionValue, confidence];
    
    return {
      type: 'csfe_tensor',
      values,
      dimensions: values.length,
      risk,
      finance,
      action: actionValue,
      confidence,
      timestamp: Date.now()
    };
  }

  /**
   * Create a tensor representation for CSME
   * @param {number} bio - Bio score (0-1)
   * @param {number} medCompliance - Medical compliance score (0-1)
   * @param {number|string} action - Action score or action string
   * @param {number} confidence - Confidence score (0-1)
   * @returns {Object} - CSME tensor
   */
  createCsmeTensor(bio, medCompliance, action, confidence) {
    // Convert action to numeric value if it's a string
    const actionValue = typeof action === 'string' ? this._actionToValue(action) : action;

    // Create tensor [B, Γ, A3, c3]
    const values = [bio, medCompliance, actionValue, confidence];
    
    return {
      type: 'csme_tensor',
      values,
      dimensions: values.length,
      bio,
      medCompliance,
      action: actionValue,
      confidence,
      timestamp: Date.now()
    };
  }

  /**
   * Fuse the CSDE, CSFE, and CSME engines using the core fusion equation:
   * Ψ_fused = (Ψ_CSDE ⊗ Ψ_CSFE) ⊕ (Ψ_CSME * π103)
   * 
   * @param {Object} csdeTensor - CSDE tensor
   * @param {Object} csfeTensor - CSFE tensor
   * @param {Object} csmeTensor - CSME tensor
   * @param {Object} metrics - Engine metrics for dynamic weighting
   * @returns {Object} - Fused tensor
   */
  fuseEngines(csdeTensor, csfeTensor, csmeTensor, metrics = {}) {
    const startTime = performance.now();

    // Apply dynamic weighting if enabled
    if (this.options.useDynamicWeighting && metrics.csde && metrics.csfe && metrics.csme) {
      const weightingResult = this.dynamicWeighting.calculateWeights(
        metrics.csde,
        metrics.csfe,
        metrics.csme
      );

      // Apply weights to tensors
      const weightedTensors = this.dynamicWeighting.applyWeights(
        csdeTensor,
        csfeTensor,
        csmeTensor
      );

      csdeTensor = weightedTensors.csdeTensor;
      csfeTensor = weightedTensors.csfeTensor;
      csmeTensor = weightedTensors.csmeTensor;
    }

    // Step 1: Apply tensor product: (Ψ_CSDE ⊗ Ψ_CSFE)
    const csdeValues = csdeTensor.values || (Array.isArray(csdeTensor) ? csdeTensor : [csdeTensor]);
    const csfeValues = csfeTensor.values || (Array.isArray(csfeTensor) ? csfeTensor : [csfeTensor]);
    
    const csdecsfeProduct = this.tensorOps.tensorProduct(csdeValues, csfeValues);
    
    // Step 2: Apply scaling: (Ψ_CSME * π103)
    const csmeValues = csmeTensor.values || (Array.isArray(csmeTensor) ? csmeTensor : [csmeTensor]);
    const csmeScaled = this.tensorOps.scaleTensor(csmeValues, this.PI_103);
    
    // Step 3: Apply direct sum: (Ψ_CSDE ⊗ Ψ_CSFE) ⊕ (Ψ_CSME * π103)
    const fusedTensor = this.tensorOps.directSum(csdecsfeProduct, csmeScaled);
    
    const endTime = performance.now();
    
    if (this.options.enableLogging) {
      console.log(`Engines fused in ${endTime - startTime}ms`);
      console.log(`CSDE ⊗ CSFE product dimensions: ${csdecsfeProduct.length}`);
      console.log(`CSME * π103 dimensions: ${csmeScaled.length}`);
      console.log(`Fused tensor dimensions: ${fusedTensor.length}`);
    }
    
    return {
      type: 'fused_tensor',
      values: fusedTensor,
      dimensions: fusedTensor.length,
      csdecsfeProduct,
      csmeScaled,
      timestamp: Date.now(),
      processingTime: endTime - startTime
    };
  }

  /**
   * Convert action string to numeric value
   * @param {string} action - Action string
   * @returns {number} - Action value
   * @private
   */
  _actionToValue(action) {
    // Simple mapping of action strings to values
    const actionMap = {
      'allow': 1.0,
      'monitor': 0.7,
      'alert': 0.5,
      'block': 0.0,
      'remediate': 0.3,
      'escalate': 0.2
    };

    return actionMap[action.toLowerCase()] || 0.5;
  }

  /**
   * Extract action from fused tensor
   * @param {Object} fusedTensor - Fused tensor
   * @returns {Object} - Extracted action
   */
  extractAction(fusedTensor) {
    // In a real implementation, this would analyze the fused tensor
    // to determine the consensus action
    
    // For now, use a simple approach: average the values
    const values = fusedTensor.values || (Array.isArray(fusedTensor) ? fusedTensor : [fusedTensor]);
    const average = values.reduce((sum, val) => sum + val, 0) / values.length;
    
    // Map average to action
    let action;
    let confidence;
    
    if (average > 0.8) {
      action = 'allow';
      confidence = 0.9;
    } else if (average > 0.6) {
      action = 'monitor';
      confidence = 0.8;
    } else if (average > 0.4) {
      action = 'alert';
      confidence = 0.7;
    } else if (average > 0.2) {
      action = 'remediate';
      confidence = 0.6;
    } else {
      action = 'block';
      confidence = 0.9;
    }
    
    return {
      action,
      confidence,
      value: average
    };
  }
}

module.exports = PsiTensorCore;
