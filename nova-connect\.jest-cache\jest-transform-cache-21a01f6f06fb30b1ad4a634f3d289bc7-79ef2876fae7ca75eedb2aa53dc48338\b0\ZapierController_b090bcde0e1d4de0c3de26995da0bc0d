708f4a9d4f89e6015169b4b34dbbec73
/**
 * Zapier Controller
 * 
 * This controller handles Zapier integration for NovaConnect UAC.
 */

const ZapierService = require('../services/ZapierService');
const logger = require('../../config/logger');

// Initialize services
const zapierService = new ZapierService();

/**
 * Get Zapier app definition
 */
const getAppDefinition = async (req, res, next) => {
  try {
    const appDefinition = zapierService.getAppDefinition();
    res.status(200).json(appDefinition);
  } catch (error) {
    logger.error('Error getting Zapier app definition:', error);
    next(error);
  }
};

/**
 * Get Zapier triggers
 */
const getTriggers = async (req, res, next) => {
  try {
    const triggers = await zapierService.getTriggers();
    res.status(200).json(triggers);
  } catch (error) {
    logger.error('Error getting Zapier triggers:', error);
    next(error);
  }
};

/**
 * Get Zapier actions
 */
const getActions = async (req, res, next) => {
  try {
    const actions = await zapierService.getActions();
    res.status(200).json(actions);
  } catch (error) {
    logger.error('Error getting Zapier actions:', error);
    next(error);
  }
};

/**
 * OAuth authorization endpoint
 */
const authorizeOAuth = async (req, res, next) => {
  try {
    const {
      client_id,
      redirect_uri,
      state,
      response_type
    } = req.query;

    // Validate client ID
    if (client_id !== zapierService.clientId) {
      return res.status(400).json({
        error: 'invalid_client',
        error_description: 'Invalid client ID'
      });
    }

    // Validate response type
    if (response_type !== 'code') {
      return res.status(400).json({
        error: 'unsupported_response_type',
        error_description: 'Only code response type is supported'
      });
    }

    // In a real implementation, this would redirect to a login page
    // For now, we'll generate a code directly
    const code = Math.random().toString(36).substring(2, 15);

    // Redirect to redirect URI with code and state
    const redirectUrl = new URL(redirect_uri);
    redirectUrl.searchParams.append('code', code);
    redirectUrl.searchParams.append('state', state);
    res.redirect(redirectUrl.toString());
  } catch (error) {
    logger.error('Error authorizing OAuth:', error);
    next(error);
  }
};

/**
 * OAuth token endpoint
 */
const getOAuthToken = async (req, res, next) => {
  try {
    const {
      grant_type,
      code,
      refresh_token,
      redirect_uri,
      client_id,
      client_secret
    } = req.body;

    // Validate client credentials
    if (client_id !== zapierService.clientId || client_secret !== zapierService.clientSecret) {
      return res.status(401).json({
        error: 'invalid_client',
        error_description: 'Invalid client credentials'
      });
    }

    // Handle different grant types
    if (grant_type === 'authorization_code') {
      // Validate code
      if (!code) {
        return res.status(400).json({
          error: 'invalid_request',
          error_description: 'Code is required'
        });
      }

      // Generate access token
      const tokenResponse = await zapierService.generateAccessToken(code, redirect_uri);
      res.status(200).json(tokenResponse);
    } else if (grant_type === 'refresh_token') {
      // Validate refresh token
      if (!refresh_token) {
        return res.status(400).json({
          error: 'invalid_request',
          error_description: 'Refresh token is required'
        });
      }

      // Refresh access token
      const tokenResponse = await zapierService.refreshAccessToken(refresh_token);
      res.status(200).json(tokenResponse);
    } else {
      return res.status(400).json({
        error: 'unsupported_grant_type',
        error_description: 'Unsupported grant type'
      });
    }
  } catch (error) {
    logger.error('Error getting OAuth token:', error);
    res.status(400).json({
      error: 'invalid_grant',
      error_description: error.message
    });
  }
};

/**
 * Before app hook
 */
const beforeApp = async (req, res, next) => {
  try {
    // This hook is called before the Zapier app is loaded
    // It can be used to perform any necessary setup

    res.status(200).json({
      status: 'success',
      message: 'Before app hook executed successfully'
    });
  } catch (error) {
    logger.error('Error executing before app hook:', error);
    next(error);
  }
};

/**
 * After app hook
 */
const afterApp = async (req, res, next) => {
  try {
    // This hook is called after the Zapier app is loaded
    // It can be used to perform any necessary cleanup

    res.status(200).json({
      status: 'success',
      message: 'After app hook executed successfully'
    });
  } catch (error) {
    logger.error('Error executing after app hook:', error);
    next(error);
  }
};

/**
 * New connector trigger
 */
const newConnectorTrigger = async (req, res, next) => {
  try {
    // In a real implementation, this would fetch new connectors since the last poll
    // For now, we'll return sample data

    const connectors = [{
      id: 'conn-123',
      name: 'Sample Connector',
      type: 'api',
      createdAt: new Date().toISOString()
    }];
    res.status(200).json(connectors);
  } catch (error) {
    logger.error('Error executing new connector trigger:', error);
    next(error);
  }
};

/**
 * New workflow trigger
 */
const newWorkflowTrigger = async (req, res, next) => {
  try {
    // In a real implementation, this would fetch new workflows since the last poll
    // For now, we'll return sample data

    const workflows = [{
      id: 'wf-123',
      name: 'Sample Workflow',
      status: 'active',
      createdAt: new Date().toISOString()
    }];
    res.status(200).json(workflows);
  } catch (error) {
    logger.error('Error executing new workflow trigger:', error);
    next(error);
  }
};

/**
 * Compliance event trigger
 */
const complianceEventTrigger = async (req, res, next) => {
  try {
    // In a real implementation, this would fetch new compliance events since the last poll
    // For now, we'll return sample data

    const events = [{
      id: 'evt-123',
      type: 'compliance.violation',
      severity: 'high',
      resource: 'storage-bucket-123',
      details: 'Public access detected',
      timestamp: new Date().toISOString()
    }];
    res.status(200).json(events);
  } catch (error) {
    logger.error('Error executing compliance event trigger:', error);
    next(error);
  }
};

/**
 * Create connector action
 */
const createConnectorAction = async (req, res, next) => {
  try {
    const {
      name,
      type,
      config
    } = req.body;

    // Validate required fields
    if (!name || !type || !config) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Name, type, and config are required'
      });
    }

    // In a real implementation, this would create a connector
    // For now, we'll return sample data

    const connector = {
      id: `conn-${Math.floor(Math.random() * 1000)}`,
      name,
      type,
      config: typeof config === 'string' ? JSON.parse(config) : config,
      createdAt: new Date().toISOString()
    };
    res.status(201).json(connector);
  } catch (error) {
    logger.error('Error executing create connector action:', error);
    next(error);
  }
};

/**
 * Execute workflow action
 */
const executeWorkflowAction = async (req, res, next) => {
  try {
    const {
      workflowId,
      inputs
    } = req.body;

    // Validate required fields
    if (!workflowId) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Workflow ID is required'
      });
    }

    // In a real implementation, this would execute a workflow
    // For now, we'll return sample data

    const execution = {
      id: `exec-${Math.floor(Math.random() * 1000)}`,
      workflowId,
      status: 'completed',
      result: {
        success: true,
        data: inputs ? typeof inputs === 'string' ? JSON.parse(inputs) : inputs : {}
      },
      startedAt: new Date().toISOString(),
      completedAt: new Date().toISOString()
    };
    res.status(200).json(execution);
  } catch (error) {
    logger.error('Error executing workflow action:', error);
    next(error);
  }
};

/**
 * Create compliance evidence action
 */
const createComplianceEvidenceAction = async (req, res, next) => {
  try {
    const {
      controlId,
      evidenceType,
      description,
      data
    } = req.body;

    // Validate required fields
    if (!controlId || !evidenceType || !description) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Control ID, evidence type, and description are required'
      });
    }

    // In a real implementation, this would create compliance evidence
    // For now, we'll return sample data

    const evidence = {
      id: `evid-${Math.floor(Math.random() * 1000)}`,
      controlId,
      evidenceType,
      description,
      data: data ? typeof data === 'string' ? JSON.parse(data) : data : null,
      createdAt: new Date().toISOString()
    };
    res.status(201).json(evidence);
  } catch (error) {
    logger.error('Error executing create compliance evidence action:', error);
    next(error);
  }
};

/**
 * Register Zapier app
 */
const registerApp = async (req, res, next) => {
  try {
    const {
      name,
      description,
      webhookUrl
    } = req.body;

    // Validate required fields
    if (!name || !webhookUrl) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Name and webhook URL are required'
      });
    }

    // Register app
    const app = await zapierService.registerApp({
      name,
      description,
      webhookUrl
    });
    res.status(201).json(app);
  } catch (error) {
    logger.error('Error registering Zapier app:', error);
    next(error);
  }
};

/**
 * Get all Zapier apps
 */
const getAllApps = async (req, res, next) => {
  try {
    const apps = await zapierService.getAllApps();
    res.status(200).json(apps);
  } catch (error) {
    logger.error('Error getting all Zapier apps:', error);
    next(error);
  }
};

/**
 * Get Zapier app by ID
 */
const getAppById = async (req, res, next) => {
  try {
    const {
      id
    } = req.params;
    const app = await zapierService.getAppById(id);
    res.status(200).json(app);
  } catch (error) {
    logger.error('Error getting Zapier app by ID:', error);
    if (error.message.includes('not found')) {
      return res.status(404).json({
        error: 'Not Found',
        message: error.message
      });
    }
    next(error);
  }
};

/**
 * Update Zapier app
 */
const updateApp = async (req, res, next) => {
  try {
    const {
      id
    } = req.params;
    const {
      name,
      description,
      webhookUrl
    } = req.body;

    // Update app
    const app = await zapierService.updateApp(id, {
      name,
      description,
      webhookUrl
    });
    res.status(200).json(app);
  } catch (error) {
    logger.error('Error updating Zapier app:', error);
    if (error.message.includes('not found')) {
      return res.status(404).json({
        error: 'Not Found',
        message: error.message
      });
    }
    next(error);
  }
};

/**
 * Delete Zapier app
 */
const deleteApp = async (req, res, next) => {
  try {
    const {
      id
    } = req.params;
    await zapierService.deleteApp(id);
    res.status(204).end();
  } catch (error) {
    logger.error('Error deleting Zapier app:', error);
    if (error.message.includes('not found')) {
      return res.status(404).json({
        error: 'Not Found',
        message: error.message
      });
    }
    next(error);
  }
};

/**
 * Register Zapier trigger
 */
const registerTrigger = async (req, res, next) => {
  try {
    const {
      key,
      noun,
      display,
      operation
    } = req.body;

    // Validate required fields
    if (!key || !noun || !display || !operation) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Key, noun, display, and operation are required'
      });
    }

    // Register trigger
    const trigger = await zapierService.registerTrigger({
      key,
      noun,
      display,
      operation
    });
    res.status(201).json(trigger);
  } catch (error) {
    logger.error('Error registering Zapier trigger:', error);
    next(error);
  }
};

/**
 * Register Zapier action
 */
const registerAction = async (req, res, next) => {
  try {
    const {
      key,
      noun,
      display,
      operation
    } = req.body;

    // Validate required fields
    if (!key || !noun || !display || !operation) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Key, noun, display, and operation are required'
      });
    }

    // Register action
    const action = await zapierService.registerAction({
      key,
      noun,
      display,
      operation
    });
    res.status(201).json(action);
  } catch (error) {
    logger.error('Error registering Zapier action:', error);
    next(error);
  }
};
module.exports = {
  getAppDefinition,
  getTriggers,
  getActions,
  authorizeOAuth,
  getOAuthToken,
  beforeApp,
  afterApp,
  newConnectorTrigger,
  newWorkflowTrigger,
  complianceEventTrigger,
  createConnectorAction,
  executeWorkflowAction,
  createComplianceEvidenceAction,
  registerApp,
  getAllApps,
  getAppById,
  updateApp,
  deleteApp,
  registerTrigger,
  registerAction
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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