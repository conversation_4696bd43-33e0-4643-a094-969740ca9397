/**
 * Help Context
 * 
 * This module provides a context for contextual help functionality.
 */

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import PropTypes from 'prop-types';

// Create context
const HelpContext = createContext();

/**
 * Use help hook
 * 
 * @returns {Object} Help context
 */
export const useHelp = () => {
  const context = useContext(HelpContext);
  
  if (!context) {
    throw new Error('useHelp must be used within a HelpProvider');
  }
  
  return context;
};

/**
 * Help provider component
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components
 * @param {string} [props.storageKey='novavision_help'] - Local storage key for help state
 * @param {Object} [props.initialHelpContent={}] - Initial help content
 * @param {Function} [props.onHelpOpen] - Function to call when help is opened
 * @param {Function} [props.onHelpClose] - Function to call when help is closed
 * @param {Function} [props.onHelpSearch] - Function to call when help is searched
 * @returns {React.ReactElement} HelpProvider component
 */
export const HelpProvider = ({
  children,
  storageKey = 'novavision_help',
  initialHelpContent = {},
  onHelpOpen,
  onHelpClose,
  onHelpSearch
}) => {
  // State
  const [helpContent, setHelpContent] = useState(initialHelpContent);
  const [activeHelpId, setActiveHelpId] = useState(null);
  const [isPanelOpen, setIsPanelOpen] = useState(false);
  const [isContextualHelpEnabled, setIsContextualHelpEnabled] = useState(true);
  const [viewedHelpIds, setViewedHelpIds] = useState({});
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  
  // Load viewed help IDs from local storage
  useEffect(() => {
    try {
      const storedViewedHelpIds = localStorage.getItem(storageKey);
      
      if (storedViewedHelpIds) {
        setViewedHelpIds(JSON.parse(storedViewedHelpIds));
      }
    } catch (error) {
      console.error('Error loading help state from local storage:', error);
    }
  }, [storageKey]);
  
  // Save viewed help IDs to local storage
  useEffect(() => {
    try {
      localStorage.setItem(storageKey, JSON.stringify(viewedHelpIds));
    } catch (error) {
      console.error('Error saving help state to local storage:', error);
    }
  }, [viewedHelpIds, storageKey]);
  
  /**
   * Register help content
   * 
   * @param {string} helpId - Help ID
   * @param {Object} content - Help content
   */
  const registerHelpContent = useCallback((helpId, content) => {
    setHelpContent(prevContent => ({
      ...prevContent,
      [helpId]: content
    }));
  }, []);
  
  /**
   * Register multiple help content items
   * 
   * @param {Object} contentMap - Map of help ID to content
   */
  const registerMultipleHelpContent = useCallback((contentMap) => {
    setHelpContent(prevContent => ({
      ...prevContent,
      ...contentMap
    }));
  }, []);
  
  /**
   * Open help panel
   * 
   * @param {string} [helpId] - Help ID to show
   */
  const openHelpPanel = useCallback((helpId = null) => {
    setIsPanelOpen(true);
    
    if (helpId) {
      setActiveHelpId(helpId);
      
      // Mark as viewed
      setViewedHelpIds(prev => ({
        ...prev,
        [helpId]: {
          viewed: true,
          timestamp: new Date().toISOString()
        }
      }));
    }
    
    if (onHelpOpen) {
      onHelpOpen(helpId);
    }
  }, [onHelpOpen]);
  
  /**
   * Close help panel
   */
  const closeHelpPanel = useCallback(() => {
    setIsPanelOpen(false);
    
    if (onHelpClose) {
      onHelpClose();
    }
  }, [onHelpClose]);
  
  /**
   * Show contextual help
   * 
   * @param {string} helpId - Help ID to show
   */
  const showContextualHelp = useCallback((helpId) => {
    setActiveHelpId(helpId);
    
    // Mark as viewed
    setViewedHelpIds(prev => ({
      ...prev,
      [helpId]: {
        viewed: true,
        timestamp: new Date().toISOString()
      }
    }));
  }, []);
  
  /**
   * Hide contextual help
   */
  const hideContextualHelp = useCallback(() => {
    setActiveHelpId(null);
  }, []);
  
  /**
   * Toggle contextual help
   * 
   * @param {string} helpId - Help ID to toggle
   */
  const toggleContextualHelp = useCallback((helpId) => {
    if (activeHelpId === helpId) {
      hideContextualHelp();
    } else {
      showContextualHelp(helpId);
    }
  }, [activeHelpId, showContextualHelp, hideContextualHelp]);
  
  /**
   * Enable contextual help
   */
  const enableContextualHelp = useCallback(() => {
    setIsContextualHelpEnabled(true);
  }, []);
  
  /**
   * Disable contextual help
   */
  const disableContextualHelp = useCallback(() => {
    setIsContextualHelpEnabled(false);
  }, []);
  
  /**
   * Toggle contextual help enabled state
   */
  const toggleContextualHelpEnabled = useCallback(() => {
    setIsContextualHelpEnabled(prev => !prev);
  }, []);
  
  /**
   * Search help content
   * 
   * @param {string} query - Search query
   * @returns {Array} Search results
   */
  const searchHelp = useCallback((query) => {
    setSearchQuery(query);
    
    if (!query) {
      setSearchResults([]);
      return [];
    }
    
    const results = Object.entries(helpContent)
      .filter(([helpId, content]) => {
        const title = content.title || '';
        const description = content.description || '';
        const keywords = content.keywords || [];
        
        const lowerQuery = query.toLowerCase();
        
        return (
          title.toLowerCase().includes(lowerQuery) ||
          description.toLowerCase().includes(lowerQuery) ||
          keywords.some(keyword => keyword.toLowerCase().includes(lowerQuery))
        );
      })
      .map(([helpId, content]) => ({
        helpId,
        ...content
      }));
    
    setSearchResults(results);
    
    if (onHelpSearch) {
      onHelpSearch(query, results);
    }
    
    return results;
  }, [helpContent, onHelpSearch]);
  
  /**
   * Check if help has been viewed
   * 
   * @param {string} helpId - Help ID to check
   * @returns {boolean} Whether help has been viewed
   */
  const isHelpViewed = useCallback((helpId) => {
    return !!viewedHelpIds[helpId]?.viewed;
  }, [viewedHelpIds]);
  
  /**
   * Reset viewed help
   * 
   * @param {string} [helpId] - Help ID to reset (if not provided, all will be reset)
   */
  const resetViewedHelp = useCallback((helpId) => {
    if (helpId) {
      setViewedHelpIds(prev => {
        const newViewedHelpIds = { ...prev };
        delete newViewedHelpIds[helpId];
        return newViewedHelpIds;
      });
    } else {
      setViewedHelpIds({});
    }
  }, []);
  
  /**
   * Get help content
   * 
   * @param {string} helpId - Help ID
   * @returns {Object|null} Help content
   */
  const getHelpContent = useCallback((helpId) => {
    return helpContent[helpId] || null;
  }, [helpContent]);
  
  // Context value
  const value = {
    helpContent,
    activeHelpId,
    isPanelOpen,
    isContextualHelpEnabled,
    viewedHelpIds,
    searchQuery,
    searchResults,
    registerHelpContent,
    registerMultipleHelpContent,
    openHelpPanel,
    closeHelpPanel,
    showContextualHelp,
    hideContextualHelp,
    toggleContextualHelp,
    enableContextualHelp,
    disableContextualHelp,
    toggleContextualHelpEnabled,
    searchHelp,
    isHelpViewed,
    resetViewedHelp,
    getHelpContent
  };
  
  return (
    <HelpContext.Provider value={value}>
      {children}
    </HelpContext.Provider>
  );
};

HelpProvider.propTypes = {
  children: PropTypes.node.isRequired,
  storageKey: PropTypes.string,
  initialHelpContent: PropTypes.object,
  onHelpOpen: PropTypes.func,
  onHelpClose: PropTypes.func,
  onHelpSearch: PropTypes.func
};

export default HelpContext;
