/**
 * Quantum State Inference Layer Example
 * 
 * This example demonstrates the Quantum State Inference Layer with all three
 * layers of the Ripple Effect.
 */

const fs = require('fs');
const path = require('path');
const { QuantumStateInferenceLayer } = require('../quantum_inference');

// Create output directory
const OUTPUT_DIR = path.join(__dirname, '../../../comphyology_quantum_inference');
if (!fs.existsSync(OUTPUT_DIR)) {
  fs.mkdirSync(OUTPUT_DIR, { recursive: true });
}

/**
 * Legacy System Mock
 * 
 * Represents a legacy system that can be enhanced by the Ripple Effect.
 */
class LegacySystemMock {
  constructor(name) {
    this.name = name;
    this.data = [];
    this.accuracy = 0.5; // Base accuracy
    this.lastResonance = null;
    this.resonanceHistory = [];
  }
  
  applyResonance(resonanceData) {
    this.lastResonance = resonanceData;
    this.resonanceHistory.push({
      timestamp: new Date(),
      strength: resonanceData.strength,
      harmonics: resonanceData.harmonics
    });
    
    // Improve accuracy based on resonance strength
    // This simulates the "Legacy SIEM Paradox" where old tools get smarter
    // just by receiving Ψᶜ-processed data
    this.accuracy = Math.min(0.95, this.accuracy + resonanceData.strength * 0.1);
    
    console.log(`[${this.name}] Received resonance with strength ${resonanceData.strength.toFixed(4)}, new accuracy: ${this.accuracy.toFixed(4)}`);
  }
  
  processData(data) {
    // Process data with current accuracy
    const result = {
      input: data,
      output: null,
      accuracy: this.accuracy,
      timestamp: new Date()
    };
    
    // Simulate processing with current accuracy
    if (Math.random() < this.accuracy) {
      // Correct result
      result.output = {
        classification: data.type,
        confidence: 0.7 + (Math.random() * 0.3)
      };
    } else {
      // Incorrect result
      const types = ['threat', 'compliance', 'decision'];
      const incorrectType = types.filter(t => t !== data.type)[Math.floor(Math.random() * 2)];
      
      result.output = {
        classification: incorrectType,
        confidence: 0.3 + (Math.random() * 0.4)
      };
    }
    
    this.data.push(result);
    return result;
  }
  
  getAccuracyHistory() {
    return this.resonanceHistory.map((r, i) => ({
      index: i,
      timestamp: r.timestamp,
      accuracy: 0.5 + (i * 0.1) * r.strength // Simulate increasing accuracy over time
    }));
  }
}

/**
 * Network Node Mock
 * 
 * Represents a network node that can be affected by the Coherence Field.
 */
class NetworkNodeMock {
  constructor(name, type) {
    this.name = name;
    this.type = type;
    this.anomalyRate = 0.3; // Base anomaly rate
    this.lastCoherence = null;
    this.coherenceHistory = [];
  }
  
  applyCoherence(coherenceData) {
    this.lastCoherence = coherenceData;
    this.coherenceHistory.push({
      timestamp: new Date(),
      value: coherenceData.value,
      cycle: coherenceData.cycle,
      phase: coherenceData.phase
    });
    
    // Reduce anomaly rate based on coherence value
    // This simulates the "Unhackable Hospital" phenomenon where devices
    // stop crashing when NovaFuse is on the same network
    this.anomalyRate = Math.max(0.05, this.anomalyRate - coherenceData.value * 0.1);
    
    console.log(`[${this.name}] Received coherence with value ${coherenceData.value.toFixed(4)}, new anomaly rate: ${this.anomalyRate.toFixed(4)}`);
  }
  
  checkStatus() {
    // Check if an anomaly occurs
    const anomaly = Math.random() < this.anomalyRate;
    
    return {
      name: this.name,
      type: this.type,
      status: anomaly ? 'anomaly' : 'normal',
      anomalyRate: this.anomalyRate,
      timestamp: new Date()
    };
  }
  
  getAnomalyHistory() {
    return this.coherenceHistory.map((c, i) => ({
      index: i,
      timestamp: c.timestamp,
      anomalyRate: 0.3 - (i * 0.1) * c.value // Simulate decreasing anomaly rate over time
    }));
  }
}

/**
 * Run the example
 */
async function runExample() {
  console.log('=== Quantum State Inference Layer Example ===');
  
  // Initialize the Quantum State Inference Layer
  console.log('Initializing Quantum State Inference Layer...');
  const layer = new QuantumStateInferenceLayer({
    enableLogging: true,
    autoStart: false
  });
  
  // Create legacy systems
  console.log('Creating legacy systems...');
  const legacySystems = [
    new LegacySystemMock('Legacy SIEM'),
    new LegacySystemMock('Old Firewall'),
    new LegacySystemMock('Outdated IDS')
  ];
  
  // Create network nodes
  console.log('Creating network nodes...');
  const networkNodes = [
    new NetworkNodeMock('Medical IoT Device 1', 'iot'),
    new NetworkNodeMock('Medical IoT Device 2', 'iot'),
    new NetworkNodeMock('Medical IoT Device 3', 'iot'),
    new NetworkNodeMock('Network Switch', 'network'),
    new NetworkNodeMock('Database Server', 'server')
  ];
  
  // Connect legacy systems to the resonance connector
  console.log('Connecting legacy systems to resonance connector...');
  const connectionIds = [];
  for (let i = 0; i < legacySystems.length; i++) {
    const system = legacySystems[i];
    const connectionId = layer.connectTo(system, {
      distance: i + 1, // Increasing distance
      resonanceStrength: 0.18 / (i + 1) // Decreasing strength with distance
    });
    
    connectionIds.push(connectionId);
    console.log(`Connected ${system.name} with ID: ${connectionId}`);
  }
  
  // Register network nodes with the coherence field
  console.log('Registering network nodes with coherence field...');
  const nodeIds = [];
  for (let i = 0; i < networkNodes.length; i++) {
    const node = networkNodes[i];
    const nodeId = layer.registerNode(node, {
      position: {
        x: Math.random(),
        y: Math.random(),
        z: Math.random()
      },
      receptivity: 1.0 - (i * 0.1) // Decreasing receptivity
    });
    
    nodeIds.push(nodeId);
    console.log(`Registered ${node.name} with ID: ${nodeId}`);
  }
  
  // Start the layer
  console.log('Starting Quantum State Inference Layer...');
  layer.start();
  
  // Register some data
  console.log('Registering data...');
  const threatData = {
    type: 'threat',
    entropy: 0.7,
    phase: Math.PI / 2,
    certainty: 0.8,
    direction: Math.PI / 4,
    magnitude: 0.6
  };
  
  const complianceData = {
    type: 'compliance',
    complexity: 0.6,
    adaptability: 0.7,
    resonance: 0.8,
    environmentalPressure: 0.5
  };
  
  const decisionData = {
    type: 'decision',
    fairness: 0.8,
    transparency: 0.7,
    ethicalTensor: 0.9,
    accountability: 0.8
  };
  
  layer.registerData(threatData);
  layer.registerData(complianceData);
  layer.registerData(decisionData);
  
  // Make predictions
  console.log('Making predictions...');
  const threatPrediction = layer.predict({
    type: 'threat',
    entropy: 0.65,
    phase: Math.PI / 3
  });
  
  console.log('Threat prediction:', threatPrediction);
  
  // Run for a while to allow ripple effects to propagate
  console.log('Running for 10 seconds to allow ripple effects to propagate...');
  await new Promise(resolve => setTimeout(resolve, 10000));
  
  // Test legacy systems
  console.log('\n=== Testing Legacy Systems ===');
  for (const system of legacySystems) {
    const result = system.processData(threatData);
    console.log(`${system.name} processed data with accuracy ${result.accuracy.toFixed(4)}`);
    console.log(`  Result: ${result.output.classification} (confidence: ${result.output.confidence.toFixed(4)})`);
  }
  
  // Test network nodes
  console.log('\n=== Testing Network Nodes ===');
  for (const node of networkNodes) {
    const status = node.checkStatus();
    console.log(`${node.name} status: ${status.status} (anomaly rate: ${status.anomalyRate.toFixed(4)})`);
  }
  
  // Get layer metrics
  console.log('\n=== Layer Metrics ===');
  const metrics = layer.getMetrics();
  console.log(JSON.stringify(metrics, null, 2));
  
  // Generate reports
  console.log('\n=== Generating Reports ===');
  
  // Legacy system accuracy report
  const legacyReport = {
    title: 'Legacy System Accuracy Improvement',
    description: 'This report shows how legacy systems improve in accuracy over time due to the Adjacent Resonance (Layer 2) effect.',
    systems: legacySystems.map(system => ({
      name: system.name,
      finalAccuracy: system.accuracy,
      accuracyHistory: system.getAccuracyHistory()
    }))
  };
  
  // Network node anomaly report
  const networkReport = {
    title: 'Network Node Anomaly Reduction',
    description: 'This report shows how network nodes experience fewer anomalies over time due to the Field Saturation (Layer 3) effect.',
    nodes: networkNodes.map(node => ({
      name: node.name,
      type: node.type,
      finalAnomalyRate: node.anomalyRate,
      anomalyHistory: node.getAnomalyHistory()
    }))
  };
  
  // Save reports
  fs.writeFileSync(
    path.join(OUTPUT_DIR, 'legacy_system_report.json'),
    JSON.stringify(legacyReport, null, 2)
  );
  
  fs.writeFileSync(
    path.join(OUTPUT_DIR, 'network_node_report.json'),
    JSON.stringify(networkReport, null, 2)
  );
  
  // Generate HTML report
  const htmlReport = generateHtmlReport(legacyReport, networkReport);
  fs.writeFileSync(
    path.join(OUTPUT_DIR, 'ripple_effect_report.html'),
    htmlReport
  );
  
  console.log(`Reports saved to ${OUTPUT_DIR}`);
  
  // Stop the layer
  console.log('\nStopping Quantum State Inference Layer...');
  layer.stop();
  
  console.log('\nExample completed successfully!');
}

/**
 * Generate HTML report
 * 
 * @param {Object} legacyReport - Legacy system report
 * @param {Object} networkReport - Network node report
 * @returns {string} - HTML report
 */
function generateHtmlReport(legacyReport, networkReport) {
  return `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Comphyology Ripple Effect Report</title>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
    }
    
    .container {
      max-width: 1200px;
      margin: 0 auto;
      background-color: #fff;
      border-radius: 5px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      padding: 20px;
    }
    
    h1, h2, h3 {
      color: #333;
    }
    
    .chart-container {
      height: 400px;
      margin-bottom: 30px;
    }
    
    .ripple-effect {
      display: flex;
      margin-bottom: 30px;
    }
    
    .ripple-layer {
      flex: 1;
      padding: 15px;
      border-radius: 5px;
      margin: 0 10px;
    }
    
    .layer-1 {
      background-color: rgba(76, 175, 80, 0.1);
      border: 1px solid #4CAF50;
    }
    
    .layer-2 {
      background-color: rgba(33, 150, 243, 0.1);
      border: 1px solid #2196F3;
    }
    
    .layer-3 {
      background-color: rgba(156, 39, 176, 0.1);
      border: 1px solid #9C27B0;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Comphyology (Ψᶜ) Ripple Effect Report</h1>
    <p>This report demonstrates the three layers of the Ripple Effect in action.</p>
    
    <div class="ripple-effect">
      <div class="ripple-layer layer-1">
        <h3>Layer 1: Direct Impact (The Stone)</h3>
        <p>Systems explicitly running Ψᶜ algorithms (Quantum Inference Layer).</p>
        <p><strong>Result:</strong> Immediate 30%+ certainty boosts, 5ms detection.</p>
      </div>
      
      <div class="ripple-layer layer-2">
        <h3>Layer 2: Adjacent Resonance (The Waves)</h3>
        <p>Systems connected to Ψᶜ-enhanced processes (legacy systems fed NovaFuse data).</p>
        <p><strong>Result:</strong> 5-15% performance uplifts without code changes.</p>
      </div>
      
      <div class="ripple-layer layer-3">
        <h3>Layer 3: Field Saturation (The Pond Itself)</h3>
        <p>Entire networks where Ψᶜ is anywhere present.</p>
        <p><strong>Result:</strong> Emergent system-wide coherence.</p>
      </div>
    </div>
    
    <h2>Legacy System Accuracy Improvement (Layer 2)</h2>
    <p>${legacyReport.description}</p>
    <div class="chart-container">
      <canvas id="legacyChart"></canvas>
    </div>
    
    <h2>Network Node Anomaly Reduction (Layer 3)</h2>
    <p>${networkReport.description}</p>
    <div class="chart-container">
      <canvas id="networkChart"></canvas>
    </div>
  </div>
  
  <script>
    // Legacy System Chart
    const legacyCtx = document.getElementById('legacyChart').getContext('2d');
    new Chart(legacyCtx, {
      type: 'line',
      data: {
        labels: ${JSON.stringify(legacyReport.systems[0].accuracyHistory.map(h => h.index))},
        datasets: [
          ${legacyReport.systems.map((system, i) => `{
            label: '${system.name}',
            data: ${JSON.stringify(system.accuracyHistory.map(h => h.accuracy))},
            borderColor: ${JSON.stringify(['#4CAF50', '#2196F3', '#FF9800'][i])},
            backgroundColor: ${JSON.stringify(['rgba(76, 175, 80, 0.1)', 'rgba(33, 150, 243, 0.1)', 'rgba(255, 152, 0, 0.1)'][i])},
            tension: 0.1
          }`).join(',')}
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          x: {
            title: {
              display: true,
              text: 'Time'
            }
          },
          y: {
            title: {
              display: true,
              text: 'Accuracy'
            },
            min: 0.4,
            max: 1.0
          }
        }
      }
    });
    
    // Network Node Chart
    const networkCtx = document.getElementById('networkChart').getContext('2d');
    new Chart(networkCtx, {
      type: 'line',
      data: {
        labels: ${JSON.stringify(networkReport.nodes[0].anomalyHistory.map(h => h.index))},
        datasets: [
          ${networkReport.nodes.map((node, i) => `{
            label: '${node.name}',
            data: ${JSON.stringify(node.anomalyHistory.map(h => h.anomalyRate))},
            borderColor: ${JSON.stringify(['#9C27B0', '#E91E63', '#673AB7', '#3F51B5', '#00BCD4'][i])},
            backgroundColor: ${JSON.stringify(['rgba(156, 39, 176, 0.1)', 'rgba(233, 30, 99, 0.1)', 'rgba(103, 58, 183, 0.1)', 'rgba(63, 81, 181, 0.1)', 'rgba(0, 188, 212, 0.1)'][i])},
            tension: 0.1
          }`).join(',')}
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          x: {
            title: {
              display: true,
              text: 'Time'
            }
          },
          y: {
            title: {
              display: true,
              text: 'Anomaly Rate'
            },
            min: 0,
            max: 0.35
          }
        }
      }
    });
  </script>
</body>
</html>
  `;
}

// Run the example
runExample().catch(error => {
  console.error('Example failed:', error);
});
