/**
 * Load Balancer
 *
 * This module provides load balancing capabilities for the Finite Universe
 * Principle defense system, distributing tasks across multiple nodes.
 */

const EventEmitter = require('events');

/**
 * LoadBalancer class
 * 
 * Distributes tasks across multiple nodes based on various strategies.
 */
class LoadBalancer extends EventEmitter {
  /**
   * Constructor
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();

    this.options = {
      enableLogging: true,
      strategy: options.strategy || 'round-robin', // 'round-robin', 'least-connections', 'weighted', 'random'
      healthCheckInterval: options.healthCheckInterval || 10000, // 10 seconds
      failureThreshold: options.failureThreshold || 3,
      successThreshold: options.successThreshold || 2,
      timeout: options.timeout || 5000, // 5 seconds
      retryCount: options.retryCount || 3,
      retryDelay: options.retryDelay || 1000, // 1 second
      ...options
    };

    // Initialize nodes registry
    this.nodes = new Map();
    
    // Initialize node health
    this.nodeHealth = new Map();
    
    // Initialize node weights (for weighted strategy)
    this.nodeWeights = new Map();
    
    // Initialize node connections (for least-connections strategy)
    this.nodeConnections = new Map();
    
    // Initialize round-robin index
    this.roundRobinIndex = 0;
    
    // Initialize health check interval
    this.healthCheckInterval = null;

    if (this.options.enableLogging) {
      console.log('LoadBalancer initialized with options:', this.options);
    }
  }

  /**
   * Start the load balancer
   */
  start() {
    // Start health check interval
    this.healthCheckInterval = setInterval(() => {
      this._checkNodeHealth();
    }, this.options.healthCheckInterval);
    
    if (this.options.enableLogging) {
      console.log('LoadBalancer started');
    }
    
    // Emit start event
    this.emit('start');
  }

  /**
   * Stop the load balancer
   */
  stop() {
    // Clear health check interval
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }
    
    if (this.options.enableLogging) {
      console.log('LoadBalancer stopped');
    }
    
    // Emit stop event
    this.emit('stop');
  }

  /**
   * Register a node with the load balancer
   * @param {Object} nodeInfo - Node information
   * @param {number} weight - Node weight (for weighted strategy)
   * @returns {boolean} - True if node was registered, false otherwise
   */
  registerNode(nodeInfo, weight = 1) {
    if (!nodeInfo || !nodeInfo.id) {
      if (this.options.enableLogging) {
        console.log('Invalid node information:', nodeInfo);
      }
      return false;
    }
    
    // Register node
    this.nodes.set(nodeInfo.id, nodeInfo);
    
    // Initialize node health
    this.nodeHealth.set(nodeInfo.id, {
      healthy: true,
      failureCount: 0,
      successCount: 0,
      lastCheck: Date.now(),
      lastFailure: null,
      lastSuccess: Date.now()
    });
    
    // Initialize node weight
    this.nodeWeights.set(nodeInfo.id, weight);
    
    // Initialize node connections
    this.nodeConnections.set(nodeInfo.id, 0);
    
    if (this.options.enableLogging) {
      console.log(`Node ${nodeInfo.id} registered with weight ${weight}`);
    }
    
    // Emit node-registered event
    this.emit('node-registered', { nodeId: nodeInfo.id, nodeInfo, weight });
    
    return true;
  }

  /**
   * Unregister a node from the load balancer
   * @param {string} nodeId - Node ID
   * @returns {boolean} - True if node was unregistered, false otherwise
   */
  unregisterNode(nodeId) {
    // Check if node exists
    if (!this.nodes.has(nodeId)) {
      if (this.options.enableLogging) {
        console.log(`Node ${nodeId} not found`);
      }
      return false;
    }
    
    // Get node information
    const nodeInfo = this.nodes.get(nodeId);
    
    // Unregister node
    this.nodes.delete(nodeId);
    this.nodeHealth.delete(nodeId);
    this.nodeWeights.delete(nodeId);
    this.nodeConnections.delete(nodeId);
    
    if (this.options.enableLogging) {
      console.log(`Node ${nodeId} unregistered`);
    }
    
    // Emit node-unregistered event
    this.emit('node-unregistered', { nodeId, nodeInfo });
    
    return true;
  }

  /**
   * Update node weight
   * @param {string} nodeId - Node ID
   * @param {number} weight - Node weight
   * @returns {boolean} - True if node weight was updated, false otherwise
   */
  updateNodeWeight(nodeId, weight) {
    // Check if node exists
    if (!this.nodes.has(nodeId)) {
      if (this.options.enableLogging) {
        console.log(`Node ${nodeId} not found`);
      }
      return false;
    }
    
    // Update node weight
    this.nodeWeights.set(nodeId, weight);
    
    if (this.options.enableLogging) {
      console.log(`Node ${nodeId} weight updated to ${weight}`);
    }
    
    // Emit node-weight-updated event
    this.emit('node-weight-updated', { nodeId, weight });
    
    return true;
  }

  /**
   * Get next node for processing
   * @param {Object} task - Task to process
   * @param {string} domain - Domain of the task
   * @returns {Object} - Node information
   */
  getNextNode(task, domain = '') {
    // Get healthy nodes
    const healthyNodes = this._getHealthyNodes();
    
    // Check if there are any healthy nodes
    if (healthyNodes.length === 0) {
      if (this.options.enableLogging) {
        console.log('No healthy nodes available');
      }
      return null;
    }
    
    // Get next node based on strategy
    let nextNode;
    
    switch (this.options.strategy) {
      case 'round-robin':
        nextNode = this._getRoundRobinNode(healthyNodes);
        break;
      case 'least-connections':
        nextNode = this._getLeastConnectionsNode(healthyNodes);
        break;
      case 'weighted':
        nextNode = this._getWeightedNode(healthyNodes);
        break;
      case 'random':
        nextNode = this._getRandomNode(healthyNodes);
        break;
      default:
        nextNode = this._getRoundRobinNode(healthyNodes);
    }
    
    if (this.options.enableLogging) {
      console.log(`Selected node ${nextNode.id} for task in domain ${domain}`);
    }
    
    // Increment node connections
    this.nodeConnections.set(nextNode.id, this.nodeConnections.get(nextNode.id) + 1);
    
    // Emit node-selected event
    this.emit('node-selected', { nodeId: nextNode.id, task, domain });
    
    return nextNode;
  }

  /**
   * Release node after processing
   * @param {string} nodeId - Node ID
   * @param {boolean} success - Whether processing was successful
   */
  releaseNode(nodeId, success = true) {
    // Check if node exists
    if (!this.nodes.has(nodeId)) {
      if (this.options.enableLogging) {
        console.log(`Node ${nodeId} not found`);
      }
      return;
    }
    
    // Decrement node connections
    const connections = this.nodeConnections.get(nodeId);
    this.nodeConnections.set(nodeId, Math.max(0, connections - 1));
    
    // Update node health
    const health = this.nodeHealth.get(nodeId);
    
    if (success) {
      // Reset failure count and increment success count
      health.successCount = Math.min(health.successCount + 1, this.options.successThreshold);
      health.failureCount = 0;
      health.lastSuccess = Date.now();
      
      // Mark as healthy if success threshold reached
      if (!health.healthy && health.successCount >= this.options.successThreshold) {
        health.healthy = true;
        
        if (this.options.enableLogging) {
          console.log(`Node ${nodeId} marked as healthy`);
        }
        
        // Emit node-healthy event
        this.emit('node-healthy', { nodeId });
      }
    } else {
      // Increment failure count and reset success count
      health.failureCount = health.failureCount + 1;
      health.successCount = 0;
      health.lastFailure = Date.now();
      
      // Mark as unhealthy if failure threshold reached
      if (health.healthy && health.failureCount >= this.options.failureThreshold) {
        health.healthy = false;
        
        if (this.options.enableLogging) {
          console.log(`Node ${nodeId} marked as unhealthy`);
        }
        
        // Emit node-unhealthy event
        this.emit('node-unhealthy', { nodeId });
      }
    }
    
    // Update node health
    this.nodeHealth.set(nodeId, health);
    
    // Emit node-released event
    this.emit('node-released', { nodeId, success });
  }

  /**
   * Get healthy nodes
   * @returns {Array} - Array of healthy node information
   * @private
   */
  _getHealthyNodes() {
    const healthyNodes = [];
    
    for (const [nodeId, nodeInfo] of this.nodes.entries()) {
      const health = this.nodeHealth.get(nodeId);
      
      if (health && health.healthy) {
        healthyNodes.push(nodeInfo);
      }
    }
    
    return healthyNodes;
  }

  /**
   * Get next node using round-robin strategy
   * @param {Array} nodes - Array of node information
   * @returns {Object} - Node information
   * @private
   */
  _getRoundRobinNode(nodes) {
    // Get next node
    const node = nodes[this.roundRobinIndex];
    
    // Update round-robin index
    this.roundRobinIndex = (this.roundRobinIndex + 1) % nodes.length;
    
    return node;
  }

  /**
   * Get next node using least-connections strategy
   * @param {Array} nodes - Array of node information
   * @returns {Object} - Node information
   * @private
   */
  _getLeastConnectionsNode(nodes) {
    let minConnections = Infinity;
    let selectedNode = null;
    
    for (const node of nodes) {
      const connections = this.nodeConnections.get(node.id);
      
      if (connections < minConnections) {
        minConnections = connections;
        selectedNode = node;
      }
    }
    
    return selectedNode;
  }

  /**
   * Get next node using weighted strategy
   * @param {Array} nodes - Array of node information
   * @returns {Object} - Node information
   * @private
   */
  _getWeightedNode(nodes) {
    let totalWeight = 0;
    
    // Calculate total weight
    for (const node of nodes) {
      totalWeight += this.nodeWeights.get(node.id);
    }
    
    // Get random weight
    const randomWeight = Math.random() * totalWeight;
    
    // Find node with weight
    let currentWeight = 0;
    
    for (const node of nodes) {
      currentWeight += this.nodeWeights.get(node.id);
      
      if (randomWeight <= currentWeight) {
        return node;
      }
    }
    
    // Fallback to first node
    return nodes[0];
  }

  /**
   * Get next node using random strategy
   * @param {Array} nodes - Array of node information
   * @returns {Object} - Node information
   * @private
   */
  _getRandomNode(nodes) {
    const randomIndex = Math.floor(Math.random() * nodes.length);
    return nodes[randomIndex];
  }

  /**
   * Check node health
   * @private
   */
  _checkNodeHealth() {
    // In a real implementation, this would check the health of each node
    // For now, we'll just emit an event
    this.emit('health-check');
  }

  /**
   * Dispose resources
   */
  dispose() {
    this.stop();
    
    // Clear registries
    this.nodes.clear();
    this.nodeHealth.clear();
    this.nodeWeights.clear();
    this.nodeConnections.clear();
    
    if (this.options.enableLogging) {
      console.log('LoadBalancer disposed');
    }
  }
}

/**
 * Create a load balancer with recommended settings
 * @param {Object} options - Configuration options
 * @returns {LoadBalancer} - Configured load balancer
 */
function createLoadBalancer(options = {}) {
  return new LoadBalancer({
    enableLogging: true,
    strategy: 'round-robin',
    ...options
  });
}

module.exports = {
  LoadBalancer,
  createLoadBalancer
};
