# Cyber-Safety Visualization API

This document provides documentation for the Cyber-Safety Visualization API endpoints. These endpoints provide data for the Cyber-Safety fusion visualizations.

## Base URL

All API endpoints are relative to the base URL:

```
/api/cyber-safety/visualizations
```

## Authentication

Most endpoints require authentication. Include an authentication token in the `Authorization` header:

```
Authorization: Bearer <token>
```

## Endpoints

### Tri-Domain Tensor Visualization

#### GET /tri-domain-tensor

Returns data for the Tri-Domain Tensor Visualization, which shows how data flows between GRC, IT, and Cybersecurity domains.

**Query Parameters:**

| Parameter | Type | Description |
|-----------|------|-------------|
| timeframe | string | Optional. Time frame for the data (e.g., '30d', '1w', '6m'). Default: '30d' |
| domains | string | Optional. Comma-separated list of domains to include. Default: 'grc,it,cybersecurity' |

**Response:**

```json
{
  "success": true,
  "data": {
    "grc": {
      "values": [
        { "timestamp": "2023-01-01T00:00:00Z", "value": 0.75 },
        ...
      ],
      "health": 0.8,
      "entropyContainment": 0.03
    },
    "it": {
      "values": [...],
      "health": 0.85,
      "entropyContainment": 0.02
    },
    "cybersecurity": {
      "values": [...],
      "health": 0.7,
      "entropyContainment": 0.05
    },
    "connections": [
      {
        "source": "grc",
        "target": "it",
        "strength": 0.7
      },
      ...
    ]
  }
}
```

### Cyber-Safety Harmony Index

#### GET /harmony-index

Returns data for the Cyber-Safety Harmony Index, which displays alignment and harmony between domains using the 3-6-9-12-13 pattern.

**Query Parameters:**

| Parameter | Type | Description |
|-----------|------|-------------|
| timeframe | string | Optional. Time frame for the data (e.g., '30d', '1w', '6m'). Default: '30d' |
| historyPoints | number | Optional. Number of history points to include. Default: 10 |

**Response:**

```json
{
  "success": true,
  "data": {
    "domainData": {
      "grc": {
        "score": 0.7,
        "metrics": {
          "governance": 0.6,
          "risk": 0.7,
          "compliance": 0.8
        }
      },
      "it": {
        "score": 0.8,
        "metrics": {
          "infrastructure": 0.8,
          "applications": 0.7,
          "data": 0.9
        }
      },
      "cybersecurity": {
        "score": 0.6,
        "metrics": {
          "prevention": 0.5,
          "detection": 0.6,
          "response": 0.7
        }
      }
    },
    "harmonyHistory": [0.65, 0.68, 0.72, 0.69, 0.71, 0.73, 0.72, 0.74, 0.73, 0.75]
  }
}
```

### Risk-Control Fusion Map

#### GET /risk-control-fusion

Returns data for the Risk-Control Fusion Map, which maps risks across domains to controls and shows coverage.

**Query Parameters:**

| Parameter | Type | Description |
|-----------|------|-------------|
| timeframe | string | Optional. Time frame for the data (e.g., '30d', '1w', '6m'). Default: '30d' |
| domains | string | Optional. Comma-separated list of domains to include. Default: 'grc,it,cybersecurity' |
| categories | string | Optional. Comma-separated list of categories to include. Default: all categories |

**Response:**

```json
{
  "success": true,
  "data": {
    "riskData": {
      "grc": {
        "governance": 0.6,
        "risk": 0.7,
        "compliance": 0.8
      },
      "it": {
        "infrastructure": 0.8,
        "applications": 0.7,
        "data": 0.9
      },
      "cybersecurity": {
        "prevention": 0.5,
        "detection": 0.6,
        "response": 0.7
      }
    },
    "controlData": {
      "grc": {
        "governance": 0.7,
        "risk": 0.6,
        "compliance": 0.9
      },
      "it": {
        "infrastructure": 0.7,
        "applications": 0.8,
        "data": 0.8
      },
      "cybersecurity": {
        "prevention": 0.6,
        "detection": 0.7,
        "response": 0.8
      }
    }
  }
}
```

### Cyber-Safety Resonance Spectrogram

#### GET /resonance-spectrogram

Returns data for the Cyber-Safety Resonance Spectrogram, which shows 3D resonance patterns for domain interactions.

**Query Parameters:**

| Parameter | Type | Description |
|-----------|------|-------------|
| timeframe | string | Optional. Time frame for the data (e.g., '30d', '1w', '6m'). Default: '30d' |
| domains | string | Optional. Comma-separated list of domains to include. Default: 'grc,it,cybersecurity' |
| predictionHorizon | number | Optional. Number of time steps to predict into the future. Default: 10 |

**Response:**

```json
{
  "success": true,
  "data": {
    "domainData": {
      "grc": {
        "values": [...],
        "frequency": 0.4,
        "amplitude": 0.8,
        "phase": 0
      },
      "it": {
        "values": [...],
        "frequency": 0.7,
        "amplitude": 0.9,
        "phase": 1.047
      },
      "cybersecurity": {
        "values": [...],
        "frequency": 0.9,
        "amplitude": 0.7,
        "phase": 1.571
      },
      "crossDomainFlows": [
        {
          "source": "grc",
          "target": "it",
          "strength": 0.7,
          "frequency": 0.5
        },
        ...
      ]
    },
    "predictionData": {
      "timeHorizon": 10,
      "dissonanceProbability": 0.3,
      "criticalPoints": [
        {
          "timeStep": 3,
          "severity": 0.7,
          "description": "Potential dissonance at time step 3"
        },
        ...
      ]
    }
  }
}
```

### Unified Compliance-Security Visualizer

#### GET /unified-compliance-security

Returns data for the Unified Compliance-Security Visualizer, which shows how compliance requirements map to security controls.

**Query Parameters:**

| Parameter | Type | Description |
|-----------|------|-------------|
| timeframe | string | Optional. Time frame for the data (e.g., '30d', '1w', '6m'). Default: '30d' |
| frameworks | string | Optional. Comma-separated list of frameworks to include. Default: 'SOC2,GDPR,HIPAA' |
| domains | string | Optional. Comma-separated list of domains to include. Default: 'grc,it,cybersecurity' |

**Response:**

```json
{
  "success": true,
  "data": {
    "complianceData": {
      "requirements": [
        {
          "id": "req1",
          "name": "Requirement 1",
          "domain": "grc",
          "completeness": 0.8
        },
        ...
      ],
      "controls": [
        {
          "id": "ctrl1",
          "name": "Control 1",
          "domain": "cybersecurity",
          "completeness": 0.7
        },
        ...
      ],
      "implementations": [
        {
          "id": "impl1",
          "name": "Implementation 1",
          "domain": "it",
          "completeness": 0.6
        },
        ...
      ],
      "links": [
        {
          "source": "req1",
          "target": "ctrl1",
          "strength": 0.8,
          "efficiency": 0.7
        },
        ...
      ]
    },
    "impactAnalysis": {
      "proposedChanges": [
        {
          "id": "change1",
          "target": "ctrl2",
          "impact": 0.7,
          "description": "Upgrade to biometric authentication"
        },
        ...
      ]
    }
  }
}
```

### Real-Time Data

#### GET /:visualizationType/real-time

Returns real-time data for a specific visualization type.

**Path Parameters:**

| Parameter | Type | Description |
|-----------|------|-------------|
| visualizationType | string | Required. The type of visualization (tri-domain-tensor, harmony-index, risk-control-fusion, resonance-spectrogram, unified-compliance-security) |

**Response:**

The response format is the same as the corresponding visualization endpoint.

### Historical Data

#### GET /:visualizationType/historical

Returns historical data for a specific visualization type.

**Path Parameters:**

| Parameter | Type | Description |
|-----------|------|-------------|
| visualizationType | string | Required. The type of visualization (tri-domain-tensor, harmony-index, risk-control-fusion, resonance-spectrogram, unified-compliance-security) |

**Query Parameters:**

| Parameter | Type | Description |
|-----------|------|-------------|
| startDate | string | Optional. Start date for historical data (ISO 8601 format). Default: 30 days ago |
| endDate | string | Optional. End date for historical data (ISO 8601 format). Default: now |
| interval | string | Optional. Interval for data points (e.g., 'day', 'week', 'month'). Default: 'day' |

**Response:**

The response format is the same as the corresponding visualization endpoint.

### Export Data

#### GET /:visualizationType/export

Exports data for a specific visualization type.

**Path Parameters:**

| Parameter | Type | Description |
|-----------|------|-------------|
| visualizationType | string | Required. The type of visualization (tri-domain-tensor, harmony-index, risk-control-fusion, resonance-spectrogram, unified-compliance-security) |

**Query Parameters:**

| Parameter | Type | Description |
|-----------|------|-------------|
| format | string | Optional. Export format ('json', 'csv'). Default: 'json' |

**Response:**

The response is a file download with the appropriate content type.

### Submit Feedback

#### POST /:visualizationType/feedback

Submits feedback for a specific visualization type.

**Path Parameters:**

| Parameter | Type | Description |
|-----------|------|-------------|
| visualizationType | string | Required. The type of visualization (tri-domain-tensor, harmony-index, risk-control-fusion, resonance-spectrogram, unified-compliance-security) |

**Request Body:**

```json
{
  "rating": 4,
  "comments": "Very insightful visualization",
  "suggestions": "Add more color options",
  "userEmail": "<EMAIL>"
}
```

**Response:**

```json
{
  "success": true,
  "message": "Feedback submitted successfully"
}
```

## Error Responses

In case of an error, the API will return an error response:

```json
{
  "success": false,
  "error": {
    "message": "Error message",
    "code": "ERROR_CODE"
  }
}
```

## Rate Limiting

The API is rate-limited to prevent abuse. The rate limit is 100 requests per minute per API key.

## Versioning

The current API version is v1. The API version is included in the URL path:

```
/api/v1/cyber-safety/visualizations
```

## Support

For API support, contact the NovaFuse <NAME_EMAIL>.
