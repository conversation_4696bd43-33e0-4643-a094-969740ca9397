/**
 * NovaVision - Universal UI Connector (NUUI)
 *
 * This module provides a universal UI framework that dynamically renders
 * based on API responses with a ui_schema specification.
 * It enforces NIST security requirements and implements role-based access control.
 */

const { createLogger } = require('../utils/logger');
const FormBuilder = require('./components/form-builder');
const DashboardBuilder = require('./components/dashboard-builder');
const ReportBuilder = require('./components/report-builder');
const uiSchemaService = require('./services/ui-schema-service');
const renderingService = require('./services/rendering-service');
const { regulationOrchestrator } = require('./services/regulation-orchestrator');
const { uiOptimizer } = require('./services/ui-optimizer-service');
const { consistencyEnforcer } = require('./services/consistency-enforcer-service');
const { NovaVisionSecurityManager } = require('./security');

const logger = createLogger('novavision');

/**
 * NovaVision class for universal UI connector
 */
class NovaVision {
  constructor(options = {}) {
    this.options = {
      theme: options.theme || 'default',
      responsive: options.responsive !== false,
      accessibilityLevel: options.accessibilityLevel || 'AA',
      regulationAware: options.regulationAware !== false,
      aiOptimization: options.aiOptimization !== false,
      consistencyEnforcement: options.consistencyEnforcement !== false,
      enableSecurity: options.enableSecurity !== false,
      enableNIST: options.enableNIST !== false,
      enableRBAC: options.enableRBAC !== false,
      ...options
    };

    this.formBuilder = new FormBuilder(this.options);
    this.dashboardBuilder = new DashboardBuilder(this.options);
    this.reportBuilder = new ReportBuilder(this.options);
    this.uiSchemaService = uiSchemaService;
    this.renderingService = renderingService;
    this.regulationOrchestrator = regulationOrchestrator;
    this.uiOptimizer = uiOptimizer;
    this.consistencyEnforcer = consistencyEnforcer;

    // Initialize security manager if security is enabled
    if (this.options.enableSecurity) {
      this.securityManager = new NovaVisionSecurityManager({
        enableRBAC: this.options.enableRBAC,
        enableNIST: this.options.enableNIST,
        rbacOptions: options.rbacOptions || {},
        nistOptions: options.nistOptions || {}
      });
    }

    logger.info('NovaVision initialized', {
      theme: this.options.theme,
      responsive: this.options.responsive,
      accessibilityLevel: this.options.accessibilityLevel,
      regulationAware: this.options.regulationAware,
      aiOptimization: this.options.aiOptimization,
      consistencyEnforcement: this.options.consistencyEnforcement,
      enableSecurity: this.options.enableSecurity,
      enableNIST: this.options.enableNIST,
      enableRBAC: this.options.enableRBAC
    });
  }

  /**
   * Generate UI schema for a form
   *
   * @param {Object} formConfig - Form configuration
   * @returns {Object} - UI schema for the form
   */
  generateFormSchema(formConfig) {
    logger.info('Generating form schema', { formId: formConfig.id });

    try {
      return this.formBuilder.buildFormSchema(formConfig);
    } catch (error) {
      logger.error('Error generating form schema', {
        formId: formConfig.id,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Generate UI schema for a dashboard
   *
   * @param {Object} dashboardConfig - Dashboard configuration
   * @returns {Object} - UI schema for the dashboard
   */
  generateDashboardSchema(dashboardConfig) {
    logger.info('Generating dashboard schema', { dashboardId: dashboardConfig.id });

    try {
      return this.dashboardBuilder.buildDashboardSchema(dashboardConfig);
    } catch (error) {
      logger.error('Error generating dashboard schema', {
        dashboardId: dashboardConfig.id,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Generate UI schema for a report
   *
   * @param {Object} reportConfig - Report configuration
   * @returns {Object} - UI schema for the report
   */
  generateReportSchema(reportConfig) {
    logger.info('Generating report schema', { reportId: reportConfig.id });

    try {
      return this.reportBuilder.buildReportSchema(reportConfig);
    } catch (error) {
      logger.error('Error generating report schema', {
        reportId: reportConfig.id,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Generate UI schema from API response
   *
   * @param {Object} apiResponse - API response
   * @param {string} schemaType - Schema type (form, dashboard, report)
   * @returns {Object} - UI schema
   */
  generateSchemaFromApiResponse(apiResponse, schemaType = 'form') {
    logger.info('Generating schema from API response', { schemaType });

    try {
      return this.uiSchemaService.generateSchemaFromApiResponse(apiResponse, schemaType);
    } catch (error) {
      logger.error('Error generating schema from API response', {
        schemaType,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Render UI from schema
   *
   * @param {Object} schema - UI schema
   * @param {Object} data - Data to render
   * @param {Object} options - Rendering options
   * @returns {Object} - Rendered UI
   */
  renderUiFromSchema(schema, data = {}, options = {}) {
    logger.info('Rendering UI from schema', {
      schemaType: schema.type,
      schemaId: schema.id
    });

    try {
      // Apply security if enabled
      let securedSchema = schema;
      if (this.options.enableSecurity && this.securityManager) {
        securedSchema = this.securityManager.secureUISchema(schema, options.userId);
      }

      // Render UI
      const renderedUI = this.renderingService.renderUiFromSchema(securedSchema, data, {
        ...this.options,
        ...options
      });

      // Apply security to rendered content if enabled
      if (this.options.enableSecurity && this.securityManager) {
        return this.securityManager.secureRenderedContent(
          renderedUI,
          options.userId,
          schema.contentType || 'default'
        );
      }

      return renderedUI;
    } catch (error) {
      logger.error('Error rendering UI from schema', {
        schemaType: schema.type,
        schemaId: schema.id,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Get UI component by type
   *
   * @param {string} componentType - Component type
   * @returns {Object} - UI component
   */
  getUiComponent(componentType) {
    logger.debug('Getting UI component', { componentType });

    switch (componentType) {
      case 'form':
        return this.formBuilder;
      case 'dashboard':
        return this.dashboardBuilder;
      case 'report':
        return this.reportBuilder;
      default:
        logger.warn('Unknown component type', { componentType });
        throw new Error(`Unknown component type: ${componentType}`);
    }
  }

  /**
   * Validate UI schema
   *
   * @param {Object} schema - UI schema to validate
   * @returns {Object} - Validation result
   */
  validateSchema(schema) {
    logger.debug('Validating schema', { schemaType: schema.type });

    try {
      return this.uiSchemaService.validateSchema(schema);
    } catch (error) {
      logger.error('Error validating schema', {
        schemaType: schema.type,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Handle jurisdiction change
   *
   * @param {string} userId - User ID
   * @param {string} sessionId - Session ID
   * @param {string} newJurisdiction - New jurisdiction
   * @returns {Promise<Object>} - Result of the jurisdiction change
   */
  async handleJurisdictionChange(userId, sessionId, newJurisdiction) {
    logger.info('Handling jurisdiction change', { userId, sessionId, newJurisdiction });

    if (!this.options.regulationAware) {
      logger.warn('Regulation awareness is disabled');
      return { status: 'REGULATION_AWARENESS_DISABLED' };
    }

    try {
      return await this.regulationOrchestrator.handleJurisdictionChange(userId, sessionId, newJurisdiction);
    } catch (error) {
      logger.error('Error handling jurisdiction change', {
        userId,
        sessionId,
        newJurisdiction,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Optimize UI layout
   *
   * @param {string} userId - User ID
   * @param {Array} userBehaviorStream - Stream of user behavior data
   * @returns {Promise<Object>} - Optimization result
   */
  async optimizeLayout(userId, userBehaviorStream) {
    logger.info('Optimizing UI layout', { userId });

    if (!this.options.aiOptimization) {
      logger.warn('AI optimization is disabled');
      return { status: 'AI_OPTIMIZATION_DISABLED' };
    }

    try {
      return await this.uiOptimizer.optimizeLayout(userId, userBehaviorStream);
    } catch (error) {
      logger.error('Error optimizing layout', {
        userId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Validate UI consistency
   *
   * @param {Array} components - UI components
   * @param {string} profileName - Regulatory profile name
   * @returns {Promise<Object>} - Validation result
   */
  async validateConsistency(components, profileName) {
    logger.info('Validating UI consistency', { componentCount: components.length, profileName });

    if (!this.options.consistencyEnforcement) {
      logger.warn('Consistency enforcement is disabled');
      return { status: 'CONSISTENCY_ENFORCEMENT_DISABLED' };
    }

    try {
      return await this.consistencyEnforcer.validateConsistency(components, profileName);
    } catch (error) {
      logger.error('Error validating consistency', {
        profileName,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Fix UI consistency issues
   *
   * @param {Array} components - UI components
   * @param {Object} validationResult - Validation result
   * @returns {Promise<Object>} - Fixed components and results
   */
  async fixConsistencyIssues(components, validationResult) {
    logger.info('Fixing UI consistency issues', {
      componentCount: components.length,
      profileName: validationResult.profileName
    });

    if (!this.options.consistencyEnforcement) {
      logger.warn('Consistency enforcement is disabled');
      return { status: 'CONSISTENCY_ENFORCEMENT_DISABLED' };
    }

    try {
      return await this.consistencyEnforcer.fixConsistencyIssues(components, validationResult);
    } catch (error) {
      logger.error('Error fixing consistency issues', {
        profileName: validationResult.profileName,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Get security report
   * @returns {Object} - Security report
   */
  getSecurityReport() {
    if (!this.options.enableSecurity || !this.securityManager) {
      logger.warn('Security is disabled');
      return { status: 'SECURITY_DISABLED' };
    }

    try {
      return this.securityManager.getSecurityReport();
    } catch (error) {
      logger.error('Error getting security report', {
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Validate user access to a component
   * @param {string} userId - User ID
   * @param {string} componentType - Component type
   * @returns {boolean} - Whether access is allowed
   */
  validateAccess(userId, componentType) {
    if (!this.options.enableSecurity || !this.securityManager) {
      // If security is disabled, allow access by default
      return true;
    }

    try {
      return this.securityManager.validateAccess(userId, componentType);
    } catch (error) {
      logger.error('Error validating access', {
        userId,
        componentType,
        error: error.message
      });
      // Deny access on error
      return false;
    }
  }
}

// Create singleton instance
const novaVision = new NovaVision();

// Export React components if in a browser environment
let reactComponents = {};
if (typeof window !== 'undefined') {
  try {
    reactComponents = require('./react');
  } catch (error) {
    console.warn('NovaVision React components could not be loaded:', error);
  }
}

module.exports = {
  NovaVision,
  novaVision,
  FormBuilder,
  DashboardBuilder,
  ReportBuilder,
  uiSchemaService,
  renderingService,
  regulationOrchestrator,
  uiOptimizer,
  consistencyEnforcer,

  // React components
  ...reactComponents
};
