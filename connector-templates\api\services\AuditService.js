/**
 * Audit Service
 * 
 * This service handles audit logging for tracking user actions.
 */

const fs = require('fs').promises;
const path = require('path');
const { v4: uuidv4 } = require('uuid');

class AuditService {
  constructor(dataDir = path.join(__dirname, '../data')) {
    this.dataDir = dataDir;
    this.auditDir = path.join(this.dataDir, 'audit');
    this.auditFile = path.join(this.auditDir, 'audit_log.json');
    this.retentionDays = 90; // Keep audit logs for 90 days
    this.ensureDataDir();
    
    // Clean up old audit logs once a day
    setInterval(() => this.cleanupOldLogs(), 24 * 60 * 60 * 1000);
  }

  /**
   * Ensure the data directory exists
   */
  async ensureDataDir() {
    try {
      await fs.mkdir(this.auditDir, { recursive: true });
      
      // Initialize audit log file if it doesn't exist
      try {
        await fs.access(this.auditFile);
      } catch (error) {
        if (error.code === 'ENOENT') {
          // File doesn't exist, create it with empty array
          await fs.writeFile(this.auditFile, JSON.stringify([]));
        } else {
          throw error;
        }
      }
    } catch (error) {
      console.error('Error creating audit directory:', error);
      throw error;
    }
  }

  /**
   * Load audit logs from file
   */
  async loadAuditLogs() {
    try {
      const data = await fs.readFile(this.auditFile, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      if (error.code === 'ENOENT') {
        // File doesn't exist, return empty array
        return [];
      }
      console.error('Error loading audit logs:', error);
      throw error;
    }
  }

  /**
   * Save audit logs to file
   */
  async saveAuditLogs(logs) {
    try {
      await fs.writeFile(this.auditFile, JSON.stringify(logs, null, 2));
    } catch (error) {
      console.error('Error saving audit logs:', error);
      throw error;
    }
  }

  /**
   * Log an audit event
   */
  async logEvent(data) {
    try {
      const auditLogs = await this.loadAuditLogs();
      
      // Create audit log entry
      const logEntry = {
        id: uuidv4(),
        timestamp: new Date().toISOString(),
        userId: data.userId || null,
        action: data.action,
        resourceType: data.resourceType,
        resourceId: data.resourceId,
        details: data.details || null,
        ip: data.ip || null,
        userAgent: data.userAgent || null,
        status: data.status || 'success',
        teamId: data.teamId || null,
        environmentId: data.environmentId || null
      };
      
      auditLogs.push(logEntry);
      
      // Limit the size of the audit logs
      if (auditLogs.length > 10000) {
        auditLogs.splice(0, auditLogs.length - 10000);
      }
      
      await this.saveAuditLogs(auditLogs);
      
      return logEntry;
    } catch (error) {
      console.error('Error logging audit event:', error);
      // Don't throw error to prevent affecting the main request flow
    }
  }

  /**
   * Get audit logs
   */
  async getAuditLogs(filters = {}) {
    const auditLogs = await this.loadAuditLogs();
    
    // Apply filters
    let filteredLogs = auditLogs;
    
    if (filters.startDate) {
      const startDate = new Date(filters.startDate);
      filteredLogs = filteredLogs.filter(log => new Date(log.timestamp) >= startDate);
    }
    
    if (filters.endDate) {
      const endDate = new Date(filters.endDate);
      filteredLogs = filteredLogs.filter(log => new Date(log.timestamp) <= endDate);
    }
    
    if (filters.userId) {
      filteredLogs = filteredLogs.filter(log => log.userId === filters.userId);
    }
    
    if (filters.action) {
      filteredLogs = filteredLogs.filter(log => log.action === filters.action);
    }
    
    if (filters.resourceType) {
      filteredLogs = filteredLogs.filter(log => log.resourceType === filters.resourceType);
    }
    
    if (filters.resourceId) {
      filteredLogs = filteredLogs.filter(log => log.resourceId === filters.resourceId);
    }
    
    if (filters.status) {
      filteredLogs = filteredLogs.filter(log => log.status === filters.status);
    }
    
    if (filters.teamId) {
      filteredLogs = filteredLogs.filter(log => log.teamId === filters.teamId);
    }
    
    if (filters.environmentId) {
      filteredLogs = filteredLogs.filter(log => log.environmentId === filters.environmentId);
    }
    
    // Sort by timestamp (newest first)
    filteredLogs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
    
    // Apply pagination
    const page = filters.page || 1;
    const limit = filters.limit || 100;
    const startIndex = (page - 1) * limit;
    const endIndex = page * limit;
    
    const paginatedLogs = filteredLogs.slice(startIndex, endIndex);
    
    return {
      logs: paginatedLogs,
      total: filteredLogs.length,
      page,
      limit,
      totalPages: Math.ceil(filteredLogs.length / limit)
    };
  }

  /**
   * Get audit log by ID
   */
  async getAuditLogById(id) {
    const auditLogs = await this.loadAuditLogs();
    const log = auditLogs.find(log => log.id === id);
    
    if (!log) {
      throw new Error(`Audit log with ID ${id} not found`);
    }
    
    return log;
  }

  /**
   * Get audit logs for a resource
   */
  async getAuditLogsForResource(resourceType, resourceId) {
    const auditLogs = await this.loadAuditLogs();
    
    // Filter logs for the resource
    const resourceLogs = auditLogs.filter(log => 
      log.resourceType === resourceType && log.resourceId === resourceId
    );
    
    // Sort by timestamp (newest first)
    resourceLogs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
    
    return resourceLogs;
  }

  /**
   * Get audit logs for a user
   */
  async getAuditLogsForUser(userId) {
    const auditLogs = await this.loadAuditLogs();
    
    // Filter logs for the user
    const userLogs = auditLogs.filter(log => log.userId === userId);
    
    // Sort by timestamp (newest first)
    userLogs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
    
    return userLogs;
  }

  /**
   * Get audit logs for a team
   */
  async getAuditLogsForTeam(teamId) {
    const auditLogs = await this.loadAuditLogs();
    
    // Filter logs for the team
    const teamLogs = auditLogs.filter(log => log.teamId === teamId);
    
    // Sort by timestamp (newest first)
    teamLogs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
    
    return teamLogs;
  }

  /**
   * Clean up old audit logs
   */
  async cleanupOldLogs() {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - this.retentionDays);
      
      const auditLogs = await this.loadAuditLogs();
      const filteredLogs = auditLogs.filter(log => 
        new Date(log.timestamp) >= cutoffDate
      );
      
      if (filteredLogs.length < auditLogs.length) {
        await this.saveAuditLogs(filteredLogs);
        console.log(`Cleaned up audit logs older than ${cutoffDate.toISOString()}`);
      }
    } catch (error) {
      console.error('Error cleaning up old audit logs:', error);
    }
  }

  /**
   * Create audit middleware
   */
  createAuditMiddleware() {
    return (req, res, next) => {
      // Store original end method
      const originalEnd = res.end;
      
      // Override end method to capture response
      res.end = function(chunk, encoding) {
        // Restore original end method
        res.end = originalEnd;
        
        // Call original end method
        res.end(chunk, encoding);
        
        // Skip audit logging for certain paths
        if (req.path.startsWith('/health') || req.path.startsWith('/api/monitoring/health')) {
          return;
        }
        
        // Log audit event
        const auditData = {
          userId: req.user ? req.user.id : null,
          action: req.method,
          resourceType: req.path.split('/')[2] || 'unknown',
          resourceId: req.params.id || null,
          details: {
            path: req.path,
            query: req.query,
            body: req.method !== 'GET' ? req.body : null
          },
          ip: req.ip || req.connection.remoteAddress,
          userAgent: req.headers['user-agent'],
          status: res.statusCode >= 400 ? 'failure' : 'success',
          teamId: req.headers['x-team-id'] || null,
          environmentId: req.headers['x-environment-id'] || null
        };
        
        this.logEvent(auditData);
      };
      
      next();
    };
  }
}

module.exports = AuditService;
