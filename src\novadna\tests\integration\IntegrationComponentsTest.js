/**
 * IntegrationComponentsTest.js
 * 
 * This module provides tests for the integration components in NovaDNA.
 */

const NovaConnectAdapter = require('../../integration/NovaConnectAdapter');
const HealthcareIntegration = require('../../integration/healthcare/HealthcareIntegration');
const EmergencyDataPipeline = require('../../integration/healthcare/EmergencyDataPipeline');
const ProviderConnector = require('../../integration/healthcare/ProviderConnector');
const DataSourcePrioritization = require('../../integration/healthcare/DataSourcePrioritization');
const SecureTemporaryCache = require('../../integration/healthcare/SecureTemporaryCache');

/**
 * Test NovaConnectAdapter
 */
function testNovaConnectAdapter() {
  console.log('=== Testing NovaConnectAdapter ===');
  
  try {
    // Initialize NovaConnectAdapter
    console.log('Testing initialization...');
    const novaConnectAdapter = new NovaConnectAdapter({
      apiUrl: 'http://localhost:3000/api/novaconnect',
      apiKey: 'test-api-key',
      apiSecret: 'test-api-secret'
    });
    
    if (!novaConnectAdapter) {
      throw new Error('NovaConnectAdapter initialization failed');
    }
    
    console.log('✅ Initialization passed');
    
    // Test getAvailableConnectors method
    console.log('Testing getAvailableConnectors method...');
    
    // Mock the client
    novaConnectAdapter.client = {
      get: async () => ({ data: [{ id: 'epic', name: 'Epic' }, { id: 'cerner', name: 'Cerner' }] })
    };
    
    novaConnectAdapter.getAvailableConnectors()
      .then(connectors => {
        if (!connectors || !Array.isArray(connectors) || connectors.length !== 2) {
          throw new Error('getAvailableConnectors returned invalid data');
        }
        
        console.log('✅ getAvailableConnectors method passed');
      })
      .catch(error => {
        console.error('❌ getAvailableConnectors method failed:', error.message);
      });
    
    // Test connectToEHR method
    console.log('Testing connectToEHR method...');
    
    // Mock the client
    novaConnectAdapter.client.post = async () => ({
      data: {
        connectionId: 'test-connection',
        provider: 'Epic',
        status: 'connected'
      }
    });
    
    novaConnectAdapter.connectToEHR('Epic', {
      clientId: 'test-client-id',
      clientSecret: 'test-client-secret',
      redirectUri: 'http://localhost:3000/callback'
    })
      .then(connection => {
        if (!connection || !connection.connectionId) {
          throw new Error('connectToEHR returned invalid data');
        }
        
        console.log('✅ connectToEHR method passed');
      })
      .catch(error => {
        console.error('❌ connectToEHR method failed:', error.message);
      });
    
    // Test fetchPatientData method
    console.log('Testing fetchPatientData method...');
    
    // Mock the client
    novaConnectAdapter.client.get = async () => ({
      data: {
        demographics: {
          firstName: 'John',
          lastName: 'Doe',
          dateOfBirth: '1980-01-01'
        },
        allergies: [
          { name: 'Penicillin', severity: 'Severe' }
        ],
        medications: [
          { name: 'Aspirin', dosage: '81mg' }
        ]
      }
    });
    
    novaConnectAdapter.fetchPatientData('test-connection', 'patient-123', {
      dataTypes: ['demographics', 'allergies', 'medications']
    })
      .then(patientData => {
        if (!patientData || !patientData.demographics) {
          throw new Error('fetchPatientData returned invalid data');
        }
        
        console.log('✅ fetchPatientData method passed');
      })
      .catch(error => {
        console.error('❌ fetchPatientData method failed:', error.message);
      });
    
    // Test createProfileFromEHR method
    console.log('Testing createProfileFromEHR method...');
    
    const ehrData = {
      demographics: {
        firstName: 'John',
        lastName: 'Doe',
        dateOfBirth: '1980-01-01'
      },
      allergies: [
        { name: 'Penicillin', severity: 'Severe' }
      ],
      medications: [
        { name: 'Aspirin', dosage: '81mg' }
      ]
    };
    
    const profile = novaConnectAdapter.createProfileFromEHR(ehrData);
    
    if (!profile || !profile.fullName) {
      throw new Error('createProfileFromEHR returned invalid data');
    }
    
    console.log('✅ createProfileFromEHR method passed');
    
    return true;
  } catch (error) {
    console.error('❌ NovaConnectAdapter test failed:', error.message);
    return false;
  }
}

/**
 * Test EmergencyDataPipeline
 */
function testEmergencyDataPipeline() {
  console.log('\n=== Testing EmergencyDataPipeline ===');
  
  try {
    // Initialize NovaConnectAdapter
    const novaConnectAdapter = new NovaConnectAdapter({
      apiUrl: 'http://localhost:3000/api/novaconnect',
      apiKey: 'test-api-key',
      apiSecret: 'test-api-secret'
    });
    
    // Mock the client
    novaConnectAdapter.client = {
      get: async () => ({ data: [] }),
      post: async () => ({ data: { success: true } })
    };
    
    // Initialize EmergencyDataPipeline
    console.log('Testing initialization...');
    const emergencyDataPipeline = new EmergencyDataPipeline({
      novaConnectAdapter,
      cacheEnabled: true
    });
    
    if (!emergencyDataPipeline) {
      throw new Error('EmergencyDataPipeline initialization failed');
    }
    
    console.log('✅ Initialization passed');
    
    // Test createTransferSession method
    console.log('Testing createTransferSession method...');
    const session = emergencyDataPipeline.createTransferSession({
      emergencyType: 'CARDIAC',
      emergencySeverity: 'HIGH'
    });
    
    if (!session || !session.sessionId) {
      throw new Error('createTransferSession returned invalid data');
    }
    
    console.log('✅ createTransferSession method passed');
    
    // Test closeSession method
    console.log('Testing closeSession method...');
    const closeResult = emergencyDataPipeline.closeSession(session.sessionId);
    
    if (closeResult !== true) {
      throw new Error('closeSession returned invalid data');
    }
    
    console.log('✅ closeSession method passed');
    
    // Test getActiveSessions method
    console.log('Testing getActiveSessions method...');
    const activeSessions = emergencyDataPipeline.getActiveSessions();
    
    if (!Array.isArray(activeSessions)) {
      throw new Error('getActiveSessions returned invalid data');
    }
    
    console.log('✅ getActiveSessions method passed');
    
    // Test _getDataTypesByContext method
    console.log('Testing _getDataTypesByContext method...');
    const dataTypes = emergencyDataPipeline._getDataTypesByContext({
      emergencyType: 'CARDIAC',
      emergencySeverity: 'HIGH'
    });
    
    if (!Array.isArray(dataTypes) || dataTypes.length === 0) {
      throw new Error('_getDataTypesByContext returned invalid data');
    }
    
    console.log('✅ _getDataTypesByContext method passed');
    
    return true;
  } catch (error) {
    console.error('❌ EmergencyDataPipeline test failed:', error.message);
    return false;
  }
}

/**
 * Test DataSourcePrioritization
 */
function testDataSourcePrioritization() {
  console.log('\n=== Testing DataSourcePrioritization ===');
  
  try {
    // Initialize DataSourcePrioritization
    console.log('Testing initialization...');
    const dataSourcePrioritization = new DataSourcePrioritization();
    
    if (!dataSourcePrioritization) {
      throw new Error('DataSourcePrioritization initialization failed');
    }
    
    console.log('✅ Initialization passed');
    
    // Test prioritizeDataSources method
    console.log('Testing prioritizeDataSources method...');
    const dataSources = [
      {
        id: 'epic-1',
        type: 'EHR',
        provider: 'Epic',
        availableDataTypes: ['demographics', 'allergies', 'medications', 'conditions'],
        lastUpdated: new Date().toISOString()
      },
      {
        id: 'cerner-1',
        type: 'EHR',
        provider: 'Cerner',
        availableDataTypes: ['demographics', 'allergies', 'medications'],
        lastUpdated: new Date(Date.now() - 86400000).toISOString() // 1 day ago
      }
    ];
    
    const priorities = dataSourcePrioritization.prioritizeDataSources(dataSources, {
      emergencyType: 'CARDIAC',
      emergencySeverity: 'HIGH'
    });
    
    if (!Array.isArray(priorities) || priorities.length !== 2) {
      throw new Error('prioritizeDataSources returned invalid data');
    }
    
    console.log('✅ prioritizeDataSources method passed');
    
    // Test rateDataSource method
    console.log('Testing rateDataSource method...');
    const rateResult = dataSourcePrioritization.rateDataSource('epic-1', {
      score: 8,
      reason: 'Good data quality'
    });
    
    if (rateResult !== true) {
      throw new Error('rateDataSource returned invalid data');
    }
    
    console.log('✅ rateDataSource method passed');
    
    // Test getDataSourceRatings method
    console.log('Testing getDataSourceRatings method...');
    const ratings = dataSourcePrioritization.getDataSourceRatings();
    
    if (!ratings || !ratings['epic-1']) {
      throw new Error('getDataSourceRatings returned invalid data');
    }
    
    console.log('✅ getDataSourceRatings method passed');
    
    // Test setCustomPriorities method
    console.log('Testing setCustomPriorities method...');
    const setResult = dataSourcePrioritization.setCustomPriorities('TRAUMA', {
      sourceTypes: {
        EHR: 10,
        HIE: 8,
        PERSONAL: 5
      }
    });
    
    if (setResult !== true) {
      throw new Error('setCustomPriorities returned invalid data');
    }
    
    console.log('✅ setCustomPriorities method passed');
    
    return true;
  } catch (error) {
    console.error('❌ DataSourcePrioritization test failed:', error.message);
    return false;
  }
}

/**
 * Test SecureTemporaryCache
 */
function testSecureTemporaryCache() {
  console.log('\n=== Testing SecureTemporaryCache ===');
  
  try {
    // Initialize SecureTemporaryCache
    console.log('Testing initialization...');
    const secureTemporaryCache = new SecureTemporaryCache({
      enabled: true,
      encryptionEnabled: true
    });
    
    if (!secureTemporaryCache) {
      throw new Error('SecureTemporaryCache initialization failed');
    }
    
    console.log('✅ Initialization passed');
    
    // Test store method
    console.log('Testing store method...');
    const storeResult = secureTemporaryCache.store('test-key', { name: 'Test Data', value: 123 });
    
    if (storeResult !== true) {
      throw new Error('store returned invalid data');
    }
    
    console.log('✅ store method passed');
    
    // Test retrieve method
    console.log('Testing retrieve method...');
    const retrievedData = secureTemporaryCache.retrieve('test-key');
    
    if (!retrievedData || retrievedData.name !== 'Test Data') {
      throw new Error('retrieve returned invalid data');
    }
    
    console.log('✅ retrieve method passed');
    
    // Test remove method
    console.log('Testing remove method...');
    const removeResult = secureTemporaryCache.remove('test-key');
    
    if (removeResult !== true) {
      throw new Error('remove returned invalid data');
    }
    
    console.log('✅ remove method passed');
    
    // Test getStats method
    console.log('Testing getStats method...');
    const stats = secureTemporaryCache.getStats();
    
    if (!stats || typeof stats.enabled !== 'boolean') {
      throw new Error('getStats returned invalid data');
    }
    
    console.log('✅ getStats method passed');
    
    // Test getAccessLogs method
    console.log('Testing getAccessLogs method...');
    const logs = secureTemporaryCache.getAccessLogs();
    
    if (!Array.isArray(logs)) {
      throw new Error('getAccessLogs returned invalid data');
    }
    
    console.log('✅ getAccessLogs method passed');
    
    return true;
  } catch (error) {
    console.error('❌ SecureTemporaryCache test failed:', error.message);
    return false;
  }
}

/**
 * Run all integration component tests
 */
function runIntegrationComponentTests() {
  console.log('=== Running Integration Component Tests ===\n');
  
  let allTestsPassed = true;
  
  // Test NovaConnectAdapter
  const novaConnectAdapterResult = testNovaConnectAdapter();
  
  if (!novaConnectAdapterResult) {
    allTestsPassed = false;
  }
  
  // Test EmergencyDataPipeline
  const emergencyDataPipelineResult = testEmergencyDataPipeline();
  
  if (!emergencyDataPipelineResult) {
    allTestsPassed = false;
  }
  
  // Test DataSourcePrioritization
  const dataSourcePrioritizationResult = testDataSourcePrioritization();
  
  if (!dataSourcePrioritizationResult) {
    allTestsPassed = false;
  }
  
  // Test SecureTemporaryCache
  const secureTemporaryCacheResult = testSecureTemporaryCache();
  
  if (!secureTemporaryCacheResult) {
    allTestsPassed = false;
  }
  
  // Summary
  console.log('\n=== Integration Component Tests Summary ===');
  
  if (allTestsPassed) {
    console.log('✅ All integration component tests passed');
  } else {
    console.error('❌ Some integration component tests failed');
  }
  
  return allTestsPassed;
}

// If running directly (not through a test runner)
if (require.main === module) {
  runIntegrationComponentTests();
}

module.exports = {
  testNovaConnectAdapter,
  testEmergencyDataPipeline,
  testDataSourcePrioritization,
  testSecureTemporaryCache,
  runIntegrationComponentTests
};
