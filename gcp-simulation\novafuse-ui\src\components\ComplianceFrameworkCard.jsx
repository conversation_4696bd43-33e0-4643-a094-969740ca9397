import React from 'react';
import { 
  <PERSON>, 
  Card<PERSON>ontent, 
  CardHeader, 
  Typography, 
  LinearProgress, 
  Box, 
  Chip,
  Grid
} from '@mui/material';

/**
 * ComplianceFrameworkCard Component
 * 
 * Displays a card with compliance framework information, including score and status.
 * 
 * @param {Object} props - Component props
 * @param {Object} props.framework - Framework data object
 * @param {string} props.framework.id - Framework ID
 * @param {string} props.framework.name - Framework name
 * @param {string} props.framework.version - Framework version
 * @param {string} props.framework.region - Framework region
 * @param {string} props.framework.category - Framework category
 * @param {number} props.framework.overallScore - Overall compliance score
 * @param {string} props.framework.lastAssessment - Date of last assessment
 * @param {string} props.framework.nextAssessment - Date of next assessment
 * @param {Function} props.onClick - Click handler function
 */
const ComplianceFrameworkCard = ({ framework, onClick }) => {
  // Determine score color based on value
  const getScoreColor = (score) => {
    if (score >= 90) return 'success';
    if (score >= 80) return 'warning';
    return 'error';
  };

  // Format date for display
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric' 
    });
  };

  return (
    <Card 
      sx={{ 
        minWidth: 275, 
        cursor: 'pointer',
        transition: 'transform 0.2s',
        '&:hover': {
          transform: 'scale(1.02)',
          boxShadow: 3
        }
      }} 
      onClick={() => onClick(framework.id)}
    >
      <CardHeader
        title={framework.name}
        subheader={`Version: ${framework.version}`}
        action={
          <Chip 
            label={framework.category} 
            color="primary" 
            size="small" 
            sx={{ mr: 1 }}
          />
        }
      />
      <CardContent>
        <Box sx={{ mb: 2 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
            <Typography variant="body2" color="text.secondary">
              Compliance Score
            </Typography>
            <Typography 
              variant="body2" 
              color={`${getScoreColor(framework.overallScore)}.main`}
              fontWeight="bold"
            >
              {framework.overallScore}%
            </Typography>
          </Box>
          <LinearProgress 
            variant="determinate" 
            value={framework.overallScore} 
            color={getScoreColor(framework.overallScore)}
            sx={{ height: 8, borderRadius: 4 }}
          />
        </Box>
        
        <Grid container spacing={2}>
          <Grid item xs={6}>
            <Typography variant="body2" color="text.secondary">
              Region
            </Typography>
            <Typography variant="body1">
              {framework.region}
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Typography variant="body2" color="text.secondary">
              Controls
            </Typography>
            <Typography variant="body1">
              {framework.controlCount || 'N/A'}
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Typography variant="body2" color="text.secondary">
              Last Assessment
            </Typography>
            <Typography variant="body1">
              {formatDate(framework.lastAssessment)}
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Typography variant="body2" color="text.secondary">
              Next Assessment
            </Typography>
            <Typography variant="body1">
              {formatDate(framework.nextAssessment)}
            </Typography>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );
};

export default ComplianceFrameworkCard;
