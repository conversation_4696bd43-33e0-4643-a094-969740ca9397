import { useState } from "react";
import Head from "next/head";

export default function Support() {
  const [activeTab, setActiveTab] = useState("faq");
  const [searchQuery, setSearchQuery] = useState("");
  const [ticketForm, setTicketForm] = useState({
    name: "",
    email: "",
    subject: "",
    description: "",
    priority: "medium"
  });

  const faqs = [
    {
      question: "What is the NovaFuse API Superstore?",
      answer: "The NovaFuse API Superstore is a marketplace for governance, risk, and compliance (GRC) APIs. It provides a centralized platform for discovering, testing, and integrating with various GRC APIs."
    },
    {
      question: "How do I get started with the NovaFuse API?",
      answer: "To get started, sign up for an account, generate an API key, and choose the APIs you want to use. You can then start making requests to the API endpoints."
    },
    {
      question: "What is the Universal API Connector (UAC)?",
      answer: "The Universal API Connector (UAC) is a tool that allows you to connect to any API using a standardized interface. It abstracts away the details of each API, making it easier to integrate with multiple services."
    },
    {
      question: "How do I create a connector template?",
      answer: "You can create a connector template using the Connector Template Designer in the NovaFuse dashboard. The template defines the API's endpoints, authentication method, and data mapping."
    },
    {
      question: "What authentication methods are supported?",
      answer: "NovaFuse supports various authentication methods, including API keys, OAuth 2.0, JWT, and custom authentication schemes."
    },
    {
      question: "How are API credentials stored?",
      answer: "API credentials are encrypted using AES-256 encryption and stored securely. NovaFuse never stores credentials in plaintext."
    },
    {
      question: "What is the pricing model?",
      answer: "NovaFuse offers tiered pricing based on API usage and features. Please visit the Pricing page for more details."
    },
    {
      question: "How do I report a bug or request a feature?",
      answer: "You can report bugs or request features by submitting a support ticket through the Support page or <NAME_EMAIL>."
    }
  ];

  const filteredFaqs = searchQuery
    ? faqs.filter(
        faq =>
          faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
          faq.answer.toLowerCase().includes(searchQuery.toLowerCase())
      )
    : faqs;

  const handleTicketSubmit = (e) => {
    e.preventDefault();
    // In a real implementation, this would submit the ticket to a backend API
    alert("Support ticket submitted successfully!");
    setTicketForm({
      name: "",
      email: "",
      subject: "",
      description: "",
      priority: "medium"
    });
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setTicketForm(prev => ({
      ...prev,
      [name]: value
    }));
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Head>
        <title>NovaFuse Support</title>
        <meta name="description" content="NovaFuse Support - Get help with NovaFuse APIs" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-6">Support Center</h1>

        {/* Navigation Tabs */}
        <div className="border-b border-gray-200 mb-8">
          <nav className="flex -mb-px">
            <button
              onClick={() => setActiveTab("faq")}
              className={`py-4 px-6 font-medium text-sm ${
                activeTab === "faq"
                  ? "border-b-2 border-blue-500 text-blue-600"
                  : "text-gray-500 hover:text-gray-700 hover:border-gray-300"
              }`}
            >
              FAQ
            </button>
            <button
              onClick={() => setActiveTab("contact")}
              className={`py-4 px-6 font-medium text-sm ${
                activeTab === "contact"
                  ? "border-b-2 border-blue-500 text-blue-600"
                  : "text-gray-500 hover:text-gray-700 hover:border-gray-300"
              }`}
            >
              Contact Support
            </button>
            <button
              onClick={() => setActiveTab("documentation")}
              className={`py-4 px-6 font-medium text-sm ${
                activeTab === "documentation"
                  ? "border-b-2 border-blue-500 text-blue-600"
                  : "text-gray-500 hover:text-gray-700 hover:border-gray-300"
              }`}
            >
              Documentation
            </button>
          </nav>
        </div>

        {/* FAQ Tab */}
        {activeTab === "faq" && (
          <div>
            <div className="mb-6">
              <label htmlFor="search" className="sr-only">
                Search FAQs
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg
                    className="h-5 w-5 text-gray-400"
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                    aria-hidden="true"
                  >
                    <path
                      fillRule="evenodd"
                      d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
                <input
                  id="search"
                  name="search"
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  placeholder="Search FAQs"
                  type="search"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>

            <div className="space-y-6">
              {filteredFaqs.length > 0 ? (
                filteredFaqs.map((faq, index) => (
                  <div key={index} className="bg-white shadow overflow-hidden rounded-lg">
                    <div className="px-4 py-5 sm:p-6">
                      <h3 className="text-lg leading-6 font-medium text-gray-900">
                        {faq.question}
                      </h3>
                      <div className="mt-2 text-base text-gray-500">
                        <p>{faq.answer}</p>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-10">
                  <p className="text-gray-500">No FAQs found matching your search.</p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Contact Support Tab */}
        {activeTab === "contact" && (
          <div>
            <div className="bg-white shadow overflow-hidden rounded-lg">
              <div className="px-4 py-5 sm:p-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                  Submit a Support Ticket
                </h3>
                <form onSubmit={handleTicketSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                        Name
                      </label>
                      <input
                        type="text"
                        name="name"
                        id="name"
                        className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        value={ticketForm.name}
                        onChange={handleInputChange}
                        required
                      />
                    </div>
                    <div>
                      <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                        Email
                      </label>
                      <input
                        type="email"
                        name="email"
                        id="email"
                        className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        value={ticketForm.email}
                        onChange={handleInputChange}
                        required
                      />
                    </div>
                  </div>
                  <div>
                    <label htmlFor="subject" className="block text-sm font-medium text-gray-700">
                      Subject
                    </label>
                    <input
                      type="text"
                      name="subject"
                      id="subject"
                      className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      value={ticketForm.subject}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                  <div>
                    <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                      Description
                    </label>
                    <textarea
                      id="description"
                      name="description"
                      rows={4}
                      className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      value={ticketForm.description}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                  <div>
                    <label htmlFor="priority" className="block text-sm font-medium text-gray-700">
                      Priority
                    </label>
                    <select
                      id="priority"
                      name="priority"
                      className="mt-1 block w-full bg-white border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      value={ticketForm.priority}
                      onChange={handleInputChange}
                    >
                      <option value="low">Low</option>
                      <option value="medium">Medium</option>
                      <option value="high">High</option>
                      <option value="critical">Critical</option>
                    </select>
                  </div>
                  <div>
                    <button
                      type="submit"
                      className="w-full inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      Submit Ticket
                    </button>
                  </div>
                </form>
              </div>
            </div>

            <div className="mt-8 bg-white shadow overflow-hidden rounded-lg">
              <div className="px-4 py-5 sm:p-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                  Contact Information
                </h3>
                <div className="space-y-4">
                  <div className="flex items-start">
                    <div className="flex-shrink-0">
                      <svg
                        className="h-6 w-6 text-gray-400"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                        />
                      </svg>
                    </div>
                    <div className="ml-3 text-sm">
                      <p className="text-gray-700">Email</p>
                      <p className="font-medium text-gray-900"><EMAIL></p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <div className="flex-shrink-0">
                      <svg
                        className="h-6 w-6 text-gray-400"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                        />
                      </svg>
                    </div>
                    <div className="ml-3 text-sm">
                      <p className="text-gray-700">Phone</p>
                      <p className="font-medium text-gray-900">+****************</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <div className="flex-shrink-0">
                      <svg
                        className="h-6 w-6 text-gray-400"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                        />
                      </svg>
                    </div>
                    <div className="ml-3 text-sm">
                      <p className="text-gray-700">Support Hours</p>
                      <p className="font-medium text-gray-900">Monday - Friday, 9:00 AM - 5:00 PM EST</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Documentation Tab */}
        {activeTab === "documentation" && (
          <div>
            <div className="bg-white shadow overflow-hidden rounded-lg">
              <div className="px-4 py-5 sm:p-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                  Documentation Resources
                </h3>
                <div className="space-y-4">
                  <div className="border border-gray-200 rounded-md p-4 hover:bg-gray-50">
                    <h4 className="text-base font-medium text-gray-900">API Reference</h4>
                    <p className="mt-1 text-sm text-gray-500">
                      Comprehensive documentation for all NovaFuse API endpoints.
                    </p>
                    <a
                      href="/api-docs"
                      className="mt-2 inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-500"
                    >
                      View API Reference
                      <svg
                        className="ml-1 h-4 w-4"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                        aria-hidden="true"
                      >
                        <path
                          fillRule="evenodd"
                          d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </a>
                  </div>
                  <div className="border border-gray-200 rounded-md p-4 hover:bg-gray-50">
                    <h4 className="text-base font-medium text-gray-900">Getting Started Guide</h4>
                    <p className="mt-1 text-sm text-gray-500">
                      Learn how to get started with NovaFuse APIs and the Universal API Connector.
                    </p>
                    <a
                      href="/api-docs?category=getting-started&api=quickstart"
                      className="mt-2 inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-500"
                    >
                      View Guide
                      <svg
                        className="ml-1 h-4 w-4"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                        aria-hidden="true"
                      >
                        <path
                          fillRule="evenodd"
                          d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </a>
                  </div>
                  <div className="border border-gray-200 rounded-md p-4 hover:bg-gray-50">
                    <h4 className="text-base font-medium text-gray-900">Tutorials</h4>
                    <p className="mt-1 text-sm text-gray-500">
                      Step-by-step tutorials for common use cases and integrations.
                    </p>
                    <a
                      href="/tutorials"
                      className="mt-2 inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-500"
                    >
                      View Tutorials
                      <svg
                        className="ml-1 h-4 w-4"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                        aria-hidden="true"
                      >
                        <path
                          fillRule="evenodd"
                          d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </a>
                  </div>
                  <div className="border border-gray-200 rounded-md p-4 hover:bg-gray-50">
                    <h4 className="text-base font-medium text-gray-900">Sample Code</h4>
                    <p className="mt-1 text-sm text-gray-500">
                      Sample code and SDKs for various programming languages.
                    </p>
                    <a
                      href="/samples"
                      className="mt-2 inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-500"
                    >
                      View Samples
                      <svg
                        className="ml-1 h-4 w-4"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                        aria-hidden="true"
                      >
                        <path
                          fillRule="evenodd"
                          d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </main>
    </div>
  );
}
