/**
 * Compliance Frameworks Data Model
 * 
 * This file defines the data structure for compliance frameworks in the NovaFuse GCP simulation.
 * It includes detailed control mappings to GCP services and implementation status.
 */

// Sample compliance frameworks data
const complianceFrameworks = [
  {
    id: "gdpr",
    name: "General Data Protection Regulation",
    version: "2016/679",
    region: "European Union",
    category: "Privacy",
    description: "The General Data Protection Regulation is a regulation in EU law on data protection and privacy in the European Union and the European Economic Area.",
    overallScore: 87,
    lastAssessment: "2023-06-15T00:00:00Z",
    nextAssessment: "2023-09-15T00:00:00Z",
    controls: [
      {
        id: "gdpr-art5-1a",
        name: "Lawfulness, Fairness and Transparency",
        description: "Personal data shall be processed lawfully, fairly and in a transparent manner in relation to the data subject.",
        category: "Data Processing Principles",
        gcpServices: [
          { name: "Cloud IAM", relevance: "High", implementation: "IAM policies for data access control" },
          { name: "Cloud Logging", relevance: "Medium", implementation: "Audit logging of data access" },
          { name: "Security Command Center", relevance: "Medium", implementation: "Monitoring for unauthorized access" }
        ],
        status: "PASS",
        score: 95,
        evidence: [
          { type: "IAM_POLICY", resource: "project-123", timestamp: "2023-06-15T14:22:31Z", description: "IAM policy restricting access to personal data" },
          { type: "AUDIT_LOG", resource: "bigquery-dataset-1", timestamp: "2023-06-14T09:17:45Z", description: "Audit logs showing authorized data access" }
        ],
        remediationSteps: [],
        lastAssessment: "2023-06-15T00:00:00Z",
        owner: "Privacy Team"
      },
      {
        id: "gdpr-art5-1b",
        name: "Purpose Limitation",
        description: "Personal data shall be collected for specified, explicit and legitimate purposes and not further processed in a manner that is incompatible with those purposes.",
        category: "Data Processing Principles",
        gcpServices: [
          { name: "Data Catalog", relevance: "High", implementation: "Data classification and purpose tagging" },
          { name: "Cloud DLP", relevance: "High", implementation: "Data discovery and classification" },
          { name: "BigQuery", relevance: "Medium", implementation: "Query auditing and purpose validation" }
        ],
        status: "PASS",
        score: 90,
        evidence: [
          { type: "DATA_CATALOG", resource: "data-catalog-1", timestamp: "2023-06-10T11:30:00Z", description: "Data catalog entries with purpose specifications" },
          { type: "DLP_SCAN", resource: "gcs-bucket-1", timestamp: "2023-06-12T08:45:22Z", description: "DLP scan results showing proper data classification" }
        ],
        remediationSteps: [],
        lastAssessment: "2023-06-15T00:00:00Z",
        owner: "Data Governance Team"
      },
      {
        id: "gdpr-art5-1c",
        name: "Data Minimization",
        description: "Personal data shall be adequate, relevant and limited to what is necessary in relation to the purposes for which they are processed.",
        category: "Data Processing Principles",
        gcpServices: [
          { name: "Cloud DLP", relevance: "High", implementation: "Data minimization through redaction" },
          { name: "BigQuery", relevance: "High", implementation: "Column-level access control" },
          { name: "Cloud Storage", relevance: "Medium", implementation: "Object lifecycle management" }
        ],
        status: "PARTIAL",
        score: 75,
        evidence: [
          { type: "DLP_CONFIG", resource: "dlp-template-1", timestamp: "2023-06-05T10:15:00Z", description: "DLP templates for data redaction" }
        ],
        remediationSteps: [
          "Implement column-level access controls in BigQuery datasets containing personal data",
          "Configure object lifecycle management policies for Cloud Storage buckets"
        ],
        lastAssessment: "2023-06-15T00:00:00Z",
        owner: "Data Governance Team"
      },
      {
        id: "gdpr-art5-1d",
        name: "Accuracy",
        description: "Personal data shall be accurate and, where necessary, kept up to date.",
        category: "Data Processing Principles",
        gcpServices: [
          { name: "Dataflow", relevance: "High", implementation: "Data validation pipelines" },
          { name: "Cloud Functions", relevance: "Medium", implementation: "Automated data correction" },
          { name: "BigQuery", relevance: "Medium", implementation: "Data quality checks" }
        ],
        status: "PASS",
        score: 85,
        evidence: [
          { type: "DATAFLOW_JOB", resource: "dataflow-job-1", timestamp: "2023-06-08T16:20:00Z", description: "Dataflow job for data validation" },
          { type: "FUNCTION_LOG", resource: "data-correction-function", timestamp: "2023-06-09T14:30:00Z", description: "Cloud Function logs showing data corrections" }
        ],
        remediationSteps: [],
        lastAssessment: "2023-06-15T00:00:00Z",
        owner: "Data Quality Team"
      },
      {
        id: "gdpr-art5-1e",
        name: "Storage Limitation",
        description: "Personal data shall be kept in a form which permits identification of data subjects for no longer than is necessary for the purposes for which the personal data are processed.",
        category: "Data Processing Principles",
        gcpServices: [
          { name: "Cloud Storage", relevance: "High", implementation: "Object lifecycle management" },
          { name: "BigQuery", relevance: "High", implementation: "Table expiration policies" },
          { name: "Cloud KMS", relevance: "Medium", implementation: "Key rotation and destruction" }
        ],
        status: "FAIL",
        score: 60,
        evidence: [
          { type: "STORAGE_POLICY", resource: "bucket-pii-data", timestamp: "2023-06-10T11:05:22Z", description: "Missing lifecycle policy on PII data bucket" }
        ],
        remediationSteps: [
          "Configure lifecycle policy on bucket-pii-data to delete data after retention period",
          "Implement table expiration policies for BigQuery datasets containing personal data",
          "Enable automatic encryption key rotation in Cloud KMS"
        ],
        lastAssessment: "2023-06-15T00:00:00Z",
        owner: "Data Governance Team"
      },
      {
        id: "gdpr-art5-1f",
        name: "Integrity and Confidentiality",
        description: "Personal data shall be processed in a manner that ensures appropriate security of the personal data, including protection against unauthorised or unlawful processing and against accidental loss, destruction or damage, using appropriate technical or organisational measures.",
        category: "Data Processing Principles",
        gcpServices: [
          { name: "Cloud KMS", relevance: "High", implementation: "Encryption key management" },
          { name: "Cloud IAM", relevance: "High", implementation: "Access control policies" },
          { name: "Security Command Center", relevance: "High", implementation: "Security monitoring and alerting" },
          { name: "VPC Service Controls", relevance: "High", implementation: "Network security perimeter" }
        ],
        status: "PASS",
        score: 95,
        evidence: [
          { type: "KMS_CONFIG", resource: "kms-key-1", timestamp: "2023-06-07T09:10:00Z", description: "KMS configuration for data encryption" },
          { type: "IAM_POLICY", resource: "project-123", timestamp: "2023-06-15T14:22:31Z", description: "IAM policy restricting access to personal data" },
          { type: "VPC_CONFIG", resource: "vpc-sc-perimeter-1", timestamp: "2023-06-12T10:45:00Z", description: "VPC Service Controls perimeter configuration" }
        ],
        remediationSteps: [],
        lastAssessment: "2023-06-15T00:00:00Z",
        owner: "Security Team"
      }
    ]
  },
  {
    id: "hipaa",
    name: "Health Insurance Portability and Accountability Act",
    version: "1996",
    region: "United States",
    category: "Healthcare",
    description: "HIPAA establishes national standards for protecting sensitive patient health information from being disclosed without the patient's consent or knowledge.",
    overallScore: 92,
    lastAssessment: "2023-06-01T00:00:00Z",
    nextAssessment: "2023-09-01T00:00:00Z",
    controls: [
      {
        id: "hipaa-164-312a",
        name: "Access Control",
        description: "Implement technical policies and procedures for electronic information systems that maintain ePHI to allow access only to authorized persons or software programs.",
        category: "Technical Safeguards",
        gcpServices: [
          { name: "Cloud IAM", relevance: "High", implementation: "Role-based access control" },
          { name: "Identity-Aware Proxy", relevance: "High", implementation: "Context-aware access control" },
          { name: "VPC Service Controls", relevance: "High", implementation: "Network security perimeter" }
        ],
        status: "PASS",
        score: 95,
        evidence: [
          { type: "IAM_POLICY", resource: "project-123", timestamp: "2023-06-01T10:15:00Z", description: "IAM policy restricting access to ePHI" },
          { type: "IAP_CONFIG", resource: "iap-web-1", timestamp: "2023-05-28T14:30:00Z", description: "IAP configuration for web applications" },
          { type: "VPC_CONFIG", resource: "vpc-sc-perimeter-1", timestamp: "2023-05-30T09:45:00Z", description: "VPC Service Controls perimeter configuration" }
        ],
        remediationSteps: [],
        lastAssessment: "2023-06-01T00:00:00Z",
        owner: "Security Team"
      },
      {
        id: "hipaa-164-312b",
        name: "Audit Controls",
        description: "Implement hardware, software, and/or procedural mechanisms that record and examine activity in information systems that contain or use ePHI.",
        category: "Technical Safeguards",
        gcpServices: [
          { name: "Cloud Logging", relevance: "High", implementation: "Centralized logging" },
          { name: "Cloud Monitoring", relevance: "High", implementation: "Log-based metrics and alerting" },
          { name: "Security Command Center", relevance: "High", implementation: "Security monitoring and alerting" }
        ],
        status: "PASS",
        score: 90,
        evidence: [
          { type: "LOGGING_CONFIG", resource: "logging-sink-1", timestamp: "2023-05-29T11:20:00Z", description: "Logging configuration for ePHI systems" },
          { type: "MONITORING_ALERT", resource: "alert-policy-1", timestamp: "2023-05-30T13:45:00Z", description: "Alert policy for suspicious access patterns" }
        ],
        remediationSteps: [],
        lastAssessment: "2023-06-01T00:00:00Z",
        owner: "Security Team"
      },
      {
        id: "hipaa-164-312c1",
        name: "Integrity",
        description: "Implement policies and procedures to protect ePHI from improper alteration or destruction.",
        category: "Technical Safeguards",
        gcpServices: [
          { name: "Cloud Storage", relevance: "High", implementation: "Object versioning and retention" },
          { name: "BigQuery", relevance: "High", implementation: "Table snapshots and time travel" },
          { name: "Binary Authorization", relevance: "Medium", implementation: "Deployment integrity validation" }
        ],
        status: "PARTIAL",
        score: 85,
        evidence: [
          { type: "STORAGE_CONFIG", resource: "bucket-ephi-1", timestamp: "2023-05-28T09:30:00Z", description: "Object versioning configuration for ePHI bucket" }
        ],
        remediationSteps: [
          "Enable table snapshots for BigQuery tables containing ePHI",
          "Implement Binary Authorization for all deployments handling ePHI"
        ],
        lastAssessment: "2023-06-01T00:00:00Z",
        owner: "Data Governance Team"
      },
      {
        id: "hipaa-164-312d",
        name: "Person or Entity Authentication",
        description: "Implement procedures to verify that a person or entity seeking access to ePHI is the one claimed.",
        category: "Technical Safeguards",
        gcpServices: [
          { name: "Identity Platform", relevance: "High", implementation: "Multi-factor authentication" },
          { name: "Cloud IAM", relevance: "High", implementation: "Identity federation" },
          { name: "Identity-Aware Proxy", relevance: "High", implementation: "Context-aware access" }
        ],
        status: "PASS",
        score: 95,
        evidence: [
          { type: "IDENTITY_CONFIG", resource: "identity-platform-1", timestamp: "2023-05-27T14:15:00Z", description: "MFA configuration for ePHI access" },
          { type: "IAM_CONFIG", resource: "workload-identity-1", timestamp: "2023-05-29T10:30:00Z", description: "Workload identity configuration" }
        ],
        remediationSteps: [],
        lastAssessment: "2023-06-01T00:00:00Z",
        owner: "Identity Team"
      },
      {
        id: "hipaa-164-312e1",
        name: "Transmission Security",
        description: "Implement technical security measures to guard against unauthorized access to ePHI that is being transmitted over an electronic communications network.",
        category: "Technical Safeguards",
        gcpServices: [
          { name: "Cloud Load Balancing", relevance: "High", implementation: "SSL/TLS termination" },
          { name: "VPC", relevance: "High", implementation: "Private network communication" },
          { name: "Cloud VPN", relevance: "High", implementation: "Secure VPN connections" }
        ],
        status: "PASS",
        score: 95,
        evidence: [
          { type: "LB_CONFIG", resource: "https-lb-1", timestamp: "2023-05-26T11:45:00Z", description: "HTTPS load balancer configuration" },
          { type: "VPC_CONFIG", resource: "vpc-network-1", timestamp: "2023-05-28T09:15:00Z", description: "VPC network configuration for private communication" },
          { type: "VPN_CONFIG", resource: "vpn-tunnel-1", timestamp: "2023-05-29T14:30:00Z", description: "VPN tunnel configuration for secure transmission" }
        ],
        remediationSteps: [],
        lastAssessment: "2023-06-01T00:00:00Z",
        owner: "Network Security Team"
      }
    ]
  },
  {
    id: "pci-dss",
    name: "Payment Card Industry Data Security Standard",
    version: "3.2.1",
    region: "Global",
    category: "Financial",
    description: "The Payment Card Industry Data Security Standard is an information security standard for organizations that handle branded credit cards from the major card schemes.",
    overallScore: 88,
    lastAssessment: "2023-05-15T00:00:00Z",
    nextAssessment: "2023-08-15T00:00:00Z",
    controls: [
      {
        id: "pci-dss-req1",
        name: "Install and maintain a firewall configuration to protect cardholder data",
        description: "Firewalls are devices that control computer traffic allowed between an entity's networks and untrusted networks, as well as traffic into and out of more sensitive areas within an entity's internal trusted networks.",
        category: "Network Security",
        gcpServices: [
          { name: "VPC Firewall Rules", relevance: "High", implementation: "Network firewall configuration" },
          { name: "Cloud Armor", relevance: "High", implementation: "Web application firewall" },
          { name: "VPC Service Controls", relevance: "High", implementation: "Network security perimeter" }
        ],
        status: "PASS",
        score: 90,
        evidence: [
          { type: "FIREWALL_CONFIG", resource: "fw-rule-pci-1", timestamp: "2023-05-10T09:30:00Z", description: "Firewall rules for PCI environment" },
          { type: "ARMOR_CONFIG", resource: "armor-policy-1", timestamp: "2023-05-12T14:15:00Z", description: "Cloud Armor security policy" }
        ],
        remediationSteps: [],
        lastAssessment: "2023-05-15T00:00:00Z",
        owner: "Network Security Team"
      },
      {
        id: "pci-dss-req3",
        name: "Protect stored cardholder data",
        description: "Protection methods such as encryption, truncation, masking, and hashing are critical components of cardholder data protection.",
        category: "Data Protection",
        gcpServices: [
          { name: "Cloud KMS", relevance: "High", implementation: "Encryption key management" },
          { name: "Cloud DLP", relevance: "High", implementation: "Data tokenization and masking" },
          { name: "Secret Manager", relevance: "High", implementation: "Secure secrets storage" }
        ],
        status: "PARTIAL",
        score: 80,
        evidence: [
          { type: "KMS_CONFIG", resource: "kms-key-pci-1", timestamp: "2023-05-11T10:45:00Z", description: "KMS configuration for cardholder data encryption" },
          { type: "DLP_CONFIG", resource: "dlp-template-pci-1", timestamp: "2023-05-13T11:30:00Z", description: "DLP templates for cardholder data tokenization" }
        ],
        remediationSteps: [
          "Implement Secret Manager for storing encryption keys and credentials",
          "Configure automatic key rotation for all KMS keys used for cardholder data"
        ],
        lastAssessment: "2023-05-15T00:00:00Z",
        owner: "Security Team"
      },
      {
        id: "pci-dss-req10",
        name: "Track and monitor all access to network resources and cardholder data",
        description: "Logging mechanisms and the ability to track user activities are critical in preventing, detecting, or minimizing the impact of a data compromise.",
        category: "Monitoring and Logging",
        gcpServices: [
          { name: "Cloud Logging", relevance: "High", implementation: "Centralized logging" },
          { name: "Cloud Monitoring", relevance: "High", implementation: "Log-based metrics and alerting" },
          { name: "Security Command Center", relevance: "High", implementation: "Security monitoring and alerting" }
        ],
        status: "PASS",
        score: 95,
        evidence: [
          { type: "LOGGING_CONFIG", resource: "logging-sink-pci-1", timestamp: "2023-05-09T14:30:00Z", description: "Logging configuration for PCI environment" },
          { type: "MONITORING_ALERT", resource: "alert-policy-pci-1", timestamp: "2023-05-12T09:15:00Z", description: "Alert policy for cardholder data access" },
          { type: "SCC_FINDING", resource: "finding-pci-1", timestamp: "2023-05-14T10:30:00Z", description: "Security Command Center finding for suspicious access" }
        ],
        remediationSteps: [],
        lastAssessment: "2023-05-15T00:00:00Z",
        owner: "Security Operations Team"
      }
    ]
  },
  {
    id: "soc2",
    name: "System and Organization Controls 2",
    version: "2017",
    region: "Global",
    category: "Trust Services",
    description: "SOC 2 defines criteria for managing customer data based on five 'trust service principles'—security, availability, processing integrity, confidentiality and privacy.",
    overallScore: 90,
    lastAssessment: "2023-05-01T00:00:00Z",
    nextAssessment: "2023-08-01T00:00:00Z",
    controls: [
      {
        id: "soc2-cc61",
        name: "CC6.1 - Logical Access Security",
        description: "The entity implements logical access security software, infrastructure, and architectures over protected information assets to protect them from security events to meet the entity's objectives.",
        category: "Common Criteria - Logical and Physical Access Controls",
        gcpServices: [
          { name: "Cloud IAM", relevance: "High", implementation: "Role-based access control" },
          { name: "Identity-Aware Proxy", relevance: "High", implementation: "Context-aware access control" },
          { name: "VPC Service Controls", relevance: "High", implementation: "Network security perimeter" }
        ],
        status: "PASS",
        score: 95,
        evidence: [
          { type: "IAM_POLICY", resource: "project-123", timestamp: "2023-04-28T10:15:00Z", description: "IAM policy implementing least privilege" },
          { type: "IAP_CONFIG", resource: "iap-web-1", timestamp: "2023-04-29T14:30:00Z", description: "IAP configuration for web applications" }
        ],
        remediationSteps: [],
        lastAssessment: "2023-05-01T00:00:00Z",
        owner: "Security Team"
      },
      {
        id: "soc2-cc62",
        name: "CC6.2 - Physical Access Security",
        description: "The entity implements physical access security software, infrastructure, and architectures over protected information assets to protect them from security events to meet the entity's objectives.",
        category: "Common Criteria - Logical and Physical Access Controls",
        gcpServices: [
          { name: "Google Cloud Physical Security", relevance: "High", implementation: "Google data center physical security" }
        ],
        status: "PASS",
        score: 100,
        evidence: [
          { type: "ATTESTATION", resource: "google-cloud", timestamp: "2023-04-15T00:00:00Z", description: "Google Cloud SOC 2 attestation for physical security" }
        ],
        remediationSteps: [],
        lastAssessment: "2023-05-01T00:00:00Z",
        owner: "Compliance Team"
      },
      {
        id: "soc2-cc72",
        name: "CC7.2 - Security Incident Identification and Response",
        description: "The entity identifies, develops, and implements activities to identify and track security incidents, detect security events, and respond to security incidents and events.",
        category: "Common Criteria - System Operations",
        gcpServices: [
          { name: "Security Command Center", relevance: "High", implementation: "Security monitoring and alerting" },
          { name: "Cloud Logging", relevance: "High", implementation: "Centralized logging" },
          { name: "Cloud Monitoring", relevance: "High", implementation: "Log-based metrics and alerting" }
        ],
        status: "PARTIAL",
        score: 85,
        evidence: [
          { type: "SCC_CONFIG", resource: "scc-premium-1", timestamp: "2023-04-27T11:30:00Z", description: "Security Command Center Premium tier configuration" },
          { type: "LOGGING_CONFIG", resource: "logging-sink-1", timestamp: "2023-04-28T09:45:00Z", description: "Logging configuration for security events" }
        ],
        remediationSteps: [
          "Implement automated incident response playbooks",
          "Configure additional alert policies for security events"
        ],
        lastAssessment: "2023-05-01T00:00:00Z",
        owner: "Security Operations Team"
      },
      {
        id: "soc2-cc83",
        name: "CC8.3 - Remediation of Deficiencies",
        description: "The entity designs, develops, implements, and operates controls to remediate identified deficiencies in a timely manner.",
        category: "Common Criteria - Change Management",
        gcpServices: [
          { name: "Security Command Center", relevance: "High", implementation: "Vulnerability management" },
          { name: "Cloud Build", relevance: "Medium", implementation: "Automated remediation pipelines" },
          { name: "Cloud Functions", relevance: "Medium", implementation: "Automated remediation functions" }
        ],
        status: "PASS",
        score: 90,
        evidence: [
          { type: "SCC_FINDING", resource: "finding-1", timestamp: "2023-04-26T14:15:00Z", description: "Security Command Center finding with remediation" },
          { type: "BUILD_CONFIG", resource: "build-trigger-1", timestamp: "2023-04-27T10:30:00Z", description: "Cloud Build trigger for automated remediation" }
        ],
        remediationSteps: [],
        lastAssessment: "2023-05-01T00:00:00Z",
        owner: "Security Operations Team"
      }
    ]
  }
];

module.exports = complianceFrameworks;
