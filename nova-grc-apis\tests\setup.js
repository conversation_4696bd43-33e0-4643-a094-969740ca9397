// Set up environment variables for testing
process.env.NODE_ENV = 'test';

// Set up global test utilities
global.testUtils = {
  // Add test utilities here
};

// Mock database connection for testing
jest.mock('../database', () => ({
  connectDatabase: jest.fn().mockResolvedValue(true),
  disconnectDatabase: jest.fn().mockResolvedValue(true),
}));

// Before all tests
beforeAll(async () => {
  // Any setup needed before all tests
});

// After all tests
afterAll(async () => {
  // Any cleanup needed after all tests
});

// Before each test
beforeEach(() => {
  // Any setup needed before each test
});

// After each test
afterEach(() => {
  // Any cleanup needed after each test
  jest.clearAllMocks();
});
