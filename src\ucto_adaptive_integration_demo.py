"""
Demo script for the Adaptive Optimization and Integration capabilities of the Universal Compliance Tracking Optimizer (UCTO).

This script demonstrates how to use the Adaptive Optimization Manager to adaptively optimize
compliance workflows and the Integration Manager to integrate with other Universal components.
"""

import os
import sys
import json
import logging
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add the src directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Import the UCTO
from ucto import TrackingManager, AdaptiveOptimizationManager, IntegrationManager

def generate_sample_data():
    """Generate sample compliance data for demonstration purposes."""
    logger.info("Generating sample compliance data")
    
    # Initialize the Tracking Manager
    tracking_manager = TrackingManager()
    
    # Generate sample requirements
    requirements = []
    
    # GDPR requirements
    requirements.append(tracking_manager.create_requirement({
        'name': 'Data Subject Rights',
        'description': 'Implement processes for handling data subject rights requests',
        'framework': 'GDPR',
        'category': 'privacy',
        'priority': 'high',
        'status': 'in_progress',
        'due_date': (datetime.now() + timedelta(days=15)).isoformat(),
        'assigned_to': 'privacy_officer',
        'tags': ['gdpr', 'data_subject_rights', 'privacy']
    }))
    
    requirements.append(tracking_manager.create_requirement({
        'name': 'Data Protection Impact Assessment',
        'description': 'Conduct data protection impact assessments for high-risk processing',
        'framework': 'GDPR',
        'category': 'risk_assessment',
        'priority': 'medium',
        'status': 'pending',
        'due_date': (datetime.now() + timedelta(days=45)).isoformat(),
        'assigned_to': 'privacy_officer',
        'tags': ['gdpr', 'dpia', 'risk_assessment']
    }))
    
    requirements.append(tracking_manager.create_requirement({
        'name': 'Data Breach Notification',
        'description': 'Implement processes for notifying authorities of data breaches',
        'framework': 'GDPR',
        'category': 'incident_response',
        'priority': 'high',
        'status': 'pending',
        'due_date': (datetime.now() + timedelta(days=10)).isoformat(),
        'assigned_to': 'security_officer',
        'tags': ['gdpr', 'breach_notification', 'incident_response']
    }))
    
    # SOC 2 requirements
    requirements.append(tracking_manager.create_requirement({
        'name': 'Access Control',
        'description': 'Implement access controls to restrict access to information assets',
        'framework': 'SOC 2',
        'category': 'access_control',
        'priority': 'high',
        'status': 'completed',
        'due_date': (datetime.now() - timedelta(days=15)).isoformat(),
        'assigned_to': 'security_officer',
        'tags': ['soc2', 'access_control', 'security']
    }))
    
    requirements.append(tracking_manager.create_requirement({
        'name': 'Risk Management',
        'description': 'Implement risk management processes to identify and mitigate risks',
        'framework': 'SOC 2',
        'category': 'risk_assessment',
        'priority': 'medium',
        'status': 'in_progress',
        'due_date': (datetime.now() + timedelta(days=30)).isoformat(),
        'assigned_to': 'risk_manager',
        'tags': ['soc2', 'risk_management', 'risk_assessment']
    }))
    
    requirements.append(tracking_manager.create_requirement({
        'name': 'Incident Response',
        'description': 'Implement incident response processes to detect and respond to security incidents',
        'framework': 'SOC 2',
        'category': 'incident_response',
        'priority': 'high',
        'status': 'in_progress',
        'due_date': (datetime.now() + timedelta(days=5)).isoformat(),
        'assigned_to': 'security_officer',
        'tags': ['soc2', 'incident_response', 'security']
    }))
    
    # Generate sample activities
    activities = []
    
    # Activities for Data Subject Rights
    activities.append(tracking_manager.create_activity({
        'name': 'Document Data Subject Rights Process',
        'description': 'Create documentation for handling data subject rights requests',
        'requirement_id': requirements[0]['id'],
        'type': 'documentation',
        'status': 'completed',
        'start_date': (datetime.now() - timedelta(days=15)).isoformat(),
        'end_date': (datetime.now() - timedelta(days=5)).isoformat(),
        'assigned_to': 'privacy_officer',
        'notes': 'Documentation completed and reviewed'
    }))
    
    activities.append(tracking_manager.create_activity({
        'name': 'Implement Data Subject Rights Portal',
        'description': 'Develop a portal for handling data subject rights requests',
        'requirement_id': requirements[0]['id'],
        'type': 'task',
        'status': 'in_progress',
        'start_date': (datetime.now() - timedelta(days=5)).isoformat(),
        'end_date': (datetime.now() + timedelta(days=10)).isoformat(),
        'assigned_to': 'developer',
        'notes': 'Portal development in progress'
    }))
    
    # Activities for Access Control
    activities.append(tracking_manager.create_activity({
        'name': 'Implement Role-Based Access Control',
        'description': 'Implement role-based access control for all systems',
        'requirement_id': requirements[3]['id'],
        'type': 'task',
        'status': 'completed',
        'start_date': (datetime.now() - timedelta(days=30)).isoformat(),
        'end_date': (datetime.now() - timedelta(days=15)).isoformat(),
        'assigned_to': 'security_engineer',
        'notes': 'RBAC implemented for all systems'
    }))
    
    activities.append(tracking_manager.create_activity({
        'name': 'Conduct Access Control Audit',
        'description': 'Audit access controls to ensure proper implementation',
        'requirement_id': requirements[3]['id'],
        'type': 'audit',
        'status': 'completed',
        'start_date': (datetime.now() - timedelta(days=15)).isoformat(),
        'end_date': (datetime.now() - timedelta(days=10)).isoformat(),
        'assigned_to': 'auditor',
        'notes': 'Audit completed with no findings'
    }))
    
    # Activities for Incident Response
    activities.append(tracking_manager.create_activity({
        'name': 'Develop Incident Response Plan',
        'description': 'Develop a comprehensive incident response plan',
        'requirement_id': requirements[5]['id'],
        'type': 'documentation',
        'status': 'completed',
        'start_date': (datetime.now() - timedelta(days=20)).isoformat(),
        'end_date': (datetime.now() - timedelta(days=10)).isoformat(),
        'assigned_to': 'security_officer',
        'notes': 'Incident response plan developed'
    }))
    
    activities.append(tracking_manager.create_activity({
        'name': 'Conduct Incident Response Training',
        'description': 'Train staff on incident response procedures',
        'requirement_id': requirements[5]['id'],
        'type': 'meeting',
        'status': 'in_progress',
        'start_date': (datetime.now() - timedelta(days=5)).isoformat(),
        'end_date': (datetime.now() + timedelta(days=2)).isoformat(),
        'assigned_to': 'trainer',
        'notes': 'Training sessions in progress'
    }))
    
    logger.info(f"Generated {len(requirements)} requirements and {len(activities)} activities")
    
    return requirements, activities

def demonstrate_adaptive_optimization(requirements, activities):
    """Demonstrate adaptive optimization capabilities."""
    logger.info("Demonstrating adaptive optimization capabilities")
    
    # Initialize the Adaptive Optimization Manager
    output_dir = os.path.join(os.path.dirname(__file__), 'adaptive_output')
    os.makedirs(output_dir, exist_ok=True)
    
    adaptive_optimization_manager = AdaptiveOptimizationManager(data_dir=output_dir)
    
    # Step 1: Apply risk-based prioritization
    logger.info("Step 1: Applying risk-based prioritization")
    risk_result = adaptive_optimization_manager.optimize(
        'risk_based_prioritization',
        requirements,
        activities,
        {'risk_threshold': 'high'}
    )
    
    # Save the result to a file
    with open(os.path.join(output_dir, 'risk_based_prioritization.json'), 'w', encoding='utf-8') as f:
        json.dump(risk_result, f, indent=2)
    
    logger.info(f"Saved risk-based prioritization result to {os.path.join(output_dir, 'risk_based_prioritization.json')}")
    
    # Step 2: Apply resource-aware scheduling
    logger.info("Step 2: Applying resource-aware scheduling")
    resource_result = adaptive_optimization_manager.optimize(
        'resource_aware_scheduling',
        requirements,
        activities,
        {'max_utilization': 0.8}
    )
    
    # Save the result to a file
    with open(os.path.join(output_dir, 'resource_aware_scheduling.json'), 'w', encoding='utf-8') as f:
        json.dump(resource_result, f, indent=2)
    
    logger.info(f"Saved resource-aware scheduling result to {os.path.join(output_dir, 'resource_aware_scheduling.json')}")
    
    # Step 3: Apply deadline-driven optimization
    logger.info("Step 3: Applying deadline-driven optimization")
    deadline_result = adaptive_optimization_manager.optimize(
        'deadline_driven_optimization',
        requirements,
        activities,
        {'urgency_factor': 1.5, 'deadline_threshold': 15}
    )
    
    # Save the result to a file
    with open(os.path.join(output_dir, 'deadline_driven_optimization.json'), 'w', encoding='utf-8') as f:
        json.dump(deadline_result, f, indent=2)
    
    logger.info(f"Saved deadline-driven optimization result to {os.path.join(output_dir, 'deadline_driven_optimization.json')}")
    
    # Step 4: Apply adaptive optimization
    logger.info("Step 4: Applying adaptive optimization")
    adaptive_result = adaptive_optimization_manager.adapt_and_optimize(
        requirements,
        activities,
        {
            'resource_utilization': 0.9,
            'regulatory_changes': True,
            'deadline_threshold': 15
        }
    )
    
    # Save the result to a file
    with open(os.path.join(output_dir, 'adaptive_optimization.json'), 'w', encoding='utf-8') as f:
        json.dump(adaptive_result, f, indent=2)
    
    logger.info(f"Saved adaptive optimization result to {os.path.join(output_dir, 'adaptive_optimization.json')}")
    
    return adaptive_result

def demonstrate_integration(requirements, activities):
    """Demonstrate integration capabilities."""
    logger.info("Demonstrating integration capabilities")
    
    # Initialize the Integration Manager
    output_dir = os.path.join(os.path.dirname(__file__), 'integration_output')
    os.makedirs(output_dir, exist_ok=True)
    
    integration_manager = IntegrationManager(config_dir=output_dir)
    
    # Step 1: Configure integrations
    logger.info("Step 1: Configuring integrations")
    
    # Configure UCECS integration
    ucecs_config = integration_manager.configure_integration('ucecs', {
        'module_path': 'ucecs',
        'api_url': 'https://api.example.com/ucecs',
        'api_key': 'sample_api_key',
        'enabled': True
    })
    
    # Configure UCWO integration
    ucwo_config = integration_manager.configure_integration('ucwo', {
        'module_path': 'ucwo',
        'api_url': 'https://api.example.com/ucwo',
        'api_key': 'sample_api_key',
        'enabled': True
    })
    
    # Configure UCVF integration
    ucvf_config = integration_manager.configure_integration('ucvf', {
        'module_path': 'ucvf',
        'api_url': 'https://api.example.com/ucvf',
        'api_key': 'sample_api_key',
        'enabled': True
    })
    
    # Configure UCTF integration
    uctf_config = integration_manager.configure_integration('uctf', {
        'module_path': 'uctf',
        'api_url': 'https://api.example.com/uctf',
        'api_key': 'sample_api_key',
        'enabled': True
    })
    
    # Step 2: Execute integration actions
    logger.info("Step 2: Executing integration actions")
    
    # Execute UCECS integration action
    ucecs_result = integration_manager.execute_integration(
        'ucecs',
        'collect_evidence',
        {
            'requirement_id': requirements[0]['id'],
            'evidence_type': 'document',
            'evidence_name': 'Data Subject Rights Policy',
            'evidence_description': 'Policy document for handling data subject rights requests'
        }
    )
    
    # Save the result to a file
    with open(os.path.join(output_dir, 'ucecs_integration.json'), 'w', encoding='utf-8') as f:
        json.dump(ucecs_result, f, indent=2)
    
    logger.info(f"Saved UCECS integration result to {os.path.join(output_dir, 'ucecs_integration.json')}")
    
    # Execute UCWO integration action
    ucwo_result = integration_manager.execute_integration(
        'ucwo',
        'create_workflow',
        {
            'name': 'Data Breach Notification Workflow',
            'description': 'Workflow for handling data breach notifications',
            'requirement_id': requirements[2]['id'],
            'steps': [
                {
                    'name': 'Detect Breach',
                    'description': 'Detect and confirm a data breach',
                    'assignee': 'security_officer'
                },
                {
                    'name': 'Assess Impact',
                    'description': 'Assess the impact of the breach',
                    'assignee': 'risk_manager'
                },
                {
                    'name': 'Notify Authorities',
                    'description': 'Notify relevant authorities of the breach',
                    'assignee': 'privacy_officer'
                }
            ]
        }
    )
    
    # Save the result to a file
    with open(os.path.join(output_dir, 'ucwo_integration.json'), 'w', encoding='utf-8') as f:
        json.dump(ucwo_result, f, indent=2)
    
    logger.info(f"Saved UCWO integration result to {os.path.join(output_dir, 'ucwo_integration.json')}")
    
    # Execute UCVF integration action
    ucvf_result = integration_manager.execute_integration(
        'ucvf',
        'generate_visualization',
        {
            'type': 'dashboard',
            'name': 'Compliance Status Dashboard',
            'description': 'Dashboard showing compliance status across frameworks',
            'data': {
                'requirements': requirements,
                'activities': activities
            }
        }
    )
    
    # Save the result to a file
    with open(os.path.join(output_dir, 'ucvf_integration.json'), 'w', encoding='utf-8') as f:
        json.dump(ucvf_result, f, indent=2)
    
    logger.info(f"Saved UCVF integration result to {os.path.join(output_dir, 'ucvf_integration.json')}")
    
    # Execute UCTF integration action
    uctf_result = integration_manager.execute_integration(
        'uctf',
        'run_test',
        {
            'test_id': 'test-001',
            'name': 'Access Control Test',
            'description': 'Test access controls for compliance',
            'requirement_id': requirements[3]['id']
        }
    )
    
    # Save the result to a file
    with open(os.path.join(output_dir, 'uctf_integration.json'), 'w', encoding='utf-8') as f:
        json.dump(uctf_result, f, indent=2)
    
    logger.info(f"Saved UCTF integration result to {os.path.join(output_dir, 'uctf_integration.json')}")
    
    # Step 3: Generate integration summary
    logger.info("Step 3: Generating integration summary")
    
    # Create a summary of all integrations
    integration_summary = {
        'timestamp': datetime.now().isoformat(),
        'supported_components': integration_manager.get_supported_components(),
        'configured_components': list(integration_manager.integration_configs.keys()),
        'integration_results': {
            'ucecs': ucecs_result,
            'ucwo': ucwo_result,
            'ucvf': ucvf_result,
            'uctf': uctf_result
        }
    }
    
    # Save the summary to a file
    with open(os.path.join(output_dir, 'integration_summary.json'), 'w', encoding='utf-8') as f:
        json.dump(integration_summary, f, indent=2)
    
    logger.info(f"Saved integration summary to {os.path.join(output_dir, 'integration_summary.json')}")
    
    return integration_summary

def main():
    """Main function."""
    logger.info("Starting Adaptive Optimization and Integration demo")
    
    # Generate sample data
    requirements, activities = generate_sample_data()
    
    # Demonstrate adaptive optimization
    adaptive_result = demonstrate_adaptive_optimization(requirements, activities)
    
    # Demonstrate integration
    integration_result = demonstrate_integration(requirements, activities)
    
    # Generate a comprehensive report
    output_dir = os.path.join(os.path.dirname(__file__), 'demo_output')
    os.makedirs(output_dir, exist_ok=True)
    
    comprehensive_report = {
        'timestamp': datetime.now().isoformat(),
        'requirements_count': len(requirements),
        'activities_count': len(activities),
        'adaptive_optimization': {
            'rules_applied': adaptive_result.get('rules_applied', []),
            'strategies_applied': adaptive_result.get('strategies_applied', [])
        },
        'integration': {
            'supported_components': integration_result.get('supported_components', []),
            'configured_components': integration_result.get('configured_components', [])
        }
    }
    
    # Save the report to a file
    with open(os.path.join(output_dir, 'comprehensive_report.json'), 'w', encoding='utf-8') as f:
        json.dump(comprehensive_report, f, indent=2)
    
    logger.info(f"Saved comprehensive report to {os.path.join(output_dir, 'comprehensive_report.json')}")
    
    logger.info("Adaptive Optimization and Integration demo completed successfully")
    logger.info(f"All output files are in: {os.path.dirname(os.path.abspath(__file__))}")

if __name__ == '__main__':
    main()
