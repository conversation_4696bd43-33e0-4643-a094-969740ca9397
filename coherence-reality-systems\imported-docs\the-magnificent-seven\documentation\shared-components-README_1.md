# NovaFuse Shared Components

Shared components for NovaFuse's Universal frameworks. This repository contains common data models, utilities, and integration mechanisms used across NovaFuse's compliance and governance solutions.

## Overview

NovaFuse Shared Components provides a set of reusable components that enable seamless integration between NovaFuse's Universal frameworks, such as UCTF (Universal Control Testing Framework) and UCVF (Universal Compliance Visualization Framework).

## Key Features

- **Common Data Models**: Standardized data models for compliance and governance
- **Event Bus**: Lightweight event bus for inter-component communication
- **Serialization Utilities**: Utilities for serializing and deserializing data
- **Integration Patterns**: Common patterns for integrating NovaFuse components
- **Reusable Utilities**: Utilities for common compliance and governance tasks

## Components

### Data Models

The shared components include the following data models:

- **TestResult**: A common data model for test results
- **ComplianceFramework**: A common data model for compliance frameworks
- **ComplianceRequirement**: A common data model for compliance requirements
- **ComplianceControl**: A common data model for compliance controls

### Event Bus

The event bus provides a simple mechanism for components to communicate with each other through events:

- **EventBus**: A singleton class that manages event registration and emission
- **Event Handlers**: Functions that handle specific types of events
- **Event Types**: Standardized event types for common operations

### Utilities

The shared components include the following utilities:

- **Serialization**: Utilities for serializing and deserializing data
- **Validation**: Utilities for validating data against schemas
- **Logging**: Standardized logging utilities
- **Configuration**: Utilities for managing configuration

## Installation

```bash
# Clone the repository
git clone https://github.com/novafuse/shared-components.git
cd shared-components

# Install the package
pip install -e .
```

## Usage

### Using the TestResult Model

```python
from shared.models.test_result import TestResult

# Create a test result
test_result = TestResult(
    test_id='gdpr_data_protection',
    name='GDPR Data Protection Test',
    framework='GDPR',
    status='passed',
    score=90.0,
    findings=[
        {
            'id': 'GDPR-1',
            'description': 'Data protection measures are in place',
            'status': 'passed',
            'severity': 'high'
        }
    ],
    metadata={
        'target_system': 'customer_database'
    }
)

# Convert to dictionary
result_dict = test_result.to_dict()

# Convert to JSON
result_json = test_result.to_json()

# Create from dictionary
test_result_from_dict = TestResult.from_dict(result_dict)

# Create from JSON
test_result_from_json = TestResult.from_json(result_json)
```

### Using the Event Bus

```python
from shared.events.event_bus import event_bus

# Define an event handler
def handle_test_completed(event_data):
    print(f"Test completed: {event_data.get('id')}")
    print(f"Test result: {event_data.get('result', {}).get('passed')}")

# Register the event handler
event_bus.register_handler('test_completed', handle_test_completed)

# Emit an event
event_bus.emit('test_completed', {
    'id': 'test-123',
    'result': {
        'passed': True,
        'score': 90
    }
})
```

## Integration with NovaFuse Components

### Integration with UCTF

```python
from uctf import TestEngine
from shared.models.test_result import TestResult
from shared.events.event_bus import event_bus

# Define an event handler for test completion
def handle_test_completed(event_data):
    # Extract test result data
    test_run = event_data
    result = test_run.get('result', {})

    # Create a TestResult object
    test_result = TestResult(
        test_id=result.get('test_id', ''),
        name=result.get('name', ''),
        framework=result.get('framework', ''),
        status='passed' if result.get('passed', False) else 'failed',
        score=result.get('score', 0.0),
        findings=result.get('findings', []),
        metadata=result.get('metadata', {}),
        result_id=test_run.get('id', '')
    )

    # Do something with the test result
    print(f"Test result: {test_result.to_dict()}")

# Register the event handler
event_bus.register_handler('uctf.test_completed', handle_test_completed)

# Initialize the Test Engine
engine = TestEngine()

# Run a test (the event handler will be called when the test completes)
test_run = engine.run_test('gdpr_data_protection', {
    'strict_mode': True,
    'target_system': 'customer_database'
})
```

### Integration with UCVF

```python
from ucvf import VisualizationEngine
from shared.models.test_result import TestResult

# Create a test result
test_result = TestResult(
    test_id='gdpr_data_protection',
    name='GDPR Data Protection Test',
    framework='GDPR',
    status='passed',
    score=90.0,
    findings=[
        {
            'id': 'GDPR-1',
            'description': 'Data protection measures are in place',
            'status': 'passed',
            'severity': 'high'
        }
    ],
    metadata={
        'target_system': 'customer_database'
    }
)

# Initialize the Visualization Engine
engine = VisualizationEngine()

# Generate a visualization for the test result
visualization = engine.generate_visualization(
    data=test_result.to_dict(),
    role='test_result',
    visualization_type='dashboard'
)

# Print the visualization
print(f"Visualization title: {visualization['title']}")
print(f"Visualization subtitle: {visualization['subtitle']}")
```

## License

Copyright © 2023-2025 NovaFuse. All rights reserved.

## Contributing

Please read [CONTRIBUTING.md](CONTRIBUTING.md) for details on our code of conduct and the process for submitting pull requests.