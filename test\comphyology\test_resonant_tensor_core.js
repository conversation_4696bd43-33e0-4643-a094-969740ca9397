/**
 * Resonant Tensor Core Test
 * 
 * This script tests the Resonant Tensor Core implementation, verifying:
 * 1. Resonance validation for tensor values
 * 2. Harmonization of non-resonant values
 * 3. Tensor operations with resonance constraints
 * 4. Unified interface for resonant tensor core
 */

const fs = require('fs');
const path = require('path');
const ResonanceValidator = require('../../src/comphyology/resonance_validator');
const ResonantTensorCore = require('../../src/comphyology/resonant_tensor_core');
const UnifiedResonantTensorCore = require('../../src/comphyology/unified_resonant_tensor_core');

// Create results directory
const RESULTS_DIR = path.join(__dirname, '../../resonance_results');
if (!fs.existsSync(RESULTS_DIR)) {
  fs.mkdirSync(RESULTS_DIR, { recursive: true });
}

/**
 * Test Resonance Validator
 */
function testResonanceValidator() {
  console.log('=== Testing Resonance Validator ===');
  
  // Create resonance validator
  const validator = new ResonanceValidator({
    strictMode: false,
    logValidation: true,
    resonanceLock: true
  });
  
  // Test isResonant method
  console.log('\nTesting isResonant method:');
  const testValues = [3, 6, 9, 12, 13, 0.3, 0.6, 0.9, 0.03, 0.06, 0.09, 0.12, 0.13, 0.7, 0.8, 0.4, 0.07];
  
  testValues.forEach(value => {
    const isResonant = validator.isResonant(value);
    console.log(`Value ${value} is ${isResonant ? 'resonant' : 'not resonant'}`);
  });
  
  // Test harmonize method
  console.log('\nTesting harmonize method:');
  const nonResonantValues = [0.7, 0.8, 0.4, 0.07, 0.25, 0.55, 0.85];
  
  nonResonantValues.forEach(value => {
    const harmonizedValue = validator.harmonize(value);
    console.log(`Value ${value} harmonized to ${harmonizedValue}`);
  });
  
  // Test validate method
  console.log('\nTesting validate method:');
  const valuesToValidate = [0.3, 0.7, 0.9, 0.07];
  
  valuesToValidate.forEach(value => {
    const validationResult = validator.validate(value);
    console.log(`Validation result for ${value}:`, validationResult);
  });
  
  // Test validateTensor method
  console.log('\nTesting validateTensor method:');
  const testTensor = {
    dimensions: [3],
    values: [0.3, 0.7, 0.9, 0.07]
  };
  
  const tensorValidationResult = validator.validateTensor(testTensor);
  console.log('Tensor validation result:', tensorValidationResult);
  
  return {
    validator,
    testValues,
    nonResonantValues,
    valuesToValidate,
    testTensor,
    tensorValidationResult
  };
}

/**
 * Test Resonant Tensor Core with Mock Tensor Core
 */
function testResonantTensorCore() {
  console.log('\n=== Testing Resonant Tensor Core ===');
  
  // Create mock tensor core
  const mockTensorCore = {
    createTensor: (dimensions, values, domain) => ({
      dimensions,
      values,
      domain
    }),
    
    tensorProduct: (tensorA, tensorB) => {
      const resultDimensions = [...tensorA.dimensions, ...tensorB.dimensions];
      const resultValues = [];
      
      for (let i = 0; i < tensorA.values.length; i++) {
        for (let j = 0; j < tensorB.values.length; j++) {
          resultValues.push(tensorA.values[i] * tensorB.values[j]);
        }
      }
      
      return {
        dimensions: resultDimensions,
        values: resultValues,
        domain: tensorA.domain
      };
    },
    
    directSum: (tensorA, tensorB) => {
      const resultDimensions = [tensorA.dimensions[0] + tensorB.dimensions[0]];
      const resultValues = [...tensorA.values, ...tensorB.values];
      
      return {
        dimensions: resultDimensions,
        values: resultValues,
        domain: tensorA.domain
      };
    },
    
    fuseEngines: (csde_tensor, csfe_tensor, csme_tensor) => {
      // Mock implementation of fuseEngines
      const csde_csfe_product = mockTensorCore.tensorProduct(csde_tensor, csfe_tensor);
      const csme_scaled = {
        dimensions: csme_tensor.dimensions,
        values: csme_tensor.values.map(v => v * 3141.59),
        domain: csme_tensor.domain
      };
      
      return mockTensorCore.directSum(csde_csfe_product, csme_scaled);
    }
  };
  
  // Create resonant tensor core
  const resonantTensorCore = new ResonantTensorCore(mockTensorCore, {
    strictMode: false,
    logValidation: true,
    resonanceLock: true,
    trackDrift: true
  });
  
  // Test createTensor method
  console.log('\nTesting createTensor method:');
  const testTensor = resonantTensorCore.createTensor(
    [3],
    [0.3, 0.7, 0.9, 0.07],
    'test'
  );
  
  console.log('Created tensor:', testTensor);
  
  // Test tensorProduct method
  console.log('\nTesting tensorProduct method:');
  const tensorA = resonantTensorCore.createTensor(
    [2],
    [0.3, 0.6],
    'test'
  );
  
  const tensorB = resonantTensorCore.createTensor(
    [2],
    [0.9, 0.3],
    'test'
  );
  
  const productTensor = resonantTensorCore.tensorProduct(tensorA, tensorB);
  console.log('Product tensor:', productTensor);
  
  // Test directSum method
  console.log('\nTesting directSum method:');
  const sumTensor = resonantTensorCore.directSum(tensorA, tensorB);
  console.log('Sum tensor:', sumTensor);
  
  // Test fuseEngines method
  console.log('\nTesting fuseEngines method:');
  const csde_tensor = resonantTensorCore.createTensor(
    [4],
    [0.3, 0.6, 0.9, 0.3],
    'csde'
  );
  
  const csfe_tensor = resonantTensorCore.createTensor(
    [4],
    [0.6, 0.3, 0.6, 0.9],
    'csfe'
  );
  
  const csme_tensor = resonantTensorCore.createTensor(
    [4],
    [0.9, 0.3, 0.6, 0.3],
    'csme'
  );
  
  const fusedTensor = resonantTensorCore.fuseEngines(csde_tensor, csfe_tensor, csme_tensor);
  console.log('Fused tensor:', fusedTensor);
  
  // Get metrics
  console.log('\nResonant Tensor Core Metrics:');
  const metrics = resonantTensorCore.getMetrics();
  console.log(metrics);
  
  return {
    mockTensorCore,
    resonantTensorCore,
    testTensor,
    tensorA,
    tensorB,
    productTensor,
    sumTensor,
    csde_tensor,
    csfe_tensor,
    csme_tensor,
    fusedTensor,
    metrics
  };
}

/**
 * Test Unified Resonant Tensor Core
 */
function testUnifiedResonantTensorCore() {
  console.log('\n=== Testing Unified Resonant Tensor Core ===');
  
  try {
    // Create unified resonant tensor core
    const unifiedCore = new UnifiedResonantTensorCore({
      strictMode: false,
      logValidation: true,
      resonanceLock: true,
      trackDrift: true,
      useEnergyBasedComphyon: true
    });
    
    console.log('Unified Resonant Tensor Core created successfully');
    
    // Test process method
    console.log('\nTesting process method:');
    
    // Create test data
    const csde_data = {
      dimensions: [4],
      values: [0.3, 0.6, 0.9, 0.3]
    };
    
    const csfe_data = {
      dimensions: [4],
      values: [0.6, 0.3, 0.6, 0.9]
    };
    
    const csme_data = {
      dimensions: [4],
      values: [0.9, 0.3, 0.6, 0.3]
    };
    
    // Process data
    const result = unifiedCore.process(csde_data, csfe_data, csme_data);
    console.log('Process result:', result);
    
    // Get metrics
    console.log('\nUnified Resonant Tensor Core Metrics:');
    const metrics = unifiedCore.getMetrics();
    console.log(metrics);
    
    return {
      unifiedCore,
      csde_data,
      csfe_data,
      csme_data,
      result,
      metrics
    };
  } catch (error) {
    console.error('Error testing Unified Resonant Tensor Core:', error);
    return { error };
  }
}

/**
 * Main test function
 */
function main() {
  console.log('=== Resonant Tensor Core Test ===');
  
  // Run tests
  const validatorResults = testResonanceValidator();
  const tensorCoreResults = testResonantTensorCore();
  const unifiedResults = testUnifiedResonantTensorCore();
  
  // Save results to file
  const results = {
    validatorResults,
    tensorCoreResults,
    unifiedResults
  };
  
  fs.writeFileSync(
    path.join(RESULTS_DIR, 'resonant_tensor_core_test_results.json'),
    JSON.stringify(results, null, 2)
  );
  
  console.log(`\nTest results saved to ${path.join(RESULTS_DIR, 'resonant_tensor_core_test_results.json')}`);
}

// Run main test function
main();
