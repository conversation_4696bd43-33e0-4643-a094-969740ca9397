# NovaFuse

NovaFuse is a comprehensive API marketplace and integration platform for GRC (Governance, Risk, and Compliance) APIs, featuring a robust Universal API Connector for seamless integration.

## Overview

NovaFuse is a platform that allows organizations to discover, connect, and manage GRC APIs in a single place. It provides a unified interface for accessing various governance, security, and API management services.

## Repository Structure

NovaFuse is organized into the following repositories:

1. **nova-fuse** (this repository) - Main repository with documentation and project overview
2. **nova-connect** - Universal API Connector for seamless API integration
3. **nova-grc-apis** - Collection of GRC APIs (Privacy, Security, Compliance, etc.)
4. **nova-ui** - UI components for all NovaFuse products with feature toggles
5. **nova-gateway** - API Gateway for routing and managing API requests

## Products

NovaFuse offers the following products:

1. **NovaPrime** - Comprehensive GRC platform with all features
2. **NovaCore** - Freemium version with limited functionality
3. **NovaShield** - Security-focused product
4. **NovaLearn** - Gamification and education platform
5. **NovaAssistAI** - AI-powered chatbot assistant

## Features

- **API Marketplace**: Browse and discover GRC APIs
- **Universal API Connector**: Enterprise-grade solution for seamless API integration
- **GRC APIs**: Comprehensive suite of GRC APIs
- **Feature Toggles**: Enable/disable features based on product tier
- **API Gateway**: Centralized API management and routing

## Architecture

NovaFuse uses a microservices architecture with the following components:

- **API Gateway**: Central entry point for all NovaFuse APIs
- **NovaConnect (Universal API Connector)**:
  - Connector Registry
  - Authentication Service
  - Connector Executor
  - Transformation Engine
  - Monitoring & Logging
- **NovaMarketplace (GRC APIs)**:
  - Privacy Management API
  - Security Assessment API
  - Control Testing API
  - ESG API
  - Regulatory Compliance API
  - Compliance Automation API
- **UI Components**:
  - Marketplace UI
  - NovaConnect UI
  - Documentation Portal
  - Admin Portal

## Migration Status

The codebase has been successfully migrated to the new repository structure. See the following documents for details:

- [Migration Summary](../migration-summary.md)
- [Test Report](../test-report.md)

## Getting Started

### Prerequisites

- Node.js (v14 or higher)
- Docker and Docker Compose
- MongoDB

### Installation

1. Clone all repositories:
   ```
   git clone https://github.com/Dartan1983/nova-fuse.git
   git clone https://github.com/Dartan1983/nova-connect.git
   git clone https://github.com/Dartan1983/nova-grc-apis.git
   git clone https://github.com/Dartan1983/nova-ui.git
   git clone https://github.com/Dartan1983/nova-gateway.git
   ```

2. Set up each repository according to its README instructions.

## Development

### Coding Standards

- Follow the ESLint configuration in each repository
- Write unit tests for all new features
- Maintain test coverage of at least 80%
- Use semantic versioning for releases

### Branching Strategy

- `main` - Production-ready code
- `develop` - Development branch
- `feature/*` - Feature branches
- `bugfix/*` - Bug fix branches
- `release/*` - Release branches

### Pull Request Process

1. Create a feature branch from `develop`
2. Implement your changes
3. Write tests for your changes
4. Submit a pull request to `develop`
5. Ensure all tests pass
6. Get approval from at least one reviewer

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Contact

For more information, contact the NovaFuse <NAME_EMAIL>.
