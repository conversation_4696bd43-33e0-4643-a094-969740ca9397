/**
 * Regulatory Compliance Seeder
 * 
 * This script seeds the database with initial regulatory compliance data.
 */

const mongoose = require('mongoose');
const { RegulatoryFramework, ComplianceRequirement } = require('../models');
const logger = require('../config/logger');

/**
 * Seed regulatory frameworks
 * @returns {Promise<Array>} - Seeded frameworks
 */
const seedRegulatoryFrameworks = async () => {
  try {
    logger.info('Seeding regulatory frameworks...');
    
    // Check if frameworks already exist
    const frameworkCount = await RegulatoryFramework.countDocuments();
    
    if (frameworkCount > 0) {
      logger.info(`Skipping framework seeding, ${frameworkCount} frameworks already exist`);
      return RegulatoryFramework.find();
    }
    
    // Define frameworks to seed
    const frameworks = [
      {
        name: 'General Data Protection Regulation',
        code: 'GDPR',
        version: '2016',
        description: 'The General Data Protection Regulation is a regulation in EU law on data protection and privacy in the European Union and the European Economic Area.',
        category: 'privacy',
        regions: ['EU', 'EEA'],
        countries: ['AT', 'BE', 'BG', 'HR', 'CY', 'CZ', 'DK', 'EE', 'FI', 'FR', 'DE', 'GR', 'HU', 'IE', 'IT', 'LV', 'LT', 'LU', 'MT', 'NL', 'PL', 'PT', 'RO', 'SK', 'SI', 'ES', 'SE', 'GB', 'IS', 'LI', 'NO'],
        effectiveDate: new Date('2018-05-25'),
        lastUpdated: new Date('2018-05-25'),
        authority: 'European Commission',
        authorityUrl: 'https://ec.europa.eu/info/law/law-topic/data-protection_en',
        documentUrl: 'https://eur-lex.europa.eu/legal-content/EN/TXT/PDF/?uri=CELEX:32016R0679',
        status: 'active'
      },
      {
        name: 'California Consumer Privacy Act',
        code: 'CCPA',
        version: '2018',
        description: 'The California Consumer Privacy Act is a state statute intended to enhance privacy rights and consumer protection for residents of California.',
        category: 'privacy',
        regions: ['US-CA'],
        countries: ['US'],
        effectiveDate: new Date('2020-01-01'),
        lastUpdated: new Date('2020-01-01'),
        authority: 'California State Legislature',
        authorityUrl: 'https://www.oag.ca.gov/privacy/ccpa',
        documentUrl: 'https://leginfo.legislature.ca.gov/faces/codes_displayText.xhtml?division=3.&part=4.&lawCode=CIV&title=1.81.5',
        status: 'active'
      },
      {
        name: 'Health Insurance Portability and Accountability Act',
        code: 'HIPAA',
        version: '1996',
        description: 'The Health Insurance Portability and Accountability Act of 1996 is a federal law that required the creation of national standards to protect sensitive patient health information.',
        category: 'privacy',
        regions: ['US'],
        countries: ['US'],
        effectiveDate: new Date('2003-04-14'),
        lastUpdated: new Date('2013-01-25'),
        authority: 'U.S. Department of Health & Human Services',
        authorityUrl: 'https://www.hhs.gov/hipaa/index.html',
        documentUrl: 'https://www.govinfo.gov/content/pkg/PLAW-104publ191/pdf/PLAW-104publ191.pdf',
        status: 'active'
      },
      {
        name: 'ISO/IEC 27001',
        code: 'ISO27001',
        version: '2013',
        description: 'ISO/IEC 27001 is an international standard on how to manage information security.',
        category: 'security',
        regions: ['GLOBAL'],
        countries: [],
        effectiveDate: new Date('2013-10-01'),
        lastUpdated: new Date('2013-10-01'),
        authority: 'International Organization for Standardization',
        authorityUrl: 'https://www.iso.org/isoiec-27001-information-security.html',
        documentUrl: 'https://www.iso.org/standard/54534.html',
        status: 'active'
      },
      {
        name: 'Payment Card Industry Data Security Standard',
        code: 'PCI-DSS',
        version: '3.2.1',
        description: 'The Payment Card Industry Data Security Standard is an information security standard for organizations that handle branded credit cards.',
        category: 'security',
        regions: ['GLOBAL'],
        countries: [],
        effectiveDate: new Date('2018-05-01'),
        lastUpdated: new Date('2018-05-01'),
        authority: 'PCI Security Standards Council',
        authorityUrl: 'https://www.pcisecuritystandards.org/',
        documentUrl: 'https://www.pcisecuritystandards.org/document_library',
        status: 'active'
      }
    ];
    
    // Insert frameworks
    const seededFrameworks = await RegulatoryFramework.insertMany(frameworks);
    
    logger.info(`Seeded ${seededFrameworks.length} regulatory frameworks`);
    
    return seededFrameworks;
  } catch (error) {
    logger.error('Error seeding regulatory frameworks:', error);
    throw error;
  }
};

/**
 * Seed compliance requirements for GDPR
 * @param {Object} gdprFramework - GDPR framework document
 * @returns {Promise<Array>} - Seeded requirements
 */
const seedGdprRequirements = async (gdprFramework) => {
  try {
    logger.info('Seeding GDPR compliance requirements...');
    
    // Check if requirements already exist
    const requirementCount = await ComplianceRequirement.countDocuments({ framework: gdprFramework._id });
    
    if (requirementCount > 0) {
      logger.info(`Skipping GDPR requirement seeding, ${requirementCount} requirements already exist`);
      return ComplianceRequirement.find({ framework: gdprFramework._id });
    }
    
    // Define requirements to seed
    const requirements = [
      {
        framework: gdprFramework._id,
        section: 'Art',
        number: '5',
        title: 'Principles relating to processing of personal data',
        description: 'Personal data shall be processed lawfully, fairly and in a transparent manner in relation to the data subject; collected for specified, explicit and legitimate purposes; adequate, relevant and limited to what is necessary; accurate and, where necessary, kept up to date; kept in a form which permits identification of data subjects for no longer than is necessary; processed in a manner that ensures appropriate security.',
        guidance: 'Implement data processing principles in all data processing activities. Document the lawful basis for processing. Implement data minimization practices. Establish data accuracy procedures. Implement data retention policies. Implement security measures.',
        category: 'accountability',
        risk: 'high',
        implementationEffort: 'high',
        keywords: ['principles', 'lawfulness', 'fairness', 'transparency', 'purpose limitation', 'data minimization', 'accuracy', 'storage limitation', 'integrity', 'confidentiality'],
        evidenceRequired: [
          'Data processing inventory',
          'Privacy notices',
          'Data retention policy',
          'Data security policy'
        ],
        status: 'active'
      },
      {
        framework: gdprFramework._id,
        section: 'Art',
        number: '6',
        title: 'Lawfulness of processing',
        description: 'Processing shall be lawful only if and to the extent that at least one of the following applies: consent, contract, legal obligation, vital interests, public interest, legitimate interests.',
        guidance: 'Identify and document the lawful basis for each processing activity. Ensure that the appropriate lawful basis is applied. Implement processes to obtain and manage consent where applicable. Document legitimate interests assessments where applicable.',
        category: 'accountability',
        risk: 'high',
        implementationEffort: 'medium',
        keywords: ['lawfulness', 'consent', 'contract', 'legal obligation', 'vital interests', 'public interest', 'legitimate interests'],
        evidenceRequired: [
          'Documentation of lawful basis for processing',
          'Consent records',
          'Legitimate interests assessments'
        ],
        status: 'active'
      },
      {
        framework: gdprFramework._id,
        section: 'Art',
        number: '7',
        title: 'Conditions for consent',
        description: 'Where processing is based on consent, the controller shall be able to demonstrate that the data subject has consented to processing of his or her personal data. The data subject shall have the right to withdraw his or her consent at any time.',
        guidance: 'Implement processes to obtain, record, and manage consent. Ensure consent is freely given, specific, informed, and unambiguous. Implement processes to handle consent withdrawal. Regularly review and update consent records.',
        category: 'consent',
        risk: 'high',
        implementationEffort: 'medium',
        keywords: ['consent', 'withdrawal', 'demonstration', 'freely given', 'specific', 'informed', 'unambiguous'],
        evidenceRequired: [
          'Consent records',
          'Consent forms',
          'Consent withdrawal process'
        ],
        status: 'active'
      },
      {
        framework: gdprFramework._id,
        section: 'Art',
        number: '12',
        title: 'Transparent information, communication and modalities for the exercise of the rights of the data subject',
        description: 'The controller shall take appropriate measures to provide information referred to in Articles 13 and 14 and any communication under Articles 15 to 22 and 34 relating to processing to the data subject in a concise, transparent, intelligible and easily accessible form, using clear and plain language.',
        guidance: 'Develop clear and transparent privacy notices. Implement processes to handle data subject requests. Ensure communications with data subjects are in plain language. Provide information in a timely manner.',
        category: 'rights',
        risk: 'medium',
        implementationEffort: 'medium',
        keywords: ['transparency', 'information', 'communication', 'rights', 'plain language', 'accessibility'],
        evidenceRequired: [
          'Privacy notices',
          'Data subject request procedures',
          'Communication templates'
        ],
        status: 'active'
      },
      {
        framework: gdprFramework._id,
        section: 'Art',
        number: '13',
        title: 'Information to be provided where personal data are collected from the data subject',
        description: 'Where personal data relating to a data subject are collected from the data subject, the controller shall, at the time when personal data are obtained, provide the data subject with information about the controller, processing purposes, recipients, transfers, retention period, rights, and more.',
        guidance: 'Develop comprehensive privacy notices for direct collection. Ensure all required information is included. Provide privacy notices at the time of data collection. Regularly review and update privacy notices.',
        category: 'notice',
        risk: 'medium',
        implementationEffort: 'medium',
        keywords: ['privacy notice', 'information', 'direct collection', 'transparency'],
        evidenceRequired: [
          'Privacy notices',
          'Data collection forms'
        ],
        status: 'active'
      }
    ];
    
    // Insert requirements
    const seededRequirements = await ComplianceRequirement.insertMany(requirements);
    
    logger.info(`Seeded ${seededRequirements.length} GDPR compliance requirements`);
    
    return seededRequirements;
  } catch (error) {
    logger.error('Error seeding GDPR compliance requirements:', error);
    throw error;
  }
};

/**
 * Seed compliance requirements for CCPA
 * @param {Object} ccpaFramework - CCPA framework document
 * @returns {Promise<Array>} - Seeded requirements
 */
const seedCcpaRequirements = async (ccpaFramework) => {
  try {
    logger.info('Seeding CCPA compliance requirements...');
    
    // Check if requirements already exist
    const requirementCount = await ComplianceRequirement.countDocuments({ framework: ccpaFramework._id });
    
    if (requirementCount > 0) {
      logger.info(`Skipping CCPA requirement seeding, ${requirementCount} requirements already exist`);
      return ComplianceRequirement.find({ framework: ccpaFramework._id });
    }
    
    // Define requirements to seed
    const requirements = [
      {
        framework: ccpaFramework._id,
        section: '1798',
        number: '100',
        title: 'Right to know what personal information is being collected',
        description: 'A consumer shall have the right to request that a business that collects personal information about the consumer disclose to the consumer the categories and specific pieces of personal information the business has collected.',
        guidance: 'Implement processes to handle consumer requests for information. Develop procedures to verify consumer identity. Ensure systems can identify and retrieve all personal information about a consumer.',
        category: 'rights',
        risk: 'high',
        implementationEffort: 'medium',
        keywords: ['right to know', 'disclosure', 'categories', 'specific pieces', 'collection'],
        evidenceRequired: [
          'Consumer request procedures',
          'Identity verification procedures',
          'Data inventory'
        ],
        status: 'active'
      },
      {
        framework: ccpaFramework._id,
        section: '1798',
        number: '105',
        title: 'Right to deletion',
        description: 'A consumer shall have the right to request that a business delete any personal information about the consumer which the business has collected from the consumer.',
        guidance: 'Implement processes to handle deletion requests. Develop procedures to verify consumer identity. Ensure systems can identify and delete all personal information about a consumer. Document exceptions to deletion.',
        category: 'rights',
        risk: 'high',
        implementationEffort: 'high',
        keywords: ['right to deletion', 'erasure', 'removal'],
        evidenceRequired: [
          'Deletion request procedures',
          'Identity verification procedures',
          'Deletion exception documentation'
        ],
        status: 'active'
      },
      {
        framework: ccpaFramework._id,
        section: '1798',
        number: '110',
        title: 'Right to know what personal information is sold or disclosed',
        description: 'A consumer shall have the right to request that a business that sells or discloses personal information about the consumer disclose to the consumer the categories of personal information sold or disclosed.',
        guidance: 'Implement processes to handle consumer requests for disclosure information. Track all sales and disclosures of personal information. Ensure systems can identify categories of personal information sold or disclosed.',
        category: 'rights',
        risk: 'medium',
        implementationEffort: 'medium',
        keywords: ['right to know', 'disclosure', 'sale', 'categories'],
        evidenceRequired: [
          'Consumer request procedures',
          'Data disclosure tracking',
          'Data sale tracking'
        ],
        status: 'active'
      },
      {
        framework: ccpaFramework._id,
        section: '1798',
        number: '115',
        title: 'Right to opt-out of sale',
        description: 'A consumer shall have the right, at any time, to direct a business that sells personal information about the consumer to third parties not to sell the consumer\'s personal information.',
        guidance: 'Implement a "Do Not Sell My Personal Information" link on website. Develop processes to handle opt-out requests. Ensure systems can track opt-out preferences. Communicate opt-out preferences to third parties.',
        category: 'rights',
        risk: 'high',
        implementationEffort: 'medium',
        keywords: ['right to opt-out', 'do not sell', 'sale', 'third parties'],
        evidenceRequired: [
          'Do Not Sell link implementation',
          'Opt-out request procedures',
          'Opt-out preference tracking'
        ],
        status: 'active'
      },
      {
        framework: ccpaFramework._id,
        section: '1798',
        number: '120',
        title: 'Non-discrimination',
        description: 'A business shall not discriminate against a consumer because the consumer exercised any of the consumer\'s rights under this title.',
        guidance: 'Review pricing and service offerings to ensure non-discrimination. Document legitimate business reasons for any differences in pricing or service. Train staff on non-discrimination requirements.',
        category: 'rights',
        risk: 'medium',
        implementationEffort: 'low',
        keywords: ['non-discrimination', 'equal service', 'equal price'],
        evidenceRequired: [
          'Non-discrimination policy',
          'Staff training materials',
          'Pricing and service documentation'
        ],
        status: 'active'
      }
    ];
    
    // Insert requirements
    const seededRequirements = await ComplianceRequirement.insertMany(requirements);
    
    logger.info(`Seeded ${seededRequirements.length} CCPA compliance requirements`);
    
    return seededRequirements;
  } catch (error) {
    logger.error('Error seeding CCPA compliance requirements:', error);
    throw error;
  }
};

/**
 * Seed regulatory compliance data
 * @returns {Promise<void>}
 */
const seedRegulatoryCompliance = async () => {
  try {
    logger.info('Starting regulatory compliance seeding...');
    
    // Seed frameworks
    const frameworks = await seedRegulatoryFrameworks();
    
    // Find GDPR and CCPA frameworks
    const gdprFramework = frameworks.find(f => f.code === 'GDPR');
    const ccpaFramework = frameworks.find(f => f.code === 'CCPA');
    
    // Seed requirements
    if (gdprFramework) {
      await seedGdprRequirements(gdprFramework);
    }
    
    if (ccpaFramework) {
      await seedCcpaRequirements(ccpaFramework);
    }
    
    logger.info('Regulatory compliance seeding completed successfully');
  } catch (error) {
    logger.error('Error seeding regulatory compliance data:', error);
    throw error;
  }
};

module.exports = seedRegulatoryCompliance;
