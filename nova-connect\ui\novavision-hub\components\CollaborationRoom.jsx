/**
 * CollaborationRoom Component
 * 
 * A component for collaborative rooms.
 */

import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { useCollaboration } from '../collaboration/CollaborationContext';
import { useAuth } from '../auth/AuthContext';
import { useTheme } from '../theme/ThemeContext';
import { Animated } from './Animated';
import CollaborationChat from './CollaborationChat';
import SharedCursor from './SharedCursor';

/**
 * CollaborationRoom component
 * 
 * @param {Object} props - Component props
 * @param {string} [props.roomId] - Room ID (if not using the current room from context)
 * @param {React.ReactNode} props.children - Child components
 * @param {boolean} [props.showChat=true] - Whether to show the chat
 * @param {boolean} [props.showCursors=true] - Whether to show shared cursors
 * @param {boolean} [props.showHeader=true] - Whether to show the header
 * @param {boolean} [props.showControls=true] - Whether to show controls
 * @param {string} [props.className] - Additional CSS class names
 * @param {Object} [props.style] - Additional inline styles
 * @returns {React.ReactElement} CollaborationRoom component
 */
const CollaborationRoom = ({
  roomId: externalRoomId,
  children,
  showChat = true,
  showCursors = true,
  showHeader = true,
  showControls = true,
  className = '',
  style = {}
}) => {
  const { theme } = useTheme();
  const { user } = useAuth();
  const {
    isConnected,
    isLoading,
    error,
    activeUsers,
    activeRooms,
    currentRoom,
    joinRoom,
    leaveRoom,
    createRoom,
    sharedState,
    updateSharedState
  } = useCollaboration();
  
  // State
  const [isChatOpen, setIsChatOpen] = useState(true);
  const [isJoining, setIsJoining] = useState(false);
  const [isLeaving, setIsLeaving] = useState(false);
  const [localError, setLocalError] = useState(null);
  
  // Get room ID
  const roomId = externalRoomId || (currentRoom ? currentRoom.id : null);
  
  // Container ID for shared cursors
  const containerId = `collaboration-room-${roomId || 'default'}`;
  
  // Join room
  useEffect(() => {
    if (externalRoomId && externalRoomId !== currentRoom?.id && isConnected && !isJoining && !isLeaving) {
      const joinSpecifiedRoom = async () => {
        setIsJoining(true);
        setLocalError(null);
        
        try {
          await joinRoom(externalRoomId);
        } catch (err) {
          console.error('Error joining room:', err);
          setLocalError(err);
        } finally {
          setIsJoining(false);
        }
      };
      
      joinSpecifiedRoom();
    }
  }, [externalRoomId, currentRoom, isConnected, isJoining, isLeaving, joinRoom]);
  
  // Handle join room
  const handleJoinRoom = async (roomId) => {
    if (!isConnected || isJoining) {
      return;
    }
    
    setIsJoining(true);
    setLocalError(null);
    
    try {
      await joinRoom(roomId);
    } catch (err) {
      console.error('Error joining room:', err);
      setLocalError(err);
    } finally {
      setIsJoining(false);
    }
  };
  
  // Handle leave room
  const handleLeaveRoom = async () => {
    if (!isConnected || !currentRoom || isLeaving) {
      return;
    }
    
    setIsLeaving(true);
    setLocalError(null);
    
    try {
      await leaveRoom();
    } catch (err) {
      console.error('Error leaving room:', err);
      setLocalError(err);
    } finally {
      setIsLeaving(false);
    }
  };
  
  // Check if user is in the room
  const isUserInRoom = currentRoom && user && currentRoom.members.includes(user.id);
  
  return (
    <div
      id={containerId}
      className={`bg-surface rounded-lg shadow-lg overflow-hidden relative ${className}`}
      style={style}
      data-testid="collaboration-room"
    >
      {/* Header */}
      {showHeader && (
        <div className="bg-background p-4 border-b border-divider flex justify-between items-center">
          <div>
            <h2 className="text-xl font-bold text-textPrimary">
              {currentRoom ? currentRoom.name : 'Collaboration Room'}
            </h2>
            {currentRoom && (
              <p className="text-sm text-textSecondary">
                {currentRoom.description}
              </p>
            )}
          </div>
          
          <div className="flex items-center space-x-2">
            {isConnected ? (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-success bg-opacity-10 text-success">
                <span className="w-2 h-2 rounded-full bg-success mr-1"></span>
                Connected
              </span>
            ) : (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-error bg-opacity-10 text-error">
                <span className="w-2 h-2 rounded-full bg-error mr-1"></span>
                Disconnected
              </span>
            )}
            
            {showControls && (
              <div className="flex items-center space-x-2">
                {currentRoom ? (
                  <button
                    type="button"
                    className="bg-error text-errorContrast px-3 py-1 rounded-md text-sm hover:bg-errorDark transition-colors duration-200"
                    onClick={handleLeaveRoom}
                    disabled={isLeaving}
                  >
                    {isLeaving ? 'Leaving...' : 'Leave Room'}
                  </button>
                ) : (
                  <select
                    className="bg-background border border-divider text-textPrimary rounded-md px-3 py-1 text-sm"
                    value=""
                    onChange={(e) => handleJoinRoom(e.target.value)}
                    disabled={!isConnected || isJoining || activeRooms.length === 0}
                  >
                    <option value="" disabled>
                      {isJoining ? 'Joining...' : 'Join a room'}
                    </option>
                    {activeRooms.map((room) => (
                      <option key={room.id} value={room.id}>
                        {room.name}
                      </option>
                    ))}
                  </select>
                )}
                
                {showChat && (
                  <button
                    type="button"
                    className={`p-2 rounded-md ${isChatOpen ? 'bg-primary text-primaryContrast' : 'bg-background border border-divider text-textPrimary'}`}
                    onClick={() => setIsChatOpen(!isChatOpen)}
                    aria-label={isChatOpen ? 'Close chat' : 'Open chat'}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clipRule="evenodd" />
                    </svg>
                  </button>
                )}
              </div>
            )}
          </div>
        </div>
      )}
      
      {/* Error message */}
      {(error || localError) && (
        <div className="bg-error bg-opacity-10 border border-error text-error px-4 py-3">
          {(error || localError).message || 'An error occurred'}
        </div>
      )}
      
      {/* Loading state */}
      {isLoading && (
        <div className="absolute inset-0 bg-background bg-opacity-50 flex items-center justify-center z-10">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      )}
      
      {/* Content */}
      <div className="flex flex-1 overflow-hidden">
        <div className="flex-1 relative">
          {/* Main content */}
          <div className="p-4">
            {children}
          </div>
          
          {/* Shared cursors */}
          {showCursors && isConnected && roomId && (
            <SharedCursor
              containerId={containerId}
              roomId={roomId}
              showNames
            />
          )}
        </div>
        
        {/* Chat */}
        {showChat && isChatOpen && (
          <Animated
            animation="slideInRight"
            className="w-80 border-l border-divider"
          >
            <CollaborationChat
              roomId={roomId}
              showHeader={false}
              maxHeight="100%"
            />
          </Animated>
        )}
      </div>
      
      {/* Not connected message */}
      {!isConnected && !isLoading && (
        <div className="absolute inset-0 bg-background bg-opacity-75 flex items-center justify-center z-10">
          <div className="bg-surface p-6 rounded-lg shadow-md max-w-md text-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-warning mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
            <h3 className="text-lg font-medium text-textPrimary mb-2">
              Not Connected
            </h3>
            <p className="text-textSecondary mb-4">
              You are not connected to the collaboration service. Please check your connection and try again.
            </p>
          </div>
        </div>
      )}
      
      {/* Not in room message */}
      {isConnected && !currentRoom && !isLoading && (
        <div className="absolute inset-0 bg-background bg-opacity-75 flex items-center justify-center z-10">
          <div className="bg-surface p-6 rounded-lg shadow-md max-w-md text-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-info mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
            <h3 className="text-lg font-medium text-textPrimary mb-2">
              No Room Selected
            </h3>
            <p className="text-textSecondary mb-4">
              Please select a room to join from the dropdown menu.
            </p>
            {activeRooms.length === 0 && (
              <p className="text-warning text-sm">
                No rooms available. Please create a new room.
              </p>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

CollaborationRoom.propTypes = {
  roomId: PropTypes.string,
  children: PropTypes.node.isRequired,
  showChat: PropTypes.bool,
  showCursors: PropTypes.bool,
  showHeader: PropTypes.bool,
  showControls: PropTypes.bool,
  className: PropTypes.string,
  style: PropTypes.object
};

export default CollaborationRoom;
