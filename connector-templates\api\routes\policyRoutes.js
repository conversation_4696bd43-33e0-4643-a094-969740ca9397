/**
 * Policy Routes
 */

const express = require('express');
const router = express.Router();
const PolicyController = require('../controllers/PolicyController');
const { authenticate, hasPermission } = require('../middleware/authMiddleware');

// All routes require authentication
router.use(authenticate);

// Get all policies
router.get('/', (req, res, next) => {
  PolicyController.getAllPolicies(req, res, next);
});

// Get policies for a team
router.get('/team/:id', (req, res, next) => {
  PolicyController.getPoliciesForTeam(req, res, next);
});

// Create a new policy
router.post('/', hasPermission('system:settings'), (req, res, next) => {
  PolicyController.createPolicy(req, res, next);
});

// Get policy by ID
router.get('/:id', (req, res, next) => {
  PolicyController.getPolicyById(req, res, next);
});

// Update a policy
router.put('/:id', hasPermission('system:settings'), (req, res, next) => {
  PolicyController.updatePolicy(req, res, next);
});

// Delete a policy
router.delete('/:id', hasPermission('system:settings'), (req, res, next) => {
  PolicyController.deletePolicy(req, res, next);
});

// Evaluate a resource against policies
router.post('/evaluate/:resourceType', (req, res, next) => {
  PolicyController.evaluateResource(req, res, next);
});

// Get policy violations
router.get('/violations', (req, res, next) => {
  PolicyController.getPolicyViolations(req, res, next);
});

// Get policy violation by ID
router.get('/violations/:id', (req, res, next) => {
  PolicyController.getPolicyViolationById(req, res, next);
});

// Update policy violation
router.put('/violations/:id', (req, res, next) => {
  PolicyController.updatePolicyViolation(req, res, next);
});

// Record a policy violation
router.post('/:policyId/violations', (req, res, next) => {
  PolicyController.recordPolicyViolation(req, res, next);
});

module.exports = router;
