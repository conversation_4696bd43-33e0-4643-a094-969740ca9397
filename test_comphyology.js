/**
 * Test script for Comphyology Framework Integration
 * 
 * This script tests the basic functionality of the Comphyology Framework Integration
 * components, including UUFT, Nested Trinity, 18/82 Principle, πφe Scoring System,
 * and Finite Universe Math.
 */

// Import Comphyology Framework Integration
const { ComphyologyFrameworkIntegration } = require('./src/comphyology/integration/comphyology_framework_integration');
const uuftIntegration = require('./src/comphyology/integration/uuft_integration');
const nestedTrinityIntegration = require('./src/comphyology/integration/nested_trinity_integration');
const principle1882Integration = require('./src/comphyology/integration/principle_1882_integration');
const piPhiEScoringIntegration = require('./src/comphyology/integration/pi_phi_e_scoring_integration');
const finiteUniverseMathIntegration = require('./src/comphyology/integration/finite_universe_math_integration');
const componentAlignmentIntegration = require('./src/comphyology/integration/component_alignment_integration');

// Initialize Comphyology Framework Integration
console.log('Initializing Comphyology Framework Integration...');
const comphyologyFrameworkIntegration = new ComphyologyFrameworkIntegration({
  enableUUFT: true,
  enableNestedTrinity: true,
  enable1882Principle: true,
  enablePiPhiEScoring: true,
  enableFiniteUniverseMath: true
});

// Test UUFT Equation
console.log('\nTesting UUFT Equation...');
const A = 0.5;
const B = 0.7;
const C = 0.3;
const uuftResult = comphyologyFrameworkIntegration.applyUUFTEquation(A, B, C);
console.log(`UUFT Equation (${A} ⊗ ${B} ⊕ ${C}) × π10³ = ${uuftResult}`);

// Test 18/82 Principle
console.log('\nTesting 18/82 Principle...');
const keyComponent = 0.9;
const complementaryComponent = 0.5;
const principle1882Result = comphyologyFrameworkIntegration.apply1882Principle(keyComponent, complementaryComponent);
console.log(`18/82 Principle: ${keyComponent} (18%) + ${complementaryComponent} (82%) = ${principle1882Result}`);

// Test πφe Scoring System
console.log('\nTesting πφe Scoring System...');
const piScore = 0.8; // π (Governance)
const phiScore = 0.7; // φ (Resonance)
const eScore = 0.9; // e (Adaptation)
const piPhiEResult = comphyologyFrameworkIntegration.calculatePiPhiEScore(piScore, phiScore, eScore);
console.log(`πφe Score: π=${piScore}, φ=${phiScore}, e=${eScore} => ${piPhiEResult}`);

// Test Nested Trinity Structure
console.log('\nTesting Nested Trinity Structure...');
const sourceId = 'testComponent';
const data = { value: 42 };
const crossLayerResult = nestedTrinityIntegration.sendCrossLayerData('micro', 'meso', sourceId, data);
console.log('Cross-Layer Communication:', crossLayerResult);

// Test Finite Universe Math
console.log('\nTesting Finite Universe Math...');
const value = 1.5;
const boundaryType = 'computational';
const boundedValue = finiteUniverseMathIntegration.boundedModels.applyBoundary(value, boundaryType);
console.log(`Bounded Value: ${value} => ${boundedValue} (boundary type: ${boundaryType})`);

// Test Component Alignment
console.log('\nTesting Component Alignment...');
try {
  comphyologyFrameworkIntegration.initializeComponentAlignments();
  console.log('Component Alignment Status:', componentAlignmentIntegration.getAlignmentStatus());
} catch (error) {
  console.error('Error initializing component alignments:', error.message);
}

// Get Performance Metrics
console.log('\nPerformance Metrics:');
console.log('UUFT:', uuftIntegration.getPerformanceMetrics());
console.log('Nested Trinity:', nestedTrinityIntegration.getPerformanceMetrics());
console.log('18/82 Principle:', principle1882Integration.getPerformanceMetrics());
console.log('πφe Scoring System:', piPhiEScoringIntegration.getPerformanceMetrics());
console.log('Finite Universe Math:', finiteUniverseMathIntegration.getPerformanceMetrics());
console.log('Component Alignment:', componentAlignmentIntegration.getPerformanceMetrics());
console.log('Overall:', comphyologyFrameworkIntegration.getPerformanceMetrics());

console.log('\nComphyology Framework Integration test completed successfully!');
