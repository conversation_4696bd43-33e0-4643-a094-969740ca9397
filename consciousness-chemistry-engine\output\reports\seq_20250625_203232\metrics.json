{"psi_score": 0.66, "fibonacci_analysis": {"residue_distances": [{"start": 9, "end": 13, "score": 0.7890735827445721, "ratio": 1.574782494883194, "golden_ratio_diff": 0.026730893273828705}, {"start": 7, "end": 12, "score": 0.7194122179819583, "ratio": 1.554926835884648, "golden_ratio_diff": 0.03900236540395791}, {"start": 0, "end": 3, "score": 0.6918533213017959, "ratio": 1.6901001022312805, "golden_ratio_diff": 0.044539307568603304}, {"start": 1, "end": 4, "score": 0.6863098097452875, "ratio": 1.6919891300814633, "golden_ratio_diff": 0.045706790985711454}, {"start": 1, "end": 5, "score": 0.6468643609904224, "ratio": 1.706365561953519, "golden_ratio_diff": 0.054591914519589106}, {"start": 13, "end": 16, "score": 0.6419679261977029, "ratio": 1.7082733980465397, "golden_ratio_diff": 0.0557710220700397}, {"start": 3, "end": 5, "score": 0.5801737404489736, "ratio": 1.7351184256976302, "golden_ratio_diff": 0.0723621615873444}, {"start": 0, "end": 5, "score": 0.5742795105972959, "ratio": 1.498087132062569, "golden_ratio_diff": 0.07413123427648}, {"start": 16, "end": 19, "score": 0.574151023125044, "ratio": 1.4980240800489255, "golden_ratio_diff": 0.07417020256397082}, {"start": 8, "end": 12, "score": 0.5548932873045727, "ratio": 1.4882436657744018, "golden_ratio_diff": 0.08021483100968112}, {"start": 13, "end": 18, "score": 0.5466253914692586, "ratio": 1.4838332110898669, "golden_ratio_diff": 0.0829406419105649}, {"start": 7, "end": 9, "score": 0.5416090285499557, "ratio": 1.754976346215386, "golden_ratio_diff": 0.08463503141321141}], "torsion_angles": [{"start": 14, "end": 16, "score": 0.8802110199909011, "ratio": 1.640054008578141, "golden_ratio_diff": 0.013609120686802702}, {"start": 13, "end": 16, "score": 0.6891042278652718, "ratio": 1.545034882674031, "golden_ratio_diff": 0.04511592870324285}, {"start": 5, "end": 7, "score": 0.6625317370448973, "ratio": 1.5356175414984419, "golden_ratio_diff": 0.05093616563341082}], "overall_score": 0.6519373456905273}, "fibonacci_alignment": {"closest_fibonacci": 21, "difference": 1, "ratio": 0.9523809523809523, "alignment_score": 0.9545454545454545}, "trinity_validation": {"ners": 0.6575812037071581, "nepi": 0.5389999999999999, "nefc": 0.694, "overall": 0.6329324814828632, "passed": false}, "trinity_report": {"scores": {"ners": 0.6575812037071581, "nepi": 0.5389999999999999, "nefc": 0.694, "overall": 0.6329324814828632, "passed": false}, "thresholds": {"ners": 0.7, "nepi": 0.5, "nefc": 0.6}, "weights": {"ners": 0.4, "nepi": 0.3, "nefc": 0.3}, "validation": {"ners": {"score": 0.6575812037071581, "threshold": 0.7, "passed": false, "description": "Neural-Emotional Resonance Score: Measures structural harmony and consciousness resonance."}, "nepi": {"score": 0.5389999999999999, "threshold": 0.5, "passed": true, "description": "Neural-Emotional Potential Index: Evaluates functional potential and adaptability."}, "nefc": {"score": 0.694, "threshold": 0.6, "passed": true, "description": "Neural-Emotional Field Coherence: Assesses field coherence and quantum effects."}, "overall": {"score": 0.6329324814828632, "passed": false, "description": "Overall validation status based on all metrics."}}}}