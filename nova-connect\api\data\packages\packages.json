[{"id": "core", "name": "NovaConnect Core", "description": "Basic functionality for connecting APIs and automating workflows", "tier": "core", "features": ["core.basic_connectors", "core.basic_workflows", "core.basic_authentication", "core.basic_monitoring", "core.basic_logging"], "limits": {"connections": 10, "operations_per_day": 1000, "workflows": 5, "actions_per_workflow": 10, "scheduled_workflows": 2, "alerts": 5}}, {"id": "secure", "name": "NovaConnect Secure", "description": "Enhanced security and compliance features", "tier": "secure", "features": ["core.basic_connectors", "core.basic_workflows", "core.basic_authentication", "core.basic_monitoring", "core.basic_logging", "security.encryption", "security.audit_logs", "security.compliance_checks", "security.vulnerability_scanning", "security.access_controls"], "limits": {"connections": 25, "operations_per_day": 5000, "workflows": 15, "actions_per_workflow": 20, "scheduled_workflows": 5, "alerts": 15}}, {"id": "enterprise", "name": "NovaConnect Enterprise", "description": "Advanced enterprise features for large-scale deployments", "tier": "enterprise", "features": ["core.basic_connectors", "core.basic_workflows", "core.basic_authentication", "core.basic_monitoring", "core.basic_logging", "security.encryption", "security.audit_logs", "security.compliance_checks", "security.vulnerability_scanning", "security.access_controls", "enterprise.advanced_connectors", "enterprise.advanced_workflows", "enterprise.advanced_authentication", "enterprise.advanced_monitoring", "enterprise.advanced_logging", "enterprise.sla", "enterprise.priority_support"], "limits": {"connections": 100, "operations_per_day": 25000, "workflows": 50, "actions_per_workflow": 50, "scheduled_workflows": 20, "alerts": 50}}, {"id": "ai_boost", "name": "NovaConnect AI Boost", "description": "AI-powered features for intelligent automation", "tier": "ai_boost", "features": ["core.basic_connectors", "core.basic_workflows", "core.basic_authentication", "core.basic_monitoring", "core.basic_logging", "security.encryption", "security.audit_logs", "security.compliance_checks", "security.vulnerability_scanning", "security.access_controls", "enterprise.advanced_connectors", "enterprise.advanced_workflows", "enterprise.advanced_authentication", "enterprise.advanced_monitoring", "enterprise.advanced_logging", "enterprise.sla", "enterprise.priority_support", "ai.predictive_analytics", "ai.anomaly_detection", "ai.natural_language_processing", "ai.automated_remediation", "ai.intelligent_recommendations"], "limits": {"connections": -1, "operations_per_day": -1, "workflows": -1, "actions_per_workflow": -1, "scheduled_workflows": -1, "alerts": -1}}]