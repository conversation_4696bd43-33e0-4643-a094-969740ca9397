/**
 * Quantum State Visualization Component for NovaVision
 *
 * This component visualizes quantum states, superpositions, and Bayesian inference
 * networks from the Quantum State Inference Layer.
 */

const { createLogger } = require('../../utils/logger');
const logger = createLogger('quantum-visualization');

/**
 * Quantum State Visualization Component
 */
class QuantumVisualization {
  /**
   * Create a new Quantum State Visualization component
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    this.options = {
      theme: options.theme || 'cyber-safety',
      colorScheme: options.colorScheme || 'quantum',
      animateTransitions: options.animateTransitions !== false,
      showProbabilities: options.showProbabilities !== false,
      showBayesianNetwork: options.showBayesianNetwork !== false,
      showActionableIntelligence: options.showActionableIntelligence !== false,
      detailLevel: options.detailLevel || 'standard', // 'basic', 'standard', 'advanced'
      ...options
    };

    logger.info('Quantum State Visualization component initialized', {
      theme: this.options.theme,
      colorScheme: this.options.colorScheme,
      detailLevel: this.options.detailLevel
    });
  }

  /**
   * Generate visualization schema for quantum states
   * @param {Object} quantumData - Quantum state data
   * @returns {Object} - Visualization schema
   */
  generateQuantumVisualization(quantumData) {
    if (!quantumData) {
      logger.warn('No quantum data provided for visualization');
      return {
        id: 'quantum-visualization',
        type: 'visualization',
        title: 'Quantum State Inference Visualization',
        description: 'No quantum data available',
        theme: this.options.theme,
        colorScheme: this.options.colorScheme,
        sections: []
      };
    }

    logger.info('Generating quantum visualization', {
      states: quantumData.quantumStates ? quantumData.quantumStates.length : 0,
      collapsedStates: quantumData.collapsedStates ? quantumData.collapsedStates.length : 0
    });

    try {
      // Create base visualization schema
      const schema = {
        id: 'quantum-visualization',
        type: 'visualization',
        title: 'Quantum State Inference Visualization',
        description: 'Visualization of quantum states and threat predictions',
        theme: this.options.theme,
        colorScheme: this.options.colorScheme,
        sections: []
      };

      // Add quantum state visualization section
      if (quantumData.quantumStates && quantumData.quantumStates.length > 0) {
        schema.sections.push(this._createQuantumStateSection(quantumData.quantumStates));
      }

      // Add collapsed states visualization section
      if (quantumData.collapsedStates && quantumData.collapsedStates.length > 0) {
        schema.sections.push(this._createCollapsedStateSection(quantumData.collapsedStates));
      }

      // Add Bayesian inference visualization section
      if (this.options.showBayesianNetwork && quantumData.bayesianResults) {
        schema.sections.push(this._createBayesianNetworkSection(quantumData.bayesianResults));
      }

      // Add actionable intelligence visualization section
      if (this.options.showActionableIntelligence && quantumData.actionableIntelligence) {
        schema.sections.push(this._createActionableIntelligenceSection(quantumData.actionableIntelligence));
      }

      // Add metrics section if available
      if (quantumData.metrics) {
        schema.sections.push(this._createMetricsSection(quantumData.metrics));
      }

      // If no sections were added, add a placeholder section
      if (schema.sections.length === 0) {
        schema.sections.push({
          id: 'no-data-section',
          title: 'No Quantum Data',
          description: 'No quantum state data is available for visualization',
          components: [{
            id: 'no-data-message',
            type: 'message',
            messageType: 'info',
            title: 'No Quantum Data Available',
            message: 'There is no quantum state data available for visualization. Please verify a component with quantum inference enabled.'
          }]
        });
      }

      return schema;
    } catch (error) {
      logger.error('Error generating quantum visualization', {
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Create quantum state visualization section
   * @param {Array} quantumStates - Quantum states
   * @returns {Object} - Section schema
   * @private
   */
  _createQuantumStateSection(quantumStates) {
    // Create quantum state visualization components
    const components = [];

    // Add superposition state visualization
    components.push({
      id: 'superposition-visualization',
      type: 'quantum-superposition',
      title: 'Quantum Superposition States',
      description: 'Visualization of threat signals in quantum superposition',
      states: quantumStates.map(state => ({
        id: state.id,
        type: state.type,
        amplitude: state.amplitude,
        phase: state.phase,
        probability: state.probability,
        entropy: state.entropy
      })),
      options: {
        showProbabilities: this.options.showProbabilities,
        animateTransitions: this.options.animateTransitions,
        colorMapping: {
          capability: '#3498db', // Blue
          severity: '#e74c3c',   // Red
          baseline: '#2ecc71',   // Green
          threat: '#9b59b6'      // Purple
        }
      }
    });

    // Add quantum state table for detailed view
    if (this.options.detailLevel === 'advanced') {
      components.push({
        id: 'quantum-state-table',
        type: 'data-table',
        title: 'Quantum State Details',
        columns: [
          { id: 'id', title: 'ID', width: '15%' },
          { id: 'type', title: 'Type', width: '15%' },
          { id: 'amplitude', title: 'Amplitude', width: '15%' },
          { id: 'phase', title: 'Phase', width: '15%' },
          { id: 'probability', title: 'Probability', width: '15%' },
          { id: 'entropy', title: 'Entropy', width: '15%' },
          { id: 'isSuperposition', title: 'Superposition', width: '10%' }
        ],
        data: quantumStates
      });
    }

    // Create section schema
    return {
      id: 'quantum-states-section',
      title: 'Quantum States',
      description: 'Visualization of quantum states representing threat signals',
      components
    };
  }

  /**
   * Create collapsed state visualization section
   * @param {Array} collapsedStates - Collapsed quantum states
   * @returns {Object} - Section schema
   * @private
   */
  _createCollapsedStateSection(collapsedStates) {
    // Create collapsed state visualization components
    const components = [];

    // Add collapsed state visualization
    components.push({
      id: 'collapsed-state-visualization',
      type: 'quantum-collapse',
      title: 'Collapsed Quantum States',
      description: 'Visualization of collapsed quantum states representing actionable threats',
      states: collapsedStates.map(state => ({
        id: state.id,
        type: state.type,
        probability: state.collapsedProbability || state.probability,
        timestamp: state.collapsedTimestamp
      })),
      options: {
        showProbabilities: this.options.showProbabilities,
        animateTransitions: this.options.animateTransitions,
        colorMapping: {
          capability: '#3498db', // Blue
          severity: '#e74c3c',   // Red
          baseline: '#2ecc71',   // Green
          threat: '#9b59b6'      // Purple
        }
      }
    });

    // Add probability distribution chart
    components.push({
      id: 'probability-distribution',
      type: 'chart',
      chartType: 'bar',
      title: 'Threat Probability Distribution',
      data: {
        labels: collapsedStates.map(state => state.id),
        datasets: [{
          label: 'Probability',
          data: collapsedStates.map(state => state.collapsedProbability || state.probability),
          backgroundColor: collapsedStates.map(state => {
            switch (state.type) {
              case 'capability': return '#3498db';
              case 'severity': return '#e74c3c';
              case 'baseline': return '#2ecc71';
              case 'threat': return '#9b59b6';
              default: return '#95a5a6';
            }
          })
        }]
      },
      options: {
        scales: {
          y: {
            beginAtZero: true,
            max: 1
          }
        }
      }
    });

    // Create section schema
    return {
      id: 'collapsed-states-section',
      title: 'Collapsed Quantum States',
      description: 'Visualization of collapsed quantum states representing actionable threats',
      components
    };
  }

  /**
   * Create Bayesian network visualization section
   * @param {Object} bayesianResults - Bayesian inference results
   * @returns {Object} - Section schema
   * @private
   */
  _createBayesianNetworkSection(bayesianResults) {
    // Create Bayesian network visualization components
    const components = [];

    // Extract data for visualization
    const stateIds = Object.keys(bayesianResults.posteriors);
    const priors = stateIds.map(id => bayesianResults.priors[id]);
    const likelihoods = stateIds.map(id => bayesianResults.likelihoods[id]);
    const posteriors = stateIds.map(id => bayesianResults.posteriors[id]);

    // Add Bayesian inference chart
    components.push({
      id: 'bayesian-inference-chart',
      type: 'chart',
      chartType: 'radar',
      title: 'Bayesian Inference Network',
      data: {
        labels: stateIds,
        datasets: [
          {
            label: 'Prior Probability',
            data: priors,
            backgroundColor: 'rgba(52, 152, 219, 0.2)',
            borderColor: 'rgba(52, 152, 219, 1)',
            pointBackgroundColor: 'rgba(52, 152, 219, 1)'
          },
          {
            label: 'Likelihood',
            data: likelihoods,
            backgroundColor: 'rgba(46, 204, 113, 0.2)',
            borderColor: 'rgba(46, 204, 113, 1)',
            pointBackgroundColor: 'rgba(46, 204, 113, 1)'
          },
          {
            label: 'Posterior Probability',
            data: posteriors,
            backgroundColor: 'rgba(155, 89, 182, 0.2)',
            borderColor: 'rgba(155, 89, 182, 1)',
            pointBackgroundColor: 'rgba(155, 89, 182, 1)'
          }
        ]
      },
      options: {
        scales: {
          r: {
            beginAtZero: true,
            max: 1
          }
        }
      }
    });

    // Add Bayesian inference explanation
    components.push({
      id: 'bayesian-inference-explanation',
      type: 'text',
      title: 'Bayesian Inference Explanation',
      content: `
        ## Bayesian Inference in Quantum State Analysis

        The Bayesian inference network calculates the probability of threats based on:

        - **Prior Probability**: Initial belief about the threat
        - **Likelihood**: Probability of observing the evidence given the threat
        - **Posterior Probability**: Updated belief about the threat after observing evidence

        The radar chart shows how the posterior probabilities are calculated using Bayes' theorem:

        P(Threat|Evidence) = P(Evidence|Threat) × P(Threat) / P(Evidence)
      `
    });

    // Create section schema
    return {
      id: 'bayesian-network-section',
      title: 'Bayesian Inference Network',
      description: 'Visualization of Bayesian inference network for threat probability calculation',
      components
    };
  }

  /**
   * Create actionable intelligence visualization section
   * @param {Array} actionableIntelligence - Actionable intelligence
   * @returns {Object} - Section schema
   * @private
   */
  _createActionableIntelligenceSection(actionableIntelligence) {
    // Create actionable intelligence visualization components
    const components = [];

    // Add actionable intelligence table
    components.push({
      id: 'actionable-intelligence-table',
      type: 'data-table',
      title: 'Actionable Intelligence',
      columns: [
        { id: 'stateId', title: 'State ID', width: '20%' },
        { id: 'action.type', title: 'Action Type', width: '20%' },
        { id: 'action.description', title: 'Description', width: '40%' },
        { id: 'confidence', title: 'Confidence', width: '20%' }
      ],
      data: actionableIntelligence,
      options: {
        sortable: true,
        filterable: true,
        pagination: true,
        rowsPerPage: 5
      }
    });

    // Add priority chart
    components.push({
      id: 'action-priority-chart',
      type: 'chart',
      chartType: 'horizontalBar',
      title: 'Action Priorities',
      data: {
        labels: actionableIntelligence.map(item => item.action.description),
        datasets: [{
          label: 'Priority',
          data: actionableIntelligence.map(item => item.action.priority),
          backgroundColor: actionableIntelligence.map(item => {
            // Color based on priority
            const priority = item.action.priority;
            if (priority > 0.7) return '#e74c3c'; // High priority - Red
            if (priority > 0.4) return '#f39c12'; // Medium priority - Orange
            return '#2ecc71'; // Low priority - Green
          })
        }]
      },
      options: {
        scales: {
          x: {
            beginAtZero: true,
            max: 1
          }
        }
      }
    });

    // Create section schema
    return {
      id: 'actionable-intelligence-section',
      title: 'Actionable Intelligence',
      description: 'Visualization of actionable intelligence derived from collapsed quantum states',
      components
    };
  }

  /**
   * Create metrics visualization section
   * @param {Object} metrics - Quantum inference metrics
   * @returns {Object} - Section schema
   * @private
   */
  _createMetricsSection(metrics) {
    // Create metrics visualization components
    const components = [];

    // Add metrics cards
    components.push({
      id: 'quantum-metrics-cards',
      type: 'metric-cards',
      title: 'Quantum Inference Metrics',
      metrics: [
        {
          id: 'inference-count',
          title: 'Inference Count',
          value: metrics.inferenceCount,
          icon: 'analytics'
        },
        {
          id: 'average-inference-time',
          title: 'Avg. Inference Time',
          value: `${metrics.averageInferenceTime.toFixed(2)} ms`,
          icon: 'timer'
        },
        {
          id: 'collapse-events',
          title: 'Collapse Events',
          value: metrics.collapseEvents,
          icon: 'collapse'
        },
        {
          id: 'certainty-rate',
          title: 'Certainty Rate',
          value: `${(metrics.certaintyRate * 100).toFixed(2)}%`,
          icon: 'certainty'
        }
      ]
    });

    // Create section schema
    return {
      id: 'metrics-section',
      title: 'Quantum Inference Metrics',
      description: 'Metrics for the Quantum State Inference Layer',
      components
    };
  }
}

module.exports = QuantumVisualization;
