/**
 * Data Subject Request Model
 * 
 * Represents a request from a data subject to exercise their privacy rights,
 * such as the right to access, rectify, or erase their personal data.
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const subjectRequestSchema = new Schema({
  requestType: {
    type: String,
    required: true,
    enum: ['access', 'rectification', 'erasure', 'restriction', 'portability', 'objection', 'automated_decision'],
    trim: true
  },
  dataSubjectId: {
    type: String,
    trim: true
  },
  dataSubjectName: {
    type: String,
    required: true,
    trim: true
  },
  dataSubjectEmail: {
    type: String,
    required: true,
    trim: true
  },
  dataSubjectPhone: {
    type: String,
    trim: true
  },
  requestDetails: {
    type: String,
    required: true,
    trim: true
  },
  status: {
    type: String,
    required: true,
    enum: ['pending', 'in-progress', 'completed', 'rejected', 'withdrawn'],
    default: 'pending'
  },
  assignedTo: {
    type: String,
    trim: true
  },
  verificationMethod: {
    type: String,
    enum: ['email', 'id_verification', 'other'],
    trim: true
  },
  verificationStatus: {
    type: String,
    enum: ['pending', 'verified', 'failed'],
    default: 'pending'
  },
  verificationDetails: {
    type: String,
    trim: true
  },
  affectedSystems: [{
    systemId: {
      type: String,
      required: true,
      trim: true
    },
    systemName: {
      type: String,
      required: true,
      trim: true
    },
    status: {
      type: String,
      required: true,
      enum: ['pending', 'in-progress', 'completed', 'failed'],
      default: 'pending'
    },
    details: {
      type: String,
      trim: true
    }
  }],
  responseDetails: {
    type: String,
    trim: true
  },
  responseDate: {
    type: Date
  },
  responseMethod: {
    type: String,
    enum: ['email', 'mail', 'phone', 'in-person'],
    trim: true
  },
  responseAttachments: [{
    name: {
      type: String,
      required: true,
      trim: true
    },
    type: {
      type: String,
      required: true,
      trim: true
    },
    url: {
      type: String,
      required: true,
      trim: true
    }
  }],
  notes: [{
    content: {
      type: String,
      required: true,
      trim: true
    },
    createdBy: {
      type: String,
      required: true,
      trim: true
    },
    createdAt: {
      type: Date,
      default: Date.now
    }
  }],
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
  dueDate: {
    type: Date,
    required: true
  },
  completedDate: {
    type: Date
  }
}, {
  timestamps: true
});

// Create a text index for searching
subjectRequestSchema.index({
  dataSubjectName: 'text',
  dataSubjectEmail: 'text',
  requestDetails: 'text'
});

// Pre-save hook to update the updatedAt field
subjectRequestSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Virtual for time remaining until due date
subjectRequestSchema.virtual('timeRemaining').get(function() {
  if (!this.dueDate) return null;
  const now = new Date();
  const dueDate = new Date(this.dueDate);
  const diffTime = dueDate - now;
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays;
});

// Virtual for overdue status
subjectRequestSchema.virtual('isOverdue').get(function() {
  if (!this.dueDate) return false;
  if (this.status === 'completed' || this.status === 'rejected' || this.status === 'withdrawn') return false;
  const now = new Date();
  const dueDate = new Date(this.dueDate);
  return now > dueDate;
});

const SubjectRequest = mongoose.model('SubjectRequest', subjectRequestSchema);

module.exports = SubjectRequest;
