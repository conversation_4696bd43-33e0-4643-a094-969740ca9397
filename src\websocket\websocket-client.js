/**
 * WebSocket Client
 * 
 * This module provides a WebSocket client for real-time communication with the WebSocket server.
 */

const WebSocket = require('ws');
const { EventEmitter } = require('events');
const { performance } = require('perf_hooks');

/**
 * WebSocketClient class
 */
class WebSocketClient extends EventEmitter {
  /**
   * Create a new WebSocketClient instance
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      url: options.url || 'ws://localhost:3001/ws',
      clientId: options.clientId || `client-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      autoReconnect: options.autoReconnect !== undefined ? options.autoReconnect : true,
      reconnectInterval: options.reconnectInterval || 5000, // 5 seconds
      maxReconnectAttempts: options.maxReconnectAttempts || 10,
      pingInterval: options.pingInterval || 30000, // 30 seconds
      enableLogging: options.enableLogging !== undefined ? options.enableLogging : true,
      enableMetrics: options.enableMetrics !== undefined ? options.enableMetrics : true,
      ...options
    };
    
    // Initialize state
    this.state = {
      isConnected: false,
      isConnecting: false,
      reconnectAttempts: 0,
      lastMessageId: 0,
      pendingMessages: new Map(), // messageId -> { resolve, reject, timeout }
      subscriptions: new Map(), // channel -> Set of callbacks
      lastActivity: Date.now()
    };
    
    // Initialize metrics
    this.metrics = {
      messagesReceived: 0,
      messagesSent: 0,
      bytesReceived: 0,
      bytesSent: 0,
      errors: 0,
      reconnects: 0,
      lastMessageTime: 0,
      processingTimeMs: 0
    };
    
    // Bind methods
    this._handleOpen = this._handleOpen.bind(this);
    this._handleMessage = this._handleMessage.bind(this);
    this._handleClose = this._handleClose.bind(this);
    this._handleError = this._handleError.bind(this);
    this._ping = this._ping.bind(this);
    
    if (this.options.enableLogging) {
      console.log('WebSocketClient initialized');
    }
  }
  
  /**
   * Connect to the WebSocket server
   * @returns {Promise<WebSocketClient>} - Promise that resolves when connected
   */
  async connect() {
    if (this.state.isConnected) {
      if (this.options.enableLogging) {
        console.log('WebSocketClient is already connected');
      }
      return this;
    }
    
    if (this.state.isConnecting) {
      if (this.options.enableLogging) {
        console.log('WebSocketClient is already connecting');
      }
      return new Promise((resolve, reject) => {
        this.once('connected', () => resolve(this));
        this.once('error', (error) => reject(error));
      });
    }
    
    this.state.isConnecting = true;
    
    // Build URL with client ID
    const url = new URL(this.options.url);
    url.searchParams.append('clientId', this.options.clientId);
    
    // Create WebSocket
    this.ws = new WebSocket(url.toString());
    
    // Set up event handlers
    this.ws.on('open', this._handleOpen);
    this.ws.on('message', this._handleMessage);
    this.ws.on('close', this._handleClose);
    this.ws.on('error', this._handleError);
    
    // Wait for connection
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        this.ws.removeAllListeners();
        this.state.isConnecting = false;
        reject(new Error('Connection timeout'));
      }, 10000); // 10 seconds timeout
      
      this.once('connected', () => {
        clearTimeout(timeout);
        resolve(this);
      });
      
      this.once('error', (error) => {
        clearTimeout(timeout);
        this.state.isConnecting = false;
        reject(error);
      });
    });
  }
  
  /**
   * Disconnect from the WebSocket server
   * @param {number} [code=1000] - Close code
   * @param {string} [reason='Client disconnecting'] - Close reason
   * @returns {Promise<void>} - Promise that resolves when disconnected
   */
  async disconnect(code = 1000, reason = 'Client disconnecting') {
    if (!this.state.isConnected && !this.state.isConnecting) {
      if (this.options.enableLogging) {
        console.log('WebSocketClient is not connected');
      }
      return;
    }
    
    // Clear intervals
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
      this.pingInterval = null;
    }
    
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }
    
    // Clear pending messages
    for (const [messageId, pending] of this.state.pendingMessages.entries()) {
      clearTimeout(pending.timeout);
      pending.reject(new Error('Client disconnecting'));
      this.state.pendingMessages.delete(messageId);
    }
    
    // Close WebSocket
    if (this.ws) {
      if (this.ws.readyState === WebSocket.OPEN) {
        this.ws.close(code, reason);
      }
      
      this.ws.removeAllListeners();
      this.ws = null;
    }
    
    // Update state
    this.state.isConnected = false;
    this.state.isConnecting = false;
    this.state.reconnectAttempts = 0;
    
    // Emit event
    this.emit('disconnected', {
      code,
      reason,
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log(`WebSocketClient disconnected (${code}: ${reason})`);
    }
  }
  
  /**
   * Handle WebSocket open event
   * @private
   */
  _handleOpen() {
    // Update state
    this.state.isConnected = true;
    this.state.isConnecting = false;
    this.state.reconnectAttempts = 0;
    this.state.lastActivity = Date.now();
    
    // Start ping interval
    this.pingInterval = setInterval(this._ping, this.options.pingInterval);
    
    // Emit event
    this.emit('connected', {
      clientId: this.options.clientId,
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log('WebSocketClient connected');
    }
    
    // Resubscribe to channels
    for (const channel of this.state.subscriptions.keys()) {
      this.subscribe(channel).catch((error) => {
        if (this.options.enableLogging) {
          console.error(`WebSocketClient: Error resubscribing to channel ${channel}:`, error);
        }
      });
    }
  }
  
  /**
   * Handle WebSocket message event
   * @param {string|Buffer} data - Message data
   * @private
   */
  _handleMessage(data) {
    const startTime = performance.now();
    
    // Update metrics
    this.metrics.messagesReceived++;
    this.metrics.bytesReceived += data.length;
    this.metrics.lastMessageTime = Date.now();
    this.state.lastActivity = Date.now();
    
    // Parse message
    let message;
    try {
      message = JSON.parse(data);
    } catch (error) {
      if (this.options.enableLogging) {
        console.error('WebSocketClient: Invalid JSON message:', error);
      }
      
      this.metrics.errors++;
      return;
    }
    
    // Handle message based on type
    switch (message.type) {
      case 'welcome':
        this._handleWelcomeMessage(message);
        break;
        
      case 'pong':
        this._handlePongMessage(message);
        break;
        
      case 'subscribed':
        this._handleSubscribedMessage(message);
        break;
        
      case 'unsubscribed':
        this._handleUnsubscribedMessage(message);
        break;
        
      case 'published':
        this._handlePublishedMessage(message);
        break;
        
      case 'message':
        this._handleChannelMessage(message);
        break;
        
      case 'error':
        this._handleErrorMessage(message);
        break;
        
      case 'heartbeat':
        // Just update last activity
        break;
        
      default:
        // Emit message event for custom handling
        this.emit('message', message);
    }
    
    // Resolve pending message
    if (message.id && this.state.pendingMessages.has(message.id)) {
      const pending = this.state.pendingMessages.get(message.id);
      clearTimeout(pending.timeout);
      pending.resolve(message);
      this.state.pendingMessages.delete(message.id);
    }
    
    // Update metrics
    this.metrics.processingTimeMs += performance.now() - startTime;
  }
  
  /**
   * Handle WebSocket close event
   * @param {number} code - Close code
   * @param {string} reason - Close reason
   * @private
   */
  _handleClose(code, reason) {
    const wasConnected = this.state.isConnected;
    
    // Update state
    this.state.isConnected = false;
    this.state.isConnecting = false;
    
    // Clear ping interval
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
      this.pingInterval = null;
    }
    
    // Reject pending messages
    for (const [messageId, pending] of this.state.pendingMessages.entries()) {
      clearTimeout(pending.timeout);
      pending.reject(new Error(`WebSocket closed (${code}: ${reason})`));
      this.state.pendingMessages.delete(messageId);
    }
    
    // Emit event
    if (wasConnected) {
      this.emit('disconnected', {
        code,
        reason,
        timestamp: Date.now()
      });
      
      if (this.options.enableLogging) {
        console.log(`WebSocketClient disconnected (${code}: ${reason})`);
      }
    }
    
    // Reconnect if enabled
    if (this.options.autoReconnect && 
        code !== 1000 && // Normal closure
        code !== 1001 && // Going away
        this.state.reconnectAttempts < this.options.maxReconnectAttempts) {
      
      this.state.reconnectAttempts++;
      
      const delay = Math.min(
        this.options.reconnectInterval * Math.pow(1.5, this.state.reconnectAttempts - 1),
        30000 // Max 30 seconds
      );
      
      if (this.options.enableLogging) {
        console.log(`WebSocketClient: Reconnecting in ${delay}ms (attempt ${this.state.reconnectAttempts}/${this.options.maxReconnectAttempts})`);
      }
      
      this.reconnectTimeout = setTimeout(() => {
        this.metrics.reconnects++;
        this.connect().catch((error) => {
          if (this.options.enableLogging) {
            console.error('WebSocketClient: Reconnect error:', error);
          }
        });
      }, delay);
    }
  }
  
  /**
   * Handle WebSocket error event
   * @param {Error} error - Error object
   * @private
   */
  _handleError(error) {
    // Update metrics
    this.metrics.errors++;
    
    // Emit event
    this.emit('error', error);
    
    if (this.options.enableLogging) {
      console.error('WebSocketClient error:', error);
    }
  }
  
  /**
   * Handle welcome message
   * @param {Object} message - Message data
   * @private
   */
  _handleWelcomeMessage(message) {
    if (this.options.enableLogging) {
      console.log(`WebSocketClient: Received welcome message (Client ID: ${message.clientId})`);
    }
    
    // Emit event
    this.emit('welcome', message);
  }
  
  /**
   * Handle pong message
   * @param {Object} message - Message data
   * @private
   */
  _handlePongMessage(message) {
    // Calculate round-trip time
    const rtt = Date.now() - message.timestamp;
    
    // Emit event
    this.emit('pong', {
      rtt,
      timestamp: message.timestamp
    });
    
    if (this.options.enableLogging) {
      console.log(`WebSocketClient: Received pong (RTT: ${rtt}ms)`);
    }
  }
  
  /**
   * Handle subscribed message
   * @param {Object} message - Message data
   * @private
   */
  _handleSubscribedMessage(message) {
    if (this.options.enableLogging) {
      console.log(`WebSocketClient: Subscribed to channel ${message.channel}`);
    }
    
    // Emit event
    this.emit('subscribed', {
      channel: message.channel,
      timestamp: message.timestamp
    });
  }
  
  /**
   * Handle unsubscribed message
   * @param {Object} message - Message data
   * @private
   */
  _handleUnsubscribedMessage(message) {
    if (this.options.enableLogging) {
      console.log(`WebSocketClient: Unsubscribed from channel ${message.channel}`);
    }
    
    // Emit event
    this.emit('unsubscribed', {
      channel: message.channel,
      timestamp: message.timestamp
    });
  }
  
  /**
   * Handle published message
   * @param {Object} message - Message data
   * @private
   */
  _handlePublishedMessage(message) {
    if (this.options.enableLogging) {
      console.log(`WebSocketClient: Published message to channel ${message.channel}`);
    }
    
    // Emit event
    this.emit('published', {
      channel: message.channel,
      timestamp: message.timestamp
    });
  }
  
  /**
   * Handle channel message
   * @param {Object} message - Message data
   * @private
   */
  _handleChannelMessage(message) {
    const channel = message.channel;
    const data = message.data;
    
    // Emit channel-specific event
    this.emit(`message:${channel}`, data);
    
    // Call channel callbacks
    if (this.state.subscriptions.has(channel)) {
      for (const callback of this.state.subscriptions.get(channel)) {
        try {
          callback(data, channel);
        } catch (error) {
          if (this.options.enableLogging) {
            console.error(`WebSocketClient: Error in channel callback for ${channel}:`, error);
          }
        }
      }
    }
    
    // Emit general message event
    this.emit('channel-message', {
      channel,
      data,
      timestamp: message.timestamp
    });
  }
  
  /**
   * Handle error message
   * @param {Object} message - Message data
   * @private
   */
  _handleErrorMessage(message) {
    // Update metrics
    this.metrics.errors++;
    
    // Emit event
    this.emit('server-error', {
      error: message.error,
      timestamp: message.timestamp
    });
    
    if (this.options.enableLogging) {
      console.error(`WebSocketClient: Server error: ${message.error}`);
    }
  }
  
  /**
   * Send ping to server
   * @private
   */
  _ping() {
    this.send({
      type: 'ping',
      timestamp: Date.now()
    }).catch((error) => {
      if (this.options.enableLogging) {
        console.error('WebSocketClient: Error sending ping:', error);
      }
    });
  }
  
  /**
   * Send a message to the server
   * @param {Object} message - Message to send
   * @param {number} [timeout=10000] - Timeout in milliseconds
   * @returns {Promise<Object>} - Promise that resolves with the response
   */
  async send(message, timeout = 10000) {
    if (!this.state.isConnected) {
      throw new Error('WebSocketClient is not connected');
    }
    
    // Add message ID if not provided
    if (!message.id) {
      message.id = ++this.state.lastMessageId;
    }
    
    // Add timestamp if not provided
    if (!message.timestamp) {
      message.timestamp = Date.now();
    }
    
    // Send message
    return new Promise((resolve, reject) => {
      try {
        const messageStr = JSON.stringify(message);
        this.ws.send(messageStr);
        
        // Update metrics
        this.metrics.messagesSent++;
        this.metrics.bytesSent += messageStr.length;
        this.state.lastActivity = Date.now();
        
        // Set timeout for response
        const timeoutId = setTimeout(() => {
          this.state.pendingMessages.delete(message.id);
          reject(new Error(`Timeout waiting for response to message ${message.id}`));
        }, timeout);
        
        // Store pending message
        this.state.pendingMessages.set(message.id, {
          resolve,
          reject,
          timeout: timeoutId,
          sentAt: Date.now()
        });
      } catch (error) {
        this.metrics.errors++;
        reject(error);
      }
    });
  }
  
  /**
   * Subscribe to a channel
   * @param {string} channel - Channel name
   * @param {Function} [callback] - Callback function for messages
   * @returns {Promise<Object>} - Promise that resolves when subscribed
   */
  async subscribe(channel, callback) {
    // Send subscribe message
    const response = await this.send({
      type: 'subscribe',
      channel
    });
    
    // Store subscription
    if (!this.state.subscriptions.has(channel)) {
      this.state.subscriptions.set(channel, new Set());
    }
    
    // Add callback if provided
    if (callback) {
      this.state.subscriptions.get(channel).add(callback);
    }
    
    return response;
  }
  
  /**
   * Unsubscribe from a channel
   * @param {string} channel - Channel name
   * @param {Function} [callback] - Callback function to remove (if not provided, removes all callbacks)
   * @returns {Promise<Object>} - Promise that resolves when unsubscribed
   */
  async unsubscribe(channel, callback) {
    // Remove callback if provided
    if (callback && this.state.subscriptions.has(channel)) {
      this.state.subscriptions.get(channel).delete(callback);
      
      // If there are still callbacks, don't unsubscribe from the channel
      if (this.state.subscriptions.get(channel).size > 0) {
        return { type: 'unsubscribed', channel };
      }
    }
    
    // Send unsubscribe message
    const response = await this.send({
      type: 'unsubscribe',
      channel
    });
    
    // Remove subscription
    this.state.subscriptions.delete(channel);
    
    return response;
  }
  
  /**
   * Publish a message to a channel
   * @param {string} channel - Channel name
   * @param {*} data - Message data
   * @returns {Promise<Object>} - Promise that resolves when published
   */
  async publish(channel, data) {
    return this.send({
      type: 'publish',
      channel,
      data
    });
  }
  
  /**
   * Get metrics
   * @returns {Object} - Metrics
   */
  getMetrics() {
    return { ...this.metrics };
  }
  
  /**
   * Get subscriptions
   * @returns {Array} - Array of channel names
   */
  getSubscriptions() {
    return Array.from(this.state.subscriptions.keys());
  }
  
  /**
   * Check if connected
   * @returns {boolean} - Whether the client is connected
   */
  isConnected() {
    return this.state.isConnected;
  }
}

module.exports = WebSocketClient;
