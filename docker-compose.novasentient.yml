version: '3.8'

services:
  novasentient:
    build:
      context: .
      dockerfile: Dockerfile.novasentient
    image: novafuse/novasentient:1.0.0-pi-coherence
    container_name: novasentient-consciousness-platform
    restart: unless-stopped
    
    environment:
      - NOVASENTIENT_MODE=production
      - PI_COHERENCE_ENABLED=true
      - CONSCIOUSNESS_THRESHOLD=0.618
      - PERFORMANCE_MULTIPLIER=3.142
      - PYTHONPATH=/app/src
      
    ports:
      - "8080:8080"
      
    volumes:
      - novasentient-logs:/app/logs
      - novasentient-data:/app/data
      
    labels:
      - "com.novafuse.service=novasentient"
      - "com.novafuse.version=1.0.0"
      - "com.novafuse.pi-coherence=enabled"
      - "com.novafuse.consciousness=active"
      - "com.novafuse.modules=6"
      - "com.novafuse.breakthrough=pi-coherence-timing"
      
    healthcheck:
      test: ["CMD", "python", "-c", "from src.novasentientx.nova_pi import NovaPi; pi = NovaPi(); print('Health: π-Coherence OK')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
      
    networks:
      - novasentient-network
      
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

volumes:
  novasentient-logs:
    driver: local
    labels:
      - "com.novafuse.volume=logs"
  novasentient-data:
    driver: local
    labels:
      - "com.novafuse.volume=data"

networks:
  novasentient-network:
    driver: bridge
    labels:
      - "com.novafuse.network=consciousness-platform"
