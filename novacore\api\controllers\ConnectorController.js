/**
 * NovaCore Connector Controller
 * 
 * This controller handles API requests related to connector management.
 */

const { ConnectorService } = require('../services');
const logger = require('../../config/logger');

class ConnectorController {
  /**
   * Create a new connector
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async createConnector(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const connector = await ConnectorService.createConnector(req.body, userId);
      
      res.status(201).json({
        success: true,
        data: connector
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Get all connectors
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async getAllConnectors(req, res, next) {
    try {
      // Extract filter parameters from query
      const filter = {};
      
      if (req.query.name) filter.name = req.query.name;
      if (req.query.category) filter.category = req.query.category;
      if (req.query.status) filter.status = req.query.status;
      if (req.query.tags) filter.tags = req.query.tags.split(',');
      if (req.query.isPublic !== undefined) filter.isPublic = req.query.isPublic === 'true';
      
      // Extract pagination and sorting options
      const options = {
        page: parseInt(req.query.page, 10) || 1,
        limit: parseInt(req.query.limit, 10) || 10
      };
      
      if (req.query.sort) {
        const [field, order] = req.query.sort.split(':');
        options.sort = { [field]: order === 'desc' ? -1 : 1 };
      }
      
      const result = await ConnectorService.getAllConnectors(filter, options);
      
      res.status(200).json({
        success: true,
        data: result.data,
        pagination: result.pagination
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Get connector by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async getConnectorById(req, res, next) {
    try {
      const connector = await ConnectorService.getConnectorById(req.params.id);
      
      res.status(200).json({
        success: true,
        data: connector
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Update connector by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async updateConnector(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const connector = await ConnectorService.updateConnector(req.params.id, req.body, userId);
      
      res.status(200).json({
        success: true,
        data: connector
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Delete connector by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async deleteConnector(req, res, next) {
    try {
      await ConnectorService.deleteConnector(req.params.id);
      
      res.status(200).json({
        success: true,
        message: 'Connector deleted successfully'
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Test connector connection
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async testConnection(req, res, next) {
    try {
      const result = await ConnectorService.testConnection(req.params.id, req.body);
      
      res.status(200).json({
        success: result.success,
        data: result
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Execute connector endpoint
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async executeEndpoint(req, res, next) {
    try {
      const { id, endpointId } = req.params;
      const { credentials, parameters } = req.body;
      
      const result = await ConnectorService.executeEndpoint(id, endpointId, credentials, parameters);
      
      res.status(200).json({
        success: result.success,
        data: result
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Find connectors by category
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async findByCategory(req, res, next) {
    try {
      const connectors = await ConnectorService.findByCategory(req.params.category);
      
      res.status(200).json({
        success: true,
        data: connectors
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Find connectors by tags
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async findByTags(req, res, next) {
    try {
      const tags = req.query.tags.split(',');
      const connectors = await ConnectorService.findByTags(tags);
      
      res.status(200).json({
        success: true,
        data: connectors
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Find active connectors
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async findActive(req, res, next) {
    try {
      const connectors = await ConnectorService.findActive();
      
      res.status(200).json({
        success: true,
        data: connectors
      });
    } catch (error) {
      next(error);
    }
  }
}

module.exports = new ConnectorController();
