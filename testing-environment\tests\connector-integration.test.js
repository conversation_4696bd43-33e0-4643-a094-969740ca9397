const axios = require('axios');

// Configuration
const registryApiUrl = process.env.REGISTRY_API_URL || 'http://localhost:3001';
const authServiceUrl = process.env.AUTH_SERVICE_URL || 'http://localhost:3002';
const connectorExecutorUrl = process.env.CONNECTOR_EXECUTOR_URL || 'http://localhost:3003';
const usageMeteringUrl = process.env.USAGE_METERING_URL || 'http://localhost:3004';
const mockApisUrl = process.env.MOCK_APIS_URL || 'http://localhost:3005';

// Test data
const testConnector = {
  metadata: {
    name: 'AWS Security Hub Test Connector',
    version: '1.0.0',
    category: 'Cloud Security',
    description: 'Test connector for AWS Security Hub',
    author: 'NovaGRC',
    tags: ['aws', 'security', 'cloud']
  },
  authentication: {
    type: 'AWS_SIG_V4',
    fields: {
      accessKeyId: {
        type: 'string',
        description: 'AWS Access Key ID',
        required: true,
        sensitive: false
      },
      secretAccessKey: {
        type: 'string',
        description: 'AWS Secret Access Key',
        required: true,
        sensitive: true
      },
      region: {
        type: 'string',
        description: 'AWS Region',
        required: true,
        sensitive: false
      }
    },
    testConnection: {
      endpoint: '/health',
      method: 'GET',
      expectedResponse: {
        status: 200
      }
    }
  },
  configuration: {
    baseUrl: 'http://mock-apis:3000/aws',
    headers: {
      'Content-Type': 'application/json'
    },
    timeout: 30000,
    retryPolicy: {
      maxRetries: 3,
      backoffStrategy: 'exponential'
    }
  },
  endpoints: [
    {
      id: 'getFindings',
      name: 'Get Findings',
      path: '/securityhub/findings',
      method: 'GET',
      parameters: {
        query: {},
        path: {},
        body: {}
      },
      response: {
        successCode: 200,
        dataPath: '$.Findings',
        schema: {}
      }
    }
  ],
  mappings: [
    {
      sourceEndpoint: 'getFindings',
      targetSystem: 'NovaGRC',
      targetEntity: 'ComplianceFindings',
      transformations: [
        {
          source: '$.Findings[*].Id',
          target: 'findingIds',
          transform: 'identity'
        },
        {
          source: '$.Findings[*].Title',
          target: 'findingTitles',
          transform: 'identity'
        },
        {
          source: '$.Findings[*].Severity.Label',
          target: 'severities',
          transform: 'mapSeverityToRisk'
        },
        {
          source: '$.Findings[*].Compliance.Status',
          target: 'complianceStatuses',
          transform: 'mapComplianceStatus'
        }
      ]
    }
  ],
  events: {
    webhooks: [],
    polling: [
      {
        endpoint: 'getFindings',
        interval: '1h',
        condition: ''
      }
    ]
  }
};

const testCredentials = {
  name: 'Test AWS Credentials',
  connectorId: '',  // Will be set after connector creation
  authType: 'AWS_SIG_V4',
  credentials: {
    accessKeyId: 'AKIAIOSFODNN7EXAMPLE',
    secretAccessKey: 'wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY',
    region: 'us-east-1'
  },
  userId: 'test-user'
};

// Helper function to wait for services to be ready
const waitForServices = async () => {
  const services = [
    { name: 'Registry API', url: `${registryApiUrl}/health` },
    { name: 'Auth Service', url: `${authServiceUrl}/health` },
    { name: 'Connector Executor', url: `${connectorExecutorUrl}/health` },
    { name: 'Usage Metering', url: `${usageMeteringUrl}/health` },
    { name: 'Mock APIs', url: `${mockApisUrl}/health` }
  ];
  
  for (const service of services) {
    let ready = false;
    let attempts = 0;
    
    while (!ready && attempts < 10) {
      try {
        const response = await axios.get(service.url);
        if (response.status === 200) {
          console.log(`${service.name} is ready`);
          ready = true;
        }
      } catch (err) {
        console.log(`Waiting for ${service.name}...`);
        await new Promise(resolve => setTimeout(resolve, 1000));
        attempts++;
      }
    }
    
    if (!ready) {
      throw new Error(`${service.name} is not ready after 10 attempts`);
    }
  }
};

describe('Connector Integration Tests', () => {
  let connectorId;
  let credentialId;
  
  beforeAll(async () => {
    // Wait for services to be ready
    await waitForServices();
    
    // Create test connector
    const connectorResponse = await axios.post(`${registryApiUrl}/connectors`, testConnector);
    connectorId = connectorResponse.data._id;
    console.log(`Created test connector with ID: ${connectorId}`);
    
    // Update test credentials with connector ID
    testCredentials.connectorId = connectorId;
    
    // Create test credentials
    const credentialResponse = await axios.post(`${authServiceUrl}/credentials`, testCredentials);
    credentialId = credentialResponse.data._id;
    console.log(`Created test credentials with ID: ${credentialId}`);
  });
  
  afterAll(async () => {
    // Clean up test data
    try {
      await axios.delete(`${registryApiUrl}/connectors/${connectorId}`);
      await axios.delete(`${authServiceUrl}/credentials/${credentialId}`);
    } catch (err) {
      console.error('Error cleaning up test data:', err.message);
    }
  });
  
  // Test AWS Security Hub connector
  describe('AWS Security Hub Connector', () => {
    it('should fetch and transform findings', async () => {
      const response = await axios.post(`${connectorExecutorUrl}/execute/${connectorId}/getFindings`, {
        credentialId,
        parameters: {},
        userId: 'test-user'
      });
      
      expect(response.status).toBe(200);
      expect(response.data.success).toBe(true);
      expect(response.data.data).toHaveProperty('targetSystem', 'NovaGRC');
      expect(response.data.data).toHaveProperty('targetEntity', 'ComplianceFindings');
      expect(response.data.data.data).toHaveProperty('findingIds');
      expect(Array.isArray(response.data.data.data.findingIds)).toBe(true);
      expect(response.data.data.data).toHaveProperty('findingTitles');
      expect(Array.isArray(response.data.data.data.findingTitles)).toBe(true);
      expect(response.data.data.data).toHaveProperty('severities');
      expect(Array.isArray(response.data.data.data.severities)).toBe(true);
      expect(response.data.data.data).toHaveProperty('complianceStatuses');
      expect(Array.isArray(response.data.data.data.complianceStatuses)).toBe(true);
    });
  });
  
  // Test usage tracking
  describe('Usage Tracking', () => {
    it('should track API usage', async () => {
      // Execute the connector to generate usage data
      await axios.post(`${connectorExecutorUrl}/execute/${connectorId}/getFindings`, {
        credentialId,
        parameters: {},
        userId: 'test-user'
      });
      
      // Get usage data
      const response = await axios.get(`${usageMeteringUrl}/usage?userId=test-user`);
      
      expect(response.status).toBe(200);
      expect(Array.isArray(response.data)).toBe(true);
      expect(response.data.length).toBeGreaterThan(0);
      
      const usage = response.data.find(item => item.connectorId === connectorId && item.endpointId === 'getFindings');
      expect(usage).toBeDefined();
      expect(usage.count).toBeGreaterThan(0);
      expect(usage.successCount).toBeGreaterThan(0);
    });
  });
});
