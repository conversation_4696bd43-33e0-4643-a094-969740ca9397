/**
 * Comphyology Visualization Performance Test
 * 
 * This script tests the performance of the Comphyology visualization components,
 * comparing different performance modes and caching strategies.
 */

const fs = require('fs');
const path = require('path');
const { ComphyologyCore } = require('../../src/comphyology/index');
const ComphyologyVisualization = require('../../src/comphyology/visualization');

// Create results directory
const RESULTS_DIR = path.join(__dirname, '../../comphyology_results');
if (!fs.existsSync(RESULTS_DIR)) {
  fs.mkdirSync(RESULTS_DIR, { recursive: true });
}

/**
 * Test Morphological Resonance Field performance
 */
function testMorphologicalResonancePerformance() {
  console.log('\n=== Testing Morphological Resonance Field Performance ===');
  
  // Initialize visualization generator
  const visualizer = new ComphyologyVisualization({ 
    enableLogging: true,
    resolution: 50
  });
  
  // Test scenarios
  const scenarios = [
    { name: 'High Performance Mode', performanceMode: 'high', useCache: false },
    { name: 'Balanced Performance Mode', performanceMode: 'balanced', useCache: false },
    { name: 'Low Performance Mode', performanceMode: 'low', useCache: false },
    { name: 'High Performance Mode with Cache', performanceMode: 'high', useCache: true },
    { name: 'Balanced Performance Mode with Cache', performanceMode: 'balanced', useCache: true },
    { name: 'Low Performance Mode with Cache', performanceMode: 'low', useCache: true }
  ];
  
  // Results table
  const results = [];
  
  // Run tests for each scenario
  for (const scenario of scenarios) {
    console.log(`\nScenario: ${scenario.name}`);
    
    // First run - generate data
    console.log('First run (data generation):');
    const startTime1 = performance.now();
    const result1 = visualizer.generateMorphologicalResonanceField({
      performanceMode: scenario.performanceMode,
      useCache: scenario.useCache
    });
    const duration1 = performance.now() - startTime1;
    
    console.log(`  Resolution: ${result1.metadata.resolution}`);
    console.log(`  Data Points: ${result1.metadata.dataPoints}`);
    console.log(`  Generation Time: ${result1.metadata.generationTime.toFixed(2)}ms`);
    console.log(`  Total Time: ${duration1.toFixed(2)}ms`);
    
    // Second run - should use cache if enabled
    console.log('Second run (potential cache use):');
    const startTime2 = performance.now();
    const result2 = visualizer.generateMorphologicalResonanceField({
      performanceMode: scenario.performanceMode,
      useCache: scenario.useCache
    });
    const duration2 = performance.now() - startTime2;
    
    console.log(`  Resolution: ${result2.metadata.resolution}`);
    console.log(`  Data Points: ${result2.metadata.dataPoints}`);
    console.log(`  Generation Time: ${result2.metadata.generationTime.toFixed(2)}ms`);
    console.log(`  Total Time: ${duration2.toFixed(2)}ms`);
    
    // Calculate cache speedup
    const cacheSpeedup = duration1 > 0 ? duration1 / duration2 : 1;
    
    // Add to results table
    results.push({
      scenario: scenario.name,
      performanceMode: scenario.performanceMode,
      useCache: scenario.useCache,
      resolution: result1.metadata.resolution,
      dataPoints: result1.metadata.dataPoints,
      firstRunTime: duration1,
      secondRunTime: duration2,
      cacheSpeedup: scenario.useCache ? cacheSpeedup : 1
    });
  }
  
  // Calculate performance comparisons
  const lowPerf = results.find(r => r.performanceMode === 'low' && !r.useCache);
  const balancedPerf = results.find(r => r.performanceMode === 'balanced' && !r.useCache);
  const highPerf = results.find(r => r.performanceMode === 'high' && !r.useCache);
  
  const balancedSpeedup = lowPerf && balancedPerf ? lowPerf.firstRunTime / balancedPerf.firstRunTime : 0;
  const highSpeedup = lowPerf && highPerf ? lowPerf.firstRunTime / highPerf.firstRunTime : 0;
  
  // Print performance comparison
  console.log('\n=== Performance Mode Comparison ===');
  console.log(`Balanced Mode Speedup: ${balancedSpeedup.toFixed(2)}x faster than Low Mode`);
  console.log(`High Mode Speedup: ${highSpeedup.toFixed(2)}x faster than Low Mode`);
  
  // Print cache comparison
  console.log('\n=== Cache Comparison ===');
  for (const mode of ['high', 'balanced', 'low']) {
    const uncached = results.find(r => r.performanceMode === mode && !r.useCache);
    const cached = results.find(r => r.performanceMode === mode && r.useCache);
    
    if (uncached && cached) {
      console.log(`${mode.charAt(0).toUpperCase() + mode.slice(1)} Mode Cache Speedup: ${cached.cacheSpeedup.toFixed(2)}x faster on second run`);
    }
  }
  
  // Save results to file
  const resultFile = path.join(RESULTS_DIR, `visualization_performance_${new Date().toISOString().replace(/:/g, '-')}.json`);
  fs.writeFileSync(resultFile, JSON.stringify({
    scenarios: results,
    comparisons: {
      balancedSpeedup,
      highSpeedup
    }
  }, null, 2));
  
  console.log(`\nResults saved to ${resultFile}`);
  
  return results;
}

/**
 * Test resolution impact on performance
 */
function testResolutionImpact() {
  console.log('\n=== Testing Resolution Impact on Performance ===');
  
  // Test different resolutions
  const resolutions = [10, 20, 35, 50, 75, 100];
  const results = [];
  
  for (const resolution of resolutions) {
    console.log(`\nTesting resolution: ${resolution}`);
    
    // Initialize visualization generator with specific resolution
    const visualizer = new ComphyologyVisualization({ 
      enableLogging: false,
      resolution
    });
    
    // Generate visualization
    const startTime = performance.now();
    const result = visualizer.generateMorphologicalResonanceField({
      useCache: false,
      adaptiveResolution: false
    });
    const duration = performance.now() - startTime;
    
    console.log(`  Data Points: ${result.metadata.dataPoints}`);
    console.log(`  Generation Time: ${result.metadata.generationTime.toFixed(2)}ms`);
    console.log(`  Total Time: ${duration.toFixed(2)}ms`);
    
    // Add to results
    results.push({
      resolution,
      dataPoints: result.metadata.dataPoints,
      generationTime: result.metadata.generationTime,
      totalTime: duration
    });
  }
  
  // Calculate scaling factor
  const baseResult = results[0];
  for (const result of results) {
    result.scalingFactor = result.totalTime / baseResult.totalTime;
    result.theoreticalScaling = (result.resolution * result.resolution) / (baseResult.resolution * baseResult.resolution);
  }
  
  // Print scaling comparison
  console.log('\n=== Resolution Scaling ===');
  console.log('Resolution | Data Points | Time (ms) | Scaling Factor | Theoretical Scaling');
  console.log('-----------------------------------------------------------------------');
  for (const result of results) {
    console.log(`${result.resolution.toString().padStart(10)} | ${result.dataPoints.toString().padStart(11)} | ${result.totalTime.toFixed(2).padStart(9)} | ${result.scalingFactor.toFixed(2).padStart(14)} | ${result.theoreticalScaling.toFixed(2).padStart(19)}`);
  }
  
  // Save results to file
  const resultFile = path.join(RESULTS_DIR, `resolution_impact_${new Date().toISOString().replace(/:/g, '-')}.json`);
  fs.writeFileSync(resultFile, JSON.stringify(results, null, 2));
  
  console.log(`\nResults saved to ${resultFile}`);
  
  return results;
}

/**
 * Main test function
 */
function main() {
  console.log('=== Comphyology Visualization Performance Test ===');
  
  // Run performance tests
  const performanceResults = testMorphologicalResonancePerformance();
  const resolutionResults = testResolutionImpact();
  
  // Print summary
  console.log('\n=== Performance Test Summary ===');
  console.log(`Performance Modes Tested: ${performanceResults.length / 2}`);
  console.log(`Resolutions Tested: ${resolutionResults.length}`);
  
  // Calculate overall recommendations
  const highPerfCached = performanceResults.find(r => r.performanceMode === 'high' && r.useCache);
  const balancedPerfCached = performanceResults.find(r => r.performanceMode === 'balanced' && r.useCache);
  
  console.log('\n=== Recommendations ===');
  console.log('For interactive visualizations:');
  console.log(`  - Use ${highPerfCached.cacheSpeedup > balancedPerfCached.cacheSpeedup ? 'High' : 'Balanced'} Performance Mode with Caching`);
  console.log('For detailed analysis:');
  console.log('  - Use Balanced Performance Mode with Caching');
  console.log('For maximum detail:');
  console.log('  - Use Low Performance Mode with Caching');
  
  console.log('\nCONCLUSION: Performance optimizations provide significant speedup while maintaining visualization quality.');
}

// Run the tests
main();
