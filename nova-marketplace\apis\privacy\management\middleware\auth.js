/**
 * Authentication Middleware
 *
 * This middleware verifies JWT tokens for protected routes.
 * It validates the token signature and checks for expiration.
 */

const { verifyToken } = require('../services/authService');

/**
 * Authenticate a request using JWT
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 * @returns {void}
 */
const authenticate = async (req, res, next) => {
  try {
    // Get the authorization header
    const authHeader = req.headers.authorization;

    // Check if the authorization header exists and has the correct format
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication required'
      });
    }

    // Extract the token
    const token = authHeader.split(' ')[1];

    if (!token) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication token is required'
      });
    }

    // Verify the token
    const decoded = await verifyToken(token);

    // Attach the user information to the request object
    req.user = {
      id: decoded.sub,
      username: decoded.username,
      role: decoded.role
    };

    // Continue to the next middleware or route handler
    next();
  } catch (error) {
    if (error.name === 'AuthenticationError' || error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
      return res.status(401).json({
        error: 'Unauthorized',
        message: error.message || 'Invalid token'
      });
    }

    // For other errors, pass to the error handler
    next(error);
  }
};

/**
 * Authorize a request based on user role
 * @param {string[]} roles - Allowed roles
 * @returns {Function} Express middleware
 */
const authorize = (roles = []) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication required'
      });
    }

    if (roles.length && !roles.includes(req.user.role)) {
      return res.status(403).json({
        error: 'Forbidden',
        message: 'Insufficient permissions'
      });
    }

    next();
  };
};

module.exports = {
  authenticate,
  authorize
};
