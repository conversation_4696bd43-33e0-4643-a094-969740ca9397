import React, { useEffect, useRef, useState } from 'react';
import { Box, Typography, CircularProgress, Paper, Slider, FormControlLabel, Switch, Grid } from '@mui/material';
import * as d3 from 'd3';
import withEnhancements from './utils/withEnhancements';

/**
 * Harmony Index Visualization
 * 
 * This component visualizes the harmony between GRC, IT, and Cybersecurity domains
 * using a harmony index that shows alignment over time.
 */
const HarmonyIndexVisualization = ({
  data,
  width = '100%',
  height = 500,
  onInteraction,
  layoutOptions = {},
  interactionOptions = {},
  detailLevel = 'high',
  animationLevel = 'full',
  interactionMode = 'mouse'
}) => {
  // Reference to the SVG containers
  const mainChartRef = useRef(null);
  const domainChartRef = useRef(null);
  const crossDomainChartRef = useRef(null);
  
  // State for visualization options
  const [options, setOptions] = useState({
    showDomainScores: layoutOptions.showDomainScores !== false,
    showCrossDomainHarmony: layoutOptions.showCrossDomainHarmony !== false,
    showResonanceFactors: layoutOptions.showResonanceFactors !== false,
    timeRange: 'all',
    animateTransitions: animationLevel !== 'none'
  });
  
  // State for loading
  const [loading, setLoading] = useState(true);
  
  // State for error
  const [error, setError] = useState(null);
  
  // Effect to initialize and update main harmony chart
  useEffect(() => {
    if (!data || !mainChartRef.current) return;
    
    try {
      setLoading(true);
      setError(null);
      
      // Clear previous visualization
      d3.select(mainChartRef.current).selectAll('*').remove();
      
      // Create SVG container
      const svg = d3.select(mainChartRef.current)
        .attr('width', '100%')
        .attr('height', 250)
        .attr('viewBox', `0 0 800 250`)
        .attr('preserveAspectRatio', 'xMidYMid meet');
      
      // Set up margins
      const margin = { top: 20, right: 30, bottom: 40, left: 50 };
      const width = 800 - margin.left - margin.right;
      const height = 250 - margin.top - margin.bottom;
      
      // Create chart group
      const chart = svg.append('g')
        .attr('transform', `translate(${margin.left}, ${margin.top})`);
      
      // Parse dates
      const parseDate = d3.timeParse('%Y-%m-%d');
      const harmonyData = data.harmonyHistory.map(d => ({
        date: parseDate(d.date),
        value: d.value
      }));
      
      // Set up scales
      const x = d3.scaleTime()
        .domain(d3.extent(harmonyData, d => d.date))
        .range([0, width]);
      
      const y = d3.scaleLinear()
        .domain([0, 1])
        .range([height, 0]);
      
      // Create axes
      const xAxis = chart.append('g')
        .attr('transform', `translate(0, ${height})`)
        .call(d3.axisBottom(x));
      
      const yAxis = chart.append('g')
        .call(d3.axisLeft(y).ticks(5).tickFormat(d => `${d * 100}%`));
      
      // Add axis labels
      chart.append('text')
        .attr('x', width / 2)
        .attr('y', height + margin.bottom - 5)
        .attr('text-anchor', 'middle')
        .text('Time');
      
      chart.append('text')
        .attr('transform', 'rotate(-90)')
        .attr('x', -height / 2)
        .attr('y', -margin.left + 15)
        .attr('text-anchor', 'middle')
        .text('Harmony Index');
      
      // Create line generator
      const line = d3.line()
        .x(d => x(d.date))
        .y(d => y(d.value))
        .curve(d3.curveMonotoneX);
      
      // Add harmony line
      const path = chart.append('path')
        .datum(harmonyData)
        .attr('fill', 'none')
        .attr('stroke', '#3366cc')
        .attr('stroke-width', 2)
        .attr('d', line);
      
      // Add data points
      const points = chart.selectAll('.data-point')
        .data(harmonyData)
        .enter()
        .append('circle')
        .attr('class', 'data-point')
        .attr('cx', d => x(d.date))
        .attr('cy', d => y(d.value))
        .attr('r', 5)
        .attr('fill', '#3366cc')
        .on('mouseover', function(event, d) {
          d3.select(this)
            .attr('r', 8)
            .attr('fill', '#e6b422');
          
          // Show tooltip
          tooltip
            .style('opacity', 1)
            .html(`
              <strong>Date:</strong> ${d.date.toLocaleDateString()}<br>
              <strong>Harmony:</strong> ${Math.round(d.value * 100)}%
            `)
            .style('left', (event.pageX + 10) + 'px')
            .style('top', (event.pageY - 20) + 'px');
          
          // Track interaction
          if (onInteraction) {
            onInteraction('hover', {
              type: 'harmonyPoint',
              date: d.date,
              value: d.value
            });
          }
        })
        .on('mouseout', function() {
          d3.select(this)
            .attr('r', 5)
            .attr('fill', '#3366cc');
          
          // Hide tooltip
          tooltip.style('opacity', 0);
        })
        .on('click', function(event, d) {
          // Track interaction
          if (onInteraction) {
            onInteraction('click', {
              type: 'harmonyPoint',
              date: d.date,
              value: d.value
            });
          }
        });
      
      // Add current harmony value
      const currentHarmony = harmonyData[harmonyData.length - 1].value;
      
      chart.append('text')
        .attr('x', width)
        .attr('y', y(currentHarmony))
        .attr('text-anchor', 'end')
        .attr('dy', -10)
        .attr('font-size', '14px')
        .attr('font-weight', 'bold')
        .text(`Current: ${Math.round(currentHarmony * 100)}%`);
      
      // Create tooltip
      const tooltip = d3.select('body')
        .append('div')
        .attr('class', 'visualization-tooltip')
        .style('position', 'absolute')
        .style('background', 'rgba(0, 0, 0, 0.8)')
        .style('color', '#fff')
        .style('padding', '8px')
        .style('border-radius', '4px')
        .style('font-size', '12px')
        .style('pointer-events', 'none')
        .style('opacity', 0)
        .style('z-index', 1000);
      
      // Clean up tooltip on unmount
      return () => {
        tooltip.remove();
      };
    } catch (error) {
      console.error('Error rendering Harmony Index visualization:', error);
      setError('Failed to render harmony chart. Please try again.');
    } finally {
      setLoading(false);
    }
  }, [data, onInteraction]);
  
  // Effect to initialize and update domain scores chart
  useEffect(() => {
    if (!data || !domainChartRef.current || !options.showDomainScores) return;
    
    try {
      // Clear previous visualization
      d3.select(domainChartRef.current).selectAll('*').remove();
      
      // Create SVG container
      const svg = d3.select(domainChartRef.current)
        .attr('width', '100%')
        .attr('height', 150)
        .attr('viewBox', `0 0 400 150`)
        .attr('preserveAspectRatio', 'xMidYMid meet');
      
      // Set up margins
      const margin = { top: 20, right: 30, bottom: 30, left: 40 };
      const width = 400 - margin.left - margin.right;
      const height = 150 - margin.top - margin.bottom;
      
      // Create chart group
      const chart = svg.append('g')
        .attr('transform', `translate(${margin.left}, ${margin.top})`);
      
      // Prepare data
      const domainData = Object.entries(data.domainData).map(([domain, data]) => ({
        domain,
        score: data.score,
        trend: data.trend
      }));
      
      // Set up scales
      const x = d3.scaleBand()
        .domain(domainData.map(d => d.domain))
        .range([0, width])
        .padding(0.3);
      
      const y = d3.scaleLinear()
        .domain([0, 1])
        .range([height, 0]);
      
      // Create axes
      chart.append('g')
        .attr('transform', `translate(0, ${height})`)
        .call(d3.axisBottom(x));
      
      chart.append('g')
        .call(d3.axisLeft(y).ticks(5).tickFormat(d => `${d * 100}%`));
      
      // Add bars
      const bars = chart.selectAll('.bar')
        .data(domainData)
        .enter()
        .append('rect')
        .attr('class', 'bar')
        .attr('x', d => x(d.domain))
        .attr('y', d => y(d.score))
        .attr('width', x.bandwidth())
        .attr('height', d => height - y(d.score))
        .attr('fill', d => {
          if (d.domain === 'grc') return '#3366cc';
          if (d.domain === 'it') return '#33cc33';
          if (d.domain === 'cybersecurity') return '#cc3333';
          return '#999';
        })
        .on('mouseover', function(event, d) {
          d3.select(this)
            .attr('opacity', 0.8);
          
          // Track interaction
          if (onInteraction) {
            onInteraction('hover', {
              type: 'domainScore',
              domain: d.domain,
              score: d.score,
              trend: d.trend
            });
          }
        })
        .on('mouseout', function() {
          d3.select(this)
            .attr('opacity', 1);
        })
        .on('click', function(event, d) {
          // Track interaction
          if (onInteraction) {
            onInteraction('click', {
              type: 'domainScore',
              domain: d.domain,
              score: d.score,
              trend: d.trend
            });
          }
        });
      
      // Add score labels
      chart.selectAll('.score-label')
        .data(domainData)
        .enter()
        .append('text')
        .attr('class', 'score-label')
        .attr('x', d => x(d.domain) + x.bandwidth() / 2)
        .attr('y', d => y(d.score) - 5)
        .attr('text-anchor', 'middle')
        .attr('font-size', '12px')
        .text(d => `${Math.round(d.score * 100)}%`);
      
      // Add trend indicators
      chart.selectAll('.trend-indicator')
        .data(domainData)
        .enter()
        .append('text')
        .attr('class', 'trend-indicator')
        .attr('x', d => x(d.domain) + x.bandwidth() / 2)
        .attr('y', d => y(0) + 20)
        .attr('text-anchor', 'middle')
        .attr('font-size', '14px')
        .text(d => {
          if (d.trend === 'increasing') return '↑';
          if (d.trend === 'decreasing') return '↓';
          return '→';
        })
        .attr('fill', d => {
          if (d.trend === 'increasing') return '#33cc33';
          if (d.trend === 'decreasing') return '#cc3333';
          return '#999';
        });
    } catch (error) {
      console.error('Error rendering domain scores chart:', error);
    }
  }, [data, options.showDomainScores, onInteraction]);
  
  // Effect to initialize and update cross-domain harmony chart
  useEffect(() => {
    if (!data || !crossDomainChartRef.current || !options.showCrossDomainHarmony) return;
    
    try {
      // Clear previous visualization
      d3.select(crossDomainChartRef.current).selectAll('*').remove();
      
      // Create SVG container
      const svg = d3.select(crossDomainChartRef.current)
        .attr('width', '100%')
        .attr('height', 150)
        .attr('viewBox', `0 0 400 150`)
        .attr('preserveAspectRatio', 'xMidYMid meet');
      
      // Set up margins
      const margin = { top: 20, right: 30, bottom: 30, left: 40 };
      const width = 400 - margin.left - margin.right;
      const height = 150 - margin.top - margin.bottom;
      
      // Create chart group
      const chart = svg.append('g')
        .attr('transform', `translate(${margin.left}, ${margin.top})`);
      
      // Prepare data
      const crossDomainData = data.crossDomainHarmony;
      
      // Set up scales
      const x = d3.scaleBand()
        .domain(crossDomainData.map(d => `${d.source}-${d.target}`))
        .range([0, width])
        .padding(0.3);
      
      const y = d3.scaleLinear()
        .domain([0, 1])
        .range([height, 0]);
      
      // Create axes
      chart.append('g')
        .attr('transform', `translate(0, ${height})`)
        .call(d3.axisBottom(x))
        .selectAll('text')
        .attr('transform', 'rotate(-45)')
        .attr('text-anchor', 'end')
        .attr('dx', '-.8em')
        .attr('dy', '.15em');
      
      chart.append('g')
        .call(d3.axisLeft(y).ticks(5).tickFormat(d => `${d * 100}%`));
      
      // Add bars
      const bars = chart.selectAll('.bar')
        .data(crossDomainData)
        .enter()
        .append('rect')
        .attr('class', 'bar')
        .attr('x', d => x(`${d.source}-${d.target}`))
        .attr('y', d => y(d.value))
        .attr('width', x.bandwidth())
        .attr('height', d => height - y(d.value))
        .attr('fill', d => {
          // Blend colors of source and target domains
          const sourceColor = d.source === 'grc' ? '#3366cc' : d.source === 'it' ? '#33cc33' : '#cc3333';
          const targetColor = d.target === 'grc' ? '#3366cc' : d.target === 'it' ? '#33cc33' : '#cc3333';
          return d3.interpolateRgb(sourceColor, targetColor)(0.5);
        })
        .on('mouseover', function(event, d) {
          d3.select(this)
            .attr('opacity', 0.8);
          
          // Track interaction
          if (onInteraction) {
            onInteraction('hover', {
              type: 'crossDomainHarmony',
              source: d.source,
              target: d.target,
              value: d.value
            });
          }
        })
        .on('mouseout', function() {
          d3.select(this)
            .attr('opacity', 1);
        })
        .on('click', function(event, d) {
          // Track interaction
          if (onInteraction) {
            onInteraction('click', {
              type: 'crossDomainHarmony',
              source: d.source,
              target: d.target,
              value: d.value
            });
          }
        });
      
      // Add value labels
      chart.selectAll('.value-label')
        .data(crossDomainData)
        .enter()
        .append('text')
        .attr('class', 'value-label')
        .attr('x', d => x(`${d.source}-${d.target}`) + x.bandwidth() / 2)
        .attr('y', d => y(d.value) - 5)
        .attr('text-anchor', 'middle')
        .attr('font-size', '10px')
        .text(d => `${Math.round(d.value * 100)}%`);
    } catch (error) {
      console.error('Error rendering cross-domain harmony chart:', error);
    }
  }, [data, options.showCrossDomainHarmony, onInteraction]);
  
  // Handle toggle change
  const handleToggleChange = (name) => (event) => {
    setOptions(prev => ({ ...prev, [name]: event.target.checked }));
    
    // Track interaction
    if (onInteraction) {
      onInteraction('toggle', { option: name, value: event.target.checked });
    }
  };
  
  return (
    <Box sx={{ width, height, position: 'relative' }}>
      {loading && (
        <Box sx={{ 
          position: 'absolute', 
          top: 0, 
          left: 0, 
          right: 0, 
          bottom: 0, 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center',
          backgroundColor: 'rgba(255, 255, 255, 0.7)',
          zIndex: 1
        }}>
          <CircularProgress />
        </Box>
      )}
      
      {error && (
        <Box sx={{ 
          position: 'absolute', 
          top: 0, 
          left: 0, 
          right: 0, 
          bottom: 0, 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center',
          backgroundColor: 'rgba(255, 255, 255, 0.7)',
          zIndex: 1
        }}>
          <Typography color="error">{error}</Typography>
        </Box>
      )}
      
      <Box sx={{ mb: 2 }}>
        <Typography variant="h6" gutterBottom>Harmony Index Over Time</Typography>
        <svg ref={mainChartRef} />
      </Box>
      
      <Grid container spacing={2}>
        {options.showDomainScores && (
          <Grid item xs={12} md={6}>
            <Typography variant="subtitle1" gutterBottom>Domain Scores</Typography>
            <svg ref={domainChartRef} />
          </Grid>
        )}
        
        {options.showCrossDomainHarmony && (
          <Grid item xs={12} md={6}>
            <Typography variant="subtitle1" gutterBottom>Cross-Domain Harmony</Typography>
            <svg ref={crossDomainChartRef} />
          </Grid>
        )}
      </Grid>
      
      {options.showResonanceFactors && data.resonanceFactors && (
        <Box sx={{ mt: 2 }}>
          <Typography variant="subtitle1" gutterBottom>Resonance Factors</Typography>
          <Grid container spacing={2}>
            {Object.entries(data.resonanceFactors).map(([factor, value]) => (
              <Grid item xs={12} sm={4} key={factor}>
                <Paper sx={{ p: 2, textAlign: 'center' }}>
                  <Typography variant="body2" color="text.secondary">
                    {factor.charAt(0).toUpperCase() + factor.slice(1)}
                  </Typography>
                  <Typography variant="h4">
                    {Math.round(value * 100)}%
                  </Typography>
                </Paper>
              </Grid>
            ))}
          </Grid>
        </Box>
      )}
      
      <Paper sx={{ p: 2, mt: 2 }}>
        <Typography variant="subtitle1" gutterBottom>Visualization Controls</Typography>
        
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
          <FormControlLabel
            control={
              <Switch
                checked={options.showDomainScores}
                onChange={handleToggleChange('showDomainScores')}
              />
            }
            label="Show Domain Scores"
          />
          
          <FormControlLabel
            control={
              <Switch
                checked={options.showCrossDomainHarmony}
                onChange={handleToggleChange('showCrossDomainHarmony')}
              />
            }
            label="Show Cross-Domain Harmony"
          />
          
          <FormControlLabel
            control={
              <Switch
                checked={options.showResonanceFactors}
                onChange={handleToggleChange('showResonanceFactors')}
              />
            }
            label="Show Resonance Factors"
          />
        </Box>
      </Paper>
    </Box>
  );
};

// Export the enhanced component
export default withEnhancements(HarmonyIndexVisualization, {
  visualizationType: 'harmonyIndex'
});
