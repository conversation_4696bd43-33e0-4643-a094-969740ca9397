import axios from 'axios';

// Create an axios instance
const api = axios.create({
  baseURL: '/api', // This will be proxied to http://localhost:5000/api/v1 by Next.js
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add a request interceptor to add the auth token to requests
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add a response interceptor to handle token refresh
api.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    // If the error is due to an expired token and we haven't already tried to refresh
    if (error.response.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        // Try to refresh the token
        const refreshToken = localStorage.getItem('refresh_token');
        if (!refreshToken) {
          // No refresh token, redirect to login
          window.location.href = '/login';
          return Promise.reject(error);
        }

        const response = await axios.post('/api/auth/refresh', {}, {
          headers: {
            Authorization: `Bearer ${refreshToken}`,
          },
        });

        const { access_token } = response.data;

        // Save the new token
        localStorage.setItem('access_token', access_token);

        // Retry the original request with the new token
        originalRequest.headers.Authorization = `Bearer ${access_token}`;
        return axios(originalRequest);
      } catch (refreshError) {
        // If refresh fails, redirect to login
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
        window.location.href = '/login';
        return Promise.reject(refreshError);
      }
    }

    return Promise.reject(error);
  }
);

// Authentication
export const login = async (username: string, password: string) => {
  const response = await api.post('/auth/login', { username, password });
  const { access_token, refresh_token, user } = response.data;

  // Save tokens to localStorage
  localStorage.setItem('access_token', access_token);
  localStorage.setItem('refresh_token', refresh_token);
  localStorage.setItem('user', JSON.stringify(user));

  return user;
};

export const logout = () => {
  localStorage.removeItem('access_token');
  localStorage.removeItem('refresh_token');
  localStorage.removeItem('user');

  // Redirect to login page
  window.location.href = '/login';
};

export const getUser = () => {
  const userStr = localStorage.getItem('user');
  if (userStr) {
    return JSON.parse(userStr);
  }
  return null;
};

// Evidence
export const getEvidence = async (page = 1, pageSize = 10, query = {}) => {
  const response = await api.post('/search/evidence', {
    query,
    page,
    page_size: pageSize,
  });
  return response.data;
};

export const getEvidenceById = async (id: string) => {
  const response = await api.get(`/evidence/${id}`);
  return response.data;
};

export const createEvidence = async (evidence: any) => {
  const response = await api.post('/evidence', evidence);
  return response.data;
};

export const updateEvidence = async (id: string, evidence: any) => {
  const response = await api.put(`/evidence/${id}`, evidence);
  return response.data;
};

export const deleteEvidence = async (id: string, storageId = 'file_system') => {
  const response = await api.delete(`/evidence/${id}?storage_id=${storageId}`);
  return response.data;
};

export const validateEvidence = async (id: string, validatorId: string) => {
  const response = await api.post(`/evidence/${id}/validate`, { validator_id: validatorId });
  return response.data;
};

export const storeEvidence = async (id: string, storageId: string, options = {}) => {
  const response = await api.post(`/evidence/${id}/store`, {
    storage_id: storageId,
    ...options,
  });
  return response.data;
};

export const retrieveEvidence = async (id: string, storageId = 'file_system', versionId?: string) => {
  let url = `/evidence/${id}/retrieve?storage_id=${storageId}`;
  if (versionId) {
    url += `&version_id=${versionId}`;
  }
  const response = await api.get(url);
  return response.data;
};

// Requirements
export const searchRequirements = async (requirements: any, query: any, page = 1, pageSize = 10) => {
  const response = await api.post('/search/requirements', {
    requirements,
    query,
    page,
    page_size: pageSize,
  });
  return response.data;
};

export const getRequirements = async (page = 1, pageSize = 10, query = {}) => {
  const response = await api.post('/search/requirements', {
    query,
    page,
    page_size: pageSize,
  });
  return response.data;
};

export const getRequirementById = async (id: string) => {
  const response = await api.get(`/requirements/${id}`);
  return response.data;
};

export const createRequirement = async (requirement: any) => {
  const response = await api.post('/requirements', requirement);
  return response.data;
};

export const updateRequirement = async (id: string, requirement: any) => {
  const response = await api.put(`/requirements/${id}`, requirement);
  return response.data;
};

export const deleteRequirement = async (id: string) => {
  const response = await api.delete(`/requirements/${id}`);
  return response.data;
};

export const linkEvidenceToRequirement = async (requirementId: string, evidenceId: string) => {
  const response = await api.post(`/requirements/${requirementId}/evidence/${evidenceId}`);
  return response.data;
};

export const unlinkEvidenceFromRequirement = async (requirementId: string, evidenceId: string) => {
  const response = await api.delete(`/requirements/${requirementId}/evidence/${evidenceId}`);
  return response.data;
};

// Reports
export const generateEvidenceReport = async (format = 'json', filters?: any) => {
  let url = `/reports/evidence?format=${format}`;
  if (filters) {
    url += `&query=${encodeURIComponent(JSON.stringify(filters))}`;
  }
  const response = await api.get(url);
  return response.data;
};

export const generateComplianceReport = async (requirements: any, format = 'json') => {
  const response = await api.post(`/reports/compliance?format=${format}`, {
    requirements,
  });
  return response.data;
};

export const getReports = async (page = 1, pageSize = 10) => {
  const response = await api.get(`/reports?page=${page}&page_size=${pageSize}`);
  return response.data;
};

export const getReportById = async (id: string) => {
  const response = await api.get(`/reports/${id}`);
  return response.data;
};

export const downloadReport = async (id: string, format = 'pdf') => {
  const response = await api.get(`/reports/${id}/download?format=${format}`, {
    responseType: 'blob',
  });
  return response.data;
};

export const scheduleReport = async (reportConfig: any) => {
  const response = await api.post('/reports/schedule', reportConfig);
  return response.data;
};

export const getScheduledReports = async () => {
  const response = await api.get('/reports/schedule');
  return response.data;
};

export const deleteScheduledReport = async (id: string) => {
  const response = await api.delete(`/reports/schedule/${id}`);
  return response.data;
};

// Notifications
export const getNotifications = async (limit = 100, type?: string) => {
  let url = `/notifications?limit=${limit}`;
  if (type) {
    url += `&type=${type}`;
  }
  const response = await api.get(url);
  return response.data;
};

// Schedules
export const getSchedules = async () => {
  const response = await api.get('/schedules');
  return response.data;
};

export const createSchedule = async (schedule: any) => {
  const response = await api.post('/schedules', schedule);
  return response.data;
};

export const deleteSchedule = async (id: string) => {
  const response = await api.delete(`/schedules/${id}`);
  return response.data;
};

// Settings
export const getSettings = async () => {
  const response = await api.get('/settings');
  return response.data;
};

export const updateSettings = async (settings: any) => {
  const response = await api.put('/settings', settings);
  return response.data;
};

export const getUserProfile = async () => {
  const response = await api.get('/users/profile');
  return response.data;
};

export const updateUserProfile = async (profile: any) => {
  const response = await api.put('/users/profile', profile);
  return response.data;
};

export const changePassword = async (currentPassword: string, newPassword: string) => {
  const response = await api.post('/users/change-password', {
    current_password: currentPassword,
    new_password: newPassword,
  });
  return response.data;
};

export const updateNotificationSettings = async (settings: any) => {
  const response = await api.put('/users/notification-settings', settings);
  return response.data;
};

// NovaCore API
export * from './novaCoreApi';

export default api;
