00ac7094c4047885b0588455cf4cbe75
/**
 * NovaConnect Encryption Service Security Tests
 * 
 * This test suite validates the security properties of the encryption service,
 * including FIPS 140-3 compliance, key management, and cryptographic operations.
 */

const {
  EncryptionService
} = require('../../src/security/encryption-service');
const crypto = require('crypto');
const {
  performance
} = require('perf_hooks');
describe('Encryption Service Security', () => {
  let encryptionService;
  let keyId;
  beforeAll(async () => {
    // Initialize encryption service
    encryptionService = new EncryptionService({
      algorithm: 'aes-256-gcm',
      keyLength: 32,
      ivLength: 16,
      saltLength: 32,
      iterations: 100000,
      digest: 'sha256'
    });

    // Generate a key for testing
    const keyInfo = await encryptionService.generateKey();
    keyId = keyInfo.keyId;
  });
  describe('Key Generation', () => {
    it('should generate cryptographically secure keys', async () => {
      // Generate multiple keys and check for uniqueness
      const keys = [];
      for (let i = 0; i < 10; i++) {
        const keyInfo = await encryptionService.generateKey();
        keys.push(keyInfo.keyId);
      }

      // Check that all keys are unique
      const uniqueKeys = new Set(keys);
      expect(uniqueKeys.size).toBe(keys.length);

      // Check that keys are stored securely in the cache
      for (const key of keys) {
        const keyInfo = encryptionService.keyCache.get(key);
        expect(keyInfo).toBeDefined();
        expect(keyInfo.key).toBeInstanceOf(Buffer);
        expect(keyInfo.key.length).toBe(32); // 256 bits
      }
    });
    it('should use secure random number generation', async () => {
      // Generate multiple keys and check for randomness
      const keyBuffers = [];
      for (let i = 0; i < 10; i++) {
        const keyInfo = await encryptionService.generateKey();
        const key = encryptionService.keyCache.get(keyInfo.keyId).key;
        keyBuffers.push(key);
      }

      // Check that keys are different
      for (let i = 0; i < keyBuffers.length; i++) {
        for (let j = i + 1; j < keyBuffers.length; j++) {
          expect(Buffer.compare(keyBuffers[i], keyBuffers[j])).not.toBe(0);
        }
      }

      // Check entropy of keys
      for (const key of keyBuffers) {
        const entropy = calculateEntropy(key);
        expect(entropy).toBeGreaterThan(7.5); // High entropy expected for random data
      }
    });
  });
  describe('Encryption and Decryption', () => {
    it('should encrypt and decrypt data correctly', async () => {
      const testData = 'This is sensitive data that needs to be encrypted';

      // Encrypt the data
      const encrypted = await encryptionService.encrypt(testData, keyId);

      // Check encrypted package structure
      expect(encrypted).toHaveProperty('keyId', keyId);
      expect(encrypted).toHaveProperty('iv');
      expect(encrypted).toHaveProperty('authTag');
      expect(encrypted).toHaveProperty('data');
      expect(encrypted).toHaveProperty('algorithm', 'aes-256-gcm');
      expect(encrypted).toHaveProperty('encryptedAt');

      // Decrypt the data
      const decrypted = await encryptionService.decrypt(encrypted);

      // Check that decrypted data matches original
      expect(decrypted.toString()).toBe(testData);
    });
    it('should encrypt and decrypt objects correctly', async () => {
      const testObject = {
        id: '12345',
        name: 'Test User',
        email: '<EMAIL>',
        ssn: '***********',
        creditCard: '4111-1111-1111-1111'
      };

      // Encrypt the object
      const encrypted = await encryptionService.encrypt(testObject, keyId);

      // Decrypt the object
      const decrypted = await encryptionService.decrypt(encrypted);

      // Parse the decrypted data
      const decryptedObject = JSON.parse(decrypted.toString());

      // Check that decrypted object matches original
      expect(decryptedObject).toEqual(testObject);
    });
    it('should fail to decrypt with wrong key', async () => {
      const testData = 'This is sensitive data that needs to be encrypted';

      // Encrypt the data
      const encrypted = await encryptionService.encrypt(testData, keyId);

      // Generate a new key
      const newKeyInfo = await encryptionService.generateKey();

      // Modify the encrypted package to use the wrong key
      const modifiedEncrypted = {
        ...encrypted,
        keyId: newKeyInfo.keyId
      };

      // Attempt to decrypt with wrong key
      await expect(encryptionService.decrypt(modifiedEncrypted)).rejects.toThrow();
    });
    it('should fail to decrypt with tampered data', async () => {
      const testData = 'This is sensitive data that needs to be encrypted';

      // Encrypt the data
      const encrypted = await encryptionService.encrypt(testData, keyId);

      // Tamper with the encrypted data
      const tamperedEncrypted = {
        ...encrypted,
        data: encrypted.data.substring(0, encrypted.data.length - 5) + 'XXXXX'
      };

      // Attempt to decrypt tampered data
      await expect(encryptionService.decrypt(tamperedEncrypted)).rejects.toThrow();
    });
    it('should fail to decrypt with tampered authentication tag', async () => {
      const testData = 'This is sensitive data that needs to be encrypted';

      // Encrypt the data
      const encrypted = await encryptionService.encrypt(testData, keyId);

      // Tamper with the authentication tag
      const tamperedEncrypted = {
        ...encrypted,
        authTag: 'AAAA' + encrypted.authTag.substring(4)
      };

      // Attempt to decrypt with tampered authentication tag
      await expect(encryptionService.decrypt(tamperedEncrypted)).rejects.toThrow();
    });
  });
  describe('Password-Based Encryption', () => {
    it('should encrypt and decrypt data with password', async () => {
      const testData = 'This is sensitive data that needs to be encrypted with a password';
      const password = 'StrongP@ssw0rd!';

      // Encrypt the data with password
      const encrypted = await encryptionService.encryptWithPassword(testData, password);

      // Check encrypted package structure
      expect(encrypted).toHaveProperty('salt');
      expect(encrypted).toHaveProperty('iv');
      expect(encrypted).toHaveProperty('authTag');
      expect(encrypted).toHaveProperty('data');
      expect(encrypted).toHaveProperty('algorithm', 'aes-256-gcm');
      expect(encrypted).toHaveProperty('iterations', 100000);
      expect(encrypted).toHaveProperty('keyLength', 32);
      expect(encrypted).toHaveProperty('digest', 'sha256');
      expect(encrypted).toHaveProperty('encryptedAt');

      // Decrypt the data with password
      const decrypted = await encryptionService.decryptWithPassword(encrypted, password);

      // Check that decrypted data matches original
      expect(decrypted.toString()).toBe(testData);
    });
    it('should fail to decrypt with wrong password', async () => {
      const testData = 'This is sensitive data that needs to be encrypted with a password';
      const password = 'StrongP@ssw0rd!';
      const wrongPassword = 'WrongP@ssw0rd!';

      // Encrypt the data with password
      const encrypted = await encryptionService.encryptWithPassword(testData, password);

      // Attempt to decrypt with wrong password
      await expect(encryptionService.decryptWithPassword(encrypted, wrongPassword)).rejects.toThrow();
    });
    it('should use secure key derivation', async () => {
      const password = 'StrongP@ssw0rd!';
      const salt = crypto.randomBytes(32);

      // Derive key with different iterations
      const startTime = performance.now();
      const key = await crypto.pbkdf2Sync(password, salt, 100000, 32, 'sha256');
      const endTime = performance.now();
      const duration = endTime - startTime;

      // Check that key derivation takes a reasonable amount of time
      // Should be slow enough to prevent brute force, but not too slow for normal use
      expect(duration).toBeGreaterThan(10); // At least 10ms

      // Check key properties
      expect(key).toBeInstanceOf(Buffer);
      expect(key.length).toBe(32); // 256 bits

      // Check entropy of derived key
      const entropy = calculateEntropy(key);
      expect(entropy).toBeGreaterThan(7.5); // High entropy expected
    });
  });
  describe('Key Rotation', () => {
    it('should rotate keys securely', async () => {
      // Get current keys
      const initialKeys = Array.from(encryptionService.keyCache.keys());

      // Rotate keys
      const newKeyInfo = await encryptionService.rotateKeys();

      // Check that a new key was generated
      expect(newKeyInfo).toHaveProperty('keyId');
      expect(initialKeys).not.toContain(newKeyInfo.keyId);

      // Check that old keys are marked as rotated
      for (const oldKeyId of initialKeys) {
        const keyInfo = encryptionService.keyCache.get(oldKeyId);
        expect(keyInfo).toHaveProperty('rotatedAt');
        expect(keyInfo).toHaveProperty('replacedBy', newKeyInfo.keyId);
      }

      // Encrypt data with the new key
      const testData = 'Data encrypted with the new key';
      const encrypted = await encryptionService.encrypt(testData, newKeyInfo.keyId);

      // Decrypt the data
      const decrypted = await encryptionService.decrypt(encrypted);

      // Check that decrypted data matches original
      expect(decrypted.toString()).toBe(testData);
    });
  });
  describe('Performance and Security Tradeoffs', () => {
    it('should have acceptable performance for encryption operations', async () => {
      const testData = 'This is sensitive data that needs to be encrypted';

      // Measure encryption performance
      const encryptionTimes = [];
      for (let i = 0; i < 100; i++) {
        const startTime = performance.now();
        await encryptionService.encrypt(testData, keyId);
        const endTime = performance.now();
        encryptionTimes.push(endTime - startTime);
      }

      // Calculate average encryption time
      const avgEncryptionTime = encryptionTimes.reduce((sum, time) => sum + time, 0) / encryptionTimes.length;

      // Check that encryption is reasonably fast
      expect(avgEncryptionTime).toBeLessThan(10); // Less than 10ms per operation

      // Measure decryption performance
      const encrypted = await encryptionService.encrypt(testData, keyId);
      const decryptionTimes = [];
      for (let i = 0; i < 100; i++) {
        const startTime = performance.now();
        await encryptionService.decrypt(encrypted);
        const endTime = performance.now();
        decryptionTimes.push(endTime - startTime);
      }

      // Calculate average decryption time
      const avgDecryptionTime = decryptionTimes.reduce((sum, time) => sum + time, 0) / decryptionTimes.length;

      // Check that decryption is reasonably fast
      expect(avgDecryptionTime).toBeLessThan(10); // Less than 10ms per operation

      console.log(`Average encryption time: ${avgEncryptionTime.toFixed(2)}ms`);
      console.log(`Average decryption time: ${avgDecryptionTime.toFixed(2)}ms`);
    });
    it('should handle large data efficiently', async () => {
      // Generate 1MB of random data
      const largeData = crypto.randomBytes(1024 * 1024);

      // Measure encryption time
      const encryptStartTime = performance.now();
      const encrypted = await encryptionService.encrypt(largeData, keyId);
      const encryptEndTime = performance.now();
      const encryptDuration = encryptEndTime - encryptStartTime;

      // Measure decryption time
      const decryptStartTime = performance.now();
      const decrypted = await encryptionService.decrypt(encrypted);
      const decryptEndTime = performance.now();
      const decryptDuration = decryptEndTime - decryptStartTime;

      // Check that decrypted data matches original
      expect(Buffer.compare(decrypted, largeData)).toBe(0);

      // Check performance
      console.log(`Encryption of 1MB: ${encryptDuration.toFixed(2)}ms`);
      console.log(`Decryption of 1MB: ${decryptDuration.toFixed(2)}ms`);

      // Performance should be reasonable for large data
      expect(encryptDuration).toBeLessThan(1000); // Less than 1 second
      expect(decryptDuration).toBeLessThan(1000); // Less than 1 second
    });
  });
});

/**
 * Calculate Shannon entropy of a buffer
 * @param {Buffer} buffer - Buffer to calculate entropy for
 * @returns {number} - Entropy value (bits per byte)
 */
function calculateEntropy(buffer) {
  const frequencies = new Array(256).fill(0);

  // Count byte frequencies
  for (const byte of buffer) {
    frequencies[byte]++;
  }

  // Calculate entropy
  let entropy = 0;
  for (const count of frequencies) {
    if (count > 0) {
      const probability = count / buffer.length;
      entropy -= probability * Math.log2(probability);
    }
  }
  return entropy;
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************