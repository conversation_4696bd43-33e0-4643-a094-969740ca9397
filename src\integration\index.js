/**
 * Unified Integration Module
 * 
 * This module exports all the components of the unified integration system.
 */

const UnifiedIntegrationModule = require('./unified-integration-module');
const ComponentRegistry = require('./component-registry');
const DataFlowManager = require('./data-flow-manager');
const TensorAdapter = require('./adapters/tensor-adapter');
const VisualizationAdapter = require('./adapters/visualization-adapter');
const AnalyticsAdapter = require('./adapters/analytics-adapter');

/**
 * Create a unified integration system
 * @param {Object} options - Configuration options
 * @returns {Object} - Unified integration system
 */
function createUnifiedIntegration(options = {}) {
  // Create component registry
  const registry = new ComponentRegistry({
    enableLogging: options.enableLogging
  });
  
  // Create data flow manager
  const dataFlowManager = new DataFlowManager({
    enableLogging: options.enableLogging,
    enableMetrics: options.enableMetrics,
    registry
  });
  
  // Create unified integration module
  const integration = new UnifiedIntegrationModule({
    enableLogging: options.enableLogging,
    enableMetrics: options.enableMetrics,
    updateInterval: options.updateInterval
  });
  
  // Create adapters
  const adapters = {
    tensor: options.tensor ? new TensorAdapter({
      enableLogging: options.enableLogging,
      autoConnect: options.autoConnect,
      tensor: options.tensor
    }) : null,
    
    visualization: options.visualization ? new VisualizationAdapter({
      enableLogging: options.enableLogging,
      autoConnect: options.autoConnect,
      visualization: options.visualization
    }) : null,
    
    analytics: options.analytics ? new AnalyticsAdapter({
      enableLogging: options.enableLogging,
      autoConnect: options.autoConnect,
      analytics: options.analytics
    }) : null
  };
  
  // Register adapters with registry if they exist
  if (adapters.tensor) {
    registry.register('tensor-adapter', adapters.tensor, {
      type: 'adapter',
      interfaces: ['connect', 'disconnect', 'registerTensor', 'getTensor', 'updateTensor', 'healTensor']
    });
  }
  
  if (adapters.visualization) {
    registry.register('visualization-adapter', adapters.visualization, {
      type: 'adapter',
      interfaces: ['connect', 'disconnect', 'getVisualizationTypes', 'createVisualization', 'updateVisualization', 'deleteVisualization']
    });
  }
  
  if (adapters.analytics) {
    registry.register('analytics-adapter', adapters.analytics, {
      type: 'adapter',
      interfaces: ['connect', 'disconnect', 'getMetrics', 'getDashboards', 'getDashboard', 'executeQuery']
    });
  }
  
  // Register core components with registry
  registry.register('integration-module', integration, {
    type: 'core',
    interfaces: ['registerComponent', 'getComponent', 'createConnection', 'createDataFlow', 'processDataFlow']
  });
  
  registry.register('data-flow-manager', dataFlowManager, {
    type: 'core',
    interfaces: ['createFlow', 'executeFlow', 'getFlow', 'getFlows', 'updateFlow', 'deleteFlow']
  });
  
  // Return the unified integration system
  return {
    integration,
    registry,
    dataFlowManager,
    adapters
  };
}

module.exports = {
  UnifiedIntegrationModule,
  ComponentRegistry,
  DataFlowManager,
  TensorAdapter,
  VisualizationAdapter,
  AnalyticsAdapter,
  createUnifiedIntegration
};
