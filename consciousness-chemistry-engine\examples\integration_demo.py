"""
Integration demo for ConsciousNovaFold with all components.
"""
import os
import sys
import json
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.ConsciousNovaFold import ConsciousNovaFold, NovaFoldClient
from src.evolutionary_analysis import EvolutionaryAnalyzer
from src.domain_analysis import DomainAnalyzer, FIBONACCI_SEQ
from src.visualization import ProteinVisualizer, create_consciousness_report

# Example protein sequences
PROTEINS = {
    "Lysozyme": "KVFGRCELAAAMKRHGLDNYRGYSLGNWVCAAKFESNFNTQATNRNTDGSTDYGILQINSRWWCNDGRTPGSRNLCNIPCSALLSSDITASVNCAKKIVSDGNGMNAWVAWRNRCKGTDVQAWIRGCRL",
    "TDP-43_NLS": "MSSPGSPYEPGSPGEPPSPDLKRALQKGLMNNKSEEMDLAALAAVDNSSSFLKQAGSVNGGLLFKDNKTKFYEEFMDVDPAKLSKQVMKRAPSRRSGGRGYYFGGGTQS"
}

def main():
    """Run the integration demo."""
    # Create output directories
    os.makedirs("output/structures", exist_ok=True)
    os.makedirs("output/reports", exist_ok=True)
    
    # Initialize components
    print("Initializing components...")
    novafold = NovaFoldClient()
    cn = ConsciousNovaFold(novafold)
    evo_analyzer = EvolutionaryAnalyzer()
    domain_analyzer = DomainAnalyzer()
    visualizer = ProteinVisualizer()
    
    results = {}
    
    # Process each protein
    for name, seq in PROTEINS.items():
        print(f"\nProcessing {name} (length: {len(seq)} aa)")
        
        # 1. Get structure prediction with consciousness enhancement
        print("  - Running ConsciousNovaFold...")
        result = cn.fold(seq)
        
        # 2. Run evolutionary analysis
        print("  - Running evolutionary analysis...")
        psi_scores = {i+1: score for i, score in enumerate(evo_analyzer.predict_functional_sites(seq).values())}
        
        # 3. Run domain analysis
        print("  - Running domain analysis...")
        domain_analysis = domain_analyzer.analyze_domain_fibonacci(seq)
        
        # 4. Generate visualization
        print("  - Generating visualizations...")
        pdb_file = f"output/structures/{name}.pdb"
        with open(pdb_file, 'w') as f:
            f.write(result.get('pdb', 'MOCK PDB CONTENT'))
            
        # Visualize with PyMOL if available
        try:
            obj_name = visualizer.color_by_psi_scores(pdb_file, psi_scores)
            visualizer.highlight_domains(
                obj_name, 
                domain_analysis.get('domains', []),
                color="gold"
            )
            visualizer.render_image(f"output/structures/{name}_consciousness.png")
        except Exception as e:
            print(f"    Warning: Could not generate 3D visualization: {e}")
        
        # 5. Generate HTML report
        print("  - Generating HTML report...")
        report_data = {
            'name': name,
            'sequence': seq,
            'consciousness_metrics': {
                'average_psi': result.get('consciousness_metrics', {}).get('average_psi', 0),
                'fibonacci_alignment': domain_analysis.get('protein_fibonacci', {}).get('alignment_score', 0),
                'trinity_validation': result.get('consciousness_metrics', {}).get('trinity_validation', {})
            },
            'domain_analysis': domain_analysis
        }
        
        report_path = create_consciousness_report(
            report_data, 
            output_dir="output/reports"
        )
        
        # Store results
        results[name] = {
            'sequence_length': len(seq),
            'average_psi': report_data['consciousness_metrics']['average_psi'],
            'fibonacci_score': report_data['consciousness_metrics']['fibonacci_alignment'],
            'report_path': report_path
        }
    
    # Print summary
    print("\n=== Integration Demo Complete ===")
    print("Results summary:")
    for name, res in results.items():
        print(f"\n{name}:")
        print(f"  Length: {res['sequence_length']} aa")
        print(f"  Avg Ψ-score: {res['average_psi']:.3f}")
        print(f"  Fibonacci alignment: {res['fibonacci_score']:.3f}")
        print(f"  Report: {res['report_path']}")

if __name__ == "__main__":
    main()
