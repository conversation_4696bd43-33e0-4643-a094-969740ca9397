{"version": 3, "names": ["RateLimiter", "constructor", "options", "windowMs", "maxRequests", "keyGenerator", "req", "ip", "requests", "Map", "cleanupInterval", "setInterval", "cleanup", "isAllowed", "key", "now", "Date", "record", "get", "count", "resetTime", "set", "getRemainingRequests", "Math", "max", "getResetTime", "entries", "delete", "stop", "clearInterval", "module", "exports"], "sources": ["rate-limiter.js"], "sourcesContent": ["/**\n * NovaFuse Universal API Connector Rate Limiter\n * \n * This module provides rate limiting functionality for the UAC.\n */\n\n/**\n * Rate Limiting Utility\n * \n * Provides methods for rate limiting API requests.\n */\nclass RateLimiter {\n  constructor(options = {}) {\n    this.options = {\n      windowMs: 60 * 1000, // 1 minute\n      maxRequests: 100, // 100 requests per minute\n      keyGenerator: (req) => req.ip || 'default',\n      ...options\n    };\n    \n    this.requests = new Map();\n    this.cleanupInterval = setInterval(() => this.cleanup(), this.options.windowMs);\n  }\n\n  /**\n   * Check if a request is allowed\n   * @param {Object} req - Request object\n   * @returns {boolean} - Whether the request is allowed\n   */\n  isAllowed(req) {\n    const key = this.options.keyGenerator(req);\n    const now = Date.now();\n    \n    // Get or create request record\n    let record = this.requests.get(key);\n    if (!record) {\n      record = {\n        count: 0,\n        resetTime: now + this.options.windowMs\n      };\n      this.requests.set(key, record);\n    }\n    \n    // Reset count if window has passed\n    if (now > record.resetTime) {\n      record.count = 0;\n      record.resetTime = now + this.options.windowMs;\n    }\n    \n    // Check if request is allowed\n    if (record.count >= this.options.maxRequests) {\n      return false;\n    }\n    \n    // Increment count\n    record.count++;\n    \n    return true;\n  }\n\n  /**\n   * Get remaining requests for a key\n   * @param {Object} req - Request object\n   * @returns {number} - Remaining requests\n   */\n  getRemainingRequests(req) {\n    const key = this.options.keyGenerator(req);\n    const now = Date.now();\n    \n    // Get request record\n    const record = this.requests.get(key);\n    if (!record) {\n      return this.options.maxRequests;\n    }\n    \n    // Reset count if window has passed\n    if (now > record.resetTime) {\n      return this.options.maxRequests;\n    }\n    \n    return Math.max(0, this.options.maxRequests - record.count);\n  }\n\n  /**\n   * Get reset time for a key\n   * @param {Object} req - Request object\n   * @returns {number} - Reset time in milliseconds\n   */\n  getResetTime(req) {\n    const key = this.options.keyGenerator(req);\n    \n    // Get request record\n    const record = this.requests.get(key);\n    if (!record) {\n      return Date.now() + this.options.windowMs;\n    }\n    \n    return record.resetTime;\n  }\n\n  /**\n   * Clean up expired records\n   */\n  cleanup() {\n    const now = Date.now();\n    \n    for (const [key, record] of this.requests.entries()) {\n      if (now > record.resetTime) {\n        this.requests.delete(key);\n      }\n    }\n  }\n\n  /**\n   * Stop the rate limiter and clear the cleanup interval\n   */\n  stop() {\n    if (this.cleanupInterval) {\n      clearInterval(this.cleanupInterval);\n      this.cleanupInterval = null;\n    }\n  }\n}\n\nmodule.exports = RateLimiter;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMA,WAAW,CAAC;EAChBC,WAAWA,CAACC,OAAO,GAAG,CAAC,CAAC,EAAE;IACxB,IAAI,CAACA,OAAO,GAAG;MACbC,QAAQ,EAAE,EAAE,GAAG,IAAI;MAAE;MACrBC,WAAW,EAAE,GAAG;MAAE;MAClBC,YAAY,EAAGC,GAAG,IAAKA,GAAG,CAACC,EAAE,IAAI,SAAS;MAC1C,GAAGL;IACL,CAAC;IAED,IAAI,CAACM,QAAQ,GAAG,IAAIC,GAAG,CAAC,CAAC;IACzB,IAAI,CAACC,eAAe,GAAGC,WAAW,CAAC,MAAM,IAAI,CAACC,OAAO,CAAC,CAAC,EAAE,IAAI,CAACV,OAAO,CAACC,QAAQ,CAAC;EACjF;;EAEA;AACF;AACA;AACA;AACA;EACEU,SAASA,CAACP,GAAG,EAAE;IACb,MAAMQ,GAAG,GAAG,IAAI,CAACZ,OAAO,CAACG,YAAY,CAACC,GAAG,CAAC;IAC1C,MAAMS,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;;IAEtB;IACA,IAAIE,MAAM,GAAG,IAAI,CAACT,QAAQ,CAACU,GAAG,CAACJ,GAAG,CAAC;IACnC,IAAI,CAACG,MAAM,EAAE;MACXA,MAAM,GAAG;QACPE,KAAK,EAAE,CAAC;QACRC,SAAS,EAAEL,GAAG,GAAG,IAAI,CAACb,OAAO,CAACC;MAChC,CAAC;MACD,IAAI,CAACK,QAAQ,CAACa,GAAG,CAACP,GAAG,EAAEG,MAAM,CAAC;IAChC;;IAEA;IACA,IAAIF,GAAG,GAAGE,MAAM,CAACG,SAAS,EAAE;MAC1BH,MAAM,CAACE,KAAK,GAAG,CAAC;MAChBF,MAAM,CAACG,SAAS,GAAGL,GAAG,GAAG,IAAI,CAACb,OAAO,CAACC,QAAQ;IAChD;;IAEA;IACA,IAAIc,MAAM,CAACE,KAAK,IAAI,IAAI,CAACjB,OAAO,CAACE,WAAW,EAAE;MAC5C,OAAO,KAAK;IACd;;IAEA;IACAa,MAAM,CAACE,KAAK,EAAE;IAEd,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;EACEG,oBAAoBA,CAAChB,GAAG,EAAE;IACxB,MAAMQ,GAAG,GAAG,IAAI,CAACZ,OAAO,CAACG,YAAY,CAACC,GAAG,CAAC;IAC1C,MAAMS,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;;IAEtB;IACA,MAAME,MAAM,GAAG,IAAI,CAACT,QAAQ,CAACU,GAAG,CAACJ,GAAG,CAAC;IACrC,IAAI,CAACG,MAAM,EAAE;MACX,OAAO,IAAI,CAACf,OAAO,CAACE,WAAW;IACjC;;IAEA;IACA,IAAIW,GAAG,GAAGE,MAAM,CAACG,SAAS,EAAE;MAC1B,OAAO,IAAI,CAAClB,OAAO,CAACE,WAAW;IACjC;IAEA,OAAOmB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAACtB,OAAO,CAACE,WAAW,GAAGa,MAAM,CAACE,KAAK,CAAC;EAC7D;;EAEA;AACF;AACA;AACA;AACA;EACEM,YAAYA,CAACnB,GAAG,EAAE;IAChB,MAAMQ,GAAG,GAAG,IAAI,CAACZ,OAAO,CAACG,YAAY,CAACC,GAAG,CAAC;;IAE1C;IACA,MAAMW,MAAM,GAAG,IAAI,CAACT,QAAQ,CAACU,GAAG,CAACJ,GAAG,CAAC;IACrC,IAAI,CAACG,MAAM,EAAE;MACX,OAAOD,IAAI,CAACD,GAAG,CAAC,CAAC,GAAG,IAAI,CAACb,OAAO,CAACC,QAAQ;IAC3C;IAEA,OAAOc,MAAM,CAACG,SAAS;EACzB;;EAEA;AACF;AACA;EACER,OAAOA,CAAA,EAAG;IACR,MAAMG,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;IAEtB,KAAK,MAAM,CAACD,GAAG,EAAEG,MAAM,CAAC,IAAI,IAAI,CAACT,QAAQ,CAACkB,OAAO,CAAC,CAAC,EAAE;MACnD,IAAIX,GAAG,GAAGE,MAAM,CAACG,SAAS,EAAE;QAC1B,IAAI,CAACZ,QAAQ,CAACmB,MAAM,CAACb,GAAG,CAAC;MAC3B;IACF;EACF;;EAEA;AACF;AACA;EACEc,IAAIA,CAAA,EAAG;IACL,IAAI,IAAI,CAAClB,eAAe,EAAE;MACxBmB,aAAa,CAAC,IAAI,CAACnB,eAAe,CAAC;MACnC,IAAI,CAACA,eAAe,GAAG,IAAI;IAC7B;EACF;AACF;AAEAoB,MAAM,CAACC,OAAO,GAAG/B,WAAW", "ignoreList": []}