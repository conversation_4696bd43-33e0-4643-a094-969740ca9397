/**
 * Regulatory Framework Model
 * 
 * This model defines the schema for regulatory frameworks in the Privacy Management API.
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const regulatoryFrameworkSchema = new Schema({
  name: { 
    type: String, 
    required: true, 
    trim: true 
  },
  code: { 
    type: String, 
    required: true, 
    unique: true, 
    trim: true 
  },
  version: { 
    type: String, 
    required: true, 
    trim: true 
  },
  description: { 
    type: String, 
    trim: true 
  },
  category: { 
    type: String, 
    enum: ['privacy', 'security', 'governance', 'industry', 'general'], 
    default: 'privacy' 
  },
  regions: [{ 
    type: String, 
    trim: true 
  }],
  countries: [{ 
    type: String, 
    trim: true 
  }],
  effectiveDate: { 
    type: Date 
  },
  lastUpdated: { 
    type: Date 
  },
  authority: { 
    type: String, 
    trim: true 
  },
  authorityUrl: { 
    type: String, 
    trim: true 
  },
  documentUrl: { 
    type: String, 
    trim: true 
  },
  status: { 
    type: String, 
    enum: ['active', 'inactive', 'draft', 'superseded', 'archived'], 
    default: 'active' 
  },
  supersededBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'RegulatoryFramework' 
  },
  relatedFrameworks: [{
    framework: { 
      type: Schema.Types.ObjectId, 
      ref: 'RegulatoryFramework' 
    },
    relationship: { 
      type: String, 
      enum: ['supersedes', 'implements', 'complements', 'conflicts'], 
      default: 'complements' 
    },
    notes: { 
      type: String 
    }
  }],
  metadata: { 
    type: Object 
  }
}, {
  timestamps: true
});

// Add text index for searching
regulatoryFrameworkSchema.index({ 
  name: 'text', 
  code: 'text', 
  description: 'text' 
});

// Add compound index for region and status
regulatoryFrameworkSchema.index({ 
  regions: 1, 
  status: 1 
});

// Add compound index for category and status
regulatoryFrameworkSchema.index({ 
  category: 1, 
  status: 1 
});

// Create model
const RegulatoryFramework = mongoose.model('RegulatoryFramework', regulatoryFrameworkSchema);

module.exports = RegulatoryFramework;
