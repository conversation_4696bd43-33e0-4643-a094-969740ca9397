/**
 * NovaFuse API Superstore Configuration
 * 
 * This file exports the configuration for the NovaFuse API Superstore.
 */

const encryptionConfig = require('./encryption');

// Load environment variables
const env = process.env.NODE_ENV || 'development';

// Base configuration
const config = {
  // Server configuration
  server: {
    port: process.env.PORT || 3000,
    host: process.env.HOST || 'localhost',
    baseUrl: process.env.BASE_URL || 'http://localhost:3000'
  },
  
  // Database configuration
  database: {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 27017,
    name: process.env.DB_NAME || 'novafuse',
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    url: process.env.DB_URL || 'mongodb://localhost:27017/novafuse'
  },
  
  // API configuration
  api: {
    version: process.env.API_VERSION || 'v1',
    prefix: process.env.API_PREFIX || '/api',
    rateLimitWindow: process.env.RATE_LIMIT_WINDOW || 15 * 60 * 1000, // 15 minutes
    rateLimitMax: process.env.RATE_LIMIT_MAX || 100, // 100 requests per window
    timeout: process.env.API_TIMEOUT || 30000 // 30 seconds
  },
  
  // Authentication configuration
  auth: {
    jwtExpiresIn: process.env.JWT_EXPIRES_IN || '1h',
    jwtRefreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d',
    passwordMinLength: process.env.PASSWORD_MIN_LENGTH || 12,
    passwordRequireUppercase: process.env.PASSWORD_REQUIRE_UPPERCASE !== 'false',
    passwordRequireLowercase: process.env.PASSWORD_REQUIRE_LOWERCASE !== 'false',
    passwordRequireNumbers: process.env.PASSWORD_REQUIRE_NUMBERS !== 'false',
    passwordRequireSymbols: process.env.PASSWORD_REQUIRE_SYMBOLS !== 'false',
    maxLoginAttempts: process.env.MAX_LOGIN_ATTEMPTS || 5,
    lockoutTime: process.env.LOCKOUT_TIME || 15 * 60 * 1000 // 15 minutes
  },
  
  // Encryption configuration
  encryption: encryptionConfig,
  
  // Logging configuration
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    format: process.env.LOG_FORMAT || 'json',
    filename: process.env.LOG_FILENAME || 'novafuse.log',
    maxSize: process.env.LOG_MAX_SIZE || '10m',
    maxFiles: process.env.LOG_MAX_FILES || 5,
    console: process.env.LOG_CONSOLE !== 'false'
  },
  
  // CORS configuration
  cors: {
    origin: process.env.CORS_ORIGIN || '*',
    methods: process.env.CORS_METHODS || 'GET,HEAD,PUT,PATCH,POST,DELETE',
    preflightContinue: process.env.CORS_PREFLIGHT_CONTINUE === 'true',
    optionsSuccessStatus: parseInt(process.env.CORS_OPTIONS_SUCCESS_STATUS || '204')
  },
  
  // Email configuration
  email: {
    host: process.env.EMAIL_HOST,
    port: process.env.EMAIL_PORT || 587,
    secure: process.env.EMAIL_SECURE === 'true',
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASS
    },
    from: process.env.EMAIL_FROM || '<EMAIL>'
  }
};

// Environment-specific configurations
const configurations = {
  development: {
    ...config,
    // Development-specific overrides
  },
  test: {
    ...config,
    // Test-specific overrides
    database: {
      ...config.database,
      name: 'novafuse_test'
    }
  },
  production: {
    ...config,
    // Production-specific overrides
    server: {
      ...config.server,
      host: process.env.HOST,
      baseUrl: process.env.BASE_URL
    },
    logging: {
      ...config.logging,
      level: 'info',
      console: false
    },
    cors: {
      ...config.cors,
      origin: process.env.CORS_ORIGIN
    }
  }
};

// Export the configuration for the current environment
module.exports = configurations[env];
