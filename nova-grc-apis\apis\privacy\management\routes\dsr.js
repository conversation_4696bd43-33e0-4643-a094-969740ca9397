/**
 * Data Subject Request Routes
 * 
 * Routes for managing data subject requests (DSRs).
 */

const express = require('express');
const { check } = require('express-validator');
const DataSubjectRequestController = require('../controllers/DataSubjectRequestController');
const auth = require('../../../middleware/auth');

const router = express.Router();

/**
 * @route   GET /api/privacy/management/dsr
 * @desc    Get all data subject requests
 * @access  Private
 */
router.get('/', auth, DataSubjectRequestController.getAllDataSubjectRequests);

/**
 * @route   GET /api/privacy/management/dsr/:id
 * @desc    Get a single data subject request
 * @access  Private
 */
router.get('/:id', auth, DataSubjectRequestController.getDataSubjectRequest);

/**
 * @route   POST /api/privacy/management/dsr
 * @desc    Create a new data subject request
 * @access  Private
 */
router.post('/', [
  auth,
  [
    check('requestType', 'Request type is required').not().isEmpty(),
    check('requestType', 'Invalid request type').isIn([
      'Access', 'Rectification', 'Erasure', 'Restriction', 'Portability', 
      'Objection', 'AutomatedDecisionMaking', 'Withdraw Consent', 'Do Not Sell', 'Other'
    ]),
    check('requestDetails', 'Request details are required').not().isEmpty(),
    check('dataSubject.name', 'Data subject name is required').not().isEmpty(),
    check('dataSubject.email', 'Data subject email is required').isEmail(),
    check('dataSubject.identificationMethod', 'Identification method is required').not().isEmpty(),
    check('regulatoryFramework', 'Regulatory framework is required').not().isEmpty()
  ]
], DataSubjectRequestController.createDataSubjectRequest);

/**
 * @route   PUT /api/privacy/management/dsr/:id
 * @desc    Update a data subject request
 * @access  Private
 */
router.put('/:id', [
  auth,
  [
    check('requestType', 'Invalid request type').optional().isIn([
      'Access', 'Rectification', 'Erasure', 'Restriction', 'Portability', 
      'Objection', 'AutomatedDecisionMaking', 'Withdraw Consent', 'Do Not Sell', 'Other'
    ]),
    check('status', 'Invalid status').optional().isIn([
      'New', 'Verification Pending', 'In Progress', 'Awaiting Approval', 
      'Completed', 'Denied', 'Withdrawn'
    ]),
    check('dataSubject.email', 'Invalid email').optional().isEmail()
  ]
], DataSubjectRequestController.updateDataSubjectRequest);

/**
 * @route   DELETE /api/privacy/management/dsr/:id
 * @desc    Delete a data subject request
 * @access  Private
 */
router.delete('/:id', auth, DataSubjectRequestController.deleteDataSubjectRequest);

/**
 * @route   POST /api/privacy/management/dsr/:id/timeline
 * @desc    Add a timeline entry to a data subject request
 * @access  Private
 */
router.post('/:id/timeline', [
  auth,
  [
    check('action', 'Action is required').not().isEmpty(),
    check('notes', 'Notes are required').not().isEmpty()
  ]
], DataSubjectRequestController.addTimelineEntry);

/**
 * @route   GET /api/privacy/management/dsr/:id/overdue
 * @desc    Check if a data subject request is overdue
 * @access  Private
 */
router.get('/:id/overdue', auth, DataSubjectRequestController.checkOverdue);

module.exports = router;
