/**
 * File Attachment Component
 * 
 * This component displays file attachments in the NovaAssistAI chat.
 * It supports different file types with appropriate previews.
 */

import React, { useState } from 'react';
import { Download, Eye, X, File, Image, FileText, Archive, Film, Music, Database } from 'lucide-react';

interface FileAttachmentProps {
  url: string;
  name: string;
  onRemove?: () => void;
  showRemove?: boolean;
}

const FileAttachment: React.FC<FileAttachmentProps> = ({
  url,
  name,
  onRemove,
  showRemove = false
}) => {
  const [previewOpen, setPreviewOpen] = useState(false);
  
  const getFileExtension = (): string => {
    return `.${name.split('.').pop()}`.toLowerCase();
  };
  
  const getFileType = (): string => {
    const extension = getFileExtension();
    
    if (['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg'].includes(extension)) {
      return 'image';
    } else if (extension === '.pdf') {
      return 'pdf';
    } else if (['.doc', '.docx'].includes(extension)) {
      return 'word';
    } else if (['.xls', '.xlsx', '.csv'].includes(extension)) {
      return 'excel';
    } else if (['.zip', '.rar', '.tar', '.gz'].includes(extension)) {
      return 'archive';
    } else if (['.mp4', '.webm', '.avi', '.mov'].includes(extension)) {
      return 'video';
    } else if (['.mp3', '.wav', '.ogg'].includes(extension)) {
      return 'audio';
    } else {
      return 'other';
    }
  };
  
  const getFileIcon = () => {
    const fileType = getFileType();
    
    switch (fileType) {
      case 'image':
        return <Image className="h-5 w-5 text-blue-500" />;
      case 'pdf':
        return <FileText className="h-5 w-5 text-red-500" />;
      case 'word':
        return <FileText className="h-5 w-5 text-blue-700" />;
      case 'excel':
        return <Database className="h-5 w-5 text-green-600" />;
      case 'archive':
        return <Archive className="h-5 w-5 text-yellow-600" />;
      case 'video':
        return <Film className="h-5 w-5 text-purple-500" />;
      case 'audio':
        return <Music className="h-5 w-5 text-pink-500" />;
      default:
        return <File className="h-5 w-5 text-gray-500" />;
    }
  };
  
  const canPreview = (): boolean => {
    const fileType = getFileType();
    return ['image', 'pdf', 'video', 'audio'].includes(fileType);
  };
  
  const renderPreview = () => {
    if (!previewOpen) return null;
    
    const fileType = getFileType();
    
    return (
      <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
        <div className="relative bg-white rounded-lg max-w-4xl max-h-[90vh] overflow-hidden">
          <div className="flex justify-between items-center p-4 border-b">
            <h3 className="font-medium truncate max-w-md">{name}</h3>
            <button 
              onClick={() => setPreviewOpen(false)}
              className="text-gray-500 hover:text-gray-700"
            >
              <X className="h-5 w-5" />
            </button>
          </div>
          
          <div className="p-4 overflow-auto" style={{ maxHeight: 'calc(90vh - 60px)' }}>
            {fileType === 'image' && (
              <img 
                src={url} 
                alt={name} 
                className="max-w-full max-h-[70vh] object-contain mx-auto"
              />
            )}
            
            {fileType === 'pdf' && (
              <iframe 
                src={`${url}#view=FitH`} 
                title={name}
                className="w-full h-[70vh] border-0"
              />
            )}
            
            {fileType === 'video' && (
              <video 
                src={url} 
                controls 
                className="max-w-full max-h-[70vh] mx-auto"
              >
                Your browser does not support the video tag.
              </video>
            )}
            
            {fileType === 'audio' && (
              <audio 
                src={url} 
                controls 
                className="w-full"
              >
                Your browser does not support the audio tag.
              </audio>
            )}
          </div>
          
          <div className="p-4 border-t flex justify-end">
            <a 
              href={url} 
              download={name}
              className="bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 transition-colors flex items-center"
            >
              <Download className="h-4 w-4 mr-2" />
              Download
            </a>
          </div>
        </div>
      </div>
    );
  };
  
  return (
    <>
      <div className="flex items-center bg-gray-100 rounded-lg p-2 max-w-xs">
        <div className="flex-shrink-0 mr-2">
          {getFileIcon()}
        </div>
        
        <div className="flex-grow min-w-0">
          <div className="text-sm font-medium truncate">{name}</div>
        </div>
        
        <div className="flex-shrink-0 ml-2 flex items-center space-x-1">
          {canPreview() && (
            <button
              onClick={() => setPreviewOpen(true)}
              className="text-gray-500 hover:text-blue-600 p-1"
              title="Preview"
            >
              <Eye className="h-4 w-4" />
            </button>
          )}
          
          <a
            href={url}
            download={name}
            className="text-gray-500 hover:text-blue-600 p-1"
            title="Download"
          >
            <Download className="h-4 w-4" />
          </a>
          
          {showRemove && onRemove && (
            <button
              onClick={onRemove}
              className="text-gray-500 hover:text-red-600 p-1"
              title="Remove"
            >
              <X className="h-4 w-4" />
            </button>
          )}
        </div>
      </div>
      
      {renderPreview()}
    </>
  );
};

export default FileAttachment;
