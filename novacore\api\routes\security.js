/**
 * Security Routes
 * 
 * This file defines the routes for security-related functionality.
 */

const express = require('express');
const router = express.Router();
const zeroTrust = require('../middleware/zeroTrust');
const blockchainEvidence = require('../services/blockchainEvidence');
const logger = require('../utils/logger');

// Apply Zero Trust middleware to all routes
router.use(zeroTrust.contextAwareAuth);
router.use(zeroTrust.leastPrivilege);
router.use(zeroTrust.validateRequests);

/**
 * @swagger
 * /api/v1/security/evidence:
 *   post:
 *     summary: Create evidence record
 *     description: Create a new evidence record and anchor it to the blockchain
 *     tags: [Security]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - type
 *               - content
 *             properties:
 *               type:
 *                 type: string
 *                 description: Type of evidence
 *               content:
 *                 type: string
 *                 description: Evidence content
 *               metadata:
 *                 type: object
 *                 description: Additional metadata
 *     responses:
 *       201:
 *         description: Evidence record created
 *       400:
 *         description: Invalid request
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post('/evidence', async (req, res) => {
  try {
    // Validate request
    if (!req.body || !req.body.type || !req.body.content) {
      return res.status(400).json({ error: 'Invalid evidence data' });
    }
    
    // Create evidence record
    const evidence = {
      type: req.body.type,
      content: req.body.content,
      metadata: req.body.metadata || {}
    };
    
    // Add user information to metadata
    evidence.metadata.createdBy = req.user.id;
    evidence.metadata.organizationId = req.user.organizationId;
    
    // Create evidence record
    const evidenceRecord = await blockchainEvidence.createEvidenceRecord(evidence);
    
    // Log evidence creation
    logger.info('Evidence record created', {
      userId: req.user.id,
      evidenceId: evidenceRecord.id,
      evidenceType: evidenceRecord.type
    });
    
    // Return evidence record
    res.status(201).json(evidenceRecord);
  } catch (error) {
    logger.error('Evidence creation error:', error);
    res.status(500).json({ error: 'Failed to create evidence record' });
  }
});

/**
 * @swagger
 * /api/v1/security/evidence/verify:
 *   post:
 *     summary: Verify evidence record
 *     description: Verify an evidence record against the blockchain
 *     tags: [Security]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - evidenceRecord
 *             properties:
 *               evidenceRecord:
 *                 type: object
 *                 description: Evidence record to verify
 *     responses:
 *       200:
 *         description: Verification result
 *       400:
 *         description: Invalid request
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post('/evidence/verify', async (req, res) => {
  try {
    // Validate request
    if (!req.body || !req.body.evidenceRecord) {
      return res.status(400).json({ error: 'Invalid verification request' });
    }
    
    // Verify evidence record
    const verification = await blockchainEvidence.verifyEvidenceRecord(req.body.evidenceRecord);
    
    // Log verification
    logger.info('Evidence record verified', {
      userId: req.user.id,
      evidenceId: req.body.evidenceRecord.id,
      verified: verification.verified
    });
    
    // Return verification result
    res.status(200).json(verification);
  } catch (error) {
    logger.error('Evidence verification error:', error);
    res.status(500).json({ error: 'Failed to verify evidence record' });
  }
});

/**
 * @swagger
 * /api/v1/security/evidence/{id}:
 *   get:
 *     summary: Get evidence record
 *     description: Get an evidence record by ID
 *     tags: [Security]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Evidence record ID
 *     responses:
 *       200:
 *         description: Evidence record
 *       404:
 *         description: Evidence record not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/evidence/:id', async (req, res) => {
  try {
    // In a real implementation, this would query the database for the evidence record
    // For this placeholder, we'll return a mock record
    
    // Check if ID is provided
    if (!req.params.id) {
      return res.status(400).json({ error: 'Evidence ID is required' });
    }
    
    // Create mock evidence record
    const evidenceRecord = {
      id: req.params.id,
      type: 'policy',
      content: 'Information Security Policy',
      metadata: {
        createdBy: req.user.id,
        organizationId: req.user.organizationId,
        version: '1.0'
      },
      createdAt: new Date().toISOString(),
      blockchain: {
        merkleRoot: '0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef',
        proof: ['0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef'],
        submissionId: '12345',
        blockNumber: 12345678,
        blockHash: '0x0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef',
        timestamp: Math.floor(Date.now() / 1000) - 3600, // 1 hour ago
        transactionHash: '0x0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef'
      }
    };
    
    // Log evidence retrieval
    logger.info('Evidence record retrieved', {
      userId: req.user.id,
      evidenceId: req.params.id
    });
    
    // Return evidence record
    res.status(200).json(evidenceRecord);
  } catch (error) {
    logger.error('Evidence retrieval error:', error);
    res.status(500).json({ error: 'Failed to retrieve evidence record' });
  }
});

/**
 * @swagger
 * /api/v1/security/evidence:
 *   get:
 *     summary: List evidence records
 *     description: List evidence records for the current organization
 *     tags: [Security]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *         description: Filter by evidence type
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Items per page
 *     responses:
 *       200:
 *         description: List of evidence records
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/evidence', async (req, res) => {
  try {
    // In a real implementation, this would query the database for evidence records
    // For this placeholder, we'll return mock records
    
    // Parse query parameters
    const type = req.query.type;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    
    // Create mock evidence records
    const evidenceRecords = [];
    for (let i = 0; i < limit; i++) {
      evidenceRecords.push({
        id: `evidence-${(page - 1) * limit + i + 1}`,
        type: type || (i % 3 === 0 ? 'policy' : i % 3 === 1 ? 'control' : 'assessment'),
        content: `Evidence ${(page - 1) * limit + i + 1}`,
        metadata: {
          createdBy: req.user.id,
          organizationId: req.user.organizationId,
          version: '1.0'
        },
        createdAt: new Date().toISOString(),
        blockchain: {
          merkleRoot: '0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef',
          submissionId: '12345',
          blockNumber: 12345678,
          timestamp: Math.floor(Date.now() / 1000) - 3600 * (i + 1) // i+1 hours ago
        }
      });
    }
    
    // Log evidence listing
    logger.info('Evidence records listed', {
      userId: req.user.id,
      count: evidenceRecords.length,
      page,
      limit,
      type
    });
    
    // Return evidence records with pagination
    res.status(200).json({
      records: evidenceRecords,
      pagination: {
        page,
        limit,
        total: 100, // Mock total
        pages: Math.ceil(100 / limit)
      }
    });
  } catch (error) {
    logger.error('Evidence listing error:', error);
    res.status(500).json({ error: 'Failed to list evidence records' });
  }
});

/**
 * @swagger
 * /api/v1/security/status:
 *   get:
 *     summary: Get security status
 *     description: Get the current security status for the organization
 *     tags: [Security]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Security status
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/status', async (req, res) => {
  try {
    // In a real implementation, this would query the security status
    // For this placeholder, we'll return a mock status
    
    const securityStatus = {
      overall: 'good',
      lastUpdated: new Date().toISOString(),
      components: {
        accessControl: {
          status: 'good',
          lastChecked: new Date().toISOString(),
          issues: 0
        },
        dataProtection: {
          status: 'good',
          lastChecked: new Date().toISOString(),
          issues: 0
        },
        vulnerabilityManagement: {
          status: 'warning',
          lastChecked: new Date().toISOString(),
          issues: 2
        },
        incidentResponse: {
          status: 'good',
          lastChecked: new Date().toISOString(),
          issues: 0
        }
      },
      alerts: [
        {
          id: 'alert-1',
          severity: 'medium',
          message: 'Vulnerability scan found 2 medium severity issues',
          timestamp: new Date().toISOString()
        }
      ]
    };
    
    // Log security status retrieval
    logger.info('Security status retrieved', {
      userId: req.user.id,
      status: securityStatus.overall
    });
    
    // Return security status
    res.status(200).json(securityStatus);
  } catch (error) {
    logger.error('Security status error:', error);
    res.status(500).json({ error: 'Failed to retrieve security status' });
  }
});

module.exports = router;
