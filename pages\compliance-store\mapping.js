import React from 'react';
import PageWithSidebar from '../../components/PageWithSidebar';
import CrossFrameworkMapping from '../../components/CrossFrameworkMapping';
import ProtectedRoute from '../../components/ProtectedRoute';
import { motion } from 'framer-motion';

function MappingContent() {
  // Define sidebar items for the Mapping page
  const sidebarItems = [
    { type: 'category', label: 'Compliance Mapping', items: [
      { label: 'Framework Mapping', href: '#framework-mapping' },
      { label: 'Coverage Analysis', href: '#coverage-analysis' }
    ]},
    { type: 'category', label: 'Navigation', items: [
      { label: 'Dashboard', href: '/compliance-store/dashboard' },
      { label: 'Browse Connectors', href: '/compliance-store/browse' },
      { label: 'Compliance Store Home', href: '/compliance-store' }
    ]}
  ];

  // SEO metadata
  const pageProps = {
    title: 'Cross-Framework Mapping - NovaFuse Compliance App Store',
    description: 'Map controls and requirements between different compliance frameworks with NovaFuse\'s Cross-Framework Mapping Engine.',
    keywords: 'compliance mapping, cross-framework mapping, control mapping, requirement mapping, NovaFuse, compliance frameworks',
    canonical: 'https://novafuse.io/compliance-store/mapping',
    ogImage: '/images/mapping-og-image.png'
  };

  return (
    <PageWithSidebar title={pageProps.title} sidebarItems={sidebarItems} {...pageProps}>
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-4">Cross-Framework Mapping</h1>
        <p className="text-xl">
          Map controls and requirements between different compliance frameworks to understand your compliance coverage.
        </p>
      </div>

      {/* Framework Mapping Section */}
      <div id="framework-mapping" className="mb-12">
        <h2 className="text-2xl font-bold mb-6">Framework Mapping</h2>
        <CrossFrameworkMapping />
      </div>

      {/* Coverage Analysis Section */}
      <div id="coverage-analysis" className="mb-12">
        <h2 className="text-2xl font-bold mb-6">Coverage Analysis</h2>
        <div className="bg-secondary rounded-lg p-6">
          <p className="mb-4">
            Analyze your compliance coverage across different frameworks based on your implemented controls.
          </p>
          <div className="bg-blue-900 bg-opacity-30 border border-blue-700 rounded-lg p-6 mb-6">
            <h3 className="text-xl font-bold mb-2">Coming Soon</h3>
            <p>
              The Coverage Analysis feature is currently under development. Check back soon for updates!
            </p>
          </div>
          <p>
            With Coverage Analysis, you'll be able to:
          </p>
          <ul className="space-y-2 mt-4">
            <li className="flex items-start">
              <span className="text-green-400 mr-2">✓</span>
              <span>Calculate compliance coverage across different frameworks</span>
            </li>
            <li className="flex items-start">
              <span className="text-green-400 mr-2">✓</span>
              <span>Identify gaps in your compliance program</span>
            </li>
            <li className="flex items-start">
              <span className="text-green-400 mr-2">✓</span>
              <span>Prioritize control implementation based on coverage impact</span>
            </li>
            <li className="flex items-start">
              <span className="text-green-400 mr-2">✓</span>
              <span>Generate compliance coverage reports for stakeholders</span>
            </li>
          </ul>
        </div>
      </div>

      {/* Call to Action */}
      <div className="bg-blue-900 rounded-lg p-8 text-center mb-12">
        <h2 className="text-2xl font-bold mb-4">Ready to simplify compliance mapping?</h2>
        <p className="mb-6">Get early access to NovaFuse's Cross-Framework Mapping Engine and transform your compliance program.</p>
        <motion.button
          className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={() => window.location.href = '/contact'}
        >
          Get Started
        </motion.button>
      </div>

      {/* Confidentiality Notice */}
      <div className="border border-blue-800 bg-blue-900 bg-opacity-20 rounded-lg p-4">
        <div className="flex items-start">
          <div className="text-yellow-400 mr-3 mt-1">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
          </div>
          <div>
            <p className="text-sm">
              <strong>CONFIDENTIAL:</strong> The NovaFuse Compliance App Store is currently under IP protection review.
              All content is considered confidential and proprietary. Unauthorized access or sharing is prohibited.
            </p>
          </div>
        </div>
      </div>
    </PageWithSidebar>
  );
}

// Export the mapping page with protection
export default function Mapping() {
  return (
    <ProtectedRoute>
      <MappingContent />
    </ProtectedRoute>
  );
}
