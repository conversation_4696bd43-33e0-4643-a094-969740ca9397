/**
 * BioEntropicTensor
 * 
 * This module implements the BioEntropicTensor class, which processes biological data
 * into entropy variables and creates tensor representations for the CSME engine.
 * 
 * The BioEntropicTensor is responsible for:
 * 1. Processing raw biological data into entropy variables
 * 2. Calculating coherence (Ψₜ) and entropy gradient (ΔΨₑ)
 * 3. Creating tensor representations of biological metrics
 * 4. Calculating coherence dynamics (velocity and acceleration)
 * 5. Supporting multiple entropy estimators (<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)
 */

const { performance } = require('perf_hooks');

/**
 * BioEntropicTensor class
 */
class BioEntropicTensor {
  /**
   * Create a new BioEntropicTensor instance
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    this.options = {
      entropyEstimator: 'shannon', // 'shannon', 'renyi', 'tsallis'
      renyiAlpha: 2.0, // Alpha parameter for Rényi entropy
      tsallisQ: 1.5, // q parameter for <PERSON><PERSON>lis entropy
      tensorDimensions: 4, // Default tensor dimensions
      enableCaching: true, // Enable result caching
      enableMetrics: true, // Enable performance metrics
      coherenceThreshold: 0.82, // Default coherence threshold based on 18/82 principle
      ...options
    };
    
    // Initialize cache
    this.cache = new Map();
    
    // Initialize metrics
    this.metrics = {
      processingTimeMs: 0,
      cacheHits: 0,
      cacheMisses: 0,
      totalProcessed: 0
    };
    
    console.log('BioEntropicTensor initialized with entropy estimator:', this.options.entropyEstimator);
  }
  
  /**
   * Process biological data into entropy variables and tensor representation
   * @param {Object} biologicalData - Raw biological data
   * @returns {Object} - Processed data with entropy variables and tensor representation
   */
  processBiologicalData(biologicalData) {
    const startTime = performance.now();
    this.metrics.totalProcessed++;
    
    // Generate cache key
    const cacheKey = this._generateCacheKey(biologicalData);
    
    // Check cache if enabled
    if (this.options.enableCaching && this.cache.has(cacheKey)) {
      this.metrics.cacheHits++;
      const cachedResult = this.cache.get(cacheKey);
      
      // Update processing time
      this.metrics.processingTimeMs += performance.now() - startTime;
      
      return cachedResult;
    }
    
    this.metrics.cacheMisses++;
    
    // Extract key biological metrics
    const { 
      genomicData = {}, 
      proteomicData = {}, 
      clinicalData = {},
      environmentalData = {}
    } = biologicalData;
    
    // Calculate entropy for each component
    const genomicEntropy = this._calculateEntropy(genomicData);
    const proteomicEntropy = this._calculateEntropy(proteomicData);
    const clinicalEntropy = this._calculateEntropy(clinicalData);
    const environmentalEntropy = this._calculateEntropy(environmentalData);
    
    // Calculate coherence
    const coherence = this._calculateCoherence(
      genomicEntropy,
      proteomicEntropy,
      clinicalEntropy,
      environmentalEntropy
    );
    
    // Create tensor representation
    const tensor = this._createTensor(
      genomicEntropy,
      proteomicEntropy,
      clinicalEntropy,
      environmentalEntropy,
      coherence
    );
    
    // Calculate entropy gradient (if previous data available)
    const entropyGradient = this._calculateEntropyGradient(coherence);
    
    // Prepare result
    const result = {
      coherence,
      entropyGradient,
      tensor,
      components: {
        genomicEntropy,
        proteomicEntropy,
        clinicalEntropy,
        environmentalEntropy
      },
      processedAt: new Date().toISOString()
    };
    
    // Cache result if caching is enabled
    if (this.options.enableCaching) {
      this.cache.set(cacheKey, result);
      
      // Limit cache size
      if (this.cache.size > 1000) {
        const oldestKey = this.cache.keys().next().value;
        this.cache.delete(oldestKey);
      }
    }
    
    // Update processing time
    this.metrics.processingTimeMs += performance.now() - startTime;
    
    return result;
  }
  
  /**
   * Calculate coherence dynamics (velocity and acceleration)
   * @param {Array} coherenceHistory - History of coherence values
   * @returns {Object} - Coherence dynamics including velocity and acceleration
   */
  calculateCoherenceDynamics(coherenceHistory) {
    if (!coherenceHistory || coherenceHistory.length < 2) {
      return {
        velocity: 0,
        acceleration: 0,
        isDecaying: false,
        decayRate: 0
      };
    }
    
    // Calculate velocity (first derivative)
    const velocities = [];
    for (let i = 1; i < coherenceHistory.length; i++) {
      velocities.push(coherenceHistory[i] - coherenceHistory[i - 1]);
    }
    
    // Calculate acceleration (second derivative)
    const accelerations = [];
    for (let i = 1; i < velocities.length; i++) {
      accelerations.push(velocities[i] - velocities[i - 1]);
    }
    
    // Calculate average velocity and acceleration
    const avgVelocity = velocities.reduce((sum, v) => sum + v, 0) / velocities.length;
    const avgAcceleration = accelerations.length > 0 
      ? accelerations.reduce((sum, a) => sum + a, 0) / accelerations.length
      : 0;
    
    // Determine if system is decaying
    const isDecaying = avgVelocity < 0;
    
    // Calculate decay rate (if decaying)
    const decayRate = isDecaying ? Math.abs(avgVelocity) : 0;
    
    return {
      velocity: avgVelocity,
      acceleration: avgAcceleration,
      isDecaying,
      decayRate
    };
  }
  
  /**
   * Calculate entropy based on the selected estimator
   * @param {Object|Array} data - Input data
   * @returns {number} - Entropy value
   * @private
   */
  _calculateEntropy(data) {
    // Convert data to array of values
    const values = this._extractValues(data);
    
    // If no values, return maximum entropy
    if (values.length === 0) {
      return 1.0;
    }
    
    // Normalize values to probabilities
    const probabilities = this._normalizeToProbabilities(values);
    
    // Calculate entropy based on selected estimator
    switch (this.options.entropyEstimator) {
      case 'renyi':
        return this._calculateRenyiEntropy(probabilities);
      case 'tsallis':
        return this._calculateTsallisEntropy(probabilities);
      case 'shannon':
      default:
        return this._calculateShannonEntropy(probabilities);
    }
  }
  
  /**
   * Calculate Shannon entropy
   * @param {Array} probabilities - Array of probability values
   * @returns {number} - Shannon entropy value
   * @private
   */
  _calculateShannonEntropy(probabilities) {
    return -probabilities.reduce((sum, p) => {
      return sum + (p > 0 ? p * Math.log2(p) : 0);
    }, 0);
  }
  
  /**
   * Calculate Rényi entropy
   * @param {Array} probabilities - Array of probability values
   * @returns {number} - Rényi entropy value
   * @private
   */
  _calculateRenyiEntropy(probabilities) {
    const alpha = this.options.renyiAlpha;
    
    // Handle special case: alpha = 1 (Shannon entropy)
    if (Math.abs(alpha - 1.0) < 0.0001) {
      return this._calculateShannonEntropy(probabilities);
    }
    
    const sum = probabilities.reduce((acc, p) => {
      return acc + (p > 0 ? Math.pow(p, alpha) : 0);
    }, 0);
    
    return (1 / (1 - alpha)) * Math.log2(sum);
  }
  
  /**
   * Calculate Tsallis entropy
   * @param {Array} probabilities - Array of probability values
   * @returns {number} - Tsallis entropy value
   * @private
   */
  _calculateTsallisEntropy(probabilities) {
    const q = this.options.tsallisQ;
    
    // Handle special case: q = 1 (Shannon entropy)
    if (Math.abs(q - 1.0) < 0.0001) {
      return this._calculateShannonEntropy(probabilities);
    }
    
    const sum = probabilities.reduce((acc, p) => {
      return acc + (p > 0 ? Math.pow(p, q) : 0);
    }, 0);
    
    return (1 - sum) / (q - 1);
  }
  
  /**
   * Calculate coherence from entropy components
   * @param {number} genomicEntropy - Genomic entropy
   * @param {number} proteomicEntropy - Proteomic entropy
   * @param {number} clinicalEntropy - Clinical entropy
   * @param {number} environmentalEntropy - Environmental entropy
   * @returns {number} - Coherence value (0-1)
   * @private
   */
  _calculateCoherence(genomicEntropy, proteomicEntropy, clinicalEntropy, environmentalEntropy) {
    // Apply 18/82 principle: 18% weight to genomic and proteomic, 82% to clinical and environmental
    const weightedEntropy = 
      (0.18 * (genomicEntropy + proteomicEntropy) / 2) + 
      (0.82 * (clinicalEntropy + environmentalEntropy) / 2);
    
    // Convert entropy to coherence (inverse relationship)
    // Normalize to 0-1 range where 1 is maximum coherence (minimum entropy)
    const maxEntropy = 1.0; // Maximum possible entropy
    const coherence = 1.0 - (weightedEntropy / maxEntropy);
    
    return Math.max(0, Math.min(1, coherence));
  }
  
  /**
   * Create tensor representation of biological metrics
   * @param {number} genomicEntropy - Genomic entropy
   * @param {number} proteomicEntropy - Proteomic entropy
   * @param {number} clinicalEntropy - Clinical entropy
   * @param {number} environmentalEntropy - Environmental entropy
   * @param {number} coherence - Overall coherence
   * @returns {Array} - Tensor representation [T, I, E, c₃]
   * @private
   */
  _createTensor(genomicEntropy, proteomicEntropy, clinicalEntropy, environmentalEntropy, coherence) {
    // Create tensor [T, I, E, c₃] for CSME
    // T = Trust (inverse of genomic entropy)
    // I = Integrity (inverse of proteomic entropy)
    // E = Effectiveness (inverse of clinical entropy)
    // c₃ = Coherence factor
    
    const trust = 1.0 - genomicEntropy;
    const integrity = 1.0 - proteomicEntropy;
    const effectiveness = 1.0 - clinicalEntropy;
    const coherenceFactor = coherence;
    
    return [trust, integrity, effectiveness, coherenceFactor];
  }
  
  /**
   * Calculate entropy gradient based on current coherence
   * @param {number} currentCoherence - Current coherence value
   * @returns {number} - Entropy gradient
   * @private
   */
  _calculateEntropyGradient(currentCoherence) {
    // If no previous coherence, return 0 gradient
    if (!this.previousCoherence) {
      this.previousCoherence = currentCoherence;
      this.previousTimestamp = Date.now();
      return 0;
    }
    
    // Calculate time delta in seconds
    const currentTime = Date.now();
    const deltaTime = (currentTime - this.previousTimestamp) / 1000;
    
    // If time delta is too small, return previous gradient
    if (deltaTime < 0.001) {
      return this.previousGradient || 0;
    }
    
    // Calculate gradient
    const deltaCoherence = currentCoherence - this.previousCoherence;
    const gradient = deltaCoherence / deltaTime;
    
    // Update previous values
    this.previousCoherence = currentCoherence;
    this.previousTimestamp = currentTime;
    this.previousGradient = gradient;
    
    return gradient;
  }
  
  /**
   * Extract values from data object or array
   * @param {Object|Array} data - Input data
   * @returns {Array} - Array of values
   * @private
   */
  _extractValues(data) {
    if (!data) {
      return [];
    }
    
    if (Array.isArray(data)) {
      return data.filter(v => typeof v === 'number');
    }
    
    if (typeof data === 'object') {
      return Object.values(data).filter(v => typeof v === 'number');
    }
    
    return [typeof data === 'number' ? data : 0];
  }
  
  /**
   * Normalize values to probabilities
   * @param {Array} values - Array of values
   * @returns {Array} - Array of probabilities
   * @private
   */
  _normalizeToProbabilities(values) {
    const sum = values.reduce((acc, val) => acc + Math.abs(val), 0);
    
    if (sum === 0) {
      // If sum is 0, return uniform distribution
      return values.map(() => 1 / values.length);
    }
    
    // Normalize values to probabilities
    return values.map(val => Math.abs(val) / sum);
  }
  
  /**
   * Generate cache key for biological data
   * @param {Object} biologicalData - Biological data
   * @returns {string} - Cache key
   * @private
   */
  _generateCacheKey(biologicalData) {
    try {
      // Use only essential properties for the key
      const keyData = {
        g: this._hashObject(biologicalData.genomicData),
        p: this._hashObject(biologicalData.proteomicData),
        c: this._hashObject(biologicalData.clinicalData),
        e: this._hashObject(biologicalData.environmentalData)
      };
      
      return JSON.stringify(keyData);
    } catch (error) {
      console.error('Error generating cache key:', error);
      return Date.now().toString(); // Fallback to timestamp
    }
  }
  
  /**
   * Create a simple hash of an object
   * @param {Object} obj - Input object
   * @returns {number} - Hash value
   * @private
   */
  _hashObject(obj) {
    if (!obj) return 0;
    
    const str = JSON.stringify(obj);
    let hash = 0;
    
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    
    return hash;
  }
}

module.exports = BioEntropicTensor;
