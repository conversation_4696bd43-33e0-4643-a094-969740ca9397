const axios = require('axios');
const hubspotIntegration = require('../../../../apis/privacy/management/integrations/hubspot');

// Mock axios
jest.mock('axios');

describe('HubSpot Integration', () => {
  // Mock data
  const mockConfig = {
    apiKey: 'test-api-key'
  };
  
  const mockContact = {
    id: 'contact-123',
    properties: {
      email: '<EMAIL>',
      firstname: '<PERSON>',
      lastname: '<PERSON><PERSON>',
      phone: '******-123-4567'
    }
  };
  
  const mockDeal = {
    id: 'deal-123',
    properties: {
      dealname: 'Test Deal',
      amount: '1000',
      dealstage: 'closedwon'
    }
  };
  
  const mockActivity = {
    id: 'activity-123',
    properties: {
      type: 'EMAIL',
      timestamp: '2023-06-15T10:30:00Z',
      subject: 'Test Email'
    }
  };
  
  // Reset mocks before each test
  beforeEach(() => {
    jest.resetAllMocks();
  });
  
  describe('getClient', () => {
    it('should create a client with the correct configuration', () => {
      const client = hubspotIntegration.getClient(mockConfig);
      
      expect(axios.create).toHaveBeenCalledWith({
        baseURL: 'https://api.hubapi.com',
        headers: {
          'Authorization': 'Bearer test-api-key',
          'Content-Type': 'application/json'
        }
      });
    });
    
    it('should throw an error if API key is missing', () => {
      expect(() => {
        hubspotIntegration.getClient({});
      }).toThrow('HubSpot API key is required');
    });
  });
  
  describe('searchContactsByEmail', () => {
    it('should search for contacts by email', async () => {
      const mockClient = {
        get: jest.fn().mockResolvedValue({
          data: {
            results: [mockContact]
          }
        })
      };
      
      const result = await hubspotIntegration.searchContactsByEmail(mockClient, '<EMAIL>');
      
      expect(mockClient.get).toHaveBeenCalledWith('/crm/v3/objects/contacts/search', {
        params: {
          filterGroups: JSON.stringify([
            {
              filters: [
                {
                  propertyName: 'email',
                  operator: 'EQ',
                  value: '<EMAIL>'
                }
              ]
            }
          ])
        }
      });
      
      expect(result).toEqual([mockContact]);
    });
    
    it('should return an empty array if no contacts are found', async () => {
      const mockClient = {
        get: jest.fn().mockResolvedValue({
          data: {
            results: []
          }
        })
      };
      
      const result = await hubspotIntegration.searchContactsByEmail(mockClient, '<EMAIL>');
      
      expect(result).toEqual([]);
    });
    
    it('should handle errors', async () => {
      const mockClient = {
        get: jest.fn().mockRejectedValue(new Error('API error'))
      };
      
      await expect(
        hubspotIntegration.searchContactsByEmail(mockClient, '<EMAIL>')
      ).rejects.toThrow('API error');
    });
  });
  
  describe('getContact', () => {
    it('should get contact details', async () => {
      const mockClient = {
        get: jest.fn().mockResolvedValue({
          data: mockContact
        })
      };
      
      const result = await hubspotIntegration.getContact(mockClient, 'contact-123', ['email', 'firstname', 'lastname']);
      
      expect(mockClient.get).toHaveBeenCalledWith('/crm/v3/objects/contacts/contact-123', {
        params: {
          properties: 'email,firstname,lastname'
        }
      });
      
      expect(result).toEqual(mockContact);
    });
    
    it('should handle errors', async () => {
      const mockClient = {
        get: jest.fn().mockRejectedValue(new Error('API error'))
      };
      
      await expect(
        hubspotIntegration.getContact(mockClient, 'contact-123', ['email'])
      ).rejects.toThrow('API error');
    });
  });
  
  describe('getContactDeals', () => {
    it('should get deals associated with a contact', async () => {
      const mockClient = {
        get: jest.fn()
          .mockResolvedValueOnce({
            data: {
              results: [{ id: 'deal-123' }]
            }
          })
          .mockResolvedValueOnce({
            data: mockDeal
          })
      };
      
      const result = await hubspotIntegration.getContactDeals(mockClient, 'contact-123');
      
      expect(mockClient.get).toHaveBeenCalledWith('/crm/v3/objects/contacts/contact-123/associations/deals');
      expect(mockClient.get).toHaveBeenCalledWith('/crm/v3/objects/deals/deal-123');
      
      expect(result).toEqual([mockDeal]);
    });
    
    it('should handle errors', async () => {
      const mockClient = {
        get: jest.fn().mockRejectedValue(new Error('API error'))
      };
      
      await expect(
        hubspotIntegration.getContactDeals(mockClient, 'contact-123')
      ).rejects.toThrow('API error');
    });
  });
  
  describe('getContactActivities', () => {
    it('should get activities associated with a contact', async () => {
      const mockClient = {
        get: jest.fn()
          .mockResolvedValueOnce({
            data: {
              results: [{ id: 'activity-123' }]
            }
          })
          .mockResolvedValueOnce({
            data: mockActivity
          })
      };
      
      const result = await hubspotIntegration.getContactActivities(mockClient, 'contact-123');
      
      expect(mockClient.get).toHaveBeenCalledWith('/crm/v3/objects/contacts/contact-123/associations/engagements');
      expect(mockClient.get).toHaveBeenCalledWith('/crm/v3/objects/engagements/activity-123');
      
      expect(result).toEqual([mockActivity]);
    });
    
    it('should handle errors', async () => {
      const mockClient = {
        get: jest.fn().mockRejectedValue(new Error('API error'))
      };
      
      await expect(
        hubspotIntegration.getContactActivities(mockClient, 'contact-123')
      ).rejects.toThrow('API error');
    });
  });
  
  describe('updateContact', () => {
    it('should update contact properties', async () => {
      const mockClient = {
        patch: jest.fn().mockResolvedValue({
          data: {
            ...mockContact,
            properties: {
              ...mockContact.properties,
              phone: '******-987-6543'
            }
          }
        })
      };
      
      const result = await hubspotIntegration.updateContact(mockClient, 'contact-123', {
        phone: '******-987-6543'
      });
      
      expect(mockClient.patch).toHaveBeenCalledWith('/crm/v3/objects/contacts/contact-123', {
        properties: {
          phone: '******-987-6543'
        }
      });
      
      expect(result.properties.phone).toBe('******-987-6543');
    });
    
    it('should handle errors', async () => {
      const mockClient = {
        patch: jest.fn().mockRejectedValue(new Error('API error'))
      };
      
      await expect(
        hubspotIntegration.updateContact(mockClient, 'contact-123', { phone: '******-987-6543' })
      ).rejects.toThrow('API error');
    });
  });
  
  describe('deleteContact', () => {
    it('should delete a contact', async () => {
      const mockClient = {
        delete: jest.fn().mockResolvedValue({})
      };
      
      const result = await hubspotIntegration.deleteContact(mockClient, 'contact-123');
      
      expect(mockClient.delete).toHaveBeenCalledWith('/crm/v3/objects/contacts/contact-123');
      expect(result).toBe(true);
    });
    
    it('should handle errors', async () => {
      const mockClient = {
        delete: jest.fn().mockRejectedValue(new Error('API error'))
      };
      
      await expect(
        hubspotIntegration.deleteContact(mockClient, 'contact-123')
      ).rejects.toThrow('API error');
    });
  });
  
  describe('exportData', () => {
    it('should export data from HubSpot', async () => {
      // Mock the internal functions
      const originalGetClient = hubspotIntegration.getClient;
      const originalSearchContactsByEmail = hubspotIntegration.searchContactsByEmail;
      const originalGetContact = hubspotIntegration.getContact;
      const originalGetContactDeals = hubspotIntegration.getContactDeals;
      const originalGetContactActivities = hubspotIntegration.getContactActivities;
      
      hubspotIntegration.getClient = jest.fn().mockReturnValue({});
      hubspotIntegration.searchContactsByEmail = jest.fn().mockResolvedValue([{ id: 'contact-123' }]);
      hubspotIntegration.getContact = jest.fn().mockResolvedValue(mockContact);
      hubspotIntegration.getContactDeals = jest.fn().mockResolvedValue([mockDeal]);
      hubspotIntegration.getContactActivities = jest.fn().mockResolvedValue([mockActivity]);
      
      const result = await hubspotIntegration.exportData(mockConfig, {
        email: '<EMAIL>',
        dataCategories: ['contacts', 'deals', 'activities']
      });
      
      expect(hubspotIntegration.getClient).toHaveBeenCalledWith(mockConfig);
      expect(hubspotIntegration.searchContactsByEmail).toHaveBeenCalledWith({}, '<EMAIL>');
      expect(hubspotIntegration.getContact).toHaveBeenCalledWith({}, 'contact-123', expect.any(Array));
      expect(hubspotIntegration.getContactDeals).toHaveBeenCalledWith({}, 'contact-123');
      expect(hubspotIntegration.getContactActivities).toHaveBeenCalledWith({}, 'contact-123');
      
      expect(result).toEqual({
        contacts: [mockContact],
        deals: [mockDeal],
        activities: [mockActivity]
      });
      
      // Restore original functions
      hubspotIntegration.getClient = originalGetClient;
      hubspotIntegration.searchContactsByEmail = originalSearchContactsByEmail;
      hubspotIntegration.getContact = originalGetContact;
      hubspotIntegration.getContactDeals = originalGetContactDeals;
      hubspotIntegration.getContactActivities = originalGetContactActivities;
    });
    
    it('should return empty results if no contacts are found', async () => {
      // Mock the internal functions
      const originalGetClient = hubspotIntegration.getClient;
      const originalSearchContactsByEmail = hubspotIntegration.searchContactsByEmail;
      
      hubspotIntegration.getClient = jest.fn().mockReturnValue({});
      hubspotIntegration.searchContactsByEmail = jest.fn().mockResolvedValue([]);
      
      const result = await hubspotIntegration.exportData(mockConfig, {
        email: '<EMAIL>'
      });
      
      expect(result).toEqual({
        contacts: [],
        deals: [],
        activities: []
      });
      
      // Restore original functions
      hubspotIntegration.getClient = originalGetClient;
      hubspotIntegration.searchContactsByEmail = originalSearchContactsByEmail;
    });
    
    it('should throw an error if email is missing', async () => {
      await expect(
        hubspotIntegration.exportData(mockConfig, {})
      ).rejects.toThrow('Email is required for HubSpot data export');
    });
  });
  
  describe('deleteData', () => {
    it('should delete data from HubSpot', async () => {
      // Mock the internal functions
      const originalGetClient = hubspotIntegration.getClient;
      const originalSearchContactsByEmail = hubspotIntegration.searchContactsByEmail;
      const originalDeleteContact = hubspotIntegration.deleteContact;
      
      hubspotIntegration.getClient = jest.fn().mockReturnValue({});
      hubspotIntegration.searchContactsByEmail = jest.fn().mockResolvedValue([
        { id: 'contact-123' },
        { id: 'contact-456' }
      ]);
      hubspotIntegration.deleteContact = jest.fn().mockResolvedValue(true);
      
      const result = await hubspotIntegration.deleteData(mockConfig, {
        email: '<EMAIL>'
      });
      
      expect(hubspotIntegration.getClient).toHaveBeenCalledWith(mockConfig);
      expect(hubspotIntegration.searchContactsByEmail).toHaveBeenCalledWith({}, '<EMAIL>');
      expect(hubspotIntegration.deleteContact).toHaveBeenCalledTimes(2);
      expect(hubspotIntegration.deleteContact).toHaveBeenCalledWith({}, 'contact-123');
      expect(hubspotIntegration.deleteContact).toHaveBeenCalledWith({}, 'contact-456');
      
      expect(result).toEqual({
        deletedContacts: 2
      });
      
      // Restore original functions
      hubspotIntegration.getClient = originalGetClient;
      hubspotIntegration.searchContactsByEmail = originalSearchContactsByEmail;
      hubspotIntegration.deleteContact = originalDeleteContact;
    });
    
    it('should return zero deletions if no contacts are found', async () => {
      // Mock the internal functions
      const originalGetClient = hubspotIntegration.getClient;
      const originalSearchContactsByEmail = hubspotIntegration.searchContactsByEmail;
      
      hubspotIntegration.getClient = jest.fn().mockReturnValue({});
      hubspotIntegration.searchContactsByEmail = jest.fn().mockResolvedValue([]);
      
      const result = await hubspotIntegration.deleteData(mockConfig, {
        email: '<EMAIL>'
      });
      
      expect(result).toEqual({
        deletedContacts: 0
      });
      
      // Restore original functions
      hubspotIntegration.getClient = originalGetClient;
      hubspotIntegration.searchContactsByEmail = originalSearchContactsByEmail;
    });
    
    it('should throw an error if email is missing', async () => {
      await expect(
        hubspotIntegration.deleteData(mockConfig, {})
      ).rejects.toThrow('Email is required for HubSpot data deletion');
    });
  });
  
  describe('updateData', () => {
    it('should update data in HubSpot', async () => {
      // Mock the internal functions
      const originalGetClient = hubspotIntegration.getClient;
      const originalSearchContactsByEmail = hubspotIntegration.searchContactsByEmail;
      const originalUpdateContact = hubspotIntegration.updateContact;
      
      hubspotIntegration.getClient = jest.fn().mockReturnValue({});
      hubspotIntegration.searchContactsByEmail = jest.fn().mockResolvedValue([
        { id: 'contact-123' },
        { id: 'contact-456' }
      ]);
      hubspotIntegration.updateContact = jest.fn().mockResolvedValue({});
      
      const updates = {
        phone: '******-987-6543',
        address: '123 Main St, Anytown, USA'
      };
      
      const result = await hubspotIntegration.updateData(mockConfig, {
        email: '<EMAIL>',
        updates
      });
      
      expect(hubspotIntegration.getClient).toHaveBeenCalledWith(mockConfig);
      expect(hubspotIntegration.searchContactsByEmail).toHaveBeenCalledWith({}, '<EMAIL>');
      expect(hubspotIntegration.updateContact).toHaveBeenCalledTimes(2);
      expect(hubspotIntegration.updateContact).toHaveBeenCalledWith({}, 'contact-123', updates);
      expect(hubspotIntegration.updateContact).toHaveBeenCalledWith({}, 'contact-456', updates);
      
      expect(result).toEqual({
        updatedContacts: 2
      });
      
      // Restore original functions
      hubspotIntegration.getClient = originalGetClient;
      hubspotIntegration.searchContactsByEmail = originalSearchContactsByEmail;
      hubspotIntegration.updateContact = originalUpdateContact;
    });
    
    it('should return zero updates if no contacts are found', async () => {
      // Mock the internal functions
      const originalGetClient = hubspotIntegration.getClient;
      const originalSearchContactsByEmail = hubspotIntegration.searchContactsByEmail;
      
      hubspotIntegration.getClient = jest.fn().mockReturnValue({});
      hubspotIntegration.searchContactsByEmail = jest.fn().mockResolvedValue([]);
      
      const result = await hubspotIntegration.updateData(mockConfig, {
        email: '<EMAIL>',
        updates: { phone: '******-987-6543' }
      });
      
      expect(result).toEqual({
        updatedContacts: 0
      });
      
      // Restore original functions
      hubspotIntegration.getClient = originalGetClient;
      hubspotIntegration.searchContactsByEmail = originalSearchContactsByEmail;
    });
    
    it('should throw an error if email is missing', async () => {
      await expect(
        hubspotIntegration.updateData(mockConfig, {
          updates: { phone: '******-987-6543' }
        })
      ).rejects.toThrow('Email is required for HubSpot data update');
    });
    
    it('should throw an error if updates are missing', async () => {
      await expect(
        hubspotIntegration.updateData(mockConfig, {
          email: '<EMAIL>'
        })
      ).rejects.toThrow('Updates are required for HubSpot data update');
    });
  });
  
  describe('healthCheck', () => {
    it('should return healthy status if the integration is working', async () => {
      // Mock the internal functions
      const originalGetClient = hubspotIntegration.getClient;
      
      hubspotIntegration.getClient = jest.fn().mockReturnValue({
        get: jest.fn().mockResolvedValue({})
      });
      
      const result = await hubspotIntegration.healthCheck(mockConfig);
      
      expect(result).toEqual({
        status: 'healthy',
        message: 'HubSpot integration is working properly'
      });
      
      // Restore original function
      hubspotIntegration.getClient = originalGetClient;
    });
    
    it('should return unhealthy status if the integration is not working', async () => {
      // Mock the internal functions
      const originalGetClient = hubspotIntegration.getClient;
      
      hubspotIntegration.getClient = jest.fn().mockReturnValue({
        get: jest.fn().mockRejectedValue(new Error('API error'))
      });
      
      const result = await hubspotIntegration.healthCheck(mockConfig);
      
      expect(result).toEqual({
        status: 'unhealthy',
        message: 'HubSpot integration error: API error'
      });
      
      // Restore original function
      hubspotIntegration.getClient = originalGetClient;
    });
  });
});
