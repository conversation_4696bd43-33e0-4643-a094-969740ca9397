import React from 'react';
import styles from '../styles/Home.module.css';

const PerformanceMetrics = ({ stats, products }) => {
  // Calculate triadic metrics
  const triadicMetrics = {
    psi: products.reduce((sum, product) => sum + (product.psi || 0), 0) / (products.length || 1),
    phi: products.reduce((sum, product) => sum + (product.phi || 0), 0) / (products.length || 1),
    kappa: products.reduce((sum, product) => sum + (product.kappa || 0), 0) / (products.length || 1)
  };

  // Map stats to metrics format
  const metrics = {
    conversionRate: stats.conversionRate * 100,
    clickThroughRate: stats.monthlySales / 100,
    averageEarnings: stats.totalRevenue / (stats.monthlySales || 1),
    salesVolume: stats.monthlySales,
    triadicScore: (triadicMetrics.psi + triadicMetrics.phi + triadicMetrics.kappa) / 3
  };

  return (
    <div className={styles.card}>
      <h3>Performance Metrics</h3>
      <div className={styles.metricsGrid}>
        <div className={styles.metricCard}>
          <h4>Conversion Rate</h4>
          <p>{metrics.conversionRate.toFixed(1)}%</p>
          <span className={styles.trend}>↑ 0.8%</span>
        </div>
        <div className={styles.metricCard}>
          <h4>CTR</h4>
          <p>{metrics.clickThroughRate.toFixed(1)}%</p>
          <span className={styles.trend}>↑ 1.2%</span>
        </div>
        <div className={styles.metricCard}>
          <h4>Average Earnings</h4>
          <p>${metrics.averageEarnings.toFixed(2)}</p>
          <span className={styles.trend}>↑ 5.5%</span>
        </div>
        <div className={styles.metricCard}>
          <h4>Sales Volume</h4>
          <p>{metrics.salesVolume}</p>
          <span className={styles.trend}>↑ 15</span>
        </div>
      </div>
    </div>
  );
};

export default PerformanceMetrics;
