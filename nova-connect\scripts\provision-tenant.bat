@echo off
REM NovaConnect UAC Tenant Provisioning Script for Windows

REM Set variables
set TENANT_ID=%1
set TENANT_NAME=%2
if "%TENANT_NAME%"=="" set TENANT_NAME=Tenant %TENANT_ID%
set TIER=%3
if "%TIER%"=="" set TIER=core
set REPLICA_COUNT=%4
if "%REPLICA_COUNT%"=="" set REPLICA_COUNT=3
set CPU_REQUEST=%5
if "%CPU_REQUEST%"=="" set CPU_REQUEST=500m
set MEMORY_REQUEST=%6
if "%MEMORY_REQUEST%"=="" set MEMORY_REQUEST=512Mi
set CPU_LIMIT=%7
if "%CPU_LIMIT%"=="" set CPU_LIMIT=1000m
set MEMORY_LIMIT=%8
if "%MEMORY_LIMIT%"=="" set MEMORY_LIMIT=1Gi
set POD_LIMIT=%9
if "%POD_LIMIT%"=="" set POD_LIMIT=20
if "%PROJECT_ID%"=="" set PROJECT_ID=novafuse-marketplace

REM Colors for output
set RED=[91m
set GREEN=[92m
set YELLOW=[93m
set NC=[0m

REM Check if tenant ID is provided
if "%TENANT_ID%"=="" (
  echo %RED%Error: Tenant ID is required.%NC%
  echo Usage: %0 ^<tenant_id^> [tenant_name] [tier] [replica_count] [cpu_request] [memory_request] [cpu_limit] [memory_limit] [pod_limit]
  exit /b 1
)

REM Generate random secrets
for /f "delims=" %%a in ('powershell -Command "[System.BitConverter]::ToString([System.Security.Cryptography.RandomNumberGenerator]::Create().GetBytes(16)).Replace('-', '').ToLower()"') do set API_KEY=%%a
for /f "delims=" %%a in ('powershell -Command "[System.BitConverter]::ToString([System.Security.Cryptography.RandomNumberGenerator]::Create().GetBytes(32)).Replace('-', '').ToLower()"') do set JWT_SECRET=%%a
for /f "delims=" %%a in ('powershell -Command "[System.BitConverter]::ToString([System.Security.Cryptography.RandomNumberGenerator]::Create().GetBytes(16)).Replace('-', '').ToLower()"') do set CSRF_SECRET=%%a

REM Create namespace from template
echo %YELLOW%Creating namespace for tenant %TENANT_ID%...%NC%
powershell -Command "(Get-Content k8s\templates\namespace.yaml) -replace '\${TENANT_ID}', '%TENANT_ID%' | kubectl apply -f -"

REM Create resource quota from template
echo %YELLOW%Creating resource quota for tenant %TENANT_ID%...%NC%
powershell -Command "(Get-Content k8s\templates\resource-quota.yaml) -replace '\${TENANT_ID}', '%TENANT_ID%' -replace '\${CPU_REQUEST}', '%CPU_REQUEST%' -replace '\${MEMORY_REQUEST}', '%MEMORY_REQUEST%' -replace '\${CPU_LIMIT}', '%CPU_LIMIT%' -replace '\${MEMORY_LIMIT}', '%MEMORY_LIMIT%' -replace '\${POD_LIMIT}', '%POD_LIMIT%' | kubectl apply -f -"

REM Create network policy from template
echo %YELLOW%Creating network policy for tenant %TENANT_ID%...%NC%
powershell -Command "(Get-Content k8s\templates\network-policy.yaml) -replace '\${TENANT_ID}', '%TENANT_ID%' | kubectl apply -f -"

REM Create tenant-specific values file
echo %YELLOW%Creating tenant-specific values file...%NC%
set VALUES_FILE=marketplace\chart\values-tenant-%TENANT_ID%.yaml
powershell -Command "(Get-Content marketplace\chart\values-tenant-template.yaml) -replace '\${TENANT_ID}', '%TENANT_ID%' -replace '\${TENANT_NAME}', '%TENANT_NAME%' -replace '\${TIER}', '%TIER%' -replace '\${REPLICA_COUNT}', '%REPLICA_COUNT%' -replace '\${CPU_REQUEST}', '%CPU_REQUEST%' -replace '\${MEMORY_REQUEST}', '%MEMORY_REQUEST%' -replace '\${CPU_LIMIT}', '%CPU_LIMIT%' -replace '\${MEMORY_LIMIT}', '%MEMORY_LIMIT%' -replace '\${PROJECT_ID}', '%PROJECT_ID%' -replace '\${API_KEY}', '%API_KEY%' -replace '\${JWT_SECRET}', '%JWT_SECRET%' -replace '\${CSRF_SECRET}', '%CSRF_SECRET%' | Set-Content %VALUES_FILE%"

REM Create tenant-specific service account
echo %YELLOW%Creating service account for tenant %TENANT_ID%...%NC%
gcloud iam service-accounts create tenant-%TENANT_ID% --display-name="NovaConnect UAC Tenant %TENANT_ID% Service Account"

REM Grant permissions to service account
echo %YELLOW%Granting permissions to service account...%NC%
gcloud projects add-iam-policy-binding %PROJECT_ID% --member="serviceAccount:tenant-%TENANT_ID%@%PROJECT_ID%.iam.gserviceaccount.com" --role="roles/monitoring.metricWriter"
gcloud projects add-iam-policy-binding %PROJECT_ID% --member="serviceAccount:tenant-%TENANT_ID%@%PROJECT_ID%.iam.gserviceaccount.com" --role="roles/logging.logWriter"

REM Create tenant-specific encryption key
echo %YELLOW%Creating encryption key for tenant %TENANT_ID%...%NC%
gcloud kms keyrings create tenant-%TENANT_ID%-keyring --location=global
gcloud kms keys create tenant-%TENANT_ID%-key --location=global --keyring=tenant-%TENANT_ID%-keyring --purpose=encryption

REM Grant key access to service account
gcloud kms keys add-iam-policy-binding tenant-%TENANT_ID%-key --location=global --keyring=tenant-%TENANT_ID%-keyring --member="serviceAccount:tenant-%TENANT_ID%@%PROJECT_ID%.iam.gserviceaccount.com" --role="roles/cloudkms.cryptoKeyEncrypterDecrypter"

REM Create tenant-specific BigQuery dataset
echo %YELLOW%Creating BigQuery dataset for tenant %TENANT_ID%...%NC%
bq mk --dataset --description "NovaConnect UAC Data for Tenant %TENANT_ID%" %PROJECT_ID%:tenant_%TENANT_ID%

REM Create tenant-specific monitoring dashboard
echo %YELLOW%Creating monitoring dashboard for tenant %TENANT_ID%...%NC%
if not exist monitoring\dashboards mkdir monitoring\dashboards
powershell -Command "(Get-Content monitoring\templates\tenant-dashboard.json) -replace '\${TENANT_ID}', '%TENANT_ID%' | Set-Content monitoring\dashboards\tenant-%TENANT_ID%-dashboard.json"

REM Deploy tenant-specific instance
echo %YELLOW%Deploying NovaConnect UAC for tenant %TENANT_ID%...%NC%
helm upgrade --install tenant-%TENANT_ID% .\marketplace\chart --namespace tenant-%TENANT_ID% --values %VALUES_FILE%

echo %GREEN%Tenant %TENANT_ID% provisioned successfully!%NC%
echo %GREEN%Dashboard URL: https://console.cloud.google.com/monitoring/dashboards/custom/tenant-%TENANT_ID%?project=%PROJECT_ID%%NC%
echo %GREEN%API URL: https://tenant-%TENANT_ID%.novafuse.io%NC%

exit /b 0
