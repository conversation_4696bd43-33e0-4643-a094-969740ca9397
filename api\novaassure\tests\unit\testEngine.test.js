/**
 * Test Engine Service Tests
 * 
 * This file contains unit tests for the test engine service.
 */

const testEngine = require('../../services/testEngine');
const evidenceService = require('../../services/evidenceService');
const { TestExecution, TestPlan, Control } = require('../../models');

// Mock dependencies
jest.mock('../../services/evidenceService');
jest.mock('../../models');
jest.mock('../../utils/logger', () => ({
  info: jest.fn(),
  error: jest.fn(),
  warn: jest.fn()
}));

describe('Test Engine Service', () => {
  beforeEach(() => {
    // Clear all mocks
    jest.clearAllMocks();
  });
  
  describe('runAutomatedTest', () => {
    it('should create a test execution and start running tests', async () => {
      // Mock data
      const testPlanId = 'test-plan-id';
      const testPlan = {
        _id: testPlanId,
        name: 'Test Plan',
        controls: ['control-1', 'control-2']
      };
      const testExecution = {
        _id: 'test-execution-id',
        testPlan: testPlanId,
        status: 'in-progress'
      };
      
      // Mock functions
      TestPlan.findById = jest.fn().mockReturnValue({
        populate: jest.fn().mockResolvedValue(testPlan)
      });
      TestExecution.create = jest.fn().mockResolvedValue(testExecution);
      
      // Call function
      const result = await testEngine.runAutomatedTest(testPlanId);
      
      // Assertions
      expect(TestPlan.findById).toHaveBeenCalledWith(testPlanId);
      expect(TestExecution.create).toHaveBeenCalledWith({
        testPlan: testPlanId,
        status: 'in-progress',
        executedBy: 'system',
        startedAt: expect.any(Date),
        notes: 'Automated test execution',
        metadata: { parameters: {} }
      });
      expect(result).toEqual(testExecution);
    });
    
    it('should throw an error if test plan is not found', async () => {
      // Mock data
      const testPlanId = 'test-plan-id';
      
      // Mock functions
      TestPlan.findById = jest.fn().mockReturnValue({
        populate: jest.fn().mockResolvedValue(null)
      });
      
      // Call function and assertions
      await expect(testEngine.runAutomatedTest(testPlanId)).rejects.toThrow('Test plan not found');
      expect(TestPlan.findById).toHaveBeenCalledWith(testPlanId);
      expect(TestExecution.create).not.toHaveBeenCalled();
    });
  });
  
  describe('runTests', () => {
    it('should process each control and update test execution', async () => {
      // Mock data
      const testExecution = {
        _id: 'test-execution-id',
        testPlan: 'test-plan-id',
        status: 'in-progress'
      };
      const testPlan = {
        _id: 'test-plan-id',
        name: 'Test Plan',
        controls: ['control-1', 'control-2']
      };
      const control1 = {
        _id: 'control-1',
        name: 'Control 1',
        category: 'access-control'
      };
      const control2 = {
        _id: 'control-2',
        name: 'Control 2',
        category: 'data-protection'
      };
      const testResult1 = {
        status: 'pass',
        notes: 'Test passed',
        evidenceIds: ['evidence-1']
      };
      const testResult2 = {
        status: 'pass',
        notes: 'Test passed',
        evidenceIds: ['evidence-2']
      };
      
      // Mock functions
      Control.findById = jest.fn()
        .mockResolvedValueOnce(control1)
        .mockResolvedValueOnce(control2);
      TestExecution.findByIdAndUpdate = jest.fn().mockResolvedValue({});
      
      // Mock implementation of runTestByType
      const runTestByTypeSpy = jest.spyOn(testEngine, 'runTestByType')
        .mockImplementation(() => {});
      runTestByTypeSpy
        .mockResolvedValueOnce(testResult1)
        .mockResolvedValueOnce(testResult2);
      
      // Call function
      await testEngine.runTests(testExecution, testPlan, {});
      
      // Assertions
      expect(Control.findById).toHaveBeenCalledTimes(2);
      expect(Control.findById).toHaveBeenCalledWith('control-1');
      expect(Control.findById).toHaveBeenCalledWith('control-2');
      expect(runTestByTypeSpy).toHaveBeenCalledTimes(2);
      expect(runTestByTypeSpy).toHaveBeenCalledWith('access-control', control1, {});
      expect(runTestByTypeSpy).toHaveBeenCalledWith('data-protection', control2, {});
      expect(TestExecution.findByIdAndUpdate).toHaveBeenCalledWith(
        'test-execution-id',
        {
          status: 'completed',
          results: [
            {
              control: 'control-1',
              status: 'pass',
              notes: 'Test passed',
              evidence: ['evidence-1'],
              executedBy: 'system',
              executedAt: expect.any(Date)
            },
            {
              control: 'control-2',
              status: 'pass',
              notes: 'Test passed',
              evidence: ['evidence-2'],
              executedBy: 'system',
              executedAt: expect.any(Date)
            }
          ],
          completedAt: expect.any(Date)
        }
      );
    });
    
    it('should handle errors during test execution', async () => {
      // Mock data
      const testExecution = {
        _id: 'test-execution-id',
        testPlan: 'test-plan-id',
        status: 'in-progress'
      };
      const testPlan = {
        _id: 'test-plan-id',
        name: 'Test Plan',
        controls: ['control-1']
      };
      
      // Mock functions
      Control.findById = jest.fn().mockRejectedValue(new Error('Test error'));
      TestExecution.findByIdAndUpdate = jest.fn().mockResolvedValue({});
      
      // Call function
      await testEngine.runTests(testExecution, testPlan, {});
      
      // Assertions
      expect(Control.findById).toHaveBeenCalledWith('control-1');
      expect(TestExecution.findByIdAndUpdate).toHaveBeenCalledWith(
        'test-execution-id',
        {
          status: 'failed',
          notes: 'Execution error: Test error',
          completedAt: expect.any(Date)
        }
      );
    });
  });
  
  describe('determineTestType', () => {
    it('should determine test type based on control category', () => {
      // Test cases
      const testCases = [
        { category: 'access-control', expected: 'access-control' },
        { category: 'data-protection', expected: 'data-protection' },
        { category: 'network-security', expected: 'network-security' },
        { category: 'logging-monitoring', expected: 'logging-monitoring' },
        { category: 'incident-response', expected: 'incident-response' },
        { category: 'business-continuity', expected: 'business-continuity' },
        { category: 'compliance', expected: 'compliance' },
        { category: 'unknown', expected: 'generic' }
      ];
      
      // Run test cases
      testCases.forEach(({ category, expected }) => {
        const control = { category };
        const result = testEngine.determineTestType(control);
        expect(result).toBe(expected);
      });
    });
  });
  
  describe('runTestByType', () => {
    it('should run the appropriate test based on test type', async () => {
      // Mock data
      const control = { _id: 'control-1', name: 'Control 1' };
      const parameters = { targetSystem: 'local' };
      
      // Mock implementations
      const runAccessControlTestSpy = jest.spyOn(testEngine, 'runAccessControlTest')
        .mockResolvedValue({ status: 'pass', notes: 'Access control test passed', evidenceIds: ['evidence-1'] });
      const runDataProtectionTestSpy = jest.spyOn(testEngine, 'runDataProtectionTest')
        .mockResolvedValue({ status: 'pass', notes: 'Data protection test passed', evidenceIds: ['evidence-2'] });
      const runNetworkSecurityTestSpy = jest.spyOn(testEngine, 'runNetworkSecurityTest')
        .mockResolvedValue({ status: 'pass', notes: 'Network security test passed', evidenceIds: ['evidence-3'] });
      const runLoggingMonitoringTestSpy = jest.spyOn(testEngine, 'runLoggingMonitoringTest')
        .mockResolvedValue({ status: 'pass', notes: 'Logging and monitoring test passed', evidenceIds: ['evidence-4'] });
      const runIncidentResponseTestSpy = jest.spyOn(testEngine, 'runIncidentResponseTest')
        .mockResolvedValue({ status: 'pass', notes: 'Incident response test passed', evidenceIds: ['evidence-5'] });
      const runBusinessContinuityTestSpy = jest.spyOn(testEngine, 'runBusinessContinuityTest')
        .mockResolvedValue({ status: 'pass', notes: 'Business continuity test passed', evidenceIds: ['evidence-6'] });
      const runComplianceTestSpy = jest.spyOn(testEngine, 'runComplianceTest')
        .mockResolvedValue({ status: 'pass', notes: 'Compliance test passed', evidenceIds: ['evidence-7'] });
      const runGenericTestSpy = jest.spyOn(testEngine, 'runGenericTest')
        .mockResolvedValue({ status: 'pass', notes: 'Generic test passed', evidenceIds: ['evidence-8'] });
      
      // Test cases
      const testCases = [
        { type: 'access-control', spy: runAccessControlTestSpy, expected: { status: 'pass', notes: 'Access control test passed', evidenceIds: ['evidence-1'] } },
        { type: 'data-protection', spy: runDataProtectionTestSpy, expected: { status: 'pass', notes: 'Data protection test passed', evidenceIds: ['evidence-2'] } },
        { type: 'network-security', spy: runNetworkSecurityTestSpy, expected: { status: 'pass', notes: 'Network security test passed', evidenceIds: ['evidence-3'] } },
        { type: 'logging-monitoring', spy: runLoggingMonitoringTestSpy, expected: { status: 'pass', notes: 'Logging and monitoring test passed', evidenceIds: ['evidence-4'] } },
        { type: 'incident-response', spy: runIncidentResponseTestSpy, expected: { status: 'pass', notes: 'Incident response test passed', evidenceIds: ['evidence-5'] } },
        { type: 'business-continuity', spy: runBusinessContinuityTestSpy, expected: { status: 'pass', notes: 'Business continuity test passed', evidenceIds: ['evidence-6'] } },
        { type: 'compliance', spy: runComplianceTestSpy, expected: { status: 'pass', notes: 'Compliance test passed', evidenceIds: ['evidence-7'] } },
        { type: 'generic', spy: runGenericTestSpy, expected: { status: 'pass', notes: 'Generic test passed', evidenceIds: ['evidence-8'] } }
      ];
      
      // Run test cases
      for (const { type, spy, expected } of testCases) {
        const result = await testEngine.runTestByType(type, control, parameters);
        expect(result).toEqual(expected);
        expect(spy).toHaveBeenCalledWith(control, parameters);
      }
    });
  });
});
