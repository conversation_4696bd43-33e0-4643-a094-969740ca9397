/**
 * Trinity CSDE with 18/82 Principle Demo
 *
 * This script demonstrates the Trinity CSDE implementation with the 18/82 principle applied.
 * It shows:
 * 1. The Father (Governance) component with 18/82 principle
 * 2. The Son (Detection) component with 18/82 principle
 * 3. The Spirit (Response) component with 18/82 principle
 * 4. The complete Trinity CSDE formula with 18/82 principle
 */

const { TrinityCSDE1882Engine } = require('../src/csde');

// Create a logger
const logger = {
  info: (message) => console.log(`[INFO] ${message}`),
  error: (message) => console.log(`[ERROR] ${message}`),
  warn: (message) => console.log(`[WARN] ${message}`),
  debug: (message) => console.log(`[DEBUG] ${message}`)
};

// Sample data
const governanceData = {
  complianceScore: 0.85,
  auditFrequency: 4,
  policies: [
    { id: 'POL-001', name: 'Access Control Policy', effectiveness: 0.9 },
    { id: 'POL-002', name: 'Data Protection Policy', effectiveness: 0.8 },
    { id: 'POL-003', name: 'Incident Response Policy', effectiveness: 0.85 },
    { id: 'POL-004', name: 'Risk Management Policy', effectiveness: 0.75 },
    { id: 'POL-005', name: 'Governance Policy', effectiveness: 0.9 }
  ]
};

const detectionData = {
  detectionCapability: 0.75,
  threatSeverity: 0.8,
  threatConfidence: 0.7,
  baselineSignals: 0.65,
  threats: {
    malware: 0.9,
    phishing: 0.8,
    ddos: 0.7,
    insider: 0.6
  }
};

const responseData = {
  baseResponseTime: 50,
  threatSurface: 175,
  systemRadius: 150,
  reactionTime: 0.8,
  mitigationSurface: 0.7,
  threats: {
    malware: 0.9,
    phishing: 0.8,
    ddos: 0.7,
    insider: 0.6
  }
};

/**
 * Demo Father component with 18/82 principle
 * @param {TrinityCSDE1882Engine} csdeEngine - Trinity CSDE Engine with 18/82 principle
 */
function demoFatherComponent(csdeEngine) {
  logger.info('Demo 1: Father Component (Governance) with 18/82 Principle');

  try {
    // Process Father component
    const result = csdeEngine.fatherComponent(governanceData);

    // Print results
    logger.info(`Policy Design (18%): ${result.policyDesign.toFixed(4)}`);
    logger.info(`Compliance Enforcement (82%): ${result.complianceEnforcement.toFixed(4)}`);
    logger.info(`Governance Score: ${result.governanceScore.toFixed(4)}`);
    logger.info(`Father Component Result (πG): ${result.result.toFixed(4)}`);

    // Verify 18/82 principle
    const expectedScore = (
      0.18 * result.policyDesign +
      0.82 * result.complianceEnforcement
    );
    logger.info(`Expected Score: ${expectedScore.toFixed(4)}`);
    logger.info(`Actual Score: ${result.governanceScore.toFixed(4)}`);
    logger.info(`Difference: ${Math.abs(result.governanceScore - expectedScore).toFixed(6)}`);
  } catch (error) {
    logger.error(`Error in Father component demo: ${error.message}`);
  }
}

/**
 * Demo Son component with 18/82 principle
 * @param {TrinityCSDE1882Engine} csdeEngine - Trinity CSDE Engine with 18/82 principle
 */
function demoSonComponent(csdeEngine) {
  logger.info('Demo 2: Son Component (Detection) with 18/82 Principle');

  try {
    // Process Son component
    const result = csdeEngine.sonComponent(detectionData);

    // Print results
    logger.info(`Baseline Signals (18%): ${result.baselineSignals.toFixed(4)}`);
    logger.info(`Threat Weight (82%): ${result.threatWeight.toFixed(4)}`);
    logger.info(`Detection Score: ${result.detectionScore.toFixed(4)}`);
    logger.info(`Son Component Result (ϕD): ${result.result.toFixed(4)}`);

    // Verify 18/82 principle
    const expectedScore = (
      0.18 * result.baselineSignals +
      0.82 * result.threatWeight
    );
    logger.info(`Expected Score: ${expectedScore.toFixed(4)}`);
    logger.info(`Actual Score: ${result.detectionScore.toFixed(4)}`);
    logger.info(`Difference: ${Math.abs(result.detectionScore - expectedScore).toFixed(6)}`);
  } catch (error) {
    logger.error(`Error in Son component demo: ${error.message}`);
  }
}

/**
 * Demo Spirit component with 18/82 principle
 * @param {TrinityCSDE1882Engine} csdeEngine - Trinity CSDE Engine with 18/82 principle
 */
function demoSpiritComponent(csdeEngine) {
  logger.info('Demo 3: Spirit Component (Response) with 18/82 Principle');

  try {
    // Process Spirit component
    const result = csdeEngine.spiritComponent(responseData);

    // Print results
    logger.info(`Reaction Time (18%): ${result.reactionTime.toFixed(4)}`);
    logger.info(`Mitigation Surface (82%): ${result.mitigationSurface.toFixed(4)}`);
    logger.info(`Response Score: ${result.responseScore.toFixed(4)}`);
    logger.info(`Spirit Component Result ((ℏ + c^-1)R): ${result.result.toFixed(4)}`);

    // Verify 18/82 principle
    const expectedScore = (
      0.18 * result.reactionTime +
      0.82 * result.mitigationSurface
    );
    logger.info(`Expected Score: ${expectedScore.toFixed(4)}`);
    logger.info(`Actual Score: ${result.responseScore.toFixed(4)}`);
    logger.info(`Difference: ${Math.abs(result.responseScore - expectedScore).toFixed(6)}`);
  } catch (error) {
    logger.error(`Error in Spirit component demo: ${error.message}`);
  }
}

/**
 * Demo Trinity CSDE with 18/82 principle
 * @param {TrinityCSDE1882Engine} csdeEngine - Trinity CSDE Engine with 18/82 principle
 */
function demoTrinityCSDE(csdeEngine) {
  logger.info('Demo 4: Trinity CSDE with 18/82 Principle');

  try {
    // Calculate Trinity CSDE
    const result = csdeEngine.calculateTrinityCSDE(governanceData, detectionData, responseData);

    // Print results
    logger.info(`Father Component (πG): ${result.fatherComponent.result.toFixed(4)}`);
    logger.info(`Son Component (ϕD): ${result.sonComponent.result.toFixed(4)}`);
    logger.info(`Spirit Component ((ℏ + c^-1)R): ${result.spiritComponent.result.toFixed(4)}`);
    logger.info(`Trinity CSDE Value: ${result.csdeTrinity.toFixed(4)}`);
    logger.info(`Performance Factor: ${result.performanceFactor}x`);

    // Compare with original Trinity CSDE (without 18/82 principle)
    // This is just a placeholder - in a real demo, you would compare with actual values
    logger.info('\nComparison with Original Trinity CSDE:');
    logger.info('Original Father Component (πG): 10.6814');
    logger.info('Original Son Component (ϕD): 1.0458');
    logger.info('Original Spirit Component ((ℏ + c^-1)R): 1.8280');
    logger.info('Original Trinity CSDE Value: 13.5553');
    logger.info(`18/82 Trinity CSDE Value: ${result.csdeTrinity.toFixed(4)}`);
    logger.info(`Difference: ${(result.csdeTrinity - 13.5553).toFixed(4)}`);
  } catch (error) {
    logger.error(`Error in Trinity CSDE demo: ${error.message}`);
  }
}

/**
 * Main function
 */
function main() {
  logger.info('Trinity CSDE with 18/82 Principle Demo');

  try {
    // Initialize Trinity CSDE Engine with 18/82 principle
    const csdeEngine = new TrinityCSDE1882Engine({
      enableMetrics: true,
      enableCaching: true
    });

    // Demo 1: Father Component with 18/82 Principle
    demoFatherComponent(csdeEngine);

    // Demo 2: Son Component with 18/82 Principle
    demoSonComponent(csdeEngine);

    // Demo 3: Spirit Component with 18/82 Principle
    demoSpiritComponent(csdeEngine);

    // Demo 4: Trinity CSDE with 18/82 Principle
    demoTrinityCSDE(csdeEngine);

    logger.info('Trinity CSDE with 18/82 Principle Demo completed successfully');
  } catch (error) {
    logger.error(`Error running Trinity CSDE with 18/82 Principle Demo: ${error.message}`);
  }
}

// Run the demo
main();
