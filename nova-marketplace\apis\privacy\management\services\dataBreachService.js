/**
 * Data Breach Service
 * 
 * This service handles business logic for data breaches.
 */

const { DataBreach } = require('../models');
const { logger } = require('../utils/logger');

/**
 * Get all data breaches with pagination and filtering
 * 
 * @param {Object} options - Query options
 * @param {number} options.page - Page number
 * @param {number} options.limit - Items per page
 * @param {Object} options.filter - Filter criteria
 * @param {Object} options.sort - Sort criteria
 * @returns {Promise<Object>} - Paginated results
 */
async function getAllDataBreaches(options) {
  try {
    const { page = 1, limit = 10, filter = {}, sort = { detectionDate: -1 } } = options;
    const skip = (page - 1) * limit;
    
    // Execute query with pagination
    const dataBreaches = await DataBreach
      .find(filter)
      .sort(sort)
      .skip(skip)
      .limit(limit);
    
    // Get total count for pagination
    const total = await DataBreach.countDocuments(filter);
    
    return {
      data: dataBreaches,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit)
      }
    };
  } catch (error) {
    logger.error(`Error getting data breaches: ${error.message}`);
    throw error;
  }
}

/**
 * Get a data breach by ID
 * 
 * @param {string} id - Data breach ID
 * @returns {Promise<Object>} - Data breach
 */
async function getDataBreachById(id) {
  try {
    const dataBreach = await DataBreach.findById(id);
    
    if (!dataBreach) {
      const error = new Error('Data breach not found');
      error.name = 'NotFoundError';
      throw error;
    }
    
    return dataBreach;
  } catch (error) {
    logger.error(`Error getting data breach by ID: ${error.message}`);
    throw error;
  }
}

/**
 * Create a new data breach
 * 
 * @param {Object} breachData - Data breach data
 * @returns {Promise<Object>} - Created data breach
 */
async function createDataBreach(breachData) {
  try {
    const dataBreach = new DataBreach(breachData);
    await dataBreach.save();
    
    logger.info(`Created new data breach with ID: ${dataBreach._id}`);
    return dataBreach;
  } catch (error) {
    logger.error(`Error creating data breach: ${error.message}`);
    throw error;
  }
}

/**
 * Update a data breach
 * 
 * @param {string} id - Data breach ID
 * @param {Object} updateData - Data to update
 * @returns {Promise<Object>} - Updated data breach
 */
async function updateDataBreach(id, updateData) {
  try {
    const dataBreach = await getDataBreachById(id);
    
    // Update status-related dates
    if (updateData.status && updateData.status !== dataBreach.status) {
      if (updateData.status === 'contained' && !updateData.containmentDate) {
        updateData.containmentDate = new Date();
      } else if (updateData.status === 'remediated' && !updateData.remediationDate) {
        updateData.remediationDate = new Date();
      } else if (updateData.status === 'resolved' && !updateData.resolutionDate) {
        updateData.resolutionDate = new Date();
      }
    }
    
    // Update notification-related dates
    if (updateData.supervisoryAuthorityNotified === true && 
        dataBreach.supervisoryAuthorityNotified === false && 
        !updateData.supervisoryAuthorityNotificationDate) {
      updateData.supervisoryAuthorityNotificationDate = new Date();
    }
    
    if (updateData.dataSubjectsNotified === true && 
        dataBreach.dataSubjectsNotified === false && 
        !updateData.dataSubjectNotificationDate) {
      updateData.dataSubjectNotificationDate = new Date();
    }
    
    // Update only the fields that are provided in the update data
    Object.keys(updateData).forEach(key => {
      dataBreach[key] = updateData[key];
    });
    
    await dataBreach.save();
    
    logger.info(`Updated data breach with ID: ${id}`);
    return dataBreach;
  } catch (error) {
    logger.error(`Error updating data breach: ${error.message}`);
    throw error;
  }
}

/**
 * Delete a data breach
 * 
 * @param {string} id - Data breach ID
 * @returns {Promise<Object>} - Deleted data breach
 */
async function deleteDataBreach(id) {
  try {
    const dataBreach = await getDataBreachById(id);
    
    await dataBreach.remove();
    
    logger.info(`Deleted data breach with ID: ${id}`);
    return dataBreach;
  } catch (error) {
    logger.error(`Error deleting data breach: ${error.message}`);
    throw error;
  }
}

/**
 * Send notifications for a data breach
 * 
 * @param {string} id - Data breach ID
 * @param {Object} notificationData - Notification data
 * @returns {Promise<Object>} - Notification result
 */
async function sendBreachNotifications(id, notificationData) {
  try {
    const dataBreach = await getDataBreachById(id);
    
    const { notificationType, notificationMethod, notificationContent } = notificationData;
    
    if (!notificationType || !notificationMethod || !notificationContent) {
      const error = new Error('Notification type, method, and content are required');
      error.name = 'ValidationError';
      throw error;
    }
    
    // In a real implementation, this would send notifications to the appropriate recipients
    // For now, we'll just update the data breach record
    
    if (notificationType === 'supervisory-authority') {
      dataBreach.supervisoryAuthorityNotified = true;
      dataBreach.supervisoryAuthorityNotificationDate = new Date();
    } else if (notificationType === 'data-subjects') {
      dataBreach.dataSubjectsNotified = true;
      dataBreach.dataSubjectNotificationDate = new Date();
      dataBreach.dataSubjectNotificationMethod = notificationMethod;
      dataBreach.dataSubjectNotificationContent = notificationContent;
    }
    
    await dataBreach.save();
    
    logger.info(`Sent ${notificationType} notifications for data breach with ID: ${id}`);
    
    return {
      id: dataBreach._id,
      notificationType,
      notificationMethod,
      notificationTimestamp: new Date()
    };
  } catch (error) {
    logger.error(`Error sending breach notifications: ${error.message}`);
    throw error;
  }
}

/**
 * Generate a breach report
 * 
 * @param {string} id - Data breach ID
 * @returns {Promise<Object>} - Breach report
 */
async function generateBreachReport(id) {
  try {
    const dataBreach = await getDataBreachById(id);
    
    // In a real implementation, this would generate a comprehensive report
    // For now, we'll just return a structured version of the data breach
    
    const report = {
      id: dataBreach._id,
      title: dataBreach.title,
      description: dataBreach.description,
      timeline: {
        breachDate: dataBreach.breachDate,
        detectionDate: dataBreach.detectionDate,
        containmentDate: dataBreach.containmentDate,
        remediationDate: dataBreach.remediationDate,
        resolutionDate: dataBreach.resolutionDate
      },
      impact: {
        severity: dataBreach.severity,
        affectedDataCategories: dataBreach.affectedDataCategories,
        affectedDataSubjects: dataBreach.affectedDataSubjects,
        approximateSubjectsCount: dataBreach.approximateSubjectsCount,
        potentialImpact: dataBreach.potentialImpact
      },
      response: {
        containmentMeasures: dataBreach.containmentMeasures,
        remediationMeasures: dataBreach.remediationMeasures,
        preventativeMeasures: dataBreach.preventativeMeasures
      },
      notifications: {
        supervisoryAuthority: {
          required: dataBreach.notificationRequired,
          notified: dataBreach.supervisoryAuthorityNotified,
          notificationDate: dataBreach.supervisoryAuthorityNotificationDate,
          reference: dataBreach.supervisoryAuthorityReference
        },
        dataSubjects: {
          notified: dataBreach.dataSubjectsNotified,
          notificationDate: dataBreach.dataSubjectNotificationDate,
          notificationMethod: dataBreach.dataSubjectNotificationMethod
        }
      },
      affectedSystems: dataBreach.affectedSystems,
      involvedParties: dataBreach.involvedParties,
      currentStatus: dataBreach.status,
      generatedAt: new Date()
    };
    
    logger.info(`Generated report for data breach with ID: ${id}`);
    return report;
  } catch (error) {
    logger.error(`Error generating breach report: ${error.message}`);
    throw error;
  }
}

/**
 * Assess notification requirements for a data breach
 * 
 * @param {string} id - Data breach ID
 * @returns {Promise<Object>} - Assessment result
 */
async function assessNotificationRequirements(id) {
  try {
    const dataBreach = await getDataBreachById(id);
    
    // In a real implementation, this would perform a detailed assessment
    // For now, we'll use a simple rule-based approach
    
    const highRiskCategories = ['Financial Data', 'Health Data', 'Biometric Data', 'Location Data'];
    const containsHighRiskData = dataBreach.affectedDataCategories.some(category => 
      highRiskCategories.includes(category)
    );
    
    const largeScale = dataBreach.approximateSubjectsCount > 1000;
    
    // Determine if notification to supervisory authority is required
    const authorityNotificationRequired = 
      dataBreach.severity === 'high' || 
      dataBreach.severity === 'critical' || 
      containsHighRiskData;
    
    // Determine if notification to data subjects is required
    const dataSubjectNotificationRequired = 
      (dataBreach.severity === 'critical' || 
      (dataBreach.severity === 'high' && containsHighRiskData)) && 
      largeScale;
    
    // Determine notification threshold
    let notificationThreshold = 'not-required';
    if (authorityNotificationRequired) {
      notificationThreshold = dataBreach.severity === 'critical' ? '72-hours' : 'without-undue-delay';
    }
    
    // Create assessment object
    const assessment = {
      id: dataBreach._id,
      authorityNotification: {
        required: authorityNotificationRequired,
        threshold: notificationThreshold,
        reasoning: authorityNotificationRequired
          ? `Notification required due to ${dataBreach.severity} severity${containsHighRiskData ? ' and high-risk data categories' : ''}`
          : 'Notification not required based on current assessment',
        status: dataBreach.supervisoryAuthorityNotified ? 'completed' : 'pending'
      },
      dataSubjectNotification: {
        required: dataSubjectNotificationRequired,
        reasoning: dataSubjectNotificationRequired
          ? `Notification required due to high severity and sensitive data categories`
          : 'Notification not required based on current assessment',
        status: dataBreach.dataSubjectsNotified ? 'completed' : 'pending'
      },
      factors: {
        severity: dataBreach.severity,
        containsHighRiskData,
        highRiskCategories: dataBreach.affectedDataCategories.filter(category => 
          highRiskCategories.includes(category)
        ),
        largeScale,
        approximateSubjectsCount: dataBreach.approximateSubjectsCount
      },
      assessedAt: new Date()
    };
    
    logger.info(`Assessed notification requirements for data breach with ID: ${id}`);
    return assessment;
  } catch (error) {
    logger.error(`Error assessing notification requirements: ${error.message}`);
    throw error;
  }
}

module.exports = {
  getAllDataBreaches,
  getDataBreachById,
  createDataBreach,
  updateDataBreach,
  deleteDataBreach,
  sendBreachNotifications,
  generateBreachReport,
  assessNotificationRequirements
};
