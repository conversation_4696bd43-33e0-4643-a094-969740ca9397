{"version": 3, "names": ["winston", "require", "format", "LOG_LEVEL", "process", "env", "customFormat", "combine", "timestamp", "errors", "stack", "splat", "json", "consoleTransport", "transports", "<PERSON><PERSON><PERSON>", "colorize", "printf", "level", "message", "module", "meta", "moduleStr", "metaStr", "Object", "keys", "length", "JSON", "stringify", "defaultLogger", "createLogger", "defaultMeta", "service", "NODE_ENV", "add", "File", "filename", "maxsize", "maxFiles", "child", "exports"], "sources": ["logger.js"], "sourcesContent": ["/**\n * NovaFuse Universal API Connector - Logger\n * \n * This module provides logging functionality for the UAC.\n */\n\nconst winston = require('winston');\nconst { format } = winston;\n\n// Get log level from environment\nconst LOG_LEVEL = process.env.LOG_LEVEL || 'info';\n\n// Create custom format\nconst customFormat = format.combine(\n  format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss.SSS' }),\n  format.errors({ stack: true }),\n  format.splat(),\n  format.json()\n);\n\n// Create console transport\nconst consoleTransport = new winston.transports.Console({\n  format: format.combine(\n    format.colorize(),\n    format.printf(({ timestamp, level, message, module, ...meta }) => {\n      const moduleStr = module ? `[${module}] ` : '';\n      const metaStr = Object.keys(meta).length ? `\\n${JSON.stringify(meta, null, 2)}` : '';\n      return `${timestamp} ${level}: ${moduleStr}${message}${metaStr}`;\n    })\n  )\n});\n\n// Create default logger\nconst defaultLogger = winston.createLogger({\n  level: LOG_LEVEL,\n  format: customFormat,\n  defaultMeta: { service: 'nova-connect' },\n  transports: [consoleTransport]\n});\n\n// Add file transport in production\nif (process.env.NODE_ENV === 'production') {\n  defaultLogger.add(new winston.transports.File({ \n    filename: 'logs/error.log', \n    level: 'error',\n    maxsize: 10485760, // 10MB\n    maxFiles: 10\n  }));\n  \n  defaultLogger.add(new winston.transports.File({ \n    filename: 'logs/combined.log',\n    maxsize: 10485760, // 10MB\n    maxFiles: 10\n  }));\n}\n\n/**\n * Create a logger for a specific module\n * @param {string} module - Module name\n * @returns {winston.Logger} Logger instance\n */\nfunction createLogger(module) {\n  return defaultLogger.child({ module });\n}\n\nmodule.exports = {\n  createLogger,\n  defaultLogger\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,MAAMA,OAAO,GAAGC,OAAO,CAAC,SAAS,CAAC;AAClC,MAAM;EAAEC;AAAO,CAAC,GAAGF,OAAO;;AAE1B;AACA,MAAMG,SAAS,GAAGC,OAAO,CAACC,GAAG,CAACF,SAAS,IAAI,MAAM;;AAEjD;AACA,MAAMG,YAAY,GAAGJ,MAAM,CAACK,OAAO,CACjCL,MAAM,CAACM,SAAS,CAAC;EAAEN,MAAM,EAAE;AAA0B,CAAC,CAAC,EACvDA,MAAM,CAACO,MAAM,CAAC;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC,EAC9BR,MAAM,CAACS,KAAK,CAAC,CAAC,EACdT,MAAM,CAACU,IAAI,CAAC,CACd,CAAC;;AAED;AACA,MAAMC,gBAAgB,GAAG,IAAIb,OAAO,CAACc,UAAU,CAACC,OAAO,CAAC;EACtDb,MAAM,EAAEA,MAAM,CAACK,OAAO,CACpBL,MAAM,CAACc,QAAQ,CAAC,CAAC,EACjBd,MAAM,CAACe,MAAM,CAAC,CAAC;IAAET,SAAS;IAAEU,KAAK;IAAEC,OAAO;IAAEC,MAAM;IAAE,GAAGC;EAAK,CAAC,KAAK;IAChE,MAAMC,SAAS,GAAGF,MAAM,GAAG,IAAIA,MAAM,IAAI,GAAG,EAAE;IAC9C,MAAMG,OAAO,GAAGC,MAAM,CAACC,IAAI,CAACJ,IAAI,CAAC,CAACK,MAAM,GAAG,KAAKC,IAAI,CAACC,SAAS,CAACP,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE;IACpF,OAAO,GAAGb,SAAS,IAAIU,KAAK,KAAKI,SAAS,GAAGH,OAAO,GAAGI,OAAO,EAAE;EAClE,CAAC,CACH;AACF,CAAC,CAAC;;AAEF;AACA,MAAMM,aAAa,GAAG7B,OAAO,CAAC8B,YAAY,CAAC;EACzCZ,KAAK,EAAEf,SAAS;EAChBD,MAAM,EAAEI,YAAY;EACpByB,WAAW,EAAE;IAAEC,OAAO,EAAE;EAAe,CAAC;EACxClB,UAAU,EAAE,CAACD,gBAAgB;AAC/B,CAAC,CAAC;;AAEF;AACA,IAAIT,OAAO,CAACC,GAAG,CAAC4B,QAAQ,KAAK,YAAY,EAAE;EACzCJ,aAAa,CAACK,GAAG,CAAC,IAAIlC,OAAO,CAACc,UAAU,CAACqB,IAAI,CAAC;IAC5CC,QAAQ,EAAE,gBAAgB;IAC1BlB,KAAK,EAAE,OAAO;IACdmB,OAAO,EAAE,QAAQ;IAAE;IACnBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC;EAEHT,aAAa,CAACK,GAAG,CAAC,IAAIlC,OAAO,CAACc,UAAU,CAACqB,IAAI,CAAC;IAC5CC,QAAQ,EAAE,mBAAmB;IAC7BC,OAAO,EAAE,QAAQ;IAAE;IACnBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASR,YAAYA,CAACV,MAAM,EAAE;EAC5B,OAAOS,aAAa,CAACU,KAAK,CAAC;IAAEnB;EAAO,CAAC,CAAC;AACxC;AAEAA,MAAM,CAACoB,OAAO,GAAG;EACfV,YAAY;EACZD;AACF,CAAC", "ignoreList": []}