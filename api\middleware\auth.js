const jwt = require('jsonwebtoken');
const { User } = require('../models');

/**
 * Middleware to authenticate JWT tokens
 */
const authenticateJWT = (req, res, next) => {
  const authHeader = req.headers.authorization;

  if (!authHeader) {
    return res.status(401).json({ error: true, message: 'Authorization header missing' });
  }

  const token = authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: true, message: 'Token missing' });
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    req.user = decoded;
    next();
  } catch (error) {
    return res.status(403).json({ error: true, message: 'Invalid or expired token' });
  }
};

/**
 * Middleware to authorize admin users
 */
const authorizeAdmin = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({ error: true, message: 'User not authenticated' });
  }

  if (req.user.role !== 'admin') {
    return res.status(403).json({ error: true, message: 'Admin access required' });
  }

  next();
};

/**
 * Middleware to authorize partner users
 */
const authorizePartner = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({ error: true, message: 'User not authenticated' });
  }

  if (req.user.role !== 'partner' && req.user.role !== 'admin') {
    return res.status(403).json({ error: true, message: 'Partner access required' });
  }

  next();
};

/**
 * Middleware to check if user is the owner of a resource
 * @param {Function} getResourceOwner Function that returns the owner ID of the resource
 */
const authorizeOwner = (getResourceOwner) => {
  return async (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({ error: true, message: 'User not authenticated' });
    }

    try {
      const ownerId = await getResourceOwner(req);
      
      if (req.user.id !== ownerId && req.user.role !== 'admin') {
        return res.status(403).json({ error: true, message: 'You do not have permission to access this resource' });
      }
      
      next();
    } catch (error) {
      next(error);
    }
  };
};

module.exports = {
  authenticateJWT,
  authorizeAdmin,
  authorizePartner,
  authorizeOwner
};
