# 🔌 NovaBrowser API Reference

## 🎯 **NovaAgent Backend API**

**Base URL**: `http://localhost:8090`  
**Protocol**: HTTP/1.1 + WebSocket  
**Authentication**: None (local development)  
**Content-Type**: `application/json`

---

## 📊 **Core Endpoints**

### **GET /status**
Returns current NovaAgent status and coherence metrics.

**Request**:
```bash
curl -X GET http://localhost:8090/status \
  -H "Content-Type: application/json"
```

**Response**:
```json
{
  "status": "coherent",
  "version": "1.0.0",
  "coherence": 0.625,
  "uptime": "2h 15m 30s",
  "psi_snap": false,
  "timestamp": "2025-06-11T12:45:30Z",
  "metrics": {
    "structural_coherence": 0.85,
    "functional_alignment": 0.72,
    "relational_integrity": 0.91
  }
}
```

**Performance**: 5-55ms response time  
**Usage**: Health checks, coherence monitoring

---

### **GET /health**
Simple health check endpoint for monitoring.

**Request**:
```bash
curl -X GET http://localhost:8090/health
```

**Response**:
```json
{
  "status": "healthy",
  "service": "nova-agent",
  "timestamp": "2025-06-11T12:45:30Z"
}
```

**Performance**: <10ms response time  
**Usage**: Load balancer health checks

---

### **GET /coherence**
Detailed coherence analysis with breakdown.

**Request**:
```bash
curl -X GET http://localhost:8090/coherence
```

**Response**:
```json
{
  "overall_coherence": 0.87,
  "psi_snap_active": true,
  "threshold": 0.82,
  "metrics": {
    "structural_coherence": 1.0,
    "functional_alignment": 0.88,
    "relational_integrity": 0.91
  },
  "analysis_time_ms": 8,
  "timestamp": "2025-06-11T12:45:30Z",
  "recommendations": [
    "Maintain current coherence levels",
    "Monitor relational integrity"
  ]
}
```

**Performance**: 8-21ms analysis time  
**Usage**: Real-time coherence monitoring

---

### **POST /command**
Execute commands on the NovaAgent system.

**Request**:
```bash
curl -X POST http://localhost:8090/command \
  -H "Content-Type: application/json" \
  -d '{
    "type": "scan_vendor",
    "payload": {
      "target": "accessibility",
      "depth": "full"
    }
  }'
```

**Response**:
```json
{
  "status": "success",
  "command": "scan_vendor",
  "execution_time_ms": 45,
  "result": {
    "violations_found": 3,
    "fixes_applied": 2,
    "coherence_improvement": 0.15
  },
  "timestamp": "2025-06-11T12:45:30Z"
}
```

**Available Commands**:
- `scan_vendor` - Vendor analysis
- `analyze_page` - Page coherence analysis
- `auto_fix` - Automatic violation remediation
- `reset_metrics` - Reset coherence metrics

---

## 🔄 **WebSocket API**

### **Connection**
```javascript
const ws = new WebSocket('ws://localhost:8090/ws');
```

### **Real-time Messages**
```javascript
// Incoming message format
{
  "type": "coherence_update",
  "data": {
    "coherence": 0.87,
    "psi_snap": true,
    "timestamp": "2025-06-11T12:45:30Z"
  }
}

// Outgoing message format
{
  "type": "request_analysis",
  "payload": {
    "url": "https://example.com",
    "depth": "full"
  }
}
```

**Message Types**:
- `coherence_update` - Real-time coherence changes
- `violation_detected` - Accessibility violations found
- `threat_alert` - Security threats identified
- `analysis_complete` - Full analysis finished

**Performance**: <20ms WebSocket latency

---

## 🌐 **Chrome Extension API**

### **Content Script Messages**

#### **Analysis Request**
```javascript
// Send to content script
chrome.tabs.sendMessage(tabId, {
  type: 'REQUEST_ANALYSIS'
});

// Response from content script
{
  type: 'ANALYSIS_COMPLETE',
  data: {
    coherence: { overall: 87, structural: 100, functional: 88, relational: 91 },
    accessibility: { score: 100, violations: [] },
    security: { level: 'LOW', threats: 0 },
    performance: { analysisTime: 8 }
  },
  url: 'https://example.com',
  timestamp: '2025-06-11T12:45:30Z'
}
```

#### **Auto-fix Request**
```javascript
// Trigger auto-fix
chrome.tabs.sendMessage(tabId, {
  type: 'AUTO_FIX'
});

// Response
{
  type: 'AUTO_FIX_COMPLETE',
  fixes_applied: 4,
  violations_remaining: 0,
  execution_time_ms: 2
}
```

#### **Overlay Control**
```javascript
// Toggle overlay visibility
chrome.tabs.sendMessage(tabId, {
  type: 'TOGGLE_OVERLAY'
});
```

### **Background Script API**

#### **Storage Operations**
```javascript
// Store analysis data
chrome.storage.local.set({
  [`analysis_${tabId}`]: {
    coherence: 87,
    accessibility: 100,
    timestamp: Date.now()
  }
});

// Retrieve analysis data
chrome.storage.local.get([`analysis_${tabId}`], (result) => {
  const analysis = result[`analysis_${tabId}`];
});
```

#### **Badge Updates**
```javascript
// Update extension badge
chrome.action.setBadgeText({ text: '87%', tabId: tabId });
chrome.action.setBadgeBackgroundColor({ color: '#00ff96', tabId: tabId });
```

#### **Notifications**
```javascript
// Show coherence notification
chrome.notifications.create({
  type: 'basic',
  iconUrl: 'icon48.png',
  title: '⚠️ Low Coherence Detected',
  message: 'example.com has 45% coherence (below 82% Ψ-Snap threshold)'
});
```

---

## 🔧 **Proxy Server API**

### **URL Proxying**
```bash
# Proxy any URL through iframe-bypass server
curl "http://localhost:3001/proxy?url=https://google.com"
```

**Headers Removed**:
- `X-Frame-Options`
- `Content-Security-Policy`
- `Content-Security-Policy-Report-Only`

**Headers Added**:
- `X-Frame-Options: ALLOWALL`
- `Access-Control-Allow-Origin: *`

### **Health Check**
```bash
curl http://localhost:3001/health
```

**Response**:
```json
{
  "status": "ok",
  "message": "NovaBrowser proxy server running"
}
```

---

## 📊 **Error Handling**

### **HTTP Error Codes**
- `200` - Success
- `400` - Bad Request (invalid parameters)
- `404` - Endpoint not found
- `500` - Internal server error
- `503` - Service unavailable

### **Error Response Format**
```json
{
  "error": true,
  "code": 400,
  "message": "Invalid command type",
  "details": "Command 'invalid_command' not recognized",
  "timestamp": "2025-06-11T12:45:30Z"
}
```

### **WebSocket Error Handling**
```javascript
ws.onerror = (error) => {
  console.error('WebSocket error:', error);
  // Implement reconnection logic
};

ws.onclose = (event) => {
  if (event.code !== 1000) {
    // Unexpected close, attempt reconnection
    setTimeout(() => connectWebSocket(), 5000);
  }
};
```

---

## 🚀 **Performance Optimization**

### **Caching Strategy**
```javascript
// Cache coherence results for 30 seconds
const cacheKey = `coherence_${url_hash}`;
const cached = localStorage.getItem(cacheKey);
if (cached && Date.now() - cached.timestamp < 30000) {
  return cached.data;
}
```

### **Batch Operations**
```javascript
// Batch multiple analysis requests
const batchRequest = {
  type: 'batch_analysis',
  requests: [
    { type: 'coherence', url: 'https://example1.com' },
    { type: 'accessibility', url: 'https://example2.com' }
  ]
};
```

### **Rate Limiting**
- **Backend**: 100 requests/minute per client
- **Extension**: 10 analysis/second per tab
- **WebSocket**: 50 messages/second per connection

---

## 🔐 **Security Considerations**

### **CORS Configuration**
```javascript
// Backend CORS headers
Access-Control-Allow-Origin: *
Access-Control-Allow-Methods: GET, POST, OPTIONS
Access-Control-Allow-Headers: Content-Type
```

### **Input Validation**
```javascript
// Command validation
if (!['scan_vendor', 'analyze_page', 'auto_fix'].includes(command.type)) {
  return { error: 'Invalid command type' };
}
```

### **Extension Permissions**
```json
{
  "permissions": ["activeTab", "storage", "scripting"],
  "host_permissions": ["<all_urls>", "http://localhost:8090/*"]
}
```

---

## 📈 **Monitoring & Analytics**

### **Performance Metrics**
```javascript
// Track API performance
const startTime = performance.now();
const response = await fetch('/status');
const responseTime = performance.now() - startTime;

// Log metrics
console.log(`API response time: ${responseTime}ms`);
```

### **Usage Analytics**
```javascript
// Track feature usage
chrome.runtime.sendMessage({
  type: 'LOG_EVENT',
  event: 'auto_fix_used',
  data: { violations_fixed: 4, execution_time: 2 }
});
```

---

**This API reference covers all documented endpoints and integration points for the NovaBrowser system.**
