@echo off
echo 🌐 Building NovaBrowser - Coherence-First Web Gateway
echo 🚀 NovaFuse Coherence Operating System
echo.

echo 📦 Installing dependencies...
cargo install wasm-pack

echo.
echo 🔧 Building WASM module...
wasm-pack build --target web --out-dir pkg

echo.
echo 🏗️ Building standalone browser...
cargo build --release --features standalone

echo.
echo 📋 Creating distribution...
if not exist "dist" mkdir dist
copy src\browser_integration.js dist\
copy pkg\* dist\
copy demo.html dist\
copy target\release\novabrowser-standalone.exe dist\

echo.
echo ✅ NovaBrowser build complete!
echo.
echo 🚀 Available modes:
echo    1. WASM Integration: Open dist\demo.html in any browser
echo    2. Standalone App: Run dist\novabrowser-standalone.exe
echo    3. Web Extension: Load dist\ as unpacked extension
echo.
echo 🧬 Features enabled:
echo    • NovaDNA coherence analysis
echo    • NovaVision UI compliance
echo    • NovaShield threat detection
echo    • 82/18 Comphyological filtering
echo.
pause
