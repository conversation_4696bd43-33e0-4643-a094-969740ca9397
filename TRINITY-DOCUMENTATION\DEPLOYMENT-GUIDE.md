# Trinity of Trust - Deployment Guide

## 🚀 **Deployment Overview**

This guide covers deploying the Trinity of Trust platform across different environments, from local development to enterprise production on Google Cloud Platform.

## 🏠 **Local Development Deployment**

### **Prerequisites**
- Docker Desktop 4.0+
- Node.js 18+
- Git
- 8GB+ RAM
- 20GB+ available disk space

### **Quick Start**
```bash
# Clone the repository
git clone https://github.com/novafuse/trinity-of-trust.git
cd trinity-of-trust

# Start local development environment
docker-compose up -d

# Verify deployment
curl http://localhost:8080/health
```

### **Development Services**
- **KetherNet Blockchain**: `http://localhost:8080`
- **NovaDNA Identity**: `http://localhost:8083`
- **NovaShield Security**: `http://localhost:8085`
- **Trinity Gateway**: `http://localhost:80`
- **PostgreSQL**: `localhost:5432`
- **Redis**: `localhost:6379`

### **Running Tests**
```bash
# Run complete test suite
npm test

# Run specific component tests
npm run test:kethernet
npm run test:novadna
npm run test:novashield

# Run integration tests
npm run test:integration
```

## 🐳 **Docker Simulation Deployment**

### **GCP Simulation Environment**
Complete Google Cloud Platform simulation using Docker containers.

```bash
cd docker-trinity-simulation

# Start GCP simulation
docker-compose -f docker-compose-gcp-sim.yml up -d

# Run GCP integration tests
docker-compose -f docker-compose-gcp-sim.yml --profile testing up trinity-gcp-test-runner

# Monitor services
docker-compose -f docker-compose-gcp-sim.yml ps
```

### **Simulated GCP Services**
- **Cloud SQL (PostgreSQL)**: `localhost:5432`
- **Memorystore (Redis)**: `localhost:6379`
- **Cloud Storage (MinIO)**: `localhost:9000`
- **Cloud Monitoring (Prometheus)**: `localhost:9090`
- **Cloud Logging (Grafana)**: `localhost:3000`
- **Load Balancer (Nginx)**: `localhost:80`

### **Simulation Validation**
```bash
# Check all services are healthy
./scripts/check-simulation-health.sh

# Run performance tests
./scripts/run-performance-tests.sh

# Generate test report
./scripts/generate-test-report.sh
```

## ☁️ **Google Cloud Platform Deployment**

### **Prerequisites**
- Google Cloud Platform account with billing enabled
- `gcloud` CLI installed and authenticated
- `kubectl` installed
- `terraform` installed (>= 1.0)
- Project with required APIs enabled

### **Infrastructure Deployment**

#### **1. Setup GCP Project**
```bash
# Set project variables
export PROJECT_ID="trinity-consciousness-prod"
export REGION="us-central1"
export CLUSTER_NAME="trinity-consciousness-cluster"

# Set active project
gcloud config set project $PROJECT_ID

# Enable required APIs
gcloud services enable \
  compute.googleapis.com \
  container.googleapis.com \
  cloudsql.googleapis.com \
  redis.googleapis.com \
  monitoring.googleapis.com \
  logging.googleapis.com \
  secretmanager.googleapis.com
```

#### **2. Deploy Infrastructure with Terraform**
```bash
cd gcp-trinity-deployment/terraform

# Initialize Terraform
terraform init

# Review deployment plan
terraform plan -var="project_id=$PROJECT_ID" -var="region=$REGION"

# Deploy infrastructure
terraform apply -var="project_id=$PROJECT_ID" -var="region=$REGION"
```

#### **3. Configure Kubernetes**
```bash
# Get cluster credentials
gcloud container clusters get-credentials $CLUSTER_NAME \
  --region $REGION --project $PROJECT_ID

# Verify cluster connection
kubectl cluster-info

# Create namespaces
kubectl apply -f kubernetes/namespaces.yaml
```

### **Application Deployment**

#### **1. Build and Push Container Images**
```bash
# Configure Docker for GCR
gcloud auth configure-docker

# Build Trinity images
docker build -t gcr.io/$PROJECT_ID/kethernet:latest \
  -f docker/Dockerfile.kethernet .

docker build -t gcr.io/$PROJECT_ID/novadna:latest \
  -f docker/Dockerfile.novadna .

docker build -t gcr.io/$PROJECT_ID/novashield:latest \
  -f docker/Dockerfile.novashield .

# Push images to GCR
docker push gcr.io/$PROJECT_ID/kethernet:latest
docker push gcr.io/$PROJECT_ID/novadna:latest
docker push gcr.io/$PROJECT_ID/novashield:latest
```

#### **2. Deploy Trinity Components**
```bash
# Deploy KetherNet Blockchain
kubectl apply -f kubernetes/kethernet-deployment.yaml

# Deploy NovaDNA Identity Fabric
kubectl apply -f kubernetes/novadna-deployment.yaml

# Deploy NovaShield Security Platform
kubectl apply -f kubernetes/novashield-deployment.yaml

# Deploy Trinity Gateway
kubectl apply -f kubernetes/trinity-gateway.yaml
```

#### **3. Configure Secrets**
```bash
# Create database password
echo -n "$(openssl rand -base64 32)" | \
  gcloud secrets create trinity-database-password --data-file=-

# Create JWT secret
echo -n "$(openssl rand -base64 64)" | \
  gcloud secrets create trinity-jwt-secret --data-file=-

# Create encryption key
echo -n "$(openssl rand -base64 32)" | \
  gcloud secrets create trinity-encryption-key --data-file=-

# Create consciousness salt
echo -n "$(openssl rand -base64 32)" | \
  gcloud secrets create trinity-consciousness-salt --data-file=-
```

### **Verification and Testing**

#### **1. Health Checks**
```bash
# Check pod status
kubectl get pods --all-namespaces -l trinity-component

# Check service endpoints
kubectl get services --all-namespaces -l trinity-component

# Test external access
EXTERNAL_IP=$(kubectl get service trinity-gateway \
  -o jsonpath='{.status.loadBalancer.ingress[0].ip}')

curl https://$EXTERNAL_IP/health
```

#### **2. Integration Testing**
```bash
# Run production integration tests
kubectl apply -f kubernetes/trinity-integration-test.yaml

# Wait for test completion
kubectl wait --for=condition=complete job/trinity-integration-test \
  --timeout=600s

# View test results
kubectl logs job/trinity-integration-test
```

## 🏢 **Enterprise Production Deployment**

### **High Availability Configuration**

#### **Multi-Region Deployment**
```yaml
# terraform/variables.tf
variable "regions" {
  default = ["us-central1", "us-east1", "europe-west1"]
}

variable "zones_per_region" {
  default = 3
}
```

#### **Database High Availability**
```yaml
# Cloud SQL with read replicas
resource "google_sql_database_instance" "trinity_postgres_replica" {
  count = 2
  name = "trinity-postgres-replica-${count.index}"
  master_instance_name = google_sql_database_instance.trinity_postgres.name
  region = var.replica_regions[count.index]
  
  replica_configuration {
    failover_target = true
  }
}
```

#### **Auto-Scaling Configuration**
```yaml
# kubernetes/hpa.yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: trinity-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: trinity-deployment
  minReplicas: 10
  maxReplicas: 100
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Pods
    pods:
      metric:
        name: consciousness_validations_per_second
      target:
        type: AverageValue
        averageValue: "1000"
```

### **Security Hardening**

#### **Network Security**
```yaml
# kubernetes/network-policy.yaml
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: trinity-network-policy
spec:
  podSelector:
    matchLabels:
      app: trinity
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: trinity-system
    ports:
    - protocol: TCP
      port: 8080
  egress:
  - to: []
    ports:
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 5432
```

#### **Pod Security Standards**
```yaml
# kubernetes/pod-security.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: trinity-production
  labels:
    pod-security.kubernetes.io/enforce: restricted
    pod-security.kubernetes.io/audit: restricted
    pod-security.kubernetes.io/warn: restricted
```

### **Monitoring and Observability**

#### **Prometheus Configuration**
```yaml
# monitoring/prometheus.yaml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "trinity-alerts.yml"

scrape_configs:
  - job_name: 'trinity-kethernet'
    kubernetes_sd_configs:
    - role: pod
    relabel_configs:
    - source_labels: [__meta_kubernetes_pod_label_app]
      action: keep
      regex: kethernet
```

#### **Grafana Dashboards**
```json
{
  "dashboard": {
    "title": "Trinity of Trust - Production Dashboard",
    "panels": [
      {
        "title": "Consciousness Validations/sec",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(trinity_consciousness_validations_total[5m])"
          }
        ]
      },
      {
        "title": "Security Threats Detected",
        "type": "stat",
        "targets": [
          {
            "expr": "trinity_security_threats_total"
          }
        ]
      }
    ]
  }
}
```

### **Backup and Disaster Recovery**

#### **Database Backup**
```bash
# Automated backup script
#!/bin/bash
BACKUP_NAME="trinity-backup-$(date +%Y%m%d-%H%M%S)"

gcloud sql backups create \
  --instance=trinity-postgres \
  --description="Automated Trinity backup" \
  --project=$PROJECT_ID

# Cross-region backup replication
gsutil cp gs://trinity-backups/* gs://trinity-backups-dr/
```

#### **Disaster Recovery Plan**
```yaml
# dr-plan.yaml
recovery_objectives:
  rto: "15 minutes"  # Recovery Time Objective
  rpo: "5 minutes"   # Recovery Point Objective

procedures:
  - name: "Database Failover"
    steps:
      - "Promote read replica to master"
      - "Update application configuration"
      - "Restart application pods"
  
  - name: "Cross-Region Failover"
    steps:
      - "Activate standby region"
      - "Update DNS routing"
      - "Restore from backup if needed"
```

## 🔧 **Configuration Management**

### **Environment-Specific Configuration**

#### **Development**
```yaml
# config/development.yaml
trinity:
  consciousness:
    threshold: 2000  # Lower threshold for testing
    validation_timeout: 500ms
  
  logging:
    level: debug
    
  security:
    strict_mode: false
```

#### **Production**
```yaml
# config/production.yaml
trinity:
  consciousness:
    threshold: 2847  # Full production threshold
    validation_timeout: 100ms
  
  logging:
    level: info
    
  security:
    strict_mode: true
    audit_all_requests: true
```

### **Secret Management**
```bash
# Using Google Secret Manager
gcloud secrets create trinity-config \
  --data-file=config/production.yaml

# Mount secrets in Kubernetes
kubectl create secret generic trinity-secrets \
  --from-literal=database-url="$(gcloud secrets versions access latest --secret=trinity-database-url)"
```

## 📊 **Performance Tuning**

### **Database Optimization**
```sql
-- PostgreSQL consciousness optimization
CREATE INDEX CONCURRENTLY idx_consciousness_uuft 
ON consciousness_validations (uuft_score DESC, timestamp DESC);

CREATE INDEX CONCURRENTLY idx_identity_consciousness 
ON identities USING GIN (consciousness_fingerprint);

-- Partitioning for large tables
CREATE TABLE consciousness_validations_y2024m01 
PARTITION OF consciousness_validations 
FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');
```

### **Application Optimization**
```javascript
// Connection pooling
const pool = new Pool({
  host: process.env.DB_HOST,
  port: 5432,
  database: 'trinity',
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  max: 20,  // Maximum pool size
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
});

// Consciousness validation caching
const consciousnessCache = new Redis({
  host: process.env.REDIS_HOST,
  port: 6379,
  retryDelayOnFailover: 100,
  maxRetriesPerRequest: 3,
});
```

## 🚨 **Troubleshooting**

### **Common Issues**

#### **Consciousness Validation Timeouts**
```bash
# Check consciousness validator pods
kubectl logs -l app=consciousness-validator

# Scale up validators
kubectl scale deployment consciousness-validator --replicas=10

# Check resource usage
kubectl top pods -l app=consciousness-validator
```

#### **Database Connection Issues**
```bash
# Check Cloud SQL proxy
kubectl logs -l app=cloudsql-proxy

# Test database connectivity
kubectl exec -it trinity-pod -- psql -h localhost -U trinity_user -d trinity_db -c "SELECT 1;"
```

#### **Security Analysis Delays**
```bash
# Check NovaShield performance
kubectl logs -l app=novashield | grep "analysis_time"

# Monitor threat detection queue
kubectl exec -it redis-pod -- redis-cli llen threat_analysis_queue
```

### **Performance Monitoring**
```bash
# Real-time metrics
kubectl top nodes
kubectl top pods --all-namespaces

# Consciousness validation metrics
curl http://prometheus:9090/api/v1/query?query=trinity_consciousness_validations_per_second

# Security threat metrics
curl http://prometheus:9090/api/v1/query?query=trinity_security_threats_detected_total
```

This deployment guide provides comprehensive instructions for deploying Trinity of Trust across all environments, from development to enterprise production, with proper security, monitoring, and disaster recovery configurations.
