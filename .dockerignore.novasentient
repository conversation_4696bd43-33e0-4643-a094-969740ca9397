# NovaSentient Docker ignore file
# Exclude unnecessary files from Docker build context

# Git
.git
.gitignore

# Node.js (not needed for Python container)
node_modules
npm-debug.log
package*.json

# Python cache
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.mypy_cache
.pytest_cache
.hypothesis

# Documentation
*.md
docs/
README*

# IDE
.vscode
.idea
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Archives
*.zip
*.tar.gz
*.tgz

# Exclude everything except what we need
*
!src/
!test_novapi.py
!Dockerfile.novasentient
