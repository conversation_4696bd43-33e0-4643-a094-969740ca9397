const express = require('express');
const app = express();
app.use(express.json());
app.get('/health', (req, res) => res.json({ status: 'ok' }));
app.post('/token', (req, res) => res.json({ access_token: 'simulation-token', expires_in: 3600 }));
app.get('/v1/projects/:projectId/roles', (req, res) => res.json({ roles: [{ name: 'roles/viewer' }, { name: 'roles/editor' }] }));
app.post('/v1/projects/:projectId:getIamPolicy', (req, res) => res.json({ bindings: [{ role: 'roles/viewer', members: ['user:<EMAIL>'] }] }));
app.listen(8082, () => console.log('Cloud IAM Simulator running on port 8082'));
