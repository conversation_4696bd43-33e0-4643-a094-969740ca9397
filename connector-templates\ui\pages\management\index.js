/**
 * Connector Management Page
 * 
 * This page provides tools for managing API connectors.
 */

import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Button, 
  Container, 
  Dialog, 
  DialogActions, 
  DialogContent, 
  DialogContentText, 
  DialogTitle, 
  Typography 
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import DashboardLayout from '../../layouts/DashboardLayout';
import ConnectorList from '../../components/management/ConnectorList';
import { useRouter } from 'next/router';

const ManagementPage = () => {
  const router = useRouter();
  const [connectors, setConnectors] = useState([]);
  const [loading, setLoading] = useState(true);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [connectorToDelete, setConnectorToDelete] = useState(null);
  
  useEffect(() => {
    // In a real implementation, this would fetch connectors from the API
    const fetchConnectors = async () => {
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Mock data
        const mockConnectors = [
          {
            id: 'conn1',
            name: 'GitHub API Connector',
            description: 'Connect to GitHub API for repository management',
            category: 'development',
            version: '1.0.0',
            status: 'active',
            created: '2023-01-15T12:00:00Z',
            updated: '2023-03-20T14:30:00Z',
            baseUrl: 'https://api.github.com'
          },
          {
            id: 'conn2',
            name: 'Jira API Connector',
            description: 'Connect to Jira for issue tracking and project management',
            category: 'development',
            version: '1.2.0',
            status: 'active',
            created: '2023-02-10T09:15:00Z',
            updated: '2023-04-05T11:45:00Z',
            baseUrl: 'https://your-domain.atlassian.net/rest/api/3'
          },
          {
            id: 'conn3',
            name: 'Salesforce API Connector',
            description: 'Connect to Salesforce CRM',
            category: 'sales',
            version: '2.0.1',
            status: 'active',
            created: '2023-01-05T10:30:00Z',
            updated: '2023-05-12T16:20:00Z',
            baseUrl: 'https://your-instance.salesforce.com/services/data/v56.0'
          },
          {
            id: 'conn4',
            name: 'Google Analytics API Connector',
            description: 'Connect to Google Analytics for website analytics',
            category: 'analytics',
            version: '0.9.0',
            status: 'draft',
            created: '2023-04-20T08:45:00Z',
            updated: '2023-04-20T08:45:00Z',
            baseUrl: 'https://analyticsdata.googleapis.com/v1beta'
          },
          {
            id: 'conn5',
            name: 'Stripe API Connector',
            description: 'Connect to Stripe for payment processing',
            category: 'finance',
            version: '1.5.0',
            status: 'active',
            created: '2023-03-01T14:00:00Z',
            updated: '2023-05-18T09:30:00Z',
            baseUrl: 'https://api.stripe.com/v1'
          },
          {
            id: 'conn6',
            name: 'AWS S3 Connector',
            description: 'Connect to Amazon S3 for file storage',
            category: 'cloud',
            version: '1.1.0',
            status: 'active',
            created: '2023-02-15T11:20:00Z',
            updated: '2023-04-10T13:15:00Z',
            baseUrl: 'https://s3.amazonaws.com'
          },
          {
            id: 'conn7',
            name: 'Slack API Connector',
            description: 'Connect to Slack for messaging and notifications',
            category: 'communication',
            version: '0.8.5',
            status: 'draft',
            created: '2023-05-01T15:30:00Z',
            updated: '2023-05-15T10:45:00Z',
            baseUrl: 'https://slack.com/api'
          },
          {
            id: 'conn8',
            name: 'HubSpot API Connector',
            description: 'Connect to HubSpot for marketing and CRM',
            category: 'marketing',
            version: '1.0.0',
            status: 'deprecated',
            created: '2022-11-10T09:00:00Z',
            updated: '2023-01-20T14:00:00Z',
            baseUrl: 'https://api.hubapi.com'
          }
        ];
        
        setConnectors(mockConnectors);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching connectors:', error);
        setLoading(false);
      }
    };
    
    fetchConnectors();
  }, []);
  
  const handleCreateConnector = () => {
    router.push('/connectors/new');
  };
  
  const handleDeleteConnector = (connectorId) => {
    const connector = connectors.find(c => c.id === connectorId);
    setConnectorToDelete(connector);
    setDeleteDialogOpen(true);
  };
  
  const confirmDeleteConnector = () => {
    // In a real implementation, this would call an API to delete the connector
    setConnectors(connectors.filter(c => c.id !== connectorToDelete.id));
    setDeleteDialogOpen(false);
    setConnectorToDelete(null);
  };
  
  const handleDuplicateConnector = (connectorId) => {
    // In a real implementation, this would call an API to duplicate the connector
    const connector = connectors.find(c => c.id === connectorId);
    
    if (connector) {
      const duplicatedConnector = {
        ...connector,
        id: `${connector.id}_copy`,
        name: `${connector.name} (Copy)`,
        created: new Date().toISOString(),
        updated: new Date().toISOString(),
        status: 'draft'
      };
      
      setConnectors([...connectors, duplicatedConnector]);
    }
  };
  
  return (
    <DashboardLayout>
      <Container maxWidth="xl">
        <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h4" component="h1">
            Connector Management
          </Typography>
          
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleCreateConnector}
          >
            Create Connector
          </Button>
        </Box>
        
        <ConnectorList 
          connectors={connectors} 
          onDelete={handleDeleteConnector}
          onDuplicate={handleDuplicateConnector}
          loading={loading}
        />
        
        <Dialog
          open={deleteDialogOpen}
          onClose={() => setDeleteDialogOpen(false)}
        >
          <DialogTitle>Delete Connector</DialogTitle>
          <DialogContent>
            <DialogContentText>
              Are you sure you want to delete the connector "{connectorToDelete?.name}"? This action cannot be undone.
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={confirmDeleteConnector} color="error" autoFocus>
              Delete
            </Button>
          </DialogActions>
        </Dialog>
      </Container>
    </DashboardLayout>
  );
};

export default ManagementPage;
