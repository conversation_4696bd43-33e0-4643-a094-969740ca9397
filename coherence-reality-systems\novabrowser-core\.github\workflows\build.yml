name: test

on:
  push:
    branches:
      - dev
  pull_request:

jobs:
  test:
    strategy:
      fail-fast: false
      matrix:
        platform:
          - { target: x86_64-pc-windows-msvc, os: windows-latest }
          - { target: x86_64-unknown-linux-gnu, os: ubuntu-latest }
          - { target: aarch64-apple-darwin, os: macos-latest }
          - { target: aarch64-apple-ios, os: macos-latest }
          - { target: x86_64-apple-darwin, os: macos-latest }
          - { target: aarch64-linux-android, os: ubuntu-latest }

    runs-on: ${{ matrix.platform.os }}

    steps:
      - uses: actions/checkout@v4
      - name: install stable
        uses: dtolnay/rust-toolchain@1.77
        with:
          targets: ${{ matrix.platform.target }}

      - name: install webkit2gtk (ubuntu only)
        if: contains(matrix.platform.target, 'gnu')
        run: |
          sudo apt-get update
          sudo apt-get install -y libwebkit2gtk-4.1-dev

      - name: install webview2 (windows only)
        if: contains(matrix.platform.target, 'windows')
        shell: pwsh
        run: |
          Invoke-WebRequest https://go.microsoft.com/fwlink/p/?LinkId=2124703 -OutFile installwebview.exe -UseBasicParsing
          cmd /C start /wait installwebview.exe /silent /install

      - uses: Swatinem/rust-cache@v2

      - name: build wry
        run: cargo build --target ${{ matrix.platform.target }} --all-features

      - name: build tests and examples
        shell: bash
        if: (!contains(matrix.platform.target, 'android') && !contains(matrix.platform.target, 'ios'))
        run: cargo test --no-run --verbose --target ${{ matrix.platform.target }}

      - name: run tests
        if: (!contains(matrix.platform.target, 'android') && !contains(matrix.platform.target, 'ios'))
        run: cargo test --verbose --target ${{ matrix.platform.target }} --features linux-body

      - name: install nightly
        uses: dtolnay/rust-toolchain@nightly
        with:
          targets: ${{ matrix.platform.target }}
          components: miri

      - name: Run tests with miri
        if: (!contains(matrix.platform.target, 'android') && !contains(matrix.platform.target, 'ios'))
        run: cargo +nightly miri test --verbose --target ${{ matrix.platform.target }} --features linux-body

  doc:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: install nightly
        uses: dtolnay/rust-toolchain@nightly
      - name: install webkit2gtk
        run: |
          sudo apt-get update -y -q
          sudo apt-get install -y --no-install-recommends libwebkit2gtk-4.1-dev
      - name: Run cargo doc with each targets
        env:
          DOCS_RS: true
        run: |
          set -ex

          md="$(cargo metadata --format-version=1 | jq '.packages[] | select(.name=="wry") | .metadata.docs.rs')"

          export RUSTDOCFLAGS="$(echo "$md" | jq -r '.["rustdoc-args"] | join(" ")')"
          export RUSTFLAGS="$(echo "$md" | jq -r '.["rustc-args"] | join(" ")')"

          features="$(echo "$md" | jq -r '.features | join(",")')"
          no_default_features=
          if [ "$(echo "$md" | jq '.["no-default-features"]')" = true ]; then
            no_default_features="--no-default-features"
          fi

          for target in $(echo "$md" | jq -r '.targets | join(" ")')
          do
            rustup target add "$target"
            cargo doc -p wry "$no_default_features" "--features=$features" "--target=$target"
          done
