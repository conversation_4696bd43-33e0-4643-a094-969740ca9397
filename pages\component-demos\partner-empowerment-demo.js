import React, { useState } from 'react';
import BasicDemoTemplate from '../../components/demo-framework/BasicDemoTemplate';
import PageWithSidebar from '../../components/PageWithSidebar';
import Link from 'next/link';

export default function PartnerEmpowermentDemo() {
  // State for interactive elements
  const [dealSize, setDealSize] = useState(1000000);
  const [dealType, setDealType] = useState('subscription');
  const [dealTerm, setDealTerm] = useState(3);
  const [partnerTier, setPartnerTier] = useState('gold');
  const [yearlyDeals, setYearlyDeals] = useState(5);
  const [yearsActive, setYearsActive] = useState(3);
  const [selectedCompetitor, setSelectedCompetitor] = useState('traditional');
  const [selectedRole, setSelectedRole] = useState('all');
  const [showProtectionValue, setShowProtectionValue] = useState(false);

  // Competitor data
  const competitors = {
    traditional: { name: 'Traditional Model', partnerShare: 0.3, vendorShare: 0.7 },
    drata: { name: 'Drata', partnerShare: 0.25, vendorShare: 0.75 },
    vanta: { name: 'Vanta', partnerShare: 0.2, vendorShare: 0.8 },
    secureframe: { name: 'SecureFrame', partnerShare: 0.22, vendorShare: 0.78 }
  };

  // Calculate NovaFuse vs Competitor earnings
  const calculateEarnings = () => {
    // NovaFuse takes 18%, partner gets 82%
    const novaFuseShare = dealSize * 0.18;
    const partnerShare = dealSize * 0.82;

    // Get selected competitor data
    const competitor = competitors[selectedCompetitor];
    const competitorShare = dealSize * competitor.vendorShare;
    const competitorPartnerShare = dealSize * competitor.partnerShare;

    // Calculate difference
    const difference = partnerShare - competitorPartnerShare;
    const percentageDifference = ((partnerShare / competitorPartnerShare) - 1) * 100;

    return {
      novaFuseShare,
      partnerShare,
      competitorShare,
      competitorPartnerShare,
      difference,
      percentageDifference,
      competitorName: competitor.name
    };
  };

  // Calculate long-term earnings
  const calculateLongTermEarnings = () => {
    const annualRevenue = dealSize * yearlyDeals;

    // NovaFuse earnings over time
    const novaFuseYearlyEarnings = annualRevenue * 0.82;
    const novaFuseTotalEarnings = novaFuseYearlyEarnings * yearsActive;

    // Get selected competitor data
    const competitor = competitors[selectedCompetitor];
    const competitorYearlyEarnings = annualRevenue * competitor.partnerShare;
    const competitorTotalEarnings = competitorYearlyEarnings * yearsActive;

    // Calculate difference
    const totalDifference = novaFuseTotalEarnings - competitorTotalEarnings;

    return {
      novaFuseYearlyEarnings,
      novaFuseTotalEarnings,
      competitorYearlyEarnings,
      competitorTotalEarnings,
      totalDifference,
      competitorName: competitor.name
    };
  };

  // Calculate compliance failure costs
  const calculateComplianceFailureCosts = () => {
    // Base costs by industry
    const industries = {
      healthcare: { baseFine: 5000000, perRecordFine: 500, avgRecords: 10000 },
      financial: { baseFine: 2500000, perRecordFine: 250, avgRecords: 15000 },
      retail: { baseFine: 1000000, perRecordFine: 100, avgRecords: 25000 },
      technology: { baseFine: 1500000, perRecordFine: 200, avgRecords: 20000 }
    };

    // Default to technology if no industry selected
    const industry = industries.technology;

    // Calculate potential fines
    const regulatoryFine = industry.baseFine;
    const dataBreachCost = industry.perRecordFine * industry.avgRecords;
    const legalCosts = regulatoryFine * 0.3; // Legal costs typically 30% of regulatory fines
    const brandDamage = (regulatoryFine + dataBreachCost) * 0.5; // Brand damage typically 50% of direct costs

    // Calculate NovaFuse implementation cost (18% of traditional)
    const traditionalImplementationCost = 500000;
    const novaFuseImplementationCost = traditionalImplementationCost * 0.18;
    const implementationSavings = traditionalImplementationCost - novaFuseImplementationCost;

    // Total potential costs
    const totalPotentialCosts = regulatoryFine + dataBreachCost + legalCosts + brandDamage;

    return {
      regulatoryFine,
      dataBreachCost,
      legalCosts,
      brandDamage,
      totalPotentialCosts,
      traditionalImplementationCost,
      novaFuseImplementationCost,
      implementationSavings
    };
  };

  // Calculate equity potential
  const calculateEquityPotential = () => {
    // Base equity percentage based on partner tier
    let baseEquity = 0;
    switch (partnerTier) {
      case 'platinum':
        baseEquity = 0.5; // 0.5% base equity
        break;
      case 'gold':
        baseEquity = 0.25; // 0.25% base equity
        break;
      case 'silver':
        baseEquity = 0.1; // 0.1% base equity
        break;
      default:
        baseEquity = 0;
    }

    // Performance multiplier based on yearly deals
    let performanceMultiplier = 1;
    if (yearlyDeals >= 10) {
      performanceMultiplier = 2;
    } else if (yearlyDeals >= 5) {
      performanceMultiplier = 1.5;
    }

    // Calculate equity percentage
    const equityPercentage = baseEquity * performanceMultiplier;

    // Assuming NovaFuse valuation of $100M for equity value calculation
    const assumedValuation = 100000000;
    const equityValue = assumedValuation * (equityPercentage / 100);

    return {
      equityPercentage,
      equityValue,
      assumedValuation
    };
  };

  // Get calculated values
  const earnings = calculateEarnings();
  const longTermEarnings = calculateLongTermEarnings();
  const equityPotential = calculateEquityPotential();

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      maximumFractionDigits: 0
    }).format(amount);
  };

  // SEO metadata
  const pageProps = {
    title: '18/82 Partner Empowerment Model - NovaFuse',
    description: 'Experience NovaFuse\'s revolutionary 18/82 Partner Empowerment Model that transforms the GRC landscape with unprecedented revenue sharing.',
    keywords: 'NovaFuse, Partner Empowerment Model, 18/82 revenue sharing, partner program, GRC partnership',
    canonical: 'https://novafuse.io/component-demos/partner-empowerment-demo',
    ogImage: '/images/demos/partner-empowerment-demo-og.png'
  };

  // Role-specific content
  const roleContent = {
    all: {
      title: "18/82 Partner Empowerment Model",
      description: "Experience NovaFuse's revolutionary approach to partner revenue sharing",
      highlightMetric: "82% Revenue Share",
      highlightDescription: "Partners earn 82% of all revenue generated"
    },
    strategic: {
      title: "Strategic Alliance Leader",
      description: "Orchestrate enterprise client success with unprecedented revenue share",
      highlightMetric: "40% Less Effort",
      highlightDescription: "Manage 50+ clients with 40% less effort while earning 82% of revenue"
    },
    developer: {
      title: "Daring Developer",
      description: "Build and monetize compliance apps with industry-leading revenue share",
      highlightMetric: "3.28x Earnings",
      highlightDescription: "Earn $820K on a $1M app vs. $300K elsewhere"
    },
    guardian: {
      title: "Cross-Cloud Guardian",
      description: "Deliver multi-cloud, zero-touch compliance with maximum profit potential",
      highlightMetric: "82% Cost Reduction",
      highlightDescription: "Cut FedRAMP costs by 82% while maximizing your revenue"
    },
    catalyst: {
      title: "Innovation Catalyst",
      description: "Create hybrid product-service offerings with revolutionary economics",
      highlightMetric: "2x Deal Size",
      highlightDescription: "Double deal size with compliance-as-code while keeping 82% of revenue"
    }
  };

  // Get role-specific content
  const currentRole = roleContent[selectedRole];

  // Get compliance failure costs
  const complianceCosts = calculateComplianceFailureCosts();

  // Sidebar items
  const sidebarItems = [
    { type: 'category', label: 'Partner Empowerment', items: [
      { label: 'Overview', href: '#overview' },
      { label: 'ROI Comparison', href: '#roi-comparison' },
      { label: 'Protection Value', href: '#protection-value' },
      { label: 'Contract Builder', href: '#contract-builder' },
      { label: 'Equity Simulator', href: '#equity-simulator' },
      { label: 'Long-Term Benefits', href: '#long-term-benefits' }
    ]},
    { type: 'category', label: 'Partner Roles', items: [
      { label: 'All Roles', href: '#', onClick: () => setSelectedRole('all') },
      { label: 'Strategic Alliance Leader', href: '#', onClick: () => setSelectedRole('strategic') },
      { label: 'Daring Developer', href: '#', onClick: () => setSelectedRole('developer') },
      { label: 'Cross-Cloud Guardian', href: '#', onClick: () => setSelectedRole('guardian') },
      { label: 'Innovation Catalyst', href: '#', onClick: () => setSelectedRole('catalyst') }
    ]},
    { type: 'category', label: 'Resources', items: [
      { label: 'Partner Program', href: '/partner-program' },
      { label: 'Partner Success Stories', href: '/resources/partner-success-stories' },
      { label: 'Schedule Demo', href: '/contact?demo=partner-empowerment' }
    ]}
  ];

  return (
    <PageWithSidebar
      title={pageProps.title}
      description={pageProps.description}
      sidebarItems={sidebarItems}
    >
      <BasicDemoTemplate
        title={currentRole.title}
        description={currentRole.description}
        demoType="executive"
      >
        <div className="space-y-8">
          {/* Overview Section */}
          <div id="overview" className="bg-gray-800 rounded-lg p-6 scroll-mt-16">
            <div className="flex justify-center mb-4">
              <div className="bg-blue-800 bg-opacity-70 px-4 py-2 rounded-full text-sm font-bold inline-flex items-center">
                <span className="mr-2">💰</span>
                <span>REVOLUTIONARY REVENUE SHARING</span>
              </div>
            </div>

            <h2 className="text-2xl font-bold mb-4 text-center">Not a margin. A movement.</h2>
            <p className="text-gray-300 mb-6 text-center max-w-3xl mx-auto">
              We take 18% so you can take over your market. NovaFuse's Partner Empowerment Model flips the traditional
              vendor-partner relationship on its head. Instead of taking the lion's share, we give our partners 82% of the revenue.
            </p>

            <p className="text-blue-400 text-lg mb-8 text-center max-w-3xl mx-auto font-semibold">
              SecureFrame takes 78%. We give you 82%. The math isn't just better. It's exponential.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-gray-700 p-6 rounded-lg">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 rounded-full bg-blue-900 flex items-center justify-center mr-4">
                    <span className="text-2xl">🤝</span>
                  </div>
                  <h3 className="text-xl font-semibold">True Partnership</h3>
                </div>
                <p className="text-gray-300">
                  We succeed when you succeed. Our 18/82 model aligns our interests with yours, creating a true partnership rather than a vendor relationship.
                </p>
              </div>

              <div className="bg-gray-700 p-6 rounded-lg">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 rounded-full bg-blue-900 flex items-center justify-center mr-4">
                    <span className="text-2xl">📈</span>
                  </div>
                  <h3 className="text-xl font-semibold">Exponential Growth</h3>
                </div>
                <p className="text-gray-300">
                  With 82% of the revenue, partners can reinvest in growth, expand their teams, and scale their businesses faster than ever before.
                </p>
              </div>

              <div className="bg-gray-700 p-6 rounded-lg">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 rounded-full bg-blue-900 flex items-center justify-center mr-4">
                    <span className="text-2xl">🚀</span>
                  </div>
                  <h3 className="text-xl font-semibold">Equity Opportunities</h3>
                </div>
                <p className="text-gray-300">
                  Top-performing partners can earn equity in NovaFuse, creating long-term value beyond revenue sharing.
                </p>
              </div>
            </div>
          </div>

          {/* ROI Comparison Dashboard */}
          <div id="roi-comparison" className="bg-gray-800 rounded-lg p-6 scroll-mt-16">
            <h2 className="text-2xl font-bold mb-4">ROI Comparison Dashboard</h2>
            <p className="text-gray-300 mb-6">
              See how NovaFuse's 18/82 model compares to other vendor models.
              Adjust the deal size and select different competitors to see the difference in your earnings.
            </p>

            <div className="mb-6">
              <label className="block text-gray-300 mb-2">Deal Size</label>
              <div className="flex items-center">
                <input
                  type="range"
                  min="100000"
                  max="5000000"
                  step="100000"
                  value={dealSize}
                  onChange={(e) => setDealSize(parseInt(e.target.value))}
                  className="w-full mr-4"
                />
                <span className="text-gray-300 font-bold whitespace-nowrap">{formatCurrency(dealSize)}</span>
              </div>
            </div>

            {/* Competitor Toggle */}
            <div className="mb-6">
              <label className="block text-gray-300 mb-2">Compare With</label>
              <div className="flex flex-wrap gap-2">
                {Object.keys(competitors).map(competitor => (
                  <button
                    key={competitor}
                    className={`px-4 py-2 rounded-lg text-sm font-medium ${
                      selectedCompetitor === competitor
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                    }`}
                    onClick={() => setSelectedCompetitor(competitor)}
                  >
                    {competitors[competitor].name}
                  </button>
                ))}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-6">
              <div className="bg-blue-900 bg-opacity-30 border border-blue-500 rounded-lg p-6">
                <h3 className="text-xl font-bold mb-4 text-center">NovaFuse Model</h3>
                <div className="flex justify-between items-center mb-4">
                  <span className="text-gray-300">Partner Share (82%):</span>
                  <span className="text-green-400 font-bold text-xl">{formatCurrency(earnings.partnerShare)}</span>
                </div>
                <div className="flex justify-between items-center mb-4">
                  <span className="text-gray-300">NovaFuse Share (18%):</span>
                  <span className="text-gray-400">{formatCurrency(earnings.novaFuseShare)}</span>
                </div>
                <div className="mt-6 pt-4 border-t border-blue-800">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-300">Total Deal Value:</span>
                    <span className="text-gray-300 font-bold">{formatCurrency(dealSize)}</span>
                  </div>
                </div>
              </div>

              <div className="bg-gray-700 rounded-lg p-6">
                <h3 className="text-xl font-bold mb-4 text-center">{earnings.competitorName}</h3>
                <div className="flex justify-between items-center mb-4">
                  <span className="text-gray-300">Partner Share ({(competitors[selectedCompetitor].partnerShare * 100).toFixed(0)}%):</span>
                  <span className="text-gray-300 font-bold text-xl">{formatCurrency(earnings.competitorPartnerShare)}</span>
                </div>
                <div className="flex justify-between items-center mb-4">
                  <span className="text-gray-300">Vendor Share ({(competitors[selectedCompetitor].vendorShare * 100).toFixed(0)}%):</span>
                  <span className="text-gray-400">{formatCurrency(earnings.competitorShare)}</span>
                </div>
                <div className="mt-6 pt-4 border-t border-gray-600">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-300">Total Deal Value:</span>
                    <span className="text-gray-300 font-bold">{formatCurrency(dealSize)}</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-green-900 bg-opacity-30 border border-green-500 rounded-lg p-6 text-center">
              <h3 className="text-xl font-bold mb-2">Your Additional Earnings with NovaFuse</h3>
              <p className="text-4xl font-bold text-green-400 mb-2">{formatCurrency(earnings.difference)}</p>
              <p className="text-gray-300 mb-4">That's <span className="text-green-400 font-bold">{earnings.percentageDifference.toFixed(0)}%</span> more revenue compared to {earnings.competitorName}!</p>

              <div className="mt-6 pt-4 border-t border-green-800">
                <h4 className="font-semibold mb-3">Time-to-Profit Breakeven</h4>
                <div className="bg-gray-800 p-4 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-gray-400">NovaFuse (82%)</span>
                    <span className="text-sm text-green-400 font-bold">{Math.ceil(dealSize * 0.18 / (dealSize * 0.82 / 12))} months</span>
                  </div>
                  <div className="w-full bg-gray-700 h-2 rounded-full mb-4">
                    <div className="bg-green-500 h-2 rounded-full" style={{ width: `${Math.min(100, Math.ceil(dealSize * 0.18 / (dealSize * 0.82 / 12)) * 8)}%` }}></div>
                  </div>

                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-gray-400">{earnings.competitorName} ({(competitors[selectedCompetitor].partnerShare * 100).toFixed(0)}%)</span>
                    <span className="text-sm text-gray-400 font-bold">{Math.ceil(dealSize * competitors[selectedCompetitor].vendorShare / (dealSize * competitors[selectedCompetitor].partnerShare / 12))} months</span>
                  </div>
                  <div className="w-full bg-gray-700 h-2 rounded-full">
                    <div className="bg-blue-500 h-2 rounded-full" style={{ width: `${Math.min(100, Math.ceil(dealSize * competitors[selectedCompetitor].vendorShare / (dealSize * competitors[selectedCompetitor].partnerShare / 12)) * 8)}%` }}></div>
                  </div>

                  <p className="text-xs text-gray-500 mt-3">
                    *Breakeven calculated as time to recover implementation costs based on monthly revenue
                  </p>
                </div>
              </div>
            </div>

            {/* Role-Specific Highlight */}
            {selectedRole !== 'all' && (
              <div className="mt-6 bg-blue-900 bg-opacity-30 border border-blue-500 rounded-lg p-6">
                <h3 className="text-xl font-bold mb-4">{currentRole.highlightMetric}: {currentRole.title} Advantage</h3>
                <p className="text-gray-300">{currentRole.highlightDescription}</p>

                {selectedRole === 'developer' && (
                  <div className="mt-4 bg-gray-700 p-4 rounded-lg">
                    <h4 className="font-semibold mb-2">App Monetization Example</h4>
                    <p className="text-sm text-gray-400 mb-3">
                      As a Daring Developer, you can build and monetize compliance apps through the NovaFuse Solutions Marketplace.
                      With our 82% revenue share, a $1M app generates $820K for you versus just $300K with traditional vendors.
                    </p>
                    <div className="flex justify-between items-center text-sm">
                      <span>$1M App Revenue</span>
                      <span className="text-green-400 font-bold">$820K to You</span>
                    </div>
                  </div>
                )}

                {selectedRole === 'strategic' && (
                  <div className="mt-4 bg-gray-700 p-4 rounded-lg">
                    <h4 className="font-semibold mb-2">Enterprise Client Orchestration</h4>
                    <p className="text-sm text-gray-400 mb-3">
                      As a Strategic Alliance Leader, you can manage 50+ enterprise clients with 40% less effort while earning 82% of all revenue.
                      NovaFuse's automation and integration capabilities dramatically reduce implementation time and cost.
                    </p>
                    <div className="flex justify-between items-center text-sm">
                      <span>50 Enterprise Clients</span>
                      <span className="text-green-400 font-bold">40% Less Effort + 82% Revenue</span>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Protection Value Section */}
          <div id="protection-value" className="bg-gray-800 rounded-lg p-6 scroll-mt-16">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-2xl font-bold">Protection Value</h2>
              <button
                className="text-blue-400 hover:text-blue-300 text-sm flex items-center"
                onClick={() => setShowProtectionValue(!showProtectionValue)}
              >
                {showProtectionValue ? 'Hide Details' : 'Show Details'}
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={showProtectionValue ? "M5 15l7-7 7 7" : "M19 9l-7 7-7-7"} />
                </svg>
              </button>
            </div>

            <p className="text-gray-300 mb-6">
              Beyond revenue sharing, NovaFuse provides significant protection value by reducing compliance risks and potential penalties.
              See how much you could save by avoiding compliance failures.
            </p>

            {showProtectionValue && (
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  <div className="bg-red-900 bg-opacity-30 border border-red-700 rounded-lg p-6">
                    <h3 className="text-xl font-bold mb-4 text-center">Compliance Failure Costs</h3>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-gray-300">Regulatory Fines:</span>
                        <span className="text-red-400 font-bold">{formatCurrency(complianceCosts.regulatoryFine)}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-300">Data Breach Costs:</span>
                        <span className="text-red-400 font-bold">{formatCurrency(complianceCosts.dataBreachCost)}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-300">Legal Expenses:</span>
                        <span className="text-red-400 font-bold">{formatCurrency(complianceCosts.legalCosts)}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-300">Brand Damage:</span>
                        <span className="text-red-400 font-bold">{formatCurrency(complianceCosts.brandDamage)}</span>
                      </div>
                      <div className="pt-3 mt-3 border-t border-red-800">
                        <div className="flex justify-between items-center">
                          <span className="text-gray-300 font-semibold">Total Potential Costs:</span>
                          <span className="text-red-400 font-bold text-xl">{formatCurrency(complianceCosts.totalPotentialCosts)}</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="bg-green-900 bg-opacity-30 border border-green-700 rounded-lg p-6">
                    <h3 className="text-xl font-bold mb-4 text-center">NovaFuse Implementation Savings</h3>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-gray-300">Traditional Implementation:</span>
                        <span className="text-gray-400">{formatCurrency(complianceCosts.traditionalImplementationCost)}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-300">NovaFuse Implementation (18%):</span>
                        <span className="text-green-400 font-bold">{formatCurrency(complianceCosts.novaFuseImplementationCost)}</span>
                      </div>
                      <div className="pt-3 mt-3 border-t border-green-800">
                        <div className="flex justify-between items-center">
                          <span className="text-gray-300 font-semibold">Implementation Savings:</span>
                          <span className="text-green-400 font-bold text-xl">{formatCurrency(complianceCosts.implementationSavings)}</span>
                        </div>
                      </div>
                    </div>

                    <div className="mt-6 pt-4 border-t border-green-800">
                      <h4 className="font-semibold mb-3">Risk Mitigation Value</h4>
                      <p className="text-sm text-gray-300 mb-3">
                        By implementing NovaFuse, you significantly reduce the risk of compliance failures and associated costs.
                        The total protection value includes both implementation savings and risk mitigation.
                      </p>
                      <div className="bg-gray-800 p-3 rounded-lg">
                        <div className="flex justify-between items-center">
                          <span className="text-gray-300">Total Protection Value:</span>
                          <span className="text-green-400 font-bold text-xl">{formatCurrency(complianceCosts.implementationSavings + complianceCosts.totalPotentialCosts * 0.9)}</span>
                        </div>
                        <p className="text-xs text-gray-400 mt-2">
                          *Assumes 90% reduction in compliance failure risk
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-blue-900 bg-opacity-30 border border-blue-700 rounded-lg p-6 text-center">
                  <h3 className="text-xl font-bold mb-3">Save vs. Spend Comparison</h3>
                  <p className="text-gray-300 mb-4">
                    Invest {formatCurrency(complianceCosts.novaFuseImplementationCost)} in NovaFuse implementation vs. risk {formatCurrency(complianceCosts.totalPotentialCosts)} in potential penalties
                  </p>
                  <div className="bg-gray-800 p-4 rounded-lg inline-block">
                    <div className="text-2xl font-bold text-green-400">
                      {(complianceCosts.totalPotentialCosts / complianceCosts.novaFuseImplementationCost).toFixed(0)}x Return on Protection Investment
                    </div>
                  </div>
                </div>

                {selectedRole === 'guardian' && (
                  <div className="bg-blue-900 bg-opacity-30 border border-blue-500 rounded-lg p-6">
                    <h3 className="text-xl font-bold mb-3">Cross-Cloud Guardian: FedRAMP Cost Reduction</h3>
                    <p className="text-gray-300 mb-4">
                      As a Cross-Cloud Guardian, you can reduce FedRAMP compliance costs by 82% while maintaining the highest security standards.
                    </p>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="bg-gray-800 p-4 rounded-lg">
                        <h4 className="font-semibold mb-2">Traditional FedRAMP</h4>
                        <ul className="space-y-2 text-sm text-gray-400">
                          <li>• 12-18 month implementation</li>
                          <li>• $1M+ in consulting fees</li>
                          <li>• 300+ manual control checks</li>
                          <li>• Extensive documentation burden</li>
                        </ul>
                      </div>
                      <div className="bg-gray-800 p-4 rounded-lg">
                        <h4 className="font-semibold mb-2">NovaFuse FedRAMP</h4>
                        <ul className="space-y-2 text-sm text-gray-400">
                          <li>• 3-4 month implementation</li>
                          <li>• $180K in implementation costs</li>
                          <li>• Automated control validation</li>
                          <li>• Auto-generated documentation</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Contract Builder */}
          <div id="contract-builder" className="bg-gray-800 rounded-lg p-6 scroll-mt-16">
            <h2 className="text-2xl font-bold mb-4">Live Contract Builder</h2>
            <p className="text-gray-300 mb-6">
              Customize your partnership agreement by adjusting the parameters below. See how different deal types and terms affect your revenue.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
              <div>
                <label className="block text-gray-300 mb-2">Deal Type</label>
                <select
                  value={dealType}
                  onChange={(e) => setDealType(e.target.value)}
                  className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
                >
                  <option value="subscription">Subscription</option>
                  <option value="perpetual">Perpetual License</option>
                  <option value="services">Professional Services</option>
                </select>
              </div>

              <div>
                <label className="block text-gray-300 mb-2">Deal Term (Years)</label>
                <select
                  value={dealTerm}
                  onChange={(e) => setDealTerm(parseInt(e.target.value))}
                  className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
                >
                  <option value="1">1 Year</option>
                  <option value="2">2 Years</option>
                  <option value="3">3 Years</option>
                  <option value="5">5 Years</option>
                </select>
              </div>

              <div>
                <label className="block text-gray-300 mb-2">Partner Tier</label>
                <select
                  value={partnerTier}
                  onChange={(e) => setPartnerTier(e.target.value)}
                  className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
                >
                  <option value="silver">Silver</option>
                  <option value="gold">Gold</option>
                  <option value="platinum">Platinum</option>
                </select>
              </div>
            </div>

            <div className="bg-gray-700 rounded-lg p-6 mb-6">
              <h3 className="text-xl font-bold mb-4">Partnership Agreement Summary</h3>

              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-300">Partner:</span>
                  <span className="text-white font-bold">[Your Company Name]</span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-gray-300">Partner Tier:</span>
                  <span className="text-white font-bold capitalize">{partnerTier}</span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-gray-300">Deal Type:</span>
                  <span className="text-white font-bold capitalize">{dealType}</span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-gray-300">Deal Term:</span>
                  <span className="text-white font-bold">{dealTerm} {dealTerm === 1 ? 'Year' : 'Years'}</span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-gray-300">Revenue Share:</span>
                  <span className="text-green-400 font-bold">82% to Partner / 18% to NovaFuse</span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-gray-300">Payment Terms:</span>
                  <span className="text-white font-bold">Net 30</span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-gray-300">Equity Opportunity:</span>
                  <span className="text-white font-bold">Yes (Performance-Based)</span>
                </div>
              </div>

              <div className="mt-6 pt-4 border-t border-gray-600 text-center">
                <button className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-bold">
                  Generate Term Sheet
                </button>
              </div>
            </div>
          </div>

          {/* Equity Simulator */}
          <div id="equity-simulator" className="bg-gray-800 rounded-lg p-6 scroll-mt-16">
            <h2 className="text-2xl font-bold mb-4">Equity Simulator</h2>
            <p className="text-gray-300 mb-6">
              Top-performing partners can earn equity in NovaFuse. Use this simulator to see how your performance affects your equity potential.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div>
                <label className="block text-gray-300 mb-2">Yearly Deals</label>
                <div className="flex items-center">
                  <input
                    type="range"
                    min="1"
                    max="20"
                    value={yearlyDeals}
                    onChange={(e) => setYearlyDeals(parseInt(e.target.value))}
                    className="w-full mr-4"
                  />
                  <span className="text-gray-300 font-bold whitespace-nowrap">{yearlyDeals} deals</span>
                </div>
              </div>

              <div>
                <label className="block text-gray-300 mb-2">Years as Partner</label>
                <div className="flex items-center">
                  <input
                    type="range"
                    min="1"
                    max="5"
                    value={yearsActive}
                    onChange={(e) => setYearsActive(parseInt(e.target.value))}
                    className="w-full mr-4"
                  />
                  <span className="text-gray-300 font-bold whitespace-nowrap">{yearsActive} years</span>
                </div>
              </div>
            </div>

            <div className="bg-purple-900 bg-opacity-30 border border-purple-500 rounded-lg p-6">
              <h3 className="text-xl font-bold mb-4 text-center">Your Equity Potential</h3>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div className="bg-gray-800 p-4 rounded-lg text-center">
                  <div className="text-3xl font-bold text-purple-400 mb-1">{equityPotential.equityPercentage.toFixed(2)}%</div>
                  <div className="text-sm text-gray-400">Equity Percentage</div>
                </div>

                <div className="bg-gray-800 p-4 rounded-lg text-center">
                  <div className="text-3xl font-bold text-purple-400 mb-1">{formatCurrency(equityPotential.equityValue)}</div>
                  <div className="text-sm text-gray-400">Equity Value</div>
                </div>

                <div className="bg-gray-800 p-4 rounded-lg text-center">
                  <div className="text-3xl font-bold text-purple-400 mb-1">{formatCurrency(equityPotential.assumedValuation)}</div>
                  <div className="text-sm text-gray-400">Assumed Valuation</div>
                </div>
              </div>

              <p className="text-gray-300 text-sm text-center">
                Note: Equity calculations are based on current partnership tiers and performance metrics.
                Actual equity grants are subject to board approval and may vary.
              </p>
            </div>
          </div>

          {/* Long-Term Benefits */}
          <div id="long-term-benefits" className="bg-gray-800 rounded-lg p-6 scroll-mt-16">
            <h2 className="text-2xl font-bold mb-4">Long-Term Partnership Benefits</h2>
            <p className="text-gray-300 mb-6">
              The 18/82 Partner Empowerment Model creates exponential value over time. See how your earnings compound compared to traditional vendor models.
            </p>

            <div className="mb-6">
              <div className="flex items-center mb-4">
                <label className="text-gray-300 mr-4">Yearly Deals:</label>
                <div className="flex items-center flex-1">
                  <input
                    type="range"
                    min="1"
                    max="20"
                    value={yearlyDeals}
                    onChange={(e) => setYearlyDeals(parseInt(e.target.value))}
                    className="w-full mr-4"
                  />
                  <span className="text-gray-300 font-bold whitespace-nowrap">{yearlyDeals} deals</span>
                </div>
              </div>

              <div className="flex items-center mb-4">
                <label className="text-gray-300 mr-4">Years Active:</label>
                <div className="flex items-center flex-1">
                  <input
                    type="range"
                    min="1"
                    max="5"
                    value={yearsActive}
                    onChange={(e) => setYearsActive(parseInt(e.target.value))}
                    className="w-full mr-4"
                  />
                  <span className="text-gray-300 font-bold whitespace-nowrap">{yearsActive} years</span>
                </div>
              </div>
            </div>

            <div className="bg-gray-700 rounded-lg p-6 mb-6">
              <h3 className="text-xl font-bold mb-4 text-center">5-Year Earnings Projection</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-6">
                <div>
                  <h4 className="font-semibold mb-3 text-center">NovaFuse Model (82%)</h4>
                  <div className="bg-blue-900 bg-opacity-30 border border-blue-500 rounded-lg p-4">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-gray-300">Annual Revenue:</span>
                      <span className="text-white font-bold">{formatCurrency(longTermEarnings.novaFuseYearlyEarnings)}</span>
                    </div>
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-gray-300">Total ({yearsActive} Years):</span>
                      <span className="text-green-400 font-bold">{formatCurrency(longTermEarnings.novaFuseTotalEarnings)}</span>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold mb-3 text-center">{longTermEarnings.competitorName} ({(competitors[selectedCompetitor].partnerShare * 100).toFixed(0)}%)</h4>
                  <div className="bg-gray-800 rounded-lg p-4">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-gray-300">Annual Revenue:</span>
                      <span className="text-white font-bold">{formatCurrency(longTermEarnings.competitorYearlyEarnings)}</span>
                    </div>
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-gray-300">Total ({yearsActive} Years):</span>
                      <span className="text-gray-400 font-bold">{formatCurrency(longTermEarnings.competitorTotalEarnings)}</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-green-900 bg-opacity-30 border border-green-500 rounded-lg p-4 text-center">
                <h4 className="font-semibold mb-2">Additional Earnings with NovaFuse</h4>
                <p className="text-3xl font-bold text-green-400">{formatCurrency(longTermEarnings.totalDifference)}</p>
                <p className="text-gray-300 text-sm mt-2">
                  That's what you're leaving on the table with {longTermEarnings.competitorName}.
                </p>
              </div>
            </div>

            {/* Role-specific long-term benefits */}
            {selectedRole === 'catalyst' && (
              <div className="bg-blue-900 bg-opacity-30 border border-blue-500 rounded-lg p-6 mb-6">
                <h3 className="text-xl font-bold mb-3">Innovation Catalyst: Equity Accelerator</h3>
                <p className="text-gray-300 mb-4">
                  As an Innovation Catalyst, you can access our exclusive Equity Accelerator program:
                </p>
                <div className="bg-gray-800 p-4 rounded-lg mb-4">
                  <div className="flex items-center mb-2">
                    <span className="text-blue-400 text-xl mr-2">🚀</span>
                    <h4 className="font-semibold">Hit $5M GMV → 2% equity vested quarterly</h4>
                  </div>
                  <p className="text-sm text-gray-400">
                    Innovation Catalysts who generate $5M in Gross Merchandise Value through hybrid product-service offerings
                    can earn up to 2% equity in NovaFuse, vested quarterly based on performance.
                  </p>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-gray-800 p-3 rounded-lg">
                    <h4 className="font-semibold mb-2 text-sm">Year 1-2</h4>
                    <ul className="text-xs text-gray-400 space-y-1">
                      <li>• 0.5% equity for $5M GMV</li>
                      <li>• Quarterly vesting schedule</li>
                      <li>• Performance-based acceleration</li>
                    </ul>
                  </div>
                  <div className="bg-gray-800 p-3 rounded-lg">
                    <h4 className="font-semibold mb-2 text-sm">Year 3-5</h4>
                    <ul className="text-xs text-gray-400 space-y-1">
                      <li>• Additional 1.5% equity available</li>
                      <li>• Accelerated vesting options</li>
                      <li>• Board advisor opportunities</li>
                    </ul>
                  </div>
                </div>
              </div>
            )}

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-gray-700 p-4 rounded-lg">
                <div className="flex items-center mb-3">
                  <span className="text-blue-400 text-2xl mr-3">🏆</span>
                  <h3 className="text-lg font-semibold">Performance Bonuses</h3>
                </div>
                <p className="text-gray-400 text-sm">
                  Top-performing partners can earn additional bonuses based on yearly performance metrics and customer satisfaction scores.
                </p>
              </div>

              <div className="bg-gray-700 p-4 rounded-lg">
                <div className="flex items-center mb-3">
                  <span className="text-blue-400 text-2xl mr-3">🎓</span>
                  <h3 className="text-lg font-semibold">Advanced Training</h3>
                </div>
                <p className="text-gray-400 text-sm">
                  Gain access to exclusive training programs, certification paths, and technical resources to enhance your team's capabilities.
                </p>
              </div>

              <div className="bg-gray-700 p-4 rounded-lg">
                <div className="flex items-center mb-3">
                  <span className="text-blue-400 text-2xl mr-3">🚀</span>
                  <h3 className="text-lg font-semibold">Co-Development</h3>
                </div>
                <p className="text-gray-400 text-sm">
                  Collaborate with NovaFuse on new features and products, with opportunities to earn royalties on jointly developed solutions.
                </p>
              </div>
            </div>
          </div>

          {/* CTA Section */}
          <div className="bg-gradient-to-r from-blue-900 to-indigo-900 rounded-lg p-6 text-center">
            <h2 className="text-2xl font-bold mb-3">Ready to Transform Your Business?</h2>
            <p className="text-lg mb-6 max-w-3xl mx-auto">
              Join the NovaFuse Partner Network and start earning 82% of the revenue from day one.
              Our Partner Empowerment Model is revolutionizing the GRC landscape.
            </p>

            {selectedRole !== 'all' && (
              <div className="mb-6 bg-blue-800 bg-opacity-50 p-4 rounded-lg inline-block">
                <h3 className="font-bold text-xl mb-2">{currentRole.title} Fast-Track</h3>
                <p className="text-gray-300 mb-4">
                  We've prepared a personalized onboarding path for {currentRole.title}s.
                </p>
                <div className="bg-blue-900 bg-opacity-50 p-3 rounded-lg text-left inline-block">
                  <ul className="space-y-2 text-sm">
                    {selectedRole === 'developer' && (
                      <>
                        <li className="flex items-start">
                          <span className="text-green-400 mr-2">✓</span>
                          <span>Solutions Marketplace developer account</span>
                        </li>
                        <li className="flex items-start">
                          <span className="text-green-400 mr-2">✓</span>
                          <span>API development toolkit & documentation</span>
                        </li>
                        <li className="flex items-start">
                          <span className="text-green-400 mr-2">✓</span>
                          <span>App monetization fast-track program</span>
                        </li>
                      </>
                    )}

                    {selectedRole === 'strategic' && (
                      <>
                        <li className="flex items-start">
                          <span className="text-green-400 mr-2">✓</span>
                          <span>Enterprise client management toolkit</span>
                        </li>
                        <li className="flex items-start">
                          <span className="text-green-400 mr-2">✓</span>
                          <span>Multi-client orchestration dashboard</span>
                        </li>
                        <li className="flex items-start">
                          <span className="text-green-400 mr-2">✓</span>
                          <span>Strategic alliance partner resources</span>
                        </li>
                      </>
                    )}

                    {selectedRole === 'guardian' && (
                      <>
                        <li className="flex items-start">
                          <span className="text-green-400 mr-2">✓</span>
                          <span>Multi-cloud compliance toolkit</span>
                        </li>
                        <li className="flex items-start">
                          <span className="text-green-400 mr-2">✓</span>
                          <span>FedRAMP acceleration program</span>
                        </li>
                        <li className="flex items-start">
                          <span className="text-green-400 mr-2">✓</span>
                          <span>Cross-cloud guardian certification</span>
                        </li>
                      </>
                    )}

                    {selectedRole === 'catalyst' && (
                      <>
                        <li className="flex items-start">
                          <span className="text-green-400 mr-2">✓</span>
                          <span>Hybrid solution development toolkit</span>
                        </li>
                        <li className="flex items-start">
                          <span className="text-green-400 mr-2">✓</span>
                          <span>Equity accelerator program enrollment</span>
                        </li>
                        <li className="flex items-start">
                          <span className="text-green-400 mr-2">✓</span>
                          <span>Innovation catalyst resources & mentoring</span>
                        </li>
                      </>
                    )}
                  </ul>
                </div>
              </div>
            )}

            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <Link
                href="/partner-onboarding"
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-bold"
              >
                Apply to Partner Program
              </Link>
              <Link
                href={`/contact?type=partner&role=${selectedRole}`}
                className="border border-white text-white px-6 py-3 rounded-lg font-bold hover:bg-white hover:text-blue-900"
              >
                Schedule a Consultation
              </Link>
            </div>

            <p className="mt-6 text-lg font-semibold text-blue-400">
              "This isn't partnership as usual—this is a redistribution of power in the GRC ecosystem."
            </p>

            <p className="mt-4 text-xs text-gray-400">
              *All equity percentages are simulated and subject to board approval and vesting schedules.
              Revenue share applies post-deductions (e.g., processing fees, partner-specific support SLAs).
            </p>
          </div>
        </div>
      </BasicDemoTemplate>
    </PageWithSidebar>
  );
}
