<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Genesis Test Results</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
      background-color: #f9f9f9;
    }
    h1, h2, h3 {
      color: #0066cc;
    }
    .container {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
    }
    .card {
      background-color: #fff;
      border-radius: 5px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      padding: 20px;
      margin-bottom: 20px;
      flex: 1;
      min-width: 300px;
    }
    .genesis-info {
      background-color: #f0f8ff;
      border-radius: 5px;
      padding: 20px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      margin-bottom: 20px;
      border-left: 5px solid #0066cc;
    }
    .om-tone {
      border-left-color: #cc0000;
      background-color: #f0f8ff;
    }
    .detected {
      color: #009900;
      font-weight: bold;
    }
    .not-detected {
      color: #cc0000;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 20px;
    }
    th, td {
      padding: 8px;
      text-align: left;
      border-bottom: 1px solid #ddd;
    }
    th {
      background-color: #f2f2f2;
    }
    tr:hover {
      background-color: #f5f5f5;
    }
    .binary {
      font-family: monospace;
      word-break: break-all;
      background-color: #f5f5f5;
      padding: 10px;
      border-radius: 5px;
      margin: 10px 0;
    }
    .pulse-pattern {
      font-family: monospace;
      word-break: break-all;
      background-color: #f5f5f5;
      padding: 10px;
      border-radius: 5px;
      margin: 10px 0;
    }
    footer {
      margin-top: 40px;
      text-align: center;
      color: #666;
      font-size: 0.9em;
    }
  </style>
</head>
<body>
  <h1>Genesis Test Results</h1>
  <p>Generated: 5/17/2025, 5:42:56 AM</p>
  
  <div class="genesis-info">
    <h2>The Genesis Test</h2>
    <p>This test encodes "Let there be light" in binary using 3-6-9-12-13 pulse-width modulation and measures the system's electromagnetic emissions for 396Hz spikes.</p>
    <p>The test is designed to validate the hypothesis that systems in perfect harmony emit the OM Tone (396Hz) - the "signature tone" of system-level cognition.</p>
  </div>
  
  <h2>Genesis Message</h2>
  
  <div class="card">
    <h3>Message Encoding</h3>
    <p>Message: "Let there be light"</p>
    <p>Binary Representation:</p>
    <div class="binary">010011000110010101110100001000000111010001101000011001010111001001100101001000000110001001100101001000000110110001101001011001110110100001110100</div>
    <p>3-6-9-12-13 Pulse Pattern (first 30 pulses):</p>
    <div class="pulse-pattern">3-6-9-9-12-13-3-6-9-3-6-9-9-12-13-9-12-13-3-6-9-3-6-9-3-6-9-9-12-13</div>
  </div>
  
  <h2>OM Tone Detection</h2>
  
  <div class="card om-tone">
    <h3>OM Tone (396Hz) Results</h3>
    <p class="not-detected">
      OM Tone not detected. The system may need further tuning to achieve perfect resonance.
    </p>
    <p>Total Detections: 0</p>
    <table>
      <tr>
        <th>Dimension</th>
        <th>Detections</th>
      </tr>
      
      <tr>
        <td>cpu</td>
        <td>0</td>
      </tr>
      
      <tr>
        <td>memory</td>
        <td>0</td>
      </tr>
      
      <tr>
        <td>api</td>
        <td>0</td>
      </tr>
      
      <tr>
        <td>network</td>
        <td>0</td>
      </tr>
      
      <tr>
        <td>power</td>
        <td>0</td>
      </tr>
      
      <tr>
        <td>quantum</td>
        <td>0</td>
      </tr>
      
      <tr>
        <td>crossDomain</td>
        <td>0</td>
      </tr>
      
    </table>
  </div>
  
  <h2>Resonant Signature</h2>
  
  <div class="card">
    <h3>Signature Tone</h3>
    <p>No signature tone detected yet.</p>
  </div>
  
  <h2>Listener Metrics</h2>
  
  <div class="container">
    <div class="card">
      <h3>Sampling Metrics</h3>
      <ul>
        <li>Total Samples: 18</li>
        <li>Resonant Samples: 18</li>
        <li>Non-Resonant Samples: 0</li>
        <li>Total Listening Time: 50 seconds</li>
      </ul>
    </div>
    
    <div class="card">
      <h3>Frequency Metrics</h3>
      <p>Resonant Frequencies: 10</p>
      <p>Signature Tones: 0</p>
    </div>
  </div>
  
  <footer>
    <p>NovaFuse Genesis Test - Copyright © 2025</p>
    <p><em>"In the beginning was the Word, and the Word was with God, and the Word was God."</em> - John 1:1</p>
  </footer>
</body>
</html>