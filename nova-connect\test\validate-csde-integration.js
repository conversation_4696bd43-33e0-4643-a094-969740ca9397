/**
 * CSDE Integration Validation Test
 * 
 * This script validates the NovaConnect + CSDE integration by processing
 * real security findings through the CSDE connector and measuring performance.
 */

const fs = require('fs');
const path = require('path');
const { performance } = require('perf_hooks');
const CSEDConnector = require('../src/connectors/csde-connector');
const axios = require('axios');

// Mock axios for testing if needed
let mockEnabled = process.env.MOCK_CSDE_API === 'true';
if (mockEnabled) {
  console.log('MOCK MODE ENABLED: Using mock CSDE API responses');
  const mockCsdeResponse = require('./mocks/csde-api-response.json');
  axios.post = jest.fn().mockResolvedValue({ data: { result: mockCsdeResponse } });
}

// Configuration
const CONFIG = {
  csdeApiUrl: process.env.CSDE_API_URL || 'http://localhost:3010',
  enableCaching: true,
  enableMetrics: true,
  cacheSize: 1000,
  domain: 'security',
  testDataDir: path.join(__dirname, 'data'),
  outputDir: path.join(__dirname, 'output'),
  batchSize: 10,
  iterations: 3
};

// Create output directory if it doesn't exist
if (!fs.existsSync(CONFIG.outputDir)) {
  fs.mkdirSync(CONFIG.outputDir, { recursive: true });
}

// Load test data
function loadTestData() {
  console.log(`Loading test data from ${CONFIG.testDataDir}...`);
  
  // Create test data directory if it doesn't exist
  if (!fs.existsSync(CONFIG.testDataDir)) {
    fs.mkdirSync(CONFIG.testDataDir, { recursive: true });
    
    // Create sample test data if directory was empty
    const sampleData = generateSampleData(50);
    fs.writeFileSync(
      path.join(CONFIG.testDataDir, 'sample-findings.json'),
      JSON.stringify(sampleData, null, 2)
    );
    
    console.log(`Created sample test data with ${sampleData.length} findings`);
    return sampleData;
  }
  
  // Load all JSON files in the test data directory
  const findings = [];
  const files = fs.readdirSync(CONFIG.testDataDir).filter(file => file.endsWith('.json'));
  
  for (const file of files) {
    const filePath = path.join(CONFIG.testDataDir, file);
    const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));
    
    if (Array.isArray(data)) {
      findings.push(...data);
    } else {
      findings.push(data);
    }
  }
  
  console.log(`Loaded ${findings.length} findings from ${files.length} files`);
  return findings;
}

// Generate sample security findings
function generateSampleData(count) {
  console.log(`Generating ${count} sample security findings...`);
  
  const findings = [];
  const severities = ['LOW', 'MEDIUM', 'HIGH', 'CRITICAL'];
  const categories = ['VULNERABILITY', 'MISCONFIGURATION', 'IAM', 'NETWORK', 'STORAGE'];
  const resourceTypes = [
    'google.compute.Instance',
    'google.storage.Bucket',
    'google.iam.ServiceAccount',
    'google.cloud.BigQuery.Dataset',
    'google.cloud.KMS.CryptoKey'
  ];
  
  for (let i = 0; i < count; i++) {
    findings.push({
      id: `finding-${100000 + i}`,
      source: 'gcp-security-command-center',
      severity: severities[i % severities.length],
      category: categories[i % categories.length],
      createTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
      updateTime: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
      resourceName: `projects/my-project/zones/us-central1-a/instances/instance-${i}`,
      resourceType: resourceTypes[i % resourceTypes.length],
      state: Math.random() > 0.3 ? 'ACTIVE' : 'INACTIVE',
      externalUri: `https://console.cloud.google.com/security/command-center/findings?project=my-project&id=finding-${100000 + i}`,
      sourceProperties: {
        scanConfigId: `scan-config-${10000 + i}`,
        scanRunId: `scan-run-${20000 + i}`,
        vulnerabilityType: ['XSS', 'SQL_INJECTION', 'CSRF', 'OPEN_REDIRECT', 'RCE'][i % 5],
        vulnerabilityDetails: {
          cvssScore: 4 + Math.random() * 6,
          cvssVector: 'CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:U/C:H/I:H/A:H',
          cve: `CVE-2025-${10000 + i}`,
          description: `Security vulnerability in ${['web application', 'API', 'database', 'storage', 'network'][i % 5]}`,
          references: [
            `https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2025-${10000 + i}`,
            `https://nvd.nist.gov/vuln/detail/CVE-2025-${10000 + i}`
          ],
          fixAvailable: Math.random() > 0.2,
          exploitAvailable: Math.random() > 0.7
        }
      },
      securityMarks: {
        marks: {
          criticality: ['p0', 'p1', 'p2', 'p3'][i % 4],
          compliance: ['pci-dss', 'hipaa', 'soc2', 'gdpr', 'iso27001'].slice(0, 1 + i % 4).join(','),
          dataClassification: ['public', 'internal', 'confidential', 'restricted'][i % 4]
        }
      },
      nextSteps: [
        'Update application to latest version',
        'Apply security patch',
        'Implement input validation',
        'Add Content Security Policy (CSP) headers',
        'Enable firewall rules'
      ].slice(0, 2 + i % 3),
      complianceState: Math.random() > 0.7 ? 'COMPLIANT' : 'NON_COMPLIANT'
    });
  }
  
  return findings;
}

// Create transformation rules
const transformationRules = [
  {
    source: 'id',
    target: 'finding_id',
    transform: 'uppercase'
  },
  {
    source: 'source',
    target: 'source_system',
    transform: 'lowercase'
  },
  {
    source: 'severity',
    target: 'risk_level',
    transform: ['uppercase', 'trim']
  },
  {
    source: 'category',
    target: 'finding_type',
    transform: 'lowercase'
  },
  {
    source: 'createTime',
    target: 'created_at'
  },
  {
    source: 'updateTime',
    target: 'updated_at'
  },
  {
    source: 'resourceName',
    target: 'asset.full_path'
  },
  {
    source: 'resourceType',
    target: 'asset.type',
    transform: 'split',
    transformParams: '.'
  },
  {
    source: 'state',
    target: 'status',
    transform: 'lowercase'
  },
  {
    source: 'externalUri',
    target: 'links.console'
  },
  {
    source: 'sourceProperties.vulnerabilityType',
    target: 'details.vulnerability_type',
    transform: 'lowercase'
  },
  {
    source: 'sourceProperties.vulnerabilityDetails.cvssScore',
    target: 'details.cvss_score',
    transform: 'toNumber'
  },
  {
    source: 'sourceProperties.vulnerabilityDetails.cvssVector',
    target: 'details.cvss_vector'
  },
  {
    source: 'sourceProperties.vulnerabilityDetails.cve',
    target: 'details.cve_id'
  },
  {
    source: 'sourceProperties.vulnerabilityDetails.description',
    target: 'details.description'
  },
  {
    source: 'sourceProperties.vulnerabilityDetails.references',
    target: 'details.references'
  },
  {
    source: 'sourceProperties.vulnerabilityDetails.fixAvailable',
    target: 'details.fix_available'
  },
  {
    source: 'sourceProperties.vulnerabilityDetails.exploitAvailable',
    target: 'details.exploit_available'
  },
  {
    source: 'securityMarks.marks.criticality',
    target: 'metadata.criticality'
  },
  {
    source: 'securityMarks.marks.compliance',
    target: 'metadata.compliance_frameworks',
    transform: 'split',
    transformParams: ','
  },
  {
    source: 'securityMarks.marks.dataClassification',
    target: 'metadata.data_classification'
  },
  {
    source: 'nextSteps',
    target: 'remediation.steps'
  },
  {
    source: 'complianceState',
    target: 'compliance_status',
    transform: 'lowercase'
  }
];

// Run a single test iteration
async function runTestIteration(connector, findings, iteration) {
  console.log(`\nRunning test iteration ${iteration + 1}...`);
  
  // Process findings in batches
  const batches = [];
  for (let i = 0; i < findings.length; i += CONFIG.batchSize) {
    batches.push(findings.slice(i, i + CONFIG.batchSize));
  }
  
  console.log(`Processing ${findings.length} findings in ${batches.length} batches...`);
  
  const batchResults = [];
  let totalDuration = 0;
  
  for (let i = 0; i < batches.length; i++) {
    const batch = batches[i];
    console.log(`Processing batch ${i + 1}/${batches.length} (${batch.length} findings)...`);
    
    const startTime = performance.now();
    const result = await connector.processBatch(batch, { rules: transformationRules });
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    totalDuration += duration;
    
    batchResults.push({
      batchIndex: i,
      batchSize: batch.length,
      duration,
      averageItemDuration: duration / batch.length
    });
    
    console.log(`Batch ${i + 1} processed in ${duration.toFixed(2)}ms (${(duration / batch.length).toFixed(2)}ms per finding)`);
  }
  
  const iterationResult = {
    iteration: iteration + 1,
    totalFindings: findings.length,
    totalBatches: batches.length,
    totalDuration,
    averageBatchDuration: totalDuration / batches.length,
    averageItemDuration: totalDuration / findings.length,
    batchResults,
    connectorMetrics: connector.metrics,
    csdeMetrics: connector.csdeIntegration.getMetrics()
  };
  
  console.log(`Iteration ${iteration + 1} completed in ${totalDuration.toFixed(2)}ms (${(totalDuration / findings.length).toFixed(2)}ms per finding)`);
  
  return iterationResult;
}

// Compare CSDE with standard transformation
async function compareWithStandard(findings) {
  console.log('\nComparing CSDE with standard transformation...');
  
  // Create CSDE connector
  const csdeConnector = new CSEDConnector({
    id: 'csde-connector',
    name: 'CSDE Connector',
    csdeApiUrl: CONFIG.csdeApiUrl,
    enableCaching: false, // Disable caching for fair comparison
    enableMetrics: true
  });
  
  // Create standard connector (CSDE with UUFT disabled)
  const standardConnector = new CSEDConnector({
    id: 'standard-connector',
    name: 'Standard Connector',
    csdeApiUrl: CONFIG.csdeApiUrl,
    enableCaching: false, // Disable caching for fair comparison
    enableMetrics: true,
    enableUUFT: false // Disable UUFT
  });
  
  // Select a sample of findings for comparison
  const sampleSize = Math.min(10, findings.length);
  const sampleFindings = findings.slice(0, sampleSize);
  
  console.log(`Using ${sampleSize} findings for comparison...`);
  
  // Process with CSDE
  console.log('Processing with CSDE...');
  const csdeStartTime = performance.now();
  const csdeResults = await csdeConnector.processBatch(sampleFindings, { rules: transformationRules });
  const csdeEndTime = performance.now();
  const csdeDuration = csdeEndTime - csdeStartTime;
  
  // Process with standard transformation
  console.log('Processing with standard transformation...');
  const standardStartTime = performance.now();
  const standardResults = await standardConnector.processBatch(sampleFindings, { rules: transformationRules });
  const standardEndTime = performance.now();
  const standardDuration = standardEndTime - standardStartTime;
  
  // Calculate performance improvement
  const performanceImprovement = standardDuration / csdeDuration;
  
  const comparisonResult = {
    sampleSize,
    csde: {
      duration: csdeDuration,
      averageItemDuration: csdeDuration / sampleSize
    },
    standard: {
      duration: standardDuration,
      averageItemDuration: standardDuration / sampleSize
    },
    performanceImprovement,
    performanceImprovementPercentage: (performanceImprovement * 100) - 100
  };
  
  console.log(`CSDE: ${csdeDuration.toFixed(2)}ms (${(csdeDuration / sampleSize).toFixed(2)}ms per finding)`);
  console.log(`Standard: ${standardDuration.toFixed(2)}ms (${(standardDuration / sampleSize).toFixed(2)}ms per finding)`);
  console.log(`Performance Improvement: ${performanceImprovement.toFixed(2)}x (${comparisonResult.performanceImprovementPercentage.toFixed(2)}%)`);
  
  return comparisonResult;
}

// Main function
async function main() {
  try {
    console.log('CSDE Integration Validation Test');
    console.log('===============================');
    
    // Load test data
    const findings = loadTestData();
    
    // Create CSDE connector
    console.log('\nInitializing CSDE connector...');
    const connector = new CSEDConnector({
      id: 'csde-connector',
      name: 'CSDE Connector',
      csdeApiUrl: CONFIG.csdeApiUrl,
      enableCaching: CONFIG.enableCaching,
      enableMetrics: CONFIG.enableMetrics,
      cacheSize: CONFIG.cacheSize,
      domain: CONFIG.domain
    });
    
    // Run test iterations
    const iterationResults = [];
    for (let i = 0; i < CONFIG.iterations; i++) {
      const result = await runTestIteration(connector, findings, i);
      iterationResults.push(result);
      
      // Clear cache between iterations
      if (i < CONFIG.iterations - 1) {
        await connector.clearCache();
      }
    }
    
    // Compare with standard transformation
    const comparisonResult = await compareWithStandard(findings);
    
    // Calculate overall results
    const totalDuration = iterationResults.reduce((sum, result) => sum + result.totalDuration, 0);
    const averageIterationDuration = totalDuration / CONFIG.iterations;
    const averageItemDuration = averageIterationDuration / findings.length;
    
    const overallResults = {
      testConfig: CONFIG,
      findings: {
        count: findings.length
      },
      iterations: {
        count: CONFIG.iterations,
        results: iterationResults
      },
      overall: {
        totalDuration,
        averageIterationDuration,
        averageItemDuration
      },
      comparison: comparisonResult,
      connectorMetrics: connector.metrics,
      csdeMetrics: connector.csdeIntegration.getMetrics()
    };
    
    // Save results
    const resultsPath = path.join(CONFIG.outputDir, 'csde-validation-results.json');
    fs.writeFileSync(resultsPath, JSON.stringify(overallResults, null, 2));
    
    console.log(`\nResults saved to ${resultsPath}`);
    
    // Print summary
    console.log('\nTest Summary:');
    console.log(`Processed ${findings.length} findings in ${CONFIG.iterations} iterations`);
    console.log(`Average Duration: ${averageIterationDuration.toFixed(2)}ms per iteration, ${averageItemDuration.toFixed(2)}ms per finding`);
    console.log(`Performance Improvement: ${comparisonResult.performanceImprovement.toFixed(2)}x (${comparisonResult.performanceImprovementPercentage.toFixed(2)}%)`);
    console.log(`CSDE Metrics: ${connector.csdeIntegration.getMetrics().totalRequests} requests, ${connector.csdeIntegration.getMetrics().cacheHits} cache hits, ${connector.csdeIntegration.getMetrics().cacheMisses} cache misses`);
    
    console.log('\nValidation test completed successfully!');
    
    return overallResults;
  } catch (error) {
    console.error('Error running validation test:', error);
    throw error;
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { main, loadTestData, generateSampleData, transformationRules };
