config:
  target: "http://novafuse-api:3000"
  phases:
    - duration: 60
      arrivalRate: 5
      rampTo: 50
      name: "Warm up phase"
    - duration: 120
      arrivalRate: 50
      name: "Sustained load phase"
    - duration: 60
      arrivalRate: 50
      rampTo: 100
      name: "High load phase"
    - duration: 60
      arrivalRate: 100
      rampTo: 10
      name: "Cool down phase"
  plugins:
    metrics-by-endpoint: {}
    expect: {}
  http:
    timeout: 30
  defaults:
    headers:
      Content-Type: "application/json"
      Accept: "application/json"
      Authorization: "Bearer test-api-key"

scenarios:
  # NovaCore API Tests
  - name: "NovaCore API Tests"
    weight: 10
    flow:
      - get:
          url: "/api/v1/novacore/test-results"
          expect:
            - statusCode: 200
            - contentType: "application/json"
      - think: 1
      - get:
          url: "/api/v1/novacore/test-report"
          expect:
            - statusCode: 200
            - contentType: "application/json"

  # NovaTrack API Tests
  - name: "NovaTrack API Tests"
    weight: 15
    flow:
      - get:
          url: "/api/v1/novatrack/requirements"
          expect:
            - statusCode: 200
            - contentType: "application/json"
      - think: 1
      - get:
          url: "/api/v1/novatrack/activities"
          expect:
            - statusCode: 200
            - contentType: "application/json"
      - think: 1
      - get:
          url: "/api/v1/novatrack/compliance-score"
          expect:
            - statusCode: 200
            - contentType: "application/json"

  # NovaView API Tests
  - name: "NovaView API Tests"
    weight: 15
    flow:
      - get:
          url: "/api/v1/novaview/dashboard"
          expect:
            - statusCode: 200
            - contentType: "application/json"
      - think: 1
      - get:
          url: "/api/v1/novaview/dashboard-data"
          expect:
            - statusCode: 200
            - contentType: "application/json"

  # NovaFlowX API Tests
  - name: "NovaFlowX API Tests"
    weight: 15
    flow:
      - get:
          url: "/api/v1/novaflowx/workflows"
          expect:
            - statusCode: 200
            - contentType: "application/json"
      - think: 1
      - post:
          url: "/api/v1/novaflowx/events"
          json:
            eventType: "test_event"
            data: {}
          expect:
            - statusCode: 200
            - contentType: "application/json"

  # NovaThink API Tests
  - name: "NovaThink API Tests"
    weight: 15
    flow:
      - get:
          url: "/api/v1/novathink/frameworks"
          expect:
            - statusCode: 200
            - contentType: "application/json"
      - think: 1
      - get:
          url: "/api/v1/novathink/search?q=gdpr"
          expect:
            - statusCode: 200
            - contentType: "application/json"

  # NovaProof API Tests
  - name: "NovaProof API Tests"
    weight: 15
    flow:
      - get:
          url: "/api/v1/novaproof/evidence"
          expect:
            - statusCode: 200
            - contentType: "application/json"
      - think: 1
      - get:
          url: "/api/v1/novaproof/audit-trail"
          expect:
            - statusCode: 200
            - contentType: "application/json"

  # NovaConnect API Tests
  - name: "NovaConnect API Tests"
    weight: 15
    flow:
      - get:
          url: "/api/v1/novaconnect/discover"
          expect:
            - statusCode: 200
            - contentType: "application/json"
      - think: 1
      - post:
          url: "/api/v1/novaconnect/execute"
          json:
            connectorId: "test-connector"
            operation: "test-operation"
            parameters: {}
          expect:
            - statusCode: 200
            - contentType: "application/json"
