"""
ComphyonΨᶜ Calculator - Core calculation engine for ComphyonΨᶜ metrics.
"""

import math
import time
import numpy as np

class ComphyonCalculator:
    """
    Calculator for ComphyonΨᶜ metrics.
    
    Implements the core algorithms for calculating ComphyonΨᶜ Velocity and Acceleration.
    """
    
    def __init__(self):
        """Initialize the ComphyonΨᶜ Calculator."""
        self.last_calculation = None
        self.normalization_factor = 166000  # Derived from π10³
    
    def calculate_metrics(self, csde_tensor, csfe_tensor, csme_tensor, timestamp=None):
        """
        Calculate ComphyonΨᶜ metrics from system data.
        
        Args:
            csde_tensor: Tensor data from CSDE engine [G, D, A₁, c₁]
            csfe_tensor: Tensor data from CSFE engine [F₁, P, A₂, c₂]
            csme_tensor: Tensor data from CSME engine [T, I, E, c₃]
            timestamp: Optional timestamp for the measurement
            
        Returns:
            Dictionary containing ComphyonΨᶜ metrics
        """
        # Use current time if timestamp not provided
        if timestamp is None:
            timestamp = time.time()
        
        # Extract values from tensors
        G, D, A1, c1 = csde_tensor
        F1, P, A2, c2 = csfe_tensor
        T, I, E, c3 = csme_tensor
        
        # Calculate domain-specific energies
        E_CSDE = A1 * D  # Risk × Data relevance
        E_CSFE = A2 * P  # Alignment accuracy × Policy relevance
        E_CSME = T * I   # Trust × Integrity
        
        # Calculate gradients (simulated for this example)
        dE_CSDE = E_CSDE * c1
        dE_CSFE = E_CSFE * c2
        
        # Calculate ComphyonΨᶜ Velocity
        velocity = (abs(dE_CSDE) + abs(dE_CSFE)) * math.log(max(0.01, E_CSME))
        
        # Calculate ComphyonΨᶜ Acceleration
        acceleration = ((dE_CSDE * dE_CSFE) * math.log(max(0.01, E_CSME))) / self.normalization_factor
        
        # Create metrics dictionary
        metrics = {
            'timestamp': timestamp,
            'velocity': velocity,
            'acceleration': acceleration,
            'domain_energies': {
                'E_CSDE': E_CSDE,
                'E_CSFE': E_CSFE,
                'E_CSME': E_CSME
            },
            'gradients': {
                'dE_CSDE': dE_CSDE,
                'dE_CSFE': dE_CSFE
            }
        }
        
        self.last_calculation = metrics
        return metrics
    
    def calculate_from_rates(self, csde_rate, csfe_rate, csme_score):
        """
        Calculate ComphyonΨᶜ metrics from prediction rates and ethical score.
        
        This is a simplified calculation for real-time monitoring.
        
        Args:
            csde_rate: Predictions per second from CSDE engine
            csfe_rate: Predictions per second from CSFE engine
            csme_score: Ethical score from CSME engine (0-1)
            
        Returns:
            Dictionary containing ComphyonΨᶜ metrics
        """
        # Ensure csme_score is positive for log calculation
        safe_csme_score = max(0.01, csme_score)
        
        # Calculate unified gradient (CSDE * CSFE)
        unified_gradient = csde_rate * csfe_rate
        
        # Apply ethical modifier using logarithm
        ethical_modifier = math.log(safe_csme_score * 10)  # Scale to 0.1-10 range for log
        
        # Calculate raw ComphyonΨᶜ value
        raw_cph = unified_gradient * ethical_modifier
        
        # Normalize to 1 Cph = 3142 predictions/sec
        normalized_cph = raw_cph / self.normalization_factor
        
        # Calculate velocity (simplified)
        velocity = (csde_rate + csfe_rate) * ethical_modifier
        
        return {
            'timestamp': time.time(),
            'velocity': velocity,
            'acceleration': max(0, normalized_cph),
            'raw_values': {
                'csde_rate': csde_rate,
                'csfe_rate': csfe_rate,
                'csme_score': csme_score
            }
        }
