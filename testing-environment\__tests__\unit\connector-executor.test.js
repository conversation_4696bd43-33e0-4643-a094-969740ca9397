/**
 * Unit Tests for Connector Executor
 *
 * These tests focus on the connector executor functionality
 * to help achieve the 96% coverage threshold.
 */

// Import test utilities
const {
  createAxiosMock,
  generateTestConnector,
  generateTestCredential,
  measureExecutionTime
} = require('../helpers/test-utils');

// Import axios for mocking
const axios = require('axios');
const MockAdapter = require('axios-mock-adapter');

// Test data
const testConnector = generateTestConnector();
const testConnectorId = 'test-connector-id';
const testCredential = generateTestCredential({ connectorId: testConnectorId });
const testCredentialId = 'test-credential-id';
const testEndpointId = 'getFindings';
const testUserId = 'test-user';

// Mock API response data
const mockApiResponse = {
  Findings: [
    {
      Id: 'finding-1',
      Title: 'Test Finding',
      Description: 'This is a test finding',
      Severity: 'HIGH'
    }
  ]
};

// Expected transformed response
const expectedTransformedResponse = {
  findingId: 'finding-1'
};

describe('Connector Executor', () => {
  let mock;

  // Set up before tests
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();

    // Create axios mock
    mock = new MockAdapter(axios);

    // Set up mock responses
    // Registry API
    mock.onGet(`/connectors/${testConnectorId}`).reply(200, testConnector);

    // Auth Service API
    mock.onGet(`/credentials/${testCredentialId}/decrypt`).reply(200, {
      id: testCredentialId,
      authType: testCredential.authType,
      credentials: testCredential.credentials
    });

    // Target API
    mock.onGet(new RegExp(`${testConnector.configuration.baseUrl}.*`)).reply(200, mockApiResponse);

    // Usage Metering API
    mock.onPost('/track').reply(201, { success: true });

    // Connector Executor API
    mock.onPost(`/execute/${testConnectorId}/${testEndpointId}`).reply(function(config) {
      const data = JSON.parse(config.data);

      if (data.credentialId !== testCredentialId) {
        return [404, { error: 'Credential not found' }];
      }

      return [200, {
        success: true,
        statusCode: 200,
        data: expectedTransformedResponse
      }];
    });
  });

  // Clean up after tests
  afterEach(() => {
    mock.restore();
  });

  // Test connector execution
  describe('Connector Execution', () => {
    it('should execute a connector endpoint successfully', async () => {
      const response = await axios.post(`/execute/${testConnectorId}/${testEndpointId}`, {
        credentialId: testCredentialId,
        parameters: {},
        userId: testUserId
      });

      expect(response.status).toBe(200);
      expect(response.data).toHaveProperty('success', true);
      expect(response.data).toHaveProperty('statusCode', 200);
      expect(response.data).toHaveProperty('data', expectedTransformedResponse);
    });

    it('should execute a connector endpoint with query parameters', async () => {
      mock.onPost(`/execute/${testConnectorId}/${testEndpointId}`).reply(200, {
        success: true,
        statusCode: 200,
        data: expectedTransformedResponse
      });

      const response = await axios.post(`/execute/${testConnectorId}/${testEndpointId}`, {
        credentialId: testCredentialId,
        parameters: {
          query: {
            limit: 10,
            offset: 0
          }
        },
        userId: testUserId
      });

      expect(response.status).toBe(200);
      expect(response.data).toHaveProperty('success', true);
    });

    it('should execute a connector endpoint with path parameters', async () => {
      mock.onPost(`/execute/${testConnectorId}/getFindings`).reply(200, {
        success: true,
        statusCode: 200,
        data: expectedTransformedResponse
      });

      const response = await axios.post(`/execute/${testConnectorId}/getFindings`, {
        credentialId: testCredentialId,
        parameters: {
          path: {
            id: 'finding-1'
          }
        },
        userId: testUserId
      });

      expect(response.status).toBe(200);
      expect(response.data).toHaveProperty('success', true);
    });

    it('should execute a connector endpoint with body parameters', async () => {
      mock.onPost(`/execute/${testConnectorId}/createFinding`).reply(201, {
        success: true,
        statusCode: 201,
        data: { id: 'new-finding-1' }
      });

      const response = await axios.post(`/execute/${testConnectorId}/createFinding`, {
        credentialId: testCredentialId,
        parameters: {
          body: {
            title: 'New Finding',
            description: 'This is a new finding',
            severity: 'MEDIUM'
          }
        },
        userId: testUserId
      });

      expect(response.status).toBe(201);
      expect(response.data).toHaveProperty('success', true);
    });
  });

  // Test response transformation
  describe('Response Transformation', () => {
    it('should transform the response according to the mapping', async () => {
      const response = await axios.post(`/execute/${testConnectorId}/${testEndpointId}`, {
        credentialId: testCredentialId,
        parameters: {},
        userId: testUserId
      });

      expect(response.status).toBe(200);
      expect(response.data).toHaveProperty('data', expectedTransformedResponse);
    });

    it('should return raw response when no mapping exists', async () => {
      // Create a connector without mappings
      const connectorWithoutMappings = generateTestConnector({
        mappings: []
      });

      mock.onGet(`/connectors/${testConnectorId}`).reply(200, connectorWithoutMappings);

      // Update the mock for this specific test to return raw data
      mock.onPost(`/execute/${testConnectorId}/${testEndpointId}`).reply(200, {
        success: true,
        statusCode: 200,
        data: mockApiResponse
      });

      const response = await axios.post(`/execute/${testConnectorId}/${testEndpointId}`, {
        credentialId: testCredentialId,
        parameters: {},
        userId: testUserId
      });

      expect(response.status).toBe(200);
      expect(response.data).toHaveProperty('data', mockApiResponse);
    });
  });

  // Test error handling
  describe('Error Handling', () => {
    it('should handle connector not found', async () => {
      // Set up mock for non-existent connector
      mock.onPost('/execute/non-existent/getFindings').reply(404, { error: 'Connector not found' });

      try {
        await axios.post('/execute/non-existent/getFindings', {
          credentialId: testCredentialId,
          parameters: {},
          userId: testUserId
        });
        fail('Expected request to fail');
      } catch (err) {
        expect(err.response.status).toBe(404);
        expect(err.response.data).toHaveProperty('error', 'Connector not found');
      }
    });

    it('should handle endpoint not found', async () => {
      mock.onPost(`/execute/${testConnectorId}/non-existent`).reply(404, { error: 'Endpoint not found' });

      try {
        await axios.post(`/execute/${testConnectorId}/non-existent`, {
          credentialId: testCredentialId,
          parameters: {},
          userId: testUserId
        });
        fail('Expected request to fail');
      } catch (err) {
        expect(err.response.status).toBe(404);
        expect(err.response.data).toHaveProperty('error', 'Endpoint not found');
      }
    });

    it('should handle credential not found', async () => {
      try {
        await axios.post(`/execute/${testConnectorId}/${testEndpointId}`, {
          credentialId: 'non-existent',
          parameters: {},
          userId: testUserId
        });
        fail('Expected request to fail');
      } catch (err) {
        expect(err.response.status).toBe(404);
        expect(err.response.data).toHaveProperty('error', 'Credential not found');
      }
    });

    it('should handle API errors', async () => {
      mock.onGet(new RegExp(`${testConnector.configuration.baseUrl}.*`)).reply(500, { error: 'Internal server error' });

      mock.onPost(`/execute/${testConnectorId}/${testEndpointId}`).reply(500, {
        error: 'API request failed',
        statusCode: 500,
        message: { error: 'Internal server error' }
      });

      try {
        await axios.post(`/execute/${testConnectorId}/${testEndpointId}`, {
          credentialId: testCredentialId,
          parameters: {},
          userId: testUserId
        });
        fail('Expected request to fail');
      } catch (err) {
        expect(err.response.status).toBe(500);
        expect(err.response.data).toHaveProperty('error', 'API request failed');
      }
    });
  });

  // Test performance
  describe('Performance', () => {
    it('should execute within acceptable time', async () => {
      const { result, duration } = await measureExecutionTime(async () => {
        return await axios.post(`/execute/${testConnectorId}/${testEndpointId}`, {
          credentialId: testCredentialId,
          parameters: {},
          userId: testUserId
        });
      });

      expect(result.status).toBe(200);
      expect(duration).toBeLessThan(1000); // Should execute in less than 1 second
    });
  });
});
