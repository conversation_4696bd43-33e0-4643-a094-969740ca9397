/**
 * CHAEONIX PERFORMANCE TRACKER
 * Real-time monitoring of 18/82 rule and trading performance
 * Validates that the system is working as designed
 */

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  ChartBarIcon, 
  TrophyIcon, 
  ShieldCheckIcon,
  CurrencyDollarIcon,
  BoltIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

export default function CHAEONIXPerformanceTracker() {
  const [performance, setPerformance] = useState({
    total_trades: 0,
    active_trades: 0,
    closed_trades: 0,
    winning_trades: 0,
    losing_trades: 0,
    neutral_trades: 0,
    win_rate: 0,
    total_profit: 0.00,
    average_win: 0.00,
    average_loss: 0.00,
    largest_win: 0.00,
    largest_loss: 0.00,
    rule_18_82_compliance: 0,
    divine_protection_active: true,
    phi_enhancement: 1.618
  });

  const [recentTrades, setRecentTrades] = useState([]);

  useEffect(() => {
    // Update performance metrics every 3 seconds
    const interval = setInterval(() => {
      updatePerformanceMetrics();
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  const resetMetrics = async () => {
    try {
      const response = await fetch('/api/analytics/profit-tracker', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'RESET_METRICS'
        })
      });

      if (response.ok) {
        // Reset local state
        setPerformance({
          total_trades: 0,
          active_trades: 0,
          closed_trades: 0,
          winning_trades: 0,
          losing_trades: 0,
          neutral_trades: 0,
          win_rate: 0,
          total_profit: 0.00,
          average_win: 0.00,
          average_loss: 0.00,
          largest_win: 0.00,
          largest_loss: 0.00,
          rule_18_82_compliance: 0,
          divine_protection_active: true,
          phi_enhancement: 1.618
        });
        setRecentTrades([]);
        console.log('💰 CHAEONIX metrics reset to $0');
      }
    } catch (error) {
      console.error('Error resetting metrics:', error);
    }
  };

  const updatePerformanceMetrics = async () => {
    try {
      // Get MT5 status for active positions
      const mt5Response = await fetch('/api/mt5/status');
      const mt5Data = await mt5Response.json();

      // Get Live Trading Bot for total trade count
      const botResponse = await fetch('/api/trading/live-bot');
      const botData = await botResponse.json();

      const activePositions = mt5Data.positions || [];
      const totalTradesFromBot = botData.bot_status?.total_trades || 0;
      const activeTrades = activePositions.length;

      // Calculate closed trades
      const closedTrades = Math.max(0, totalTradesFromBot - activeTrades);

      console.log(`📊 TRADE COUNT DEBUG:
        - Live Bot Total: ${totalTradesFromBot}
        - Active Positions: ${activeTrades}
        - Calculated Closed: ${closedTrades}`);

      // Use active positions for P&L calculation, but total count for trade metrics
      const trades = activePositions;

      if (totalTradesFromBot > 0) {
        const winning = trades.filter(t => t.profit > 0);
        const losing = trades.filter(t => t.profit < 0);
        const neutral = trades.filter(t => t.profit === 0);

        // For win rate calculation, treat neutral trades as non-winning
        // This ensures: winning + losing + neutral = total_trades
        const totalProfit = trades.reduce((sum, t) => sum + (t.profit || 0), 0);
        const winRate = trades.length > 0 ? (winning.length / trades.length) * 100 : 0;
        
        // Calculate 18/82 compliance
        const rule_compliance = winRate >= 82 ? 100 : (winRate / 82) * 100;
        
        setPerformance({
          total_trades: totalTradesFromBot, // Use total from Live Trading Bot
          active_trades: activeTrades, // Track active positions separately
          closed_trades: closedTrades, // Track closed trades
          winning_trades: winning.length,
          losing_trades: losing.length,
          neutral_trades: neutral.length, // Track neutral trades
          win_rate: winRate,
          total_profit: totalProfit,
          average_win: winning.length > 0 ? winning.reduce((sum, t) => sum + t.profit, 0) / winning.length : 0,
          average_loss: losing.length > 0 ? losing.reduce((sum, t) => sum + t.profit, 0) / losing.length : 0,
          largest_win: winning.length > 0 ? Math.max(...winning.map(t => t.profit)) : 0,
          largest_loss: losing.length > 0 ? Math.min(...losing.map(t => t.profit)) : 0,
          rule_18_82_compliance: rule_compliance,
          divine_protection_active: losing.every(t => t.profit > -10), // No loss > $10
          phi_enhancement: 1.618
        });
        
        // Keep recent trades (last 5)
        setRecentTrades(trades.slice(-5).reverse());
      }
      
    } catch (error) {
      console.error('Performance tracking error:', error);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(amount || 0);
  };

  const getComplianceColor = (compliance) => {
    if (compliance >= 90) return 'text-green-400';
    if (compliance >= 70) return 'text-yellow-400';
    return 'text-red-400';
  };

  const getComplianceStatus = (compliance) => {
    if (compliance >= 90) return 'EXCELLENT';
    if (compliance >= 70) return 'GOOD';
    return 'NEEDS IMPROVEMENT';
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="p-6 rounded-lg border border-purple-500/30 bg-gradient-to-br from-purple-900/20 to-blue-900/20 backdrop-blur-sm"
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <TrophyIcon className="w-6 h-6 text-purple-400" />
          <div>
            <h3 className="text-xl font-bold text-white">
              CHAEONIX Performance Tracker
            </h3>
            <p className="text-sm text-gray-400">
              Real-time 18/82 Rule Validation & Divine Protection Status
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-3">
          <button
            onClick={resetMetrics}
            className="px-3 py-1 rounded-full text-xs font-medium bg-yellow-600 hover:bg-yellow-700 text-white transition-all"
          >
            💰 RESET
          </button>

          <div className="flex items-center space-x-2">
            {performance.divine_protection_active ? (
              <CheckCircleIcon className="w-5 h-5 text-green-400" />
            ) : (
              <ExclamationTriangleIcon className="w-5 h-5 text-red-400" />
            )}
            <span className="text-sm text-gray-300">
              Divine Protection: {performance.divine_protection_active ? 'ACTIVE' : 'INACTIVE'}
            </span>
          </div>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <div className="p-4 rounded-lg bg-gray-800/50 border border-gray-600">
          <div className="flex items-center space-x-2 mb-2">
            <ChartBarIcon className="w-4 h-4 text-blue-400" />
            <span className="text-sm text-gray-400">Total Trades</span>
          </div>
          <div className="text-2xl font-bold text-white">
            {performance.total_trades}
          </div>
        </div>

        <div className="p-4 rounded-lg bg-gray-800/50 border border-gray-600">
          <div className="flex items-center space-x-2 mb-2">
            <TrophyIcon className="w-4 h-4 text-green-400" />
            <span className="text-sm text-gray-400">Win Rate</span>
          </div>
          <div className={`text-2xl font-bold ${getComplianceColor(performance.win_rate)}`}>
            {performance.win_rate.toFixed(1)}%
          </div>
        </div>

        <div className="p-4 rounded-lg bg-gray-800/50 border border-gray-600">
          <div className="flex items-center space-x-2 mb-2">
            <CurrencyDollarIcon className="w-4 h-4 text-yellow-400" />
            <span className="text-sm text-gray-400">Total P&L</span>
          </div>
          <div className={`text-2xl font-bold ${performance.total_profit >= 0 ? 'text-green-400' : 'text-red-400'}`}>
            {formatCurrency(performance.total_profit)}
          </div>
        </div>

        <div className="p-4 rounded-lg bg-gray-800/50 border border-gray-600">
          <div className="flex items-center space-x-2 mb-2">
            <ShieldCheckIcon className="w-4 h-4 text-purple-400" />
            <span className="text-sm text-gray-400">18/82 Compliance</span>
          </div>
          <div className={`text-2xl font-bold ${getComplianceColor(performance.rule_18_82_compliance)}`}>
            {performance.rule_18_82_compliance.toFixed(0)}%
          </div>
        </div>
      </div>

      {/* 18/82 Rule Status */}
      <div className="mb-6 p-4 rounded-lg bg-gradient-to-r from-purple-900/30 to-blue-900/30 border border-purple-500/30">
        <div className="flex items-center justify-between mb-3">
          <h4 className="text-lg font-semibold text-purple-300">📐 18/82 Universal Constant Status</h4>
          <span className={`px-3 py-1 rounded-full text-sm font-medium ${getComplianceColor(performance.rule_18_82_compliance)} bg-gray-800`}>
            {getComplianceStatus(performance.rule_18_82_compliance)}
          </span>
        </div>
        
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-400">Target Win Rate:</span>
            <span className="text-white ml-2 font-medium">≥82%</span>
          </div>
          <div>
            <span className="text-gray-400">Current Win Rate:</span>
            <span className={`ml-2 font-medium ${getComplianceColor(performance.win_rate)}`}>
              {performance.win_rate.toFixed(1)}%
            </span>
          </div>
          <div>
            <span className="text-gray-400">Winning Trades:</span>
            <span className="text-green-400 ml-2 font-medium">{performance.winning_trades}</span>
          </div>
          <div>
            <span className="text-gray-400">Losing Trades:</span>
            <span className="text-red-400 ml-2 font-medium">{performance.losing_trades}</span>
          </div>
          <div>
            <span className="text-gray-400">Neutral Trades:</span>
            <span className="text-yellow-400 ml-2 font-medium">{performance.neutral_trades || 0}</span>
          </div>
          <div className="text-xs text-gray-500 mt-1 space-y-1">
            <div>Active: {performance.winning_trades + performance.losing_trades + (performance.neutral_trades || 0)} = {performance.active_trades || 0}</div>
            <div>Closed: {performance.closed_trades || 0}</div>
            <div className="font-medium text-white">Total: {performance.total_trades}</div>
          </div>
        </div>
      </div>

      {/* Recent Trades */}
      <div>
        <h4 className="text-lg font-semibold text-white mb-3">📈 Recent Trading Activity</h4>
        <div className="space-y-2">
          {recentTrades.length > 0 ? (
            recentTrades.map((trade, index) => (
              <div key={index} className="flex items-center justify-between p-3 rounded-lg bg-gray-800/30 border border-gray-600">
                <div className="flex items-center space-x-3">
                  <span className={`px-2 py-1 rounded text-xs font-medium ${
                    trade.action === 'BUY' ? 'bg-green-600 text-white' : 'bg-red-600 text-white'
                  }`}>
                    {trade.action}
                  </span>
                  <span className="text-white font-medium">{trade.symbol}</span>
                  <span className="text-gray-400">{trade.volume} lots</span>
                </div>
                <div className="text-right">
                  <div className={`font-bold ${trade.profit >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                    {trade.profit >= 0 ? '+' : ''}{formatCurrency(trade.profit)}
                  </div>
                  <div className="text-xs text-gray-400">
                    {trade.status || 'OPEN'}
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-4 text-gray-400">
              No trading activity yet. Bot is analyzing markets...
            </div>
          )}
        </div>
      </div>

      {/* φ Enhancement Status */}
      <div className="mt-6 p-3 rounded-lg bg-gradient-to-r from-yellow-900/20 to-orange-900/20 border border-yellow-500/30">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <BoltIcon className="w-4 h-4 text-yellow-400" />
            <span className="text-sm text-yellow-300">φ-Enhancement Active</span>
          </div>
          <span className="text-yellow-400 font-mono">
            φ = {performance.phi_enhancement}
          </span>
        </div>
      </div>
    </motion.div>
  );
}
