import React from 'react';
import Link from 'next/link';
import Sidebar from '../components/Sidebar';

export default function Developers() {
  const sidebarItems = [
    { type: 'category', label: 'Developer Resources', items: [
      { label: 'Getting Started', href: '#getting-started' },
      { label: 'API Documentation', href: '/api-docs' },
      { label: 'Sandbox', href: '#sandbox' },
      { label: 'SDKs & Libraries', href: '#sdks' },
      { label: 'Code Samples', href: '#code-samples' }
    ]},
    { type: 'category', label: 'Support', items: [
      { label: 'Developer Forum', href: '#forum' },
      { label: 'FAQ', href: '#faq' },
      { label: 'Contact Support', href: '/contact' }
    ]}
  ];

  return (
    <div className="flex flex-col md:flex-row gap-8">
      {/* Sidebar - Hidden on mobile, shown on desktop */}
      <div className="hidden md:block md:w-1/4 lg:w-1/5">
        <div className="sticky top-4">
          <Sidebar items={sidebarItems} title="Developer Portal" />
        </div>
      </div>

      {/* Mobile Sidebar Toggle - Shown on mobile only */}
      <div className="md:hidden mb-4">
        <select
          className="w-full bg-secondary text-white border border-gray-700 rounded p-2"
          onChange={(e) => {
            if (e.target.value) window.location.href = e.target.value;
          }}
          defaultValue=""
        >
          <option value="" disabled>Navigate to...</option>
          {sidebarItems.map((item, index) => (
            item.type === 'category' ? (
              <optgroup key={index} label={item.label}>
                {item.items.map((subItem, subIndex) => (
                  <option key={`${index}-${subIndex}`} value={subItem.href}>
                    {subItem.label}
                  </option>
                ))}
              </optgroup>
            ) : (
              <option key={index} value={item.href}>
                {item.label}
              </option>
            )
          ))}
        </select>
      </div>

      {/* Main Content */}
      <div className="w-full md:w-3/4 lg:w-4/5">
        {/* Hero Section */}
        <div className="bg-blue-900 text-white rounded-lg p-8 mb-8 shadow-lg">
          <h1 className="text-3xl md:text-4xl font-bold mb-4">NovaFuse Developer Portal</h1>
          <p className="text-xl mb-6">
            Build innovative solutions with NovaFuse's powerful APIs and developer tools.
          </p>
          <div className="flex flex-wrap gap-4">
            <Link href="/api-docs" className="bg-white text-blue-600 px-6 py-3 rounded-lg font-bold hover:bg-blue-50 inline-block">
              API Documentation
            </Link>
            <a href="#sandbox" className="border border-white text-white px-6 py-3 rounded-lg font-bold hover:bg-blue-800 inline-block">
              Try the Sandbox
            </a>
          </div>
        </div>

        {/* Getting Started Section */}
        <div id="getting-started" className="mb-12">
          <h2 className="text-2xl font-bold mb-6">Getting Started</h2>
          <div className="bg-secondary border border-gray-700 rounded-lg p-6">
            <p className="mb-6">
              Welcome to the NovaFuse Developer Portal. Here you'll find everything you need to integrate with our APIs and build powerful solutions.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
              <div className="bg-blue-900 p-5 rounded-lg shadow-lg border border-blue-700">
                <h3 className="text-xl font-semibold mb-3">1. Create an Account</h3>
                <p className="text-gray-300 mb-4">
                  Sign up for a NovaFuse developer account to get access to our APIs and developer tools.
                </p>
                <Link href="/sign-up" className="text-blue-400 hover:text-blue-300 font-medium">
                  Create Account →
                </Link>
              </div>
              
              <div className="bg-blue-900 p-5 rounded-lg shadow-lg border border-blue-700">
                <h3 className="text-xl font-semibold mb-3">2. Explore the APIs</h3>
                <p className="text-gray-300 mb-4">
                  Browse our API documentation to understand the available endpoints and how to use them.
                </p>
                <Link href="/api-docs" className="text-blue-400 hover:text-blue-300 font-medium">
                  View API Docs →
                </Link>
              </div>
              
              <div className="bg-blue-900 p-5 rounded-lg shadow-lg border border-blue-700">
                <h3 className="text-xl font-semibold mb-3">3. Test in Sandbox</h3>
                <p className="text-gray-300 mb-4">
                  Use our sandbox environment to test your integration before going live.
                </p>
                <a href="#sandbox" className="text-blue-400 hover:text-blue-300 font-medium">
                  Try Sandbox →
                </a>
              </div>
            </div>
          </div>
        </div>

        {/* Sandbox Section */}
        <div id="sandbox" className="mb-12">
          <h2 className="text-2xl font-bold mb-6">API Sandbox</h2>
          <div className="bg-secondary border border-gray-700 rounded-lg p-6">
            <p className="mb-6">
              The NovaFuse API Sandbox allows you to test our APIs in a safe environment. No real data is affected, and you can experiment freely.
            </p>
            
            <div className="bg-blue-900 p-6 rounded-lg shadow-lg border border-blue-700 mb-6">
              <h3 className="text-xl font-semibold mb-4">Sandbox Features</h3>
              <ul className="space-y-2">
                <li className="flex items-start">
                  <span className="text-blue-400 mr-2">✓</span>
                  <span>Test all NovaFuse APIs without affecting production data</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-400 mr-2">✓</span>
                  <span>Pre-populated with sample data for realistic testing</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-400 mr-2">✓</span>
                  <span>No usage limits or throttling in sandbox mode</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-400 mr-2">✓</span>
                  <span>Simulate various error conditions and edge cases</span>
                </li>
              </ul>
            </div>
            
            <div className="text-center">
              <p className="mb-4">
                The sandbox is currently available to registered developers. Sign up or sign in to access the sandbox.
              </p>
              <div className="flex flex-wrap justify-center gap-4">
                <Link href="/sign-in" className="accent-bg text-white px-6 py-3 rounded-lg font-bold hover:bg-blue-700 inline-block">
                  Sign In to Access Sandbox
                </Link>
                <Link href="/sign-up" className="border border-gray-600 text-white px-6 py-3 rounded-lg font-bold hover:bg-gray-800 inline-block">
                  Create Developer Account
                </Link>
              </div>
            </div>
          </div>
        </div>

        {/* SDKs Section */}
        <div id="sdks" className="mb-12">
          <h2 className="text-2xl font-bold mb-6">SDKs & Libraries</h2>
          <div className="bg-secondary border border-gray-700 rounded-lg p-6">
            <p className="mb-6">
              We provide SDKs in multiple languages to make integration with NovaFuse APIs easier.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="bg-blue-900 p-5 rounded-lg shadow-lg border border-blue-700">
                <div className="flex items-center mb-4">
                  <svg className="h-8 w-8 text-yellow-400 mr-3" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M0 0h24v24H0V0zm22.034 18.276c-.175-1.095-.888-2.015-3.003-2.873-.736-.345-1.554-.585-1.797-1.14-.091-.33-.105-.51-.046-.705.15-.646.915-.84 1.515-.***********.42.976.9 1.034-.676 1.034-.676 1.755-1.125-.27-.42-.404-.601-.586-.78-.63-.705-1.469-1.065-2.834-1.034l-.705.089c-.676.165-1.32.525-1.71 1.005-1.14 1.291-.811 3.541.569 4.471 1.365 1.02 3.361 1.244 3.616 2.205.24 1.17-.87 1.545-1.966 1.41-.811-.18-1.26-.586-1.755-1.336l-1.83 1.051c.21.48.45.689.81 1.109 1.74 1.756 6.09 1.666 6.871-1.004.029-.09.24-.705.074-1.65l.046.067zm-8.983-7.245h-2.248c0 1.938-.009 3.864-.009 5.805 0 1.232.063 2.363-.138 2.711-.33.689-1.18.601-1.566.48-.396-.196-.597-.466-.83-.855-.063-.105-.11-.196-.127-.196l-1.825 1.125c.305.63.75 1.172 1.324 1.517.855.51 2.004.675 3.207.405.783-.226 1.458-.691 1.811-1.411.51-.93.402-2.07.397-3.346.012-2.054 0-4.109 0-6.179l.004-.056z"/>
                  </svg>
                  <h3 className="text-xl font-semibold">JavaScript</h3>
                </div>
                <p className="text-gray-300 mb-4">
                  Our JavaScript SDK works in both browser and Node.js environments.
                </p>
                <div className="flex justify-between">
                  <a href="#" className="text-blue-400 hover:text-blue-300">Documentation</a>
                  <a href="#" className="text-blue-400 hover:text-blue-300">GitHub</a>
                </div>
              </div>
              
              <div className="bg-blue-900 p-5 rounded-lg shadow-lg border border-blue-700">
                <div className="flex items-center mb-4">
                  <svg className="h-8 w-8 text-blue-400 mr-3" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M14.25.18l.*********.**********.***********.*********.**********-.01.13V8.5l-.05.63-.13.55-.21.46-.26.38-.3.31-.33.25-.35.19-.35.14-.33.1-.3.07-.26.04-.21.02H8.77l-.69.05-.59.14-.5.22-.41.27-.33.32-.27.35-.2.36-.15.37-.1.35-.07.32-.04.27-.02.21v3.06H3.17l-.21-.03-.28-.07-.32-.12-.35-.18-.36-.26-.36-.36-.35-.46-.32-.59-.28-.73-.21-.88-.14-1.05-.05-1.23.06-1.22.16-1.04.24-.87.32-.71.36-.57.4-.44.42-.33.42-.24.4-.16.36-.1.32-.05.24-.01h.16l.06.01h8.16v-.83H6.18l-.01-2.75-.02-.37.05-.34.11-.31.17-.28.25-.26.31-.23.38-.2.44-.18.51-.15.58-.12.64-.1.71-.06.77-.04.84-.02 1.27.05zm-6.3 1.98l-.23.33-.08.41.08.41.23.34.33.22.41.09.41-.09.33-.22.23-.34.08-.41-.08-.41-.23-.33-.33-.22-.41-.09-.41.09zm13.09 3.95l.28.06.32.12.35.18.36.27.36.35.35.47.32.59.28.73.21.88.14 1.04.05 1.23-.06 1.23-.16 1.04-.24.86-.32.71-.36.57-.4.45-.42.33-.42.24-.4.16-.36.09-.32.05-.24.02-.16-.01h-8.22v.82h5.84l.01 2.76.02.36-.05.34-.11.31-.17.29-.25.25-.31.24-.38.2-.44.17-.51.15-.58.13-.64.09-.71.07-.77.04-.84.01-1.27-.04-1.07-.14-.9-.2-.73-.25-.59-.3-.45-.33-.34-.34-.25-.34-.16-.33-.1-.3-.04-.25-.02-.2.01-.13v-5.34l.05-.64.13-.54.21-.46.26-.38.3-.32.33-.24.35-.2.35-.14.33-.1.3-.06.26-.04.21-.02.13-.01h5.84l.69-.05.59-.14.5-.21.41-.28.33-.32.27-.35.2-.36.15-.36.1-.35.07-.32.04-.28.02-.21V6.07h2.09l.14.01zm-6.47 14.25l-.23.33-.08.41.08.41.23.33.33.23.41.08.41-.08.33-.23.23-.33.08-.41-.08-.41-.23-.33-.33-.23-.41-.08-.41.08z"/>
                  </svg>
                  <h3 className="text-xl font-semibold">Python</h3>
                </div>
                <p className="text-gray-300 mb-4">
                  Python SDK with support for async operations and all NovaFuse APIs.
                </p>
                <div className="flex justify-between">
                  <a href="#" className="text-blue-400 hover:text-blue-300">Documentation</a>
                  <a href="#" className="text-blue-400 hover:text-blue-300">GitHub</a>
                </div>
              </div>
              
              <div className="bg-blue-900 p-5 rounded-lg shadow-lg border border-blue-700">
                <div className="flex items-center mb-4">
                  <svg className="h-8 w-8 text-red-400 mr-3" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M16.934 8.519a1.044 1.044 0 0 1 .303.23l2.349-1.045-2.192-2.495-2.823 2.198 1.776.837a1.2 1.2 0 0 1 .587.275zM12.06 6.546a1.106 1.106 0 0 1 .402-.265l1.053-1.8-2.861-1.61-1.556 3.201 2.962.474zm-2.957.074a1.11 1.11 0 0 1 .874-.445l.052-2.126L6.8 3.008l-.986 3.67 3.289-.058zM9.07 8.97a1.102 1.102 0 0 1 .136.755l1.902.618L13.463 6l-3.362.944-1.031 2.026zm-3.921.237a1.126 1.126 0 0 1 .487-.965l-.334-2.098-3.557 1.854 2.053 2.373 1.35-1.164zM5.09 11.78a1.105 1.105 0 0 1 .853-.228l.961-1.834-2.818-1.58-1.968 2.771 2.972.87zm-.815 3.258a1.125 1.125 0 0 1 .546.868l2.095-.096-1.045-3.011-2.673 1.15 1.077 1.09zm2.392 3.349a1.108 1.108 0 0 1-.093-.78l-1.81-.627-1.834 2.96 3.196-1.295.54-.258zm3.926.409a1.108 1.108 0 0 1-.66-.498l-1.91.475.771 3.33 2.566-1.986-.767-1.321zm3.94-1.207a1.125 1.125 0 0 1-.807-.486l-1.58 1.193 2.099 2.48 1.746-2.936-1.458-.251zm3.91-2.692a1.103 1.103 0 0 1-.512-.865l-2.01.021L14.698 17l2.673-1.133-1.096-1.088zm.789-3.157a1.101 1.101 0 0 1-.56-.866l-2.009-.07-.505 3.228 2.655-1.15.42-1.142zm-2.192-3.477a1.112 1.112 0 0 1 .153-.782l-1.779-.551-1.38 2.997 3.184-1.446-.178-.218z"/>
                  </svg>
                  <h3 className="text-xl font-semibold">Java</h3>
                </div>
                <p className="text-gray-300 mb-4">
                  Java SDK with full support for enterprise applications.
                </p>
                <div className="flex justify-between">
                  <a href="#" className="text-blue-400 hover:text-blue-300">Documentation</a>
                  <a href="#" className="text-blue-400 hover:text-blue-300">GitHub</a>
                </div>
              </div>
              
              <div className="bg-blue-900 p-5 rounded-lg shadow-lg border border-blue-700">
                <div className="flex items-center mb-4">
                  <svg className="h-8 w-8 text-purple-400 mr-3" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M24 8.77h-7.46v-.77h7.46v.77zm-7.46 2.31h7.46v.77h-7.46v-.77zm0 3.08h7.46v.77h-7.46v-.77zm0 3.08h7.46v.77h-7.46v-.77zm-1.54-8.31L9.85 12l5.15 3.08v-6.16zM9.85 12l-9.85 5.89V6.11L9.85 12z"/>
                  </svg>
                  <h3 className="text-xl font-semibold">C#</h3>
                </div>
                <p className="text-gray-300 mb-4">
                  .NET SDK for Windows and cross-platform applications.
                </p>
                <div className="flex justify-between">
                  <a href="#" className="text-blue-400 hover:text-blue-300">Documentation</a>
                  <a href="#" className="text-blue-400 hover:text-blue-300">GitHub</a>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Code Samples Section */}
        <div id="code-samples" className="mb-12">
          <h2 className="text-2xl font-bold mb-6">Code Samples</h2>
          <div className="bg-secondary border border-gray-700 rounded-lg p-6">
            <p className="mb-6">
              Get started quickly with these code samples for common use cases.
            </p>
            
            <div className="mb-6">
              <h3 className="text-xl font-semibold mb-4">Authentication Example</h3>
              <div className="bg-gray-900 p-4 rounded-lg">
                <pre className="text-green-400 overflow-x-auto">
{`// JavaScript Example
const novafuse = require('novafuse-sdk');

// Initialize the client with your API key
const client = new novafuse.Client({
  apiKey: 'YOUR_API_KEY',
  environment: 'sandbox' // or 'production'
});

// Authenticate and get a session token
async function authenticate() {
  try {
    const session = await client.auth.login({
      username: 'your-username',
      password: 'your-password'
    });
    
    console.log('Authentication successful!');
    console.log('Session token:', session.token);
    
    return session;
  } catch (error) {
    console.error('Authentication failed:', error.message);
  }
}`}
                </pre>
              </div>
            </div>
            
            <div>
              <h3 className="text-xl font-semibold mb-4">API Request Example</h3>
              <div className="bg-gray-900 p-4 rounded-lg">
                <pre className="text-green-400 overflow-x-auto">
{`// JavaScript Example
const novafuse = require('novafuse-sdk');

// Initialize the client with your API key
const client = new novafuse.Client({
  apiKey: 'YOUR_API_KEY',
  environment: 'sandbox' // or 'production'
});

// Make a request to the Governance API
async function getComplianceFrameworks() {
  try {
    const frameworks = await client.compliance.getFrameworks({
      status: 'active',
      limit: 10
    });
    
    console.log('Frameworks retrieved:', frameworks.length);
    frameworks.forEach(framework => {
      console.log(\`- \${framework.name} (\${framework.version})\`);
    });
    
    return frameworks;
  } catch (error) {
    console.error('Failed to retrieve frameworks:', error.message);
  }
}`}
                </pre>
              </div>
            </div>
          </div>
        </div>

        {/* Developer Forum Section */}
        <div id="forum" className="mb-12">
          <h2 className="text-2xl font-bold mb-6">Developer Forum</h2>
          <div className="bg-secondary border border-gray-700 rounded-lg p-6">
            <p className="mb-6">
              Connect with other developers and get help from the NovaFuse team in our developer forum.
            </p>
            
            <div className="text-center">
              <p className="mb-4">
                The developer forum is available to registered developers. Sign up or sign in to access the forum.
              </p>
              <div className="flex flex-wrap justify-center gap-4">
                <Link href="/sign-in" className="accent-bg text-white px-6 py-3 rounded-lg font-bold hover:bg-blue-700 inline-block">
                  Sign In to Access Forum
                </Link>
                <Link href="/sign-up" className="border border-gray-600 text-white px-6 py-3 rounded-lg font-bold hover:bg-gray-800 inline-block">
                  Create Developer Account
                </Link>
              </div>
            </div>
          </div>
        </div>

        {/* FAQ Section */}
        <div id="faq" className="mb-12">
          <h2 className="text-2xl font-bold mb-6">Frequently Asked Questions</h2>
          <div className="bg-secondary border border-gray-700 rounded-lg p-6">
            <div className="space-y-6">
              <div>
                <h3 className="text-xl font-semibold mb-2">How do I get an API key?</h3>
                <p className="text-gray-300">
                  You can get an API key by signing up for a developer account and creating a new project in the developer dashboard.
                </p>
              </div>
              
              <div>
                <h3 className="text-xl font-semibold mb-2">What are the rate limits for the APIs?</h3>
                <p className="text-gray-300">
                  Rate limits vary by API and subscription tier. Basic developer accounts have a limit of 1000 requests per day. Enterprise accounts have higher limits.
                </p>
              </div>
              
              <div>
                <h3 className="text-xl font-semibold mb-2">How do I report a bug or request a feature?</h3>
                <p className="text-gray-300">
                  You can report bugs and request features in the developer forum or by contacting support directly.
                </p>
              </div>
              
              <div>
                <h3 className="text-xl font-semibold mb-2">Are there any usage fees for the APIs?</h3>
                <p className="text-gray-300">
                  NovaFuse offers a tiered pricing model. There is a free tier for development and testing, and paid tiers for production use.
                </p>
              </div>
              
              <div>
                <h3 className="text-xl font-semibold mb-2">How do I transition from sandbox to production?</h3>
                <p className="text-gray-300">
                  When you're ready to go live, you can request production access in the developer dashboard. Our team will review your request and provide production credentials.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="bg-blue-900 p-8 rounded-lg text-center">
          <h2 className="text-2xl font-bold mb-4">Ready to Start Building?</h2>
          <p className="text-xl mb-6 max-w-3xl mx-auto">
            Create a developer account today and start building with NovaFuse APIs.
          </p>
          <div className="flex flex-wrap justify-center gap-4">
            <Link href="/sign-up" className="bg-white text-blue-600 px-6 py-3 rounded-lg font-bold hover:bg-blue-50 inline-block">
              Create Developer Account
            </Link>
            <Link href="/api-docs" className="border border-white text-white px-6 py-3 rounded-lg font-bold hover:bg-blue-800 inline-block">
              Explore API Docs
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
