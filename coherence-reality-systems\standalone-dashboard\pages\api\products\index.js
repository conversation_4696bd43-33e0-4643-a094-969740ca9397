export default function handler(req, res) {
    const products = [
        {
            id: 1,
            name: 'Conscious AI Assistant',
            category: 'AI Services',
            price: 99.99,
            description: 'Advanced AI assistant with ethical considerations',
            ethicalScore: 85
        },
        {
            id: 2,
            name: 'Ethical Computing Suite',
            category: 'Software',
            price: 199.99,
            description: 'Suite of tools for ethical computing practices',
            ethicalScore: 92
        },
        {
            id: 3,
            name: 'Triadic Optimization Tool',
            category: 'Tools',
            price: 299.99,
            description: 'Optimization tool for triadic relationships',
            ethicalScore: 88
        }
    ];

    const { query } = req.query;
    
    const filteredProducts = query 
        ? products.filter(p => 
            p.name.toLowerCase().includes(query.toLowerCase()) ||
            p.category.toLowerCase().includes(query.toLowerCase())
        )
        : products;

    res.status(200).json(filteredProducts);
}
