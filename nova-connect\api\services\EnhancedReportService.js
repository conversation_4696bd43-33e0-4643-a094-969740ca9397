/**
 * Enhanced Report Service
 *
 * This service provides advanced reporting functionality including:
 * - Compliance reporting
 * - Performance reporting
 * - Security reporting
 * - Custom reporting with advanced filtering
 * - Multiple export formats (PDF, CSV, Excel, JSON)
 * - Report scheduling and distribution
 */

const fs = require('fs').promises;
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const { ValidationError, NotFoundError, AuthorizationError } = require('../utils/errors');
const ReportService = require('./ReportService');
const AuditService = require('./AuditService');
const ConnectorService = require('./ConnectorService');
const logger = require('../../config/logger');
const PDFDocument = require('pdfkit');
const ExcelJS = require('exceljs');
const { createObjectCsvWriter } = require('csv-writer');
const nodemailer = require('nodemailer');
const cron = require('node-cron');
const moment = require('moment-timezone');

class EnhancedReportService extends ReportService {
  constructor(dataDir = path.join(__dirname, '../data')) {
    super(dataDir);
    
    // Additional directories for enhanced reporting
    this.reportsOutputDir = path.join(this.reportsDir, 'output');
    this.reportTemplatesDir = path.join(this.reportsDir, 'templates');
    this.scheduledReportsDir = path.join(this.reportsDir, 'scheduled');
    
    // Ensure directories exist
    this.ensureDirectories();
    
    // Initialize services
    this.auditService = new AuditService(dataDir);
    this.connectorService = new ConnectorService(dataDir);
    
    // Initialize scheduled reports
    this.initializeScheduledReports();
  }
  
  /**
   * Ensure all required directories exist
   */
  async ensureDirectories() {
    try {
      await fs.mkdir(this.reportsOutputDir, { recursive: true });
      await fs.mkdir(this.reportTemplatesDir, { recursive: true });
      await fs.mkdir(this.scheduledReportsDir, { recursive: true });
    } catch (error) {
      logger.error('Error creating report directories', { error: error.message });
    }
  }
  
  /**
   * Initialize scheduled reports
   */
  async initializeScheduledReports() {
    try {
      const scheduledReports = await this.loadData(this.scheduledReportsFile, []);
      
      // Set up cron jobs for each scheduled report
      scheduledReports.forEach(report => {
        if (report.active) {
          this.scheduleReportJob(report);
        }
      });
      
      logger.info(`Initialized ${scheduledReports.length} scheduled reports`);
    } catch (error) {
      logger.error('Error initializing scheduled reports', { error: error.message });
    }
  }
  
  /**
   * Schedule a report job
   * 
   * @param {Object} report - Scheduled report configuration
   */
  scheduleReportJob(report) {
    try {
      const { id, schedule, parameters, format } = report;
      
      // Convert schedule to cron expression
      let cronExpression;
      
      switch (schedule.type) {
        case 'daily':
          cronExpression = `0 ${schedule.hour || 0} * * *`;
          break;
        case 'weekly':
          cronExpression = `0 ${schedule.hour || 0} * * ${schedule.dayOfWeek || 1}`;
          break;
        case 'monthly':
          cronExpression = `0 ${schedule.hour || 0} ${schedule.dayOfMonth || 1} * *`;
          break;
        case 'quarterly':
          // Run on the 1st day of Jan, Apr, Jul, Oct
          cronExpression = `0 ${schedule.hour || 0} 1 1,4,7,10 *`;
          break;
        case 'custom':
          cronExpression = schedule.cronExpression;
          break;
        default:
          throw new Error(`Invalid schedule type: ${schedule.type}`);
      }
      
      // Schedule the job
      cron.schedule(cronExpression, async () => {
        try {
          // Check if the report should run (based on start/end dates)
          const now = new Date();
          
          if (schedule.startDate && new Date(schedule.startDate) > now) {
            logger.info(`Scheduled report ${id} not running yet (start date in future)`);
            return;
          }
          
          if (schedule.endDate && new Date(schedule.endDate) < now) {
            logger.info(`Scheduled report ${id} expired (end date in past)`);
            
            // Deactivate the report
            await this.updateScheduledReport(id, { active: false });
            return;
          }
          
          // Generate the report
          logger.info(`Running scheduled report ${id}`);
          
          const generatedReport = await this.generateReportByType(
            report.type,
            parameters,
            format,
            report.userId
          );
          
          // Send the report to recipients
          if (report.recipients && report.recipients.length > 0) {
            await this.sendReportByEmail(generatedReport.id, report.recipients);
          }
          
          logger.info(`Completed scheduled report ${id}`);
        } catch (error) {
          logger.error(`Error running scheduled report ${id}`, { error: error.message });
        }
      }, {
        timezone: schedule.timezone || 'UTC'
      });
      
      logger.info(`Scheduled report ${id} with cron: ${cronExpression}`);
    } catch (error) {
      logger.error(`Error scheduling report job ${report.id}`, { error: error.message });
    }
  }
  
  /**
   * Generate a report based on type
   * 
   * @param {string} type - Report type
   * @param {Object} parameters - Report parameters
   * @param {string} format - Report format
   * @param {string} userId - User ID
   * @returns {Object} - Generated report
   */
  async generateReportByType(type, parameters, format, userId) {
    switch (type) {
      case 'compliance':
        return this.generateComplianceReport({ ...parameters, format, userId });
      case 'performance':
        return this.generatePerformanceReport({ ...parameters, format, userId });
      case 'security':
        return this.generateSecurityReport({ ...parameters, format, userId });
      case 'custom':
        return this.generateCustomReport({ ...parameters, format, userId });
      default:
        throw new ValidationError(`Unsupported report type: ${type}`);
    }
  }
  
  /**
   * Generate a compliance report
   * 
   * @param {Object} options - Report options
   * @returns {Object} - Generated report
   */
  async generateComplianceReport(options) {
    const {
      framework,
      startDate,
      endDate,
      includeControls = true,
      includeFindings = true,
      includeRemediation = true,
      format = 'pdf',
      userId
    } = options;
    
    // Create report record
    const report = {
      id: uuidv4(),
      type: 'compliance',
      name: `Compliance Report - ${framework}`,
      description: `Compliance report for ${framework} framework`,
      parameters: {
        framework,
        startDate,
        endDate,
        includeControls,
        includeFindings,
        includeRemediation
      },
      format,
      status: 'generating',
      createdBy: userId,
      created: new Date().toISOString(),
      updated: new Date().toISOString()
    };
    
    // Save report record
    const reports = await this.loadData(this.reportsFile, []);
    reports.push(report);
    await this.saveData(this.reportsFile, reports);
    
    // Generate report asynchronously
    this.generateComplianceReportFile(report.id)
      .catch(error => {
        logger.error(`Error generating compliance report ${report.id}`, { error: error.message });
        this.updateReportStatus(report.id, 'failed', error.message);
      });
    
    // Log audit event
    await this.auditService.logEvent({
      userId,
      action: 'GENERATE',
      resourceType: 'report',
      resourceId: report.id,
      details: {
        type: 'compliance',
        framework,
        format
      }
    });
    
    return report;
  }
  
  /**
   * Generate a performance report
   * 
   * @param {Object} options - Report options
   * @returns {Object} - Generated report
   */
  async generatePerformanceReport(options) {
    const {
      metrics = ['api_calls', 'response_time', 'error_rate'],
      startDate,
      endDate,
      interval = 'day',
      connectorIds = [],
      format = 'pdf',
      userId
    } = options;
    
    // Create report record
    const report = {
      id: uuidv4(),
      type: 'performance',
      name: `Performance Report`,
      description: `Performance report for ${startDate} to ${endDate}`,
      parameters: {
        metrics,
        startDate,
        endDate,
        interval,
        connectorIds
      },
      format,
      status: 'generating',
      createdBy: userId,
      created: new Date().toISOString(),
      updated: new Date().toISOString()
    };
    
    // Save report record
    const reports = await this.loadData(this.reportsFile, []);
    reports.push(report);
    await this.saveData(this.reportsFile, reports);
    
    // Generate report asynchronously
    this.generatePerformanceReportFile(report.id)
      .catch(error => {
        logger.error(`Error generating performance report ${report.id}`, { error: error.message });
        this.updateReportStatus(report.id, 'failed', error.message);
      });
    
    // Log audit event
    await this.auditService.logEvent({
      userId,
      action: 'GENERATE',
      resourceType: 'report',
      resourceId: report.id,
      details: {
        type: 'performance',
        metrics,
        format
      }
    });
    
    return report;
  }
  
  /**
   * Generate a security report
   * 
   * @param {Object} options - Report options
   * @returns {Object} - Generated report
   */
  async generateSecurityReport(options) {
    const {
      securityDomains = ['authentication', 'authorization', 'data_protection', 'network_security'],
      startDate,
      endDate,
      includeIncidents = true,
      includeVulnerabilities = true,
      includeMitigations = true,
      format = 'pdf',
      userId
    } = options;
    
    // Create report record
    const report = {
      id: uuidv4(),
      type: 'security',
      name: `Security Report`,
      description: `Security report for ${startDate} to ${endDate}`,
      parameters: {
        securityDomains,
        startDate,
        endDate,
        includeIncidents,
        includeVulnerabilities,
        includeMitigations
      },
      format,
      status: 'generating',
      createdBy: userId,
      created: new Date().toISOString(),
      updated: new Date().toISOString()
    };
    
    // Save report record
    const reports = await this.loadData(this.reportsFile, []);
    reports.push(report);
    await this.saveData(this.reportsFile, reports);
    
    // Generate report asynchronously
    this.generateSecurityReportFile(report.id)
      .catch(error => {
        logger.error(`Error generating security report ${report.id}`, { error: error.message });
        this.updateReportStatus(report.id, 'failed', error.message);
      });
    
    // Log audit event
    await this.auditService.logEvent({
      userId,
      action: 'GENERATE',
      resourceType: 'report',
      resourceId: report.id,
      details: {
        type: 'security',
        securityDomains,
        format
      }
    });
    
    return report;
  }
}

module.exports = new EnhancedReportService();
