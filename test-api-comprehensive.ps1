# Comprehensive test script for NovaFuse API Superstore

Write-Host "Running comprehensive tests for NovaFuse API Superstore..." -ForegroundColor Cyan
Write-Host "============================================================" -ForegroundColor Cyan
Write-Host ""

# Test UI Components
Write-Host "Testing UI Components..." -ForegroundColor Yellow
Write-Host "------------------------" -ForegroundColor Yellow

# Test Marketplace UI
Write-Host "Testing Marketplace UI..."
try {
    $response = Invoke-WebRequest -Uri "http://localhost:3000" -ErrorAction Stop
    Write-Host "? Marketplace UI is working!" -ForegroundColor Green
    Write-Host "  Status: $($response.StatusCode) $($response.StatusDescription)"
} catch {
    Write-Host "? Marketplace UI test failed: $_" -ForegroundColor Red
}
Write-Host ""

# Test API Documentation
Write-Host "Testing API Documentation..."
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8080" -ErrorAction Stop
    Write-Host "? API Documentation is working!" -ForegroundColor Green
    Write-Host "  Status: $($response.StatusCode) $($response.StatusDescription)"
} catch {
    Write-Host "? API Documentation test failed: $_" -ForegroundColor Red
}
Write-Host ""

# Test Kong Admin API
Write-Host "Testing Kong Admin API..."
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8001" -ErrorAction Stop
    Write-Host "? Kong Admin API is working!" -ForegroundColor Green
    Write-Host "  Status: $($response.StatusCode) $($response.StatusDescription)"
} catch {
    Write-Host "? Kong Admin API test failed: $_" -ForegroundColor Red
}
Write-Host ""

# Test Direct API Endpoints
Write-Host "Testing Direct API Endpoints..." -ForegroundColor Yellow
Write-Host "-----------------------------" -ForegroundColor Yellow

# Test Governance API directly
Write-Host "Testing Governance API directly..."
try {
    $response = Invoke-RestMethod -Uri "http://localhost:3001/governance/board/meetings" -ErrorAction Stop
    Write-Host "? Governance API is working!" -ForegroundColor Green
    Write-Host "  Found $($response.data.Count) board meetings"
    Write-Host "  Sample data: $($response.data[0].title) - $($response.data[0].status)"
} catch {
    Write-Host "? Governance API test failed: $_" -ForegroundColor Red
}
Write-Host ""

# Test Security API directly
Write-Host "Testing Security API directly..."
try {
    $response = Invoke-RestMethod -Uri "http://localhost:3002/security/vulnerabilities" -ErrorAction Stop
    Write-Host "? Security API is working!" -ForegroundColor Green
    Write-Host "  Found $($response.data.Count) vulnerabilities"
    if ($response.data.Count -gt 0) {
        Write-Host "  Sample data: $($response.data[0].title) - $($response.data[0].severity)"
    }
} catch {
    Write-Host "? Security API test failed: $_" -ForegroundColor Red
}
Write-Host ""

# Test APIs API directly
Write-Host "Testing APIs API directly..."
try {
    $response = Invoke-RestMethod -Uri "http://localhost:3003/apis/catalog" -ErrorAction Stop
    Write-Host "? APIs API is working!" -ForegroundColor Green
    Write-Host "  Found $($response.data.Count) APIs in the catalog"
    if ($response.data.Count -gt 0) {
        Write-Host "  Sample data: $($response.data[0].name) - $($response.data[0].status)"
    }
} catch {
    Write-Host "? APIs API test failed: $_" -ForegroundColor Red
}
Write-Host ""

# Test Kong API Gateway
Write-Host "Testing Kong API Gateway..." -ForegroundColor Yellow
Write-Host "-------------------------" -ForegroundColor Yellow

# Test Kong API Gateway configuration
Write-Host "Testing Kong API Gateway configuration..."
try {
    $services = Invoke-RestMethod -Uri "http://localhost:8001/services" -ErrorAction Stop
    Write-Host "? Kong services configuration is working!" -ForegroundColor Green
    Write-Host "  Found $($services.data.Count) services"
    foreach ($service in $services.data) {
        Write-Host "  - Service: $($service.name), Host: $($service.host), Port: $($service.port)"
    }
} catch {
    Write-Host "? Kong services configuration test failed: $_" -ForegroundColor Red
}
Write-Host ""

# Test Kong routes
Write-Host "Testing Kong routes..."
try {
    $routes = Invoke-RestMethod -Uri "http://localhost:8001/routes" -ErrorAction Stop
    Write-Host "? Kong routes configuration is working!" -ForegroundColor Green
    Write-Host "  Found $($routes.data.Count) routes"
    foreach ($route in $routes.data) {
        Write-Host "  - Route: $($route.name), Paths: $($route.paths -join ", "), Service: $($route.service.id)"
    }
} catch {
    Write-Host "? Kong routes configuration test failed: $_" -ForegroundColor Red
}
Write-Host ""

# Test Kong plugins
Write-Host "Testing Kong plugins..."
try {
    $plugins = Invoke-RestMethod -Uri "http://localhost:8001/plugins" -ErrorAction Stop
    Write-Host "? Kong plugins configuration is working!" -ForegroundColor Green
    Write-Host "  Found $($plugins.data.Count) plugins"
    foreach ($plugin in $plugins.data) {
        Write-Host "  - Plugin: $($plugin.name), Enabled: $($plugin.enabled)"
    }
} catch {
    Write-Host "? Kong plugins configuration test failed: $_" -ForegroundColor Red
}
Write-Host ""

# Test API through Kong
Write-Host "Testing API through Kong..."
try {
    $response = Invoke-RestMethod -Uri "http://localhost:8000/governance/board/meetings" -Headers @{apikey = "test-api-key"} -ErrorAction SilentlyContinue
    Write-Host "? API through Kong is working!" -ForegroundColor Green
    Write-Host "  Found $($response.data.Count) board meetings"
} catch {
    Write-Host "? API through Kong test failed: $_" -ForegroundColor Red
    Write-Host "  Note: This may be due to DNS resolution issues between containers."
    Write-Host "  Try accessing the API services directly instead."
}
Write-Host ""

Write-Host "Comprehensive tests completed!" -ForegroundColor Cyan
Write-Host "============================================================" -ForegroundColor Cyan
