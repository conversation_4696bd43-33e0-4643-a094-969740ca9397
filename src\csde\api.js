/**
 * CSDE API
 * 
 * This module provides a simple Express API for the CSDE engine.
 */

const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');
const { CSDEEngine } = require('./index');

// Initialize Express app
const app = express();
const port = process.env.PORT || 3010;

// Middleware
app.use(cors());
app.use(bodyParser.json());

// Initialize CSDE Engine
const csdeEngine = new CSDEEngine();

// Routes
app.get('/', (req, res) => {
  res.json({
    name: 'CSDE API',
    version: '1.0.0',
    description: 'API for the Cyber-Safety Dominance Equation (CSDE) Engine',
    endpoints: [
      {
        path: '/calculate',
        method: 'POST',
        description: 'Calculate CSDE value',
        body: {
          complianceData: 'Object - Compliance data',
          gcpData: 'Object - GCP integration data',
          cyberSafetyData: 'Object - Cyber-Safety data'
        }
      },
      {
        path: '/metrics',
        method: 'GET',
        description: 'Get CSDE engine performance metrics'
      }
    ]
  });
});

// Calculate CSDE
app.post('/calculate', (req, res) => {
  try {
    const { complianceData, gcpData, cyberSafetyData } = req.body;
    
    // Validate input
    if (!complianceData || !gcpData || !cyberSafetyData) {
      return res.status(400).json({
        error: 'Missing required data',
        message: 'complianceData, gcpData, and cyberSafetyData are required'
      });
    }
    
    // Calculate CSDE
    const result = csdeEngine.calculate(complianceData, gcpData, cyberSafetyData);
    
    // Return result
    res.json({
      success: true,
      result
    });
  } catch (error) {
    console.error('Error calculating CSDE:', error);
    res.status(500).json({
      error: 'CSDE calculation failed',
      message: error.message
    });
  }
});

// Get metrics
app.get('/metrics', (req, res) => {
  try {
    const metrics = csdeEngine.getMetrics();
    res.json({
      success: true,
      metrics
    });
  } catch (error) {
    console.error('Error getting metrics:', error);
    res.status(500).json({
      error: 'Failed to get metrics',
      message: error.message
    });
  }
});

// Clear cache
app.post('/clear-cache', (req, res) => {
  try {
    csdeEngine.clearCache();
    res.json({
      success: true,
      message: 'Cache cleared successfully'
    });
  } catch (error) {
    console.error('Error clearing cache:', error);
    res.status(500).json({
      error: 'Failed to clear cache',
      message: error.message
    });
  }
});

// Start server
if (require.main === module) {
  app.listen(port, () => {
    console.log(`CSDE API listening on port ${port}`);
    console.log(`Visit http://localhost:${port} to see available endpoints`);
  });
}

module.exports = app;
