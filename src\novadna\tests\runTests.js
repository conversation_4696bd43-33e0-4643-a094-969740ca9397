/**
 * Test Runner for NovaDNA
 * 
 * This script runs all tests for the NovaDNA system.
 */

const { testNovaVisionIntegration } = require('./integration/NovaVisionIntegrationTest');
const { testHealthcareIntegration } = require('./integration/HealthcareIntegrationTest');

/**
 * Run all tests
 */
async function runAllTests() {
  console.log('=== NovaDNA Test Runner ===');
  console.log('Running all tests...\n');
  
  let allTestsPassed = true;
  
  try {
    // Run NovaVision integration tests
    console.log('=== NovaVision Integration Tests ===');
    const novaVisionResult = testNovaVisionIntegration();
    
    if (novaVisionResult.success) {
      console.log('✅ NovaVision integration tests passed');
      console.log(`- Generated ${Object.keys(novaVisionResult.schemas).length} UI schemas`);
    } else {
      console.error('❌ NovaVision integration tests failed');
      allTestsPassed = false;
    }
    
    console.log('');
    
    // Run healthcare integration tests
    console.log('=== Healthcare Integration Tests ===');
    const healthcareResult = testHealthcareIntegration();
    
    if (healthcareResult.success) {
      console.log('✅ Healthcare integration tests passed');
      console.log(`- Created emergency session: ${healthcareResult.session.sessionId}`);
      console.log(`- Active sessions: ${healthcareResult.activeSessions.length}`);
      console.log(`- Connected providers: ${Object.keys(healthcareResult.status.providers).length}`);
    } else {
      console.error('❌ Healthcare integration tests failed');
      allTestsPassed = false;
    }
    
    console.log('');
    
    // Summary
    if (allTestsPassed) {
      console.log('=== Test Summary ===');
      console.log('✅ All tests passed successfully!');
    } else {
      console.log('=== Test Summary ===');
      console.error('❌ Some tests failed. See above for details.');
      process.exit(1);
    }
  } catch (error) {
    console.error('Error running tests:', error);
    process.exit(1);
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runAllTests();
}

module.exports = {
  runAllTests
};
