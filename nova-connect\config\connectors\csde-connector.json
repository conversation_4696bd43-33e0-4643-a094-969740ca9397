{"id": "csde-connector", "name": "CSDE Connector", "description": "Connector for the Cyber-Safety Domain Engine (CSDE)", "version": "1.0.0", "type": "transformation", "csdeApiUrl": "${CSDE_API_URL}", "enableCaching": true, "enableMetrics": true, "cacheSize": 1000, "domain": "security", "rules": [{"source": "id", "target": "finding_id", "transform": "uppercase"}, {"source": "source", "target": "source_system", "transform": "lowercase"}, {"source": "severity", "target": "risk_level", "transform": ["uppercase", "trim"]}, {"source": "category", "target": "finding_type", "transform": "lowercase"}, {"source": "createTime", "target": "created_at"}, {"source": "updateTime", "target": "updated_at"}, {"source": "resourceName", "target": "asset.full_path"}, {"source": "resourceType", "target": "asset.type", "transform": "split", "transformParams": "."}, {"source": "state", "target": "status", "transform": "lowercase"}, {"source": "externalUri", "target": "links.console"}, {"source": "sourceProperties.vulnerabilityType", "target": "details.vulnerability_type", "transform": "lowercase"}, {"source": "sourceProperties.vulnerabilityDetails.cvssScore", "target": "details.cvss_score", "transform": "toNumber"}, {"source": "sourceProperties.vulnerabilityDetails.cvssVector", "target": "details.cvss_vector"}, {"source": "sourceProperties.vulnerabilityDetails.cve", "target": "details.cve_id"}, {"source": "sourceProperties.vulnerabilityDetails.description", "target": "details.description"}, {"source": "sourceProperties.vulnerabilityDetails.references", "target": "details.references"}, {"source": "sourceProperties.vulnerabilityDetails.fixAvailable", "target": "details.fix_available"}, {"source": "sourceProperties.vulnerabilityDetails.exploitAvailable", "target": "details.exploit_available"}, {"source": "securityMarks.marks.criticality", "target": "metadata.criticality"}, {"source": "securityMarks.marks.compliance", "target": "metadata.compliance_frameworks", "transform": "split", "transformParams": ","}, {"source": "securityMarks.marks.dataClassification", "target": "metadata.data_classification"}, {"source": "nextSteps", "target": "remediation.steps"}, {"source": "complianceState", "target": "compliance_status", "transform": "lowercase"}], "logging": {"level": "info", "format": "json"}, "retry": {"maxRetries": 3, "initialDelay": 1000, "maxDelay": 10000, "factor": 2}, "timeout": 30000, "healthCheck": {"interval": 60000, "timeout": 5000, "path": "/health"}}