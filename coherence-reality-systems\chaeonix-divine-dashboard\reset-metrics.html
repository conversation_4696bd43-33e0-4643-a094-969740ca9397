<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CHAEONIX Metrics Reset</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
            max-width: 500px;
            width: 100%;
        }
        h1 {
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        .emoji {
            font-size: 3em;
            margin-bottom: 20px;
        }
        button {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 1.2em;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px;
            transition: transform 0.2s;
        }
        button:hover {
            transform: translateY(-2px);
        }
        .success {
            background: linear-gradient(135deg, #00b894, #00a085);
        }
        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="emoji">🔄</div>
        <h1>CHAEONIX Metrics Reset</h1>
        <p>Reset all profit tracking metrics to clear simulation data and start fresh with real trading data only.</p>
        
        <button onclick="resetMetrics()">Reset All Metrics</button>
        <button onclick="resetForLive()" class="success">Reset for Live Trading</button>
        
        <div id="status"></div>
        
        <div style="margin-top: 30px; font-size: 0.9em; color: #666;">
            <p><strong>Reset Metrics:</strong> Clears all data, sets everything to $0</p>
            <p><strong>Reset for Live:</strong> Clears sample data, keeps structure for real trades</p>
        </div>
    </div>

    <script>
        async function resetMetrics() {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = '⏳ Resetting all metrics...';
            statusDiv.className = 'status';
            
            try {
                const response = await fetch('/api/analytics/profit-tracker', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'RESET_METRICS'
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    statusDiv.innerHTML = '✅ All metrics reset successfully! Dashboard now shows $0.';
                    statusDiv.className = 'status success';
                } else {
                    statusDiv.innerHTML = '❌ Reset failed: ' + (result.error || 'Unknown error');
                    statusDiv.className = 'status error';
                }
            } catch (error) {
                statusDiv.innerHTML = '❌ Network error: ' + error.message;
                statusDiv.className = 'status error';
            }
        }
        
        async function resetForLive() {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = '⏳ Resetting for live trading...';
            statusDiv.className = 'status';
            
            try {
                const response = await fetch('/api/analytics/profit-tracker', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'RESET_FOR_LIVE'
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    statusDiv.innerHTML = '✅ Reset for live trading! Ready for real trades only.';
                    statusDiv.className = 'status success';
                } else {
                    statusDiv.innerHTML = '❌ Reset failed: ' + (result.error || 'Unknown error');
                    statusDiv.className = 'status error';
                }
            } catch (error) {
                statusDiv.innerHTML = '❌ Network error: ' + error.message;
                statusDiv.className = 'status error';
            }
        }
    </script>
</body>
</html>
