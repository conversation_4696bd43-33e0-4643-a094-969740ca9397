{"id": "remediation-1744567472255", "type": "data_leak", "severity": "high", "resource": {"id": "patient_records", "type": "bigquery.dataset", "name": "patient_records", "provider": "gcp", "projectId": "demo-project"}, "finding": {"id": "finding-1744567472254", "category": "DATA_LEAK", "severity": "HIGH", "resourceName": "//bigquery.googleapis.com/projects/demo-project/datasets/patient_records", "resourceType": "bigquery.dataset", "createdAt": 1744567472254, "description": "PHI data exposed in BigQuery dataset", "dataType": "PHI", "complianceFrameworks": ["HIPAA", "GDPR"]}, "remediationSteps": [{"id": "step-1", "action": "encrypt-dataset", "parameters": {"projectId": "demo-project", "datasetId": "patient_records", "encryptionType": "AES-256", "keyRotationPeriod": "90d"}}, {"id": "step-2", "action": "update-access-controls", "parameters": {"projectId": "demo-project", "datasetId": "patient_records", "accessLevel": "restricted", "allowedRoles": ["healthcare-admin", "compliance-officer"]}}, {"id": "step-3", "action": "update-compliance-dashboard", "parameters": {"dashboardId": "hipaa-compliance-dashboard", "findingId": "finding-1744567472254", "remediationId": "remediation-1744567472255"}}]}