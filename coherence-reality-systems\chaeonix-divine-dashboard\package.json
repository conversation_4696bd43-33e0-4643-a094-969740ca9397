{"name": "chaeonix-divine-dashboard", "version": "1.0.0-COHERENCE_DRIVEN_AEONIC_INTELLIGENCE", "description": "CHAEONIX - Coherence-Driven Aeonic Intelligence Engine Dashboard for Tri-Market Domination", "private": true, "scripts": {"dev": "next dev -p 3141", "build": "next build", "start": "next start -p 3141", "lint": "next lint", "chaeonix": "npm run dev", "divine": "npm run dev", "cdaie": "npm run dev"}, "dependencies": {"@headlessui/react": "1.7.17", "@heroicons/react": "2.0.18", "@monaco-editor/react": "4.6.0", "@react-spring/three": "9.7.3", "@react-three/drei": "9.88.13", "@react-three/fiber": "8.15.11", "autoprefixer": "10.4.16", "axios": "^1.9.0", "chart.js": "4.4.0", "class-variance-authority": "0.7.0", "clsx": "2.0.0", "d3": "7.8.5", "date-fns": "2.30.0", "framer-motion": "10.16.5", "lucide-react": "0.294.0", "moment": "^2.30.1", "next": "14.0.3", "numeral": "2.0.6", "postcss": "8.4.32", "react": "18.2.0", "react-chartjs-2": "5.2.0", "react-dom": "18.2.0", "react-draggable": "4.4.6", "react-grid-layout": "1.4.4", "react-hot-toast": "2.4.1", "react-intersection-observer": "9.5.3", "react-spring": "9.7.3", "react-use-websocket": "4.5.0", "react-virtualized": "9.22.5", "recharts": "2.8.0", "socket.io-client": "4.7.4", "tailwindcss": "3.3.6", "three": "0.158.0"}, "devDependencies": {"@types/d3": "7.4.3", "@types/node": "20.9.0", "@types/numeral": "2.0.5", "@types/react": "18.2.37", "@types/react-dom": "18.2.15", "@types/three": "0.158.3", "eslint": "8.53.0", "eslint-config-next": "14.0.3", "typescript": "5.2.2"}, "keywords": ["chaeonix", "cdaie", "coherence-driven", "aeonic-intelligence", "tri-market", "divine-trading", "<PERSON><PERSON><PERSON><PERSON>", "prophecy", "stocks", "crypto", "forex"], "author": "CHAEONIX Divine Intelligence", "license": "MIT"}