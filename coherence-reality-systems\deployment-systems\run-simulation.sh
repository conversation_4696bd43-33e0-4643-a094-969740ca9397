#!/bin/bash

# KetherNet + Comphyon + NEPI Simulation Runner
# Consciousness-Validated Network Architecture Testing

echo "🚀 KetherNet Simulation Suite"
echo "⚛️ Testing Trinity of Trust Architecture"
echo "=================================================="

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Create necessary directories
mkdir -p simulation-results
mkdir -p evolution-data
mkdir -p traffic-scripts
mkdir -p aggregation-scripts
mkdir -p grafana-simulation-dashboards

echo "📁 Created simulation directories"

# Start the main Trinity stack first
echo "🔄 Starting Trinity of Trust stack..."
docker-compose -f docker-compose.yml up -d

# Wait for services to be ready
echo "⏳ Waiting for Trinity services to initialize..."
sleep 30

# Check service health
echo "🏥 Checking service health..."
services=("kethernet-blockchain:8080" "novadna-identity:8083" "novashield-security:8085")

for service in "${services[@]}"; do
    if curl -f http://localhost:${service##*:}/health > /dev/null 2>&1; then
        echo "✅ $service is healthy"
    else
        echo "⚠️ $service may not be ready yet"
    fi
done

# Run the simulation suite
echo "🧪 Starting KetherNet simulation tests..."

# Option 1: Run Python simulation directly
if [ "$1" = "direct" ]; then
    echo "🐍 Running direct Python simulation..."
    python3 kethernet-simulation-suite.py
    
# Option 2: Run full Docker simulation harness
elif [ "$1" = "docker" ]; then
    echo "🐳 Running Docker simulation harness..."
    docker-compose -f docker-simulation-harness.yml up --build
    
# Option 3: Run specific test type
elif [ "$1" = "consciousness" ]; then
    echo "🧠 Running consciousness filtering tests only..."
    python3 -c "
import asyncio
from kethernet_simulation_suite import KetherNetSimulator

async def main():
    sim = KetherNetSimulator()
    await sim.test_consciousness_filtering()
    sim.generate_simulation_report()

asyncio.run(main())
"

elif [ "$1" = "trinity" ]; then
    echo "⚛️ Running Trinity stack validation tests..."
    python3 -c "
import asyncio
from kethernet_simulation_suite import KetherNetSimulator

async def main():
    sim = KetherNetSimulator()
    await sim.test_trinity_stack_validation()
    sim.generate_simulation_report()

asyncio.run(main())
"

elif [ "$1" = "threats" ]; then
    echo "🛡️ Running threat detection tests..."
    python3 -c "
import asyncio
from kethernet_simulation_suite import KetherNetSimulator

async def main():
    sim = KetherNetSimulator()
    await sim.test_threat_detection_auto_blocking()
    sim.generate_simulation_report()

asyncio.run(main())
"

elif [ "$1" = "evolution" ]; then
    echo "🧬 Running evolution tracking tests..."
    python3 -c "
import asyncio
from kethernet_simulation_suite import KetherNetSimulator

async def main():
    sim = KetherNetSimulator()
    await sim.test_evolution_tracking()
    sim.generate_simulation_report()

asyncio.run(main())
"

elif [ "$1" = "nepi" ]; then
    echo "🧬 Running NEPI adaptive behavior tests..."
    python3 -c "
import asyncio
from kethernet_simulation_suite import KetherNetSimulator

async def main():
    sim = KetherNetSimulator()
    await sim.test_nepi_adaptive_behavior()
    sim.generate_simulation_report()

asyncio.run(main())
"

# Default: Run all tests
else
    echo "🌟 Running complete simulation suite..."
    echo ""
    echo "Available options:"
    echo "  ./run-simulation.sh direct      - Run Python simulation directly"
    echo "  ./run-simulation.sh docker      - Run full Docker simulation harness"
    echo "  ./run-simulation.sh consciousness - Test consciousness filtering only"
    echo "  ./run-simulation.sh trinity     - Test Trinity stack only"
    echo "  ./run-simulation.sh threats     - Test threat detection only"
    echo "  ./run-simulation.sh evolution   - Test evolution tracking only"
    echo "  ./run-simulation.sh nepi        - Test NEPI adaptive behavior only"
    echo ""
    echo "🚀 Running complete simulation in 5 seconds..."
    sleep 5
    
    python3 kethernet-simulation-suite.py
fi

echo ""
echo "=================================================="
echo "✅ Simulation complete!"
echo "📊 Check simulation-results/ for detailed reports"
echo "📈 View dashboards at:"
echo "   - Trinity Dashboard: http://localhost:3000"
echo "   - Simulation Dashboard: http://localhost:3001"
echo "   - Prometheus Metrics: http://localhost:9090"
echo "=================================================="

# Optional: Open dashboards automatically
if command -v xdg-open > /dev/null; then
    echo "🌐 Opening dashboards..."
    xdg-open http://localhost:3000 > /dev/null 2>&1 &
    xdg-open http://localhost:3001 > /dev/null 2>&1 &
elif command -v open > /dev/null; then
    echo "🌐 Opening dashboards..."
    open http://localhost:3000 > /dev/null 2>&1 &
    open http://localhost:3001 > /dev/null 2>&1 &
fi
