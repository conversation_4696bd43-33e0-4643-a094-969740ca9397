/**
 * ESG Initiatives API - Validation Schemas
 * 
 * This file defines the validation schemas for the ESG Initiatives API.
 */

const Joi = require('joi');

// Common validation schemas
const idSchema = Joi.string().regex(/^[0-9a-fA-F]{24}$/);
const dateSchema = Joi.date().iso();

// Initiative validation schemas
const initiativeSchema = {
  create: Joi.object({
    name: Joi.string().required(),
    description: Joi.string().required(),
    category: Joi.string().valid('Environmental', 'Social', 'Governance').required(),
    subcategory: Joi.string().required(),
    status: Joi.string().valid('Planned', 'In Progress', 'Completed', 'Cancelled').default('Planned'),
    startDate: dateSchema.required(),
    endDate: dateSchema.required(),
    budget: Joi.object({
      amount: Joi.number(),
      currency: Joi.string()
    }),
    owner: Joi.string().required(),
    stakeholders: Joi.array().items(Joi.object({
      name: Joi.string().required(),
      role: Joi.string().required(),
      email: Joi.string().email()
    })),
    goals: Joi.array().items(Joi.object({
      description: Joi.string().required(),
      targetDate: dateSchema.required(),
      status: Joi.string().valid('Not Started', 'In Progress', 'Completed', 'Cancelled').default('Not Started'),
      metrics: Joi.array().items(Joi.object({
        metric: idSchema.required(),
        targetValue: Joi.any()
      }))
    })),
    relatedMetrics: Joi.array().items(idSchema),
    sdgAlignment: Joi.array().items(Joi.number().integer().min(1).max(17)),
    documents: Joi.array().items(Joi.object({
      name: Joi.string().required(),
      description: Joi.string(),
      fileUrl: Joi.string().required(),
      fileType: Joi.string(),
      uploadDate: dateSchema.default(new Date().toISOString()),
      uploadedBy: Joi.string()
    })),
    tags: Joi.array().items(Joi.string())
  }),
  update: Joi.object({
    name: Joi.string(),
    description: Joi.string(),
    category: Joi.string().valid('Environmental', 'Social', 'Governance'),
    subcategory: Joi.string(),
    status: Joi.string().valid('Planned', 'In Progress', 'Completed', 'Cancelled'),
    startDate: dateSchema,
    endDate: dateSchema,
    budget: Joi.object({
      amount: Joi.number(),
      currency: Joi.string()
    }),
    owner: Joi.string(),
    stakeholders: Joi.array().items(Joi.object({
      name: Joi.string().required(),
      role: Joi.string().required(),
      email: Joi.string().email()
    })),
    relatedMetrics: Joi.array().items(idSchema),
    sdgAlignment: Joi.array().items(Joi.number().integer().min(1).max(17)),
    documents: Joi.array().items(Joi.object({
      name: Joi.string().required(),
      description: Joi.string(),
      fileUrl: Joi.string().required(),
      fileType: Joi.string(),
      uploadDate: dateSchema,
      uploadedBy: Joi.string()
    })),
    tags: Joi.array().items(Joi.string())
  }),
  addGoal: Joi.object({
    description: Joi.string().required(),
    targetDate: dateSchema.required(),
    status: Joi.string().valid('Not Started', 'In Progress', 'Completed', 'Cancelled').default('Not Started'),
    metrics: Joi.array().items(Joi.object({
      metric: idSchema.required(),
      targetValue: Joi.any()
    }))
  }),
  updateGoal: Joi.object({
    description: Joi.string(),
    targetDate: dateSchema,
    status: Joi.string().valid('Not Started', 'In Progress', 'Completed', 'Cancelled'),
    metrics: Joi.array().items(Joi.object({
      metric: idSchema.required(),
      targetValue: Joi.any()
    }))
  }),
  addUpdate: Joi.object({
    content: Joi.string().required()
  })
};

// Impact Assessment validation schemas
const impactAssessmentSchema = {
  create: Joi.object({
    initiative: idSchema.required(),
    assessmentDate: dateSchema.default(new Date().toISOString()),
    environmentalImpact: Joi.object({
      score: Joi.number().integer().min(1).max(5),
      description: Joi.string(),
      metrics: Joi.array().items(Joi.object({
        metric: idSchema.required(),
        value: Joi.any(),
        notes: Joi.string()
      }))
    }),
    socialImpact: Joi.object({
      score: Joi.number().integer().min(1).max(5),
      description: Joi.string(),
      metrics: Joi.array().items(Joi.object({
        metric: idSchema.required(),
        value: Joi.any(),
        notes: Joi.string()
      }))
    }),
    governanceImpact: Joi.object({
      score: Joi.number().integer().min(1).max(5),
      description: Joi.string(),
      metrics: Joi.array().items(Joi.object({
        metric: idSchema.required(),
        value: Joi.any(),
        notes: Joi.string()
      }))
    }),
    overallImpact: Joi.object({
      score: Joi.number().integer().min(1).max(5),
      description: Joi.string()
    }),
    recommendations: Joi.array().items(Joi.object({
      description: Joi.string().required(),
      priority: Joi.string().valid('Low', 'Medium', 'High').required()
    })),
    status: Joi.string().valid('Draft', 'Completed', 'Reviewed').default('Draft')
  }),
  update: Joi.object({
    assessmentDate: dateSchema,
    environmentalImpact: Joi.object({
      score: Joi.number().integer().min(1).max(5),
      description: Joi.string(),
      metrics: Joi.array().items(Joi.object({
        metric: idSchema.required(),
        value: Joi.any(),
        notes: Joi.string()
      }))
    }),
    socialImpact: Joi.object({
      score: Joi.number().integer().min(1).max(5),
      description: Joi.string(),
      metrics: Joi.array().items(Joi.object({
        metric: idSchema.required(),
        value: Joi.any(),
        notes: Joi.string()
      }))
    }),
    governanceImpact: Joi.object({
      score: Joi.number().integer().min(1).max(5),
      description: Joi.string(),
      metrics: Joi.array().items(Joi.object({
        metric: idSchema.required(),
        value: Joi.any(),
        notes: Joi.string()
      }))
    }),
    overallImpact: Joi.object({
      score: Joi.number().integer().min(1).max(5),
      description: Joi.string()
    }),
    recommendations: Joi.array().items(Joi.object({
      description: Joi.string().required(),
      priority: Joi.string().valid('Low', 'Medium', 'High').required()
    })),
    status: Joi.string().valid('Draft', 'Completed', 'Reviewed')
  }),
  review: Joi.object({
    reviewComments: Joi.string().required()
  })
};

// Query validation schemas
const querySchema = {
  pagination: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(10),
    sort: Joi.string().default('-createdAt')
  }),
  initiativeFilters: Joi.object({
    category: Joi.string().valid('Environmental', 'Social', 'Governance'),
    status: Joi.string().valid('Planned', 'In Progress', 'Completed', 'Cancelled'),
    tag: Joi.string()
  }),
  impactAssessmentFilters: Joi.object({
    initiative: idSchema,
    status: Joi.string().valid('Draft', 'Completed', 'Reviewed')
  })
};

module.exports = {
  initiative: initiativeSchema,
  impactAssessment: impactAssessmentSchema,
  query: querySchema
};
