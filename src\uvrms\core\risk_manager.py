"""
Risk Manager for the Universal Vendor Risk Management System.

This module provides functionality for managing vendor risks.
"""

import os
import json
import logging
import uuid
from typing import Dict, List, Any, Optional

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RiskManager:
    """
    Manager for vendor risks.
    
    This class is responsible for identifying, assessing, and managing
    vendor risks.
    """
    
    def __init__(self, risks_dir: Optional[str] = None):
        """
        Initialize the Risk Manager.
        
        Args:
            risks_dir: Path to a directory for storing risk information
        """
        logger.info("Initializing Risk Manager")
        
        # Set the risks directory
        self.risks_dir = risks_dir or os.path.join(os.getcwd(), 'risk_data')
        
        # Create the risks directory if it doesn't exist
        os.makedirs(self.risks_dir, exist_ok=True)
        
        # Dictionary to store risks in memory
        self.risks: Dict[str, Dict[str, Any]] = {}
        
        # Dictionary to store risk categories
        self.risk_categories: Dict[str, Dict[str, Any]] = {}
        
        # Load risks from disk
        self._load_risks_from_disk()
        
        # Register default risk categories
        self._register_default_risk_categories()
        
        logger.info(f"Risk Manager initialized with {len(self.risks)} risks and {len(self.risk_categories)} categories")
    
    def _register_default_risk_categories(self) -> None:
        """Register default risk categories."""
        # Security risk category
        self.register_risk_category({
            'id': 'security',
            'name': 'Security Risk',
            'description': 'Risks related to information security',
            'impact_levels': {
                'low': 'Minor security impact, limited to non-sensitive systems',
                'medium': 'Moderate security impact, affecting some sensitive systems',
                'high': 'Major security impact, affecting critical systems or sensitive data'
            },
            'likelihood_levels': {
                'low': 'Unlikely to occur (less than once per year)',
                'medium': 'May occur occasionally (once per year)',
                'high': 'Likely to occur frequently (multiple times per year)'
            }
        })
        
        # Privacy risk category
        self.register_risk_category({
            'id': 'privacy',
            'name': 'Privacy Risk',
            'description': 'Risks related to data privacy',
            'impact_levels': {
                'low': 'Minor privacy impact, affecting few individuals',
                'medium': 'Moderate privacy impact, affecting many individuals',
                'high': 'Major privacy impact, affecting large numbers of individuals or sensitive data'
            },
            'likelihood_levels': {
                'low': 'Unlikely to occur (less than once per year)',
                'medium': 'May occur occasionally (once per year)',
                'high': 'Likely to occur frequently (multiple times per year)'
            }
        })
        
        # Operational risk category
        self.register_risk_category({
            'id': 'operational',
            'name': 'Operational Risk',
            'description': 'Risks related to business operations',
            'impact_levels': {
                'low': 'Minor operational impact, minimal disruption',
                'medium': 'Moderate operational impact, some disruption to business processes',
                'high': 'Major operational impact, significant disruption to business processes'
            },
            'likelihood_levels': {
                'low': 'Unlikely to occur (less than once per year)',
                'medium': 'May occur occasionally (once per year)',
                'high': 'Likely to occur frequently (multiple times per year)'
            }
        })
        
        # Financial risk category
        self.register_risk_category({
            'id': 'financial',
            'name': 'Financial Risk',
            'description': 'Risks related to financial impact',
            'impact_levels': {
                'low': 'Minor financial impact (less than $10,000)',
                'medium': 'Moderate financial impact ($10,000 - $100,000)',
                'high': 'Major financial impact (more than $100,000)'
            },
            'likelihood_levels': {
                'low': 'Unlikely to occur (less than once per year)',
                'medium': 'May occur occasionally (once per year)',
                'high': 'Likely to occur frequently (multiple times per year)'
            }
        })
        
        # Compliance risk category
        self.register_risk_category({
            'id': 'compliance',
            'name': 'Compliance Risk',
            'description': 'Risks related to regulatory compliance',
            'impact_levels': {
                'low': 'Minor compliance impact, no regulatory penalties',
                'medium': 'Moderate compliance impact, potential for minor penalties',
                'high': 'Major compliance impact, potential for significant penalties or sanctions'
            },
            'likelihood_levels': {
                'low': 'Unlikely to occur (less than once per year)',
                'medium': 'May occur occasionally (once per year)',
                'high': 'Likely to occur frequently (multiple times per year)'
            }
        })
        
        # Reputational risk category
        self.register_risk_category({
            'id': 'reputational',
            'name': 'Reputational Risk',
            'description': 'Risks related to reputation and brand image',
            'impact_levels': {
                'low': 'Minor reputational impact, limited to small audience',
                'medium': 'Moderate reputational impact, affecting broader audience',
                'high': 'Major reputational impact, affecting public perception and trust'
            },
            'likelihood_levels': {
                'low': 'Unlikely to occur (less than once per year)',
                'medium': 'May occur occasionally (once per year)',
                'high': 'Likely to occur frequently (multiple times per year)'
            }
        })
        
        # Strategic risk category
        self.register_risk_category({
            'id': 'strategic',
            'name': 'Strategic Risk',
            'description': 'Risks related to strategic objectives',
            'impact_levels': {
                'low': 'Minor strategic impact, limited effect on objectives',
                'medium': 'Moderate strategic impact, affecting some objectives',
                'high': 'Major strategic impact, affecting critical objectives'
            },
            'likelihood_levels': {
                'low': 'Unlikely to occur (less than once per year)',
                'medium': 'May occur occasionally (once per year)',
                'high': 'Likely to occur frequently (multiple times per year)'
            }
        })
    
    def register_risk_category(self, category: Dict[str, Any]) -> None:
        """
        Register a risk category.
        
        Args:
            category: The risk category
            
        Raises:
            ValueError: If the category is invalid
        """
        # Validate the category
        if 'id' not in category:
            raise ValueError("Risk category ID is required")
        
        if 'name' not in category:
            raise ValueError("Risk category name is required")
        
        if 'impact_levels' not in category or not isinstance(category['impact_levels'], dict):
            raise ValueError("Risk category must have impact levels")
        
        if 'likelihood_levels' not in category or not isinstance(category['likelihood_levels'], dict):
            raise ValueError("Risk category must have likelihood levels")
        
        # Register the category
        category_id = category['id']
        self.risk_categories[category_id] = category
        
        logger.info(f"Registered risk category: {category_id}")
    
    def get_risk_category(self, category_id: str) -> Dict[str, Any]:
        """
        Get a risk category.
        
        Args:
            category_id: The ID of the category
            
        Returns:
            The risk category
            
        Raises:
            ValueError: If the category does not exist
        """
        if category_id not in self.risk_categories:
            raise ValueError(f"Risk category not found: {category_id}")
        
        return self.risk_categories[category_id]
    
    def get_all_risk_categories(self) -> List[Dict[str, Any]]:
        """
        Get all risk categories.
        
        Returns:
            List of risk categories
        """
        return list(self.risk_categories.values())
    
    def create_risk(self, risk_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a new risk.
        
        Args:
            risk_data: The risk data
            
        Returns:
            The created risk
            
        Raises:
            ValueError: If the risk data is invalid
        """
        logger.info("Creating new risk")
        
        # Validate the risk data
        self._validate_risk_data(risk_data)
        
        # Generate a unique risk ID
        risk_id = str(uuid.uuid4())
        
        # Create the risk object
        risk = {
            'id': risk_id,
            'vendor_id': risk_data['vendor_id'],
            'category_id': risk_data['category_id'],
            'name': risk_data['name'],
            'description': risk_data.get('description', ''),
            'impact': risk_data['impact'],
            'likelihood': risk_data['likelihood'],
            'risk_level': self._calculate_risk_level(risk_data['impact'], risk_data['likelihood']),
            'controls': risk_data.get('controls', []),
            'status': risk_data.get('status', 'identified'),
            'created_at': self._get_current_timestamp(),
            'updated_at': self._get_current_timestamp()
        }
        
        # Store the risk in memory
        self.risks[risk_id] = risk
        
        # Store the risk on disk
        self._save_risk_to_disk(risk)
        
        logger.info(f"Risk created: {risk_id}")
        
        return risk
    
    def get_risk(self, risk_id: str) -> Dict[str, Any]:
        """
        Get a risk.
        
        Args:
            risk_id: The ID of the risk
            
        Returns:
            The risk
            
        Raises:
            ValueError: If the risk does not exist
        """
        logger.info(f"Getting risk: {risk_id}")
        
        if risk_id not in self.risks:
            raise ValueError(f"Risk not found: {risk_id}")
        
        return self.risks[risk_id]
    
    def update_risk(self, risk_id: str, risk_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update a risk.
        
        Args:
            risk_id: The ID of the risk
            risk_data: The updated risk data
            
        Returns:
            The updated risk
            
        Raises:
            ValueError: If the risk does not exist
            ValueError: If the risk data is invalid
        """
        logger.info(f"Updating risk: {risk_id}")
        
        # Check if the risk exists
        if risk_id not in self.risks:
            raise ValueError(f"Risk not found: {risk_id}")
        
        # Get the existing risk
        risk = self.risks[risk_id]
        
        # Update the risk data
        if 'name' in risk_data:
            risk['name'] = risk_data['name']
        
        if 'description' in risk_data:
            risk['description'] = risk_data['description']
        
        if 'impact' in risk_data:
            risk['impact'] = risk_data['impact']
            # Recalculate risk level if impact or likelihood changes
            risk['risk_level'] = self._calculate_risk_level(risk_data['impact'], risk['likelihood'])
        
        if 'likelihood' in risk_data:
            risk['likelihood'] = risk_data['likelihood']
            # Recalculate risk level if impact or likelihood changes
            risk['risk_level'] = self._calculate_risk_level(risk['impact'], risk_data['likelihood'])
        
        if 'controls' in risk_data:
            risk['controls'] = risk_data['controls']
        
        if 'status' in risk_data:
            risk['status'] = risk_data['status']
        
        # Update the updated_at timestamp
        risk['updated_at'] = self._get_current_timestamp()
        
        # Store the updated risk on disk
        self._save_risk_to_disk(risk)
        
        logger.info(f"Risk updated: {risk_id}")
        
        return risk
    
    def delete_risk(self, risk_id: str) -> None:
        """
        Delete a risk.
        
        Args:
            risk_id: The ID of the risk
            
        Raises:
            ValueError: If the risk does not exist
        """
        logger.info(f"Deleting risk: {risk_id}")
        
        # Check if the risk exists
        if risk_id not in self.risks:
            raise ValueError(f"Risk not found: {risk_id}")
        
        # Remove the risk from memory
        del self.risks[risk_id]
        
        # Remove the risk from disk
        self._delete_risk_from_disk(risk_id)
        
        logger.info(f"Risk deleted: {risk_id}")
    
    def get_vendor_risks(self, vendor_id: str) -> List[Dict[str, Any]]:
        """
        Get all risks for a vendor.
        
        Args:
            vendor_id: The ID of the vendor
            
        Returns:
            List of risks for the vendor
        """
        logger.info(f"Getting risks for vendor: {vendor_id}")
        
        return [r for r in self.risks.values() if r['vendor_id'] == vendor_id]
    
    def get_risks_by_category(self, category_id: str) -> List[Dict[str, Any]]:
        """
        Get all risks for a category.
        
        Args:
            category_id: The ID of the category
            
        Returns:
            List of risks for the category
        """
        logger.info(f"Getting risks for category: {category_id}")
        
        return [r for r in self.risks.values() if r['category_id'] == category_id]
    
    def get_risks_by_level(self, risk_level: str) -> List[Dict[str, Any]]:
        """
        Get all risks with a specific risk level.
        
        Args:
            risk_level: The risk level
            
        Returns:
            List of risks with the specified risk level
        """
        logger.info(f"Getting risks with level: {risk_level}")
        
        return [r for r in self.risks.values() if r['risk_level'] == risk_level]
    
    def get_risks_by_status(self, status: str) -> List[Dict[str, Any]]:
        """
        Get all risks with a specific status.
        
        Args:
            status: The risk status
            
        Returns:
            List of risks with the specified status
        """
        logger.info(f"Getting risks with status: {status}")
        
        return [r for r in self.risks.values() if r['status'] == status]
    
    def add_control(self, risk_id: str, control: Dict[str, Any]) -> Dict[str, Any]:
        """
        Add a control to a risk.
        
        Args:
            risk_id: The ID of the risk
            control: The control to add
            
        Returns:
            The updated risk
            
        Raises:
            ValueError: If the risk does not exist
            ValueError: If the control is invalid
        """
        logger.info(f"Adding control to risk: {risk_id}")
        
        # Check if the risk exists
        if risk_id not in self.risks:
            raise ValueError(f"Risk not found: {risk_id}")
        
        # Validate the control
        if 'name' not in control:
            raise ValueError("Control name is required")
        
        if 'description' not in control:
            raise ValueError("Control description is required")
        
        # Generate a unique control ID
        control_id = str(uuid.uuid4())
        
        # Create the control object
        control_obj = {
            'id': control_id,
            'name': control['name'],
            'description': control['description'],
            'type': control.get('type', 'preventive'),
            'status': control.get('status', 'planned'),
            'effectiveness': control.get('effectiveness', 'medium'),
            'created_at': self._get_current_timestamp()
        }
        
        # Get the existing risk
        risk = self.risks[risk_id]
        
        # Add the control to the risk
        risk['controls'].append(control_obj)
        
        # Update the updated_at timestamp
        risk['updated_at'] = self._get_current_timestamp()
        
        # Store the updated risk on disk
        self._save_risk_to_disk(risk)
        
        logger.info(f"Control added to risk: {risk_id}")
        
        return risk
    
    def update_control(self, risk_id: str, control_id: str, control_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update a control in a risk.
        
        Args:
            risk_id: The ID of the risk
            control_id: The ID of the control
            control_data: The updated control data
            
        Returns:
            The updated risk
            
        Raises:
            ValueError: If the risk does not exist
            ValueError: If the control does not exist
        """
        logger.info(f"Updating control {control_id} in risk: {risk_id}")
        
        # Check if the risk exists
        if risk_id not in self.risks:
            raise ValueError(f"Risk not found: {risk_id}")
        
        # Get the existing risk
        risk = self.risks[risk_id]
        
        # Find the control
        control = None
        for c in risk['controls']:
            if c['id'] == control_id:
                control = c
                break
        
        if not control:
            raise ValueError(f"Control not found: {control_id}")
        
        # Update the control data
        if 'name' in control_data:
            control['name'] = control_data['name']
        
        if 'description' in control_data:
            control['description'] = control_data['description']
        
        if 'type' in control_data:
            control['type'] = control_data['type']
        
        if 'status' in control_data:
            control['status'] = control_data['status']
        
        if 'effectiveness' in control_data:
            control['effectiveness'] = control_data['effectiveness']
        
        # Update the updated_at timestamp
        risk['updated_at'] = self._get_current_timestamp()
        
        # Store the updated risk on disk
        self._save_risk_to_disk(risk)
        
        logger.info(f"Control updated in risk: {risk_id}")
        
        return risk
    
    def remove_control(self, risk_id: str, control_id: str) -> Dict[str, Any]:
        """
        Remove a control from a risk.
        
        Args:
            risk_id: The ID of the risk
            control_id: The ID of the control
            
        Returns:
            The updated risk
            
        Raises:
            ValueError: If the risk does not exist
            ValueError: If the control does not exist
        """
        logger.info(f"Removing control {control_id} from risk: {risk_id}")
        
        # Check if the risk exists
        if risk_id not in self.risks:
            raise ValueError(f"Risk not found: {risk_id}")
        
        # Get the existing risk
        risk = self.risks[risk_id]
        
        # Find the control
        control_index = None
        for i, c in enumerate(risk['controls']):
            if c['id'] == control_id:
                control_index = i
                break
        
        if control_index is None:
            raise ValueError(f"Control not found: {control_id}")
        
        # Remove the control
        risk['controls'].pop(control_index)
        
        # Update the updated_at timestamp
        risk['updated_at'] = self._get_current_timestamp()
        
        # Store the updated risk on disk
        self._save_risk_to_disk(risk)
        
        logger.info(f"Control removed from risk: {risk_id}")
        
        return risk
    
    def _calculate_risk_level(self, impact: str, likelihood: str) -> str:
        """
        Calculate the risk level based on impact and likelihood.
        
        Args:
            impact: The impact level
            likelihood: The likelihood level
            
        Returns:
            The risk level
        """
        # Risk level matrix:
        # +------------+--------+--------+--------+
        # |            | Low    | Medium | High   |
        # +------------+--------+--------+--------+
        # | Low        | Low    | Low    | Medium |
        # | Medium     | Low    | Medium | High   |
        # | High       | Medium | High   | High   |
        # +------------+--------+--------+--------+
        
        if impact == 'low' and likelihood == 'low':
            return 'low'
        elif impact == 'low' and likelihood == 'medium':
            return 'low'
        elif impact == 'low' and likelihood == 'high':
            return 'medium'
        elif impact == 'medium' and likelihood == 'low':
            return 'low'
        elif impact == 'medium' and likelihood == 'medium':
            return 'medium'
        elif impact == 'medium' and likelihood == 'high':
            return 'high'
        elif impact == 'high' and likelihood == 'low':
            return 'medium'
        elif impact == 'high' and likelihood == 'medium':
            return 'high'
        elif impact == 'high' and likelihood == 'high':
            return 'high'
        else:
            return 'medium'  # Default to medium if invalid values
    
    def _validate_risk_data(self, risk_data: Dict[str, Any]) -> None:
        """
        Validate risk data.
        
        Args:
            risk_data: The risk data to validate
            
        Raises:
            ValueError: If the risk data is invalid
        """
        # Check required fields
        if 'vendor_id' not in risk_data:
            raise ValueError("Vendor ID is required")
        
        if 'category_id' not in risk_data:
            raise ValueError("Category ID is required")
        
        if 'name' not in risk_data:
            raise ValueError("Risk name is required")
        
        if 'impact' not in risk_data:
            raise ValueError("Risk impact is required")
        
        if 'likelihood' not in risk_data:
            raise ValueError("Risk likelihood is required")
        
        # Validate category ID
        if risk_data['category_id'] not in self.risk_categories:
            raise ValueError(f"Invalid category ID: {risk_data['category_id']}")
        
        # Validate impact
        category = self.risk_categories[risk_data['category_id']]
        if risk_data['impact'] not in category['impact_levels']:
            raise ValueError(f"Invalid impact level: {risk_data['impact']}")
        
        # Validate likelihood
        if risk_data['likelihood'] not in category['likelihood_levels']:
            raise ValueError(f"Invalid likelihood level: {risk_data['likelihood']}")
        
        # Validate status if provided
        if 'status' in risk_data:
            valid_statuses = ['identified', 'assessed', 'mitigated', 'accepted', 'transferred', 'avoided']
            if risk_data['status'] not in valid_statuses:
                raise ValueError(f"Invalid risk status: {risk_data['status']}")
    
    def _load_risks_from_disk(self) -> None:
        """Load risks from disk."""
        try:
            # Get all JSON files in the risks directory
            risk_files = [f for f in os.listdir(self.risks_dir) if f.endswith('.json')]
            
            for risk_file in risk_files:
                try:
                    # Load the risk from disk
                    file_path = os.path.join(self.risks_dir, risk_file)
                    
                    with open(file_path, 'r', encoding='utf-8') as f:
                        risk = json.load(f)
                    
                    # Store the risk in memory
                    risk_id = risk.get('id')
                    
                    if risk_id:
                        self.risks[risk_id] = risk
                        logger.info(f"Loaded risk from disk: {risk_id}")
                
                except Exception as e:
                    logger.error(f"Failed to load risk from {risk_file}: {e}")
            
            logger.info(f"Loaded {len(self.risks)} risks from disk")
        
        except Exception as e:
            logger.error(f"Failed to load risks from disk: {e}")
    
    def _save_risk_to_disk(self, risk: Dict[str, Any]) -> None:
        """
        Save a risk to disk.
        
        Args:
            risk: The risk to save
        """
        try:
            # Get the risk ID
            risk_id = risk.get('id')
            
            if not risk_id:
                raise ValueError("Risk ID is missing")
            
            # Create the file path
            file_path = os.path.join(self.risks_dir, f"{risk_id}.json")
            
            # Save the risk to a JSON file
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(risk, f, indent=2)
            
            logger.info(f"Saved risk to disk: {risk_id}")
        
        except Exception as e:
            logger.error(f"Failed to save risk to disk: {e}")
    
    def _delete_risk_from_disk(self, risk_id: str) -> None:
        """
        Delete a risk from disk.
        
        Args:
            risk_id: The ID of the risk
        """
        try:
            # Create the file path
            file_path = os.path.join(self.risks_dir, f"{risk_id}.json")
            
            # Check if the file exists
            if os.path.exists(file_path):
                # Delete the file
                os.remove(file_path)
                logger.info(f"Deleted risk from disk: {risk_id}")
            else:
                logger.warning(f"Risk file not found on disk: {risk_id}")
        
        except Exception as e:
            logger.error(f"Failed to delete risk from disk: {e}")
    
    def _get_current_timestamp(self) -> str:
        """
        Get the current timestamp.
        
        Returns:
            The current timestamp as a string
        """
        import datetime
        return datetime.datetime.now().isoformat()
