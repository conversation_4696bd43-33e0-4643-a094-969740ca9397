/**
 * Blockchain Evidence Service Unit Tests
 */

const { expect } = require('chai');
const sinon = require('sinon');
const blockchainEvidence = require('../../../api/services/blockchainEvidence');

describe('Blockchain Evidence Service', () => {
  describe('hashEvidence', () => {
    it('should hash evidence correctly', () => {
      const evidence = 'test evidence';
      const hash = blockchainEvidence.hashEvidence(evidence);
      
      // Hash should be a string
      expect(hash).to.be.a('string');
      
      // Hash should be 64 characters long (SHA-256 produces 32 bytes, which is 64 hex characters)
      expect(hash).to.have.lengthOf(64);
      
      // Hash should be deterministic
      const hash2 = blockchainEvidence.hashEvidence(evidence);
      expect(hash).to.equal(hash2);
      
      // Different evidence should produce different hashes
      const differentEvidence = 'different evidence';
      const differentHash = blockchainEvidence.hashEvidence(differentEvidence);
      expect(hash).to.not.equal(differentHash);
    });
    
    it('should handle Buffer input', () => {
      const evidence = Buffer.from('test evidence');
      const hash = blockchainEvidence.hashEvidence(evidence);
      
      expect(hash).to.be.a('string');
      expect(hash).to.have.lengthOf(64);
      
      // Should produce the same hash as string input
      const stringHash = blockchainEvidence.hashEvidence('test evidence');
      expect(hash).to.equal(stringHash);
    });
  });
  
  describe('constructMerkleTree', () => {
    it('should construct a Merkle tree from a single hash', () => {
      const hash = 'a'.repeat(64);
      const tree = blockchainEvidence.constructMerkleTree([hash]);
      
      expect(tree).to.have.property('root');
      expect(tree).to.have.property('proofs');
      
      // With a single hash, the root should be the hash itself
      expect(tree.root).to.equal(hash);
      
      // Proofs should contain the hash as a key
      expect(tree.proofs).to.have.property(hash);
      
      // Proof for a single hash should be an empty array
      expect(tree.proofs[hash]).to.be.an('array').that.is.empty;
    });
    
    it('should construct a Merkle tree from multiple hashes', () => {
      const hashes = [
        'a'.repeat(64),
        'b'.repeat(64),
        'c'.repeat(64),
        'd'.repeat(64)
      ];
      
      const tree = blockchainEvidence.constructMerkleTree(hashes);
      
      expect(tree).to.have.property('root');
      expect(tree).to.have.property('proofs');
      
      // Root should be a string of length 64
      expect(tree.root).to.be.a('string');
      expect(tree.root).to.have.lengthOf(64);
      
      // Proofs should contain all hashes as keys
      for (const hash of hashes) {
        expect(tree.proofs).to.have.property(hash);
        expect(tree.proofs[hash]).to.be.an('array');
        expect(tree.proofs[hash].length).to.be.greaterThan(0);
      }
    });
    
    it('should handle an odd number of hashes', () => {
      const hashes = [
        'a'.repeat(64),
        'b'.repeat(64),
        'c'.repeat(64)
      ];
      
      const tree = blockchainEvidence.constructMerkleTree(hashes);
      
      expect(tree).to.have.property('root');
      expect(tree).to.have.property('proofs');
      
      // Root should be a string of length 64
      expect(tree.root).to.be.a('string');
      expect(tree.root).to.have.lengthOf(64);
      
      // Proofs should contain all hashes as keys
      for (const hash of hashes) {
        expect(tree.proofs).to.have.property(hash);
        expect(tree.proofs[hash]).to.be.an('array');
      }
    });
    
    it('should throw an error for empty hashes', () => {
      expect(() => blockchainEvidence.constructMerkleTree([])).to.throw('No hashes provided');
    });
  });
  
  describe('verifyMerkleProof', () => {
    it('should verify a valid Merkle proof', () => {
      const hashes = [
        'a'.repeat(64),
        'b'.repeat(64),
        'c'.repeat(64),
        'd'.repeat(64)
      ];
      
      const tree = blockchainEvidence.constructMerkleTree(hashes);
      
      // Verify each hash
      for (const hash of hashes) {
        const proof = tree.proofs[hash];
        const isValid = blockchainEvidence.verifyMerkleProof(hash, proof, tree.root);
        expect(isValid).to.be.true;
      }
    });
    
    it('should reject an invalid Merkle proof', () => {
      const hashes = [
        'a'.repeat(64),
        'b'.repeat(64),
        'c'.repeat(64),
        'd'.repeat(64)
      ];
      
      const tree = blockchainEvidence.constructMerkleTree(hashes);
      
      // Use a different hash
      const differentHash = 'e'.repeat(64);
      const proof = tree.proofs[hashes[0]];
      const isValid = blockchainEvidence.verifyMerkleProof(differentHash, proof, tree.root);
      expect(isValid).to.be.false;
    });
    
    it('should reject a tampered Merkle proof', () => {
      const hashes = [
        'a'.repeat(64),
        'b'.repeat(64),
        'c'.repeat(64),
        'd'.repeat(64)
      ];
      
      const tree = blockchainEvidence.constructMerkleTree(hashes);
      
      // Tamper with the proof
      const hash = hashes[0];
      const proof = [...tree.proofs[hash]];
      proof[0] = 'e'.repeat(64);
      
      const isValid = blockchainEvidence.verifyMerkleProof(hash, proof, tree.root);
      expect(isValid).to.be.false;
    });
  });
  
  describe('createEvidenceRecord', () => {
    let submitEvidenceStub;
    
    beforeEach(() => {
      // Stub the submitEvidence method
      submitEvidenceStub = sinon.stub(blockchainEvidence, 'submitEvidence').resolves({
        merkleRoot: 'root'.repeat(16),
        proofs: {
          'hash'.repeat(16): ['proof'.repeat(16)]
        },
        submissionId: '12345',
        blockNumber: 12345678,
        blockHash: '0x' + 'block'.repeat(16),
        timestamp: Math.floor(Date.now() / 1000),
        transactionHash: '0x' + 'tx'.repeat(16)
      });
    });
    
    afterEach(() => {
      // Restore the stub
      submitEvidenceStub.restore();
    });
    
    it('should create an evidence record', async () => {
      const evidence = {
        type: 'test',
        content: 'Test evidence',
        metadata: {
          key: 'value'
        }
      };
      
      const record = await blockchainEvidence.createEvidenceRecord(evidence);
      
      expect(record).to.have.property('id');
      expect(record).to.have.property('type', 'test');
      expect(record).to.have.property('content', 'Test evidence');
      expect(record).to.have.property('metadata');
      expect(record.metadata).to.have.property('key', 'value');
      expect(record).to.have.property('createdAt');
      expect(record).to.have.property('blockchain');
      expect(record.blockchain).to.have.property('merkleRoot');
      expect(record.blockchain).to.have.property('proof');
      expect(record.blockchain).to.have.property('submissionId');
      expect(record.blockchain).to.have.property('blockNumber');
      expect(record.blockchain).to.have.property('blockHash');
      expect(record.blockchain).to.have.property('timestamp');
      expect(record.blockchain).to.have.property('transactionHash');
      
      // Verify that submitEvidence was called
      expect(submitEvidenceStub.calledOnce).to.be.true;
    });
    
    it('should throw an error for invalid evidence', async () => {
      try {
        await blockchainEvidence.createEvidenceRecord(null);
        // Should not reach here
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error.message).to.equal('Invalid evidence');
      }
      
      try {
        await blockchainEvidence.createEvidenceRecord({});
        // Should not reach here
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error.message).to.equal('Invalid evidence');
      }
      
      try {
        await blockchainEvidence.createEvidenceRecord({ type: 'test' });
        // Should not reach here
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error.message).to.equal('Invalid evidence');
      }
      
      try {
        await blockchainEvidence.createEvidenceRecord({ content: 'Test evidence' });
        // Should not reach here
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error.message).to.equal('Invalid evidence');
      }
    });
  });
  
  describe('verifyEvidenceRecord', () => {
    let verifyEvidenceStub;
    
    beforeEach(() => {
      // Stub the verifyEvidence method
      verifyEvidenceStub = sinon.stub(blockchainEvidence, 'verifyEvidence').resolves({
        verified: true,
        evidenceHash: 'hash'.repeat(16),
        merkleRoot: 'root'.repeat(16),
        submissionId: '12345',
        timestamp: Math.floor(Date.now() / 1000)
      });
    });
    
    afterEach(() => {
      // Restore the stub
      verifyEvidenceStub.restore();
    });
    
    it('should verify a valid evidence record', async () => {
      const evidenceRecord = {
        id: '12345',
        type: 'test',
        content: 'Test evidence',
        metadata: {
          key: 'value'
        },
        createdAt: new Date().toISOString(),
        blockchain: {
          merkleRoot: 'root'.repeat(16),
          proof: ['proof'.repeat(16)],
          submissionId: '12345',
          blockNumber: 12345678,
          blockHash: '0x' + 'block'.repeat(16),
          timestamp: Math.floor(Date.now() / 1000),
          transactionHash: '0x' + 'tx'.repeat(16)
        }
      };
      
      const verification = await blockchainEvidence.verifyEvidenceRecord(evidenceRecord);
      
      expect(verification).to.have.property('verified', true);
      expect(verification).to.have.property('evidenceHash');
      expect(verification).to.have.property('merkleRoot');
      expect(verification).to.have.property('submissionId');
      expect(verification).to.have.property('timestamp');
      
      // Verify that verifyEvidence was called
      expect(verifyEvidenceStub.calledOnce).to.be.true;
    });
    
    it('should throw an error for invalid evidence record', async () => {
      try {
        await blockchainEvidence.verifyEvidenceRecord(null);
        // Should not reach here
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error.message).to.equal('Invalid evidence record');
      }
      
      try {
        await blockchainEvidence.verifyEvidenceRecord({});
        // Should not reach here
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error.message).to.equal('Invalid evidence record');
      }
      
      try {
        await blockchainEvidence.verifyEvidenceRecord({ id: '12345' });
        // Should not reach here
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error.message).to.equal('Invalid evidence record');
      }
    });
  });
});
