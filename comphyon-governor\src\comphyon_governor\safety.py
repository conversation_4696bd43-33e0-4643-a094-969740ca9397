"""
ComphyonΨᶜ Safety Protocols - Safety mechanisms for the ComphyonΨᶜ Governor.
"""

import time
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('comphyon_governor.safety')

class SafetyProtocols:
    """
    Safety protocols for the ComphyonΨᶜ Governor.
    
    Implements circuit-breaker mechanisms and other safety features.
    """
    
    def __init__(self):
        """Initialize the SafetyProtocols."""
        self.circuit_breakers = {
            'acceleration': {
                'threshold': 5.0,
                'tripped': False,
                'last_trip_time': None,
                'reset_delay': 300  # 5 minutes
            },
            'velocity': {
                'threshold': 200.0,
                'tripped': False,
                'last_trip_time': None,
                'reset_delay': 300  # 5 minutes
            }
        }
        
        self.safety_overrides = []
    
    def apply_safety_protocols(self, control_actions, metrics):
        """
        Apply safety protocols to control actions.
        
        Args:
            control_actions: Control actions to be applied
            metrics: Metrics that triggered the control actions
            
        Returns:
            dict: Modified control actions with safety protocols applied
        """
        # Check if any circuit breakers should be tripped
        self._check_circuit_breakers(metrics)
        
        # Apply circuit breaker actions if tripped
        if any(cb['tripped'] for cb in self.circuit_breakers.values()):
            self._apply_circuit_breaker_actions(control_actions)
        
        # Apply ethical guardrails
        self._apply_ethical_guardrails(control_actions)
        
        # Apply forced diversity if needed
        if metrics.get('acceleration', 0) > 2.0:
            self._apply_forced_diversity(control_actions)
        
        return control_actions
    
    def _check_circuit_breakers(self, metrics):
        """
        Check if any circuit breakers should be tripped.
        
        Args:
            metrics: Metrics dictionary from ComphyonMeter
        """
        current_time = time.time()
        
        # Check acceleration circuit breaker
        if 'acceleration' in metrics:
            cb = self.circuit_breakers['acceleration']
            if metrics['acceleration'] > cb['threshold'] and not cb['tripped']:
                cb['tripped'] = True
                cb['last_trip_time'] = current_time
                logger.warning(f"Acceleration circuit breaker tripped: {metrics['acceleration']} > {cb['threshold']}")
            elif cb['tripped'] and cb['last_trip_time'] is not None:
                # Check if reset delay has passed
                if current_time - cb['last_trip_time'] > cb['reset_delay']:
                    cb['tripped'] = False
                    logger.info("Acceleration circuit breaker reset")
        
        # Check velocity circuit breaker
        if 'velocity' in metrics:
            cb = self.circuit_breakers['velocity']
            if metrics['velocity'] > cb['threshold'] and not cb['tripped']:
                cb['tripped'] = True
                cb['last_trip_time'] = current_time
                logger.warning(f"Velocity circuit breaker tripped: {metrics['velocity']} > {cb['threshold']}")
            elif cb['tripped'] and cb['last_trip_time'] is not None:
                # Check if reset delay has passed
                if current_time - cb['last_trip_time'] > cb['reset_delay']:
                    cb['tripped'] = False
                    logger.info("Velocity circuit breaker reset")
    
    def _apply_circuit_breaker_actions(self, control_actions):
        """
        Apply circuit breaker actions to control actions.
        
        Args:
            control_actions: Control actions to be applied
        """
        # Add circuit breaker actions
        circuit_breaker_actions = []
        
        if self.circuit_breakers['acceleration']['tripped']:
            circuit_breaker_actions.append({
                'level': 'macro',
                'type': 'circuit_breaker',
                'target': 'acceleration',
                'value': 'emergency_shutdown'
            })
        
        if self.circuit_breakers['velocity']['tripped']:
            circuit_breaker_actions.append({
                'level': 'macro',
                'type': 'circuit_breaker',
                'target': 'velocity',
                'value': 'emergency_throttle'
            })
        
        # Add circuit breaker actions to control actions
        control_actions['actions'].extend(circuit_breaker_actions)
        
        # Update control action type and reason
        control_actions['type'] = 'circuit_breaker'
        control_actions['reason'] = 'Circuit breaker tripped'
    
    def _apply_ethical_guardrails(self, control_actions):
        """
        Apply ethical guardrails to control actions.
        
        Args:
            control_actions: Control actions to be applied
        """
        # Add ethical guardrail actions
        ethical_actions = [{
            'level': 'macro',
            'type': 'ethical_guardrail',
            'target': 'decision_making',
            'value': 'enforce_constraints'
        }]
        
        # Add ethical guardrail actions to control actions
        control_actions['actions'].extend(ethical_actions)
    
    def _apply_forced_diversity(self, control_actions):
        """
        Apply forced diversity to control actions.
        
        Args:
            control_actions: Control actions to be applied
        """
        # Add forced diversity actions
        diversity_actions = [{
            'level': 'meso',
            'type': 'forced_diversity',
            'target': 'decision_space',
            'value': 'introduce_randomness'
        }]
        
        # Add forced diversity actions to control actions
        control_actions['actions'].extend(diversity_actions)
    
    def set_circuit_breaker_threshold(self, metric, threshold):
        """
        Set the threshold for a circuit breaker.
        
        Args:
            metric: Metric name (e.g., 'velocity', 'acceleration')
            threshold: Threshold value
        """
        if metric in self.circuit_breakers:
            self.circuit_breakers[metric]['threshold'] = threshold
    
    def reset_circuit_breakers(self):
        """Reset all circuit breakers."""
        for cb in self.circuit_breakers.values():
            cb['tripped'] = False
            cb['last_trip_time'] = None
        
        logger.info("All circuit breakers reset")
