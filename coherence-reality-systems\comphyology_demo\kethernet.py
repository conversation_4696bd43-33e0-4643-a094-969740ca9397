"""KetherNet simulation for Stage 4"""
import hashlib
import json
from datetime import datetime

class KetherBlock:
    def __init__(self, index, timestamp, data, previous_hash, psi_signature):
        self.index = index
        self.timestamp = timestamp
        self.data = data
        self.previous_hash = previous_hash
        self.psi_signature = psi_signature
        self.hash = self.calculate_hash()
    
    def calculate_hash(self):
        block_string = json.dumps({
            "index": self.index,
            "timestamp": str(self.timestamp),
            "data": self.data,
            "previous_hash": self.previous_hash,
            "psi_signature": self.psi_signature
        }, sort_keys=True).encode()
        return hashlib.sha256(block_string).hexdigest()

class KetherNet:
    def __init__(self):
        self.chain = [self.create_genesis_block()]
    
    def create_genesis_block(self):
        return KetherBlock(0, datetime.now(), "Genesis Block", "0", "0" * 64)
    
    def add_block(self, data, psi_signature):
        previous_block = self.chain[-1]
        new_block = KetherBlock(
            index=len(self.chain),
            timestamp=datetime.now(),
            data=data,
            previous_hash=previous_block.hash,
            psi_signature=psi_signature
        )
        self.chain.append(new_block)
        return new_block
    
    def validate_block(self, block):
        # Validate Ψ signature (simplified)
        if block.psi_signature == "INVALID_PSI":
            return False
        
        # Validate hash chain
        if block.previous_hash != self.chain[-1].hash:
            return False
        
        return True

# Helper function to simulate Ψᶜʰ signature
def generate_psi_signature(data, valid=True):
    """Generate mock Ψ signature"""
    if valid:
        return hashlib.sha256(data.encode()).hexdigest()
    return "INVALID_PSI"
