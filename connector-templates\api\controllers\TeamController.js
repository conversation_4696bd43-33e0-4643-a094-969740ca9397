/**
 * Team Controller
 * 
 * This controller handles API requests related to team management.
 */

const TeamService = require('../services/TeamService');
const AuditService = require('../services/AuditService');
const { ValidationError } = require('../utils/errors');

class TeamController {
  constructor() {
    this.teamService = new TeamService();
    this.auditService = new AuditService();
  }

  /**
   * Get all teams
   */
  async getAllTeams(req, res, next) {
    try {
      const teams = await this.teamService.getAllTeams();
      res.json(teams);
      
      // Log audit event
      this.auditService.logEvent({
        userId: req.user.id,
        action: 'GET',
        resourceType: 'team',
        resourceId: 'all',
        details: { count: teams.length },
        ip: req.ip,
        userAgent: req.headers['user-agent']
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get teams for current user
   */
  async getMyTeams(req, res, next) {
    try {
      const teams = await this.teamService.getTeamsForUser(req.user.id);
      res.json(teams);
      
      // Log audit event
      this.auditService.logEvent({
        userId: req.user.id,
        action: 'GET',
        resourceType: 'team',
        resourceId: 'my',
        details: { count: teams.length },
        ip: req.ip,
        userAgent: req.headers['user-agent']
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get team by ID
   */
  async getTeamById(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Team ID is required');
      }
      
      const team = await this.teamService.getTeamById(id);
      
      // Check if user is a member of the team
      const isMember = await this.teamService.isUserTeamMember(id, req.user.id);
      
      if (!isMember) {
        throw new ValidationError('You are not a member of this team');
      }
      
      res.json(team);
      
      // Log audit event
      this.auditService.logEvent({
        userId: req.user.id,
        action: 'GET',
        resourceType: 'team',
        resourceId: id,
        ip: req.ip,
        userAgent: req.headers['user-agent'],
        teamId: id
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Create a new team
   */
  async createTeam(req, res, next) {
    try {
      const teamData = req.body;
      
      if (!teamData) {
        throw new ValidationError('Team data is required');
      }
      
      const team = await this.teamService.createTeam(teamData, req.user.id);
      res.status(201).json(team);
      
      // Log audit event
      this.auditService.logEvent({
        userId: req.user.id,
        action: 'CREATE',
        resourceType: 'team',
        resourceId: team.id,
        details: { name: team.name },
        ip: req.ip,
        userAgent: req.headers['user-agent'],
        teamId: team.id
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update a team
   */
  async updateTeam(req, res, next) {
    try {
      const { id } = req.params;
      const teamData = req.body;
      
      if (!id) {
        throw new ValidationError('Team ID is required');
      }
      
      if (!teamData) {
        throw new ValidationError('Team data is required');
      }
      
      const team = await this.teamService.updateTeam(id, teamData, req.user.id);
      res.json(team);
      
      // Log audit event
      this.auditService.logEvent({
        userId: req.user.id,
        action: 'UPDATE',
        resourceType: 'team',
        resourceId: id,
        details: { name: team.name },
        ip: req.ip,
        userAgent: req.headers['user-agent'],
        teamId: id
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Delete a team
   */
  async deleteTeam(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Team ID is required');
      }
      
      // Get team before deletion for audit log
      const team = await this.teamService.getTeamById(id);
      
      const result = await this.teamService.deleteTeam(id, req.user.id);
      res.json(result);
      
      // Log audit event
      this.auditService.logEvent({
        userId: req.user.id,
        action: 'DELETE',
        resourceType: 'team',
        resourceId: id,
        details: { name: team.name },
        ip: req.ip,
        userAgent: req.headers['user-agent']
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get team members
   */
  async getTeamMembers(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Team ID is required');
      }
      
      const members = await this.teamService.getTeamMembers(id, req.user.id);
      res.json(members);
      
      // Log audit event
      this.auditService.logEvent({
        userId: req.user.id,
        action: 'GET',
        resourceType: 'team_member',
        resourceId: 'all',
        details: { teamId: id, count: members.length },
        ip: req.ip,
        userAgent: req.headers['user-agent'],
        teamId: id
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Add team member
   */
  async addTeamMember(req, res, next) {
    try {
      const { id } = req.params;
      const { userId, role } = req.body;
      
      if (!id) {
        throw new ValidationError('Team ID is required');
      }
      
      if (!userId) {
        throw new ValidationError('User ID is required');
      }
      
      // Check if user has permission to add team members
      await this.teamService.checkTeamPermission(id, req.user.id, ['owner', 'admin']);
      
      const member = await this.teamService.addTeamMember(id, userId, role);
      res.status(201).json(member);
      
      // Log audit event
      this.auditService.logEvent({
        userId: req.user.id,
        action: 'CREATE',
        resourceType: 'team_member',
        resourceId: member.id,
        details: { teamId: id, userId, role },
        ip: req.ip,
        userAgent: req.headers['user-agent'],
        teamId: id
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update team member
   */
  async updateTeamMember(req, res, next) {
    try {
      const { id, memberId } = req.params;
      const memberData = req.body;
      
      if (!id) {
        throw new ValidationError('Team ID is required');
      }
      
      if (!memberId) {
        throw new ValidationError('Member ID is required');
      }
      
      if (!memberData) {
        throw new ValidationError('Member data is required');
      }
      
      const member = await this.teamService.updateTeamMember(id, memberId, memberData, req.user.id);
      res.json(member);
      
      // Log audit event
      this.auditService.logEvent({
        userId: req.user.id,
        action: 'UPDATE',
        resourceType: 'team_member',
        resourceId: member.id,
        details: { teamId: id, userId: memberId, role: member.role },
        ip: req.ip,
        userAgent: req.headers['user-agent'],
        teamId: id
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Remove team member
   */
  async removeTeamMember(req, res, next) {
    try {
      const { id, memberId } = req.params;
      
      if (!id) {
        throw new ValidationError('Team ID is required');
      }
      
      if (!memberId) {
        throw new ValidationError('Member ID is required');
      }
      
      const result = await this.teamService.removeTeamMember(id, memberId, req.user.id);
      res.json(result);
      
      // Log audit event
      this.auditService.logEvent({
        userId: req.user.id,
        action: 'DELETE',
        resourceType: 'team_member',
        resourceId: memberId,
        details: { teamId: id, userId: memberId },
        ip: req.ip,
        userAgent: req.headers['user-agent'],
        teamId: id
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Create team invitation
   */
  async createTeamInvitation(req, res, next) {
    try {
      const { id } = req.params;
      const { email, role } = req.body;
      
      if (!id) {
        throw new ValidationError('Team ID is required');
      }
      
      if (!email) {
        throw new ValidationError('Email is required');
      }
      
      const invitation = await this.teamService.createTeamInvitation(id, email, role || 'member', req.user.id);
      res.status(201).json(invitation);
      
      // Log audit event
      this.auditService.logEvent({
        userId: req.user.id,
        action: 'CREATE',
        resourceType: 'team_invitation',
        resourceId: invitation.id,
        details: { teamId: id, email, role: invitation.role },
        ip: req.ip,
        userAgent: req.headers['user-agent'],
        teamId: id
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get team invitations
   */
  async getTeamInvitations(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Team ID is required');
      }
      
      const invitations = await this.teamService.getTeamInvitations(id, req.user.id);
      res.json(invitations);
      
      // Log audit event
      this.auditService.logEvent({
        userId: req.user.id,
        action: 'GET',
        resourceType: 'team_invitation',
        resourceId: 'all',
        details: { teamId: id, count: invitations.length },
        ip: req.ip,
        userAgent: req.headers['user-agent'],
        teamId: id
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get my invitations
   */
  async getMyInvitations(req, res, next) {
    try {
      // Get user email from user object
      const email = req.user.email;
      
      if (!email) {
        throw new ValidationError('User email is required');
      }
      
      const invitations = await this.teamService.getInvitationsForEmail(email);
      res.json(invitations);
      
      // Log audit event
      this.auditService.logEvent({
        userId: req.user.id,
        action: 'GET',
        resourceType: 'team_invitation',
        resourceId: 'my',
        details: { email, count: invitations.length },
        ip: req.ip,
        userAgent: req.headers['user-agent']
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Accept team invitation
   */
  async acceptTeamInvitation(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Invitation ID is required');
      }
      
      const invitation = await this.teamService.acceptTeamInvitation(id, req.user.id);
      res.json(invitation);
      
      // Log audit event
      this.auditService.logEvent({
        userId: req.user.id,
        action: 'UPDATE',
        resourceType: 'team_invitation',
        resourceId: id,
        details: { status: 'accepted', teamId: invitation.teamId },
        ip: req.ip,
        userAgent: req.headers['user-agent'],
        teamId: invitation.teamId
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Decline team invitation
   */
  async declineTeamInvitation(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Invitation ID is required');
      }
      
      const invitation = await this.teamService.declineTeamInvitation(id, req.user.id);
      res.json(invitation);
      
      // Log audit event
      this.auditService.logEvent({
        userId: req.user.id,
        action: 'UPDATE',
        resourceType: 'team_invitation',
        resourceId: id,
        details: { status: 'declined', teamId: invitation.teamId },
        ip: req.ip,
        userAgent: req.headers['user-agent'],
        teamId: invitation.teamId
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Cancel team invitation
   */
  async cancelTeamInvitation(req, res, next) {
    try {
      const { id, invitationId } = req.params;
      
      if (!id) {
        throw new ValidationError('Team ID is required');
      }
      
      if (!invitationId) {
        throw new ValidationError('Invitation ID is required');
      }
      
      const invitation = await this.teamService.cancelTeamInvitation(invitationId, req.user.id);
      res.json(invitation);
      
      // Log audit event
      this.auditService.logEvent({
        userId: req.user.id,
        action: 'UPDATE',
        resourceType: 'team_invitation',
        resourceId: invitationId,
        details: { status: 'cancelled', teamId: id },
        ip: req.ip,
        userAgent: req.headers['user-agent'],
        teamId: id
      });
    } catch (error) {
      next(error);
    }
  }
}

module.exports = new TeamController();
