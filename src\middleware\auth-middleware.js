/**
 * NovaFuse Universal API Connector - Authentication Middleware
 * 
 * This module provides middleware for authentication and authorization.
 */

const jwt = require('jsonwebtoken');
const { createLogger } = require('../utils/logger');
const { UnauthorizedError, ForbiddenError } = require('../errors');

const logger = createLogger('auth-middleware');

// JWT secret key (should be in environment variables in production)
const JWT_SECRET = process.env.JWT_SECRET || 'novafuse-uac-secret-key';

// Token expiration (1 day)
const TOKEN_EXPIRATION = '1d';

/**
 * Generate a JWT token
 * 
 * @param {Object} user - The user object
 * @returns {string} - The JWT token
 */
function generateToken(user) {
  return jwt.sign(
    {
      id: user.id,
      username: user.username,
      role: user.role
    },
    JWT_SECRET,
    { expiresIn: TOKEN_EXPIRATION }
  );
}

/**
 * Verify a JWT token
 * 
 * @param {string} token - The JWT token
 * @returns {Object} - The decoded token
 */
function verifyToken(token) {
  try {
    return jwt.verify(token, JWT_SECRET);
  } catch (error) {
    throw new UnauthorizedError('Invalid token');
  }
}

/**
 * Authentication middleware
 * 
 * @param {Object} req - The request object
 * @param {Object} res - The response object
 * @param {Function} next - The next middleware function
 */
function authenticate(req, res, next) {
  try {
    // Get token from Authorization header
    const authHeader = req.headers.authorization;
    
    if (!authHeader) {
      throw new UnauthorizedError('No authorization header provided');
    }
    
    // Check if token is Bearer token
    const parts = authHeader.split(' ');
    
    if (parts.length !== 2 || parts[0] !== 'Bearer') {
      throw new UnauthorizedError('Invalid authorization format');
    }
    
    const token = parts[1];
    
    // Verify token
    const decoded = verifyToken(token);
    
    // Add user to request
    req.user = decoded;
    
    next();
  } catch (error) {
    next(error);
  }
}

/**
 * Authorization middleware factory
 * 
 * @param {string[]} roles - The roles allowed to access the resource
 * @returns {Function} - The authorization middleware
 */
function authorize(roles = []) {
  return (req, res, next) => {
    try {
      // Check if user exists
      if (!req.user) {
        throw new UnauthorizedError('User not authenticated');
      }
      
      // Check if user has required role
      if (roles.length > 0 && !roles.includes(req.user.role)) {
        throw new ForbiddenError(`User role ${req.user.role} not authorized`);
      }
      
      next();
    } catch (error) {
      next(error);
    }
  };
}

/**
 * Mock authentication for development
 * 
 * @param {Object} req - The request object
 * @param {Object} res - The response object
 * @param {Function} next - The next middleware function
 */
function mockAuthenticate(req, res, next) {
  // Add mock user to request
  req.user = {
    id: 'mock-user-id',
    username: 'mock-user',
    role: 'admin'
  };
  
  next();
}

module.exports = {
  generateToken,
  verifyToken,
  authenticate,
  authorize,
  mockAuthenticate
};
