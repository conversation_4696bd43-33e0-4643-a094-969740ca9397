# Quantum Protein Folding Dashboard

## Overview
The Quantum Protein Folding Dashboard is a real-time monitoring and management interface for quantum protein folding experiments. It provides visualization of protein structures, system metrics, and experiment status through an intuitive web-based interface.

## Features

### 1. Real-time System Monitoring
- CPU, Memory, GPU, and Storage usage metrics
- Network statistics and system information
- Visual indicators for system health

### 2. Protein Structure Visualization
- Interactive 3D visualization using 3DMol.js
- Multiple representation styles (Cartoon, Stick, Sphere, Line)
- Real-time rendering and manipulation
- Export functionality for images

### 3. Experiment Management
- Create and monitor quantum protein folding experiments
- Real-time status updates via WebSocket
- Filtering and pagination of experiment history
- Detailed experiment views

### 4. Quantum Backend Integration
- Support for multiple quantum backends (Qiskit, PennyLane, Amazon Braket)
- Backend status and capabilities display
- Resource estimation

## Architecture

### Frontend
- **Framework**: Bootstrap 5 with jQuery
- **Visualization**: 3DMol.js for protein structure rendering
- **Real-time Updates**: Socket.IO for WebSocket communication
- **Build System**: Static assets served via Flask

### Backend
- **Web Framework**: Flask
- **API**: RESTful endpoints for data retrieval
- **WebSockets**: Real-time event broadcasting
- **Task Queue**: Asynchronous job processing

## Getting Started

### Prerequisites
- Python 3.8+
- Node.js 14+ (for development)
- Modern web browser (Chrome, Firefox, Edge, or Safari)

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/consciousness-chemistry-engine.git
   cd consciousness-chemistry-engine
   ```

2. Create and activate a virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

4. Install development dependencies:
   ```bash
   pip install -r requirements-dev.txt
   ```

### Running the Dashboard

1. Start the Flask development server:
   ```bash
   python -m quantum.dashboard.app
   ```

2. Open your browser and navigate to:
   ```
   http://localhost:5000
   ```

## User Guide

### Dashboard Layout

1. **Navigation Bar**
   - Quick access to main sections
   - System status indicators
   - User profile and settings

2. **System Status Cards**
   - Real-time resource usage metrics
   - Visual indicators for system health
   - Quick actions for system management

3. **Protein Visualization**
   - 3D viewport for protein structures
   - Style selector (Cartoon, Stick, Sphere, Line)
   - View controls and export options

4. **Experiment Management**
   - List of recent experiments with status indicators
   - Filtering and search functionality
   - Detailed experiment views

5. **Quantum Backends**
   - Available quantum computing backends
   - Status and capabilities
   - Resource utilization

### Using the Protein Viewer

1. **Loading a Structure**
   - Click "Load Example" to load a sample protein
   - Or enter a PDB ID in the search box

2. **Controlling the View**
   - **Rotate**: Left-click and drag
   - **Zoom**: Scroll up/down
   - **Pan**: Right-click and drag
   - **Reset View**: Click the reset button

3. **Changing Visualization Style**
   - Use the style buttons to switch between representations
   - Toggle between different color schemes

## API Reference

### REST API Endpoints

#### GET /api/experiments
List all experiments with optional filtering.

**Query Parameters:**
- `status`: Filter by status (pending, running, completed, failed)
- `limit`: Maximum number of results to return (default: 10)
- `offset`: Pagination offset (default: 0)

**Response:**
```json
{
  "experiments": [
    {
      "id": "exp_123",
      "name": "My Experiment",
      "status": "running",
      "created_at": "2025-06-27T12:00:00Z",
      "progress": 45
    }
  ],
  "total": 42,
  "limit": 10,
  "offset": 0
}
```

#### POST /api/experiments
Create a new experiment.

**Request Body:**
```json
{
  "name": "My New Experiment",
  "parameters": {
    "sequence": "ACDEFGHIKLMNPQRSTVWY",
    "method": "qaoa",
    "iterations": 1000
  }
}
```

**Response:**
```json
{
  "id": "exp_124",
  "status": "pending",
  "created_at": "2025-06-27T12:05:00Z"
}
```

#### GET /api/experiments/{experiment_id}
Get details of a specific experiment.

**Response:**
```json
{
  "id": "exp_123",
  "name": "My Experiment",
  "status": "completed",
  "created_at": "2025-06-27T12:00:00Z",
  "completed_at": "2025-06-27T12:30:00Z",
  "parameters": {
    "sequence": "ACDEFGHIKLMNPQRSTVWY",
    "method": "qaoa",
    "iterations": 1000
  },
  "results": {
    "energy": -42.0,
    "structure": "...pdb data...",
    "metrics": {
      "accuracy": 0.95,
      "confidence": 0.92
    }
  }
}
```

### WebSocket Events

#### Server Events
- `system_status`: System metrics update
  ```json
  {
    "cpu_percent": 24.5,
    "memory_percent": 36.2,
    "gpu_percent": 15.8,
    "disk_percent": 42.1,
    "timestamp": "2025-06-27T12:00:01Z"
  }
  ```

- `experiment_update`: Experiment status update
  ```json
  {
    "experiment_id": "exp_123",
    "status": "running",
    "progress": 75,
    "message": "Optimizing quantum circuit..."
  }
  ```

#### Client Events
- `subscribe_system_updates`: Subscribe to system status updates
  ```json
  {
    "interval": 5  // Update interval in seconds
  }
  ```

- `unsubscribe_system_updates`: Unsubscribe from system status updates

## Development

### Project Structure

```
quantum/dashboard/
├── app.py                 # Flask application
├── static/
│   ├── css/
│   │   └── styles.css    # Custom styles
│   └── js/
│       ├── dashboard.js # Main dashboard logic
│       └── main.js       # Shared utilities
└── templates/
    ├── base.html         # Base template
    ├── dashboard.html    # Main dashboard view
    └── experiments/      # Experiment-related templates
```

### Adding a New Visualization

1. Create a new JavaScript file in `static/js/` for your visualization
2. Add the script to the dashboard template
3. Initialize the visualization in `dashboard.js`
4. Add any necessary styles to `static/css/styles.css`

### Running Tests

```bash
pytest tests/test_dashboard.py
```

### Building for Production

1. Minify JavaScript and CSS:
   ```bash
   npm install -g uglify-js clean-css-cli
   uglifyjs static/js/*.js -o static/js/bundle.min.js
   cleancss -o static/css/bundle.min.css static/css/*.css
   ```

2. Configure production settings in `config.py`

3. Deploy using a production WSGI server:
   ```bash
   gunicorn -w 4 -b 0.0.0.0:5000 quantum.dashboard.app:app
   ```

## Troubleshooting

### Common Issues

1. **WebSocket Connection Failed**
   - Ensure the WebSocket server is running
   - Check for any proxy or firewall issues
   - Verify the WebSocket URL in the JavaScript console

2. **3D Rendering Issues**
   - Ensure WebGL is enabled in your browser
   - Try a different visualization style if performance is poor
   - Check the browser console for any WebGL errors

3. **API Authentication Errors**
   - Verify your API key is correctly set
   - Check the authentication headers in the request
   - Ensure your user account has the necessary permissions

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- [3DMol.js](https://3dmol.org/) - For the molecular visualization library
- [Bootstrap](https://getbootstrap.com/) - For the responsive UI components
- [Flask](https://flask.palletsprojects.com/) - For the web framework
- [Socket.IO](https://socket.io/) - For real-time communication
