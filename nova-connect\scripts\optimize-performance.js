/**
 * NovaConnect UAC Performance Optimization Script
 * 
 * This script analyzes the NovaConnect UAC system and provides recommendations
 * for performance optimization.
 */

const os = require('os');
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const mongoose = require('mongoose');
const Redis = require('redis');
const logger = require('../api/utils/logger');

// Configuration
const config = {
  mongodbUri: process.env.MONGODB_URI || 'mongodb://localhost:27017/novafuse',
  redisUri: process.env.REDIS_URI || 'redis://localhost:6379',
  outputFile: process.env.OUTPUT_FILE || 'performance-report.md',
  detailedAnalysis: process.env.DETAILED_ANALYSIS === 'true'
};

/**
 * Main function
 */
async function main() {
  try {
    console.log('Starting NovaConnect UAC Performance Optimization Analysis...');
    
    // Analyze system resources
    const systemInfo = analyzeSystemResources();
    
    // Analyze Node.js configuration
    const nodeInfo = analyzeNodeConfiguration();
    
    // Analyze MongoDB
    const mongoInfo = await analyzeMongoDb();
    
    // Analyze Redis
    const redisInfo = await analyzeRedis();
    
    // Analyze code
    const codeInfo = analyzeCode();
    
    // Generate recommendations
    const recommendations = generateRecommendations(systemInfo, nodeInfo, mongoInfo, redisInfo, codeInfo);
    
    // Generate report
    const report = generateReport(systemInfo, nodeInfo, mongoInfo, redisInfo, codeInfo, recommendations);
    
    // Write report to file
    fs.writeFileSync(config.outputFile, report);
    
    console.log(`Performance optimization report generated: ${config.outputFile}`);
  } catch (error) {
    console.error('Error analyzing performance:', error);
    process.exit(1);
  }
}

/**
 * Analyze system resources
 * @returns {Object} System information
 */
function analyzeSystemResources() {
  console.log('Analyzing system resources...');
  
  const cpus = os.cpus();
  const totalMemory = os.totalmem();
  const freeMemory = os.freemem();
  const usedMemory = totalMemory - freeMemory;
  const memoryUsage = usedMemory / totalMemory;
  
  return {
    cpus: {
      count: cpus.length,
      model: cpus[0].model,
      speed: cpus[0].speed
    },
    memory: {
      total: totalMemory,
      free: freeMemory,
      used: usedMemory,
      usage: memoryUsage
    },
    platform: os.platform(),
    arch: os.arch(),
    uptime: os.uptime()
  };
}

/**
 * Analyze Node.js configuration
 * @returns {Object} Node.js information
 */
function analyzeNodeConfiguration() {
  console.log('Analyzing Node.js configuration...');
  
  const nodeVersion = process.version;
  const v8Version = process.versions.v8;
  const memoryUsage = process.memoryUsage();
  const maxOldSpaceSize = process.env.NODE_OPTIONS?.match(/--max-old-space-size=(\d+)/)?.[1] || '1536';
  
  return {
    nodeVersion,
    v8Version,
    memoryUsage,
    maxOldSpaceSize: parseInt(maxOldSpaceSize, 10),
    environment: process.env.NODE_ENV || 'development',
    clusterMode: process.env.CLUSTER_ENABLED === 'true'
  };
}

/**
 * Analyze MongoDB
 * @returns {Promise<Object>} MongoDB information
 */
async function analyzeMongoDb() {
  console.log('Analyzing MongoDB...');
  
  try {
    // Connect to MongoDB
    const connection = await mongoose.createConnection(config.mongodbUri, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    
    // Get server status
    const db = connection.db;
    const serverStatus = await db.command({ serverStatus: 1 });
    
    // Get collection stats
    const collections = await db.listCollections().toArray();
    const collectionStats = [];
    
    for (const collection of collections) {
      const stats = await db.command({ collStats: collection.name });
      collectionStats.push({
        name: collection.name,
        count: stats.count,
        size: stats.size,
        avgObjSize: stats.avgObjSize,
        storageSize: stats.storageSize,
        indexes: stats.nindexes,
        indexSize: stats.totalIndexSize
      });
    }
    
    // Close connection
    await connection.close();
    
    return {
      version: serverStatus.version,
      uptime: serverStatus.uptime,
      connections: serverStatus.connections,
      collections: collectionStats,
      indexStats: serverStatus.metrics?.queryExecutor?.scanned || 0,
      opcounters: serverStatus.opcounters
    };
  } catch (error) {
    console.warn('Error analyzing MongoDB:', error.message);
    return {
      error: error.message
    };
  }
}

/**
 * Analyze Redis
 * @returns {Promise<Object>} Redis information
 */
async function analyzeRedis() {
  console.log('Analyzing Redis...');
  
  try {
    // Connect to Redis
    const client = Redis.createClient({
      url: config.redisUri
    });
    
    // Connect to Redis
    await client.connect();
    
    // Get server info
    const info = await client.info();
    
    // Parse info
    const infoObj = {};
    const sections = info.split('#');
    
    for (const section of sections) {
      const lines = section.split('\r\n');
      const sectionName = lines[0].trim();
      
      if (sectionName) {
        infoObj[sectionName] = {};
        
        for (let i = 1; i < lines.length; i++) {
          const line = lines[i].trim();
          
          if (line) {
            const [key, value] = line.split(':');
            infoObj[sectionName][key] = value;
          }
        }
      }
    }
    
    // Get memory usage
    const memoryInfo = await client.info('memory');
    const usedMemory = memoryInfo.match(/used_memory:(\d+)/)?.[1];
    const usedMemoryPeak = memoryInfo.match(/used_memory_peak:(\d+)/)?.[1];
    
    // Close connection
    await client.quit();
    
    return {
      version: infoObj.Server?.redis_version,
      uptime: infoObj.Server?.uptime_in_seconds,
      clients: infoObj.Clients?.connected_clients,
      memory: {
        used: parseInt(usedMemory, 10),
        peak: parseInt(usedMemoryPeak, 10)
      },
      keyspace: infoObj.Keyspace
    };
  } catch (error) {
    console.warn('Error analyzing Redis:', error.message);
    return {
      error: error.message
    };
  }
}

/**
 * Analyze code
 * @returns {Object} Code information
 */
function analyzeCode() {
  console.log('Analyzing code...');
  
  try {
    // Get package.json
    const packageJson = JSON.parse(fs.readFileSync(path.join(__dirname, '..', 'package.json'), 'utf8'));
    
    // Get dependencies
    const dependencies = packageJson.dependencies || {};
    const devDependencies = packageJson.devDependencies || {};
    
    // Count files
    const fileCount = {
      js: countFiles('.js'),
      json: countFiles('.json'),
      md: countFiles('.md'),
      other: countFiles('')
    };
    
    // Count lines of code
    const linesOfCode = countLinesOfCode();
    
    return {
      dependencies: Object.keys(dependencies).length,
      devDependencies: Object.keys(devDependencies).length,
      fileCount,
      linesOfCode
    };
  } catch (error) {
    console.warn('Error analyzing code:', error.message);
    return {
      error: error.message
    };
  }
}

/**
 * Count files with a specific extension
 * @param {string} extension - File extension
 * @returns {number} - Number of files
 */
function countFiles(extension) {
  try {
    const result = execSync(`find . -type f -name "*${extension}" | wc -l`, {
      cwd: path.join(__dirname, '..'),
      encoding: 'utf8'
    });
    
    return parseInt(result.trim(), 10);
  } catch (error) {
    console.warn('Error counting files:', error.message);
    return 0;
  }
}

/**
 * Count lines of code
 * @returns {number} - Number of lines of code
 */
function countLinesOfCode() {
  try {
    const result = execSync('find . -name "*.js" -not -path "./node_modules/*" | xargs wc -l', {
      cwd: path.join(__dirname, '..'),
      encoding: 'utf8'
    });
    
    const match = result.match(/(\d+) total/);
    return match ? parseInt(match[1], 10) : 0;
  } catch (error) {
    console.warn('Error counting lines of code:', error.message);
    return 0;
  }
}

/**
 * Generate recommendations
 * @param {Object} systemInfo - System information
 * @param {Object} nodeInfo - Node.js information
 * @param {Object} mongoInfo - MongoDB information
 * @param {Object} redisInfo - Redis information
 * @param {Object} codeInfo - Code information
 * @returns {Array<Object>} - Recommendations
 */
function generateRecommendations(systemInfo, nodeInfo, mongoInfo, redisInfo, codeInfo) {
  console.log('Generating recommendations...');
  
  const recommendations = [];
  
  // System recommendations
  if (systemInfo.cpus.count > 1 && !nodeInfo.clusterMode) {
    recommendations.push({
      category: 'System',
      title: 'Enable Cluster Mode',
      description: 'Your system has multiple CPU cores, but cluster mode is not enabled. Enable cluster mode to utilize all available CPU cores.',
      impact: 'High',
      implementation: 'Set CLUSTER_ENABLED=true in your environment variables.'
    });
  }
  
  if (systemInfo.memory.usage > 0.8) {
    recommendations.push({
      category: 'System',
      title: 'Increase Available Memory',
      description: 'Your system is using more than 80% of available memory. Increase available memory to improve performance.',
      impact: 'High',
      implementation: 'Increase the memory allocation for your system or reduce memory usage by optimizing your application.'
    });
  }
  
  // Node.js recommendations
  if (nodeInfo.maxOldSpaceSize < 4096 && systemInfo.memory.total > 8 * 1024 * 1024 * 1024) {
    recommendations.push({
      category: 'Node.js',
      title: 'Increase Node.js Memory Limit',
      description: 'Your Node.js memory limit is set to a low value. Increase the memory limit to improve performance.',
      impact: 'Medium',
      implementation: 'Set NODE_OPTIONS="--max-old-space-size=4096" in your environment variables.'
    });
  }
  
  if (nodeInfo.environment !== 'production') {
    recommendations.push({
      category: 'Node.js',
      title: 'Use Production Environment',
      description: 'Your Node.js environment is not set to production. Set the environment to production to improve performance.',
      impact: 'Medium',
      implementation: 'Set NODE_ENV=production in your environment variables.'
    });
  }
  
  // MongoDB recommendations
  if (mongoInfo.error) {
    recommendations.push({
      category: 'MongoDB',
      title: 'Fix MongoDB Connection',
      description: `Unable to connect to MongoDB: ${mongoInfo.error}`,
      impact: 'High',
      implementation: 'Check your MongoDB connection string and ensure that MongoDB is running.'
    });
  } else {
    if (mongoInfo.collections) {
      for (const collection of mongoInfo.collections) {
        if (collection.count > 10000 && collection.indexes < 2) {
          recommendations.push({
            category: 'MongoDB',
            title: `Add Indexes to ${collection.name} Collection`,
            description: `The ${collection.name} collection has ${collection.count} documents but only ${collection.indexes} indexes. Add indexes to improve query performance.`,
            impact: 'High',
            implementation: 'Identify frequently queried fields and add indexes for them.'
          });
        }
      }
    }
  }
  
  // Redis recommendations
  if (redisInfo.error) {
    recommendations.push({
      category: 'Redis',
      title: 'Fix Redis Connection',
      description: `Unable to connect to Redis: ${redisInfo.error}`,
      impact: 'High',
      implementation: 'Check your Redis connection string and ensure that Redis is running.'
    });
  } else {
    if (redisInfo.memory && redisInfo.memory.used > 1024 * 1024 * 1024) {
      recommendations.push({
        category: 'Redis',
        title: 'Optimize Redis Memory Usage',
        description: 'Redis is using more than 1GB of memory. Optimize Redis memory usage to improve performance.',
        impact: 'Medium',
        implementation: 'Set appropriate TTL for cached data and consider using Redis eviction policies.'
      });
    }
  }
  
  // Code recommendations
  if (codeInfo.linesOfCode > 10000) {
    recommendations.push({
      category: 'Code',
      title: 'Optimize Code Structure',
      description: 'Your codebase is large. Consider optimizing code structure to improve maintainability and performance.',
      impact: 'Medium',
      implementation: 'Refactor code to use modular architecture and reduce duplication.'
    });
  }
  
  if (codeInfo.dependencies > 50) {
    recommendations.push({
      category: 'Code',
      title: 'Reduce Dependencies',
      description: 'Your application has a large number of dependencies. Reduce dependencies to improve startup time and reduce security risks.',
      impact: 'Low',
      implementation: 'Review dependencies and remove unused or unnecessary ones.'
    });
  }
  
  return recommendations;
}

/**
 * Generate report
 * @param {Object} systemInfo - System information
 * @param {Object} nodeInfo - Node.js information
 * @param {Object} mongoInfo - MongoDB information
 * @param {Object} redisInfo - Redis information
 * @param {Object} codeInfo - Code information
 * @param {Array<Object>} recommendations - Recommendations
 * @returns {string} - Report
 */
function generateReport(systemInfo, nodeInfo, mongoInfo, redisInfo, codeInfo, recommendations) {
  console.log('Generating report...');
  
  let report = `# NovaConnect UAC Performance Optimization Report\n\n`;
  report += `Generated on: ${new Date().toISOString()}\n\n`;
  
  // Add system information
  report += `## System Information\n\n`;
  report += `- **Platform**: ${systemInfo.platform} ${systemInfo.arch}\n`;
  report += `- **CPUs**: ${systemInfo.cpus.count} x ${systemInfo.cpus.model} @ ${systemInfo.cpus.speed} MHz\n`;
  report += `- **Memory**: ${formatBytes(systemInfo.memory.total)} total, ${formatBytes(systemInfo.memory.used)} used (${(systemInfo.memory.usage * 100).toFixed(2)}%)\n`;
  report += `- **Uptime**: ${formatDuration(systemInfo.uptime)}\n\n`;
  
  // Add Node.js information
  report += `## Node.js Information\n\n`;
  report += `- **Node.js Version**: ${nodeInfo.nodeVersion}\n`;
  report += `- **V8 Version**: ${nodeInfo.v8Version}\n`;
  report += `- **Memory Limit**: ${nodeInfo.maxOldSpaceSize} MB\n`;
  report += `- **Environment**: ${nodeInfo.environment}\n`;
  report += `- **Cluster Mode**: ${nodeInfo.clusterMode ? 'Enabled' : 'Disabled'}\n`;
  report += `- **Memory Usage**: ${formatBytes(nodeInfo.memoryUsage.rss)} (RSS), ${formatBytes(nodeInfo.memoryUsage.heapTotal)} (Heap Total), ${formatBytes(nodeInfo.memoryUsage.heapUsed)} (Heap Used)\n\n`;
  
  // Add MongoDB information
  report += `## MongoDB Information\n\n`;
  
  if (mongoInfo.error) {
    report += `- **Error**: ${mongoInfo.error}\n\n`;
  } else {
    report += `- **Version**: ${mongoInfo.version}\n`;
    report += `- **Uptime**: ${formatDuration(mongoInfo.uptime)}\n`;
    report += `- **Connections**: ${mongoInfo.connections?.current} current, ${mongoInfo.connections?.available} available\n`;
    report += `- **Operations**: ${JSON.stringify(mongoInfo.opcounters)}\n\n`;
    
    if (mongoInfo.collections) {
      report += `### Collections\n\n`;
      report += `| Collection | Documents | Size | Avg Object Size | Indexes | Index Size |\n`;
      report += `| --- | --- | --- | --- | --- | --- |\n`;
      
      for (const collection of mongoInfo.collections) {
        report += `| ${collection.name} | ${collection.count} | ${formatBytes(collection.size)} | ${formatBytes(collection.avgObjSize)} | ${collection.indexes} | ${formatBytes(collection.indexSize)} |\n`;
      }
      
      report += `\n`;
    }
  }
  
  // Add Redis information
  report += `## Redis Information\n\n`;
  
  if (redisInfo.error) {
    report += `- **Error**: ${redisInfo.error}\n\n`;
  } else {
    report += `- **Version**: ${redisInfo.version}\n`;
    report += `- **Uptime**: ${formatDuration(redisInfo.uptime)}\n`;
    report += `- **Connected Clients**: ${redisInfo.clients}\n`;
    report += `- **Memory Usage**: ${formatBytes(redisInfo.memory?.used)}, Peak: ${formatBytes(redisInfo.memory?.peak)}\n\n`;
    
    if (redisInfo.keyspace) {
      report += `### Keyspace\n\n`;
      report += `| Database | Keys | Expires | Avg TTL |\n`;
      report += `| --- | --- | --- | --- |\n`;
      
      for (const [db, info] of Object.entries(redisInfo.keyspace)) {
        const [keys, expires, avgTtl] = info.split(',').map(item => item.split('=')[1]);
        report += `| ${db} | ${keys} | ${expires} | ${avgTtl} |\n`;
      }
      
      report += `\n`;
    }
  }
  
  // Add code information
  report += `## Code Information\n\n`;
  report += `- **Dependencies**: ${codeInfo.dependencies}\n`;
  report += `- **Dev Dependencies**: ${codeInfo.devDependencies}\n`;
  report += `- **Files**: ${codeInfo.fileCount.js} JS, ${codeInfo.fileCount.json} JSON, ${codeInfo.fileCount.md} MD, ${codeInfo.fileCount.other} Other\n`;
  report += `- **Lines of Code**: ${codeInfo.linesOfCode}\n\n`;
  
  // Add recommendations
  report += `## Recommendations\n\n`;
  
  if (recommendations.length === 0) {
    report += `No recommendations found. Your system is optimized!\n\n`;
  } else {
    // Group recommendations by impact
    const highImpact = recommendations.filter(rec => rec.impact === 'High');
    const mediumImpact = recommendations.filter(rec => rec.impact === 'Medium');
    const lowImpact = recommendations.filter(rec => rec.impact === 'Low');
    
    // Add high impact recommendations
    if (highImpact.length > 0) {
      report += `### High Impact Recommendations\n\n`;
      
      for (const rec of highImpact) {
        report += `#### ${rec.title}\n\n`;
        report += `**Category**: ${rec.category}\n\n`;
        report += `**Description**: ${rec.description}\n\n`;
        report += `**Implementation**: ${rec.implementation}\n\n`;
      }
    }
    
    // Add medium impact recommendations
    if (mediumImpact.length > 0) {
      report += `### Medium Impact Recommendations\n\n`;
      
      for (const rec of mediumImpact) {
        report += `#### ${rec.title}\n\n`;
        report += `**Category**: ${rec.category}\n\n`;
        report += `**Description**: ${rec.description}\n\n`;
        report += `**Implementation**: ${rec.implementation}\n\n`;
      }
    }
    
    // Add low impact recommendations
    if (lowImpact.length > 0) {
      report += `### Low Impact Recommendations\n\n`;
      
      for (const rec of lowImpact) {
        report += `#### ${rec.title}\n\n`;
        report += `**Category**: ${rec.category}\n\n`;
        report += `**Description**: ${rec.description}\n\n`;
        report += `**Implementation**: ${rec.implementation}\n\n`;
      }
    }
  }
  
  // Add conclusion
  report += `## Conclusion\n\n`;
  report += `This performance optimization report provides recommendations for improving the performance of your NovaConnect UAC system. `;
  
  if (recommendations.length === 0) {
    report += `Your system is already well-optimized, but you can continue to monitor performance and make adjustments as needed.\n\n`;
  } else {
    report += `By implementing these recommendations, you can improve the performance, scalability, and reliability of your system.\n\n`;
    
    // Add priority recommendations
    const highImpact = recommendations.filter(rec => rec.impact === 'High');
    
    if (highImpact.length > 0) {
      report += `### Priority Actions\n\n`;
      report += `1. ${highImpact.map(rec => rec.title).join('\n2. ')}\n\n`;
    }
  }
  
  return report;
}

/**
 * Format bytes
 * @param {number} bytes - Bytes
 * @returns {string} - Formatted bytes
 */
function formatBytes(bytes) {
  if (bytes === undefined || bytes === null) {
    return 'N/A';
  }
  
  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  let value = bytes;
  let unitIndex = 0;
  
  while (value >= 1024 && unitIndex < units.length - 1) {
    value /= 1024;
    unitIndex++;
  }
  
  return `${value.toFixed(2)} ${units[unitIndex]}`;
}

/**
 * Format duration
 * @param {number} seconds - Duration in seconds
 * @returns {string} - Formatted duration
 */
function formatDuration(seconds) {
  if (seconds === undefined || seconds === null) {
    return 'N/A';
  }
  
  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  
  const parts = [];
  
  if (days > 0) {
    parts.push(`${days}d`);
  }
  
  if (hours > 0) {
    parts.push(`${hours}h`);
  }
  
  if (minutes > 0) {
    parts.push(`${minutes}m`);
  }
  
  if (remainingSeconds > 0 || parts.length === 0) {
    parts.push(`${remainingSeconds}s`);
  }
  
  return parts.join(' ');
}

// Run main function
if (require.main === module) {
  main().catch(error => {
    console.error('Error:', error);
    process.exit(1);
  });
}

module.exports = {
  analyzeSystemResources,
  analyzeNodeConfiguration,
  analyzeMongoDb,
  analyzeRedis,
  analyzeCode,
  generateRecommendations,
  generateReport
};
