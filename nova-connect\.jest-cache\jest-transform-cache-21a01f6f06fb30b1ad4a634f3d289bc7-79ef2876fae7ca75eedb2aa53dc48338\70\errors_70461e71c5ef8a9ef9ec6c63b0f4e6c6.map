{"version": 3, "names": ["ValidationError", "Error", "constructor", "message", "name", "statusCode", "AuthenticationError", "AuthorizationError", "NotFoundError", "ConflictError", "RateLimitError", "retryAfter", "BruteForceError", "ServerError", "module", "exports"], "sources": ["errors.js"], "sourcesContent": ["/**\n * Custom error classes\n */\n\nclass ValidationError extends Error {\n  constructor(message) {\n    super(message);\n    this.name = 'ValidationError';\n    this.statusCode = 400;\n  }\n}\n\nclass AuthenticationError extends Error {\n  constructor(message) {\n    super(message);\n    this.name = 'AuthenticationError';\n    this.statusCode = 401;\n  }\n}\n\nclass AuthorizationError extends Error {\n  constructor(message) {\n    super(message);\n    this.name = 'AuthorizationError';\n    this.statusCode = 403;\n  }\n}\n\nclass NotFoundError extends Error {\n  constructor(message) {\n    super(message);\n    this.name = 'NotFoundError';\n    this.statusCode = 404;\n  }\n}\n\nclass ConflictError extends Error {\n  constructor(message) {\n    super(message);\n    this.name = 'ConflictError';\n    this.statusCode = 409;\n  }\n}\n\nclass RateLimitError extends Error {\n  constructor(message, retryAfter) {\n    super(message);\n    this.name = 'RateLimitError';\n    this.statusCode = 429;\n    this.retryAfter = retryAfter;\n  }\n}\n\nclass BruteForceError extends Error {\n  constructor(message, retryAfter) {\n    super(message);\n    this.name = 'BruteForceError';\n    this.statusCode = 429;\n    this.retryAfter = retryAfter;\n  }\n}\n\nclass ServerError extends Error {\n  constructor(message) {\n    super(message);\n    this.name = 'ServerError';\n    this.statusCode = 500;\n  }\n}\n\nmodule.exports = {\n  ValidationError,\n  AuthenticationError,\n  AuthorizationError,\n  NotFoundError,\n  ConflictError,\n  RateLimitError,\n  BruteForceError,\n  ServerError\n};\n"], "mappings": "AAAA;AACA;AACA;;AAEA,MAAMA,eAAe,SAASC,KAAK,CAAC;EAClCC,WAAWA,CAACC,OAAO,EAAE;IACnB,KAAK,CAACA,OAAO,CAAC;IACd,IAAI,CAACC,IAAI,GAAG,iBAAiB;IAC7B,IAAI,CAACC,UAAU,GAAG,GAAG;EACvB;AACF;AAEA,MAAMC,mBAAmB,SAASL,KAAK,CAAC;EACtCC,WAAWA,CAACC,OAAO,EAAE;IACnB,KAAK,CAACA,OAAO,CAAC;IACd,IAAI,CAACC,IAAI,GAAG,qBAAqB;IACjC,IAAI,CAACC,UAAU,GAAG,GAAG;EACvB;AACF;AAEA,MAAME,kBAAkB,SAASN,KAAK,CAAC;EACrCC,WAAWA,CAACC,OAAO,EAAE;IACnB,KAAK,CAACA,OAAO,CAAC;IACd,IAAI,CAACC,IAAI,GAAG,oBAAoB;IAChC,IAAI,CAACC,UAAU,GAAG,GAAG;EACvB;AACF;AAEA,MAAMG,aAAa,SAASP,KAAK,CAAC;EAChCC,WAAWA,CAACC,OAAO,EAAE;IACnB,KAAK,CAACA,OAAO,CAAC;IACd,IAAI,CAACC,IAAI,GAAG,eAAe;IAC3B,IAAI,CAACC,UAAU,GAAG,GAAG;EACvB;AACF;AAEA,MAAMI,aAAa,SAASR,KAAK,CAAC;EAChCC,WAAWA,CAACC,OAAO,EAAE;IACnB,KAAK,CAACA,OAAO,CAAC;IACd,IAAI,CAACC,IAAI,GAAG,eAAe;IAC3B,IAAI,CAACC,UAAU,GAAG,GAAG;EACvB;AACF;AAEA,MAAMK,cAAc,SAAST,KAAK,CAAC;EACjCC,WAAWA,CAACC,OAAO,EAAEQ,UAAU,EAAE;IAC/B,KAAK,CAACR,OAAO,CAAC;IACd,IAAI,CAACC,IAAI,GAAG,gBAAgB;IAC5B,IAAI,CAACC,UAAU,GAAG,GAAG;IACrB,IAAI,CAACM,UAAU,GAAGA,UAAU;EAC9B;AACF;AAEA,MAAMC,eAAe,SAASX,KAAK,CAAC;EAClCC,WAAWA,CAACC,OAAO,EAAEQ,UAAU,EAAE;IAC/B,KAAK,CAACR,OAAO,CAAC;IACd,IAAI,CAACC,IAAI,GAAG,iBAAiB;IAC7B,IAAI,CAACC,UAAU,GAAG,GAAG;IACrB,IAAI,CAACM,UAAU,GAAGA,UAAU;EAC9B;AACF;AAEA,MAAME,WAAW,SAASZ,KAAK,CAAC;EAC9BC,WAAWA,CAACC,OAAO,EAAE;IACnB,KAAK,CAACA,OAAO,CAAC;IACd,IAAI,CAACC,IAAI,GAAG,aAAa;IACzB,IAAI,CAACC,UAAU,GAAG,GAAG;EACvB;AACF;AAEAS,MAAM,CAACC,OAAO,GAAG;EACff,eAAe;EACfM,mBAAmB;EACnBC,kBAAkB;EAClBC,aAAa;EACbC,aAAa;EACbC,cAAc;EACdE,eAAe;EACfC;AACF,CAAC", "ignoreList": []}