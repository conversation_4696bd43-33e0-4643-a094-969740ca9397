/**
 * NovaCore Index
 *
 * This file exports all the components of the NovaCore system.
 * NovaCore is the foundation of the NovaFuse Cyber-Safety Platform, providing
 * tensor-based runtime and real-time control capabilities.
 */

// Export tensor models
const Tensor = require('./models/Tensor');

// Export tensor runtime
const {
  TensorRuntime,
  createTensor,
  transformTensor,
  processTensor
} = require('./runtime/tensor-runtime');

// Export tensor utilities
const tensorUtils = require('./utils/tensor-utils');

// Export control system
const {
  ControlSystem,
  ControlLoop,
  ControlLoopStatus,
  ControlLoopPriority
} = require('./control/control-system');

// Export event processor
const {
  EventProcessor,
  Event,
  EventPriority,
  EventStatus
} = require('./events/event-processor');

// Export component communicator
const {
  ComponentCommunicator,
  Message,
  MessageType,
  ComponentStatus
} = require('./events/component-communicator');

// Export all components
module.exports = {
  // Models
  Tensor,

  // Runtime
  TensorRuntime,
  createTensor,
  transformTensor,
  processTensor,

  // Control System
  ControlSystem,
  ControlLoop,
  ControlLoopStatus,
  ControlLoopPriority,

  // Event Processing
  EventProcessor,
  Event,
  EventPriority,
  EventStatus,

  // Component Communication
  ComponentCommunicator,
  Message,
  MessageType,
  ComponentStatus,

  // Utilities
  tensorUtils
};
