import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Typography,
  Divider,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  Switch,
  FormControlLabel,
  TextField,
  Slider,
  IconButton,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Tabs,
  Tab,
  Snackbar,
  Alert
} from '@mui/material';
import {
  Save as SaveIcon,
  Delete as DeleteIcon,
  Upload as ImportIcon,
  Download as ExportIcon,
  Settings as SettingsIcon,
  Bookmark as BookmarkIcon,
  BookmarkBorder as BookmarkBorderIcon,
  Refresh as ResetIcon
} from '@mui/icons-material';
import userPreferencesService from '../../services/UserPreferencesService';

/**
 * VisualizationPreferences component
 * 
 * Provides controls for managing visualization preferences
 */
function VisualizationPreferences({
  visualizationType,
  options,
  onOptionsChange,
  onPresetLoad
}) {
  const [open, setOpen] = useState(false);
  const [tabIndex, setTabIndex] = useState(0);
  const [presetName, setPresetName] = useState('');
  const [presets, setPresets] = useState({});
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState('success');
  const [importDialogOpen, setImportDialogOpen] = useState(false);
  const [importText, setImportText] = useState('');
  
  // Load presets on mount
  useEffect(() => {
    setPresets(userPreferencesService.getVisualizationPresets());
  }, []);
  
  // Open dialog
  const handleOpen = () => {
    setOpen(true);
  };
  
  // Close dialog
  const handleClose = () => {
    setOpen(false);
  };
  
  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setTabIndex(newValue);
  };
  
  // Save current options as preset
  const handleSavePreset = () => {
    if (!presetName) {
      showSnackbar('Please enter a preset name', 'error');
      return;
    }
    
    // Save preset
    userPreferencesService.saveVisualizationPreset(presetName, {
      visualizationType,
      options: { ...options }
    });
    
    // Update presets
    setPresets(userPreferencesService.getVisualizationPresets());
    
    // Clear preset name
    setPresetName('');
    
    // Show success message
    showSnackbar(`Preset "${presetName}" saved successfully`);
  };
  
  // Load preset
  const handleLoadPreset = (name) => {
    const preset = userPreferencesService.getVisualizationPreset(name);
    
    if (preset) {
      // Update options
      if (onOptionsChange) {
        onOptionsChange(preset.options);
      }
      
      // Load preset
      if (onPresetLoad) {
        onPresetLoad(preset);
      }
      
      // Show success message
      showSnackbar(`Preset "${name}" loaded successfully`);
      
      // Close dialog
      handleClose();
    }
  };
  
  // Delete preset
  const handleDeletePreset = (name) => {
    userPreferencesService.deleteVisualizationPreset(name);
    
    // Update presets
    setPresets(userPreferencesService.getVisualizationPresets());
    
    // Show success message
    showSnackbar(`Preset "${name}" deleted successfully`);
  };
  
  // Save current options as default
  const handleSaveAsDefault = () => {
    // Save visualization preferences
    Object.entries(options).forEach(([key, value]) => {
      userPreferencesService.setPreference(`visualizations.${key}`, value);
    });
    
    // Save visualization type
    userPreferencesService.setPreference('visualizations.defaultType', visualizationType);
    
    // Show success message
    showSnackbar('Default preferences saved successfully');
  };
  
  // Reset to default preferences
  const handleResetToDefault = () => {
    // Get default preferences
    const defaultPreferences = userPreferencesService.getVisualizationPreferences();
    
    // Update options
    if (onOptionsChange) {
      onOptionsChange(defaultPreferences);
    }
    
    // Show success message
    showSnackbar('Reset to default preferences');
    
    // Close dialog
    handleClose();
  };
  
  // Export preferences
  const handleExportPreferences = () => {
    const json = userPreferencesService.exportPreferences();
    
    // Create blob
    const blob = new Blob([json], { type: 'application/json' });
    
    // Create download link
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = 'novafuse-preferences.json';
    
    // Trigger download
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // Show success message
    showSnackbar('Preferences exported successfully');
  };
  
  // Open import dialog
  const handleOpenImportDialog = () => {
    setImportDialogOpen(true);
  };
  
  // Close import dialog
  const handleCloseImportDialog = () => {
    setImportDialogOpen(false);
    setImportText('');
  };
  
  // Import preferences
  const handleImportPreferences = () => {
    if (!importText) {
      showSnackbar('Please enter preferences JSON', 'error');
      return;
    }
    
    // Import preferences
    const success = userPreferencesService.importPreferences(importText);
    
    if (success) {
      // Update presets
      setPresets(userPreferencesService.getVisualizationPresets());
      
      // Show success message
      showSnackbar('Preferences imported successfully');
      
      // Close dialog
      handleCloseImportDialog();
    } else {
      // Show error message
      showSnackbar('Failed to import preferences', 'error');
    }
  };
  
  // Show snackbar
  const showSnackbar = (message, severity = 'success') => {
    setSnackbarMessage(message);
    setSnackbarSeverity(severity);
    setSnackbarOpen(true);
  };
  
  // Close snackbar
  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };

  return (
    <>
      <Button
        variant="outlined"
        color="primary"
        startIcon={<SettingsIcon />}
        onClick={handleOpen}
        size="small"
      >
        Preferences
      </Button>
      
      <Dialog
        open={open}
        onClose={handleClose}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Visualization Preferences</DialogTitle>
        <DialogContent>
          <Tabs
            value={tabIndex}
            onChange={handleTabChange}
            indicatorColor="primary"
            textColor="primary"
            variant="fullWidth"
            sx={{ mb: 2 }}
          >
            <Tab label="Presets" />
            <Tab label="Default Settings" />
            <Tab label="Import/Export" />
          </Tabs>
          
          {/* Presets Tab */}
          {tabIndex === 0 && (
            <Box>
              <Typography variant="subtitle1" gutterBottom>
                Save Current Settings as Preset
              </Typography>
              
              <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
                <TextField
                  label="Preset Name"
                  value={presetName}
                  onChange={(e) => setPresetName(e.target.value)}
                  fullWidth
                />
                
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<SaveIcon />}
                  onClick={handleSavePreset}
                  disabled={!presetName}
                >
                  Save
                </Button>
              </Box>
              
              <Divider sx={{ my: 2 }} />
              
              <Typography variant="subtitle1" gutterBottom>
                Saved Presets
              </Typography>
              
              {Object.keys(presets).length === 0 ? (
                <Typography variant="body2" color="text.secondary">
                  No presets saved yet. Save your current settings as a preset to see them here.
                </Typography>
              ) : (
                <List>
                  {Object.entries(presets).map(([name, preset]) => (
                    <ListItem
                      key={name}
                      button
                      onClick={() => handleLoadPreset(name)}
                    >
                      <ListItemText
                        primary={name}
                        secondary={`Type: ${preset.visualizationType}`}
                      />
                      <ListItemSecondaryAction>
                        <IconButton
                          edge="end"
                          aria-label="delete"
                          onClick={() => handleDeletePreset(name)}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </ListItemSecondaryAction>
                    </ListItem>
                  ))}
                </List>
              )}
            </Box>
          )}
          
          {/* Default Settings Tab */}
          {tabIndex === 1 && (
            <Box>
              <Typography variant="subtitle1" gutterBottom>
                Default Visualization Settings
              </Typography>
              
              <Typography variant="body2" color="text.secondary" paragraph>
                Save your current settings as the default for all visualizations. These settings will be applied whenever you create a new visualization.
              </Typography>
              
              <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<BookmarkIcon />}
                  onClick={handleSaveAsDefault}
                >
                  Save as Default
                </Button>
                
                <Button
                  variant="outlined"
                  color="primary"
                  startIcon={<ResetIcon />}
                  onClick={handleResetToDefault}
                >
                  Reset to Default
                </Button>
              </Box>
              
              <Divider sx={{ my: 2 }} />
              
              <Typography variant="subtitle1" gutterBottom>
                Current Settings
              </Typography>
              
              <Typography variant="body2" gutterBottom>
                Visualization Type: {visualizationType}
              </Typography>
              
              <Box sx={{ ml: 2 }}>
                {Object.entries(options).map(([key, value]) => (
                  <Typography key={key} variant="body2" gutterBottom>
                    {key}: {typeof value === 'boolean' ? (value ? 'Yes' : 'No') : value.toString()}
                  </Typography>
                ))}
              </Box>
            </Box>
          )}
          
          {/* Import/Export Tab */}
          {tabIndex === 2 && (
            <Box>
              <Typography variant="subtitle1" gutterBottom>
                Export Preferences
              </Typography>
              
              <Typography variant="body2" color="text.secondary" paragraph>
                Export all your preferences, including presets, to a JSON file. You can import this file later to restore your preferences.
              </Typography>
              
              <Button
                variant="contained"
                color="primary"
                startIcon={<ExportIcon />}
                onClick={handleExportPreferences}
              >
                Export Preferences
              </Button>
              
              <Divider sx={{ my: 2 }} />
              
              <Typography variant="subtitle1" gutterBottom>
                Import Preferences
              </Typography>
              
              <Typography variant="body2" color="text.secondary" paragraph>
                Import preferences from a JSON file. This will overwrite your current preferences.
              </Typography>
              
              <Button
                variant="contained"
                color="primary"
                startIcon={<ImportIcon />}
                onClick={handleOpenImportDialog}
              >
                Import Preferences
              </Button>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose}>Close</Button>
        </DialogActions>
      </Dialog>
      
      {/* Import Dialog */}
      <Dialog
        open={importDialogOpen}
        onClose={handleCloseImportDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Import Preferences</DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary" paragraph>
            Paste the JSON content of your preferences file below.
          </Typography>
          
          <TextField
            label="Preferences JSON"
            multiline
            rows={10}
            value={importText}
            onChange={(e) => setImportText(e.target.value)}
            fullWidth
            variant="outlined"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseImportDialog}>Cancel</Button>
          <Button
            onClick={handleImportPreferences}
            variant="contained"
            color="primary"
            disabled={!importText}
          >
            Import
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Snackbar */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleSnackbarClose}
          severity={snackbarSeverity}
          sx={{ width: '100%' }}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </>
  );
}

export default VisualizationPreferences;
