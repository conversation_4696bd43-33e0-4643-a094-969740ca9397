/**
 * NovaFuse Universal API Connector API Key Model
 * 
 * This model defines the schema for API keys in the NovaConnect UAC.
 */

const mongoose = require('mongoose');
const crypto = require('crypto');
const Schema = mongoose.Schema;

const apiKeySchema = new Schema({
  userId: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'User', 
    required: true 
  },
  name: { 
    type: String, 
    required: true, 
    trim: true 
  },
  key: { 
    type: String, 
    required: true, 
    unique: true 
  },
  secret: { 
    type: String, 
    required: true 
  },
  permissions: [{ 
    type: String 
  }],
  scopes: [{ 
    type: String 
  }],
  status: { 
    type: String, 
    enum: ['active', 'inactive', 'revoked'], 
    default: 'active' 
  },
  expiresAt: { 
    type: Date 
  },
  lastUsed: { 
    type: Date 
  },
  usageCount: { 
    type: Number, 
    default: 0 
  },
  rateLimit: {
    limit: { type: Number, default: 100 },
    window: { type: Number, default: 60 }, // in seconds
    remaining: { type: Number }
  },
  ipRestrictions: [{ 
    type: String 
  }],
  allowedOrigins: [{ 
    type: String 
  }],
  metadata: { 
    type: Object 
  }
}, {
  timestamps: true
});

// Add pre-save hook to generate API key and secret if not provided
apiKeySchema.pre('save', function(next) {
  // Only generate key and secret if they're not already set
  if (!this.isNew) return next();
  
  if (!this.key) {
    this.key = `nf_${crypto.randomBytes(16).toString('hex')}`;
  }
  
  if (!this.secret) {
    this.secret = crypto.randomBytes(32).toString('hex');
  }
  
  // Set initial rate limit remaining
  if (this.rateLimit && this.rateLimit.limit && !this.rateLimit.remaining) {
    this.rateLimit.remaining = this.rateLimit.limit;
  }
  
  next();
});

// Add method to check if API key is expired
apiKeySchema.methods.isExpired = function() {
  return this.expiresAt && this.expiresAt < new Date();
};

// Add method to check if API key is active
apiKeySchema.methods.isActive = function() {
  return this.status === 'active' && !this.isExpired();
};

// Add method to check if API key has a specific permission
apiKeySchema.methods.hasPermission = function(permission) {
  if (Array.isArray(permission)) {
    return permission.some(p => this.permissions.includes(p));
  }
  
  return this.permissions.includes(permission);
};

// Add method to check if API key has a specific scope
apiKeySchema.methods.hasScope = function(scope) {
  if (Array.isArray(scope)) {
    return scope.some(s => this.scopes.includes(s));
  }
  
  return this.scopes.includes(scope);
};

// Add method to check if IP is allowed
apiKeySchema.methods.isIpAllowed = function(ip) {
  // If no IP restrictions, all IPs are allowed
  if (!this.ipRestrictions || this.ipRestrictions.length === 0) {
    return true;
  }
  
  return this.ipRestrictions.includes(ip);
};

// Add method to check if origin is allowed
apiKeySchema.methods.isOriginAllowed = function(origin) {
  // If no origin restrictions, all origins are allowed
  if (!this.allowedOrigins || this.allowedOrigins.length === 0) {
    return true;
  }
  
  return this.allowedOrigins.includes(origin);
};

// Add method to update usage
apiKeySchema.methods.updateUsage = function() {
  this.lastUsed = new Date();
  this.usageCount += 1;
  return this.save();
};

// Add method to reset rate limit
apiKeySchema.methods.resetRateLimit = function() {
  if (this.rateLimit) {
    this.rateLimit.remaining = this.rateLimit.limit;
  }
  return this.save();
};

// Add method to consume rate limit
apiKeySchema.methods.consumeRateLimit = function() {
  if (this.rateLimit && this.rateLimit.remaining > 0) {
    this.rateLimit.remaining -= 1;
    return this.save();
  }
  return false;
};

// Add method to revoke API key
apiKeySchema.methods.revoke = function() {
  this.status = 'revoked';
  return this.save();
};

// Add indexes
apiKeySchema.index({ key: 1 });
apiKeySchema.index({ userId: 1 });
apiKeySchema.index({ status: 1 });
apiKeySchema.index({ expiresAt: 1 });

// Create model
const ApiKey = mongoose.model('ApiKey', apiKeySchema);

module.exports = ApiKey;
