@echo off
setlocal enabledelayedexpansion

set "file=d:\\novafuse-api-superstore\\coherence-reality-systems\\Comphyology Master Archive\\3. APPENDIX A - MATH FOUNDATION\\The Comphyological Dictionary 1st Edition.md"
set "backup=%file%.backup.%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%"

:: Create backup
copy "%file%" "%backup%" >nul
echo Created backup at: %backup%

:: Read the file
set "content="
for /f "delims=" %%a in ('type "%file%" ^& break ^>"%file%"') do (
    set "line=%%a"
    
    :: Apply replacements
    set "line=!line:Ã°Å¸â€=💡!"
    set "line=!line:Ã°Å¸Â§Â¬=📋!"
    set "line=!line:Ã°Å¸Â§Â=⚠️!"
    set "line=!line:ÃŽÂº=κ!"
    set "line=!line:Ã—=×!"
    set "line=!line:Ã¢Å“Â¨=✨!"
    set "line=!line:Ã¢â€œ="!"
    set "line=!line:Ã¢â€="!"
    set "line=!line:Ã¢â€˜='!"
    set "line=!line:Ã¢â„¢Â=™!"
    set "line=!line:Ã‚Â= !"
    
    :: Write the line back to the file
    echo !line!>>"%file%"
)

echo Fixed character rendering in the dictionary file.
echo Please review the changes in the file: %file%
