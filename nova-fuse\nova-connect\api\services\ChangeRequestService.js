/**
 * Change Request Service
 * 
 * This service handles change requests for governance controls.
 */

const fs = require('fs').promises;
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const { ValidationError, NotFoundError, AuthorizationError } = require('../utils/errors');
const TeamService = require('./TeamService');
const AuditService = require('./AuditService');

class ChangeRequestService {
  constructor(dataDir = path.join(__dirname, '../data')) {
    this.dataDir = dataDir;
    this.changeRequestsFile = path.join(this.dataDir, 'change_requests.json');
    this.changeRequestCommentsFile = path.join(this.dataDir, 'change_request_comments.json');
    this.changeRequestApprovalsFile = path.join(this.dataDir, 'change_request_approvals.json');
    this.teamService = new TeamService(dataDir);
    this.auditService = new AuditService(dataDir);
    this.ensureDataDir();
  }

  /**
   * Ensure the data directory exists
   */
  async ensureDataDir() {
    try {
      await fs.mkdir(this.dataDir, { recursive: true });
      
      // Initialize files if they don't exist
      await this.initializeFile(this.changeRequestsFile, []);
      await this.initializeFile(this.changeRequestCommentsFile, []);
      await this.initializeFile(this.changeRequestApprovalsFile, []);
    } catch (error) {
      console.error('Error creating data directory:', error);
      throw error;
    }
  }

  /**
   * Initialize a file with default data if it doesn't exist
   */
  async initializeFile(filePath, defaultData) {
    try {
      await fs.access(filePath);
    } catch (error) {
      if (error.code === 'ENOENT') {
        // File doesn't exist, create it with default data
        await fs.writeFile(filePath, JSON.stringify(defaultData, null, 2));
      } else {
        throw error;
      }
    }
  }

  /**
   * Load data from file
   */
  async loadData(filePath) {
    try {
      const data = await fs.readFile(filePath, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      if (error.code === 'ENOENT') {
        // File doesn't exist, return empty array
        return [];
      }
      console.error(`Error loading data from ${filePath}:`, error);
      throw error;
    }
  }

  /**
   * Save data to file
   */
  async saveData(filePath, data) {
    try {
      await fs.writeFile(filePath, JSON.stringify(data, null, 2));
    } catch (error) {
      console.error(`Error saving data to ${filePath}:`, error);
      throw error;
    }
  }

  /**
   * Get all change requests
   */
  async getAllChangeRequests(filters = {}) {
    const changeRequests = await this.loadData(this.changeRequestsFile);
    
    // Apply filters
    let filteredRequests = changeRequests;
    
    if (filters.status) {
      filteredRequests = filteredRequests.filter(cr => cr.status === filters.status);
    }
    
    if (filters.createdBy) {
      filteredRequests = filteredRequests.filter(cr => cr.createdBy === filters.createdBy);
    }
    
    if (filters.teamId) {
      filteredRequests = filteredRequests.filter(cr => cr.teamId === filters.teamId);
    }
    
    if (filters.environmentId) {
      filteredRequests = filteredRequests.filter(cr => cr.environmentId === filters.environmentId);
    }
    
    if (filters.resourceType) {
      filteredRequests = filteredRequests.filter(cr => cr.resourceType === filters.resourceType);
    }
    
    if (filters.resourceId) {
      filteredRequests = filteredRequests.filter(cr => cr.resourceId === filters.resourceId);
    }
    
    if (filters.startDate) {
      const startDate = new Date(filters.startDate);
      filteredRequests = filteredRequests.filter(cr => new Date(cr.created) >= startDate);
    }
    
    if (filters.endDate) {
      const endDate = new Date(filters.endDate);
      filteredRequests = filteredRequests.filter(cr => new Date(cr.created) <= endDate);
    }
    
    // Sort by created date (newest first)
    filteredRequests.sort((a, b) => new Date(b.created) - new Date(a.created));
    
    return filteredRequests;
  }

  /**
   * Get change requests for a user
   */
  async getChangeRequestsForUser(userId, filters = {}) {
    const changeRequests = await this.getAllChangeRequests(filters);
    
    // Get user's teams
    const userTeams = await this.teamService.getTeamsForUser(userId);
    const userTeamIds = userTeams.map(team => team.id);
    
    // Filter change requests that are created by the user or belong to user's teams
    return changeRequests.filter(cr => 
      cr.createdBy === userId || 
      (cr.teamId && userTeamIds.includes(cr.teamId))
    );
  }

  /**
   * Get change requests that need approval from a user
   */
  async getChangeRequestsForApproval(userId, filters = {}) {
    const changeRequests = await this.getAllChangeRequests({ ...filters, status: 'pending' });
    
    // Get user's teams and roles
    const userTeams = await this.teamService.getTeamsForUser(userId);
    
    // Filter change requests that need approval from the user
    const result = [];
    
    for (const cr of changeRequests) {
      // Skip if no team is associated
      if (!cr.teamId) continue;
      
      // Find user's role in the team
      const userTeam = userTeams.find(team => team.id === cr.teamId);
      if (!userTeam) continue;
      
      // Get user's membership in the team
      try {
        const membership = await this.teamService.getUserTeamMembership(cr.teamId, userId);
        
        // Check if user has approval role (owner or admin)
        if (['owner', 'admin'].includes(membership.role)) {
          // Check if user hasn't already approved
          const approvals = await this.getChangeRequestApprovals(cr.id);
          const hasApproved = approvals.some(approval => approval.userId === userId);
          
          if (!hasApproved) {
            result.push(cr);
          }
        }
      } catch (error) {
        // User is not a member of the team, skip
        continue;
      }
    }
    
    return result;
  }

  /**
   * Get change request by ID
   */
  async getChangeRequestById(id) {
    const changeRequests = await this.loadData(this.changeRequestsFile);
    const changeRequest = changeRequests.find(cr => cr.id === id);
    
    if (!changeRequest) {
      throw new NotFoundError(`Change request with ID ${id} not found`);
    }
    
    return changeRequest;
  }

  /**
   * Create a new change request
   */
  async createChangeRequest(data, userId) {
    if (!data.title) {
      throw new ValidationError('Change request title is required');
    }
    
    if (!data.resourceType) {
      throw new ValidationError('Resource type is required');
    }
    
    if (!data.resourceId) {
      throw new ValidationError('Resource ID is required');
    }
    
    if (!data.changes) {
      throw new ValidationError('Changes are required');
    }
    
    // If team ID is provided, check if user is a member
    if (data.teamId) {
      const isMember = await this.teamService.isUserTeamMember(data.teamId, userId);
      
      if (!isMember) {
        throw new AuthorizationError('You are not a member of this team');
      }
    }
    
    const changeRequests = await this.loadData(this.changeRequestsFile);
    
    // Create new change request
    const newChangeRequest = {
      id: uuidv4(),
      title: data.title,
      description: data.description || '',
      resourceType: data.resourceType,
      resourceId: data.resourceId,
      changes: data.changes,
      status: 'pending',
      createdBy: userId,
      teamId: data.teamId || null,
      environmentId: data.environmentId || null,
      priority: data.priority || 'medium',
      requiredApprovals: data.requiredApprovals || 1,
      dueDate: data.dueDate || null,
      created: new Date().toISOString(),
      updated: new Date().toISOString()
    };
    
    changeRequests.push(newChangeRequest);
    await this.saveData(this.changeRequestsFile, changeRequests);
    
    // Log audit event
    await this.auditService.logEvent({
      userId,
      action: 'CREATE',
      resourceType: 'change_request',
      resourceId: newChangeRequest.id,
      details: {
        title: newChangeRequest.title,
        resourceType: newChangeRequest.resourceType,
        resourceId: newChangeRequest.resourceId
      },
      teamId: newChangeRequest.teamId,
      environmentId: newChangeRequest.environmentId
    });
    
    return newChangeRequest;
  }

  /**
   * Update a change request
   */
  async updateChangeRequest(id, data, userId) {
    const changeRequests = await this.loadData(this.changeRequestsFile);
    const index = changeRequests.findIndex(cr => cr.id === id);
    
    if (index === -1) {
      throw new NotFoundError(`Change request with ID ${id} not found`);
    }
    
    const changeRequest = changeRequests[index];
    
    // Check if user has permission to update
    if (changeRequest.createdBy !== userId) {
      // If not the creator, check if user is team owner or admin
      if (changeRequest.teamId) {
        try {
          await this.teamService.checkTeamPermission(changeRequest.teamId, userId, ['owner', 'admin']);
        } catch (error) {
          throw new AuthorizationError('You do not have permission to update this change request');
        }
      } else {
        throw new AuthorizationError('You do not have permission to update this change request');
      }
    }
    
    // Check if change request is already approved or rejected
    if (['approved', 'rejected', 'implemented'].includes(changeRequest.status)) {
      throw new ValidationError(`Cannot update a change request with status '${changeRequest.status}'`);
    }
    
    // Update change request
    const updatedChangeRequest = {
      ...changeRequest,
      ...data,
      id, // Don't allow changing the ID
      createdBy: changeRequest.createdBy, // Don't allow changing the creator
      status: data.status || changeRequest.status, // Allow updating status
      updated: new Date().toISOString()
    };
    
    changeRequests[index] = updatedChangeRequest;
    await this.saveData(this.changeRequestsFile, changeRequests);
    
    // Log audit event
    await this.auditService.logEvent({
      userId,
      action: 'UPDATE',
      resourceType: 'change_request',
      resourceId: id,
      details: {
        title: updatedChangeRequest.title,
        status: updatedChangeRequest.status
      },
      teamId: updatedChangeRequest.teamId,
      environmentId: updatedChangeRequest.environmentId
    });
    
    return updatedChangeRequest;
  }

  /**
   * Delete a change request
   */
  async deleteChangeRequest(id, userId) {
    const changeRequests = await this.loadData(this.changeRequestsFile);
    const index = changeRequests.findIndex(cr => cr.id === id);
    
    if (index === -1) {
      throw new NotFoundError(`Change request with ID ${id} not found`);
    }
    
    const changeRequest = changeRequests[index];
    
    // Check if user has permission to delete
    if (changeRequest.createdBy !== userId) {
      // If not the creator, check if user is team owner or admin
      if (changeRequest.teamId) {
        try {
          await this.teamService.checkTeamPermission(changeRequest.teamId, userId, ['owner', 'admin']);
        } catch (error) {
          throw new AuthorizationError('You do not have permission to delete this change request');
        }
      } else {
        throw new AuthorizationError('You do not have permission to delete this change request');
      }
    }
    
    // Check if change request is already approved or implemented
    if (['approved', 'implemented'].includes(changeRequest.status)) {
      throw new ValidationError(`Cannot delete a change request with status '${changeRequest.status}'`);
    }
    
    // Remove the change request
    changeRequests.splice(index, 1);
    await this.saveData(this.changeRequestsFile, changeRequests);
    
    // Remove all comments and approvals
    await this.deleteChangeRequestComments(id);
    await this.deleteChangeRequestApprovals(id);
    
    // Log audit event
    await this.auditService.logEvent({
      userId,
      action: 'DELETE',
      resourceType: 'change_request',
      resourceId: id,
      details: {
        title: changeRequest.title
      },
      teamId: changeRequest.teamId,
      environmentId: changeRequest.environmentId
    });
    
    return { success: true, message: `Change request ${id} deleted` };
  }

  /**
   * Get change request comments
   */
  async getChangeRequestComments(changeRequestId) {
    const comments = await this.loadData(this.changeRequestCommentsFile);
    return comments
      .filter(comment => comment.changeRequestId === changeRequestId)
      .sort((a, b) => new Date(a.created) - new Date(b.created));
  }

  /**
   * Add comment to change request
   */
  async addChangeRequestComment(changeRequestId, comment, userId) {
    // Check if change request exists
    await this.getChangeRequestById(changeRequestId);
    
    if (!comment.text) {
      throw new ValidationError('Comment text is required');
    }
    
    const comments = await this.loadData(this.changeRequestCommentsFile);
    
    // Create new comment
    const newComment = {
      id: uuidv4(),
      changeRequestId,
      text: comment.text,
      userId,
      created: new Date().toISOString(),
      updated: new Date().toISOString()
    };
    
    comments.push(newComment);
    await this.saveData(this.changeRequestCommentsFile, comments);
    
    // Log audit event
    await this.auditService.logEvent({
      userId,
      action: 'CREATE',
      resourceType: 'change_request_comment',
      resourceId: newComment.id,
      details: {
        changeRequestId,
        text: newComment.text.substring(0, 50) + (newComment.text.length > 50 ? '...' : '')
      }
    });
    
    return newComment;
  }

  /**
   * Update comment
   */
  async updateComment(commentId, data, userId) {
    const comments = await this.loadData(this.changeRequestCommentsFile);
    const index = comments.findIndex(comment => comment.id === commentId);
    
    if (index === -1) {
      throw new NotFoundError(`Comment with ID ${commentId} not found`);
    }
    
    const comment = comments[index];
    
    // Check if user has permission to update
    if (comment.userId !== userId) {
      throw new AuthorizationError('You do not have permission to update this comment');
    }
    
    if (!data.text) {
      throw new ValidationError('Comment text is required');
    }
    
    // Update comment
    const updatedComment = {
      ...comment,
      text: data.text,
      updated: new Date().toISOString()
    };
    
    comments[index] = updatedComment;
    await this.saveData(this.changeRequestCommentsFile, comments);
    
    // Log audit event
    await this.auditService.logEvent({
      userId,
      action: 'UPDATE',
      resourceType: 'change_request_comment',
      resourceId: commentId,
      details: {
        changeRequestId: comment.changeRequestId,
        text: updatedComment.text.substring(0, 50) + (updatedComment.text.length > 50 ? '...' : '')
      }
    });
    
    return updatedComment;
  }

  /**
   * Delete comment
   */
  async deleteComment(commentId, userId) {
    const comments = await this.loadData(this.changeRequestCommentsFile);
    const index = comments.findIndex(comment => comment.id === commentId);
    
    if (index === -1) {
      throw new NotFoundError(`Comment with ID ${commentId} not found`);
    }
    
    const comment = comments[index];
    
    // Check if user has permission to delete
    if (comment.userId !== userId) {
      // Get change request
      const changeRequest = await this.getChangeRequestById(comment.changeRequestId);
      
      // If not the comment creator, check if user is change request creator or team owner/admin
      if (changeRequest.createdBy !== userId) {
        if (changeRequest.teamId) {
          try {
            await this.teamService.checkTeamPermission(changeRequest.teamId, userId, ['owner', 'admin']);
          } catch (error) {
            throw new AuthorizationError('You do not have permission to delete this comment');
          }
        } else {
          throw new AuthorizationError('You do not have permission to delete this comment');
        }
      }
    }
    
    // Remove the comment
    comments.splice(index, 1);
    await this.saveData(this.changeRequestCommentsFile, comments);
    
    // Log audit event
    await this.auditService.logEvent({
      userId,
      action: 'DELETE',
      resourceType: 'change_request_comment',
      resourceId: commentId,
      details: {
        changeRequestId: comment.changeRequestId
      }
    });
    
    return { success: true, message: `Comment ${commentId} deleted` };
  }

  /**
   * Delete all comments for a change request
   */
  async deleteChangeRequestComments(changeRequestId) {
    const comments = await this.loadData(this.changeRequestCommentsFile);
    const updatedComments = comments.filter(comment => comment.changeRequestId !== changeRequestId);
    
    if (updatedComments.length !== comments.length) {
      await this.saveData(this.changeRequestCommentsFile, updatedComments);
    }
    
    return { success: true, message: `Comments for change request ${changeRequestId} deleted` };
  }

  /**
   * Get change request approvals
   */
  async getChangeRequestApprovals(changeRequestId) {
    const approvals = await this.loadData(this.changeRequestApprovalsFile);
    return approvals
      .filter(approval => approval.changeRequestId === changeRequestId)
      .sort((a, b) => new Date(a.created) - new Date(b.created));
  }

  /**
   * Approve change request
   */
  async approveChangeRequest(changeRequestId, data, userId) {
    // Get change request
    const changeRequest = await this.getChangeRequestById(changeRequestId);
    
    // Check if change request is pending
    if (changeRequest.status !== 'pending') {
      throw new ValidationError(`Cannot approve a change request with status '${changeRequest.status}'`);
    }
    
    // Check if user has permission to approve
    if (changeRequest.teamId) {
      try {
        await this.teamService.checkTeamPermission(changeRequest.teamId, userId, ['owner', 'admin']);
      } catch (error) {
        throw new AuthorizationError('You do not have permission to approve this change request');
      }
    } else {
      throw new AuthorizationError('You do not have permission to approve this change request');
    }
    
    // Check if user has already approved
    const approvals = await this.getChangeRequestApprovals(changeRequestId);
    const existingApproval = approvals.find(approval => approval.userId === userId);
    
    if (existingApproval) {
      throw new ValidationError('You have already approved this change request');
    }
    
    // Add approval
    const allApprovals = await this.loadData(this.changeRequestApprovalsFile);
    
    const newApproval = {
      id: uuidv4(),
      changeRequestId,
      userId,
      comments: data.comments || '',
      created: new Date().toISOString()
    };
    
    allApprovals.push(newApproval);
    await this.saveData(this.changeRequestApprovalsFile, allApprovals);
    
    // Log audit event
    await this.auditService.logEvent({
      userId,
      action: 'APPROVE',
      resourceType: 'change_request',
      resourceId: changeRequestId,
      details: {
        title: changeRequest.title
      },
      teamId: changeRequest.teamId,
      environmentId: changeRequest.environmentId
    });
    
    // Check if change request has enough approvals
    const updatedApprovals = await this.getChangeRequestApprovals(changeRequestId);
    
    if (updatedApprovals.length >= changeRequest.requiredApprovals) {
      // Update change request status to approved
      await this.updateChangeRequest(changeRequestId, { status: 'approved' }, userId);
      
      // Return updated change request with approval status
      const updatedChangeRequest = await this.getChangeRequestById(changeRequestId);
      return {
        approval: newApproval,
        changeRequest: updatedChangeRequest,
        isFullyApproved: true
      };
    }
    
    return {
      approval: newApproval,
      changeRequest,
      isFullyApproved: false
    };
  }

  /**
   * Reject change request
   */
  async rejectChangeRequest(changeRequestId, data, userId) {
    // Get change request
    const changeRequest = await this.getChangeRequestById(changeRequestId);
    
    // Check if change request is pending
    if (changeRequest.status !== 'pending') {
      throw new ValidationError(`Cannot reject a change request with status '${changeRequest.status}'`);
    }
    
    // Check if user has permission to reject
    if (changeRequest.teamId) {
      try {
        await this.teamService.checkTeamPermission(changeRequest.teamId, userId, ['owner', 'admin']);
      } catch (error) {
        throw new AuthorizationError('You do not have permission to reject this change request');
      }
    } else {
      throw new AuthorizationError('You do not have permission to reject this change request');
    }
    
    // Update change request status to rejected
    const updatedChangeRequest = await this.updateChangeRequest(
      changeRequestId, 
      { 
        status: 'rejected',
        rejectionReason: data.reason || ''
      }, 
      userId
    );
    
    // Add comment with rejection reason if provided
    if (data.reason) {
      await this.addChangeRequestComment(
        changeRequestId,
        { text: `Rejected: ${data.reason}` },
        userId
      );
    }
    
    // Log audit event
    await this.auditService.logEvent({
      userId,
      action: 'REJECT',
      resourceType: 'change_request',
      resourceId: changeRequestId,
      details: {
        title: changeRequest.title,
        reason: data.reason || ''
      },
      teamId: changeRequest.teamId,
      environmentId: changeRequest.environmentId
    });
    
    return updatedChangeRequest;
  }

  /**
   * Mark change request as implemented
   */
  async implementChangeRequest(changeRequestId, data, userId) {
    // Get change request
    const changeRequest = await this.getChangeRequestById(changeRequestId);
    
    // Check if change request is approved
    if (changeRequest.status !== 'approved') {
      throw new ValidationError(`Cannot implement a change request with status '${changeRequest.status}'`);
    }
    
    // Check if user has permission to implement
    if (changeRequest.teamId) {
      try {
        await this.teamService.checkTeamPermission(changeRequest.teamId, userId, ['owner', 'admin']);
      } catch (error) {
        throw new AuthorizationError('You do not have permission to implement this change request');
      }
    } else if (changeRequest.createdBy !== userId) {
      throw new AuthorizationError('You do not have permission to implement this change request');
    }
    
    // Update change request status to implemented
    const updatedChangeRequest = await this.updateChangeRequest(
      changeRequestId, 
      { 
        status: 'implemented',
        implementationDetails: data.details || '',
        implementedAt: new Date().toISOString()
      }, 
      userId
    );
    
    // Add comment with implementation details if provided
    if (data.details) {
      await this.addChangeRequestComment(
        changeRequestId,
        { text: `Implemented: ${data.details}` },
        userId
      );
    }
    
    // Log audit event
    await this.auditService.logEvent({
      userId,
      action: 'IMPLEMENT',
      resourceType: 'change_request',
      resourceId: changeRequestId,
      details: {
        title: changeRequest.title,
        details: data.details || ''
      },
      teamId: changeRequest.teamId,
      environmentId: changeRequest.environmentId
    });
    
    return updatedChangeRequest;
  }

  /**
   * Delete all approvals for a change request
   */
  async deleteChangeRequestApprovals(changeRequestId) {
    const approvals = await this.loadData(this.changeRequestApprovalsFile);
    const updatedApprovals = approvals.filter(approval => approval.changeRequestId !== changeRequestId);
    
    if (updatedApprovals.length !== approvals.length) {
      await this.saveData(this.changeRequestApprovalsFile, updatedApprovals);
    }
    
    return { success: true, message: `Approvals for change request ${changeRequestId} deleted` };
  }
}

module.exports = ChangeRequestService;
