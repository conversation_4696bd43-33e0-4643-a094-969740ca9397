/**
 * API Test Setup
 *
 * This file contains setup code for API integration tests.
 */

const request = require('supertest');
const createMockServer = require('./mockServer');

// Create a mock server for testing
const app = createMockServer();

// Setup before all tests
beforeAll(async () => {
  // Set test environment
  process.env.NODE_ENV = 'test';

  // Add any additional setup here
  console.log('API test setup complete');
});

// Cleanup after all tests
afterAll(async () => {
  // Add any cleanup here
  console.log('API test cleanup complete');
});

// Reset state before each test
beforeEach(async () => {
  // Clear mocks
  jest.clearAllMocks();
});

// Export test utilities
module.exports = {
  request,
  app
};
