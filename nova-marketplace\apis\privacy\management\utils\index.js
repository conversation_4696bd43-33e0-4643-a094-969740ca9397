/**
 * Utilities Index
 *
 * This file exports all utility functions for the Privacy Management API.
 */

const validators = require('./validators');
const idGenerator = require('./idGenerator');
const dateUtils = require('./dateUtils');
const encryptionUtils = require('./encryptionUtils');
const dataUtils = require('./dataUtils');

module.exports = {
  validators,
  idGenerator,
  dateUtils,
  encryptionUtils,
  dataUtils,
  // Export dataUtils functions directly for convenience
  advancedFilter: dataUtils.advancedFilter,
  search: dataUtils.search,
  sort: dataUtils.sort,
  paginate: dataUtils.paginate,
  cursorPaginate: dataUtils.cursorPaginate
};
