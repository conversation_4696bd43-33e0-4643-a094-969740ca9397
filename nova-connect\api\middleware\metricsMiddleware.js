/**
 * Metrics Middleware
 * 
 * This middleware automatically tracks HTTP requests and errors for Prometheus metrics.
 */

const prometheusMetrics = require('../services/PrometheusMetricsService');
const logger = require('../utils/logger');

/**
 * Middleware to track HTTP requests and response times
 */
const metricsMiddleware = (req, res, next) => {
  // Record start time
  const startTime = process.hrtime();
  
  // Add response listener
  res.on('finish', () => {
    // Calculate duration
    const duration = getDurationInSeconds(startTime);
    
    // Get route path (normalize dynamic routes)
    const route = normalizeRoute(req.originalUrl || req.url);
    
    // Record HTTP request
    prometheusMetrics.recordHttpRequest(
      req.method,
      route,
      res.statusCode,
      duration
    );
    
    // Record API error if status code is 4xx or 5xx
    if (res.statusCode >= 400) {
      const errorType = res.statusCode >= 500 ? 'server_error' : 'client_error';
      prometheusMetrics.recordApiError(req.method, route, errorType);
    }
    
    // Update active connections
    updateActiveConnections();
  });
  
  next();
};

/**
 * Calculate duration in seconds from hrtime
 * @param {Array} startTime - Start time from process.hrtime()
 * @returns {number} - Duration in seconds
 */
const getDurationInSeconds = (startTime) => {
  const diff = process.hrtime(startTime);
  return diff[0] + diff[1] / 1e9;
};

/**
 * Normalize route path to avoid high cardinality in metrics
 * @param {string} url - Original URL
 * @returns {string} - Normalized route path
 */
const normalizeRoute = (url) => {
  // Remove query parameters
  let route = url.split('?')[0];
  
  // Replace numeric IDs with :id
  route = route.replace(/\/[0-9a-f]{24}(?=\/|$)/g, '/:id');
  route = route.replace(/\/\d+(?=\/|$)/g, '/:id');
  
  // Replace UUIDs with :uuid
  route = route.replace(/\/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}(?=\/|$)/g, '/:uuid');
  
  return route;
};

/**
 * Update active connections metric
 */
const updateActiveConnections = () => {
  // This is a simple approximation - in a real implementation,
  // you would use the server's connection tracking
  const activeConnections = process.listeners('request').length;
  prometheusMetrics.updateActiveConnections(activeConnections);
};

/**
 * Error tracking middleware
 */
const errorMetricsMiddleware = (err, req, res, next) => {
  // Get route path (normalize dynamic routes)
  const route = normalizeRoute(req.originalUrl || req.url);
  
  // Determine error type
  let errorType = 'unknown_error';
  if (err.name) {
    errorType = err.name.toLowerCase().replace('error', '');
  }
  
  // Record API error
  prometheusMetrics.recordApiError(req.method, route, errorType);
  
  // Continue to next error handler
  next(err);
};

/**
 * Metrics endpoint handler
 */
const metricsEndpoint = async (req, res) => {
  try {
    res.set('Content-Type', prometheusMetrics.getContentType());
    res.end(await prometheusMetrics.getMetrics());
  } catch (error) {
    logger.error('Error generating metrics', { error });
    res.status(500).send('Error generating metrics');
  }
};

module.exports = {
  metricsMiddleware,
  errorMetricsMiddleware,
  metricsEndpoint
};
