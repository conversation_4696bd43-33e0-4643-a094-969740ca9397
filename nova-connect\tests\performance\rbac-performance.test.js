/**
 * RBAC Performance Tests
 * 
 * This file contains performance tests for the RBAC system, focusing on caching.
 */

const mongoose = require('mongoose');
const RBACService = require('../../api/services/RBACService');
const CacheService = require('../../api/services/CacheService');
const { setupTestEnvironment, clearDatabase, disconnectFromDatabase, getTestData } = require('../setup/rbac-test-setup');

// Initialize services
const rbacService = new RBACService();

// Test data
let adminUser;
let regularUser;
let viewerUser;
let testRole;
let testPermission;

// Setup and teardown
beforeAll(async () => {
  try {
    const testData = await setupTestEnvironment();
    adminUser = testData.adminUser;
    regularUser = testData.regularUser;
    viewerUser = testData.viewerUser;
    testRole = testData.testRole;
    testPermission = testData.testPermission;
  } catch (error) {
    console.error('Error in beforeAll:', error);
    throw error;
  }
});

afterAll(async () => {
  await disconnectFromDatabase();
});

// Helper function to measure execution time
const measureExecutionTime = async (fn) => {
  const start = process.hrtime.bigint();
  const result = await fn();
  const end = process.hrtime.bigint();
  const duration = Number(end - start) / 1_000_000; // Convert to milliseconds
  return { result, duration };
};

// Test suites
describe('RBAC Performance', () => {
  describe('Permission Caching', () => {
    test('hasPermission should be faster with caching', async () => {
      // Clear cache before test
      await CacheService.flush('rbac');
      
      // First call (no cache)
      const { duration: firstDuration } = await measureExecutionTime(() => 
        rbacService.hasPermission(regularUser._id, 'resource:view')
      );
      
      // Second call (with cache)
      const { duration: secondDuration } = await measureExecutionTime(() => 
        rbacService.hasPermission(regularUser._id, 'resource:view')
      );
      
      console.log(`First call (no cache): ${firstDuration.toFixed(2)}ms`);
      console.log(`Second call (with cache): ${secondDuration.toFixed(2)}ms`);
      
      // The second call should be significantly faster
      expect(secondDuration).toBeLessThan(firstDuration * 0.5);
    });
    
    test('getUserPermissions should be faster with caching', async () => {
      // Clear cache before test
      await CacheService.flush('rbac');
      
      // First call (no cache)
      const { duration: firstDuration } = await measureExecutionTime(() => 
        rbacService.getUserPermissions(regularUser._id)
      );
      
      // Second call (with cache)
      const { duration: secondDuration } = await measureExecutionTime(() => 
        rbacService.getUserPermissions(regularUser._id)
      );
      
      console.log(`First call (no cache): ${firstDuration.toFixed(2)}ms`);
      console.log(`Second call (with cache): ${secondDuration.toFixed(2)}ms`);
      
      // The second call should be significantly faster
      expect(secondDuration).toBeLessThan(firstDuration * 0.5);
    });
    
    test('getAllRoles should be faster with caching', async () => {
      // Clear cache before test
      await CacheService.flush('rbac');
      
      // First call (no cache)
      const { duration: firstDuration } = await measureExecutionTime(() => 
        rbacService.getAllRoles()
      );
      
      // Second call (with cache)
      const { duration: secondDuration } = await measureExecutionTime(() => 
        rbacService.getAllRoles()
      );
      
      console.log(`First call (no cache): ${firstDuration.toFixed(2)}ms`);
      console.log(`Second call (with cache): ${secondDuration.toFixed(2)}ms`);
      
      // The second call should be significantly faster
      expect(secondDuration).toBeLessThan(firstDuration * 0.5);
    });
    
    test('getAllPermissions should be faster with caching', async () => {
      // Clear cache before test
      await CacheService.flush('rbac');
      
      // First call (no cache)
      const { duration: firstDuration } = await measureExecutionTime(() => 
        rbacService.getAllPermissions()
      );
      
      // Second call (with cache)
      const { duration: secondDuration } = await measureExecutionTime(() => 
        rbacService.getAllPermissions()
      );
      
      console.log(`First call (no cache): ${firstDuration.toFixed(2)}ms`);
      console.log(`Second call (with cache): ${secondDuration.toFixed(2)}ms`);
      
      // The second call should be significantly faster
      expect(secondDuration).toBeLessThan(firstDuration * 0.5);
    });
  });
  
  describe('Cache Invalidation', () => {
    test('createRole should invalidate role cache', async () => {
      // First, populate the cache
      await rbacService.getAllRoles();
      
      // Create a new role
      const newRole = {
        name: 'Cache Test Role',
        description: 'Role for testing cache invalidation',
        permissions: [testPermission._id]
      };
      
      await rbacService.createRole(newRole);
      
      // Get roles again and verify the new role is included
      const roles = await rbacService.getAllRoles();
      const hasNewRole = roles.some(role => role.name === newRole.name);
      
      expect(hasNewRole).toBe(true);
      
      // Clean up
      const createdRole = roles.find(role => role.name === newRole.name);
      if (createdRole) {
        await rbacService.deleteRole(createdRole._id);
      }
    });
    
    test('assignRoleToUser should invalidate user permission cache', async () => {
      // First, populate the cache
      await rbacService.getUserPermissions(viewerUser._id);
      
      // Get initial permissions count
      const initialPermissions = await rbacService.getUserPermissions(viewerUser._id);
      const initialCount = initialPermissions.length;
      
      // Assign a role with more permissions
      await rbacService.assignRoleToUser(viewerUser._id, testRole._id);
      
      // Get permissions again and verify they've changed
      const updatedPermissions = await rbacService.getUserPermissions(viewerUser._id);
      const updatedCount = updatedPermissions.length;
      
      expect(updatedCount).toBeGreaterThan(initialCount);
      
      // Clean up
      await rbacService.removeRoleFromUser(viewerUser._id, testRole._id);
    });
  });
  
  describe('Performance Under Load', () => {
    test('hasPermission should handle multiple concurrent requests', async () => {
      // Clear cache before test
      await CacheService.flush('rbac');
      
      // Create an array of promises for concurrent permission checks
      const concurrentChecks = 100;
      const promises = [];
      
      for (let i = 0; i < concurrentChecks; i++) {
        promises.push(rbacService.hasPermission(regularUser._id, 'resource:view'));
      }
      
      // Measure execution time for all concurrent checks
      const { duration } = await measureExecutionTime(() => Promise.all(promises));
      
      console.log(`${concurrentChecks} concurrent permission checks: ${duration.toFixed(2)}ms`);
      console.log(`Average time per check: ${(duration / concurrentChecks).toFixed(2)}ms`);
      
      // The average time should be reasonable
      expect(duration / concurrentChecks).toBeLessThan(10); // Less than 10ms per check on average
    });
  });
});
