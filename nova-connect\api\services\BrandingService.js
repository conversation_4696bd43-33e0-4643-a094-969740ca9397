/**
 * Branding Service
 * 
 * This service handles organization branding settings.
 */

const fs = require('fs').promises;
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const { ValidationError, NotFoundError, AuthorizationError } = require('../utils/errors');
const AuditService = require('./AuditService');
const ThemeService = require('./ThemeService');

class BrandingService {
  constructor(dataDir = path.join(__dirname, '../data')) {
    this.dataDir = dataDir;
    this.brandingDir = path.join(this.dataDir, 'branding');
    this.brandingFile = path.join(this.brandingDir, 'organization_branding.json');
    this.assetsDir = path.join(this.brandingDir, 'assets');
    this.auditService = new AuditService(dataDir);
    this.themeService = new ThemeService(dataDir);
    
    // Define default branding
    this.defaultBranding = {
      name: 'NovaConnect',
      logoUrl: '/assets/logo.png',
      faviconUrl: '/assets/favicon.ico',
      loginBackgroundUrl: '/assets/login-background.jpg',
      footerText: '© NovaConnect. All rights reserved.',
      showPoweredBy: true,
      customCss: '',
      customJs: ''
    };
    
    this.ensureDataDir();
  }

  /**
   * Ensure the data directory exists
   */
  async ensureDataDir() {
    try {
      await fs.mkdir(this.brandingDir, { recursive: true });
      await fs.mkdir(this.assetsDir, { recursive: true });
      
      // Initialize files if they don't exist
      await this.initializeFile(this.brandingFile, []);
    } catch (error) {
      console.error('Error creating branding directory:', error);
      throw error;
    }
  }

  /**
   * Initialize a file with default data if it doesn't exist
   */
  async initializeFile(filePath, defaultData) {
    try {
      await fs.access(filePath);
    } catch (error) {
      if (error.code === 'ENOENT') {
        // File doesn't exist, create it with default data
        await fs.writeFile(filePath, JSON.stringify(defaultData, null, 2));
      } else {
        throw error;
      }
    }
  }

  /**
   * Load data from file
   */
  async loadData(filePath) {
    try {
      const data = await fs.readFile(filePath, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      if (error.code === 'ENOENT') {
        // File doesn't exist, return empty array
        return [];
      }
      console.error(`Error loading data from ${filePath}:`, error);
      throw error;
    }
  }

  /**
   * Save data to file
   */
  async saveData(filePath, data) {
    try {
      await fs.writeFile(filePath, JSON.stringify(data, null, 2));
    } catch (error) {
      console.error(`Error saving data to ${filePath}:`, error);
      throw error;
    }
  }

  /**
   * Get organization branding
   */
  async getOrganizationBranding(organizationId) {
    const brandingList = await this.loadData(this.brandingFile);
    const branding = brandingList.find(b => b.organizationId === organizationId);
    
    if (!branding) {
      // Return default branding if no branding is set for organization
      return { ...this.defaultBranding };
    }
    
    // Merge with default branding to ensure all properties are present
    return { ...this.defaultBranding, ...branding };
  }

  /**
   * Update organization branding
   */
  async updateOrganizationBranding(organizationId, data, userId) {
    const brandingList = await this.loadData(this.brandingFile);
    const index = brandingList.findIndex(b => b.organizationId === organizationId);
    
    // Validate data
    if (data.customCss && data.customCss.length > 50000) {
      throw new ValidationError('Custom CSS is too large (max 50KB)');
    }
    
    if (data.customJs && data.customJs.length > 50000) {
      throw new ValidationError('Custom JavaScript is too large (max 50KB)');
    }
    
    let branding;
    
    if (index === -1) {
      // Create new branding
      branding = {
        organizationId,
        ...data,
        updatedBy: userId,
        updated: new Date().toISOString()
      };
      
      brandingList.push(branding);
    } else {
      // Update existing branding
      branding = {
        ...brandingList[index],
        ...data,
        organizationId, // Don't allow changing the organization ID
        updatedBy: userId,
        updated: new Date().toISOString()
      };
      
      brandingList[index] = branding;
    }
    
    await this.saveData(this.brandingFile, brandingList);
    
    // Log audit event
    await this.auditService.logEvent({
      userId,
      action: 'UPDATE',
      resourceType: 'branding',
      resourceId: organizationId,
      details: {
        organizationId
      }
    });
    
    return branding;
  }

  /**
   * Reset organization branding to default
   */
  async resetOrganizationBranding(organizationId, userId) {
    const brandingList = await this.loadData(this.brandingFile);
    const index = brandingList.findIndex(b => b.organizationId === organizationId);
    
    if (index === -1) {
      // Organization already using default branding
      return { success: true, message: `Organization ${organizationId} already using default branding` };
    }
    
    // Remove organization branding
    brandingList.splice(index, 1);
    await this.saveData(this.brandingFile, brandingList);
    
    // Log audit event
    await this.auditService.logEvent({
      userId,
      action: 'RESET',
      resourceType: 'branding',
      resourceId: organizationId
    });
    
    return { success: true, message: `Branding reset for organization ${organizationId}` };
  }

  /**
   * Upload branding asset
   */
  async uploadBrandingAsset(organizationId, assetType, fileBuffer, fileName, userId) {
    // Validate asset type
    const validAssetTypes = ['logo', 'favicon', 'loginBackground', 'custom'];
    if (!validAssetTypes.includes(assetType)) {
      throw new ValidationError(`Invalid asset type: ${assetType}`);
    }
    
    // Validate file size
    const maxSizes = {
      logo: 1024 * 1024, // 1MB
      favicon: 256 * 1024, // 256KB
      loginBackground: 5 * 1024 * 1024, // 5MB
      custom: 2 * 1024 * 1024 // 2MB
    };
    
    if (fileBuffer.length > maxSizes[assetType]) {
      throw new ValidationError(`File too large (max ${maxSizes[assetType] / 1024 / 1024}MB)`);
    }
    
    // Validate file type
    const fileExt = path.extname(fileName).toLowerCase();
    const validExtensions = {
      logo: ['.png', '.jpg', '.jpeg', '.svg'],
      favicon: ['.ico', '.png'],
      loginBackground: ['.jpg', '.jpeg', '.png'],
      custom: ['.jpg', '.jpeg', '.png', '.svg', '.gif', '.pdf']
    };
    
    if (!validExtensions[assetType].includes(fileExt)) {
      throw new ValidationError(`Invalid file type for ${assetType}. Allowed: ${validExtensions[assetType].join(', ')}`);
    }
    
    // Generate unique filename
    const uniqueFileName = `${organizationId}_${assetType}_${uuidv4()}${fileExt}`;
    const filePath = path.join(this.assetsDir, uniqueFileName);
    
    // Save file
    await fs.writeFile(filePath, fileBuffer);
    
    // Update branding with new asset URL
    const assetUrl = `/api/branding/assets/${uniqueFileName}`;
    
    const updateData = {};
    if (assetType === 'logo') {
      updateData.logoUrl = assetUrl;
    } else if (assetType === 'favicon') {
      updateData.faviconUrl = assetUrl;
    } else if (assetType === 'loginBackground') {
      updateData.loginBackgroundUrl = assetUrl;
    }
    
    // Only update branding if it's a standard asset type
    if (Object.keys(updateData).length > 0) {
      await this.updateOrganizationBranding(organizationId, updateData, userId);
    }
    
    // Log audit event
    await this.auditService.logEvent({
      userId,
      action: 'UPLOAD',
      resourceType: 'branding_asset',
      resourceId: uniqueFileName,
      details: {
        organizationId,
        assetType,
        fileName
      }
    });
    
    return {
      fileName: uniqueFileName,
      url: assetUrl,
      size: fileBuffer.length,
      type: assetType
    };
  }

  /**
   * Get branding asset
   */
  async getBrandingAsset(fileName) {
    const filePath = path.join(this.assetsDir, fileName);
    
    try {
      // Check if file exists
      await fs.access(filePath);
      
      // Read file
      const fileBuffer = await fs.readFile(filePath);
      
      // Determine content type
      const fileExt = path.extname(fileName).toLowerCase();
      const contentTypes = {
        '.png': 'image/png',
        '.jpg': 'image/jpeg',
        '.jpeg': 'image/jpeg',
        '.gif': 'image/gif',
        '.svg': 'image/svg+xml',
        '.ico': 'image/x-icon',
        '.pdf': 'application/pdf'
      };
      
      const contentType = contentTypes[fileExt] || 'application/octet-stream';
      
      return {
        buffer: fileBuffer,
        contentType
      };
    } catch (error) {
      if (error.code === 'ENOENT') {
        throw new NotFoundError(`Asset ${fileName} not found`);
      }
      throw error;
    }
  }

  /**
   * Delete branding asset
   */
  async deleteBrandingAsset(organizationId, fileName, userId) {
    const filePath = path.join(this.assetsDir, fileName);
    
    // Verify that the file belongs to the organization
    if (!fileName.startsWith(`${organizationId}_`)) {
      throw new AuthorizationError('Asset does not belong to this organization');
    }
    
    try {
      // Check if file exists
      await fs.access(filePath);
      
      // Delete file
      await fs.unlink(filePath);
      
      // Log audit event
      await this.auditService.logEvent({
        userId,
        action: 'DELETE',
        resourceType: 'branding_asset',
        resourceId: fileName,
        details: {
          organizationId
        }
      });
      
      return { success: true, message: `Asset ${fileName} deleted` };
    } catch (error) {
      if (error.code === 'ENOENT') {
        throw new NotFoundError(`Asset ${fileName} not found`);
      }
      throw error;
    }
  }

  /**
   * Get complete branding package
   */
  async getBrandingPackage(organizationId) {
    // Get organization branding
    const branding = await this.getOrganizationBranding(organizationId);
    
    // Get organization theme
    const theme = await this.themeService.getOrganizationTheme(organizationId);
    
    // Generate theme CSS
    const themeCss = this.themeService.getCssVariables(theme);
    
    return {
      branding,
      theme,
      themeCss
    };
  }
}

module.exports = BrandingService;
