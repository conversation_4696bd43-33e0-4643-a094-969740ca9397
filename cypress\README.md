# NovaFuse End-to-End Tests

This directory contains end-to-end tests for the NovaFuse API Superstore using Cypress.

## Test Structure

- `e2e/` - Contains all the end-to-end test files
- `fixtures/` - Contains test data used by the tests
- `support/` - Contains custom commands and global configuration

## Running Tests

To run the tests in headless mode:

```bash
npm run test:e2e
```

To open the Cypress Test Runner:

```bash
npm run cypress:open
```

## Test Files

- `homepage.cy.js` - Tests for the homepage
- `products.cy.js` - Tests for the products page
- `partner-empowerment.cy.js` - Tests for the partner empowerment page
- `nova-concierge.cy.js` - Tests for the NovaConcierge chat functionality
- `authentication.cy.js` - Tests for the authentication flow

## Custom Commands

Custom commands are defined in `support/commands.js`:

- `login(email, password)` - Logs in a user
- `navigateToProduct(productName)` - Navigates to a specific product page
- `isInViewport()` - Checks if an element is in the viewport
- `testNovaConcierge(message)` - Tests the NovaConcierge chat with a specific message
- `testImplementationCalculator(framework, controls)` - Tests the implementation calculator

## Test Data

Test data is stored in the `fixtures/` directory:

- `users.json` - Contains test user data
- `products.json` - Contains product information

## Best Practices

1. Use data-testid attributes for selecting elements
2. Use custom commands for common operations
3. Keep tests independent of each other
4. Use fixtures for test data
5. Use before/beforeEach hooks for setup
6. Use after/afterEach hooks for cleanup
