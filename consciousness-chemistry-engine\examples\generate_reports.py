"""
Example Report Generator for ConsciousNovaFold

This script generates comprehensive example reports showing the capabilities of the
ConsciousNovaFold pipeline, including structure comparisons, consciousness metrics,
and visualizations.
"""

import os
import json
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from datetime import datetime

# Add parent directory to path to import our modules
import sys
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.ConsciousNovaFold import ConsciousNovaFold, NovaFoldClient
from src.evolutionary_analysis import EvolutionaryAnalyzer
from src.domain_analysis import DomainAnalyzer
from src.visualization import ProteinVisualizer

# Set up output directories
REPORT_DIR = Path("reports")
REPORT_DIR.mkdir(exist_ok=True)

# Example sequence (human hemoglobin beta chain)
EXAMPLE_SEQUENCE = "MVHLTPEEKSAVTALWGKVNVDEVGGEALGRLLVVYPWTQRFFESFGDLSTPDAVMGNPKVKAHGKKVLGAFSDGLAHLDNLKGTFATLSELHCDKLHVDPENFRLLGNVLVCVLAHHFGKEFTPPVQAAYQKVVAGVANALAHKYH"

class ReportGenerator:
    """Generates example reports for ConsciousNovaFold."""
    
    def __init__(self):
        """Initialize the report generator with required components."""
        print("Initializing ConsciousNovaFold pipeline components...")
        self.novafold = NovaFoldClient()
        self.cn = ConsciousNovaFold(self.novafold)
        self.evo_analyzer = EvolutionaryAnalyzer()
        self.domain_analyzer = DomainAnalyzer()
        self.visualizer = ProteinVisualizer()
        
        # Set up output paths
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.report_dir = REPORT_DIR / f"report_{self.timestamp}"
        self.report_dir.mkdir(exist_ok=True)
        
        # Store results
        self.results = {}
    
    def run_pipeline(self, sequence):
        """Run the full ConsciousNovaFold pipeline."""
        print("\n=== Running ConsciousNovaFold Pipeline ===")
        print(f"Processing sequence (length: {len(sequence)})...")
        
        # 1. Run ConsciousNovaFold
        print("1/4 Running ConsciousNovaFold...")
        self.results['fold'] = self.cn.fold(sequence)
        
        # 2. Run evolutionary analysis
        print("2/4 Running evolutionary analysis...")
        self.results['evolutionary'] = self.evo_analyzer.predict_functional_sites(sequence)
        
        # 3. Run domain analysis
        print("3/4 Running domain analysis...")
        self.results['domain'] = self.domain_analyzer.analyze_domain_fibonacci(sequence)
        
        # 4. Generate visualizations
        print("4/4 Generating visualizations...")
        self._generate_visualizations(sequence)
        
        print("\n✓ Pipeline completed successfully!")
        return self.results
    
    def _generate_visualizations(self, sequence):
        """Generate visualizations for the report."""
        # Create plots directory
        plots_dir = self.report_dir / "plots"
        plots_dir.mkdir(exist_ok=True)
        
        # 1. Conservation plot
        conservation = list(self.results['evolutionary'].values())
        plt.figure(figsize=(12, 4))
        plt.plot(conservation)
        plt.title('Evolutionary Conservation')
        plt.xlabel('Residue Position')
        plt.ylabel('Conservation Score')
        plt.savefig(plots_dir / 'conservation.png')
        plt.close()
        
        # 2. Domain alignment visualization
        domains = self.results['domain'].get('domains', [])
        if domains:
            plt.figure(figsize=(12, 2))
            for i, domain in enumerate(domains):
                plt.hlines(i, domain['start'], domain['end'], 
                          linewidth=10, label=f"Domain {i+1}")
            plt.yticks(range(len(domains)), [f"Domain {i+1}" for i in range(len(domains))])
            plt.title('Protein Domain Architecture')
            plt.xlabel('Residue Position')
            plt.tight_layout()
            plt.savefig(plots_dir / 'domains.png')
            plt.close()
    
    def generate_markdown_report(self):
        """Generate a markdown report with results and visualizations."""
        print("\nGenerating markdown report...")
        
        report = [
            "# ConsciousNovaFold Analysis Report",
            f"**Generated on**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n",
            "## 1. Pipeline Overview",
            "### Input Sequence",
            f"- Length: {len(EXAMPLE_SEQUENCE)} residues",
            f"- Sequence: `{EXAMPLE_SEQUENCE[:10]}...{EXAMPLE_SEQUENCE[-10:]}`\n",
            "## 2. Consciousness Metrics",
            "### Trinity Validation"
        ]
        
        # Add Trinity validation results
        trinity = self.results['fold']['consciousness_metrics']['trinity_validation']
        for key, val in trinity.items():
            if key != 'passed':
                status = "✅ PASS" if val.get('passed', False) else "❌ FAIL"
                report.append(f"- **{key}**: {val.get('score', 0):.3f} {status}")
        
        # Add Fibonacci alignment info
        fib = self.results['fold']['consciousness_metrics']['fibonacci_alignment']
        report.extend([
            f"\n### Fibonacci Alignment",
            f"- Closest Fibonacci number: {fib.get('closest_fibonacci', 'N/A')}",
            f"- Alignment score: {fib.get('alignment_score', 0):.3f}",
            f"- Difference: {fib.get('difference', 'N/A')}"
        ])
        
        # Add visualizations
        report.extend([
            "\n## 3. Visualizations",
            "### Evolutionary Conservation",
            "![Conservation Plot](plots/conservation.png)\n",
            "### Domain Architecture",
            "![Domain Plot](plots/domains.png)"
        ])
        
        # Save the report
        report_path = self.report_dir / "report.md"
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report))
            
        print(f"✓ Report generated at: {report_path}")
        return str(report_path)

def main():
    """Main function to generate the example report."""
    print("=== ConsciousNovaFold Example Report Generator ===\n")
    
    # Initialize and run the pipeline
    generator = ReportGenerator()
    results = generator.run_pipeline(EXAMPLE_SEQUENCE)
    
    # Generate the report
    report_path = generator.generate_markdown_report()
    
    print("\n=== Report Generation Complete ===")
    print(f"Report saved to: {report_path}")
    print("\nTo view the report, open the generated markdown file in a viewer")
    print("that supports embedded images, or convert it to PDF/HTML.")

if __name__ == "__main__":
    main()
