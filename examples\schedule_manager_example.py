"""
Example of using the Schedule Manager.

This example demonstrates how to use the Schedule Manager to schedule tasks
and execute them when they are due.
"""

import os
import sys
import json
import logging
import datetime
import time

# Add the parent directory to the path so we can import the UCECS modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.ucecs.core.schedule_manager import ScheduleManager, ScheduleInterval, TaskType, TaskStatus

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def execute_collect_evidence_task(task):
    """
    Execute a collect evidence task.
    
    Args:
        task: The task to execute
        
    Returns:
        The result of the task execution
    """
    logger.info(f"Executing collect evidence task: {task['name']}")
    
    # Simulate collecting evidence
    time.sleep(1)
    
    return {
        'evidence_id': f"evidence_{int(time.time())}",
        'collector_id': task['parameters'].get('collector_id'),
        'status': 'collected'
    }

def execute_validate_evidence_task(task):
    """
    Execute a validate evidence task.
    
    Args:
        task: The task to execute
        
    Returns:
        The result of the task execution
    """
    logger.info(f"Executing validate evidence task: {task['name']}")
    
    # Simulate validating evidence
    time.sleep(1)
    
    return {
        'evidence_id': task['parameters'].get('evidence_id'),
        'validator_id': task['parameters'].get('validator_id'),
        'is_valid': True
    }

def execute_generate_report_task(task):
    """
    Execute a generate report task.
    
    Args:
        task: The task to execute
        
    Returns:
        The result of the task execution
    """
    logger.info(f"Executing generate report task: {task['name']}")
    
    # Simulate generating a report
    time.sleep(1)
    
    return {
        'report_id': f"report_{int(time.time())}",
        'format': task['parameters'].get('format', 'json'),
        'status': 'generated'
    }

def execute_custom_task(task):
    """
    Execute a custom task.
    
    Args:
        task: The task to execute
        
    Returns:
        The result of the task execution
    """
    logger.info(f"Executing custom task: {task['name']}")
    
    # Simulate executing a custom task
    time.sleep(1)
    
    return {
        'custom_result': f"result_{int(time.time())}",
        'status': 'completed'
    }

def execute_task(task):
    """
    Execute a task based on its type.
    
    Args:
        task: The task to execute
        
    Returns:
        The result of the task execution
    """
    task_type = task.get('type')
    
    if task_type == TaskType.COLLECT_EVIDENCE.value:
        return execute_collect_evidence_task(task)
    elif task_type == TaskType.VALIDATE_EVIDENCE.value:
        return execute_validate_evidence_task(task)
    elif task_type == TaskType.GENERATE_REPORT.value:
        return execute_generate_report_task(task)
    elif task_type == TaskType.CUSTOM.value:
        return execute_custom_task(task)
    else:
        raise ValueError(f"Unknown task type: {task_type}")

def main():
    """Run the Schedule Manager example."""
    # Create a temporary directory for the example
    temp_dir = os.path.join(os.getcwd(), 'temp_schedule_example')
    os.makedirs(temp_dir, exist_ok=True)
    
    # Create a schedules directory
    schedules_dir = os.path.join(temp_dir, 'schedules')
    
    # Create a Schedule Manager
    schedule_manager = ScheduleManager(schedules_dir=schedules_dir)
    
    try:
        # Create a scheduled task to collect evidence
        logger.info("Creating a scheduled task to collect evidence...")
        collect_task_id = schedule_manager.create_scheduled_task(
            name="Collect System Configuration",
            task_type=TaskType.COLLECT_EVIDENCE,
            interval=ScheduleInterval.DAILY,
            parameters={
                'collector_id': 'system_config',
                'parameters': {
                    'include_network': True,
                    'include_users': True
                }
            }
        )
        logger.info(f"Created collect evidence task: {collect_task_id}")
        
        # Create a scheduled task to validate evidence
        logger.info("Creating a scheduled task to validate evidence...")
        validate_task_id = schedule_manager.create_scheduled_task(
            name="Validate System Configuration",
            task_type=TaskType.VALIDATE_EVIDENCE,
            interval=ScheduleInterval.WEEKLY,
            parameters={
                'evidence_id': 'evidence_123',
                'validator_id': 'system_config_validator'
            }
        )
        logger.info(f"Created validate evidence task: {validate_task_id}")
        
        # Create a scheduled task to generate a report
        logger.info("Creating a scheduled task to generate a report...")
        report_task_id = schedule_manager.create_scheduled_task(
            name="Generate Compliance Report",
            task_type=TaskType.GENERATE_REPORT,
            interval=ScheduleInterval.MONTHLY,
            parameters={
                'format': 'html',
                'include_evidence': True,
                'include_validation': True
            }
        )
        logger.info(f"Created generate report task: {report_task_id}")
        
        # Create a custom scheduled task
        logger.info("Creating a custom scheduled task...")
        custom_task_id = schedule_manager.create_scheduled_task(
            name="Custom Task",
            task_type=TaskType.CUSTOM,
            interval=ScheduleInterval.HOURLY,
            parameters={
                'custom_parameter': 'custom_value'
            }
        )
        logger.info(f"Created custom task: {custom_task_id}")
        
        # Update a scheduled task
        logger.info("Updating a scheduled task...")
        updated_task = schedule_manager.update_scheduled_task(
            task_id=collect_task_id,
            name="Collect System Configuration (Updated)",
            interval=ScheduleInterval.WEEKLY,
            parameters={
                'collector_id': 'system_config',
                'parameters': {
                    'include_network': True,
                    'include_users': True,
                    'include_services': True
                }
            }
        )
        logger.info(f"Updated task: {json.dumps(updated_task, indent=2)}")
        
        # Get all scheduled tasks
        logger.info("Getting all scheduled tasks...")
        all_tasks = schedule_manager.get_all_scheduled_tasks()
        logger.info(f"All tasks: {json.dumps(all_tasks, indent=2)}")
        
        # Modify the next_run time of a task to make it due
        logger.info("Modifying a task to make it due...")
        collect_task = schedule_manager.get_scheduled_task(collect_task_id)
        collect_task['next_run'] = datetime.datetime.now(datetime.timezone.utc).isoformat()
        schedule_manager._save_scheduled_task(collect_task_id)
        
        # Get due tasks
        logger.info("Getting due tasks...")
        due_tasks = schedule_manager.get_due_tasks()
        logger.info(f"Due tasks: {json.dumps(due_tasks, indent=2)}")
        
        # Execute a due task
        if due_tasks:
            logger.info("Executing a due task...")
            task = due_tasks[0]
            execution = schedule_manager.execute_task(task['id'], execute_task)
            logger.info(f"Task execution: {json.dumps(execution, indent=2)}")
            
            # Get task history
            logger.info("Getting task history...")
            task_history = schedule_manager.get_task_history(task['id'])
            logger.info(f"Task history: {json.dumps(task_history, indent=2)}")
        
        # Delete a scheduled task
        logger.info("Deleting a scheduled task...")
        schedule_manager.delete_scheduled_task(custom_task_id)
        
        # Verify the task was deleted
        logger.info("Getting all scheduled tasks after deletion...")
        remaining_tasks = schedule_manager.get_all_scheduled_tasks()
        logger.info(f"Remaining tasks: {json.dumps(remaining_tasks, indent=2)}")
        
    except Exception as e:
        logger.error(f"Error: {e}")
    
    finally:
        # Clean up the temporary directory
        # Uncomment the following line to delete the temporary directory
        # import shutil; shutil.rmtree(temp_dir)
        pass

if __name__ == "__main__":
    main()
