#!/bin/bash
# Script to test the marketplace deployment of NovaConnect UAC

# Set variables
PROJECT_ID=${1:-"novafuse-test"}
IMAGE_NAME=${2:-"novafuse-uac"}
IMAGE_TAG=${3:-"1.0.0"}
FULL_IMAGE_NAME="gcr.io/$PROJECT_ID/$IMAGE_NAME:$IMAGE_TAG"
NAMESPACE=${4:-"marketplace-test"}
TIER=${5:-"core"}

# Install mpdev
echo "Installing mpdev..."
if ! command -v mpdev &> /dev/null; then
  echo "mpdev not found, installing..."
  curl -LO https://github.com/GoogleCloudPlatform/marketplace-tools/releases/download/v0.3.5/mpdev_linux_amd64
  chmod +x mpdev_linux_amd64
  sudo mv mpdev_linux_amd64 /usr/local/bin/mpdev
fi

# Create namespace
echo "Creating namespace $NAMESPACE..."
kubectl create namespace $NAMESPACE || true

# Deploy from marketplace
echo "Deploying from marketplace..."
mpdev install \
  --deployer=$FULL_IMAGE_NAME/deployer:$IMAGE_TAG \
  --parameters='{
    "name": "novafuse-uac-test",
    "namespace": "'$NAMESPACE'",
    "tier": "'$TIER'",
    "mongodb.uri": "mongodb://mongodb:27017/novafuse-test",
    "redis.uri": "redis://redis:6379"
  }'

# Wait for deployment to be ready
echo "Waiting for deployment to be ready..."
kubectl rollout status deployment/novafuse-uac-test -n $NAMESPACE

# Get the service URL
echo "Getting service URL..."
SERVICE_IP=$(kubectl get service novafuse-uac-test -n $NAMESPACE -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
if [ -z "$SERVICE_IP" ]; then
  echo "Service is not exposed externally. Creating a port-forward..."
  kubectl port-forward service/novafuse-uac-test -n $NAMESPACE 8080:80 &
  SERVICE_URL="http://localhost:8080"
  echo "Service available at $SERVICE_URL"
else
  SERVICE_URL="http://$SERVICE_IP"
  echo "Service available at $SERVICE_URL"
fi

# Test the health endpoint
echo "Testing health endpoint..."
curl -f $SERVICE_URL/health || { echo "Health check failed"; exit 1; }

# Test the metrics endpoint
echo "Testing metrics endpoint..."
curl -f $SERVICE_URL/metrics || { echo "Metrics check failed"; exit 1; }

# Test the API endpoints
echo "Testing API endpoints..."
curl -f -H "Authorization: Bearer test-api-key-12345" $SERVICE_URL/api/v1/status || { echo "API check failed"; exit 1; }

# Clean up
echo "Cleaning up..."
kubectl delete namespace $NAMESPACE

echo "Marketplace deployment test complete!"
