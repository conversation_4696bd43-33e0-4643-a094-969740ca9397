/**
 * NovaVision Security Example
 * 
 * This example demonstrates how to use the security features of NovaVision,
 * including RBAC and NIST security requirements.
 */

const { NovaVision } = require('../index');
const { ROLES, PERMISSIONS } = require('../security/rbac');

// Create a NovaVision instance with security enabled
const novaVision = new NovaVision({
  theme: 'cyber-safety',
  enableSecurity: true,
  enableNIST: true,
  enableRBAC: true,
  rbacOptions: {
    strictMode: true
  },
  nistOptions: {
    enableCSP: true,
    enableXSS: true,
    enableSRI: true,
    enableHSTS: true,
    enableSecureCookies: true,
    enableInputValidation: true,
    enableOutputEncoding: true,
    enableAuditLogging: true,
    enableAccessControl: true,
    enableSecureDefaults: true,
    nistFrameworks: ['SP800-53', 'CSF', 'SP800-171']
  }
});

// Example user IDs
const users = {
  ciso: 'user-123',
  admin: 'user-456',
  standardUser: 'user-789',
  partner: 'user-101'
};

// Assign roles to users
const securityManager = novaVision.securityManager;
securityManager.rbac.assignRole(users.ciso, 'CISO');
securityManager.rbac.assignRole(users.admin, 'ADMIN');
securityManager.rbac.assignRole(users.standardUser, 'USER');
securityManager.rbac.assignRole(users.partner, 'PARTNER');

// Example: Create a dashboard with Trinity CSDE visualization
const trinityDashboardConfig = {
  id: 'trinity-dashboard',
  title: 'Trinity CSDE Dashboard',
  contentType: 'trinity_csde',
  sections: [
    {
      id: 'trinity-overview',
      title: 'Trinity CSDE Overview',
      requiredPermission: PERMISSIONS.VIEW_TRINITY_CSDE,
      components: [
        {
          id: 'trinity-formula',
          type: 'formula',
          formula: 'CSDE_Trinity = πG + ϕD + (ℏ+c^-1)R',
          description: 'The Trinity CSDE formula'
        },
        {
          id: 'adaptive-ratios',
          type: 'chart',
          chartType: 'pie',
          title: 'Adaptive Ratios',
          requiredPermission: PERMISSIONS.VIEW_OPTIMIZATION
        }
      ]
    },
    {
      id: 'optimization-section',
      title: 'Optimization Process',
      requiredPermission: PERMISSIONS.VIEW_OPTIMIZATION,
      components: [
        {
          id: 'optimization-chart',
          type: 'chart',
          chartType: 'line',
          title: 'Optimization Progress'
        },
        {
          id: 'optimization-metrics',
          type: 'metrics',
          title: 'Optimization Metrics'
        }
      ]
    },
    {
      id: 'uuft-section',
      title: 'UUFT Data Quality Framework',
      requiredPermission: PERMISSIONS.VIEW_UUFT,
      components: [
        {
          id: 'uuft-visualization',
          type: 'visualization',
          title: 'UUFT Tensor Product'
        }
      ]
    }
  ]
};

// Generate dashboard schema
const trinityDashboardSchema = novaVision.generateDashboardSchema(trinityDashboardConfig);

// Example data for the dashboard
const dashboardData = {
  'trinity-formula': {
    components: {
      G: 0.314,
      D: 0.618,
      R: 0.068
    }
  },
  'adaptive-ratios': {
    father: { alpha: 0.18 },
    son: { beta: 0.18 },
    spirit: { gamma: 0.18 }
  },
  'optimization-chart': {
    labels: ['Cycle 1', 'Cycle 2', 'Cycle 3', 'Cycle 4', 'Cycle 5'],
    datasets: [
      {
        label: 'Father Ratio',
        data: [0.18, 0.19, 0.21, 0.22, 0.23]
      },
      {
        label: 'Son Ratio',
        data: [0.18, 0.17, 0.16, 0.15, 0.14]
      },
      {
        label: 'Spirit Ratio',
        data: [0.18, 0.20, 0.22, 0.24, 0.25]
      }
    ]
  },
  'optimization-metrics': {
    initialPerformance: 0.72,
    finalPerformance: 0.95,
    improvementPercentage: 31.11,
    cycles: 5,
    convergenceTime: '0.07ms'
  },
  'uuft-visualization': {
    tensorProduct: '(A ⊗ B ⊕ C) × π10³',
    performanceImprovement: '3,142x',
    accuracy: '95%'
  }
};

// Render dashboard for different users
console.log('\n=== NovaVision Security Example ===\n');

// Render for CISO (should see everything)
console.log('Rendering dashboard for CISO:');
const cisoDashboard = novaVision.renderUiFromSchema(
  trinityDashboardSchema,
  dashboardData,
  { userId: users.ciso }
);
console.log('CISO can see sections:', cisoDashboard.sections.map(s => s.id));

// Render for standard user (should see limited content)
console.log('\nRendering dashboard for standard user:');
const userDashboard = novaVision.renderUiFromSchema(
  trinityDashboardSchema,
  dashboardData,
  { userId: users.standardUser }
);
console.log('Standard user can see sections:', userDashboard.sections.map(s => s.id));

// Validate access to specific components
console.log('\nValidating component access:');
console.log('CISO access to trinity dashboard:', 
  novaVision.validateAccess(users.ciso, 'trinity_dashboard'));
console.log('CISO access to optimization chart:', 
  novaVision.validateAccess(users.ciso, 'optimization_chart'));
console.log('Standard user access to trinity dashboard:', 
  novaVision.validateAccess(users.standardUser, 'trinity_dashboard'));
console.log('Standard user access to optimization chart:', 
  novaVision.validateAccess(users.standardUser, 'optimization_chart'));

// Get security report
console.log('\nSecurity Report:');
const securityReport = novaVision.getSecurityReport();
console.log('NIST Compliance:', securityReport.nistCompliance.compliance);

console.log('\n=== End of Example ===\n');

// How to run this example:
// node src/novavision/examples/security-example.js
