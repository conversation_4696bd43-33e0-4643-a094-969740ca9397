/**
 * Integration tests for the Connector API
 */

const request = require('supertest');
const express = require('express');
const bodyParser = require('body-parser');
const connectorApi = require('../../api/connector-api');
const connectorRegistry = require('../../registry/connector-registry');
const { mockGoogleCloudConnector, mockAwsConnector } = require('../mocks/mock-connector');

// Create Express app for testing
const app = express();
app.use(bodyParser.json());
app.use('/api/connectors', connectorApi);

// Mock connector registry
jest.mock('../../registry/connector-registry', () => ({
  initialize: jest.fn().mockResolvedValue(true),
  getAllConnectors: jest.fn(),
  getConnector: jest.fn(),
  registerConnector: jest.fn().mockResolvedValue(true),
  updateConnector: jest.fn().mockResolvedValue(true),
  deleteConnector: jest.fn().mockResolvedValue(true),
  searchConnectors: jest.fn(),
  getConnectorsByCategory: jest.fn()
}));

describe('Connector API', () => {
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
  });
  
  test('GET /api/connectors should return all connectors', async () => {
    // Mock connector registry response
    connectorRegistry.getAllConnectors.mockReturnValue([
      mockGoogleCloudConnector,
      mockAwsConnector
    ]);
    
    // Make request
    const response = await request(app)
      .get('/api/connectors')
      .expect('Content-Type', /json/)
      .expect(200);
    
    // Verify response
    expect(response.body).toHaveLength(2);
    expect(response.body[0].metadata.name).toBe('Google Cloud Security');
    expect(response.body[1].metadata.name).toBe('AWS Security Hub');
    
    // Verify connector registry was called
    expect(connectorRegistry.getAllConnectors).toHaveBeenCalled();
  });
  
  test('GET /api/connectors/:id should return a connector', async () => {
    // Mock connector registry response
    connectorRegistry.getConnector.mockReturnValue(mockGoogleCloudConnector);
    
    // Make request
    const response = await request(app)
      .get('/api/connectors/google-cloud-security-1.0.0')
      .expect('Content-Type', /json/)
      .expect(200);
    
    // Verify response
    expect(response.body.metadata.name).toBe('Google Cloud Security');
    
    // Verify connector registry was called
    expect(connectorRegistry.getConnector).toHaveBeenCalledWith('google-cloud-security-1.0.0');
  });
  
  test('GET /api/connectors/:id should return 404 for non-existent connector', async () => {
    // Mock connector registry response
    connectorRegistry.getConnector.mockReturnValue(null);
    
    // Make request
    await request(app)
      .get('/api/connectors/non-existent-connector')
      .expect('Content-Type', /json/)
      .expect(404);
    
    // Verify connector registry was called
    expect(connectorRegistry.getConnector).toHaveBeenCalledWith('non-existent-connector');
  });
  
  test('POST /api/connectors should register a new connector', async () => {
    // Make request
    const response = await request(app)
      .post('/api/connectors')
      .send(mockAwsConnector)
      .expect('Content-Type', /json/)
      .expect(201);
    
    // Verify response
    expect(response.body.success).toBe(true);
    expect(response.body.message).toBe('Connector registered successfully');
    
    // Verify connector registry was called
    expect(connectorRegistry.registerConnector).toHaveBeenCalledWith(mockAwsConnector);
  });
  
  test('POST /api/connectors should return 400 for missing template', async () => {
    // Make request
    await request(app)
      .post('/api/connectors')
      .send({})
      .expect('Content-Type', /json/)
      .expect(400);
    
    // Verify connector registry was not called
    expect(connectorRegistry.registerConnector).not.toHaveBeenCalled();
  });
  
  test('PUT /api/connectors/:id should update a connector', async () => {
    // Make request
    const response = await request(app)
      .put('/api/connectors/aws-security-hub-1.0.0')
      .send(mockAwsConnector)
      .expect('Content-Type', /json/)
      .expect(200);
    
    // Verify response
    expect(response.body.success).toBe(true);
    expect(response.body.message).toBe('Connector updated successfully');
    
    // Verify connector registry was called
    expect(connectorRegistry.updateConnector).toHaveBeenCalledWith('aws-security-hub-1.0.0', mockAwsConnector);
  });
  
  test('DELETE /api/connectors/:id should delete a connector', async () => {
    // Make request
    const response = await request(app)
      .delete('/api/connectors/aws-security-hub-1.0.0')
      .expect('Content-Type', /json/)
      .expect(200);
    
    // Verify response
    expect(response.body.success).toBe(true);
    expect(response.body.message).toBe('Connector deleted successfully');
    
    // Verify connector registry was called
    expect(connectorRegistry.deleteConnector).toHaveBeenCalledWith('aws-security-hub-1.0.0');
  });
  
  test('GET /api/connectors/search/:query should search connectors', async () => {
    // Mock connector registry response
    connectorRegistry.searchConnectors.mockReturnValue([mockAwsConnector]);
    
    // Make request
    const response = await request(app)
      .get('/api/connectors/search/aws')
      .expect('Content-Type', /json/)
      .expect(200);
    
    // Verify response
    expect(response.body).toHaveLength(1);
    expect(response.body[0].metadata.name).toBe('AWS Security Hub');
    
    // Verify connector registry was called
    expect(connectorRegistry.searchConnectors).toHaveBeenCalledWith('aws');
  });
  
  test('GET /api/connectors/category/:category should get connectors by category', async () => {
    // Mock connector registry response
    connectorRegistry.getConnectorsByCategory.mockReturnValue([
      mockGoogleCloudConnector,
      mockAwsConnector
    ]);
    
    // Make request
    const response = await request(app)
      .get('/api/connectors/category/Cloud%20Security')
      .expect('Content-Type', /json/)
      .expect(200);
    
    // Verify response
    expect(response.body).toHaveLength(2);
    
    // Verify connector registry was called
    expect(connectorRegistry.getConnectorsByCategory).toHaveBeenCalledWith('Cloud Security');
  });
});
