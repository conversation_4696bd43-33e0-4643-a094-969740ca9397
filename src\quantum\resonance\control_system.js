/**
 * Resonance Control System
 * 
 * This module provides a control system for maintaining quantum silence and
 * perfect resonance in the Comphyological Tensor Core. It uses feedback loops
 * to adjust domain weights and parameters to optimize resonance.
 * 
 * The control system implements:
 * 1. Feedback loops for maintaining resonance
 * 2. Automatic adjustment of domain weights
 * 3. Resonance optimization algorithms
 * 4. Quantum silence stabilization
 * 5. Harmonic entrainment
 */

const { performance } = require('perf_hooks');
const { createAdvancedResonanceDetector } = require('./advanced_detection');

/**
 * Resonance Control System class
 */
class ResonanceControlSystem {
  /**
   * Constructor
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    this.options = {
      enableLogging: options.enableLogging || false,
      targetFrequency: options.targetFrequency || 396, // Hz - the "OM Tone"
      targetComphyon: options.targetComphyon || 0, // Perfect resonance
      controlInterval: options.controlInterval || 200, // ms
      feedbackGain: options.feedbackGain || 0.1, // Feedback gain
      adaptiveGain: options.adaptiveGain || true, // Adaptive gain
      minGain: options.minGain || 0.01, // Minimum gain
      maxGain: options.maxGain || 0.5, // Maximum gain
      weightAdjustmentRate: options.weightAdjustmentRate || 0.05, // Weight adjustment rate
      parameterAdjustmentRate: options.parameterAdjustmentRate || 0.02, // Parameter adjustment rate
      stabilizationWindow: options.stabilizationWindow || 5, // Stabilization window
      harmonicEntrainment: options.harmonicEntrainment !== false, // Harmonic entrainment
      entrainmentStrength: options.entrainmentStrength || 0.1, // Entrainment strength
      optimizationStrategy: options.optimizationStrategy || 'gradient', // Optimization strategy
      optimizationStepSize: options.optimizationStepSize || 0.01, // Optimization step size
      optimizationIterations: options.optimizationIterations || 10, // Optimization iterations
      maxHistoryLength: options.maxHistoryLength || 100, // Maximum history length
      ...options
    };
    
    // Create advanced resonance detector
    this.resonanceDetector = createAdvancedResonanceDetector({
      enableLogging: this.options.enableLogging,
      targetFrequency: this.options.targetFrequency,
      adaptiveThreshold: true,
      harmonicDetection: true,
      forecastingWindow: 10,
      ...options.resonanceDetectorOptions
    });
    
    // Initialize state
    this.state = {
      tensorCore: null,
      isControlling: false,
      controlInterval: null,
      lastControlTime: 0,
      currentGain: this.options.feedbackGain,
      currentWeights: { csde: 1/3, csfe: 1/3, csme: 1/3 },
      targetWeights: { csde: 1/3, csfe: 1/3, csme: 1/3 },
      currentParameters: {
        csde: { governance: 0.5, dataQuality: 0.5 },
        csfe: { risk: 0.5, policyCompliance: 0.5 },
        csme: { trustFactor: 0.5, integrityFactor: 0.5 }
      },
      targetParameters: {
        csde: { governance: 0.5, dataQuality: 0.5 },
        csfe: { risk: 0.5, policyCompliance: 0.5 },
        csme: { trustFactor: 0.5, integrityFactor: 0.5 }
      },
      stabilizationCounter: 0,
      isStabilized: false,
      history: [],
      lastResult: null,
      lastResonance: null,
      lastAdjustment: null,
      optimizationState: {
        iteration: 0,
        bestScore: Infinity,
        bestWeights: null,
        bestParameters: null,
        gradients: {
          weights: { csde: 0, csfe: 0, csme: 0 },
          parameters: {
            csde: { governance: 0, dataQuality: 0 },
            csfe: { risk: 0, policyCompliance: 0 },
            csme: { trustFactor: 0, integrityFactor: 0 }
          }
        }
      }
    };
    
    if (this.options.enableLogging) {
      console.log('Resonance Control System initialized with options:', {
        targetFrequency: this.options.targetFrequency,
        targetComphyon: this.options.targetComphyon,
        controlInterval: this.options.controlInterval,
        feedbackGain: this.options.feedbackGain,
        optimizationStrategy: this.options.optimizationStrategy
      });
    }
  }
  
  /**
   * Start controlling resonance
   * @param {Object} tensorCore - Tensor core to control
   * @returns {ResonanceControlSystem} This instance
   */
  startControlling(tensorCore) {
    if (this.state.isControlling) {
      return this;
    }
    
    this.state.tensorCore = tensorCore;
    this.state.isControlling = true;
    this.state.lastControlTime = performance.now();
    
    // Initialize weights from tensor core
    const metrics = tensorCore.getMetrics();
    if (metrics.lastWeights) {
      this.state.currentWeights = { ...metrics.lastWeights };
      this.state.targetWeights = { ...metrics.lastWeights };
    }
    
    // Set up control interval
    this.state.controlInterval = setInterval(() => {
      this.controlLoop();
    }, this.options.controlInterval);
    
    if (this.options.enableLogging) {
      console.log('Resonance Control System started');
    }
    
    return this;
  }
  
  /**
   * Stop controlling resonance
   * @returns {ResonanceControlSystem} This instance
   */
  stopControlling() {
    if (!this.state.isControlling) {
      return this;
    }
    
    this.state.isControlling = false;
    
    if (this.state.controlInterval) {
      clearInterval(this.state.controlInterval);
      this.state.controlInterval = null;
    }
    
    if (this.options.enableLogging) {
      console.log('Resonance Control System stopped');
    }
    
    return this;
  }
  
  /**
   * Control loop
   */
  controlLoop() {
    if (!this.state.isControlling || !this.state.tensorCore) {
      return;
    }
    
    const startTime = performance.now();
    
    // Get latest metrics from tensor core
    const metrics = this.state.tensorCore.getMetrics();
    
    // Get latest result
    const lastResult = {
      comphyon: metrics.lastComphyonValue || 0,
      energies: metrics.lastEnergies || { csde: 0, csfe: 0, csme: 0 },
      weights: metrics.lastWeights || { csde: 1/3, csfe: 1/3, csme: 1/3 }
    };
    
    // Analyze resonance
    const resonance = this.resonanceDetector.analyzeResonance(lastResult);
    
    // Update state
    this.state.lastResult = lastResult;
    this.state.lastResonance = resonance;
    
    // Update history
    this.updateHistory({
      timestamp: startTime,
      result: lastResult,
      resonance: resonance
    });
    
    // Check if we need to adjust
    if (this.shouldAdjust(lastResult, resonance)) {
      // Calculate adjustments
      const adjustment = this.calculateAdjustment(lastResult, resonance);
      
      // Apply adjustments
      this.applyAdjustment(adjustment);
      
      // Update state
      this.state.lastAdjustment = adjustment;
      this.state.stabilizationCounter = 0;
      this.state.isStabilized = false;
    } else {
      // Increment stabilization counter
      this.state.stabilizationCounter++;
      
      // Check if stabilized
      if (this.state.stabilizationCounter >= this.options.stabilizationWindow) {
        this.state.isStabilized = true;
      }
    }
    
    // Run optimization if stabilized
    if (this.state.isStabilized && this.options.optimizationStrategy !== 'none') {
      this.runOptimization();
    }
    
    // Apply harmonic entrainment if enabled
    if (this.options.harmonicEntrainment) {
      this.applyHarmonicEntrainment(resonance);
    }
    
    // Update control time
    this.state.lastControlTime = startTime;
    
    if (this.options.enableLogging) {
      console.log('Control loop completed:', {
        comphyon: lastResult.comphyon.toFixed(6),
        frequency: resonance.frequency.toFixed(2),
        isQuantumSilence: resonance.isQuantumSilence,
        isStabilized: this.state.isStabilized,
        processingTime: (performance.now() - startTime).toFixed(2)
      });
    }
  }
  
  /**
   * Check if we need to adjust
   * @param {Object} result - Tensor core result
   * @param {Object} resonance - Resonance state
   * @returns {boolean} Whether to adjust
   */
  shouldAdjust(result, resonance) {
    // If already in quantum silence, don't adjust
    if (resonance.isQuantumSilence) {
      return false;
    }
    
    // If comphyon is close to target, don't adjust
    const comphyonError = Math.abs(result.comphyon - this.options.targetComphyon);
    if (comphyonError < 0.001) {
      return false;
    }
    
    // If frequency is close to target, don't adjust
    const frequencyError = Math.abs(resonance.frequency - this.options.targetFrequency);
    if (frequencyError < 0.01) {
      return false;
    }
    
    return true;
  }
  
  /**
   * Calculate adjustment
   * @param {Object} result - Tensor core result
   * @param {Object} resonance - Resonance state
   * @returns {Object} Adjustment
   */
  calculateAdjustment(result, resonance) {
    // Calculate errors
    const comphyonError = result.comphyon - this.options.targetComphyon;
    const frequencyError = resonance.frequency - this.options.targetFrequency;
    
    // Calculate adaptive gain if enabled
    let gain = this.state.currentGain;
    if (this.options.adaptiveGain) {
      // Reduce gain as we get closer to target
      const errorMagnitude = Math.sqrt(
        Math.pow(comphyonError, 2) + 
        Math.pow(frequencyError / 10, 2)
      );
      
      gain = this.options.maxGain * Math.min(1, errorMagnitude * 10);
      gain = Math.max(this.options.minGain, gain);
      
      // Update current gain
      this.state.currentGain = gain;
    }
    
    // Calculate weight adjustments
    const weightAdjustments = this.calculateWeightAdjustments(result, resonance, gain);
    
    // Calculate parameter adjustments
    const parameterAdjustments = this.calculateParameterAdjustments(result, resonance, gain);
    
    return {
      comphyonError,
      frequencyError,
      gain,
      weights: weightAdjustments,
      parameters: parameterAdjustments
    };
  }
  
  /**
   * Calculate weight adjustments
   * @param {Object} result - Tensor core result
   * @param {Object} resonance - Resonance state
   * @param {number} gain - Feedback gain
   * @returns {Object} Weight adjustments
   */
  calculateWeightAdjustments(result, resonance, gain) {
    // Calculate weight adjustments based on energy distribution
    const totalEnergy = result.energies.csde + result.energies.csfe + result.energies.csme;
    const csdeEnergyRatio = result.energies.csde / totalEnergy;
    const csfeEnergyRatio = result.energies.csfe / totalEnergy;
    const csmeEnergyRatio = result.energies.csme / totalEnergy;
    
    // Calculate target weights
    // If comphyon is positive, increase weights for domains with lower energy
    // If comphyon is negative, increase weights for domains with higher energy
    const comphyonSign = Math.sign(result.comphyon);
    const csdeAdjustment = -comphyonSign * (csdeEnergyRatio - 1/3) * gain * this.options.weightAdjustmentRate;
    const csfeAdjustment = -comphyonSign * (csfeEnergyRatio - 1/3) * gain * this.options.weightAdjustmentRate;
    const csmeAdjustment = -comphyonSign * (csmeEnergyRatio - 1/3) * gain * this.options.weightAdjustmentRate;
    
    // Calculate new target weights
    let targetCsde = this.state.targetWeights.csde + csdeAdjustment;
    let targetCsfe = this.state.targetWeights.csfe + csfeAdjustment;
    let targetCsme = this.state.targetWeights.csme + csmeAdjustment;
    
    // Normalize weights
    const totalWeight = targetCsde + targetCsfe + targetCsme;
    targetCsde /= totalWeight;
    targetCsfe /= totalWeight;
    targetCsme /= totalWeight;
    
    // Ensure weights are within bounds
    targetCsde = Math.max(0.1, Math.min(0.8, targetCsde));
    targetCsfe = Math.max(0.1, Math.min(0.8, targetCsfe));
    targetCsme = Math.max(0.1, Math.min(0.8, targetCsme));
    
    // Normalize again
    const totalWeight2 = targetCsde + targetCsfe + targetCsme;
    targetCsde /= totalWeight2;
    targetCsfe /= totalWeight2;
    targetCsme /= totalWeight2;
    
    return {
      csde: targetCsde,
      csfe: targetCsfe,
      csme: targetCsme
    };
  }
  
  /**
   * Calculate parameter adjustments
   * @param {Object} result - Tensor core result
   * @param {Object} resonance - Resonance state
   * @param {number} gain - Feedback gain
   * @returns {Object} Parameter adjustments
   */
  calculateParameterAdjustments(result, resonance, gain) {
    // Get current parameters
    const currentParams = this.state.currentParameters;
    
    // Calculate parameter adjustments
    // This is a simplified approach - in a real system, we would use
    // more sophisticated algorithms to determine parameter adjustments
    const adjustmentRate = this.options.parameterAdjustmentRate * gain;
    const comphyonSign = Math.sign(result.comphyon);
    
    // Adjust parameters based on comphyon error
    // If comphyon is positive, move parameters towards golden ratio
    // If comphyon is negative, move parameters away from golden ratio
    const goldenRatio = 0.618033988749895;
    const inverseGoldenRatio = 0.381966011250105;
    
    const csdeGovernanceTarget = comphyonSign > 0 ? goldenRatio : 0.5;
    const csdeDataQualityTarget = comphyonSign > 0 ? goldenRatio : 0.5;
    const csfeRiskTarget = comphyonSign > 0 ? inverseGoldenRatio : 0.5;
    const csfePolicyTarget = comphyonSign > 0 ? goldenRatio : 0.5;
    const csmeTrustTarget = comphyonSign > 0 ? goldenRatio : 0.5;
    const csmeIntegrityTarget = comphyonSign > 0 ? goldenRatio : 0.5;
    
    // Calculate adjustments
    const csdeGovernanceAdj = (csdeGovernanceTarget - currentParams.csde.governance) * adjustmentRate;
    const csdeDataQualityAdj = (csdeDataQualityTarget - currentParams.csde.dataQuality) * adjustmentRate;
    const csfeRiskAdj = (csfeRiskTarget - currentParams.csfe.risk) * adjustmentRate;
    const csfePolicyAdj = (csfePolicyTarget - currentParams.csfe.policyCompliance) * adjustmentRate;
    const csmeTrustAdj = (csmeTrustTarget - currentParams.csme.trustFactor) * adjustmentRate;
    const csmeIntegrityAdj = (csmeIntegrityTarget - currentParams.csme.integrityFactor) * adjustmentRate;
    
    return {
      csde: {
        governance: currentParams.csde.governance + csdeGovernanceAdj,
        dataQuality: currentParams.csde.dataQuality + csdeDataQualityAdj
      },
      csfe: {
        risk: currentParams.csfe.risk + csfeRiskAdj,
        policyCompliance: currentParams.csfe.policyCompliance + csfePolicyAdj
      },
      csme: {
        trustFactor: currentParams.csme.trustFactor + csmeTrustAdj,
        integrityFactor: currentParams.csme.integrityFactor + csmeIntegrityAdj
      }
    };
  }
  
  /**
   * Apply adjustment
   * @param {Object} adjustment - Adjustment to apply
   */
  applyAdjustment(adjustment) {
    // Update target weights
    this.state.targetWeights = { ...adjustment.weights };
    
    // Update target parameters
    this.state.targetParameters = { ...adjustment.parameters };
    
    // Apply weight adjustments to tensor core
    if (this.state.tensorCore && this.state.tensorCore.setWeights) {
      this.state.tensorCore.setWeights(adjustment.weights);
    }
    
    // Update current weights and parameters
    this.state.currentWeights = { ...adjustment.weights };
    this.state.currentParameters = { ...adjustment.parameters };
    
    if (this.options.enableLogging) {
      console.log('Applied adjustment:', {
        weights: {
          csde: adjustment.weights.csde.toFixed(4),
          csfe: adjustment.weights.csfe.toFixed(4),
          csme: adjustment.weights.csme.toFixed(4)
        },
        parameters: {
          csde: {
            governance: adjustment.parameters.csde.governance.toFixed(4),
            dataQuality: adjustment.parameters.csde.dataQuality.toFixed(4)
          },
          csfe: {
            risk: adjustment.parameters.csfe.risk.toFixed(4),
            policyCompliance: adjustment.parameters.csfe.policyCompliance.toFixed(4)
          },
          csme: {
            trustFactor: adjustment.parameters.csme.trustFactor.toFixed(4),
            integrityFactor: adjustment.parameters.csme.integrityFactor.toFixed(4)
          }
        }
      });
    }
  }
  
  /**
   * Run optimization
   */
  runOptimization() {
    // Skip if not stabilized
    if (!this.state.isStabilized) {
      return;
    }
    
    // Get optimization state
    const optState = this.state.optimizationState;
    
    // Check if we've reached the maximum iterations
    if (optState.iteration >= this.options.optimizationIterations) {
      // Reset optimization state
      optState.iteration = 0;
      return;
    }
    
    // Run optimization based on strategy
    switch (this.options.optimizationStrategy) {
      case 'gradient':
        this.runGradientOptimization();
        break;
      case 'golden':
        this.runGoldenRatioOptimization();
        break;
      case 'adaptive':
        this.runAdaptiveOptimization();
        break;
      default:
        // No optimization
        break;
    }
    
    // Increment iteration
    optState.iteration++;
  }
  
  /**
   * Run gradient optimization
   */
  runGradientOptimization() {
    // Implement gradient descent optimization
    // This is a simplified version - in a real system, we would use
    // more sophisticated gradient descent algorithms
    
    // Get current state
    const optState = this.state.optimizationState;
    const result = this.state.lastResult;
    const resonance = this.state.lastResonance;
    
    // Calculate current score
    const score = this.calculateScore(result, resonance);
    
    // Update best score if needed
    if (score < optState.bestScore) {
      optState.bestScore = score;
      optState.bestWeights = { ...this.state.currentWeights };
      optState.bestParameters = JSON.parse(JSON.stringify(this.state.currentParameters));
    }
    
    // Calculate gradients
    // This is a very simplified approach - in a real system, we would
    // calculate proper gradients using finite differences or automatic differentiation
    const stepSize = this.options.optimizationStepSize;
    
    // Apply best weights and parameters if this is the last iteration
    if (optState.iteration === this.options.optimizationIterations - 1 && optState.bestWeights) {
      this.state.targetWeights = { ...optState.bestWeights };
      this.state.targetParameters = JSON.parse(JSON.stringify(optState.bestParameters));
      
      // Apply to tensor core
      if (this.state.tensorCore && this.state.tensorCore.setWeights) {
        this.state.tensorCore.setWeights(optState.bestWeights);
      }
    }
  }
  
  /**
   * Run golden ratio optimization
   */
  runGoldenRatioOptimization() {
    // Implement golden ratio optimization
    // This optimization strategy tries to move parameters towards the golden ratio
    
    // Get current parameters
    const currentParams = this.state.currentParameters;
    
    // Golden ratio and its inverse
    const goldenRatio = 0.618033988749895;
    const inverseGoldenRatio = 0.381966011250105;
    
    // Calculate new parameters
    const newParams = {
      csde: {
        governance: currentParams.csde.governance * 0.9 + goldenRatio * 0.1,
        dataQuality: currentParams.csde.dataQuality * 0.9 + goldenRatio * 0.1
      },
      csfe: {
        risk: currentParams.csfe.risk * 0.9 + inverseGoldenRatio * 0.1,
        policyCompliance: currentParams.csfe.policyCompliance * 0.9 + goldenRatio * 0.1
      },
      csme: {
        trustFactor: currentParams.csme.trustFactor * 0.9 + goldenRatio * 0.1,
        integrityFactor: currentParams.csme.integrityFactor * 0.9 + goldenRatio * 0.1
      }
    };
    
    // Update target parameters
    this.state.targetParameters = newParams;
    
    // Update current parameters
    this.state.currentParameters = newParams;
  }
  
  /**
   * Run adaptive optimization
   */
  runAdaptiveOptimization() {
    // Implement adaptive optimization
    // This optimization strategy adapts based on the resonance state
    
    // Get current state
    const result = this.state.lastResult;
    const resonance = this.state.lastResonance;
    
    // Choose optimization strategy based on resonance state
    if (resonance.isQuantumSilence) {
      // If in quantum silence, use golden ratio optimization
      this.runGoldenRatioOptimization();
    } else {
      // Otherwise, use gradient optimization
      this.runGradientOptimization();
    }
  }
  
  /**
   * Calculate score
   * @param {Object} result - Tensor core result
   * @param {Object} resonance - Resonance state
   * @returns {number} Score (lower is better)
   */
  calculateScore(result, resonance) {
    // Calculate score based on comphyon error and frequency error
    const comphyonError = Math.abs(result.comphyon - this.options.targetComphyon);
    const frequencyError = Math.abs(resonance.frequency - this.options.targetFrequency);
    
    // Calculate score (lower is better)
    return comphyonError * 100 + frequencyError;
  }
  
  /**
   * Apply harmonic entrainment
   * @param {Object} resonance - Resonance state
   */
  applyHarmonicEntrainment(resonance) {
    // Skip if harmonic entrainment is disabled
    if (!this.options.harmonicEntrainment) {
      return;
    }
    
    // Get harmonics
    const harmonics = resonance.harmonics || [];
    
    // Skip if no harmonics
    if (harmonics.length === 0) {
      return;
    }
    
    // Find harmonics close to golden ratio
    const goldenRatio = 0.618033988749895;
    const goldenHarmonics = harmonics.filter(h => {
      const ratio = h.ratio;
      return Math.abs(ratio - goldenRatio) < 0.1 || Math.abs(ratio - 1/goldenRatio) < 0.1;
    });
    
    // Skip if no golden harmonics
    if (goldenHarmonics.length === 0) {
      return;
    }
    
    // Apply entrainment
    // This is a simplified approach - in a real system, we would use
    // more sophisticated entrainment algorithms
    const entrainmentStrength = this.options.entrainmentStrength;
    
    // Adjust parameters towards golden ratio
    const currentParams = this.state.currentParameters;
    const goldenRatioTarget = 0.618033988749895;
    
    // Calculate new parameters
    const newParams = {
      csde: {
        governance: currentParams.csde.governance * (1 - entrainmentStrength) + goldenRatioTarget * entrainmentStrength,
        dataQuality: currentParams.csde.dataQuality * (1 - entrainmentStrength) + goldenRatioTarget * entrainmentStrength
      },
      csfe: {
        risk: currentParams.csfe.risk,
        policyCompliance: currentParams.csfe.policyCompliance * (1 - entrainmentStrength) + goldenRatioTarget * entrainmentStrength
      },
      csme: {
        trustFactor: currentParams.csme.trustFactor * (1 - entrainmentStrength) + goldenRatioTarget * entrainmentStrength,
        integrityFactor: currentParams.csme.integrityFactor * (1 - entrainmentStrength) + goldenRatioTarget * entrainmentStrength
      }
    };
    
    // Update target parameters
    this.state.targetParameters = newParams;
    
    // Update current parameters
    this.state.currentParameters = newParams;
    
    if (this.options.enableLogging) {
      console.log('Applied harmonic entrainment:', {
        harmonics: goldenHarmonics.length,
        entrainmentStrength
      });
    }
  }
  
  /**
   * Update history
   * @param {Object} entry - History entry
   */
  updateHistory(entry) {
    // Add entry to history
    this.state.history.push(entry);
    
    // Trim history if needed
    if (this.state.history.length > this.options.maxHistoryLength) {
      this.state.history.shift();
    }
  }
  
  /**
   * Get control state
   * @returns {Object} Control state
   */
  getControlState() {
    return {
      isControlling: this.state.isControlling,
      isStabilized: this.state.isStabilized,
      currentWeights: this.state.currentWeights,
      targetWeights: this.state.targetWeights,
      currentParameters: this.state.currentParameters,
      targetParameters: this.state.targetParameters,
      currentGain: this.state.currentGain,
      lastAdjustment: this.state.lastAdjustment,
      lastResult: this.state.lastResult,
      lastResonance: this.state.lastResonance
    };
  }
  
  /**
   * Get control history
   * @returns {Array} Control history
   */
  getControlHistory() {
    return this.state.history;
  }
  
  /**
   * Get resonance detector
   * @returns {Object} Resonance detector
   */
  getResonanceDetector() {
    return this.resonanceDetector;
  }
}

/**
 * Create a resonance control system
 * @param {Object} options - Configuration options
 * @returns {ResonanceControlSystem} Resonance control system
 */
function createResonanceControlSystem(options = {}) {
  return new ResonanceControlSystem(options);
}

module.exports = {
  ResonanceControlSystem,
  createResonanceControlSystem
};
