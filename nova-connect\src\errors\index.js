/**
 * NovaFuse Universal API Connector - Errors
 * 
 * This module exports all error types for the UAC.
 */

const UAConnectorError = require('./base-error');
const AuthenticationErrors = require('./authentication-error');
const ConnectionErrors = require('./connection-error');
const ValidationErrors = require('./validation-error');
const ApiErrors = require('./api-error');
const TransformationErrors = require('./transformation-error');
const ConnectorErrors = require('./connector-error');

module.exports = {
  // Base error
  UAConnectorError,
  
  // Authentication errors
  ...AuthenticationErrors,
  
  // Connection errors
  ...ConnectionErrors,
  
  // Validation errors
  ...ValidationErrors,
  
  // API errors
  ...ApiErrors,
  
  // Transformation errors
  ...TransformationErrors,
  
  // Connector errors
  ...ConnectorErrors
};
