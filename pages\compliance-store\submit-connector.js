import React, { useState } from 'react';
import Link from 'next/link';
import PageWithSidebar from '../../components/PageWithSidebar';

export default function SubmitConnector() {
  // Define sidebar items for the Submit Connector page
  const sidebarItems = [
    { type: 'category', label: 'Submit Connector', items: [
      { label: 'Submission Form', href: '#form' },
      { label: 'Submission Guidelines', href: '#guidelines' },
      { label: 'Certification Process', href: '#certification' }
    ]},
    { type: 'category', label: 'Navigation', items: [
      { label: 'Back to Browse', href: '/compliance-store/browse' },
      { label: 'Compliance Store Home', href: '/compliance-store' }
    ]}
  ];

  // SEO metadata
  const pageProps = {
    title: 'Submit a Connector - NovaFuse Compliance App Store',
    description: 'Submit your compliance connector to the NovaFuse Compliance App Store and reach thousands of organizations looking for compliance solutions.',
    keywords: 'submit connector, compliance connector, NovaFuse Compliance App Store, partner program',
    canonical: 'https://novafuse.io/compliance-store/submit-connector',
    ogImage: '/images/submit-connector-og-image.png'
  };

  // Form state
  const [formData, setFormData] = useState({
    connectorName: '',
    vendorName: '',
    description: '',
    longDescription: '',
    category: '',
    framework: '',
    features: '',
    integrations: '',
    price: '',
    email: '',
    phone: '',
    website: '',
    termsAccepted: false
  });

  // Form validation state
  const [errors, setErrors] = useState({});
  
  // Form submission state
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [submitError, setSubmitError] = useState('');

  // Handle form input changes
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
    
    // Clear error for this field when user starts typing
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: ''
      });
    }
  };

  // Validate form
  const validateForm = () => {
    const newErrors = {};
    
    // Required fields
    if (!formData.connectorName.trim()) newErrors.connectorName = 'Connector name is required';
    if (!formData.vendorName.trim()) newErrors.vendorName = 'Vendor name is required';
    if (!formData.description.trim()) newErrors.description = 'Description is required';
    if (!formData.category) newErrors.category = 'Category is required';
    if (!formData.framework) newErrors.framework = 'Framework is required';
    if (!formData.price.trim()) newErrors.price = 'Price is required';
    if (!formData.email.trim()) newErrors.email = 'Email is required';
    
    // Email validation
    if (formData.email && !/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(formData.email)) {
      newErrors.email = 'Invalid email address';
    }
    
    // Terms acceptance
    if (!formData.termsAccepted) newErrors.termsAccepted = 'You must accept the terms and conditions';
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) return;
    
    setIsSubmitting(true);
    setSubmitError('');
    
    try {
      // In a real app, this would be an API call to submit the connector
      // For now, we'll simulate a successful submission after a delay
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      setSubmitSuccess(true);
      // Reset form
      setFormData({
        connectorName: '',
        vendorName: '',
        description: '',
        longDescription: '',
        category: '',
        framework: '',
        features: '',
        integrations: '',
        price: '',
        email: '',
        phone: '',
        website: '',
        termsAccepted: false
      });
    } catch (error) {
      setSubmitError('An error occurred while submitting your connector. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <PageWithSidebar title={pageProps.title} sidebarItems={sidebarItems} {...pageProps}>
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-4">Submit a Connector</h1>
        <p className="text-xl">
          Join the NovaFuse Compliance App Store and reach thousands of organizations looking for compliance solutions.
        </p>
      </div>

      {/* Coming Soon Notice */}
      <div className="bg-blue-900 bg-opacity-30 border border-blue-700 rounded-lg p-6 mb-8">
        <h2 className="text-xl font-bold mb-2">Coming Soon</h2>
        <p>
          The connector submission process is currently in preview mode. Your submissions will be saved for review
          when the Compliance App Store launches.
        </p>
      </div>

      {submitSuccess ? (
        <div className="bg-green-900 bg-opacity-30 border border-green-700 rounded-lg p-6 mb-8">
          <h2 className="text-xl font-bold mb-2">Submission Received!</h2>
          <p className="mb-4">
            Thank you for submitting your connector to the NovaFuse Compliance App Store. Our team will review your submission
            and contact you with next steps.
          </p>
          <button
            onClick={() => setSubmitSuccess(false)}
            className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded-lg"
          >
            Submit Another Connector
          </button>
        </div>
      ) : (
        <>
          {/* Submission Form */}
          <div id="form" className="mb-12">
            <h2 className="text-2xl font-bold mb-6">Connector Submission Form</h2>
            <div className="bg-secondary rounded-lg p-6">
              <form onSubmit={handleSubmit}>
                <div className="mb-8">
                  <h3 className="text-xl font-semibold mb-4">Connector Information</h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                      <label htmlFor="connectorName" className="block text-sm font-medium mb-1">
                        Connector Name <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        id="connectorName"
                        name="connectorName"
                        value={formData.connectorName}
                        onChange={handleChange}
                        className={`w-full bg-gray-800 border ${errors.connectorName ? 'border-red-500' : 'border-gray-700'} rounded-lg py-2 px-3 focus:outline-none focus:ring-2 focus:ring-blue-600`}
                        placeholder="e.g., GDPR Shield"
                      />
                      {errors.connectorName && (
                        <p className="text-red-500 text-sm mt-1">{errors.connectorName}</p>
                      )}
                    </div>
                    
                    <div>
                      <label htmlFor="vendorName" className="block text-sm font-medium mb-1">
                        Vendor Name <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        id="vendorName"
                        name="vendorName"
                        value={formData.vendorName}
                        onChange={handleChange}
                        className={`w-full bg-gray-800 border ${errors.vendorName ? 'border-red-500' : 'border-gray-700'} rounded-lg py-2 px-3 focus:outline-none focus:ring-2 focus:ring-blue-600`}
                        placeholder="e.g., Compliance Partners Inc."
                      />
                      {errors.vendorName && (
                        <p className="text-red-500 text-sm mt-1">{errors.vendorName}</p>
                      )}
                    </div>
                  </div>
                  
                  <div className="mb-6">
                    <label htmlFor="description" className="block text-sm font-medium mb-1">
                      Short Description <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      id="description"
                      name="description"
                      value={formData.description}
                      onChange={handleChange}
                      className={`w-full bg-gray-800 border ${errors.description ? 'border-red-500' : 'border-gray-700'} rounded-lg py-2 px-3 focus:outline-none focus:ring-2 focus:ring-blue-600`}
                      placeholder="A brief description of your connector (max 150 characters)"
                      maxLength={150}
                    />
                    {errors.description && (
                      <p className="text-red-500 text-sm mt-1">{errors.description}</p>
                    )}
                  </div>
                  
                  <div className="mb-6">
                    <label htmlFor="longDescription" className="block text-sm font-medium mb-1">
                      Long Description
                    </label>
                    <textarea
                      id="longDescription"
                      name="longDescription"
                      value={formData.longDescription}
                      onChange={handleChange}
                      className="w-full bg-gray-800 border border-gray-700 rounded-lg py-2 px-3 focus:outline-none focus:ring-2 focus:ring-blue-600"
                      placeholder="A detailed description of your connector"
                      rows={4}
                    />
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                      <label htmlFor="category" className="block text-sm font-medium mb-1">
                        Category <span className="text-red-500">*</span>
                      </label>
                      <select
                        id="category"
                        name="category"
                        value={formData.category}
                        onChange={handleChange}
                        className={`w-full bg-gray-800 border ${errors.category ? 'border-red-500' : 'border-gray-700'} rounded-lg py-2 px-3 focus:outline-none focus:ring-2 focus:ring-blue-600`}
                      >
                        <option value="">Select a category</option>
                        <option value="data-privacy">Data Privacy</option>
                        <option value="security">Security</option>
                        <option value="healthcare">Healthcare</option>
                        <option value="financial">Financial</option>
                        <option value="other">Other</option>
                      </select>
                      {errors.category && (
                        <p className="text-red-500 text-sm mt-1">{errors.category}</p>
                      )}
                    </div>
                    
                    <div>
                      <label htmlFor="framework" className="block text-sm font-medium mb-1">
                        Framework <span className="text-red-500">*</span>
                      </label>
                      <select
                        id="framework"
                        name="framework"
                        value={formData.framework}
                        onChange={handleChange}
                        className={`w-full bg-gray-800 border ${errors.framework ? 'border-red-500' : 'border-gray-700'} rounded-lg py-2 px-3 focus:outline-none focus:ring-2 focus:ring-blue-600`}
                      >
                        <option value="">Select a framework</option>
                        <option value="gdpr">GDPR</option>
                        <option value="hipaa">HIPAA</option>
                        <option value="soc2">SOC 2</option>
                        <option value="pci-dss">PCI DSS</option>
                        <option value="iso-27001">ISO 27001</option>
                        <option value="ccpa">CCPA</option>
                        <option value="nist">NIST</option>
                        <option value="finra">FINRA</option>
                        <option value="fedramp">FedRAMP</option>
                        <option value="other">Other</option>
                      </select>
                      {errors.framework && (
                        <p className="text-red-500 text-sm mt-1">{errors.framework}</p>
                      )}
                    </div>
                  </div>
                  
                  <div className="mb-6">
                    <label htmlFor="features" className="block text-sm font-medium mb-1">
                      Key Features
                    </label>
                    <textarea
                      id="features"
                      name="features"
                      value={formData.features}
                      onChange={handleChange}
                      className="w-full bg-gray-800 border border-gray-700 rounded-lg py-2 px-3 focus:outline-none focus:ring-2 focus:ring-blue-600"
                      placeholder="List the key features of your connector (one per line)"
                      rows={4}
                    />
                  </div>
                  
                  <div className="mb-6">
                    <label htmlFor="integrations" className="block text-sm font-medium mb-1">
                      Integrations
                    </label>
                    <textarea
                      id="integrations"
                      name="integrations"
                      value={formData.integrations}
                      onChange={handleChange}
                      className="w-full bg-gray-800 border border-gray-700 rounded-lg py-2 px-3 focus:outline-none focus:ring-2 focus:ring-blue-600"
                      placeholder="List the systems your connector integrates with (one per line)"
                      rows={4}
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="price" className="block text-sm font-medium mb-1">
                      Price <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      id="price"
                      name="price"
                      value={formData.price}
                      onChange={handleChange}
                      className={`w-full bg-gray-800 border ${errors.price ? 'border-red-500' : 'border-gray-700'} rounded-lg py-2 px-3 focus:outline-none focus:ring-2 focus:ring-blue-600`}
                      placeholder="e.g., $499/mo"
                    />
                    {errors.price && (
                      <p className="text-red-500 text-sm mt-1">{errors.price}</p>
                    )}
                  </div>
                </div>
                
                <div className="mb-8">
                  <h3 className="text-xl font-semibold mb-4">Contact Information</h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                      <label htmlFor="email" className="block text-sm font-medium mb-1">
                        Email <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        value={formData.email}
                        onChange={handleChange}
                        className={`w-full bg-gray-800 border ${errors.email ? 'border-red-500' : 'border-gray-700'} rounded-lg py-2 px-3 focus:outline-none focus:ring-2 focus:ring-blue-600`}
                        placeholder="<EMAIL>"
                      />
                      {errors.email && (
                        <p className="text-red-500 text-sm mt-1">{errors.email}</p>
                      )}
                    </div>
                    
                    <div>
                      <label htmlFor="phone" className="block text-sm font-medium mb-1">
                        Phone
                      </label>
                      <input
                        type="tel"
                        id="phone"
                        name="phone"
                        value={formData.phone}
                        onChange={handleChange}
                        className="w-full bg-gray-800 border border-gray-700 rounded-lg py-2 px-3 focus:outline-none focus:ring-2 focus:ring-blue-600"
                        placeholder="+****************"
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label htmlFor="website" className="block text-sm font-medium mb-1">
                      Website
                    </label>
                    <input
                      type="url"
                      id="website"
                      name="website"
                      value={formData.website}
                      onChange={handleChange}
                      className="w-full bg-gray-800 border border-gray-700 rounded-lg py-2 px-3 focus:outline-none focus:ring-2 focus:ring-blue-600"
                      placeholder="https://example.com"
                    />
                  </div>
                </div>
                
                <div className="mb-8">
                  <div className="flex items-start">
                    <div className="flex items-center h-5">
                      <input
                        id="termsAccepted"
                        name="termsAccepted"
                        type="checkbox"
                        checked={formData.termsAccepted}
                        onChange={handleChange}
                        className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-700 rounded"
                      />
                    </div>
                    <div className="ml-3 text-sm">
                      <label htmlFor="termsAccepted" className={`font-medium ${errors.termsAccepted ? 'text-red-500' : 'text-gray-300'}`}>
                        I agree to the <Link href="/terms" className="text-blue-400 hover:text-blue-300">Terms and Conditions</Link> and <Link href="/privacy" className="text-blue-400 hover:text-blue-300">Privacy Policy</Link>
                      </label>
                      {errors.termsAccepted && (
                        <p className="text-red-500 text-sm mt-1">{errors.termsAccepted}</p>
                      )}
                    </div>
                  </div>
                </div>
                
                {submitError && (
                  <div className="bg-red-900 bg-opacity-30 border border-red-700 rounded-lg p-4 mb-6">
                    <p className="text-red-400">{submitError}</p>
                  </div>
                )}
                
                <div className="flex justify-end">
                  <button
                    type="submit"
                    className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <span className="flex items-center">
                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Submitting...
                      </span>
                    ) : 'Submit Connector'}
                  </button>
                </div>
              </form>
            </div>
          </div>

          {/* Submission Guidelines */}
          <div id="guidelines" className="mb-12">
            <h2 className="text-2xl font-bold mb-6">Submission Guidelines</h2>
            <div className="bg-secondary rounded-lg p-6">
              <p className="mb-4">
                To ensure your connector is approved quickly, please follow these guidelines:
              </p>
              <ul className="space-y-3">
                <li className="flex items-start">
                  <span className="text-blue-400 mr-2">•</span>
                  <span>Provide accurate and detailed information about your connector</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-400 mr-2">•</span>
                  <span>Ensure your connector meets our technical requirements</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-400 mr-2">•</span>
                  <span>Include clear documentation on how to use your connector</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-400 mr-2">•</span>
                  <span>Test your connector thoroughly before submission</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-400 mr-2">•</span>
                  <span>Be responsive to feedback and requests for changes</span>
                </li>
              </ul>
            </div>
          </div>

          {/* Certification Process */}
          <div id="certification" className="mb-12">
            <h2 className="text-2xl font-bold mb-6">Certification Process</h2>
            <div className="bg-secondary rounded-lg p-6">
              <p className="mb-4">
                All connectors submitted to the NovaFuse Compliance App Store go through a rigorous certification process:
              </p>
              <ol className="space-y-6">
                <li className="flex">
                  <div className="flex-shrink-0 flex items-center justify-center w-8 h-8 rounded-full bg-blue-600 text-white font-bold mr-4">
                    1
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold mb-2">Initial Review</h3>
                    <p>Our team reviews your submission to ensure it meets our basic requirements and guidelines.</p>
                  </div>
                </li>
                <li className="flex">
                  <div className="flex-shrink-0 flex items-center justify-center w-8 h-8 rounded-full bg-blue-600 text-white font-bold mr-4">
                    2
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold mb-2">Technical Validation</h3>
                    <p>We test your connector to ensure it functions correctly and meets our performance standards.</p>
                  </div>
                </li>
                <li className="flex">
                  <div className="flex-shrink-0 flex items-center justify-center w-8 h-8 rounded-full bg-blue-600 text-white font-bold mr-4">
                    3
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold mb-2">Security Assessment</h3>
                    <p>Our security team evaluates your connector for potential vulnerabilities and security risks.</p>
                  </div>
                </li>
                <li className="flex">
                  <div className="flex-shrink-0 flex items-center justify-center w-8 h-8 rounded-full bg-blue-600 text-white font-bold mr-4">
                    4
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold mb-2">Compliance Verification</h3>
                    <p>We verify that your connector accurately addresses the compliance requirements it claims to support.</p>
                  </div>
                </li>
                <li className="flex">
                  <div className="flex-shrink-0 flex items-center justify-center w-8 h-8 rounded-full bg-blue-600 text-white font-bold mr-4">
                    5
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold mb-2">Final Approval</h3>
                    <p>Once all checks are passed, your connector is approved and published to the Compliance App Store.</p>
                  </div>
                </li>
              </ol>
            </div>
          </div>
        </>
      )}

      {/* Call to Action */}
      <div className="bg-blue-900 rounded-lg p-8 text-center mb-12">
        <h2 className="text-2xl font-bold mb-4">Have questions about submitting a connector?</h2>
        <p className="mb-6">Our team is here to help you through the submission process.</p>
        <Link href="/contact" className="inline-block bg-white text-blue-700 hover:bg-gray-100 font-bold py-2 px-6 rounded-lg">
          Contact Us
        </Link>
      </div>

      {/* Confidentiality Notice */}
      <div className="border border-blue-800 bg-blue-900 bg-opacity-20 rounded-lg p-4">
        <div className="flex items-start">
          <div className="text-yellow-400 mr-3 mt-1">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
          </div>
          <div>
            <p className="text-sm">
              <strong>CONFIDENTIAL:</strong> The NovaFuse Compliance App Store is currently under IP protection review.
              All content is considered confidential and proprietary. Unauthorized access or sharing is prohibited.
            </p>
          </div>
        </div>
      </div>
    </PageWithSidebar>
  );
}
