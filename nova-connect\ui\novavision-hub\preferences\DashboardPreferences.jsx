/**
 * DashboardPreferences Component
 * 
 * A component for managing dashboard preferences.
 */

import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { usePreferences } from './PreferencesContext';
import { TabPanel } from '../components';

/**
 * DashboardPreferences component
 * 
 * @param {Object} props - Component props
 * @param {string} props.dashboardId - Dashboard ID
 * @param {Function} [props.onClose] - Function to call when the preferences manager is closed
 * @param {string} [props.className] - Additional CSS class names
 * @param {Object} [props.style] - Additional inline styles
 * @returns {React.ReactElement} DashboardPreferences component
 */
const DashboardPreferences = ({
  dashboardId,
  onClose,
  className = '',
  style = {}
}) => {
  const { 
    preferences, 
    getDashboardPreferences,
    updateDashboardLayout,
    updateDashboardWidgets,
    updateDashboardSettings
  } = usePreferences();
  
  // Get dashboard preferences
  const dashboardPreferences = getDashboardPreferences(dashboardId);
  
  const [activeTab, setActiveTab] = useState('layout');
  const [layoutSettings, setLayoutSettings] = useState(dashboardPreferences.layout || {});
  const [widgetSettings, setWidgetSettings] = useState(dashboardPreferences.widgets || []);
  const [dashboardSettings, setDashboardSettings] = useState(dashboardPreferences.settings || {});
  
  // Update state when dashboard preferences change
  useEffect(() => {
    setLayoutSettings(dashboardPreferences.layout || {});
    setWidgetSettings(dashboardPreferences.widgets || []);
    setDashboardSettings(dashboardPreferences.settings || {});
  }, [dashboardPreferences]);
  
  // Handle save
  const handleSave = () => {
    // Update layout settings
    updateDashboardLayout(dashboardId, layoutSettings);
    
    // Update widget settings
    updateDashboardWidgets(dashboardId, widgetSettings);
    
    // Update dashboard settings
    updateDashboardSettings(dashboardId, dashboardSettings);
    
    // Close preferences manager
    if (onClose) {
      onClose();
    }
  };
  
  // Handle cancel
  const handleCancel = () => {
    if (onClose) {
      onClose();
    }
  };
  
  // Update layout settings
  const handleLayoutSettingsChange = (key, value) => {
    setLayoutSettings(prevSettings => ({
      ...prevSettings,
      [key]: value
    }));
  };
  
  // Update widget settings
  const handleWidgetSettingsChange = (index, key, value) => {
    setWidgetSettings(prevWidgets => {
      const updatedWidgets = [...prevWidgets];
      
      if (key.includes('.')) {
        // Handle nested properties
        const [parent, child] = key.split('.');
        updatedWidgets[index] = {
          ...updatedWidgets[index],
          [parent]: {
            ...updatedWidgets[index][parent],
            [child]: value
          }
        };
      } else {
        updatedWidgets[index] = {
          ...updatedWidgets[index],
          [key]: value
        };
      }
      
      return updatedWidgets;
    });
  };
  
  // Update dashboard settings
  const handleDashboardSettingsChange = (key, value) => {
    setDashboardSettings(prevSettings => ({
      ...prevSettings,
      [key]: value
    }));
  };
  
  // Add widget
  const handleAddWidget = () => {
    const newWidget = {
      id: `widget-${Date.now()}`,
      type: 'metrics',
      title: 'New Widget',
      position: { x: 0, y: 0, w: 1, h: 1 },
      settings: {}
    };
    
    setWidgetSettings(prevWidgets => [...prevWidgets, newWidget]);
  };
  
  // Remove widget
  const handleRemoveWidget = (index) => {
    setWidgetSettings(prevWidgets => {
      const updatedWidgets = [...prevWidgets];
      updatedWidgets.splice(index, 1);
      return updatedWidgets;
    });
  };
  
  // Define tabs
  const tabs = [
    {
      id: 'layout',
      label: 'Layout',
      content: (
        <div className="space-y-6">
          <h3 className="text-lg font-medium mb-4 text-textPrimary">Dashboard Layout</h3>
          <div className="space-y-4">
            {/* Columns */}
            <div>
              <label className="block text-sm font-medium text-textSecondary mb-1">Columns</label>
              <select
                className="w-full px-3 py-2 border border-divider rounded-md bg-background text-textPrimary"
                value={layoutSettings.columns || 3}
                onChange={(e) => handleLayoutSettingsChange('columns', parseInt(e.target.value))}
              >
                <option value="1">1</option>
                <option value="2">2</option>
                <option value="3">3</option>
                <option value="4">4</option>
              </select>
            </div>
            
            {/* Row height */}
            <div>
              <label className="block text-sm font-medium text-textSecondary mb-1">Row Height (px)</label>
              <input
                type="number"
                className="w-full px-3 py-2 border border-divider rounded-md bg-background text-textPrimary"
                value={layoutSettings.rowHeight || 200}
                onChange={(e) => handleLayoutSettingsChange('rowHeight', parseInt(e.target.value))}
                min="50"
                max="500"
              />
            </div>
            
            {/* Gap */}
            <div>
              <label className="block text-sm font-medium text-textSecondary mb-1">Gap (px)</label>
              <input
                type="number"
                className="w-full px-3 py-2 border border-divider rounded-md bg-background text-textPrimary"
                value={layoutSettings.gap || 16}
                onChange={(e) => handleLayoutSettingsChange('gap', parseInt(e.target.value))}
                min="0"
                max="50"
              />
            </div>
            
            {/* Compact type */}
            <div>
              <label className="block text-sm font-medium text-textSecondary mb-1">Compact Type</label>
              <select
                className="w-full px-3 py-2 border border-divider rounded-md bg-background text-textPrimary"
                value={layoutSettings.compactType || 'vertical'}
                onChange={(e) => handleLayoutSettingsChange('compactType', e.target.value)}
              >
                <option value="vertical">Vertical</option>
                <option value="horizontal">Horizontal</option>
              </select>
            </div>
          </div>
        </div>
      )
    },
    {
      id: 'widgets',
      label: 'Widgets',
      content: (
        <div className="space-y-6">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium text-textPrimary">Dashboard Widgets</h3>
            <button
              className="px-3 py-1 bg-primary text-primaryContrast rounded-md hover:bg-primaryDark transition-colors duration-200 text-sm"
              onClick={handleAddWidget}
            >
              Add Widget
            </button>
          </div>
          
          {widgetSettings.length === 0 ? (
            <div className="text-center py-8 text-textSecondary">
              No widgets added yet. Click "Add Widget" to add one.
            </div>
          ) : (
            <div className="space-y-6">
              {widgetSettings.map((widget, index) => (
                <div key={widget.id} className="border border-divider rounded-md p-4">
                  <div className="flex justify-between items-center mb-4">
                    <h4 className="font-medium text-textPrimary">{widget.title}</h4>
                    <button
                      className="text-error hover:text-errorDark transition-colors duration-200"
                      onClick={() => handleRemoveWidget(index)}
                    >
                      Remove
                    </button>
                  </div>
                  
                  <div className="space-y-4">
                    {/* Widget title */}
                    <div>
                      <label className="block text-sm font-medium text-textSecondary mb-1">Title</label>
                      <input
                        type="text"
                        className="w-full px-3 py-2 border border-divider rounded-md bg-background text-textPrimary"
                        value={widget.title}
                        onChange={(e) => handleWidgetSettingsChange(index, 'title', e.target.value)}
                      />
                    </div>
                    
                    {/* Widget type */}
                    <div>
                      <label className="block text-sm font-medium text-textSecondary mb-1">Type</label>
                      <select
                        className="w-full px-3 py-2 border border-divider rounded-md bg-background text-textPrimary"
                        value={widget.type}
                        onChange={(e) => handleWidgetSettingsChange(index, 'type', e.target.value)}
                      >
                        <option value="metrics">Metrics</option>
                        <option value="chart">Chart</option>
                        <option value="alerts">Alerts</option>
                        <option value="graph">Graph</option>
                        <option value="heatmap">Heatmap</option>
                        <option value="treemap">Treemap</option>
                        <option value="sankey">Sankey</option>
                      </select>
                    </div>
                    
                    {/* Widget position */}
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-textSecondary mb-1">Width</label>
                        <input
                          type="number"
                          className="w-full px-3 py-2 border border-divider rounded-md bg-background text-textPrimary"
                          value={widget.position?.w || 1}
                          onChange={(e) => handleWidgetSettingsChange(index, 'position.w', parseInt(e.target.value))}
                          min="1"
                          max={layoutSettings.columns || 3}
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-textSecondary mb-1">Height</label>
                        <input
                          type="number"
                          className="w-full px-3 py-2 border border-divider rounded-md bg-background text-textPrimary"
                          value={widget.position?.h || 1}
                          onChange={(e) => handleWidgetSettingsChange(index, 'position.h', parseInt(e.target.value))}
                          min="1"
                          max="4"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )
    },
    {
      id: 'settings',
      label: 'Settings',
      content: (
        <div className="space-y-6">
          <h3 className="text-lg font-medium mb-4 text-textPrimary">Dashboard Settings</h3>
          <div className="space-y-4">
            {/* Dashboard title */}
            <div>
              <label className="block text-sm font-medium text-textSecondary mb-1">Title</label>
              <input
                type="text"
                className="w-full px-3 py-2 border border-divider rounded-md bg-background text-textPrimary"
                value={dashboardSettings.title || ''}
                onChange={(e) => handleDashboardSettingsChange('title', e.target.value)}
              />
            </div>
            
            {/* Dashboard description */}
            <div>
              <label className="block text-sm font-medium text-textSecondary mb-1">Description</label>
              <textarea
                className="w-full px-3 py-2 border border-divider rounded-md bg-background text-textPrimary"
                value={dashboardSettings.description || ''}
                onChange={(e) => handleDashboardSettingsChange('description', e.target.value)}
                rows="3"
              />
            </div>
            
            {/* Refresh interval */}
            <div>
              <label className="block text-sm font-medium text-textSecondary mb-1">Refresh Interval</label>
              <select
                className="w-full px-3 py-2 border border-divider rounded-md bg-background text-textPrimary"
                value={dashboardSettings.refreshInterval || 300}
                onChange={(e) => handleDashboardSettingsChange('refreshInterval', parseInt(e.target.value))}
              >
                <option value="60">1 minute</option>
                <option value="300">5 minutes</option>
                <option value="600">10 minutes</option>
                <option value="1800">30 minutes</option>
                <option value="3600">1 hour</option>
                <option value="0">Never</option>
              </select>
            </div>
            
            {/* Default tab */}
            <div>
              <label className="block text-sm font-medium text-textSecondary mb-1">Default Tab</label>
              <input
                type="text"
                className="w-full px-3 py-2 border border-divider rounded-md bg-background text-textPrimary"
                value={dashboardSettings.defaultTab || 'overview'}
                onChange={(e) => handleDashboardSettingsChange('defaultTab', e.target.value)}
              />
            </div>
          </div>
        </div>
      )
    }
  ];
  
  return (
    <div
      className={`bg-background border border-divider rounded-lg shadow-lg ${className}`}
      style={style}
      data-testid="dashboard-preferences"
    >
      {/* Header */}
      <div className="px-6 py-4 border-b border-divider">
        <h2 className="text-xl font-semibold text-textPrimary">Dashboard Preferences</h2>
        <p className="text-sm text-textSecondary mt-1">
          {dashboardSettings.title || `Dashboard: ${dashboardId}`}
        </p>
      </div>
      
      {/* Content */}
      <div className="p-6">
        <TabPanel
          tabs={tabs}
          defaultTab="layout"
          variant="pills"
          onTabChange={setActiveTab}
        />
      </div>
      
      {/* Footer */}
      <div className="px-6 py-4 border-t border-divider flex justify-end space-x-2">
        <button
          className="px-4 py-2 bg-surface text-textPrimary border border-divider rounded-md hover:bg-actionHover transition-colors duration-200"
          onClick={handleCancel}
          data-testid="cancel-button"
        >
          Cancel
        </button>
        
        <button
          className="px-4 py-2 bg-primary text-primaryContrast rounded-md hover:bg-primaryDark transition-colors duration-200"
          onClick={handleSave}
          data-testid="save-button"
        >
          Save
        </button>
      </div>
    </div>
  );
};

DashboardPreferences.propTypes = {
  dashboardId: PropTypes.string.isRequired,
  onClose: PropTypes.func,
  className: PropTypes.string,
  style: PropTypes.object
};

export default DashboardPreferences;
