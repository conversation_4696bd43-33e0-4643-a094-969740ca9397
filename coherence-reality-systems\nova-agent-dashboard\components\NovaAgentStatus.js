import { motion } from 'framer-motion';
import { 
  CheckCircleIcon, 
  ExclamationTriangleIcon,
  ClockIcon,
  CpuChipIcon 
} from '@heroicons/react/24/outline';

export default function NovaAgentStatus({ agentData, isConnected }) {
  if (!agentData) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gray-800/50 backdrop-blur-xl rounded-lg border border-gray-600 p-6"
      >
        <div className="animate-pulse">
          <div className="h-6 bg-gray-700 rounded mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-700 rounded w-3/4"></div>
            <div className="h-4 bg-gray-700 rounded w-1/2"></div>
          </div>
        </div>
      </motion.div>
    );
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'coherent': return 'text-green-400';
      case 'warning': return 'text-yellow-400';
      case 'error': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'coherent': 
        return <CheckCircleIcon className="w-6 h-6 text-green-400" />;
      case 'warning': 
        return <ExclamationTriangleIcon className="w-6 h-6 text-yellow-400" />;
      default: 
        return <ExclamationTriangleIcon className="w-6 h-6 text-red-400" />;
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-gray-800/50 backdrop-blur-xl rounded-lg border border-gray-600 p-6"
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <CpuChipIcon className="w-6 h-6 text-nova-400" />
          <div>
            <h3 className="text-lg font-semibold text-white">Nova Agent Status</h3>
            <p className="text-sm text-gray-400">System Core</p>
          </div>
        </div>
        {getStatusIcon(agentData.status)}
      </div>

      {/* Status Grid */}
      <div className="grid grid-cols-2 gap-6">
        {/* Status */}
        <div>
          <p className="text-sm text-gray-400 mb-1">Status</p>
          <p className={`text-xl font-bold ${getStatusColor(agentData.status)}`}>
            {agentData.status.toUpperCase()}
          </p>
        </div>

        {/* Version */}
        <div>
          <p className="text-sm text-gray-400 mb-1">Version</p>
          <p className="text-xl font-bold text-white">{agentData.version}</p>
        </div>

        {/* Coherence Level */}
        <div>
          <p className="text-sm text-gray-400 mb-1">Coherence Level</p>
          <p className={`text-xl font-bold ${
            agentData.coherence >= 0.82 ? 'text-green-400' :
            agentData.coherence >= 0.618 ? 'text-yellow-400' : 'text-red-400'
          }`}>
            {(agentData.coherence * 100).toFixed(1)}%
          </p>
        </div>

        {/* Uptime */}
        <div>
          <p className="text-sm text-gray-400 mb-1">Uptime</p>
          <div className="flex items-center space-x-2">
            <ClockIcon className="w-4 h-4 text-gray-400" />
            <p className="text-sm text-white font-medium">{agentData.uptime}</p>
          </div>
        </div>
      </div>

      {/* Timestamp */}
      <div className="mt-6 pt-4 border-t border-gray-700">
        <p className="text-xs text-gray-500">
          Last Update: {new Date(agentData.timestamp).toLocaleTimeString()}
        </p>
      </div>
    </motion.div>
  );
}
