/**
 * NovaFuse Universal API Connector Database Configuration
 * 
 * This module provides MongoDB connection management for the NovaConnect UAC.
 */

const mongoose = require('mongoose');
const logger = require('./logger');

// Default connection options
const DEFAULT_POOL_SIZE = 10;
const DEFAULT_CONNECT_TIMEOUT_MS = 30000;
const DEFAULT_SOCKET_TIMEOUT_MS = 45000;
const DEFAULT_HEARTBEAT_FREQUENCY_MS = 10000;

/**
 * Database connection manager
 */
class DatabaseManager {
  constructor(config = {}) {
    this.config = {
      uri: process.env.MONGODB_URI || 'mongodb://localhost:27017/novafuse-uac',
      dbName: process.env.MONGODB_DB_NAME || 'novafuse-uac',
      poolSize: parseInt(process.env.MONGODB_POOL_SIZE) || DEFAULT_POOL_SIZE,
      connectTimeoutMS: parseInt(process.env.MONGODB_CONNECT_TIMEOUT_MS) || DEFAULT_CONNECT_TIMEOUT_MS,
      socketTimeoutMS: parseInt(process.env.MONGODB_SOCKET_TIMEOUT_MS) || DEFAULT_SOCKET_TIMEOUT_MS,
      heartbeatFrequencyMS: parseInt(process.env.MONGODB_HEARTBEAT_FREQUENCY_MS) || DEFAULT_HEARTBEAT_FREQUENCY_MS,
      retryWrites: process.env.MONGODB_RETRY_WRITES !== 'false',
      retryReads: process.env.MONGODB_RETRY_READS !== 'false',
      ...config
    };
    
    this.connection = null;
    this.isConnected = false;
    this.connectionPromise = null;
  }

  /**
   * Connect to MongoDB
   * @returns {Promise<mongoose.Connection>} MongoDB connection
   */
  async connect() {
    // If already connecting, return the existing promise
    if (this.connectionPromise) {
      return this.connectionPromise;
    }
    
    // If already connected, return the existing connection
    if (this.isConnected && this.connection) {
      return this.connection;
    }
    
    // Create connection promise
    this.connectionPromise = new Promise(async (resolve, reject) => {
      try {
        logger.info('Connecting to MongoDB...', { uri: this.config.uri, dbName: this.config.dbName });
        
        // Configure mongoose
        mongoose.set('strictQuery', true);
        
        // Connect to MongoDB
        await mongoose.connect(this.config.uri, {
          dbName: this.config.dbName,
          maxPoolSize: this.config.poolSize,
          connectTimeoutMS: this.config.connectTimeoutMS,
          socketTimeoutMS: this.config.socketTimeoutMS,
          heartbeatFrequencyMS: this.config.heartbeatFrequencyMS,
          retryWrites: this.config.retryWrites,
          retryReads: this.config.retryReads
        });
        
        this.connection = mongoose.connection;
        this.isConnected = true;
        
        // Set up connection event handlers
        this.connection.on('error', (err) => {
          logger.error('MongoDB connection error:', { error: err.message });
          this.isConnected = false;
        });
        
        this.connection.on('disconnected', () => {
          logger.warn('MongoDB disconnected');
          this.isConnected = false;
        });
        
        this.connection.on('reconnected', () => {
          logger.info('MongoDB reconnected');
          this.isConnected = true;
        });
        
        logger.info('Connected to MongoDB successfully');
        resolve(this.connection);
      } catch (error) {
        logger.error('Failed to connect to MongoDB:', { error: error.message });
        this.isConnected = false;
        this.connectionPromise = null;
        reject(error);
      }
    });
    
    return this.connectionPromise;
  }

  /**
   * Disconnect from MongoDB
   * @returns {Promise<void>}
   */
  async disconnect() {
    if (!this.isConnected || !this.connection) {
      logger.warn('Not connected to MongoDB, nothing to disconnect');
      return;
    }
    
    try {
      logger.info('Disconnecting from MongoDB...');
      await mongoose.disconnect();
      this.isConnected = false;
      this.connection = null;
      this.connectionPromise = null;
      logger.info('Disconnected from MongoDB successfully');
    } catch (error) {
      logger.error('Failed to disconnect from MongoDB:', { error: error.message });
      throw error;
    }
  }

  /**
   * Get MongoDB connection
   * @returns {Promise<mongoose.Connection>} MongoDB connection
   */
  async getConnection() {
    if (!this.isConnected || !this.connection) {
      return this.connect();
    }
    
    return this.connection;
  }

  /**
   * Check if connected to MongoDB
   * @returns {boolean} Connection status
   */
  isConnected() {
    return this.isConnected;
  }

  /**
   * Create indexes for collections
   * @returns {Promise<void>}
   */
  async createIndexes() {
    if (!this.isConnected || !this.connection) {
      await this.connect();
    }
    
    logger.info('Creating indexes...');
    
    // In a real implementation, this would create indexes for all collections
    // For now, we'll just log a message
    
    logger.info('Indexes created successfully');
  }
}

// Create singleton instance
const databaseManager = new DatabaseManager();

module.exports = databaseManager;
