{"version": 3, "names": ["ValidationError", "AuthenticationError", "AuthorizationError", "NotFoundError", "ConflictError", "RateLimitError", "ServerError", "require", "<PERSON><PERSON><PERSON><PERSON>", "err", "req", "res", "next", "console", "error", "status", "json", "message", "retryAfter", "module", "exports"], "sources": ["errorHandler.js"], "sourcesContent": ["/**\n * Error Handler Middleware\n * \n * This middleware handles errors and sends appropriate responses.\n */\n\nconst { \n  ValidationError, \n  AuthenticationError, \n  AuthorizationError, \n  NotFoundError, \n  ConflictError, \n  RateLimitError, \n  ServerError \n} = require('../utils/errors');\n\nconst errorHandler = (err, req, res, next) => {\n  console.error('Error:', err);\n  \n  // Handle specific error types\n  if (err instanceof ValidationError) {\n    return res.status(400).json({\n      error: 'Bad Request',\n      message: err.message\n    });\n  }\n  \n  if (err instanceof AuthenticationError) {\n    return res.status(401).json({\n      error: 'Unauthorized',\n      message: err.message\n    });\n  }\n  \n  if (err instanceof AuthorizationError) {\n    return res.status(403).json({\n      error: 'Forbidden',\n      message: err.message\n    });\n  }\n  \n  if (err instanceof NotFoundError) {\n    return res.status(404).json({\n      error: 'Not Found',\n      message: err.message\n    });\n  }\n  \n  if (err instanceof ConflictError) {\n    return res.status(409).json({\n      error: 'Conflict',\n      message: err.message\n    });\n  }\n  \n  if (err instanceof RateLimitError) {\n    return res.status(429).json({\n      error: 'Too Many Requests',\n      message: err.message,\n      retryAfter: err.retryAfter\n    });\n  }\n  \n  if (err instanceof ServerError) {\n    return res.status(500).json({\n      error: 'Internal Server Error',\n      message: err.message\n    });\n  }\n  \n  // Handle other errors\n  return res.status(500).json({\n    error: 'Internal Server Error',\n    message: 'An unexpected error occurred'\n  });\n};\n\nmodule.exports = errorHandler;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,MAAM;EACJA,eAAe;EACfC,mBAAmB;EACnBC,kBAAkB;EAClBC,aAAa;EACbC,aAAa;EACbC,cAAc;EACdC;AACF,CAAC,GAAGC,OAAO,CAAC,iBAAiB,CAAC;AAE9B,MAAMC,YAAY,GAAGA,CAACC,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,KAAK;EAC5CC,OAAO,CAACC,KAAK,CAAC,QAAQ,EAAEL,GAAG,CAAC;;EAE5B;EACA,IAAIA,GAAG,YAAYT,eAAe,EAAE;IAClC,OAAOW,GAAG,CAACI,MAAM,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC;MAC1BF,KAAK,EAAE,aAAa;MACpBG,OAAO,EAAER,GAAG,CAACQ;IACf,CAAC,CAAC;EACJ;EAEA,IAAIR,GAAG,YAAYR,mBAAmB,EAAE;IACtC,OAAOU,GAAG,CAACI,MAAM,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC;MAC1BF,KAAK,EAAE,cAAc;MACrBG,OAAO,EAAER,GAAG,CAACQ;IACf,CAAC,CAAC;EACJ;EAEA,IAAIR,GAAG,YAAYP,kBAAkB,EAAE;IACrC,OAAOS,GAAG,CAACI,MAAM,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC;MAC1BF,KAAK,EAAE,WAAW;MAClBG,OAAO,EAAER,GAAG,CAACQ;IACf,CAAC,CAAC;EACJ;EAEA,IAAIR,GAAG,YAAYN,aAAa,EAAE;IAChC,OAAOQ,GAAG,CAACI,MAAM,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC;MAC1BF,KAAK,EAAE,WAAW;MAClBG,OAAO,EAAER,GAAG,CAACQ;IACf,CAAC,CAAC;EACJ;EAEA,IAAIR,GAAG,YAAYL,aAAa,EAAE;IAChC,OAAOO,GAAG,CAACI,MAAM,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC;MAC1BF,KAAK,EAAE,UAAU;MACjBG,OAAO,EAAER,GAAG,CAACQ;IACf,CAAC,CAAC;EACJ;EAEA,IAAIR,GAAG,YAAYJ,cAAc,EAAE;IACjC,OAAOM,GAAG,CAACI,MAAM,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC;MAC1BF,KAAK,EAAE,mBAAmB;MAC1BG,OAAO,EAAER,GAAG,CAACQ,OAAO;MACpBC,UAAU,EAAET,GAAG,CAACS;IAClB,CAAC,CAAC;EACJ;EAEA,IAAIT,GAAG,YAAYH,WAAW,EAAE;IAC9B,OAAOK,GAAG,CAACI,MAAM,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC;MAC1BF,KAAK,EAAE,uBAAuB;MAC9BG,OAAO,EAAER,GAAG,CAACQ;IACf,CAAC,CAAC;EACJ;;EAEA;EACA,OAAON,GAAG,CAACI,MAAM,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC;IAC1BF,KAAK,EAAE,uBAAuB;IAC9BG,OAAO,EAAE;EACX,CAAC,CAAC;AACJ,CAAC;AAEDE,MAAM,CAACC,OAAO,GAAGZ,YAAY", "ignoreList": []}