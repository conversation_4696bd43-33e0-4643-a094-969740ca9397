# ConsciousNovaFold Analysis Report

## Sequence Information

* Sequence ID: seq_20250625_182740
* Sequence: ACDEFGHIKLMNPQRSTVWY

## Consciousness Metrics

* PSI Score: 0.660
* Fibonacci Analysis: {'residue_distances': [{'start': 3, 'end': 6, 'score': 0.9532054526266053, 'ratio': 1.6259772047616878, 'golden_ratio_diff': 0.004909177475270417}, {'start': 9, 'end': 13, 'score': 0.8186318544632966, 'ratio': 1.6538815828002533, 'golden_ratio_diff': 0.022155031538030015}, {'start': 17, 'end': 19, 'score': 0.7797090855668583, 'ratio': 1.6637482421251115, 'golden_ratio_diff': 0.02825296235620847}, {'start': 4, 'end': 6, 'score': 0.7038915826025195, 'ratio': 1.5499676138214402, 'golden_ratio_diff': 0.04206733319677869}, {'start': 3, 'end': 5, 'score': 0.658389880045883, 'ratio': 1.7019867957019352, 'golden_ratio_diff': 0.051885688147319385}, {'start': 9, 'end': 14, 'score': 0.6252664217648041, 'ratio': 1.521062258747338, 'golden_ratio_diff': 0.05993182508945814}, {'start': 8, 'end': 11, 'score': 0.6225238435825914, 'ratio': 1.7161457731132206, 'golden_ratio_diff': 0.06063641743343573}, {'start': 7, 'end': 11, 'score': 0.5805147921212757, 'ratio': 1.5011133980367137, 'golden_ratio_diff': 0.07226089904546126}, {'start': 0, 'end': 3, 'score': 0.5327637240322977, 'ratio': 1.4761316517118654, 'golden_ratio_diff': 0.08770046737254528}, {'start': 9, 'end': 15, 'score': 0.5073671907752434, 'ratio': 1.4609295003652485, 'golden_ratio_diff': 0.09709591360687454}], 'torsion_angles': [{'start': 10, 'end': 19, 'score': 0.8441546365288902, 'ratio': 1.5881623178292428, 'golden_ratio_diff': 0.018461707929714875}, {'start': 9, 'end': 17, 'score': 0.7645249137008858, 'ratio': 1.667869734623946, 'golden_ratio_diff': 0.03080018480486588}, {'start': 9, 'end': 18, 'score': 0.7049329151104746, 'ratio': 1.6857607982205212, 'golden_ratio_diff': 0.0418574702024353}, {'start': 10, 'end': 15, 'score': 0.5612694383843193, 'ratio': 1.7445117393719451, 'golden_ratio_diff': 0.07816754870506021}, {'start': 11, 'end': 16, 'score': 0.5337255659003021, 'ratio': 1.7593890091311675, 'golden_ratio_diff': 0.08736220707606056}], 'overall_score': 0.6793914198137497}
* Fibonacci Alignment: {'closest_fibonacci': 21, 'difference': 1, 'ratio': 0.9523809523809523, 'alignment_score': 0.9545454545454545}
* Trinity Validation: {'ners': np.float64(0.6658174259441249), 'nepi': 0.5389999999999999, 'nefc': 0.694, 'overall': np.float64(0.6362269703776499), 'passed': False}
* Trinity Report: {'scores': {'ners': np.float64(0.6658174259441249), 'nepi': 0.5389999999999999, 'nefc': 0.694, 'overall': np.float64(0.6362269703776499), 'passed': False}, 'thresholds': {'ners': 0.7, 'nepi': 0.5, 'nefc': 0.6}, 'weights': {'ners': 0.4, 'nepi': 0.3, 'nefc': 0.3}, 'validation': {'ners': {'score': np.float64(0.6658174259441249), 'threshold': 0.7, 'passed': np.False_, 'description': 'Neural-Emotional Resonance Score: Measures structural harmony and consciousness resonance.'}, 'nepi': {'score': 0.5389999999999999, 'threshold': 0.5, 'passed': True, 'description': 'Neural-Emotional Potential Index: Evaluates functional potential and adaptability.'}, 'nefc': {'score': 0.694, 'threshold': 0.6, 'passed': True, 'description': 'Neural-Emotional Field Coherence: Assesses field coherence and quantum effects.'}, 'overall': {'score': np.float64(0.6362269703776499), 'passed': False, 'description': 'Overall validation status based on all metrics.'}}}

## Structure Information

* Structure information not available
