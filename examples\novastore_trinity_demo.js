/**
 * NovaStore Trinity CSDE Demo
 * 
 * This script demonstrates the integration between NovaStore and Trinity CSDE.
 * It shows:
 * 1. Component verification using Trinity CSDE
 * 2. Revenue sharing using Trinity CSDE
 * 3. 18/82 principle implementation
 */

const NovaStore = require('../src/novastore');
const { TrinityCSDEEngine } = require('../src/csde');
const chalk = require('chalk');

// Create a logger
const logger = {
  info: (message) => console.log(chalk.blue(`[INFO] ${message}`)),
  error: (message) => console.log(chalk.red(`[ERROR] ${message}`)),
  warn: (message) => console.log(chalk.yellow(`[WARN] ${message}`)),
  debug: (message) => console.log(chalk.gray(`[DEBUG] ${message}`))
};

// Sample data
const componentData = {
  id: 'advanced-threat-detection',
  name: 'Advanced Threat Detection',
  version: '1.0.0',
  description: 'AI-powered threat detection module',
  author: 'NovaFuse Partner',
  interfaces: ['wasm', 'grpc'],
  category: 'security',
  security: {
    detectionCapability: 0.85,
    systemRadius: 150,
    threatSurface: 175
  },
  compliance: {
    score: 0.9
  },
  audit: {
    frequency: 4
  },
  policies: [
    { id: 'POL-001', name: 'Access Control Policy', effectiveness: 0.9 },
    { id: 'POL-002', name: 'Data Protection Policy', effectiveness: 0.8 },
    { id: 'POL-003', name: 'Incident Response Policy', effectiveness: 0.85 },
    { id: 'POL-004', name: 'Risk Management Policy', effectiveness: 0.75 },
    { id: 'POL-005', name: 'Governance Policy', effectiveness: 0.9 }
  ],
  threats: {
    severity: 0.8,
    confidence: 0.7,
    items: {
      malware: 0.9,
      phishing: 0.8,
      ddos: 0.7,
      insider: 0.6
    }
  },
  performance: {
    responseTime: 50
  }
};

const transactionData = {
  id: 'transaction-1',
  amount: 1000,
  component: 'advanced-threat-detection',
  customer: 'customer-1',
  timestamp: new Date().toISOString()
};

/**
 * Demo component verification
 * @param {Object} novaStore - Initialized NovaStore
 */
async function demoComponentVerification(novaStore) {
  logger.info('Demo 1: Component Verification using Trinity CSDE');
  
  try {
    // Verify component
    const result = await novaStore.verifyComponent(componentData);
    
    // Print results
    logger.info(`Component: ${result.component.name} (${result.component.id})`);
    logger.info(`Status: ${result.status}`);
    logger.info(`Message: ${result.message}`);
    logger.info('Trinity Scores:');
    logger.info(`  Father Score (πG): ${result.trinityScores.fatherScore.toFixed(4)}`);
    logger.info(`  Son Score (ϕD): ${result.trinityScores.sonScore.toFixed(4)}`);
    logger.info(`  Spirit Score ((ℏ + c^-1)R): ${result.trinityScores.spiritScore.toFixed(4)}`);
    logger.info(`  Total Score: ${result.trinityScores.totalScore.toFixed(4)}`);
    logger.info(`Verified At: ${result.verifiedAt}`);
  } catch (error) {
    logger.error(`Error verifying component: ${error.message}`);
  }
}

/**
 * Demo revenue sharing
 * @param {Object} novaStore - Initialized NovaStore
 */
async function demoRevenueSharing(novaStore) {
  logger.info('Demo 2: Revenue Sharing using Trinity CSDE');
  
  try {
    // Calculate revenue sharing
    const result = await novaStore.calculateRevenueSharing(transactionData);
    
    // Print results
    logger.info(`Transaction: ${result.transaction.id}`);
    logger.info(`Amount: $${result.transaction.amount.toFixed(2)}`);
    logger.info(`Platform Fee (18%): $${result.platformFee.toFixed(2)}`);
    logger.info(`Developer Share (82%): $${result.developerShare.toFixed(2)}`);
    logger.info(`Revenue Share: ${(result.revenueShare * 100).toFixed(0)}%`);
    logger.info(`Calculated At: ${result.calculatedAt}`);
  } catch (error) {
    logger.error(`Error calculating revenue sharing: ${error.message}`);
  }
}

/**
 * Demo transaction fee
 * @param {Object} novaStore - Initialized NovaStore
 */
function demoTransactionFee(novaStore) {
  logger.info('Demo 3: Transaction Fee');
  
  // The transaction fee is $0.0018 per enhancement call
  const transactionFee = 0.0018;
  
  // Calculate fees for different numbers of calls
  const calls = [1, 10, 100, 1000, 10000, 100000, 1000000];
  
  logger.info(`Transaction Fee: $${transactionFee.toFixed(4)} per enhancement call`);
  logger.info('Fee for different numbers of calls:');
  
  for (const numCalls of calls) {
    const fee = numCalls * transactionFee;
    logger.info(`  ${numCalls.toLocaleString()} calls: $${fee.toFixed(2)}`);
  }
}

/**
 * Main function
 */
async function main() {
  logger.info('NovaStore Trinity CSDE Demo');
  
  try {
    // Initialize NovaStore with Trinity CSDE
    const novaStore = NovaStore.initialize({
      logger,
      csdeIntegration: {
        enableTrinity: true,
        enableOriginal: false
      }
    });
    
    // Start NovaStore
    await novaStore.start();
    
    // Demo 1: Component Verification
    await demoComponentVerification(novaStore);
    
    // Demo 2: Revenue Sharing
    await demoRevenueSharing(novaStore);
    
    // Demo 3: Transaction Fee
    demoTransactionFee(novaStore);
    
    // Stop NovaStore
    await novaStore.stop();
    
    logger.info('NovaStore Trinity CSDE Demo completed successfully');
  } catch (error) {
    logger.error(`Error running NovaStore Trinity CSDE Demo: ${error.message}`);
  }
}

// Run the demo
main().catch(error => {
  console.error('Unhandled error:', error);
  process.exit(1);
});
