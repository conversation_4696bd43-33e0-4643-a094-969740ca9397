/**
 * HealthcareIntegration.js
 * 
 * This module integrates all healthcare components for NovaDNA.
 * It provides a unified interface for accessing healthcare data in emergency scenarios.
 */

const NovaConnectAdapter = require('../NovaConnectAdapter');
const EmergencyDataPipeline = require('./EmergencyDataPipeline');
const ProviderConnector = require('./ProviderConnector');
const DataSourcePrioritization = require('./DataSourcePrioritization');
const SecureTemporaryCache = require('./SecureTemporaryCache');
const { v4: uuidv4 } = require('uuid');

/**
 * HealthcareIntegration class for integrating healthcare components
 */
class HealthcareIntegration {
  constructor(options = {}) {
    // Initialize NovaConnectAdapter
    this.novaConnectAdapter = options.novaConnectAdapter || new NovaConnectAdapter(options);
    
    // Initialize EmergencyDataPipeline
    this.emergencyDataPipeline = options.emergencyDataPipeline || new EmergencyDataPipeline({
      novaConnectAdapter: this.novaConnectAdapter,
      cacheEnabled: options.cacheEnabled !== false
    });
    
    // Initialize ProviderConnector
    this.providerConnector = options.providerConnector || new ProviderConnector({
      novaConnectAdapter: this.novaConnectAdapter
    });
    
    // Initialize DataSourcePrioritization
    this.dataSourcePrioritization = options.dataSourcePrioritization || new DataSourcePrioritization(options);
    
    // Initialize SecureTemporaryCache
    this.secureTemporaryCache = options.secureTemporaryCache || new SecureTemporaryCache({
      enabled: options.cacheEnabled !== false,
      encryptionEnabled: options.encryptionEnabled !== false
    });
    
    // Track active emergency sessions
    this.activeSessions = new Map();
  }

  /**
   * Initialize healthcare integration
   * @param {Object} config - Configuration options
   * @returns {Promise<Object>} - Initialization result
   */
  async initialize(config = {}) {
    try {
      // Connect to providers if credentials are provided
      if (config.providers) {
        for (const [provider, credentials] of Object.entries(config.providers)) {
          try {
            if (provider === 'Epic' && credentials) {
              await this.providerConnector.connectToEpic(credentials);
            } else if (provider === 'Cerner' && credentials) {
              await this.providerConnector.connectToCerner(credentials);
            }
          } catch (error) {
            console.warn(`Failed to connect to ${provider}:`, error.message);
            // Continue with other providers
          }
        }
      }
      
      // Set custom priorities if provided
      if (config.priorities) {
        for (const [contextType, priorities] of Object.entries(config.priorities)) {
          this.dataSourcePrioritization.setCustomPriorities(contextType, priorities);
        }
      }
      
      return {
        initialized: true,
        connectedProviders: Object.keys(this.providerConnector.getConnectionStatus()).filter(
          provider => this.providerConnector.getConnectionStatus()[provider].status === 'CONNECTED'
        )
      };
    } catch (error) {
      throw new Error(`Failed to initialize healthcare integration: ${error.message}`);
    }
  }

  /**
   * Start an emergency session
   * @param {Object} context - The emergency context
   * @returns {Object} - The session information
   */
  startEmergencySession(context = {}) {
    // Create a transfer session
    const session = this.emergencyDataPipeline.createTransferSession(context);
    
    // Create emergency session
    const emergencySession = {
      sessionId: session.sessionId,
      timestamp: session.timestamp,
      expiresAt: session.expiresAt,
      context: session.context,
      status: 'ACTIVE',
      patientData: null,
      dataSourcesUsed: [],
      accessLog: []
    };
    
    // Store session
    this.activeSessions.set(session.sessionId, emergencySession);
    
    return {
      sessionId: session.sessionId,
      timestamp: session.timestamp,
      expiresAt: session.expiresAt,
      context: session.context
    };
  }

  /**
   * Get emergency medical data
   * @param {String} patientId - The patient ID
   * @param {String} sessionId - The emergency session ID
   * @returns {Promise<Object>} - The emergency medical data
   */
  async getEmergencyMedicalData(patientId, sessionId) {
    // Validate session
    const session = this.activeSessions.get(sessionId);
    if (!session || session.status !== 'ACTIVE') {
      throw new Error('Invalid or expired emergency session');
    }
    
    // Check cache
    const cacheKey = `${patientId}:${sessionId}`;
    const cachedData = this.secureTemporaryCache.retrieve(cacheKey, {
      sessionId,
      patientId,
      action: 'RETRIEVE'
    });
    
    if (cachedData) {
      // Log cache hit
      this._logAccess(sessionId, 'CACHE_HIT', {
        patientId,
        cacheKey
      });
      
      return cachedData;
    }
    
    try {
      // Fetch emergency data
      const emergencyData = await this.emergencyDataPipeline.fetchEmergencyData(patientId, sessionId);
      
      // Store in cache
      this.secureTemporaryCache.store(cacheKey, emergencyData, {
        ttl: 300000, // 5 minutes
        context: {
          sessionId,
          patientId,
          emergencyType: session.context.emergencyType
        }
      });
      
      // Update session
      session.patientData = {
        patientId,
        fetchedAt: new Date().toISOString()
      };
      
      // Log data fetch
      this._logAccess(sessionId, 'DATA_FETCH', {
        patientId,
        dataSize: JSON.stringify(emergencyData).length
      });
      
      return emergencyData;
    } catch (error) {
      // Log error
      this._logAccess(sessionId, 'ERROR', {
        patientId,
        error: error.message
      });
      
      throw error;
    }
  }

  /**
   * End an emergency session
   * @param {String} sessionId - The session ID
   * @returns {Boolean} - Whether the session was ended successfully
   */
  endEmergencySession(sessionId) {
    // Validate session
    const session = this.activeSessions.get(sessionId);
    if (!session) {
      return false;
    }
    
    // Close data pipeline session
    this.emergencyDataPipeline.closeSession(sessionId);
    
    // Update session status
    session.status = 'CLOSED';
    session.closedAt = new Date().toISOString();
    
    // Clear cache for this session
    const cacheKeys = this.secureTemporaryCache.getAccessLogs({
      action: 'STORE',
      limit: 1000
    })
      .filter(log => log.context && log.context.sessionId === sessionId)
      .map(log => log.key);
    
    for (const key of cacheKeys) {
      this.secureTemporaryCache.remove(key, {
        sessionId,
        reason: 'SESSION_CLOSED'
      });
    }
    
    // Log session end
    this._logAccess(sessionId, 'SESSION_END', {
      duration: new Date().getTime() - new Date(session.timestamp).getTime()
    });
    
    return true;
  }

  /**
   * Get active emergency sessions
   * @returns {Array} - The active sessions
   */
  getActiveEmergencySessions() {
    const sessions = [];
    
    for (const [sessionId, session] of this.activeSessions.entries()) {
      if (session.status === 'ACTIVE') {
        sessions.push({
          sessionId,
          timestamp: session.timestamp,
          expiresAt: session.expiresAt,
          context: session.context,
          patientData: session.patientData
        });
      }
    }
    
    return sessions;
  }

  /**
   * Get healthcare integration status
   * @returns {Object} - The status information
   */
  getStatus() {
    return {
      providers: this.providerConnector.getConnectionStatus(),
      activeSessions: this.getActiveEmergencySessions().length,
      cacheStats: this.secureTemporaryCache.getStats(),
      dataPipelineStats: {
        activeSessions: this.emergencyDataPipeline.getActiveSessions().length
      }
    };
  }

  /**
   * Log access to the integration
   * @param {String} sessionId - The session ID
   * @param {String} action - The action performed
   * @param {Object} details - Additional details
   * @private
   */
  _logAccess(sessionId, action, details = {}) {
    const session = this.activeSessions.get(sessionId);
    
    if (!session) {
      return;
    }
    
    // Create log entry
    const logEntry = {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      action,
      details
    };
    
    // Add to session log
    session.accessLog.push(logEntry);
  }
}

module.exports = HealthcareIntegration;
