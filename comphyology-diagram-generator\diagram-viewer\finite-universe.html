﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Comphyology Diagram Viewer</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            margin-bottom: 20px;
        }
        .diagram-container {
            margin-top: 20px;
            border: 1px solid #eee;
            padding: 20px;
            border-radius: 8px;
        }
        .navigation {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
        }
        button {
            padding: 10px 20px;
            background-color: #0070f3;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #0060df;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>2. Finite Universe Paradigm</h1>
        
        <div class="diagram-container" id="diagram-container">
            <div style="text-align: center; margin-bottom: 20px;">
                <h2 style="color: #4fc3f7; font-size: 2em;">📐 Sacred Geometry & Finite Universe</h2>
                <div style="font-size: 1.2em; color: #0070f3; margin: 20px 0;">
                    Golden Ratio Spirals & Pentagonal Symmetry
                </div>
            </div>

            <svg width="100%" height="600" viewBox="0 0 1000 600" xmlns="http://www.w3.org/2000/svg" style="background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);">
                <defs>
                    <!-- Golden ratio gradient -->
                    <linearGradient id="goldenGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#ffd700;stop-opacity:0.9"/>
                        <stop offset="50%" style="stop-color:#ff9800;stop-opacity:0.7"/>
                        <stop offset="100%" style="stop-color:#f44336;stop-opacity:0.5"/>
                    </linearGradient>

                    <!-- Sacred geometry gradient -->
                    <radialGradient id="sacredGrad" cx="50%" cy="50%" r="50%">
                        <stop offset="0%" style="stop-color:#9c27b0;stop-opacity:0.8"/>
                        <stop offset="50%" style="stop-color:#673ab7;stop-opacity:0.6"/>
                        <stop offset="100%" style="stop-color:#3f51b5;stop-opacity:0.4"/>
                    </radialGradient>

                    <!-- Fibonacci gradient -->
                    <linearGradient id="fibGrad" x1="0%" y1="0%" x2="100%" y2="0%">
                        <stop offset="0%" style="stop-color:#4fc3f7;stop-opacity:0.8"/>
                        <stop offset="100%" style="stop-color:#29b6f6;stop-opacity:0.4"/>
                    </linearGradient>

                    <!-- Glow filter -->
                    <filter id="sacredGlow" x="-50%" y="-50%" width="200%" height="200%">
                        <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
                        <feMerge>
                            <feMergeNode in="coloredBlur"/>
                            <feMergeNode in="SourceGraphic"/>
                        </feMerge>
                    </filter>
                </defs>

                <!-- Central Pentagon -->
                <g id="central-pentagon">
                    <polygon points="500,200 576,238 553,324 447,324 424,238"
                             fill="url(#sacredGrad)"
                             stroke="#9c27b0"
                             stroke-width="2"
                             filter="url(#sacredGlow)">
                        <animateTransform attributeName="transform"
                                          type="rotate"
                                          values="0 500 300;360 500 300"
                                          dur="20s"
                                          repeatCount="indefinite"/>
                    </polygon>
                    <text x="500" y="280" text-anchor="middle" fill="#e1bee7" font-size="14" font-weight="bold">φ = 1.618</text>
                </g>

                <!-- Golden Ratio Spiral -->
                <g id="golden-spiral">
                    <path d="M 500 300 Q 550 250 600 300 Q 650 350 600 400 Q 550 450 500 400 Q 450 350 500 300"
                          fill="none"
                          stroke="url(#goldenGrad)"
                          stroke-width="3"
                          filter="url(#sacredGlow)">
                        <animate attributeName="stroke-width" values="3;5;3" dur="3s" repeatCount="indefinite"/>
                    </path>

                    <!-- Extended spiral -->
                    <path d="M 600 300 Q 700 200 800 300 Q 900 400 800 500 Q 700 600 600 500 Q 500 400 600 300"
                          fill="none"
                          stroke="url(#goldenGrad)"
                          stroke-width="2"
                          opacity="0.7">
                        <animate attributeName="opacity" values="0.7;1;0.7" dur="4s" repeatCount="indefinite"/>
                    </path>

                    <!-- Inner spiral -->
                    <path d="M 500 300 Q 475 275 450 300 Q 425 325 450 350 Q 475 375 500 350 Q 525 325 500 300"
                          fill="none"
                          stroke="url(#goldenGrad)"
                          stroke-width="2"
                          opacity="0.8">
                        <animate attributeName="opacity" values="0.8;1;0.8" dur="2s" repeatCount="indefinite"/>
                    </path>
                </g>

                <!-- Fibonacci Sequence Visualization -->
                <g id="fibonacci-sequence">
                    <!-- Fibonacci squares -->
                    <rect x="100" y="100" width="21" height="21" fill="url(#fibGrad)" opacity="0.6"/>
                    <text x="110" y="115" text-anchor="middle" fill="#4fc3f7" font-size="10">1</text>

                    <rect x="121" y="100" width="21" height="21" fill="url(#fibGrad)" opacity="0.6"/>
                    <text x="131" y="115" text-anchor="middle" fill="#4fc3f7" font-size="10">1</text>

                    <rect x="142" y="100" width="42" height="42" fill="url(#fibGrad)" opacity="0.6"/>
                    <text x="163" y="125" text-anchor="middle" fill="#4fc3f7" font-size="10">2</text>

                    <rect x="184" y="100" width="63" height="63" fill="url(#fibGrad)" opacity="0.6"/>
                    <text x="215" y="135" text-anchor="middle" fill="#4fc3f7" font-size="10">3</text>

                    <rect x="247" y="100" width="105" height="105" fill="url(#fibGrad)" opacity="0.6"/>
                    <text x="299" y="155" text-anchor="middle" fill="#4fc3f7" font-size="12">5</text>

                    <!-- Fibonacci spiral through squares -->
                    <path d="M 110 121 Q 131 121 131 110 Q 131 100 142 100 Q 184 100 184 142 Q 184 205 247 205 Q 352 205 352 100"
                          fill="none"
                          stroke="#ffd700"
                          stroke-width="2"
                          filter="url(#sacredGlow)">
                        <animate attributeName="stroke-width" values="2;4;2" dur="3s" repeatCount="indefinite"/>
                    </path>
                </g>

                <!-- Sacred Geometry Patterns -->
                <g id="sacred-patterns">
                    <!-- Flower of Life pattern -->
                    <g id="flower-of-life" transform="translate(750, 150)">
                        <circle cx="0" cy="0" r="30" fill="none" stroke="#9c27b0" stroke-width="1" opacity="0.6"/>
                        <circle cx="26" cy="15" r="30" fill="none" stroke="#9c27b0" stroke-width="1" opacity="0.6"/>
                        <circle cx="26" cy="-15" r="30" fill="none" stroke="#9c27b0" stroke-width="1" opacity="0.6"/>
                        <circle cx="0" cy="30" r="30" fill="none" stroke="#9c27b0" stroke-width="1" opacity="0.6"/>
                        <circle cx="0" cy="-30" r="30" fill="none" stroke="#9c27b0" stroke-width="1" opacity="0.6"/>
                        <circle cx="-26" cy="15" r="30" fill="none" stroke="#9c27b0" stroke-width="1" opacity="0.6"/>
                        <circle cx="-26" cy="-15" r="30" fill="none" stroke="#9c27b0" stroke-width="1" opacity="0.6"/>

                        <animateTransform attributeName="transform"
                                          type="rotate"
                                          values="0 750 150;360 750 150"
                                          dur="15s"
                                          repeatCount="indefinite"/>
                    </g>

                    <!-- Vesica Piscis -->
                    <g id="vesica-piscis" transform="translate(150, 400)">
                        <circle cx="-15" cy="0" r="40" fill="none" stroke="#4fc3f7" stroke-width="2" opacity="0.7"/>
                        <circle cx="15" cy="0" r="40" fill="none" stroke="#4fc3f7" stroke-width="2" opacity="0.7"/>
                        <path d="M -15 -34.6 Q 0 -20 15 -34.6 Q 0 -50 -15 -34.6"
                              fill="url(#sacredGrad)" opacity="0.3"/>
                        <path d="M -15 34.6 Q 0 20 15 34.6 Q 0 50 -15 34.6"
                              fill="url(#sacredGrad)" opacity="0.3"/>

                        <animate attributeName="opacity" values="0.7;1;0.7" dur="3s" repeatCount="indefinite"/>
                    </g>
                </g>

                <!-- Platonic Solids Projection -->
                <g id="platonic-solids">
                    <!-- Tetrahedron -->
                    <g transform="translate(800, 400)">
                        <polygon points="0,-30 -26,15 26,15"
                                 fill="none"
                                 stroke="#ffd700"
                                 stroke-width="2"
                                 filter="url(#sacredGlow)"/>
                        <text x="0" y="35" text-anchor="middle" fill="#ffd700" font-size="10">Tetrahedron</text>

                        <animateTransform attributeName="transform"
                                          type="rotate"
                                          values="0 800 400;360 800 400"
                                          dur="8s"
                                          repeatCount="indefinite"/>
                    </g>

                    <!-- Cube -->
                    <g transform="translate(850, 500)">
                        <rect x="-20" y="-20" width="40" height="40"
                              fill="none"
                              stroke="#ff9800"
                              stroke-width="2"/>
                        <rect x="-15" y="-15" width="40" height="40"
                              fill="none"
                              stroke="#ff9800"
                              stroke-width="1"
                              opacity="0.6"/>
                        <text x="0" y="35" text-anchor="middle" fill="#ff9800" font-size="10">Cube</text>

                        <animateTransform attributeName="transform"
                                          type="rotate"
                                          values="0 850 500;360 850 500"
                                          dur="6s"
                                          repeatCount="indefinite"/>
                    </g>
                </g>

                <!-- Mathematical Constants -->
                <g id="constants">
                    <text x="50" y="50" fill="#ffd700" font-size="16" font-weight="bold">φ = (1 + √5) / 2 ≈ 1.618</text>
                    <text x="50" y="70" fill="#4fc3f7" font-size="14">π ≈ 3.14159</text>
                    <text x="50" y="90" fill="#9c27b0" font-size="14">e ≈ 2.71828</text>
                </g>

                <!-- Finite Universe Boundary -->
                <g id="universe-boundary">
                    <circle cx="500" cy="300" r="280"
                            fill="none"
                            stroke="#ff5722"
                            stroke-width="3"
                            stroke-dasharray="15,10"
                            opacity="0.6">
                        <animate attributeName="opacity" values="0.6;1;0.6" dur="4s" repeatCount="indefinite"/>
                    </circle>
                    <text x="500" y="50" text-anchor="middle" fill="#ff5722" font-size="14" font-weight="bold">Finite Universe Boundary</text>
                </g>

                <!-- Sacred Ratios -->
                <g id="sacred-ratios">
                    <text x="600" y="550" fill="#ffd700" font-size="12">Golden Ratio: 1:1.618</text>
                    <text x="600" y="570" fill="#4fc3f7" font-size="12">Sacred Proportion: 3:4:5</text>
                    <text x="600" y="590" fill="#9c27b0" font-size="12">Pentagonal Symmetry: 72°</text>
                </g>
            </svg>

            <div style="margin-top: 20px; padding: 15px; background: rgba(255, 215, 0, 0.1); border-radius: 8px; border-left: 4px solid #ffd700;">
                <p><strong>Sacred Geometry & Finite Universe:</strong> Visualization of golden ratio spirals, Fibonacci sequences, and pentagonal symmetry within a bounded universe paradigm.</p>
                <p><strong>Key Elements:</strong> Flower of Life patterns, Vesica Piscis, Platonic solids, and mathematical constants governing universal structure and consciousness field geometry.</p>
            </div>
        </div>
        
        <div class="navigation">
            <button id="prev-button" onclick="prevDiagram()">Previous</button>
            <button id="next-button" onclick="nextDiagram()">Next</button>
        </div>
    </div>

    <script>
        function prevDiagram() {
            window.location.href = 'high-level-architecture.html';
        }
        
        function nextDiagram() {
            window.location.href = 'three-body-problem.html';
        }
        
        // Disable buttons if needed
        document.addEventListener('DOMContentLoaded', function() {
            if ('high-level-architecture.html' === '#') {
                document.getElementById('prev-button').disabled = true;
            }
            if ('three-body-problem.html' === '#') {
                document.getElementById('next-button').disabled = true;
            }
        });
    </script>
</body>
</html>
