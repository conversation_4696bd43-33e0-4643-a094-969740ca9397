/**
 * Secure Connector Registry for NovaConnect Universal API Connector
 * 
 * This service provides secure storage and management of connector definitions.
 */

const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const bodyParser = require('body-parser');
const winston = require('winston');
const crypto = require('crypto');
const fs = require('fs');
const path = require('path');

// Import encryption utilities
const { encrypt, decrypt, hashData, verifyHash } = require('../utils/encryption');

const app = express();
const port = process.env.PORT || 3001;

// Configure logger
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' })
  ]
});

// Middleware
app.use(cors());
app.use(bodyParser.json({ limit: '10mb' }));
app.use(bodyParser.urlencoded({ extended: true, limit: '10mb' }));

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/connector-registry', {
  useNewUrlParser: true,
  useUnifiedTopology: true
})
.then(() => {
  logger.info('Connected to MongoDB');
})
.catch((err) => {
  logger.error('Error connecting to MongoDB', { error: err.message });
});

// Define connector schema with enhanced security
const connectorSchema = new mongoose.Schema({
  name: { type: String, required: true },
  version: { type: String, required: true },
  category: { type: String, required: true },
  description: { type: String },
  author: { type: String },
  tags: [String],
  definition: { type: String, required: true }, // Encrypted connector definition
  definitionHash: { type: String, required: true }, // Hash of the connector definition for integrity verification
  created: { type: Date, default: Date.now },
  updated: { type: Date, default: Date.now }
});

// Define transformation schema
const transformationSchema = new mongoose.Schema({
  name: { type: String, required: true, unique: true },
  code: { type: String, required: true },
  description: { type: String },
  codeHash: { type: String, required: true }, // Hash of the code for integrity verification
  created: { type: Date, default: Date.now },
  updated: { type: Date, default: Date.now }
});

const Connector = mongoose.model('Connector', connectorSchema);
const Transformation = mongoose.model('Transformation', transformationSchema);

// Encryption key for connector definitions
const ENCRYPTION_KEY = process.env.CONNECTOR_ENCRYPTION_KEY || crypto.randomBytes(32).toString('hex');

// Enhanced security functions
const encryptConnectorDefinition = (definition) => {
  // Encrypt the connector definition
  const encryptedData = encrypt(definition, ENCRYPTION_KEY, { deriveKey: true });
  
  // Hash the original definition for integrity verification
  const hash = crypto.createHash('sha256').update(JSON.stringify(definition)).digest('hex');
  
  return {
    encryptedData: JSON.stringify(encryptedData),
    hash
  };
};

const decryptConnectorDefinition = (encryptedDefinition, hash) => {
  // Decrypt the connector definition
  const encryptedData = JSON.parse(encryptedDefinition);
  const definition = decrypt(encryptedData, ENCRYPTION_KEY, { deriveKey: true, parseJson: true });
  
  // Verify the integrity of the decrypted definition
  const computedHash = crypto.createHash('sha256').update(JSON.stringify(definition)).digest('hex');
  
  if (computedHash !== hash) {
    throw new Error('Connector definition integrity check failed');
  }
  
  return definition;
};

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok' });
});

// API endpoints
app.get('/connectors', async (req, res) => {
  try {
    const { category, tag } = req.query;
    
    let query = {};
    if (category) {
      query.category = category;
    }
    if (tag) {
      query.tags = tag;
    }
    
    const connectors = await Connector.find(query, '-definition -definitionHash');
    res.json(connectors);
  } catch (err) {
    logger.error('Error fetching connectors', { error: err.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.get('/connectors/:id', async (req, res) => {
  try {
    const connector = await Connector.findById(req.params.id);
    if (!connector) {
      return res.status(404).json({ error: 'Connector not found' });
    }
    
    // Decrypt the connector definition
    try {
      const definition = decryptConnectorDefinition(connector.definition, connector.definitionHash);
      
      // Return the connector with decrypted definition
      const result = {
        ...connector.toObject(),
        definition
      };
      
      delete result.definitionHash;
      
      res.json(result);
    } catch (decryptError) {
      logger.error('Error decrypting connector definition', { error: decryptError.message });
      res.status(500).json({ error: 'Error decrypting connector definition' });
    }
  } catch (err) {
    logger.error('Error fetching connector', { error: err.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.post('/connectors', async (req, res) => {
  try {
    const { name, version, category, description, author, tags, ...definition } = req.body;
    
    if (!name || !version || !category) {
      return res.status(400).json({ error: 'Missing required fields' });
    }
    
    // Encrypt the connector definition
    const { encryptedData, hash } = encryptConnectorDefinition(definition);
    
    const connector = new Connector({
      name,
      version,
      category,
      description,
      author,
      tags,
      definition: encryptedData,
      definitionHash: hash
    });
    
    await connector.save();
    
    // Return the connector without the encrypted definition
    const { definition: _, definitionHash: __, ...result } = connector.toObject();
    res.status(201).json(result);
  } catch (err) {
    logger.error('Error creating connector', { error: err.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.put('/connectors/:id', async (req, res) => {
  try {
    const { name, version, category, description, author, tags, ...definition } = req.body;
    
    if (!name || !version || !category) {
      return res.status(400).json({ error: 'Missing required fields' });
    }
    
    // Encrypt the connector definition
    const { encryptedData, hash } = encryptConnectorDefinition(definition);
    
    const connector = await Connector.findByIdAndUpdate(
      req.params.id,
      {
        name,
        version,
        category,
        description,
        author,
        tags,
        definition: encryptedData,
        definitionHash: hash,
        updated: Date.now()
      },
      { new: true }
    );
    
    if (!connector) {
      return res.status(404).json({ error: 'Connector not found' });
    }
    
    // Return the connector without the encrypted definition
    const { definition: _, definitionHash: __, ...result } = connector.toObject();
    res.json(result);
  } catch (err) {
    logger.error('Error updating connector', { error: err.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.delete('/connectors/:id', async (req, res) => {
  try {
    const connector = await Connector.findByIdAndDelete(req.params.id);
    if (!connector) {
      return res.status(404).json({ error: 'Connector not found' });
    }
    res.status(204).send();
  } catch (err) {
    logger.error('Error deleting connector', { error: err.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Transformation endpoints
app.get('/transformations', async (req, res) => {
  try {
    const transformations = await Transformation.find({}, '-code -codeHash');
    res.json(transformations);
  } catch (err) {
    logger.error('Error fetching transformations', { error: err.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.get('/transformations/:name', async (req, res) => {
  try {
    const transformation = await Transformation.findOne({ name: req.params.name });
    if (!transformation) {
      return res.status(404).json({ error: 'Transformation not found' });
    }
    
    // Verify code integrity
    const computedHash = crypto.createHash('sha256').update(transformation.code).digest('hex');
    if (computedHash !== transformation.codeHash) {
      logger.error('Transformation code integrity check failed', { name: req.params.name });
      return res.status(500).json({ error: 'Transformation code integrity check failed' });
    }
    
    // Return the transformation without the hash
    const { codeHash, ...result } = transformation.toObject();
    res.json(result);
  } catch (err) {
    logger.error('Error fetching transformation', { error: err.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.post('/transformations', async (req, res) => {
  try {
    const { name, code, description } = req.body;
    
    if (!name || !code) {
      return res.status(400).json({ error: 'Missing required fields' });
    }
    
    // Hash the code for integrity verification
    const codeHash = crypto.createHash('sha256').update(code).digest('hex');
    
    const transformation = new Transformation({
      name,
      code,
      description,
      codeHash
    });
    
    await transformation.save();
    
    // Return the transformation without the hash
    const { codeHash: _, ...result } = transformation.toObject();
    res.status(201).json(result);
  } catch (err) {
    logger.error('Error creating transformation', { error: err.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.put('/transformations/:name', async (req, res) => {
  try {
    const { code, description } = req.body;
    
    if (!code) {
      return res.status(400).json({ error: 'Missing required fields' });
    }
    
    // Hash the code for integrity verification
    const codeHash = crypto.createHash('sha256').update(code).digest('hex');
    
    const transformation = await Transformation.findOneAndUpdate(
      { name: req.params.name },
      {
        code,
        description,
        codeHash,
        updated: Date.now()
      },
      { new: true }
    );
    
    if (!transformation) {
      return res.status(404).json({ error: 'Transformation not found' });
    }
    
    // Return the transformation without the hash
    const { codeHash: _, ...result } = transformation.toObject();
    res.json(result);
  } catch (err) {
    logger.error('Error updating transformation', { error: err.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.delete('/transformations/:name', async (req, res) => {
  try {
    const transformation = await Transformation.findOneAndDelete({ name: req.params.name });
    if (!transformation) {
      return res.status(404).json({ error: 'Transformation not found' });
    }
    res.status(204).send();
  } catch (err) {
    logger.error('Error deleting transformation', { error: err.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Start the server
const server = app.listen(port, () => {
  logger.info(`Secure connector registry running on port ${port}`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM signal received: closing HTTP server');
  server.close(() => {
    logger.info('HTTP server closed');
    process.exit(0);
  });
});

module.exports = app;
