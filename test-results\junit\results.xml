<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="jest tests" tests="15" failures="0" errors="0" time="6.523">
  <testsuite name="undefined" errors="0" failures="0" skipped="0" timestamp="2025-07-20T17:35:00" time="2.202" tests="5">
    <testcase classname=" Policy adaptation in NovaCortex" name=" Policy adaptation in NovaCortex" time="0.009">
    </testcase>
    <testcase classname=" System coherence under load" name=" System coherence under load" time="0.002">
    </testcase>
    <testcase classname=" Performance scaling in NovaLift" name=" Performance scaling in NovaLift" time="0.001">
    </testcase>
    <testcase classname=" Error handling robustness" name=" Error handling robustness" time="0.013">
    </testcase>
    <testcase classname=" E2E workflow" name=" E2E workflow" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="NovaAscend API" errors="0" failures="0" skipped="0" timestamp="2025-07-20T17:35:00" time="5.376" tests="10">
    <testcase classname="NovaAscend API should align NovaCortex via /decree" name="NovaAscend API should align NovaCortex via /decree" time="0.257">
    </testcase>
    <testcase classname="NovaAscend API should get vision from NovaCortex via /vision" name="NovaAscend API should get vision from NovaCortex via /vision" time="0.008">
    </testcase>
    <testcase classname="NovaAscend API should update policy via /firewall" name="NovaAscend API should update policy via /firewall" time="0.007">
    </testcase>
    <testcase classname="NovaAscend API should check system coherence via /coherence/check" name="NovaAscend API should check system coherence via /coherence/check" time="0.006">
    </testcase>
    <testcase classname="NovaAscend API should scale NovaLift via /lift/scale" name="NovaAscend API should scale NovaLift via /lift/scale" time="0.006">
    </testcase>
    <testcase classname="NovaAscend API should get NovaLift status via /lift/status" name="NovaAscend API should get NovaLift status via /lift/status" time="0.006">
    </testcase>
    <testcase classname="NovaAscend API should get NovaCaia status via /caia/status" name="NovaAscend API should get NovaCaia status via /caia/status" time="0.006">
    </testcase>
    <testcase classname="NovaAscend API should evaluate performance vs. compliance via /performance/evaluate" name="NovaAscend API should evaluate performance vs. compliance via /performance/evaluate" time="0.006">
    </testcase>
    <testcase classname="NovaAscend API should normalize telemetry data via /telemetry/normalize" name="NovaAscend API should normalize telemetry data via /telemetry/normalize" time="0.007">
    </testcase>
    <testcase classname="NovaAscend API should return 400 for invalid /decree request" name="NovaAscend API should return 400 for invalid /decree request" time="0.005">
    </testcase>
  </testsuite>
</testsuites>