const express = require('express');
const { validateRequest, validateQuery, schemas } = require('./validation');
const controllers = require('./controllers');
const { cacheMiddleware } = require('./cache');
const { authorize } = require('./auth');

const router = express.Router();

/**
 * @swagger
 * /privacy/management/processing-activities:
 *   get:
 *     summary: Get a list of data processing activities
 *     description: Returns a paginated list of data processing activities with optional filtering
 *     tags: [Privacy Management]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: page
 *         in: query
 *         description: Page number
 *         schema:
 *           type: integer
 *           default: 1
 *       - name: limit
 *         in: query
 *         description: Number of items per page
 *         schema:
 *           type: integer
 *           default: 10
 *       - name: q
 *         in: query
 *         description: Search query to search across multiple fields
 *         schema:
 *           type: string
 *       - name: status
 *         in: query
 *         description: Filter by status
 *         schema:
 *           type: string
 *           enum: [active, inactive, archived]
 *       - name: purpose
 *         in: query
 *         description: Filter by purpose (partial match)
 *         schema:
 *           type: string
 *       - name: dataSubject
 *         in: query
 *         description: Filter by data subject (partial match)
 *         schema:
 *           type: string
 *       - name: dataCategory
 *         in: query
 *         description: Filter by data category (partial match)
 *         schema:
 *           type: string
 *       - name: securityMeasure
 *         in: query
 *         description: Filter by security measure (partial match)
 *         schema:
 *           type: string
 *       - name: legalBasis
 *         in: query
 *         description: Filter by legal basis
 *         schema:
 *           type: string
 *           enum: [consent, contract, legal-obligation, vital-interests, public-interest, legitimate-interests]
 *       - name: riskLevel
 *         in: query
 *         description: Filter by risk level (activities with at least one risk of the specified level)
 *         schema:
 *           type: string
 *           enum: [low, medium, high]
 *       - name: dpiaRequired
 *         in: query
 *         description: Filter by DPIA required status
 *         schema:
 *           type: string
 *           enum: [true, false]
 *       - name: dpiaCompleted
 *         in: query
 *         description: Filter by DPIA completion status
 *         schema:
 *           type: string
 *           enum: [true, false]
 *       - name: createdAfter
 *         in: query
 *         description: Filter by creation date (after)
 *         schema:
 *           type: string
 *           format: date-time
 *       - name: createdBefore
 *         in: query
 *         description: Filter by creation date (before)
 *         schema:
 *           type: string
 *           format: date-time
 *       - name: updatedAfter
 *         in: query
 *         description: Filter by update date (after)
 *         schema:
 *           type: string
 *           format: date-time
 *       - name: updatedBefore
 *         in: query
 *         description: Filter by update date (before)
 *         schema:
 *           type: string
 *           format: date-time
 *       - name: sortBy
 *         in: query
 *         description: Field to sort by
 *         schema:
 *           type: string
 *           default: name
 *       - name: sortOrder
 *         in: query
 *         description: Sort order
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *           default: asc
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/DataProcessingActivity'
 *                 pagination:
 *                   $ref: '#/components/schemas/Pagination'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/processing-activities', controllers.getDataProcessingActivities);

/**
 * @swagger
 * /privacy/management/processing-activities/{id}:
 *   get:
 *     summary: Get a specific data processing activity
 *     description: Returns a specific data processing activity by ID
 *     tags: [Privacy Management]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Data processing activity ID
 *         required: true
 *         schema:
 *           type: string
 *       - name: includeCompliance
 *         in: query
 *         description: Include compliance status information
 *         schema:
 *           type: string
 *           enum: [true, false]
 *       - name: includeRequirements
 *         in: query
 *         description: Include relevant regulatory requirements
 *         schema:
 *           type: string
 *           enum: [true, false]
 *       - name: includeChanges
 *         in: query
 *         description: Include relevant regulatory changes
 *         schema:
 *           type: string
 *           enum: [true, false]
 *       - name: includeImpactAssessment
 *         in: query
 *         description: Include automated privacy impact assessment
 *         schema:
 *           type: string
 *           enum: [true, false]
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/DataProcessingActivity'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/processing-activities/:id', controllers.getDataProcessingActivityById);

/**
 * @swagger
 * /privacy/management/processing-activities:
 *   post:
 *     summary: Create a new data processing activity
 *     description: Creates a new data processing activity
 *     tags: [Privacy Management]
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateDataProcessingActivity'
 *     responses:
 *       201:
 *         description: Data processing activity created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/DataProcessingActivity'
 *                 message:
 *                   type: string
 *                   example: Data processing activity created successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/processing-activities', validateRequest('createDataProcessingActivity'), controllers.createDataProcessingActivity);

/**
 * @swagger
 * /privacy/management/processing-activities/{id}:
 *   put:
 *     summary: Update a data processing activity
 *     description: Updates an existing data processing activity
 *     tags: [Privacy Management]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Data processing activity ID
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateDataProcessingActivity'
 *     responses:
 *       200:
 *         description: Data processing activity updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/DataProcessingActivity'
 *                 message:
 *                   type: string
 *                   example: Data processing activity updated successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.put('/processing-activities/:id', validateRequest('updateDataProcessingActivity'), controllers.updateDataProcessingActivity);

/**
 * @swagger
 * /privacy/management/processing-activities/{id}:
 *   delete:
 *     summary: Delete a data processing activity
 *     description: Deletes a data processing activity
 *     tags: [Privacy Management]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Data processing activity ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Data processing activity deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Data processing activity deleted successfully
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.delete('/processing-activities/:id', controllers.deleteDataProcessingActivity);

/**
 * @swagger
 * /privacy/management/subject-requests:
 *   get:
 *     summary: Get a list of data subject requests
 *     description: Returns a paginated list of data subject requests with optional filtering
 *     tags: [Privacy Management]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: page
 *         in: query
 *         description: Page number
 *         schema:
 *           type: integer
 *           default: 1
 *       - name: limit
 *         in: query
 *         description: Number of items per page
 *         schema:
 *           type: integer
 *           default: 10
 *       - name: requestType
 *         in: query
 *         description: Filter by request type
 *         schema:
 *           type: string
 *           enum: [access, rectification, erasure, restriction, portability, objection, automated-decision, withdraw-consent, other]
 *       - name: status
 *         in: query
 *         description: Filter by status
 *         schema:
 *           type: string
 *           enum: [pending, in-progress, completed, rejected, withdrawn]
 *       - name: dataSubjectEmail
 *         in: query
 *         description: Filter by data subject email (partial match)
 *         schema:
 *           type: string
 *       - name: sortBy
 *         in: query
 *         description: Field to sort by
 *         schema:
 *           type: string
 *           default: requestDate
 *       - name: sortOrder
 *         in: query
 *         description: Sort order
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *           default: desc
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/DataSubjectRequest'
 *                 pagination:
 *                   $ref: '#/components/schemas/Pagination'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/subject-requests', controllers.getDataSubjectRequests);

/**
 * @swagger
 * /privacy/management/subject-requests/{id}:
 *   get:
 *     summary: Get a specific data subject request
 *     description: Returns a specific data subject request by ID
 *     tags: [Privacy Management]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Data subject request ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/DataSubjectRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/subject-requests/:id', controllers.getDataSubjectRequestById);

/**
 * @swagger
 * /privacy/management/subject-requests:
 *   post:
 *     summary: Create a new data subject request
 *     description: Creates a new data subject request
 *     tags: [Privacy Management]
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateDataSubjectRequest'
 *     responses:
 *       201:
 *         description: Data subject request created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/DataSubjectRequest'
 *                 message:
 *                   type: string
 *                   example: Data subject request created successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/subject-requests', validateRequest('createDataSubjectRequest'), controllers.createDataSubjectRequest);

/**
 * @swagger
 * /privacy/management/subject-requests/{id}:
 *   put:
 *     summary: Update a data subject request
 *     description: Updates an existing data subject request
 *     tags: [Privacy Management]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Data subject request ID
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateDataSubjectRequest'
 *     responses:
 *       200:
 *         description: Data subject request updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/DataSubjectRequest'
 *                 message:
 *                   type: string
 *                   example: Data subject request updated successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.put('/subject-requests/:id', validateRequest('updateDataSubjectRequest'), controllers.updateDataSubjectRequest);

/**
 * @swagger
 * /privacy/management/subject-requests/{id}:
 *   delete:
 *     summary: Delete a data subject request
 *     description: Deletes a data subject request
 *     tags: [Privacy Management]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Data subject request ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Data subject request deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Data subject request deleted successfully
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.delete('/subject-requests/:id', controllers.deleteDataSubjectRequest);

/**
 * @swagger
 * /privacy/management/consent-records:
 *   get:
 *     summary: Get a list of consent records
 *     description: Returns a paginated list of consent records with optional filtering
 *     tags: [Privacy Management]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: page
 *         in: query
 *         description: Page number
 *         schema:
 *           type: integer
 *           default: 1
 *       - name: limit
 *         in: query
 *         description: Number of items per page
 *         schema:
 *           type: integer
 *           default: 10
 *       - name: dataSubjectEmail
 *         in: query
 *         description: Filter by data subject email (partial match)
 *         schema:
 *           type: string
 *       - name: consentType
 *         in: query
 *         description: Filter by consent type
 *         schema:
 *           type: string
 *           enum: [marketing, analytics, profiling, third-party-sharing, cookies, research, other]
 *       - name: status
 *         in: query
 *         description: Filter by status
 *         schema:
 *           type: string
 *           enum: [active, withdrawn, expired, declined]
 *       - name: sortBy
 *         in: query
 *         description: Field to sort by
 *         schema:
 *           type: string
 *           default: consentDate
 *       - name: sortOrder
 *         in: query
 *         description: Sort order
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *           default: desc
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/ConsentRecord'
 *                 pagination:
 *                   $ref: '#/components/schemas/Pagination'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/consent-records', controllers.getConsentRecords);

/**
 * @swagger
 * /privacy/management/consent-records/{id}:
 *   get:
 *     summary: Get a specific consent record
 *     description: Returns a specific consent record by ID
 *     tags: [Privacy Management]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Consent record ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/ConsentRecord'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/consent-records/:id', controllers.getConsentRecordById);

/**
 * @swagger
 * /privacy/management/consent-records:
 *   post:
 *     summary: Create a new consent record
 *     description: Creates a new consent record
 *     tags: [Privacy Management]
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateConsentRecord'
 *     responses:
 *       201:
 *         description: Consent record created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/ConsentRecord'
 *                 message:
 *                   type: string
 *                   example: Consent record created successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/consent-records', validateRequest('createConsentRecord'), controllers.createConsentRecord);

/**
 * @swagger
 * /privacy/management/consent-records/{id}:
 *   put:
 *     summary: Update a consent record
 *     description: Updates an existing consent record
 *     tags: [Privacy Management]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Consent record ID
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateConsentRecord'
 *     responses:
 *       200:
 *         description: Consent record updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/ConsentRecord'
 *                 message:
 *                   type: string
 *                   example: Consent record updated successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.put('/consent-records/:id', validateRequest('updateConsentRecord'), controllers.updateConsentRecord);

/**
 * @swagger
 * /privacy/management/consent-records/{id}:
 *   delete:
 *     summary: Delete a consent record
 *     description: Deletes a consent record
 *     tags: [Privacy Management]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Consent record ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Consent record deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Consent record deleted successfully
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.delete('/consent-records/:id', controllers.deleteConsentRecord);

/**
 * @swagger
 * /privacy/management/privacy-notices:
 *   get:
 *     summary: Get a list of privacy notices
 *     description: Returns a paginated list of privacy notices with optional filtering
 *     tags: [Privacy Management]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: page
 *         in: query
 *         description: Page number
 *         schema:
 *           type: integer
 *           default: 1
 *       - name: limit
 *         in: query
 *         description: Number of items per page
 *         schema:
 *           type: integer
 *           default: 10
 *       - name: status
 *         in: query
 *         description: Filter by status
 *         schema:
 *           type: string
 *           enum: [draft, active, archived]
 *       - name: audience
 *         in: query
 *         description: Filter by audience
 *         schema:
 *           type: string
 *           enum: [website-visitors, app-users, customers, employees, job-applicants, vendors, other]
 *       - name: language
 *         in: query
 *         description: Filter by language
 *         schema:
 *           type: string
 *       - name: sortBy
 *         in: query
 *         description: Field to sort by
 *         schema:
 *           type: string
 *           default: effectiveDate
 *       - name: sortOrder
 *         in: query
 *         description: Sort order
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *           default: desc
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/PrivacyNotice'
 *                 pagination:
 *                   $ref: '#/components/schemas/Pagination'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/privacy-notices', controllers.getPrivacyNotices);

/**
 * @swagger
 * /privacy/management/privacy-notices/{id}:
 *   get:
 *     summary: Get a specific privacy notice
 *     description: Returns a specific privacy notice by ID
 *     tags: [Privacy Management]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Privacy notice ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/PrivacyNotice'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/privacy-notices/:id', controllers.getPrivacyNoticeById);

/**
 * @swagger
 * /privacy/management/privacy-notices:
 *   post:
 *     summary: Create a new privacy notice
 *     description: Creates a new privacy notice
 *     tags: [Privacy Management]
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreatePrivacyNotice'
 *     responses:
 *       201:
 *         description: Privacy notice created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/PrivacyNotice'
 *                 message:
 *                   type: string
 *                   example: Privacy notice created successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/privacy-notices', validateRequest('createPrivacyNotice'), controllers.createPrivacyNotice);

/**
 * @swagger
 * /privacy/management/privacy-notices/{id}:
 *   put:
 *     summary: Update a privacy notice
 *     description: Updates an existing privacy notice
 *     tags: [Privacy Management]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Privacy notice ID
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdatePrivacyNotice'
 *     responses:
 *       200:
 *         description: Privacy notice updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/PrivacyNotice'
 *                 message:
 *                   type: string
 *                   example: Privacy notice updated successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.put('/privacy-notices/:id', validateRequest('updatePrivacyNotice'), controllers.updatePrivacyNotice);

/**
 * @swagger
 * /privacy/management/privacy-notices/{id}:
 *   delete:
 *     summary: Delete a privacy notice
 *     description: Deletes a privacy notice
 *     tags: [Privacy Management]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Privacy notice ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Privacy notice deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Privacy notice deleted successfully
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.delete('/privacy-notices/:id', controllers.deletePrivacyNotice);

/**
 * @swagger
 * /privacy/management/data-breaches:
 *   get:
 *     summary: Get a list of data breaches
 *     description: Returns a paginated list of data breaches with optional filtering
 *     tags: [Privacy Management]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: page
 *         in: query
 *         description: Page number
 *         schema:
 *           type: integer
 *           default: 1
 *       - name: limit
 *         in: query
 *         description: Number of items per page
 *         schema:
 *           type: integer
 *           default: 10
 *       - name: status
 *         in: query
 *         description: Filter by status
 *         schema:
 *           type: string
 *           enum: [open, closed]
 *       - name: breachType
 *         in: query
 *         description: Filter by breach type
 *         schema:
 *           type: string
 *           enum: [unauthorized-access, unauthorized-disclosure, lost-device, stolen-device, hacking, malware, phishing, insider-threat, physical-breach, other]
 *       - name: potentialImpact
 *         in: query
 *         description: Filter by potential impact
 *         schema:
 *           type: string
 *           enum: [low, medium, high, critical]
 *       - name: sortBy
 *         in: query
 *         description: Field to sort by
 *         schema:
 *           type: string
 *           default: detectionDate
 *       - name: sortOrder
 *         in: query
 *         description: Sort order
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *           default: desc
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/DataBreach'
 *                 pagination:
 *                   $ref: '#/components/schemas/Pagination'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/data-breaches', controllers.getDataBreaches);

/**
 * @swagger
 * /privacy/management/data-breaches/{id}:
 *   get:
 *     summary: Get a specific data breach
 *     description: Returns a specific data breach by ID
 *     tags: [Privacy Management]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Data breach ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/DataBreach'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/data-breaches/:id', controllers.getDataBreachById);

/**
 * @swagger
 * /privacy/management/data-breaches:
 *   post:
 *     summary: Create a new data breach
 *     description: Creates a new data breach
 *     tags: [Privacy Management]
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateDataBreach'
 *     responses:
 *       201:
 *         description: Data breach created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/DataBreach'
 *                 message:
 *                   type: string
 *                   example: Data breach created successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/data-breaches', validateRequest('createDataBreach'), controllers.createDataBreach);

/**
 * @swagger
 * /privacy/management/data-breaches/{id}:
 *   put:
 *     summary: Update a data breach
 *     description: Updates an existing data breach
 *     tags: [Privacy Management]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Data breach ID
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateDataBreach'
 *     responses:
 *       200:
 *         description: Data breach updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/DataBreach'
 *                 message:
 *                   type: string
 *                   example: Data breach updated successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.put('/data-breaches/:id', validateRequest('updateDataBreach'), controllers.updateDataBreach);

/**
 * @swagger
 * /privacy/management/data-breaches/{id}:
 *   delete:
 *     summary: Delete a data breach
 *     description: Deletes a data breach
 *     tags: [Privacy Management]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Data breach ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Data breach deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Data breach deleted successfully
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.delete('/data-breaches/:id', controllers.deleteDataBreach);

/**
 * @swagger
 * /privacy/management/consent/forms:
 *   get:
 *     summary: Generate a consent form
 *     description: Generates a consent form for a specific purpose
 *     tags: [Privacy Management]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: consentType
 *         in: query
 *         description: Type of consent
 *         required: true
 *         schema:
 *           type: string
 *           enum: [marketing, analytics, profiling, third-party-sharing, cookies, research, other]
 *       - name: language
 *         in: query
 *         description: Language code
 *         schema:
 *           type: string
 *           default: en
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/ConsentForm'
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/consent/forms', controllers.generateConsentForm);

/**
 * @swagger
 * /privacy/management/consent/{id}/validity:
 *   get:
 *     summary: Verify consent validity
 *     description: Checks if a consent record is valid
 *     tags: [Privacy Management]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Consent record ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: object
 *                   properties:
 *                     valid:
 *                       type: boolean
 *                     reason:
 *                       type: string
 *                     record:
 *                       $ref: '#/components/schemas/ConsentRecord'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/consent/:id/validity', controllers.verifyConsentValidity);

/**
 * @swagger
 * /privacy/management/consent/{id}/withdraw:
 *   post:
 *     summary: Withdraw consent
 *     description: Withdraws a previously given consent
 *     tags: [Privacy Management]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Consent record ID
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               withdrawalMethod:
 *                 type: string
 *                 enum: [online-form, email, phone, in-person, api, other]
 *             required:
 *               - withdrawalMethod
 *     responses:
 *       200:
 *         description: Consent withdrawn successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/ConsentRecord'
 *                 message:
 *                   type: string
 *                   example: Consent withdrawn successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/consent/:id/withdraw', controllers.withdrawConsent);

/**
 * @swagger
 * /privacy/management/consent/data-subjects/{dataSubjectId}:
 *   get:
 *     summary: Get consent records by data subject
 *     description: Returns all consent records for a specific data subject
 *     tags: [Privacy Management]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: dataSubjectId
 *         in: path
 *         description: Data subject ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/ConsentRecord'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/consent/data-subjects/:dataSubjectId', controllers.getConsentRecordsByDataSubject);

/**
 * @swagger
 * /privacy/management/consent/emails/{email}:
 *   get:
 *     summary: Get consent records by email
 *     description: Returns all consent records for a specific email address
 *     tags: [Privacy Management]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: email
 *         in: path
 *         description: Email address
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/ConsentRecord'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/consent/emails/:email', controllers.getConsentRecordsByEmail);

/**
 * @swagger
 * /privacy/management/consent/proof/verify:
 *   post:
 *     summary: Verify consent proof
 *     description: Verifies the validity of a consent proof
 *     tags: [Privacy Management]
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               consentProof:
 *                 type: string
 *             required:
 *               - consentProof
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: object
 *                   properties:
 *                     verified:
 *                       type: boolean
 *                     reason:
 *                       type: string
 *                     details:
 *                       type: object
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/consent/proof/verify', controllers.verifyConsentProof);

/**
 * @swagger
 * /privacy/management/consent/proof/generate:
 *   post:
 *     summary: Generate consent proof
 *     description: Generates a consent proof based on the provided information
 *     tags: [Privacy Management]
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               ip:
 *                 type: string
 *               userAgent:
 *                 type: string
 *               timestamp:
 *                 type: string
 *                 format: date-time
 *             required:
 *               - ip
 *               - userAgent
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: object
 *                   properties:
 *                     consentProof:
 *                       type: string
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/consent/proof/generate', controllers.generateConsentProof);

/**
 * @swagger
 * /privacy/management/data-systems:
 *   get:
 *     summary: Get data systems
 *     description: Returns a list of data systems that can be used for data subject requests
 *     tags: [Privacy Management]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: requestType
 *         in: query
 *         description: Filter by request type
 *         schema:
 *           type: string
 *           enum: [access, rectification, erasure, restriction, portability, objection, automated-decision, withdraw-consent, other]
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/DataSystem'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/data-systems', controllers.getDataSystems);

/**
 * @swagger
 * /privacy/management/data-systems/{id}:
 *   get:
 *     summary: Get a data system
 *     description: Returns a specific data system by ID
 *     tags: [Privacy Management]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Data system ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/DataSystem'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/data-systems/:id', controllers.getDataSystemById);

/**
 * @swagger
 * /privacy/management/subject-requests/{id}/process:
 *   post:
 *     summary: Process a data subject request
 *     description: Processes a data subject request automatically
 *     tags: [Privacy Management]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Data subject request ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Request processed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/DataSubjectRequest'
 *                 message:
 *                   type: string
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/subject-requests/:id/process', controllers.processDataSubjectRequest);

/**
 * @swagger
 * /privacy/management/subject-requests/{id}/export:
 *   get:
 *     summary: Generate a data export
 *     description: Generates a data export for a data subject request
 *     tags: [Privacy Management]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Data subject request ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Data export generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/DataExport'
 *                 message:
 *                   type: string
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/subject-requests/:id/export', controllers.generateDataExport);

/**
 * @swagger
 * /privacy/management/subject-requests/{id}/affected-systems:
 *   get:
 *     summary: Determine affected systems
 *     description: Determines which systems are affected by a data subject request
 *     tags: [Privacy Management]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Data subject request ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/DataSystem'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/subject-requests/:id/affected-systems', controllers.determineAffectedSystems);

/**
 * @swagger
 * /privacy/management/reports/{reportType}:
 *   get:
 *     summary: Generate a report
 *     description: Generates a report based on the specified type and parameters
 *     tags: [Privacy Management]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: reportType
 *         in: path
 *         description: Type of report to generate
 *         required: true
 *         schema:
 *           type: string
 *           enum: [dsr-summary, consent-management, data-breach, processing-activities, compliance-status]
 *       - name: period
 *         in: query
 *         description: Time period for the report
 *         required: true
 *         schema:
 *           type: string
 *           enum: [last-7-days, last-30-days, last-90-days, last-12-months, year-to-date, custom]
 *       - name: startDate
 *         in: query
 *         description: Start date for custom period (ISO format)
 *         schema:
 *           type: string
 *           format: date-time
 *       - name: endDate
 *         in: query
 *         description: End date for custom period (ISO format)
 *         schema:
 *           type: string
 *           format: date-time
 *       - name: groupBy
 *         in: query
 *         description: Field to group data by
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Report generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: object
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/reports/:reportType', controllers.generateReport);

/**
 * @swagger
 * /privacy/management/dashboard/metrics:
 *   get:
 *     summary: Get dashboard metrics
 *     description: Returns metrics for the dashboard
 *     tags: [Privacy Management]
 *     security:
 *       - ApiKeyAuth: []
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: object
 *                   properties:
 *                     dsrMetrics:
 *                       type: object
 *                     consentMetrics:
 *                       type: object
 *                     dataBreachMetrics:
 *                       type: object
 *                     complianceMetrics:
 *                       type: object
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/dashboard/metrics', controllers.getDashboardMetrics);

/**
 * @swagger
 * /privacy/management/notifications:
 *   get:
 *     summary: Get notifications
 *     description: Returns a list of notifications with optional filtering
 *     tags: [Privacy Management]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: recipient
 *         in: query
 *         description: Filter by recipient
 *         schema:
 *           type: string
 *       - name: status
 *         in: query
 *         description: Filter by status
 *         schema:
 *           type: string
 *           enum: [pending, sent, delivered, read, failed]
 *       - name: type
 *         in: query
 *         description: Filter by type
 *         schema:
 *           type: string
 *           enum: [dsr-received, dsr-due-soon, dsr-overdue, dsr-completed, consent-withdrawn, consent-expired, data-breach-reported, data-breach-notification-due, dpia-required, regulatory-change, compliance-issue]
 *       - name: priority
 *         in: query
 *         description: Filter by priority
 *         schema:
 *           type: string
 *           enum: [low, medium, high, urgent]
 *       - name: relatedEntityType
 *         in: query
 *         description: Filter by related entity type
 *         schema:
 *           type: string
 *       - name: relatedEntityId
 *         in: query
 *         description: Filter by related entity ID
 *         schema:
 *           type: string
 *       - name: startDate
 *         in: query
 *         description: Filter by creation date (after)
 *         schema:
 *           type: string
 *           format: date-time
 *       - name: endDate
 *         in: query
 *         description: Filter by creation date (before)
 *         schema:
 *           type: string
 *           format: date-time
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Notification'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/notifications', controllers.getNotifications);

/**
 * @swagger
 * /privacy/management/notifications/{id}:
 *   get:
 *     summary: Get a notification
 *     description: Returns a specific notification by ID
 *     tags: [Privacy Management]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Notification ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/Notification'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/notifications/:id', controllers.getNotificationById);

/**
 * @swagger
 * /privacy/management/notifications:
 *   post:
 *     summary: Create a notification
 *     description: Creates a new notification
 *     tags: [Privacy Management]
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               type:
 *                 type: string
 *                 enum: [dsr-received, dsr-due-soon, dsr-overdue, dsr-completed, consent-withdrawn, consent-expired, data-breach-reported, data-breach-notification-due, dpia-required, regulatory-change, compliance-issue]
 *               title:
 *                 type: string
 *               message:
 *                 type: string
 *               priority:
 *                 type: string
 *                 enum: [low, medium, high, urgent]
 *               recipients:
 *                 type: array
 *                 items:
 *                   type: string
 *               channels:
 *                 type: array
 *                 items:
 *                   type: string
 *                   enum: [email, sms, push, in-app, webhook]
 *               relatedEntityType:
 *                 type: string
 *               relatedEntityId:
 *                 type: string
 *               metadata:
 *                 type: object
 *             required:
 *               - type
 *               - title
 *               - message
 *               - recipients
 *     responses:
 *       201:
 *         description: Notification created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/Notification'
 *                 message:
 *                   type: string
 *                   example: Notification created successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/notifications', controllers.createNotification);

/**
 * @swagger
 * /privacy/management/notifications/{id}/read:
 *   post:
 *     summary: Mark a notification as read
 *     description: Marks a notification as read
 *     tags: [Privacy Management]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Notification ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Notification marked as read
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/Notification'
 *                 message:
 *                   type: string
 *                   example: Notification marked as read
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/notifications/:id/read', controllers.markNotificationAsRead);

/**
 * @swagger
 * /privacy/management/notifications/{id}/send:
 *   post:
 *     summary: Send a notification
 *     description: Sends a notification through the specified channels
 *     tags: [Privacy Management]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Notification ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Notification sent successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/Notification'
 *                 message:
 *                   type: string
 *                   example: Notification sent successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/notifications/:id/send', controllers.sendNotification);

/**
 * @swagger
 * /privacy/management/notifications/generate:
 *   post:
 *     summary: Generate notifications
 *     description: Generates notifications based on the current state of the system
 *     tags: [Privacy Management]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: type
 *         in: query
 *         description: Type of notifications to generate
 *         schema:
 *           type: string
 *           enum: [dsr, data-breach, dpia]
 *     responses:
 *       200:
 *         description: Notifications generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Notification'
 *                 message:
 *                   type: string
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/notifications/generate', controllers.generateNotifications);

/**
 * @swagger
 * /privacy/management/notifications/send-all:
 *   post:
 *     summary: Send all pending notifications
 *     description: Sends all pending notifications
 *     tags: [Privacy Management]
 *     security:
 *       - ApiKeyAuth: []
 *     responses:
 *       200:
 *         description: Notifications sent successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: object
 *                   properties:
 *                     total:
 *                       type: integer
 *                     sent:
 *                       type: integer
 *                     failed:
 *                       type: integer
 *                     details:
 *                       type: array
 *                       items:
 *                         type: object
 *                 message:
 *                   type: string
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/notifications/send-all', controllers.sendAllPendingNotifications);

module.exports = router;
