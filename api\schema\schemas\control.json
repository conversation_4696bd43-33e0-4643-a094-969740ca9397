{"entity": "Control", "entityPlural": "Controls", "apiEndpoint": "/api/v1/novaassure/controls", "fields": [{"name": "name", "type": "string", "required": true, "label": "Control Name", "placeholder": "Enter control name"}, {"name": "description", "type": "textarea", "required": true, "label": "Description", "placeholder": "Enter control description"}, {"name": "framework", "type": "dropdown", "options": ["soc2", "gdpr", "hipaa", "iso27001", "pci-dss"], "required": true, "label": "Framework"}, {"name": "category", "type": "dropdown", "options": ["access-control", "data-protection", "network-security", "logging-monitoring", "incident-response", "business-continuity", "compliance"], "required": true, "label": "Category"}, {"name": "status", "type": "dropdown", "options": ["active", "inactive", "draft"], "required": true, "label": "Status"}, {"name": "riskLevel", "type": "dropdown", "options": ["low", "medium", "high", "critical"], "required": true, "label": "Risk Level"}, {"name": "implementationStatus", "type": "dropdown", "options": ["implemented", "partially-implemented", "not-implemented", "not-applicable"], "required": true, "label": "Implementation Status"}, {"name": "requirements", "type": "array", "required": false, "label": "Requirements"}, {"name": "testProcedures", "type": "array", "required": false, "label": "Test Procedures"}]}