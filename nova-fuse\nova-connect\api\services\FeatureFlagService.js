/**
 * Feature Flag Service
 *
 * This service manages feature flags and subscription tier access.
 */

const fs = require('fs').promises;
const path = require('path');
const { ValidationError, NotFoundError, AuthorizationError } = require('../utils/errors');

class FeatureFlagService {
  constructor(dataDir = path.join(__dirname, '../data')) {
    this.dataDir = dataDir;
    this.featureFlagsDir = path.join(this.dataDir, 'feature_flags');
    this.featureFlagsFile = path.join(this.featureFlagsDir, 'feature_flags.json');
    this.subscriptionTiersFile = path.join(this.featureFlagsDir, 'subscription_tiers.json');
    this.userEntitlementsFile = path.join(this.featureFlagsDir, 'user_entitlements.json');
    this.featureUsageFile = path.join(this.featureFlagsDir, 'feature_usage.json');

    this.ensureDataDir();

    // Define subscription tiers
    this.tiers = {
      FREE: 'free',
      STANDARD: 'standard',
      PROFESSIONAL: 'professional',
      ENTERPRISE: 'enterprise'
    };
  }

  /**
   * Ensure the data directory exists
   */
  async ensureDataDir() {
    try {
      await fs.mkdir(this.featureFlagsDir, { recursive: true });

      // Initialize files if they don't exist
      await this.initializeFile(this.featureFlagsFile, this.getDefaultFeatureFlags());
      await this.initializeFile(this.subscriptionTiersFile, this.getDefaultSubscriptionTiers());
      await this.initializeFile(this.userEntitlementsFile, []);
      await this.initializeFile(this.featureUsageFile, []);
    } catch (error) {
      console.error('Error creating feature flags directory:', error);
      throw error;
    }
  }

  /**
   * Initialize a file with default data if it doesn't exist
   */
  async initializeFile(filePath, defaultData) {
    try {
      await fs.access(filePath);
    } catch (error) {
      if (error.code === 'ENOENT') {
        // File doesn't exist, create it with default data
        await fs.writeFile(filePath, JSON.stringify(defaultData, null, 2));
      } else {
        throw error;
      }
    }
  }

  /**
   * Load data from file
   */
  async loadData(filePath) {
    try {
      const data = await fs.readFile(filePath, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      if (error.code === 'ENOENT') {
        // File doesn't exist, return empty array or default data
        if (filePath === this.featureFlagsFile) {
          return this.getDefaultFeatureFlags();
        } else if (filePath === this.subscriptionTiersFile) {
          return this.getDefaultSubscriptionTiers();
        }
        return [];
      }
      console.error(`Error loading data from ${filePath}:`, error);
      throw error;
    }
  }

  /**
   * Save data to file
   */
  async saveData(filePath, data) {
    try {
      await fs.writeFile(filePath, JSON.stringify(data, null, 2));
    } catch (error) {
      console.error(`Error saving data to ${filePath}:`, error);
      throw error;
    }
  }

  /**
   * Get default feature flags
   */
  getDefaultFeatureFlags() {
    return [
      // Core Features - Available in all tiers
      {
        id: 'core.basic_connectors',
        name: 'Basic Connectors',
        description: 'Connect to basic API endpoints',
        category: 'core',
        enabled: true,
        tiers: [this.tiers.FREE, this.tiers.STANDARD, this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE],
        limits: {
          [this.tiers.FREE]: { connections: 3 },
          [this.tiers.STANDARD]: { connections: 10 },
          [this.tiers.PROFESSIONAL]: { connections: 50 },
          [this.tiers.ENTERPRISE]: { connections: -1 } // Unlimited
        }
      },
      {
        id: 'core.manual_execution',
        name: 'Manual Execution',
        description: 'Manually execute API operations',
        category: 'core',
        enabled: true,
        tiers: [this.tiers.FREE, this.tiers.STANDARD, this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE],
        limits: {
          [this.tiers.FREE]: { operations_per_day: 50 },
          [this.tiers.STANDARD]: { operations_per_day: 500 },
          [this.tiers.PROFESSIONAL]: { operations_per_day: 5000 },
          [this.tiers.ENTERPRISE]: { operations_per_day: -1 } // Unlimited
        }
      },
      {
        id: 'core.basic_monitoring',
        name: 'Basic Monitoring',
        description: 'Basic API monitoring capabilities',
        category: 'core',
        enabled: true,
        tiers: [this.tiers.FREE, this.tiers.STANDARD, this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE]
      },

      // Workflow Features
      {
        id: 'workflow.basic',
        name: 'Basic Workflows',
        description: 'Create simple sequential workflows',
        category: 'workflow',
        enabled: true,
        tiers: [this.tiers.FREE, this.tiers.STANDARD, this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE],
        limits: {
          [this.tiers.FREE]: { workflows: 1, actions_per_workflow: 5 },
          [this.tiers.STANDARD]: { workflows: 5, actions_per_workflow: 10 },
          [this.tiers.PROFESSIONAL]: { workflows: 20, actions_per_workflow: 50 },
          [this.tiers.ENTERPRISE]: { workflows: -1, actions_per_workflow: -1 } // Unlimited
        }
      },
      {
        id: 'workflow.advanced',
        name: 'Advanced Workflows',
        description: 'Create complex workflows with conditions and branching',
        category: 'workflow',
        enabled: true,
        tiers: [this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE]
      },
      {
        id: 'workflow.scheduled',
        name: 'Scheduled Workflows',
        description: 'Schedule workflows to run automatically',
        category: 'workflow',
        enabled: true,
        tiers: [this.tiers.STANDARD, this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE],
        limits: {
          [this.tiers.STANDARD]: { scheduled_workflows: 2 },
          [this.tiers.PROFESSIONAL]: { scheduled_workflows: 10 },
          [this.tiers.ENTERPRISE]: { scheduled_workflows: -1 } // Unlimited
        }
      },
      {
        id: 'workflow.event_triggered',
        name: 'Event-Triggered Workflows',
        description: 'Trigger workflows based on events',
        category: 'workflow',
        enabled: true,
        tiers: [this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE]
      },

      // Export/Import Features
      {
        id: 'export_import.basic',
        name: 'Basic Export/Import',
        description: 'Basic configuration export and import',
        category: 'export_import',
        enabled: true,
        tiers: [this.tiers.STANDARD, this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE]
      },
      {
        id: 'export_import.advanced',
        name: 'Advanced Export/Import',
        description: 'Advanced configuration export and import with selective options',
        category: 'export_import',
        enabled: true,
        tiers: [this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE]
      },

      // Security Features
      {
        id: 'security.basic',
        name: 'Basic Security',
        description: 'Basic security features',
        category: 'security',
        enabled: true,
        tiers: [this.tiers.FREE, this.tiers.STANDARD, this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE]
      },
      {
        id: 'security.advanced',
        name: 'Advanced Security',
        description: 'Advanced security features including IP restrictions',
        category: 'security',
        enabled: true,
        tiers: [this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE]
      },
      {
        id: 'security.enterprise',
        name: 'Enterprise Security',
        description: 'Enterprise-grade security features',
        category: 'security',
        enabled: true,
        tiers: [this.tiers.ENTERPRISE]
      },

      // Monitoring and Alerting Features
      {
        id: 'monitoring.advanced',
        name: 'Advanced Monitoring',
        description: 'Advanced API monitoring capabilities',
        category: 'monitoring',
        enabled: true,
        tiers: [this.tiers.STANDARD, this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE]
      },
      {
        id: 'monitoring.alerting',
        name: 'Alerting',
        description: 'Set up alerts for API monitoring',
        category: 'monitoring',
        enabled: true,
        tiers: [this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE],
        limits: {
          [this.tiers.PROFESSIONAL]: { alerts: 10 },
          [this.tiers.ENTERPRISE]: { alerts: -1 } // Unlimited
        }
      },

      // Analytics Features
      {
        id: 'analytics.basic',
        name: 'Basic Analytics',
        description: 'Basic analytics and reporting',
        category: 'analytics',
        enabled: true,
        tiers: [this.tiers.STANDARD, this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE]
      },
      {
        id: 'analytics.advanced',
        name: 'Advanced Analytics',
        description: 'Advanced analytics and reporting',
        category: 'analytics',
        enabled: true,
        tiers: [this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE]
      },
      {
        id: 'analytics.custom_reports',
        name: 'Custom Reports',
        description: 'Create and schedule custom reports',
        category: 'analytics',
        enabled: true,
        tiers: [this.tiers.ENTERPRISE]
      },

      // AI Features
      {
        id: 'ai.connector_generation',
        name: 'AI-Assisted Connector Creation',
        description: 'Generate connector configurations from API documentation',
        category: 'ai',
        enabled: true,
        tiers: [this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE],
        limits: {
          [this.tiers.PROFESSIONAL]: { generations_per_day: 5 },
          [this.tiers.ENTERPRISE]: { generations_per_day: 20 }
        }
      },
      {
        id: 'ai.natural_language',
        name: 'Natural Language API Queries',
        description: 'Create connectors using natural language descriptions',
        category: 'ai',
        enabled: true,
        tiers: [this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE],
        limits: {
          [this.tiers.PROFESSIONAL]: { queries_per_day: 10 },
          [this.tiers.ENTERPRISE]: { queries_per_day: 50 }
        }
      },
      {
        id: 'ai.error_resolution',
        name: 'Intelligent Error Resolution',
        description: 'AI-powered suggestions for fixing API errors',
        category: 'ai',
        enabled: true,
        tiers: [this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE],
        limits: {
          [this.tiers.PROFESSIONAL]: { suggestions_per_day: 20 },
          [this.tiers.ENTERPRISE]: { suggestions_per_day: 100 }
        }
      },
      {
        id: 'ai.workflow_optimization',
        name: 'Predictive Workflow Optimization',
        description: 'AI-powered suggestions for optimizing workflows',
        category: 'ai',
        enabled: true,
        tiers: [this.tiers.ENTERPRISE],
        limits: {
          [this.tiers.ENTERPRISE]: { optimizations_per_day: 10 }
        }
      },

      // Governance Features
      {
        id: 'governance.approvals',
        name: 'Approval Workflows',
        description: 'Create and manage multi-step approval workflows',
        category: 'governance',
        enabled: true,
        tiers: [this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE],
        limits: {
          [this.tiers.PROFESSIONAL]: { active_workflows: 5 },
          [this.tiers.ENTERPRISE]: { active_workflows: -1 } // Unlimited
        }
      },
      {
        id: 'governance.compliance',
        name: 'Compliance Templates',
        description: 'Pre-built compliance templates for various regulations',
        category: 'governance',
        enabled: true,
        tiers: [this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE],
        limits: {
          [this.tiers.PROFESSIONAL]: { custom_templates: 2 },
          [this.tiers.ENTERPRISE]: { custom_templates: -1 } // Unlimited
        }
      },
      {
        id: 'governance.data_lineage',
        name: 'Data Lineage Tracking',
        description: 'Track how data moves through different systems',
        category: 'governance',
        enabled: true,
        tiers: [this.tiers.ENTERPRISE],
        limits: {
          [this.tiers.ENTERPRISE]: { tracking_depth: 3 }
        }
      },

      // Security Features
      {
        id: 'security.ip_restrictions',
        name: 'IP Restrictions',
        description: 'Restrict API access to specific IP addresses',
        category: 'security',
        enabled: true,
        tiers: [this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE],
        limits: {
          [this.tiers.PROFESSIONAL]: { restrictions_per_resource: 5 },
          [this.tiers.ENTERPRISE]: { restrictions_per_resource: -1 } // Unlimited
        }
      },
      {
        id: 'security.encryption',
        name: 'Advanced Encryption',
        description: 'Manage encryption keys and encrypt sensitive data',
        category: 'security',
        enabled: true,
        tiers: [this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE],
        limits: {
          [this.tiers.PROFESSIONAL]: { encryption_keys: 10 },
          [this.tiers.ENTERPRISE]: { encryption_keys: -1 } // Unlimited
        }
      },
      {
        id: 'security.policies',
        name: 'Custom Security Policies',
        description: 'Create and enforce custom security policies',
        category: 'security',
        enabled: true,
        tiers: [this.tiers.ENTERPRISE],
        limits: {
          [this.tiers.ENTERPRISE]: { policies: -1 } // Unlimited
        }
      },

      // Advanced Monitoring Features
      {
        id: 'monitoring.anomaly_detection',
        name: 'Anomaly Detection',
        description: 'Automatically detect unusual patterns in API usage',
        category: 'monitoring',
        enabled: true,
        tiers: [this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE],
        limits: {
          [this.tiers.PROFESSIONAL]: { detection_frequency: 'daily' },
          [this.tiers.ENTERPRISE]: { detection_frequency: 'hourly' }
        }
      },

      // Advanced Analytics Features
      {
        id: 'analytics.custom_reports',
        name: 'Custom Reports',
        description: 'Create and schedule custom reports',
        category: 'analytics',
        enabled: true,
        tiers: [this.tiers.ENTERPRISE],
        limits: {
          [this.tiers.ENTERPRISE]: { reports_per_month: 50 }
        }
      },
      {
        id: 'analytics.scheduled_reports',
        name: 'Scheduled Reports',
        description: 'Schedule reports to run automatically',
        category: 'analytics',
        enabled: true,
        tiers: [this.tiers.ENTERPRISE],
        limits: {
          [this.tiers.ENTERPRISE]: { scheduled_reports: 10 }
        }
      },
      {
        id: 'analytics.export_formats',
        name: 'Advanced Export Formats',
        description: 'Export reports in various formats (PDF, CSV, Excel)',
        category: 'analytics',
        enabled: true,
        tiers: [this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE],
        limits: {
          [this.tiers.PROFESSIONAL]: { formats: ['CSV'] },
          [this.tiers.ENTERPRISE]: { formats: ['CSV', 'PDF', 'Excel'] }
        }
      }
    ];
  }

  /**
   * Get default subscription tiers
   */
  getDefaultSubscriptionTiers() {
    return [
      {
        id: this.tiers.FREE,
        name: 'Free',
        description: 'Basic functionality for small projects',
        price: 0,
        features: [
          'core.basic_connectors',
          'core.manual_execution',
          'core.basic_monitoring',
          'workflow.basic',
          'security.basic'
        ],
        limits: {
          connections: 3,
          operations_per_day: 50,
          workflows: 1,
          actions_per_workflow: 5
        }
      },
      {
        id: this.tiers.STANDARD,
        name: 'Standard',
        description: 'Standard functionality for growing teams',
        price: 49,
        features: [
          'core.basic_connectors',
          'core.manual_execution',
          'core.basic_monitoring',
          'workflow.basic',
          'workflow.scheduled',
          'export_import.basic',
          'security.basic',
          'monitoring.advanced',
          'analytics.basic'
        ],
        limits: {
          connections: 10,
          operations_per_day: 500,
          workflows: 5,
          actions_per_workflow: 10,
          scheduled_workflows: 2
        }
      },
      {
        id: this.tiers.PROFESSIONAL,
        name: 'Professional',
        description: 'Advanced functionality for professional teams',
        price: 149,
        features: [
          'core.basic_connectors',
          'core.manual_execution',
          'core.basic_monitoring',
          'workflow.basic',
          'workflow.advanced',
          'workflow.scheduled',
          'workflow.event_triggered',
          'export_import.basic',
          'export_import.advanced',
          'security.basic',
          'security.advanced',
          'security.ip_restrictions',
          'security.encryption',
          'monitoring.advanced',
          'monitoring.alerting',
          'monitoring.anomaly_detection',
          'analytics.basic',
          'analytics.advanced',
          'analytics.export_formats',
          'ai.connector_generation',
          'ai.natural_language',
          'ai.error_resolution',
          'governance.approvals',
          'governance.compliance'
        ],
        limits: {
          connections: 50,
          operations_per_day: 5000,
          workflows: 20,
          actions_per_workflow: 50,
          scheduled_workflows: 10,
          alerts: 10
        }
      },
      {
        id: this.tiers.ENTERPRISE,
        name: 'Enterprise',
        description: 'Enterprise-grade functionality for large organizations',
        price: 499,
        features: [
          'core.basic_connectors',
          'core.manual_execution',
          'core.basic_monitoring',
          'workflow.basic',
          'workflow.advanced',
          'workflow.scheduled',
          'workflow.event_triggered',
          'export_import.basic',
          'export_import.advanced',
          'security.basic',
          'security.advanced',
          'security.enterprise',
          'security.ip_restrictions',
          'security.encryption',
          'security.policies',
          'monitoring.advanced',
          'monitoring.alerting',
          'monitoring.anomaly_detection',
          'analytics.basic',
          'analytics.advanced',
          'analytics.custom_reports',
          'analytics.scheduled_reports',
          'analytics.export_formats',
          'ai.connector_generation',
          'ai.natural_language',
          'ai.error_resolution',
          'ai.workflow_optimization',
          'governance.approvals',
          'governance.compliance',
          'governance.data_lineage'
        ],
        limits: {
          connections: -1, // Unlimited
          operations_per_day: -1, // Unlimited
          workflows: -1, // Unlimited
          actions_per_workflow: -1, // Unlimited
          scheduled_workflows: -1, // Unlimited
          alerts: -1 // Unlimited
        }
      }
    ];
  }

  /**
   * Get all feature flags
   */
  async getAllFeatureFlags() {
    return await this.loadData(this.featureFlagsFile);
  }

  /**
   * Get feature flag by ID
   */
  async getFeatureFlagById(id) {
    const featureFlags = await this.loadData(this.featureFlagsFile);
    const featureFlag = featureFlags.find(f => f.id === id);

    if (!featureFlag) {
      throw new NotFoundError(`Feature flag with ID ${id} not found`);
    }

    return featureFlag;
  }

  /**
   * Update feature flag
   */
  async updateFeatureFlag(id, data) {
    const featureFlags = await this.loadData(this.featureFlagsFile);
    const index = featureFlags.findIndex(f => f.id === id);

    if (index === -1) {
      throw new NotFoundError(`Feature flag with ID ${id} not found`);
    }

    // Update feature flag
    featureFlags[index] = {
      ...featureFlags[index],
      ...data,
      id: featureFlags[index].id // Ensure ID doesn't change
    };

    await this.saveData(this.featureFlagsFile, featureFlags);

    return featureFlags[index];
  }

  /**
   * Get all subscription tiers
   */
  async getAllSubscriptionTiers() {
    return await this.loadData(this.subscriptionTiersFile);
  }

  /**
   * Get subscription tier by ID
   */
  async getSubscriptionTierById(id) {
    const subscriptionTiers = await this.loadData(this.subscriptionTiersFile);
    const subscriptionTier = subscriptionTiers.find(t => t.id === id);

    if (!subscriptionTier) {
      throw new NotFoundError(`Subscription tier with ID ${id} not found`);
    }

    return subscriptionTier;
  }

  /**
   * Get user entitlement
   */
  async getUserEntitlement(userId) {
    const userEntitlements = await this.loadData(this.userEntitlementsFile);
    let userEntitlement = userEntitlements.find(e => e.userId === userId);

    if (!userEntitlement) {
      // Create default entitlement for user
      userEntitlement = {
        userId,
        tierId: this.tiers.FREE,
        customFeatures: [],
        customLimits: {},
        created: new Date().toISOString(),
        updated: new Date().toISOString()
      };

      userEntitlements.push(userEntitlement);
      await this.saveData(this.userEntitlementsFile, userEntitlements);
    }

    return userEntitlement;
  }

  /**
   * Update user entitlement
   */
  async updateUserEntitlement(userId, data) {
    const userEntitlements = await this.loadData(this.userEntitlementsFile);
    const index = userEntitlements.findIndex(e => e.userId === userId);

    if (index === -1) {
      // Create new entitlement
      const userEntitlement = {
        userId,
        tierId: data.tierId || this.tiers.FREE,
        customFeatures: data.customFeatures || [],
        customLimits: data.customLimits || {},
        created: new Date().toISOString(),
        updated: new Date().toISOString()
      };

      userEntitlements.push(userEntitlement);
    } else {
      // Update existing entitlement
      userEntitlements[index] = {
        ...userEntitlements[index],
        tierId: data.tierId || userEntitlements[index].tierId,
        customFeatures: data.customFeatures || userEntitlements[index].customFeatures,
        customLimits: data.customLimits || userEntitlements[index].customLimits,
        updated: new Date().toISOString()
      };
    }

    await this.saveData(this.userEntitlementsFile, userEntitlements);

    return index === -1 ? userEntitlements[userEntitlements.length - 1] : userEntitlements[index];
  }

  /**
   * Check if user has access to feature
   */
  async hasFeatureAccess(userId, featureId) {
    // Get user entitlement
    const userEntitlement = await this.getUserEntitlement(userId);

    // Check if user has custom access to this feature
    if (userEntitlement.customFeatures.includes(featureId)) {
      return true;
    }

    // Get user's subscription tier
    const subscriptionTier = await this.getSubscriptionTierById(userEntitlement.tierId);

    // Check if feature is included in the subscription tier
    return subscriptionTier.features.includes(featureId);
  }

  /**
   * Get user's feature limit
   */
  async getFeatureLimit(userId, featureId, limitKey) {
    // Get user entitlement
    const userEntitlement = await this.getUserEntitlement(userId);

    // Check if user has custom limit for this feature
    if (userEntitlement.customLimits[featureId] &&
        userEntitlement.customLimits[featureId][limitKey] !== undefined) {
      return userEntitlement.customLimits[featureId][limitKey];
    }

    // Get feature flag
    const featureFlag = await this.getFeatureFlagById(featureId);

    // Get user's subscription tier
    const tierId = userEntitlement.tierId;

    // Check if feature has limits for this tier
    if (featureFlag.limits &&
        featureFlag.limits[tierId] &&
        featureFlag.limits[tierId][limitKey] !== undefined) {
      return featureFlag.limits[tierId][limitKey];
    }

    // No limit found
    return null;
  }

  /**
   * Track feature usage
   */
  async trackFeatureUsage(userId, featureId, quantity = 1) {
    const now = new Date();
    const date = now.toISOString().split('T')[0]; // YYYY-MM-DD

    // Load feature usage data
    const featureUsage = await this.loadData(this.featureUsageFile);

    // Find or create usage record
    let usageRecord = featureUsage.find(u =>
      u.userId === userId &&
      u.featureId === featureId &&
      u.date === date
    );

    if (!usageRecord) {
      // Create new usage record
      usageRecord = {
        userId,
        featureId,
        date,
        quantity: 0,
        created: now.toISOString(),
        updated: now.toISOString()
      };

      featureUsage.push(usageRecord);
    }

    // Update usage quantity
    usageRecord.quantity += quantity;
    usageRecord.updated = now.toISOString();

    // Save updated usage data
    await this.saveData(this.featureUsageFile, featureUsage);

    return usageRecord;
  }

  /**
   * Get feature usage for user
   */
  async getFeatureUsageForUser(userId, featureId, startDate, endDate) {
    // Load feature usage data
    const featureUsage = await this.loadData(this.featureUsageFile);

    // Filter usage records
    let filteredUsage = featureUsage.filter(u => u.userId === userId);

    if (featureId) {
      filteredUsage = filteredUsage.filter(u => u.featureId === featureId);
    }

    if (startDate) {
      filteredUsage = filteredUsage.filter(u => u.date >= startDate);
    }

    if (endDate) {
      filteredUsage = filteredUsage.filter(u => u.date <= endDate);
    }

    return filteredUsage;
  }

  /**
   * Check if user has reached feature limit
   */
  async hasReachedFeatureLimit(userId, featureId, limitKey) {
    // Get feature limit
    const limit = await this.getFeatureLimit(userId, featureId, limitKey);

    // If limit is null or -1, there is no limit
    if (limit === null || limit === -1) {
      return false;
    }

    // Get current usage
    const now = new Date();
    const today = now.toISOString().split('T')[0]; // YYYY-MM-DD

    // For daily limits
    if (limitKey.includes('per_day')) {
      const usage = await this.getFeatureUsageForUser(userId, featureId, today, today);
      const totalUsage = usage.reduce((sum, record) => sum + record.quantity, 0);
      return totalUsage >= limit;
    }

    // For other limits, we need to check the actual count
    // This would typically involve querying the relevant service
    // For now, we'll just return false
    return false;
  }

  /**
   * Get user's available features
   */
  async getUserAvailableFeatures(userId) {
    // Get user entitlement
    const userEntitlement = await this.getUserEntitlement(userId);

    // Get user's subscription tier
    const subscriptionTier = await this.getSubscriptionTierById(userEntitlement.tierId);

    // Get all feature flags
    const featureFlags = await this.getAllFeatureFlags();

    // Filter features available to user
    const availableFeatures = featureFlags.filter(feature =>
      feature.enabled && (
        subscriptionTier.features.includes(feature.id) ||
        userEntitlement.customFeatures.includes(feature.id)
      )
    );

    return availableFeatures;
  }

  /**
   * Get user's subscription details
   */
  async getUserSubscriptionDetails(userId) {
    // Get user entitlement
    const userEntitlement = await this.getUserEntitlement(userId);

    // Get user's subscription tier
    const subscriptionTier = await this.getSubscriptionTierById(userEntitlement.tierId);

    // Get user's available features
    const availableFeatures = await this.getUserAvailableFeatures(userId);

    // Get feature usage
    const now = new Date();
    const today = now.toISOString().split('T')[0]; // YYYY-MM-DD
    const usage = await this.getFeatureUsageForUser(userId, null, today, today);

    // Compile subscription details
    return {
      userId,
      tier: {
        id: subscriptionTier.id,
        name: subscriptionTier.name,
        description: subscriptionTier.description,
        price: subscriptionTier.price
      },
      features: availableFeatures.map(feature => ({
        id: feature.id,
        name: feature.name,
        description: feature.description,
        category: feature.category
      })),
      limits: subscriptionTier.limits,
      customFeatures: userEntitlement.customFeatures,
      customLimits: userEntitlement.customLimits,
      usage: usage.reduce((result, record) => {
        result[record.featureId] = (result[record.featureId] || 0) + record.quantity;
        return result;
      }, {})
    };
  }
}

module.exports = FeatureFlagService;
