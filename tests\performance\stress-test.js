import http from 'k6/http';
import { sleep, check } from 'k6';
import { Counter, Rate, Trend } from 'k6/metrics';
import { randomIntBetween } from 'https://jslib.k6.io/k6-utils/1.2.0/index.js';

// Custom metrics
const errorRate = new Rate('error_rate');
const successRate = new Rate('success_rate');
const apiCallDuration = new Trend('api_call_duration');
const apiCalls = new Counter('api_calls');

// Test configuration
export const options = {
  stages: [
    // Ramp up to 100 VUs over 1 minute
    { duration: '1m', target: 100 },
    // Stay at 100 VUs for 3 minutes
    { duration: '3m', target: 100 },
    // Ramp up to 200 VUs over 1 minute
    { duration: '1m', target: 200 },
    // Stay at 200 VUs for 3 minutes
    { duration: '3m', target: 200 },
    // Ramp up to 300 VUs over 1 minute
    { duration: '1m', target: 300 },
    // Stay at 300 VUs for 3 minutes
    { duration: '3m', target: 300 },
    // Ramp down to 0 VUs over 1 minute
    { duration: '1m', target: 0 }
  ],
  thresholds: {
    'http_req_duration': ['p(95)<500'], // 95% of requests should be below 500ms
    'http_req_failed': ['rate<0.1'],    // Less than 10% of requests should fail
    'error_rate': ['rate<0.1'],         // Less than 10% error rate
    'success_rate': ['rate>0.9']        // More than 90% success rate
  }
};

// Base URL
const baseUrl = 'http://novafuse-api:3000';

// API endpoints to test
const endpoints = [
  // NovaCore endpoints
  { method: 'GET', url: `${baseUrl}/api/v1/novacore/test-results` },
  { method: 'GET', url: `${baseUrl}/api/v1/novacore/test-report` },
  
  // NovaTrack endpoints
  { method: 'GET', url: `${baseUrl}/api/v1/novatrack/requirements` },
  { method: 'GET', url: `${baseUrl}/api/v1/novatrack/activities` },
  { method: 'GET', url: `${baseUrl}/api/v1/novatrack/compliance-score` },
  
  // NovaView endpoints
  { method: 'GET', url: `${baseUrl}/api/v1/novaview/dashboard` },
  { method: 'GET', url: `${baseUrl}/api/v1/novaview/dashboard-data` },
  
  // NovaFlowX endpoints
  { method: 'GET', url: `${baseUrl}/api/v1/novaflowx/workflows` },
  { method: 'POST', url: `${baseUrl}/api/v1/novaflowx/events`, body: JSON.stringify({ eventType: 'test_event', data: {} }) },
  
  // NovaThink endpoints
  { method: 'GET', url: `${baseUrl}/api/v1/novathink/frameworks` },
  { method: 'GET', url: `${baseUrl}/api/v1/novathink/search?q=gdpr` },
  
  // NovaProof endpoints
  { method: 'GET', url: `${baseUrl}/api/v1/novaproof/evidence` },
  { method: 'GET', url: `${baseUrl}/api/v1/novaproof/audit-trail` },
  
  // NovaConnect endpoints
  { method: 'GET', url: `${baseUrl}/api/v1/novaconnect/discover` },
  { method: 'POST', url: `${baseUrl}/api/v1/novaconnect/execute`, body: JSON.stringify({ connectorId: 'test-connector', operation: 'test-operation', parameters: {} }) }
];

// Default function - called for each VU iteration
export default function() {
  // Select a random endpoint to test
  const endpoint = endpoints[randomIntBetween(0, endpoints.length - 1)];
  
  // Set headers
  const headers = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'Authorization': 'Bearer test-api-key'
  };
  
  // Make the request
  const startTime = new Date().getTime();
  let response;
  
  if (endpoint.method === 'GET') {
    response = http.get(endpoint.url, { headers });
  } else if (endpoint.method === 'POST') {
    response = http.post(endpoint.url, endpoint.body, { headers });
  }
  
  const endTime = new Date().getTime();
  const duration = endTime - startTime;
  
  // Record metrics
  apiCallDuration.add(duration);
  apiCalls.add(1);
  
  // Check if the request was successful
  const success = check(response, {
    'status is 200': (r) => r.status === 200,
    'response time < 500ms': (r) => r.timings.duration < 500,
    'content-type is application/json': (r) => r.headers['Content-Type'] && r.headers['Content-Type'].includes('application/json')
  });
  
  // Record success/error rates
  successRate.add(success);
  errorRate.add(!success);
  
  // Sleep between requests
  sleep(randomIntBetween(1, 3));
}
