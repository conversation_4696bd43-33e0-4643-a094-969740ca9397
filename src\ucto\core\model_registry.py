"""
Model Registry for the Predictive Engine.

This module provides functionality for registering, managing, and selecting
machine learning models for the Predictive Engine.
"""

import os
import json
import logging
import pickle
from typing import Dict, List, Any, Optional, Callable, Tuple, Union
import datetime

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ModelRegistry:
    """
    Registry for machine learning models used by the Predictive Engine.
    """
    
    def __init__(self, registry_dir: Optional[str] = None):
        """
        Initialize the Model Registry.
        
        Args:
            registry_dir: Directory for storing model registry data
        """
        logger.info("Initializing Model Registry")
        
        # Set the registry directory
        self.registry_dir = registry_dir or os.path.join(os.getcwd(), 'model_registry')
        
        # Create the registry directory if it doesn't exist
        os.makedirs(self.registry_dir, exist_ok=True)
        
        # Create the models directory if it doesn't exist
        self.models_dir = os.path.join(self.registry_dir, 'models')
        os.makedirs(self.models_dir, exist_ok=True)
        
        # Create the metadata directory if it doesn't exist
        self.metadata_dir = os.path.join(self.registry_dir, 'metadata')
        os.makedirs(self.metadata_dir, exist_ok=True)
        
        # Dictionary to store model metadata
        self.model_metadata: Dict[str, Dict[str, Any]] = {}
        
        # Load model metadata from disk
        self._load_model_metadata()
        
        logger.info(f"Model Registry initialized with {len(self.model_metadata)} models")
    
    def register_model(self, 
                      model_id: str, 
                      model: Any, 
                      metadata: Dict[str, Any]) -> Dict[str, Any]:
        """
        Register a model in the registry.
        
        Args:
            model_id: ID of the model
            model: The model to register
            metadata: Metadata for the model
            
        Returns:
            The updated metadata
        """
        logger.info(f"Registering model: {model_id}")
        
        # Check if the model already exists
        if model_id in self.model_metadata:
            logger.warning(f"Model already exists: {model_id}")
            
            # Update the version
            current_version = self.model_metadata[model_id].get('version', 0)
            metadata['version'] = current_version + 1
        else:
            # Set the initial version
            metadata['version'] = 1
        
        # Add registration timestamp
        metadata['registered_at'] = datetime.datetime.now().isoformat()
        
        # Save the model
        model_path = os.path.join(self.models_dir, f"{model_id}.pkl")
        try:
            with open(model_path, 'wb') as f:
                pickle.dump(model, f)
            
            logger.info(f"Model saved to {model_path}")
        except Exception as e:
            logger.error(f"Failed to save model to {model_path}: {e}")
            raise
        
        # Save the metadata
        metadata_path = os.path.join(self.metadata_dir, f"{model_id}.json")
        try:
            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, indent=2)
            
            logger.info(f"Metadata saved to {metadata_path}")
        except Exception as e:
            logger.error(f"Failed to save metadata to {metadata_path}: {e}")
            raise
        
        # Update the in-memory metadata
        self.model_metadata[model_id] = metadata
        
        logger.info(f"Model registered: {model_id} (version {metadata['version']})")
        
        return metadata
    
    def get_model(self, model_id: str) -> Tuple[Any, Dict[str, Any]]:
        """
        Get a model from the registry.
        
        Args:
            model_id: ID of the model
            
        Returns:
            Tuple of (model, metadata)
            
        Raises:
            ValueError: If the model does not exist
        """
        logger.info(f"Getting model: {model_id}")
        
        # Check if the model exists
        if model_id not in self.model_metadata:
            raise ValueError(f"Model not found: {model_id}")
        
        # Get the metadata
        metadata = self.model_metadata[model_id]
        
        # Load the model
        model_path = os.path.join(self.models_dir, f"{model_id}.pkl")
        try:
            with open(model_path, 'rb') as f:
                model = pickle.load(f)
            
            logger.info(f"Model loaded from {model_path}")
        except Exception as e:
            logger.error(f"Failed to load model from {model_path}: {e}")
            raise
        
        return model, metadata
    
    def get_model_metadata(self, model_id: str) -> Dict[str, Any]:
        """
        Get metadata for a model.
        
        Args:
            model_id: ID of the model
            
        Returns:
            Model metadata
            
        Raises:
            ValueError: If the model does not exist
        """
        logger.info(f"Getting metadata for model: {model_id}")
        
        # Check if the model exists
        if model_id not in self.model_metadata:
            raise ValueError(f"Model not found: {model_id}")
        
        return self.model_metadata[model_id]
    
    def list_models(self) -> List[Dict[str, Any]]:
        """
        List all models in the registry.
        
        Returns:
            List of model metadata
        """
        logger.info("Listing models")
        
        return [
            {
                'id': model_id,
                **metadata
            }
            for model_id, metadata in self.model_metadata.items()
        ]
    
    def delete_model(self, model_id: str) -> None:
        """
        Delete a model from the registry.
        
        Args:
            model_id: ID of the model
            
        Raises:
            ValueError: If the model does not exist
        """
        logger.info(f"Deleting model: {model_id}")
        
        # Check if the model exists
        if model_id not in self.model_metadata:
            raise ValueError(f"Model not found: {model_id}")
        
        # Delete the model file
        model_path = os.path.join(self.models_dir, f"{model_id}.pkl")
        try:
            if os.path.exists(model_path):
                os.remove(model_path)
                logger.info(f"Model file deleted: {model_path}")
        except Exception as e:
            logger.error(f"Failed to delete model file {model_path}: {e}")
        
        # Delete the metadata file
        metadata_path = os.path.join(self.metadata_dir, f"{model_id}.json")
        try:
            if os.path.exists(metadata_path):
                os.remove(metadata_path)
                logger.info(f"Metadata file deleted: {metadata_path}")
        except Exception as e:
            logger.error(f"Failed to delete metadata file {metadata_path}: {e}")
        
        # Remove from in-memory metadata
        del self.model_metadata[model_id]
        
        logger.info(f"Model deleted: {model_id}")
    
    def select_best_model(self, 
                         model_type: str, 
                         selection_criteria: Optional[Dict[str, Any]] = None) -> Tuple[str, Dict[str, Any]]:
        """
        Select the best model of a given type based on criteria.
        
        Args:
            model_type: Type of model to select
            selection_criteria: Criteria for selection
            
        Returns:
            Tuple of (model_id, metadata)
            
        Raises:
            ValueError: If no models of the given type exist
        """
        logger.info(f"Selecting best model of type: {model_type}")
        
        # Default selection criteria
        criteria = {
            'metric': 'accuracy',
            'min_threshold': 0.7,
            'prefer_recent': True
        }
        
        # Update with provided criteria
        if selection_criteria:
            criteria.update(selection_criteria)
        
        # Filter models by type
        models_of_type = [
            (model_id, metadata)
            for model_id, metadata in self.model_metadata.items()
            if metadata.get('type') == model_type
        ]
        
        # Check if any models exist
        if not models_of_type:
            raise ValueError(f"No models of type {model_type} found")
        
        # Filter by minimum threshold
        metric = criteria['metric']
        min_threshold = criteria['min_threshold']
        
        qualified_models = [
            (model_id, metadata)
            for model_id, metadata in models_of_type
            if metadata.get('metrics', {}).get(metric, 0) >= min_threshold
        ]
        
        # Check if any qualified models exist
        if not qualified_models:
            logger.warning(f"No models of type {model_type} meet the minimum threshold of {min_threshold} for {metric}")
            
            # Fall back to all models of the type
            qualified_models = models_of_type
        
        # Sort by metric (descending) or registration time (descending)
        if criteria['prefer_recent']:
            # Sort by registration time (most recent first)
            sorted_models = sorted(
                qualified_models,
                key=lambda x: x[1].get('registered_at', ''),
                reverse=True
            )
        else:
            # Sort by metric (highest first)
            sorted_models = sorted(
                qualified_models,
                key=lambda x: x[1].get('metrics', {}).get(metric, 0),
                reverse=True
            )
        
        # Get the best model
        best_model_id, best_model_metadata = sorted_models[0]
        
        logger.info(f"Selected best model: {best_model_id}")
        
        return best_model_id, best_model_metadata
    
    def _load_model_metadata(self) -> None:
        """Load model metadata from disk."""
        try:
            # Get all JSON files in the metadata directory
            metadata_files = [f for f in os.listdir(self.metadata_dir) if f.endswith('.json')]
            
            for metadata_file in metadata_files:
                try:
                    # Extract the model ID from the filename
                    model_id = os.path.splitext(metadata_file)[0]
                    
                    # Load the metadata from disk
                    file_path = os.path.join(self.metadata_dir, metadata_file)
                    
                    with open(file_path, 'r', encoding='utf-8') as f:
                        metadata = json.load(f)
                    
                    # Store the metadata
                    self.model_metadata[model_id] = metadata
                    
                    logger.info(f"Loaded metadata for model: {model_id}")
                
                except Exception as e:
                    logger.error(f"Failed to load metadata from {metadata_file}: {e}")
            
            logger.info(f"Loaded metadata for {len(self.model_metadata)} models from disk")
        
        except Exception as e:
            logger.error(f"Failed to load model metadata from disk: {e}")
