/**
 * Advanced Visualizations Dashboard 2 Example
 * 
 * This example demonstrates the use of additional specialized visualization components.
 */

import React, { useState } from 'react';
import PropTypes from 'prop-types';
import {
  DashboardCard,
  RadarChartVisualization,
  FunnelChartVisualization,
  GaugeChartVisualization,
  CalendarHeatmapVisualization,
  NetworkGraphVisualization,
  TabPanel,
  SkipLink,
  ThemeSelector
} from '../components';
import { ThemeProvider } from '../theme';
import { PreferencesProvider } from '../preferences';
import { OfflineProvider } from '../offline';
import { AnimationProvider } from '../animation';

/**
 * Advanced Visualizations Dashboard 2 Content component
 * 
 * @param {Object} props - Component props
 * @param {Object} props.novaConnect - NovaConnect instance
 * @param {Object} props.novaShield - NovaShield instance
 * @param {Object} props.novaTrack - NovaTrack instance
 * @param {boolean} [props.enableLogging=false] - Whether to enable logging
 * @returns {React.ReactElement} Advanced Visualizations Dashboard 2 Content component
 */
const AdvancedVisualizationsDashboard2Content = ({
  novaConnect,
  novaShield,
  novaTrack,
  enableLogging = false
}) => {
  // State
  const [activeTab, setActiveTab] = useState('radar');
  
  // Sample data for radar chart
  const radarChartData = {
    labels: ['Compliance', 'Security', 'Identity', 'Risk', 'Governance', 'Privacy'],
    datasets: [
      {
        label: 'Current State',
        data: [85, 92, 78, 65, 88, 72],
        backgroundColor: 'rgba(54, 162, 235, 0.2)',
        borderColor: 'rgba(54, 162, 235, 1)',
        borderWidth: 2
      },
      {
        label: 'Target State',
        data: [95, 98, 90, 85, 92, 88],
        backgroundColor: 'rgba(255, 99, 132, 0.2)',
        borderColor: 'rgba(255, 99, 132, 1)',
        borderWidth: 2
      }
    ]
  };
  
  // Sample data for funnel chart
  const funnelChartData = [
    { label: 'Awareness', value: 1000, color: '#3498db' },
    { label: 'Interest', value: 750, color: '#2ecc71' },
    { label: 'Consideration', value: 500, color: '#f1c40f' },
    { label: 'Intent', value: 300, color: '#e67e22' },
    { label: 'Evaluation', value: 200, color: '#e74c3c' },
    { label: 'Purchase', value: 100, color: '#9b59b6' }
  ];
  
  // Sample data for gauge chart
  const gaugeChartValue = 78;
  const gaugeChartThresholds = [
    { value: 33, color: '#e74c3c' },
    { value: 66, color: '#f1c40f' },
    { value: 100, color: '#2ecc71' }
  ];
  
  // Sample data for calendar heatmap
  const calendarHeatmapData = [];
  const today = new Date();
  const oneYearAgo = new Date();
  oneYearAgo.setFullYear(today.getFullYear() - 1);
  
  // Generate random data for the past year
  for (let d = new Date(oneYearAgo); d <= today; d.setDate(d.getDate() + 1)) {
    // More activity on weekdays, less on weekends
    const isWeekend = d.getDay() === 0 || d.getDay() === 6;
    const baseValue = isWeekend ? 2 : 5;
    const randomValue = Math.floor(Math.random() * baseValue);
    
    calendarHeatmapData.push({
      date: new Date(d).toISOString().split('T')[0],
      value: randomValue
    });
  }
  
  // Sample data for network graph
  const networkGraphData = {
    nodes: [
      { id: 'n1', label: 'AWS S3', category: 'storage', size: 15 },
      { id: 'n2', label: 'Azure AD', category: 'identity', size: 12 },
      { id: 'n3', label: 'Salesforce', category: 'crm', size: 18 },
      { id: 'n4', label: 'Google Workspace', category: 'productivity', size: 14 },
      { id: 'n5', label: 'Customer Data', category: 'data', size: 20 },
      { id: 'n6', label: 'Employee Data', category: 'data', size: 16 },
      { id: 'n7', label: 'Payment Processing', category: 'finance', size: 22 }
    ],
    links: [
      { source: 'n1', target: 'n5', weight: 3 },
      { source: 'n1', target: 'n6', weight: 2 },
      { source: 'n2', target: 'n4', weight: 1 },
      { source: 'n2', target: 'n6', weight: 3 },
      { source: 'n3', target: 'n5', weight: 4 },
      { source: 'n4', target: 'n6', weight: 2 },
      { source: 'n5', target: 'n7', weight: 5 },
      { source: 'n6', target: 'n7', weight: 3 }
    ]
  };
  
  // Tabs
  const tabs = [
    {
      id: 'radar',
      label: 'Radar Chart',
      content: (
        <DashboardCard
          title="Cyber-Safety Domain Scores"
          subtitle="Radar chart showing scores across different domains"
        >
          <div className="h-96">
            <RadarChartVisualization
              data={radarChartData}
              options={{
                showLegend: true,
                showLabels: true,
                showGrid: true,
                gridLevels: 5,
                fillArea: true,
                fillOpacity: 0.2,
                showPoints: true,
                showTooltips: true,
                animate: true
              }}
            />
          </div>
        </DashboardCard>
      )
    },
    {
      id: 'funnel',
      label: 'Funnel Chart',
      content: (
        <DashboardCard
          title="Customer Journey Funnel"
          subtitle="Funnel chart showing customer journey stages"
        >
          <div className="h-96">
            <FunnelChartVisualization
              data={funnelChartData}
              options={{
                showLabels: true,
                showValues: true,
                showPercentages: true,
                animate: true,
                valueFormat: 'both',
                direction: 'vertical',
                gradientColors: true
              }}
            />
          </div>
        </DashboardCard>
      )
    },
    {
      id: 'gauge',
      label: 'Gauge Chart',
      content: (
        <DashboardCard
          title="Overall Compliance Score"
          subtitle="Gauge chart showing overall compliance score"
        >
          <div className="h-96">
            <GaugeChartVisualization
              value={gaugeChartValue}
              min={0}
              max={100}
              thresholds={gaugeChartThresholds}
              options={{
                showValue: true,
                showLabel: true,
                label: 'Compliance Score',
                showMinMax: true,
                animate: true,
                animationDuration: 1000,
                thickness: 20,
                startAngle: 135,
                endAngle: 45,
                valueFormat: 'percentage'
              }}
            />
          </div>
        </DashboardCard>
      )
    },
    {
      id: 'calendar',
      label: 'Calendar Heatmap',
      content: (
        <DashboardCard
          title="Activity Calendar"
          subtitle="Calendar heatmap showing activity over time"
        >
          <div className="h-96 overflow-x-auto">
            <CalendarHeatmapVisualization
              data={calendarHeatmapData}
              options={{
                showMonthLabels: true,
                showDayLabels: true,
                showTooltips: true,
                cellSize: 14,
                cellPadding: 2
              }}
            />
          </div>
        </DashboardCard>
      )
    },
    {
      id: 'network',
      label: 'Network Graph',
      content: (
        <DashboardCard
          title="Data Flow Network"
          subtitle="Network graph showing data flow between systems"
        >
          <div className="h-96">
            <NetworkGraphVisualization
              data={networkGraphData}
              options={{
                directed: true,
                weighted: true,
                layout: 'force',
                draggable: true,
                zoomable: true,
                pannable: true,
                showLabels: true,
                showTooltips: true,
                highlightNeighbors: true
              }}
            />
          </div>
        </DashboardCard>
      )
    }
  ];
  
  return (
    <div className="space-y-6">
      {/* Skip link for keyboard navigation */}
      <SkipLink targetId="main-content" />
      
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-textPrimary" id="dashboard-title">
          Advanced Visualizations Dashboard
        </h1>
        
        <ThemeSelector variant="dropdown" />
      </div>
      
      {/* Main content */}
      <main id="main-content" tabIndex="-1">
        <TabPanel
          tabs={tabs}
          defaultTab="radar"
          variant="pills"
          onTabChange={setActiveTab}
        />
      </main>
    </div>
  );
};

AdvancedVisualizationsDashboard2Content.propTypes = {
  novaConnect: PropTypes.object,
  novaShield: PropTypes.object,
  novaTrack: PropTypes.object,
  enableLogging: PropTypes.bool
};

/**
 * Advanced Visualizations Dashboard 2 component
 * 
 * @param {Object} props - Component props
 * @param {Object} props.novaConnect - NovaConnect instance
 * @param {Object} props.novaShield - NovaShield instance
 * @param {Object} props.novaTrack - NovaTrack instance
 * @param {Object} [props.initialTheme] - Initial theme
 * @param {boolean} [props.enableSystemPreference=true] - Whether to enable system preference detection
 * @param {boolean} [props.enableLogging=false] - Whether to enable logging
 * @returns {React.ReactElement} Advanced Visualizations Dashboard 2 component
 */
const AdvancedVisualizationsDashboard2 = ({
  novaConnect,
  novaShield,
  novaTrack,
  initialTheme,
  enableSystemPreference = true,
  enableLogging = false
}) => {
  return (
    <ThemeProvider theme={initialTheme} enableSystemPreference={enableSystemPreference}>
      <PreferencesProvider>
        <OfflineProvider>
          <AnimationProvider>
            <AdvancedVisualizationsDashboard2Content
              novaConnect={novaConnect}
              novaShield={novaShield}
              novaTrack={novaTrack}
              enableLogging={enableLogging}
            />
          </AnimationProvider>
        </OfflineProvider>
      </PreferencesProvider>
    </ThemeProvider>
  );
};

AdvancedVisualizationsDashboard2.propTypes = {
  novaConnect: PropTypes.object,
  novaShield: PropTypes.object,
  novaTrack: PropTypes.object,
  initialTheme: PropTypes.object,
  enableSystemPreference: PropTypes.bool,
  enableLogging: PropTypes.bool
};

export default AdvancedVisualizationsDashboard2;
