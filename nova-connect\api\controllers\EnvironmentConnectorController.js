/**
 * Environment Connector Controller
 * 
 * This controller handles API requests related to environment-specific connectors.
 */

const EnvironmentConnectorService = require('../services/EnvironmentConnectorService');
const { ValidationError } = require('../utils/errors');

class EnvironmentConnectorController {
  constructor() {
    this.environmentConnectorService = new EnvironmentConnectorService();
  }

  /**
   * Get all connectors for an environment
   */
  async getAllConnectors(req, res, next) {
    try {
      const { environmentId } = req.params;
      
      if (!environmentId) {
        throw new ValidationError('Environment ID is required');
      }
      
      const connectors = await this.environmentConnectorService.getAllConnectors(environmentId);
      res.json(connectors);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get connector by ID for an environment
   */
  async getConnectorById(req, res, next) {
    try {
      const { environmentId, connectorId } = req.params;
      
      if (!environmentId) {
        throw new ValidationError('Environment ID is required');
      }
      
      if (!connectorId) {
        throw new ValidationError('Connector ID is required');
      }
      
      const connector = await this.environmentConnectorService.getConnectorById(environmentId, connectorId);
      res.json(connector);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Create a new connector for an environment
   */
  async createConnector(req, res, next) {
    try {
      const { environmentId } = req.params;
      const connectorData = req.body;
      
      if (!environmentId) {
        throw new ValidationError('Environment ID is required');
      }
      
      if (!connectorData) {
        throw new ValidationError('Connector data is required');
      }
      
      const connector = await this.environmentConnectorService.createConnector(environmentId, connectorData);
      res.status(201).json(connector);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update a connector for an environment
   */
  async updateConnector(req, res, next) {
    try {
      const { environmentId, connectorId } = req.params;
      const connectorData = req.body;
      
      if (!environmentId) {
        throw new ValidationError('Environment ID is required');
      }
      
      if (!connectorId) {
        throw new ValidationError('Connector ID is required');
      }
      
      if (!connectorData) {
        throw new ValidationError('Connector data is required');
      }
      
      const connector = await this.environmentConnectorService.updateConnector(environmentId, connectorId, connectorData);
      res.json(connector);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Delete a connector for an environment
   */
  async deleteConnector(req, res, next) {
    try {
      const { environmentId, connectorId } = req.params;
      
      if (!environmentId) {
        throw new ValidationError('Environment ID is required');
      }
      
      if (!connectorId) {
        throw new ValidationError('Connector ID is required');
      }
      
      const result = await this.environmentConnectorService.deleteConnector(environmentId, connectorId);
      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Clone a connector between environments
   */
  async cloneConnector(req, res, next) {
    try {
      const { sourceEnvironmentId, targetEnvironmentId, connectorId } = req.body;
      
      if (!sourceEnvironmentId) {
        throw new ValidationError('Source environment ID is required');
      }
      
      if (!targetEnvironmentId) {
        throw new ValidationError('Target environment ID is required');
      }
      
      if (!connectorId) {
        throw new ValidationError('Connector ID is required');
      }
      
      const connector = await this.environmentConnectorService.cloneConnector(
        sourceEnvironmentId,
        targetEnvironmentId,
        connectorId
      );
      
      res.status(201).json(connector);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Promote all connectors from one environment to another
   */
  async promoteConnectors(req, res, next) {
    try {
      const { sourceEnvironmentId, targetEnvironmentId } = req.body;
      
      if (!sourceEnvironmentId) {
        throw new ValidationError('Source environment ID is required');
      }
      
      if (!targetEnvironmentId) {
        throw new ValidationError('Target environment ID is required');
      }
      
      const results = await this.environmentConnectorService.promoteConnectors(
        sourceEnvironmentId,
        targetEnvironmentId
      );
      
      res.json(results);
    } catch (error) {
      next(error);
    }
  }
}

module.exports = new EnvironmentConnectorController();
