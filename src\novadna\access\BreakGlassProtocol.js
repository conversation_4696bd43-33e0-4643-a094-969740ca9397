/**
 * BreakGlassProtocol.js
 * 
 * This module implements the emergency override capabilities for NovaDNA.
 * It provides a "break glass" mechanism for emergency access when normal
 * authentication procedures cannot be followed, with comprehensive auditing.
 */

const { v4: uuidv4 } = require('uuid');
const EventEmitter = require('events');

/**
 * BreakGlassProtocol class for emergency override capabilities
 */
class BreakGlassProtocol extends EventEmitter {
  constructor(options = {}) {
    super();
    this.overrideTimeout = options.overrideTimeout || 3600; // 1 hour default
    this.reviewRequired = options.reviewRequired !== false;
    this.notificationTargets = options.notificationTargets || [];
    this.activeOverrides = new Map();
    this.overrideLog = [];
    this.maxLogSize = options.maxLogSize || 1000;
    this.severityLevels = options.severityLevels || this._initializeSeverityLevels();
    this.reviewStatuses = options.reviewStatuses || this._initializeReviewStatuses();
  }

  /**
   * Initiate a break-glass override
   * @param {Object} request - The override request
   * @returns {Object} - The override result
   */
  initiateOverride(request) {
    if (!request.serviceId) {
      throw new Error('Service ID is required');
    }

    if (!request.reason) {
      throw new Error('Override reason is required');
    }

    if (!request.emergencyType) {
      throw new Error('Emergency type is required');
    }

    // Generate override ID and token
    const overrideId = uuidv4();
    const overrideToken = this._generateOverrideToken();
    const timestamp = new Date().toISOString();
    const expiresAt = new Date(Date.now() + (this.overrideTimeout * 1000)).toISOString();

    // Determine severity level
    const severityLevel = request.severityLevel || 'HIGH';
    if (!this.severityLevels.find(level => level.id === severityLevel)) {
      throw new Error(`Invalid severity level: ${severityLevel}`);
    }

    // Create override record
    const override = {
      overrideId,
      token: overrideToken,
      serviceId: request.serviceId,
      userId: request.userId,
      reason: request.reason,
      emergencyType: request.emergencyType,
      severityLevel,
      timestamp,
      expiresAt,
      status: 'ACTIVE',
      location: request.location,
      deviceInfo: request.deviceInfo,
      targetProfileId: request.targetProfileId,
      reviewStatus: 'PENDING',
      reviewNotes: []
    };

    // Store in active overrides
    this.activeOverrides.set(overrideId, override);

    // Log the override
    this._logOverride({
      ...override,
      action: 'INITIATED'
    });

    // Send notifications
    this._sendOverrideNotifications(override, 'INITIATED');

    // Schedule automatic expiration
    setTimeout(() => {
      this._expireOverride(overrideId);
    }, this.overrideTimeout * 1000);

    // Return override information
    return {
      overrideId,
      token: overrideToken,
      timestamp,
      expiresAt,
      status: 'ACTIVE',
      message: 'Emergency override initiated successfully'
    };
  }

  /**
   * Validate an override token
   * @param {String} overrideId - The override ID
   * @param {String} token - The override token
   * @returns {Object} - The validation result
   */
  validateOverride(overrideId, token) {
    const override = this.activeOverrides.get(overrideId);

    if (!override) {
      return {
        valid: false,
        error: 'Override not found or expired'
      };
    }

    if (override.token !== token) {
      // Log failed validation
      this._logOverride({
        ...override,
        action: 'VALIDATION_FAILED',
        error: 'Invalid token'
      });

      return {
        valid: false,
        error: 'Invalid override token'
      };
    }

    if (override.status !== 'ACTIVE') {
      return {
        valid: false,
        error: `Override is ${override.status.toLowerCase()}`
      };
    }

    if (new Date() > new Date(override.expiresAt)) {
      // Expire the override
      this._expireOverride(overrideId);

      return {
        valid: false,
        error: 'Override has expired'
      };
    }

    // Log successful validation
    this._logOverride({
      ...override,
      action: 'VALIDATED'
    });

    return {
      valid: true,
      override: {
        overrideId: override.overrideId,
        serviceId: override.serviceId,
        userId: override.userId,
        reason: override.reason,
        emergencyType: override.emergencyType,
        severityLevel: override.severityLevel,
        timestamp: override.timestamp,
        expiresAt: override.expiresAt,
        status: override.status
      }
    };
  }

  /**
   * Complete an override (mark as used)
   * @param {String} overrideId - The override ID
   * @param {String} token - The override token
   * @param {Object} result - The result of using the override
   * @returns {Object} - The completion result
   */
  completeOverride(overrideId, token, result) {
    const validation = this.validateOverride(overrideId, token);

    if (!validation.valid) {
      return {
        success: false,
        error: validation.error
      };
    }

    const override = this.activeOverrides.get(overrideId);

    // Update override status
    override.status = 'COMPLETED';
    override.completedAt = new Date().toISOString();
    override.result = result || {};

    // Log the completion
    this._logOverride({
      ...override,
      action: 'COMPLETED'
    });

    // Send notifications
    this._sendOverrideNotifications(override, 'COMPLETED');

    return {
      success: true,
      overrideId,
      status: 'COMPLETED',
      message: 'Emergency override completed successfully'
    };
  }

  /**
   * Terminate an override (cancel it)
   * @param {String} overrideId - The override ID
   * @param {String} token - The override token
   * @param {String} reason - The reason for termination
   * @returns {Object} - The termination result
   */
  terminateOverride(overrideId, token, reason) {
    const validation = this.validateOverride(overrideId, token);

    if (!validation.valid) {
      return {
        success: false,
        error: validation.error
      };
    }

    const override = this.activeOverrides.get(overrideId);

    // Update override status
    override.status = 'TERMINATED';
    override.terminatedAt = new Date().toISOString();
    override.terminationReason = reason || 'No longer needed';

    // Log the termination
    this._logOverride({
      ...override,
      action: 'TERMINATED'
    });

    // Send notifications
    this._sendOverrideNotifications(override, 'TERMINATED');

    return {
      success: true,
      overrideId,
      status: 'TERMINATED',
      message: 'Emergency override terminated successfully'
    };
  }

  /**
   * Review an override (post-access)
   * @param {String} overrideId - The override ID
   * @param {Object} review - The review information
   * @returns {Object} - The review result
   */
  reviewOverride(overrideId, review) {
    if (!overrideId) {
      throw new Error('Override ID is required');
    }

    if (!review || !review.reviewerId || !review.status) {
      throw new Error('Review information is incomplete');
    }

    const override = this.activeOverrides.get(overrideId);

    if (!override) {
      // Check in log for expired overrides
      const loggedOverride = this.overrideLog.find(log => 
        log.overrideId === overrideId && 
        (log.action === 'INITIATED' || log.action === 'EXPIRED' || 
         log.action === 'COMPLETED' || log.action === 'TERMINATED')
      );

      if (!loggedOverride) {
        return {
          success: false,
          error: 'Override not found'
        };
      }

      // Cannot review active overrides
      if (loggedOverride.status === 'ACTIVE') {
        return {
          success: false,
          error: 'Cannot review active overrides'
        };
      }
    } else if (override.status === 'ACTIVE') {
      return {
        success: false,
        error: 'Cannot review active overrides'
      };
    }

    // Validate review status
    if (!this.reviewStatuses.includes(review.status)) {
      return {
        success: false,
        error: `Invalid review status: ${review.status}`
      };
    }

    // Create review record
    const reviewRecord = {
      reviewId: uuidv4(),
      overrideId,
      reviewerId: review.reviewerId,
      reviewerName: review.reviewerName,
      status: review.status,
      notes: review.notes || '',
      timestamp: new Date().toISOString()
    };

    // Update override if still active
    if (override) {
      override.reviewStatus = review.status;
      override.reviewNotes = override.reviewNotes || [];
      override.reviewNotes.push(reviewRecord);
    }

    // Log the review
    this._logOverride({
      overrideId,
      action: 'REVIEWED',
      review: reviewRecord
    });

    // Send notifications
    this._sendOverrideNotifications(override || { overrideId }, 'REVIEWED', reviewRecord);

    return {
      success: true,
      reviewId: reviewRecord.reviewId,
      status: reviewRecord.status,
      message: 'Emergency override reviewed successfully'
    };
  }

  /**
   * Get override details
   * @param {String} overrideId - The override ID
   * @returns {Object} - The override details
   */
  getOverrideDetails(overrideId) {
    if (!overrideId) {
      throw new Error('Override ID is required');
    }

    // Check active overrides
    const activeOverride = this.activeOverrides.get(overrideId);
    if (activeOverride) {
      // Return without sensitive token
      const { token, ...overrideDetails } = activeOverride;
      return {
        found: true,
        override: overrideDetails
      };
    }

    // Check override log
    const logEntries = this.overrideLog.filter(log => log.overrideId === overrideId);
    if (logEntries.length > 0) {
      // Reconstruct override from log entries
      const initiationEntry = logEntries.find(log => log.action === 'INITIATED');
      if (!initiationEntry) {
        return {
          found: false,
          error: 'Override log is incomplete'
        };
      }

      // Get latest status
      const statusEntries = logEntries.filter(log => 
        ['COMPLETED', 'TERMINATED', 'EXPIRED'].includes(log.action)
      );
      const latestStatusEntry = statusEntries.length > 0 
        ? statusEntries.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))[0]
        : null;

      // Get reviews
      const reviewEntries = logEntries.filter(log => log.action === 'REVIEWED');

      // Construct override details
      const { token, ...overrideDetails } = initiationEntry;
      const details = {
        ...overrideDetails,
        status: latestStatusEntry ? latestStatusEntry.action : 'UNKNOWN',
        reviews: reviewEntries.map(entry => entry.review)
      };

      return {
        found: true,
        override: details
      };
    }

    return {
      found: false,
      error: 'Override not found'
    };
  }

  /**
   * Get override logs
   * @param {Object} options - Options for filtering logs
   * @returns {Array} - The override logs
   */
  getOverrideLogs(options = {}) {
    let filteredLogs = [...this.overrideLog];

    // Apply filters
    if (options.overrideId) {
      filteredLogs = filteredLogs.filter(log => log.overrideId === options.overrideId);
    }

    if (options.serviceId) {
      filteredLogs = filteredLogs.filter(log => log.serviceId === options.serviceId);
    }

    if (options.userId) {
      filteredLogs = filteredLogs.filter(log => log.userId === options.userId);
    }

    if (options.action) {
      filteredLogs = filteredLogs.filter(log => log.action === options.action);
    }

    if (options.status) {
      filteredLogs = filteredLogs.filter(log => log.status === options.status);
    }

    if (options.startDate) {
      const startTimestamp = new Date(options.startDate).getTime();
      filteredLogs = filteredLogs.filter(log => 
        new Date(log.timestamp).getTime() >= startTimestamp
      );
    }

    if (options.endDate) {
      const endTimestamp = new Date(options.endDate).getTime();
      filteredLogs = filteredLogs.filter(log => 
        new Date(log.timestamp).getTime() <= endTimestamp
      );
    }

    // Sort by timestamp (newest first)
    filteredLogs.sort((a, b) => 
      new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    );

    // Apply limit
    if (options.limit) {
      filteredLogs = filteredLogs.slice(0, options.limit);
    }

    // Remove sensitive information
    return filteredLogs.map(log => {
      const { token, ...sanitizedLog } = log;
      return sanitizedLog;
    });
  }

  /**
   * Add notification target
   * @param {Object} target - The notification target
   * @returns {Boolean} - Whether the target was added successfully
   */
  addNotificationTarget(target) {
    if (!target || !target.type || !target.destination) {
      throw new Error('Invalid notification target');
    }

    this.notificationTargets.push(target);
    return true;
  }

  /**
   * Remove notification target
   * @param {String} targetId - The notification target ID
   * @returns {Boolean} - Whether the target was removed successfully
   */
  removeNotificationTarget(targetId) {
    const initialLength = this.notificationTargets.length;
    this.notificationTargets = this.notificationTargets.filter(target => target.id !== targetId);
    return this.notificationTargets.length < initialLength;
  }

  /**
   * Generate an override token
   * @returns {String} - The generated token
   * @private
   */
  _generateOverrideToken() {
    // Generate a secure random token
    return uuidv4().replace(/-/g, '') + uuidv4().replace(/-/g, '');
  }

  /**
   * Expire an override
   * @param {String} overrideId - The override ID
   * @private
   */
  _expireOverride(overrideId) {
    const override = this.activeOverrides.get(overrideId);
    if (!override || override.status !== 'ACTIVE') {
      return;
    }

    // Update override status
    override.status = 'EXPIRED';
    override.expiredAt = new Date().toISOString();

    // Log the expiration
    this._logOverride({
      ...override,
      action: 'EXPIRED'
    });

    // Send notifications
    this._sendOverrideNotifications(override, 'EXPIRED');
  }

  /**
   * Log an override action
   * @param {Object} logEntry - The log entry
   * @private
   */
  _logOverride(logEntry) {
    this.overrideLog.push({
      ...logEntry,
      logId: uuidv4(),
      logTimestamp: new Date().toISOString()
    });

    // Emit log event
    this.emit('override:log', logEntry);

    // Limit log size
    if (this.overrideLog.length > this.maxLogSize) {
      this.overrideLog = this.overrideLog.slice(-this.maxLogSize);
    }
  }

  /**
   * Send override notifications
   * @param {Object} override - The override
   * @param {String} action - The action that triggered the notification
   * @param {Object} additionalData - Additional data to include
   * @private
   */
  _sendOverrideNotifications(override, action, additionalData = {}) {
    // Create notification content
    const notification = {
      type: 'OVERRIDE',
      action,
      overrideId: override.overrideId,
      serviceId: override.serviceId,
      userId: override.userId,
      timestamp: new Date().toISOString(),
      emergencyType: override.emergencyType,
      severityLevel: override.severityLevel,
      ...additionalData
    };

    // Send to all targets
    for (const target of this.notificationTargets) {
      try {
        // In a real implementation, this would send to different notification channels
        // For now, we'll just emit an event
        this.emit('notification:sent', {
          target,
          notification
        });
      } catch (error) {
        this.emit('notification:error', {
          target,
          error: error.message
        });
      }
    }
  }

  /**
   * Initialize severity levels
   * @returns {Array} - The severity levels
   * @private
   */
  _initializeSeverityLevels() {
    return [
      {
        id: 'LOW',
        description: 'Minor emergency, routine override',
        reviewPriority: 'LOW'
      },
      {
        id: 'MEDIUM',
        description: 'Significant emergency, prompt review needed',
        reviewPriority: 'MEDIUM'
      },
      {
        id: 'HIGH',
        description: 'Serious emergency, urgent review needed',
        reviewPriority: 'HIGH'
      },
      {
        id: 'CRITICAL',
        description: 'Life-threatening emergency, immediate review needed',
        reviewPriority: 'CRITICAL'
      }
    ];
  }

  /**
   * Initialize review statuses
   * @returns {Array} - The review statuses
   * @private
   */
  _initializeReviewStatuses() {
    return [
      'APPROVED',
      'REJECTED',
      'FLAGGED',
      'REQUIRES_INVESTIGATION'
    ];
  }
}

module.exports = BreakGlassProtocol;
