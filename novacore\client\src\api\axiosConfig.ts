/**
 * Axios Configuration
 * 
 * This file configures axios with interceptors for:
 * - Adding authentication tokens to requests
 * - Handling token refresh on 401 errors
 * - Global error handling
 */

import axios, { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';
import authService from './authService';

// Create axios instance with default config
const axiosInstance = axios.create({
  baseURL: process.env.REACT_APP_API_URL || '/api/v1',
  timeout: 30000, // 30 seconds
  headers: {
    'Content-Type': 'application/json'
  }
});

// Request interceptor to add auth token
axiosInstance.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    // Skip auth header for login and public endpoints
    const isAuthEndpoint = config.url?.includes('/auth/');
    if (isAuthEndpoint) return config;
    
    const token = authService.getToken();
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    return config;
  },
  (error: AxiosError) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle token refresh
axiosInstance.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  async (error: AxiosError) => {
    const originalRequest = error.config;
    
    // Handle 401 Unauthorized errors (token expired)
    if (error.response?.status === 401 && originalRequest && !originalRequest.headers._retry) {
      // Mark request as retried to prevent infinite loop
      originalRequest.headers._retry = true;
      
      try {
        // Try to refresh token
        const newToken = await authService.refreshToken();
        
        // Update request with new token
        originalRequest.headers.Authorization = `Bearer ${newToken}`;
        
        // Retry the original request
        return axiosInstance(originalRequest);
      } catch (refreshError) {
        // If refresh fails, logout user and redirect to login
        authService.logout();
        return Promise.reject(refreshError);
      }
    }
    
    // Handle other errors
    if (error.response) {
      // Server responded with error status
      const errorMessage = error.response.data?.message || 'An error occurred';
      console.error('API Error:', errorMessage, error.response.status);
      
      // Handle specific error codes
      switch (error.response.status) {
        case 403:
          console.error('Permission denied');
          break;
        case 404:
          console.error('Resource not found');
          break;
        case 500:
          console.error('Server error');
          break;
        default:
          console.error(`Error ${error.response.status}`);
      }
    } else if (error.request) {
      // Request made but no response received
      console.error('No response received:', error.request);
    } else {
      // Error setting up request
      console.error('Request error:', error.message);
    }
    
    return Promise.reject(error);
  }
);

export default axiosInstance;
