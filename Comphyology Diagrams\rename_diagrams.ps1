# Rename diagram files to include figure numbers
$basePath = "D:\novafuse-api-superstore\Comphyology Diagrams\Mermaid"

# Mapping of current filenames to figure numbers
$fileMap = @{
    "alignment_architecture.mmd" = "FIG1"
    "uuft_core_architecture.mmd" = "FIG2"
    "FIG3_zero_entropy_law.mmd" = "FIG3"  # Already renamed
    "tee_equation.mmd" = "FIG4"
    "nepi_analysis_pipeline.mmd" = "FIG5"
    "efficiency_formula.mmd" = "FIG6"
    "12_plus_1_novas.mmd" = "FIG7"
    "nova_fuse_universal_stack.mmd" = "FIG8"
    "nova_align_studio.mmd" = "FIG9"
    "cadence_governance_loop.mmd" = "FIG10"
    "application_data_layer.mmd" = "FIG11"
    "three_body_problem_reframing.mmd" = "FIG12"
    "protein_folding.mmd" = "FIG13"
    "finite_universe_paradigm_visualization.mmd" = "FIG14"
    "cross_module_data_processing_pipeline.mmd" = "FIG15"
    "healthcare_implementation.mmd" = "FIG16"
    "finite_universe_principle.mmd" = "FIG17"
    "dark_field_classification.mmd" = "FIG18"
    "consciousness_threshold.mmd" = "FIG19"
    "principle_18_82.mmd" = "FIG20"
    "ai_alignment_case.mmd" = "FIG21"
}

# Rename each file
foreach ($file in Get-ChildItem -Path $basePath -Filter "*.mmd") {
    $baseName = $file.Name
    
    # Skip if already renamed
    if ($baseName -match "^FIG\d+") {
        Write-Host "Skipping already renamed file: $baseName"
        continue
    }
    
    if ($fileMap.ContainsKey($baseName)) {
        $newName = "$($fileMap[$baseName])_$baseName"
        $newPath = Join-Path -Path $basePath -ChildPath $newName
        
        Write-Host "Renaming $($file.FullName) to $newName"
        Rename-Item -Path $file.FullName -NewName $newName -Force
    } else {
        Write-Warning "No figure number mapping found for: $baseName"
    }
}

Write-Host "File renaming complete!"
