/**
 * Monitoring Middleware
 * 
 * This middleware provides monitoring functionality for NovaConnect UAC.
 */

const logger = require('../utils/logger');
const MonitoringService = require('../services/MonitoringService');

/**
 * Request monitoring middleware
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const requestMonitoringMiddleware = (req, res, next) => {
  // Record start time
  const startTime = Date.now();
  
  // Store original end method
  const originalEnd = res.end;
  
  // Override end method
  res.end = function(chunk, encoding) {
    // Calculate request duration
    const duration = Date.now() - startTime;
    
    // Log request
    logger.info('Request completed', {
      method: req.method,
      path: req.path,
      status: res.statusCode,
      duration: duration,
      requestId: req.requestId
    });
    
    // Call original end method
    return originalEnd.call(this, chunk, encoding);
  };
  
  next();
};

/**
 * Metrics endpoint
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const metricsEndpoint = (req, res) => {
  try {
    // Get system metrics
    const metrics = {
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      cpu: process.cpuUsage(),
      timestamp: Date.now()
    };
    
    res.json({
      status: 'ok',
      metrics
    });
  } catch (error) {
    logger.error('Error getting metrics', { error });
    
    res.status(500).json({
      status: 'error',
      message: 'Failed to get metrics',
      error: error.message
    });
  }
};

/**
 * Health check endpoint
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const healthCheckEndpoint = (req, res) => {
  try {
    // Get health status
    const health = {
      status: 'ok',
      uptime: process.uptime(),
      timestamp: Date.now(),
      version: process.env.npm_package_version || '1.0.0'
    };
    
    res.json(health);
  } catch (error) {
    logger.error('Error getting health status', { error });
    
    res.status(500).json({
      status: 'error',
      message: 'Failed to get health status',
      error: error.message
    });
  }
};

module.exports = {
  requestMonitoringMiddleware,
  metricsEndpoint,
  healthCheckEndpoint
};
