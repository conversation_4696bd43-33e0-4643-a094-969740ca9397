import React from 'react';
import DiagramTemplate from '../DiagramTemplate';

// Define the diagram data
const elements = [
  {
    id: 'meta-field-schema',
    top: 50,
    left: 300,
    width: 300,
    text: 'Meta-Field Schema',
    number: '1',
    bold: true,
    fontSize: '20px',
    backgroundColor: '#f9f0ff'
  },
  // Four fundamental dimensions
  {
    id: 'governance-layer',
    top: 150,
    left: 100,
    width: 200,
    text: 'G (Governance Layer)',
    number: '2',
    bold: true,
    fontSize: '16px',
    backgroundColor: '#e6f7ff'
  },
  {
    id: 'data-layer',
    top: 150,
    left: 350,
    width: 200,
    text: 'D (Data Layer)',
    number: '3',
    bold: true,
    fontSize: '16px',
    backgroundColor: '#f6ffed'
  },
  {
    id: 'response-layer',
    top: 150,
    left: 600,
    width: 200,
    text: 'R (Response/Action Layer)',
    number: '4',
    bold: true,
    fontSize: '16px',
    backgroundColor: '#fff2e8'
  },
  {
    id: 'trust-factor',
    top: 250,
    left: 350,
    width: 200,
    text: 'π (Trust Factor)',
    number: '5',
    bold: true,
    fontSize: '16px',
    backgroundColor: '#fff0f6'
  },
  // Descriptions
  {
    id: 'governance-desc',
    top: 200,
    left: 100,
    width: 200,
    text: 'Structures, rules, principles, and authorities',
    fontSize: '12px',
    backgroundColor: '#e6f7ff'
  },
  {
    id: 'data-desc',
    top: 200,
    left: 350,
    width: 200,
    text: 'Flow, content, quality, and characteristics of information',
    fontSize: '12px',
    backgroundColor: '#f6ffed'
  },
  {
    id: 'response-desc',
    top: 200,
    left: 600,
    width: 200,
    text: 'Behaviors, actions, feedback loops, and adaptive mechanisms',
    fontSize: '12px',
    backgroundColor: '#fff2e8'
  },
  {
    id: 'trust-desc',
    top: 300,
    left: 350,
    width: 200,
    text: 'System stability, transparency, integrity, and coherent evolution',
    fontSize: '12px',
    backgroundColor: '#fff0f6'
  },
  // Equation
  {
    id: 'meta-field-equation',
    top: 350,
    left: 300,
    width: 300,
    text: 'Meta-Field = ∑(Gₙ⊗Dₙ⊗Rₙ)·πⁿ',
    number: '6',
    bold: true,
    fontSize: '16px',
    backgroundColor: '#fffbe6'
  },
  // Implementation components
  {
    id: 'layer-abstraction',
    top: 450,
    left: 100,
    width: 150,
    text: 'Layer Abstraction Engine',
    number: '7',
    fontSize: '12px',
    backgroundColor: '#f9f0ff'
  },
  {
    id: 'cross-layer',
    top: 450,
    left: 300,
    width: 150,
    text: 'Cross-Layer Integration Module',
    number: '8',
    fontSize: '12px',
    backgroundColor: '#f9f0ff'
  },
  {
    id: 'pi-weighted',
    top: 450,
    left: 500,
    width: 150,
    text: 'π-Weighted Aggregator',
    number: '9',
    fontSize: '12px',
    backgroundColor: '#f9f0ff'
  },
  {
    id: 'universal-rep',
    top: 450,
    left: 700,
    width: 150,
    text: 'Universal Representation Generator',
    number: '10',
    fontSize: '12px',
    backgroundColor: '#f9f0ff'
  },
  {
    id: 'implementation',
    top: 550,
    left: 300,
    width: 300,
    text: 'Technical Implementation: Schema Processing System',
    number: '11',
    bold: true,
    fontSize: '16px',
    backgroundColor: '#fffbe6'
  }
];

const connections = [
  // Connect Meta-Field Schema to dimensions
  {
    start: { x: 350, y: 100 },
    end: { x: 200, y: 150 },
    type: 'arrow'
  },
  {
    start: { x: 450, y: 100 },
    end: { x: 450, y: 150 },
    type: 'arrow'
  },
  {
    start: { x: 550, y: 100 },
    end: { x: 700, y: 150 },
    type: 'arrow'
  },
  // Connect dimensions to descriptions
  {
    start: { x: 200, y: 175 },
    end: { x: 200, y: 200 },
    type: 'line'
  },
  {
    start: { x: 450, y: 175 },
    end: { x: 450, y: 200 },
    type: 'line'
  },
  {
    start: { x: 700, y: 175 },
    end: { x: 700, y: 200 },
    type: 'line'
  },
  // Connect to Trust Factor
  {
    start: { x: 200, y: 225 },
    end: { x: 350, y: 250 },
    type: 'arrow'
  },
  {
    start: { x: 450, y: 225 },
    end: { x: 450, y: 250 },
    type: 'arrow'
  },
  {
    start: { x: 700, y: 225 },
    end: { x: 550, y: 250 },
    type: 'arrow'
  },
  // Connect Trust Factor to description
  {
    start: { x: 450, y: 275 },
    end: { x: 450, y: 300 },
    type: 'line'
  },
  // Connect to equation
  {
    start: { x: 200, y: 225 },
    end: { x: 300, y: 350 },
    type: 'arrow',
    dashed: true
  },
  {
    start: { x: 450, y: 325 },
    end: { x: 450, y: 350 },
    type: 'arrow',
    dashed: true
  },
  {
    start: { x: 700, y: 225 },
    end: { x: 600, y: 350 },
    type: 'arrow',
    dashed: true
  },
  // Connect equation to implementation components
  {
    start: { x: 350, y: 400 },
    end: { x: 175, y: 450 },
    type: 'arrow'
  },
  {
    start: { x: 425, y: 400 },
    end: { x: 375, y: 450 },
    type: 'arrow'
  },
  {
    start: { x: 475, y: 400 },
    end: { x: 575, y: 450 },
    type: 'arrow'
  },
  {
    start: { x: 550, y: 400 },
    end: { x: 775, y: 450 },
    type: 'arrow'
  },
  // Connect implementation components to implementation label
  {
    start: { x: 175, y: 500 },
    end: { x: 300, y: 550 },
    type: 'line'
  },
  {
    start: { x: 375, y: 500 },
    end: { x: 375, y: 550 },
    type: 'line'
  },
  {
    start: { x: 575, y: 500 },
    end: { x: 525, y: 550 },
    type: 'line'
  },
  {
    start: { x: 775, y: 500 },
    end: { x: 600, y: 550 },
    type: 'line'
  }
];

const MetaFieldSchema: React.FC = () => {
  return (
    <DiagramTemplate 
      elements={elements} 
      connections={connections} 
      width="900px" 
      height="650px" 
    />
  );
};

export default MetaFieldSchema;
