version: '3'

services:
  nova-connect:
    build:
      context: .
      dockerfile: Dockerfile.test
    ports:
      - "3001:3001"
    volumes:
      - ./src:/app/src
      - ./public:/app/public
      - ./server.js:/app/server.js
      - ./test-monitoring.js:/app/test-monitoring.js
    environment:
      - NODE_ENV=development
      - PORT=3001
      - LOG_LEVEL=debug
    command: node test-monitoring.js
