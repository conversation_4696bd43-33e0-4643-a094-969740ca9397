/**
 * NovaFuse Universal API Connector Transformation Functions
 * 
 * This module provides a set of transformation functions for mapping data
 * between API responses and NovaGRC entities.
 */

class TransformationFunctions {
  constructor() {
    // Register all transformation functions
    this.functions = {
      // Basic transformations
      'identity': this.identity.bind(this),
      'formatDate': this.formatDate.bind(this),
      'toUpperCase': this.toUpperCase.bind(this),
      'toLowerCase': this.toLowerCase.bind(this),
      'trim': this.trim.bind(this),
      'concat': this.concat.bind(this),
      'split': this.split.bind(this),
      'replace': this.replace.bind(this),
      'substring': this.substring.bind(this),
      'toNumber': this.toNumber.bind(this),
      'toBoolean': this.toBoolean.bind(this),
      'toString': this.toString.bind(this),
      
      // Domain-specific transformations
      'mapComplianceStatus': this.mapComplianceStatus.bind(this),
      'mapSeverityToRisk': this.mapSeverityToRisk.bind(this),
      'extractResourceDetails': this.extractResourceDetails.bind(this),
      'extractAssetDetails': this.extractAssetDetails.bind(this),
      'mapJiraStatusToCompliance': this.mapJiraStatusToCompliance.bind(this),
      'mapJiraPriorityToRisk': this.mapJiraPriorityToRisk.bind(this)
    };
  }

  /**
   * Get a transformation function by name
   * 
   * @param {string} name - The name of the transformation function
   * @returns {Function} - The transformation function
   */
  getFunction(name) {
    const func = this.functions[name];
    
    if (!func) {
      throw new Error(`Transformation function '${name}' not found`);
    }
    
    return func;
  }

  /**
   * Apply a transformation function to a value
   * 
   * @param {string} functionName - The name of the transformation function
   * @param {any} value - The value to transform
   * @param {Object} parameters - Additional parameters for the transformation
   * @returns {any} - The transformed value
   */
  applyTransformation(functionName, value, parameters = {}) {
    const func = this.getFunction(functionName || 'identity');
    return func(value, parameters);
  }

  // ===== Basic Transformations =====

  /**
   * Identity transformation (returns the value unchanged)
   * 
   * @param {any} value - The value to transform
   * @returns {any} - The original value
   */
  identity(value) {
    return value;
  }

  /**
   * Format a date string
   * 
   * @param {string} value - The date string to format
   * @param {Object} parameters - Transformation parameters
   * @param {string} parameters.format - The output format (default: 'ISO')
   * @returns {string} - The formatted date string
   */
  formatDate(value, parameters = {}) {
    if (!value) return null;
    
    const format = parameters.format || 'ISO';
    const date = new Date(value);
    
    if (isNaN(date.getTime())) {
      return value; // Return original value if it's not a valid date
    }
    
    switch (format) {
      case 'ISO':
        return date.toISOString();
      case 'UTC':
        return date.toUTCString();
      case 'local':
        return date.toLocaleString();
      case 'date':
        return date.toLocaleDateString();
      case 'time':
        return date.toLocaleTimeString();
      case 'timestamp':
        return date.getTime().toString();
      default:
        return date.toISOString();
    }
  }

  /**
   * Convert a string to uppercase
   * 
   * @param {string} value - The string to convert
   * @returns {string} - The uppercase string
   */
  toUpperCase(value) {
    if (typeof value !== 'string') return value;
    return value.toUpperCase();
  }

  /**
   * Convert a string to lowercase
   * 
   * @param {string} value - The string to convert
   * @returns {string} - The lowercase string
   */
  toLowerCase(value) {
    if (typeof value !== 'string') return value;
    return value.toLowerCase();
  }

  /**
   * Trim whitespace from a string
   * 
   * @param {string} value - The string to trim
   * @returns {string} - The trimmed string
   */
  trim(value) {
    if (typeof value !== 'string') return value;
    return value.trim();
  }

  /**
   * Concatenate strings
   * 
   * @param {string|Array} value - The string or array of strings to concatenate
   * @param {Object} parameters - Transformation parameters
   * @param {string} parameters.separator - The separator to use (default: '')
   * @param {string} parameters.prefix - The prefix to add (default: '')
   * @param {string} parameters.suffix - The suffix to add (default: '')
   * @returns {string} - The concatenated string
   */
  concat(value, parameters = {}) {
    const separator = parameters.separator || '';
    const prefix = parameters.prefix || '';
    const suffix = parameters.suffix || '';
    
    if (Array.isArray(value)) {
      return prefix + value.join(separator) + suffix;
    }
    
    return prefix + value + suffix;
  }

  /**
   * Split a string into an array
   * 
   * @param {string} value - The string to split
   * @param {Object} parameters - Transformation parameters
   * @param {string} parameters.separator - The separator to use (default: ',')
   * @returns {Array} - The split string as an array
   */
  split(value, parameters = {}) {
    if (typeof value !== 'string') return value;
    
    const separator = parameters.separator || ',';
    return value.split(separator);
  }

  /**
   * Replace text in a string
   * 
   * @param {string} value - The string to modify
   * @param {Object} parameters - Transformation parameters
   * @param {string} parameters.search - The text to search for
   * @param {string} parameters.replace - The replacement text
   * @param {boolean} parameters.global - Whether to replace all occurrences (default: true)
   * @returns {string} - The modified string
   */
  replace(value, parameters = {}) {
    if (typeof value !== 'string') return value;
    
    const search = parameters.search || '';
    const replace = parameters.replace || '';
    const global = parameters.global !== false;
    
    if (global) {
      const regex = new RegExp(search, 'g');
      return value.replace(regex, replace);
    }
    
    return value.replace(search, replace);
  }

  /**
   * Extract a substring
   * 
   * @param {string} value - The string to extract from
   * @param {Object} parameters - Transformation parameters
   * @param {number} parameters.start - The start index
   * @param {number} parameters.end - The end index
   * @returns {string} - The extracted substring
   */
  substring(value, parameters = {}) {
    if (typeof value !== 'string') return value;
    
    const start = parameters.start || 0;
    const end = parameters.end;
    
    return value.substring(start, end);
  }

  /**
   * Convert a value to a number
   * 
   * @param {any} value - The value to convert
   * @returns {number} - The converted number
   */
  toNumber(value) {
    if (typeof value === 'number') return value;
    
    const num = Number(value);
    return isNaN(num) ? 0 : num;
  }

  /**
   * Convert a value to a boolean
   * 
   * @param {any} value - The value to convert
   * @returns {boolean} - The converted boolean
   */
  toBoolean(value) {
    if (typeof value === 'boolean') return value;
    
    if (typeof value === 'string') {
      const lowerValue = value.toLowerCase();
      return lowerValue === 'true' || lowerValue === 'yes' || lowerValue === '1';
    }
    
    return Boolean(value);
  }

  /**
   * Convert a value to a string
   * 
   * @param {any} value - The value to convert
   * @returns {string} - The converted string
   */
  toString(value) {
    if (value === null || value === undefined) return '';
    if (typeof value === 'object') return JSON.stringify(value);
    return String(value);
  }

  // ===== Domain-Specific Transformations =====

  /**
   * Map AWS Security Hub compliance status to NovaGRC compliance status
   * 
   * @param {string} value - The AWS compliance status
   * @returns {string} - The NovaGRC compliance status
   */
  mapComplianceStatus(value) {
    const statusMap = {
      'PASSED': 'Compliant',
      'FAILED': 'Non-Compliant',
      'WARNING': 'Partially Compliant',
      'NOT_AVAILABLE': 'Unknown'
    };
    
    return statusMap[value] || 'Unknown';
  }

  /**
   * Map severity to risk level
   * 
   * @param {string} value - The severity label
   * @returns {string} - The risk level
   */
  mapSeverityToRisk(value) {
    const riskMap = {
      'CRITICAL': 'Critical',
      'HIGH': 'High',
      'MEDIUM': 'Medium',
      'LOW': 'Low',
      'INFORMATIONAL': 'Informational'
    };
    
    return riskMap[value] || 'Unknown';
  }

  /**
   * Extract resource details from AWS Security Hub findings
   * 
   * @param {Array} value - The resources array
   * @returns {Array} - The extracted resource details
   */
  extractResourceDetails(value) {
    if (!Array.isArray(value)) return [];
    
    return value.map(resource => ({
      id: resource.Id,
      type: resource.Type,
      name: resource.Details?.AwsEc2Instance?.InstanceId || resource.Id,
      region: resource.Region,
      tags: resource.Tags
    }));
  }

  /**
   * Extract asset details from AWS Security Hub findings
   * 
   * @param {Array} value - The resources array
   * @returns {Array} - The extracted asset details
   */
  extractAssetDetails(value) {
    if (!Array.isArray(value)) return [];
    
    return value.map(resource => ({
      assetId: resource.Id,
      assetType: resource.Type,
      assetName: resource.Details?.AwsEc2Instance?.InstanceId || resource.Id,
      location: resource.Region,
      tags: resource.Tags
    }));
  }

  /**
   * Map Jira status to compliance status
   * 
   * @param {string} value - The Jira status
   * @returns {string} - The compliance status
   */
  mapJiraStatusToCompliance(value) {
    const statusMap = {
      'To Do': 'Not Started',
      'In Progress': 'In Progress',
      'Done': 'Completed',
      'Blocked': 'Blocked',
      'Backlog': 'Planned',
      'Selected for Development': 'Planned',
      'Ready for Review': 'In Review',
      'Closed': 'Completed'
    };
    
    return statusMap[value] || value;
  }

  /**
   * Map Jira priority to risk level
   * 
   * @param {string} value - The Jira priority
   * @returns {string} - The risk level
   */
  mapJiraPriorityToRisk(value) {
    const priorityMap = {
      'Highest': 'Critical',
      'High': 'High',
      'Medium': 'Medium',
      'Low': 'Low',
      'Lowest': 'Informational'
    };
    
    return priorityMap[value] || value;
  }
}

// Create and export a singleton instance
const transformationFunctions = new TransformationFunctions();

module.exports = transformationFunctions;
