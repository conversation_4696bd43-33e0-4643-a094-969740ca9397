/**
 * LanguageSelector Component
 * 
 * A component for selecting the application language.
 */

import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { useI18n } from '../i18n/I18nContext';
import { useTheme } from '../theme/ThemeContext';
import { Animated } from './Animated';

/**
 * LanguageSelector component
 * 
 * @param {Object} props - Component props
 * @param {string} [props.variant='dropdown'] - Variant ('dropdown', 'buttons', or 'select')
 * @param {string} [props.size='md'] - Size ('sm', 'md', or 'lg')
 * @param {boolean} [props.showFlags=true] - Whether to show flags
 * @param {boolean} [props.showNames=true] - Whether to show names
 * @param {string} [props.className] - Additional CSS class names
 * @param {Object} [props.style] - Additional inline styles
 * @returns {React.ReactElement} LanguageSelector component
 */
const LanguageSelector = ({
  variant = 'dropdown',
  size = 'md',
  showFlags = true,
  showNames = true,
  className = '',
  style = {}
}) => {
  const { theme } = useTheme();
  const {
    locale,
    availableLocales,
    changeLocale,
    translate,
    isLoading
  } = useI18n();
  
  // State
  const [isOpen, setIsOpen] = useState(false);
  
  // Get current locale
  const currentLocale = availableLocales.find(l => l.code === locale) || availableLocales[0];
  
  // Get flag emoji
  const getFlag = (localeCode) => {
    const countryCode = localeCode.split('-')[1];
    
    // Convert country code to flag emoji
    if (countryCode) {
      const codePoints = countryCode
        .toUpperCase()
        .split('')
        .map(char => 127397 + char.charCodeAt(0));
      
      return String.fromCodePoint(...codePoints);
    }
    
    return '';
  };
  
  // Handle locale change
  const handleLocaleChange = (localeCode) => {
    changeLocale(localeCode);
    setIsOpen(false);
  };
  
  // Dropdown variant
  if (variant === 'dropdown') {
    return (
      <div
        className={`relative ${className}`}
        style={style}
        data-testid="language-selector-dropdown"
      >
        <button
          type="button"
          className={`flex items-center justify-between rounded-md border border-divider bg-background text-textPrimary hover:bg-surface transition-colors duration-200 ${
            size === 'sm' ? 'px-2 py-1 text-xs' : size === 'lg' ? 'px-4 py-2 text-base' : 'px-3 py-1.5 text-sm'
          } ${isOpen ? 'bg-surface' : ''}`}
          onClick={() => setIsOpen(!isOpen)}
          aria-haspopup="listbox"
          aria-expanded={isOpen}
          disabled={isLoading}
        >
          <span className="flex items-center">
            {showFlags && (
              <span className="mr-2 text-lg" aria-hidden="true">
                {getFlag(currentLocale.code)}
              </span>
            )}
            {showNames && (
              <span>{currentLocale.name}</span>
            )}
          </span>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className={`ml-2 h-4 w-4 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fillRule="evenodd"
              d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
              clipRule="evenodd"
            />
          </svg>
        </button>
        
        {isOpen && (
          <Animated
            animation="fadeIn"
            className="absolute right-0 mt-1 w-48 rounded-md border border-divider bg-background shadow-lg z-10"
          >
            <ul
              className="py-1 max-h-60 overflow-auto"
              role="listbox"
              aria-labelledby="language-selector"
              tabIndex="-1"
            >
              {availableLocales.map((localeOption) => (
                <li
                  key={localeOption.code}
                  role="option"
                  aria-selected={localeOption.code === locale}
                  className={`px-3 py-2 cursor-pointer flex items-center ${
                    localeOption.code === locale
                      ? 'bg-primary bg-opacity-10 text-primary'
                      : 'text-textPrimary hover:bg-surface'
                  }`}
                  onClick={() => handleLocaleChange(localeOption.code)}
                >
                  {showFlags && (
                    <span className="mr-2 text-lg" aria-hidden="true">
                      {getFlag(localeOption.code)}
                    </span>
                  )}
                  {showNames && (
                    <span>{localeOption.name}</span>
                  )}
                </li>
              ))}
            </ul>
          </Animated>
        )}
      </div>
    );
  }
  
  // Buttons variant
  if (variant === 'buttons') {
    return (
      <div
        className={`flex space-x-1 ${className}`}
        style={style}
        data-testid="language-selector-buttons"
        role="group"
        aria-label={translate('common.selectLanguage', { defaultValue: 'Select language' })}
      >
        {availableLocales.map((localeOption) => (
          <button
            key={localeOption.code}
            type="button"
            className={`flex items-center justify-center rounded-md border ${
              localeOption.code === locale
                ? 'bg-primary text-primaryContrast border-primary'
                : 'bg-background text-textPrimary border-divider hover:bg-surface'
            } ${
              size === 'sm' ? 'px-2 py-1 text-xs' : size === 'lg' ? 'px-4 py-2 text-base' : 'px-3 py-1.5 text-sm'
            } transition-colors duration-200`}
            onClick={() => handleLocaleChange(localeOption.code)}
            aria-pressed={localeOption.code === locale}
            disabled={isLoading}
          >
            {showFlags && (
              <span className={showNames ? 'mr-2 text-lg' : 'text-lg'} aria-hidden="true">
                {getFlag(localeOption.code)}
              </span>
            )}
            {showNames && (
              <span>{localeOption.name}</span>
            )}
          </button>
        ))}
      </div>
    );
  }
  
  // Select variant
  return (
    <div
      className={`${className}`}
      style={style}
      data-testid="language-selector-select"
    >
      <select
        className={`rounded-md border border-divider bg-background text-textPrimary ${
          size === 'sm' ? 'px-2 py-1 text-xs' : size === 'lg' ? 'px-4 py-2 text-base' : 'px-3 py-1.5 text-sm'
        }`}
        value={locale}
        onChange={(e) => handleLocaleChange(e.target.value)}
        aria-label={translate('common.selectLanguage', { defaultValue: 'Select language' })}
        disabled={isLoading}
      >
        {availableLocales.map((localeOption) => (
          <option key={localeOption.code} value={localeOption.code}>
            {showFlags && getFlag(localeOption.code)} {showNames && localeOption.name}
          </option>
        ))}
      </select>
    </div>
  );
};

LanguageSelector.propTypes = {
  variant: PropTypes.oneOf(['dropdown', 'buttons', 'select']),
  size: PropTypes.oneOf(['sm', 'md', 'lg']),
  showFlags: PropTypes.bool,
  showNames: PropTypes.bool,
  className: PropTypes.string,
  style: PropTypes.object
};

export default LanguageSelector;
