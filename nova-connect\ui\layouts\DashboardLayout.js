/**
 * Dashboard Layout Component
 * 
 * This component provides the layout for dashboard pages.
 */

import React, { useState } from 'react';
import { 
  AppBar, 
  Box, 
  Container, 
  CssBaseline, 
  Divider, 
  Drawer, 
  IconButton, 
  List, 
  ListItem, 
  ListItemIcon, 
  ListItemText, 
  Toolbar, 
  Typography 
} from '@mui/material';
import MenuIcon from '@mui/icons-material/Menu';
import DashboardIcon from '@mui/icons-material/Dashboard';
import ApiIcon from '@mui/icons-material/Api';
import SettingsIcon from '@mui/icons-material/Settings';
import MonitoringIcon from '@mui/icons-material/Monitoring';
import BarChartIcon from '@mui/icons-material/BarChart';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { EnvironmentProvider } from '../contexts/EnvironmentContext';
import EnvironmentSelector from '../components/environments/EnvironmentSelector';
import EnvironmentManagementDialog from '../components/environments/EnvironmentManagementDialog';

const drawerWidth = 240;

const DashboardLayout = ({ children }) => {
  const router = useRouter();
  const [mobileOpen, setMobileOpen] = useState(false);
  const [environmentDialogOpen, setEnvironmentDialogOpen] = useState(false);
  
  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };
  
  const isActive = (path) => {
    return router.pathname === path || router.pathname.startsWith(`${path}/`);
  };
  
  const drawer = (
    <div>
      <Toolbar>
        <Typography variant="h6" noWrap>
          NovaConnect
        </Typography>
      </Toolbar>
      <Divider />
      <List>
        <Link href="/dashboard" passHref>
          <ListItem button component="a" selected={isActive('/dashboard')}>
            <ListItemIcon>
              <DashboardIcon color={isActive('/dashboard') ? 'primary' : 'inherit'} />
            </ListItemIcon>
            <ListItemText primary="Dashboard" />
          </ListItem>
        </Link>
        
        <Link href="/connectors" passHref>
          <ListItem button component="a" selected={isActive('/connectors')}>
            <ListItemIcon>
              <ApiIcon color={isActive('/connectors') ? 'primary' : 'inherit'} />
            </ListItemIcon>
            <ListItemText primary="Connectors" />
          </ListItem>
        </Link>
        
        <Link href="/testing" passHref>
          <ListItem button component="a" selected={isActive('/testing')}>
            <ListItemIcon>
              <ApiIcon color={isActive('/testing') ? 'primary' : 'inherit'} />
            </ListItemIcon>
            <ListItemText primary="Testing" />
          </ListItem>
        </Link>
        
        <Link href="/monitoring" passHref>
          <ListItem button component="a" selected={isActive('/monitoring')}>
            <ListItemIcon>
              <MonitoringIcon color={isActive('/monitoring') ? 'primary' : 'inherit'} />
            </ListItemIcon>
            <ListItemText primary="Monitoring" />
          </ListItem>
        </Link>
        
        <Link href="/analytics" passHref>
          <ListItem button component="a" selected={isActive('/analytics')}>
            <ListItemIcon>
              <BarChartIcon color={isActive('/analytics') ? 'primary' : 'inherit'} />
            </ListItemIcon>
            <ListItemText primary="Analytics" />
          </ListItem>
        </Link>
        
        <Link href="/graphql" passHref>
          <ListItem button component="a" selected={isActive('/graphql')}>
            <ListItemIcon>
              <ApiIcon color={isActive('/graphql') ? 'primary' : 'inherit'} />
            </ListItemIcon>
            <ListItemText primary="GraphQL" />
          </ListItem>
        </Link>
      </List>
      <Divider />
      <List>
        <Link href="/settings" passHref>
          <ListItem button component="a" selected={isActive('/settings')}>
            <ListItemIcon>
              <SettingsIcon color={isActive('/settings') ? 'primary' : 'inherit'} />
            </ListItemIcon>
            <ListItemText primary="Settings" />
          </ListItem>
        </Link>
      </List>
    </div>
  );
  
  return (
    <EnvironmentProvider>
      <Box sx={{ display: 'flex' }}>
        <CssBaseline />
        <AppBar
          position="fixed"
          sx={{
            width: { sm: `calc(100% - ${drawerWidth}px)` },
            ml: { sm: `${drawerWidth}px` },
          }}
        >
          <Toolbar>
            <IconButton
              color="inherit"
              aria-label="open drawer"
              edge="start"
              onClick={handleDrawerToggle}
              sx={{ mr: 2, display: { sm: 'none' } }}
            >
              <MenuIcon />
            </IconButton>
            <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1 }}>
              NovaConnect
            </Typography>
            
            <EnvironmentSelector onManageClick={() => setEnvironmentDialogOpen(true)} />
          </Toolbar>
        </AppBar>
        <Box
          component="nav"
          sx={{ width: { sm: drawerWidth }, flexShrink: { sm: 0 } }}
          aria-label="mailbox folders"
        >
          {/* The implementation can be swapped with js to avoid SEO duplication of links. */}
          <Drawer
            variant="temporary"
            open={mobileOpen}
            onClose={handleDrawerToggle}
            ModalProps={{
              keepMounted: true, // Better open performance on mobile.
            }}
            sx={{
              display: { xs: 'block', sm: 'none' },
              '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth },
            }}
          >
            {drawer}
          </Drawer>
          <Drawer
            variant="permanent"
            sx={{
              display: { xs: 'none', sm: 'block' },
              '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth },
            }}
            open
          >
            {drawer}
          </Drawer>
        </Box>
        <Box
          component="main"
          sx={{ flexGrow: 1, p: 3, width: { sm: `calc(100% - ${drawerWidth}px)` } }}
        >
          <Toolbar />
          {children}
        </Box>
        
        <EnvironmentManagementDialog
          open={environmentDialogOpen}
          onClose={() => setEnvironmentDialogOpen(false)}
        />
      </Box>
    </EnvironmentProvider>
  );
};

export default DashboardLayout;
