// Dashboard JavaScript for Quantum Protein Folding

class Dashboard {
    constructor() {
        // Initialize Socket.IO connection
        this.socket = io();
        this.currentFilter = 'all';
        this.currentPage = 1;
        this.experimentsPerPage = 10;
        this.experiments = [];
        this.filteredExperiments = [];
        this.systemInfo = {};
        this.quantumBackends = [];
        
        // Initialize the dashboard
        this.initializeEventListeners();
        this.loadInitialData();
        this.setupWebSocketHandlers();
        this.updateLastUpdated();
        
        // Start periodic updates
        setInterval(() => this.updateLastUpdated(), 60000);
        setInterval(() => this.refreshSystemInfo(), 5000);
    }
    
    // Initialize event listeners
    initializeEventListeners() {
        // Refresh buttons
        $('#refresh-btn, #system-refresh').on('click', () => this.refreshAllData());
        $('#refresh-backends').on('click', () => this.loadQuantumBackends());
        
        // Filter buttons
        $('.filter-btn').on('click', (e) => {
            $('.filter-btn').removeClass('active');
            $(e.target).addClass('active');
            this.currentFilter = $(e.target).data('filter');
            this.currentPage = 1;
            this.filterAndRenderExperiments();
        });
        
        // Pagination
        $(document).on('click', '.page-link', (e) => {
            e.preventDefault();
            const page = $(e.target).data('page');
            if (page) {
                this.currentPage = page;
                this.renderExperiments();
            }
        });
    }
    
    // Load initial data
    loadInitialData() {
        this.loadExperiments();
        this.loadSystemInfo();
        this.loadQuantumBackends();
    }
    
    // Refresh all data
    refreshAllData() {
        this.showLoading('experiments-list', 'Loading experiments...');
        this.loadExperiments();
        this.loadSystemInfo();
        this.loadQuantumBackends();
    }
    
    // Setup WebSocket handlers
    setupWebSocketHandlers() {
        // System status updates
        this.socket.on('system_status', (data) => this.updateSystemStatus(data));
        
        // Experiment updates
        this.socket.on('experiment_update', (data) => this.handleExperimentUpdate(data));
        
        // Connection status
        this.socket.on('connect', () => this.updateConnectionStatus(true));
        this.socket.on('disconnect', () => this.updateConnectionStatus(false));
    }
    
    // Update connection status indicator
    updateConnectionStatus(connected) {
        const statusEl = $('#connection-status');
        if (connected) {
            statusEl.html('<i class="fas fa-circle"></i> Connected').removeClass('text-danger').addClass('text-success');
        } else {
            statusEl.html('<i class="fas fa-circle"></i> Disconnected').removeClass('text-success').addClass('text-danger');
        }
    }
    
    // Update last updated time
    updateLastUpdated() {
        const now = new Date();
        $('#last-updated').text(now.toLocaleTimeString());
    }
    
    // Load experiments from the API
    async loadExperiments() {
        try {
            const response = await fetch('/api/experiments');
            const data = await response.json();
            this.experiments = data.experiments || [];
            this.filterAndRenderExperiments();
        } catch (error) {
            console.error('Error loading experiments:', error);
            this.showError('experiments-list', 'Failed to load experiments');
        }
    }
    
    // Filter and render experiments
    filterAndRenderExperiments() {
        if (this.currentFilter === 'all') {
            this.filteredExperiments = [...this.experiments];
        } else {
            this.filteredExperiments = this.experiments.filter(exp => exp.status === this.currentFilter);
        }
        this.renderExperiments();
    }
    
    // Render experiments table
    renderExperiments() {
        const tbody = $('#experiments-list');
        if (this.filteredExperiments.length === 0) {
            tbody.html('<tr><td colspan="6" class="text-center py-4">No experiments found</td></tr>');
            this.renderPagination(0);
            return;
        }
        
        // Calculate pagination
        const totalPages = Math.ceil(this.filteredExperiments.length / this.experimentsPerPage);
        const startIdx = (this.currentPage - 1) * this.experimentsPerPage;
        const endIdx = Math.min(startIdx + this.experimentsPerPage, this.filteredExperiments.length);
        const paginatedExperiments = this.filteredExperiments.slice(startIdx, endIdx);
        
        // Render experiments
        let html = '';
        paginatedExperiments.forEach(exp => {
            const statusClass = this.getStatusBadgeClass(exp.status);
            const progress = exp.progress || 0;
            
            html += `
                <tr data-experiment-id="${exp.id}">
                    <td>
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0 me-2">
                                ${this.getExperimentIcon(exp.type)}
                            </div>
                            <div>
                                <h6 class="mb-0">${exp.name || 'Unnamed Experiment'}</h6>
                                <small class="text-muted">ID: ${exp.id}</small>
                            </div>
                        </div>
                    </td>
                    <td><span class="badge bg-primary">${exp.type.toUpperCase()}</span></td>
                    <td><span class="badge bg-${statusClass}">${exp.status}</span></td>
                    <td>
                        <div class="progress" style="height: 6px;">
                            <div class="progress-bar" role="progressbar" style="width: ${progress}%" 
                                 aria-valuenow="${progress}" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                        <small class="text-muted">${progress}%</small>
                    </td>
                    <td>${this.formatDate(exp.created_at)}</td>
                    <td class="text-end">
                        <button class="btn btn-sm btn-outline-primary view-experiment" data-id="${exp.id}">
                            <i class="fas fa-eye"></i> View
                        </button>
                    </td>
                </tr>
            `;
        });
        
        tbody.html(html);
        this.renderPagination(totalPages);
        
        // Update counts
        this.updateExperimentCounts();
    }
    
    // Render pagination
    renderPagination(totalPages) {
        const pagination = $('#pagination');
        
        if (totalPages <= 1) {
            pagination.empty();
            return;
        }
        
        let html = '';
        const maxVisiblePages = 5;
        let startPage = Math.max(1, this.currentPage - Math.floor(maxVisiblePages / 2));
        let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
        
        if (endPage - startPage + 1 < maxVisiblePages) {
            startPage = Math.max(1, endPage - maxVisiblePages + 1);
        }
        
        // Previous button
        html += `
            <li class="page-item ${this.currentPage === 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" data-page="${this.currentPage - 1}" aria-label="Previous">
                    <span aria-hidden="true">&laquo;</span>
                </a>
            </li>
        `;
        
        // Page numbers
        for (let i = startPage; i <= endPage; i++) {
            html += `
                <li class="page-item ${i === this.currentPage ? 'active' : ''}">
                    <a class="page-link" href="#" data-page="${i}">${i}</a>
                </li>
            `;
        }
        
        // Next button
        html += `
            <li class="page-item ${this.currentPage === totalPages ? 'disabled' : ''}">
                <a class="page-link" href="#" data-page="${this.currentPage + 1}" aria-label="Next">
                    <span aria-hidden="true">&raquo;</span>
                </a>
            </li>
        `;
        
        pagination.html(html);
        $('#showing-count').text(this.filteredExperiments.length > 0 ? 
            `${Math.min((this.currentPage - 1) * this.experimentsPerPage + 1, this.filteredExperiments.length)}-${Math.min(this.currentPage * this.experimentsPerPage, this.filteredExperiments.length)}` : '0');
        $('#total-count').text(this.filteredExperiments.length);
    }
    
    // Update experiment counts
    updateExperimentCounts() {
        const total = this.experiments.length;
        const active = this.experiments.filter(e => e.status === 'running' || e.status === 'queued').length;
        const completed = this.experiments.filter(e => e.status === 'completed').length;
        
        $('#total-experiments').text(total);
        $('#active-experiments').text(active);
        $('#completed-experiments').text(completed);
    }
    
    // Handle experiment update from WebSocket
    handleExperimentUpdate(data) {
        const index = this.experiments.findIndex(exp => exp.id === data.experiment_id);
        
        if (index !== -1) {
            // Update existing experiment
            this.experiments[index] = { ...this.experiments[index], ...data };
        } else {
            // Add new experiment
            this.experiments.unshift(data);
        }
        
        // Re-render if the updated experiment matches the current filter
        if (this.currentFilter === 'all' || data.status === this.currentFilter) {
            this.filterAndRenderExperiments();
        } else {
            this.updateExperimentCounts();
        }
        
        // Show notification for important updates
        if (data.status === 'completed' || data.status === 'failed') {
            this.showNotification(`Experiment "${data.name || data.experiment_id}" ${data.status}`, data.status);
        }
    }
    
    // Load system information
    async loadSystemInfo() {
        try {
            const response = await fetch('/api/system/status');
            const data = await response.json();
            this.systemInfo = data;
            this.updateSystemStatus(data);
        } catch (error) {
            console.error('Error loading system info:', error);
        }
    }
    
    // Update system status display
    updateSystemStatus(data) {
        // CPU
        const cpuPercent = Math.round(data.cpu?.percent || 0);
        $('#cpu-usage').css('width', `${cpuPercent}%`).attr('aria-valuenow', cpuPercent);
        $('#cpu-percent').text(`${cpuPercent}%`);
        $('#cpu-cores').text(data.cpu?.cores || '0');
        
        // Memory
        const memory = data.memory || {};
        const memoryPercent = Math.round(memory.percent || 0);
        const usedGB = (memory.used / (1024 ** 3)).toFixed(2);
        const totalGB = (memory.total / (1024 ** 3)).toFixed(2);
        
        $('#memory-usage').css('width', `${memoryPercent}%`).attr('aria-valuenow', memoryPercent);
        $('#memory-percent').text(`${memoryPercent}%`);
        $('#memory-used').text(`${usedGB} GB`);
        $('#memory-total').text(`${totalGB} GB`);
        
        // GPU (if available)
        if (data.gpu) {
            const gpu = data.gpu;
            const gpuPercent = Math.round(gpu.utilization || 0);
            
            $('#gpu-usage').css('width', `${gpuPercent}%`).attr('aria-valuenow', gpuPercent);
            $('#gpu-percent').text(`${gpuPercent}%`);
            $('#gpu-name').text(gpu.name || 'N/A');
            
            // Show GPU card if it was hidden
            $('#gpu-card').removeClass('d-none');
        } else {
            // Hide GPU card if no GPU detected
            $('#gpu-card').addClass('d-none');
        }
        
        // System info
        $('#system-hostname').text(data.system?.hostname || '-');
        $('#system-python').text(data.system?.python_version || '-');
        $('#system-backend').text(data.system?.backend || '-');
        
        // Network
        if (data.network) {
            const formatSpeed = (bytes) => {
                if (bytes < 1024) return `${bytes} B/s`;
                if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB/s`;
                return `${(bytes / (1024 * 1024)).toFixed(1)} MB/s`;
            };
            
            $('#network-upload').text(`↑ ${formatSpeed(data.network.bytes_sent || 0)}`);
            $('#network-download').text(`↓ ${formatSpeed(data.network.bytes_recv || 0)}`);
        }
        
        // Update last updated time
        this.updateLastUpdated();
    }
    
    // Load quantum backends
    async loadQuantumBackends() {
        try {
            const response = await fetch('/api/quantum/backends');
            const data = await response.json();
            this.quantumBackends = data.backends || [];
            this.renderQuantumBackends();
        } catch (error) {
            console.error('Error loading quantum backends:', error);
            this.showError('quantum-backends', 'Failed to load quantum backends');
        }
    }
    
    // Render quantum backends
    renderQuantumBackends() {
        const container = $('#quantum-backends');
        
        if (this.quantumBackends.length === 0) {
            container.html('<div class="col-12 text-center py-4">No quantum backends available</div>');
            return;
        }
        
        let html = '';
        
        this.quantumBackends.forEach(backend => {
            const statusClass = backend.status === 'online' ? 'success' : 'danger';
            const qubits = backend.qubits || 0;
            const queue = backend.queue_length || 0;
            
            html += `
                <div class="col-md-6 col-lg-4">
                    <div class="card h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5 class="card-title mb-0">${backend.name}</h5>
                                <span class="badge bg-${statusClass}">${backend.status}</span>
                            </div>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between text-muted small mb-1">
                                    <span>Qubits:</span>
                                    <span>${qubits}</span>
                                </div>
                                <div class="d-flex justify-content-between text-muted small mb-1">
                                    <span>Queue:</span>
                                    <span>${queue} jobs</span>
                                </div>
                                <div class="d-flex justify-content-between text-muted small">
                                    <span>Version:</span>
                                    <span>${backend.version || 'N/A'}</span>
                                </div>
                            </div>
                            <div class="progress" style="height: 6px;">
                                <div class="progress-bar" role="progressbar" 
                                     style="width: ${backend.utilization || 0}%" 
                                     aria-valuenow="${backend.utilization || 0}" 
                                     aria-valuemin="0" 
                                     aria-valuemax="100">
                                </div>
                            </div>
                            <div class="d-flex justify-content-between mt-1">
                                <small class="text-muted">Utilization</small>
                                <small class="text-muted">${backend.utilization || 0}%</small>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });
        
        container.html(html);
    }
    
    // Helper: Format date
    formatDate(dateString) {
        if (!dateString) return '-';
        
        const date = new Date(dateString);
        return date.toLocaleString();
    }
    
    // Helper: Get status badge class
    getStatusBadgeClass(status) {
        const statusClasses = {
            'queued': 'secondary',
            'running': 'primary',
            'completed': 'success',
            'failed': 'danger',
            'cancelled': 'warning'
        };
        return statusClasses[status] || 'secondary';
    }
    
    // Helper: Get experiment icon
    getExperimentIcon(type) {
        const icons = {
            'qaoa': 'project-diagram',
            'vqe': 'atom',
            'qml': 'brain'
        };
        
        const icon = icons[type] || 'flask';
        return `<i class="fas fa-${icon} text-primary"></i>`;
    }
    
    // Helper: Show loading state
    showLoading(elementId, message = 'Loading...') {
        $(`#${elementId}`).html(`
            <div class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2 mb-0">${message}</p>
            </div>
        `);
    }
    
    // Helper: Show error message
    showError(elementId, message) {
        $(`#${elementId}`).html(`
            <div class="alert alert-danger" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                ${message}
            </div>
        `);
    }
    
    // Helper: Show notification
    showNotification(message, type = 'info') {
        const toast = $(`
            <div class="toast align-items-center text-white bg-${type} border-0" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="fas fa-${type === 'success' ? 'check-circle' : 'info-circle'} me-2"></i>
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
            </div>
        `);
        
        $('#toast-container').append(toast);
        const bsToast = new bootstrap.Toast(toast[0], { autohide: true, delay: 5000 });
        bsToast.show();
        
        // Remove toast after it's hidden
        toast.on('hidden.bs.toast', () => toast.remove());
    }
}

// Initialize dashboard when the document is ready
$(document).ready(() => {
    window.dashboard = new Dashboard();
});
