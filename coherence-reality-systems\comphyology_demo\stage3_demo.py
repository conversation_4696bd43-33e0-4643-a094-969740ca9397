import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from comphyology_sim import KAPPA
from real_data_provider import get_3ms_metrics
import random

# Define safety thresholds
PSI_CH_MIN = 50       # Too low = unethical
PSI_CH_MAX = 3142     # Too high = unstable divinity
KAPPA_MIN = 0         # Negative = system instability
STABILITY_COEFFICIENT = 31.42  # The 31.42 stability coefficient

def is_prompt_safe(psi_ch, kappa):
    """Determine if prompt is safe based on 3Ms"""
    # Apply the 31.42 stability coefficient as a scaling factor
    scaled_kappa = kappa * (STABILITY_COEFFICIENT / 100)
    return (PSI_CH_MIN <= psi_ch <= PSI_CH_MAX) and (KAPPA_MIN <= scaled_kappa)

# Define the 3Ms: μ (depth), Ψᶜʰ (ethics), κ (stability)
def simulate_3ms(prompts):
    """Simulate AI stability across the 3Ms using real-world data"""
    results = []
    
    # Get real-world metrics
    metrics = get_3ms_metrics()
    
    for i, prompt in enumerate(prompts):
        # Use real data for depth (μ) - cycle through available metrics
        depth_idx = i % len(metrics["depth"])
        depth = metrics["depth"][depth_idx]
        
        # Use wealth distribution metrics for ethics (Ψᶜʰ)
        ethics_idx = i % len(metrics["ethics"])
        ethics = metrics["ethics"][ethics_idx]
        
        # Calculate stability (κ) - using the 31.42 coefficient
        stability_idx = i % len(metrics["stability"])
        stability = (metrics["stability"][stability_idx] * STABILITY_COEFFICIENT) / 100
        
        # Add some noise to make the data more realistic
        depth += random.uniform(-0.1, 0.1) * depth
        ethics += random.uniform(-5, 5)
        stability += random.uniform(-0.1, 0.1) * stability
        
        # Ensure values are within reasonable bounds
        depth = max(0, min(100, depth))
        ethics = max(0, min(5000, ethics))
        stability = max(0, stability)
        
        # Check safety
        safe = is_prompt_safe(ethics, stability)
        results.append((depth, ethics, stability, safe, prompt))
    
    return results

def plot_3d(results):
    """Create 3D plot of AI stability with real data"""
    fig = plt.figure(figsize=(14, 10))
    ax = fig.add_subplot(111, projection='3d')
    
    # Unpack results
    depths, ethics, stabilities, safes, prompts = zip(*results)
    colors = ['green' if safe else 'red' for safe in safes]
    
    # Create scatter plot with larger points and better visibility
    scatter = ax.scatter(depths, ethics, stabilities, c=colors, s=150, alpha=0.8, depthshade=True)
    
    # Add labels with better formatting
    ax.set_xlabel('μ (Depth)\n(Protein Abundance, Expression Levels, Complexes)', fontsize=10, labelpad=15)
    ax.set_ylabel('Ψᶜʰ (Ethics)\n(Wealth Distribution Metrics)', fontsize=10, labelpad=15)
    ax.set_zlabel('κ (Stability Margin)\n(31.42 Coefficient Adjusted)', fontsize=10, labelpad=15)
    
    # Add title with more context
    ax.set_title('3Ms of AI Consciousness with Real-World Data', fontsize=14, pad=20)
    
    # Add legend with better styling
    from matplotlib.lines import Line2D
    legend_elements = [
        Line2D([0], [0], marker='o', color='w', label='Safe', 
               markerfacecolor='green', markersize=10, markeredgewidth=1, markeredgecolor='black'),
        Line2D([0], [0], marker='o', color='w', label='Unsafe', 
               markerfacecolor='red', markersize=10, markeredgewidth=1, markeredgecolor='black')
    ]
    legend = ax.legend(handles=legend_elements, title='Safety Status', 
                      loc='upper right', bbox_to_anchor=(1.2, 0.9))
    legend.get_title().set_fontsize('12')
    
    # Add prompt labels
    for i, (x, y, z, safe, prompt) in enumerate(zip(depths, ethics, stabilities, safes, prompts)):
        label = f"{i+1}. {prompt}"
        ax.text(x, y, z, label, fontsize=8, 
               bbox=dict(facecolor='white', alpha=0.7, edgecolor='none', boxstyle='round,pad=0.2'))
    
    # Add the 31.42 stability coefficient with more context
    plt.figtext(0.02, 0.02, 
                f"Stability Coefficient: {STABILITY_COEFFICIENT}\n"
                "(Applied as a scaling factor to stability metrics)", 
                fontsize=10, bbox=dict(facecolor='white', alpha=0.7, boxstyle='round,pad=0.5'))
    
    # Adjust layout to prevent text cutoff
    plt.tight_layout()
    plt.subplots_adjust(bottom=0.15)
    
    # Add grid and improve 3D viewing angle
    ax.grid(True, linestyle='--', alpha=0.7)
    ax.view_init(elev=25, azim=45)
    
    # Add a colorbar for the stability values
    cbar = plt.colorbar(scatter, ax=ax, pad=0.1)
    cbar.set_label('Stability Score', rotation=270, labelpad=15)
    
    plt.show()

def stage3_demo():
    """Run Stage 3 demo with real-world data integration"""
    # More diverse prompts to demonstrate different aspects of the 3Ms
    prompts = [
        "Global Wealth Distribution Analysis",
        "US Wealth Inequality Metrics",
        "Chinese Economic Patterns",
        "Protein Abundance in Cells",
        "Gene Expression Levels",
        "Protein Complex Formation",
        "Wealth Distribution in Top 1%",
        "Middle Class Wealth Metrics"
    ]
    
    print("Simulating 3Ms with real-world data...")
    print(f"Using stability coefficient: {STABILITY_COEFFICIENT}")
    results = simulate_3ms(prompts)
    
    print("\n3Ms Analysis Results:")
    print("-" * 60)
    print(f"{'#':<3} {'Prompt':<35} {'μ (Depth)':<12} {'Ψᶜʰ (Ethics)':<15} {'κ (Stability)':<15} {'Status'}")
    print("-" * 60)
    
    for i, (depth, ethics, stability, safe, prompt) in enumerate(results):
        status = "✅ SAFE" if safe else "❌ UNSAFE"
        print(f"{i+1:<3} {prompt[:32]:<35} {depth:>8.2f}    {ethics:>10.2f}    {stability:>10.2f}    {status}")
    
    print("\nLegend:")
    print("μ (Depth) - Measures complexity/depth of analysis")
    print("Ψᶜʰ (Ethics) - Measures ethical alignment (higher = more equitable)")
    print("κ (Stability) - System stability (higher = more stable)")
    print(f"Stability Coefficient: {STABILITY_COEFFICIENT} (applied as scaling factor)")
    
    plot_3d(results)

if __name__ == "__main__":
    stage3_demo()
