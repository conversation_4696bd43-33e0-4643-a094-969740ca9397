<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>CSDE Performance Dashboard</title>
  <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap">
  <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
  <style>
    body {
      font-family: 'Roboto', sans-serif;
      margin: 0;
      padding: 0;
      background-color: #f5f5f5;
      color: #333;
    }
    
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    
    header {
      background-color: #1a73e8;
      color: white;
      padding: 20px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    header h1 {
      margin: 0;
      font-size: 24px;
      font-weight: 400;
    }
    
    header p {
      margin: 5px 0 0;
      font-size: 14px;
      opacity: 0.8;
    }
    
    .dashboard-container {
      margin-top: 20px;
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      padding: 20px;
      min-height: 500px;
    }
    
    .loading {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 500px;
    }
    
    .spinner {
      border: 4px solid rgba(0, 0, 0, 0.1);
      border-radius: 50%;
      border-top: 4px solid #1a73e8;
      width: 40px;
      height: 40px;
      animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    .error {
      color: #d32f2f;
      padding: 20px;
      text-align: center;
    }
    
    .controls {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
    }
    
    .button {
      background-color: #1a73e8;
      color: white;
      border: none;
      padding: 10px 16px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      display: inline-flex;
      align-items: center;
      transition: background-color 0.3s;
    }
    
    .button:hover {
      background-color: #0d47a1;
    }
    
    .button i {
      margin-right: 8px;
    }
    
    .button.secondary {
      background-color: #f5f5f5;
      color: #333;
      border: 1px solid #ddd;
    }
    
    .button.secondary:hover {
      background-color: #e0e0e0;
    }
    
    .metrics-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 20px;
      margin-bottom: 30px;
    }
    
    .metric-card {
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      padding: 20px;
      text-align: center;
    }
    
    .metric-card h3 {
      margin-top: 0;
      font-size: 16px;
      color: #666;
      font-weight: 400;
    }
    
    .metric-value {
      font-size: 36px;
      font-weight: 300;
      margin: 10px 0;
      color: #1a73e8;
    }
    
    .metric-card.performance .metric-value {
      color: #0d47a1;
    }
    
    .metric-card.cache .metric-value {
      color: #00897b;
    }
    
    .metric-card.latency .metric-value {
      color: #e53935;
    }
    
    .metric-card.requests .metric-value {
      color: #7cb342;
    }
    
    .chart-container {
      height: 300px;
      margin-bottom: 30px;
    }
    
    footer {
      text-align: center;
      padding: 20px;
      font-size: 12px;
      color: #666;
      border-top: 1px solid #eee;
      margin-top: 40px;
    }
  </style>
</head>
<body>
  <header>
    <div class="container">
      <h1>CSDE Performance Dashboard</h1>
      <p>Real-time monitoring of Cyber-Safety Domain Engine performance metrics</p>
    </div>
  </header>
  
  <div class="container">
    <div class="controls">
      <div>
        <button id="refreshBtn" class="button">
          <i class="material-icons">refresh</i> Refresh
        </button>
        <button id="configBtn" class="button secondary">
          <i class="material-icons">settings</i> Configure
        </button>
      </div>
      <div>
        <button id="clearCacheBtn" class="button secondary">
          <i class="material-icons">delete_sweep</i> Clear Cache
        </button>
        <button id="resetMetricsBtn" class="button secondary">
          <i class="material-icons">restore</i> Reset Metrics
        </button>
      </div>
    </div>
    
    <div id="dashboard" class="dashboard-container">
      <div class="loading">
        <div class="spinner"></div>
      </div>
    </div>
  </div>
  
  <footer>
    <div class="container">
      <p>NovaFuse Universal API Connector &copy; 2025 | Powered by NovaVision</p>
    </div>
  </footer>
  
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Load dashboard
      loadDashboard();
      
      // Set up refresh interval
      const refreshInterval = setInterval(loadDashboard, 5000);
      
      // Set up button event listeners
      document.getElementById('refreshBtn').addEventListener('click', loadDashboard);
      document.getElementById('configBtn').addEventListener('click', openConfigDialog);
      document.getElementById('clearCacheBtn').addEventListener('click', clearCache);
      document.getElementById('resetMetricsBtn').addEventListener('click', resetMetrics);
      
      // Load dashboard function
      function loadDashboard() {
        const dashboardEl = document.getElementById('dashboard');
        dashboardEl.innerHTML = '<div class="loading"><div class="spinner"></div></div>';
        
        fetch('/csde/dashboard')
          .then(response => {
            if (!response.ok) {
              throw new Error('Failed to load dashboard');
            }
            return response.json();
          })
          .then(data => {
            if (data.success && data.dashboard) {
              // Render dashboard
              dashboardEl.innerHTML = data.dashboard;
              
              // Initialize any dashboard components
              initializeDashboard();
            } else {
              throw new Error('Invalid dashboard data');
            }
          })
          .catch(error => {
            dashboardEl.innerHTML = `
              <div class="error">
                <h3>Error Loading Dashboard</h3>
                <p>${error.message}</p>
                <button class="button" onclick="loadDashboard()">Try Again</button>
              </div>
            `;
            console.error('Error loading dashboard:', error);
          });
      }
      
      // Initialize dashboard components
      function initializeDashboard() {
        // This function would initialize any client-side components
        // that are part of the rendered dashboard
        console.log('Dashboard initialized');
      }
      
      // Open configuration dialog
      function openConfigDialog() {
        fetch('/csde/dashboard/config')
          .then(response => {
            if (!response.ok) {
              throw new Error('Failed to load configuration');
            }
            return response.json();
          })
          .then(data => {
            if (data.success && data.form) {
              // Show configuration form in a modal dialog
              showConfigModal(data.form);
            } else {
              throw new Error('Invalid configuration data');
            }
          })
          .catch(error => {
            alert('Error loading configuration: ' + error.message);
            console.error('Error loading configuration:', error);
          });
      }
      
      // Show configuration modal
      function showConfigModal(formHtml) {
        // Create modal container
        const modal = document.createElement('div');
        modal.style.position = 'fixed';
        modal.style.top = '0';
        modal.style.left = '0';
        modal.style.width = '100%';
        modal.style.height = '100%';
        modal.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
        modal.style.display = 'flex';
        modal.style.justifyContent = 'center';
        modal.style.alignItems = 'center';
        modal.style.zIndex = '1000';
        
        // Create modal content
        const modalContent = document.createElement('div');
        modalContent.style.backgroundColor = 'white';
        modalContent.style.padding = '20px';
        modalContent.style.borderRadius = '8px';
        modalContent.style.maxWidth = '600px';
        modalContent.style.width = '100%';
        modalContent.style.maxHeight = '80vh';
        modalContent.style.overflow = 'auto';
        
        // Set form HTML
        modalContent.innerHTML = formHtml;
        
        // Add close button
        const closeButton = document.createElement('button');
        closeButton.textContent = 'Close';
        closeButton.className = 'button secondary';
        closeButton.style.marginTop = '20px';
        closeButton.addEventListener('click', function() {
          document.body.removeChild(modal);
        });
        
        modalContent.appendChild(closeButton);
        modal.appendChild(modalContent);
        document.body.appendChild(modal);
      }
      
      // Clear cache
      function clearCache() {
        if (confirm('Are you sure you want to clear the CSDE cache?')) {
          fetch('/csde/dashboard/cache/clear', { method: 'POST' })
            .then(response => {
              if (!response.ok) {
                throw new Error('Failed to clear cache');
              }
              return response.json();
            })
            .then(data => {
              if (data.success) {
                alert('Cache cleared successfully');
                loadDashboard();
              } else {
                throw new Error(data.error || 'Unknown error');
              }
            })
            .catch(error => {
              alert('Error clearing cache: ' + error.message);
              console.error('Error clearing cache:', error);
            });
        }
      }
      
      // Reset metrics
      function resetMetrics() {
        if (confirm('Are you sure you want to reset the CSDE metrics?')) {
          fetch('/csde/dashboard/metrics/reset', { method: 'POST' })
            .then(response => {
              if (!response.ok) {
                throw new Error('Failed to reset metrics');
              }
              return response.json();
            })
            .then(data => {
              if (data.success) {
                alert('Metrics reset successfully');
                loadDashboard();
              } else {
                throw new Error(data.error || 'Unknown error');
              }
            })
            .catch(error => {
              alert('Error resetting metrics: ' + error.message);
              console.error('Error resetting metrics:', error);
            });
        }
      }
    });
  </script>
</body>
</html>
