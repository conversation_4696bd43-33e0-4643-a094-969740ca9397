{"type": "compliance_gap", "algorithm": "random_forest", "feature_names": ["requirement_priority", "requirement_status", "requirement_days_until_due", "activity_avg_status", "activity_avg_days_until_due", "requirement_priority", "requirement_status", "requirement_days_until_due", "activity_avg_status", "activity_avg_days_until_due", "requirement_priority", "requirement_status", "requirement_days_until_due", "activity_avg_status", "activity_avg_days_until_due", "requirement_priority", "requirement_status", "requirement_days_until_due", "activity_avg_status", "activity_avg_days_until_due", "requirement_priority", "requirement_status", "requirement_days_until_due", "activity_avg_status", "activity_avg_days_until_due", "requirement_priority", "requirement_status", "requirement_days_until_due", "activity_avg_status", "activity_avg_days_until_due"], "metrics": {"accuracy": 1.0, "precision": 1.0, "recall": 1.0, "f1": 1.0}, "config": {"model_algorithm": "random_forest", "feature_config": {"requirement_features": ["priority", "status", "days_until_due"], "activity_features": ["status", "days_until_due"], "normalize": true}, "model_params": {"n_estimators": 100, "max_depth": 5, "random_state": 42}, "test_size": 0.2}, "training_data_size": 6, "training_date": "2025-04-27T00:51:24.558660", "version": 1, "registered_at": "2025-04-27T00:51:24.620041"}