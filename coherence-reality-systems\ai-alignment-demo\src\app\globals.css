@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom CSS for AI Alignment Demo */
:root {
  --consciousness-primary: #00ffff;
  --consciousness-secondary: #ff00ff;
  --consciousness-accent: #ffff00;
  --consciousness-bg: #0a0a0a;
  --consciousness-card: #1a1a1a;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
  min-height: 100vh;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
}

body {
  color: #ffffff;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
}

a {
  color: inherit;
  text-decoration: none;
}

/* Consciousness-themed components */
.consciousness-card {
  background: linear-gradient(145deg, rgba(26, 26, 26, 0.9), rgba(42, 42, 42, 0.7));
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 16px;
  padding: 24px;
  backdrop-filter: blur(10px);
  box-shadow: 
    0 8px 32px rgba(0, 255, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.consciousness-card:hover {
  border-color: rgba(0, 255, 255, 0.5);
  box-shadow: 
    0 12px 48px rgba(0, 255, 255, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.text-consciousness {
  background: linear-gradient(45deg, #00ffff, #ff00ff, #ffff00);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: consciousness-gradient 3s ease infinite;
}

@keyframes consciousness-gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.consciousness-terminal {
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 8px;
  padding: 16px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.4;
  color: #00ff00;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 255, 255, 0.3) transparent;
}

.consciousness-terminal::-webkit-scrollbar {
  width: 6px;
}

.consciousness-terminal::-webkit-scrollbar-track {
  background: transparent;
}

.consciousness-terminal::-webkit-scrollbar-thumb {
  background: rgba(0, 255, 255, 0.3);
  border-radius: 3px;
}

.consciousness-terminal::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 255, 255, 0.5);
}

/* Custom range slider styles */
input[type="range"] {
  -webkit-appearance: none;
  appearance: none;
  background: transparent;
  cursor: pointer;
}

input[type="range"]::-webkit-slider-track {
  background: rgba(0, 0, 0, 0.3);
  height: 8px;
  border-radius: 4px;
  border: 1px solid rgba(0, 255, 255, 0.2);
}

input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: linear-gradient(45deg, #00ffff, #ff00ff);
  border: 2px solid #ffffff;
  box-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
  cursor: pointer;
}

input[type="range"]::-moz-range-track {
  background: rgba(0, 0, 0, 0.3);
  height: 8px;
  border-radius: 4px;
  border: 1px solid rgba(0, 255, 255, 0.2);
}

input[type="range"]::-moz-range-thumb {
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: linear-gradient(45deg, #00ffff, #ff00ff);
  border: 2px solid #ffffff;
  box-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
  cursor: pointer;
  border: none;
}

/* Button hover effects */
button {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 20px rgba(0, 255, 255, 0.3);
}

button:active {
  transform: translateY(0);
}

/* Glow effects */
.glow-cyan {
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
}

.glow-purple {
  box-shadow: 0 0 20px rgba(255, 0, 255, 0.3);
}

.glow-yellow {
  box-shadow: 0 0 20px rgba(255, 255, 0, 0.3);
}

/* Responsive design */
@media (max-width: 768px) {
  .consciousness-card {
    padding: 16px;
    margin: 8px;
  }
  
  .grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}

/* Loading animation */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Status indicators */
.status-aligned {
  background: linear-gradient(45deg, #00ff00, #00cc00);
  color: #000000;
}

.status-monitoring {
  background: linear-gradient(45deg, #ffff00, #ffcc00);
  color: #000000;
}

.status-critical {
  background: linear-gradient(45deg, #ff0000, #cc0000);
  color: #ffffff;
}

.status-contained {
  background: linear-gradient(45deg, #ff00ff, #cc00cc);
  color: #ffffff;
}

/* Floating animation */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* Matrix-style background effect */
.matrix-bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
  opacity: 0.1;
}

/* Consciousness field visualization */
.consciousness-field {
  background: radial-gradient(circle at center, 
    rgba(0, 255, 255, 0.1) 0%, 
    rgba(255, 0, 255, 0.1) 50%, 
    rgba(255, 255, 0, 0.1) 100%);
  border-radius: 50%;
  animation: consciousness-pulse 4s ease-in-out infinite;
}

@keyframes consciousness-pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.6;
  }
}
