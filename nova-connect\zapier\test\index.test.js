/**
 * NovaConnect UAC Zapier Integration Tests
 * 
 * This file contains tests for the Zapier integration.
 */

const zapier = require('zapier-platform-core');
const App = require('../index');

// Create a new version of the app
const appTester = zapier.createAppTester(App);

// Set environment variables
zapier.tools.env.inject();

// Test the app
describe('App', () => {
  test('should load', () => {
    expect(App).toBeDefined();
    expect(App.version).toBeDefined();
    expect(App.platformVersion).toBeDefined();
    expect(App.authentication).toBeDefined();
    expect(App.triggers).toBeDefined();
    expect(App.creates).toBeDefined();
  });
});

// Test authentication
describe('Authentication', () => {
  test('should define OAuth2 authentication', () => {
    expect(App.authentication.type).toBe('oauth2');
    expect(App.authentication.oauth2Config).toBeDefined();
    expect(App.authentication.oauth2Config.authorizeUrl).toBeDefined();
    expect(App.authentication.oauth2Config.getAccessToken).toBeDefined();
    expect(App.authentication.oauth2Config.refreshAccessToken).toBeDefined();
  });
});

// Test triggers
describe('Triggers', () => {
  test('should define new_connector trigger', () => {
    expect(App.triggers.new_connector).toBeDefined();
    expect(App.triggers.new_connector.key).toBe('new_connector');
    expect(App.triggers.new_connector.noun).toBe('Connector');
    expect(App.triggers.new_connector.display).toBeDefined();
    expect(App.triggers.new_connector.operation).toBeDefined();
  });
  
  test('should define new_workflow trigger', () => {
    expect(App.triggers.new_workflow).toBeDefined();
    expect(App.triggers.new_workflow.key).toBe('new_workflow');
    expect(App.triggers.new_workflow.noun).toBe('Workflow');
    expect(App.triggers.new_workflow.display).toBeDefined();
    expect(App.triggers.new_workflow.operation).toBeDefined();
  });
  
  test('should define compliance_event trigger', () => {
    expect(App.triggers.compliance_event).toBeDefined();
    expect(App.triggers.compliance_event.key).toBe('compliance_event');
    expect(App.triggers.compliance_event.noun).toBe('Compliance Event');
    expect(App.triggers.compliance_event.display).toBeDefined();
    expect(App.triggers.compliance_event.operation).toBeDefined();
  });
});

// Test actions
describe('Actions', () => {
  test('should define create_connector action', () => {
    expect(App.creates.create_connector).toBeDefined();
    expect(App.creates.create_connector.key).toBe('create_connector');
    expect(App.creates.create_connector.noun).toBe('Connector');
    expect(App.creates.create_connector.display).toBeDefined();
    expect(App.creates.create_connector.operation).toBeDefined();
  });
  
  test('should define execute_workflow action', () => {
    expect(App.creates.execute_workflow).toBeDefined();
    expect(App.creates.execute_workflow.key).toBe('execute_workflow');
    expect(App.creates.execute_workflow.noun).toBe('Workflow');
    expect(App.creates.execute_workflow.display).toBeDefined();
    expect(App.creates.execute_workflow.operation).toBeDefined();
  });
  
  test('should define create_compliance_evidence action', () => {
    expect(App.creates.create_compliance_evidence).toBeDefined();
    expect(App.creates.create_compliance_evidence.key).toBe('create_compliance_evidence');
    expect(App.creates.create_compliance_evidence.noun).toBe('Compliance Evidence');
    expect(App.creates.create_compliance_evidence.display).toBeDefined();
    expect(App.creates.create_compliance_evidence.operation).toBeDefined();
  });
});
