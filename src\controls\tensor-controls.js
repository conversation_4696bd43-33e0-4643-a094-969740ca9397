/**
 * Tensor Controls
 * 
 * This module provides controls for manipulating tensors.
 */

const ControlPanel = require('./control-panel');
const { WebSocketClient } = require('../websocket');

/**
 * TensorControls class
 */
class TensorControls {
  /**
   * Create a new TensorControls instance
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    this.options = {
      enableLogging: options.enableLogging !== undefined ? options.enableLogging : true,
      wsUrl: options.wsUrl || 'ws://localhost:3001/ws',
      controlPanel: options.controlPanel || null,
      ...options
    };
    
    // Create control panel if not provided
    if (!this.options.controlPanel) {
      this.controlPanel = new ControlPanel({
        enableLogging: this.options.enableLogging,
        wsUrl: this.options.wsUrl,
        autoConnect: false
      });
    } else {
      this.controlPanel = this.options.controlPanel;
    }
    
    // Create WebSocket client for tensor operations
    this.client = new WebSocketClient({
      url: this.options.wsUrl,
      clientId: `tensor-controls-${Date.now()}`,
      enableLogging: this.options.enableLogging,
      autoReconnect: true
    });
    
    // Initialize state
    this.state = {
      isConnected: false,
      tensors: new Map(), // tensorId -> tensor
      selectedTensorId: null,
      lastUpdate: Date.now()
    };
    
    // Set up event handlers
    this.client.on('connected', this._handleConnected.bind(this));
    this.client.on('disconnected', this._handleDisconnected.bind(this));
    this.client.on('error', this._handleError.bind(this));
    this.client.on('channel-message', this._handleChannelMessage.bind(this));
    
    // Set up control panel event handlers
    this.controlPanel.on('control-value-changed', this._handleControlValueChanged.bind(this));
    this.controlPanel.on('action-executed', this._handleActionExecuted.bind(this));
    
    if (this.options.enableLogging) {
      console.log('TensorControls initialized');
    }
  }
  
  /**
   * Connect to the WebSocket server
   * @returns {Promise<void>} - Promise that resolves when connected
   */
  async connect() {
    if (this.state.isConnected) {
      if (this.options.enableLogging) {
        console.log('TensorControls: Already connected');
      }
      return;
    }
    
    // Connect control panel
    if (!this.controlPanel.state.isConnected) {
      await this.controlPanel.connect();
    }
    
    // Connect WebSocket client
    await this.client.connect();
    
    // Subscribe to tensor channels
    await this.client.subscribe('tensor-updates');
    await this.client.subscribe('tensor-events');
    
    // Update state
    this.state.isConnected = true;
    this.state.lastUpdate = Date.now();
    
    // Register controls
    this._registerControls();
    
    // Get available tensors
    await this._fetchTensors();
    
    if (this.options.enableLogging) {
      console.log('TensorControls: Connected');
    }
  }
  
  /**
   * Disconnect from the WebSocket server
   * @returns {Promise<void>} - Promise that resolves when disconnected
   */
  async disconnect() {
    if (!this.state.isConnected) {
      if (this.options.enableLogging) {
        console.log('TensorControls: Not connected');
      }
      return;
    }
    
    // Disconnect WebSocket client
    await this.client.disconnect();
    
    // Update state
    this.state.isConnected = false;
    this.state.lastUpdate = Date.now();
    
    if (this.options.enableLogging) {
      console.log('TensorControls: Disconnected');
    }
  }
  
  /**
   * Handle WebSocket connected event
   * @private
   */
  _handleConnected() {
    // Update state
    this.state.isConnected = true;
    this.state.lastUpdate = Date.now();
    
    if (this.options.enableLogging) {
      console.log('TensorControls: WebSocket connected');
    }
  }
  
  /**
   * Handle WebSocket disconnected event
   * @private
   */
  _handleDisconnected() {
    // Update state
    this.state.isConnected = false;
    this.state.lastUpdate = Date.now();
    
    if (this.options.enableLogging) {
      console.log('TensorControls: WebSocket disconnected');
    }
  }
  
  /**
   * Handle WebSocket error event
   * @param {Error} error - Error object
   * @private
   */
  _handleError(error) {
    if (this.options.enableLogging) {
      console.error('TensorControls: WebSocket error:', error);
    }
  }
  
  /**
   * Handle WebSocket channel message event
   * @param {Object} data - Message data
   * @private
   */
  _handleChannelMessage(data) {
    const { channel, data: messageData } = data;
    
    // Handle message based on channel
    switch (channel) {
      case 'tensor-updates':
        this._handleTensorUpdates(messageData);
        break;
        
      case 'tensor-events':
        this._handleTensorEvents(messageData);
        break;
    }
  }
  
  /**
   * Handle tensor updates
   * @param {Object} data - Update data
   * @private
   */
  _handleTensorUpdates(data) {
    if (!data || !data.id) {
      return;
    }
    
    // Update tensor
    this.state.tensors.set(data.id, data.tensor);
    this.state.lastUpdate = Date.now();
    
    // Update tensor list control
    this._updateTensorListControl();
    
    // Update tensor details if this is the selected tensor
    if (this.state.selectedTensorId === data.id) {
      this._updateTensorDetailsControls(data.id);
    }
    
    if (this.options.enableLogging) {
      console.log(`TensorControls: Tensor updated - ${data.id}`);
    }
  }
  
  /**
   * Handle tensor events
   * @param {Object} data - Event data
   * @private
   */
  _handleTensorEvents(data) {
    if (!data || !data.event) {
      return;
    }
    
    // Handle event based on type
    switch (data.event) {
      case 'tensor-registered':
        this._handleTensorRegistered(data);
        break;
        
      case 'tensor-healed':
        this._handleTensorHealed(data);
        break;
        
      case 'tensor-damaged':
        this._handleTensorDamaged(data);
        break;
    }
  }
  
  /**
   * Handle tensor registered event
   * @param {Object} data - Event data
   * @private
   */
  _handleTensorRegistered(data) {
    if (!data || !data.id) {
      return;
    }
    
    // Add tensor to list
    this.state.tensors.set(data.id, data.tensor);
    this.state.lastUpdate = Date.now();
    
    // Update tensor list control
    this._updateTensorListControl();
    
    if (this.options.enableLogging) {
      console.log(`TensorControls: Tensor registered - ${data.id}`);
    }
  }
  
  /**
   * Handle tensor healed event
   * @param {Object} data - Event data
   * @private
   */
  _handleTensorHealed(data) {
    if (!data || !data.id) {
      return;
    }
    
    // Update tensor
    this.state.tensors.set(data.id, data.tensor);
    this.state.lastUpdate = Date.now();
    
    // Update tensor details if this is the selected tensor
    if (this.state.selectedTensorId === data.id) {
      this._updateTensorDetailsControls(data.id);
    }
    
    if (this.options.enableLogging) {
      console.log(`TensorControls: Tensor healed - ${data.id}`);
    }
  }
  
  /**
   * Handle tensor damaged event
   * @param {Object} data - Event data
   * @private
   */
  _handleTensorDamaged(data) {
    if (!data || !data.id) {
      return;
    }
    
    // Update tensor
    this.state.tensors.set(data.id, data.tensor);
    this.state.lastUpdate = Date.now();
    
    // Update tensor details if this is the selected tensor
    if (this.state.selectedTensorId === data.id) {
      this._updateTensorDetailsControls(data.id);
    }
    
    if (this.options.enableLogging) {
      console.log(`TensorControls: Tensor damaged - ${data.id}`);
    }
  }
  
  /**
   * Handle control value changed event
   * @param {Object} data - Event data
   * @private
   */
  _handleControlValueChanged(data) {
    const { controlId, value } = data;
    
    // Handle control value change based on control ID
    switch (controlId) {
      case 'tensor-selector':
        this._handleTensorSelected(value);
        break;
        
      case 'damage-level':
        // No action needed, damage level is used when damage button is clicked
        break;
        
      case 'healing-factor':
        // No action needed, healing factor is used when heal button is clicked
        break;
    }
  }
  
  /**
   * Handle action executed event
   * @param {Object} data - Event data
   * @private
   */
  _handleActionExecuted(data) {
    const { action, params } = data;
    
    // Handle action based on type
    switch (action) {
      case 'register-tensor':
        this._handleRegisterTensorAction(params);
        break;
        
      case 'heal-tensor':
        this._handleHealTensorAction(params);
        break;
        
      case 'damage-tensor':
        this._handleDamageTensorAction(params);
        break;
    }
  }
  
  /**
   * Handle tensor selected
   * @param {string} tensorId - Tensor ID
   * @private
   */
  _handleTensorSelected(tensorId) {
    // Update selected tensor
    this.state.selectedTensorId = tensorId;
    this.state.lastUpdate = Date.now();
    
    // Update tensor details controls
    this._updateTensorDetailsControls(tensorId);
    
    if (this.options.enableLogging) {
      console.log(`TensorControls: Tensor selected - ${tensorId}`);
    }
  }
  
  /**
   * Handle register tensor action
   * @param {Object} params - Action parameters
   * @private
   */
  async _handleRegisterTensorAction(params) {
    if (!this.state.isConnected) {
      if (this.options.enableLogging) {
        console.log('TensorControls: Not connected');
      }
      return;
    }
    
    try {
      // Send register tensor message
      const response = await this.client.send({
        component: 'tensor',
        type: 'register-tensor',
        id: params.id,
        tensor: params.tensor,
        domain: params.domain || 'universal'
      });
      
      if (this.options.enableLogging) {
        console.log(`TensorControls: Tensor registered - ${params.id}`);
      }
      
      // Add tensor to list
      if (response && response.result && response.result.tensor) {
        this.state.tensors.set(params.id, response.result.tensor);
        this.state.lastUpdate = Date.now();
        
        // Update tensor list control
        this._updateTensorListControl();
      }
    } catch (error) {
      if (this.options.enableLogging) {
        console.error(`TensorControls: Error registering tensor ${params.id}:`, error);
      }
    }
  }
  
  /**
   * Handle heal tensor action
   * @param {Object} params - Action parameters
   * @private
   */
  async _handleHealTensorAction(params) {
    if (!this.state.isConnected) {
      if (this.options.enableLogging) {
        console.log('TensorControls: Not connected');
      }
      return;
    }
    
    const tensorId = params.id || this.state.selectedTensorId;
    
    if (!tensorId) {
      if (this.options.enableLogging) {
        console.log('TensorControls: No tensor selected');
      }
      return;
    }
    
    try {
      // Send heal tensor message
      const response = await this.client.send({
        component: 'tensor',
        type: 'heal-tensor',
        id: tensorId
      });
      
      if (this.options.enableLogging) {
        console.log(`TensorControls: Tensor healed - ${tensorId}`);
      }
      
      // Update tensor
      if (response && response.result && response.result.tensor) {
        this.state.tensors.set(tensorId, response.result.tensor);
        this.state.lastUpdate = Date.now();
        
        // Update tensor details controls
        this._updateTensorDetailsControls(tensorId);
      }
    } catch (error) {
      if (this.options.enableLogging) {
        console.error(`TensorControls: Error healing tensor ${tensorId}:`, error);
      }
    }
  }
  
  /**
   * Handle damage tensor action
   * @param {Object} params - Action parameters
   * @private
   */
  async _handleDamageTensorAction(params) {
    if (!this.state.isConnected) {
      if (this.options.enableLogging) {
        console.log('TensorControls: Not connected');
      }
      return;
    }
    
    const tensorId = params.id || this.state.selectedTensorId;
    
    if (!tensorId) {
      if (this.options.enableLogging) {
        console.log('TensorControls: No tensor selected');
      }
      return;
    }
    
    // Get damage level
    const damageLevel = params.damageLevel || this.controlPanel.getControlValue('damage-level') || 0.5;
    
    try {
      // Send damage tensor message
      const response = await this.client.send({
        component: 'tensor',
        type: 'damage-tensor',
        id: tensorId,
        damageLevel
      });
      
      if (this.options.enableLogging) {
        console.log(`TensorControls: Tensor damaged - ${tensorId} (${damageLevel})`);
      }
      
      // Update tensor
      if (response && response.result && response.result.tensor) {
        this.state.tensors.set(tensorId, response.result.tensor);
        this.state.lastUpdate = Date.now();
        
        // Update tensor details controls
        this._updateTensorDetailsControls(tensorId);
      }
    } catch (error) {
      if (this.options.enableLogging) {
        console.error(`TensorControls: Error damaging tensor ${tensorId}:`, error);
      }
    }
  }
  
  /**
   * Register controls
   * @private
   */
  _registerControls() {
    // Register tensor selector
    this.controlPanel.registerControl('tensor-selector', {
      type: 'select',
      label: 'Tensor',
      options: [],
      defaultValue: '',
      validate: (value) => typeof value === 'string'
    }, 'tensor-controls');
    
    // Register tensor details controls
    this.controlPanel.registerControl('tensor-health', {
      type: 'progress',
      label: 'Health',
      min: 0,
      max: 1,
      defaultValue: 0,
      readOnly: true
    }, 'tensor-details');
    
    this.controlPanel.registerControl('tensor-entropy', {
      type: 'progress',
      label: 'Entropy Containment',
      min: 0,
      max: 1,
      defaultValue: 0,
      readOnly: true
    }, 'tensor-details');
    
    this.controlPanel.registerControl('tensor-healing-cycles', {
      type: 'number',
      label: 'Healing Cycles',
      min: 0,
      defaultValue: 0,
      readOnly: true
    }, 'tensor-details');
    
    // Register tensor action controls
    this.controlPanel.registerControl('damage-level', {
      type: 'slider',
      label: 'Damage Level',
      min: 0,
      max: 1,
      step: 0.1,
      defaultValue: 0.5,
      validate: (value) => typeof value === 'number' && value >= 0 && value <= 1
    }, 'tensor-actions');
    
    this.controlPanel.registerControl('healing-factor', {
      type: 'slider',
      label: 'Healing Factor',
      min: 0,
      max: 1,
      step: 0.1,
      defaultValue: 0.6,
      validate: (value) => typeof value === 'number' && value >= 0 && value <= 1
    }, 'tensor-actions');
  }
  
  /**
   * Update tensor list control
   * @private
   */
  _updateTensorListControl() {
    // Get tensor IDs
    const tensorIds = Array.from(this.state.tensors.keys());
    
    // Update tensor selector options
    const tensorSelector = this.controlPanel.getControl('tensor-selector');
    
    if (tensorSelector) {
      tensorSelector.options = tensorIds.map((id) => ({
        value: id,
        label: id
      }));
      
      // Update control
      this.controlPanel.registerControl('tensor-selector', tensorSelector, 'tensor-controls');
      
      // If no tensor is selected and there are tensors available, select the first one
      if (!this.state.selectedTensorId && tensorIds.length > 0) {
        this.controlPanel.setControlValue('tensor-selector', tensorIds[0]);
      }
    }
  }
  
  /**
   * Update tensor details controls
   * @param {string} tensorId - Tensor ID
   * @private
   */
  _updateTensorDetailsControls(tensorId) {
    // Get tensor
    const tensor = this.state.tensors.get(tensorId);
    
    if (!tensor) {
      return;
    }
    
    // Update tensor health
    this.controlPanel.setControlValue('tensor-health', tensor.health || 0);
    
    // Update tensor entropy containment
    this.controlPanel.setControlValue('tensor-entropy', tensor.entropyContainment || 0);
    
    // Update tensor healing cycles
    this.controlPanel.setControlValue('tensor-healing-cycles', tensor.healingCycles || 0);
  }
  
  /**
   * Fetch tensors from the server
   * @private
   */
  async _fetchTensors() {
    if (!this.state.isConnected) {
      if (this.options.enableLogging) {
        console.log('TensorControls: Not connected');
      }
      return;
    }
    
    try {
      // TODO: Implement get-tensors message to fetch all tensors
      // For now, we'll just use the tensors that are registered during the session
    } catch (error) {
      if (this.options.enableLogging) {
        console.error('TensorControls: Error fetching tensors:', error);
      }
    }
  }
  
  /**
   * Register a tensor
   * @param {string} id - Tensor ID
   * @param {Object} tensor - Tensor data
   * @param {string} [domain='universal'] - Domain
   * @returns {Promise<Object>} - Promise that resolves with the registered tensor
   */
  async registerTensor(id, tensor, domain = 'universal') {
    return this.controlPanel.executeAction('register-tensor', {
      id,
      tensor,
      domain
    });
  }
  
  /**
   * Heal a tensor
   * @param {string} [id] - Tensor ID (uses selected tensor if not provided)
   * @returns {Promise<Object>} - Promise that resolves with the healed tensor
   */
  async healTensor(id) {
    return this.controlPanel.executeAction('heal-tensor', {
      id: id || this.state.selectedTensorId
    });
  }
  
  /**
   * Damage a tensor
   * @param {string} [id] - Tensor ID (uses selected tensor if not provided)
   * @param {number} [damageLevel] - Damage level (uses control value if not provided)
   * @returns {Promise<Object>} - Promise that resolves with the damaged tensor
   */
  async damageTensor(id, damageLevel) {
    return this.controlPanel.executeAction('damage-tensor', {
      id: id || this.state.selectedTensorId,
      damageLevel: damageLevel || this.controlPanel.getControlValue('damage-level') || 0.5
    });
  }
}

module.exports = TensorControls;
