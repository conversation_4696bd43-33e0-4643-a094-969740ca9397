import React, { useState, useEffect, useRef } from 'react';

const FloatingNovaConcierge = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState([
    { role: 'assistant', content: 'Hello! I\'m <PERSON><PERSON><PERSON>cier<PERSON>, your API integration assistant. How can I help you today?' }
  ]);
  const [inputValue, setInputValue] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const messagesEndRef = useRef(null);
  
  useEffect(() => {
    if (isOpen) {
      scrollToBottom();
    }
  }, [messages, isOpen]);
  
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };
  
  const handleSubmit = (e) => {
    e.preventDefault();
    if (!inputValue.trim() || isProcessing) return;
    
    // Add user message
    const userMessage = { role: 'user', content: inputValue };
    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsProcessing(true);
    
    // Simulate AI processing
    setTimeout(() => {
      let response;
      const lowerInput = inputValue.toLowerCase();
      
      if (lowerInput.includes('test') || lowerInput.includes('abilities') || lowerInput.includes('what can you do')) {
        response = { 
          role: 'assistant', 
          content: "I'd be happy to show you what I can do! Here are some ways I can help you:",
          suggestions: [
            { title: "Compare NovaFuse to competitors", description: "See how NovaFuse stacks up against other solutions" },
            { title: "Explore success stories", description: "Learn how other companies have benefited from NovaFuse" },
            { title: "Calculate your potential ROI", description: "See how much you could save with NovaFuse" },
            { title: "Learn about Partner Empowerment", description: "Discover our unique approach to partnerships" },
            { title: "Find the right API for your needs", description: "Get personalized API recommendations" }
          ]
        };
      } else if (lowerInput.includes('compare') || lowerInput.includes('competitor')) {
        response = { 
          role: 'assistant', 
          content: "Here's how NovaFuse compares to traditional GRC solutions:",
          comparison: {
            title: "NovaFuse vs. Traditional GRC Solutions",
            advantages: [
              "3,142x faster data normalization (0.07ms vs 220ms per finding)",
              "Multi-cloud support vs. single-cloud coverage",
              "Automated remediation across all platforms",
              "Unified compliance view across all cloud providers",
              "Partner Empowerment model vs. traditional licensing"
            ]
          }
        };
      } else if (lowerInput.includes('partner') || lowerInput.includes('empowerment')) {
        response = { 
          role: 'assistant', 
          content: "Partner Empowerment is our unique approach to partnerships. Unlike traditional vendor relationships, we believe in building with partners rather than selling to them.",
          partnerInfo: {
            title: "Partner Empowerment Program",
            description: "Our Partner Empowerment program is designed to create mutual success through revenue sharing, co-marketing, and technical enablement.",
            benefits: [
              "Up to 90% revenue sharing",
              "Co-marketing opportunities",
              "Technical enablement and support",
              "Joint product development"
            ]
          }
        };
      } else if (lowerInput.includes('roi') || lowerInput.includes('return') || lowerInput.includes('save')) {
        response = { 
          role: 'assistant', 
          content: "I can help you calculate your potential ROI with NovaFuse. Based on industry averages, companies typically see:",
          roi: {
            metrics: [
              "75% reduction in compliance management effort",
              "90% faster audit response time",
              "85% automation of common remediation tasks",
              "$1.5M annual savings in compliance management costs"
            ]
          }
        };
      } else {
        response = { 
          role: 'assistant', 
          content: "I'd be happy to help with that. Could you provide more details about your specific needs or use case? This will help me give you the most relevant information about our APIs and integration options." 
        };
      }
      
      setMessages(prev => [...prev, response]);
      setIsProcessing(false);
    }, 1500);
  };

  const renderMessageContent = (message) => {
    if (message.role === 'user') {
      return <div>{message.content}</div>;
    }
    
    return (
      <div>
        <div>{message.content}</div>
        
        {message.suggestions && (
          <div className="mt-4 space-y-2">
            {message.suggestions.map((suggestion, index) => (
              <div 
                key={index} 
                className="bg-gray-800 p-3 rounded cursor-pointer hover:bg-gray-700"
                onClick={() => {
                  setMessages(prev => [...prev, 
                    { role: 'user', content: suggestion.title },
                    { role: 'assistant', content: `I'll help you ${suggestion.title.toLowerCase()}.` }
                  ]);
                }}
              >
                <div className="font-medium">{suggestion.title}</div>
                <div className="text-sm text-gray-400">{suggestion.description}</div>
              </div>
            ))}
          </div>
        )}
        
        {message.comparison && (
          <div className="mt-4 bg-gray-800 p-4 rounded">
            <div className="font-semibold mb-2">{message.comparison.title}</div>
            <ul className="space-y-1">
              {message.comparison.advantages.map((advantage, index) => (
                <li key={index} className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  <span>{advantage}</span>
                </li>
              ))}
            </ul>
          </div>
        )}
        
        {message.partnerInfo && (
          <div className="mt-4 bg-gray-800 p-4 rounded">
            <div className="font-semibold mb-2">{message.partnerInfo.title}</div>
            <p className="mb-2 text-gray-300">{message.partnerInfo.description}</p>
            <div className="font-medium mt-3 mb-1">Key Benefits:</div>
            <ul className="space-y-1">
              {message.partnerInfo.benefits.map((benefit, index) => (
                <li key={index} className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  <span>{benefit}</span>
                </li>
              ))}
            </ul>
          </div>
        )}
        
        {message.roi && (
          <div className="mt-4 bg-gray-800 p-4 rounded">
            <div className="font-semibold mb-2">ROI Metrics</div>
            <ul className="space-y-1">
              {message.roi.metrics.map((metric, index) => (
                <li key={index} className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  <span>{metric}</span>
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="fixed bottom-4 right-4 z-50">
      {isOpen ? (
        <div className="bg-secondary rounded-lg shadow-lg overflow-hidden flex flex-col w-80 md:w-96 h-[500px] border border-gray-700">
          <div className="bg-gray-800 p-4 flex justify-between items-center">
            <div>
              <h2 className="text-lg font-bold">NovaConcierge</h2>
              <p className="text-xs text-gray-400">Your API integration assistant</p>
            </div>
            <button 
              onClick={() => setIsOpen(false)}
              className="text-gray-400 hover:text-white"
              aria-label="Close NovaConcierge"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {messages.map((message, index) => (
              <div 
                key={index} 
                className={`p-3 rounded-lg max-w-[80%] ${
                  message.role === 'user' 
                    ? 'bg-blue-600 ml-auto' 
                    : 'bg-gray-700'
                }`}
              >
                {renderMessageContent(message)}
              </div>
            ))}
            
            {isProcessing && (
              <div className="bg-gray-700 p-3 rounded-lg max-w-[80%] flex items-center space-x-2">
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-pulse"></div>
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-pulse delay-150"></div>
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-pulse delay-300"></div>
              </div>
            )}
            
            <div ref={messagesEndRef} />
          </div>
          
          <form onSubmit={handleSubmit} className="p-4 border-t border-gray-700 flex">
            <input
              type="text"
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              placeholder="Ask about API integration..."
              className="flex-1 bg-gray-700 text-white rounded-l-lg px-4 py-2 focus:outline-none"
              disabled={isProcessing}
            />
            <button
              type="submit"
              className="bg-blue-600 text-white px-4 py-2 rounded-r-lg hover:bg-blue-700 focus:outline-none disabled:opacity-50"
              disabled={isProcessing || !inputValue.trim()}
            >
              Send
            </button>
          </form>
        </div>
      ) : (
        <button
          onClick={() => setIsOpen(true)}
          className="bg-blue-600 text-white p-4 rounded-full shadow-lg hover:bg-blue-700 focus:outline-none flex items-center justify-center"
          aria-label="Open NovaConcierge"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
          </svg>
        </button>
      )}
    </div>
  );
};

export default FloatingNovaConcierge;
