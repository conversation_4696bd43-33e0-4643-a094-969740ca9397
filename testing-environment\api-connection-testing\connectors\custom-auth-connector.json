{"metadata": {"name": "Custom Auth Connector", "version": "1.0.0", "category": "Test", "description": "Test connector for Custom authentication", "author": "NovaGRC", "tags": ["test", "custom-auth"]}, "authentication": {"type": "CUSTOM", "fields": {"customAuthValue": {"type": "string", "description": "Custom Authentication Value", "required": true, "sensitive": true}, "headerName": {"type": "string", "description": "Header name for the custom authentication", "required": true, "default": "X-Custom-Auth"}}, "testConnection": {"endpoint": "/health", "method": "GET", "expectedResponse": {"status": 200}}}, "configuration": {"baseUrl": "http://localhost:3005", "headers": {"Content-Type": "application/json"}, "timeout": 30000, "retryPolicy": {"maxRetries": 3, "backoffStrategy": "exponential"}}, "endpoints": [{"id": "getResource", "name": "Get Resource", "path": "/custom/resource", "method": "GET", "parameters": {"query": {}, "path": {}, "body": {}}, "response": {"successCode": 200}}], "mappings": [{"sourceEndpoint": "getResource", "targetSystem": "NovaGRC", "targetEntity": "Resource", "transformations": [{"source": "$.data.id", "target": "resourceId", "transform": "identity"}, {"source": "$.data.name", "target": "resourceName", "transform": "identity"}]}], "events": {"webhooks": [], "polling": []}}