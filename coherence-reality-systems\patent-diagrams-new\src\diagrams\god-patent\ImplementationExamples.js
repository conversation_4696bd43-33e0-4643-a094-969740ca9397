import React from 'react';
import {
  DiagramFrame,
  ComponentBox,
  ComponentLabel,
  ComponentNumber,
  Arrow,
  ContainerBox,
  ContainerLabel,
  CurvedArrow,
  DiagramLegend,
  LegendItem,
  LegendColor,
  LegendText
} from '../../components/DiagramComponents';

const ImplementationExamples = () => {
  return (
    <DiagramFrame>
      <ContainerBox width="750px" height="400px" left="25px" top="20px">
        <ContainerLabel>IMPLEMENTATION EXAMPLES</ContainerLabel>
      </ContainerBox>
      
      {/* Enterprise Implementation */}
      <ContainerBox width="330px" height="160px" left="50px" top="70px">
        <ContainerLabel>ENTERPRISE IMPLEMENTATION</ContainerLabel>
      </ContainerBox>
      
      <ComponentBox left="70px" top="110px" width="130px" height="40px">
        <ComponentNumber>501</ComponentNumber>
        <ComponentLabel>Cyber-Safety</ComponentLabel>
        Protocol Core
      </ComponentBox>
      
      <ComponentBox left="230px" top="110px" width="130px" height="40px">
        <ComponentNumber>502</ComponentNumber>
        <ComponentLabel>Enterprise</ComponentLabel>
        Systems
      </ComponentBox>
      
      <Arrow left="200px" top="130px" width="30px" />
      
      <ComponentBox left="70px" top="170px" width="130px" height="40px">
        <ComponentNumber>503</ComponentNumber>
        <ComponentLabel>Compliance</ComponentLabel>
        Dashboard
      </ComponentBox>
      
      <ComponentBox left="230px" top="170px" width="130px" height="40px">
        <ComponentNumber>504</ComponentNumber>
        <ComponentLabel>Security</ComponentLabel>
        Operations
      </ComponentBox>
      
      <Arrow left="200px" top="190px" width="30px" transform="rotate(180deg)" />
      
      {/* Cloud Implementation */}
      <ContainerBox width="330px" height="160px" left="420px" top="70px">
        <ContainerLabel>CLOUD IMPLEMENTATION</ContainerLabel>
      </ContainerBox>
      
      <ComponentBox left="440px" top="110px" width="130px" height="40px">
        <ComponentNumber>505</ComponentNumber>
        <ComponentLabel>Cyber-Safety</ComponentLabel>
        Protocol Core
      </ComponentBox>
      
      <ComponentBox left="600px" top="110px" width="130px" height="40px">
        <ComponentNumber>506</ComponentNumber>
        <ComponentLabel>Cloud</ComponentLabel>
        Services
      </ComponentBox>
      
      <Arrow left="570px" top="130px" width="30px" />
      
      <ComponentBox left="440px" top="170px" width="130px" height="40px">
        <ComponentNumber>507</ComponentNumber>
        <ComponentLabel>Multi-Cloud</ComponentLabel>
        Compliance
      </ComponentBox>
      
      <ComponentBox left="600px" top="170px" width="130px" height="40px">
        <ComponentNumber>508</ComponentNumber>
        <ComponentLabel>DevSecOps</ComponentLabel>
        Integration
      </ComponentBox>
      
      <Arrow left="570px" top="190px" width="30px" transform="rotate(180deg)" />
      
      {/* Hybrid Implementation */}
      <ContainerBox width="330px" height="160px" left="50px" top="250px">
        <ContainerLabel>HYBRID IMPLEMENTATION</ContainerLabel>
      </ContainerBox>
      
      <ComponentBox left="70px" top="290px" width="130px" height="40px">
        <ComponentNumber>509</ComponentNumber>
        <ComponentLabel>On-Premises</ComponentLabel>
        Components
      </ComponentBox>
      
      <ComponentBox left="230px" top="290px" width="130px" height="40px">
        <ComponentNumber>510</ComponentNumber>
        <ComponentLabel>Cloud</ComponentLabel>
        Components
      </ComponentBox>
      
      <Arrow left="200px" top="310px" width="30px" />
      <Arrow left="200px" top="310px" width="30px" transform="rotate(180deg)" />
      
      <ComponentBox left="70px" top="350px" width="130px" height="40px">
        <ComponentNumber>511</ComponentNumber>
        <ComponentLabel>Unified</ComponentLabel>
        Management
      </ComponentBox>
      
      <ComponentBox left="230px" top="350px" width="130px" height="40px">
        <ComponentNumber>512</ComponentNumber>
        <ComponentLabel>Cross-Environment</ComponentLabel>
        Compliance
      </ComponentBox>
      
      <Arrow left="200px" top="370px" width="30px" />
      <Arrow left="200px" top="370px" width="30px" transform="rotate(180deg)" />
      
      {/* SaaS Implementation */}
      <ContainerBox width="330px" height="160px" left="420px" top="250px">
        <ContainerLabel>SAAS IMPLEMENTATION</ContainerLabel>
      </ContainerBox>
      
      <ComponentBox left="440px" top="290px" width="130px" height="40px">
        <ComponentNumber>513</ComponentNumber>
        <ComponentLabel>Multi-Tenant</ComponentLabel>
        Architecture
      </ComponentBox>
      
      <ComponentBox left="600px" top="290px" width="130px" height="40px">
        <ComponentNumber>514</ComponentNumber>
        <ComponentLabel>Tenant-Specific</ComponentLabel>
        Compliance
      </ComponentBox>
      
      <Arrow left="570px" top="310px" width="30px" />
      
      <ComponentBox left="440px" top="350px" width="130px" height="40px">
        <ComponentNumber>515</ComponentNumber>
        <ComponentLabel>API-Based</ComponentLabel>
        Integration
      </ComponentBox>
      
      <ComponentBox left="600px" top="350px" width="130px" height="40px">
        <ComponentNumber>516</ComponentNumber>
        <ComponentLabel>Automated</ComponentLabel>
        Deployment
      </ComponentBox>
      
      <Arrow left="570px" top="370px" width="30px" transform="rotate(180deg)" />
      
      {/* Legend */}
      <DiagramLegend>
        <LegendItem>
          <LegendColor color="#fff" />
          <LegendText>Enterprise</LegendText>
        </LegendItem>
        <LegendItem>
          <LegendColor color="#fff" />
          <LegendText>Cloud</LegendText>
        </LegendItem>
        <LegendItem>
          <LegendColor color="#fff" />
          <LegendText>Hybrid</LegendText>
        </LegendItem>
        <LegendItem>
          <LegendColor color="#fff" />
          <LegendText>SaaS</LegendText>
        </LegendItem>
      </DiagramLegend>
    </DiagramFrame>
  );
};

export default ImplementationExamples;
