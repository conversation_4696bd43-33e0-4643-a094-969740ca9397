#!/usr/bin/env node
/**
 * CHAEONIX Metrics Reset Script
 * Resets all profit analytics to $0.00 and clears simulation data
 */

const http = require('http');

console.log('🔄 CHAEONIX METRICS RESET UTILITY');
console.log('=' * 50);

const resetData = JSON.stringify({
    action: 'RESET_METRICS'
});

const options = {
    hostname: 'localhost',
    port: 3141,
    path: '/api/analytics/profit-tracker',
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(resetData)
    }
};

console.log('📡 Sending reset request to CHAEONIX dashboard...');

const req = http.request(options, (res) => {
    console.log(`📊 Response Status: ${res.statusCode}`);
    
    let data = '';
    
    res.on('data', (chunk) => {
        data += chunk;
    });
    
    res.on('end', () => {
        try {
            const result = JSON.parse(data);
            
            if (result.success) {
                console.log('✅ SUCCESS! Metrics reset completed');
                console.log(`💰 Message: ${result.message}`);
                console.log(`📈 Total Trades: ${result.total_trades}`);
                console.log(`💵 Total Profit: $${result.total_profit || 0}`);
                console.log(`⏰ Reset Time: ${result.reset_timestamp}`);
                console.log('');
                console.log('🎯 NEXT STEPS:');
                console.log('1. Refresh your CHAEONIX dashboard');
                console.log('2. Verify all profit numbers show $0.00');
                console.log('3. Start real trading with clean metrics');
                console.log('');
                console.log('🚀 CHAEONIX is ready for real trading data!');
            } else {
                console.log('❌ FAILED! Reset was not successful');
                console.log(`🚨 Error: ${result.error || 'Unknown error'}`);
            }
        } catch (error) {
            console.log('❌ FAILED! Could not parse response');
            console.log(`🚨 Raw response: ${data}`);
            console.log(`🚨 Parse error: ${error.message}`);
        }
    });
});

req.on('error', (error) => {
    console.log('❌ FAILED! Network error occurred');
    console.log(`🚨 Error: ${error.message}`);
    console.log('');
    console.log('🔧 TROUBLESHOOTING:');
    console.log('1. Make sure CHAEONIX dashboard is running (npm run dev)');
    console.log('2. Verify dashboard is accessible at http://localhost:3141');
    console.log('3. Check if port 3141 is available');
});

req.write(resetData);
req.end();
