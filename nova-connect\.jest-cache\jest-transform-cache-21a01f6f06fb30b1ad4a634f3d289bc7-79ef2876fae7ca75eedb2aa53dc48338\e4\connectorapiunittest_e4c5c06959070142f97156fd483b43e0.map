{"version": 3, "names": ["expect", "require", "sinon", "supertest", "connectorApi", "connectorRegistry", "authenticationManager", "connectorExecutor", "describe", "app", "request", "sandbox", "beforeEach", "createSandbox", "stub", "resolves", "after<PERSON>ach", "restore", "it", "listenStub", "callsFake", "port", "callback", "close", "result", "initialize", "to", "be", "true", "calledOnce", "rejects", "Error", "fail", "error", "message", "equal", "response", "get", "status", "body", "deep", "connectors", "id", "name", "type", "description", "authentication", "fields", "<PERSON><PERSON><PERSON><PERSON>", "label", "required", "sensitive", "default", "returns", "an", "have", "lengthOf", "undefined", "category", "throws", "property"], "sources": ["connector-api.unit.test.js"], "sourcesContent": ["/**\n * Unit tests for the Connector API\n */\n\nconst { expect } = require('chai');\nconst sinon = require('sinon');\nconst supertest = require('supertest');\n\n// Import the modules to test\nconst connectorApi = require('../../api/connector-api');\nconst connectorRegistry = require('../../registry/connector-registry');\nconst authenticationManager = require('../../auth/authentication-manager');\nconst connectorExecutor = require('../../executor/connector-executor');\n\ndescribe('Connector API', () => {\n  let app;\n  let request;\n  let sandbox;\n\n  beforeEach(() => {\n    // Create a sinon sandbox for stubs\n    sandbox = sinon.createSandbox();\n\n    // Stub the dependencies\n    sandbox.stub(connectorRegistry, 'initialize').resolves();\n    sandbox.stub(authenticationManager, 'initialize').resolves();\n    sandbox.stub(connectorExecutor, 'initialize').resolves();\n\n    // Get the Express app\n    app = connectorApi.app;\n    request = supertest(app);\n  });\n\n  afterEach(() => {\n    // Restore the stubs\n    sandbox.restore();\n  });\n\n  describe('initialize()', () => {\n    it('should initialize dependencies and start the server', async () => {\n      // Stub the app.listen method\n      const listenStub = sandbox.stub(app, 'listen').callsFake((port, callback) => {\n        callback();\n        return { close: () => {} };\n      });\n\n      // Call the initialize method\n      const result = await connectorApi.initialize();\n\n      // Verify the result\n      expect(result).to.be.true;\n\n      // Verify the dependencies were initialized\n      expect(connectorRegistry.initialize.calledOnce).to.be.true;\n      expect(authenticationManager.initialize.calledOnce).to.be.true;\n      expect(connectorExecutor.initialize.calledOnce).to.be.true;\n\n      // Verify the server was started\n      expect(listenStub.calledOnce).to.be.true;\n    });\n\n    it('should handle initialization errors', async () => {\n      // Stub the connectorRegistry.initialize method to throw an error\n      connectorRegistry.initialize.rejects(new Error('Initialization error'));\n\n      try {\n        // Call the initialize method\n        await connectorApi.initialize();\n        // If we get here, the test should fail\n        expect.fail('Expected initialize to throw an error');\n      } catch (error) {\n        // Verify the error\n        expect(error.message).to.equal('Initialization error');\n      }\n    });\n  });\n\n  describe('GET /health', () => {\n    it('should return a 200 status code', async () => {\n      // Make a request to the health endpoint\n      const response = await request.get('/health');\n\n      // Verify the response\n      expect(response.status).to.equal(200);\n      expect(response.body).to.deep.equal({ status: 'ok' });\n    });\n  });\n\n  describe('GET /connectors', () => {\n    it('should return a list of connectors', async () => {\n      // Stub the connectorRegistry.getAllConnectors method\n      const connectors = [\n        {\n          id: '1',\n          name: 'Test Connector',\n          type: 'http',\n          description: 'Test connector for unit tests',\n          status: 'active',\n          authentication: {\n            fields: {\n              apiKey: {\n                type: 'string',\n                label: 'API Key',\n                required: true,\n                sensitive: true,\n                default: 'test-api-key'\n              }\n            }\n          }\n        }\n      ];\n      sandbox.stub(connectorRegistry, 'getAllConnectors').returns(connectors);\n\n      // Make a request to the connectors endpoint\n      const response = await request.get('/connectors');\n\n      // Verify the response\n      expect(response.status).to.equal(200);\n      expect(response.body).to.be.an('array');\n      expect(response.body).to.have.lengthOf(1);\n      expect(response.body[0].id).to.equal('1');\n      expect(response.body[0].name).to.equal('Test Connector');\n      expect(response.body[0].authentication.fields.apiKey.default).to.be.undefined;\n    });\n\n    it('should filter connectors by category', async () => {\n      // Stub the connectorRegistry.getConnectorsByCategory method\n      const connectors = [\n        {\n          id: '1',\n          name: 'Test Connector',\n          type: 'http',\n          category: 'test',\n          description: 'Test connector for unit tests',\n          status: 'active',\n          authentication: {\n            fields: {}\n          }\n        }\n      ];\n      sandbox.stub(connectorRegistry, 'getConnectorsByCategory').returns(connectors);\n\n      // Make a request to the connectors endpoint with a category filter\n      const response = await request.get('/connectors?category=test');\n\n      // Verify the response\n      expect(response.status).to.equal(200);\n      expect(response.body).to.be.an('array');\n      expect(response.body).to.have.lengthOf(1);\n      expect(response.body[0].id).to.equal('1');\n      expect(response.body[0].category).to.equal('test');\n    });\n\n    it('should search connectors by query', async () => {\n      // Stub the connectorRegistry.searchConnectors method\n      const connectors = [\n        {\n          id: '1',\n          name: 'Test Connector',\n          type: 'http',\n          description: 'Test connector for unit tests',\n          status: 'active',\n          authentication: {\n            fields: {}\n          }\n        }\n      ];\n      sandbox.stub(connectorRegistry, 'searchConnectors').returns(connectors);\n\n      // Make a request to the connectors endpoint with a search query\n      const response = await request.get('/connectors?query=test');\n\n      // Verify the response\n      expect(response.status).to.equal(200);\n      expect(response.body).to.be.an('array');\n      expect(response.body).to.have.lengthOf(1);\n      expect(response.body[0].id).to.equal('1');\n      expect(response.body[0].name).to.equal('Test Connector');\n    });\n\n    it('should handle errors', async () => {\n      // Stub the connectorRegistry.getAllConnectors method to throw an error\n      sandbox.stub(connectorRegistry, 'getAllConnectors').throws(new Error('Test error'));\n\n      // Make a request to the connectors endpoint\n      const response = await request.get('/connectors');\n\n      // Verify the response\n      expect(response.status).to.equal(500);\n      expect(response.body).to.have.property('error');\n      expect(response.body.error).to.equal('Test error');\n    });\n  });\n});\n"], "mappings": "AAAA;AACA;AACA;;AAEA,MAAM;EAAEA;AAAO,CAAC,GAAGC,OAAO,CAAC,MAAM,CAAC;AAClC,MAAMC,KAAK,GAAGD,OAAO,CAAC,OAAO,CAAC;AAC9B,MAAME,SAAS,GAAGF,OAAO,CAAC,WAAW,CAAC;;AAEtC;AACA,MAAMG,YAAY,GAAGH,OAAO,CAAC,yBAAyB,CAAC;AACvD,MAAMI,iBAAiB,GAAGJ,OAAO,CAAC,mCAAmC,CAAC;AACtE,MAAMK,qBAAqB,GAAGL,OAAO,CAAC,mCAAmC,CAAC;AAC1E,MAAMM,iBAAiB,GAAGN,OAAO,CAAC,mCAAmC,CAAC;AAEtEO,QAAQ,CAAC,eAAe,EAAE,MAAM;EAC9B,IAAIC,GAAG;EACP,IAAIC,OAAO;EACX,IAAIC,OAAO;EAEXC,UAAU,CAAC,MAAM;IACf;IACAD,OAAO,GAAGT,KAAK,CAACW,aAAa,CAAC,CAAC;;IAE/B;IACAF,OAAO,CAACG,IAAI,CAACT,iBAAiB,EAAE,YAAY,CAAC,CAACU,QAAQ,CAAC,CAAC;IACxDJ,OAAO,CAACG,IAAI,CAACR,qBAAqB,EAAE,YAAY,CAAC,CAACS,QAAQ,CAAC,CAAC;IAC5DJ,OAAO,CAACG,IAAI,CAACP,iBAAiB,EAAE,YAAY,CAAC,CAACQ,QAAQ,CAAC,CAAC;;IAExD;IACAN,GAAG,GAAGL,YAAY,CAACK,GAAG;IACtBC,OAAO,GAAGP,SAAS,CAACM,GAAG,CAAC;EAC1B,CAAC,CAAC;EAEFO,SAAS,CAAC,MAAM;IACd;IACAL,OAAO,CAACM,OAAO,CAAC,CAAC;EACnB,CAAC,CAAC;EAEFT,QAAQ,CAAC,cAAc,EAAE,MAAM;IAC7BU,EAAE,CAAC,qDAAqD,EAAE,YAAY;MACpE;MACA,MAAMC,UAAU,GAAGR,OAAO,CAACG,IAAI,CAACL,GAAG,EAAE,QAAQ,CAAC,CAACW,SAAS,CAAC,CAACC,IAAI,EAAEC,QAAQ,KAAK;QAC3EA,QAAQ,CAAC,CAAC;QACV,OAAO;UAAEC,KAAK,EAAEA,CAAA,KAAM,CAAC;QAAE,CAAC;MAC5B,CAAC,CAAC;;MAEF;MACA,MAAMC,MAAM,GAAG,MAAMpB,YAAY,CAACqB,UAAU,CAAC,CAAC;;MAE9C;MACAzB,MAAM,CAACwB,MAAM,CAAC,CAACE,EAAE,CAACC,EAAE,CAACC,IAAI;;MAEzB;MACA5B,MAAM,CAACK,iBAAiB,CAACoB,UAAU,CAACI,UAAU,CAAC,CAACH,EAAE,CAACC,EAAE,CAACC,IAAI;MAC1D5B,MAAM,CAACM,qBAAqB,CAACmB,UAAU,CAACI,UAAU,CAAC,CAACH,EAAE,CAACC,EAAE,CAACC,IAAI;MAC9D5B,MAAM,CAACO,iBAAiB,CAACkB,UAAU,CAACI,UAAU,CAAC,CAACH,EAAE,CAACC,EAAE,CAACC,IAAI;;MAE1D;MACA5B,MAAM,CAACmB,UAAU,CAACU,UAAU,CAAC,CAACH,EAAE,CAACC,EAAE,CAACC,IAAI;IAC1C,CAAC,CAAC;IAEFV,EAAE,CAAC,qCAAqC,EAAE,YAAY;MACpD;MACAb,iBAAiB,CAACoB,UAAU,CAACK,OAAO,CAAC,IAAIC,KAAK,CAAC,sBAAsB,CAAC,CAAC;MAEvE,IAAI;QACF;QACA,MAAM3B,YAAY,CAACqB,UAAU,CAAC,CAAC;QAC/B;QACAzB,MAAM,CAACgC,IAAI,CAAC,uCAAuC,CAAC;MACtD,CAAC,CAAC,OAAOC,KAAK,EAAE;QACd;QACAjC,MAAM,CAACiC,KAAK,CAACC,OAAO,CAAC,CAACR,EAAE,CAACS,KAAK,CAAC,sBAAsB,CAAC;MACxD;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF3B,QAAQ,CAAC,aAAa,EAAE,MAAM;IAC5BU,EAAE,CAAC,iCAAiC,EAAE,YAAY;MAChD;MACA,MAAMkB,QAAQ,GAAG,MAAM1B,OAAO,CAAC2B,GAAG,CAAC,SAAS,CAAC;;MAE7C;MACArC,MAAM,CAACoC,QAAQ,CAACE,MAAM,CAAC,CAACZ,EAAE,CAACS,KAAK,CAAC,GAAG,CAAC;MACrCnC,MAAM,CAACoC,QAAQ,CAACG,IAAI,CAAC,CAACb,EAAE,CAACc,IAAI,CAACL,KAAK,CAAC;QAAEG,MAAM,EAAE;MAAK,CAAC,CAAC;IACvD,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF9B,QAAQ,CAAC,iBAAiB,EAAE,MAAM;IAChCU,EAAE,CAAC,oCAAoC,EAAE,YAAY;MACnD;MACA,MAAMuB,UAAU,GAAG,CACjB;QACEC,EAAE,EAAE,GAAG;QACPC,IAAI,EAAE,gBAAgB;QACtBC,IAAI,EAAE,MAAM;QACZC,WAAW,EAAE,+BAA+B;QAC5CP,MAAM,EAAE,QAAQ;QAChBQ,cAAc,EAAE;UACdC,MAAM,EAAE;YACNC,MAAM,EAAE;cACNJ,IAAI,EAAE,QAAQ;cACdK,KAAK,EAAE,SAAS;cAChBC,QAAQ,EAAE,IAAI;cACdC,SAAS,EAAE,IAAI;cACfC,OAAO,EAAE;YACX;UACF;QACF;MACF,CAAC,CACF;MACDzC,OAAO,CAACG,IAAI,CAACT,iBAAiB,EAAE,kBAAkB,CAAC,CAACgD,OAAO,CAACZ,UAAU,CAAC;;MAEvE;MACA,MAAML,QAAQ,GAAG,MAAM1B,OAAO,CAAC2B,GAAG,CAAC,aAAa,CAAC;;MAEjD;MACArC,MAAM,CAACoC,QAAQ,CAACE,MAAM,CAAC,CAACZ,EAAE,CAACS,KAAK,CAAC,GAAG,CAAC;MACrCnC,MAAM,CAACoC,QAAQ,CAACG,IAAI,CAAC,CAACb,EAAE,CAACC,EAAE,CAAC2B,EAAE,CAAC,OAAO,CAAC;MACvCtD,MAAM,CAACoC,QAAQ,CAACG,IAAI,CAAC,CAACb,EAAE,CAAC6B,IAAI,CAACC,QAAQ,CAAC,CAAC,CAAC;MACzCxD,MAAM,CAACoC,QAAQ,CAACG,IAAI,CAAC,CAAC,CAAC,CAACG,EAAE,CAAC,CAAChB,EAAE,CAACS,KAAK,CAAC,GAAG,CAAC;MACzCnC,MAAM,CAACoC,QAAQ,CAACG,IAAI,CAAC,CAAC,CAAC,CAACI,IAAI,CAAC,CAACjB,EAAE,CAACS,KAAK,CAAC,gBAAgB,CAAC;MACxDnC,MAAM,CAACoC,QAAQ,CAACG,IAAI,CAAC,CAAC,CAAC,CAACO,cAAc,CAACC,MAAM,CAACC,MAAM,CAACI,OAAO,CAAC,CAAC1B,EAAE,CAACC,EAAE,CAAC8B,SAAS;IAC/E,CAAC,CAAC;IAEFvC,EAAE,CAAC,sCAAsC,EAAE,YAAY;MACrD;MACA,MAAMuB,UAAU,GAAG,CACjB;QACEC,EAAE,EAAE,GAAG;QACPC,IAAI,EAAE,gBAAgB;QACtBC,IAAI,EAAE,MAAM;QACZc,QAAQ,EAAE,MAAM;QAChBb,WAAW,EAAE,+BAA+B;QAC5CP,MAAM,EAAE,QAAQ;QAChBQ,cAAc,EAAE;UACdC,MAAM,EAAE,CAAC;QACX;MACF,CAAC,CACF;MACDpC,OAAO,CAACG,IAAI,CAACT,iBAAiB,EAAE,yBAAyB,CAAC,CAACgD,OAAO,CAACZ,UAAU,CAAC;;MAE9E;MACA,MAAML,QAAQ,GAAG,MAAM1B,OAAO,CAAC2B,GAAG,CAAC,2BAA2B,CAAC;;MAE/D;MACArC,MAAM,CAACoC,QAAQ,CAACE,MAAM,CAAC,CAACZ,EAAE,CAACS,KAAK,CAAC,GAAG,CAAC;MACrCnC,MAAM,CAACoC,QAAQ,CAACG,IAAI,CAAC,CAACb,EAAE,CAACC,EAAE,CAAC2B,EAAE,CAAC,OAAO,CAAC;MACvCtD,MAAM,CAACoC,QAAQ,CAACG,IAAI,CAAC,CAACb,EAAE,CAAC6B,IAAI,CAACC,QAAQ,CAAC,CAAC,CAAC;MACzCxD,MAAM,CAACoC,QAAQ,CAACG,IAAI,CAAC,CAAC,CAAC,CAACG,EAAE,CAAC,CAAChB,EAAE,CAACS,KAAK,CAAC,GAAG,CAAC;MACzCnC,MAAM,CAACoC,QAAQ,CAACG,IAAI,CAAC,CAAC,CAAC,CAACmB,QAAQ,CAAC,CAAChC,EAAE,CAACS,KAAK,CAAC,MAAM,CAAC;IACpD,CAAC,CAAC;IAEFjB,EAAE,CAAC,mCAAmC,EAAE,YAAY;MAClD;MACA,MAAMuB,UAAU,GAAG,CACjB;QACEC,EAAE,EAAE,GAAG;QACPC,IAAI,EAAE,gBAAgB;QACtBC,IAAI,EAAE,MAAM;QACZC,WAAW,EAAE,+BAA+B;QAC5CP,MAAM,EAAE,QAAQ;QAChBQ,cAAc,EAAE;UACdC,MAAM,EAAE,CAAC;QACX;MACF,CAAC,CACF;MACDpC,OAAO,CAACG,IAAI,CAACT,iBAAiB,EAAE,kBAAkB,CAAC,CAACgD,OAAO,CAACZ,UAAU,CAAC;;MAEvE;MACA,MAAML,QAAQ,GAAG,MAAM1B,OAAO,CAAC2B,GAAG,CAAC,wBAAwB,CAAC;;MAE5D;MACArC,MAAM,CAACoC,QAAQ,CAACE,MAAM,CAAC,CAACZ,EAAE,CAACS,KAAK,CAAC,GAAG,CAAC;MACrCnC,MAAM,CAACoC,QAAQ,CAACG,IAAI,CAAC,CAACb,EAAE,CAACC,EAAE,CAAC2B,EAAE,CAAC,OAAO,CAAC;MACvCtD,MAAM,CAACoC,QAAQ,CAACG,IAAI,CAAC,CAACb,EAAE,CAAC6B,IAAI,CAACC,QAAQ,CAAC,CAAC,CAAC;MACzCxD,MAAM,CAACoC,QAAQ,CAACG,IAAI,CAAC,CAAC,CAAC,CAACG,EAAE,CAAC,CAAChB,EAAE,CAACS,KAAK,CAAC,GAAG,CAAC;MACzCnC,MAAM,CAACoC,QAAQ,CAACG,IAAI,CAAC,CAAC,CAAC,CAACI,IAAI,CAAC,CAACjB,EAAE,CAACS,KAAK,CAAC,gBAAgB,CAAC;IAC1D,CAAC,CAAC;IAEFjB,EAAE,CAAC,sBAAsB,EAAE,YAAY;MACrC;MACAP,OAAO,CAACG,IAAI,CAACT,iBAAiB,EAAE,kBAAkB,CAAC,CAACsD,MAAM,CAAC,IAAI5B,KAAK,CAAC,YAAY,CAAC,CAAC;;MAEnF;MACA,MAAMK,QAAQ,GAAG,MAAM1B,OAAO,CAAC2B,GAAG,CAAC,aAAa,CAAC;;MAEjD;MACArC,MAAM,CAACoC,QAAQ,CAACE,MAAM,CAAC,CAACZ,EAAE,CAACS,KAAK,CAAC,GAAG,CAAC;MACrCnC,MAAM,CAACoC,QAAQ,CAACG,IAAI,CAAC,CAACb,EAAE,CAAC6B,IAAI,CAACK,QAAQ,CAAC,OAAO,CAAC;MAC/C5D,MAAM,CAACoC,QAAQ,CAACG,IAAI,CAACN,KAAK,CAAC,CAACP,EAAE,CAACS,KAAK,CAAC,YAAY,CAAC;IACpD,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}