/**
 * Team Service
 * 
 * This service handles team management for collaboration features.
 */

const fs = require('fs').promises;
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const { ValidationError, NotFoundError, AuthorizationError } = require('../utils/errors');

class TeamService {
  constructor(dataDir = path.join(__dirname, '../data')) {
    this.dataDir = dataDir;
    this.teamsFile = path.join(this.dataDir, 'teams.json');
    this.teamMembersFile = path.join(this.dataDir, 'team_members.json');
    this.teamInvitationsFile = path.join(this.dataDir, 'team_invitations.json');
    this.ensureDataDir();
  }

  /**
   * Ensure the data directory exists
   */
  async ensureDataDir() {
    try {
      await fs.mkdir(this.dataDir, { recursive: true });
      
      // Initialize files if they don't exist
      await this.initializeFile(this.teamsFile, []);
      await this.initializeFile(this.teamMembersFile, []);
      await this.initializeFile(this.teamInvitationsFile, []);
    } catch (error) {
      console.error('Error creating data directory:', error);
      throw error;
    }
  }

  /**
   * Initialize a file with default data if it doesn't exist
   */
  async initializeFile(filePath, defaultData) {
    try {
      await fs.access(filePath);
    } catch (error) {
      if (error.code === 'ENOENT') {
        // File doesn't exist, create it with default data
        await fs.writeFile(filePath, JSON.stringify(defaultData, null, 2));
      } else {
        throw error;
      }
    }
  }

  /**
   * Load data from file
   */
  async loadData(filePath) {
    try {
      const data = await fs.readFile(filePath, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      if (error.code === 'ENOENT') {
        // File doesn't exist, return empty array
        return [];
      }
      console.error(`Error loading data from ${filePath}:`, error);
      throw error;
    }
  }

  /**
   * Save data to file
   */
  async saveData(filePath, data) {
    try {
      await fs.writeFile(filePath, JSON.stringify(data, null, 2));
    } catch (error) {
      console.error(`Error saving data to ${filePath}:`, error);
      throw error;
    }
  }

  /**
   * Get all teams
   */
  async getAllTeams() {
    return this.loadData(this.teamsFile);
  }

  /**
   * Get team by ID
   */
  async getTeamById(id) {
    const teams = await this.loadData(this.teamsFile);
    const team = teams.find(t => t.id === id);
    
    if (!team) {
      throw new NotFoundError(`Team with ID ${id} not found`);
    }
    
    return team;
  }

  /**
   * Get teams for a user
   */
  async getTeamsForUser(userId) {
    const teamMembers = await this.loadData(this.teamMembersFile);
    const userTeamMemberships = teamMembers.filter(tm => tm.userId === userId);
    
    if (userTeamMemberships.length === 0) {
      return [];
    }
    
    const teams = await this.loadData(this.teamsFile);
    return teams.filter(team => 
      userTeamMemberships.some(membership => membership.teamId === team.id)
    );
  }

  /**
   * Create a new team
   */
  async createTeam(data, creatorId) {
    if (!data.name) {
      throw new ValidationError('Team name is required');
    }
    
    if (!creatorId) {
      throw new ValidationError('Creator ID is required');
    }
    
    const teams = await this.loadData(this.teamsFile);
    
    // Create new team
    const newTeam = {
      id: uuidv4(),
      name: data.name,
      description: data.description || '',
      createdBy: creatorId,
      created: new Date().toISOString(),
      updated: new Date().toISOString()
    };
    
    teams.push(newTeam);
    await this.saveData(this.teamsFile, teams);
    
    // Add creator as team owner
    await this.addTeamMember(newTeam.id, creatorId, 'owner');
    
    return newTeam;
  }

  /**
   * Update a team
   */
  async updateTeam(id, data, userId) {
    const teams = await this.loadData(this.teamsFile);
    const index = teams.findIndex(t => t.id === id);
    
    if (index === -1) {
      throw new NotFoundError(`Team with ID ${id} not found`);
    }
    
    // Check if user has permission to update team
    await this.checkTeamPermission(id, userId, ['owner', 'admin']);
    
    // Update team
    const updatedTeam = {
      ...teams[index],
      ...data,
      id, // Don't allow changing the ID
      updated: new Date().toISOString()
    };
    
    teams[index] = updatedTeam;
    await this.saveData(this.teamsFile, teams);
    
    return updatedTeam;
  }

  /**
   * Delete a team
   */
  async deleteTeam(id, userId) {
    const teams = await this.loadData(this.teamsFile);
    const index = teams.findIndex(t => t.id === id);
    
    if (index === -1) {
      throw new NotFoundError(`Team with ID ${id} not found`);
    }
    
    // Check if user has permission to delete team
    await this.checkTeamPermission(id, userId, ['owner']);
    
    // Remove the team
    teams.splice(index, 1);
    await this.saveData(this.teamsFile, teams);
    
    // Remove all team members
    const teamMembers = await this.loadData(this.teamMembersFile);
    const updatedTeamMembers = teamMembers.filter(tm => tm.teamId !== id);
    await this.saveData(this.teamMembersFile, updatedTeamMembers);
    
    // Remove all team invitations
    const teamInvitations = await this.loadData(this.teamInvitationsFile);
    const updatedTeamInvitations = teamInvitations.filter(ti => ti.teamId !== id);
    await this.saveData(this.teamInvitationsFile, updatedTeamInvitations);
    
    return { success: true, message: `Team ${id} deleted` };
  }

  /**
   * Get team members
   */
  async getTeamMembers(teamId, userId) {
    // Check if user has permission to view team members
    await this.checkTeamPermission(teamId, userId, ['owner', 'admin', 'member']);
    
    const teamMembers = await this.loadData(this.teamMembersFile);
    return teamMembers.filter(tm => tm.teamId === teamId);
  }

  /**
   * Get team member
   */
  async getTeamMember(teamId, memberId) {
    const teamMembers = await this.loadData(this.teamMembersFile);
    const teamMember = teamMembers.find(tm => tm.teamId === teamId && tm.userId === memberId);
    
    if (!teamMember) {
      throw new NotFoundError(`Team member with ID ${memberId} not found in team ${teamId}`);
    }
    
    return teamMember;
  }

  /**
   * Add team member
   */
  async addTeamMember(teamId, userId, role = 'member') {
    // Validate role
    const validRoles = ['owner', 'admin', 'member'];
    if (!validRoles.includes(role)) {
      throw new ValidationError(`Invalid role: ${role}. Must be one of: ${validRoles.join(', ')}`);
    }
    
    // Check if team exists
    await this.getTeamById(teamId);
    
    const teamMembers = await this.loadData(this.teamMembersFile);
    
    // Check if user is already a member
    const existingMember = teamMembers.find(tm => tm.teamId === teamId && tm.userId === userId);
    
    if (existingMember) {
      // Update role if different
      if (existingMember.role !== role) {
        existingMember.role = role;
        existingMember.updated = new Date().toISOString();
        await this.saveData(this.teamMembersFile, teamMembers);
      }
      
      return existingMember;
    }
    
    // Add new team member
    const newTeamMember = {
      id: uuidv4(),
      teamId,
      userId,
      role,
      created: new Date().toISOString(),
      updated: new Date().toISOString()
    };
    
    teamMembers.push(newTeamMember);
    await this.saveData(this.teamMembersFile, teamMembers);
    
    return newTeamMember;
  }

  /**
   * Update team member
   */
  async updateTeamMember(teamId, memberId, data, requesterId) {
    // Check if requester has permission to update team members
    await this.checkTeamPermission(teamId, requesterId, ['owner', 'admin']);
    
    // Validate role
    const validRoles = ['owner', 'admin', 'member'];
    if (data.role && !validRoles.includes(data.role)) {
      throw new ValidationError(`Invalid role: ${data.role}. Must be one of: ${validRoles.join(', ')}`);
    }
    
    const teamMembers = await this.loadData(this.teamMembersFile);
    const index = teamMembers.findIndex(tm => tm.teamId === teamId && tm.userId === memberId);
    
    if (index === -1) {
      throw new NotFoundError(`Team member with ID ${memberId} not found in team ${teamId}`);
    }
    
    // Check if trying to change the role of the last owner
    if (teamMembers[index].role === 'owner' && data.role && data.role !== 'owner') {
      const ownerCount = teamMembers.filter(tm => tm.teamId === teamId && tm.role === 'owner').length;
      
      if (ownerCount <= 1) {
        throw new ValidationError('Cannot change the role of the last team owner');
      }
    }
    
    // Update team member
    const updatedTeamMember = {
      ...teamMembers[index],
      ...data,
      teamId, // Don't allow changing the team ID
      userId: memberId, // Don't allow changing the user ID
      updated: new Date().toISOString()
    };
    
    teamMembers[index] = updatedTeamMember;
    await this.saveData(this.teamMembersFile, teamMembers);
    
    return updatedTeamMember;
  }

  /**
   * Remove team member
   */
  async removeTeamMember(teamId, memberId, requesterId) {
    // Check if requester has permission to remove team members
    const requesterMembership = await this.getUserTeamMembership(teamId, requesterId);
    
    // Allow users to remove themselves
    if (memberId !== requesterId) {
      // For removing others, requester must be owner or admin
      if (!['owner', 'admin'].includes(requesterMembership.role)) {
        throw new AuthorizationError('You do not have permission to remove team members');
      }
      
      // Check if trying to remove an owner
      const memberToRemove = await this.getUserTeamMembership(teamId, memberId);
      
      if (memberToRemove.role === 'owner') {
        // Only owners can remove other owners
        if (requesterMembership.role !== 'owner') {
          throw new AuthorizationError('Only team owners can remove other team owners');
        }
        
        // Check if this is the last owner
        const teamMembers = await this.loadData(this.teamMembersFile);
        const ownerCount = teamMembers.filter(tm => tm.teamId === teamId && tm.role === 'owner').length;
        
        if (ownerCount <= 1) {
          throw new ValidationError('Cannot remove the last team owner');
        }
      }
    }
    
    const teamMembers = await this.loadData(this.teamMembersFile);
    const index = teamMembers.findIndex(tm => tm.teamId === teamId && tm.userId === memberId);
    
    if (index === -1) {
      throw new NotFoundError(`Team member with ID ${memberId} not found in team ${teamId}`);
    }
    
    // Remove the team member
    teamMembers.splice(index, 1);
    await this.saveData(this.teamMembersFile, teamMembers);
    
    return { success: true, message: `User ${memberId} removed from team ${teamId}` };
  }

  /**
   * Get user's team membership
   */
  async getUserTeamMembership(teamId, userId) {
    const teamMembers = await this.loadData(this.teamMembersFile);
    const teamMember = teamMembers.find(tm => tm.teamId === teamId && tm.userId === userId);
    
    if (!teamMember) {
      throw new NotFoundError(`User ${userId} is not a member of team ${teamId}`);
    }
    
    return teamMember;
  }

  /**
   * Check if user has permission for a team action
   */
  async checkTeamPermission(teamId, userId, allowedRoles) {
    try {
      const membership = await this.getUserTeamMembership(teamId, userId);
      
      if (!allowedRoles.includes(membership.role)) {
        throw new AuthorizationError(`User does not have required role. Required: ${allowedRoles.join(' or ')}, Current: ${membership.role}`);
      }
      
      return true;
    } catch (error) {
      if (error instanceof NotFoundError) {
        throw new AuthorizationError('User is not a member of this team');
      }
      throw error;
    }
  }

  /**
   * Create team invitation
   */
  async createTeamInvitation(teamId, email, role, inviterId) {
    // Check if inviter has permission to invite team members
    await this.checkTeamPermission(teamId, inviterId, ['owner', 'admin']);
    
    // Validate role
    const validRoles = ['admin', 'member'];
    if (!validRoles.includes(role)) {
      throw new ValidationError(`Invalid role: ${role}. Must be one of: ${validRoles.join(', ')}`);
    }
    
    const teamInvitations = await this.loadData(this.teamInvitationsFile);
    
    // Check if invitation already exists
    const existingInvitation = teamInvitations.find(ti => 
      ti.teamId === teamId && 
      ti.email.toLowerCase() === email.toLowerCase() &&
      ti.status === 'pending'
    );
    
    if (existingInvitation) {
      throw new ValidationError(`An invitation for ${email} is already pending`);
    }
    
    // Create new invitation
    const newInvitation = {
      id: uuidv4(),
      teamId,
      email: email.toLowerCase(),
      role,
      inviterId,
      status: 'pending',
      created: new Date().toISOString(),
      updated: new Date().toISOString(),
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // 7 days
    };
    
    teamInvitations.push(newInvitation);
    await this.saveData(this.teamInvitationsFile, teamInvitations);
    
    return newInvitation;
  }

  /**
   * Get team invitations
   */
  async getTeamInvitations(teamId, userId) {
    // Check if user has permission to view team invitations
    await this.checkTeamPermission(teamId, userId, ['owner', 'admin']);
    
    const teamInvitations = await this.loadData(this.teamInvitationsFile);
    return teamInvitations.filter(ti => ti.teamId === teamId);
  }

  /**
   * Get invitations for email
   */
  async getInvitationsForEmail(email) {
    const teamInvitations = await this.loadData(this.teamInvitationsFile);
    return teamInvitations.filter(ti => 
      ti.email.toLowerCase() === email.toLowerCase() && 
      ti.status === 'pending' &&
      new Date(ti.expiresAt) > new Date()
    );
  }

  /**
   * Accept team invitation
   */
  async acceptTeamInvitation(invitationId, userId) {
    const teamInvitations = await this.loadData(this.teamInvitationsFile);
    const index = teamInvitations.findIndex(ti => ti.id === invitationId);
    
    if (index === -1) {
      throw new NotFoundError(`Invitation with ID ${invitationId} not found`);
    }
    
    const invitation = teamInvitations[index];
    
    // Check if invitation is still valid
    if (invitation.status !== 'pending') {
      throw new ValidationError(`Invitation is ${invitation.status}`);
    }
    
    if (new Date(invitation.expiresAt) <= new Date()) {
      throw new ValidationError('Invitation has expired');
    }
    
    // Update invitation status
    invitation.status = 'accepted';
    invitation.acceptedBy = userId;
    invitation.acceptedAt = new Date().toISOString();
    invitation.updated = new Date().toISOString();
    
    await this.saveData(this.teamInvitationsFile, teamInvitations);
    
    // Add user to team
    await this.addTeamMember(invitation.teamId, userId, invitation.role);
    
    return invitation;
  }

  /**
   * Decline team invitation
   */
  async declineTeamInvitation(invitationId, userId) {
    const teamInvitations = await this.loadData(this.teamInvitationsFile);
    const index = teamInvitations.findIndex(ti => ti.id === invitationId);
    
    if (index === -1) {
      throw new NotFoundError(`Invitation with ID ${invitationId} not found`);
    }
    
    const invitation = teamInvitations[index];
    
    // Check if invitation is still valid
    if (invitation.status !== 'pending') {
      throw new ValidationError(`Invitation is already ${invitation.status}`);
    }
    
    // Update invitation status
    invitation.status = 'declined';
    invitation.declinedBy = userId;
    invitation.declinedAt = new Date().toISOString();
    invitation.updated = new Date().toISOString();
    
    await this.saveData(this.teamInvitationsFile, teamInvitations);
    
    return invitation;
  }

  /**
   * Cancel team invitation
   */
  async cancelTeamInvitation(invitationId, userId) {
    const teamInvitations = await this.loadData(this.teamInvitationsFile);
    const index = teamInvitations.findIndex(ti => ti.id === invitationId);
    
    if (index === -1) {
      throw new NotFoundError(`Invitation with ID ${invitationId} not found`);
    }
    
    const invitation = teamInvitations[index];
    
    // Check if user has permission to cancel invitation
    await this.checkTeamPermission(invitation.teamId, userId, ['owner', 'admin']);
    
    // Check if invitation is still valid
    if (invitation.status !== 'pending') {
      throw new ValidationError(`Invitation is already ${invitation.status}`);
    }
    
    // Update invitation status
    invitation.status = 'cancelled';
    invitation.cancelledBy = userId;
    invitation.cancelledAt = new Date().toISOString();
    invitation.updated = new Date().toISOString();
    
    await this.saveData(this.teamInvitationsFile, teamInvitations);
    
    return invitation;
  }

  /**
   * Get user's role in a team
   */
  async getUserRoleInTeam(teamId, userId) {
    try {
      const membership = await this.getUserTeamMembership(teamId, userId);
      return membership.role;
    } catch (error) {
      if (error instanceof NotFoundError) {
        return null;
      }
      throw error;
    }
  }

  /**
   * Check if user is a member of a team
   */
  async isUserTeamMember(teamId, userId) {
    try {
      await this.getUserTeamMembership(teamId, userId);
      return true;
    } catch (error) {
      if (error instanceof NotFoundError) {
        return false;
      }
      throw error;
    }
  }
}

module.exports = TeamService;
