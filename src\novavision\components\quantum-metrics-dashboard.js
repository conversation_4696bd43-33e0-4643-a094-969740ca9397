/**
 * Quantum Metrics Dashboard
 *
 * Provides visualization components for Quantum State Inference metrics,
 * including entropy trends, certainty rates, security events, and action distributions.
 * Can be used internally or exported to monitoring tools like Grafana.
 */

// Create a simple logger
const logger = {
  info: (message, data) => console.log(`[INFO] [quantum-metrics-dashboard] ${message}`, data || ''),
  debug: (message, data) => console.log(`[DEBUG] [quantum-metrics-dashboard] ${message}`, data || ''),
  error: (message, data) => console.error(`[ERROR] [quantum-metrics-dashboard] ${message}`, data || '')
};

class QuantumMetricsDashboard {
  /**
   * Create a new Quantum Metrics Dashboard
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    this.options = {
      theme: options.theme || 'cyber-safety',
      colorScheme: options.colorScheme || 'quantum',
      refreshInterval: options.refreshInterval || 60000, // 1 minute
      maxHistoryPoints: options.maxHistoryPoints || 100,
      enableRealTimeUpdates: options.enableRealTimeUpdates !== false,
      exportFormat: options.exportFormat || 'json',
      enablePerformanceOptimization: options.enablePerformanceOptimization !== false,
      samplingRate: options.samplingRate || 0.18, // Use 18% sampling for large datasets
      lazyLoading: options.lazyLoading !== false, // Enable lazy loading by default
      ...options
    };

    // Initialize metrics history
    this.metricsHistory = [];
    this.securityEvents = [];
    this.entropyTrends = [];
    this.actionDistribution = {
      enhance_detection: { count: 0, byPriority: { low: 0, medium: 0, high: 0, critical: 0 } },
      mitigate_threat: { count: 0, byPriority: { low: 0, medium: 0, high: 0, critical: 0 } },
      escalate: { count: 0, byPriority: { low: 0, medium: 0, high: 0, critical: 0 } },
      adjust_baseline: { count: 0, byPriority: { low: 0, medium: 0, high: 0, critical: 0 } },
      investigate: { count: 0, byPriority: { low: 0, medium: 0, high: 0, critical: 0 } }
    };

    // Initialize refresh timer if enabled
    if (this.options.enableRealTimeUpdates) {
      this._startRefreshTimer();
    }

    logger.info('Quantum Metrics Dashboard initialized', {
      theme: this.options.theme,
      refreshInterval: this.options.refreshInterval
    });
  }

  /**
   * Update metrics with latest data
   * @param {Object} quantumMetrics - Quantum inference metrics
   * @param {Object} securityMetrics - Security metrics
   */
  updateMetrics(quantumMetrics, securityMetrics = {}) {
    // Add timestamp
    const timestamp = new Date().toISOString();

    // Update metrics history
    this.metricsHistory.push({
      timestamp,
      inferenceCount: quantumMetrics.inferenceCount || 0,
      averageInferenceTime: quantumMetrics.averageInferenceTime || 0,
      collapseEvents: quantumMetrics.collapseEvents || 0,
      superpositionEvents: quantumMetrics.superpositionEvents || 0,
      certaintyRate: quantumMetrics.certaintyRate || 0,
      falsePositives: quantumMetrics.falsePositives || 0,
      falseNegatives: quantumMetrics.falseNegatives || 0
    });

    // Update entropy trends
    if (quantumMetrics.entropyTrends && quantumMetrics.entropyTrends.length > 0) {
      const latestTrend = quantumMetrics.entropyTrends[quantumMetrics.entropyTrends.length - 1];
      this.entropyTrends.push({
        timestamp,
        averageEntropy: latestTrend.averageEntropy,
        distribution: latestTrend.entropyDistribution,
        collapseRatio: latestTrend.collapseByEntropy
      });
    }

    // Update security events if provided
    if (securityMetrics.events) {
      this.securityEvents.push(...securityMetrics.events.map(event => ({
        ...event,
        processedAt: timestamp
      })));
    }

    // Update action distribution if provided
    if (securityMetrics.actionDistribution) {
      Object.keys(securityMetrics.actionDistribution).forEach(actionType => {
        if (this.actionDistribution[actionType]) {
          this.actionDistribution[actionType].count += securityMetrics.actionDistribution[actionType].count || 0;

          // Update by priority
          Object.keys(securityMetrics.actionDistribution[actionType].byPriority || {}).forEach(priority => {
            this.actionDistribution[actionType].byPriority[priority] +=
              securityMetrics.actionDistribution[actionType].byPriority[priority] || 0;
          });
        }
      });
    }

    // Limit history size
    this._limitHistorySize();

    logger.debug('Metrics updated', {
      historySize: this.metricsHistory.length,
      entropyTrendsSize: this.entropyTrends.length,
      securityEventsSize: this.securityEvents.length
    });
  }

  /**
   * Generate dashboard schema for visualization
   * @returns {Object} - Dashboard schema
   */
  generateDashboard() {
    logger.info('Generating quantum metrics dashboard');

    try {
      // Create base dashboard schema
      const schema = {
        id: 'quantum-metrics-dashboard',
        type: 'dashboard',
        title: 'Quantum State Inference Metrics',
        description: 'Visualization of quantum inference metrics and security events',
        theme: this.options.theme,
        colorScheme: this.options.colorScheme,
        refreshInterval: this.options.enableRealTimeUpdates ? this.options.refreshInterval : null,
        timestamp: new Date().toISOString(),
        layout: {
          type: 'divine-proportion',  // Using the golden ratio (φ) for layout
          gridRatio: 1.618,           // φ value for grid proportions
          spacing: 18,                // Base spacing using our 18/82 principle
          responsive: true,
          adaptiveHeight: true
        },
        sections: []
      };

      // Add overview section
      schema.sections.push(this._createOverviewSection());

      // Add entropy trends section
      schema.sections.push(this._createEntropyTrendsSection());

      // Add certainty vs entropy section
      schema.sections.push(this._createCertaintySection());

      // Add security events section
      schema.sections.push(this._createSecurityEventsSection());

      // Add action distribution section
      schema.sections.push(this._createActionDistributionSection());

      return schema;
    } catch (error) {
      logger.error('Error generating quantum metrics dashboard', {
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Export metrics data in specified format
   * @param {string} format - Export format (json, csv, prometheus)
   * @returns {string} - Exported data
   */
  exportMetrics(format = this.options.exportFormat) {
    switch (format.toLowerCase()) {
      case 'json':
        return this._exportAsJSON();
      case 'csv':
        return this._exportAsCSV();
      case 'prometheus':
        return this._exportAsPrometheus();
      default:
        return this._exportAsJSON();
    }
  }

  /**
   * Create overview section
   * @returns {Object} - Overview section schema
   * @private
   */
  _createOverviewSection() {
    // Get latest metrics
    const latestMetrics = this.metricsHistory.length > 0
      ? this.metricsHistory[this.metricsHistory.length - 1]
      : null;

    return {
      id: 'overview-section',
      title: 'Quantum Inference Overview',
      description: 'Key metrics for quantum state inference performance',
      components: [
        {
          id: 'metrics-summary',
          type: 'metrics-panel',
          title: 'Key Metrics',
          metrics: [
            {
              id: 'inference-count',
              label: 'Total Inferences',
              value: latestMetrics ? latestMetrics.inferenceCount : 0,
              trend: this._calculateTrend('inferenceCount'),
              icon: 'quantum-processor'
            },
            {
              id: 'certainty-rate',
              label: 'Certainty Rate',
              value: latestMetrics ? `${(latestMetrics.certaintyRate * 100).toFixed(2)}%` : '0%',
              trend: this._calculateTrend('certaintyRate'),
              icon: 'certainty'
            },
            {
              id: 'avg-inference-time',
              label: 'Avg. Inference Time',
              value: latestMetrics ? `${latestMetrics.averageInferenceTime.toFixed(2)} ms` : '0 ms',
              trend: this._calculateTrend('averageInferenceTime', true), // Lower is better
              icon: 'performance'
            },
            {
              id: 'false-positives',
              label: 'False Positives',
              value: latestMetrics ? latestMetrics.falsePositives : 0,
              trend: this._calculateTrend('falsePositives', true), // Lower is better
              icon: 'warning'
            }
          ]
        },
        {
          id: 'inference-trend-chart',
          type: 'line-chart',
          title: 'Inference Metrics Over Time',
          data: this._prepareTimeSeriesData(['inferenceCount', 'collapseEvents', 'certaintyRate']),
          xAxis: { label: 'Time', dataKey: 'timestamp' },
          yAxis: { label: 'Value', dataKey: 'value' },
          series: [
            { dataKey: 'inferenceCount', name: 'Inferences', color: '#3498db' },
            { dataKey: 'collapseEvents', name: 'Collapses', color: '#2ecc71' },
            { dataKey: 'certaintyRate', name: 'Certainty Rate', color: '#f39c12', yAxisId: 'rate' }
          ]
        }
      ]
    };
  }

  /**
   * Create entropy trends section
   * @returns {Object} - Entropy trends section schema
   * @private
   */
  _createEntropyTrendsSection() {
    return {
      id: 'entropy-trends-section',
      title: 'Entropy Trends',
      description: 'Visualization of entropy distribution and trends over time',
      components: [
        {
          id: 'entropy-trend-chart',
          type: 'line-chart',
          title: 'Average Entropy Over Time',
          data: this.entropyTrends.map(trend => ({
            timestamp: trend.timestamp,
            averageEntropy: trend.averageEntropy
          })),
          xAxis: { label: 'Time', dataKey: 'timestamp' },
          yAxis: { label: 'Entropy', dataKey: 'averageEntropy', domain: [0, 1] },
          series: [
            { dataKey: 'averageEntropy', name: 'Average Entropy', color: '#9b59b6' }
          ]
        },
        {
          id: 'entropy-distribution-chart',
          type: 'stacked-area-chart',
          title: 'Entropy Distribution Over Time',
          data: this.entropyTrends.map(trend => ({
            timestamp: trend.timestamp,
            low: trend.distribution?.low || 0,
            medium: trend.distribution?.medium || 0,
            high: trend.distribution?.high || 0
          })),
          xAxis: { label: 'Time', dataKey: 'timestamp' },
          yAxis: { label: 'Count', dataKey: 'value' },
          series: [
            { dataKey: 'low', name: 'Low Entropy', color: '#2ecc71' },
            { dataKey: 'medium', name: 'Medium Entropy', color: '#f39c12' },
            { dataKey: 'high', name: 'High Entropy', color: '#e74c3c' }
          ]
        }
      ]
    };
  }

  /**
   * Create certainty section
   * @returns {Object} - Certainty section schema
   * @private
   */
  _createCertaintySection() {
    // Prepare data for scatter plot
    const scatterData = this.entropyTrends.map((trend, index) => {
      const metrics = index < this.metricsHistory.length ? this.metricsHistory[index] : null;
      return {
        timestamp: trend.timestamp,
        entropy: trend.averageEntropy,
        certainty: metrics ? metrics.certaintyRate : 0
      };
    });

    return {
      id: 'certainty-section',
      title: 'Certainty vs. Entropy Analysis',
      description: 'Relationship between certainty rate and entropy levels',
      components: [
        {
          id: 'certainty-entropy-scatter',
          type: 'scatter-plot',
          title: 'Certainty vs. Entropy',
          data: scatterData,
          xAxis: { label: 'Average Entropy', dataKey: 'entropy', domain: [0, 1] },
          yAxis: { label: 'Certainty Rate', dataKey: 'certainty', domain: [0, 1] },
          series: [
            { dataKey: 'certainty', name: 'Data Points', color: '#3498db' }
          ]
        },
        {
          id: 'certainty-trend-chart',
          type: 'line-chart',
          title: 'Certainty Rate Over Time',
          data: this.metricsHistory.map(metrics => ({
            timestamp: metrics.timestamp,
            certaintyRate: metrics.certaintyRate
          })),
          xAxis: { label: 'Time', dataKey: 'timestamp' },
          yAxis: { label: 'Certainty Rate', dataKey: 'certaintyRate', domain: [0, 1] },
          series: [
            { dataKey: 'certaintyRate', name: 'Certainty Rate', color: '#f39c12' }
          ]
        }
      ]
    };
  }

  /**
   * Create security events section
   * @returns {Object} - Security events section schema
   * @private
   */
  _createSecurityEventsSection() {
    // Group security events by role
    const roleBasedEvents = {};
    this.securityEvents.forEach(event => {
      const role = event.role || 'unknown';
      roleBasedEvents[role] = roleBasedEvents[role] || [];
      roleBasedEvents[role].push(event);
    });

    // Count events by role and status
    const roleEventCounts = Object.keys(roleBasedEvents).map(role => {
      const events = roleBasedEvents[role];
      const allowed = events.filter(e => e.allowed).length;
      const denied = events.filter(e => !e.allowed).length;
      return { role, allowed, denied, total: events.length };
    });

    return {
      id: 'security-events-section',
      title: 'Security Events',
      description: 'Role-based access events and security metrics',
      components: [
        {
          id: 'role-based-access-chart',
          type: 'bar-chart',
          title: 'Role-Based Access Events',
          data: roleEventCounts,
          xAxis: { label: 'Role', dataKey: 'role' },
          yAxis: { label: 'Count', dataKey: 'value' },
          series: [
            { dataKey: 'allowed', name: 'Allowed', color: '#2ecc71' },
            { dataKey: 'denied', name: 'Denied', color: '#e74c3c' }
          ]
        },
        {
          id: 'security-events-table',
          type: 'data-table',
          title: 'Recent Security Events',
          data: this.securityEvents.slice(-10).reverse(),
          columns: [
            { field: 'timestamp', header: 'Time', width: '20%' },
            { field: 'userId', header: 'User', width: '15%' },
            { field: 'role', header: 'Role', width: '15%' },
            { field: 'operation', header: 'Operation', width: '20%' },
            { field: 'allowed', header: 'Status', width: '15%', formatter: 'boolean' },
            { field: 'reason', header: 'Reason', width: '15%' }
          ]
        }
      ]
    };
  }

  /**
   * Create action distribution section
   * @returns {Object} - Action distribution section schema
   * @private
   */
  _createActionDistributionSection() {
    // Prepare data for action distribution
    const actionData = Object.keys(this.actionDistribution).map(actionType => {
      const data = this.actionDistribution[actionType];
      return {
        actionType,
        count: data.count,
        low: data.byPriority.low,
        medium: data.byPriority.medium,
        high: data.byPriority.high,
        critical: data.byPriority.critical
      };
    });

    // Calculate 18/82 split for action prioritization
    const allActions = [];
    Object.keys(this.actionDistribution).forEach(actionType => {
      const data = this.actionDistribution[actionType];

      // Add individual actions with their priorities
      Object.keys(data.byPriority).forEach(priority => {
        for (let i = 0; i < data.byPriority[priority]; i++) {
          allActions.push({
            type: actionType,
            priority: priority,
            // Assign numeric value for sorting
            value: priority === 'critical' ? 4 :
                   priority === 'high' ? 3 :
                   priority === 'medium' ? 2 : 1
          });
        }
      });
    });

    // Sort actions by priority (descending)
    allActions.sort((a, b) => b.value - a.value);

    // Calculate 18/82 split
    const totalActions = allActions.length;
    const criticalCount = Math.ceil(totalActions * 0.18); // Top 18% are critical
    const standardCount = totalActions - criticalCount;   // Remaining 82% are standard

    // Prepare 18/82 data for visualization
    const priorityData = [
      { name: 'Critical (18%)', value: criticalCount, color: '#e74c3c' },
      { name: 'Standard (82%)', value: standardCount, color: '#3498db' }
    ];

    return {
      id: 'action-distribution-section',
      title: 'Action Severity Distribution',
      description: 'Distribution of actions by type and severity with 18/82 principle',
      components: [
        {
          id: 'action-severity-chart',
          type: 'stacked-bar-chart',
          title: 'Action Severity Distribution',
          data: actionData,
          xAxis: { label: 'Action Type', dataKey: 'actionType' },
          yAxis: { label: 'Count', dataKey: 'value' },
          series: [
            { dataKey: 'low', name: 'Low', color: '#3498db' },
            { dataKey: 'medium', name: 'Medium', color: '#f39c12' },
            { dataKey: 'high', name: 'High', color: '#e74c3c' },
            { dataKey: 'critical', name: 'Critical', color: '#8e44ad' }
          ]
        },
        {
          id: '18-82-principle-chart',
          type: 'pie-chart',
          title: '18/82 Action Prioritization',
          description: 'Top 18% of actions require immediate attention',
          data: priorityData,
          dataKey: 'value',
          nameKey: 'name',
          colors: ['#e74c3c', '#3498db'],
          innerRadius: '60%',
          outerRadius: '80%',
          legend: { position: 'right' },
          label: {
            position: 'inside',
            content: '{percent}%',
            style: { fill: '#ffffff', fontSize: 14, fontWeight: 'bold' }
          },
          annotations: [
            {
              type: 'text',
              position: 'center',
              content: '18/82',
              style: { fontSize: 24, fontWeight: 'bold', fill: '#2c3e50' }
            }
          ]
        },
        {
          id: 'action-distribution-pie',
          type: 'pie-chart',
          title: 'Action Type Distribution',
          data: actionData.map(item => ({
            name: item.actionType,
            value: item.count
          })),
          dataKey: 'value',
          nameKey: 'name',
          colorScheme: 'categorical'
        }
      ]
    };
  }

  /**
   * Start refresh timer
   * @private
   */
  _startRefreshTimer() {
    this.refreshTimer = setInterval(() => {
      // This would typically fetch the latest metrics from the quantum inference engine
      // For now, we'll just log that a refresh would occur
      logger.debug('Refresh timer triggered - would fetch latest metrics');
    }, this.options.refreshInterval);
  }

  /**
   * Stop refresh timer
   */
  stopRefreshTimer() {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
      this.refreshTimer = null;
    }
  }

  /**
   * Limit history size to prevent memory issues
   * @private
   */
  _limitHistorySize() {
    if (this.metricsHistory.length > this.options.maxHistoryPoints) {
      this.metricsHistory = this.metricsHistory.slice(-this.options.maxHistoryPoints);
    }

    if (this.entropyTrends.length > this.options.maxHistoryPoints) {
      this.entropyTrends = this.entropyTrends.slice(-this.options.maxHistoryPoints);
    }

    if (this.securityEvents.length > this.options.maxHistoryPoints * 2) {
      this.securityEvents = this.securityEvents.slice(-this.options.maxHistoryPoints * 2);
    }
  }

  /**
   * Calculate trend for a metric
   * @param {string} metricKey - Metric key
   * @param {boolean} [lowerIsBetter=false] - Whether lower values are better
   * @returns {string} - Trend direction ('up', 'down', 'stable')
   * @private
   */
  _calculateTrend(metricKey, lowerIsBetter = false) {
    if (this.metricsHistory.length < 2) {
      return 'stable';
    }

    const current = this.metricsHistory[this.metricsHistory.length - 1][metricKey];
    const previous = this.metricsHistory[this.metricsHistory.length - 2][metricKey];

    if (current === previous) {
      return 'stable';
    }

    const isUp = current > previous;

    if (lowerIsBetter) {
      return isUp ? 'down' : 'up'; // Invert trend if lower is better
    }

    return isUp ? 'up' : 'down';
  }

  /**
   * Prepare time series data for charts
   * @param {Array} keys - Metric keys to include
   * @returns {Array} - Prepared time series data
   * @private
   */
  _prepareTimeSeriesData(keys) {
    // Get metrics history
    let metricsData = [...this.metricsHistory];

    // Apply performance optimization if enabled and dataset is large
    if (this.options.enablePerformanceOptimization && metricsData.length > 100) {
      metricsData = this._optimizeDataset(metricsData);
    }

    // Map data to time series format
    return metricsData.map(metrics => {
      const result = { timestamp: metrics.timestamp };
      keys.forEach(key => {
        result[key] = metrics[key];
      });
      return result;
    });
  }

  /**
   * Optimize dataset for performance using 18/82 principle
   * @param {Array} dataset - Dataset to optimize
   * @returns {Array} - Optimized dataset
   * @private
   */
  _optimizeDataset(dataset) {
    // If dataset is small, return as is
    if (dataset.length <= 100) {
      return dataset;
    }

    // Sort by timestamp (newest first)
    const sortedData = [...dataset].sort((a, b) =>
      new Date(b.timestamp) - new Date(a.timestamp)
    );

    // Apply 18/82 principle:
    // - Keep 18% of newest data at full resolution
    // - Sample 82% of older data at lower resolution

    // Calculate critical data count (newest 18%)
    const criticalCount = Math.ceil(sortedData.length * 0.18);
    const criticalData = sortedData.slice(0, criticalCount);

    // Get remaining data (oldest 82%)
    const remainingData = sortedData.slice(criticalCount);

    // Sample remaining data
    const sampledData = this._sampleData(remainingData);

    // Combine and sort by timestamp (oldest first)
    return [...sampledData, ...criticalData].sort((a, b) =>
      new Date(a.timestamp) - new Date(b.timestamp)
    );
  }

  /**
   * Sample data at lower resolution
   * @param {Array} data - Data to sample
   * @returns {Array} - Sampled data
   * @private
   */
  _sampleData(data) {
    // If data is small, return as is
    if (data.length <= 50) {
      return data;
    }

    // Calculate sampling interval based on sampling rate
    const interval = Math.max(1, Math.floor(1 / this.options.samplingRate));

    // Sample data at calculated interval
    const sampledData = [];
    for (let i = 0; i < data.length; i += interval) {
      sampledData.push(data[i]);
    }

    return sampledData;
  }

  /**
   * Export metrics as JSON
   * @returns {string} - JSON string
   * @private
   */
  _exportAsJSON() {
    return JSON.stringify({
      timestamp: new Date().toISOString(),
      metrics: this.metricsHistory,
      entropyTrends: this.entropyTrends,
      securityEvents: this.securityEvents,
      actionDistribution: this.actionDistribution
    }, null, 2);
  }

  /**
   * Export metrics as CSV
   * @returns {string} - CSV string
   * @private
   */
  _exportAsCSV() {
    // Implementation would convert metrics to CSV format
    // Simplified version for now
    const headers = ['timestamp', 'inferenceCount', 'averageInferenceTime', 'certaintyRate'];
    const rows = this.metricsHistory.map(metrics =>
      headers.map(header => metrics[header]).join(',')
    );

    return [headers.join(','), ...rows].join('\n');
  }

  /**
   * Export metrics in Prometheus format
   * @returns {string} - Prometheus metrics
   * @private
   */
  _exportAsPrometheus() {
    // Implementation would convert metrics to Prometheus format
    // Simplified version for now
    const latest = this.metricsHistory.length > 0
      ? this.metricsHistory[this.metricsHistory.length - 1]
      : null;

    if (!latest) {
      return '';
    }

    return [
      '# HELP quantum_inference_count Total number of quantum inferences',
      '# TYPE quantum_inference_count counter',
      `quantum_inference_count ${latest.inferenceCount}`,
      '# HELP quantum_inference_time_ms Average inference time in milliseconds',
      '# TYPE quantum_inference_time_ms gauge',
      `quantum_inference_time_ms ${latest.averageInferenceTime}`,
      '# HELP quantum_certainty_rate Certainty rate of quantum inferences',
      '# TYPE quantum_certainty_rate gauge',
      `quantum_certainty_rate ${latest.certaintyRate}`
    ].join('\n');
  }
}

module.exports = QuantumMetricsDashboard;
