/**
 * Role Permission Routes
 */

const express = require('express');
const router = express.Router();
const RolePermissionController = require('../controllers/RolePermissionController');
const { authenticate } = require('../middleware/authMiddleware');
const rbacMiddleware = require('../middleware/rbacMiddleware');

// All routes require authentication
router.use(authenticate);

// Role routes

// Get all roles
router.get('/roles', rbacMiddleware.hasPermission('role:read'), (req, res, next) => {
  RolePermissionController.getAllRoles(req, res, next);
});

// Get role by ID
router.get('/roles/:id', rbacMiddleware.hasPermission('role:read'), (req, res, next) => {
  RolePermissionController.getRoleById(req, res, next);
});

// Create a new role (admin only)
router.post('/roles', rbacMiddleware.hasPermission('role:create'), (req, res, next) => {
  RolePermissionController.createRole(req, res, next);
});

// Update a role (admin only)
router.put('/roles/:id', rbacMiddleware.hasPermission('role:update'), (req, res, next) => {
  RolePermissionController.updateRole(req, res, next);
});

// Delete a role (admin only)
router.delete('/roles/:id', rbacMiddleware.hasPermission('role:delete'), (req, res, next) => {
  RolePermissionController.deleteRole(req, res, next);
});

// Permission routes

// Get all permissions
router.get('/permissions', rbacMiddleware.hasPermission('role:read'), (req, res, next) => {
  RolePermissionController.getAllPermissions(req, res, next);
});

// Get permissions for a role
router.get('/roles/:id/permissions', rbacMiddleware.hasPermission('role:read'), (req, res, next) => {
  RolePermissionController.getPermissionsForRole(req, res, next);
});

// Add permission to role (admin only)
router.post('/roles/:id/permissions', rbacMiddleware.hasPermission('role:update'), (req, res, next) => {
  RolePermissionController.addPermissionToRole(req, res, next);
});

// Remove permission from role (admin only)
router.delete('/roles/:id/permissions/:permissionId', rbacMiddleware.hasPermission('role:update'), (req, res, next) => {
  RolePermissionController.removePermissionFromRole(req, res, next);
});

// User role routes

// Get roles for a user (admin only)
router.get('/users/:id/roles', rbacMiddleware.hasPermission('user:read'), (req, res, next) => {
  RolePermissionController.getRolesForUser(req, res, next);
});

// Get my roles
router.get('/users/me/roles', (req, res, next) => {
  RolePermissionController.getMyRoles(req, res, next);
});

// Assign role to user (admin only)
router.post('/users/:id/roles', rbacMiddleware.hasPermission('role:assign'), (req, res, next) => {
  RolePermissionController.assignRoleToUser(req, res, next);
});

// Remove role from user (admin only)
router.delete('/users/:id/roles/:roleId', rbacMiddleware.hasPermission('role:assign'), (req, res, next) => {
  RolePermissionController.removeRoleFromUser(req, res, next);
});

// Get permissions for a user (admin only)
router.get('/users/:id/permissions', rbacMiddleware.hasPermission('user:read'), (req, res, next) => {
  RolePermissionController.getPermissionsForUser(req, res, next);
});

// Get my permissions
router.get('/users/me/permissions', (req, res, next) => {
  RolePermissionController.getMyPermissions(req, res, next);
});

module.exports = router;
