/**
 * NovaStore Component Service
 * 
 * This service manages components in the NovaStore marketplace,
 * including verification using the Trinity CSDE integration.
 */

const NovaStoreComponent = require('../models/component');
const NovaStoreTrinityIntegration = require('../trinity_csde_integration');

class ComponentService {
  /**
   * Create a new Component Service
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    this.options = {
      defaultVerificationLevel: 'standard',
      autoVerify: true,
      ...options
    };
    
    // Initialize Trinity CSDE integration
    this.trinityIntegration = new NovaStoreTrinityIntegration(options);
    
    // Initialize component storage (in-memory for demo)
    this.components = new Map();
    
    console.log('NovaStore Component Service initialized');
  }
  
  /**
   * Create a new component
   * @param {Object} componentData - Component data
   * @returns {Promise<NovaStoreComponent>} - Created component
   */
  async createComponent(componentData) {
    // Create new component
    const component = new NovaStoreComponent(componentData);
    
    // Auto-verify if enabled
    if (this.options.autoVerify) {
      await this.verifyComponent(component, this.options.defaultVerificationLevel);
    }
    
    // Store component
    this.components.set(component.id, component);
    
    return component;
  }
  
  /**
   * Get a component by ID
   * @param {string} componentId - Component ID
   * @returns {NovaStoreComponent|null} - Component or null if not found
   */
  getComponent(componentId) {
    return this.components.get(componentId) || null;
  }
  
  /**
   * Get all components
   * @param {Object} [filters={}] - Filters to apply
   * @returns {Array<NovaStoreComponent>} - Array of components
   */
  getComponents(filters = {}) {
    let components = Array.from(this.components.values());
    
    // Apply filters
    if (filters.category) {
      components = components.filter(c => c.category === filters.category);
    }
    
    if (filters.verified) {
      components = components.filter(c => c.isVerified());
    }
    
    if (filters.author) {
      components = components.filter(c => c.author === filters.author);
    }
    
    if (filters.tags && filters.tags.length > 0) {
      components = components.filter(c => 
        filters.tags.some(tag => c.tags.includes(tag))
      );
    }
    
    // Apply sorting
    if (filters.sort) {
      switch (filters.sort) {
        case 'name':
          components.sort((a, b) => a.name.localeCompare(b.name));
          break;
        case 'downloads':
          components.sort((a, b) => b.downloads - a.downloads);
          break;
        case 'rating':
          components.sort((a, b) => b.averageRating - a.averageRating);
          break;
        case 'verification':
          components.sort((a, b) => b.verification.score - a.verification.score);
          break;
        default:
          // No sorting
      }
    }
    
    return components;
  }
  
  /**
   * Update a component
   * @param {string} componentId - Component ID
   * @param {Object} updates - Updates to apply
   * @returns {NovaStoreComponent|null} - Updated component or null if not found
   */
  async updateComponent(componentId, updates) {
    const component = this.getComponent(componentId);
    
    if (!component) {
      return null;
    }
    
    // Apply updates
    Object.keys(updates).forEach(key => {
      if (key !== 'id' && key !== 'verification') {
        component[key] = updates[key];
      }
    });
    
    // Re-verify if significant changes were made
    const significantChanges = [
      'policies', 'complianceScore', 'auditFrequency',
      'detectionCapability', 'threatSeverity', 'threatConfidence',
      'baseResponseTime', 'threatSurface', 'reactionTime'
    ];
    
    const needsVerification = significantChanges.some(key => key in updates);
    
    if (needsVerification && this.options.autoVerify) {
      await this.verifyComponent(component, component.verification.level || this.options.defaultVerificationLevel);
    }
    
    return component;
  }
  
  /**
   * Delete a component
   * @param {string} componentId - Component ID
   * @returns {boolean} - Whether the component was deleted
   */
  deleteComponent(componentId) {
    return this.components.delete(componentId);
  }
  
  /**
   * Verify a component using Trinity CSDE
   * @param {NovaStoreComponent} component - Component to verify
   * @param {string} [level='standard'] - Verification level
   * @returns {Promise<NovaStoreComponent>} - Verified component
   */
  async verifyComponent(component, level = 'standard') {
    try {
      // Verify component
      const verificationResult = await this.trinityIntegration.verifyComponent(component, level);
      
      // Update component verification status
      component.updateVerification(verificationResult);
      
      return component;
    } catch (error) {
      console.error(`Error verifying component ${component.id}: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Verify all components
   * @param {string} [level='standard'] - Verification level
   * @returns {Promise<Array<NovaStoreComponent>>} - Verified components
   */
  async verifyAllComponents(level = 'standard') {
    const components = this.getComponents();
    
    for (const component of components) {
      try {
        await this.verifyComponent(component, level);
      } catch (error) {
        console.error(`Error verifying component ${component.id}: ${error.message}`);
      }
    }
    
    return components;
  }
  
  /**
   * Get verification metrics
   * @returns {Object} - Verification metrics
   */
  getVerificationMetrics() {
    return this.trinityIntegration.getVerificationMetrics();
  }
  
  /**
   * Get adaptive ratios
   * @returns {Object} - Adaptive ratios
   */
  getAdaptiveRatios() {
    return this.trinityIntegration.getAdaptiveRatios();
  }
  
  /**
   * Calculate marketplace metrics
   * @returns {Object} - Marketplace metrics
   */
  calculateMarketplaceMetrics() {
    const components = this.getComponents();
    
    // Calculate total downloads
    const totalDownloads = components.reduce((sum, component) => sum + component.downloads, 0);
    
    // Calculate average rating
    const ratedComponents = components.filter(c => c.averageRating > 0);
    const averageRating = ratedComponents.length > 0 ?
      ratedComponents.reduce((sum, c) => sum + c.averageRating, 0) / ratedComponents.length : 0;
    
    // Calculate verification metrics
    const verifiedComponents = components.filter(c => c.isVerified());
    const verificationRate = components.length > 0 ?
      verifiedComponents.length / components.length : 0;
    
    // Calculate average verification score
    const averageVerificationScore = verifiedComponents.length > 0 ?
      verifiedComponents.reduce((sum, c) => sum + c.verification.score, 0) / verifiedComponents.length : 0;
    
    // Calculate badge distribution
    const badgeDistribution = {
      gold: components.filter(c => c.getVerificationBadge() === 'gold').length,
      silver: components.filter(c => c.getVerificationBadge() === 'silver').length,
      bronze: components.filter(c => c.getVerificationBadge() === 'bronze').length,
      none: components.filter(c => c.getVerificationBadge() === 'none').length
    };
    
    // Calculate category distribution
    const categoryDistribution = {};
    components.forEach(component => {
      const category = component.category;
      categoryDistribution[category] = (categoryDistribution[category] || 0) + 1;
    });
    
    return {
      totalComponents: components.length,
      totalDownloads,
      averageRating,
      verificationRate,
      averageVerificationScore,
      badgeDistribution,
      categoryDistribution,
      adaptiveRatios: this.getAdaptiveRatios(),
      timestamp: new Date().toISOString()
    };
  }
}

module.exports = ComponentService;
