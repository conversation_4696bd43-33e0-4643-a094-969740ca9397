/**
 * NovaConnect - BigQuery Live Integration Test
 * 
 * This test connects to the actual Google BigQuery API
 * to validate data protection and remediation capabilities.
 * 
 * NOTE: This test requires valid GCP credentials with BigQuery access.
 * Set the GOOGLE_APPLICATION_CREDENTIALS environment variable to point
 * to a service account key file with appropriate permissions.
 */

const { BigQuery } = require('@google-cloud/bigquery');
const { RemediationEngine } = require('../../../src/engines/remediation-engine');
const { EncryptionService } = require('../../../src/security/encryption-service');
const { performance } = require('perf_hooks');
const fs = require('fs');

// Skip tests if credentials are not available
const hasCredentials = process.env.GOOGLE_APPLICATION_CREDENTIALS && 
  fs.existsSync(process.env.GOOGLE_APPLICATION_CREDENTIALS);

// Test configuration
const config = {
  projectId: process.env.GCP_PROJECT_ID || 'test-project',
  datasetId: process.env.BQ_TEST_DATASET || 'test_dataset',
  tableId: process.env.BQ_TEST_TABLE || 'test_table',
  location: process.env.BQ_LOCATION || 'US'
};

describe('BigQuery Live Integration', () => {
  let bigquery;
  let remediationEngine;
  let encryptionService;
  let dataset;
  let table;
  
  beforeAll(async () => {
    // Initialize services
    remediationEngine = new RemediationEngine();
    encryptionService = new EncryptionService();
    
    // Generate encryption key
    await encryptionService.generateKey();
    
    if (hasCredentials) {
      try {
        // Initialize BigQuery client
        bigquery = new BigQuery({
          projectId: config.projectId
        });
        
        // Check if dataset exists, create if not
        try {
          [dataset] = await bigquery.dataset(config.datasetId).get();
        } catch (error) {
          if (error.code === 404) {
            // Create dataset for testing
            [dataset] = await bigquery.createDataset(config.datasetId, {
              location: config.location
            });
            console.log(`Created dataset ${config.datasetId}`);
          } else {
            throw error;
          }
        }
        
        // Check if table exists, create if not
        try {
          [table] = await dataset.table(config.tableId).get();
        } catch (error) {
          if (error.code === 404) {
            // Create table for testing
            const schema = [
              { name: 'id', type: 'STRING', mode: 'REQUIRED' },
              { name: 'name', type: 'STRING' },
              { name: 'email', type: 'STRING' },
              { name: 'ssn', type: 'STRING' }, // Sensitive data
              { name: 'created_at', type: 'TIMESTAMP' }
            ];
            
            [table] = await dataset.createTable(config.tableId, { schema });
            console.log(`Created table ${config.tableId}`);
            
            // Insert test data
            const rows = [
              { id: '1', name: 'John Doe', email: '<EMAIL>', ssn: '***********', created_at: new Date() },
              { id: '2', name: 'Jane Smith', email: '<EMAIL>', ssn: '***********', created_at: new Date() }
            ];
            
            await table.insert(rows);
            console.log(`Inserted ${rows.length} rows`);
          } else {
            throw error;
          }
        }
        
        // Register remediation actions
        remediationEngine.registerAction('encrypt-bigquery-column', async ({ parameters }) => {
          const { projectId, datasetId, tableId, columnName, keyId } = parameters;
          
          // Get the table
          const dataset = bigquery.dataset(datasetId);
          const table = dataset.table(tableId);
          
          // Get the current data
          const [rows] = await table.query(`SELECT * FROM \`${projectId}.${datasetId}.${tableId}\``);
          
          // Encrypt the sensitive column
          const encryptedRows = await Promise.all(rows.map(async (row) => {
            if (row[columnName]) {
              const encrypted = await encryptionService.encrypt(row[columnName], keyId);
              return {
                ...row,
                [columnName]: `ENCRYPTED:${JSON.stringify(encrypted)}`
              };
            }
            return row;
          }));
          
          // Update the table with encrypted data
          // In a real implementation, this would use a more efficient approach
          await table.delete();
          await new Promise(resolve => setTimeout(resolve, 1000)); // Wait for deletion
          
          const [newTable] = await dataset.createTable(tableId, {
            schema: table.metadata.schema
          });
          
          await newTable.insert(encryptedRows);
          
          return { success: true, rowsEncrypted: encryptedRows.length };
        });
        
        remediationEngine.registerAction('update-bigquery-access', async ({ parameters }) => {
          const { datasetId, allowedRoles } = parameters;
          
          // Get the dataset
          const dataset = bigquery.dataset(datasetId);
          
          // Get current access
          const [metadata] = await dataset.getMetadata();
          
          // Update access controls
          const newAccess = allowedRoles.map(role => ({
            role: 'READER',
            specialGroup: role
          }));
          
          metadata.access = newAccess;
          await dataset.setMetadata(metadata);
          
          return { success: true, updatedAccess: newAccess };
        });
      } catch (error) {
        console.error('Error setting up BigQuery test:', error);
      }
    }
  });
  
  afterAll(async () => {
    if (hasCredentials && dataset) {
      try {
        // Clean up test resources
        // Uncomment to actually delete the test dataset
        // await dataset.delete({ force: true });
        console.log(`Test cleanup complete`);
      } catch (error) {
        console.error('Error cleaning up:', error);
      }
    }
  });
  
  // Skip tests if credentials are not available
  const conditionalTest = hasCredentials ? it : it.skip;
  
  conditionalTest('should detect sensitive data in BigQuery', async () => {
    // Skip if no credentials
    if (!hasCredentials) {
      return;
    }
    
    const startTime = performance.now();
    
    // Query for sensitive data patterns
    const query = `
      SELECT column_name, data_type
      FROM \`${config.projectId}\`.${config.datasetId}.INFORMATION_SCHEMA.COLUMNS
      WHERE table_name = '${config.tableId}'
      AND (
        column_name LIKE '%ssn%' OR
        column_name LIKE '%social%' OR
        column_name LIKE '%tax%' OR
        column_name LIKE '%passport%' OR
        column_name LIKE '%credit%' OR
        column_name LIKE '%card%' OR
        column_name LIKE '%password%' OR
        column_name LIKE '%secret%'
      )
    `;
    
    const [sensitiveColumns] = await bigquery.query(query);
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    // Log performance metrics
    console.log(`Detected ${sensitiveColumns.length} sensitive columns in ${duration.toFixed(2)}ms`);
    
    // Verify sensitive data detection
    expect(Array.isArray(sensitiveColumns)).toBe(true);
    expect(sensitiveColumns.length).toBeGreaterThan(0);
    
    // Check if SSN column was detected
    const ssnColumn = sensitiveColumns.find(col => col.column_name === 'ssn');
    expect(ssnColumn).toBeDefined();
  }, 30000);
  
  conditionalTest('should encrypt sensitive data in BigQuery', async () => {
    // Skip if no credentials
    if (!hasCredentials) {
      return;
    }
    
    // Get encryption key
    const keys = Array.from(encryptionService.keyCache.keys());
    const keyId = keys[0];
    
    // Create remediation scenario
    const remediationScenario = {
      id: `bq-remediation-${Date.now()}`,
      type: 'compliance',
      framework: 'HIPAA',
      control: '164.312(a)(1)',
      severity: 'high',
      resource: {
        id: config.tableId,
        type: 'bigquery.table',
        name: config.tableId,
        provider: 'gcp',
        projectId: config.projectId
      },
      remediationSteps: [
        {
          id: 'step-1',
          action: 'encrypt-bigquery-column',
          parameters: {
            projectId: config.projectId,
            datasetId: config.datasetId,
            tableId: config.tableId,
            columnName: 'ssn',
            keyId
          }
        }
      ]
    };
    
    const startTime = performance.now();
    
    // Execute the remediation
    const remediationResult = await remediationEngine.executeRemediation(remediationScenario);
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    // Log performance metrics
    console.log(`Executed encryption remediation in ${duration.toFixed(2)}ms`);
    
    // Verify remediation result
    expect(remediationResult).toHaveProperty('id');
    expect(remediationResult).toHaveProperty('status');
    expect(remediationResult).toHaveProperty('steps');
    expect(remediationResult.steps.length).toBe(1);
    expect(remediationResult.steps[0].success).toBe(true);
    
    // Verify data is encrypted
    const [rows] = await table.query(`SELECT * FROM \`${config.projectId}.${config.datasetId}.${config.tableId}\` LIMIT 1`);
    
    if (rows.length > 0) {
      const row = rows[0];
      expect(row.ssn).toContain('ENCRYPTED:');
    }
    
    // Verify remediation speed
    expect(duration).toBeLessThan(8000); // Less than 8 seconds
  }, 60000);
  
  conditionalTest('should update BigQuery access controls', async () => {
    // Skip if no credentials
    if (!hasCredentials) {
      return;
    }
    
    // Create remediation scenario
    const remediationScenario = {
      id: `bq-access-remediation-${Date.now()}`,
      type: 'compliance',
      framework: 'HIPAA',
      control: '164.312(a)(1)',
      severity: 'high',
      resource: {
        id: config.datasetId,
        type: 'bigquery.dataset',
        name: config.datasetId,
        provider: 'gcp',
        projectId: config.projectId
      },
      remediationSteps: [
        {
          id: 'step-1',
          action: 'update-bigquery-access',
          parameters: {
            projectId: config.projectId,
            datasetId: config.datasetId,
            allowedRoles: ['projectOwners', 'projectReaders']
          }
        }
      ]
    };
    
    const startTime = performance.now();
    
    // Execute the remediation
    const remediationResult = await remediationEngine.executeRemediation(remediationScenario);
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    // Log performance metrics
    console.log(`Executed access control remediation in ${duration.toFixed(2)}ms`);
    
    // Verify remediation result
    expect(remediationResult).toHaveProperty('id');
    expect(remediationResult).toHaveProperty('status');
    expect(remediationResult).toHaveProperty('steps');
    expect(remediationResult.steps.length).toBe(1);
    expect(remediationResult.steps[0].success).toBe(true);
    
    // Verify access controls are updated
    const [metadata] = await dataset.getMetadata();
    expect(metadata.access).toBeDefined();
    
    // Verify remediation speed
    expect(duration).toBeLessThan(8000); // Less than 8 seconds
  }, 30000);
});
