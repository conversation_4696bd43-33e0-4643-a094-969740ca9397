/**
 * Internationalization Context
 * 
 * This module provides internationalization context and provider for the NovaVision Hub.
 */

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import PropTypes from 'prop-types';
import { usePerformance } from '../performance/usePerformance';

// Create internationalization context
const I18nContext = createContext();

/**
 * Internationalization provider component
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components
 * @param {Object} [props.initialLocale] - Initial locale
 * @param {Object} [props.initialMessages] - Initial messages
 * @param {Function} [props.onLocaleChanged] - Callback when locale changes
 * @param {boolean} [props.detectBrowserLocale=true] - Whether to detect browser locale
 * @returns {React.ReactElement} Internationalization provider component
 */
export const I18nProvider = ({
  children,
  initialLocale,
  initialMessages,
  onLocaleChanged,
  detectBrowserLocale = true
}) => {
  const { measureOperation } = usePerformance('I18nProvider');
  
  // State
  const [locale, setLocale] = useState(initialLocale || 'en-US');
  const [messages, setMessages] = useState(initialMessages || {});
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [availableLocales, setAvailableLocales] = useState([
    { code: 'en-US', name: 'English (US)' },
    { code: 'es-ES', name: 'Español (España)' },
    { code: 'fr-FR', name: 'Français (France)' },
    { code: 'de-DE', name: 'Deutsch (Deutschland)' },
    { code: 'ja-JP', name: '日本語 (日本)' },
    { code: 'zh-CN', name: '中文 (简体)' }
  ]);
  
  // Detect browser locale
  useEffect(() => {
    if (detectBrowserLocale && !initialLocale) {
      const detectLocale = () => {
        try {
          // Get browser language
          const browserLocale = navigator.language || navigator.userLanguage || 'en-US';
          
          // Check if browser locale is available
          const isAvailable = availableLocales.some(l => l.code === browserLocale);
          
          // Set locale
          setLocale(isAvailable ? browserLocale : 'en-US');
        } catch (err) {
          console.error('Error detecting browser locale:', err);
          setLocale('en-US');
        }
      };
      
      detectLocale();
    }
  }, [detectBrowserLocale, initialLocale, availableLocales]);
  
  // Load messages for locale
  useEffect(() => {
    const loadMessages = async () => {
      if (initialMessages && initialMessages[locale]) {
        setMessages(initialMessages);
        return;
      }
      
      setIsLoading(true);
      setError(null);
      
      try {
        // Load messages for locale
        const localeMessages = await measureOperation('loadMessages', () => 
          import(`./locales/${locale}.json`)
            .then(module => module.default)
            .catch(() => import(`./locales/en-US.json`).then(module => module.default))
        );
        
        setMessages(prevMessages => ({
          ...prevMessages,
          [locale]: localeMessages
        }));
        
        // Notify locale changed
        if (onLocaleChanged) {
          onLocaleChanged(locale);
        }
      } catch (err) {
        console.error(`Error loading messages for locale ${locale}:`, err);
        setError(err);
      } finally {
        setIsLoading(false);
      }
    };
    
    loadMessages();
  }, [locale, initialMessages, onLocaleChanged, measureOperation]);
  
  /**
   * Change locale
   * 
   * @param {string} newLocale - New locale
   */
  const changeLocale = useCallback((newLocale) => {
    if (newLocale && newLocale !== locale) {
      setLocale(newLocale);
    }
  }, [locale]);
  
  /**
   * Add messages for locale
   * 
   * @param {string} localeCode - Locale code
   * @param {Object} localeMessages - Locale messages
   */
  const addMessages = useCallback((localeCode, localeMessages) => {
    if (localeCode && localeMessages) {
      setMessages(prevMessages => ({
        ...prevMessages,
        [localeCode]: {
          ...(prevMessages[localeCode] || {}),
          ...localeMessages
        }
      }));
    }
  }, []);
  
  /**
   * Add locale
   * 
   * @param {string} localeCode - Locale code
   * @param {string} localeName - Locale name
   * @param {Object} [localeMessages] - Locale messages
   */
  const addLocale = useCallback((localeCode, localeName, localeMessages) => {
    if (localeCode && localeName) {
      // Add to available locales
      setAvailableLocales(prevLocales => {
        // Check if locale already exists
        const exists = prevLocales.some(l => l.code === localeCode);
        
        if (exists) {
          return prevLocales;
        }
        
        return [...prevLocales, { code: localeCode, name: localeName }];
      });
      
      // Add messages if provided
      if (localeMessages) {
        addMessages(localeCode, localeMessages);
      }
    }
  }, [addMessages]);
  
  /**
   * Translate message
   * 
   * @param {string} key - Message key
   * @param {Object} [params] - Message parameters
   * @returns {string} Translated message
   */
  const translate = useCallback((key, params = {}) => {
    if (!key) {
      return '';
    }
    
    // Get messages for current locale
    const localeMessages = messages[locale] || {};
    
    // Get message
    let message = localeMessages[key];
    
    // Fallback to English
    if (!message && locale !== 'en-US') {
      message = (messages['en-US'] || {})[key];
    }
    
    // Fallback to key
    if (!message) {
      return key;
    }
    
    // Replace parameters
    if (params && Object.keys(params).length > 0) {
      return Object.entries(params).reduce((msg, [paramKey, paramValue]) => {
        return msg.replace(new RegExp(`{${paramKey}}`, 'g'), paramValue);
      }, message);
    }
    
    return message;
  }, [locale, messages]);
  
  /**
   * Format date
   * 
   * @param {Date|string|number} date - Date to format
   * @param {Object} [options] - Format options
   * @returns {string} Formatted date
   */
  const formatDate = useCallback((date, options = {}) => {
    try {
      const dateObj = date instanceof Date ? date : new Date(date);
      return new Intl.DateTimeFormat(locale, options).format(dateObj);
    } catch (err) {
      console.error('Error formatting date:', err);
      return String(date);
    }
  }, [locale]);
  
  /**
   * Format number
   * 
   * @param {number} number - Number to format
   * @param {Object} [options] - Format options
   * @returns {string} Formatted number
   */
  const formatNumber = useCallback((number, options = {}) => {
    try {
      return new Intl.NumberFormat(locale, options).format(number);
    } catch (err) {
      console.error('Error formatting number:', err);
      return String(number);
    }
  }, [locale]);
  
  /**
   * Format currency
   * 
   * @param {number} number - Number to format
   * @param {string} [currency='USD'] - Currency code
   * @param {Object} [options] - Format options
   * @returns {string} Formatted currency
   */
  const formatCurrency = useCallback((number, currency = 'USD', options = {}) => {
    try {
      return new Intl.NumberFormat(locale, {
        style: 'currency',
        currency,
        ...options
      }).format(number);
    } catch (err) {
      console.error('Error formatting currency:', err);
      return String(number);
    }
  }, [locale]);
  
  /**
   * Get text direction
   * 
   * @returns {string} Text direction ('ltr' or 'rtl')
   */
  const getTextDirection = useCallback(() => {
    // RTL languages
    const rtlLocales = ['ar', 'he', 'fa', 'ur'];
    
    // Check if current locale is RTL
    const localePrefix = locale.split('-')[0];
    return rtlLocales.includes(localePrefix) ? 'rtl' : 'ltr';
  }, [locale]);
  
  // Create context value
  const contextValue = {
    locale,
    messages: messages[locale] || {},
    isLoading,
    error,
    availableLocales,
    changeLocale,
    addMessages,
    addLocale,
    translate,
    formatDate,
    formatNumber,
    formatCurrency,
    getTextDirection
  };
  
  return (
    <I18nContext.Provider value={contextValue}>
      {children}
    </I18nContext.Provider>
  );
};

I18nProvider.propTypes = {
  children: PropTypes.node.isRequired,
  initialLocale: PropTypes.string,
  initialMessages: PropTypes.object,
  onLocaleChanged: PropTypes.func,
  detectBrowserLocale: PropTypes.bool
};

/**
 * Use internationalization hook
 * 
 * @returns {Object} Internationalization context
 */
export const useI18n = () => {
  const context = useContext(I18nContext);
  
  if (!context) {
    throw new Error('useI18n must be used within an I18nProvider');
  }
  
  return context;
};

export default I18nContext;
