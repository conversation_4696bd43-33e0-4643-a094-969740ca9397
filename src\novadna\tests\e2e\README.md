# NovaDNA End-to-End Tests

This directory contains end-to-end tests for the NovaDNA system. These tests simulate real-world scenarios to verify that all components work together correctly.

## Running Tests

To run the tests, use the following commands:

```bash
# Run all end-to-end tests
node runE2ETests.js

# Run specific scenarios
node runE2ETests.js access        # Run emergency access scenario
node runE2ETests.js override      # Run emergency override scenario
node runE2ETests.js epic          # Run Epic integration scenario
node runE2ETests.js multiprovider # Run multi-provider integration scenario
```

## Test Scenarios

### Emergency Access Scenarios

1. **Emergency Access Scenario**
   - Simulates the complete flow from scanning a QR code to accessing medical data
   - Tests the progressive disclosure system based on access level
   - Verifies that critical information is highlighted based on emergency context

2. **Emergency Override Scenario**
   - Simulates the break-glass protocol for emergency override
   - Tests the override request, approval, and review process
   - Verifies that full access is granted during critical emergencies

### Healthcare Integration Scenarios

1. **Epic Integration Scenario**
   - Simulates integration with Epic EHR system
   - Tests the connection, data retrieval, and caching process
   - Verifies that medical data is correctly formatted for emergency use

2. **Multi-Provider Integration Scenario**
   - Simulates integration with multiple healthcare providers (Epic and Cerner)
   - Tests data source prioritization based on emergency context
   - Verifies that data from multiple sources is correctly merged

## Test Structure

Each scenario follows a step-by-step process to simulate a real-world emergency situation:

1. **Initialize Components**: Set up all required components
2. **Simulate User Actions**: Simulate QR code scanning, form submission, etc.
3. **Process Data**: Retrieve and process medical data
4. **Verify Results**: Verify that the data is correctly processed and displayed
5. **Clean Up**: End emergency sessions and clean up resources

## Adding New Scenarios

To add a new scenario:

1. Create a new file with your scenario implementation
2. Export a function that runs the scenario and returns a boolean result
3. Update `runE2ETests.js` to include your new scenario

Example:

```javascript
// MyNewScenario.js
async function runMyNewScenario() {
  console.log('=== My New Scenario ===');
  // Implement your scenario
  return true; // Return true if successful, false otherwise
}

module.exports = {
  runMyNewScenario
};
```

Then update `runE2ETests.js` to include your new scenario:

```javascript
// In runE2ETests.js
const { runMyNewScenario } = require('./MyNewScenario');

// Add to runAllE2ETests function
console.log('\n=== My New Scenario ===');
const myNewResult = await runMyNewScenario();

if (myNewResult) {
  console.log('✅ My new scenario passed');
} else {
  console.error('❌ My new scenario failed');
  allTestsPassed = false;
}

// Add to runSpecificE2ETest function
case 'mynew':
  result = await runMyNewScenario();
  break;
```

## Test Coverage

The current end-to-end tests cover:

1. **User Interface Flow**
   - Emergency access UI
   - Emergency override UI
   - Profile view UI with different access levels

2. **Healthcare Integration**
   - Connection to EHR systems
   - Data retrieval and merging
   - Caching and performance optimization

3. **Security Features**
   - Authentication and authorization
   - Break-glass protocol
   - Audit logging

4. **Data Processing**
   - Progressive disclosure based on access level
   - Context-aware data prioritization
   - Highlighting critical information
