"""
Predictive Compliance Intelligence Engine for the Universal Compliance Tracking Optimizer.

This module provides functionality for predicting compliance gaps, forecasting resource
requirements, and recommending proactive actions.
"""

import os
import json
import logging
import numpy as np
from typing import Dict, List, Any, Optional, Callable, Tuple, Union

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PredictiveEngine:
    """
    Predictive Compliance Intelligence Engine.

    This class is responsible for analyzing compliance data to predict gaps,
    forecast resource requirements, and recommend proactive actions.
    """

    def __init__(self, data_dir: Optional[str] = None):
        """
        Initialize the Predictive Engine.

        Args:
            data_dir: Path to a directory for storing prediction data
        """
        logger.info("Initializing Predictive Compliance Intelligence Engine")

        # Set the data directory
        self.data_dir = data_dir or os.path.join(os.getcwd(), 'prediction_data')

        # Create the data directory if it doesn't exist
        os.makedirs(self.data_dir, exist_ok=True)

        # Dictionary to store prediction models
        self.prediction_models: Dict[str, Callable] = {}

        # Dictionary to store forecasting models
        self.forecasting_models: Dict[str, Callable] = {}

        # Dictionary to store recommendation models
        self.recommendation_models: Dict[str, Callable] = {}

        # Initialize the model registry
        from .model_registry import ModelRegistry
        self.model_registry = ModelRegistry(os.path.join(self.data_dir, 'model_registry'))

        # Initialize ML utilities
        from .ml_utils import MLUtils
        self.ml_utils = MLUtils

        logger.info(f"Predictive Compliance Intelligence Engine initialized with data directory: {self.data_dir}")

    def register_prediction_model(self, model_name: str, model_function: Callable) -> None:
        """
        Register a custom prediction model.

        Args:
            model_name: The name of the model
            model_function: The function that implements the model

        Raises:
            ValueError: If a model with the same name already exists
        """
        logger.info(f"Registering prediction model: {model_name}")

        if model_name in self.prediction_models:
            raise ValueError(f"Prediction model already exists: {model_name}")

        self.prediction_models[model_name] = model_function

        logger.info(f"Prediction model registered: {model_name}")

    def register_forecasting_model(self, model_name: str, model_function: Callable) -> None:
        """
        Register a custom forecasting model.

        Args:
            model_name: The name of the model
            model_function: The function that implements the model

        Raises:
            ValueError: If a model with the same name already exists
        """
        logger.info(f"Registering forecasting model: {model_name}")

        if model_name in self.forecasting_models:
            raise ValueError(f"Forecasting model already exists: {model_name}")

        self.forecasting_models[model_name] = model_function

        logger.info(f"Forecasting model registered: {model_name}")

    def register_recommendation_model(self, model_name: str, model_function: Callable) -> None:
        """
        Register a custom recommendation model.

        Args:
            model_name: The name of the model
            model_function: The function that implements the model

        Raises:
            ValueError: If a model with the same name already exists
        """
        logger.info(f"Registering recommendation model: {model_name}")

        if model_name in self.recommendation_models:
            raise ValueError(f"Recommendation model already exists: {model_name}")

        self.recommendation_models[model_name] = model_function

        logger.info(f"Recommendation model registered: {model_name}")

    def predict_compliance_gaps(self, requirements: List[Dict[str, Any]],
                               activities: List[Dict[str, Any]],
                               parameters: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Predict potential compliance gaps based on historical data.

        Args:
            requirements: List of compliance requirements
            activities: List of compliance activities
            parameters: Optional parameters for the prediction

        Returns:
            Prediction results including potential compliance gaps
        """
        logger.info("Predicting compliance gaps")

        # Default parameters
        params = {
            'confidence_threshold': 0.7,
            'time_horizon_days': 90,
            'model': 'default'
        }

        # Update with provided parameters
        if parameters:
            params.update(parameters)

        # Check if a custom model is specified
        model_name = params.get('model')

        if model_name != 'default' and model_name in self.prediction_models:
            logger.info(f"Using custom prediction model: {model_name}")

            # Call the custom model
            try:
                custom_result = self.prediction_models[model_name](requirements, activities, params)

                # Create the prediction object
                prediction = {
                    'prediction_id': self._generate_id(),
                    'prediction_type': 'compliance_gaps',
                    'timestamp': self._get_current_timestamp(),
                    'parameters': params,
                    'predicted_gaps': custom_result.get('predicted_gaps', []),
                    'confidence_score': custom_result.get('confidence_score', 0.0),
                    'model': model_name
                }

                logger.info(f"Custom prediction model {model_name} completed with ID: {prediction['prediction_id']}")

                return prediction

            except Exception as e:
                logger.error(f"Error using custom prediction model {model_name}: {e}")
                logger.info("Falling back to default prediction model")

        # Use the default model
        logger.info("Using default prediction model")

        # Placeholder for prediction logic
        # In a real implementation, this would use historical data and machine learning
        # to predict potential compliance gaps

        # For now, return a simple prediction
        prediction = {
            'prediction_id': self._generate_id(),
            'prediction_type': 'compliance_gaps',
            'timestamp': self._get_current_timestamp(),
            'parameters': params,
            'predicted_gaps': [],
            'confidence_score': 0.0,
            'model': 'default'
        }

        logger.info(f"Compliance gap prediction completed with ID: {prediction['prediction_id']}")

        return prediction

    def forecast_resource_requirements(self, requirements: List[Dict[str, Any]],
                                      activities: List[Dict[str, Any]],
                                      parameters: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Forecast resource requirements for upcoming compliance activities.

        Args:
            requirements: List of compliance requirements
            activities: List of compliance activities
            parameters: Optional parameters for the forecast

        Returns:
            Forecast results including resource requirements
        """
        logger.info("Forecasting resource requirements")

        # Default parameters
        params = {
            'time_horizon_days': 90,
            'resource_types': ['staff', 'budget', 'time'],
            'model': 'default'
        }

        # Update with provided parameters
        if parameters:
            params.update(parameters)

        # Check if a custom model is specified
        model_name = params.get('model')

        if model_name != 'default' and model_name in self.forecasting_models:
            logger.info(f"Using custom forecasting model: {model_name}")

            # Call the custom model
            try:
                custom_result = self.forecasting_models[model_name](requirements, activities, params)

                # Create the forecast object
                forecast = {
                    'forecast_id': self._generate_id(),
                    'forecast_type': 'resource_requirements',
                    'timestamp': self._get_current_timestamp(),
                    'parameters': params,
                    'forecasted_resources': custom_result.get('forecasted_resources', []),
                    'confidence_score': custom_result.get('confidence_score', 0.0),
                    'model': model_name
                }

                logger.info(f"Custom forecasting model {model_name} completed with ID: {forecast['forecast_id']}")

                return forecast

            except Exception as e:
                logger.error(f"Error using custom forecasting model {model_name}: {e}")
                logger.info("Falling back to default forecasting model")

        # Use the default model
        logger.info("Using default forecasting model")

        # Placeholder for forecasting logic
        # In a real implementation, this would use historical data and time series analysis
        # to forecast resource requirements

        # For now, return a simple forecast
        forecast = {
            'forecast_id': self._generate_id(),
            'forecast_type': 'resource_requirements',
            'timestamp': self._get_current_timestamp(),
            'parameters': params,
            'forecasted_resources': [],
            'confidence_score': 0.0,
            'model': 'default'
        }

        logger.info(f"Resource requirements forecast completed with ID: {forecast['forecast_id']}")

        return forecast

    def recommend_actions(self, requirements: List[Dict[str, Any]],
                         activities: List[Dict[str, Any]],
                         predictions: Optional[List[Dict[str, Any]]] = None,
                         parameters: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Recommend proactive actions based on predictions and forecasts.

        Args:
            requirements: List of compliance requirements
            activities: List of compliance activities
            predictions: Optional list of predictions to base recommendations on
            parameters: Optional parameters for the recommendations

        Returns:
            Recommendation results including suggested actions
        """
        logger.info("Recommending proactive actions")

        # Default parameters
        params = {
            'max_recommendations': 5,
            'priority_threshold': 'medium',
            'model': 'default'
        }

        # Update with provided parameters
        if parameters:
            params.update(parameters)

        # Check if a custom model is specified
        model_name = params.get('model')

        if model_name != 'default' and model_name in self.recommendation_models:
            logger.info(f"Using custom recommendation model: {model_name}")

            # Call the custom model
            try:
                custom_result = self.recommendation_models[model_name](requirements, activities, predictions, params)

                # Create the recommendations object
                recommendations = {
                    'recommendation_id': self._generate_id(),
                    'recommendation_type': 'proactive_actions',
                    'timestamp': self._get_current_timestamp(),
                    'parameters': params,
                    'recommended_actions': custom_result.get('recommended_actions', []),
                    'priority_scores': custom_result.get('priority_scores', {}),
                    'model': model_name
                }

                logger.info(f"Custom recommendation model {model_name} completed with ID: {recommendations['recommendation_id']}")

                return recommendations

            except Exception as e:
                logger.error(f"Error using custom recommendation model {model_name}: {e}")
                logger.info("Falling back to default recommendation model")

        # Use the default model
        logger.info("Using default recommendation model")

        # Placeholder for recommendation logic
        # In a real implementation, this would analyze predictions and forecasts
        # to recommend proactive actions

        # For now, return simple recommendations
        recommendations = {
            'recommendation_id': self._generate_id(),
            'recommendation_type': 'proactive_actions',
            'timestamp': self._get_current_timestamp(),
            'parameters': params,
            'recommended_actions': [],
            'priority_scores': {},
            'model': 'default'
        }

        logger.info(f"Action recommendations completed with ID: {recommendations['recommendation_id']}")

        return recommendations

    def train_ml_model(self,
                    model_type: str,
                    requirements: List[Dict[str, Any]],
                    activities: List[Dict[str, Any]],
                    labels: List[int],
                    model_config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Train a machine learning model.

        Args:
            model_type: Type of model to train (e.g., 'compliance_gap', 'resource_forecast')
            requirements: List of requirements for training
            activities: List of activities for training
            labels: Labels for training
            model_config: Optional configuration for the model

        Returns:
            Training results including model ID and metrics
        """
        logger.info(f"Training {model_type} model")

        # Default model configuration
        config = {
            'model_algorithm': 'random_forest',
            'feature_config': {
                'requirement_features': ['priority', 'status', 'days_until_due'],
                'activity_features': ['status', 'days_until_due'],
                'normalize': True
            },
            'model_params': {
                'n_estimators': 100,
                'max_depth': 10,
                'random_state': 42
            },
            'test_size': 0.2
        }

        # Update with provided configuration
        if model_config:
            # Deep update for nested dictionaries
            for key, value in model_config.items():
                if isinstance(value, dict) and key in config and isinstance(config[key], dict):
                    config[key].update(value)
                else:
                    config[key] = value

        try:
            # Extract features
            X, feature_names = self.ml_utils.extract_features(
                requirements,
                activities,
                config['feature_config']
            )

            # Convert labels to numpy array
            y = np.array(labels)

            # Split data into training and testing sets
            from sklearn.model_selection import train_test_split
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=config['test_size'], random_state=config['model_params'].get('random_state', 42)
            )

            # Train the model
            model = None

            if config['model_algorithm'] == 'random_forest':
                from sklearn.ensemble import RandomForestClassifier
                model = RandomForestClassifier(**config['model_params'])

            elif config['model_algorithm'] == 'gradient_boosting':
                from sklearn.ensemble import GradientBoostingClassifier
                model = GradientBoostingClassifier(**config['model_params'])

            elif config['model_algorithm'] == 'logistic_regression':
                from sklearn.linear_model import LogisticRegression
                model = LogisticRegression(**config['model_params'])

            else:
                raise ValueError(f"Unsupported model algorithm: {config['model_algorithm']}")

            # Fit the model
            model.fit(X_train, y_train)

            # Evaluate the model
            metrics = self.ml_utils.evaluate_model(model, X_test, y_test)

            # Generate a model ID
            model_id = f"{model_type}_{config['model_algorithm']}_{self._generate_id()}"

            # Register the model
            metadata = {
                'type': model_type,
                'algorithm': config['model_algorithm'],
                'feature_names': feature_names,
                'metrics': metrics,
                'config': config,
                'training_data_size': len(X),
                'training_date': self._get_current_timestamp()
            }

            self.model_registry.register_model(model_id, model, metadata)

            # Return the results
            result = {
                'model_id': model_id,
                'model_type': model_type,
                'algorithm': config['model_algorithm'],
                'metrics': metrics,
                'feature_names': feature_names,
                'training_data_size': len(X)
            }

            logger.info(f"Model training completed with ID: {model_id}")

            return result

        except Exception as e:
            logger.error(f"Failed to train model: {e}")
            raise

    def predict_with_ml_model(self,
                             model_id: str,
                             requirements: List[Dict[str, Any]],
                             activities: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Make predictions using a trained machine learning model.

        Args:
            model_id: ID of the model to use
            requirements: List of requirements for prediction
            activities: List of activities for prediction

        Returns:
            Prediction results
        """
        logger.info(f"Making predictions with model: {model_id}")

        try:
            # Get the model and metadata
            model, metadata = self.model_registry.get_model(model_id)

            # Extract features
            X, feature_names = self.ml_utils.extract_features(
                requirements,
                activities,
                metadata.get('config', {}).get('feature_config', {})
            )

            # Make predictions
            y_pred = model.predict(X)

            # Get prediction probabilities if available
            y_prob = None
            if hasattr(model, 'predict_proba'):
                y_prob = model.predict_proba(X)

            # Create the prediction result
            result = {
                'prediction_id': self._generate_id(),
                'model_id': model_id,
                'model_type': metadata.get('type'),
                'timestamp': self._get_current_timestamp(),
                'predictions': []
            }

            # Add predictions for each requirement
            for i, req in enumerate(requirements):
                prediction = {
                    'requirement_id': req.get('id'),
                    'requirement_name': req.get('name'),
                    'prediction': int(y_pred[i])
                }

                # Add probability if available
                if y_prob is not None:
                    prediction['probability'] = float(y_prob[i][1])

                result['predictions'].append(prediction)

            logger.info(f"Predictions completed with model: {model_id}")

            return result

        except Exception as e:
            logger.error(f"Failed to make predictions with model {model_id}: {e}")
            raise

    def _generate_id(self) -> str:
        """
        Generate a unique ID.

        Returns:
            A unique ID string
        """
        import uuid
        return str(uuid.uuid4())

    def _get_current_timestamp(self) -> str:
        """
        Get the current timestamp.

        Returns:
            The current timestamp as a string
        """
        import datetime
        return datetime.datetime.now().isoformat()
