"""
Example of using the Query Manager.

This example demonstrates how to use the Query Manager to search for evidence
and requirements based on various criteria.
"""

import os
import sys
import json
import logging
import datetime
import uuid

# Add the parent directory to the path so we can import the UCECS modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.ucecs.core.query_manager import QueryManager, QueryField, QueryOperator

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """Run the Query Manager example."""
    # Create a Query Manager
    query_manager = QueryManager()
    
    # Create sample evidence metadata
    evidence_metadata = {}
    evidence_tags = {}
    evidence_by_category = {
        'configuration': set(),
        'policy': set(),
        'procedure': set(),
        'log': set()
    }
    
    # Create sample requirements
    requirements = {
        'REQ-001': {
            'id': 'REQ-001',
            'name': 'Password Policy',
            'description': 'The organization must have a password policy that requires complex passwords.',
            'framework': 'NIST SP 800-53',
            'control': 'IA-5'
        },
        'REQ-002': {
            'id': 'REQ-002',
            'name': 'Access Control',
            'description': 'The organization must implement access controls to restrict system access to authorized users.',
            'framework': 'NIST SP 800-53',
            'control': 'AC-3'
        },
        'REQ-003': {
            'id': 'REQ-003',
            'name': 'Audit Logging',
            'description': 'The organization must implement audit logging to record security-relevant events.',
            'framework': 'NIST SP 800-53',
            'control': 'AU-2'
        },
        'REQ-004': {
            'id': 'REQ-004',
            'name': 'Security Awareness Training',
            'description': 'The organization must provide security awareness training to all personnel.',
            'framework': 'NIST SP 800-53',
            'control': 'AT-2'
        },
        'REQ-005': {
            'id': 'REQ-005',
            'name': 'Incident Response',
            'description': 'The organization must establish an incident response capability.',
            'framework': 'NIST SP 800-53',
            'control': 'IR-1'
        }
    }
    
    # Create sample evidence by requirement
    evidence_by_requirement = {
        'REQ-001': set(),
        'REQ-002': set(),
        'REQ-003': set(),
        'REQ-004': set(),
        'REQ-005': set()
    }
    
    # Create sample evidence items
    for i in range(50):
        # Generate a unique ID
        evidence_id = f"evidence_{i+1}"
        
        # Determine the type and category
        if i < 10:
            evidence_type = 'document'
            category = 'policy'
            requirement_id = 'REQ-001'
            is_valid = i != 5  # Make one invalid
        elif i < 20:
            evidence_type = 'configuration'
            category = 'configuration'
            requirement_id = 'REQ-002'
            is_valid = i != 15  # Make one invalid
        elif i < 30:
            evidence_type = 'log'
            category = 'log'
            requirement_id = 'REQ-003'
            is_valid = i != 25  # Make one invalid
        elif i < 40:
            evidence_type = 'document'
            category = 'procedure'
            requirement_id = 'REQ-004'
            is_valid = i != 35  # Make one invalid
        else:
            evidence_type = 'document'
            category = 'policy'
            requirement_id = 'REQ-005'
            is_valid = i != 45  # Make one invalid
        
        # Create the evidence metadata
        created_at = (datetime.datetime.now(datetime.timezone.utc) - 
                     datetime.timedelta(days=i)).isoformat()
        
        metadata = {
            'id': evidence_id,
            'type': evidence_type,
            'source': 'example',
            'status': 'stored',
            'created_at': created_at,
            'updated_at': created_at,
            'validation_results': {
                'is_valid': is_valid,
                'details': {
                    'reason': 'Example validation result'
                }
            },
            'data': {
                'title': f'Sample {evidence_type.title()} {i+1}',
                'content': f'This is a sample {evidence_type} for testing the Query Manager.'
            }
        }
        
        # Add the evidence metadata
        evidence_metadata[evidence_id] = metadata
        
        # Add tags
        tags = set(['example', evidence_type])
        if category:
            tags.add(category)
        evidence_tags[evidence_id] = tags
        
        # Add to category
        if category:
            evidence_by_category[category].add(evidence_id)
        
        # Add to requirement
        evidence_by_requirement[requirement_id].add(evidence_id)
    
    try:
        # Example 1: Simple query by type
        logger.info("Example 1: Simple query by type")
        type_query = query_manager.build_query(QueryField.TYPE, 'document')
        type_results = query_manager.search_evidence(
            evidence_metadata=evidence_metadata,
            evidence_tags=evidence_tags,
            evidence_by_category=evidence_by_category,
            evidence_by_requirement=evidence_by_requirement,
            query=type_query,
            page=1,
            page_size=10
        )
        logger.info(f"Found {type_results['pagination']['total_results']} documents")
        logger.info(f"First page results: {json.dumps([r['id'] for r in type_results['results']], indent=2)}")
        
        # Example 2: Query by category
        logger.info("\nExample 2: Query by category")
        category_query = query_manager.build_query(QueryField.CATEGORY, 'policy')
        category_results = query_manager.search_evidence(
            evidence_metadata=evidence_metadata,
            evidence_tags=evidence_tags,
            evidence_by_category=evidence_by_category,
            evidence_by_requirement=evidence_by_requirement,
            query=category_query,
            page=1,
            page_size=10
        )
        logger.info(f"Found {category_results['pagination']['total_results']} policy evidence items")
        logger.info(f"First page results: {json.dumps([r['id'] for r in category_results['results']], indent=2)}")
        
        # Example 3: Query by validation status
        logger.info("\nExample 3: Query by validation status")
        validation_query = query_manager.build_query(QueryField.VALIDATION_STATUS, True)
        validation_results = query_manager.search_evidence(
            evidence_metadata=evidence_metadata,
            evidence_tags=evidence_tags,
            evidence_by_category=evidence_by_category,
            evidence_by_requirement=evidence_by_requirement,
            query=validation_query,
            page=1,
            page_size=10
        )
        logger.info(f"Found {validation_results['pagination']['total_results']} valid evidence items")
        logger.info(f"First page results: {json.dumps([r['id'] for r in validation_results['results']], indent=2)}")
        
        # Example 4: Query by date range
        logger.info("\nExample 4: Query by date range")
        date_query = query_manager.build_query(
            QueryField.CREATED_AT,
            {
                'start': (datetime.datetime.now(datetime.timezone.utc) - datetime.timedelta(days=20)).isoformat(),
                'end': (datetime.datetime.now(datetime.timezone.utc) - datetime.timedelta(days=10)).isoformat()
            }
        )
        date_results = query_manager.search_evidence(
            evidence_metadata=evidence_metadata,
            evidence_tags=evidence_tags,
            evidence_by_category=evidence_by_category,
            evidence_by_requirement=evidence_by_requirement,
            query=date_query,
            page=1,
            page_size=10
        )
        logger.info(f"Found {date_results['pagination']['total_results']} evidence items in date range")
        logger.info(f"First page results: {json.dumps([r['id'] for r in date_results['results']], indent=2)}")
        
        # Example 5: Compound query (AND)
        logger.info("\nExample 5: Compound query (AND)")
        and_query = query_manager.build_compound_query(
            QueryOperator.AND,
            [
                query_manager.build_query(QueryField.TYPE, 'document'),
                query_manager.build_query(QueryField.VALIDATION_STATUS, True)
            ]
        )
        and_results = query_manager.search_evidence(
            evidence_metadata=evidence_metadata,
            evidence_tags=evidence_tags,
            evidence_by_category=evidence_by_category,
            evidence_by_requirement=evidence_by_requirement,
            query=and_query,
            page=1,
            page_size=10
        )
        logger.info(f"Found {and_results['pagination']['total_results']} valid documents")
        logger.info(f"First page results: {json.dumps([r['id'] for r in and_results['results']], indent=2)}")
        
        # Example 6: Compound query (OR)
        logger.info("\nExample 6: Compound query (OR)")
        or_query = query_manager.build_compound_query(
            QueryOperator.OR,
            [
                query_manager.build_query(QueryField.CATEGORY, 'policy'),
                query_manager.build_query(QueryField.CATEGORY, 'procedure')
            ]
        )
        or_results = query_manager.search_evidence(
            evidence_metadata=evidence_metadata,
            evidence_tags=evidence_tags,
            evidence_by_category=evidence_by_category,
            evidence_by_requirement=evidence_by_requirement,
            query=or_query,
            page=1,
            page_size=10
        )
        logger.info(f"Found {or_results['pagination']['total_results']} policy or procedure evidence items")
        logger.info(f"First page results: {json.dumps([r['id'] for r in or_results['results']], indent=2)}")
        
        # Example 7: Compound query (NOT)
        logger.info("\nExample 7: Compound query (NOT)")
        not_query = query_manager.build_compound_query(
            QueryOperator.NOT,
            [
                query_manager.build_query(QueryField.VALIDATION_STATUS, True)
            ]
        )
        not_results = query_manager.search_evidence(
            evidence_metadata=evidence_metadata,
            evidence_tags=evidence_tags,
            evidence_by_category=evidence_by_category,
            evidence_by_requirement=evidence_by_requirement,
            query=not_query,
            page=1,
            page_size=10
        )
        logger.info(f"Found {not_results['pagination']['total_results']} invalid evidence items")
        logger.info(f"First page results: {json.dumps([r['id'] for r in not_results['results']], indent=2)}")
        
        # Example 8: Complex compound query
        logger.info("\nExample 8: Complex compound query")
        complex_query = query_manager.build_compound_query(
            QueryOperator.AND,
            [
                query_manager.build_query(QueryField.TYPE, 'document'),
                query_manager.build_compound_query(
                    QueryOperator.OR,
                    [
                        query_manager.build_query(QueryField.CATEGORY, 'policy'),
                        query_manager.build_query(QueryField.CATEGORY, 'procedure')
                    ]
                ),
                query_manager.build_query(QueryField.VALIDATION_STATUS, True)
            ]
        )
        complex_results = query_manager.search_evidence(
            evidence_metadata=evidence_metadata,
            evidence_tags=evidence_tags,
            evidence_by_category=evidence_by_category,
            evidence_by_requirement=evidence_by_requirement,
            query=complex_query,
            page=1,
            page_size=10
        )
        logger.info(f"Found {complex_results['pagination']['total_results']} valid policy or procedure documents")
        logger.info(f"First page results: {json.dumps([r['id'] for r in complex_results['results']], indent=2)}")
        
        # Example 9: Search requirements by framework
        logger.info("\nExample 9: Search requirements by framework")
        framework_query = {
            'field': 'framework',
            'value': 'NIST SP 800-53'
        }
        framework_results = query_manager.search_requirements(
            requirements=requirements,
            evidence_by_requirement=evidence_by_requirement,
            query=framework_query,
            page=1,
            page_size=10
        )
        logger.info(f"Found {framework_results['pagination']['total_results']} NIST SP 800-53 requirements")
        logger.info(f"First page results: {json.dumps([r['id'] for r in framework_results['results']], indent=2)}")
        
        # Example 10: Search requirements by text
        logger.info("\nExample 10: Search requirements by text")
        text_query = {
            'field': 'content',
            'value': 'password'
        }
        text_results = query_manager.search_requirements(
            requirements=requirements,
            evidence_by_requirement=evidence_by_requirement,
            query=text_query,
            page=1,
            page_size=10
        )
        logger.info(f"Found {text_results['pagination']['total_results']} requirements containing 'password'")
        logger.info(f"First page results: {json.dumps([r['id'] for r in text_results['results']], indent=2)}")
        
        # Example 11: Search requirements by evidence count
        logger.info("\nExample 11: Search requirements by evidence count")
        count_query = {
            'field': 'evidence_count',
            'value': {
                'min': 5
            }
        }
        count_results = query_manager.search_requirements(
            requirements=requirements,
            evidence_by_requirement=evidence_by_requirement,
            query=count_query,
            page=1,
            page_size=10
        )
        logger.info(f"Found {count_results['pagination']['total_results']} requirements with at least 5 evidence items")
        logger.info(f"First page results: {json.dumps([r['id'] for r in count_results['results']], indent=2)}")
        
    except Exception as e:
        logger.error(f"Error: {e}")

if __name__ == "__main__":
    main()
