version: '3.8'

services:
  nova-connect:
    build:
      context: ./nova-connect
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - PORT=3000
      - MONGODB_URI=mongodb://mongo:27017/nova-connect
    depends_on:
      - mongo
    volumes:
      - ./nova-connect:/app
      - /app/node_modules
    networks:
      - novafuse-network

  nova-grc-apis:
    build:
      context: ../nova-grc-apis
      dockerfile: Dockerfile
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=development
      - PORT=3001
      - MONGODB_URI=mongodb://mongo:27017/nova-grc-apis
    depends_on:
      - mongo
    volumes:
      - ../nova-grc-apis:/app
      - /app/node_modules
    networks:
      - novafuse-network

  mongo:
    image: mongo:4.4
    ports:
      - "27017:27017"
    volumes:
      - mongo-data:/data/db
    networks:
      - novafuse-network

  mongo-express:
    image: mongo-express
    ports:
      - "8081:8081"
    environment:
      - ME_CONFIG_MONGODB_SERVER=mongo
      - ME_CONFIG_MONGODB_PORT=27017
    depends_on:
      - mongo
    networks:
      - novafuse-network

  test-runner:
    build:
      context: ./nova-connect
      dockerfile: Dockerfile
    command: ["npm", "test"]
    environment:
      - NODE_ENV=test
      - PORT=3000
      - MONGODB_URI=mongodb://mongo:27017/nova-connect
    depends_on:
      - mongo
    volumes:
      - ./nova-connect:/app
      - /app/node_modules
    networks:
      - novafuse-network

networks:
  novafuse-network:
    driver: bridge

volumes:
  mongo-data:
