/**
 * NovaFuse Three-Tier Product Portfolio Management
 * 
 * This module manages the three-tier product portfolio:
 * 1. Physics Tier: Pure CSDE Direct Integration
 * 2. Transition Tier: Enhanced NovaConnect + CSDE Integration
 * 3. Legacy Tier: Traditional NovaConnect
 * 
 * It provides capabilities for:
 * - Product definition and configuration
 * - Feature management across tiers
 * - Migration paths between tiers
 * - Pricing and licensing
 */

const { v4: uuidv4 } = require('uuid');

class ProductPortfolio {
  /**
   * Create a new Product Portfolio
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    this.options = {
      enableAllTiers: true,
      defaultTier: 'transition',
      ...options
    };
    
    this.logger = options.logger || console;
    
    // Initialize product tiers
    this.tiers = {
      physics: {
        id: 'physics',
        name: 'Pure CSDE Direct Integration',
        description: 'Direct gRPC integration with sub-millisecond latency',
        features: new Map(),
        customers: new Map(),
        pricing: {
          base: 10000,
          perUser: 100,
          perEvent: 0.001
        }
      },
      transition: {
        id: 'transition',
        name: 'Enhanced NovaConnect + CSDE Integration',
        description: 'Hybrid integration with governance and visibility',
        features: new Map(),
        customers: new Map(),
        pricing: {
          base: 5000,
          perUser: 50,
          perEvent: 0.005
        }
      },
      legacy: {
        id: 'legacy',
        name: 'Traditional NovaConnect',
        description: 'REST API integration with existing systems',
        features: new Map(),
        customers: new Map(),
        pricing: {
          base: 2000,
          perUser: 20,
          perEvent: 0.01
        }
      }
    };
    
    // Initialize features
    this._initializeFeatures();
    
    // Initialize migration paths
    this.migrationPaths = {
      'legacy-to-transition': {
        sourceId: 'legacy',
        targetId: 'transition',
        steps: [
          {
            id: 'step1',
            name: 'Deploy Enhanced NovaConnect',
            description: 'Deploy the Enhanced NovaConnect with CSDE integration'
          },
          {
            id: 'step2',
            name: 'Configure Hybrid Mode',
            description: 'Configure the system to operate in hybrid mode'
          },
          {
            id: 'step3',
            name: 'Migrate Connectors',
            description: 'Migrate connectors to the Enhanced NovaConnect'
          }
        ]
      },
      'transition-to-physics': {
        sourceId: 'transition',
        targetId: 'physics',
        steps: [
          {
            id: 'step1',
            name: 'Deploy gRPC Endpoints',
            description: 'Deploy gRPC endpoints for direct CSDE integration'
          },
          {
            id: 'step2',
            name: 'Implement Wilson Loops',
            description: 'Implement Wilson loops for closed-loop validation'
          },
          {
            id: 'step3',
            name: 'Configure π10³ Remediation',
            description: 'Configure π10³ remediation scaling'
          }
        ]
      }
    };
    
    this.logger.info('Product Portfolio initialized');
  }
  
  /**
   * Initialize features for each tier
   * @private
   */
  _initializeFeatures() {
    // Physics Tier Features
    this._addFeature('physics', {
      id: 'direct-grpc',
      name: 'Direct gRPC Integration',
      description: 'Direct gRPC integration with sub-millisecond latency',
      category: 'integration'
    });
    
    this._addFeature('physics', {
      id: 'wilson-loops',
      name: 'Wilson Loop Enforcement',
      description: 'Closed-loop validation with Wilson loops',
      category: 'security'
    });
    
    this._addFeature('physics', {
      id: 'pi-remediation',
      name: 'π10³ Remediation Scaling',
      description: 'Remediation scaling with π10³ factor',
      category: 'remediation'
    });
    
    this._addFeature('physics', {
      id: 'event-throughput',
      name: '69,000 Events/Sec Throughput',
      description: 'Process up to 69,000 events per second',
      category: 'performance'
    });
    
    this._addFeature('physics', {
      id: 'sub-millisecond',
      name: 'Sub-Millisecond Latency',
      description: 'Process events with ≤0.07ms latency',
      category: 'performance'
    });
    
    // Transition Tier Features
    this._addFeature('transition', {
      id: 'hybrid-integration',
      name: 'Hybrid Integration',
      description: 'Hybrid integration with both gRPC and REST',
      category: 'integration'
    });
    
    this._addFeature('transition', {
      id: 'governance',
      name: 'Governance Layer',
      description: 'Governance layer for compliance and oversight',
      category: 'compliance'
    });
    
    this._addFeature('transition', {
      id: 'dashboard',
      name: 'NovaVision Dashboard',
      description: 'Visual dashboard for monitoring and reporting',
      category: 'ui'
    });
    
    this._addFeature('transition', {
      id: 'batch-processing',
      name: 'Batch Processing',
      description: 'Process data in batches for non-critical workloads',
      category: 'processing'
    });
    
    this._addFeature('transition', {
      id: 'compliance-mapping',
      name: 'Compliance Mapping',
      description: 'Map controls across different compliance frameworks',
      category: 'compliance'
    });
    
    // Legacy Tier Features
    this._addFeature('legacy', {
      id: 'rest-api',
      name: 'REST API Integration',
      description: 'Integration with REST APIs',
      category: 'integration'
    });
    
    this._addFeature('legacy', {
      id: 'file-upload',
      name: 'File Upload',
      description: 'Upload files for processing',
      category: 'processing'
    });
    
    this._addFeature('legacy', {
      id: 'ui-driven',
      name: 'UI-Driven Workflows',
      description: 'Workflows driven by user interface',
      category: 'ui'
    });
    
    this._addFeature('legacy', {
      id: 'connector-registry',
      name: 'Connector Registry',
      description: 'Registry of available connectors',
      category: 'integration'
    });
    
    this._addFeature('legacy', {
      id: 'basic-monitoring',
      name: 'Basic Monitoring',
      description: 'Basic monitoring of API performance and health',
      category: 'monitoring'
    });
  }
  
  /**
   * Add a feature to a tier
   * @param {string} tierId - Tier ID
   * @param {Object} feature - Feature definition
   * @private
   */
  _addFeature(tierId, feature) {
    if (!this.tiers[tierId]) {
      throw new Error(`Invalid tier ID: ${tierId}`);
    }
    
    this.tiers[tierId].features.set(feature.id, {
      ...feature,
      tierId
    });
  }
  
  /**
   * Get all tiers
   * @returns {Array} - Array of tier definitions
   */
  getTiers() {
    return Object.values(this.tiers).map(tier => ({
      id: tier.id,
      name: tier.name,
      description: tier.description,
      featureCount: tier.features.size,
      customerCount: tier.customers.size,
      pricing: { ...tier.pricing }
    }));
  }
  
  /**
   * Get a tier by ID
   * @param {string} tierId - Tier ID
   * @returns {Object} - Tier definition
   */
  getTier(tierId) {
    if (!this.tiers[tierId]) {
      throw new Error(`Invalid tier ID: ${tierId}`);
    }
    
    const tier = this.tiers[tierId];
    
    return {
      id: tier.id,
      name: tier.name,
      description: tier.description,
      features: Array.from(tier.features.values()),
      customerCount: tier.customers.size,
      pricing: { ...tier.pricing }
    };
  }
  
  /**
   * Get features for a tier
   * @param {string} tierId - Tier ID
   * @returns {Array} - Array of feature definitions
   */
  getTierFeatures(tierId) {
    if (!this.tiers[tierId]) {
      throw new Error(`Invalid tier ID: ${tierId}`);
    }
    
    return Array.from(this.tiers[tierId].features.values());
  }
  
  /**
   * Get migration paths
   * @returns {Array} - Array of migration path definitions
   */
  getMigrationPaths() {
    return Object.values(this.migrationPaths);
  }
  
  /**
   * Get a migration path
   * @param {string} sourceId - Source tier ID
   * @param {string} targetId - Target tier ID
   * @returns {Object} - Migration path definition
   */
  getMigrationPath(sourceId, targetId) {
    const pathId = `${sourceId}-to-${targetId}`;
    
    if (!this.migrationPaths[pathId]) {
      throw new Error(`Invalid migration path: ${pathId}`);
    }
    
    return this.migrationPaths[pathId];
  }
  
  /**
   * Register a customer
   * @param {string} tierId - Tier ID
   * @param {Object} customer - Customer definition
   * @returns {string} - Customer ID
   */
  registerCustomer(tierId, customer) {
    if (!this.tiers[tierId]) {
      throw new Error(`Invalid tier ID: ${tierId}`);
    }
    
    const customerId = customer.id || uuidv4();
    
    this.tiers[tierId].customers.set(customerId, {
      id: customerId,
      name: customer.name,
      email: customer.email,
      company: customer.company,
      registeredAt: new Date().toISOString(),
      ...customer
    });
    
    return customerId;
  }
  
  /**
   * Get a customer
   * @param {string} tierId - Tier ID
   * @param {string} customerId - Customer ID
   * @returns {Object} - Customer definition
   */
  getCustomer(tierId, customerId) {
    if (!this.tiers[tierId]) {
      throw new Error(`Invalid tier ID: ${tierId}`);
    }
    
    if (!this.tiers[tierId].customers.has(customerId)) {
      throw new Error(`Customer not found: ${customerId}`);
    }
    
    return this.tiers[tierId].customers.get(customerId);
  }
  
  /**
   * Get all customers for a tier
   * @param {string} tierId - Tier ID
   * @returns {Array} - Array of customer definitions
   */
  getTierCustomers(tierId) {
    if (!this.tiers[tierId]) {
      throw new Error(`Invalid tier ID: ${tierId}`);
    }
    
    return Array.from(this.tiers[tierId].customers.values());
  }
  
  /**
   * Calculate price for a customer
   * @param {string} tierId - Tier ID
   * @param {Object} parameters - Pricing parameters
   * @returns {Object} - Price calculation
   */
  calculatePrice(tierId, parameters) {
    if (!this.tiers[tierId]) {
      throw new Error(`Invalid tier ID: ${tierId}`);
    }
    
    const tier = this.tiers[tierId];
    const { users = 1, events = 0, addons = [] } = parameters;
    
    // Calculate base price
    const basePrice = tier.pricing.base;
    
    // Calculate user price
    const userPrice = users * tier.pricing.perUser;
    
    // Calculate event price
    const eventPrice = events * tier.pricing.perEvent;
    
    // Calculate addon price
    const addonPrice = addons.reduce((total, addon) => {
      return total + (addon.price || 0);
    }, 0);
    
    // Calculate total price
    const totalPrice = basePrice + userPrice + eventPrice + addonPrice;
    
    return {
      tierId,
      basePrice,
      userPrice,
      eventPrice,
      addonPrice,
      totalPrice,
      breakdown: {
        base: basePrice,
        users: {
          count: users,
          price: userPrice
        },
        events: {
          count: events,
          price: eventPrice
        },
        addons: {
          count: addons.length,
          price: addonPrice
        }
      }
    };
  }
  
  /**
   * Migrate a customer from one tier to another
   * @param {string} customerId - Customer ID
   * @param {string} sourceTierId - Source tier ID
   * @param {string} targetTierId - Target tier ID
   * @returns {Object} - Migration result
   */
  migrateCustomer(customerId, sourceTierId, targetTierId) {
    if (!this.tiers[sourceTierId]) {
      throw new Error(`Invalid source tier ID: ${sourceTierId}`);
    }
    
    if (!this.tiers[targetTierId]) {
      throw new Error(`Invalid target tier ID: ${targetTierId}`);
    }
    
    if (!this.tiers[sourceTierId].customers.has(customerId)) {
      throw new Error(`Customer not found in source tier: ${customerId}`);
    }
    
    // Get customer from source tier
    const customer = this.tiers[sourceTierId].customers.get(customerId);
    
    // Remove customer from source tier
    this.tiers[sourceTierId].customers.delete(customerId);
    
    // Add customer to target tier
    this.tiers[targetTierId].customers.set(customerId, {
      ...customer,
      tierId: targetTierId,
      migratedAt: new Date().toISOString(),
      previousTierId: sourceTierId
    });
    
    // Get migration path
    const migrationPath = this.getMigrationPath(sourceTierId, targetTierId);
    
    return {
      customerId,
      sourceTierId,
      targetTierId,
      migrationPath,
      migratedAt: new Date().toISOString()
    };
  }
}

module.exports = ProductPortfolio;
