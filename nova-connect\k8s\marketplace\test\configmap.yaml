apiVersion: v1
kind: ConfigMap
metadata:
  name: novafuse-uac-config
  namespace: novafuse-test
  labels:
    app: novafuse-uac
    component: api
data:
  config.json: |
    {
      "app": {
        "name": "NovaConnect UAC",
        "version": "1.0.0",
        "environment": "test"
      },
      "server": {
        "port": 3001,
        "host": "0.0.0.0"
      },
      "logging": {
        "level": "info",
        "format": "json",
        "colorize": false
      },
      "security": {
        "helmet": true,
        "csrf": true,
        "rateLimit": {
          "enabled": true,
          "windowMs": 60000,
          "max": 100
        }
      },
      "performance": {
        "cluster": true,
        "cache": true,
        "compression": true
      }
    }
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: novafuse-uac-feature-flags
  namespace: novafuse-test
  labels:
    app: novafuse-uac
    component: api
data:
  feature_flags.json: |
    {
      "core": {
        "basicAuth": true,
        "jwtAuth": true,
        "apiKeyAuth": true,
        "basicConnectors": true,
        "basicTransformations": true,
        "basicValidation": true,
        "basicErrorHandling": true,
        "basicLogging": true,
        "basicMetrics": true
      },
      "secure": {
        "oauth2Auth": true,
        "samlAuth": true,
        "advancedConnectors": true,
        "advancedTransformations": true,
        "advancedValidation": true,
        "advancedErrorHandling": true,
        "advancedLogging": true,
        "advancedMetrics": true,
        "encryption": true,
        "ipFiltering": true
      },
      "enterprise": {
        "customAuth": true,
        "customConnectors": true,
        "customTransformations": true,
        "customValidation": true,
        "customErrorHandling": true,
        "customLogging": true,
        "customMetrics": true,
        "highAvailability": true,
        "loadBalancing": true,
        "autoScaling": true
      },
      "ai_boost": {
        "aiAuth": true,
        "aiConnectors": true,
        "aiTransformations": true,
        "aiValidation": true,
        "aiErrorHandling": true,
        "aiLogging": true,
        "aiMetrics": true,
        "aiOptimization": true,
        "aiPrediction": true,
        "aiRecommendation": true
      }
    }
