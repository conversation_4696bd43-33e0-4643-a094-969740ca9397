/**
 * Regulatory Compliance Routes
 * 
 * This file defines the routes for regulatory compliance in the Privacy Management API.
 */

const express = require('express');
const router = express.Router();
const regulatoryComplianceController = require('../controllers/regulatoryComplianceController');
const { authenticate, hasPermission } = require('../middleware/authMiddleware');
const { validateRequest } = require('../middleware/validationMiddleware');
const { regulatoryComplianceValidation } = require('../validations');

// Get all regulatory frameworks
router.get(
  '/frameworks',
  validateRequest(regulatoryComplianceValidation.getAllFrameworks),
  regulatoryComplianceController.getAllFrameworks
);

// Get regulatory framework by ID
router.get(
  '/frameworks/:id',
  validateRequest(regulatoryComplianceValidation.getFrameworkById),
  regulatoryComplianceController.getFrameworkById
);

// Get regulatory framework by code
router.get(
  '/frameworks/code/:code',
  validateRequest(regulatoryComplianceValidation.getFrameworkByCode),
  regulatoryComplianceController.getFrameworkByCode
);

// Get compliance requirements for a framework
router.get(
  '/frameworks/:frameworkId/requirements',
  validateRequest(regulatoryComplianceValidation.getRequirementsByFramework),
  regulatoryComplianceController.getRequirementsByFramework
);

// Get compliance requirement by ID
router.get(
  '/requirements/:id',
  validateRequest(regulatoryComplianceValidation.getRequirementById),
  regulatoryComplianceController.getRequirementById
);

// Get compliance status for an entity
router.get(
  '/status/:entityType/:entityId/:frameworkId',
  authenticate,
  validateRequest(regulatoryComplianceValidation.getComplianceStatus),
  regulatoryComplianceController.getComplianceStatus
);

// Update compliance status for a requirement
router.patch(
  '/status/:statusId/requirements/:requirementId',
  authenticate,
  hasPermission(['privacy:compliance:update']),
  validateRequest(regulatoryComplianceValidation.updateRequirementStatus),
  regulatoryComplianceController.updateRequirementStatus
);

// Generate compliance report
router.get(
  '/reports/:entityType/:entityId/:frameworkId',
  authenticate,
  validateRequest(regulatoryComplianceValidation.generateComplianceReport),
  regulatoryComplianceController.generateComplianceReport
);

// Map requirements between frameworks
router.get(
  '/mapping/:sourceFrameworkId/:targetFrameworkId',
  validateRequest(regulatoryComplianceValidation.mapRequirementsBetweenFrameworks),
  regulatoryComplianceController.mapRequirementsBetweenFrameworks
);

// Get regulatory updates
router.get(
  '/updates',
  validateRequest(regulatoryComplianceValidation.getRegulatoryUpdates),
  regulatoryComplianceController.getRegulatoryUpdates
);

module.exports = router;
