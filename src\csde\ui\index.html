<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>CSDE Dashboard</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: #f8f9fa;
      padding-top: 20px;
    }
    .card {
      margin-bottom: 20px;
      border-radius: 10px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    .card-header {
      background-color: #0a84ff;
      color: white;
      font-weight: bold;
      border-radius: 10px 10px 0 0 !important;
    }
    .metric-value {
      font-size: 2.5rem;
      font-weight: bold;
      color: #0a84ff;
    }
    .metric-label {
      font-size: 1rem;
      color: #6c757d;
    }
    .progress {
      height: 10px;
      margin-bottom: 10px;
    }
    .action-card {
      border-left: 5px solid #0a84ff;
    }
    .critical {
      border-left-color: #dc3545;
    }
    .high {
      border-left-color: #fd7e14;
    }
    .medium {
      border-left-color: #ffc107;
    }
    .low {
      border-left-color: #20c997;
    }
    .step-list {
      padding-left: 20px;
    }
    .step-list li {
      margin-bottom: 5px;
    }
    .component-card {
      height: 100%;
    }
    .tensor-diagram {
      width: 100%;
      height: 200px;
      background-color: #f8f9fa;
      border: 1px solid #dee2e6;
      border-radius: 5px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 20px;
    }
    #loadingOverlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(255, 255, 255, 0.8);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 1000;
    }
    .spinner-border {
      width: 3rem;
      height: 3rem;
    }
  </style>
</head>
<body>
  <div id="loadingOverlay">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
  </div>

  <div class="container">
    <div class="row mb-4">
      <div class="col-12">
        <h1 class="text-center">Cyber-Safety Dominance Equation (CSDE) Dashboard</h1>
        <p class="text-center text-muted">CSDE = (N ⊗ G ⊕ C) × π10³</p>
      </div>
    </div>

    <div class="row mb-4">
      <div class="col-md-4">
        <div class="card">
          <div class="card-header">CSDE Value</div>
          <div class="card-body text-center">
            <div class="metric-value" id="csdeValue">-</div>
            <div class="metric-label">Cyber-Safety Dominance Score</div>
          </div>
        </div>
      </div>
      <div class="col-md-4">
        <div class="card">
          <div class="card-header">Performance Factor</div>
          <div class="card-body text-center">
            <div class="metric-value" id="performanceFactor">-</div>
            <div class="metric-label">Improvement over baseline</div>
          </div>
        </div>
      </div>
      <div class="col-md-4">
        <div class="card">
          <div class="card-header">Fusion Result</div>
          <div class="card-body text-center">
            <div class="metric-value" id="fusionValue">-</div>
            <div class="metric-label">Non-linear synergy value</div>
          </div>
        </div>
      </div>
    </div>

    <div class="row mb-4">
      <div class="col-md-4">
        <div class="card component-card">
          <div class="card-header">NIST Component (N)</div>
          <div class="card-body">
            <div class="metric-value" id="nistValue">-</div>
            <div class="metric-label">Compliance Score</div>
            <div class="progress mt-3">
              <div class="progress-bar bg-primary" id="nistProgress" role="progressbar" style="width: 0%"></div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-4">
        <div class="card component-card">
          <div class="card-header">GCP Component (G)</div>
          <div class="card-body">
            <div class="metric-value" id="gcpValue">-</div>
            <div class="metric-label">Integration Score</div>
            <div class="progress mt-3">
              <div class="progress-bar bg-success" id="gcpProgress" role="progressbar" style="width: 0%"></div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-4">
        <div class="card component-card">
          <div class="card-header">Cyber-Safety Component (C)</div>
          <div class="card-body">
            <div class="metric-value" id="cyberSafetyValue">-</div>
            <div class="metric-label">Safety Score</div>
            <div class="progress mt-3">
              <div class="progress-bar bg-info" id="cyberSafetyProgress" role="progressbar" style="width: 0%"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="row mb-4">
      <div class="col-12">
        <div class="card">
          <div class="card-header">Tensor Operations Visualization</div>
          <div class="card-body">
            <div class="tensor-diagram" id="tensorDiagram">
              <svg width="100%" height="100%" id="tensorSvg"></svg>
            </div>
            <div class="row">
              <div class="col-md-6">
                <h5>Tensor Product (N ⊗ G)</h5>
                <p>Value: <span id="tensorValue">-</span></p>
                <p>The tensor product enables multi-dimensional integration of compliance frameworks.</p>
              </div>
              <div class="col-md-6">
                <h5>Circular Trust Topology (π10³)</h5>
                <p>Value: <span id="circularTrustValue">-</span></p>
                <p>The circular trust topology forms a closed loop for comprehensive security.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="row mb-4">
      <div class="col-12">
        <div class="card">
          <div class="card-header">Remediation Actions</div>
          <div class="card-body">
            <div id="remediationActions">
              <!-- Remediation actions will be inserted here -->
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="row mb-4">
      <div class="col-12">
        <div class="card">
          <div class="card-header">Run CSDE Calculation</div>
          <div class="card-body">
            <form id="csdeForm">
              <div class="mb-3">
                <label for="complianceScore" class="form-label">Compliance Score (0-1)</label>
                <input type="range" class="form-range" min="0" max="1" step="0.01" id="complianceScore" value="0.75">
                <div class="d-flex justify-content-between">
                  <span>0</span>
                  <span id="complianceScoreValue">0.75</span>
                  <span>1</span>
                </div>
              </div>
              <div class="mb-3">
                <label for="gcpScore" class="form-label">GCP Integration Score (0-1)</label>
                <input type="range" class="form-range" min="0" max="1" step="0.01" id="gcpScore" value="0.85">
                <div class="d-flex justify-content-between">
                  <span>0</span>
                  <span id="gcpScoreValue">0.85</span>
                  <span>1</span>
                </div>
              </div>
              <div class="mb-3">
                <label for="cyberSafetyScore" class="form-label">Cyber-Safety Score (0-1)</label>
                <input type="range" class="form-range" min="0" max="1" step="0.01" id="cyberSafetyScore" value="0.65">
                <div class="d-flex justify-content-between">
                  <span>0</span>
                  <span id="cyberSafetyScoreValue">0.65</span>
                  <span>1</span>
                </div>
              </div>
              <button type="submit" class="btn btn-primary">Calculate CSDE</button>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
  <script src="https://d3js.org/d3.v7.min.js"></script>
  <script>
    // Sample data for initial display
    const sampleData = {
      csdeValue: 1417399.10,
      performanceFactor: 3142,
      nistComponent: { processedValue: 7.5 },
      gcpComponent: { processedValue: 8.5 },
      cyberSafetyComponent: { processedValue: 20.42 },
      tensorProduct: { normalizedValue: 255.0 },
      fusionResult: { fusionValue: 451.17 },
      remediationActions: [
        {
          title: "Remediate Account Management",
          type: "compliance",
          description: "Implement controls to address: The organization needs to implement account management procedures",
          priority: "critical",
          automationPotential: "high",
          estimatedEffort: "medium",
          steps: [
            "Review NIST 800-53 requirements for AC-2",
            "Develop implementation plan for Account Management",
            "Implement required controls",
            "Document evidence of implementation",
            "Verify effectiveness of controls"
          ]
        },
        {
          title: "Remediate Least Functionality",
          type: "compliance",
          description: "Implement controls to address: The organization needs to configure systems to provide only essential capabilities",
          priority: "high",
          automationPotential: "high",
          estimatedEffort: "low",
          steps: [
            "Review NIST 800-53 requirements for CM-7",
            "Develop implementation plan for Least Functionality",
            "Implement required controls",
            "Document evidence of implementation",
            "Verify effectiveness of controls"
          ]
        },
        {
          title: "Optimize IAM Role Configuration",
          type: "gcp",
          description: "Enhance GCP configuration: IAM roles need to be configured with least privilege",
          priority: "medium",
          automationPotential: "high",
          estimatedEffort: "medium",
          steps: [
            "Review current configuration of Cloud IAM",
            "Identify optimization opportunities for IAM Role Configuration",
            "Implement recommended configurations",
            "Test and validate changes",
            "Document updated configuration"
          ]
        }
      ]
    };

    // Function to update the dashboard with data
    function updateDashboard(data) {
      // Update metrics
      document.getElementById('csdeValue').textContent = data.csdeValue.toLocaleString(undefined, { maximumFractionDigits: 2 });
      document.getElementById('performanceFactor').textContent = data.performanceFactor + 'x';
      document.getElementById('fusionValue').textContent = data.fusionResult.fusionValue.toLocaleString(undefined, { maximumFractionDigits: 2 });
      
      // Update component values
      document.getElementById('nistValue').textContent = data.nistComponent.processedValue.toFixed(2);
      document.getElementById('gcpValue').textContent = data.gcpComponent.processedValue.toFixed(2);
      document.getElementById('cyberSafetyValue').textContent = data.cyberSafetyComponent.processedValue.toFixed(2);
      
      // Update progress bars
      document.getElementById('nistProgress').style.width = (data.nistComponent.processedValue / 10 * 100) + '%';
      document.getElementById('gcpProgress').style.width = (data.gcpComponent.processedValue / 10 * 100) + '%';
      document.getElementById('cyberSafetyProgress').style.width = (data.cyberSafetyComponent.processedValue / 31.42 * 100) + '%';
      
      // Update tensor values
      document.getElementById('tensorValue').textContent = data.tensorProduct.normalizedValue.toFixed(2);
      document.getElementById('circularTrustValue').textContent = '3,141.59';
      
      // Update remediation actions
      const actionsContainer = document.getElementById('remediationActions');
      actionsContainer.innerHTML = '';
      
      data.remediationActions.forEach((action, index) => {
        const actionCard = document.createElement('div');
        actionCard.className = `card action-card mb-3 ${action.priority}`;
        
        const actionHeader = document.createElement('div');
        actionHeader.className = 'card-header d-flex justify-content-between align-items-center';
        actionHeader.innerHTML = `
          <h5 class="mb-0">${index + 1}. ${action.title}</h5>
          <span class="badge bg-${getPriorityColor(action.priority)}">${action.priority.toUpperCase()}</span>
        `;
        
        const actionBody = document.createElement('div');
        actionBody.className = 'card-body';
        actionBody.innerHTML = `
          <p><strong>Type:</strong> ${action.type}</p>
          <p><strong>Description:</strong> ${action.description}</p>
          <div class="row">
            <div class="col-md-6">
              <p><strong>Automation Potential:</strong> ${action.automationPotential}</p>
            </div>
            <div class="col-md-6">
              <p><strong>Estimated Effort:</strong> ${action.estimatedEffort}</p>
            </div>
          </div>
          <p><strong>Steps:</strong></p>
          <ol class="step-list">
            ${action.steps.map(step => `<li>${step}</li>`).join('')}
          </ol>
        `;
        
        actionCard.appendChild(actionHeader);
        actionCard.appendChild(actionBody);
        actionsContainer.appendChild(actionCard);
      });
      
      // Draw tensor diagram
      drawTensorDiagram(data);
    }
    
    // Function to get priority color
    function getPriorityColor(priority) {
      switch (priority.toLowerCase()) {
        case 'critical': return 'danger';
        case 'high': return 'warning';
        case 'medium': return 'primary';
        case 'low': return 'success';
        default: return 'secondary';
      }
    }
    
    // Function to draw tensor diagram
    function drawTensorDiagram(data) {
      const svg = d3.select('#tensorSvg');
      svg.selectAll('*').remove();
      
      const width = svg.node().getBoundingClientRect().width;
      const height = svg.node().getBoundingClientRect().height;
      
      // Draw NIST circle
      svg.append('circle')
        .attr('cx', width / 4)
        .attr('cy', height / 2)
        .attr('r', 40)
        .attr('fill', '#0d6efd')
        .attr('opacity', 0.7);
      
      // Draw GCP circle
      svg.append('circle')
        .attr('cx', width / 2)
        .attr('cy', height / 2)
        .attr('r', 40)
        .attr('fill', '#198754')
        .attr('opacity', 0.7);
      
      // Draw Cyber-Safety circle
      svg.append('circle')
        .attr('cx', 3 * width / 4)
        .attr('cy', height / 2)
        .attr('r', 40)
        .attr('fill', '#0dcaf0')
        .attr('opacity', 0.7);
      
      // Draw tensor product line
      svg.append('line')
        .attr('x1', width / 4)
        .attr('y1', height / 2)
        .attr('x2', width / 2)
        .attr('y2', height / 2)
        .attr('stroke', '#000')
        .attr('stroke-width', 2)
        .attr('stroke-dasharray', '5,5');
      
      // Draw fusion line
      svg.append('line')
        .attr('x1', width / 2)
        .attr('y1', height / 2)
        .attr('x2', 3 * width / 4)
        .attr('y2', height / 2)
        .attr('stroke', '#000')
        .attr('stroke-width', 2)
        .attr('stroke-dasharray', '5,5');
      
      // Draw circular trust topology
      svg.append('circle')
        .attr('cx', width / 2)
        .attr('cy', height / 2)
        .attr('r', 100)
        .attr('fill', 'none')
        .attr('stroke', '#dc3545')
        .attr('stroke-width', 2)
        .attr('stroke-dasharray', '10,10');
      
      // Add labels
      svg.append('text')
        .attr('x', width / 4)
        .attr('y', height / 2 - 50)
        .attr('text-anchor', 'middle')
        .text('NIST (N)');
      
      svg.append('text')
        .attr('x', width / 2)
        .attr('y', height / 2 - 50)
        .attr('text-anchor', 'middle')
        .text('GCP (G)');
      
      svg.append('text')
        .attr('x', 3 * width / 4)
        .attr('y', height / 2 - 50)
        .attr('text-anchor', 'middle')
        .text('Cyber-Safety (C)');
      
      svg.append('text')
        .attr('x', width / 3)
        .attr('y', height / 2 - 20)
        .attr('text-anchor', 'middle')
        .text('⊗');
      
      svg.append('text')
        .attr('x', 2 * width / 3)
        .attr('y', height / 2 - 20)
        .attr('text-anchor', 'middle')
        .text('⊕');
      
      svg.append('text')
        .attr('x', width / 2)
        .attr('y', height - 20)
        .attr('text-anchor', 'middle')
        .text('π10³');
    }
    
    // Initialize the dashboard with sample data
    document.addEventListener('DOMContentLoaded', function() {
      // Hide loading overlay
      document.getElementById('loadingOverlay').style.display = 'none';
      
      // Update dashboard with sample data
      updateDashboard(sampleData);
      
      // Set up form event listeners
      document.getElementById('complianceScore').addEventListener('input', function() {
        document.getElementById('complianceScoreValue').textContent = this.value;
      });
      
      document.getElementById('gcpScore').addEventListener('input', function() {
        document.getElementById('gcpScoreValue').textContent = this.value;
      });
      
      document.getElementById('cyberSafetyScore').addEventListener('input', function() {
        document.getElementById('cyberSafetyScoreValue').textContent = this.value;
      });
      
      document.getElementById('csdeForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Show loading overlay
        document.getElementById('loadingOverlay').style.display = 'flex';
        
        // Get form values
        const complianceScore = parseFloat(document.getElementById('complianceScore').value);
        const gcpScore = parseFloat(document.getElementById('gcpScore').value);
        const cyberSafetyScore = parseFloat(document.getElementById('cyberSafetyScore').value);
        
        // Create data for calculation
        const complianceData = {
          complianceScore,
          controls: sampleData.remediationActions
            .filter(action => action.type === 'compliance')
            .map(action => ({
              id: action.title.split(' ').pop(),
              name: action.title.replace('Remediate ', ''),
              description: action.description.replace('Implement controls to address: ', ''),
              severity: action.priority,
              status: 'non-compliant',
              framework: 'NIST 800-53'
            }))
        };
        
        const gcpData = {
          integrationScore: gcpScore,
          services: sampleData.remediationActions
            .filter(action => action.type === 'gcp')
            .map(action => ({
              id: action.title.split(' ').join('-'),
              name: action.title.replace('Optimize ', ''),
              description: action.description.replace('Enhance GCP configuration: ', ''),
              severity: action.priority,
              status: 'non-optimal',
              service: 'Cloud ' + action.title.split(' ')[1]
            }))
        };
        
        const cyberSafetyData = {
          safetyScore: cyberSafetyScore,
          controls: sampleData.remediationActions
            .filter(action => action.type === 'cyber-safety')
            .map(action => ({
              id: action.title.split(' ').join('-'),
              name: action.title.replace('Implement ', ''),
              description: action.description.replace('Enhance Cyber-Safety: ', ''),
              severity: action.priority,
              status: 'not-implemented',
              pillar: 'Pillar ' + Math.floor(Math.random() * 12 + 1)
            }))
        };
        
        // Simulate API call
        setTimeout(() => {
          // Calculate new values
          const nistValue = complianceScore * 10;
          const gcpValue = gcpScore * 10;
          const cyberSafetyValue = cyberSafetyScore * 31.42;
          const tensorValue = nistValue * gcpValue * 3;
          const fusionValue = tensorValue + cyberSafetyValue * 1.618;
          const csdeValue = fusionValue * 3141.59;
          
          // Create result object
          const result = {
            csdeValue,
            performanceFactor: 3142,
            nistComponent: { processedValue: nistValue },
            gcpComponent: { processedValue: gcpValue },
            cyberSafetyComponent: { processedValue: cyberSafetyValue },
            tensorProduct: { normalizedValue: tensorValue },
            fusionResult: { fusionValue },
            remediationActions: sampleData.remediationActions
          };
          
          // Update dashboard
          updateDashboard(result);
          
          // Hide loading overlay
          document.getElementById('loadingOverlay').style.display = 'none';
        }, 1500);
      });
    });
  </script>
</body>
</html>
