<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NovaFuse Test Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            color: #212529;
        }
        .navbar {
            background-color: #0A84FF !important;
        }
        .card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            border: none;
        }
        .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            font-weight: 600;
        }
        .coverage-badge {
            font-size: 0.8rem;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-weight: 600;
        }
        .badge-success {
            background-color: #28a745;
            color: white;
        }
        .badge-warning {
            background-color: #ffc107;
            color: #212529;
        }
        .badge-danger {
            background-color: #dc3545;
            color: white;
        }
        .component-card:hover {
            transform: translateY(-5px);
            transition: transform 0.3s ease;
            cursor: pointer;
        }
        .test-status-icon {
            font-size: 1.5rem;
        }
        .test-status-passed {
            color: #28a745;
        }
        .test-status-failed {
            color: #dc3545;
        }
        .test-status-skipped {
            color: #ffc107;
        }
        .dashboard-header {
            background-color: #343a40;
            color: white;
            padding: 20px 0;
            margin-bottom: 30px;
        }
        .dashboard-title {
            font-weight: 700;
            margin-bottom: 0;
        }
        .dashboard-subtitle {
            opacity: 0.8;
            margin-bottom: 0;
        }
        .trend-indicator {
            font-size: 0.8rem;
            margin-left: 5px;
        }
        .trend-up {
            color: #28a745;
        }
        .trend-down {
            color: #dc3545;
        }
        .trend-neutral {
            color: #6c757d;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary mb-4">
        <div class="container">
            <a class="navbar-brand" href="#">
                <strong>NovaFuse</strong> Test Dashboard
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="#">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#">History</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#">Reports</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#">Settings</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container">
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h1 class="mb-0">Test Results Overview</h1>
                                <p class="text-muted">Last updated: <span id="lastUpdated">May 15, 2023 10:30 AM</span></p>
                            </div>
                            <div class="col-md-6 text-md-end">
                                <button class="btn btn-primary me-2">Run All Tests</button>
                                <button class="btn btn-outline-secondary">Generate Report</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title">Overall Coverage</h5>
                        <div class="d-flex justify-content-center">
                            <div class="position-relative" style="width: 120px; height: 120px;">
                                <canvas id="overallCoverageChart"></canvas>
                                <div class="position-absolute top-50 start-50 translate-middle">
                                    <h3 class="mb-0">83%</h3>
                                </div>
                            </div>
                        </div>
                        <p class="card-text text-success mt-2">
                            <i class="bi bi-arrow-up-short"></i> +2% from last run
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title">Tests Passed</h5>
                        <div class="d-flex justify-content-center">
                            <div class="position-relative" style="width: 120px; height: 120px;">
                                <canvas id="testsPassedChart"></canvas>
                                <div class="position-absolute top-50 start-50 translate-middle">
                                    <h3 class="mb-0">95%</h3>
                                </div>
                            </div>
                        </div>
                        <p class="card-text text-success mt-2">
                            <i class="bi bi-arrow-up-short"></i> 342/360 tests
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title">Components Tested</h5>
                        <div class="d-flex justify-content-center">
                            <div class="position-relative" style="width: 120px; height: 120px;">
                                <canvas id="componentsTestedChart"></canvas>
                                <div class="position-absolute top-50 start-50 translate-middle">
                                    <h3 class="mb-0">9/13</h3>
                                </div>
                            </div>
                        </div>
                        <p class="card-text text-warning mt-2">
                            <i class="bi bi-dash"></i> 4 components need tests
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title">Test Duration</h5>
                        <div class="d-flex justify-content-center">
                            <div class="position-relative" style="width: 120px; height: 120px;">
                                <canvas id="testDurationChart"></canvas>
                                <div class="position-absolute top-50 start-50 translate-middle">
                                    <h3 class="mb-0">4:32</h3>
                                </div>
                            </div>
                        </div>
                        <p class="card-text text-danger mt-2">
                            <i class="bi bi-arrow-up-short"></i> +0:45 from last run
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Component Coverage</h5>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-sm btn-outline-secondary active">All</button>
                            <button type="button" class="btn btn-sm btn-outline-secondary">Critical</button>
                            <button type="button" class="btn btn-sm btn-outline-secondary">Core</button>
                            <button type="button" class="btn btn-sm btn-outline-secondary">UI</button>
                        </div>
                    </div>
                    <div class="card-body">
                        <canvas id="componentCoverageChart" height="250"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Test Types Distribution</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="testTypesChart" height="250"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Component Status</h5>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="showOnlyFailing">
                            <label class="form-check-label" for="showOnlyFailing">Show only failing</label>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Component</th>
                                        <th>Coverage</th>
                                        <th>Tests</th>
                                        <th>Status</th>
                                        <th>Last Run</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>NovaTrack (NUCTO)</td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="progress flex-grow-1" style="height: 8px;">
                                                    <div class="progress-bar bg-success" role="progressbar" style="width: 87%"></div>
                                                </div>
                                                <span class="ms-2">87%</span>
                                            </div>
                                        </td>
                                        <td>42/45 passed</td>
                                        <td><span class="badge bg-success">Passing</span></td>
                                        <td>10 minutes ago</td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-primary">Run</button>
                                            <button class="btn btn-sm btn-outline-secondary">Details</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>NovaConnect (NUAC)</td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="progress flex-grow-1" style="height: 8px;">
                                                    <div class="progress-bar bg-success" role="progressbar" style="width: 92%"></div>
                                                </div>
                                                <span class="ms-2">92%</span>
                                            </div>
                                        </td>
                                        <td>78/78 passed</td>
                                        <td><span class="badge bg-success">Passing</span></td>
                                        <td>25 minutes ago</td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-primary">Run</button>
                                            <button class="btn btn-sm btn-outline-secondary">Details</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>NovaProof (NUCE)</td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="progress flex-grow-1" style="height: 8px;">
                                                    <div class="progress-bar bg-warning" role="progressbar" style="width: 76%"></div>
                                                </div>
                                                <span class="ms-2">76%</span>
                                            </div>
                                        </td>
                                        <td>35/38 passed</td>
                                        <td><span class="badge bg-warning">Partial</span></td>
                                        <td>1 hour ago</td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-primary">Run</button>
                                            <button class="btn btn-sm btn-outline-secondary">Details</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>NovaThink (NUCI)</td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="progress flex-grow-1" style="height: 8px;">
                                                    <div class="progress-bar bg-danger" role="progressbar" style="width: 65%"></div>
                                                </div>
                                                <span class="ms-2">65%</span>
                                            </div>
                                        </td>
                                        <td>28/35 passed</td>
                                        <td><span class="badge bg-danger">Failing</span></td>
                                        <td>2 hours ago</td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-primary">Run</button>
                                            <button class="btn btn-sm btn-outline-secondary">Details</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>NovaVision (NUUI)</td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="progress flex-grow-1" style="height: 8px;">
                                                    <div class="progress-bar bg-success" role="progressbar" style="width: 88%"></div>
                                                </div>
                                                <span class="ms-2">88%</span>
                                            </div>
                                        </td>
                                        <td>65/67 passed</td>
                                        <td><span class="badge bg-success">Passing</span></td>
                                        <td>3 hours ago</td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-primary">Run</button>
                                            <button class="btn btn-sm btn-outline-secondary">Details</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Recent Test Runs</h5>
                    </div>
                    <div class="card-body">
                        <div class="list-group">
                            <a href="#" class="list-group-item list-group-item-action">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">Full Test Suite</h6>
                                    <small>10 minutes ago</small>
                                </div>
                                <p class="mb-1">342/360 tests passed (95%)</p>
                                <small class="text-success">Coverage: 83% (+2%)</small>
                            </a>
                            <a href="#" class="list-group-item list-group-item-action">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">NovaTrack Tests</h6>
                                    <small>1 hour ago</small>
                                </div>
                                <p class="mb-1">42/45 tests passed (93%)</p>
                                <small class="text-success">Coverage: 87% (+5%)</small>
                            </a>
                            <a href="#" class="list-group-item list-group-item-action">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">NovaThink Tests</h6>
                                    <small>2 hours ago</small>
                                </div>
                                <p class="mb-1">28/35 tests passed (80%)</p>
                                <small class="text-danger">Coverage: 65% (-3%)</small>
                            </a>
                            <a href="#" class="list-group-item list-group-item-action">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">NovaProof Tests</h6>
                                    <small>3 hours ago</small>
                                </div>
                                <p class="mb-1">35/38 tests passed (92%)</p>
                                <small class="text-warning">Coverage: 76% (no change)</small>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Test Coverage Trends</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="coverageTrendChart" height="250"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <footer class="bg-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p class="mb-0">NovaFuse Universal Platform &copy; 2023</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">Test Dashboard v1.0.0</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>
    <script>
        // Overall Coverage Chart
        const overallCoverageChart = new Chart(
            document.getElementById('overallCoverageChart'),
            {
                type: 'doughnut',
                data: {
                    labels: ['Covered', 'Not Covered'],
                    datasets: [{
                        data: [83, 17],
                        backgroundColor: ['#28a745', '#e9ecef'],
                        borderWidth: 0
                    }]
                },
                options: {
                    cutout: '75%',
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            enabled: false
                        }
                    },
                    responsive: true,
                    maintainAspectRatio: false
                }
            }
        );

        // Tests Passed Chart
        const testsPassedChart = new Chart(
            document.getElementById('testsPassedChart'),
            {
                type: 'doughnut',
                data: {
                    labels: ['Passed', 'Failed'],
                    datasets: [{
                        data: [95, 5],
                        backgroundColor: ['#28a745', '#dc3545'],
                        borderWidth: 0
                    }]
                },
                options: {
                    cutout: '75%',
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            enabled: false
                        }
                    },
                    responsive: true,
                    maintainAspectRatio: false
                }
            }
        );

        // Components Tested Chart
        const componentsTestedChart = new Chart(
            document.getElementById('componentsTestedChart'),
            {
                type: 'doughnut',
                data: {
                    labels: ['Tested', 'Not Tested'],
                    datasets: [{
                        data: [9, 4],
                        backgroundColor: ['#0A84FF', '#e9ecef'],
                        borderWidth: 0
                    }]
                },
                options: {
                    cutout: '75%',
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            enabled: false
                        }
                    },
                    responsive: true,
                    maintainAspectRatio: false
                }
            }
        );

        // Test Duration Chart
        const testDurationChart = new Chart(
            document.getElementById('testDurationChart'),
            {
                type: 'doughnut',
                data: {
                    labels: ['Duration'],
                    datasets: [{
                        data: [100],
                        backgroundColor: ['#6f42c1'],
                        borderWidth: 0
                    }]
                },
                options: {
                    cutout: '75%',
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            enabled: false
                        }
                    },
                    responsive: true,
                    maintainAspectRatio: false
                }
            }
        );

        // Component Coverage Chart
        const componentCoverageChart = new Chart(
            document.getElementById('componentCoverageChart'),
            {
                type: 'bar',
                data: {
                    labels: ['NovaTrack', 'NovaConnect', 'NovaProof', 'NovaThink', 'NovaVision', 'NovaFlowX', 'NovaPulse+', 'NovaDNA', 'NovaStore'],
                    datasets: [
                        {
                            label: 'Current Coverage',
                            data: [87, 92, 76, 65, 88, 83, 79, 85, 72],
                            backgroundColor: '#0A84FF',
                            borderWidth: 0
                        },
                        {
                            label: 'Target (81%)',
                            data: [81, 81, 81, 81, 81, 81, 81, 81, 81],
                            type: 'line',
                            borderColor: '#dc3545',
                            borderWidth: 2,
                            borderDash: [5, 5],
                            fill: false,
                            pointRadius: 0
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top'
                        }
                    }
                }
            }
        );

        // Test Types Chart
        const testTypesChart = new Chart(
            document.getElementById('testTypesChart'),
            {
                type: 'doughnut',
                data: {
                    labels: ['Unit Tests', 'Integration Tests', 'API Tests', 'UI Tests', 'E2E Tests'],
                    datasets: [{
                        data: [45, 25, 15, 10, 5],
                        backgroundColor: ['#0A84FF', '#28a745', '#ffc107', '#6f42c1', '#fd7e14'],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            }
        );

        // Coverage Trend Chart
        const coverageTrendChart = new Chart(
            document.getElementById('coverageTrendChart'),
            {
                type: 'line',
                data: {
                    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May'],
                    datasets: [
                        {
                            label: 'Overall Coverage',
                            data: [65, 68, 72, 78, 83],
                            borderColor: '#0A84FF',
                            backgroundColor: 'rgba(10, 132, 255, 0.1)',
                            fill: true,
                            tension: 0.4
                        },
                        {
                            label: 'Target (81%)',
                            data: [81, 81, 81, 81, 81],
                            borderColor: '#dc3545',
                            borderWidth: 2,
                            borderDash: [5, 5],
                            fill: false,
                            pointRadius: 0
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: false,
                            min: 60,
                            max: 100,
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top'
                        }
                    }
                }
            }
        );
    </script>
</body>
</html>
