const express = require('express');
const router = express.Router();
const { authenticateJWT } = require('../middleware/auth');

// Import controllers
const dashboardController = require('../controllers/dashboardController');

/**
 * @route GET /api/dashboard/connectors
 * @desc Get all installed connectors for the authenticated user
 * @access Private
 */
router.get('/connectors', authenticateJWT, dashboardController.getInstalledConnectors);

/**
 * @route GET /api/dashboard/connectors/:id
 * @desc Get details of an installed connector
 * @access Private
 */
router.get('/connectors/:id', authenticateJWT, dashboardController.getInstalledConnectorDetails);

/**
 * @route PUT /api/dashboard/connectors/:id/status
 * @desc Update the status of an installed connector (active/inactive)
 * @access Private
 */
router.put('/connectors/:id/status', authenticateJWT, dashboardController.updateConnectorStatus);

/**
 * @route GET /api/dashboard/statistics
 * @desc Get usage statistics for the authenticated user
 * @access Private
 */
router.get('/statistics', authenticateJWT, dashboardController.getUsageStatistics);

/**
 * @route GET /api/dashboard/compliance
 * @desc Get compliance status for the authenticated user
 * @access Private
 */
router.get('/compliance', authenticateJWT, dashboardController.getComplianceStatus);

/**
 * @route GET /api/dashboard/compliance/:framework
 * @desc Get detailed compliance status for a specific framework
 * @access Private
 */
router.get('/compliance/:framework', authenticateJWT, dashboardController.getFrameworkComplianceDetails);

/**
 * @route GET /api/dashboard/notifications
 * @desc Get notifications for the authenticated user
 * @access Private
 */
router.get('/notifications', authenticateJWT, dashboardController.getNotifications);

/**
 * @route PUT /api/dashboard/notifications/:id
 * @desc Mark a notification as read
 * @access Private
 */
router.put('/notifications/:id', authenticateJWT, dashboardController.markNotificationAsRead);

/**
 * @route DELETE /api/dashboard/notifications/:id
 * @desc Delete a notification
 * @access Private
 */
router.delete('/notifications/:id', authenticateJWT, dashboardController.deleteNotification);

module.exports = router;
