"""
Test Result Subscriber for the Universal Compliance Visualization Framework.

This module provides functionality for subscribing to test result events from UCTF.
"""

import logging
from typing import Dict, List, Any, Optional, Callable

from shared.events.event_bus import event_bus
from shared.models.test_result import TestResult

from .visualization_engine import VisualizationEngine

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class TestResultSubscriber:
    """
    Subscriber for test result events.

    This class subscribes to test result events from UCTF and processes them
    for visualization in UCVF.
    """

    def __init__(self, visualization_engine: Optional[VisualizationEngine] = None):
        """
        Initialize the Test Result Subscriber.

        Args:
            visualization_engine: The visualization engine to use for generating visualizations
        """
        logger.info("Initializing Test Result Subscriber")

        # Set the visualization engine
        self.visualization_engine = visualization_engine or VisualizationEngine()

        # Dictionary to store test result handlers
        # Structure: {event_type: [handler1, handler2, ...]}
        self.handlers: Dict[str, List[Callable]] = {}

        # Register default handlers
        self._register_default_handlers()

        # Subscribe to test result events
        self._subscribe_to_events()

        logger.info("Test Result Subscriber initialized")

    def _register_default_handlers(self) -> None:
        """Register default handlers for test result events."""
        # Register handler for test completed events
        self.register_handler('uctf.test_completed', self._handle_test_completed)

        # Register handler for test result stored events
        self.register_handler('uctf.result_stored', self._handle_result_stored)

    def _subscribe_to_events(self) -> None:
        """Subscribe to test result events."""
        # Subscribe to test completed events
        event_bus.register_handler('uctf.test_completed', self._dispatch_event)

        # Subscribe to test result stored events
        event_bus.register_handler('uctf.result_stored', self._dispatch_event)

    def register_handler(self, event_type: str, handler: Callable) -> None:
        """
        Register a handler for a specific event type.

        Args:
            event_type: The type of event
            handler: The handler function
        """
        if event_type not in self.handlers:
            self.handlers[event_type] = []

        self.handlers[event_type].append(handler)
        logger.info(f"Registered handler for event type: {event_type}")

    def unregister_handler(self, event_type: str, handler: Callable) -> None:
        """
        Unregister a handler for a specific event type.

        Args:
            event_type: The type of event
            handler: The handler function
        """
        if event_type in self.handlers and handler in self.handlers[event_type]:
            self.handlers[event_type].remove(handler)
            logger.info(f"Unregistered handler for event type: {event_type}")

    def _dispatch_event(self, event_data: Dict[str, Any]) -> None:
        """
        Dispatch an event to registered handlers.

        Args:
            event_data: Data associated with the event
        """
        event_type = event_data.get('event_type', '')

        logger.debug(f"Dispatching event: {event_type}")

        if event_type in self.handlers:
            for handler in self.handlers[event_type]:
                try:
                    handler(event_data)
                except Exception as e:
                    logger.error(f"Error in event handler for {event_type}: {e}")

    def _handle_test_completed(self, event_data: Dict[str, Any]) -> None:
        """
        Handle a test completed event.

        Args:
            event_data: Data associated with the event
        """
        logger.info(f"Handling test completed event: {event_data.get('id', 'unknown')}")

        # Extract test result data
        test_run = event_data
        result = test_run.get('result', {})

        if not result:
            logger.warning("Test result is empty, skipping visualization")
            return

        # Create a TestResult object
        test_result = TestResult(
            test_id=result.get('test_id', ''),
            name=result.get('name', ''),
            framework=result.get('framework', ''),
            status='passed' if result.get('passed', False) else 'failed',
            score=result.get('score', 0.0),
            findings=result.get('findings', []),
            metadata=result.get('metadata', {}),
            result_id=test_run.get('id', '')
        )

        # Generate visualizations
        self._generate_visualizations(test_result)

    def _handle_result_stored(self, event_data: Dict[str, Any]) -> None:
        """
        Handle a result stored event.

        Args:
            event_data: Data associated with the event
        """
        logger.info(f"Handling result stored event: {event_data.get('run_id', 'unknown')}")

        # Extract test result data
        run_id = event_data.get('run_id', '')
        test_result_data = event_data.get('test_result', {})

        if not test_result_data:
            logger.warning("Test result data is empty, skipping visualization")
            return

        # Create a TestResult object
        test_result = TestResult.from_dict(test_result_data)

        # Generate visualizations
        self._generate_visualizations(test_result)

    def _generate_visualizations(self, test_result: TestResult) -> None:
        """
        Generate visualizations for a test result.

        Args:
            test_result: The test result
        """
        logger.info(f"Generating visualizations for test result: {test_result.result_id}")

        try:
            # Generate a dashboard visualization
            dashboard = self.visualization_engine.generate_visualization(
                data=test_result.to_dict(),
                role='test_result',
                visualization_type='dashboard'
            )

            # Generate a report visualization
            report = self.visualization_engine.generate_visualization(
                data=test_result.to_dict(),
                role='test_result',
                visualization_type='report'
            )

            # Store the visualizations (in a real implementation, this would save to a database or file)
            # For now, we just log that they were generated
            logger.info(f"Generated dashboard visualization for test result: {test_result.result_id}")
            logger.info(f"Generated report visualization for test result: {test_result.result_id}")

        except Exception as e:
            logger.error(f"Error generating visualizations for test result {test_result.result_id}: {e}")


# Create a global instance of the TestResultSubscriber
test_result_subscriber = None


def initialize_test_result_subscriber(visualization_engine: Optional[VisualizationEngine] = None) -> TestResultSubscriber:
    """
    Initialize the Test Result Subscriber.

    Args:
        visualization_engine: The visualization engine to use for generating visualizations

    Returns:
        The initialized Test Result Subscriber
    """
    global test_result_subscriber

    if test_result_subscriber is None:
        test_result_subscriber = TestResultSubscriber(visualization_engine)

    return test_result_subscriber