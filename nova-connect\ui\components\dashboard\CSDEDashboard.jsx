import React, { useState, useEffect, useCallback } from 'react';
import { 
  Box, 
  Container, 
  Grid, 
  Paper, 
  Typography, 
  CircularProgress,
  Card,
  CardHeader,
  CardContent,
  Divider,
  Button,
  IconButton,
  Tooltip,
  useTheme
} from '@mui/material';
import RefreshIcon from '@mui/icons-material/Refresh';
import SettingsIcon from '@mui/icons-material/Settings';
import FullscreenIcon from '@mui/icons-material/Fullscreen';
import DownloadIcon from '@mui/icons-material/Download';
import InfoIcon from '@mui/icons-material/Info';

// Import visualization components
import CSEDMetricsGauge from './CSEDMetricsGauge';
import OfflineProcessingChart from './OfflineProcessingChart';
import CrossDomainPredictionChart from './CrossDomainPredictionChart';
import ComplianceMappingChart from './ComplianceMappingChart';
import PerformanceMetricsChart from './PerformanceMetricsChart';
import HealthStatusChart from './HealthStatusChart';
import CSEDTrinityVisualization from './CSEDTrinityVisualization';

// Import API service
import { fetchDashboardData, fetchMetrics, fetchHealth } from '../../services/csdeService';

/**
 * CSDE Dashboard Component
 * 
 * Displays a comprehensive dashboard for the CSDE Advanced Integration,
 * including metrics, charts, and visualizations.
 */
const CSDEDashboard = () => {
  const theme = useTheme();
  
  // State for dashboard data
  const [dashboardData, setDashboardData] = useState(null);
  const [metrics, setMetrics] = useState(null);
  const [health, setHealth] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [refreshInterval, setRefreshInterval] = useState(30000); // 30 seconds
  const [lastUpdated, setLastUpdated] = useState(null);
  
  // Fetch dashboard data
  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      
      // Fetch dashboard data, metrics, and health in parallel
      const [dashboardResponse, metricsResponse, healthResponse] = await Promise.all([
        fetchDashboardData(),
        fetchMetrics(),
        fetchHealth()
      ]);
      
      setDashboardData(dashboardResponse.data);
      setMetrics(metricsResponse.metrics);
      setHealth(healthResponse.health);
      setLastUpdated(new Date());
      setError(null);
    } catch (err) {
      console.error('Error fetching dashboard data:', err);
      setError('Failed to fetch dashboard data. Please try again later.');
    } finally {
      setLoading(false);
    }
  }, []);
  
  // Set up automatic refresh
  useEffect(() => {
    fetchData();
    
    // Set up interval for automatic refresh
    const intervalId = setInterval(fetchData, refreshInterval);
    
    // Clean up interval on component unmount
    return () => clearInterval(intervalId);
  }, [fetchData, refreshInterval]);
  
  // Handle manual refresh
  const handleRefresh = () => {
    fetchData();
  };
  
  // Format last updated time
  const formatLastUpdated = () => {
    if (!lastUpdated) return 'Never';
    
    return lastUpdated.toLocaleTimeString();
  };
  
  // Render loading state
  if (loading && !dashboardData) {
    return (
      <Box 
        sx={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center', 
          height: '100vh' 
        }}
      >
        <CircularProgress />
        <Typography variant="h6" sx={{ ml: 2 }}>
          Loading CSDE Dashboard...
        </Typography>
      </Box>
    );
  }
  
  // Render error state
  if (error && !dashboardData) {
    return (
      <Box 
        sx={{ 
          display: 'flex', 
          flexDirection: 'column',
          justifyContent: 'center', 
          alignItems: 'center', 
          height: '100vh' 
        }}
      >
        <Typography variant="h6" color="error" gutterBottom>
          {error}
        </Typography>
        <Button 
          variant="contained" 
          onClick={handleRefresh}
          startIcon={<RefreshIcon />}
        >
          Retry
        </Button>
      </Box>
    );
  }
  
  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
      {/* Dashboard Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
        <Box>
          <Typography variant="h4" gutterBottom>
            CSDE Advanced Dashboard
          </Typography>
          <Typography variant="subtitle1" color="text.secondary">
            Real-time monitoring of CSDE Advanced Integration
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Typography variant="body2" color="text.secondary" sx={{ mr: 2 }}>
            Last updated: {formatLastUpdated()}
          </Typography>
          <Tooltip title="Refresh Dashboard">
            <IconButton onClick={handleRefresh} disabled={loading}>
              <RefreshIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title="Dashboard Settings">
            <IconButton>
              <SettingsIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>
      
      {/* Health Status */}
      {health && (
        <Paper 
          sx={{ 
            p: 2, 
            mb: 3, 
            display: 'flex', 
            justifyContent: 'space-between',
            alignItems: 'center',
            bgcolor: health.status === 'healthy' 
              ? 'success.light' 
              : health.status === 'degraded' 
                ? 'warning.light' 
                : 'error.light'
          }}
        >
          <Typography variant="h6">
            System Status: {health.status.toUpperCase()}
          </Typography>
          <Typography variant="body2">
            Success Rate: {(health.metrics.overallSuccessRate * 100).toFixed(2)}%
          </Typography>
        </Paper>
      )}
      
      {/* Main Dashboard Content */}
      <Grid container spacing={3}>
        {/* CSDE Metrics Gauge */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 2, display: 'flex', flexDirection: 'column', height: 240 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
              <Typography variant="h6">CSDE Performance</Typography>
              <Tooltip title="CSDE Performance Metrics">
                <IconButton size="small">
                  <InfoIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Box>
            <Box sx={{ flexGrow: 1, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
              {metrics ? (
                <CSEDMetricsGauge 
                  metrics={metrics} 
                  size={180}
                />
              ) : (
                <CircularProgress />
              )}
            </Box>
          </Paper>
        </Grid>
        
        {/* Offline Processing Chart */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 2, display: 'flex', flexDirection: 'column', height: 240 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
              <Typography variant="h6">Offline Processing</Typography>
              <Tooltip title="Offline Processing Metrics">
                <IconButton size="small">
                  <InfoIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Box>
            <Box sx={{ flexGrow: 1 }}>
              {metrics ? (
                <OfflineProcessingChart 
                  data={dashboardData?.offlineProcessing} 
                  metrics={metrics.offlineProcessing}
                />
              ) : (
                <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                  <CircularProgress />
                </Box>
              )}
            </Box>
          </Paper>
        </Grid>
        
        {/* Cross-Domain Prediction Chart */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, display: 'flex', flexDirection: 'column', height: 300 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
              <Typography variant="h6">Cross-Domain Prediction</Typography>
              <Tooltip title="Cross-Domain Prediction Metrics">
                <IconButton size="small">
                  <InfoIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Box>
            <Box sx={{ flexGrow: 1 }}>
              {metrics ? (
                <CrossDomainPredictionChart 
                  data={dashboardData?.crossDomainPrediction} 
                  metrics={metrics.crossDomainPrediction}
                />
              ) : (
                <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                  <CircularProgress />
                </Box>
              )}
            </Box>
          </Paper>
        </Grid>
        
        {/* Compliance Mapping Chart */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, display: 'flex', flexDirection: 'column', height: 300 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
              <Typography variant="h6">Compliance Mapping</Typography>
              <Tooltip title="Compliance Mapping Metrics">
                <IconButton size="small">
                  <InfoIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Box>
            <Box sx={{ flexGrow: 1 }}>
              {metrics ? (
                <ComplianceMappingChart 
                  data={dashboardData?.complianceMapping} 
                  metrics={metrics.complianceMapping}
                />
              ) : (
                <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                  <CircularProgress />
                </Box>
              )}
            </Box>
          </Paper>
        </Grid>
        
        {/* CSDE Trinity Visualization */}
        <Grid item xs={12}>
          <Paper sx={{ p: 2, display: 'flex', flexDirection: 'column', height: 400 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
              <Typography variant="h6">CSDE Trinity Visualization</Typography>
              <Box>
                <Tooltip title="View Fullscreen">
                  <IconButton size="small">
                    <FullscreenIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
                <Tooltip title="Download Visualization">
                  <IconButton size="small">
                    <DownloadIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
                <Tooltip title="Trinity Visualization Info">
                  <IconButton size="small">
                    <InfoIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Box>
            </Box>
            <Box sx={{ flexGrow: 1 }}>
              {dashboardData ? (
                <CSEDTrinityVisualization 
                  data={dashboardData.trinityVisualization}
                />
              ) : (
                <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                  <CircularProgress />
                </Box>
              )}
            </Box>
          </Paper>
        </Grid>
        
        {/* Performance Metrics Chart */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, display: 'flex', flexDirection: 'column', height: 300 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
              <Typography variant="h6">Performance Metrics</Typography>
              <Tooltip title="Performance Metrics Info">
                <IconButton size="small">
                  <InfoIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Box>
            <Box sx={{ flexGrow: 1 }}>
              {metrics ? (
                <PerformanceMetricsChart 
                  metrics={metrics}
                />
              ) : (
                <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                  <CircularProgress />
                </Box>
              )}
            </Box>
          </Paper>
        </Grid>
        
        {/* Health Status Chart */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, display: 'flex', flexDirection: 'column', height: 300 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
              <Typography variant="h6">Health Status</Typography>
              <Tooltip title="Health Status Info">
                <IconButton size="small">
                  <InfoIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Box>
            <Box sx={{ flexGrow: 1 }}>
              {health ? (
                <HealthStatusChart 
                  health={health}
                />
              ) : (
                <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                  <CircularProgress />
                </Box>
              )}
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
};

export default CSDEDashboard;
