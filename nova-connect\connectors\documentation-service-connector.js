/**
 * NovaFuse Documentation Service Connector
 * Integrates document search, indexing, and content serving with NovaConnect
 * Enables real-time document discovery, search, and content management
 */

const fs = require('fs').promises;
const path = require('path');
const EventEmitter = require('events');

class DocumentationServiceConnector extends EventEmitter {
    constructor(novaConnect, options = {}) {
        super();
        
        this.novaConnect = novaConnect;
        this.options = {
            docsRootPath: options.docsRootPath || './imported-docs',
            indexingEnabled: options.indexingEnabled !== false,
            maxSearchResults: options.maxSearchResults || 50,
            enableRealTimeUpdates: options.enableRealTimeUpdates !== false,
            supportedExtensions: options.supportedExtensions || ['.md', '.txt', '.json', '.py', '.js'],
            ...options
        };
        
        // Document categories and their configurations
        this.documentCategories = {
            core: {
                name: 'Core Platform Documentation',
                description: 'NovaFuse core platform and API documentation',
                patterns: ['**/core/**', '**/api/**', '**/platform/**'],
                priority: 1
            },
            coherence: {
                name: 'Coherence Systems',
                description: 'Coherence-native computing and consciousness technology',
                patterns: ['**/coherence/**', '**/consciousness/**', '**/neri/**', '**/nece/**'],
                priority: 1
            },
            financial: {
                name: 'Financial Systems',
                description: 'NovaFinX, trading systems, and financial technology',
                patterns: ['**/financial/**', '**/novafinx/**', '**/trading/**'],
                priority: 2
            },
            medical: {
                name: 'Medical & Healthcare',
                description: 'NovaFold, medical applications, and healthcare systems',
                patterns: ['**/medical/**', '**/novafold/**', '**/healthcare/**'],
                priority: 2
            },
            blockchain: {
                name: 'Blockchain & Security',
                description: 'KetherNet, security systems, and blockchain technology',
                patterns: ['**/blockchain/**', '**/kethernet/**', '**/security/**'],
                priority: 2
            },
            testing: {
                name: 'Testing & Validation',
                description: 'Test frameworks, validation protocols, and quality assurance',
                patterns: ['**/test/**', '**/testing/**', '**/validation/**'],
                priority: 3
            },
            deployment: {
                name: 'Deployment & Operations',
                description: 'Deployment guides, operations, and infrastructure',
                patterns: ['**/deployment/**', '**/ops/**', '**/infrastructure/**'],
                priority: 3
            },
            integration: {
                name: 'Integration Guides',
                description: 'API integration, SDKs, and developer resources',
                patterns: ['**/integration/**', '**/sdk/**', '**/examples/**'],
                priority: 2
            }
        };
        
        this.documentIndex = new Map();
        this.searchIndex = new Map();
        this.indexingInProgress = false;
        this.lastIndexUpdate = null;
        
        this.registerWithNovaConnect();
    }
    
    /**
     * Register this connector with NovaConnect
     */
    async registerWithNovaConnect() {
        const connectorConfig = {
            id: 'novafuse-documentation-service',
            name: 'NovaFuse Documentation Service',
            description: 'Document search, indexing, and content management',
            version: '1.0.0',
            type: 'service',
            category: 'documentation',
            endpoints: {
                'discover-documents': {
                    method: 'GET',
                    path: '/docs/discover',
                    description: 'Discover and categorize available documents'
                },
                'search-documents': {
                    method: 'GET',
                    path: '/docs/search',
                    description: 'Search documents by content, title, or category'
                },
                'get-document': {
                    method: 'GET',
                    path: '/docs/content',
                    description: 'Get document content by path or ID'
                },
                'index-documents': {
                    method: 'POST',
                    path: '/docs/index',
                    description: 'Rebuild document index'
                },
                'document-stats': {
                    method: 'GET',
                    path: '/docs/stats',
                    description: 'Get documentation statistics and metrics'
                },
                'related-documents': {
                    method: 'GET',
                    path: '/docs/related',
                    description: 'Find related documents based on content similarity'
                }
            },
            authentication: {
                type: 'none',
                required: false
            },
            realTimeEvents: [
                'docs.indexed',
                'docs.search.completed',
                'docs.content.updated'
            ]
        };
        
        try {
            await this.novaConnect.registerConnector(connectorConfig);
            console.log('✅ Documentation Service Connector registered with NovaConnect');
            
            this.setupEventHandlers();
            
            // Initial indexing
            if (this.options.indexingEnabled) {
                await this.indexDocuments();
            }
            
        } catch (error) {
            console.error('❌ Failed to register Documentation Service Connector:', error);
            throw error;
        }
    }
    
    /**
     * Set up NovaConnect event handlers
     */
    setupEventHandlers() {
        this.novaConnect.on('connector.execute', async (event) => {
            if (event.connectorId === 'novafuse-documentation-service') {
                await this.handleConnectorRequest(event);
            }
        });
        
        if (this.options.enableRealTimeUpdates) {
            this.on('docsIndexed', (data) => {
                this.novaConnect.emit('docs.indexed', data);
            });
            
            this.on('searchCompleted', (data) => {
                this.novaConnect.emit('docs.search.completed', data);
            });
        }
    }
    
    /**
     * Handle NovaConnect connector requests
     */
    async handleConnectorRequest(event) {
        const { endpointId, parameters, requestId } = event;
        
        try {
            let result;
            
            switch (endpointId) {
                case 'discover-documents':
                    result = await this.discoverDocuments(parameters);
                    break;
                    
                case 'search-documents':
                    result = await this.searchDocuments(parameters);
                    break;
                    
                case 'get-document':
                    result = await this.getDocument(parameters);
                    break;
                    
                case 'index-documents':
                    result = await this.indexDocuments(parameters);
                    break;
                    
                case 'document-stats':
                    result = await this.getDocumentStats(parameters);
                    break;
                    
                case 'related-documents':
                    result = await this.getRelatedDocuments(parameters);
                    break;
                    
                default:
                    throw new Error(`Unknown endpoint: ${endpointId}`);
            }
            
            this.novaConnect.emit('connector.response', {
                requestId,
                success: true,
                data: result
            });
            
        } catch (error) {
            console.error(`Documentation Service error for ${endpointId}:`, error);
            
            this.novaConnect.emit('connector.response', {
                requestId,
                success: false,
                error: {
                    message: error.message,
                    code: error.code || 'DOCUMENTATION_SERVICE_ERROR'
                }
            });
        }
    }
    
    /**
     * Discover and categorize documents
     */
    async discoverDocuments(parameters = {}) {
        const { category, includeContent = false, forceRefresh = false } = parameters;
        
        if (forceRefresh || this.documentIndex.size === 0) {
            await this.indexDocuments();
        }
        
        const discoveredDocs = {};
        const categoriesToScan = category ? [category] : Object.keys(this.documentCategories);
        
        for (const categoryKey of categoriesToScan) {
            const categoryConfig = this.documentCategories[categoryKey];
            if (!categoryConfig) continue;
            
            const categoryDocs = [];
            
            for (const [filePath, docInfo] of this.documentIndex) {
                if (this.matchesCategory(filePath, categoryKey)) {
                    const doc = {
                        path: filePath,
                        title: docInfo.title || path.basename(filePath, path.extname(filePath)),
                        size: docInfo.size,
                        modified: docInfo.modified,
                        type: docInfo.type,
                        category: categoryKey
                    };
                    
                    if (includeContent && docInfo.content) {
                        doc.preview = docInfo.content.substring(0, 200) + '...';
                    }
                    
                    categoryDocs.push(doc);
                }
            }
            
            discoveredDocs[categoryKey] = {
                name: categoryConfig.name,
                description: categoryConfig.description,
                documents: categoryDocs,
                count: categoryDocs.length
            };
        }
        
        return {
            totalCategories: Object.keys(discoveredDocs).length,
            totalDocuments: Object.values(discoveredDocs).reduce((sum, cat) => sum + cat.count, 0),
            categories: discoveredDocs,
            lastIndexed: this.lastIndexUpdate,
            discoveredAt: new Date().toISOString()
        };
    }
    
    /**
     * Search documents
     */
    async searchDocuments(parameters = {}) {
        const { 
            query, 
            category, 
            type, 
            limit = this.options.maxSearchResults,
            includeContent = false 
        } = parameters;
        
        if (!query || query.trim().length === 0) {
            throw new Error('Search query is required');
        }
        
        const searchResults = [];
        const searchTerms = query.toLowerCase().split(/\s+/);
        
        for (const [filePath, docInfo] of this.documentIndex) {
            // Category filter
            if (category && !this.matchesCategory(filePath, category)) {
                continue;
            }
            
            // Type filter
            if (type && docInfo.type !== type) {
                continue;
            }
            
            // Calculate relevance score
            let score = 0;
            const content = (docInfo.content || '').toLowerCase();
            const title = (docInfo.title || path.basename(filePath)).toLowerCase();
            
            for (const term of searchTerms) {
                // Title matches are worth more
                if (title.includes(term)) {
                    score += 10;
                }
                
                // Content matches
                const contentMatches = (content.match(new RegExp(term, 'g')) || []).length;
                score += contentMatches;
                
                // Filename matches
                if (filePath.toLowerCase().includes(term)) {
                    score += 5;
                }
            }
            
            if (score > 0) {
                const result = {
                    path: filePath,
                    title: docInfo.title || path.basename(filePath, path.extname(filePath)),
                    type: docInfo.type,
                    size: docInfo.size,
                    modified: docInfo.modified,
                    score: score,
                    category: this.getCategoryForPath(filePath)
                };
                
                if (includeContent && docInfo.content) {
                    // Find relevant excerpt
                    const excerpt = this.extractRelevantExcerpt(docInfo.content, searchTerms);
                    result.excerpt = excerpt;
                }
                
                searchResults.push(result);
            }
        }
        
        // Sort by relevance score
        searchResults.sort((a, b) => b.score - a.score);
        
        const limitedResults = searchResults.slice(0, limit);
        
        this.emit('searchCompleted', {
            query: query,
            resultsCount: limitedResults.length,
            totalMatches: searchResults.length
        });
        
        return {
            query: query,
            results: limitedResults,
            totalResults: searchResults.length,
            returnedResults: limitedResults.length,
            searchedAt: new Date().toISOString()
        };
    }
    
    /**
     * Get document content
     */
    async getDocument(parameters = {}) {
        const { path: docPath, includeMetadata = true } = parameters;
        
        if (!docPath) {
            throw new Error('Document path is required');
        }
        
        const fullPath = path.join(this.options.docsRootPath, docPath);
        
        try {
            const content = await fs.readFile(fullPath, 'utf8');
            const stats = await fs.stat(fullPath);
            
            const result = {
                path: docPath,
                content: content,
                size: stats.size,
                modified: stats.mtime,
                type: this.getFileType(docPath)
            };
            
            if (includeMetadata) {
                const docInfo = this.documentIndex.get(docPath);
                if (docInfo) {
                    result.title = docInfo.title;
                    result.category = this.getCategoryForPath(docPath);
                }
            }
            
            return result;
            
        } catch (error) {
            throw new Error(`Failed to read document: ${error.message}`);
        }
    }
    
    /**
     * Index all documents
     */
    async indexDocuments(parameters = {}) {
        if (this.indexingInProgress) {
            return { status: 'indexing_in_progress', message: 'Document indexing already in progress' };
        }
        
        this.indexingInProgress = true;
        const startTime = Date.now();
        
        try {
            console.log('🔍 Starting document indexing...');
            
            this.documentIndex.clear();
            this.searchIndex.clear();
            
            const indexedCount = await this.scanDirectory(this.options.docsRootPath);
            
            this.lastIndexUpdate = new Date().toISOString();
            this.indexingInProgress = false;
            
            const duration = Date.now() - startTime;
            
            console.log(`✅ Document indexing completed: ${indexedCount} documents in ${duration}ms`);
            
            this.emit('docsIndexed', {
                documentsIndexed: indexedCount,
                duration: duration,
                indexedAt: this.lastIndexUpdate
            });
            
            return {
                status: 'completed',
                documentsIndexed: indexedCount,
                duration: duration,
                indexedAt: this.lastIndexUpdate
            };
            
        } catch (error) {
            this.indexingInProgress = false;
            console.error('❌ Document indexing failed:', error);
            throw error;
        }
    }
    
    /**
     * Scan directory recursively for documents
     */
    async scanDirectory(dirPath, relativePath = '') {
        let indexedCount = 0;
        
        try {
            const entries = await fs.readdir(dirPath, { withFileTypes: true });
            
            for (const entry of entries) {
                const fullPath = path.join(dirPath, entry.name);
                const relativeFilePath = path.join(relativePath, entry.name);
                
                if (entry.isDirectory()) {
                    // Skip node_modules and other common ignore patterns
                    if (!this.shouldIgnoreDirectory(entry.name)) {
                        indexedCount += await this.scanDirectory(fullPath, relativeFilePath);
                    }
                } else if (entry.isFile()) {
                    if (this.shouldIndexFile(entry.name)) {
                        await this.indexFile(fullPath, relativeFilePath);
                        indexedCount++;
                    }
                }
            }
            
        } catch (error) {
            console.error(`Error scanning directory ${dirPath}:`, error);
        }
        
        return indexedCount;
    }
    
    /**
     * Index a single file
     */
    async indexFile(fullPath, relativePath) {
        try {
            const stats = await fs.stat(fullPath);
            const content = await fs.readFile(fullPath, 'utf8');
            
            const docInfo = {
                path: relativePath,
                size: stats.size,
                modified: stats.mtime,
                created: stats.birthtime,
                type: this.getFileType(relativePath),
                content: content,
                title: this.extractTitle(content, relativePath)
            };
            
            this.documentIndex.set(relativePath, docInfo);
            
            // Add to search index
            this.addToSearchIndex(relativePath, content);
            
        } catch (error) {
            console.error(`Error indexing file ${fullPath}:`, error);
        }
    }
    
    /**
     * Get document statistics
     */
    async getDocumentStats(parameters = {}) {
        const stats = {
            totalDocuments: this.documentIndex.size,
            lastIndexed: this.lastIndexUpdate,
            indexingInProgress: this.indexingInProgress,
            categories: {},
            fileTypes: {},
            totalSize: 0
        };
        
        // Calculate category and type statistics
        for (const [filePath, docInfo] of this.documentIndex) {
            const category = this.getCategoryForPath(filePath);
            const fileType = docInfo.type;
            
            stats.categories[category] = (stats.categories[category] || 0) + 1;
            stats.fileTypes[fileType] = (stats.fileTypes[fileType] || 0) + 1;
            stats.totalSize += docInfo.size;
        }
        
        return stats;
    }
    
    /**
     * Get related documents
     */
    async getRelatedDocuments(parameters = {}) {
        const { path: docPath, limit = 10 } = parameters;
        
        if (!docPath) {
            throw new Error('Document path is required');
        }
        
        const docInfo = this.documentIndex.get(docPath);
        if (!docInfo) {
            throw new Error('Document not found');
        }
        
        // Simple related document algorithm based on category and content similarity
        const related = [];
        const category = this.getCategoryForPath(docPath);
        const keywords = this.extractKeywords(docInfo.content);
        
        for (const [otherPath, otherInfo] of this.documentIndex) {
            if (otherPath === docPath) continue;
            
            let similarity = 0;
            
            // Same category bonus
            if (this.getCategoryForPath(otherPath) === category) {
                similarity += 5;
            }
            
            // Content similarity
            const otherKeywords = this.extractKeywords(otherInfo.content);
            const commonKeywords = keywords.filter(k => otherKeywords.includes(k));
            similarity += commonKeywords.length;
            
            if (similarity > 0) {
                related.push({
                    path: otherPath,
                    title: otherInfo.title || path.basename(otherPath, path.extname(otherPath)),
                    category: this.getCategoryForPath(otherPath),
                    similarity: similarity
                });
            }
        }
        
        // Sort by similarity and limit results
        related.sort((a, b) => b.similarity - a.similarity);
        
        return {
            document: docPath,
            related: related.slice(0, limit),
            totalRelated: related.length
        };
    }
    
    // Helper methods
    
    shouldIgnoreDirectory(dirName) {
        const ignorePatterns = ['node_modules', '.git', '.vscode', 'dist', 'build', '__pycache__'];
        return ignorePatterns.includes(dirName) || dirName.startsWith('.');
    }
    
    shouldIndexFile(fileName) {
        const ext = path.extname(fileName).toLowerCase();
        return this.options.supportedExtensions.includes(ext);
    }
    
    getFileType(filePath) {
        const ext = path.extname(filePath).toLowerCase();
        const typeMap = {
            '.md': 'markdown',
            '.txt': 'text',
            '.json': 'json',
            '.py': 'python',
            '.js': 'javascript',
            '.html': 'html',
            '.css': 'css'
        };
        return typeMap[ext] || 'unknown';
    }
    
    extractTitle(content, filePath) {
        // Try to extract title from markdown headers or first line
        const lines = content.split('\n');
        for (const line of lines.slice(0, 10)) {
            const trimmed = line.trim();
            if (trimmed.startsWith('# ')) {
                return trimmed.substring(2).trim();
            }
            if (trimmed.startsWith('## ')) {
                return trimmed.substring(3).trim();
            }
        }
        
        // Fallback to filename
        return path.basename(filePath, path.extname(filePath));
    }
    
    matchesCategory(filePath, categoryKey) {
        const category = this.documentCategories[categoryKey];
        if (!category) return false;
        
        const lowerPath = filePath.toLowerCase();
        return category.patterns.some(pattern => {
            const regex = new RegExp(pattern.replace(/\*\*/g, '.*').replace(/\*/g, '[^/]*'));
            return regex.test(lowerPath);
        });
    }
    
    getCategoryForPath(filePath) {
        for (const [categoryKey, category] of Object.entries(this.documentCategories)) {
            if (this.matchesCategory(filePath, categoryKey)) {
                return categoryKey;
            }
        }
        return 'uncategorized';
    }
    
    extractRelevantExcerpt(content, searchTerms) {
        const sentences = content.split(/[.!?]+/);
        let bestSentence = '';
        let maxMatches = 0;
        
        for (const sentence of sentences) {
            const lowerSentence = sentence.toLowerCase();
            const matches = searchTerms.reduce((count, term) => {
                return count + (lowerSentence.includes(term) ? 1 : 0);
            }, 0);
            
            if (matches > maxMatches) {
                maxMatches = matches;
                bestSentence = sentence.trim();
            }
        }
        
        return bestSentence.substring(0, 200) + (bestSentence.length > 200 ? '...' : '');
    }
    
    extractKeywords(content) {
        // Simple keyword extraction
        const words = content.toLowerCase()
            .replace(/[^\w\s]/g, ' ')
            .split(/\s+/)
            .filter(word => word.length > 3)
            .filter(word => !['this', 'that', 'with', 'have', 'will', 'from', 'they', 'been', 'were', 'said', 'each', 'which', 'their', 'time', 'would', 'there', 'could', 'other'].includes(word));
        
        // Count frequency and return top keywords
        const frequency = {};
        words.forEach(word => {
            frequency[word] = (frequency[word] || 0) + 1;
        });
        
        return Object.entries(frequency)
            .sort((a, b) => b[1] - a[1])
            .slice(0, 20)
            .map(([word]) => word);
    }
    
    addToSearchIndex(filePath, content) {
        const words = content.toLowerCase()
            .replace(/[^\w\s]/g, ' ')
            .split(/\s+/)
            .filter(word => word.length > 2);
        
        for (const word of words) {
            if (!this.searchIndex.has(word)) {
                this.searchIndex.set(word, new Set());
            }
            this.searchIndex.get(word).add(filePath);
        }
    }
}

module.exports = DocumentationServiceConnector;
