# ClickBank Setup Guide

This guide will help you set up your ClickBank credentials for the Triadic Affiliate System.

## Prerequisites

1. **ClickBank Account**
   - You must have a ClickBank account
   - Ensure you have affiliate access
   - Your account must be verified

2. **Required Information**
   - Your ClickBank username
   - Your ClickBank password

## Setup Steps

1. **Create .env File**
   - The system will automatically create a `.env` file from the template
   - This file contains all your configuration settings

2. **Enter Credentials**
   - Run the setup script:
   ```bash
   node scripts/setup-clickbank.js
   ```
   - The script will ask for your ClickBank username and password
   - Password input will be hidden for security

3. **Verify Setup**
   - The script will confirm successful setup
   - You can verify your credentials by checking the `.env` file
   - The file should contain your ClickBank credentials

## Security Notes

1. **Keep .env Secure**
   - Never share your `.env` file
   - Never commit it to version control
   - The file is automatically ignored by git

2. **Password Protection**
   - Password input is hidden during setup
   - Credentials are stored in plain text in `.env`
   - Consider using environment variable manager for production

3. **API Security**
   - The system uses HTTPS for all API calls
   - Credentials are encrypted in transit
   - Access is rate-limited for security

## Troubleshooting

1. **Missing .env**
   - If `.env` is missing, run setup script
   - Or copy `.env.example` manually:
   ```bash
   cp .env.example .env
   ```

2. **Invalid Credentials**
   - Double-check your ClickBank login
   - Ensure you have affiliate access
   - Verify account verification status

3. **API Errors**
   - Check network connection
   - Verify ClickBank API status
   - Contact ClickBank support if needed

## Next Steps

After setup, you can:
1. Run product discovery:
```bash
node scripts/discover-products.js
```

2. Access the dashboard:
```
http://localhost:3000
```

3. Start optimizing campaigns:
- Scan for products
- Apply triadic optimization
- Generate κ-boosted links
- Monitor performance

## Support

For assistance:
- Email: <EMAIL>
- Telegram: @TriadicAffiliateSupport
- Phone: +****************
