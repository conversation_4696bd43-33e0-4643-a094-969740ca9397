/**
 * Meter Example
 * 
 * This example demonstrates the usage of the Meter component.
 */

const {
  UniversalEntropyMeasurement,
  ThresholdManagement,
  AlertingSystem,
  createMeterSystem,
  createEnhancedMeterSystem
} = require('../meter');

// Import Bridge for integration example
const { createEnhancedBridgeSystem } = require('../bridge');

// Example 1: Using individual components
console.log('Example 1: Using individual components');

// Create components
const universalEntropyMeasurement = new UniversalEntropyMeasurement();
const thresholdManagement = new ThresholdManagement();
const alertingSystem = new AlertingSystem();

// Start components
universalEntropyMeasurement.start();
alertingSystem.start();

// Update domain metrics
universalEntropyMeasurement.updateDomainMetric('cyber', 'policyEntropy', 0.5);
universalEntropyMeasurement.updateDomainMetric('cyber', 'auditEntropy', 0.6);
universalEntropyMeasurement.updateDomainMetric('cyber', 'regulatoryEntropy', 0.4);

universalEntropyMeasurement.updateDomainMetric('financial', 'transactionEntropy', 0.7);
universalEntropyMeasurement.updateDomainMetric('financial', 'attackSurfaceCoherence', 0.6);
universalEntropyMeasurement.updateDomainMetric('financial', 'marketStress', 0.5);

universalEntropyMeasurement.updateDomainMetric('biological', 'telomereLength', 0.7);
universalEntropyMeasurement.updateDomainMetric('biological', 'mtorActivation', 0.4);
universalEntropyMeasurement.updateDomainMetric('biological', 'inflammationLevel', 0.3);

// Get universal entropy
const universalEntropy = universalEntropyMeasurement.getUniversalEntropy();
console.log(`Universal Entropy: ${universalEntropy.toFixed(4)}`);

// Get domain entropy
const cyberEntropy = universalEntropyMeasurement.getDomainEntropy('cyber');
console.log(`Cyber Domain Entropy: ${cyberEntropy.overallEntropy.toFixed(4)}`);

const financialEntropy = universalEntropyMeasurement.getDomainEntropy('financial');
console.log(`Financial Domain Entropy: ${financialEntropy.overallEntropy.toFixed(4)}`);

const biologicalEntropy = universalEntropyMeasurement.getDomainEntropy('biological');
console.log(`Biological Domain Entropy: ${biologicalEntropy.overallEntropy.toFixed(4)}`);

// Calculate Comphyon value
const comphyonValue = universalEntropyMeasurement.calculateComphyon();
console.log(`Comphyon Value: ${comphyonValue.toFixed(4)} Cph`);

// Set thresholds
thresholdManagement.setThreshold('universal', 'warning', 0.5);
thresholdManagement.setThreshold('universal', 'critical', 0.7);
thresholdManagement.setThreshold('universal', 'emergency', 0.9);

// Evaluate thresholds
const evaluation = thresholdManagement.evaluateThreshold('universal', universalEntropy);
console.log(`Threshold Evaluation: ${evaluation.thresholdLevel}`);

// Process alert if threshold violated
if (evaluation.thresholdLevel !== 'normal') {
  const alert = {
    id: `alert-universal-${evaluation.thresholdLevel}-${Date.now()}`,
    domain: 'universal',
    level: evaluation.thresholdLevel,
    entropyValue: evaluation.entropyValue,
    threshold: evaluation.violatedThreshold,
    deviation: evaluation.entropyValue - evaluation.violatedThreshold,
    timestamp: Date.now()
  };
  
  const alertResult = alertingSystem.processAlert(alert);
  console.log(`Alert Processed: ${alertResult.status}`);
}

// Stop components
universalEntropyMeasurement.stop();
alertingSystem.stop();

// Example 2: Using the basic Meter system
console.log('\nExample 2: Using the basic Meter system');

// Create Meter system
const meterSystem = createMeterSystem({
  universalEntropyMeasurementOptions: {
    enableLogging: true
  },
  thresholdManagementOptions: {
    enableLogging: true
  },
  alertingSystemOptions: {
    enableLogging: true
  }
});

// Start components
meterSystem.universalEntropyMeasurement.start();
meterSystem.alertingSystem.start();

// Update domain metrics
meterSystem.universalEntropyMeasurement.updateDomainMetric('cyber', 'policyEntropy', 0.6);
meterSystem.universalEntropyMeasurement.updateDomainMetric('financial', 'transactionEntropy', 0.7);
meterSystem.universalEntropyMeasurement.updateDomainMetric('biological', 'inflammationLevel', 0.5);

// Get universal entropy
const universalEntropy2 = meterSystem.universalEntropyMeasurement.getUniversalEntropy();
console.log(`Universal Entropy: ${universalEntropy2.toFixed(4)}`);

// Evaluate thresholds
const evaluation2 = meterSystem.thresholdManagement.evaluateThreshold('universal', universalEntropy2);
console.log(`Threshold Evaluation: ${evaluation2.thresholdLevel}`);

// Process alert if threshold violated
if (evaluation2.thresholdLevel !== 'normal') {
  const alert = {
    id: `alert-universal-${evaluation2.thresholdLevel}-${Date.now()}`,
    domain: 'universal',
    level: evaluation2.thresholdLevel,
    entropyValue: evaluation2.entropyValue,
    threshold: evaluation2.violatedThreshold,
    deviation: evaluation2.entropyValue - evaluation2.violatedThreshold,
    timestamp: Date.now()
  };
  
  const alertResult = meterSystem.alertingSystem.processAlert(alert);
  console.log(`Alert Processed: ${alertResult.status}`);
}

// Stop components
meterSystem.universalEntropyMeasurement.stop();
meterSystem.alertingSystem.stop();

// Example 3: Using the enhanced Meter system
console.log('\nExample 3: Using the enhanced Meter system');

// Create Bridge system for integration
const bridgeSystem = createEnhancedBridgeSystem({
  enableLogging: true
});

// Create enhanced Meter system
const enhancedMeterSystem = createEnhancedMeterSystem(
  {
    enableLogging: true
  },
  bridgeSystem
);

// Start system
enhancedMeterSystem.start();

// Process domain data
const cyberResult = enhancedMeterSystem.processDomainData('cyber', 'policyEntropy', 0.7, {
  source: 'test',
  description: 'High policy entropy'
});
console.log(`Processed cyber data: entropy=${cyberResult.entropyValue.toFixed(4)}, comphyon=${cyberResult.comphyonValue.toFixed(4)} Cph`);

const financialResult = enhancedMeterSystem.processDomainData('financial', 'transactionEntropy', 0.8, {
  source: 'test',
  description: 'High transaction entropy'
});
console.log(`Processed financial data: entropy=${financialResult.entropyValue.toFixed(4)}, comphyon=${financialResult.comphyonValue.toFixed(4)} Cph`);

const biologicalResult = enhancedMeterSystem.processDomainData('biological', 'inflammationLevel', 0.6, {
  source: 'test',
  description: 'Moderate inflammation level'
});
console.log(`Processed biological data: entropy=${biologicalResult.entropyValue.toFixed(4)}, comphyon=${biologicalResult.comphyonValue.toFixed(4)} Cph`);

// Configure thresholds
enhancedMeterSystem.configureThresholds({
  universal: {
    warning: 0.5,
    critical: 0.7,
    emergency: 0.9
  },
  cyber: {
    warning: 0.6,
    critical: 0.8,
    emergency: 0.95
  }
});

// Configure alerting channels
enhancedMeterSystem.configureAlertingChannels({
  console: {
    enabled: true
  }
});

// Get entropy dashboard
const dashboard = enhancedMeterSystem.getEntropyDashboard();
console.log('Entropy Dashboard:');
console.log(`- Universal Entropy: ${dashboard.universalEntropy.toFixed(4)}`);
console.log('- Domain Entropy:');
console.log(`  - Cyber: ${dashboard.domainEntropy.cyber.overallEntropy.toFixed(4)}`);
console.log(`  - Financial: ${dashboard.domainEntropy.financial.overallEntropy.toFixed(4)}`);
console.log(`  - Biological: ${dashboard.domainEntropy.biological.overallEntropy.toFixed(4)}`);
console.log(`- Comphyon Value: ${dashboard.comphyonValue.toFixed(4)} Cph`);
console.log(`- Active Alerts: ${dashboard.activeAlerts.length}`);

// Get unified state
const unifiedState = enhancedMeterSystem.getUnifiedState();
console.log('Unified State Summary:');
console.log(`- Universal Entropy: ${unifiedState.universalEntropy.toFixed(4)}`);
console.log(`- Active Alerts: ${unifiedState.activeAlerts.length}`);

// Get unified metrics
const unifiedMetrics = enhancedMeterSystem.getUnifiedMetrics();
console.log('Unified Metrics Summary:');
console.log(`- Entropy Updates: ${unifiedMetrics.universalEntropyMeasurement.totalUpdates}`);
console.log(`- Threshold Evaluations: ${unifiedMetrics.thresholdManagement.totalEvaluations}`);
console.log(`- Alerts Processed: ${unifiedMetrics.alertingSystem.alertsProcessed}`);

// Stop system
enhancedMeterSystem.stop();

console.log('\nMeter example completed successfully!');
