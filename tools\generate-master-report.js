/**
 * NovaFuse Universal Platform Master Test Report Generator
 * 
 * This script generates a comprehensive master test report by combining
 * results from functional, security, compliance, and performance tests.
 */

const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  outputDir: path.join(__dirname, '../test-reports'),
  outputFile: 'master-report.html',
  functionalTestResults: path.join(__dirname, '../test-results/junit/results.xml'),
  securityTestResults: path.join(__dirname, '../security-reports/security-report.json'),
  complianceTestResults: path.join(__dirname, '../compliance-reports/compliance-summary-report.json'),
  performanceTestResults: path.join(__dirname, '../performance-reports/performance-report.json'),
  coverageResults: path.join(__dirname, '../coverage/coverage-summary.json')
};

// Ensure output directory exists
if (!fs.existsSync(config.outputDir)) {
  fs.mkdirSync(config.outputDir, { recursive: true });
}

/**
 * Parse functional test results
 */
function parseFunctionalTestResults() {
  try {
    if (!fs.existsSync(config.functionalTestResults)) {
      console.warn(`Functional test results file not found: ${config.functionalTestResults}`);
      return {
        success: false,
        message: 'Functional test results file not found',
        data: null
      };
    }

    const xml = fs.readFileSync(config.functionalTestResults, 'utf8');
    
    // Simple XML parsing (in a real implementation, use a proper XML parser)
    const testsuites = xml.match(/<testsuites.*?>(.*?)<\/testsuites>/s)[1];
    const testsuitesAttrs = xml.match(/<testsuites(.*?)>/)[1];
    
    const tests = testsuitesAttrs.match(/tests="(\d+)"/)[1];
    const failures = testsuitesAttrs.match(/failures="(\d+)"/)[1];
    const errors = testsuitesAttrs.match(/errors="(\d+)"/)[1];
    
    return {
      success: parseInt(failures) === 0 && parseInt(errors) === 0,
      message: `${tests} tests, ${failures} failures, ${errors} errors`,
      data: {
        tests: parseInt(tests),
        failures: parseInt(failures),
        errors: parseInt(errors),
        testsuites: testsuites.match(/<testsuite(.*?)>/g).length
      }
    };
  } catch (error) {
    console.error('Error parsing functional test results:', error);
    return {
      success: false,
      message: `Error parsing functional test results: ${error.message}`,
      data: null
    };
  }
}

/**
 * Parse security test results
 */
function parseSecurityTestResults() {
  try {
    if (!fs.existsSync(config.securityTestResults)) {
      console.warn(`Security test results file not found: ${config.securityTestResults}`);
      return {
        success: false,
        message: 'Security test results file not found',
        data: null
      };
    }

    const securityResults = JSON.parse(fs.readFileSync(config.securityTestResults, 'utf8'));
    
    return {
      success: securityResults.success,
      message: securityResults.message,
      data: securityResults.data
    };
  } catch (error) {
    console.error('Error parsing security test results:', error);
    return {
      success: false,
      message: `Error parsing security test results: ${error.message}`,
      data: null
    };
  }
}

/**
 * Parse compliance test results
 */
function parseComplianceTestResults() {
  try {
    if (!fs.existsSync(config.complianceTestResults)) {
      console.warn(`Compliance test results file not found: ${config.complianceTestResults}`);
      return {
        success: false,
        message: 'Compliance test results file not found',
        data: null
      };
    }

    const complianceResults = JSON.parse(fs.readFileSync(config.complianceTestResults, 'utf8'));
    
    return {
      success: complianceResults.success,
      message: complianceResults.message,
      data: complianceResults.data
    };
  } catch (error) {
    console.error('Error parsing compliance test results:', error);
    return {
      success: false,
      message: `Error parsing compliance test results: ${error.message}`,
      data: null
    };
  }
}

/**
 * Parse performance test results
 */
function parsePerformanceTestResults() {
  try {
    if (!fs.existsSync(config.performanceTestResults)) {
      console.warn(`Performance test results file not found: ${config.performanceTestResults}`);
      return {
        success: false,
        message: 'Performance test results file not found',
        data: null
      };
    }

    const performanceResults = JSON.parse(fs.readFileSync(config.performanceTestResults, 'utf8'));
    
    return {
      success: performanceResults.success,
      message: performanceResults.message,
      data: performanceResults.data
    };
  } catch (error) {
    console.error('Error parsing performance test results:', error);
    return {
      success: false,
      message: `Error parsing performance test results: ${error.message}`,
      data: null
    };
  }
}

/**
 * Parse coverage results
 */
function parseCoverageResults() {
  try {
    if (!fs.existsSync(config.coverageResults)) {
      console.warn(`Coverage results file not found: ${config.coverageResults}`);
      return {
        success: false,
        message: 'Coverage results file not found',
        data: null
      };
    }

    const coverageResults = JSON.parse(fs.readFileSync(config.coverageResults, 'utf8'));
    
    const total = coverageResults.total;
    
    return {
      success: total.branches.pct >= 96 && total.functions.pct >= 96 && total.lines.pct >= 96 && total.statements.pct >= 96,
      message: `Coverage: ${total.statements.pct}% statements, ${total.branches.pct}% branches, ${total.functions.pct}% functions, ${total.lines.pct}% lines`,
      data: total
    };
  } catch (error) {
    console.error('Error parsing coverage results:', error);
    return {
      success: false,
      message: `Error parsing coverage results: ${error.message}`,
      data: null
    };
  }
}

/**
 * Generate HTML report
 */
function generateHtmlReport(results) {
  const timestamp = new Date().toISOString();
  const overallSuccess = results.functional.success && results.security.success && results.compliance.success && results.performance.success && results.coverage.success;
  
  const html = `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>NovaFuse Universal Platform Test Report</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    h1, h2, h3 {
      color: #0A84FF;
    }
    .header {
      text-align: center;
      margin-bottom: 30px;
      padding-bottom: 20px;
      border-bottom: 1px solid #eee;
    }
    .summary {
      display: flex;
      justify-content: space-between;
      margin-bottom: 30px;
      padding: 20px;
      background-color: ${overallSuccess ? '#f0f7f0' : '#fff0f0'};
      border-radius: 5px;
      border-left: 5px solid ${overallSuccess ? '#4CAF50' : '#F44336'};
    }
    .summary-item {
      text-align: center;
    }
    .success {
      color: #4CAF50;
    }
    .failure {
      color: #F44336;
    }
    .section {
      margin-bottom: 30px;
      padding: 20px;
      background-color: #f9f9f9;
      border-radius: 5px;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 20px;
    }
    th, td {
      padding: 12px 15px;
      text-align: left;
      border-bottom: 1px solid #ddd;
    }
    th {
      background-color: #0A84FF;
      color: white;
    }
    tr:hover {
      background-color: #f5f5f5;
    }
    .progress-bar {
      height: 20px;
      background-color: #e0e0e0;
      border-radius: 10px;
      overflow: hidden;
      margin-bottom: 10px;
    }
    .progress-bar-fill {
      height: 100%;
      background-color: #0A84FF;
      border-radius: 10px;
    }
    .footer {
      text-align: center;
      margin-top: 30px;
      padding-top: 20px;
      border-top: 1px solid #eee;
      color: #777;
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>NovaFuse Universal Platform Test Report</h1>
    <p>Generated on ${new Date(timestamp).toLocaleString()}</p>
  </div>
  
  <div class="summary">
    <div class="summary-item">
      <h3>Overall Status</h3>
      <p class="${overallSuccess ? 'success' : 'failure'}">${overallSuccess ? 'PASS' : 'FAIL'}</p>
    </div>
    <div class="summary-item">
      <h3>Functional Tests</h3>
      <p class="${results.functional.success ? 'success' : 'failure'}">${results.functional.success ? 'PASS' : 'FAIL'}</p>
    </div>
    <div class="summary-item">
      <h3>Security Tests</h3>
      <p class="${results.security.success ? 'success' : 'failure'}">${results.security.success ? 'PASS' : 'FAIL'}</p>
    </div>
    <div class="summary-item">
      <h3>Compliance Tests</h3>
      <p class="${results.compliance.success ? 'success' : 'failure'}">${results.compliance.success ? 'PASS' : 'FAIL'}</p>
    </div>
    <div class="summary-item">
      <h3>Performance Tests</h3>
      <p class="${results.performance.success ? 'success' : 'failure'}">${results.performance.success ? 'PASS' : 'FAIL'}</p>
    </div>
    <div class="summary-item">
      <h3>Code Coverage</h3>
      <p class="${results.coverage.success ? 'success' : 'failure'}">${results.coverage.success ? 'PASS' : 'FAIL'}</p>
    </div>
  </div>
  
  <div class="section">
    <h2>Functional Tests</h2>
    <p>${results.functional.message}</p>
    ${results.functional.data ? `
    <table>
      <tr>
        <th>Metric</th>
        <th>Value</th>
      </tr>
      <tr>
        <td>Total Tests</td>
        <td>${results.functional.data.tests}</td>
      </tr>
      <tr>
        <td>Failures</td>
        <td>${results.functional.data.failures}</td>
      </tr>
      <tr>
        <td>Errors</td>
        <td>${results.functional.data.errors}</td>
      </tr>
      <tr>
        <td>Test Suites</td>
        <td>${results.functional.data.testsuites}</td>
      </tr>
    </table>
    ` : ''}
  </div>
  
  <div class="section">
    <h2>Security Tests</h2>
    <p>${results.security.message}</p>
    ${results.security.data ? `
    <table>
      <tr>
        <th>Category</th>
        <th>Critical</th>
        <th>High</th>
        <th>Medium</th>
        <th>Low</th>
      </tr>
      <tr>
        <td>SAST</td>
        <td>${results.security.data.sast.critical}</td>
        <td>${results.security.data.sast.high}</td>
        <td>${results.security.data.sast.medium}</td>
        <td>${results.security.data.sast.low}</td>
      </tr>
      <tr>
        <td>DAST</td>
        <td>${results.security.data.dast.critical}</td>
        <td>${results.security.data.dast.high}</td>
        <td>${results.security.data.dast.medium}</td>
        <td>${results.security.data.dast.low}</td>
      </tr>
      <tr>
        <td>Dependencies</td>
        <td>${results.security.data.dependencies.critical}</td>
        <td>${results.security.data.dependencies.high}</td>
        <td>${results.security.data.dependencies.medium}</td>
        <td>${results.security.data.dependencies.low}</td>
      </tr>
    </table>
    ` : ''}
  </div>
  
  <div class="section">
    <h2>Compliance Tests</h2>
    <p>${results.compliance.message}</p>
    ${results.compliance.data ? `
    <table>
      <tr>
        <th>Framework</th>
        <th>Status</th>
        <th>Compliance Score</th>
        <th>Controls Tested</th>
        <th>Controls Passed</th>
      </tr>
      ${Object.entries(results.compliance.data.frameworks).map(([framework, data]) => `
      <tr>
        <td>${framework}</td>
        <td class="${data.compliant ? 'success' : 'failure'}">${data.compliant ? 'COMPLIANT' : 'NON-COMPLIANT'}</td>
        <td>${data.score}%</td>
        <td>${data.controlsTested}</td>
        <td>${data.controlsPassed}</td>
      </tr>
      `).join('')}
    </table>
    ` : ''}
  </div>
  
  <div class="section">
    <h2>Performance Tests</h2>
    <p>${results.performance.message}</p>
    ${results.performance.data ? `
    <table>
      <tr>
        <th>Metric</th>
        <th>Value</th>
        <th>Threshold</th>
        <th>Status</th>
      </tr>
      ${Object.entries(results.performance.data.metrics).map(([metric, data]) => `
      <tr>
        <td>${metric}</td>
        <td>${typeof data.value === 'number' ? data.value.toFixed(2) : data.value} ${data.unit || ''}</td>
        <td>${data.threshold || 'N/A'}</td>
        <td class="${data.passed ? 'success' : 'failure'}">${data.passed ? 'PASS' : 'FAIL'}</td>
      </tr>
      `).join('')}
    </table>
    ` : ''}
  </div>
  
  <div class="section">
    <h2>Code Coverage</h2>
    <p>${results.coverage.message}</p>
    ${results.coverage.data ? `
    <div>
      <h3>Statements</h3>
      <div class="progress-bar">
        <div class="progress-bar-fill" style="width: ${results.coverage.data.statements.pct}%"></div>
      </div>
      <p>${results.coverage.data.statements.pct}% (${results.coverage.data.statements.covered}/${results.coverage.data.statements.total})</p>
      
      <h3>Branches</h3>
      <div class="progress-bar">
        <div class="progress-bar-fill" style="width: ${results.coverage.data.branches.pct}%"></div>
      </div>
      <p>${results.coverage.data.branches.pct}% (${results.coverage.data.branches.covered}/${results.coverage.data.branches.total})</p>
      
      <h3>Functions</h3>
      <div class="progress-bar">
        <div class="progress-bar-fill" style="width: ${results.coverage.data.functions.pct}%"></div>
      </div>
      <p>${results.coverage.data.functions.pct}% (${results.coverage.data.functions.covered}/${results.coverage.data.functions.total})</p>
      
      <h3>Lines</h3>
      <div class="progress-bar">
        <div class="progress-bar-fill" style="width: ${results.coverage.data.lines.pct}%"></div>
      </div>
      <p>${results.coverage.data.lines.pct}% (${results.coverage.data.lines.covered}/${results.coverage.data.lines.total})</p>
    </div>
    ` : ''}
  </div>
  
  <div class="footer">
    <p>NovaFuse Universal Platform Test Report</p>
    <p>Generated by NovaFuse Test Framework</p>
  </div>
</body>
</html>
  `;
  
  const outputPath = path.join(config.outputDir, config.outputFile);
  fs.writeFileSync(outputPath, html);
  
  return outputPath;
}

// Main execution
try {
  console.log('Generating master test report...');
  
  // Parse test results
  const functionalResults = parseFunctionalTestResults();
  const securityResults = parseSecurityTestResults();
  const complianceResults = parseComplianceTestResults();
  const performanceResults = parsePerformanceTestResults();
  const coverageResults = parseCoverageResults();
  
  // Combine results
  const results = {
    functional: functionalResults,
    security: securityResults,
    compliance: complianceResults,
    performance: performanceResults,
    coverage: coverageResults
  };
  
  // Generate HTML report
  const reportPath = generateHtmlReport(results);
  
  console.log(`Master test report generated: ${reportPath}`);
} catch (error) {
  console.error('Error generating master test report:', error);
  process.exit(1);
}
