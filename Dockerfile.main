# Multi-stage Dockerfile for NovaFuse API Superstore Main Service
# This Dockerfile builds the primary Node.js application with Next.js support

# Stage 1: Base Node.js environment
FROM node:18-alpine AS base
LABEL maintainer="NovaFuse Team <<EMAIL>>"
LABEL version="1.0.0"
LABEL description="NovaFuse API Superstore - Main Service"

# Install system dependencies for production
RUN apk add --no-cache \
    dumb-init \
    tini \
    curl \
    ca-certificates \
    && rm -rf /var/cache/apk/*

# Create app user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S novafuse -u 1001

# Set working directory
WORKDIR /app

# Stage 2: Dependencies installation
FROM base AS deps
# Install dependencies based on package files
COPY package*.json ./
COPY yarn.lock* ./

# Install dependencies
RUN npm ci --only=production && npm cache clean --force

# Stage 3: Build environment
FROM base AS builder
COPY package*.json ./
RUN npm ci

# Copy source code
COPY . .

# Build Next.js application
RUN npm run build

# Stage 4: Development environment
FROM base AS development
ENV NODE_ENV=development
COPY package*.json ./
RUN npm ci
COPY . .
USER novafuse
EXPOSE 3002
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3002/privacy/management/health || exit 1
CMD ["dumb-init", "npm", "run", "dev"]

# Stage 5: Production environment
FROM base AS production
ENV NODE_ENV=production
ENV PORT=3002

# Copy built application from builder stage
COPY --from=builder --chown=novafuse:nodejs /app/package*.json ./
COPY --from=deps --chown=novafuse:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=novafuse:nodejs /app/.next ./.next
COPY --from=builder --chown=novafuse:nodejs /app/public ./public
COPY --from=builder --chown=novafuse:nodejs /app/server.js ./
COPY --from=builder --chown=novafuse:nodejs /app/app.js ./
COPY --from=builder --chown=novafuse:nodejs /app/src ./src
COPY --from=builder --chown=novafuse:nodejs /app/apis ./apis
COPY --from=builder --chown=novafuse:nodejs /app/config ./config

# Create necessary directories with proper permissions
RUN mkdir -p /app/logs /app/temp && \
    chown -R novafuse:nodejs /app/logs /app/temp

# Switch to non-root user
USER novafuse

# Expose port
EXPOSE 3002

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:3002/privacy/management/health || exit 1

# Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--"]
CMD ["node", "server.js"]

# Stage 6: Testing environment
FROM development AS testing
ENV NODE_ENV=test
RUN npm install --only=dev
COPY . .
USER novafuse
CMD ["npm", "test"]
