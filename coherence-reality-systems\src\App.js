import React, { useState, useEffect } from 'react';
import CBEConsciousnessEngine from './engines/CBEConsciousnessEngine';
import CBEHeader from './components/CBEHeader';
import ServiceCards from './components/ServiceCards';
import BrowserFrame from './components/BrowserFrame';
import ConsciousnessDashboard from './components/ConsciousnessDashboard';

function App() {
  const [currentUrl, setCurrentUrl] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showHomePage, setShowHomePage] = useState(true);
  const [cbeMode, setCbeMode] = useState(true);
  const [consciousnessEngine] = useState(new CBEConsciousnessEngine());
  const [consciousnessData, setConsciousnessData] = useState({
    overall_consciousness: 95.7,
    content_quality: 94.2,
    intent_clarity: 97.1,
    coherence_level: 95.7,
    psi_snap_active: true,
    analysis_time: 11,
    consciousness_state: 'DIVINE',
    threat_level: 'LOW',
    threats_detected: 0
  });

  const handleNavigate = async (url) => {
    if (!url) return;

    setIsLoading(true);
    setCurrentUrl(url);
    setShowHomePage(false);

    try {
      // Simulate fetching content (in real implementation, this would fetch actual content)
      const mockContent = `<html><body><h1>Sample Content</h1><p>Analyzing consciousness...</p></body></html>`;

      // Run CBE consciousness analysis
      const analysis = await consciousnessEngine.analyzeWebContent(url, mockContent, {
        user_context: 'browsing',
        timestamp: new Date().toISOString()
      });

      // Update consciousness data with real analysis
      setConsciousnessData({
        overall_consciousness: analysis.overall_consciousness,
        content_quality: analysis.content_quality,
        intent_clarity: analysis.intent_clarity,
        coherence_level: analysis.coherence_level,
        psi_snap_active: analysis.psi_snap_active,
        analysis_time: analysis.analysis_time,
        consciousness_state: analysis.consciousness_state,
        threat_level: analysis.threat_level,
        threats_detected: analysis.threats_detected,
        recommendations: analysis.recommendations,
        engine_results: analysis.engine_results
      });

      setIsLoading(false);

    } catch (error) {
      console.error('CBE Analysis failed:', error);
      // Fallback to mock data
      const mockData = {
        overall_consciousness: Math.floor(Math.random() * 40) + 60,
        content_quality: Math.floor(Math.random() * 30) + 70,
        intent_clarity: Math.floor(Math.random() * 30) + 70,
        coherence_level: Math.floor(Math.random() * 40) + 60,
        psi_snap_active: Math.random() > 0.3,
        analysis_time: Math.floor(Math.random() * 20) + 5,
        consciousness_state: 'CONSCIOUS',
        threat_level: 'LOW',
        threats_detected: 0
      };
      setConsciousnessData(mockData);
      setIsLoading(false);
    }
  };

  const handleHome = () => {
    setShowHomePage(true);
    setCurrentUrl('');
    setConsciousnessData({
      overall_consciousness: 95.7,
      content_quality: 94.2,
      intent_clarity: 97.1,
      coherence_level: 95.7,
      psi_snap_active: true,
      analysis_time: 11,
      consciousness_state: 'DIVINE',
      threat_level: 'LOW',
      threats_detected: 0
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Quantum Field Background */}
      <div className="fixed inset-0 opacity-30 pointer-events-none">
        <div className="absolute inset-0 bg-gradient-to-r from-purple-600/10 via-transparent to-pink-600/10 animate-pulse" />
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-purple-500/5 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-pink-500/5 rounded-full blur-3xl animate-pulse" />
      </div>

      {/* Main Content */}
      <div className="relative z-10">
        <CBEHeader
          currentUrl={currentUrl}
          onNavigate={handleNavigate}
          onHome={handleHome}
          cbeMode={cbeMode}
          setCbeMode={setCbeMode}
          consciousnessData={consciousnessData}
        />

        {showHomePage ? (
          <ServiceCards onNavigate={handleNavigate} />
        ) : (
          <div className="flex h-[calc(100vh-80px)]">
            <ConsciousnessDashboard consciousnessData={consciousnessData} />
            <BrowserFrame
              url={currentUrl}
              isLoading={isLoading}
              consciousnessData={consciousnessData}
            />
          </div>
        )}
      </div>
    </div>
  );
}

export default App;
