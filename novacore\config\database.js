/**
 * NovaCore Database Configuration
 * 
 * This file provides database connection functionality for the NovaCore API server.
 */

const mongoose = require('mongoose');
const config = require('./index');
const logger = require('./logger');

/**
 * Database connection manager
 */
const databaseManager = {
  connection: null,
  
  /**
   * Connect to the database
   * @returns {Promise<mongoose.Connection>} - Mongoose connection
   */
  async connect() {
    try {
      if (this.connection) {
        logger.info('Using existing database connection');
        return this.connection;
      }
      
      // Set mongoose options
      mongoose.set('strictQuery', false);
      
      // Connect to MongoDB
      await mongoose.connect(config.database.url, {
        useNewUrlParser: true,
        useUnifiedTopology: true
      });
      
      this.connection = mongoose.connection;
      
      // Log successful connection
      logger.info(`Connected to MongoDB at ${config.database.host}:${config.database.port}/${config.database.name}`);
      
      // Handle connection events
      this.connection.on('error', (err) => {
        logger.error('MongoDB connection error:', err);
      });
      
      this.connection.on('disconnected', () => {
        logger.warn('MongoDB disconnected');
      });
      
      this.connection.on('reconnected', () => {
        logger.info('MongoDB reconnected');
      });
      
      return this.connection;
    } catch (error) {
      logger.error('Failed to connect to MongoDB:', error);
      throw error;
    }
  },
  
  /**
   * Disconnect from the database
   * @returns {Promise<void>}
   */
  async disconnect() {
    if (this.connection) {
      await mongoose.disconnect();
      this.connection = null;
      logger.info('Disconnected from MongoDB');
    }
  }
};

module.exports = databaseManager;
