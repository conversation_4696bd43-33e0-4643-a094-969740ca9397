{"name": "test_castl_decision_making", "status": "passed", "description": "Test CASTL-compliant decision making.", "start": 1752966148917, "stop": 1752966148962, "uuid": "294deba6-cd26-45c9-a374-d3fe32fb013b", "historyId": "7b0049abd6d3f0937c05413b2c466271", "testCaseId": "7b0049abd6d3f0937c05413b2c466271", "fullName": "tests.novacortex.test_integration#test_castl_decision_making", "labels": [{"name": "tag", "value": "asyncio"}, {"name": "parentSuite", "value": "tests.novacortex"}, {"name": "suite", "value": "test_integration"}, {"name": "host", "value": "d1cae64bda82"}, {"name": "thread", "value": "1-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.novacortex.test_integration"}]}