/**
 * Async Handler Middleware
 * 
 * This middleware wraps async route handlers to catch errors and pass them to the next middleware.
 */

/**
 * Wrap an async function to catch errors and pass them to the next middleware
 * @param {Function} fn - Async function to wrap
 * @returns {Function} - Express middleware function
 */
const asyncHandler = (fn) => (req, res, next) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

module.exports = {
  asyncHandler
};
