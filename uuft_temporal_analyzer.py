#!/usr/bin/env python3
"""
UUFT Temporal Pattern Analyzer

This module analyzes time series data for 18/82 patterns and π10³ relationships,
measuring their stability and resilience over time. It implements the Temporal
Stability Quotient (TSQ) to quantify pattern persistence.
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime
import os
import logging
from scipy import stats
from statsmodels.tsa.stattools import acf, pacf
import json

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('uuft_temporal_analysis.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('UUFT_Temporal_Analysis')

# Constants
PATTERN_1882_RATIO = 18 / 82
PI = np.pi
PI_10_CUBED = PI * 10**3
RESULTS_DIR = "uuft_results/temporal"
os.makedirs(RESULTS_DIR, exist_ok=True)

class UUFTTemporalAnalyzer:
    """Analyzer for detecting and measuring UUFT patterns in time series data."""

    def __init__(self, pattern_threshold=0.05):
        """
        Initialize the temporal analyzer.

        Args:
            pattern_threshold: Threshold for considering a match to the 18/82 pattern (0-1)
        """
        self.pattern_threshold = pattern_threshold
        logger.info(f"Initialized temporal analyzer with pattern threshold {pattern_threshold}")

    def load_data(self, file_path):
        """
        Load time series data from a CSV file.

        Args:
            file_path: Path to the CSV file

        Returns:
            DataFrame with time series data
        """
        df = pd.read_csv(file_path)

        # Convert timestamp to datetime if it's a string
        if 'timestamp' in df.columns and isinstance(df['timestamp'].iloc[0], str):
            df['timestamp'] = pd.to_datetime(df['timestamp'])

        logger.info(f"Loaded data from {file_path} with {len(df)} rows")
        return df

    def calculate_1882_ratio(self, values):
        """
        Calculate the best 18/82 split for a set of values.

        Args:
            values: Array of values to analyze

        Returns:
            Dict with ratio information
        """
        # Sort the values
        sorted_values = np.sort(values)
        total_sum = np.sum(sorted_values)

        # Find the best 18/82 split
        best_split_idx = None
        best_proximity = float('inf')

        for i in range(1, len(sorted_values)):
            lower_sum = np.sum(sorted_values[:i])
            upper_sum = np.sum(sorted_values[i:])

            if total_sum == 0:
                continue

            lower_ratio = lower_sum / total_sum
            upper_ratio = upper_sum / total_sum

            # Calculate proximity to 18/82 ratio
            proximity_to_1882 = abs((lower_ratio / upper_ratio) - PATTERN_1882_RATIO)

            if proximity_to_1882 < best_proximity:
                best_proximity = proximity_to_1882
                best_split_idx = i

        if best_split_idx is None:
            logger.warning("Could not find a valid 18/82 split")
            return None

        # Calculate the actual ratios
        lower_sum = np.sum(sorted_values[:best_split_idx])
        upper_sum = np.sum(sorted_values[best_split_idx:])

        if total_sum == 0:
            lower_ratio = 0
            upper_ratio = 0
        else:
            lower_ratio = lower_sum / total_sum
            upper_ratio = upper_sum / total_sum

        # Calculate proximity to 18/82
        proximity_percent = abs((lower_ratio / upper_ratio) - PATTERN_1882_RATIO) / PATTERN_1882_RATIO * 100
        is_1882_pattern = proximity_percent <= self.pattern_threshold * 100

        result = {
            "total_data_points": len(values),
            "split_index": best_split_idx,
            "lower_sum": float(lower_sum),
            "upper_sum": float(upper_sum),
            "lower_ratio": float(lower_ratio),
            "upper_ratio": float(upper_ratio),
            "proximity_to_1882_percent": float(proximity_percent),
            "is_1882_pattern": is_1882_pattern
        }

        return result

    def calculate_pi_relationships(self, values):
        """
        Calculate π relationships in a set of values.

        Args:
            values: Array of values to analyze

        Returns:
            Dict with π relationship information
        """
        pi_values = []
        pi_ratios = []

        # Check for values close to Pi
        for i, value in enumerate(values):
            if abs(value - PI) / PI < 0.05:
                pi_values.append({
                    "index": i,
                    "value": float(value),
                    "proximity_percent": float(abs(value - PI) / PI * 100)
                })

        # Check for ratios close to Pi
        for i in range(len(values)):
            for j in range(i+1, min(i+100, len(values))):  # Limit to nearby indices for performance
                if values[i] == 0 or values[j] == 0:
                    continue

                ratio = values[i] / values[j]
                if abs(ratio - PI) / PI < 0.05:
                    pi_ratios.append({
                        "indices": [i, j],
                        "ratio": float(ratio),
                        "proximity_percent": float(abs(ratio - PI) / PI * 100)
                    })

                ratio = values[j] / values[i]
                if abs(ratio - PI) / PI < 0.05:
                    pi_ratios.append({
                        "indices": [j, i],
                        "ratio": float(ratio),
                        "proximity_percent": float(abs(ratio - PI) / PI * 100)
                    })

        # Check for π*10³ relationships
        pi_10_cubed_relationships = []
        for i, value in enumerate(values):
            if abs(value - PI_10_CUBED) / PI_10_CUBED < 0.05:
                pi_10_cubed_relationships.append({
                    "index": i,
                    "value": float(value),
                    "proximity_percent": float(abs(value - PI_10_CUBED) / PI_10_CUBED * 100)
                })

        result = {
            "total_data_points": len(values),
            "pi_values_count": len(pi_values),
            "pi_ratios_count": len(pi_ratios),
            "pi_10_cubed_count": len(pi_10_cubed_relationships),
            "pi_values": pi_values[:10],  # Limit to first 10 for brevity
            "pi_ratios": pi_ratios[:10],  # Limit to first 10 for brevity
            "pi_10_cubed_relationships": pi_10_cubed_relationships[:10]  # Limit to first 10 for brevity
        }

        return result

    def calculate_temporal_stability_quotient(self, df, window_size=30, step_size=7):
        """
        Calculate the Temporal Stability Quotient (TSQ) for 18/82 patterns.

        The TSQ measures how stable the 18/82 pattern is over time, with values
        ranging from 0 (completely unstable) to 1 (perfectly stable).

        Args:
            df: DataFrame with time series data
            window_size: Size of the rolling window in days
            step_size: Step size for the rolling window in days

        Returns:
            Dict with TSQ information
        """
        if 'timestamp' not in df.columns or 'value' not in df.columns:
            logger.error("DataFrame must have 'timestamp' and 'value' columns")
            return None

        # Ensure timestamp is datetime
        df['timestamp'] = pd.to_datetime(df['timestamp'])

        # Sort by timestamp
        df = df.sort_values('timestamp')

        # Create windows
        windows = []
        start_idx = 0

        while start_idx + window_size <= len(df):
            window = df.iloc[start_idx:start_idx + window_size]
            windows.append(window)
            start_idx += step_size

        # Calculate 18/82 ratio for each window
        window_results = []

        for i, window in enumerate(windows):
            result = self.calculate_1882_ratio(window['value'].values)
            if result:
                result['window_start'] = window['timestamp'].iloc[0]
                result['window_end'] = window['timestamp'].iloc[-1]
                result['window_index'] = i
                window_results.append(result)

        # Calculate stability metrics
        if not window_results:
            logger.warning("No valid windows for TSQ calculation")
            return None

        # Extract proximity values
        proximities = [r['proximity_to_1882_percent'] for r in window_results]

        # Calculate stability metrics
        mean_proximity = np.mean(proximities)
        std_proximity = np.std(proximities)
        max_proximity = np.max(proximities)

        # Calculate TSQ
        # TSQ is 1 when all windows have 0% proximity (perfect match)
        # TSQ approaches 0 as proximity increases or becomes more variable
        tsq = 1 - (mean_proximity / 100) * (1 + std_proximity / mean_proximity)
        tsq = max(0, min(1, tsq))  # Clamp to [0, 1]

        # Calculate pattern persistence
        pattern_matches = sum(1 for r in window_results if r['is_1882_pattern'])
        persistence = pattern_matches / len(window_results)

        result = {
            "temporal_stability_quotient": float(tsq),
            "pattern_persistence": float(persistence),
            "mean_proximity_percent": float(mean_proximity),
            "std_proximity_percent": float(std_proximity),
            "max_proximity_percent": float(max_proximity),
            "window_count": len(window_results),
            "pattern_match_count": pattern_matches,
            "window_size_days": window_size,
            "step_size_days": step_size,
            "window_results": window_results
        }

        return result

    def calculate_pi_temporal_stability(self, df, window_size=30, step_size=7):
        """
        Calculate the temporal stability of π relationships.

        Args:
            df: DataFrame with time series data
            window_size: Size of the rolling window in days
            step_size: Step size for the rolling window in days

        Returns:
            Dict with π stability information
        """
        if 'timestamp' not in df.columns or 'value' not in df.columns:
            logger.error("DataFrame must have 'timestamp' and 'value' columns")
            return None

        # Ensure timestamp is datetime
        df['timestamp'] = pd.to_datetime(df['timestamp'])

        # Sort by timestamp
        df = df.sort_values('timestamp')

        # Create windows
        windows = []
        start_idx = 0

        while start_idx + window_size <= len(df):
            window = df.iloc[start_idx:start_idx + window_size]
            windows.append(window)
            start_idx += step_size

        # Calculate π relationships for each window
        window_results = []

        for i, window in enumerate(windows):
            result = self.calculate_pi_relationships(window['value'].values)
            if result:
                result['window_start'] = window['timestamp'].iloc[0]
                result['window_end'] = window['timestamp'].iloc[-1]
                result['window_index'] = i
                window_results.append(result)

        # Calculate stability metrics
        if not window_results:
            logger.warning("No valid windows for π stability calculation")
            return None

        # Extract counts
        pi_values_counts = [r['pi_values_count'] for r in window_results]
        pi_ratios_counts = [r['pi_ratios_count'] for r in window_results]
        pi_10_cubed_counts = [r['pi_10_cubed_count'] for r in window_results]

        # Calculate stability metrics
        mean_pi_values = np.mean(pi_values_counts)
        std_pi_values = np.std(pi_values_counts)
        mean_pi_ratios = np.mean(pi_ratios_counts)
        std_pi_ratios = np.std(pi_ratios_counts)
        mean_pi_10_cubed = np.mean(pi_10_cubed_counts)
        std_pi_10_cubed = np.std(pi_10_cubed_counts)

        # Calculate stability quotients
        # Higher is more stable
        pi_values_stability = 1 - min(1, std_pi_values / (mean_pi_values + 1))
        pi_ratios_stability = 1 - min(1, std_pi_ratios / (mean_pi_ratios + 1))
        pi_10_cubed_stability = 1 - min(1, std_pi_10_cubed / (mean_pi_10_cubed + 1))

        # Overall π stability
        pi_stability = (pi_values_stability + pi_ratios_stability + pi_10_cubed_stability) / 3

        result = {
            "pi_stability_quotient": float(pi_stability),
            "pi_values_stability": float(pi_values_stability),
            "pi_ratios_stability": float(pi_ratios_stability),
            "pi_10_cubed_stability": float(pi_10_cubed_stability),
            "mean_pi_values_count": float(mean_pi_values),
            "std_pi_values_count": float(std_pi_values),
            "mean_pi_ratios_count": float(mean_pi_ratios),
            "std_pi_ratios_count": float(std_pi_ratios),
            "mean_pi_10_cubed_count": float(mean_pi_10_cubed),
            "std_pi_10_cubed_count": float(std_pi_10_cubed),
            "window_count": len(window_results),
            "window_size_days": window_size,
            "step_size_days": step_size
        }

        return result

    def visualize_temporal_stability(self, tsq_result, title="18/82 Pattern Temporal Stability", save_path=None):
        """
        Visualize the temporal stability of 18/82 patterns.

        Args:
            tsq_result: Result from calculate_temporal_stability_quotient
            title: Plot title
            save_path: Path to save the visualization
        """
        if not tsq_result or 'window_results' not in tsq_result:
            logger.error("Invalid TSQ result for visualization")
            return

        plt.figure(figsize=(12, 8))

        # Extract data
        window_starts = [pd.to_datetime(r['window_start']) for r in tsq_result['window_results']]
        proximities = [r['proximity_to_1882_percent'] for r in tsq_result['window_results']]
        is_pattern = [r['is_1882_pattern'] for r in tsq_result['window_results']]

        # Plot proximities
        plt.plot(window_starts, proximities, 'b-', alpha=0.7, label='Proximity to 18/82 (%)')

        # Highlight pattern matches
        match_windows = [window_starts[i] for i in range(len(window_starts)) if is_pattern[i]]
        match_proximities = [proximities[i] for i in range(len(proximities)) if is_pattern[i]]
        plt.scatter(match_windows, match_proximities, color='green', alpha=0.7, label='18/82 Pattern Match')

        # Highlight non-matches
        non_match_windows = [window_starts[i] for i in range(len(window_starts)) if not is_pattern[i]]
        non_match_proximities = [proximities[i] for i in range(len(proximities)) if not is_pattern[i]]
        plt.scatter(non_match_windows, non_match_proximities, color='red', alpha=0.7, label='No Pattern Match')

        # Add threshold line
        plt.axhline(y=self.pattern_threshold * 100, color='r', linestyle='--',
                   label=f'Pattern Threshold ({self.pattern_threshold * 100}%)')

        # Add TSQ and persistence
        plt.text(0.02, 0.95, f"TSQ: {tsq_result['temporal_stability_quotient']:.4f}",
                transform=plt.gca().transAxes, fontsize=12,
                bbox=dict(facecolor='white', alpha=0.8))
        plt.text(0.02, 0.90, f"Persistence: {tsq_result['pattern_persistence']:.4f}",
                transform=plt.gca().transAxes, fontsize=12,
                bbox=dict(facecolor='white', alpha=0.8))

        plt.title(title)
        plt.xlabel('Time')
        plt.ylabel('Proximity to 18/82 Pattern (%)')
        plt.legend()
        plt.grid(True, alpha=0.3)

        if save_path:
            plt.savefig(save_path, dpi=300)
            logger.info(f"Visualization saved to {save_path}")

        plt.close()

    def analyze_file(self, file_path, window_size=30, step_size=7):
        """
        Analyze a time series file for UUFT patterns and temporal stability.

        Args:
            file_path: Path to the CSV file
            window_size: Size of the rolling window in days
            step_size: Step size for the rolling window in days

        Returns:
            Dict with analysis results
        """
        logger.info(f"Analyzing file {file_path}")

        # Load data
        df = self.load_data(file_path)

        # Calculate overall 18/82 ratio
        ratio_result = self.calculate_1882_ratio(df['value'].values)

        # Calculate π relationships
        pi_result = self.calculate_pi_relationships(df['value'].values)

        # Calculate temporal stability
        tsq_result = self.calculate_temporal_stability_quotient(df, window_size, step_size)

        # Calculate π temporal stability
        pi_stability_result = self.calculate_pi_temporal_stability(df, window_size, step_size)

        # Combine results
        result = {
            "file_path": file_path,
            "data_points": len(df),
            "start_date": df['timestamp'].min().strftime('%Y-%m-%d'),
            "end_date": df['timestamp'].max().strftime('%Y-%m-%d'),
            "ratio_result": ratio_result,
            "pi_result": pi_result,
            "tsq_result": tsq_result,
            "pi_stability_result": pi_stability_result
        }

        # Create visualizations
        base_name = os.path.splitext(os.path.basename(file_path))[0]

        if tsq_result:
            self.visualize_temporal_stability(
                tsq_result,
                title=f"18/82 Pattern Temporal Stability - {base_name}",
                save_path=os.path.join(RESULTS_DIR, f"{base_name}_tsq.png")
            )

        # Save results
        with open(os.path.join(RESULTS_DIR, f"{base_name}_analysis.json"), 'w', encoding='utf-8') as f:
            # Convert any non-serializable values to strings
            json_result = json.loads(json.dumps(result, default=str))
            json.dump(json_result, f, indent=2)

        logger.info(f"Analysis completed for {file_path}")

        return result

def analyze_example_datasets():
    """Analyze example datasets."""
    logger.info("Analyzing example datasets")

    # Create analyzer
    analyzer = UUFTTemporalAnalyzer()

    # Get all CSV files in the results directory
    csv_files = [f for f in os.listdir(RESULTS_DIR) if f.endswith('.csv')]

    # Analyze each file
    results = []
    for file in csv_files:
        file_path = os.path.join(RESULTS_DIR, file)
        result = analyzer.analyze_file(file_path)
        results.append(result)

    # Create summary report
    summary = {
        "total_files_analyzed": len(results),
        "files_with_1882_pattern": sum(1 for r in results if r['ratio_result'] and r['ratio_result'].get('is_1882_pattern', False)),
        "average_tsq": float(np.mean([r['tsq_result']['temporal_stability_quotient'] for r in results if r['tsq_result'] and 'temporal_stability_quotient' in r['tsq_result']] or [0])),
        "average_pattern_persistence": float(np.mean([r['tsq_result']['pattern_persistence'] for r in results if r['tsq_result'] and 'pattern_persistence' in r['tsq_result']] or [0])),
        "total_pi_values": sum(r['pi_result']['pi_values_count'] for r in results if r['pi_result']),
        "total_pi_ratios": sum(r['pi_result']['pi_ratios_count'] for r in results if r['pi_result']),
        "total_pi_10_cubed": sum(r['pi_result']['pi_10_cubed_count'] for r in results if r['pi_result']),
        "average_pi_stability": float(np.mean([r['pi_stability_result']['pi_stability_quotient'] for r in results if r['pi_stability_result'] and 'pi_stability_quotient' in r['pi_stability_result']] or [0]))
    }

    # Save summary
    with open(os.path.join(RESULTS_DIR, "analysis_summary.json"), 'w', encoding='utf-8') as f:
        json.dump(summary, f, indent=2)

    logger.info(f"Analysis summary: {len(results)} files analyzed, {summary['files_with_1882_pattern']} with 18/82 pattern")

    return summary

if __name__ == "__main__":
    analyze_example_datasets()
