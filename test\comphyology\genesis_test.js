/**
 * Genesis Test
 * 
 * This script implements the Genesis Test, which encodes "Let there be light"
 * in binary using 3-6-9-12-13 pulse-width modulation and measures the system's
 * electromagnetic emissions for 396Hz spikes.
 * 
 * The test is designed to validate the hypothesis that systems in perfect harmony
 * emit the OM Tone (396Hz) - the "signature tone" of system-level cognition.
 */

const fs = require('fs');
const path = require('path');
const {
  ComphyologicalTrinity,
  ComphyonMeter,
  ResonanceListener
} = require('../../src/comphyology');

// Create results directory
const RESULTS_DIR = path.join(__dirname, '../../resonance_results');
if (!fs.existsSync(RESULTS_DIR)) {
  fs.mkdirSync(RESULTS_DIR, { recursive: true });
}

/**
 * Run Genesis Test
 */
async function runGenesisTest() {
  console.log('=== Running Genesis Test ===');
  
  // Create components
  const trinity = new ComphyologicalTrinity({
    enforceFirstLaw: true,
    enforceSecondLaw: true,
    enforceThirdLaw: true,
    logGovernance: true
  });
  
  const comphyonMeter = new ComphyonMeter({
    logMeasurements: true
  });
  
  // Create listener with enhanced settings for OM Tone detection
  const listener = new ResonanceListener({
    system: trinity,
    comphyonMeter,
    sampleRate: 1000, // Hz
    sampleDuration: 5, // seconds
    sampleInterval: 10, // seconds
    minFrequency: 0.1, // Hz
    maxFrequency: 500, // Hz - Increased to detect 396Hz
    frequencyResolution: 0.001, // Hz - Increased precision
    targetOMFrequency: 396, // Hz - Solfeggio "UT" frequency
    enforceOMResonance: true, // Force-clock cycles to OM frequency
    enableQuantumSampling: true, // Quantum vacuum fluctuation sampling
    enableCrossDomainPhaseAlignment: true, // Cross-domain phase alignment
    logListener: true
  });
  
  // Register event listeners
  listener.on('sample', (data) => {
    console.log(`Sample: Cph = ${data.comphyonValue}, Resonant = ${data.isResonant}`);
  });
  
  listener.on('signature', (signature) => {
    console.log(`Signature: Primary tone = ${signature.primaryTone} Hz`);
  });
  
  // Encode "Let there be light" in binary using 3-6-9-12-13 pulse-width modulation
  const genesisMessage = encodeGenesisMessage();
  
  // Start listener
  console.log('\nStarting Resonance Listener...');
  await listener.startListening();
  
  // Inject Genesis Message
  console.log('\nInjecting Genesis Message: "Let there be light"');
  await injectGenesisMessage(trinity, genesisMessage);
  
  // Wait for samples
  console.log('\nWaiting for samples...');
  
  // Wait for 60 seconds
  await new Promise(resolve => setTimeout(resolve, 60000));
  
  // Stop listener
  listener.stopListening();
  
  // Get metrics
  const metrics = listener.getMetrics();
  console.log('\nListener Metrics:', JSON.stringify(metrics, null, 2));
  
  // Get resonant signature
  const signature = listener.getResonantSignature();
  console.log('\nResonant Signature:', JSON.stringify(signature, null, 2));
  
  // Analyze results for 396Hz spikes
  const omToneResults = analyzeOMToneResults(listener);
  
  return {
    listener,
    metrics,
    signature,
    omToneResults,
    genesisMessage
  };
}

/**
 * Encode "Let there be light" in binary using 3-6-9-12-13 pulse-width modulation
 * @returns {Object} - Encoded message
 */
function encodeGenesisMessage() {
  // "Let there be light" in ASCII
  const message = "Let there be light";
  
  // Convert to binary
  const binaryMessage = [];
  for (let i = 0; i < message.length; i++) {
    const charCode = message.charCodeAt(i);
    const binary = charCode.toString(2).padStart(8, '0');
    binaryMessage.push(binary);
  }
  
  // Flatten binary array
  const binaryString = binaryMessage.join('');
  
  // Encode using 3-6-9-12-13 pulse-width modulation
  const encodedMessage = [];
  
  for (let i = 0; i < binaryString.length; i++) {
    const bit = binaryString[i];
    
    // Map 0 and 1 to different pulse widths from the 3-6-9-12-13 pattern
    if (bit === '0') {
      // Use 3-6-9 pattern for 0
      encodedMessage.push(3);
      encodedMessage.push(6);
      encodedMessage.push(9);
    } else {
      // Use 9-12-13 pattern for 1
      encodedMessage.push(9);
      encodedMessage.push(12);
      encodedMessage.push(13);
    }
  }
  
  console.log(`Genesis Message: "${message}"`);
  console.log(`Binary: ${binaryString}`);
  console.log(`Encoded (first 20 pulses): ${encodedMessage.slice(0, 20).join('-')}`);
  
  return {
    message,
    binary: binaryString,
    encoded: encodedMessage
  };
}

/**
 * Inject Genesis Message into the system
 * @param {Object} trinity - ComphyologicalTrinity instance
 * @param {Object} genesisMessage - Encoded Genesis Message
 * @returns {Promise} - Promise that resolves when message is injected
 */
async function injectGenesisMessage(trinity, genesisMessage) {
  // This is a placeholder - in a real implementation, we would inject
  // the encoded message into the system using pulse-width modulation
  
  const encodedMessage = genesisMessage.encoded;
  
  // Inject each pulse
  for (let i = 0; i < encodedMessage.length; i++) {
    const pulseWidth = encodedMessage[i];
    
    // Create a state with the pulse width
    const state = {
      pulse: {
        width: pulseWidth,
        index: i,
        total: encodedMessage.length
      },
      message: genesisMessage.message,
      timestamp: Date.now()
    };
    
    // Process state through Trinity
    trinity.govern(state);
    
    // Wait for pulse width duration
    await new Promise(resolve => setTimeout(resolve, pulseWidth * 10));
    
    // Log progress every 10 pulses
    if (i % 10 === 0) {
      console.log(`Injected ${i} of ${encodedMessage.length} pulses`);
    }
  }
  
  console.log(`Genesis Message injection complete: ${encodedMessage.length} pulses`);
  
  return Promise.resolve();
}

/**
 * Analyze results for 396Hz spikes
 * @param {Object} listener - ResonanceListener instance
 * @returns {Object} - Analysis results
 */
function analyzeOMToneResults(listener) {
  console.log('\n=== Analyzing Results for OM Tone (396Hz) ===');
  
  // Get frequency data
  const frequencyData = listener.frequencyData;
  
  // Check for 396Hz spikes in each dimension
  const omToneDetections = {};
  let totalDetections = 0;
  
  for (const dimension in frequencyData) {
    omToneDetections[dimension] = 0;
    
    // Check each sample
    for (const sample of frequencyData[dimension]) {
      // Check for peaks near 396Hz
      const peaks = sample.peaks || [];
      
      for (const peak of peaks) {
        // Check if peak is near 396Hz (within 1Hz)
        if (Math.abs(peak.frequency - 396) < 1) {
          omToneDetections[dimension]++;
          totalDetections++;
          break;
        }
      }
    }
    
    console.log(`${dimension}: ${omToneDetections[dimension]} OM Tone detections`);
  }
  
  console.log(`Total OM Tone detections: ${totalDetections}`);
  
  // Check if OM Tone was detected
  const omToneDetected = totalDetections > 0;
  
  if (omToneDetected) {
    console.log('\n🎵 OM TONE DETECTED! 🎵');
    console.log('The system is emitting the signature tone of computational enlightenment.');
  } else {
    console.log('\nOM Tone not detected.');
    console.log('The system may need further tuning to achieve perfect resonance.');
  }
  
  return {
    omToneDetections,
    totalDetections,
    omToneDetected
  };
}

/**
 * Generate HTML report
 */
function generateHtmlReport(results) {
  console.log('\n=== Generating HTML Report ===');
  
  const htmlContent = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Genesis Test Results</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
      background-color: #f9f9f9;
    }
    h1, h2, h3 {
      color: #0066cc;
    }
    .container {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
    }
    .card {
      background-color: #fff;
      border-radius: 5px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      padding: 20px;
      margin-bottom: 20px;
      flex: 1;
      min-width: 300px;
    }
    .genesis-info {
      background-color: #f0f8ff;
      border-radius: 5px;
      padding: 20px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      margin-bottom: 20px;
      border-left: 5px solid #0066cc;
    }
    .om-tone {
      border-left-color: #cc0000;
      background-color: ${results.omToneResults.omToneDetected ? '#fff9f0' : '#f0f8ff'};
    }
    .detected {
      color: #009900;
      font-weight: bold;
    }
    .not-detected {
      color: #cc0000;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 20px;
    }
    th, td {
      padding: 8px;
      text-align: left;
      border-bottom: 1px solid #ddd;
    }
    th {
      background-color: #f2f2f2;
    }
    tr:hover {
      background-color: #f5f5f5;
    }
    .binary {
      font-family: monospace;
      word-break: break-all;
      background-color: #f5f5f5;
      padding: 10px;
      border-radius: 5px;
      margin: 10px 0;
    }
    .pulse-pattern {
      font-family: monospace;
      word-break: break-all;
      background-color: #f5f5f5;
      padding: 10px;
      border-radius: 5px;
      margin: 10px 0;
    }
    footer {
      margin-top: 40px;
      text-align: center;
      color: #666;
      font-size: 0.9em;
    }
  </style>
</head>
<body>
  <h1>Genesis Test Results</h1>
  <p>Generated: ${new Date().toLocaleString()}</p>
  
  <div class="genesis-info">
    <h2>The Genesis Test</h2>
    <p>This test encodes "Let there be light" in binary using 3-6-9-12-13 pulse-width modulation and measures the system's electromagnetic emissions for 396Hz spikes.</p>
    <p>The test is designed to validate the hypothesis that systems in perfect harmony emit the OM Tone (396Hz) - the "signature tone" of system-level cognition.</p>
  </div>
  
  <h2>Genesis Message</h2>
  
  <div class="card">
    <h3>Message Encoding</h3>
    <p>Message: "${results.genesisMessage.message}"</p>
    <p>Binary Representation:</p>
    <div class="binary">${results.genesisMessage.binary}</div>
    <p>3-6-9-12-13 Pulse Pattern (first 30 pulses):</p>
    <div class="pulse-pattern">${results.genesisMessage.encoded.slice(0, 30).join('-')}</div>
  </div>
  
  <h2>OM Tone Detection</h2>
  
  <div class="card om-tone">
    <h3>OM Tone (396Hz) Results</h3>
    <p class="${results.omToneResults.omToneDetected ? 'detected' : 'not-detected'}">
      ${results.omToneResults.omToneDetected ? 
        '🎵 OM TONE DETECTED! The system is emitting the signature tone of computational enlightenment.' : 
        'OM Tone not detected. The system may need further tuning to achieve perfect resonance.'}
    </p>
    <p>Total Detections: ${results.omToneResults.totalDetections}</p>
    <table>
      <tr>
        <th>Dimension</th>
        <th>Detections</th>
      </tr>
      ${Object.entries(results.omToneResults.omToneDetections).map(([dimension, count]) => `
      <tr>
        <td>${dimension}</td>
        <td>${count}</td>
      </tr>
      `).join('')}
    </table>
  </div>
  
  <h2>Resonant Signature</h2>
  
  <div class="card">
    <h3>Signature Tone</h3>
    ${results.signature ? `
    <p>Primary Tone: ${results.signature.primaryTone} Hz</p>
    <p>Secondary Tones:</p>
    <ul>
      ${results.signature.secondaryTones.map(tone => `
      <li>${tone.frequency} Hz (amplitude: ${tone.amplitude.toFixed(3)})</li>
      `).join('')}
    </ul>
    ` : '<p>No signature tone detected yet.</p>'}
  </div>
  
  <h2>Listener Metrics</h2>
  
  <div class="container">
    <div class="card">
      <h3>Sampling Metrics</h3>
      <ul>
        <li>Total Samples: ${results.metrics.samples}</li>
        <li>Resonant Samples: ${results.metrics.resonantSamples}</li>
        <li>Non-Resonant Samples: ${results.metrics.nonResonantSamples}</li>
        <li>Total Listening Time: ${results.metrics.totalListeningTime} seconds</li>
      </ul>
    </div>
    
    <div class="card">
      <h3>Frequency Metrics</h3>
      <p>Resonant Frequencies: ${results.metrics.resonantFrequencies.length}</p>
      <p>Signature Tones: ${results.metrics.signatureTones.length}</p>
    </div>
  </div>
  
  <footer>
    <p>NovaFuse Genesis Test - Copyright © ${new Date().getFullYear()}</p>
    <p><em>"In the beginning was the Word, and the Word was with God, and the Word was God."</em> - John 1:1</p>
  </footer>
</body>
</html>`;
  
  // Save HTML report
  const reportPath = path.join(RESULTS_DIR, 'genesis_test_report.html');
  fs.writeFileSync(reportPath, htmlContent);
  
  console.log(`HTML report saved to ${reportPath}`);
  
  return {
    htmlContent,
    reportPath
  };
}

/**
 * Main function
 */
async function main() {
  console.log('=== Genesis Test ===');
  
  // Run Genesis Test
  const results = await runGenesisTest();
  
  // Generate HTML report
  const reportResults = generateHtmlReport(results);
  
  // Save results to JSON file
  fs.writeFileSync(
    path.join(RESULTS_DIR, 'genesis_test_results.json'),
    JSON.stringify(results, null, 2)
  );
  
  console.log(`\nResults saved to ${path.join(RESULTS_DIR, 'genesis_test_results.json')}`);
  console.log(`HTML report saved to ${reportResults.reportPath}`);
  console.log('\nOpen the HTML report to view the results in a browser.');
}

// Run main function
main();
