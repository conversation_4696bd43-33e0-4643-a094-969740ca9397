const { v4: uuidv4 } = require('uuid');
const models = require('./models');
const { advancedFilter, search, sort, paginate } = require('./utils');
const regulatoryIntegration = require('./regulatory-integration');
const impactAssessment = require('./impact-assessment');
const consentManagement = require('./consent-management');
const dsrAutomation = require('./dsr-automation');
const analytics = require('./analytics');
const notifications = require('./notifications');

/**
 * Get a list of data processing activities
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getDataProcessingActivities = (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      status,
      purpose,
      dataSubject,
      legalBasis,
      sortBy = 'name',
      sortOrder = 'asc',
      q,
      riskLevel,
      createdAfter,
      createdBefore,
      updatedAfter,
      updatedBefore,
      dataCategory,
      securityMeasure,
      dpiaRequired,
      dpiaCompleted
    } = req.query;

    // Create filters object
    const filters = {};
    if (status) filters.status = status;
    if (legalBasis) filters.legalBasis = legalBasis;
    if (riskLevel && (riskLevel === 'high' || riskLevel === 'medium' || riskLevel === 'low')) {
      // Filter by risk level (activities with at least one risk of the specified level)
      filters._riskLevel = riskLevel; // Using underscore to indicate special handling
    }
    if (dpiaRequired !== undefined) filters['dpia.required'] = dpiaRequired === 'true';
    if (dpiaCompleted !== undefined) filters['dpia.completed'] = dpiaCompleted === 'true';

    // Filter options
    const filterOptions = {
      exactMatch: ['status', 'legalBasis', 'dpia.required', 'dpia.completed'],
      arrayContains: [],
      arrayIntersects: [],
      dateRange: {}
    };

    // Add date range filters
    if (createdAfter || createdBefore) {
      filterOptions.dateRange.createdAt = {
        from: createdAfter,
        to: createdBefore
      };
    }

    if (updatedAfter || updatedBefore) {
      filterOptions.dateRange.updatedAt = {
        from: updatedAfter,
        to: updatedBefore
      };
    }

    // Apply basic filters
    let filteredActivities = advancedFilter(models.dataProcessingActivities, filters, filterOptions);

    // Apply special filters that need custom logic
    if (purpose) {
      filteredActivities = filteredActivities.filter(activity =>
        activity.purpose.toLowerCase().includes(purpose.toLowerCase())
      );
    }

    if (dataSubject) {
      filteredActivities = filteredActivities.filter(activity =>
        activity.dataSubjects.some(subject =>
          subject.toLowerCase().includes(dataSubject.toLowerCase())
        )
      );
    }

    if (dataCategory) {
      filteredActivities = filteredActivities.filter(activity =>
        activity.dataCategories.some(category =>
          category.toLowerCase().includes(dataCategory.toLowerCase())
        )
      );
    }

    if (securityMeasure) {
      filteredActivities = filteredActivities.filter(activity =>
        activity.securityMeasures && activity.securityMeasures.some(measure =>
          measure.toLowerCase().includes(securityMeasure.toLowerCase())
        )
      );
    }

    // Apply risk level filter with custom logic
    if (riskLevel && (riskLevel === 'high' || riskLevel === 'medium' || riskLevel === 'low')) {
      filteredActivities = filteredActivities.filter(activity =>
        activity.risks && activity.risks.some(risk => risk.impact === riskLevel || risk.likelihood === riskLevel)
      );
    }

    // Apply search if query parameter is provided
    if (q) {
      const searchFields = [
        'name', 'description', 'purpose', 'dataCategories', 'dataSubjects',
        'legalBasis', 'retentionPeriod', 'processingOperations', 'securityMeasures',
        'dataControllers', 'dataProcessors', 'risks.description', 'risks.mitigations'
      ];

      filteredActivities = search(filteredActivities, q, searchFields);
    }

    // Sort the results
    const sortedActivities = sort(filteredActivities, sortBy, sortOrder);

    // Paginate the results
    const result = paginate(sortedActivities, page, limit);

    res.json(result);
  } catch (error) {
    console.error('Error in getDataProcessingActivities:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get a specific data processing activity by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getDataProcessingActivityById = (req, res) => {
  try {
    const { id } = req.params;
    const { includeCompliance, includeRequirements, includeChanges, includeImpactAssessment } = req.query;
    const activity = models.dataProcessingActivities.find(a => a.id === id);

    if (!activity) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Data processing activity with ID ${id} not found`
      });
    }

    // Create response object
    const response = { data: activity };

    // Add compliance status if requested
    if (includeCompliance === 'true') {
      response.compliance = regulatoryIntegration.getActivityComplianceStatus(activity);
    }

    // Add relevant requirements if requested
    if (includeRequirements === 'true') {
      response.requirements = regulatoryIntegration.mapActivityToRequirements(activity);
    }

    // Add relevant regulatory changes if requested
    if (includeChanges === 'true') {
      response.regulatoryChanges = regulatoryIntegration.getRelevantRegulatoryChanges(activity);
    }

    // Add automated privacy impact assessment if requested
    if (includeImpactAssessment === 'true') {
      response.impactAssessment = impactAssessment.generateImpactAssessment(activity);
    }

    res.json(response);
  } catch (error) {
    console.error('Error in getDataProcessingActivityById:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Create a new data processing activity
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const createDataProcessingActivity = (req, res) => {
  try {
    const {
      name,
      description,
      purpose,
      dataCategories,
      dataSubjects,
      legalBasis,
      retentionPeriod,
      processingOperations,
      crossBorderTransfers,
      securityMeasures,
      dataControllers,
      dataProcessors,
      dpia,
      risks,
      status
    } = req.body;

    // Create a new activity with a unique ID
    const newActivity = {
      id: `dpa-${uuidv4().substring(0, 4)}`,
      name,
      description,
      purpose,
      dataCategories,
      dataSubjects,
      legalBasis,
      retentionPeriod,
      processingOperations,
      crossBorderTransfers: crossBorderTransfers || [],
      securityMeasures: securityMeasures || [],
      dataControllers: dataControllers || [],
      dataProcessors: dataProcessors || [],
      dpia: dpia || {
        required: false,
        completed: false,
        completionDate: null,
        reviewDate: null
      },
      risks: risks || [],
      status,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // Add the new activity to the collection
    models.dataProcessingActivities.push(newActivity);

    res.status(201).json({
      data: newActivity,
      message: 'Data processing activity created successfully'
    });
  } catch (error) {
    console.error('Error in createDataProcessingActivity:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Update an existing data processing activity
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const updateDataProcessingActivity = (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      description,
      purpose,
      dataCategories,
      dataSubjects,
      legalBasis,
      retentionPeriod,
      processingOperations,
      crossBorderTransfers,
      securityMeasures,
      dataControllers,
      dataProcessors,
      dpia,
      risks,
      status
    } = req.body;

    // Find the activity to update
    const activityIndex = models.dataProcessingActivities.findIndex(a => a.id === id);

    if (activityIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Data processing activity with ID ${id} not found`
      });
    }

    // Create updated activity object
    const updatedActivity = {
      ...models.dataProcessingActivities[activityIndex],
      name: name !== undefined ? name : models.dataProcessingActivities[activityIndex].name,
      description: description !== undefined ? description : models.dataProcessingActivities[activityIndex].description,
      purpose: purpose !== undefined ? purpose : models.dataProcessingActivities[activityIndex].purpose,
      dataCategories: dataCategories !== undefined ? dataCategories : models.dataProcessingActivities[activityIndex].dataCategories,
      dataSubjects: dataSubjects !== undefined ? dataSubjects : models.dataProcessingActivities[activityIndex].dataSubjects,
      legalBasis: legalBasis !== undefined ? legalBasis : models.dataProcessingActivities[activityIndex].legalBasis,
      retentionPeriod: retentionPeriod !== undefined ? retentionPeriod : models.dataProcessingActivities[activityIndex].retentionPeriod,
      processingOperations: processingOperations !== undefined ? processingOperations : models.dataProcessingActivities[activityIndex].processingOperations,
      crossBorderTransfers: crossBorderTransfers !== undefined ? crossBorderTransfers : models.dataProcessingActivities[activityIndex].crossBorderTransfers,
      securityMeasures: securityMeasures !== undefined ? securityMeasures : models.dataProcessingActivities[activityIndex].securityMeasures,
      dataControllers: dataControllers !== undefined ? dataControllers : models.dataProcessingActivities[activityIndex].dataControllers,
      dataProcessors: dataProcessors !== undefined ? dataProcessors : models.dataProcessingActivities[activityIndex].dataProcessors,
      dpia: dpia !== undefined ? dpia : models.dataProcessingActivities[activityIndex].dpia,
      risks: risks !== undefined ? risks : models.dataProcessingActivities[activityIndex].risks,
      status: status !== undefined ? status : models.dataProcessingActivities[activityIndex].status,
      updatedAt: new Date().toISOString()
    };

    // Replace the old activity with the updated one
    models.dataProcessingActivities[activityIndex] = updatedActivity;

    res.json({
      data: updatedActivity,
      message: 'Data processing activity updated successfully'
    });
  } catch (error) {
    console.error('Error in updateDataProcessingActivity:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Delete a data processing activity
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const deleteDataProcessingActivity = (req, res) => {
  try {
    const { id } = req.params;

    // Find the activity to delete
    const activityIndex = models.dataProcessingActivities.findIndex(a => a.id === id);

    if (activityIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Data processing activity with ID ${id} not found`
      });
    }

    // Remove the activity from the collection
    models.dataProcessingActivities.splice(activityIndex, 1);

    res.json({
      message: 'Data processing activity deleted successfully'
    });
  } catch (error) {
    console.error('Error in deleteDataProcessingActivity:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get a list of data subject requests
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getDataSubjectRequests = (req, res) => {
  try {
    const { page = 1, limit = 10, requestType, status, dataSubjectEmail, sortBy = 'requestDate', sortOrder = 'desc' } = req.query;

    // Convert page and limit to numbers
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);

    // Filter requests based on query parameters
    let filteredRequests = [...models.dataSubjectRequests];

    if (requestType) {
      filteredRequests = filteredRequests.filter(request => request.requestType === requestType);
    }

    if (status) {
      filteredRequests = filteredRequests.filter(request => request.status === status);
    }

    if (dataSubjectEmail) {
      filteredRequests = filteredRequests.filter(request =>
        request.dataSubjectEmail.toLowerCase().includes(dataSubjectEmail.toLowerCase())
      );
    }

    // Sort requests
    filteredRequests.sort((a, b) => {
      if (sortOrder.toLowerCase() === 'desc') {
        return a[sortBy] > b[sortBy] ? -1 : 1;
      }
      return a[sortBy] > b[sortBy] ? 1 : -1;
    });

    // Paginate results
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = pageNum * limitNum;
    const paginatedRequests = filteredRequests.slice(startIndex, endIndex);

    // Calculate pagination info
    const totalRequests = filteredRequests.length;
    const totalPages = Math.ceil(totalRequests / limitNum);

    res.json({
      data: paginatedRequests,
      pagination: {
        total: totalRequests,
        page: pageNum,
        limit: limitNum,
        pages: totalPages
      }
    });
  } catch (error) {
    console.error('Error in getDataSubjectRequests:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get a specific data subject request by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getDataSubjectRequestById = (req, res) => {
  try {
    const { id } = req.params;
    const request = models.dataSubjectRequests.find(r => r.id === id);

    if (!request) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Data subject request with ID ${id} not found`
      });
    }

    res.json({ data: request });
  } catch (error) {
    console.error('Error in getDataSubjectRequestById:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Create a new data subject request
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const createDataSubjectRequest = (req, res) => {
  try {
    const {
      requestType,
      dataSubjectName,
      dataSubjectEmail,
      dataSubjectId,
      identityVerified,
      verificationMethod,
      verificationDate,
      requestDetails,
      status,
      assignedTo,
      dueDate,
      affectedSystems,
      notes
    } = req.body;

    // Create a new request with a unique ID
    const newRequest = {
      id: `dsr-${uuidv4().substring(0, 4)}`,
      requestType,
      requestDate: new Date().toISOString(),
      dataSubjectName,
      dataSubjectEmail,
      dataSubjectId: dataSubjectId || null,
      identityVerified: identityVerified || false,
      verificationMethod: verificationMethod || null,
      verificationDate: verificationDate || null,
      requestDetails,
      status: status || 'pending',
      assignedTo: assignedTo || null,
      dueDate: dueDate || null,
      completionDate: null,
      responseDetails: null,
      affectedSystems: affectedSystems || [],
      notes: notes || '',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // Add the new request to the collection
    models.dataSubjectRequests.push(newRequest);

    res.status(201).json({
      data: newRequest,
      message: 'Data subject request created successfully'
    });
  } catch (error) {
    console.error('Error in createDataSubjectRequest:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Update an existing data subject request
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const updateDataSubjectRequest = (req, res) => {
  try {
    const { id } = req.params;
    const {
      requestType,
      dataSubjectName,
      dataSubjectEmail,
      dataSubjectId,
      identityVerified,
      verificationMethod,
      verificationDate,
      requestDetails,
      status,
      assignedTo,
      dueDate,
      completionDate,
      responseDetails,
      affectedSystems,
      notes
    } = req.body;

    // Find the request to update
    const requestIndex = models.dataSubjectRequests.findIndex(r => r.id === id);

    if (requestIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Data subject request with ID ${id} not found`
      });
    }

    // Create updated request object
    const updatedRequest = {
      ...models.dataSubjectRequests[requestIndex],
      requestType: requestType !== undefined ? requestType : models.dataSubjectRequests[requestIndex].requestType,
      dataSubjectName: dataSubjectName !== undefined ? dataSubjectName : models.dataSubjectRequests[requestIndex].dataSubjectName,
      dataSubjectEmail: dataSubjectEmail !== undefined ? dataSubjectEmail : models.dataSubjectRequests[requestIndex].dataSubjectEmail,
      dataSubjectId: dataSubjectId !== undefined ? dataSubjectId : models.dataSubjectRequests[requestIndex].dataSubjectId,
      identityVerified: identityVerified !== undefined ? identityVerified : models.dataSubjectRequests[requestIndex].identityVerified,
      verificationMethod: verificationMethod !== undefined ? verificationMethod : models.dataSubjectRequests[requestIndex].verificationMethod,
      verificationDate: verificationDate !== undefined ? verificationDate : models.dataSubjectRequests[requestIndex].verificationDate,
      requestDetails: requestDetails !== undefined ? requestDetails : models.dataSubjectRequests[requestIndex].requestDetails,
      status: status !== undefined ? status : models.dataSubjectRequests[requestIndex].status,
      assignedTo: assignedTo !== undefined ? assignedTo : models.dataSubjectRequests[requestIndex].assignedTo,
      dueDate: dueDate !== undefined ? dueDate : models.dataSubjectRequests[requestIndex].dueDate,
      completionDate: completionDate !== undefined ? completionDate : models.dataSubjectRequests[requestIndex].completionDate,
      responseDetails: responseDetails !== undefined ? responseDetails : models.dataSubjectRequests[requestIndex].responseDetails,
      affectedSystems: affectedSystems !== undefined ? affectedSystems : models.dataSubjectRequests[requestIndex].affectedSystems,
      notes: notes !== undefined ? notes : models.dataSubjectRequests[requestIndex].notes,
      updatedAt: new Date().toISOString()
    };

    // Replace the old request with the updated one
    models.dataSubjectRequests[requestIndex] = updatedRequest;

    res.json({
      data: updatedRequest,
      message: 'Data subject request updated successfully'
    });
  } catch (error) {
    console.error('Error in updateDataSubjectRequest:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Delete a data subject request
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const deleteDataSubjectRequest = (req, res) => {
  try {
    const { id } = req.params;

    // Find the request to delete
    const requestIndex = models.dataSubjectRequests.findIndex(r => r.id === id);

    if (requestIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Data subject request with ID ${id} not found`
      });
    }

    // Remove the request from the collection
    models.dataSubjectRequests.splice(requestIndex, 1);

    res.json({
      message: 'Data subject request deleted successfully'
    });
  } catch (error) {
    console.error('Error in deleteDataSubjectRequest:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get a list of consent records
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getConsentRecords = (req, res) => {
  try {
    const { page = 1, limit = 10, dataSubjectEmail, consentType, status, sortBy = 'consentDate', sortOrder = 'desc' } = req.query;

    // Convert page and limit to numbers
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);

    // Filter records based on query parameters
    let filteredRecords = [...models.consentRecords];

    if (dataSubjectEmail) {
      filteredRecords = filteredRecords.filter(record =>
        record.dataSubjectEmail.toLowerCase().includes(dataSubjectEmail.toLowerCase())
      );
    }

    if (consentType) {
      filteredRecords = filteredRecords.filter(record => record.consentType === consentType);
    }

    if (status) {
      filteredRecords = filteredRecords.filter(record => record.status === status);
    }

    // Sort records
    filteredRecords.sort((a, b) => {
      if (sortOrder.toLowerCase() === 'desc') {
        return a[sortBy] > b[sortBy] ? -1 : 1;
      }
      return a[sortBy] > b[sortBy] ? 1 : -1;
    });

    // Paginate results
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = pageNum * limitNum;
    const paginatedRecords = filteredRecords.slice(startIndex, endIndex);

    // Calculate pagination info
    const totalRecords = filteredRecords.length;
    const totalPages = Math.ceil(totalRecords / limitNum);

    res.json({
      data: paginatedRecords,
      pagination: {
        total: totalRecords,
        page: pageNum,
        limit: limitNum,
        pages: totalPages
      }
    });
  } catch (error) {
    console.error('Error in getConsentRecords:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get a specific consent record by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getConsentRecordById = (req, res) => {
  try {
    const { id } = req.params;
    const record = models.consentRecords.find(r => r.id === id);

    if (!record) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Consent record with ID ${id} not found`
      });
    }

    res.json({ data: record });
  } catch (error) {
    console.error('Error in getConsentRecordById:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Create a new consent record
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const createConsentRecord = (req, res) => {
  try {
    const {
      dataSubjectId,
      dataSubjectName,
      dataSubjectEmail,
      consentType,
      consentDescription,
      consentGiven,
      consentExpiryDate,
      consentProof,
      consentVersion,
      consentMethod,
      privacyNoticeVersion
    } = req.body;

    // Create a new consent record with a unique ID
    const newRecord = {
      id: `con-${uuidv4().substring(0, 4)}`,
      dataSubjectId,
      dataSubjectName,
      dataSubjectEmail,
      consentType,
      consentDescription,
      consentGiven: consentGiven !== undefined ? consentGiven : true,
      consentDate: new Date().toISOString(),
      consentExpiryDate: consentExpiryDate || null,
      consentProof: consentProof || '',
      consentVersion,
      consentMethod,
      privacyNoticeVersion,
      withdrawalDate: null,
      withdrawalMethod: null,
      status: consentGiven !== undefined && consentGiven === false ? 'declined' : 'active',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // Add the new record to the collection
    models.consentRecords.push(newRecord);

    res.status(201).json({
      data: newRecord,
      message: 'Consent record created successfully'
    });
  } catch (error) {
    console.error('Error in createConsentRecord:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Update an existing consent record
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const updateConsentRecord = (req, res) => {
  try {
    const { id } = req.params;
    const {
      dataSubjectId,
      dataSubjectName,
      dataSubjectEmail,
      consentType,
      consentDescription,
      consentGiven,
      consentExpiryDate,
      consentProof,
      consentVersion,
      consentMethod,
      privacyNoticeVersion,
      withdrawalDate,
      withdrawalMethod,
      status
    } = req.body;

    // Find the record to update
    const recordIndex = models.consentRecords.findIndex(r => r.id === id);

    if (recordIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Consent record with ID ${id} not found`
      });
    }

    // Create updated record object
    const updatedRecord = {
      ...models.consentRecords[recordIndex],
      dataSubjectId: dataSubjectId !== undefined ? dataSubjectId : models.consentRecords[recordIndex].dataSubjectId,
      dataSubjectName: dataSubjectName !== undefined ? dataSubjectName : models.consentRecords[recordIndex].dataSubjectName,
      dataSubjectEmail: dataSubjectEmail !== undefined ? dataSubjectEmail : models.consentRecords[recordIndex].dataSubjectEmail,
      consentType: consentType !== undefined ? consentType : models.consentRecords[recordIndex].consentType,
      consentDescription: consentDescription !== undefined ? consentDescription : models.consentRecords[recordIndex].consentDescription,
      consentGiven: consentGiven !== undefined ? consentGiven : models.consentRecords[recordIndex].consentGiven,
      consentExpiryDate: consentExpiryDate !== undefined ? consentExpiryDate : models.consentRecords[recordIndex].consentExpiryDate,
      consentProof: consentProof !== undefined ? consentProof : models.consentRecords[recordIndex].consentProof,
      consentVersion: consentVersion !== undefined ? consentVersion : models.consentRecords[recordIndex].consentVersion,
      consentMethod: consentMethod !== undefined ? consentMethod : models.consentRecords[recordIndex].consentMethod,
      privacyNoticeVersion: privacyNoticeVersion !== undefined ? privacyNoticeVersion : models.consentRecords[recordIndex].privacyNoticeVersion,
      withdrawalDate: withdrawalDate !== undefined ? withdrawalDate : models.consentRecords[recordIndex].withdrawalDate,
      withdrawalMethod: withdrawalMethod !== undefined ? withdrawalMethod : models.consentRecords[recordIndex].withdrawalMethod,
      status: status !== undefined ? status : models.consentRecords[recordIndex].status,
      updatedAt: new Date().toISOString()
    };

    // Replace the old record with the updated one
    models.consentRecords[recordIndex] = updatedRecord;

    res.json({
      data: updatedRecord,
      message: 'Consent record updated successfully'
    });
  } catch (error) {
    console.error('Error in updateConsentRecord:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Delete a consent record
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const deleteConsentRecord = (req, res) => {
  try {
    const { id } = req.params;

    // Find the record to delete
    const recordIndex = models.consentRecords.findIndex(r => r.id === id);

    if (recordIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Consent record with ID ${id} not found`
      });
    }

    // Remove the record from the collection
    models.consentRecords.splice(recordIndex, 1);

    res.json({
      message: 'Consent record deleted successfully'
    });
  } catch (error) {
    console.error('Error in deleteConsentRecord:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get a list of privacy notices
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getPrivacyNotices = (req, res) => {
  try {
    const { page = 1, limit = 10, status, audience, language, sortBy = 'effectiveDate', sortOrder = 'desc' } = req.query;

    // Convert page and limit to numbers
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);

    // Filter notices based on query parameters
    let filteredNotices = [...models.privacyNotices];

    if (status) {
      filteredNotices = filteredNotices.filter(notice => notice.status === status);
    }

    if (audience) {
      filteredNotices = filteredNotices.filter(notice => notice.audience === audience);
    }

    if (language) {
      filteredNotices = filteredNotices.filter(notice => notice.language === language);
    }

    // Sort notices
    filteredNotices.sort((a, b) => {
      if (sortOrder.toLowerCase() === 'desc') {
        return a[sortBy] > b[sortBy] ? -1 : 1;
      }
      return a[sortBy] > b[sortBy] ? 1 : -1;
    });

    // Paginate results
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = pageNum * limitNum;
    const paginatedNotices = filteredNotices.slice(startIndex, endIndex);

    // Calculate pagination info
    const totalNotices = filteredNotices.length;
    const totalPages = Math.ceil(totalNotices / limitNum);

    res.json({
      data: paginatedNotices,
      pagination: {
        total: totalNotices,
        page: pageNum,
        limit: limitNum,
        pages: totalPages
      }
    });
  } catch (error) {
    console.error('Error in getPrivacyNotices:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get a specific privacy notice by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getPrivacyNoticeById = (req, res) => {
  try {
    const { id } = req.params;
    const notice = models.privacyNotices.find(n => n.id === id);

    if (!notice) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Privacy notice with ID ${id} not found`
      });
    }

    res.json({ data: notice });
  } catch (error) {
    console.error('Error in getPrivacyNoticeById:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Create a new privacy notice
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const createPrivacyNotice = (req, res) => {
  try {
    const {
      title,
      version,
      effectiveDate,
      status,
      audience,
      language,
      format,
      content,
      contentUrl,
      previousVersions,
      reviewCycle,
      nextReviewDate,
      approvedBy,
      approvalDate
    } = req.body;

    // Create a new privacy notice with a unique ID
    const newNotice = {
      id: `pn-${uuidv4().substring(0, 4)}`,
      title,
      version,
      effectiveDate,
      lastUpdated: new Date().toISOString(),
      status,
      audience,
      language,
      format,
      content,
      contentUrl: contentUrl || '',
      previousVersions: previousVersions || [],
      reviewCycle,
      nextReviewDate,
      approvedBy,
      approvalDate,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // Add the new notice to the collection
    models.privacyNotices.push(newNotice);

    res.status(201).json({
      data: newNotice,
      message: 'Privacy notice created successfully'
    });
  } catch (error) {
    console.error('Error in createPrivacyNotice:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Update an existing privacy notice
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const updatePrivacyNotice = (req, res) => {
  try {
    const { id } = req.params;
    const {
      title,
      version,
      effectiveDate,
      status,
      audience,
      language,
      format,
      content,
      contentUrl,
      previousVersions,
      reviewCycle,
      nextReviewDate,
      approvedBy,
      approvalDate
    } = req.body;

    // Find the notice to update
    const noticeIndex = models.privacyNotices.findIndex(n => n.id === id);

    if (noticeIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Privacy notice with ID ${id} not found`
      });
    }

    // Create updated notice object
    const updatedNotice = {
      ...models.privacyNotices[noticeIndex],
      title: title !== undefined ? title : models.privacyNotices[noticeIndex].title,
      version: version !== undefined ? version : models.privacyNotices[noticeIndex].version,
      effectiveDate: effectiveDate !== undefined ? effectiveDate : models.privacyNotices[noticeIndex].effectiveDate,
      lastUpdated: new Date().toISOString(),
      status: status !== undefined ? status : models.privacyNotices[noticeIndex].status,
      audience: audience !== undefined ? audience : models.privacyNotices[noticeIndex].audience,
      language: language !== undefined ? language : models.privacyNotices[noticeIndex].language,
      format: format !== undefined ? format : models.privacyNotices[noticeIndex].format,
      content: content !== undefined ? content : models.privacyNotices[noticeIndex].content,
      contentUrl: contentUrl !== undefined ? contentUrl : models.privacyNotices[noticeIndex].contentUrl,
      previousVersions: previousVersions !== undefined ? previousVersions : models.privacyNotices[noticeIndex].previousVersions,
      reviewCycle: reviewCycle !== undefined ? reviewCycle : models.privacyNotices[noticeIndex].reviewCycle,
      nextReviewDate: nextReviewDate !== undefined ? nextReviewDate : models.privacyNotices[noticeIndex].nextReviewDate,
      approvedBy: approvedBy !== undefined ? approvedBy : models.privacyNotices[noticeIndex].approvedBy,
      approvalDate: approvalDate !== undefined ? approvalDate : models.privacyNotices[noticeIndex].approvalDate,
      updatedAt: new Date().toISOString()
    };

    // Replace the old notice with the updated one
    models.privacyNotices[noticeIndex] = updatedNotice;

    res.json({
      data: updatedNotice,
      message: 'Privacy notice updated successfully'
    });
  } catch (error) {
    console.error('Error in updatePrivacyNotice:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Delete a privacy notice
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const deletePrivacyNotice = (req, res) => {
  try {
    const { id } = req.params;

    // Find the notice to delete
    const noticeIndex = models.privacyNotices.findIndex(n => n.id === id);

    if (noticeIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Privacy notice with ID ${id} not found`
      });
    }

    // Remove the notice from the collection
    models.privacyNotices.splice(noticeIndex, 1);

    res.json({
      message: 'Privacy notice deleted successfully'
    });
  } catch (error) {
    console.error('Error in deletePrivacyNotice:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get a list of data breaches
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getDataBreaches = (req, res) => {
  try {
    const { page = 1, limit = 10, status, breachType, potentialImpact, sortBy = 'detectionDate', sortOrder = 'desc' } = req.query;

    // Convert page and limit to numbers
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);

    // Filter breaches based on query parameters
    let filteredBreaches = [...models.dataBreaches];

    if (status) {
      filteredBreaches = filteredBreaches.filter(breach => breach.status === status);
    }

    if (breachType) {
      filteredBreaches = filteredBreaches.filter(breach => breach.breachType === breachType);
    }

    if (potentialImpact) {
      filteredBreaches = filteredBreaches.filter(breach => breach.potentialImpact === potentialImpact);
    }

    // Sort breaches
    filteredBreaches.sort((a, b) => {
      if (sortOrder.toLowerCase() === 'desc') {
        return a[sortBy] > b[sortBy] ? -1 : 1;
      }
      return a[sortBy] > b[sortBy] ? 1 : -1;
    });

    // Paginate results
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = pageNum * limitNum;
    const paginatedBreaches = filteredBreaches.slice(startIndex, endIndex);

    // Calculate pagination info
    const totalBreaches = filteredBreaches.length;
    const totalPages = Math.ceil(totalBreaches / limitNum);

    res.json({
      data: paginatedBreaches,
      pagination: {
        total: totalBreaches,
        page: pageNum,
        limit: limitNum,
        pages: totalPages
      }
    });
  } catch (error) {
    console.error('Error in getDataBreaches:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get a specific data breach by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getDataBreachById = (req, res) => {
  try {
    const { id } = req.params;
    const breach = models.dataBreaches.find(b => b.id === id);

    if (!breach) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Data breach with ID ${id} not found`
      });
    }

    res.json({ data: breach });
  } catch (error) {
    console.error('Error in getDataBreachById:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Create a new data breach
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const createDataBreach = (req, res) => {
  try {
    const {
      title,
      description,
      breachType,
      detectionDate,
      occurrenceDate,
      affectedDataCategories,
      affectedDataSubjects,
      approximateSubjectsCount,
      potentialImpact,
      containmentStatus,
      containmentDate,
      containmentMeasures,
      rootCause,
      remedialActions,
      notificationStatus,
      investigationStatus,
      investigationReport,
      status
    } = req.body;

    // Create a new data breach with a unique ID
    const newBreach = {
      id: `db-${uuidv4().substring(0, 4)}`,
      title,
      description,
      breachType,
      detectionDate: detectionDate || new Date().toISOString(),
      occurrenceDate,
      affectedDataCategories: affectedDataCategories || [],
      affectedDataSubjects: affectedDataSubjects || [],
      approximateSubjectsCount: approximateSubjectsCount || 0,
      potentialImpact,
      containmentStatus: containmentStatus || 'not-contained',
      containmentDate: containmentDate || null,
      containmentMeasures: containmentMeasures || '',
      rootCause: rootCause || '',
      remedialActions: remedialActions || [],
      notificationStatus: notificationStatus || {
        authorities: {
          required: false,
          completed: false,
          date: null,
          recipient: null
        },
        dataSubjects: {
          required: false,
          completed: false,
          date: null,
          method: null
        }
      },
      investigationStatus: investigationStatus || 'pending',
      investigationReport: investigationReport || '',
      status: status || 'open',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // Add the new breach to the collection
    models.dataBreaches.push(newBreach);

    res.status(201).json({
      data: newBreach,
      message: 'Data breach created successfully'
    });
  } catch (error) {
    console.error('Error in createDataBreach:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Update an existing data breach
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const updateDataBreach = (req, res) => {
  try {
    const { id } = req.params;
    const {
      title,
      description,
      breachType,
      detectionDate,
      occurrenceDate,
      affectedDataCategories,
      affectedDataSubjects,
      approximateSubjectsCount,
      potentialImpact,
      containmentStatus,
      containmentDate,
      containmentMeasures,
      rootCause,
      remedialActions,
      notificationStatus,
      investigationStatus,
      investigationReport,
      status
    } = req.body;

    // Find the breach to update
    const breachIndex = models.dataBreaches.findIndex(b => b.id === id);

    if (breachIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Data breach with ID ${id} not found`
      });
    }

    // Create updated breach object
    const updatedBreach = {
      ...models.dataBreaches[breachIndex],
      title: title !== undefined ? title : models.dataBreaches[breachIndex].title,
      description: description !== undefined ? description : models.dataBreaches[breachIndex].description,
      breachType: breachType !== undefined ? breachType : models.dataBreaches[breachIndex].breachType,
      detectionDate: detectionDate !== undefined ? detectionDate : models.dataBreaches[breachIndex].detectionDate,
      occurrenceDate: occurrenceDate !== undefined ? occurrenceDate : models.dataBreaches[breachIndex].occurrenceDate,
      affectedDataCategories: affectedDataCategories !== undefined ? affectedDataCategories : models.dataBreaches[breachIndex].affectedDataCategories,
      affectedDataSubjects: affectedDataSubjects !== undefined ? affectedDataSubjects : models.dataBreaches[breachIndex].affectedDataSubjects,
      approximateSubjectsCount: approximateSubjectsCount !== undefined ? approximateSubjectsCount : models.dataBreaches[breachIndex].approximateSubjectsCount,
      potentialImpact: potentialImpact !== undefined ? potentialImpact : models.dataBreaches[breachIndex].potentialImpact,
      containmentStatus: containmentStatus !== undefined ? containmentStatus : models.dataBreaches[breachIndex].containmentStatus,
      containmentDate: containmentDate !== undefined ? containmentDate : models.dataBreaches[breachIndex].containmentDate,
      containmentMeasures: containmentMeasures !== undefined ? containmentMeasures : models.dataBreaches[breachIndex].containmentMeasures,
      rootCause: rootCause !== undefined ? rootCause : models.dataBreaches[breachIndex].rootCause,
      remedialActions: remedialActions !== undefined ? remedialActions : models.dataBreaches[breachIndex].remedialActions,
      notificationStatus: notificationStatus !== undefined ? notificationStatus : models.dataBreaches[breachIndex].notificationStatus,
      investigationStatus: investigationStatus !== undefined ? investigationStatus : models.dataBreaches[breachIndex].investigationStatus,
      investigationReport: investigationReport !== undefined ? investigationReport : models.dataBreaches[breachIndex].investigationReport,
      status: status !== undefined ? status : models.dataBreaches[breachIndex].status,
      updatedAt: new Date().toISOString()
    };

    // Replace the old breach with the updated one
    models.dataBreaches[breachIndex] = updatedBreach;

    res.json({
      data: updatedBreach,
      message: 'Data breach updated successfully'
    });
  } catch (error) {
    console.error('Error in updateDataBreach:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Delete a data breach
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const deleteDataBreach = (req, res) => {
  try {
    const { id } = req.params;

    // Find the breach to delete
    const breachIndex = models.dataBreaches.findIndex(b => b.id === id);

    if (breachIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Data breach with ID ${id} not found`
      });
    }

    // Remove the breach from the collection
    models.dataBreaches.splice(breachIndex, 1);

    res.json({
      message: 'Data breach deleted successfully'
    });
  } catch (error) {
    console.error('Error in deleteDataBreach:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Generate a consent form
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const generateConsentForm = (req, res) => {
  try {
    const { consentType, language = 'en' } = req.query;

    if (!consentType) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Consent type is required'
      });
    }

    const consentForm = consentManagement.generateConsentForm(consentType, language);

    res.json({ data: consentForm });
  } catch (error) {
    console.error('Error in generateConsentForm:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Verify consent validity
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const verifyConsentValidity = (req, res) => {
  try {
    const { id } = req.params;

    const validityResult = consentManagement.checkConsentValidity(id);

    res.json({ data: validityResult });
  } catch (error) {
    console.error('Error in verifyConsentValidity:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Withdraw consent
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const withdrawConsent = (req, res) => {
  try {
    const { id } = req.params;
    const { withdrawalMethod } = req.body;

    if (!withdrawalMethod) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Withdrawal method is required'
      });
    }

    const updatedRecord = consentManagement.withdrawConsent(id, withdrawalMethod);

    if (!updatedRecord) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Consent record with ID ${id} not found`
      });
    }

    res.json({
      data: updatedRecord,
      message: 'Consent withdrawn successfully'
    });
  } catch (error) {
    console.error('Error in withdrawConsent:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get consent records by data subject
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getConsentRecordsByDataSubject = (req, res) => {
  try {
    const { dataSubjectId } = req.params;

    const records = consentManagement.getConsentRecordsByDataSubject(dataSubjectId);

    res.json({ data: records });
  } catch (error) {
    console.error('Error in getConsentRecordsByDataSubject:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get consent records by email
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getConsentRecordsByEmail = (req, res) => {
  try {
    const { email } = req.params;

    const records = consentManagement.getConsentRecordsByEmail(email);

    res.json({ data: records });
  } catch (error) {
    console.error('Error in getConsentRecordsByEmail:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Verify consent proof
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const verifyConsentProof = (req, res) => {
  try {
    const { consentProof } = req.body;

    if (!consentProof) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Consent proof is required'
      });
    }

    const verificationResult = consentManagement.verifyConsentProof(consentProof);

    res.json({ data: verificationResult });
  } catch (error) {
    console.error('Error in verifyConsentProof:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Generate consent proof
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const generateConsentProof = (req, res) => {
  try {
    const { ip, userAgent, timestamp } = req.body;

    if (!ip || !userAgent) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'IP address and User Agent are required'
      });
    }

    const consentProof = consentManagement.generateConsentProof({
      ip,
      userAgent,
      timestamp
    });

    res.json({
      data: { consentProof }
    });
  } catch (error) {
    console.error('Error in generateConsentProof:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get data systems
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getDataSystems = (req, res) => {
  try {
    const { requestType } = req.query;

    let systems;
    if (requestType) {
      systems = dsrAutomation.getDataSystemsByRequestType(requestType);
    } else {
      systems = dsrAutomation.getDataSystems();
    }

    res.json({ data: systems });
  } catch (error) {
    console.error('Error in getDataSystems:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get a data system by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getDataSystemById = (req, res) => {
  try {
    const { id } = req.params;

    const system = dsrAutomation.getDataSystemById(id);

    if (!system) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Data system with ID ${id} not found`
      });
    }

    res.json({ data: system });
  } catch (error) {
    console.error('Error in getDataSystemById:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Process a data subject request
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const processDataSubjectRequest = (req, res) => {
  try {
    const { id } = req.params;

    const result = dsrAutomation.processDataSubjectRequest(id);

    if (!result.success) {
      return res.status(400).json({
        error: 'Bad Request',
        message: result.message
      });
    }

    res.json({
      data: result.request,
      message: result.message
    });
  } catch (error) {
    console.error('Error in processDataSubjectRequest:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Generate a data export for a data subject
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const generateDataExport = (req, res) => {
  try {
    const { id } = req.params;

    const result = dsrAutomation.generateDataExport(id);

    if (!result.success) {
      return res.status(400).json({
        error: 'Bad Request',
        message: result.message
      });
    }

    res.json({
      data: result.dataExport,
      message: result.message
    });
  } catch (error) {
    console.error('Error in generateDataExport:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Determine affected systems for a request
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const determineAffectedSystems = (req, res) => {
  try {
    const { id } = req.params;

    // Find the request
    const request = models.dataSubjectRequests.find(r => r.id === id);

    if (!request) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Data subject request with ID ${id} not found`
      });
    }

    const affectedSystems = dsrAutomation.determineAffectedSystems(request);

    res.json({ data: affectedSystems });
  } catch (error) {
    console.error('Error in determineAffectedSystems:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Generate a report
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const generateReport = (req, res) => {
  try {
    const { reportType } = req.params;
    const {
      period,
      startDate,
      endDate,
      groupBy
    } = req.query;

    if (!reportType || !period) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Report type and period are required'
      });
    }

    // Validate report type
    const validReportTypes = Object.values(analytics.REPORT_TYPES);
    if (!validReportTypes.includes(reportType)) {
      return res.status(400).json({
        error: 'Bad Request',
        message: `Invalid report type. Valid types are: ${validReportTypes.join(', ')}`
      });
    }

    // Validate period
    const validPeriods = Object.values(analytics.TIME_PERIODS);
    if (!validPeriods.includes(period)) {
      return res.status(400).json({
        error: 'Bad Request',
        message: `Invalid period. Valid periods are: ${validPeriods.join(', ')}`
      });
    }

    // Validate custom period
    if (period === analytics.TIME_PERIODS.CUSTOM && (!startDate || !endDate)) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Start date and end date are required for custom period'
      });
    }

    const options = {
      period,
      startDate,
      endDate,
      groupBy
    };

    const report = analytics.generateReport(reportType, options);

    res.json({ data: report });
  } catch (error) {
    console.error('Error in generateReport:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get dashboard metrics
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getDashboardMetrics = (req, res) => {
  try {
    const metrics = analytics.getDashboardMetrics();

    res.json({ data: metrics });
  } catch (error) {
    console.error('Error in getDashboardMetrics:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get notifications
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getNotifications = (req, res) => {
  try {
    const {
      recipient,
      status,
      type,
      priority,
      relatedEntityType,
      relatedEntityId,
      startDate,
      endDate
    } = req.query;

    const filters = {
      recipient,
      status,
      type,
      priority,
      relatedEntityType,
      relatedEntityId,
      startDate,
      endDate
    };

    const notificationsList = notifications.getNotifications(filters);

    res.json({ data: notificationsList });
  } catch (error) {
    console.error('Error in getNotifications:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get a notification by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getNotificationById = (req, res) => {
  try {
    const { id } = req.params;

    const notification = notifications.getNotificationById(id);

    if (!notification) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Notification with ID ${id} not found`
      });
    }

    res.json({ data: notification });
  } catch (error) {
    console.error('Error in getNotificationById:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Create a notification
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const createNotification = (req, res) => {
  try {
    const {
      type,
      title,
      message,
      priority,
      recipients,
      channels,
      relatedEntityType,
      relatedEntityId,
      metadata
    } = req.body;

    if (!type || !title || !message || !recipients || !recipients.length) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Type, title, message, and recipients are required'
      });
    }

    const notificationData = {
      type,
      title,
      message,
      priority,
      recipients,
      channels,
      relatedEntityType,
      relatedEntityId,
      metadata
    };

    const notification = notifications.createNotification(notificationData);

    res.status(201).json({
      data: notification,
      message: 'Notification created successfully'
    });
  } catch (error) {
    console.error('Error in createNotification:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Mark a notification as read
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const markNotificationAsRead = (req, res) => {
  try {
    const { id } = req.params;

    const updatedNotification = notifications.markNotificationAsRead(id);

    if (!updatedNotification) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Notification with ID ${id} not found`
      });
    }

    res.json({
      data: updatedNotification,
      message: 'Notification marked as read'
    });
  } catch (error) {
    console.error('Error in markNotificationAsRead:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Send a notification
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const sendNotification = (req, res) => {
  try {
    const { id } = req.params;

    const result = notifications.sendNotification(id);

    if (!result.success) {
      return res.status(400).json({
        error: 'Bad Request',
        message: result.message
      });
    }

    res.json({
      data: result.notification,
      message: result.message
    });
  } catch (error) {
    console.error('Error in sendNotification:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Generate notifications
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const generateNotifications = (req, res) => {
  try {
    const { type } = req.query;

    let generatedNotifications = [];

    if (type === 'dsr') {
      generatedNotifications = notifications.generateDsrNotifications();
    } else if (type === 'data-breach') {
      generatedNotifications = notifications.generateDataBreachNotifications();
    } else if (type === 'dpia') {
      generatedNotifications = notifications.generateDpiaNotifications();
    } else {
      generatedNotifications = notifications.generateAllNotifications();
    }

    res.json({
      data: generatedNotifications,
      message: `Generated ${generatedNotifications.length} notifications`
    });
  } catch (error) {
    console.error('Error in generateNotifications:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Send all pending notifications
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const sendAllPendingNotifications = (req, res) => {
  try {
    const results = notifications.sendAllPendingNotifications();

    res.json({
      data: results,
      message: `Sent ${results.sent} notifications, ${results.failed} failed`
    });
  } catch (error) {
    console.error('Error in sendAllPendingNotifications:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

// Export the controller functions
module.exports = {
  getDataProcessingActivities,
  getDataProcessingActivityById,
  createDataProcessingActivity,
  updateDataProcessingActivity,
  deleteDataProcessingActivity,
  getDataSubjectRequests,
  getDataSubjectRequestById,
  createDataSubjectRequest,
  updateDataSubjectRequest,
  deleteDataSubjectRequest,
  getConsentRecords,
  getConsentRecordById,
  createConsentRecord,
  updateConsentRecord,
  deleteConsentRecord,
  getPrivacyNotices,
  getPrivacyNoticeById,
  createPrivacyNotice,
  updatePrivacyNotice,
  deletePrivacyNotice,
  getDataBreaches,
  getDataBreachById,
  createDataBreach,
  updateDataBreach,
  deleteDataBreach,
  // Consent management functions
  generateConsentForm,
  verifyConsentValidity,
  withdrawConsent,
  getConsentRecordsByDataSubject,
  getConsentRecordsByEmail,
  verifyConsentProof,
  generateConsentProof,
  // Data subject rights automation functions
  getDataSystems,
  getDataSystemById,
  processDataSubjectRequest,
  generateDataExport,
  determineAffectedSystems,
  // Analytics and reporting functions
  generateReport,
  getDashboardMetrics,
  // Notification functions
  getNotifications,
  getNotificationById,
  createNotification,
  markNotificationAsRead,
  sendNotification,
  generateNotifications,
  sendAllPendingNotifications
};
