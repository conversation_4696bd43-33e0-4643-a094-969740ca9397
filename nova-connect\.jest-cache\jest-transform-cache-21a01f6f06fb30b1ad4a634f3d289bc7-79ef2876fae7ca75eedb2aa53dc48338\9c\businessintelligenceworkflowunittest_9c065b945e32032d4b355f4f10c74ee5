7a98965330d0c070a8a51905ec2b1b98
// Mock axios
_getJestObj().mock('axios');

// Mock logger
_getJestObj().mock('../../../../utils/logger', () => ({
  createLogger: jest.fn(() => ({
    info: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn()
  }))
}));
function _getJestObj() {
  const {
    jest
  } = require("@jest/globals");
  _getJestObj = () => jest;
  return jest;
}
/**
 * Unit tests for the Business Intelligence & Workflow Connector
 */

const axios = require('axios');
const BusinessIntelligenceWorkflowConnector = require('../../../../connector/implementations/business-intelligence-workflow');
describe('BusinessIntelligenceWorkflowConnector', () => {
  let connector;
  let mockConfig;
  let mockCredentials;
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();

    // Mock config and credentials
    mockConfig = {
      baseUrl: 'https://api.test.com'
    };
    mockCredentials = {
      clientId: 'test-client-id',
      clientSecret: 'test-client-secret',
      redirectUri: 'https://test-redirect.com'
    };

    // Create connector instance
    connector = new BusinessIntelligenceWorkflowConnector(mockConfig, mockCredentials);
  });
  describe('constructor', () => {
    it('should initialize with provided config and credentials', () => {
      expect(connector.config).toEqual(mockConfig);
      expect(connector.credentials).toEqual(mockCredentials);
      expect(connector.baseUrl).toBe(mockConfig.baseUrl);
    });
    it('should use default baseUrl if not provided', () => {
      const connectorWithDefaults = new BusinessIntelligenceWorkflowConnector();
      expect(connectorWithDefaults.baseUrl).toBe('https://api.example.com');
    });
  });
  describe('initialize', () => {
    it('should authenticate if credentials are provided', async () => {
      // Mock authenticate method
      connector.authenticate = jest.fn().mockResolvedValue();
      await connector.initialize();
      expect(connector.authenticate).toHaveBeenCalled();
    });
    it('should not authenticate if credentials are not provided', async () => {
      // Create connector without credentials
      const connectorWithoutCredentials = new BusinessIntelligenceWorkflowConnector(mockConfig, {});

      // Mock authenticate method
      connectorWithoutCredentials.authenticate = jest.fn().mockResolvedValue();
      await connectorWithoutCredentials.initialize();
      expect(connectorWithoutCredentials.authenticate).not.toHaveBeenCalled();
    });
  });
  describe('authenticate', () => {
    it('should make a POST request to the token endpoint', async () => {
      // Mock axios post response
      axios.post.mockResolvedValue({
        data: {
          access_token: 'test-access-token',
          expires_in: 3600
        }
      });
      await connector.authenticate();
      expect(axios.post).toHaveBeenCalledWith(`${mockConfig.baseUrl}/oauth2/token`, {
        grant_type: 'client_credentials',
        client_id: mockCredentials.clientId,
        client_secret: mockCredentials.clientSecret,
        scope: 'read:dashboards write:dashboards read:reports write:reports read:workflows write:workflows'
      }, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': 'application/json'
        }
      });
      expect(connector.accessToken).toBe('test-access-token');
      expect(connector.tokenExpiry).toBeDefined();
    });
    it('should throw an error if authentication fails', async () => {
      // Mock axios post error
      const errorMessage = 'Authentication failed';
      axios.post.mockRejectedValue(new Error(errorMessage));
      await expect(connector.authenticate()).rejects.toThrow(`Authentication failed: ${errorMessage}`);
    });
  });
  describe('getAuthHeaders', () => {
    it('should return authorization headers with access token', async () => {
      // Set access token and expiry
      connector.accessToken = 'test-access-token';
      connector.tokenExpiry = Date.now() + 3600000; // 1 hour from now

      const headers = await connector.getAuthHeaders();
      expect(headers).toEqual({
        'Authorization': 'Bearer test-access-token'
      });
    });
    it('should authenticate if access token is not set', async () => {
      // Mock authenticate method
      connector.authenticate = jest.fn().mockImplementation(() => {
        connector.accessToken = 'new-access-token';
        connector.tokenExpiry = Date.now() + 3600000;
      });
      const headers = await connector.getAuthHeaders();
      expect(connector.authenticate).toHaveBeenCalled();
      expect(headers).toEqual({
        'Authorization': 'Bearer new-access-token'
      });
    });
  });
  describe('listDashboards', () => {
    it('should make a GET request to the dashboards endpoint', async () => {
      // Mock getAuthHeaders method
      connector.getAuthHeaders = jest.fn().mockResolvedValue({
        'Authorization': 'Bearer test-access-token'
      });

      // Mock axios get response
      const mockResponse = {
        data: {
          data: [{
            id: 'dashboard-1',
            name: 'Dashboard 1'
          }, {
            id: 'dashboard-2',
            name: 'Dashboard 2'
          }],
          pagination: {
            page: 1,
            limit: 20,
            totalItems: 2,
            totalPages: 1
          }
        }
      };
      axios.get.mockResolvedValue(mockResponse);
      const params = {
        folder: 'Finance',
        limit: 50
      };
      const result = await connector.listDashboards(params);
      expect(axios.get).toHaveBeenCalledWith(`${mockConfig.baseUrl}/dashboards`, {
        params,
        headers: {
          'Authorization': 'Bearer test-access-token',
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      expect(result).toEqual(mockResponse.data);
    });
    it('should throw an error if the request fails', async () => {
      // Mock getAuthHeaders method
      connector.getAuthHeaders = jest.fn().mockResolvedValue({
        'Authorization': 'Bearer test-access-token'
      });

      // Mock axios get error
      const errorMessage = 'Request failed';
      axios.get.mockRejectedValue(new Error(errorMessage));
      await expect(connector.listDashboards()).rejects.toThrow(`Error listing dashboards: ${errorMessage}`);
    });
  });
  describe('getDashboard', () => {
    it('should make a GET request to the specific dashboard endpoint', async () => {
      // Mock getAuthHeaders method
      connector.getAuthHeaders = jest.fn().mockResolvedValue({
        'Authorization': 'Bearer test-access-token'
      });

      // Mock axios get response
      const mockResponse = {
        data: {
          id: 'dashboard-123',
          name: 'Test Dashboard',
          description: 'Test Description'
        }
      };
      axios.get.mockResolvedValue(mockResponse);
      const dashboardId = 'dashboard-123';
      const result = await connector.getDashboard(dashboardId);
      expect(axios.get).toHaveBeenCalledWith(`${mockConfig.baseUrl}/dashboards/${dashboardId}`, {
        headers: {
          'Authorization': 'Bearer test-access-token',
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      expect(result).toEqual(mockResponse.data);
    });
    it('should throw an error if dashboardId is not provided', async () => {
      await expect(connector.getDashboard()).rejects.toThrow('Dashboard ID is required');
    });
  });
  describe('executeReport', () => {
    it('should make a POST request to execute the report', async () => {
      // Mock getAuthHeaders method
      connector.getAuthHeaders = jest.fn().mockResolvedValue({
        'Authorization': 'Bearer test-access-token'
      });

      // Mock axios post response
      const mockResponse = {
        data: {
          executionId: 'exec-123',
          status: 'success',
          data: [{
            region: 'North America',
            revenue: 1250000
          }, {
            region: 'Europe',
            revenue: 980000
          }]
        }
      };
      axios.post.mockResolvedValue(mockResponse);
      const reportId = 'report-123';
      const options = {
        parameters: {
          startDate: '2023-01-01',
          endDate: '2023-03-31'
        },
        format: 'json'
      };
      const result = await connector.executeReport(reportId, options);
      expect(axios.post).toHaveBeenCalledWith(`${mockConfig.baseUrl}/reports/${reportId}/execute`, options, {
        headers: {
          'Authorization': 'Bearer test-access-token',
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      expect(result).toEqual(mockResponse.data);
    });
    it('should throw an error if reportId is not provided', async () => {
      await expect(connector.executeReport()).rejects.toThrow('Report ID is required');
    });
  });
  describe('listWorkflows', () => {
    it('should make a GET request to the workflows endpoint', async () => {
      // Mock getAuthHeaders method
      connector.getAuthHeaders = jest.fn().mockResolvedValue({
        'Authorization': 'Bearer test-access-token'
      });

      // Mock axios get response
      const mockResponse = {
        data: {
          data: [{
            id: 'workflow-1',
            name: 'Workflow 1'
          }, {
            id: 'workflow-2',
            name: 'Workflow 2'
          }],
          pagination: {
            page: 1,
            limit: 20,
            totalItems: 2,
            totalPages: 1
          }
        }
      };
      axios.get.mockResolvedValue(mockResponse);
      const params = {
        status: 'active',
        category: 'Finance'
      };
      const result = await connector.listWorkflows(params);
      expect(axios.get).toHaveBeenCalledWith(`${mockConfig.baseUrl}/workflows`, {
        params,
        headers: {
          'Authorization': 'Bearer test-access-token',
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      expect(result).toEqual(mockResponse.data);
    });
  });
  describe('executeWorkflow', () => {
    it('should make a POST request to execute the workflow', async () => {
      // Mock getAuthHeaders method
      connector.getAuthHeaders = jest.fn().mockResolvedValue({
        'Authorization': 'Bearer test-access-token'
      });

      // Mock axios post response
      const mockResponse = {
        data: {
          executionId: 'exec-123',
          status: 'queued',
          startTime: '2023-06-01T10:15:30Z'
        }
      };
      axios.post.mockResolvedValue(mockResponse);
      const workflowId = 'workflow-123';
      const options = {
        input: {
          invoiceId: 'INV-12345',
          amount: 1500
        },
        async: true
      };
      const result = await connector.executeWorkflow(workflowId, options);
      expect(axios.post).toHaveBeenCalledWith(`${mockConfig.baseUrl}/workflows/${workflowId}/execute`, options, {
        headers: {
          'Authorization': 'Bearer test-access-token',
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      expect(result).toEqual(mockResponse.data);
    });
    it('should throw an error if workflowId is not provided', async () => {
      await expect(connector.executeWorkflow()).rejects.toThrow('Workflow ID is required');
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfZ2V0SmVzdE9iaiIsIm1vY2siLCJjcmVhdGVMb2dnZXIiLCJqZXN0IiwiZm4iLCJpbmZvIiwiZXJyb3IiLCJkZWJ1ZyIsIndhcm4iLCJyZXF1aXJlIiwiYXhpb3MiLCJCdXNpbmVzc0ludGVsbGlnZW5jZVdvcmtmbG93Q29ubmVjdG9yIiwiZGVzY3JpYmUiLCJjb25uZWN0b3IiLCJtb2NrQ29uZmlnIiwibW9ja0NyZWRlbnRpYWxzIiwiYmVmb3JlRWFjaCIsImNsZWFyQWxsTW9ja3MiLCJiYXNlVXJsIiwiY2xpZW50SWQiLCJjbGllbnRTZWNyZXQiLCJyZWRpcmVjdFVyaSIsIml0IiwiZXhwZWN0IiwiY29uZmlnIiwidG9FcXVhbCIsImNyZWRlbnRpYWxzIiwidG9CZSIsImNvbm5lY3RvcldpdGhEZWZhdWx0cyIsImF1dGhlbnRpY2F0ZSIsIm1vY2tSZXNvbHZlZFZhbHVlIiwiaW5pdGlhbGl6ZSIsInRvSGF2ZUJlZW5DYWxsZWQiLCJjb25uZWN0b3JXaXRob3V0Q3JlZGVudGlhbHMiLCJub3QiLCJwb3N0IiwiZGF0YSIsImFjY2Vzc190b2tlbiIsImV4cGlyZXNfaW4iLCJ0b0hhdmVCZWVuQ2FsbGVkV2l0aCIsImdyYW50X3R5cGUiLCJjbGllbnRfaWQiLCJjbGllbnRfc2VjcmV0Iiwic2NvcGUiLCJoZWFkZXJzIiwiYWNjZXNzVG9rZW4iLCJ0b2tlbkV4cGlyeSIsInRvQmVEZWZpbmVkIiwiZXJyb3JNZXNzYWdlIiwibW9ja1JlamVjdGVkVmFsdWUiLCJFcnJvciIsInJlamVjdHMiLCJ0b1Rocm93IiwiRGF0ZSIsIm5vdyIsImdldEF1dGhIZWFkZXJzIiwibW9ja0ltcGxlbWVudGF0aW9uIiwibW9ja1Jlc3BvbnNlIiwiaWQiLCJuYW1lIiwicGFnaW5hdGlvbiIsInBhZ2UiLCJsaW1pdCIsInRvdGFsSXRlbXMiLCJ0b3RhbFBhZ2VzIiwiZ2V0IiwicGFyYW1zIiwiZm9sZGVyIiwicmVzdWx0IiwibGlzdERhc2hib2FyZHMiLCJkZXNjcmlwdGlvbiIsImRhc2hib2FyZElkIiwiZ2V0RGFzaGJvYXJkIiwiZXhlY3V0aW9uSWQiLCJzdGF0dXMiLCJyZWdpb24iLCJyZXZlbnVlIiwicmVwb3J0SWQiLCJvcHRpb25zIiwicGFyYW1ldGVycyIsInN0YXJ0RGF0ZSIsImVuZERhdGUiLCJmb3JtYXQiLCJleGVjdXRlUmVwb3J0IiwiY2F0ZWdvcnkiLCJsaXN0V29ya2Zsb3dzIiwic3RhcnRUaW1lIiwid29ya2Zsb3dJZCIsImlucHV0IiwiaW52b2ljZUlkIiwiYW1vdW50IiwiYXN5bmMiLCJleGVjdXRlV29ya2Zsb3ciXSwic291cmNlcyI6WyJidXNpbmVzcy1pbnRlbGxpZ2VuY2Utd29ya2Zsb3cudW5pdC50ZXN0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogVW5pdCB0ZXN0cyBmb3IgdGhlIEJ1c2luZXNzIEludGVsbGlnZW5jZSAmIFdvcmtmbG93IENvbm5lY3RvclxuICovXG5cbmNvbnN0IGF4aW9zID0gcmVxdWlyZSgnYXhpb3MnKTtcbmNvbnN0IEJ1c2luZXNzSW50ZWxsaWdlbmNlV29ya2Zsb3dDb25uZWN0b3IgPSByZXF1aXJlKCcuLi8uLi8uLi8uLi9jb25uZWN0b3IvaW1wbGVtZW50YXRpb25zL2J1c2luZXNzLWludGVsbGlnZW5jZS13b3JrZmxvdycpO1xuXG4vLyBNb2NrIGF4aW9zXG5qZXN0Lm1vY2soJ2F4aW9zJyk7XG5cbi8vIE1vY2sgbG9nZ2VyXG5qZXN0Lm1vY2soJy4uLy4uLy4uLy4uL3V0aWxzL2xvZ2dlcicsICgpID0+ICh7XG4gIGNyZWF0ZUxvZ2dlcjogamVzdC5mbigoKSA9PiAoe1xuICAgIGluZm86IGplc3QuZm4oKSxcbiAgICBlcnJvcjogamVzdC5mbigpLFxuICAgIGRlYnVnOiBqZXN0LmZuKCksXG4gICAgd2FybjogamVzdC5mbigpXG4gIH0pKVxufSkpO1xuXG5kZXNjcmliZSgnQnVzaW5lc3NJbnRlbGxpZ2VuY2VXb3JrZmxvd0Nvbm5lY3RvcicsICgpID0+IHtcbiAgbGV0IGNvbm5lY3RvcjtcbiAgbGV0IG1vY2tDb25maWc7XG4gIGxldCBtb2NrQ3JlZGVudGlhbHM7XG4gIFxuICBiZWZvcmVFYWNoKCgpID0+IHtcbiAgICAvLyBSZXNldCBtb2Nrc1xuICAgIGplc3QuY2xlYXJBbGxNb2NrcygpO1xuICAgIFxuICAgIC8vIE1vY2sgY29uZmlnIGFuZCBjcmVkZW50aWFsc1xuICAgIG1vY2tDb25maWcgPSB7XG4gICAgICBiYXNlVXJsOiAnaHR0cHM6Ly9hcGkudGVzdC5jb20nXG4gICAgfTtcbiAgICBcbiAgICBtb2NrQ3JlZGVudGlhbHMgPSB7XG4gICAgICBjbGllbnRJZDogJ3Rlc3QtY2xpZW50LWlkJyxcbiAgICAgIGNsaWVudFNlY3JldDogJ3Rlc3QtY2xpZW50LXNlY3JldCcsXG4gICAgICByZWRpcmVjdFVyaTogJ2h0dHBzOi8vdGVzdC1yZWRpcmVjdC5jb20nXG4gICAgfTtcbiAgICBcbiAgICAvLyBDcmVhdGUgY29ubmVjdG9yIGluc3RhbmNlXG4gICAgY29ubmVjdG9yID0gbmV3IEJ1c2luZXNzSW50ZWxsaWdlbmNlV29ya2Zsb3dDb25uZWN0b3IobW9ja0NvbmZpZywgbW9ja0NyZWRlbnRpYWxzKTtcbiAgfSk7XG4gIFxuICBkZXNjcmliZSgnY29uc3RydWN0b3InLCAoKSA9PiB7XG4gICAgaXQoJ3Nob3VsZCBpbml0aWFsaXplIHdpdGggcHJvdmlkZWQgY29uZmlnIGFuZCBjcmVkZW50aWFscycsICgpID0+IHtcbiAgICAgIGV4cGVjdChjb25uZWN0b3IuY29uZmlnKS50b0VxdWFsKG1vY2tDb25maWcpO1xuICAgICAgZXhwZWN0KGNvbm5lY3Rvci5jcmVkZW50aWFscykudG9FcXVhbChtb2NrQ3JlZGVudGlhbHMpO1xuICAgICAgZXhwZWN0KGNvbm5lY3Rvci5iYXNlVXJsKS50b0JlKG1vY2tDb25maWcuYmFzZVVybCk7XG4gICAgfSk7XG4gICAgXG4gICAgaXQoJ3Nob3VsZCB1c2UgZGVmYXVsdCBiYXNlVXJsIGlmIG5vdCBwcm92aWRlZCcsICgpID0+IHtcbiAgICAgIGNvbnN0IGNvbm5lY3RvcldpdGhEZWZhdWx0cyA9IG5ldyBCdXNpbmVzc0ludGVsbGlnZW5jZVdvcmtmbG93Q29ubmVjdG9yKCk7XG4gICAgICBleHBlY3QoY29ubmVjdG9yV2l0aERlZmF1bHRzLmJhc2VVcmwpLnRvQmUoJ2h0dHBzOi8vYXBpLmV4YW1wbGUuY29tJyk7XG4gICAgfSk7XG4gIH0pO1xuICBcbiAgZGVzY3JpYmUoJ2luaXRpYWxpemUnLCAoKSA9PiB7XG4gICAgaXQoJ3Nob3VsZCBhdXRoZW50aWNhdGUgaWYgY3JlZGVudGlhbHMgYXJlIHByb3ZpZGVkJywgYXN5bmMgKCkgPT4ge1xuICAgICAgLy8gTW9jayBhdXRoZW50aWNhdGUgbWV0aG9kXG4gICAgICBjb25uZWN0b3IuYXV0aGVudGljYXRlID0gamVzdC5mbigpLm1vY2tSZXNvbHZlZFZhbHVlKCk7XG4gICAgICBcbiAgICAgIGF3YWl0IGNvbm5lY3Rvci5pbml0aWFsaXplKCk7XG4gICAgICBcbiAgICAgIGV4cGVjdChjb25uZWN0b3IuYXV0aGVudGljYXRlKS50b0hhdmVCZWVuQ2FsbGVkKCk7XG4gICAgfSk7XG4gICAgXG4gICAgaXQoJ3Nob3VsZCBub3QgYXV0aGVudGljYXRlIGlmIGNyZWRlbnRpYWxzIGFyZSBub3QgcHJvdmlkZWQnLCBhc3luYyAoKSA9PiB7XG4gICAgICAvLyBDcmVhdGUgY29ubmVjdG9yIHdpdGhvdXQgY3JlZGVudGlhbHNcbiAgICAgIGNvbnN0IGNvbm5lY3RvcldpdGhvdXRDcmVkZW50aWFscyA9IG5ldyBCdXNpbmVzc0ludGVsbGlnZW5jZVdvcmtmbG93Q29ubmVjdG9yKG1vY2tDb25maWcsIHt9KTtcbiAgICAgIFxuICAgICAgLy8gTW9jayBhdXRoZW50aWNhdGUgbWV0aG9kXG4gICAgICBjb25uZWN0b3JXaXRob3V0Q3JlZGVudGlhbHMuYXV0aGVudGljYXRlID0gamVzdC5mbigpLm1vY2tSZXNvbHZlZFZhbHVlKCk7XG4gICAgICBcbiAgICAgIGF3YWl0IGNvbm5lY3RvcldpdGhvdXRDcmVkZW50aWFscy5pbml0aWFsaXplKCk7XG4gICAgICBcbiAgICAgIGV4cGVjdChjb25uZWN0b3JXaXRob3V0Q3JlZGVudGlhbHMuYXV0aGVudGljYXRlKS5ub3QudG9IYXZlQmVlbkNhbGxlZCgpO1xuICAgIH0pO1xuICB9KTtcbiAgXG4gIGRlc2NyaWJlKCdhdXRoZW50aWNhdGUnLCAoKSA9PiB7XG4gICAgaXQoJ3Nob3VsZCBtYWtlIGEgUE9TVCByZXF1ZXN0IHRvIHRoZSB0b2tlbiBlbmRwb2ludCcsIGFzeW5jICgpID0+IHtcbiAgICAgIC8vIE1vY2sgYXhpb3MgcG9zdCByZXNwb25zZVxuICAgICAgYXhpb3MucG9zdC5tb2NrUmVzb2x2ZWRWYWx1ZSh7XG4gICAgICAgIGRhdGE6IHtcbiAgICAgICAgICBhY2Nlc3NfdG9rZW46ICd0ZXN0LWFjY2Vzcy10b2tlbicsXG4gICAgICAgICAgZXhwaXJlc19pbjogMzYwMFxuICAgICAgICB9XG4gICAgICB9KTtcbiAgICAgIFxuICAgICAgYXdhaXQgY29ubmVjdG9yLmF1dGhlbnRpY2F0ZSgpO1xuICAgICAgXG4gICAgICBleHBlY3QoYXhpb3MucG9zdCkudG9IYXZlQmVlbkNhbGxlZFdpdGgoXG4gICAgICAgIGAke21vY2tDb25maWcuYmFzZVVybH0vb2F1dGgyL3Rva2VuYCxcbiAgICAgICAge1xuICAgICAgICAgIGdyYW50X3R5cGU6ICdjbGllbnRfY3JlZGVudGlhbHMnLFxuICAgICAgICAgIGNsaWVudF9pZDogbW9ja0NyZWRlbnRpYWxzLmNsaWVudElkLFxuICAgICAgICAgIGNsaWVudF9zZWNyZXQ6IG1vY2tDcmVkZW50aWFscy5jbGllbnRTZWNyZXQsXG4gICAgICAgICAgc2NvcGU6ICdyZWFkOmRhc2hib2FyZHMgd3JpdGU6ZGFzaGJvYXJkcyByZWFkOnJlcG9ydHMgd3JpdGU6cmVwb3J0cyByZWFkOndvcmtmbG93cyB3cml0ZTp3b3JrZmxvd3MnXG4gICAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL3gtd3d3LWZvcm0tdXJsZW5jb2RlZCcsXG4gICAgICAgICAgICAnQWNjZXB0JzogJ2FwcGxpY2F0aW9uL2pzb24nXG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICApO1xuICAgICAgXG4gICAgICBleHBlY3QoY29ubmVjdG9yLmFjY2Vzc1Rva2VuKS50b0JlKCd0ZXN0LWFjY2Vzcy10b2tlbicpO1xuICAgICAgZXhwZWN0KGNvbm5lY3Rvci50b2tlbkV4cGlyeSkudG9CZURlZmluZWQoKTtcbiAgICB9KTtcbiAgICBcbiAgICBpdCgnc2hvdWxkIHRocm93IGFuIGVycm9yIGlmIGF1dGhlbnRpY2F0aW9uIGZhaWxzJywgYXN5bmMgKCkgPT4ge1xuICAgICAgLy8gTW9jayBheGlvcyBwb3N0IGVycm9yXG4gICAgICBjb25zdCBlcnJvck1lc3NhZ2UgPSAnQXV0aGVudGljYXRpb24gZmFpbGVkJztcbiAgICAgIGF4aW9zLnBvc3QubW9ja1JlamVjdGVkVmFsdWUobmV3IEVycm9yKGVycm9yTWVzc2FnZSkpO1xuICAgICAgXG4gICAgICBhd2FpdCBleHBlY3QoY29ubmVjdG9yLmF1dGhlbnRpY2F0ZSgpKS5yZWplY3RzLnRvVGhyb3coYEF1dGhlbnRpY2F0aW9uIGZhaWxlZDogJHtlcnJvck1lc3NhZ2V9YCk7XG4gICAgfSk7XG4gIH0pO1xuICBcbiAgZGVzY3JpYmUoJ2dldEF1dGhIZWFkZXJzJywgKCkgPT4ge1xuICAgIGl0KCdzaG91bGQgcmV0dXJuIGF1dGhvcml6YXRpb24gaGVhZGVycyB3aXRoIGFjY2VzcyB0b2tlbicsIGFzeW5jICgpID0+IHtcbiAgICAgIC8vIFNldCBhY2Nlc3MgdG9rZW4gYW5kIGV4cGlyeVxuICAgICAgY29ubmVjdG9yLmFjY2Vzc1Rva2VuID0gJ3Rlc3QtYWNjZXNzLXRva2VuJztcbiAgICAgIGNvbm5lY3Rvci50b2tlbkV4cGlyeSA9IERhdGUubm93KCkgKyAzNjAwMDAwOyAvLyAxIGhvdXIgZnJvbSBub3dcbiAgICAgIFxuICAgICAgY29uc3QgaGVhZGVycyA9IGF3YWl0IGNvbm5lY3Rvci5nZXRBdXRoSGVhZGVycygpO1xuICAgICAgXG4gICAgICBleHBlY3QoaGVhZGVycykudG9FcXVhbCh7XG4gICAgICAgICdBdXRob3JpemF0aW9uJzogJ0JlYXJlciB0ZXN0LWFjY2Vzcy10b2tlbidcbiAgICAgIH0pO1xuICAgIH0pO1xuICAgIFxuICAgIGl0KCdzaG91bGQgYXV0aGVudGljYXRlIGlmIGFjY2VzcyB0b2tlbiBpcyBub3Qgc2V0JywgYXN5bmMgKCkgPT4ge1xuICAgICAgLy8gTW9jayBhdXRoZW50aWNhdGUgbWV0aG9kXG4gICAgICBjb25uZWN0b3IuYXV0aGVudGljYXRlID0gamVzdC5mbigpLm1vY2tJbXBsZW1lbnRhdGlvbigoKSA9PiB7XG4gICAgICAgIGNvbm5lY3Rvci5hY2Nlc3NUb2tlbiA9ICduZXctYWNjZXNzLXRva2VuJztcbiAgICAgICAgY29ubmVjdG9yLnRva2VuRXhwaXJ5ID0gRGF0ZS5ub3coKSArIDM2MDAwMDA7XG4gICAgICB9KTtcbiAgICAgIFxuICAgICAgY29uc3QgaGVhZGVycyA9IGF3YWl0IGNvbm5lY3Rvci5nZXRBdXRoSGVhZGVycygpO1xuICAgICAgXG4gICAgICBleHBlY3QoY29ubmVjdG9yLmF1dGhlbnRpY2F0ZSkudG9IYXZlQmVlbkNhbGxlZCgpO1xuICAgICAgZXhwZWN0KGhlYWRlcnMpLnRvRXF1YWwoe1xuICAgICAgICAnQXV0aG9yaXphdGlvbic6ICdCZWFyZXIgbmV3LWFjY2Vzcy10b2tlbidcbiAgICAgIH0pO1xuICAgIH0pO1xuICB9KTtcbiAgXG4gIGRlc2NyaWJlKCdsaXN0RGFzaGJvYXJkcycsICgpID0+IHtcbiAgICBpdCgnc2hvdWxkIG1ha2UgYSBHRVQgcmVxdWVzdCB0byB0aGUgZGFzaGJvYXJkcyBlbmRwb2ludCcsIGFzeW5jICgpID0+IHtcbiAgICAgIC8vIE1vY2sgZ2V0QXV0aEhlYWRlcnMgbWV0aG9kXG4gICAgICBjb25uZWN0b3IuZ2V0QXV0aEhlYWRlcnMgPSBqZXN0LmZuKCkubW9ja1Jlc29sdmVkVmFsdWUoe1xuICAgICAgICAnQXV0aG9yaXphdGlvbic6ICdCZWFyZXIgdGVzdC1hY2Nlc3MtdG9rZW4nXG4gICAgICB9KTtcbiAgICAgIFxuICAgICAgLy8gTW9jayBheGlvcyBnZXQgcmVzcG9uc2VcbiAgICAgIGNvbnN0IG1vY2tSZXNwb25zZSA9IHtcbiAgICAgICAgZGF0YToge1xuICAgICAgICAgIGRhdGE6IFtcbiAgICAgICAgICAgIHsgaWQ6ICdkYXNoYm9hcmQtMScsIG5hbWU6ICdEYXNoYm9hcmQgMScgfSxcbiAgICAgICAgICAgIHsgaWQ6ICdkYXNoYm9hcmQtMicsIG5hbWU6ICdEYXNoYm9hcmQgMicgfVxuICAgICAgICAgIF0sXG4gICAgICAgICAgcGFnaW5hdGlvbjoge1xuICAgICAgICAgICAgcGFnZTogMSxcbiAgICAgICAgICAgIGxpbWl0OiAyMCxcbiAgICAgICAgICAgIHRvdGFsSXRlbXM6IDIsXG4gICAgICAgICAgICB0b3RhbFBhZ2VzOiAxXG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9O1xuICAgICAgYXhpb3MuZ2V0Lm1vY2tSZXNvbHZlZFZhbHVlKG1vY2tSZXNwb25zZSk7XG4gICAgICBcbiAgICAgIGNvbnN0IHBhcmFtcyA9IHsgZm9sZGVyOiAnRmluYW5jZScsIGxpbWl0OiA1MCB9O1xuICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgY29ubmVjdG9yLmxpc3REYXNoYm9hcmRzKHBhcmFtcyk7XG4gICAgICBcbiAgICAgIGV4cGVjdChheGlvcy5nZXQpLnRvSGF2ZUJlZW5DYWxsZWRXaXRoKFxuICAgICAgICBgJHttb2NrQ29uZmlnLmJhc2VVcmx9L2Rhc2hib2FyZHNgLFxuICAgICAgICB7XG4gICAgICAgICAgcGFyYW1zLFxuICAgICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAgICdBdXRob3JpemF0aW9uJzogJ0JlYXJlciB0ZXN0LWFjY2Vzcy10b2tlbicsXG4gICAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICAgICAgJ0FjY2VwdCc6ICdhcHBsaWNhdGlvbi9qc29uJ1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgKTtcbiAgICAgIFxuICAgICAgZXhwZWN0KHJlc3VsdCkudG9FcXVhbChtb2NrUmVzcG9uc2UuZGF0YSk7XG4gICAgfSk7XG4gICAgXG4gICAgaXQoJ3Nob3VsZCB0aHJvdyBhbiBlcnJvciBpZiB0aGUgcmVxdWVzdCBmYWlscycsIGFzeW5jICgpID0+IHtcbiAgICAgIC8vIE1vY2sgZ2V0QXV0aEhlYWRlcnMgbWV0aG9kXG4gICAgICBjb25uZWN0b3IuZ2V0QXV0aEhlYWRlcnMgPSBqZXN0LmZuKCkubW9ja1Jlc29sdmVkVmFsdWUoe1xuICAgICAgICAnQXV0aG9yaXphdGlvbic6ICdCZWFyZXIgdGVzdC1hY2Nlc3MtdG9rZW4nXG4gICAgICB9KTtcbiAgICAgIFxuICAgICAgLy8gTW9jayBheGlvcyBnZXQgZXJyb3JcbiAgICAgIGNvbnN0IGVycm9yTWVzc2FnZSA9ICdSZXF1ZXN0IGZhaWxlZCc7XG4gICAgICBheGlvcy5nZXQubW9ja1JlamVjdGVkVmFsdWUobmV3IEVycm9yKGVycm9yTWVzc2FnZSkpO1xuICAgICAgXG4gICAgICBhd2FpdCBleHBlY3QoY29ubmVjdG9yLmxpc3REYXNoYm9hcmRzKCkpLnJlamVjdHMudG9UaHJvdyhgRXJyb3IgbGlzdGluZyBkYXNoYm9hcmRzOiAke2Vycm9yTWVzc2FnZX1gKTtcbiAgICB9KTtcbiAgfSk7XG4gIFxuICBkZXNjcmliZSgnZ2V0RGFzaGJvYXJkJywgKCkgPT4ge1xuICAgIGl0KCdzaG91bGQgbWFrZSBhIEdFVCByZXF1ZXN0IHRvIHRoZSBzcGVjaWZpYyBkYXNoYm9hcmQgZW5kcG9pbnQnLCBhc3luYyAoKSA9PiB7XG4gICAgICAvLyBNb2NrIGdldEF1dGhIZWFkZXJzIG1ldGhvZFxuICAgICAgY29ubmVjdG9yLmdldEF1dGhIZWFkZXJzID0gamVzdC5mbigpLm1vY2tSZXNvbHZlZFZhbHVlKHtcbiAgICAgICAgJ0F1dGhvcml6YXRpb24nOiAnQmVhcmVyIHRlc3QtYWNjZXNzLXRva2VuJ1xuICAgICAgfSk7XG4gICAgICBcbiAgICAgIC8vIE1vY2sgYXhpb3MgZ2V0IHJlc3BvbnNlXG4gICAgICBjb25zdCBtb2NrUmVzcG9uc2UgPSB7XG4gICAgICAgIGRhdGE6IHtcbiAgICAgICAgICBpZDogJ2Rhc2hib2FyZC0xMjMnLFxuICAgICAgICAgIG5hbWU6ICdUZXN0IERhc2hib2FyZCcsXG4gICAgICAgICAgZGVzY3JpcHRpb246ICdUZXN0IERlc2NyaXB0aW9uJ1xuICAgICAgICB9XG4gICAgICB9O1xuICAgICAgYXhpb3MuZ2V0Lm1vY2tSZXNvbHZlZFZhbHVlKG1vY2tSZXNwb25zZSk7XG4gICAgICBcbiAgICAgIGNvbnN0IGRhc2hib2FyZElkID0gJ2Rhc2hib2FyZC0xMjMnO1xuICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgY29ubmVjdG9yLmdldERhc2hib2FyZChkYXNoYm9hcmRJZCk7XG4gICAgICBcbiAgICAgIGV4cGVjdChheGlvcy5nZXQpLnRvSGF2ZUJlZW5DYWxsZWRXaXRoKFxuICAgICAgICBgJHttb2NrQ29uZmlnLmJhc2VVcmx9L2Rhc2hib2FyZHMvJHtkYXNoYm9hcmRJZH1gLFxuICAgICAgICB7XG4gICAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICAgJ0F1dGhvcml6YXRpb24nOiAnQmVhcmVyIHRlc3QtYWNjZXNzLXRva2VuJyxcbiAgICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgICAgICAnQWNjZXB0JzogJ2FwcGxpY2F0aW9uL2pzb24nXG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICApO1xuICAgICAgXG4gICAgICBleHBlY3QocmVzdWx0KS50b0VxdWFsKG1vY2tSZXNwb25zZS5kYXRhKTtcbiAgICB9KTtcbiAgICBcbiAgICBpdCgnc2hvdWxkIHRocm93IGFuIGVycm9yIGlmIGRhc2hib2FyZElkIGlzIG5vdCBwcm92aWRlZCcsIGFzeW5jICgpID0+IHtcbiAgICAgIGF3YWl0IGV4cGVjdChjb25uZWN0b3IuZ2V0RGFzaGJvYXJkKCkpLnJlamVjdHMudG9UaHJvdygnRGFzaGJvYXJkIElEIGlzIHJlcXVpcmVkJyk7XG4gICAgfSk7XG4gIH0pO1xuICBcbiAgZGVzY3JpYmUoJ2V4ZWN1dGVSZXBvcnQnLCAoKSA9PiB7XG4gICAgaXQoJ3Nob3VsZCBtYWtlIGEgUE9TVCByZXF1ZXN0IHRvIGV4ZWN1dGUgdGhlIHJlcG9ydCcsIGFzeW5jICgpID0+IHtcbiAgICAgIC8vIE1vY2sgZ2V0QXV0aEhlYWRlcnMgbWV0aG9kXG4gICAgICBjb25uZWN0b3IuZ2V0QXV0aEhlYWRlcnMgPSBqZXN0LmZuKCkubW9ja1Jlc29sdmVkVmFsdWUoe1xuICAgICAgICAnQXV0aG9yaXphdGlvbic6ICdCZWFyZXIgdGVzdC1hY2Nlc3MtdG9rZW4nXG4gICAgICB9KTtcbiAgICAgIFxuICAgICAgLy8gTW9jayBheGlvcyBwb3N0IHJlc3BvbnNlXG4gICAgICBjb25zdCBtb2NrUmVzcG9uc2UgPSB7XG4gICAgICAgIGRhdGE6IHtcbiAgICAgICAgICBleGVjdXRpb25JZDogJ2V4ZWMtMTIzJyxcbiAgICAgICAgICBzdGF0dXM6ICdzdWNjZXNzJyxcbiAgICAgICAgICBkYXRhOiBbXG4gICAgICAgICAgICB7IHJlZ2lvbjogJ05vcnRoIEFtZXJpY2EnLCByZXZlbnVlOiAxMjUwMDAwIH0sXG4gICAgICAgICAgICB7IHJlZ2lvbjogJ0V1cm9wZScsIHJldmVudWU6IDk4MDAwMCB9XG4gICAgICAgICAgXVxuICAgICAgICB9XG4gICAgICB9O1xuICAgICAgYXhpb3MucG9zdC5tb2NrUmVzb2x2ZWRWYWx1ZShtb2NrUmVzcG9uc2UpO1xuICAgICAgXG4gICAgICBjb25zdCByZXBvcnRJZCA9ICdyZXBvcnQtMTIzJztcbiAgICAgIGNvbnN0IG9wdGlvbnMgPSB7XG4gICAgICAgIHBhcmFtZXRlcnM6IHtcbiAgICAgICAgICBzdGFydERhdGU6ICcyMDIzLTAxLTAxJyxcbiAgICAgICAgICBlbmREYXRlOiAnMjAyMy0wMy0zMSdcbiAgICAgICAgfSxcbiAgICAgICAgZm9ybWF0OiAnanNvbidcbiAgICAgIH07XG4gICAgICBcbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IGNvbm5lY3Rvci5leGVjdXRlUmVwb3J0KHJlcG9ydElkLCBvcHRpb25zKTtcbiAgICAgIFxuICAgICAgZXhwZWN0KGF4aW9zLnBvc3QpLnRvSGF2ZUJlZW5DYWxsZWRXaXRoKFxuICAgICAgICBgJHttb2NrQ29uZmlnLmJhc2VVcmx9L3JlcG9ydHMvJHtyZXBvcnRJZH0vZXhlY3V0ZWAsXG4gICAgICAgIG9wdGlvbnMsXG4gICAgICAgIHtcbiAgICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgICAnQXV0aG9yaXphdGlvbic6ICdCZWFyZXIgdGVzdC1hY2Nlc3MtdG9rZW4nLFxuICAgICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgICAgICdBY2NlcHQnOiAnYXBwbGljYXRpb24vanNvbidcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICk7XG4gICAgICBcbiAgICAgIGV4cGVjdChyZXN1bHQpLnRvRXF1YWwobW9ja1Jlc3BvbnNlLmRhdGEpO1xuICAgIH0pO1xuICAgIFxuICAgIGl0KCdzaG91bGQgdGhyb3cgYW4gZXJyb3IgaWYgcmVwb3J0SWQgaXMgbm90IHByb3ZpZGVkJywgYXN5bmMgKCkgPT4ge1xuICAgICAgYXdhaXQgZXhwZWN0KGNvbm5lY3Rvci5leGVjdXRlUmVwb3J0KCkpLnJlamVjdHMudG9UaHJvdygnUmVwb3J0IElEIGlzIHJlcXVpcmVkJyk7XG4gICAgfSk7XG4gIH0pO1xuICBcbiAgZGVzY3JpYmUoJ2xpc3RXb3JrZmxvd3MnLCAoKSA9PiB7XG4gICAgaXQoJ3Nob3VsZCBtYWtlIGEgR0VUIHJlcXVlc3QgdG8gdGhlIHdvcmtmbG93cyBlbmRwb2ludCcsIGFzeW5jICgpID0+IHtcbiAgICAgIC8vIE1vY2sgZ2V0QXV0aEhlYWRlcnMgbWV0aG9kXG4gICAgICBjb25uZWN0b3IuZ2V0QXV0aEhlYWRlcnMgPSBqZXN0LmZuKCkubW9ja1Jlc29sdmVkVmFsdWUoe1xuICAgICAgICAnQXV0aG9yaXphdGlvbic6ICdCZWFyZXIgdGVzdC1hY2Nlc3MtdG9rZW4nXG4gICAgICB9KTtcbiAgICAgIFxuICAgICAgLy8gTW9jayBheGlvcyBnZXQgcmVzcG9uc2VcbiAgICAgIGNvbnN0IG1vY2tSZXNwb25zZSA9IHtcbiAgICAgICAgZGF0YToge1xuICAgICAgICAgIGRhdGE6IFtcbiAgICAgICAgICAgIHsgaWQ6ICd3b3JrZmxvdy0xJywgbmFtZTogJ1dvcmtmbG93IDEnIH0sXG4gICAgICAgICAgICB7IGlkOiAnd29ya2Zsb3ctMicsIG5hbWU6ICdXb3JrZmxvdyAyJyB9XG4gICAgICAgICAgXSxcbiAgICAgICAgICBwYWdpbmF0aW9uOiB7XG4gICAgICAgICAgICBwYWdlOiAxLFxuICAgICAgICAgICAgbGltaXQ6IDIwLFxuICAgICAgICAgICAgdG90YWxJdGVtczogMixcbiAgICAgICAgICAgIHRvdGFsUGFnZXM6IDFcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH07XG4gICAgICBheGlvcy5nZXQubW9ja1Jlc29sdmVkVmFsdWUobW9ja1Jlc3BvbnNlKTtcbiAgICAgIFxuICAgICAgY29uc3QgcGFyYW1zID0geyBzdGF0dXM6ICdhY3RpdmUnLCBjYXRlZ29yeTogJ0ZpbmFuY2UnIH07XG4gICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBjb25uZWN0b3IubGlzdFdvcmtmbG93cyhwYXJhbXMpO1xuICAgICAgXG4gICAgICBleHBlY3QoYXhpb3MuZ2V0KS50b0hhdmVCZWVuQ2FsbGVkV2l0aChcbiAgICAgICAgYCR7bW9ja0NvbmZpZy5iYXNlVXJsfS93b3JrZmxvd3NgLFxuICAgICAgICB7XG4gICAgICAgICAgcGFyYW1zLFxuICAgICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAgICdBdXRob3JpemF0aW9uJzogJ0JlYXJlciB0ZXN0LWFjY2Vzcy10b2tlbicsXG4gICAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICAgICAgJ0FjY2VwdCc6ICdhcHBsaWNhdGlvbi9qc29uJ1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgKTtcbiAgICAgIFxuICAgICAgZXhwZWN0KHJlc3VsdCkudG9FcXVhbChtb2NrUmVzcG9uc2UuZGF0YSk7XG4gICAgfSk7XG4gIH0pO1xuICBcbiAgZGVzY3JpYmUoJ2V4ZWN1dGVXb3JrZmxvdycsICgpID0+IHtcbiAgICBpdCgnc2hvdWxkIG1ha2UgYSBQT1NUIHJlcXVlc3QgdG8gZXhlY3V0ZSB0aGUgd29ya2Zsb3cnLCBhc3luYyAoKSA9PiB7XG4gICAgICAvLyBNb2NrIGdldEF1dGhIZWFkZXJzIG1ldGhvZFxuICAgICAgY29ubmVjdG9yLmdldEF1dGhIZWFkZXJzID0gamVzdC5mbigpLm1vY2tSZXNvbHZlZFZhbHVlKHtcbiAgICAgICAgJ0F1dGhvcml6YXRpb24nOiAnQmVhcmVyIHRlc3QtYWNjZXNzLXRva2VuJ1xuICAgICAgfSk7XG4gICAgICBcbiAgICAgIC8vIE1vY2sgYXhpb3MgcG9zdCByZXNwb25zZVxuICAgICAgY29uc3QgbW9ja1Jlc3BvbnNlID0ge1xuICAgICAgICBkYXRhOiB7XG4gICAgICAgICAgZXhlY3V0aW9uSWQ6ICdleGVjLTEyMycsXG4gICAgICAgICAgc3RhdHVzOiAncXVldWVkJyxcbiAgICAgICAgICBzdGFydFRpbWU6ICcyMDIzLTA2LTAxVDEwOjE1OjMwWidcbiAgICAgICAgfVxuICAgICAgfTtcbiAgICAgIGF4aW9zLnBvc3QubW9ja1Jlc29sdmVkVmFsdWUobW9ja1Jlc3BvbnNlKTtcbiAgICAgIFxuICAgICAgY29uc3Qgd29ya2Zsb3dJZCA9ICd3b3JrZmxvdy0xMjMnO1xuICAgICAgY29uc3Qgb3B0aW9ucyA9IHtcbiAgICAgICAgaW5wdXQ6IHtcbiAgICAgICAgICBpbnZvaWNlSWQ6ICdJTlYtMTIzNDUnLFxuICAgICAgICAgIGFtb3VudDogMTUwMFxuICAgICAgICB9LFxuICAgICAgICBhc3luYzogdHJ1ZVxuICAgICAgfTtcbiAgICAgIFxuICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgY29ubmVjdG9yLmV4ZWN1dGVXb3JrZmxvdyh3b3JrZmxvd0lkLCBvcHRpb25zKTtcbiAgICAgIFxuICAgICAgZXhwZWN0KGF4aW9zLnBvc3QpLnRvSGF2ZUJlZW5DYWxsZWRXaXRoKFxuICAgICAgICBgJHttb2NrQ29uZmlnLmJhc2VVcmx9L3dvcmtmbG93cy8ke3dvcmtmbG93SWR9L2V4ZWN1dGVgLFxuICAgICAgICBvcHRpb25zLFxuICAgICAgICB7XG4gICAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICAgJ0F1dGhvcml6YXRpb24nOiAnQmVhcmVyIHRlc3QtYWNjZXNzLXRva2VuJyxcbiAgICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgICAgICAnQWNjZXB0JzogJ2FwcGxpY2F0aW9uL2pzb24nXG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICApO1xuICAgICAgXG4gICAgICBleHBlY3QocmVzdWx0KS50b0VxdWFsKG1vY2tSZXNwb25zZS5kYXRhKTtcbiAgICB9KTtcbiAgICBcbiAgICBpdCgnc2hvdWxkIHRocm93IGFuIGVycm9yIGlmIHdvcmtmbG93SWQgaXMgbm90IHByb3ZpZGVkJywgYXN5bmMgKCkgPT4ge1xuICAgICAgYXdhaXQgZXhwZWN0KGNvbm5lY3Rvci5leGVjdXRlV29ya2Zsb3coKSkucmVqZWN0cy50b1Rocm93KCdXb3JrZmxvdyBJRCBpcyByZXF1aXJlZCcpO1xuICAgIH0pO1xuICB9KTtcbn0pO1xuIl0sIm1hcHBpbmdzIjoiQUFPQTtBQUNBQSxXQUFBLEdBQUtDLElBQUksQ0FBQyxPQUFPLENBQUM7O0FBRWxCO0FBQ0FELFdBQUEsR0FBS0MsSUFBSSxDQUFDLDBCQUEwQixFQUFFLE9BQU87RUFDM0NDLFlBQVksRUFBRUMsSUFBSSxDQUFDQyxFQUFFLENBQUMsT0FBTztJQUMzQkMsSUFBSSxFQUFFRixJQUFJLENBQUNDLEVBQUUsQ0FBQyxDQUFDO0lBQ2ZFLEtBQUssRUFBRUgsSUFBSSxDQUFDQyxFQUFFLENBQUMsQ0FBQztJQUNoQkcsS0FBSyxFQUFFSixJQUFJLENBQUNDLEVBQUUsQ0FBQyxDQUFDO0lBQ2hCSSxJQUFJLEVBQUVMLElBQUksQ0FBQ0MsRUFBRSxDQUFDO0VBQ2hCLENBQUMsQ0FBQztBQUNKLENBQUMsQ0FBQyxDQUFDO0FBQUMsU0FBQUosWUFBQTtFQUFBO0lBQUFHO0VBQUEsSUFBQU0sT0FBQTtFQUFBVCxXQUFBLEdBQUFBLENBQUEsS0FBQUcsSUFBQTtFQUFBLE9BQUFBLElBQUE7QUFBQTtBQWxCSjtBQUNBO0FBQ0E7O0FBRUEsTUFBTU8sS0FBSyxHQUFHRCxPQUFPLENBQUMsT0FBTyxDQUFDO0FBQzlCLE1BQU1FLHFDQUFxQyxHQUFHRixPQUFPLENBQUMsc0VBQXNFLENBQUM7QUFlN0hHLFFBQVEsQ0FBQyx1Q0FBdUMsRUFBRSxNQUFNO0VBQ3RELElBQUlDLFNBQVM7RUFDYixJQUFJQyxVQUFVO0VBQ2QsSUFBSUMsZUFBZTtFQUVuQkMsVUFBVSxDQUFDLE1BQU07SUFDZjtJQUNBYixJQUFJLENBQUNjLGFBQWEsQ0FBQyxDQUFDOztJQUVwQjtJQUNBSCxVQUFVLEdBQUc7TUFDWEksT0FBTyxFQUFFO0lBQ1gsQ0FBQztJQUVESCxlQUFlLEdBQUc7TUFDaEJJLFFBQVEsRUFBRSxnQkFBZ0I7TUFDMUJDLFlBQVksRUFBRSxvQkFBb0I7TUFDbENDLFdBQVcsRUFBRTtJQUNmLENBQUM7O0lBRUQ7SUFDQVIsU0FBUyxHQUFHLElBQUlGLHFDQUFxQyxDQUFDRyxVQUFVLEVBQUVDLGVBQWUsQ0FBQztFQUNwRixDQUFDLENBQUM7RUFFRkgsUUFBUSxDQUFDLGFBQWEsRUFBRSxNQUFNO0lBQzVCVSxFQUFFLENBQUMsd0RBQXdELEVBQUUsTUFBTTtNQUNqRUMsTUFBTSxDQUFDVixTQUFTLENBQUNXLE1BQU0sQ0FBQyxDQUFDQyxPQUFPLENBQUNYLFVBQVUsQ0FBQztNQUM1Q1MsTUFBTSxDQUFDVixTQUFTLENBQUNhLFdBQVcsQ0FBQyxDQUFDRCxPQUFPLENBQUNWLGVBQWUsQ0FBQztNQUN0RFEsTUFBTSxDQUFDVixTQUFTLENBQUNLLE9BQU8sQ0FBQyxDQUFDUyxJQUFJLENBQUNiLFVBQVUsQ0FBQ0ksT0FBTyxDQUFDO0lBQ3BELENBQUMsQ0FBQztJQUVGSSxFQUFFLENBQUMsNENBQTRDLEVBQUUsTUFBTTtNQUNyRCxNQUFNTSxxQkFBcUIsR0FBRyxJQUFJakIscUNBQXFDLENBQUMsQ0FBQztNQUN6RVksTUFBTSxDQUFDSyxxQkFBcUIsQ0FBQ1YsT0FBTyxDQUFDLENBQUNTLElBQUksQ0FBQyx5QkFBeUIsQ0FBQztJQUN2RSxDQUFDLENBQUM7RUFDSixDQUFDLENBQUM7RUFFRmYsUUFBUSxDQUFDLFlBQVksRUFBRSxNQUFNO0lBQzNCVSxFQUFFLENBQUMsaURBQWlELEVBQUUsWUFBWTtNQUNoRTtNQUNBVCxTQUFTLENBQUNnQixZQUFZLEdBQUcxQixJQUFJLENBQUNDLEVBQUUsQ0FBQyxDQUFDLENBQUMwQixpQkFBaUIsQ0FBQyxDQUFDO01BRXRELE1BQU1qQixTQUFTLENBQUNrQixVQUFVLENBQUMsQ0FBQztNQUU1QlIsTUFBTSxDQUFDVixTQUFTLENBQUNnQixZQUFZLENBQUMsQ0FBQ0csZ0JBQWdCLENBQUMsQ0FBQztJQUNuRCxDQUFDLENBQUM7SUFFRlYsRUFBRSxDQUFDLHlEQUF5RCxFQUFFLFlBQVk7TUFDeEU7TUFDQSxNQUFNVywyQkFBMkIsR0FBRyxJQUFJdEIscUNBQXFDLENBQUNHLFVBQVUsRUFBRSxDQUFDLENBQUMsQ0FBQzs7TUFFN0Y7TUFDQW1CLDJCQUEyQixDQUFDSixZQUFZLEdBQUcxQixJQUFJLENBQUNDLEVBQUUsQ0FBQyxDQUFDLENBQUMwQixpQkFBaUIsQ0FBQyxDQUFDO01BRXhFLE1BQU1HLDJCQUEyQixDQUFDRixVQUFVLENBQUMsQ0FBQztNQUU5Q1IsTUFBTSxDQUFDVSwyQkFBMkIsQ0FBQ0osWUFBWSxDQUFDLENBQUNLLEdBQUcsQ0FBQ0YsZ0JBQWdCLENBQUMsQ0FBQztJQUN6RSxDQUFDLENBQUM7RUFDSixDQUFDLENBQUM7RUFFRnBCLFFBQVEsQ0FBQyxjQUFjLEVBQUUsTUFBTTtJQUM3QlUsRUFBRSxDQUFDLGtEQUFrRCxFQUFFLFlBQVk7TUFDakU7TUFDQVosS0FBSyxDQUFDeUIsSUFBSSxDQUFDTCxpQkFBaUIsQ0FBQztRQUMzQk0sSUFBSSxFQUFFO1VBQ0pDLFlBQVksRUFBRSxtQkFBbUI7VUFDakNDLFVBQVUsRUFBRTtRQUNkO01BQ0YsQ0FBQyxDQUFDO01BRUYsTUFBTXpCLFNBQVMsQ0FBQ2dCLFlBQVksQ0FBQyxDQUFDO01BRTlCTixNQUFNLENBQUNiLEtBQUssQ0FBQ3lCLElBQUksQ0FBQyxDQUFDSSxvQkFBb0IsQ0FDckMsR0FBR3pCLFVBQVUsQ0FBQ0ksT0FBTyxlQUFlLEVBQ3BDO1FBQ0VzQixVQUFVLEVBQUUsb0JBQW9CO1FBQ2hDQyxTQUFTLEVBQUUxQixlQUFlLENBQUNJLFFBQVE7UUFDbkN1QixhQUFhLEVBQUUzQixlQUFlLENBQUNLLFlBQVk7UUFDM0N1QixLQUFLLEVBQUU7TUFDVCxDQUFDLEVBQ0Q7UUFDRUMsT0FBTyxFQUFFO1VBQ1AsY0FBYyxFQUFFLG1DQUFtQztVQUNuRCxRQUFRLEVBQUU7UUFDWjtNQUNGLENBQ0YsQ0FBQztNQUVEckIsTUFBTSxDQUFDVixTQUFTLENBQUNnQyxXQUFXLENBQUMsQ0FBQ2xCLElBQUksQ0FBQyxtQkFBbUIsQ0FBQztNQUN2REosTUFBTSxDQUFDVixTQUFTLENBQUNpQyxXQUFXLENBQUMsQ0FBQ0MsV0FBVyxDQUFDLENBQUM7SUFDN0MsQ0FBQyxDQUFDO0lBRUZ6QixFQUFFLENBQUMsK0NBQStDLEVBQUUsWUFBWTtNQUM5RDtNQUNBLE1BQU0wQixZQUFZLEdBQUcsdUJBQXVCO01BQzVDdEMsS0FBSyxDQUFDeUIsSUFBSSxDQUFDYyxpQkFBaUIsQ0FBQyxJQUFJQyxLQUFLLENBQUNGLFlBQVksQ0FBQyxDQUFDO01BRXJELE1BQU16QixNQUFNLENBQUNWLFNBQVMsQ0FBQ2dCLFlBQVksQ0FBQyxDQUFDLENBQUMsQ0FBQ3NCLE9BQU8sQ0FBQ0MsT0FBTyxDQUFDLDBCQUEwQkosWUFBWSxFQUFFLENBQUM7SUFDbEcsQ0FBQyxDQUFDO0VBQ0osQ0FBQyxDQUFDO0VBRUZwQyxRQUFRLENBQUMsZ0JBQWdCLEVBQUUsTUFBTTtJQUMvQlUsRUFBRSxDQUFDLHVEQUF1RCxFQUFFLFlBQVk7TUFDdEU7TUFDQVQsU0FBUyxDQUFDZ0MsV0FBVyxHQUFHLG1CQUFtQjtNQUMzQ2hDLFNBQVMsQ0FBQ2lDLFdBQVcsR0FBR08sSUFBSSxDQUFDQyxHQUFHLENBQUMsQ0FBQyxHQUFHLE9BQU8sQ0FBQyxDQUFDOztNQUU5QyxNQUFNVixPQUFPLEdBQUcsTUFBTS9CLFNBQVMsQ0FBQzBDLGNBQWMsQ0FBQyxDQUFDO01BRWhEaEMsTUFBTSxDQUFDcUIsT0FBTyxDQUFDLENBQUNuQixPQUFPLENBQUM7UUFDdEIsZUFBZSxFQUFFO01BQ25CLENBQUMsQ0FBQztJQUNKLENBQUMsQ0FBQztJQUVGSCxFQUFFLENBQUMsZ0RBQWdELEVBQUUsWUFBWTtNQUMvRDtNQUNBVCxTQUFTLENBQUNnQixZQUFZLEdBQUcxQixJQUFJLENBQUNDLEVBQUUsQ0FBQyxDQUFDLENBQUNvRCxrQkFBa0IsQ0FBQyxNQUFNO1FBQzFEM0MsU0FBUyxDQUFDZ0MsV0FBVyxHQUFHLGtCQUFrQjtRQUMxQ2hDLFNBQVMsQ0FBQ2lDLFdBQVcsR0FBR08sSUFBSSxDQUFDQyxHQUFHLENBQUMsQ0FBQyxHQUFHLE9BQU87TUFDOUMsQ0FBQyxDQUFDO01BRUYsTUFBTVYsT0FBTyxHQUFHLE1BQU0vQixTQUFTLENBQUMwQyxjQUFjLENBQUMsQ0FBQztNQUVoRGhDLE1BQU0sQ0FBQ1YsU0FBUyxDQUFDZ0IsWUFBWSxDQUFDLENBQUNHLGdCQUFnQixDQUFDLENBQUM7TUFDakRULE1BQU0sQ0FBQ3FCLE9BQU8sQ0FBQyxDQUFDbkIsT0FBTyxDQUFDO1FBQ3RCLGVBQWUsRUFBRTtNQUNuQixDQUFDLENBQUM7SUFDSixDQUFDLENBQUM7RUFDSixDQUFDLENBQUM7RUFFRmIsUUFBUSxDQUFDLGdCQUFnQixFQUFFLE1BQU07SUFDL0JVLEVBQUUsQ0FBQyxzREFBc0QsRUFBRSxZQUFZO01BQ3JFO01BQ0FULFNBQVMsQ0FBQzBDLGNBQWMsR0FBR3BELElBQUksQ0FBQ0MsRUFBRSxDQUFDLENBQUMsQ0FBQzBCLGlCQUFpQixDQUFDO1FBQ3JELGVBQWUsRUFBRTtNQUNuQixDQUFDLENBQUM7O01BRUY7TUFDQSxNQUFNMkIsWUFBWSxHQUFHO1FBQ25CckIsSUFBSSxFQUFFO1VBQ0pBLElBQUksRUFBRSxDQUNKO1lBQUVzQixFQUFFLEVBQUUsYUFBYTtZQUFFQyxJQUFJLEVBQUU7VUFBYyxDQUFDLEVBQzFDO1lBQUVELEVBQUUsRUFBRSxhQUFhO1lBQUVDLElBQUksRUFBRTtVQUFjLENBQUMsQ0FDM0M7VUFDREMsVUFBVSxFQUFFO1lBQ1ZDLElBQUksRUFBRSxDQUFDO1lBQ1BDLEtBQUssRUFBRSxFQUFFO1lBQ1RDLFVBQVUsRUFBRSxDQUFDO1lBQ2JDLFVBQVUsRUFBRTtVQUNkO1FBQ0Y7TUFDRixDQUFDO01BQ0R0RCxLQUFLLENBQUN1RCxHQUFHLENBQUNuQyxpQkFBaUIsQ0FBQzJCLFlBQVksQ0FBQztNQUV6QyxNQUFNUyxNQUFNLEdBQUc7UUFBRUMsTUFBTSxFQUFFLFNBQVM7UUFBRUwsS0FBSyxFQUFFO01BQUcsQ0FBQztNQUMvQyxNQUFNTSxNQUFNLEdBQUcsTUFBTXZELFNBQVMsQ0FBQ3dELGNBQWMsQ0FBQ0gsTUFBTSxDQUFDO01BRXJEM0MsTUFBTSxDQUFDYixLQUFLLENBQUN1RCxHQUFHLENBQUMsQ0FBQzFCLG9CQUFvQixDQUNwQyxHQUFHekIsVUFBVSxDQUFDSSxPQUFPLGFBQWEsRUFDbEM7UUFDRWdELE1BQU07UUFDTnRCLE9BQU8sRUFBRTtVQUNQLGVBQWUsRUFBRSwwQkFBMEI7VUFDM0MsY0FBYyxFQUFFLGtCQUFrQjtVQUNsQyxRQUFRLEVBQUU7UUFDWjtNQUNGLENBQ0YsQ0FBQztNQUVEckIsTUFBTSxDQUFDNkMsTUFBTSxDQUFDLENBQUMzQyxPQUFPLENBQUNnQyxZQUFZLENBQUNyQixJQUFJLENBQUM7SUFDM0MsQ0FBQyxDQUFDO0lBRUZkLEVBQUUsQ0FBQyw0Q0FBNEMsRUFBRSxZQUFZO01BQzNEO01BQ0FULFNBQVMsQ0FBQzBDLGNBQWMsR0FBR3BELElBQUksQ0FBQ0MsRUFBRSxDQUFDLENBQUMsQ0FBQzBCLGlCQUFpQixDQUFDO1FBQ3JELGVBQWUsRUFBRTtNQUNuQixDQUFDLENBQUM7O01BRUY7TUFDQSxNQUFNa0IsWUFBWSxHQUFHLGdCQUFnQjtNQUNyQ3RDLEtBQUssQ0FBQ3VELEdBQUcsQ0FBQ2hCLGlCQUFpQixDQUFDLElBQUlDLEtBQUssQ0FBQ0YsWUFBWSxDQUFDLENBQUM7TUFFcEQsTUFBTXpCLE1BQU0sQ0FBQ1YsU0FBUyxDQUFDd0QsY0FBYyxDQUFDLENBQUMsQ0FBQyxDQUFDbEIsT0FBTyxDQUFDQyxPQUFPLENBQUMsNkJBQTZCSixZQUFZLEVBQUUsQ0FBQztJQUN2RyxDQUFDLENBQUM7RUFDSixDQUFDLENBQUM7RUFFRnBDLFFBQVEsQ0FBQyxjQUFjLEVBQUUsTUFBTTtJQUM3QlUsRUFBRSxDQUFDLDhEQUE4RCxFQUFFLFlBQVk7TUFDN0U7TUFDQVQsU0FBUyxDQUFDMEMsY0FBYyxHQUFHcEQsSUFBSSxDQUFDQyxFQUFFLENBQUMsQ0FBQyxDQUFDMEIsaUJBQWlCLENBQUM7UUFDckQsZUFBZSxFQUFFO01BQ25CLENBQUMsQ0FBQzs7TUFFRjtNQUNBLE1BQU0yQixZQUFZLEdBQUc7UUFDbkJyQixJQUFJLEVBQUU7VUFDSnNCLEVBQUUsRUFBRSxlQUFlO1VBQ25CQyxJQUFJLEVBQUUsZ0JBQWdCO1VBQ3RCVyxXQUFXLEVBQUU7UUFDZjtNQUNGLENBQUM7TUFDRDVELEtBQUssQ0FBQ3VELEdBQUcsQ0FBQ25DLGlCQUFpQixDQUFDMkIsWUFBWSxDQUFDO01BRXpDLE1BQU1jLFdBQVcsR0FBRyxlQUFlO01BQ25DLE1BQU1ILE1BQU0sR0FBRyxNQUFNdkQsU0FBUyxDQUFDMkQsWUFBWSxDQUFDRCxXQUFXLENBQUM7TUFFeERoRCxNQUFNLENBQUNiLEtBQUssQ0FBQ3VELEdBQUcsQ0FBQyxDQUFDMUIsb0JBQW9CLENBQ3BDLEdBQUd6QixVQUFVLENBQUNJLE9BQU8sZUFBZXFELFdBQVcsRUFBRSxFQUNqRDtRQUNFM0IsT0FBTyxFQUFFO1VBQ1AsZUFBZSxFQUFFLDBCQUEwQjtVQUMzQyxjQUFjLEVBQUUsa0JBQWtCO1VBQ2xDLFFBQVEsRUFBRTtRQUNaO01BQ0YsQ0FDRixDQUFDO01BRURyQixNQUFNLENBQUM2QyxNQUFNLENBQUMsQ0FBQzNDLE9BQU8sQ0FBQ2dDLFlBQVksQ0FBQ3JCLElBQUksQ0FBQztJQUMzQyxDQUFDLENBQUM7SUFFRmQsRUFBRSxDQUFDLHNEQUFzRCxFQUFFLFlBQVk7TUFDckUsTUFBTUMsTUFBTSxDQUFDVixTQUFTLENBQUMyRCxZQUFZLENBQUMsQ0FBQyxDQUFDLENBQUNyQixPQUFPLENBQUNDLE9BQU8sQ0FBQywwQkFBMEIsQ0FBQztJQUNwRixDQUFDLENBQUM7RUFDSixDQUFDLENBQUM7RUFFRnhDLFFBQVEsQ0FBQyxlQUFlLEVBQUUsTUFBTTtJQUM5QlUsRUFBRSxDQUFDLGtEQUFrRCxFQUFFLFlBQVk7TUFDakU7TUFDQVQsU0FBUyxDQUFDMEMsY0FBYyxHQUFHcEQsSUFBSSxDQUFDQyxFQUFFLENBQUMsQ0FBQyxDQUFDMEIsaUJBQWlCLENBQUM7UUFDckQsZUFBZSxFQUFFO01BQ25CLENBQUMsQ0FBQzs7TUFFRjtNQUNBLE1BQU0yQixZQUFZLEdBQUc7UUFDbkJyQixJQUFJLEVBQUU7VUFDSnFDLFdBQVcsRUFBRSxVQUFVO1VBQ3ZCQyxNQUFNLEVBQUUsU0FBUztVQUNqQnRDLElBQUksRUFBRSxDQUNKO1lBQUV1QyxNQUFNLEVBQUUsZUFBZTtZQUFFQyxPQUFPLEVBQUU7VUFBUSxDQUFDLEVBQzdDO1lBQUVELE1BQU0sRUFBRSxRQUFRO1lBQUVDLE9BQU8sRUFBRTtVQUFPLENBQUM7UUFFekM7TUFDRixDQUFDO01BQ0RsRSxLQUFLLENBQUN5QixJQUFJLENBQUNMLGlCQUFpQixDQUFDMkIsWUFBWSxDQUFDO01BRTFDLE1BQU1vQixRQUFRLEdBQUcsWUFBWTtNQUM3QixNQUFNQyxPQUFPLEdBQUc7UUFDZEMsVUFBVSxFQUFFO1VBQ1ZDLFNBQVMsRUFBRSxZQUFZO1VBQ3ZCQyxPQUFPLEVBQUU7UUFDWCxDQUFDO1FBQ0RDLE1BQU0sRUFBRTtNQUNWLENBQUM7TUFFRCxNQUFNZCxNQUFNLEdBQUcsTUFBTXZELFNBQVMsQ0FBQ3NFLGFBQWEsQ0FBQ04sUUFBUSxFQUFFQyxPQUFPLENBQUM7TUFFL0R2RCxNQUFNLENBQUNiLEtBQUssQ0FBQ3lCLElBQUksQ0FBQyxDQUFDSSxvQkFBb0IsQ0FDckMsR0FBR3pCLFVBQVUsQ0FBQ0ksT0FBTyxZQUFZMkQsUUFBUSxVQUFVLEVBQ25EQyxPQUFPLEVBQ1A7UUFDRWxDLE9BQU8sRUFBRTtVQUNQLGVBQWUsRUFBRSwwQkFBMEI7VUFDM0MsY0FBYyxFQUFFLGtCQUFrQjtVQUNsQyxRQUFRLEVBQUU7UUFDWjtNQUNGLENBQ0YsQ0FBQztNQUVEckIsTUFBTSxDQUFDNkMsTUFBTSxDQUFDLENBQUMzQyxPQUFPLENBQUNnQyxZQUFZLENBQUNyQixJQUFJLENBQUM7SUFDM0MsQ0FBQyxDQUFDO0lBRUZkLEVBQUUsQ0FBQyxtREFBbUQsRUFBRSxZQUFZO01BQ2xFLE1BQU1DLE1BQU0sQ0FBQ1YsU0FBUyxDQUFDc0UsYUFBYSxDQUFDLENBQUMsQ0FBQyxDQUFDaEMsT0FBTyxDQUFDQyxPQUFPLENBQUMsdUJBQXVCLENBQUM7SUFDbEYsQ0FBQyxDQUFDO0VBQ0osQ0FBQyxDQUFDO0VBRUZ4QyxRQUFRLENBQUMsZUFBZSxFQUFFLE1BQU07SUFDOUJVLEVBQUUsQ0FBQyxxREFBcUQsRUFBRSxZQUFZO01BQ3BFO01BQ0FULFNBQVMsQ0FBQzBDLGNBQWMsR0FBR3BELElBQUksQ0FBQ0MsRUFBRSxDQUFDLENBQUMsQ0FBQzBCLGlCQUFpQixDQUFDO1FBQ3JELGVBQWUsRUFBRTtNQUNuQixDQUFDLENBQUM7O01BRUY7TUFDQSxNQUFNMkIsWUFBWSxHQUFHO1FBQ25CckIsSUFBSSxFQUFFO1VBQ0pBLElBQUksRUFBRSxDQUNKO1lBQUVzQixFQUFFLEVBQUUsWUFBWTtZQUFFQyxJQUFJLEVBQUU7VUFBYSxDQUFDLEVBQ3hDO1lBQUVELEVBQUUsRUFBRSxZQUFZO1lBQUVDLElBQUksRUFBRTtVQUFhLENBQUMsQ0FDekM7VUFDREMsVUFBVSxFQUFFO1lBQ1ZDLElBQUksRUFBRSxDQUFDO1lBQ1BDLEtBQUssRUFBRSxFQUFFO1lBQ1RDLFVBQVUsRUFBRSxDQUFDO1lBQ2JDLFVBQVUsRUFBRTtVQUNkO1FBQ0Y7TUFDRixDQUFDO01BQ0R0RCxLQUFLLENBQUN1RCxHQUFHLENBQUNuQyxpQkFBaUIsQ0FBQzJCLFlBQVksQ0FBQztNQUV6QyxNQUFNUyxNQUFNLEdBQUc7UUFBRVEsTUFBTSxFQUFFLFFBQVE7UUFBRVUsUUFBUSxFQUFFO01BQVUsQ0FBQztNQUN4RCxNQUFNaEIsTUFBTSxHQUFHLE1BQU12RCxTQUFTLENBQUN3RSxhQUFhLENBQUNuQixNQUFNLENBQUM7TUFFcEQzQyxNQUFNLENBQUNiLEtBQUssQ0FBQ3VELEdBQUcsQ0FBQyxDQUFDMUIsb0JBQW9CLENBQ3BDLEdBQUd6QixVQUFVLENBQUNJLE9BQU8sWUFBWSxFQUNqQztRQUNFZ0QsTUFBTTtRQUNOdEIsT0FBTyxFQUFFO1VBQ1AsZUFBZSxFQUFFLDBCQUEwQjtVQUMzQyxjQUFjLEVBQUUsa0JBQWtCO1VBQ2xDLFFBQVEsRUFBRTtRQUNaO01BQ0YsQ0FDRixDQUFDO01BRURyQixNQUFNLENBQUM2QyxNQUFNLENBQUMsQ0FBQzNDLE9BQU8sQ0FBQ2dDLFlBQVksQ0FBQ3JCLElBQUksQ0FBQztJQUMzQyxDQUFDLENBQUM7RUFDSixDQUFDLENBQUM7RUFFRnhCLFFBQVEsQ0FBQyxpQkFBaUIsRUFBRSxNQUFNO0lBQ2hDVSxFQUFFLENBQUMsb0RBQW9ELEVBQUUsWUFBWTtNQUNuRTtNQUNBVCxTQUFTLENBQUMwQyxjQUFjLEdBQUdwRCxJQUFJLENBQUNDLEVBQUUsQ0FBQyxDQUFDLENBQUMwQixpQkFBaUIsQ0FBQztRQUNyRCxlQUFlLEVBQUU7TUFDbkIsQ0FBQyxDQUFDOztNQUVGO01BQ0EsTUFBTTJCLFlBQVksR0FBRztRQUNuQnJCLElBQUksRUFBRTtVQUNKcUMsV0FBVyxFQUFFLFVBQVU7VUFDdkJDLE1BQU0sRUFBRSxRQUFRO1VBQ2hCWSxTQUFTLEVBQUU7UUFDYjtNQUNGLENBQUM7TUFDRDVFLEtBQUssQ0FBQ3lCLElBQUksQ0FBQ0wsaUJBQWlCLENBQUMyQixZQUFZLENBQUM7TUFFMUMsTUFBTThCLFVBQVUsR0FBRyxjQUFjO01BQ2pDLE1BQU1ULE9BQU8sR0FBRztRQUNkVSxLQUFLLEVBQUU7VUFDTEMsU0FBUyxFQUFFLFdBQVc7VUFDdEJDLE1BQU0sRUFBRTtRQUNWLENBQUM7UUFDREMsS0FBSyxFQUFFO01BQ1QsQ0FBQztNQUVELE1BQU12QixNQUFNLEdBQUcsTUFBTXZELFNBQVMsQ0FBQytFLGVBQWUsQ0FBQ0wsVUFBVSxFQUFFVCxPQUFPLENBQUM7TUFFbkV2RCxNQUFNLENBQUNiLEtBQUssQ0FBQ3lCLElBQUksQ0FBQyxDQUFDSSxvQkFBb0IsQ0FDckMsR0FBR3pCLFVBQVUsQ0FBQ0ksT0FBTyxjQUFjcUUsVUFBVSxVQUFVLEVBQ3ZEVCxPQUFPLEVBQ1A7UUFDRWxDLE9BQU8sRUFBRTtVQUNQLGVBQWUsRUFBRSwwQkFBMEI7VUFDM0MsY0FBYyxFQUFFLGtCQUFrQjtVQUNsQyxRQUFRLEVBQUU7UUFDWjtNQUNGLENBQ0YsQ0FBQztNQUVEckIsTUFBTSxDQUFDNkMsTUFBTSxDQUFDLENBQUMzQyxPQUFPLENBQUNnQyxZQUFZLENBQUNyQixJQUFJLENBQUM7SUFDM0MsQ0FBQyxDQUFDO0lBRUZkLEVBQUUsQ0FBQyxxREFBcUQsRUFBRSxZQUFZO01BQ3BFLE1BQU1DLE1BQU0sQ0FBQ1YsU0FBUyxDQUFDK0UsZUFBZSxDQUFDLENBQUMsQ0FBQyxDQUFDekMsT0FBTyxDQUFDQyxPQUFPLENBQUMseUJBQXlCLENBQUM7SUFDdEYsQ0FBQyxDQUFDO0VBQ0osQ0FBQyxDQUFDO0FBQ0osQ0FBQyxDQUFDIiwiaWdub3JlTGlzdCI6W119