"""
Example of using the UCECS API client with authentication.

This example demonstrates how to interact with the UCECS API using requests
and JWT authentication.
"""

import os
import sys
import json
import logging
import datetime
import uuid
import time

try:
    import requests
except ImportError:
    print("The 'requests' library is not installed. Installing it now...")
    import subprocess
    subprocess.check_call([sys.executable, "-m", "pip", "install", "requests"])
    import requests

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# API base URL
API_BASE_URL = "http://localhost:5000/api/v1"

class UCECSApiClient:
    """
    Client for interacting with the UCECS API.
    """

    def __init__(self, base_url):
        """
        Initialize the API client.

        Args:
            base_url: The base URL of the API
        """
        self.base_url = base_url
        self.access_token = None
        self.refresh_token = None
        self.headers = {}

    def login(self, username, password):
        """
        Authenticate with the API.

        Args:
            username: The username
            password: The password

        Returns:
            True if authentication is successful, False otherwise
        """
        response = requests.post(
            f"{self.base_url}/auth/login",
            json={
                "username": username,
                "password": password
            }
        )

        if response.status_code == 200:
            data = response.json()
            self.access_token = data.get("access_token")
            self.refresh_token = data.get("refresh_token")
            self.headers = {
                "Authorization": f"Bearer {self.access_token}"
            }
            return True

        return False

    def refresh(self):
        """
        Refresh the access token.

        Returns:
            True if refresh is successful, False otherwise
        """
        if not self.refresh_token:
            return False

        response = requests.post(
            f"{self.base_url}/auth/refresh",
            headers={
                "Authorization": f"Bearer {self.refresh_token}"
            }
        )

        if response.status_code == 200:
            data = response.json()
            self.access_token = data.get("access_token")
            self.headers = {
                "Authorization": f"Bearer {self.access_token}"
            }
            return True

        return False

    def get(self, path, params=None):
        """
        Send a GET request to the API.

        Args:
            path: The API path
            params: Query parameters

        Returns:
            The response
        """
        return requests.get(
            f"{self.base_url}/{path}",
            params=params,
            headers=self.headers
        )

    def post(self, path, data=None):
        """
        Send a POST request to the API.

        Args:
            path: The API path
            data: Request body

        Returns:
            The response
        """
        return requests.post(
            f"{self.base_url}/{path}",
            json=data,
            headers=self.headers
        )

    def delete(self, path, params=None):
        """
        Send a DELETE request to the API.

        Args:
            path: The API path
            params: Query parameters

        Returns:
            The response
        """
        return requests.delete(
            f"{self.base_url}/{path}",
            params=params,
            headers=self.headers
        )

def main():
    """Run the API client example."""
    # Wait for the API server to start
    logger.info("Waiting for API server to start...")
    time.sleep(2)

    try:
        # Create an API client
        client = UCECSApiClient(API_BASE_URL)

        # Example 1: Get API information (no authentication required)
        logger.info("Example 1: Get API information")
        response = requests.get(f"{API_BASE_URL}/")
        logger.info(f"API info: {json.dumps(response.json(), indent=2)}")

        # Example 2: Login
        logger.info("\nExample 2: Login")
        if client.login("admin", "admin123"):
            logger.info("Login successful")
            logger.info(f"Access token: {client.access_token[:20]}...")
            logger.info(f"Refresh token: {client.refresh_token[:20]}...")
        else:
            logger.error("Login failed")
            return

        # Example 3: Create evidence
        logger.info("\nExample 3: Create evidence")
        evidence_id = str(uuid.uuid4())
        evidence = {
            'id': evidence_id,
            'type': 'document',
            'source': 'api_example',
            'data': {
                'title': 'Sample Document',
                'content': 'This is a sample document for testing the API.',
                'created_at': datetime.datetime.now().isoformat()
            },
            'metadata': {
                'tags': ['sample', 'document', 'api']
            }
        }

        response = client.post("evidence", evidence)
        logger.info(f"Create evidence response: {json.dumps(response.json(), indent=2)}")

        # Example 4: Get evidence by ID
        logger.info("\nExample 4: Get evidence by ID")
        response = client.get(f"evidence/{evidence_id}")
        logger.info(f"Get evidence response: {json.dumps(response.json(), indent=2)}")

        # Example 5: Validate evidence
        logger.info("\nExample 5: Validate evidence")
        validation_request = {
            'validator_id': 'file_exists'
        }
        response = client.post(f"evidence/{evidence_id}/validate", validation_request)
        logger.info(f"Validate evidence response: {json.dumps(response.json(), indent=2)}")

        # Example 6: Store evidence
        logger.info("\nExample 6: Store evidence")
        storage_request = {
            'storage_id': 'file_system',
            'encrypt': False,
            'create_version': True,
            'version_comment': 'Initial version'
        }
        response = client.post(f"evidence/{evidence_id}/store", storage_request)
        logger.info(f"Store evidence response: {json.dumps(response.json(), indent=2)}")

        # Example 7: Retrieve evidence
        logger.info("\nExample 7: Retrieve evidence")
        response = client.get(f"evidence/{evidence_id}/retrieve", {"storage_id": "file_system"})
        logger.info(f"Retrieve evidence response: {json.dumps(response.json(), indent=2)}")

        # Example 8: Search for evidence
        logger.info("\nExample 8: Search for evidence")
        search_request = {
            'query': {
                'field': 'type',
                'value': 'document'
            },
            'page': 1,
            'page_size': 10
        }
        response = client.post("search/evidence", search_request)
        logger.info(f"Search evidence response: {json.dumps(response.json(), indent=2)}")

        # Example 9: Generate an evidence report
        logger.info("\nExample 9: Generate an evidence report")
        response = client.get("reports/evidence", {"format": "json"})
        logger.info(f"Generate evidence report response: {json.dumps(response.json(), indent=2)}")

        # Example 10: Create a scheduled task
        logger.info("\nExample 10: Create a scheduled task")
        schedule_request = {
            'name': 'Daily Evidence Collection',
            'type': 'collect_evidence',
            'interval': 'daily',
            'parameters': {
                'collector_id': 'file_system',
                'parameters': {
                    'directory': '/tmp/evidence'
                }
            },
            'enabled': True
        }
        response = client.post("schedules", schedule_request)
        logger.info(f"Create schedule response: {json.dumps(response.json(), indent=2)}")

        # Example 11: Get scheduled tasks
        logger.info("\nExample 11: Get scheduled tasks")
        response = client.get("schedules")
        logger.info(f"Get schedules response: {json.dumps(response.json(), indent=2)}")

        # Example 12: Get notifications
        logger.info("\nExample 12: Get notifications")
        response = client.get("notifications", {"limit": 10})
        logger.info(f"Get notifications response: {json.dumps(response.json(), indent=2)}")

        # Example 13: Refresh token
        logger.info("\nExample 13: Refresh token")
        old_token = client.access_token
        if client.refresh():
            logger.info("Token refresh successful")
            logger.info(f"Old token: {old_token[:20]}...")
            logger.info(f"New token: {client.access_token[:20]}...")
        else:
            logger.error("Token refresh failed")

        # Example 14: Delete evidence
        logger.info("\nExample 14: Delete evidence")
        response = client.delete(f"evidence/{evidence_id}", {"storage_id": "file_system"})
        logger.info(f"Delete evidence response: {json.dumps(response.json(), indent=2)}")

    except requests.exceptions.ConnectionError:
        logger.error("Failed to connect to the API server. Make sure it's running.")
    except Exception as e:
        logger.error(f"Error: {e}")

if __name__ == "__main__":
    main()
