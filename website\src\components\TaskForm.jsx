import React from 'react';
import { NovaForm, NovaField, NovaButton } from '@novafuse/nova-vision';
import { useNovaState } from '@novafuse/nova-core';

// Define form schema using NovaVision's schema format
const formSchema = {
  type: 'object',
  properties: {
    task_type: {
      type: 'string',
      title: 'Task Type',
      enum: [
        { value: 'security_update', label: 'Security Update' },
        { value: 'data_processing', label: 'Data Processing' },
        { value: 'model_training', label: 'Model Training' },
        { value: 'api_call', label: 'API Call' }
      ],
      default: 'api_call',
      widget: 'select',
      required: true
    },
    vendor_qscore: {
      type: 'number',
      title: 'Vendor Q-Score',
      default: 7.5,
      minimum: 0,
      maximum: 10,
      step: 0.1,
      widget: 'slider',
      description: 'Vendor quality score (0-10)'
    },
    compliance_level: {
      type: 'string',
      title: 'Compliance Level',
      enum: [
        { value: 'low', label: 'Low' },
        { value: 'standard', label: 'Standard' },
        { value: 'high', label: 'High' },
        { value: 'critical', label: 'Critical' }
      ],
      default: 'standard',
      widget: 'radio',
      required: true
    },
    description: {
      type: 'string',
      title: 'Task Description',
      widget: 'textarea',
      rows: 3,
      required: true
    },
    priority: {
      type: 'string',
      title: 'Priority',
      enum: [
        { value: 'low', label: 'Low' },
        { value: 'medium', label: 'Medium' },
        { value: 'high', label: 'High' }
      ],
      default: 'medium',
      widget: 'select',
      required: true
    }
  },
  required: ['task_type', 'compliance_level', 'description']
};

const TaskForm = ({ onSubmit, isLoading }) => {
  // Use NovaCore for form state management
  const [formData, setFormData] = useNovaState('taskForm', {
    task_type: 'api_call',
    vendor_qscore: 7.5,
    compliance_level: 'standard',
    description: '',
    priority: 'medium'
  });

  const handleSubmit = (formData) => {
    // Ensure vendor_qscore is a number
    const submissionData = {
      ...formData,
      vendor_qscore: Number(formData.vendor_qscore) || 0
    };
    
    // Clear form after submission if successful
    const clearForm = () => setFormData({
      task_type: 'api_call',
      vendor_qscore: 7.5,
      compliance_level: 'standard',
      description: '',
      priority: 'medium'
    });
    
    onSubmit(submissionData, clearForm);
  };

  return (
    <div className="nova-task-form">
      <NovaForm
        schema={formSchema}
        formData={formData}
        onChange={({ formData }) => setFormData(formData)}
        onSubmit={handleSubmit}
        className="nova-form"
        uiSchema={{
          'ui:submitButtonOptions': {
            submitText: 'Submit Assessment',
            norender: true
          },
          'ui:options': {
            label: false
          }
        }}
      >
        <NovaField name="task_type" />
        <NovaField name="vendor_qscore" />
        <NovaField name="compliance_level" />
        <NovaField name="description" />
        <NovaField name="priority" />
        
        <div className="nova-form-actions">
          <NovaButton
            type="submit"
            variant="primary"
            loading={isLoading}
            disabled={isLoading}
            fullWidth
          >
            {isLoading ? 'Processing...' : 'Submit Assessment'}
          </NovaButton>
        </div>
      </NovaForm>
    </div>
  );
};

export default TaskForm;
