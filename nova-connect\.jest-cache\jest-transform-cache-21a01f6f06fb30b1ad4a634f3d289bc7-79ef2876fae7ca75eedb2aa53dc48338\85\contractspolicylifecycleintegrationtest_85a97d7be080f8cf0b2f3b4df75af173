03ef771600414a5adc8b7e39b326867d
// Mock logger
_getJestObj().mock('../../../../utils/logger', () => ({
  createLogger: jest.fn(() => ({
    info: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn()
  }))
}));
function _getJestObj() {
  const {
    jest
  } = require("@jest/globals");
  _getJestObj = () => jest;
  return jest;
}
/**
 * Integration tests for the Contracts & Policy Lifecycle Connector
 */

const nock = require('nock');
const ContractsPolicyLifecycleConnector = require('../../../../connector/implementations/contracts-policy-lifecycle');
describe('ContractsPolicyLifecycleConnector Integration', () => {
  let connector;
  const baseUrl = 'https://api.test.com';
  beforeAll(() => {
    // Disable real HTTP requests
    nock.disableNetConnect();
  });
  afterAll(() => {
    // Enable real HTTP requests
    nock.enableNetConnect();
  });
  beforeEach(() => {
    // Reset nock
    nock.cleanAll();

    // Create connector instance
    connector = new ContractsPolicyLifecycleConnector({
      baseUrl
    }, {
      clientId: 'test-client-id',
      clientSecret: 'test-client-secret',
      redirectUri: 'https://test-redirect.com'
    });

    // Mock authentication
    nock(baseUrl).post('/oauth2/token').reply(200, {
      access_token: 'test-access-token',
      expires_in: 3600
    });
  });
  describe('Contract Management', () => {
    it('should list contracts', async () => {
      // Mock contracts endpoint
      const mockContracts = {
        data: [{
          id: 'contract-1',
          title: 'Contract 1',
          status: 'active',
          startDate: '2023-01-01',
          endDate: '2023-12-31'
        }, {
          id: 'contract-2',
          title: 'Contract 2',
          status: 'active',
          startDate: '2023-02-01',
          endDate: '2023-12-31'
        }],
        pagination: {
          page: 1,
          limit: 20,
          totalItems: 2,
          totalPages: 1
        }
      };
      nock(baseUrl).get('/contracts').query({
        status: 'active'
      }).reply(200, mockContracts);

      // Initialize connector
      await connector.initialize();

      // List contracts
      const result = await connector.listContracts({
        status: 'active'
      });

      // Verify result
      expect(result).toEqual(mockContracts);
    });
    it('should get a specific contract', async () => {
      // Mock contract endpoint
      const mockContract = {
        id: 'contract-123',
        title: 'Test Contract',
        description: 'Test Description',
        status: 'active',
        type: 'service',
        parties: [{
          id: 'party-1',
          name: 'Acme Corp',
          role: 'client'
        }, {
          id: 'party-2',
          name: 'Tech Solutions Inc',
          role: 'vendor'
        }],
        startDate: '2023-01-01',
        endDate: '2023-12-31',
        value: 50000,
        currency: 'USD'
      };
      nock(baseUrl).get('/contracts/contract-123').reply(200, mockContract);

      // Initialize connector
      await connector.initialize();

      // Get contract
      const result = await connector.getContract('contract-123');

      // Verify result
      expect(result).toEqual(mockContract);
    });
    it('should create a new contract', async () => {
      // Contract data
      const contractData = {
        title: 'New Contract',
        description: 'New contract description',
        type: 'service',
        parties: [{
          name: 'Acme Corp',
          role: 'client'
        }, {
          name: 'New Tech Solutions',
          role: 'vendor'
        }],
        startDate: '2023-07-01',
        endDate: '2024-06-30',
        value: 75000,
        currency: 'USD'
      };

      // Mock response
      const mockResponse = {
        id: 'contract-new',
        ...contractData,
        status: 'draft',
        createdAt: '2023-06-15T10:30:00Z',
        updatedAt: '2023-06-15T10:30:00Z'
      };
      nock(baseUrl).post('/contracts', contractData).reply(201, mockResponse);

      // Initialize connector
      await connector.initialize();

      // Create contract
      const result = await connector.createContract(contractData);

      // Verify result
      expect(result).toEqual(mockResponse);
    });
    it('should update an existing contract', async () => {
      // Contract update data
      const contractId = 'contract-123';
      const updateData = {
        title: 'Updated Contract',
        description: 'Updated description',
        status: 'active'
      };

      // Mock response
      const mockResponse = {
        id: contractId,
        title: 'Updated Contract',
        description: 'Updated description',
        status: 'active',
        type: 'service',
        parties: [{
          id: 'party-1',
          name: 'Acme Corp',
          role: 'client'
        }, {
          id: 'party-2',
          name: 'Tech Solutions Inc',
          role: 'vendor'
        }],
        startDate: '2023-01-01',
        endDate: '2023-12-31',
        value: 50000,
        currency: 'USD',
        updatedAt: '2023-06-15T11:45:00Z'
      };
      nock(baseUrl).put(`/contracts/${contractId}`, updateData).reply(200, mockResponse);

      // Initialize connector
      await connector.initialize();

      // Update contract
      const result = await connector.updateContract(contractId, updateData);

      // Verify result
      expect(result).toEqual(mockResponse);
    });
    it('should delete a contract', async () => {
      // Contract ID
      const contractId = 'contract-123';
      nock(baseUrl).delete(`/contracts/${contractId}`).reply(204);

      // Initialize connector
      await connector.initialize();

      // Delete contract
      await connector.deleteContract(contractId);

      // If no error is thrown, the test passes
      expect(true).toBe(true);
    });
  });
  describe('Policy Management', () => {
    it('should list policies', async () => {
      // Mock policies endpoint
      const mockPolicies = {
        data: [{
          id: 'policy-1',
          title: 'Information Security Policy',
          status: 'active',
          category: 'security',
          version: '1.2'
        }, {
          id: 'policy-2',
          title: 'Data Protection Policy',
          status: 'active',
          category: 'privacy',
          version: '1.0'
        }],
        pagination: {
          page: 1,
          limit: 20,
          totalItems: 2,
          totalPages: 1
        }
      };
      nock(baseUrl).get('/policies').query({
        status: 'active'
      }).reply(200, mockPolicies);

      // Initialize connector
      await connector.initialize();

      // List policies
      const result = await connector.listPolicies({
        status: 'active'
      });

      // Verify result
      expect(result).toEqual(mockPolicies);
    });
    it('should get a specific policy', async () => {
      // Mock policy endpoint
      const mockPolicy = {
        id: 'policy-123',
        title: 'Information Security Policy',
        description: 'Policy governing information security practices',
        content: '# Information Security Policy\n\n## 1. Introduction\n\nThis policy...',
        status: 'active',
        category: 'security',
        version: '1.2',
        effectiveDate: '2023-01-15',
        reviewDate: '2024-01-15'
      };
      nock(baseUrl).get('/policies/policy-123').reply(200, mockPolicy);

      // Initialize connector
      await connector.initialize();

      // Get policy
      const result = await connector.getPolicy('policy-123');

      // Verify result
      expect(result).toEqual(mockPolicy);
    });
    it('should create a new policy', async () => {
      // Policy data
      const policyData = {
        title: 'New Security Policy',
        description: 'New security policy description',
        content: '# New Security Policy\n\n## 1. Introduction\n\nThis policy...',
        category: 'security'
      };

      // Mock response
      const mockResponse = {
        id: 'policy-new',
        ...policyData,
        status: 'draft',
        version: '1.0',
        createdAt: '2023-06-15T10:30:00Z',
        updatedAt: '2023-06-15T10:30:00Z'
      };
      nock(baseUrl).post('/policies', policyData).reply(201, mockResponse);

      // Initialize connector
      await connector.initialize();

      // Create policy
      const result = await connector.createPolicy(policyData);

      // Verify result
      expect(result).toEqual(mockResponse);
    });
  });
  describe('Error Handling', () => {
    it('should handle authentication errors', async () => {
      // Clean previous nock mocks
      nock.cleanAll();

      // Mock authentication error
      nock(baseUrl).post('/oauth2/token').reply(401, {
        error: 'invalid_client',
        error_description: 'Invalid client credentials'
      });

      // Try to initialize connector
      await expect(connector.initialize()).rejects.toThrow('Authentication failed');
    });
    it('should handle not found errors', async () => {
      // Mock not found error
      nock(baseUrl).get('/contracts/non-existent').reply(404, {
        error: 'not_found',
        error_description: 'Contract not found'
      });

      // Initialize connector
      await connector.initialize();

      // Try to get non-existent contract
      await expect(connector.getContract('non-existent')).rejects.toThrow('Error getting contract');
    });
    it('should handle validation errors', async () => {
      // Contract data with missing required fields
      const invalidData = {
        title: 'Invalid Contract'
        // Missing required fields: type, parties, startDate
      };

      // Mock validation error
      nock(baseUrl).post('/contracts', invalidData).reply(400, {
        error: 'validation_error',
        error_description: 'Validation failed',
        errors: [{
          field: 'type',
          message: 'Type is required'
        }, {
          field: 'parties',
          message: 'Parties are required'
        }, {
          field: 'startDate',
          message: 'Start date is required'
        }]
      });

      // Initialize connector
      await connector.initialize();

      // Try to create invalid contract
      await expect(connector.createContract(invalidData)).rejects.toThrow('type is required');
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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