/**
 * User Validation Schemas
 * 
 * This file contains validation schemas for user-related endpoints.
 */

const Joi = require('joi');
const { commonSchemas } = require('../common/commonSchemas');

/**
 * Create user schema
 */
const createUserSchema = {
  body: Joi.object({
    email: commonSchemas.email.required(),
    firstName: Joi.string().required(),
    lastName: Joi.string().required(),
    password: commonSchemas.password.optional(),
    role: commonSchemas.role.required(),
    teamIds: commonSchemas.idArray.optional(),
    isActive: Joi.boolean().default(true),
    sendInvite: Joi.boolean().default(true),
    permissions: commonSchemas.permissionArray.optional(),
    metadata: Joi.object().optional()
  })
};

/**
 * Update user schema
 */
const updateUserSchema = {
  params: Joi.object({
    userId: commonSchemas.id.required()
  }),
  body: Joi.object({
    firstName: Joi.string().optional(),
    lastName: Joi.string().optional(),
    role: commonSchemas.role.optional(),
    teamIds: commonSchemas.idArray.optional(),
    isActive: Joi.boolean().optional(),
    permissions: commonSchemas.permissionArray.optional(),
    metadata: Joi.object().optional()
  }).min(1)
};

/**
 * Get user schema
 */
const getUserSchema = {
  params: Joi.object({
    userId: commonSchemas.id.required()
  })
};

/**
 * Delete user schema
 */
const deleteUserSchema = {
  params: Joi.object({
    userId: commonSchemas.id.required()
  })
};

/**
 * List users schema
 */
const listUsersSchema = {
  query: Joi.object({
    page: commonSchemas.page,
    limit: commonSchemas.limit,
    sortBy: Joi.string().valid('firstName', 'lastName', 'email', 'role', 'createdAt', 'updatedAt').default('createdAt'),
    sortOrder: commonSchemas.sortOrder,
    search: Joi.string().allow('').optional(),
    role: commonSchemas.role.optional(),
    teamId: commonSchemas.id.optional(),
    isActive: Joi.boolean().optional()
  })
};

/**
 * Update user profile schema
 */
const updateUserProfileSchema = {
  body: Joi.object({
    firstName: Joi.string().optional(),
    lastName: Joi.string().optional(),
    phone: Joi.string().optional(),
    jobTitle: Joi.string().optional(),
    company: Joi.string().optional(),
    timezone: Joi.string().optional(),
    language: Joi.string().optional(),
    avatar: Joi.string().uri().optional(),
    metadata: Joi.object().optional()
  }).min(1)
};

/**
 * Update user password schema
 */
const updateUserPasswordSchema = {
  params: Joi.object({
    userId: commonSchemas.id.required()
  }),
  body: Joi.object({
    password: commonSchemas.password.required()
  })
};

/**
 * Add user to team schema
 */
const addUserToTeamSchema = {
  params: Joi.object({
    userId: commonSchemas.id.required(),
    teamId: commonSchemas.id.required()
  })
};

/**
 * Remove user from team schema
 */
const removeUserFromTeamSchema = {
  params: Joi.object({
    userId: commonSchemas.id.required(),
    teamId: commonSchemas.id.required()
  })
};

/**
 * Get user teams schema
 */
const getUserTeamsSchema = {
  params: Joi.object({
    userId: commonSchemas.id.required()
  })
};

/**
 * Get user permissions schema
 */
const getUserPermissionsSchema = {
  params: Joi.object({
    userId: commonSchemas.id.required()
  })
};

/**
 * Update user permissions schema
 */
const updateUserPermissionsSchema = {
  params: Joi.object({
    userId: commonSchemas.id.required()
  }),
  body: Joi.object({
    permissions: commonSchemas.permissionArray.required()
  })
};

/**
 * Invite user schema
 */
const inviteUserSchema = {
  body: Joi.object({
    email: commonSchemas.email.required(),
    role: commonSchemas.role.required(),
    teamIds: commonSchemas.idArray.optional(),
    permissions: commonSchemas.permissionArray.optional(),
    message: Joi.string().optional()
  })
};

/**
 * Accept invitation schema
 */
const acceptInvitationSchema = {
  body: Joi.object({
    token: Joi.string().required(),
    firstName: Joi.string().required(),
    lastName: Joi.string().required(),
    password: commonSchemas.password.required(),
    confirmPassword: Joi.string().valid(Joi.ref('password')).required().messages({
      'any.only': 'Passwords must match'
    })
  })
};

module.exports = {
  createUserSchema,
  updateUserSchema,
  getUserSchema,
  deleteUserSchema,
  listUsersSchema,
  updateUserProfileSchema,
  updateUserPasswordSchema,
  addUserToTeamSchema,
  removeUserFromTeamSchema,
  getUserTeamsSchema,
  getUserPermissionsSchema,
  updateUserPermissionsSchema,
  inviteUserSchema,
  acceptInvitationSchema
};
