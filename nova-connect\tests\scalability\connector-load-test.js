/**
 * NovaConnect UAC Connector Load Test
 * 
 * This script tests the performance and scalability of NovaConnect UAC connectors.
 * 
 * Usage:
 * k6 run connector-load-test.js
 */

import http from 'k6/http';
import { check, group, sleep } from 'k6';
import { Rate, Trend } from 'k6/metrics';
import { SharedArray } from 'k6/data';
import { randomItem, randomIntBetween } from 'https://jslib.k6.io/k6-utils/1.2.0/index.js';
import { options } from './k6-config.js';

// Custom metrics
const errorRate = new Rate('error_rate');
const connectorExecutionDuration = new Trend('connector_execution_duration');
const awsConnectorDuration = new Trend('aws_connector_duration');
const azureConnectorDuration = new Trend('azure_connector_duration');
const gcpConnectorDuration = new Trend('gcp_connector_duration');
const httpConnectorDuration = new Trend('http_connector_duration');
const databaseConnectorDuration = new Trend('database_connector_duration');

// Load test data
const testData = new SharedArray('test_data', function() {
  return JSON.parse(open('./test-data.json'));
});

// API base URL
const baseUrl = __ENV.API_BASE_URL || 'http://localhost:3001';

// API key for authentication
const apiKey = __ENV.API_KEY || 'test-api-key';

// Default headers
const headers = {
  'Content-Type': 'application/json',
  'X-API-Key': apiKey
};

/**
 * Setup function - called once per VU
 */
export function setup() {
  // Verify API is accessible
  const res = http.get(`${baseUrl}/health`);
  if (res.status !== 200) {
    throw new Error(`API is not accessible: ${res.status} ${res.body}`);
  }
  
  console.log('API is accessible, starting connector load test');
  
  // Create test connectors
  const connectors = {};
  
  // Create AWS connector
  if (testData.connectors.find(c => c.type === 'aws')) {
    const awsConnector = testData.connectors.find(c => c.type === 'aws');
    const awsRes = http.post(
      `${baseUrl}/api/connectors`,
      JSON.stringify(awsConnector),
      { headers }
    );
    
    if (awsRes.status === 201) {
      connectors.aws = JSON.parse(awsRes.body).id;
    }
  }
  
  // Create Azure connector
  if (testData.connectors.find(c => c.type === 'azure')) {
    const azureConnector = testData.connectors.find(c => c.type === 'azure');
    const azureRes = http.post(
      `${baseUrl}/api/connectors`,
      JSON.stringify(azureConnector),
      { headers }
    );
    
    if (azureRes.status === 201) {
      connectors.azure = JSON.parse(azureRes.body).id;
    }
  }
  
  // Create GCP connector
  if (testData.connectors.find(c => c.type === 'gcp')) {
    const gcpConnector = testData.connectors.find(c => c.type === 'gcp');
    const gcpRes = http.post(
      `${baseUrl}/api/connectors`,
      JSON.stringify(gcpConnector),
      { headers }
    );
    
    if (gcpRes.status === 201) {
      connectors.gcp = JSON.parse(gcpRes.body).id;
    }
  }
  
  // Create HTTP connector
  if (testData.connectors.find(c => c.type === 'http')) {
    const httpConnector = testData.connectors.find(c => c.type === 'http');
    const httpRes = http.post(
      `${baseUrl}/api/connectors`,
      JSON.stringify(httpConnector),
      { headers }
    );
    
    if (httpRes.status === 201) {
      connectors.http = JSON.parse(httpRes.body).id;
    }
  }
  
  // Create Database connector
  if (testData.connectors.find(c => c.type === 'database')) {
    const dbConnector = testData.connectors.find(c => c.type === 'database');
    const dbRes = http.post(
      `${baseUrl}/api/connectors`,
      JSON.stringify(dbConnector),
      { headers }
    );
    
    if (dbRes.status === 201) {
      connectors.database = JSON.parse(dbRes.body).id;
    }
  }
  
  return {
    connectors,
    testRequests: testData.testRequests
  };
}

/**
 * Teardown function - called once after all VUs are done
 */
export function teardown(data) {
  // Clean up connectors
  for (const type in data.connectors) {
    const connectorId = data.connectors[type];
    http.del(
      `${baseUrl}/api/connectors/${connectorId}`,
      null,
      { headers }
    );
  }
  
  console.log('Connector load test completed and resources cleaned up');
}

/**
 * Default function - called for each VU iteration
 */
export default function(data) {
  group('Connector Execution Performance', () => {
    // Test AWS connector
    if (data.connectors.aws) {
      testConnector(data.connectors.aws, 'aws', data.testRequests, awsConnectorDuration);
    }
    
    // Test Azure connector
    if (data.connectors.azure) {
      testConnector(data.connectors.azure, 'azure', data.testRequests, azureConnectorDuration);
    }
    
    // Test GCP connector
    if (data.connectors.gcp) {
      testConnector(data.connectors.gcp, 'gcp', data.testRequests, gcpConnectorDuration);
    }
    
    // Test HTTP connector
    if (data.connectors.http) {
      testConnector(data.connectors.http, 'http', data.testRequests, httpConnectorDuration);
    }
    
    // Test Database connector
    if (data.connectors.database) {
      testConnector(data.connectors.database, 'database', data.testRequests, databaseConnectorDuration);
    }
    
    // Small sleep to prevent overwhelming the server
    sleep(randomIntBetween(0.1, 0.5));
  });
  
  group('Concurrent Connector Execution', () => {
    // Test concurrent connector execution
    testConcurrentConnectors(data);
    
    // Small sleep to prevent overwhelming the server
    sleep(randomIntBetween(0.1, 0.5));
  });
  
  group('Connector Error Handling', () => {
    // Test connector error handling
    testConnectorErrorHandling(data);
    
    // Small sleep to prevent overwhelming the server
    sleep(randomIntBetween(0.1, 0.5));
  });
}

/**
 * Test connector execution
 */
function testConnector(connectorId, connectorType, testRequests, durationMetric) {
  // Get random test request
  const testRequest = randomItem(testRequests);
  
  // Start timer
  const startTime = new Date();
  
  // Execute connector
  const executeRes = http.post(
    `${baseUrl}/api/connectors/${connectorId}/execute`,
    JSON.stringify(testRequest),
    { headers }
  );
  
  // Calculate duration
  const duration = new Date() - startTime;
  
  // Record execution duration
  durationMetric.add(duration);
  connectorExecutionDuration.add(duration);
  
  check(executeRes, {
    [`${connectorType} connector execute status is 200`]: (r) => r.status === 200,
    [`${connectorType} connector execute response time < threshold`]: (r) => {
      // Different thresholds based on connector type
      const thresholds = {
        aws: 1000,
        azure: 1000,
        gcp: 1000,
        http: 500,
        database: 300
      };
      return r.timings.duration < thresholds[connectorType];
    },
    [`${connectorType} connector execute returns result`]: (r) => JSON.parse(r.body).result !== undefined
  });
  
  errorRate.add(executeRes.status !== 200);
}

/**
 * Test concurrent connector execution
 */
function testConcurrentConnectors(data) {
  // Create requests
  const requests = [];
  
  // Add connector requests
  for (const type in data.connectors) {
    const connectorId = data.connectors[type];
    const testRequest = randomItem(data.testRequests);
    
    requests.push({
      method: 'POST',
      url: `${baseUrl}/api/connectors/${connectorId}/execute`,
      body: JSON.stringify(testRequest),
      params: { headers }
    });
  }
  
  // Start timer
  const startTime = new Date();
  
  // Send concurrent requests
  const responses = http.batch(requests);
  
  // Calculate duration
  const duration = new Date() - startTime;
  
  // Check responses
  for (let i = 0; i < responses.length; i++) {
    const res = responses[i];
    
    check(res, {
      'concurrent connector execute status is 200': (r) => r.status === 200,
      'concurrent connector execute returns result': (r) => JSON.parse(r.body).result !== undefined
    });
    
    errorRate.add(res.status !== 200);
  }
  
  // Check total duration
  check(null, {
    'concurrent connector execute total time < threshold': () => duration < 2000
  });
}

/**
 * Test connector error handling
 */
function testConnectorErrorHandling(data) {
  // Get a connector ID
  const connectorId = Object.values(data.connectors)[0];
  
  if (!connectorId) {
    console.log('No connector available for error handling test');
    return;
  }
  
  // Test invalid request
  const invalidRequestRes = http.post(
    `${baseUrl}/api/connectors/${connectorId}/execute`,
    JSON.stringify({ invalid: 'request' }),
    { headers }
  );
  
  check(invalidRequestRes, {
    'invalid request status is 400': (r) => r.status === 400,
    'invalid request response time < 100ms': (r) => r.timings.duration < 100,
    'invalid request returns error': (r) => JSON.parse(r.body).error !== undefined
  });
  
  // Test non-existent connector
  const nonExistentRes = http.post(
    `${baseUrl}/api/connectors/non-existent-id/execute`,
    JSON.stringify(randomItem(data.testRequests)),
    { headers }
  );
  
  check(nonExistentRes, {
    'non-existent connector status is 404': (r) => r.status === 404,
    'non-existent connector response time < 100ms': (r) => r.timings.duration < 100,
    'non-existent connector returns error': (r) => JSON.parse(r.body).error !== undefined
  });
  
  // Test timeout
  const timeoutRes = http.post(
    `${baseUrl}/api/connectors/${connectorId}/execute`,
    JSON.stringify({ ...randomItem(data.testRequests), timeout: true }),
    { headers, timeout: '5s' }
  );
  
  // Either timeout or error response is acceptable
  check(timeoutRes, {
    'timeout request handled correctly': (r) => {
      return r.status === 408 || r.status === 500 || r.error === 'Request timed out';
    }
  });
}
