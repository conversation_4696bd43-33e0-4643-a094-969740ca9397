/**
 * IP Protection Module
 * Implements patent protection, watermarking, and code obfuscation
 */

class IPProtection {
  constructor(options = {}) {
    this.options = {
      protectionLevel: 'high', // 'low' | 'medium' | 'high'
      watermarkText: '© NOVAFUSE QUANTUM STREAM',
      watermarkOpacity: 0.05,
      watermarkFont: '12px Arial',
      watermarkColor: '#000000',
      patentNumbers: ['US63/XXXXXX', 'US63/YYYYYY'],
      ...options
    };
    
    this.initialized = false;
    this.watermarkCanvas = null;
  }

  /**
   * Initialize the protection system
   */
  initialize() {
    if (this.initialized) return true;
    
    try {
      // Create watermark canvas if needed
      if (this.options.protectionLevel !== 'none') {
        this.watermarkCanvas = this.createWatermarkCanvas();
      }
      
      // Inject patent notices
      this.injectPatentNotices();
      
      // Apply obfuscation based on protection level
      this.applyObfuscation();
      
      this.initialized = true;
      return true;
      
    } catch (error) {
      console.error('Failed to initialize IP protection:', error);
      return false;
    }
  }

  /**
   * Create a canvas with watermark pattern
   */
  createWatermarkCanvas() {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    // Set canvas size
    canvas.width = 800;
    canvas.height = 600;
    
    // Fill with white background
    ctx.fillStyle = 'white';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // Draw watermark text
    ctx.font = this.options.watermarkFont;
    ctx.fillStyle = this.options.watermarkColor;
    ctx.globalAlpha = this.options.watermarkOpacity;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    
    // Draw watermark pattern
    const text = `${this.options.watermarkText} ${this.options.patentNumbers.join(' ')}`;
    const angle = -20 * Math.PI / 180;
    const spacing = 200;
    
    ctx.save();
    ctx.translate(canvas.width / 2, canvas.height / 2);
    ctx.rotate(angle);
    
    // Draw watermark in a grid
    for (let x = -canvas.width; x < canvas.width * 2; x += spacing) {
      for (let y = -canvas.height; y < canvas.height * 2; y += spacing / 2) {
        ctx.fillText(text, x, y);
      }
    }
    
    ctx.restore();
    
    return canvas;
  }

  /**
   * Apply watermark to a canvas element
   */
  applyWatermark(targetCanvas) {
    if (!this.watermarkCanvas || this.options.protectionLevel === 'none') {
      return false;
    }
    
    try {
      const ctx = targetCanvas.getContext('2d');
      
      // Save current state
      ctx.save();
      
      // Draw watermark
      const pattern = ctx.createPattern(this.watermarkCanvas, 'repeat');
      ctx.fillStyle = pattern;
      ctx.globalCompositeOperation = 'overlay';
      ctx.globalAlpha = this.options.watermarkOpacity;
      
      // Fill canvas with watermark pattern
      ctx.fillRect(0, 0, targetCanvas.width, targetCanvas.height);
      
      // Restore state
      ctx.restore();
      
      return true;
      
    } catch (error) {
      console.error('Failed to apply watermark:', error);
      return false;
    }
  }

  /**
   * Inject patent notices into the page
   */
  injectPatentNotices() {
    if (this.options.protectionLevel === 'none') return;
    
    try {
      // Create notice element
      const notice = document.createElement('div');
      notice.style.position = 'fixed';
      notice.style.bottom = '10px';
      notice.style.right = '10px';
      notice.style.padding = '5px 10px';
      notice.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
      notice.style.color = 'white';
      notice.style.fontSize = '10px';
      notice.style.fontFamily = 'Arial, sans-serif';
      notice.style.zIndex = '9999';
      notice.style.borderRadius = '3px';
      notice.style.opacity = '0.7';
      notice.style.transition = 'opacity 0.3s';
      notice.style.pointerEvents = 'none';
      notice.textContent = `Protected by ${this.options.patentNumbers.join(', ')}`;
      
      // Add hover effect
      notice.addEventListener('mouseenter', () => {
        notice.style.opacity = '1';
      });
      
      notice.addEventListener('mouseleave', () => {
        notice.style.opacity = '0.7';
      });
      
      // Add to document
      document.body.appendChild(notice);
      
      return true;
      
    } catch (error) {
      console.error('Failed to inject patent notices:', error);
      return false;
    }
  }

  /**
   * Apply code obfuscation based on protection level
   */
  applyObfuscation() {
    if (this.options.protectionLevel === 'none') return;
    
    try {
      // In a real implementation, this would use a proper obfuscation tool
      // For now, we'll just add some basic protection
      
      // Make it harder to access internal methods
      Object.defineProperty(window, 'QuantumStream', {
        value: {},
        writable: false,
        configurable: false
      });
      
      // Add anti-tampering
      if (this.options.protectionLevel === 'high') {
        this.addTamperDetection();
      }
      
      return true;
      
    } catch (error) {
      console.error('Failed to apply obfuscation:', error);
      return false;
    }
  }

  /**
   * Add tamper detection
   */
  addTamperDetection() {
    // Check for debugger
    const detectDebugger = () => {
      const startTime = performance.now();
      debugger;
      const endTime = performance.now();
      
      // If the debugger is active, the time difference will be significant
      if (endTime - startTime > 100) {
        this.onTamperDetected('debugger');
      }
    };
    
    // Run detection at random intervals
    setInterval(detectDebugger, Math.random() * 5000 + 1000);
    
    // Also check for common dev tools
    window.addEventListener('devtoolschange', (e) => {
      if (e.detail.isOpen) {
        this.onTamperDetected('devtools');
      }
    });
  }

  /**
   * Handle tamper detection
   */
  onTamperDetected(type) {
    console.warn(`[SECURITY] Tamper detected: ${type}`);
    
    // In a production environment, you might want to:
    // 1. Notify the server
    // 2. Disable functionality
    // 3. Display a warning
    // 4. Trigger additional security measures
    
    // For now, we'll just log it
    if (console && console.warn) {
      console.warn('Unauthorized access detected. This incident will be reported.');
    }
    
    // Optional: Redirect or show a warning
    if (this.options.protectionLevel === 'high') {
      // In a real implementation, you might want to be less intrusive
      // and just degrade functionality rather than showing an alert
      alert('Security violation detected. Please contact support.');
    }
  }

  /**
   * Protect a function with tamper detection
   */
  protectFunction(fn, name = 'anonymous') {
    if (this.options.protectionLevel === 'none') {
      return fn;
    }
    
    const self = this;
    
    return function(...args) {
      try {
        // Check for tampering
        if (self.options.protectionLevel === 'high') {
          const stack = new Error().stack || '';
          if (stack.includes('eval at') || stack.includes('Function.')) {
            self.onTamperDetected(`function_tamper:${name}`);
            throw new Error('Execution of protected function prevented');
          }
        }
        
        // Call the original function
        return fn.apply(this, args);
        
      } catch (error) {
        console.error(`Error in protected function ${name}:`, error);
        throw error;
      }
    };
  }
}

// Singleton instance
let instance = null;

/**
 * Get the IP protection instance
 */
function getIPProtection(options) {
  if (!instance) {
    instance = new IPProtection(options);
    
    // Auto-initialize if document is ready
    if (typeof document !== 'undefined' && 
        (document.readyState === 'complete' || 
         document.readyState === 'interactive')) {
      instance.initialize();
    } else if (typeof document !== 'undefined') {
      document.addEventListener('DOMContentLoaded', () => {
        instance.initialize();
      });
    }
  }
  
  return instance;
}

// Export as ES module
export { IPProtection, getIPProtection };

// Also support CommonJS for backward compatibility
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { IPProtection, getIPProtection };
}
