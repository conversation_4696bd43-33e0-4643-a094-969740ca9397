/**
 * Help Example Page
 * 
 * This page demonstrates the contextual help functionality.
 */

import React, { useState } from 'react';
import { 
  Contextual<PERSON>elp,
  HelpButton,
  HelpPanel,
  HelpTour,
  ThemeProvider,
  PreferencesProvider,
  OfflineProvider,
  I18nProvider,
  AccessibilityProvider,
  HelpProvider,
  DashboardCard,
  TabPanel,
  ResponsiveLayout,
  MetricsCard,
  ChartCard,
  StatusIndicator
} from '../nova-connect/ui/novavision-hub';

/**
 * Help Example Page
 * @returns {React.ReactNode} - The rendered component
 */
export default function HelpExamplePage() {
  // State
  const [activeTab, setActiveTab] = useState('dashboard');
  
  // Define tour steps
  const tourSteps = [
    {
      title: 'Welcome to Contextual Help',
      description: 'This tour will guide you through the contextual help features.',
      position: 'bottom',
      targetSelector: '#welcome-card'
    },
    {
      title: 'Help Buttons',
      description: 'Help buttons provide quick access to help content. Try clicking on them!',
      position: 'bottom',
      targetSelector: '#help-buttons-card'
    },
    {
      title: 'Contextual Help',
      description: 'Contextual help provides information about specific elements on the page.',
      position: 'right',
      targetSelector: '#contextual-help-card'
    },
    {
      title: 'Help Panel',
      description: 'The help panel provides detailed help content and search functionality.',
      position: 'left',
      targetSelector: '#help-panel-card'
    },
    {
      title: 'Tour Complete',
      description: 'You\'ve completed the contextual help tour! You can now explore the features on your own.',
      position: 'bottom',
      targetSelector: '#welcome-card'
    }
  ];
  
  return (
    <ThemeProvider>
      <PreferencesProvider>
        <OfflineProvider>
          <I18nProvider>
            <AccessibilityProvider>
              <HelpProvider>
                <div className="p-8">
                  <header className="mb-8">
                    <h1 className="text-3xl font-bold mb-4">NovaVision Hub Contextual Help</h1>
                    <p className="text-lg text-gray-600 mb-4">
                      This example demonstrates the contextual help functionality of NovaVision Hub.
                    </p>
                    
                    <div className="flex space-x-4">
                      <HelpButton
                        variant="text"
                        helpId="novavision-hub"
                        label="Help Center"
                      />
                      
                      <HelpButton
                        variant="fab"
                        helpId="novavision-hub"
                        size="large"
                        style={{ position: 'fixed', bottom: '24px', right: '24px' }}
                      />
                    </div>
                  </header>
                  
                  {/* Tab Panel */}
                  <TabPanel
                    tabs={[
                      { id: 'dashboard', label: 'Dashboard' },
                      { id: 'novaconnect', label: 'NovaConnect' },
                      { id: 'novacore', label: 'NovaCore' },
                      { id: 'novaproof', label: 'NovaProof' }
                    ]}
                    activeTab={activeTab}
                    onTabChange={setActiveTab}
                    className="mb-8"
                  />
                  
                  {/* Dashboard Content */}
                  {activeTab === 'dashboard' && (
                    <div className="mb-8">
                      <ResponsiveLayout
                        layouts={{
                          xs: 1,
                          sm: 2,
                          md: 2,
                          lg: 4,
                          xl: 4
                        }}
                        gap={4}
                      >
                        {/* Welcome Card */}
                        <DashboardCard
                          id="welcome-card"
                          title={
                            <ContextualHelp helpId="novavision-hub">
                              Welcome to NovaVision Hub
                            </ContextualHelp>
                          }
                          className="col-span-2"
                        >
                          <div className="p-4">
                            <p className="mb-4">
                              Welcome to the NovaVision Hub dashboard. This dashboard provides a comprehensive view of your NovaFuse components.
                            </p>
                            <p>
                              Use the contextual help features to learn how to use the dashboard and its features.
                            </p>
                          </div>
                        </DashboardCard>
                        
                        {/* Help Buttons Card */}
                        <DashboardCard
                          id="help-buttons-card"
                          title={
                            <ContextualHelp helpId="help-buttons">
                              Help Buttons
                            </ContextualHelp>
                          }
                        >
                          <div className="p-4 space-y-4">
                            <p>
                              Help buttons provide quick access to help content. Here are some examples:
                            </p>
                            
                            <div className="flex flex-col space-y-2">
                              <div className="flex items-center justify-between">
                                <span>Icon Button:</span>
                                <HelpButton
                                  variant="icon"
                                  helpId="help-buttons"
                                />
                              </div>
                              
                              <div className="flex items-center justify-between">
                                <span>Text Button:</span>
                                <HelpButton
                                  variant="text"
                                  helpId="help-buttons"
                                  label="Help"
                                />
                              </div>
                              
                              <div className="flex items-center justify-between">
                                <span>Small Button:</span>
                                <HelpButton
                                  variant="icon"
                                  helpId="help-buttons"
                                  size="small"
                                />
                              </div>
                              
                              <div className="flex items-center justify-between">
                                <span>Large Button:</span>
                                <HelpButton
                                  variant="icon"
                                  helpId="help-buttons"
                                  size="large"
                                />
                              </div>
                              
                              <div className="flex items-center justify-between">
                                <span>With Badge:</span>
                                <HelpButton
                                  variant="icon"
                                  helpId="help-buttons-badge"
                                  showBadge={true}
                                />
                              </div>
                            </div>
                          </div>
                        </DashboardCard>
                        
                        {/* Contextual Help Card */}
                        <DashboardCard
                          id="contextual-help-card"
                          title={
                            <ContextualHelp helpId="contextual-help">
                              Contextual Help
                            </ContextualHelp>
                          }
                        >
                          <div className="p-4 space-y-4">
                            <p>
                              Contextual help provides information about specific elements on the page. Here are some examples:
                            </p>
                            
                            <div className="flex flex-col space-y-2">
                              <div className="flex items-center justify-between">
                                <ContextualHelp
                                  helpId="contextual-help-text"
                                  position="right"
                                >
                                  <span>Text with Help:</span>
                                </ContextualHelp>
                              </div>
                              
                              <div className="flex items-center justify-between">
                                <span>Button with Help:</span>
                                <ContextualHelp
                                  helpId="contextual-help-button"
                                  position="left"
                                  showIcon={false}
                                >
                                  <button className="px-4 py-2 bg-blue-500 text-white rounded-md">
                                    Hover Me
                                  </button>
                                </ContextualHelp>
                              </div>
                              
                              <div className="flex items-center justify-between">
                                <span>Custom Help:</span>
                                <ContextualHelp
                                  helpId="contextual-help-custom"
                                  position="top"
                                >
                                  {({ isActive, toggleHelp }) => (
                                    <button
                                      className={`px-4 py-2 ${isActive ? 'bg-green-500' : 'bg-gray-200'} rounded-md`}
                                      onClick={toggleHelp}
                                    >
                                      {isActive ? 'Help Active' : 'Click Me'}
                                    </button>
                                  )}
                                </ContextualHelp>
                              </div>
                            </div>
                          </div>
                        </DashboardCard>
                        
                        {/* Help Panel Card */}
                        <DashboardCard
                          id="help-panel-card"
                          title={
                            <ContextualHelp helpId="help-panel">
                              Help Panel
                            </ContextualHelp>
                          }
                        >
                          <div className="p-4 space-y-4">
                            <p>
                              The help panel provides detailed help content and search functionality.
                            </p>
                            
                            <div className="flex flex-col space-y-2">
                              <button
                                className="px-4 py-2 bg-blue-500 text-white rounded-md"
                                onClick={() => {
                                  const helpButton = document.querySelector('.help-button--fab');
                                  if (helpButton) {
                                    helpButton.click();
                                  }
                                }}
                              >
                                Open Help Panel
                              </button>
                            </div>
                          </div>
                        </DashboardCard>
                      </ResponsiveLayout>
                    </div>
                  )}
                  
                  {/* NovaConnect Content */}
                  {activeTab === 'novaconnect' && (
                    <div className="mb-8">
                      <DashboardCard
                        title={
                          <ContextualHelp helpId="novaconnect">
                            NovaConnect
                          </ContextualHelp>
                        }
                      >
                        <div className="p-4">
                          <p className="mb-4">
                            NovaConnect is the integration component of the NovaFuse platform. It allows you to connect to various data sources and systems.
                          </p>
                          <p>
                            Use the contextual help features to learn how to use NovaConnect.
                          </p>
                        </div>
                      </DashboardCard>
                    </div>
                  )}
                  
                  {/* NovaCore Content */}
                  {activeTab === 'novacore' && (
                    <div className="mb-8">
                      <DashboardCard
                        title={
                          <ContextualHelp helpId="novacore">
                            NovaCore
                          </ContextualHelp>
                        }
                      >
                        <div className="p-4">
                          <p className="mb-4">
                            NovaCore is the central processing component of the NovaFuse platform. It provides the core functionality for data processing, decision making, and policy enforcement.
                          </p>
                          <p>
                            Use the contextual help features to learn how to use NovaCore.
                          </p>
                        </div>
                      </DashboardCard>
                    </div>
                  )}
                  
                  {/* NovaProof Content */}
                  {activeTab === 'novaproof' && (
                    <div className="mb-8">
                      <DashboardCard
                        title={
                          <ContextualHelp helpId="novaproof">
                            NovaProof
                          </ContextualHelp>
                        }
                      >
                        <div className="p-4">
                          <p className="mb-4">
                            NovaProof is the verification and validation component of the NovaFuse platform. It provides tools for verifying data integrity, validating compliance, and generating audit trails.
                          </p>
                          <p>
                            Use the contextual help features to learn how to use NovaProof.
                          </p>
                        </div>
                      </DashboardCard>
                    </div>
                  )}
                  
                  {/* Help Panel */}
                  <HelpPanel position="right" />
                  
                  {/* Help Tour */}
                  <HelpTour
                    tourId="contextual-help-tour"
                    title="Contextual Help Tour"
                    description="Learn how to use the contextual help features."
                    steps={tourSteps}
                    autoStart={false}
                  />
                </div>
              </HelpProvider>
            </AccessibilityProvider>
          </I18nProvider>
        </OfflineProvider>
      </PreferencesProvider>
    </ThemeProvider>
  );
}
