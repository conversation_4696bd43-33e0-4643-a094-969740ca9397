<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>NovaFuse Performance Dashboard</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: #f8f9fa;
      padding-top: 20px;
    }
    .card {
      border-radius: 10px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      margin-bottom: 20px;
    }
    .card-header {
      background-color: #6c5ce7;
      color: white;
      font-weight: bold;
      border-radius: 10px 10px 0 0 !important;
    }
    .metric-value {
      font-size: 2rem;
      font-weight: bold;
      color: #2d3436;
    }
    .metric-label {
      font-size: 0.9rem;
      color: #636e72;
    }
    .chart-container {
      position: relative;
      height: 250px;
      width: 100%;
    }
    .navbar-brand {
      font-weight: bold;
      color: #6c5ce7 !important;
    }
    .refresh-btn {
      background-color: #6c5ce7;
      border-color: #6c5ce7;
    }
    .refresh-btn:hover {
      background-color: #5549d6;
      border-color: #5549d6;
    }
  </style>
</head>
<body>
  <div class="container">
    <nav class="navbar navbar-expand-lg navbar-light bg-light mb-4">
      <div class="container-fluid">
        <a class="navbar-brand" href="#">NovaFuse Performance Dashboard</a>
        <button class="btn btn-primary refresh-btn" id="refreshBtn">
          <i class="bi bi-arrow-clockwise"></i> Refresh
        </button>
      </div>
    </nav>
    
    <div class="row mb-4">
      <div class="col-md-3">
        <div class="card">
          <div class="card-header">CPU Usage</div>
          <div class="card-body text-center">
            <div class="metric-value" id="cpuValue">0%</div>
            <div class="metric-label">Current CPU Usage</div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card">
          <div class="card-header">Memory Usage</div>
          <div class="card-body text-center">
            <div class="metric-value" id="memoryValue">0%</div>
            <div class="metric-label">Current Memory Usage</div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card">
          <div class="card-header">Request Rate</div>
          <div class="card-body text-center">
            <div class="metric-value" id="requestRateValue">0/s</div>
            <div class="metric-label">Requests per Second</div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card">
          <div class="card-header">Response Time</div>
          <div class="card-body text-center">
            <div class="metric-value" id="responseTimeValue">0ms</div>
            <div class="metric-label">Average Response Time</div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="row mb-4">
      <div class="col-md-6">
        <div class="card">
          <div class="card-header">CPU & Memory Usage</div>
          <div class="card-body">
            <div class="chart-container">
              <canvas id="resourceChart"></canvas>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="card">
          <div class="card-header">Request Metrics</div>
          <div class="card-body">
            <div class="chart-container">
              <canvas id="requestChart"></canvas>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="row mb-4">
      <div class="col-md-6">
        <div class="card">
          <div class="card-header">Connector Executions</div>
          <div class="card-body">
            <div class="chart-container">
              <canvas id="connectorChart"></canvas>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="card">
          <div class="card-header">Cache Performance</div>
          <div class="card-body">
            <div class="chart-container">
              <canvas id="cacheChart"></canvas>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="row">
      <div class="col-md-12">
        <div class="card">
          <div class="card-header">System Information</div>
          <div class="card-body">
            <table class="table">
              <tbody>
                <tr>
                  <th>Uptime</th>
                  <td id="uptimeValue">0s</td>
                </tr>
                <tr>
                  <th>Node.js Version</th>
                  <td id="nodeVersionValue">-</td>
                </tr>
                <tr>
                  <th>Event Loop Lag</th>
                  <td id="eventLoopValue">0ms</td>
                </tr>
                <tr>
                  <th>Active Connections</th>
                  <td id="connectionsValue">0</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <script>
    // Chart configurations
    const resourceChartConfig = {
      type: 'line',
      data: {
        labels: Array(20).fill(''),
        datasets: [
          {
            label: 'CPU Usage (%)',
            data: Array(20).fill(0),
            borderColor: '#6c5ce7',
            backgroundColor: 'rgba(108, 92, 231, 0.1)',
            fill: true,
            tension: 0.4
          },
          {
            label: 'Memory Usage (%)',
            data: Array(20).fill(0),
            borderColor: '#e17055',
            backgroundColor: 'rgba(225, 112, 85, 0.1)',
            fill: true,
            tension: 0.4
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true,
            max: 100
          }
        }
      }
    };
    
    const requestChartConfig = {
      type: 'line',
      data: {
        labels: Array(20).fill(''),
        datasets: [
          {
            label: 'Requests/s',
            data: Array(20).fill(0),
            borderColor: '#00b894',
            backgroundColor: 'rgba(0, 184, 148, 0.1)',
            fill: true,
            tension: 0.4
          },
          {
            label: 'Response Time (ms)',
            data: Array(20).fill(0),
            borderColor: '#fdcb6e',
            backgroundColor: 'rgba(253, 203, 110, 0.1)',
            fill: true,
            tension: 0.4,
            yAxisID: 'y1'
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true
          },
          y1: {
            beginAtZero: true,
            position: 'right',
            grid: {
              drawOnChartArea: false
            }
          }
        }
      }
    };
    
    const connectorChartConfig = {
      type: 'bar',
      data: {
        labels: ['Total', 'Success', 'Error'],
        datasets: [
          {
            label: 'Connector Executions',
            data: [0, 0, 0],
            backgroundColor: [
              'rgba(108, 92, 231, 0.7)',
              'rgba(0, 184, 148, 0.7)',
              'rgba(225, 112, 85, 0.7)'
            ]
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true
          }
        }
      }
    };
    
    const cacheChartConfig = {
      type: 'doughnut',
      data: {
        labels: ['Hits', 'Misses'],
        datasets: [
          {
            data: [0, 0],
            backgroundColor: [
              'rgba(0, 184, 148, 0.7)',
              'rgba(225, 112, 85, 0.7)'
            ]
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false
      }
    };
    
    // Initialize charts
    const resourceChart = new Chart(
      document.getElementById('resourceChart'),
      resourceChartConfig
    );
    
    const requestChart = new Chart(
      document.getElementById('requestChart'),
      requestChartConfig
    );
    
    const connectorChart = new Chart(
      document.getElementById('connectorChart'),
      connectorChartConfig
    );
    
    const cacheChart = new Chart(
      document.getElementById('cacheChart'),
      cacheChartConfig
    );
    
    // Format time function
    function formatTime(ms) {
      const seconds = Math.floor(ms / 1000);
      const minutes = Math.floor(seconds / 60);
      const hours = Math.floor(minutes / 60);
      const days = Math.floor(hours / 24);
      
      if (days > 0) {
        return `${days}d ${hours % 24}h ${minutes % 60}m`;
      } else if (hours > 0) {
        return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
      } else if (minutes > 0) {
        return `${minutes}m ${seconds % 60}s`;
      } else {
        return `${seconds}s`;
      }
    }
    
    // Update metrics function
    async function updateMetrics() {
      try {
        const response = await fetch('/api/metrics');
        const data = await response.json();
        
        // Update metric values
        document.getElementById('cpuValue').textContent = `${data.cpu.current.toFixed(1)}%`;
        document.getElementById('memoryValue').textContent = `${data.memory.current.toFixed(1)}%`;
        document.getElementById('requestRateValue').textContent = `${data.requestRate.toFixed(1)}/s`;
        document.getElementById('responseTimeValue').textContent = `${data.responseTime.average.toFixed(0)}ms`;
        document.getElementById('uptimeValue').textContent = formatTime(data.uptime);
        document.getElementById('nodeVersionValue').textContent = data.nodeVersion;
        document.getElementById('eventLoopValue').textContent = `${data.eventLoop.current.toFixed(2)}ms`;
        document.getElementById('connectionsValue').textContent = data.activeConnections;
        
        // Update resource chart
        resourceChart.data.datasets[0].data.shift();
        resourceChart.data.datasets[0].data.push(data.cpu.current);
        resourceChart.data.datasets[1].data.shift();
        resourceChart.data.datasets[1].data.push(data.memory.current);
        resourceChart.update();
        
        // Update request chart
        requestChart.data.datasets[0].data.shift();
        requestChart.data.datasets[0].data.push(data.requestRate);
        requestChart.data.datasets[1].data.shift();
        requestChart.data.datasets[1].data.push(data.responseTime.average);
        requestChart.update();
        
        // Update connector chart
        connectorChart.data.datasets[0].data = [
          data.connectors.executions,
          data.connectors.success,
          data.connectors.error
        ];
        connectorChart.update();
        
        // Update cache chart
        cacheChart.data.datasets[0].data = [
          data.cache.hits,
          data.cache.misses
        ];
        cacheChart.update();
      } catch (error) {
        console.error('Error fetching metrics:', error);
      }
    }
    
    // Initial update
    updateMetrics();
    
    // Set up refresh interval
    let refreshInterval = setInterval(updateMetrics, 5000);
    
    // Refresh button handler
    document.getElementById('refreshBtn').addEventListener('click', () => {
      updateMetrics();
    });
  </script>
</body>
</html>
