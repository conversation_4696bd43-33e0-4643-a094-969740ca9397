/**
 * NovaConnect Adapter for Comphyology
 * 
 * This module provides an adapter to integrate Comphyology visualizations with
 * NovaFuse components using NovaConnect.
 */

const EventEmitter = require('events');

/**
 * NovaConnect Adapter
 */
class NovaConnectAdapter extends EventEmitter {
  /**
   * Constructor
   * 
   * @param {Object} options - Adapter options
   * @param {Object} options.novaConnect - NovaConnect instance
   * @param {boolean} options.enableLogging - Whether to enable logging
   * @param {string[]} options.subscribeTopics - Topics to subscribe to
   * @param {Object} options.transformers - Data transformers for each topic
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      enableLogging: options.enableLogging || false,
      subscribeTopics: options.subscribeTopics || [
        'novaShield.threatDetected',
        'novaShield.threatAnalyzed',
        'novaTrack.complianceChanged',
        'novaTrack.regulationUpdated',
        'novaCore.decisionMade',
        'novaCore.policyApplied'
      ],
      transformers: options.transformers || {},
      ...options
    };
    
    this.novaConnect = options.novaConnect;
    
    if (!this.novaConnect) {
      throw new Error('NovaConnect instance is required');
    }
    
    // Initialize data buffers
    this.dataBuffers = {
      novaShield: [],
      novaTrack: [],
      novaCore: []
    };
    
    // Initialize default transformers if not provided
    if (!this.options.transformers.novaShield) {
      this.options.transformers.novaShield = this._defaultNovaShieldTransformer.bind(this);
    }
    
    if (!this.options.transformers.novaTrack) {
      this.options.transformers.novaTrack = this._defaultNovaTrackTransformer.bind(this);
    }
    
    if (!this.options.transformers.novaCore) {
      this.options.transformers.novaCore = this._defaultNovaCoreTransformer.bind(this);
    }
    
    if (this.options.enableLogging) {
      console.log('NovaConnectAdapter initialized with options:', this.options);
    }
  }
  
  /**
   * Connect to NovaConnect and subscribe to topics
   * 
   * @returns {Promise} - Promise that resolves when connected
   */
  async connect() {
    if (this.options.enableLogging) {
      console.log('Connecting to NovaConnect...');
    }
    
    try {
      // Subscribe to topics
      for (const topic of this.options.subscribeTopics) {
        await this.novaConnect.subscribe(topic, this._handleMessage.bind(this));
        
        if (this.options.enableLogging) {
          console.log(`Subscribed to topic: ${topic}`);
        }
      }
      
      this.emit('connected');
      
      if (this.options.enableLogging) {
        console.log('Connected to NovaConnect');
      }
      
      return Promise.resolve();
    } catch (error) {
      if (this.options.enableLogging) {
        console.error('Failed to connect to NovaConnect:', error);
      }
      
      this.emit('error', error);
      return Promise.reject(error);
    }
  }
  
  /**
   * Disconnect from NovaConnect
   * 
   * @returns {Promise} - Promise that resolves when disconnected
   */
  async disconnect() {
    if (this.options.enableLogging) {
      console.log('Disconnecting from NovaConnect...');
    }
    
    try {
      // Unsubscribe from topics
      for (const topic of this.options.subscribeTopics) {
        await this.novaConnect.unsubscribe(topic);
        
        if (this.options.enableLogging) {
          console.log(`Unsubscribed from topic: ${topic}`);
        }
      }
      
      this.emit('disconnected');
      
      if (this.options.enableLogging) {
        console.log('Disconnected from NovaConnect');
      }
      
      return Promise.resolve();
    } catch (error) {
      if (this.options.enableLogging) {
        console.error('Failed to disconnect from NovaConnect:', error);
      }
      
      this.emit('error', error);
      return Promise.reject(error);
    }
  }
  
  /**
   * Handle message from NovaConnect
   * 
   * @param {Object} message - Message from NovaConnect
   * @param {string} topic - Topic of the message
   * @private
   */
  _handleMessage(message, topic) {
    if (this.options.enableLogging) {
      console.log(`Received message from topic: ${topic}`);
    }
    
    try {
      // Determine the source component from the topic
      const source = topic.split('.')[0];
      
      // Transform the data based on the source
      let transformedData = null;
      
      switch (source) {
        case 'novaShield':
          transformedData = this.options.transformers.novaShield(message, topic);
          this._addToBuffer('novaShield', message);
          break;
        case 'novaTrack':
          transformedData = this.options.transformers.novaTrack(message, topic);
          this._addToBuffer('novaTrack', message);
          break;
        case 'novaCore':
          transformedData = this.options.transformers.novaCore(message, topic);
          this._addToBuffer('novaCore', message);
          break;
      }
      
      if (transformedData) {
        // Emit specific event for the source
        this.emit(`${source}:data`, transformedData);
        
        // Emit generic data event
        this.emit('data', {
          source,
          topic,
          raw: message,
          transformed: transformedData
        });
      }
    } catch (error) {
      if (this.options.enableLogging) {
        console.error('Error handling message:', error);
      }
      
      this.emit('error', error);
    }
  }
  
  /**
   * Add data to buffer
   * 
   * @param {string} source - Source of the data
   * @param {Object} data - Data to add
   * @private
   */
  _addToBuffer(source, data) {
    if (!this.dataBuffers[source]) {
      this.dataBuffers[source] = [];
    }
    
    this.dataBuffers[source].push({
      timestamp: new Date(),
      data
    });
    
    // Limit buffer size to 100 items
    if (this.dataBuffers[source].length > 100) {
      this.dataBuffers[source].shift();
    }
  }
  
  /**
   * Get latest data from buffer
   * 
   * @param {string} source - Source to get data from
   * @returns {Object} - Latest data
   */
  getLatestData(source) {
    if (!this.dataBuffers[source] || this.dataBuffers[source].length === 0) {
      return null;
    }
    
    return this.dataBuffers[source][this.dataBuffers[source].length - 1].data;
  }
  
  /**
   * Get all data from buffer
   * 
   * @param {string} source - Source to get data from
   * @returns {Array} - All data
   */
  getAllData(source) {
    if (!this.dataBuffers[source]) {
      return [];
    }
    
    return this.dataBuffers[source];
  }
  
  /**
   * Default transformer for NovaShield data
   * 
   * @param {Object} data - Data from NovaShield
   * @param {string} topic - Topic of the message
   * @returns {Object} - Transformed data
   * @private
   */
  _defaultNovaShieldTransformer(data, topic) {
    if (!data) {
      return null;
    }
    
    // Extract relevant data for Quantum Phase Space Map
    const transformedData = {
      type: 'quantum_phase_space_map',
      timestamp: new Date(),
      source: 'novaShield',
      topic,
      data: {
        // Extract entropy values from threat data
        entropyValues: data.threats ? data.threats.map(threat => ({
          x: threat.entropy || Math.random(),
          y: threat.phase || Math.random() * Math.PI * 2,
          value: threat.certainty || Math.random(),
          label: threat.type || 'Unknown'
        })) : [],
        
        // Extract phase values from threat data
        phaseValues: data.threats ? data.threats.map(threat => ({
          x: threat.entropy || Math.random(),
          y: threat.phase || Math.random() * Math.PI * 2,
          direction: threat.direction || Math.random() * Math.PI * 2,
          magnitude: threat.magnitude || Math.random(),
          label: threat.type || 'Unknown'
        })) : [],
        
        // Overall metrics
        metrics: {
          averageEntropy: data.averageEntropy || 0.5,
          averagePhase: data.averagePhase || Math.PI,
          averageCertainty: data.averageCertainty || 0.7,
          threatCount: data.threats ? data.threats.length : 0
        }
      }
    };
    
    return transformedData;
  }
  
  /**
   * Default transformer for NovaTrack data
   * 
   * @param {Object} data - Data from NovaTrack
   * @param {string} topic - Topic of the message
   * @returns {Object} - Transformed data
   * @private
   */
  _defaultNovaTrackTransformer(data, topic) {
    if (!data) {
      return null;
    }
    
    // Extract relevant data for Morphological Resonance Field
    const transformedData = {
      type: 'morphological_resonance_field',
      timestamp: new Date(),
      source: 'novaTrack',
      topic,
      data: {
        // Extract complexity values from compliance data
        complexityValues: data.regulations ? data.regulations.map(regulation => ({
          x: regulation.complexity || Math.random(),
          y: regulation.adaptability || Math.random(),
          value: regulation.resonance || Math.random(),
          label: regulation.name || 'Unknown'
        })) : [],
        
        // Overall metrics
        metrics: {
          averageComplexity: data.averageComplexity || 0.5,
          averageAdaptability: data.averageAdaptability || 0.5,
          averageResonance: data.averageResonance || 0.7,
          regulationCount: data.regulations ? data.regulations.length : 0
        }
      }
    };
    
    return transformedData;
  }
  
  /**
   * Default transformer for NovaCore data
   * 
   * @param {Object} data - Data from NovaCore
   * @param {string} topic - Topic of the message
   * @returns {Object} - Transformed data
   * @private
   */
  _defaultNovaCoreTransformer(data, topic) {
    if (!data) {
      return null;
    }
    
    // Extract relevant data for Ethical Tensor Projection
    const transformedData = {
      type: 'ethical_tensor_projection',
      timestamp: new Date(),
      source: 'novaCore',
      topic,
      data: {
        // Extract fairness values from decision data
        fairnessValues: data.decisions ? data.decisions.map(decision => ({
          x: decision.fairness || Math.random(),
          y: decision.transparency || Math.random(),
          value: decision.ethicalTensor || Math.random(),
          label: decision.type || 'Unknown'
        })) : [],
        
        // Overall metrics
        metrics: {
          averageFairness: data.averageFairness || 0.5,
          averageTransparency: data.averageTransparency || 0.5,
          averageEthicalTensor: data.averageEthicalTensor || 0.7,
          decisionCount: data.decisions ? data.decisions.length : 0
        }
      }
    };
    
    return transformedData;
  }
}

module.exports = NovaConnectAdapter;
