export default function handler(req, res) {
    const affiliates = {
        1: {
            id: 1,
            name: 'Conscious Affiliate',
            commissionRate: 0.15,
            status: 'active',
            stats: {
                totalSales: 150,
                totalRevenue: 25000,
                conversionRate: 0.08,
                roi: 2.5
            }
        }
    };

    if (req.method === 'GET') {
        const { affiliateId } = req.query;
        if (affiliateId) {
            const affiliate = affiliates[affiliateId];
            if (affiliate) {
                res.status(200).json(affiliate);
            } else {
                res.status(404).json({ error: 'Affiliate not found' });
            }
        } else {
            res.status(200).json(Object.values(affiliates));
        }
    } else if (req.method === 'POST') {
        const newAffiliate = req.body;
        const newId = Object.keys(affiliates).length + 1;
        affiliates[newId] = {
            id: newId,
            ...newAffiliate,
            stats: {
                totalSales: 0,
                totalRevenue: 0,
                conversionRate: 0,
                roi: 0
            }
        };
        res.status(201).json(affiliates[newId]);
    } else {
        res.status(405).json({ error: 'Method not allowed' });
    }
}
