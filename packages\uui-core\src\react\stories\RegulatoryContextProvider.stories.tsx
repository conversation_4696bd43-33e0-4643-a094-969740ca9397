import React from 'react';
import { ComponentStory, ComponentMeta } from '@storybook/react';
import RegulatoryContextProvider, { useRegulatoryContext } from '../RegulatoryContextProvider';

export default {
  title: 'UUIC/RegulatoryContextProvider',
  component: RegulatoryContextProvider,
  argTypes: {
    jurisdiction: {
      control: {
        type: 'select',
        options: ['eu', 'us-healthcare', 'us-finance', 'us-general', 'global']
      }
    },
    userRole: {
      control: {
        type: 'select',
        options: ['admin', 'compliance-officer', 'data-processor', 'auditor', 'user']
      }
    }
  }
} as ComponentMeta<typeof RegulatoryContextProvider>;

// Create a template
const Template: ComponentStory<typeof RegulatoryContextProvider> = (args) => (
  <RegulatoryContextProvider {...args}>
    <RegulationDisplay />
  </RegulatoryContextProvider>
);

// Component to display regulatory context
const RegulationDisplay = () => {
  const { activeRegulations, jurisdiction, userRole, loading, error } = useRegulatoryContext();
  
  if (loading) {
    return <div>Loading regulations...</div>;
  }
  
  if (error) {
    return <div>Error: {error}</div>;
  }
  
  return (
    <div style={{ fontFamily: 'Arial, sans-serif', padding: '20px' }}>
      <h2>Regulatory Context</h2>
      <div style={{ marginBottom: '20px' }}>
        <strong>Jurisdiction:</strong> {jurisdiction}
      </div>
      <div style={{ marginBottom: '20px' }}>
        <strong>User Role:</strong> {userRole}
      </div>
      <div>
        <h3>Active Regulations</h3>
        {activeRegulations.length === 0 ? (
          <p>No active regulations</p>
        ) : (
          <ul>
            {activeRegulations.map((regulation) => (
              <li key={regulation} style={{ marginBottom: '10px' }}>
                <strong>{regulation}</strong>
                {regulation === 'GDPR' && (
                  <div style={{ marginTop: '5px', fontSize: '0.9em', color: '#666' }}>
                    General Data Protection Regulation - EU data protection and privacy
                  </div>
                )}
                {regulation === 'HIPAA' && (
                  <div style={{ marginTop: '5px', fontSize: '0.9em', color: '#666' }}>
                    Health Insurance Portability and Accountability Act - US healthcare data privacy
                  </div>
                )}
                {regulation === 'PCI_DSS' && (
                  <div style={{ marginTop: '5px', fontSize: '0.9em', color: '#666' }}>
                    Payment Card Industry Data Security Standard - Card data security
                  </div>
                )}
                {regulation === 'SOX' && (
                  <div style={{ marginTop: '5px', fontSize: '0.9em', color: '#666' }}>
                    Sarbanes-Oxley Act - US financial reporting and corporate governance
                  </div>
                )}
                {regulation === 'CCPA' && (
                  <div style={{ marginTop: '5px', fontSize: '0.9em', color: '#666' }}>
                    California Consumer Privacy Act - California data privacy
                  </div>
                )}
                {regulation === 'ISO27001' && (
                  <div style={{ marginTop: '5px', fontSize: '0.9em', color: '#666' }}>
                    ISO 27001 - Information security management
                  </div>
                )}
              </li>
            ))}
          </ul>
        )}
      </div>
    </div>
  );
};

// Create stories
export const EU = Template.bind({});
EU.args = {
  jurisdiction: 'eu',
  userRole: 'compliance-officer'
};

export const USHealthcare = Template.bind({});
USHealthcare.args = {
  jurisdiction: 'us-healthcare',
  userRole: 'data-processor'
};

export const USFinance = Template.bind({});
USFinance.args = {
  jurisdiction: 'us-finance',
  userRole: 'auditor'
};

export const USGeneral = Template.bind({});
USGeneral.args = {
  jurisdiction: 'us-general',
  userRole: 'user'
};

export const Global = Template.bind({});
Global.args = {
  jurisdiction: 'global',
  userRole: 'admin'
};
