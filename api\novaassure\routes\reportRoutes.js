/**
 * Report Routes
 * 
 * This file defines the routes for report generation.
 */

const express = require('express');
const router = express.Router();
const reportController = require('../controllers/reportController');
const authMiddleware = require('../middleware/authMiddleware');

/**
 * @swagger
 * /api/v1/novaassure/reports:
 *   get:
 *     summary: Get all reports
 *     description: Retrieve a list of all reports
 *     tags: [Reports]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [compliance, test-results, evidence, control-effectiveness]
 *         description: Filter by report type
 *       - in: query
 *         name: framework
 *         schema:
 *           type: string
 *         description: Filter by framework (e.g., soc2, gdpr, hipaa)
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Items per page
 *     responses:
 *       200:
 *         description: List of reports
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/', authMiddleware.authenticate, reportController.getAllReports);

/**
 * @swagger
 * /api/v1/novaassure/reports/{id}:
 *   get:
 *     summary: Get report by ID
 *     description: Retrieve a report by its ID
 *     tags: [Reports]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Report ID
 *     responses:
 *       200:
 *         description: Report details
 *       404:
 *         description: Report not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/:id', authMiddleware.authenticate, reportController.getReportById);

/**
 * @swagger
 * /api/v1/novaassure/reports/generate/compliance:
 *   post:
 *     summary: Generate compliance report
 *     description: Generate a compliance report
 *     tags: [Reports]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - framework
 *             properties:
 *               name:
 *                 type: string
 *                 description: Report name
 *               description:
 *                 type: string
 *                 description: Report description
 *               framework:
 *                 type: string
 *                 description: Framework (e.g., soc2, gdpr, hipaa)
 *               startDate:
 *                 type: string
 *                 format: date
 *                 description: Start date for report period
 *               endDate:
 *                 type: string
 *                 format: date
 *                 description: End date for report period
 *               format:
 *                 type: string
 *                 enum: [pdf, html, json, csv]
 *                 default: pdf
 *                 description: Report format
 *     responses:
 *       201:
 *         description: Report generated
 *       400:
 *         description: Invalid request
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post('/generate/compliance', authMiddleware.authenticate, reportController.generateComplianceReport);

/**
 * @swagger
 * /api/v1/novaassure/reports/generate/test-results:
 *   post:
 *     summary: Generate test results report
 *     description: Generate a test results report
 *     tags: [Reports]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - testPlanId
 *             properties:
 *               name:
 *                 type: string
 *                 description: Report name
 *               description:
 *                 type: string
 *                 description: Report description
 *               testPlanId:
 *                 type: string
 *                 description: Test plan ID
 *               testExecutionId:
 *                 type: string
 *                 description: Test execution ID
 *               startDate:
 *                 type: string
 *                 format: date
 *                 description: Start date for report period
 *               endDate:
 *                 type: string
 *                 format: date
 *                 description: End date for report period
 *               format:
 *                 type: string
 *                 enum: [pdf, html, json, csv]
 *                 default: pdf
 *                 description: Report format
 *     responses:
 *       201:
 *         description: Report generated
 *       400:
 *         description: Invalid request
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post('/generate/test-results', authMiddleware.authenticate, reportController.generateTestResultsReport);

/**
 * @swagger
 * /api/v1/novaassure/reports/generate/evidence:
 *   post:
 *     summary: Generate evidence report
 *     description: Generate an evidence report
 *     tags: [Reports]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               name:
 *                 type: string
 *                 description: Report name
 *               description:
 *                 type: string
 *                 description: Report description
 *               controlId:
 *                 type: string
 *                 description: Control ID
 *               testExecutionId:
 *                 type: string
 *                 description: Test execution ID
 *               startDate:
 *                 type: string
 *                 format: date
 *                 description: Start date for report period
 *               endDate:
 *                 type: string
 *                 format: date
 *                 description: End date for report period
 *               format:
 *                 type: string
 *                 enum: [pdf, html, json, csv]
 *                 default: pdf
 *                 description: Report format
 *     responses:
 *       201:
 *         description: Report generated
 *       400:
 *         description: Invalid request
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post('/generate/evidence', authMiddleware.authenticate, reportController.generateEvidenceReport);

/**
 * @swagger
 * /api/v1/novaassure/reports/generate/control-effectiveness:
 *   post:
 *     summary: Generate control effectiveness report
 *     description: Generate a control effectiveness report
 *     tags: [Reports]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - framework
 *             properties:
 *               name:
 *                 type: string
 *                 description: Report name
 *               description:
 *                 type: string
 *                 description: Report description
 *               framework:
 *                 type: string
 *                 description: Framework (e.g., soc2, gdpr, hipaa)
 *               startDate:
 *                 type: string
 *                 format: date
 *                 description: Start date for report period
 *               endDate:
 *                 type: string
 *                 format: date
 *                 description: End date for report period
 *               format:
 *                 type: string
 *                 enum: [pdf, html, json, csv]
 *                 default: pdf
 *                 description: Report format
 *     responses:
 *       201:
 *         description: Report generated
 *       400:
 *         description: Invalid request
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post('/generate/control-effectiveness', authMiddleware.authenticate, reportController.generateControlEffectivenessReport);

/**
 * @swagger
 * /api/v1/novaassure/reports/{id}/download:
 *   get:
 *     summary: Download report
 *     description: Download a report file
 *     tags: [Reports]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Report ID
 *     responses:
 *       200:
 *         description: Report file
 *         content:
 *           application/octet-stream:
 *             schema:
 *               type: string
 *               format: binary
 *       404:
 *         description: Report not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/:id/download', authMiddleware.authenticate, reportController.downloadReport);

/**
 * @swagger
 * /api/v1/novaassure/reports/{id}/share:
 *   post:
 *     summary: Share report
 *     description: Share a report with users or external recipients
 *     tags: [Reports]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Report ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - recipients
 *             properties:
 *               recipients:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Email addresses or user IDs
 *               message:
 *                 type: string
 *                 description: Message to include
 *               expirationDate:
 *                 type: string
 *                 format: date
 *                 description: Link expiration date
 *     responses:
 *       200:
 *         description: Report shared
 *       404:
 *         description: Report not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post('/:id/share', authMiddleware.authenticate, reportController.shareReport);

/**
 * @swagger
 * /api/v1/novaassure/reports/{id}/schedule:
 *   post:
 *     summary: Schedule report
 *     description: Schedule a report for automatic generation
 *     tags: [Reports]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Report ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - schedule
 *             properties:
 *               schedule:
 *                 type: object
 *                 properties:
 *                   frequency:
 *                     type: string
 *                     enum: [daily, weekly, monthly, quarterly, annually]
 *                     description: Report frequency
 *                   startDate:
 *                     type: string
 *                     format: date
 *                     description: Start date
 *                   endDate:
 *                     type: string
 *                     format: date
 *                     description: End date
 *               recipients:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Email addresses or user IDs
 *     responses:
 *       200:
 *         description: Report scheduled
 *       404:
 *         description: Report not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post('/:id/schedule', authMiddleware.authenticate, reportController.scheduleReport);

module.exports = router;
