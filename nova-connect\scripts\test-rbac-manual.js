/**
 * Manual RBAC Test Script
 *
 * This script tests the RBAC system without using Jest.
 */

const mongoose = require('mongoose');
const RBACService = require('../api/services/RBACService');
const Role = require('../api/models/Role');
const Permission = require('../api/models/Permission');
const UserRole = require('../api/models/UserRole');
const User = require('../api/models/User');
const logger = require('../config/logger');

// MongoDB connection string
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/novaconnect-test';

// Connect to MongoDB
async function setupDatabase() {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');

    // Clear existing data to avoid duplicate key errors
    await Permission.deleteMany({});
    await Role.deleteMany({});
    await UserRole.deleteMany({});
    await User.deleteMany({ email: '<EMAIL>' });

    console.log('Cleared existing test data');

    return true;
  } catch (err) {
    console.error('Failed to connect to MongoDB', err);
    return false;
  }
}

// Start tests
setupDatabase()
  .then(success => {
    if (success) {
      runTests();
    } else {
      process.exit(1);
    }
  });

// Run tests
async function runTests() {
  try {
    console.log('Starting RBAC tests...');

    // Create RBAC service
    const rbacService = new RBACService();

    // Initialize database with default roles and permissions
    await rbacService.initializeDatabase();
    console.log('Initialized database with default roles and permissions');

    // Test 1: Get all roles
    const roles = await rbacService.getAllRoles();
    console.log(`Test 1: Get all roles - Found ${roles.length} roles`);
    console.log('Role names:', roles.map(r => r.name));

    // Test 2: Get all permissions
    const permissions = await rbacService.getAllPermissions();
    console.log(`Test 2: Get all permissions - Found ${permissions.length} permissions`);

    // Test 3: Create a test user
    let testUser = await User.findOne({ email: '<EMAIL>' });

    if (!testUser) {
      testUser = new User({
        username: 'testuser',
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Test',
        lastName: 'User',
        status: 'active'
      });

      await testUser.save();
    }

    console.log(`Test 3: Create test user - Created user with ID ${testUser._id}`);

    // Test 4: Create a custom role
    const customRoleName = 'Test Role ' + Date.now();
    let customRole;

    try {
      // Find permission IDs instead of using string identifiers
      const viewPermission = await Permission.findOne({ resource: 'connector', action: 'view' });
      const usePermission = await Permission.findOne({ resource: 'connector', action: 'use' });

      if (!viewPermission || !usePermission) {
        console.error('Test 4: Could not find required permissions');
        console.log('Available permissions:');
        const allPerms = await Permission.find({});
        allPerms.forEach(p => console.log(`- ${p.resource}:${p.action} (${p._id})`));
      } else {
        customRole = await rbacService.createRole({
          name: customRoleName,
          description: 'Test role description',
          permissions: [viewPermission._id, usePermission._id]
        });

        console.log(`Test 4: Create custom role - Created role with ID ${customRole._id}`);
      }
    } catch (error) {
      console.error('Test 4 failed:', error.message);
      console.error('Error details:', error);
    }

    // Test 5: Assign role to user
    if (customRole) {
      try {
        const result = await rbacService.assignRoleToUser(testUser._id, customRole._id);
        console.log(`Test 5: Assign role to user - ${result.message}`);
      } catch (error) {
        console.error('Test 5 failed:', error.message);
      }
    }

    // Test 6: Get user roles
    try {
      const userRoles = await rbacService.getUserRoles(testUser._id);
      console.log(`Test 6: Get user roles - User has ${userRoles.length} roles`);
      console.log('Role names:', userRoles.map(r => r.name));
    } catch (error) {
      console.error('Test 6 failed:', error.message);
    }

    // Test 7: Check if user has permission
    try {
      const hasViewPermission = await rbacService.hasPermission(testUser._id, 'connector:view');
      const hasEditPermission = await rbacService.hasPermission(testUser._id, 'connector:edit');

      console.log(`Test 7: Check if user has permission - connector:view: ${hasViewPermission}`);
      console.log(`Test 7: Check if user has permission - connector:edit: ${hasEditPermission}`);
    } catch (error) {
      console.error('Test 7 failed:', error.message);
    }

    // Test 8: Get user permissions
    try {
      const userPermissions = await rbacService.getUserPermissions(testUser._id);
      console.log(`Test 8: Get user permissions - User has ${userPermissions.length} permissions`);
      console.log('Permissions:', userPermissions);
    } catch (error) {
      console.error('Test 8 failed:', error.message);
    }

    // Test 9: Remove role from user
    if (customRole) {
      try {
        const result = await rbacService.removeRoleFromUser(testUser._id, customRole._id);
        console.log(`Test 9: Remove role from user - ${result.message}`);
      } catch (error) {
        console.error('Test 9 failed:', error.message);
      }
    }

    // Test 10: Delete custom role
    if (customRole) {
      try {
        const result = await rbacService.deleteRole(customRole._id);
        console.log(`Test 10: Delete custom role - ${result.message}`);
      } catch (error) {
        console.error('Test 10 failed:', error.message);
      }
    }

    console.log('RBAC tests completed successfully');
  } catch (error) {
    console.error('Test failed:', error);
  } finally {
    // Disconnect from MongoDB
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
    process.exit(0);
  }
}
