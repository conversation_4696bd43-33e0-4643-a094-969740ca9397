/**
 * Error Handling Tests
 * 
 * This file contains tests for the error handling infrastructure.
 */

const request = require('supertest');
const app = require('../../server');
const errorHandlingService = require('../../api/services/ErrorHandlingService');

describe('Error Handling Infrastructure', () => {
  describe('Error Handler Middleware', () => {
    it('should handle 404 errors', async () => {
      const response = await request(app)
        .get('/non-existent-route')
        .expect('Content-Type', /json/)
        .expect(404);
      
      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toHaveProperty('type', 'not_found_error');
      expect(response.body.error).toHaveProperty('status', 404);
    });
    
    it('should handle validation errors', async () => {
      const response = await request(app)
        .post('/api/connectors')
        .send({}) // Missing required fields
        .expect('Content-Type', /json/)
        .expect(400);
      
      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toHaveProperty('type', 'validation_error');
      expect(response.body.error).toHaveProperty('status', 400);
    });
  });
  
  describe('Error Handling Service', () => {
    it('should create error responses with appropriate status codes', () => {
      const error = new Error('Test error');
      error.name = 'ValidationError';
      
      const context = {
        user: { id: 'test-user' },
        path: '/test',
        method: 'GET'
      };
      
      const errorResponse = errorHandlingService.handleError(error, context);
      
      expect(errorResponse).toHaveProperty('error');
      expect(errorResponse.error).toHaveProperty('type', 'validation_error');
      expect(errorResponse.error).toHaveProperty('status', 400);
      expect(errorResponse.error).toHaveProperty('message', 'Test error');
    });
    
    it('should apply retry strategy for network errors', () => {
      const error = new Error('Connection refused');
      error.name = 'NetworkError';
      
      const context = {
        resource: 'test-api',
        retryCount: 1
      };
      
      const errorResponse = errorHandlingService.handleError(error, context);
      
      expect(errorResponse).toHaveProperty('error');
      expect(errorResponse.error).toHaveProperty('type', 'network_error');
      expect(errorResponse.error).toHaveProperty('status', 503);
      expect(errorResponse).toHaveProperty('recovery');
      expect(errorResponse.recovery).toHaveProperty('strategy', 'retry');
      expect(errorResponse.recovery).toHaveProperty('retryCount', 1);
    });
    
    it('should apply circuit breaker strategy for rate limit errors', () => {
      const error = new Error('Rate limit exceeded');
      error.name = 'RateLimitError';
      
      const context = {
        resource: 'test-api'
      };
      
      // Register circuit breaker for test
      errorHandlingService.registerCircuitBreaker('test-api', {
        failureThreshold: 5,
        resetTimeout: 30000
      });
      
      const errorResponse = errorHandlingService.handleError(error, context);
      
      expect(errorResponse).toHaveProperty('error');
      expect(errorResponse.error).toHaveProperty('type', 'rate_limit_error');
      expect(errorResponse.error).toHaveProperty('status', 429);
      expect(errorResponse).toHaveProperty('recovery');
      expect(errorResponse.recovery).toHaveProperty('strategy', 'circuit_breaker');
      expect(errorResponse.recovery).toHaveProperty('state', 'closed');
    });
  });
  
  describe('Retry Mechanism', () => {
    it('should retry failed operations', async () => {
      let attempts = 0;
      
      const testFunction = errorHandlingService.withRetry(async () => {
        attempts++;
        if (attempts < 3) {
          const error = new Error('Temporary failure');
          error.name = 'NetworkError';
          throw error;
        }
        return 'success';
      }, {
        maxRetries: 3,
        initialDelay: 10,
        maxDelay: 100,
        retryableErrors: ['network_error']
      });
      
      const result = await testFunction();
      
      expect(result).toBe('success');
      expect(attempts).toBe(3);
    });
    
    it('should not retry non-retryable errors', async () => {
      let attempts = 0;
      
      const testFunction = errorHandlingService.withRetry(async () => {
        attempts++;
        const error = new Error('Validation error');
        error.name = 'ValidationError';
        throw error;
      }, {
        maxRetries: 3,
        initialDelay: 10,
        maxDelay: 100,
        retryableErrors: ['network_error', 'timeout_error']
      });
      
      await expect(testFunction()).rejects.toThrow('Validation error');
      expect(attempts).toBe(1);
    });
  });
  
  describe('Circuit Breaker', () => {
    it('should trip circuit breaker after threshold failures', async () => {
      const resource = 'test-circuit-breaker';
      
      // Register circuit breaker
      errorHandlingService.registerCircuitBreaker(resource, {
        failureThreshold: 2,
        resetTimeout: 100
      });
      
      const circuitBreaker = errorHandlingService.circuitBreakers.get(resource);
      expect(circuitBreaker.state).toBe('closed');
      
      // Create test function
      const testFunction = errorHandlingService.withCircuitBreaker(async () => {
        throw new Error('Service unavailable');
      }, { resource });
      
      // First failure
      await expect(testFunction()).rejects.toThrow('Service unavailable');
      expect(circuitBreaker.state).toBe('closed');
      expect(circuitBreaker.failureCount).toBe(1);
      
      // Second failure - should trip circuit breaker
      await expect(testFunction()).rejects.toThrow('Service unavailable');
      expect(circuitBreaker.state).toBe('open');
      expect(circuitBreaker.failureCount).toBe(2);
      
      // Third attempt - circuit breaker is open
      await expect(testFunction()).rejects.toThrow('Circuit breaker for test-circuit-breaker is open');
      
      // Wait for circuit breaker to reset
      await new Promise(resolve => setTimeout(resolve, 150));
      
      // Circuit breaker should be half-open
      expect(circuitBreaker.state).toBe('half-open');
      
      // Successful call should close circuit breaker
      const successFunction = errorHandlingService.withCircuitBreaker(async () => {
        return 'success';
      }, { resource });
      
      await successFunction();
      expect(circuitBreaker.state).toBe('closed');
      expect(circuitBreaker.failureCount).toBe(0);
    });
  });
  
  describe('Timeout Mechanism', () => {
    it('should timeout long-running operations', async () => {
      const testFunction = errorHandlingService.withTimeout(async () => {
        await new Promise(resolve => setTimeout(resolve, 100));
        return 'success';
      }, { timeoutMs: 50 });
      
      await expect(testFunction()).rejects.toThrow('Operation timed out after 50ms');
    });
    
    it('should not timeout quick operations', async () => {
      const testFunction = errorHandlingService.withTimeout(async () => {
        await new Promise(resolve => setTimeout(resolve, 10));
        return 'success';
      }, { timeoutMs: 50 });
      
      const result = await testFunction();
      expect(result).toBe('success');
    });
  });
  
  describe('Fallback Mechanism', () => {
    it('should use fallback when primary function fails', async () => {
      const primaryFunction = async () => {
        throw new Error('Primary function failed');
      };
      
      const fallbackFunction = async () => {
        return 'fallback result';
      };
      
      const testFunction = errorHandlingService.withFallback(primaryFunction, fallbackFunction);
      
      const result = await testFunction();
      expect(result).toBe('fallback result');
    });
    
    it('should use primary function when it succeeds', async () => {
      const primaryFunction = async () => {
        return 'primary result';
      };
      
      const fallbackFunction = async () => {
        return 'fallback result';
      };
      
      const testFunction = errorHandlingService.withFallback(primaryFunction, fallbackFunction);
      
      const result = await testFunction();
      expect(result).toBe('primary result');
    });
  });
});
