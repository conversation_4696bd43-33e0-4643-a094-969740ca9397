'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { featureFlags, FeatureFlag } from '@/utils/features/featureFlags';
import { useAuth } from './AuthContext';

// Context type
type FeatureFlagContextType = {
  isEnabled: (feature: FeatureFlag) => boolean;
  enableFeature: (feature: FeatureFlag) => void;
  disableFeature: (feature: FeatureFlag) => void;
  toggleFeature: (feature: FeatureFlag) => void;
  getAllFeatures: () => Record<FeatureFlag, boolean>;
};

// Create context with default values
const FeatureFlagContext = createContext<FeatureFlagContextType>({
  isEnabled: () => false,
  enableFeature: () => {},
  disableFeature: () => {},
  toggleFeature: () => {},
  getAllFeatures: () => ({} as Record<FeatureFlag, boolean>),
});

// Hook for using feature flags in components
export const useFeatureFlags = () => useContext(FeatureFlagContext);

// Provider component
export const FeatureFlagProvider = ({ children }: { children: ReactNode }) => {
  const { user } = useAuth();
  const [initialized, setInitialized] = useState(false);

  // Initialize feature flags with user roles when user changes
  useEffect(() => {
    if (user) {
      featureFlags.setUser(user.roles || []);
    } else {
      featureFlags.setUser([]);
    }
    setInitialized(true);
  }, [user]);

  // Context value
  const value: FeatureFlagContextType = {
    isEnabled: (feature: FeatureFlag) => featureFlags.isEnabled(feature),
    enableFeature: (feature: FeatureFlag) => featureFlags.enable(feature),
    disableFeature: (feature: FeatureFlag) => featureFlags.disable(feature),
    toggleFeature: (feature: FeatureFlag) => featureFlags.toggle(feature),
    getAllFeatures: () => featureFlags.getAllFlags(),
  };

  // Only render children once initialized to prevent flicker
  if (!initialized) {
    return null;
  }

  return (
    <FeatureFlagContext.Provider value={value}>
      {children}
    </FeatureFlagContext.Provider>
  );
};
