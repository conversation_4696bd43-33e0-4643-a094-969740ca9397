/**
 * NovaFuse Partner SDK - Validators
 * 
 * This module provides validation functions for the Partner SDK.
 * 
 * @version 1.0.0
 * @license NovaFuse Proprietary
 */

/**
 * Validate partner configuration
 * @param {Object} config - Partner configuration
 * @throws {Error} - If configuration is invalid
 */
function validatePartnerConfig(config) {
  if (!config) {
    throw new Error('Partner configuration is required');
  }
  
  if (!config.partnerId) {
    throw new Error('Partner ID is required');
  }
  
  if (!config.partnerKey) {
    throw new Error('Partner API key is required');
  }
  
  if (!config.partnerName) {
    throw new Error('Partner name is required');
  }
  
  if (config.environment && !['production', 'sandbox'].includes(config.environment)) {
    throw new Error('Environment must be either "production" or "sandbox"');
  }
}

/**
 * Validate client data
 * @param {Object} clientData - Client data
 * @throws {Error} - If client data is invalid
 */
function validateClientData(clientData) {
  if (!clientData) {
    throw new Error('Client data is required');
  }
  
  if (!clientData.metadata) {
    throw new Error('Client metadata is required');
  }
  
  if (!clientData.metadata.clientId) {
    throw new Error('Client ID is required');
  }
  
  if (!clientData.metadata.scope) {
    throw new Error('Operation scope is required');
  }
  
  // Validate compliance data if provided
  if (clientData.complianceData) {
    // At minimum, we need controls
    if (!clientData.complianceData.controls && !Array.isArray(clientData.complianceData.controls)) {
      throw new Error('Compliance controls must be an array');
    }
  }
  
  // Validate GCP data if provided
  if (clientData.gcpData) {
    // At minimum, we need services
    if (!clientData.gcpData.services && !Array.isArray(clientData.gcpData.services)) {
      throw new Error('GCP services must be an array');
    }
  }
  
  // Validate Cyber-Safety data if provided
  if (clientData.cyberSafetyData) {
    // At minimum, we need controls
    if (!clientData.cyberSafetyData.controls && !Array.isArray(clientData.cyberSafetyData.controls)) {
      throw new Error('Cyber-Safety controls must be an array');
    }
  }
}

module.exports = {
  validatePartnerConfig,
  validateClientData
};
