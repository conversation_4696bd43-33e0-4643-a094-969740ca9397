import React from 'react';
import {
  DiagramFrame,
  ComponentBox,
  ComponentLabel,
  ComponentNumber,
  Arrow,
  ContainerBox,
  ContainerLabel,
  CurvedArrow,
  DiagramLegend,
  LegendItem,
  LegendColor,
  LegendText
} from '../../components/DiagramComponents';

const NovaVision = () => {
  return (
    <DiagramFrame>
      <ContainerBox width="750px" height="400px" left="25px" top="20px">
        <ContainerLabel>NOVAVISION - UNIVERSAL UI FRAMEWORK</ContainerLabel>
      </ContainerBox>
      
      {/* Four-Tier Architecture */}
      <ContainerBox width="700px" height="350px" left="50px" top="70px">
        <ContainerLabel>FOUR-TIER ARCHITECTURE</ContainerLabel>
      </ContainerBox>
      
      {/* Tier 1: Dynamic Compliance-Aware UI Rendering */}
      <ContainerBox width="650px" height="70px" left="75px" top="110px">
        <ContainerLabel>TIER 1: DYNAMIC COMPLIANCE-AWARE UI RENDERING</ContainerLabel>
      </ContainerBox>
      
      <ComponentBox left="100px" top="130px" width="120px" height="40px">
        <ComponentNumber>801</ComponentNumber>
        <ComponentLabel>UI Schema</ComponentLabel>
        Generator
      </ComponentBox>
      
      <ComponentBox left="250px" top="130px" width="120px" height="40px">
        <ComponentNumber>802</ComponentNumber>
        <ComponentLabel>Compliance</ComponentLabel>
        Rules Engine
      </ComponentBox>
      
      <ComponentBox left="400px" top="130px" width="120px" height="40px">
        <ComponentNumber>803</ComponentNumber>
        <ComponentLabel>Dynamic</ComponentLabel>
        Renderer
      </ComponentBox>
      
      <ComponentBox left="550px" top="130px" width="120px" height="40px">
        <ComponentNumber>804</ComponentNumber>
        <ComponentLabel>UI Validation</ComponentLabel>
        Layer
      </ComponentBox>
      
      <Arrow left="220px" top="150px" width="30px" />
      <Arrow left="370px" top="150px" width="30px" />
      <Arrow left="520px" top="150px" width="30px" />
      
      {/* Tier 2: Real-Time Regulation Switching */}
      <ContainerBox width="650px" height="70px" left="75px" top="190px">
        <ContainerLabel>TIER 2: REAL-TIME REGULATION SWITCHING</ContainerLabel>
      </ContainerBox>
      
      <ComponentBox left="100px" top="210px" width="120px" height="40px">
        <ComponentNumber>805</ComponentNumber>
        <ComponentLabel>Regulatory</ComponentLabel>
        Monitor
      </ComponentBox>
      
      <ComponentBox left="250px" top="210px" width="120px" height="40px">
        <ComponentNumber>806</ComponentNumber>
        <ComponentLabel>Hot-Swap</ComponentLabel>
        Engine
      </ComponentBox>
      
      <ComponentBox left="400px" top="210px" width="120px" height="40px">
        <ComponentNumber>807</ComponentNumber>
        <ComponentLabel>Zero-Downtime</ComponentLabel>
        Updater
      </ComponentBox>
      
      <ComponentBox left="550px" top="210px" width="120px" height="40px">
        <ComponentNumber>808</ComponentNumber>
        <ComponentLabel>State</ComponentLabel>
        Preservation
      </ComponentBox>
      
      <Arrow left="220px" top="230px" width="30px" />
      <Arrow left="370px" top="230px" width="30px" />
      <Arrow left="520px" top="230px" width="30px" />
      
      {/* Tier 3: AI-Powered Interface Optimization */}
      <ContainerBox width="650px" height="70px" left="75px" top="270px">
        <ContainerLabel>TIER 3: AI-POWERED INTERFACE OPTIMIZATION</ContainerLabel>
      </ContainerBox>
      
      <ComponentBox left="100px" top="290px" width="120px" height="40px">
        <ComponentNumber>809</ComponentNumber>
        <ComponentLabel>Usage</ComponentLabel>
        Analytics
      </ComponentBox>
      
      <ComponentBox left="250px" top="290px" width="120px" height="40px">
        <ComponentNumber>810</ComponentNumber>
        <ComponentLabel>AI Optimization</ComponentLabel>
        Engine
      </ComponentBox>
      
      <ComponentBox left="400px" top="290px" width="120px" height="40px">
        <ComponentNumber>811</ComponentNumber>
        <ComponentLabel>Compliance</ComponentLabel>
        Verification
      </ComponentBox>
      
      <ComponentBox left="550px" top="290px" width="120px" height="40px">
        <ComponentNumber>812</ComponentNumber>
        <ComponentLabel>Adaptive</ComponentLabel>
        UI Generator
      </ComponentBox>
      
      <Arrow left="220px" top="310px" width="30px" />
      <Arrow left="370px" top="310px" width="30px" />
      <Arrow left="520px" top="310px" width="30px" />
      
      {/* Tier 4: Cross-Platform Consistency Enforcer */}
      <ContainerBox width="650px" height="70px" left="75px" top="350px">
        <ContainerLabel>TIER 4: CROSS-PLATFORM CONSISTENCY ENFORCER</ContainerLabel>
      </ContainerBox>
      
      <ComponentBox left="100px" top="370px" width="120px" height="40px">
        <ComponentNumber>813</ComponentNumber>
        <ComponentLabel>Platform</ComponentLabel>
        Detector
      </ComponentBox>
      
      <ComponentBox left="250px" top="370px" width="120px" height="40px">
        <ComponentNumber>814</ComponentNumber>
        <ComponentLabel>Adaptive</ComponentLabel>
        Rendering
      </ComponentBox>
      
      <ComponentBox left="400px" top="370px" width="120px" height="40px">
        <ComponentNumber>815</ComponentNumber>
        <ComponentLabel>Consistency</ComponentLabel>
        Validator
      </ComponentBox>
      
      <ComponentBox left="550px" top="370px" width="120px" height="40px">
        <ComponentNumber>816</ComponentNumber>
        <ComponentLabel>Compliance</ComponentLabel>
        Audit Logger
      </ComponentBox>
      
      <Arrow left="220px" top="390px" width="30px" />
      <Arrow left="370px" top="390px" width="30px" />
      <Arrow left="520px" top="390px" width="30px" />
      
      {/* Vertical Integration Arrows */}
      <Arrow left="160px" top="170px" width="2px" height="20px" />
      <Arrow left="310px" top="170px" width="2px" height="20px" />
      <Arrow left="460px" top="170px" width="2px" height="20px" />
      <Arrow left="610px" top="170px" width="2px" height="20px" />
      
      <Arrow left="160px" top="250px" width="2px" height="20px" />
      <Arrow left="310px" top="250px" width="2px" height="20px" />
      <Arrow left="460px" top="250px" width="2px" height="20px" />
      <Arrow left="610px" top="250px" width="2px" height="20px" />
      
      <Arrow left="160px" top="330px" width="2px" height="20px" />
      <Arrow left="310px" top="330px" width="2px" height="20px" />
      <Arrow left="460px" top="330px" width="2px" height="20px" />
      <Arrow left="610px" top="330px" width="2px" height="20px" />
      
      {/* Legend */}
      <DiagramLegend>
        <LegendItem>
          <LegendColor color="#fff" />
          <LegendText>Tier 1: UI Rendering</LegendText>
        </LegendItem>
        <LegendItem>
          <LegendColor color="#fff" />
          <LegendText>Tier 2: Regulation Switching</LegendText>
        </LegendItem>
        <LegendItem>
          <LegendColor color="#fff" />
          <LegendText>Tier 3: AI Optimization</LegendText>
        </LegendItem>
        <LegendItem>
          <LegendColor color="#fff" />
          <LegendText>Tier 4: Cross-Platform</LegendText>
        </LegendItem>
      </DiagramLegend>
    </DiagramFrame>
  );
};

export default NovaVision;
