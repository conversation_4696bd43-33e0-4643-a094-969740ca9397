/**
 * evidence-utils.js
 * 
 * This file provides utility functions for working with evidence in the NovaProof system.
 * These utilities help with common evidence operations, conversions, and analysis.
 */

const { Evidence, EvidenceStatus } = require('../models/Evidence');
const crypto = require('crypto');

/**
 * Create evidence from a JSON object
 * @param {Object} json - The JSON representation of evidence
 * @returns {Evidence} - A new evidence instance created from the JSON
 */
function evidenceFromJSON(json) {
  return Evidence.fromJSON(json);
}

/**
 * Convert evidence to a JSON string
 * @param {Evidence} evidence - The evidence to convert
 * @returns {String} - A JSON string representation of the evidence
 */
function evidenceToJSONString(evidence) {
  return JSON.stringify(evidence.toJSON());
}

/**
 * Create evidence from a control finding
 * @param {Object} finding - The control finding
 * @param {Object} options - Options for creating the evidence
 * @returns {Evidence} - A new evidence instance created from the finding
 */
function evidenceFromFinding(finding, options = {}) {
  if (!finding || !finding.controlId) {
    throw new Error('Invalid finding: must have a controlId');
  }
  
  const framework = options.framework || finding.framework || 'CUSTOM';
  const source = options.source || finding.source || 'MANUAL';
  
  const evidenceData = {
    controlId: finding.controlId,
    framework,
    source,
    timestamp: finding.timestamp || new Date().toISOString(),
    data: {
      value: finding.value !== undefined ? finding.value : true,
      details: finding.details || '',
      score: finding.score !== undefined ? finding.score : 100
    },
    status: EvidenceStatus.COLLECTED
  };
  
  if (finding.attachment) {
    evidenceData.attachment = finding.attachment;
  }
  
  return new Evidence(evidenceData, options);
}

/**
 * Create evidence from a compliance scan result
 * @param {Object} scanResult - The compliance scan result
 * @param {Object} options - Options for creating the evidence
 * @returns {Array<Evidence>} - An array of evidence instances created from the scan result
 */
function evidenceFromScanResult(scanResult, options = {}) {
  if (!scanResult || !Array.isArray(scanResult.findings)) {
    throw new Error('Invalid scan result: must have an array of findings');
  }
  
  const framework = options.framework || scanResult.framework || 'CUSTOM';
  const source = options.source || scanResult.source || 'SCAN';
  const timestamp = scanResult.timestamp || new Date().toISOString();
  
  return scanResult.findings.map(finding => {
    return evidenceFromFinding(finding, {
      ...options,
      framework,
      source,
      timestamp,
      metadata: {
        ...options.metadata,
        scanId: scanResult.id,
        scanName: scanResult.name,
        scanType: scanResult.type
      }
    });
  });
}

/**
 * Filter evidence by status
 * @param {Array<Evidence>} evidenceItems - The evidence items to filter
 * @param {String|Array<String>} status - The status or statuses to filter by
 * @returns {Array<Evidence>} - The filtered evidence items
 */
function filterEvidenceByStatus(evidenceItems, status) {
  if (!Array.isArray(evidenceItems)) {
    throw new Error('First argument must be an array of Evidence instances');
  }
  
  const statusArray = Array.isArray(status) ? status : [status];
  
  return evidenceItems.filter(item => {
    if (!(item instanceof Evidence)) {
      throw new Error('All items must be Evidence instances');
    }
    
    return statusArray.includes(item.status);
  });
}

/**
 * Filter evidence by framework
 * @param {Array<Evidence>} evidenceItems - The evidence items to filter
 * @param {String|Array<String>} framework - The framework or frameworks to filter by
 * @returns {Array<Evidence>} - The filtered evidence items
 */
function filterEvidenceByFramework(evidenceItems, framework) {
  if (!Array.isArray(evidenceItems)) {
    throw new Error('First argument must be an array of Evidence instances');
  }
  
  const frameworkArray = Array.isArray(framework) ? framework : [framework];
  
  return evidenceItems.filter(item => {
    if (!(item instanceof Evidence)) {
      throw new Error('All items must be Evidence instances');
    }
    
    return frameworkArray.includes(item.framework);
  });
}

/**
 * Filter evidence by control ID
 * @param {Array<Evidence>} evidenceItems - The evidence items to filter
 * @param {String|Array<String>} controlId - The control ID or IDs to filter by
 * @returns {Array<Evidence>} - The filtered evidence items
 */
function filterEvidenceByControlId(evidenceItems, controlId) {
  if (!Array.isArray(evidenceItems)) {
    throw new Error('First argument must be an array of Evidence instances');
  }
  
  const controlIdArray = Array.isArray(controlId) ? controlId : [controlId];
  
  return evidenceItems.filter(item => {
    if (!(item instanceof Evidence)) {
      throw new Error('All items must be Evidence instances');
    }
    
    return controlIdArray.includes(item.controlId);
  });
}

/**
 * Group evidence by framework
 * @param {Array<Evidence>} evidenceItems - The evidence items to group
 * @returns {Object} - An object with frameworks as keys and arrays of evidence as values
 */
function groupEvidenceByFramework(evidenceItems) {
  if (!Array.isArray(evidenceItems)) {
    throw new Error('First argument must be an array of Evidence instances');
  }
  
  const groups = {};
  
  evidenceItems.forEach(item => {
    if (!(item instanceof Evidence)) {
      throw new Error('All items must be Evidence instances');
    }
    
    const framework = item.framework;
    
    if (!groups[framework]) {
      groups[framework] = [];
    }
    
    groups[framework].push(item);
  });
  
  return groups;
}

/**
 * Group evidence by control ID
 * @param {Array<Evidence>} evidenceItems - The evidence items to group
 * @returns {Object} - An object with control IDs as keys and arrays of evidence as values
 */
function groupEvidenceByControlId(evidenceItems) {
  if (!Array.isArray(evidenceItems)) {
    throw new Error('First argument must be an array of Evidence instances');
  }
  
  const groups = {};
  
  evidenceItems.forEach(item => {
    if (!(item instanceof Evidence)) {
      throw new Error('All items must be Evidence instances');
    }
    
    const controlId = item.controlId;
    
    if (!groups[controlId]) {
      groups[controlId] = [];
    }
    
    groups[controlId].push(item);
  });
  
  return groups;
}

/**
 * Calculate the verification coverage for a set of evidence items
 * @param {Array<Evidence>} evidenceItems - The evidence items to analyze
 * @returns {Object} - An object with verification coverage statistics
 */
function calculateVerificationCoverage(evidenceItems) {
  if (!Array.isArray(evidenceItems)) {
    throw new Error('First argument must be an array of Evidence instances');
  }
  
  const total = evidenceItems.length;
  let verified = 0;
  let invalid = 0;
  let expired = 0;
  let pending = 0;
  
  evidenceItems.forEach(item => {
    if (!(item instanceof Evidence)) {
      throw new Error('All items must be Evidence instances');
    }
    
    if (item.status === EvidenceStatus.VERIFIED) {
      verified++;
    } else if (item.status === EvidenceStatus.INVALID) {
      invalid++;
    } else if (item.status === EvidenceStatus.EXPIRED) {
      expired++;
    } else if (item.status === EvidenceStatus.PENDING) {
      pending++;
    }
  });
  
  return {
    total,
    verified,
    invalid,
    expired,
    pending,
    verifiedPercentage: total > 0 ? (verified / total) * 100 : 0,
    invalidPercentage: total > 0 ? (invalid / total) * 100 : 0,
    expiredPercentage: total > 0 ? (expired / total) * 100 : 0,
    pendingPercentage: total > 0 ? (pending / total) * 100 : 0
  };
}

module.exports = {
  evidenceFromJSON,
  evidenceToJSONString,
  evidenceFromFinding,
  evidenceFromScanResult,
  filterEvidenceByStatus,
  filterEvidenceByFramework,
  filterEvidenceByControlId,
  groupEvidenceByFramework,
  groupEvidenceByControlId,
  calculateVerificationCoverage
};
