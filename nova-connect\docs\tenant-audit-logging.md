# Tenant-Specific Audit Logging

NovaConnect UAC implements tenant-specific audit logging to support our single-tenant architecture. This document explains how the audit logging system works and how to use it.

## Overview

The audit logging system captures detailed information about all API requests and important system events. For multi-tenant deployments, each tenant's audit logs are isolated and stored separately to ensure complete tenant isolation.

Key features:
- Tenant-specific audit logs stored in separate BigQuery tables
- Local file-based audit logs for development and backup
- Comprehensive audit information including user, action, resource, and context
- Support for compliance requirements with detailed chain-of-custody

## How It Works

### Tenant Identification

Tenants are identified by the `x-tenant-id` HTTP header in API requests. This header is set by the API Gateway based on the hostname or other tenant identification mechanism.

Example:
```
GET /api/connectors HTTP/1.1
Host: tenant-abc123.novafuse.io
x-tenant-id: abc123
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### Audit Log Storage

Audit logs are stored in two locations:

1. **Local File Storage**: All audit logs are stored in JSON files for development environments and as a backup mechanism.

2. **Google BigQuery**: In production environments, audit logs are stored in BigQuery for scalable, high-performance querying:
   - Global audit table: `novafuse_audit.events`
   - Tenant-specific tables: `tenant_<tenant_id>.audit_logs`

### Audit Log Structure

Each audit log entry contains:

```json
{
  "id": "550e8400-e29b-41d4-a716-446655440000",
  "timestamp": "2023-04-15T12:34:56.789Z",
  "userId": "user-123",
  "action": "GET",
  "resourceType": "connectors",
  "resourceId": "conn-456",
  "details": {
    "path": "/api/connectors/conn-456",
    "query": { "include": "config" },
    "body": null
  },
  "ip": "***********",
  "userAgent": "Mozilla/5.0...",
  "status": "success",
  "teamId": "team-789",
  "environmentId": "production",
  "tenantId": "abc123"
}
```

## Using Tenant-Specific Audit Logging

### Logging Events

To log an audit event for a specific tenant:

```javascript
const auditService = require('./services/AuditService');

// Log a tenant-specific event
await auditService.logTenantEvent('tenant-id', {
  userId: 'user-123',
  action: 'CREATE',
  resourceType: 'connector',
  resourceId: 'conn-456',
  details: { /* additional details */ }
});
```

### Querying Audit Logs

To query audit logs for a specific tenant:

```javascript
// Get all audit logs for a tenant (from local storage)
const logs = await auditService.getAuditLogs({ 
  tenantId: 'tenant-id' 
});

// Query BigQuery for tenant-specific logs (more scalable for production)
const { BigQuery } = require('@google-cloud/bigquery');
const bigquery = new BigQuery();

const query = `
  SELECT * 
  FROM \`tenant_tenant-id.audit_logs\`
  WHERE timestamp > TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 7 DAY)
  ORDER BY timestamp DESC
  LIMIT 100
`;

const [rows] = await bigquery.query({ query });
```

## Tenant Provisioning and Deprovisioning

When provisioning a new tenant, the system automatically:

1. Creates a tenant-specific BigQuery dataset (`tenant_<tenant_id>`)
2. Creates an audit logs table within that dataset
3. Sets up appropriate access controls

When deprovisioning a tenant, you can choose to:
1. Retain the audit logs for compliance purposes
2. Export and archive the audit logs
3. Delete the audit logs completely

Use the provided scripts:
- `scripts/provision-tenant.sh <tenant_id>` - Creates tenant infrastructure including audit logging
- `scripts/deprovision-tenant.sh <tenant_id>` - Removes tenant infrastructure with options for audit log handling

## Testing Tenant-Specific Audit Logging

Use the provided test script to verify tenant-specific audit logging:

```bash
node scripts/test-tenant-audit.js
```

This script simulates API requests for multiple tenants and verifies that the audit logs are correctly captured and isolated.

## Compliance Considerations

The tenant-specific audit logging system is designed to meet requirements for:

- SOC2 Type II
- HIPAA
- GDPR
- PCI-DSS
- FedRAMP

Key compliance features:
- Complete tenant isolation
- Immutable audit trail
- Detailed chain-of-custody
- Configurable retention periods
- Encryption at rest and in transit
