<!DOCTYPE html>
<html>
<head>
    <title>Patent Diagram Generator - Mermaid to SVG Converter</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        :root {
            --uspto-black: #000000;
            --uspto-white: #FFFFFF;
            --uspto-gray1: #333333;
            --uspto-gray2: #666666;
            --uspto-gray3: #999999;
        }
        
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .header {
            background-color: var(--uspto-gray1);
            color: white;
            padding: 20px;
            margin-bottom: 30px;
            border-radius: 5px;
        }
        
        .controls {
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 30px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .diagram-container {
            margin-bottom: 40px;
            padding: 20px;
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            position: relative;
        }
        
        .diagram-title {
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 15px;
            color: var(--uspto-gray1);
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        
        .diagram-wrapper {
            display: flex;
            justify-content: center;
            margin: 20px 0;
            min-height: 300px;
            align-items: center;
            background-color: white;
            border: 1px solid #eee;
            padding: 20px;
        }
        
        .btn {
            background-color: var(--uspto-gray1);
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            opacity: 0.9;
            transform: translateY(-1px);
        }
        
        .btn-primary {
            background-color: var(--uspto-gray1);
        }
        
        .btn-secondary {
            background-color: var(--uspto-gray2);
        }
        
        .btn-download {
            background-color: #27ae60;
        }
        
        .file-input {
            margin: 10px 0;
        }
        
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .figure-number {
            position: absolute;
            top: 10px;
            right: 10px;
            background-color: var(--uspto-gray1);
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
        
        .figure-caption {
            margin-top: 10px;
            font-style: italic;
            color: var(--uspto-gray2);
            text-align: center;
        }
        
        .progress-container {
            width: 100%;
            background-color: #f0f0f0;
            border-radius: 4px;
            margin: 10px 0;
            display: none;
        }
        
        .progress-bar {
            width: 0%;
            height: 20px;
            background-color: var(--uspto-gray2);
            border-radius: 4px;
            text-align: center;
            line-height: 20px;
            color: white;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Patent Diagram Generator</h1>
        <p>Convert Mermaid diagrams to USPTO-compliant SVGs</p>
    </div>
    
    <div class="controls">
        <h2>Batch Process Mermaid Files</h2>
        <div class="file-input">
            <input type="file" id="mermaid-files" multiple accept=".mmd,.md,.txt" style="display: none;">
            <button class="btn btn-primary" onclick="document.getElementById('mermaid-files').click()">
                Select Mermaid Files (.mmd)
            </button>
            <span id="file-count">No files selected</span>
        </div>
        
        <div class="progress-container" id="batch-progress">
            <div class="progress-bar" id="progress-bar">0%</div>
        </div>
        
        <div id="status" class="status" style="display: none;"></div>
        
        <button id="process-btn" class="btn btn-download" style="display: none;" onclick="processBatch()">
            Process All Files
        </button>
    </div>
    
    <div id="diagrams-container">
        <!-- Processed diagrams will appear here -->
    </div>
    <h1>Comphyology Patent - Diagram SVG Converter</h1>

    <div class="instructions">
        <h3>Instructions:</h3>
        <ol>
            <li>Each diagram will render below with its corresponding figure number</li>
            <li>Click the "Download SVG" button below each diagram to save it</li>
            <li>Diagrams are pre-named according to their figure numbers in the patent document</li>
            <li>SVG files will be saved with transparent backgrounds for easy integration</li>
        </ol>
    </div>

    <h2>Figure 1: UUFT Core Architecture (100 Series)</h2>
    <div class="diagram-container">
        <div class="mermaid" id="diagram1">
            graph TD
                subgraph UUFT_Core["UUFT Core Architecture"]
                    A[Input Layer] --> B[Preprocessing Module]
                    B --> C[UUFT Processing Core]
                    C --> D[Output Layer]
                end
                
                %% Reference Numbers (100 series)
                A:::reference100
                B:::reference110
                C:::reference120
                D:::reference130
                
                %% Styling
                classDef reference100,reference110,reference120,reference130 fill:none,stroke:none,font-size:8pt
        </div>
        <button class="download-btn" onclick="downloadSVG('diagram1', 'Fig1_UUFT_Core_Architecture.svg')">Download SVG</button>
    </div>

    <h2>Figure 2: NovaFuse Components (200 Series)</h2>
    <div class="diagram-container">
        <div class="mermaid" id="diagram2">
            graph TD
                subgraph NovaFuse["NovaFuse Components"]
                    A[NovaCore] --> B[NovaNet]
                    A --> C[NovaSync]
                    A --> D[NovaSecure]
                end
                
                %% Reference Numbers (200 series)
                A:::reference200
                B:::reference210
                C:::reference220
                D:::reference230
                
                %% Styling
                classDef reference200,reference210,reference220,reference230 fill:none,stroke:none,font-size:8pt
        </div>
        <button class="download-btn" onclick="downloadSVG('diagram2', 'Fig2_NovaFuse_Components.svg')">Download SVG</button>
    </div>

    <h2>Figure 4: 18/82 Principle (400 Series)</h2>
    <div class="diagram-container">
        <div class="mermaid" id="diagram4">
            pie showData
                title 18/82 Principle
                "Active Input (18%)" : 18
                "Adaptive Structure (82%)" : 82
                
                %% Reference Numbers (400 series)
                title:::reference400
                active["Active Input (18%)"]:::reference410
                adaptive["Adaptive Structure (82%)"]:::reference420
                
                %% Styling
                classDef reference400,reference410,reference420 fill:none,stroke:none,font-size:8pt
        </div>
        <button class="download-btn" onclick="downloadSVG('diagram4', 'Fig4_1882_Principle.svg')">Download SVG</button>
    </div>

    <h2>Figure 5: Consciousness Threshold (500 Series)</h2>
    <div class="diagram-container">
        <div class="mermaid" id="diagram5">
            flowchart TD
                U[UUFT Field Strength] -->|>= 2847| C[Conscious State]
                U -->|< 2847| UC[Unconscious State]
                
                %% Reference Numbers (500 series)
                U:::reference500
                C:::reference510
                UC:::reference520
                
                %% Styling
                classDef reference500,reference510,reference520 fill:none,stroke:none,font-size:8pt
        </div>
        <button class="download-btn" onclick="downloadSVG('diagram5', 'Fig5_Consciousness_Threshold.svg')">Download SVG</button>
    </div>

    <h2>Figure 6: Protein Folding Optimization (600 Series)</h2>
    <div class="diagram-container">
        <div class="mermaid" id="diagram6">
            flowchart TD
                S[Amino Acid Sequence] --> F[UUFT Folding Calculation]
                F -->|>= 31.42| Stable[Stable Protein Structure]
                F -->|< 31.42| Misfolded[Non-Functional Protein]
                
                %% Reference Numbers (600 series)
                S:::reference600
                F:::reference610
                Stable:::reference620
                Misfolded:::reference630
                
                %% Styling
                classDef reference600,reference610,reference620,reference630 fill:none,stroke:none,font-size:8pt
        </div>
        <button class="download-btn" onclick="downloadSVG('diagram6', 'Fig6_Protein_Folding_Optimization.svg')">Download SVG</button>
    </div>

    <h2>Figure 7: Dark Field Classification (700 Series)</h2>
    <div class="diagram-container">
        <div class="mermaid" id="diagram7">
            flowchart TD
                U[UUFT Field Strength] -->|< 100| NM[Normal Matter]
                U -->|>= 100 & < 1000| DM[Dark Matter]
                U -->|>= 1000| DE[Dark Energy]
                
                %% Reference Numbers (700 series)
                U:::reference700
                NM:::reference710
                DM:::reference720
                DE:::reference730
                
                %% Styling
                classDef reference700,reference710,reference720,reference730 fill:none,stroke:none,font-size:8pt
        </div>
        <button class="download-btn" onclick="downloadSVG('diagram7', 'Fig7_Dark_Field_Classification.svg')">Download SVG</button>
    </div>

    <h2>Figure 8: Finite Universe Principle (800 Series)</h2>
    <div class="diagram-container">
        <div class="mermaid" id="diagram8">
            graph LR
                subgraph Finite_Universe["Finite Universe (∂Ψ=0)"]
                    A[Conscious Observer] --> B[Measurable Reality]
                    B --> C[Information Boundary]
                end
                D[Infinite Potential] -.->|18% Permeability| C
                C -->|82% Containment| B
                
                %% Reference Numbers (800 series)
                A:::reference800
                B:::reference810
                C:::reference820
                D:::reference830
                
                %% Styling
                classDef reference800,reference810,reference820,reference830 fill:none,stroke:none,font-size:8pt
        </div>
        <button class="download-btn" onclick="downloadSVG('diagram8', 'Fig8_Finite_Universe_Principle.svg')">Download SVG</button>
    </div>

    <h2>Figure 3: 3-6-9-12-16 Alignment (300 Series)</h2>
    <div class="diagram-container">
        <div class="mermaid" id="diagram3">
            graph TD
                A[3: Input Layer] --> B[6: Hidden Layer 1]
                B --> C[9: Hidden Layer 2]
                C --> D[12: Hidden Layer 3]
                D --> E[16: Output Layer]
                
                %% Reference Numbers (300 series)
                A:::reference300
                B:::reference310
                C:::reference320
                D:::reference330
                E:::reference340
                
                %% Styling
                classDef reference300,reference310,reference320,reference330,reference340 fill:none,stroke:none,font-size:8pt
        </div>
        <button class="download-btn" onclick="downloadSVG('diagram3', 'Fig3_3691216_Alignment.svg')">Download SVG</button>

        <h3>Part 2: Transaction Flow</h3>
        <div class="mermaid" id="diagram3b">
            sequenceDiagram
                participant D as Developer
                participant S as NovaStore
                participant C as Customer
                D->>S: Submit UUFT-Certified Plugin
                S->>C: Distribute Enhanced Solution
                C->>S: Pay $0.0018/Transaction
                S->>D: Automatic 82% Share
        </div>
        <button class="download-btn" onclick="downloadSVG('diagram3b', 'Fig3b_Transaction_Flow.svg')">Download SVG</button>
    </div>

    <h2>Figure 4: Trinitarian AI Governance</h2>
    <div class="diagram-container">
        <div class="mermaid" id="diagram4">
            stateDiagram-v2
                [*] --> Source: Input Sanitization
                Source --> Validation: 18% Compute Budget
                Validation --> Integration: zkProof Attestation
                Integration --> [*]: Constrained Outputs

                state Source {
                    [*] --> InputFilter
                    InputFilter --> PrivacyCheck
                    PrivacyCheck --> [*]
                }

                state Validation {
                    [*] --> MemoryBound
                    MemoryBound --> InstructionLimit
                    InstructionLimit --> [*]
                }

                state Integration {
                    [*] --> FormalVerify
                    FormalVerify --> AnonymityEnforce
                    AnonymityEnforce --> [*]
                }

                style Source fill:#f5f5f5,stroke:#333
                style Validation fill:#e0e0e0,stroke:#333
                style Integration fill:#c0c0c0,stroke:#333
        </div>
        <button class="download-btn" onclick="downloadSVG('diagram4', 'Fig4_Trinitarian_AI_Governance.svg')">Download SVG</button>
    </div>

    <h2>Figure 5: Pattern Adaptation Across Domains</h2>
    <div class="diagram-container">
        <div class="mermaid" id="diagram5">
            flowchart LR
                A[Financial\nMarket Patterns] --> U[Universal\nEncoder]
                B[Healthcare\nDiagnostic Patterns] --> U
                U --> T[Translation Matrix]
                T --> C[Energy Grid\nOptimizations]

                style A fill:#f5f5f5,stroke:#333
                style B fill:#e0e0e0,stroke:#333
                style U fill:#c0c0c0,stroke:#333,stroke-width:2px
                style T fill:#b0b0b0,stroke:#333,stroke-width:2px,stroke-dasharray: 5 5
                style C fill:#a0a0a0,stroke:#333
        </div>
        <button class="download-btn" onclick="downloadSVG('diagram5', 'Fig5_Pattern_Adaptation_Across_Domains.svg')">Download SVG</button>
    </div>

    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'neutral',
            securityLevel: 'loose',
            themeVariables: {
                primaryColor: '#f4f4f4',
                primaryTextColor: '#333',
                primaryBorderColor: '#333',
                lineColor: '#333',
                secondaryColor: '#e0e0e0',
                tertiaryColor: '#f4f4f4'
            }
        });

        function downloadSVG(id, filename) {
            const svgElement = document.querySelector(`#${id} svg`);
            if (!svgElement) {
                alert("SVG not found. Please wait for the diagram to render completely.");
                return;
            }

            // Get SVG source
            const serializer = new XMLSerializer();
            let source = serializer.serializeToString(svgElement);

            // Add namespace if not already present
            if(!source.match(/^<svg[^>]+xmlns="http:\/\/www\.w3\.org\/2000\/svg"/)) {
                source = source.replace(/^<svg/, '<svg xmlns="http://www.w3.org/2000/svg"');
            }

            // Add XML declaration
            source = '<?xml version="1.0" standalone="no"?>\r\n' + source;

            // Convert SVG source to URI data scheme
            const url = "data:image/svg+xml;charset=utf-8," + encodeURIComponent(source);

            // Create download link
            const downloadLink = document.createElement("a");
            downloadLink.href = url;
            downloadLink.download = filename;
            document.body.appendChild(downloadLink);
            downloadLink.click();
            document.body.removeChild(downloadLink);
        }
    </script>
</body>
</html>

