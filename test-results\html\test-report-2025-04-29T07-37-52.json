{"numFailedTestSuites": 0, "numFailedTests": 0, "numPassedTestSuites": 1, "numPassedTests": 15, "numPendingTestSuites": 0, "numPendingTests": 0, "numRuntimeErrorTestSuites": 0, "numTodoTests": 0, "numTotalTestSuites": 1, "numTotalTests": 15, "openHandles": [], "snapshot": {"added": 0, "didUpdate": false, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0}, "startTime": 1745912268833, "success": false, "testResults": [{"leaks": false, "numFailingTests": 0, "numPassingTests": 15, "numPendingTests": 0, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 1745912272865, "runtime": 2977, "slow": false, "start": 1745912269888}, "skipped": false, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testFilePath": "D:\\novafuse-api-superstore\\tests\\unit\\connectors\\connector-registry.test.js", "testResults": [{"ancestorTitles": ["Connector Registry Service", "getAllConnectors"], "duration": 6, "failureDetails": [], "failureMessages": [], "fullName": "Connector Registry Service getAllConnectors should return an empty array when no connectors exist", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should return an empty array when no connectors exist"}, {"ancestorTitles": ["Connector Registry Service", "getAllConnectors"], "duration": 22, "failureDetails": [], "failureMessages": [], "fullName": "Connector Registry Service getAllConnectors should return all connectors", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should return all connectors"}, {"ancestorTitles": ["Connector Registry Service", "getAllConnectors"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "Connector Registry Service getAllConnectors should filter connectors by status", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should filter connectors by status"}, {"ancestorTitles": ["Connector Registry Service", "getAllConnectors"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Connector Registry Service getAllConnectors should filter connectors by type", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should filter connectors by type"}, {"ancestorTitles": ["Connector Registry Service", "getAllConnectors"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "Connector Registry Service getAllConnectors should return connectors from cache when available", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should return connectors from cache when available"}, {"ancestorTitles": ["Connector Registry Service", "getConnector"], "duration": 35, "failureDetails": [], "failureMessages": [], "fullName": "Connector Registry Service getConnector should throw an error when connector does not exist", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should throw an error when connector does not exist"}, {"ancestorTitles": ["Connector Registry Service", "getConnector"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "Connector Registry Service getConnector should return a connector by ID", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should return a connector by ID"}, {"ancestorTitles": ["Connector Registry Service", "getConnector"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Connector Registry Service getConnector should return connector from cache when available", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should return connector from cache when available"}, {"ancestorTitles": ["Connector Registry Service", "createConnector"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "Connector Registry Service createConnector should create a new connector", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should create a new connector"}, {"ancestorTitles": ["Connector Registry Service", "createConnector"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Connector Registry Service createConnector should throw an error when connector data is invalid", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should throw an error when connector data is invalid"}, {"ancestorTitles": ["Connector Registry Service", "updateConnector"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "Connector Registry Service updateConnector should update an existing connector", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should update an existing connector"}, {"ancestorTitles": ["Connector Registry Service", "updateConnector"], "duration": 5, "failureDetails": [], "failureMessages": [], "fullName": "Connector Registry Service updateConnector should throw an error when connector does not exist", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should throw an error when connector does not exist"}, {"ancestorTitles": ["Connector Registry Service", "updateConnector"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "Connector Registry Service updateConnector should throw an error when update data is invalid", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should throw an error when update data is invalid"}, {"ancestorTitles": ["Connector Registry Service", "deleteConnector"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Connector Registry Service deleteConnector should delete an existing connector", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should delete an existing connector"}, {"ancestorTitles": ["Connector Registry Service", "deleteConnector"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "Connector Registry Service deleteConnector should throw an error when connector does not exist", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should throw an error when connector does not exist"}], "failureMessage": null}], "wasInterrupted": false}