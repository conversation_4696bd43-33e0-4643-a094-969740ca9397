/**
 * OfflineCollaborationRoom Component
 * 
 * A component for displaying a collaboration room with offline support.
 */

import React, { useState, useEffect, useRef, useCallback } from 'react';
import PropTypes from 'prop-types';
import { useOfflineCollaboration } from '../collaboration/OfflineCollaborationContext';
import { useOffline } from '../offline/OfflineContext';
import { useTheme } from '../theme/ThemeContext';
import { useI18n } from '../i18n/I18nContext';
import { Animated } from './Animated';
import { FormattedDate } from './FormattedDate';

/**
 * OfflineCollaborationRoom component
 * 
 * @param {Object} props - Component props
 * @param {string} props.roomId - Room ID
 * @param {Function} [props.onLeave] - Callback when leaving room
 * @param {string} [props.className] - Additional CSS class names
 * @param {Object} [props.style] - Additional inline styles
 * @returns {React.ReactElement} OfflineCollaborationRoom component
 */
const OfflineCollaborationRoom = ({
  roomId,
  onLeave,
  className = '',
  style = {}
}) => {
  const { theme } = useTheme();
  const { translate } = useI18n();
  const { isOnline, offlineMode } = useOffline();
  const {
    isConnected,
    currentRoom,
    messages,
    users,
    cursors,
    sharedState,
    isLoading,
    pendingChanges,
    isSyncing,
    lastSyncTime,
    isOffline,
    joinRoom,
    leaveRoom,
    sendMessage,
    updateCursorPosition,
    updateSharedState,
    syncPendingChanges
  } = useOfflineCollaboration();
  
  // State
  const [messageText, setMessageText] = useState('');
  const [error, setError] = useState(null);
  const [cursorPosition, setCursorPosition] = useState({ x: 0, y: 0 });
  const [showUserList, setShowUserList] = useState(false);
  const [showOfflineIndicator, setShowOfflineIndicator] = useState(false);
  
  // Refs
  const messagesEndRef = useRef(null);
  const messageInputRef = useRef(null);
  const roomContainerRef = useRef(null);
  
  // Join room on mount
  useEffect(() => {
    if (isConnected && roomId && (!currentRoom || currentRoom.id !== roomId)) {
      joinRoom(roomId).catch(err => {
        console.error('Error joining room:', err);
        setError(err.message || 'Failed to join room');
      });
    }
    
    // Leave room on unmount
    return () => {
      if (currentRoom && currentRoom.id === roomId) {
        leaveRoom().catch(err => {
          console.error('Error leaving room:', err);
        });
      }
    };
  }, [isConnected, roomId, currentRoom, joinRoom, leaveRoom]);
  
  // Scroll to bottom when messages change
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);
  
  // Show offline indicator when offline
  useEffect(() => {
    setShowOfflineIndicator(!isOnline || offlineMode);
  }, [isOnline, offlineMode]);
  
  // Handle mouse move
  const handleMouseMove = useCallback((event) => {
    if (!roomContainerRef.current) return;
    
    const rect = roomContainerRef.current.getBoundingClientRect();
    const x = (event.clientX - rect.left) / rect.width;
    const y = (event.clientY - rect.top) / rect.height;
    
    setCursorPosition({ x, y });
    updateCursorPosition({ x, y });
  }, [updateCursorPosition]);
  
  // Handle message submit
  const handleMessageSubmit = async (event) => {
    event.preventDefault();
    
    if (!messageText.trim()) return;
    
    try {
      await sendMessage(messageText);
      setMessageText('');
      
      // Focus input after sending
      if (messageInputRef.current) {
        messageInputRef.current.focus();
      }
    } catch (err) {
      console.error('Error sending message:', err);
      setError(err.message || 'Failed to send message');
    }
  };
  
  // Handle leave room
  const handleLeaveRoom = async () => {
    try {
      await leaveRoom();
      
      if (onLeave) {
        onLeave();
      }
    } catch (err) {
      console.error('Error leaving room:', err);
      setError(err.message || 'Failed to leave room');
    }
  };
  
  // Handle sync
  const handleSync = async () => {
    try {
      await syncPendingChanges();
    } catch (err) {
      console.error('Error syncing changes:', err);
      setError(err.message || 'Failed to sync changes');
    }
  };
  
  // Format timestamp
  const formatTimestamp = (timestamp) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };
  
  // Get user color
  const getUserColor = (userId) => {
    const user = users.find(u => u.id === userId);
    return user ? user.color : '#cccccc';
  };
  
  // Get user name
  const getUserName = (userId) => {
    if (userId === 'system') {
      return translate('collaboration.system', 'System');
    }
    
    const user = users.find(u => u.id === userId);
    return user ? user.name : translate('collaboration.unknownUser', 'Unknown User');
  };
  
  // Render loading state
  if (isLoading) {
    return (
      <div
        className={`flex items-center justify-center p-8 ${className}`}
        style={style}
      >
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }
  
  // Render error state
  if (error) {
    return (
      <div
        className={`p-8 ${className}`}
        style={style}
      >
        <div className="bg-error bg-opacity-10 text-error p-4 rounded-md">
          <h3 className="text-lg font-medium mb-2">
            {translate('common.error', 'Error')}
          </h3>
          <p>{error}</p>
          <button
            type="button"
            className="mt-4 px-4 py-2 bg-primary text-primaryContrast rounded-md hover:bg-primaryDark transition-colors duration-200"
            onClick={onLeave}
          >
            {translate('common.back', 'Back')}
          </button>
        </div>
      </div>
    );
  }
  
  // Render room
  return (
    <div
      ref={roomContainerRef}
      className={`flex flex-col h-full ${className}`}
      style={style}
      onMouseMove={handleMouseMove}
      data-testid="offline-collaboration-room"
    >
      {/* Header */}
      <div className="bg-background border-b border-divider p-4 flex justify-between items-center">
        <div>
          <h2 className="text-lg font-medium text-textPrimary">
            {currentRoom?.name || translate('collaboration.room', 'Room')}
          </h2>
          <div className="text-sm text-textSecondary">
            {users.length} {users.length === 1
              ? translate('collaboration.participant', 'participant')
              : translate('collaboration.participants', 'participants')}
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          {/* Offline indicator */}
          {showOfflineIndicator && (
            <div className="flex items-center text-warning text-sm">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              <span>{translate('collaboration.offline', 'Offline')}</span>
            </div>
          )}
          
          {/* Pending changes indicator */}
          {pendingChanges.length > 0 && (
            <button
              type="button"
              className="flex items-center text-primary text-sm"
              onClick={handleSync}
              disabled={isSyncing || isOffline}
            >
              {isSyncing ? (
                <svg xmlns="http://www.w3.org/2000/svg" className="animate-spin h-4 w-4 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <circle cx="12" cy="12" r="10" strokeOpacity="0.25" />
                  <path d="M12 2a10 10 0 0110 10" />
                </svg>
              ) : (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
                </svg>
              )}
              <span>
                {isSyncing
                  ? translate('collaboration.syncing', 'Syncing...')
                  : translate('collaboration.pendingChanges', 'Sync {{count}} changes', { count: pendingChanges.length })}
              </span>
            </button>
          )}
          
          {/* Last sync time */}
          {lastSyncTime && (
            <div className="text-xs text-textSecondary">
              {translate('collaboration.lastSynced', 'Last synced: {{time}}', {
                time: formatTimestamp(lastSyncTime)
              })}
            </div>
          )}
          
          {/* User list toggle */}
          <button
            type="button"
            className="p-2 text-textSecondary hover:text-textPrimary rounded-full"
            onClick={() => setShowUserList(!showUserList)}
            aria-label={translate('collaboration.toggleUserList', 'Toggle user list')}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v1h8v-1zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-1a3 3 0 00-3-3h-2a3 3 0 00-3 3v1h8z" />
            </svg>
          </button>
          
          {/* Leave room button */}
          <button
            type="button"
            className="p-2 text-textSecondary hover:text-error rounded-full"
            onClick={handleLeaveRoom}
            aria-label={translate('collaboration.leaveRoom', 'Leave room')}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M3 3a1 1 0 00-1 1v12a1 1 0 001 1h12a1 1 0 001-1V7.414l-5.707-5.707A1 1 0 009.586 2H3zm9 2.414L16.586 9H12V5.414z" clipRule="evenodd" />
              <path d="M11 14.414V17h2.586l4.707-4.707-2.586-2.586L11 14.414z" />
            </svg>
          </button>
        </div>
      </div>
      
      {/* Main content */}
      <div className="flex-1 flex overflow-hidden">
        {/* Messages */}
        <div className="flex-1 flex flex-col overflow-hidden">
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {messages.map(message => (
              <div
                key={message.id}
                className={`flex ${message.userId === 'system' ? 'justify-center' : 'items-start'}`}
              >
                {message.userId !== 'system' && (
                  <div
                    className="w-8 h-8 rounded-full flex items-center justify-center mr-2 flex-shrink-0"
                    style={{ backgroundColor: getUserColor(message.userId) }}
                  >
                    <span className="text-white font-medium">
                      {getUserName(message.userId).charAt(0).toUpperCase()}
                    </span>
                  </div>
                )}
                
                <div className={`${message.userId === 'system' ? 'bg-background text-textSecondary text-sm py-1 px-3 rounded-md' : 'flex-1'}`}>
                  {message.userId !== 'system' && (
                    <div className="flex justify-between items-baseline">
                      <span className="font-medium text-textPrimary">
                        {getUserName(message.userId)}
                      </span>
                      <span className="text-xs text-textSecondary ml-2">
                        {formatTimestamp(message.timestamp)}
                      </span>
                    </div>
                  )}
                  
                  <div className={message.userId === 'system' ? '' : 'mt-1'}>
                    {message.text}
                  </div>
                  
                  {message.isOffline && (
                    <div className="text-xs text-warning mt-1">
                      {translate('collaboration.offlineMessage', 'Offline message')}
                    </div>
                  )}
                </div>
              </div>
            ))}
            <div ref={messagesEndRef} />
          </div>
          
          {/* Message input */}
          <div className="p-4 border-t border-divider">
            <form onSubmit={handleMessageSubmit}>
              <div className="flex">
                <input
                  ref={messageInputRef}
                  type="text"
                  className="flex-1 px-3 py-2 border border-divider rounded-l-md bg-background text-textPrimary focus:outline-none focus:ring-2 focus:ring-primary"
                  value={messageText}
                  onChange={(e) => setMessageText(e.target.value)}
                  placeholder={translate('collaboration.typeMessage', 'Type a message...')}
                />
                <button
                  type="submit"
                  className="px-4 py-2 bg-primary text-primaryContrast rounded-r-md hover:bg-primaryDark transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                  disabled={!messageText.trim()}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z" />
                  </svg>
                </button>
              </div>
            </form>
          </div>
        </div>
        
        {/* User list */}
        {showUserList && (
          <Animated
            animation="slideInRight"
            className="w-64 border-l border-divider bg-background overflow-y-auto"
          >
            <div className="p-4">
              <h3 className="text-sm font-medium text-textPrimary mb-4">
                {translate('collaboration.participants', 'Participants')}
              </h3>
              
              <div className="space-y-3">
                {users.map(user => (
                  <div key={user.id} className="flex items-center">
                    <div
                      className="w-8 h-8 rounded-full flex items-center justify-center mr-2 flex-shrink-0"
                      style={{ backgroundColor: user.color }}
                    >
                      <span className="text-white font-medium">
                        {user.name.charAt(0).toUpperCase()}
                      </span>
                    </div>
                    
                    <div className="flex-1">
                      <div className="font-medium text-textPrimary">
                        {user.name}
                      </div>
                      <div className="text-xs text-textSecondary">
                        {user.isLocal
                          ? translate('collaboration.you', 'You')
                          : translate('collaboration.online', 'Online')}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </Animated>
        )}
      </div>
      
      {/* Cursors */}
      {Object.entries(cursors).map(([userId, cursor]) => {
        if (userId === users.find(u => u.isLocal)?.id) return null;
        
        return (
          <div
            key={userId}
            className="absolute pointer-events-none"
            style={{
              left: `${cursor.position.x * 100}%`,
              top: `${cursor.position.y * 100}%`,
              transform: 'translate(-50%, -50%)'
            }}
          >
            <svg
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              style={{ color: getUserColor(userId) }}
            >
              <path d="M5 3L19 12L12 13L9 20L5 3Z" fill="currentColor" />
            </svg>
            <div
              className="absolute left-full ml-1 whitespace-nowrap px-2 py-1 rounded text-xs"
              style={{
                backgroundColor: getUserColor(userId),
                color: 'white'
              }}
            >
              {getUserName(userId)}
            </div>
          </div>
        );
      })}
    </div>
  );
};

OfflineCollaborationRoom.propTypes = {
  roomId: PropTypes.string.isRequired,
  onLeave: PropTypes.func,
  className: PropTypes.string,
  style: PropTypes.object
};

export default OfflineCollaborationRoom;
