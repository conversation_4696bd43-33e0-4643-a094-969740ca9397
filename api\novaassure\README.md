# NovaAssure (UCTF) - Universal Control Testing Framework

<div align="center">
  <img src="../../public/images/novaassure-logo.png" alt="NovaAssure Logo" width="200">
  <p><strong>Trust, Automated</strong></p>
</div>

NovaAssure is a comprehensive control testing framework that enables automated compliance testing and evidence collection. It is a core component of the NovaFuse Cyber-Safety Platform, providing organizations with a robust solution for managing compliance across multiple regulatory frameworks.

## Table of Contents

- [Overview](#overview)
- [Features](#features)
- [Architecture](#architecture)
- [Installation](#installation)
- [Configuration](#configuration)
- [API Endpoints](#api-endpoints)
- [Integration with Other NovaFuse Components](#integration-with-other-novafuse-components)
- [Testing](#testing)
- [Deployment](#deployment)
- [Contributing](#contributing)
- [License](#license)

## Overview

NovaAssure (UCTF - Universal Control Testing Framework) is designed to automate the compliance testing process, making it easier for organizations to maintain compliance with various regulatory frameworks such as SOC 2, GDPR, HIPAA, ISO 27001, and more. By providing a unified approach to control testing, evidence collection, and reporting, NovaAssure helps organizations reduce the time and effort required for compliance activities.

## Features

- **Control Management**: Create, update, and manage controls across multiple compliance frameworks
- **Test Plan Management**: Create and schedule test plans for controls
- **Test Execution**: Execute and track control tests with both manual and automated testing capabilities
- **Evidence Collection**: Collect and verify evidence for control tests from various sources
- **Blockchain Verification**: Anchor evidence to blockchain for immutable verification
- **Advanced Reporting**: Generate comprehensive compliance reports, test results reports, and evidence reports
- **Cross-Framework Mapping**: Map controls across different compliance frameworks
- **Automated Testing Engine**: Run automated tests for various control types
- **Real-time Monitoring**: Monitor control effectiveness in real-time
- **Workflow Orchestration**: Orchestrate compliance testing workflows
- **AI-powered Assistance**: Get AI-powered recommendations for compliance testing

## Architecture

NovaAssure follows a modular architecture with the following components:

- **API Layer**: RESTful API endpoints for interacting with the system
- **Service Layer**: Business logic for control management, test execution, evidence collection, and reporting
- **Data Layer**: MongoDB models for storing control, test plan, test execution, evidence, and report data
- **Integration Layer**: Integration with other NovaFuse components
- **UI Layer**: React-based UI components for managing compliance testing

### Directory Structure

```
api/novaassure/
├── controllers/       # API controllers
├── middleware/        # Express middleware
├── models/            # MongoDB models
├── routes/            # API routes
├── services/          # Business logic
├── templates/         # Report templates
├── tests/             # Unit and integration tests
│   ├── unit/          # Unit tests
│   └── integration/   # Integration tests
├── utils/             # Utility functions
├── index.js           # Main entry point
└── README.md          # Documentation
```

## Installation

### Prerequisites

- Node.js 16+
- MongoDB 5+
- Docker (optional)

### Local Installation

1. Clone the repository:

```bash
git clone https://github.com/novafuse/novafuse.git
cd novafuse
```

2. Install dependencies:

```bash
npm install
```

3. Copy the example environment file:

```bash
cp .env.example .env
```

4. Update the environment variables in the `.env` file.

5. Start the application:

```bash
npm start
```

### Docker Installation

1. Clone the repository:

```bash
git clone https://github.com/novafuse/novafuse.git
cd novafuse
```

2. Copy the example environment file:

```bash
cp .env.example .env
```

3. Update the environment variables in the `.env` file.

4. Build and start the Docker containers:

```bash
docker-compose up -d
```

## Configuration

NovaAssure can be configured using environment variables. See the `.env.example` file for a list of available configuration options.

### Key Configuration Options

- **Database**: Configure MongoDB connection
- **JWT**: Configure JWT authentication
- **Blockchain**: Configure blockchain provider for evidence verification
- **Component Integration**: Configure integration with other NovaFuse components
- **File Upload**: Configure file upload settings
- **Logging**: Configure logging settings
- **Email**: Configure email settings for report sharing

## API Endpoints

### Controls

- `GET /api/v1/novaassure/controls`: Get all controls
- `GET /api/v1/novaassure/controls/:id`: Get a specific control
- `POST /api/v1/novaassure/controls`: Create a new control
- `PUT /api/v1/novaassure/controls/:id`: Update a control
- `DELETE /api/v1/novaassure/controls/:id`: Delete a control
- `POST /api/v1/novaassure/controls/import`: Import controls from a file
- `GET /api/v1/novaassure/controls/export`: Export controls to a file
- `GET /api/v1/novaassure/controls/mapping`: Get control mapping across frameworks

### Test Plans

- `GET /api/v1/novaassure/test-plans`: Get all test plans
- `GET /api/v1/novaassure/test-plans/:id`: Get a specific test plan
- `POST /api/v1/novaassure/test-plans`: Create a new test plan
- `PUT /api/v1/novaassure/test-plans/:id`: Update a test plan
- `DELETE /api/v1/novaassure/test-plans/:id`: Delete a test plan
- `POST /api/v1/novaassure/test-plans/:id/schedule`: Schedule a test plan
- `POST /api/v1/novaassure/test-plans/:id/assign`: Assign a test plan to users
- `POST /api/v1/novaassure/test-plans/:id/clone`: Clone a test plan

### Test Execution

- `GET /api/v1/novaassure/test-execution`: Get all test executions
- `GET /api/v1/novaassure/test-execution/:id`: Get a specific test execution
- `POST /api/v1/novaassure/test-execution/start`: Start a test execution
- `POST /api/v1/novaassure/test-execution/:id/complete`: Complete a test execution
- `POST /api/v1/novaassure/test-execution/:id/cancel`: Cancel a test execution
- `POST /api/v1/novaassure/test-execution/:id/result`: Add a test result
- `POST /api/v1/novaassure/test-execution/schedule`: Schedule automated test execution
- `POST /api/v1/novaassure/test-execution/automated`: Run an automated test

### Evidence

- `GET /api/v1/novaassure/evidence`: Get all evidence
- `GET /api/v1/novaassure/evidence/:id`: Get a specific evidence
- `POST /api/v1/novaassure/evidence`: Create a new evidence
- `PUT /api/v1/novaassure/evidence/:id`: Update an evidence
- `DELETE /api/v1/novaassure/evidence/:id`: Delete an evidence
- `GET /api/v1/novaassure/evidence/:id/file`: Get the evidence file
- `GET /api/v1/novaassure/evidence/:id/verify`: Verify evidence integrity
- `POST /api/v1/novaassure/evidence/collect/api`: Collect evidence from an API
- `POST /api/v1/novaassure/evidence/collect/system`: Collect evidence from system logs or configuration

### Reports

- `GET /api/v1/novaassure/reports`: Get all reports
- `GET /api/v1/novaassure/reports/:id`: Get a specific report
- `POST /api/v1/novaassure/reports/generate/compliance`: Generate a compliance report
- `POST /api/v1/novaassure/reports/generate/test-results`: Generate a test results report
- `POST /api/v1/novaassure/reports/generate/evidence`: Generate an evidence report
- `POST /api/v1/novaassure/reports/generate/control-effectiveness`: Generate a control effectiveness report
- `GET /api/v1/novaassure/reports/:id/download`: Download a report
- `POST /api/v1/novaassure/reports/:id/share`: Share a report
- `POST /api/v1/novaassure/reports/:id/schedule`: Schedule a report

## Integration with Other NovaFuse Components

NovaAssure integrates with other NovaFuse components to provide a comprehensive compliance solution:

### NovaConnect

NovaAssure integrates with NovaConnect to collect evidence from APIs. This integration enables automated evidence collection from various systems and applications, reducing the manual effort required for compliance testing.

```javascript
// Example: Collect evidence from an API using NovaConnect
const evidence = await novaConnectIntegration.collectEvidence(
  'connector-id',
  '/api/endpoint',
  'GET',
  null,
  { 'Authorization': 'Bearer token' },
  'control-id',
  'test-execution-id'
);
```

### NovaPulse

NovaAssure integrates with NovaPulse for real-time monitoring of control effectiveness. This integration enables continuous monitoring of controls, providing early warning of potential compliance issues.

```javascript
// Example: Register a control for monitoring with NovaPulse
const monitoring = await novaPulseIntegration.registerControlMonitoring(
  'control-id',
  {
    frequency: 'daily',
    thresholds: {
      warning: 0.8,
      critical: 0.6
    }
  }
);
```

### NovaFlow

NovaAssure integrates with NovaFlow for workflow orchestration of compliance testing. This integration enables the automation of complex compliance testing workflows, reducing the manual effort required for compliance activities.

```javascript
// Example: Create a test execution workflow using NovaFlow
const workflow = await novaFlowIntegration.createTestExecutionWorkflow(
  'test-plan-id',
  'assignee-id'
);
```

### NovaAssistAI

NovaAssure integrates with NovaAssistAI for AI-powered assistance with compliance testing. This integration provides recommendations for control implementation, test procedures, and remediation actions.

```javascript
// Example: Get control recommendations from NovaAssistAI
const recommendations = await novaAssistAIIntegration.getControlRecommendations(
  'control-id'
);
```

### NovaSphere

NovaAssure integrates with NovaSphere for visualization of compliance status. This integration provides dashboards and visualizations for monitoring compliance status across multiple frameworks.

```javascript
// Example: Create a compliance dashboard in NovaSphere
const dashboard = await novaSphereIntegration.createComplianceDashboard(
  'soc2',
  'SOC 2 Compliance Dashboard',
  'Dashboard for monitoring SOC 2 compliance'
);
```

## Testing

NovaAssure includes a comprehensive test suite with unit tests and integration tests.

### Running Tests

```bash
# Run all tests
npm test

# Run unit tests
npm run test:unit

# Run integration tests
npm run test:integration

# Run tests with coverage
npm run test:coverage
```

### Test Coverage

NovaAssure aims for 85% test coverage, with 95% unit test coverage for compliance logic.

## Deployment

NovaAssure can be deployed using Docker and Docker Compose.

### Docker Deployment

1. Build the Docker image:

```bash
docker build -t novafuse/novaassure .
```

2. Run the Docker container:

```bash
docker run -p 3000:3000 --env-file .env novafuse/novaassure
```

### Docker Compose Deployment

1. Start the Docker Compose stack:

```bash
docker-compose up -d
```

2. Stop the Docker Compose stack:

```bash
docker-compose down
```

## Contributing

We welcome contributions to NovaAssure! Please see the [CONTRIBUTING.md](../../CONTRIBUTING.md) file for details.

## License

Copyright © 2023-2025 NovaFuse. All rights reserved.
