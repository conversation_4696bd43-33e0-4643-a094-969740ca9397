import { useState, useEffect } from "react";
import Head from "next/head";
import { useRouter } from "next/router";

export default function Subscription() {
  const router = useRouter();
  const [selectedPlan, setSelectedPlan] = useState(null);
  const [billingCycle, setBillingCycle] = useState("monthly");
  const [loading, setLoading] = useState(false);
  const [currentPlan, setCurrentPlan] = useState(null);
  const [usageData, setUsageData] = useState(null);

  useEffect(() => {
    // In a real implementation, this would fetch from an API
    setTimeout(() => {
      // Mock current subscription data
      setCurrentPlan({
        name: "Professional",
        price: 499,
        billingCycle: "monthly",
        startDate: "2025-01-01",
        nextBillingDate: "2025-02-01",
        status: "active"
      });
      
      // Mock usage data
      setUsageData({
        apiCalls: {
          used: 35420,
          limit: 50000,
          percentage: 70.84
        },
        endpoints: {
          used: 8,
          limit: 15,
          percentage: 53.33
        }
      });
    }, 1000);
  }, []);

  const plans = [
    {
      id: "basic",
      name: "Basic",
      description: "Essential API access for small teams",
      monthlyPrice: 199,
      yearlyPrice: 1990,
      features: [
        "10,000 API calls per month",
        "5 API endpoints",
        "Basic support (email only)",
        "Standard SLA (99% uptime)",
        "1 developer account"
      ],
      limits: {
        apiCalls: 10000,
        endpoints: 5
      }
    },
    {
      id: "professional",
      name: "Professional",
      description: "Advanced features for growing businesses",
      monthlyPrice: 499,
      yearlyPrice: 4990,
      popular: true,
      features: [
        "50,000 API calls per month",
        "15 API endpoints",
        "Priority support (email + chat)",
        "Enhanced SLA (99.5% uptime)",
        "5 developer accounts",
        "Advanced analytics"
      ],
      limits: {
        apiCalls: 50000,
        endpoints: 15
      }
    },
    {
      id: "enterprise",
      name: "Enterprise",
      description: "Maximum power for large organizations",
      monthlyPrice: 999,
      yearlyPrice: 9990,
      features: [
        "Unlimited API calls",
        "Unlimited API endpoints",
        "Premium support (24/7 phone + email)",
        "Premium SLA (99.9% uptime)",
        "Unlimited developer accounts",
        "Custom integrations",
        "Dedicated account manager"
      ],
      limits: {
        apiCalls: "Unlimited",
        endpoints: "Unlimited"
      }
    }
  ];

  const handlePlanSelect = (planId) => {
    setSelectedPlan(planId);
  };

  const handleSubscribe = () => {
    if (!selectedPlan) return;
    
    setLoading(true);
    
    // In a real implementation, this would call an API to process the subscription
    setTimeout(() => {
      // Redirect to checkout or confirmation page
      router.push("/subscription-checkout");
    }, 1000);
  };

  const handleUpgrade = () => {
    // Scroll to plans section
    document.getElementById("plans-section").scrollIntoView({ behavior: "smooth" });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Head>
        <title>NovaFuse Subscription Management</title>
        <meta name="description" content="Manage your NovaFuse API Superstore subscription" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 py-6 sm:px-6 lg:px-8">
          <h1 className="text-3xl font-bold text-gray-900">Subscription Management</h1>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Current Subscription */}
        {currentPlan && (
          <div className="bg-white rounded-lg shadow mb-8">
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-xl font-semibold">Current Subscription</h2>
            </div>
            <div className="p-6">
              <div className="flex flex-col md:flex-row md:justify-between md:items-center">
                <div>
                  <div className="flex items-center">
                    <h3 className="text-2xl font-bold text-gray-900">{currentPlan.name} Plan</h3>
                    <span className="ml-3 px-3 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                      {currentPlan.status}
                    </span>
                  </div>
                  <p className="text-gray-500 mt-1">
                    ${currentPlan.price}/{currentPlan.billingCycle === "monthly" ? "month" : "year"}
                  </p>
                  <div className="mt-2 text-sm text-gray-500">
                    <p>Started on: {currentPlan.startDate}</p>
                    <p>Next billing date: {currentPlan.nextBillingDate}</p>
                  </div>
                </div>
                <div className="mt-4 md:mt-0">
                  <button 
                    onClick={handleUpgrade}
                    className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
                  >
                    Upgrade Plan
                  </button>
                </div>
              </div>
              
              {usageData && (
                <div className="mt-6">
                  <h4 className="font-medium text-gray-700 mb-3">Current Usage</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <div className="flex justify-between mb-1">
                        <span className="text-sm font-medium text-gray-500">API Calls</span>
                        <span className="text-sm font-medium text-gray-700">
                          {usageData.apiCalls.used.toLocaleString()} / {typeof usageData.apiCalls.limit === "number" ? usageData.apiCalls.limit.toLocaleString() : usageData.apiCalls.limit}
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2.5">
                        <div 
                          className="bg-blue-600 h-2.5 rounded-full" 
                          style={{ width: `${Math.min(usageData.apiCalls.percentage, 100)}%` }}
                        ></div>
                      </div>
                      <p className="text-xs text-gray-500 mt-1">
                        {usageData.apiCalls.percentage.toFixed(1)}% used
                      </p>
                    </div>
                    <div>
                      <div className="flex justify-between mb-1">
                        <span className="text-sm font-medium text-gray-500">API Endpoints</span>
                        <span className="text-sm font-medium text-gray-700">
                          {usageData.endpoints.used} / {typeof usageData.endpoints.limit === "number" ? usageData.endpoints.limit : usageData.endpoints.limit}
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2.5">
                        <div 
                          className="bg-blue-600 h-2.5 rounded-full" 
                          style={{ width: `${Math.min(usageData.endpoints.percentage, 100)}%` }}
                        ></div>
                      </div>
                      <p className="text-xs text-gray-500 mt-1">
                        {usageData.endpoints.percentage.toFixed(1)}% used
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
        
        {/* Subscription Plans */}
        <div id="plans-section" className="mb-8">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold text-gray-900">Choose Your Plan</h2>
            <p className="mt-4 text-lg text-gray-600 max-w-2xl mx-auto">
              Select the plan that best fits your needs. All plans include access to our API Superstore and core features.
            </p>
            
            {/* Billing Toggle */}
            <div className="flex items-center justify-center mt-6">
              <span className={`mr-3 ${billingCycle === "monthly" ? "font-semibold text-gray-900" : "text-gray-500"}`}>
                Monthly
              </span>
              <button 
                onClick={() => setBillingCycle(billingCycle === "monthly" ? "yearly" : "monthly")}
                className="relative inline-flex h-6 w-11 items-center rounded-full bg-gray-200"
              >
                <span className="sr-only">Toggle billing cycle</span>
                <span 
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition ${
                    billingCycle === "yearly" ? "translate-x-6" : "translate-x-1"
                  }`}
                />
              </button>
              <span className={`ml-3 ${billingCycle === "yearly" ? "font-semibold text-gray-900" : "text-gray-500"}`}>
                Yearly <span className="text-green-600 font-medium">(Save 16%)</span>
              </span>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {plans.map((plan) => (
              <div 
                key={plan.id}
                className={`bg-white rounded-lg shadow overflow-hidden ${
                  plan.popular ? "ring-2 ring-blue-500" : ""
                } ${
                  selectedPlan === plan.id ? "ring-2 ring-blue-500" : ""
                }`}
              >
                {plan.popular && (
                  <div className="bg-blue-500 text-white text-center py-1 text-sm font-medium">
                    Most Popular
                  </div>
                )}
                <div className="p-6">
                  <h3 className="text-xl font-bold text-gray-900">{plan.name}</h3>
                  <p className="text-gray-500 mt-1">{plan.description}</p>
                  <div className="mt-4">
                    <span className="text-4xl font-bold text-gray-900">
                      ${billingCycle === "monthly" ? plan.monthlyPrice : plan.yearlyPrice}
                    </span>
                    <span className="text-gray-500">
                      /{billingCycle === "monthly" ? "month" : "year"}
                    </span>
                  </div>
                  
                  <ul className="mt-6 space-y-4">
                    {plan.features.map((feature, index) => (
                      <li key={index} className="flex items-start">
                        <svg className="h-5 w-5 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                        <span className="text-gray-600">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  
                  <button
                    onClick={() => handlePlanSelect(plan.id)}
                    className={`mt-8 w-full py-2 px-4 rounded font-medium ${
                      selectedPlan === plan.id
                        ? "bg-blue-600 text-white hover:bg-blue-700"
                        : "bg-gray-100 text-gray-800 hover:bg-gray-200"
                    }`}
                  >
                    {currentPlan && currentPlan.name === plan.name
                      ? "Current Plan"
                      : "Select Plan"}
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
        
        {/* Action Buttons */}
        {selectedPlan && (
          <div className="bg-gray-100 p-6 rounded-lg">
            <div className="flex flex-col md:flex-row md:justify-between md:items-center">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">
                  Selected Plan: {plans.find(p => p.id === selectedPlan).name}
                </h3>
                <p className="text-gray-600">
                  ${billingCycle === "monthly" 
                    ? plans.find(p => p.id === selectedPlan).monthlyPrice 
                    : plans.find(p => p.id === selectedPlan).yearlyPrice
                  }/{billingCycle === "monthly" ? "month" : "year"}
                </p>
              </div>
              <div className="mt-4 md:mt-0">
                <button
                  onClick={handleSubscribe}
                  disabled={loading}
                  className={`bg-blue-600 text-white px-6 py-2 rounded font-medium ${
                    loading ? "opacity-50 cursor-not-allowed" : "hover:bg-blue-700"
                  }`}
                >
                  {loading ? "Processing..." : "Continue to Checkout"}
                </button>
              </div>
            </div>
          </div>
        )}
        
        {/* FAQ Section */}
        <div className="mt-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Frequently Asked Questions</h2>
          
          <div className="bg-white rounded-lg shadow divide-y divide-gray-200">
            <div className="p-6">
              <h3 className="text-lg font-medium text-gray-900">What happens if I exceed my API call limit?</h3>
              <p className="mt-2 text-gray-600">
                If you exceed your monthly API call limit, you'll be charged for overage at a rate of $0.01 per API call. You can set up usage alerts to notify you when you're approaching your limit.
              </p>
            </div>
            <div className="p-6">
              <h3 className="text-lg font-medium text-gray-900">Can I change plans at any time?</h3>
              <p className="mt-2 text-gray-600">
                Yes, you can upgrade your plan at any time and the changes will take effect immediately. If you downgrade, the changes will take effect at the start of your next billing cycle.
              </p>
            </div>
            <div className="p-6">
              <h3 className="text-lg font-medium text-gray-900">What payment methods do you accept?</h3>
              <p className="mt-2 text-gray-600">
                We accept all major credit cards (Visa, Mastercard, American Express) and PayPal. For Enterprise plans, we also offer invoicing with net-30 terms.
              </p>
            </div>
            <div className="p-6">
              <h3 className="text-lg font-medium text-gray-900">Is there a free trial available?</h3>
              <p className="mt-2 text-gray-600">
                Yes, we offer a 14-day free trial of the Professional plan. No credit card is required to start your trial. You can upgrade or cancel at any time during your trial period.
              </p>
            </div>
          </div>
        </div>
      </main>

      <footer className="bg-gray-800 text-white py-12 mt-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mt-8 pt-8 border-t border-gray-700 text-center text-gray-400">
            <p>&copy; {new Date().getFullYear()} NovaFuse. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
