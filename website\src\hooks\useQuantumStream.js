import { useState, useEffect, useRef, useCallback } from 'react';
import { useNovaCore } from '@novafuse/nova-core';
import { v4 as uuidv4 } from 'uuid';

// Default configuration
const DEFAULT_CONFIG = {
  streamId: 'quantum-stream',
  autoConnect: true,
  reconnect: true,
  reconnectDelay: 5000,
  maxReconnectAttempts: 5,
  throttle: 16, // ~60fps by default
  maxQueueSize: 100,
  debug: process.env.NODE_ENV === 'development',
};

/**
 * Custom hook for managing quantum data streaming
 * @param {Object} config - Configuration options
 * @param {string} [config.streamId] - Unique identifier for the stream
 * @param {boolean} [config.autoConnect] - Whether to connect automatically
 * @param {boolean} [config.reconnect] - Whether to automatically reconnect
 * @param {number} [config.reconnectDelay] - Delay between reconnection attempts (ms)
 * @param {number} [config.maxReconnectAttempts] - Maximum number of reconnection attempts
 * @param {number} [config.throttle] - Minimum time between updates (ms)
 * @param {number} [config.maxQueueSize] - Maximum queue size before dropping updates
 * @param {Object} [config.filters] - Filters for the quantum data
 * @param {boolean} [config.debug] - Enable debug logging
 * @returns {Object} Streaming state and controls
 */
export const useQuantumStream = (config = {}) => {
  const { novaConnect, novaTrack } = useNovaCore();
  const [state, setState] = useState({
    isConnected: false,
    isConnecting: false,
    error: null,
    lastUpdate: null,
    stats: {
      bytesReceived: 0,
      messagesReceived: 0,
      updateRate: 0,
      latency: 0,
      jitter: 0,
      dropped: 0,
    },
  });

  const configRef = useRef({ ...DEFAULT_CONFIG, ...config });
  const reconnectAttempts = useRef(0);
  const lastMessageTime = useRef(0);
  const messageQueue = useRef([]);
  const statsInterval = useRef(null);
  const reconnectTimeout = useRef(null);
  const sessionId = useRef(uuidv4());
  const updateStats = useRef(() => {});

  // Update config when it changes
  useEffect(() => {
    configRef.current = { ...DEFAULT_CONFIG, ...config };
  }, [config]);

  // Process queued messages with throttling
  const processQueue = useCallback(() => {
    const now = Date.now();
    const { throttle } = configRef.current;
    
    // Process message if throttle time has passed
    if (now - lastMessageTime.current >= throttle && messageQueue.current.length > 0) {
      const message = messageQueue.current.shift();
      lastMessageTime.current = now;
      
      // Update stats
      updateStats.current({
        messagesReceived: prev => prev + 1,
        bytesReceived: prev => prev + (message.size || 0),
        lastUpdate: now,
      });

      // Process the message
      try {
        const decoded = decodeQuantumMessage(message);
        // Dispatch to NovaCore store
        novaConnect.dispatch('QUANTUM_UPDATE', {
          streamId: configRef.current.streamId,
          payload: decoded,
          metadata: {
            timestamp: now,
            sequence: state.stats.messagesReceived + 1,
          },
        });
      } catch (error) {
        console.error('Error processing quantum message:', error);
        updateStats.current({
          error: error.message,
        });
      }
    }

    // Continue processing queue if not empty
    if (messageQueue.current.length > 0) {
      requestAnimationFrame(processQueue);
    }
  }, [novaConnect]);

  // Handle incoming messages
  const handleMessage = useCallback((message) => {
    const { maxQueueSize } = configRef.current;
    
    // Drop message if queue is full
    if (messageQueue.current.length >= maxQueueSize) {
      updateStats.current({
        dropped: prev => prev + 1,
      });
      return;
    }
    
    // Add to queue and process
    messageQueue.current.push(message);
    if (messageQueue.current.length === 1) {
      requestAnimationFrame(processQueue);
    }
  }, [processQueue]);

  // Connect to the quantum stream
  const connect = useCallback(async () => {
    const { streamId, debug } = configRef.current;
    
    if (state.isConnected || state.isConnecting) {
      if (debug) console.log(`[QuantumStream] Already connected/connecting to ${streamId}`);
      return;
    }

    try {
      if (debug) console.log(`[QuantumStream] Connecting to ${streamId}...`);
      setState(prev => ({ ...prev, isConnecting: true, error: null }));

      // Initialize WebSocket connection through NovaConnect
      const socket = await novaConnect.createSocket({
        channel: `quantum:${streamId}`,
        onMessage: handleMessage,
        onError: (error) => {
          console.error(`[QuantumStream] Error on ${streamId}:`, error);
          setState(prev => ({
            ...prev,
            error: error.message || 'Connection error',
            isConnected: false,
            isConnecting: false,
          }));
          
          // Track the error
          novaTrack('quantum_stream_error', {
            streamId,
            error: error.message,
            timestamp: Date.now(),
            sessionId: sessionId.current,
            type: 'connection',
          });
        },
        onClose: () => {
          if (debug) console.log(`[QuantumStream] Disconnected from ${streamId}`);
          setState(prev => ({
            ...prev,
            isConnected: false,
            isConnecting: false,
          }));
        },
      });

      // Connection successful
      reconnectAttempts.current = 0;
      setState(prev => ({
        ...prev,
        isConnected: true,
        isConnecting: false,
        error: null,
      }));

      // Track successful connection
      novaTrack('quantum_stream_connected', {
        streamId,
        timestamp: Date.now(),
        sessionId: sessionId.current,
      });

      return socket;
    } catch (error) {
      console.error(`[QuantumStream] Failed to connect to ${streamId}:`, error);
      setState(prev => ({
        ...prev,
        isConnected: false,
        isConnecting: false,
        error: error.message,
      }));
      
      // Track connection failure
      novaTrack('quantum_stream_connect_failed', {
        streamId,
        error: error.message,
        timestamp: Date.now(),
        sessionId: sessionId.current,
      });
      
      throw error;
    }
  }, [handleMessage, novaConnect, novaTrack, state.isConnected, state.isConnecting]);

  // Disconnect from the stream
  const disconnect = useCallback(async () => {
    const { streamId, debug } = configRef.current;
    
    if (!state.isConnected) {
      if (debug) console.log(`[QuantumStream] Not connected to ${streamId}`);
      return;
    }

    try {
      if (debug) console.log(`[QuantumStream] Disconnecting from ${streamId}...`);
      
      // Close the socket through NovaConnect
      await novaConnect.closeSocket(`quantum:${streamId}`);
      
      // Clear any pending reconnection
      if (reconnectTimeout.current) {
        clearTimeout(reconnectTimeout.current);
        reconnectTimeout.current = null;
      }
      
      // Clear message queue
      messageQueue.current = [];
      
      setState(prev => ({
        ...prev,
        isConnected: false,
        isConnecting: false,
      }));
      
      // Track disconnection
      novaTrack('quantum_stream_disconnected', {
        streamId,
        timestamp: Date.now(),
        sessionId: sessionId.current,
        stats: state.stats,
      });
      
    } catch (error) {
      console.error(`[QuantumStream] Error disconnecting from ${streamId}:`, error);
      setState(prev => ({
        ...prev,
        error: error.message,
      }));
    }
  }, [novaConnect, novaTrack, state.isConnected, state.stats]);

  // Handle reconnection logic
  const handleReconnect = useCallback(() => {
    const { reconnect, reconnectDelay, maxReconnectAttempts, debug } = configRef.current;
    
    if (!reconnect || reconnectAttempts.current >= maxReconnectAttempts) {
      if (debug) {
        console.log(`[QuantumStream] Max reconnection attempts (${maxReconnectAttempts}) reached`);
      }
      return;
    }
    
    reconnectAttempts.current += 1;
    const delay = reconnectDelay * Math.pow(2, reconnectAttempts.current - 1);
    
    if (debug) {
      console.log(`[QuantumStream] Reconnecting in ${delay}ms (attempt ${reconnectAttempts.current}/${maxReconnectAttempts})`);
    }
    
    reconnectTimeout.current = setTimeout(() => {
      connect().catch(() => {
        // If connection fails, schedule next reconnection attempt
        handleReconnect();
      });
    }, delay);
  }, [connect]);

  // Initialize stats updater
  useEffect(() => {
    updateStats.current = (updates) => {
      setState(prev => ({
        ...prev,
        stats: {
          ...prev.stats,
          ...Object.entries(updates).reduce((acc, [key, value]) => ({
            ...acc,
            [key]: typeof value === 'function' ? value(prev.stats[key]) : value,
          }), {}),
        },
      }));
    };
    
    return () => {
      updateStats.current = () => {};
    };
  }, []);

  // Auto-connect on mount if enabled
  useEffect(() => {
    const { autoConnect } = configRef.current;
    
    if (autoConnect) {
      connect().catch(() => {
        handleReconnect();
      });
    }
    
    return () => {
      disconnect();
    };
  }, [connect, disconnect, handleReconnect]);

  // Update stats periodically
  useEffect(() => {
    const interval = setInterval(() => {
      const now = Date.now();
      const timeWindow = 1000; // 1 second
      
      setState(prev => {
        const messagesInWindow = prev.stats.messagesReceived - (prev.stats.lastMessagesCount || 0);
        const updateRate = Math.round((messagesInWindow * 1000) / timeWindow);
        
        return {
          ...prev,
          stats: {
            ...prev.stats,
            updateRate,
            lastMessagesCount: prev.stats.messagesReceived,
          },
        };
      });
    }, 1000);
    
    return () => clearInterval(interval);
  }, []);

  // Handle reconnection on error
  useEffect(() => {
    if (state.error && !state.isConnected && configRef.current.reconnect) {
      handleReconnect();
    }
    
    return () => {
      if (reconnectTimeout.current) {
        clearTimeout(reconnectTimeout.current);
      }
    };
  }, [state.error, state.isConnected, handleReconnect]);

  // Clean up on unmount
  useEffect(() => {
    return () => {
      if (reconnectTimeout.current) {
        clearTimeout(reconnectTimeout.current);
      }
      if (statsInterval.current) {
        clearInterval(statsInterval.current);
      }
      disconnect();
    };
  }, [disconnect]);

  return {
    // State
    isConnected: state.isConnected,
    isConnecting: state.isConnecting,
    error: state.error,
    stats: state.stats,
    lastUpdate: state.lastUpdate,
    
    // Actions
    connect,
    disconnect,
    send: (data) => {
      if (!state.isConnected) {
        throw new Error('Not connected to quantum stream');
      }
      return novaConnect.send(`quantum:${configRef.current.streamId}`, data);
    },
    
    // Metadata
    streamId: configRef.current.streamId,
    sessionId: sessionId.current,
  };
};

// Helper function to decode quantum messages
function decodeQuantumMessage(message) {
  try {
    // In a real implementation, this would decode the binary message
    // For now, we'll assume it's already a parsed object
    return typeof message === 'string' ? JSON.parse(message) : message;
  } catch (error) {
    console.error('Failed to decode quantum message:', error);
    throw new Error('Invalid quantum message format');
  }
}

export default useQuantumStream;
