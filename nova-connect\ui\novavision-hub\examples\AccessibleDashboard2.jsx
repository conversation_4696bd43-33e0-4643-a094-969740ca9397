/**
 * Accessible Dashboard Example 2
 * 
 * This example demonstrates enhanced accessibility features.
 */

import React, { useState } from 'react';
import PropTypes from 'prop-types';
import {
  DashboardCard,
  AccessibilityMenu,
  AccessibilitySettings,
  ScreenReaderText,
  KeyboardShortcuts,
  FocusTrap,
  TabPanel,
  SkipLink,
  ThemeSelector
} from '../components';
import { ThemeProvider } from '../theme';
import { PreferencesProvider } from '../preferences';
import { OfflineProvider } from '../offline';
import { AnimationProvider } from '../animation';
import { I18nProvider } from '../i18n';
import { AccessibilityProvider } from '../accessibility';
import { Translation } from '../components';

/**
 * Accessible Dashboard Content component
 * 
 * @param {Object} props - Component props
 * @param {Object} props.novaConnect - NovaConnect instance
 * @param {Object} props.novaShield - NovaShield instance
 * @param {Object} props.novaTrack - NovaTrack instance
 * @param {boolean} [props.enableLogging=false] - Whether to enable logging
 * @returns {React.ReactElement} Accessible Dashboard Content component
 */
const AccessibleDashboardContent = ({
  novaConnect,
  novaShield,
  novaTrack,
  enableLogging = false
}) => {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [showKeyboardShortcuts, setShowKeyboardShortcuts] = useState(false);
  const [showAccessibilitySettings, setShowAccessibilitySettings] = useState(false);
  
  // Keyboard shortcuts
  const shortcuts = {
    general: [
      {
        label: 'Toggle Keyboard Shortcuts',
        key: 'Shift+?',
        action: () => setShowKeyboardShortcuts(!showKeyboardShortcuts)
      },
      {
        label: 'Toggle Accessibility Settings',
        key: 'Shift+A',
        action: () => setShowAccessibilitySettings(!showAccessibilitySettings)
      },
      {
        label: 'Go to Dashboard Tab',
        key: 'Alt+1',
        action: () => setActiveTab('dashboard')
      },
      {
        label: 'Go to Settings Tab',
        key: 'Alt+2',
        action: () => setActiveTab('settings')
      },
      {
        label: 'Go to Help Tab',
        key: 'Alt+3',
        action: () => setActiveTab('help')
      }
    ],
    navigation: [
      {
        label: 'Skip to Main Content',
        key: 'Alt+S',
        action: () => {
          const mainContent = document.getElementById('main-content');
          if (mainContent) {
            mainContent.focus();
          }
        }
      },
      {
        label: 'Go to Next Tab',
        key: 'Ctrl+Tab',
        action: () => {
          const tabs = ['dashboard', 'settings', 'help'];
          const currentIndex = tabs.indexOf(activeTab);
          const nextIndex = (currentIndex + 1) % tabs.length;
          setActiveTab(tabs[nextIndex]);
        }
      },
      {
        label: 'Go to Previous Tab',
        key: 'Ctrl+Shift+Tab',
        action: () => {
          const tabs = ['dashboard', 'settings', 'help'];
          const currentIndex = tabs.indexOf(activeTab);
          const prevIndex = (currentIndex - 1 + tabs.length) % tabs.length;
          setActiveTab(tabs[prevIndex]);
        }
      }
    ]
  };
  
  // Tabs
  const tabs = [
    {
      id: 'dashboard',
      label: <Translation id="dashboard.dashboard" defaultValue="Dashboard" />,
      content: (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <DashboardCard
            title={<Translation id="dashboard.overview" defaultValue="Overview" />}
            subtitle={<Translation id="dashboard.summary" defaultValue="Summary" />}
          >
            <div className="p-4">
              <h3 className="text-lg font-medium text-textPrimary mb-4">
                <Translation id="accessibility.accessibilityFeatures" defaultValue="Accessibility Features" />
              </h3>
              <p className="text-textSecondary mb-4">
                <Translation
                  id="accessibility.featuresDescription"
                  defaultValue="This dashboard demonstrates enhanced accessibility features for users with disabilities."
                />
              </p>
              <ul className="list-disc list-inside space-y-2 text-textSecondary">
                <li>
                  <Translation id="accessibility.keyboardNavigation" defaultValue="Keyboard Navigation" />
                </li>
                <li>
                  <Translation id="accessibility.screenReaderSupport" defaultValue="Screen Reader Support" />
                </li>
                <li>
                  <Translation id="accessibility.highContrast" defaultValue="High Contrast Mode" />
                </li>
                <li>
                  <Translation id="accessibility.largeText" defaultValue="Large Text Mode" />
                </li>
                <li>
                  <Translation id="accessibility.reducedMotion" defaultValue="Reduced Motion Mode" />
                </li>
                <li>
                  <Translation id="accessibility.focusIndicators" defaultValue="Focus Indicators" />
                </li>
              </ul>
            </div>
          </DashboardCard>
          
          <DashboardCard
            title={<Translation id="accessibility.accessibilityControls" defaultValue="Accessibility Controls" />}
            subtitle={<Translation id="accessibility.quickAccess" defaultValue="Quick Access" />}
          >
            <div className="p-4">
              <div className="mb-6">
                <h3 className="text-lg font-medium text-textPrimary mb-4">
                  <Translation id="accessibility.accessibilityMenu" defaultValue="Accessibility Menu" />
                </h3>
                <p className="text-textSecondary mb-4">
                  <Translation
                    id="accessibility.menuDescription"
                    defaultValue="Use the accessibility menu to quickly toggle accessibility features."
                  />
                </p>
                <div className="flex space-x-4">
                  <div>
                    <h4 className="text-sm font-medium text-textPrimary mb-2">
                      <Translation id="accessibility.dropdown" defaultValue="Dropdown" />
                    </h4>
                    <AccessibilityMenu variant="dropdown" />
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-textPrimary mb-2">
                      <Translation id="accessibility.button" defaultValue="Button" />
                    </h4>
                    <AccessibilityMenu variant="button" />
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-textPrimary mb-2">
                      <Translation id="accessibility.icon" defaultValue="Icon" />
                    </h4>
                    <AccessibilityMenu variant="icon" />
                  </div>
                </div>
              </div>
              
              <div>
                <h3 className="text-lg font-medium text-textPrimary mb-4">
                  <Translation id="accessibility.keyboardShortcuts" defaultValue="Keyboard Shortcuts" />
                </h3>
                <p className="text-textSecondary mb-4">
                  <Translation
                    id="accessibility.shortcutsDescription"
                    defaultValue="Use keyboard shortcuts to navigate the dashboard quickly."
                  />
                </p>
                <button
                  type="button"
                  className="bg-primary text-primaryContrast px-4 py-2 rounded-md hover:bg-primaryDark transition-colors duration-200"
                  onClick={() => setShowKeyboardShortcuts(true)}
                >
                  <Translation id="accessibility.viewShortcuts" defaultValue="View Shortcuts" />
                </button>
              </div>
            </div>
          </DashboardCard>
          
          <DashboardCard
            title={<Translation id="accessibility.screenReaderText" defaultValue="Screen Reader Text" />}
            subtitle={<Translation id="accessibility.hiddenText" defaultValue="Hidden Text for Screen Readers" />}
            className="md:col-span-2"
          >
            <div className="p-4">
              <h3 className="text-lg font-medium text-textPrimary mb-4">
                <Translation id="accessibility.screenReaderExample" defaultValue="Screen Reader Example" />
              </h3>
              <p className="text-textSecondary mb-4">
                <Translation
                  id="accessibility.screenReaderDescription"
                  defaultValue="This example demonstrates how to provide additional context for screen readers."
                />
              </p>
              
              <div className="flex flex-col space-y-4">
                <div className="flex items-center">
                  <button
                    type="button"
                    className="bg-primary text-primaryContrast p-2 rounded-md hover:bg-primaryDark transition-colors duration-200"
                    aria-label="Add new item"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clipRule="evenodd" />
                    </svg>
                    <ScreenReaderText>Add new item to the dashboard</ScreenReaderText>
                  </button>
                  <span className="ml-4 text-textSecondary">
                    <Translation id="accessibility.iconButtonExample" defaultValue="Icon button with screen reader text" />
                  </span>
                </div>
                
                <div className="flex items-center">
                  <a
                    href="#"
                    className="text-primary hover:underline"
                    onClick={(e) => e.preventDefault()}
                  >
                    <Translation id="accessibility.viewDocumentation" defaultValue="View Documentation" />
                    <ScreenReaderText>Opens documentation in a new window</ScreenReaderText>
                  </a>
                  <span className="ml-4 text-textSecondary">
                    <Translation id="accessibility.linkExample" defaultValue="Link with additional screen reader context" />
                  </span>
                </div>
                
                <div className="flex items-center">
                  <div className="relative">
                    <div className="w-24 h-4 bg-success rounded-full"></div>
                    <ScreenReaderText>Progress: 100% complete</ScreenReaderText>
                  </div>
                  <span className="ml-4 text-textSecondary">
                    <Translation id="accessibility.progressExample" defaultValue="Progress bar with screen reader text" />
                  </span>
                </div>
              </div>
            </div>
          </DashboardCard>
        </div>
      )
    },
    {
      id: 'settings',
      label: <Translation id="dashboard.settings" defaultValue="Settings" />,
      content: (
        <FocusTrap>
          <DashboardCard
            title={<Translation id="accessibility.accessibilitySettings" defaultValue="Accessibility Settings" />}
            subtitle={<Translation id="accessibility.customizeExperience" defaultValue="Customize your experience" />}
          >
            <div className="p-4">
              <AccessibilitySettings
                variant="inline"
                showTitle={false}
              />
            </div>
          </DashboardCard>
        </FocusTrap>
      )
    },
    {
      id: 'help',
      label: <Translation id="common.help" defaultValue="Help" />,
      content: (
        <DashboardCard
          title={<Translation id="accessibility.accessibilityHelp" defaultValue="Accessibility Help" />}
          subtitle={<Translation id="accessibility.learnMore" defaultValue="Learn more about accessibility features" />}
        >
          <div className="p-4">
            <h3 className="text-lg font-medium text-textPrimary mb-4">
              <Translation id="accessibility.keyboardNavigation" defaultValue="Keyboard Navigation" />
            </h3>
            <p className="text-textSecondary mb-4">
              <Translation
                id="accessibility.keyboardNavigationHelp"
                defaultValue="You can navigate this dashboard using only your keyboard. Press Tab to move between interactive elements, Enter to activate buttons, and Escape to close dialogs."
              />
            </p>
            
            <h3 className="text-lg font-medium text-textPrimary mb-4">
              <Translation id="accessibility.screenReaderSupport" defaultValue="Screen Reader Support" />
            </h3>
            <p className="text-textSecondary mb-4">
              <Translation
                id="accessibility.screenReaderHelp"
                defaultValue="This dashboard is compatible with screen readers. All interactive elements have appropriate ARIA labels and additional context is provided where necessary."
              />
            </p>
            
            <h3 className="text-lg font-medium text-textPrimary mb-4">
              <Translation id="accessibility.highContrast" defaultValue="High Contrast Mode" />
            </h3>
            <p className="text-textSecondary mb-4">
              <Translation
                id="accessibility.highContrastHelp"
                defaultValue="High contrast mode increases the contrast between text and background colors to improve readability for users with visual impairments."
              />
            </p>
            
            <h3 className="text-lg font-medium text-textPrimary mb-4">
              <Translation id="accessibility.largeText" defaultValue="Large Text Mode" />
            </h3>
            <p className="text-textSecondary mb-4">
              <Translation
                id="accessibility.largeTextHelp"
                defaultValue="Large text mode increases the size of text to improve readability for users with visual impairments."
              />
            </p>
            
            <h3 className="text-lg font-medium text-textPrimary mb-4">
              <Translation id="accessibility.reducedMotion" defaultValue="Reduced Motion Mode" />
            </h3>
            <p className="text-textSecondary mb-4">
              <Translation
                id="accessibility.reducedMotionHelp"
                defaultValue="Reduced motion mode minimizes or eliminates animations and transitions to reduce discomfort for users with vestibular disorders or motion sensitivity."
              />
            </p>
            
            <h3 className="text-lg font-medium text-textPrimary mb-4">
              <Translation id="accessibility.focusIndicators" defaultValue="Focus Indicators" />
            </h3>
            <p className="text-textSecondary mb-4">
              <Translation
                id="accessibility.focusIndicatorsHelp"
                defaultValue="Focus indicators highlight the currently focused element to help keyboard users navigate the interface."
              />
            </p>
          </div>
        </DashboardCard>
      )
    }
  ];
  
  return (
    <div className="space-y-6">
      {/* Skip link for keyboard navigation */}
      <SkipLink targetId="main-content" />
      
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-textPrimary" id="dashboard-title">
          <Translation id="accessibility.accessibleDashboard" defaultValue="Accessible Dashboard" />
        </h1>
        
        <div className="flex items-center space-x-2">
          <AccessibilityMenu variant="dropdown" size="sm" />
          <ThemeSelector variant="dropdown" size="sm" />
        </div>
      </div>
      
      {/* Main content */}
      <main id="main-content" tabIndex="-1">
        <TabPanel
          tabs={tabs}
          defaultTab="dashboard"
          variant="pills"
          onTabChange={setActiveTab}
        />
      </main>
      
      {/* Keyboard shortcuts */}
      {showKeyboardShortcuts && (
        <KeyboardShortcuts
          shortcuts={shortcuts}
          showHelp={showKeyboardShortcuts}
          onShowHelpChange={setShowKeyboardShortcuts}
        />
      )}
      
      {/* Accessibility settings */}
      {showAccessibilitySettings && (
        <AccessibilitySettings
          variant="dialog"
          onClose={() => setShowAccessibilitySettings(false)}
        />
      )}
    </div>
  );
};

AccessibleDashboardContent.propTypes = {
  novaConnect: PropTypes.object,
  novaShield: PropTypes.object,
  novaTrack: PropTypes.object,
  enableLogging: PropTypes.bool
};

/**
 * Accessible Dashboard component
 * 
 * @param {Object} props - Component props
 * @param {Object} props.novaConnect - NovaConnect instance
 * @param {Object} props.novaShield - NovaShield instance
 * @param {Object} props.novaTrack - NovaTrack instance
 * @param {Object} [props.initialTheme] - Initial theme
 * @param {boolean} [props.enableSystemPreference=true] - Whether to enable system preference detection
 * @param {boolean} [props.enableLogging=false] - Whether to enable logging
 * @returns {React.ReactElement} Accessible Dashboard component
 */
const AccessibleDashboard2 = ({
  novaConnect,
  novaShield,
  novaTrack,
  initialTheme,
  enableSystemPreference = true,
  enableLogging = false
}) => {
  return (
    <ThemeProvider theme={initialTheme} enableSystemPreference={enableSystemPreference}>
      <I18nProvider detectBrowserLocale>
        <AccessibilityProvider>
          <PreferencesProvider>
            <OfflineProvider>
              <AnimationProvider>
                <AccessibleDashboardContent
                  novaConnect={novaConnect}
                  novaShield={novaShield}
                  novaTrack={novaTrack}
                  enableLogging={enableLogging}
                />
              </AnimationProvider>
            </OfflineProvider>
          </PreferencesProvider>
        </AccessibilityProvider>
      </I18nProvider>
    </ThemeProvider>
  );
};

AccessibleDashboard2.propTypes = {
  novaConnect: PropTypes.object,
  novaShield: PropTypes.object,
  novaTrack: PropTypes.object,
  initialTheme: PropTypes.object,
  enableSystemPreference: PropTypes.bool,
  enableLogging: PropTypes.bool
};

export default AccessibleDashboard2;
