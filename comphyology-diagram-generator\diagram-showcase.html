<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Comphyology Diagram Showcase</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        h1 {
            text-align: center;
            font-size: 3em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .subtitle {
            text-align: center;
            font-size: 1.2em;
            margin-bottom: 40px;
            opacity: 0.9;
        }
        
        .diagram-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }
        
        .diagram-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            cursor: pointer;
        }
        
        .diagram-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        
        .diagram-title {
            font-size: 1.5em;
            font-weight: bold;
            margin-bottom: 15px;
            color: #fff;
        }
        
        .diagram-description {
            font-size: 1em;
            line-height: 1.6;
            margin-bottom: 20px;
            opacity: 0.9;
        }
        
        .diagram-link {
            display: inline-block;
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 25px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .diagram-link:hover {
            background: linear-gradient(45deg, #ee5a24, #ff6b6b);
            transform: scale(1.05);
        }
        
        .status-badge {
            display: inline-block;
            background: #10b981;
            color: white;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
            margin-left: 10px;
        }
        
        .header-info {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        
        .tech-stack {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 15px;
            flex-wrap: wrap;
        }
        
        .tech-badge {
            background: rgba(255, 255, 255, 0.2);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9em;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌌 Comphyology Diagram Generator</h1>
        <p class="subtitle">Interactive Visualizations of Consciousness Field Theory & Patent Implementation</p>
        
        <div class="header-info">
            <p><strong>Status:</strong> <span class="status-badge">SVG CONVERTED</span></p>
            <p><strong>Framework:</strong> Interactive SVG + Animated Visualizations + Mathematical Precision</p>
            <p><strong>Purpose:</strong> Patent Documentation & Comphyology Theory Visualization</p>
            <p><strong>Converted Diagrams:</strong> 3/15 diagrams now have full SVG implementations with animations</p>

            <div class="tech-stack">
                <span class="tech-badge">Interactive SVG</span>
                <span class="tech-badge">CSS Animations</span>
                <span class="tech-badge">Mathematical Visualization</span>
                <span class="tech-badge">Consciousness Field Theory</span>
                <span class="tech-badge">Sacred Geometry</span>
            </div>
        </div>
        
        <div class="diagram-grid">
            <div class="diagram-card" onclick="openDiagram('trinity-equation.html')">
                <div class="diagram-title">🔺 Trinity Equation</div>
                <div class="diagram-description">
                    Interactive visualization of the fundamental Trinity equation (Ψᶜ = Ψᵖ ⊗ Ψᵗ ⊗ Ψᶠ) 
                    showing the relationship between Consciousness, Truth, and Financial coherence fields.
                </div>
                <a href="#" class="diagram-link" onclick="event.stopPropagation(); openDiagram('trinity-equation.html')">
                    View Trinity Equation →
                </a>
            </div>
            
            <div class="diagram-card" onclick="openDiagram('meta-field-schema.html')">
                <div class="diagram-title">🧠 Consciousness Field Schema</div>
                <div class="diagram-description">
                    Dynamic visualization of consciousness field dynamics, showing wave propagation,
                    meta-field nodes, and the ∂Ψ=0 boundary enforcement principle with animated interactions.
                </div>
                <a href="#" class="diagram-link" onclick="event.stopPropagation(); openDiagram('meta-field-schema.html')">
                    View Consciousness Field →
                </a>
            </div>

            <div class="diagram-card" onclick="openDiagram('finite-universe.html')">
                <div class="diagram-title">📐 Sacred Geometry & Finite Universe</div>
                <div class="diagram-description">
                    Interactive sacred geometry patterns including golden ratio spirals, Fibonacci sequences,
                    pentagonal symmetry, and Platonic solids within a finite universe paradigm.
                </div>
                <a href="#" class="diagram-link" onclick="event.stopPropagation(); openDiagram('finite-universe.html')">
                    View Sacred Geometry →
                </a>
            </div>
            
            <div class="diagram-card" onclick="openDiagram('quantum-coherence.html')">
                <div class="diagram-title">⚛️ Quantum Coherence</div>
                <div class="diagram-description">
                    Quantum coherence visualization showing entanglement patterns, superposition states, 
                    and quantum field interactions in consciousness processing.
                </div>
                <a href="#" class="diagram-link" onclick="event.stopPropagation(); openDiagram('quantum-coherence.html')">
                    View Quantum Coherence →
                </a>
            </div>
            
            <div class="diagram-card" onclick="openDiagram('financial-flow.html')">
                <div class="diagram-title">💰 Financial Flow</div>
                <div class="diagram-description">
                    Dynamic visualization of the 18/82 financial model, showing resource allocation, 
                    optimization flows, and economic coherence patterns.
                </div>
                <a href="#" class="diagram-link" onclick="event.stopPropagation(); openDiagram('financial-flow.html')">
                    View Financial Flow →
                </a>
            </div>
            
            <div class="diagram-card" onclick="openDiagram('system-architecture.html')">
                <div class="diagram-title">🏗️ System Architecture</div>
                <div class="diagram-description">
                    Complete system architecture diagram showing the integration of theoretical frameworks 
                    with practical implementation in the NovaCaia platform.
                </div>
                <a href="#" class="diagram-link" onclick="event.stopPropagation(); openDiagram('system-architecture.html')">
                    View System Architecture →
                </a>
            </div>
        </div>
        
        <div style="text-align: center; margin-top: 50px; padding: 30px;">
            <h2>🚀 Next.js Development Server</h2>
            <p>For the full interactive experience with live editing and hot reload:</p>
            <a href="http://localhost:3000" class="diagram-link" style="font-size: 1.2em; padding: 15px 30px;">
                Open Next.js App →
            </a>
        </div>
    </div>
    
    <script>
        function openDiagram(filename) {
            const url = `./diagram-viewer/${filename}`;
            window.open(url, '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');
        }
        
        // Add some interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.diagram-card');
            
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.background = 'rgba(255, 255, 255, 0.15)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.background = 'rgba(255, 255, 255, 0.1)';
                });
            });
        });
    </script>
</body>
</html>
