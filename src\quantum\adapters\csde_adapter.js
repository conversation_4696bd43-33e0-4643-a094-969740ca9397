/**
 * CSDE Adapter
 * 
 * This module provides an adapter for connecting the CSDE (Cyber-Safety Domain Engine)
 * with the Comphyological Tensor Core.
 */

const { createComphyologicalTensorCore } = require('../tensor');

/**
 * CSDEAdapter class
 * 
 * Provides an adapter for connecting the CSDE with the Comphyological Tensor Core.
 */
class CSDEAdapter {
  /**
   * Constructor
   * @param {Object} csdeEngine - CSDE engine instance
   * @param {Object} options - Configuration options
   */
  constructor(csdeEngine, options = {}) {
    this.options = {
      enableLogging: true,
      strictMode: false,
      useGPU: false,
      useDynamicWeighting: true,
      ...options
    };

    this.csdeEngine = csdeEngine;
    this.tensorCore = createComphyologicalTensorCore(this.options);
    this.lastResult = null;
    this.metrics = {
      processCount: 0,
      averageProcessingTime: 0,
      totalProcessingTime: 0,
      lastProcessingTime: 0,
      startTime: Date.now()
    };

    if (this.options.enableLogging) {
      console.log('CSDEAdapter initialized with options:', {
        strictMode: this.options.strictMode,
        useGPU: this.options.useGPU,
        useDynamicWeighting: this.options.useDynamicWeighting
      });
    }
  }

  /**
   * Process data through the CSDE and Comphyological Tensor Core
   * @param {Object} csdeData - CSDE data
   * @param {Object} csfeData - CSFE data
   * @param {Object} csmeData - CSME data
   * @returns {Object} - Processing result
   */
  processData(csdeData, csfeData, csmeData) {
    const startTime = Date.now();

    try {
      // Process data through CSDE engine if available
      let processedCsdeData = csdeData;
      if (this.csdeEngine && typeof this.csdeEngine.processData === 'function') {
        processedCsdeData = this.csdeEngine.processData(csdeData);
      }

      // Transform CSDE data
      const transformedCsdeData = this._transformCsdeData(processedCsdeData);

      // Process data through tensor core
      this.lastResult = this.tensorCore.processData(
        transformedCsdeData,
        csfeData,
        csmeData
      );

      // Update metrics
      this._updateMetrics(startTime);

      return this.lastResult;
    } catch (error) {
      if (this.options.enableLogging) {
        console.error('Error processing data through CSDEAdapter:', error);
      }

      if (this.options.strictMode) {
        throw error;
      }

      return {
        error: error.message,
        timestamp: Date.now(),
        processingTime: Date.now() - startTime
      };
    }
  }

  /**
   * Transform CSDE data to format expected by tensor core
   * @param {Object} csdeData - CSDE data
   * @returns {Object} - Transformed CSDE data
   * @private
   */
  _transformCsdeData(csdeData) {
    return {
      governance: csdeData.governanceScore || csdeData.governance || 0.5,
      dataQuality: csdeData.dataQuality || csdeData.data || 0.5,
      action: csdeData.recommendedAction || csdeData.action || 'allow',
      confidence: csdeData.confidenceScore || csdeData.confidence || 0.5
    };
  }

  /**
   * Update metrics
   * @param {number} startTime - Start time
   * @private
   */
  _updateMetrics(startTime) {
    const processingTime = Date.now() - startTime;

    // Update metrics
    this.metrics.processCount++;
    this.metrics.lastProcessingTime = processingTime;
    this.metrics.totalProcessingTime += processingTime;
    this.metrics.averageProcessingTime = this.metrics.totalProcessingTime / this.metrics.processCount;
  }

  /**
   * Get metrics
   * @returns {Object} - Metrics
   */
  getMetrics() {
    return {
      ...this.metrics,
      tensorCoreMetrics: this.tensorCore.getMetrics()
    };
  }

  /**
   * Reset metrics
   */
  resetMetrics() {
    this.metrics = {
      processCount: 0,
      averageProcessingTime: 0,
      totalProcessingTime: 0,
      lastProcessingTime: 0,
      startTime: Date.now()
    };

    this.tensorCore.resetMetrics();
  }

  /**
   * Get the last result
   * @returns {Object} - Last result
   */
  getLastResult() {
    return this.lastResult;
  }

  /**
   * Get the tensor core
   * @returns {Object} - Tensor core
   */
  getTensorCore() {
    return this.tensorCore;
  }
}

/**
 * Create a CSDE adapter
 * @param {Object} csdeEngine - CSDE engine instance
 * @param {Object} options - Configuration options
 * @returns {CSDEAdapter} - CSDE adapter instance
 */
function createCSDEAdapter(csdeEngine, options = {}) {
  return new CSDEAdapter(csdeEngine, options);
}

module.exports = {
  CSDEAdapter,
  createCSDEAdapter
};
