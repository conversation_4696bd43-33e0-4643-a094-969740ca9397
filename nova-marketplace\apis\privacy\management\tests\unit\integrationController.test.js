/**
 * Integration Controller Tests
 *
 * This file contains unit tests for the integration controller.
 */

const { Integration } = require('../../models');
const integrationController = require('../../controllers/integrationController');

// Mock the Integration model
jest.mock('../../models', () => {
  // Create a mock integration object
  const mockIntegration = {
    id: 'integration-123',
    name: 'Test Integration',
    type: 'crm',
    provider: 'TestProvider',
    status: 'active',
    capabilities: ['data-export', 'data-deletion', 'data-update', 'notifications'],
    config: {},
    metrics: {
      totalRequests: 100,
      successfulRequests: 90,
      averageResponseTime: 200
    },
    lastChecked: new Date(),
    save: jest.fn().mockResolvedValue(true),
    remove: jest.fn().mockResolvedValue(true)
  };

  // Create a mock constructor function
  const MockIntegration = jest.fn().mockImplementation((data) => {
    return {
      ...mockIntegration,
      ...data,
      save: jest.fn().mockResolvedValue(true),
      remove: jest.fn().mockResolvedValue(true)
    };
  });

  // Add static methods to the constructor
  MockIntegration.find = jest.fn().mockResolvedValue([mockIntegration]);
  MockIntegration.findOne = jest.fn().mockResolvedValue(mockIntegration);

  // Store the mock integration for test assertions
  MockIntegration.mockIntegration = mockIntegration;

  return {
    Integration: MockIntegration
  };
});

describe('Integration Controller', () => {
  // Mock Express request and response
  let req;
  let res;
  let next;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Setup request mock
    req = {
      params: {},
      query: {},
      body: {}
    };

    // Setup response mock
    res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    };

    // Setup next function mock
    next = jest.fn();
  });

  describe('getAllIntegrations', () => {
    it('should return all integrations', async () => {
      // Call the controller
      await integrationController.getAllIntegrations(req, res, next);

      // Assertions
      expect(Integration.find).toHaveBeenCalled();
      expect(res.json).toHaveBeenCalledWith({
        data: expect.any(Array)
      });
    });

    it('should handle errors', async () => {
      // Setup error
      const error = new Error('Database error');
      Integration.find.mockRejectedValueOnce(error);

      // Call the controller
      await integrationController.getAllIntegrations(req, res, next);

      // Assertions
      expect(next).toHaveBeenCalledWith(error);
    });
  });

  describe('getIntegrationById', () => {
    it('should return an integration by ID', async () => {
      // Setup request with ID parameter
      req.params.id = 'integration-123';

      // Call the controller
      await integrationController.getIntegrationById(req, res, next);

      // Assertions
      expect(Integration.findOne).toHaveBeenCalledWith({ id: 'integration-123' });
      expect(res.json).toHaveBeenCalledWith({
        data: expect.objectContaining({
          id: 'integration-123'
        })
      });
    });

    it('should handle NotFoundError', async () => {
      // Setup request with ID parameter
      req.params.id = 'non-existent';

      // Setup model to return null
      Integration.findOne.mockResolvedValueOnce(null);

      // Call the controller
      await integrationController.getIntegrationById(req, res, next);

      // Assertions
      expect(Integration.findOne).toHaveBeenCalledWith({ id: 'non-existent' });
      expect(next).toHaveBeenCalledWith(expect.objectContaining({
        name: 'NotFoundError',
        message: 'Integration not found'
      }));
    });

    it('should handle other errors', async () => {
      // Setup request with ID parameter
      req.params.id = 'integration-123';

      // Setup error
      const error = new Error('Database error');
      Integration.findOne.mockRejectedValueOnce(error);

      // Call the controller
      await integrationController.getIntegrationById(req, res, next);

      // Assertions
      expect(next).toHaveBeenCalledWith(error);
    });
  });

  describe('createIntegration', () => {
    it('should create a new integration', async () => {
      // Setup request body
      req.body = {
        id: 'new-integration',
        name: 'New Integration',
        type: 'crm',
        provider: 'NewProvider',
        capabilities: ['data-export']
      };

      // Setup model to return null for existing integration check
      Integration.findOne.mockResolvedValueOnce(null);

      // Call the controller
      await integrationController.createIntegration(req, res, next);

      // Assertions
      expect(Integration.findOne).toHaveBeenCalledWith({ id: 'new-integration' });
      expect(Integration).toHaveBeenCalledWith(req.body);
      expect(res.status).toHaveBeenCalledWith(201);
      expect(res.json).toHaveBeenCalledWith({
        data: expect.any(Object),
        message: 'Integration created successfully'
      });
    });

    it('should handle ConflictError when integration already exists', async () => {
      // Setup request body
      req.body = {
        id: 'existing-integration',
        name: 'Existing Integration'
      };

      // Setup model to return an existing integration
      const existingIntegration = {
        id: 'existing-integration',
        name: 'Existing Integration'
      };
      Integration.findOne.mockResolvedValueOnce(existingIntegration);

      // Call the controller
      await integrationController.createIntegration(req, res, next);

      // Assertions
      expect(Integration.findOne).toHaveBeenCalledWith({ id: 'existing-integration' });
      expect(next).toHaveBeenCalledWith(
        expect.objectContaining({
          name: 'ConflictError'
        })
      );
    });

    it('should handle other errors', async () => {
      // Setup request body
      req.body = {
        id: 'new-integration',
        name: 'New Integration'
      };

      // Setup error
      const error = new Error('Database error');
      Integration.findOne.mockRejectedValueOnce(error);

      // Call the controller
      await integrationController.createIntegration(req, res, next);

      // Assertions
      expect(next).toHaveBeenCalledWith(error);
    });
  });

  describe('updateIntegration', () => {
    it('should update an integration', async () => {
      // Setup request parameters and body
      req.params.id = 'integration-123';
      req.body = {
        name: 'Updated Integration',
        status: 'inactive'
      };

      // Create a mock integration with updated properties
      const updatedIntegration = {
        id: 'integration-123',
        name: 'Updated Integration',
        status: 'inactive',
        type: 'crm',
        provider: 'TestProvider',
        capabilities: ['data-export', 'data-deletion', 'data-update', 'notifications'],
        save: jest.fn().mockResolvedValue(true)
      };

      // Mock findOne to return our custom integration for this test
      Integration.findOne.mockResolvedValueOnce(updatedIntegration);

      // Call the controller
      await integrationController.updateIntegration(req, res, next);

      // Assertions
      expect(Integration.findOne).toHaveBeenCalledWith({ id: 'integration-123' });
      expect(updatedIntegration.save).toHaveBeenCalled();
      expect(res.json).toHaveBeenCalledWith({
        data: expect.objectContaining({
          id: 'integration-123',
          name: 'Updated Integration',
          status: 'inactive'
        }),
        message: 'Integration updated successfully'
      });
    });

    it('should not allow changing the ID', async () => {
      // Setup request parameters and body
      req.params.id = 'integration-123';
      req.body = {
        id: 'new-id',
        name: 'Updated Integration'
      };

      // Call the controller
      await integrationController.updateIntegration(req, res, next);

      // Assertions
      expect(Integration.findOne).toHaveBeenCalledWith({ id: 'integration-123' });
      expect(Integration.mockIntegration.id).toBe('integration-123'); // ID should not change
      expect(Integration.mockIntegration.name).toBe('Updated Integration');
      expect(Integration.mockIntegration.save).toHaveBeenCalled();
    });

    it('should handle NotFoundError', async () => {
      // Setup request parameters
      req.params.id = 'non-existent';
      req.body = {
        name: 'Updated Integration'
      };

      // Setup model to return null
      Integration.findOne.mockResolvedValueOnce(null);

      // Call the controller
      await integrationController.updateIntegration(req, res, next);

      // Assertions
      expect(Integration.findOne).toHaveBeenCalledWith({ id: 'non-existent' });
      expect(next).toHaveBeenCalledWith(expect.objectContaining({
        name: 'NotFoundError',
        message: 'Integration not found'
      }));
    });

    it('should handle other errors', async () => {
      // Setup request parameters
      req.params.id = 'integration-123';
      req.body = {
        name: 'Updated Integration'
      };

      // Setup error
      const error = new Error('Database error');
      Integration.findOne.mockRejectedValueOnce(error);

      // Call the controller
      await integrationController.updateIntegration(req, res, next);

      // Assertions
      expect(next).toHaveBeenCalledWith(error);
    });
  });

  describe('deleteIntegration', () => {
    it('should delete an integration', async () => {
      // Setup request parameters
      req.params.id = 'integration-123';

      // Call the controller
      await integrationController.deleteIntegration(req, res, next);

      // Assertions
      expect(Integration.findOne).toHaveBeenCalledWith({ id: 'integration-123' });
      expect(Integration.mockIntegration.remove).toHaveBeenCalled();
      expect(res.json).toHaveBeenCalledWith({
        message: 'Integration deleted successfully'
      });
    });

    it('should handle NotFoundError', async () => {
      // Setup request parameters
      req.params.id = 'non-existent';

      // Setup model to return null
      Integration.findOne.mockResolvedValueOnce(null);

      // Call the controller
      await integrationController.deleteIntegration(req, res, next);

      // Assertions
      expect(Integration.findOne).toHaveBeenCalledWith({ id: 'non-existent' });
      expect(next).toHaveBeenCalledWith(expect.objectContaining({
        name: 'NotFoundError',
        message: 'Integration not found'
      }));
    });

    it('should handle other errors', async () => {
      // Setup request parameters
      req.params.id = 'integration-123';

      // Setup error
      const error = new Error('Database error');
      Integration.findOne.mockRejectedValueOnce(error);

      // Call the controller
      await integrationController.deleteIntegration(req, res, next);

      // Assertions
      expect(next).toHaveBeenCalledWith(error);
    });
  });

  describe('configureIntegration', () => {
    it('should configure an integration', async () => {
      // Setup request parameters and body
      req.params.id = 'integration-123';
      req.body = {
        apiKey: 'test-api-key',
        endpoint: 'https://api.example.com',
        options: {
          timeout: 5000
        }
      };

      // Call the controller
      await integrationController.configureIntegration(req, res, next);

      // Assertions
      expect(Integration.findOne).toHaveBeenCalledWith({ id: 'integration-123' });
      expect(Integration.mockIntegration.config).toEqual(req.body);
      expect(Integration.mockIntegration.status).toBe('active');
      expect(Integration.mockIntegration.save).toHaveBeenCalled();
      expect(res.json).toHaveBeenCalledWith({
        data: expect.objectContaining({
          id: 'integration-123',
          config: req.body,
          status: 'active'
        }),
        message: 'Integration configured successfully'
      });
    });

    it('should handle NotFoundError', async () => {
      // Setup request parameters
      req.params.id = 'non-existent';
      req.body = {
        apiKey: 'test-api-key'
      };

      // Setup model to return null
      Integration.findOne.mockResolvedValueOnce(null);

      // Call the controller
      await integrationController.configureIntegration(req, res, next);

      // Assertions
      expect(Integration.findOne).toHaveBeenCalledWith({ id: 'non-existent' });
      expect(next).toHaveBeenCalledWith(expect.objectContaining({
        name: 'NotFoundError',
        message: 'Integration not found'
      }));
    });

    it('should handle other errors', async () => {
      // Setup request parameters
      req.params.id = 'integration-123';
      req.body = {
        apiKey: 'test-api-key'
      };

      // Setup error
      const error = new Error('Database error');
      Integration.findOne.mockRejectedValueOnce(error);

      // Call the controller
      await integrationController.configureIntegration(req, res, next);

      // Assertions
      expect(next).toHaveBeenCalledWith(error);
    });
  });

  describe('executeIntegrationAction', () => {
    it('should execute a data-export action', async () => {
      // Setup request parameters and body
      req.params.id = 'integration-123';
      req.params.action = 'data-export';
      req.body = {
        dataCategories: ['contacts', 'accounts']
      };

      // Mock Date.now for consistent IDs
      const originalDateNow = Date.now;
      Date.now = jest.fn().mockReturnValue(12345);

      // Call the controller
      await integrationController.executeIntegrationAction(req, res, next);

      // Restore Date.now
      Date.now = originalDateNow;

      // Assertions
      expect(Integration.findOne).toHaveBeenCalledWith({ id: 'integration-123' });
      expect(Integration.mockIntegration.metrics.totalRequests).toBe(101);
      expect(Integration.mockIntegration.metrics.successfulRequests).toBe(91);
      expect(Integration.mockIntegration.save).toHaveBeenCalled();
      expect(res.json).toHaveBeenCalledWith({
        data: expect.objectContaining({
          exportId: 'export-12345',
          exportTimestamp: expect.any(Date),
          dataCategories: ['contacts', 'accounts']
        }),
        message: "Integration action 'data-export' executed successfully"
      });
    });

    it('should execute a data-deletion action', async () => {
      // Setup request parameters and body
      req.params.id = 'integration-123';
      req.params.action = 'data-deletion';
      req.body = {
        dataCategories: ['contacts']
      };

      // Mock Date.now for consistent IDs
      const originalDateNow = Date.now;
      Date.now = jest.fn().mockReturnValue(12345);

      // Call the controller
      await integrationController.executeIntegrationAction(req, res, next);

      // Restore Date.now
      Date.now = originalDateNow;

      // Assertions
      expect(res.json).toHaveBeenCalledWith({
        data: expect.objectContaining({
          deletionId: 'deletion-12345',
          deletionTimestamp: expect.any(Date),
          dataCategories: ['contacts'],
          status: 'completed'
        }),
        message: "Integration action 'data-deletion' executed successfully"
      });
    });

    it('should execute a data-update action', async () => {
      // Setup request parameters and body
      req.params.id = 'integration-123';
      req.params.action = 'data-update';
      req.body = {
        updates: {
          email: '<EMAIL>',
          name: 'New Name'
        }
      };

      // Mock Date.now for consistent IDs
      const originalDateNow = Date.now;
      Date.now = jest.fn().mockReturnValue(12345);

      // Call the controller
      await integrationController.executeIntegrationAction(req, res, next);

      // Restore Date.now
      Date.now = originalDateNow;

      // Assertions
      expect(res.json).toHaveBeenCalledWith({
        data: expect.objectContaining({
          updateId: 'update-12345',
          updateTimestamp: expect.any(Date),
          status: 'completed',
          updatedFields: ['email', 'name']
        }),
        message: "Integration action 'data-update' executed successfully"
      });
    });

    it('should execute a notifications action', async () => {
      // Setup request parameters and body
      req.params.id = 'integration-123';
      req.params.action = 'notifications';
      req.body = {
        channel: 'email'
      };

      // Mock Date.now for consistent IDs
      const originalDateNow = Date.now;
      Date.now = jest.fn().mockReturnValue(12345);

      // Call the controller
      await integrationController.executeIntegrationAction(req, res, next);

      // Restore Date.now
      Date.now = originalDateNow;

      // Assertions
      expect(res.json).toHaveBeenCalledWith({
        data: expect.objectContaining({
          notificationId: 'notification-12345',
          notificationTimestamp: expect.any(Date),
          status: 'sent',
          channel: 'email'
        }),
        message: "Integration action 'notifications' executed successfully"
      });
    });

    it('should execute a default action', async () => {
      // Setup request parameters and body
      req.params.id = 'integration-123';
      req.params.action = 'custom-action';
      req.body = {};

      // Add the custom action to capabilities
      Integration.mockIntegration.capabilities.push('custom-action');

      // Mock Date.now for consistent IDs
      const originalDateNow = Date.now;
      Date.now = jest.fn().mockReturnValue(12345);

      // Call the controller
      await integrationController.executeIntegrationAction(req, res, next);

      // Restore Date.now
      Date.now = originalDateNow;

      // Assertions
      expect(res.json).toHaveBeenCalledWith({
        data: expect.objectContaining({
          actionId: 'action-12345',
          actionTimestamp: expect.any(Date),
          status: 'completed'
        }),
        message: "Integration action 'custom-action' executed successfully"
      });
    });

    it('should handle NotFoundError', async () => {
      // Setup request parameters
      req.params.id = 'non-existent';
      req.params.action = 'data-export';

      // Setup model to return null
      Integration.findOne.mockResolvedValueOnce(null);

      // Call the controller
      await integrationController.executeIntegrationAction(req, res, next);

      // Assertions
      expect(Integration.findOne).toHaveBeenCalledWith({ id: 'non-existent' });
      expect(next).toHaveBeenCalledWith(expect.objectContaining({
        name: 'NotFoundError',
        message: 'Integration not found'
      }));
    });

    it('should handle ValidationError when action is not supported', async () => {
      // Setup request parameters
      req.params.id = 'integration-123';
      req.params.action = 'unsupported-action';

      // Call the controller
      await integrationController.executeIntegrationAction(req, res, next);

      // Assertions
      expect(next).toHaveBeenCalledWith(expect.objectContaining({
        name: 'ValidationError',
        message: "Integration does not support action 'unsupported-action'"
      }));
    });

    it('should handle ValidationError when integration is not active', async () => {
      // Setup request parameters
      req.params.id = 'integration-123';
      req.params.action = 'data-export';

      // Set integration status to inactive
      const inactiveIntegration = {
        ...Integration.mockIntegration,
        status: 'inactive'
      };
      Integration.findOne.mockResolvedValueOnce(inactiveIntegration);

      // Call the controller
      await integrationController.executeIntegrationAction(req, res, next);

      // Assertions
      expect(next).toHaveBeenCalledWith(expect.objectContaining({
        name: 'ValidationError',
        message: 'Integration is not active'
      }));
    });

    it('should handle other errors', async () => {
      // Setup request parameters
      req.params.id = 'integration-123';
      req.params.action = 'data-export';

      // Setup error
      const error = new Error('Database error');
      Integration.findOne.mockRejectedValueOnce(error);

      // Call the controller
      await integrationController.executeIntegrationAction(req, res, next);

      // Assertions
      expect(next).toHaveBeenCalledWith(error);
    });
  });

  describe('checkIntegrationHealth', () => {
    it('should check integration health and return healthy status', async () => {
      // Setup request parameters
      req.params.id = 'integration-123';

      // Mock Math.random to always return 0.5 (healthy)
      const originalMathRandom = Math.random;
      Math.random = jest.fn().mockReturnValue(0.5);

      // Call the controller
      await integrationController.checkIntegrationHealth(req, res, next);

      // Restore Math.random
      Math.random = originalMathRandom;

      // Assertions
      expect(Integration.findOne).toHaveBeenCalledWith({ id: 'integration-123' });
      expect(Integration.mockIntegration.lastChecked).toBeInstanceOf(Date);
      expect(Integration.mockIntegration.status).toBe('active');
      expect(Integration.mockIntegration.save).toHaveBeenCalled();
      expect(res.json).toHaveBeenCalledWith({
        data: expect.objectContaining({
          status: 'healthy',
          lastChecked: expect.any(Date),
          responseTime: expect.any(Number),
          issues: []
        }),
        message: 'Integration is healthy'
      });
    });

    it('should check integration health and return unhealthy status', async () => {
      // Setup request parameters
      req.params.id = 'integration-123';

      // Mock Math.random to always return 0.05 (unhealthy)
      const originalMathRandom = Math.random;
      Math.random = jest.fn().mockReturnValue(0.05);

      // Call the controller
      await integrationController.checkIntegrationHealth(req, res, next);

      // Restore Math.random
      Math.random = originalMathRandom;

      // Assertions
      expect(Integration.mockIntegration.status).toBe('error');
      expect(Integration.mockIntegration.lastError).toEqual(expect.objectContaining({
        message: 'Connection timeout',
        timestamp: expect.any(Date),
        details: expect.any(Object)
      }));
      expect(res.json).toHaveBeenCalledWith({
        data: expect.objectContaining({
          status: 'unhealthy',
          lastChecked: expect.any(Date),
          responseTime: expect.any(Number),
          issues: expect.arrayContaining([
            expect.objectContaining({
              type: 'connection',
              message: 'Connection timeout'
            })
          ])
        }),
        message: 'Integration is unhealthy'
      });
    });

    it('should handle NotFoundError', async () => {
      // Setup request parameters
      req.params.id = 'non-existent';

      // Setup model to return null
      Integration.findOne.mockResolvedValueOnce(null);

      // Call the controller
      await integrationController.checkIntegrationHealth(req, res, next);

      // Assertions
      expect(Integration.findOne).toHaveBeenCalledWith({ id: 'non-existent' });
      expect(next).toHaveBeenCalledWith(expect.objectContaining({
        name: 'NotFoundError',
        message: 'Integration not found'
      }));
    });

    it('should handle other errors', async () => {
      // Setup request parameters
      req.params.id = 'integration-123';

      // Setup error
      const error = new Error('Database error');
      Integration.findOne.mockRejectedValueOnce(error);

      // Call the controller
      await integrationController.checkIntegrationHealth(req, res, next);

      // Assertions
      expect(next).toHaveBeenCalledWith(error);
    });
  });

  describe('getIntegrationLogs', () => {
    it('should return integration logs with pagination', async () => {
      // Setup request parameters and query
      req.params.id = 'integration-123';
      req.query = {
        page: '2',
        limit: '5'
      };

      // Call the controller
      await integrationController.getIntegrationLogs(req, res, next);

      // Assertions
      expect(Integration.findOne).toHaveBeenCalledWith({ id: 'integration-123' });
      expect(res.json).toHaveBeenCalledWith({
        data: expect.arrayContaining([
          expect.objectContaining({
            integrationId: 'integration-123',
            action: expect.any(String),
            status: expect.any(String),
            timestamp: expect.any(Date)
          })
        ]),
        pagination: expect.objectContaining({
          total: 100,
          page: 2,
          limit: 5,
          pages: 20
        })
      });
    });

    it('should use default pagination values if not provided', async () => {
      // Setup request parameters
      req.params.id = 'integration-123';

      // Call the controller
      await integrationController.getIntegrationLogs(req, res, next);

      // Assertions
      expect(res.json).toHaveBeenCalledWith({
        data: expect.any(Array),
        pagination: expect.objectContaining({
          page: 1,
          limit: 10,
          pages: 10
        })
      });
    });

    it('should handle NotFoundError', async () => {
      // Setup request parameters
      req.params.id = 'non-existent';

      // Setup model to return null
      Integration.findOne.mockResolvedValueOnce(null);

      // Call the controller
      await integrationController.getIntegrationLogs(req, res, next);

      // Assertions
      expect(Integration.findOne).toHaveBeenCalledWith({ id: 'non-existent' });
      expect(next).toHaveBeenCalledWith(expect.objectContaining({
        name: 'NotFoundError',
        message: 'Integration not found'
      }));
    });

    it('should handle other errors', async () => {
      // Setup request parameters
      req.params.id = 'integration-123';

      // Setup error
      const error = new Error('Database error');
      Integration.findOne.mockRejectedValueOnce(error);

      // Call the controller
      await integrationController.getIntegrationLogs(req, res, next);

      // Assertions
      expect(next).toHaveBeenCalledWith(error);
    });
  });

  describe('getIntegrationMetrics', () => {
    it('should return integration metrics', async () => {
      // Setup request parameters
      req.params.id = 'integration-123';
      req.query = {
        period: 'last-30-days'
      };

      // Create a mock integration with specific metrics
      const integrationWithMetrics = {
        id: 'integration-123',
        name: 'Test Integration',
        type: 'crm',
        provider: 'TestProvider',
        status: 'active',
        capabilities: ['data-export', 'data-deletion', 'data-update', 'notifications'],
        metrics: {
          totalRequests: 100,
          successfulRequests: 90,
          averageResponseTime: 200
        },
        save: jest.fn().mockResolvedValue(true)
      };

      // Mock findOne to return our custom integration for this test
      Integration.findOne.mockResolvedValueOnce(integrationWithMetrics);

      // Call the controller
      await integrationController.getIntegrationMetrics(req, res, next);

      // Assertions
      expect(Integration.findOne).toHaveBeenCalledWith({ id: 'integration-123' });
      expect(res.json).toHaveBeenCalledWith({
        data: expect.objectContaining({
          requests: expect.objectContaining({
            total: expect.any(Number),
            success: expect.any(Number),
            error: expect.any(Number)
          }),
          performance: expect.objectContaining({
            averageResponseTime: expect.any(Number)
          }),
          errors: expect.any(Object)
        })
      });
    });

    it('should use default period if not provided', async () => {
      // Setup request parameters
      req.params.id = 'integration-123';

      // Call the controller
      await integrationController.getIntegrationMetrics(req, res, next);

      // Assertions
      expect(res.json).toHaveBeenCalledWith({
        data: expect.any(Object)
      });
    });

    it('should handle NotFoundError', async () => {
      // Setup request parameters
      req.params.id = 'non-existent';

      // Setup model to return null
      Integration.findOne.mockResolvedValueOnce(null);

      // Call the controller
      await integrationController.getIntegrationMetrics(req, res, next);

      // Assertions
      expect(Integration.findOne).toHaveBeenCalledWith({ id: 'non-existent' });
      expect(next).toHaveBeenCalledWith(expect.objectContaining({
        name: 'NotFoundError',
        message: 'Integration not found'
      }));
    });

    it('should handle other errors', async () => {
      // Setup request parameters
      req.params.id = 'integration-123';

      // Setup error
      const error = new Error('Database error');
      Integration.findOne.mockRejectedValueOnce(error);

      // Call the controller
      await integrationController.getIntegrationMetrics(req, res, next);

      // Assertions
      expect(next).toHaveBeenCalledWith(error);
    });
  });
});
