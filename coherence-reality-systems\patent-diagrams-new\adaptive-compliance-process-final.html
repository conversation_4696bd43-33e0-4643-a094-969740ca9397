<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Adaptive Compliance Process Diagram</title>
    <style>
        /* Reset and Base Styles */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            line-height: 1.4;
        }
        
        /* Main Diagram Container */
        .diagram-container {
            position: relative;
            width: 950px;
            min-height: 900px; /* Increased minimum height */
            margin: 0 auto 40px;
            background-color: white;
            border: 1px solid #ddd;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            box-sizing: border-box;
            overflow: visible; /* Allow content to be fully visible */
        }
        
        /* Container Box Styles */
        .container-box {
            position: absolute;
            border: 2px solid #666;
            border-radius: 10px;
            padding: 30px 10px 10px;
            overflow: visible; /* Allow content to be fully visible */
            min-height: 100px; /* Minimum height */
            min-width: 200px; /* Minimum width */
            z-index: 5; /* Bring containers in front of connecting lines */
            background-color: white; /* Ensure background is opaque */
        }
        
        .container-label {
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            font-weight: bold;
            font-size: 16px;
            text-align: center;
            width: 90%; /* Ensures text doesn't overflow container */
            white-space: normal; /* Allows text to wrap */
            overflow: visible; /* Allow content to be fully visible */
            z-index: 10; /* Ensure labels are in front of connecting lines */
            background-color: white; /* Add background to hide lines behind text */
            padding: 0 5px; /* Add padding around text */
        }
        
        /* Component Box Styles */
        .component-box {
            position: absolute;
            background-color: white;
            border: 2px solid #333;
            border-radius: 6px;
            display: flex;
            flex-direction: column;
            justify-content: center; /* Center content vertically */
            align-items: center;
            text-align: center;
            padding: 5px; /* Reduced padding */
            overflow: visible; /* Allow content to be fully visible */
            border-top-left-radius: 0; /* Flat corner for number */
            min-height: 50px; /* Minimum height */
            min-width: 100px; /* Minimum width */
        }
        
        /* Component Number Styles - Integrated into corner */
        .component-number-inside {
            position: absolute;
            top: 0;
            left: 0;
            width: 18px;
            height: 18px;
            background-color: #000000; /* Black background for component numbers */
            color: white;
            border-radius: 0 0 6px 0; /* Rounded on bottom-right corner only */
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 10px; /* Smaller font */
            font-weight: bold;
            z-index: 2;
            margin: 0; /* Remove any margin */
            padding: 0; /* Remove any padding */
        }
        
        .component-label {
            font-weight: bold;
            margin-top: 10px; /* Reduced margin */
            margin-bottom: 2px; /* Reduced margin */
            font-size: 12px; /* Smaller font */
            text-align: center;
            width: 100%;
            white-space: normal; /* Allows text to wrap */
            overflow: visible; /* Allow content to be fully visible */
        }
        
        .component-content {
            font-size: 10px; /* Smaller font */
            text-align: center;
            width: 100%;
            padding: 0;
            white-space: normal; /* Allows text to wrap */
            overflow: visible; /* Allow content to be fully visible */
            line-height: 1.2; /* Tighter line spacing */
        }
        
        /* Arrow Styles */
        .arrow {
            position: absolute;
            background-color: #555555; /* Grey color for patent compliance */
            width: 2px;
            z-index: 1; /* Ensure arrows are behind containers but visible */
        }
        
        /* Legend Styles */
        .legend {
            position: absolute;
            right: 10px;
            bottom: 30px;
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 8px;
            z-index: 10;
            width: 200px;
            font-size: 12px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 6px;
        }
        
        .legend-color {
            width: 15px;
            height: 15px;
            margin-right: 8px;
            border: 1px solid #333;
            flex-shrink: 0; /* Prevents the color box from shrinking */
        }
        
        /* Inventor Label */
        .inventor-label {
            position: absolute;
            left: 10px;
            bottom: 30px;
            font-size: 12px;
            font-style: italic;
            color: #333;
            z-index: 10;
        }
        
        /* Title Styles */
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 20px;
            color: #333;
        }
    </style>
</head>
<body>

    <h1>FIG. 9: Adaptive Compliance Process Diagram</h1>
    
    <div class="diagram-container">
        <!-- Main Container -->
        <div class="container-box" style="width: 750px; height: 600px; left: 25px; top: 20px;">
            <div class="container-label" style="font-size: 18px;">ADAPTIVE COMPLIANCE PROCESS: CYBER-SAFETY CONTEXT</div>
        </div>

        <!-- Continuous Cycle Indicator -->
        <div class="cycle-arrow" style="left: 355px; top: 300px;">↻</div>

        <!-- Regulatory Monitoring -->
        <div class="component-box" style="left: 300px; top: 80px; width: 200px; height: 80px;">
            <div class="component-number-inside">1101</div>
            <div class="component-label">Regulatory Monitoring</div>
            <div style="font-size: 12px; text-align: center;">
                NovaTrack<br>
                Real-time Compliance Updates
            </div>
        </div>
        <div class="formula-box" style="left: 510px; top: 100px;">Data Purity Score</div>

        <!-- Compliance Assessment -->
        <div class="component-box" style="left: 500px; top: 180px; width: 200px; height: 80px;">
            <div class="component-number-inside">1102</div>
            <div class="component-label">Compliance Assessment</div>
            <div style="font-size: 12px; text-align: center;">
                NovaCore<br>
                Gap Analysis & Risk Evaluation
            </div>
        </div>
        <div class="formula-box" style="left: 500px; top: 270px;">(A⊗B⊕C)×π10³</div>

        <!-- Policy Generation -->
        <div class="component-box" style="left: 500px; top: 380px; width: 200px; height: 80px;">
            <div class="component-number-inside">1103</div>
            <div class="component-label">Policy Generation</div>
            <div style="font-size: 12px; text-align: center;">
                NovaThink<br>
                Automated Policy Creation
            </div>
        </div>
        <div class="formula-box" style="left: 500px; top: 470px;">Trinity Equation</div>

        <!-- Implementation -->
        <div class="component-box" style="left: 300px; top: 480px; width: 200px; height: 80px;">
            <div class="component-number-inside">1104</div>
            <div class="component-label">Implementation</div>
            <div style="font-size: 12px; text-align: center;">
                NovaFlowX<br>
                Automated Control Deployment
            </div>
        </div>
        <div class="formula-box" style="left: 200px; top: 500px;">18/82 Principle</div>

        <!-- Verification -->
        <div class="component-box" style="left: 100px; top: 380px; width: 200px; height: 80px;">
            <div class="component-number-inside">1105</div>
            <div class="component-label">Verification</div>
            <div style="font-size: 12px; text-align: center;">
                NovaProof<br>
                Control Effectiveness Testing
            </div>
        </div>
        <div class="formula-box" style="left: 100px; top: 470px;">Field Coherence</div>

        <!-- Continuous Learning -->
        <div class="component-box" style="left: 100px; top: 180px; width: 200px; height: 80px;">
            <div class="component-number-inside">1106</div>
            <div class="component-label">Continuous Learning</div>
            <div style="font-size: 12px; text-align: center;">
                NovaLearn<br>
                Adaptive Improvement
            </div>
        </div>
        <div class="formula-box" style="left: 100px; top: 270px;">Adaptive Coherence</div>

        <!-- Compliance Domains -->
        <div class="container-box" style="width: 650px; height: 80px; left: 75px; top: 580px;">
            <div class="container-label" style="font-size: 14px;">COMPLIANCE DOMAINS</div>
        </div>

        <div style="position: absolute; left: 100px; top: 610px; display: flex; justify-content: space-between; width: 600px;">
            <div style="font-size: 12px; text-align: center; width: 80px;">GDPR</div>
            <div style="font-size: 12px; text-align: center; width: 80px;">HIPAA</div>
            <div style="font-size: 12px; text-align: center; width: 80px;">PCI DSS</div>
            <div style="font-size: 12px; text-align: center; width: 80px;">SOX</div>
            <div style="font-size: 12px; text-align: center; width: 80px;">NIST 800-53</div>
            <div style="font-size: 12px; text-align: center; width: 80px;">ISO 27001</div>
        </div>

        <!-- Arrows -->
        <div class="arrow-line" style="left: 400px; top: 160px; width: 100px; height: 2px; transform: translate(0, 0) rotate(45deg); transform-origin: top left;"></div>
        <div class="arrow-head" style="left: 500px; top: 180px; border-width: 4px 0 4px 8px; border-color: transparent transparent transparent #333;"></div>

        <div class="arrow-line" style="left: 600px; top: 270px; width: 2px; height: 120px;"></div>
        <div class="arrow-head" style="left: 596px; top: 380px; border-width: 8px 4px 0 4px; border-color: #333 transparent transparent transparent;"></div>

        <div class="arrow-line" style="left: 500px; top: 440px; width: 100px; height: 2px; transform: translate(0, 0) rotate(-45deg); transform-origin: top left;"></div>
        <div class="arrow-head" style="left: 400px; top: 480px; border-width: 4px 8px 4px 0; border-color: transparent #333 transparent transparent;"></div>

        <div class="arrow-line" style="left: 300px; top: 520px; width: 100px; height: 2px; transform: translate(0, 0) rotate(-45deg); transform-origin: top left;"></div>
        <div class="arrow-head" style="left: 200px; top: 480px; border-width: 4px 8px 4px 0; border-color: transparent #333 transparent transparent;"></div>

        <div class="arrow-line" style="left: 200px; top: 380px; width: 2px; height: 120px;"></div>
        <div class="arrow-head" style="left: 196px; top: 270px; border-width: 0 4px 8px 4px; border-color: transparent transparent #333 transparent;"></div>

        <div class="arrow-line" style="left: 200px; top: 180px; width: 100px; height: 2px; transform: translate(0, 0) rotate(-45deg); transform-origin: top left;"></div>
        <div class="arrow-head" style="left: 300px; top: 120px; border-width: 4px 0 4px 8px; border-color: transparent transparent transparent #333;"></div>

        <!-- Legend -->
        <div class="legend">
            <div class="legend-item">
                <div class="legend-color" style="background-color: #fff;"></div>
                <div>Process Steps</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #f9f9f9;"></div>
                <div>Mathematical Formulas</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #fff; border-radius: 50%; display: flex; justify-content: center; align-items: center; font-size: 10px;">↻</div>
                <div>Continuous Cycle</div>
            </div>
        </div>

        <div class="inventor-label">Inventor: David Nigel Irvin</div>
    </div>

</body>
</html>
