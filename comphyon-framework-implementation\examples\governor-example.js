/**
 * Governor Example
 * 
 * This example demonstrates the usage of the Governor component.
 */

const {
  ControlActionExecution,
  AutomatedResponseSystem,
  PolicyEnforcement,
  createGovernorSystem,
  createEnhancedGovernorSystem
} = require('../governor');

// Import Meter for integration example
const { createEnhancedMeterSystem } = require('../meter');

// Example 1: Using individual components
console.log('Example 1: Using individual components');

// Create components
const controlActionExecution = new ControlActionExecution();
const automatedResponseSystem = new AutomatedResponseSystem(controlActionExecution);
const policyEnforcement = new PolicyEnforcement();

// Start components
controlActionExecution.start();
automatedResponseSystem.start();
policyEnforcement.start();

// Register control action
const action = controlActionExecution.registerAction({
  name: 'Log Message',
  description: 'Logs a message to the console',
  handler: (parameters, context) => {
    const { message = 'Default message' } = parameters;
    console.log(`[ACTION] ${message}`);
    return { success: true, message };
  }
});
console.log(`Registered action: ${action.id} (${action.name})`);

// Execute control action
const executionId = controlActionExecution.executeAction(
  action.id,
  { message: 'Hello from control action!' },
  { source: 'example' }
);
console.log(`Executed action: ${executionId}`);

// Register response rule
const rule = automatedResponseSystem.registerRule({
  name: 'Test Response Rule',
  description: 'Test response rule for demonstration',
  condition: (alert) => {
    return alert.level === 'warning' || alert.level === 'critical';
  },
  actions: [
    {
      actionId: action.id,
      parameters: {
        message: 'Response triggered by alert!'
      }
    }
  ]
});
console.log(`Registered rule: ${rule.id} (${rule.name})`);

// Process alert
const alert = {
  id: 'alert-test-1',
  domain: 'cyber',
  level: 'warning',
  entropyValue: 0.7,
  threshold: 0.6,
  deviation: 0.1,
  timestamp: Date.now()
};
const responses = automatedResponseSystem.processAlert(alert);
console.log(`Processed alert with ${responses.length} responses`);

// Register policy
const policy = policyEnforcement.registerPolicy({
  name: 'Test Policy',
  description: 'Test policy for demonstration',
  domains: ['cyber'],
  rules: [
    {
      condition: (data, context) => {
        return data.entropyValue > 0.6;
      },
      action: (data, context) => {
        console.log(`[POLICY] Entropy too high: ${data.entropyValue}`);
        return { action: 'log', message: 'Entropy too high' };
      }
    }
  ]
});
console.log(`Registered policy: ${policy.id} (${policy.name})`);

// Evaluate data against policies
const violations = policyEnforcement.evaluateData(
  'cyber',
  { entropyValue: 0.7 },
  { source: 'example' }
);
console.log(`Found ${violations.length} policy violations`);

// Enforce policy
const actions = policyEnforcement.enforcePolicy(
  policy.id,
  'cyber',
  { entropyValue: 0.7 },
  { source: 'example' }
);
console.log(`Enforced policy with ${actions.length} actions`);

// Stop components
controlActionExecution.stop();
automatedResponseSystem.stop();
policyEnforcement.stop();

// Example 2: Using the basic Governor system
console.log('\nExample 2: Using the basic Governor system');

// Create Governor system
const governorSystem = createGovernorSystem({
  controlActionExecutionOptions: {
    enableLogging: true
  },
  automatedResponseSystemOptions: {
    enableLogging: true
  },
  policyEnforcementOptions: {
    enableLogging: true
  }
});

// Start components
governorSystem.controlActionExecution.start();
governorSystem.automatedResponseSystem.start();
governorSystem.policyEnforcement.start();

// Register control action
const action2 = governorSystem.controlActionExecution.registerAction({
  name: 'Log Message',
  description: 'Logs a message to the console',
  handler: (parameters, context) => {
    const { message = 'Default message' } = parameters;
    console.log(`[ACTION] ${message}`);
    return { success: true, message };
  }
});
console.log(`Registered action: ${action2.id} (${action2.name})`);

// Register response rule
const rule2 = governorSystem.automatedResponseSystem.registerRule({
  name: 'Test Response Rule',
  description: 'Test response rule for demonstration',
  condition: (alert) => {
    return alert.level === 'warning' || alert.level === 'critical';
  },
  actions: [
    {
      actionId: action2.id,
      parameters: {
        message: 'Response triggered by alert!'
      }
    }
  ]
});
console.log(`Registered rule: ${rule2.id} (${rule2.name})`);

// Register policy
const policy2 = governorSystem.policyEnforcement.registerPolicy({
  name: 'Test Policy',
  description: 'Test policy for demonstration',
  domains: ['cyber'],
  rules: [
    {
      condition: (data, context) => {
        return data.entropyValue > 0.6;
      },
      action: (data, context) => {
        console.log(`[POLICY] Entropy too high: ${data.entropyValue}`);
        return { action: 'log', message: 'Entropy too high' };
      }
    }
  ]
});
console.log(`Registered policy: ${policy2.id} (${policy2.name})`);

// Stop components
governorSystem.controlActionExecution.stop();
governorSystem.automatedResponseSystem.stop();
governorSystem.policyEnforcement.stop();

// Example 3: Using the enhanced Governor system
console.log('\nExample 3: Using the enhanced Governor system');

// Create Meter system for integration
const meterSystem = createEnhancedMeterSystem({
  enableLogging: true
});

// Create enhanced Governor system
const enhancedGovernorSystem = createEnhancedGovernorSystem(
  {
    enableLogging: true
  },
  meterSystem
);

// Start system
enhancedGovernorSystem.start();

// Register custom control action
const customAction = enhancedGovernorSystem.registerControlAction({
  name: 'Custom Action',
  description: 'Custom action for demonstration',
  domains: ['cyber', 'financial', 'biological'],
  handler: (parameters, context) => {
    const { domain = 'universal', message = 'Custom action executed' } = parameters;
    console.log(`[CUSTOM ACTION] ${message} for ${domain} domain`);
    return { success: true, domain, message };
  }
});
console.log(`Registered custom action: ${customAction.id} (${customAction.name})`);

// Register custom response rule
const customRule = enhancedGovernorSystem.registerResponseRule({
  name: 'Custom Response Rule',
  description: 'Custom response rule for demonstration',
  domains: ['cyber', 'financial'],
  alertLevels: ['warning', 'critical', 'emergency'],
  condition: (alert) => {
    return alert.domain === 'cyber' && alert.entropyValue > 0.7;
  },
  actions: [
    {
      actionId: customAction.id,
      parameters: {
        domain: 'cyber',
        message: 'Responding to high cyber entropy'
      }
    }
  ]
});
console.log(`Registered custom rule: ${customRule.id} (${customRule.name})`);

// Register custom policy
const customPolicy = enhancedGovernorSystem.registerPolicy({
  name: 'Custom Policy',
  description: 'Custom policy for demonstration',
  domains: ['cyber', 'financial', 'biological'],
  priority: 'high',
  rules: [
    {
      condition: (data, context) => {
        return data.entropyValue > 0.8;
      },
      action: (data, context) => {
        console.log(`[CUSTOM POLICY] Critical entropy detected: ${data.entropyValue}`);
        return { action: 'alert', level: 'critical', message: 'Critical entropy detected' };
      }
    }
  ]
});
console.log(`Registered custom policy: ${customPolicy.id} (${customPolicy.name})`);

// Process alert
const customAlert = {
  id: 'alert-custom-1',
  domain: 'cyber',
  level: 'critical',
  entropyValue: 0.85,
  threshold: 0.7,
  deviation: 0.15,
  timestamp: Date.now()
};
const alertResult = enhancedGovernorSystem.processAlert(customAlert);
console.log(`Processed alert with ${alertResult.responses.length} responses and ${alertResult.violations.length} violations`);

// Execute custom action
const customExecutionId = enhancedGovernorSystem.executeAction(
  customAction.id,
  {
    domain: 'financial',
    message: 'Manual execution of custom action'
  },
  {
    source: 'example',
    timestamp: Date.now()
  }
);
console.log(`Executed custom action: ${customExecutionId}`);

// Evaluate data against policies
const customViolations = enhancedGovernorSystem.evaluateData(
  'financial',
  {
    entropyValue: 0.9,
    source: 'example'
  },
  {
    timestamp: Date.now()
  }
);
console.log(`Found ${customViolations.length} policy violations`);

// Get unified state
const unifiedState = enhancedGovernorSystem.getUnifiedState();
console.log('Unified State Summary:');
console.log(`- Action Queue: ${unifiedState.actionQueue.length}`);
console.log(`- Running Actions: ${unifiedState.runningActions.length}`);
console.log(`- Active Responses: ${unifiedState.activeResponses.length}`);
console.log(`- Policy Violations: ${unifiedState.policyViolations.length}`);

// Get unified metrics
const unifiedMetrics = enhancedGovernorSystem.getUnifiedMetrics();
console.log('Unified Metrics Summary:');
console.log(`- Actions Executed: ${unifiedMetrics.controlActionExecution.actionsExecuted}`);
console.log(`- Responses Triggered: ${unifiedMetrics.automatedResponseSystem.responsesTriggered}`);
console.log(`- Policy Violations: ${unifiedMetrics.policyEnforcement.policyViolations}`);

// Stop system
enhancedGovernorSystem.stop();

console.log('\nGovernor example completed successfully!');
