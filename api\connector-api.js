/**
 * NovaFuse Universal API Connector API
 * 
 * This module provides the API endpoints for managing and executing connectors.
 */

const express = require('express');
const router = express.Router();
const connectorRegistry = require('../registry/connector-registry');
const connectorExecutor = require('../connector-executor');

// Initialize the connector registry
connectorRegistry.initialize().catch(error => {
  console.error('Failed to initialize connector registry:', error);
});

/**
 * Get all connectors
 */
router.get('/', async (req, res) => {
  try {
    const connectors = connectorRegistry.getAllConnectors();
    res.json(connectors);
  } catch (error) {
    console.error('Error getting connectors:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * Get a connector by ID
 */
router.get('/:id', async (req, res) => {
  try {
    const connector = connectorRegistry.getConnector(req.params.id);
    
    if (!connector) {
      return res.status(404).json({ error: `Connector ${req.params.id} not found` });
    }
    
    res.json(connector);
  } catch (error) {
    console.error('Error getting connector:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * Register a new connector
 */
router.post('/', async (req, res) => {
  try {
    const template = req.body;
    
    if (!template) {
      return res.status(400).json({ error: 'Connector template is required' });
    }
    
    await connectorRegistry.registerConnector(template);
    
    res.status(201).json({ success: true, message: 'Connector registered successfully' });
  } catch (error) {
    console.error('Error registering connector:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * Update a connector
 */
router.put('/:id', async (req, res) => {
  try {
    const template = req.body;
    
    if (!template) {
      return res.status(400).json({ error: 'Connector template is required' });
    }
    
    await connectorRegistry.updateConnector(req.params.id, template);
    
    res.json({ success: true, message: 'Connector updated successfully' });
  } catch (error) {
    console.error('Error updating connector:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * Delete a connector
 */
router.delete('/:id', async (req, res) => {
  try {
    await connectorRegistry.deleteConnector(req.params.id);
    
    res.json({ success: true, message: 'Connector deleted successfully' });
  } catch (error) {
    console.error('Error deleting connector:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * Search connectors
 */
router.get('/search/:query', async (req, res) => {
  try {
    const connectors = connectorRegistry.searchConnectors(req.params.query);
    res.json(connectors);
  } catch (error) {
    console.error('Error searching connectors:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * Get connectors by category
 */
router.get('/category/:category', async (req, res) => {
  try {
    const connectors = connectorRegistry.getConnectorsByCategory(req.params.category);
    res.json(connectors);
  } catch (error) {
    console.error('Error getting connectors by category:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * Execute a connector endpoint
 */
router.post('/:id/execute/:endpoint', async (req, res) => {
  try {
    const result = await connectorExecutor.executeConnector(
      req.params.id,
      req.params.endpoint,
      req.body
    );
    
    if (!result.success) {
      return res.status(result.statusCode || 500).json({ error: result.error });
    }
    
    res.json(result.data);
  } catch (error) {
    console.error('Error executing connector:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * Get connector metrics
 */
router.get('/metrics', async (req, res) => {
  try {
    const metrics = {
      registry: connectorRegistry.getMetrics ? connectorRegistry.getMetrics() : {},
      executor: connectorExecutor.getMetrics()
    };
    
    res.json(metrics);
  } catch (error) {
    console.error('Error getting connector metrics:', error);
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;
