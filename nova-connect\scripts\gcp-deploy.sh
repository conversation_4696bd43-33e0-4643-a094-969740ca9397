#!/bin/bash
# NovaConnect UAC GCP Deployment Script
# This script deploys NovaConnect UAC to Google Cloud Platform

# Exit on error
set -e

# Load environment variables
source .env

# Check if required environment variables are set
if [ -z "$GOOGLE_CLOUD_PROJECT" ]; then
  echo "Error: GOOGLE_CLOUD_PROJECT environment variable is not set"
  exit 1
fi

if [ -z "$GCP_REGION" ]; then
  echo "Error: GCP_REGION environment variable is not set"
  exit 1
fi

if [ -z "$GCP_ZONE" ]; then
  echo "Error: GCP_ZONE environment variable is not set"
  exit 1
fi

# Set default values
CLUSTER_NAME=${CLUSTER_NAME:-"novafuse-cluster"}
NAMESPACE=${NAMESPACE:-"novafuse"}
SERVICE_ACCOUNT=${SERVICE_ACCOUNT:-"novafuse-sa"}
VERSION=$(node -p "require('./package.json').version")

echo "Deploying NovaConnect UAC version $VERSION to GCP project $GOOGLE_CLOUD_PROJECT"

# Authenticate with Google Cloud
echo "Authenticating with Google Cloud..."
gcloud auth activate-service-account --key-file=$GOOGLE_APPLICATION_CREDENTIALS

# Set the project
echo "Setting project to $GOOGLE_CLOUD_PROJECT..."
gcloud config set project $GOOGLE_CLOUD_PROJECT

# Set the compute region and zone
echo "Setting compute region to $GCP_REGION and zone to $GCP_ZONE..."
gcloud config set compute/region $GCP_REGION
gcloud config set compute/zone $GCP_ZONE

# Create a GKE cluster if it doesn't exist
echo "Checking if GKE cluster $CLUSTER_NAME exists..."
if ! gcloud container clusters describe $CLUSTER_NAME --region $GCP_REGION > /dev/null 2>&1; then
  echo "Creating GKE cluster $CLUSTER_NAME..."
  gcloud container clusters create $CLUSTER_NAME \
    --region $GCP_REGION \
    --num-nodes 3 \
    --machine-type e2-standard-4 \
    --enable-autoscaling \
    --min-nodes 3 \
    --max-nodes 10 \
    --enable-autorepair \
    --enable-autoupgrade \
    --enable-stackdriver-kubernetes
else
  echo "GKE cluster $CLUSTER_NAME already exists"
fi

# Get credentials for the cluster
echo "Getting credentials for GKE cluster $CLUSTER_NAME..."
gcloud container clusters get-credentials $CLUSTER_NAME --region $GCP_REGION

# Create a namespace if it doesn't exist
echo "Checking if namespace $NAMESPACE exists..."
if ! kubectl get namespace $NAMESPACE > /dev/null 2>&1; then
  echo "Creating namespace $NAMESPACE..."
  kubectl create namespace $NAMESPACE
else
  echo "Namespace $NAMESPACE already exists"
fi

# Create a service account if it doesn't exist
echo "Checking if service account $SERVICE_ACCOUNT exists..."
if ! gcloud iam service-accounts describe $SERVICE_ACCOUNT@$GOOGLE_CLOUD_PROJECT.iam.gserviceaccount.com > /dev/null 2>&1; then
  echo "Creating service account $SERVICE_ACCOUNT..."
  gcloud iam service-accounts create $SERVICE_ACCOUNT \
    --display-name="NovaConnect UAC Service Account"
else
  echo "Service account $SERVICE_ACCOUNT already exists"
fi

# Grant required permissions to the service account
echo "Granting required permissions to service account $SERVICE_ACCOUNT..."
gcloud projects add-iam-policy-binding $GOOGLE_CLOUD_PROJECT \
  --member="serviceAccount:$SERVICE_ACCOUNT@$GOOGLE_CLOUD_PROJECT.iam.gserviceaccount.com" \
  --role="roles/monitoring.metricWriter"

gcloud projects add-iam-policy-binding $GOOGLE_CLOUD_PROJECT \
  --member="serviceAccount:$SERVICE_ACCOUNT@$GOOGLE_CLOUD_PROJECT.iam.gserviceaccount.com" \
  --role="roles/logging.logWriter"

gcloud projects add-iam-policy-binding $GOOGLE_CLOUD_PROJECT \
  --member="serviceAccount:$SERVICE_ACCOUNT@$GOOGLE_CLOUD_PROJECT.iam.gserviceaccount.com" \
  --role="roles/secretmanager.secretAccessor"

# Build the Docker image
echo "Building Docker image..."
docker build -t gcr.io/$GOOGLE_CLOUD_PROJECT/novafuse-uac:$VERSION .

# Push the Docker image to Google Container Registry
echo "Pushing Docker image to Google Container Registry..."
docker push gcr.io/$GOOGLE_CLOUD_PROJECT/novafuse-uac:$VERSION

# Apply Kubernetes manifests
echo "Applying Kubernetes manifests..."
kubectl apply -f kubernetes/namespace.yaml
kubectl apply -f kubernetes/configmap.yaml
kubectl apply -f kubernetes/secret.yaml
kubectl apply -f kubernetes/deployment.yaml
kubectl apply -f kubernetes/service.yaml
kubectl apply -f kubernetes/ingress.yaml
kubectl apply -f kubernetes/hpa.yaml

# Wait for deployment to complete
echo "Waiting for deployment to complete..."
kubectl rollout status deployment/novafuse-uac -n $NAMESPACE

# Get the external IP
echo "Getting external IP..."
EXTERNAL_IP=$(kubectl get service novafuse-uac -n $NAMESPACE -o jsonpath='{.status.loadBalancer.ingress[0].ip}')

echo "NovaConnect UAC has been deployed successfully!"
echo "External IP: $EXTERNAL_IP"
echo "Version: $VERSION"
