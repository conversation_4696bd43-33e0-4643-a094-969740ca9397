apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: novafuse-uac-ingress
  namespace: novafuse-production
  annotations:
    kubernetes.io/ingress.class: "gce"
    kubernetes.io/ingress.global-static-ip-name: "novafuse-production-ip"
    networking.gke.io/managed-certificates: "novafuse-production-cert"
    networking.gke.io/v1beta1.FrontendConfig: "novafuse-production-frontend-config"
    cloud.google.com/security-policy: "novafuse-production-security-policy"
spec:
  rules:
  - host: api.novafuse.io
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: novafuse-uac-service
            port:
              number: 80
