74933db1d7da4f4d3842a396e58f055b
/**
 * Unit tests for the Connector API
 */

const {
  expect
} = require('chai');
const sinon = require('sinon');
const supertest = require('supertest');

// Import the modules to test
const connectorApi = require('../../api/connector-api');
const connectorRegistry = require('../../registry/connector-registry');
const authenticationManager = require('../../auth/authentication-manager');
const connectorExecutor = require('../../executor/connector-executor');
describe('Connector API', () => {
  let app;
  let request;
  let sandbox;
  beforeEach(() => {
    // Create a sinon sandbox for stubs
    sandbox = sinon.createSandbox();

    // Stub the dependencies
    sandbox.stub(connectorRegistry, 'initialize').resolves();
    sandbox.stub(authenticationManager, 'initialize').resolves();
    sandbox.stub(connectorExecutor, 'initialize').resolves();

    // Get the Express app
    app = connectorApi.app;
    request = supertest(app);
  });
  afterEach(() => {
    // Restore the stubs
    sandbox.restore();
  });
  describe('initialize()', () => {
    it('should initialize dependencies and start the server', async () => {
      // Stub the app.listen method
      const listenStub = sandbox.stub(app, 'listen').callsFake((port, callback) => {
        callback();
        return {
          close: () => {}
        };
      });

      // Call the initialize method
      const result = await connectorApi.initialize();

      // Verify the result
      expect(result).to.be.true;

      // Verify the dependencies were initialized
      expect(connectorRegistry.initialize.calledOnce).to.be.true;
      expect(authenticationManager.initialize.calledOnce).to.be.true;
      expect(connectorExecutor.initialize.calledOnce).to.be.true;

      // Verify the server was started
      expect(listenStub.calledOnce).to.be.true;
    });
    it('should handle initialization errors', async () => {
      // Stub the connectorRegistry.initialize method to throw an error
      connectorRegistry.initialize.rejects(new Error('Initialization error'));
      try {
        // Call the initialize method
        await connectorApi.initialize();
        // If we get here, the test should fail
        expect.fail('Expected initialize to throw an error');
      } catch (error) {
        // Verify the error
        expect(error.message).to.equal('Initialization error');
      }
    });
  });
  describe('GET /health', () => {
    it('should return a 200 status code', async () => {
      // Make a request to the health endpoint
      const response = await request.get('/health');

      // Verify the response
      expect(response.status).to.equal(200);
      expect(response.body).to.deep.equal({
        status: 'ok'
      });
    });
  });
  describe('GET /connectors', () => {
    it('should return a list of connectors', async () => {
      // Stub the connectorRegistry.getAllConnectors method
      const connectors = [{
        id: '1',
        name: 'Test Connector',
        type: 'http',
        description: 'Test connector for unit tests',
        status: 'active',
        authentication: {
          fields: {
            apiKey: {
              type: 'string',
              label: 'API Key',
              required: true,
              sensitive: true,
              default: 'test-api-key'
            }
          }
        }
      }];
      sandbox.stub(connectorRegistry, 'getAllConnectors').returns(connectors);

      // Make a request to the connectors endpoint
      const response = await request.get('/connectors');

      // Verify the response
      expect(response.status).to.equal(200);
      expect(response.body).to.be.an('array');
      expect(response.body).to.have.lengthOf(1);
      expect(response.body[0].id).to.equal('1');
      expect(response.body[0].name).to.equal('Test Connector');
      expect(response.body[0].authentication.fields.apiKey.default).to.be.undefined;
    });
    it('should filter connectors by category', async () => {
      // Stub the connectorRegistry.getConnectorsByCategory method
      const connectors = [{
        id: '1',
        name: 'Test Connector',
        type: 'http',
        category: 'test',
        description: 'Test connector for unit tests',
        status: 'active',
        authentication: {
          fields: {}
        }
      }];
      sandbox.stub(connectorRegistry, 'getConnectorsByCategory').returns(connectors);

      // Make a request to the connectors endpoint with a category filter
      const response = await request.get('/connectors?category=test');

      // Verify the response
      expect(response.status).to.equal(200);
      expect(response.body).to.be.an('array');
      expect(response.body).to.have.lengthOf(1);
      expect(response.body[0].id).to.equal('1');
      expect(response.body[0].category).to.equal('test');
    });
    it('should search connectors by query', async () => {
      // Stub the connectorRegistry.searchConnectors method
      const connectors = [{
        id: '1',
        name: 'Test Connector',
        type: 'http',
        description: 'Test connector for unit tests',
        status: 'active',
        authentication: {
          fields: {}
        }
      }];
      sandbox.stub(connectorRegistry, 'searchConnectors').returns(connectors);

      // Make a request to the connectors endpoint with a search query
      const response = await request.get('/connectors?query=test');

      // Verify the response
      expect(response.status).to.equal(200);
      expect(response.body).to.be.an('array');
      expect(response.body).to.have.lengthOf(1);
      expect(response.body[0].id).to.equal('1');
      expect(response.body[0].name).to.equal('Test Connector');
    });
    it('should handle errors', async () => {
      // Stub the connectorRegistry.getAllConnectors method to throw an error
      sandbox.stub(connectorRegistry, 'getAllConnectors').throws(new Error('Test error'));

      // Make a request to the connectors endpoint
      const response = await request.get('/connectors');

      // Verify the response
      expect(response.status).to.equal(500);
      expect(response.body).to.have.property('error');
      expect(response.body.error).to.equal('Test error');
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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