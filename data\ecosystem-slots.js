/**
 * NovaFuse Partner Network
 *
 * This file contains sample data for the NovaFuse Partner Network.
 * Each collaborative role represents a specific compliance domain, industry, or use case.
 */

const ecosystemSlots = [
  // Regulatory Compliance Roles (1-20)
  {
    id: 'RC-01',
    name: 'GDPR Compliance',
    description: 'General Data Protection Regulation compliance for EU data subjects',
    category: 'Regulatory Compliance',
    tags: ['Privacy', 'EU', 'Data Protection']
  },
  {
    id: 'RC-02',
    name: 'HIPAA Compliance',
    description: 'Health Insurance Portability and Accountability Act compliance for healthcare',
    category: 'Regulatory Compliance',
    tags: ['Healthcare', 'US', 'PHI']
  },
  {
    id: 'RC-03',
    name: 'CCPA Compliance',
    description: 'California Consumer Privacy Act compliance for California residents',
    category: 'Regulatory Compliance',
    tags: ['Privacy', 'California', 'Consumer Rights']
  },
  {
    id: 'RC-04',
    name: 'PCI DSS Compliance',
    description: 'Payment Card Industry Data Security Standard for payment processing',
    category: 'Regulatory Compliance',
    tags: ['Payments', 'Financial', 'Security']
  },
  {
    id: 'RC-05',
    name: 'SOC 2 Compliance',
    description: 'Service Organization Control 2 for service providers',
    category: 'Regulatory Compliance',
    tags: ['Service Providers', 'Trust', 'Security']
  },

  // Security Compliance Roles (21-40)
  {
    id: 'SC-01',
    name: 'NIST 800-53 Controls',
    description: 'NIST Special Publication 800-53 security controls',
    category: 'Security Compliance',
    tags: ['Federal', 'Security Controls', 'Risk Management']
  },
  {
    id: 'SC-02',
    name: 'ISO 27001 Controls',
    description: 'ISO/IEC 27001 information security management',
    category: 'Security Compliance',
    tags: ['International', 'ISMS', 'Security Management']
  },
  {
    id: 'SC-03',
    name: 'CIS Controls',
    description: 'Center for Internet Security Critical Security Controls',
    category: 'Security Compliance',
    tags: ['Security', 'Best Practices', 'Cybersecurity']
  },
  {
    id: 'SC-04',
    name: 'CMMC Compliance',
    description: 'Cybersecurity Maturity Model Certification for defense contractors',
    category: 'Security Compliance',
    tags: ['Defense', 'Government', 'Maturity Model']
  },
  {
    id: 'SC-05',
    name: 'FedRAMP Compliance',
    description: 'Federal Risk and Authorization Management Program for cloud services',
    category: 'Security Compliance',
    tags: ['Federal', 'Cloud', 'Authorization']
  },

  // Privacy Management Roles (41-60)
  {
    id: 'PM-01',
    name: 'Data Subject Rights',
    description: 'Management of data subject access requests and rights',
    category: 'Privacy Management',
    tags: ['DSAR', 'Privacy Rights', 'Data Subjects']
  },
  {
    id: 'PM-02',
    name: 'Consent Management',
    description: 'Collection and management of user consent',
    category: 'Privacy Management',
    tags: ['Consent', 'Preferences', 'Opt-in/Opt-out']
  },
  {
    id: 'PM-03',
    name: 'Privacy Impact Assessment',
    description: 'Assessment of privacy risks for new projects and systems',
    category: 'Privacy Management',
    tags: ['PIA', 'Risk Assessment', 'Privacy by Design']
  },
  {
    id: 'PM-04',
    name: 'Data Mapping',
    description: 'Mapping of data flows and processing activities',
    category: 'Privacy Management',
    tags: ['Data Inventory', 'Processing Activities', 'Data Flows']
  },
  {
    id: 'PM-05',
    name: 'Privacy Notices',
    description: 'Management of privacy policies and notices',
    category: 'Privacy Management',
    tags: ['Privacy Policy', 'Transparency', 'Notices']
  },

  // Industry-Specific Roles (61-80)
  {
    id: 'IS-01',
    name: 'Healthcare Compliance',
    description: 'Compliance solutions for healthcare organizations',
    category: 'Industry-Specific',
    tags: ['Healthcare', 'Medical', 'Patient Data']
  },
  {
    id: 'IS-02',
    name: 'Financial Services Compliance',
    description: 'Compliance solutions for banks and financial institutions',
    category: 'Industry-Specific',
    tags: ['Financial', 'Banking', 'Investment']
  },
  {
    id: 'IS-03',
    name: 'Retail Compliance',
    description: 'Compliance solutions for retail and e-commerce',
    category: 'Industry-Specific',
    tags: ['Retail', 'E-commerce', 'Consumer Data']
  },
  {
    id: 'IS-04',
    name: 'Education Compliance',
    description: 'Compliance solutions for educational institutions',
    category: 'Industry-Specific',
    tags: ['Education', 'FERPA', 'Student Data']
  },
  {
    id: 'IS-05',
    name: 'Government Compliance',
    description: 'Compliance solutions for government agencies',
    category: 'Industry-Specific',
    tags: ['Government', 'Public Sector', 'Agencies']
  },

  // Emerging Technology Roles (81-100)
  {
    id: 'ET-01',
    name: 'AI Governance',
    description: 'Governance and compliance for artificial intelligence systems',
    category: 'Emerging Technology',
    tags: ['AI', 'Machine Learning', 'Algorithms']
  },
  {
    id: 'ET-02',
    name: 'Blockchain Compliance',
    description: 'Compliance solutions for blockchain and distributed ledger technologies',
    category: 'Emerging Technology',
    tags: ['Blockchain', 'DLT', 'Cryptocurrency']
  },
  {
    id: 'ET-03',
    name: 'IoT Security Compliance',
    description: 'Compliance and security for Internet of Things devices',
    category: 'Emerging Technology',
    tags: ['IoT', 'Connected Devices', 'Embedded Systems']
  },
  {
    id: 'ET-04',
    name: 'Cloud Compliance',
    description: 'Compliance solutions for cloud environments',
    category: 'Emerging Technology',
    tags: ['Cloud', 'SaaS', 'IaaS']
  },
  {
    id: 'ET-05',
    name: 'Quantum-Safe Compliance',
    description: 'Compliance solutions for quantum computing threats',
    category: 'Emerging Technology',
    tags: ['Quantum', 'Cryptography', 'Post-Quantum']
  },

  // Risk Management Roles (101-120)
  {
    id: 'RM-01',
    name: 'Third-Party Risk Management',
    description: 'Management of risks associated with third-party vendors',
    category: 'Risk Management',
    tags: ['Vendors', 'Supply Chain', 'Third Parties']
  },
  {
    id: 'RM-02',
    name: 'Enterprise Risk Management',
    description: 'Holistic approach to managing organizational risks',
    category: 'Risk Management',
    tags: ['ERM', 'Strategic Risk', 'Operational Risk']
  },
  {
    id: 'RM-03',
    name: 'Cyber Risk Quantification',
    description: 'Quantitative assessment of cybersecurity risks',
    category: 'Risk Management',
    tags: ['Quantification', 'Cyber Risk', 'Financial Impact']
  },
  {
    id: 'RM-04',
    name: 'Business Continuity',
    description: 'Planning and management for business continuity',
    category: 'Risk Management',
    tags: ['BCP', 'Disaster Recovery', 'Resilience']
  },
  {
    id: 'RM-05',
    name: 'Compliance Risk Management',
    description: 'Management of risks related to regulatory compliance',
    category: 'Risk Management',
    tags: ['Compliance Risk', 'Regulatory Risk', 'Legal Risk']
  },

  // ESG Roles (121-140)
  {
    id: 'ESG-01',
    name: 'Environmental Compliance',
    description: 'Compliance with environmental regulations and standards',
    category: 'ESG',
    tags: ['Environmental', 'Sustainability', 'Climate']
  },
  {
    id: 'ESG-02',
    name: 'Social Responsibility',
    description: 'Management of social responsibility initiatives and compliance',
    category: 'ESG',
    tags: ['Social', 'Human Rights', 'Labor Standards']
  },
  {
    id: 'ESG-03',
    name: 'Corporate Governance',
    description: 'Governance structures and processes for organizations',
    category: 'ESG',
    tags: ['Governance', 'Board Oversight', 'Ethics']
  },
  {
    id: 'ESG-04',
    name: 'ESG Reporting',
    description: 'Reporting on environmental, social, and governance metrics',
    category: 'ESG',
    tags: ['Reporting', 'Disclosure', 'Transparency']
  },
  {
    id: 'ESG-05',
    name: 'Sustainable Finance',
    description: 'Compliance with sustainable finance regulations and standards',
    category: 'ESG',
    tags: ['Finance', 'Green Bonds', 'Sustainable Investing']
  },

  // Special Roles (141-144)
  {
    id: 'SP-01',
    name: 'Cross-Framework Mapping',
    description: 'Mapping between different compliance frameworks and standards',
    category: 'Special',
    tags: ['Mapping', 'Crosswalk', 'Harmonization']
  },
  {
    id: 'SP-02',
    name: 'Compliance Automation',
    description: 'Automation of compliance processes and workflows',
    category: 'Special',
    tags: ['Automation', 'Workflows', 'Efficiency']
  },
  {
    id: 'SP-03',
    name: 'Compliance Analytics',
    description: 'Analytics and insights for compliance data',
    category: 'Special',
    tags: ['Analytics', 'Insights', 'Dashboards']
  },
  {
    id: 'SP-04',
    name: 'Hybrid Innovation',
    description: 'Special collaborative role for partners who excel at both services and product development',
    category: 'Special',
    tags: ['Innovation', 'Hybrid', 'Excellence']
  }
];

export default ecosystemSlots;
