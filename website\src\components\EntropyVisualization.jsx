import React, { useMemo, useEffect, useRef, useState, useCallback } from 'react';
import PropTypes from 'prop-types';
import { 
  LineChart, Line, XAxis, YAxis, CartesianGrid, 
  Tooltip, Legend, ResponsiveContainer, Area, 
  AreaChart, ReferenceLine, Label
} from 'recharts';
import * as d3 from 'd3';
import { Button, ToggleButtonGroup, ToggleButton } from '@novafuse/nova-ui';
import { useNovaCore } from '@novafuse/nova-core';
import { QuantumEntropyViz3D } from './quantum/QuantumEntropyViz3D';
import { useQuantumVisualization } from '../hooks/useQuantumVisualization';
import '../styles/EntropyVisualization.css';

const WAVE_ANIMATION_DURATION = 2000; // ms

const EntropyVisualization = ({ 
  entropyHistory = [], 
  coherence = 1, 
  isQuantumActive,
  onModeChange,
  className = ''
}) => {
  const { novaTrack } = useNovaCore();
  const waveRef = useRef(null);
  const animationFrame = useRef(null);
  const lastUpdateTime = useRef(0);
  const [viewMode, setViewMode] = useState('3d'); // '3d' or 'chart'
  
  // Initialize quantum visualization
  const {
    quantumState,
    metrics,
    isAnimating,
    toggleAnimation,
    randomizeState,
    updateMetric,
    formattedMetrics
  } = useQuantumVisualization({
    autoAnimate: true,
    qubits: 3,
  });
  
  // Process history data for visualization
  const { chartData, stats } = useMemo(() => {
    if (entropyHistory.length === 0) return { chartData: [], stats: {} };
    
    // Prepare data for the chart
    const processedData = entropyHistory.map((entry, index) => ({
      timestamp: entry.timestamp || Date.now(),
      entropy: entry.entropy || 0,
      mode: entry.mode || 'classical',
      coherence: entry.coherence || 1,
      isAnomaly: entry.anomaly?.isAnomaly || false,
      id: entry.id || `entry-${index}`,
      quantumMetrics: entry.quantumMetrics || null,
    });
    
    // Calculate statistics
    const quantumEntries = processedData.filter(d => d.mode === 'quantum');
    const classicalEntries = processedData.filter(d => d.mode === 'classical');
    
    const stats = {
      total: processedData.length,
      quantum: quantumEntries.length,
      classical: classicalEntries.length,
      avgQuantum: quantumEntries.length > 0 
        ? d3.mean(quantumEntries, d => d.entropy) 
        : 0,
      avgClassical: classicalEntries.length > 0 
        ? d3.mean(classicalEntries, d => d.entropy) 
        : 0,
      anomalies: processedData.filter(d => d.isAnomaly).length,
      lastUpdated: processedData[processedData.length - 1]?.timestamp || null,
      currentCoherence: metrics.coherence,
      currentEntropy: metrics.entropy,
      currentEntanglement: metrics.entanglement,
      qScore: metrics.qScore,
    };
    
    return { chartData: processedData, stats };
  }, [entropyHistory, metrics]);
  
  // Sync quantum state with parent component
  useEffect(() => {
    if (isQuantumActive && quantumState) {
      onModeChange?.('quantum', {
        entropy: metrics.entropy,
        coherence: metrics.coherence,
        quantumState,
        metrics: formattedMetrics,
      });
    }
  }, [isQuantumActive, quantumState, metrics, formattedMetrics, onModeChange]);
  
  // Handle view mode change
  const handleViewModeChange = (event, newViewMode) => {
    if (newViewMode !== null) {
      setViewMode(newViewMode);
      novaTrack('entropy_viz_view_changed', {
        view: newViewMode,
        timestamp: Date.now(),
      });
    }
  };
  
  // Animate quantum wave effect
  useEffect(() => {
    if (!isQuantumActive || !waveRef.current) return;
    
    let startTime = null;
    const wave = waveRef.current;
    
    const animate = (timestamp) => {
      if (!startTime) startTime = timestamp;
      const elapsed = timestamp - startTime;
      
      // Update wave animation
      if (elapsed < WAVE_ANIMATION_DURATION) {
        const progress = elapsed / WAVE_ANIMATION_DURATION;
        const amplitude = 0.1 * coherence;
        const frequency = 2 * Math.PI * (coherence * 2);
        
        // Generate wave path
        const points = [];
        const width = wave.clientWidth;
        const height = wave.clientHeight;
        const segments = 50;
        
        for (let i = 0; i <= segments; i++) {
          const x = (i / segments) * width;
          const t = (i / segments) * 2 * Math.PI + progress * 2 * Math.PI;
          const y = height / 2 + Math.sin(t * frequency) * amplitude * height;
          points.push(`${x},${y}`);
        }
        
        // Update wave path
        wave.setAttribute('d', `M${points.join(' L')} V${height} H0 Z`);
        animationFrame.current = requestAnimationFrame(animate);
      } else {
        // Reset animation
        startTime = null;
        animationFrame.current = requestAnimationFrame(animate);
      }
    };
    
    animationFrame.current = requestAnimationFrame(animate);
    
    return () => {
      if (animationFrame.current) {
        cancelAnimationFrame(animationFrame.current);
      }
    };
  }, [isQuantumActive, coherence]);
  
  // Format timestamp for display
  const formatTime = (timestamp) => {
    if (!timestamp) return '';
    return new Date(timestamp).toLocaleTimeString();
  };
  
  // Generate gradient ID for the area chart
  const gradientId = 'entropy-gradient';
  
  // Toggle between 3D and chart view
  const renderView = () => {
    if (viewMode === '3d') {
      return (
        <div className="quantum-3d-container">
          <QuantumEntropyViz3D
            entropy={metrics.entropy}
            coherence={metrics.coherence}
            entanglement={metrics.entanglement}
            quantumState={quantumState}
          />
        </div>
      );
    }

    // Original chart view
    return (
      <div className="chart-container">
        <ResponsiveContainer width="100%" height={300}>
          <AreaChart 
            data={chartData} 
            margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
          >
            <defs>
              <linearGradient id="quantumGradient" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="#8b5cf6" stopOpacity={0.8}/>
                <stop offset="95%" stopColor="#8b5cf6" stopOpacity={0.1}/>
              </linearGradient>
              <linearGradient id="classicalGradient" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="#4b5563" stopOpacity={0.8}/>
                <stop offset="95%" stopColor="#4b5563" stopOpacity={0.1}/>
              </linearGradient>
            </defs>
            <CartesianGrid strokeDasharray="3 3" stroke="#2d3748" />
            <XAxis 
              dataKey="timestamp" 
              tickFormatter={(ts) => new Date(ts).toLocaleTimeString()}
              stroke="#a0aec0"
              tick={{ fill: '#a0aec0' }}
            />
            <YAxis 
              domain={[0, 1]}
              stroke="#a0aec0"
              tick={{ fill: '#a0aec0' }}
            />
            <Tooltip 
              contentStyle={{ 
                backgroundColor: '#1e293b', 
                border: '1px solid #334155',
                borderRadius: '6px',
                color: '#f8fafc',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
              }}
              labelFormatter={(value) => `Time: ${new Date(value).toLocaleTimeString()}`}
              formatter={(value, name, props) => {
                if (name === 'entropy') {
                  return [value.toFixed(4), 'Entropy'];
                }
                return [value, name];
              }}
            />
            <Area 
              type="monotone" 
              dataKey="entropy" 
              name="Entropy Level"
              stroke={isQuantumActive ? "#8b5cf6" : "#4b5563"}
              fillOpacity={1} 
              fill={isQuantumActive ? "url(#quantumGradient)" : "url(#classicalGradient)"}
              activeDot={{ r: 6, stroke: '#fff', strokeWidth: 2 }}
            />
            {isQuantumActive && (
              <ReferenceLine 
                y={stats.avgQuantum} 
                label={{
                  value: `Avg: ${stats.avgQuantum.toFixed(2)}`, 
                  position: 'top',
                  fill: '#8b5cf6',
                  fontSize: 12
                }} 
                stroke="#8b5cf6" 
                strokeDasharray="3 3"
                strokeWidth={1.5}
              />
            )}
          </AreaChart>
        </ResponsiveContainer>
      </div>
    );
  };

  return (
    <div className={`entropy-visualization ${className}`}>
      <div className="visualization-header">
        <h3>Quantum Entropy Visualization</h3>
        <div className="visualization-controls">
          <ToggleButtonGroup
            value={viewMode}
            exclusive
            onChange={handleViewModeChange}
            aria-label="visualization view mode"
            size="small"
          >
            <ToggleButton value="3d" aria-label="3D view">
              3D View
            </ToggleButton>
            <ToggleButton value="chart" aria-label="Chart view">
              Chart View
            </ToggleButton>
          </ToggleButtonGroup>
          
          <div className="quantum-indicator">
            <span className={`status-dot ${isQuantumActive ? 'quantum' : 'classical'}`} />
            <span>{isQuantumActive ? 'Quantum Mode' : 'Classical Mode'}</span>
          </div>
        </div>
      </div>
      
      {chartData.length === 0 ? (
        <div className="no-data">
          <p>No entropy data available</p>
          <p>Submit a task to see quantum measurements</p>
        </div>
      ) : (
        <>
          {renderView()}
          
          <div className="quantum-stats">
            <div className="stat-card">
              <div className="stat-label">Quantum Score</div>
              <div className="stat-value quantum">{formattedMetrics.qScore}</div>
              <div className="stat-description">Overall quantum coherence</div>
            </div>
            
            <div className="stat-card">
              <div className="stat-label">Entropy</div>
              <div className="stat-value">{formattedMetrics.entropy}</div>
              <div className="stat-description">System disorder</div>
            </div>
            
            <div className="stat-card">
              <div className="stat-label">Coherence</div>
              <div className="stat-value">{formattedMetrics.coherence}</div>
              <div className="stat-description">Quantum state stability</div>
            </div>
            
            <div className="stat-card">
              <div className="stat-label">Entanglement</div>
              <div className="stat-value">{formattedMetrics.entanglement}</div>
              <div className="stat-description">Qubit correlation</div>
            </div>
          </div>
          
          <div className="quantum-actions">
            <Button
              variant="outlined"
              onClick={toggleAnimation}
              startIcon={isAnimating ? '⏸' : '▶'}
              size="small"
            >
              {isAnimating ? 'Pause' : 'Animate'}
            </Button>
            
            <Button
              variant="outlined"
              onClick={randomizeState}
              startIcon="🎲"
              size="small"
              disabled={isAnimating}
            >
              Randomize
            </Button>
          </div>
        </>
      )}
    </div>
  );
};

EntropyVisualization.propTypes = {
  entropyHistory: PropTypes.arrayOf(PropTypes.shape({
    entropy: PropTypes.number.isRequired,
    timestamp: PropTypes.number.isRequired,
    mode: PropTypes.oneOf(['quantum', 'classical']),
    coherence: PropTypes.number,
    anomaly: PropTypes.shape({
      isAnomaly: PropTypes.bool,
      confidence: PropTypes.number,
      reason: PropTypes.string
    }),
    quantumMetrics: PropTypes.object
  })),
  coherence: PropTypes.number,
  isQuantumActive: PropTypes.bool,
  onModeChange: PropTypes.func,
  className: PropTypes.string
};

EntropyVisualization.defaultProps = {
  entropyHistory: [],
  coherence: 1,
  isQuantumActive: false,
  onModeChange: () => {},
  className: ''
};

export default React.memo(EntropyVisualization);
                  type="monotone" 
                  dataKey="value" 
                  stroke="#8884d8" 
                  activeDot={{ r: 8 }} 
                  strokeWidth={3}
                  name="Entropy Value"
                />
              </LineChart>
            </ResponsiveContainer>
            
            <div className="entropy-values">
              {data.map((item, index) => (
                <div key={index} className={`entropy-value ${item.type}`}>
                  <span className="label">{item.name}:</span>
                  <span className={`value ${getRiskLevel(item.value)}`}>
                    {item.value.toFixed(4)}
                  </span>
                  <span className="risk-level">
                    {getRiskLevel(item.value).toUpperCase()} RISK
                  </span>
                </div>
              ))}
            </div>
          </div>
        ) : (
          <div className="no-data">
            <p>Submit a task to see entropy analysis</p>
          </div>
        )}
      </div>
      
      <div className="legend">
        <div className="legend-item">
          <span className="dot quantum"></span>
          <span>Quantum Entropy</span>
        </div>
        <div className="legend-item">
          <span className="dot classical"></span>
          <span>Classical Entropy</span>
        </div>
      </div>
    </div>
  );
};

export default EntropyVisualization;
