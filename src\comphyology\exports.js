/**
 * Comphyology (Ψᶜ) Exports
 *
 * This module exports all Comphyology components for easy access.
 */

const { ComphyologyCore } = require('./index');
const ComphyologyEnhancedTrinityCSDEEngine = require('./trinity_integration');
const ComphyologyEnhancedQuantumStateInference = require('./quantum_integration');
const AdvancedComphyologyQuantumInference = require('./quantum_inference_enhanced');
const ComphyologyVisualization = require('./visualization');
const {
  generateVisualizationHTML,
  generateVisualizationCSS
} = require('./visualization_renderer');
const {
  generateDemoPage,
  runDemo
} = require('./demo');
const ComphyologyNovaVisionIntegration = require('./novavision_integration');
const ComphyologyRealTimeDashboard = require('./real_time_dashboard');
const ComphyologyNovaConnectDashboard = require('./nova_connect_dashboard');
const { ComphyologicalCosmos } = require('./ComphyologicalCosmos');
const { createDivineFirewall } = require('./divine-firewall');
const domainComponents = require('./domain');
const { ResonanceOptimizer, RESONANCE_PATTERNS } = require('./resonance/ResonanceOptimizer');
const { QuantumResilienceSystem, QUANTUM_ATTACK_TYPES } = require('./quantum/QuantumResilienceSystem');
const {
  SpiritualIntegrityValidator,
  FRUITS_OF_SPIRIT,
  ANTI_FRUITS,
  SPIRITUAL_ATTACK_TYPES,
  SCRIPTURE_SHIELD
} = require('./spiritual/SpiritualIntegrityValidator');
const { UnifiedDefenseLayer, DEFENSE_LAYER_TYPES } = require('./UnifiedDefenseLayer');
const DataIntegrationManager = require('./data_integration/manager');
const NovaConnectAdapter = require('./data_integration/nova_connect_adapter');
const {
  BaseDataConnector,
  NovaShieldConnector,
  NovaTrackConnector,
  NovaCoreConnector
} = require('./data_integration/connectors');
const {
  QuantumStateInferenceLayer,
  QuantumStateInferenceEngine,
  ResonanceConnector,
  CoherenceFieldGenerator
} = require('./quantum_inference');
const {
  UniversalRippleStack,
  NovaConnectRippleAdapter,
  NovaThinkRippleAdapter,
  NovaPulseRippleAdapter,
  NovaFlowRippleAdapter
} = require('./universal_ripple');
const EnhancedMetricsDashboard = require('../novavision/components/enhanced-metrics-dashboard');
const QuantumResilienceDashboard = require('../novavision/components/quantum-resilience-dashboard');

module.exports = {
  // Core components
  ComphyologyCore,
  ComphyologyEnhancedTrinityCSDEEngine,
  ComphyologyEnhancedQuantumStateInference,
  AdvancedComphyologyQuantumInference,

  // Visualization components
  ComphyologyVisualization,
  generateVisualizationHTML,
  generateVisualizationCSS,

  // NovaVision integration
  ComphyologyNovaVisionIntegration,

  // Real-time components
  ComphyologyRealTimeDashboard,
  ComphyologyNovaConnectDashboard,
  DataIntegrationManager,

  // NovaConnect integration
  NovaConnectAdapter,

  // Data connectors
  BaseDataConnector,
  NovaShieldConnector,
  NovaTrackConnector,
  NovaCoreConnector,

  // Quantum State Inference Layer
  QuantumStateInferenceLayer,
  QuantumStateInferenceEngine,
  ResonanceConnector,
  CoherenceFieldGenerator,

  // Divine Firewall and Domain Containerization
  ComphyologicalCosmos,
  createDivineFirewall,

  // Domain components
  ...domainComponents,

  // Resonance Optimization
  ResonanceOptimizer,
  RESONANCE_PATTERNS,

  // Quantum Resilience
  QuantumResilienceSystem,
  QUANTUM_ATTACK_TYPES,

  // Spiritual Integrity
  SpiritualIntegrityValidator,
  FRUITS_OF_SPIRIT,
  ANTI_FRUITS,
  SPIRITUAL_ATTACK_TYPES,
  SCRIPTURE_SHIELD,

  // Unified Defense
  UnifiedDefenseLayer,
  DEFENSE_LAYER_TYPES,

  // Universal Ripple Stack
  UniversalRippleStack,
  NovaConnectRippleAdapter,
  NovaThinkRippleAdapter,
  NovaPulseRippleAdapter,
  NovaFlowRippleAdapter,

  // Dashboard components
  EnhancedMetricsDashboard,
  QuantumResilienceDashboard,

  // Demo components
  generateDemoPage,
  runDemo
};
