{"name": "aeonix-divine-dashboard", "version": "1.0.0-DIVINE_VISUALIZATION", "description": "AEONIX Divine Intelligence Dashboard - React Interface for 9-Engine Market Analysis", "private": true, "scripts": {"dev": "next dev -p 3141", "build": "next build", "start": "next start -p 3141", "lint": "next lint", "divine": "npm run dev"}, "dependencies": {"next": "14.0.3", "react": "18.2.0", "react-dom": "18.2.0", "tailwindcss": "3.3.6", "autoprefixer": "10.4.16", "postcss": "8.4.32", "@headlessui/react": "1.7.17", "@heroicons/react": "2.0.18", "d3": "7.8.5", "chart.js": "4.4.0", "react-chartjs-2": "5.2.0", "three": "0.158.0", "@react-three/fiber": "8.15.11", "@react-three/drei": "9.88.13", "framer-motion": "10.16.5", "axios": "1.6.2", "socket.io-client": "4.7.4", "@monaco-editor/react": "4.6.0", "react-hot-toast": "2.4.1", "lucide-react": "0.294.0", "recharts": "2.8.0", "react-use-websocket": "4.5.0", "clsx": "2.0.0", "class-variance-authority": "0.7.0"}, "devDependencies": {"@types/node": "20.9.0", "@types/react": "18.2.37", "@types/react-dom": "18.2.15", "@types/d3": "7.4.3", "@types/three": "0.158.3", "typescript": "5.2.2", "eslint": "8.53.0", "eslint-config-next": "14.0.3"}, "keywords": ["aeonix", "divine-intelligence", "market-analysis", "<PERSON><PERSON><PERSON><PERSON>", "prophecy", "react", "dashboard"], "author": "AEONIX Divine Intelligence", "license": "MIT"}