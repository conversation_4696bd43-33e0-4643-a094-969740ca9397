/**
 * Enhanced Connector Management Page
 * 
 * This page provides an improved interface for managing API connectors.
 */

import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Button, 
  Container, 
  Dialog, 
  DialogActions, 
  DialogContent, 
  DialogContentText, 
  DialogTitle, 
  Typography,
  Alert,
  Snackbar,
  Paper,
  Fade
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import RefreshIcon from '@mui/icons-material/Refresh';
import DashboardLayout from '../../layouts/DashboardLayout';
import EnhancedConnectorList from '../../components/management/EnhancedConnectorList';
import { useRouter } from 'next/router';

const ManagementPage = () => {
  const router = useRouter();
  const [connectors, setConnectors] = useState([]);
  const [loading, setLoading] = useState(true);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [connectorToDelete, setConnectorToDelete] = useState(null);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'info'
  });
  
  const fetchConnectors = async () => {
    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock data
      const mockConnectors = [
        {
          id: 'conn1',
          name: 'GitHub API Connector',
          description: 'Connect to GitHub API for repository management',
          category: 'development',
          type: 'api',
          version: '1.0.0',
          status: 'active',
          created: '2023-01-15T12:00:00Z',
          updated: '2023-03-20T14:30:00Z',
          baseUrl: 'https://api.github.com',
          usage: 1250,
          successRate: '99.8%',
          avgResponseTime: '120ms',
          lastUsed: '2023-05-25T09:15:00Z',
          tags: ['github', 'development', 'api']
        },
        {
          id: 'conn2',
          name: 'Jira API Connector',
          description: 'Connect to Jira for issue tracking and project management',
          category: 'development',
          type: 'api',
          version: '1.2.0',
          status: 'active',
          created: '2023-02-10T09:15:00Z',
          updated: '2023-04-05T11:45:00Z',
          baseUrl: 'https://your-domain.atlassian.net/rest/api/3',
          usage: 3420,
          successRate: '99.5%',
          avgResponseTime: '150ms',
          lastUsed: '2023-05-26T10:30:00Z',
          tags: ['jira', 'development', 'project-management']
        },
        {
          id: 'conn3',
          name: 'Salesforce API Connector',
          description: 'Connect to Salesforce CRM',
          category: 'sales',
          type: 'api',
          version: '2.0.1',
          status: 'active',
          created: '2023-01-05T10:30:00Z',
          updated: '2023-05-12T16:20:00Z',
          baseUrl: 'https://your-instance.salesforce.com/services/data/v56.0',
          usage: 5680,
          successRate: '99.9%',
          avgResponseTime: '90ms',
          lastUsed: '2023-05-26T14:45:00Z',
          tags: ['salesforce', 'crm', 'sales']
        },
        {
          id: 'conn4',
          name: 'Google Analytics API Connector',
          description: 'Connect to Google Analytics for website analytics',
          category: 'analytics',
          type: 'api',
          version: '0.9.0',
          status: 'draft',
          created: '2023-04-20T08:45:00Z',
          updated: '2023-04-20T08:45:00Z',
          baseUrl: 'https://analyticsdata.googleapis.com/v1beta',
          usage: 320,
          successRate: '98.2%',
          avgResponseTime: '200ms',
          lastUsed: '2023-05-24T11:20:00Z',
          tags: ['google', 'analytics', 'reporting']
        },
        {
          id: 'conn5',
          name: 'Stripe API Connector',
          description: 'Connect to Stripe for payment processing',
          category: 'finance',
          type: 'api',
          version: '1.5.0',
          status: 'active',
          created: '2023-03-01T14:00:00Z',
          updated: '2023-05-18T09:30:00Z',
          baseUrl: 'https://api.stripe.com/v1',
          usage: 4250,
          successRate: '99.95%',
          avgResponseTime: '85ms',
          lastUsed: '2023-05-26T15:10:00Z',
          tags: ['stripe', 'payments', 'finance']
        },
        {
          id: 'conn6',
          name: 'AWS S3 Connector',
          description: 'Connect to Amazon S3 for file storage',
          category: 'cloud',
          type: 'storage',
          version: '1.1.0',
          status: 'active',
          created: '2023-02-15T11:20:00Z',
          updated: '2023-04-10T13:15:00Z',
          baseUrl: 'https://s3.amazonaws.com',
          usage: 7890,
          successRate: '99.7%',
          avgResponseTime: '110ms',
          lastUsed: '2023-05-26T16:30:00Z',
          tags: ['aws', 's3', 'storage', 'cloud']
        },
        {
          id: 'conn7',
          name: 'Slack API Connector',
          description: 'Connect to Slack for messaging and notifications',
          category: 'communication',
          type: 'api',
          version: '0.8.5',
          status: 'draft',
          created: '2023-05-01T15:30:00Z',
          updated: '2023-05-15T10:45:00Z',
          baseUrl: 'https://slack.com/api',
          usage: 950,
          successRate: '97.8%',
          avgResponseTime: '130ms',
          lastUsed: '2023-05-25T14:20:00Z',
          tags: ['slack', 'messaging', 'communication']
        },
        {
          id: 'conn8',
          name: 'HubSpot API Connector',
          description: 'Connect to HubSpot for marketing and CRM',
          category: 'marketing',
          type: 'api',
          version: '1.0.0',
          status: 'deprecated',
          created: '2022-11-10T09:00:00Z',
          updated: '2023-01-20T14:00:00Z',
          baseUrl: 'https://api.hubapi.com',
          usage: 120,
          successRate: '95.5%',
          avgResponseTime: '180ms',
          lastUsed: '2023-05-20T09:45:00Z',
          tags: ['hubspot', 'marketing', 'crm']
        },
        {
          id: 'conn9',
          name: 'MongoDB Atlas Connector',
          description: 'Connect to MongoDB Atlas for database operations',
          category: 'database',
          type: 'database',
          version: '1.3.0',
          status: 'active',
          created: '2023-03-15T13:20:00Z',
          updated: '2023-05-10T11:30:00Z',
          baseUrl: 'https://cloud.mongodb.com/api/atlas/v1.0',
          usage: 3150,
          successRate: '99.6%',
          avgResponseTime: '95ms',
          lastUsed: '2023-05-26T13:15:00Z',
          tags: ['mongodb', 'database', 'atlas']
        },
        {
          id: 'conn10',
          name: 'Twilio API Connector',
          description: 'Connect to Twilio for SMS and voice communications',
          category: 'communication',
          type: 'api',
          version: '1.2.1',
          status: 'active',
          created: '2023-02-20T10:15:00Z',
          updated: '2023-04-25T14:40:00Z',
          baseUrl: 'https://api.twilio.com/2010-04-01',
          usage: 2780,
          successRate: '99.3%',
          avgResponseTime: '140ms',
          lastUsed: '2023-05-26T12:10:00Z',
          tags: ['twilio', 'sms', 'communication']
        }
      ];
      
      setConnectors(mockConnectors);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching connectors:', error);
      setLoading(false);
      setSnackbar({
        open: true,
        message: 'Error fetching connectors: ' + error.message,
        severity: 'error'
      });
    }
  };
  
  useEffect(() => {
    fetchConnectors();
  }, []);
  
  const handleCreateConnector = () => {
    router.push('/connectors/new');
  };
  
  const handleDeleteConnector = (connectorId) => {
    const connector = connectors.find(c => c.id === connectorId);
    setConnectorToDelete(connector);
    setDeleteDialogOpen(true);
  };
  
  const confirmDeleteConnector = () => {
    // In a real implementation, this would call an API to delete the connector
    setConnectors(connectors.filter(c => c.id !== connectorToDelete.id));
    setDeleteDialogOpen(false);
    
    setSnackbar({
      open: true,
      message: `Connector "${connectorToDelete.name}" deleted successfully`,
      severity: 'success'
    });
    
    setConnectorToDelete(null);
  };
  
  const handleDuplicateConnector = (connectorId) => {
    // In a real implementation, this would call an API to duplicate the connector
    const connector = connectors.find(c => c.id === connectorId);
    
    if (connector) {
      const duplicatedConnector = {
        ...connector,
        id: `${connector.id}_copy`,
        name: `${connector.name} (Copy)`,
        created: new Date().toISOString(),
        updated: new Date().toISOString(),
        status: 'draft'
      };
      
      setConnectors([...connectors, duplicatedConnector]);
      
      setSnackbar({
        open: true,
        message: `Connector "${connector.name}" duplicated successfully`,
        severity: 'success'
      });
    }
  };
  
  const handleRefresh = () => {
    fetchConnectors();
    
    setSnackbar({
      open: true,
      message: 'Connectors refreshed successfully',
      severity: 'info'
    });
  };
  
  const handleSnackbarClose = () => {
    setSnackbar({
      ...snackbar,
      open: false
    });
  };
  
  return (
    <DashboardLayout>
      <Container maxWidth="xl">
        <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box>
            <Typography variant="h4" component="h1" gutterBottom>
              Connector Management
            </Typography>
            <Typography variant="subtitle1" color="text.secondary">
              Manage your API connectors and integrations
            </Typography>
          </Box>
          
          <Box>
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={handleRefresh}
              sx={{ mr: 2 }}
            >
              Refresh
            </Button>
            
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleCreateConnector}
            >
              Create Connector
            </Button>
          </Box>
        </Box>
        
        <Fade in={true} timeout={500}>
          <Paper variant="outlined" sx={{ p: 0, overflow: 'hidden' }}>
            <EnhancedConnectorList 
              connectors={connectors} 
              onDelete={handleDeleteConnector}
              onDuplicate={handleDuplicateConnector}
              loading={loading}
              onRefresh={handleRefresh}
            />
          </Paper>
        </Fade>
        
        <Dialog
          open={deleteDialogOpen}
          onClose={() => setDeleteDialogOpen(false)}
        >
          <DialogTitle>Delete Connector</DialogTitle>
          <DialogContent>
            <DialogContentText>
              Are you sure you want to delete the connector "{connectorToDelete?.name}"? This action cannot be undone.
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={confirmDeleteConnector} color="error" autoFocus>
              Delete
            </Button>
          </DialogActions>
        </Dialog>
        
        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={handleSnackbarClose}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        >
          <Alert 
            onClose={handleSnackbarClose} 
            severity={snackbar.severity}
            variant="filled"
            sx={{ width: '100%' }}
          >
            {snackbar.message}
          </Alert>
        </Snackbar>
      </Container>
    </DashboardLayout>
  );
};

export default ManagementPage;
