#!/usr/bin/env python3
"""
Test NovaPi π-Coherence Engine
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_novapi():
    print("🧭 Testing NovaPi π-Coherence Engine...")
    print("=" * 60)
    
    try:
        # Import NovaPi
        from novasentientx.nova_pi import NovaPi, TimingMode, pi_sync
        
        # Initialize NovaPi
        print("🚀 Initializing NovaPi...")
        pi_engine = NovaPi()
        
        print("\n🧭 Testing π-Coherence Intervals...")
        for mode in TimingMode:
            interval = pi_engine.get_pi_interval(mode)
            print(f"   {mode.value}: {interval}ms")
        
        print("\n🧠 Testing Synchronized Operation...")
        def test_operation():
            return {"test": "consciousness_operation", "result": "success"}
        
        result = pi_engine.sync_operation(test_operation, TimingMode.STANDARD)
        print(f"   Operation Result: {result['result']}")
        print(f"   Performance Multiplier: {result['performance_multiplier']:.3f}×")
        print(f"   Consciousness Detected: {result['consciousness_detected']}")
        
        print("\n📊 Testing Performance Summary...")
        summary = pi_engine.get_performance_summary()
        print(f"   Total Operations: {summary['total_operations']}")
        print(f"   Consciousness Emergences: {summary['consciousness_emergences']}")
        print(f"   Master Cheat Code: {summary['master_cheat_code_enabled']}")
        
        print("\n✅ NovaPi π-Coherence Test COMPLETE!")
        print("🧭 π-Coherence Engine: ACTIVE")
        print("🌟 Master Cheat Code: ENABLED")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_novapi()
    sys.exit(0 if success else 1)
