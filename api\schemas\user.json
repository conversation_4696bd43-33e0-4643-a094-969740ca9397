{"entity": "User", "entityPlural": "Users", "apiEndpoint": "/api/v1/users", "fields": [{"name": "email", "label": "Email Address", "type": "email", "required": true, "description": "We'll never share your email.", "placeholder": "Enter your email address"}, {"name": "password", "label": "Password", "type": "password", "required": true, "placeholder": "Enter your password"}, {"name": "firstName", "label": "First Name", "type": "text", "required": true, "placeholder": "Enter your first name"}, {"name": "lastName", "label": "Last Name", "type": "text", "required": true, "placeholder": "Enter your last name"}, {"name": "role", "label": "Role", "type": "select", "options": [{"label": "Admin", "value": "admin"}, {"label": "User", "value": "user"}, {"label": "Auditor", "value": "auditor"}], "required": true}, {"name": "status", "label": "Status", "type": "radio", "options": [{"label": "Active", "value": "active"}, {"label": "Inactive", "value": "inactive"}, {"label": "Pending", "value": "pending"}], "required": true}, {"name": "agreeToTerms", "label": "I agree to the terms and conditions", "type": "checkbox", "required": true}], "submitLabel": "Create User"}