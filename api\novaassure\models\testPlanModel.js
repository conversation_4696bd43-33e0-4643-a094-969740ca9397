/**
 * Test Plan Model
 * 
 * This model defines the schema for test plans.
 */

const mongoose = require('mongoose');

const testPlanSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    required: true,
    trim: true
  },
  framework: {
    type: String,
    required: true,
    trim: true,
    index: true
  },
  controls: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Control',
    required: true
  }],
  schedule: {
    frequency: {
      type: String,
      enum: ['daily', 'weekly', 'monthly', 'quarterly', 'annually', 'custom'],
      default: 'quarterly'
    },
    startDate: {
      type: Date,
      default: Date.now
    },
    endDate: {
      type: Date
    },
    customSchedule: {
      type: String
    }
  },
  assignees: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }],
  status: {
    type: String,
    enum: ['draft', 'active', 'completed', 'archived'],
    default: 'draft'
  },
  tags: [{
    type: String,
    trim: true
  }],
  metadata: {
    type: mongoose.Schema.Types.Mixed
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Create indexes
testPlanSchema.index({ name: 'text', description: 'text' });
testPlanSchema.index({ framework: 1, status: 1 });
testPlanSchema.index({ 'schedule.startDate': 1, 'schedule.endDate': 1 });

/**
 * Get test plan by ID
 * @param {string} id - Test plan ID
 * @returns {Promise<Object>} - Test plan object
 */
testPlanSchema.statics.getById = async function(id) {
  return this.findById(id).populate('controls').populate('assignees');
};

/**
 * Get test plans by framework
 * @param {string} framework - Framework name
 * @returns {Promise<Array>} - Array of test plan objects
 */
testPlanSchema.statics.getByFramework = async function(framework) {
  return this.find({ framework }).populate('controls').populate('assignees');
};

/**
 * Get test plans by status
 * @param {string} status - Status
 * @returns {Promise<Array>} - Array of test plan objects
 */
testPlanSchema.statics.getByStatus = async function(status) {
  return this.find({ status }).populate('controls').populate('assignees');
};

/**
 * Get test plans by assignee
 * @param {string} assigneeId - Assignee ID
 * @returns {Promise<Array>} - Array of test plan objects
 */
testPlanSchema.statics.getByAssignee = async function(assigneeId) {
  return this.find({ assignees: assigneeId }).populate('controls').populate('assignees');
};

/**
 * Get test plans due for execution
 * @returns {Promise<Array>} - Array of test plan objects
 */
testPlanSchema.statics.getDueForExecution = async function() {
  const now = new Date();
  
  // Find active test plans
  const testPlans = await this.find({
    status: 'active',
    'schedule.startDate': { $lte: now },
    $or: [
      { 'schedule.endDate': { $gte: now } },
      { 'schedule.endDate': { $exists: false } }
    ]
  }).populate('controls').populate('assignees');
  
  // Filter test plans due for execution based on frequency
  const dueTestPlans = [];
  
  for (const testPlan of testPlans) {
    // Get last execution
    const lastExecution = await mongoose.model('TestExecution').findOne({
      testPlan: testPlan._id
    }).sort({ createdAt: -1 });
    
    if (!lastExecution) {
      // No previous execution, so it's due
      dueTestPlans.push(testPlan);
      continue;
    }
    
    const lastExecutionDate = lastExecution.createdAt;
    let isDue = false;
    
    switch (testPlan.schedule.frequency) {
      case 'daily':
        // Due if last execution was more than 1 day ago
        isDue = now - lastExecutionDate > 24 * 60 * 60 * 1000;
        break;
      case 'weekly':
        // Due if last execution was more than 7 days ago
        isDue = now - lastExecutionDate > 7 * 24 * 60 * 60 * 1000;
        break;
      case 'monthly':
        // Due if last execution was more than 30 days ago
        isDue = now - lastExecutionDate > 30 * 24 * 60 * 60 * 1000;
        break;
      case 'quarterly':
        // Due if last execution was more than 90 days ago
        isDue = now - lastExecutionDate > 90 * 24 * 60 * 60 * 1000;
        break;
      case 'annually':
        // Due if last execution was more than 365 days ago
        isDue = now - lastExecutionDate > 365 * 24 * 60 * 60 * 1000;
        break;
      case 'custom':
        // Custom schedule not implemented yet
        isDue = false;
        break;
    }
    
    if (isDue) {
      dueTestPlans.push(testPlan);
    }
  }
  
  return dueTestPlans;
};

/**
 * Clone test plan
 * @param {string} id - Test plan ID
 * @param {string} name - New test plan name
 * @param {string} userId - User ID
 * @returns {Promise<Object>} - Cloned test plan object
 */
testPlanSchema.statics.clone = async function(id, name, userId) {
  const testPlan = await this.findById(id);
  
  if (!testPlan) {
    throw new Error('Test plan not found');
  }
  
  const clonedTestPlan = new this({
    name,
    description: testPlan.description,
    framework: testPlan.framework,
    controls: testPlan.controls,
    schedule: testPlan.schedule,
    assignees: testPlan.assignees,
    status: 'draft',
    tags: testPlan.tags,
    metadata: testPlan.metadata,
    createdBy: userId,
    updatedBy: userId
  });
  
  return clonedTestPlan.save();
};

const TestPlan = mongoose.model('TestPlan', testPlanSchema);

module.exports = TestPlan;
