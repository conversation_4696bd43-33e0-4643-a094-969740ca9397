/**
 * Error Handler Middleware Tests
 *
 * This file contains unit tests for the error handler middleware.
 */

const errorHandler = require('../../../middleware/errorHandler');

describe('Error Handler Middleware', () => {
  let req, res, next;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Setup request, response and next function
    req = {
      path: '/test',
      method: 'GET',
      user: { username: 'testuser' }
    };

    res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    };

    next = jest.fn();

    // Mock console.error to prevent test output pollution
    console.error = jest.fn();
  });

  it('should handle ValidationError', () => {
    // Setup error
    const error = new Error('Validation failed');
    error.name = 'ValidationError';

    // Call the middleware
    errorHandler(error, req, res, next);

    // Verify console.error was called
    expect(console.error).toHaveBeenCalled();

    // Verify the response
    expect(res.status).toHaveBeenCalledWith(400);
    expect(res.json).toHaveBeenCalledWith({
      error: 'ValidationError',
      message: 'Validation failed'
    });
  });

  it('should handle UnauthorizedError', () => {
    // Setup error
    const error = new Error('Unauthorized');
    error.name = 'UnauthorizedError';

    // Call the middleware
    errorHandler(error, req, res, next);

    // Verify the response
    expect(res.status).toHaveBeenCalledWith(401);
    expect(res.json).toHaveBeenCalledWith({
      error: 'UnauthorizedError',
      message: 'Unauthorized'
    });
  });

  it('should handle ForbiddenError', () => {
    // Setup error
    const error = new Error('Forbidden');
    error.name = 'ForbiddenError';

    // Call the middleware
    errorHandler(error, req, res, next);

    // Verify the response
    expect(res.status).toHaveBeenCalledWith(403);
    expect(res.json).toHaveBeenCalledWith({
      error: 'ForbiddenError',
      message: 'Forbidden'
    });
  });

  it('should handle NotFoundError', () => {
    // Setup error
    const error = new Error('Resource not found');
    error.name = 'NotFoundError';

    // Call the middleware
    errorHandler(error, req, res, next);

    // Verify the response
    expect(res.status).toHaveBeenCalledWith(404);
    expect(res.json).toHaveBeenCalledWith({
      error: 'NotFoundError',
      message: 'Resource not found'
    });
  });

  it('should handle ConflictError', () => {
    // Setup error
    const error = new Error('Conflict');
    error.name = 'ConflictError';

    // Call the middleware
    errorHandler(error, req, res, next);

    // Verify the response
    expect(res.status).toHaveBeenCalledWith(409);
    expect(res.json).toHaveBeenCalledWith({
      error: 'ConflictError',
      message: 'Conflict'
    });
  });

  it('should handle RateLimitError', () => {
    // Setup error
    const error = new Error('Too many requests');
    error.name = 'RateLimitError';

    // Call the middleware
    errorHandler(error, req, res, next);

    // Verify the response
    expect(res.status).toHaveBeenCalledWith(429);
    expect(res.json).toHaveBeenCalledWith({
      error: 'RateLimitError',
      message: 'Too many requests'
    });
  });

  it('should handle unknown errors as 500 Internal Server Error', () => {
    // Setup error
    const error = new Error('Unknown error');
    error.name = 'Error';

    // Call the middleware
    errorHandler(error, req, res, next);

    // Verify the response
    expect(res.status).toHaveBeenCalledWith(500);
    expect(res.json).toHaveBeenCalledWith({
      error: 'ServerError',
      message: 'Internal Server Error'
    });
  });

  it('should use default messages when error message is empty', () => {
    // Test cases for each error type with empty messages
    const errorTypes = [
      { name: 'ValidationError', status: 400, defaultMessage: 'Validation failed' },
      { name: 'UnauthorizedError', status: 401, defaultMessage: 'Unauthorized' },
      { name: 'ForbiddenError', status: 403, defaultMessage: 'Forbidden' },
      { name: 'NotFoundError', status: 404, defaultMessage: 'Resource not found' },
      { name: 'ConflictError', status: 409, defaultMessage: 'Conflict' },
      { name: 'RateLimitError', status: 429, defaultMessage: 'Too many requests' }
    ];

    errorTypes.forEach(({ name, status, defaultMessage }) => {
      // Reset mocks
      jest.clearAllMocks();

      // Setup error with empty message
      const error = new Error();
      error.name = name;
      error.message = '';

      // Call the middleware
      errorHandler(error, req, res, next);

      // Verify the response uses default message
      expect(res.status).toHaveBeenCalledWith(status);
      expect(res.json).toHaveBeenCalledWith({
        error: name,
        message: defaultMessage
      });
    });
  });
});
