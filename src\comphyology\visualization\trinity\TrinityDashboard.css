.trinity-dashboard {
  font-family: 'Roboto', 'Segoe UI', Arial, sans-serif;
  background-color: #1a1a2e;
  color: #ffffff;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  max-width: 1200px;
  margin: 0 auto;
}

.trinity-dashboard-header {
  text-align: center;
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.trinity-dashboard-header h1 {
  margin: 0;
  font-size: 28px;
  font-weight: 500;
  background: linear-gradient(45deg, #4CAF50, #2196F3, #9C27B0);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  text-fill-color: transparent;
}

.trinity-dashboard-header p {
  margin: 10px 0 0;
  font-size: 16px;
  opacity: 0.8;
}

.trinity-dashboard-content {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.trinity-visualization-wrapper {
  flex: 1;
  min-width: 500px;
  height: 500px;
  background-color: #111133;
  border-radius: 8px;
  overflow: hidden;
}

.trinity-dashboard-controls {
  flex: 1;
  min-width: 300px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.control-section {
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 15px;
}

.control-section h3 {
  margin: 0 0 15px 0;
  font-size: 18px;
  font-weight: 500;
  color: #2196F3;
}

.wheel-selector {
  display: flex;
  gap: 10px;
}

.wheel-selector button {
  flex: 1;
  background-color: rgba(255, 255, 255, 0.1);
  border: none;
  color: white;
  padding: 10px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.wheel-selector button:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.wheel-selector button.active {
  background-color: #2196F3;
}

.control-options {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.control-toggle {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.control-toggle input {
  margin-right: 10px;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
}

.metric-item {
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  padding: 10px;
}

.metric-label {
  font-size: 14px;
  margin-bottom: 5px;
  opacity: 0.8;
}

.metric-value {
  display: flex;
  align-items: center;
  gap: 10px;
}

.progress-bar {
  flex: 1;
  height: 8px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: #4CAF50;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.metric-number {
  font-size: 16px;
  font-weight: 500;
  min-width: 50px;
  text-align: right;
}

.trinity-dashboard-details {
  margin-top: 20px;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.details-section {
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 15px;
}

.details-section h3 {
  margin: 0 0 15px 0;
  font-size: 18px;
  font-weight: 500;
}

.details-section:nth-child(1) h3 {
  color: #4CAF50;
}

.details-section:nth-child(2) h3 {
  color: #2196F3;
}

.details-section:nth-child(3) h3 {
  color: #9C27B0;
}

.details-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 10px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}

.detail-label {
  font-size: 14px;
}

.detail-value {
  font-size: 14px;
  font-weight: 500;
}

.trinity-dashboard-footer {
  margin-top: 20px;
  text-align: center;
  font-size: 14px;
  opacity: 0.6;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Animation for the header gradient */
@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.trinity-dashboard-header h1 {
  background-size: 200% 200%;
  animation: gradientShift 5s ease infinite;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .trinity-dashboard-content {
    flex-direction: column;
  }
  
  .trinity-visualization-wrapper {
    min-width: 100%;
    height: 400px;
  }
  
  .metrics-grid {
    grid-template-columns: 1fr 1fr;
  }
  
  .trinity-dashboard-details {
    grid-template-columns: 1fr;
  }
}
