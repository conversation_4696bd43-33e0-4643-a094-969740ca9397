/**
 * NovaProofConnector.js
 * 
 * This module provides integration with NovaProof's blockchain infrastructure
 * for NovaDNA. It leverages NovaProof's verification capabilities to ensure
 * the integrity and authenticity of emergency medical data.
 */

const axios = require('axios');
const { v4: uuidv4 } = require('uuid');
const crypto = require('crypto');

/**
 * NovaProofConnector class for integrating with NovaProof
 */
class NovaProofConnector {
  constructor(options = {}) {
    this.apiUrl = options.apiUrl || 'http://localhost:3000/api/novaproof';
    this.apiKey = options.apiKey;
    this.apiSecret = options.apiSecret;
    this.timeout = options.timeout || 10000; // 10 seconds
    this.verificationCache = new Map();
    
    // Create axios instance
    this.client = axios.create({
      baseURL: this.apiUrl,
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': this.apiKey
      }
    });
    
    // Add request interceptor for authentication
    this.client.interceptors.request.use(config => {
      if (this.apiSecret) {
        const timestamp = Date.now().toString();
        const signature = this._generateSignature(timestamp);
        
        config.headers['X-Timestamp'] = timestamp;
        config.headers['X-Signature'] = signature;
      }
      
      return config;
    });
  }

  /**
   * Verify data using NovaProof
   * @param {Object} data - The data to verify
   * @param {Object} options - Verification options
   * @returns {Promise<Object>} - The verification result
   */
  async verifyData(data, options = {}) {
    try {
      const contentHash = this._generateHash(data);
      const verificationId = options.verificationId || uuidv4();
      
      // Check cache first
      if (this.verificationCache.has(contentHash) && !options.forceVerify) {
        return this.verificationCache.get(contentHash);
      }
      
      const response = await this.client.post('/verify', {
        verificationId,
        contentHash,
        verificationMethod: options.verificationMethod || 'merkle',
        metadata: {
          dataType: 'MEDICAL_EMERGENCY',
          source: 'NovaDNA',
          ...options.metadata
        }
      });
      
      const result = response.data;
      
      // Cache the result
      this.verificationCache.set(contentHash, result);
      
      return result;
    } catch (error) {
      // Handle API errors
      if (error.response) {
        throw new Error(`NovaProof API error: ${error.response.status} - ${error.response.data.message || error.response.statusText}`);
      } else if (error.request) {
        throw new Error(`NovaProof API request failed: ${error.message}`);
      } else {
        throw new Error(`NovaProof error: ${error.message}`);
      }
    }
  }

  /**
   * Get verification status from NovaProof
   * @param {String} verificationId - The verification ID
   * @returns {Promise<Object>} - The verification status
   */
  async getVerificationStatus(verificationId) {
    try {
      const response = await this.client.get(`/verification/${verificationId}`);
      return response.data;
    } catch (error) {
      // Handle API errors
      if (error.response) {
        throw new Error(`NovaProof API error: ${error.response.status} - ${error.response.data.message || error.response.statusText}`);
      } else if (error.request) {
        throw new Error(`NovaProof API request failed: ${error.message}`);
      } else {
        throw new Error(`NovaProof error: ${error.message}`);
      }
    }
  }

  /**
   * Create an audit trail entry in NovaProof
   * @param {String} profileId - The profile ID
   * @param {String} action - The action performed
   * @param {Object} details - The audit details
   * @returns {Promise<Object>} - The audit trail entry
   */
  async createAuditTrail(profileId, action, details = {}) {
    try {
      const auditId = uuidv4();
      const timestamp = new Date().toISOString();
      
      const response = await this.client.post('/audit', {
        auditId,
        profileId,
        action,
        timestamp,
        details: {
          source: 'NovaDNA',
          ...details
        }
      });
      
      return response.data;
    } catch (error) {
      // Handle API errors
      if (error.response) {
        throw new Error(`NovaProof API error: ${error.response.status} - ${error.response.data.message || error.response.statusText}`);
      } else if (error.request) {
        throw new Error(`NovaProof API request failed: ${error.message}`);
      } else {
        throw new Error(`NovaProof error: ${error.message}`);
      }
    }
  }

  /**
   * Get audit trail for a profile
   * @param {String} profileId - The profile ID
   * @param {Object} options - Options for filtering the audit trail
   * @returns {Promise<Array>} - The audit trail entries
   */
  async getProfileAuditTrail(profileId, options = {}) {
    try {
      const queryParams = new URLSearchParams();
      
      if (options.startDate) {
        queryParams.append('startDate', options.startDate);
      }
      
      if (options.endDate) {
        queryParams.append('endDate', options.endDate);
      }
      
      if (options.action) {
        queryParams.append('action', options.action);
      }
      
      if (options.limit) {
        queryParams.append('limit', options.limit.toString());
      }
      
      const response = await this.client.get(`/audit/${profileId}?${queryParams.toString()}`);
      return response.data;
    } catch (error) {
      // Handle API errors
      if (error.response) {
        throw new Error(`NovaProof API error: ${error.response.status} - ${error.response.data.message || error.response.statusText}`);
      } else if (error.request) {
        throw new Error(`NovaProof API request failed: ${error.message}`);
      } else {
        throw new Error(`NovaProof error: ${error.message}`);
      }
    }
  }

  /**
   * Verify a blockchain transaction
   * @param {String} transactionId - The transaction ID
   * @param {String} network - The blockchain network
   * @returns {Promise<Object>} - The transaction verification result
   */
  async verifyTransaction(transactionId, network = 'ethereum') {
    try {
      const response = await this.client.get(`/transaction/${network}/${transactionId}`);
      return response.data;
    } catch (error) {
      // Handle API errors
      if (error.response) {
        throw new Error(`NovaProof API error: ${error.response.status} - ${error.response.data.message || error.response.statusText}`);
      } else if (error.request) {
        throw new Error(`NovaProof API request failed: ${error.message}`);
      } else {
        throw new Error(`NovaProof error: ${error.message}`);
      }
    }
  }

  /**
   * Generate a hash of the provided data
   * @param {Object} data - The data to hash
   * @returns {String} - The generated hash
   * @private
   */
  _generateHash(data) {
    const stringData = typeof data === 'string' ? data : JSON.stringify(data);
    return crypto
      .createHash('sha256')
      .update(stringData)
      .digest('hex');
  }

  /**
   * Generate a signature for API authentication
   * @param {String} timestamp - The timestamp
   * @returns {String} - The generated signature
   * @private
   */
  _generateSignature(timestamp) {
    return crypto
      .createHmac('sha256', this.apiSecret)
      .update(`${this.apiKey}:${timestamp}`)
      .digest('hex');
  }

  /**
   * Clear the verification cache
   */
  clearCache() {
    this.verificationCache.clear();
  }
}

module.exports = NovaProofConnector;
