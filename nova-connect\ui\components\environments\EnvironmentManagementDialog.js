/**
 * Environment Management Dialog Component
 * 
 * This component allows users to manage environments.
 */

import React, { useState } from 'react';
import { 
  Box, 
  Button, 
  Chip, 
  CircularProgress, 
  Dialog, 
  DialogActions, 
  DialogContent, 
  DialogTitle, 
  Divider, 
  Grid, 
  IconButton, 
  List, 
  ListItem, 
  ListItemIcon, 
  ListItemSecondaryAction, 
  ListItemText, 
  Menu, 
  MenuItem, 
  Tab, 
  Tabs, 
  Tooltip, 
  Typography 
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import StarIcon from '@mui/icons-material/Star';
import StarOutlineIcon from '@mui/icons-material/StarOutline';
import ArrowUpwardIcon from '@mui/icons-material/ArrowUpward';
import CompareArrowsIcon from '@mui/icons-material/CompareArrows';
import { useEnvironment } from '../../contexts/EnvironmentContext';
import EnvironmentFormDialog from './EnvironmentFormDialog';
import EnvironmentPromotionDialog from './EnvironmentPromotionDialog';
import EnvironmentComparisonDialog from './EnvironmentComparisonDialog';

const EnvironmentManagementDialog = ({ open, onClose }) => {
  const { 
    environments, 
    currentEnvironment, 
    loading, 
    deleteEnvironment, 
    setDefaultEnvironment 
  } = useEnvironment();
  
  const [activeTab, setActiveTab] = useState('environments');
  const [formDialogOpen, setFormDialogOpen] = useState(false);
  const [promotionDialogOpen, setPromotionDialogOpen] = useState(false);
  const [comparisonDialogOpen, setComparisonDialogOpen] = useState(false);
  const [selectedEnvironment, setSelectedEnvironment] = useState(null);
  const [actionMenuAnchor, setActionMenuAnchor] = useState(null);
  const [actionEnvironment, setActionEnvironment] = useState(null);
  
  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };
  
  // Open action menu
  const handleOpenActionMenu = (event, environment) => {
    setActionMenuAnchor(event.currentTarget);
    setActionEnvironment(environment);
  };
  
  // Close action menu
  const handleCloseActionMenu = () => {
    setActionMenuAnchor(null);
    setActionEnvironment(null);
  };
  
  // Open form dialog for creating a new environment
  const handleCreateEnvironment = () => {
    setSelectedEnvironment(null);
    setFormDialogOpen(true);
  };
  
  // Open form dialog for editing an environment
  const handleEditEnvironment = (environment) => {
    setSelectedEnvironment(environment);
    setFormDialogOpen(true);
    handleCloseActionMenu();
  };
  
  // Handle deleting an environment
  const handleDeleteEnvironment = async (environment) => {
    if (window.confirm(`Are you sure you want to delete the environment "${environment.name}"?`)) {
      try {
        await deleteEnvironment(environment.id);
      } catch (error) {
        console.error('Error deleting environment:', error);
        alert(`Failed to delete environment: ${error.message}`);
      }
    }
    
    handleCloseActionMenu();
  };
  
  // Handle setting an environment as default
  const handleSetDefaultEnvironment = async (environment) => {
    try {
      await setDefaultEnvironment(environment.id);
    } catch (error) {
      console.error('Error setting default environment:', error);
      alert(`Failed to set default environment: ${error.message}`);
    }
    
    handleCloseActionMenu();
  };
  
  // Open promotion dialog
  const handleOpenPromotionDialog = (environment) => {
    setSelectedEnvironment(environment);
    setPromotionDialogOpen(true);
    handleCloseActionMenu();
  };
  
  // Open comparison dialog
  const handleOpenComparisonDialog = (environment) => {
    setSelectedEnvironment(environment);
    setComparisonDialogOpen(true);
    handleCloseActionMenu();
  };
  
  // Get environment color
  const getEnvironmentColor = (environment) => {
    return environment.color || '#2196f3';
  };
  
  return (
    <>
      <Dialog 
        open={open} 
        onClose={onClose} 
        maxWidth="md" 
        fullWidth
      >
        <DialogTitle>
          Environment Management
        </DialogTitle>
        
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          sx={{ px: 3 }}
        >
          <Tab label="Environments" value="environments" />
          <Tab label="Promotion" value="promotion" />
        </Tabs>
        
        <DialogContent>
          {activeTab === 'environments' && (
            <Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="subtitle1">
                  Manage your environments
                </Typography>
                
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={handleCreateEnvironment}
                >
                  New Environment
                </Button>
              </Box>
              
              {loading ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                  <CircularProgress />
                </Box>
              ) : environments.length === 0 ? (
                <Typography variant="body2" color="textSecondary" align="center" sx={{ p: 3 }}>
                  No environments found. Create your first environment to get started.
                </Typography>
              ) : (
                <List>
                  {environments.map((environment) => (
                    <ListItem
                      key={environment.id}
                      sx={{
                        borderLeft: `4px solid ${getEnvironmentColor(environment)}`,
                        mb: 1,
                        bgcolor: environment.id === currentEnvironment?.id ? 'action.selected' : 'background.paper'
                      }}
                    >
                      <ListItemIcon>
                        {environment.isDefault ? (
                          <Tooltip title="Default Environment">
                            <StarIcon color="warning" />
                          </Tooltip>
                        ) : (
                          <StarOutlineIcon color="disabled" />
                        )}
                      </ListItemIcon>
                      
                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            {environment.name}
                            {environment.id === currentEnvironment?.id && (
                              <Chip
                                size="small"
                                label="Current"
                                color="primary"
                                sx={{ ml: 1 }}
                              />
                            )}
                          </Box>
                        }
                        secondary={environment.description || 'No description'}
                      />
                      
                      <ListItemSecondaryAction>
                        <IconButton
                          edge="end"
                          onClick={(event) => handleOpenActionMenu(event, environment)}
                        >
                          <MoreVertIcon />
                        </IconButton>
                      </ListItemSecondaryAction>
                    </ListItem>
                  ))}
                </List>
              )}
            </Box>
          )}
          
          {activeTab === 'promotion' && (
            <Box>
              <Typography variant="subtitle1" gutterBottom>
                Promote Configuration Between Environments
              </Typography>
              
              <Typography variant="body2" color="textSecondary" paragraph>
                Promotion allows you to copy configuration and connectors from one environment to another.
                This is useful for moving changes from development to testing, or from testing to production.
              </Typography>
              
              <Grid container spacing={2}>
                {environments.map((environment) => (
                  <Grid item xs={12} sm={6} md={4} key={environment.id}>
                    <Box
                      sx={{
                        p: 2,
                        border: 1,
                        borderColor: 'divider',
                        borderRadius: 1,
                        height: '100%',
                        display: 'flex',
                        flexDirection: 'column'
                      }}
                    >
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <Chip
                          size="small"
                          label=""
                          sx={{
                            bgcolor: getEnvironmentColor(environment),
                            width: 16,
                            height: 16,
                            mr: 1
                          }}
                        />
                        <Typography variant="subtitle2">
                          {environment.name}
                        </Typography>
                        {environment.isDefault && (
                          <Tooltip title="Default Environment">
                            <StarIcon color="warning" fontSize="small" sx={{ ml: 1 }} />
                          </Tooltip>
                        )}
                      </Box>
                      
                      <Typography variant="body2" color="textSecondary" sx={{ mb: 2, flexGrow: 1 }}>
                        {environment.description || 'No description'}
                      </Typography>
                      
                      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                        <Button
                          variant="outlined"
                          size="small"
                          startIcon={<ArrowUpwardIcon />}
                          onClick={() => handleOpenPromotionDialog(environment)}
                        >
                          Promote From
                        </Button>
                        
                        <Button
                          variant="outlined"
                          size="small"
                          startIcon={<CompareArrowsIcon />}
                          onClick={() => handleOpenComparisonDialog(environment)}
                        >
                          Compare
                        </Button>
                      </Box>
                    </Box>
                  </Grid>
                ))}
              </Grid>
            </Box>
          )}
        </DialogContent>
        
        <DialogActions>
          <Button onClick={onClose}>
            Close
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Environment action menu */}
      <Menu
        anchorEl={actionMenuAnchor}
        open={Boolean(actionMenuAnchor)}
        onClose={handleCloseActionMenu}
      >
        <MenuItem onClick={() => handleEditEnvironment(actionEnvironment)}>
          <ListItemIcon>
            <EditIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Edit</ListItemText>
        </MenuItem>
        
        {!actionEnvironment?.isDefault && (
          <MenuItem onClick={() => handleSetDefaultEnvironment(actionEnvironment)}>
            <ListItemIcon>
              <StarIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Set as Default</ListItemText>
          </MenuItem>
        )}
        
        <MenuItem onClick={() => handleOpenPromotionDialog(actionEnvironment)}>
          <ListItemIcon>
            <ArrowUpwardIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Promote</ListItemText>
        </MenuItem>
        
        <MenuItem onClick={() => handleOpenComparisonDialog(actionEnvironment)}>
          <ListItemIcon>
            <CompareArrowsIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Compare</ListItemText>
        </MenuItem>
        
        <Divider />
        
        <MenuItem 
          onClick={() => handleDeleteEnvironment(actionEnvironment)}
          disabled={environments.length <= 1 || actionEnvironment?.id === currentEnvironment?.id}
        >
          <ListItemIcon>
            <DeleteIcon fontSize="small" color="error" />
          </ListItemIcon>
          <ListItemText sx={{ color: 'error.main' }}>Delete</ListItemText>
        </MenuItem>
      </Menu>
      
      {/* Environment form dialog */}
      <EnvironmentFormDialog
        open={formDialogOpen}
        onClose={() => setFormDialogOpen(false)}
        environment={selectedEnvironment}
      />
      
      {/* Environment promotion dialog */}
      <EnvironmentPromotionDialog
        open={promotionDialogOpen}
        onClose={() => setPromotionDialogOpen(false)}
        sourceEnvironment={selectedEnvironment}
      />
      
      {/* Environment comparison dialog */}
      <EnvironmentComparisonDialog
        open={comparisonDialogOpen}
        onClose={() => setComparisonDialogOpen(false)}
        sourceEnvironment={selectedEnvironment}
      />
    </>
  );
};

export default EnvironmentManagementDialog;
