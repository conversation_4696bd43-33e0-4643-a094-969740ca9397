mkdocs>=1.4.0
mkdocs-material>=9.0.0
mkdocstrings[python]>=0.20.0
mkdocs-minify-plugin>=0.6.0
mkdocs-redirects>=1.2.0
mkdocs-git-revision-date-localized-plugin>=1.1.0
mkdocs-git-committers-plugin-2>=1.1.2
mkdocs-git-authors-plugin>=0.6.0
mkdocs-gitbook>=0.1.13
mkdocs-rtd-dropdown>=1.0.2
mkdocs-section-index>=0.3.0
mkdocs-awesome-pages-plugin>=2.8.0
mkdocs-exclude>=1.0.2
mkdocs-macros-plugin>=0.7.0
mkdocs-markdownextradata-plugin>=0.2.9
mkdocs-mermaid2-plugin>=1.0.6
mkdocs-pdf-export-plugin>=0.5.13
mkdocs-simple-hooks>=0.2.0
mkdocs-simple-plugin>=0.3.0
mkdocs-static-i18n>=0.53
mkdocs-video>=1.4.2
mkdocs-with-pdf>=0.9.1
mkdocs-build-plantuml-plugin>=1.7.4
mkdocs-autolinks-plugin>=0.7.0
mkdocs-bibtex>=2.10.0
mkdocs-gallery>=0.8.2
mkdocs-git-committers-plugin-2>=1.1.2
mkdocs-git-revision-date-plugin>=0.3.2
mkdocs-gitbook>=0.1.13
mkdocs-glightbox>=0.3.2
mkdocs-macros-plugin>=0.7.0
mkdocs-markdownextradata-plugin>=0.2.9
mkdocs-mermaid2-plugin>=1.0.6
mkdocs-pandoc>=0.3.0
mkdocs-pdf-export-plugin>=0.5.13
mkdocs-rtd-dropdown>=1.0.2
mkdocs-section-index>=0.3.0
mkdocs-simple-hooks>=0.2.0
mkdocs-simple-plugin>=0.3.0
mkdocs-static-i18n>=0.53
mkdocs-video>=1.4.2
mkdocs-with-pdf>=0.9.1
mkdocs>=1.4.0
mkdocs-material>=9.0.0
mkdocstrings[python]>=0.20.0
pymdown-extensions>=9.0
pygments>=2.13.0
plantuml-markdown>=3.5.0
