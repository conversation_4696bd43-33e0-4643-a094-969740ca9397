/**
 * Compliance Engine
 * 
 * This module handles compliance rules, data validation, and regulatory requirements.
 * It ensures that all API calls and data transformations comply with relevant regulations.
 */

const { getLogger } = require('./logger');

const logger = getLogger('compliance');

// Supported compliance frameworks
const COMPLIANCE_FRAMEWORKS = {
  HIPAA: 'hipaa',
  GDPR: 'gdpr',
  PCI_DSS: 'pci_dss',
  SOC2: 'soc2',
  ISO27001: 'iso27001',
  CCPA: 'ccpa',
  NIST: 'nist'
};

// Data classification levels
const DATA_CLASSIFICATION = {
  PUBLIC: 'public',
  INTERNAL: 'internal',
  CONFIDENTIAL: 'confidential',
  RESTRICTED: 'restricted'
};

/**
 * Setup the Compliance Engine
 * @returns {Object} The compliance engine instance
 */
function setupComplianceEngine() {
  // In-memory store for compliance rules
  const complianceRules = {};
  
  // In-memory store for data classification rules
  const dataClassificationRules = {};
  
  // In-memory store for compliance audit logs
  const complianceAuditLogs = [];

  /**
   * Register a compliance rule
   * @param {String} ruleId Unique identifier for the rule
   * @param {Object} rule Rule configuration
   * @returns {Boolean} Success status
   */
  function registerComplianceRule(ruleId, rule) {
    logger.info(`Registering compliance rule: ${ruleId}`);
    
    // Validate rule
    if (!rule.framework || !rule.check) {
      logger.error(`Invalid rule configuration for ${ruleId}`);
      return false;
    }
    
    // Store rule
    complianceRules[ruleId] = {
      ...rule,
      createdAt: new Date()
    };
    
    logger.info(`Compliance rule ${ruleId} registered successfully`);
    return true;
  }

  /**
   * Get a compliance rule
   * @param {String} ruleId Rule identifier
   * @returns {Object|null} Rule configuration or null if not found
   */
  function getComplianceRule(ruleId) {
    return complianceRules[ruleId] || null;
  }

  /**
   * List all compliance rules
   * @param {String} framework Optional filter by framework
   * @returns {Array} Array of compliance rules
   */
  function listComplianceRules(framework) {
    const rules = Object.keys(complianceRules).map(ruleId => ({
      id: ruleId,
      ...complianceRules[ruleId]
    }));
    
    if (framework) {
      return rules.filter(rule => rule.framework === framework);
    }
    
    return rules;
  }

  /**
   * Check if data complies with a specific rule
   * @param {String} ruleId Rule identifier
   * @param {Object} data Data to check
   * @returns {Object} Compliance check result
   */
  function checkCompliance(ruleId, data) {
    const rule = getComplianceRule(ruleId);
    
    if (!rule) {
      logger.error(`Compliance rule ${ruleId} not found`);
      throw new Error(`Compliance rule ${ruleId} not found`);
    }
    
    logger.info(`Checking compliance for rule: ${ruleId}`);
    
    try {
      // Execute the rule's check function
      const result = rule.check(data);
      
      // Log the compliance check
      complianceAuditLogs.push({
        ruleId,
        timestamp: new Date(),
        result,
        data: rule.logData ? data : null
      });
      
      return {
        compliant: result.compliant,
        details: result.details || {},
        framework: rule.framework
      };
    } catch (error) {
      logger.error(`Compliance check failed for rule ${ruleId}`, error);
      throw error;
    }
  }

  /**
   * Check if data complies with all rules for a framework
   * @param {String} framework Compliance framework
   * @param {Object} data Data to check
   * @returns {Object} Compliance check results
   */
  function checkFrameworkCompliance(framework, data) {
    const rules = listComplianceRules(framework);
    
    if (rules.length === 0) {
      logger.warn(`No compliance rules found for framework: ${framework}`);
      return {
        compliant: false,
        details: {
          message: `No compliance rules found for framework: ${framework}`
        }
      };
    }
    
    logger.info(`Checking compliance for framework: ${framework}`);
    
    const results = rules.map(rule => ({
      ruleId: rule.id,
      result: checkCompliance(rule.id, data)
    }));
    
    const allCompliant = results.every(item => item.result.compliant);
    
    return {
      compliant: allCompliant,
      results,
      framework
    };
  }

  /**
   * Classify data based on content
   * @param {Object} data Data to classify
   * @returns {String} Classification level
   */
  function classifyData(data) {
    // Default to the highest classification if no rules match
    let classification = DATA_CLASSIFICATION.RESTRICTED;
    
    // Apply classification rules
    Object.keys(dataClassificationRules).forEach(ruleId => {
      const rule = dataClassificationRules[ruleId];
      
      if (rule.check(data)) {
        classification = rule.classification;
      }
    });
    
    return classification;
  }

  /**
   * Register a data classification rule
   * @param {String} ruleId Unique identifier for the rule
   * @param {Object} rule Rule configuration
   * @returns {Boolean} Success status
   */
  function registerClassificationRule(ruleId, rule) {
    logger.info(`Registering classification rule: ${ruleId}`);
    
    // Validate rule
    if (!rule.classification || !rule.check) {
      logger.error(`Invalid classification rule configuration for ${ruleId}`);
      return false;
    }
    
    // Store rule
    dataClassificationRules[ruleId] = {
      ...rule,
      createdAt: new Date()
    };
    
    logger.info(`Classification rule ${ruleId} registered successfully`);
    return true;
  }

  /**
   * Get compliance audit logs
   * @param {Object} filters Optional filters
   * @returns {Array} Filtered audit logs
   */
  function getComplianceAuditLogs(filters = {}) {
    let logs = [...complianceAuditLogs];
    
    // Apply filters
    if (filters.ruleId) {
      logs = logs.filter(log => log.ruleId === filters.ruleId);
    }
    
    if (filters.startDate) {
      logs = logs.filter(log => log.timestamp >= new Date(filters.startDate));
    }
    
    if (filters.endDate) {
      logs = logs.filter(log => log.timestamp <= new Date(filters.endDate));
    }
    
    return logs;
  }

  // Return the compliance engine interface
  return {
    registerComplianceRule,
    getComplianceRule,
    listComplianceRules,
    checkCompliance,
    checkFrameworkCompliance,
    classifyData,
    registerClassificationRule,
    getComplianceAuditLogs,
    COMPLIANCE_FRAMEWORKS,
    DATA_CLASSIFICATION
  };
}

module.exports = {
  setupComplianceEngine
};
