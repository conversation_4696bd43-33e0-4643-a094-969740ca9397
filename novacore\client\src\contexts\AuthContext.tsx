/**
 * Authentication Context
 * 
 * This context provides authentication state and functions to the entire application.
 * It handles user authentication, session management, and permission checking.
 */

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import authService, { User, LoginCredentials, AuthState } from '../api/authService';

// Create context with default values
interface AuthContextType extends AuthState {
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => void;
  hasPermission: (permission: string) => boolean;
  hasPermissions: (permissions: string[]) => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Provider component
interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [authState, setAuthState] = useState<AuthState>({
    isAuthenticated: false,
    user: null,
    token: null,
    loading: true,
    error: null
  });

  // Initialize auth state from local storage
  useEffect(() => {
    const initializeAuth = () => {
      const isAuthenticated = authService.isAuthenticated();
      const user = authService.getCurrentUser();
      const token = authService.getToken();

      setAuthState({
        isAuthenticated,
        user,
        token,
        loading: false,
        error: null
      });
    };

    initializeAuth();
  }, []);

  // Login function
  const login = async (credentials: LoginCredentials) => {
    setAuthState(prev => ({ ...prev, loading: true, error: null }));
    
    try {
      const user = await authService.login(credentials);
      const token = authService.getToken();
      
      setAuthState({
        isAuthenticated: true,
        user,
        token,
        loading: false,
        error: null
      });
    } catch (error) {
      setAuthState(prev => ({
        ...prev,
        isAuthenticated: false,
        user: null,
        token: null,
        loading: false,
        error: error instanceof Error ? error.message : 'Login failed'
      }));
      
      throw error;
    }
  };

  // Logout function
  const logout = () => {
    authService.logout();
    
    setAuthState({
      isAuthenticated: false,
      user: null,
      token: null,
      loading: false,
      error: null
    });
  };

  // Check if user has a specific permission
  const hasPermission = (permission: string) => {
    if (!authState.user) return false;
    return authState.user.permissions.includes(permission);
  };

  // Check if user has all required permissions
  const hasPermissions = (permissions: string[]) => {
    return authService.hasPermissions(permissions);
  };

  // Context value
  const value = {
    ...authState,
    login,
    logout,
    hasPermission,
    hasPermissions
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use the auth context
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  
  return context;
};

// Protected route component
interface ProtectedRouteProps {
  children: ReactNode;
  requiredPermissions?: string[];
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ 
  children, 
  requiredPermissions = [] 
}) => {
  const { isAuthenticated, loading, hasPermissions } = useAuth();
  
  // Show loading state
  if (loading) {
    return <div className="flex items-center justify-center h-screen">Loading...</div>;
  }
  
  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    // In a real app, use React Router's Navigate or Redirect
    window.location.href = '/login';
    return null;
  }
  
  // Check permissions if required
  if (requiredPermissions.length > 0 && !hasPermissions(requiredPermissions)) {
    return (
      <div className="flex flex-col items-center justify-center h-screen">
        <h1 className="text-2xl font-bold mb-4">Access Denied</h1>
        <p className="text-gray-600">
          You don't have the required permissions to access this page.
        </p>
      </div>
    );
  }
  
  // Render children if authenticated and has permissions
  return <>{children}</>;
};

export default AuthContext;
