import { motion } from 'framer-motion'
import { TriangleIcon } from '@radix-ui/react-icons'

const triadicMetrics = {
  psi: 75,
  phi: 85,
  kappa: 90
}

export function TriadicMetrics() {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {Object.entries(triadicMetrics).map(([metric, value]) => (
          <motion.div
            key={metric}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="p-4 bg-white/5 backdrop-blur-lg rounded-lg border border-white/10"
          >
            <div className="flex items-center">
              <TriangleIcon className="w-6 h-6 mr-2 text-purple-500" />
              <div>
                <p className="text-sm text-gray-400">{metric.toUpperCase()}</p>
                <p className="text-xl font-bold">{value}%</p>
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      <div className="bg-white/5 backdrop-blur-lg rounded-lg border border-white/10 p-4">
        <h3 className="text-lg font-semibold mb-4">Triadic Impact Analysis</h3>
        <div className="space-y-2">
          <p className="text-sm text-gray-400">
            The triadic metrics show a balanced ecosystem with strong 
            <span className="text-purple-500">PSI</span>, 
            <span className="text-purple-500">PHI</span>, and 
            <span className="text-purple-500">KAPPA</span> values.
          </p>
          <p className="text-sm text-gray-400">
            Current health score: {Object.values(triadicMetrics).reduce((a, b) => a + b, 0) / 3} %
          </p>
        </div>
      </div>
    </div>
  )
}
