/**
 * Blockchain Evidence Service
 * 
 * This service provides functionality for blockchain evidence anchoring and verification.
 */

const crypto = require('crypto');
const logger = require('../utils/logger');

/**
 * Create a Merkle tree from evidence hashes
 * @param {string[]} hashes - Array of evidence hashes
 * @returns {Object} - Merkle tree
 */
function createMerkleTree(hashes) {
  if (hashes.length === 0) {
    throw new Error('No hashes provided');
  }
  
  // If only one hash, it's the root
  if (hashes.length === 1) {
    return {
      root: hashes[0],
      leaves: hashes,
      proof: []
    };
  }
  
  // Create leaf nodes
  let nodes = hashes.map(hash => ({
    hash,
    parent: null,
    left: null,
    right: null
  }));
  
  // Build tree
  while (nodes.length > 1) {
    const newLevel = [];
    
    for (let i = 0; i < nodes.length; i += 2) {
      const left = nodes[i];
      const right = i + 1 < nodes.length ? nodes[i + 1] : left;
      
      // Create parent node
      const parentHash = crypto.createHash('sha256')
        .update(left.hash + right.hash)
        .digest('hex');
      
      const parent = {
        hash: parentHash,
        parent: null,
        left,
        right
      };
      
      // Update children
      left.parent = parent;
      right.parent = parent;
      
      // Add parent to new level
      newLevel.push(parent);
    }
    
    // Move up to next level
    nodes = newLevel;
  }
  
  // Root is the only node at the top level
  const root = nodes[0];
  
  // Generate proof for each leaf
  const proofs = {};
  
  for (const hash of hashes) {
    proofs[hash] = generateProof(hash, root);
  }
  
  return {
    root: root.hash,
    leaves: hashes,
    proofs
  };
}

/**
 * Generate Merkle proof for a hash
 * @param {string} hash - Hash to generate proof for
 * @param {Object} root - Root node of Merkle tree
 * @returns {Array} - Merkle proof
 */
function generateProof(hash, root) {
  // Find leaf node
  const leaf = findLeaf(hash, root);
  
  if (!leaf) {
    throw new Error(`Hash ${hash} not found in Merkle tree`);
  }
  
  // Generate proof
  const proof = [];
  let current = leaf;
  
  while (current.parent) {
    const parent = current.parent;
    const isLeft = parent.left === current;
    
    proof.push({
      position: isLeft ? 'right' : 'left',
      hash: isLeft ? parent.right.hash : parent.left.hash
    });
    
    current = parent;
  }
  
  return proof;
}

/**
 * Find leaf node with given hash
 * @param {string} hash - Hash to find
 * @param {Object} node - Node to search
 * @returns {Object|null} - Leaf node or null if not found
 */
function findLeaf(hash, node) {
  if (!node) {
    return null;
  }
  
  if (!node.left && !node.right && node.hash === hash) {
    return node;
  }
  
  return findLeaf(hash, node.left) || findLeaf(hash, node.right);
}

/**
 * Verify Merkle proof
 * @param {string} hash - Hash to verify
 * @param {Array} proof - Merkle proof
 * @param {string} root - Merkle root
 * @returns {boolean} - True if proof is valid
 */
function verifyProof(hash, proof, root) {
  let current = hash;
  
  for (const step of proof) {
    if (step.position === 'left') {
      current = crypto.createHash('sha256')
        .update(step.hash + current)
        .digest('hex');
    } else {
      current = crypto.createHash('sha256')
        .update(current + step.hash)
        .digest('hex');
    }
  }
  
  return current === root;
}

/**
 * Create evidence record
 * @param {Object} evidence - Evidence data
 * @returns {Promise<Object>} - Evidence record
 */
async function createEvidenceRecord(evidence) {
  try {
    // In a real implementation, this would interact with a blockchain
    // For this placeholder, we'll simulate blockchain anchoring
    
    // Create evidence hash
    const evidenceHash = crypto.createHash('sha256')
      .update(JSON.stringify(evidence))
      .digest('hex');
    
    // Simulate Merkle tree
    const merkleTree = createMerkleTree([evidenceHash]);
    
    // Simulate blockchain transaction
    const blockchainRecord = {
      merkleRoot: merkleTree.root,
      proof: merkleTree.proofs[evidenceHash],
      submissionId: crypto.randomBytes(16).toString('hex'),
      blockNumber: Math.floor(Math.random() * 1000000),
      blockHash: crypto.randomBytes(32).toString('hex'),
      timestamp: Math.floor(Date.now() / 1000),
      transactionHash: crypto.randomBytes(32).toString('hex')
    };
    
    logger.info(`Evidence anchored to blockchain: ${evidenceHash}`);
    
    return {
      evidenceHash,
      blockchain: blockchainRecord
    };
  } catch (error) {
    logger.error('Failed to create evidence record', error);
    throw error;
  }
}

/**
 * Verify evidence
 * @param {string} evidenceHash - Evidence hash
 * @param {Array} proof - Merkle proof
 * @param {string} merkleRoot - Merkle root
 * @param {string} submissionId - Submission ID
 * @returns {Promise<Object>} - Verification result
 */
async function verifyEvidence(evidenceHash, proof, merkleRoot, submissionId) {
  try {
    // In a real implementation, this would verify against a blockchain
    // For this placeholder, we'll simulate verification
    
    // Verify Merkle proof
    const isValid = verifyProof(evidenceHash, proof, merkleRoot);
    
    if (!isValid) {
      return {
        verified: false,
        reason: 'Invalid Merkle proof'
      };
    }
    
    // Simulate blockchain verification
    const isAnchored = true; // In a real implementation, this would check the blockchain
    
    if (!isAnchored) {
      return {
        verified: false,
        reason: 'Merkle root not found on blockchain'
      };
    }
    
    logger.info(`Evidence verified on blockchain: ${evidenceHash}`);
    
    return {
      verified: true,
      evidenceHash,
      merkleRoot,
      submissionId
    };
  } catch (error) {
    logger.error('Failed to verify evidence', error);
    throw error;
  }
}

module.exports = {
  createEvidenceRecord,
  verifyEvidence
};
