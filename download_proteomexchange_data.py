#!/usr/bin/env python3
"""
Download and analyze proteomics data from ProteomeXchange.
This script downloads metadata from ProteomeXchange and analyzes it for 18/82 patterns.
"""

import os
import sys
import logging
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime
import requests
import json
import re
import time
from bs4 import BeautifulSoup
from urllib.parse import urljoin

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('proteomexchange_analysis.log')
    ]
)
logger = logging.getLogger('ProteomeXchange_Analysis')

# Constants
PATTERN_1882_RATIO = 18 / 82
PATTERN_1882_THRESHOLD = 0.05  # 5% threshold for considering a match
PI = np.pi
RESULTS_DIR = "proteomexchange_results"
os.makedirs(RESULTS_DIR, exist_ok=True)

def fetch_proteomexchange_datasets(num_datasets=100):
    """Fetch metadata for ProteomeXchange datasets."""
    logger.info(f"Fetching metadata for {num_datasets} ProteomeXchange datasets...")
    
    # Base URL for ProteomeXchange
    base_url = "https://proteomecentral.proteomexchange.org/cgi/GetDataset"
    
    datasets = []
    for i in range(1, num_datasets + 1):
        dataset_id = f"PXD{i:06d}"
        url = f"{base_url}?ID={dataset_id}"
        
        try:
            logger.info(f"Fetching metadata for dataset {dataset_id}...")
            response = requests.get(url)
            
            if response.status_code == 200:
                # Parse the HTML response
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # Extract dataset information
                title = soup.find('h1').text.strip() if soup.find('h1') else "Unknown"
                
                # Extract species information
                species_list = []
                species_section = soup.find(string=re.compile("SpeciesList"))
                if species_section and species_section.parent:
                    species_text = species_section.parent.find_next('td').text.strip()
                    species_list = [s.strip() for s in species_text.split(';')]
                
                # Extract instrument information
                instrument = "Unknown"
                instrument_section = soup.find(string=re.compile("Instrument"))
                if instrument_section and instrument_section.parent:
                    instrument = instrument_section.parent.find_next('td').text.strip()
                
                # Extract publication information
                publications = []
                pub_section = soup.find(string=re.compile("Publication List"))
                if pub_section and pub_section.parent:
                    pub_elements = pub_section.parent.find_next_siblings(['p', 'div'])
                    for elem in pub_elements:
                        if elem.name == 'div':
                            break
                        pub_text = elem.text.strip()
                        if pub_text:
                            publications.append(pub_text)
                
                # Extract repository information
                repository = "Unknown"
                repo_section = soup.find(string=re.compile("HostingRepository"))
                if repo_section and repo_section.parent:
                    repository = repo_section.parent.find_next('td').text.strip()
                
                # Create dataset object
                dataset = {
                    "id": dataset_id,
                    "title": title,
                    "species": species_list,
                    "instrument": instrument,
                    "publications": publications,
                    "repository": repository,
                    "url": url
                }
                
                datasets.append(dataset)
                logger.info(f"Successfully fetched metadata for dataset {dataset_id}")
            else:
                logger.warning(f"Failed to fetch metadata for dataset {dataset_id}. Status code: {response.status_code}")
            
            # Be nice to the server
            time.sleep(1)
            
        except Exception as e:
            logger.error(f"Error fetching metadata for dataset {dataset_id}: {e}")
    
    # Save the datasets to a JSON file
    output_file = os.path.join(RESULTS_DIR, "proteomexchange_datasets.json")
    with open(output_file, 'w') as f:
        json.dump(datasets, f, indent=2)
    
    logger.info(f"Saved metadata for {len(datasets)} datasets to {output_file}")
    
    return datasets

def analyze_dataset_distribution(datasets):
    """Analyze the distribution of datasets across various dimensions."""
    logger.info("Analyzing dataset distribution...")
    
    # Count datasets by repository
    repository_counts = {}
    for dataset in datasets:
        repo = dataset.get("repository", "Unknown")
        repository_counts[repo] = repository_counts.get(repo, 0) + 1
    
    # Count datasets by species
    species_counts = {}
    for dataset in datasets:
        for species in dataset.get("species", []):
            species_counts[species] = species_counts.get(species, 0) + 1
    
    # Count datasets by instrument
    instrument_counts = {}
    for dataset in datasets:
        instrument = dataset.get("instrument", "Unknown")
        instrument_counts[instrument] = instrument_counts.get(instrument, 0) + 1
    
    # Analyze for 18/82 patterns
    results_1882 = []
    
    # Analyze repository distribution
    repo_values = list(repository_counts.values())
    if len(repo_values) > 1:
        result = analyze_1882_patterns(repo_values, "Repository Distribution")
        if result:
            results_1882.append(result)
    
    # Analyze species distribution
    species_values = list(species_counts.values())
    if len(species_values) > 1:
        result = analyze_1882_patterns(species_values, "Species Distribution")
        if result:
            results_1882.append(result)
    
    # Analyze instrument distribution
    instrument_values = list(instrument_counts.values())
    if len(instrument_values) > 1:
        result = analyze_1882_patterns(instrument_values, "Instrument Distribution")
        if result:
            results_1882.append(result)
    
    # Create visualizations
    visualize_dataset_distribution(repository_counts, species_counts, instrument_counts, results_1882)
    
    return {
        "repository_counts": repository_counts,
        "species_counts": species_counts,
        "instrument_counts": instrument_counts,
        "results_1882": results_1882
    }

def analyze_1882_patterns(data, feature_name):
    """Analyze a dataset for 18/82 patterns."""
    logger.info(f"Analyzing {feature_name} for 18/82 patterns...")
    
    # Check if we have enough data
    if len(data) < 2:
        logger.warning(f"Not enough data points in {feature_name} to test for 18/82 patterns")
        return None
    
    # Sort the data
    sorted_data = np.sort(data)
    total_sum = np.sum(sorted_data)
    
    # Find the best 18/82 split
    best_split_idx = None
    best_proximity = float('inf')
    
    for i in range(1, len(sorted_data)):
        lower_sum = np.sum(sorted_data[:i])
        upper_sum = np.sum(sorted_data[i:])
        
        if total_sum == 0:
            continue
            
        lower_ratio = lower_sum / total_sum
        upper_ratio = upper_sum / total_sum
        
        # Calculate proximity to 18/82 ratio
        proximity_to_1882 = abs((lower_ratio / upper_ratio) - (18 / 82))
        
        if proximity_to_1882 < best_proximity:
            best_proximity = proximity_to_1882
            best_split_idx = i
    
    if best_split_idx is None:
        logger.warning(f"Could not find a valid 18/82 split for {feature_name}")
        return None
        
    # Calculate the actual ratios
    lower_sum = np.sum(sorted_data[:best_split_idx])
    upper_sum = np.sum(sorted_data[best_split_idx:])
    
    if total_sum == 0:
        lower_ratio = 0
        upper_ratio = 0
    else:
        lower_ratio = lower_sum / total_sum
        upper_ratio = upper_sum / total_sum
    
    # Calculate proximity to 18/82
    proximity_percent = abs((lower_ratio / upper_ratio) - (18 / 82)) / (18 / 82) * 100
    is_1882_pattern = proximity_percent <= PATTERN_1882_THRESHOLD * 100
    
    result = {
        "feature": feature_name,
        "total_data_points": len(data),
        "split_index": best_split_idx,
        "lower_sum": float(lower_sum),
        "upper_sum": float(upper_sum),
        "lower_ratio": float(lower_ratio),
        "upper_ratio": float(upper_ratio),
        "proximity_to_1882_percent": float(proximity_percent),
        "is_1882_pattern": is_1882_pattern
    }
    
    logger.info(f"18/82 pattern analysis for {feature_name}:")
    logger.info(f"  Lower ratio: {lower_ratio:.4f}, Upper ratio: {upper_ratio:.4f}")
    logger.info(f"  Proximity to 18/82: {proximity_percent:.2f}%")
    logger.info(f"  18/82 pattern present: {is_1882_pattern}")
    
    return result

def visualize_dataset_distribution(repository_counts, species_counts, instrument_counts, results_1882):
    """Create visualizations of dataset distribution."""
    logger.info("Creating dataset distribution visualizations...")
    
    # Create a figure for repository distribution
    plt.figure(figsize=(12, 8))
    
    repos = list(repository_counts.keys())
    counts = list(repository_counts.values())
    
    # Sort by count
    sorted_indices = np.argsort(counts)[::-1]
    repos = [repos[i] for i in sorted_indices]
    counts = [counts[i] for i in sorted_indices]
    
    plt.bar(repos, counts, color='skyblue')
    plt.xlabel('Repository')
    plt.ylabel('Number of Datasets')
    plt.title('ProteomeXchange Dataset Distribution by Repository')
    plt.xticks(rotation=45, ha='right')
    plt.grid(axis='y', alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(os.path.join(RESULTS_DIR, 'repository_distribution.png'), dpi=300)
    plt.close()
    
    # Create a figure for species distribution (top 20)
    plt.figure(figsize=(12, 8))
    
    species = list(species_counts.keys())
    counts = list(species_counts.values())
    
    # Sort by count and take top 20
    sorted_indices = np.argsort(counts)[::-1][:20]
    species = [species[i] for i in sorted_indices]
    counts = [counts[i] for i in sorted_indices]
    
    plt.bar(species, counts, color='lightgreen')
    plt.xlabel('Species')
    plt.ylabel('Number of Datasets')
    plt.title('ProteomeXchange Dataset Distribution by Species (Top 20)')
    plt.xticks(rotation=45, ha='right')
    plt.grid(axis='y', alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(os.path.join(RESULTS_DIR, 'species_distribution.png'), dpi=300)
    plt.close()
    
    # Create a figure for instrument distribution (top 20)
    plt.figure(figsize=(12, 8))
    
    instruments = list(instrument_counts.keys())
    counts = list(instrument_counts.values())
    
    # Sort by count and take top 20
    sorted_indices = np.argsort(counts)[::-1][:20]
    instruments = [instruments[i] for i in sorted_indices]
    counts = [counts[i] for i in sorted_indices]
    
    plt.bar(instruments, counts, color='orange')
    plt.xlabel('Instrument')
    plt.ylabel('Number of Datasets')
    plt.title('ProteomeXchange Dataset Distribution by Instrument (Top 20)')
    plt.xticks(rotation=45, ha='right')
    plt.grid(axis='y', alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(os.path.join(RESULTS_DIR, 'instrument_distribution.png'), dpi=300)
    plt.close()
    
    # Create a figure for 18/82 pattern results
    if results_1882:
        plt.figure(figsize=(12, 8))
        
        features = [r["feature"] for r in results_1882]
        proximities = [r["proximity_to_1882_percent"] for r in results_1882]
        is_1882 = [r["is_1882_pattern"] for r in results_1882]
        
        colors = ['green' if x else 'red' for x in is_1882]
        
        plt.bar(features, proximities, color=colors)
        plt.axhline(y=PATTERN_1882_THRESHOLD * 100, color='black', linestyle='--', label=f'{PATTERN_1882_THRESHOLD * 100}% Threshold')
        
        plt.xlabel('Feature')
        plt.ylabel('Proximity to 18/82 (%)')
        plt.title('18/82 Pattern Analysis Results')
        plt.xticks(rotation=45, ha='right')
        plt.grid(axis='y', alpha=0.3)
        plt.legend()
        
        plt.tight_layout()
        plt.savefig(os.path.join(RESULTS_DIR, '1882_patterns.png'), dpi=300)
        plt.close()
    
    logger.info("Visualizations saved to the 'proteomexchange_results' directory.")

def main():
    """Main function to download and analyze ProteomeXchange data."""
    logger.info("Starting ProteomeXchange data analysis...")
    
    # Fetch dataset metadata
    datasets = fetch_proteomexchange_datasets(num_datasets=100)
    
    # Analyze dataset distribution
    distribution_results = analyze_dataset_distribution(datasets)
    
    # Print summary
    logger.info("\n=== ProteomeXchange Data Analysis Summary ===")
    logger.info(f"Total datasets analyzed: {len(datasets)}")
    logger.info(f"Number of repositories: {len(distribution_results['repository_counts'])}")
    logger.info(f"Number of species: {len(distribution_results['species_counts'])}")
    logger.info(f"Number of instruments: {len(distribution_results['instrument_counts'])}")
    logger.info(f"Features with 18/82 patterns: {sum(1 for r in distribution_results['results_1882'] if r['is_1882_pattern'])}")
    
    return {
        "datasets": datasets,
        "distribution_results": distribution_results
    }

if __name__ == "__main__":
    main()
