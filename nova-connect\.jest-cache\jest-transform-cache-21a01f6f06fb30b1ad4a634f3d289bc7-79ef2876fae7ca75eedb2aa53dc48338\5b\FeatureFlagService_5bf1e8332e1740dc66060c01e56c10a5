0403661344e8c7dbd6706851f07032df
/**
 * Feature Flag Service
 *
 * This service manages feature flags and subscription tier access.
 * It has been enhanced to support package-based feature controls and tenant-specific features.
 */

const fs = require('fs').promises;
const path = require('path');
const {
  ValidationError,
  NotFoundError,
  AuthorizationError
} = require('../utils/errors');
const PackageConfigRegistry = require('./PackageConfigRegistry');
const NodeCache = require('node-cache');
class FeatureFlagService {
  constructor(dataDir = path.join(__dirname, '../data')) {
    this.dataDir = dataDir;
    this.featureFlagsDir = path.join(this.dataDir, 'feature_flags');
    this.featureFlagsFile = path.join(this.featureFlagsDir, 'feature_flags.json');
    this.subscriptionTiersFile = path.join(this.featureFlagsDir, 'subscription_tiers.json');
    this.userEntitlementsFile = path.join(this.featureFlagsDir, 'user_entitlements.json');
    this.featureUsageFile = path.join(this.featureFlagsDir, 'feature_usage.json');

    // Initialize cache with 5-minute TTL
    this.cache = new NodeCache({
      stdTTL: 300,
      checkperiod: 60
    });

    // Initialize package config registry
    this.packageRegistry = new PackageConfigRegistry(dataDir);
    this.ensureDataDir();

    // Define subscription tiers
    this.tiers = {
      FREE: 'free',
      STANDARD: 'standard',
      PROFESSIONAL: 'professional',
      ENTERPRISE: 'enterprise'
    };

    // Define package tiers (aligned with GCP Marketplace)
    this.packageTiers = {
      CORE: 'core',
      SECURE: 'secure',
      ENTERPRISE: 'enterprise',
      AI_BOOST: 'ai_boost'
    };
  }

  /**
   * Ensure the data directory exists
   */
  async ensureDataDir() {
    try {
      await fs.mkdir(this.featureFlagsDir, {
        recursive: true
      });

      // Initialize files if they don't exist
      await this.initializeFile(this.featureFlagsFile, this.getDefaultFeatureFlags());
      await this.initializeFile(this.subscriptionTiersFile, this.getDefaultSubscriptionTiers());
      await this.initializeFile(this.userEntitlementsFile, []);
      await this.initializeFile(this.featureUsageFile, []);
    } catch (error) {
      console.error('Error creating feature flags directory:', error);
      throw error;
    }
  }

  /**
   * Initialize a file with default data if it doesn't exist
   */
  async initializeFile(filePath, defaultData) {
    try {
      await fs.access(filePath);
    } catch (error) {
      if (error.code === 'ENOENT') {
        // File doesn't exist, create it with default data
        await fs.writeFile(filePath, JSON.stringify(defaultData, null, 2));
      } else {
        throw error;
      }
    }
  }

  /**
   * Load data from file
   */
  async loadData(filePath) {
    try {
      const data = await fs.readFile(filePath, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      if (error.code === 'ENOENT') {
        // File doesn't exist, return empty array or default data
        if (filePath === this.featureFlagsFile) {
          return this.getDefaultFeatureFlags();
        } else if (filePath === this.subscriptionTiersFile) {
          return this.getDefaultSubscriptionTiers();
        }
        return [];
      }
      console.error(`Error loading data from ${filePath}:`, error);
      throw error;
    }
  }

  /**
   * Save data to file
   */
  async saveData(filePath, data) {
    try {
      await fs.writeFile(filePath, JSON.stringify(data, null, 2));
    } catch (error) {
      console.error(`Error saving data to ${filePath}:`, error);
      throw error;
    }
  }

  /**
   * Get default feature flags
   */
  getDefaultFeatureFlags() {
    return [
    // Core Features - Available in all tiers
    {
      id: 'core.basic_connectors',
      name: 'Basic Connectors',
      description: 'Connect to basic API endpoints',
      category: 'core',
      enabled: true,
      tiers: [this.tiers.FREE, this.tiers.STANDARD, this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE],
      limits: {
        [this.tiers.FREE]: {
          connections: 3
        },
        [this.tiers.STANDARD]: {
          connections: 10
        },
        [this.tiers.PROFESSIONAL]: {
          connections: 50
        },
        [this.tiers.ENTERPRISE]: {
          connections: -1
        } // Unlimited
      }
    }, {
      id: 'core.manual_execution',
      name: 'Manual Execution',
      description: 'Manually execute API operations',
      category: 'core',
      enabled: true,
      tiers: [this.tiers.FREE, this.tiers.STANDARD, this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE],
      limits: {
        [this.tiers.FREE]: {
          operations_per_day: 50
        },
        [this.tiers.STANDARD]: {
          operations_per_day: 500
        },
        [this.tiers.PROFESSIONAL]: {
          operations_per_day: 5000
        },
        [this.tiers.ENTERPRISE]: {
          operations_per_day: -1
        } // Unlimited
      }
    }, {
      id: 'core.basic_monitoring',
      name: 'Basic Monitoring',
      description: 'Basic API monitoring capabilities',
      category: 'core',
      enabled: true,
      tiers: [this.tiers.FREE, this.tiers.STANDARD, this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE]
    },
    // Workflow Features
    {
      id: 'workflow.basic',
      name: 'Basic Workflows',
      description: 'Create simple sequential workflows',
      category: 'workflow',
      enabled: true,
      tiers: [this.tiers.FREE, this.tiers.STANDARD, this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE],
      limits: {
        [this.tiers.FREE]: {
          workflows: 1,
          actions_per_workflow: 5
        },
        [this.tiers.STANDARD]: {
          workflows: 5,
          actions_per_workflow: 10
        },
        [this.tiers.PROFESSIONAL]: {
          workflows: 20,
          actions_per_workflow: 50
        },
        [this.tiers.ENTERPRISE]: {
          workflows: -1,
          actions_per_workflow: -1
        } // Unlimited
      }
    }, {
      id: 'workflow.advanced',
      name: 'Advanced Workflows',
      description: 'Create complex workflows with conditions and branching',
      category: 'workflow',
      enabled: true,
      tiers: [this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE]
    }, {
      id: 'workflow.scheduled',
      name: 'Scheduled Workflows',
      description: 'Schedule workflows to run automatically',
      category: 'workflow',
      enabled: true,
      tiers: [this.tiers.STANDARD, this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE],
      limits: {
        [this.tiers.STANDARD]: {
          scheduled_workflows: 2
        },
        [this.tiers.PROFESSIONAL]: {
          scheduled_workflows: 10
        },
        [this.tiers.ENTERPRISE]: {
          scheduled_workflows: -1
        } // Unlimited
      }
    }, {
      id: 'workflow.event_triggered',
      name: 'Event-Triggered Workflows',
      description: 'Trigger workflows based on events',
      category: 'workflow',
      enabled: true,
      tiers: [this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE]
    },
    // Export/Import Features
    {
      id: 'export_import.basic',
      name: 'Basic Export/Import',
      description: 'Basic configuration export and import',
      category: 'export_import',
      enabled: true,
      tiers: [this.tiers.STANDARD, this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE]
    }, {
      id: 'export_import.advanced',
      name: 'Advanced Export/Import',
      description: 'Advanced configuration export and import with selective options',
      category: 'export_import',
      enabled: true,
      tiers: [this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE]
    },
    // Security Features
    {
      id: 'security.basic',
      name: 'Basic Security',
      description: 'Basic security features',
      category: 'security',
      enabled: true,
      tiers: [this.tiers.FREE, this.tiers.STANDARD, this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE]
    }, {
      id: 'security.advanced',
      name: 'Advanced Security',
      description: 'Advanced security features including IP restrictions',
      category: 'security',
      enabled: true,
      tiers: [this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE]
    }, {
      id: 'security.enterprise',
      name: 'Enterprise Security',
      description: 'Enterprise-grade security features',
      category: 'security',
      enabled: true,
      tiers: [this.tiers.ENTERPRISE]
    },
    // Monitoring and Alerting Features
    {
      id: 'monitoring.advanced',
      name: 'Advanced Monitoring',
      description: 'Advanced API monitoring capabilities',
      category: 'monitoring',
      enabled: true,
      tiers: [this.tiers.STANDARD, this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE]
    }, {
      id: 'monitoring.alerting',
      name: 'Alerting',
      description: 'Set up alerts for API monitoring',
      category: 'monitoring',
      enabled: true,
      tiers: [this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE],
      limits: {
        [this.tiers.PROFESSIONAL]: {
          alerts: 10
        },
        [this.tiers.ENTERPRISE]: {
          alerts: -1
        } // Unlimited
      }
    },
    // Analytics Features
    {
      id: 'analytics.basic',
      name: 'Basic Analytics',
      description: 'Basic analytics and reporting',
      category: 'analytics',
      enabled: true,
      tiers: [this.tiers.STANDARD, this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE]
    }, {
      id: 'analytics.advanced',
      name: 'Advanced Analytics',
      description: 'Advanced analytics and reporting',
      category: 'analytics',
      enabled: true,
      tiers: [this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE]
    }, {
      id: 'analytics.custom_reports',
      name: 'Custom Reports',
      description: 'Create and schedule custom reports',
      category: 'analytics',
      enabled: true,
      tiers: [this.tiers.ENTERPRISE]
    },
    // AI Features
    {
      id: 'ai.connector_generation',
      name: 'AI-Assisted Connector Creation',
      description: 'Generate connector configurations from API documentation',
      category: 'ai',
      enabled: true,
      tiers: [this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE],
      limits: {
        [this.tiers.PROFESSIONAL]: {
          generations_per_day: 5
        },
        [this.tiers.ENTERPRISE]: {
          generations_per_day: 20
        }
      }
    }, {
      id: 'ai.natural_language',
      name: 'Natural Language API Queries',
      description: 'Create connectors using natural language descriptions',
      category: 'ai',
      enabled: true,
      tiers: [this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE],
      limits: {
        [this.tiers.PROFESSIONAL]: {
          queries_per_day: 10
        },
        [this.tiers.ENTERPRISE]: {
          queries_per_day: 50
        }
      }
    }, {
      id: 'ai.error_resolution',
      name: 'Intelligent Error Resolution',
      description: 'AI-powered suggestions for fixing API errors',
      category: 'ai',
      enabled: true,
      tiers: [this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE],
      limits: {
        [this.tiers.PROFESSIONAL]: {
          suggestions_per_day: 20
        },
        [this.tiers.ENTERPRISE]: {
          suggestions_per_day: 100
        }
      }
    }, {
      id: 'ai.workflow_optimization',
      name: 'Predictive Workflow Optimization',
      description: 'AI-powered suggestions for optimizing workflows',
      category: 'ai',
      enabled: true,
      tiers: [this.tiers.ENTERPRISE],
      limits: {
        [this.tiers.ENTERPRISE]: {
          optimizations_per_day: 10
        }
      }
    },
    // Governance Features
    {
      id: 'governance.approvals',
      name: 'Approval Workflows',
      description: 'Create and manage multi-step approval workflows',
      category: 'governance',
      enabled: true,
      tiers: [this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE],
      limits: {
        [this.tiers.PROFESSIONAL]: {
          active_workflows: 5
        },
        [this.tiers.ENTERPRISE]: {
          active_workflows: -1
        } // Unlimited
      }
    }, {
      id: 'governance.compliance',
      name: 'Compliance Templates',
      description: 'Pre-built compliance templates for various regulations',
      category: 'governance',
      enabled: true,
      tiers: [this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE],
      limits: {
        [this.tiers.PROFESSIONAL]: {
          custom_templates: 2
        },
        [this.tiers.ENTERPRISE]: {
          custom_templates: -1
        } // Unlimited
      }
    }, {
      id: 'governance.data_lineage',
      name: 'Data Lineage Tracking',
      description: 'Track how data moves through different systems',
      category: 'governance',
      enabled: true,
      tiers: [this.tiers.ENTERPRISE],
      limits: {
        [this.tiers.ENTERPRISE]: {
          tracking_depth: 3
        }
      }
    },
    // Security Features
    {
      id: 'security.ip_restrictions',
      name: 'IP Restrictions',
      description: 'Restrict API access to specific IP addresses',
      category: 'security',
      enabled: true,
      tiers: [this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE],
      limits: {
        [this.tiers.PROFESSIONAL]: {
          restrictions_per_resource: 5
        },
        [this.tiers.ENTERPRISE]: {
          restrictions_per_resource: -1
        } // Unlimited
      }
    }, {
      id: 'security.encryption',
      name: 'Advanced Encryption',
      description: 'Manage encryption keys and encrypt sensitive data',
      category: 'security',
      enabled: true,
      tiers: [this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE],
      limits: {
        [this.tiers.PROFESSIONAL]: {
          encryption_keys: 10
        },
        [this.tiers.ENTERPRISE]: {
          encryption_keys: -1
        } // Unlimited
      }
    }, {
      id: 'security.policies',
      name: 'Custom Security Policies',
      description: 'Create and enforce custom security policies',
      category: 'security',
      enabled: true,
      tiers: [this.tiers.ENTERPRISE],
      limits: {
        [this.tiers.ENTERPRISE]: {
          policies: -1
        } // Unlimited
      }
    },
    // Advanced Monitoring Features
    {
      id: 'monitoring.anomaly_detection',
      name: 'Anomaly Detection',
      description: 'Automatically detect unusual patterns in API usage',
      category: 'monitoring',
      enabled: true,
      tiers: [this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE],
      limits: {
        [this.tiers.PROFESSIONAL]: {
          detection_frequency: 'daily'
        },
        [this.tiers.ENTERPRISE]: {
          detection_frequency: 'hourly'
        }
      }
    },
    // Advanced Analytics Features
    {
      id: 'analytics.custom_reports',
      name: 'Custom Reports',
      description: 'Create and schedule custom reports',
      category: 'analytics',
      enabled: true,
      tiers: [this.tiers.ENTERPRISE],
      limits: {
        [this.tiers.ENTERPRISE]: {
          reports_per_month: 50
        }
      }
    }, {
      id: 'analytics.scheduled_reports',
      name: 'Scheduled Reports',
      description: 'Schedule reports to run automatically',
      category: 'analytics',
      enabled: true,
      tiers: [this.tiers.ENTERPRISE],
      limits: {
        [this.tiers.ENTERPRISE]: {
          scheduled_reports: 10
        }
      }
    }, {
      id: 'analytics.export_formats',
      name: 'Advanced Export Formats',
      description: 'Export reports in various formats (PDF, CSV, Excel)',
      category: 'analytics',
      enabled: true,
      tiers: [this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE],
      limits: {
        [this.tiers.PROFESSIONAL]: {
          formats: ['CSV']
        },
        [this.tiers.ENTERPRISE]: {
          formats: ['CSV', 'PDF', 'Excel']
        }
      }
    }];
  }

  /**
   * Get default subscription tiers
   */
  getDefaultSubscriptionTiers() {
    return [{
      id: this.tiers.FREE,
      name: 'Free',
      description: 'Basic functionality for small projects',
      price: 0,
      features: ['core.basic_connectors', 'core.manual_execution', 'core.basic_monitoring', 'workflow.basic', 'security.basic'],
      limits: {
        connections: 3,
        operations_per_day: 50,
        workflows: 1,
        actions_per_workflow: 5
      }
    }, {
      id: this.tiers.STANDARD,
      name: 'Standard',
      description: 'Standard functionality for growing teams',
      price: 49,
      features: ['core.basic_connectors', 'core.manual_execution', 'core.basic_monitoring', 'workflow.basic', 'workflow.scheduled', 'export_import.basic', 'security.basic', 'monitoring.advanced', 'analytics.basic'],
      limits: {
        connections: 10,
        operations_per_day: 500,
        workflows: 5,
        actions_per_workflow: 10,
        scheduled_workflows: 2
      }
    }, {
      id: this.tiers.PROFESSIONAL,
      name: 'Professional',
      description: 'Advanced functionality for professional teams',
      price: 149,
      features: ['core.basic_connectors', 'core.manual_execution', 'core.basic_monitoring', 'workflow.basic', 'workflow.advanced', 'workflow.scheduled', 'workflow.event_triggered', 'export_import.basic', 'export_import.advanced', 'security.basic', 'security.advanced', 'security.ip_restrictions', 'security.encryption', 'monitoring.advanced', 'monitoring.alerting', 'monitoring.anomaly_detection', 'analytics.basic', 'analytics.advanced', 'analytics.export_formats', 'ai.connector_generation', 'ai.natural_language', 'ai.error_resolution', 'governance.approvals', 'governance.compliance'],
      limits: {
        connections: 50,
        operations_per_day: 5000,
        workflows: 20,
        actions_per_workflow: 50,
        scheduled_workflows: 10,
        alerts: 10
      }
    }, {
      id: this.tiers.ENTERPRISE,
      name: 'Enterprise',
      description: 'Enterprise-grade functionality for large organizations',
      price: 499,
      features: ['core.basic_connectors', 'core.manual_execution', 'core.basic_monitoring', 'workflow.basic', 'workflow.advanced', 'workflow.scheduled', 'workflow.event_triggered', 'export_import.basic', 'export_import.advanced', 'security.basic', 'security.advanced', 'security.enterprise', 'security.ip_restrictions', 'security.encryption', 'security.policies', 'monitoring.advanced', 'monitoring.alerting', 'monitoring.anomaly_detection', 'analytics.basic', 'analytics.advanced', 'analytics.custom_reports', 'analytics.scheduled_reports', 'analytics.export_formats', 'ai.connector_generation', 'ai.natural_language', 'ai.error_resolution', 'ai.workflow_optimization', 'governance.approvals', 'governance.compliance', 'governance.data_lineage'],
      limits: {
        connections: -1,
        // Unlimited
        operations_per_day: -1,
        // Unlimited
        workflows: -1,
        // Unlimited
        actions_per_workflow: -1,
        // Unlimited
        scheduled_workflows: -1,
        // Unlimited
        alerts: -1 // Unlimited
      }
    }];
  }

  /**
   * Get all feature flags
   */
  async getAllFeatureFlags() {
    return await this.loadData(this.featureFlagsFile);
  }

  /**
   * Get feature flag by ID
   */
  async getFeatureFlagById(id) {
    const featureFlags = await this.loadData(this.featureFlagsFile);
    const featureFlag = featureFlags.find(f => f.id === id);
    if (!featureFlag) {
      throw new NotFoundError(`Feature flag with ID ${id} not found`);
    }
    return featureFlag;
  }

  /**
   * Update feature flag
   */
  async updateFeatureFlag(id, data) {
    const featureFlags = await this.loadData(this.featureFlagsFile);
    const index = featureFlags.findIndex(f => f.id === id);
    if (index === -1) {
      throw new NotFoundError(`Feature flag with ID ${id} not found`);
    }

    // Update feature flag
    featureFlags[index] = {
      ...featureFlags[index],
      ...data,
      id: featureFlags[index].id // Ensure ID doesn't change
    };
    await this.saveData(this.featureFlagsFile, featureFlags);
    return featureFlags[index];
  }

  /**
   * Get all subscription tiers
   */
  async getAllSubscriptionTiers() {
    return await this.loadData(this.subscriptionTiersFile);
  }

  /**
   * Get subscription tier by ID
   */
  async getSubscriptionTierById(id) {
    const subscriptionTiers = await this.loadData(this.subscriptionTiersFile);
    const subscriptionTier = subscriptionTiers.find(t => t.id === id);
    if (!subscriptionTier) {
      throw new NotFoundError(`Subscription tier with ID ${id} not found`);
    }
    return subscriptionTier;
  }

  /**
   * Get user entitlement
   */
  async getUserEntitlement(userId) {
    const userEntitlements = await this.loadData(this.userEntitlementsFile);
    let userEntitlement = userEntitlements.find(e => e.userId === userId);
    if (!userEntitlement) {
      // Create default entitlement for user
      userEntitlement = {
        userId,
        tierId: this.tiers.FREE,
        customFeatures: [],
        customLimits: {},
        created: new Date().toISOString(),
        updated: new Date().toISOString()
      };
      userEntitlements.push(userEntitlement);
      await this.saveData(this.userEntitlementsFile, userEntitlements);
    }
    return userEntitlement;
  }

  /**
   * Update user entitlement
   */
  async updateUserEntitlement(userId, data) {
    const userEntitlements = await this.loadData(this.userEntitlementsFile);
    const index = userEntitlements.findIndex(e => e.userId === userId);
    if (index === -1) {
      // Create new entitlement
      const userEntitlement = {
        userId,
        tierId: data.tierId || this.tiers.FREE,
        customFeatures: data.customFeatures || [],
        customLimits: data.customLimits || {},
        created: new Date().toISOString(),
        updated: new Date().toISOString()
      };
      userEntitlements.push(userEntitlement);
    } else {
      // Update existing entitlement
      userEntitlements[index] = {
        ...userEntitlements[index],
        tierId: data.tierId || userEntitlements[index].tierId,
        customFeatures: data.customFeatures || userEntitlements[index].customFeatures,
        customLimits: data.customLimits || userEntitlements[index].customLimits,
        updated: new Date().toISOString()
      };
    }
    await this.saveData(this.userEntitlementsFile, userEntitlements);
    return index === -1 ? userEntitlements[userEntitlements.length - 1] : userEntitlements[index];
  }

  /**
   * Check if user has access to feature
   */
  async hasFeatureAccess(userId, featureId, tenantId = null) {
    // If tenant ID is provided, check tenant-specific access first
    if (tenantId) {
      // Check cache first
      const cacheKey = `tenant_feature_${tenantId}_${featureId}`;
      const cachedAccess = this.cache.get(cacheKey);
      if (cachedAccess !== undefined) {
        return cachedAccess;
      }

      // Check tenant-specific access
      const hasTenantAccess = await this.packageRegistry.hasTenantFeatureAccess(tenantId, featureId);

      // Cache the result
      this.cache.set(cacheKey, hasTenantAccess);
      if (hasTenantAccess) {
        return true;
      }
    }

    // Check cache for user access
    const cacheKey = `user_feature_${userId}_${featureId}`;
    const cachedAccess = this.cache.get(cacheKey);
    if (cachedAccess !== undefined) {
      return cachedAccess;
    }

    // Get user entitlement
    const userEntitlement = await this.getUserEntitlement(userId);

    // Check if user has custom access to this feature
    if (userEntitlement.customFeatures.includes(featureId)) {
      // Cache the result
      this.cache.set(cacheKey, true);
      return true;
    }

    // Get user's subscription tier
    const subscriptionTier = await this.getSubscriptionTierById(userEntitlement.tierId);

    // Check if feature is included in the subscription tier
    const hasAccess = subscriptionTier.features.includes(featureId);

    // Cache the result
    this.cache.set(cacheKey, hasAccess);
    return hasAccess;
  }

  /**
   * Get user's feature limit
   */
  async getFeatureLimit(userId, featureId, limitKey, tenantId = null) {
    // If tenant ID is provided, check tenant-specific limit first
    if (tenantId) {
      // Check cache first
      const cacheKey = `tenant_limit_${tenantId}_${featureId}_${limitKey}`;
      const cachedLimit = this.cache.get(cacheKey);
      if (cachedLimit !== undefined) {
        return cachedLimit;
      }

      // Check tenant-specific limit
      const tenantLimit = await this.packageRegistry.getTenantFeatureLimit(tenantId, limitKey);
      if (tenantLimit !== null) {
        // Cache the result
        this.cache.set(cacheKey, tenantLimit);
        return tenantLimit;
      }
    }

    // Check cache for user limit
    const cacheKey = `user_limit_${userId}_${featureId}_${limitKey}`;
    const cachedLimit = this.cache.get(cacheKey);
    if (cachedLimit !== undefined) {
      return cachedLimit;
    }

    // Get user entitlement
    const userEntitlement = await this.getUserEntitlement(userId);

    // Check if user has custom limit for this feature
    if (userEntitlement.customLimits[featureId] && userEntitlement.customLimits[featureId][limitKey] !== undefined) {
      const limit = userEntitlement.customLimits[featureId][limitKey];
      // Cache the result
      this.cache.set(cacheKey, limit);
      return limit;
    }

    // Get feature flag
    const featureFlag = await this.getFeatureFlagById(featureId);

    // Get user's subscription tier
    const tierId = userEntitlement.tierId;

    // Check if feature has limits for this tier
    if (featureFlag.limits && featureFlag.limits[tierId] && featureFlag.limits[tierId][limitKey] !== undefined) {
      const limit = featureFlag.limits[tierId][limitKey];
      // Cache the result
      this.cache.set(cacheKey, limit);
      return limit;
    }

    // No limit found
    this.cache.set(cacheKey, null);
    return null;
  }

  /**
   * Track feature usage
   */
  async trackFeatureUsage(userId, featureId, quantity = 1) {
    const now = new Date();
    const date = now.toISOString().split('T')[0]; // YYYY-MM-DD

    // Load feature usage data
    const featureUsage = await this.loadData(this.featureUsageFile);

    // Find or create usage record
    let usageRecord = featureUsage.find(u => u.userId === userId && u.featureId === featureId && u.date === date);
    if (!usageRecord) {
      // Create new usage record
      usageRecord = {
        userId,
        featureId,
        date,
        quantity: 0,
        created: now.toISOString(),
        updated: now.toISOString()
      };
      featureUsage.push(usageRecord);
    }

    // Update usage quantity
    usageRecord.quantity += quantity;
    usageRecord.updated = now.toISOString();

    // Save updated usage data
    await this.saveData(this.featureUsageFile, featureUsage);
    return usageRecord;
  }

  /**
   * Get feature usage for user
   */
  async getFeatureUsageForUser(userId, featureId, startDate, endDate) {
    // Load feature usage data
    const featureUsage = await this.loadData(this.featureUsageFile);

    // Filter usage records
    let filteredUsage = featureUsage.filter(u => u.userId === userId);
    if (featureId) {
      filteredUsage = filteredUsage.filter(u => u.featureId === featureId);
    }
    if (startDate) {
      filteredUsage = filteredUsage.filter(u => u.date >= startDate);
    }
    if (endDate) {
      filteredUsage = filteredUsage.filter(u => u.date <= endDate);
    }
    return filteredUsage;
  }

  /**
   * Check if user has reached feature limit
   */
  async hasReachedFeatureLimit(userId, featureId, limitKey) {
    // Get feature limit
    const limit = await this.getFeatureLimit(userId, featureId, limitKey);

    // If limit is null or -1, there is no limit
    if (limit === null || limit === -1) {
      return false;
    }

    // Get current usage
    const now = new Date();
    const today = now.toISOString().split('T')[0]; // YYYY-MM-DD

    // For daily limits
    if (limitKey.includes('per_day')) {
      const usage = await this.getFeatureUsageForUser(userId, featureId, today, today);
      const totalUsage = usage.reduce((sum, record) => sum + record.quantity, 0);
      return totalUsage >= limit;
    }

    // For other limits, we need to check the actual count
    // This would typically involve querying the relevant service
    // For now, we'll just return false
    return false;
  }

  /**
   * Get user's available features
   */
  async getUserAvailableFeatures(userId, tenantId = null) {
    // Check cache first
    const cacheKey = tenantId ? `tenant_user_features_${tenantId}_${userId}` : `user_features_${userId}`;
    const cachedFeatures = this.cache.get(cacheKey);
    if (cachedFeatures) {
      return cachedFeatures;
    }

    // Get tenant-specific features if tenant ID is provided
    let tenantFeatures = [];
    if (tenantId) {
      tenantFeatures = await this.packageRegistry.getTenantAvailableFeatures(tenantId);
    }

    // Get user entitlement
    const userEntitlement = await this.getUserEntitlement(userId);

    // Get user's subscription tier
    const subscriptionTier = await this.getSubscriptionTierById(userEntitlement.tierId);

    // Get all feature flags
    const featureFlags = await this.getAllFeatureFlags();

    // Filter features available to user
    const availableFeatures = featureFlags.filter(feature => feature.enabled && (subscriptionTier.features.includes(feature.id) || userEntitlement.customFeatures.includes(feature.id) || tenantFeatures.includes(feature.id)));

    // Cache the result
    this.cache.set(cacheKey, availableFeatures);
    return availableFeatures;
  }

  /**
   * Get tenant package information
   */
  async getTenantPackage(tenantId) {
    // Check cache first
    const cacheKey = `tenant_package_${tenantId}`;
    const cachedPackage = this.cache.get(cacheKey);
    if (cachedPackage) {
      return cachedPackage;
    }

    // Get tenant mapping
    const mapping = await this.packageRegistry.getTenantMapping(tenantId);

    // Get package details
    const packageDetails = await this.packageRegistry.getPackageById(mapping.packageId);

    // Cache the result
    this.cache.set(cacheKey, packageDetails);
    return packageDetails;
  }

  /**
   * Set tenant package
   */
  async setTenantPackage(tenantId, packageId, customFeatures = [], customLimits = {}) {
    // Set tenant mapping
    const mapping = await this.packageRegistry.setTenantMapping(tenantId, packageId, customFeatures, customLimits);

    // Invalidate cache
    this.cache.del(`tenant_package_${tenantId}`);
    this.cache.del(`tenant_user_features_${tenantId}_*`);
    return mapping;
  }

  /**
   * Get all packages
   */
  async getAllPackages() {
    return await this.packageRegistry.getAllPackages();
  }

  /**
   * Get package by ID
   */
  async getPackageById(id) {
    return await this.packageRegistry.getPackageById(id);
  }

  /**
   * Create a new package
   */
  async createPackage(packageData) {
    return await this.packageRegistry.createPackage(packageData);
  }

  /**
   * Update a package
   */
  async updatePackage(id, packageData) {
    return await this.packageRegistry.updatePackage(id, packageData);
  }

  /**
   * Delete a package
   */
  async deletePackage(id) {
    return await this.packageRegistry.deletePackage(id);
  }

  /**
   * Clear cache
   */
  clearCache() {
    this.cache.flushAll();
    this.packageRegistry.clearCache();
  }

  /**
   * Get user's subscription details
   */
  async getUserSubscriptionDetails(userId) {
    // Get user entitlement
    const userEntitlement = await this.getUserEntitlement(userId);

    // Get user's subscription tier
    const subscriptionTier = await this.getSubscriptionTierById(userEntitlement.tierId);

    // Get user's available features
    const availableFeatures = await this.getUserAvailableFeatures(userId);

    // Get feature usage
    const now = new Date();
    const today = now.toISOString().split('T')[0]; // YYYY-MM-DD
    const usage = await this.getFeatureUsageForUser(userId, null, today, today);

    // Compile subscription details
    return {
      userId,
      tier: {
        id: subscriptionTier.id,
        name: subscriptionTier.name,
        description: subscriptionTier.description,
        price: subscriptionTier.price
      },
      features: availableFeatures.map(feature => ({
        id: feature.id,
        name: feature.name,
        description: feature.description,
        category: feature.category
      })),
      limits: subscriptionTier.limits,
      customFeatures: userEntitlement.customFeatures,
      customLimits: userEntitlement.customLimits,
      usage: usage.reduce((result, record) => {
        result[record.featureId] = (result[record.featureId] || 0) + record.quantity;
        return result;
      }, {})
    };
  }
}
module.exports = FeatureFlagService;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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