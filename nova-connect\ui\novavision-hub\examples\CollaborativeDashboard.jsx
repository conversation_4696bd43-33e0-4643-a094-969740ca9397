/**
 * Collaborative Dashboard Example
 * 
 * This example demonstrates how to use the collaboration features.
 */

import React, { useState } from 'react';
import PropTypes from 'prop-types';
import {
  DashboardCard,
  CollaborationRoom,
  CollaborationChat,
  SharedState,
  TabPanel,
  SkipLink,
  ThemeSelector
} from '../components';
import { ThemeProvider } from '../theme';
import { PreferencesProvider } from '../preferences';
import { OfflineProvider } from '../offline';
import { AnimationProvider } from '../animation';
import { AuthProvider, MockAuthService } from '../auth';
import { CollaborationProvider, MockCollaborationService } from '../collaboration';

// Create mock services
const mockAuthService = new MockAuthService();
const mockCollaborationService = new MockCollaborationService();

/**
 * Collaborative Dashboard Content component
 * 
 * @param {Object} props - Component props
 * @param {Object} props.novaConnect - NovaConnect instance
 * @param {Object} props.novaShield - NovaShield instance
 * @param {Object} props.novaTrack - NovaTrack instance
 * @param {boolean} [props.enableLogging=false] - Whether to enable logging
 * @returns {React.ReactElement} Collaborative Dashboard Content component
 */
const CollaborativeDashboardContent = ({
  novaConnect,
  novaShield,
  novaTrack,
  enableLogging = false
}) => {
  const [activeTab, setActiveTab] = useState('dashboard');
  
  // Tabs
  const tabs = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      content: (
        <CollaborationRoom
          roomId="room-1"
          className="min-h-[600px]"
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <DashboardCard
              title="Compliance Score"
              subtitle="Shared compliance score"
            >
              <div className="p-4">
                <SharedState
                  stateKey="complianceScore"
                  initialValue={75}
                  showControls
                  showHistory
                >
                  {({ value, updateValue, isLoading }) => (
                    <div className="text-center">
                      <div className="text-4xl font-bold text-primary mb-4">
                        {isLoading ? (
                          <span className="inline-block w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin"></span>
                        ) : (
                          `${value}%`
                        )}
                      </div>
                      <div className="relative h-4 bg-background rounded-full overflow-hidden mb-4">
                        <div
                          className="absolute top-0 left-0 h-full bg-primary transition-all duration-500 ease-in-out"
                          style={{ width: `${value}%` }}
                        ></div>
                      </div>
                      <div className="flex justify-between mb-4">
                        <button
                          className="bg-error text-errorContrast px-3 py-1 rounded-md text-sm"
                          onClick={() => updateValue(Math.max(0, value - 5))}
                          disabled={isLoading || value <= 0}
                        >
                          -5%
                        </button>
                        <button
                          className="bg-success text-successContrast px-3 py-1 rounded-md text-sm"
                          onClick={() => updateValue(Math.min(100, value + 5))}
                          disabled={isLoading || value >= 100}
                        >
                          +5%
                        </button>
                      </div>
                      <p className="text-textSecondary text-sm">
                        This score is shared with all users in the room. Any changes you make will be visible to others in real-time.
                      </p>
                    </div>
                  )}
                </SharedState>
              </div>
            </DashboardCard>
            
            <DashboardCard
              title="Risk Assessment"
              subtitle="Collaborative risk assessment"
            >
              <div className="p-4">
                <SharedState
                  stateKey="riskAssessment"
                  initialValue={{
                    securityRisk: 'medium',
                    complianceRisk: 'low',
                    operationalRisk: 'high'
                  }}
                  showControls
                  showHistory
                >
                  {({ value, updateValue, isLoading }) => (
                    <div>
                      {isLoading ? (
                        <div className="flex justify-center py-4">
                          <span className="inline-block w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin"></span>
                        </div>
                      ) : (
                        <div className="space-y-4">
                          <div>
                            <label className="block text-textPrimary font-medium mb-1">
                              Security Risk
                            </label>
                            <select
                              value={value.securityRisk}
                              onChange={(e) => updateValue({ ...value, securityRisk: e.target.value })}
                              className="w-full px-3 py-2 border border-divider rounded-md bg-background text-textPrimary"
                              disabled={isLoading}
                            >
                              <option value="low">Low</option>
                              <option value="medium">Medium</option>
                              <option value="high">High</option>
                              <option value="critical">Critical</option>
                            </select>
                          </div>
                          
                          <div>
                            <label className="block text-textPrimary font-medium mb-1">
                              Compliance Risk
                            </label>
                            <select
                              value={value.complianceRisk}
                              onChange={(e) => updateValue({ ...value, complianceRisk: e.target.value })}
                              className="w-full px-3 py-2 border border-divider rounded-md bg-background text-textPrimary"
                              disabled={isLoading}
                            >
                              <option value="low">Low</option>
                              <option value="medium">Medium</option>
                              <option value="high">High</option>
                              <option value="critical">Critical</option>
                            </select>
                          </div>
                          
                          <div>
                            <label className="block text-textPrimary font-medium mb-1">
                              Operational Risk
                            </label>
                            <select
                              value={value.operationalRisk}
                              onChange={(e) => updateValue({ ...value, operationalRisk: e.target.value })}
                              className="w-full px-3 py-2 border border-divider rounded-md bg-background text-textPrimary"
                              disabled={isLoading}
                            >
                              <option value="low">Low</option>
                              <option value="medium">Medium</option>
                              <option value="high">High</option>
                              <option value="critical">Critical</option>
                            </select>
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </SharedState>
              </div>
            </DashboardCard>
            
            <DashboardCard
              title="Compliance Notes"
              subtitle="Shared notes for compliance"
              className="md:col-span-2"
            >
              <div className="p-4">
                <SharedState
                  stateKey="complianceNotes"
                  initialValue=""
                  showControls
                  showHistory
                >
                  {({ value, updateValue, isLoading }) => (
                    <div>
                      <textarea
                        value={value}
                        onChange={(e) => updateValue(e.target.value)}
                        placeholder="Enter compliance notes here..."
                        className="w-full h-32 px-3 py-2 border border-divider rounded-md bg-background text-textPrimary resize-none"
                        disabled={isLoading}
                      ></textarea>
                      {isLoading && (
                        <div className="mt-2 text-textSecondary text-sm">
                          <span className="inline-block w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin mr-1"></span>
                          Syncing changes...
                        </div>
                      )}
                    </div>
                  )}
                </SharedState>
              </div>
            </DashboardCard>
          </div>
        </CollaborationRoom>
      )
    },
    {
      id: 'chat',
      label: 'Chat',
      content: (
        <div className="h-[600px]">
          <CollaborationChat
            roomId="room-1"
            maxHeight={600}
          />
        </div>
      )
    }
  ];
  
  return (
    <div className="space-y-6">
      {/* Skip link for keyboard navigation */}
      <SkipLink targetId="main-content" />
      
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-textPrimary" id="dashboard-title">
          Collaborative Dashboard
        </h1>
        
        <div className="flex items-center space-x-2">
          <ThemeSelector variant="dropdown" />
        </div>
      </div>
      
      {/* Main content */}
      <main id="main-content" tabIndex="-1">
        <TabPanel
          tabs={tabs}
          defaultTab="dashboard"
          variant="pills"
          onTabChange={setActiveTab}
        />
      </main>
    </div>
  );
};

CollaborativeDashboardContent.propTypes = {
  novaConnect: PropTypes.object,
  novaShield: PropTypes.object,
  novaTrack: PropTypes.object,
  enableLogging: PropTypes.bool
};

/**
 * Collaborative Dashboard component
 * 
 * @param {Object} props - Component props
 * @param {Object} props.novaConnect - NovaConnect instance
 * @param {Object} props.novaShield - NovaShield instance
 * @param {Object} props.novaTrack - NovaTrack instance
 * @param {Object} [props.initialTheme] - Initial theme
 * @param {boolean} [props.enableSystemPreference=true] - Whether to enable system preference detection
 * @param {boolean} [props.enableLogging=false] - Whether to enable logging
 * @returns {React.ReactElement} Collaborative Dashboard component
 */
const CollaborativeDashboard = ({
  novaConnect,
  novaShield,
  novaTrack,
  initialTheme,
  enableSystemPreference = true,
  enableLogging = false
}) => {
  return (
    <ThemeProvider theme={initialTheme} enableSystemPreference={enableSystemPreference}>
      <PreferencesProvider>
        <OfflineProvider>
          <AnimationProvider>
            <AuthProvider authService={mockAuthService}>
              <CollaborationProvider collaborationService={mockCollaborationService}>
                <CollaborativeDashboardContent
                  novaConnect={novaConnect}
                  novaShield={novaShield}
                  novaTrack={novaTrack}
                  enableLogging={enableLogging}
                />
              </CollaborationProvider>
            </AuthProvider>
          </AnimationProvider>
        </OfflineProvider>
      </PreferencesProvider>
    </ThemeProvider>
  );
};

CollaborativeDashboard.propTypes = {
  novaConnect: PropTypes.object,
  novaShield: PropTypes.object,
  novaTrack: PropTypes.object,
  initialTheme: PropTypes.object,
  enableSystemPreference: PropTypes.bool,
  enableLogging: PropTypes.bool
};

export default CollaborativeDashboard;
