# Test NovaFuse Feature Flag System
# This script demonstrates how to use the NovaFuse API with different product tiers

Write-Host "Testing NovaFuse Feature Flag System" -ForegroundColor Yellow

# Test with NovaPrime tier (should have access to all features)
Write-Host "`nTesting with NovaPrime tier..." -ForegroundColor Green
Write-Host "Privacy Management API:" -ForegroundColor Cyan
Invoke-RestMethod -Uri "http://localhost:3001/privacy/management/processing-activities" -Method Get -Headers @{"X-Product-Tier" = "novaPrime"} | ConvertTo-Json

Write-Host "Security Assessment API:" -ForegroundColor Cyan
Invoke-RestMethod -Uri "http://localhost:3001/security/assessment/assessments" -Method Get -Headers @{"X-Product-Tier" = "novaPrime"} | ConvertTo-Json

Write-Host "Regulatory Compliance API:" -ForegroundColor Cyan
Invoke-RestMethod -Uri "http://localhost:3001/compliance/regulatory/frameworks" -Method Get -Headers @{"X-Product-Tier" = "novaPrime"} | ConvertTo-Json

Write-Host "Control Testing API:" -ForegroundColor Cyan
Invoke-RestMethod -Uri "http://localhost:3001/control/testing/tests" -Method Get -Headers @{"X-Product-Tier" = "novaPrime"} | ConvertTo-Json

Write-Host "ESG API:" -ForegroundColor Cyan
Invoke-RestMethod -Uri "http://localhost:3001/esg/metrics" -Method Get -Headers @{"X-Product-Tier" = "novaPrime"} | ConvertTo-Json

# Test with NovaCore tier (should have access to privacy, security, and compliance, but not control testing or ESG)
Write-Host "`nTesting with NovaCore tier..." -ForegroundColor Green
Write-Host "Privacy Management API:" -ForegroundColor Cyan
Invoke-RestMethod -Uri "http://localhost:3001/privacy/management/processing-activities" -Method Get -Headers @{"X-Product-Tier" = "novaCore"} | ConvertTo-Json

Write-Host "Security Assessment API:" -ForegroundColor Cyan
Invoke-RestMethod -Uri "http://localhost:3001/security/assessment/assessments" -Method Get -Headers @{"X-Product-Tier" = "novaCore"} | ConvertTo-Json

Write-Host "Regulatory Compliance API:" -ForegroundColor Cyan
Invoke-RestMethod -Uri "http://localhost:3001/compliance/regulatory/frameworks" -Method Get -Headers @{"X-Product-Tier" = "novaCore"} | ConvertTo-Json

Write-Host "Control Testing API (should be denied):" -ForegroundColor Cyan
try {
    Invoke-RestMethod -Uri "http://localhost:3001/control/testing/tests" -Method Get -Headers @{"X-Product-Tier" = "novaCore"} | ConvertTo-Json
} catch {
    Write-Host "Access denied as expected: $($_.Exception.Response.StatusCode)" -ForegroundColor Yellow
}

Write-Host "ESG API (should be denied):" -ForegroundColor Cyan
try {
    Invoke-RestMethod -Uri "http://localhost:3001/esg/metrics" -Method Get -Headers @{"X-Product-Tier" = "novaCore"} | ConvertTo-Json
} catch {
    Write-Host "Access denied as expected: $($_.Exception.Response.StatusCode)" -ForegroundColor Yellow
}

# Test with NovaShield tier (should have access to security and control testing, but not privacy, compliance, or ESG)
Write-Host "`nTesting with NovaShield tier..." -ForegroundColor Green
Write-Host "Privacy Management API (should be denied):" -ForegroundColor Cyan
try {
    Invoke-RestMethod -Uri "http://localhost:3001/privacy/management/processing-activities" -Method Get -Headers @{"X-Product-Tier" = "novaShield"} | ConvertTo-Json
} catch {
    Write-Host "Access denied as expected: $($_.Exception.Response.StatusCode)" -ForegroundColor Yellow
}

Write-Host "Security Assessment API:" -ForegroundColor Cyan
Invoke-RestMethod -Uri "http://localhost:3001/security/assessment/assessments" -Method Get -Headers @{"X-Product-Tier" = "novaShield"} | ConvertTo-Json

Write-Host "Regulatory Compliance API:" -ForegroundColor Cyan
Invoke-RestMethod -Uri "http://localhost:3001/compliance/regulatory/frameworks" -Method Get -Headers @{"X-Product-Tier" = "novaShield"} | ConvertTo-Json

Write-Host "Control Testing API:" -ForegroundColor Cyan
Invoke-RestMethod -Uri "http://localhost:3001/control/testing/tests" -Method Get -Headers @{"X-Product-Tier" = "novaShield"} | ConvertTo-Json

Write-Host "ESG API (should be denied):" -ForegroundColor Cyan
try {
    Invoke-RestMethod -Uri "http://localhost:3001/esg/metrics" -Method Get -Headers @{"X-Product-Tier" = "novaShield"} | ConvertTo-Json
} catch {
    Write-Host "Access denied as expected: $($_.Exception.Response.StatusCode)" -ForegroundColor Yellow
}

# Test with NovaLearn tier (should have access to compliance only)
Write-Host "`nTesting with NovaLearn tier..." -ForegroundColor Green
Write-Host "Privacy Management API (should be denied):" -ForegroundColor Cyan
try {
    Invoke-RestMethod -Uri "http://localhost:3001/privacy/management/processing-activities" -Method Get -Headers @{"X-Product-Tier" = "novaLearn"} | ConvertTo-Json
} catch {
    Write-Host "Access denied as expected: $($_.Exception.Response.StatusCode)" -ForegroundColor Yellow
}

Write-Host "Security Assessment API (should be denied):" -ForegroundColor Cyan
try {
    Invoke-RestMethod -Uri "http://localhost:3001/security/assessment/assessments" -Method Get -Headers @{"X-Product-Tier" = "novaLearn"} | ConvertTo-Json
} catch {
    Write-Host "Access denied as expected: $($_.Exception.Response.StatusCode)" -ForegroundColor Yellow
}

Write-Host "Regulatory Compliance API:" -ForegroundColor Cyan
Invoke-RestMethod -Uri "http://localhost:3001/compliance/regulatory/frameworks" -Method Get -Headers @{"X-Product-Tier" = "novaLearn"} | ConvertTo-Json

Write-Host "Control Testing API (should be denied):" -ForegroundColor Cyan
try {
    Invoke-RestMethod -Uri "http://localhost:3001/control/testing/tests" -Method Get -Headers @{"X-Product-Tier" = "novaLearn"} | ConvertTo-Json
} catch {
    Write-Host "Access denied as expected: $($_.Exception.Response.StatusCode)" -ForegroundColor Yellow
}

Write-Host "ESG API (should be denied):" -ForegroundColor Cyan
try {
    Invoke-RestMethod -Uri "http://localhost:3001/esg/metrics" -Method Get -Headers @{"X-Product-Tier" = "novaLearn"} | ConvertTo-Json
} catch {
    Write-Host "Access denied as expected: $($_.Exception.Response.StatusCode)" -ForegroundColor Yellow
}

Write-Host "`nFeature Flag System Test Complete" -ForegroundColor Green
