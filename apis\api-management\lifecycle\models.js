/**
 * @swagger
 * components:
 *   schemas:
 *     API:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: Unique identifier for the API
 *         name:
 *           type: string
 *           description: Name of the API
 *         description:
 *           type: string
 *           description: Description of the API
 *         version:
 *           type: string
 *           description: Current version of the API
 *         status:
 *           type: string
 *           enum: [planning, development, testing, staging, production, deprecated, retired]
 *           description: Current status of the API
 *         type:
 *           type: string
 *           enum: [rest, graphql, soap, grpc, webhook]
 *           description: Type of API
 *         owner:
 *           type: string
 *           description: Owner of the API
 *         team:
 *           type: string
 *           description: Team responsible for the API
 *         baseUrl:
 *           type: string
 *           description: Base URL of the API
 *         documentation:
 *           type: string
 *           description: URL to the API documentation
 *         repository:
 *           type: string
 *           description: URL to the API source code repository
 *         tags:
 *           type: array
 *           items:
 *             type: string
 *           description: Tags associated with the API
 *         versions:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/APIVersion'
 *         dependencies:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/APIDependency'
 *         consumers:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/APIConsumer'
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the API was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the API was last updated
 *       required:
 *         - id
 *         - name
 *         - version
 *         - status
 *         - type
 *         - owner
 *         - createdAt
 *         - updatedAt
 *     
 *     APIInput:
 *       type: object
 *       properties:
 *         name:
 *           type: string
 *           description: Name of the API
 *         description:
 *           type: string
 *           description: Description of the API
 *         version:
 *           type: string
 *           description: Current version of the API
 *         status:
 *           type: string
 *           enum: [planning, development, testing, staging, production, deprecated, retired]
 *           description: Current status of the API
 *         type:
 *           type: string
 *           enum: [rest, graphql, soap, grpc, webhook]
 *           description: Type of API
 *         owner:
 *           type: string
 *           description: Owner of the API
 *         team:
 *           type: string
 *           description: Team responsible for the API
 *         baseUrl:
 *           type: string
 *           description: Base URL of the API
 *         documentation:
 *           type: string
 *           description: URL to the API documentation
 *         repository:
 *           type: string
 *           description: URL to the API source code repository
 *         tags:
 *           type: array
 *           items:
 *             type: string
 *           description: Tags associated with the API
 *       required:
 *         - name
 *         - version
 *         - status
 *         - type
 *         - owner
 *     
 *     APIVersion:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: Unique identifier for the API version
 *         apiId:
 *           type: string
 *           description: ID of the API this version belongs to
 *         version:
 *           type: string
 *           description: Version number or identifier
 *         status:
 *           type: string
 *           enum: [planning, development, testing, staging, production, deprecated, retired]
 *           description: Status of this version
 *         releaseDate:
 *           type: string
 *           format: date
 *           description: Date when this version was released
 *         endOfLifeDate:
 *           type: string
 *           format: date
 *           description: Date when this version will be retired
 *         changeLog:
 *           type: string
 *           description: Description of changes in this version
 *         baseUrl:
 *           type: string
 *           description: Base URL for this version
 *         documentation:
 *           type: string
 *           description: URL to the documentation for this version
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the version was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the version was last updated
 *       required:
 *         - id
 *         - apiId
 *         - version
 *         - status
 *         - createdAt
 *         - updatedAt
 *     
 *     APIVersionInput:
 *       type: object
 *       properties:
 *         version:
 *           type: string
 *           description: Version number or identifier
 *         status:
 *           type: string
 *           enum: [planning, development, testing, staging, production, deprecated, retired]
 *           description: Status of this version
 *         releaseDate:
 *           type: string
 *           format: date
 *           description: Date when this version was released
 *         endOfLifeDate:
 *           type: string
 *           format: date
 *           description: Date when this version will be retired
 *         changeLog:
 *           type: string
 *           description: Description of changes in this version
 *         baseUrl:
 *           type: string
 *           description: Base URL for this version
 *         documentation:
 *           type: string
 *           description: URL to the documentation for this version
 *       required:
 *         - version
 *         - status
 *     
 *     APIDependency:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: Unique identifier for the dependency
 *         apiId:
 *           type: string
 *           description: ID of the API that has the dependency
 *         dependencyType:
 *           type: string
 *           enum: [api, service, database, library, other]
 *           description: Type of dependency
 *         name:
 *           type: string
 *           description: Name of the dependency
 *         version:
 *           type: string
 *           description: Version of the dependency
 *         description:
 *           type: string
 *           description: Description of the dependency
 *         criticality:
 *           type: string
 *           enum: [critical, high, medium, low]
 *           description: Criticality of the dependency
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the dependency was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the dependency was last updated
 *       required:
 *         - id
 *         - apiId
 *         - dependencyType
 *         - name
 *         - createdAt
 *         - updatedAt
 *     
 *     APIDependencyInput:
 *       type: object
 *       properties:
 *         dependencyType:
 *           type: string
 *           enum: [api, service, database, library, other]
 *           description: Type of dependency
 *         name:
 *           type: string
 *           description: Name of the dependency
 *         version:
 *           type: string
 *           description: Version of the dependency
 *         description:
 *           type: string
 *           description: Description of the dependency
 *         criticality:
 *           type: string
 *           enum: [critical, high, medium, low]
 *           description: Criticality of the dependency
 *       required:
 *         - dependencyType
 *         - name
 *     
 *     APIConsumer:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: Unique identifier for the consumer
 *         apiId:
 *           type: string
 *           description: ID of the API being consumed
 *         name:
 *           type: string
 *           description: Name of the consumer
 *         type:
 *           type: string
 *           enum: [internal, external, partner]
 *           description: Type of consumer
 *         contact:
 *           type: string
 *           description: Contact information for the consumer
 *         usageLevel:
 *           type: string
 *           enum: [high, medium, low]
 *           description: Level of API usage by this consumer
 *         apiVersions:
 *           type: array
 *           items:
 *             type: string
 *           description: Versions of the API being used by this consumer
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the consumer was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the consumer was last updated
 *       required:
 *         - id
 *         - apiId
 *         - name
 *         - type
 *         - createdAt
 *         - updatedAt
 *     
 *     APIConsumerInput:
 *       type: object
 *       properties:
 *         name:
 *           type: string
 *           description: Name of the consumer
 *         type:
 *           type: string
 *           enum: [internal, external, partner]
 *           description: Type of consumer
 *         contact:
 *           type: string
 *           description: Contact information for the consumer
 *         usageLevel:
 *           type: string
 *           enum: [high, medium, low]
 *           description: Level of API usage by this consumer
 *         apiVersions:
 *           type: array
 *           items:
 *             type: string
 *           description: Versions of the API being used by this consumer
 *       required:
 *         - name
 *         - type
 */

// Sample APIs
const apis = [
  {
    id: 'api-001',
    name: 'User Management API',
    description: 'API for managing user accounts and authentication',
    version: '2.0.0',
    status: 'production',
    type: 'rest',
    owner: 'Identity Team',
    team: 'Platform Services',
    baseUrl: 'https://api.example.com/users',
    documentation: 'https://docs.example.com/apis/users',
    repository: 'https://github.com/example/user-api',
    tags: ['identity', 'authentication', 'users'],
    versions: [
      {
        id: 'ver-001',
        apiId: 'api-001',
        version: '1.0.0',
        status: 'deprecated',
        releaseDate: '2023-01-15',
        endOfLifeDate: '2024-01-15',
        changeLog: 'Initial release',
        baseUrl: 'https://api.example.com/v1/users',
        documentation: 'https://docs.example.com/apis/users/v1',
        createdAt: '2023-01-10T09:00:00Z',
        updatedAt: '2023-12-01T14:30:00Z'
      },
      {
        id: 'ver-002',
        apiId: 'api-001',
        version: '2.0.0',
        status: 'production',
        releaseDate: '2023-12-01',
        endOfLifeDate: null,
        changeLog: 'Added support for OAuth 2.0 and improved rate limiting',
        baseUrl: 'https://api.example.com/v2/users',
        documentation: 'https://docs.example.com/apis/users/v2',
        createdAt: '2023-11-15T11:30:00Z',
        updatedAt: '2023-12-01T14:30:00Z'
      }
    ],
    dependencies: [
      {
        id: 'dep-001',
        apiId: 'api-001',
        dependencyType: 'database',
        name: 'User Database',
        version: 'PostgreSQL 14',
        description: 'Primary database for user data',
        criticality: 'critical',
        createdAt: '2023-01-10T09:00:00Z',
        updatedAt: '2023-01-10T09:00:00Z'
      },
      {
        id: 'dep-002',
        apiId: 'api-001',
        dependencyType: 'service',
        name: 'Email Service',
        version: '1.5.0',
        description: 'Service for sending verification emails',
        criticality: 'high',
        createdAt: '2023-01-10T09:00:00Z',
        updatedAt: '2023-01-10T09:00:00Z'
      }
    ],
    consumers: [
      {
        id: 'con-001',
        apiId: 'api-001',
        name: 'Web Application',
        type: 'internal',
        contact: '<EMAIL>',
        usageLevel: 'high',
        apiVersions: ['2.0.0'],
        createdAt: '2023-01-15T10:00:00Z',
        updatedAt: '2023-12-05T15:45:00Z'
      },
      {
        id: 'con-002',
        apiId: 'api-001',
        name: 'Mobile Application',
        type: 'internal',
        contact: '<EMAIL>',
        usageLevel: 'high',
        apiVersions: ['2.0.0'],
        createdAt: '2023-01-15T10:00:00Z',
        updatedAt: '2023-12-05T15:45:00Z'
      },
      {
        id: 'con-003',
        apiId: 'api-001',
        name: 'Partner Integration',
        type: 'partner',
        contact: '<EMAIL>',
        usageLevel: 'medium',
        apiVersions: ['1.0.0', '2.0.0'],
        createdAt: '2023-02-10T14:20:00Z',
        updatedAt: '2023-12-05T15:45:00Z'
      }
    ],
    createdAt: '2023-01-10T09:00:00Z',
    updatedAt: '2023-12-01T14:30:00Z'
  },
  {
    id: 'api-002',
    name: 'Payment Processing API',
    description: 'API for processing payments and managing transactions',
    version: '1.5.0',
    status: 'production',
    type: 'rest',
    owner: 'Payments Team',
    team: 'Financial Services',
    baseUrl: 'https://api.example.com/payments',
    documentation: 'https://docs.example.com/apis/payments',
    repository: 'https://github.com/example/payment-api',
    tags: ['payments', 'transactions', 'financial'],
    versions: [
      {
        id: 'ver-003',
        apiId: 'api-002',
        version: '1.0.0',
        status: 'deprecated',
        releaseDate: '2023-03-01',
        endOfLifeDate: '2024-03-01',
        changeLog: 'Initial release',
        baseUrl: 'https://api.example.com/v1/payments',
        documentation: 'https://docs.example.com/apis/payments/v1',
        createdAt: '2023-02-15T13:45:00Z',
        updatedAt: '2023-08-10T11:20:00Z'
      },
      {
        id: 'ver-004',
        apiId: 'api-002',
        version: '1.5.0',
        status: 'production',
        releaseDate: '2023-08-10',
        endOfLifeDate: null,
        changeLog: 'Added support for recurring payments and improved error handling',
        baseUrl: 'https://api.example.com/v1.5/payments',
        documentation: 'https://docs.example.com/apis/payments/v1.5',
        createdAt: '2023-07-20T10:15:00Z',
        updatedAt: '2023-08-10T11:20:00Z'
      }
    ],
    dependencies: [
      {
        id: 'dep-003',
        apiId: 'api-002',
        dependencyType: 'api',
        name: 'User Management API',
        version: '2.0.0',
        description: 'API for user authentication and authorization',
        criticality: 'critical',
        createdAt: '2023-02-15T13:45:00Z',
        updatedAt: '2023-02-15T13:45:00Z'
      },
      {
        id: 'dep-004',
        apiId: 'api-002',
        dependencyType: 'service',
        name: 'Payment Gateway',
        version: '2.3.0',
        description: 'External payment processing service',
        criticality: 'critical',
        createdAt: '2023-02-15T13:45:00Z',
        updatedAt: '2023-02-15T13:45:00Z'
      }
    ],
    consumers: [
      {
        id: 'con-004',
        apiId: 'api-002',
        name: 'E-commerce Platform',
        type: 'internal',
        contact: '<EMAIL>',
        usageLevel: 'high',
        apiVersions: ['1.5.0'],
        createdAt: '2023-03-05T09:30:00Z',
        updatedAt: '2023-08-15T14:10:00Z'
      },
      {
        id: 'con-005',
        apiId: 'api-002',
        name: 'Subscription Service',
        type: 'internal',
        contact: '<EMAIL>',
        usageLevel: 'medium',
        apiVersions: ['1.5.0'],
        createdAt: '2023-03-05T09:30:00Z',
        updatedAt: '2023-08-15T14:10:00Z'
      }
    ],
    createdAt: '2023-02-15T13:45:00Z',
    updatedAt: '2023-08-10T11:20:00Z'
  },
  {
    id: 'api-003',
    name: 'Product Catalog API',
    description: 'API for managing product information and inventory',
    version: '1.0.0',
    status: 'development',
    type: 'graphql',
    owner: 'Product Team',
    team: 'E-commerce',
    baseUrl: 'https://api.example.com/graphql/products',
    documentation: 'https://docs.example.com/apis/products',
    repository: 'https://github.com/example/product-api',
    tags: ['products', 'inventory', 'catalog'],
    versions: [
      {
        id: 'ver-005',
        apiId: 'api-003',
        version: '1.0.0',
        status: 'development',
        releaseDate: null,
        endOfLifeDate: null,
        changeLog: 'Initial development',
        baseUrl: 'https://api.example.com/graphql/products',
        documentation: 'https://docs.example.com/apis/products/v1',
        createdAt: '2024-01-10T11:00:00Z',
        updatedAt: '2024-01-10T11:00:00Z'
      }
    ],
    dependencies: [
      {
        id: 'dep-005',
        apiId: 'api-003',
        dependencyType: 'database',
        name: 'Product Database',
        version: 'MongoDB 5.0',
        description: 'Database for product information',
        criticality: 'critical',
        createdAt: '2024-01-10T11:00:00Z',
        updatedAt: '2024-01-10T11:00:00Z'
      }
    ],
    consumers: [],
    createdAt: '2024-01-10T11:00:00Z',
    updatedAt: '2024-01-10T11:00:00Z'
  }
];

// Sample API versions (separate from APIs for direct API access)
const apiVersions = apis.flatMap(api => api.versions);

// Sample API dependencies (separate from APIs for direct API access)
const apiDependencies = apis.flatMap(api => api.dependencies);

// Sample API consumers (separate from APIs for direct API access)
const apiConsumers = apis.flatMap(api => api.consumers);

// API types for reference
const apiTypes = [
  {
    id: 'rest',
    name: 'REST API',
    description: 'Representational State Transfer API'
  },
  {
    id: 'graphql',
    name: 'GraphQL API',
    description: 'Query language for APIs'
  },
  {
    id: 'soap',
    name: 'SOAP API',
    description: 'Simple Object Access Protocol API'
  },
  {
    id: 'grpc',
    name: 'gRPC API',
    description: 'High-performance Remote Procedure Call framework'
  },
  {
    id: 'webhook',
    name: 'Webhook API',
    description: 'HTTP callbacks for event-driven architectures'
  }
];

// API statuses for reference
const apiStatuses = [
  {
    id: 'planning',
    name: 'Planning',
    description: 'API is in the planning phase'
  },
  {
    id: 'development',
    name: 'Development',
    description: 'API is in active development'
  },
  {
    id: 'testing',
    name: 'Testing',
    description: 'API is being tested'
  },
  {
    id: 'staging',
    name: 'Staging',
    description: 'API is deployed to staging environment'
  },
  {
    id: 'production',
    name: 'Production',
    description: 'API is in production'
  },
  {
    id: 'deprecated',
    name: 'Deprecated',
    description: 'API is deprecated and will be retired'
  },
  {
    id: 'retired',
    name: 'Retired',
    description: 'API is no longer available'
  }
];

module.exports = {
  apis,
  apiVersions,
  apiDependencies,
  apiConsumers,
  apiTypes,
  apiStatuses
};
