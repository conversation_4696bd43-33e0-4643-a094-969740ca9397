/**
 * OnboardingTour Component
 * 
 * A component for displaying a guided tour of the application.
 */

import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { useI18n } from '../i18n';
import { useAccessibility } from '../accessibility';
import { useOnboarding } from '../onboarding/OnboardingContext';
import OnboardingStep from './OnboardingStep';

/**
 * OnboardingTour component
 * 
 * @param {Object} props - Component props
 * @param {string} props.tourId - Tour ID
 * @param {Array} props.steps - Tour steps
 * @param {string} [props.title] - Tour title
 * @param {string} [props.description] - Tour description
 * @param {boolean} [props.autoStart=false] - Whether to start the tour automatically
 * @param {boolean} [props.showSkip=true] - Whether to show skip button
 * @param {boolean} [props.showProgress=true] - Whether to show progress indicator
 * @param {Function} [props.onComplete] - Function to call when the tour is completed
 * @param {Function} [props.onSkip] - Function to call when the tour is skipped
 * @param {Function} [props.onStepChange] - Function to call when the active step changes
 * @param {string} [props.className=''] - Additional CSS class names
 * @param {Object} [props.style={}] - Additional inline styles
 * @returns {React.ReactElement} OnboardingTour component
 */
const OnboardingTour = ({
  tourId,
  steps,
  title,
  description,
  autoStart = false,
  showSkip = true,
  showProgress = true,
  onComplete,
  onSkip,
  onStepChange,
  className = '',
  style = {}
}) => {
  // Hooks
  const { translate } = useI18n();
  const { settings } = useAccessibility();
  const {
    registerTour,
    startTour,
    endTour,
    activeTour,
    activeStep,
    isTourCompleted,
    nextStep,
    prevStep,
    skipTour
  } = useOnboarding();
  
  // State
  const [isActive, setIsActive] = useState(false);
  const [isCompleted, setIsCompleted] = useState(false);
  
  // Register tour
  useEffect(() => {
    registerTour(tourId, {
      title,
      description,
      steps
    });
    
    setIsCompleted(isTourCompleted(tourId));
  }, [tourId, title, description, steps, registerTour, isTourCompleted]);
  
  // Start tour automatically if autoStart is true
  useEffect(() => {
    if (autoStart && !isCompleted && !activeTour) {
      startTour(tourId);
    }
  }, [autoStart, isCompleted, activeTour, tourId, startTour]);
  
  // Update isActive when activeTour changes
  useEffect(() => {
    setIsActive(activeTour === tourId);
  }, [activeTour, tourId]);
  
  // Call onStepChange when activeStep changes
  useEffect(() => {
    if (isActive && onStepChange) {
      onStepChange(activeStep);
    }
  }, [isActive, activeStep, onStepChange]);
  
  // Handle tour completion
  const handleComplete = () => {
    setIsCompleted(true);
    
    if (onComplete) {
      onComplete();
    }
    
    endTour(true);
  };
  
  // Handle tour skip
  const handleSkip = () => {
    if (onSkip) {
      onSkip();
    }
    
    skipTour();
  };
  
  // Handle next step
  const handleNext = () => {
    if (activeStep === steps.length - 1) {
      handleComplete();
    } else {
      nextStep();
    }
  };
  
  // Start the tour
  const handleStart = () => {
    startTour(tourId);
  };
  
  // Reset the tour
  const handleReset = () => {
    setIsCompleted(false);
  };
  
  // If tour is not active, don't render anything
  if (!isActive) {
    return null;
  }
  
  // Get current step
  const currentStep = steps[activeStep];
  
  if (!currentStep) {
    return null;
  }
  
  return (
    <div className={`onboarding-tour ${className}`} style={style}>
      <OnboardingStep
        title={currentStep.title}
        description={currentStep.description}
        position={currentStep.position}
        targetSelector={currentStep.targetSelector}
        targetElement={currentStep.targetElement}
        stepNumber={activeStep}
        totalSteps={steps.length}
        showButtons={true}
        showProgress={showProgress}
        showSkip={showSkip && activeStep < steps.length - 1}
        showClose={true}
        onNext={handleNext}
        onPrev={prevStep}
        onSkip={handleSkip}
        onClose={handleSkip}
      />
    </div>
  );
};

OnboardingTour.propTypes = {
  tourId: PropTypes.string.isRequired,
  steps: PropTypes.arrayOf(
    PropTypes.shape({
      title: PropTypes.string.isRequired,
      description: PropTypes.string.isRequired,
      position: PropTypes.oneOf(['top', 'right', 'bottom', 'left']),
      targetSelector: PropTypes.string,
      targetElement: PropTypes.object
    })
  ).isRequired,
  title: PropTypes.string,
  description: PropTypes.string,
  autoStart: PropTypes.bool,
  showSkip: PropTypes.bool,
  showProgress: PropTypes.bool,
  onComplete: PropTypes.func,
  onSkip: PropTypes.func,
  onStepChange: PropTypes.func,
  className: PropTypes.string,
  style: PropTypes.object
};

export default OnboardingTour;
