
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>NovaConnect Coverage Report</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    h1, h2, h3 {
      color: #0066cc;
    }
    .summary {
      background-color: #f5f5f5;
      border-radius: 5px;
      padding: 20px;
      margin-bottom: 20px;
    }
    .status {
      font-size: 1.2em;
      font-weight: bold;
      padding: 10px;
      border-radius: 5px;
      display: inline-block;
      margin-bottom: 10px;
    }
    .pass {
      background-color: #dff0d8;
      color: #3c763d;
    }
    .fail {
      background-color: #f2dede;
      color: #a94442;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 20px;
    }
    th, td {
      padding: 10px;
      border: 1px solid #ddd;
      text-align: left;
    }
    th {
      background-color: #f5f5f5;
    }
    .low {
      background-color: #f2dede;
    }
    .medium {
      background-color: #fcf8e3;
    }
    .high {
      background-color: #dff0d8;
    }
    .progress-container {
      width: 100%;
      background-color: #f1f1f1;
      border-radius: 5px;
    }
    .progress-bar {
      height: 20px;
      border-radius: 5px;
      text-align: center;
      line-height: 20px;
      color: white;
    }
    .actions {
      margin-top: 20px;
    }
    .btn {
      display: inline-block;
      padding: 10px 20px;
      background-color: #0066cc;
      color: white;
      text-decoration: none;
      border-radius: 5px;
      margin-right: 10px;
    }
  </style>
</head>
<body>
  <h1>NovaConnect Coverage Report</h1>
  
  <div class="summary">
    <div class="status fail">
      FAIL: Coverage does not meet the 96% threshold
    </div>
    
    <h2>Overall Coverage</h2>
    <table>
      <tr>
        <th>Metric</th>
        <th>Coverage</th>
        <th>Status</th>
        <th>Progress</th>
      </tr>
      <tr class="low">
        <td>Statements</td>
        <td>48.71% (19/39)</td>
        <td>FAIL</td>
        <td>
          <div class="progress-container">
            <div class="progress-bar" style="width: 48.71%; background-color: rgb(130, 124, 0)">
              48.71%
            </div>
          </div>
        </td>
      </tr>
      <tr class="low">
        <td>Branches</td>
        <td>30.76% (8/26)</td>
        <td>FAIL</td>
        <td>
          <div class="progress-container">
            <div class="progress-bar" style="width: 30.76%; background-color: rgb(176, 78, 0)">
              30.76%
            </div>
          </div>
        </td>
      </tr>
      <tr class="low">
        <td>Functions</td>
        <td>75.00% (6/8)</td>
        <td>FAIL</td>
        <td>
          <div class="progress-container">
            <div class="progress-bar" style="width: 75%; background-color: rgb(63, 191, 0)">
              75.00%
            </div>
          </div>
        </td>
      </tr>
      <tr class="low">
        <td>Lines</td>
        <td>48.71% (19/39)</td>
        <td>FAIL</td>
        <td>
          <div class="progress-container">
            <div class="progress-bar" style="width: 48.71%; background-color: rgb(130, 124, 0)">
              48.71%
            </div>
          </div>
        </td>
      </tr>
    </table>
  </div>
  
  <h2>Files with Lowest Coverage</h2>
  <p>Focus on these files to improve overall coverage:</p>
  
  <table>
    <tr>
      <th>File</th>
      <th>Statements</th>
      <th>Branches</th>
      <th>Functions</th>
      <th>Lines</th>
    </tr>
    
    <tr>
      <td>D:\novafuse-api-superstore\testing-environment\__tests__\helpers\test-utils.js</td>
      <td class="low">48.71%</td>
      <td class="low">30.76%</td>
      <td class="low">75.00%</td>
      <td class="low">48.71%</td>
    </tr>
    
  </table>
  
  <div class="actions">
    <a href="details.html" class="btn">View Detailed Report</a>
    <a href="javascript:window.print()" class="btn">Print Report</a>
  </div>
  
  <h2>Recommendations</h2>
  <ul>
    <li>Focus on files with the lowest coverage first</li>
    <li>Add tests for uncovered branches and edge cases</li>
    <li>Ensure all exported functions have unit tests</li>
    <li>Consider refactoring complex functions into smaller, more testable units</li>
  </ul>
  
  <p>Report generated on 4/7/2025, 6:17:07 PM</p>
</body>
</html>
  