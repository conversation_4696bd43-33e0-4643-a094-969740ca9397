/**
 * Monitoring Dashboard
 *
 * This module provides a real-time monitoring dashboard for the Finite Universe
 * Principle defense system. It collects metrics from various components and
 * provides visualization and alerting capabilities.
 * 
 * Key features:
 * 1. Real-time monitoring of boundary violations and corrections
 * 2. Visualization of metrics across domains
 * 3. Alerting for critical events
 * 4. Historical data tracking
 */

const EventEmitter = require('events');

/**
 * MonitoringDashboard class
 * 
 * Provides real-time monitoring for the Finite Universe Principle defense system.
 */
class MonitoringDashboard extends EventEmitter {
  /**
   * Constructor
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();

    this.options = {
      enableLogging: true,
      monitoringInterval: 5000, // Monitoring interval in milliseconds
      historyLength: 100, // Number of historical data points to keep
      alertThresholds: {
        boundaryViolations: 10, // Alert if more than 10 violations per interval
        validationFailures: 5, // Alert if more than 5 validation failures per interval
        criticalAlerts: 1 // Alert if any critical alerts are triggered
      },
      ...options
    };

    // Initialize components to monitor
    this.components = {
      boundaryEnforcer: null,
      domainIntegration: null,
      crossDomainValidator: null,
      domainAdapters: {
        cyber: null,
        financial: null,
        medical: null
      }
    };

    // Initialize metrics
    this.metrics = {
      current: this._createEmptyMetrics(),
      history: []
    };

    // Initialize alerts
    this.alerts = [];

    // Initialize monitoring interval
    this.monitoringInterval = null;

    if (this.options.enableLogging) {
      console.log('MonitoringDashboard initialized with options:', this.options);
    }
  }

  /**
   * Register a boundary enforcer to monitor
   * @param {Object} boundaryEnforcer - Boundary enforcer to monitor
   */
  registerBoundaryEnforcer(boundaryEnforcer) {
    this.components.boundaryEnforcer = boundaryEnforcer;
    
    // Register event listeners
    boundaryEnforcer.on('boundary-violation', (data) => {
      this._handleBoundaryViolation(data);
    });
    
    boundaryEnforcer.on('boundary-correction', (data) => {
      this._handleBoundaryCorrection(data);
    });
    
    boundaryEnforcer.on('violation-threshold-exceeded', (data) => {
      this._handleViolationThresholdExceeded(data);
    });
    
    if (this.options.enableLogging) {
      console.log('Boundary enforcer registered for monitoring');
    }
  }

  /**
   * Register a domain integration to monitor
   * @param {Object} domainIntegration - Domain integration to monitor
   */
  registerDomainIntegration(domainIntegration) {
    this.components.domainIntegration = domainIntegration;
    
    // Register event listeners
    domainIntegration.on('processing-error', (data) => {
      this._handleProcessingError(data);
    });
    
    domainIntegration.on('adapter-registered', (data) => {
      this._handleAdapterRegistered(data);
    });
    
    if (this.options.enableLogging) {
      console.log('Domain integration registered for monitoring');
    }
  }

  /**
   * Register a cross-domain validator to monitor
   * @param {Object} crossDomainValidator - Cross-domain validator to monitor
   */
  registerCrossDomainValidator(crossDomainValidator) {
    this.components.crossDomainValidator = crossDomainValidator;
    
    // Register event listeners
    crossDomainValidator.on('validation-failure', (data) => {
      this._handleValidationFailure(data);
    });
    
    crossDomainValidator.on('contamination-prevented', (data) => {
      this._handleContaminationPrevented(data);
    });
    
    if (this.options.enableLogging) {
      console.log('Cross-domain validator registered for monitoring');
    }
  }

  /**
   * Register a domain adapter to monitor
   * @param {string} domain - Domain of the adapter
   * @param {Object} adapter - Domain adapter to monitor
   */
  registerDomainAdapter(domain, adapter) {
    this.components.domainAdapters[domain] = adapter;
    
    // Register event listeners
    adapter.on('processing-error', (data) => {
      this._handleAdapterProcessingError(domain, data);
    });
    
    if (domain === 'medical') {
      adapter.on('critical-alert', (data) => {
        this._handleCriticalAlert(data);
      });
    }
    
    if (this.options.enableLogging) {
      console.log(`${domain} domain adapter registered for monitoring`);
    }
  }

  /**
   * Start monitoring
   */
  startMonitoring() {
    if (this.monitoringInterval) {
      return;
    }
    
    this.monitoringInterval = setInterval(() => {
      this._collectMetrics();
      this._checkAlertThresholds();
    }, this.options.monitoringInterval);
    
    if (this.options.enableLogging) {
      console.log('Monitoring started');
    }
    
    this.emit('monitoring-started');
  }

  /**
   * Stop monitoring
   */
  stopMonitoring() {
    if (!this.monitoringInterval) {
      return;
    }
    
    clearInterval(this.monitoringInterval);
    this.monitoringInterval = null;
    
    if (this.options.enableLogging) {
      console.log('Monitoring stopped');
    }
    
    this.emit('monitoring-stopped');
  }

  /**
   * Collect metrics from all registered components
   * @private
   */
  _collectMetrics() {
    const metrics = this._createEmptyMetrics();
    
    // Collect metrics from boundary enforcer
    if (this.components.boundaryEnforcer) {
      const enforcerMetrics = this.components.boundaryEnforcer.getViolationStats();
      metrics.boundaryViolations = enforcerMetrics.total;
      metrics.infiniteValues = enforcerMetrics.infiniteValue;
      metrics.nanValues = enforcerMetrics.nanValue;
      metrics.exceedsBounds = enforcerMetrics.exceedsBounds;
      metrics.circularReferences = enforcerMetrics.circularReference;
      metrics.excessiveComplexity = enforcerMetrics.excessiveComplexity;
      
      const correctionMetrics = this.components.boundaryEnforcer.getCorrectionStats();
      metrics.boundaryCorrections = correctionMetrics.total;
    }
    
    // Collect metrics from domain integration
    if (this.components.domainIntegration) {
      const integrationMetrics = this.components.domainIntegration.getMonitoringStats();
      metrics.processingErrors = integrationMetrics.processingErrors || 0;
    }
    
    // Collect metrics from cross-domain validator
    if (this.components.crossDomainValidator) {
      const validatorMetrics = this.components.crossDomainValidator.getMetrics();
      metrics.validationFailures = validatorMetrics.validationFailures;
      metrics.contaminationPrevented = validatorMetrics.contaminationPrevented;
      metrics.domainDetections = validatorMetrics.domainDetections;
    }
    
    // Collect metrics from domain adapters
    for (const domain in this.components.domainAdapters) {
      const adapter = this.components.domainAdapters[domain];
      if (adapter) {
        const adapterMetrics = adapter.getMetrics();
        metrics.domainMetrics[domain] = {
          processedDataCount: adapterMetrics.processedDataCount,
          boundaryViolations: adapterMetrics.boundaryViolations
        };
        
        if (domain === 'cyber') {
          metrics.domainMetrics[domain].averageSecurityScore = adapterMetrics.averageSecurityScore;
          metrics.domainMetrics[domain].averageThreatLevel = adapterMetrics.averageThreatLevel;
        } else if (domain === 'financial') {
          metrics.domainMetrics[domain].totalTransactionVolume = adapterMetrics.totalTransactionVolume;
          metrics.domainMetrics[domain].averageInterestRate = adapterMetrics.averageInterestRate;
        } else if (domain === 'medical') {
          metrics.domainMetrics[domain].averageHeartRate = adapterMetrics.averageHeartRate;
          metrics.domainMetrics[domain].criticalAlerts = adapterMetrics.criticalAlerts;
        }
      }
    }
    
    // Update current metrics
    this.metrics.current = metrics;
    
    // Add to history
    this.metrics.history.push({
      timestamp: new Date(),
      metrics: { ...metrics }
    });
    
    // Trim history if needed
    if (this.metrics.history.length > this.options.historyLength) {
      this.metrics.history.shift();
    }
    
    this.emit('metrics-collected', metrics);
  }

  /**
   * Check alert thresholds and trigger alerts if needed
   * @private
   */
  _checkAlertThresholds() {
    const metrics = this.metrics.current;
    
    // Check boundary violations
    if (metrics.boundaryViolations > this.options.alertThresholds.boundaryViolations) {
      this._triggerAlert('boundary-violations', {
        message: `Boundary violations threshold exceeded: ${metrics.boundaryViolations}`,
        level: 'warning',
        metrics: {
          boundaryViolations: metrics.boundaryViolations,
          threshold: this.options.alertThresholds.boundaryViolations
        }
      });
    }
    
    // Check validation failures
    if (metrics.validationFailures > this.options.alertThresholds.validationFailures) {
      this._triggerAlert('validation-failures', {
        message: `Validation failures threshold exceeded: ${metrics.validationFailures}`,
        level: 'warning',
        metrics: {
          validationFailures: metrics.validationFailures,
          threshold: this.options.alertThresholds.validationFailures
        }
      });
    }
    
    // Check critical alerts
    if (metrics.domainMetrics.medical && 
        metrics.domainMetrics.medical.criticalAlerts > this.options.alertThresholds.criticalAlerts) {
      this._triggerAlert('critical-alerts', {
        message: `Critical alerts threshold exceeded: ${metrics.domainMetrics.medical.criticalAlerts}`,
        level: 'critical',
        metrics: {
          criticalAlerts: metrics.domainMetrics.medical.criticalAlerts,
          threshold: this.options.alertThresholds.criticalAlerts
        }
      });
    }
  }

  /**
   * Trigger an alert
   * @param {string} type - Alert type
   * @param {Object} data - Alert data
   * @private
   */
  _triggerAlert(type, data) {
    const alert = {
      type,
      timestamp: new Date(),
      ...data
    };
    
    this.alerts.push(alert);
    
    this.emit('alert', alert);
    
    if (this.options.enableLogging) {
      console.log(`Alert triggered: ${data.message}`);
    }
  }

  /**
   * Handle boundary violation event
   * @param {Object} data - Event data
   * @private
   */
  _handleBoundaryViolation(data) {
    // Update metrics in real-time
    this.metrics.current.boundaryViolations++;
    this.metrics.current[data.type]++;
    
    this.emit('boundary-violation', data);
  }

  /**
   * Handle boundary correction event
   * @param {Object} data - Event data
   * @private
   */
  _handleBoundaryCorrection(data) {
    // Update metrics in real-time
    this.metrics.current.boundaryCorrections++;
    
    this.emit('boundary-correction', data);
  }

  /**
   * Handle violation threshold exceeded event
   * @param {Object} data - Event data
   * @private
   */
  _handleViolationThresholdExceeded(data) {
    this._triggerAlert('violation-threshold', {
      message: `Violation threshold exceeded: ${data.violationsPerSecond} per second`,
      level: 'warning',
      metrics: data
    });
    
    this.emit('violation-threshold-exceeded', data);
  }

  /**
   * Handle processing error event
   * @param {Object} data - Event data
   * @private
   */
  _handleProcessingError(data) {
    // Update metrics in real-time
    this.metrics.current.processingErrors++;
    
    this.emit('processing-error', data);
  }

  /**
   * Handle adapter registered event
   * @param {Object} data - Event data
   * @private
   */
  _handleAdapterRegistered(data) {
    this.emit('adapter-registered', data);
  }

  /**
   * Handle validation failure event
   * @param {Object} data - Event data
   * @private
   */
  _handleValidationFailure(data) {
    // Update metrics in real-time
    this.metrics.current.validationFailures++;
    
    this.emit('validation-failure', data);
  }

  /**
   * Handle contamination prevented event
   * @param {Object} data - Event data
   * @private
   */
  _handleContaminationPrevented(data) {
    // Update metrics in real-time
    this.metrics.current.contaminationPrevented++;
    
    this.emit('contamination-prevented', data);
  }

  /**
   * Handle adapter processing error event
   * @param {string} domain - Domain of the adapter
   * @param {Object} data - Event data
   * @private
   */
  _handleAdapterProcessingError(domain, data) {
    // Update metrics in real-time
    this.metrics.current.processingErrors++;
    
    this.emit('adapter-processing-error', { domain, ...data });
  }

  /**
   * Handle critical alert event
   * @param {Object} data - Event data
   * @private
   */
  _handleCriticalAlert(data) {
    // Update metrics in real-time
    if (this.metrics.current.domainMetrics.medical) {
      this.metrics.current.domainMetrics.medical.criticalAlerts++;
    }
    
    this._triggerAlert('medical-critical', {
      message: 'Medical critical alert',
      level: 'critical',
      metrics: data
    });
    
    this.emit('critical-alert', data);
  }

  /**
   * Create empty metrics object
   * @returns {Object} - Empty metrics object
   * @private
   */
  _createEmptyMetrics() {
    return {
      boundaryViolations: 0,
      boundaryCorrections: 0,
      infiniteValues: 0,
      nanValues: 0,
      exceedsBounds: 0,
      circularReferences: 0,
      excessiveComplexity: 0,
      processingErrors: 0,
      validationFailures: 0,
      contaminationPrevented: 0,
      domainDetections: 0,
      domainMetrics: {
        cyber: {},
        financial: {},
        medical: {}
      }
    };
  }

  /**
   * Get current metrics
   * @returns {Object} - Current metrics
   */
  getCurrentMetrics() {
    return { ...this.metrics.current };
  }

  /**
   * Get metrics history
   * @returns {Array} - Metrics history
   */
  getMetricsHistory() {
    return [...this.metrics.history];
  }

  /**
   * Get alerts
   * @param {number} limit - Maximum number of alerts to return
   * @returns {Array} - Alerts
   */
  getAlerts(limit = 10) {
    return this.alerts.slice(-limit);
  }

  /**
   * Reset metrics and alerts
   */
  reset() {
    this.metrics.current = this._createEmptyMetrics();
    this.metrics.history = [];
    this.alerts = [];
    
    this.emit('dashboard-reset');
  }
}

/**
 * Create a monitoring dashboard with recommended settings
 * @param {Object} options - Configuration options
 * @returns {MonitoringDashboard} - Configured monitoring dashboard
 */
function createMonitoringDashboard(options = {}) {
  return new MonitoringDashboard({
    enableLogging: true,
    monitoringInterval: 5000,
    historyLength: 100,
    ...options
  });
}

module.exports = {
  MonitoringDashboard,
  createMonitoringDashboard
};
