{"displayName": "NovaConnect UAC Compliance Dashboard", "gridLayout": {"columns": 2, "widgets": [{"title": "Overall Compliance Score", "scorecard": {"timeSeriesQuery": {"timeSeriesFilter": {"filter": "metric.type=\"custom.googleapis.com/novafuse/compliance/score\" resource.type=\"k8s_container\" resource.label.\"container_name\"=\"novafuse-uac\"", "aggregation": {"perSeriesAligner": "ALIGN_MEAN", "crossSeriesReducer": "REDUCE_MEAN"}}, "unitOverride": "1"}, "thresholds": [{"value": 60, "color": "RED"}, {"value": 80, "color": "YELLOW"}, {"value": 90, "color": "GREEN"}], "sparkChartView": {"sparkChartType": "SPARK_LINE"}}}, {"title": "Compliance Score Trend", "xyChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "metric.type=\"custom.googleapis.com/novafuse/compliance/score\" resource.type=\"k8s_container\" resource.label.\"container_name\"=\"novafuse-uac\"", "aggregation": {"perSeriesAligner": "ALIGN_MEAN", "crossSeriesReducer": "REDUCE_MEAN"}}, "unitOverride": "1"}, "plotType": "LINE", "minAlignmentPeriod": "60s"}], "yAxis": {"label": "Compliance Score", "scale": "LINEAR"}, "chartOptions": {"mode": "COLOR"}}}, {"title": "Framework Compliance Scores", "barChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "metric.type=\"custom.googleapis.com/novafuse/compliance/framework/score\" resource.type=\"k8s_container\" resource.label.\"container_name\"=\"novafuse-uac\"", "aggregation": {"perSeriesAligner": "ALIGN_MEAN", "crossSeriesReducer": "REDUCE_MEAN", "groupByFields": ["metric.label.\"framework\""]}}, "unitOverride": "1"}, "plotType": "STACKED_BAR", "minAlignmentPeriod": "60s"}], "yAxis": {"label": "Compliance Score", "scale": "LINEAR"}}}, {"title": "Control Compliance Status", "pieChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "metric.type=\"custom.googleapis.com/novafuse/compliance/control/status\" resource.type=\"k8s_container\" resource.label.\"container_name\"=\"novafuse-uac\"", "aggregation": {"perSeriesAligner": "ALIGN_MEAN", "crossSeriesReducer": "REDUCE_COUNT", "groupByFields": ["metric.label.\"status\""]}}, "unitOverride": "1"}}]}}, {"title": "Compliance Failures by Framework", "barChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "metric.type=\"custom.googleapis.com/novafuse/compliance/control/failures\" resource.type=\"k8s_container\" resource.label.\"container_name\"=\"novafuse-uac\"", "aggregation": {"perSeriesAligner": "ALIGN_MEAN", "crossSeriesReducer": "REDUCE_SUM", "groupByFields": ["metric.label.\"framework\""]}}, "unitOverride": "1"}, "plotType": "STACKED_BAR", "minAlignmentPeriod": "60s"}], "yAxis": {"label": "Failure Count", "scale": "LINEAR"}}}, {"title": "Compliance Audit Events", "xyChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "metric.type=\"custom.googleapis.com/novafuse/compliance/audit/events\" resource.type=\"k8s_container\" resource.label.\"container_name\"=\"novafuse-uac\"", "aggregation": {"perSeriesAligner": "ALIGN_RATE", "crossSeriesReducer": "REDUCE_SUM", "groupByFields": ["metric.label.\"type\""]}}, "unitOverride": "1"}, "plotType": "LINE", "minAlignmentPeriod": "60s"}], "yAxis": {"label": "Events/minute", "scale": "LINEAR"}, "chartOptions": {"mode": "COLOR"}}}, {"title": "Compliance Scan Duration", "xyChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "metric.type=\"custom.googleapis.com/novafuse/compliance/scan/duration\" resource.type=\"k8s_container\" resource.label.\"container_name\"=\"novafuse-uac\"", "aggregation": {"perSeriesAligner": "ALIGN_MEAN", "crossSeriesReducer": "REDUCE_MEAN", "groupByFields": ["metric.label.\"framework\""]}}, "unitOverride": "s"}, "plotType": "LINE", "minAlignmentPeriod": "60s"}], "yAxis": {"label": "Duration (seconds)", "scale": "LINEAR"}, "chartOptions": {"mode": "COLOR"}}}, {"title": "Compliance Remediation Actions", "xyChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "metric.type=\"custom.googleapis.com/novafuse/compliance/remediation/actions\" resource.type=\"k8s_container\" resource.label.\"container_name\"=\"novafuse-uac\"", "aggregation": {"perSeriesAligner": "ALIGN_RATE", "crossSeriesReducer": "REDUCE_SUM", "groupByFields": ["metric.label.\"status\""]}}, "unitOverride": "1"}, "plotType": "STACKED_AREA", "minAlignmentPeriod": "60s"}], "yAxis": {"label": "Actions/minute", "scale": "LINEAR"}, "chartOptions": {"mode": "COLOR"}}}, {"title": "Compliance Evidence Collection", "xyChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "metric.type=\"custom.googleapis.com/novafuse/compliance/evidence/count\" resource.type=\"k8s_container\" resource.label.\"container_name\"=\"novafuse-uac\"", "aggregation": {"perSeriesAligner": "ALIGN_RATE", "crossSeriesReducer": "REDUCE_SUM", "groupByFields": ["metric.label.\"type\""]}}, "unitOverride": "1"}, "plotType": "LINE", "minAlignmentPeriod": "60s"}], "yAxis": {"label": "Evidence Items/minute", "scale": "LINEAR"}, "chartOptions": {"mode": "COLOR"}}}, {"title": "Compliance Report Generation", "xyChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "metric.type=\"custom.googleapis.com/novafuse/compliance/report/generation\" resource.type=\"k8s_container\" resource.label.\"container_name\"=\"novafuse-uac\"", "aggregation": {"perSeriesAligner": "ALIGN_RATE", "crossSeriesReducer": "REDUCE_SUM", "groupByFields": ["metric.label.\"format\""]}}, "unitOverride": "1"}, "plotType": "LINE", "minAlignmentPeriod": "60s"}], "yAxis": {"label": "Reports/minute", "scale": "LINEAR"}, "chartOptions": {"mode": "COLOR"}}}, {"title": "Compliance API Calls", "xyChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "metric.type=\"custom.googleapis.com/novafuse/compliance/api/calls\" resource.type=\"k8s_container\" resource.label.\"container_name\"=\"novafuse-uac\"", "aggregation": {"perSeriesAligner": "ALIGN_RATE", "crossSeriesReducer": "REDUCE_SUM", "groupByFields": ["metric.label.\"endpoint\""]}}, "unitOverride": "1"}, "plotType": "LINE", "minAlignmentPeriod": "60s"}], "yAxis": {"label": "Calls/minute", "scale": "LINEAR"}, "chartOptions": {"mode": "COLOR"}}}, {"title": "Compliance API Response Time", "xyChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "metric.type=\"custom.googleapis.com/novafuse/compliance/api/response_time\" resource.type=\"k8s_container\" resource.label.\"container_name\"=\"novafuse-uac\"", "aggregation": {"perSeriesAligner": "ALIGN_PERCENTILE_95", "crossSeriesReducer": "REDUCE_MEAN", "groupByFields": ["metric.label.\"endpoint\""]}}, "unitOverride": "ms"}, "plotType": "LINE", "minAlignmentPeriod": "60s"}], "yAxis": {"label": "Response Time (ms)", "scale": "LINEAR"}, "chartOptions": {"mode": "COLOR"}}}]}}