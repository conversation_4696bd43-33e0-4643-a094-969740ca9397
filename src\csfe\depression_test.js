/**
 * Depression Prediction Engine Test
 * 
 * This script tests the Depression Prediction engine with sample data.
 */

const DepressionPredictionEngine = require('./depression/depression_prediction_engine');
const HistoricalPatterns = require('./depression/historical_patterns');
const TimelineProjector = require('./depression/timeline_projector');
const MitigationStrategies = require('./depression/mitigation_strategies');

// Create Depression Prediction Engine instance
const depressionEngine = new DepressionPredictionEngine();

// Sample market data
const marketData = {
  price: {
    current: 100,
    moving_average: 95,
    history: [90, 92, 95, 98, 100]
  },
  volume: {
    current: 1000000,
    average: 900000,
    trend: 'increasing'
  },
  liquidity: {
    value: 0.8,
    trend: 'stable'
  },
  volatility: {
    value: 15,
    trend: 'decreasing'
  },
  depth: {
    value: 0.7,
    trend: 'increasing'
  },
  spread: {
    value: 0.5,
    trend: 'decreasing'
  }
};

// Sample economic data
const economicData = {
  gdp: {
    value: 21000,
    growth: 2.5,
    trend: 'increasing'
  },
  inflation: {
    rate: 2.1,
    core: 1.8,
    trend: 'stable'
  },
  unemployment: {
    rate: 3.8,
    trend: 'decreasing'
  },
  interestRates: {
    fed_funds: 0.25,
    ten_year: 1.5,
    trend: 'stable'
  },
  pmi: {
    value: 53.5,
    trend: 'increasing'
  },
  consumerConfidence: {
    value: 110,
    trend: 'increasing'
  },
  buildingPermits: {
    value: 1800000,
    growth: 3.2,
    trend: 'increasing'
  }
};

// Sample sentiment data
const sentimentData = {
  retail: {
    bullishPercentage: 65,
    bearishPercentage: 35,
    trend: 'increasing'
  },
  institutional: {
    bullishPercentage: 55,
    bearishPercentage: 45,
    netPositioning: 10,
    trend: 'stable'
  },
  media: {
    sentiment: 0.6,
    volume: 1000,
    trend: 'increasing'
  },
  social: {
    sentiment: 0.7,
    volume: 5000,
    trend: 'increasing'
  },
  futures: {
    commercialNetPositioning: 15,
    nonCommercialNetPositioning: -5,
    trend: 'increasing'
  }
};

// Calculate depression probability
console.log('Calculating depression probability...');
const result = depressionEngine.calculateDepressionProbability(marketData, economicData, sentimentData);

// Display result
console.log('\nDepression Prediction Result:');
console.log(`CSFE Value: ${result.csfeValue}`);
console.log(`Depression Probability: ${result.depressionProbability}`);
console.log(`Confidence: ${result.confidence}`);

// Display timeline probability
console.log('\nTimeline Probability:');
Object.entries(result.timelineProbability).forEach(([year, probability]) => {
  console.log(`  ${year}: ${probability.toFixed(4)}`);
});

// Display severity distribution
console.log('\nSeverity Distribution:');
Object.entries(result.severityDistribution).forEach(([severity, probability]) => {
  console.log(`  ${severity}: ${probability.toFixed(4)}`);
});

// Display key indicators
console.log('\nKey Indicators:');
result.keyIndicators.forEach(indicator => {
  console.log(`  ${indicator.name}: ${indicator.value} (${indicator.impact} impact)`);
});

// Display historical comparison
if (result.historicalComparison) {
  console.log('\nHistorical Comparison:');
  console.log(`  Most Similar Pattern: ${result.historicalComparison.mostSimilarPattern.name}`);
  console.log(`  Similarity: ${result.historicalComparison.mostSimilarPattern.overallSimilarity.toFixed(4)}`);
  
  console.log('\n  Key Similarities:');
  result.historicalComparison.mostSimilarPattern.keySimilarities.forEach(similarity => {
    console.log(`    ${similarity.factor}: Current=${similarity.current}, Historical=${similarity.historical}`);
  });
  
  console.log('\n  Key Differences:');
  result.historicalComparison.mostSimilarPattern.keyDifferences.forEach(difference => {
    console.log(`    ${difference.factor}: Current=${difference.current}, Historical=${difference.historical}`);
  });
}

// Display timeline projection
if (result.timelineProjection) {
  console.log('\nTimeline Projection:');
  console.log(`  Onset Year: ${result.timelineProjection.onsetYear}`);
  console.log(`  Peak Year: ${result.timelineProjection.peakYear}`);
  console.log(`  Recovery Year: ${result.timelineProjection.recoveryYear}`);
  
  console.log('\n  Phases:');
  Object.entries(result.timelineProjection.phases).forEach(([phase, data]) => {
    console.log(`    ${data.name}: ${data.startYear.toFixed(1)} to ${data.endYear.toFixed(1)} (${data.duration.toFixed(1)} years)`);
    console.log(`      ${data.description}`);
  });
  
  console.log('\n  Market Projection:');
  Object.entries(result.timelineProjection.marketProjection).forEach(([year, data]) => {
    console.log(`    ${year}: ${data.overall.direction} (${data.overall.decline.toFixed(1)}% decline)`);
  });
}

// Display mitigation strategies
if (result.mitigationStrategies) {
  console.log('\nMitigation Strategies:');
  console.log(`  Intensity: ${result.mitigationStrategies.intensity}`);
  
  console.log('\n  Government Strategies:');
  result.mitigationStrategies.government.slice(0, 3).forEach(strategy => {
    console.log(`    ${strategy.name} (${strategy.priority} priority): ${strategy.description}`);
  });
  
  console.log('\n  Institutional Strategies:');
  result.mitigationStrategies.institutional.slice(0, 3).forEach(strategy => {
    console.log(`    ${strategy.name} (${strategy.priority} priority): ${strategy.description}`);
  });
  
  console.log('\n  Individual Strategies:');
  result.mitigationStrategies.individual.slice(0, 3).forEach(strategy => {
    console.log(`    ${strategy.name} (${strategy.priority} priority): ${strategy.description}`);
  });
  
  console.log('\n  Implementation Timeline:');
  result.mitigationStrategies.implementationTimeline.phases.forEach(phase => {
    console.log(`    ${phase.name}: ${phase.startYear} to ${phase.endYear}`);
    console.log(`      ${phase.description}`);
  });
}

console.log('\nTest completed successfully.');
