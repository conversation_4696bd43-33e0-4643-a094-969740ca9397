"""
∂Ψ Hashing Module - Quantum Coherence Fingerprinting

Each memory gets a quantum coherence fingerprint that remains stable
across sessions while maintaining ∂Ψ<0.01 preservation.
"""

import hashlib
import math
import json
import time
from typing import Dict, Any, Optional, Tuple

class PsiHasher:
    """
    Quantum coherence fingerprinting system for NovaMemX™
    
    Generates stable ∂Ψ hashes that maintain quantum coherence
    across infinite time horizons.
    """
    
    def __init__(self):
        """Initialize the ∂Ψ hashing system"""
        self.name = "∂Ψ Hasher"
        self.version = "1.0.0-QUANTUM_COHERENCE"
        
        # Sacred geometry constants for hash stability
        self.phi = 1.618033988749      # Golden ratio
        self.pi = math.pi              # π
        self.e = math.e                # <PERSON><PERSON><PERSON>'s number
        
        # Quantum coherence parameters
        self.psi_stability_factor = 0.01    # ∂Ψ<0.01 enforcement
        self.coherence_salt = "NOVAMEMX_PSI_COHERENCE_2024"
        
        # Hash algorithm selection
        self.primary_algorithm = hashlib.sha256
        self.secondary_algorithm = hashlib.sha512
        
    def generate_psi_hash(self, content: str, context: Dict[str, Any] = None, 
                         consciousness_state: Dict[str, Any] = None) -> str:
        """
        Generate ∂Ψ quantum coherence fingerprint
        
        Args:
            content: The content to hash
            context: Additional context for coherence
            consciousness_state: Current consciousness state
            
        Returns:
            str: Stable ∂Ψ hash maintaining quantum coherence
        """
        # Prepare hash input with sacred geometry enhancement
        hash_input = self._prepare_hash_input(content, context, consciousness_state)
        
        # Generate primary hash
        primary_hash = self.primary_algorithm(hash_input.encode()).hexdigest()
        
        # Apply quantum coherence stabilization
        stabilized_hash = self._apply_coherence_stabilization(primary_hash, content)
        
        # Validate ∂Ψ stability
        if not self._validate_psi_stability(stabilized_hash, content):
            # Fallback to secondary algorithm if stability violated
            secondary_input = hash_input + f"_FALLBACK_{time.time()}"
            stabilized_hash = self.secondary_algorithm(secondary_input.encode()).hexdigest()
        
        return stabilized_hash
    
    def verify_psi_hash(self, content: str, psi_hash: str, 
                       context: Dict[str, Any] = None) -> bool:
        """
        Verify that a ∂Ψ hash maintains quantum coherence
        
        Args:
            content: Original content
            psi_hash: Hash to verify
            context: Original context
            
        Returns:
            bool: True if hash maintains ∂Ψ<0.01 stability
        """
        # Regenerate hash
        regenerated_hash = self.generate_psi_hash(content, context)
        
        # Check exact match (quantum coherence requires perfect stability)
        exact_match = regenerated_hash == psi_hash
        
        # Check ∂Ψ stability
        stability_check = self._validate_psi_stability(psi_hash, content)
        
        return exact_match and stability_check
    
    def calculate_hash_coherence(self, hash1: str, hash2: str) -> float:
        """
        Calculate coherence similarity between two ∂Ψ hashes
        
        Args:
            hash1: First hash
            hash2: Second hash
            
        Returns:
            float: Coherence similarity (0.0 to 1.0)
        """
        # Character-level similarity
        char_similarity = sum(c1 == c2 for c1, c2 in zip(hash1, hash2)) / max(len(hash1), len(hash2))
        
        # Quantum coherence pattern matching
        pattern_similarity = self._calculate_pattern_coherence(hash1, hash2)
        
        # Sacred geometry resonance
        geometry_resonance = self._calculate_geometry_resonance(hash1, hash2)
        
        # Combined coherence score
        coherence = (char_similarity + pattern_similarity + geometry_resonance) / 3
        
        return min(coherence, 1.0)
    
    def _prepare_hash_input(self, content: str, context: Dict[str, Any] = None,
                           consciousness_state: Dict[str, Any] = None) -> str:
        """Prepare input for ∂Ψ hashing with sacred geometry enhancement"""
        
        # Start with content
        hash_input = content
        
        # Add context if provided
        if context:
            context_str = json.dumps(context, sort_keys=True)
            hash_input += f"|CONTEXT:{context_str}"
        
        # Add consciousness state if provided
        if consciousness_state:
            consciousness_str = json.dumps(consciousness_state, sort_keys=True)
            hash_input += f"|CONSCIOUSNESS:{consciousness_str}"
        
        # Add sacred geometry constants for stability
        hash_input += f"|PHI:{self.phi:.15f}"
        hash_input += f"|PI:{self.pi:.15f}"
        hash_input += f"|E:{self.e:.15f}"
        
        # Add coherence salt
        hash_input += f"|SALT:{self.coherence_salt}"
        
        # Add ∂Ψ stability factor
        hash_input += f"|PSI_STABILITY:{self.psi_stability_factor}"
        
        return hash_input
    
    def _apply_coherence_stabilization(self, primary_hash: str, content: str) -> str:
        """Apply quantum coherence stabilization to hash"""
        
        # Calculate content-based stabilization factor
        content_factor = sum(ord(c) for c in content) % 1000
        
        # Apply golden ratio transformation
        phi_transform = int(content_factor * self.phi) % len(primary_hash)
        
        # Rotate hash based on sacred geometry
        stabilized = primary_hash[phi_transform:] + primary_hash[:phi_transform]
        
        # Apply π-based character substitution for additional stability
        pi_factor = int(self.pi * 1000) % 256
        stabilized_chars = []
        
        for i, char in enumerate(stabilized):
            if i % 7 == 0:  # Every 7th character (sacred number)
                # Apply π transformation
                char_code = ord(char) if char.isalpha() else ord(char) + pi_factor
                new_char = hex(char_code % 16)[-1]
                stabilized_chars.append(new_char)
            else:
                stabilized_chars.append(char)
        
        return ''.join(stabilized_chars)
    
    def _validate_psi_stability(self, psi_hash: str, content: str) -> bool:
        """Validate that hash maintains ∂Ψ<0.01 stability"""
        
        # Calculate hash entropy
        char_counts = {}
        for char in psi_hash:
            char_counts[char] = char_counts.get(char, 0) + 1
        
        # Calculate entropy
        total_chars = len(psi_hash)
        entropy = -sum((count/total_chars) * math.log2(count/total_chars) 
                      for count in char_counts.values())
        
        # Normalize entropy (max entropy for hex is log2(16) = 4)
        normalized_entropy = entropy / 4.0
        
        # Calculate ∂Ψ derivative approximation
        psi_derivative = abs(1.0 - normalized_entropy)
        
        # Check stability threshold
        return psi_derivative < self.psi_stability_factor
    
    def _calculate_pattern_coherence(self, hash1: str, hash2: str) -> float:
        """Calculate quantum coherence pattern matching"""
        
        # Look for repeating patterns
        pattern_score = 0.0
        
        # Check for common subsequences
        for length in range(2, 8):  # Pattern lengths 2-7
            patterns1 = set(hash1[i:i+length] for i in range(len(hash1)-length+1))
            patterns2 = set(hash2[i:i+length] for i in range(len(hash2)-length+1))
            
            common_patterns = patterns1.intersection(patterns2)
            pattern_score += len(common_patterns) / max(len(patterns1), len(patterns2))
        
        return pattern_score / 6  # Average over pattern lengths
    
    def _calculate_geometry_resonance(self, hash1: str, hash2: str) -> float:
        """Calculate sacred geometry resonance between hashes"""
        
        # Convert hashes to numeric values for geometry calculation
        num1 = int(hash1[:16], 16) if len(hash1) >= 16 else int(hash1, 16)
        num2 = int(hash2[:16], 16) if len(hash2) >= 16 else int(hash2, 16)
        
        # Calculate golden ratio resonance
        ratio = max(num1, num2) / max(min(num1, num2), 1)
        phi_resonance = abs(ratio - self.phi) / self.phi
        phi_score = max(0, 1.0 - phi_resonance)
        
        # Calculate π resonance
        pi_factor = (num1 + num2) % int(self.pi * 1000)
        pi_resonance = math.sin(pi_factor / 1000 * self.pi)
        pi_score = (pi_resonance + 1) / 2  # Normalize to 0-1
        
        # Calculate e resonance
        e_factor = abs(num1 - num2) % int(self.e * 1000)
        e_resonance = math.exp(-e_factor / 1000)
        e_score = e_resonance
        
        # Combined geometry resonance
        geometry_resonance = (phi_score + pi_score + e_score) / 3
        
        return geometry_resonance
    
    def get_hash_info(self, psi_hash: str) -> Dict[str, Any]:
        """Get detailed information about a ∂Ψ hash"""
        
        # Calculate hash properties
        entropy = self._calculate_hash_entropy(psi_hash)
        stability = self._validate_psi_stability(psi_hash, "")
        
        # Extract sacred geometry properties
        geometry_props = self._extract_geometry_properties(psi_hash)
        
        return {
            "hash": psi_hash,
            "length": len(psi_hash),
            "entropy": entropy,
            "psi_stable": stability,
            "algorithm": self.primary_algorithm.__name__,
            "sacred_geometry": geometry_props,
            "coherence_validated": stability and entropy > 0.8
        }
    
    def _calculate_hash_entropy(self, psi_hash: str) -> float:
        """Calculate entropy of ∂Ψ hash"""
        char_counts = {}
        for char in psi_hash:
            char_counts[char] = char_counts.get(char, 0) + 1
        
        total_chars = len(psi_hash)
        entropy = -sum((count/total_chars) * math.log2(count/total_chars) 
                      for count in char_counts.values())
        
        return entropy / 4.0  # Normalize for hex
    
    def _extract_geometry_properties(self, psi_hash: str) -> Dict[str, Any]:
        """Extract sacred geometry properties from hash"""
        
        # Convert to numeric for analysis
        numeric_value = int(psi_hash[:16], 16) if len(psi_hash) >= 16 else int(psi_hash, 16)
        
        # Calculate geometry ratios
        phi_alignment = (numeric_value % 1618) / 1618
        pi_alignment = (numeric_value % 3142) / 3142
        e_alignment = (numeric_value % 2718) / 2718
        
        return {
            "phi_alignment": phi_alignment,
            "pi_alignment": pi_alignment,
            "e_alignment": e_alignment,
            "geometric_mean": (phi_alignment * pi_alignment * e_alignment) ** (1/3)
        }
