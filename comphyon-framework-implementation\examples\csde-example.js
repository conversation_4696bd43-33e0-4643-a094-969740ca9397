/**
 * CSDE Example
 * 
 * This example demonstrates the usage of the CSDE component.
 */

const {
  EntropicGRCControlSystem,
  HumanSystemCoherenceInterface,
  CrossDomainEntropyBridge,
  createCSDESystem,
  createEnhancedCSDESystem
} = require('../csde');

// Example 1: Using individual components
console.log('Example 1: Using individual components');

// Create components
const entropicGRCControlSystem = new EntropicGRCControlSystem();
const humanSystemCoherenceInterface = new HumanSystemCoherenceInterface();
const crossDomainEntropyBridge = new CrossDomainEntropyBridge();

// Start components
entropicGRCControlSystem.start();
humanSystemCoherenceInterface.start();
crossDomainEntropyBridge.start();

// Example policy data
const policyData = {
  policyCount: 100,
  policyChanges: [
    { id: 'change-1', policyId: 'policy-1', changeType: 'update' },
    { id: 'change-2', policyId: 'policy-2', changeType: 'add' },
    { id: 'change-3', policyId: 'policy-3', changeType: 'delete' }
  ],
  policyConflicts: [
    { id: 'conflict-1', policies: ['policy-1', 'policy-3'] },
    { id: 'conflict-2', policies: ['policy-2', 'policy-4'] }
  ],
  policyGaps: [
    { id: 'gap-1', description: 'Missing policy for new regulation' },
    { id: 'gap-2', description: 'Incomplete coverage of data protection' }
  ],
  policyImplementation: {
    implemented: 80,
    pending: 15,
    failed: 5
  }
};

// Calculate policy entropy
const policyEntropy = entropicGRCControlSystem.calculatePolicyEntropy(policyData);
console.log(`Policy Entropy (Ψ_gov): ${policyEntropy.toFixed(4)}`);

// Example audit data
const auditData = {
  findings: [
    { id: 'finding-1', severity: 'high', description: 'Unauthorized access' },
    { id: 'finding-2', severity: 'medium', description: 'Missing encryption' },
    { id: 'finding-3', severity: 'low', description: 'Incomplete documentation' }
  ],
  coverage: 0.85,
  remediation: {
    completed: 10,
    inProgress: 5,
    notStarted: 3
  }
};

// Calculate audit entropy
const auditEntropy = entropicGRCControlSystem.calculateAuditEntropy(auditData);
console.log(`Audit Entropy (Aᵋ): ${auditEntropy.toFixed(4)}`);

// Example human data
const humanData = {
  cognitiveLoad: 0.3,
  stressLevel: 0.2,
  fatigue: 0.3,
  attention: 0.8,
  expertise: 0.7
};

// Update human coherence index
const humanCoherenceIndex = humanSystemCoherenceInterface.updateHumanCoherenceIndex(humanData);
console.log(`Human Coherence Index (Ψₕ): ${humanCoherenceIndex.toFixed(4)}`);

// Update cyber entropy in bridge
crossDomainEntropyBridge.updateCyberEntropy(0.7);
console.log(`Cyber Entropy (Ψₜᵈ): ${crossDomainEntropyBridge.getState().cyberEntropy.toFixed(4)}`);

// Update financial entropy in bridge
crossDomainEntropyBridge.updateFinancialEntropy(0.6);
console.log(`Financial Entropy (Ψₜᶠ): ${crossDomainEntropyBridge.getState().financialEntropy.toFixed(4)}`);

// Update biological entropy in bridge
crossDomainEntropyBridge.updateBiologicalEntropy(0.5);
console.log(`Biological Entropy (Ψₜ): ${crossDomainEntropyBridge.getState().biologicalEntropy.toFixed(4)}`);

// Get unified risk score
const unifiedRiskScore = crossDomainEntropyBridge.getUnifiedRiskScore();
console.log(`Unified Risk Score: ${unifiedRiskScore.toFixed(4)}`);

// Stop components
entropicGRCControlSystem.stop();
humanSystemCoherenceInterface.stop();
crossDomainEntropyBridge.stop();

// Example 2: Using the basic CSDE system
console.log('\nExample 2: Using the basic CSDE system');

// Create CSDE system
const csdeSystem = createCSDESystem({
  entropicGRCControlSystemOptions: {
    enableLogging: true
  },
  humanSystemCoherenceInterfaceOptions: {
    enableLogging: true
  },
  crossDomainEntropyBridgeOptions: {
    enableLogging: true
  }
});

// Start components
csdeSystem.entropicGRCControlSystem.start();
csdeSystem.humanSystemCoherenceInterface.start();
csdeSystem.crossDomainEntropyBridge.start();

// Calculate policy entropy
const policyEntropy2 = csdeSystem.entropicGRCControlSystem.calculatePolicyEntropy(policyData);
console.log(`Policy Entropy (Ψ_gov): ${policyEntropy2.toFixed(4)}`);

// Update human coherence index
const humanCoherenceIndex2 = csdeSystem.humanSystemCoherenceInterface.updateHumanCoherenceIndex(humanData);
console.log(`Human Coherence Index (Ψₕ): ${humanCoherenceIndex2.toFixed(4)}`);

// Update cyber entropy in bridge
csdeSystem.crossDomainEntropyBridge.updateCyberEntropy(0.7);
console.log(`Cyber Entropy (Ψₜᵈ): ${csdeSystem.crossDomainEntropyBridge.getState().cyberEntropy.toFixed(4)}`);

// Get unified risk score
const unifiedRiskScore2 = csdeSystem.crossDomainEntropyBridge.getUnifiedRiskScore();
console.log(`Unified Risk Score: ${unifiedRiskScore2.toFixed(4)}`);

// Stop components
csdeSystem.entropicGRCControlSystem.stop();
csdeSystem.humanSystemCoherenceInterface.stop();
csdeSystem.crossDomainEntropyBridge.stop();

// Example 3: Using the enhanced CSDE system
console.log('\nExample 3: Using the enhanced CSDE system');

// Create enhanced CSDE system
const enhancedCSDESystem = createEnhancedCSDESystem({
  enableLogging: true
});

// Start system
enhancedCSDESystem.start();

// Calculate cyber entropy
const cyberEntropy = enhancedCSDESystem.calculateCyberEntropy({
  policyData,
  auditData
});
console.log(`Cyber Entropy (Ψₜᵈ): ${cyberEntropy.toFixed(4)}`);

// Update human factors
const humanCoherenceIndex3 = enhancedCSDESystem.updateHumanFactors(humanData);
console.log(`Human Coherence Index (Ψₕ): ${humanCoherenceIndex3.toFixed(4)}`);

// Get unified risk assessment
const riskAssessment = enhancedCSDESystem.getUnifiedRiskAssessment();
console.log('Unified Risk Assessment:');
console.log(`- Unified Risk Score: ${riskAssessment.unifiedRiskScore.toFixed(4)}`);
console.log(`- Risk Status: ${riskAssessment.riskStatus}`);
console.log(`- Cyber Entropy: ${riskAssessment.cyberEntropy.toFixed(4)}`);
console.log(`- Financial Entropy: ${riskAssessment.financialEntropy.toFixed(4)}`);
console.log(`- Biological Entropy: ${riskAssessment.biologicalEntropy.toFixed(4)}`);
console.log(`- Policy Entropy: ${riskAssessment.policyEntropy.toFixed(4)}`);
console.log(`- Audit Entropy: ${riskAssessment.auditEntropy.toFixed(4)}`);
console.log(`- Regulatory Entropy: ${riskAssessment.regulatoryEntropy.toFixed(4)}`);
console.log(`- Human Coherence Index: ${riskAssessment.humanCoherenceIndex.toFixed(4)}`);

// Get unified state
const unifiedState = enhancedCSDESystem.getUnifiedState();
console.log('Unified State Summary:');
console.log(`- GRC Control Status: ${unifiedState.grc.controlStatus}`);
console.log(`- Human Coherence Index: ${unifiedState.human.humanCoherenceIndex.toFixed(4)}`);
console.log(`- Bridge Risk Status: ${unifiedState.bridge.riskStatus}`);

// Get unified metrics
const unifiedMetrics = enhancedCSDESystem.getUnifiedMetrics();
console.log('Unified Metrics Summary:');
console.log(`- GRC Total Updates: ${unifiedMetrics.grc.totalUpdates}`);
console.log(`- Human Total Updates: ${unifiedMetrics.human.totalUpdates}`);
console.log(`- Bridge Total Updates: ${unifiedMetrics.bridge.totalUpdates}`);

// Stop system
enhancedCSDESystem.stop();

console.log('\nCSDE example completed successfully!');
