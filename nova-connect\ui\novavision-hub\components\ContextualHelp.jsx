/**
 * ContextualHelp Component
 * 
 * A component for displaying contextual help.
 */

import React, { useState, useEffect, useRef } from 'react';
import PropTypes from 'prop-types';
import { useI18n } from '../i18n';
import { useAccessibility } from '../accessibility';
import { useHelp } from '../help/HelpContext';
import { AccessibleTooltip } from './';

/**
 * ContextualHelp component
 * 
 * @param {Object} props - Component props
 * @param {string} props.helpId - Help ID
 * @param {React.ReactNode} props.children - Trigger element
 * @param {string} [props.position='top'] - Tooltip position
 * @param {boolean} [props.showIcon=true] - Whether to show help icon
 * @param {boolean} [props.interactive=true] - Whether the tooltip is interactive
 * @param {boolean} [props.showLearnMore=true] - Whether to show "Learn More" link
 * @param {Function} [props.onLearnMore] - Function to call when "Learn More" is clicked
 * @param {string} [props.className=''] - Additional CSS class names
 * @param {Object} [props.style={}] - Additional inline styles
 * @returns {React.ReactElement} ContextualHelp component
 */
const ContextualHelp = ({
  helpId,
  children,
  position = 'top',
  showIcon = true,
  interactive = true,
  showLearnMore = true,
  onLearnMore,
  className = '',
  style = {}
}) => {
  // Hooks
  const { translate } = useI18n();
  const { settings } = useAccessibility();
  const {
    isContextualHelpEnabled,
    activeHelpId,
    toggleContextualHelp,
    openHelpPanel,
    getHelpContent
  } = useHelp();
  
  // State
  const [helpContent, setHelpContent] = useState(null);
  
  // Refs
  const triggerRef = useRef(null);
  
  // Get help content
  useEffect(() => {
    const content = getHelpContent(helpId);
    setHelpContent(content);
  }, [helpId, getHelpContent]);
  
  // If contextual help is disabled, just render children
  if (!isContextualHelpEnabled) {
    return children;
  }
  
  // If no help content, just render children
  if (!helpContent) {
    return children;
  }
  
  // Handle learn more click
  const handleLearnMore = (e) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (onLearnMore) {
      onLearnMore(helpId);
    } else {
      openHelpPanel(helpId);
    }
  };
  
  // Render help icon
  const renderHelpIcon = () => (
    <span
      className="contextual-help__icon"
      aria-hidden="true"
    >
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
        <circle cx="12" cy="12" r="10"></circle>
        <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
        <line x1="12" y1="17" x2="12.01" y2="17"></line>
      </svg>
    </span>
  );
  
  // Render tooltip content
  const renderTooltipContent = () => (
    <div className="contextual-help__content">
      <div className="contextual-help__title">
        {helpContent.title}
      </div>
      
      <div className="contextual-help__description">
        {helpContent.description}
      </div>
      
      {showLearnMore && (
        <div className="contextual-help__footer">
          <button
            className="contextual-help__learn-more"
            onClick={handleLearnMore}
          >
            {translate('help.learnMore', 'Learn More')}
          </button>
        </div>
      )}
    </div>
  );
  
  // If children is a function, call it with help props
  if (typeof children === 'function') {
    return children({
      helpId,
      helpContent,
      isActive: activeHelpId === helpId,
      toggleHelp: () => toggleContextualHelp(helpId),
      openHelpPanel: () => openHelpPanel(helpId)
    });
  }
  
  // If showIcon is false, wrap children with tooltip
  if (!showIcon) {
    return (
      <AccessibleTooltip
        content={renderTooltipContent()}
        position={position}
        interactive={interactive}
        className={`contextual-help__tooltip ${className}`}
        style={style}
      >
        {children}
      </AccessibleTooltip>
    );
  }
  
  // Otherwise, render children with help icon
  return (
    <span
      className={`contextual-help ${className}`}
      style={style}
      ref={triggerRef}
    >
      {children}
      <AccessibleTooltip
        content={renderTooltipContent()}
        position={position}
        interactive={interactive}
        className="contextual-help__tooltip"
      >
        {renderHelpIcon()}
      </AccessibleTooltip>
    </span>
  );
};

ContextualHelp.propTypes = {
  helpId: PropTypes.string.isRequired,
  children: PropTypes.oneOfType([PropTypes.node, PropTypes.func]).isRequired,
  position: PropTypes.oneOf(['top', 'right', 'bottom', 'left']),
  showIcon: PropTypes.bool,
  interactive: PropTypes.bool,
  showLearnMore: PropTypes.bool,
  onLearnMore: PropTypes.func,
  className: PropTypes.string,
  style: PropTypes.object
};

export default ContextualHelp;
