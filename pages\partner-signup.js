import React, { useEffect, useState } from 'react';
import <PERSON> from 'next/link';
import Head from 'next/head';

// Countdown Timer Component
const CountdownTimer = ({ targetDate }) => {
  const [timeLeft, setTimeLeft] = useState({});

  useEffect(() => {
    const interval = setInterval(() => {
      const now = new Date();
      const difference = new Date(targetDate) - now;

      const months = Math.floor(difference / (1000 * 60 * 60 * 24 * 30.44));
      const days = Math.floor((difference / (1000 * 60 * 60 * 24)) % 30.44);
      const hours = Math.floor((difference / (1000 * 60 * 60)) % 24);
      const minutes = Math.floor((difference / (1000 * 60)) % 60);
      const seconds = Math.floor((difference / 1000) % 60);

      setTimeLeft({ months, days, hours, minutes, seconds });
    }, 1000);

    return () => clearInterval(interval);
  }, [targetDate]);

  return (
    <div className="text-white text-center mt-6">
      <h2 className="text-xl font-semibold">Time Remaining</h2>
      <div className="text-3xl font-bold">
        {`${timeLeft.months}m ${timeLeft.days}d ${timeLeft.hours}h ${timeLeft.minutes}m ${timeLeft.seconds}s`}
      </div>
    </div>
  );
};

const PartnerSignup = () => {
  const [partnerType, setPartnerType] = useState('partner');
  const launchDate = new Date();
  const countdownTarget = new Date(launchDate.setMonth(launchDate.getMonth() + 18));

  return (
    <>
      <Head>
        <title>Become a Founding Partner | NovaFuse</title>
        <meta name="description" content="Join NovaFuse's exclusive Founding Partner Program and secure your 82% revenue share for 18 months." />
      </Head>

      <div className="min-h-screen bg-gradient-to-b from-blue-900 to-purple-900 text-white">
        {/* Header with Logo and Navigation */}
        <header className="bg-secondary py-4">
          <div className="container mx-auto px-4 flex justify-between items-center">
            <Link href="/" className="text-2xl font-bold">
              NovaFuse
            </Link>
            <Link href="/" className="text-white hover:text-blue-300">
              Back to Home
            </Link>
          </div>
        </header>

        {/* Partner Type Selector */}
        <div className="container mx-auto px-4 pt-8">
          <div className="bg-blue-900 bg-opacity-50 p-4 rounded-lg border border-blue-700 flex flex-col sm:flex-row justify-center items-center gap-4">
            <div className="text-xl font-bold">I want to join as a:</div>
            <div className="flex gap-4">
              <button
                className={`px-6 py-3 rounded-lg font-bold transition-all ${partnerType === 'partner' ? 'bg-blue-600 text-white' : 'bg-gray-800 text-gray-300 hover:bg-gray-700'}`}
                onClick={() => setPartnerType('partner')}
              >
                Founding Partner
                <div className="text-xs font-normal mt-1">Implementation & Consulting</div>
                <div className="text-xs font-normal">(93 slots remaining)</div>
              </button>
              <button
                className={`px-6 py-3 rounded-lg font-bold transition-all ${partnerType === 'developer' ? 'bg-blue-600 text-white' : 'bg-gray-800 text-gray-300 hover:bg-gray-700'}`}
                onClick={() => setPartnerType('developer')}
              >
                Daring Developer
                <div className="text-xs font-normal mt-1">App Store Applications</div>
                <div className="text-xs font-normal">(50 slots remaining)</div>
              </button>
            </div>
          </div>
        </div>

        <div className="container mx-auto px-4 py-8 flex flex-col lg:flex-row gap-12">
          {/* Left Column - Promo Content */}
          <div className="lg:w-1/2">
            {partnerType === 'partner' ? (
              <div className="bg-blue-900 bg-opacity-50 p-8 rounded-lg border border-blue-700">
                <h1 className="text-4xl font-extrabold mb-4">Founding Partner Opportunity</h1>
                <h2 className="text-2xl font-semibold mb-2">Secure Your 82% Revenue Share</h2>
                <p className="text-lg mb-6">Limited to the first 93 partners</p>
                <p className="text-sm text-gray-400 mb-4">From 1882 to 18/82: Revolutionizing Partnership Economics</p>

                <div className="bg-white text-blue-900 px-4 py-2 rounded-xl font-bold inline-block text-lg mb-6">
                  NovaFuse Partner Empowerment
                </div>

                <p className="mt-4 text-xl font-semibold">Includes Access To:</p>
                <ul className="mt-2 space-y-2">
                  <li className="flex items-center">
                    <span className="mr-2 text-green-400">✓</span>
                    <span>82% Revenue Share on All Transactions</span>
                  </li>
                  <li className="flex items-center">
                    <span className="mr-2 text-green-400">✓</span>
                    <span>Priority Placement in the Partner Directory</span>
                  </li>
                  <li className="flex items-center">
                    <span className="mr-2 text-green-400">✓</span>
                    <span>Early Access to New Features</span>
                  </li>
                  <li className="flex items-center">
                    <span className="mr-2 text-green-400">✓</span>
                    <span>Co-Marketing Opportunities</span>
                  </li>
                  <li className="flex items-center">
                    <span className="mr-2 text-green-400">✓</span>
                    <span>Dedicated Partner Success Manager</span>
                  </li>
                </ul>

                <CountdownTimer targetDate={countdownTarget} />

                <div className="mt-8 text-center">
                  <p className="text-sm text-gray-300 mb-2">
                    After the 18-month Founding Partner enrollment period ends, the standard revenue share will apply.
                  </p>
                  <Link href="/" className="text-blue-300 hover:text-blue-100 text-sm">
                    View full terms and conditions
                  </Link>
                </div>
              </div>
            ) : (
              <div className="bg-blue-900 bg-opacity-50 p-8 rounded-lg border border-blue-700">
                <h1 className="text-4xl font-extrabold mb-4">Daring Developer Opportunity</h1>
                <h2 className="text-2xl font-semibold mb-2">Secure Your 82% App Store Revenue</h2>
                <p className="text-lg mb-6">Limited to the first 50 approved apps</p>
                <p className="text-sm text-gray-400 mb-4">From 1882 to 18/82: Revolutionizing the Compliance App Economy</p>

                <div className="bg-white text-blue-900 px-4 py-2 rounded-xl font-bold inline-block text-lg mb-6">
                  NovaFuse Compliance App Store
                </div>

                <p className="mt-4 text-xl font-semibold">Includes Access To:</p>
                <ul className="mt-2 space-y-2">
                  <li className="flex items-center">
                    <span className="mr-2 text-green-400">✓</span>
                    <span>82% Revenue Share for 5 Years</span>
                  </li>
                  <li className="flex items-center">
                    <span className="mr-2 text-green-400">✓</span>
                    <span>Equity Option (0.5-2% for $1M+ GMV/year)</span>
                  </li>
                  <li className="flex items-center">
                    <span className="mr-2 text-green-400">✓</span>
                    <span>Category Exclusivity Protection</span>
                  </li>
                  <li className="flex items-center">
                    <span className="mr-2 text-green-400">✓</span>
                    <span>Featured Placement in App Store</span>
                  </li>
                  <li className="flex items-center">
                    <span className="mr-2 text-green-400">✓</span>
                    <span>Developer Success Program</span>
                  </li>
                </ul>

                <div className="bg-blue-800 bg-opacity-70 p-4 rounded-lg mt-6">
                  <h3 className="font-bold mb-2">Eligibility Requirements:</h3>
                  <ul className="space-y-1 text-sm">
                    <li className="flex items-start">
                      <span className="mr-2 text-blue-400">•</span>
                      <span>Pass NovaCert security & compliance audits</span>
                    </li>
                    <li className="flex items-start">
                      <span className="mr-2 text-blue-400">•</span>
                      <span>Unique value proposition (no duplicate solutions)</span>
                    </li>
                    <li className="flex items-start">
                      <span className="mr-2 text-blue-400">•</span>
                      <span>Launch within 18 months of NovaFuse GA</span>
                    </li>
                    <li className="flex items-start">
                      <span className="mr-2 text-blue-400">•</span>
                      <span>Achieve $100K ARR by Year 2</span>
                    </li>
                  </ul>
                </div>

                <CountdownTimer targetDate={countdownTarget} />

                <div className="mt-8 text-center">
                  <p className="text-sm text-gray-300 mb-2">
                    After 5 years, transition to 70/30 split with performance bonuses.
                  </p>
                  <Link href="/" className="text-blue-300 hover:text-blue-100 text-sm">
                    View full terms and conditions
                  </Link>
                </div>
              </div>
            )}
          </div>

          {/* Right Column - Sign Up Form */}
          <div className="lg:w-1/2">
            <div className="bg-blue-900 bg-opacity-50 p-8 rounded-lg border border-blue-700">
              <h2 className="text-2xl font-bold mb-6">
                {partnerType === 'partner' ? 'Become a Founding Partner' : 'Become a Founding Developer'}
              </h2>

              <form className="space-y-6">
                {/* Common Fields */}
                <div>
                  <label className="block text-sm font-medium mb-1" htmlFor="company-name">
                    {partnerType === 'partner' ? 'Company Name' : 'Company/Developer Name'}
                  </label>
                  <input
                    type="text"
                    id="company-name"
                    className="w-full bg-gray-800 border border-gray-700 rounded p-2 text-white"
                    placeholder={partnerType === 'partner' ? 'Your company name' : 'Your company or developer name'}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1" htmlFor="full-name">
                    Full Name
                  </label>
                  <input
                    type="text"
                    id="full-name"
                    className="w-full bg-gray-800 border border-gray-700 rounded p-2 text-white"
                    placeholder="Your full name"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1" htmlFor="email">
                    Business Email
                  </label>
                  <input
                    type="email"
                    id="email"
                    className="w-full bg-gray-800 border border-gray-700 rounded p-2 text-white"
                    placeholder="<EMAIL>"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1" htmlFor="phone">
                    Phone Number
                  </label>
                  <input
                    type="tel"
                    id="phone"
                    className="w-full bg-gray-800 border border-gray-700 rounded p-2 text-white"
                    placeholder="Your phone number"
                  />
                </div>

                {/* Partner-specific fields */}
                {partnerType === 'partner' ? (
                  <>
                    <div>
                      <label className="block text-sm font-medium mb-1" htmlFor="company-size">
                        Company Size
                      </label>
                      <select
                        id="company-size"
                        className="w-full bg-gray-800 border border-gray-700 rounded p-2 text-white"
                      >
                        <option value="">Select company size</option>
                        <option value="1-10">1-10 employees</option>
                        <option value="11-50">11-50 employees</option>
                        <option value="51-200">51-200 employees</option>
                        <option value="201-500">201-500 employees</option>
                        <option value="501+">501+ employees</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-1" htmlFor="partner-type">
                        Partner Type
                      </label>
                      <select
                        id="partner-type"
                        className="w-full bg-gray-800 border border-gray-700 rounded p-2 text-white"
                      >
                        <option value="">Select partner type</option>
                        <option value="technology">Technology Partner</option>
                        <option value="consulting">Consulting Partner</option>
                        <option value="implementation">Implementation Partner</option>
                        <option value="reseller">Reseller</option>
                        <option value="other">Other</option>
                      </select>
                    </div>
                  </>
                ) : (
                  <>
                    <div>
                      <label className="block text-sm font-medium mb-1" htmlFor="app-name">
                        App Name
                      </label>
                      <input
                        type="text"
                        id="app-name"
                        className="w-full bg-gray-800 border border-gray-700 rounded p-2 text-white"
                        placeholder="Your app name"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-1" htmlFor="app-category">
                        App Category
                      </label>
                      <select
                        id="app-category"
                        className="w-full bg-gray-800 border border-gray-700 rounded p-2 text-white"
                      >
                        <option value="">Select app category</option>
                        <option value="gdpr">GDPR Compliance</option>
                        <option value="hipaa">HIPAA Compliance</option>
                        <option value="pci">PCI DSS Compliance</option>
                        <option value="soc2">SOC 2 Compliance</option>
                        <option value="iso27001">ISO 27001 Compliance</option>
                        <option value="nist">NIST Compliance</option>
                        <option value="cmmc">CMMC Compliance</option>
                        <option value="other">Other Compliance Framework</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-1" htmlFor="app-description">
                        App Description
                      </label>
                      <textarea
                        id="app-description"
                        rows="3"
                        className="w-full bg-gray-800 border border-gray-700 rounded p-2 text-white"
                        placeholder="Brief description of your app and its unique value proposition"
                      ></textarea>
                    </div>

                    <div className="bg-blue-800 bg-opacity-50 p-4 rounded-lg">
                      <h3 className="font-bold mb-2 text-sm">NovaCert Requirements</h3>
                      <p className="text-xs text-gray-300 mb-2">
                        By applying, you agree to submit your app for NovaCert security and compliance audits before launch.
                      </p>
                      <p className="text-xs text-gray-300">
                        A $10,000 performance bond is required (refunded at $100K GMV).
                      </p>
                    </div>
                  </>
                )}

                <div className="flex items-start">
                  <div className="flex items-center h-5">
                    <input
                      id="terms"
                      type="checkbox"
                      className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded"
                    />
                  </div>
                  <div className="ml-3 text-sm">
                    <label htmlFor="terms" className="font-medium text-gray-300">
                      I agree to the{" "}
                      <Link href="/" className="text-blue-400 hover:text-blue-300">
                        terms and conditions
                      </Link>
                    </label>
                  </div>
                </div>

                <Link
                  href={`/onboard?type=${partnerType}`}
                  className="block w-full text-center bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 px-4 rounded-lg font-bold hover:from-blue-700 hover:to-purple-700 transition duration-300"
                >
                  {partnerType === 'partner'
                    ? 'Secure Your Founding Partner Status'
                    : 'Apply for Daring Developer Status'}
                </Link>
              </form>

              <div className="mt-6 text-center">
                <p className="text-sm text-gray-300">
                  Already have an account?{" "}
                  <Link href="/sign-in" className="text-blue-400 hover:text-blue-300">
                    Sign in
                  </Link>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default PartnerSignup;
