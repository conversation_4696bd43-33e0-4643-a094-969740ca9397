/**
 * Comphyological Tensor Core Example
 *
 * This example demonstrates how to use the Comphyological Tensor Core
 * to fuse CSDE, CSFE, and CSME engines using tensor operations.
 */

console.log('Starting Comphyological Tensor Core Example...');

try {
  const tensorModule = require('../src/quantum/tensor');
  console.log('Tensor module loaded successfully:', Object.keys(tensorModule));

  const {
    createComphyologicalTensorCore,
    createTensorComponents
  } = tensorModule;
} catch (error) {
  console.error('Error loading tensor module:', error);
}

/**
 * Example 1: Basic Tensor Operations
 */
async function example1() {
  console.log('\n=== Example 1: Basic Tensor Operations ===\n');

  // Create tensor components
  const components = createTensorComponents({
    enableLogging: true,
    precision: 4
  });

  // Create tensors
  const tensorA = [1, 2, 3, 4];
  const tensorB = [5, 6, 7, 8];

  console.log('Tensor A:', tensorA);
  console.log('Tensor B:', tensorB);

  // Tensor product
  const product = components.tensorOps.tensorProduct(tensorA, tensorB);
  console.log('\nTensor Product (A ⊗ B):', product);

  // Direct sum
  const sum = components.tensorOps.directSum(tensorA, tensorB);
  console.log('\nDirect Sum (A ⊕ B):', sum);

  // Scale tensor
  const scaled = components.tensorOps.scaleTensor(tensorA, 3.1416);
  console.log('\nScaled Tensor (A * 3.1416):', scaled);
}

/**
 * Example 2: Dynamic Weighting Protocol
 */
async function example2() {
  console.log('\n=== Example 2: Dynamic Weighting Protocol ===\n');

  // Create tensor components
  const components = createTensorComponents({
    enableLogging: true
  });

  // Create engine metrics
  const csdeMetrics = {
    governanceScore: 0.8,
    dataQualityScore: 0.7,
    actionConfidence: 0.9
  };

  const csfeMetrics = {
    riskScore: 0.3,
    financialScore: 0.6,
    actionConfidence: 0.8
  };

  const csmeMetrics = {
    bioScore: 0.5,
    medComplianceScore: 0.6,
    actionConfidence: 0.7
  };

  console.log('CSDE Metrics:', csdeMetrics);
  console.log('CSFE Metrics:', csfeMetrics);
  console.log('CSME Metrics:', csmeMetrics);

  // Calculate weights
  const weightingResult = components.dynamicWeighting.calculateWeights(
    csdeMetrics,
    csfeMetrics,
    csmeMetrics
  );

  console.log('\nWeighting Result:');
  console.log('Weights:', weightingResult.weights);
  console.log('Dominant Engine:', weightingResult.dominantEngine);
  console.log('Dominance Scores:', weightingResult.dominanceScores);

  // Create tensors
  const csdeTensor = { values: [0.8, 0.7, 0.9, 0.8] };
  const csfeTensor = { values: [0.3, 0.6, 0.8, 0.7] };
  const csmeTensor = { values: [0.5, 0.6, 0.7, 0.6] };

  // Apply weights
  const weightedTensors = components.dynamicWeighting.applyWeights(
    csdeTensor,
    csfeTensor,
    csmeTensor
  );

  console.log('\nWeighted Tensors:');
  console.log('CSDE Tensor:', weightedTensors.csdeTensor);
  console.log('CSFE Tensor:', weightedTensors.csfeTensor);
  console.log('CSME Tensor:', weightedTensors.csmeTensor);
}

/**
 * Example 3: Psi Tensor Core
 */
async function example3() {
  console.log('\n=== Example 3: Psi Tensor Core ===\n');

  // Create tensor components
  const components = createTensorComponents({
    enableLogging: true
  });

  // Create tensors
  const csdeTensor = components.psiTensorCore.createCsdeTensor(
    0.8, // governance
    0.7, // data quality
    'allow', // action
    0.9 // confidence
  );

  const csfeTensor = components.psiTensorCore.createCsfeTensor(
    0.3, // risk
    0.6, // finance
    'monitor', // action
    0.8 // confidence
  );

  const csmeTensor = components.psiTensorCore.createCsmeTensor(
    0.5, // bio
    0.6, // med compliance
    'alert', // action
    0.7 // confidence
  );

  console.log('CSDE Tensor:', csdeTensor);
  console.log('CSFE Tensor:', csfeTensor);
  console.log('CSME Tensor:', csmeTensor);

  // Create metrics for dynamic weighting
  const metrics = {
    csde: {
      governanceScore: 0.8,
      dataQualityScore: 0.7,
      actionConfidence: 0.9
    },
    csfe: {
      riskScore: 0.3,
      financialScore: 0.6,
      actionConfidence: 0.8
    },
    csme: {
      bioScore: 0.5,
      medComplianceScore: 0.6,
      actionConfidence: 0.7
    }
  };

  // Fuse engines
  const fusedTensor = components.psiTensorCore.fuseEngines(
    csdeTensor,
    csfeTensor,
    csmeTensor,
    metrics
  );

  console.log('\nFused Tensor:');
  console.log('Type:', fusedTensor.type);
  console.log('Dimensions:', fusedTensor.dimensions);
  console.log('Processing Time:', fusedTensor.processingTime, 'ms');

  // Extract action
  const actionResult = components.psiTensorCore.extractAction(fusedTensor);

  console.log('\nExtracted Action:');
  console.log('Action:', actionResult.action);
  console.log('Confidence:', actionResult.confidence);
  console.log('Value:', actionResult.value);
}

/**
 * Example 4: Energy Calculator
 */
async function example4() {
  console.log('\n=== Example 4: Energy Calculator ===\n');

  // Create tensor components
  const components = createTensorComponents({
    enableLogging: true
  });

  // Create domain data
  const csdeData = {
    governance: 0.8,
    dataQuality: 0.7,
    action: 'allow',
    confidence: 0.9
  };

  const csfeData = {
    risk: 0.3,
    policyCompliance: 0.6,
    action: 'monitor',
    confidence: 0.8
  };

  const csmeData = {
    trustFactor: 0.5,
    integrityFactor: 0.6,
    action: 'alert',
    confidence: 0.7
  };

  console.log('CSDE Data:', csdeData);
  console.log('CSFE Data:', csfeData);
  console.log('CSME Data:', csmeData);

  // Create fused tensor
  const fusedTensor = {
    values: [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8]
  };

  // Calculate Comphyon
  const comphyonResult = components.energyCalculator.calculateComphyon(
    csdeData,
    csfeData,
    csmeData,
    fusedTensor
  );

  console.log('\nComphyon Result:');
  console.log('Comphyon Value:', comphyonResult.comphyonValue);
  console.log('Energies:', comphyonResult.energies);
  console.log('Gradients:', comphyonResult.gradients);
  console.log('Processing Time:', comphyonResult.processingTime, 'ms');
}

/**
 * Example 5: Comphyological Tensor Core
 */
async function example5() {
  console.log('\n=== Example 5: Comphyological Tensor Core ===\n');

  // Create Comphyological Tensor Core
  const tensorCore = createComphyologicalTensorCore({
    enableLogging: true,
    strictMode: false,
    useGPU: false,
    useDynamicWeighting: true,
    precision: 6,
    normalizationFactor: 166000
  });

  // Create domain data
  const csdeData = {
    governance: 0.8,
    dataQuality: 0.7,
    action: 'allow',
    confidence: 0.9
  };

  const csfeData = {
    risk: 0.3,
    policyCompliance: 0.6,
    action: 'monitor',
    confidence: 0.8
  };

  const csmeData = {
    trustFactor: 0.5,
    integrityFactor: 0.6,
    action: 'alert',
    confidence: 0.7
  };

  console.log('CSDE Data:', csdeData);
  console.log('CSFE Data:', csfeData);
  console.log('CSME Data:', csmeData);

  // Process data
  const result = tensorCore.processData(
    csdeData,
    csfeData,
    csmeData
  );

  console.log('\nProcessing Result:');
  console.log('Comphyon Value:', result.comphyon);
  console.log('Action:', result.action);
  console.log('Confidence:', result.confidence);
  console.log('Energies:', result.energies);
  console.log('Weights:', result.weights);
  console.log('Processing Time:', result.processingTime, 'ms');

  // Get metrics
  const metrics = tensorCore.getMetrics();

  console.log('\nMetrics:');
  console.log('Fusion Count:', metrics.fusionCount);
  console.log('Last Comphyon Value:', metrics.lastComphyonValue);
  console.log('Average Comphyon Value:', metrics.averageComphyonValue);
  console.log('Max Comphyon Value:', metrics.maxComphyonValue);
  console.log('Min Comphyon Value:', metrics.minComphyonValue);
  console.log('Processing Time:', metrics.processingTime, 'ms');
}

/**
 * Example 6: Performance Benchmark
 */
async function example6() {
  console.log('\n=== Example 6: Performance Benchmark ===\n');

  // Create Comphyological Tensor Core
  const tensorCore = createComphyologicalTensorCore({
    enableLogging: false, // Disable logging for benchmark
    strictMode: false,
    useGPU: false,
    useDynamicWeighting: true,
    precision: 6,
    normalizationFactor: 166000
  });

  // Reset metrics
  tensorCore.resetMetrics();

  // Number of iterations
  const iterations = 1000;

  console.log(`Running benchmark with ${iterations} iterations...`);

  const startTime = Date.now();

  // Run benchmark
  for (let i = 0; i < iterations; i++) {
    // Create random domain data
    const csdeData = {
      governance: Math.random(),
      dataQuality: Math.random(),
      action: Math.random() > 0.5 ? 'allow' : 'block',
      confidence: Math.random()
    };

    const csfeData = {
      risk: Math.random(),
      policyCompliance: Math.random(),
      action: Math.random() > 0.5 ? 'monitor' : 'alert',
      confidence: Math.random()
    };

    const csmeData = {
      trustFactor: Math.random(),
      integrityFactor: Math.random(),
      action: Math.random() > 0.5 ? 'remediate' : 'escalate',
      confidence: Math.random()
    };

    // Process data
    tensorCore.processData(
      csdeData,
      csfeData,
      csmeData
    );
  }

  const endTime = Date.now();
  const totalTime = endTime - startTime;
  const averageTime = totalTime / iterations;

  console.log('\nBenchmark Results:');
  console.log('Total Time:', totalTime, 'ms');
  console.log('Average Time per Iteration:', averageTime, 'ms');
  console.log('Iterations per Second:', 1000 / averageTime);

  // Get metrics
  const metrics = tensorCore.getMetrics();

  console.log('\nMetrics:');
  console.log('Fusion Count:', metrics.fusionCount);
  console.log('Average Comphyon Value:', metrics.averageComphyonValue);
  console.log('Max Comphyon Value:', metrics.maxComphyonValue);
  console.log('Min Comphyon Value:', metrics.minComphyonValue);
}

/**
 * Run all examples
 */
async function runAllExamples() {
  await example1();
  await example2();
  await example3();
  await example4();
  await example5();
  await example6();
}

// Run all examples
runAllExamples().catch(error => {
  console.error('Error running examples:', error);
});
