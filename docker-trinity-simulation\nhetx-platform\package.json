{"name": "nhetx-quantum-platform", "version": "1.0.0-TRANSCENDENT", "description": "NHET-X Quantum Intelligence Platform - Reality Studio for the Post-Human Era", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "reality:compile": "echo 'Compiling consciousness reality...'", "consciousness:deploy": "echo 'Deploying consciousness field...'"}, "dependencies": {"@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "autoprefixer": "^10.4.21", "framer-motion": "^10.16.0", "lucide-react": "^0.292.0", "next": "15.0.0", "postcss": "^8.5.4", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwindcss": "^3.3.0", "typescript": "^5.0.0"}, "keywords": ["consciousness", "reality-programming", "quantum-intelligence", "NHET-X", "HOD-patent", "financial-consciousness", "temporal-programming"], "author": "<PERSON> <<EMAIL>>", "license": "HOD-PATENT-PROTECTED", "engines": {"node": ">=18.0.0"}}