#!/usr/bin/env python3
"""
UUFT Demonstration Runner

This script runs the demonstration functions in uuft_code.py to show the UUFT in action.
"""

import os
import sys
import json
from datetime import datetime

# Try to import the UUFT code
try:
    from uuft_code import (
        UUFT<PERSON>ore, 
        UUFTPatternExtractor, 
        UUFTCrossDomainPredictor,
        demonstrate_uuft_core, 
        demonstrate_pattern_extraction, 
        demonstrate_cross_domain_prediction
    )
except ImportError:
    print("Error: Could not import UUFT code. Make sure uuft_code.py is in the current directory.")
    sys.exit(1)

# Create output directory
OUTPUT_DIR = "uuft_demo_results"
os.makedirs(OUTPUT_DIR, exist_ok=True)

def main():
    """Main function to run all demonstrations"""
    print("="*80)
    print("UUFT Demonstration Runner")
    print("="*80)
    print(f"Results will be saved to: {os.path.abspath(OUTPUT_DIR)}")
    
    # Run UUFTCore demonstration
    print("\n" + "="*80)
    print("Running UUFTCore Demonstration")
    print("="*80)
    demonstrate_uuft_core()
    
    # Run Pattern Extraction demonstration
    print("\n" + "="*80)
    print("Running Pattern Extraction Demonstration")
    print("="*80)
    demonstrate_pattern_extraction()
    
    # Run Cross-Domain Prediction demonstration
    print("\n" + "="*80)
    print("Running Cross-Domain Prediction Demonstration")
    print("="*80)
    try:
        demonstrate_cross_domain_prediction()
    except Exception as e:
        print(f"Error in Cross-Domain Prediction demonstration: {e}")
    
    print("\n" + "="*80)
    print("UUFT Demonstrations Completed")
    print("="*80)

if __name__ == "__main__":
    main()
