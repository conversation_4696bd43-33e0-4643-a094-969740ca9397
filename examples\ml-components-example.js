/**
 * Machine Learning Components Example
 *
 * This example demonstrates how to use the machine learning components
 * for the Finite Universe Principle defense system, including anomaly
 * detection and predictive analytics.
 */

const {
  // Complete defense system
  createCompleteDefenseSystem,
  
  // Machine learning components
  createAnomalyDetector,
  createPredictiveAnalytics,
  createMLComponents
} = require('../src/quantum/finite-universe-principle');

/**
 * Example 1: Anomaly Detection
 * 
 * This example demonstrates how to use the anomaly detector.
 */
async function example1() {
  console.log('\n=== Example 1: Anomaly Detection ===\n');

  // Create anomaly detector
  const anomalyDetector = createAnomalyDetector({
    enableLogging: true,
    historyLength: 100,
    anomalyThreshold: 3.0,
    learningRate: 0.1
  });

  // Register event listener for anomalies
  anomalyDetector.on('anomalies-detected', (data) => {
    console.log('Anomalies detected:', data);
  });

  // Generate normal data
  console.log('Processing normal data...');
  for (let i = 0; i < 20; i++) {
    const normalData = {
      securityScore: 7 + Math.random(),
      threatLevel: 2 + Math.random(),
      encryptionStrength: 256
    };
    
    const result = anomalyDetector.processData(normalData, 'cyber');
    
    if (i % 5 === 0) {
      console.log(`Iteration ${i}: No anomalies detected`);
    }
  }

  // Generate anomalous data
  console.log('\nProcessing anomalous data...');
  const anomalousData = {
    securityScore: 2, // Much lower than normal
    threatLevel: 9, // Much higher than normal
    encryptionStrength: 256
  };
  
  const result = anomalyDetector.processData(anomalousData, 'cyber');
  console.log('Anomaly detection result:', result);

  // Get anomaly statistics
  console.log('\nAnomaly statistics:');
  console.log(anomalyDetector.getAnomalyStats());

  // Get domain statistics
  console.log('\nDomain statistics:');
  console.log(anomalyDetector.getDomainStatistics('cyber'));
}

/**
 * Example 2: Predictive Analytics
 * 
 * This example demonstrates how to use predictive analytics.
 */
async function example2() {
  console.log('\n=== Example 2: Predictive Analytics ===\n');

  // Create predictive analytics
  const predictiveAnalytics = createPredictiveAnalytics({
    enableLogging: true,
    historyLength: 100,
    forecastHorizon: 10,
    seasonalityPeriod: 24,
    confidenceLevel: 0.95
  });

  // Register event listener for predicted violations
  predictiveAnalytics.on('violations-predicted', (data) => {
    console.log('Violations predicted:', data);
  });

  // Generate metrics data with increasing violations
  console.log('Processing metrics data with increasing violations...');
  for (let i = 0; i < 20; i++) {
    const metrics = {
      boundaryViolations: i,
      boundaryCorrections: i * 2,
      validationFailures: Math.floor(i / 2),
      domainMetrics: {
        cyber: {
          boundaryViolations: Math.floor(i / 3),
          averageSecurityScore: 8 - i * 0.1,
          averageThreatLevel: 2 + i * 0.1
        },
        financial: {
          boundaryViolations: Math.floor(i / 4),
          averageInterestRate: 0.05,
          totalTransactionVolume: 1000 * i
        },
        medical: {
          boundaryViolations: Math.floor(i / 5),
          averageHeartRate: 70 + i,
          criticalAlerts: Math.floor(i / 10)
        }
      }
    };
    
    const result = predictiveAnalytics.processMetrics(metrics);
    
    if (i % 5 === 0 || i === 19) {
      console.log(`Iteration ${i}: Metrics processed`);
      if (result.predictions && result.predictions.predictedViolations) {
        console.log('Predictions:', result.predictions);
      }
    }
  }

  // Get forecasts
  console.log('\nForecasts:');
  console.log(predictiveAnalytics.getForecasts());

  // Get prediction accuracy
  console.log('\nPrediction accuracy:');
  console.log(predictiveAnalytics.getAccuracy());
}

/**
 * Example 3: Integrated ML Components
 * 
 * This example demonstrates how to use ML components with the complete defense system.
 */
async function example3() {
  console.log('\n=== Example 3: Integrated ML Components ===\n');

  // Create complete defense system
  const defenseSystem = createCompleteDefenseSystem({
    enableLogging: false, // Disable logging for cleaner output
    enableMonitoring: true,
    strictMode: true
  });

  // Create ML components
  const mlComponents = createMLComponents({
    anomalyDetectorOptions: {
      enableLogging: true,
      anomalyThreshold: 3.0
    },
    predictiveAnalyticsOptions: {
      enableLogging: true,
      forecastHorizon: 5
    }
  });

  // Register event listeners
  mlComponents.anomalyDetector.on('anomalies-detected', (data) => {
    console.log('Anomalies detected:', data);
  });

  mlComponents.predictiveAnalytics.on('violations-predicted', (data) => {
    console.log('Violations predicted:', data);
  });

  // Process data through the defense system and ML components
  console.log('Processing data through the defense system and ML components...');
  
  for (let i = 0; i < 10; i++) {
    // Create test data
    const testData = {
      securityScore: 7 + Math.random() * 2,
      threatLevel: 2 + Math.random(),
      encryptionStrength: 256,
      balance: 1000 + Math.random() * 1000,
      interestRate: 0.05 + Math.random() * 0.05,
      heartRate: 70 + Math.random() * 10,
      bloodPressure: 120 + Math.random() * 20
    };
    
    // Process data through defense system
    const processedData = await defenseSystem.processData(testData);
    
    // Process data through anomaly detector
    mlComponents.anomalyDetector.processData(processedData, 'cyber');
    
    // Process metrics through predictive analytics
    const metrics = defenseSystem.monitoringDashboard.getCurrentMetrics();
    mlComponents.predictiveAnalytics.processMetrics(metrics);
    
    if (i % 3 === 0) {
      console.log(`Iteration ${i}: Data processed`);
    }
  }

  // Process anomalous data
  console.log('\nProcessing anomalous data...');
  const anomalousData = {
    securityScore: 2, // Much lower than normal
    threatLevel: 9, // Much higher than normal
    encryptionStrength: Infinity, // Should be bounded
    balance: 1e10, // Very large balance
    interestRate: 2.0, // Above maximum
    heartRate: 200, // High heart rate
    bloodPressure: -50 // Invalid blood pressure
  };
  
  await defenseSystem.processData(anomalousData);
  
  // Process through anomaly detector
  mlComponents.anomalyDetector.processData(anomalousData, 'cyber');
  
  // Process metrics through predictive analytics
  const metrics = defenseSystem.monitoringDashboard.getCurrentMetrics();
  mlComponents.predictiveAnalytics.processMetrics(metrics);

  // Dispose resources
  defenseSystem.dispose();
}

/**
 * Run all examples
 */
async function runAllExamples() {
  await example1();
  await example2();
  await example3();
}

// Run all examples
runAllExamples().catch(error => {
  console.error('Error running examples:', error);
});
