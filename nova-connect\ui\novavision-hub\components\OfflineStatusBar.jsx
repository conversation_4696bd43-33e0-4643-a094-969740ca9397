/**
 * OfflineStatusBar Component
 * 
 * A component that displays the current offline status and allows toggling offline mode.
 */

import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { useOffline } from '../offline/OfflineContext';

/**
 * OfflineStatusBar component
 * 
 * @param {Object} props - Component props
 * @param {string} [props.className] - Additional CSS class names
 * @param {Object} [props.style] - Additional inline styles
 * @returns {React.ReactElement} OfflineStatusBar component
 */
const OfflineStatusBar = ({
  className = '',
  style = {}
}) => {
  const { 
    isOnline, 
    offlineMode, 
    pendingRequests, 
    lastSyncTime,
    toggleOfflineMode, 
    syncPendingRequests 
  } = useOffline();
  
  const [showDetails, setShowDetails] = useState(false);
  
  // Toggle details
  const toggleDetails = () => {
    setShowDetails(!showDetails);
  };
  
  // Format last sync time
  const formatLastSyncTime = () => {
    if (!lastSyncTime) return 'Never';
    
    const now = new Date();
    const diff = now - lastSyncTime;
    
    // If less than a minute ago
    if (diff < 60000) {
      return 'Just now';
    }
    
    // If less than an hour ago
    if (diff < 3600000) {
      const minutes = Math.floor(diff / 60000);
      return `${minutes} minute${minutes !== 1 ? 's' : ''} ago`;
    }
    
    // If less than a day ago
    if (diff < 86400000) {
      const hours = Math.floor(diff / 3600000);
      return `${hours} hour${hours !== 1 ? 's' : ''} ago`;
    }
    
    // Otherwise, show date
    return lastSyncTime.toLocaleString();
  };
  
  // Get status color
  const getStatusColor = () => {
    if (!isOnline) {
      return 'bg-error';
    }
    
    if (offlineMode) {
      return 'bg-warning';
    }
    
    if (pendingRequests.length > 0) {
      return 'bg-info';
    }
    
    return 'bg-success';
  };
  
  // Get status text
  const getStatusText = () => {
    if (!isOnline) {
      return 'Offline';
    }
    
    if (offlineMode) {
      return 'Offline Mode';
    }
    
    if (pendingRequests.length > 0) {
      return `Online (${pendingRequests.length} pending)`;
    }
    
    return 'Online';
  };
  
  return (
    <div
      className={`fixed bottom-0 left-0 right-0 z-50 ${className}`}
      style={style}
      data-testid="offline-status-bar"
    >
      {/* Status bar */}
      <div className={`flex justify-between items-center px-4 py-2 text-white ${getStatusColor()}`}>
        <div className="flex items-center">
          {/* Status indicator */}
          <div className="flex items-center">
            <div className={`w-3 h-3 rounded-full mr-2 ${isOnline ? 'bg-white' : 'bg-gray-300'}`} />
            <span className="font-medium">{getStatusText()}</span>
          </div>
          
          {/* Last sync time */}
          {lastSyncTime && (
            <div className="ml-4 text-sm opacity-80">
              Last sync: {formatLastSyncTime()}
            </div>
          )}
        </div>
        
        <div className="flex items-center space-x-2">
          {/* Toggle details button */}
          <button
            className="text-sm px-2 py-1 rounded hover:bg-white hover:bg-opacity-20 transition-colors duration-200"
            onClick={toggleDetails}
            aria-expanded={showDetails}
            aria-controls="offline-details"
          >
            {showDetails ? 'Hide Details' : 'Show Details'}
          </button>
          
          {/* Sync button */}
          {isOnline && pendingRequests.length > 0 && (
            <button
              className="text-sm px-2 py-1 rounded bg-white bg-opacity-20 hover:bg-opacity-30 transition-colors duration-200"
              onClick={syncPendingRequests}
              disabled={!isOnline}
            >
              Sync Now
            </button>
          )}
          
          {/* Toggle offline mode button */}
          <button
            className={`
              text-sm px-3 py-1 rounded
              ${offlineMode
                ? 'bg-white text-warning hover:bg-gray-100'
                : 'bg-white bg-opacity-20 hover:bg-opacity-30'
              }
              transition-colors duration-200
            `}
            onClick={toggleOfflineMode}
            aria-pressed={offlineMode}
          >
            {offlineMode ? 'Go Online' : 'Go Offline'}
          </button>
        </div>
      </div>
      
      {/* Details panel */}
      {showDetails && (
        <div
          id="offline-details"
          className="bg-surface border-t border-divider p-4 text-textPrimary"
        >
          <div className="max-w-4xl mx-auto">
            <h3 className="text-lg font-medium mb-2">Offline Status</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Connection status */}
              <div>
                <h4 className="text-sm font-medium text-textSecondary mb-1">Connection Status</h4>
                <p className="flex items-center">
                  <span className={`inline-block w-3 h-3 rounded-full mr-2 ${isOnline ? 'bg-success' : 'bg-error'}`} />
                  {isOnline ? 'Connected to network' : 'No network connection'}
                </p>
              </div>
              
              {/* Offline mode */}
              <div>
                <h4 className="text-sm font-medium text-textSecondary mb-1">Offline Mode</h4>
                <p className="flex items-center">
                  <span className={`inline-block w-3 h-3 rounded-full mr-2 ${offlineMode ? 'bg-warning' : 'bg-success'}`} />
                  {offlineMode ? 'Enabled (working offline)' : 'Disabled (working online)'}
                </p>
              </div>
              
              {/* Pending requests */}
              <div>
                <h4 className="text-sm font-medium text-textSecondary mb-1">Pending Requests</h4>
                <p>
                  {pendingRequests.length === 0
                    ? 'No pending requests'
                    : `${pendingRequests.length} request${pendingRequests.length !== 1 ? 's' : ''} pending sync`
                  }
                </p>
              </div>
              
              {/* Last sync */}
              <div>
                <h4 className="text-sm font-medium text-textSecondary mb-1">Last Sync</h4>
                <p>{formatLastSyncTime()}</p>
              </div>
            </div>
            
            {/* Pending requests list */}
            {pendingRequests.length > 0 && (
              <div className="mt-4">
                <h4 className="text-sm font-medium text-textSecondary mb-2">Pending Requests</h4>
                <div className="max-h-40 overflow-y-auto border border-divider rounded-md">
                  <table className="min-w-full divide-y divide-divider">
                    <thead className="bg-surface">
                      <tr>
                        <th className="px-4 py-2 text-left text-xs font-medium text-textSecondary uppercase tracking-wider">Method</th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-textSecondary uppercase tracking-wider">URL</th>
                      </tr>
                    </thead>
                    <tbody className="bg-background divide-y divide-divider">
                      {pendingRequests.map((request, index) => (
                        <tr key={index}>
                          <td className="px-4 py-2 text-sm text-textPrimary">{request.method}</td>
                          <td className="px-4 py-2 text-sm text-textPrimary truncate max-w-xs">{request.url}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

OfflineStatusBar.propTypes = {
  className: PropTypes.string,
  style: PropTypes.object
};

export default OfflineStatusBar;
