import React from 'react';
import {
  <PERSON>agramFrame,
  ComponentBox,
  ComponentLabel,
  ComponentNumber,
  Arrow,
  VerticalArrow,
  ContainerBox,
  ContainerLabel,
  CurvedArrow
} from '../components/DiagramComponents';

const ExplainableAI = () => {
  return (
    <DiagramFrame>
      <ContainerBox width="750px" height="400px" left="25px" top="20px">
        <ContainerLabel>EXPLAINABLE AI WITH RULE ATTRIBUTION</ContainerLabel>
      </ContainerBox>
      
      {/* Main flow components */}
      <ComponentBox left="80px" top="100px" width="130px">
        <ComponentNumber>301</ComponentNumber>
        <ComponentLabel>AI Fraud Model</ComponentLabel>
      </ComponentBox>
      
      <Arrow left="210px" top="130px" width="100px" />
      
      <ComponentBox left="320px" top="100px" width="130px">
        <ComponentNumber>302</ComponentNumber>
        <ComponentLabel>Feature Attribution</ComponentLabel>
        Engine
      </ComponentBox>
      
      <Arrow left="450px" top="130px" width="100px" />
      
      <ComponentBox left="560px" top="100px" width="130px">
        <ComponentNumber>303</ComponentNumber>
        <ComponentLabel>Decision Explanation</ComponentLabel>
        Generator
      </ComponentBox>
      
      <Arrow left="625px" top="160px" width="2px" height="100px" />
      
      <ComponentBox left="560px" top="260px" width="130px">
        <ComponentNumber>304</ComponentNumber>
        <ComponentLabel>Regulatory Mapping</ComponentLabel>
        System
      </ComponentBox>
      
      {/* Bias Detection Module */}
      <ComponentBox left="320px" top="260px" width="130px">
        <ComponentNumber>305</ComponentNumber>
        <ComponentLabel>Bias Detection</ComponentLabel>
        Module
      </ComponentBox>
      
      {/* Connecting arrows */}
      <VerticalArrow left="145px" top="160px" height="100px" />
      
      <Arrow left="145px" top="260px" width="175px" />
      
      <Arrow left="450px" top="290px" width="110px" transform="rotate(180deg)" />
      
      {/* Curved arrow from AI Fraud Model to Bias Detection */}
      <CurvedArrow width="240" height="160" left="145" top="130">
        <path
          d="M 0,0 Q 120,80 175,130"
          fill="none"
          stroke="#333"
          strokeWidth="2"
        />
        <polygon
          points="175,130 165,122 168,132"
          fill="#333"
        />
      </CurvedArrow>
      
      {/* Additional components */}
      <ComponentBox left="80px" top="350px" width="130px" height="50px" style={{ opacity: 0.7 }}>
        <ComponentNumber>306</ComponentNumber>
        <ComponentLabel>Confidence Scoring</ComponentLabel>
        System
      </ComponentBox>
      
      <ComponentBox left="240px" top="350px" width="130px" height="50px" style={{ opacity: 0.7 }}>
        <ComponentNumber>307</ComponentNumber>
        <ComponentLabel>Model Training</ComponentLabel>
        Data
      </ComponentBox>
      
      <ComponentBox left="400px" top="350px" width="130px" height="50px" style={{ opacity: 0.7 }}>
        <ComponentNumber>308</ComponentNumber>
        <ComponentLabel>Regulatory Alignment</ComponentLabel>
        Verification
      </ComponentBox>
      
      <ComponentBox left="560px" top="350px" width="130px" height="50px" style={{ opacity: 0.7 }}>
        <ComponentNumber>309</ComponentNumber>
        <ComponentLabel>FINRA Rule 4110</ComponentLabel>
        Compliance
      </ComponentBox>
    </DiagramFrame>
  );
};

export default ExplainableAI;
