"""
Setup script for the Universal Compliance Intelligence Architecture (UCIA).
"""

from setuptools import setup, find_packages

setup(
    name="ucia",
    version="0.1.0",
    description="Universal Compliance Intelligence Architecture",
    long_description="""
        A modular compliance intelligence framework with cross-regulatory knowledge distillation.
        The UCIA provides a unified approach to compliance across multiple regulatory frameworks.
    """,
    author="NovaFuse",
    author_email="<EMAIL>",
    url="https://novafuse.com",
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    install_requires=[
        "transformers>=4.0.0",
        "torch>=1.0.0",
        "numpy>=1.0.0",
        "fastapi>=0.68.0",
        "uvicorn>=0.15.0",
        "pydantic>=1.0.0",
    ],
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
    ],
    python_requires=">=3.8",
)
