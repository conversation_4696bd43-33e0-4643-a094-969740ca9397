/**
 * Coverage Report Generator for NovaConnect
 * 
 * This script generates detailed HTML coverage reports to help identify
 * areas that need improvement to reach the 96% coverage threshold.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const COVERAGE_THRESHOLD = 96;
const COVERAGE_DIR = path.join(__dirname, 'coverage');
const REPORT_DIR = path.join(__dirname, 'coverage-report');
const COVERAGE_SUMMARY_PATH = path.join(COVERAGE_DIR, 'coverage-summary.json');

/**
 * Generates a detailed HTML coverage report
 */
function generateCoverageReport() {
  console.log('\n📊 GENERATING COVERAGE REPORT');
  console.log('==================================================');
  
  // Check if coverage data exists
  if (!fs.existsSync(COVERAGE_SUMMARY_PATH)) {
    console.error('❌ Coverage summary not found. Run tests with coverage first.');
    console.log('   Run: npm run test:coverage');
    return;
  }
  
  // Create report directory if it doesn't exist
  if (!fs.existsSync(REPORT_DIR)) {
    fs.mkdirSync(REPORT_DIR, { recursive: true });
  }
  
  // Read coverage data
  const coverageData = JSON.parse(fs.readFileSync(COVERAGE_SUMMARY_PATH, 'utf8'));
  
  // Get total coverage
  const total = coverageData.total;
  
  // Generate HTML report
  const html = generateHtmlReport(coverageData, total);
  
  // Write HTML report
  fs.writeFileSync(path.join(REPORT_DIR, 'index.html'), html);
  
  // Copy LCOV report for detailed file view
  try {
    fs.copyFileSync(
      path.join(COVERAGE_DIR, 'lcov-report', 'index.html'),
      path.join(REPORT_DIR, 'details.html')
    );
    
    // Copy lcov-report directory
    copyDirectory(
      path.join(COVERAGE_DIR, 'lcov-report'),
      path.join(REPORT_DIR, 'lcov-report')
    );
    
    console.log('✅ Coverage report generated successfully!');
    console.log(`   Report location: ${path.join(REPORT_DIR, 'index.html')}`);
  } catch (err) {
    console.error(`❌ Error copying LCOV report: ${err.message}`);
    console.log('   Only summary report will be available.');
  }
}

/**
 * Generates an HTML report from coverage data
 */
function generateHtmlReport(coverageData, total) {
  const fileEntries = Object.entries(coverageData)
    .filter(([key]) => key !== 'total')
    .sort(([_, a], [__, b]) => {
      // Sort by lowest statement coverage
      return a.statements.pct - b.statements.pct;
    });
  
  // Calculate overall status
  const allPass = 
    total.statements.pct >= COVERAGE_THRESHOLD &&
    total.branches.pct >= COVERAGE_THRESHOLD &&
    total.functions.pct >= COVERAGE_THRESHOLD &&
    total.lines.pct >= COVERAGE_THRESHOLD;
  
  // Generate HTML
  return `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>NovaConnect Coverage Report</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    h1, h2, h3 {
      color: #0066cc;
    }
    .summary {
      background-color: #f5f5f5;
      border-radius: 5px;
      padding: 20px;
      margin-bottom: 20px;
    }
    .status {
      font-size: 1.2em;
      font-weight: bold;
      padding: 10px;
      border-radius: 5px;
      display: inline-block;
      margin-bottom: 10px;
    }
    .pass {
      background-color: #dff0d8;
      color: #3c763d;
    }
    .fail {
      background-color: #f2dede;
      color: #a94442;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 20px;
    }
    th, td {
      padding: 10px;
      border: 1px solid #ddd;
      text-align: left;
    }
    th {
      background-color: #f5f5f5;
    }
    .low {
      background-color: #f2dede;
    }
    .medium {
      background-color: #fcf8e3;
    }
    .high {
      background-color: #dff0d8;
    }
    .progress-container {
      width: 100%;
      background-color: #f1f1f1;
      border-radius: 5px;
    }
    .progress-bar {
      height: 20px;
      border-radius: 5px;
      text-align: center;
      line-height: 20px;
      color: white;
    }
    .actions {
      margin-top: 20px;
    }
    .btn {
      display: inline-block;
      padding: 10px 20px;
      background-color: #0066cc;
      color: white;
      text-decoration: none;
      border-radius: 5px;
      margin-right: 10px;
    }
  </style>
</head>
<body>
  <h1>NovaConnect Coverage Report</h1>
  
  <div class="summary">
    <div class="status ${allPass ? 'pass' : 'fail'}">
      ${allPass ? 'PASS' : 'FAIL'}: Coverage ${allPass ? 'meets' : 'does not meet'} the ${COVERAGE_THRESHOLD}% threshold
    </div>
    
    <h2>Overall Coverage</h2>
    <table>
      <tr>
        <th>Metric</th>
        <th>Coverage</th>
        <th>Status</th>
        <th>Progress</th>
      </tr>
      <tr class="${total.statements.pct >= COVERAGE_THRESHOLD ? 'high' : 'low'}">
        <td>Statements</td>
        <td>${total.statements.pct.toFixed(2)}% (${total.statements.covered}/${total.statements.total})</td>
        <td>${total.statements.pct >= COVERAGE_THRESHOLD ? 'PASS' : 'FAIL'}</td>
        <td>
          <div class="progress-container">
            <div class="progress-bar" style="width: ${total.statements.pct}%; background-color: ${getColorForPercentage(total.statements.pct / 100)}">
              ${total.statements.pct.toFixed(2)}%
            </div>
          </div>
        </td>
      </tr>
      <tr class="${total.branches.pct >= COVERAGE_THRESHOLD ? 'high' : 'low'}">
        <td>Branches</td>
        <td>${total.branches.pct.toFixed(2)}% (${total.branches.covered}/${total.branches.total})</td>
        <td>${total.branches.pct >= COVERAGE_THRESHOLD ? 'PASS' : 'FAIL'}</td>
        <td>
          <div class="progress-container">
            <div class="progress-bar" style="width: ${total.branches.pct}%; background-color: ${getColorForPercentage(total.branches.pct / 100)}">
              ${total.branches.pct.toFixed(2)}%
            </div>
          </div>
        </td>
      </tr>
      <tr class="${total.functions.pct >= COVERAGE_THRESHOLD ? 'high' : 'low'}">
        <td>Functions</td>
        <td>${total.functions.pct.toFixed(2)}% (${total.functions.covered}/${total.functions.total})</td>
        <td>${total.functions.pct >= COVERAGE_THRESHOLD ? 'PASS' : 'FAIL'}</td>
        <td>
          <div class="progress-container">
            <div class="progress-bar" style="width: ${total.functions.pct}%; background-color: ${getColorForPercentage(total.functions.pct / 100)}">
              ${total.functions.pct.toFixed(2)}%
            </div>
          </div>
        </td>
      </tr>
      <tr class="${total.lines.pct >= COVERAGE_THRESHOLD ? 'high' : 'low'}">
        <td>Lines</td>
        <td>${total.lines.pct.toFixed(2)}% (${total.lines.covered}/${total.lines.total})</td>
        <td>${total.lines.pct >= COVERAGE_THRESHOLD ? 'PASS' : 'FAIL'}</td>
        <td>
          <div class="progress-container">
            <div class="progress-bar" style="width: ${total.lines.pct}%; background-color: ${getColorForPercentage(total.lines.pct / 100)}">
              ${total.lines.pct.toFixed(2)}%
            </div>
          </div>
        </td>
      </tr>
    </table>
  </div>
  
  <h2>Files with Lowest Coverage</h2>
  <p>Focus on these files to improve overall coverage:</p>
  
  <table>
    <tr>
      <th>File</th>
      <th>Statements</th>
      <th>Branches</th>
      <th>Functions</th>
      <th>Lines</th>
    </tr>
    ${fileEntries.slice(0, 10).map(([file, data]) => `
    <tr>
      <td>${file}</td>
      <td class="${getClassForPercentage(data.statements.pct)}">${data.statements.pct.toFixed(2)}%</td>
      <td class="${getClassForPercentage(data.branches.pct)}">${data.branches.pct.toFixed(2)}%</td>
      <td class="${getClassForPercentage(data.functions.pct)}">${data.functions.pct.toFixed(2)}%</td>
      <td class="${getClassForPercentage(data.lines.pct)}">${data.lines.pct.toFixed(2)}%</td>
    </tr>
    `).join('')}
  </table>
  
  <div class="actions">
    <a href="details.html" class="btn">View Detailed Report</a>
    <a href="javascript:window.print()" class="btn">Print Report</a>
  </div>
  
  <h2>Recommendations</h2>
  <ul>
    <li>Focus on files with the lowest coverage first</li>
    <li>Add tests for uncovered branches and edge cases</li>
    <li>Ensure all exported functions have unit tests</li>
    <li>Consider refactoring complex functions into smaller, more testable units</li>
  </ul>
  
  <p>Report generated on ${new Date().toLocaleString()}</p>
</body>
</html>
  `;
}

/**
 * Gets a CSS class based on a percentage value
 */
function getClassForPercentage(percentage) {
  if (percentage >= COVERAGE_THRESHOLD) {
    return 'high';
  } else if (percentage >= COVERAGE_THRESHOLD - 10) {
    return 'medium';
  } else {
    return 'low';
  }
}

/**
 * Gets a color based on a percentage value (0-1)
 */
function getColorForPercentage(percentage) {
  // Red to green gradient
  const r = Math.floor(255 * (1 - percentage));
  const g = Math.floor(255 * percentage);
  const b = 0;
  
  return `rgb(${r}, ${g}, ${b})`;
}

/**
 * Copies a directory recursively
 */
function copyDirectory(source, destination) {
  // Create destination directory if it doesn't exist
  if (!fs.existsSync(destination)) {
    fs.mkdirSync(destination, { recursive: true });
  }
  
  // Get all files and directories in the source directory
  const entries = fs.readdirSync(source, { withFileTypes: true });
  
  // Copy each entry
  for (const entry of entries) {
    const sourcePath = path.join(source, entry.name);
    const destinationPath = path.join(destination, entry.name);
    
    if (entry.isDirectory()) {
      // Recursively copy directory
      copyDirectory(sourcePath, destinationPath);
    } else {
      // Copy file
      fs.copyFileSync(sourcePath, destinationPath);
    }
  }
}

// Run the report generator
generateCoverageReport();
