<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>4. UUFT Equation Flow</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: white;
        }
        .diagram-container {
            width: 1000px;
            height: 700px;
            position: relative;
            border: 2px solid black;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
        }
        .element {
            position: absolute;
            border-radius: 0; /* Square corners for patent diagrams */
            padding: 20px 20px 20px 50px; /* Extra padding on left for number */
            z-index: 2;
            background-color: white;
            border: 2px solid black;
        }
        .element-number {
            position: absolute;
            top: 50%;
            left: 15px;
            transform: translateY(-50%);
            background-color: black;
            color: white;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: bold;
            z-index: 3;
        }
        .connection {
            position: absolute;
            z-index: 1;
            background-color: black;
        }
        h1 {
            text-align: center;
            margin-bottom: 20px;
            font-size: 24px;
        }
        .bold-formula {
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>4. UUFT Equation Flow</h1>

    <div class="diagram-container">
        <!-- UUFT Equation -->
        <div class="element" style="top: 50px; left: 300px; width: 400px; font-weight: bold; font-size: 20px;">
            UUFT Equation: <span class="bold-formula">(A⊗B⊕C)×π10³</span>
            <div class="element-number">1</div>
        </div>

        <!-- Tensor A -->
        <div class="element" style="top: 150px; left: 100px; width: 200px; font-size: 14px;">
            Tensor A<br>Domain-specific input
            <div class="element-number">2</div>
        </div>

        <!-- Tensor B -->
        <div class="element" style="top: 150px; left: 350px; width: 200px; font-size: 14px;">
            Tensor B<br>Metadata input
            <div class="element-number">3</div>
        </div>

        <!-- Tensor C -->
        <div class="element" style="top: 150px; left: 600px; width: 200px; font-size: 14px;">
            Tensor C<br>Context information
            <div class="element-number">4</div>
        </div>

        <!-- π Factor -->
        <div class="element" style="top: 250px; left: 600px; width: 200px; font-size: 14px;">
            <span class="bold-formula">π10³</span> Factor<br>Circular trust topology
            <div class="element-number">5</div>
        </div>

        <!-- Tensor Product -->
        <div class="element" style="top: 250px; left: 100px; width: 200px; font-size: 14px;">
            Tensor Product <span class="bold-formula">(⊗)</span><br>Multi-dimensional relationships
            <div class="element-number">6</div>
        </div>

        <!-- Fusion Operator -->
        <div class="element" style="top: 350px; left: 350px; width: 200px; font-size: 14px;">
            Fusion Operator <span class="bold-formula">(⊕)</span><br>Merges related data points
            <div class="element-number">7</div>
        </div>

        <!-- Scaling Operation -->
        <div class="element" style="top: 450px; left: 600px; width: 200px; font-size: 14px;">
            Scaling Operation <span class="bold-formula">(×)</span><br>Applies trust topology factor
            <div class="element-number">8</div>
        </div>

        <!-- Result -->
        <div class="element" style="top: 550px; left: 350px; width: 300px; font-weight: bold; font-size: 14px;">
            Result<br>3,142x improvement<br>95% accuracy
            <div class="element-number">9</div>
        </div>

        <!-- Connections - direct lines to boxes -->
        <!-- UUFT to Tensor A -->
        <!-- Line removed as requested -->

        <!-- UUFT to Tensor B -->
        <div class="connection" style="top: 120px; left: 450px; width: 2px; height: 30px;"></div>

        <!-- UUFT to Tensor C -->
        <div class="connection" style="top: 120px; left: 700px; width: 2px; height: 30px;"></div>

        <!-- Tensor A to Tensor Product -->
        <div class="connection" style="top: 200px; left: 200px; width: 2px; height: 50px;"></div>

        <!-- Tensor B to Tensor Product -->
        <div class="connection" style="top: 200px; left: 350px; width: 150px; height: 2px;"></div>
        <div class="connection" style="top: 200px; left: 200px; width: 2px; height: 50px;"></div>

        <!-- Tensor Product to Fusion Operator -->
        <div class="connection" style="top: 300px; left: 200px; width: 150px; height: 2px;"></div>
        <div class="connection" style="top: 300px; left: 350px; width: 2px; height: 50px;"></div>

        <!-- Tensor C to Fusion Operator -->
        <div class="connection" style="top: 200px; left: 600px; width: 2px; height: 150px;"></div>
        <div class="connection" style="top: 350px; left: 550px; width: 50px; height: 2px;"></div>

        <!-- π Factor to Scaling Operation -->
        <div class="connection" style="top: 300px; left: 700px; width: 2px; height: 150px;"></div>

        <!-- Fusion Operator to Scaling Operation -->
        <div class="connection" style="top: 400px; left: 450px; width: 150px; height: 2px;"></div>
        <div class="connection" style="top: 400px; left: 600px; width: 2px; height: 50px;"></div>

        <!-- Scaling Operation to Result -->
        <div class="connection" style="top: 500px; left: 700px; width: 2px; height: 50px;"></div>
        <div class="connection" style="top: 550px; left: 650px; width: 50px; height: 2px;"></div>
    </div>
</body>
</html>
