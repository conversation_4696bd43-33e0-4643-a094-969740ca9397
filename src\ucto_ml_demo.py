"""
Demo script for the Enhanced Predictive Engine with Machine Learning integration.

This script demonstrates how to use the enhanced Predictive Engine to train
machine learning models and make predictions.
"""

import os
import sys
import json
import logging
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add the src directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Import the UCTO
from ucto import TrackingManager, PredictiveEngine

def generate_sample_data():
    """Generate sample compliance data for demonstration purposes."""
    logger.info("Generating sample compliance data")
    
    # Initialize the Tracking Manager
    tracking_manager = TrackingManager()
    
    # Generate sample requirements
    requirements = []
    
    # GDPR requirements
    requirements.append(tracking_manager.create_requirement({
        'name': 'Data Subject Rights',
        'description': 'Implement processes for handling data subject rights requests',
        'framework': 'GDPR',
        'category': 'privacy',
        'priority': 'high',
        'status': 'in_progress',
        'due_date': (datetime.now() + timedelta(days=15)).isoformat(),
        'assigned_to': 'privacy_officer',
        'tags': ['gdpr', 'data_subject_rights', 'privacy']
    }))
    
    requirements.append(tracking_manager.create_requirement({
        'name': 'Data Protection Impact Assessment',
        'description': 'Conduct data protection impact assessments for high-risk processing',
        'framework': 'GDPR',
        'category': 'risk_assessment',
        'priority': 'medium',
        'status': 'pending',
        'due_date': (datetime.now() + timedelta(days=45)).isoformat(),
        'assigned_to': 'privacy_officer',
        'tags': ['gdpr', 'dpia', 'risk_assessment']
    }))
    
    requirements.append(tracking_manager.create_requirement({
        'name': 'Data Breach Notification',
        'description': 'Implement processes for notifying authorities of data breaches',
        'framework': 'GDPR',
        'category': 'incident_response',
        'priority': 'high',
        'status': 'pending',
        'due_date': (datetime.now() + timedelta(days=10)).isoformat(),
        'assigned_to': 'security_officer',
        'tags': ['gdpr', 'breach_notification', 'incident_response']
    }))
    
    # SOC 2 requirements
    requirements.append(tracking_manager.create_requirement({
        'name': 'Access Control',
        'description': 'Implement access controls to restrict access to information assets',
        'framework': 'SOC 2',
        'category': 'access_control',
        'priority': 'high',
        'status': 'completed',
        'due_date': (datetime.now() - timedelta(days=15)).isoformat(),
        'assigned_to': 'security_officer',
        'tags': ['soc2', 'access_control', 'security']
    }))
    
    requirements.append(tracking_manager.create_requirement({
        'name': 'Risk Management',
        'description': 'Implement risk management processes to identify and mitigate risks',
        'framework': 'SOC 2',
        'category': 'risk_assessment',
        'priority': 'medium',
        'status': 'in_progress',
        'due_date': (datetime.now() + timedelta(days=30)).isoformat(),
        'assigned_to': 'risk_manager',
        'tags': ['soc2', 'risk_management', 'risk_assessment']
    }))
    
    requirements.append(tracking_manager.create_requirement({
        'name': 'Incident Response',
        'description': 'Implement incident response processes to detect and respond to security incidents',
        'framework': 'SOC 2',
        'category': 'incident_response',
        'priority': 'high',
        'status': 'in_progress',
        'due_date': (datetime.now() + timedelta(days=5)).isoformat(),
        'assigned_to': 'security_officer',
        'tags': ['soc2', 'incident_response', 'security']
    }))
    
    # Generate sample activities
    activities = []
    
    # Activities for Data Subject Rights
    activities.append(tracking_manager.create_activity({
        'name': 'Document Data Subject Rights Process',
        'description': 'Create documentation for handling data subject rights requests',
        'requirement_id': requirements[0]['id'],
        'type': 'documentation',
        'status': 'completed',
        'start_date': (datetime.now() - timedelta(days=15)).isoformat(),
        'end_date': (datetime.now() - timedelta(days=5)).isoformat(),
        'assigned_to': 'privacy_officer',
        'notes': 'Documentation completed and reviewed'
    }))
    
    activities.append(tracking_manager.create_activity({
        'name': 'Implement Data Subject Rights Portal',
        'description': 'Develop a portal for handling data subject rights requests',
        'requirement_id': requirements[0]['id'],
        'type': 'task',
        'status': 'in_progress',
        'start_date': (datetime.now() - timedelta(days=5)).isoformat(),
        'end_date': (datetime.now() + timedelta(days=10)).isoformat(),
        'assigned_to': 'developer',
        'notes': 'Portal development in progress'
    }))
    
    # Activities for Access Control
    activities.append(tracking_manager.create_activity({
        'name': 'Implement Role-Based Access Control',
        'description': 'Implement role-based access control for all systems',
        'requirement_id': requirements[3]['id'],
        'type': 'task',
        'status': 'completed',
        'start_date': (datetime.now() - timedelta(days=30)).isoformat(),
        'end_date': (datetime.now() - timedelta(days=15)).isoformat(),
        'assigned_to': 'security_engineer',
        'notes': 'RBAC implemented for all systems'
    }))
    
    activities.append(tracking_manager.create_activity({
        'name': 'Conduct Access Control Audit',
        'description': 'Audit access controls to ensure proper implementation',
        'requirement_id': requirements[3]['id'],
        'type': 'audit',
        'status': 'completed',
        'start_date': (datetime.now() - timedelta(days=15)).isoformat(),
        'end_date': (datetime.now() - timedelta(days=10)).isoformat(),
        'assigned_to': 'auditor',
        'notes': 'Audit completed with no findings'
    }))
    
    # Activities for Incident Response
    activities.append(tracking_manager.create_activity({
        'name': 'Develop Incident Response Plan',
        'description': 'Develop a comprehensive incident response plan',
        'requirement_id': requirements[5]['id'],
        'type': 'documentation',
        'status': 'completed',
        'start_date': (datetime.now() - timedelta(days=20)).isoformat(),
        'end_date': (datetime.now() - timedelta(days=10)).isoformat(),
        'assigned_to': 'security_officer',
        'notes': 'Incident response plan developed'
    }))
    
    activities.append(tracking_manager.create_activity({
        'name': 'Conduct Incident Response Training',
        'description': 'Train staff on incident response procedures',
        'requirement_id': requirements[5]['id'],
        'type': 'meeting',
        'status': 'in_progress',
        'start_date': (datetime.now() - timedelta(days=5)).isoformat(),
        'end_date': (datetime.now() + timedelta(days=2)).isoformat(),
        'assigned_to': 'trainer',
        'notes': 'Training sessions in progress'
    }))
    
    logger.info(f"Generated {len(requirements)} requirements and {len(activities)} activities")
    
    return requirements, activities

def generate_sample_labels(requirements):
    """Generate sample labels for compliance gap prediction."""
    logger.info("Generating sample labels for compliance gap prediction")
    
    # Generate labels (1 = compliance gap, 0 = no gap)
    labels = []
    
    for req in requirements:
        # High priority + pending status = likely gap
        if req.get('priority') == 'high' and req.get('status') == 'pending':
            labels.append(1)
        # Medium priority + pending status + short due date = likely gap
        elif req.get('priority') == 'medium' and req.get('status') == 'pending':
            due_date = datetime.fromisoformat(req.get('due_date'))
            days_until_due = (due_date - datetime.now()).days
            if days_until_due < 30:
                labels.append(1)
            else:
                labels.append(0)
        # Completed status = no gap
        elif req.get('status') == 'completed':
            labels.append(0)
        # In progress + high priority + short due date = likely gap
        elif req.get('status') == 'in_progress' and req.get('priority') == 'high':
            due_date = datetime.fromisoformat(req.get('due_date'))
            days_until_due = (due_date - datetime.now()).days
            if days_until_due < 15:
                labels.append(1)
            else:
                labels.append(0)
        # Default = no gap
        else:
            labels.append(0)
    
    logger.info(f"Generated {len(labels)} labels with {sum(labels)} positive cases")
    
    return labels

def demonstrate_ml_integration():
    """Demonstrate machine learning integration with the Predictive Engine."""
    logger.info("Demonstrating machine learning integration with the Predictive Engine")
    
    # Initialize the Predictive Engine
    output_dir = os.path.join(os.path.dirname(__file__), 'ml_output')
    os.makedirs(output_dir, exist_ok=True)
    
    predictive_engine = PredictiveEngine(data_dir=output_dir)
    
    # Generate sample data
    requirements, activities = generate_sample_data()
    
    # Generate sample labels
    labels = generate_sample_labels(requirements)
    
    # Step 1: Train a compliance gap prediction model
    logger.info("Step 1: Training a compliance gap prediction model")
    
    try:
        training_result = predictive_engine.train_ml_model(
            model_type='compliance_gap',
            requirements=requirements,
            activities=activities,
            labels=labels,
            model_config={
                'model_algorithm': 'random_forest',
                'model_params': {
                    'n_estimators': 100,
                    'max_depth': 5,
                    'random_state': 42
                }
            }
        )
        
        # Save the training result to a file
        with open(os.path.join(output_dir, 'training_result.json'), 'w', encoding='utf-8') as f:
            # Convert any non-serializable values to strings
            result_copy = {}
            for key, value in training_result.items():
                if isinstance(value, (list, dict, str, int, float, bool, type(None))):
                    result_copy[key] = value
                else:
                    result_copy[key] = str(value)
            
            json.dump(result_copy, f, indent=2)
        
        logger.info(f"Saved training result to {os.path.join(output_dir, 'training_result.json')}")
        
        # Step 2: Make predictions with the trained model
        logger.info("Step 2: Making predictions with the trained model")
        
        model_id = training_result['model_id']
        
        prediction_result = predictive_engine.predict_with_ml_model(
            model_id=model_id,
            requirements=requirements,
            activities=activities
        )
        
        # Save the prediction result to a file
        with open(os.path.join(output_dir, 'prediction_result.json'), 'w', encoding='utf-8') as f:
            json.dump(prediction_result, f, indent=2)
        
        logger.info(f"Saved prediction result to {os.path.join(output_dir, 'prediction_result.json')}")
        
        # Step 3: Generate a summary report
        logger.info("Step 3: Generating a summary report")
        
        # Count correct predictions
        correct_predictions = 0
        for i, pred in enumerate(prediction_result['predictions']):
            if pred['prediction'] == labels[i]:
                correct_predictions += 1
        
        accuracy = correct_predictions / len(labels)
        
        summary_report = {
            'timestamp': datetime.now().isoformat(),
            'model_id': model_id,
            'model_type': training_result['model_type'],
            'algorithm': training_result['algorithm'],
            'training_data_size': training_result['training_data_size'],
            'metrics': training_result.get('metrics', {}),
            'prediction_accuracy': accuracy,
            'correct_predictions': correct_predictions,
            'total_predictions': len(labels)
        }
        
        # Save the summary report to a file
        with open(os.path.join(output_dir, 'summary_report.json'), 'w', encoding='utf-8') as f:
            json.dump(summary_report, f, indent=2)
        
        logger.info(f"Saved summary report to {os.path.join(output_dir, 'summary_report.json')}")
        
        return summary_report
    
    except Exception as e:
        logger.error(f"Failed to demonstrate ML integration: {e}")
        raise

def main():
    """Main function."""
    logger.info("Starting Enhanced Predictive Engine with ML integration demo")
    
    try:
        # Demonstrate ML integration
        summary_report = demonstrate_ml_integration()
        
        logger.info("Enhanced Predictive Engine with ML integration demo completed successfully")
        logger.info(f"Summary report: {summary_report}")
        logger.info(f"All output files are in: {os.path.join(os.path.dirname(os.path.abspath(__file__)), 'ml_output')}")
    
    except Exception as e:
        logger.error(f"Demo failed: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
