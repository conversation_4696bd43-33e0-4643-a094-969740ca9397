import type { Metadata } from 'next'
import './globals.css'

export const metadata: Metadata = {
  title: 'NovaBrowser - Coherence-First Web Gateway',
  description: 'Experience the future of consciousness-driven web browsing with real-time coherence analysis',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className="antialiased">
        {children}
      </body>
    </html>
  )
}
