/**
 * Enhanced Quantum State Inference with Comphyology Test
 * 
 * This script tests the enhanced Quantum State Inference implementation, demonstrating
 * how Comphyology concepts significantly improve threat detection capabilities.
 */

const fs = require('fs');
const path = require('path');
const AdvancedComphyologyQuantumInference = require('../../src/comphyology/quantum_inference_enhanced');
const ComphyologyEnhancedQuantumStateInference = require('../../src/comphyology/quantum_integration');

// Create results directory
const RESULTS_DIR = path.join(__dirname, '../../comphyology_results');
if (!fs.existsSync(RESULTS_DIR)) {
  fs.mkdirSync(RESULTS_DIR, { recursive: true });
}

/**
 * Generate test detection data
 * 
 * @param {number} threatLevel - Threat level (0-1)
 * @param {number} noiseFactor - Noise factor (0-1)
 * @returns {Object} - Test detection data
 */
function generateTestDetectionData(threatLevel, noiseFactor) {
  // Calculate signal-to-noise ratio
  const signalStrength = threatLevel;
  const noiseStrength = noiseFactor;
  
  // Create detection systems with varying effectiveness
  const detectionSystems = {
    firewall: { 
      effectiveness: 0.9 - (noiseStrength * 0.2), 
      coverage: 0.95 
    },
    ids: { 
      effectiveness: 0.8 - (noiseStrength * 0.3), 
      coverage: 0.85 
    },
    siem: { 
      effectiveness: 0.7 - (noiseStrength * 0.2), 
      coverage: 0.8 
    },
    endpoint: { 
      effectiveness: 0.6 - (noiseStrength * 0.1), 
      coverage: 0.75 
    }
  };
  
  // Create threats with varying severity and confidence
  const threats = {
    malware: { 
      severity: Math.min(1, Math.max(0, 0.9 * signalStrength + (Math.random() - 0.5) * noiseStrength)), 
      confidence: Math.min(1, Math.max(0, 0.8 * signalStrength + (Math.random() - 0.5) * noiseStrength)) 
    },
    phishing: { 
      severity: Math.min(1, Math.max(0, 0.8 * signalStrength + (Math.random() - 0.5) * noiseStrength)), 
      confidence: Math.min(1, Math.max(0, 0.9 * signalStrength + (Math.random() - 0.5) * noiseStrength)) 
    },
    ddos: { 
      severity: Math.min(1, Math.max(0, 0.7 * signalStrength + (Math.random() - 0.5) * noiseStrength)), 
      confidence: Math.min(1, Math.max(0, 0.7 * signalStrength + (Math.random() - 0.5) * noiseStrength)) 
    },
    insider: { 
      severity: Math.min(1, Math.max(0, 0.6 * signalStrength + (Math.random() - 0.5) * noiseStrength)), 
      confidence: Math.min(1, Math.max(0, 0.5 * signalStrength + (Math.random() - 0.5) * noiseStrength)) 
    }
  };
  
  return {
    detectionCapability: 0.75,
    threatSeverity: signalStrength,
    threatConfidence: signalStrength * 0.9,
    detectionSystems,
    threats
  };
}

/**
 * Generate test context data
 * 
 * @param {boolean} isThreat - Whether this is an actual threat
 * @returns {Object} - Test context data
 */
function generateTestContextData(isThreat) {
  return {
    organization: {
      industry: 'finance',
      size: 'large',
      riskTolerance: 'low'
    },
    environment: {
      timeOfDay: 'business_hours',
      dayOfWeek: 'weekday',
      location: 'headquarters'
    },
    user: {
      role: 'admin',
      department: 'it',
      riskScore: 0.2
    },
    actualThreatStatus: isThreat,
    actualThreats: isThreat ? ['malware', 'phishing'] : []
  };
}

/**
 * Test basic vs. enhanced quantum inference
 */
function testBasicVsEnhanced() {
  console.log('\n=== Testing Basic vs. Enhanced Quantum Inference ===');
  
  // Initialize inference engines
  const basicInference = new ComphyologyEnhancedQuantumStateInference({ enableLogging: true });
  const enhancedInference = new AdvancedComphyologyQuantumInference({ enableLogging: true });
  
  // Test scenarios
  const scenarios = [
    { name: 'High Signal, Low Noise', threatLevel: 0.9, noiseFactor: 0.1, isThreat: true },
    { name: 'Medium Signal, Medium Noise', threatLevel: 0.6, noiseFactor: 0.4, isThreat: true },
    { name: 'Low Signal, High Noise', threatLevel: 0.3, noiseFactor: 0.7, isThreat: true },
    { name: 'Very Low Signal, High Noise', threatLevel: 0.2, noiseFactor: 0.8, isThreat: true },
    { name: 'No Signal, Low Noise', threatLevel: 0.0, noiseFactor: 0.2, isThreat: false },
    { name: 'No Signal, High Noise', threatLevel: 0.0, noiseFactor: 0.7, isThreat: false }
  ];
  
  // Results table
  const results = [];
  
  // Run tests for each scenario
  for (const scenario of scenarios) {
    console.log(`\nScenario: ${scenario.name}`);
    console.log(`Threat Level: ${scenario.threatLevel.toFixed(2)}, Noise Factor: ${scenario.noiseFactor.toFixed(2)}, Actual Threat: ${scenario.isThreat}`);
    
    // Generate test data
    const detectionData = generateTestDetectionData(scenario.threatLevel, scenario.noiseFactor);
    const contextData = generateTestContextData(scenario.isThreat);
    
    // Process with basic inference
    const basicResult = basicInference.processDetection(detectionData, contextData);
    
    // Process with enhanced inference
    const enhancedResult = enhancedInference.processDetection(detectionData, contextData);
    
    // Print results
    console.log('\nBasic Inference:');
    console.log(`  Threat Detected: ${basicResult.actionableIntelligence.threatDetected}`);
    console.log(`  Threat Severity: ${basicResult.actionableIntelligence.threatSeverity}`);
    console.log(`  Confidence: ${basicResult.actionableIntelligence.confidence.toFixed(4)}`);
    console.log(`  Certainty Rate: ${basicResult.certaintyRate.toFixed(4)}`);
    console.log(`  Recommended Actions: ${basicResult.actionableIntelligence.recommendedActions.join(', ')}`);
    
    console.log('\nEnhanced Inference:');
    console.log(`  Threat Detected: ${enhancedResult.actionableIntelligence.threatDetected}`);
    console.log(`  Threat Severity: ${enhancedResult.actionableIntelligence.threatSeverity}`);
    console.log(`  Confidence: ${enhancedResult.actionableIntelligence.confidence.toFixed(4)}`);
    console.log(`  Certainty Rate: ${enhancedResult.certaintyRate.toFixed(4)}`);
    console.log(`  Recommended Actions: ${enhancedResult.actionableIntelligence.recommendedActions.join(', ')}`);
    
    // Print enhancement details
    if (enhancedResult.harmonicMapping) {
      console.log('\nHarmonic Mapping:');
      console.log(`  Average Resonance Strength: ${enhancedResult.harmonicMapping.avgResonanceStrength.toFixed(4)}`);
      console.log(`  Certainty Boost: ${enhancedResult.harmonicMapping.certaintyBoost.toFixed(4)}`);
    }
    
    if (enhancedResult.tensorCollapse) {
      console.log('\nTensor Collapse:');
      console.log(`  Original Collapsed Count: ${enhancedResult.tensorCollapse.originalCollapsedCount}`);
      console.log(`  Tensor Collapsed Count: ${enhancedResult.tensorCollapse.tensorCollapsedCount}`);
      console.log(`  Collapse Difference: ${enhancedResult.tensorCollapse.collapseDifference}`);
    }
    
    if (enhancedResult.ethicalBoundaries) {
      console.log('\nEthical Boundaries:');
      console.log(`  Original Action Count: ${enhancedResult.ethicalBoundaries.originalActionCount}`);
      console.log(`  Permissible Action Count: ${enhancedResult.ethicalBoundaries.permissibleActionCount}`);
      console.log(`  Rejected Action Count: ${enhancedResult.ethicalBoundaries.rejectedActionCount}`);
    }
    
    // Calculate accuracy
    const basicAccuracy = basicResult.actionableIntelligence.threatDetected === scenario.isThreat ? 1 : 0;
    const enhancedAccuracy = enhancedResult.actionableIntelligence.threatDetected === scenario.isThreat ? 1 : 0;
    
    // Add to results table
    results.push({
      scenario: scenario.name,
      threatLevel: scenario.threatLevel,
      noiseFactor: scenario.noiseFactor,
      isThreat: scenario.isThreat,
      basicDetected: basicResult.actionableIntelligence.threatDetected,
      basicCertainty: basicResult.certaintyRate,
      basicAccuracy,
      enhancedDetected: enhancedResult.actionableIntelligence.threatDetected,
      enhancedCertainty: enhancedResult.certaintyRate,
      enhancedAccuracy,
      certaintyImprovement: enhancedResult.certaintyRate - basicResult.certaintyRate,
      accuracyImprovement: enhancedAccuracy - basicAccuracy
    });
  }
  
  // Calculate overall results
  const totalScenarios = results.length;
  const basicAccuracyAvg = results.reduce((sum, result) => sum + result.basicAccuracy, 0) / totalScenarios;
  const enhancedAccuracyAvg = results.reduce((sum, result) => sum + result.enhancedAccuracy, 0) / totalScenarios;
  const basicCertaintyAvg = results.reduce((sum, result) => sum + result.basicCertainty, 0) / totalScenarios;
  const enhancedCertaintyAvg = results.reduce((sum, result) => sum + result.enhancedCertainty, 0) / totalScenarios;
  const certaintyImprovementAvg = results.reduce((sum, result) => sum + result.certaintyImprovement, 0) / totalScenarios;
  const accuracyImprovementAvg = enhancedAccuracyAvg - basicAccuracyAvg;
  
  // Print overall results
  console.log('\n=== Overall Results ===');
  console.log(`Basic Accuracy: ${(basicAccuracyAvg * 100).toFixed(2)}%`);
  console.log(`Enhanced Accuracy: ${(enhancedAccuracyAvg * 100).toFixed(2)}%`);
  console.log(`Accuracy Improvement: ${(accuracyImprovementAvg * 100).toFixed(2)}%`);
  console.log(`Basic Certainty: ${basicCertaintyAvg.toFixed(4)}`);
  console.log(`Enhanced Certainty: ${enhancedCertaintyAvg.toFixed(4)}`);
  console.log(`Certainty Improvement: ${certaintyImprovementAvg.toFixed(4)} (${(certaintyImprovementAvg / basicCertaintyAvg * 100).toFixed(2)}%)`);
  
  // Save results to file
  const resultFile = path.join(RESULTS_DIR, `quantum_inference_comparison_${new Date().toISOString().replace(/:/g, '-')}.json`);
  fs.writeFileSync(resultFile, JSON.stringify({
    scenarios: results,
    summary: {
      totalScenarios,
      basicAccuracyAvg,
      enhancedAccuracyAvg,
      basicCertaintyAvg,
      enhancedCertaintyAvg,
      certaintyImprovementAvg,
      accuracyImprovementAvg
    }
  }, null, 2));
  
  console.log(`\nResults saved to ${resultFile}`);
}

/**
 * Test adaptive learning capabilities
 */
function testAdaptiveLearning() {
  console.log('\n=== Testing Adaptive Learning Capabilities ===');
  
  // Initialize enhanced inference engine
  const enhancedInference = new AdvancedComphyologyQuantumInference({ enableLogging: true });
  
  // Generate test sequence with evolving threat
  const iterations = 10;
  const results = [];
  
  console.log(`Running ${iterations} iterations of adaptive learning...`);
  
  for (let i = 0; i < iterations; i++) {
    // Evolving threat: starts weak, gets stronger, then weakens
    const threatCycle = Math.sin((i / iterations) * Math.PI);
    const threatLevel = 0.3 + threatCycle * 0.4; // Range from 0.3 to 0.7
    const noiseFactor = 0.5 - threatCycle * 0.3; // Range from 0.5 to 0.2
    
    // Generate test data
    const detectionData = generateTestDetectionData(threatLevel, noiseFactor);
    const contextData = generateTestContextData(threatLevel > 0.4); // Actual threat if level > 0.4
    
    // Process with enhanced inference
    const result = enhancedInference.processDetection(detectionData, contextData);
    
    // Track results
    results.push({
      iteration: i + 1,
      threatLevel,
      noiseFactor,
      actualThreat: contextData.actualThreatStatus,
      detectedThreat: result.actionableIntelligence.threatDetected,
      certaintyRate: result.certaintyRate,
      accuracy: result.actionableIntelligence.threatDetected === contextData.actualThreatStatus ? 1 : 0
    });
    
    // Print progress
    if ((i + 1) % 5 === 0 || i === 0) {
      console.log(`\nIteration ${i + 1}:`);
      console.log(`  Threat Level: ${threatLevel.toFixed(2)}, Noise Factor: ${noiseFactor.toFixed(2)}`);
      console.log(`  Actual Threat: ${contextData.actualThreatStatus}, Detected Threat: ${result.actionableIntelligence.threatDetected}`);
      console.log(`  Certainty Rate: ${result.certaintyRate.toFixed(4)}`);
    }
  }
  
  // Get performance metrics
  const metrics = enhancedInference.getPerformanceMetrics();
  
  // Print performance metrics
  console.log('\n=== Performance Metrics ===');
  console.log(`Total Inferences: ${metrics.totalInferences}`);
  console.log(`True Positives: ${metrics.truePositives}`);
  console.log(`False Positives: ${metrics.falsePositives}`);
  console.log(`True Negatives: ${metrics.trueNegatives}`);
  console.log(`False Negatives: ${metrics.falseNegatives}`);
  console.log(`Average Certainty Rate: ${metrics.averageCertaintyRate.toFixed(4)}`);
  console.log(`Adaptation Count: ${metrics.adaptationCount}`);
  
  if (metrics.precision !== undefined) {
    console.log(`Precision: ${metrics.precision.toFixed(4)}`);
  }
  if (metrics.recall !== undefined) {
    console.log(`Recall: ${metrics.recall.toFixed(4)}`);
  }
  if (metrics.f1Score !== undefined) {
    console.log(`F1 Score: ${metrics.f1Score.toFixed(4)}`);
  }
  if (metrics.accuracy !== undefined) {
    console.log(`Accuracy: ${metrics.accuracy.toFixed(4)}`);
  }
  
  // Calculate learning curve
  const initialAccuracy = results.slice(0, 3).reduce((sum, r) => sum + r.accuracy, 0) / 3;
  const finalAccuracy = results.slice(-3).reduce((sum, r) => sum + r.accuracy, 0) / 3;
  const accuracyImprovement = finalAccuracy - initialAccuracy;
  
  const initialCertainty = results.slice(0, 3).reduce((sum, r) => sum + r.certaintyRate, 0) / 3;
  const finalCertainty = results.slice(-3).reduce((sum, r) => sum + r.certaintyRate, 0) / 3;
  const certaintyImprovement = finalCertainty - initialCertainty;
  
  console.log('\n=== Learning Curve ===');
  console.log(`Initial Accuracy (first 3): ${(initialAccuracy * 100).toFixed(2)}%`);
  console.log(`Final Accuracy (last 3): ${(finalAccuracy * 100).toFixed(2)}%`);
  console.log(`Accuracy Improvement: ${(accuracyImprovement * 100).toFixed(2)}%`);
  console.log(`Initial Certainty (first 3): ${initialCertainty.toFixed(4)}`);
  console.log(`Final Certainty (last 3): ${finalCertainty.toFixed(4)}`);
  console.log(`Certainty Improvement: ${certaintyImprovement.toFixed(4)} (${(certaintyImprovement / initialCertainty * 100).toFixed(2)}%)`);
  
  // Save results to file
  const resultFile = path.join(RESULTS_DIR, `adaptive_learning_results_${new Date().toISOString().replace(/:/g, '-')}.json`);
  fs.writeFileSync(resultFile, JSON.stringify({
    iterations: results,
    metrics,
    learningCurve: {
      initialAccuracy,
      finalAccuracy,
      accuracyImprovement,
      initialCertainty,
      finalCertainty,
      certaintyImprovement
    }
  }, null, 2));
  
  console.log(`\nResults saved to ${resultFile}`);
}

/**
 * Main test function
 */
function main() {
  console.log('=== Enhanced Quantum State Inference with Comphyology Test ===');
  
  // Run tests
  testBasicVsEnhanced();
  testAdaptiveLearning();
  
  console.log('\n=== Test Complete ===');
  console.log('All results have been saved to the comphyology_results directory.');
}

// Run the tests
main();
