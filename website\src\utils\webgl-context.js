/**
 * WebGL Context Manager
 * Handles WebGL initialization, resource management, and rendering
 */

export class WebGLContext {
  constructor(canvas, options = {}) {
    this.canvas = canvas;
    this.options = {
      alpha: true,
      antialias: true,
      depth: true,
      stencil: false,
      premultipliedAlpha: false,
      preserveDrawingBuffer: false,
      powerPreference: 'high-performance',
      ...options
    };

    this.gl = null;
    this.extensions = new Map();
    this.programs = new Map();
    this.buffers = new Map();
    this.textures = new Map();
    this.frameCount = 0;
    this.lastFrameTime = 0;
    this.fps = 0;
    this.isContextLost = false;

    this.initialize();
  }

  /**
   * Initialize WebGL context
   */
  initialize() {
    try {
      // Try to get WebGL2 context first
      this.gl = this.canvas.getContext('webgl2', this.options) || 
                this.canvas.getContext('experimental-webgl2', this.options);
      
      // Fall back to WebGL1 if WebGL2 is not available
      if (!this.gl) {
        this.gl = this.canvas.getContext('webgl', this.options) || 
                  this.canvas.getContext('experimental-webgl', this.options);
      }

      if (!this.gl) {
        throw new Error('WebGL is not supported in this browser');
      }

      // Set up context lost/restored handlers
      this.setupEventHandlers();
      
      // Load required extensions
      this.loadExtensions([
        'EXT_color_buffer_float',
        'OES_texture_float',
        'OES_texture_float_linear',
        'OES_element_index_uint',
        'WEBGL_compressed_texture_astc',
        'WEBGL_compressed_texture_etc',
        'WEBGL_compressed_texture_s3tc',
        'EXT_disjoint_timer_query_webgl2',
        'KHR_parallel_shader_compile'
      ]);

      // Set initial GL state
      this.setDefaultState();
      
      console.log('WebGL initialized successfully');
      console.log(`Renderer: ${this.gl.getParameter(this.gl.RENDERER)}`);
      console.log(`Vendor: ${this.gl.getParameter(this.gl.VENDOR)}`);
      console.log(`Version: ${this.gl.getParameter(this.gl.VERSION)}`);
      
    } catch (error) {
      console.error('Failed to initialize WebGL:', error);
      throw error;
    }
  }

  /**
   * Set up event handlers for the WebGL context
   */
  setupEventHandlers() {
    this.canvas.addEventListener('webglcontextlost', (event) => {
      console.warn('WebGL context lost');
      this.isContextLost = true;
      event.preventDefault();
    }, false);

    this.canvas.addEventListener('webglcontextrestored', () => {
      console.log('WebGL context restored');
      this.isContextLost = false;
      this.setDefaultState();
    }, false);

    // Handle resize
    window.addEventListener('resize', this.handleResize.bind(this));
    this.handleResize();
  }

  /**
   * Handle canvas resize
   */
  handleResize() {
    const dpr = window.devicePixelRatio || 1;
    const width = this.canvas.clientWidth * dpr;
    const height = this.canvas.clientHeight * dpr;

    // Only resize if the canvas size has changed
    if (this.canvas.width !== width || this.canvas.height !== height) {
      this.canvas.width = width;
      this.canvas.height = height;
      this.gl.viewport(0, 0, width, height);
      
      // Notify any listeners
      if (this.onResize) {
        this.onResize(width, height, dpr);
      }
    }
  }

  /**
   * Set default WebGL state
   */
  setDefaultState() {
    const gl = this.gl;
    
    // Clear color (transparent black)
    gl.clearColor(0, 0, 0, 0);
    
    // Enable depth testing
    gl.enable(gl.DEPTH_TEST);
    gl.depthFunc(gl.LEQUAL);
    
    // Enable backface culling
    gl.enable(gl.CULL_FACE);
    gl.cullFace(gl.BACK);
    
    // Enable blending
    gl.enable(gl.BLEND);
    gl.blendFunc(gl.SRC_ALPHA, gl.ONE_MINUS_SRC_ALPHA);
    
    // Set viewport
    gl.viewport(0, 0, this.canvas.width, this.canvas.height);
  }

  /**
   * Load required WebGL extensions
   */
  loadExtensions(extensions) {
    const gl = this.gl;
    
    for (const extName of extensions) {
      try {
        const ext = gl.getExtension(extName);
        if (ext) {
          this.extensions.set(extName, ext);
          console.log(`Loaded extension: ${extName}`);
        } else {
          console.warn(`Could not load extension: ${extName}`);
        }
      } catch (error) {
        console.error(`Error loading extension ${extName}:`, error);
      }
    }
  }

  /**
   * Get a loaded extension by name
   */
  getExtension(name) {
    return this.extensions.get(name);
  }

  /**
   * Create a shader program
   */
  createProgram(vertexSource, fragmentSource, transformFeedbackVaryings = null) {
    const gl = this.gl;
    
    // Compile shaders
    const vertexShader = this.compileShader(gl.VERTEX_SHADER, vertexSource);
    const fragmentShader = this.compileShader(gl.FRAGMENT_SHADER, fragmentSource);
    
    // Create and link program
    const program = gl.createProgram();
    gl.attachShader(program, vertexShader);
    gl.attachShader(program, fragmentShader);
    
    // Set up transform feedback if needed
    if (transformFeedbackVaryings && transformFeedbackVaryings.length > 0) {
      if (typeof gl.transformFeedbackVaryings === 'function') {
        gl.transformFeedbackVaryings(
          program,
          transformFeedbackVaryings,
          gl.SEPARATE_ATTRIBS
        );
      } else {
        console.warn('Transform feedback not supported in this context');
      }
    }
    
    // Link program
    gl.linkProgram(program);
    
    // Check for linking errors
    if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
      const info = gl.getProgramInfoLog(program);
      gl.deleteProgram(program);
      throw new Error(`Failed to link shader program: ${info}`);
    }
    
    // Clean up shaders
    gl.detachShader(program, vertexShader);
    gl.detachShader(program, fragmentShader);
    gl.deleteShader(vertexShader);
    gl.deleteShader(fragmentShader);
    
    // Store program for cleanup
    const programId = `program_${this.programs.size}`;
    this.programs.set(programId, program);
    
    return {
      id: programId,
      program,
      use: () => gl.useProgram(program),
      getUniformLocation: (name) => gl.getUniformLocation(program, name),
      getAttribLocation: (name) => gl.getAttribLocation(program, name),
      setUniform: (location, type, ...values) => {
        const method = `uniform${type}`;
        if (gl[method]) {
          gl[method](location, ...values);
        } else {
          console.warn(`Uniform type ${type} not supported`);
        }
      }
    };
  }

  /**
   * Compile a shader
   */
  compileShader(type, source) {
    const gl = this.gl;
    const shader = gl.createShader(type);
    
    gl.shaderSource(shader, source);
    gl.compileShader(shader);
    
    // Check for compilation errors
    if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
      const info = gl.getShaderInfoLog(shader);
      gl.deleteShader(shader);
      throw new Error(`Failed to compile shader: ${info}`);
    }
    
    return shader;
  }

  /**
   * Create a buffer
   */
  createBuffer(target, data, usage) {
    const gl = this.gl;
    const buffer = gl.createBuffer();
    
    gl.bindBuffer(target, buffer);
    gl.bufferData(target, data, usage || gl.STATIC_DRAW);
    
    // Store buffer for cleanup
    const bufferId = `buffer_${this.buffers.size}`;
    this.buffers.set(bufferId, { buffer, target });
    
    return {
      id: bufferId,
      buffer,
      bind: () => gl.bindBuffer(target, buffer),
      update: (data, offset = 0) => {
        gl.bindBuffer(target, buffer);
        gl.bufferSubData(target, offset, data);
      }
    };
  }

  /**
   * Create a texture
   */
  createTexture(options = {}) {
    const gl = this.gl;
    const texture = gl.createTexture();
    const {
      width = 1,
      height = 1,
      format = gl.RGBA,
      internalFormat = format,
      type = gl.UNSIGNED_BYTE,
      data = null,
      minFilter = gl.LINEAR,
      magFilter = gl.LINEAR,
      wrapS = gl.CLAMP_TO_EDGE,
      wrapT = gl.CLAMP_TO_EDGE,
      generateMipmaps = false,
      flipY = false,
      anisotropy = 1
    } = options;
    
    gl.bindTexture(gl.TEXTURE_2D, texture);
    
    // Set texture parameters
    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, minFilter);
    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MAG_FILTER, magFilter);
    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_S, wrapS);
    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_T, wrapT);
    
    // Set anisotropy if supported
    const ext = this.getExtension('EXT_texture_filter_anisotropic') ||
                this.getExtension('MOZ_EXT_texture_filter_anisotropic') ||
                this.getExtension('WEBKIT_EXT_texture_filter_anisotropic');
    
    if (ext) {
      const maxAnisotropy = Math.min(
        anisotropy,
        gl.getParameter(ext.MAX_TEXTURE_MAX_ANISOTROPY_EXT)
      );
      gl.texParameterf(gl.TEXTURE_2D, ext.TEXTURE_MAX_ANISOTROPY_EXT, maxAnisotropy);
    }
    
    // Set pixel store parameters
    gl.pixelStorei(gl.UNPACK_FLIP_Y_WEBGL, flipY);
    gl.pixelStorei(gl.UNPACK_PREMULTIPLY_ALPHA_WEBGL, false);
    
    // Allocate texture storage
    gl.texImage2D(
      gl.TEXTURE_2D,
      0,
      internalFormat,
      width,
      height,
      0,
      format,
      type,
      data
    );
    
    // Generate mipmaps if requested
    if (generateMipmaps) {
      gl.generateMipmap(gl.TEXTURE_2D);
    }
    
    // Store texture for cleanup
    const textureId = `texture_${this.textures.size}`;
    this.textures.set(textureId, texture);
    
    return {
      id: textureId,
      texture,
      bind: (unit = 0) => {
        gl.activeTexture(gl.TEXTURE0 + unit);
        gl.bindTexture(gl.TEXTURE_2D, texture);
      },
      update: (data, x = 0, y = 0, w = width, h = height) => {
        gl.bindTexture(gl.TEXTURE_2D, texture);
        gl.texSubImage2D(gl.TEXTURE_2D, 0, x, y, w, h, format, type, data);
      }
    };
  }

  /**
   * Begin a new frame
   */
  beginFrame() {
    if (this.isContextLost) return false;
    
    const gl = this.gl;
    const now = performance.now();
    
    // Calculate FPS
    if (this.lastFrameTime > 0) {
      const delta = now - this.lastFrameTime;
      this.fps = 1000 / delta;
    }
    this.lastFrameTime = now;
    this.frameCount++;
    
    // Clear the canvas
    gl.clear(gl.COLOR_BUFFER_BIT | gl.DEPTH_BUFFER_BIT);
    
    return true;
  }

  /**
   * End the current frame
   */
  endFrame() {
    // In a real implementation, this might handle things like:
    // - Flushing the command buffer
    // - Handling readbacks
    // - Updating performance metrics
    
    // For now, we'll just ensure the default framebuffer is bound
    this.gl.bindFramebuffer(this.gl.FRAMEBUFFER, null);
  }

  /**
   * Clean up resources
   */
  dispose() {
    const gl = this.gl;
    
    // Clean up programs
    this.programs.forEach(program => {
      gl.deleteProgram(program);
    });
    this.programs.clear();
    
    // Clean up buffers
    this.buffers.forEach(({ buffer }) => {
      gl.deleteBuffer(buffer);
    });
    this.buffers.clear();
    
    // Clean up textures
    this.textures.forEach(texture => {
      gl.deleteTexture(texture);
    });
    this.textures.clear();
    
    // Remove event listeners
    window.removeEventListener('resize', this.handleResize);
    
    // Lose the context
    if (gl.getExtension('WEBGL_lose_context')) {
      gl.getExtension('WEBGL_lose_context').loseContext();
    }
    
    console.log('WebGL context disposed');
  }
}

// Example usage:
/*
async function init() {
  const canvas = document.getElementById('gl-canvas');
  const glContext = new WebGLContext(canvas, {
    antialias: true,
    alpha: true,
    powerPreference: 'high-performance'
  });
  
  // Create a simple shader program
  const vs = `
    attribute vec2 position;
    void main() {
      gl_Position = vec4(position, 0.0, 1.0);
    }
  `;
  
  const fs = `
    precision highp float;
    void main() {
      gl_FragColor = vec4(1.0, 0.5, 0.2, 1.0);
    }
  `;
  
  const program = glContext.createProgram(vs, fs);
  
  // Create a triangle
  const vertices = new Float32Array([
    0.0,  0.5,
    -0.5, -0.5,
    0.5, -0.5
  ]);
  
  const buffer = glContext.createBuffer(
    glContext.gl.ARRAY_BUFFER,
    vertices,
    glContext.gl.STATIC_DRAW
  );
  
  // Render loop
  function render() {
    if (!glContext.beginFrame()) return;
    
    const gl = glContext.gl;
    
    // Clear the canvas
    gl.clearColor(0.1, 0.1, 0.1, 1.0);
    gl.clear(gl.COLOR_BUFFER_BIT | gl.DEPTH_BUFFER_BIT);
    
    // Use the shader program
    program.use();
    
    // Set up vertex attributes
    buffer.bind();
    const positionLocation = program.getAttribLocation('position');
    gl.enableVertexAttribArray(positionLocation);
    gl.vertexAttribPointer(positionLocation, 2, gl.FLOAT, false, 0, 0);
    
    // Draw the triangle
    gl.drawArrays(gl.TRIANGLES, 0, 3);
    
    // End the frame
    glContext.endFrame();
    
    // Request the next frame
    requestAnimationFrame(render);
  }
  
  // Start the render loop
  requestAnimationFrame(render);
  
  // Clean up when done
  // glContext.dispose();
}
*/

export default WebGLContext;
