# NovaCore: The Genesis Pairing

NovaCore is the foundational integration of NovaSphere (Evidence Collection System) and NovaConnect (Universal API Connector) in the NovaFuse ecosystem.

## Overview

NovaCore serves as the backbone of the NovaFuse platform, providing:

1. **Evidence Collection & Management**: Automated collection, verification, and management of compliance evidence.
2. **Blockchain Verification**: Tamper-proof verification of evidence integrity and timestamps.
3. **Data Source Integration**: Seamless integration with various data sources through NovaConnect.
4. **API-First Architecture**: Comprehensive API for integration with other NovaFuse components.

## Components

NovaCore consists of the following key components:

### Models

- **Evidence**: Core data model for compliance evidence.
- **BlockchainVerification**: Model for blockchain verification of evidence.
- **Connector**: Model for data source connectors.
- **CollectionJob**: Model for evidence collection jobs.
- **Requirement**: Model for compliance requirements.

### Services

- **EvidenceService**: Service for managing evidence.
- **BlockchainService**: Service for blockchain verification.
- **ConnectorService**: Service for managing data source connectors.

### API

- **Evidence API**: Endpoints for evidence management.
- **Connector API**: Endpoints for connector management.
- **Job API**: Endpoints for collection job management.

## Getting Started

### Prerequisites

- Node.js 14+
- TypeScript 4.5+

### Installation

1. Clone the repository
2. Install dependencies:

```bash
cd src/novacore
npm install
```

### Running the Server

```bash
npm run dev
```

The server will start on port 5000 by default. You can access the API at `http://localhost:5000/api`.

## API Documentation

### Evidence API

- `GET /api/evidence`: Get all evidence with optional filtering
- `GET /api/evidence/:id`: Get evidence by ID
- `POST /api/evidence`: Create new evidence
- `PUT /api/evidence/:id`: Update evidence
- `DELETE /api/evidence/:id`: Delete evidence
- `POST /api/evidence/:id/versions`: Create new version of evidence
- `GET /api/evidence/:id/versions`: Get all versions of evidence
- `GET /api/evidence/:id/versions/:versionId`: Get specific version of evidence
- `PUT /api/evidence/:id/status`: Update evidence status
- `POST /api/evidence/:id/verify`: Verify evidence on blockchain
- `POST /api/evidence/:id/versions/:versionId/verify`: Verify specific version of evidence on blockchain
- `POST /api/evidence/:id/requirements/:requirementId`: Link evidence to requirement
- `DELETE /api/evidence/:id/requirements/:requirementId`: Unlink evidence from requirement
- `GET /api/evidence/:id/requirements`: Get requirements linked to evidence

### Connector API

- `GET /api/connectors`: Get all connectors with optional filtering
- `GET /api/connectors/:id`: Get connector by ID
- `POST /api/connectors`: Create new connector
- `PUT /api/connectors/:id`: Update connector
- `DELETE /api/connectors/:id`: Delete connector
- `POST /api/connectors/:id/activate`: Activate connector
- `POST /api/connectors/:id/deactivate`: Deactivate connector
- `POST /api/connectors/:id/test`: Test connector connection
- `POST /api/connectors/:id/jobs`: Create collection job for connector
- `GET /api/connectors/:id/jobs`: Get collection jobs for connector
- `POST /api/connectors/:id/schedule`: Schedule collection for connector
- `GET /api/connectors/scheduled`: Get scheduled collections

### Job API

- `GET /api/jobs`: Get all jobs with optional filtering
- `GET /api/jobs/:id`: Get job by ID
- `POST /api/jobs/:id/execute`: Execute job
- `POST /api/jobs/:id/cancel`: Cancel job

## License

Proprietary - NovaFuse, Inc.
