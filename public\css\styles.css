/* Global Styles */
body {
  font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f8f9fa;
}

/* Sidebar Styles */
.sidebar {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  z-index: 100;
  padding: 48px 0 0;
  box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
  height: 100vh;
}

.sidebar-sticky {
  position: relative;
  top: 0;
  height: calc(100vh - 48px);
  padding-top: .5rem;
  overflow-x: hidden;
  overflow-y: auto;
}

.sidebar .nav-link {
  font-weight: 500;
  color: #adb5bd;
  padding: .75rem 1rem;
  margin-bottom: .25rem;
  border-radius: .25rem;
}

.sidebar .nav-link:hover {
  color: #fff;
  background-color: rgba(255, 255, 255, .1);
}

.sidebar .nav-link.active {
  color: #fff;
  background-color: rgba(255, 255, 255, .2);
}

.sidebar-header {
  padding: 0 1rem;
}

.sidebar-footer {
  padding: 1rem;
  position: absolute;
  bottom: 0;
  width: 100%;
}

/* Main Content Styles */
main {
  padding-top: 1.5rem;
}

/* Card Styles */
.card {
  border-radius: .5rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  margin-bottom: 1.5rem;
}

.card-header {
  background-color: rgba(0, 0, 0, 0.03);
  border-bottom: 1px solid rgba(0, 0, 0, 0.125);
  padding: 0.75rem 1.25rem;
}

.card-footer {
  background-color: rgba(0, 0, 0, 0.03);
  border-top: 1px solid rgba(0, 0, 0, 0.125);
  padding: 0.75rem 1.25rem;
}

/* Dashboard Cards */
.card-title {
  margin-bottom: 0.5rem;
  font-weight: 500;
}

/* Notification Styles */
.notification-item {
  border-left: 4px solid transparent;
  padding-left: 0.75rem;
}

.notification-item.priority-high {
  border-left-color: #dc3545;
}

.notification-item.priority-medium {
  border-left-color: #fd7e14;
}

.notification-item.priority-low {
  border-left-color: #20c997;
}

/* Table Styles */
.table-responsive {
  border-radius: .5rem;
  overflow: hidden;
}

.table {
  margin-bottom: 0;
}

.table th {
  border-top: none;
  background-color: #f8f9fa;
  font-weight: 600;
}

/* Form Styles */
.form-control:focus {
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.form-label {
  font-weight: 500;
}

/* Button Styles */
.btn {
  border-radius: .25rem;
  padding: .375rem .75rem;
  font-weight: 500;
}

.btn-primary {
  background-color: #0d6efd;
  border-color: #0d6efd;
}

.btn-primary:hover {
  background-color: #0b5ed7;
  border-color: #0a58ca;
}

/* Badge Styles */
.badge {
  font-weight: 500;
  padding: .35em .65em;
  border-radius: .25rem;
}

/* Animation */
.fade-in {
  animation: fadeIn 0.5s;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Responsive Adjustments */
@media (max-width: 767.98px) {
  .sidebar {
    position: static;
    height: auto;
    padding-top: 0;
  }
  
  .sidebar-sticky {
    height: auto;
  }
  
  .sidebar-footer {
    position: static;
  }
}

/* Dark Mode Styles */
.dark-mode {
  background-color: #212529;
  color: #f8f9fa;
}

.dark-mode .card {
  background-color: #343a40;
  border-color: #495057;
}

.dark-mode .card-header,
.dark-mode .card-footer {
  background-color: rgba(255, 255, 255, 0.05);
  border-color: #495057;
}

.dark-mode .table {
  color: #f8f9fa;
}

.dark-mode .table th {
  background-color: #343a40;
}

.dark-mode .form-control {
  background-color: #495057;
  border-color: #6c757d;
  color: #f8f9fa;
}

.dark-mode .form-control:focus {
  background-color: #495057;
  color: #f8f9fa;
}

/* Accessibility Styles */
.visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus Styles for Accessibility */
a:focus, button:focus, input:focus, select:focus, textarea:focus {
  outline: 2px solid #0d6efd;
  outline-offset: 2px;
}
