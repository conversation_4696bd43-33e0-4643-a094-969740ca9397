/**
 * Legal & Regulatory Intelligence Connector Implementation
 * 
 * This module implements the connector for legal and regulatory intelligence systems.
 */

const axios = require('axios');
const { createLogger } = require('../../utils/logger');

const logger = createLogger('legal-regulatory-intelligence-connector');

/**
 * Legal & Regulatory Intelligence Connector
 */
class LegalRegulatoryIntelligenceConnector {
  /**
   * Constructor
   * 
   * @param {Object} config - Connector configuration
   * @param {Object} credentials - Connector credentials
   */
  constructor(config, credentials) {
    this.config = config || {};
    this.credentials = credentials || {};
    this.baseUrl = this.config.baseUrl || 'https://api.example.com';
    this.accessToken = null;
    this.tokenExpiry = null;
  }

  /**
   * Initialize the connector
   * 
   * @returns {Promise<void>}
   */
  async initialize() {
    logger.info('Initializing Legal & Regulatory Intelligence connector');
    
    // Authenticate if needed
    if (this.credentials.clientId && this.credentials.clientSecret) {
      await this.authenticate();
    }
  }

  /**
   * Authenticate with the API
   * 
   * @returns {Promise<void>}
   */
  async authenticate() {
    logger.info('Authenticating with Legal & Regulatory Intelligence API');
    
    try {
      const response = await axios.post(`${this.baseUrl}/oauth2/token`, {
        grant_type: 'client_credentials',
        client_id: this.credentials.clientId,
        client_secret: this.credentials.clientSecret,
        scope: 'read:regulations read:legal'
      }, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': 'application/json'
        }
      });
      
      this.accessToken = response.data.access_token;
      this.tokenExpiry = Date.now() + (response.data.expires_in * 1000);
      
      logger.info('Authentication successful');
    } catch (error) {
      logger.error('Authentication failed', { error: error.message });
      throw new Error(`Authentication failed: ${error.message}`);
    }
  }

  /**
   * Get authentication headers
   * 
   * @returns {Promise<Object>} - Authentication headers
   */
  async getAuthHeaders() {
    // Check if token is expired or about to expire (within 5 minutes)
    if (!this.accessToken || (this.tokenExpiry && this.tokenExpiry - Date.now() < 300000)) {
      await this.authenticate();
    }
    
    return {
      'Authorization': `Bearer ${this.accessToken}`
    };
  }

  /**
   * List regulations
   * 
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} - List of regulations
   */
  async listRegulations(params = {}) {
    logger.info('Listing regulations', { params });
    
    try {
      const authHeaders = await this.getAuthHeaders();
      
      const response = await axios.get(`${this.baseUrl}/regulations`, {
        params,
        headers: {
          ...authHeaders,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error('Error listing regulations', { error: error.message });
      throw new Error(`Error listing regulations: ${error.message}`);
    }
  }

  /**
   * Get a regulation
   * 
   * @param {string} regulationId - Regulation ID
   * @returns {Promise<Object>} - Regulation details
   */
  async getRegulation(regulationId) {
    logger.info('Getting regulation', { regulationId });
    
    if (!regulationId) {
      throw new Error('Regulation ID is required');
    }
    
    try {
      const authHeaders = await this.getAuthHeaders();
      
      const response = await axios.get(`${this.baseUrl}/regulations/${regulationId}`, {
        headers: {
          ...authHeaders,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error('Error getting regulation', { regulationId, error: error.message });
      throw new Error(`Error getting regulation: ${error.message}`);
    }
  }

  /**
   * Search regulations
   * 
   * @param {string} query - Search query
   * @param {Object} params - Additional query parameters
   * @returns {Promise<Object>} - Search results
   */
  async searchRegulations(query, params = {}) {
    logger.info('Searching regulations', { query, params });
    
    if (!query) {
      throw new Error('Search query is required');
    }
    
    try {
      const authHeaders = await this.getAuthHeaders();
      
      const response = await axios.get(`${this.baseUrl}/regulations/search`, {
        params: {
          query,
          ...params
        },
        headers: {
          ...authHeaders,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error('Error searching regulations', { query, error: error.message });
      throw new Error(`Error searching regulations: ${error.message}`);
    }
  }

  /**
   * Get regulatory updates
   * 
   * @param {string} jurisdiction - Jurisdiction code
   * @param {string} industry - Industry code
   * @param {Object} params - Additional query parameters
   * @returns {Promise<Object>} - Regulatory updates
   */
  async getRegulatoryUpdates(jurisdiction, industry, params = {}) {
    logger.info('Getting regulatory updates', { jurisdiction, industry, params });
    
    try {
      const authHeaders = await this.getAuthHeaders();
      
      const response = await axios.get(`${this.baseUrl}/regulations/updates`, {
        params: {
          jurisdiction,
          industry,
          ...params
        },
        headers: {
          ...authHeaders,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error('Error getting regulatory updates', { jurisdiction, industry, error: error.message });
      throw new Error(`Error getting regulatory updates: ${error.message}`);
    }
  }

  /**
   * Get regulatory calendar
   * 
   * @param {string} startDate - Start date (YYYY-MM-DD)
   * @param {string} endDate - End date (YYYY-MM-DD)
   * @param {Object} params - Additional query parameters
   * @returns {Promise<Object>} - Regulatory calendar
   */
  async getRegulatoryCalendar(startDate, endDate, params = {}) {
    logger.info('Getting regulatory calendar', { startDate, endDate, params });
    
    try {
      const authHeaders = await this.getAuthHeaders();
      
      const response = await axios.get(`${this.baseUrl}/regulations/calendar`, {
        params: {
          startDate,
          endDate,
          ...params
        },
        headers: {
          ...authHeaders,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error('Error getting regulatory calendar', { startDate, endDate, error: error.message });
      throw new Error(`Error getting regulatory calendar: ${error.message}`);
    }
  }

  /**
   * Get regulatory impact analysis
   * 
   * @param {string} regulationId - Regulation ID
   * @param {string} organizationId - Organization ID
   * @returns {Promise<Object>} - Regulatory impact analysis
   */
  async getRegulatoryImpact(regulationId, organizationId) {
    logger.info('Getting regulatory impact analysis', { regulationId, organizationId });
    
    if (!regulationId) {
      throw new Error('Regulation ID is required');
    }
    
    if (!organizationId) {
      throw new Error('Organization ID is required');
    }
    
    try {
      const authHeaders = await this.getAuthHeaders();
      
      const response = await axios.get(`${this.baseUrl}/regulations/${regulationId}/impact`, {
        params: {
          organizationId
        },
        headers: {
          ...authHeaders,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error('Error getting regulatory impact analysis', { regulationId, organizationId, error: error.message });
      throw new Error(`Error getting regulatory impact analysis: ${error.message}`);
    }
  }
}

module.exports = LegalRegulatoryIntelligenceConnector;
