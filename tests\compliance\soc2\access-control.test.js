/**
 * SOC 2 Compliance Tests - Access Control
 * 
 * These tests verify that NovaTrack supports SOC 2 access control requirements
 */

const { describe, it, beforeEach, afterEach, expect } = require('@jest/globals');
const fs = require('fs');
const path = require('path');
const os = require('os');

// Import the TrackingManager mock
const { TrackingManager } = require('../../mocks/tracking-manager.mock');

// Import test data generator
const { generateRequirement, generateActivity } = require('../../data/novatrack-test-data');

describe('SOC 2 Compliance - Access Control', () => {
  let trackingManager;
  let tempDir;
  
  beforeEach(() => {
    // Create a temporary directory for test data
    tempDir = fs.mkdtempSync(path.join(os.tmpdir(), 'soc2-compliance-test-'));
    
    // Initialize the TrackingManager with the temporary directory
    trackingManager = new TrackingManager(tempDir);
  });
  
  afterEach(() => {
    // Clean up the temporary directory
    if (fs.existsSync(tempDir)) {
      fs.rmSync(tempDir, { recursive: true, force: true });
    }
  });
  
  describe('Logical Access Controls (CC6.1)', () => {
    it('should track access control requirements', () => {
      // Create a SOC 2 access control requirement
      const requirement = trackingManager.create_requirement({
        name: 'Implement Logical Access Controls',
        description: 'Implement logical access security measures to restrict access to information assets',
        framework: 'SOC 2',
        category: 'access_control',
        priority: 'high',
        status: 'in_progress',
        due_date: '2023-12-31',
        assigned_to: 'security_officer',
        tags: ['soc2', 'access_control', 'security']
      });
      
      // Create activities for implementing access controls
      const activity1 = trackingManager.create_activity({
        name: 'Define Access Control Policy',
        description: 'Create a formal access control policy document',
        requirement_id: requirement.id,
        type: 'documentation',
        status: 'completed',
        start_date: '2023-01-01',
        end_date: '2023-01-15',
        assigned_to: 'security_officer',
        notes: 'Access control policy document created and approved'
      });
      
      const activity2 = trackingManager.create_activity({
        name: 'Implement Role-Based Access Control',
        description: 'Configure role-based access control in the system',
        requirement_id: requirement.id,
        type: 'implementation',
        status: 'in_progress',
        start_date: '2023-01-16',
        end_date: '2023-02-15',
        assigned_to: 'system_administrator',
        notes: 'RBAC implementation in progress'
      });
      
      // Verify the requirement was created correctly
      expect(requirement).toBeDefined();
      expect(requirement.framework).toBe('SOC 2');
      expect(requirement.category).toBe('access_control');
      
      // Verify the activities were created correctly
      expect(activity1).toBeDefined();
      expect(activity1.requirement_id).toBe(requirement.id);
      expect(activity1.status).toBe('completed');
      
      expect(activity2).toBeDefined();
      expect(activity2.requirement_id).toBe(requirement.id);
      expect(activity2.status).toBe('in_progress');
      
      // Verify the activities can be retrieved for the requirement
      const activities = trackingManager.get_requirement_activities(requirement.id);
      expect(activities.length).toBe(2);
      expect(activities).toContainEqual(activity1);
      expect(activities).toContainEqual(activity2);
    });
  });
  
  describe('User Access Reviews (CC6.2)', () => {
    it('should track user access review requirements', () => {
      // Create a SOC 2 user access review requirement
      const requirement = trackingManager.create_requirement({
        name: 'Perform User Access Reviews',
        description: 'Periodically review user access rights to verify appropriate access',
        framework: 'SOC 2',
        category: 'access_control',
        priority: 'medium',
        status: 'in_progress',
        due_date: '2023-12-31',
        assigned_to: 'security_officer',
        tags: ['soc2', 'access_control', 'user_access_review']
      });
      
      // Create activities for user access reviews
      const activity1 = trackingManager.create_activity({
        name: 'Q1 User Access Review',
        description: 'Perform user access review for Q1',
        requirement_id: requirement.id,
        type: 'review',
        status: 'completed',
        start_date: '2023-01-01',
        end_date: '2023-01-15',
        assigned_to: 'security_officer',
        notes: 'Q1 user access review completed'
      });
      
      const activity2 = trackingManager.create_activity({
        name: 'Q2 User Access Review',
        description: 'Perform user access review for Q2',
        requirement_id: requirement.id,
        type: 'review',
        status: 'completed',
        start_date: '2023-04-01',
        end_date: '2023-04-15',
        assigned_to: 'security_officer',
        notes: 'Q2 user access review completed'
      });
      
      const activity3 = trackingManager.create_activity({
        name: 'Q3 User Access Review',
        description: 'Perform user access review for Q3',
        requirement_id: requirement.id,
        type: 'review',
        status: 'pending',
        start_date: '2023-07-01',
        end_date: '2023-07-15',
        assigned_to: 'security_officer',
        notes: 'Q3 user access review scheduled'
      });
      
      // Verify the requirement was created correctly
      expect(requirement).toBeDefined();
      expect(requirement.framework).toBe('SOC 2');
      expect(requirement.category).toBe('access_control');
      
      // Verify the activities were created correctly
      expect(activity1).toBeDefined();
      expect(activity1.requirement_id).toBe(requirement.id);
      expect(activity1.status).toBe('completed');
      
      expect(activity2).toBeDefined();
      expect(activity2.requirement_id).toBe(requirement.id);
      expect(activity2.status).toBe('completed');
      
      expect(activity3).toBeDefined();
      expect(activity3.requirement_id).toBe(requirement.id);
      expect(activity3.status).toBe('pending');
      
      // Verify the activities can be retrieved for the requirement
      const activities = trackingManager.get_requirement_activities(requirement.id);
      expect(activities.length).toBe(3);
      expect(activities).toContainEqual(activity1);
      expect(activities).toContainEqual(activity2);
      expect(activities).toContainEqual(activity3);
    });
  });
  
  describe('System Change Management (CC8.1)', () => {
    it('should track system change management requirements', () => {
      // Create a SOC 2 change management requirement
      const requirement = trackingManager.create_requirement({
        name: 'Implement Change Management Process',
        description: 'Establish a formal change management process for system changes',
        framework: 'SOC 2',
        category: 'change_management',
        priority: 'high',
        status: 'in_progress',
        due_date: '2023-12-31',
        assigned_to: 'it_manager',
        tags: ['soc2', 'change_management', 'security']
      });
      
      // Create activities for change management
      const activity1 = trackingManager.create_activity({
        name: 'Define Change Management Policy',
        description: 'Create a formal change management policy document',
        requirement_id: requirement.id,
        type: 'documentation',
        status: 'completed',
        start_date: '2023-01-01',
        end_date: '2023-01-15',
        assigned_to: 'it_manager',
        notes: 'Change management policy document created and approved'
      });
      
      const activity2 = trackingManager.create_activity({
        name: 'Implement Change Approval Process',
        description: 'Configure change approval workflow in the system',
        requirement_id: requirement.id,
        type: 'implementation',
        status: 'in_progress',
        start_date: '2023-01-16',
        end_date: '2023-02-15',
        assigned_to: 'system_administrator',
        notes: 'Change approval workflow implementation in progress'
      });
      
      // Verify the requirement was created correctly
      expect(requirement).toBeDefined();
      expect(requirement.framework).toBe('SOC 2');
      expect(requirement.category).toBe('change_management');
      
      // Verify the activities were created correctly
      expect(activity1).toBeDefined();
      expect(activity1.requirement_id).toBe(requirement.id);
      expect(activity1.status).toBe('completed');
      
      expect(activity2).toBeDefined();
      expect(activity2.requirement_id).toBe(requirement.id);
      expect(activity2.status).toBe('in_progress');
      
      // Verify the activities can be retrieved for the requirement
      const activities = trackingManager.get_requirement_activities(requirement.id);
      expect(activities.length).toBe(2);
      expect(activities).toContainEqual(activity1);
      expect(activities).toContainEqual(activity2);
    });
  });
  
  describe('Risk Assessment (CC3.1)', () => {
    it('should track risk assessment requirements', () => {
      // Create a SOC 2 risk assessment requirement
      const requirement = trackingManager.create_requirement({
        name: 'Perform Risk Assessment',
        description: 'Conduct a comprehensive risk assessment of the system',
        framework: 'SOC 2',
        category: 'risk_assessment',
        priority: 'high',
        status: 'in_progress',
        due_date: '2023-12-31',
        assigned_to: 'risk_manager',
        tags: ['soc2', 'risk_assessment', 'security']
      });
      
      // Create activities for risk assessment
      const activity1 = trackingManager.create_activity({
        name: 'Identify Assets',
        description: 'Identify and inventory all system assets',
        requirement_id: requirement.id,
        type: 'assessment',
        status: 'completed',
        start_date: '2023-01-01',
        end_date: '2023-01-15',
        assigned_to: 'risk_manager',
        notes: 'Asset inventory completed'
      });
      
      const activity2 = trackingManager.create_activity({
        name: 'Identify Threats',
        description: 'Identify potential threats to the system',
        requirement_id: requirement.id,
        type: 'assessment',
        status: 'completed',
        start_date: '2023-01-16',
        end_date: '2023-01-31',
        assigned_to: 'risk_manager',
        notes: 'Threat identification completed'
      });
      
      const activity3 = trackingManager.create_activity({
        name: 'Assess Vulnerabilities',
        description: 'Assess system vulnerabilities',
        requirement_id: requirement.id,
        type: 'assessment',
        status: 'in_progress',
        start_date: '2023-02-01',
        end_date: '2023-02-15',
        assigned_to: 'security_officer',
        notes: 'Vulnerability assessment in progress'
      });
      
      // Verify the requirement was created correctly
      expect(requirement).toBeDefined();
      expect(requirement.framework).toBe('SOC 2');
      expect(requirement.category).toBe('risk_assessment');
      
      // Verify the activities were created correctly
      expect(activity1).toBeDefined();
      expect(activity1.requirement_id).toBe(requirement.id);
      expect(activity1.status).toBe('completed');
      
      expect(activity2).toBeDefined();
      expect(activity2.requirement_id).toBe(requirement.id);
      expect(activity2.status).toBe('completed');
      
      expect(activity3).toBeDefined();
      expect(activity3.requirement_id).toBe(requirement.id);
      expect(activity3.status).toBe('in_progress');
      
      // Verify the activities can be retrieved for the requirement
      const activities = trackingManager.get_requirement_activities(requirement.id);
      expect(activities.length).toBe(3);
      expect(activities).toContainEqual(activity1);
      expect(activities).toContainEqual(activity2);
      expect(activities).toContainEqual(activity3);
    });
  });
});
