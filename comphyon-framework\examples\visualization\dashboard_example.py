"""
Dashboard Example for the ComphyonΨᶜ Framework.

This example demonstrates how to create an interactive dashboard for visualizing
ComphyonΨᶜ metrics.
"""

import sys
import os
import time
import threading
import numpy as np
import pandas as pd
from datetime import datetime

# Add the parent directory to the path so we can import the packages
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../comphyon-meter')))
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../comphyon-governor')))

try:
    from src.comphyon_meter import ComphyonMeter
    from src.comphyon_governor import ComphyonGovernor
except ImportError:
    print("Error: Could not import ComphyonMeter or ComphyonGovernor.")
    print("Please make sure you have cloned the repositories and installed the packages.")
    print("See the README.md for instructions.")
    sys.exit(1)

try:
    import dash
    from dash import dcc, html
    from dash.dependencies import Input, Output
    import plotly.graph_objs as go
    from plotly.subplots import make_subplots
except ImportError:
    print("Error: Could not import Dash or Plotly.")
    print("Please install the required packages:")
    print("pip install dash plotly pandas")
    sys.exit(1)

# Import the SystemSimulator from the advanced example
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../advanced')))
try:
    from real_time_monitoring import SystemSimulator
except ImportError:
    # Define a simplified version if the import fails
    class SystemSimulator:
        """Simplified SystemSimulator for the dashboard example."""
        
        def __init__(self, name="System-1"):
            self.name = name
            self.start_time = time.time()
            self.csde_base = [0.75, 0.85, 0.65, 0.90]
            self.csfe_base = [0.65, 0.70, 0.80, 0.80]
            self.csme_base = [0.70, 0.90, 0.60, 0.85]
            self.state = {
                'trend_factor': 0.02,
                'oscillation_factor': 0.05,
                'resource_allocation': 1.0,
                'coupling_strength': 1.0,
                'ethical_score': 0.82
            }
        
        def generate_tensor_data(self):
            elapsed = time.time() - self.start_time
            oscillation = self.state['oscillation_factor'] * np.sin(elapsed / 5)
            trend = self.state['trend_factor'] * np.sin(elapsed / 20)
            
            csde_tensor = [
                self.csde_base[0] + trend + np.random.normal(0, 0.03),
                self.csde_base[1] + oscillation + np.random.normal(0, 0.03),
                self.csde_base[2] + np.random.normal(0, 0.03),
                self.csde_base[3] + np.random.normal(0, 0.03)
            ]
            
            csfe_tensor = [
                self.csfe_base[0] + trend + np.random.normal(0, 0.03),
                self.csfe_base[1] + oscillation + np.random.normal(0, 0.03),
                self.csfe_base[2] + np.random.normal(0, 0.03),
                self.csfe_base[3] + np.random.normal(0, 0.03)
            ]
            
            csme_tensor = [
                self.csme_base[0] + np.random.normal(0, 0.03),
                self.csme_base[1] + np.random.normal(0, 0.03),
                self.csme_base[2] + trend + np.random.normal(0, 0.03),
                self.csme_base[3] + oscillation + np.random.normal(0, 0.03)
            ]
            
            # Clip values to valid range
            csde_tensor = [max(0.1, min(0.99, x)) for x in csde_tensor]
            csfe_tensor = [max(0.1, min(0.99, x)) for x in csfe_tensor]
            csme_tensor = [max(0.1, min(0.99, x)) for x in csme_tensor]
            
            return csde_tensor, csfe_tensor, csme_tensor
        
        def apply_control_actions(self, control_actions):
            if control_actions['type'] != 'none':
                self.state['trend_factor'] *= 0.8
                self.state['oscillation_factor'] *= 0.7
                self.state['resource_allocation'] = 0.7
            return self.state


class ComphyonDashboard:
    """
    Interactive dashboard for visualizing ComphyonΨᶜ metrics.
    """
    
    def __init__(self):
        """Initialize the ComphyonΨᶜ Dashboard."""
        self.meter = ComphyonMeter()
        self.governor = ComphyonGovernor(
            thresholds={
                'acceleration': 1.5,  # Trigger control at 1.5 Cph
                'velocity': 60.0      # Trigger control at 60.0 Cph-Flux
            }
        )
        self.system = SystemSimulator()
        
        # Data storage
        self.df = pd.DataFrame(columns=[
            'timestamp', 'acceleration', 'velocity', 
            'E_CSDE', 'E_CSFE', 'E_CSME',
            'resource_allocation', 'control_action'
        ])
        
        # Initialize the Dash app
        self.app = dash.Dash(__name__)
        self.app.title = 'ComphyonΨᶜ Dashboard'
        
        # Define the layout
        self.app.layout = html.Div([
            html.H1('ComphyonΨᶜ Dashboard', style={'textAlign': 'center'}),
            
            html.Div([
                html.Div([
                    html.H3('System Status', style={'textAlign': 'center'}),
                    html.Div(id='status-display', style={'textAlign': 'center', 'fontSize': '24px'})
                ], className='status-container'),
                
                html.Div([
                    html.Button('Apply Control', id='control-button', n_clicks=0),
                    html.Div(id='control-status', style={'marginTop': '10px'})
                ], className='control-container')
            ], style={'display': 'flex', 'justifyContent': 'space-between', 'margin': '20px'}),
            
            html.Div([
                dcc.Graph(id='metrics-graph'),
                dcc.Graph(id='energy-graph'),
                dcc.Graph(id='control-graph')
            ]),
            
            dcc.Interval(
                id='interval-component',
                interval=1000,  # in milliseconds
                n_intervals=0
            )
        ])
        
        # Define callbacks
        self.app.callback(
            [Output('metrics-graph', 'figure'),
             Output('energy-graph', 'figure'),
             Output('control-graph', 'figure'),
             Output('status-display', 'children'),
             Output('status-display', 'style')],
            [Input('interval-component', 'n_intervals')]
        )(self.update_graphs)
        
        self.app.callback(
            [Output('control-status', 'children')],
            [Input('control-button', 'n_clicks')]
        )(self.apply_manual_control)
        
        # Start the data collection thread
        self.running = True
        self.data_thread = threading.Thread(target=self.collect_data)
        self.data_thread.daemon = True
        self.data_thread.start()
    
    def collect_data(self):
        """Collect data from the system and store it in the DataFrame."""
        while self.running:
            # Generate tensor data
            csde_tensor, csfe_tensor, csme_tensor = self.system.generate_tensor_data()
            
            # Calculate metrics
            metrics = self.meter.calculate(csde_tensor, csfe_tensor, csme_tensor)
            
            # Apply control actions if needed
            control_actions = self.governor.regulate(metrics)
            
            # Apply control actions to the system
            self.system.apply_control_actions(control_actions)
            
            # Store data in DataFrame
            new_row = {
                'timestamp': datetime.fromtimestamp(metrics['timestamp']),
                'acceleration': metrics['acceleration'],
                'velocity': metrics['velocity'],
                'E_CSDE': metrics['domain_energies']['E_CSDE'],
                'E_CSFE': metrics['domain_energies']['E_CSFE'],
                'E_CSME': metrics['domain_energies']['E_CSME'],
                'resource_allocation': self.system.state['resource_allocation'],
                'control_action': control_actions['type']
            }
            
            self.df = pd.concat([self.df, pd.DataFrame([new_row])], ignore_index=True)
            
            # Keep only the last 100 rows
            if len(self.df) > 100:
                self.df = self.df.iloc[-100:]
            
            time.sleep(1)
    
    def update_graphs(self, n_intervals):
        """
        Update the graphs with the latest data.
        
        Args:
            n_intervals: Number of intervals (not used)
            
        Returns:
            tuple: (metrics_fig, energy_fig, control_fig, status_text, status_style)
        """
        if self.df.empty:
            # Return empty figures if no data
            return {}, {}, {}, "No data available", {'textAlign': 'center', 'fontSize': '24px'}
        
        # Create metrics figure
        metrics_fig = make_subplots(rows=2, cols=1, shared_xaxes=True)
        
        metrics_fig.add_trace(
            go.Scatter(
                x=self.df['timestamp'], 
                y=self.df['velocity'],
                name='Velocity',
                line=dict(color='blue')
            ),
            row=1, col=1
        )
        
        metrics_fig.add_trace(
            go.Scatter(
                x=self.df['timestamp'], 
                y=self.df['acceleration'],
                name='Acceleration',
                line=dict(color='red')
            ),
            row=2, col=1
        )
        
        # Add threshold lines
        metrics_fig.add_trace(
            go.Scatter(
                x=self.df['timestamp'],
                y=[self.governor.thresholds['acceleration']] * len(self.df),
                name='Acceleration Threshold',
                line=dict(color='orange', dash='dash')
            ),
            row=2, col=1
        )
        
        metrics_fig.add_trace(
            go.Scatter(
                x=self.df['timestamp'],
                y=[2.5] * len(self.df),
                name='Critical Threshold',
                line=dict(color='red', dash='dash')
            ),
            row=2, col=1
        )
        
        metrics_fig.update_layout(
            title='ComphyonΨᶜ Metrics',
            height=500,
            margin=dict(l=50, r=50, t=50, b=50)
        )
        
        metrics_fig.update_yaxes(title_text='Velocity (Cph-Flux)', row=1, col=1)
        metrics_fig.update_yaxes(title_text='Acceleration (Cph)', row=2, col=1)
        metrics_fig.update_xaxes(title_text='Time', row=2, col=1)
        
        # Create energy figure
        energy_fig = go.Figure()
        
        energy_fig.add_trace(
            go.Scatter(
                x=self.df['timestamp'], 
                y=self.df['E_CSDE'],
                name='CSDE Energy',
                line=dict(color='green')
            )
        )
        
        energy_fig.add_trace(
            go.Scatter(
                x=self.df['timestamp'], 
                y=self.df['E_CSFE'],
                name='CSFE Energy',
                line=dict(color='orange')
            )
        )
        
        energy_fig.add_trace(
            go.Scatter(
                x=self.df['timestamp'], 
                y=self.df['E_CSME'],
                name='CSME Energy',
                line=dict(color='purple')
            )
        )
        
        energy_fig.update_layout(
            title='Domain Energies',
            xaxis_title='Time',
            yaxis_title='Energy Value',
            height=400,
            margin=dict(l=50, r=50, t=50, b=50)
        )
        
        # Create control figure
        control_fig = go.Figure()
        
        control_fig.add_trace(
            go.Scatter(
                x=self.df['timestamp'], 
                y=self.df['resource_allocation'],
                name='Resource Allocation',
                line=dict(color='blue')
            )
        )
        
        # Add markers for control actions
        control_df = self.df[self.df['control_action'] != 'none']
        if not control_df.empty:
            control_fig.add_trace(
                go.Scatter(
                    x=control_df['timestamp'], 
                    y=control_df['resource_allocation'],
                    name='Control Actions',
                    mode='markers',
                    marker=dict(color='red', size=10)
                )
            )
        
        control_fig.update_layout(
            title='System Control',
            xaxis_title='Time',
            yaxis_title='Resource Allocation',
            height=300,
            margin=dict(l=50, r=50, t=50, b=50)
        )
        
        # Determine system status
        latest = self.df.iloc[-1]
        status_text = "Status: "
        status_style = {'textAlign': 'center', 'fontSize': '24px'}
        
        if latest['acceleration'] > 2.5:
            status_text += "CRITICAL"
            status_style['color'] = 'red'
        elif latest['acceleration'] > 1.5:
            status_text += "WARNING"
            status_style['color'] = 'orange'
        else:
            status_text += "SAFE"
            status_style['color'] = 'green'
        
        status_text += f" | Acceleration: {latest['acceleration']:.4f} Cph | Velocity: {latest['velocity']:.4f} Cph-Flux"
        
        return metrics_fig, energy_fig, control_fig, status_text, status_style
    
    def apply_manual_control(self, n_clicks):
        """
        Apply manual control when the button is clicked.
        
        Args:
            n_clicks: Number of button clicks
            
        Returns:
            list: [status_text]
        """
        if n_clicks == 0:
            return ["No manual control applied yet."]
        
        # Apply manual control by reducing the trend factor
        self.system.state['trend_factor'] *= 0.5
        self.system.state['resource_allocation'] = 0.5
        
        return [f"Manual control applied at {datetime.now().strftime('%H:%M:%S')}. Resource allocation set to 50%."]
    
    def run(self, debug=False, port=8050):
        """
        Run the dashboard.
        
        Args:
            debug: Whether to run in debug mode
            port: Port to run the server on
        """
        self.app.run_server(debug=debug, port=port)
    
    def shutdown(self):
        """Shutdown the dashboard."""
        self.running = False
        if self.data_thread.is_alive():
            self.data_thread.join(timeout=1)


def main():
    """
    Run the dashboard example.
    """
    print("ComphyonΨᶜ Framework - Dashboard Example")
    print("========================================")
    print("This example demonstrates how to create an interactive dashboard")
    print("for visualizing ComphyonΨᶜ metrics.")
    print("\nStarting the dashboard on http://127.0.0.1:8050")
    print("Press Ctrl+C to exit")
    
    dashboard = ComphyonDashboard()
    
    try:
        dashboard.run()
    except KeyboardInterrupt:
        print("\nShutting down...")
    finally:
        dashboard.shutdown()

if __name__ == "__main__":
    main()
