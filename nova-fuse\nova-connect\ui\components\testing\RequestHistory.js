/**
 * Request History Component
 * 
 * This component displays a history of executed requests and allows users to reuse them.
 */

import React, { useState } from 'react';
import { 
  Box, 
  Button, 
  Divider, 
  IconButton, 
  List, 
  ListItem, 
  ListItemButton, 
  ListItemText, 
  Paper, 
  Typography 
} from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import ReplayIcon from '@mui/icons-material/Replay';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';

const RequestHistory = ({ history, onSelect, onDelete }) => {
  const [expandedItem, setExpandedItem] = useState(null);
  
  const handleToggleExpand = (id) => {
    setExpandedItem(expandedItem === id ? null : id);
  };
  
  const handleSelect = (item) => {
    if (onSelect) {
      onSelect(item);
    }
  };
  
  const handleDelete = (event, id) => {
    event.stopPropagation();
    if (onDelete) {
      onDelete(id);
    }
  };
  
  const formatTimestamp = (timestamp) => {
    if (typeof timestamp === 'string') {
      return new Date(timestamp).toLocaleString();
    }
    return timestamp.toLocaleString();
  };
  
  const formatParameters = (params) => {
    if (!params || Object.keys(params).length === 0) {
      return 'No parameters';
    }
    
    return Object.entries(params)
      .map(([key, value]) => `${key}: ${typeof value === 'object' ? JSON.stringify(value) : value}`)
      .join(', ');
  };
  
  const getStatusColor = (status) => {
    if (status >= 200 && status < 300) {
      return 'success.main';
    } else if (status >= 400 && status < 500) {
      return 'warning.main';
    } else if (status >= 500) {
      return 'error.main';
    }
    return 'text.primary';
  };
  
  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        Request History
      </Typography>
      
      {history.length === 0 ? (
        <Typography variant="body2" color="textSecondary">
          No requests have been executed yet.
        </Typography>
      ) : (
        <List sx={{ width: '100%', bgcolor: 'background.paper' }} component={Paper} variant="outlined">
          {history.map((item) => (
            <React.Fragment key={item.id}>
              <ListItem
                disablePadding
                secondaryAction={
                  <Box>
                    <IconButton 
                      edge="end" 
                      aria-label="replay" 
                      onClick={() => handleSelect(item)}
                      title="Reuse this request"
                    >
                      <ReplayIcon />
                    </IconButton>
                    {onDelete && (
                      <IconButton 
                        edge="end" 
                        aria-label="delete" 
                        onClick={(e) => handleDelete(e, item.id)}
                        title="Delete from history"
                      >
                        <DeleteIcon />
                      </IconButton>
                    )}
                  </Box>
                }
              >
                <ListItemButton onClick={() => handleToggleExpand(item.id)}>
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Typography variant="subtitle1" component="span">
                          {item.endpoint}
                        </Typography>
                        <Typography 
                          variant="body2" 
                          component="span" 
                          sx={{ 
                            ml: 2, 
                            color: getStatusColor(item.response.status),
                            fontWeight: 'bold'
                          }}
                        >
                          {item.response.status} {item.response.statusText}
                        </Typography>
                      </Box>
                    }
                    secondary={
                      <Box>
                        <Typography variant="caption" component="span" color="textSecondary">
                          {formatTimestamp(item.timestamp)}
                        </Typography>
                        <Typography variant="caption" component="div" color="textSecondary" noWrap>
                          {formatParameters(item.parameters)}
                        </Typography>
                      </Box>
                    }
                  />
                  {expandedItem === item.id ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                </ListItemButton>
              </ListItem>
              
              {expandedItem === item.id && (
                <Box sx={{ p: 2, bgcolor: 'action.hover' }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Parameters:
                  </Typography>
                  <Typography variant="body2" component="pre" sx={{ mb: 2, overflow: 'auto' }}>
                    {JSON.stringify(item.parameters, null, 2)}
                  </Typography>
                  
                  <Typography variant="subtitle2" gutterBottom>
                    Response:
                  </Typography>
                  <Typography variant="body2" component="pre" sx={{ overflow: 'auto', maxHeight: 200 }}>
                    {JSON.stringify(item.response.data, null, 2)}
                  </Typography>
                </Box>
              )}
              
              <Divider />
            </React.Fragment>
          ))}
        </List>
      )}
    </Box>
  );
};

export default RequestHistory;
