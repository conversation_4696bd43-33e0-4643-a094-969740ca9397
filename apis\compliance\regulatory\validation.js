/**
 * Validation schemas for the Regulatory Compliance API
 */

const Joi = require('joi');

// Validation schemas
const schemas = {
  // Regulatory Framework validation schemas
  createRegulatoryFramework: Joi.object({
    name: Joi.string().required().min(3).max(200),
    shortName: Joi.string().required().min(1).max(50),
    description: Joi.string().required().max(1000),
    version: Joi.string().required().max(50),
    effectiveDate: Joi.string().required().pattern(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
    category: Joi.string().required().valid('privacy', 'security', 'financial', 'healthcare', 'environmental', 'employment', 'other'),
    jurisdiction: Joi.string().required().max(50),
    status: Joi.string().required().valid('draft', 'active', 'superseded', 'retired'),
    website: Joi.string().uri().allow('').optional(),
    enforcementAuthority: Joi.string().max(200).allow('').optional()
  }),

  updateRegulatoryFramework: Joi.object({
    name: Joi.string().min(3).max(200).optional(),
    shortName: Joi.string().min(1).max(50).optional(),
    description: Joi.string().max(1000).optional(),
    version: Joi.string().max(50).optional(),
    effectiveDate: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}$/).optional(), // YYYY-MM-DD format
    category: Joi.string().valid('privacy', 'security', 'financial', 'healthcare', 'environmental', 'employment', 'other').optional(),
    jurisdiction: Joi.string().max(50).optional(),
    status: Joi.string().valid('draft', 'active', 'superseded', 'retired').optional(),
    website: Joi.string().uri().allow('').optional(),
    enforcementAuthority: Joi.string().max(200).allow('').optional()
  }).min(1), // At least one field must be provided

  // Regulatory Requirement validation schemas
  createRegulatoryRequirement: Joi.object({
    frameworkId: Joi.string().required(),
    code: Joi.string().required().max(50),
    name: Joi.string().required().min(3).max(200),
    description: Joi.string().required().max(1000),
    article: Joi.string().max(100).allow('').optional(),
    section: Joi.string().max(100).allow('').optional(),
    category: Joi.string().required().max(50),
    priority: Joi.string().required().valid('low', 'medium', 'high', 'critical'),
    status: Joi.string().required().valid('applicable', 'not-applicable', 'under-review'),
    applicableJurisdictions: Joi.array().items(Joi.string()).required(),
    controlObjectives: Joi.array().items(Joi.string().max(200)).optional(),
    relatedRequirements: Joi.array().items(Joi.string()).optional()
  }),

  updateRegulatoryRequirement: Joi.object({
    frameworkId: Joi.string().optional(),
    code: Joi.string().max(50).optional(),
    name: Joi.string().min(3).max(200).optional(),
    description: Joi.string().max(1000).optional(),
    article: Joi.string().max(100).allow('').optional(),
    section: Joi.string().max(100).allow('').optional(),
    category: Joi.string().max(50).optional(),
    priority: Joi.string().valid('low', 'medium', 'high', 'critical').optional(),
    status: Joi.string().valid('applicable', 'not-applicable', 'under-review').optional(),
    applicableJurisdictions: Joi.array().items(Joi.string()).optional(),
    controlObjectives: Joi.array().items(Joi.string().max(200)).optional(),
    relatedRequirements: Joi.array().items(Joi.string()).optional()
  }).min(1), // At least one field must be provided

  // Jurisdiction validation schemas
  createJurisdiction: Joi.object({
    code: Joi.string().required().max(50),
    name: Joi.string().required().min(2).max(100),
    type: Joi.string().required().valid('global', 'region', 'country', 'state', 'province', 'city', 'other'),
    description: Joi.string().max(500).optional(),
    parentJurisdiction: Joi.string().max(50).allow(null).optional()
  }),

  updateJurisdiction: Joi.object({
    code: Joi.string().max(50).optional(),
    name: Joi.string().min(2).max(100).optional(),
    type: Joi.string().valid('global', 'region', 'country', 'state', 'province', 'city', 'other').optional(),
    description: Joi.string().max(500).optional(),
    parentJurisdiction: Joi.string().max(50).allow(null).optional()
  }).min(1), // At least one field must be provided

  // Regulatory Change validation schemas
  createRegulatoryChange: Joi.object({
    frameworkId: Joi.string().required(),
    title: Joi.string().required().min(3).max(200),
    description: Joi.string().required().max(1000),
    changeType: Joi.string().required().valid('legislation', 'regulation', 'guidance', 'case-law', 'other'),
    publicationDate: Joi.string().required().pattern(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
    effectiveDate: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}$/).allow(null).optional(), // YYYY-MM-DD format
    source: Joi.string().required().max(200),
    sourceUrl: Joi.string().uri().allow('').optional(),
    impactLevel: Joi.string().required().valid('low', 'medium', 'high', 'critical'),
    affectedRequirements: Joi.array().items(Joi.string()).optional(),
    status: Joi.string().required().valid('draft', 'published', 'in-effect', 'superseded'),
    summary: Joi.string().max(2000).optional()
  }),

  updateRegulatoryChange: Joi.object({
    frameworkId: Joi.string().optional(),
    title: Joi.string().min(3).max(200).optional(),
    description: Joi.string().max(1000).optional(),
    changeType: Joi.string().valid('legislation', 'regulation', 'guidance', 'case-law', 'other').optional(),
    publicationDate: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}$/).optional(), // YYYY-MM-DD format
    effectiveDate: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}$/).allow(null).optional(), // YYYY-MM-DD format
    source: Joi.string().max(200).optional(),
    sourceUrl: Joi.string().uri().allow('').optional(),
    impactLevel: Joi.string().valid('low', 'medium', 'high', 'critical').optional(),
    affectedRequirements: Joi.array().items(Joi.string()).optional(),
    status: Joi.string().valid('draft', 'published', 'in-effect', 'superseded').optional(),
    summary: Joi.string().max(2000).optional()
  }).min(1), // At least one field must be provided

  // Regulatory Report validation schemas
  createRegulatoryReport: Joi.object({
    frameworkId: Joi.string().required(),
    name: Joi.string().required().min(3).max(200),
    description: Joi.string().required().max(1000),
    type: Joi.string().required().valid('internal', 'external', 'regulatory', 'audit'),
    frequency: Joi.string().required().valid('one-time', 'monthly', 'quarterly', 'semi-annual', 'annual', 'biennial'),
    lastSubmissionDate: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}$/).allow(null).optional(), // YYYY-MM-DD format
    nextDueDate: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}$/).allow(null).optional(), // YYYY-MM-DD format
    assignee: Joi.string().max(100).optional(),
    status: Joi.string().required().valid('not-started', 'in-progress', 'review', 'completed', 'submitted'),
    templateUrl: Joi.string().max(500).allow('').optional(),
    sections: Joi.array().items(Joi.string().max(100)).optional()
  }),

  updateRegulatoryReport: Joi.object({
    frameworkId: Joi.string().optional(),
    name: Joi.string().min(3).max(200).optional(),
    description: Joi.string().max(1000).optional(),
    type: Joi.string().valid('internal', 'external', 'regulatory', 'audit').optional(),
    frequency: Joi.string().valid('one-time', 'monthly', 'quarterly', 'semi-annual', 'annual', 'biennial').optional(),
    lastSubmissionDate: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}$/).allow(null).optional(), // YYYY-MM-DD format
    nextDueDate: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}$/).allow(null).optional(), // YYYY-MM-DD format
    assignee: Joi.string().max(100).optional(),
    status: Joi.string().valid('not-started', 'in-progress', 'review', 'completed', 'submitted').optional(),
    templateUrl: Joi.string().max(500).allow('').optional(),
    sections: Joi.array().items(Joi.string().max(100)).optional()
  }).min(1) // At least one field must be provided
};

/**
 * Middleware to validate request data against a schema
 * @param {string} schemaName - Name of the schema to validate against
 * @returns {function} Express middleware function
 */
const validateRequest = (schemaName) => {
  return (req, res, next) => {
    const schema = schemas[schemaName];
    
    if (!schema) {
      return res.status(500).json({
        error: 'Internal Server Error',
        message: `Schema '${schemaName}' not found`
      });
    }

    const { error } = schema.validate(req.body, { abortEarly: false });
    
    if (error) {
      const errorMessage = error.details.map(detail => detail.message).join(', ');
      return res.status(400).json({
        error: 'Bad Request',
        message: errorMessage
      });
    }
    
    next();
  };
};

module.exports = {
  schemas,
  validateRequest
};
