/**
 * Authentication Validation Schemas
 * 
 * This file contains validation schemas for authentication-related endpoints.
 */

const Joi = require('joi');
const { commonSchemas } = require('../common/commonSchemas');

/**
 * Login schema
 */
const loginSchema = {
  body: Joi.object({
    email: commonSchemas.email.required(),
    password: Joi.string().required(),
    rememberMe: Joi.boolean().optional()
  })
};

/**
 * Register schema
 */
const registerSchema = {
  body: Joi.object({
    email: commonSchemas.email.required(),
    password: commonSchemas.password.required(),
    firstName: Joi.string().required(),
    lastName: Joi.string().required(),
    company: Joi.string().optional(),
    jobTitle: Joi.string().optional(),
    phone: Joi.string().optional(),
    acceptTerms: Joi.boolean().valid(true).required().messages({
      'any.only': 'You must accept the terms and conditions'
    })
  })
};

/**
 * Forgot password schema
 */
const forgotPasswordSchema = {
  body: Joi.object({
    email: commonSchemas.email.required()
  })
};

/**
 * Reset password schema
 */
const resetPasswordSchema = {
  body: Joi.object({
    token: Joi.string().required(),
    password: commonSchemas.password.required(),
    confirmPassword: Joi.string().valid(Joi.ref('password')).required().messages({
      'any.only': 'Passwords must match'
    })
  })
};

/**
 * Change password schema
 */
const changePasswordSchema = {
  body: Joi.object({
    currentPassword: Joi.string().required(),
    newPassword: commonSchemas.password.required(),
    confirmPassword: Joi.string().valid(Joi.ref('newPassword')).required().messages({
      'any.only': 'Passwords must match'
    })
  })
};

/**
 * Refresh token schema
 */
const refreshTokenSchema = {
  body: Joi.object({
    refreshToken: Joi.string().required()
  })
};

/**
 * Verify email schema
 */
const verifyEmailSchema = {
  query: Joi.object({
    token: Joi.string().required()
  })
};

/**
 * Two-factor authentication setup schema
 */
const setupTwoFactorSchema = {
  body: Joi.object({
    enable: Joi.boolean().required()
  })
};

/**
 * Two-factor authentication verify schema
 */
const verifyTwoFactorSchema = {
  body: Joi.object({
    code: Joi.string().pattern(/^\d{6}$/).required().messages({
      'string.pattern.base': 'Code must be a 6-digit number'
    })
  })
};

/**
 * OAuth login schema
 */
const oauthLoginSchema = {
  query: Joi.object({
    provider: Joi.string().valid('google', 'github', 'microsoft', 'okta').required(),
    redirectUrl: Joi.string().uri().optional()
  })
};

/**
 * OAuth callback schema
 */
const oauthCallbackSchema = {
  query: Joi.object({
    provider: Joi.string().valid('google', 'github', 'microsoft', 'okta').required(),
    code: Joi.string().required(),
    state: Joi.string().optional()
  })
};

/**
 * Logout schema
 */
const logoutSchema = {
  body: Joi.object({
    refreshToken: Joi.string().optional()
  })
};

/**
 * Validate token schema
 */
const validateTokenSchema = {
  body: Joi.object({
    token: Joi.string().required()
  })
};

module.exports = {
  loginSchema,
  registerSchema,
  forgotPasswordSchema,
  resetPasswordSchema,
  changePasswordSchema,
  refreshTokenSchema,
  verifyEmailSchema,
  setupTwoFactorSchema,
  verifyTwoFactorSchema,
  oauthLoginSchema,
  oauthCallbackSchema,
  logoutSchema,
  validateTokenSchema
};
