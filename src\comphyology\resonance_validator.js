/**
 * Resonance Validator
 * 
 * This module provides functionality for validating and enforcing the 3-6-9-12-13 resonance pattern
 * across all tensor operations. It implements the Law of Resonant Encapsulation:
 * 
 * "No system shall externalize what its resonance core has not first harmonized."
 */

// Mathematical constants
const PI = Math.PI;
const PHI = (1 + Math.sqrt(5)) / 2;

// 3-6-9-12-13 Resonance Pattern constants
const RESONANCE_PATTERN = {
  CYCLES: [3, 6, 9, 12],
  THRESHOLDS: [0.3, 0.6, 0.9],
  DECAY_RATES: [0.03, 0.06, 0.09, 0.12, 0.13],
  EFFECTIVENESS_TARGETS: [0.3, 0.6, 0.9]
};

/**
 * Resonance Validator class
 */
class ResonanceValidator {
  /**
   * Constructor
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    this.options = {
      strictMode: false, // Whether to reject non-resonant values (true) or harmonize them (false)
      logValidation: true, // Whether to log validation results
      resonanceLock: true, // Whether to enforce resonance constraints
      ...options
    };
  }

  /**
   * Check if a value is resonant (aligns with the 3-6-9-12-13 pattern)
   * @param {number} value - Value to check
   * @param {string} type - Type of value ('cycle', 'threshold', 'decay', 'factor')
   * @returns {boolean} - Whether the value is resonant
   */
  isResonant(value, type = 'generic') {
    if (!this.options.resonanceLock) {
      return true; // If resonance lock is disabled, all values are considered resonant
    }

    // For integer values (like cycles)
    if (type === 'cycle') {
      return RESONANCE_PATTERN.CYCLES.includes(value);
    }

    // For threshold values
    if (type === 'threshold') {
      return RESONANCE_PATTERN.THRESHOLDS.includes(value);
    }

    // For decay rates
    if (type === 'decay') {
      return RESONANCE_PATTERN.DECAY_RATES.includes(value);
    }

    // For effectiveness factors
    if (type === 'factor') {
      return RESONANCE_PATTERN.EFFECTIVENESS_TARGETS.includes(value);
    }

    // For generic values, check if the digit sum is resonant
    const digitSum = this._calculateDigitSum(value);
    return [3, 6, 9, 12, 13].includes(digitSum);
  }

  /**
   * Harmonize a value to the nearest resonant value
   * @param {number} value - Value to harmonize
   * @param {string} type - Type of value ('cycle', 'threshold', 'decay', 'factor')
   * @returns {number} - Harmonized value
   */
  harmonize(value, type = 'generic') {
    if (!this.options.resonanceLock) {
      return value; // If resonance lock is disabled, return the original value
    }

    // If the value is already resonant, return it
    if (this.isResonant(value, type)) {
      return value;
    }

    // For integer values (like cycles)
    if (type === 'cycle') {
      return this._findNearestValue(value, RESONANCE_PATTERN.CYCLES);
    }

    // For threshold values
    if (type === 'threshold') {
      return this._findNearestValue(value, RESONANCE_PATTERN.THRESHOLDS);
    }

    // For decay rates
    if (type === 'decay') {
      return this._findNearestValue(value, RESONANCE_PATTERN.DECAY_RATES);
    }

    // For effectiveness factors
    if (type === 'factor') {
      return this._findNearestValue(value, RESONANCE_PATTERN.EFFECTIVENESS_TARGETS);
    }

    // For generic values, find the nearest value with a resonant digit sum
    const digitSum = this._calculateDigitSum(value);
    const targetSums = [3, 6, 9, 12, 13];
    const nearestSum = this._findNearestValue(digitSum, targetSums);
    
    // Adjust the value to achieve the target digit sum
    return this._adjustValueToDigitSum(value, nearestSum);
  }

  /**
   * Validate a value against resonance constraints
   * @param {number} value - Value to validate
   * @param {string} type - Type of value ('cycle', 'threshold', 'decay', 'factor')
   * @returns {Object} - Validation result
   */
  validate(value, type = 'generic') {
    const isResonant = this.isResonant(value, type);
    
    if (!this.options.resonanceLock) {
      return {
        isValid: true,
        originalValue: value,
        harmonizedValue: value,
        isResonant,
        type
      };
    }

    if (isResonant) {
      return {
        isValid: true,
        originalValue: value,
        harmonizedValue: value,
        isResonant: true,
        type
      };
    }

    // If strict mode is enabled and the value is not resonant, it's invalid
    if (this.options.strictMode) {
      return {
        isValid: false,
        originalValue: value,
        harmonizedValue: null,
        isResonant: false,
        type,
        error: `Value ${value} is not resonant for type ${type}`
      };
    }

    // If strict mode is disabled, harmonize the value
    const harmonizedValue = this.harmonize(value, type);
    
    return {
      isValid: true,
      originalValue: value,
      harmonizedValue,
      isResonant: false,
      wasHarmonized: true,
      type,
      resonanceDrift: Math.abs(value - harmonizedValue)
    };
  }

  /**
   * Validate a tensor against resonance constraints
   * @param {Object} tensor - Tensor to validate
   * @returns {Object} - Validation result
   */
  validateTensor(tensor) {
    if (!this.options.resonanceLock) {
      return {
        isValid: true,
        originalTensor: tensor,
        harmonizedTensor: tensor,
        isResonant: true
      };
    }

    // Check if tensor values are resonant
    const validationResults = tensor.values.map(value => this.validate(value));
    const isResonant = validationResults.every(result => result.isResonant);
    
    if (isResonant) {
      return {
        isValid: true,
        originalTensor: tensor,
        harmonizedTensor: tensor,
        isResonant: true,
        validationResults
      };
    }

    // If strict mode is enabled and the tensor is not resonant, it's invalid
    if (this.options.strictMode) {
      return {
        isValid: false,
        originalTensor: tensor,
        harmonizedTensor: null,
        isResonant: false,
        validationResults,
        error: 'Tensor contains non-resonant values'
      };
    }

    // If strict mode is disabled, harmonize the tensor
    const harmonizedValues = validationResults.map(result => result.harmonizedValue);
    const harmonizedTensor = {
      ...tensor,
      values: harmonizedValues
    };
    
    return {
      isValid: true,
      originalTensor: tensor,
      harmonizedTensor,
      isResonant: false,
      wasHarmonized: true,
      validationResults,
      resonanceDrift: this._calculateResonanceDrift(tensor.values, harmonizedValues)
    };
  }

  /**
   * Calculate the digit sum of a number
   * @param {number} value - Value to calculate digit sum for
   * @returns {number} - Digit sum
   * @private
   */
  _calculateDigitSum(value) {
    // Convert to string and remove decimal point
    const valueStr = value.toString().replace('.', '');
    
    // Sum the digits
    let sum = 0;
    for (let i = 0; i < valueStr.length; i++) {
      if (valueStr[i] >= '0' && valueStr[i] <= '9') {
        sum += parseInt(valueStr[i], 10);
      }
    }
    
    // If sum is greater than 13, recursively calculate digit sum
    if (sum > 13) {
      return this._calculateDigitSum(sum);
    }
    
    return sum;
  }

  /**
   * Find the nearest value in an array
   * @param {number} value - Value to find nearest for
   * @param {Array} values - Array of values to search
   * @returns {number} - Nearest value
   * @private
   */
  _findNearestValue(value, values) {
    let nearestValue = values[0];
    let minDistance = Math.abs(value - nearestValue);
    
    for (let i = 1; i < values.length; i++) {
      const distance = Math.abs(value - values[i]);
      if (distance < minDistance) {
        minDistance = distance;
        nearestValue = values[i];
      }
    }
    
    return nearestValue;
  }

  /**
   * Adjust a value to achieve a target digit sum
   * @param {number} value - Value to adjust
   * @param {number} targetSum - Target digit sum
   * @returns {number} - Adjusted value
   * @private
   */
  _adjustValueToDigitSum(value, targetSum) {
    // Start with small adjustments and increase if needed
    let adjustment = 0.001;
    let direction = 1;
    let adjustedValue = value;
    let currentSum = this._calculateDigitSum(value);
    let iterations = 0;
    const maxIterations = 1000; // Prevent infinite loops
    
    // If current sum is already target sum, return the value
    if (currentSum === targetSum) {
      return value;
    }
    
    // Determine initial direction
    if (currentSum > targetSum) {
      direction = -1;
    }
    
    // Adjust the value until the digit sum matches the target
    while (currentSum !== targetSum && iterations < maxIterations) {
      adjustedValue = value + (adjustment * direction * iterations);
      currentSum = this._calculateDigitSum(adjustedValue);
      
      if (currentSum === targetSum) {
        break;
      }
      
      iterations++;
    }
    
    return adjustedValue;
  }

  /**
   * Calculate the resonance drift between two arrays of values
   * @param {Array} originalValues - Original values
   * @param {Array} harmonizedValues - Harmonized values
   * @returns {number} - Resonance drift
   * @private
   */
  _calculateResonanceDrift(originalValues, harmonizedValues) {
    let totalDrift = 0;
    
    for (let i = 0; i < originalValues.length; i++) {
      totalDrift += Math.abs(originalValues[i] - harmonizedValues[i]);
    }
    
    return totalDrift / originalValues.length;
  }
}

module.exports = ResonanceValidator;
