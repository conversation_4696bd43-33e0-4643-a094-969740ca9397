# install-novalift.ps1
# NovaLift Enterprise System Booster Installer
# NovaFuse Coherence Operating System
# Cross-platform installer for Windows

param(
    [string]$InstallPath = "C:\NovaFuse",
    [string]$Mode = "Enterprise",
    [switch]$Silent = $false,
    [switch]$SkipDependencies = $false
)

# NovaLift Installer Configuration
$NovaLiftConfig = @{
    Name = "NovaLift Enterprise System Booster"
    Version = "1.0.0"
    Publisher = "NovaFuse Technologies"
    Description = "Coherence Operating System - Enterprise Deployment"
    InstallPath = $InstallPath
    Mode = $Mode
}

# Color functions for output
function Write-NovaLiftInfo {
    param([string]$Message)
    Write-Host "🚀 [NovaLift] $Message" -ForegroundColor Cyan
}

function Write-NovaLiftSuccess {
    param([string]$Message)
    Write-Host "✅ [NovaLift] $Message" -ForegroundColor Green
}

function Write-NovaLiftWarning {
    param([string]$Message)
    Write-Host "⚠️ [NovaLift] $Message" -ForegroundColor Yellow
}

function Write-NovaLiftError {
    param([string]$Message)
    Write-Host "❌ [NovaLift] $Message" -ForegroundColor Red
}

# Banner
function Show-NovaLiftBanner {
    Write-Host ""
    Write-Host "╔══════════════════════════════════════════════════════════════════════════════╗" -ForegroundColor Magenta
    Write-Host "║                           NOVALIFT ENTERPRISE INSTALLER                     ║" -ForegroundColor Magenta
    Write-Host "║                        NovaFuse Coherence Operating System                   ║" -ForegroundColor Magenta
    Write-Host "║                                Version 1.0.0                                ║" -ForegroundColor Magenta
    Write-Host "╚══════════════════════════════════════════════════════════════════════════════╝" -ForegroundColor Magenta
    Write-Host ""
}

# Check prerequisites
function Test-Prerequisites {
    Write-NovaLiftInfo "Checking system prerequisites..."
    
    $prerequisites = @()
    
    # Check PowerShell version
    if ($PSVersionTable.PSVersion.Major -lt 5) {
        $prerequisites += "PowerShell 5.0 or higher required"
    }
    
    # Check .NET Framework
    try {
        $dotNetVersion = Get-ItemProperty "HKLM:SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full\" -Name Release -ErrorAction Stop
        if ($dotNetVersion.Release -lt 461808) {
            $prerequisites += ".NET Framework 4.7.2 or higher required"
        }
    } catch {
        $prerequisites += ".NET Framework 4.7.2 or higher required"
    }
    
    # Check Node.js
    try {
        $nodeVersion = node --version 2>$null
        if (-not $nodeVersion) {
            $prerequisites += "Node.js 16.0 or higher required"
        }
    } catch {
        $prerequisites += "Node.js 16.0 or higher required"
    }
    
    # Check Python
    try {
        $pythonVersion = python --version 2>$null
        if (-not $pythonVersion) {
            $prerequisites += "Python 3.8 or higher required"
        }
    } catch {
        $prerequisites += "Python 3.8 or higher required"
    }
    
    # Check Docker (optional)
    try {
        $dockerVersion = docker --version 2>$null
        if ($dockerVersion) {
            Write-NovaLiftSuccess "Docker detected: $dockerVersion"
        } else {
            Write-NovaLiftWarning "Docker not found (optional for advanced features)"
        }
    } catch {
        Write-NovaLiftWarning "Docker not found (optional for advanced features)"
    }
    
    if ($prerequisites.Count -gt 0 -and -not $SkipDependencies) {
        Write-NovaLiftError "Missing prerequisites:"
        foreach ($prereq in $prerequisites) {
            Write-Host "  - $prereq" -ForegroundColor Red
        }
        return $false
    }
    
    Write-NovaLiftSuccess "Prerequisites check completed"
    return $true
}

# Install dependencies
function Install-Dependencies {
    if ($SkipDependencies) {
        Write-NovaLiftInfo "Skipping dependency installation"
        return
    }
    
    Write-NovaLiftInfo "Installing NovaFuse dependencies..."
    
    # Install Chocolatey if not present
    if (-not (Get-Command choco -ErrorAction SilentlyContinue)) {
        Write-NovaLiftInfo "Installing Chocolatey package manager..."
        Set-ExecutionPolicy Bypass -Scope Process -Force
        [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
        iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
    }
    
    # Install Node.js
    if (-not (Get-Command node -ErrorAction SilentlyContinue)) {
        Write-NovaLiftInfo "Installing Node.js..."
        choco install nodejs -y
    }
    
    # Install Python
    if (-not (Get-Command python -ErrorAction SilentlyContinue)) {
        Write-NovaLiftInfo "Installing Python..."
        choco install python -y
    }
    
    # Install Git
    if (-not (Get-Command git -ErrorAction SilentlyContinue)) {
        Write-NovaLiftInfo "Installing Git..."
        choco install git -y
    }
    
    Write-NovaLiftSuccess "Dependencies installation completed"
}

# Create directory structure
function New-NovaFuseDirectories {
    Write-NovaLiftInfo "Creating NovaFuse directory structure..."
    
    $directories = @(
        $InstallPath,
        "$InstallPath\NovaCore",
        "$InstallPath\NovaAgent", 
        "$InstallPath\NovaBridge",
        "$InstallPath\NovaConsole",
        "$InstallPath\NovaLift",
        "$InstallPath\Config",
        "$InstallPath\Logs",
        "$InstallPath\Data"
    )
    
    foreach ($dir in $directories) {
        if (-not (Test-Path $dir)) {
            New-Item -ItemType Directory -Path $dir -Force | Out-Null
            Write-NovaLiftInfo "Created directory: $dir"
        }
    }
    
    Write-NovaLiftSuccess "Directory structure created"
}

# Install NovaCore engines
function Install-NovaCore {
    Write-NovaLiftInfo "Installing NovaCore engines..."
    
    $novaCoreSource = "nhetx-castl-alpha"
    $novaCoreTarget = "$InstallPath\NovaCore"
    
    if (Test-Path $novaCoreSource) {
        Copy-Item -Path "$novaCoreSource\*" -Destination $novaCoreTarget -Recurse -Force
        Write-NovaLiftSuccess "NovaCore engines installed"
    } else {
        Write-NovaLiftWarning "NovaCore source not found, creating placeholder"
        New-Item -ItemType File -Path "$novaCoreTarget\placeholder.txt" -Force | Out-Null
    }
    
    # Install Node.js dependencies
    if (Test-Path "$novaCoreTarget\package.json") {
        Push-Location $novaCoreTarget
        npm install
        Pop-Location
        Write-NovaLiftSuccess "NovaCore dependencies installed"
    }
}

# Install NovaAgent
function Install-NovaAgent {
    Write-NovaLiftInfo "Installing NovaAgent runtime..."
    
    $novaAgentTarget = "$InstallPath\NovaAgent"
    
    # Copy NovaAgent executable
    if (Test-Path "nova-agent.exe") {
        Copy-Item -Path "nova-agent.exe" -Destination "$novaAgentTarget\nova-agent.exe" -Force
    } elseif (Test-Path "nova-agent.go") {
        # Build from source if Go is available
        if (Get-Command go -ErrorAction SilentlyContinue) {
            Write-NovaLiftInfo "Building NovaAgent from source..."
            go build -o "$novaAgentTarget\nova-agent.exe" nova-agent.go
        } else {
            Write-NovaLiftWarning "NovaAgent executable not found, creating placeholder"
            New-Item -ItemType File -Path "$novaAgentTarget\nova-agent.exe" -Force | Out-Null
        }
    }
    
    Write-NovaLiftSuccess "NovaAgent installed"
}

# Install NovaBridge
function Install-NovaBridge {
    Write-NovaLiftInfo "Installing NovaBridge API layer..."
    
    $novaBridgeSource = "aeonix-divine-api"
    $novaBridgeTarget = "$InstallPath\NovaBridge"
    
    if (Test-Path $novaBridgeSource) {
        Copy-Item -Path "$novaBridgeSource\*" -Destination $novaBridgeTarget -Recurse -Force
        Write-NovaLiftSuccess "NovaBridge installed"
    } else {
        Write-NovaLiftWarning "NovaBridge source not found, creating placeholder"
        New-Item -ItemType File -Path "$novaBridgeTarget\placeholder.txt" -Force | Out-Null
    }
    
    # Install Python dependencies
    if (Test-Path "$novaBridgeTarget\requirements.txt") {
        pip install -r "$novaBridgeTarget\requirements.txt"
        Write-NovaLiftSuccess "NovaBridge dependencies installed"
    }
}

# Install NovaConsole
function Install-NovaConsole {
    Write-NovaLiftInfo "Installing NovaFuse Platform Console..."
    
    $novaConsoleSource = "chaeonix-divine-dashboard"
    $novaConsoleTarget = "$InstallPath\NovaConsole"
    
    if (Test-Path $novaConsoleSource) {
        Copy-Item -Path "$novaConsoleSource\*" -Destination $novaConsoleTarget -Recurse -Force
        Write-NovaLiftSuccess "NovaConsole installed"
    } else {
        Write-NovaLiftWarning "NovaConsole source not found, creating placeholder"
        New-Item -ItemType File -Path "$novaConsoleTarget\placeholder.txt" -Force | Out-Null
    }
    
    # Install npm dependencies
    if (Test-Path "$novaConsoleTarget\package.json") {
        Push-Location $novaConsoleTarget
        npm install
        Pop-Location
        Write-NovaLiftSuccess "NovaConsole dependencies installed"
    }
}

# Create configuration files
function New-NovaFuseConfiguration {
    Write-NovaLiftInfo "Creating NovaFuse configuration..."
    
    $configPath = "$InstallPath\Config"
    
    # Main configuration
    $mainConfig = @{
        novafuse = @{
            version = "1.0.0"
            install_path = $InstallPath
            mode = $Mode
            components = @{
                novacore = @{
                    enabled = $true
                    path = "$InstallPath\NovaCore"
                    port = 8000
                }
                novaagent = @{
                    enabled = $true
                    path = "$InstallPath\NovaAgent"
                    executable = "nova-agent.exe"
                }
                novabridge = @{
                    enabled = $true
                    path = "$InstallPath\NovaBridge"
                    port = 8001
                }
                novaconsole = @{
                    enabled = $true
                    path = "$InstallPath\NovaConsole"
                    port = 3000
                }
            }
        }
    }
    
    $mainConfig | ConvertTo-Json -Depth 10 | Out-File "$configPath\novafuse-config.json" -Encoding UTF8
    
    # NovaLift configuration
    $novaliftConfig = @{
        novalift = @{
            version = "1.0.0"
            mode = $Mode
            auto_start = $true
            monitoring = @{
                enabled = $true
                interval = 30
                health_checks = $true
            }
            logging = @{
                level = "INFO"
                path = "$InstallPath\Logs"
                max_size = "100MB"
            }
        }
    }
    
    $novaliftConfig | ConvertTo-Json -Depth 10 | Out-File "$configPath\novalift-config.json" -Encoding UTF8
    
    Write-NovaLiftSuccess "Configuration files created"
}

# Create Windows services
function New-NovaFuseServices {
    Write-NovaLiftInfo "Creating NovaFuse Windows services..."
    
    # Create NovaAgent service
    $serviceScript = @"
# NovaFuse Service Wrapper
`$serviceName = "NovaFuseAgent"
`$serviceDisplayName = "NovaFuse Agent Service"
`$serviceDescription = "NovaFuse Coherence Operating System Agent"
`$servicePath = "$InstallPath\NovaAgent\nova-agent.exe"

if (Get-Service `$serviceName -ErrorAction SilentlyContinue) {
    Stop-Service `$serviceName -Force
    Remove-Service `$serviceName
}

New-Service -Name `$serviceName -DisplayName `$serviceDisplayName -Description `$serviceDescription -BinaryPathName `$servicePath -StartupType Automatic
Start-Service `$serviceName
"@
    
    $serviceScript | Out-File "$InstallPath\NovaLift\install-service.ps1" -Encoding UTF8
    
    Write-NovaLiftSuccess "Service scripts created"
}

# Create desktop shortcuts
function New-NovaFuseShortcuts {
    Write-NovaLiftInfo "Creating desktop shortcuts..."
    
    $desktopPath = [Environment]::GetFolderPath("Desktop")
    $shell = New-Object -ComObject WScript.Shell
    
    # NovaFuse Console shortcut
    $shortcut = $shell.CreateShortcut("$desktopPath\NovaFuse Console.lnk")
    $shortcut.TargetPath = "http://localhost:3000"
    $shortcut.Description = "NovaFuse Platform Console"
    $shortcut.Save()
    
    Write-NovaLiftSuccess "Desktop shortcuts created"
}

# Main installation function
function Install-NovaLift {
    Show-NovaLiftBanner
    
    Write-NovaLiftInfo "Starting NovaLift installation..."
    Write-NovaLiftInfo "Install Path: $InstallPath"
    Write-NovaLiftInfo "Mode: $Mode"
    
    # Check prerequisites
    if (-not (Test-Prerequisites)) {
        Write-NovaLiftError "Prerequisites check failed. Installation aborted."
        return $false
    }
    
    # Install dependencies
    Install-Dependencies
    
    # Create directories
    New-NovaFuseDirectories
    
    # Install components
    Install-NovaCore
    Install-NovaAgent
    Install-NovaBridge
    Install-NovaConsole
    
    # Create configuration
    New-NovaFuseConfiguration
    
    # Create services
    New-NovaFuseServices
    
    # Create shortcuts
    New-NovaFuseShortcuts
    
    Write-NovaLiftSuccess "NovaLift installation completed successfully!"
    Write-Host ""
    Write-Host "🎯 Next Steps:" -ForegroundColor Yellow
    Write-Host "  1. Run: $InstallPath\NovaAgent\nova-agent.exe" -ForegroundColor White
    Write-Host "  2. Open: http://localhost:3000 (NovaFuse Console)" -ForegroundColor White
    Write-Host "  3. Check: $InstallPath\Logs for system logs" -ForegroundColor White
    Write-Host ""
    Write-Host "🚀 NovaFuse Coherence Operating System is ready!" -ForegroundColor Green
    
    return $true
}

# Uninstall function
function Uninstall-NovaLift {
    Write-NovaLiftInfo "Uninstalling NovaLift..."
    
    # Stop services
    try {
        Stop-Service "NovaFuseAgent" -Force -ErrorAction SilentlyContinue
        Remove-Service "NovaFuseAgent" -ErrorAction SilentlyContinue
    } catch {
        # Service doesn't exist
    }
    
    # Remove installation directory
    if (Test-Path $InstallPath) {
        Remove-Item -Path $InstallPath -Recurse -Force
        Write-NovaLiftSuccess "Installation directory removed"
    }
    
    # Remove shortcuts
    $desktopPath = [Environment]::GetFolderPath("Desktop")
    if (Test-Path "$desktopPath\NovaFuse Console.lnk") {
        Remove-Item -Path "$desktopPath\NovaFuse Console.lnk" -Force
    }
    
    Write-NovaLiftSuccess "NovaLift uninstalled successfully"
}

# Main execution
if ($args[0] -eq "uninstall") {
    Uninstall-NovaLift
} else {
    Install-NovaLift
}
