/**
 * NovaFuse Universal API Connector - Async Handler Tests
 * 
 * This module tests the async handler utilities for the UAC.
 */

const { asyncHandler, retryWithBackoff, circuitBreaker } = require('../../src/utils/async-handler');

describe('Async Handler', () => {
  describe('asyncHandler', () => {
    it('should pass the result to the next middleware on success', async () => {
      const req = {};
      const res = {};
      const next = jest.fn();
      
      const handler = asyncHandler(async (req, res) => {
        return 'success';
      });
      
      await handler(req, res, next);
      
      expect(next).not.toHaveBeenCalled();
    });
    
    it('should pass the error to the next middleware on failure', async () => {
      const req = {};
      const res = {};
      const next = jest.fn();
      const error = new Error('Test error');
      
      const handler = asyncHandler(async (req, res) => {
        throw error;
      });
      
      await handler(req, res, next);
      
      expect(next).toHaveBeenCalledWith(error);
    });
  });
  
  describe('retryWithBackoff', () => {
    beforeEach(() => {
      jest.useFakeTimers();
    });
    
    afterEach(() => {
      jest.useRealTimers();
    });
    
    it('should return the result if the function succeeds on first try', async () => {
      const fn = jest.fn().mockResolvedValue('success');
      
      const promise = retryWithBackoff(fn);
      
      await expect(promise).resolves.toBe('success');
      expect(fn).toHaveBeenCalledTimes(1);
    });
    
    it('should retry the function if it fails', async () => {
      const error = new Error('Test error');
      const fn = jest.fn()
        .mockRejectedValueOnce(error)
        .mockResolvedValueOnce('success');
      
      const promise = retryWithBackoff(fn, { initialDelay: 100 });
      
      // Fast-forward time to trigger retry
      jest.advanceTimersByTime(200);
      
      await expect(promise).resolves.toBe('success');
      expect(fn).toHaveBeenCalledTimes(2);
    });
    
    it('should throw the error if max retries is reached', async () => {
      const error = new Error('Test error');
      const fn = jest.fn().mockRejectedValue(error);
      
      const promise = retryWithBackoff(fn, { maxRetries: 2, initialDelay: 100 });
      
      // Fast-forward time to trigger retries
      jest.advanceTimersByTime(300);
      
      await expect(promise).rejects.toThrow(error);
      expect(fn).toHaveBeenCalledTimes(3); // Initial + 2 retries
    });
    
    it('should respect shouldRetry function', async () => {
      const retryError = new Error('Retry error');
      const noRetryError = new Error('No retry error');
      const fn = jest.fn()
        .mockRejectedValueOnce(retryError)
        .mockRejectedValueOnce(noRetryError);
      
      const shouldRetry = jest.fn(error => error === retryError);
      
      const promise = retryWithBackoff(fn, { 
        maxRetries: 2, 
        initialDelay: 100,
        shouldRetry
      });
      
      // Fast-forward time to trigger retry
      jest.advanceTimersByTime(200);
      
      await expect(promise).rejects.toThrow(noRetryError);
      expect(fn).toHaveBeenCalledTimes(2);
      expect(shouldRetry).toHaveBeenCalledTimes(2);
    });
  });
  
  describe('circuitBreaker', () => {
    it('should pass through the result if the function succeeds', async () => {
      const fn = jest.fn().mockResolvedValue('success');
      
      const protectedFn = circuitBreaker(fn);
      
      await expect(protectedFn()).resolves.toBe('success');
      expect(fn).toHaveBeenCalledTimes(1);
    });
    
    it('should pass through the error if the function fails but threshold not reached', async () => {
      const error = new Error('Test error');
      const fn = jest.fn().mockRejectedValue(error);
      
      const protectedFn = circuitBreaker(fn, { failureThreshold: 2 });
      
      await expect(protectedFn()).rejects.toThrow(error);
      expect(fn).toHaveBeenCalledTimes(1);
    });
    
    it('should open the circuit after threshold failures', async () => {
      const error = new Error('Test error');
      const fn = jest.fn().mockRejectedValue(error);
      
      const protectedFn = circuitBreaker(fn, { failureThreshold: 2 });
      
      // First failure
      await expect(protectedFn()).rejects.toThrow(error);
      
      // Second failure - should open the circuit
      await expect(protectedFn()).rejects.toThrow(error);
      
      // Third call - should fail with circuit open error
      await expect(protectedFn()).rejects.toThrow('Circuit is open');
      
      expect(fn).toHaveBeenCalledTimes(2);
    });
    
    it('should reset failures after a successful call', async () => {
      const error = new Error('Test error');
      const fn = jest.fn()
        .mockRejectedValueOnce(error)
        .mockResolvedValueOnce('success')
        .mockRejectedValueOnce(error);
      
      const protectedFn = circuitBreaker(fn, { failureThreshold: 2 });
      
      // First failure
      await expect(protectedFn()).rejects.toThrow(error);
      
      // Success - should reset failures
      await expect(protectedFn()).resolves.toBe('success');
      
      // Another failure - should not open the circuit yet
      await expect(protectedFn()).rejects.toThrow(error);
      
      expect(fn).toHaveBeenCalledTimes(3);
    });
    
    it('should respect isFailure function', async () => {
      const retryError = new Error('Retry error');
      const noRetryError = new Error('No retry error');
      
      const fn = jest.fn()
        .mockRejectedValueOnce(retryError)
        .mockRejectedValueOnce(retryError)
        .mockRejectedValueOnce(noRetryError);
      
      const isFailure = jest.fn(error => error === retryError);
      
      const protectedFn = circuitBreaker(fn, { 
        failureThreshold: 2,
        isFailure
      });
      
      // First failure (counts)
      await expect(protectedFn()).rejects.toThrow(retryError);
      
      // Second failure (counts) - should open the circuit
      await expect(protectedFn()).rejects.toThrow(retryError);
      
      // Third call - should fail with circuit open error
      await expect(protectedFn()).rejects.toThrow('Circuit is open');
      
      expect(fn).toHaveBeenCalledTimes(2);
      expect(isFailure).toHaveBeenCalledTimes(2);
    });
  });
});
