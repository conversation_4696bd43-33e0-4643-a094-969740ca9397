/**
 * NovaVisionIntegrationTest.js
 * 
 * This module provides tests for the integration between NovaDNA and NovaVision.
 */

const assert = require('assert');
const NovaVisionComponents = require('../../ui/NovaVisionComponents');
const NovaVisionIntegration = require('../../ui/integration/NovaVisionIntegration');

/**
 * Test suite for NovaVision integration
 */
describe('NovaVision Integration Tests', () => {
  let novaVisionComponents;
  let novaVisionIntegration;
  
  // Set up test environment
  beforeEach(() => {
    // Initialize NovaVisionComponents
    novaVisionComponents = new NovaVisionComponents({
      baseUrl: '/novadna',
      theme: 'emergency'
    });
    
    // Initialize NovaVisionIntegration
    novaVisionIntegration = new NovaVisionIntegration({
      apiBaseUrl: '/api',
      novaVisionComponents
    });
  });
  
  /**
   * Test emergency access UI schema
   */
  describe('Emergency Access UI', () => {
    it('should generate a valid emergency access UI schema', () => {
      // Get emergency access UI schema
      const schema = novaVisionIntegration.getEmergencyAccessUI();
      
      // Verify schema structure
      assert.strictEqual(typeof schema, 'object', 'Schema should be an object');
      assert.strictEqual(schema.title, 'Emergency Medical Access', 'Schema should have correct title');
      assert.ok(Array.isArray(schema.sections), 'Schema should have sections array');
      
      // Verify scanner section
      const scannerSection = schema.sections.find(section => section.id === 'scanner');
      assert.ok(scannerSection, 'Schema should have scanner section');
      assert.strictEqual(scannerSection.title, 'Scan QR Code or NFC Tag', 'Scanner section should have correct title');
      
      // Verify manual entry section
      const manualEntrySection = schema.sections.find(section => section.id === 'manualEntry');
      assert.ok(manualEntrySection, 'Schema should have manual entry section');
      assert.strictEqual(manualEntrySection.title, 'Manual Entry', 'Manual entry section should have correct title');
      
      // Verify API endpoints
      const qrScanner = scannerSection.components.find(component => component.type === 'qrScanner');
      assert.strictEqual(qrScanner.onScan.url, '/api/access/emergency', 'QR scanner should use correct API endpoint');
      
      const submitAction = manualEntrySection.actions.find(action => action.id === 'submit');
      assert.strictEqual(submitAction.url, '/api/access/emergency', 'Submit action should use correct API endpoint');
      
      // Verify context collection
      assert.ok(schema.contextCollection, 'Schema should have context collection');
      assert.ok(schema.contextCollection.enabled, 'Context collection should be enabled');
      assert.ok(Array.isArray(schema.contextCollection.fields), 'Context collection should have fields array');
      
      // Verify emergency type field
      const emergencyTypeField = schema.contextCollection.fields.find(field => field.id === 'emergencyType');
      assert.ok(emergencyTypeField, 'Context collection should have emergency type field');
      assert.strictEqual(emergencyTypeField.type, 'select', 'Emergency type field should be a select field');
      assert.ok(Array.isArray(emergencyTypeField.options), 'Emergency type field should have options array');
    });
  });
  
  /**
   * Test emergency override UI schema
   */
  describe('Emergency Override UI', () => {
    it('should generate a valid emergency override UI schema', () => {
      // Get emergency override UI schema
      const schema = novaVisionIntegration.getEmergencyOverrideUI();
      
      // Verify schema structure
      assert.strictEqual(typeof schema, 'object', 'Schema should be an object');
      assert.strictEqual(schema.title, 'Emergency Override Access', 'Schema should have correct title');
      assert.strictEqual(schema.submitUrl, '/api/access/override', 'Schema should have correct submit URL');
      assert.ok(Array.isArray(schema.sections), 'Schema should have sections array');
      
      // Verify override info section
      const overrideInfoSection = schema.sections.find(section => section.id === 'overrideInfo');
      assert.ok(overrideInfoSection, 'Schema should have override info section');
      assert.strictEqual(overrideInfoSection.title, 'Override Information', 'Override info section should have correct title');
      
      // Verify fields
      const profileIdField = overrideInfoSection.fields.find(field => field.id === 'profileId');
      assert.ok(profileIdField, 'Override info section should have profile ID field');
      assert.strictEqual(profileIdField.type, 'text', 'Profile ID field should be a text field');
      assert.strictEqual(profileIdField.required, true, 'Profile ID field should be required');
      
      const reasonField = overrideInfoSection.fields.find(field => field.id === 'reason');
      assert.ok(reasonField, 'Override info section should have reason field');
      assert.strictEqual(reasonField.type, 'textarea', 'Reason field should be a textarea field');
      
      // Verify actions
      assert.ok(Array.isArray(schema.actions), 'Schema should have actions array');
      const submitAction = schema.actions.find(action => action.id === 'submit');
      assert.ok(submitAction, 'Schema should have submit action');
      assert.strictEqual(submitAction.label, 'Request Override', 'Submit action should have correct label');
      assert.strictEqual(submitAction.primary, true, 'Submit action should be primary');
    });
  });
  
  /**
   * Test profile view UI schema
   */
  describe('Profile View UI', () => {
    it('should generate a valid profile view UI schema with basic access level', () => {
      // Get profile view UI schema with basic access level
      const schema = novaVisionIntegration.getProfileViewUI('basic');
      
      // Verify schema structure
      assert.strictEqual(typeof schema, 'object', 'Schema should be an object');
      assert.strictEqual(schema.title, 'Emergency Medical Profile', 'Schema should have correct title');
      assert.strictEqual(schema.dataUrl, '/api/profiles/{profileId}', 'Schema should have correct data URL');
      assert.ok(Array.isArray(schema.sections), 'Schema should have sections array');
      
      // Verify sections for basic access level
      const sectionIds = schema.sections.map(section => section.id);
      assert.ok(sectionIds.includes('demographics'), 'Schema should include demographics section');
      assert.ok(sectionIds.includes('emergencyContacts'), 'Schema should include emergency contacts section');
      assert.ok(!sectionIds.includes('medications'), 'Schema should not include medications section for basic access');
      assert.ok(!sectionIds.includes('allergies'), 'Schema should not include allergies section for basic access');
    });
    
    it('should generate a valid profile view UI schema with standard access level', () => {
      // Get profile view UI schema with standard access level
      const schema = novaVisionIntegration.getProfileViewUI('standard');
      
      // Verify schema structure
      assert.strictEqual(typeof schema, 'object', 'Schema should be an object');
      assert.ok(Array.isArray(schema.sections), 'Schema should have sections array');
      
      // Verify sections for standard access level
      const sectionIds = schema.sections.map(section => section.id);
      assert.ok(sectionIds.includes('demographics'), 'Schema should include demographics section');
      assert.ok(sectionIds.includes('emergencyContacts'), 'Schema should include emergency contacts section');
      assert.ok(sectionIds.includes('medications'), 'Schema should include medications section for standard access');
      assert.ok(sectionIds.includes('allergies'), 'Schema should include allergies section for standard access');
      assert.ok(!sectionIds.includes('insuranceInfo'), 'Schema should not include insurance info section for standard access');
      
      // Verify emergency actions
      assert.ok(Array.isArray(schema.actions), 'Schema should have actions array');
      const emergencyAction = schema.actions.find(action => action.id === 'emergency');
      assert.ok(emergencyAction, 'Schema should have emergency action for standard access');
      assert.ok(Array.isArray(emergencyAction.actions), 'Emergency action should have sub-actions');
    });
    
    it('should generate a valid profile view UI schema with full access level', () => {
      // Get profile view UI schema with full access level
      const schema = novaVisionIntegration.getProfileViewUI('full');
      
      // Verify schema structure
      assert.strictEqual(typeof schema, 'object', 'Schema should be an object');
      assert.ok(Array.isArray(schema.sections), 'Schema should have sections array');
      
      // Verify sections for full access level
      const sectionIds = schema.sections.map(section => section.id);
      assert.ok(sectionIds.includes('demographics'), 'Schema should include demographics section');
      assert.ok(sectionIds.includes('emergencyContacts'), 'Schema should include emergency contacts section');
      assert.ok(sectionIds.includes('medications'), 'Schema should include medications section for full access');
      assert.ok(sectionIds.includes('allergies'), 'Schema should include allergies section for full access');
      assert.ok(sectionIds.includes('insuranceInfo'), 'Schema should include insurance info section for full access');
      assert.ok(sectionIds.includes('notes'), 'Schema should include notes section for full access');
      
      // Verify emergency actions
      assert.ok(Array.isArray(schema.actions), 'Schema should have actions array');
      const emergencyAction = schema.actions.find(action => action.id === 'emergency');
      assert.ok(emergencyAction, 'Schema should have emergency action for full access');
      assert.ok(Array.isArray(emergencyAction.actions), 'Emergency action should have sub-actions');
    });
  });
  
  /**
   * Test security dashboard UI schema
   */
  describe('Security Dashboard UI', () => {
    it('should generate a valid security dashboard UI schema', () => {
      // Get security dashboard UI schema
      const schema = novaVisionIntegration.getSecurityDashboardUI();
      
      // Verify schema structure
      assert.strictEqual(typeof schema, 'object', 'Schema should be an object');
      assert.strictEqual(schema.title, 'Security Monitoring', 'Schema should have correct title');
      assert.strictEqual(schema.dataUrl, '/api/security/dashboard', 'Schema should have correct data URL');
      assert.ok(Array.isArray(schema.sections), 'Schema should have sections array');
      
      // Verify sections
      const summarySection = schema.sections.find(section => section.id === 'summary');
      assert.ok(summarySection, 'Schema should have summary section');
      
      const incidentsSection = schema.sections.find(section => section.id === 'incidents');
      assert.ok(incidentsSection, 'Schema should have incidents section');
      
      const overridesSection = schema.sections.find(section => section.id === 'overrides');
      assert.ok(overridesSection, 'Schema should have overrides section');
      
      // Verify overrides table
      const overridesTable = overridesSection.components.find(component => component.id === 'overridesTable');
      assert.ok(overridesTable, 'Overrides section should have overrides table');
      assert.ok(Array.isArray(overridesTable.columns), 'Overrides table should have columns array');
      
      // Verify actions
      const actionsColumn = overridesTable.columns.find(column => column.id === 'actions');
      assert.ok(actionsColumn, 'Overrides table should have actions column');
      assert.ok(Array.isArray(actionsColumn.actions), 'Actions column should have actions array');
      
      const viewAction = actionsColumn.actions.find(action => action.id === 'view');
      assert.ok(viewAction, 'Actions column should have view action');
      assert.strictEqual(viewAction.url, '/overrides/{overrideId}', 'View action should have correct URL');
    });
  });
});

// If running directly (not through a test runner)
if (require.main === module) {
  // Simple test runner
  const runTests = async () => {
    console.log('Running NovaVision Integration Tests...');
    
    // Create test instances
    const novaVisionComponents = new NovaVisionComponents({
      baseUrl: '/novadna',
      theme: 'emergency'
    });
    
    const novaVisionIntegration = new NovaVisionIntegration({
      apiBaseUrl: '/api',
      novaVisionComponents
    });
    
    // Test emergency access UI
    console.log('\nTesting Emergency Access UI...');
    const emergencyAccessSchema = novaVisionIntegration.getEmergencyAccessUI();
    console.log('- Schema generated successfully');
    console.log(`- Title: ${emergencyAccessSchema.title}`);
    console.log(`- Sections: ${emergencyAccessSchema.sections.length}`);
    
    // Test emergency override UI
    console.log('\nTesting Emergency Override UI...');
    const emergencyOverrideSchema = novaVisionIntegration.getEmergencyOverrideUI();
    console.log('- Schema generated successfully');
    console.log(`- Title: ${emergencyOverrideSchema.title}`);
    console.log(`- Sections: ${emergencyOverrideSchema.sections.length}`);
    
    // Test profile view UI
    console.log('\nTesting Profile View UI...');
    const basicProfileSchema = novaVisionIntegration.getProfileViewUI('basic');
    console.log('- Basic schema generated successfully');
    console.log(`- Sections: ${basicProfileSchema.sections.length}`);
    
    const standardProfileSchema = novaVisionIntegration.getProfileViewUI('standard');
    console.log('- Standard schema generated successfully');
    console.log(`- Sections: ${standardProfileSchema.sections.length}`);
    
    const fullProfileSchema = novaVisionIntegration.getProfileViewUI('full');
    console.log('- Full schema generated successfully');
    console.log(`- Sections: ${fullProfileSchema.sections.length}`);
    
    // Test security dashboard UI
    console.log('\nTesting Security Dashboard UI...');
    const securityDashboardSchema = novaVisionIntegration.getSecurityDashboardUI();
    console.log('- Schema generated successfully');
    console.log(`- Title: ${securityDashboardSchema.title}`);
    console.log(`- Sections: ${securityDashboardSchema.sections.length}`);
    
    console.log('\nAll tests completed successfully!');
  };
  
  runTests().catch(error => {
    console.error('Test failed:', error);
    process.exit(1);
  });
}

module.exports = {
  testNovaVisionIntegration: () => {
    // Create test instances
    const novaVisionComponents = new NovaVisionComponents({
      baseUrl: '/novadna',
      theme: 'emergency'
    });
    
    const novaVisionIntegration = new NovaVisionIntegration({
      apiBaseUrl: '/api',
      novaVisionComponents
    });
    
    // Run basic tests
    const emergencyAccessSchema = novaVisionIntegration.getEmergencyAccessUI();
    const emergencyOverrideSchema = novaVisionIntegration.getEmergencyOverrideUI();
    const profileViewSchema = novaVisionIntegration.getProfileViewUI('standard');
    const securityDashboardSchema = novaVisionIntegration.getSecurityDashboardUI();
    
    return {
      success: true,
      schemas: {
        emergencyAccess: emergencyAccessSchema,
        emergencyOverride: emergencyOverrideSchema,
        profileView: profileViewSchema,
        securityDashboard: securityDashboardSchema
      }
    };
  }
};
