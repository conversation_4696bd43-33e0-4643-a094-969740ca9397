import React from 'react';
import { motion } from 'framer-motion';
import { Chart as ChartJS, ArcElement, Tooltip, Legend } from 'chart.js';
import { Pie } from 'react-chartjs-2';

ChartJS.register(ArcElement, Tooltip, Legend);

const ConsciousnessBrowser = () => {
    const [selectedCategory, setSelectedCategory] = React.useState('');
    const [searchQuery, setSearchQuery] = React.useState('');
    const [products, setProducts] = React.useState([]);
    const [loading, setLoading] = React.useState(false);

    React.useEffect(() => {
        // Initialize with sample data
        setProducts([
            { id: 1, name: 'Conscious AI Assistant', category: 'AI Services', price: 99.99 },
            { id: 2, name: 'Ethical Computing Suite', category: 'Software', price: 199.99 },
            { id: 3, name: 'Triadic Optimization Tool', category: 'Tools', price: 299.99 }
        ]);
    }, []);

    const handleCategorySelect = (category) => {
        setSelectedCategory(category);
        // Filter products by category
        const filteredProducts = products.filter(p => p.category === category);
        setProducts(filteredProducts);
    };

    const handleSearch = (e) => {
        setSearchQuery(e.target.value);
        // Filter products by search query
        const filteredProducts = products.filter(p => 
            p.name.toLowerCase().includes(e.target.value.toLowerCase())
        );
        setProducts(filteredProducts);
    };

    const chartData = {
        labels: ['AI Services', 'Software', 'Tools'],
        datasets: [
            {
                data: [30, 45, 25],
                backgroundColor: ['#3b82f6', '#10b981', '#f59e0b'],
                hoverBackgroundColor: ['#2563eb', '#059669', '#d97706']
            }
        ]
    };

    return (
        <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="p-6 rounded-lg shadow-lg bg-white dark:bg-gray-800"
        >
            <div className="flex justify-between items-center mb-6">
                <div className="flex-1">
                    <h2 className="text-2xl font-bold mb-2">Consciousness Browser</h2>
                    <p className="text-gray-600 dark:text-gray-400">Explore products with ethical considerations</p>
                </div>
                <div className="w-48">
                    <Pie data={chartData} />
                </div>
            </div>

            <div className="mb-6">
                <select
                    value={selectedCategory}
                    onChange={(e) => handleCategorySelect(e.target.value)}
                    className="w-full p-2 border rounded-lg"
                >
                    <option value="">All Categories</option>
                    <option value="AI Services">AI Services</option>
                    <option value="Software">Software</option>
                    <option value="Tools">Tools</option>
                </select>
            </div>

            <div className="mb-6">
                <input
                    type="text"
                    placeholder="Search products..."
                    value={searchQuery}
                    onChange={handleSearch}
                    className="w-full p-2 border rounded-lg"
                />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {products.map((product) => (
                    <motion.div
                        key={product.id}
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 hover:shadow-lg transition-all"
                    >
                        <h3 className="text-lg font-semibold mb-2">{product.name}</h3>
                        <p className="text-gray-600 dark:text-gray-400">Category: {product.category}</p>
                        <p className="text-gray-600 dark:text-gray-400">Price: ${product.price.toFixed(2)}</p>
                    </motion.div>
                ))}
            </div>
        </motion.div>
    );
};

export default ConsciousnessBrowser;
