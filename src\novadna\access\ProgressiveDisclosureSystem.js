/**
 * ProgressiveDisclosureSystem.js
 * 
 * This module implements a tiered information disclosure model for emergency medical data.
 * It ensures that only the appropriate level of information is disclosed based on
 * the emergency context, need-to-know, and authentication level.
 */

const { v4: uuidv4 } = require('uuid');
const EventEmitter = require('events');

/**
 * ProgressiveDisclosureSystem class for managing tiered information disclosure
 */
class ProgressiveDisclosureSystem extends EventEmitter {
  constructor(options = {}) {
    super();
    this.accessLevels = options.accessLevels || this._initializeAccessLevels();
    this.contextFactors = options.contextFactors || this._initializeContextFactors();
    this.disclosureRules = options.disclosureRules || this._initializeDisclosureRules();
    this.accessLog = [];
    this.maxLogSize = options.maxLogSize || 1000;
    this.needToKnowThreshold = options.needToKnowThreshold || 0.7;
  }

  /**
   * Determine the appropriate disclosure level for a profile
   * @param {Object} profile - The full profile
   * @param {Object} context - The emergency context
   * @param {Object} authentication - The authentication information
   * @returns {Object} - The disclosure result with filtered profile
   */
  determineDisclosureLevel(profile, context, authentication) {
    if (!profile || !profile.profileId) {
      throw new Error('Invalid profile');
    }

    if (!context || !context.emergencyType) {
      throw new Error('Invalid context: emergencyType is required');
    }

    if (!authentication || !authentication.serviceId) {
      throw new Error('Invalid authentication: serviceId is required');
    }

    // Calculate context score
    const contextScore = this._calculateContextScore(context);

    // Calculate need-to-know score
    const needToKnowScore = this._calculateNeedToKnowScore(context, authentication);

    // Calculate authentication strength
    const authStrength = this._calculateAuthenticationStrength(authentication);

    // Determine access level based on scores
    const accessLevel = this._determineAccessLevel(contextScore, needToKnowScore, authStrength);

    // Get disclosure rules for this access level
    const disclosureRule = this.disclosureRules.find(rule => rule.level === accessLevel);

    // Apply disclosure rules to profile
    const filteredProfile = this._applyDisclosureRules(profile, disclosureRule, context);

    // Log the access
    this._logAccess({
      profileId: profile.profileId,
      accessId: uuidv4(),
      timestamp: new Date().toISOString(),
      serviceId: authentication.serviceId,
      contextType: context.emergencyType,
      accessLevel,
      contextScore,
      needToKnowScore,
      authStrength,
      disclosedFields: disclosureRule.fields
    });

    return {
      accessLevel,
      contextScore,
      needToKnowScore,
      authStrength,
      profile: filteredProfile,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Elevate disclosure level for a specific emergency
   * @param {String} profileId - The profile ID
   * @param {Object} context - The updated emergency context
   * @param {Object} authentication - The authentication information
   * @param {String} reason - The reason for elevation
   * @returns {Object} - The elevation result
   */
  elevateDisclosureLevel(profileId, context, authentication, reason) {
    if (!profileId) {
      throw new Error('Profile ID is required');
    }

    if (!context || !context.emergencyType) {
      throw new Error('Invalid context: emergencyType is required');
    }

    if (!authentication || !authentication.serviceId) {
      throw new Error('Invalid authentication: serviceId is required');
    }

    if (!reason) {
      throw new Error('Reason for elevation is required');
    }

    // Set emergency severity to maximum if not already
    const elevatedContext = {
      ...context,
      emergencySeverity: 'CRITICAL'
    };

    // Set override flag in authentication
    const elevatedAuth = {
      ...authentication,
      override: true,
      overrideReason: reason
    };

    // Log the elevation
    const elevationId = uuidv4();
    this._logElevation({
      elevationId,
      profileId,
      timestamp: new Date().toISOString(),
      serviceId: authentication.serviceId,
      contextType: context.emergencyType,
      reason,
      requestedBy: authentication.userId || authentication.serviceId
    });

    // Emit elevation event
    this.emit('disclosure:elevated', {
      elevationId,
      profileId,
      serviceId: authentication.serviceId,
      reason
    });

    return {
      elevationId,
      status: 'APPROVED',
      timestamp: new Date().toISOString(),
      note: 'Disclosure level elevated due to emergency needs'
    };
  }

  /**
   * Get access logs for a profile
   * @param {String} profileId - The profile ID
   * @param {Object} options - Options for filtering logs
   * @returns {Array} - The access logs
   */
  getAccessLogs(profileId, options = {}) {
    if (!profileId) {
      throw new Error('Profile ID is required');
    }

    let filteredLogs = this.accessLog.filter(log => log.profileId === profileId);

    // Apply filters
    if (options.startDate) {
      const startTimestamp = new Date(options.startDate).getTime();
      filteredLogs = filteredLogs.filter(log => 
        new Date(log.timestamp).getTime() >= startTimestamp
      );
    }

    if (options.endDate) {
      const endTimestamp = new Date(options.endDate).getTime();
      filteredLogs = filteredLogs.filter(log => 
        new Date(log.timestamp).getTime() <= endTimestamp
      );
    }

    if (options.serviceId) {
      filteredLogs = filteredLogs.filter(log => log.serviceId === options.serviceId);
    }

    if (options.accessLevel) {
      filteredLogs = filteredLogs.filter(log => log.accessLevel === options.accessLevel);
    }

    // Sort by timestamp (newest first)
    filteredLogs.sort((a, b) => 
      new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    );

    // Apply limit
    if (options.limit) {
      filteredLogs = filteredLogs.slice(0, options.limit);
    }

    return filteredLogs;
  }

  /**
   * Get the disclosure rules
   * @returns {Array} - The disclosure rules
   */
  getDisclosureRules() {
    return this.disclosureRules;
  }

  /**
   * Update the disclosure rules
   * @param {Array} rules - The new disclosure rules
   * @returns {Boolean} - Whether the update was successful
   */
  updateDisclosureRules(rules) {
    if (!Array.isArray(rules) || rules.length === 0) {
      throw new Error('Invalid disclosure rules');
    }

    // Validate rules
    for (const rule of rules) {
      if (!rule.level || !rule.description || !Array.isArray(rule.fields)) {
        throw new Error('Invalid rule format');
      }
    }

    this.disclosureRules = rules;
    this.emit('rules:updated', { timestamp: new Date().toISOString() });
    return true;
  }

  /**
   * Initialize default access levels
   * @returns {Array} - The default access levels
   * @private
   */
  _initializeAccessLevels() {
    return [
      {
        level: 'MINIMAL',
        description: 'Basic identification only',
        threshold: 0.3
      },
      {
        level: 'BASIC',
        description: 'Basic medical information',
        threshold: 0.5
      },
      {
        level: 'STANDARD',
        description: 'Standard emergency information',
        threshold: 0.7
      },
      {
        level: 'EXTENDED',
        description: 'Extended medical information',
        threshold: 0.85
      },
      {
        level: 'FULL',
        description: 'Complete medical profile',
        threshold: 0.95
      }
    ];
  }

  /**
   * Initialize default context factors
   * @returns {Object} - The default context factors
   * @private
   */
  _initializeContextFactors() {
    return {
      emergencyTypes: {
        'MEDICAL': 0.8,
        'TRAUMA': 0.9,
        'CARDIAC': 0.95,
        'RESPIRATORY': 0.9,
        'NEUROLOGICAL': 0.85,
        'ALLERGIC': 0.8,
        'VEHICULAR': 0.75,
        'OTHER': 0.6
      },
      emergencySeverity: {
        'LOW': 0.5,
        'MODERATE': 0.7,
        'HIGH': 0.85,
        'CRITICAL': 1.0
      },
      responderTypes: {
        'PARAMEDIC': 0.9,
        'EMT': 0.85,
        'DOCTOR': 1.0,
        'NURSE': 0.9,
        'FIREFIGHTER': 0.8,
        'POLICE': 0.7,
        'OTHER': 0.6
      },
      locationTypes: {
        'HOSPITAL': 0.9,
        'AMBULANCE': 0.85,
        'EMERGENCY_SCENE': 0.8,
        'CLINIC': 0.75,
        'OTHER': 0.6
      }
    };
  }

  /**
   * Initialize default disclosure rules
   * @returns {Array} - The default disclosure rules
   * @private
   */
  _initializeDisclosureRules() {
    return [
      {
        level: 'MINIMAL',
        description: 'Basic identification only',
        fields: [
          'profileId',
          'fullName',
          'dateOfBirth',
          'bloodType'
        ]
      },
      {
        level: 'BASIC',
        description: 'Basic medical information',
        fields: [
          'profileId',
          'fullName',
          'dateOfBirth',
          'bloodType',
          'emergencyContacts',
          'dnr',
          'organDonor'
        ]
      },
      {
        level: 'STANDARD',
        description: 'Standard emergency information',
        fields: [
          'profileId',
          'fullName',
          'dateOfBirth',
          'bloodType',
          'emergencyContacts',
          'allergies',
          'medications',
          'medicalConditions',
          'dnr',
          'organDonor'
        ]
      },
      {
        level: 'EXTENDED',
        description: 'Extended medical information',
        fields: [
          'profileId',
          'fullName',
          'dateOfBirth',
          'bloodType',
          'emergencyContacts',
          'allergies',
          'medications',
          'medicalConditions',
          'dnr',
          'organDonor',
          'primaryCareProvider',
          'recentProcedures',
          'immunizations'
        ]
      },
      {
        level: 'FULL',
        description: 'Complete medical profile',
        fields: [
          'profileId',
          'fullName',
          'dateOfBirth',
          'bloodType',
          'emergencyContacts',
          'allergies',
          'medications',
          'medicalConditions',
          'dnr',
          'organDonor',
          'primaryCareProvider',
          'recentProcedures',
          'immunizations',
          'insuranceInfo',
          'medicalHistory',
          'familyHistory',
          'notes'
        ]
      }
    ];
  }

  /**
   * Calculate context score based on emergency context
   * @param {Object} context - The emergency context
   * @returns {Number} - The context score
   * @private
   */
  _calculateContextScore(context) {
    let score = 0;
    let factorCount = 0;

    // Emergency type factor
    if (context.emergencyType && this.contextFactors.emergencyTypes[context.emergencyType]) {
      score += this.contextFactors.emergencyTypes[context.emergencyType];
      factorCount++;
    } else {
      score += this.contextFactors.emergencyTypes.OTHER;
      factorCount++;
    }

    // Emergency severity factor
    if (context.emergencySeverity && this.contextFactors.emergencySeverity[context.emergencySeverity]) {
      score += this.contextFactors.emergencySeverity[context.emergencySeverity];
      factorCount++;
    } else {
      score += this.contextFactors.emergencySeverity.MODERATE;
      factorCount++;
    }

    // Responder type factor
    if (context.responderType && this.contextFactors.responderTypes[context.responderType]) {
      score += this.contextFactors.responderTypes[context.responderType];
      factorCount++;
    }

    // Location type factor
    if (context.locationType && this.contextFactors.locationTypes[context.locationType]) {
      score += this.contextFactors.locationTypes[context.locationType];
      factorCount++;
    }

    // Calculate average score
    return factorCount > 0 ? score / factorCount : 0.5;
  }

  /**
   * Calculate need-to-know score based on context and authentication
   * @param {Object} context - The emergency context
   * @param {Object} authentication - The authentication information
   * @returns {Number} - The need-to-know score
   * @private
   */
  _calculateNeedToKnowScore(context, authentication) {
    let score = 0;
    let factorCount = 0;

    // Emergency type relevance
    if (context.emergencyType) {
      // Different emergency types require different information
      switch (context.emergencyType) {
        case 'CARDIAC':
          score += context.relatedFactors && context.relatedFactors.includes('MEDICATIONS') ? 1.0 : 0.8;
          break;
        case 'ALLERGIC':
          score += context.relatedFactors && context.relatedFactors.includes('ALLERGIES') ? 1.0 : 0.8;
          break;
        case 'TRAUMA':
          score += context.relatedFactors && context.relatedFactors.includes('BLOOD_TYPE') ? 1.0 : 0.8;
          break;
        default:
          score += 0.7;
      }
      factorCount++;
    }

    // Responder role relevance
    if (authentication.role) {
      switch (authentication.role) {
        case 'DOCTOR':
          score += 1.0;
          break;
        case 'PARAMEDIC':
          score += 0.9;
          break;
        case 'NURSE':
          score += 0.85;
          break;
        case 'EMT':
          score += 0.8;
          break;
        default:
          score += 0.6;
      }
      factorCount++;
    }

    // Explicit information request
    if (context.requestedInformation && Array.isArray(context.requestedInformation)) {
      // Higher score if specific information is requested
      score += 0.8;
      factorCount++;
    }

    // Override flag
    if (authentication.override) {
      score += 1.0;
      factorCount++;
    }

    // Calculate average score
    return factorCount > 0 ? score / factorCount : 0.5;
  }

  /**
   * Calculate authentication strength
   * @param {Object} authentication - The authentication information
   * @returns {Number} - The authentication strength
   * @private
   */
  _calculateAuthenticationStrength(authentication) {
    let score = 0;
    let factorCount = 0;

    // Service verification
    if (authentication.serviceVerified) {
      score += 0.8;
      factorCount++;
    }

    // User verification
    if (authentication.userVerified) {
      score += 0.7;
      factorCount++;
    }

    // Multi-factor authentication
    if (authentication.mfaCompleted) {
      score += 0.9;
      factorCount++;
    }

    // Location verification
    if (authentication.locationVerified) {
      score += 0.7;
      factorCount++;
    }

    // Biometric verification
    if (authentication.biometricVerified) {
      score += 1.0;
      factorCount++;
    }

    // Token strength
    if (authentication.tokenStrength) {
      score += authentication.tokenStrength;
      factorCount++;
    } else {
      score += 0.5;
      factorCount++;
    }

    // Override authorization
    if (authentication.override) {
      score += 1.0;
      factorCount++;
    }

    // Calculate average score
    return factorCount > 0 ? score / factorCount : 0.5;
  }

  /**
   * Determine access level based on scores
   * @param {Number} contextScore - The context score
   * @param {Number} needToKnowScore - The need-to-know score
   * @param {Number} authStrength - The authentication strength
   * @returns {String} - The determined access level
   * @private
   */
  _determineAccessLevel(contextScore, needToKnowScore, authStrength) {
    // Calculate combined score
    // Weight: context (40%), need-to-know (30%), auth strength (30%)
    const combinedScore = (contextScore * 0.4) + (needToKnowScore * 0.3) + (authStrength * 0.3);

    // Find appropriate access level based on threshold
    for (let i = this.accessLevels.length - 1; i >= 0; i--) {
      if (combinedScore >= this.accessLevels[i].threshold) {
        return this.accessLevels[i].level;
      }
    }

    // Default to minimal access
    return 'MINIMAL';
  }

  /**
   * Apply disclosure rules to profile
   * @param {Object} profile - The full profile
   * @param {Object} disclosureRule - The disclosure rule to apply
   * @param {Object} context - The emergency context
   * @returns {Object} - The filtered profile
   * @private
   */
  _applyDisclosureRules(profile, disclosureRule, context) {
    const filteredProfile = {
      profileId: profile.profileId,
      schemaVersion: profile.schemaVersion
    };

    // Add fields based on disclosure rule
    for (const field of disclosureRule.fields) {
      if (profile[field] !== undefined) {
        filteredProfile[field] = profile[field];
      }
    }

    // Apply context-specific rules
    if (context.emergencyType === 'ALLERGIC' && profile.allergies) {
      // Ensure allergies are included and highlighted for allergic emergencies
      filteredProfile.allergies = profile.allergies;
      filteredProfile._highlighted = filteredProfile._highlighted || [];
      filteredProfile._highlighted.push('allergies');
    }

    if (context.emergencyType === 'CARDIAC' && profile.medications) {
      // Ensure medications are included and highlighted for cardiac emergencies
      filteredProfile.medications = profile.medications;
      filteredProfile._highlighted = filteredProfile._highlighted || [];
      filteredProfile._highlighted.push('medications');
    }

    // Add emergency-specific notes if available
    if (profile.emergencyNotes && profile.emergencyNotes[context.emergencyType]) {
      filteredProfile.emergencyNotes = {
        [context.emergencyType]: profile.emergencyNotes[context.emergencyType]
      };
    }

    return filteredProfile;
  }

  /**
   * Log access to a profile
   * @param {Object} logEntry - The log entry
   * @private
   */
  _logAccess(logEntry) {
    this.accessLog.push(logEntry);

    // Emit access event
    this.emit('disclosure:access', logEntry);

    // Limit log size
    if (this.accessLog.length > this.maxLogSize) {
      this.accessLog = this.accessLog.slice(-this.maxLogSize);
    }
  }

  /**
   * Log elevation of disclosure level
   * @param {Object} logEntry - The log entry
   * @private
   */
  _logElevation(logEntry) {
    this.accessLog.push({
      ...logEntry,
      type: 'ELEVATION'
    });

    // Emit elevation event
    this.emit('disclosure:elevation', logEntry);

    // Limit log size
    if (this.accessLog.length > this.maxLogSize) {
      this.accessLog = this.accessLog.slice(-this.maxLogSize);
    }
  }
}

module.exports = ProgressiveDisclosureSystem;
