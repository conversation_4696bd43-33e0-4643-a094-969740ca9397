/**
 * Swagger documentation for the ESG Metrics API
 */
const swaggerDocs = {
  tags: [
    {
      name: 'ESG Metrics',
      description: 'API endpoints for ESG metrics, data points, and initiatives'
    }
  ],
  components: {
    parameters: {
      page: {
        name: 'page',
        in: 'query',
        description: 'Page number for pagination',
        schema: {
          type: 'integer',
          default: 1,
          minimum: 1
        }
      },
      limit: {
        name: 'limit',
        in: 'query',
        description: 'Number of items per page',
        schema: {
          type: 'integer',
          default: 10,
          minimum: 1,
          maximum: 100
        }
      },
      sortBy: {
        name: 'sortBy',
        in: 'query',
        description: 'Field to sort by',
        schema: {
          type: 'string'
        }
      },
      sortOrder: {
        name: 'sortOrder',
        in: 'query',
        description: 'Sort order (asc or desc)',
        schema: {
          type: 'string',
          enum: ['asc', 'desc'],
          default: 'asc'
        }
      }
    },
    schemas: {
      ESGMetric: {
        type: 'object',
        properties: {
          id: {
            type: 'string',
            description: 'Unique identifier for the metric'
          },
          name: {
            type: 'string',
            description: 'Name of the metric'
          },
          description: {
            type: 'string',
            description: 'Description of the metric'
          },
          category: {
            type: 'string',
            enum: ['environmental', 'social', 'governance'],
            description: 'Category of the metric'
          },
          subcategory: {
            type: 'string',
            description: 'Subcategory of the metric'
          },
          unit: {
            type: 'string',
            description: 'Unit of measurement'
          },
          dataType: {
            type: 'string',
            enum: ['numeric', 'percentage', 'boolean', 'text', 'date'],
            description: 'Data type of the metric'
          },
          framework: {
            type: 'string',
            description: 'ESG framework the metric belongs to'
          },
          targetValue: {
            type: 'string',
            description: 'Target value for the metric'
          },
          targetDate: {
            type: 'string',
            format: 'date',
            description: 'Target date for achieving the target value'
          },
          owner: {
            type: 'string',
            description: 'Owner of the metric'
          },
          status: {
            type: 'string',
            enum: ['active', 'inactive', 'archived'],
            description: 'Status of the metric'
          },
          createdAt: {
            type: 'string',
            format: 'date-time',
            description: 'Creation timestamp'
          },
          updatedAt: {
            type: 'string',
            format: 'date-time',
            description: 'Last update timestamp'
          }
        }
      },
      ESGDataPoint: {
        type: 'object',
        properties: {
          id: {
            type: 'string',
            description: 'Unique identifier for the data point'
          },
          metricId: {
            type: 'string',
            description: 'ID of the associated metric'
          },
          value: {
            type: 'string',
            description: 'Value of the data point'
          },
          date: {
            type: 'string',
            format: 'date',
            description: 'Date of the data point'
          },
          period: {
            type: 'string',
            enum: ['daily', 'weekly', 'monthly', 'quarterly', 'annually'],
            description: 'Reporting period'
          },
          source: {
            type: 'string',
            description: 'Source of the data'
          },
          notes: {
            type: 'string',
            description: 'Additional notes'
          },
          verificationStatus: {
            type: 'string',
            enum: ['unverified', 'verified', 'rejected'],
            description: 'Verification status'
          },
          verifiedBy: {
            type: 'string',
            description: 'Person who verified the data point'
          },
          verifiedAt: {
            type: 'string',
            format: 'date-time',
            description: 'Verification timestamp'
          },
          createdAt: {
            type: 'string',
            format: 'date-time',
            description: 'Creation timestamp'
          },
          updatedAt: {
            type: 'string',
            format: 'date-time',
            description: 'Last update timestamp'
          }
        }
      },
      ESGInitiative: {
        type: 'object',
        properties: {
          id: {
            type: 'string',
            description: 'Unique identifier for the initiative'
          },
          name: {
            type: 'string',
            description: 'Name of the initiative'
          },
          description: {
            type: 'string',
            description: 'Description of the initiative'
          },
          category: {
            type: 'string',
            enum: ['environmental', 'social', 'governance'],
            description: 'Category of the initiative'
          },
          startDate: {
            type: 'string',
            format: 'date',
            description: 'Start date of the initiative'
          },
          endDate: {
            type: 'string',
            format: 'date',
            description: 'End date of the initiative'
          },
          status: {
            type: 'string',
            enum: ['planned', 'in-progress', 'completed', 'cancelled'],
            description: 'Status of the initiative'
          },
          owner: {
            type: 'string',
            description: 'Owner of the initiative'
          },
          budget: {
            type: 'number',
            description: 'Budget allocated for the initiative'
          },
          metrics: {
            type: 'array',
            items: {
              type: 'string'
            },
            description: 'IDs of metrics associated with the initiative'
          },
          goals: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                description: {
                  type: 'string',
                  description: 'Description of the goal'
                },
                targetDate: {
                  type: 'string',
                  format: 'date',
                  description: 'Target date for achieving the goal'
                },
                status: {
                  type: 'string',
                  enum: ['not-started', 'in-progress', 'completed', 'cancelled'],
                  description: 'Status of the goal'
                }
              }
            },
            description: 'Goals associated with the initiative'
          },
          createdAt: {
            type: 'string',
            format: 'date-time',
            description: 'Creation timestamp'
          },
          updatedAt: {
            type: 'string',
            format: 'date-time',
            description: 'Last update timestamp'
          }
        }
      },
      Pagination: {
        type: 'object',
        properties: {
          total: {
            type: 'integer',
            description: 'Total number of items'
          },
          page: {
            type: 'integer',
            description: 'Current page number'
          },
          limit: {
            type: 'integer',
            description: 'Number of items per page'
          },
          pages: {
            type: 'integer',
            description: 'Total number of pages'
          }
        }
      },
      Error: {
        type: 'object',
        properties: {
          error: {
            type: 'string',
            description: 'Error type'
          },
          message: {
            type: 'string',
            description: 'Error message'
          }
        }
      }
    },
    responses: {
      BadRequest: {
        description: 'Bad Request',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/Error'
            },
            example: {
              error: 'Bad Request',
              message: 'Invalid request parameters'
            }
          }
        }
      },
      Unauthorized: {
        description: 'Unauthorized',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/Error'
            },
            example: {
              error: 'Unauthorized',
              message: 'Authentication required'
            }
          }
        }
      },
      NotFound: {
        description: 'Not Found',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/Error'
            },
            example: {
              error: 'Not Found',
              message: 'Resource not found'
            }
          }
        }
      },
      InternalError: {
        description: 'Internal Server Error',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/Error'
            },
            example: {
              error: 'Internal Server Error',
              message: 'An unexpected error occurred'
            }
          }
        }
      }
    },
    securitySchemes: {
      ApiKeyAuth: {
        type: 'apiKey',
        in: 'header',
        name: 'X-API-Key'
      }
    }
  }
};

module.exports = swaggerDocs;
