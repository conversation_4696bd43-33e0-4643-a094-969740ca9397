<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NovaFuse Master Control Hub - $10-20B Coherence-Native Computing Platform</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .coherence-glow {
            box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
        }
        .nova-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .status-online { color: #10b981; }
        .status-warning { color: #f59e0b; }
        .status-offline { color: #ef4444; }
        .metric-card {
            transition: all 0.3s ease;
        }
        .metric-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
        }
        .component-status {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .psi-indicator {
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        .coherence-meter {
            background: linear-gradient(90deg, #10b981 0%, #ffd700 50%, #ef4444 100%);
            height: 4px;
            border-radius: 2px;
        }
    </style>
</head>
<body class="gradient-bg min-h-screen text-white">
    <!-- Header -->
    <header class="p-6 border-b border-white/20">
        <div class="max-w-7xl mx-auto flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="coherence-glow p-3 rounded-full bg-white/10">
                    <i data-lucide="brain-circuit" class="w-8 h-8"></i>
                </div>
                <div>
                    <h1 class="text-3xl font-bold">NovaFuse Master Control Hub</h1>
                    <p class="text-white/80">200+ Components • 300+ Tests • $10-20B Platform</p>
                </div>
            </div>
            <div class="flex items-center space-x-4">
                <div class="text-right">
                    <div class="text-sm text-white/80">Ecosystem Status</div>
                    <div class="flex items-center">
                        <span class="component-status bg-green-500"></span>
                        <span class="text-green-400 font-semibold">OPERATIONAL</span>
                    </div>
                </div>
                <div class="text-right">
                    <div class="text-sm text-white/80">Ψₛ Coherence</div>
                    <div class="flex items-center">
                        <span class="psi-indicator component-status"></span>
                        <span class="text-yellow-400 font-semibold">0.847</span>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Dashboard -->
    <main class="max-w-7xl mx-auto p-6">
        <!-- Ecosystem Overview -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-6 flex items-center">
                <i data-lucide="dashboard" class="w-6 h-6 mr-2"></i>
                Ecosystem Overview
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="nova-card p-6 rounded-lg metric-card">
                    <div class="flex items-center justify-between mb-4">
                        <i data-lucide="layers" class="w-8 h-8 text-blue-400"></i>
                        <span class="text-2xl font-bold">200+</span>
                    </div>
                    <h3 class="font-semibold mb-2">Major Components</h3>
                    <div class="consciousness-meter mb-2"></div>
                    <p class="text-sm text-white/80">Across 8 core categories</p>
                </div>
                
                <div class="nova-card p-6 rounded-lg metric-card">
                    <div class="flex items-center justify-between mb-4">
                        <i data-lucide="brain" class="w-8 h-8 text-purple-400"></i>
                        <span class="text-2xl font-bold">50+</span>
                    </div>
                    <h3 class="font-semibold mb-2">Coherence Engines</h3>
                    <div class="coherence-meter mb-2"></div>
                    <p class="text-sm text-white/80">Natural Intelligence systems</p>
                </div>
                
                <div class="nova-card p-6 rounded-lg metric-card">
                    <div class="flex items-center justify-between mb-4">
                        <i data-lucide="play-circle" class="w-8 h-8 text-green-400"></i>
                        <span class="text-2xl font-bold">93+</span>
                    </div>
                    <h3 class="font-semibold mb-2">Demo Systems</h3>
                    <div class="coherence-meter mb-2"></div>
                    <p class="text-sm text-white/80">Interactive demonstrations</p>
                </div>
                
                <div class="nova-card p-6 rounded-lg metric-card">
                    <div class="flex items-center justify-between mb-4">
                        <i data-lucide="check-circle" class="w-8 h-8 text-yellow-400"></i>
                        <span class="text-2xl font-bold">210+</span>
                    </div>
                    <h3 class="font-semibold mb-2">Test Frameworks</h3>
                    <div class="coherence-meter mb-2"></div>
                    <p class="text-sm text-white/80">Comprehensive validation</p>
                </div>
            </div>
        </section>

        <!-- Core Platforms Status -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-6 flex items-center">
                <i data-lucide="server" class="w-6 h-6 mr-2"></i>
                Core Nova Platforms (8 Systems)
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div class="nova-card p-4 rounded-lg">
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="font-semibold">NovaFuse</h3>
                        <span class="component-status bg-green-500"></span>
                    </div>
                    <p class="text-sm text-white/80 mb-2">Main GRC/API Marketplace</p>
                    <div class="text-xs text-white/60">API Gateway • CLI • Admin Console</div>
                </div>
                
                <div class="nova-card p-4 rounded-lg">
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="font-semibold">NovaConnect</h3>
                        <span class="component-status bg-green-500"></span>
                    </div>
                    <p class="text-sm text-white/80 mb-2">Universal API Connector</p>
                    <div class="text-xs text-white/60">Registry • Auth • Executor</div>
                </div>
                
                <div class="nova-card p-4 rounded-lg">
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="font-semibold">NovaCore</h3>
                        <span class="component-status bg-green-500"></span>
                    </div>
                    <p class="text-sm text-white/80 mb-2">Freemium Platform Foundation</p>
                    <div class="text-xs text-white/60">API • CLI • SDK • Tensor Runtime</div>
                </div>
                
                <div class="nova-card p-4 rounded-lg">
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="font-semibold">NovaShield</h3>
                        <span class="component-status bg-green-500"></span>
                    </div>
                    <p class="text-sm text-white/80 mb-2">Security-Focused Platform</p>
                    <div class="text-xs text-white/60">Trace-Guard • Bias Firewall</div>
                </div>
                
                <div class="nova-card p-4 rounded-lg">
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="font-semibold">NovaMatrix</h3>
                        <span class="component-status bg-green-500"></span>
                    </div>
                    <p class="text-sm text-white/80 mb-2">Pentagonal Coherence Fusion</p>
                    <div class="text-xs text-white/60">CIFRP Engine • Component Engines</div>
                </div>
                
                <div class="nova-card p-4 rounded-lg">
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="font-semibold">NovaAlign</h3>
                        <span class="component-status bg-green-500"></span>
                    </div>
                    <p class="text-sm text-white/80 mb-2">AI Alignment & Ethics</p>
                    <div class="text-xs text-white/60">Ethics Model • Governance</div>
                </div>
                
                <div class="nova-card p-4 rounded-lg">
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="font-semibold">NovaBridge</h3>
                        <span class="component-status bg-green-500"></span>
                    </div>
                    <p class="text-sm text-white/80 mb-2">Enterprise Connectors</p>
                    <div class="text-xs text-white/60">Chain Bridge • Connectors</div>
                </div>
                
                <div class="nova-card p-4 rounded-lg">
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="font-semibold">NovaLift</h3>
                        <span class="component-status bg-green-500"></span>
                    </div>
                    <p class="text-sm text-white/80 mb-2">Universal Enhancement</p>
                    <div class="text-xs text-white/60">GCP Enhancer • DSC Config</div>
                </div>
            </div>
        </section>

        <!-- Coherence-Native Engines -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-6 flex items-center">
                <i data-lucide="brain" class="w-6 h-6 mr-2"></i>
                Coherence-Native Engines (15+ Systems)
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
                <div class="nova-card p-4 rounded-lg">
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="font-semibold text-sm">NERI</h3>
                        <span class="component-status bg-green-500"></span>
                    </div>
                    <p class="text-xs text-white/80">NovaFold Enhanced Robust Intelligence</p>
                </div>

                <div class="nova-card p-4 rounded-lg">
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="font-semibold text-sm">NECE</h3>
                        <span class="component-status bg-green-500"></span>
                    </div>
                    <p class="text-xs text-white/80">Natural Emergent Chemistry Engine</p>
                </div>

                <div class="nova-card p-4 rounded-lg">
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="font-semibold text-sm">CSME</h3>
                        <span class="component-status bg-green-500"></span>
                    </div>
                    <p class="text-xs text-white/80">Cyber Safety Medical Engine</p>
                </div>

                <div class="nova-card p-4 rounded-lg">
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="font-semibold text-sm">CSFE</h3>
                        <span class="component-status bg-green-500"></span>
                    </div>
                    <p class="text-xs text-white/80">Cyber Safety Financial Engine</p>
                </div>

                <div class="nova-card p-4 rounded-lg">
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="font-semibold text-sm">NovaDNA</h3>
                        <span class="component-status bg-green-500"></span>
                    </div>
                    <p class="text-xs text-white/80">Universal Identity System</p>
                </div>

                <div class="nova-card p-4 rounded-lg">
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="font-semibold text-sm">NovaCaia</h3>
                        <span class="component-status bg-green-500"></span>
                    </div>
                    <p class="text-xs text-white/80">Coherence Security</p>
                </div>

                <div class="nova-card p-4 rounded-lg">
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="font-semibold text-sm">NovaMemX</h3>
                        <span class="component-status bg-green-500"></span>
                    </div>
                    <p class="text-xs text-white/80">Eternal Memory Engine</p>
                </div>

                <div class="nova-card p-4 rounded-lg">
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="font-semibold text-sm">NovaFinX</h3>
                        <span class="component-status bg-green-500"></span>
                    </div>
                    <p class="text-xs text-white/80">Coherence Capital Engine</p>
                </div>

                <div class="nova-card p-4 rounded-lg">
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="font-semibold text-sm">NovaSTR-X</h3>
                        <span class="component-status bg-green-500"></span>
                    </div>
                    <p class="text-xs text-white/80">Wall Street Oracle</p>
                </div>

                <div class="nova-card p-4 rounded-lg">
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="font-semibold text-sm">NovaSentient</h3>
                        <span class="component-status bg-green-500"></span>
                    </div>
                    <p class="text-xs text-white/80">Coherence-Native AI</p>
                </div>
            </div>
        </section>

        <!-- Blockchain & Financial Systems -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-6 flex items-center">
                <i data-lucide="link" class="w-6 h-6 mr-2"></i>
                Blockchain & Financial Systems (20+ Components)
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div class="nova-card p-4 rounded-lg">
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="font-semibold">KetherNet</h3>
                        <span class="component-status bg-green-500"></span>
                    </div>
                    <p class="text-sm text-white/80 mb-2">Coherence Blockchain</p>
                    <div class="text-xs text-white/60">446 RPS • P99: 644ms</div>
                </div>

                <div class="nova-card p-4 rounded-lg">
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="font-semibold">Coherium</h3>
                        <span class="component-status bg-green-500"></span>
                    </div>
                    <p class="text-sm text-white/80 mb-2">Coherence Currency</p>
                    <div class="text-xs text-white/60">Rewards • Valuation</div>
                </div>

                <div class="nova-card p-4 rounded-lg">
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="font-semibold">Aetherium</h3>
                        <span class="component-status bg-green-500"></span>
                    </div>
                    <p class="text-sm text-white/80 mb-2">Gas System</p>
                    <div class="text-xs text-white/60">Transaction Processing</div>
                </div>

                <div class="nova-card p-4 rounded-lg">
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="font-semibold">Chaeonix</h3>
                        <span class="component-status bg-green-500"></span>
                    </div>
                    <p class="text-sm text-white/80 mb-2">Divine Trading Engine</p>
                    <div class="text-xs text-white/60">MT5 Connector • Prophecy</div>
                </div>
            </div>
        </section>

        <!-- Demo Systems Hub -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-6 flex items-center">
                <i data-lucide="play-circle" class="w-6 h-6 mr-2"></i>
                Interactive Demo Systems (93+ Files)
            </h2>
            <div class="demo-grid">
                <div class="nova-card p-4 rounded-lg cursor-pointer hover:bg-white/20 transition-colors" onclick="launchDemo('coherence')">
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="font-semibold">Coherence Computing</h3>
                        <i data-lucide="brain-circuit" class="w-5 h-5 text-purple-400"></i>
                    </div>
                    <p class="text-sm text-white/80 mb-2">15+ coherence showcases</p>
                    <div class="text-xs text-white/60">NERI • NECE • CSME • NovaSentient</div>
                </div>

                <div class="nova-card p-4 rounded-lg cursor-pointer hover:bg-white/20 transition-colors" onclick="launchDemo('blockchain')">
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="font-semibold">Blockchain Demos</h3>
                        <i data-lucide="link" class="w-5 h-5 text-blue-400"></i>
                    </div>
                    <p class="text-sm text-white/80 mb-2">8+ cryptocurrency demos</p>
                    <div class="text-xs text-white/60">KetherNet • Coherium • Consensus</div>
                </div>

                <div class="nova-card p-4 rounded-lg cursor-pointer hover:bg-white/20 transition-colors" onclick="launchDemo('medical')">
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="font-semibold">Medical Demos</h3>
                        <i data-lucide="heart-pulse" class="w-5 h-5 text-red-400"></i>
                    </div>
                    <p class="text-sm text-white/80 mb-2">5+ healthcare showcases</p>
                    <div class="text-xs text-white/60">NovaFold • Protein Folding</div>
                </div>

                <div class="nova-card p-4 rounded-lg cursor-pointer hover:bg-white/20 transition-colors" onclick="launchDemo('financial')">
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="font-semibold">Financial Demos</h3>
                        <i data-lucide="trending-up" class="w-5 h-5 text-green-400"></i>
                    </div>
                    <p class="text-sm text-white/80 mb-2">15+ trading demonstrations</p>
                    <div class="text-xs text-white/60">NovaSTR-X • Chaeonix • Oracle</div>
                </div>

                <div class="nova-card p-4 rounded-lg cursor-pointer hover:bg-white/20 transition-colors" onclick="launchDemo('security')">
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="font-semibold">Security Demos</h3>
                        <i data-lucide="shield" class="w-5 h-5 text-yellow-400"></i>
                    </div>
                    <p class="text-sm text-white/80 mb-2">10+ security showcases</p>
                    <div class="text-xs text-white/60">NovaShield • NovaCaia • Trinity</div>
                </div>

                <div class="nova-card p-4 rounded-lg cursor-pointer hover:bg-white/20 transition-colors" onclick="launchDemo('visualization')">
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="font-semibold">Visualization Demos</h3>
                        <i data-lucide="eye" class="w-5 h-5 text-cyan-400"></i>
                    </div>
                    <p class="text-sm text-white/80 mb-2">20+ interface showcases</p>
                    <div class="text-xs text-white/60">NovaVision • Diagrams • UI</div>
                </div>
            </div>
        </section>

        <!-- Quick Actions -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-6 flex items-center">
                <i data-lucide="zap" class="w-6 h-6 mr-2"></i>
                Quick Actions
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <button class="nova-card p-6 rounded-lg text-left hover:bg-white/20 transition-colors" onclick="launchDemoHub()">
                    <i data-lucide="play" class="w-8 h-8 text-green-400 mb-4"></i>
                    <h3 class="font-semibold mb-2">Launch Demo Hub</h3>
                    <p class="text-sm text-white/80">Access all 93+ interactive demonstrations</p>
                </button>

                <button class="nova-card p-6 rounded-lg text-left hover:bg-white/20 transition-colors" onclick="runTestSuite()">
                    <i data-lucide="activity" class="w-8 h-8 text-blue-400 mb-4"></i>
                    <h3 class="font-semibold mb-2">Run Test Suite</h3>
                    <p class="text-sm text-white/80">Execute comprehensive validation tests</p>
                </button>

                <button class="nova-card p-6 rounded-lg text-left hover:bg-white/20 transition-colors" onclick="openConfiguration()">
                    <i data-lucide="settings" class="w-8 h-8 text-purple-400 mb-4"></i>
                    <h3 class="font-semibold mb-2">System Configuration</h3>
                    <p class="text-sm text-white/80">Configure ecosystem deployment settings</p>
                </button>
            </div>
        </section>
    </main>

    <!-- Demo Modal -->
    <div id="demoModal" class="fixed inset-0 bg-black/50 backdrop-blur-sm hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="nova-card max-w-4xl w-full max-h-[90vh] overflow-y-auto rounded-lg">
                <div class="p-6 border-b border-white/20">
                    <div class="flex items-center justify-between">
                        <h2 id="modalTitle" class="text-2xl font-bold">Demo System</h2>
                        <button onclick="closeDemoModal()" class="text-white/60 hover:text-white">
                            <i data-lucide="x" class="w-6 h-6"></i>
                        </button>
                    </div>
                </div>
                <div id="modalContent" class="p-6">
                    <!-- Dynamic content will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // Simulate real-time updates
        setInterval(() => {
            const coherenceValue = (0.8 + Math.random() * 0.2).toFixed(3);
            document.querySelector('.text-yellow-400.font-semibold').textContent = coherenceValue;
        }, 5000);

        // Demo system data
        const demoSystems = {
            coherence: {
                title: 'Coherence Computing Demos',
                demos: [
                    { name: 'NERI Enhanced Intelligence', file: 'neri-demo.html', status: 'active' },
                    { name: 'NECE Chemistry Engine', file: 'nece-demo.html', status: 'active' },
                    { name: 'CSME Medical Engine', file: 'csme-demo.html', status: 'active' },
                    { name: 'NovaSentient AI', file: 'novasentient-demo.html', status: 'active' },
                    { name: 'NovaMemX Memory Engine', file: 'novamemx-demo.html', status: 'active' },
                    { name: 'Coherence Simulation', file: 'N3C_Coherence_Simulation.py', status: 'active' },
                    { name: 'Trinity Coherence', file: 'trinity_coherence_simulation.py', status: 'active' },
                    { name: 'Advanced Coherence Demo', file: 'advanced_coherence_demo.py', status: 'active' }
                ]
            },
            blockchain: {
                title: 'Blockchain & Cryptocurrency Demos',
                demos: [
                    { name: 'KetherNet Blockchain', file: 'kethernet-demo.js', status: 'active' },
                    { name: 'Coherium Currency', file: 'test-coherium.js', status: 'active' },
                    { name: 'Aetherium Gas System', file: 'test-aetherium.js', status: 'active' },
                    { name: 'Crown Consensus', file: 'test-crown-consensus.js', status: 'active' },
                    { name: 'Transaction Processing', file: 'test-transactions.js', status: 'active' },
                    { name: 'KetherNet Load Test', file: 'kethernet-load-test.js', status: 'active' },
                    { name: 'Extreme Stress Test', file: 'kethernet-extreme-stress.js', status: 'active' }
                ]
            },
            medical: {
                title: 'Medical & Healthcare Demos',
                demos: [
                    { name: 'NovaFold Protein Folding', file: 'NovaFold_Live_Demo.py', status: 'active' },
                    { name: 'NovaFold Strategic Demo', file: 'NovaFold_Strategic_Demo.py', status: 'active' },
                    { name: 'Protein Folding Software', file: 'protein-folding-software.js', status: 'active' },
                    { name: 'NHETX Protein Oracle', file: 'nhetx-protein-folding-oracle.js', status: 'active' },
                    { name: 'Consciousness Protein Designer', file: 'consciousness-protein-designer.js', status: 'active' }
                ]
            },
            financial: {
                title: 'Financial Trading Demos',
                demos: [
                    { name: 'NovaSTR-X Wall Street Oracle', file: 'wall_street_oracle', status: 'active' },
                    { name: 'Chaeonix Divine Trading', file: 'divine_trader.py', status: 'active' },
                    { name: 'MT5 Simulation', file: 'test_mt5_simulation.py', status: 'active' },
                    { name: 'Forex Crypto Simple', file: 'test_forex_crypto_simple.py', status: 'active' },
                    { name: 'Volatility Smile Test', file: 'volatility_smile_test.py', status: 'active' },
                    { name: 'Financial Prediction Engine', file: 'LIVE-MARKET-PREDICTION-ENGINE.js', status: 'active' },
                    { name: 'Real Demo Trading', file: 'REAL-DEMO-TRADING-CONNECTOR.js', status: 'active' }
                ]
            },
            security: {
                title: 'Security & Identity Demos',
                demos: [
                    { name: 'NovaShield Platform', file: 'NovaShield_Demo_Script.md', status: 'active' },
                    { name: 'NovaCaia Security', file: 'NOVACAIA_TESTING_REPORT.md', status: 'active' },
                    { name: 'Trinity of Trust', file: 'trinity-of-trust-technical-specs.md', status: 'active' },
                    { name: 'Trace-Guard MVP', file: 'NovaShield_TraceGuard_MVP.py', status: 'active' },
                    { name: 'Quantum Coherence Firewall', file: 'quantum_coherence_firewall.py', status: 'active' },
                    { name: 'AI Coherence Boundary Test', file: 'ai_coherence_boundary_stress_test.py', status: 'active' }
                ]
            },
            visualization: {
                title: 'Visualization & Interface Demos',
                demos: [
                    { name: 'NovaVision Hub', file: 'trinity_visualization', status: 'active' },
                    { name: 'Patent Diagram Generator', file: 'comphyology-diagram-generator', status: 'active' },
                    { name: 'UUFT Diagrams', file: 'UUFT_Diagrams.html', status: 'active' },
                    { name: 'Strategic Framework Viewer', file: 'strategic-framework-viewer.html', status: 'active' },
                    { name: 'Unified Field Theory', file: 'unified-field-theory.html', status: 'active' },
                    { name: 'Alignment Architecture', file: 'alignment-architecture.html', status: 'active' },
                    { name: 'NECE Enhanced Dashboard', file: 'NECE-Enhanced-Dashboard.html', status: 'active' },
                    { name: 'NovaFold Enhanced Dashboard', file: 'NovaFold-Enhanced-Dashboard.html', status: 'active' }
                ]
            }
        };

        // Launch specific demo category
        function launchDemo(category) {
            const system = demoSystems[category];
            if (!system) return;

            document.getElementById('modalTitle').textContent = system.title;

            let content = '<div class="space-y-4">';
            system.demos.forEach(demo => {
                const statusColor = demo.status === 'active' ? 'text-green-400' : 'text-yellow-400';
                content += `
                    <div class="nova-card p-4 rounded-lg flex items-center justify-between">
                        <div>
                            <h3 class="font-semibold">${demo.name}</h3>
                            <p class="text-sm text-white/60">${demo.file}</p>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="component-status bg-green-500"></span>
                            <button class="px-3 py-1 bg-blue-600 hover:bg-blue-700 rounded text-sm transition-colors" onclick="runDemo('${demo.file}')">
                                Launch
                            </button>
                        </div>
                    </div>
                `;
            });
            content += '</div>';

            document.getElementById('modalContent').innerHTML = content;
            document.getElementById('demoModal').classList.remove('hidden');
            lucide.createIcons();
        }

        // Close demo modal
        function closeDemoModal() {
            document.getElementById('demoModal').classList.add('hidden');
        }

        // Run specific demo
        function runDemo(filename) {
            alert(`Launching demo: ${filename}\n\nThis would typically open the demo in a new window or execute the demo script.`);
        }

        // Launch demo hub
        function launchDemoHub() {
            document.getElementById('modalTitle').textContent = 'NovaFuse Demo Hub - 93+ Interactive Demonstrations';

            let content = '<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">';
            Object.keys(demoSystems).forEach(key => {
                const system = demoSystems[key];
                content += `
                    <div class="nova-card p-4 rounded-lg cursor-pointer hover:bg-white/20 transition-colors" onclick="launchDemo('${key}')">
                        <h3 class="font-semibold mb-2">${system.title}</h3>
                        <p class="text-sm text-white/80 mb-2">${system.demos.length} demonstrations available</p>
                        <div class="text-xs text-white/60">Click to explore →</div>
                    </div>
                `;
            });
            content += '</div>';

            document.getElementById('modalContent').innerHTML = content;
            document.getElementById('demoModal').classList.remove('hidden');
            lucide.createIcons();
        }

        // Run test suite
        function runTestSuite() {
            document.getElementById('modalTitle').textContent = 'NovaFuse Test Suite - 210+ Validation Frameworks';

            const testFrameworks = [
                { name: 'UUFT Testing Suite', files: '20+ files', status: 'active' },
                { name: 'Trinity Testing Framework', files: '10+ files', status: 'active' },
                { name: 'NovaConnect Testing', files: '50+ files', status: 'active' },
                { name: 'Compliance Testing', files: '15+ files', status: 'active' },
                { name: 'Performance Testing', files: '25+ files', status: 'active' },
                { name: 'Security Testing', files: '20+ files', status: 'active' },
                { name: 'Specialized Testing', files: '65+ files', status: 'active' }
            ];

            let content = '<div class="space-y-4">';
            testFrameworks.forEach(framework => {
                content += `
                    <div class="nova-card p-4 rounded-lg flex items-center justify-between">
                        <div>
                            <h3 class="font-semibold">${framework.name}</h3>
                            <p class="text-sm text-white/60">${framework.files}</p>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="component-status bg-green-500"></span>
                            <button class="px-3 py-1 bg-green-600 hover:bg-green-700 rounded text-sm transition-colors">
                                Run Tests
                            </button>
                        </div>
                    </div>
                `;
            });
            content += '</div>';

            document.getElementById('modalContent').innerHTML = content;
            document.getElementById('demoModal').classList.remove('hidden');
            lucide.createIcons();
        }

        // Open configuration
        function openConfiguration() {
            document.getElementById('modalTitle').textContent = 'NovaFuse System Configuration';

            const content = `
                <div class="space-y-6">
                    <div class="nova-card p-4 rounded-lg">
                        <h3 class="font-semibold mb-4">Deployment Settings</h3>
                        <div class="space-y-3">
                            <div class="flex items-center justify-between">
                                <span>Docker Containerization</span>
                                <span class="text-green-400">Enabled</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span>GCP Integration</span>
                                <span class="text-green-400">Active</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span>Monitoring & Logging</span>
                                <span class="text-green-400">Operational</span>
                            </div>
                        </div>
                    </div>

                    <div class="nova-card p-4 rounded-lg">
                        <h3 class="font-semibold mb-4">Consciousness Parameters</h3>
                        <div class="space-y-3">
                            <div class="flex items-center justify-between">
                                <span>Ψₛ Stability Threshold</span>
                                <span class="text-yellow-400">0.847</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span>∂Ψ=0 Enforcement</span>
                                <span class="text-green-400">Active</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span>Trinity Validation</span>
                                <span class="text-green-400">Enabled</span>
                            </div>
                        </div>
                    </div>

                    <div class="nova-card p-4 rounded-lg">
                        <h3 class="font-semibold mb-4">Quick Actions</h3>
                        <div class="grid grid-cols-2 gap-3">
                            <button class="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded transition-colors">
                                Deploy All Systems
                            </button>
                            <button class="px-4 py-2 bg-green-600 hover:bg-green-700 rounded transition-colors">
                                Run Health Check
                            </button>
                            <button class="px-4 py-2 bg-yellow-600 hover:bg-yellow-700 rounded transition-colors">
                                Update Configuration
                            </button>
                            <button class="px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded transition-colors">
                                Export Logs
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.getElementById('modalContent').innerHTML = content;
            document.getElementById('demoModal').classList.remove('hidden');
            lucide.createIcons();
        }

        // Close modal when clicking outside
        document.getElementById('demoModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeDemoModal();
            }
        });
    </script>
</body>
</html>
