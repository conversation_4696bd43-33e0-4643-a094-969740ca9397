/**
 * Consent Record Model
 * 
 * Represents a record of consent given by a data subject for processing
 * their personal data for specific purposes.
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const consentRecordSchema = new Schema({
  dataSubjectId: {
    type: String,
    trim: true
  },
  dataSubjectName: {
    type: String,
    required: true,
    trim: true
  },
  dataSubjectEmail: {
    type: String,
    required: true,
    trim: true
  },
  consentType: {
    type: String,
    required: true,
    enum: ['marketing', 'analytics', 'profiling', 'third_party', 'research', 'other'],
    trim: true
  },
  consentDetails: {
    type: String,
    required: true,
    trim: true
  },
  purposes: [{
    type: String,
    required: true,
    trim: true
  }],
  dataCategories: [{
    type: String,
    required: true,
    trim: true
  }],
  thirdParties: [{
    name: {
      type: String,
      required: true,
      trim: true
    },
    purpose: {
      type: String,
      required: true,
      trim: true
    },
    location: {
      type: String,
      trim: true
    }
  }],
  collectionMethod: {
    type: String,
    required: true,
    enum: ['web-form', 'mobile-app', 'paper-form', 'verbal', 'email', 'other'],
    trim: true
  },
  collectionLocation: {
    type: String,
    trim: true
  },
  collectionTimestamp: {
    type: Date,
    required: true
  },
  ipAddress: {
    type: String,
    trim: true
  },
  userAgent: {
    type: String,
    trim: true
  },
  formVersion: {
    type: String,
    trim: true
  },
  status: {
    type: String,
    required: true,
    enum: ['active', 'withdrawn', 'expired'],
    default: 'active'
  },
  withdrawalTimestamp: {
    type: Date
  },
  withdrawalMethod: {
    type: String,
    enum: ['web-form', 'mobile-app', 'paper-form', 'verbal', 'email', 'other'],
    trim: true
  },
  withdrawalReason: {
    type: String,
    trim: true
  },
  expiryDate: {
    type: Date
  },
  proofOfConsent: {
    type: String,
    trim: true
  },
  proofType: {
    type: String,
    enum: ['screenshot', 'form-submission', 'log-record', 'email', 'other'],
    trim: true
  },
  notes: [{
    content: {
      type: String,
      required: true,
      trim: true
    },
    createdBy: {
      type: String,
      required: true,
      trim: true
    },
    createdAt: {
      type: Date,
      default: Date.now
    }
  }],
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Create a text index for searching
consentRecordSchema.index({
  dataSubjectName: 'text',
  dataSubjectEmail: 'text',
  consentDetails: 'text'
});

// Pre-save hook to update the updatedAt field
consentRecordSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Virtual for expiry status
consentRecordSchema.virtual('isExpired').get(function() {
  if (!this.expiryDate) return false;
  if (this.status === 'withdrawn' || this.status === 'expired') return true;
  const now = new Date();
  const expiryDate = new Date(this.expiryDate);
  return now > expiryDate;
});

// Virtual for time remaining until expiry
consentRecordSchema.virtual('timeRemaining').get(function() {
  if (!this.expiryDate) return null;
  if (this.status === 'withdrawn' || this.status === 'expired') return 0;
  const now = new Date();
  const expiryDate = new Date(this.expiryDate);
  const diffTime = expiryDate - now;
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays > 0 ? diffDays : 0;
});

const ConsentRecord = mongoose.model('ConsentRecord', consentRecordSchema);

module.exports = ConsentRecord;
