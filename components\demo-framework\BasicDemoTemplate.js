import React, { useState } from 'react';
import Link from 'next/link';

/**
 * Basic Demo Template Component
 * 
 * A foundational template for NovaFuse demos that includes the four core pillars:
 * - UIs (User Interfaces)
 * - GRC APIs
 * - UAC (Universal API Connector)
 * - App Store
 * 
 * This template can be customized for different partners and demo types.
 */
const BasicDemoTemplate = ({
  title = "NovaFuse Demo",
  description = "Experience the power of NovaFuse",
  partnerInfo = null,
  demoType = "standard",
  children
}) => {
  const [activeTab, setActiveTab] = useState('overview');

  // Demo type configuration
  const demoConfig = {
    'war-room': {
      title: 'Live Compliance War Room',
      description: 'Real-time problem solving with NovaFuse engineers',
      icon: '🔥'
    },
    'executive': {
      title: 'Executive Briefing',
      description: 'Strategic overview for decision makers',
      icon: '📊'
    },
    'technical': {
      title: 'Technical Deep Dive',
      description: 'Detailed exploration of NovaFuse capabilities',
      icon: '⚙️'
    },
    'standard': {
      title: 'NovaFuse Demo',
      description: 'Experience the power of NovaFuse',
      icon: '🔌'
    }
  };

  const currentDemoConfig = demoConfig[demoType];

  return (
    <div className="demo-template bg-gray-900 text-white">
      {/* Header Section */}
      <div className="bg-gradient-to-r from-blue-900 to-indigo-900 text-white rounded-lg p-8 mb-8">
        {/* Demo Type Badge */}
        <div className="flex justify-center mb-4">
          <div className="bg-blue-800 bg-opacity-70 px-4 py-2 rounded-full text-sm font-bold inline-flex items-center">
            <span className="mr-2">{currentDemoConfig.icon}</span>
            <span>{currentDemoConfig.title.toUpperCase()}</span>
          </div>
        </div>
        
        {/* Partner-Specific Branding (if available) */}
        {partnerInfo && (
          <div className="flex justify-center items-center mb-6">
            <div className="bg-white bg-opacity-10 px-6 py-3 rounded-lg">
              <div className="flex items-center">
                {partnerInfo.logo && (
                  <img 
                    src={partnerInfo.logo} 
                    alt={`${partnerInfo.name} logo`} 
                    className="h-8 mr-3" 
                  />
                )}
                <div className="text-xl font-semibold">
                  {partnerInfo.tagline || `NovaFuse + ${partnerInfo.name}`}
                </div>
              </div>
            </div>
          </div>
        )}
        
        <h1 className="text-3xl md:text-4xl font-bold mb-4 text-center">
          {title}
        </h1>
        
        <p className="text-xl mb-6 text-center max-w-3xl mx-auto">
          {description}
        </p>
      </div>

      {/* Main Demo Content */}
      <div className="bg-gray-800 rounded-lg overflow-hidden mb-8">
        {/* Navigation Tabs */}
        <div className="bg-gray-700 px-4">
          <nav className="flex space-x-4 overflow-x-auto">
            <button
              className={`py-4 px-3 text-sm font-medium border-b-2 ${
                activeTab === 'overview'
                  ? 'border-blue-500 text-blue-500'
                  : 'border-transparent text-gray-400 hover:text-gray-300'
              }`}
              onClick={() => setActiveTab('overview')}
            >
              Overview
            </button>
            <button
              className={`py-4 px-3 text-sm font-medium border-b-2 ${
                activeTab === 'ui'
                  ? 'border-blue-500 text-blue-500'
                  : 'border-transparent text-gray-400 hover:text-gray-300'
              }`}
              onClick={() => setActiveTab('ui')}
            >
              UI Components
            </button>
            <button
              className={`py-4 px-3 text-sm font-medium border-b-2 ${
                activeTab === 'api'
                  ? 'border-blue-500 text-blue-500'
                  : 'border-transparent text-gray-400 hover:text-gray-300'
              }`}
              onClick={() => setActiveTab('api')}
            >
              GRC APIs
            </button>
            <button
              className={`py-4 px-3 text-sm font-medium border-b-2 ${
                activeTab === 'uac'
                  ? 'border-blue-500 text-blue-500'
                  : 'border-transparent text-gray-400 hover:text-gray-300'
              }`}
              onClick={() => setActiveTab('uac')}
            >
              UAC
            </button>
            <button
              className={`py-4 px-3 text-sm font-medium border-b-2 ${
                activeTab === 'appstore'
                  ? 'border-blue-500 text-blue-500'
                  : 'border-transparent text-gray-400 hover:text-gray-300'
              }`}
              onClick={() => setActiveTab('appstore')}
            >
              App Store
            </button>
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {/* Overview Tab */}
          {activeTab === 'overview' && (
            <div>
              <h2 className="text-2xl font-bold mb-4">Demo Overview</h2>
              <p className="text-gray-300 mb-6">
                This demo showcases NovaFuse's capabilities and how they can integrate with your existing systems to enhance compliance operations.
              </p>
              
              {/* Demo Content Placeholder */}
              <div className="bg-gray-700 p-6 rounded-lg text-center">
                <p className="text-gray-400">Overview content will be customized based on partner needs and demo type.</p>
              </div>
            </div>
          )}
          
          {/* UI Components Tab */}
          {activeTab === 'ui' && (
            <div>
              <h2 className="text-2xl font-bold mb-4">UI Components</h2>
              <p className="text-gray-300 mb-6">
                Explore NovaFuse's user interface components designed for seamless integration with your platform.
              </p>
              
              {/* Demo Content Placeholder */}
              <div className="bg-gray-700 p-6 rounded-lg text-center">
                <p className="text-gray-400">UI component demos will be customized based on partner needs.</p>
              </div>
            </div>
          )}
          
          {/* GRC APIs Tab */}
          {activeTab === 'api' && (
            <div>
              <h2 className="text-2xl font-bold mb-4">GRC APIs</h2>
              <p className="text-gray-300 mb-6">
                Explore NovaFuse's powerful GRC APIs that can be integrated with your platform.
              </p>
              
              {/* Demo Content Placeholder */}
              <div className="bg-gray-700 p-6 rounded-lg text-center">
                <p className="text-gray-400">API demos will be customized based on partner needs.</p>
              </div>
            </div>
          )}
          
          {/* UAC Tab */}
          {activeTab === 'uac' && (
            <div>
              <h2 className="text-2xl font-bold mb-4">Universal API Connector (UAC)</h2>
              <p className="text-gray-300 mb-6">
                Discover how NovaFuse's Universal API Connector can transform your compliance operations.
              </p>
              
              {/* Demo Content Placeholder */}
              <div className="bg-gray-700 p-6 rounded-lg text-center">
                <p className="text-gray-400">UAC demos will be customized based on partner needs.</p>
              </div>
            </div>
          )}
          
          {/* App Store Tab */}
          {activeTab === 'appstore' && (
            <div>
              <h2 className="text-2xl font-bold mb-4">Compliance App Store</h2>
              <p className="text-gray-300 mb-6">
                Explore NovaFuse's Compliance App Store with plug-and-play modules for your platform.
              </p>
              
              {/* Demo Content Placeholder */}
              <div className="bg-gray-700 p-6 rounded-lg text-center">
                <p className="text-gray-400">App Store demos will be customized based on partner needs.</p>
              </div>
            </div>
          )}
          
          {/* Custom Demo Content */}
          {children}
        </div>
      </div>

      {/* Call to Action */}
      <div className="bg-gradient-to-r from-blue-900 to-indigo-900 rounded-lg p-6 text-center">
        <h2 className="text-2xl font-bold mb-3">Ready to Transform Your Compliance Operations?</h2>
        <p className="text-lg mb-6 max-w-3xl mx-auto">
          Join the NovaFuse ecosystem and revolutionize how you manage compliance.
        </p>
        <div className="flex flex-col sm:flex-row justify-center gap-4">
          <Link href="/contact" className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-bold">
            Schedule a Personalized Demo
          </Link>
          <Link href="/partner-program" className="border border-white text-white px-6 py-3 rounded-lg font-bold hover:bg-white hover:text-blue-900">
            Learn About Our Partner Program
          </Link>
        </div>
      </div>
    </div>
  );
};

export default BasicDemoTemplate;
