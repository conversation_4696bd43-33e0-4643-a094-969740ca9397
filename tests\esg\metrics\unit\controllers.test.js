const controllers = require('../../../../apis/esg/metrics/controllers');
const models = require('../../../../apis/esg/metrics/models');

// Mock the models
jest.mock('../../../../apis/esg/metrics/models', () => ({
  esgMetrics: [
    {
      id: 'esg-m-12345678',
      name: 'Carbon Emissions',
      description: 'Total carbon emissions in metric tons of CO2 equivalent',
      category: 'environmental',
      subcategory: 'emissions',
      unit: 'tCO2e',
      dataType: 'numeric',
      framework: 'GRI',
      targetValue: '1000',
      targetDate: '2025-12-31',
      owner: 'Sustainability Team',
      status: 'active',
      createdAt: '2023-01-01T00:00:00Z',
      updatedAt: '2023-01-01T00:00:00Z'
    },
    {
      id: 'esg-m-87654321',
      name: 'Gender Diversity',
      description: 'Percentage of women in leadership positions',
      category: 'social',
      subcategory: 'diversity',
      unit: '%',
      dataType: 'percentage',
      framework: 'SASB',
      targetValue: '50',
      targetDate: '2024-12-31',
      owner: 'HR Team',
      status: 'active',
      createdAt: '2023-01-01T00:00:00Z',
      updatedAt: '2023-01-01T00:00:00Z'
    }
  ],
  esgDataPoints: [
    {
      id: 'esg-d-12345678',
      metricId: 'esg-m-12345678',
      value: '1200',
      date: '2023-01-01',
      period: 'quarterly',
      source: 'Internal Report',
      notes: 'Initial measurement',
      verificationStatus: 'verified',
      verifiedBy: 'John Doe',
      verifiedAt: '2023-01-15T00:00:00Z',
      createdAt: '2023-01-01T00:00:00Z',
      updatedAt: '2023-01-01T00:00:00Z'
    }
  ],
  esgInitiatives: [],
  esgCategories: [
    { id: 'env', name: 'Environmental', description: 'Environmental metrics' },
    { id: 'soc', name: 'Social', description: 'Social metrics' },
    { id: 'gov', name: 'Governance', description: 'Governance metrics' }
  ],
  esgFrameworks: [
    { id: 'gri', name: 'GRI', description: 'Global Reporting Initiative' },
    { id: 'sasb', name: 'SASB', description: 'Sustainability Accounting Standards Board' }
  ]
}));

// Mock Express request and response
const mockRequest = (params = {}, query = {}, body = {}) => ({
  params,
  query,
  body
});

const mockResponse = () => {
  const res = {};
  res.status = jest.fn().mockReturnValue(res);
  res.json = jest.fn().mockReturnValue(res);
  return res;
};

describe('ESG Metrics Controllers', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getMetrics', () => {
    it('should return all metrics with default pagination', () => {
      const req = mockRequest({}, {});
      const res = mockResponse();

      controllers.getMetrics(req, res);

      expect(res.json).toHaveBeenCalledWith({
        data: models.esgMetrics,
        pagination: {
          total: 2,
          page: 1,
          limit: 10,
          pages: 1
        }
      });
    });

    it('should filter metrics by category', () => {
      const req = mockRequest({}, { category: 'environmental' });
      const res = mockResponse();

      controllers.getMetrics(req, res);

      expect(res.json).toHaveBeenCalledWith({
        data: [models.esgMetrics[0]],
        pagination: {
          total: 1,
          page: 1,
          limit: 10,
          pages: 1
        }
      });
    });

    it('should handle errors', () => {
      const req = mockRequest();
      const res = mockResponse();

      // Force an error
      jest.spyOn(console, 'error').mockImplementation(() => {});
      jest.spyOn(Array.prototype, 'filter').mockImplementation(() => {
        throw new Error('Test error');
      });

      controllers.getMetrics(req, res);

      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Internal Server Error',
        message: 'Test error'
      });

      // Restore console.error
      console.error.mockRestore();
    });
  });

  describe('getMetricById', () => {
    it('should return a specific metric by ID', () => {
      const req = mockRequest({ id: 'esg-m-12345678' });
      const res = mockResponse();

      controllers.getMetricById(req, res);

      expect(res.json).toHaveBeenCalledWith({
        data: models.esgMetrics[0]
      });
    });

    it('should return 404 if metric not found', () => {
      const req = mockRequest({ id: 'non-existent-id' });
      const res = mockResponse();

      controllers.getMetricById(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'ESG metric with ID non-existent-id not found'
      });
    });
  });

  describe('createMetric', () => {
    it('should create a new metric', () => {
      const newMetric = {
        name: 'Water Usage',
        description: 'Total water consumption in cubic meters',
        category: 'environmental',
        subcategory: 'water',
        unit: 'm³',
        dataType: 'numeric',
        framework: 'GRI',
        targetValue: '5000',
        targetDate: '2025-12-31',
        owner: 'Sustainability Team',
        status: 'active'
      };

      const req = mockRequest({}, {}, newMetric);
      const res = mockResponse();

      // Mock Date and UUID
      const originalDateNow = Date.now;
      Date.now = jest.fn(() => 1577836800000); // 2020-01-01T00:00:00Z
      global.Date = jest.fn(() => ({
        toISOString: () => '2020-01-01T00:00:00Z'
      }));
      jest.mock('uuid', () => ({
        v4: jest.fn(() => '00000000-0000-0000-0000-000000000000')
      }));

      controllers.createMetric(req, res);

      expect(res.status).toHaveBeenCalledWith(201);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({
          name: 'Water Usage',
          category: 'environmental'
        }),
        message: 'ESG metric created successfully'
      }));

      // Restore Date
      Date.now = originalDateNow;
    });
  });

  describe('updateMetric', () => {
    it('should update an existing metric', () => {
      const updateData = {
        name: 'Updated Carbon Emissions',
        description: 'Updated description'
      };

      const req = mockRequest({ id: 'esg-m-12345678' }, {}, updateData);
      const res = mockResponse();

      // Mock Date
      global.Date = jest.fn(() => ({
        toISOString: () => '2020-01-01T00:00:00Z'
      }));

      controllers.updateMetric(req, res);

      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({
          id: 'esg-m-12345678',
          name: 'Updated Carbon Emissions',
          description: 'Updated description'
        }),
        message: 'ESG metric updated successfully'
      }));
    });

    it('should return 404 if metric not found', () => {
      const req = mockRequest({ id: 'non-existent-id' }, {}, { name: 'Updated Name' });
      const res = mockResponse();

      controllers.updateMetric(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'ESG metric with ID non-existent-id not found'
      });
    });
  });

  describe('deleteMetric', () => {
    it('should delete a metric', () => {
      // Mock the initiatives array to be empty
      models.esgInitiatives = [];
      // Mock the data points array to not have any points for this metric
      models.esgDataPoints = models.esgDataPoints.filter(dp => dp.metricId !== 'esg-m-12345678');

      const req = mockRequest({ id: 'esg-m-12345678' });
      const res = mockResponse();

      controllers.deleteMetric(req, res);

      expect(res.json).toHaveBeenCalledWith({
        message: 'ESG metric deleted successfully'
      });
    });

    it('should return 404 if metric not found', () => {
      const req = mockRequest({ id: 'non-existent-id' });
      const res = mockResponse();

      controllers.deleteMetric(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'ESG metric with ID non-existent-id not found'
      });
    });

    it('should return 400 if metric has associated data points', () => {
      // Add a data point for the metric
      models.esgDataPoints = [{
        id: 'test-data-point',
        metricId: 'esg-m-87654321'
      }];

      const req = mockRequest({ id: 'esg-m-87654321' });
      const res = mockResponse();

      controllers.deleteMetric(req, res);

      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Bad Request',
        message: 'Cannot delete metric with ID esg-m-87654321 because it has associated data points'
      });
    });
  });

  describe('getMetricDataPoints', () => {
    it('should return data points for a metric', () => {
      // Add data points for testing
      models.esgDataPoints = [{
        id: 'dp1',
        metricId: 'esg-m-12345678',
        date: '2023-01-01',
        value: '100'
      }];

      const req = mockRequest({ id: 'esg-m-12345678' });
      const res = mockResponse();

      controllers.getMetricDataPoints(req, res);

      expect(res.json).toHaveBeenCalledWith({
        data: expect.arrayContaining([expect.objectContaining({
          id: 'dp1',
          metricId: 'esg-m-12345678'
        })])
      });
    });

    it('should filter data points by date range', () => {
      // Add data points for testing
      models.esgDataPoints = [
        { id: 'dp1', metricId: 'esg-m-12345678', date: '2023-01-01', value: '100' },
        { id: 'dp2', metricId: 'esg-m-12345678', date: '2023-02-01', value: '200' },
        { id: 'dp3', metricId: 'esg-m-12345678', date: '2023-03-01', value: '300' }
      ];

      const req = mockRequest(
        { id: 'esg-m-12345678' },
        { startDate: '2023-02-01', endDate: '2023-02-28' }
      );
      const res = mockResponse();

      controllers.getMetricDataPoints(req, res);

      expect(res.json).toHaveBeenCalledWith({
        data: expect.arrayContaining([
          expect.objectContaining({ id: 'dp2' })
        ])
      });
      expect(res.json.mock.calls[0][0].data).toHaveLength(1);
    });
  });
});

