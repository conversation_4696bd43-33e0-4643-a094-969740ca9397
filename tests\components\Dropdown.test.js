import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import Dropdown from '../../components/Dropdown';

// Mock the Next.js router
jest.mock('next/router', () => ({
  useRouter: jest.fn()
}));

// Mock the Next.js Link component
jest.mock('next/link', () => {
  return ({ children, href, className }) => {
    return (
      <a href={href} className={className} data-testid="mock-link">
        {children}
      </a>
    );
  };
});

// Import the mocked router
const useRouter = jest.requireMock('next/router').useRouter;

describe('Dropdown', () => {
  const dropdownItems = [
    { label: 'Item 1', href: '/item1' },
    { label: 'Item 2', href: '/item2' },
    { label: 'Item 3', href: '/item3' }
  ];
  
  beforeEach(() => {
    // Reset the router mock before each test
    useRouter.mockReset();
    
    // Mock the router
    useRouter.mockReturnValue({
      pathname: '/'
    });
    
    // Mock document event listeners
    document.addEventListener = jest.fn();
    document.removeEventListener = jest.fn();
  });
  
  it('renders the dropdown button with title', () => {
    render(<Dropdown title="Dropdown Title" items={dropdownItems} />);
    
    // Check if the button is rendered with the correct title
    expect(screen.getByRole('button', { name: /dropdown title/i })).toBeInTheDocument();
    
    // Check if the dropdown icon is rendered
    const button = screen.getByRole('button');
    const svg = button.querySelector('svg');
    expect(svg).toBeInTheDocument();
  });
  
  it('shows dropdown items when clicked', () => {
    render(<Dropdown title="Dropdown Title" items={dropdownItems} />);
    
    // Initially, dropdown items should not be visible
    expect(screen.queryByText('Item 1')).not.toBeInTheDocument();
    
    // Click the dropdown button
    fireEvent.click(screen.getByRole('button'));
    
    // Now dropdown items should be visible
    expect(screen.getByText('Item 1')).toBeInTheDocument();
    expect(screen.getByText('Item 2')).toBeInTheDocument();
    expect(screen.getByText('Item 3')).toBeInTheDocument();
    
    // Check if links have correct hrefs
    const links = screen.getAllByTestId('mock-link');
    expect(links[0]).toHaveAttribute('href', '/item1');
    expect(links[1]).toHaveAttribute('href', '/item2');
    expect(links[2]).toHaveAttribute('href', '/item3');
  });
  
  it('hides dropdown items when clicked again', () => {
    render(<Dropdown title="Dropdown Title" items={dropdownItems} />);
    
    // Click to open the dropdown
    fireEvent.click(screen.getByRole('button'));
    
    // Dropdown items should be visible
    expect(screen.getByText('Item 1')).toBeInTheDocument();
    
    // Click again to close the dropdown
    fireEvent.click(screen.getByRole('button'));
    
    // Dropdown items should not be visible
    expect(screen.queryByText('Item 1')).not.toBeInTheDocument();
  });
  
  it('adds event listener for outside clicks', () => {
    render(<Dropdown title="Dropdown Title" items={dropdownItems} />);
    
    // Check if addEventListener was called
    expect(document.addEventListener).toHaveBeenCalledWith('mousedown', expect.any(Function));
  });
  
  it('removes event listener on unmount', () => {
    const { unmount } = render(<Dropdown title="Dropdown Title" items={dropdownItems} />);
    
    // Unmount the component
    unmount();
    
    // Check if removeEventListener was called
    expect(document.removeEventListener).toHaveBeenCalledWith('mousedown', expect.any(Function));
  });
  
  it('closes dropdown when route changes', () => {
    const { rerender } = render(<Dropdown title="Dropdown Title" items={dropdownItems} />);
    
    // Click to open the dropdown
    fireEvent.click(screen.getByRole('button'));
    
    // Dropdown items should be visible
    expect(screen.getByText('Item 1')).toBeInTheDocument();
    
    // Change the router pathname
    useRouter.mockReturnValue({
      pathname: '/new-path'
    });
    
    // Re-render the component with the new router value
    rerender(<Dropdown title="Dropdown Title" items={dropdownItems} />);
    
    // Dropdown items should not be visible
    expect(screen.queryByText('Item 1')).not.toBeInTheDocument();
  });
});
