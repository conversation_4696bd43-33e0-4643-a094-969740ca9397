/**
 * Compliance Requirement Model
 * 
 * This model defines the schema for compliance requirements in the Privacy Management API.
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const complianceRequirementSchema = new Schema({
  framework: { 
    type: Schema.Types.ObjectId, 
    ref: 'RegulatoryFramework', 
    required: true 
  },
  section: { 
    type: String, 
    required: true, 
    trim: true 
  },
  number: { 
    type: String, 
    required: true, 
    trim: true 
  },
  title: { 
    type: String, 
    required: true, 
    trim: true 
  },
  description: { 
    type: String, 
    required: true 
  },
  guidance: { 
    type: String 
  },
  category: { 
    type: String, 
    enum: [
      'notice', 
      'choice', 
      'access', 
      'security', 
      'retention', 
      'transfer', 
      'accountability', 
      'governance', 
      'rights', 
      'breach', 
      'consent', 
      'other'
    ], 
    default: 'other' 
  },
  subCategory: { 
    type: String, 
    trim: true 
  },
  applicability: { 
    type: String, 
    enum: ['all', 'conditional', 'optional'], 
    default: 'all' 
  },
  applicabilityCondition: { 
    type: String 
  },
  risk: { 
    type: String, 
    enum: ['low', 'medium', 'high', 'critical'], 
    default: 'medium' 
  },
  implementationEffort: { 
    type: String, 
    enum: ['low', 'medium', 'high'], 
    default: 'medium' 
  },
  keywords: [{ 
    type: String, 
    trim: true 
  }],
  relatedRequirements: [{
    framework: { 
      type: Schema.Types.ObjectId, 
      ref: 'RegulatoryFramework' 
    },
    requirement: { 
      type: Schema.Types.ObjectId, 
      ref: 'ComplianceRequirement' 
    },
    requirementCode: { 
      type: String, 
      trim: true 
    },
    requirementTitle: { 
      type: String, 
      trim: true 
    },
    mappingStrength: { 
      type: String, 
      enum: ['strong', 'medium', 'weak'], 
      default: 'medium' 
    },
    notes: { 
      type: String 
    }
  }],
  implementationGuidance: { 
    type: String 
  },
  evidenceRequired: [{ 
    type: String, 
    trim: true 
  }],
  status: { 
    type: String, 
    enum: ['active', 'inactive', 'draft', 'archived'], 
    default: 'active' 
  },
  metadata: { 
    type: Object 
  }
}, {
  timestamps: true
});

// Add compound index for framework and status
complianceRequirementSchema.index({ 
  framework: 1, 
  status: 1 
});

// Add compound index for section and number
complianceRequirementSchema.index({ 
  framework: 1, 
  section: 1, 
  number: 1 
}, { 
  unique: true 
});

// Add text index for searching
complianceRequirementSchema.index({ 
  title: 'text', 
  description: 'text', 
  guidance: 'text' 
});

// Add compound index for category and risk
complianceRequirementSchema.index({ 
  category: 1, 
  risk: 1 
});

// Create model
const ComplianceRequirement = mongoose.model('ComplianceRequirement', complianceRequirementSchema);

module.exports = ComplianceRequirement;
