"""
Qiskit backend for quantum computing in protein folding.

This module provides an implementation of the QuantumBackend interface
using Qiskit and IBM Quantum services.
"""

from typing import Dict, Any, Optional, List
import numpy as np
from qiskit import QuantumCircuit, execute, transpile
from qiskit.providers.ibmq import IBMQ
from qiskit.providers.ibmq.managed import IBMQJobManager
from qiskit.providers import Backend

from . import QuantumBackend

class QiskitBackend(QuantumBackend):
    """Qiskit implementation of the QuantumBackend interface."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize the Qiskit backend.
        
        Args:
            config: Configuration dictionary with the following optional keys:
                - api_token: IBM Quantum API token
                - hub: IBM Quantum hub
                - group: IBM Quantum group
                - project: IBM Quantum project
                - backend_name: Name of the IBM Quantum backend to use
                - use_simulator: Whether to use a local simulator (default: True)
                - optimization_level: Optimization level for transpiler (0-3)
        """
        super().__init__(config)
        self.backend = None
        self.optimization_level = self.config.get('optimization_level', 1)
        self._initialize_backend()
    
    def _initialize_backend(self) -> None:
        """Initialize the Qiskit backend."""
        use_simulator = self.config.get('use_simulator', True)
        
        if use_simulator:
            from qiskit.providers.aer import Aer
            self.backend = Aer.get_backend('qasm_simulator')
        else:
            # Load IBMQ account if not already loaded
            if not IBMQ.stored_accounts():
                api_token = self.config.get('api_token')
                if not api_token:
                    raise ValueError("API token is required when not using simulator")
                IBMQ.save_account(api_token)
            
            # Get provider and backend
            provider = IBMQ.load_account()
            backend_name = self.config.get('backend_name', 'ibmq_qasm_simulator')
            self.backend = provider.get_backend(backend_name)
    
    def run_circuit(
        self, 
        circuit: 'QuantumCircuit',
        num_qubits: int,
        depth: int,
        shots: int
    ) -> Dict[str, int]:
        """Run a quantum circuit and return the measurement results."""
        # Transpile the circuit for the target backend
        transpiled_circuit = transpile(
            circuit, 
            backend=self.backend,
            optimization_level=self.optimization_level
        )
        
        # Execute the circuit
        job = execute(
            transpiled_circuit,
            backend=self.backend,
            shots=shots,
            optimization_level=self.optimization_level
        )
        
        # Get the results
        result = job.result()
        counts = result.get_counts()
        
        # Convert to standard format
        return {k: int(v) for k, v in counts.items()}
    
    def get_quantum_volume(self) -> int:
        """Get the quantum volume of the backend."""
        if hasattr(self.backend, 'configuration') and hasattr(self.backend.configuration(), 'quantum_volume'):
            return self.backend.configuration().quantum_volume
        return 32  # Default for simulators
    
    def get_backend_info(self) -> Dict[str, Any]:
        """Get information about the backend."""
        info = {
            'name': 'qiskit',
            'backend': str(self.backend),
            'quantum_volume': self.get_quantum_volume(),
            'is_simulator': 'simulator' in str(self.backend).lower()
        }
        
        # Add backend-specific information if available
        if hasattr(self.backend, 'configuration'):
            config = self.backend.configuration()
            info.update({
                'n_qubits': getattr(config, 'n_qubits', 0),
                'coupling_map': getattr(config, 'coupling_map', None),
                'basis_gates': getattr(config, 'basis_gates', None),
                'backend_version': getattr(config, 'backend_version', 'unknown'),
            })
        
        return info
    
    @classmethod
    def is_available(cls) -> bool:
        """Check if Qiskit is available."""
        try:
            import qiskit
            return True
        except ImportError:
            return False
