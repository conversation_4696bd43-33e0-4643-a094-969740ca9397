x-google-marketplace:
  schemaVersion: v2
  applicationApiVersion: v1beta1
  publishedVersion: '1.0.0'
  publishedVersionMetadata:
    releaseNote: >-
      Initial release of NovaConnect UAC.
    releaseTypes:
      - Feature
    recommended: true
  managedUpdates:
    kalmSupported: true
  images:
    novafuse-uac:
      properties:
        imageRepository:
          type: REPO_WITH_REGISTRY
        imageTag:
          type: TAG
  # Single-tenant architecture configuration
  deploymentArchitecture: SINGLE_TENANT

  # Customer-Managed Encryption Keys (CMEK) support
  encryption:
    cmek: true
    keySpec:
      keyName: projects/{PROJECT_ID}/locations/global/keyRings/tenant-{TENANT_ID}-keyring/cryptoKeys/tenant-{TENANT_ID}-key

  # Billing configuration for single-tenant
  billing:
    type: PER_USE
    metrics:
      - name: grc_api_calls
        displayName: "GRC API Calls"
        description: "Number of GRC API calls"
        valueType: INT64
        unit: "1"
        perTenant: true
        defaultPrice:
          currencyCode: USD
          units: 0
          nanos: 1990000  # $0.00199 per call (approx. $1.99 per 1000 calls)
      - name: connector_executions
        displayName: "Connector Executions"
        description: "Number of connector executions"
        valueType: INT64
        unit: "1"
        perTenant: true
        defaultPrice:
          currencyCode: USD
          units: 0
          nanos: 4990000  # $0.00499 per execution (approx. $4.99 per 1000 executions)
  clusterConstraints:
    k8sVersion: '>= 1.19.0'
    resources:
      - replicas: 3
        requests:
          cpu: 100m
          memory: 256Mi
        limits:
          cpu: 500m
          memory: 512Mi
  securityContext:
    runAsUser: 1000
    fsGroup: 2000
  gcpIAMPolicy:
    bindings:
    - role: roles/cloudsql.client
      members:
      - serviceAccount:{SERVICE_ACCOUNT}
    - role: roles/secretmanager.secretAccessor
      members:
      - serviceAccount:{SERVICE_ACCOUNT}
    - role: roles/monitoring.metricWriter
      members:
      - serviceAccount:{SERVICE_ACCOUNT}
    - role: roles/logging.logWriter
      members:
      - serviceAccount:{SERVICE_ACCOUNT}

properties:
  name:
    type: string
    x-google-marketplace:
      type: NAME
    default: novafuse-uac
  namespace:
    type: string
    x-google-marketplace:
      type: NAMESPACE
    default: default
  deployerServiceAccount:
    type: string
    x-google-marketplace:
      type: SERVICE_ACCOUNT
      serviceAccount:
        roles:
          - type: ClusterRole
            rulesType: PREDEFINED
            rulesFromRoleName: cluster-admin
  tier:
    type: string
    title: Subscription Tier
    description: NovaConnect UAC subscription tier
    enum:
      - core
      - secure
      - enterprise
      - ai_boost
    default: core
  replicas:
    type: integer
    title: Replicas
    description: Number of replicas
    default: 3
    minimum: 1
    maximum: 10
  logLevel:
    type: string
    title: Log Level
    description: Logging level
    enum:
      - debug
      - info
      - warn
      - error
    default: info
  corsOrigin:
    type: string
    title: CORS Origin
    description: CORS allowed origins
    default: '*'
  clusterEnabled:
    type: boolean
    title: Cluster Mode
    description: Enable cluster mode
    default: true
  cacheEnabled:
    type: boolean
    title: Cache
    description: Enable caching
    default: true
  compressionEnabled:
    type: boolean
    title: Compression
    description: Enable response compression
    default: true
  rateLimitEnabled:
    type: boolean
    title: Rate Limiting
    description: Enable rate limiting
    default: true
  rateLimitWindowMs:
    type: integer
    title: Rate Limit Window
    description: Rate limit window in milliseconds
    default: 60000
  rateLimitMax:
    type: integer
    title: Rate Limit Max
    description: Maximum requests per window
    default: 100
  helmetEnabled:
    type: boolean
    title: Helmet
    description: Enable Helmet security headers
    default: true
  csrfEnabled:
    type: boolean
    title: CSRF Protection
    description: Enable CSRF protection
    default: true
  ipFilteringEnabled:
    type: boolean
    title: IP Filtering
    description: Enable IP filtering
    default: false
  mongodb.uri:
    type: string
    title: MongoDB URI
    description: MongoDB connection URI
    x-google-marketplace:
      type: STRING
      default: mongodb://mongodb:27017/novafuse
      required: true
  redis.uri:
    type: string
    title: Redis URI
    description: Redis connection URI
    x-google-marketplace:
      type: STRING
      default: redis://redis:6379
      required: true
  apiKey:
    type: string
    title: API Key
    description: API key for authentication
    x-google-marketplace:
      type: GENERATED_PASSWORD
      generatedPassword:
        length: 32
        base64: false
  jwtSecret:
    type: string
    title: JWT Secret
    description: Secret for JWT authentication
    x-google-marketplace:
      type: GENERATED_PASSWORD
      generatedPassword:
        length: 32
        base64: false
  csrfSecret:
    type: string
    title: CSRF Secret
    description: Secret for CSRF protection
    x-google-marketplace:
      type: GENERATED_PASSWORD
      generatedPassword:
        length: 32
        base64: false
  gcpProjectId:
    type: string
    title: GCP Project ID
    description: Google Cloud project ID
    x-google-marketplace:
      type: PROJECT_ID
  gcpRegion:
    type: string
    title: GCP Region
    description: Google Cloud region
    default: us-central1
  gcpZone:
    type: string
    title: GCP Zone
    description: Google Cloud zone
    default: us-central1-a
  resources.requests.cpu:
    type: string
    title: CPU Request
    description: CPU request
    default: 100m
  resources.requests.memory:
    type: string
    title: Memory Request
    description: Memory request
    default: 256Mi
  resources.limits.cpu:
    type: string
    title: CPU Limit
    description: CPU limit
    default: 500m
  resources.limits.memory:
    type: string
    title: Memory Limit
    description: Memory limit
    default: 512Mi

required:
  - name
  - namespace
  - deployerServiceAccount
  - tier
  - mongodb.uri
  - redis.uri
  - gcpProjectId

form:
  - widget: help
    description: >
      <h2>NovaConnect UAC</h2>
      <p>NovaConnect Universal API Connector (UAC) is a powerful API integration platform that simplifies connecting to any API, normalizing data, and integrating with your existing systems.</p>
      <p>For more information, visit <a href="https://novafuse.io" target="_blank">novafuse.io</a>.</p>
  - widget: deploymentName
    title: NovaConnect UAC Deployment Name
    description: >
      The name of your NovaConnect UAC deployment.
    validationRegex: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?$
    validationErrorMessage: >
      Deployment name must start with a lowercase letter, and contain only lowercase letters,
      numbers, and hyphens.
  - widget: selector
    id: tier
    title: Subscription Tier
    description: Select your NovaConnect UAC subscription tier
    jsonPath: .properties.tier
    options:
      - label: NovaConnect Core
        value: core
        description: Basic functionality for small projects
      - label: NovaConnect Secure
        value: secure
        description: Standard functionality for growing teams
      - label: NovaConnect Enterprise
        value: enterprise
        description: Advanced functionality for professional teams
      - label: NovaConnect AI Boost
        value: ai_boost
        description: Enterprise-grade functionality with AI capabilities
  - widget: help
    description: >
      <h3>Database Configuration</h3>
      <p>NovaConnect UAC requires MongoDB and Redis for data storage and caching.</p>
  - widget: input
    id: mongodb.uri
    title: MongoDB URI
    description: MongoDB connection URI (e.g., ****************************:port/database)
    jsonPath: .properties.mongodb.uri
    required: true
  - widget: input
    id: redis.uri
    title: Redis URI
    description: Redis connection URI (e.g., redis://user:password@host:port/database)
    jsonPath: .properties.redis.uri
    required: true
  - widget: help
    description: >
      <h3>Advanced Configuration</h3>
      <p>Configure advanced settings for NovaConnect UAC.</p>
  - widget: slider
    id: replicas
    title: Replicas
    description: Number of replicas
    jsonPath: .properties.replicas
    min: 1
    max: 10
    step: 1
  - widget: selector
    id: logLevel
    title: Log Level
    description: Logging level
    jsonPath: .properties.logLevel
    options:
      - label: Debug
        value: debug
      - label: Info
        value: info
      - label: Warning
        value: warn
      - label: Error
        value: error
  - widget: checkbox
    id: clusterEnabled
    title: Cluster Mode
    description: Enable cluster mode
    jsonPath: .properties.clusterEnabled
  - widget: checkbox
    id: cacheEnabled
    title: Cache
    description: Enable caching
    jsonPath: .properties.cacheEnabled
  - widget: checkbox
    id: compressionEnabled
    title: Compression
    description: Enable response compression
    jsonPath: .properties.compressionEnabled
  - widget: checkbox
    id: rateLimitEnabled
    title: Rate Limiting
    description: Enable rate limiting
    jsonPath: .properties.rateLimitEnabled
  - widget: checkbox
    id: helmetEnabled
    title: Helmet
    description: Enable Helmet security headers
    jsonPath: .properties.helmetEnabled
  - widget: checkbox
    id: csrfEnabled
    title: CSRF Protection
    description: Enable CSRF protection
    jsonPath: .properties.csrfEnabled
  - widget: checkbox
    id: ipFilteringEnabled
    title: IP Filtering
    description: Enable IP filtering
    jsonPath: .properties.ipFilteringEnabled

deployerServiceAccount:
  roles:
    - type: ClusterRole
      rulesType: PREDEFINED
      rulesFromRoleName: cluster-admin

images:
  novafuse-uac:
    properties:
      image:
        type: FULL
      imageNovaFuseUAC:
        type: FULL
    gcr.io/novafuse-production/novafuse-uac:1.0.0
