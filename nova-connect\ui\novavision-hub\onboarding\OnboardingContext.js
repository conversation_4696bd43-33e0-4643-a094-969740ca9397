/**
 * Onboarding Context
 * 
 * This module provides a context for onboarding functionality.
 */

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import PropTypes from 'prop-types';

// Create context
const OnboardingContext = createContext();

/**
 * Use onboarding hook
 * 
 * @returns {Object} Onboarding context
 */
export const useOnboarding = () => {
  const context = useContext(OnboardingContext);
  
  if (!context) {
    throw new Error('useOnboarding must be used within an OnboardingProvider');
  }
  
  return context;
};

/**
 * Onboarding provider component
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components
 * @param {string} [props.storageKey='novavision_onboarding'] - Local storage key for onboarding state
 * @param {Object} [props.initialTours={}] - Initial tours configuration
 * @param {Function} [props.onComplete] - Function to call when a tour is completed
 * @param {Function} [props.onSkip] - Function to call when a tour is skipped
 * @param {Function} [props.onStepComplete] - Function to call when a step is completed
 * @returns {React.ReactElement} OnboardingProvider component
 */
export const OnboardingProvider = ({
  children,
  storageKey = 'novavision_onboarding',
  initialTours = {},
  onComplete,
  onSkip,
  onStepComplete
}) => {
  // State
  const [tours, setTours] = useState(initialTours);
  const [activeTour, setActiveTour] = useState(null);
  const [activeStep, setActiveStep] = useState(0);
  const [completedTours, setCompletedTours] = useState({});
  const [isVisible, setIsVisible] = useState(false);
  
  // Load completed tours from local storage
  useEffect(() => {
    try {
      const storedCompletedTours = localStorage.getItem(storageKey);
      
      if (storedCompletedTours) {
        setCompletedTours(JSON.parse(storedCompletedTours));
      }
    } catch (error) {
      console.error('Error loading onboarding state from local storage:', error);
    }
  }, [storageKey]);
  
  // Save completed tours to local storage
  useEffect(() => {
    try {
      localStorage.setItem(storageKey, JSON.stringify(completedTours));
    } catch (error) {
      console.error('Error saving onboarding state to local storage:', error);
    }
  }, [completedTours, storageKey]);
  
  /**
   * Register a tour
   * 
   * @param {string} tourId - Tour ID
   * @param {Object} tourConfig - Tour configuration
   */
  const registerTour = useCallback((tourId, tourConfig) => {
    setTours(prevTours => ({
      ...prevTours,
      [tourId]: tourConfig
    }));
  }, []);
  
  /**
   * Start a tour
   * 
   * @param {string} tourId - Tour ID
   * @returns {boolean} - Whether the tour was started
   */
  const startTour = useCallback((tourId) => {
    if (!tours[tourId]) {
      console.error(`Tour with ID "${tourId}" not found`);
      return false;
    }
    
    setActiveTour(tourId);
    setActiveStep(0);
    setIsVisible(true);
    
    return true;
  }, [tours]);
  
  /**
   * End the active tour
   * 
   * @param {boolean} [completed=true] - Whether the tour was completed
   */
  const endTour = useCallback((completed = true) => {
    if (!activeTour) return;
    
    if (completed) {
      setCompletedTours(prev => ({
        ...prev,
        [activeTour]: {
          completed: true,
          timestamp: new Date().toISOString()
        }
      }));
      
      if (onComplete) {
        onComplete(activeTour);
      }
    } else if (onSkip) {
      onSkip(activeTour);
    }
    
    setActiveTour(null);
    setActiveStep(0);
    setIsVisible(false);
  }, [activeTour, onComplete, onSkip]);
  
  /**
   * Go to the next step in the active tour
   */
  const nextStep = useCallback(() => {
    if (!activeTour || !tours[activeTour]) return;
    
    const tour = tours[activeTour];
    const steps = tour.steps || [];
    
    if (activeStep < steps.length - 1) {
      if (onStepComplete) {
        onStepComplete(activeTour, activeStep);
      }
      
      setActiveStep(prev => prev + 1);
    } else {
      endTour(true);
    }
  }, [activeTour, activeStep, tours, endTour, onStepComplete]);
  
  /**
   * Go to the previous step in the active tour
   */
  const prevStep = useCallback(() => {
    if (!activeTour || activeStep <= 0) return;
    
    setActiveStep(prev => prev - 1);
  }, [activeTour, activeStep]);
  
  /**
   * Skip the active tour
   */
  const skipTour = useCallback(() => {
    endTour(false);
  }, [endTour]);
  
  /**
   * Check if a tour is completed
   * 
   * @param {string} tourId - Tour ID
   * @returns {boolean} - Whether the tour is completed
   */
  const isTourCompleted = useCallback((tourId) => {
    return !!completedTours[tourId]?.completed;
  }, [completedTours]);
  
  /**
   * Reset a tour's completion status
   * 
   * @param {string} tourId - Tour ID
   */
  const resetTour = useCallback((tourId) => {
    setCompletedTours(prev => {
      const newCompletedTours = { ...prev };
      delete newCompletedTours[tourId];
      return newCompletedTours;
    });
  }, []);
  
  /**
   * Reset all tours' completion status
   */
  const resetAllTours = useCallback(() => {
    setCompletedTours({});
  }, []);
  
  /**
   * Get the active tour configuration
   * 
   * @returns {Object|null} - Active tour configuration
   */
  const getActiveTourConfig = useCallback(() => {
    if (!activeTour || !tours[activeTour]) return null;
    
    return tours[activeTour];
  }, [activeTour, tours]);
  
  /**
   * Get the active step configuration
   * 
   * @returns {Object|null} - Active step configuration
   */
  const getActiveStepConfig = useCallback(() => {
    if (!activeTour || !tours[activeTour]) return null;
    
    const tour = tours[activeTour];
    const steps = tour.steps || [];
    
    if (activeStep < 0 || activeStep >= steps.length) return null;
    
    return steps[activeStep];
  }, [activeTour, activeStep, tours]);
  
  // Context value
  const value = {
    tours,
    activeTour,
    activeStep,
    completedTours,
    isVisible,
    registerTour,
    startTour,
    endTour,
    nextStep,
    prevStep,
    skipTour,
    isTourCompleted,
    resetTour,
    resetAllTours,
    getActiveTourConfig,
    getActiveStepConfig
  };
  
  return (
    <OnboardingContext.Provider value={value}>
      {children}
    </OnboardingContext.Provider>
  );
};

OnboardingProvider.propTypes = {
  children: PropTypes.node.isRequired,
  storageKey: PropTypes.string,
  initialTours: PropTypes.object,
  onComplete: PropTypes.func,
  onSkip: PropTypes.func,
  onStepComplete: PropTypes.func
};

export default OnboardingContext;
