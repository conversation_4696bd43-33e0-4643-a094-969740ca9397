#!/bin/bash
# NovaAgent WASM Build Script - Following Exact Guide

echo "🔧 Building NovaAgent WASM Module..."

# Step 3: Build and Optimize (from guide)

# Build WASM module
echo "📦 Building WASM target..."
cargo build --target wasm32-unknown-unknown --release

# Generate JS bindings
echo "🔗 Generating JS bindings..."
wasm-bindgen target/wasm32-unknown-unknown/release/novabrowser.wasm \
  --out-dir ./pkg \
  --target web \
  --no-typescript

# Optimize with wasm-opt (binaryen)
echo "⚡ Optimizing WASM..."
wasm-opt -Oz -o pkg/nova_agent_optimized.wasm pkg/novabrowser_bg.wasm

echo "✅ NovaAgent WASM build complete!"
echo "📁 Output: ./pkg/"
echo "🎯 Expected: Ψ-Scan functionality ready for browser integration"
