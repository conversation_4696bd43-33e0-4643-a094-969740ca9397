/**
 * NetworkPerformancePanel Component
 * 
 * A component for visualizing network performance metrics.
 */

import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { networkMonitor } from '../performance';
import { TabPanel } from './index';

// Import chart components
import {
  LineChart,
  Bar<PERSON>hart,
  <PERSON>hart,
  ResponsiveContainer,
  CartesianGrid,
  XAxis,
  YAxis,
  Tooltip,
  Legend,
  Line,
  Bar,
  Pie,
  Cell
} from 'recharts';

/**
 * NetworkPerformancePanel component
 * 
 * @param {Object} props - Component props
 * @param {boolean} [props.startMonitoringOnMount=false] - Whether to start monitoring on mount
 * @param {string} [props.className] - Additional CSS class names
 * @param {Object} [props.style] - Additional inline styles
 * @returns {React.ReactElement} NetworkPerformancePanel component
 */
const NetworkPerformancePanel = ({
  startMonitoringOnMount = false,
  className = '',
  style = {}
}) => {
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [metrics, setMetrics] = useState({
    requests: {},
    totalRequests: 0,
    successfulRequests: 0,
    failedRequests: 0,
    totalSize: 0,
    totalTime: 0,
    averageTime: 0
  });
  const [activeTab, setActiveTab] = useState('overview');
  const [updateInterval, setUpdateInterval] = useState(1000);
  const [autoUpdate, setAutoUpdate] = useState(true);
  const [historicalData, setHistoricalData] = useState([]);
  
  // Start/stop monitoring on mount/unmount
  useEffect(() => {
    if (startMonitoringOnMount) {
      handleStartMonitoring();
    }
    
    return () => {
      networkMonitor.stopMonitoring();
    };
  }, [startMonitoringOnMount]);
  
  // Update metrics periodically
  useEffect(() => {
    if (!autoUpdate) return;
    
    const intervalId = setInterval(() => {
      updateMetricsNow();
    }, updateInterval);
    
    return () => {
      clearInterval(intervalId);
    };
  }, [autoUpdate, updateInterval]);
  
  // Start monitoring
  const handleStartMonitoring = () => {
    networkMonitor.startMonitoring();
    setIsMonitoring(true);
  };
  
  // Stop monitoring
  const handleStopMonitoring = () => {
    networkMonitor.stopMonitoring();
    setIsMonitoring(false);
  };
  
  // Clear metrics
  const handleClearMetrics = () => {
    networkMonitor.clearMetrics();
    setMetrics(networkMonitor.getMetrics());
    setHistoricalData([]);
  };
  
  // Toggle monitoring
  const toggleMonitoring = () => {
    if (isMonitoring) {
      handleStopMonitoring();
    } else {
      handleStartMonitoring();
    }
  };
  
  // Toggle auto update
  const toggleAutoUpdate = () => {
    setAutoUpdate(!autoUpdate);
  };
  
  // Update metrics now
  const updateMetricsNow = () => {
    const currentMetrics = networkMonitor.getMetrics();
    setMetrics(currentMetrics);
    
    // Update historical data
    setHistoricalData(prevData => {
      const newData = [
        ...prevData,
        {
          timestamp: Date.now(),
          totalRequests: currentMetrics.totalRequests,
          successfulRequests: currentMetrics.successfulRequests,
          failedRequests: currentMetrics.failedRequests,
          averageTime: currentMetrics.averageTime
        }
      ];
      
      // Limit historical data to 50 points
      if (newData.length > 50) {
        return newData.slice(-50);
      }
      
      return newData;
    });
  };
  
  // Format time in ms
  const formatTime = (time) => {
    if (time === Infinity) return 'N/A';
    if (time === 0) return '0ms';
    
    if (time < 1) {
      return `${(time * 1000).toFixed(2)}μs`;
    } else if (time < 1000) {
      return `${time.toFixed(2)}ms`;
    } else {
      return `${(time / 1000).toFixed(2)}s`;
    }
  };
  
  // Format bytes
  const formatBytes = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
  };
  
  // Format timestamp
  const formatTimestamp = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString();
  };
  
  // Get network report
  const report = networkMonitor.getNetworkReport();
  
  // Prepare chart data
  const chartData = historicalData.map(data => ({
    name: formatTimestamp(data.timestamp),
    requests: data.totalRequests,
    successful: data.successfulRequests,
    failed: data.failedRequests,
    averageTime: data.averageTime
  }));
  
  // Prepare domain data for pie chart
  const domainData = Object.entries(metrics.requests).map(([domain, data]) => ({
    name: domain,
    value: data.count
  }));
  
  // Colors for pie chart
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];
  
  // Define tabs
  const tabs = [
    {
      id: 'overview',
      label: 'Overview',
      content: (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Request metrics */}
            <div className="bg-surface p-4 rounded-md border border-divider">
              <h3 className="text-lg font-medium mb-2 text-textPrimary">Request Performance</h3>
              <div className="space-y-2">
                <p className="text-sm text-textSecondary">Total requests: {report.totalRequests}</p>
                <p className="text-sm text-textSecondary">Success rate: {report.successRate.toFixed(2)}%</p>
                <p className="text-sm text-textSecondary">Average time: {formatTime(report.averageTime)}</p>
                <p className="text-sm text-textSecondary">Total size: {formatBytes(report.totalSize)}</p>
                
                {report.slowestDomains.length > 0 && (
                  <div className="mt-4">
                    <h4 className="text-md font-medium mb-2 text-textPrimary">Slowest Domains</h4>
                    <div className="overflow-x-auto">
                      <table className="min-w-full text-sm">
                        <thead>
                          <tr>
                            <th className="px-2 py-1 text-left text-xs font-medium text-textSecondary">Domain</th>
                            <th className="px-2 py-1 text-left text-xs font-medium text-textSecondary">Avg Time</th>
                            <th className="px-2 py-1 text-left text-xs font-medium text-textSecondary">Requests</th>
                          </tr>
                        </thead>
                        <tbody>
                          {report.slowestDomains.map((domain) => (
                            <tr key={domain.domain}>
                              <td className="px-2 py-1 text-xs text-textPrimary">{domain.domain}</td>
                              <td className="px-2 py-1 text-xs text-textPrimary">{formatTime(domain.averageTime)}</td>
                              <td className="px-2 py-1 text-xs text-textPrimary">{domain.count}</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                )}
              </div>
            </div>
            
            {/* Domain distribution */}
            <div className="bg-surface p-4 rounded-md border border-divider">
              <h3 className="text-lg font-medium mb-2 text-textPrimary">Domain Distribution</h3>
              {domainData.length > 0 ? (
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={domainData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      >
                        {domainData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value) => value} />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              ) : (
                <p className="text-sm text-textSecondary">No domain data available</p>
              )}
            </div>
          </div>
          
          {/* Request trend chart */}
          <div className="bg-surface p-4 rounded-md border border-divider">
            <h3 className="text-lg font-medium mb-2 text-textPrimary">Request Trends</h3>
            {chartData.length > 0 ? (
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={chartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Line type="monotone" dataKey="requests" name="Total Requests" stroke="#8884d8" />
                    <Line type="monotone" dataKey="successful" name="Successful" stroke="#82ca9d" />
                    <Line type="monotone" dataKey="failed" name="Failed" stroke="#ff8042" />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            ) : (
              <p className="text-sm text-textSecondary">No trend data available</p>
            )}
          </div>
        </div>
      )
    },
    {
      id: 'domains',
      label: 'Domains',
      content: (
        <div className="space-y-4">
          <h3 className="text-lg font-medium mb-2 text-textPrimary">Domain Performance</h3>
          
          {Object.keys(metrics.requests).length > 0 ? (
            <div className="max-h-96 overflow-y-auto">
              <table className="min-w-full divide-y divide-divider">
                <thead className="bg-surface sticky top-0">
                  <tr>
                    <th className="px-4 py-2 text-left text-xs font-medium text-textSecondary uppercase tracking-wider">Domain</th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-textSecondary uppercase tracking-wider">Requests</th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-textSecondary uppercase tracking-wider">Success</th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-textSecondary uppercase tracking-wider">Avg Time</th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-textSecondary uppercase tracking-wider">Total Size</th>
                  </tr>
                </thead>
                <tbody className="bg-background divide-y divide-divider">
                  {Object.entries(metrics.requests)
                    .sort((a, b) => b[1].count - a[1].count)
                    .map(([domain, data]) => (
                      <tr key={domain}>
                        <td className="px-4 py-2 text-sm text-textPrimary">{domain}</td>
                        <td className="px-4 py-2 text-sm text-textPrimary">{data.count}</td>
                        <td className="px-4 py-2 text-sm text-textPrimary">
                          {data.count > 0 ? `${((data.successCount / data.count) * 100).toFixed(1)}%` : 'N/A'}
                        </td>
                        <td className="px-4 py-2 text-sm text-textPrimary">{formatTime(data.averageTime)}</td>
                        <td className="px-4 py-2 text-sm text-textPrimary">{formatBytes(data.totalSize)}</td>
                      </tr>
                    ))}
                </tbody>
              </table>
            </div>
          ) : (
            <p className="text-sm text-textSecondary">No domain metrics available</p>
          )}
        </div>
      )
    }
  ];
  
  return (
    <div className={`network-performance-panel bg-background rounded-lg shadow-lg overflow-hidden ${className}`} style={style}>
      {/* Header */}
      <div className="bg-surface p-4 border-b border-divider flex justify-between items-center">
        <h2 className="text-xl font-bold text-textPrimary">Network Performance</h2>
        
        <div className="flex items-center space-x-2">
          <button
            className={`text-sm px-3 py-1 rounded ${isMonitoring ? 'bg-error text-white' : 'bg-success text-white'}`}
            onClick={toggleMonitoring}
          >
            {isMonitoring ? 'Stop' : 'Start'}
          </button>
          
          <button
            className={`text-sm px-3 py-1 rounded ${autoUpdate ? 'bg-primary text-white' : 'bg-surface text-textPrimary border border-divider'}`}
            onClick={toggleAutoUpdate}
          >
            Auto Update
          </button>
          
          <button
            className="text-sm px-3 py-1 rounded bg-surface text-textPrimary border border-divider"
            onClick={handleClearMetrics}
          >
            Clear
          </button>
        </div>
      </div>
      
      {/* Content */}
      <div className="p-6">
        <TabPanel
          tabs={tabs}
          defaultTab="overview"
          variant="pills"
          onTabChange={setActiveTab}
        />
      </div>
    </div>
  );
};

NetworkPerformancePanel.propTypes = {
  startMonitoringOnMount: PropTypes.bool,
  className: PropTypes.string,
  style: PropTypes.object
};

export default NetworkPerformancePanel;
