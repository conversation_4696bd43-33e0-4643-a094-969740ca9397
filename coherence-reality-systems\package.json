{"name": "cbe-browser-react", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.10.0", "bn.js": "^5.2.2", "cors": "^2.8.5", "crypto": "^1.0.1", "eslint-config-react-app": "^7.0.1", "ethereum-cryptography": "^3.2.0", "ethers": "^6.14.4", "express": "^4.21.2", "express-prometheus-middleware": "^1.2.0", "express-rate-limit": "^7.5.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-scripts": "5.0.1", "secp256k1": "^5.0.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^10.4.21", "postcss": "^8.5.5", "tailwindcss": "^4.1.10"}}