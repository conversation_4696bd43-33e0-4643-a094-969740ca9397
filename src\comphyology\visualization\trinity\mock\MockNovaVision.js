/**
 * Mock NovaVision
 * 
 * This is a mock implementation of NovaVision for testing purposes.
 */
class MockNovaVision {
  /**
   * Constructor
   * 
   * @param {Object} options - NovaVision options
   */
  constructor(options = {}) {
    this.options = {
      enableLogging: options.enableLogging || false,
      ...options
    };
    
    // Initialize state
    this.state = {
      components: new Map(),
      dataSources: new Map(),
      componentSubscriptions: new Map(),
      dataSourceSubscriptions: new Map(),
      componentData: new Map(),
      dataSourceData: new Map(),
      componentEvents: new Map()
    };
    
    if (this.options.enableLogging) {
      console.log('MockNovaVision initialized with options:', this.options);
    }
  }
  
  /**
   * Register component
   * 
   * @param {Object} component - Component to register
   * @returns {Promise} - Promise that resolves when registration is complete
   */
  async registerComponent(component) {
    if (!component.id) {
      return Promise.reject(new Error('Component ID is required'));
    }
    
    if (this.state.components.has(component.id)) {
      return Promise.reject(new Error(`Component with ID ${component.id} already exists`));
    }
    
    this.state.components.set(component.id, component);
    this.state.componentSubscriptions.set(component.id, new Set());
    this.state.componentData.set(component.id, null);
    this.state.componentEvents.set(component.id, []);
    
    if (this.options.enableLogging) {
      console.log(`Registered component: ${component.id}`);
    }
    
    return Promise.resolve();
  }
  
  /**
   * Unregister component
   * 
   * @param {string} componentId - Component ID
   * @returns {Promise} - Promise that resolves when unregistration is complete
   */
  async unregisterComponent(componentId) {
    if (!this.state.components.has(componentId)) {
      return Promise.reject(new Error(`Component with ID ${componentId} does not exist`));
    }
    
    this.state.components.delete(componentId);
    this.state.componentSubscriptions.delete(componentId);
    this.state.componentData.delete(componentId);
    this.state.componentEvents.delete(componentId);
    
    if (this.options.enableLogging) {
      console.log(`Unregistered component: ${componentId}`);
    }
    
    return Promise.resolve();
  }
  
  /**
   * Register data source
   * 
   * @param {Object} dataSource - Data source to register
   * @returns {Promise} - Promise that resolves when registration is complete
   */
  async registerDataSource(dataSource) {
    if (!dataSource.id) {
      return Promise.reject(new Error('Data source ID is required'));
    }
    
    if (this.state.dataSources.has(dataSource.id)) {
      return Promise.reject(new Error(`Data source with ID ${dataSource.id} already exists`));
    }
    
    this.state.dataSources.set(dataSource.id, dataSource);
    this.state.dataSourceSubscriptions.set(dataSource.id, new Set());
    this.state.dataSourceData.set(dataSource.id, null);
    
    if (this.options.enableLogging) {
      console.log(`Registered data source: ${dataSource.id}`);
    }
    
    return Promise.resolve();
  }
  
  /**
   * Unregister data source
   * 
   * @param {string} dataSourceId - Data source ID
   * @returns {Promise} - Promise that resolves when unregistration is complete
   */
  async unregisterDataSource(dataSourceId) {
    if (!this.state.dataSources.has(dataSourceId)) {
      return Promise.reject(new Error(`Data source with ID ${dataSourceId} does not exist`));
    }
    
    this.state.dataSources.delete(dataSourceId);
    this.state.dataSourceSubscriptions.delete(dataSourceId);
    this.state.dataSourceData.delete(dataSourceId);
    
    if (this.options.enableLogging) {
      console.log(`Unregistered data source: ${dataSourceId}`);
    }
    
    return Promise.resolve();
  }
  
  /**
   * Subscribe to component events
   * 
   * @param {string} componentId - Component ID
   * @param {Function} callback - Callback function
   * @returns {Promise<string>} - Promise that resolves with subscription ID
   */
  async subscribeToComponentEvents(componentId, callback) {
    if (!this.state.components.has(componentId)) {
      return Promise.reject(new Error(`Component with ID ${componentId} does not exist`));
    }
    
    if (typeof callback !== 'function') {
      return Promise.reject(new Error('Callback must be a function'));
    }
    
    const subscriptionId = `sub_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    this.state.componentSubscriptions.get(componentId).add({
      id: subscriptionId,
      callback
    });
    
    if (this.options.enableLogging) {
      console.log(`Subscribed to component events: ${componentId} (${subscriptionId})`);
    }
    
    return Promise.resolve(subscriptionId);
  }
  
  /**
   * Unsubscribe from component events
   * 
   * @param {string} componentId - Component ID
   * @param {string} subscriptionId - Subscription ID
   * @returns {Promise} - Promise that resolves when unsubscription is complete
   */
  async unsubscribeFromComponentEvents(componentId, subscriptionId) {
    if (!this.state.components.has(componentId)) {
      return Promise.reject(new Error(`Component with ID ${componentId} does not exist`));
    }
    
    const subscriptions = this.state.componentSubscriptions.get(componentId);
    const subscription = Array.from(subscriptions).find(sub => sub.id === subscriptionId);
    
    if (!subscription) {
      return Promise.reject(new Error(`Subscription with ID ${subscriptionId} does not exist`));
    }
    
    subscriptions.delete(subscription);
    
    if (this.options.enableLogging) {
      console.log(`Unsubscribed from component events: ${componentId} (${subscriptionId})`);
    }
    
    return Promise.resolve();
  }
  
  /**
   * Subscribe to data source
   * 
   * @param {string} dataSourceId - Data source ID
   * @param {Function} callback - Callback function
   * @returns {Promise<string>} - Promise that resolves with subscription ID
   */
  async subscribeToDataSource(dataSourceId, callback) {
    if (!this.state.dataSources.has(dataSourceId)) {
      return Promise.reject(new Error(`Data source with ID ${dataSourceId} does not exist`));
    }
    
    if (typeof callback !== 'function') {
      return Promise.reject(new Error('Callback must be a function'));
    }
    
    const subscriptionId = `sub_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    this.state.dataSourceSubscriptions.get(dataSourceId).add({
      id: subscriptionId,
      callback
    });
    
    if (this.options.enableLogging) {
      console.log(`Subscribed to data source: ${dataSourceId} (${subscriptionId})`);
    }
    
    return Promise.resolve(subscriptionId);
  }
  
  /**
   * Unsubscribe from data source
   * 
   * @param {string} dataSourceId - Data source ID
   * @param {string} subscriptionId - Subscription ID
   * @returns {Promise} - Promise that resolves when unsubscription is complete
   */
  async unsubscribeFromDataSource(dataSourceId, subscriptionId) {
    if (!this.state.dataSources.has(dataSourceId)) {
      return Promise.reject(new Error(`Data source with ID ${dataSourceId} does not exist`));
    }
    
    const subscriptions = this.state.dataSourceSubscriptions.get(dataSourceId);
    const subscription = Array.from(subscriptions).find(sub => sub.id === subscriptionId);
    
    if (!subscription) {
      return Promise.reject(new Error(`Subscription with ID ${subscriptionId} does not exist`));
    }
    
    subscriptions.delete(subscription);
    
    if (this.options.enableLogging) {
      console.log(`Unsubscribed from data source: ${dataSourceId} (${subscriptionId})`);
    }
    
    return Promise.resolve();
  }
  
  /**
   * Publish component data
   * 
   * @param {string} componentId - Component ID
   * @param {Object} data - Component data
   * @returns {Promise} - Promise that resolves when data is published
   */
  async publishComponentData(componentId, data) {
    if (!this.state.components.has(componentId)) {
      return Promise.reject(new Error(`Component with ID ${componentId} does not exist`));
    }
    
    this.state.componentData.set(componentId, data);
    
    if (this.options.enableLogging) {
      console.log(`Published component data: ${componentId}`);
    }
    
    return Promise.resolve();
  }
  
  /**
   * Publish component event
   * 
   * @param {string} componentId - Component ID
   * @param {Object} event - Component event
   * @returns {Promise} - Promise that resolves when event is published
   */
  async publishComponentEvent(componentId, event) {
    if (!this.state.components.has(componentId)) {
      return Promise.reject(new Error(`Component with ID ${componentId} does not exist`));
    }
    
    const events = this.state.componentEvents.get(componentId);
    events.push({
      ...event,
      timestamp: event.timestamp || new Date()
    });
    
    // Notify subscribers
    const subscriptions = this.state.componentSubscriptions.get(componentId);
    
    for (const subscription of subscriptions) {
      try {
        subscription.callback(event, componentId);
      } catch (error) {
        console.error(`Error in component event subscription callback: ${error.message}`);
      }
    }
    
    if (this.options.enableLogging) {
      console.log(`Published component event: ${componentId}`, event);
    }
    
    return Promise.resolve();
  }
  
  /**
   * Publish data source data
   * 
   * @param {string} dataSourceId - Data source ID
   * @param {Object} data - Data source data
   * @returns {Promise} - Promise that resolves when data is published
   */
  async publishDataSourceData(dataSourceId, data) {
    if (!this.state.dataSources.has(dataSourceId)) {
      return Promise.reject(new Error(`Data source with ID ${dataSourceId} does not exist`));
    }
    
    this.state.dataSourceData.set(dataSourceId, data);
    
    // Notify subscribers
    const subscriptions = this.state.dataSourceSubscriptions.get(dataSourceId);
    
    for (const subscription of subscriptions) {
      try {
        subscription.callback(data, dataSourceId);
      } catch (error) {
        console.error(`Error in data source subscription callback: ${error.message}`);
      }
    }
    
    if (this.options.enableLogging) {
      console.log(`Published data source data: ${dataSourceId}`);
    }
    
    return Promise.resolve();
  }
  
  /**
   * Get component data
   * 
   * @param {string} componentId - Component ID
   * @returns {Promise<Object>} - Promise that resolves with component data
   */
  async getComponentData(componentId) {
    if (!this.state.components.has(componentId)) {
      return Promise.reject(new Error(`Component with ID ${componentId} does not exist`));
    }
    
    return Promise.resolve(this.state.componentData.get(componentId));
  }
  
  /**
   * Get data source data
   * 
   * @param {string} dataSourceId - Data source ID
   * @returns {Promise<Object>} - Promise that resolves with data source data
   */
  async getDataSourceData(dataSourceId) {
    if (!this.state.dataSources.has(dataSourceId)) {
      return Promise.reject(new Error(`Data source with ID ${dataSourceId} does not exist`));
    }
    
    return Promise.resolve(this.state.dataSourceData.get(dataSourceId));
  }
  
  /**
   * Get component events
   * 
   * @param {string} componentId - Component ID
   * @returns {Promise<Array>} - Promise that resolves with component events
   */
  async getComponentEvents(componentId) {
    if (!this.state.components.has(componentId)) {
      return Promise.reject(new Error(`Component with ID ${componentId} does not exist`));
    }
    
    return Promise.resolve(this.state.componentEvents.get(componentId));
  }
}

module.exports = MockNovaVision;
