import React from 'react';
import { render, screen } from '@testing-library/react';
import Layout from '../../components/Layout';

// Mock the FloatingNovaConcierge component
jest.mock('../../components/FloatingNovaConcierge', () => {
  return function MockFloatingNovaConcierge() {
    return <div data-testid="mock-nova-concierge">Mock NovaConcierge</div>;
  };
});

// Mock the Next.js Head component
jest.mock('next/head', () => {
  return function MockHead({ children }) {
    return <div data-testid="mock-head">{children}</div>;
  };
});

describe('Layout', () => {
  it('renders children correctly', () => {
    render(
      <Layout>
        <div data-testid="test-content">Test Content</div>
      </Layout>
    );
    
    // Check if the content is rendered
    expect(screen.getByTestId('test-content')).toBeInTheDocument();
    expect(screen.getByText('Test Content')).toBeInTheDocument();
    
    // Check if the NovaConcierge component is rendered
    expect(screen.getByTestId('mock-nova-concierge')).toBeInTheDocument();
  });
  
  it('renders with default props', () => {
    render(
      <Layout>
        <div>Content</div>
      </Layout>
    );
    
    // Check if the Head component is rendered with default props
    const head = screen.getByTestId('mock-head');
    expect(head).toBeInTheDocument();
    
    // Check if the main container has the correct classes
    const mainContainer = screen.getByText('Content').closest('main');
    expect(mainContainer).toHaveClass('flex-grow');
    expect(mainContainer).toHaveClass('container');
    expect(mainContainer).toHaveClass('mx-auto');
  });
  
  it('renders with custom props', () => {
    const customProps = {
      title: 'Custom Title',
      description: 'Custom description',
      keywords: 'custom, keywords',
      canonical: 'https://example.com/custom',
      ogImage: '/custom-image.png'
    };
    
    render(
      <Layout {...customProps}>
        <div>Content</div>
      </Layout>
    );
    
    // Check if the Head component is rendered
    const head = screen.getByTestId('mock-head');
    expect(head).toBeInTheDocument();
    
    // We can't directly test the Head content since it's mocked,
    // but we can verify that the component renders without errors
    expect(screen.getByText('Content')).toBeInTheDocument();
  });
});
