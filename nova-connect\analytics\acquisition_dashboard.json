{"dashboard": {"id": "novafuse_acquisition_dashboard", "title": "NovaFuse Acquisition Valuation Dashboard", "description": "Real-time valuation metrics for NovaFuse UAC", "filters": [{"name": "date_range", "title": "Date Range", "type": "date_range", "default": "last 90 days"}, {"name": "tier", "title": "Customer Tier", "type": "string", "default": "all", "values": ["all", "core", "secure", "enterprise", "ai_boost"]}], "elements": [{"id": "valuation_summary", "type": "statistic_group", "title": "Valuation Summary", "statistics": [{"title": "Current ARR", "value": "${current_arr}", "format": "currency", "comparison": {"value": "${previous_arr}", "label": "vs. Previous Month"}}, {"title": "Target Valuation", "value": "${target_valuation}", "format": "currency", "description": "Based on 10x ARR multiple"}, {"title": "Total Customers", "value": "${total_customers}", "format": "number", "comparison": {"value": "${previous_total_customers}", "label": "vs. Previous Month"}}, {"title": "Acquisition Readiness", "value": "${acquisition_readiness_score}", "format": "percentage", "description": "Overall readiness score"}]}, {"id": "arr_trend", "type": "time_series", "title": "ARR Growth Trend", "query": "SELECT month, annualized_revenue FROM monthly_aggregates ORDER BY month", "x_axis": {"field": "month", "title": "Month"}, "y_axis": {"field": "annualized_revenue", "title": "Annual Recurring Revenue", "format": "currency"}, "goal_line": {"value": 1000000, "label": "$1M ARR Target"}}, {"id": "customer_growth", "type": "time_series", "title": "Customer Growth", "query": "SELECT month, new_customers, active_customers FROM customer_growth JOIN monthly_aggregates USING(month) ORDER BY month", "x_axis": {"field": "month", "title": "Month"}, "y_axis": [{"field": "active_customers", "title": "Active Customers", "format": "number"}, {"field": "new_customers", "title": "New Customers", "format": "number"}]}, {"id": "retention_rate", "type": "time_series", "title": "Customer Retention Rate", "query": "SELECT current_month, retention_rate FROM customer_retention ORDER BY current_month", "x_axis": {"field": "current_month", "title": "Month"}, "y_axis": {"field": "retention_rate", "title": "Retention Rate", "format": "percentage", "min": 0, "max": 1}, "goal_line": {"value": 0.9, "label": "90% Target Retention"}}, {"id": "tier_distribution", "type": "pie_chart", "title": "Customer Tier Distribution", "query": "SELECT tier, customer_count, percentage FROM tier_distribution", "dimensions": [{"field": "tier", "title": "Tier"}], "measures": [{"field": "customer_count", "title": "Customer Count"}], "value_labels": "percentage"}, {"id": "usage_metrics", "type": "time_series", "title": "API Usage Metrics", "query": "SELECT week, avg_grc_checks_per_customer, avg_connector_executions_per_customer FROM usage_patterns ORDER BY week", "x_axis": {"field": "week", "title": "Week"}, "y_axis": [{"field": "avg_grc_checks_per_customer", "title": "Avg GRC Checks per Customer", "format": "number"}, {"field": "avg_connector_executions_per_customer", "title": "Avg Connector Executions per Customer", "format": "number"}]}, {"id": "revenue_by_tier", "type": "bar_chart", "title": "Revenue by Customer Tier", "query": "SELECT t.tier, SUM(dr.total_revenue) as revenue FROM daily_revenue dr JOIN customer_tiers t ON dr.customer_id = t.customer_id GROUP BY t.tier ORDER BY revenue DESC", "x_axis": {"field": "tier", "title": "Customer Tier"}, "y_axis": {"field": "revenue", "title": "Revenue", "format": "currency"}}, {"id": "valuation_projections", "type": "table", "title": "Valuation Projections", "query": "SELECT 'Conservative (5x ARR)' as model, conservative_valuation as valuation FROM valuation_metrics UNION ALL SELECT 'Target (10x ARR)', target_valuation FROM valuation_metrics UNION ALL SELECT 'Optimistic (15x ARR)', optimistic_valuation FROM valuation_metrics", "columns": [{"field": "model", "title": "Valuation Model"}, {"field": "valuation", "title": "Projected Valuation", "format": "currency"}]}, {"id": "growth_projections", "type": "table", "title": "ARR Growth Projections", "query": "SELECT 'Current' as timeframe, current_arr as arr FROM valuation_metrics UNION ALL SELECT '6 Months', projected_arr_6_months FROM valuation_metrics UNION ALL SELECT '12 Months', projected_arr_12_months FROM valuation_metrics UNION ALL SELECT '24 Months', projected_arr_24_months FROM valuation_metrics", "columns": [{"field": "timeframe", "title": "Timeframe"}, {"field": "arr", "title": "Projected ARR", "format": "currency"}]}], "layout": {"type": "grid", "rows": [{"height": "100px", "columns": [{"width": "100%", "elements": ["valuation_summary"]}]}, {"height": "300px", "columns": [{"width": "50%", "elements": ["arr_trend"]}, {"width": "50%", "elements": ["customer_growth"]}]}, {"height": "300px", "columns": [{"width": "50%", "elements": ["retention_rate"]}, {"width": "50%", "elements": ["tier_distribution"]}]}, {"height": "300px", "columns": [{"width": "50%", "elements": ["usage_metrics"]}, {"width": "50%", "elements": ["revenue_by_tier"]}]}, {"height": "200px", "columns": [{"width": "50%", "elements": ["valuation_projections"]}, {"width": "50%", "elements": ["growth_projections"]}]}]}, "theme": {"colors": {"primary": "#4285F4", "secondary": "#34A853", "accent": "#FBBC05", "error": "#EA4335", "warning": "#F9AB00", "info": "#1A73E8", "success": "#0F9D58"}, "font": {"family": "Google Sans, Roboto, <PERSON><PERSON>, sans-serif"}}, "refresh_rate": "15m", "export_options": {"pdf": true, "csv": true, "image": true, "scheduled_delivery": {"email": true, "frequency_options": ["daily", "weekly", "monthly"]}}}}