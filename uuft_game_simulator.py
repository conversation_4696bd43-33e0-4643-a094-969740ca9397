#!/usr/bin/env python3
"""
UUFT Game Theory Simulator

This module simulates strategic interactions in game theory scenarios according to UUFT principles,
analyzing how 18/82 patterns and π-related relationships affect decision-making processes.
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import os
import logging
import json
from collections import defaultdict
from uuft_game_analyzer import UUFTGameScenario

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('uuft_game_sim.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('UUFT_Game_Sim')

# Constants
PATTERN_1882_RATIO = 18 / 82
PI = np.pi
PI_10_CUBED = PI * 10**3
RESULTS_DIR = "uuft_results/game"
os.makedirs(RESULTS_DIR, exist_ok=True)

class UUFTGameSimulator:
    """Simulates strategic interactions in game theory scenarios according to UUFT principles."""
    
    def __init__(self, game_scenario, num_agents=100, learning_rate=0.1, uuft_parameters=None):
        """
        Initialize the game simulator.
        
        Args:
            game_scenario: UUFTGameScenario instance
            num_agents: Number of agents in the simulation
            learning_rate: Rate at which agents update their strategies
            uuft_parameters: Dictionary of UUFT-specific parameters
        """
        self.game = game_scenario
        self.num_agents = num_agents
        self.learning_rate = learning_rate
        self.uuft_parameters = uuft_parameters or {
            "pi_influence": 0.5,  # Influence of π-related patterns
            "pattern_1882_strength": 0.7,  # Strength of 18/82 pattern enforcement
            "temporal_stability": 0.8  # Stability of patterns over time
        }
        
        # Initialize agent strategies
        self.agent_strategies = self._initialize_strategies()
        
        # Initialize history
        self.history = []
        self.current_time = 0
        
        logger.info(f"Initialized game simulator with {num_agents} agents and UUFT parameters: {uuft_parameters}")
    
    def _initialize_strategies(self):
        """Initialize agent strategies."""
        # For 2-player, 2-strategy games, initialize mixed strategies
        if self.game.num_players == 2 and self.game.num_strategies == 2:
            # Each agent has a probability of playing each strategy
            strategies = np.random.uniform(0, 1, (self.num_agents, self.game.num_strategies))
            
            # Normalize to ensure probabilities sum to 1
            strategies = strategies / np.sum(strategies, axis=1, keepdims=True)
            
            return strategies
        else:
            # For other games, just randomly assign pure strategies
            return np.random.randint(0, self.game.num_strategies, self.num_agents)
    
    def step(self):
        """
        Perform one step of the simulation.
        
        Returns:
            Dictionary with step results
        """
        self.current_time += 1
        
        # For 2-player, 2-strategy games with mixed strategies
        if self.game.num_players == 2 and self.game.num_strategies == 2:
            return self._step_mixed_strategies()
        else:
            # For other games, use pure strategies
            return self._step_pure_strategies()
    
    def _step_mixed_strategies(self):
        """Perform one step with mixed strategies for 2x2 games."""
        # Pair agents randomly
        pairs = np.random.permutation(self.num_agents).reshape(-1, 2)
        
        # Track payoffs and strategy choices
        payoffs = np.zeros(self.num_agents)
        choices = np.zeros(self.num_agents, dtype=int)
        
        # For each pair, play the game
        for pair in pairs:
            # Extract agents
            agent1, agent2 = pair
            
            # Determine strategy choices based on probabilities
            choice1 = np.random.choice(self.game.num_strategies, p=self.agent_strategies[agent1])
            choice2 = np.random.choice(self.game.num_strategies, p=self.agent_strategies[agent2])
            
            # Record choices
            choices[agent1] = choice1
            choices[agent2] = choice2
            
            # Determine payoffs
            payoff1 = self.game.payoff_matrix[choice1, choice2, 0]
            payoff2 = self.game.payoff_matrix[choice1, choice2, 1]
            
            # Record payoffs
            payoffs[agent1] = payoff1
            payoffs[agent2] = payoff2
        
        # Update strategies based on payoffs
        self._update_mixed_strategies(payoffs, choices)
        
        # Calculate statistics
        strategy_dist = np.mean(self.agent_strategies, axis=0)
        avg_payoff = np.mean(payoffs)
        
        # Record history
        step_result = {
            "time": self.current_time,
            "strategy_distribution": strategy_dist.tolist(),
            "average_payoff": float(avg_payoff),
            "strategy_0_frequency": float(strategy_dist[0]),
            "payoffs": payoffs.tolist()
        }
        
        self.history.append(step_result)
        
        return step_result
    
    def _update_mixed_strategies(self, payoffs, choices):
        """
        Update mixed strategies based on payoffs and choices.
        
        Args:
            payoffs: Array of payoffs for each agent
            choices: Array of strategy choices for each agent
        """
        # Extract UUFT parameters
        pi_influence = self.uuft_parameters["pi_influence"]
        pattern_1882_strength = self.uuft_parameters["pattern_1882_strength"]
        temporal_stability = self.uuft_parameters["temporal_stability"]
        
        # Calculate current strategy distribution
        current_dist = np.mean(self.agent_strategies, axis=0)
        
        # Apply temporal stability - adjust learning rate based on pattern stability
        if self.current_time > 1 and len(self.history) > 1:
            # Calculate distribution change
            prev_dist = np.array(self.history[-1]["strategy_distribution"])
            dist_change = np.sum(np.abs(current_dist - prev_dist))
            
            # If change is too rapid, apply stabilizing factor
            if dist_change > 0.1:  # More than 10% change
                stability_factor = 1.0 - (dist_change - 0.1) * temporal_stability * 5
                stability_factor = max(0.5, min(1.0, stability_factor))  # Clamp to [0.5, 1.0]
            else:
                stability_factor = 1.0
        else:
            stability_factor = 1.0
        
        # Apply 18/82 pattern enforcement
        # Sort agents by payoff
        sorted_indices = np.argsort(payoffs)
        top_agents = sorted_indices[-int(self.num_agents * 0.18):]
        
        # Calculate target distribution based on 18/82 pattern
        if current_dist[0] < 0.5:  # If strategy 0 is minority
            target_ratio = PATTERN_1882_RATIO  # strategy_0 / strategy_1 should be low
        else:  # If strategy 0 is majority
            target_ratio = 1 / PATTERN_1882_RATIO  # strategy_0 / strategy_1 should be high
        
        # Calculate current ratio
        if current_dist[1] > 0:
            current_ratio = current_dist[0] / current_dist[1]
        else:
            current_ratio = float('inf')
        
        # Apply pattern adjustment
        pattern_adjustment = np.zeros((self.num_agents, self.game.num_strategies))
        
        if abs(current_ratio - target_ratio) > 0.1:
            # Need to adjust toward target ratio
            if current_ratio < target_ratio:
                # Boost strategy 0 for top agents
                for agent in top_agents:
                    pattern_adjustment[agent, 0] = pattern_1882_strength * 0.1
                    pattern_adjustment[agent, 1] = -pattern_1882_strength * 0.1
            else:
                # Boost strategy 1 for top agents
                for agent in top_agents:
                    pattern_adjustment[agent, 0] = -pattern_1882_strength * 0.1
                    pattern_adjustment[agent, 1] = pattern_1882_strength * 0.1
        
        # Apply π-influence
        pi_adjustment = np.zeros((self.num_agents, self.game.num_strategies))
        
        # Check if any distribution is close to π-related values
        pi_values = [1/PI, PI/10, 1/(PI*10)]
        for i, value in enumerate(pi_values):
            if abs(current_dist[0] - value) < 0.05:
                # If close to a π-related value, reinforce it
                pi_adjustment[:, 0] = pi_influence * 0.05 * np.sign(value - current_dist[0])
                pi_adjustment[:, 1] = -pi_adjustment[:, 0]
                break
        
        # Update strategies based on payoffs and choices
        for i in range(self.num_agents):
            # Basic reinforcement learning update
            update = np.zeros(self.game.num_strategies)
            update[choices[i]] = self.learning_rate * payoffs[i] * stability_factor
            
            # Apply adjustments
            update += pattern_adjustment[i] + pi_adjustment[i]
            
            # Update strategy probabilities
            self.agent_strategies[i] += update
            
            # Ensure probabilities are valid
            self.agent_strategies[i] = np.clip(self.agent_strategies[i], 0.01, 0.99)
            self.agent_strategies[i] /= np.sum(self.agent_strategies[i])
    
    def _step_pure_strategies(self):
        """Perform one step with pure strategies."""
        # Not implemented for this simplified version
        logger.warning("Pure strategy simulation not implemented in this version")
        return {"time": self.current_time, "error": "Pure strategy simulation not implemented"}
    
    def run_simulation(self, num_steps=100):
        """
        Run the complete simulation.
        
        Args:
            num_steps: Number of steps to simulate
            
        Returns:
            DataFrame with simulation results
        """
        logger.info(f"Running game simulation for {num_steps} steps")
        
        for _ in range(num_steps):
            self.step()
            
            # Log progress periodically
            if self.current_time % 10 == 0:
                strategy_dist = self.history[-1]["strategy_distribution"]
                logger.info(f"Step {self.current_time}: Strategy distribution = {strategy_dist}")
        
        # Convert history to DataFrame
        df = pd.DataFrame(self.history)
        
        return df
    
    def visualize_strategy_evolution(self, save_path=None):
        """
        Visualize the evolution of strategies over time.
        
        Args:
            save_path: Path to save the visualization
        """
        if not self.history:
            logger.warning("No simulation data to visualize")
            return
        
        plt.figure(figsize=(10, 6))
        
        # Extract data
        times = [entry["time"] for entry in self.history]
        strategy_0_freq = [entry["strategy_0_frequency"] for entry in self.history]
        
        # Plot strategy evolution
        plt.plot(times, strategy_0_freq, 'b-', linewidth=2, label='Strategy 0')
        plt.plot(times, [1 - freq for freq in strategy_0_freq], 'r-', linewidth=2, label='Strategy 1')
        
        # Add reference lines
        plt.axhline(y=0.18, color='g', linestyle='--', alpha=0.5, label='18%')
        plt.axhline(y=0.82, color='g', linestyle='--', alpha=0.5, label='82%')
        
        # Add π-related reference lines
        plt.axhline(y=1/PI, color='purple', linestyle=':', alpha=0.5, label='1/π')
        plt.axhline(y=PI/10, color='orange', linestyle=':', alpha=0.5, label='π/10')
        
        # Add labels and title
        plt.xlabel('Time Step')
        plt.ylabel('Strategy Frequency')
        plt.title('Evolution of Strategy Distribution')
        plt.grid(True, alpha=0.3)
        plt.legend()
        
        # Save if path provided
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Strategy evolution visualization saved to {save_path}")
        
        plt.close()
    
    def visualize_payoff_distribution(self, save_path=None):
        """
        Visualize the distribution of payoffs in the final step.
        
        Args:
            save_path: Path to save the visualization
        """
        if not self.history:
            logger.warning("No simulation data to visualize")
            return
        
        plt.figure(figsize=(10, 6))
        
        # Get final payoffs
        final_payoffs = np.array(self.history[-1]["payoffs"])
        
        # Plot histogram
        n, bins, patches = plt.hist(final_payoffs, bins=20, alpha=0.7, color='blue')
        
        # Add reference lines for 18/82 split
        sorted_payoffs = np.sort(final_payoffs)
        split_index = int(len(sorted_payoffs) * 0.18)
        split_value = sorted_payoffs[split_index]
        
        plt.axvline(x=split_value, color='red', linestyle='--',
                   label=f'18/82 Split')
        
        # Add labels and title
        plt.xlabel('Payoff')
        plt.ylabel('Frequency')
        plt.title('Distribution of Payoffs')
        plt.grid(True, alpha=0.3)
        plt.legend()
        
        # Save if path provided
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Payoff distribution visualization saved to {save_path}")
        
        plt.close()
    
    def to_dict(self):
        """Convert simulator to dictionary for serialization."""
        return {
            "num_agents": self.num_agents,
            "learning_rate": self.learning_rate,
            "uuft_parameters": self.uuft_parameters,
            "current_time": self.current_time,
            "agent_strategies": self.agent_strategies.tolist(),
            "history": self.history
        }
    
    @classmethod
    def from_dict(cls, data, game_scenario):
        """Create simulator from dictionary."""
        simulator = cls(
            game_scenario=game_scenario,
            num_agents=data["num_agents"],
            learning_rate=data["learning_rate"],
            uuft_parameters=data["uuft_parameters"]
        )
        
        simulator.current_time = data["current_time"]
        simulator.agent_strategies = np.array(data["agent_strategies"])
        simulator.history = data["history"]
        
        return simulator
