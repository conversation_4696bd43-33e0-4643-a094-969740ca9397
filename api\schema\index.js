/**
 * Schema API
 * 
 * This module provides schema management functionality.
 */

const schemaRoutes = require('./schemaRoutes');
const schemaController = require('./schemaController');
const schemaGenerator = require('./schemaGenerator');
const { exampleSchema } = require('./schemaDefinition');

/**
 * Initialize Schema API
 * @param {Object} app - Express app
 */
function initialize(app) {
  // Mount routes
  app.use('/api/v1/schemas', schemaRoutes);
  
  // Create custom schemas directory
  const fs = require('fs');
  const path = require('path');
  const customSchemasDir = path.join(__dirname, 'custom');
  
  if (!fs.existsSync(customSchemasDir)) {
    fs.mkdirSync(customSchemasDir, { recursive: true });
  }
}

module.exports = {
  initialize,
  schemaRoutes,
  schemaController,
  schemaGenerator,
  exampleSchema
};
