/**
 * Schema Generator
 * 
 * This utility generates UI schemas from Mongoose models.
 */

const pluralize = require('pluralize');
const { camelCase, startCase } = require('lodash');

/**
 * Map Mongoose schema type to UI field type
 * @param {string} mongooseType - Mongoose schema type
 * @returns {string} - UI field type
 */
const mapMongooseTypeToUIType = (mongooseType) => {
  const typeMap = {
    'String': 'string',
    'Number': 'number',
    'Date': 'date',
    'Boolean': 'boolean',
    'ObjectID': 'string',
    'Array': 'array',
    'Object': 'object',
    'Mixed': 'object',
    'Decimal128': 'number',
    'Map': 'object',
    'Buffer': 'string'
  };
  
  return typeMap[mongooseType] || 'string';
};

/**
 * Format field label from field name
 * @param {string} fieldName - Field name
 * @returns {string} - Formatted field label
 */
const formatFieldLabel = (fieldName) => {
  return startCase(fieldName);
};

/**
 * Determine appropriate UI component based on field type and properties
 * @param {Object} path - Mongoose schema path
 * @returns {string} - UI component name
 */
const determineUIComponent = (path) => {
  const type = path.instance;
  const options = path.options || {};
  
  // Check for enum values
  if (options.enum && options.enum.length > 0) {
    return options.enum.length <= 5 ? 'radio-group' : 'select';
  }
  
  // Check for references (ObjectId with ref)
  if (type === 'ObjectID' && options.ref) {
    return 'entity-select';
  }
  
  // Map types to components
  switch (type) {
    case 'String':
      if (options.maxlength && options.maxlength > 100) {
        return 'textarea';
      }
      return 'text-input';
    case 'Number':
      return 'number-input';
    case 'Date':
      return 'date-picker';
    case 'Boolean':
      return 'checkbox';
    case 'Array':
      if (path.schema) {
        return 'array-input';
      }
      return 'tags-input';
    case 'Object':
      return 'object-input';
    default:
      return 'text-input';
  }
};

/**
 * Extract validation rules from Mongoose schema path
 * @param {Object} path - Mongoose schema path
 * @returns {Object} - Validation rules
 */
const extractValidationRules = (path) => {
  const options = path.options || {};
  const validation = {};
  
  if (options.required) {
    validation.required = true;
  }
  
  if (path.instance === 'String') {
    if (options.minlength !== undefined) {
      validation.minLength = options.minlength;
    }
    if (options.maxlength !== undefined) {
      validation.maxLength = options.maxlength;
    }
    if (options.match) {
      validation.pattern = options.match.toString().slice(1, -1);
    }
  }
  
  if (path.instance === 'Number') {
    if (options.min !== undefined) {
      validation.min = options.min;
    }
    if (options.max !== undefined) {
      validation.max = options.max;
    }
  }
  
  return Object.keys(validation).length > 0 ? validation : undefined;
};

/**
 * Generate default list view from schema fields
 * @param {Array} fields - Schema fields
 * @returns {Object} - Default list view configuration
 */
const generateDefaultListView = (fields) => {
  // Select fields that make sense in a list view (skip long text, arrays, objects)
  const listFields = fields.filter(field => {
    return !['array', 'object'].includes(field.type) && 
           !['textarea', 'array-input', 'object-input'].includes(field.uiComponent);
  }).slice(0, 5); // Limit to 5 columns
  
  const columns = listFields.map(field => ({
    field: field.name,
    label: field.label,
    sortable: true,
    filterable: true
  }));
  
  return {
    columns,
    actions: ['view', 'edit', 'delete'],
    defaultSort: fields[0]?.name || 'createdAt',
    defaultSortDirection: 'asc',
    pageSize: 10,
    searchable: true,
    searchFields: fields.filter(f => f.type === 'string').map(f => f.name)
  };
};

/**
 * Generate default detail view from schema fields
 * @param {Array} fields - Schema fields
 * @returns {Object} - Default detail view configuration
 */
const generateDefaultDetailView = (fields) => {
  // Group fields into logical sections
  const basicFields = fields.filter(f => 
    !['array', 'object'].includes(f.type) || 
    (f.type === 'object' && !f.fields)
  ).map(f => f.name);
  
  const sections = [
    {
      title: 'Basic Information',
      fields: basicFields
    }
  ];
  
  // Add sections for array and object fields
  fields.filter(f => 
    (f.type === 'array' && f.fields) || 
    (f.type === 'object' && f.fields)
  ).forEach(field => {
    sections.push({
      title: field.label,
      fields: [field.name],
      collapsible: true
    });
  });
  
  return {
    sections,
    actions: ['edit', 'delete']
  };
};

/**
 * Generate default form view from schema fields
 * @param {Array} fields - Schema fields
 * @returns {Object} - Default form view configuration
 */
const generateDefaultFormView = (fields) => {
  // Use the same sections as detail view
  const detailView = generateDefaultDetailView(fields);
  
  return {
    sections: detailView.sections,
    submitLabel: 'Save',
    cancelLabel: 'Cancel',
    showReset: true,
    resetLabel: 'Reset'
  };
};

/**
 * Generate schema from Mongoose model
 * @param {Object} model - Mongoose model
 * @param {Object} options - Options
 * @returns {Object} - Generated schema
 */
const generateSchemaFromModel = (model, options = {}) => {
  const modelName = model.modelName;
  const collectionName = model.collection.name;
  
  const schema = {
    entityName: modelName,
    entityNamePlural: pluralize(modelName),
    apiEndpoint: `/api/v1/${camelCase(collectionName)}`,
    fields: []
  };
  
  // Process schema paths
  Object.keys(model.schema.paths).forEach(pathName => {
    // Skip internal fields
    if (pathName === '_id' || pathName === '__v' || pathName === 'createdAt' || pathName === 'updatedAt') {
      return;
    }
    
    const path = model.schema.paths[pathName];
    const fieldType = mapMongooseTypeToUIType(path.instance);
    
    const field = {
      name: pathName,
      type: fieldType,
      label: formatFieldLabel(pathName),
      required: path.isRequired || false,
      uiComponent: determineUIComponent(path)
    };
    
    // Add validation rules if any
    const validation = extractValidationRules(path);
    if (validation) {
      field.validation = validation;
    }
    
    // Add options for enum fields
    if (path.options && path.options.enum && path.options.enum.length > 0) {
      field.options = {
        source: 'static',
        values: path.options.enum.map(value => ({
          value,
          label: startCase(value.toString())
        }))
      };
    }
    
    // Add reference options
    if (path.instance === 'ObjectID' && path.options && path.options.ref) {
      const refModel = path.options.ref;
      field.options = {
        source: 'api',
        endpoint: `/api/v1/${camelCase(pluralize(refModel).toLowerCase())}`,
        valueField: '_id',
        labelField: 'name' // Assuming most models have a name field
      };
    }
    
    // Handle array of subdocuments
    if (path.instance === 'Array' && path.schema) {
      field.fields = [];
      
      // Process subdocument schema
      Object.keys(path.schema.paths).forEach(subPathName => {
        if (subPathName === '_id' || subPathName === '__v') {
          return;
        }
        
        const subPath = path.schema.paths[subPathName];
        const subFieldType = mapMongooseTypeToUIType(subPath.instance);
        
        const subField = {
          name: subPathName,
          type: subFieldType,
          label: formatFieldLabel(subPathName),
          required: subPath.isRequired || false,
          uiComponent: determineUIComponent(subPath)
        };
        
        // Add validation rules if any
        const subValidation = extractValidationRules(subPath);
        if (subValidation) {
          subField.validation = subValidation;
        }
        
        field.fields.push(subField);
      });
    }
    
    schema.fields.push(field);
  });
  
  // Generate views
  schema.listView = generateDefaultListView(schema.fields);
  schema.detailView = generateDefaultDetailView(schema.fields);
  schema.formView = generateDefaultFormView(schema.fields);
  
  return schema;
};

/**
 * Generate schema from JSON object
 * @param {Object} jsonObject - JSON object
 * @param {Object} options - Options
 * @returns {Object} - Generated schema
 */
const generateSchemaFromJSON = (jsonObject, entityName, options = {}) => {
  const schema = {
    entityName,
    entityNamePlural: pluralize(entityName),
    apiEndpoint: `/api/v1/${camelCase(pluralize(entityName).toLowerCase())}`,
    fields: []
  };
  
  // Process JSON properties
  Object.keys(jsonObject).forEach(propName => {
    const value = jsonObject[propName];
    const type = typeof value;
    
    let fieldType;
    let uiComponent;
    
    // Determine field type and UI component
    if (type === 'string') {
      fieldType = 'string';
      uiComponent = value.length > 100 ? 'textarea' : 'text-input';
    } else if (type === 'number') {
      fieldType = 'number';
      uiComponent = 'number-input';
    } else if (type === 'boolean') {
      fieldType = 'boolean';
      uiComponent = 'checkbox';
    } else if (type === 'object') {
      if (value === null) {
        fieldType = 'string';
        uiComponent = 'text-input';
      } else if (Array.isArray(value)) {
        fieldType = 'array';
        uiComponent = 'array-input';
      } else {
        fieldType = 'object';
        uiComponent = 'object-input';
      }
    } else {
      fieldType = 'string';
      uiComponent = 'text-input';
    }
    
    const field = {
      name: propName,
      type: fieldType,
      label: formatFieldLabel(propName),
      uiComponent
    };
    
    // Handle nested objects
    if (fieldType === 'object' && value !== null && !Array.isArray(value)) {
      field.fields = [];
      
      Object.keys(value).forEach(subPropName => {
        const subValue = value[subPropName];
        const subType = typeof subValue;
        
        let subFieldType;
        let subUiComponent;
        
        if (subType === 'string') {
          subFieldType = 'string';
          subUiComponent = subValue.length > 100 ? 'textarea' : 'text-input';
        } else if (subType === 'number') {
          subFieldType = 'number';
          subUiComponent = 'number-input';
        } else if (subType === 'boolean') {
          subFieldType = 'boolean';
          subUiComponent = 'checkbox';
        } else {
          subFieldType = 'string';
          subUiComponent = 'text-input';
        }
        
        const subField = {
          name: subPropName,
          type: subFieldType,
          label: formatFieldLabel(subPropName),
          uiComponent: subUiComponent
        };
        
        field.fields.push(subField);
      });
    }
    
    // Handle arrays
    if (fieldType === 'array' && Array.isArray(value) && value.length > 0) {
      const firstItem = value[0];
      
      if (typeof firstItem === 'object' && firstItem !== null) {
        field.fields = [];
        
        Object.keys(firstItem).forEach(subPropName => {
          const subValue = firstItem[subPropName];
          const subType = typeof subValue;
          
          let subFieldType;
          let subUiComponent;
          
          if (subType === 'string') {
            subFieldType = 'string';
            subUiComponent = subValue.length > 100 ? 'textarea' : 'text-input';
          } else if (subType === 'number') {
            subFieldType = 'number';
            subUiComponent = 'number-input';
          } else if (subType === 'boolean') {
            subFieldType = 'boolean';
            subUiComponent = 'checkbox';
          } else {
            subFieldType = 'string';
            subUiComponent = 'text-input';
          }
          
          const subField = {
            name: subPropName,
            type: subFieldType,
            label: formatFieldLabel(subPropName),
            uiComponent: subUiComponent
          };
          
          field.fields.push(subField);
        });
      }
    }
    
    schema.fields.push(field);
  });
  
  // Generate views
  schema.listView = generateDefaultListView(schema.fields);
  schema.detailView = generateDefaultDetailView(schema.fields);
  schema.formView = generateDefaultFormView(schema.fields);
  
  return schema;
};

module.exports = {
  generateSchemaFromModel,
  generateSchemaFromJSON,
  mapMongooseTypeToUIType,
  formatFieldLabel,
  determineUIComponent,
  extractValidationRules,
  generateDefaultListView,
  generateDefaultDetailView,
  generateDefaultFormView
};
