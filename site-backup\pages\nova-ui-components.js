import React from 'react';
import PageWithSidebar from '../components/PageWithSidebar';

export default function NovaUIComponents() {
  const sidebarItems = [
    { type: 'category', label: 'Component Categories', items: [
      { label: 'Data Display', href: '#data-display' },
      { label: 'Input & Forms', href: '#input-forms' },
      { label: 'Navigation', href: '#navigation' },
      { label: 'Feedback', href: '#feedback' },
      { label: 'Layout', href: '#layout' }
    ]},
    { type: 'category', label: 'Component Showcase', items: [
      { label: 'Data Tables', href: '#data-tables' },
      { label: 'Risk Heatmaps', href: '#risk-heatmaps' },
      { label: 'Compliance Cards', href: '#compliance-cards' }
    ]},
    { type: 'category', label: 'Resources', items: [
      { label: 'Integration', href: '#integration' },
      { label: 'Customization', href: '#customization' },
      { label: 'Documentation', href: '#documentation' }
    ]}
  ];

  return (
    <PageWithSidebar sidebarItems={sidebarItems} sidebarTitle="NovaUI Components" title="NovaUI Components - NovaFuse API Superstore">

      {/* Hero Section */}
      <div className="bg-secondary p-8 rounded-lg mb-8">
        <h2 className="text-3xl font-bold mb-4">NovaUI Components</h2>
        <p className="text-xl mb-6">
          A comprehensive library of UI components designed for governance, risk, and compliance applications.
          NovaUI components are built with accessibility, consistency, and enterprise requirements in mind.
        </p>
        <div className="flex flex-wrap gap-4">
          <div className="accent-bg text-white px-6 py-3 rounded-lg font-bold hover:bg-blue-700 inline-block">
            Get Started
          </div>
          <div className="border border-blue-600 text-blue-600 px-6 py-3 rounded-lg font-bold hover:bg-blue-900 hover:bg-opacity-20 inline-block">
            View Documentation
          </div>
        </div>
      </div>

      {/* Component Categories */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
        <div id="data-display" className="bg-secondary p-6 rounded-lg">
          <h3 className="text-xl font-bold mb-3">Data Display</h3>
          <p className="text-gray-300 mb-4">
            Components for displaying data in various formats, including tables, cards, and visualizations.
          </p>
          <ul className="space-y-2">
            <li className="flex items-center">
              <span className="text-blue-500 mr-2">→</span>
              <span>Data Tables</span>
            </li>
            <li className="flex items-center">
              <span className="text-blue-500 mr-2">→</span>
              <span>Compliance Cards</span>
            </li>
            <li className="flex items-center">
              <span className="text-blue-500 mr-2">→</span>
              <span>Risk Heatmaps</span>
            </li>
            <li className="flex items-center">
              <span className="text-blue-500 mr-2">→</span>
              <span>Compliance Dashboards</span>
            </li>
          </ul>
        </div>

        <div id="input-forms" className="bg-secondary p-6 rounded-lg">
          <h3 className="text-xl font-bold mb-3">Input & Forms</h3>
          <p className="text-gray-300 mb-4">
            Form components designed for compliance workflows, with validation and accessibility built-in.
          </p>
          <ul className="space-y-2">
            <li className="flex items-center">
              <span className="text-blue-500 mr-2">→</span>
              <span>Policy Forms</span>
            </li>
            <li className="flex items-center">
              <span className="text-blue-500 mr-2">→</span>
              <span>Risk Assessment Forms</span>
            </li>
            <li className="flex items-center">
              <span className="text-blue-500 mr-2">→</span>
              <span>Compliance Questionnaires</span>
            </li>
            <li className="flex items-center">
              <span className="text-blue-500 mr-2">→</span>
              <span>Evidence Collection Forms</span>
            </li>
          </ul>
        </div>

        <div id="navigation" className="bg-secondary p-6 rounded-lg">
          <h3 className="text-xl font-bold mb-3">Navigation</h3>
          <p className="text-gray-300 mb-4">
            Navigation components for complex GRC applications, with role-based access control integration.
          </p>
          <ul className="space-y-2">
            <li className="flex items-center">
              <span className="text-blue-500 mr-2">→</span>
              <span>Compliance Sidebar</span>
            </li>
            <li className="flex items-center">
              <span className="text-blue-500 mr-2">→</span>
              <span>Framework Navigator</span>
            </li>
            <li className="flex items-center">
              <span className="text-blue-500 mr-2">→</span>
              <span>Control Breadcrumbs</span>
            </li>
            <li className="flex items-center">
              <span className="text-blue-500 mr-2">→</span>
              <span>Role-Based Menus</span>
            </li>
          </ul>
        </div>
      </div>

      {/* Component Showcase */}
      <div id="component-showcase" className="bg-secondary p-6 rounded-lg mb-12">
        <h3 className="text-2xl font-bold mb-6 text-center">Component Showcase</h3>

        {/* Data Table Example */}
        <div id="data-tables" className="mb-8">
          <h4 className="text-xl font-semibold mb-4">Data Table</h4>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-800">
                <tr>
                  <th className="p-3 text-left">Control ID</th>
                  <th className="p-3 text-left">Description</th>
                  <th className="p-3 text-left">Framework</th>
                  <th className="p-3 text-left">Status</th>
                  <th className="p-3 text-left">Last Tested</th>
                </tr>
              </thead>
              <tbody>
                <tr className="border-b border-gray-700">
                  <td className="p-3">AC-1</td>
                  <td className="p-3">Access Control Policy and Procedures</td>
                  <td className="p-3">NIST 800-53</td>
                  <td className="p-3">
                    <span className="bg-green-900 text-green-300 px-2 py-1 rounded text-xs">Compliant</span>
                  </td>
                  <td className="p-3">2025-01-15</td>
                </tr>
                <tr className="border-b border-gray-700">
                  <td className="p-3">AC-2</td>
                  <td className="p-3">Account Management</td>
                  <td className="p-3">NIST 800-53</td>
                  <td className="p-3">
                    <span className="bg-yellow-900 text-yellow-300 px-2 py-1 rounded text-xs">In Progress</span>
                  </td>
                  <td className="p-3">2024-12-10</td>
                </tr>
                <tr className="border-b border-gray-700">
                  <td className="p-3">AC-3</td>
                  <td className="p-3">Access Enforcement</td>
                  <td className="p-3">NIST 800-53</td>
                  <td className="p-3">
                    <span className="bg-red-900 text-red-300 px-2 py-1 rounded text-xs">Non-Compliant</span>
                  </td>
                  <td className="p-3">2024-11-05</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* Risk Heatmap Example */}
        <div id="risk-heatmaps" className="mb-8">
          <h4 className="text-xl font-semibold mb-4">Risk Heatmap</h4>
          <div className="grid grid-cols-5 gap-1 max-w-md mx-auto">
            <div className="bg-green-900 h-16 rounded flex items-center justify-center text-white">Very Low</div>
            <div className="bg-green-800 h-16 rounded flex items-center justify-center text-white">Low</div>
            <div className="bg-yellow-700 h-16 rounded flex items-center justify-center text-white">Medium</div>
            <div className="bg-orange-700 h-16 rounded flex items-center justify-center text-white">High</div>
            <div className="bg-red-700 h-16 rounded flex items-center justify-center text-white">Very High</div>

            <div className="bg-green-800 h-16 rounded flex items-center justify-center text-white">Low</div>
            <div className="bg-yellow-700 h-16 rounded flex items-center justify-center text-white">Medium</div>
            <div className="bg-yellow-700 h-16 rounded flex items-center justify-center text-white">Medium</div>
            <div className="bg-orange-700 h-16 rounded flex items-center justify-center text-white">High</div>
            <div className="bg-red-700 h-16 rounded flex items-center justify-center text-white">Very High</div>

            <div className="bg-yellow-700 h-16 rounded flex items-center justify-center text-white">Medium</div>
            <div className="bg-yellow-700 h-16 rounded flex items-center justify-center text-white">Medium</div>
            <div className="bg-orange-700 h-16 rounded flex items-center justify-center text-white">High</div>
            <div className="bg-red-700 h-16 rounded flex items-center justify-center text-white">Very High</div>
            <div className="bg-red-700 h-16 rounded flex items-center justify-center text-white">Very High</div>

            <div className="bg-yellow-700 h-16 rounded flex items-center justify-center text-white">Medium</div>
            <div className="bg-orange-700 h-16 rounded flex items-center justify-center text-white">High</div>
            <div className="bg-orange-700 h-16 rounded flex items-center justify-center text-white">High</div>
            <div className="bg-red-700 h-16 rounded flex items-center justify-center text-white">Very High</div>
            <div className="bg-red-700 h-16 rounded flex items-center justify-center text-white">Very High</div>

            <div className="bg-orange-700 h-16 rounded flex items-center justify-center text-white">High</div>
            <div className="bg-orange-700 h-16 rounded flex items-center justify-center text-white">High</div>
            <div className="bg-red-700 h-16 rounded flex items-center justify-center text-white">Very High</div>
            <div className="bg-red-700 h-16 rounded flex items-center justify-center text-white">Very High</div>
            <div className="bg-red-700 h-16 rounded flex items-center justify-center text-white">Very High</div>
          </div>
          <div className="flex justify-between max-w-md mx-auto mt-2">
            <div className="text-sm text-gray-400">Impact</div>
            <div className="text-sm text-gray-400">Likelihood →</div>
          </div>
        </div>

        {/* Compliance Card Example */}
        <div id="compliance-cards">
          <h4 className="text-xl font-semibold mb-4">Compliance Cards</h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-gray-800 p-4 rounded-lg border-l-4 border-green-500">
              <div className="flex justify-between items-start mb-2">
                <h5 className="font-semibold">SOC 2 Type 2</h5>
                <span className="bg-green-900 text-green-300 px-2 py-1 rounded text-xs">Compliant</span>
              </div>
              <p className="text-sm text-gray-400 mb-3">Last audit: January 15, 2025</p>
              <div className="flex justify-between text-sm">
                <span>Controls: 124/124</span>
                <span className="text-green-400">100%</span>
              </div>
              <div className="w-full bg-gray-700 h-2 mt-1 rounded-full overflow-hidden">
                <div className="bg-green-500 h-full rounded-full" style={{ width: '100%' }}></div>
              </div>
            </div>

            <div className="bg-gray-800 p-4 rounded-lg border-l-4 border-yellow-500">
              <div className="flex justify-between items-start mb-2">
                <h5 className="font-semibold">HIPAA</h5>
                <span className="bg-yellow-900 text-yellow-300 px-2 py-1 rounded text-xs">In Progress</span>
              </div>
              <p className="text-sm text-gray-400 mb-3">Next audit: March 10, 2025</p>
              <div className="flex justify-between text-sm">
                <span>Controls: 78/92</span>
                <span className="text-yellow-400">85%</span>
              </div>
              <div className="w-full bg-gray-700 h-2 mt-1 rounded-full overflow-hidden">
                <div className="bg-yellow-500 h-full rounded-full" style={{ width: '85%' }}></div>
              </div>
            </div>

            <div className="bg-gray-800 p-4 rounded-lg border-l-4 border-red-500">
              <div className="flex justify-between items-start mb-2">
                <h5 className="font-semibold">GDPR</h5>
                <span className="bg-red-900 text-red-300 px-2 py-1 rounded text-xs">At Risk</span>
              </div>
              <p className="text-sm text-gray-400 mb-3">Remediation due: February 28, 2025</p>
              <div className="flex justify-between text-sm">
                <span>Controls: 45/68</span>
                <span className="text-red-400">66%</span>
              </div>
              <div className="w-full bg-gray-700 h-2 mt-1 rounded-full overflow-hidden">
                <div className="bg-red-500 h-full rounded-full" style={{ width: '66%' }}></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Integration & Usage */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
        <div id="integration" className="bg-secondary p-6 rounded-lg">
          <h3 className="text-xl font-bold mb-4">Integration</h3>
          <p className="text-gray-300 mb-4">
            NovaUI components are designed to work seamlessly with popular frontend frameworks and libraries.
          </p>

          <div className="bg-gray-900 p-4 rounded-lg text-green-400 mb-4 overflow-x-auto">
            <pre>{`// Install NovaUI components
npm install @novafuse/nova-ui

// Import and use components
import { ComplianceTable, RiskHeatmap } from '@novafuse/nova-ui';

function ComplianceDashboard() {
  return (
    <div>
      <ComplianceTable
        data={controlsData}
        framework="NIST"
      />
      <RiskHeatmap
        risks={riskData}
        onRiskSelect={handleRiskSelect}
      />
    </div>
  );
}`}</pre>
          </div>

          <p className="text-gray-300">
            NovaUI components are framework-agnostic and can be used with React, Vue, Angular, or any other modern frontend framework.
          </p>
        </div>

        <div id="customization" className="bg-secondary p-6 rounded-lg">
          <h3 className="text-xl font-bold mb-4">Customization</h3>
          <p className="text-gray-300 mb-4">
            NovaUI components are highly customizable to match your brand and design system.
          </p>

          <div className="bg-gray-900 p-4 rounded-lg text-green-400 mb-4 overflow-x-auto">
            <pre>{`// Create a custom theme
const customTheme = {
  colors: {
    primary: '#3B82F6',
    secondary: '#1E293B',
    success: '#10B981',
    warning: '#F59E0B',
    danger: '#EF4444',
    background: '#0F172A'
  },
  typography: {
    fontFamily: '"Inter", sans-serif',
    headingFontFamily: '"Montserrat", sans-serif'
  },
  borderRadius: '0.375rem'
};

// Apply the theme
import { NovaUIProvider } from '@novafuse/nova-ui';

function App() {
  return (
    <NovaUIProvider theme={customTheme}>
      <YourApplication />
    </NovaUIProvider>
  );
}`}</pre>
          </div>

          <p className="text-gray-300">
            Every component can be customized using theme variables or component-specific props.
          </p>
        </div>
      </div>

      {/* Call to Action */}
      <div className="bg-secondary p-6 rounded-lg">
        <div className="md:flex justify-between items-center">
          <div className="mb-6 md:mb-0">
            <h3 className="text-2xl font-bold mb-2">Ready to Get Started?</h3>
            <p className="text-gray-300">
              Explore the NovaUI component library and start building your GRC application today.
            </p>
          </div>
          <div className="flex flex-wrap gap-4">
            <div className="accent-bg text-white px-6 py-3 rounded-lg font-bold hover:bg-blue-700 inline-block">
              View Documentation
            </div>
            <div className="border border-blue-600 text-blue-600 px-6 py-3 rounded-lg font-bold hover:bg-blue-900 hover:bg-opacity-20 inline-block">
              Download Storybook
            </div>
          </div>
        </div>
      </div>
    </PageWithSidebar>
  );
}
