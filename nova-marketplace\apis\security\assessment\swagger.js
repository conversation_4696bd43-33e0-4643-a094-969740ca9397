/**
 * @swagger
 * tags:
 *   name: Security Assessment
 *   description: Security Assessment API
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     SecurityAssessment:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: Unique identifier for the security assessment
 *         title:
 *           type: string
 *           description: Title of the security assessment
 *         description:
 *           type: string
 *           description: Description of the security assessment
 *         type:
 *           type: string
 *           enum: [internal, external, vendor, compliance]
 *           description: Type of security assessment
 *         status:
 *           type: string
 *           enum: [planned, in-progress, completed, cancelled]
 *           description: Status of the security assessment
 *         scope:
 *           type: string
 *           description: Scope of the security assessment
 *         startDate:
 *           type: string
 *           format: date
 *           description: Start date of the security assessment
 *         endDate:
 *           type: string
 *           format: date
 *           description: End date of the security assessment
 *         findings:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/SecurityFinding'
 *         assignedTo:
 *           type: string
 *           description: Person or team assigned to the assessment
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the assessment was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the assessment was last updated
 *       required:
 *         - id
 *         - title
 *         - type
 *         - status
 *         - startDate
 *         - createdAt
 *         - updatedAt
 *     
 *     SecurityAssessmentInput:
 *       type: object
 *       properties:
 *         title:
 *           type: string
 *           description: Title of the security assessment
 *         description:
 *           type: string
 *           description: Description of the security assessment
 *         type:
 *           type: string
 *           enum: [internal, external, vendor, compliance]
 *           description: Type of security assessment
 *         status:
 *           type: string
 *           enum: [planned, in-progress, completed, cancelled]
 *           description: Status of the security assessment
 *         scope:
 *           type: string
 *           description: Scope of the security assessment
 *         startDate:
 *           type: string
 *           format: date
 *           description: Start date of the security assessment
 *         endDate:
 *           type: string
 *           format: date
 *           description: End date of the security assessment
 *         assignedTo:
 *           type: string
 *           description: Person or team assigned to the assessment
 *       required:
 *         - title
 *         - type
 *         - status
 *         - startDate
 *     
 *     SecurityFinding:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: Unique identifier for the security finding
 *         title:
 *           type: string
 *           description: Title of the security finding
 *         description:
 *           type: string
 *           description: Description of the security finding
 *         severity:
 *           type: string
 *           enum: [critical, high, medium, low, info]
 *           description: Severity of the security finding
 *         status:
 *           type: string
 *           enum: [open, in-remediation, remediated, accepted, false-positive]
 *           description: Status of the security finding
 *         remediation:
 *           type: string
 *           description: Recommended remediation steps
 *         assignedTo:
 *           type: string
 *           description: Person or team assigned to remediate the finding
 *         dueDate:
 *           type: string
 *           format: date
 *           description: Due date for remediation
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the finding was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the finding was last updated
 *       required:
 *         - id
 *         - title
 *         - severity
 *         - status
 *         - createdAt
 *         - updatedAt
 *     
 *     SecurityFindingInput:
 *       type: object
 *       properties:
 *         title:
 *           type: string
 *           description: Title of the security finding
 *         description:
 *           type: string
 *           description: Description of the security finding
 *         severity:
 *           type: string
 *           enum: [critical, high, medium, low, info]
 *           description: Severity of the security finding
 *         status:
 *           type: string
 *           enum: [open, in-remediation, remediated, accepted, false-positive]
 *           description: Status of the security finding
 *         remediation:
 *           type: string
 *           description: Recommended remediation steps
 *         assignedTo:
 *           type: string
 *           description: Person or team assigned to remediate the finding
 *         dueDate:
 *           type: string
 *           format: date
 *           description: Due date for remediation
 *       required:
 *         - title
 *         - severity
 *         - status
 */
