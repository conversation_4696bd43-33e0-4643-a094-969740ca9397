/**
 * Resource Lock Controller
 * 
 * This controller handles API requests related to resource locks.
 */

const ResourceLockService = require('../services/ResourceLockService');
const { ValidationError } = require('../utils/errors');

class ResourceLockController {
  constructor() {
    this.resourceLockService = new ResourceLockService();
  }

  /**
   * Get all resource locks
   */
  async getAllResourceLocks(req, res, next) {
    try {
      const filters = req.query;
      const locks = await this.resourceLockService.getAllResourceLocks(filters);
      res.json(locks);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get resource locks for a team
   */
  async getResourceLocksForTeam(req, res, next) {
    try {
      const { id } = req.params;
      const filters = req.query;
      
      if (!id) {
        throw new ValidationError('Team ID is required');
      }
      
      const locks = await this.resourceLockService.getResourceLocksForTeam(id, filters);
      res.json(locks);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get resource lock by ID
   */
  async getResourceLockById(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Resource lock ID is required');
      }
      
      const lock = await this.resourceLockService.getResourceLockById(id);
      res.json(lock);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Check if a resource is locked
   */
  async isResourceLocked(req, res, next) {
    try {
      const { resourceType, resourceId } = req.params;
      const { environmentId } = req.query;
      
      if (!resourceType) {
        throw new ValidationError('Resource type is required');
      }
      
      if (!resourceId) {
        throw new ValidationError('Resource ID is required');
      }
      
      const isLocked = await this.resourceLockService.isResourceLocked(
        resourceType, 
        resourceId, 
        environmentId
      );
      
      res.json({ isLocked });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Create a new resource lock
   */
  async createResourceLock(req, res, next) {
    try {
      const data = req.body;
      
      if (!data) {
        throw new ValidationError('Resource lock data is required');
      }
      
      const lock = await this.resourceLockService.createResourceLock(data, req.user.id);
      res.status(201).json(lock);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update a resource lock
   */
  async updateResourceLock(req, res, next) {
    try {
      const { id } = req.params;
      const data = req.body;
      
      if (!id) {
        throw new ValidationError('Resource lock ID is required');
      }
      
      if (!data) {
        throw new ValidationError('Resource lock data is required');
      }
      
      const lock = await this.resourceLockService.updateResourceLock(id, data, req.user.id);
      res.json(lock);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Delete a resource lock (unlock)
   */
  async deleteResourceLock(req, res, next) {
    try {
      const { id } = req.params;
      const { reason } = req.body || {};
      
      if (!id) {
        throw new ValidationError('Resource lock ID is required');
      }
      
      const result = await this.resourceLockService.deleteResourceLock(id, req.user.id, reason);
      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Check if a user can modify a resource
   */
  async canModifyResource(req, res, next) {
    try {
      const { resourceType, resourceId } = req.params;
      const { environmentId } = req.query;
      
      if (!resourceType) {
        throw new ValidationError('Resource type is required');
      }
      
      if (!resourceId) {
        throw new ValidationError('Resource ID is required');
      }
      
      const result = await this.resourceLockService.canModifyResource(
        resourceType, 
        resourceId, 
        req.user.id, 
        environmentId
      );
      
      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Create a lock override
   */
  async createLockOverride(req, res, next) {
    try {
      const { id } = req.params;
      const { userId, reason } = req.body;
      
      if (!id) {
        throw new ValidationError('Resource lock ID is required');
      }
      
      if (!userId) {
        throw new ValidationError('User ID is required');
      }
      
      if (!reason) {
        throw new ValidationError('Reason is required');
      }
      
      const override = await this.resourceLockService.createLockOverride(
        id, 
        req.user.id, 
        userId, 
        reason
      );
      
      res.status(201).json(override);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Delete a lock override
   */
  async deleteLockOverride(req, res, next) {
    try {
      const { id, overrideId } = req.params;
      
      if (!id) {
        throw new ValidationError('Resource lock ID is required');
      }
      
      if (!overrideId) {
        throw new ValidationError('Override ID is required');
      }
      
      const result = await this.resourceLockService.deleteLockOverride(overrideId, req.user.id);
      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get lock overrides for a lock
   */
  async getLockOverrides(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Resource lock ID is required');
      }
      
      const overrides = await this.resourceLockService.getLockOverrides(id);
      res.json(overrides);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Clean up expired locks
   */
  async cleanupExpiredLocks(req, res, next) {
    try {
      const result = await this.resourceLockService.cleanupExpiredLocks();
      res.json(result);
    } catch (error) {
      next(error);
    }
  }
}

module.exports = new ResourceLockController();
