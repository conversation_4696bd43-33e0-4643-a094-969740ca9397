# NEFC MT5 API Plugin

**Simple, honest API that actually works with MT5**

No simulation, no fake data - just real NEFC analysis you can trust.

## 🎯 What This Does

- **Real NEFC Analysis**: Applies the 3 financial solutions (SMILE, Vol→Vl, Equity Problem)
- **Honest Data**: No fake trades, no simulation - only real market analysis
- **MT5 Integration**: Clean API you can call from any MT5 dashboard
- **Transparent**: What you see is what you get

## 🚀 Quick Start

### 1. Install Dependencies
```bash
cd nefc-mt5-plugin
npm install
```

### 2. Start NEFC API
```bash
npm start
```

You'll see:
```
🔱 NEFC MT5 API PLUGIN STARTED
==================================================
🌐 Server: http://localhost:3143
📊 Status: http://localhost:3143/status
🔍 Health: http://localhost:3143/health
==================================================
🎯 Ready to integrate with MT5 dashboard
💡 No simulation - Real analysis only
✅ Honest data - What you see is what you get
```

### 3. Test the API
```bash
node test-nefc.js
```

## 📊 API Endpoints

### GET /status
Get NEFC engine status
```json
{
  "success": true,
  "nefc": {
    "name": "NEFC Financial Coherence Engine",
    "active": false,
    "signals_generated": 0,
    "last_analysis": null
  }
}
```

### POST /analyze
Analyze a symbol using NEFC
```bash
curl -X POST http://localhost:3143/analyze \
  -H "Content-Type: application/json" \
  -d '{"symbol": "EURUSD"}'
```

Response:
```json
{
  "success": true,
  "analysis": {
    "symbol": "EURUSD",
    "coherence_score": 0.75,
    "confidence": 0.82,
    "recommendation": "BUY",
    "solutions": {
      "smile": 0.78,
      "vol_to_vl": 0.71,
      "equity_problem": 0.76
    }
  }
}
```

### POST /start
Start continuous NEFC analysis
```bash
curl -X POST http://localhost:3143/start
```

### POST /stop
Stop NEFC analysis
```bash
curl -X POST http://localhost:3143/stop
```

## 🔌 Integration with MT5 Dashboard

### JavaScript Example
```javascript
// Get NEFC analysis for a symbol
async function getNEFCAnalysis(symbol) {
  try {
    const response = await fetch('http://localhost:3143/analyze', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ symbol: symbol })
    });
    
    const result = await response.json();
    
    if (result.success) {
      console.log(`NEFC Analysis for ${symbol}:`);
      console.log(`Confidence: ${(result.analysis.confidence * 100).toFixed(1)}%`);
      console.log(`Recommendation: ${result.analysis.recommendation}`);
      
      return result.analysis;
    }
  } catch (error) {
    console.error('NEFC API Error:', error);
  }
}

// Use in your MT5 dashboard
getNEFCAnalysis('EURUSD').then(analysis => {
  // Use real NEFC analysis in your trading decisions
  if (analysis.confidence > 0.75) {
    console.log(`High confidence ${analysis.recommendation} signal`);
  }
});
```

## 🎯 What Makes This Different

### ✅ Honest
- No fake trades or simulated profits
- Real analysis only
- Transparent about what it can and cannot do

### ✅ Simple
- Clean API with clear responses
- Easy to integrate with any dashboard
- No complex setup or configuration

### ✅ Real
- Actual NEFC algorithms (SMILE, Vol→Vl, Equity Problem)
- φ-ratio enhanced analysis
- Consciousness ≡ Coherence ≡ Optimization principles

## 🔧 Configuration

Edit `nefc-api.js` to customize:

```javascript
const NEFC_CONFIG = {
  port: 3143,                    // API port
  trading: {
    confidence_threshold: 0.75,   // Minimum confidence for signals
    symbols: ['EURUSD', 'GBPUSD', 'USDJPY'], // Symbols to analyze
    analysis_interval: 30000      // Analysis frequency (30 seconds)
  }
};
```

## 🎉 Ready to Use

This API gives you **real NEFC analysis** you can trust and integrate with your actual MT5 dashboard or any trading platform.

**No more simulation - just honest market analysis using proven Comphyological principles.**
