import React from 'react';
import { useState, useEffect } from 'react';
import styles from '../styles/Home.module.css';

const RevenueTracker = () => {
  const [revenueData, setRevenueData] = useState({
    total: 0,
    daily: 0,
    weekly: 0,
    monthly: 0,
    growth: 0
  });

  useEffect(() => {
    // Simulate loading revenue data
    const loadRevenueData = async () => {
      // In a real implementation, this would fetch from API
      const mockData = {
        total: 15250.75,
        daily: 450.25,
        weekly: 3215.50,
        monthly: 12500.75,
        growth: 12.5
      };
      setRevenueData(mockData);
    };

    loadRevenueData();
  }, []);

  return (
    <div className={styles.card}>
      <h3>Revenue Tracker</h3>
      <div className={styles.revenueContainer}>
        <div className={styles.totalRevenue}>
          <h4>Total Revenue</h4>
          <p>${revenueData.total.toFixed(2)}</p>
          <span className={styles.growth}>+{revenueData.growth}% Growth</span>
        </div>
        <div className={styles.timePeriods}>
          <div className={styles.period}>
            <h4>Today</h4>
            <p>${revenueData.daily.toFixed(2)}</p>
          </div>
          <div className={styles.period}>
            <h4>This Week</h4>
            <p>${revenueData.weekly.toFixed(2)}</p>
          </div>
          <div className={styles.period}>
            <h4>This Month</h4>
            <p>${revenueData.monthly.toFixed(2)}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RevenueTracker;
