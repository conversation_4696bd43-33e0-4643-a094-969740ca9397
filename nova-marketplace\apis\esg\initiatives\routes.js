/**
 * ESG Initiatives API - Routes
 * 
 * This file defines the routes for the ESG Initiatives API.
 */

const express = require('express');
const router = express.Router();
const { initiativeController, impactAssessmentController } = require('./controllers');
const validate = require('../../../middleware/validate');
const validationSchemas = require('./validation');
const auth = require('../../../middleware/auth');

/**
 * Initiative Routes
 */
// Get all initiatives
router.get('/initiatives', auth, validate(validationSchemas.query.pagination, 'query'), initiativeController.getAllInitiatives);

// Get initiative by ID
router.get('/initiatives/:id', auth, initiativeController.getInitiativeById);

// Create a new initiative
router.post('/initiatives', auth, validate(validationSchemas.initiative.create), initiativeController.createInitiative);

// Update an initiative
router.put('/initiatives/:id', auth, validate(validationSchemas.initiative.update), initiativeController.updateInitiative);

// Delete an initiative
router.delete('/initiatives/:id', auth, initiativeController.deleteInitiative);

// Add a goal to an initiative
router.post('/initiatives/:id/goals', auth, validate(validationSchemas.initiative.addGoal), initiativeController.addGoal);

// Update a goal in an initiative
router.put('/initiatives/:id/goals/:goalId', auth, validate(validationSchemas.initiative.updateGoal), initiativeController.updateGoal);

// Remove a goal from an initiative
router.delete('/initiatives/:id/goals/:goalId', auth, initiativeController.removeGoal);

// Add an update to an initiative
router.post('/initiatives/:id/updates', auth, validate(validationSchemas.initiative.addUpdate), initiativeController.addUpdate);

/**
 * Impact Assessment Routes
 */
// Get all impact assessments
router.get('/impact-assessments', auth, validate(validationSchemas.query.pagination, 'query'), impactAssessmentController.getAllImpactAssessments);

// Get impact assessments by initiative ID
router.get('/initiatives/:initiativeId/impact-assessments', auth, validate(validationSchemas.query.pagination, 'query'), impactAssessmentController.getImpactAssessmentsByInitiativeId);

// Get impact assessment by ID
router.get('/impact-assessments/:id', auth, impactAssessmentController.getImpactAssessmentById);

// Create a new impact assessment
router.post('/impact-assessments', auth, validate(validationSchemas.impactAssessment.create), impactAssessmentController.createImpactAssessment);

// Update an impact assessment
router.put('/impact-assessments/:id', auth, validate(validationSchemas.impactAssessment.update), impactAssessmentController.updateImpactAssessment);

// Delete an impact assessment
router.delete('/impact-assessments/:id', auth, impactAssessmentController.deleteImpactAssessment);

// Review an impact assessment
router.post('/impact-assessments/:id/review', auth, validate(validationSchemas.impactAssessment.review), impactAssessmentController.reviewImpactAssessment);

module.exports = router;
