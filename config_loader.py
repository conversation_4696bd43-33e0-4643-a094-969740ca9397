#!/usr/bin/env python3
"""
UUFT Configuration Loader

A streamlined configuration loader for UUFT/CSDE testing.
Based on <PERSON>'s implementation.
"""

import yaml
import datetime
import logging
import os
import sys

# === Logger Setup ===
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger("UUFT-Loader")

# === GCP Logging Integration ===
def setup_gcp_logging():
    """Set up Google Cloud Logging if running in GCP environment"""
    if os.environ.get("GOOGLE_CLOUD_PROJECT"):
        try:
            import google.cloud.logging
            client = google.cloud.logging.Client()
            client.setup_logging()
            logger.info("GCP logging initialized")
        except ImportError:
            logger.warning("google-cloud-logging not installed, skipping GCP logging setup")
        except Exception as e:
            logger.warning(f"Failed to set up GCP logging: {e}")

# === Config Loader ===
def load_config(path="uuft_config.yaml"):
    """Load UUFT configuration from YAML file"""
    try:
        with open(path, "r") as f:
            config = yaml.safe_load(f)
        logger.info(f"Loaded UUFT config: {config.get('experiment_id')}")
        return config
    except FileNotFoundError:
        logger.error(f"Configuration file not found: {path}")
        sys.exit(1)
    except yaml.YAMLError as e:
        logger.error(f"Error parsing YAML configuration: {e}")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error loading configuration: {e}")
        sys.exit(1)

# === Test Runner ===
def run_uuft_test(config):
    """Run UUFT test with the provided configuration"""
    env = config["environment"]
    resources = config["resource_allocation"]
    pattern = config["pattern_preservation"]
    scaling = config["pi10_scaling"]
    fusion = config["fusion"]
    hardware = config.get("hardware", {})
    metadata = config.get("metadata", {})

    # Log configuration details
    logger.info(f"Running UUFT test in {env.upper()} environment...")
    logger.info(f"Experiment ID: {metadata.get('experiment_id', 'unknown')}")

    # Resource allocation
    logger.debug(f"Resource method: {resources['method']} | Critical weight: {resources['critical_task_weight']}")
    logger.debug(f"Standard task floor: {resources['standard_task_floor']} | Feedback: {resources['feedback_loop_enabled']}")

    # Pattern preservation
    logger.debug(f"Skip connections: {pattern['use_skip_connections']} | Weight: {pattern['skip_connection_weight']}")
    logger.debug(f"Attention: {pattern['attention_enabled']} | Type: {pattern['attention_type']}")
    logger.debug(f"Contrastive loss weight: {pattern['contrastive_loss_weight']}")

    # Pi10 scaling
    logger.debug(f"Pi10 scaling: {scaling['enabled']} | Base: {scaling['base_multiplier']}")
    logger.debug(f"Dynamic scaling: {scaling['dynamic_scaling_enabled']} | Method: {scaling['scaling_factor_adjustment']}")

    # Fusion
    logger.debug(f"Fusion Ops: {fusion['operator_chain']} | Normalization: {fusion['normalization']}")
    logger.debug(f"Feature bifurcation: {fusion.get('feature_bifurcation', False)}")

    # Hardware
    logger.debug(f"GPU: {hardware.get('use_gpu', False)} | Parallel: {hardware.get('parallel_processing', False)}")

    # Notes
    if 'notes' in metadata:
        logger.info(f"Notes: {metadata['notes']}")

    # Execute test based on environment
    if env == "docker":
        return run_docker_test(config)
    elif env == "gcp":
        return run_gcp_test(config)
    else:
        logger.error(f"Unsupported environment: {env}")
        return False

def run_docker_test(config):
    """Run UUFT test in Docker environment"""
    print("\n=== DOCKER EXECUTION ===")
    metadata = config.get("metadata", {})
    experiment_id = metadata.get("experiment_id", "unknown")
    print(f"[{datetime.datetime.now(datetime.timezone.utc).isoformat()}] Executing test pipeline with experiment ID: {experiment_id}")

    # Import and run the appropriate test module
    try:
        # Pass the configuration to the test module
        from run_minimal_test import run_tests
        results = run_tests(config)
        print(f"Test completed with results: {results}")
        return True
    except ImportError:
        logger.error("Could not import test module. Make sure run_minimal_test.py is available.")
        return False
    except Exception as e:
        logger.error(f"Error running Docker test: {e}")
        return False

def run_gcp_test(config):
    """Run UUFT test in GCP environment"""
    print("\n=== GCP EXECUTION ===")
    metadata = config.get("metadata", {})
    experiment_id = metadata.get("experiment_id", "unknown")
    print(f"[{datetime.datetime.now(datetime.timezone.utc).isoformat()}] Executing test pipeline with experiment ID: {experiment_id}")

    # This would be replaced with actual GCP test execution
    # For now, we'll just simulate the test
    print("Connecting to GCP services...")
    print("Running tests on GCP infrastructure...")
    print("Test completed. Results stored in GCP Storage.")

    return True

# === CLI Entrypoint ===
if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="UUFT Test Runner")
    parser.add_argument("--config", default="uuft_config.yaml", help="Path to configuration YAML file")
    parser.add_argument("--gcp", action="store_true", help="Enable GCP logging")
    args = parser.parse_args()

    # Set up GCP logging if requested
    if args.gcp:
        setup_gcp_logging()

    # Load configuration and run test
    config = load_config(args.config)
    success = run_uuft_test(config)

    # Exit with appropriate status code
    sys.exit(0 if success else 1)
