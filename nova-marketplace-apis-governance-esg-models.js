/**
 * @swagger
 * components:
 *   schemas:
 *     ESGReport:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: Unique identifier for the ESG report
 *         title:
 *           type: string
 *           description: Title of the ESG report
 *         description:
 *           type: string
 *           description: Description of the ESG report
 *         framework:
 *           type: string
 *           description: ESG framework used for the report
 *         reportingYear:
 *           type: integer
 *           description: Year the report covers
 *         status:
 *           type: string
 *           enum: [draft, in-progress, completed, published]
 *           description: Status of the ESG report
 *         metrics:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               category:
 *                 type: string
 *                 enum: [environmental, social, governance]
 *               name:
 *                 type: string
 *               value:
 *                 type: number
 *               unit:
 *                 type: string
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the report was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the report was last updated
 *       required:
 *         - id
 *         - title
 *         - framework
 *         - reportingYear
 *         - status
 *         - createdAt
 *         - updatedAt
 *     
 *     ESGReportInput:
 *       type: object
 *       properties:
 *         title:
 *           type: string
 *           description: Title of the ESG report
 *         description:
 *           type: string
 *           description: Description of the ESG report
 *         framework:
 *           type: string
 *           description: ESG framework used for the report
 *         reportingYear:
 *           type: integer
 *           description: Year the report covers
 *         status:
 *           type: string
 *           enum: [draft, in-progress, completed, published]
 *           description: Status of the ESG report
 *         metrics:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               category:
 *                 type: string
 *                 enum: [environmental, social, governance]
 *               name:
 *                 type: string
 *               value:
 *                 type: number
 *               unit:
 *                 type: string
 *       required:
 *         - title
 *         - framework
 *         - reportingYear
 *     
 *     ESGMetric:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: Unique identifier for the metric
 *         name:
 *           type: string
 *           description: Name of the metric
 *         description:
 *           type: string
 *           description: Description of the metric
 *         category:
 *           type: string
 *           enum: [environmental, social, governance]
 *           description: Category of the metric
 *         unit:
 *           type: string
 *           description: Unit of measurement
 *         value:
 *           type: number
 *           description: Value of the metric
 *         year:
 *           type: integer
 *           description: Year the metric applies to
 *         trend:
 *           type: string
 *           enum: [increasing, decreasing, stable]
 *           description: Trend of the metric over time
 *       required:
 *         - id
 *         - name
 *         - category
 *         - unit
 *         - value
 *         - year
 *     
 *     ESGFramework:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: Unique identifier for the framework
 *         name:
 *           type: string
 *           description: Name of the framework
 *         description:
 *           type: string
 *           description: Description of the framework
 *         organization:
 *           type: string
 *           description: Organization that created the framework
 *         website:
 *           type: string
 *           description: Website of the framework
 *         categories:
 *           type: array
 *           items:
 *             type: string
 *           description: Categories covered by the framework
 *       required:
 *         - id
 *         - name
 *         - description
 *         - organization
 */

// Sample ESG reports
const esgReports = [
  {
    id: 'esg-001',
    title: 'Annual Sustainability Report 2024',
    description: 'Comprehensive report on our environmental, social, and governance performance for 2024',
    framework: 'gri',
    reportingYear: 2024,
    status: 'published',
    metrics: [
      {
        category: 'environmental',
        name: 'Carbon Emissions',
        value: 1250,
        unit: 'metric tons CO2e'
      },
      {
        category: 'environmental',
        name: 'Water Usage',
        value: 45000,
        unit: 'cubic meters'
      },
      {
        category: 'social',
        name: 'Employee Diversity',
        value: 42,
        unit: 'percent'
      },
      {
        category: 'governance',
        name: 'Board Independence',
        value: 75,
        unit: 'percent'
      }
    ],
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-03-20T14:30:00Z'
  },
  {
    id: 'esg-002',
    title: 'Q1 2025 ESG Update',
    description: 'Quarterly update on our ESG initiatives and performance',
    framework: 'sasb',
    reportingYear: 2025,
    status: 'completed',
    metrics: [
      {
        category: 'environmental',
        name: 'Renewable Energy Usage',
        value: 35,
        unit: 'percent'
      },
      {
        category: 'social',
        name: 'Employee Training Hours',
        value: 2500,
        unit: 'hours'
      },
      {
        category: 'governance',
        name: 'Ethics Violations',
        value: 0,
        unit: 'count'
      }
    ],
    createdAt: '2025-01-05T09:15:00Z',
    updatedAt: '2025-04-10T11:45:00Z'
  },
  {
    id: 'esg-003',
    title: 'TCFD Climate Risk Assessment',
    description: 'Assessment of climate-related risks and opportunities in line with TCFD recommendations',
    framework: 'tcfd',
    reportingYear: 2024,
    status: 'in-progress',
    metrics: [
      {
        category: 'environmental',
        name: 'Climate Risk Exposure',
        value: 3.2,
        unit: 'risk score'
      },
      {
        category: 'governance',
        name: 'Climate Governance Maturity',
        value: 4,
        unit: 'maturity level'
      }
    ],
    createdAt: '2024-02-20T13:30:00Z',
    updatedAt: '2024-04-15T16:20:00Z'
  }
];

// Sample ESG metrics
const esgMetrics = {
  environmental: [
    {
      id: 'env-001',
      name: 'Carbon Emissions',
      description: 'Total greenhouse gas emissions (Scope 1 and 2)',
      category: 'environmental',
      unit: 'metric tons CO2e',
      value: 1250,
      year: 2024,
      trend: 'decreasing'
    },
    {
      id: 'env-002',
      name: 'Water Usage',
      description: 'Total water consumption',
      category: 'environmental',
      unit: 'cubic meters',
      value: 45000,
      year: 2024,
      trend: 'decreasing'
    },
    {
      id: 'env-003',
      name: 'Renewable Energy Usage',
      description: 'Percentage of energy from renewable sources',
      category: 'environmental',
      unit: 'percent',
      value: 35,
      year: 2024,
      trend: 'increasing'
    },
    {
      id: 'env-004',
      name: 'Waste Generated',
      description: 'Total waste generated',
      category: 'environmental',
      unit: 'metric tons',
      value: 320,
      year: 2024,
      trend: 'decreasing'
    }
  ],
  social: [
    {
      id: 'soc-001',
      name: 'Employee Diversity',
      description: 'Percentage of employees from underrepresented groups',
      category: 'social',
      unit: 'percent',
      value: 42,
      year: 2024,
      trend: 'increasing'
    },
    {
      id: 'soc-002',
      name: 'Employee Training Hours',
      description: 'Total hours of employee training',
      category: 'social',
      unit: 'hours',
      value: 2500,
      year: 2024,
      trend: 'increasing'
    },
    {
      id: 'soc-003',
      name: 'Workplace Injuries',
      description: 'Number of workplace injuries',
      category: 'social',
      unit: 'count',
      value: 3,
      year: 2024,
      trend: 'decreasing'
    },
    {
      id: 'soc-004',
      name: 'Community Investment',
      description: 'Total investment in community programs',
      category: 'social',
      unit: 'USD',
      value: 250000,
      year: 2024,
      trend: 'increasing'
    }
  ],
  governance: [
    {
      id: 'gov-001',
      name: 'Board Independence',
      description: 'Percentage of independent board members',
      category: 'governance',
      unit: 'percent',
      value: 75,
      year: 2024,
      trend: 'stable'
    },
    {
      id: 'gov-002',
      name: 'Ethics Violations',
      description: 'Number of ethics violations',
      category: 'governance',
      unit: 'count',
      value: 0,
      year: 2024,
      trend: 'stable'
    },
    {
      id: 'gov-003',
      name: 'Executive Compensation Ratio',
      description: 'Ratio of CEO compensation to median employee compensation',
      category: 'governance',
      unit: 'ratio',
      value: 45,
      year: 2024,
      trend: 'decreasing'
    },
    {
      id: 'gov-004',
      name: 'Board Gender Diversity',
      description: 'Percentage of women on the board',
      category: 'governance',
      unit: 'percent',
      value: 40,
      year: 2024,
      trend: 'increasing'
    }
  ]
};

// Sample ESG frameworks
const esgFrameworks = [
  {
    id: 'gri',
    name: 'Global Reporting Initiative (GRI)',
    description: 'The GRI Standards are the world\'s most widely used standards for sustainability reporting',
    organization: 'Global Reporting Initiative',
    website: 'https://www.globalreporting.org',
    categories: ['environmental', 'social', 'governance', 'economic']
  },
  {
    id: 'sasb',
    name: 'Sustainability Accounting Standards Board (SASB)',
    description: 'SASB Standards identify the subset of ESG issues most relevant to financial performance in each industry',
    organization: 'Value Reporting Foundation',
    website: 'https://www.sasb.org',
    categories: ['environmental', 'social', 'governance', 'human capital', 'business model']
  },
  {
    id: 'tcfd',
    name: 'Task Force on Climate-related Financial Disclosures (TCFD)',
    description: 'The TCFD develops recommendations for more effective climate-related disclosures',
    organization: 'Financial Stability Board',
    website: 'https://www.fsb-tcfd.org',
    categories: ['governance', 'strategy', 'risk management', 'metrics and targets']
  },
  {
    id: 'cdp',
    name: 'Carbon Disclosure Project (CDP)',
    description: 'CDP runs the global disclosure system for investors, companies, cities, states and regions to manage their environmental impacts',
    organization: 'CDP',
    website: 'https://www.cdp.net',
    categories: ['climate change', 'water security', 'forests']
  },
  {
    id: 'ungc',
    name: 'UN Global Compact',
    description: 'The UN Global Compact is a voluntary initiative based on CEO commitments to implement universal sustainability principles',
    organization: 'United Nations',
    website: 'https://www.unglobalcompact.org',
    categories: ['human rights', 'labor', 'environment', 'anti-corruption']
  }
];

module.exports = {
  esgReports,
  esgMetrics,
  esgFrameworks
};
