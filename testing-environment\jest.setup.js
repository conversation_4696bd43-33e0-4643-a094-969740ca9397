// Set up global test environment
const axios = require('axios');
const MockAdapter = require('axios-mock-adapter');

// Increase timeout for all tests
jest.setTimeout(30000);

// Global setup
beforeAll(async () => {
  console.log('Setting up global test environment...');
  
  // Create global mock for axios
  global.axiosMock = new MockAdapter(axios);
  
  // Set up any other global test requirements
});

// Global teardown
afterAll(async () => {
  console.log('Tearing down global test environment...');
  
  // Clean up global mock
  if (global.axiosMock) {
    global.axiosMock.restore();
  }
  
  // Clean up any other global test resources
});
