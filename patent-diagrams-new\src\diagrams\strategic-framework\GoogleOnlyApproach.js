import React from 'react';
import {
  DiagramFrame,
  ComponentBox,
  ComponentLabel,
  ComponentNumber,
  Arrow,
  ContainerBox,
  ContainerLabel,
  CurvedArrow,
  DiagramLegend,
  LegendItem,
  LegendColor,
  LegendText,
  InventorLabel
} from '../../components/DiagramComponents';

const GoogleOnlyApproach = () => {
  return (
    <DiagramFrame>
      <ContainerBox width="750px" height="600px" left="25px" top="20px">
        <ContainerLabel fontSize="18px">GOOGLE-ONLY APPROACH WITH WIZ ENHANCEMENT</ContainerLabel>
      </ContainerBox>

      {/* Architectural Alignment Section */}
      <ContainerBox width="700px" height="180px" left="50px" top="70px">
        <ContainerLabel fontSize="16px">ARCHITECTURAL ALIGNMENT</ContainerLabel>
      </ContainerBox>

      <ComponentBox left="100px" top="110px" width="150px" height="60px">
        <ComponentNumber>401</ComponentNumber>
        <ComponentLabel fontSize="14px">Google Cloud Platform</ComponentLabel>
        <span style={{ fontSize: '12px' }}>Infrastructure Foundation</span>
      </ComponentBox>

      <Arrow left="250px" top="140px" width="100px" />

      <ComponentBox left="350px" top="110px" width="150px" height="60px">
        <ComponentNumber>402</ComponentNumber>
        <ComponentLabel fontSize="14px" color="#0A84FF">NovaConnect</ComponentLabel>
        <span style={{ fontSize: '12px' }}>Tensor-Based Transformation</span>
      </ComponentBox>

      <Arrow left="500px" top="140px" width="100px" />

      <ComponentBox left="600px" top="110px" width="150px" height="60px">
        <ComponentNumber>403</ComponentNumber>
        <ComponentLabel fontSize="14px">TensorFlow Ecosystem</ComponentLabel>
        <span style={{ fontSize: '12px' }}>AI/ML Integration</span>
      </ComponentBox>

      {/* Wiz Enhancement Section */}
      <ContainerBox width="700px" height="180px" left="50px" top="270px">
        <ContainerLabel fontSize="16px">WIZ ACQUISITION ENHANCEMENT</ContainerLabel>
      </ContainerBox>

      <ComponentBox left="100px" top="310px" width="150px" height="60px">
        <ComponentNumber>404</ComponentNumber>
        <ComponentLabel fontSize="14px">Wiz</ComponentLabel>
        <span style={{ fontSize: '12px' }}>Cloud Security Posture</span>
      </ComponentBox>

      <ComponentBox left="350px" top="310px" width="150px" height="60px">
        <ComponentNumber>405</ComponentNumber>
        <ComponentLabel fontSize="14px" color="#0A84FF">NovaConnect</ComponentLabel>
        <span style={{ fontSize: '12px' }}>Real-Time Compliance</span>
      </ComponentBox>

      <ComponentBox left="600px" top="310px" width="150px" height="60px">
        <ComponentNumber>406</ComponentNumber>
        <ComponentLabel fontSize="14px">Combined Solution</ComponentLabel>
        <span style={{ fontSize: '12px', fontWeight: 'bold', color: '#0A84FF' }}>5-7x Greater ROI</span>
      </ComponentBox>

      {/* Connecting arrows with plus sign */}
      <svg width="800" height="600" style={{ position: 'absolute', top: 0, left: 0, zIndex: 0 }}>
        <line x1="250" y1="340" x2="350" y2="340" stroke="#333" strokeWidth="2" />
        <circle cx="300" cy="340" r="15" fill="white" stroke="#333" strokeWidth="2" />
        <line x1="290" y1="340" x2="310" y2="340" stroke="#333" strokeWidth="2" />
        <line x1="300" y1="330" x2="300" y2="350" stroke="#333" strokeWidth="2" />

        <line x1="500" y1="340" x2="600" y2="340" stroke="#333" strokeWidth="2" />
        <circle cx="550" cy="340" r="15" fill="white" stroke="#333" strokeWidth="2" />
        <line x1="540" y1="340" x2="560" y2="340" stroke="#333" strokeWidth="2" />
        <line x1="550" y1="330" x2="550" y2="350" stroke="#333" strokeWidth="2" />
      </svg>

      {/* Value Proposition Section */}
      <ContainerBox width="700px" height="120px" left="50px" top="470px">
        <ContainerLabel fontSize="16px">EXCLUSIVE VALUE PROPOSITION</ContainerLabel>
      </ContainerBox>

      <ComponentBox left="100px" top="510px" width="150px" height="60px">
        <ComponentNumber>407</ComponentNumber>
        <ComponentLabel fontSize="14px">Wiz Acquisition</ComponentLabel>
        <span style={{ fontSize: '12px' }}>$32B Value</span>
      </ComponentBox>

      <ComponentBox left="350px" top="510px" width="150px" height="60px">
        <ComponentNumber>408</ComponentNumber>
        <ComponentLabel fontSize="14px">Cyber-Safety/NovaFuse</ComponentLabel>
        <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
          <span style={{ fontSize: '14px', fontWeight: 'bold', color: '#0A84FF' }}>$45B Value</span>
          <span style={{ fontSize: '10px' }}>God Patent/Global Dominance</span>
        </div>
      </ComponentBox>

      <ComponentBox left="600px" top="510px" width="150px" height="60px">
        <ComponentNumber>409</ComponentNumber>
        <ComponentLabel fontSize="14px">Market Dominance</ComponentLabel>
        <span style={{ fontSize: '12px', fontWeight: 'bold' }}>15+ Years</span>
      </ComponentBox>

      {/* Connecting arrows with multiplication and equals signs */}
      <svg width="800" height="600" style={{ position: 'absolute', top: 0, left: 0, zIndex: 0 }}>
        <line x1="250" y1="540" x2="350" y2="540" stroke="#333" strokeWidth="2" />
        <circle cx="300" cy="540" r="15" fill="white" stroke="#333" strokeWidth="2" />
        <line x1="290" y1="535" x2="310" y2="545" stroke="#333" strokeWidth="2" />
        <line x1="290" y1="545" x2="310" y2="535" stroke="#333" strokeWidth="2" />

        <line x1="500" y1="540" x2="600" y2="540" stroke="#333" strokeWidth="2" />
        <circle cx="550" cy="540" r="15" fill="white" stroke="#333" strokeWidth="2" />
        <line x1="540" y1="540" x2="560" y2="540" stroke="#333" strokeWidth="2" />
        <line x1="540" y1="535" x2="560" y2="535" stroke="#333" strokeWidth="2" />
      </svg>

      {/* Legend */}
      <DiagramLegend>
        <LegendItem>
          <LegendColor color="#fff" />
          <LegendText>Core Components</LegendText>
        </LegendItem>
        <LegendItem>
          <LegendColor color="#fff" style={{ border: '1px dashed #333' }} />
          <LegendText>Integration Points</LegendText>
        </LegendItem>
      </DiagramLegend>

      <InventorLabel>Inventor: David Nigel Irvin</InventorLabel>
    </DiagramFrame>
  );
};

export default GoogleOnlyApproach;
