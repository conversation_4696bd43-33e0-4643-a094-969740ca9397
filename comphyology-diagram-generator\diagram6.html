<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>6. Meta-Field Schema</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: white;
        }
        .diagram-container {
            width: 900px;
            height: 650px;
            position: relative;
            border: 1px solid #eee;
            margin: 0 auto;
            background-color: white;
        }
        .element {
            position: absolute;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            z-index: 2;
        }
        .element-number {
            position: absolute;
            top: 5px;
            left: 5px;
            background-color: black;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            z-index: 3;
        }
        .connection {
            position: absolute;
            z-index: 1;
        }
        .arrow {
            position: absolute;
            width: 0;
            height: 0;
            border-style: solid;
            z-index: 1;
        }
        h1 {
            text-align: center;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <h1>6. Meta-Field Schema</h1>
    
    <div class="diagram-container">
        <!-- Meta-Field Schema -->
        <div class="element" style="top: 50px; left: 300px; width: 300px; background-color: #f9f0ff; font-weight: bold; font-size: 20px;">
            Meta-Field Schema
            <div class="element-number">1</div>
        </div>
        
        <!-- Four fundamental dimensions -->
        <div class="element" style="top: 150px; left: 100px; width: 200px; background-color: #e6f7ff; font-weight: bold; font-size: 16px;">
            G (Governance Layer)
            <div class="element-number">2</div>
        </div>
        
        <div class="element" style="top: 150px; left: 350px; width: 200px; background-color: #f6ffed; font-weight: bold; font-size: 16px;">
            D (Data Layer)
            <div class="element-number">3</div>
        </div>
        
        <div class="element" style="top: 150px; left: 600px; width: 200px; background-color: #fff2e8; font-weight: bold; font-size: 16px;">
            R (Response/Action Layer)
            <div class="element-number">4</div>
        </div>
        
        <div class="element" style="top: 250px; left: 350px; width: 200px; background-color: #fff0f6; font-weight: bold; font-size: 16px;">
            π (Trust Factor)
            <div class="element-number">5</div>
        </div>
        
        <!-- Descriptions -->
        <div class="element" style="top: 200px; left: 100px; width: 200px; background-color: #e6f7ff; font-size: 12px;">
            Structures, rules, principles, and authorities
            <div class="element-number">6</div>
        </div>
        
        <div class="element" style="top: 200px; left: 350px; width: 200px; background-color: #f6ffed; font-size: 12px;">
            Flow, content, quality, and characteristics of information
            <div class="element-number">7</div>
        </div>
        
        <div class="element" style="top: 200px; left: 600px; width: 200px; background-color: #fff2e8; font-size: 12px;">
            Behaviors, actions, feedback loops, and adaptive mechanisms
            <div class="element-number">8</div>
        </div>
        
        <div class="element" style="top: 300px; left: 350px; width: 200px; background-color: #fff0f6; font-size: 12px;">
            System stability, transparency, integrity, and coherent evolution
            <div class="element-number">9</div>
        </div>
        
        <!-- Equation -->
        <div class="element" style="top: 350px; left: 300px; width: 300px; background-color: #fffbe6; font-weight: bold; font-size: 16px;">
            Meta-Field = ∑(Gₙ⊗Dₙ⊗Rₙ)·πⁿ
            <div class="element-number">10</div>
        </div>
        
        <!-- Implementation components -->
        <div class="element" style="top: 450px; left: 100px; width: 150px; background-color: #f9f0ff; font-size: 12px;">
            Layer Abstraction Engine
            <div class="element-number">11</div>
        </div>
        
        <div class="element" style="top: 450px; left: 300px; width: 150px; background-color: #f9f0ff; font-size: 12px;">
            Cross-Layer Integration Module
            <div class="element-number">12</div>
        </div>
        
        <div class="element" style="top: 450px; left: 500px; width: 150px; background-color: #f9f0ff; font-size: 12px;">
            π-Weighted Aggregator
            <div class="element-number">13</div>
        </div>
        
        <div class="element" style="top: 450px; left: 700px; width: 150px; background-color: #f9f0ff; font-size: 12px;">
            Universal Representation Generator
            <div class="element-number">14</div>
        </div>
        
        <div class="element" style="top: 550px; left: 300px; width: 300px; background-color: #fffbe6; font-weight: bold; font-size: 16px;">
            Technical Implementation: Schema Processing System
            <div class="element-number">15</div>
        </div>
        
        <!-- Connections -->
        <!-- Connect Meta-Field Schema to dimensions -->
        <div class="connection" style="top: 100px; left: 350px; width: 150px; height: 2px; background-color: black; transform: rotate(45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 150px; left: 190px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <div class="connection" style="top: 100px; left: 450px; width: 2px; height: 50px; background-color: black;"></div>
        <div class="arrow" style="top: 140px; left: 445px; border-width: 10px 5px 0 5px; border-color: black transparent transparent transparent;"></div>
        
        <div class="connection" style="top: 100px; left: 550px; width: 150px; height: 2px; background-color: black; transform: rotate(-45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 150px; left: 690px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <!-- Connect dimensions to descriptions -->
        <div class="connection" style="top: 175px; left: 200px; width: 2px; height: 25px; background-color: black;"></div>
        <div class="connection" style="top: 175px; left: 450px; width: 2px; height: 25px; background-color: black;"></div>
        <div class="connection" style="top: 175px; left: 700px; width: 2px; height: 25px; background-color: black;"></div>
        
        <!-- Connect to Trust Factor -->
        <div class="connection" style="top: 225px; left: 200px; width: 150px; height: 2px; background-color: black; transform: rotate(-45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 250px; left: 340px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <div class="connection" style="top: 225px; left: 450px; width: 2px; height: 25px; background-color: black;"></div>
        <div class="arrow" style="top: 240px; left: 445px; border-width: 10px 5px 0 5px; border-color: black transparent transparent transparent;"></div>
        
        <div class="connection" style="top: 225px; left: 700px; width: 150px; height: 2px; background-color: black; transform: rotate(45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 250px; left: 540px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <!-- Connect Trust Factor to description -->
        <div class="connection" style="top: 275px; left: 450px; width: 2px; height: 25px; background-color: black;"></div>
        
        <!-- Connect to equation -->
        <div class="connection" style="top: 225px; left: 200px; width: 100px; height: 2px; background-color: black; transform: rotate(-45deg); transform-origin: left center; border-style: dashed;"></div>
        <div class="arrow" style="top: 350px; left: 290px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <div class="connection" style="top: 325px; left: 450px; width: 2px; height: 25px; background-color: black; border-style: dashed;"></div>
        <div class="arrow" style="top: 340px; left: 445px; border-width: 10px 5px 0 5px; border-color: black transparent transparent transparent;"></div>
        
        <div class="connection" style="top: 225px; left: 700px; width: 100px; height: 2px; background-color: black; transform: rotate(45deg); transform-origin: left center; border-style: dashed;"></div>
        <div class="arrow" style="top: 350px; left: 590px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <!-- Connect equation to implementation components -->
        <div class="connection" style="top: 400px; left: 350px; width: 175px; height: 2px; background-color: black; transform: rotate(45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 450px; left: 165px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <div class="connection" style="top: 400px; left: 425px; width: 50px; height: 2px; background-color: black; transform: rotate(45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 450px; left: 365px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <div class="connection" style="top: 400px; left: 475px; width: 100px; height: 2px; background-color: black; transform: rotate(-45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 450px; left: 565px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <div class="connection" style="top: 400px; left: 550px; width: 225px; height: 2px; background-color: black; transform: rotate(-45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 450px; left: 765px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <!-- Connect implementation components to implementation label -->
        <div class="connection" style="top: 500px; left: 175px; width: 125px; height: 2px; background-color: black; transform: rotate(-45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 550px; left: 290px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <div class="connection" style="top: 500px; left: 375px; width: 2px; height: 50px; background-color: black;"></div>
        <div class="arrow" style="top: 540px; left: 370px; border-width: 10px 5px 0 5px; border-color: black transparent transparent transparent;"></div>
        
        <div class="connection" style="top: 500px; left: 575px; width: 50px; height: 2px; background-color: black; transform: rotate(45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 550px; left: 515px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <div class="connection" style="top: 500px; left: 775px; width: 175px; height: 2px; background-color: black; transform: rotate(45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 550px; left: 590px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
    </div>
</body>
</html>
