<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Diagram Viewer</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .diagram-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            padding: 20px;
            page-break-inside: avoid;
        }
        .diagram-title {
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        .diagram {
            text-align: center;
            margin: 0 auto;
            overflow: auto;
        }
        .mermaid {
            margin: 0 auto;
        }
        .file-info {
            font-size: 0.8em;
            color: #7f8c8d;
            margin-top: 10px;
            font-style: italic;
        }
        .instructions {
            background: #fff3cd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 5px solid #ffc107;
        }
        .tab {
            overflow: hidden;
            border: 1px solid #ccc;
            background-color: #f1f1f1;
            border-radius: 5px 5px 0 0;
        }
        .tab button {
            background-color: inherit;
            float: left;
            border: none;
            outline: none;
            cursor: pointer;
            padding: 10px 16px;
            transition: 0.3s;
        }
        .tab button:hover {
            background-color: #ddd;
        }
        .tab button.active {
            background-color: #4CAF50;
            color: white;
        }
        .tabcontent {
            display: none;
            padding: 20px;
            border: 1px solid #ccc;
            border-top: none;
            border-radius: 0 0 5px 5px;
            background: white;
        }
        .tabcontent.active {
            display: block;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Comphyology Patent Diagrams</h1>
        
        <div class="instructions">
            <h3>How to Use This Viewer</h3>
            <ol>
                <li>Click on the tabs below to view different types of diagrams</li>
                <li>Use your browser's zoom (Ctrl + or Ctrl -) to adjust size</li>
                <li>Take screenshots using your operating system's screenshot tool</li>
                <li>For HTML files, you may need to open them directly in your browser</li>
            </ol>
        </div>

        <div class="tab">
            <button class="tablinks active" onclick="openTab(event, 'mermaid')">Mermaid Diagrams</button>
            <button class="tablinks" onclick="openTab(event, 'html')">HTML Diagrams</button>
            <button class="tablinks" onclick="openTab(event, 'svg')">SVG Files</button>
        </div>

        <div id="mermaid" class="tabcontent active">
            <h2>Mermaid Diagrams</h2>
            <div id="mermaid-diagrams">
                <!-- Mermaid diagrams will be loaded here -->
                <p>Loading Mermaid diagrams...</p>
            </div>
        </div>

        <div id="html" class="tabcontent">
            <h2>HTML Diagrams</h2>
            <div id="html-diagrams">
                <!-- HTML diagrams will be listed here -->
                <p>Loading HTML diagrams...</p>
            </div>
        </div>

        <div id="svg" class="tabcontent">
            <h2>SVG Files</h2>
            <div id="svg-files">
                <!-- SVG files will be listed here -->
                <p>Loading SVG files...</p>
            </div>
        </div>
    </div>

    <script>
        // Initialize Mermaid
        mermaid.initialize({
            startOnLoad: false,
            theme: 'default',
            securityLevel: 'loose',
            fontFamily: 'Arial, sans-serif'
        });

        // Tab functionality
        function openTab(evt, tabName) {
            const tabcontent = document.getElementsByClassName("tabcontent");
            for (let i = 0; i < tabcontent.length; i++) {
                tabcontent[i].classList.remove("active");
            }

            const tablinks = document.getElementsByClassName("tablinks");
            for (let i = 0; i < tablinks.length; i++) {
                tablinks[i].className = tablinks[i].className.replace(" active", "");
            }

            document.getElementById(tabName).classList.add("active");
            evt.currentTarget.className += " active";
        }

        // Load Mermaid diagrams
        async function loadMermaidDiagrams() {
            const container = document.getElementById('mermaid-diagrams');
            container.innerHTML = '<p>Loading Mermaid diagrams...</p>';
            
            try {
                // This would need to be replaced with actual file loading logic
                // For now, we'll just show instructions
                container.innerHTML = `
                    <div class="instructions">
                        <h3>How to view Mermaid diagrams:</h3>
                        <ol>
                            <li>Open any .mmd file in a text editor</li>
                            <li>Copy the Mermaid code</li>
                            <li>Go to <a href="https://mermaid.live" target="_blank">Mermaid Live Editor</a></li>
                            <li>Paste the code and view the diagram</li>
                            <li>Take a screenshot</li>
                        </ol>
                        <p>Alternatively, you can open the .html files in the browser to see the rendered diagrams.</p>
                    </div>
                `;
            } catch (error) {
                container.innerHTML = `<p>Error loading Mermaid diagrams: ${error.message}</p>`;
            }
        }

        // Load HTML diagrams
        function loadHtmlDiagrams() {
            const container = document.getElementById('html-diagrams');
            container.innerHTML = `
                <div class="instructions">
                    <h3>Available HTML Diagrams:</h3>
                    <p>Click on any link below to open the diagram in a new tab for screenshotting.</p>
                    <ul id="html-files">
                        <!-- HTML files will be listed here -->
                    </ul>
                </div>
            `;

            // This would need to be replaced with actual file listing logic
            const htmlFiles = [
                'strategic-framework-viewer.html',
                // Add other HTML files here
            ];

            const fileList = document.getElementById('html-files');
            htmlFiles.forEach(file => {
                const li = document.createElement('li');
                const link = document.createElement('a');
                link.href = file;
                link.target = '_blank';
                link.textContent = file;
                li.appendChild(link);
                fileList.appendChild(li);
            });
        }

        // Load SVG files
        function loadSvgFiles() {
            const container = document.getElementById('svg-files');
            container.innerHTML = `
                <div class="instructions">
                    <h3>Available SVG Files:</h3>
                    <p>Click on any link below to open the SVG in a new tab for screenshotting.</p>
                    <ul id="svg-file-list">
                        <!-- SVG files will be listed here -->
                    </ul>
                </div>
            `;

            // This would need to be replaced with actual file listing logic
            const svgFiles = [
                // Add SVG files here
            ];

            const fileList = document.getElementById('svg-file-list');
            if (svgFiles.length === 0) {
                fileList.innerHTML = '<li>No SVG files found in the directory.</li>';
            } else {
                svgFiles.forEach(file => {
                    const li = document.createElement('li');
                    const link = document.createElement('a');
                    link.href = file;
                    link.target = '_blank';
                    link.textContent = file;
                    li.appendChild(link);
                    fileList.appendChild(li);
                });
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            loadMermaidDiagrams();
            loadHtmlDiagrams();
            loadSvgFiles();
        });
    </script>
</body>
</html>
