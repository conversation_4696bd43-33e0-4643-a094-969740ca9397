/**
 * Resource Lock Routes
 */

const express = require('express');
const router = express.Router();
const ResourceLockController = require('../controllers/ResourceLockController');
const { authenticate, hasPermission } = require('../middleware/authMiddleware');

// All routes require authentication
router.use(authenticate);

// Get all resource locks
router.get('/', (req, res, next) => {
  ResourceLockController.getAllResourceLocks(req, res, next);
});

// Get resource locks for a team
router.get('/team/:id', (req, res, next) => {
  ResourceLockController.getResourceLocksForTeam(req, res, next);
});

// Check if a resource is locked
router.get('/check/:resourceType/:resourceId', (req, res, next) => {
  ResourceLockController.isResourceLocked(req, res, next);
});

// Check if a user can modify a resource
router.get('/can-modify/:resourceType/:resourceId', (req, res, next) => {
  ResourceLockController.canModifyResource(req, res, next);
});

// Clean up expired locks (admin only)
router.post('/cleanup', hasPermission('system:settings'), (req, res, next) => {
  ResourceLockController.cleanupExpiredLocks(req, res, next);
});

// Create a new resource lock
router.post('/', (req, res, next) => {
  ResourceLockController.createResourceLock(req, res, next);
});

// Get resource lock by ID
router.get('/:id', (req, res, next) => {
  ResourceLockController.getResourceLockById(req, res, next);
});

// Update a resource lock
router.put('/:id', (req, res, next) => {
  ResourceLockController.updateResourceLock(req, res, next);
});

// Delete a resource lock (unlock)
router.delete('/:id', (req, res, next) => {
  ResourceLockController.deleteResourceLock(req, res, next);
});

// Get lock overrides for a lock
router.get('/:id/overrides', (req, res, next) => {
  ResourceLockController.getLockOverrides(req, res, next);
});

// Create a lock override
router.post('/:id/overrides', (req, res, next) => {
  ResourceLockController.createLockOverride(req, res, next);
});

// Delete a lock override
router.delete('/:id/overrides/:overrideId', (req, res, next) => {
  ResourceLockController.deleteLockOverride(req, res, next);
});

module.exports = router;
