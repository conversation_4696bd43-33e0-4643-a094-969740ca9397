# ConsciousNovaFold

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Python 3.8+](https://img.shields.io/badge/python-3.8+-blue.svg)](https://www.python.org/downloads/)
[![Tests](https://github.com/your-org/consciousness-chemistry-engine/actions/workflows/tests.yml/badge.svg)](https://github.com/your-org/consciousness-chemistry-engine/actions)

ConsciousNovaFold is an advanced protein folding system that integrates ethical and structural validation with state-of-the-art protein structure prediction. Includes specialized validation for disease-related proteins like CFTR (Cystic Fibrosis).

## 📚 Documentation

- [User Guide](docs/guides/user_guide.md) - Getting started, installation, and usage
- [Developer Guide](docs/guides/developer_guide.md) - Contributing and development setup
- [API Reference](docs/api/reference.md) - Complete API documentation
- [Scientific Foundations](docs/scientific/foundations.md) - Theoretical background and methodology

## 📦 Installation

```bash
# Clone the repository
git clone https://github.com/your-org/consciousness-chemistry-engine.git
cd consciousness-chemistry-engine

# Create and activate a virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Run the main script
python -m src.conscious_novafold --sequence ACEDEF...

# Run with CFTR validation
python -m src.conscious_novafold --sequence CFTR_SEQUENCE --enable-cftr
```

## 🚀 Quick Start

```python
from ConsciousNovaFold import ConsciousNovaFold, NovaFoldClient

# Initialize the client and folder
client = NovaFoldClient()
folder = ConsciousNovaFold(client)

# Fold a protein sequence with consciousness enhancement
sequence = "ACDEFGHIKLMNPQRSTVWY"
result = folder.fold(sequence)

# View results
print(f"Structure: {result['structure']}")
print(f"Metrics: {result['metrics']}")

# Generate reports
reports = folder.generate_reports(result, "reports")
print(f"Reports saved to: {reports}")
```

## ✨ Key Features

- **Ethical Validation**: Ensures protein structures meet ethical guidelines
- **Disease-Specific Validation**: Specialized validation for disease-related proteins (e.g., CFTR for Cystic Fibrosis)
- **Mutation Analysis**: Detects common pathogenic mutations
- **Structural Analysis**: Advanced analysis of protein folds and stability
- **Domain Integrity Checks**: Validates structural integrity of functional domains
- **Edge Case Handling**: Specialized validation for challenging sequences
- **Performance Optimized**: Efficient processing of protein structures
- **Modular Design**: Easily extensible architecture
- **Comprehensive Documentation**: Detailed guides for users and developers
- **Scientific Validation**: Rigorous testing and validation of consciousness metrics

## Documentation

For detailed documentation, please see the [docs](docs/) directory.

### CFTR Validation

ConsciousNovaFold includes specialized validation for the CFTR protein, which is associated with Cystic Fibrosis. The validation includes:

- Detection of common CF-causing mutations (e.g., F508del, G551D)
- Domain integrity analysis
- Sequence similarity to wild-type CFTR
- Therapeutic impact assessment

Example usage:

```python
from src.metrics.trinity_validator import TrinityValidator

# Initialize with CFTR validation enabled
validator = TrinityValidator(enable_cftr_validation=True)

# Validate a CFTR sequence
result = validator.validate({
    'sequence': "MQRSPLEKASVVSKLFFSWTRPILRKGYRQRLELSDIYQIPSVDSADNLSEKLEREWDRE...",
    'structure': None  # Optional: Add structure if available
})

# Check results
if result.get('cftr_validation', {}).get('is_cftr', False):
    print("CFTR protein detected")
    if result['cftr_validation']['validation_passed']:
        print("No pathogenic mutations detected")
    else:
        print("Pathogenic mutations found:")
        for mut_id, status in result['cftr_validation']['mutation_status'].items():
            if status.get('present', False):
                print(f"- {mut_id}: {status.get('impact', 'No impact info')}")
```

## Contributing

Contributions are welcome! Please read our [contributing guidelines](CONTRIBUTING.md) for details.

### Testing

Run the test suite including CFTR validation tests:

```bash
# Run all tests
pytest tests/

# Run only CFTR validation tests
pytest tests/test_cftr_validator.py tests/test_cftr_integration.py -v
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 📧 Contact

For questions or support, please open an issue on our [GitHub repository](https://github.com/your-org/consciousness-chemistry-engine).

## 📖 Citation

If you use ConsciousNovaFold in your research, please cite:

```
@software{conscious_novafold_2025,
  author = {Your Name},
  title = {ConsciousNovaFold: Consciousness-Enhanced Protein Structure Prediction},
  year = {2025},
  publisher = {GitHub},
  journal = {GitHub repository},
  howpublished = {\url{https://github.com/your-org/consciousness-chemistry-engine}}
}
```

## 🙏 Acknowledgments

- NovaFold team for the base protein folding algorithms
- Quantum biology research community
- Open source contributors

## Quick Start

```python
from ConsciousNovaFold import ConsciousNovaFold, NovaFoldClient

# Initialize with a NovaFold client
novafold = NovaFoldClient()
conscious_folder = ConsciousNovaFold(novafold)

# Fold a protein sequence with consciousness enhancement
sequence = "ACDEFGHIKLMNPQRSTVWY"
result = conscious_folder.fold(sequence)

print(f"Consciousness metrics: {result['consciousness_metrics']}")
```

## Consciousness Metrics

### Ψ-Scoring
Each amino acid is assigned a consciousness score based on evolutionary conservation:

| Amino Acid | Ψ-Score | Conservation Level |
|------------|---------|-------------------|
| W (Trp)    | 0.92    | Highest           |
| C (Cys)    | 0.90    | Very High         |
| R (Arg)    | 0.88    | High              |
| ...        | ...     | ...               |
| F (Phe)    | 0.22    | Low               |


### Fibonacci Alignment
Protein domains are evaluated based on their alignment with Fibonacci sequence lengths, which are commonly observed in stable protein structures.

## Trinity Validation

### 1. NERS (Structural Consciousness)
- Validates geometric harmony and structural integrity
- Checks secondary structure elements and their arrangement
- Ensures proper backbone geometry

### 2. NEPI (Functional Truth)
- Validates preservation of functional sites
- Checks for proper active site geometry
- Ensures catalytic residues are correctly positioned

### 3. NEFC (Purpose Alignment)
- Validates alignment with biological function
- Checks for presence of binding sites
- Ensures post-translational modification sites are accessible

## Integration with NovaFold

ConsciousNovaFold wraps around NovaFold's prediction pipeline, adding consciousness-based enhancements:

1. Pre-folding: Analyzes sequence for consciousness patterns
2. Folding: Runs standard NovaFold prediction
3. Post-folding: Applies consciousness-based refinements
4. Validation: Runs Trinity validation on the final structure

## Example Output

```json
{
  "sequence": "ACDEFGHIKLMNPQRSTVWY",
  "secondary_structure": "HHHHHHHHHHHHHHHHHHHH",
  "consciousness_metrics": {
    "average_psi": 0.42,
    "fibonacci_alignment": {
      "closest_fibonacci": 21,
      "difference": 1,
      "alignment_score": 0.5
    },
    "trinity_validation": {
      "NERS": {"score": 0.85, "passed": true},
      "NEPI": {"score": 0.75, "passed": true},
      "NEFC": {"score": 0.80, "passed": true},
      "passed": true
    }
  }
}
```

## References

1. CFTR Mutation Database - [CFTR1](https://www.cftr1.org/)
2. Cystic Fibrosis Foundation - [Research](https://www.cff.org/Research/)
3. Protein Data Bank - [CFTR Structures](https://www.rcsb.org/search?request=%7B%22query%22%3A%7B%22parameters%22%3A%7B%22value%22%3A%22CFTR%22%7D%2C%22type%22%3A%22terminal%22%2C%22service%22%3A%22full_text%22%7D%7D)

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
