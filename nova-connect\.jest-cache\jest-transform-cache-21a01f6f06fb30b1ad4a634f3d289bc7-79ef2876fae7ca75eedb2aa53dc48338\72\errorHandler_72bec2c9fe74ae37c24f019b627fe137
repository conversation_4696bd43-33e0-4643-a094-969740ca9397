87683fd549cc16c5b83945bad9c21345
/**
 * Error Handler Middleware
 * 
 * This middleware handles errors and sends appropriate responses.
 */

const {
  ValidationError,
  AuthenticationError,
  AuthorizationError,
  NotFoundError,
  ConflictError,
  RateLimitError,
  ServerError
} = require('../utils/errors');
const errorHandler = (err, req, res, next) => {
  console.error('Error:', err);

  // Handle specific error types
  if (err instanceof ValidationError) {
    return res.status(400).json({
      error: 'Bad Request',
      message: err.message
    });
  }
  if (err instanceof AuthenticationError) {
    return res.status(401).json({
      error: 'Unauthorized',
      message: err.message
    });
  }
  if (err instanceof AuthorizationError) {
    return res.status(403).json({
      error: 'Forbidden',
      message: err.message
    });
  }
  if (err instanceof NotFoundError) {
    return res.status(404).json({
      error: 'Not Found',
      message: err.message
    });
  }
  if (err instanceof ConflictError) {
    return res.status(409).json({
      error: 'Conflict',
      message: err.message
    });
  }
  if (err instanceof RateLimitError) {
    return res.status(429).json({
      error: 'Too Many Requests',
      message: err.message,
      retryAfter: err.retryAfter
    });
  }
  if (err instanceof ServerError) {
    return res.status(500).json({
      error: 'Internal Server Error',
      message: err.message
    });
  }

  // Handle other errors
  return res.status(500).json({
    error: 'Internal Server Error',
    message: 'An unexpected error occurred'
  });
};
module.exports = errorHandler;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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