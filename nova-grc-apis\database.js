/**
 * Database connection module
 * 
 * This module provides functions for connecting to and disconnecting from the database.
 */

const mongoose = require('mongoose');
const { logger } = require('./utils/logger');

/**
 * Connect to the MongoDB database
 * @returns {Promise} A promise that resolves when the connection is established
 */
const connectDatabase = async () => {
  try {
    const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/nova-grc-apis';
    await mongoose.connect(mongoURI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    logger.info('MongoDB connected successfully');
    return true;
  } catch (error) {
    logger.error(`MongoDB connection error: ${error.message}`);
    process.exit(1);
  }
};

/**
 * Disconnect from the MongoDB database
 * @returns {Promise} A promise that resolves when the connection is closed
 */
const disconnectDatabase = async () => {
  try {
    await mongoose.disconnect();
    logger.info('MongoDB disconnected successfully');
    return true;
  } catch (error) {
    logger.error(`MongoDB disconnection error: ${error.message}`);
    return false;
  }
};

module.exports = {
  connectDatabase,
  disconnectDatabase,
};
