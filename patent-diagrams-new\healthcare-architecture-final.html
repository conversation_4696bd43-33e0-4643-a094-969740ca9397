<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Healthcare System Architecture</title>
    <style>
        /* Reset and Base Styles */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            line-height: 1.4;
        }
        
        /* Main Diagram Container */
        .diagram-container {
            position: relative;
            width: 950px;
            min-height: 900px; /* Increased minimum height */
            margin: 0 auto 40px;
            background-color: white;
            border: 1px solid #ddd;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            box-sizing: border-box;
            overflow: visible; /* Allow content to be fully visible */
        }
        
        /* Container Box Styles */
        .container-box {
            position: absolute;
            border: 2px solid #666;
            border-radius: 10px;
            padding: 30px 10px 10px;
            overflow: visible; /* Allow content to be fully visible */
            min-height: 100px; /* Minimum height */
            min-width: 200px; /* Minimum width */
            z-index: 5; /* Bring containers in front of connecting lines */
            background-color: white; /* Ensure background is opaque */
        }
        
        .container-label {
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            font-weight: bold;
            font-size: 16px;
            text-align: center;
            width: 90%; /* Ensures text doesn't overflow container */
            white-space: normal; /* Allows text to wrap */
            overflow: visible; /* Allow content to be fully visible */
            z-index: 10; /* Ensure labels are in front of connecting lines */
            background-color: white; /* Add background to hide lines behind text */
            padding: 0 5px; /* Add padding around text */
        }
        
        /* Component Box Styles */
        .component-box {
            position: absolute;
            background-color: white;
            border: 2px solid #333;
            border-radius: 6px;
            display: flex;
            flex-direction: column;
            justify-content: center; /* Center content vertically */
            align-items: center;
            text-align: center;
            padding: 5px; /* Reduced padding */
            overflow: visible; /* Allow content to be fully visible */
            border-top-left-radius: 0; /* Flat corner for number */
            min-height: 50px; /* Minimum height */
            min-width: 100px; /* Minimum width */
        }
        
        /* Component Number Styles - Integrated into corner */
        .component-number-inside {
            position: absolute;
            top: 0;
            left: 0;
            width: 18px;
            height: 18px;
            background-color: #000000; /* Black background for component numbers */
            color: white;
            border-radius: 0 0 6px 0; /* Rounded on bottom-right corner only */
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 10px; /* Smaller font */
            font-weight: bold;
            z-index: 2;
            margin: 0; /* Remove any margin */
            padding: 0; /* Remove any padding */
        }
        
        .component-label {
            font-weight: bold;
            margin-top: 10px; /* Reduced margin */
            margin-bottom: 2px; /* Reduced margin */
            font-size: 12px; /* Smaller font */
            text-align: center;
            width: 100%;
            white-space: normal; /* Allows text to wrap */
            overflow: visible; /* Allow content to be fully visible */
        }
        
        .component-content {
            font-size: 10px; /* Smaller font */
            text-align: center;
            width: 100%;
            padding: 0;
            white-space: normal; /* Allows text to wrap */
            overflow: visible; /* Allow content to be fully visible */
            line-height: 1.2; /* Tighter line spacing */
        }
        
        /* Arrow Styles */
        .arrow {
            position: absolute;
            background-color: #555555; /* Grey color for patent compliance */
            width: 2px;
            z-index: 1; /* Ensure arrows are behind containers but visible */
        }
        
        /* Legend Styles */
        .legend {
            position: absolute;
            right: 10px;
            bottom: 30px;
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 8px;
            z-index: 10;
            width: 200px;
            font-size: 12px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 6px;
        }
        
        .legend-color {
            width: 15px;
            height: 15px;
            margin-right: 8px;
            border: 1px solid #333;
            flex-shrink: 0; /* Prevents the color box from shrinking */
        }
        
        /* Inventor Label */
        .inventor-label {
            position: absolute;
            left: 10px;
            bottom: 30px;
            font-size: 12px;
            font-style: italic;
            color: #333;
            z-index: 10;
        }
        
        /* Title Styles */
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 20px;
            color: #333;
        }
    </style>
</head>
<body>

    <h1>FIG. 11: Healthcare System Architecture</h1>
    
    <div class="diagram-container">
        <!-- Main Container -->
        <div class="container-box" style="width: 750px; height: 600px; left: 25px; top: 20px;">
            <div class="container-label" style="font-size: 18px;">HEALTHCARE SYSTEM ARCHITECTURE</div>
        </div>

        <!-- Core Components -->
        <div class="container-box" style="width: 300px; height: 150px; left: 250px; top: 70px;">
            <div class="container-label">CYBER-SAFETY PROTOCOL CORE</div>
        </div>
        
        <div class="component-box" style="left: 270px; top: 120px; width: 120px; height: 80px;">
            <div class="component-number-inside">301</div>
            <div class="component-label">Native Unification</div>
            <div style="font-size: 12px; text-align: center;">
                Engine
            </div>
        </div>
        
        <div class="component-box" style="left: 410px; top: 120px; width: 120px; height: 80px;">
            <div class="component-number-inside">302</div>
            <div class="component-label">Dynamic UI</div>
            <div style="font-size: 12px; text-align: center;">
                Enforcement
            </div>
        </div>

        <!-- Healthcare Extensions -->
        <div class="container-box" style="width: 200px; height: 120px; left: 100px; top: 250px;">
            <div class="container-label">PHI PROTECTION</div>
        </div>
        
        <div class="component-box" style="left: 135px; top: 290px; width: 130px; height: 60px;">
            <div class="component-number-inside">303</div>
            <div class="component-label">Zero-Persistence</div>
            <div style="font-size: 12px; text-align: center;">
                PHI Processing
            </div>
        </div>
        
        <div class="container-box" style="width: 200px; height: 120px; left: 500px; top: 250px;">
            <div class="container-label">HIPAA ENFORCEMENT</div>
        </div>
        
        <div class="component-box" style="left: 535px; top: 290px; width: 130px; height: 60px;">
            <div class="component-number-inside">304</div>
            <div class="component-label">Dynamic HIPAA</div>
            <div style="font-size: 12px; text-align: center;">
                Enforcement Mechanism
            </div>
        </div>
        
        <!-- Device Security -->
        <div class="container-box" style="width: 200px; height: 120px; left: 300px; top: 250px;">
            <div class="container-label">DEVICE SECURITY</div>
        </div>
        
        <div class="component-box" style="left: 335px; top: 290px; width: 130px; height: 60px;">
            <div class="component-number-inside">305</div>
            <div class="component-label">Medical Device</div>
            <div style="font-size: 12px; text-align: center;">
                Security Framework
            </div>
        </div>

        <!-- Novel Elements -->
        <div class="container-box" style="width: 650px; height: 150px; left: 75px; top: 400px;">
            <div class="container-label">HEALTHCARE SPECIFIC IMPLEMENTATIONS</div>
        </div>
        
        <div class="component-box" style="left: 100px; top: 450px; width: 130px; height: 60px;">
            <div class="component-number-inside">306</div>
            <div class="component-label">Blockchain-Anchored</div>
            <div style="font-size: 12px; text-align: center;">
                PHI Disclosure
            </div>
        </div>
        
        <div class="component-box" style="left: 250px; top: 450px; width: 130px; height: 60px;">
            <div class="component-number-inside">307</div>
            <div class="component-label">Break-Glass Access</div>
            <div style="font-size: 12px; text-align: center;">
                Protocol
            </div>
        </div>
        
        <div class="component-box" style="left: 400px; top: 450px; width: 130px; height: 60px;">
            <div class="component-number-inside">308</div>
            <div class="component-label">Telehealth</div>
            <div style="font-size: 12px; text-align: center;">
                Compliance Bridge
            </div>
        </div>
        
        <div class="component-box" style="left: 550px; top: 450px; width: 130px; height: 60px;">
            <div class="component-number-inside">309</div>
            <div class="component-label">AI Diagnostic</div>
            <div style="font-size: 12px; text-align: center;">
                Compliance
            </div>
        </div>

        <!-- Arrows connecting components -->
        <div class="arrow-line" style="left: 200px; top: 290px; width: 135px; height: 2px;"></div>
        <div class="arrow-head" style="left: 335px; top: 286px; border-width: 4px 0 4px 8px; border-color: transparent transparent transparent #333;"></div>

        <div class="arrow-line" style="left: 465px; top: 290px; width: 70px; height: 2px;"></div>
        <div class="arrow-head" style="left: 535px; top: 286px; border-width: 4px 0 4px 8px; border-color: transparent transparent transparent #333;"></div>

        <!-- Curved arrows -->
        <svg class="curved-arrow" style="position: absolute; left: 0; top: 0; width: 800px; height: 650px;">
            <path d="M 335,170 Q 335,250 185,290" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)"></path>
            <path d="M 465,170 Q 465,250 615,290" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)"></path>
            <defs>
                <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="0" refY="3.5" orient="auto">
                    <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
                </marker>
            </defs>
        </svg>

        <!-- UUFT Implementation -->
        <div class="component-box" style="left: 325px; top: 530px; width: 150px; height: 50px;">
            <div class="component-number-inside">310</div>
            <div class="component-label">UUFT Implementation</div>
            <div style="font-size: 12px; text-align: center;">
                (A⊗B⊕C)×π10³
            </div>
        </div>

        <!-- Legend -->
        <div class="legend">
            <div class="legend-item">
                <div class="legend-color" style="background-color: #fff;"></div>
                <div>Core Components</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #fff;"></div>
                <div>Healthcare Extensions</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #fff;"></div>
                <div>Novel Elements</div>
            </div>
        </div>

        <div class="inventor-label">Inventor: David Nigel Irvin</div>
    </div>

</body>
</html>
