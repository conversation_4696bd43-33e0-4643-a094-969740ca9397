/**
 * Controllers Index
 * 
 * This file exports all controllers for the Privacy Management API.
 */

const dataProcessingController = require('./dataProcessingController');
const subjectRequestController = require('./subjectRequestController');
const consentController = require('./consentController');
const privacyNoticeController = require('./privacyNoticeController');
const dataBreachController = require('./dataBreachController');
const integrationController = require('./integrationController');
const notificationController = require('./notificationController');
const reportController = require('./reportController');

module.exports = {
  dataProcessingController,
  subjectRequestController,
  consentController,
  privacyNoticeController,
  dataBreachController,
  integrationController,
  notificationController,
  reportController
};
