/**
 * Large File Configuration
 * 
 * This file contains configuration settings for handling large files.
 */

const path = require('path');

module.exports = {
  // Maximum file size (default: 1GB)
  maxFileSize: process.env.LARGE_FILE_MAX_SIZE || 1024 * 1024 * 1024,
  
  // Temporary directory for file uploads
  tempDir: process.env.LARGE_FILE_TEMP_DIR || path.join(process.cwd(), 'uploads', 'temp'),
  
  // Whether to compress files during processing
  compressFiles: process.env.LARGE_FILE_COMPRESS === 'true',
  
  // Rate limit for uploads in bytes per second (optional)
  // Set to null to disable throttling
  throttleRate: process.env.LARGE_FILE_THROTTLE_RATE ? 
    parseInt(process.env.LARGE_FILE_THROTTLE_RATE, 10) : 
    null,
  
  // Allowed MIME types (optional)
  // Set to null to allow all types
  allowedMimeTypes: process.env.LARGE_FILE_ALLOWED_TYPES ? 
    process.env.LARGE_FILE_ALLOWED_TYPES.split(',') : 
    null,
  
  // Allowed file extensions (optional)
  // Set to null to allow all extensions
  allowedExtensions: process.env.LARGE_FILE_ALLOWED_EXTENSIONS ? 
    process.env.LARGE_FILE_ALLOWED_EXTENSIONS.split(',').map(ext => ext.startsWith('.') ? ext : `.${ext}`) : 
    null,
    
  // Chunk size for processing large files in chunks (in bytes)
  chunkSize: process.env.LARGE_FILE_CHUNK_SIZE || 1024 * 1024, // 1MB default
  
  // Maximum concurrent uploads
  maxConcurrentUploads: process.env.LARGE_FILE_MAX_CONCURRENT || 5,
  
  // File cleanup settings
  cleanup: {
    // Whether to automatically clean up temporary files after processing
    enabled: process.env.LARGE_FILE_AUTO_CLEANUP !== 'false',
    
    // Maximum age of temporary files before cleanup (in milliseconds)
    maxAge: process.env.LARGE_FILE_CLEANUP_MAX_AGE || 24 * 60 * 60 * 1000, // 24 hours
  }
};
