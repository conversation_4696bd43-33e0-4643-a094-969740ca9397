const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const bodyParser = require('body-parser');
const CryptoJS = require('crypto-js');
const winston = require('winston');

const app = express();
const port = process.env.PORT || 3000;

// Configure logger
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' })
  ]
});

// Middleware
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/auth-service', {
  useNewUrlParser: true,
  useUnifiedTopology: true
})
.then(() => {
  logger.info('Connected to MongoDB');
})
.catch((err) => {
  logger.error('Error connecting to MongoDB', { error: err.message });
});

// Define credential schema
const credentialSchema = new mongoose.Schema({
  name: { type: String, required: true },
  connectorId: { type: String, required: true },
  authType: { type: String, required: true },
  credentials: { type: String, required: true }, // Encrypted credentials
  userId: { type: String, required: true },
  created: { type: Date, default: Date.now },
  updated: { type: Date, default: Date.now }
});

const Credential = mongoose.model('Credential', credentialSchema);

// Encryption/decryption functions
const encryptCredentials = (credentials) => {
  const encryptionKey = process.env.ENCRYPTION_KEY || 'test-encryption-key';
  return CryptoJS.AES.encrypt(JSON.stringify(credentials), encryptionKey).toString();
};

const decryptCredentials = (encryptedCredentials) => {
  const encryptionKey = process.env.ENCRYPTION_KEY || 'test-encryption-key';
  const bytes = CryptoJS.AES.decrypt(encryptedCredentials, encryptionKey);
  return JSON.parse(bytes.toString(CryptoJS.enc.Utf8));
};

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok' });
});

// API endpoints
app.get('/credentials', async (req, res) => {
  try {
    const userId = req.query.userId;
    if (!userId) {
      return res.status(400).json({ error: 'userId is required' });
    }
    
    const credentials = await Credential.find({ userId }, '-credentials');
    res.json(credentials);
  } catch (err) {
    logger.error('Error fetching credentials', { error: err.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.get('/credentials/:id', async (req, res) => {
  try {
    const credential = await Credential.findById(req.params.id);
    if (!credential) {
      return res.status(404).json({ error: 'Credential not found' });
    }
    
    // Return credential without the encrypted credentials
    const { credentials, ...credentialData } = credential.toObject();
    res.json(credentialData);
  } catch (err) {
    logger.error('Error fetching credential', { error: err.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.post('/credentials', async (req, res) => {
  try {
    const { name, connectorId, authType, credentials, userId } = req.body;
    
    if (!name || !connectorId || !authType || !credentials || !userId) {
      return res.status(400).json({ error: 'Missing required fields' });
    }
    
    // Encrypt credentials
    const encryptedCredentials = encryptCredentials(credentials);
    
    const credential = new Credential({
      name,
      connectorId,
      authType,
      credentials: encryptedCredentials,
      userId
    });
    
    await credential.save();
    
    // Return credential without the encrypted credentials
    const { credentials: _, ...credentialData } = credential.toObject();
    res.status(201).json(credentialData);
  } catch (err) {
    logger.error('Error creating credential', { error: err.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.put('/credentials/:id', async (req, res) => {
  try {
    const { name, credentials } = req.body;
    const updateData = { name, updated: Date.now() };
    
    if (credentials) {
      updateData.credentials = encryptCredentials(credentials);
    }
    
    const credential = await Credential.findByIdAndUpdate(
      req.params.id,
      updateData,
      { new: true }
    );
    
    if (!credential) {
      return res.status(404).json({ error: 'Credential not found' });
    }
    
    // Return credential without the encrypted credentials
    const { credentials: _, ...credentialData } = credential.toObject();
    res.json(credentialData);
  } catch (err) {
    logger.error('Error updating credential', { error: err.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.delete('/credentials/:id', async (req, res) => {
  try {
    const credential = await Credential.findByIdAndDelete(req.params.id);
    if (!credential) {
      return res.status(404).json({ error: 'Credential not found' });
    }
    res.status(204).send();
  } catch (err) {
    logger.error('Error deleting credential', { error: err.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get decrypted credentials (for internal use by connector executor)
app.get('/credentials/:id/decrypt', async (req, res) => {
  try {
    const credential = await Credential.findById(req.params.id);
    if (!credential) {
      return res.status(404).json({ error: 'Credential not found' });
    }
    
    // Decrypt credentials
    const decryptedCredentials = decryptCredentials(credential.credentials);
    
    res.json({
      id: credential._id,
      authType: credential.authType,
      credentials: decryptedCredentials
    });
  } catch (err) {
    logger.error('Error decrypting credentials', { error: err.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Start the server
app.listen(port, () => {
  logger.info(`Authentication service running on port ${port}`);
});
