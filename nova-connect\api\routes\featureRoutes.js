/**
 * Feature Routes
 * 
 * This file contains routes for demonstrating feature flag functionality.
 */

const express = require('express');
const router = express.Router();
const { requireFeature, checkFeatureLimit } = require('../middleware/featureFlagMiddleware');
const logger = require('../utils/logger');

/**
 * Basic feature endpoint - available in all tiers
 */
router.get('/basic', requireFeature('core.basic_connectors'), (req, res) => {
  res.json({
    message: 'Basic feature endpoint',
    feature: 'core.basic_connectors',
    tier: 'All tiers'
  });
});

/**
 * Standard feature endpoint - available in Standard tier and above
 */
router.get('/standard', requireFeature('workflow.scheduled'), (req, res) => {
  res.json({
    message: 'Standard feature endpoint',
    feature: 'workflow.scheduled',
    tier: 'Standard and above'
  });
});

/**
 * Professional feature endpoint - available in Professional tier and above
 */
router.get('/professional', requireFeature('ai.connector_generation'), (req, res) => {
  res.json({
    message: 'Professional feature endpoint',
    feature: 'ai.connector_generation',
    tier: 'Professional and above'
  });
});

/**
 * Enterprise feature endpoint - available in Enterprise tier only
 */
router.get('/enterprise', requireFeature('analytics.custom_reports'), (req, res) => {
  res.json({
    message: 'Enterprise feature endpoint',
    feature: 'analytics.custom_reports',
    tier: 'Enterprise only'
  });
});

/**
 * Limited feature endpoint - checks usage limits
 */
router.get('/limited', 
  requireFeature('core.manual_execution'),
  checkFeatureLimit('core.manual_execution', 'operations_per_day'),
  (req, res) => {
    res.json({
      message: 'Limited feature endpoint',
      feature: 'core.manual_execution',
      limit: 'operations_per_day'
    });
  }
);

/**
 * AI feature endpoint - with usage tracking
 */
router.post('/ai/generate', 
  requireFeature('ai.connector_generation'),
  checkFeatureLimit('ai.connector_generation', 'generations_per_day'),
  (req, res) => {
    // Get request body
    const { apiSpec } = req.body;
    
    if (!apiSpec) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'API specification is required'
      });
    }
    
    // Log AI generation request
    logger.info('AI connector generation request', { apiSpec });
    
    // Simulate AI generation
    const connector = {
      name: `Generated Connector for ${apiSpec.name || 'Unknown API'}`,
      type: 'http',
      description: `Automatically generated connector for ${apiSpec.name || 'Unknown API'}`,
      config: {
        base_url: apiSpec.baseUrl || 'https://api.example.com',
        default_headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        authentication: {
          type: apiSpec.authType || 'bearer_token',
          token_ref: 'api-token'
        }
      },
      endpoints: apiSpec.endpoints || [],
      created: new Date().toISOString(),
      generated: true
    };
    
    res.json({
      message: 'Connector generated successfully',
      connector
    });
  }
);

module.exports = router;
