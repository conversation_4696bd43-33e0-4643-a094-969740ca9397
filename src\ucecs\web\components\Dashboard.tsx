'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON>older, FiCheckSquare, FiAlertTriangle, FiCheckCircle, FiSettings } from 'react-icons/fi';
import { Chart as ChartJS, ArcElement, Tooltip, Legend, CategoryScale, LinearScale, BarElement, Title } from 'chart.js';
import { <PERSON>hn<PERSON>, Bar } from 'react-chartjs-2';
import FeatureGate from './FeatureGate';
import { FeatureFlag } from '@/utils/features/featureFlags';

// Register ChartJS components
ChartJS.register(ArcElement, Tooltip, Legend, CategoryScale, LinearScale, BarElement, Title);

// Mock data for the dashboard
const mockData = {
  evidenceCount: 256,
  requirementsCount: 124,
  complianceRate: 78,
  evidenceByCategory: {
    labels: ['Policies', 'Procedures', 'Configurations', 'Logs', 'Reports'],
    data: [42, 35, 68, 89, 22],
  },
  evidenceByStatus: {
    labels: ['Valid', 'Invalid', 'Pending'],
    data: [189, 32, 35],
  },
  recentEvidence: [
    { id: 'ev-001', name: 'Password Policy', category: 'Policies', status: 'Valid', date: '2023-10-15' },
    { id: 'ev-002', name: 'Firewall Configuration', category: 'Configurations', status: 'Valid', date: '2023-10-14' },
    { id: 'ev-003', name: 'User Access Review', category: 'Reports', status: 'Pending', date: '2023-10-13' },
    { id: 'ev-004', name: 'Incident Response Plan', category: 'Procedures', status: 'Invalid', date: '2023-10-12' },
    { id: 'ev-005', name: 'System Logs', category: 'Logs', status: 'Valid', date: '2023-10-11' },
  ],
};

export default function Dashboard() {
  const [data, setData] = useState(mockData);
  const [showDashboardSettings, setShowDashboardSettings] = useState(false);
  const [dashboardLayout, setDashboardLayout] = useState({
    showStats: true,
    showCategoryChart: true,
    showStatusChart: true,
    showRecentEvidence: true
  });

  // In a real application, we would fetch the data from the API
  useEffect(() => {
    // Fetch data from API
    // For now, we'll use the mock data
    setData(mockData);

    // Load dashboard layout from localStorage
    const savedLayout = localStorage.getItem('dashboardLayout');
    if (savedLayout) {
      try {
        setDashboardLayout(JSON.parse(savedLayout));
      } catch (e) {
        console.error('Failed to parse saved dashboard layout', e);
      }
    }
  }, []);

  // Save dashboard layout to localStorage
  const saveDashboardLayout = (newLayout: typeof dashboardLayout) => {
    setDashboardLayout(newLayout);
    localStorage.setItem('dashboardLayout', JSON.stringify(newLayout));
    setShowDashboardSettings(false);
  };

  // Chart data for evidence by category
  const categoryChartData = {
    labels: data.evidenceByCategory.labels,
    datasets: [
      {
        label: 'Evidence by Category',
        data: data.evidenceByCategory.data,
        backgroundColor: [
          'rgba(255, 99, 132, 0.2)',
          'rgba(54, 162, 235, 0.2)',
          'rgba(255, 206, 86, 0.2)',
          'rgba(75, 192, 192, 0.2)',
          'rgba(153, 102, 255, 0.2)',
        ],
        borderColor: [
          'rgba(255, 99, 132, 1)',
          'rgba(54, 162, 235, 1)',
          'rgba(255, 206, 86, 1)',
          'rgba(75, 192, 192, 1)',
          'rgba(153, 102, 255, 1)',
        ],
        borderWidth: 1,
      },
    ],
  };

  // Chart data for evidence by status
  const statusChartData = {
    labels: data.evidenceByStatus.labels,
    datasets: [
      {
        label: 'Evidence by Status',
        data: data.evidenceByStatus.data,
        backgroundColor: [
          'rgba(75, 192, 192, 0.2)',
          'rgba(255, 99, 132, 0.2)',
          'rgba(255, 206, 86, 0.2)',
        ],
        borderColor: [
          'rgba(75, 192, 192, 1)',
          'rgba(255, 99, 132, 1)',
          'rgba(255, 206, 86, 1)',
        ],
        borderWidth: 1,
      },
    ],
  };

  return (
    <div className="space-y-6">
      {/* Dashboard header with settings button */}
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Dashboard Overview</h2>
        <FeatureGate feature={FeatureFlag.CUSTOMIZABLE_DASHBOARD}>
          <button
            onClick={() => setShowDashboardSettings(!showDashboardSettings)}
            className="btn btn-outline btn-sm flex items-center"
          >
            <FiSettings className="mr-2" />
            Customize Dashboard
          </button>
        </FeatureGate>
      </div>

      {/* Dashboard Settings Modal */}
      {showDashboardSettings && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-secondary-light rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Dashboard Settings</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Show Statistics Cards
                </label>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    className="sr-only peer"
                    checked={dashboardLayout.showStats}
                    onChange={(e) => setDashboardLayout({...dashboardLayout, showStats: e.target.checked})}
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                </label>
              </div>
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Show Category Chart
                </label>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    className="sr-only peer"
                    checked={dashboardLayout.showCategoryChart}
                    onChange={(e) => setDashboardLayout({...dashboardLayout, showCategoryChart: e.target.checked})}
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                </label>
              </div>
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Show Status Chart
                </label>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    className="sr-only peer"
                    checked={dashboardLayout.showStatusChart}
                    onChange={(e) => setDashboardLayout({...dashboardLayout, showStatusChart: e.target.checked})}
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                </label>
              </div>
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Show Recent Evidence
                </label>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    className="sr-only peer"
                    checked={dashboardLayout.showRecentEvidence}
                    onChange={(e) => setDashboardLayout({...dashboardLayout, showRecentEvidence: e.target.checked})}
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                </label>
              </div>
            </div>
            <div className="mt-6 flex justify-end space-x-3">
              <button
                onClick={() => setShowDashboardSettings(false)}
                className="btn btn-outline"
              >
                Cancel
              </button>
              <button
                onClick={() => saveDashboardLayout(dashboardLayout)}
                className="btn btn-primary"
              >
                Save Changes
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Stats cards */}
      {dashboardLayout.showStats && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="card flex items-center">
            <div className="p-3 rounded-full bg-blue-100 dark:bg-blue-900 mr-4">
              <FiFolder className="h-6 w-6 text-blue-600 dark:text-blue-300" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Total Evidence</p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">{data.evidenceCount}</p>
            </div>
          </div>
          <div className="card flex items-center">
            <div className="p-3 rounded-full bg-green-100 dark:bg-green-900 mr-4">
              <FiCheckSquare className="h-6 w-6 text-green-600 dark:text-green-300" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Total Requirements</p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">{data.requirementsCount}</p>
            </div>
          </div>
          <div className="card flex items-center">
            <div className="p-3 rounded-full bg-purple-100 dark:bg-purple-900 mr-4">
              <FiCheckCircle className="h-6 w-6 text-purple-600 dark:text-purple-300" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Compliance Rate</p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">{data.complianceRate}%</p>
            </div>
          </div>
        </div>
      )}

      {/* Charts */}
      <FeatureGate feature={FeatureFlag.ADVANCED_DASHBOARD}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {dashboardLayout.showCategoryChart && (
            <div className="card">
              <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Evidence by Category</h2>
              <div className="h-64">
                <Bar
                  data={categoryChartData}
                  options={{
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                      legend: {
                        display: false,
                      },
                    },
                  }}
                />
              </div>
            </div>
          )}
          {dashboardLayout.showStatusChart && (
            <div className="card">
              <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Evidence by Status</h2>
              <div className="h-64 flex items-center justify-center">
                <Doughnut
                  data={statusChartData}
                  options={{
                    responsive: true,
                    maintainAspectRatio: false,
                  }}
                />
              </div>
            </div>
          )}
        </div>
      </FeatureGate>

      {/* Recent evidence */}
      {dashboardLayout.showRecentEvidence && (
        <div className="card">
          <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Recent Evidence</h2>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-800">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    ID
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Name
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Category
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Status
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Date
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-secondary-light divide-y divide-gray-200 dark:divide-gray-700">
                {data.recentEvidence.map((evidence) => (
                  <tr key={evidence.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                      {evidence.id}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                      {evidence.name}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                      {evidence.category}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          evidence.status === 'Valid'
                            ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
                            : evidence.status === 'Invalid'
                            ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
                            : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
                        }`}
                      >
                        {evidence.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                      {evidence.date}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* AI Insights - Experimental Feature */}
      <FeatureGate
        feature={FeatureFlag.EXPERIMENTAL_AI_ASSISTANT}
        fallback={
          <div className="bg-blue-50 dark:bg-blue-900/20 border-l-4 border-blue-400 p-4">
            <div className="flex">
              <div className="ml-3">
                <p className="text-sm text-blue-700 dark:text-blue-200">
                  AI Assistant is coming soon! Enable this experimental feature in settings to get AI-powered compliance insights.
                </p>
              </div>
            </div>
          </div>
        }
      >
        <div className="card">
          <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">AI Compliance Insights</h2>
          <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-md">
            <p className="text-sm text-blue-700 dark:text-blue-200 mb-2">
              <strong>Compliance Insight:</strong> Based on your recent evidence collection patterns, you may want to focus on updating your Password Policy documentation, which is approaching its review date.
            </p>
            <p className="text-sm text-blue-700 dark:text-blue-200">
              <strong>Risk Alert:</strong> Your System Logs evidence has a higher-than-average validation time. Consider implementing automated log collection to improve efficiency.
            </p>
          </div>
        </div>
      </FeatureGate>
    </div>
  );
}
