import React from 'react';
import DiagramTemplate from '../DiagramTemplate';

// Define the diagram data
const elements = [
  {
    id: 'novafuse-platform',
    top: 50,
    left: 350,
    width: 300,
    text: '13 Universal NovaFuse Components',
    number: '1',
    bold: true,
    fontSize: '20px',
    backgroundColor: '#e6f7ff'
  },
  // Core components
  {
    id: 'novacore',
    top: 150,
    left: 350,
    width: 300,
    text: 'NovaCore (Universal Compliance Testing Framework)',
    number: '2',
    bold: true,
    fontSize: '16px',
    backgroundColor: '#fff0f6'
  },
  {
    id: 'novashield',
    top: 220,
    left: 150,
    width: 250,
    text: 'NovaShield (Universal Vendor Risk Management)',
    number: '3',
    fontSize: '14px',
    backgroundColor: '#f6ffed'
  },
  {
    id: 'novatrack',
    top: 220,
    left: 600,
    width: 250,
    text: 'NovaTrack (Universal Compliance Tracking)',
    number: '4',
    fontSize: '14px',
    backgroundColor: '#f6ffed'
  },
  {
    id: 'novalearn',
    top: 290,
    left: 50,
    width: 200,
    text: '<PERSON><PERSON><PERSON><PERSON> (Universal Training)',
    number: '5',
    fontSize: '14px',
    backgroundColor: '#fffbe6'
  },
  {
    id: 'novathink',
    top: 290,
    left: 300,
    width: 200,
    text: 'NovaThink (Universal Decision Engine)',
    number: '6',
    fontSize: '14px',
    backgroundColor: '#fffbe6'
  },
  {
    id: 'novaconnect',
    top: 290,
    left: 550,
    width: 200,
    text: 'NovaConnect (Universal API)',
    number: '7',
    fontSize: '14px',
    backgroundColor: '#fffbe6'
  },
  {
    id: 'novaview',
    top: 290,
    left: 800,
    width: 200,
    text: 'NovaView (Universal Visualization)',
    number: '8',
    fontSize: '14px',
    backgroundColor: '#fffbe6'
  },
  {
    id: 'novavision',
    top: 360,
    left: 50,
    width: 200,
    text: 'NovaVision (Universal UI Framework)',
    number: '9',
    fontSize: '14px',
    backgroundColor: '#f9f0ff'
  },
  {
    id: 'novaflow',
    top: 360,
    left: 300,
    width: 200,
    text: 'NovaFlow (Universal Workflow)',
    number: '10',
    fontSize: '14px',
    backgroundColor: '#f9f0ff'
  },
  {
    id: 'novapulse',
    top: 360,
    left: 550,
    width: 200,
    text: 'NovaPulse+ (Universal Monitoring)',
    number: '11',
    fontSize: '14px',
    backgroundColor: '#f9f0ff'
  },
  {
    id: 'novastore',
    top: 360,
    left: 800,
    width: 200,
    text: 'NovaStore (Universal Marketplace)',
    number: '12',
    fontSize: '14px',
    backgroundColor: '#f9f0ff'
  },
  {
    id: 'novafuse',
    top: 430,
    left: 350,
    width: 300,
    text: 'NovaFuse (Universal Platform)',
    number: '13',
    bold: true,
    fontSize: '16px',
    backgroundColor: '#fff0f6'
  },
  // Implementation
  {
    id: 'implementation',
    top: 500,
    left: 350,
    width: 300,
    text: '3-6-9-12-13 Alignment Architecture',
    number: '14',
    bold: true,
    fontSize: '16px',
    backgroundColor: '#fffbe6'
  }
];

const connections = [
  // Connect NovaFuse Platform to NovaCore
  {
    start: { x: 500, y: 100 },
    end: { x: 500, y: 150 },
    type: 'arrow'
  },
  // Connect NovaCore to second tier
  {
    start: { x: 400, y: 200 },
    end: { x: 275, y: 220 },
    type: 'arrow'
  },
  {
    start: { x: 600, y: 200 },
    end: { x: 725, y: 220 },
    type: 'arrow'
  },
  // Connect second tier to third tier
  {
    start: { x: 200, y: 270 },
    end: { x: 150, y: 290 },
    type: 'arrow'
  },
  {
    start: { x: 250, y: 270 },
    end: { x: 400, y: 290 },
    type: 'arrow'
  },
  {
    start: { x: 750, y: 270 },
    end: { x: 600, y: 290 },
    type: 'arrow'
  },
  {
    start: { x: 800, y: 270 },
    end: { x: 900, y: 290 },
    type: 'arrow'
  },
  // Connect third tier to fourth tier
  {
    start: { x: 150, y: 340 },
    end: { x: 150, y: 360 },
    type: 'arrow'
  },
  {
    start: { x: 400, y: 340 },
    end: { x: 400, y: 360 },
    type: 'arrow'
  },
  {
    start: { x: 650, y: 340 },
    end: { x: 650, y: 360 },
    type: 'arrow'
  },
  {
    start: { x: 900, y: 340 },
    end: { x: 900, y: 360 },
    type: 'arrow'
  },
  // Connect fourth tier to NovaFuse
  {
    start: { x: 150, y: 410 },
    end: { x: 350, y: 430 },
    type: 'arrow'
  },
  {
    start: { x: 400, y: 410 },
    end: { x: 450, y: 430 },
    type: 'arrow'
  },
  {
    start: { x: 650, y: 410 },
    end: { x: 550, y: 430 },
    type: 'arrow'
  },
  {
    start: { x: 900, y: 410 },
    end: { x: 650, y: 430 },
    type: 'arrow'
  },
  // Connect NovaFuse to Implementation
  {
    start: { x: 500, y: 480 },
    end: { x: 500, y: 500 },
    type: 'arrow'
  }
];

const NovaFuseComponents: React.FC = () => {
  return (
    <DiagramTemplate 
      elements={elements} 
      connections={connections} 
      width="1050px" 
      height="600px" 
    />
  );
};

export default NovaFuseComponents;
