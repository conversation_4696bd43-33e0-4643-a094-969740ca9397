"""
Query Manager for the Universal Compliance Evidence Collection System.

This module provides functionality for searching and querying evidence,
requirements, and their relationships.
"""

import os
import json
import logging
import datetime
import re
from enum import Enum
from typing import Dict, List, Any, Optional, Set, Union, Tuple

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class QueryOperator(Enum):
    """Query operators for combining conditions."""
    AND = "and"
    OR = "or"
    NOT = "not"


class QueryField(Enum):
    """Fields that can be queried."""
    ID = "id"
    TYPE = "type"
    SOURCE = "source"
    STATUS = "status"
    CREATED_AT = "created_at"
    UPDATED_AT = "updated_at"
    TAGS = "tags"
    CATEGORY = "category"
    REQUIREMENT = "requirement"
    VALIDATION_STATUS = "validation_status"
    CONTENT = "content"
    CUSTOM = "custom"


class QueryManager:
    """
    Manager for searching and querying evidence and requirements.
    
    This class is responsible for providing search functionality across
    evidence, requirements, and their relationships.
    """
    
    def __init__(self):
        """Initialize the Query Manager."""
        logger.info("Initializing Query Manager")
        logger.info("Query Manager initialized")
    
    def search_evidence(self,
                      evidence_metadata: Dict[str, Dict[str, Any]],
                      evidence_tags: Dict[str, Set[str]],
                      evidence_by_category: Dict[str, Set[str]],
                      evidence_by_requirement: Dict[str, Set[str]],
                      query: Dict[str, Any],
                      page: int = 1,
                      page_size: int = 10) -> Dict[str, Any]:
        """
        Search for evidence based on a query.
        
        Args:
            evidence_metadata: Dictionary of evidence metadata
            evidence_tags: Dictionary of evidence tags
            evidence_by_category: Dictionary of evidence by category
            evidence_by_requirement: Dictionary of evidence by requirement
            query: The search query
            page: The page number (1-based)
            page_size: The number of results per page
            
        Returns:
            Dictionary containing search results and pagination information
        """
        logger.info(f"Searching evidence with query: {query}")
        
        # Apply the query to filter evidence
        filtered_ids = self._apply_query(
            evidence_metadata,
            evidence_tags,
            evidence_by_category,
            evidence_by_requirement,
            query
        )
        
        # Get the total number of results
        total_results = len(filtered_ids)
        
        # Calculate pagination
        total_pages = (total_results + page_size - 1) // page_size if total_results > 0 else 1
        page = max(1, min(page, total_pages))
        start_index = (page - 1) * page_size
        end_index = min(start_index + page_size, total_results)
        
        # Get the paginated results
        paginated_ids = list(filtered_ids)[start_index:end_index]
        results = [evidence_metadata[evidence_id] for evidence_id in paginated_ids]
        
        # Create the response
        response = {
            'results': results,
            'pagination': {
                'page': page,
                'page_size': page_size,
                'total_results': total_results,
                'total_pages': total_pages
            },
            'query': query
        }
        
        logger.info(f"Found {total_results} evidence items matching the query")
        
        return response
    
    def _apply_query(self,
                   evidence_metadata: Dict[str, Dict[str, Any]],
                   evidence_tags: Dict[str, Set[str]],
                   evidence_by_category: Dict[str, Set[str]],
                   evidence_by_requirement: Dict[str, Set[str]],
                   query: Dict[str, Any]) -> Set[str]:
        """
        Apply a query to filter evidence.
        
        Args:
            evidence_metadata: Dictionary of evidence metadata
            evidence_tags: Dictionary of evidence tags
            evidence_by_category: Dictionary of evidence by category
            evidence_by_requirement: Dictionary of evidence by requirement
            query: The search query
            
        Returns:
            Set of evidence IDs matching the query
        """
        # Start with all evidence IDs
        result_ids = set(evidence_metadata.keys())
        
        # Check if the query is empty
        if not query:
            return result_ids
        
        # Check if the query is a compound query (AND, OR, NOT)
        if 'operator' in query:
            operator = QueryOperator(query['operator'])
            conditions = query.get('conditions', [])
            
            if operator == QueryOperator.AND:
                # AND: Intersection of all condition results
                if conditions:
                    result_ids = self._apply_query(
                        evidence_metadata,
                        evidence_tags,
                        evidence_by_category,
                        evidence_by_requirement,
                        conditions[0]
                    )
                    
                    for condition in conditions[1:]:
                        condition_ids = self._apply_query(
                            evidence_metadata,
                            evidence_tags,
                            evidence_by_category,
                            evidence_by_requirement,
                            condition
                        )
                        result_ids &= condition_ids
            
            elif operator == QueryOperator.OR:
                # OR: Union of all condition results
                result_ids = set()
                
                for condition in conditions:
                    condition_ids = self._apply_query(
                        evidence_metadata,
                        evidence_tags,
                        evidence_by_category,
                        evidence_by_requirement,
                        condition
                    )
                    result_ids |= condition_ids
            
            elif operator == QueryOperator.NOT:
                # NOT: Negate the condition result
                if conditions:
                    condition_ids = self._apply_query(
                        evidence_metadata,
                        evidence_tags,
                        evidence_by_category,
                        evidence_by_requirement,
                        conditions[0]
                    )
                    result_ids -= condition_ids
        
        # Check if the query is a simple field query
        elif 'field' in query and 'value' in query:
            field = QueryField(query['field'])
            value = query['value']
            
            if field == QueryField.ID:
                # Filter by ID
                if isinstance(value, list):
                    result_ids = {evidence_id for evidence_id in result_ids if evidence_id in value}
                else:
                    result_ids = {evidence_id for evidence_id in result_ids if evidence_id == value}
            
            elif field == QueryField.TYPE:
                # Filter by type
                if isinstance(value, list):
                    result_ids = {
                        evidence_id for evidence_id in result_ids
                        if evidence_metadata[evidence_id].get('type') in value
                    }
                else:
                    result_ids = {
                        evidence_id for evidence_id in result_ids
                        if evidence_metadata[evidence_id].get('type') == value
                    }
            
            elif field == QueryField.SOURCE:
                # Filter by source
                if isinstance(value, list):
                    result_ids = {
                        evidence_id for evidence_id in result_ids
                        if evidence_metadata[evidence_id].get('source') in value
                    }
                else:
                    result_ids = {
                        evidence_id for evidence_id in result_ids
                        if evidence_metadata[evidence_id].get('source') == value
                    }
            
            elif field == QueryField.STATUS:
                # Filter by status
                if isinstance(value, list):
                    result_ids = {
                        evidence_id for evidence_id in result_ids
                        if evidence_metadata[evidence_id].get('status') in value
                    }
                else:
                    result_ids = {
                        evidence_id for evidence_id in result_ids
                        if evidence_metadata[evidence_id].get('status') == value
                    }
            
            elif field == QueryField.CREATED_AT:
                # Filter by created_at
                if isinstance(value, dict):
                    start = value.get('start')
                    end = value.get('end')
                    
                    if start:
                        start_dt = datetime.datetime.fromisoformat(start.replace('Z', '+00:00'))
                        result_ids = {
                            evidence_id for evidence_id in result_ids
                            if evidence_metadata[evidence_id].get('created_at') and
                            datetime.datetime.fromisoformat(
                                evidence_metadata[evidence_id]['created_at'].replace('Z', '+00:00')
                            ) >= start_dt
                        }
                    
                    if end:
                        end_dt = datetime.datetime.fromisoformat(end.replace('Z', '+00:00'))
                        result_ids = {
                            evidence_id for evidence_id in result_ids
                            if evidence_metadata[evidence_id].get('created_at') and
                            datetime.datetime.fromisoformat(
                                evidence_metadata[evidence_id]['created_at'].replace('Z', '+00:00')
                            ) <= end_dt
                        }
            
            elif field == QueryField.UPDATED_AT:
                # Filter by updated_at
                if isinstance(value, dict):
                    start = value.get('start')
                    end = value.get('end')
                    
                    if start:
                        start_dt = datetime.datetime.fromisoformat(start.replace('Z', '+00:00'))
                        result_ids = {
                            evidence_id for evidence_id in result_ids
                            if evidence_metadata[evidence_id].get('updated_at') and
                            datetime.datetime.fromisoformat(
                                evidence_metadata[evidence_id]['updated_at'].replace('Z', '+00:00')
                            ) >= start_dt
                        }
                    
                    if end:
                        end_dt = datetime.datetime.fromisoformat(end.replace('Z', '+00:00'))
                        result_ids = {
                            evidence_id for evidence_id in result_ids
                            if evidence_metadata[evidence_id].get('updated_at') and
                            datetime.datetime.fromisoformat(
                                evidence_metadata[evidence_id]['updated_at'].replace('Z', '+00:00')
                            ) <= end_dt
                        }
            
            elif field == QueryField.TAGS:
                # Filter by tags
                if isinstance(value, list):
                    # Match any of the tags
                    tag_ids = set()
                    for tag in value:
                        for evidence_id, tags in evidence_tags.items():
                            if tag in tags:
                                tag_ids.add(evidence_id)
                    result_ids &= tag_ids
                else:
                    # Match a single tag
                    tag_ids = {
                        evidence_id for evidence_id, tags in evidence_tags.items()
                        if value in tags
                    }
                    result_ids &= tag_ids
            
            elif field == QueryField.CATEGORY:
                # Filter by category
                if isinstance(value, list):
                    # Match any of the categories
                    category_ids = set()
                    for category in value:
                        if category in evidence_by_category:
                            category_ids |= evidence_by_category[category]
                    result_ids &= category_ids
                else:
                    # Match a single category
                    if value in evidence_by_category:
                        result_ids &= evidence_by_category[value]
                    else:
                        result_ids = set()
            
            elif field == QueryField.REQUIREMENT:
                # Filter by requirement
                if isinstance(value, list):
                    # Match any of the requirements
                    requirement_ids = set()
                    for requirement in value:
                        if requirement in evidence_by_requirement:
                            requirement_ids |= evidence_by_requirement[requirement]
                    result_ids &= requirement_ids
                else:
                    # Match a single requirement
                    if value in evidence_by_requirement:
                        result_ids &= evidence_by_requirement[value]
                    else:
                        result_ids = set()
            
            elif field == QueryField.VALIDATION_STATUS:
                # Filter by validation status
                if value is True:
                    # Valid evidence
                    result_ids = {
                        evidence_id for evidence_id in result_ids
                        if evidence_metadata[evidence_id].get('validation_results', {}).get('is_valid', False)
                    }
                elif value is False:
                    # Invalid evidence
                    result_ids = {
                        evidence_id for evidence_id in result_ids
                        if not evidence_metadata[evidence_id].get('validation_results', {}).get('is_valid', False)
                    }
            
            elif field == QueryField.CONTENT:
                # Filter by content (text search)
                if isinstance(value, str) and value:
                    # Simple text search in the evidence data
                    # This is a placeholder - in a real implementation, you would
                    # need to access the actual evidence content, not just metadata
                    result_ids = {
                        evidence_id for evidence_id in result_ids
                        if self._search_text_in_metadata(evidence_metadata[evidence_id], value)
                    }
            
            elif field == QueryField.CUSTOM:
                # Custom query function
                if callable(value):
                    result_ids = {
                        evidence_id for evidence_id in result_ids
                        if value(evidence_metadata[evidence_id])
                    }
        
        return result_ids
    
    def _search_text_in_metadata(self, metadata: Dict[str, Any], search_text: str) -> bool:
        """
        Search for text in metadata.
        
        Args:
            metadata: The metadata to search in
            search_text: The text to search for
            
        Returns:
            True if the text is found, False otherwise
        """
        # Convert search text to lowercase for case-insensitive search
        search_text = search_text.lower()
        
        # Check if the search text is in any of the metadata values
        for key, value in metadata.items():
            if isinstance(value, str) and search_text in value.lower():
                return True
            elif isinstance(value, dict):
                if self._search_text_in_metadata(value, search_text):
                    return True
        
        return False
    
    def search_requirements(self,
                          requirements: Dict[str, Dict[str, Any]],
                          evidence_by_requirement: Dict[str, Set[str]],
                          query: Dict[str, Any],
                          page: int = 1,
                          page_size: int = 10) -> Dict[str, Any]:
        """
        Search for requirements based on a query.
        
        Args:
            requirements: Dictionary of requirements
            evidence_by_requirement: Dictionary of evidence by requirement
            query: The search query
            page: The page number (1-based)
            page_size: The number of results per page
            
        Returns:
            Dictionary containing search results and pagination information
        """
        logger.info(f"Searching requirements with query: {query}")
        
        # Apply the query to filter requirements
        filtered_ids = self._apply_requirement_query(
            requirements,
            evidence_by_requirement,
            query
        )
        
        # Get the total number of results
        total_results = len(filtered_ids)
        
        # Calculate pagination
        total_pages = (total_results + page_size - 1) // page_size if total_results > 0 else 1
        page = max(1, min(page, total_pages))
        start_index = (page - 1) * page_size
        end_index = min(start_index + page_size, total_results)
        
        # Get the paginated results
        paginated_ids = list(filtered_ids)[start_index:end_index]
        results = [requirements[requirement_id] for requirement_id in paginated_ids]
        
        # Create the response
        response = {
            'results': results,
            'pagination': {
                'page': page,
                'page_size': page_size,
                'total_results': total_results,
                'total_pages': total_pages
            },
            'query': query
        }
        
        logger.info(f"Found {total_results} requirements matching the query")
        
        return response
    
    def _apply_requirement_query(self,
                               requirements: Dict[str, Dict[str, Any]],
                               evidence_by_requirement: Dict[str, Set[str]],
                               query: Dict[str, Any]) -> Set[str]:
        """
        Apply a query to filter requirements.
        
        Args:
            requirements: Dictionary of requirements
            evidence_by_requirement: Dictionary of evidence by requirement
            query: The search query
            
        Returns:
            Set of requirement IDs matching the query
        """
        # Start with all requirement IDs
        result_ids = set(requirements.keys())
        
        # Check if the query is empty
        if not query:
            return result_ids
        
        # Check if the query is a compound query (AND, OR, NOT)
        if 'operator' in query:
            operator = QueryOperator(query['operator'])
            conditions = query.get('conditions', [])
            
            if operator == QueryOperator.AND:
                # AND: Intersection of all condition results
                if conditions:
                    result_ids = self._apply_requirement_query(
                        requirements,
                        evidence_by_requirement,
                        conditions[0]
                    )
                    
                    for condition in conditions[1:]:
                        condition_ids = self._apply_requirement_query(
                            requirements,
                            evidence_by_requirement,
                            condition
                        )
                        result_ids &= condition_ids
            
            elif operator == QueryOperator.OR:
                # OR: Union of all condition results
                result_ids = set()
                
                for condition in conditions:
                    condition_ids = self._apply_requirement_query(
                        requirements,
                        evidence_by_requirement,
                        condition
                    )
                    result_ids |= condition_ids
            
            elif operator == QueryOperator.NOT:
                # NOT: Negate the condition result
                if conditions:
                    condition_ids = self._apply_requirement_query(
                        requirements,
                        evidence_by_requirement,
                        conditions[0]
                    )
                    result_ids -= condition_ids
        
        # Check if the query is a simple field query
        elif 'field' in query and 'value' in query:
            field = query['field']
            value = query['value']
            
            if field == 'id':
                # Filter by ID
                if isinstance(value, list):
                    result_ids = {req_id for req_id in result_ids if req_id in value}
                else:
                    result_ids = {req_id for req_id in result_ids if req_id == value}
            
            elif field == 'name':
                # Filter by name
                if isinstance(value, str):
                    result_ids = {
                        req_id for req_id in result_ids
                        if value.lower() in requirements[req_id].get('name', '').lower()
                    }
            
            elif field == 'framework':
                # Filter by framework
                if isinstance(value, list):
                    result_ids = {
                        req_id for req_id in result_ids
                        if requirements[req_id].get('framework') in value
                    }
                else:
                    result_ids = {
                        req_id for req_id in result_ids
                        if requirements[req_id].get('framework') == value
                    }
            
            elif field == 'control':
                # Filter by control
                if isinstance(value, list):
                    result_ids = {
                        req_id for req_id in result_ids
                        if requirements[req_id].get('control') in value
                    }
                else:
                    result_ids = {
                        req_id for req_id in result_ids
                        if requirements[req_id].get('control') == value
                    }
            
            elif field == 'description':
                # Filter by description
                if isinstance(value, str):
                    result_ids = {
                        req_id for req_id in result_ids
                        if value.lower() in requirements[req_id].get('description', '').lower()
                    }
            
            elif field == 'has_evidence':
                # Filter by whether the requirement has evidence
                if value is True:
                    result_ids = {
                        req_id for req_id in result_ids
                        if req_id in evidence_by_requirement and evidence_by_requirement[req_id]
                    }
                elif value is False:
                    result_ids = {
                        req_id for req_id in result_ids
                        if req_id not in evidence_by_requirement or not evidence_by_requirement[req_id]
                    }
            
            elif field == 'evidence_count':
                # Filter by evidence count
                if isinstance(value, dict):
                    min_count = value.get('min')
                    max_count = value.get('max')
                    
                    if min_count is not None:
                        result_ids = {
                            req_id for req_id in result_ids
                            if req_id in evidence_by_requirement and len(evidence_by_requirement[req_id]) >= min_count
                        }
                    
                    if max_count is not None:
                        result_ids = {
                            req_id for req_id in result_ids
                            if req_id not in evidence_by_requirement or len(evidence_by_requirement[req_id]) <= max_count
                        }
            
            elif field == 'content':
                # Filter by content (text search)
                if isinstance(value, str) and value:
                    # Simple text search in the requirement data
                    result_ids = {
                        req_id for req_id in result_ids
                        if self._search_text_in_requirement(requirements[req_id], value)
                    }
            
            elif field == 'custom':
                # Custom query function
                if callable(value):
                    result_ids = {
                        req_id for req_id in result_ids
                        if value(requirements[req_id])
                    }
        
        return result_ids
    
    def _search_text_in_requirement(self, requirement: Dict[str, Any], search_text: str) -> bool:
        """
        Search for text in a requirement.
        
        Args:
            requirement: The requirement to search in
            search_text: The text to search for
            
        Returns:
            True if the text is found, False otherwise
        """
        # Convert search text to lowercase for case-insensitive search
        search_text = search_text.lower()
        
        # Check if the search text is in any of the requirement values
        for key, value in requirement.items():
            if isinstance(value, str) and search_text in value.lower():
                return True
            elif isinstance(value, dict):
                if self._search_text_in_requirement(value, search_text):
                    return True
        
        return False
    
    def build_query(self,
                  field: Union[str, QueryField],
                  value: Any) -> Dict[str, Any]:
        """
        Build a simple query for a field.
        
        Args:
            field: The field to query
            value: The value to match
            
        Returns:
            A query dictionary
        """
        if isinstance(field, QueryField):
            field = field.value
        
        return {
            'field': field,
            'value': value
        }
    
    def build_compound_query(self,
                           operator: Union[str, QueryOperator],
                           conditions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Build a compound query with multiple conditions.
        
        Args:
            operator: The operator to use (AND, OR, NOT)
            conditions: The conditions to combine
            
        Returns:
            A compound query dictionary
        """
        if isinstance(operator, QueryOperator):
            operator = operator.value
        
        return {
            'operator': operator,
            'conditions': conditions
        }
