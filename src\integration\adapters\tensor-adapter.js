/**
 * Tensor Adapter
 * 
 * This module provides an adapter for the Self-Healing Tensor system to integrate
 * with the unified integration system.
 */

const EventEmitter = require('events');

/**
 * TensorAdapter class
 */
class TensorAdapter extends EventEmitter {
  /**
   * Create a new TensorAdapter instance
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      enableLogging: true,
      autoConnect: true,
      ...options
    };
    
    // Store tensor instance
    this.tensor = options.tensor || null;
    
    // Initialize state
    this.state = {
      isConnected: false,
      lastUpdate: null,
      registeredTensors: new Map(),
      healingHistory: new Map()
    };
    
    // Connect to tensor if provided and autoConnect is true
    if (this.tensor && this.options.autoConnect) {
      this.connect();
    }
    
    if (this.options.enableLogging) {
      console.log('TensorAdapter initialized');
    }
  }
  
  /**
   * Connect to the tensor system
   * @returns {boolean} - Success status
   */
  connect() {
    if (!this.tensor) {
      if (this.options.enableLogging) {
        console.log('TensorAdapter: No tensor instance provided');
      }
      return false;
    }
    
    if (this.state.isConnected) {
      if (this.options.enableLogging) {
        console.log('TensorAdapter: Already connected');
      }
      return true;
    }
    
    // Set up event listeners
    this._setupEventListeners();
    
    // Update state
    this.state.isConnected = true;
    this.state.lastUpdate = Date.now();
    
    // Emit event
    this.emit('connected', {
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log('TensorAdapter: Connected to tensor system');
    }
    
    return true;
  }
  
  /**
   * Disconnect from the tensor system
   * @returns {boolean} - Success status
   */
  disconnect() {
    if (!this.state.isConnected) {
      if (this.options.enableLogging) {
        console.log('TensorAdapter: Not connected');
      }
      return false;
    }
    
    // Remove event listeners
    this._removeEventListeners();
    
    // Update state
    this.state.isConnected = false;
    this.state.lastUpdate = Date.now();
    
    // Emit event
    this.emit('disconnected', {
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log('TensorAdapter: Disconnected from tensor system');
    }
    
    return true;
  }
  
  /**
   * Set up event listeners
   * @private
   */
  _setupEventListeners() {
    if (!this.tensor) {
      return;
    }
    
    // Listen for tensor events
    this.tensor.on('tensor-registered', this._handleTensorRegistered.bind(this));
    this.tensor.on('tensor-healed', this._handleTensorHealed.bind(this));
    this.tensor.on('tensor-damaged', this._handleTensorDamaged.bind(this));
    this.tensor.on('healing-skipped', this._handleHealingSkipped.bind(this));
    this.tensor.on('healing-limit-reached', this._handleHealingLimitReached.bind(this));
    this.tensor.on('healing-ineffective', this._handleHealingIneffective.bind(this));
    this.tensor.on('emergency-healing', this._handleEmergencyHealing.bind(this));
    this.tensor.on('entropy-decayed', this._handleEntropyDecayed.bind(this));
    this.tensor.on('threshold-adjusted', this._handleThresholdAdjusted.bind(this));
    this.tensor.on('resonance-optimization', this._handleResonanceOptimization.bind(this));
  }
  
  /**
   * Remove event listeners
   * @private
   */
  _removeEventListeners() {
    if (!this.tensor) {
      return;
    }
    
    // Remove tensor event listeners
    this.tensor.removeAllListeners('tensor-registered');
    this.tensor.removeAllListeners('tensor-healed');
    this.tensor.removeAllListeners('tensor-damaged');
    this.tensor.removeAllListeners('healing-skipped');
    this.tensor.removeAllListeners('healing-limit-reached');
    this.tensor.removeAllListeners('healing-ineffective');
    this.tensor.removeAllListeners('emergency-healing');
    this.tensor.removeAllListeners('entropy-decayed');
    this.tensor.removeAllListeners('threshold-adjusted');
    this.tensor.removeAllListeners('resonance-optimization');
  }
  
  /**
   * Handle tensor registered event
   * @param {Object} data - Event data
   * @private
   */
  _handleTensorRegistered(data) {
    // Store registered tensor
    this.state.registeredTensors.set(data.id, {
      domain: data.domain,
      registeredAt: Date.now()
    });
    
    // Update state
    this.state.lastUpdate = Date.now();
    
    // Emit event
    this.emit('tensor-registered', {
      id: data.id,
      domain: data.domain,
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log(`TensorAdapter: Tensor registered - ${data.id} (${data.domain})`);
    }
  }
  
  /**
   * Handle tensor healed event
   * @param {Object} data - Event data
   * @private
   */
  _handleTensorHealed(data) {
    // Store healing history
    const history = this.state.healingHistory.get(data.id) || [];
    history.push({
      cycle: data.healingCycle,
      healthImprovement: data.healthImprovement,
      entropyReduction: data.entropyReduction,
      efficiencyScore: data.efficiencyScore,
      timestamp: Date.now()
    });
    this.state.healingHistory.set(data.id, history);
    
    // Update state
    this.state.lastUpdate = Date.now();
    
    // Emit event
    this.emit('tensor-healed', {
      id: data.id,
      cycle: data.healingCycle,
      healthImprovement: data.healthImprovement,
      entropyReduction: data.entropyReduction,
      efficiencyScore: data.efficiencyScore,
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log(`TensorAdapter: Tensor healed - ${data.id} (Cycle: ${data.healingCycle})`);
    }
  }
  
  /**
   * Handle tensor damaged event
   * @param {Object} data - Event data
   * @private
   */
  _handleTensorDamaged(data) {
    // Update state
    this.state.lastUpdate = Date.now();
    
    // Emit event
    this.emit('tensor-damaged', {
      id: data.id,
      damageLevel: data.damageLevel,
      healthBefore: data.healthBefore,
      healthAfter: data.healthAfter,
      entropyContainmentBefore: data.entropyContainmentBefore,
      entropyContainmentAfter: data.entropyContainmentAfter,
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log(`TensorAdapter: Tensor damaged - ${data.id} (Damage: ${data.damageLevel})`);
    }
  }
  
  /**
   * Handle healing skipped event
   * @param {Object} data - Event data
   * @private
   */
  _handleHealingSkipped(data) {
    // Update state
    this.state.lastUpdate = Date.now();
    
    // Emit event
    this.emit('healing-skipped', {
      id: data.id,
      reason: data.reason,
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log(`TensorAdapter: Healing skipped - ${data.id} (${data.reason})`);
    }
  }
  
  /**
   * Handle healing limit reached event
   * @param {Object} data - Event data
   * @private
   */
  _handleHealingLimitReached(data) {
    // Update state
    this.state.lastUpdate = Date.now();
    
    // Emit event
    this.emit('healing-limit-reached', {
      id: data.id,
      healingCycles: data.healingCycles,
      maxCycles: data.maxCycles,
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log(`TensorAdapter: Healing limit reached - ${data.id} (${data.healingCycles}/${data.maxCycles})`);
    }
  }
  
  /**
   * Register a tensor
   * @param {string} id - Tensor ID
   * @param {Object} tensor - Tensor to register
   * @param {string} domain - Domain to register tensor in
   * @returns {Object} - Registered tensor
   */
  registerTensor(id, tensor, domain = 'universal') {
    if (!this.tensor) {
      throw new Error('No tensor instance provided');
    }
    
    if (!this.state.isConnected) {
      throw new Error('Not connected to tensor system');
    }
    
    return this.tensor.registerTensor(id, tensor, domain);
  }
  
  /**
   * Get a tensor
   * @param {string} id - Tensor ID
   * @returns {Object} - Tensor
   */
  getTensor(id) {
    if (!this.tensor) {
      throw new Error('No tensor instance provided');
    }
    
    if (!this.state.isConnected) {
      throw new Error('Not connected to tensor system');
    }
    
    return this.tensor.getTensor(id);
  }
  
  /**
   * Update a tensor
   * @param {string} id - Tensor ID
   * @param {Object} updatedTensor - Updated tensor
   * @returns {Object} - Updated tensor
   */
  updateTensor(id, updatedTensor) {
    if (!this.tensor) {
      throw new Error('No tensor instance provided');
    }
    
    if (!this.state.isConnected) {
      throw new Error('Not connected to tensor system');
    }
    
    return this.tensor.updateTensor(id, updatedTensor);
  }
  
  /**
   * Heal a tensor
   * @param {string} id - Tensor ID
   * @returns {Object} - Healed tensor
   */
  healTensor(id) {
    if (!this.tensor) {
      throw new Error('No tensor instance provided');
    }
    
    if (!this.state.isConnected) {
      throw new Error('Not connected to tensor system');
    }
    
    return this.tensor.healTensor(id);
  }
  
  /**
   * Get healing history for a tensor
   * @param {string} id - Tensor ID
   * @returns {Array} - Healing history
   */
  getHealingHistory(id) {
    if (!this.tensor) {
      throw new Error('No tensor instance provided');
    }
    
    if (!this.state.isConnected) {
      throw new Error('Not connected to tensor system');
    }
    
    return this.tensor.getHealingHistory(id);
  }
}

module.exports = TensorAdapter;
