90b70b7a7c5b237efdcb53c80fcca813
// Mock connector registry
_getJestObj().mock('../../registry/connector-registry', () => ({
  getConnector: jest.fn(),
  initialize: jest.fn().mockResolvedValue(true)
}));
function _getJestObj() {
  const {
    jest
  } = require("@jest/globals");
  _getJestObj = () => jest;
  return jest;
}
/**
 * Unit tests for the Connector Executor
 */

const path = require('path');
const fs = require('fs').promises;
const axios = require('axios');
const MockAdapter = require('axios-mock-adapter');
const connectorExecutor = require('../../connector-executor');
const connectorRegistry = require('../../registry/connector-registry');
const {
  mockGoogleCloudConnector
} = require('../mocks/mock-connector');

// Mock axios
const mockAxios = new MockAdapter(axios);
describe('Connector Executor', () => {
  beforeEach(() => {
    // Reset mocks
    mockAxios.reset();
    jest.clearAllMocks();
  });
  test('should execute a connector endpoint successfully', async () => {
    // Mock connector registry response
    connectorRegistry.getConnector.mockReturnValue(mockGoogleCloudConnector);

    // Mock axios response
    mockAxios.onGet('https://securitycenter.googleapis.com/v1/organizations/123/sources/456/findings').reply(200, {
      findings: [{
        name: 'finding-1',
        parent: 'organizations/123/sources/456',
        resourceName: 'projects/my-project/instances/my-instance',
        state: 'ACTIVE',
        category: 'VULNERABILITY',
        severity: 'HIGH',
        eventTime: '2023-06-01T00:00:00Z',
        createTime: '2023-06-01T00:00:00Z'
      }],
      nextPageToken: 'next-page-token'
    });

    // Execute connector
    const result = await connectorExecutor.executeConnector('google-cloud-security-1.0.0', 'list-findings', {
      path: {
        organizationId: '123',
        sourceId: '456'
      },
      auth: {
        token: 'test-token'
      }
    });

    // Verify result
    expect(result.success).toBe(true);
    expect(result.data).toEqual([{
      name: 'finding-1',
      parent: 'organizations/123/sources/456',
      resourceName: 'projects/my-project/instances/my-instance',
      state: 'ACTIVE',
      category: 'VULNERABILITY',
      severity: 'HIGH',
      eventTime: '2023-06-01T00:00:00Z',
      createTime: '2023-06-01T00:00:00Z'
    }]);
    expect(result.statusCode).toBe(200);

    // Verify connector registry was called
    expect(connectorRegistry.getConnector).toHaveBeenCalledWith('google-cloud-security-1.0.0');

    // Verify axios was called with correct parameters
    expect(mockAxios.history.get.length).toBe(1);
    expect(mockAxios.history.get[0].url).toBe('https://securitycenter.googleapis.com/v1/organizations/123/sources/456/findings');
    expect(mockAxios.history.get[0].headers.Authorization).toBe('Bearer test-token');
  });
  test('should handle connector not found error', async () => {
    // Mock connector registry response
    connectorRegistry.getConnector.mockReturnValue(null);

    // Execute connector
    const result = await connectorExecutor.executeConnector('non-existent-connector', 'list-findings', {});

    // Verify result
    expect(result.success).toBe(false);
    expect(result.error).toContain('not found');
    expect(result.statusCode).toBe(500);

    // Verify connector registry was called
    expect(connectorRegistry.getConnector).toHaveBeenCalledWith('non-existent-connector');

    // Verify axios was not called
    expect(mockAxios.history.get.length).toBe(0);
  });
  test('should handle endpoint not found error', async () => {
    // Mock connector registry response
    connectorRegistry.getConnector.mockReturnValue(mockGoogleCloudConnector);

    // Execute connector
    const result = await connectorExecutor.executeConnector('google-cloud-security-1.0.0', 'non-existent-endpoint', {});

    // Verify result
    expect(result.success).toBe(false);
    expect(result.error).toContain('not found');
    expect(result.statusCode).toBe(500);

    // Verify connector registry was called
    expect(connectorRegistry.getConnector).toHaveBeenCalledWith('google-cloud-security-1.0.0');

    // Verify axios was not called
    expect(mockAxios.history.get.length).toBe(0);
  });
  test('should handle API error', async () => {
    // Mock connector registry response
    connectorRegistry.getConnector.mockReturnValue(mockGoogleCloudConnector);

    // Mock axios response
    mockAxios.onGet('https://securitycenter.googleapis.com/v1/organizations/123/sources/456/findings').reply(403, {
      error: {
        code: 403,
        message: 'Permission denied',
        status: 'PERMISSION_DENIED'
      }
    });

    // Execute connector
    const result = await connectorExecutor.executeConnector('google-cloud-security-1.0.0', 'list-findings', {
      path: {
        organizationId: '123',
        sourceId: '456'
      },
      auth: {
        token: 'test-token'
      }
    });

    // Verify result
    expect(result.success).toBe(false);
    expect(result.statusCode).toBe(403);

    // Verify connector registry was called
    expect(connectorRegistry.getConnector).toHaveBeenCalledWith('google-cloud-security-1.0.0');

    // Verify axios was called with correct parameters
    expect(mockAxios.history.get.length).toBe(1);
    expect(mockAxios.history.get[0].url).toBe('https://securitycenter.googleapis.com/v1/organizations/123/sources/456/findings');
  });
  test('should update metrics after execution', async () => {
    // Mock connector registry response
    connectorRegistry.getConnector.mockReturnValue(mockGoogleCloudConnector);

    // Mock axios response
    mockAxios.onGet('https://securitycenter.googleapis.com/v1/organizations/123/sources/456/findings').reply(200, {
      findings: [],
      nextPageToken: ''
    });

    // Get initial metrics
    const initialMetrics = connectorExecutor.getMetrics();

    // Execute connector
    await connectorExecutor.executeConnector('google-cloud-security-1.0.0', 'list-findings', {
      path: {
        organizationId: '123',
        sourceId: '456'
      }
    });

    // Get updated metrics
    const updatedMetrics = connectorExecutor.getMetrics();

    // Verify metrics were updated
    expect(updatedMetrics.totalRequests).toBe(initialMetrics.totalRequests + 1);
    expect(updatedMetrics.successfulRequests).toBe(initialMetrics.successfulRequests + 1);
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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