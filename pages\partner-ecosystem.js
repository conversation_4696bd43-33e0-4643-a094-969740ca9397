import React from 'react';
import Link from 'next/link';
import PageWithSidebar from '../components/PageWithSidebar';

export default function PartnerEcosystem() {
  const sidebarItems = [
    { type: 'category', label: 'Partner Types', items: [
      { label: 'Technology Partners', href: '#technology-partners' },
      { label: 'Solution Partners', href: '#solution-partners' },
      { label: 'Consulting Partners', href: '#consulting-partners' },
      { label: 'Marketplace Partners', href: '#marketplace-partners' }
    ]},
    { type: 'category', label: 'Partner Resources', items: [
      { label: 'Partner Portal', href: '#partner-portal' },
      { label: 'Revenue Share Model', href: '#revenue-share' },
      { label: 'Marketing Resources', href: '#marketing-resources' },
      { label: 'Technical Resources', href: '#technical-resources' }
    ]},
    { type: 'category', label: 'Preferred Partners', items: [
      { label: 'Zapier', href: '/zapier-partnership' },
      { label: 'Okta', href: '/okta-partnership' },
      { label: 'Airtable', href: '/airtable-partnership' }
    ]}
  ];

  return (
    <PageWithSidebar sidebarItems={sidebarItems} sidebarTitle="Partner Ecosystem" title="Partner Ecosystem - NovaFuse API Superstore">

      {/* Hero Section */}
      <div className="bg-secondary rounded-lg p-8 mb-8">
        <h1 className="text-3xl font-bold mb-4">NovaFuse Partner Ecosystem</h1>
        <p className="text-xl mb-6">
          Join our growing network of partners and help customers streamline their GRC workflows.
          Together, we can build a more secure and compliant digital world.
        </p>
        <div className="flex flex-wrap gap-4">
          <Link href="/become-partner" className="accent-bg text-white px-6 py-3 rounded-lg font-bold hover:bg-blue-700 inline-block">
            Become a Partner
          </Link>
          <Link href="/partner-login" className="border border-blue-600 text-blue-600 px-6 py-3 rounded-lg font-bold hover:bg-blue-900 hover:bg-opacity-20 inline-block">
            Partner Login
          </Link>
        </div>
      </div>

      {/* Partner Benefits */}
      <div className="mb-12">
        <h2 className="text-2xl font-bold mb-6">Partner Benefits</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-secondary border border-gray-700 rounded-lg p-6">
            <div className="text-3xl text-blue-500 mb-4">01</div>
            <h3 className="text-xl font-semibold mb-3">Revenue Sharing</h3>
            <p className="text-gray-300">Earn up to 90% revenue share on all customer subscriptions through our tiered partnership model.</p>
          </div>

          <div className="bg-secondary border border-gray-700 rounded-lg p-6">
            <div className="text-3xl text-blue-500 mb-4">02</div>
            <h3 className="text-xl font-semibold mb-3">Co-Marketing</h3>
            <p className="text-gray-300">Access joint marketing opportunities, featured placement in our marketplace, and co-branded materials.</p>
          </div>

          <div className="bg-secondary border border-gray-700 rounded-lg p-6">
            <div className="text-3xl text-blue-500 mb-4">03</div>
            <h3 className="text-xl font-semibold mb-3">Technical Support</h3>
            <p className="text-gray-300">Receive dedicated technical support, integration assistance, and access to our developer resources.</p>
          </div>

          <div className="bg-secondary border border-gray-700 rounded-lg p-6">
            <div className="text-3xl text-blue-500 mb-4">04</div>
            <h3 className="text-xl font-semibold mb-3">Market Expansion</h3>
            <p className="text-gray-300">Reach new customers and markets through our established customer base and distribution channels.</p>
          </div>

          <div className="bg-secondary border border-gray-700 rounded-lg p-6">
            <div className="text-3xl text-blue-500 mb-4">05</div>
            <h3 className="text-xl font-semibold mb-3">Early Access</h3>
            <p className="text-gray-300">Get early access to new features, APIs, and product roadmap to stay ahead of the competition.</p>
          </div>

          <div className="bg-secondary border border-gray-700 rounded-lg p-6">
            <div className="text-3xl text-blue-500 mb-4">06</div>
            <h3 className="text-xl font-semibold mb-3">Partner Empowerment</h3>
            <p className="text-gray-300">Benefit from our unique Partner Empowerment approach, building with partners rather than selling to them.</p>
          </div>
        </div>
      </div>

      {/* Partner Types */}
      <div className="mb-12">
        <h2 className="text-2xl font-bold mb-6">Partner Types</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-secondary border border-gray-700 rounded-lg p-6">
            <h3 className="text-xl font-semibold mb-3">Technology Partners</h3>
            <p className="text-gray-300 mb-4">
              Integrate your technology with NovaFuse to provide seamless experiences for mutual customers.
              Ideal for SaaS providers, security tools, and compliance platforms.
            </p>
            <ul className="list-disc list-inside text-gray-300 mb-4">
              <li>API integration</li>
              <li>Joint solution development</li>
              <li>Technical certification</li>
              <li>Co-selling opportunities</li>
            </ul>
            <Link href="/technology-partners" className="text-blue-400 hover:text-blue-300">Learn more →</Link>
          </div>

          <div className="bg-secondary border border-gray-700 rounded-lg p-6">
            <h3 className="text-xl font-semibold mb-3">Consulting Partners</h3>
            <p className="text-gray-300 mb-4">
              Help customers implement and optimize NovaFuse solutions. Ideal for consulting firms,
              system integrators, and professional services organizations.
            </p>
            <ul className="list-disc list-inside text-gray-300 mb-4">
              <li>Implementation services</li>
              <li>Customer success management</li>
              <li>Training and certification</li>
              <li>Advisory services</li>
            </ul>
            <Link href="/consulting-partners" className="text-blue-400 hover:text-blue-300">Learn more →</Link>
          </div>

          <div className="bg-secondary border border-gray-700 rounded-lg p-6">
            <h3 className="text-xl font-semibold mb-3">Reseller Partners</h3>
            <p className="text-gray-300 mb-4">
              Sell NovaFuse solutions to your customers. Ideal for value-added resellers,
              distributors, and managed service providers.
            </p>
            <ul className="list-disc list-inside text-gray-300 mb-4">
              <li>Competitive margins</li>
              <li>Sales enablement</li>
              <li>Deal registration</li>
              <li>Marketing development funds</li>
            </ul>
            <Link href="/reseller-partners" className="text-blue-400 hover:text-blue-300">Learn more →</Link>
          </div>

          <div className="bg-secondary border border-gray-700 rounded-lg p-6">
            <h3 className="text-xl font-semibold mb-3">Strategic Partners</h3>
            <p className="text-gray-300 mb-4">
              Collaborate on strategic initiatives and market development. Ideal for industry
              leaders, cloud providers, and enterprise platforms.
            </p>
            <ul className="list-disc list-inside text-gray-300 mb-4">
              <li>Executive sponsorship</li>
              <li>Joint go-to-market strategy</li>
              <li>Product roadmap influence</li>
              <li>Strategic investment opportunities</li>
            </ul>
            <Link href="/strategic-partners" className="text-blue-400 hover:text-blue-300">Learn more →</Link>
          </div>
        </div>
      </div>

      {/* Preferred Partners */}
      <div className="mb-12">
        <h2 className="text-2xl font-bold mb-6">Preferred Partners</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
          <Link href="/zapier-partnership" className="bg-secondary border border-gray-700 rounded-lg p-6 text-center hover:border-blue-500 transition-colors">
            <div className="w-16 h-16 mx-auto bg-gray-800 rounded-md flex items-center justify-center mb-4">
              <span className="text-2xl">Z</span>
            </div>
            <h3 className="font-semibold">Zapier</h3>
          </Link>

          <Link href="/okta-partnership" className="bg-secondary border border-gray-700 rounded-lg p-6 text-center hover:border-blue-500 transition-colors">
            <div className="w-16 h-16 mx-auto bg-gray-800 rounded-md flex items-center justify-center mb-4">
              <span className="text-2xl">O</span>
            </div>
            <h3 className="font-semibold">Okta</h3>
          </Link>

          <Link href="/airtable-partnership" className="bg-secondary border border-gray-700 rounded-lg p-6 text-center hover:border-blue-500 transition-colors">
            <div className="w-16 h-16 mx-auto bg-gray-800 rounded-md flex items-center justify-center mb-4">
              <span className="text-2xl">A</span>
            </div>
            <h3 className="font-semibold">Airtable</h3>
          </Link>

          <Link href="/google-partnership" className="bg-secondary border border-gray-700 rounded-lg p-6 text-center hover:border-blue-500 transition-colors">
            <div className="w-16 h-16 mx-auto bg-gray-800 rounded-md flex items-center justify-center mb-4">
              <span className="text-2xl">G</span>
            </div>
            <h3 className="font-semibold">Google Cloud</h3>
          </Link>
        </div>
      </div>

      {/* Partner Testimonials */}
      <div className="mb-12">
        <h2 className="text-2xl font-bold mb-6">Partner Testimonials</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-secondary border border-gray-700 rounded-lg p-6">
            <p className="text-gray-300 mb-4 italic">
              "Partnering with NovaFuse has been transformative for our business. The revenue sharing model
              is the most generous in the industry, and their technical support is unmatched."
            </p>
            <div className="flex items-center">
              <div className="w-12 h-12 bg-gray-800 rounded-full mr-4"></div>
              <div>
                <p className="font-semibold">Sarah Johnson</p>
                <p className="text-sm text-gray-400">CEO, ComplianceFirst</p>
              </div>
            </div>
          </div>

          <div className="bg-secondary border border-gray-700 rounded-lg p-6">
            <p className="text-gray-300 mb-4 italic">
              "The Partner Empowerment approach is refreshing. NovaFuse truly builds with us rather than
              just selling through us. This has led to deeper integration and better customer outcomes."
            </p>
            <div className="flex items-center">
              <div className="w-12 h-12 bg-gray-800 rounded-full mr-4"></div>
              <div>
                <p className="font-semibold">Michael Chen</p>
                <p className="text-sm text-gray-400">CTO, SecureStack</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Partner Application */}
      <div className="bg-secondary border border-gray-700 rounded-lg p-6">
        <h2 className="text-2xl font-bold mb-4">Ready to Join?</h2>
        <p className="text-gray-300 mb-6">
          Apply to become a NovaFuse partner today and start growing your business with our
          Partner Empowerment approach.
        </p>
        <Link href="/become-partner" className="accent-bg text-white px-6 py-3 rounded-lg font-bold hover:bg-blue-700 inline-block">
          Apply Now
        </Link>
      </div>
    </PageWithSidebar>
  );
}
