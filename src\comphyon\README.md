# Comphyon Measurement System

## Overview

The Comphyon Measurement System is a module for measuring emergent intelligence in the NovaFuse platform. It quantifies the platform's predictive capabilities using the Comphyon (Cph) unit, where 1 Cph = 3,142 predictions/sec under a unified compliance-driven structure.

The Comphyon value is calculated using the formula:

```
Cph = ((csdeRate * csfeRate) × log(csmeScore)) / 166000
```

Where:
- **csdeRate**: Predictions per second from the CSDE engine
- **csfeRate**: Predictions per second from the CSFE engine
- **csmeScore**: Ethical score from the CSME engine (0-1)
- **3142**: Normalization factor derived from π10³

## Components

### Core Components

- **ComphyonMeter**: The core class that calculates Comphyon values based on data from CSDE, CSFE, and CSME engines.

### React Components

- **ComphyonGauge**: A circular gauge component that displays the current Comphyon value.
- **ComphyonDashboard**: A dashboard component that displays Comphyon metrics, including a gauge and historical charts.

## Usage

### Basic Usage

```javascript
const { ComphyonMeter } = require('./comphyon/exports');

// Create ComphyonMeter with CSDE, CSFE, and CSME engines
const comphyonMeter = new ComphyonMeter({
  csdeEngine,
  csfeEngine,
  csmeEngine,
  enableLogging: true,
  updateInterval: 1000
});

// Listen for updates
comphyonMeter.on('update', (data) => {
  console.log(`Comphyon: ${data.cph.toFixed(4)} Cph`);
  console.log(`CSDE Rate: ${data.csdeRate.toFixed(2)} predictions/sec`);
  console.log(`CSFE Rate: ${data.csfeRate.toFixed(2)} predictions/sec`);
  console.log(`CSME Score: ${data.csmeScore.toFixed(2)}`);
});

// Start the Comphyon meter
comphyonMeter.start();

// Later, stop the Comphyon meter
comphyonMeter.stop();
```

### React Integration

```jsx
import React, { useState, useEffect } from 'react';
import ComphyonGauge from './comphyon/components/ComphyonGauge';
import ComphyonDashboard from './comphyon/components/ComphyonDashboard';
const { ComphyonMeter } = require('./comphyon/exports');

const ComphyonDemo = () => {
  const [comphyonMeter, setComphyonMeter] = useState(null);

  useEffect(() => {
    // Initialize ComphyonMeter
    const meter = new ComphyonMeter({
      csdeEngine,
      csfeEngine,
      csmeEngine
    });

    setComphyonMeter(meter);
    meter.start();

    // Clean up on unmount
    return () => {
      meter.stop();
    };
  }, []);

  return (
    <div>
      {comphyonMeter && (
        <ComphyonDashboard
          comphyonMeter={comphyonMeter}
          autoRefresh={true}
        />
      )}
    </div>
  );
};
```

## API Reference

### ComphyonMeter

#### Constructor

```javascript
const comphyonMeter = new ComphyonMeter({
  csdeEngine,       // CSDE engine instance (required)
  csfeEngine,       // CSFE engine instance (optional)
  csmeEngine,       // CSME engine instance (optional)
  enableLogging,    // Enable logging (default: false)
  enableMetrics,    // Enable performance metrics (default: true)
  updateInterval,   // Update interval in milliseconds (default: 1000)
  historySize       // Size of history buffer (default: 60)
});
```

#### Methods

- **start()**: Start the Comphyon meter
- **stop()**: Stop the Comphyon meter
- **getCurrentCph()**: Get the current Comphyon value
- **getHistory()**: Get the Comphyon history
- **getMetrics()**: Get performance metrics

#### Events

- **update**: Emitted when the Comphyon value is updated
- **start**: Emitted when the Comphyon meter is started
- **stop**: Emitted when the Comphyon meter is stopped

### ComphyonGauge

```jsx
<ComphyonGauge
  value={1.5}                                // Comphyon value
  title="Comphyon (Cph)"                     // Title for the gauge
  subtitle="Emergent intelligence metric"    // Subtitle for the gauge
  thresholds={{ low: 0.5, medium: 1.0, high: 2.0 }}  // Value thresholds for color coding
  size={200}                                 // Size of the gauge in pixels
  metrics={{                                 // Additional metrics to display
    'CSDE Rate': '314.20/s',
    'CSFE Rate': '251.36/s',
    'CSME Score': '0.82'
  }}
/>
```

### ComphyonDashboard

```jsx
<ComphyonDashboard
  comphyonMeter={comphyonMeter}    // ComphyonMeter instance
  autoRefresh={true}               // Whether to auto-refresh the dashboard
  refreshInterval={1000}           // Refresh interval in milliseconds
/>
```

## Examples

See the `examples` directory for complete examples:

- **demo.js**: A simple command-line demo of the Comphyon meter
- **integration_example.js**: An example of integrating the Comphyon meter with NovaFuse components
- **react_example.jsx**: A React example showing how to use the Comphyon components

## Testing

Run the tests with:

```
node test/comphyon/test_comphyon.js
```
