"""
<PERSON><PERSON><PERSON> to run the UCECS API server.

This script initializes the necessary managers and starts the API server.
"""

import os
import sys
import logging
import argparse

# Add the parent directory to the path so we can import the UCECS modules
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
sys.path.append(parent_dir)

# Import the UCECS modules
from src.ucecs.core.evidence_manager import EvidenceManager
from src.ucecs.api.app import initialize_api, run_api

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """Run the API server."""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Run the UCECS API server")
    parser.add_argument("--host", default="0.0.0.0", help="Host to bind to")
    parser.add_argument("--port", type=int, default=5000, help="Port to bind to")
    parser.add_argument("--debug", action="store_true", help="Run in debug mode")
    parser.add_argument("--evidence-dir", default=None, help="Path to the evidence directory")
    args = parser.parse_args()

    # Create the evidence directory if it doesn't exist
    evidence_dir = args.evidence_dir or os.path.join(os.getcwd(), 'evidence_data')
    os.makedirs(evidence_dir, exist_ok=True)

    # Initialize the Evidence Manager
    logger.info("Initializing Evidence Manager...")
    evidence_manager = EvidenceManager(
        evidence_dir=evidence_dir,
        current_user_id="api_user"
    )

    # Initialize the API
    logger.info("Initializing API...")
    initialize_api(evidence_manager)

    # Run the API server
    logger.info(f"Starting API server on {args.host}:{args.port}...")
    run_api(host=args.host, port=args.port, debug=args.debug)

if __name__ == "__main__":
    main()
