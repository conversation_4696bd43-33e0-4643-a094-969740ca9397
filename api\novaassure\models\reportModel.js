/**
 * Report Model
 * 
 * This model defines the schema for reports.
 */

const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');
const logger = require('../utils/logger');

const reportSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  type: {
    type: String,
    enum: ['compliance', 'test-results', 'evidence', 'control-effectiveness'],
    required: true,
    index: true
  },
  framework: {
    type: String,
    trim: true,
    index: true
  },
  testPlan: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'TestPlan'
  },
  testExecution: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'TestExecution'
  },
  control: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Control'
  },
  startDate: {
    type: Date
  },
  endDate: {
    type: Date
  },
  format: {
    type: String,
    enum: ['pdf', 'html', 'json', 'csv'],
    default: 'pdf'
  },
  filePath: {
    type: String
  },
  fileSize: {
    type: Number
  },
  status: {
    type: String,
    enum: ['generating', 'completed', 'failed'],
    default: 'generating',
    index: true
  },
  error: {
    type: String
  },
  schedule: {
    frequency: {
      type: String,
      enum: ['daily', 'weekly', 'monthly', 'quarterly', 'annually', 'custom']
    },
    startDate: {
      type: Date
    },
    endDate: {
      type: Date
    },
    customSchedule: {
      type: String
    },
    recipients: [{
      type: String
    }]
  },
  metadata: {
    type: mongoose.Schema.Types.Mixed
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Create indexes
reportSchema.index({ name: 'text', description: 'text' });
reportSchema.index({ type: 1, framework: 1 });
reportSchema.index({ type: 1, testPlan: 1 });
reportSchema.index({ type: 1, testExecution: 1 });
reportSchema.index({ type: 1, control: 1 });
reportSchema.index({ 'schedule.frequency': 1, 'schedule.startDate': 1, 'schedule.endDate': 1 });

/**
 * Get report by ID
 * @param {string} id - Report ID
 * @returns {Promise<Object>} - Report object
 */
reportSchema.statics.getById = async function(id) {
  return this.findById(id)
    .populate('testPlan')
    .populate('testExecution')
    .populate('control')
    .populate('createdBy');
};

/**
 * Get reports by type
 * @param {string} type - Report type
 * @returns {Promise<Array>} - Array of report objects
 */
reportSchema.statics.getByType = async function(type) {
  return this.find({ type })
    .populate('testPlan')
    .populate('testExecution')
    .populate('control')
    .populate('createdBy')
    .sort({ createdAt: -1 });
};

/**
 * Get reports by framework
 * @param {string} framework - Framework name
 * @returns {Promise<Array>} - Array of report objects
 */
reportSchema.statics.getByFramework = async function(framework) {
  return this.find({ framework })
    .populate('testPlan')
    .populate('testExecution')
    .populate('control')
    .populate('createdBy')
    .sort({ createdAt: -1 });
};

/**
 * Get reports by test plan
 * @param {string} testPlanId - Test plan ID
 * @returns {Promise<Array>} - Array of report objects
 */
reportSchema.statics.getByTestPlan = async function(testPlanId) {
  return this.find({ testPlan: testPlanId })
    .populate('testPlan')
    .populate('testExecution')
    .populate('control')
    .populate('createdBy')
    .sort({ createdAt: -1 });
};

/**
 * Get reports by test execution
 * @param {string} testExecutionId - Test execution ID
 * @returns {Promise<Array>} - Array of report objects
 */
reportSchema.statics.getByTestExecution = async function(testExecutionId) {
  return this.find({ testExecution: testExecutionId })
    .populate('testPlan')
    .populate('testExecution')
    .populate('control')
    .populate('createdBy')
    .sort({ createdAt: -1 });
};

/**
 * Get reports by control
 * @param {string} controlId - Control ID
 * @returns {Promise<Array>} - Array of report objects
 */
reportSchema.statics.getByControl = async function(controlId) {
  return this.find({ control: controlId })
    .populate('testPlan')
    .populate('testExecution')
    .populate('control')
    .populate('createdBy')
    .sort({ createdAt: -1 });
};

/**
 * Get reports due for generation
 * @returns {Promise<Array>} - Array of report objects
 */
reportSchema.statics.getDueForGeneration = async function() {
  const now = new Date();
  
  // Find reports with schedules
  const reports = await this.find({
    'schedule.frequency': { $exists: true },
    'schedule.startDate': { $lte: now },
    $or: [
      { 'schedule.endDate': { $gte: now } },
      { 'schedule.endDate': { $exists: false } }
    ]
  }).populate('testPlan').populate('testExecution').populate('control');
  
  // Filter reports due for generation based on frequency
  const dueReports = [];
  
  for (const report of reports) {
    // Get last generation
    const lastGeneration = await this.findOne({
      name: report.name,
      type: report.type,
      framework: report.framework,
      testPlan: report.testPlan,
      testExecution: report.testExecution,
      control: report.control,
      _id: { $ne: report._id }
    }).sort({ createdAt: -1 });
    
    if (!lastGeneration) {
      // No previous generation, so it's due
      dueReports.push(report);
      continue;
    }
    
    const lastGenerationDate = lastGeneration.createdAt;
    let isDue = false;
    
    switch (report.schedule.frequency) {
      case 'daily':
        // Due if last generation was more than 1 day ago
        isDue = now - lastGenerationDate > 24 * 60 * 60 * 1000;
        break;
      case 'weekly':
        // Due if last generation was more than 7 days ago
        isDue = now - lastGenerationDate > 7 * 24 * 60 * 60 * 1000;
        break;
      case 'monthly':
        // Due if last generation was more than 30 days ago
        isDue = now - lastGenerationDate > 30 * 24 * 60 * 60 * 1000;
        break;
      case 'quarterly':
        // Due if last generation was more than 90 days ago
        isDue = now - lastGenerationDate > 90 * 24 * 60 * 60 * 1000;
        break;
      case 'annually':
        // Due if last generation was more than 365 days ago
        isDue = now - lastGenerationDate > 365 * 24 * 60 * 60 * 1000;
        break;
      case 'custom':
        // Custom schedule not implemented yet
        isDue = false;
        break;
    }
    
    if (isDue) {
      dueReports.push(report);
    }
  }
  
  return dueReports;
};

/**
 * Complete report generation
 * @param {string} id - Report ID
 * @param {string} filePath - Report file path
 * @param {number} fileSize - Report file size
 * @returns {Promise<Object>} - Report object
 */
reportSchema.statics.completeGeneration = async function(id, filePath, fileSize) {
  // Get report
  const report = await this.findById(id);
  
  if (!report) {
    throw new Error('Report not found');
  }
  
  if (report.status !== 'generating') {
    throw new Error('Report is not in generating status');
  }
  
  // Update report
  report.status = 'completed';
  report.filePath = filePath;
  report.fileSize = fileSize;
  
  // Save report
  await report.save();
  
  return report;
};

/**
 * Fail report generation
 * @param {string} id - Report ID
 * @param {string} error - Error message
 * @returns {Promise<Object>} - Report object
 */
reportSchema.statics.failGeneration = async function(id, error) {
  // Get report
  const report = await this.findById(id);
  
  if (!report) {
    throw new Error('Report not found');
  }
  
  if (report.status !== 'generating') {
    throw new Error('Report is not in generating status');
  }
  
  // Update report
  report.status = 'failed';
  report.error = error;
  
  // Save report
  await report.save();
  
  return report;
};

/**
 * Share report
 * @param {string} id - Report ID
 * @param {Array} recipients - Recipients
 * @param {string} [message] - Message
 * @param {Date} [expirationDate] - Expiration date
 * @returns {Promise<Object>} - Share result
 */
reportSchema.statics.share = async function(id, recipients, message = '', expirationDate = null) {
  // Get report
  const report = await this.findById(id);
  
  if (!report) {
    throw new Error('Report not found');
  }
  
  if (report.status !== 'completed') {
    throw new Error('Report is not completed');
  }
  
  // In a real implementation, this would send emails or create share links
  // For this placeholder, we'll just return a success message
  
  return {
    success: true,
    report,
    recipients,
    message,
    expirationDate,
    shareUrl: `https://novafuse.com/reports/share/${id}`
  };
};

const Report = mongoose.model('Report', reportSchema);

module.exports = Report;
