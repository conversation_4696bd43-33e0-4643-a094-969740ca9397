local typedefs = require "kong.db.schema.typedefs"

return {
  name = "novafuse-usage-tracking",
  fields = {
    { consumer = typedefs.no_consumer },
    { protocols = typedefs.protocols_http },
    { config = {
        type = "record",
        fields = {
          { subscription_tier = { type = "string", default = "standard", one_of = { "free", "basic", "professional", "enterprise" } } },
          { track_request_body = { type = "boolean", default = false } },
          { track_response_body = { type = "boolean", default = false } },
        },
      },
    },
  },
}
