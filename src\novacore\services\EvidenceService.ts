/**
 * EvidenceService.ts
 * 
 * Service for managing evidence in the NovaCore system.
 * This service handles the creation, retrieval, and management of compliance evidence.
 */

import {
  Evidence,
  EvidenceType,
  EvidenceStatus,
  EvidenceVersion,
  EvidenceMetadata,
  createEvidence,
  createEvidenceVersion,
  validateEvidence,
} from '../models/Evidence';

import { BlockchainService } from './BlockchainService';

/**
 * Evidence service interface
 */
export interface IEvidenceService {
  // CRUD operations
  createEvidence(
    name: string,
    type: EvidenceType,
    category: string,
    content: any,
    createdBy: string,
    organization: string,
    metadata?: EvidenceMetadata,
    description?: string,
    framework?: string,
    control?: string,
  ): Promise<Evidence>;
  
  getEvidence(id: string): Promise<Evidence>;
  updateEvidence(id: string, updates: Partial<Evidence>, updatedBy: string): Promise<Evidence>;
  deleteEvidence(id: string): Promise<boolean>;
  
  // Version management
  createVersion(
    evidenceId: string,
    content: any,
    updatedBy: string,
    metadata?: EvidenceMetadata,
    comments?: string,
  ): Promise<Evidence>;
  
  getVersion(evidenceId: string, versionId: string): Promise<EvidenceVersion>;
  listVersions(evidenceId: string): Promise<EvidenceVersion[]>;
  
  // Status management
  updateStatus(evidenceId: string, status: EvidenceStatus, updatedBy: string): Promise<Evidence>;
  
  // Verification
  verifyEvidence(evidenceId: string): Promise<boolean>;
  verifyVersion(evidenceId: string, versionId: string): Promise<boolean>;
  
  // Search and filtering
  searchEvidence(query: any, page?: number, pageSize?: number): Promise<{ items: Evidence[], total: number }>;
  
  // Relationships
  linkToRequirement(evidenceId: string, requirementId: string): Promise<boolean>;
  unlinkFromRequirement(evidenceId: string, requirementId: string): Promise<boolean>;
  getLinkedRequirements(evidenceId: string): Promise<string[]>;
}

/**
 * Evidence service implementation
 */
export class EvidenceService implements IEvidenceService {
  private evidenceStore: Map<string, Evidence> = new Map();
  private blockchainService: BlockchainService;
  
  constructor(blockchainService?: BlockchainService) {
    this.blockchainService = blockchainService || new BlockchainService();
  }
  
  /**
   * Create new evidence
   */
  public async createEvidence(
    name: string,
    type: EvidenceType,
    category: string,
    content: any,
    createdBy: string,
    organization: string,
    metadata: EvidenceMetadata = {},
    description?: string,
    framework?: string,
    control?: string,
  ): Promise<Evidence> {
    // Create evidence
    const evidence = createEvidence(
      name,
      type,
      category,
      content,
      createdBy,
      organization,
      metadata,
      description,
      framework,
      control,
    );
    
    // Validate evidence
    if (!validateEvidence(evidence)) {
      throw new Error('Invalid evidence');
    }
    
    // Store evidence
    this.evidenceStore.set(evidence.id, evidence);
    
    // Anchor evidence on blockchain if available
    if (this.blockchainService) {
      try {
        const verification = await this.blockchainService.anchorEvidence(evidence);
        
        // Update evidence with blockchain verification
        const updatedEvidence = {
          ...evidence,
          currentVersion: {
            ...evidence.currentVersion,
            blockchainVerification: verification,
          },
          versions: evidence.versions.map(v => 
            v.versionId === evidence.currentVersion.versionId
              ? { ...v, blockchainVerification: verification }
              : v
          ),
          verificationStatus: verification.status === 'verified' ? 'verified' : 'unverified',
        };
        
        // Update stored evidence
        this.evidenceStore.set(updatedEvidence.id, updatedEvidence);
        
        return updatedEvidence;
      } catch (error) {
        console.error('Error anchoring evidence on blockchain:', error);
        // Continue without blockchain verification
      }
    }
    
    return evidence;
  }
  
  /**
   * Get evidence by ID
   */
  public async getEvidence(id: string): Promise<Evidence> {
    const evidence = this.evidenceStore.get(id);
    if (!evidence) {
      throw new Error(`Evidence ${id} not found`);
    }
    return evidence;
  }
  
  /**
   * Update evidence
   */
  public async updateEvidence(id: string, updates: Partial<Evidence>, updatedBy: string): Promise<Evidence> {
    const evidence = await this.getEvidence(id);
    
    // Apply updates
    const updatedEvidence = {
      ...evidence,
      ...updates,
      updatedAt: new Date(),
      updatedBy,
    };
    
    // Validate updated evidence
    if (!validateEvidence(updatedEvidence)) {
      throw new Error('Invalid evidence updates');
    }
    
    // Store updated evidence
    this.evidenceStore.set(id, updatedEvidence);
    
    return updatedEvidence;
  }
  
  /**
   * Delete evidence
   */
  public async deleteEvidence(id: string): Promise<boolean> {
    // Check if evidence exists
    if (!this.evidenceStore.has(id)) {
      throw new Error(`Evidence ${id} not found`);
    }
    
    // Delete evidence
    return this.evidenceStore.delete(id);
  }
  
  /**
   * Create new version of evidence
   */
  public async createVersion(
    evidenceId: string,
    content: any,
    updatedBy: string,
    metadata?: EvidenceMetadata,
    comments?: string,
  ): Promise<Evidence> {
    const evidence = await this.getEvidence(evidenceId);
    
    // Create new version
    const updatedEvidence = createEvidenceVersion(
      evidence,
      content,
      updatedBy,
      metadata,
      comments,
    );
    
    // Store updated evidence
    this.evidenceStore.set(evidenceId, updatedEvidence);
    
    // Anchor new version on blockchain if available
    if (this.blockchainService) {
      try {
        const verification = await this.blockchainService.anchorEvidenceVersion(
          updatedEvidence,
          updatedEvidence.currentVersion,
        );
        
        // Update evidence with blockchain verification
        const verifiedEvidence = {
          ...updatedEvidence,
          currentVersion: {
            ...updatedEvidence.currentVersion,
            blockchainVerification: verification,
          },
          versions: updatedEvidence.versions.map(v => 
            v.versionId === updatedEvidence.currentVersion.versionId
              ? { ...v, blockchainVerification: verification }
              : v
          ),
          verificationStatus: verification.status === 'verified' ? 'verified' : 'unverified',
        };
        
        // Update stored evidence
        this.evidenceStore.set(evidenceId, verifiedEvidence);
        
        return verifiedEvidence;
      } catch (error) {
        console.error('Error anchoring evidence version on blockchain:', error);
        // Continue without blockchain verification
      }
    }
    
    return updatedEvidence;
  }
  
  /**
   * Get specific version of evidence
   */
  public async getVersion(evidenceId: string, versionId: string): Promise<EvidenceVersion> {
    const evidence = await this.getEvidence(evidenceId);
    
    // Find version
    const version = evidence.versions.find(v => v.versionId === versionId);
    if (!version) {
      throw new Error(`Version ${versionId} not found for evidence ${evidenceId}`);
    }
    
    return version;
  }
  
  /**
   * List all versions of evidence
   */
  public async listVersions(evidenceId: string): Promise<EvidenceVersion[]> {
    const evidence = await this.getEvidence(evidenceId);
    return evidence.versions;
  }
  
  /**
   * Update evidence status
   */
  public async updateStatus(evidenceId: string, status: EvidenceStatus, updatedBy: string): Promise<Evidence> {
    return this.updateEvidence(
      evidenceId,
      { status, updatedBy, updatedAt: new Date() },
      updatedBy,
    );
  }
  
  /**
   * Verify evidence on blockchain
   */
  public async verifyEvidence(evidenceId: string): Promise<boolean> {
    const evidence = await this.getEvidence(evidenceId);
    
    // Check if blockchain service is available
    if (!this.blockchainService) {
      throw new Error('Blockchain service not available');
    }
    
    // Verify evidence
    const isVerified = await this.blockchainService.verifyEvidence(evidence);
    
    // Update evidence verification status
    await this.updateEvidence(
      evidenceId,
      { verificationStatus: isVerified ? 'verified' : 'failed' },
      'system',
    );
    
    return isVerified;
  }
  
  /**
   * Verify specific version of evidence on blockchain
   */
  public async verifyVersion(evidenceId: string, versionId: string): Promise<boolean> {
    const evidence = await this.getEvidence(evidenceId);
    
    // Check if blockchain service is available
    if (!this.blockchainService) {
      throw new Error('Blockchain service not available');
    }
    
    // Verify version
    return this.blockchainService.verifyEvidenceVersion(evidence, versionId);
  }
  
  /**
   * Search evidence
   */
  public async searchEvidence(
    query: any,
    page: number = 1,
    pageSize: number = 10,
  ): Promise<{ items: Evidence[], total: number }> {
    // Convert Map to array
    const allEvidence = Array.from(this.evidenceStore.values());
    
    // Filter evidence based on query
    const filteredEvidence = this.filterEvidence(allEvidence, query);
    
    // Paginate results
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedEvidence = filteredEvidence.slice(startIndex, endIndex);
    
    return {
      items: paginatedEvidence,
      total: filteredEvidence.length,
    };
  }
  
  /**
   * Filter evidence based on query
   */
  private filterEvidence(evidence: Evidence[], query: any): Evidence[] {
    if (!query || Object.keys(query).length === 0) {
      return evidence;
    }
    
    return evidence.filter(e => {
      // Check each query parameter
      for (const [key, value] of Object.entries(query)) {
        // Handle special case for metadata
        if (key === 'metadata' && typeof value === 'object') {
          for (const [metaKey, metaValue] of Object.entries(value)) {
            if (e.metadata[metaKey] !== metaValue) {
              return false;
            }
          }
          continue;
        }
        
        // Handle array values (OR condition)
        if (Array.isArray(value)) {
          if (!value.includes(e[key])) {
            return false;
          }
          continue;
        }
        
        // Handle regular values
        if (e[key] !== value) {
          return false;
        }
      }
      
      return true;
    });
  }
  
  /**
   * Link evidence to requirement
   */
  public async linkToRequirement(evidenceId: string, requirementId: string): Promise<boolean> {
    const evidence = await this.getEvidence(evidenceId);
    
    // Check if already linked
    if (evidence.relatedRequirements && evidence.relatedRequirements.includes(requirementId)) {
      return true;
    }
    
    // Add requirement to related requirements
    const relatedRequirements = evidence.relatedRequirements || [];
    relatedRequirements.push(requirementId);
    
    // Update evidence
    await this.updateEvidence(
      evidenceId,
      { relatedRequirements },
      'system',
    );
    
    return true;
  }
  
  /**
   * Unlink evidence from requirement
   */
  public async unlinkFromRequirement(evidenceId: string, requirementId: string): Promise<boolean> {
    const evidence = await this.getEvidence(evidenceId);
    
    // Check if linked
    if (!evidence.relatedRequirements || !evidence.relatedRequirements.includes(requirementId)) {
      return true;
    }
    
    // Remove requirement from related requirements
    const relatedRequirements = evidence.relatedRequirements.filter(id => id !== requirementId);
    
    // Update evidence
    await this.updateEvidence(
      evidenceId,
      { relatedRequirements },
      'system',
    );
    
    return true;
  }
  
  /**
   * Get requirements linked to evidence
   */
  public async getLinkedRequirements(evidenceId: string): Promise<string[]> {
    const evidence = await this.getEvidence(evidenceId);
    return evidence.relatedRequirements || [];
  }
}
