/**
 * Error Handling Tests for NovaConnect Universal API Connector
 * 
 * These tests verify that the connector can handle various error scenarios.
 */

const path = require('path');
const fs = require('fs');
const axios = require('axios');
const { 
  startAllServices, 
  stopAllServices,
  registryUrl,
  authUrl,
  executorUrl,
  mockApiUrl
} = require('../setup');

// Test data
const connector = require('../connectors/error-handling-connector.json');

// Test credential
const credential = {
  name: 'Error Handling Test Credential',
  authType: 'API_KEY',
  credentials: {
    apiKey: 'valid-api-key',
    headerName: 'X-API-Key'
  }
};

// Store connector and credential IDs
let connectorId;
let credentialId;

describe('Error Handling Tests', () => {
  // Start services before all tests
  beforeAll(async () => {
    await startAllServices();
    
    // Clear request history
    await axios.post(`${mockApiUrl}/clear-history`);
    
    // Register connector
    const connResponse = await axios.post(`${registryUrl}/connectors`, connector);
    connectorId = connResponse.data.id;
    
    // Create credential
    credential.connectorId = connectorId;
    const credResponse = await axios.post(`${authUrl}/credentials`, credential);
    credentialId = credResponse.data.id;
  }, 60000);
  
  // Stop services after all tests
  afterAll(async () => {
    // Clean up test data
    try {
      await axios.delete(`${registryUrl}/connectors/${connectorId}`);
    } catch (error) {
      console.error(`Error deleting connector ${connectorId}:`, error.message);
    }
    
    try {
      await axios.delete(`${authUrl}/credentials/${credentialId}`);
    } catch (error) {
      console.error(`Error deleting credential ${credentialId}:`, error.message);
    }
    
    stopAllServices();
  });
  
  // Test HTTP error handling
  describe('HTTP Error Handling', () => {
    it('should handle 400 Bad Request errors', async () => {
      try {
        await axios.post(`${executorUrl}/execute/${connectorId}/badRequest`, {
          credentialId,
          parameters: {},
          userId: 'test-user'
        });
        
        fail('Expected request to fail');
      } catch (error) {
        expect(error.response.status).toBe(400);
        expect(error.response.data).toHaveProperty('error');
        expect(error.response.data.error).toContain('Bad Request');
      }
    });
    
    it('should handle 401 Unauthorized errors', async () => {
      try {
        await axios.post(`${executorUrl}/execute/${connectorId}/unauthorized`, {
          credentialId,
          parameters: {},
          userId: 'test-user'
        });
        
        fail('Expected request to fail');
      } catch (error) {
        expect(error.response.status).toBe(401);
        expect(error.response.data).toHaveProperty('error');
        expect(error.response.data.error).toContain('Unauthorized');
      }
    });
    
    it('should handle 403 Forbidden errors', async () => {
      try {
        await axios.post(`${executorUrl}/execute/${connectorId}/forbidden`, {
          credentialId,
          parameters: {},
          userId: 'test-user'
        });
        
        fail('Expected request to fail');
      } catch (error) {
        expect(error.response.status).toBe(403);
        expect(error.response.data).toHaveProperty('error');
        expect(error.response.data.error).toContain('Forbidden');
      }
    });
    
    it('should handle 404 Not Found errors', async () => {
      try {
        await axios.post(`${executorUrl}/execute/${connectorId}/notFound`, {
          credentialId,
          parameters: {},
          userId: 'test-user'
        });
        
        fail('Expected request to fail');
      } catch (error) {
        expect(error.response.status).toBe(404);
        expect(error.response.data).toHaveProperty('error');
        expect(error.response.data.error).toContain('Not Found');
      }
    });
    
    it('should handle 429 Rate Limit errors', async () => {
      try {
        await axios.post(`${executorUrl}/execute/${connectorId}/rateLimited`, {
          credentialId,
          parameters: {},
          userId: 'test-user'
        });
        
        fail('Expected request to fail');
      } catch (error) {
        expect(error.response.status).toBe(429);
        expect(error.response.data).toHaveProperty('error');
        expect(error.response.data.error).toContain('Rate limit');
      }
    });
    
    it('should handle 500 Server Error', async () => {
      try {
        await axios.post(`${executorUrl}/execute/${connectorId}/serverError`, {
          credentialId,
          parameters: {},
          userId: 'test-user'
        });
        
        fail('Expected request to fail');
      } catch (error) {
        expect(error.response.status).toBe(500);
        expect(error.response.data).toHaveProperty('error');
        expect(error.response.data.error).toContain('Server Error');
      }
    });
  });
  
  // Test timeout handling
  describe('Timeout Handling', () => {
    it('should handle request timeouts', async () => {
      try {
        await axios.post(`${executorUrl}/execute/${connectorId}/timeout`, {
          credentialId,
          parameters: {},
          userId: 'test-user'
        });
        
        fail('Expected request to fail');
      } catch (error) {
        expect(error.response.status).toBe(500);
        expect(error.response.data).toHaveProperty('error');
        expect(error.response.data.error).toContain('timeout');
      }
    }, 10000);
  });
  
  // Test malformed response handling
  describe('Malformed Response Handling', () => {
    it('should handle malformed JSON responses', async () => {
      try {
        await axios.post(`${executorUrl}/execute/${connectorId}/malformedJson`, {
          credentialId,
          parameters: {},
          userId: 'test-user'
        });
        
        fail('Expected request to fail');
      } catch (error) {
        expect(error.response.status).toBe(500);
        expect(error.response.data).toHaveProperty('error');
        expect(error.response.data.error).toContain('parse');
      }
    });
  });
  
  // Test retry policy
  describe('Retry Policy', () => {
    it('should retry failed requests according to the retry policy', async () => {
      // First, clear the request history
      await axios.post(`${mockApiUrl}/clear-history`);
      
      try {
        await axios.post(`${executorUrl}/execute/${connectorId}/serverError`, {
          credentialId,
          parameters: {},
          userId: 'test-user'
        });
        
        fail('Expected request to fail');
      } catch (error) {
        // The request should have failed after retries
        expect(error.response.status).toBe(500);
      }
      
      // Check the request history to verify retries
      const historyResponse = await axios.get(`${mockApiUrl}/history`);
      const serverErrorRequests = historyResponse.data.filter(req => 
        req.path === '/error/500'
      );
      
      // Should have made the original request plus retries (total: 4)
      expect(serverErrorRequests.length).toBeGreaterThan(1);
      expect(serverErrorRequests.length).toBeLessThanOrEqual(4);
    });
  });
  
  // Test error reporting
  describe('Error Reporting', () => {
    it('should include detailed error information in the response', async () => {
      try {
        await axios.post(`${executorUrl}/execute/${connectorId}/badRequest`, {
          credentialId,
          parameters: {},
          userId: 'test-user'
        });
        
        fail('Expected request to fail');
      } catch (error) {
        expect(error.response.data).toHaveProperty('error');
        expect(error.response.data).toHaveProperty('statusCode', 400);
        expect(error.response.data).toHaveProperty('message');
        expect(error.response.data).toHaveProperty('timestamp');
        expect(error.response.data).toHaveProperty('path');
      }
    });
  });
  
  // Test invalid endpoint
  describe('Invalid Endpoint', () => {
    it('should handle requests to non-existent endpoints', async () => {
      try {
        await axios.post(`${executorUrl}/execute/${connectorId}/nonExistentEndpoint`, {
          credentialId,
          parameters: {},
          userId: 'test-user'
        });
        
        fail('Expected request to fail');
      } catch (error) {
        expect(error.response.status).toBe(404);
        expect(error.response.data).toHaveProperty('error');
        expect(error.response.data.error).toContain('Endpoint not found');
      }
    });
  });
  
  // Test invalid connector
  describe('Invalid Connector', () => {
    it('should handle requests to non-existent connectors', async () => {
      try {
        await axios.post(`${executorUrl}/execute/non-existent-connector/getResource`, {
          credentialId,
          parameters: {},
          userId: 'test-user'
        });
        
        fail('Expected request to fail');
      } catch (error) {
        expect(error.response.status).toBe(404);
        expect(error.response.data).toHaveProperty('error');
        expect(error.response.data.error).toContain('Connector not found');
      }
    });
  });
  
  // Test invalid credential
  describe('Invalid Credential', () => {
    it('should handle requests with non-existent credentials', async () => {
      try {
        await axios.post(`${executorUrl}/execute/${connectorId}/badRequest`, {
          credentialId: 'non-existent-credential',
          parameters: {},
          userId: 'test-user'
        });
        
        fail('Expected request to fail');
      } catch (error) {
        expect(error.response.status).toBe(404);
        expect(error.response.data).toHaveProperty('error');
        expect(error.response.data.error).toContain('Credential not found');
      }
    });
  });
});
